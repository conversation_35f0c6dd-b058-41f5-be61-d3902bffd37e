# Codeowners Simulator: https://mibexsoftware.bitbucket.io/codeowners-playground/#tabs-co-playground
# Syntax: https://docs.mibexsoftware.com/codeowners/cloud

# CodeOwners Group
@@@codeowners @mperep<PERSON>tsyn @hfujita @nravik<PERSON><PERSON>
@@@policy @nravikumar @xyu @vbhatt
@@@user_risk @aanjali @blewis @utripathy @nravikumar @xyu
@@@scim @blewis @nravikumar @xyu
@@@app_download @nravikumar @xyu @vbhatt @aanja<PERSON>
@@@dta @xyu @vbhatt @vramaka @nravikumar
@@@wally @jliu @hpai @madhusr @xyu @nravikumar
@@@svcp @hfujita @vramaka @utripathy
@@@posture_profile @vbhatt @xyu @nravikumar @blewis
@@@app_connector @sshi @ytang @tdixit @igupta @sphanisree @yitaohe @pankajkumar @g<PERSON><PERSON><PERSON> @spodapati @ashekar @ntriantafill<PERSON>
@@@sarge @sshi @ytang @randavar @sgurusamy @tdixit @igupta @yitaohe @pankajkumar @ashekar @ntriantafillis
@@@private_broker @amansoor @sgurusamy @utripathy @"shantanu.raj" @"sonam.khurana" @sshi @vbheemireddy @ytang @"rupesh.kumar" @ntriantafillis
@@@ipars @hfujita @tbhope @"shantanu.raj" @jchadha @sgurusamy @"rupesh.kumar" @ntriantafillis
@@@c2c @vpavlov @hfujita @tbhope
@@@argo @mchalla
@@@logging @mchalla @aramaratnam
@@@build @mperepelitsyn @hfujita @blewis @kkrishnachettyravi
@@@public_broker @randavar @hgurram @rsalim @kkrishnachettyravi @blewis @jnie @mchalla
@@@zpath_lib @kkrishnachettyravi @mchalla
@@@sitec @randavar @jnie
@@@network_presence @amansoor @hfujita @smohanty @sphanisree @"shantanu.raj" @sshi @sgurusamy @"sonam.khurana" @vpalaparty @vbheemireddy @ytang @"rupesh.kumar" @ntriantafillis
@@@transport @grybinski @smohanty @sshi @yitaohe @pankajkumar @ashekar @ntriantafillis
@@@zdx @igupta @grybinski @spodapati @ashekar @ntriantafillis
@@@c_dispatcher @spodapati @ashekar @ntriantafillis

# Public Broker
src/zpn/zbalance/ @@public_broker
src/zpn/zpn_version_control/ @@public_broker
src/zpn/{zpn_broker*,zpn_mconn*,zpn_mtunnel*}.{h,c} @@public_broker
src/zpn_brokerd/ @@public_broker

# Transport
src/fohh/* @@transport
src/zpn/*mconn*.{h,c} @@transport
src/zpn/zpn_mtunnel.{h,c} @@transport

# Policy
src/diamond/ @@policy
src/pattern_match/ @@policy
src/zpn/policy_engine_test/ @@policy
src/zpn/{zpn_broker_policy*,zpn_application*,zpn_policy*,zpn_rule*,zpn_customer*}.{h,c} @@policy

# DTA
src/zpn/zpn_scope*.{h,c} @@dta

# App Download
src/zpn/client_apps_test/ @@app_download
src/zpn/*client_apps*.{h,c} @@app_download

# SCIM
src/zpn/{*scim*,*userdb*}.{h,c} @@scim
src/zpath_lib/*userdb*.{h,c} @@scim

# User Risk
src/zpn/*user_risk*.{h,c} @@user_risk

# Posture Profile
src/zpn/zpn_posture*.{h,c} @@posture_profile

# SVCP
src/zpn/*svcp*.{h,c} @@svcp

# Wally
src/wally*/ @@wally
src/zpath_lib/*wally*.{h,c} @@wally

# Private Broker
src/zpn_private_brokerd/ @@private_broker
src/zpn/{*pbroker*,*private_broker*,zpn_pb_client}.{h,c} @@private_broker
src/zpn_assistantd/*pbroker*.{h,c} @@private_broker

#site controller
src/zpn/{*site*,*ddil*}.{h,c} @@sitec
src/zpn_sitecd/ @@sitec
src/zpn_assistantd/*site*.{h,c} @@sitec

# App Connector
src/zpn_assistantd/ @@app_connector
src/zpn/{*assistant*,*connector*}.{h,c} @@app_connector

# sarge
src/sarge/ @@sarge

# IPARS
src/zpn_iparsd/ @@ipars
src/zpn/*ipars*.{h,c} @@ipars
src/tools/ipars_client/ @@ipars

# C2C
src/zpn/zpn_c2c* @@c2c
src/tools/c2c_client/ @@c2c

# Argo
src/argo/ @@argo

# Itasca Logging (Natural & Slogger)
src/natural/ @@logging
src/slogger/ @@logging
src/zpn/*siem*.{h,c} @@logging
src/zpn/*slogger*.{h,c} @@logging
src/fohh/*log*.{h,c} @@logging

# ZPath Lib - For Cloud Config
src/zpath_lib/*cloud_config* @@zpath_lib
src/zpath_lib/test/* @@zpath_lib

# C Dispatcher
src/zpn_dispatcherd/* @@c_dispatcher
src/zpn/dsp_*.{h,c} @@c_dispatcher

# ZDX
src/zpn_zdx/* @@zdx
src/zpn/zpn_zdx* @@zdx

# Network Presence
src/np_lib/* @@network_presence
src/zpn_npgatewayd/* @@network_presence
src/np_connector/* @@network_presence
src/zpn/zpn_broker_np*.{h,c} @@network_presence @@public_broker
src/zpn_assistantd/assistant_np*.{h,c} @@network_presence @@app_connector
src/zpn_iparsd/zpn_np_ipars*.{h,c} @@network_presence @@ipars

# Codeowners
**/CODEOWNERS @@codeowners

# Build
**/{CMakeLists.txt,*.cmake,Makefile*,*.mk,rpm/**} @@build
{CMakePresets.json,vcpkg.json,vcpkg-configuration.json,sonar-project.properties,.gersemirc,.pre-commit-config.yaml,.clang-format,vcpkg/**,build/**,.devcontainer/**} @@build
