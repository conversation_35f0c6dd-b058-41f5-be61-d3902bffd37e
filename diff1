commit c863236b2c143a457edc689fac4f636b527ce8b3
Author: <PERSON><PERSON><PERSON> <<EMAIL>>
Date:   Sat Jun 14 11:37:39 2025 +0530

    ET-94242: lp_map and filter with coordinates and dc_name

diff --git a/src/zpath_lib/zpath_instance.c b/src/zpath_lib/zpath_instance.c
index 0b3310e0a3..36d3308a6b 100644
--- a/src/zpath_lib/zpath_instance.c
+++ b/src/zpath_lib/zpath_instance.c
@@ -788,6 +788,54 @@ int zpath_instance_get_instances_for_role(const char *role, struct zpath_instanc
     return res;
 }
 
+/* This function fetches dc_name present in instance_name
+ * Assuming the instance name is like -->  <instance_name>.<dc_name>.<cloud>
+ * Caller is responsible for freeing dc_name if not needed.
+*/
+char *zpath_instance_get_dc_name_from_instance_name(char *instance_name) {
+    char *dc_name = NULL;
+    char *instance_name_dup = ZLIB_STRDUP(instance_name, strlen(instance_name) + 1);
+    instance_name_dup[strlen(instance_name)] = '\0';
+
+    char *token;
+    char *saveptr;
+    int index = 0;
+    token = strtok_r(instance_name_dup, ".", &saveptr);
+
+    while (token != NULL) {
+        if(index == 1) {
+            dc_name = ZLIB_STRDUP(token, strlen(token));
+            break;
+        }
+        index++;
+        token = strtok_r(NULL, ".", &saveptr);
+    }
+    ZLIB_FREE(instance_name_dup);
+    return dc_name;
+}
+
+struct zpath_instance_coordinates* zpath_instance_get_instance_coordinates(int64_t instance_gid){
+    struct zpath_instance *instance = zpath_instance_get_no_locking(instance_gid);
+
+    if(!instance) {
+        ZPATH_LOG(AL_INFO, "Unable to find instance for the instance_gid %"PRId64, instance_gid);
+        return NULL;
+    }
+    if(!(strcmp(instance->role, "zpn_brokerd") == 0)){
+        return NULL;
+    }
+
+    struct zpath_instance_coordinates *instance_coordinate;
+    instance_coordinate = ZLIB_CALLOC(sizeof(struct zpath_instance_coordinates));
+
+    if(!instance_coordinate) return NULL;
+
+    instance_coordinate->latitude = instance->latitude;
+    instance_coordinate->longitude = instance->longitude;
+
+    return instance_coordinate;
+}
+
 
 void zpath_domain_name_to_instance_name(const char *domain_name, char *instance_name, size_t instance_name_length)
 {
diff --git a/src/zpath_lib/zpath_instance.h b/src/zpath_lib/zpath_instance.h
index 4534dcb459..6fa6b6e806 100644
--- a/src/zpath_lib/zpath_instance.h
+++ b/src/zpath_lib/zpath_instance.h
@@ -83,6 +83,13 @@ struct zpath_instance_global {
     char instance_full_name[256]; /* populate the instance name based on alt_cloud */
 };
 
+
+/* Object to hold the geographical coordinates for the instance */
+struct zpath_instance_coordinates {
+    double latitude;
+    double longitude;
+};
+
 #define ZPATH_INSTANCE_GID \
     (zpath_instance_global_state.gid)
 
@@ -157,4 +164,7 @@ int zpath_instance_is_alt_cloud_enabled_for_customer(int64_t customer_gid);
 int zpath_broker_is_lbb_enabled(void);
 
 int zpath_broker_is_lbb_enabled_for_customer(int64_t customer_gid);
+
+struct zpath_instance_coordinates* zpath_instance_get_instance_coordinates(int64_t instance_gid);
+char *zpath_instance_get_dc_name_from_instance_name(char *instance_name);
 #endif /* __ZPATH_INSTANCE__H */
diff --git a/src/zpath_lib/zpath_partition_common.c b/src/zpath_lib/zpath_partition_common.c
index 5004d595e5..12deed5d5d 100644
--- a/src/zpath_lib/zpath_partition_common.c
+++ b/src/zpath_lib/zpath_partition_common.c
@@ -29,6 +29,7 @@
 
 #define DEFAULT_LP_PROFILE_VERSION -1
 #define ZPATH_VIRTUAL_NODE_COUNT 1000
+#define MAX_INSTANCE_PER_DC_COUNT 200
 
 int64_t g_lp_init_status;
 
@@ -57,6 +58,17 @@ struct zpath_instance_partitions
     int64_t partition_gids[ZPATH_MAX_PARTITIONS];
 };
 
+/* Used to hold array of instances
+ * Can be used as an object to hold instances within partition, or instances within dc, or lat-lon
+ * MAX_INSTANCE_PER_DC_COUNT = 200;
+ */
+struct zpath_partition_instances_name
+{
+    size_t instances_count;
+    char *instances[MAX_INSTANCE_PER_DC_COUNT];
+};
+
+
 /*
  * active_partitions is accessed during l4proxy initialization
  * before registering for config override monitor
@@ -767,6 +779,412 @@ static int debug_partition_partition(struct zpath_debug_state *request_state,
     return ZPATH_RESULT_NO_ERROR;
 }
 
+static __inline__ void free_partition_mapping_inner_hash_table(void *element, void* cookie) {
+    if(!element){
+        return;
+    }
+
+    struct zpath_partition_instances_name *temp_value = element;
+
+    for(int i=0; i<temp_value->instances_count; i++) {
+        ZLIB_FREE(temp_value->instances[i]);
+    }
+    ZLIB_FREE(temp_value);
+}
+
+static __inline__ void free_partition_mapping_outer_hash_table(void *element, void* cookie) {
+    if (!element) {
+        return;
+    }
+    char *key_str = cookie;
+
+    struct zhash_table *inner_hash_table = element;
+    zhash_table_free_and_call(inner_hash_table, free_partition_mapping_inner_hash_table, key_str);
+}
+
+/* This function adds the (key,value) pair to the provided hash-table
+ * Key --> partition gid/name
+ * value --> Array of instance gids/name in that partition
+*/
+static int zpath_partition_map_instance_to_partition(struct zhash_table *hash_table,int64_t *instance_gid, int64_t *partition_gid) {
+    if(!hash_table) return ZPATH_RESULT_NO_ERROR;
+
+    if(!instance_gid || !partition_gid) {
+        return ZPATH_RESULT_ERR;
+    }
+    int res = ZPATH_RESULT_NO_ERROR;
+    char *instance_name = NULL;
+
+     /* This fetches instance_name (instance_display_name) from zpath_partition_profile_instance table. */
+    {
+        struct zpath_partition_profile_instance **instances = NULL;
+        size_t instance_count = ZPATH_PARTITION_PROFILE_MAX_INSTANCES;
+
+        instances = ZLIB_CALLOC(ZPATH_PARTITION_PROFILE_MAX_INSTANCES *
+                                sizeof(struct zpath_partition_profile_instance*));
+
+        if (!instances) {
+            ZPATH_LOG(AL_ERROR, "Unable to allocate memory for instances");
+            return ZPATH_RESULT_ERR;
+        }
+        res = zpath_partition_profile_get_instances_by_instance_gid(*instance_gid, instances, &instance_count);
+        if(instance_count) {
+            instance_name = ZLIB_STRDUP(instances[0]->instance_display_name, strlen(instances[0]->instance_display_name));
+        }
+        ZLIB_FREE(instances);
+    }
+    struct zpath_partition_instances_name *temp_value=zhash_table_lookup(hash_table, partition_gid, sizeof(int64_t), NULL);
+
+    if(temp_value){
+        temp_value->instances[temp_value->instances_count++] = instance_name;
+    } else {
+        temp_value = ZLIB_CALLOC(sizeof(struct zpath_partition_instances_name));
+        if(!temp_value) {
+            ZPATH_LOG(AL_ERROR, "Unable to allocate memory for zpath_partition_instances_name structure.");
+            ZLIB_FREE(instance_name);
+            return ZPATH_RESULT_ERR;
+        }
+
+        temp_value->instances[temp_value->instances_count++] = instance_name;
+        res = zhash_table_store(hash_table, partition_gid, sizeof(int64_t), 0, temp_value);
+        if(res) {
+            ZPATH_LOG(AL_ERROR, "Failed to store instance list!");
+            ZLIB_FREE(instance_name);
+            ZLIB_FREE(temp_value);
+            return ZPATH_RESULT_ERR;
+        }
+    }
+    return ZPATH_RESULT_NO_ERROR;
+}
+
+/* Function to provide key based on filter to the caller function
+ * Currently only [coordinates and dc] supported
+ */
+static void* zpath_partition_get_lp_map_table_key(char *key_str, size_t *key_len, void* cookie1, void* cookie2){
+    int64_t *inst_gid = cookie1;
+
+    if(!key_str){
+        ZPATH_LOG(AL_ERROR, "No filter provided!");
+        return NULL;
+    }
+
+    if (strcmp(key_str, "coordinates") == 0) { /* Key will be coordinates */
+        struct zpath_instance_coordinates *coordinates = zpath_instance_get_instance_coordinates(*inst_gid);
+        struct zpath_instance_coordinates *filter_coordinates = cookie2;
+        *key_len = sizeof(struct zpath_instance_coordinates);
+
+        if(!coordinates) {
+            ZPATH_LOG(AL_ERROR, "Unable to create coordinate key!");
+            return NULL;
+        }
+
+        if (!filter_coordinates) return coordinates;
+
+        if(filter_coordinates->latitude == coordinates->latitude && filter_coordinates->longitude == coordinates->longitude) {
+            return coordinates;
+        }
+        ZLIB_FREE(coordinates);
+        return NULL;
+
+    } else if (strcmp(key_str, "dc") == 0) {
+        /* Check if instance present or not, Need to re-confirm if we need to use zpath_partition_profile_instance table/ */
+        struct zpath_instance *instance = zpath_instance_get_no_locking(*inst_gid);
+
+        if(!instance) return NULL;
+
+        char *dc_name = NULL;
+        struct zpath_partition_profile_instance **instances = NULL;
+
+        char *filter_dc_name = cookie2;
+        size_t instance_count = ZPATH_PARTITION_PROFILE_MAX_INSTANCES;
+
+        instances = ZLIB_CALLOC(ZPATH_PARTITION_PROFILE_MAX_INSTANCES *
+                                sizeof(struct zpath_partition_profile_instance*));
+
+        if (!instances) {
+            ZPATH_LOG(AL_ERROR, "Unable to allocate memory for instances");
+            return NULL;
+        }
+
+        zpath_partition_profile_get_instances_by_instance_gid(*inst_gid, instances, &instance_count);
+        if(!instance_count) {
+            ZLIB_FREE(instances);
+            return NULL;
+        }
+
+        dc_name = zpath_instance_get_dc_name_from_instance_name(instances[0]->instance_display_name); /* Make sure to free dc_name to avoid memory leak */
+        ZLIB_FREE(instances);
+        if(!dc_name) return NULL;
+
+        if(dc_name){
+            *key_len = strlen(dc_name) + 1;
+        }
+
+        if(!filter_dc_name || (strcmp(filter_dc_name, dc_name) == 0)) {
+            return dc_name;
+        }
+
+        ZLIB_FREE(dc_name);
+        return NULL;
+    }
+    return NULL;
+}
+
+/* This function will create LP Map from the active profile
+ * Creates hash_table in the form of
+ *   { "dc_name" : {
+ *                    "partition_name1" : [inst1, inst2, inst3 ...],
+ *                    "partition_name2" : [inst1, inst2, inst3 ...],
+ *                 },
+ *   }
+ */
+static __inline__ int partition_to_instance_mapping_cb(void *cookie1, void *cookie2,
+                                                        void *cookie3, void *cookie4, void *cookie5,
+                                                        void *object,
+                                                        void *key, size_t key_len)
+{
+    assert(key_len == sizeof(int64_t));
+    int res = ZPATH_RESULT_NO_ERROR;
+
+    struct zhash_table *hash_table = cookie1;
+    char *key_str = cookie2;
+    void *filter_cookie = cookie3; /* Filter based on dc_name or coordinates */
+
+    struct zpath_instance_partitions *obj = object;  /* Value coming from instance_to_partition hash_table*/
+    int64_t *inst_gid = key;
+
+    size_t mapping_key_len = 0;
+    void *mapping_key = zpath_partition_get_lp_map_table_key(key_str, &mapping_key_len, inst_gid, filter_cookie);
+
+    if(!mapping_key) return ZPATH_RESULT_NO_ERROR;
+
+    struct zhash_table *temp_internal_table = zhash_table_lookup(hash_table, mapping_key, mapping_key_len, NULL);
+
+    if(temp_internal_table) {
+        /* If internal hash table is already present, means this mapping_key is already in memory, no need to create another key that's why freeing here */
+        ZLIB_FREE(mapping_key);
+
+    } else {
+        temp_internal_table = zhash_table_alloc(&zhash_table_allocator);
+        if (!temp_internal_table) {
+            ZPATH_LOG(AL_ERROR, "Failed to allocate memory for internal table!");
+            ZLIB_FREE(mapping_key);
+            return ZPATH_RESULT_NO_ERROR;
+        }
+
+        res = zhash_table_store(hash_table, mapping_key, mapping_key_len, 0, temp_internal_table);
+        if(res) {
+            zhash_table_free(temp_internal_table);
+            ZLIB_FREE(mapping_key);
+            return ZPATH_RESULT_NO_ERROR;
+        }
+        ZLIB_FREE(mapping_key);
+    }
+
+    for(int i = 0; i<obj->partition_gids_count; i++) {
+        int64_t *partition_gid = &obj->partition_gids[i];
+        zpath_partition_map_instance_to_partition(temp_internal_table, inst_gid, partition_gid);
+    }
+
+	return ZPATH_RESULT_NO_ERROR;
+}
+
+static __inline__ int print_lp_map_inner_hash_table (void *cookie1, void *cookie2,
+                                                        void *cookie3, void *cookie4, void *cookie5,
+                                                        void *object,
+                                                        void *key, size_t key_len)
+{
+    struct zpath_debug_state  *request_state = cookie1;
+    char *coordinate_str = cookie2;
+    int *first_element = cookie5;
+
+    struct zpath_partition_instances_name *temp_value = object;
+    int64_t *partition_gid = key;
+
+    char *partition_name = zpath_partition_gid_to_name(*partition_gid);
+    char partition_field[60];
+    snprintf(partition_field, sizeof(partition_field), "%s (%"PRId64")", partition_name, *partition_gid);
+
+    /* This is part of table design */
+    char column2_width[63];
+    char column3_width[73];
+    memset(column2_width, '-', 62);
+    memset(column3_width, '-', 72);
+    column2_width[62] = '\0';
+    column3_width[72] = '\0';
+    // Ends here.
+
+    if(!first_element || (*first_element == 0)) {
+        ZDP("| %-30s |%-62s|%-72s|\n", "", column2_width, column3_width);
+    }
+
+    for(int i=0; i<temp_value->instances_count; i++) {
+        if (first_element && (*first_element == 1)) {
+            *first_element = 0;
+            ZDP("| %-30s | %-60s | %-70s |\n", coordinate_str, (i==0) ? partition_field : "", temp_value->instances[i]);
+            continue;
+        }
+        ZDP("| %-30s | %-60s | %-70s |\n", "", (i==0) ? partition_field : "", temp_value->instances[i]);
+    }
+
+    return ZPATH_RESULT_NO_ERROR;
+}
+
+static __inline__ int print_lp_map_outer_hash_table (void *cookie1, void *cookie2,
+                                                                void *cookie3, void *cookie4, void *cookie5,
+                                                                void *object,
+                                                                void *key, size_t key_len)
+{
+    struct zpath_debug_state  *request_state = cookie1;
+    char *key_type_str = cookie2;
+
+    struct zhash_table *instances_obj = object;
+    char key_str[30];
+
+    if(strcmp(key_type_str, "coordinates") == 0){
+        struct zpath_instance_coordinates *coordinates = key;
+        snprintf(key_str, sizeof(key_str), "(%.2f,%.2f)", coordinates->latitude, coordinates->longitude);
+
+    } else if (strcmp(key_type_str, "dc") == 0) {
+        char *dc_name = key;
+        snprintf(key_str, sizeof(key_str), "%s", dc_name);
+    }
+
+    int walk_first_element = 1; /* This is just for printing purpose (Printing nicely)*/
+
+    char border_table[169];
+    memset(border_table, '=', 168);
+    border_table[168] = '\0';
+
+    zhash_table_walk2(instances_obj, NULL, print_lp_map_inner_hash_table , request_state, key_str, NULL, NULL, &walk_first_element);
+    ZDP("| %-30s | %-60s | %-70s |\n", "", "", "");
+    ZDP("|%-168s|\n", border_table);
+    ZDP("| %-30s | %-60s | %-70s |\n", "", "", "");
+
+    return ZPATH_RESULT_NO_ERROR;
+}
+
+
+static int debug_partition_map_by_coordinates_in_active_profile(struct zpath_debug_state *request_state,
+    const char **query_values,
+    int query_value_count,
+    void *object)
+{
+    int lat_lon_filter_provided = 0;
+
+    struct zpath_instance_coordinates filter_lat_lon;
+
+    filter_lat_lon.latitude = 0.0;
+    filter_lat_lon.longitude = 0.0;
+
+    if(!zpath_get_logical_partition_feature_status()) {
+        ZDP("Logical partitioning feature is not enabled.\n");
+        return ZPATH_RESULT_NO_ERROR;
+    }
+
+    if (query_values && query_values[0] && query_values[1]){ /* Need both lat and lon value to proceed with filter query */
+        lat_lon_filter_provided = 1;
+        char *endptr_lat, *endptr_lon;
+
+        filter_lat_lon.latitude = strtod(query_values[0], &endptr_lat);
+        filter_lat_lon.longitude = strtod(query_values[1], &endptr_lon);
+
+        if (!(*endptr_lat == '\0') || !(*endptr_lon == '\0')){  /* One of the value of lat or lon is incorrect, exit from here */
+            ZDP("Lat-Lon provided are not correct!\n");
+            return ZPATH_RESULT_NO_ERROR;
+        }
+    }
+
+    struct zhash_table *temp_hash_table = NULL;
+    temp_hash_table = zhash_table_alloc(&zhash_table_allocator);
+
+    /* Code for creating the hash-table
+     * zhash-table -> passing cookies
+     * cookie1 -> temp-hash-table pointer
+     * cookie2 -> key_str for outer hash-table, currently []"coordinates", "dc"] supported.
+     * cookie3 -> filter [coordinates] : If wanted to filter via outer hash-table key.
+    */
+    if (lat_lon_filter_provided) {
+        zhash_table_walk2(instance_to_partitions_table, NULL, partition_to_instance_mapping_cb, temp_hash_table, "coordinates", &filter_lat_lon, NULL, NULL);
+    } else {
+        zhash_table_walk2(instance_to_partitions_table, NULL, partition_to_instance_mapping_cb, temp_hash_table, "coordinates", NULL, NULL, NULL);
+    }
+
+    char border_table[169];
+    memset(border_table, '=', 168);
+    border_table[168] = '\0';
+
+    ZDP("|%-168s|\n", border_table);
+    ZDP("| %-30s | %-60s | %-70s |\n", "DC (Lat-Lon)", "Partition", "Instance");
+    ZDP("|%-168s|\n", border_table);
+    ZDP("| %-30s | %-60s | %-70s |\n", "", "", "");
+
+    zhash_table_walk2(temp_hash_table, NULL, print_lp_map_outer_hash_table , request_state, "coordinates", NULL, NULL, NULL);
+    ZDP("|%-168s|\n", border_table);
+
+    zhash_table_free_and_call(temp_hash_table, free_partition_mapping_outer_hash_table, "coordinates");
+
+    return ZPATH_RESULT_NO_ERROR;
+
+}
+
+static int debug_partition_map_by_dc_in_active_profile(struct zpath_debug_state *request_state,
+    const char **query_values,
+    int query_value_count,
+    void *object)
+{
+    int dc_name_provided = 0;
+
+    char *dc_name = NULL;
+
+    if(!zpath_get_logical_partition_feature_status()) {
+        ZDP("Logical partitioning feature is not enabled.\n");
+        return ZPATH_RESULT_NO_ERROR;
+    }
+
+    if (query_values && query_values[0]){ /* Need both lat and lon value to proceed with filter query */
+        dc_name_provided = 1;
+        dc_name = ZLIB_STRDUP(query_values[0], strlen(query_values[0]));
+    }
+
+    struct zhash_table *temp_hash_table = NULL;
+    temp_hash_table = zhash_table_alloc(&zhash_table_allocator);
+
+    /* Code for creating the hash-table
+     * zhash-table -> passing cookies
+     * cookie1 -> temp-hash-table pointer
+     * cookie2 -> key_str for outer hash-table, currently []"coordinates", "dc"] supported.
+     * cookie3 -> filter [dc-name] : If wanted to filter via outer hash-table key.
+    */
+    if (dc_name_provided) {
+        zhash_table_walk2(instance_to_partitions_table, NULL, partition_to_instance_mapping_cb, temp_hash_table, "dc", dc_name, NULL, NULL);
+    } else {
+        zhash_table_walk2(instance_to_partitions_table, NULL, partition_to_instance_mapping_cb, temp_hash_table, "dc", NULL, NULL, NULL);
+    }
+
+    if(dc_name) {
+        ZLIB_FREE(dc_name);
+    }
+
+    char border_table[169];
+    memset(border_table, '=', 168);
+    border_table[168] = '\0';
+
+    ZDP("|%-168s|\n", border_table);
+    ZDP("| %-30s | %-60s | %-70s |\n", "DC Name", "Partition", "Instance");
+    ZDP("|%-168s|\n", border_table);
+    ZDP("| %-30s | %-60s | %-70s |\n", "", "", "");
+
+    zhash_table_walk2(temp_hash_table, NULL, print_lp_map_outer_hash_table , request_state, "dc", NULL, NULL, NULL);
+    ZDP("|%-168s|\n", border_table);
+
+    zhash_table_free_and_call(temp_hash_table, free_partition_mapping_outer_hash_table, "dc");
+
+    return ZPATH_RESULT_NO_ERROR;
+
+}
+
+
 int debug_partition_paused_profile(struct zpath_debug_state *request_state,
                              const char **query_values,
                              int query_value_count,
@@ -1227,6 +1645,24 @@ static int zpath_partition_init_debug_commands()
                 NULL);
     if (res) return res;
 
+    res = zpath_debug_add_safe_read_command("Show LP map by coordinates (lat-lon)",
+                "/balance/partition/lp_map/coordinates",
+                debug_partition_map_by_coordinates_in_active_profile,
+                NULL,
+                "lat",  "<optional> Range [-180, 180]",
+                "lon",     "<optional> Range [-90,90]",
+                NULL);
+    if (res) return res;
+
+    res = zpath_debug_add_safe_read_command("Show LP map by DC",
+        "/balance/partition/lp_map/dc",
+        debug_partition_map_by_dc_in_active_profile,
+        NULL,
+        "dc",  "<optional> string",
+        NULL);
+
+    if (res) return res;
+
     res = zpath_debug_add_safe_read_command("View partitions added to cshash in the current profile",
                 "/balance/partition/cshash_partition",
                 debug_partition_cshash_partition,
diff --git a/src/zpath_lib/zpath_partition_profile.h b/src/zpath_lib/zpath_partition_profile.h
index 4b0a8d9c3f..a165336b57 100644
--- a/src/zpath_lib/zpath_partition_profile.h
+++ b/src/zpath_lib/zpath_partition_profile.h
@@ -30,6 +30,7 @@ struct zpath_partition_profile_instance {    /* _ARGO: object_definition */
     int64_t modifiedby_userid;                          /* _ARGO: integer */
     int8_t deleted;                                     /* _ARGO: integer, deleted */
     int64_t request_id;                                 /* _ARGO: integer, nodb, reqid */
+    char *instance_display_name;                        /* _ARGO: string */
 
     int64_t profile_gid;                                /* _ARGO: integer, index */
     int64_t instance_gid;                               /* _ARGO: integer, index */
@@ -80,6 +81,10 @@ int zpath_partition_profile_get_instances(int64_t profile_gid,
                                           struct zpath_partition_profile_instance **instances,
                                           size_t *count);
 
+int zpath_partition_profile_get_instances_by_instance_gid(int64_t profile_gid,
+                                          struct zpath_partition_profile_instance **instances,
+                                          size_t *count);
+
 /* partitions and count are in/out - make sure partitions is allocated */
 int zpath_partition_profile_get_partitions(int64_t profile_gid,
                                           struct zpath_partition_profile_partition **partitions,
