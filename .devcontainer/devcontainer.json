{"name": "itasca-dev", "image": "harbor.et.zscaler.com/itasca-dev/el9:1.29.0", "mounts": [{"source": "cache_vol", "target": "/home/<USER>/.cache", "type": "volume"}], "workspaceMount": "source=${localWorkspaceFolder},target=/itasca,type=bind", "workspaceFolder": "/itasca", "customizations": {"vscode": {"extensions": ["golang.go", "ms-azuretools.vscode-docker", "ms-vscode.cpptools-extension-pack", "rust-lang.rust-analyzer"]}}, "containerEnv": {"NODE_EXTRA_CA_CERTS": "/itasca/.devcontainer/zscaler-rootca.crt"}}