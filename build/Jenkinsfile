import com.zscaler.et.BitbucketServer
import com.zscaler.et.JiraServer

@Library(["shared_libraries@1.35.0", "api-services@2.4.0"]) _
Map<String,List> artifactLinks = [:]
dockerImages = []
helmCharts = []
ninjaLogs = []

pipeline {
    agent { label 'generic-large-x86' }
    environment {
        BUILD_AGENT_TAG = "1.29.0"
        JENKINS_BUILD = 1
        PATH = "${env.HOME}/.cargo/bin:${env.HOME}/.local/bin:/usr/local/go/bin:/vcpkg:${env.PATH}"
        COVERITY_TOOL_HOME="/mnt/efs/cov-analysis-linux64-2024.6.0"
        COVERITY_TARGETS="agrep zpa-connector-child zpa-service-edge-child zpa-pcc-child sarge slogger wallyd lookup uatu zpn_brokerd zpn_clientd zpn_l4proxyd zpn_dispatcherd zpn_iparsd zpn_npgatewayd natural zblu exporter"
    }
    options {
        parallelsAlwaysFailFast()
        skipDefaultCheckout true
        timeout(time: 1, unit: 'HOURS')   // timeout on whole pipeline job
    }

    stages {
        stage('Init') {
            steps {
                script {
                    if (env.BRANCH_NAME == 'master' || env.BRANCH_NAME.startsWith('release/')) {
                        addGitTag()
                    }
                    gitCheckout(false)
                    def version = readFile('build/version.txt').trim()
                    env.SIGNED = false

                    if (env.BRANCH_NAME.startsWith('release/')) {
                        if (env.GIT_DESCRIBE.contains(env.GIT_HEAD_ID) || sh(returnStdout: true, script: 'git describe --exact-match --tags').trim() != version) {
                            error "Oops, release build is somehow not considered a release: GIT_DESCRIBE='${env.GIT_DESCRIBE}', version='${version}'"
                        }
                        addJiraRelease(version)
                        env.SIGNED = true
                    } else if (env.CHANGE_TARGET && (env.CHANGE_TARGET == 'master' || env.CHANGE_TARGET.startsWith('release/'))) {
                        failIfGitTagExists(version)
                    }

                    setCoverityEnabledVars()
                    env.ITASCA_VERSION = sh(returnStdout: true, script: "./build/get_version.sh").trim()

                    jenkinsUtils.buildInfo()
                    withCredentials([usernameColonPassword(credentialsId: "55d4e39a-fdf1-4084-b99d-8a2771d83b2a", variable: "USERPASS")]) {
                        env.NEXUS_USERPASS = USERPASS
                    }
                }
            }
        }

        stage('Build All') {
            parallel {
                stage('el8 x64 release') {
                    agent {
                        kubernetes {
                            inheritFrom 'jenkins-agent-itasca-x86'
                            yamlMergeStrategy new org.csanchez.jenkins.plugins.kubernetes.pod.yaml.Merge()
                            yaml genBuildAgentYaml(platform: 'el8', cpu: 24, mem: '48Gi')
                        }
                    }
                    steps {
                        script {
                            container('itasca') {
                                gitCheckout()
                                buildSargePkgsOnly('el8', 'x64')
                                dirtyCheck()
                                artifactLinks['el8_x86'] = uploadArtifacts(platform: 'el8', uploadBin: true, uploadVersion: true)
                                stashNinjaLog('el8-x64-release', 'x64-linux-release')
                            }
                        }
                    }
                    post {
                        cleanup {
                            script {
                                myCleanWs()
                            }
                        }
                    }
                }

                stage('el9 x64 release') {
                    agent {
                        kubernetes {
                            inheritFrom 'jenkins-agent-itasca-x86'
                            yamlMergeStrategy new org.csanchez.jenkins.plugins.kubernetes.pod.yaml.Merge()
                            yaml genBuildAgentYaml(platform: 'el9', cpu: 24, mem: '48Gi')
                        }
                    }
                    steps {
                        script {
                            container('itasca') {
                                gitCheckout()
                                sh "cd /tmpfs && time scl enable gcc-toolset-14 -- cmake --workflow x64-linux-release"
                                sh "ccache -s"
                                dirtyCheck()
                                artifactLinks['el9_x86'] = uploadArtifacts(platform: 'el9', uploadBin: true, uploadVersion: true)
                                stashNinjaLog('el9-x64-release', 'x64-linux-release')
                            }
                        }
                    }
                    post {
                        cleanup {
                            script {
                                myCleanWs()
                            }
                        }
                    }
                }

                stage('el9 x64 debug') {
                    agent {
                        kubernetes {
                            inheritFrom 'jenkins-agent-itasca-x86'
                            yamlMergeStrategy new org.csanchez.jenkins.plugins.kubernetes.pod.yaml.Merge()
                            yaml genBuildAgentYaml(platform: 'el9', cpu: 24, mem: '48Gi')
                        }
                    }
                    steps {
                        script {
                            container('itasca') {
                                gitCheckout()
                                sh "cd /tmpfs && time scl enable gcc-toolset-14 -- cmake --workflow x64-linux-debug"
                                dirtyCheck()
                                artifactLinks['el9_x86_debug'] = uploadArtifacts(platform: 'el9', uploadBin: false, uploadVersion: false)
                                stashNinjaLog('el9-x64-debug', 'x64-linux-debug')
                            }
                        }
                    }
                    post {
                        cleanup {
                            script {
                                myCleanWs()
                            }
                        }
                    }
                }

                stage('el9 x64 asan') {
                    agent {
                        kubernetes {
                            inheritFrom 'jenkins-agent-itasca-x86'
                            yamlMergeStrategy new org.csanchez.jenkins.plugins.kubernetes.pod.yaml.Merge()
                            yaml genBuildAgentYaml(platform: 'el9', cpu: 24, mem: '48Gi')
                        }
                    }
                    steps {
                        script {
                            container('itasca') {
                                gitCheckout()
                                sh "cd /tmpfs && time scl enable gcc-toolset-14 -- cmake --workflow x64-linux-asan"
                                dirtyCheck()
                                artifactLinks['el9_x86_asan'] = uploadArtifacts(platform: 'el9', uploadBin: false, uploadVersion: false)
                                stashNinjaLog('el9-x64-asan', 'x64-linux-asan')
                            }
                        }
                    }
                    post {
                        cleanup {
                            script {
                                myCleanWs()
                            }
                        }
                    }
                }

                stage('el9 arm64 release') {
                    agent {
                        kubernetes {
                            inheritFrom 'jenkins-agent-itasca-arm'
                            yamlMergeStrategy new org.csanchez.jenkins.plugins.kubernetes.pod.yaml.Merge()
                            yaml genBuildAgentYaml(platform: 'el9', cpu: 24, mem: '48Gi')
                        }
                    }
                    steps {
                        script {
                            container('itasca') {
                                gitCheckout()
                                sh "cd /tmpfs && time scl enable gcc-toolset-14 -- cmake --workflow arm64-linux-release"
                                dirtyCheck()
                                artifactLinks['el9_arm'] = uploadArtifacts(platform: 'el9', uploadBin: true, uploadVersion: false)
                                stashNinjaLog('el9-arm64-release', 'arm64-linux-release')
                            }
                        }
                    }
                    post {
                        cleanup {
                            script {
                                myCleanWs()
                            }
                        }
                    }
                }

                stage('el9 arm64 asan') {
                    agent {
                        kubernetes {
                            inheritFrom 'jenkins-agent-itasca-arm'
                            yamlMergeStrategy new org.csanchez.jenkins.plugins.kubernetes.pod.yaml.Merge()
                            yaml genBuildAgentYaml(platform: 'el9', cpu: 24, mem: '48Gi')
                        }
                    }
                    steps {
                        script {
                            container('itasca') {
                                gitCheckout()
                                sh "cd /tmpfs && time scl enable gcc-toolset-14 -- cmake --workflow arm64-linux-asan"
                                dirtyCheck()
                                artifactLinks['el9_arm_asan'] = uploadArtifacts(platform: 'el9', uploadBin: false, uploadVersion: false)
                                stashNinjaLog('el9-arm64-asan', 'arm64-linux-asan')
                            }
                        }
                    }
                    post {
                        cleanup {
                            script {
                                myCleanWs()
                            }
                        }
                    }
                }

                stage('el9 arm64 fuzz') {
                    agent {
                        kubernetes {
                            inheritFrom 'jenkins-agent-itasca-arm'
                            yamlMergeStrategy new org.csanchez.jenkins.plugins.kubernetes.pod.yaml.Merge()
                            yaml genBuildAgentYaml(platform: 'el9', cpu: 24, mem: '48Gi')
                        }
                    }
                    environment {
                        BASE_FUZZ_MINIMIZED_CORPUS = "/tmpfs/out/fuzz_minimized_input/"
                        BASE_FUZZ_CORPUS = "/tmpfs/out/fuzzinput/"
                        BASE_FUZZ_OUTPUT = "/tmpfs/out/fuzzoutput/"
                        BASE_EFS_FUZZ_CORPUS = "/mnt/efs/fuzz/input/"
                        FUZZ_DOC_URL = "https://confluence.corp.zscaler.com/pages/viewpage.action?pageId=737998545"
                    }
                    steps {
                        script {
                            container('itasca') {
                                echo "Fuzzing stage for detecting crash/leak. For details, visit $FUZZ_DOC_URL"

                                gitCheckout()

                                // fuzzTarget's naming convention like '<component>_<specific_fuzz_behavior>'
                                def fuzzTargets = ["argo_deserialize_json_fuzz"]

                                for (fuzzTarget in fuzzTargets) {
                                    def component = fuzzTarget.split('_')[0]

                                    def fuzz_seeds = "/tmpfs/src/${component}/${fuzzTarget}/seeds/"
                                    def fuzz_corpus = "${BASE_FUZZ_CORPUS}${fuzzTarget}/"
                                    def fuzz_output = "${BASE_FUZZ_OUTPUT}${fuzzTarget}/"
                                    def fuzz_minimized_corpus = "${BASE_FUZZ_MINIMIZED_CORPUS}${fuzzTarget}/"
                                    def efs_fuzz_corpus = "${BASE_EFS_FUZZ_CORPUS}${fuzzTarget}/"

                                    // Prepare the fuzzing input/output directories
                                    sh "mkdir -p ${fuzz_corpus} ${fuzz_output} ${efs_fuzz_corpus} ${fuzz_minimized_corpus}"

                                    sh "cd /tmpfs && cmake --preset arm64-linux-fuzz"
                                    sh "cd /tmpfs && ASAN_OPTIONS=detect_leaks=0 cmake --build out/arm64-linux-fuzz/ -t ${fuzzTarget}"

                                    // Prepare fuzzing input files
                                    // We download continue fuzzing inputs from EFS (if there is any),
                                    downloadFuzzCorpus("${efs_fuzz_corpus}", "${fuzz_corpus}", "${fuzzTarget}")

                                    // Run the fuzzing executable
                                    // TODO: we will add more fuzzing modules, currently only argo
                                    catchError(buildResult: 'SUCCESS', stageResult: 'UNSTABLE') {
                                        echo "Fuzzing start ..."
                                        sh "cd /tmpfs/out/arm64-linux-fuzz/src/${component} && ./${fuzzTarget} ${fuzz_corpus} ${fuzz_seeds} -artifact_prefix=${fuzz_output} -fork=\$REQUEST_CPU -ignore_crashes=1 -max_total_time=30"
                                        echo "Fuzzing failure! Treating as warning."

                                        // Minimize fuzzing corpus and seeds
                                        sh "cd /tmpfs/out/arm64-linux-fuzz/src/${component} && ./${fuzzTarget} -merge=1 ${fuzz_minimized_corpus} ${fuzz_corpus} ${fuzz_seeds}"
                                    }

                                    // Zip and upload the minimized corpus to EFS
                                    zipAndUpload("${fuzz_minimized_corpus}", "${efs_fuzz_corpus}", "${fuzzTarget}")

                                    // Fuzz output to Jenkins
                                    sh """
                                        if [ -d '${BASE_FUZZ_OUTPUT}${fuzzTarget}' ] && [ "\$(ls -A '${BASE_FUZZ_OUTPUT}${fuzzTarget}')" ]; then
                                            cd '${BASE_FUZZ_OUTPUT}' && \
                                            zip -r '${fuzzTarget}_output.zip' '${fuzzTarget}' && \
                                            mv '${BASE_FUZZ_OUTPUT}${fuzzTarget}_output.zip' '${env.WORKSPACE}'
                                        else
                                            echo "Directory ${BASE_FUZZ_OUTPUT}${fuzzTarget} is empty."
                                        fi
                                    """
                                    archiveArtifacts artifacts: "${fuzzTarget}_output.zip", allowEmptyArchive: true
                                }

                                stashNinjaLog('el9-arm64-fuzz', 'arm64-linux-fuzz')
                            }
                        }
                    }
                    post {
                        // Currently manually set the build result as 'SUCCESS' to avoid develop work blocking by the fuzzing
                        // TODO: After we fix enough fuzzing crashes,
                        // set the fuzz stage to back to keep it original status
                        unstable {
                            script {
                                echo "Overriding pipeline status to SUCCESS to meet Bitbucket requirements."
                                currentBuild.result = 'SUCCESS'
                            }
                        }
                        cleanup {
                            script {
                                // Clean up workspace after stage execution
                                myCleanWs()
                            }
                        }
                    }
                }

                stage('clang') {
                    agent {
                        kubernetes {
                            inheritFrom 'jenkins-agent-itasca-x86'
                            yamlMergeStrategy new org.csanchez.jenkins.plugins.kubernetes.pod.yaml.Merge()
                            yaml genBuildAgentYaml(platform: 'el9', cpu: 24, mem: '48Gi')
                        }
                    }
                    steps {
                        script {
                            container('itasca') {
                                gitCheckout()
                                sh """
                                cd /tmpfs
                                CC=clang CXX=clang++ cmake --preset x64-linux-release
                                cmake --build out/x64-linux-release
                                ctest --preset x64-linux-release
                                """
                                dirtyCheck()
                                stashNinjaLog('clang', 'x64-linux-release')
                            }
                        }
                    }
                    post {
                        cleanup {
                            script {
                                myCleanWs()
                            }
                        }
                    }
                }

                stage('misc') {
                    agent {
                        kubernetes {
                            inheritFrom 'jenkins-agent-itasca-x86'
                            yamlMergeStrategy new org.csanchez.jenkins.plugins.kubernetes.pod.yaml.Merge()
                            yaml genBuildAgentYaml(platform: 'el9', cpu: 24, mem: '48Gi')
                        }
                    }
                    steps {
                        script {
                            container('itasca') {
                                gitCheckout()
                                sh "git config --global --add safe.directory '*'"
                                sh "cd /tmpfs && PRE_COMMIT_HOME=/mnt/efs/cache/pre-commit pre-commit run --all-files --show-diff-on-failure"
                                sh "cd /tmpfs && cargo fmt --check"
                                sh "cd /tmpfs && cargo build"
                                sh "cd /tmpfs && cargo test"
                                sh "cd /tmpfs && cargo clippy -- -Dclippy::pedantic -Dwarnings"
                            }
                        }
                    }
                    post {
                        cleanup {
                            script {
                                myCleanWs()
                            }
                        }
                    }
                }

                stage('Coverity Full Scan') {
                    when {
                        expression {
                            env.COVERITY_SOURCE_ENABLED == 'true' || env.BRANCH_NAME == "master" || env.BRANCH_NAME.startsWith("release/")
                        }
                        beforeAgent true
                    }
                    agent {
                        kubernetes {
                            cloud 'eks-build-cc'
                            inheritFrom 'jenkins-agent-cc-generic-x86'
                            yamlMergeStrategy new org.csanchez.jenkins.plugins.kubernetes.pod.yaml.Merge()
                            yaml genBuildAgentYaml(platform: 'el9', cpu: 29, mem: '29Gi', isCoverity: true)
                        }
                    }
                    steps {
                        script {
                            container('itasca') {
                                gitCheckout(false)
                                def branch = env.CHANGE_BRANCH ? env.CHANGE_BRANCH : env.BRANCH_NAME
                                env.COV_STREAM_FULLSCAN = "datapath-${branch.replace('/', '-')}-stream"
                                withEnv(["PATH+COVERITYTOOLBIN=${COVERITY_TOOL_HOME}/bin"]) {
                                    sh "export"
                                    // Coverity build
                                    println("Running Coverity build")
                                    sh """
                                        time scl enable gcc-toolset-14 -- cmake --preset x64-linux-debug
                                        time cov-build --dir ${env.WORKSPACE}/idir cmake --build out/x64-linux-debug -t ${env.COVERITY_TARGETS}
                                    """
                                    stashNinjaLog('coverity-full', 'x64-linux-debug')
                                    // Coverity analysis
                                    println("Performing Coverity analysis")
                                    sh """
                                        cov-analyze --dir ${WORKSPACE}/idir \
                                            --security --webapp-security --enable-constraint-fpp \
                                            --disable-parse-warnings --allow-unmerged-emits \
                                            --strip-path ${WORKSPACE} --all -j auto
                                    """
                                    // Coverity HTML report
                                    println("Publishing Coverity report")
                                    sh """
                                        cov-format-errors --dir ${WORKSPACE}/idir \
                                            --html-output defects --sort file
                                    """
                                    // Create coverity Stream
                                    println("Creating Coverity stream")
                                    withCredentials([string(credentialsId: '9f5eae3d-334f-4260-8117-3f0c3f4091ea', variable: 'COV_AUTH_STREAM')]) {
                                        sh(script: "python3 ./build/CreateStream.py")
                                    }
                                    // Commit to Coverity server
                                    println("Running Coverity commit")
                                    withCredentials([usernamePassword(credentialsId: 'coverity-service-account', passwordVariable: 'COVERITY_PASSWORD', usernameVariable: 'COVERITY_USERNAME')]) {
                                        sh """
                                            cov-commit-defects --dir ${WORKSPACE}/idir \
                                                --url https://coverity.corp.zscaler.com/ \
                                                --user ${COVERITY_USERNAME} \
                                                --password ${COVERITY_PASSWORD} \
                                                --stream ${COV_STREAM_FULLSCAN} \
                                                --version ${GIT_COMMIT}
                                        """
                                    }
                                }
                            }
                        }
                    }
                    post {
                        always{
                            publishHTML(
                                [
                                    allowMissing: true,
                                    alwaysLinkToLastBuild: false,
                                    keepAll: true,
                                    reportDir: 'defects',
                                    reportFiles: 'index.html',
                                    reportName: 'Coverity Report',
                                    reportTitles: 'Coverity Report'
                                ]
                            )
                        }
                        cleanup {
                            cleanWs()
                        }
                    }
                }

                stage('Coverity PR Scan') {
                    when {
                        expression { env.CHANGE_TARGET }
                        beforeAgent true
                    }
                    agent {
                        kubernetes {
                            cloud 'eks-build-cc'
                            inheritFrom 'jenkins-agent-cc-generic-x86'
                            yamlMergeStrategy new org.csanchez.jenkins.plugins.kubernetes.pod.yaml.Merge()
                            yaml genBuildAgentYaml(platform: 'el9', cpu: 29, mem: '29Gi', isCoverity: true)
                        }
                    }
                    steps {
                        script {
                            container('itasca') {
                                gitCheckout(false)
                                def branch = env.COVERITY_TARGET_ENABLED == 'true' ? env.CHANGE_TARGET : 'master'
                                env.COV_STREAM_PR = "datapath-${branch.replace('/', '-')}-stream"
                                withEnv(["PATH+COVERITYTOOLBIN=${COVERITY_TOOL_HOME}/bin"]) {
                                    sh "export"
                                    // Coverity build
                                    println("Running Coverity build")
                                    sh """
                                        time scl enable gcc-toolset-14 -- cmake --preset x64-linux-debug
                                        time cov-build --dir ${env.WORKSPACE}/idir cmake --build out/x64-linux-debug -t ${env.COVERITY_TARGETS}
                                    """
                                    stashNinjaLog('coverity-pr', 'x64-linux-debug')
                                    println("Getting the PR diff for Coverity scan")
                                    withCredentials([usernamePassword(credentialsId: 'et-jenkins-bitbucket', passwordVariable: 'BB_PASSWORD', usernameVariable: 'BB_USERNAME')]) {
                                        sh(script: "python3 ./build/getPRchanges.py")
                                        def prop = readJSON file: 'props.json'
                                        env.CHANGE_COUNT = prop['count']
                                        env.CHANGE_FILES = prop['files']
                                    }
                                    // Coverity PR scan
                                    println("Running Coverity PR Scan")
                                    if (Integer.parseInt(env.CHANGE_COUNT) > 0) {
                                        withCredentials([usernamePassword(credentialsId: 'coverity-service-account', passwordVariable: 'COVERITY_PASSWORD', usernameVariable: 'COVERITY_USERNAME')]) {
                                            sh """
                                                cp /mnt/efs/coverity/covpr/itasca/coverity.conf .
                                                cov-run-desktop --dir ${WORKSPACE}/idir \
                                                    --url https://coverity.corp.zscaler.com/ \
                                                    --user ${COVERITY_USERNAME} \
                                                    --password ${COVERITY_PASSWORD} \
                                                    --stream ${COV_STREAM_PR} \
                                                    --ignore-uncapturable-inputs true \
                                                    --present-in-reference false \
                                                    --disable-parse-warnings \
                                                    --disable RW.* \
                                                    -j auto \
                                                    ${CHANGE_FILES} 2>&1 | tee coverity_scan.log
                                            """
                                        }
                                    }
                                    // Update Coverity report
                                    println("Updating Coverity report on BB")
                                    withCredentials(
                                        [
                                            usernamePassword(credentialsId: 'coverity-service-account', passwordVariable: 'COVERITY_PASSWORD', usernameVariable: 'COVERITY_USERNAME'),
                                            usernamePassword(credentialsId: 'et-jenkins-bitbucket', passwordVariable: 'BB_PASSWORD', usernameVariable: 'BB_USERNAME')
                                        ]
                                    ) {
                                        sh(script: "python3 ./build/parseCoverityReport.py")
                                    }
                                }
                            }
                        }
                    }
                    post {
                        cleanup {
                            cleanWs()
                        }
                    }
                }

                stage('Sonar') {
                    when {
                        expression {
                            env.CHANGE_BRANCH || env.BRANCH_NAME == "master" || env.BRANCH_NAME.startsWith("release/")
                        }
                        beforeAgent true
                    }
                    agent {
                        kubernetes {
                            inheritFrom 'jenkins-agent-itasca-x86'
                            yamlMergeStrategy new org.csanchez.jenkins.plugins.kubernetes.pod.yaml.Merge()
                            yaml genBuildAgentYaml(platform: 'el9', cpu: 24, mem: '48Gi')
                        }
                    }
                    steps {
                        script {
                            container('itasca') {
                                gitCheckout()
                                def sonarScannerHome = tool 'sonar_scanner_et_6.2'
                                withEnv(["scannerHome=${sonarScannerHome}"]) {
                                    sonarStep()
                                }
                                stashNinjaLog('sonar', 'x64-linux-cov')
                            }
                        }
                    }
                    post {
                        always {
                            junit allowEmptyResults: true, skipMarkingBuildUnstable: true, testResults: 'junit.xml'
                        }
                        cleanup {
                            script {
                                myCleanWs()
                            }
                        }
                    }
                }

                stage('SCA Scans') {
                    when {
                        expression {
                            env.CHANGE_BRANCH || env.BRANCH_NAME == "master" || env.BRANCH_NAME.startsWith("release/")
                        }
                        beforeAgent true
                    }
                    agent {
                        kubernetes {
                            inheritFrom 'jenkins-agent-itasca-x86'
                            yamlMergeStrategy new org.csanchez.jenkins.plugins.kubernetes.pod.yaml.Merge()
                            yaml genBuildAgentYaml(platform: 'el9', cpu: 16, mem: '16Gi')
                        }
                    }
                    steps {
                        script {
                            container('itasca') {
                                gitCheckout(false)
                              	sh "scl enable gcc-toolset-14 -- vcpkg install --triplet x64-linux-debug --only-downloads --x-buildtrees-root=3rd_party"
                                scanners.blackduckScan("Itasca")
                                scanners.snykInit("Itasca")
                            }
                        }
                    }
                    post {
                        cleanup {
                            cleanWs()
                        }
                    }
                }

                stage('Flexcloud') {
                    when {
                        expression {
                            env.CHANGE_BRANCH || env.BRANCH_NAME == "master" || env.BRANCH_NAME.startsWith("release/")
                        }
                        beforeAgent true
                    }
                    stages {
                        stage('agrep') {
                            agent {
                                kubernetes {
                                    inheritFrom 'jenkins-agent-itasca-x86'
                                    yamlMergeStrategy new org.csanchez.jenkins.plugins.kubernetes.pod.yaml.Merge()
                                    yaml genBuildAgentYaml(platform: 'el9', cpu: 24, mem: '48Gi')
                                }
                            }
                            steps {
                                script {
                                    container('itasca') {
                                        gitCheckout()
                                        sh "cd /tmpfs && scl enable gcc-toolset-14 'cmake --preset x64-linux-release && cmake --build out/x64-linux-release -t agrep && mkdir -p ${env.WORKSPACE}/flexcloud/docker/packages/bin && mv out/x64-linux-release/src/argo/agrep ${env.WORKSPACE}/flexcloud/docker/packages/bin'"
                                        stash(name: "dockerBuildFiles", includes: "flexcloud/docker/packages/**/*")
                                        stashNinjaLog('flexcloud', 'x64-linux-release')
                                    }
                                }
                            }
                            post {
                                cleanup {
                                    script {
                                        myCleanWs()
                                    }
                                }
                            }
                        }

                        stage('docker') {
                            agent {
                                label "docker-rhel9-x86-large"
                            }
                            steps {
                                script {
                                    gitCheckout(false)
                                    env.REGISTRIES = dockerUtils.registryLogin(["ecr_tools"]).join(' ')
                                    env.TAG_SUFFIX = env.SIGNED == 'true' ? '' : '-dev'
                                    env.REPO_PREFIX = env.SIGNED == 'true' ? 'et-eng-prod/' : 'et-eng-dev/'

                                    unstash(name: "dockerBuildFiles")
                                    dockerImages.addAll(dockerUtils.dockerBake(target: "itasca", push: true))
                                    withEnv(["GIT_DESCRIBE=${ITASCA_VERSION}"]) {
                                        def pkg = helmUtils.buildChart("flexcloud/helm/itasca")
                                        helmCharts += helmUtils.pushChart(pkg)
                                    }
                                }
                            }
                            post {
                                cleanup {
                                    cleanWs()
                                }
                            }
                        }
                    }
                }

                stage('zkube') {
                    when {
                        expression {
                            env.CHANGE_BRANCH || env.BRANCH_NAME == "master" || env.BRANCH_NAME.startsWith("release/")
                        }
                        beforeAgent true
                    }
                    stages {
                        stage('Build Natural') {
                            agent {
                                kubernetes {
                                    inheritFrom 'jenkins-agent-itasca-x86'
                                        yamlMergeStrategy new org.csanchez.jenkins.plugins.kubernetes.pod.yaml.Merge()
                                        yaml genBuildAgentYaml(platform: 'el9', cpu: 24, mem: '48Gi')
                                }
                            }
                            steps {
                                script {
                                    container('itasca') {
                                        gitCheckout()
                                            sh """
                                            set +x
                                            cd /tmpfs
                                            scl enable gcc-toolset-14 -- cmake --preset x64-linux-release
                                            cmake --build out/x64-linux-release -t agrep -t rpm-natural
                                            mkdir -p ${env.WORKSPACE}/zkube/docker/packages/bin
                                            mv out/x64-linux-release/src/argo/agrep ${env.WORKSPACE}/zkube/docker/packages/bin
                                            mv out/x64-linux-release/src/natural/natural*.rpm  ${env.WORKSPACE}/zkube/docker/packages/bin/natural.rpm
                                            """
                                            stash(name: "zkubeBuildFiles", includes: "zkube/docker/packages/**/*")
                                            stashNinjaLog('zkube', 'x64-linux-release')
                                    }
                                }
                            }
                            post {
                                cleanup {
                                    script {
                                        myCleanWs()
                                    }
                                }
                            }
                        }
                        stage('Zkube Docker/Helm Build') {
                            when {
                                expression {
                                    env.CHANGE_BRANCH || env.BRANCH_NAME == "master" || env.BRANCH_NAME.startsWith("release/")
                                }
                                beforeAgent true
                            }
                            agent {
                                node {
                                    label "docker-rhel9-x86-large"
                                    customWorkspace "${env.BUILD_TAG}/zkube"
                                }
                            }
                            steps {
                                script {
                                    gitCheckout(false)
                                    env.ZKUBE_REGISTRIES = dockerUtils.registryLogin(['ecr_tools', 'ecr_flex']).join(" ")
                                    env.TAG_SUFFIX = env.SIGNED == 'true' ? '' : '-dev'
                                    env.REPO_PREFIX = env.SIGNED == 'true' ? 'et-eng-prod/' : 'et-eng-dev/'

                                    unstash(name: "zkubeBuildFiles")
                                    dockerImages.addAll(dockerUtils.dockerBake(target: "et-c-producer", push: true))
                                    withEnv(["GIT_DESCRIBE=${ITASCA_VERSION}"]) {
                                        def pkg = helmUtils.buildChart("zkube/helm/et-c-producer")
                                        helmCharts += helmUtils.pushChart(pkg)
                                    }
                                }
                            }
                            post {
                                cleanup {
                                    // Custom workspace doesn't delete the directory
                                    // calling explicit delete
                                    deleteDir()
                                    cleanWs()
                                }
                            }
                        }
                    }
                }
            }
        }

// Temporarily disable, per ET-92080
//        stage('ninja trace') {
//            steps {
//                script {
//                    exportNinjaTrace()
//                }
//            }
//        }
    }

    post {
        cleanup {
            cleanWs()
        }
        aborted {
            script {
                def log = currentBuild.rawBuild.getLog(2000)
                println("Searching the last 2000 lines for error pattern")
                if (log.any { it =~ /(Unable to create live FilePath|Agent was removed)/ }) {
                    println("Sending the slack alert")
                    slackSend channel: "#zpa-itasca-build-alerts", color: "red", message: "Build aborted with Unable to create live FilePath issue *${env.BUILD_URL}", tokenCredentialId: "zpa-itasca-build-alerts"
                }
            }
        }
        always {
            script {
                jenkinsUtils.sendMail(additionalBody: jenkinsUtils.etArtifacts(artifacts: artifactLinks, images: dockerImages, charts: helmCharts))
            }
        }
    }
}

/////////////////////////////////////////////////////////////
//                  Utility functions                      //
/////////////////////////////////////////////////////////////

def sonarStep() {
    withEnv(["SONAR_SCAN=1", "CMAKE_EXPORT_COMPILE_COMMANDS=ON"]) {
        // zpm go
        sh '''
          cd /tmpfs/src/zpm
          go mod init src/zpm
          go mod tidy
          mkdir -p /tmpfs/out/go
          go test . -cover
          go test -coverprofile=/tmpfs/out/go/zpm_coverage.out
          go tool cover -html=/tmpfs/out/go/zpm_coverage.out -o /tmpfs/out/go/zpm_coverage.html
          cat /tmpfs/out/go/zpm_coverage.html
          cd /tmpfs
        '''
        sh "cd /tmpfs && time scl enable gcc-toolset-14 -- cmake --workflow --preset x64-linux-cov && cp out/x64-linux-cov/junit.xml ${env.WORKSPACE}/"
    }

    def cacheBranch
    if (!sh(script: "aws s3 ls s3://zscaler-et-builds/itasca/${env.GIT_SOURCE_BRANCH}/sonar_cache.tar", returnStatus: true)) {
        cacheBranch = env.GIT_SOURCE_BRANCH
    } else if (env.CHANGE_BRANCH) {
        cacheBranch = env.CHANGE_TARGET
    } else {
        cacheBranch = "master"
    }
    sh "cd /tmpfs && (aws s3 cp s3://zscaler-et-builds/itasca/${cacheBranch}/sonar_cache.tar . --no-progress && tar xf sonar_cache.tar) || mkdir -p local_sonar_cache"
    def threads = sh(script: 'echo $REQUEST_CPU', returnStdout: true).trim()
  	if (sonarScan(threads)) {
        sh "cd /tmpfs && rm -rf local_sonar_cache/*"  // Run with clean cache on failure.
        if (sonarScan(threads)) {
            error "Sonar scan failed"
        }
    }

    sh "cd /tmpfs && tar cf sonar_cache.tar local_sonar_cache && aws s3 cp sonar_cache.tar s3://zscaler-et-builds/itasca/${env.GIT_SOURCE_BRANCH}/ --no-progress"
}

def uploadArtifacts(Map params=[:]) {
    sh "cd /tmpfs && ./build/upload_s3.sh ${env.GIT_SOURCE_BRANCH} ${params.platform} ${params.uploadBin} ${params.uploadVersion} ${env.SIGNED}"
    return sh(returnStdout: true, script: 'cat /tmpfs/out/artifacts.txt').trim().readLines()
}

String genBuildAgentYaml(Map params=[:]) {
    def dev = env.BUILD_AGENT_TAG.contains('-') ? "-dev" : ""
    def vcpkgBinarySources = "clear;files,/mnt/efs/cache/vcpkg/binary-cache,readwrite"
    def ccacheEnv = """
            - name: CCACHE_REMOTE_ONLY
    """
    if (!params.isCoverity) {
        vcpkgBinarySources += ";x-aws,s3://zscaler-vcpkg-cache,write"
        ccacheEnv += """
            - name: CCACHE_REMOTE_STORAGE
              value: file:/mnt/efs/cache/ccache/file
        """
    }

    return """
        spec:
          containers:
          - name: itasca
            image: harbor.et.zscaler.com/build-agents${dev}/itasca-cmake-${params.platform}:${env.BUILD_AGENT_TAG}
            alwaysPullImage: true
            imagePullSecrets:
            - harbor-et-jenkins
            env:
            - name: REQUEST_CPU
              value: "${params.cpu}"
            - name: REQUEST_MEMORY
              value: "${params.mem}"
            - name: VCPKG_MAX_CONCURRENCY
              value: "${params.cpu}"
            - name: CMAKE_BUILD_PARALLEL_LEVEL
              value: "${params.cpu}"
            - name: CTEST_PARALLEL_LEVEL
              value: "${params.cpu}"
            - name: VCPKG_BINARY_SOURCES
              value: "${vcpkgBinarySources}"
            - name: X_VCPKG_ASSET_SOURCES
              value: "x-script,${env.WORKSPACE}/vcpkg/asset-cache.sh {url} {sha512} {dst}"
            ${ccacheEnv}
            resources:
              requests:
                cpu: "${params.cpu}"
                memory: "${params.mem}"
              limits:
                cpu: "${params.cpu}"
                memory: "${params.mem}"
            volumeMounts:
            - mountPath: "/mnt/efs"
              name: "volume-0"
              readOnly: false
            - mountPath: /tmpfs
              name: tmpfs-vol
              readOnly: false
          volumes:
          - name: tmpfs-vol
            emptyDir:
              medium: Memory
    """
}

def gitCheckout(isTmpfs = true) {
    def changePathCmd = isTmpfs ? "cd /tmpfs &&" : ""
    def sourceBranch = env.CHANGE_BRANCH ? env.CHANGE_BRANCH : env.BRANCH_NAME
    sshagent(credentials: ['eae250b7-fc91-44b5-ac26-7d8c65a39915']) {
        sh """
            mkdir -p /home/<USER>/.ssh
            ssh-keyscan -t rsa -p 7999 bitbucket.corp.zscaler.com >> /home/<USER>/.ssh/known_hosts
            ${changePathCmd} git clone --filter=tree:0 ssh://******************************:7999/zpa-datapath/datapath.git -b ${sourceBranch} .
            ${isTmpfs ? "git config --global --add safe.directory /tmpfs" : ""}
        """
        if (!env.GIT_COMMIT) {
            env.GIT_URL = "ssh://******************************:7999/zpa-datapath/datapath.git"
            env.GIT_BRANCH = env.BRANCH_NAME
            env.GIT_SOURCE_BRANCH = env.CHANGE_BRANCH ? env.CHANGE_BRANCH : env.BRANCH_NAME
            env.GIT_COMMIT =  "${sh(returnStdout: true, script: "${changePathCmd} git rev-parse HEAD")}".trim()
            env.GIT_DESCRIBE =  "${sh(returnStdout: true, script: "${changePathCmd} git describe --tags --dirty --abbrev=10")}".trim()
            env.GIT_HEAD_ID = "${sh(script: "${changePathCmd} git rev-parse --short=10 HEAD", returnStdout: true)}".trim()
        } else {
            sh "${changePathCmd} git checkout ${env.GIT_COMMIT}"
        }
  	}
}

def sonarScan(threads) {
    def projectBaseName = jenkinsUtils.getMultibranchBaseName()

    withSonarQubeEnv('sonarqube-et') {
        withCredentials([string(credentialsId: 'f01e30d3-3320-44f9-ab8f-96a0991f4658', variable: 'SONAR_TOKEN')]) {
            List scanOptions = [
                '-Dsonar.token=${SONAR_TOKEN}',
                "-Dsonar.projectName=${projectBaseName}",
                "-Dsonar.projectKey=${projectBaseName}",
                "-Dsonar.projectVersion=${env.ITASCA_VERSION}",
                "-Dsonar.cfamily.threads=${threads}"
            ]
            if (env.JOB_BASE_NAME.contains('PR')) {
                scanOptions.addAll([
                    "-Dsonar.pullrequest.key=${env.CHANGE_ID}",
                    "-Dsonar.pullrequest.base=${env.CHANGE_TARGET}",
                    "-Dsonar.pullrequest.branch=${env.CHANGE_BRANCH}"])
            } else {
                scanOptions.add("-Dsonar.branch.name=${env.BRANCH_NAME}")
            }
            def status = sh(script: "set -o pipefail; cd /tmpfs && ${scannerHome}/bin/sonar-scanner ${scanOptions.join(' ')} 2>&1 | tee ${env.WORKSPACE}/sonar_output.txt", returnStatus: true)
            archiveArtifacts(artifacts: 'sonar_output.txt', allowEmptyArchive: true)

            return status
        }
    }
}

def stashNinjaLog(name, preset) {
    def log_in = "out/${preset}/.ninja_log"
    def log_out = "ninja-log-${name}"

    sh "cp /tmpfs/${log_in} ${log_out} || cp ${log_in} ${log_out}"
    stash name: "${log_out}", includes: "${log_out}"
    ninjaLogs += "${log_out}"
}

def exportNinjaTrace() {
    def trace = 'ninja-trace.json'
    for (log in ninjaLogs) { unstash "${log}" }
    sh "./src/tools/ninjatracing --showall ${ninjaLogs.join(' ')} > ${trace}"
    archiveArtifacts "${trace}"
}

def dirtyCheck() {
    if (sh(returnStdout: true, script: "cd /tmpfs && git status --untracked-files=no --porcelain").trim().length() > 0) {
        error "Dirty check failed"
    }
}

def myCleanWs() {
    // In case pods happen to be reused for another build.
    cleanWs()
    sh "rm -rf tmpfs/*"
}

def buildSargePkgsOnly(platform, arch) {
    def preset = "${arch}-linux-release"
    def targets = 'all bin nostrip rpm-zpa-connector rpm-zpa-service-edge rpm-zpa-pcc rpm-zpa-connector-child rpm-zpa-service-edge-child rpm-zpa-pcc-child tar-headers rpm-libargo'
    sh """
    cd /tmpfs
    time scl enable gcc-toolset-14 -- cmake --preset ${preset}
    time cmake --build --preset ${preset} -t ${targets}
    time ctest --preset ${preset}
    """
}

def setCoverityEnabledVars() {
    dir ('datapath-coverity-config') {
        withCredentials([usernamePassword(credentialsId: 'et-jenkins-bitbucket', passwordVariable: 'BB_PASSWORD', usernameVariable: 'BB_USERNAME')]) {
            sh 'curl -u ${BB_USERNAME}:${BB_PASSWORD} "https://bitbucket.corp.zscaler.com/rest/api/1.0/projects/zpa-datapath/repos/datapath-coverity-config/archive?at=master&format=zip" --output master.zip && unzip master.zip'
        }
    }
    def enabled = readFile('datapath-coverity-config/enabledBranches.conf').split('\n').collect{it.trim()}.findAll{!it.startsWith('#') && !it.isEmpty()}
    sh 'rm -rf datapath-coverity-config*'

    env.COVERITY_SOURCE_ENABLED = enabled.contains(env.CHANGE_BRANCH ? env.CHANGE_BRANCH : env.BRANCH_NAME)
    env.COVERITY_TARGET_ENABLED = env.CHANGE_TARGET ? enabled.contains(CHANGE_TARGET) : false
}

BitbucketServer getBBServer() {
    BitbucketServer server
    withCredentials([usernamePassword(credentialsId: auth.getBitbucketCreds(), passwordVariable: 'PASSWORD', usernameVariable: 'USERNAME')]) {
        server = new BitbucketServer('bitbucket.corp.zscaler.com', USERNAME, PASSWORD)
    }
    server
}

def failIfGitTagExists(version) {
    if (getBBServer().isTag('ZPA-DATAPATH', 'datapath', version)) {
        error "Git tag ${version} already exists"
    }
}

def addGitTag() {
    gitCheckout(false)
    def version = readFile('build/version.txt').trim()
    def server = getBBServer()
    if (server.isTag('ZPA-DATAPATH', 'datapath', version)) {
        error "Git tag ${version} already exists"
	}
    server.createTag('ZPA-DATAPATH', 'datapath', version, 'ANNOTATED', "refs/heads/${env.GIT_BRANCH}", version)
    sh "shopt -s dotglob && rm -rf *"

    env.GIT_COMMIT = '' // Force GIT_DESCRIBE re-eval.
}

def addJiraRelease(version) {
	JiraServer server
    withCredentials([string(credentialsId: auth.getJiraCreds('jira.corp.zscaler.com'), variable: 'TOKEN')]) {
        server = new JiraServer('jira.corp.zscaler.com', TOKEN)
    }
	String releaseName = "itasca-${version}"

	if (server.isVersion('ET', releaseName)) {
	    error "Jira release ${releaseName} already exists"
	}
    server.createVersion('ET', releaseName, true, '')
}

def downloadFuzzCorpus(efsInputPath, inputDir, fuzzTarget) {
    echo "Downloading and preparing fuzz corpus..."

    def corpusZipFile = "${efsInputPath}${fuzzTarget}_corpus.zip"

    sh """
        if [ -e "$corpusZipFile" ]; then
            echo "Found fuzz zip corpus: $corpusZipFile"

            cp "$corpusZipFile" /tmp/    # Temporarily copy the ZIP file to a safe location (/tmp/)

            # Unzip the contents into the inputDir
            unzip -o -j /tmp/\$(basename "$corpusZipFile") -d ${inputDir} || {
                echo "Failed to unzip fuzz corpus. Ensure ZIP file integrity."
                exit 1
            }

            # Clean up temporary ZIP file
            rm -f /tmp/\$(basename "$corpusZipFile")
        else
            echo "No valid zip files found in '${efsInputPath}'."
            exit 0
        fi
    """

    echo "Fuzz corpus successfully downloaded and prepared at ${inputDir}."
}

def zipAndUpload(sourceDir, efsPath, fuzzTarget) {
    echo "Zipping and uploading fuzz corpus ..."

    def zipPath = "/tmpfs/out/${fuzzTarget}_corpus.zip"

    sh """
        if [ -d '${sourceDir}' ] && [ "\$(ls -A '${sourceDir}')" ]; then
            mkdir -p ${efsPath}
            zip -r ${zipPath} '${sourceDir}'
            mv ${zipPath} '${efsPath}'
        fi
    """
    echo "Fuzz corpus files zipped and uploaded successfully."
}
