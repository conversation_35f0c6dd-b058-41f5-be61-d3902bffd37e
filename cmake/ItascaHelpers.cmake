# Run argo_parse on files.
# Assumed to be run once per folder.
function(argo_parse_files)
    cmake_parse_arguments(PARSE_ARGV 0 F "DO_NOT_EXPORT" "OUTPUT_FILES_VAR;OUTPUT_DIR" "INPUT_FILES")
    if(F_MISSING_KEYWORD_VALUES OR F_UNPARSED_ARGUMENTS)
        message(FATAL_ERROR "Bad arguments for argo_parse_files")
    endif()

    foreach(input_file ${F_INPUT_FILES})
        string(REPLACE .h _compiled.h output_file ${input_file})
        string(REPLACE .cpp _compiled_cpp.h output_file ${output_file})
        string(REPLACE .c _compiled_c.h output_file ${output_file})
        set(output_file ${F_OUTPUT_DIR}/${output_file})
        add_custom_command(
            OUTPUT ${output_file}
            COMMAND argo_parse ${input_file} ${output_file}
            WORKING_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}
            DEPENDS ${input_file} argo_parse
        )
        list(APPEND output_files ${output_file})

        if(NOT F_DO_NOT_EXPORT)
            set_property(TARGET gen-headers APPEND PROPERTY output ${output_file})
        endif()
    endforeach()

    cmake_path(GET CMAKE_CURRENT_SOURCE_DIR FILENAME folder)
    set(tgt "gen-headers-${folder}")
    add_custom_target(${tgt} DEPENDS ${output_files})
    add_dependencies(gen-headers ${tgt})

    set(${F_OUTPUT_FILES_VAR} ${${F_OUTPUT_FILES_VAR}} ${output_files} PARENT_SCOPE)
endfunction()

# Add exe per source and link deps.
function(add_simple_apps)
    cmake_parse_arguments(PARSE_ARGV 0 F "" "" "SOURCES;DEPS")
    if(F_MISSING_KEYWORD_VALUES OR F_UNPARSED_ARGUMENTS)
        message(FATAL_ERROR "Bad arguments for add_simple_exe")
    endif()

    foreach(source ${F_SOURCES})
        get_filename_component(name ${source} NAME_WE)
        add_executable(${name} ${source})
        target_link_libraries(${name} PRIVATE ${F_DEPS})
    endforeach()
endfunction()

# Add test for each executable.
function(add_simple_tests)
    foreach(exe ${ARGV})
        add_test(NAME ${exe} COMMAND ${exe})
    endforeach()
endfunction()

# Dump contents of files using xxd.
function(xxd_files)
    cmake_parse_arguments(PARSE_ARGV 0 F "RAW" "OUTPUT_DIR;OUTPUT_FILES_VAR" "FILES")
    if(F_MISSING_KEYWORD_VALUES OR F_UNPARSED_ARGUMENTS)
        message(FATAL_ERROR "Bad arguments for xxd_files")
    endif()

    find_program(xxd xxd REQUIRED)
    find_program(cat cat REQUIRED)

    foreach(xxd_file ${F_FILES})
        get_filename_component(dir ${xxd_file} ABSOLUTE)
        get_filename_component(dir ${dir} DIRECTORY)
        get_filename_component(file ${xxd_file} NAME)
        if(F_RAW)
            set(output_file ${F_OUTPUT_DIR}/${file}_generated_raw.h)
            add_custom_command(
                OUTPUT ${output_file}
                COMMAND ${cat} ${file} | ${xxd} -i > ${output_file}
                WORKING_DIRECTORY ${dir}
            )
        else()
            set(output_file ${F_OUTPUT_DIR}/${file}_generated.h)
            add_custom_command(OUTPUT ${output_file} COMMAND ${xxd} -i ${file} ${output_file} WORKING_DIRECTORY ${dir})
        endif()
        list(APPEND output_files ${output_file})
    endforeach()

    set(${F_OUTPUT_FILES_VAR} ${${F_OUTPUT_FILES_VAR}} ${output_files} PARENT_SCOPE)
endfunction()

# Add test with generated name based on args.
function(add_test_with_args)
    cmake_parse_arguments(PARSE_ARGV 0 F "" "BINARY" "ARGS")
    if(F_MISSING_KEYWORD_VALUES OR F_UNPARSED_ARGUMENTS)
        message(FATAL_ERROR "Bad arguments for add_test_with_args")
    endif()

    string(REPLACE ";" "-" args_name "${F_ARGS}")
    string(REPLACE " " "+" args_name ${args_name})
    add_test(NAME ${F_BINARY}-${args_name} COMMAND ${F_BINARY} ${F_ARGS} WORKING_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR})
endfunction()

# Sets up everything needed for a testing lib to allow importing testing utilities
# Exports a copy of the passed in library as ${LIBRARY}_testing
# Library is compiled with 2 macros
#    - ${LIBRARY}_TESTING (all caps)
#    - TESTING_LIBRARY
#    These macros can be used to enable testing harnesses that won't be enabled in production
function(create_testing_library)
    cmake_parse_arguments(PARSE_ARGV 0 F "" "LIBRARY" "")

    # initialize our testing lib meta deta
    set(test_lib "${F_LIBRARY}_testing") #testing libraries are always named _testing
    string(TOUPPER ${test_lib} test_macro)

    # Create the library using the same sources as the parent library
    add_library(${test_lib} STATIC $<TARGET_PROPERTY:${F_LIBRARY},SOURCES>)

    # Copy the existing parent libraries attributes to make a direct clone
    target_link_libraries(${test_lib} PUBLIC $<TARGET_PROPERTY:${F_LIBRARY},LINK_LIBRARIES>)

    # Add the magic sauce to make this a testing library
    target_compile_definitions(${test_lib} PRIVATE ${test_macro})
    target_compile_definitions(${test_lib} PRIVATE TESTING_LIBRARY)
endfunction()

function(add_go_app)
    cmake_parse_arguments(PARSE_ARGV 0 F "" "NAME" "SRC")
    if(F_MISSING_KEYWORD_VALUES OR F_UNPARSED_ARGUMENTS)
        message(FATAL_ERROR "Bad arguments for add_go_app")
    endif()

    set(output ${CMAKE_CURRENT_BINARY_DIR}/${F_NAME})
    execute_process(
        COMMAND bash -c "${CMAKE_SOURCE_DIR}/build/get_version.sh"
        OUTPUT_VARIABLE VERSION
        OUTPUT_STRIP_TRAILING_WHITESPACE
        COMMAND_ERROR_IS_FATAL ANY
    )

    add_custom_command(
        OUTPUT ${output}
        DEPENDS ${F_SRC}
        COMMAND go build -ldflags "-X main.zpath_version=${VERSION}" -o ${output} ${F_SRC}
        WORKING_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}
    )

    add_executable(go-exe-${F_NAME} IMPORTED GLOBAL)
    set_target_properties(go-exe-${F_NAME} PROPERTIES IMPORTED_LOCATION ${output})
    add_custom_target("go-${F_NAME}" DEPENDS ${output})
    add_dependencies(go-exe-${F_NAME} go-${F_NAME})
endfunction()

function(get_platform)
    cmake_parse_arguments(PARSE_ARGV 0 F "" "OUTPUT_VAR;" "")
    if(F_MISSING_KEYWORD_VALUES OR F_UNPARSED_ARGUMENTS)
        message(FATAL_ERROR "Bad arguments for get_platform")
    endif()

    if(${CMAKE_SYSTEM_NAME} STREQUAL "Linux")
        file(READ /etc/os-release content)
        foreach(digit 9 8 7)
            if("${content}" MATCHES "VERSION_ID=\"${digit}")
                set(${F_OUTPUT_VAR} "el${digit}" PARENT_SCOPE)
                message(STATUS "Detected platform: el${digit}")
                return()
            endif()
        endforeach()
        message(FATAL_ERROR "Can't determine linux platform")
    else()
        message(STATUS "Detected platform: osx")
        set(${F_OUTPUT_VAR} "osx" PARENT_SCOPE)
    endif()
endfunction()

function(get_package_version)
    cmake_parse_arguments(PARSE_ARGV 0 F "" "OUTPUT_VAR;" "")
    if(F_MISSING_KEYWORD_VALUES OR F_UNPARSED_ARGUMENTS)
        message(FATAL_ERROR "Bad arguments for get_package_version")
    endif()

    execute_process(
        COMMAND bash -c "VERSION_SUFFIX=${VERSION_SUFFIX} ${CMAKE_SOURCE_DIR}/build/get_version.sh"
        OUTPUT_VARIABLE version
        OUTPUT_STRIP_TRAILING_WHITESPACE
        COMMAND_ERROR_IS_FATAL ANY
    )
    string(REPLACE "-" "_" version ${version})

    set(${F_OUTPUT_VAR} ${version} PARENT_SCOPE)
    message(STATUS "Detected package version: ${version}")
endfunction()

macro(init_packaging)
    get_platform(OUTPUT_VAR PKG_PLATFORM)
    get_package_version(OUTPUT_VAR PKG_VERSION) # Reconfigure if actual package version is needed.
    set(PKG_ARCH ${CMAKE_SYSTEM_PROCESSOR})
    if($ENV{CMAKE_BUILD_PARALLEL_LEVEL})
        set(PKG_NPROC $ENV{CMAKE_BUILD_PARALLEL_LEVEL})
    else()
        include(ProcessorCount)
        ProcessorCount(PKG_NPROC)
    endif()
    message(STATUS "Detected nproc: ${PKG_NPROC}")

    add_custom_target(package)
    add_custom_target(gzip)
    add_custom_target(tar)

    if(${CMAKE_SYSTEM_NAME} STREQUAL "Linux")
        add_custom_target(rpm)
        add_dependencies(package rpm)
        init_rpm_signing()
    endif()
    if($ENV{JENKINS_BUILD})
        add_custom_target(bin)
        add_custom_target(nostrip)
        add_dependencies(package bin nostrip)
        init_bin_signing()
    endif()
    find_program(shiv shiv)
    if(shiv)
        add_custom_target(shiv)
        add_dependencies(package shiv)
    endif()

    add_dependencies(package gzip tar)
endmacro()

macro(init_argo_parse)
    add_custom_target(gen-headers)
endmacro()

function(resolve_target F_FILE F_OUTPUT)
    if(TARGET ${F_FILE})
        set(${F_OUTPUT} $<TARGET_FILE:${F_FILE}> PARENT_SCOPE)
    else()
        set(${F_OUTPUT} ${F_FILE} PARENT_SCOPE)
    endif()
endfunction()

function(resolve_targets F_FILES F_OUTPUT)
    set(files)
    foreach(file ${F_FILES})
        resolve_target(${file} resolved)
        list(APPEND files ${resolved})
    endforeach()
    set(${F_OUTPUT} ${files} PARENT_SCOPE)
endfunction()

function(add_rpm)
    cmake_parse_arguments(PARSE_ARGV 0 F "" "NAME;MAKEFILE;SPEC;COMMON_FILES" "FILES")
    if(F_MISSING_KEYWORD_VALUES OR F_UNPARSED_ARGUMENTS)
        message(FATAL_ERROR "Bad arguments for add_rpm")
    endif()
    if(NOT CMAKE_SYSTEM_NAME STREQUAL "Linux")
        return()
    endif()

    if(F_COMMON_FILES AND EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/${F_COMMON_FILES})
        file(STRINGS ${CMAKE_CURRENT_SOURCE_DIR}/${F_COMMON_FILES} common_files_list)
        list(APPEND F_FILES ${common_files_list})
    endif()

    find_program(rpmbuild rpmbuild REQUIRED)
    find_program(tar tar REQUIRED)

    set(rpm_root "${CMAKE_CURRENT_BINARY_DIR}/rpmbuild-${F_NAME}")
    set(tar_name "${F_NAME}-${PKG_VERSION}")
    set(tar_src "${CMAKE_CURRENT_BINARY_DIR}/${tar_name}")
    set(tar_file "${rpm_root}/SOURCES/${tar_name}.tar")
    set(rpm_name "${tar_name}-1.${PKG_PLATFORM}.${PKG_ARCH}.rpm")
    resolve_targets("${F_FILES}" files)

    add_custom_command(
        OUTPUT ${rpm_name}
        DEPENDS ${F_FILES} ${F_MAKEFILE} ${F_SPEC}
        COMMAND mkdir -p "${rpm_root}/{SOURCES,SPECS,SRPMS,RPMS,BUILD,BUILDROOT}"
        COMMAND mkdir "${tar_src}"
        COMMAND cp ${files} ${tar_src}
        COMMAND cp ${F_MAKEFILE} ${tar_src}/Makefile
        COMMAND ${tar} cf ${tar_file} -C ${tar_src}/.. ${tar_name}
        COMMAND rm -rf "${tar_src}"
        COMMAND
            ${rpmbuild} -bb ${F_SPEC} --define "_topdir ${rpm_root}" --define "_cmake_rpm_path ${CMAKE_SOURCE_DIR}"
            --define "_version ${PKG_VERSION}" --define "_sanitized $<BOOL:${SANITIZED_INTERFACE}>" --define
            "_binary_payload w2T${PKG_NPROC}.xzdio" --clean --quiet
        COMMAND rm -rf ${tar_file}
        COMMAND mv "${rpm_root}/RPMS/${PKG_ARCH}/${rpm_name}" ${CMAKE_CURRENT_BINARY_DIR}
        COMMAND rm -rf ${rpm_root}
        VERBATIM
        WORKING_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}
    )

    find_program(rpmsign rpmsign)
    if(rpmsign)
        find_program(rpm rpm REQUIRED)
        add_custom_command(
            OUTPUT ${rpm_name}
            DEPENDS sign-rpm-key
            COMMAND
                GPG_TTY= ${rpm} --addsign --define "_signature gpg" --define "_gpg_path ${SIGN_RPM_KEYRING}" --define
                "_gpg_name <EMAIL>" ${CMAKE_CURRENT_BINARY_DIR}/${rpm_name} > /dev/null
            APPEND
        )
    endif()

    add_custom_target("rpm-${F_NAME}" DEPENDS ${rpm_name})
    add_dependencies(rpm "rpm-${F_NAME}")
endfunction()

function(add_gzip)
    cmake_parse_arguments(PARSE_ARGV 0 F "" "NAME;FILE;OUTPUT_VAR" "")
    if(F_MISSING_KEYWORD_VALUES OR F_UNPARSED_ARGUMENTS)
        message(FATAL_ERROR "Bad arguments for add_gzip")
    endif()

    find_program(gzip gzip REQUIRED)

    set(output "${CMAKE_CURRENT_BINARY_DIR}/${F_NAME}-${PKG_VERSION}.${PKG_ARCH}.gz") # Add platform?
    resolve_target(${F_FILE} file)

    add_custom_command(
        OUTPUT ${output}
        DEPENDS ${F_FILE}
        COMMAND ${gzip} -c ${file} > ${output}
        WORKING_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}
        VERBATIM
    )

    if(DEFINED F_OUTPUT_VAR)
        set(${F_OUTPUT_VAR} ${output} PARENT_SCOPE)
    endif()

    add_custom_target("gzip-${F_NAME}" DEPENDS ${output})
    add_dependencies(gzip "gzip-${F_NAME}")
endfunction()

function(add_tar)
    cmake_parse_arguments(PARSE_ARGV 0 F "GZ" "NAME;OUTPUT_VAR;DEPS" "FILES")
    if(F_MISSING_KEYWORD_VALUES OR F_UNPARSED_ARGUMENTS)
        message(FATAL_ERROR "Bad arguments for add_tar")
    endif()

    find_program(tar tar REQUIRED)

    set(tar_base "${F_NAME}-${PKG_VERSION}")
    set(tar_src "${CMAKE_CURRENT_BINARY_DIR}/${tar_base}")
    set(output "${CMAKE_CURRENT_BINARY_DIR}/${tar_base}.${PKG_ARCH}.tar") # Add platform?
    set(flags cf)
    if(F_GZ)
        string(APPEND output ".gz")
        set(flags czf)
    endif()
    resolve_targets("${F_FILES}" files)

    add_custom_command(
        OUTPUT ${output}
        DEPENDS ${F_FILES} ${F_DEPS}
        COMMAND mkdir ${tar_src}
        COMMAND cp ${files} ${tar_src}
        COMMAND ${tar} ${flags} ${output} -C "${tar_src}/.." ${tar_base}
        COMMAND rm -rf ${tar_src}
        WORKING_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}
        VERBATIM
    )

    if(DEFINED F_OUTPUT_VAR)
        set(${F_OUTPUT_VAR} ${output} PARENT_SCOPE)
    endif()

    add_custom_target("tar-${F_NAME}" DEPENDS ${output})
    add_dependencies(tar "tar-${F_NAME}")
endfunction()

function(fetch_s3)
    cmake_parse_arguments(PARSE_ARGV 0 F "" "OUTPUT;PATH" "")
    if(F_MISSING_KEYWORD_VALUES OR F_UNPARSED_ARGUMENTS)
        message(FATAL_ERROR "Bad arguments for fetch_s3")
    endif()

    if($ENV{JENKINS_BUILD})
        string(PREPEND F_PATH "https://s3-us-west-2.amazonaws.com")
    else()
        string(PREPEND F_PATH "https://s3.zpa.tools")
    endif()

    find_program(curl curl REQUIRED)
    add_custom_command(OUTPUT ${F_OUTPUT} COMMAND ${curl} -sSfo ${F_OUTPUT} ${F_PATH} VERBATIM)
endfunction()

function(init_coverage)
    if(NOT ${CMAKE_C_FLAGS} MATCHES "--coverage")
        return()
    endif()
    find_program(grcov grcov REQUIRED)

    add_custom_command(
        OUTPUT cobertura.xml
        COMMAND ${grcov} -s ${CMAKE_SOURCE_DIR}/src/ --ignore '/*' -t cobertura -o . src/
    )
    add_custom_target(cov-cobertura DEPENDS cobertura.xml)

    add_custom_command(
        OUTPUT html
        COMMAND ${grcov} -s ${CMAKE_SOURCE_DIR}/src/ --branch --ignore '/*' -t html -o . src/
    )
    add_custom_target(cov-html DEPENDS html)
    set_target_properties(cov-html PROPERTIES ADDITIONAL_CLEAN_FILES html)

    add_custom_target(cov DEPENDS cov-html cov-cobertura)
endfunction()

function(init_bin_signing)
    if(NOT "$ENV{JENKINS_BUILD}")
        return()
    endif()

    find_program(aws aws REQUIRED)

    if($ENV{SIGNED})
        set(env prod)
    else()
        set(env dev)
    endif()

    set(encoded sign_bin.encoded)
    set(output sign_bin.key)

    add_custom_command(
        OUTPUT ${output}
        COMMAND
            ${aws} kms decrypt --ciphertext-blob fileb://${CMAKE_SOURCE_DIR}/pki/${env}_signing.key.kmsenc --output text
            --query Plaintext > ${encoded}
        COMMAND base64 -d ${encoded} > ${output}
        COMMAND rm -rf ${encoded}
        DEPENDS ${CMAKE_SOURCE_DIR}/pki/${env}_signing.key.kmsenc
        VERBATIM
    )
    add_custom_target(sign-bin-key DEPENDS ${output})

    set(SIGN_BIN_KEY "${CMAKE_CURRENT_BINARY_DIR}/${output}" PARENT_SCOPE)
    set(SIGN_BIN_CERT "${CMAKE_SOURCE_DIR}/pki/${env}_signing.crt" PARENT_SCOPE)
    set(SIGN_BIN_ROOT "${CMAKE_SOURCE_DIR}/pki/${env}_signing_root.crt" PARENT_SCOPE)

    message(STATUS "Bins are going to be signed with a ${env} key")
endfunction()

function(init_rpm_signing)
    find_program(rpmsign rpmsign)

    set(env)
    set(key sign_rpm.key)
    if($ENV{SIGNED})
        find_program(aws aws REQUIRED)
        find_program(jq jq REQUIRED)
        set(key_json sign_rpm.json)
        add_custom_command(
            OUTPUT ${key}
            COMMAND
                ${aws} secretsmanager get-secret-value --secret-id zpa/signing/gpg/release --query SecretString --output
                text > ${key_json}
            COMMAND ${jq} -r .["private_key"] ${key_json} | base64 -d > ${key}
            COMMAND rm -rf ${key_json}
            VERBATIM
        )
        set(env prod)
    else()
        find_program(curl curl REQUIRED)
        add_custom_command(
            OUTPUT ${key}
            COMMAND
                ${curl} -sSfo ${key}
                https://nexus.et.zscaler.com/repository/zpa-release/gpg/zpa/development/ET-RPM-GPG-PRIVATE-KEY-DEV
            VERBATIM
        )
        set(env dev)
    endif()

    set(keyring ${CMAKE_CURRENT_BINARY_DIR}/rpm-keyring)
    add_custom_command(
        OUTPUT ${keyring}
        DEPENDS ${key}
        COMMAND mkdir -m 0700 ${keyring}
        COMMAND gpg --quiet --homedir=${keyring} --import ${key}
        VERBATIM
    )
    add_custom_target(sign-rpm-key DEPENDS ${keyring})
    set_target_properties(sign-rpm-key PROPERTIES ADDITIONAL_CLEAN_FILES ${keyring})

    set(SIGN_RPM_KEYRING ${keyring} PARENT_SCOPE)

    message(STATUS "RPMs are going to be signed with a ${env} key")
endfunction()

function(add_nostrip)
    cmake_parse_arguments(PARSE_ARGV 0 F "" "TARGET" "")

    if(F_MISSING_KEYWORD_VALUES OR F_UNPARSED_ARGUMENTS)
        message(FATAL_ERROR "Bad arguments for add_nostrip")
    endif()

    if(NOT F_TARGET)
        message(FATAL_ERROR "Missing required argument: TARGET")
        return()
    endif()

    if(NOT TARGET nostrip)
        return()
    endif()

    string(REPLACE "_" "-" zpath_version ${PKG_VERSION})
    set(nostrip "${CMAKE_CURRENT_BINARY_DIR}/${F_TARGET}.${PKG_PLATFORM}.${PKG_ARCH}.${zpath_version}.bin.nostrip")

    add_custom_command(OUTPUT ${nostrip} DEPENDS ${F_TARGET} COMMAND cp ${F_TARGET} ${nostrip} VERBATIM)

    add_custom_target("nostrip-${F_TARGET}" DEPENDS ${nostrip})
    add_dependencies(nostrip "nostrip-${F_TARGET}")
endfunction()

function(add_bin)
    cmake_parse_arguments(PARSE_ARGV 0 F "DEFAULT_BUILD_ONLY" "TARGET;DROPDB" "")
    if(F_MISSING_KEYWORD_VALUES OR F_UNPARSED_ARGUMENTS)
        message(FATAL_ERROR "Bad arguments for add_bin")
    endif()

    if((NOT TARGET bin) OR (F_DEFAULT_BUILD_ONLY AND VERSION_SUFFIX))
        return()
    endif()

    string(REPLACE "_" "-" zpath_version ${PKG_VERSION})
    set(base "${F_TARGET}.${PKG_PLATFORM}.${PKG_ARCH}.${zpath_version}")
    set(meta "${CMAKE_CURRENT_BINARY_DIR}/${base}.meta")
    set(bin "${CMAKE_CURRENT_BINARY_DIR}/${base}.bin")
    set(nostrip "${bin}.nostrip")

    add_custom_command(
        OUTPUT ${bin} ${meta} ${nostrip}
        DEPENDS ${F_TARGET} sign-bin-key
        COMMAND cp ${F_TARGET} ${nostrip}
        COMMAND cp ${F_TARGET} ${bin}
        COMMAND strip --strip-all ${bin}
        COMMAND
            zcrypt_sign -sign -metadata ${meta} -key ${SIGN_BIN_KEY} -cert ${SIGN_BIN_CERT} -root ${SIGN_BIN_ROOT} -role
            ${F_TARGET} -platform ${PKG_PLATFORM} -arch ${PKG_ARCH} -version ${zpath_version} ${bin} > /dev/null 2>&1
        VERBATIM
    )

    if(F_DROPDB)
        message("dropdb is set")
        string(REPLACE "_" "-" zpath_version ${PKG_VERSION})
        set(dropdb_base "${F_TARGET}.${PKG_PLATFORM}.${PKG_ARCH}.${zpath_version}.dropdb")
        set(dropdb_bin "${CMAKE_CURRENT_BINARY_DIR}/${dropdb_base}.dropdbin")
        set(dropdb_meta "${CMAKE_CURRENT_BINARY_DIR}/${dropdb_base}.meta")
        add_custom_command(
            OUTPUT ${dropdb_meta}
            DEPENDS ${F_TARGET} sign-bin-key
            COMMAND cp ${F_TARGET} ${dropdb_bin}
            COMMAND strip --strip-all ${dropdb_bin}
            COMMAND
                zcrypt_sign -sign -metadata ${dropdb_meta} -key ${SIGN_BIN_KEY} -cert ${SIGN_BIN_CERT} -root
                ${SIGN_BIN_ROOT} -role ${F_TARGET} -platform ${PKG_PLATFORM} -arch ${PKG_ARCH} -dropdb 1 -version
                ${zpath_version} ${dropdb_bin} > /dev/null 2>&1
            VERBATIM
        )
        add_custom_target("bin-${F_TARGET}" DEPENDS ${bin} ${meta} ${nostrip} ${dropdb_meta})
    else()
        add_custom_target("bin-${F_TARGET}" DEPENDS ${bin} ${meta} ${nostrip})
    endif()

    add_dependencies(bin "bin-${F_TARGET}")
endfunction()

#TODO: depend on python source.
function(add_shiv)
    cmake_parse_arguments(PARSE_ARGV 0 F "" "NAME;REQS" "")
    if(F_MISSING_KEYWORD_VALUES OR F_UNPARSED_ARGUMENTS)
        message(FATAL_ERROR "Bad arguments for add_shiv")
    endif()

    if(NOT TARGET shiv)
        return()
    endif()

    find_program(shiv shiv REQUIRED)
    set(output "${CMAKE_CURRENT_BINARY_DIR}/${F_NAME}-${PKG_VERSION}.${PKG_PLATFORM}.${PKG_ARCH}.pyz")

    add_custom_command(
        OUTPUT ${output}
        DEPENDS ${F_REQS}
        COMMAND ${shiv} -o ${output} -e cli:main . -r ${F_REQS} --quiet
        COMMAND rm -rf build && find . -name *.egg-info -type d -exec rm -rf {} +
        WORKING_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}
        VERBATIM
    )

    add_custom_target("shiv-${F_NAME}" DEPENDS ${output})
    add_dependencies(shiv "shiv-${F_NAME}")
endfunction()

function(strip_and_rename)
    cmake_parse_arguments(PARSE_ARGV 0 F "" "TARGET;NAME;OUTPUT_VAR" "")
    if(F_MISSING_KEYWORD_VALUES OR F_UNPARSED_ARGUMENTS)
        message(FATAL_ERROR "Bad arguments for strip_and_rename")
    endif()

    find_program(strip strip REQUIRED)
    set(output "${CMAKE_CURRENT_BINARY_DIR}/${F_NAME}")

    add_custom_command(
        OUTPUT ${output}
        DEPENDS ${F_TARGET}
        COMMAND cp ${F_TARGET} ${output}
        COMMAND ${strip} --strip-all ${output}
        VERBATIM
    )

    set(${F_OUTPUT_VAR} ${output} PARENT_SCOPE)
endfunction()
