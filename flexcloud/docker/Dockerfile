FROM 348440474813.dkr.ecr.us-west-2.amazonaws.com/etjc:el9-fips-java11-1.55.0-rc

RUN yum -y install https://download.postgresql.org/pub/repos/yum/reporpms/EL-9-x86_64/pgdg-redhat-repo-latest.noarch.rpm \
    && yum install -y postgresql15 \
    openssh-server \
    openssh-clients \
    sudo

COPY zpath /zpath/

RUN useradd eng
RUN echo 'eng ALL=(ALL) NOPASSWD: ALL' >> /etc/sudoers
RUN echo 'zscaler ALL=(ALL) NOPASSWD: ALL' >> /etc/sudoers

# wally will run as zscaler
# sshd will run as eng
RUN ssh-keygen -A \
    && mkdir -p /home/<USER>/.ssh \
    && ssh-keygen -f /home/<USER>/.ssh/id_rsa -N '' \
    && cat /home/<USER>/.ssh/id_rsa.pub >> /home/<USER>/.ssh/authorized_keys \
    && cp /home/<USER>/.ssh/id_rsa.pub /id_rsa.pub \
    && cp /home/<USER>/.ssh/id_rsa /id_rsa \
    && chown zscaler /id_rsa

RUN chown -R zscaler /opt/zscaler \
    && mkdir -p /zpath/log/ \
    && chown -R zscaler /zpath/log \
    && mkdir -p /itasca/packages && chown -R zscaler /itasca \
    && chown -R eng /etc/ssh \
    && mkdir -p /home/<USER>/db_schema_upgrade_automation \
    && chown -R eng /home/<USER>/home/<USER>

RUN echo "source /common_env.sh" >> /etc/bashrc

USER zscaler

COPY packages/bin/agrep /zpath/bin/agrep

CMD bash run.sh
