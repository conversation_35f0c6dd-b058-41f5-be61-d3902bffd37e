/*
 * npwg_exec.c. Copyright (C) 2024 Zscaler Inc. All Rights Reserved.
 */

#include "npwg_lib/npwg_exec.h"
#include "npwg_lib/npwg_provider.h"

#include <errno.h>
#include <fcntl.h>
#include <stdarg.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <sys/wait.h>
#include <unistd.h>
#include <sys/types.h>
#include <event2/event.h>
#include <libgen.h>
#include <sys/stat.h>

static inline void close_fds(int count, ...)
{
    va_list args;
    va_start(args, count);
    for (int ii = 0; ii < count; ++ii) {
        int fd = va_arg(args, int);
        if (fd) {
            // not looking for errors from these syscalls
            fsync(fd);
            close(fd);
        }
    }
    va_end(args);
}

static void cleanup_npwg_exec_on_error(int input_fd,
                                       int output_fd,
                                       int error_fd,
                                       char *out_buf,
                                       int *out_buf_len,
                                       char *err_buf,
                                       int *err_buf_len)
{
    close_fds(3, input_fd, output_fd, error_fd);
    if (out_buf && out_buf_len) {
        *out_buf = '\0';
        *out_buf_len = 0;
    }
    if (err_buf && err_buf_len) {
        *err_buf = '\0';
        *err_buf_len = 0;
    }
}

int npwg_exec(const char *executable,
              char *const argv[],
              const char *in_buf,
              int in_buf_len,
              char *out_buf,
              int *out_buf_len,
              char *err_buf,
              int *err_buf_len,
              int max_wait_time)
{
    if (!executable) {
        NPWG_LOG(AL_ERROR, "Null executable passed to npwg_exec_with_input");
        return NPWG_RESULT_BAD_ARGUMENT;
    }

    char errno_str[128];
    int pipefd_in[2] = {0};
    if (-1 == pipe(pipefd_in)) {
        strerror_r(errno, errno_str, sizeof(errno_str));
        NPWG_LOG(AL_ERROR, "pipe() failed. Error: %s", errno_str);
        return NPWG_RESULT_SYSCALL_FAILED;
    }

    int pipefd_out[2] = {0};
    if (-1 == pipe(pipefd_out)) {
        strerror_r(errno, errno_str, sizeof(errno_str));
        close_fds(2, pipefd_in[0], pipefd_in[1]);
        NPWG_LOG(AL_ERROR, "pipe() failed. Error: %s", errno_str);
        return NPWG_RESULT_SYSCALL_FAILED;
    }

    int pipefd_err[2] = {0};
    if (-1 == pipe(pipefd_err)) {
        strerror_r(errno, errno_str, sizeof(errno_str));
        close_fds(4, pipefd_in[0], pipefd_in[1], pipefd_out[0], pipefd_out[1]);
        NPWG_LOG(AL_ERROR, "pipe() failed. Error: %s", errno_str);
        return NPWG_RESULT_SYSCALL_FAILED;
    }

    pid_t child_pid = fork();
    if (-1 == child_pid) {
        strerror_r(errno, errno_str, sizeof(errno_str));
        close_fds(6, pipefd_in[0], pipefd_in[1],
                  pipefd_out[0], pipefd_out[1],
                  pipefd_err[0], pipefd_err[1]);
        NPWG_LOG(AL_ERROR, "fork() failed. Error: %s", errno_str);
        return NPWG_RESULT_SYSCALL_FAILED;
    }

    if (0 == child_pid) {
        close_fds(3, pipefd_in[1], pipefd_out[0], pipefd_err[0]);

        if (-1 == dup2(pipefd_in[0], STDIN_FILENO)) {
            close_fds(3, pipefd_in[0], pipefd_out[1],
                      pipefd_err[1]);
            exit(EXIT_FAILURE);
        }

        if (-1 == dup2(pipefd_out[1], STDOUT_FILENO)) {
            close_fds(3, STDIN_FILENO, pipefd_out[1], pipefd_err[1]);
            exit(EXIT_FAILURE);
        }

        if (-1 == dup2(pipefd_err[1], STDERR_FILENO)) {
            close_fds(3, STDIN_FILENO, STDOUT_FILENO, pipefd_err[1]);
            exit(EXIT_FAILURE);
        }

        alarm(max_wait_time);
        execv(executable, argv);
    }
    else {
        close_fds(3, pipefd_in[0], pipefd_out[1], pipefd_err[1]);

        if (in_buf && in_buf_len) {
            int bytes_written = (int)write(pipefd_in[1], in_buf, in_buf_len);
            if (bytes_written != in_buf_len) {
                strerror_r(errno, errno_str, sizeof(errno_str));
                cleanup_npwg_exec_on_error(pipefd_in[1], pipefd_out[0],
                                           pipefd_err[0], out_buf, out_buf_len,
                                           err_buf, err_buf_len);
                NPWG_LOG(AL_ERROR, "write() failed. Error: %s", errno_str);
                return NPWG_RESULT_SYSCALL_FAILED;
            }
        }
        close(pipefd_in[1]);

        int status;
        waitpid(child_pid, &status, 0);
        if (!WIFEXITED(status)) {
            strerror_r(errno, errno_str, sizeof(errno_str));
            cleanup_npwg_exec_on_error(0, pipefd_out[0], pipefd_err[0],
                                       out_buf, out_buf_len, err_buf, err_buf_len);
            NPWG_LOG(AL_ERROR, "Child took too long. Error: %s", errno_str);
            return NPWG_RESULT_CHILD_HUNG;
        }

        if (out_buf && out_buf_len) {
            int bytes_read = (int)read(pipefd_out[0], out_buf, *out_buf_len);
            if (-1 == bytes_read) {
                strerror_r(errno, errno_str, sizeof(errno_str));
                cleanup_npwg_exec_on_error(0, pipefd_out[0], pipefd_err[0],
                                           out_buf, out_buf_len, err_buf, err_buf_len);
                NPWG_LOG(AL_ERROR, "Could not read from stdout of child. Error: %s", errno_str);
                return NPWG_RESULT_SYSCALL_FAILED;
            }
            *out_buf_len = bytes_read;
        }
        close(pipefd_out[0]);

        if (err_buf && err_buf_len) {
            int bytes_read = (int)read(pipefd_err[0], err_buf, *err_buf_len);
            if (-1 == bytes_read) {
                strerror_r(errno, errno_str, sizeof(errno_str));
                cleanup_npwg_exec_on_error(0, 0, pipefd_err[0],
                                           out_buf, out_buf_len, err_buf, err_buf_len);
                NPWG_LOG(AL_ERROR, "Could not read from stderr of child. Error: %s", errno_str);
                return NPWG_RESULT_SYSCALL_FAILED;
            }
            *err_buf_len = bytes_read;
        }
        close(pipefd_err[0]);
    }

    return NPWG_RESULT_NO_ERROR;
}

/**
 * Command execution context for libevent
 *
 * The execution_context_t structure is used to:
 *   Track command-related data (pipe, pipe_fd, out_buf).
 *   Store pointers to buffers that hold command output (stdout).
 *   Manage the Libevent workflow with two types of events:
 *         pipe_event: Monitors when data is available to be read from the pipe.
 *         timeout_event: Fires when the command exceeds the timeout limit.
 */
typedef struct execution_context {
    struct event *pipe_event;        // Event triggered for reading from the pipe
    struct event *timeout_event;     // Event for timeout handling
    struct event_base *base;         // Libevent base
    FILE *pipe;                      // Pointer to the pipe opened by popen()
    int pipe_fd;                     // File descriptor corresponding to popen()
    char *out_buf;                   // Buffer to store command output (stdout)
    ssize_t *out_buf_length;         // Current length of data in out_buf
    char *err_buf;                   // Buffer to store error messages
    ssize_t *err_buf_length;         // Current length of data in err_buf
    ssize_t out_buf_capacity;        // Maximum capacity of out_buf
    ssize_t err_buf_capacity;        // Maximum capacity of err_buf
    int status;                      //status tracking variable
} execution_context_t;


/*
 * Callback function to handle reading from the pipe
 */
static void pipe_read_cb(evutil_socket_t fd,
                         short events __attribute__((unused)),
                         void *arg)
{
    execution_context_t *ctx = (execution_context_t *)arg;

    ssize_t available_space = ctx->out_buf_capacity - *ctx->out_buf_length;
    if (available_space == 0) {
        NPWG_LOG(AL_ERROR, "Output buffer full. Data truncated. Draining pipe....");

        // Drain the pipe to prevent blocking
        char drain_buf[1024];  // Small temporary buffer to read leftover data
        while (read(fd, drain_buf, sizeof(drain_buf)) > 0) {
            // Discard the data
        }
        return;
    }

    ssize_t bytes_read = read(fd, ctx->out_buf + *ctx->out_buf_length, available_space);
    if (bytes_read > 0) {
        *ctx->out_buf_length += bytes_read;
    } else if (bytes_read == 0) {
        ctx->status = 0;     // Mark success
        event_base_loopexit(ctx->base, NULL);
    } else {
        char strerror_buf[128] = {0};
        strerror_r(errno, strerror_buf, sizeof(strerror_buf));
        NPWG_LOG(AL_ERROR, "Error reading from pipe: %s", strerror_buf);
        ctx->status = errno;     // Capture read error
        event_base_loopexit(ctx->base, NULL);
    }
}

/*
 * Callback function to handle timeout events
 */
static void timeout_cb(evutil_socket_t fd __attribute__((unused)),
                       short events __attribute__((unused)),
                       void *arg)
{
    execution_context_t *ctx = (execution_context_t *)arg;
    ctx->status = ETIMEDOUT; // Set status for timeout
    event_base_loopexit(ctx->base, NULL);
}

int npwg_exec_popen(const char *command,
                    char *out_buf,
                    ssize_t *out_buf_length,
                    char *err_buf,
                    ssize_t *err_buf_length,
                    int timeout_seconds)
{

    if (!command || !out_buf || !out_buf_length || !err_buf || !err_buf_length) {
        NPWG_LOG(AL_ERROR, "Null arguments passed to npwg_exec_popen");
        return NPWG_RESULT_BAD_ARGUMENT;
    }

    if (!(*out_buf_length) || !(*err_buf_length)) {
        NPWG_LOG(AL_ERROR, "output or error buffer capacity is zero.");
        return NPWG_RESULT_BAD_ARGUMENT;
    }

    char temp_file[256];       // Temporary file template for mkstemp
    snprintf(temp_file, sizeof(temp_file), "/tmp/%s_stderr_XXXXXX", __func__);

    char strerror_buf[128] = {0};
    execution_context_t ctx = {0};
    int status = NPWG_RESULT_ERR;

    // Create a temporary file for stderr
    int temp_fd = mkstemp(temp_file);
    if (temp_fd == -1) {
        strerror_r(errno, strerror_buf, sizeof(strerror_buf));
        NPWG_LOG(AL_ERROR, "Failed to create temporary file: %s", strerror_buf);
        return NPWG_RESULT_SYSCALL_FAILED;
    }

    // Redirect stderr to the temporary file
    char command_with_redirection[4096];
    snprintf(command_with_redirection, sizeof(command_with_redirection), "%s 2>%s", command, temp_file);

    // Execute the command using popen
    ctx.pipe = popen(command_with_redirection, "r");
    if (!ctx.pipe) {
        strerror_r(errno, strerror_buf, sizeof(strerror_buf));
        NPWG_LOG(AL_ERROR, "popen failed for command '%s': %s", command_with_redirection, strerror_buf);
        status = NPWG_RESULT_SYSCALL_FAILED;
        goto cleanup;
    }

    ctx.pipe_fd = fileno(ctx.pipe);
    if (ctx.pipe_fd == -1) {
        strerror_r(errno, strerror_buf, sizeof(strerror_buf));
        NPWG_LOG(AL_ERROR, "fileno failed for pipe, command '%s': %s", command_with_redirection, strerror_buf);
        status = NPWG_RESULT_SYSCALL_FAILED;
        goto cleanup;
    }

    // Get existing flags on the pipe
    int existing_flags = fcntl(ctx.pipe_fd, F_GETFL, 0);
    if (existing_flags == -1) {
        strerror_r(errno, strerror_buf, sizeof(strerror_buf));
        NPWG_LOG(AL_ERROR, "Failed to get current pipe flags: %s", strerror_buf);
        status = NPWG_RESULT_SYSCALL_FAILED;
        goto cleanup;
    }

    // Set the pipe to non-blocking mode
    if (fcntl(ctx.pipe_fd, F_SETFL, existing_flags | O_NONBLOCK) == -1) {
        strerror_r(errno, strerror_buf, sizeof(strerror_buf));
        NPWG_LOG(AL_ERROR, "Failed to set pipe to non-blocking mode: %s", strerror_buf);
        status = NPWG_RESULT_SYSCALL_FAILED;
        goto cleanup;
    }

    ctx.base = event_base_new();
    if (!ctx.base) {
        NPWG_LOG(AL_ERROR, "Failed to initialize libevent base");
        goto cleanup;
    }

    ctx.out_buf = out_buf;
    ctx.err_buf = err_buf;
    ctx.out_buf_capacity = *out_buf_length;
    ctx.err_buf_capacity = *err_buf_length;
    *out_buf_length = 0;
    *err_buf_length = 0;
    ctx.out_buf_length = out_buf_length;
    ctx.err_buf_length = err_buf_length;
    ctx.status = 0;

    // Create an event for monitoring the pipe
    ctx.pipe_event = event_new(ctx.base, ctx.pipe_fd, EV_READ | EV_PERSIST, pipe_read_cb, &ctx);
    if (!ctx.pipe_event) {
        NPWG_LOG(AL_ERROR, "Failed to create pipe event");
        goto cleanup;
    }

    if (event_add(ctx.pipe_event, NULL)) {
        strerror_r(errno, strerror_buf, sizeof(strerror_buf));
        NPWG_LOG(AL_ERROR, "Failed to add pipe event: %s", strerror_buf);
        goto cleanup;
    }

    // Create a timeout event
    struct timeval timeout = {timeout_seconds, 0};
    ctx.timeout_event = evtimer_new(ctx.base, timeout_cb, &ctx);
    if (!ctx.timeout_event) {
        NPWG_LOG(AL_ERROR, "Failed to create timeout event");
        goto cleanup;
    }

    if (evtimer_add(ctx.timeout_event, &timeout)) {
        NPWG_LOG(AL_ERROR, "Failed to add timeout event");
        goto cleanup;
    }

    int dispatch_result = event_base_dispatch(ctx.base);
    if (dispatch_result == -1) {
        NPWG_LOG(AL_ERROR, "Error in event dispatch loop.");
        goto cleanup;
    }

    // Check execution state via ctx.status
    if (ctx.status == ETIMEDOUT) {
        NPWG_LOG(AL_ERROR, "Command:'%s' execution timed out" , command_with_redirection);
        status = NPWG_RESULT_CHILD_HUNG;
        goto cleanup;
    }

    if (ctx.status != 0) {
        NPWG_LOG(AL_ERROR, "Command :'%s' execution failed with status: %s", command_with_redirection, strerror(ctx.status));
        status = NPWG_RESULT_SYSCALL_FAILED;
        goto cleanup;
    }

    // Read stderr from the temporary file
    lseek(temp_fd, 0, SEEK_SET);

    size_t available_space = ctx.err_buf_capacity - *err_buf_length;
    if (available_space == 0) {
        NPWG_LOG(AL_ERROR, "Error buffer full. Draining stderr data...");

        char drain_buf[1024];
        while (read(temp_fd, drain_buf, sizeof(drain_buf)) > 0) {
            // Discard the data
        }
    } else {
        ssize_t bytes_read = read(temp_fd, err_buf + *err_buf_length, available_space);
        if (bytes_read > 0) {
            *err_buf_length = bytes_read;
        }  else if (bytes_read == -1) {
            strerror_r(errno, strerror_buf, sizeof(strerror_buf));
            NPWG_LOG(AL_ERROR, "Error reading stderr from temporary file: %s", strerror_buf);
            status = NPWG_RESULT_SYSCALL_FAILED;
            goto cleanup;
        }
    }

    int pclose_status = pclose(ctx.pipe);
    ctx.pipe = NULL;
    if (pclose_status == -1) {
        strerror_r(errno, strerror_buf, sizeof(strerror_buf));
        NPWG_LOG(AL_ERROR, "Error closing pipe: %s", strerror_buf);
        goto cleanup;
    }

    if ((pclose_status >= 0) && (WEXITSTATUS(pclose_status) == 0)) {
        status = NPWG_RESULT_NO_ERROR;
    }

cleanup:
    if (ctx.pipe_event) event_free(ctx.pipe_event);
    if (ctx.timeout_event) event_free(ctx.timeout_event);
    if (ctx.base) event_base_free(ctx.base);
    if (ctx.pipe) pclose(ctx.pipe);
    if (temp_fd != -1) close(temp_fd);

    if (unlink(temp_file) == -1) {
        strerror_r(errno, strerror_buf, sizeof(strerror_buf));
        NPWG_LOG(AL_ERROR, "Failed to delete temporary file: %s", temp_file);
    }

    return status;
}
