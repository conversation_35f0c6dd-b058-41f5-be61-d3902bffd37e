/*
 * npwg_config.h. Copyright (C) 2024 Zscaler Inc. All Rights Reserved.
 *
 */

#ifndef _NPWG_EXEC_H_
#define _NPWG_EXEC_H_

#include <sys/types.h>

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief Execute an executable, return output and error. Do not hang if the child
 *        hangs. The executable must be either a binary executable, or a script starting
 *        with a line of the form:
 *        #!interpreter [optional-arg]
 *
 * @param [in] executable          Path to executable.
 * @param [in] argv                argv for the executable.
 * @param [in] in_buf              Input buffer. Optional. If not required, provide NULL.
 * @param [in] in_buf_len          Input buffer length. Optional. If not required, provide 0.
 *                                 If it is a NUL terminated string, provide the strlen().
 * @param [out] out_buf            Output buffer. Optional. If not required, provide NULL.
 * @param [in,out] out_buf_len     Output buffer length. Optional. If not required, provide 0.
 *                                 If it is going to be a NUL terminated string,
 *                                 provide strlen() + 1 or larger.
 * @param [out] err_buf            Error buffer. Optional. If not required, provide NULL.
 * @param [in,out] err_buf_len     Error buffer length. Optional. If not required, provide 0.
 *                                 If it is going to be a NUL terminated string,
 *                                 provide strlen() + 1 or larger.
 * @param [in] max_wait_time       How long to wait before giving up on execution.
 *
 * @return int                     NPWG_RESULT_NO_ERROR on success, other NPWG errors on error
 *                                 Detailed error message can be accessed via npwg_get_error()
 */
int npwg_exec(const char *executable,
              char *const argv[],
              const char *in_buf,
              int in_buf_len,
              char *out_buf,
              int *out_buf_len,
              char *err_buf,
              int *err_buf_len,
              int max_wait_time);


/**
 * @brief  Function to execute a command using popen and Libevent, supporting timeout
 *              Spawning the command via popen().
 *              Setting up Libevent for asynchronous monitoring of the pipe.
 *              Managing a timeout to handle long-running commands.
 *              Returning the exit status of the command.
 *
 * @param command               The shell command to execute.
 * @param out_buf               Pre-allocated buffer to store stdout output.
 * @param out_buf_length        Pointer to store the actual length of written stdout.
 * @param err_buf               Pre-allocated buffer to store stderr output.
 * @param err_buf_length        Pointer to store the actual length of written stderr.
 * @param max_wait_time         Maximum time (in seconds) to wait for command execution.
 * @return int                  Exit status of the command (0 for success, 1 for failure, 14 for timeout).
 */

int npwg_exec_popen(const char *command,
                    char *out_buf,
                    ssize_t *out_buf_length,
                    char *err_buf,
                    ssize_t *err_buf_length,
                    int timeout_seconds);

#ifdef __cplusplus
} // extern "C"
#endif

#endif  /* _NPWG_EXEC_H_ */
