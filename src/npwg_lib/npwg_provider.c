/*
 * npwg_provider.c. Copyright (C) 2024 Zscaler Inc. All Rights Reserved.
 */

#include "npwg_lib/npwg_provider.h"
#include "npwg_lib/npwg_common.h"
#include "npwg_lib/npwg_config.h"
#include "npwg_lib/npwg_exec.h"
#include "zhash/zhash_table.h"
#include "npwg_lib/npwg_net_util.h"
#include "zpath_misc/zpath_misc.h"
#include "zpath_lib/zpath_debug.h"

#include <assert.h>
#include <errno.h>
#include <fcntl.h>
#include <stdbool.h>
#include <stdint.h>
#include <stdio.h>
#include <string.h>
#include <sys/stat.h>
#include <unistd.h>
#include <errno.h>
#include <net/if.h>

#ifdef __linux__
#include <net/if.h>
#include <sys/ioctl.h>
#endif

struct argo_log_collection *npwg_event_collection = NULL;
uint64_t npwg_debug_log = 0;
uint64_t npwg_debug_log_catch_defaults = 0;

const char *npwg_debug_log_names[] = NPWG_DEBUG_LOG_NAMES;

static struct argo_structure_description *npwg_provider_stats_description;

static struct npwg_provider_stats{                        /* _ARGO: * object_definition */
    int64_t update_peer_hash_walk_no_cookie;              /* _ARGO: integer */
    int64_t update_peer_no_public_key_in_obj;             /* _ARGO: integer */
    int64_t update_peer_wg_set_add_attempt;               /* _ARGO: integer */
    int64_t update_peer_wg_set_remove_attempt;            /* _ARGO: integer */
    int64_t update_peer_wg_set_fail;                      /* _ARGO: integer */
    int64_t update_peer_wg_set_success;                   /* _ARGO: integer */
} stats;
#include "npwg_lib/npwg_provider_compiled_c.h"

/*
 * NOTE: The paths are hard-coded for now. The interface config
 *       file needs to be named as <interface_name>.conf to be
 *       usable by the wg-quick utility.
 * NOTE: Try to get rid of the config file using wg commands.
 *
 */
#define NPWG_SUDO_PATH                          "/usr/bin/sudo"
#define NPWG_WIREGUARD_BINARY_PATH              "/opt/zscaler/bin/wg"
#define NPWG_WIREGUARD_QUICK_BINARY_PATH        "/opt/zscaler/bin/wg-quick"

#define NPWG_WIREGUARD_LOG_BUFFER_SIZE          512

// NOTE: an operator must be accessed from only one thread
typedef struct npwg_provider_impl
{
    npwg_config *config;
    bool updates_pending;

    /*
     * for client facing/gateway facing interface, its the current gateway gid
     * for connector facing interface, its the connector gid
     */
    int64_t gid;
    int is_seperate_interface;

    /* ie /opt/zscaler/etc/wireguard/wg23240.conf */
    char conf_path[256];

    /* wg23240.conf */
    char interface_name[IFNAMSIZ];
} npwg_provider_impl;

static void init_lib()
{
    static zpath_mutex_t npwg_lib_lock;
    static bool npwg_lib_initialized = 0;
    ZPATH_MUTEX_LOCK(&npwg_lib_lock, __FILE__, __LINE__);
    if (!npwg_lib_initialized) {
        zpath_debug_add_allocator(&npwg_allocator, "npwg_lib");
        npwg_lib_initialized = 1;
    }
    ZPATH_MUTEX_UNLOCK(&npwg_lib_lock, __FILE__, __LINE__);
}

void npwg_destroy_provider(npwg_provider *provider_ptr)
{
    if (!provider_ptr) return;
    npwg_provider provider = *provider_ptr;
    if (!provider) return;

    if (provider->config) npwg_destroy_config(&provider->config);

    NPWG_FREE(provider);
    *provider_ptr = NULL;
}

npwg_provider npwg_create_provider()
{
    init_lib();
    npwg_provider provider = NPWG_CALLOC(sizeof(npwg_provider_impl));
    if (!provider) {
        NPWG_LOG(AL_ERROR, "Failed to allocate memory for provider.");
        return NULL;
    }

    provider->config = npwg_create_config();
    if (!provider->config) {
        npwg_destroy_provider(&provider);
        return NULL;
    }

    provider->updates_pending = false;

    return provider;
}

int npwg_set_interface_config(npwg_provider provider,
                              const char *private_key,
                              uint16_t listen_port)
{
    if (!provider) {
        NPWG_LOG(AL_ERROR, "Invalid provider.");
        return NPWG_RESULT_BAD_ARGUMENT;
    }
    int rc = npwg_set_interface_config_impl(provider->config, private_key, listen_port);
    if (!rc) provider->updates_pending = true;
    return rc;
}

int npwg_set_peer_config(npwg_provider provider,
                         const char *public_key,
                         const struct argo_inet *ip,
                         uint16_t port,
                         const struct argo_inet *cidrs,
                         size_t cidr_count,
                         int keepalive_interval,
                         enum npwg_wireguard_peer_types peer_type)
{
    if (!provider) {
        NPWG_LOG(AL_ERROR, "Invalid provider.");
        return NPWG_RESULT_BAD_ARGUMENT;
    }

    const char *converted_ip = NULL;
    char argo_ip[NPWG_ARGO_INET_STR_MAX_LENGTH + 1];
    if (ip) {
        converted_ip = argo_inet_generate(argo_ip, ip);
    }

    int rc = 0;
    if (!cidrs || !cidr_count) {
        rc = npwg_set_peer_config_impl(provider->config, public_key, converted_ip,
                                       port, NULL, 0, keepalive_interval, peer_type);
        if (!rc) provider->updates_pending = true;
        return rc;
    }

    rc = npwg_set_peer_config_impl(provider->config, public_key, converted_ip,
                                   port, cidrs, cidr_count, keepalive_interval, peer_type);
    if (!rc) provider->updates_pending = true;
    return rc;
}

int npwg_delete_peer_config(npwg_provider provider, const char *public_key)
{
    static int npwg_delete_peer_config_runs = 0;
    ++npwg_delete_peer_config_runs;
    NPWG_LOG(AL_INFO,
             "public_key = %s, npwg_delete_peer_config_runs = %d",
             public_key,
             npwg_delete_peer_config_runs);
    if (!provider || !provider->config) {
        NPWG_LOG(AL_ERROR, "Invalid provider.");
        return NPWG_RESULT_BAD_ARGUMENT;
    }
    int rc = npwg_delete_peer_config_impl(provider->config, public_key);
    if (!rc) provider->updates_pending = true;
    return rc;
}

int npwg_set_client_peer(npwg_provider provider,
                         const char *public_key,
                         const struct argo_inet *client_ip)
{
    if (!client_ip) {
        NPWG_LOG(AL_ERROR, "Invalid client IP.");
        return NPWG_RESULT_BAD_ARGUMENT;
    }
    int rc = npwg_set_peer_config(provider, public_key, NULL, NPWG_INVALID_PORT,
                                  client_ip, 1, DEFAULT_KEEPALIVE_INTERVAL_S,
                                  npwg_wireguard_peer_type_client);
    if (!rc) provider->updates_pending = true;
    return rc;
}

int npwg_set_connector_peer(npwg_provider provider,
                            const char *public_key,
                            const struct argo_inet *lan_cidrs,
                            size_t cidr_count,
                            bool updates_pending)
{
    int rc = npwg_set_peer_config(provider, public_key, NULL, NPWG_INVALID_PORT,
                                  lan_cidrs, cidr_count, DEFAULT_KEEPALIVE_INTERVAL_S,
                                  npwg_wireguard_peer_type_connector);
    if (!rc) provider->updates_pending = updates_pending;
    return rc;
}

int npwg_set_gateway_peer(npwg_provider provider,
                          const char *public_key,
                          const struct argo_inet *ip,
                          uint16_t port,
                          const struct argo_inet *remote_cidrs,
                          size_t cidr_count)
{
    int rc = npwg_set_peer_config(provider, public_key, ip, port,
                                  remote_cidrs, cidr_count, DEFAULT_KEEPALIVE_INTERVAL_S,
                                  npwg_wireguard_peer_type_npgateway);
    if (!rc) provider->updates_pending = true;
    return rc;
}

// work on a standard page at a time
#define PAGE_SIZE 4096
static const char zero_buf[PAGE_SIZE];

/**
 * @brief Zero out contents of a file (one pass) and truncate the file
 *
 * @param [in] file_path       Path of the file.
 *
 * @return int                 NPWG_RESULT_NO_ERROR on success,
 *                             other NPWG_RESULT_* codes otherwise.
 */
static int zero_out_file(const char *file_path)
{
    if (!file_path) {
        NPWG_LOG(AL_ERROR, "Invalid config file.");
        return NPWG_RESULT_BAD_ARGUMENT;
    }
    int fd = open(file_path, O_WRONLY);
    char errno_str[128];
    if (-1 == fd) {
        strerror_r(errno, errno_str, sizeof(errno_str));
        NPWG_LOG(AL_ERROR, "Failed to open config file, %s, error: %s.",
                 file_path, errno_str);
        return NPWG_RESULT_SYSCALL_FAILED;
    }

    struct stat file_stat;
    int rc = fstat(fd, &file_stat);
    if (-1 == rc) {
        strerror_r(errno, errno_str, sizeof(errno_str));
        NPWG_LOG(AL_ERROR, "Failed to find config file stat, %s, error: %s.",
                 file_path, errno_str);
        close(fd);
        return NPWG_RESULT_SYSCALL_FAILED;
    }

    const int64_t file_size = file_stat.st_size;
    for (int64_t offset = 0, bytes_written = 0;
         offset < file_size; offset += bytes_written) {
        // bytes to write this time
        int64_t bytes_to_write = file_size - offset;
        if (bytes_to_write > PAGE_SIZE) bytes_to_write = PAGE_SIZE;
        if ((bytes_written = write(fd, zero_buf, bytes_to_write)) < 0) {
            strerror_r(errno, errno_str, sizeof(errno_str));
            NPWG_LOG(AL_ERROR, "Failed to write to config file, %s, error: %s.",
                     file_path, errno_str);
            close(fd);
            return NPWG_RESULT_SYSCALL_FAILED;
        }
    }

    rc = ftruncate(fd, 0);
    if (-1 == rc) {
        strerror_r(errno, errno_str, sizeof(errno_str));
        NPWG_LOG(AL_ERROR,
                 "Failed to find config file stat, %s, error: %s.",
                 file_path,
                 errno_str);
        close(fd);
        return NPWG_RESULT_SYSCALL_FAILED;
    }

    close(fd);
    return NPWG_RESULT_NO_ERROR;
}

/**
 * @brief Create a Wireguard VRF and populate it with a default route
 *
 * @param [in] provider            NPWG provider handle. Can't be NULL. Created using
 *                                 npwg_create_provider().
 *
 * @return int                     NPWG_RESULT_NO_ERROR on success,
 *                                 other NPWG_RESULT_* codes otherwise.
 */
int npwg_wireguard_vrf(npwg_provider provider) {
    int rc;
    char *const argv1[]  = {
                    NPWG_SUDO_PATH, NPWG_COMMAND_IP, NPWG_COMMAND_LINK,
                    NPWG_COMMAND_ADD, NPWG_VRF_NAME, NPWG_COMMAND_TYPE,
                    NPWG_COMMAND_VRF, NPWG_COMMAND_TABLE,
                    NPWG_VRF_TABLE_ID,
                    NULL
                   };
    char *const argv2[] = {
                    NPWG_SUDO_PATH, NPWG_COMMAND_IP, NPWG_COMMAND_LINK,
                    NPWG_COMMAND_SET, NPWG_COMMAND_DEV, NPWG_VRF_NAME,
                    NPWG_COMMAND_UP,
                    NULL
                   };
    char *const argv3[] = {
                    NPWG_SUDO_PATH, NPWG_COMMAND_IP, NPWG_COMMAND_LINK,
                    NPWG_COMMAND_SET, NPWG_COMMAND_DEV, NPWG_INTERFACE_NAME,
                    NPWG_COMMAND_MASTER,
                    NPWG_VRF_NAME,
                    NULL
                   };
    char *const argv4[] = {
                    NPWG_SUDO_PATH, NPWG_COMMAND_IP, NPWG_COMMAND_ROUTE,
                    NPWG_COMMAND_ADD, NPWG_DEFAULT_ROUTE, NPWG_COMMAND_DEV,
                    NPWG_INTERFACE_NAME, NPWG_COMMAND_VRF,
                    NPWG_VRF_NAME,
                    NULL
                   };
    char err_buf[1024];
    int err_buf_length = sizeof(err_buf);

    // Create the Wireguard VRF here and bring it up
    //ip link add NPWG_VRF_NAME type vrf table table_id
    rc = npwg_exec(NPWG_SUDO_PATH, argv1, NULL, 0, NULL, 0, err_buf, &err_buf_length, 5);
    if (rc == 0) {
        if (err_buf_length > 0 && err_buf[0] != '\0') {
            NPWG_LOG(AL_WARNING,
             "Command: " NPWG_SUDO_PATH " " NPWG_COMMAND_IP " " NPWG_COMMAND_LINK " "
             NPWG_COMMAND_ADD " " NPWG_VRF_NAME " " NPWG_COMMAND_TYPE " "
             NPWG_COMMAND_VRF " " NPWG_COMMAND_TABLE " "
             NPWG_VRF_TABLE_ID ". Output: %s", err_buf);
        }
    } else {
        NPWG_LOG(AL_ERROR,
                 "Failed to create Wireguard VRF " NPWG_VRF_NAME);
    }
    if (rc != 0) {
        return rc;
    }
    //ip link set dev NPWG_VRF_NAME up
    rc = npwg_exec(NPWG_SUDO_PATH, argv2, NULL, 0, NULL, 0, err_buf, &err_buf_length, 5);
    if (rc == 0) {
        if (err_buf_length > 0 && err_buf[0] != '\0') {
            NPWG_LOG(AL_WARNING,
             "Command: " NPWG_SUDO_PATH " " NPWG_COMMAND_IP " " NPWG_COMMAND_LINK " "
             NPWG_COMMAND_SET " " NPWG_COMMAND_DEV " " NPWG_VRF_NAME " "
             NPWG_COMMAND_UP "  "
             NPWG_VRF_TABLE_ID ". Output: %s", err_buf);
        }
    } else {
        NPWG_LOG(AL_ERROR,
            "Wireguard VRF is down for " NPWG_VRF_NAME);
    }
    //ip link set dev NPWG_INTERFACE_NAME master NPWG_VRF_NAME
    rc = npwg_exec(NPWG_SUDO_PATH, argv3, NULL, 0, NULL, 0, err_buf, &err_buf_length, 5);
    if (rc == 0) {
        if (err_buf_length > 0 && err_buf[0] != '\0') {
            NPWG_LOG(AL_WARNING,
                    "Command: " NPWG_SUDO_PATH " " NPWG_COMMAND_IP " " NPWG_COMMAND_LINK " "
                    NPWG_COMMAND_SET " " NPWG_COMMAND_DEV " " NPWG_INTERFACE_NAME " "
                    NPWG_COMMAND_MASTER " " NPWG_VRF_NAME ". Output: %s",
                    err_buf);
        }
    } else {
        NPWG_LOG(AL_ERROR,
            NPWG_INTERFACE_NAME " Interface unsuccessfully configured under VRF " NPWG_VRF_NAME);
    }
    //ip route add 0.0.0.0/0 dev NPWG_INTERFACE_NAME vrf NPWG_VRF_NAME
    rc = npwg_exec(NPWG_SUDO_PATH, argv4, NULL, 0, NULL, 0, err_buf,
             &err_buf_length, 5);
    if (rc == 0) {
        if (err_buf_length > 0 && err_buf[0] != '\0') {
            NPWG_LOG(AL_WARNING,
                    "Command: " NPWG_SUDO_PATH " " NPWG_COMMAND_IP " " NPWG_COMMAND_ROUTE " "
                    NPWG_COMMAND_ADD " " NPWG_DEFAULT_ROUTE " " NPWG_COMMAND_DEV " "
                    NPWG_INTERFACE_NAME " " NPWG_COMMAND_VRF " " NPWG_VRF_NAME ". Output: %s\n",
                    err_buf);
        }
    } else {
        NPWG_LOG(AL_ERROR, NPWG_INTERFACE_NAME " interface could not be added in VRF " NPWG_VRF_NAME);
    }
    return rc;
}

int npwg_link_wg_interface_to_vrf(npwg_provider provider, const char* vrf_name)
{
    if (!provider || !vrf_name) {
        NPWG_LOG(AL_ERROR, "no provider or no vrf name provided");
        return NPWG_RESULT_BAD_ARGUMENT;
    }
    int rc = NPWG_RESULT_NO_ERROR;
    char err_buf[1024];
    int err_buf_length = sizeof(err_buf);

    char *interface_name = NULL;
    if (provider->is_seperate_interface) {
        interface_name = provider->interface_name;
    } else {
        interface_name = NPWG_INTERFACE_NAME;
    }
    char *const argv[] = {
                    NPWG_SUDO_PATH, NPWG_COMMAND_IP, NPWG_COMMAND_LINK,
                    NPWG_COMMAND_SET, NPWG_COMMAND_DEV, interface_name,
                    NPWG_COMMAND_MASTER,
                    NPWG_VRF_NAME,
                    NULL
                   };

    //ip link set dev NPWG_INTERFACE_NAME master NPWG_VRF_NAME
    rc = npwg_exec(NPWG_SUDO_PATH, argv, NULL, 0, NULL, 0, err_buf, &err_buf_length, 5);
    if (rc == 0) {
        if (err_buf_length > 0 && err_buf[0] != '\0') {
            NPWG_LOG(AL_WARNING,
                    "Command: " NPWG_SUDO_PATH " " NPWG_COMMAND_IP " " NPWG_COMMAND_LINK " "
                    NPWG_COMMAND_SET " " NPWG_COMMAND_DEV " %s " NPWG_COMMAND_MASTER " "
                    NPWG_VRF_NAME ". Output: %s",
                    interface_name, err_buf);
            rc = NPWG_RESULT_EXEC_ERROR;
        }
    } else {
            NPWG_LOG(AL_ERROR,
             "Interface %s unsuccessfully configured under VRF " NPWG_VRF_NAME, interface_name);
    }

    return rc;

}


int npwg_start_wireguard(npwg_provider provider)
{
    if (!provider) {
        NPWG_LOG(AL_ERROR, "Invalid provider.");
        return NPWG_RESULT_BAD_ARGUMENT;
    }

    int rc = NPWG_RESULT_NO_ERROR;

    char* path = NULL;

    if (provider->is_seperate_interface) {
        path = provider->conf_path;
    } else {
        path = NPWG_INTERFACE_CONFIG_PATH;
    }

    rc = npwg_write_config_file_impl(provider->config, path);
    if (rc) return rc;

    char *const argv[] = {NPWG_SUDO_PATH, NPWG_WIREGUARD_QUICK_BINARY_PATH,
                          NPWG_COMMAND_UP, path, NULL};
    char err_buf[1024];
    int err_buf_length = sizeof(err_buf);
    rc = npwg_exec(NPWG_SUDO_PATH, argv, NULL, 0, NULL, 0, err_buf, &err_buf_length, 5);
    if (rc == 0) {
        if (err_buf_length > 0 && err_buf[0] != '\0') {
            NPWG_LOG(AL_WARNING,
                    "Command: " NPWG_SUDO_PATH " " NPWG_WIREGUARD_QUICK_BINARY_PATH " "
                    NPWG_COMMAND_UP " %s. Output: %s",
                    path, err_buf);
        }
    }

    // need to zero out the contents no matter what
    int rc1 = zero_out_file(path);
    if (rc1) return rc1;

    return rc;
}

static int npwg_provider_wg_set_update_peer(const char* public_key,
                                            const char* ip,
                                            uint16_t port,
                                            const char* allowed_ips,
                                            int keepalive_interval,
                                            npwg_provider provider)
{
    if (!public_key) {
        NPWG_LOG(AL_ERROR, "Invalid public key, skipping wg set update");
        return NPWG_RESULT_NO_ERROR;
    }

    if (!provider) {
        NPWG_LOG(AL_ERROR, "No provider, skipping wg set update");
        return NPWG_RESULT_NO_ERROR;
    }

    char *argv[15];
    int argc = 0;
    char endpoint_str[64];
    char *allowed_ips_copy_no_space = NULL;
    char keepalive_str[10];

    argv[argc++] = NPWG_SUDO_PATH;
    argv[argc++] = NPWG_WIREGUARD_BINARY_PATH;
    argv[argc++] = NPWG_COMMAND_SET;
    if (provider->is_seperate_interface) {
        argv[argc++] = provider->interface_name;
    } else {
        argv[argc++] = NPWG_INTERFACE_NAME;
    }
    argv[argc++] = NPWG_COMMAND_SET_OPTION_PEER;
    argv[argc++] = (char*) public_key;

    if (ip && ip[0] && NPWG_INVALID_PORT != port) {
        snprintf(endpoint_str, sizeof(endpoint_str), "%s:%d", ip, port);
        argv[argc++] = NPWG_COMMAND_SET_OPTION_ENDPOINT;
        argv[argc++] = endpoint_str;
    }

    if (allowed_ips && allowed_ips[0]) {
        allowed_ips_copy_no_space = NPWG_MALLOC(strlen(allowed_ips) + 1);
        if (!allowed_ips_copy_no_space) {
            return NPWG_RESULT_NO_MEMORY;
        }
        npwg_remove_spaces_copy(allowed_ips, allowed_ips_copy_no_space);
        argv[argc++] = NPWG_COMMAND_SET_OPTION_ALLOWED_IPS;
        argv[argc++] = allowed_ips_copy_no_space;

    }

    if (INVALID_KEEPALIVE_INTERVAL_S != keepalive_interval) {
        snprintf(keepalive_str, sizeof(keepalive_str), "%d", keepalive_interval);
        argv[argc++] = NPWG_COMMAND_SET_OPTION_KEEPALIVE;
        argv[argc++] = keepalive_str;
    }

    argv[argc] = NULL;


    char err_buf[1024];
    int err_buf_length = sizeof(err_buf);
    int rc = npwg_exec(NPWG_SUDO_PATH, argv, NULL, 0, NULL, 0, err_buf, &err_buf_length, 5);
    if (rc == 0) {
        if (err_buf_length > 0 && err_buf[0] != '\0') {
            NPWG_LOG(AL_WARNING, "%s, wg set peer gave warning, Output: %s", public_key, err_buf);
        }
    }

    if (allowed_ips_copy_no_space) {
        NPWG_FREE(allowed_ips_copy_no_space);
        allowed_ips_copy_no_space = NULL;
    }

    return rc;
}


static int npwg_provider_wg_set_remove_peer(const char* public_key, npwg_provider provider)
{
    if (!public_key) {
        NPWG_LOG(AL_ERROR, "Invalid public key, skipping wg set remove");
        return NPWG_RESULT_NO_ERROR;
    }

    char *argv[10];
    int argc = 0;

    argv[argc++] = NPWG_SUDO_PATH;
    argv[argc++] = NPWG_WIREGUARD_BINARY_PATH;
    argv[argc++] = NPWG_COMMAND_SET;
    if (provider->is_seperate_interface) {
        argv[argc++] = provider->conf_path;
    } else {
        argv[argc++] = NPWG_INTERFACE_NAME;
    }
    argv[argc++] = NPWG_COMMAND_SET_OPTION_PEER;
    argv[argc++] = (char *) public_key;
    argv[argc++] = NPWG_COMMAND_SET_OPTION_REMOVE;

    argv[argc] = NULL;

    char err_buf[1024];
    int err_buf_length = sizeof(err_buf);
    int rc = npwg_exec(NPWG_SUDO_PATH, argv, NULL, 0, NULL, 0, err_buf, &err_buf_length, 5);
    if (rc == 0) {
        if (err_buf_length > 0 && err_buf[0] != '\0') {
            NPWG_LOG(AL_WARNING, "%s, wg set remove gave warning, Output: %s", public_key, err_buf);
        }
    }

    return rc;
}

static int npwg_provider_update_peer_f(void *cookie,
                                       void *object,
                                       __attribute__((unused)) void *key,
                                       __attribute__((unused)) size_t key_len)
{
    npwg_updated_peers_config *pending_config = object;
    npwg_provider provider = cookie;
     int rc = NPWG_RESULT_NO_ERROR;

    if (!provider) {
        return rc;
    }

    npwg_config *config = provider->config;


    zthread_heartbeat(NULL);

    if (!pending_config || !config) {
        stats.update_peer_hash_walk_no_cookie++;
        return rc;
    }

    if (!pending_config->public_key[0]) {
        stats.update_peer_no_public_key_in_obj++;
        return rc;
    }

    if (pending_config->do_add) {
        NPWG_PEER_MAP_RDLOCK(config);
        npwg_peer_config *peer_config = zhash_table_lookup(config->peer_map,
                                                           pending_config->public_key,
                                                           NPWG_KEY_LENGTH,
                                                           NULL);
        NPWG_PEER_MAP_UNLOCK(config);

        if (!peer_config) {
            NPWG_LOG(AL_ERROR, "have a pending update for peer %s, but not corresponding entry in peer_map ",
                     pending_config->public_key);
            return rc;
        }

        rc = npwg_provider_wg_set_update_peer(peer_config->public_key,
                                              peer_config->ip,
                                              peer_config->port,
                                              peer_config->allowed_ips,
                                              peer_config->keepalive_interval,
                                              provider);
        stats.update_peer_wg_set_add_attempt++;

    } else {
        rc = npwg_provider_wg_set_remove_peer(pending_config->public_key, provider);
        stats.update_peer_wg_set_remove_attempt++;
    }

    if (rc) {
        NPWG_LOG(AL_ERROR, "failed to update wg route for peer %s ",
                 pending_config->public_key);
        stats.update_peer_wg_set_fail++;
        return rc;
    }

    stats.update_peer_wg_set_success++;
    npwg_config_destroy_pending_config(pending_config, config);

    return rc;

}

int npwg_update_wireguard(npwg_provider provider, bool dont_configure_linux_routes)
{
    if (!provider) {
        NPWG_LOG(AL_ERROR, "Invalid provider.");
        return NPWG_RESULT_BAD_ARGUMENT;
    }

    if (!provider->updates_pending) return NPWG_RESULT_NO_ERROR;

    int rc;

    /* traverse updated_peers_config for peers update, and do wg set*/
    rc = zhash_table_walk(provider->config->updated_peers_config, NULL, npwg_provider_update_peer_f, provider);
    if (rc) return rc;

    NPWG_LOG(AL_INFO, "In %s, about to call: npwg_configure_routes()\n", __FUNCTION__);

    if (!dont_configure_linux_routes) {
        rc = npwg_configure_routes(provider->config);
    }

    if (!rc) provider->updates_pending = false;

    return rc;
}

int npwg_stop_wireguard(npwg_provider provider)
{
    if (!provider) {
        NPWG_LOG(AL_ERROR, "Invalid provider.");
        return NPWG_RESULT_BAD_ARGUMENT;
    }

    char* path = NULL;

    if (provider->is_seperate_interface) {
        path = provider->conf_path;
    } else {
        path = NPWG_INTERFACE_CONFIG_PATH;
    }

    int rc = npwg_write_config_file_impl(provider->config, path);
    if (rc) return rc;

    char *const argv[] = {NPWG_SUDO_PATH, NPWG_WIREGUARD_QUICK_BINARY_PATH,
                          NPWG_COMMAND_DOWN, path, NULL};
    char err_buf[1024];
    int err_buf_length = sizeof(err_buf);
    rc = npwg_exec(NPWG_SUDO_PATH, argv, NULL, 0, NULL, 0, err_buf, &err_buf_length, 5);
    if (rc == 0) {
        if (err_buf_length > 0 && err_buf[0] != '\0') {
            NPWG_LOG(AL_WARNING,
                    "Command: " NPWG_SUDO_PATH " " NPWG_WIREGUARD_QUICK_BINARY_PATH " "
                    NPWG_COMMAND_DOWN " %s. Output: %s",
                    path, err_buf);
        }
    }

    int rc1 = zero_out_file(path);
    if (rc1) return rc1;

    return rc;
}

int npwg_delete_wireguard_interface()
{
    int rc;
    char *const argv[] = {NPWG_SUDO_PATH, NPWG_WIREGUARD_QUICK_BINARY_PATH,
                          NPWG_COMMAND_DOWN, NPWG_INTERFACE_CONFIG_PATH, NULL};
    char err_buf[1024];
    int err_buf_length = sizeof(err_buf);
    rc = npwg_exec(NPWG_SUDO_PATH, argv, NULL, 0, NULL, 0, err_buf, &err_buf_length, 5);
    if (rc == 0) {
        if (err_buf_length > 0 && err_buf[0] != '\0') {
            NPWG_LOG(AL_WARNING,
                    "Command: " NPWG_SUDO_PATH " " NPWG_WIREGUARD_QUICK_BINARY_PATH " "
                    NPWG_COMMAND_DOWN " " NPWG_INTERFACE_CONFIG_PATH ". Output: %s",
                    err_buf);
        }
    }

    return rc;
}

int npwg_wg_show_all_dump(char *out_buf, ssize_t out_buf_length)
{
    if (!out_buf || !out_buf_length) {
        NPWG_LOG(AL_ERROR, "show all dump out_buf or out_buf_length NULL.");
        return NPWG_RESULT_BAD_ARGUMENT;
    }
    memset(out_buf, 0, out_buf_length);

    char command[128];
    snprintf(command, sizeof(command), "%s %s %s %s %s", NPWG_SUDO_PATH, NPWG_WIREGUARD_BINARY_PATH, NPWG_COMMAND_SHOW, NPWG_COMMAND_ALL, NPWG_COMMAND_DUMP);

    ssize_t generated_out_buf_length = out_buf_length;
    char err_buf[1024] = {0};
    ssize_t err_buf_length = sizeof(err_buf);

    int rc = npwg_exec_popen(command, out_buf, &generated_out_buf_length, err_buf, &err_buf_length, 5);
    if ( generated_out_buf_length < out_buf_length) {
        out_buf[generated_out_buf_length] = '\0';
    }
    out_buf[out_buf_length - 1] = '\0';
    if (rc) {
        NPWG_LOG(AL_ERROR, "npwg_wg_show_all_dump npwg_exec returned rc: %s", zpath_result_string(rc));
        return rc;
    }

    if (err_buf_length) {
        NPWG_LOG(AL_WARNING, "Error in executing %s %s, error message: %s", NPWG_WIREGUARD_BINARY_PATH, "show", err_buf);
        return NPWG_RESULT_EXEC_ERROR;
    }
    return NPWG_RESULT_NO_ERROR;
}

int npwg_write_config_file(npwg_provider provider, const char *config_file)
{
    if (!provider) {
        NPWG_LOG(AL_ERROR, "Invalid provider.");
        return NPWG_RESULT_BAD_ARGUMENT;
    }
    return npwg_write_config_file_impl(provider->config, config_file);
}

// note: caller ensures that length of private key is equal to NPWG_KEY_LENGTH
// note: caller ensures that public_key can hold NPWG_KEY_LENGTH + 1 bytes
static int generate_public_key(const char *private_key, char *public_key)
{
    int private_key_length = NPWG_KEY_LENGTH;
    int public_key_length = NPWG_KEY_LENGTH;
    char *const pubkey_argv[] = {NPWG_WIREGUARD_BINARY_PATH, NPWG_COMMAND_PUBKEY, NULL};
    char err_buf[1024];
    int err_buf_length = sizeof(err_buf);
    int rc = npwg_exec(NPWG_WIREGUARD_BINARY_PATH, pubkey_argv,
                       private_key, private_key_length, public_key, &public_key_length,
                       err_buf, &err_buf_length, 5);
    if (rc) return rc;

    if (err_buf_length) {
        NPWG_LOG(AL_WARNING, "Error in executing %s %s, error message: %s", NPWG_WIREGUARD_BINARY_PATH, NPWG_COMMAND_PUBKEY, err_buf);
        return NPWG_RESULT_EXEC_ERROR;
    }

    if (NPWG_KEY_LENGTH != public_key_length) {
        NPWG_LOG(AL_ERROR, "Generated public key is of incorrect size: %d", public_key_length - 1);
        return NPWG_RESULT_WG_USER_ERROR;
    }


    public_key[NPWG_KEY_LENGTH] = '\0';
    return NPWG_RESULT_NO_ERROR;
}

int npwg_gen_keys(char *private_key, int private_key_length,
                  char *public_key, int public_key_length)
{
    if (!private_key) {
        NPWG_LOG(AL_ERROR, "private_key NULL.");
        return NPWG_RESULT_BAD_ARGUMENT;
    }
    if (private_key_length < NPWG_KEY_LENGTH + 1) {
        NPWG_LOG(AL_ERROR, "private_key buffer small.");
        return NPWG_RESULT_BAD_ARGUMENT;
    }
    if (!public_key) {
        NPWG_LOG(AL_ERROR, "public_key NULL.");
        return NPWG_RESULT_BAD_ARGUMENT;
    }
    if (public_key_length < NPWG_KEY_LENGTH + 1) {
        NPWG_LOG(AL_ERROR, "public_key buffer small.");
        return NPWG_RESULT_BAD_ARGUMENT;
    }

    char *const genkey_argv[] = {NPWG_WIREGUARD_BINARY_PATH, NPWG_COMMAND_GENKEY, NULL};
    int generated_private_key_length = NPWG_KEY_LENGTH;
    char err_buf[1024];
    int err_buf_length = sizeof(err_buf);
    int rc = npwg_exec(NPWG_WIREGUARD_BINARY_PATH, genkey_argv, NULL, 0,
                   private_key, &generated_private_key_length,
                   err_buf, &err_buf_length, 5);

    if (rc) return rc;
    if (err_buf_length) {
        NPWG_LOG(AL_WARNING, "Error in executing %s %s, error message: %s", NPWG_WIREGUARD_BINARY_PATH, NPWG_COMMAND_GENKEY, err_buf);
        return NPWG_RESULT_EXEC_ERROR;
    }

    if (NPWG_KEY_LENGTH != generated_private_key_length) {
        private_key[0] = '\0';
        NPWG_LOG(AL_ERROR, "Generated private key is of incorrect size: %d", generated_private_key_length);
        return NPWG_RESULT_WG_USER_ERROR;
    }

    private_key[NPWG_KEY_LENGTH] = '\0';

    rc = generate_public_key(private_key, public_key);
    if (rc) return rc;
    public_key[NPWG_KEY_LENGTH] = '\0';

    return NPWG_RESULT_NO_ERROR;
}

int npwg_validate_keys(const char *private_key, const char *public_key)
{
    if (!private_key) {
        NPWG_LOG(AL_ERROR, "private_key NULL.");
        return NPWG_RESULT_BAD_ARGUMENT;
    }
    size_t private_key_length = strnlen(private_key, NPWG_KEY_LENGTH + 1);
    if (private_key_length != NPWG_KEY_LENGTH) {
        NPWG_LOG(AL_ERROR, "private_key has incorrect size.");
        return NPWG_RESULT_BAD_ARGUMENT;
    }
    if (!public_key) {
        NPWG_LOG(AL_ERROR, "public_key NULL.");
        return NPWG_RESULT_BAD_ARGUMENT;
    }

    size_t public_key_length = strnlen(public_key, NPWG_KEY_LENGTH + 1);
    if (public_key_length != NPWG_KEY_LENGTH) {
        NPWG_LOG(AL_ERROR, "public_key has incorrect size.");
        return NPWG_RESULT_BAD_ARGUMENT;
    }

    char derived_public_key[NPWG_KEY_LENGTH + 1];

    int rc = generate_public_key(private_key, derived_public_key);
    if (rc) return rc;

    if (0 == strncmp(public_key, derived_public_key, NPWG_KEY_LENGTH + 1))
        return NPWG_RESULT_NO_ERROR;

    return NPWG_RESULT_BAD_ARGUMENT;
}


int npwg_read_config_file(npwg_provider provider, const char *config_file)
{
    if (!provider) {
        return NPWG_RESULT_ERR;
    }

    int rc = npwg_parse_config_file(config_file, provider->config);

    return rc;
}

static int npwg_provider_dump_stats(struct zpath_debug_state*  request_state,
                                    __attribute__((unused))const char** query_values,
                                    __attribute__((unused))int          query_value_count,
                                    __attribute__((unused))void*        cookie)
{
    char jsonout[10000];

    if (ARGO_RESULT_NO_ERROR == argo_structure_dump(npwg_provider_stats_description,
                                                    &stats,
                                                    jsonout,
                                                    sizeof(jsonout),
                                                    NULL,
                                                    1)) {
        ZDP("%s\n", jsonout);
    }
    return NPWG_RESULT_NO_ERROR;
}

int npwg_provider_debug_init()
{
    int rc;
    npwg_provider_stats_description = argo_register_global_structure(NPWG_PROVIDER_STATS_HELPER);

    if (!npwg_provider_stats_description) {
        NPWG_LOG(AL_ERROR, "Cannot argo_register_global_structure npwg_provider_stats_description");
        return NPWG_RESULT_ERR;
    }

     rc = zpath_debug_add_read_command("dump the stats of npwg_provider_stats",
                                  "/npwg/npwg_provider_stats",
                                  npwg_provider_dump_stats,
                                  NULL,
                                  NULL);
    if (rc){
        NPWG_LOG(AL_ERROR, "Cannot zpath_debug_add_admin_command /npwg/npwg_provider_stats");
        return rc;
    }

    rc = npwg_config_debug_init();

    return rc;

}


int npwg_provider_log_init(struct argo_log_collection *event_log)
{
    init_lib();
    npwg_event_collection = event_log;

    int rc = zpath_debug_add_flag(&npwg_debug_log, npwg_debug_log_catch_defaults,
                                  "npwg", npwg_debug_log_names);
    if (rc) {
        return rc;
    }

    rc = npwg_provider_debug_init();
    return rc;
}

size_t np_provider_get_config_peer_size(npwg_provider provider)
{
    if (!provider || !provider->config || !provider->config->peer_map) {
        return 0;
    }

    NPWG_PEER_MAP_RDLOCK(provider->config);
    const size_t size = zhash_table_get_size(provider->config->peer_map);
    NPWG_PEER_MAP_UNLOCK(provider->config);
    return size;
}

#ifdef __linux__

// not MT Safe
void npwg_provider_monitor_wg_log()
{
    static int fd = 0;
    if (fd <= 0) {
        fd = open(NPWG_KMSG_RING_BUFFER, O_RDONLY | O_NONBLOCK);
        if (fd < 0) {
            NPWG_LOG(AL_ERROR, "Could not open " NPWG_KMSG_RING_BUFFER);
            return;
        }
    }

    // handle the case where the existing fd is no longer valid
    // this is the chepest way to check if an fd is valid
    // see: https://github.com/python/cpython/issues/90073
    if (-1 == fcntl(fd, F_GETFD)) {
        close(fd);
        NPWG_LOG(AL_ERROR, "Open fd for: " NPWG_KMSG_RING_BUFFER
                 " has gone bad. Trying to reopen");
        fd = open(NPWG_KMSG_RING_BUFFER, O_RDONLY | O_NONBLOCK);
        if (fd < 0) {
            NPWG_LOG(AL_ERROR, "Could not open " NPWG_KMSG_RING_BUFFER);
            return;
        }
    }

    char buffer[NPWG_WIREGUARD_LOG_BUFFER_SIZE];
    ssize_t bytes_read = 0;

    do {
        bytes_read = read(fd, buffer, NPWG_WIREGUARD_LOG_BUFFER_SIZE - 1);

        // most likely
        if (bytes_read > 0) {
            buffer[bytes_read] = '\0';
            // majority of kernel messages are expected to be wireguard related in steady state
            if (strstr(buffer, "wireguard")) {
                NPWG_LOG(AL_NOTICE, "%s", buffer);

                #ifdef NPWG_PROVIDER_MONITOR_WG_LOG_TESTING
                FILE *fp = fopen("/tmp/out.file", "a");
                fprintf(fp, "buffer: %s\n", buffer);
                fclose(fp);
                #endif
            }

            continue;
        }

        // less likey
        if (bytes_read < 0) {
            if (errno == EAGAIN) {
                return;
            }
            else if (errno == EINTR) {
                    NPWG_LOG(AL_INFO,
                             "Read from " NPWG_KMSG_RING_BUFFER " got interrupted");
                    continue;
            }
            NPWG_LOG(AL_ERROR,
                     "Read from " NPWG_KMSG_RING_BUFFER " failed with errno: %d",
                     errno);
            return;
        }

        // impossible to get here - you won't get an EOF
        break;

    } while (true);

    // note: not closing the fd is intentional, we intend to reuse the fd
}

#else

void npwg_provider_monitor_wg_log()
{
    NPWG_LOG(AL_ERROR, "Implementation exists only for Linux");
}

#endif

void npwg_provider_update_wg_status_file(int status)
{
    static int is_np_dir_created_in_cwd = 0;

    if (!is_np_dir_created_in_cwd) {

        if (mkdir(NPWG_WIREGUARD_NPWG0_STATUS_FILENAME_LOCATION, 0700) == -1) {
            if (errno == EEXIST) {
                is_np_dir_created_in_cwd = 1;
            } else {
                NPWG_LOG(AL_ERROR, "Could not create directory /%s in cwd for writing wg status", NPWG_WIREGUARD_NPWG0_STATUS_FILENAME_LOCATION);
                return;
            }
        } else {
            is_np_dir_created_in_cwd = 1;
        }
    }

    FILE *fp = fopen(NPWG_WIREGUARD_NPWG0_STATUS_FULL_PATH_FILENAME, "w");

    if (fp) {
        fprintf(fp, "%d", status);
        fclose(fp);
    } else {
        NPWG_LOG(AL_ERROR, "Fail to write to %s for status update", NPWG_WIREGUARD_NPWG0_STATUS_FULL_PATH_FILENAME);
    }

}

enum npwg_wireguard_peer_types npwg_provider_get_config_peer_type(npwg_provider provider, const char *public_key)
{
    if (!provider || !provider->config || !provider->config->peer_map || !public_key) {
        return npwg_wireguard_peer_type_invalid;
    }

    enum npwg_wireguard_peer_types peer_type = npwg_wireguard_peer_type_invalid;

    NPWG_PEER_MAP_RDLOCK(provider->config);
    const npwg_peer_config *peer_config = zhash_table_lookup(provider->config->peer_map,
                                                             public_key,
                                                             NPWG_KEY_LENGTH,
                                                             NULL);
    if (peer_config) {
        peer_type = peer_config->peer_type;
    }
    NPWG_PEER_MAP_UNLOCK(provider->config);

    return peer_type;
}


int npwg_provider_set_mtu(npwg_provider provider, int mtu)
{
    if (provider) {
        return npwg_set_mtu(provider->interface_name, mtu);
    } else {
        return npwg_set_mtu(NPWG_INTERFACE_NAME, mtu);
    }

}

/* Set wireguard interface to NPWG_INTERFACE_PREFIX + "last 6 digits of np connector gid"
 * Wireguard interface's max length is 15 characters
 * With this trick we can handle up to 1M provisioned connectors in total */
void npwg_provider_redundancy_wg_interface_name(int64_t connector_gid, char *out_buf, size_t out_buf_len)
{
    int last_6_digits = connector_gid % 1000000;
    snprintf(out_buf, out_buf_len, "%s%d", NPWG_INTERFACE_PREFIX, last_6_digits);
}

int npwg_provider_set_provider_meta(npwg_provider provider,
                                    int64_t gid)
{
    if (!provider) {
        NPWG_LOG(AL_ERROR, "npwg_provider_set_provider_meta no provider");
        return NPWG_RESULT_ERR;
    }

    const char* base_path ="/opt/zscaler/etc/wireguard/";
    const char* ext = ".conf";
#ifdef __linux__
        char interface[IFNAMSIZ];
#else
        char interface[16];
#endif

    provider->gid = gid;
    provider->is_seperate_interface = 1;
    npwg_provider_redundancy_wg_interface_name(gid, &(interface[0]), sizeof(interface));
    snprintf(provider->conf_path, sizeof(provider->conf_path), "%s%s%s", base_path, interface, ext);
    snprintf(provider->interface_name, sizeof(provider->interface_name), "%s", interface);

    return NPWG_RESULT_NO_ERROR;
}


int npwg_provider_ip_link_add_delete_npwg_intf(npwg_provider provider, int add)
{
    if (!provider) {
        NPWG_LOG(AL_ERROR, "npwg_provider_ip_link_add_wg_intf no provider");
        return NPWG_RESULT_ERR;
    }

    if (provider->interface_name[0] == '\0') {
        NPWG_LOG(AL_ERROR, "provider without interface name!");
        return NPWG_RESULT_ERR;
    }

    int rc = NPWG_RESULT_NO_ERROR;
    char * action = NULL;
    if (add) {
        action = NPWG_COMMAND_ADD;
    } else {
        action = NPWG_COMMAND_DEL;
    }

    //ip link add dev wgxxxxx type wireguard
    char *const argv[]  = {
                    NPWG_SUDO_PATH,
                    NPWG_COMMAND_IP,
                    NPWG_COMMAND_LINK,
                    action,
                    NPWG_COMMAND_DEV,
                    provider->interface_name,
                    NPWG_COMMAND_TYPE,
                    NPWG_COMMAND_WIREGUARD,
                    NULL
                   };
    char err_buf[1024];
    int err_buf_length = sizeof(err_buf);

    rc = npwg_exec(NPWG_SUDO_PATH, argv, NULL, 0, NULL, 0, err_buf, &err_buf_length, 5);
    if (rc == 0) {
        if (err_buf_length > 0 && err_buf[0] != '\0') {
            NPWG_LOG(AL_WARNING,
                    "Command: " NPWG_SUDO_PATH " " NPWG_COMMAND_IP " " NPWG_COMMAND_LINK " %s "
                    NPWG_COMMAND_DEV " %s " NPWG_COMMAND_TYPE " " NPWG_COMMAND_WIREGUARD ". Output: %s",
                    action, provider->interface_name, err_buf);
        }
    } else {
        NPWG_LOG(AL_ERROR,
            "Failed to %s Wireguard Interface %s",
            action, provider->interface_name);
    }

    return rc;

}


int npwg_provider_ip_link_set_up_down_npwg_intf(npwg_provider provider, int up)
{
    if (!provider) {
        NPWG_LOG(AL_ERROR, "npwg_provider_ip_link_set_up_npwg_intf no provider");
        return NPWG_RESULT_ERR;
    }

    if (provider->interface_name[0] == '\0') {
        NPWG_LOG(AL_ERROR, "provider without interface name!");
        return NPWG_RESULT_ERR;
    }

    char *action = NULL;
    if (up) {
        action = NPWG_COMMAND_UP;
    } else {
        action = NPWG_COMMAND_DOWN;
    }

    int rc = NPWG_RESULT_NO_ERROR;

    //ip link set up dev wg0
    char *const argv[]  = {
                    NPWG_SUDO_PATH,
                    NPWG_COMMAND_IP,
                    NPWG_COMMAND_LINK,
                    NPWG_COMMAND_SET,
                    action,
                    provider->interface_name,
                    NULL
                   };
    char err_buf[1024];
    int err_buf_length = sizeof(err_buf);

    rc = npwg_exec(NPWG_SUDO_PATH, argv, NULL, 0, NULL, 0, err_buf, &err_buf_length, 5);
    if (rc == 0) {
        if (err_buf_length > 0 && err_buf[0] != '\0') {
            NPWG_LOG(AL_WARNING,
                    "Command: " NPWG_SUDO_PATH " " NPWG_COMMAND_IP " " NPWG_COMMAND_LINK " "
                    NPWG_COMMAND_SET " %s %s. Output: %s",
                    action, provider->interface_name, err_buf);
        }
    } else {
        NPWG_LOG(AL_ERROR,
            "Failed to bring up %s Wireguard Interface %s",
            action, provider->interface_name);
    }

    return rc;

}


int npwg_provider_wg_setconf_nwpg_intf(npwg_provider provider)
{
    if (!provider) {
        NPWG_LOG(AL_ERROR, "Invalid provider.");
        return NPWG_RESULT_BAD_ARGUMENT;
    }

    if (provider->interface_name[0] == '\0') {
        NPWG_LOG(AL_ERROR, "provider without interface name!");
        return NPWG_RESULT_ERR;
    }

    if (provider->conf_path[0] == '\0') {
        NPWG_LOG(AL_ERROR, "provider without interface configuration path!");
        return NPWG_RESULT_ERR;
    }

    int rc = NPWG_RESULT_NO_ERROR;

    rc = npwg_write_config_file_impl_full(provider->config, provider->conf_path);
    if (rc) return rc;

    char *const argv[] = {NPWG_SUDO_PATH, NPWG_WIREGUARD_BINARY_PATH,
                          NPWG_COMMAND_SETCONF, provider->interface_name,
                          provider->conf_path, NULL};
    char err_buf[1024];
    int err_buf_length = sizeof(err_buf);
    rc = npwg_exec(NPWG_SUDO_PATH, argv, NULL, 0, NULL, 0, err_buf, &err_buf_length, 5);
    if (rc == 0) {
        if (err_buf_length > 0 && err_buf[0] != '\0') {
            NPWG_LOG(AL_WARNING,
                    "Command: " NPWG_SUDO_PATH " " NPWG_WIREGUARD_BINARY_PATH " "
                    NPWG_COMMAND_SETCONF " %s %s. Output: %s",
                    provider->interface_name, provider->conf_path, err_buf);
        }
    }

    // need to zero out the contents no matter what
    int rc1 = zero_out_file(provider->conf_path);
    if (rc1) return rc1;

    return rc;
}

int npwg_check_and_delete_interface(npwg_provider provider)
{
    if (!provider) {
        NPWG_LOG(AL_ERROR, "Invalid provider.");
        return NPWG_RESULT_ERR;
    }

    if (provider->interface_name[0] == '\0') {
        NPWG_LOG(AL_ERROR, "Provider without interface name!");
        return NPWG_RESULT_ERR;
    }

    int rc = NPWG_RESULT_NO_ERROR;
    char out_buf[1024];
    int out_buf_len = sizeof(out_buf);
    char err_buf[1024];
    int err_buf_len = sizeof(err_buf);

    char *const check_argv[] = {
        NPWG_SUDO_PATH,
        NPWG_COMMAND_IP,
        NPWG_COMMAND_LINK,
        NPWG_COMMAND_SHOW,
        provider->interface_name,
        NULL};

    rc = npwg_exec(NPWG_SUDO_PATH, check_argv, NULL, 0, out_buf, &out_buf_len, err_buf, &err_buf_len, 5);

    if (rc == NPWG_RESULT_NO_ERROR) {
        if (out_buf_len > 0 && out_buf[0] != '\0') {
            NPWG_LOG(AL_INFO, "Interface %s exists. Proceeding to bring down and delete.", provider->interface_name);

            char *const down_argv[] = {
                NPWG_SUDO_PATH,
                NPWG_COMMAND_IP,
                NPWG_COMMAND_LINK,
                NPWG_COMMAND_SET,
                NPWG_COMMAND_DEV,
                provider->interface_name,
                NPWG_COMMAND_DOWN,
                NULL};

            err_buf_len = sizeof(err_buf);
            rc = npwg_exec(NPWG_SUDO_PATH, down_argv, NULL, 0, NULL, 0, err_buf, &err_buf_len, 5);

            if (rc == NPWG_RESULT_NO_ERROR) {
                if (err_buf_len > 0 && err_buf[0] != '\0') {
                    NPWG_LOG(AL_WARNING, "Non-critical warnings while bringing down the interface %s: %s", provider->interface_name, err_buf);
                }
            } else {
                NPWG_LOG(AL_ERROR, "Failed to bring down interface %s. Error: %s", provider->interface_name, err_buf);
                // Proceed with deletion anyway
            }

            char *const delete_argv[] = {
                NPWG_SUDO_PATH,
                NPWG_COMMAND_IP,
                NPWG_COMMAND_LINK,
                NPWG_COMMAND_DEL,
                NPWG_COMMAND_DEV,
                provider->interface_name,
                NPWG_COMMAND_TYPE,
                NPWG_COMMAND_WIREGUARD,
                NULL};

            err_buf_len = sizeof(err_buf); // Reset error buffer for delete command
            rc = npwg_exec(NPWG_SUDO_PATH, delete_argv, NULL, 0, NULL, 0, err_buf, &err_buf_len, 5);

            if (rc == NPWG_RESULT_NO_ERROR) {
                NPWG_LOG(AL_INFO, "WireGuard interface %s deleted successfully.", provider->interface_name);
            } else {
                NPWG_LOG(AL_ERROR, "Failed to delete WireGuard interface %s. Error: %s", provider->interface_name, err_buf);
                return NPWG_RESULT_ERR;
            }
        } else if (err_buf_len > 0 && err_buf[0] != '\0') {
            NPWG_LOG(AL_INFO, "Interface %s does not exist. No deletion needed.", provider->interface_name);
        } else {
            NPWG_LOG(AL_WARNING, "NO output response while checking interface %s.", provider->interface_name);
        }
    } else {
        NPWG_LOG(AL_ERROR, "Failed to check WireGuard interface %s. Error: %s", provider->interface_name, zpath_result_string(rc));
        return NPWG_RESULT_ERR;
    }

    return NPWG_RESULT_NO_ERROR;
}


char* npwg_provider_return_interface_name(npwg_provider provider)
{
    if (!provider || provider->interface_name[0] == '\0') {
        return NULL;
    }

    return provider->interface_name;
}

int npwg_provider_setup_interface_dummy(int is_gateway)
{
    int rc;
    char *const argv1[]  = {
                    NPWG_SUDO_PATH, NPWG_COMMAND_IP, NPWG_COMMAND_LINK,
                    NPWG_COMMAND_ADD, NPWG_INTERFACE_DUMMY, NPWG_COMMAND_TYPE,
                    NPWG_COMMAND_DUMMY,
                    NULL
                   };
    char *const argv2[] = {
                    NPWG_SUDO_PATH, NPWG_COMMAND_IP, NPWG_COMMAND_LINK,
                    NPWG_COMMAND_SET, NPWG_INTERFACE_DUMMY,
                    NPWG_COMMAND_UP,
                    NULL
                   };
    char *const argv3[] = {
                    NPWG_SUDO_PATH, NPWG_COMMAND_IP, NPWG_COMMAND_LINK,
                    NPWG_COMMAND_SET, NPWG_INTERFACE_DUMMY, NPWG_COMMAND_MASTER,
                    NPWG_VRF_NAME,
                    NULL
                };

    //ip link add dummy0 type dummy
    char err_buf[1024];
    int err_buf_length = sizeof(err_buf);
    rc = npwg_exec(NPWG_SUDO_PATH, argv1, NULL, 0, NULL, 0, err_buf, &err_buf_length, 5);
    if (rc == 0) {
        if (err_buf_length > 0 && err_buf[0] != '\0') {
            NPWG_LOG(AL_WARNING,
            "Command: " NPWG_SUDO_PATH " " NPWG_COMMAND_IP " " NPWG_COMMAND_LINK " "
            NPWG_COMMAND_ADD " " NPWG_INTERFACE_DUMMY " " NPWG_COMMAND_TYPE " "
            NPWG_COMMAND_DUMMY ". Output: %s", err_buf);
        }
    } else {
        NPWG_LOG(AL_ERROR,
                 "Failed to create interface " NPWG_INTERFACE_DUMMY);
    }
    if (rc != 0) {
        return rc;
    }

    //ip link set dummy0 up
    rc = npwg_exec(NPWG_SUDO_PATH, argv2, NULL, 0, NULL, 0, err_buf, &err_buf_length, 5);
    if (rc == 0) {
        if (err_buf_length > 0 && err_buf[0] != '\0') {
            NPWG_LOG(AL_WARNING,
            "Command: " NPWG_SUDO_PATH " " NPWG_COMMAND_IP " " NPWG_COMMAND_LINK " "
            NPWG_COMMAND_SET " " NPWG_INTERFACE_DUMMY " "
            NPWG_COMMAND_UP ". Output: %s", err_buf);
        }
    } else {
        NPWG_LOG(AL_ERROR,
            "Failed to bring up interface " NPWG_INTERFACE_DUMMY);
    }
    if (rc != 0) {
        return rc;
    }

    if (is_gateway) {
        //only required on gateway
        //ip link set dummy0 master NP_VRF_NAME
        rc = npwg_exec(NPWG_SUDO_PATH, argv3, NULL, 0, NULL, 0, err_buf, &err_buf_length, 5);
        if (rc == 0) {
            if (err_buf_length > 0 && err_buf[0] != '\0') {
                NPWG_LOG(AL_WARNING,
                "Command: " NPWG_SUDO_PATH " " NPWG_COMMAND_IP " " NPWG_COMMAND_LINK " "
                NPWG_COMMAND_SET " " NPWG_COMMAND_DEV " " NPWG_VRF_NAME " "
                NPWG_COMMAND_UP "  "
                NPWG_VRF_TABLE_ID ". Output: %s", err_buf);
            }
        } else {
            NPWG_LOG(AL_ERROR,
                "Failed to set " NPWG_VRF_NAME);
        }
    }
    return rc;
}
