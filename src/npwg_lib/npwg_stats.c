/*
* npwg_stats.c. Copyright (C) 2025 Zscaler, Inc. All Rights Reserved.
*
* This is the NP wireguard statistics monitor
*
*/

#include <event.h>
#include <stdlib.h>
#include <string.h>
#include <time.h>

#include "argo/argo_log.h"
#include "zevent/zevent.h"
#include "zpath_misc/zpath_misc.h"
#include "zthread/zthread.h"

#include "npwg_lib/npwg_provider.h"
#include "npwg_lib/npwg_stats.h"

static struct npwg_stats_wg_monitor g_npwg_stats_wg_monitor;
struct npwg_stats_wg g_npwg_stats_wg;

static char ip_route_dump_buf[NPWG_STATS_WG_MONITOR_SHOW_ALL_DUMP_BUFF_LEN];

static void npwg_stats_wg_parse_wg_show_all_dump_listen_port(const char *listen_port,
                                                                      struct npwg_stats_wg *wg_stats)
{
    if (wg_stats == NULL || listen_port == NULL || strcmp(listen_port, "(none)") == 0) {
        return;
    }
    int listen_port_int = atoi(listen_port);

    if (listen_port_int >= NPWG_STATS_WG_PORT_NUMBER_MIN &&
        listen_port_int <= NPWG_STATS_WG_PORT_NUMBER_MAX ) {
        wg_stats->this_wireguard_listen_port = listen_port_int;
    } else {
        wg_stats->this_wireguard_listen_port = NPWG_STATS_WG_PORT_DEFAULT;
    }
    return;
}

static void npwg_stats_wg_parse_wg_show_all_dump_allowed_ips_get(char *ip_addr, char *mask,
                                                                 enum npwg_wireguard_peer_types peer_type,
                                                                 struct npwg_stats_wg *wg_stats)
{
    int bytes_written = 0;
    int total_bytes_written = 0;
    char *buff = NULL;
    int bytes_available = 0;

    if (peer_type == npwg_wireguard_peer_type_client) {
        buff = g_npwg_stats_wg_monitor.buff_allowed_ips_clients;
        total_bytes_written = g_npwg_stats_wg_monitor.buff_allowed_ips_clients_total_bytes_written;
        bytes_available = NPWG_STATS_WG_MONITOR_SHOW_ALLOWED_IPS_CLIENTS_BUFF_LEN - total_bytes_written;
        size_t len = strnlen(ip_addr, NPWG_STATS_WG_MONITOR_SHOW_ALLOWED_IPS_ADDRESS_MASK_STR_LEN);
        if (bytes_available < len + NPWG_STATS_WG_MONITOR_SHOW_ALLOWED_IPS_ADDRESS_MASK_STR_LEN) {
            NPWG_LOG(AL_ERROR, "ERROR wireguard stats: wg buff allowed ips clients not enough space to get ");
            wg_stats->allowed_ips_clients_get_error_cnt++;
            return;
        }
        // skip dumping mask for clients as it is 32
        bytes_written = snprintf(buff + total_bytes_written, bytes_available, "%s,", ip_addr);
        if (bytes_written < 0) {
            NPWG_LOG(AL_ERROR, "ERROR wireguard stats: wg buff allowed ips clients snprintf error ");
            wg_stats->allowed_ips_clients_get_error_cnt++;
            return;
        }
        g_npwg_stats_wg_monitor.buff_allowed_ips_clients_total_bytes_written += bytes_written;

    } else if (peer_type == npwg_wireguard_peer_type_connector) {

        buff = g_npwg_stats_wg_monitor.buff_allowed_ips_connectors;
        total_bytes_written = g_npwg_stats_wg_monitor.buff_allowed_ips_connectors_total_bytes_written;
        bytes_available = NPWG_STATS_WG_MONITOR_SHOW_ALLOWED_IPS_CONNECTORS_BUFF_LEN - total_bytes_written;
        size_t len = strnlen(ip_addr, NPWG_STATS_WG_MONITOR_SHOW_ALLOWED_IPS_ADDRESS_MASK_STR_LEN);
        if (bytes_available < len + NPWG_STATS_WG_MONITOR_SHOW_ALLOWED_IPS_ADDRESS_MASK_STR_LEN) {
            NPWG_LOG(AL_ERROR, "ERROR wireguard stats : wg buff allowed ips connectors not enough space to get ");
            wg_stats->allowed_ips_connectors_get_error_cnt++;
            return;
        }
        bytes_written = snprintf(buff + total_bytes_written, bytes_available, "%s/%s,", ip_addr, mask);
        if (bytes_written < 0) {
            NPWG_LOG(AL_ERROR, "ERROR wireguard stats : wg buff allowed ips connectors snprintf error ");
            wg_stats->allowed_ips_connectors_get_error_cnt++;
            return;
        }
        g_npwg_stats_wg_monitor.buff_allowed_ips_connectors_total_bytes_written += bytes_written;

    } else if (peer_type == npwg_wireguard_peer_type_npgateway) {

        buff = g_npwg_stats_wg_monitor.buff_allowed_ips_npgateways;
        total_bytes_written = g_npwg_stats_wg_monitor.buff_allowed_ips_npgateways_total_bytes_written;
        bytes_available = NPWG_STATS_WG_MONITOR_SHOW_ALLOWED_IPS_NPGATEWAYS_BUFF_LEN - total_bytes_written;
        size_t len = strnlen(ip_addr, NPWG_STATS_WG_MONITOR_SHOW_ALLOWED_IPS_ADDRESS_MASK_STR_LEN);
        if (bytes_available < len + NPWG_STATS_WG_MONITOR_SHOW_ALLOWED_IPS_ADDRESS_MASK_STR_LEN) {
            NPWG_LOG(AL_ERROR, "ERROR wireguard stats : wg buff allowed ips npgateways not enough space to get ");
            wg_stats->allowed_ips_npgateways_get_error_cnt++;
            return;
        }
        bytes_written = snprintf(buff + total_bytes_written, bytes_available, "%s/%s,", ip_addr, mask);
        if (bytes_written < 0) {
            NPWG_LOG(AL_ERROR, "ERROR wireguard stats : wg buff allowed ips npgateways snprintf error ");
            wg_stats->allowed_ips_npgateways_get_error_cnt++;
            return;
        }
        g_npwg_stats_wg_monitor.buff_allowed_ips_npgateways_total_bytes_written += bytes_written;
    }
    // note gathering details about npwg_wireguard_peer_type_invalid is not considered at this time
    return;
}

static int npwg_stats_wg_parse_wg_show_all_dump_allowed_ips_allocate(void)
{
    g_npwg_stats_wg_monitor.buff_allowed_ips_clients = NPWG_MALLOC(NPWG_STATS_WG_MONITOR_SHOW_ALLOWED_IPS_CLIENTS_BUFF_LEN);
    if (g_npwg_stats_wg_monitor.buff_allowed_ips_clients == NULL) {
        NPWG_LOG(AL_NOTICE, "ERROR wireguard stats : wg init allowed ips buff clients got NULL buff\n");
        return NPWG_RESULT_ERR;
    }

    g_npwg_stats_wg_monitor.buff_allowed_ips_npgateways = NPWG_MALLOC(NPWG_STATS_WG_MONITOR_SHOW_ALLOWED_IPS_NPGATEWAYS_BUFF_LEN);
    if (g_npwg_stats_wg_monitor.buff_allowed_ips_npgateways == NULL) {
        NPWG_LOG(AL_NOTICE, "ERROR wireguard stats : wg init allowed ips buff npgateways got NULL buff\n");
        return NPWG_RESULT_ERR;
    }

    g_npwg_stats_wg_monitor.buff_allowed_ips_connectors = NPWG_MALLOC(NPWG_STATS_WG_MONITOR_SHOW_ALLOWED_IPS_CONNECTORS_BUFF_LEN);
    if (g_npwg_stats_wg_monitor.buff_allowed_ips_connectors == NULL) {
        NPWG_LOG(AL_NOTICE, "ERROR wireguard stats : wg init allowed ips buff connectors got NULL buff\n");
        return NPWG_RESULT_ERR;
    }
    return NPWG_RESULT_NO_ERROR;
}

static void npwg_stats_wg_parse_wg_show_all_dump_allowed_ips_free(void)
{
    NPWG_FREE(g_npwg_stats_wg_monitor.buff_allowed_ips_clients);
    g_npwg_stats_wg_monitor.buff_allowed_ips_clients = NULL;
    NPWG_FREE(g_npwg_stats_wg_monitor.buff_allowed_ips_connectors);
    g_npwg_stats_wg_monitor.buff_allowed_ips_connectors = NULL;
    NPWG_FREE(g_npwg_stats_wg_monitor.buff_allowed_ips_npgateways);
    g_npwg_stats_wg_monitor.buff_allowed_ips_npgateways = NULL;
}

static void npwg_stats_wg_parse_wg_show_all_dump_allowed_ips_reset(void)
{
    g_npwg_stats_wg_monitor.buff_allowed_ips_clients_total_bytes_written = 0;
    g_npwg_stats_wg_monitor.buff_allowed_ips_connectors_total_bytes_written = 0;
    g_npwg_stats_wg_monitor.buff_allowed_ips_npgateways_total_bytes_written = 0;
}

static void npwg_stats_wg_parse_wg_show_all_dump_allowed_ips_dump(void)
{
    // dumping wg stats is every 1min, however dumping allowed ips (routes) may create too much output, hence throttling it to once in a while - 2-5min initially
    //if (g_npwg_stats_wg.npgateway_stats_monitor_cnt%NPWG_STATS_WG_MONITOR_SHOW_ALLOWED_IPS_DUMP_EVERY_MINUTES == 0) {

    NPWG_LOG(AL_NOTICE, "allowed ips npgateways: %s", g_npwg_stats_wg_monitor.buff_allowed_ips_npgateways);
    NPWG_LOG(AL_NOTICE, "allowed ips connectors: %s", g_npwg_stats_wg_monitor.buff_allowed_ips_connectors);
    NPWG_LOG(AL_NOTICE, "allowed ips clients: %s", g_npwg_stats_wg_monitor.buff_allowed_ips_clients);

        //TODO for scale configs - limit further by partial display from the buffer (i.e. display only 1000 routes at the time)
    //}
}

static void npwg_stats_wg_parse_wg_show_all_dump_allowed_ips(char *allowed_ips,
                                                             struct npwg_stats_wg *wg_stats,
                                                             enum npwg_wireguard_peer_types peer_type)
{
    if ( wg_stats == NULL || allowed_ips == NULL || strcmp(allowed_ips, "(none)") == 0 ) {
        return;
    }

    // process allowed-ips if any
    // extract ip addr and mask
    // traverse allowed-ips
    // example: ************/24,************/32,************/32

    char *saveptr_ip_addr_mask = NULL;
    char *ip_addr_mask = strtok_r(allowed_ips, ",", &saveptr_ip_addr_mask);
    while (ip_addr_mask != NULL) {

        char *saveptr_ip = NULL;
        char *ip_addr = strtok_r(ip_addr_mask, "/", &saveptr_ip);
        char *mask = strtok_r(NULL, "/", &saveptr_ip);

        if (ip_addr != NULL && mask != NULL) {

            if (peer_type == npwg_wireguard_peer_type_npgateway) {
                wg_stats->allowed_ips_npgateways_cnt++;
            } else if (peer_type == npwg_wireguard_peer_type_client) {
                wg_stats->allowed_ips_clients_cnt++;
            } else if (peer_type == npwg_wireguard_peer_type_connector) {
                wg_stats->allowed_ips_connectors_cnt++;
            } else {
                wg_stats->allowed_ips_invalids_cnt++;
            }
            wg_stats->allowed_ips_total_cnt++;

            // get allowed ips into buff for later dump
            npwg_stats_wg_parse_wg_show_all_dump_allowed_ips_get(ip_addr, mask, peer_type, wg_stats);
        }
        // next ip_addr_mask
        ip_addr_mask = strtok_r(NULL, ",", &saveptr_ip_addr_mask);
    }
}

static void npwg_stats_wg_parse_wg_show_all_dump_latest_handshake(const char *latest_handshake,
                                                                  const int64_t curr_s,
                                                                  struct npwg_stats_wg *wg_stats,
                                                                  enum npwg_wireguard_peer_types peer_type)
{
    if (wg_stats == NULL || latest_handshake == NULL) {
        return;
    }

    int64_t handshake_s = (strtoll(latest_handshake, NULL, 10));

    if (!handshake_s) {
        if (peer_type == npwg_wireguard_peer_type_client) {
            wg_stats->handshake_clients_hist_0++;
        } else if (peer_type == npwg_wireguard_peer_type_connector) {
            wg_stats->handshake_connectors_hist_0++;
        } else if (peer_type == npwg_wireguard_peer_type_npgateway) {
            wg_stats->handshake_npgateways_hist_0++;
        }
        return;
    }

    // convert from utc to time diff since the latest handshake
    handshake_s = curr_s - handshake_s;

    if (handshake_s <= NPWG_STATS_WG_MONITOR_HANDSHAKE_HISTOGRAM_BUCKET_1_S) {
        if (peer_type == npwg_wireguard_peer_type_client) {
            wg_stats->handshake_clients_hist_1++;
        } else if (peer_type == npwg_wireguard_peer_type_connector) {
            wg_stats->handshake_connectors_hist_1++;
        } else if (peer_type == npwg_wireguard_peer_type_npgateway) {
            wg_stats->handshake_npgateways_hist_1++;
        }
    } else {
        if (peer_type == npwg_wireguard_peer_type_client) {
            wg_stats->handshake_clients_hist_2++;
        } else if (peer_type == npwg_wireguard_peer_type_connector) {
            wg_stats->handshake_connectors_hist_2++;
        } else if (peer_type == npwg_wireguard_peer_type_npgateway) {
            wg_stats->handshake_npgateways_hist_2++;
        }
    }
    return;
}

static void npwg_stats_wg_parse_wg_show_all_dump_transfer_rx_tx(const char *transfer_rx, const char *transfer_tx,
                                                                         struct npwg_stats_wg *wg_stats,
                                                                         enum npwg_wireguard_peer_types peer_type)
{
    if (wg_stats == NULL || transfer_rx == NULL || transfer_tx == NULL) {
        return;
    }

    // convert transfer bytes from string
    int64_t transfer_rx_int = (strtoll(transfer_rx, NULL, 10));
    int64_t transfer_tx_int = (strtoll(transfer_tx, NULL, 10));

    if (peer_type == npwg_wireguard_peer_type_npgateway) {
        wg_stats->peers_count_npgateways++;

        wg_stats->rx_npgateways_bytes += transfer_rx_int;
        wg_stats->tx_npgateways_bytes += transfer_tx_int;

    } else if (peer_type == npwg_wireguard_peer_type_client) {
        wg_stats->peers_count_clients++;

        wg_stats->rx_clients_bytes += transfer_rx_int;
        wg_stats->tx_clients_bytes += transfer_tx_int;
    } else if (peer_type == npwg_wireguard_peer_type_connector) {
        wg_stats->peers_count_connectors++;

        wg_stats->rx_connectors_bytes += transfer_rx_int;
        wg_stats->tx_connectors_bytes += transfer_tx_int;
    } else {
        wg_stats->peers_count_invalid++;

        wg_stats->rx_invalids_bytes += transfer_rx_int;
        wg_stats->tx_invalids_bytes += transfer_tx_int;
    }
    wg_stats->rx_total_bytes += transfer_rx_int;
    wg_stats->tx_total_bytes += transfer_tx_int;

    return;
}

static void npwg_stats_wg_parse_wg_show_all_dump_persistent_keepalive(const char *persistent_keepalive,
                                                                               struct npwg_stats_wg *wg_stats)
{
    if (wg_stats == NULL || persistent_keepalive == NULL) {
        return;
    }

    if ( strcmp(persistent_keepalive, "off") == 0 ) {
        wg_stats->persistent_keepalive_off_count++;
    }
    return;
}

static int npwg_stats_ip_route_show_all_dump()
{

    FILE *fp;
    char cmd[150];
    char buffer[256];
    char *token;
    char *saveptr;
    char *start = ip_route_dump_buf;
    const char *end = ip_route_dump_buf + NPWG_STATS_WG_MONITOR_SHOW_ALL_DUMP_BUFF_LEN;
    int bytes_written = 0;

    //reset buffer
    ip_route_dump_buf[0] = '\0';
    snprintf(cmd, sizeof(cmd), "ip route show dev %s", NPWG_INTERFACE_NAME);

    // Open a pipe to run the command
    fp = popen(cmd, "r");
    if (fp == NULL) {
        NPWG_LOG(AL_ERROR,"popen failed");
        return NPWG_RESULT_ERR;
    }

    while (fgets(buffer, sizeof(buffer), fp) != NULL) {
        // Tokenize the first part of the line
        token = strtok_r(buffer, " ", &saveptr);
        if (token != NULL && strchr(token, '.') != NULL) {
            bytes_written = snprintf(start, end - start, "%s,", token);
            // Check if the buffer is large enough to hold the string
            if (bytes_written == (end - start)) {
                NPWG_LOG(AL_ERROR,"Max buffer size for dumping ip routes reached!");
                pclose(fp);
                return NPWG_RESULT_ERR;
            }
            start += bytes_written;
        }
    }

    pclose(fp);

    NPWG_LOG(AL_NOTICE, "Ip routes: %s", ip_route_dump_buf);
    return NPWG_RESULT_NO_ERROR;

}

static int npwg_stats_wg_parse_wg_show_all_dump(char *out_buf, int out_buf_length)
{
    if (!out_buf) {
        NPWG_LOG(AL_ERROR, "ERROR wireguard stats parse show all dump out_buf NULL");
        return NPWG_RESULT_ERR;
    }
    if (out_buf_length < NPWG_STATS_WG_MONITOR_SHOW_ALL_DUMP_BUFF_LEN) {
        NPWG_LOG(AL_ERROR, "ERROR wireguard stats parse show all dump out_buf is too small: %d", out_buf_length);
        return NPWG_RESULT_ERR;
    }

    struct npwg_stats_wg wg_stats = {0};

    //clear buff for collecting allowed ips
    npwg_stats_wg_parse_wg_show_all_dump_allowed_ips_reset();

    /*
     * command: wg show all dump
     * If dump is specified, then several lines are printed;
     * the first contains in order separated by tab:
     * private-key, public-key, listen-port, fwmark.
     * Subsequent lines are printed for each peer and contain in order separated by tab:
     * public-key, preshared-key, endpoint, allowed-ips, latest-handshake, transfer-rx, transfer-tx, persistent-keepalive.
     */

    // split output into lines
    char *line_saveptr;
    char *line = strtok_r(out_buf, "\n", &line_saveptr);

    // verify the first line
    if (line == NULL) {
        NPWG_LOG(AL_ERROR, "ERROR wireguard stats  parse show all dump first line NULL");
        return NPWG_RESULT_ERR;
    }

    // parse the first line
    char *f_saveptr;
    char *f_interface = strtok_r(line, " \t", &f_saveptr);
    strtok_r(NULL, " \t", &f_saveptr); // private key - not store, nor display
    char *f_public_key = strtok_r(NULL, " \t", &f_saveptr);
    char *listen_port = strtok_r(NULL, " \t", &f_saveptr);
    char *fwmark = strtok_r(NULL, " \t", &f_saveptr);

    NPWG_LOG(AL_DEBUG, "this wg config: interface=%s public_key=%s, listen_port=%s, fwmark=%s",
              f_interface, f_public_key, listen_port, fwmark);

    // it is assumed that all gateways are using the same global listenning port , therefore
    // this listen_port is the global listen port for all gateways
    npwg_stats_wg_parse_wg_show_all_dump_listen_port(listen_port, &wg_stats);

    int64_t curr_s = epoch_s();

    // get next line
    line = strtok_r(NULL, "\n", &line_saveptr);

    // for all lines, each per peer
    // Subsequent lines are printed for each peer and contain in order separated by tab:
    // public-key, preshared-key, endpoint, allowed-ips, latest-handshake, transfer-rx, transfer-tx, persistent-keepalive
    while (line != NULL) {

        char *saveptr;
        char *interface = strtok_r(line, " \t", &saveptr);
        char *public_key = strtok_r(NULL, " \t", &saveptr);
        char *preshared_key = strtok_r(NULL, " \t", &saveptr);
        char *endpoint = strtok_r(NULL, " \t", &saveptr);
        char *allowed_ips = strtok_r(NULL, " \t", &saveptr);
        char *latest_handshake = strtok_r(NULL, " \t", &saveptr);
        char *transfer_rx = strtok_r(NULL, " \t", &saveptr);
        char *transfer_tx = strtok_r(NULL, " \t", &saveptr);
        char *persistent_keepalive = strtok_r(NULL, " \t", &saveptr);

        if (!public_key) {
            // get next line
            line = strtok_r(NULL, "\n", &line_saveptr);
            continue;
        }

        npwg_provider provider = (npwg_provider)g_npwg_stats_wg_monitor.config_cookie;
        enum npwg_wireguard_peer_types peer_type;

        if (0 == strcmp(interface, NPWG_INTERFACE_NAME)) {
            peer_type = npwg_provider_get_config_peer_type(provider, public_key);
        } else {
            peer_type = npwg_wireguard_peer_type_connector;
        }

        if (peer_type == npwg_wireguard_peer_type_invalid) {
            NPWG_LOG(AL_ERROR, "NP wireguard stats error invalid peer_type for public key: %s", public_key);
            wg_stats.peer_type_get_err_cnt++;

            // It should not happen, but instead of ignoring it,
            // lets verify if there is any traffic going on this invalid peer wg connection.
        }

        // process allowed-ips if any
        // extract ip addr and mask
        // if mask is 32, then it is assumed peer is client
        // if endpoint port is specific well known it is assumed peer is the npgateway
        // otherwise peer is connector
        npwg_stats_wg_parse_wg_show_all_dump_allowed_ips(allowed_ips,
                                                         &wg_stats,
                                                         peer_type);

        // latest_handshake process
        npwg_stats_wg_parse_wg_show_all_dump_latest_handshake(latest_handshake,
                                                              curr_s,
                                                              &wg_stats,
                                                              peer_type);

        // process transfer_rx, transfer_tx
        npwg_stats_wg_parse_wg_show_all_dump_transfer_rx_tx(transfer_rx, transfer_tx,
                                                            &wg_stats,
                                                            peer_type);

        // persistent_keepalive count
        npwg_stats_wg_parse_wg_show_all_dump_persistent_keepalive(persistent_keepalive,
                                                                  &wg_stats);

        wg_stats.peers_count_total++;

        NPWG_LOG(AL_DEBUG, "parsed peer: interface=%s public_key=%s, preshared_key=%s, endpoint=%s, allowed_ips=%s, "
                  "latest_handshake=%s transfer_rx=%s, transfer_tx=%s, keepalive=%s, epoch_s=%"PRId64", peer_type=%d ",
                  interface, public_key, preshared_key, endpoint, allowed_ips,
                  latest_handshake, transfer_rx, transfer_tx, persistent_keepalive, curr_s, peer_type);

        // get next line
        line = strtok_r(NULL, "\n", &line_saveptr);

    } // end walk per peer

    // lock before update global
    ZPATH_RWLOCK_WRLOCK(&(g_npwg_stats_wg.rw_lock), __FILE__, __LINE__);

    // update this listen_port
    g_npwg_stats_wg.this_wireguard_listen_port = wg_stats.this_wireguard_listen_port;

    // update peers
    g_npwg_stats_wg.peers_count_total = wg_stats.peers_count_total;
    g_npwg_stats_wg.peers_count_npgateways = wg_stats.peers_count_npgateways;
    g_npwg_stats_wg.peers_count_clients = wg_stats.peers_count_clients;
    g_npwg_stats_wg.peers_count_connectors = wg_stats.peers_count_connectors;
    g_npwg_stats_wg.peers_count_invalid = wg_stats.peers_count_invalid;
    if (g_npwg_stats_wg.peers_count_total > g_npwg_stats_wg.peers_count_total_max) {
        g_npwg_stats_wg.peers_count_total_max = wg_stats.peers_count_total;
    }

    // update routes
    g_npwg_stats_wg.allowed_ips_total_cnt = wg_stats.allowed_ips_total_cnt;
    g_npwg_stats_wg.allowed_ips_npgateways_cnt = wg_stats.allowed_ips_npgateways_cnt;
    g_npwg_stats_wg.allowed_ips_clients_cnt = wg_stats.allowed_ips_clients_cnt;
    g_npwg_stats_wg.allowed_ips_connectors_cnt = wg_stats.allowed_ips_connectors_cnt;
    if (g_npwg_stats_wg.allowed_ips_total_cnt > g_npwg_stats_wg.allowed_ips_total_cnt_max) {
        g_npwg_stats_wg.allowed_ips_total_cnt_max = wg_stats.allowed_ips_total_cnt;
    }

    // latest handshake interval
    g_npwg_stats_wg.handshake_clients_hist_0 = wg_stats.handshake_clients_hist_0;
    g_npwg_stats_wg.handshake_clients_hist_1 = wg_stats.handshake_clients_hist_1;
    g_npwg_stats_wg.handshake_clients_hist_2 = wg_stats.handshake_clients_hist_2;
    g_npwg_stats_wg.handshake_connectors_hist_0 = wg_stats.handshake_connectors_hist_0;
    g_npwg_stats_wg.handshake_connectors_hist_1 = wg_stats.handshake_connectors_hist_1;
    g_npwg_stats_wg.handshake_connectors_hist_2 = wg_stats.handshake_connectors_hist_2;
    g_npwg_stats_wg.handshake_npgateways_hist_0 = wg_stats.handshake_npgateways_hist_0;
    g_npwg_stats_wg.handshake_npgateways_hist_1 = wg_stats.handshake_npgateways_hist_1;
    g_npwg_stats_wg.handshake_npgateways_hist_2 = wg_stats.handshake_npgateways_hist_2;

    // bytes per sec approximation, which is more accurate when number of peers is not decreasing dramatically, otherwise
    // there might be measurements error within 1 iteration (1 min).
    // This is due to accounting per total number of clients/connect/gateways.Note we don't account per each peer (too expensive)
    // For example number of peers decreased to 1 from 100. This will cause that rate aprox will go to zero relatively
    // to previous number of peers, although 1 peer still might tx/rx some data.
    // In that case this will converge in the next iteration
    if (wg_stats.rx_total_bytes - g_npwg_stats_wg.rx_total_bytes > 0) {
        g_npwg_stats_wg.rx_total_bytes_per_sec =
            (wg_stats.rx_total_bytes - g_npwg_stats_wg.rx_total_bytes)/NPWG_STATS_WG_MONITOR_S;
    } else {
        g_npwg_stats_wg.rx_total_bytes_per_sec = 0;
    }
    if (wg_stats.tx_total_bytes - g_npwg_stats_wg.tx_total_bytes > 0) {
        g_npwg_stats_wg.tx_total_bytes_per_sec =
            (wg_stats.tx_total_bytes - g_npwg_stats_wg.tx_total_bytes)/NPWG_STATS_WG_MONITOR_S;
    } else {
        g_npwg_stats_wg.tx_total_bytes_per_sec = 0;
    }
    if (wg_stats.rx_npgateways_bytes - g_npwg_stats_wg.rx_npgateways_bytes > 0) {
        g_npwg_stats_wg.rx_npgateways_bytes_per_sec =
            (wg_stats.rx_npgateways_bytes - g_npwg_stats_wg.rx_npgateways_bytes)/NPWG_STATS_WG_MONITOR_S;
    } else {
        g_npwg_stats_wg.rx_npgateways_bytes_per_sec = 0;
    }
    if (wg_stats.tx_npgateways_bytes - g_npwg_stats_wg.tx_npgateways_bytes > 0) {
        g_npwg_stats_wg.tx_npgateways_bytes_per_sec =
            (wg_stats.tx_npgateways_bytes - g_npwg_stats_wg.tx_npgateways_bytes)/NPWG_STATS_WG_MONITOR_S;
    } else {
        g_npwg_stats_wg.tx_npgateways_bytes_per_sec = 0;
    }
    if (wg_stats.rx_clients_bytes - g_npwg_stats_wg.rx_clients_bytes > 0) {
        g_npwg_stats_wg.rx_clients_bytes_per_sec =
            (wg_stats.rx_clients_bytes - g_npwg_stats_wg.rx_clients_bytes)/NPWG_STATS_WG_MONITOR_S;
    } else {
        g_npwg_stats_wg.rx_clients_bytes_per_sec = 0;
    }
    if (wg_stats.tx_clients_bytes - g_npwg_stats_wg.tx_clients_bytes > 0) {
        g_npwg_stats_wg.tx_clients_bytes_per_sec =
            (wg_stats.tx_clients_bytes - g_npwg_stats_wg.tx_clients_bytes)/NPWG_STATS_WG_MONITOR_S;
    } else {
        g_npwg_stats_wg.tx_clients_bytes_per_sec = 0;
    }
    if (wg_stats.rx_connectors_bytes - g_npwg_stats_wg.rx_connectors_bytes > 0) {
        g_npwg_stats_wg.rx_connectors_bytes_per_sec =
            (wg_stats.rx_connectors_bytes - g_npwg_stats_wg.rx_connectors_bytes)/NPWG_STATS_WG_MONITOR_S;
    } else {
        g_npwg_stats_wg.rx_connectors_bytes_per_sec = 0;
    }
    if (wg_stats.tx_connectors_bytes - g_npwg_stats_wg.tx_connectors_bytes > 0) {
        g_npwg_stats_wg.tx_connectors_bytes_per_sec =
            (wg_stats.tx_connectors_bytes - g_npwg_stats_wg.tx_connectors_bytes)/NPWG_STATS_WG_MONITOR_S;
    } else {
        g_npwg_stats_wg.tx_connectors_bytes_per_sec = 0;
    }
    if (wg_stats.rx_invalids_bytes - g_npwg_stats_wg.rx_invalids_bytes > 0) {
        g_npwg_stats_wg.rx_invalids_bytes_per_sec =
        (wg_stats.rx_invalids_bytes - g_npwg_stats_wg.rx_invalids_bytes)/NPWG_STATS_WG_MONITOR_S;
    } else {
        g_npwg_stats_wg.rx_invalids_bytes_per_sec = 0;
    }
    if (wg_stats.tx_invalids_bytes - g_npwg_stats_wg.tx_invalids_bytes > 0) {
        g_npwg_stats_wg.tx_invalids_bytes_per_sec =
            (wg_stats.tx_invalids_bytes - g_npwg_stats_wg.tx_invalids_bytes)/NPWG_STATS_WG_MONITOR_S;
    } else {
        g_npwg_stats_wg.tx_invalids_bytes_per_sec = 0;
    }

    // update transfer bytes
    g_npwg_stats_wg.rx_total_bytes = wg_stats.rx_total_bytes;
    g_npwg_stats_wg.tx_total_bytes = wg_stats.tx_total_bytes;

    g_npwg_stats_wg.rx_npgateways_bytes = wg_stats.rx_npgateways_bytes;
    g_npwg_stats_wg.tx_npgateways_bytes = wg_stats.tx_npgateways_bytes;

    g_npwg_stats_wg.rx_clients_bytes = wg_stats.rx_clients_bytes;
    g_npwg_stats_wg.tx_clients_bytes = wg_stats.tx_clients_bytes;

    g_npwg_stats_wg.rx_connectors_bytes = wg_stats.rx_connectors_bytes;
    g_npwg_stats_wg.tx_connectors_bytes = wg_stats.tx_connectors_bytes;

    g_npwg_stats_wg.rx_invalids_bytes = wg_stats.rx_invalids_bytes;
    g_npwg_stats_wg.tx_invalids_bytes = wg_stats.tx_invalids_bytes;

    g_npwg_stats_wg.persistent_keepalive_off_count = wg_stats.persistent_keepalive_off_count;

    g_npwg_stats_wg.allowed_ips_clients_get_error_cnt = wg_stats.allowed_ips_clients_get_error_cnt;
    g_npwg_stats_wg.allowed_ips_connectors_get_error_cnt = wg_stats.allowed_ips_connectors_get_error_cnt;
    g_npwg_stats_wg.allowed_ips_npgateways_get_error_cnt = wg_stats.allowed_ips_npgateways_get_error_cnt;
    g_npwg_stats_wg.peer_type_get_err_cnt = wg_stats.peer_type_get_err_cnt;
    g_npwg_stats_wg.peer_type_get_err_total_cnt += wg_stats.peer_type_get_err_cnt;

    ZPATH_RWLOCK_UNLOCK(&(g_npwg_stats_wg.rw_lock), __FILE__, __LINE__);

    NPWG_LOG(AL_NOTICE, "wireguard stats: "
              "this_listen_port: %"PRId64", "
              "peers_count: { total: %"PRId64", max: %"PRId64", clients: %"PRId64", conns: %"PRId64", gtws: %"PRId64", invalids: %"PRId64" }, "
              "allowed_ips: { total: %"PRId64", max: %"PRId64", clients: %"PRId64", conns: %"PRId64", gtws: %"PRId64", invalids: %"PRId64" }, "
              "rx_total_bytes: %"PRId64"/s,%"PRId64", tx_total_bytes: %"PRId64"/s,%"PRId64", "
              "clients: { rx: %"PRId64"/s,%"PRId64", tx: %"PRId64"/s,%"PRId64" }, "
              "connectors: { rx: %"PRId64"/s,%"PRId64", tx: %"PRId64"/s,%"PRId64" }, "
              "gateways: { rx: %"PRId64"/s,%"PRId64", tx: %"PRId64"/s,%"PRId64" }, "
              "invalids: { rx: %"PRId64"/s,%"PRId64", tx: %"PRId64"/s,%"PRId64" }, "
              "latest_handshake (hist): { clients: (%"PRId64", %"PRId64", %"PRId64"), connectors: (%"PRId64", %"PRId64", %"PRId64"), gateways: (%"PRId64", %"PRId64", %"PRId64") }, "
              "keepalive_off_count: %"PRId64", "
              "get_error_cnt: { peer_type: %"PRId64", total: %"PRId64" }, "
              "allowed_ips: { clients: %"PRId64", connectors: %"PRId64", gateways: %"PRId64" }, "
              "monitor_cnt: %"PRId64", monitor_err: %"PRId64,
              g_npwg_stats_wg.this_wireguard_listen_port,
              g_npwg_stats_wg.peers_count_total,
              g_npwg_stats_wg.peers_count_total_max,
              g_npwg_stats_wg.peers_count_clients,
              g_npwg_stats_wg.peers_count_connectors,
              g_npwg_stats_wg.peers_count_npgateways,
              g_npwg_stats_wg.peers_count_invalid,
              g_npwg_stats_wg.allowed_ips_total_cnt,
              g_npwg_stats_wg.allowed_ips_total_cnt_max,
              g_npwg_stats_wg.allowed_ips_clients_cnt,
              g_npwg_stats_wg.allowed_ips_connectors_cnt,
              g_npwg_stats_wg.allowed_ips_npgateways_cnt,
              g_npwg_stats_wg.allowed_ips_invalids_cnt,
              g_npwg_stats_wg.rx_total_bytes_per_sec,
              g_npwg_stats_wg.rx_total_bytes,
              g_npwg_stats_wg.tx_total_bytes_per_sec,
              g_npwg_stats_wg.tx_total_bytes,
              g_npwg_stats_wg.rx_clients_bytes_per_sec,
              g_npwg_stats_wg.rx_clients_bytes,
              g_npwg_stats_wg.tx_clients_bytes_per_sec,
              g_npwg_stats_wg.tx_clients_bytes,
              g_npwg_stats_wg.rx_connectors_bytes_per_sec,
              g_npwg_stats_wg.rx_connectors_bytes,
              g_npwg_stats_wg.tx_connectors_bytes_per_sec,
              g_npwg_stats_wg.tx_connectors_bytes,
              g_npwg_stats_wg.rx_npgateways_bytes_per_sec,
              g_npwg_stats_wg.rx_npgateways_bytes,
              g_npwg_stats_wg.tx_npgateways_bytes_per_sec,
              g_npwg_stats_wg.tx_npgateways_bytes,
              g_npwg_stats_wg.rx_invalids_bytes_per_sec,
              g_npwg_stats_wg.rx_invalids_bytes,
              g_npwg_stats_wg.tx_invalids_bytes_per_sec,
              g_npwg_stats_wg.tx_invalids_bytes,
              g_npwg_stats_wg.handshake_clients_hist_0,
              g_npwg_stats_wg.handshake_clients_hist_1,
              g_npwg_stats_wg.handshake_clients_hist_2,
              g_npwg_stats_wg.handshake_connectors_hist_0,
              g_npwg_stats_wg.handshake_connectors_hist_1,
              g_npwg_stats_wg.handshake_connectors_hist_2,
              g_npwg_stats_wg.handshake_npgateways_hist_0,
              g_npwg_stats_wg.handshake_npgateways_hist_1,
              g_npwg_stats_wg.handshake_npgateways_hist_2,
              g_npwg_stats_wg.persistent_keepalive_off_count,
              g_npwg_stats_wg.peer_type_get_err_cnt,
              g_npwg_stats_wg.peer_type_get_err_total_cnt,
              g_npwg_stats_wg.allowed_ips_clients_get_error_cnt,
              g_npwg_stats_wg.allowed_ips_connectors_get_error_cnt,
              g_npwg_stats_wg.allowed_ips_npgateways_get_error_cnt,
              g_npwg_stats_wg.npgateway_stats_monitor_cnt,
              g_npwg_stats_wg.npgateway_stats_monitor_error_cnt);

    // dump allowed ips
    npwg_stats_wg_parse_wg_show_all_dump_allowed_ips_dump();

    return NPWG_RESULT_NO_ERROR;
}

static int npwg_stats_wg_init()
{
    g_npwg_stats_wg.rw_lock = ZPATH_RWLOCK_INIT;

    g_npwg_stats_wg_monitor.buff_out_from_wg = NPWG_MALLOC(NPWG_STATS_WG_MONITOR_SHOW_ALL_DUMP_BUFF_LEN);
    if (g_npwg_stats_wg_monitor.buff_out_from_wg == NULL) {
        NPWG_LOG(AL_NOTICE, "ERROR NP wireguard stats wg init Got NULL buff out\n");
        return NPWG_RESULT_ERR;
    }

    int rc = npwg_stats_wg_parse_wg_show_all_dump_allowed_ips_allocate();
    if (rc) {
        NPWG_LOG(AL_NOTICE, "ERROR NP wireguard stats wg init allowed ips buffs got NULL\n");
        return NPWG_RESULT_ERR;
    }

    return NPWG_RESULT_NO_ERROR;
}

static void npwg_stats_wg_init_free()
{
    NPWG_FREE(g_npwg_stats_wg_monitor.buff_out_from_wg);
    g_npwg_stats_wg_monitor.buff_out_from_wg = NULL;

    npwg_stats_wg_parse_wg_show_all_dump_allowed_ips_free();
}

static void npwg_stats_wg_monitor_heartbeat(void)
{
    if (g_npwg_stats_wg_monitor.monitor_hb_thread) {
        zthread_heartbeat(g_npwg_stats_wg_monitor.monitor_hb_thread);
    }
}

static void npwg_stats_wg_monitor_timer_cb(__attribute__((unused)) evutil_socket_t sock,
                                           __attribute__((unused)) short flags,
                                           __attribute__((unused)) void *cookie)
{
    /* initial heartbeat */
    npwg_stats_wg_monitor_heartbeat();

    // lock before update global
    ZPATH_RWLOCK_WRLOCK(&(g_npwg_stats_wg.rw_lock), __FILE__, __LINE__);
    g_npwg_stats_wg.npgateway_stats_monitor_cnt++;
    ZPATH_RWLOCK_UNLOCK(&(g_npwg_stats_wg.rw_lock), __FILE__, __LINE__);

    NPWG_LOG(AL_NOTICE, "NP wireguard stats wg monitor timer cb cnt: %"PRId64" ",
              g_npwg_stats_wg.npgateway_stats_monitor_cnt);

    int rc = npwg_wg_show_all_dump(g_npwg_stats_wg_monitor.buff_out_from_wg,
                                   NPWG_STATS_WG_MONITOR_SHOW_ALL_DUMP_BUFF_LEN);

    if (rc) {
        ZPATH_RWLOCK_WRLOCK(&(g_npwg_stats_wg.rw_lock), __FILE__, __LINE__);
        g_npwg_stats_wg.npgateway_stats_monitor_error_cnt++;
        ZPATH_RWLOCK_UNLOCK(&(g_npwg_stats_wg.rw_lock), __FILE__, __LINE__);
    }
    npwg_stats_wg_monitor_heartbeat();

    // make sure there is zero at the end before giving it to parsing
    g_npwg_stats_wg_monitor.buff_out_from_wg[NPWG_STATS_WG_MONITOR_SHOW_ALL_DUMP_BUFF_LEN-1] = '\0';

    npwg_stats_wg_parse_wg_show_all_dump(g_npwg_stats_wg_monitor.buff_out_from_wg, NPWG_STATS_WG_MONITOR_SHOW_ALL_DUMP_BUFF_LEN);
    npwg_stats_ip_route_show_all_dump();

    npwg_stats_wg_monitor_heartbeat();
}

static int npwg_stats_wg_monitor_timer_init(struct event_base *base)
{
    // init main timer cb
    g_npwg_stats_wg_monitor.timer = event_new(base, -1, EV_PERSIST, npwg_stats_wg_monitor_timer_cb, NULL);
    if (!g_npwg_stats_wg_monitor.timer) {
        NPWG_LOG(AL_ERROR, "ERROR npgateway stats monitor timer init event_new");
        return NPWG_RESULT_ERR;
    }
    struct timeval tv = { .tv_sec = NPWG_STATS_WG_MONITOR_S, .tv_usec = 0 };
    if (event_add(g_npwg_stats_wg_monitor.timer, &tv)) {
        NPWG_LOG(AL_ERROR, "ERROR npgateway stats monitor timer init event_add");
        return NPWG_RESULT_ERR;
    }
    return NPWG_RESULT_NO_ERROR;
}

static void* npwg_stats_wg_monitor_thread(struct zthread_info *zthread_arg, void *config_cookie)
{
    struct event_base *base = NULL;
    int res;

    /* save heartbeat tickle ctx */
    g_npwg_stats_wg_monitor.monitor_hb_thread = zthread_arg;

    /* save config cookie ptr */
    if (!config_cookie) {
        NPWG_LOG(AL_ERROR, "ERROR NP wireguard stats wg monitor config value is NULL");
        goto fail;
    }
    g_npwg_stats_wg_monitor.config_cookie = config_cookie;

    base = event_base_new();
    if (!base) {
        NPWG_LOG(AL_ERROR, "ERROR NP wireguard stats wg monitor thread base_new");
        goto fail;
    }
    g_npwg_stats_wg_monitor.monitor_thread_base = base;

    res = npwg_stats_wg_monitor_timer_init(base);
    if (res) {
        NPWG_LOG(AL_ERROR, "ERROR NP wireguard stats wg monitor timer init");
        goto fail;
    }

    res = npwg_stats_wg_init();
    if (res) {
        NPWG_LOG(AL_ERROR, "ERROR NP wireguard stats init");
        goto fail;
    }

    /* Run cache timer */
    zevent_base_dispatch(base);

    NPWG_LOG(AL_ERROR, "ERROR NP wireguard stats monitor thread base dispatch");

fail:
    npwg_stats_wg_init_free();
    /* Should watchdog... */
    while (1) {
        sleep(1);
    }
    return NULL;
}

int npwg_stats_wg_monitor_init(npwg_provider provider)
{
    int res = NPWG_RESULT_NO_ERROR;

    /*
     * Monitor will create a monitor thread, this in turn will create timer event base and timer event
     */
    res = zthread_create(&g_npwg_stats_wg_monitor.monitor_thread_id,   // thread
                         npwg_stats_wg_monitor_thread,      // thread func
                         provider,                              // cookie
                         "npwg_stats_wg_monitor_thread",    // thread name
                         NPWG_STATS_WG_MONITOR_HEARTBEAT_TIMEOUT_S,
                         NPWG_STATS_WG_MONITOR_THREAD_STACK_SIZE,
                         MINUTE_TO_US(5),
                         NULL);                             // user void

    if (res) {
        NPWG_LOG(AL_ERROR, "ERROR NP wireguard stats monitor init thread_create ret %s",
                  zpath_result_string(res));
    }

    return res;
}
