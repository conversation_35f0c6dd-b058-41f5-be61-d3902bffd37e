/*
 * npwg_provider.h. Copyright (C) 2024 Zscaler Inc. All Rights Reserved.
 *
 * Provides a simple interface for interaction with the ZPA NP Wireguard library.
 * This is the only header file the users of npwg_lib need to include.
 *
 * This is how to use this library:
 * 1. Create a provider.
 * 2. Ask it to carry out certain actions using wrapper functions.
 * 3. For each action, pass the provider to the wrapper function.
 * 4. The wrapper function calls the real functions, which may call other specialized
 *    functions.
 * 5. If there is any error at any layer, two things happen:
 *  o Error code and context-specific detailed error message are store in
 *    thread-local-storage.
 *  o The error code is also returned by the function, which is returned all-the-way back by
 *    the wrapper function.
 * 6. When the caller of the wrapper function (gateway or connector code) sees the error, it
 *    queries for the detailed error message in thread-local-storage.
 * 7. The caller is free to do whatever it pleases with the error code and error message
 *    including logging it or calling a callback.
 * 8. If there is no error and you just invoked a function that updates the in-memory config,
 * 9. Call npwg_start_wireguard() or npwg_update_wireguard().
 * 10. You should, whenever possible, bunch multiple in-memory updates together and then call
 *    npwg_update_wireguard. For example, bunch multiple calls to npwg_set_gateway_peer() and
 *    then call npwg_update_wireguard().
 * 11. The provider is not thread safe by design - we do not want to waste clock cycles on
 *     locks that can be avoided by not using the provider from multiple threads. Currently
 *     there is no use case for multiple threads using the same provider.
 *
 */

#ifndef _NPWG_PROVIDER_H_
#define _NPWG_PROVIDER_H_

#include "argo/argo.h"
#include "zpath_lib/zpath_app.h"
#include <stdbool.h>

#ifdef __cplusplus
extern "C" {
#endif

#define NPWG_ERROR_MESSAGE_LENGTH           1024
#define NPWG_KEY_LENGTH                       44
#define NPWG_INVALID_KEEPALIVE_INTERVAL_S     -1
#define NPWG_INVALID_PORT                      0

/*
 * Restrict a minimum MTU value 496 per RFC 791 requirement:
 * RFC 791 suggested that all hosts must be prepared to accept datagrams of up to 576 octets[1], if we subtract the worst case wireguard overhead from 576, we get
 * 576 - 80 = 496 bytes, which will be used as min MTU value we allowed user to configure.
 *
 * Restrict a maximum MTU value 65535 per RFC 791 requirement due to the total length is limited to 16 bits in internet header format:
 * "Total Length is the length of the datagram, measured in octets, including internet header and data.  This field allows the length of a datagram to be up to 65,535 octets.
 * Such long datagrams are impractical for most hosts and networks"
 */
#define NPWG_MINIMUM_MTU 496
#define NPWG_MAXIMUM_MTU 65535
#define NPWG_DEFAULT_MTU 1420

#define NPWG_MAX_PEERS                      1024
#define NPWG_ALLOWED_IPS_BUFFER_SIZE        20479   // sufficient routes for MAX_PEERS

/*
 * the buffer we allocate for allowed_ips in npwg_set_peer_config() is NPWG_ALLOWED_IPS_BUFFER_SIZE bytes.
 * considering comma and CIDR lenth, each allowed ip range takes up to 20 bytes
 *
 */
#define NPWG_MAX_ALLOWED_IPS_PER_PEER       (NPWG_ALLOWED_IPS_BUFFER_SIZE / 20)

#define NPWG_INTERFACE_PREFIX                  "npwg"
#define NPWG_INTERFACE_NAME                    "npwg0"
/* NPWG_INTERFACE_DUMMY must be in sync with NPWG_COMMAND_DUMMY and
 * interface (dummy0) in inja_templates/connector.txt and inja_templates/gateway.txt */
#define NPWG_INTERFACE_DUMMY                   "dummy0"
#define NPWG_VRF_NAME                          "vpn_vrf1"
#define NPWG_VRF_TABLE_ID                      "20"
#define NPWG_DEFAULT_ROUTE                     "0.0.0.0/0"
#define NPWG_INVALID_INTERFACE_NAME            "invalid interface name"

#define NPWG_INTERFACE_CONFIG_PATH             "/opt/zscaler/etc/wireguard/npwg0.conf"

#define NPWG_WIREGUARD_NPWG0_STATUS_FILENAME_LOCATION    "np"
#define NPWG_WIREGUARD_NPWG0_STATUS_FULL_PATH_FILENAME   "/opt/zscaler/var/np/npwg0.status"

extern struct argo_log_collection *npwg_event_collection;

#define NPWG_RESULT_NO_ERROR          ZPATH_RESULT_NO_ERROR          /* AKA success */
#define NPWG_RESULT_ERR               ZPATH_RESULT_ERR               /* Generic error, when none other are appropriate */
#define NPWG_RESULT_NOT_FOUND         ZPATH_RESULT_NOT_FOUND         /* Could not find what was requested. */
#define NPWG_RESULT_NO_MEMORY         ZPATH_RESULT_NO_MEMORY         /* Could not allocate memory */
#define NPWG_RESULT_CANT_WRITE        ZPATH_RESULT_CANT_WRITE        /* Failure to write (output callback failed?) */
#define NPWG_RESULT_ERR_TOO_LARGE     ZPATH_RESULT_ERR_TOO_LARGE     /* Requested data doesn't fit in space provided */
#define NPWG_RESULT_BAD_ARGUMENT      ZPATH_RESULT_BAD_ARGUMENT      /* Asked for something wrong. */
#define NPWG_RESULT_INSUFFICIENT_DATA ZPATH_RESULT_INSUFFICIENT_DATA /* Was not provided enough data to perform operation */
#define NPWG_RESULT_NOT_IMPLEMENTED   ZPATH_RESULT_NOT_IMPLEMENTED   /* Yes, for features that are not yet implemented. */
#define NPWG_RESULT_BAD_DATA          ZPATH_RESULT_BAD_DATA          /* Tried to parse data, but format seemed wrong. */
#define NPWG_RESULT_WOULD_BLOCK       ZPATH_RESULT_WOULD_BLOCK       /* Attempting operation would result in blocking. Bad, naughty blocking. */
#define NPWG_RESULT_BAD_STATE         ZPATH_RESULT_BAD_STATE         /* Encountered bad internal state while attempting operation */
#define NPWG_RESULT_INCOMPLETE        ZPATH_RESULT_INCOMPLETE
#define NPWG_RESULT_ASYNCHRONOUS      ZPATH_RESULT_ASYNCHRONOUS
#define NPWG_RESULT_EXCESS_DYN_FIELDS ZPATH_RESULT_EXCESS_DYN_FIELDS /* The RPC has too many dynamic fields */
#define NPWG_RESULT_NOT_READY         ZPATH_RESULT_NOT_READY         /* Requested DB is not ready. */

#define NPWG_RESULT_ERROR_ERROR       (ZPATH_RESULT_NOT_READY + 1)   // Error in the error reporting subsystem.
#define NPWG_RESULT_BAD_CFG           (ZPATH_RESULT_NOT_READY + 2)   // Bad config file.
#define NPWG_RESULT_BAD_CFG_PARAM     (ZPATH_RESULT_NOT_READY + 3)   // Bad config in config file.
#define NPWG_RESULT_SYSCALL_FAILED    (ZPATH_RESULT_NOT_READY + 4)   // System call failed.
#define NPWG_RESULT_CHILD_HUNG        (ZPATH_RESULT_NOT_READY + 5)   // Child process went into a hang.
#define NPWG_RESULT_WG_USER_ERROR     (ZPATH_RESULT_NOT_READY + 6)   // Wireguard user-land error.
#define NPWG_RESULT_WOULD_OVERFLOW    (ZPATH_RESULT_NOT_READY + 7)   // Potential buffer overflow.
#define NPWG_RESULT_EXEC_ERROR        (ZPATH_RESULT_NOT_READY + 8)   // Execution error.

extern uint64_t npwg_debug_log;
extern const char *npwg_debug_log_names[];

#define NPWG_DEBUG_BIT                BIT_FLAG_U64(0)

#define NPWG_DEBUG_LOG_NAMES {               \
        "npwg_debug",                        \
        NULL                                 \
        }

#define NPWG_LOG(priority, format...) ARGO_LOG(npwg_event_collection, priority, "npwg", ##format)
#define NPWG_DEBUG_COND_LOG(condition, format...) \
        ARGO_DEBUG_LOG(condition, npwg_event_collection, argo_log_priority_debug, "npwg", ##format)
#define NPWG_DEBUG_LOG(format...) NPWG_DEBUG_COND_LOG(npwg_debug_log & NPWG_DEBUG_BIT, ##format)

enum npwg_wireguard_peer_types {
    npwg_wireguard_peer_type_invalid = -1,
    npwg_wireguard_peer_type_npgateway = 1,
    npwg_wireguard_peer_type_client = 2,
    npwg_wireguard_peer_type_connector = 3
};

int npwg_provider_log_init(struct argo_log_collection *event_log);

struct npwg_provider_impl;
typedef struct npwg_provider_impl *npwg_provider;

/**
 * @brief Create NP Wireguard provider.
 *
 * @return npwg_provider           Handle to NP Wireguard provider
 *
 */
npwg_provider npwg_create_provider();

/**
 * @brief Destroy NP Wireguard provider created by npwg_create_provider().
 *        This frees up memory allocated for the provider.
 *
 * @param [in, out] provider       Pointer to NPWG provider handle. Can't be NULL.
 *                                 The handle is created using npwg_create_provider().
 *
 */
void npwg_destroy_provider(npwg_provider *provider);

/**
 * @brief set/update in-memory interface configuration.
 *        Wrapper for npwg_set_interface_config_impl().
 *        This only updates the in-memory configuration.
 *        Call npwg_update_wireguard() to update the Wireguard driver.
 *
 * @param [in] provider            NPWG provider handle. Can't be NULL. Created using
 *                                 npwg_create_provider().
 * @param [in] private_key         Wireguard base64 encoded private key, NUL terminated
 *                                 c-string. Can't be NULL.
 * @param [in] listen_port         Well known port. Optional. Set to NPWG_INVALID_PORT if
 *                                 not used.
 *
 * @return int                     NPWG_RESULT_NO_ERROR on success,
 *                                 other NPWG_RESULT_* codes otherwise.
 *
 */
int npwg_set_interface_config(npwg_provider provider,
                              const char *private_key,
                              uint16_t listen_port);

/**
 * @brief Add one peer to the in-memory configuration / update existing peer.
 *        Wrapper for npwg_set_peer_config_impl().
 *        This only updates the in-memory configuration.
 *        Call npwg_update_wireguard() to update the Wireguard driver.
 *
 * @param [in] provider            NPWG provider handle. Can't be NULL. Created using
 *                                 npwg_create_provider().
 * @param [in] public_key          Wireguard base64 encoded public key, NUL terminated
 *                                 c-string. Can't be NULL.
 * @param [in] ip                  Well known IP of peer. Optional. If NULL, both ip
 *                                 and port are ignored.
 * @param [in] port                Well known port of peer. Optional. Must be a valid port if
 *                                 ip is not NULL.
 * @param [in] cidrs               Destination subnets accessible through this peer. Optional.
 *                                 Set to NULL if not used.
 * @param [in] cidr_count          Count of subnets, Optional. Ignored if cidrs is NULL.
 * @param [in] keepalive_interval  Persistent keepalive interval. Optional. Set to
 *                                 NPWG_INVALID_KEEPALIVE_INTERVAL_S to disable keepalive.
 * @param [in] peer_type           A component which owns the public_key.
 *
 * @note  ip, port and cidrs are optional, but make sure to provide at least one of them.
 *
 * @return int                     NPWG_RESULT_SUCCESS on success,
 *                                 NPWG_RESULT_ERROR otherwise.
 */
int npwg_set_peer_config(npwg_provider provider,
                         const char *public_key,
                         const struct argo_inet *ip,
                         uint16_t port,
                         const struct argo_inet *cidrs,
                         size_t cidr_count,
                         int keepalive_interval,
                         enum npwg_wireguard_peer_types peer_type);

/**
 * @brief Delete an existing peer.
 *        This only updates the in-memory configuration.
 *        Call npwg_update_wireguard() to update the Wireguard driver.
 *        Wrapper for npwg_delete_peer_config_impl().
 *
 * @param [in] provider            NPWG provider handle. Can't be NULL. Created using
 *                                 npwg_create_provider().
 * @param [in] public_key          Wireguard base64 encoded public key, NUL terminated
 *                                 c-string. Can't be NULL.
 *
 * @return int                     NPWG_RESULT_SUCCESS on success,
 *                                 NPWG_RESULT_ERROR otherwise.
 *
 */
int npwg_delete_peer_config(npwg_provider provider,
                            const char *public_key);

/**
 * @brief Set a peer that is an NP Client. You may add/update a peer using this.
 *        This only updates the in-memory configuration.
 *        Call npwg_update_wireguard() to update the Wireguard driver.
 *        Called by NP Gateway.
 *
 * @param [in] provider            NPWG provider handle. Can't be NULL. Created using
 *                                 npwg_create_provider().
 * @param [in] public_key          Wireguard base64 encoded public key, NUL terminated
 *                                 c-string. Can't be NULL.
 * @param [in] client_ip           IP address of client. Can't be NULL.
 *
 * @return int                     NPWG_RESULT_NO_ERROR on success,
 *                                 other NPWG_RESULT_* codes otherwise.
 */
int npwg_set_client_peer(npwg_provider provider,
                         const char *public_key,
                         const struct argo_inet *client_ip);

/**
 * @brief Set a peer that is an NP Connector. You may add/update a peer using this.
 *        This only updates the in-memory configuration.
 *        Call npwg_update_wireguard() to update the Wireguard driver.
 *        Called by NP Gateway.
 *
 * @param [in] provider            NPWG provider handle. Can't be NULL. Created using
 *                                 npwg_create_provider().
 * @param [in] public_key          Wireguard base64 encoded public key, NUL terminated
 *                                 c-string. Can't be NULL.
 * @param [in] lan_cidrs           Array of argo_inet structures of LAN CIDRs. May be NULL.
 * @param [in] cidr_count          Count of LAN CIDRs if lan_cidrs is not NULL.
 * @param [in] updates_pending     requires timer update to perform wg set on peer update
 *
 * @return int                     NPWG_RESULT_NO_ERROR on success,
 *                                 other NPWG_RESULT_* codes otherwise.
 */
int npwg_set_connector_peer(npwg_provider provider,
                            const char *public_key,
                            const struct argo_inet *lan_cidrs,
                            size_t cidr_count,
                            bool updates_pending);
/**
 * @brief Set a peer that is an NP Gateway. You may add/update a peer using this.
 *        This only updates the in-memory configuration.
 *        Call npwg_update_wireguard() to update the Wireguard driver.
 *        Called by NP Connector and NP Gateway.
 *
 * @note  If this method were to be called by an NP Client (Linux NP Client!), the
 *        remote_cidrs will have to be replaced by the union of ALL LAN CIDRs of all
 *        NP Connector peers and ALL remote CIDRs of all NP Gateways.
 *
 * @note  ip, port and cidrs are optional, but make sure to provide at least one of them.
 *
 * @param [in] provider            NPWG provider handle. Can't be NULL. Created using
 *                                 npwg_create_provider().
 * @param [in] public_key          Wireguard base64 encoded public key, NUL terminated
 *                                 c-string. Can't be NULL.
 * @param [in] ip                  Well-known IP of gateway tunnel listener. Optional.
 * @param [in] port                Well-known port of gateway tunnel listener. Optional.
 * @param [in] remote_cidrs        Array of argo_inet structures of remote CIDRs. Optional.
 * @param [in] cidr_count          Count of remote CIDRs. Optional.
 *
 * @return int                     NPWG_RESULT_NO_ERROR on success,
 *                                 other NPWG_RESULT_* codes otherwise.
 */
int npwg_set_gateway_peer(npwg_provider provider,
                          const char *public_key,
                          const struct argo_inet *ip,
                          uint16_t port,
                          const struct argo_inet *remote_cidrs,
                          size_t cidr_count);

/**
 * @brief Create a Wireguard VRF and populate it with a default route
 *
 * @param [in] provider            NPWG provider handle. Can't be NULL. Created using
 *                                 npwg_create_provider().
 *
 * @return int                     NPWG_RESULT_NO_ERROR on success,
 *                                 other NPWG_RESULT_* codes otherwise.
 */
int npwg_wireguard_vrf(npwg_provider provider);

/**
 * @brief Start Wireguard driver.
 *
 * @param [in] provider            NPWG provider handle. Can't be NULL. Created using
 *                                 npwg_create_provider().
 *
 * @return int                     NPWG_RESULT_NO_ERROR on success,
 *                                 other NPWG_RESULT_* codes otherwise.
 */
int npwg_start_wireguard(npwg_provider provider);

/**
 * @brief Update Wireguard driver. This must be preceded by updates to the in-memory driver
 *        configuration. For example, use npwg_set_gateway_peer() one or more times before
          calling npwg_update_wiregurard().
 *
 * @param [in] provider            NPWG provider handle. Can't be NULL. Created using
 *                                 npwg_create_provider().
 *
 * @return int                     NPWG_RESULT_NO_ERROR on success,
 *                                 other NPWG_RESULT_* codes otherwise.
 */
int npwg_update_wireguard(npwg_provider provider, bool dont_configure_linux_routes);

/**
 * @brief Stop Wireguard driver.
 *
 * @param [in] provider            NPWG provider handle. Can't be NULL. Created using
 *                                 npwg_create_provider().
 *
 * @return int                     NPWG_RESULT_NO_ERROR on success,
 *                                 other NPWG_RESULT_* codes otherwise.
 */

// NOTE: store NPWG_INTERFACE_NAME in provider, use that here
int npwg_stop_wireguard(npwg_provider provider);

// stop whole wireguard, not speicifc to interface
int npwg_delete_wireguard_interface();

/**
 * @brief write in-memory configuration to INI file. See the beginning of this file for the
 *        format of the INI file. Do not use in production.
 *
 * @param [in] provider            NPWG provider handle. Can't be NULL. Created using
 *                                 npwg_create_provider().
 * @param [in] config_file         Path to config file.
 *
 * @return int                     NPWG_RESULT_SUCCESS on success,
 *                                 NPWG_RESULT_ERROR otherwise.
 */
int npwg_write_config_file(npwg_provider provider, const char *config_file);

/**
 * @brief generate wireguard keys
 *
 * @note  This does not require the provider handle.
 *
 * @param [out] private_key        Wireguard base64 encoded private key; NUL terminated
 *                                 c-string. Must be able to hold NPWG_KEY_LENGTH + 1.
 *                                 Can't be NULL.
 * @param [out] private_key_length Private key buffer length. Must be greater than
 *                                 NPWG_KEY_LENGTH.
 * @param [out] public_key         Wireguard base64 encoded public key. Must be able to hold
 *                                 NPWG_KEY_LENGTH + 1. Can't be NULL.
 * @param [out] public_key_length  Public key buffer length. Must be greater than
 *                                 NPWG_KEY_LENGTH.
 *
 * @return int                     NPWG_RESULT_NO_ERROR on success,
 *                                 other NPWG_RESULT_* codes otherwise.
 */
int npwg_gen_keys(char *private_key, int private_key_length,
                  char *public_key, int public_key_length);

/**
 * @brief validate wireguard public key given the private key
 *
 * @param [in] private_key         Wireguard base64 encoded private key, NUL terminated
 *                                 c-string. Can't be NULL.
 * @param [in] public_key          Wireguard base64 encoded public key, NUL terminated
 *                                 c-string. Can't be NULL.
 *
 * @return int                     NPWG_RESULT_NO_ERROR on success,
 *                                 other NPWG_RESULT_* codes otherwise.
 */
int npwg_validate_keys(const char *private_key, const char *public_key);


/**
 * @brief Parse configuration file in INI format and store it in in-memory config. See the
 *        beginning of this file for the format of the INI file.
 *
 * @param [in] provider            NPWG provider handle. Can't be NULL. Created using
 *                                 npwg_create_provider().
 * @param [in] config_file         Path to config file.
 *
 * @return int                     NPWG_RESULT_NO_ERROR on success,
 *                                 other NPWG_RESULT_* codes otherwise.
 */
int npwg_read_config_file(npwg_provider provider, const char *config_file);

/**
 * @brief show-dump current runtime wireguard information for NPWG_INTERFACE_NAME interfaces
 *
 * @param [out] out_buf            NULL terminated c-string.
 *                                 Can't be NULL.
 * @param [out] out_buf_length     out buffer length.
 * @return int                     NPWG_RESULT_NO_ERROR on success,
 *                                 other NPWG_RESULT_* codes otherwise.
 */
int npwg_wg_show_all_dump(char *out_buf, ssize_t out_buf_length);

/*
 * @brief return the size of current peer_map
 *
 * @param [in] provider            NPWG provider handle. Can't be NULL. Created using
 *                                 npwg_create_provider().
 *
 * @return int                     existing peer_map size
 */
size_t np_provider_get_config_peer_size(npwg_provider provider);

/*
 * @brief reroute wireguard logs to event logs
 *
 */
void npwg_provider_monitor_wg_log();


/**
 * @brief write wg status(0 / 1) locally to a file
 *
 * @param [in] status wg running status where 0 indicate not running, 1 means running
 */
void npwg_provider_update_wg_status_file(int status);

 /*
 * @brief return type of peer identified by a given public key
 *
 * @param [in] provider                 NPWG provider handle. Can't be NULL. Created using
 *                                      npwg_create_provider().
 * @param [in] public_key               Wireguard base64 encoded public key, NUL terminated
 *                                      c-string. Can't be NULL.
 *
 * @return npwg_wireguard_peer_types    A peer type identified by the public_key.
 *                                      npwg_wireguard_peer_type_invalid if no peer found in the provider.
 */
 enum npwg_wireguard_peer_types npwg_provider_get_config_peer_type(npwg_provider provider, const char *public_key);

 /**
 * @brief setting new mtu for NPWG_INTERFACE_NAME
 *
 * @param [in] mtu value
 *
 * @return int  NPWG_RESULT_NO_ERROR on success,
 *              other NPWG_RESULT_* codes otherwise.
 */
int npwg_provider_set_mtu(npwg_provider provider, int mtu);

/**
 * @brief get NPC wg interface name info (redundancy mode)
 *
 * @param [in] gid                 NPC gid
 * @param [out] out_buf            interface name output buffer
 * @param [out] out_buf_len        interface name output buffer length
 */
void npwg_provider_redundancy_wg_interface_name(int64_t connector_gid, char *out_buf, size_t out_buf_len);

/**
 * @brief set NPC wg interface info
 *
 * @param [in] provider            NPC wg interface handler
 * @param [in] gid                 NPC gid
 * @param [in] gid                 NPC port
 *
 * @return int                     NPWG_RESULT_NO_ERROR on success,
 *                                 other NPWG_RESULT_* codes otherwise.
 */
int npwg_provider_set_provider_meta(npwg_provider provider,
                                    int64_t gid);


/**
 * @brief link provider interface to master vrf
 *
 * @param [in] provider            NPC wg interface handler
 * @param [in] vrf_name            vrf table name to link

 * @return int                     NPWG_RESULT_NO_ERROR on success,
 *                                 other NPWG_RESULT_* codes otherwise.
 */
int npwg_link_wg_interface_to_vrf(npwg_provider provider, const char* vrf_name);

/**
 * @brief Create an wireguard interface using ip link add linux command
 *
 * @param [in] provider            NPC wg interface handler
 * @param [in] add                 1 - add,
 *                                 0 - del
 *
 * @return int                     NPWG_RESULT_NO_ERROR on success,
 *                                 other NPWG_RESULT_* codes otherwise.
 */
int npwg_provider_ip_link_add_delete_npwg_intf(npwg_provider provider, int add);


/**
 * @brief Set the wireguard interface UP ip link set up linux command
 *
 * @param [in] provider            NPC wg interface handler
 * @param [in] up                  1 - UP,
 *                                 0 = DOWN
 *
 * @return int                     NPWG_RESULT_NO_ERROR on success,
 *                                 other NPWG_RESULT_* codes otherwise.
 */
int npwg_provider_ip_link_set_up_down_npwg_intf(npwg_provider provider, int up);

/**
 * @brief wgset conf command
 *
 * @param [in] provider            NPC wg interface handler

 * @return int                     NPWG_RESULT_NO_ERROR on success,
 *                                 other NPWG_RESULT_* codes otherwise.
 */
int npwg_provider_wg_setconf_nwpg_intf(npwg_provider provider);


/**
 * @brief check if the speicified interface already exist, if exist
 *        if exist, bring down and delete
 *
 * @param [in] provider            NPC wg interface handler

 * @return int                     NPWG_RESULT_NO_ERROR on success,
 *                                 other NPWG_RESULT_* codes otherwise.
 */
int npwg_check_and_delete_interface(npwg_provider provider);

char* npwg_provider_return_interface_name(npwg_provider provider);

/*
 * Bring up interface NPWG_INTERFACE_DUMMY so the class E IP can be assigned there
 * Used by NP Redundancy feature
 */
int npwg_provider_setup_interface_dummy(int is_gateway);

#ifdef __cplusplus
} // extern "C"
#endif

#endif  /* _NPWG_PROVIDER_H_ */
