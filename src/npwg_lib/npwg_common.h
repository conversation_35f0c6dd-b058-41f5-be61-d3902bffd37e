/*
 * npwg_common.h. Copyright (C) 2024 Zscaler Inc. All Rights Reserved.
 *
 * Provides common functionality for intra-library artifacts.
 *
 */

#ifndef _NPWG_COMMON_H_
#define _NPWG_COMMON_H_

#include "zpath_misc/zpath_misc.h"

#ifdef __cplusplus
extern "C" {
#endif

extern struct zpath_allocator npwg_allocator;
#define NPWG_MALLOC(x)    zpath_malloc(&npwg_allocator, x, __LINE__, __FILE__)
#define NPWG_CALLOC(x)    zpath_calloc(&npwg_allocator, x, __LINE__, __FILE__)
#define NPWG_STRDUP(x, y) zpath_strdup(&npwg_allocator, x, y, __LINE__, __FILE__)
#define NPWG_FREE(x)      zpath_free(x, __LINE__, __FILE__)

/**
 * @brief Trim leading and trailing whitespace from a string.
 *
 * @param [in] str                 c-string to be trimmed. Can't be NULL.
 * @param [in] max_length          Maximum possible size of the string buffer
 *                                 including the terminating '\0'. If the length
 *                                 of the string is greater than max_lenght, the
 *                                 string won't be trimmed.
 *
 * @return int                     The input string, trimmed.
 *
 */
char *trim_white_spaces(char *str, int max_length);


/**
 * @brief Trim all the white spaces within a str.
 *
 * @param [in] str                 c-string to be trimmed with white space. Can't be NULL.
 * @param [in] result              c-string output that the final result can be stored. Can't be NULL.
 *
 *
 */
void npwg_remove_spaces_copy(const char *str, char *result);

#define     NPWG_COMMAND_GENKEY     "genkey"
#define     NPWG_COMMAND_PUBKEY     "pubkey"
#define     NPWG_COMMAND_UP         "up"
#define     NPWG_COMMAND_DOWN       "down"
#define     NPWG_COMMAND_SYNCCONF   "syncconf"
#define     NPWG_COMMAND_SHOW       "show"
#define     NPWG_COMMAND_ALL        "all"
#define     NPWG_COMMAND_DUMP       "dump"
#define     NPWG_COMMAND_SET        "set"
#define     NPWG_COMMAND_SETCONF    "setconf"
#define     NPWG_COMMAND_SET_OPTION_PEER         "peer"
#define     NPWG_COMMAND_SET_OPTION_ENDPOINT     "endpoint"
#define     NPWG_COMMAND_SET_OPTION_ALLOWED_IPS  "allowed-ips"
#define     NPWG_COMMAND_SET_OPTION_KEEPALIVE    "persistent-keepalive"
#define     NPWG_COMMAND_SET_OPTION_REMOVE       "remove"

#define     NPWG_KMSG_RING_BUFFER   "/dev/kmsg"

#define     NPWG_COMMAND_VRF        "vrf"
#define     NPWG_COMMAND_IP         "ip"
#define     NPWG_COMMAND_ADD        "add"
#define     NPWG_COMMAND_DEL        "del"
#define     NPWG_COMMAND_DEV        "dev"
#define     NPWG_COMMAND_LINK       "link"
#define     NPWG_COMMAND_TYPE       "type"
#define     NPWG_COMMAND_ROUTE      "route"
#define     NPWG_COMMAND_TABLE      "table"
#define     NPWG_COMMAND_MASTER     "master"
#define     NPWG_COMMAND_WIREGUARD  "wireguard"
/* NPWG_COMMAND_DUMMY must be in sync with NPWG_INTERFACE_DUMMY and
 * interface (dummy0) in inja_templates/connector.txt and inja_templates/gateway.txt */
#define     NPWG_COMMAND_DUMMY      "dummy"


#ifdef __cplusplus
} // extern "C"
#endif

#endif  /* _NPWG_COMMON_H_ */
