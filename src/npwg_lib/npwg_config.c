/*
 * npwg_config.c. Copyright (C) 2024 Zscaler Inc. All Rights Reserved.
 */

#include "npwg_lib/npwg_config.h"

#include "argo/argo.h"
#include "npwg_lib/npwg_common.h"
#include "npwg_lib/npwg_net_util.h"
#include "npwg_lib/npwg_provider.h"

#include <stdbool.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include "zpath_lib/zpath_debug.h"
#include <sys/stat.h>

static struct argo_structure_description *npwg_config_stats_description;

static struct npwg_config_stats{                        /* _ARGO: * object_definition */
        int64_t fail_to_store_pending_config;           /* _ARGO: integer */
        int64_t alloc_pending_config;                   /* _ARGO: integer */
        int64_t free_pending_config;                    /* _ARGO: integer */
} stats;
#include "npwg_lib/npwg_config_compiled_c.h"

npwg_config *npwg_create_config()
{
    npwg_config *config = NPWG_CALLOC(sizeof(npwg_config));
    if (!config) {
        NPWG_LOG(AL_ERROR, "Failed to allocate memory for in-memory config.");
        return NULL;
    }
    // set an invalid listen_port, this must be explicitly overridden
    // if this config is for a wireguard listener
    config->interface_config.listen_port = NPWG_INVALID_PORT;
    config->peer_map = zhash_table_alloc(&npwg_allocator);
    if (!config->peer_map) {
        NPWG_LOG(AL_ERROR, "Failed to allocate memory for peer map.");
        NPWG_FREE(config);
        return NULL;
    }

    config->route_journal = zhash_table_alloc(&npwg_allocator);
    if (!config->route_journal) {
        NPWG_LOG(AL_ERROR, "Failed to allocate memory for route journal.");
        NPWG_FREE(config->route_journal);
        NPWG_FREE(config);
        return NULL;
    }

    config->updated_peers_config = zhash_table_alloc(&npwg_allocator);
    if (!config->updated_peers_config) {
        NPWG_LOG(AL_ERROR, "Failed to allocate memory for updated peers config");
        NPWG_FREE(config->peer_map);
        NPWG_FREE(config->route_journal);
        NPWG_FREE(config);
        return NULL;
    }

    return config;
}

// is used to destroy both routes entries and route journal entries
// routes are per peer where as route journal is the pending routing
// changes for all peers
static void destroy_route_entry_f(void *element,
                                  __attribute__((unused)) void *cookie)
{
    cidr_bool_pair_t *cidr_bool_pair = element;
    NPWG_LOG(AL_INFO, "Destroying route entry: %p, CIDR: %s",
             cidr_bool_pair, cidr_bool_pair->ip_with_mask);

    NPWG_FREE(cidr_bool_pair);
}

// The ownership of cidr_bool_pair gets transferred here
// it either gets stored in the routes journal
// or it gets freed. The caller should not use cidr_bool_pair
// post the call.
static int set_route_del_in_route_journal(struct zhash_table *route_journal,
                                          const char *ip_with_mask,
                                          size_t len,
                                          cidr_bool_pair_t *cidr_bool_pair)
{
    cidr_bool_pair_t *route_journal_entry = zhash_table_lookup(route_journal,
                                                               ip_with_mask,
                                                               len,
                                                               NULL);

    if (route_journal_entry) {
        NPWG_LOG(AL_INFO, "found route_journal_entry: %p, flag: %d",
                 route_journal_entry, route_journal_entry->scratch_pad);
        // if route deletion is already in the route journal
        if (!route_journal_entry->scratch_pad) {
            // cidr_bool_pair is guaranteed to be not NULL when this method is called
            // deletion of cidr_bool_pair is in two places because ip_with_mask is a
            // member of cidr_bool_pair. do not rearrange.
            NPWG_FREE(cidr_bool_pair);
            return NPWG_RESULT_NO_ERROR;
        }

        // deletion and addition cancel each other out
        NPWG_LOG(AL_INFO,
                 "removing route_journal entry: %p, for: %s",
                 route_journal_entry,
                 ip_with_mask);
        zhash_table_remove(route_journal, ip_with_mask, len, NULL);
        NPWG_FREE(route_journal_entry);
        NPWG_FREE(cidr_bool_pair);
        return NPWG_RESULT_NO_ERROR;
    }
    NPWG_LOG(AL_INFO, "found no route_journal entry for: %s", ip_with_mask);

    // transfer of ownership of cidr_bool_pair to the route journal
    route_journal_entry = cidr_bool_pair;

    NPWG_LOG(AL_INFO,
             "storing route_journal entry: %p, for: %s",
             route_journal_entry,
             ip_with_mask);
    int rc = zhash_table_store(route_journal,
                               route_journal_entry->ip_with_mask,
                               len,
                               1,
                               route_journal_entry);

    if (rc) {
        NPWG_LOG(AL_ERROR, "Failed to store route journal entry.");
        NPWG_LOG(AL_INFO, "deleting route_journal_entry: %p", route_journal_entry);
        NPWG_FREE(route_journal_entry);
        return NPWG_RESULT_NO_MEMORY;
    }

    // mark route for deletion
    route_journal_entry->scratch_pad = false;
    return NPWG_RESULT_NO_ERROR;
}

static int set_route_add_in_route_journal(struct zhash_table *route_journal,
                                          const char *ip_with_mask,
                                          size_t len)
{
    cidr_bool_pair_t *route_journal_entry =
        zhash_table_lookup(route_journal,
                           ip_with_mask,
                           len,
                           NULL);

    if (route_journal_entry) {
        NPWG_LOG(AL_INFO, "Found route_journal_entry: %p, flag: %d",
                 route_journal_entry,
                 route_journal_entry->scratch_pad);
        // if route addition is already in the route journal
        if (route_journal_entry->scratch_pad) {
            return NPWG_RESULT_NO_ERROR;
        }
        // deletion and addition cancel each other out
        NPWG_LOG(AL_INFO,
                 "removing route_journal entry: %p, for: %s",
                 route_journal_entry,
                 ip_with_mask);
        zhash_table_remove(route_journal, ip_with_mask, len, NULL);
        NPWG_LOG(AL_INFO, "deleting route_journal_entry: %p", route_journal_entry);
        NPWG_FREE(route_journal_entry);
        return NPWG_RESULT_NO_ERROR;
    }
    NPWG_LOG(AL_INFO, "found no route_journal entry for: %s", ip_with_mask);

    route_journal_entry = NPWG_CALLOC(sizeof(cidr_bool_pair_t));
    if (!route_journal_entry) {
        NPWG_LOG(AL_ERROR, "Failed to allocate memory for route_journal_entry.");
        return NPWG_RESULT_NO_MEMORY;
    }

    // nul termination thanks to calloc()
    snprintf(route_journal_entry->ip_with_mask,
             sizeof(route_journal_entry->ip_with_mask),
             "%s",
             ip_with_mask);
    NPWG_LOG(AL_INFO,
             "storing route_journal entry: %p, for: %s",
             route_journal_entry,
             ip_with_mask);
    int rc = zhash_table_store(route_journal,
                               route_journal_entry->ip_with_mask,
                               len,
                               1,
                               route_journal_entry);

    if (rc) {
        NPWG_LOG(AL_ERROR, "Failed to store route journal entry.");
        return NPWG_RESULT_NO_MEMORY;
    }

    // mark route for addition
    route_journal_entry->scratch_pad = true;
    return NPWG_RESULT_NO_ERROR;
}

// this is called only when a peer is deleted
static int routes_delete_f(void *cookie,
                           void *object,
                           __attribute__((unused)) void *key,
                           size_t key_len)
{
    npwg_config *config = cookie;
    cidr_bool_pair_t *cidr_bool_pair = object;

    NPWG_LOG(AL_INFO,
             "Adding route: %s to route journal for route deletion",
             cidr_bool_pair->ip_with_mask);

    int rc = set_route_del_in_route_journal(config->route_journal,
                                            cidr_bool_pair->ip_with_mask,
                                            key_len,
                                            cidr_bool_pair);

    if (!rc) return rc;

    NPWG_LOG(AL_ERROR,
             "Failed to add route: %s to route journal for route deletion",
             cidr_bool_pair->ip_with_mask);
    NPWG_FREE(cidr_bool_pair);
    return rc;
}

static void destroy_npwg_peer_cb(void *element, void *cookie)
{
    NPWG_LOG(AL_INFO, "peer_config = %p", element);
    npwg_peer_config *peer_config = element;
    npwg_config *config = cookie;
    if (peer_config) {
        if (peer_config->routes) {
            zhash_table_walk(peer_config->routes, NULL, routes_delete_f, cookie);
            zhash_table_free(peer_config->routes);
            peer_config->routes = NULL;
        }

        NPWG_PEER_MAP_WRLOCK(config);
        zhash_table_remove(config->peer_map,
                           peer_config->public_key,
                           NPWG_KEY_LENGTH,
                           NULL);
        NPWG_FREE(peer_config);
        NPWG_PEER_MAP_UNLOCK(config);
    }
}

void npwg_destroy_config(npwg_config **config)
{
    zhash_table_flush_and_call((*config)->peer_map, destroy_npwg_peer_cb, *config);
    zhash_table_free((*config)->peer_map);
    zhash_table_free((*config)->route_journal);
    zhash_table_free((*config)->updated_peers_config);
    NPWG_FREE(*config);
    *config = NULL;
}

static bool is_valid_key(const char *key)
{
    return NPWG_KEY_LENGTH == strnlen(key, NPWG_KEY_LENGTH + 1);
}


static bool is_valid_ipv4_addr(const char *ip)
{
    struct argo_inet inet = {0};
    return ((argo_string_to_inet(ip, &inet) == ARGO_RESULT_NO_ERROR) &&
           (inet.length == 4) && (inet.netmask == 32));
}

static bool is_valid_port_number(int port_number)
{
    return (port_number >= MIN_VALID_PORT_NUMBER && port_number <= MAX_VALID_PORT_NUMBER);
}

// NOTE: validate better, allowed_ips is a combination of IPs and CIDRs
static bool is_valid_allowed_ips_v4(const char *allowed_ips)
{
    struct argo_inet inet = {0};
    return (argo_string_to_inet(allowed_ips, &inet) == ARGO_RESULT_NO_ERROR && inet.length == 4);
}

static bool is_valid_keepalive_interval(int keepalive_interval)
{
    return (keepalive_interval >= MIN_KEEPALIVE_INTERVAL_S &&
            keepalive_interval <= MAX_KEEPALIVE_INTERVAL_S);
}


static int store_updated_config(struct zhash_table *pending_config_map,
                                const char *public_key,
                                int do_add)
{
    int rc = NPWG_RESULT_NO_ERROR;

    if (!pending_config_map) {
        NPWG_LOG(AL_ERROR, "invalid pending_config");
        return NPWG_RESULT_BAD_ARGUMENT;
    }

    if (!public_key) {
        NPWG_LOG(AL_ERROR, "invalid public_key input for peer config");
        return NPWG_RESULT_BAD_ARGUMENT;
    }

    npwg_updated_peers_config *pending_config = NULL;

    pending_config = zhash_table_lookup(pending_config_map,
                                        public_key,
                                        NPWG_KEY_LENGTH,
                                        NULL);

    if (pending_config) {
        //already in the pending_config map, we dont have to add it again
        //but before we return, confirm pending_config->do_add matches the latest requirement, and set it correctly
        if (pending_config->do_add != do_add) {
            NPWG_LOG(AL_INFO, "peer config %s : changed from %s to %s",
                     public_key,
                     pending_config->do_add?"add":"remove",
                     do_add?"add":"remove");
            pending_config->do_add = do_add;
        }

        return NPWG_RESULT_NO_ERROR;
    }

    pending_config = NPWG_CALLOC(sizeof(npwg_updated_peers_config));
    if (!pending_config) {
        NPWG_LOG(AL_ERROR, "No Memory allocating pending_config");
        return NPWG_RESULT_NO_MEMORY;
    }
    stats.alloc_pending_config++;

    pending_config->do_add = do_add;

    memcpy(pending_config->public_key, public_key, NPWG_KEY_LENGTH);
    pending_config->public_key[NPWG_KEY_LENGTH] = '\0';

    rc = zhash_table_store(pending_config_map,
                           pending_config->public_key,
                           strnlen(public_key, NPWG_KEY_LENGTH),
                           1,
                           pending_config);

    if (rc) {
        NPWG_FREE(pending_config);
        stats.free_pending_config++;
        stats.fail_to_store_pending_config++;
    }

    return rc;
}

int npwg_set_interface_config_impl(npwg_config *config,
                                   const char *private_key,
                                   uint16_t listen_port)
{
    if (!config || !config->peer_map) {
        NPWG_LOG(AL_ERROR, "Invalid config was provided.");
        return NPWG_RESULT_BAD_ARGUMENT;
    }

    if (!private_key || !is_valid_key(private_key)) {
        NPWG_LOG(AL_ERROR, "Invalid private key was provided.");
        return NPWG_RESULT_BAD_ARGUMENT;
    }

    // listen_port is optional, but, if specified, must be valid
    if (NPWG_INVALID_PORT != listen_port && !is_valid_port_number(listen_port)) {
        NPWG_LOG(AL_ERROR, "Invalid listen port was provided.");
        return NPWG_RESULT_BAD_ARGUMENT;
    }

    memcpy(config->interface_config.private_key, private_key, NPWG_KEY_LENGTH);
    config->interface_config.private_key[NPWG_KEY_LENGTH] = '\0';
    config->interface_config.listen_port = listen_port;

    return NPWG_RESULT_NO_ERROR;
}

// note: returns whatever zhash_table_store returns
static int store_peer_config_in_peer_map(npwg_config *config,
                                         npwg_peer_config *peer_config)
{

    if (peer_config->public_key[0] == '\0') {
        NPWG_LOG(AL_WARNING, "Error: critical peer section config missing public key.");
        return NPWG_RESULT_BAD_ARGUMENT;
    }

    NPWG_PEER_MAP_WRLOCK(config);
    int rc = zhash_table_store(config->peer_map,
                               peer_config->public_key,
                               (int)strnlen(peer_config->public_key, NPWG_KEY_LENGTH),
                               1,
                               peer_config);
    NPWG_PEER_MAP_UNLOCK(config);

    if (!rc) return NPWG_RESULT_NO_ERROR;
    if (ZHASH_RESULT_NO_MEMORY == rc) {
        NPWG_LOG(AL_ERROR, "Failed to store peer info.");
        return NPWG_RESULT_NO_MEMORY;
    }

    NPWG_LOG(AL_ERROR, "Failed to store peer info. zhash error: %d", rc);
    return NPWG_RESULT_ERR;
}

static npwg_peer_config *allocate_peer_config()
{
    npwg_peer_config *peer_config = NPWG_CALLOC(sizeof(npwg_peer_config));
    if (!peer_config) return NULL;
    peer_config->routes = zhash_table_alloc(&npwg_allocator);
    if (!peer_config->routes) {
        NPWG_LOG(AL_ERROR, "Failed to allocate memory for peer_config routes.");
        NPWG_FREE(peer_config);
        return NULL;
    }

    return peer_config;
}

typedef struct routes_walk_context
{
    struct zhash_table *routes;
    struct zhash_table *route_journal;
    const char *public_key;
} routes_walk_context_t;

// go through a peer's routes, find routes to be deleted and set all scratch pads to false
static int routes_walk_f(void *cookie,
                         void *object,
                         __attribute__((unused)) void *key,
                         size_t key_len)
{
    // note: do null check on object and cookie
    cidr_bool_pair_t *cidr_bool_pair = object;
    routes_walk_context_t *routes_walk_context = cookie;
    NPWG_LOG(AL_INFO,
             "Public_key: %s, route: %s, scratch_pad: %d, key_len: %lu",
             routes_walk_context->public_key,
             cidr_bool_pair->ip_with_mask,
             cidr_bool_pair->scratch_pad,
             key_len);

    // this entry has not changed, no action is required
    if (cidr_bool_pair->scratch_pad) {
        cidr_bool_pair->scratch_pad = false;
        return ZHASH_RESULT_NO_ERROR;
    }

    // this entry needs to be deleted
    int rc = zhash_table_remove(routes_walk_context->routes,
                                cidr_bool_pair->ip_with_mask,
                                key_len,
                                NULL);
    if (rc) {
        NPWG_LOG(AL_ERROR, "Failed to remove cidr_bool_pair.");
        NPWG_FREE(cidr_bool_pair);
        return rc;
    }

    return set_route_del_in_route_journal(routes_walk_context->route_journal,
                                          cidr_bool_pair->ip_with_mask,
                                          key_len,
                                          cidr_bool_pair);
}

static int routes_print_f(void *cookie,
                          void *object,
                          __attribute__((unused)) void *key,
                          __attribute__((unused)) size_t key_len)
{
    const char *context = cookie;
    cidr_bool_pair_t *cidr_bool_pair = object;
    NPWG_LOG(AL_INFO, "Context: %s, route: %s, scratch_pad: %d",
             context, cidr_bool_pair->ip_with_mask, cidr_bool_pair->scratch_pad);
    return ZHASH_RESULT_NO_ERROR;
}

static void print_peer_routes(struct zhash_table *routes,
                              const char *public_key,
                              char *context)
{
    char buf[256];
    snprintf(buf, sizeof(buf), "%s, public_key: %s", context, public_key);
    zhash_table_walk(routes, NULL, routes_print_f, buf);
}

static void print_route_journal(struct zhash_table *route_journal, char *context)
{
    zhash_table_walk(route_journal, NULL, routes_print_f, context);
}

// local function, no error checking is done
static int store_routes_and_update_route_journal(struct zhash_table *routes,
                                                 struct zhash_table *route_journal,
                                                 cidr_bool_pair_t **cidr_bool_pairs,
                                                 size_t cidr_count,
                                                 const char *public_key)
{
    NPWG_LOG(AL_INFO, "public_key: %s, cidr_count: %zu", public_key, cidr_count);
    print_peer_routes(routes, public_key, "before store_routes_and_update_route_journal()");
    print_route_journal(route_journal, "before store_routes_and_update_route_journal()");
    int rc = NPWG_RESULT_NO_ERROR;
    for (size_t ii = 0; ii < cidr_count; ++ii) {
        char *ip_with_mask = cidr_bool_pairs[ii]->ip_with_mask;
        size_t len = strnlen(ip_with_mask, NPWG_ARGO_INET_STR_MAX_LENGTH);
        cidr_bool_pair_t *cidr_bool_pair =
            zhash_table_lookup(routes,
                               ip_with_mask,
                               len,
                               NULL);
        if (!cidr_bool_pair) {
            NPWG_LOG(AL_INFO, "cidr_bool_pair was not found, public_key: %s, ip_with_mask: %s",
                     public_key, ip_with_mask);
            cidr_bool_pairs[ii]->scratch_pad = true;
            rc = zhash_table_store(routes,
                                   ip_with_mask,
                                   len,
                                   1,
                                   cidr_bool_pairs[ii]);
            if (rc) {
                NPWG_LOG(AL_ERROR, "Failed to store cidr_bool_pair.");
                return NPWG_RESULT_NO_MEMORY;
            }

            rc = set_route_add_in_route_journal(route_journal, ip_with_mask, len);

            if (rc) {
                NPWG_LOG(AL_ERROR,
                         "Failed to mark route: %s for addition.",
                         ip_with_mask);
                return rc;
            }
        }
        else {
            NPWG_LOG(AL_INFO, "cidr_bool_pair was found, public_key: %s, ip_with_mask: %s, flag: %d",
                     public_key, cidr_bool_pair->ip_with_mask, cidr_bool_pair->scratch_pad);
            cidr_bool_pair->scratch_pad = true;
            NPWG_FREE(cidr_bool_pairs[ii]);
        }
    }

    routes_walk_context_t routes_walk_context = {routes, route_journal, public_key};
    rc = zhash_table_walk(routes, NULL, routes_walk_f, &routes_walk_context);
    print_route_journal(route_journal, "after store_routes_and_update_route_journal()");
    print_peer_routes(routes, public_key, "after store_routes_and_update_route_journal()");
    return rc;
}

static void free_cidr_bool_pairs(cidr_bool_pair_t **cidr_bool_pairs, const size_t cidr_count)
{
    for (size_t ii = 0; ii < cidr_count; ++ii) {
        NPWG_FREE(cidr_bool_pairs[ii]);
    }
}

static cidr_bool_pair_t **allocate_cidr_bool_pairs(const size_t cidr_count)
{
    static cidr_bool_pair_t *cidr_bool_pairs[1024];
    int ii = 0;
    for (; ii < cidr_count; ++ii) {
        cidr_bool_pairs[ii] = NPWG_CALLOC(sizeof(cidr_bool_pair_t));
        if (!cidr_bool_pairs[ii]) {
            free_cidr_bool_pairs(cidr_bool_pairs, ii);
            return NULL;
        }
    }
    return cidr_bool_pairs;
}

static int npwg_argo_inets_to_comma_separated_ip_netmasks(const struct argo_inet *cidrs,
                                                          size_t cidr_count,
                                                          char *allowed_ips,
                                                          struct zhash_table *routes,
                                                          struct zhash_table *route_journal,
                                                          const char *public_key)
{
    NPWG_LOG(AL_INFO, "public_key: %s, cidr_count: %zu", public_key, cidr_count);
    if (4 != cidrs[0].length) {
        NPWG_LOG(AL_ERROR, "Only IPv4 is supported.");
        return NPWG_RESULT_BAD_ARGUMENT;
    }

    int bytes_available = NPWG_ALLOWED_IPS_BUFFER_SIZE;
    char ip_with_mask[NPWG_ARGO_INET_STR_MAX_LENGTH + 1];
    int bytes_written = 0;
    int total_bytes_written = 0;

    cidr_bool_pair_t **cidr_bool_pairs = allocate_cidr_bool_pairs(cidr_count);
    if (!cidr_bool_pairs) {
        NPWG_LOG(AL_ERROR, "Failed to allocate memory for cidr_bool_pairs.");
        return NPWG_RESULT_NO_MEMORY;
    }

    for (size_t ii = 0; ii < cidr_count; ++ii) {
        if (4 != cidrs[ii].length) {
            NPWG_LOG(AL_ERROR, "Only IPv4 is supported.");
            free_cidr_bool_pairs(cidr_bool_pairs, cidr_count);
            return NPWG_RESULT_BAD_ARGUMENT;
        }

        // need space for at least one CIDR block + ", "
        if (bytes_available < NPWG_ARGO_INET_STR_MAX_LENGTH + 2) {
            NPWG_LOG(AL_ERROR, "Allowed IPs too long.");
            free_cidr_bool_pairs(cidr_bool_pairs, cidr_count);
            return NPWG_RESULT_WOULD_OVERFLOW;
        }
        // note: optimize
        if (0 == ii) {
            bytes_written = snprintf(allowed_ips + total_bytes_written,
                                     bytes_available, "%s",
                                     argo_inet_generate(ip_with_mask, cidrs + ii));
        }
        else {
            bytes_written = snprintf(allowed_ips + total_bytes_written,
                                     bytes_available, ", %s",
                                     argo_inet_generate(ip_with_mask, cidrs + ii));
        }
        total_bytes_written += bytes_written;
        bytes_available -= bytes_written;

        snprintf(cidr_bool_pairs[ii]->ip_with_mask,
                 sizeof(cidr_bool_pairs[ii]->ip_with_mask),
                 "%s",
                 ip_with_mask);
    }
    NPWG_LOG(AL_INFO, "Allowed IPs: %s", allowed_ips);

    int rc = store_routes_and_update_route_journal(routes,
                                                   route_journal,
                                                   cidr_bool_pairs,
                                                   cidr_count,
                                                   public_key);

    // do not call free_cidr_bool_pairs() here. Some of the allocated pairs are
    // likely owned by others at this point.
    return rc;
}

static inline bool npwg_peer_config_is_peer_type_valid(enum npwg_wireguard_peer_types peer_type)
{
    return (peer_type == npwg_wireguard_peer_type_npgateway ||
            peer_type == npwg_wireguard_peer_type_client ||
            peer_type == npwg_wireguard_peer_type_connector);
}

int npwg_set_peer_config_impl(npwg_config *config,
                              const char *public_key,
                              const char *ip,
                              uint16_t port,
                              const struct argo_inet *cidrs,
                              size_t cidr_count,
                              int keepalive_interval,
                              enum npwg_wireguard_peer_types peer_type)
{
    if (!config || !config->peer_map) {
        NPWG_LOG(AL_ERROR, "Invalid config was provided.");
        return NPWG_RESULT_BAD_ARGUMENT;
    }

    if (!public_key || !is_valid_key(public_key)) {
        NPWG_LOG(AL_ERROR, "Invalid public key was provided.");
        return NPWG_RESULT_BAD_ARGUMENT;
    }

    // ip is optional, but, if specified, must be valid
    if (ip && !is_valid_ipv4_addr(ip)) {
        NPWG_LOG(AL_ERROR, "Invalid peer IP was provided.");
        return NPWG_RESULT_BAD_ARGUMENT;
    }

    // port is optional, but, if specified, must be valid
    if (port != NPWG_INVALID_PORT && !is_valid_port_number(port)) {
        NPWG_LOG(AL_ERROR, "Invalid listen port was provided.");
        return NPWG_RESULT_BAD_ARGUMENT;
    }

    // keepalive_interval is optional, but, if specified, must be valid
    if (keepalive_interval != INVALID_KEEPALIVE_INTERVAL_S &&
        !is_valid_keepalive_interval(keepalive_interval)) {
        NPWG_LOG(AL_ERROR, "Invalid keepalive was provided.");
        return NPWG_RESULT_BAD_ARGUMENT;
    }
    if (!npwg_peer_config_is_peer_type_valid(peer_type)) {
        NPWG_LOG(AL_ERROR, "Invalid peer_type was provided.");
        return NPWG_RESULT_BAD_ARGUMENT;
    }

    // find out if the peer is already configured
    NPWG_PEER_MAP_RDLOCK(config);
    npwg_peer_config *peer_config = zhash_table_lookup(config->peer_map,
                                                       public_key,
                                                       NPWG_KEY_LENGTH,
                                                       NULL);
    NPWG_PEER_MAP_UNLOCK(config);

    bool is_new_peer = !peer_config;
    if (is_new_peer) {
        peer_config = allocate_peer_config();
        if (!peer_config) {
            NPWG_LOG(AL_ERROR, "Failed to allocate peer config.");
            return NPWG_RESULT_NO_MEMORY;
        }
        memcpy(peer_config->public_key, public_key, NPWG_KEY_LENGTH);
        peer_config->public_key[NPWG_KEY_LENGTH] = '\0';
    }

    if (ip) {
        memcpy(peer_config->ip, ip, MAX_IP_LENGTH);
        peer_config->ip[MAX_IP_LENGTH] = '\0';
    }

    peer_config->port = port;

    int rc = NPWG_RESULT_NO_ERROR;
    NPWG_LOG(AL_INFO, "About to process CIDRs, cidr_count: %zu", cidr_count);
    if (cidrs && cidr_count) {
        for (int ii = 0; ii < cidr_count; ++ii) {
            char buf[64];
            NPWG_LOG(AL_INFO, "public_key: %s, ip_with_mask: %s", public_key, argo_inet_generate(buf, cidrs + ii));
        }
        rc = npwg_argo_inets_to_comma_separated_ip_netmasks(cidrs,
                                                            cidr_count,
                                                            peer_config->allowed_ips,
                                                            peer_config->routes,
                                                            config->route_journal,
                                                            public_key);
        if (rc) {
            destroy_npwg_peer_cb(peer_config, config);
            return rc;
        }
    }

    peer_config->keepalive_interval = keepalive_interval;
    peer_config->peer_type = peer_type;

    store_updated_config(config->updated_peers_config,
                         public_key,
                         1);

    if (!is_new_peer) return NPWG_RESULT_NO_ERROR;

    rc = store_peer_config_in_peer_map(config, peer_config);
    if (!rc) return NPWG_RESULT_NO_ERROR;

    destroy_npwg_peer_cb(peer_config, config);
    store_updated_config(config->updated_peers_config,
                         public_key,
                         0);
    return rc;
}

int npwg_delete_peer_config_impl(npwg_config *config, const char *public_key)
{
    if (!config) {
        NPWG_LOG(AL_ERROR, "Invalid config.");
        return NPWG_RESULT_BAD_ARGUMENT;
    }
    if (!public_key || !is_valid_key(public_key)) {
        NPWG_LOG(AL_ERROR, "Invalid public key was provided.");
        return NPWG_RESULT_BAD_ARGUMENT;
    }

    NPWG_PEER_MAP_RDLOCK(config);
    npwg_peer_config *peer_config = zhash_table_lookup(config->peer_map,
                                                       public_key,
                                                       NPWG_KEY_LENGTH,
                                                       NULL);
    NPWG_PEER_MAP_UNLOCK(config);

    if (!peer_config) {
        NPWG_LOG(AL_INFO, "Peer %s, not yet in peer-map.", public_key);
        return NPWG_RESULT_NO_ERROR;
    }

    store_updated_config(config->updated_peers_config,
                         public_key,
                         0);

    destroy_npwg_peer_cb(peer_config, config);
    return NPWG_RESULT_NO_ERROR;
}

static void cleanup_parse_npwg_config_file(npwg_config *config,
                                           npwg_peer_config **peer_config, FILE **fp)
{
    if (fp && *fp) {
        fclose(*fp);
        *fp = NULL;
    }

    if (!config) return;

    if (peer_config && *peer_config) {
        destroy_npwg_peer_cb(*peer_config, config);
        *peer_config = NULL;
    }

    if (config->peer_map) {
        zhash_table_flush_and_call(config->peer_map, destroy_npwg_peer_cb, config);
        config->peer_map = NULL;
    }
}

static int parse_peer_section(npwg_config *config, const char *key, const char *value,
                              npwg_peer_config *peer_config, const char config_file[],
                              int line_number)
{
    if (!config || !key || !value || !peer_config) {
        NPWG_LOG(AL_ERROR,
                 "config: %p\t, key: %p\t, value: %p\t, peer_config: %p",
                 config,
                 key,
                 value,
                 peer_config);

        return NPWG_RESULT_BAD_ARGUMENT;
    }

    if (0 == strcasecmp(key, PUBLIC_KEY_FIELD)) {
        if (!is_valid_key(value)) {
            NPWG_LOG(AL_ERROR, "Error: malformed config @ %s:%d", config_file, line_number);
            return NPWG_RESULT_BAD_CFG_PARAM;
        }
        memcpy(peer_config->public_key, value, NPWG_KEY_LENGTH);
        peer_config->public_key[NPWG_KEY_LENGTH] = '\0';
    }
    else if (0 == strcasecmp(key, ENDPOINT_FIELD)) {
        char *colon_loc = strchr(value, ':');
        if (!colon_loc) {
            NPWG_LOG(AL_ERROR, "Error: malformed config @ %s:%d", config_file, line_number);
            return NPWG_RESULT_BAD_CFG_PARAM;
        }
        *colon_loc = '\0';
        if (!is_valid_ipv4_addr(value)) {
            NPWG_LOG(AL_ERROR, "Error: malformed config @ %s:%d", config_file, line_number);
            return NPWG_RESULT_BAD_CFG_PARAM;
        }
        memcpy(peer_config->ip, value, MAX_IP_LENGTH);
        peer_config->ip[MAX_IP_LENGTH] = '\0';
        // NOTE: atoi() does not detect errors, fix.
        int port_number = atoi(colon_loc + 1);
        if (!is_valid_port_number(port_number)) {
            NPWG_LOG(AL_ERROR, "Error: malformed config @ %s:%d", config_file, line_number);
            return NPWG_RESULT_BAD_CFG_PARAM;
        }
        peer_config->port = (uint16_t)port_number;
    }
    else if (0 == strcasecmp(key, ALLOWED_IPS_FIELD)) {
        if (!is_valid_allowed_ips_v4(value)) {
            NPWG_LOG(AL_ERROR, "Error: malformed config @ %s:%d", config_file, line_number);
            return NPWG_RESULT_BAD_CFG_PARAM;
        }
        size_t len = strnlen(value, MAX_CONFIG_LINE_SIZE);
        memcpy(peer_config->allowed_ips, value, len);
        peer_config->allowed_ips[len] = '\0';
    }
    else if (0 == strcasecmp(key, PKEEPALIVE_FIELD)) {
        int keepalive_interval = atoi(value);
        if (!is_valid_keepalive_interval(keepalive_interval)) {
            NPWG_LOG(AL_ERROR, "Error: malformed config @ %s:%d", config_file, line_number);
            return NPWG_RESULT_BAD_CFG_PARAM;
        }
        peer_config->keepalive_interval = keepalive_interval;
    }
    else {
        NPWG_LOG(AL_ERROR, "Error: malformed config @ %s:%d", config_file, line_number);
        return NPWG_RESULT_BAD_CFG_PARAM;
    }
    return NPWG_RESULT_NO_ERROR;
}

static int parse_interface_section(npwg_config *config, const char *key, const char *value,
                                  const char config_file[], int line_number)
{
    if (!config || !key || !value) {
        NPWG_LOG(AL_ERROR, "config: %p\t, key: %p\t, value: %p", config, key, value);
        return NPWG_RESULT_BAD_ARGUMENT;
    }
    if (0 == strcasecmp(key, PRIVATE_KEY_FIELD)) {
        if (!is_valid_key(value)) {
            NPWG_LOG(AL_ERROR, "Error: malformed config @ %s:%d", config_file, line_number);
            return NPWG_RESULT_BAD_CFG_PARAM;
        }
        memcpy(config->interface_config.private_key, value, NPWG_KEY_LENGTH);
        config->interface_config.private_key[NPWG_KEY_LENGTH] = '\0';
    }
    else if (0 == strcasecmp(key, LISTEN_PORT_FIELD)) {
        int port_number = atoi(value);
        if (!is_valid_port_number(port_number)) {
            NPWG_LOG(AL_ERROR, "Error: malformed config @ %s:%d", config_file, line_number);
            return NPWG_RESULT_BAD_CFG_PARAM;
        }
        config->interface_config.listen_port = (uint16_t)port_number;
    }
    else {
        NPWG_LOG(AL_ERROR, "Error: malformed config @ %s:%d", config_file, line_number);
        return NPWG_RESULT_BAD_CFG_PARAM;
    }
    return NPWG_RESULT_NO_ERROR;
}

int npwg_parse_config_file(const char *config_file, npwg_config *config)
{
    if (!config) {
        NPWG_LOG(AL_ERROR, "NULL config was provided.");
        return NPWG_RESULT_BAD_ARGUMENT;
    }

    // Open the config file for reading
    FILE *fp = fopen(config_file, "r");
    if (!fp) {
        char error_buffer[256];
        strerror_r(errno, error_buffer, sizeof(error_buffer));
        NPWG_LOG(AL_ERROR, "Failed to open config file, %s for reading. Error: %s", config_file, error_buffer);
        return NPWG_RESULT_BAD_CFG;
    }

    /*
     * FIXME:
     * npwg_parse_config_file is not used in prod code, if we do want to use,
     * remove the static usage, incremental reading using small buffers
     */
    static char config_line[MAX_CONFIG_LINE_SIZE + 1];
    int line_number = 0;
    npwg_peer_config *peer_config = NULL;
    enum { SECTION_TYPE_NONE, SECTION_TYPE_INTERFACE, SECTION_TYPE_PEER }
        section_type = SECTION_TYPE_NONE;

    // NOTE: handle the case where the line is larger than MAX_CONFIG_LINE_SIZE
    while (fgets(config_line, sizeof(config_line), fp)) {
        ++line_number;
        char *line = trim_white_spaces(config_line, MAX_CONFIG_LINE_SIZE);
        // skip empty lines and comments
        if (!*line || line[0] == '#') continue;

        int line_length = (int)strnlen(line, MAX_CONFIG_LINE_SIZE + 1);
        if (line[0] == '[' && line[line_length - 1] == ']') {
            // if we are already processing a peer section
            if (SECTION_TYPE_PEER == section_type) {
                // this is a bit counter-intuitive, peer_config is allocated further down
                int rc = store_peer_config_in_peer_map(config, peer_config);
                if (rc) {
                    cleanup_parse_npwg_config_file(config, &peer_config, &fp);
                    return rc;
                }
                // Owership of peer_config is transferred to zhash.
                peer_config = NULL;
            }
            // if the section we just started processing is an INTERFACE section
            if (0 == strncasecmp(line + 1, INTERFACE_SECTION, line_length - 2)) {
                section_type = SECTION_TYPE_INTERFACE;
            }
            // if the section we just started processing is a PEER section
            else if (0 == strncasecmp(line + 1, PEER_SECTION, line_length - 2)) {
                section_type = SECTION_TYPE_PEER;
                peer_config = allocate_peer_config();
                if (!peer_config) {
                    cleanup_parse_npwg_config_file(config, &peer_config, &fp);
                    NPWG_LOG(AL_ERROR, "Failed to allocate peer config.");
                    return NPWG_RESULT_NO_MEMORY;
                }
                // set to invalid values, these must be reset explicitly
                peer_config->port = NPWG_INVALID_PORT;
                peer_config->keepalive_interval = INVALID_KEEPALIVE_INTERVAL_S;
            }
            else {
                cleanup_parse_npwg_config_file(config, &peer_config, &fp);
                NPWG_LOG(AL_ERROR, "Error: malformed config @ %s:%d", config_file, line_number);
                return NPWG_RESULT_BAD_CFG_PARAM;
            }
            continue;
        }
        else {
            if (SECTION_TYPE_NONE == section_type) {
                cleanup_parse_npwg_config_file(config, &peer_config, &fp);
                NPWG_LOG(AL_ERROR, "Error: malformed config @ %s:%d", config_file, line_number);
                return NPWG_RESULT_BAD_CFG_PARAM;
            }
            char *equal_loc = strchr(line, '=');
            if (!equal_loc) {
                cleanup_parse_npwg_config_file(config, &peer_config, &fp);
                NPWG_LOG(AL_ERROR, "Error: malformed config @ %s:%d", config_file, line_number);
                return NPWG_RESULT_BAD_CFG_PARAM;
            }
            *equal_loc = '\0';
            const char *key = trim_white_spaces(line, MAX_INI_KEY_SIZE + 1);
            const char *value = trim_white_spaces(equal_loc + 1, MAX_INI_VALUE_SIZE + 1);
            if (SECTION_TYPE_INTERFACE == section_type) {
                int rc = parse_interface_section(config, key, value, config_file, line_number);
                if (rc) {
                    cleanup_parse_npwg_config_file(config, &peer_config, &fp);
                    return rc;
                }
            }
            else {
                int rc = parse_peer_section(config, key, value, peer_config,
                                        config_file, line_number);
                if (rc) {
                    cleanup_parse_npwg_config_file(config, &peer_config, &fp);
                    return rc;
                }
            }
        }
    }
    if (SECTION_TYPE_PEER == section_type) {
        int rc = store_peer_config_in_peer_map(config, peer_config);
        if (rc) {
            cleanup_parse_npwg_config_file(config, &peer_config, &fp);
            return rc;
        }
        peer_config = NULL;
    }
    fclose(fp);
    return NPWG_RESULT_NO_ERROR;
}

static int write_npwg_peer_one_section_f(void *cookie,
                                         void *object,
                                         __attribute__((unused)) void *key,
                                         __attribute__((unused)) size_t key_len)
{
    npwg_peer_config *peer_config = object;
    FILE *fp = cookie;

    if (fprintf(fp, "[%s]\n", PEER_SECTION) < 0) {
        NPWG_LOG(AL_ERROR, "Failed to write peer section header.");
        return NPWG_RESULT_CANT_WRITE;
    }

    if (fprintf(fp, PUBLIC_KEY_FIELD " = %s\n", peer_config->public_key) < 0) {
        NPWG_LOG(AL_ERROR, "Peer: %s, Failed to write PublicKey field.", peer_config->public_key);
        return NPWG_RESULT_CANT_WRITE;
    }

    if (peer_config->ip[0] && NPWG_INVALID_PORT != peer_config->port) {
        if (fprintf(fp, ENDPOINT_FIELD " = %s:%d\n", peer_config->ip, peer_config->port) < 0) {
            NPWG_LOG(AL_ERROR, "Peer: %s, Failed to write EndPoint field.", peer_config->public_key);
            return NPWG_RESULT_CANT_WRITE;
        }
    }

    if (peer_config->allowed_ips[0]) {
        if (fprintf(fp, ALLOWED_IPS_FIELD " = %s\n", peer_config->allowed_ips) < 0) {
            NPWG_LOG(AL_ERROR, "Peer: %s, Failed to write AllowedIPs field.", peer_config->public_key);
            return NPWG_RESULT_CANT_WRITE;
        }
    }

    if (INVALID_KEEPALIVE_INTERVAL_S != peer_config->keepalive_interval) {
        if (fprintf(fp, PKEEPALIVE_FIELD " = %d\n", peer_config->keepalive_interval) < 0) {
            NPWG_LOG(AL_ERROR, "Peer %s, Failed to write PersistentKeepalive field.", peer_config->public_key);
            return NPWG_RESULT_CANT_WRITE;
        }
    }

    return NPWG_RESULT_NO_ERROR;
}


static int write_npwg_interface_section(FILE *fp, const npwg_config *config)
{
    if (config->interface_config.private_key[0] == '\0') {
        NPWG_LOG(AL_ERROR, "Critical interface section config private key is missing.");
        return NPWG_RESULT_BAD_STATE;
    }

    if (fprintf(fp, "[%s]\n", INTERFACE_SECTION) < 0) {
        NPWG_LOG(AL_ERROR, "Failed to write interface section header.");
        return NPWG_RESULT_CANT_WRITE;
    }

    if (fprintf(fp, PRIVATE_KEY_FIELD " = %s\n", config->interface_config.private_key) < 0) {
        NPWG_LOG(AL_ERROR, "Failed to write private key to the file.");
        return NPWG_RESULT_CANT_WRITE;
    }

    if (NPWG_INVALID_PORT != config->interface_config.listen_port) {
        if (fprintf(fp, LISTEN_PORT_FIELD " = %hu\n", config->interface_config.listen_port) < 0) {
            NPWG_LOG(AL_ERROR, "Failed to write ListenPort %hu to the file.", config->interface_config.listen_port);
            return NPWG_RESULT_CANT_WRITE;
        }
    }

    return NPWG_RESULT_NO_ERROR;
}

int npwg_write_config_file_impl(const npwg_config *config, const char *config_file)
{
    // Open the INI file for writing
    FILE *fp = fopen(config_file, "w");
    if (!fp) {
        char error_buffer[256];
        strerror_r(errno, error_buffer, sizeof(error_buffer));
        NPWG_LOG(AL_ERROR, "Failed to open config file, %s for writing. Error: %s", config_file, error_buffer);
        return NPWG_RESULT_BAD_CFG;
    }

    int rc = write_npwg_interface_section(fp, config);
    if (rc != NPWG_RESULT_NO_ERROR) {
        NPWG_LOG(AL_ERROR, "Failed to write to config %s", config_file);
    }
    fclose(fp);

    return rc;
}

int npwg_write_config_file_impl_full(const npwg_config *config, const char *config_file)
{
    // Open the INI file for writing
    FILE *fp = fopen(config_file, "w");
    if (!fp) {
        char error_buffer[256];
        strerror_r(errno, error_buffer, sizeof(error_buffer));
        NPWG_LOG(AL_ERROR, "Failed to open config file, %s for writing. Error: %s", config_file, error_buffer);
        return NPWG_RESULT_BAD_CFG;
    }

    int rc = write_npwg_interface_section(fp, config);

    if (rc == NPWG_RESULT_NO_ERROR) {
        rc = zhash_table_walk(config->peer_map,
                              NULL,
                              write_npwg_peer_one_section_f,
                              fp);
    }

    if (rc) {
        NPWG_LOG(AL_ERROR, "Failed to write for config %s", config_file);
    }

    fclose(fp);
    return rc;
}

// go through a route journal entry and add or delete one route
static int route_journal_walk_f(__attribute__((unused)) void *cookie,
                                void *object,
                                __attribute__((unused)) void *key,
                                __attribute__((unused)) size_t key_len)
{
    const cidr_bool_pair_t *cidr_bool_pair = object;

    if (!cidr_bool_pair) {
        NPWG_LOG(AL_ERROR, "Received invalid object");
        return NPWG_RESULT_BAD_ARGUMENT;
    }

    if (cidr_bool_pair->scratch_pad) {
        npwg_add_ip_route(cidr_bool_pair->ip_with_mask, NPWG_INTERFACE_NAME);
        return NPWG_RESULT_NO_ERROR;
    }

    npwg_del_ip_route(cidr_bool_pair->ip_with_mask, NPWG_INTERFACE_NAME);
    return NPWG_RESULT_NO_ERROR;
}

/*
 *  This function is used to selectively program routes depending on
 *  whether we are an np-gateway or np-connector.  At present, we pass a
 *  boolean (cookie) for the governing condition but in future we
 *  can we can just replace the bool with a struct, and the inner walker
 *  function can look into the struct to make the decision per hash entry
 *  as is done in many other places in the code
 */
int npwg_configure_routes(npwg_config *config)
{

    int rc = zhash_table_walk(config->route_journal, NULL, route_journal_walk_f, NULL);

    // free up the contents no matter whether there was an error
    zhash_table_flush_without_table_free(config->route_journal,
                                         destroy_route_entry_f,
                                         NULL);
    return rc;
}


void npwg_config_destroy_pending_config(npwg_updated_peers_config *pending_config,
                                        npwg_config *config)
{
    if (!config || !pending_config) {
        NPWG_LOG(AL_ERROR, "Received invalid config, can not destroy pending config");
        return;
    }

    zhash_table_remove(config->updated_peers_config,
                       pending_config->public_key,
                       NPWG_KEY_LENGTH, NULL);

    NPWG_FREE(pending_config);
    stats.free_pending_config++;
    pending_config = NULL;
}


static int
npwg_config_dump_stats(struct zpath_debug_state*  request_state,
                       __attribute__((unused))const char**  query_values,
                       __attribute__((unused))int           query_value_count,
                       __attribute__((unused))void*         cookie)
{
    char jsonout[10000];

    if (ARGO_RESULT_NO_ERROR == argo_structure_dump(npwg_config_stats_description,
                                                    &stats,
                                                    jsonout,
                                                    sizeof(jsonout),
                                                    NULL,
                                                    1)) {
        ZDP("%s\n", jsonout);
    }
    return NPWG_RESULT_NO_ERROR;
}

int npwg_config_debug_init()
{
    int rc = NPWG_RESULT_NO_ERROR;

    npwg_config_stats_description = argo_register_global_structure(NPWG_CONFIG_STATS_HELPER);

    if (!npwg_config_stats_description) {
        NPWG_LOG(AL_ERROR, "Cannot argo_register_global_structure npwg_config_stats_description");
        return NPWG_RESULT_ERR;
    }

    rc = zpath_debug_add_read_command("dump the stats of np_config_stats",
                                  "/npwg/np_config_stats",
                                  npwg_config_dump_stats,
                                  NULL,
                                  NULL);
    if (rc){
        NPWG_LOG(AL_ERROR, "Cannot zpath_debug_add_admin_command /npwg/np_config_stats");
        return rc;
    }

    return rc;

}
