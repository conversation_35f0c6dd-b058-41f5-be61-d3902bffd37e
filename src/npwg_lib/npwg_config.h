/*
 * npwg_config.h. Copyright (C) 2024 Zscaler Inc. All Rights Reserved.
 *
 * Objective:
 * 1. Create an INI parser to parse WireGuard INI files.
 * 2. This is not a generic INI parser.
 * 3. The file format is readable by the "wg" utility to configure "one"
 *    WireGurard interface.
 * 4. It does not contain extensions supported by the "wg-quick" utility.
 *
 * There may be multiple peer sections. The INI file looks like this:
 *
 * [INTERFACE]
 * PrivateKey = <own_private_key>
 * ListenPort = <tunnel_listen_port>
 *
 * # Each peer gets one such block.
 * [PEER]
 * PublicKey = <peer_public_key>
 * Endpoint = <peer_well_known_ip>:<peer_listen_port>
 * AllowedIPs = <peer_subnet_cidr>[, <peer_subnet_cidr>]*
 * PersistentKeepalive = <keep_alive_interval>
 *
 */

#ifndef _NPWG_CONFIG_H_
#define _NPWG_CONFIG_H_

#include "argo/argo.h"
#include "zhash/zhash_table.h"

#include <stdbool.h>
#include "npwg_lib/npwg_provider.h"

#ifdef __cplusplus
extern "C" {
#endif

#define INTERFACE_SECTION               "Interface"
#define PEER_SECTION                    "Peer"
#define PUBLIC_KEY_FIELD                "PublicKey"
#define PRIVATE_KEY_FIELD               "PrivateKey"
#define LISTEN_PORT_FIELD               "ListenPort"
#define ENDPOINT_FIELD                  "EndPoint"
#define ALLOWED_IPS_FIELD               "AllowedIPs"
#define PKEEPALIVE_FIELD                "PersistentKeepalive"

#define MAX_CONFIG_LINE_SIZE            20479
#define MAX_INI_KEY_SIZE                31
#define MAX_INI_VALUE_SIZE              20447
// 32 bytes translates to 44 bytes after base64 encoding
#define NPWG_KEY_LENGTH                 44
#define MIN_VALID_PORT_NUMBER           1
#define MAX_VALID_PORT_NUMBER           65535

// only IPv4 at present
#define MIN_IP_LENGTH                   7       // number of bytes in string format
#define MAX_IP_LENGTH                   15      // number of bytes in string format
#define NPWG_ARGO_INET_STR_MAX_LENGTH   63

#define INVALID_KEEPALIVE_INTERVAL_S    -1
#define MIN_KEEPALIVE_INTERVAL_S        0
#define MAX_KEEPALIVE_INTERVAL_S        600
#define DEFAULT_KEEPALIVE_INTERVAL_S    10

// NOTE: add interface name here
// note: this is not part of the Wireguard INI files
typedef struct npwg_interface_config
{
    char private_key[NPWG_KEY_LENGTH + 1];
    uint16_t listen_port;
} npwg_interface_config;

typedef struct cidr_bool_pair
{
    char ip_with_mask[NPWG_ARGO_INET_STR_MAX_LENGTH + 1];
    bool scratch_pad;
} cidr_bool_pair_t;

typedef struct npwg_peer_config
{
    char public_key[NPWG_KEY_LENGTH + 1];
    char ip[MAX_IP_LENGTH + 1];
    uint16_t port;
    char allowed_ips[NPWG_ALLOWED_IPS_BUFFER_SIZE + 1];
    // map of cidr block to cidr_bool_pair_t
    struct zhash_table *routes;
    int keepalive_interval;
    enum npwg_wireguard_peer_types peer_type;
} npwg_peer_config;

typedef struct npwg_updated_peers_config
{
    char public_key[NPWG_KEY_LENGTH + 1];
    bool do_add;

} npwg_updated_peers_config;

typedef struct npwg_config {
    npwg_interface_config interface_config;
    // map of peer public_key -> npwg_peer_config
    struct zhash_table *peer_map;
    zpath_rwlock_t peer_map_rw_lock;
    // map of cidr block to bool
    struct zhash_table *route_journal;

    // map of peer public_key -> npwg_peer_config
    // stores the list of peers that are pending to be updated
    struct zhash_table *updated_peers_config;
} npwg_config;

#define NPWG_PEER_MAP_RDLOCK(config)    ZPATH_RWLOCK_RDLOCK(&(config->peer_map_rw_lock), __FILE__, __LINE__)
#define NPWG_PEER_MAP_WRLOCK(config)    ZPATH_RWLOCK_WRLOCK(&(config->peer_map_rw_lock), __FILE__, __LINE__)
#define NPWG_PEER_MAP_UNLOCK(config)    ZPATH_RWLOCK_UNLOCK(&(config->peer_map_rw_lock), __FILE__, __LINE__)


/**
 * @brief Create in-memory NP Wireguard configuration.
 *
 * @return pointer to the configuration. Errors accessible via npwg_get_error()
 */
npwg_config *npwg_create_config();

/**
 * @brief destroy in memory configuration, free memory.
 *
 * @param [in, out] config         Pointer to the configuration.
 *
 * @return void, upon success, sets *config to NULL
 */
void npwg_destroy_config(npwg_config **config);

/**
 * @brief Parse configuration file in INI format and store it in in-memory config. See the
 *        beginning of this file for the format of the INI file.
 *
 * @param [in] config_file         Path to config file.
 * @param [in] config              In-memory configuration. Must be pre-allocated.
 *
 * @return int                     NPWG_RESULT_NO_ERROR on success, other NPWG errors on error
 *                                 Detailed error message can be accessed via npwg_get_error()
 */
int npwg_parse_config_file(const char *config_file, npwg_config *config);

/**
 * @brief write in-memory configuration to INI file with only interface section.
 *        See the beginning of this file for the
 *        format of the INI file.
 *
 * @param [in] config_file         Path to config file.
 * @param [in] config              In-memory configuration.
 *
 * @return int                     NPWG_RESULT_SUCCESS on success,
 *                                 NPWG_RESULT_ERROR otherwise.
 */
int npwg_write_config_file_impl(const npwg_config *config, const char *config_file);


/**
 * @brief write FULL in-memory configuration to INI file.
 *        See the beginning of this file for the
 *        format of the INI file.
 *
 * @param [in] config_file         Path to config file.
 * @param [in] config              In-memory configuration.
 *
 * @return int                     NPWG_RESULT_SUCCESS on success,
 *                                 NPWG_RESULT_ERROR otherwise.
 */
int npwg_write_config_file_impl_full(const npwg_config *config, const char *config_file);

/**
 * @brief set/update interface configuration
 *
 * @param [in] config              In-memory representation of WireGuard config.
 *                                 Created by npwg_create_config(). Can't be NULL.
 * @param [in] private_key         Wireguard base64 encoded private key, NUL terminated
 *                                 c-string. Can't be NULL.
 * @param [in] listen_port         Well known port. Optional. Set to NPWG_INVALID_PORT if
 *                                 not used.
 * @return int                     NPWG_RESULT_NO_ERROR on success, other NPWG errors on error
 *                                 Detailed error message can be accessed via npwg_get_error()
 */
int npwg_set_interface_config_impl(npwg_config *config,
                                   const char *private_key,
                                   uint16_t listen_port);

/**
 * @brief Add one peer to the configuration / update existing peer.
 *
 * @param [in] config              In-memory representation of WireGuard config.
 *                                 Created by npwg_create_config(). Can't be NULL.
 * @param [in] public_key          Wireguard base64 encoded public key, NUL terminated
 *                                 c-string. Can't be NULL.
 * @param [in] ip                  Well known IP of peer. Optional. If NULL, both ip
 *                                 and port are ignored.
 * @param [in] port                Well known port of peer. Optional. Must be a valid port if
 *                                 ip is not NULL.
 * @param [in] cidrs               Destination subnets accessible through this peer. This is
 *                                 an array of pointers holding CIDR blocks.
 *                                 Set to NULL if not used.
 * @param [in] cidr_count          Number of CIDR blocks in the cidrs array.
 * @param [in] keepalive_interval  Persistent keepalive interval. Optional. Set to
 *                                 NPWG_INVALID_KEEPALIVE_INTERVAL_S to disable keepalive.
 * @param [in] peer_type           A component which owns the public_key.
 *
 * @return int                     NPWG_RESULT_NO_ERROR on success, other NPWG errors on error
 *                                 Detailed error message can be accessed via npwg_get_error()
 */
int npwg_set_peer_config_impl(npwg_config *config,
                              const char *public_key,
                              const char *ip,
                              uint16_t port,
                              const struct argo_inet *cidrs,
                              size_t cidr_count,
                              int keepalive_interval,
                              enum npwg_wireguard_peer_types peer_type);

/**
 * @brief Delete configuration of one peer.
 *
 * @param [in] config              In-memory representation of WireGuard config.
 *                                 Created by npwg_create_config(). Can't be NULL.
 * @param [in] public_key          Wireguard base64 encoded public key, NUL terminated
 *                                 c-string. Can't be NULL.
 *
 * @return int                     NPWG_RESULT_NO_ERROR on success, other NPWG errors on error
 *                                 Detailed error message can be accessed via npwg_get_error()
 */
int npwg_delete_peer_config_impl(npwg_config *config, const char *public_key);

int npwg_configure_routes(npwg_config *config);

void npwg_config_destroy_pending_config(npwg_updated_peers_config *pending_config,
                                        npwg_config *config);

int npwg_config_debug_init();

#ifdef __cplusplus
} // extern "C"
#endif

#endif  /* _NPWG_CONFIG_H_ */
