/*
 * admin_probe_private.h. Copyright (C) 2021 Zscaler Inc, All Rights Reserved
 *
 */

#ifndef _ADMIN_PROBE_PRIVATE_H_
#define _ADMIN_PROBE_PRIVATE_H_

#include "admin_probe/admin_probe_rpc.h"
#include "zevent/zevent.h"
#include "zhash/zhash_table.h"
#include "admin_probe/admin_probe.h"

#define ADMIN_PROBE_MAX_ENTITY_NAME 256
#define ADMIN_PROBE_MAX_ENTITY_ID 256


typedef int (upload_s3_output_f(char *upload_buf, int size, char *hdr, int hdr_size, char *bucket_name, char *upload_path));

struct admin_probe_callbacks{
    task_status_update_cb_f *status_update_cb;
    check_feature_flag_cb_f *feature_flag_check_cb;
    restart_cb_f            *restart_cb;
    frr_cmds_execute_cb_f   *frr_cmds_execute_cb;
    get_cloud_epoch_s_cb_f  *get_current_cloud_epoch_s_cb;
    get_remote_host_for_s3_upload_f *get_remote_host_for_s3_upload_cb;
    get_entity_state_paused_mode_f *is_entity_paused;

};

struct admin_probe_state{

    int64_t entity_gid;
    char entity_name[ADMIN_PROBE_MAX_ENTITY_NAME];
    int64_t customer_gid; //might be zero if broker/exporter is using it.

    int64_t admin_probe_init_time_s;

    pthread_t thread;
    struct zevent_base *zbase;
    struct event_base *ebase;
    struct zthread_info *tickle_me;

    unsigned is_dev_env:1;

    //lib ctx
    struct zcdns *zcdns_ctx;
    struct zhealth *zhealth_ctx;

};


extern struct admin_probe_callbacks admin_probe_callbacks;
extern struct admin_probe_state admin_probe_state;


#endif // _ADMIN_PROBE_PRIVATE_H_
