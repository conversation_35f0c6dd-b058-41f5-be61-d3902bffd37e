/*
 * admin_probe_uploader.h. Copyright (C) 2021 Zscaler Inc. All Rights Reserved.
 */
/*
 * admin_probe_uploader.h. Copyright (C) 2021 Zscaler Inc, All Rights Reserved
 *
 */

#include "admin_probe/admin_probe.h"
#include "zpath_lib/zpath_lib.h"
#include "admin_probe/admin_probe_lib.h"




#define ADMIN_PROBE_MAX_BUCKET_NAME                   100
#define ADMIN_PROBE_UPLOADER_MAX_FILE_NAME            1000
#define ADMIN_PROBE_MAX_ERROR_MESSAGE                 100
#define ADMIN_PROBE_RETRY_COUNT                       1
#define ADMIN_PROBE_RETRY_START_TIME                  2
#define ADMIN_PROBE_MAX_BUFFER_SIZE                  (100 * 1024)
#define ADMIN_PROBE_UPLOADER_MAX_FILE_CNT_FOR_UPLOAD  10

#define ADMIN_PROBE_UPLOADER_HTTP_OK                  200
typedef struct uploader_output {
  char *command_uuid;
  int status; //0 meaning success
  char *error_message;
} admin_probe_uploader_output;

struct admin_probe_s3_output_resp {
   struct fohh_http_client *client;
   int status; //0 meaning success
   int req_status;
   void *cookie;
};

typedef void (s3_output_cb_f(admin_probe_uploader_output, void *));

typedef struct uploader_task {
    char        file[ADMIN_PROBE_UPLOADER_MAX_FILE_NAME];
    int         retry_count;
    char        *bucket_name;
    int         fd;
    size_t      buffer_size;
    char        *file_location;
    char        *command_uuid;
    int         upload_status;
    int         http_status;
    char        *upload_error_message;
    s3_output_cb_f *s3_output_cb;
    void        *cookie;
    int         file_uploaded_cnt;
    struct      admin_probe_uploader_file_list_head *head;
    struct      fohh_http_client *client;
    struct      event *timeout_timer;
    int         last_status;
    SSL_CTX *   ssl_ctx;
} admin_probe_uploader_task;


struct admin_probe_uploader_file_list {
    // Stores the absolute local file path for the file intended for s3 upload.
    char file_name[ADMIN_PROBE_UPLOADER_MAX_FILE_NAME];
    int file_seqno;
    int fd;
    struct evbuffer *buf;
    struct  evbuffer *extra_headers;
    /*
    * By default, files are uploaded to the S3 bucket at the location: S3_bucket_location/file_name (local full path).
    * In cases where the application needs to override the default upload path, this field can be used to specify the custom path.
    */
    char custom_upload_path[ADMIN_PROBE_UPLOADER_MAX_FILE_NAME];
    int is_override_default_upload_path;
    ZTAILQ_ENTRY(admin_probe_uploader_file_list) file_list_entry;
};


ZTAILQ_HEAD(admin_probe_uploader_file_list_head, admin_probe_uploader_file_list);



/* admin_probe_send_to_s3_uploader
 * Inputs:
 * bucket_name : A single bucket name for S3 upload which is a FQDN in the format <bucketname>.s3-<region>.amazonaws.com
 * file_location : file prefix in S3 where the data will be uploaded
 * read_dir : Directory to read for uploading files
 * custom_upload_dir : Custom Directory name to be used to store the uploaded file in S3
 * command_uuid : the uuid that identifies the command
 * s3_output_cb : callback invoked after the result
 * cookie : cookie sent for callback
 * All arguments will be copied and will not be needed to be held once the uploader is invoked
 * If the function returns a failure immediately, the callback will not be called. If it returns success the status in callback needs to be checked.
 */
int
admin_probe_send_to_s3_uploader(char *bucket_name, char *file_location, char *read_dir, char *custom_upload_dir,
                                char *command_uuid, s3_output_cb_f *s3_output_cb, void *cookie);

int
admin_probe_uploader_module_init();
