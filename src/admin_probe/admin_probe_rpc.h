/*
 * admin_probe_rpc.h. Copyright (C) 2021 Zscaler Inc, All Rights Reserved
 */

#ifndef _ADMIN_PROBE_RPC_H_
#define _ADMIN_PROBE_RPC_H_

#include "argo/argo.h"
#include "fohh/fohh.h"
#include "zhash/zhash_table.h"

extern struct argo_structure_description *zpn_command_probe_status_description;
extern struct argo_structure_description *np_command_probe_status_description;
extern struct argo_structure_description *admin_probe_stats_description;

struct zpn_command_probe_status {                      /* _ARGO: object_definition */
    char *command_uuid;                                /* _ARGO: string */
    char *action;                                      /* _ARGO: string */
    int64_t entity_gid;                                /* _ARGO: integer */
    int64_t customer_gid;                              /* _ARGO: integer */
    const char *status;                                /* _ARGO: string */
    const char *error_message;                         /* _ARGO: string */
    int64_t start_time;                                /* _ARGO: integer */
    int64_t end_time;                                  /* _ARGO: integer */
    char *content_json;                                /* _ARGO: string */
    char *content_txt;                                 /* _ARGO: string */
};

struct np_command_probe_status {                      /* _ARGO: object_definition */
    char *command_uuid;                                /* _ARGO: string */
    char *action;                                      /* _ARGO: string */
    int64_t entity_gid;                                /* _ARGO: integer */
    int64_t customer_gid;                              /* _ARGO: integer */
    const char *status;                                /* _ARGO: string */
    const char *error_message;                         /* _ARGO: string */
    int64_t start_time;                                /* _ARGO: integer */
    int64_t end_time;                                  /* _ARGO: integer */
    char *content_json;                                /* _ARGO: string */
    char *content_txt;                                 /* _ARGO: string */
};

struct admin_probe_stats {                                   /* _ARGO: object_definition */
    int64_t total_number_of_valid_admin_probes;              /* _ARGO: integer */
    int64_t total_number_of_admin_probe_complete;            /* _ARGO: integer */
    int64_t total_number_of_admin_probe_failed;              /* _ARGO: integer */
    int64_t total_number_of_admin_probe_timeout;             /* _ARGO: integer */
    int64_t total_number_of_admin_probe_cancelled;           /* _ARGO: integer */
    int64_t total_number_of_admin_probe_restart_complete;    /* _ARGO: integer */
    int64_t total_number_of_admin_probe_restart_fail;        /* _ARGO: integer */
    int64_t total_number_of_admin_probe_restart_cancel;      /* _ARGO: integer */
    int64_t total_number_of_admin_probe_dns_a;               /* _ARGO: integer */
    int64_t total_number_of_admin_probe_dns_a_success;       /* _ARGO: integer */
    int64_t total_number_of_admin_probe_dns_aaaa;            /* _ARGO: integer */
    int64_t total_number_of_admin_probe_dns_aaaa_success;    /* _ARGO: integer */
    int64_t total_number_of_admin_probe_dns_srv;             /* _ARGO: integer */
    int64_t total_number_of_admin_probe_dns_srv_succes;      /* _ARGO: integer */
    int64_t total_number_of_admin_probe_tcp_ping;            /* _ARGO: integer */
    int64_t total_number_of_admin_probe_tcp_ping_succes;     /* _ARGO: integer */
    int64_t total_number_of_admin_probe_icmp_ping;           /* _ARGO: integer */
    int64_t total_number_of_admin_probe_icmp_ping_succes;    /* _ARGO: integer */
    int64_t total_number_of_admin_probe_tcpdump_success;     /* _ARGO: integer */
    int64_t total_number_of_admin_probe_tcpdump_fail;        /* _ARGO: integer */
    int64_t total_number_of_admin_probe_frr_cmds_success;    /* _ARGO: integer */
    int64_t total_number_of_admin_probe_frr_cmds_fail;       /* _ARGO: integer */
};


int admin_probe_rpc_init(void);
int admin_probe_rpc_debug_init();

int admin_probe_send_zpn_command_probe_status(struct fohh_connection *f_conn,
                                              int64_t f_conn_incarnation,
                                              struct zpn_command_probe_status *status);

int admin_probe_send_np_command_probe_status(struct fohh_connection *f_conn,
                                              int64_t f_conn_incarnation,
                                              struct np_command_probe_status *status);

#endif /* _ADMIN_PROBE_RPC_H_ */
