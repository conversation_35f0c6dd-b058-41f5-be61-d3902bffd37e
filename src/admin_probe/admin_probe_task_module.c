/*
 * admin_probe_task_module.c. Copyright (C) 2021 Zscaler Inc, All Rights Reserved
 *
 */

#include "admin_probe/admin_probe_task_module.h"
#include "admin_probe/admin_probe_task_module_common.h"
#include "admin_probe/admin_probe_task_module_dns.h"
#include "admin_probe/admin_probe_task_module_icmp.h"
#include "admin_probe/admin_probe_task_module_tcp.h"
#include "admin_probe/admin_probe_task_module_tcpdump.h"
#include "admin_probe/admin_probe_task_module_frr_cmds.h"
#include "admin_probe/admin_probe_task_module_restart.h"
#include "admin_probe/admin_probe_public.h"
#include "admin_probe/admin_probe_uploader.h"
#include "admin_probe/admin_probe_lib.h"
#include "admin_probe/admin_probe_rpc.h"
#include <sys/queue.h>
#include <inttypes.h>

#include <event2/listener.h>
#include <event2/bufferevent.h>
#include <event2/buffer.h>

#include "zhash/zhash_table.h"
#include "zpath_lib/zpath_debug.h"

#include "zevent/zevent.h"

#include "zcdns/zcdns_libevent.h"
#include "admin_probe/admin_probe_rate_limiting.h"
#include "admin_probe/admin_probe_rpc_send.h"
#include "zcdns/zcdns.h"
#include "argo/argo.h"
#include "parson/parson.h"

#define ADMIN_PROBE_TASK_MODULE_MAX_ALLOWED_TIME_FOR_ADMIN_PROBE_TO_COMEBACK_AFTER_A_RESTART_S 20*60
#define ADMIN_PROBE_TASK_MODULE_GRACEFUL_PERIOD_FOR_NOT_TAKING_PENDING_RESTART_COMMAND_S 2*60


static
struct admin_probe_task_module_stats{                                             /* _ARGO: * object_definition */
    int64_t total_number_of_task_created;                                         /* _ARGO: integer */
    int64_t total_number_of_task_success;                                         /* _ARGO: integer */
    int64_t total_number_of_task_failed;                                          /* _ARGO: integer */
    int64_t total_number_of_task_timeout;                                         /* _ARGO: integer */
    int64_t total_number_of_task_cancelled;                                       /* _ARGO: integer */
    int64_t number_of_task_destroyed;                                             /* _ARGO: integer */
    int64_t number_of_processing_status_sent;                                     /* _ARGO: integer */
    int64_t task_processing_sent_and_also_processing_received;                    /* _ARGO: integer */
    int64_t number_of_cancelled_status_sent;                                      /* _ARGO: integer */
    int64_t number_of_completed_status_sent;                                      /* _ARGO: integer */
    int64_t number_of_failed_status_sent;                                         /* _ARGO: integer */
    int64_t number_of_timeout_status_sent;                                        /* _ARGO: integer */
    int64_t number_of_termination_status_resend;                                  /* _ARGO: integer */
    int64_t receiving_processing_on_restart_within_time_limit;                    /* _ARGO: integer */
    int64_t receiving_processing_on_restart_exceed_time_limit;                    /* _ARGO: integer */
    int64_t expiry_fired_but_task_reap_done;                                      /* _ARGO: integer */
    int64_t expiry_fired_but_task_already_uploading;                              /* _ARGO: integer */
    int64_t expiry_fired_for_dns_task;                                            /* _ARGO: integer */
    int64_t new_task_failed_at_creation_time_validation_check;                    /* _ARGO: integer */
    int64_t new_task_failed_at_task_type_check;                                   /* _ARGO: integer */
    int64_t create_new_task_failed;                                               /* _ARGO: integer */
    int64_t action_on_restart_reaching_unexpected_branch;                         /* _ARGO: integer */
    int64_t task_not_exist_unexpected_event;                                      /* _ARGO: integer */
    int64_t task_not_exist_create_new_task;                                       /* _ARGO: integer */
    int64_t destroy_failed_to_remove_from_hash;                                   /* _ARGO: integer */
    int64_t acking_unexpected_status_that_we_have_never_set_before;               /* _ARGO: integer */
    int64_t number_of_restart_cb_failed;                                          /* _ARGO: integer */
    int64_t restart_failed_because_of_no_callback_provided;                       /* _ARGO: integer */
    int64_t number_of_tasks_checked_exceed_limit;                                 /* _ARGO: integer */
    int64_t task_module_invalid_cb;                                                 /* _ARGO: integer */
    int64_t task_already_in_pending_and_rx_pending;                               /* _ARGO: integer */
    int64_t task_is_in_pending_but_rx_processing_state_from_db;                   /* _ARGO: integer */
    int64_t task_is_in_pending_but_db_update_the_status_to_termination_state;     /* _ARGO: integer */
    int64_t task_is_in_pending_but_rx_unexpected_event_call;                      /* _ARGO: integer */
    int64_t task_processing_sent_but_rx_pending;                                  /* _ARGO: integer */
    int64_t task_processing_sent_but_rx_a_termination_update_from_db;             /* _ARGO: integer */
    int64_t task_uploading_but_rx_pending;                                        /* _ARGO: integer */
    int64_t task_processing_but_rx_internal_err_unexpected;                       /* _ARGO: integer */
    int64_t task_uploading_but_rx_a_termination_update_from_db;                   /* _ARGO: integer */
    int64_t task_uploading_but_rx_internal_err_unexpected;                        /* _ARGO: integer */
    int64_t task_termination_status_sent_but_rx_pending;                          /* _ARGO: integer */
    int64_t task_terminate_ourside_also_terminate_from_db;                        /* _ARGO: integer */
    int64_t task_terminate_bad_state;                                             /* _ARGO: integer */
    int64_t task_terminate_sent_rx_internal_task_execute_event;                   /* _ARGO: integer */
    int64_t task_terminate_sent_rx_internal_task_upload_event;                    /* _ARGO: integer */
    int64_t already_reapong_but_rx_termination_status_again_from_db;              /* _ARGO: integer */
    int64_t already_reaping_but_now_pending;                                      /* _ARGO: integer */
    int64_t already_reaping_but_now_processing;                                   /* _ARGO: integer */
    int64_t reaping_bad_state;                                                    /* _ARGO: integer */
    int64_t already_reaping_but_rx_a_cancel_request_from_user;                    /* _ARGO: integer */
    int64_t task_freeing_event_triggered;                                         /* _ARGO: integer */
    int64_t invalid_event_happened;                                               /* _ARGO: integer */
    int64_t restart_command_cancel;                                               /* _ARGO: integer */
    int64_t restart_command_complete;                                             /* _ARGO: integer */
    int64_t restart_command_fail;                                                 /* _ARGO: integer */

} stats;
#include "admin_probe/admin_probe_task_module_compiled_c.h"
static struct argo_structure_description*  admin_probe_task_module_stats_desciption;


static struct {

    struct zhash_table *task_hash_by_command_uuid;

    /*all the tasks should reside in this queue*/
    struct admin_probe_task_head task_queue;
    int64_t number_of_tasks_in_task_queue;

    int64_t number_of_tasks_in_state[max_task_state];

    unsigned is_task_module_state_pause_for_restart:1;

}task_module_state;


int admin_probe_task_module_default_init_f(struct admin_probe_task *cur_task)
{
    ADMIN_PROBE_LOG(AL_ERROR, "%s: Invalid task type",cur_task->command_uuid?cur_task->command_uuid: "");
    stats.task_module_invalid_cb++;
    return ADMIN_PROBE_RESULT_ERR;
}


int admin_probe_task_module_default_process_f(struct admin_probe_task *cur_task)
{
    ADMIN_PROBE_LOG(AL_ERROR, "%s: Invalid task type",cur_task->command_uuid?cur_task->command_uuid: "");
    stats.task_module_invalid_cb++;
    return ADMIN_PROBE_RESULT_ERR;
}

int admin_probe_task_module_default_check_expiry_f(struct admin_probe_task *cur_task)
{
    ADMIN_PROBE_LOG(AL_ERROR, "%s: Invalid task type",cur_task->command_uuid?cur_task->command_uuid: "");
    stats.task_module_invalid_cb++;
    return ADMIN_PROBE_RESULT_ERR;
}

void admin_probe_task_module_default_free_f(struct admin_probe_task *cur_task)
{
    ADMIN_PROBE_LOG(AL_ERROR, "%s: Invalid task type",cur_task->command_uuid?cur_task->command_uuid: "");
    stats.task_module_invalid_cb++;
    return;
}

int admin_probe_task_module_default_cancel_f(struct admin_probe_task *cur_task)
{
    ADMIN_PROBE_LOG(AL_ERROR, "%s: Invalid task type",cur_task->command_uuid?cur_task->command_uuid: "");
    stats.task_module_invalid_cb++;
    return ADMIN_PROBE_RESULT_ERR;
}


struct admin_probe_task_module_callouts admin_probe_task_module_callouts_default = {
    admin_probe_task_module_default_init_f,
    admin_probe_task_module_default_process_f,
    admin_probe_task_module_default_check_expiry_f,
    admin_probe_task_module_default_free_f,
    admin_probe_task_module_default_cancel_f
};



#define ADMIN_PROBE_TASK_NOT_EXIST_STR "TASK_NOT_EXIST_STR"
#define ADMIN_PROBE_TASK_PENDING_STR "TASK_PENDING_STR"
#define ADMIN_PROBE_TASK_PROCESSING_SENT_STR "TASK_PROCESSING_SENT_STR"
#define ADMIN_PROBE_TASK_UPLOADING_STR "TASK_UPLOADING_STR"
#define ADMIN_PROBE_TASK_CANCELLED_SENT_STR "TASK_CANCELLED_SENT_STR"
#define ADMIN_PROBE_TASK_FAILED_SENT_STR "TASK_FAILED_SENT_STR"
#define ADMIN_PROBE_TASK_TIMEOUT_SENT_STR "TASK_TIMEOUT_SENT_STR"
#define ADMIN_PROBE_TASK_COMPLETE_SENT_STR "TASK_COMPLETE_SENT_STR"
#define ADMIN_PROBE_TASK_REAPING_STR "TASK_REAPING_STR"
#define ADMIN_PROBE_TASK_FREE_STR "TASK_FREE_STR"

const char *task_state_str[max_task_state] = {
    [task_not_exist] = ADMIN_PROBE_TASK_NOT_EXIST_STR,
    [task_pending] = ADMIN_PROBE_TASK_PENDING_STR,
    [task_processing_sent] = ADMIN_PROBE_TASK_PROCESSING_SENT_STR,
    [task_uploading] = ADMIN_PROBE_TASK_UPLOADING_STR,
    [task_cancelled_sent] = ADMIN_PROBE_TASK_CANCELLED_SENT_STR,
    [task_failed_sent] = ADMIN_PROBE_TASK_FAILED_SENT_STR,
    [task_timeout_sent] = ADMIN_PROBE_TASK_TIMEOUT_SENT_STR,
    [task_complete_sent] = ADMIN_PROBE_TASK_COMPLETE_SENT_STR,
    [task_reaping] = ADMIN_PROBE_TASK_REAPING_STR,
    [task_free] = ADMIN_PROBE_TASK_FREE_STR,
};

#define ADMIN_PROBE_INVALID_EVENT_STR "INVALID_EVENT_STR"
#define ADMIN_PROBE_PENDING_FROM_WALLY_STR "PENDING_FROM_WALLY_STR"
#define ADMIN_PROBE_PROCESSING_FROM_WALLY_STR "PROCESSING_FROM_WALLY_STR"
#define ADMIN_PROBE_COMPLETED_FROM_WALLY_STR "COMPLETED_FROM_WALLY_STR"
#define ADMIN_PROBE_FAILED_FROM_WALLY_STR "FAILED_FROM_WALLY_STR"
#define ADMIN_PROBE_TIMED_OUT_FROM_WALLY_STR "TIMED_OUT_FROM_WALLY_STR"
#define ADMIN_PROBE_CANCELLED_FROM_WALLY_STR "CANCELLED_FROM_WALLY_STR"
#define ADMIN_PROBE_CAMCEL_REQUEST_FROM_WALLY_STR "CAMCEL_REQUEST_FROM_WALLY_STR"
#define ADMIN_PROBE_INTERNAL_TIMEOUT_TIMER_STR "INTERNAL_TIMEOUT_TIMER_STR"
#define ADMIN_PROBE_INTERNAL_TASK_EXECUTE_DONE_SUCCESS_STR "INTERNAL_TASK_EXECUTE_DONE_SUCCESS_STR"
#define ADMIN_PROBE_INTERNAL_TASK_EXECUTE_DONE_FAILED_STR "INTERNAL_TASK_EXECUTE_DONE_FAILED_STR"
#define ADMIN_PROBE_INTERNAL_TASK_UPLOAD_SUCCESS_STR "INTERNAL_TASK_UPLOAD_SUCCESS_STR"
#define ADMIN_PROBE_INTERNAL_TASK_UPLOAD_FAILED_STR "INTERNAL_TASK_UPLOAD_FAILED_STR"
#define ADMIN_PROBE_INTERNAL_ERR_STR "INTERNAL_ERR_STR"
#define ADMIN_PROBE_INTERNAL_CHECK_TASK_STR "INTERNAL_CHECK_TASK_STR"

const char *task_event_str[max_task_events] = {
    [invalid_event] = ADMIN_PROBE_INVALID_EVENT_STR,
    [pending_from_wally] = ADMIN_PROBE_PENDING_FROM_WALLY_STR,
    [processing_from_wally] = ADMIN_PROBE_PROCESSING_FROM_WALLY_STR,
    [completed_from_wally] = ADMIN_PROBE_COMPLETED_FROM_WALLY_STR,
    [failed_from_wally] = ADMIN_PROBE_FAILED_FROM_WALLY_STR,
    [timed_out_from_wally] = ADMIN_PROBE_TIMED_OUT_FROM_WALLY_STR,
    [cancelled_from_wally] = ADMIN_PROBE_CANCELLED_FROM_WALLY_STR,
    [cancel_request_from_wally] = ADMIN_PROBE_CAMCEL_REQUEST_FROM_WALLY_STR,
    [internal_timeout_timer] = ADMIN_PROBE_INTERNAL_TIMEOUT_TIMER_STR,
    [internal_task_execute_done_success] = ADMIN_PROBE_INTERNAL_TASK_EXECUTE_DONE_SUCCESS_STR,
    [internal_task_execute_done_failed] = ADMIN_PROBE_INTERNAL_TASK_EXECUTE_DONE_FAILED_STR,
    [internal_task_upload_success] = ADMIN_PROBE_INTERNAL_TASK_UPLOAD_SUCCESS_STR,
    [internal_task_upload_failed] = ADMIN_PROBE_INTERNAL_TASK_UPLOAD_FAILED_STR,
    [internal_err] = ADMIN_PROBE_INTERNAL_ERR_STR,
    [internal_check_task] = ADMIN_PROBE_INTERNAL_CHECK_TASK_STR,
};

#define ADMIN_PROBE_INVALID_TASK_TYPE_STR "ADMIN_PROBE_INVALID_TASK_TYPE_STR"
#define ADMIN_PROBE_TASK_TYPE_RESTART_PROCESS_STR "ADMIN_PROBE_TASK_TYPE_RESTART_PROCESS_STR"
#define ADMIN_PROBE_TASK_TYPE_RESTART_SYSTEM_STR "ADMIN_PROBE_TASK_TYPE_RESTART_SYSTEM_STR"
#define ADMIN_PROBE_TASK_TYPE_DNS_STR "ADMIN_PROBE_TASK_TYPE_DNS_STR"
#define ADMIN_PROBE_TASK_TYPE_ICMP_STR "ADMIN_PROBE_TASK_TYPE_ICMP_STR"
#define ADMIN_PROBE_TASK_TYPE_TCP_STR "ADMIN_PROBE_TASK_TYPE_TCP_STR"
#define ADMIN_PROBE_TASK_TYPE_MTR_STR "ADMIN_PROBE_TASK_TYPE_MTR_STR"
#define ADMIN_PROBE_TASK_TYPE_TCPDUMP_STR "ADMIN_PROBE_TASK_TYPE_TCPDUMP_STR"
#define ADMIN_PROBE_TASK_TYPE_IP_ROUTE_STR "ADMIN_PROBE_TASK_TYPE_IP_ROUTE_STR"
#define ADMIN_PROBE_TASK_TYPE_IP_INTERFACES_STR "ADMIN_PROBE_TASK_TYPE_IP_INTERFACES_STR"
#define ADMIN_PROBE_TASK_TYPE_IP_BGP_STR "ADMIN_PROBE_TASK_TYPE_IP_BGP_STR"
#define ADMIN_PROBE_TASK_TYPE_IP_BGP_NEIGHBORS_STR "ADMIN_PROBE_TASK_TYPE_IP_BGP_NEIGHBORS_STR"
#define ADMIN_PROBE_TASK_TYPE_IP_BGP_SUMMARY_STR "ADMIN_PROBE_TASK_TYPE_IP_BGP_SUMMARY_STR"
#define ADMIN_PROBE_TASK_TYPE_IP_CLEAR_BGP_STR "ADMIN_PROBE_TASK_TYPE_IP_CLEAR_BGP_STR"
#define ADMIN_PROBE_TASK_TYPE_BGP_RUNNING_CONFIG_STR "ADMIN_PROBE_TASK_TYPE_BGP_RUNNING_CONFIG_STR"
#define ADMIN_PROBE_TASK_TYPE_BGP_FAILED_CONFIG_STR "ADMIN_PROBE_TASK_TYPE_BGP_FAILED_CONFIG_STR"
#define ADMIN_PROBE_TASK_TYPE_BGP_GENERATED_CONFIG_STR "ADMIN_PROBE_TASK_TYPE_BGP_GENERATED_CONFIG_STR"
#define ADMIN_PROBE_TASK_TYPE_BGP_CONFIG_VALIDATE_STR "ADMIN_PROBE_TASK_TYPE_BGP_CONFIG_VALIDATE_STR"
#define ADMIN_PROBE_TASK_TYPE_BGP_CONFIG_STATUS_STR "ADMIN_PROBE_TASK_TYPE_BGP_CONFIG_STATUS_STR"
#define ADMIN_PROBE_TASK_TYPE_BGP_CONFIG_STATUS_DETAILS_STR "ADMIN_PROBE_TASK_TYPE_BGP_CONFIG_STATUS_DETAILS_STR"
#define ADMIN_PROBE_TASK_TYPE_BGP_GET_LOGS_STR "ADMIN_PROBE_TASK_TYPE_BGP_GET_LOGS_STR"
#define ADMIN_PROBE_TASK_TYPE_STOP_BGP_STR "ADMIN_PROBE_TASK_TYPE_STOP_BGP_STR"
#define ADMIN_PROBE_TASK_TYPE_START_BGP_STR "ADMIN_PROBE_TASK_TYPE_START_BGP_STR"
#define ADMIN_PROBE_TASK_TYPE_RESTART_BGP_STR "ADMIN_PROBE_TASK_TYPE_RESTART_BGP_STR"
#define ADMIN_PROBE_TASK_TYPE_STATUS_BGP_STR "ADMIN_PROBE_TASK_TYPE_STATUS_BGP_STR"
#define ADMIN_PROBE_TASK_TYPE_RELOAD_BGP_STR "ADMIN_PROBE_TASK_TYPE_RELOAD_BGP_STR"


const char *task_type_str[max_admin_probe_task_type] = {
    [0] = ADMIN_PROBE_INVALID_TASK_TYPE_STR,
    [admin_probe_task_type_restart_process] = ADMIN_PROBE_TASK_TYPE_RESTART_PROCESS_STR,
    [admin_probe_task_type_restart_system] = ADMIN_PROBE_TASK_TYPE_RESTART_SYSTEM_STR,
    [admin_probe_task_type_dns] = ADMIN_PROBE_TASK_TYPE_DNS_STR,
    [admin_probe_task_type_icmp] = ADMIN_PROBE_TASK_TYPE_ICMP_STR,
    [admin_probe_task_type_tcp] = ADMIN_PROBE_TASK_TYPE_TCP_STR,
    [admin_probe_task_type_mtr] = ADMIN_PROBE_TASK_TYPE_MTR_STR,
    [admin_probe_task_type_tcpdump] = ADMIN_PROBE_TASK_TYPE_TCPDUMP_STR,
    [admin_probe_task_type_ip_route] = ADMIN_PROBE_TASK_TYPE_IP_ROUTE_STR,
    [admin_probe_task_type_ip_interfaces] = ADMIN_PROBE_TASK_TYPE_IP_INTERFACES_STR,
    [admin_probe_task_type_ip_bgp] = ADMIN_PROBE_TASK_TYPE_IP_BGP_STR,
    [admin_probe_task_type_ip_bgp_neighbors] = ADMIN_PROBE_TASK_TYPE_IP_BGP_NEIGHBORS_STR,
    [admin_probe_task_type_ip_bgp_summary] = ADMIN_PROBE_TASK_TYPE_IP_BGP_SUMMARY_STR,
    [admin_probe_task_type_ip_clear_bgp] = ADMIN_PROBE_TASK_TYPE_IP_CLEAR_BGP_STR,
    [admin_probe_task_type_bgp_running_config] = ADMIN_PROBE_TASK_TYPE_BGP_RUNNING_CONFIG_STR,
    [admin_probe_task_type_bgp_failed_config] = ADMIN_PROBE_TASK_TYPE_BGP_FAILED_CONFIG_STR,
    [admin_probe_task_type_bgp_generated_config] = ADMIN_PROBE_TASK_TYPE_BGP_GENERATED_CONFIG_STR,
    [admin_probe_task_type_bgp_config_validate] = ADMIN_PROBE_TASK_TYPE_BGP_CONFIG_VALIDATE_STR,
    [admin_probe_task_type_bgp_config_status] = ADMIN_PROBE_TASK_TYPE_BGP_CONFIG_STATUS_STR,
    [admin_probe_task_type_bgp_config_status_details] = ADMIN_PROBE_TASK_TYPE_BGP_CONFIG_STATUS_DETAILS_STR,
    [admin_probe_task_type_bgp_get_logs] = ADMIN_PROBE_TASK_TYPE_BGP_GET_LOGS_STR,
    [admin_probe_task_type_stop_bgp] = ADMIN_PROBE_TASK_TYPE_STOP_BGP_STR,
    [admin_probe_task_type_start_bgp] = ADMIN_PROBE_TASK_TYPE_START_BGP_STR,
    [admin_probe_task_type_restart_bgp] = ADMIN_PROBE_TASK_TYPE_RESTART_BGP_STR,
    [admin_probe_task_type_status_bgp] = ADMIN_PROBE_TASK_TYPE_STATUS_BGP_STR,
    [admin_probe_task_type_reload_bgp] = ADMIN_PROBE_TASK_TYPE_RELOAD_BGP_STR
};

static void admin_probe_task_module_UT_task_state_transition(struct admin_probe_task *cur_task, const char* status)
{
    if (!cur_task) {
        ADMIN_PROBE_LOG(AL_ERROR, "UT: invalid cur_task");
        return;
    }

    if (!status) {
        ADMIN_PROBE_LOG(AL_ERROR, "UT: invalid status");
        return;
    }

    if (0 == strcmp(status, ADMIN_PROBE_STATUS_PROCESSING)) {
        ADMIN_PROBE_DEBUG_TASK_MODULE("%s : UT admin probe command : skip sending processing report for status %s",cur_task->command_uuid, status);
        return;
    }

    if ((0 == strcmp(status, ADMIN_PROBE_STATUS_COMPLETED)) ||
        (0 == strcmp(status, ADMIN_PROBE_STATUS_CANCELLED)) ||
        (0 == strcmp(status, ADMIN_PROBE_STATUS_FAILED)) ||
        (0 == strcmp(status, ADMIN_PROBE_STATUS_TIMED_OUT))) {

        if (cur_task->cur_state == task_pending) {
            task_module_state.number_of_tasks_in_state[task_processing_sent]--;
        }

        if (cur_task->cur_state == task_processing_sent) {
            task_module_state.number_of_tasks_in_state[task_processing_sent]--;
        }

        if (cur_task->cur_state == task_uploading) {
            task_module_state.number_of_tasks_in_state[task_processing_sent]--;
        }

        cur_task->cur_state = task_free;
        task_module_state.number_of_tasks_in_state[task_free]++;

        ADMIN_PROBE_DEBUG_TASK_MODULE("%s : UT admin probe command : skip sending termination report for status %s",cur_task->command_uuid, status);
    }
}


int64_t admin_probe_task_module_get_next_termination_resend_interval_general_s(int64_t next_termination_resend_interval_s, char *command_uuid)
{
    int64_t next_resend_interval_s;
    int next_resend_factor;

    /*we are setting the resend interval the first time*/
    if (!next_termination_resend_interval_s) {
        next_resend_interval_s = admin_probe_rate_limiting_get_termination_status_first_resend_time_general_s();

        ADMIN_PROBE_DEBUG_TASK_MODULE("%s: admin probe - setting first next termination resend general interval to %"PRId64"s ", (command_uuid)? command_uuid: " ", next_resend_interval_s);
        return next_resend_interval_s;
    }


    next_resend_factor = admin_probe_rate_limiting_get_termination_status_resend_factor_general();
    next_resend_interval_s = next_termination_resend_interval_s * next_resend_factor;

    ADMIN_PROBE_DEBUG_TASK_MODULE("%s: admin probe - setting next termination resend interval to %"PRId64"s, with general factor %d and previous interval %"PRId64"s ", (command_uuid)? command_uuid:" ",
                                                                                                                                                                        next_resend_interval_s,
                                                                                                                                                                        next_resend_factor,
                                                                                                                                                                        next_termination_resend_interval_s);

    return next_resend_interval_s;
}


int64_t admin_probe_task_module_get_next_termination_resend_interval_s(int64_t next_termination_resend_interval_s, char *command_uuid, enum admin_probe_task_type type)
{
    return admin_probe_task_module_get_next_termination_resend_interval_general_s(next_termination_resend_interval_s, command_uuid);

}

void admin_probe_task_module_update_cur_task_field_on_sending_first_termination_status_report(struct admin_probe_task *cur_task)
{
    if (!cur_task) {
        ADMIN_PROBE_LOG(AL_ERROR, "invalid cur_task, can not update cur_task field upon sending first termination status report");
        return;
    }

    cur_task->is_first_termination_status_report_sent = 1;
    cur_task->first_termination_sent_local_s = epoch_s();
    cur_task->last_termination_sent_local_s = cur_task->first_termination_sent_local_s;
    cur_task->next_termination_status_resend_time_interval_s = admin_probe_task_module_get_next_termination_resend_interval_s(0, cur_task->command_uuid, cur_task->type);

}


static int admin_probe_task_module_send_status_report_for_task(struct admin_probe_task *cur_task, const char* status)
{
    if (!cur_task) {
        ADMIN_PROBE_LOG(AL_ERROR, "invalid cur_task, can not send ");
        return ADMIN_PROBE_RESULT_ERR;
    }

    if (!cur_task->command_uuid) {
        ADMIN_PROBE_LOG(AL_ERROR, "no command_uuid, can not send status report");
        return ADMIN_PROBE_RESULT_ERR;
    }

    if (!cur_task->action) {
        ADMIN_PROBE_LOG(AL_ERROR, "%s : no action, can not send status report", cur_task->command_uuid);
        return ADMIN_PROBE_RESULT_ERR;
    }

    if (!status) {
        ADMIN_PROBE_LOG(AL_ERROR, "%s : no status, can not send status report", cur_task->command_uuid);
        return ADMIN_PROBE_RESULT_ERR;
    }

    if (cur_task->is_ut) {
        admin_probe_task_module_UT_task_state_transition(cur_task, status);
        return ADMIN_PROBE_RESULT_NO_ERROR;
    }

    int res;

    res = admin_probe_rpc_send_tx_command_probe_status_report(cur_task->command_uuid,
                                                              cur_task->action,
                                                              cur_task->entity_gid,
                                                              cur_task->customer_gid,
                                                              status,
                                                              cur_task->start_time_cloud_s,
                                                              cur_task->end_time_cloud_s,
                                                              cur_task->result_json,
                                                              cur_task->result_txt,
                                                              cur_task->err_message,
                                                              cur_task->is_np_command_probe);

    return res;

}

void admin_probe_task_module_free_serialized_str_using_parson_allocator(char *buf)
{
    json_free_serialized_string(buf);
}


static int admin_probe_task_module_free_this_task_done(struct admin_probe_task *cur_task)
{
    if (!cur_task) {
        ADMIN_PROBE_LOG(AL_ERROR, "Invalid cur_task - nothing needs to free");
        return ADMIN_PROBE_TRUE;
    }

    int res;

    res = zhash_table_remove(task_module_state.task_hash_by_command_uuid, cur_task->command_uuid, strlen(cur_task->command_uuid), cur_task);
    if (res) {
        stats.destroy_failed_to_remove_from_hash++;
        ADMIN_PROBE_LOG(AL_NOTICE, "%s : failed to clean up %s : %s because failed to remove the task from hash, will come back later", cur_task->command_uuid? cur_task->command_uuid : "(unknown command_uuid)",
                                                                                                                                        cur_task->action? cur_task->action : "(unknown action)",
                                                                                                                                        cur_task->target? cur_task->target : "");
        return ADMIN_PROBE_FALSE;
    }

    if (cur_task->command_uuid) {
        ADMIN_PROBE_FREE(cur_task->command_uuid);
        cur_task->command_uuid = NULL;
    }

    if (cur_task->action) {
        ADMIN_PROBE_FREE(cur_task->action);
        cur_task->action = NULL;
    }

    if (cur_task->target) {
        ADMIN_PROBE_FREE(cur_task->target);
        cur_task->target = NULL;
    }

    if (cur_task->file_location) {
        ADMIN_PROBE_FREE(cur_task->file_location);
        cur_task->file_location = NULL;
    }

    if (cur_task->bucket_name) {
        ADMIN_PROBE_FREE(cur_task->bucket_name);
        cur_task->bucket_name = NULL;
    }

    if (cur_task->additional_arguments) {
        ADMIN_PROBE_FREE(cur_task->additional_arguments);
        cur_task->additional_arguments = NULL;
    }

    if (cur_task->result_txt) {
        ADMIN_PROBE_FREE(cur_task->result_txt);
        cur_task->result_txt = NULL;
    }

    if (cur_task->result_json) {
        admin_probe_task_module_free_serialized_str_using_parson_allocator(cur_task->result_json);
        cur_task->result_json = NULL;
    }

    admin_probe_task_module_common_free_common_state(&(cur_task->common_state));

    cur_task->callouts->free_f(cur_task);

    ADMIN_PROBE_FREE(cur_task);
    return ADMIN_PROBE_TRUE;
}


static void admin_probe_task_module_task_in_task_termination_state_but_send_termination_status_again(struct admin_probe_task* cur_task,
                                                                                                     enum admin_probe_task_state state) {

    if (!cur_task->command_uuid) {
        ADMIN_PROBE_LOG(AL_ERROR, "no command_uuid, can not send PROCESSING status report again");
        return;
    }

    if (!cur_task->action) {
        ADMIN_PROBE_LOG(AL_ERROR, "%s: no action, can not send PROCESSING status report again", cur_task->command_uuid);
        return;
    }

    int res;

    char *termination_status = NULL;
    if (task_cancelled_sent == state) {
        termination_status = ADMIN_PROBE_STATUS_CANCELLED;
    } else if (task_complete_sent == state) {
        termination_status = ADMIN_PROBE_STATUS_COMPLETED;
    } else if (task_failed_sent == state) {
        termination_status = ADMIN_PROBE_STATUS_FAILED;
    } else if (task_timeout_sent == state) {
        termination_status = ADMIN_PROBE_STATUS_TIMED_OUT;
    } else {
        ADMIN_PROBE_LOG(AL_ERROR, "%s : invalid termination status %d - not set by us, can not send again", cur_task->command_uuid, state);
        return;
    }

    res = admin_probe_task_module_send_status_report_for_task(cur_task, termination_status);
    if (res) {
        cur_task->need_send_termination_status = 1;
    } else {
        cur_task->need_send_termination_status = 0;
        ADMIN_PROBE_DEBUG_TASK_MODULE("%s : resent status report %s",cur_task->command_uuid, termination_status);
        if (!cur_task->is_first_termination_status_report_sent) {
            admin_probe_task_module_update_cur_task_field_on_sending_first_termination_status_report(cur_task);
        }
        cur_task->last_termination_sent_local_s = epoch_s();
        stats.number_of_termination_status_resend++;

        //updating internal stats
        if (task_cancelled_sent == state) {
            stats.number_of_cancelled_status_sent++;
        } else if (task_complete_sent == state) {
            stats.number_of_completed_status_sent++;
        } else if (task_failed_sent == state) {
            stats.number_of_failed_status_sent++;
        } else if (task_timeout_sent == state) {
            stats.number_of_timeout_status_sent++;
        }
    }
}


/*
 * This is the event triggering in our state machine every 1 second.
 * it traverse all the tasks in the queue, and do the action based on the cur_task->cur_state.
 */
#define MAX_TASK_CAN_BE_SPINNED_UP_PER_ONE_ROUND 1000
void admin_probe_task_module_check_tasks(evutil_socket_t sock, short flags, void *cookie)
{

    struct admin_probe_task *cur_task;
    struct admin_probe_task *tmp_task;
    int number_of_task_has_been_checked_this_round;

    number_of_task_has_been_checked_this_round = 0;
    ZTAILQ_FOREACH_SAFE(cur_task, &(task_module_state.task_queue), task_list_entry, tmp_task) {
        if (number_of_task_has_been_checked_this_round > MAX_TASK_CAN_BE_SPINNED_UP_PER_ONE_ROUND) {
            //TODO, this check should be enhanced if possible
            stats.number_of_tasks_checked_exceed_limit++;
            ADMIN_PROBE_DEBUG_TASK_MODULE("we have checked %d tasks from the queue, returning now", number_of_task_has_been_checked_this_round);
            return;
        }
        if (cur_task->cur_state == task_free) {
            ZTAILQ_REMOVE(&(task_module_state.task_queue), cur_task, task_list_entry);
            task_module_state.number_of_tasks_in_task_queue--;
            if (ADMIN_PROBE_FALSE == admin_probe_task_module_free_this_task_done(cur_task)) {
                ZTAILQ_INSERT_TAIL(&(task_module_state.task_queue), cur_task, task_list_entry);
                task_module_state.number_of_tasks_in_task_queue++;
            } else {
                task_module_state.number_of_tasks_in_state[task_free]--;
                stats.number_of_task_destroyed++;
            }
        } else {
            admin_probe_task_module_probe_process_event(NULL, cur_task->is_np_command_probe, cur_task, cur_task->command_uuid, internal_check_task);
        }

        number_of_task_has_been_checked_this_round++;
    }

}



#define MAX_RETRIES_ALLOWED_TO_EXEC_PER_TASK 3
static int admin_probe_task_module_check_task_execute_from_pending(struct admin_probe_task *cur_task)
{
    if (!cur_task) {
        ADMIN_PROBE_LOG(AL_ERROR, "invalid cur_task, can not execute this task");
        return ADMIN_PROBE_FALSE;
    }

    if (!cur_task->command_uuid) {
        ADMIN_PROBE_LOG(AL_ERROR, "invalid command_uuid, can not execute this task");
        return ADMIN_PROBE_FALSE;
    }

    if (!cur_task->action) {
        ADMIN_PROBE_LOG(AL_ERROR, "invalid action, can not execute this task");
        return ADMIN_PROBE_FALSE;
    }

    int res;

    res = cur_task->callouts->process_f(cur_task);

    if ((res == ADMIN_PROBE_TASK_LIMIT_REACHED) || (res == ADMIN_PROBE_TASK_NOT_READY_TO_EXEC)) {
        return ADMIN_PROBE_FALSE;
    }

    if (res == ADMIN_PROBE_RESULT_BAD_STATE) {
        /* currently in pending state
         * once following event return, task will move to task_cancelled_sent state.
         */
        admin_probe_task_module_probe_process_event(NULL, cur_task->is_np_command_probe, cur_task, cur_task->command_uuid, internal_err);
        return ADMIN_PROBE_FALSE;
    }

    if (res != ADMIN_PROBE_RESULT_NO_ERROR) {
        if (++cur_task->number_of_retries_executing > MAX_RETRIES_ALLOWED_TO_EXEC_PER_TASK) {
            ADMIN_PROBE_LOG(AL_ERROR, "%s : action %s : %s has failed %d time to execute, giving up now, move to task_cancelled_sent state", cur_task->command_uuid,
                                                                                                                                                  cur_task->action,
                                                                                                                                                  cur_task->target? cur_task->target: "target = null",
                                                                                                                                                  MAX_RETRIES_ALLOWED_TO_EXEC_PER_TASK);
            admin_probe_task_module_probe_process_event(NULL, cur_task->is_np_command_probe, cur_task, cur_task->command_uuid, internal_err);
            return ADMIN_PROBE_FALSE;
        } else {
            ADMIN_PROBE_LOG(AL_ERROR, "%s : failed to execute task %s, will retry later", cur_task->command_uuid, cur_task->action);
            return ADMIN_PROBE_FALSE;
        }
    }

    ADMIN_PROBE_DEBUG_TASK_MODULE("%s : execute timer: successfully executed task %s",cur_task->command_uuid,  cur_task->action);
    if (!cur_task->start_time_cloud_s) cur_task->start_time_cloud_s = admin_probe_callbacks.get_current_cloud_epoch_s_cb();

    return ADMIN_PROBE_TRUE;

}


static int admin_probe_task_module_check_task_in_processing(struct admin_probe_task *cur_task)
{
    if (!cur_task) {
        ADMIN_PROBE_LOG(AL_ERROR, "invalid cur_task, nothign to check in processing state");
        return ADMIN_PROBE_VALID;
    }

    int res = ADMIN_PROBE_RESULT_NO_ERROR;

    res = cur_task->callouts->timeout_in_processing_f(cur_task);

    if (res) {
        return ADMIN_PROBE_INVALID;
    }

    return ADMIN_PROBE_VALID;

}


static int admin_probe_task_module_check_if_ok_to_move_to_reaping_from_termination(struct admin_probe_task *cur_task)
{
    if (!cur_task) {
        ADMIN_PROBE_LOG(AL_ERROR, "invalid cur_task, can not check this task");
        return ADMIN_PROBE_FALSE;
    }

    int64_t cur_local_s;

    cur_local_s = epoch_s();

    if (!cur_task->termination_status_resend_expiry_s) {
        cur_task->termination_status_resend_expiry_s = admin_probe_rate_limiting_get_termination_resend_expiry_general_s();
    }

    if (cur_task->need_send_termination_status) {
        admin_probe_task_module_task_in_task_termination_state_but_send_termination_status_again(cur_task, cur_task->cur_state);

    } else if ((cur_local_s - cur_task->first_termination_sent_local_s) > cur_task->termination_status_resend_expiry_s) {
        /*
         * lets not wait for the ack from db, and directly going to reaping state.
         * even though db has this entry not in termination status,
         * when connector restarts, when this entry come, we will ignore it as its too old.
         */
        return ADMIN_PROBE_TRUE;
    } else if ((cur_local_s - cur_task->last_termination_sent_local_s) > cur_task->next_termination_status_resend_time_interval_s) {
        /*we have not yet received an ack from kafka, its time to resend the termination status report*/
        admin_probe_task_module_task_in_task_termination_state_but_send_termination_status_again(cur_task, cur_task->cur_state);

        cur_task->next_termination_status_resend_time_interval_s = admin_probe_task_module_get_next_termination_resend_interval_s(cur_task->next_termination_status_resend_time_interval_s,
                                                                                                                                  cur_task->command_uuid,
                                                                                                                                  cur_task->type);
    }

    return ADMIN_PROBE_FALSE;
}


#define TIMEOUT_IN_REAPING_QUEUE_S 60*60 /*1 hr*/
static int admin_probe_task_module_check_if_ok_to_move_to_free_from_reaping(struct admin_probe_task *cur_task)
{
    if (!cur_task) {
        ADMIN_PROBE_LOG(AL_ERROR, "invalid cur_task, can not check this task");
        return ADMIN_PROBE_FALSE;
    }

    int cur_local_s;

    cur_local_s = epoch_s();

    if (!cur_task->cleanup_criteria.is_execute_callback_outstanding &&
        !cur_task->cleanup_criteria.is_expired_callback_outstanding &&
        !cur_task->cleanup_criteria.is_uploader_callback_outstanding) {
            return ADMIN_PROBE_TRUE;
    }

    if ((cur_local_s - cur_task->task_creation_local_time_s) > TIMEOUT_IN_REAPING_QUEUE_S) {
        return ADMIN_PROBE_TRUE;
    }

    return ADMIN_PROBE_FALSE;
}


static int admin_probe_task_module_action_on_restart(struct admin_probe_task *cur_task)
{

    if (!cur_task) {
        ADMIN_PROBE_LOG(AL_ERROR, "Entering restart routine with invalid arguments -- cur_task");
        return ADMIN_PROBE_RESULT_BAD_ARGUMENT;
    }

    if (!cur_task->command_uuid || !cur_task->action) {
        ADMIN_PROBE_LOG(AL_ERROR, "Entering restart routine with invalid arguments -- command_uuid/actions in command_probe");
        return ADMIN_PROBE_RESULT_BAD_ARGUMENT;
    }

    int res;

    ADMIN_PROBE_DEBUG_TASK_MODULE("%s : admin_probe_task_module : preparing to perform %s, pausing task module state", cur_task->command_uuid, task_type_str[cur_task->type]);

    ADMIN_PROBE_ASSERT_SOFT((admin_probe_callbacks.get_current_cloud_epoch_s_cb != NULL), "no cb to get current cloud time!!");
    cur_task->start_time_cloud_s = admin_probe_callbacks.get_current_cloud_epoch_s_cb();

    if (admin_probe_callbacks.restart_cb) {
        if (cur_task->type == admin_probe_task_type_restart_process) {
            ADMIN_PROBE_DEBUG_TASK_MODULE("%s : admin_probe_task_module : preparing restart process, triggering process restart_cb", cur_task->command_uuid);
            res = admin_probe_callbacks.restart_cb(process_restart);
        } else if (cur_task->type == admin_probe_task_type_restart_system) {
            ADMIN_PROBE_DEBUG_TASK_MODULE("%s : admin_probe_task_module : preparing restart system, triggering system restart_cb", cur_task->command_uuid);
            res = admin_probe_callbacks.restart_cb(system_restart);
        } else {
            stats.action_on_restart_reaching_unexpected_branch++;
            res = ADMIN_PROBE_RESULT_BAD_ARGUMENT;
        }

        if (res) {
            ADMIN_PROBE_LOG(AL_ERROR, "%s : tried restart,  but failed", cur_task->command_uuid);
            stats.number_of_restart_cb_failed++;
            return ADMIN_PROBE_RESULT_ERR;
        }
    } else {
        ADMIN_PROBE_LOG(AL_ERROR, "%s : No restart callback provided, could not restart, canceling task", cur_task->command_uuid);
        stats.restart_failed_because_of_no_callback_provided++;
        return ADMIN_PROBE_RESULT_ERR;
    }

    //we will not check the return code for sending processing status report, if it fails, we will not resend as it will messed up with termination status later.
    /* coverity[check_return : Intentional] */
    admin_probe_task_module_send_status_report_for_task(cur_task, ADMIN_PROBE_STATUS_PROCESSING);
    stats.number_of_processing_status_sent++;

    task_module_state.is_task_module_state_pause_for_restart = 1;

    return ADMIN_PROBE_RESULT_NO_ERROR;
}


/*
 * For new task command, we only take the commmand if it passes below creatime_time validation check.
 * For a new restart probe command, it has to be within 5 seconds old by default [FXIME: should be configurable]
 * For the rest, its has to be within 5mins [should be configurable]
 */
static int admin_probe_task_module_validate_task_creation_time(enum admin_probe_task_type type, int64_t creation_time_s, const char* status, int64_t request_rx_time)
{
    ADMIN_PROBE_ASSERT_SOFT((admin_probe_callbacks.get_current_cloud_epoch_s_cb != NULL), "admin probe - no cb to get cur cloud time");
    int64_t cur_cloud_time_s;
    int64_t task_created_for_how_long_s;
    int64_t valid_timeframe_s;

    cur_cloud_time_s = admin_probe_callbacks.get_current_cloud_epoch_s_cb();

    task_created_for_how_long_s = cur_cloud_time_s - creation_time_s;

    if (type == admin_probe_task_type_restart_process || type == admin_probe_task_type_restart_system) {

        if (0 == strcmp(status, ADMIN_PROBE_STATUS_PENDING)) {

            /*
             * at this point, we receives a restart probe in pending state.
             * we want to validate the creation time of the request, if its too old, we will not take the request.
             * but prior to the validation, we will check the current connector uptime.
             * if the connector uptime is less than 2 mins, we will not proceed with this new restart task and cancel the task
             * with the reason ADMIN_PROBE_CANCEL_RESTART_REQUEST_DUE_TO_ENTITY_JUST_GOT_RESTARTED
             */
            if (request_rx_time && ((request_rx_time - admin_probe_state.admin_probe_init_time_s) < ADMIN_PROBE_TASK_MODULE_GRACEFUL_PERIOD_FOR_NOT_TAKING_PENDING_RESTART_COMMAND_S)) {
                return ADMIN_PROBE_VALID;
            } else {
                valid_timeframe_s = admin_probe_rate_limiting_get_task_creation_validation_allowed_time_for_restart_s();
            }

        } else if (0 == strcmp(status ,ADMIN_PROBE_STATUS_PROCESSING)) {

            valid_timeframe_s = admin_probe_rate_limiting_get_task_creation_validation_allowed_time_in_general_s();

        } else {
            valid_timeframe_s = 0;
        }
        if (task_created_for_how_long_s > valid_timeframe_s) {
            ADMIN_PROBE_DEBUG_TASK_MODULE("%s : creation_time_s validation failed, cur_cloud_times(%"PRId64") - creation_time_s(%"PRId64") > %"PRId64" ", task_type_str[type],
                                                                                                                                              cur_cloud_time_s,
                                                                                                                                              creation_time_s,
                                                                                                                                              valid_timeframe_s);
            return ADMIN_PROBE_INVALID;
        } else {
            return ADMIN_PROBE_VALID;
        }
    } else if (type == admin_probe_task_type_tcpdump) {
        valid_timeframe_s = admin_probe_rate_limiting_get_task_creation_validation_allowed_time_in_tcpdump_s();
        if (task_created_for_how_long_s > valid_timeframe_s) {
            ADMIN_PROBE_DEBUG_TASK_MODULE("%s : creation_time_s validation failed, cur_cloud_times(%"PRId64") - creation_time_s(%"PRId64") > %"PRId64"", task_type_str[type],
                                                                                                                                             cur_cloud_time_s,
                                                                                                                                             creation_time_s,
                                                                                                                                             valid_timeframe_s);
            return ADMIN_PROBE_INVALID;
        } else {
            return ADMIN_PROBE_VALID;
        }

    } else {
        valid_timeframe_s = admin_probe_rate_limiting_get_task_creation_validation_allowed_time_in_general_s();
        if (task_created_for_how_long_s > valid_timeframe_s) {
            ADMIN_PROBE_DEBUG_TASK_MODULE("%s : creation_time_s validation failed, cur_cloud_times(%"PRId64") - creation_time_s(%"PRId64") > %"PRId64"", task_type_str[type],
                                                                                                                                             cur_cloud_time_s,
                                                                                                                                             creation_time_s,
                                                                                                                                             valid_timeframe_s);
            return ADMIN_PROBE_INVALID;
        } else {
            return ADMIN_PROBE_VALID;
        }
    }

    return ADMIN_PROBE_INVALID;
}

static int admin_probe_task_module_validate_feature_flag(enum admin_probe_task_type type)
{
    if (!admin_probe_callbacks.feature_flag_check_cb) {
        ADMIN_PROBE_LOG(AL_ERROR, "no feature flag requried, will proceed to take the request");
        return ADMIN_PROBE_VALID;
    }

    if (!admin_probe_callbacks.feature_flag_check_cb(type)) {
        ADMIN_PROBE_LOG(AL_ERROR, "feature flag for %s is not enabled, can not proceed to process the probe request", task_type_str[type]);
        return ADMIN_PROBE_INVALID;
    }

    return ADMIN_PROBE_VALID;
}

static int admin_probe_task_module_is_task_queue_full()
{

    int64_t max_tasks_allowed_to_hold_in_queue = admin_probe_rate_limiting_overall_max_tasks_allow_in_task_queue();
    if (task_module_state.number_of_tasks_in_task_queue >= max_tasks_allowed_to_hold_in_queue) {
        return ADMIN_PROBE_TRUE;
    } else {
        return ADMIN_PROBE_FALSE;
    }
}


struct admin_probe_task* admin_probe_task_module_create_new_task_alloc(const char* command_uuid,
                                                                       int64_t entity_gid,
                                                                       int64_t customer_gid,
                                                                       const char* action,
                                                                       const char* target,
                                                                       const char* additional_arguments,
                                                                       int32_t duration_in_secs,
                                                                       const char* file_location,
                                                                       const char* bucket_name,
                                                                       int64_t creation_time,
                                                                       int is_ut,
                                                                       enum admin_probe_task_type type)
{
    struct admin_probe_task* cur_task;

    cur_task = ADMIN_PROBE_CALLOC(sizeof(*cur_task));
    if (!cur_task) {
        ADMIN_PROBE_LOG(AL_ERROR, "%s : can not allocate memory", command_uuid);
        return NULL;
    }

    if (command_uuid) cur_task->command_uuid = ADMIN_PROBE_STRDUP(command_uuid, strlen(command_uuid));
    if (action) cur_task->action = ADMIN_PROBE_STRDUP(action, strlen(action));
    if (file_location) cur_task->file_location = ADMIN_PROBE_STRDUP(file_location, strlen(file_location));
    if (bucket_name) cur_task->bucket_name = ADMIN_PROBE_STRDUP(bucket_name, strlen(bucket_name));
    if (target) cur_task->target = ADMIN_PROBE_STRDUP(target, strlen(target));
    if (additional_arguments) cur_task->additional_arguments = ADMIN_PROBE_STRDUP(additional_arguments, strlen(additional_arguments));

    cur_task->type = type;
    cur_task->customer_gid = customer_gid;
    cur_task->entity_gid = entity_gid;
    cur_task->creation_time = creation_time;
    cur_task->duration_in_secs = duration_in_secs;
    cur_task->task_creation_local_time_s = epoch_s();

    cur_task->is_ut = is_ut;

    switch (type)
    {
    case admin_probe_task_type_dns:
        cur_task->callouts = &admin_probe_task_module_callouts_dns;
        break;
    case admin_probe_task_type_icmp:
        cur_task->callouts = &admin_probe_task_module_callouts_icmp;
        break;
    case admin_probe_task_type_tcp:
        cur_task->callouts = &admin_probe_task_module_callouts_tcp;
        break;
    case admin_probe_task_type_tcpdump:
        cur_task->callouts = &admin_probe_task_module_callouts_tcpdump;
    break;
    case admin_probe_task_type_restart_process:
    case admin_probe_task_type_restart_system:
        cur_task->callouts = &admin_probe_task_module_callouts_restart;
        break;
    case admin_probe_task_type_ip_route:
    case admin_probe_task_type_ip_interfaces:
    case admin_probe_task_type_ip_bgp:
    case admin_probe_task_type_ip_bgp_neighbors:
    case admin_probe_task_type_ip_bgp_summary:
    case admin_probe_task_type_ip_clear_bgp:
    case admin_probe_task_type_bgp_running_config:
    case admin_probe_task_type_bgp_failed_config:
    case admin_probe_task_type_bgp_generated_config:
    case admin_probe_task_type_bgp_config_validate:
    case admin_probe_task_type_bgp_config_status:
    case admin_probe_task_type_bgp_config_status_details:
    case admin_probe_task_type_bgp_get_logs:
    case admin_probe_task_type_stop_bgp:
    case admin_probe_task_type_start_bgp:
    case admin_probe_task_type_restart_bgp:
    case admin_probe_task_type_status_bgp:
    case admin_probe_task_type_reload_bgp:
        cur_task->callouts = &admin_probe_task_module_callouts_frr_cmds;
        break;
    default:
        cur_task->callouts = &admin_probe_task_module_callouts_default;
        break;
    }

    return cur_task;
}

enum admin_probe_task_type admin_probe_task_module_get_task_type(const char* action)
{
    if (!action) {
        ADMIN_PROBE_LOG(AL_ERROR, "invalid status, can not get task type");
        return admin_probe_invalid_task_type;
    }

    if (0 ==strcmp(action, ADMIN_PROBE_ACTION_RESTART_PROCESS)) {
        return admin_probe_task_type_restart_process;
    } else if (0 ==strcmp(action, ADMIN_PROBE_ACTION_RESTART_SYSTEM)) {
        return admin_probe_task_type_restart_system;
    } else if (0 ==strcmp(action, ADMIN_PROBE_ACTION_DNSLOOKUP_A_RECORD) ||
               0 ==strcmp(action, ADMIN_PROBE_ACTION_DNSLOOKUP_AAAA_RECORD) ||
               0 ==strcmp(action, ADMIN_PROBE_ACTION_DNSLOOKUP_SRV_RECORD)) {
        return admin_probe_task_type_dns;
    } else if (0 ==strcmp(action, ADMIN_PROBE_ACTION_ICMP_PING)) {
        return admin_probe_task_type_icmp;
    } else if (0 ==strcmp(action, ADMIN_PROBE_ACTION_TCP_PING)) {
        return admin_probe_task_type_tcp;
    } else if (0 ==strcmp(action, ADMIN_PROBE_ACTION_TCPDUMP)) {
        return admin_probe_task_type_tcpdump;
    } else if (0 ==strcmp(action, ADMIN_PROBE_ACTION_IP_ROUTE)) {
        return admin_probe_task_type_ip_route;
    } else if (0 ==strcmp(action, ADMIN_PROBE_ACTION_IP_INTERFACES)) {
        return admin_probe_task_type_ip_interfaces;
    } else if (0 ==strcmp(action, ADMIN_PROBE_ACTION_IP_BGP)) {
        return admin_probe_task_type_ip_bgp;
    } else if (0 ==strcmp(action, ADMIN_PROBE_ACTION_IP_BGP_NEIGHBORES)) {
        return admin_probe_task_type_ip_bgp_neighbors;
    } else if (0 ==strcmp(action, ADMIN_PROBE_ACTION_IP_BGP_SUMMARY)) {
        return admin_probe_task_type_ip_bgp_summary;
    } else if (0 ==strcmp(action, ADMIN_PROBE_ACTION_CLEAR_BGP)) {
        return admin_probe_task_type_ip_clear_bgp;
    } else if (0 ==strcmp(action, ADMIN_PROBE_ACTION_RUNNING_CONFIG)) {
        return admin_probe_task_type_bgp_running_config;
    } else if (0 ==strcmp(action, ADMIN_PROBE_ACTION_FAIELD_CONFIG)) {
        return admin_probe_task_type_bgp_failed_config;
    } else if (0 ==strcmp(action, ADMIN_PROBE_ACTION_GENERATED_CONFIG)) {
        return admin_probe_task_type_bgp_generated_config;
    } else if (0 ==strcmp(action, ADMIN_PROBE_ACTION_CONFIG_VALIDATE)) {
        return admin_probe_task_type_bgp_config_validate;
    } else if (0 ==strcmp(action, ADMIN_PROBE_ACTION_CONFIG_STATUS)) {
        return admin_probe_task_type_bgp_config_status;
    } else if (0 ==strcmp(action, ADMIN_PROBE_ACTION_CONFIG_STATUS_DETAILS)) {
        return admin_probe_task_type_bgp_config_status_details;
    } else if (0 ==strcmp(action, ADMIN_PROBE_ACTION_GET_LOGS)) {
        return admin_probe_task_type_bgp_get_logs;
    } else if (0 ==strcmp(action, ADMIN_PROBE_ACTION_STOP_BGP)) {
        return admin_probe_task_type_stop_bgp;
    } else if (0 ==strcmp(action, ADMIN_PROBE_ACTION_START_BGP)) {
        return admin_probe_task_type_start_bgp;
    } else if (0 ==strcmp(action, ADMIN_PROBE_ACTION_RESTART_BGP)) {
        return admin_probe_task_type_restart_bgp;
    } else if (0 ==strcmp(action, ADMIN_PROBE_ACTION_STATUS_BGP)) {
        return admin_probe_task_type_status_bgp;
    } else if (0 ==strcmp(action, ADMIN_PROBE_ACTION_RELOAD_BGP)) {
        return admin_probe_task_type_reload_bgp;
    } else {
        return admin_probe_invalid_task_type;
    }
}

/*
 * a task node will be created and the state will be either task_pending or task_cancelled_sent
 */
static void admin_probe_task_module_create_new_task_from_pending_event(const char* command_uuid,
                                                                       int64_t entity_gid,
                                                                       int64_t customer_gid,
                                                                       const char* action,
                                                                       const char* status,
                                                                       const char* target,
                                                                       const char* additional_arguments,
                                                                       int32_t duration_in_secs,
                                                                       const char* file_location,
                                                                       const char* bucket_name,
                                                                       int64_t creation_time_s,
                                                                       int is_ut,
                                                                       enum admin_probe_task_event event_type,
                                                                       int is_np_command_probe)
{
    struct admin_probe_task* cur_task;
    enum admin_probe_task_type type;
    int64_t request_rx_us;
    int res;

    if (!command_uuid) {
        ADMIN_PROBE_LOG(AL_ERROR, "invalid argument -- command_uuid, can not create new task from pending event");
        goto create_new_task_failed;
    }

    if (!action) {
        ADMIN_PROBE_LOG(AL_ERROR, "%s : invalid argument -- action, can not create new task from pending event", command_uuid);
        goto create_new_task_failed;
    }

    type = admin_probe_task_module_get_task_type(action);
    if (type == admin_probe_invalid_task_type) {
        /*api side should have mechnism cleaning up these kind of bad requests*/
        ADMIN_PROBE_LOG(AL_ERROR, "%s : rx admin probe command with invalid task type %s", command_uuid, action);
        stats.new_task_failed_at_task_type_check++;
        return;
    }

    request_rx_us = admin_probe_callbacks.get_current_cloud_epoch_s_cb();

    if (ADMIN_PROBE_INVALID == admin_probe_task_module_validate_task_creation_time(type, creation_time_s, status, request_rx_us)) {
        /*task is too old, we will not do anything, api side should have mechnism cleaning up these kind of old requests*/
        stats.new_task_failed_at_creation_time_validation_check++;
        return;
    }

    cur_task = admin_probe_task_module_create_new_task_alloc(command_uuid, entity_gid, customer_gid, action, target, additional_arguments,
                                                             duration_in_secs, file_location, bucket_name, creation_time_s, is_ut, type);
    if (!cur_task) {
        ADMIN_PROBE_LOG(AL_ERROR, "%s : can not allocate memory for new task %s", command_uuid, action);
        goto create_new_task_failed;
    }
    cur_task->is_np_command_probe = is_np_command_probe;


    res = zhash_table_store(task_module_state.task_hash_by_command_uuid, cur_task->command_uuid, strlen(cur_task->command_uuid), 0, cur_task);
    if (res) {
        ADMIN_PROBE_LOG(AL_ERROR, "%s : action : %s could not store the new task into hash table, ignoreing the task", cur_task->command_uuid, cur_task->action);
        ADMIN_PROBE_FREE(cur_task); //FIXME, MORE TO FREE
        goto create_new_task_failed;
    }
    stats.total_number_of_task_created++;

    ZTAILQ_INSERT_TAIL(&(task_module_state.task_queue), cur_task, task_list_entry);
    task_module_state.number_of_tasks_in_task_queue++;

    if (ADMIN_PROBE_INVALID == admin_probe_task_module_validate_feature_flag(type)) {
        cur_task->err_message = ADMIN_PROBE_FEATURE_NOT_ENABLED;
        goto task_cancel_state;
    }

    if (ADMIN_PROBE_TRUE == admin_probe_task_module_is_task_queue_full()) {
        ADMIN_PROBE_LOG(AL_DEBUG, "%s : cancelling new task due to current task queue if full at limit %"PRId64" ", command_uuid, task_module_state.number_of_tasks_in_task_queue);
        cur_task->err_message = ADMIN_PROBE_COMMAND_CANCELLED_TASKS_QUEUE_FULL;
        goto task_cancel_state;
    }


    switch (type) {
        case admin_probe_task_type_restart_process:
        case admin_probe_task_type_restart_system:
            /*
             * here we are receiving the new restart probe request in pending state.
             * but we notice that the connector has just been up for 2mins.
             * thus we will not proceed with this request and cancel the task saying ADMIN_PROBE_CANCEL_RESTART_REQUEST_DUE_TO_ENTITY_JUST_GOT_RESTARTED
             */
            if ((request_rx_us - admin_probe_state.admin_probe_init_time_s) < ADMIN_PROBE_TASK_MODULE_GRACEFUL_PERIOD_FOR_NOT_TAKING_PENDING_RESTART_COMMAND_S) {
                cur_task->err_message = ADMIN_PROBE_CANCEL_RESTART_REQUEST_DUE_TO_ENTITY_JUST_GOT_RESTARTED;
                stats.restart_command_cancel++;
                goto task_cancel_state;
            }

            res = admin_probe_task_module_action_on_restart(cur_task);
            if (res) {
                ADMIN_PROBE_LOG(AL_ERROR, "%s : rx %s command probe, but system failed to restart", command_uuid, action);
                cur_task->err_message = ADMIN_PROBE_CAN_NOT_RESTART;

                goto task_cancel_state;
            } else {
                cur_task->cur_state = task_processing_sent;
                ADMIN_PROBE_LOG(AL_NOTICE, "%s : Triggerd restart successfully, restart will happening soon .. ", cur_task->command_uuid);
                return;
            }
        default:
            res = cur_task->callouts->init_f(cur_task);
            if (res) {
                ADMIN_PROBE_LOG(AL_DEBUG, "%s : failed to initialize %s admin probe request, cancelling task", command_uuid, action);
                goto task_cancel_state;
            }
            //TODO: we can triggerthe internal_check event right after we add the new task to pending state. that way we can have task being executed faster.
            ADMIN_PROBE_DEBUG_TASK_MODULE("%s : added new probe %s to pending queue", command_uuid, action);
            cur_task->cur_state = task_pending;
            task_module_state.number_of_tasks_in_state[task_pending]++;
            break;
    }

    return;

create_new_task_failed:
    stats.create_new_task_failed++;
    return;
task_cancel_state:
    cur_task->start_time_cloud_s = admin_probe_callbacks.get_current_cloud_epoch_s_cb();
    cur_task->end_time_cloud_s = cur_task->start_time_cloud_s;

    res = admin_probe_task_module_send_status_report_for_task(cur_task, ADMIN_PROBE_STATUS_CANCELLED);
    if (res) {
        cur_task->need_send_termination_status = 1;
    } else {
        cur_task->need_send_termination_status = 0;
        admin_probe_task_module_update_cur_task_field_on_sending_first_termination_status_report(cur_task);
        stats.number_of_cancelled_status_sent++;
    }


    cur_task->cur_state = task_cancelled_sent;
    stats.total_number_of_task_cancelled++;
    task_module_state.number_of_tasks_in_state[task_cancelled_sent]++;
    return;
}

static void admin_probe_task_module_create_new_task_from_processing_event(const char* command_uuid,
                                                                          int64_t entity_gid,
                                                                          int64_t customer_gid,
                                                                          const char* action,
                                                                          const char* status,
                                                                          const char* target,
                                                                          const char* additional_arguments,
                                                                          int32_t duration_in_secs,
                                                                          const char* file_location,
                                                                          const char* bucket_name,
                                                                          int64_t creation_time_s,
                                                                          enum admin_probe_task_event event_type,
                                                                          int is_np_command_probe)
{
    struct admin_probe_task* cur_task;
    enum admin_probe_task_type type;
    int res;

    if (!command_uuid) {
        ADMIN_PROBE_LOG(AL_ERROR, "invalid argument -- command_uuid, can not create new task from processing event");
        goto create_new_task_failed;
    }

    if (!action) {
        ADMIN_PROBE_LOG(AL_ERROR, "%s : invalid argument -- action, can not create new task from processing event", command_uuid);
        goto create_new_task_failed;
    }

    type = admin_probe_task_module_get_task_type(action);
    if (type == admin_probe_invalid_task_type) {
        /*api side should have mechnism cleaning up these kind of bad requests*/
        ADMIN_PROBE_LOG(AL_ERROR, "%s : rx admin probe command with invalid task type %s", command_uuid, action);
        stats.new_task_failed_at_task_type_check++;
        return;
    }

    if (ADMIN_PROBE_INVALID == admin_probe_task_module_validate_task_creation_time(type, creation_time_s, status, 0)) {
        /*task is too old, we will not do anything, api side should have mechnism cleaning up these kind of old requests*/
        stats.new_task_failed_at_creation_time_validation_check++;
        return;
    }

    cur_task = admin_probe_task_module_create_new_task_alloc(command_uuid, entity_gid, customer_gid, action, target, additional_arguments,
                                                             duration_in_secs, file_location, bucket_name, creation_time_s, 0, type);
    if (!cur_task) {
        ADMIN_PROBE_LOG(AL_ERROR, "%s : can not allocate memory for new task %s", command_uuid, action);
        goto create_new_task_failed;
    }

    cur_task->is_np_command_probe = is_np_command_probe;

    res = zhash_table_store(task_module_state.task_hash_by_command_uuid, cur_task->command_uuid, strlen(cur_task->command_uuid), 0, cur_task);
    if (res) {
        ADMIN_PROBE_LOG(AL_ERROR, "%s : action : %s could not store the new task into hash table, ignoreing the task", cur_task->command_uuid, cur_task->action);
        ADMIN_PROBE_FREE(cur_task);
        goto create_new_task_failed;
    }
    stats.total_number_of_task_created++;

    ZTAILQ_INSERT_TAIL(&(task_module_state.task_queue), cur_task, task_list_entry);
    task_module_state.number_of_tasks_in_task_queue++;

    if (ADMIN_PROBE_INVALID == admin_probe_task_module_validate_feature_flag(type)) {
        cur_task->err_message = ADMIN_PROBE_FEATURE_NOT_ENABLED;
        goto task_cancel_state;
    }

    if (ADMIN_PROBE_TRUE == admin_probe_task_module_is_task_queue_full()) {
        ADMIN_PROBE_LOG(AL_DEBUG, "%s : cancelling new task due to current task queue if full at limit %"PRId64" ", command_uuid, task_module_state.number_of_tasks_in_task_queue);
        cur_task->err_message = ADMIN_PROBE_COMMAND_CANCELLED_TASKS_QUEUE_FULL;
        goto task_cancel_state;
    }

    switch (type) {
        case admin_probe_task_type_restart_process:
        case admin_probe_task_type_restart_system:
            /*
             * this is a special case for restart.
             * for a restart command, we send a processing status and then restart.
             * after we comeback restarted, the command comeback to us with status 'PROCESSING'
             * and we have to send a comleted for that command as we have now restarted.
             *
             * before we send the complete status for that, we move the restart command to correct state machine which is 'task_processing_sent'
             */
            cur_task->cur_state = task_processing_sent;
            task_module_state.number_of_tasks_in_state[task_processing_sent]++;

            if ((admin_probe_state.admin_probe_init_time_s - creation_time_s) < ADMIN_PROBE_TASK_MODULE_MAX_ALLOWED_TIME_FOR_ADMIN_PROBE_TO_COMEBACK_AFTER_A_RESTART_S)
            {
                stats.receiving_processing_on_restart_within_time_limit++;
                cur_task->end_time_cloud_s = admin_probe_callbacks.get_current_cloud_epoch_s_cb();
                admin_probe_task_module_probe_process_event(NULL, cur_task->is_np_command_probe, cur_task, cur_task->command_uuid, internal_task_execute_done_success);
                stats.restart_command_complete++;
                return;
            } else {
                stats.receiving_processing_on_restart_exceed_time_limit++;
                admin_probe_task_module_probe_process_event(NULL, cur_task->is_np_command_probe, cur_task, cur_task->command_uuid, internal_task_execute_done_failed);
                stats.restart_command_fail++;
                return;
            }

        default:
            res = cur_task->callouts->init_f(cur_task);
            if (res) {
                ADMIN_PROBE_LOG(AL_DEBUG, "%s : failed to initialize %s admin probe request, cancelling task", command_uuid, action);
                goto task_cancel_state;
            }
            //TODO: we can triggerthe internal_check event right after we add the new task to pending state. that way we can have task being executed faster.
            ADMIN_PROBE_DEBUG_TASK_MODULE("%s : added new probe %s to pending queue", command_uuid, action);
            cur_task->cur_state = task_pending;
            task_module_state.number_of_tasks_in_state[task_pending]++;
            break;
    }

    return;
create_new_task_failed:
    stats.create_new_task_failed++;
    return;

task_cancel_state:
    cur_task->start_time_cloud_s = admin_probe_callbacks.get_current_cloud_epoch_s_cb();
    cur_task->end_time_cloud_s = cur_task->start_time_cloud_s;

    res = admin_probe_task_module_send_status_report_for_task(cur_task, ADMIN_PROBE_STATUS_CANCELLED);
    if (res) {
        cur_task->need_send_termination_status = 1;
    } else {
        cur_task->need_send_termination_status = 0;
        admin_probe_task_module_update_cur_task_field_on_sending_first_termination_status_report(cur_task);
        stats.number_of_cancelled_status_sent++;
    }

    cur_task->cur_state = task_cancelled_sent;
    stats.total_number_of_task_cancelled++;
    task_module_state.number_of_tasks_in_state[task_cancelled_sent]++;
    return;

}


static void admin_probe_task_module_process_event_state_task_not_exist(void *command_probe_arg,
                                                                       enum admin_probe_task_event event_type,
                                                                       int is_np_command_probe)
{
    if (!command_probe_arg) {
        ADMIN_PROBE_LOG(AL_ERROR, "invalid command_probe, can not proceed with action on task not exist");
        return;
    }

    switch (event_type) {
    case pending_from_wally:
        if (is_np_command_probe) {
            struct np_command_probe *command_probe = (struct np_command_probe *)command_probe_arg;
            admin_probe_task_module_create_new_task_from_pending_event(command_probe->command_uuid, command_probe->entity_gid,
                                                                   command_probe->customer_gid,  command_probe->action, command_probe->status,
                                                                   command_probe->target, command_probe->additional_arguments,
                                                                   command_probe->duration_in_secs, command_probe->file_location,
                                                                   command_probe->bucket_name, command_probe->creation_time, 0, event_type, is_np_command_probe);
        } else {
            struct zpn_command_probe *command_probe = (struct zpn_command_probe *)command_probe_arg;
            admin_probe_task_module_create_new_task_from_pending_event(command_probe->command_uuid, command_probe->entity_gid,
                                                                   command_probe->customer_gid,  command_probe->action, command_probe->status,
                                                                   command_probe->target, command_probe->additional_arguments,
                                                                   command_probe->duration_in_secs, command_probe->file_location,
                                                                   command_probe->bucket_name, command_probe->creation_time, 0, event_type, is_np_command_probe);
        }
        break;
    case processing_from_wally:
        if (is_np_command_probe) {
            struct np_command_probe *command_probe = (struct np_command_probe *)command_probe_arg;
            admin_probe_task_module_create_new_task_from_processing_event(command_probe->command_uuid, command_probe->entity_gid,
                                                                      command_probe->customer_gid,  command_probe->action, command_probe->status,
                                                                      command_probe->target, command_probe->additional_arguments,
                                                                      command_probe->duration_in_secs, command_probe->file_location,
                                                                      command_probe->bucket_name, command_probe->creation_time, event_type, is_np_command_probe);
        } else {
            struct zpn_command_probe *command_probe = (struct zpn_command_probe *)command_probe_arg;
            admin_probe_task_module_create_new_task_from_processing_event(command_probe->command_uuid, command_probe->entity_gid,
                                                                      command_probe->customer_gid,  command_probe->action, command_probe->status,
                                                                      command_probe->target, command_probe->additional_arguments,
                                                                      command_probe->duration_in_secs, command_probe->file_location,
                                                                      command_probe->bucket_name, command_probe->creation_time, event_type, is_np_command_probe);
        }
        break;
    case internal_timeout_timer:
    case internal_task_execute_done_success:
    case internal_task_execute_done_failed:
    case internal_task_upload_success:
    case internal_task_upload_failed:
        ADMIN_PROBE_LOG(AL_ERROR, "%s : unexpected event %s on task not exist state, ignoring", is_np_command_probe ?
                                                                         ((struct np_command_probe *)command_probe_arg)->command_uuid :
                                                                         ((struct zpn_command_probe *)command_probe_arg)->command_uuid,
                                                                         task_event_str[event_type]);
        stats.task_not_exist_unexpected_event++;
        break;
    case completed_from_wally:
    case failed_from_wally:
    case timed_out_from_wally:
    case cancelled_from_wally:
    case cancel_request_from_wally:
    default:
        break;

    }
}

static void admin_probe_task_module_process_event_state_task_pending(struct admin_probe_task *cur_task, enum admin_probe_task_event event_type)
{
    if (!cur_task) {
        ADMIN_PROBE_LOG(AL_ERROR, "invalid command_probe, can not proceed with event %d and state task_pending", event_type);
        return;
    }

    if (!cur_task->command_uuid) {
        ADMIN_PROBE_LOG(AL_ERROR, "invalid command_uuid, can not proceed with event %d and state task_pending", event_type);
        return;
    }

    int res;

    switch(event_type) {
    case pending_from_wally:
        ADMIN_PROBE_LOG(AL_NOTICE, "%s : task is already in PENDING queue, ignoring the PENDING message", cur_task->command_uuid);
        stats.task_already_in_pending_and_rx_pending++;
        break;
    case processing_from_wally:
        ADMIN_PROBE_LOG(AL_ERROR, "%s : task is currently in PENDING queue but receives processing from wally, ignoring the PROCESSING message", cur_task->command_uuid);
        stats.task_is_in_pending_but_rx_processing_state_from_db++;
        break;
    case completed_from_wally:
    case failed_from_wally:
    case timed_out_from_wally:
    case cancelled_from_wally:
        /*
         * we can just free the task, as it is not executed yet, no callback needed to be triggered.
         * transitioning from pending state to free state
         */
        task_module_state.number_of_tasks_in_state[task_pending]--;
        cur_task->cur_state = task_free;
        task_module_state.number_of_tasks_in_state[task_free]++;

        ADMIN_PROBE_LOG(AL_ERROR, "%s : task is currently in PENDING queue, but receives termination from db, cancelling the pending task to be in sync with db", cur_task->command_uuid);
        stats.task_is_in_pending_but_db_update_the_status_to_termination_state++;
        break;
    case cancel_request_from_wally:
        cur_task->err_message = ADMIN_PROBE_RECEIVED_CANCEL_REQUEST_FROM_USER;
        cur_task->end_time_cloud_s = admin_probe_callbacks.get_current_cloud_epoch_s_cb();
        res = admin_probe_task_module_send_status_report_for_task(cur_task, ADMIN_PROBE_STATUS_CANCELLED);
        if (res) {
            cur_task->need_send_termination_status = 1;
        } else {
            cur_task->need_send_termination_status = 0;
            admin_probe_task_module_update_cur_task_field_on_sending_first_termination_status_report(cur_task);
            stats.number_of_cancelled_status_sent++;
        }

        task_module_state.number_of_tasks_in_state[task_pending]--;
        cur_task->cur_state = task_cancelled_sent;
        stats.total_number_of_task_cancelled++;
        task_module_state.number_of_tasks_in_state[task_cancelled_sent]++;

        break;
    case internal_err:
        /*
         * something preventing us from executing this task, lets fail this task.
         */
        if (!cur_task->err_message) {
            cur_task->err_message = ADMIN_PROBE_ERR_IN_GENERAL;
        }
        cur_task->end_time_cloud_s = admin_probe_callbacks.get_current_cloud_epoch_s_cb();
        /* Reporting status as failed for internal err */
        res = admin_probe_task_module_send_status_report_for_task(cur_task, ADMIN_PROBE_STATUS_FAILED);
        if (res) {
            cur_task->need_send_termination_status = 1;
        } else {
            cur_task->need_send_termination_status = 0;
            admin_probe_task_module_update_cur_task_field_on_sending_first_termination_status_report(cur_task);
            stats.number_of_failed_status_sent++;
        }

        //transitioning from task_pending state to task_cancelled_sent_state
        task_module_state.number_of_tasks_in_state[task_pending]--;
        cur_task->cur_state = task_failed_sent;
        stats.number_of_failed_status_sent++;
        task_module_state.number_of_tasks_in_state[task_failed_sent]++;

        break;
    case internal_timeout_timer:
    case internal_task_execute_done_success:
    case internal_task_execute_done_failed:
    case internal_task_upload_success:
    case internal_task_upload_failed:
        ADMIN_PROBE_LOG(AL_ERROR, "%s : unexpected event %s for task_pending scenario, ignoring", cur_task->command_uuid, task_event_str[event_type]);
        stats.task_is_in_pending_but_rx_unexpected_event_call++;
        break;
    case internal_check_task:
        if (ADMIN_PROBE_TRUE == admin_probe_task_module_check_task_execute_from_pending(cur_task)) {
            /*
            * For synchronous FRR commands, do not change the state here.
            * These commands are designed to update the status synchronously
            * as part of their command processing callback.
            */
            if (!(cur_task->type >= admin_probe_task_type_ip_route && cur_task->type <= admin_probe_task_type_reload_bgp)) {
                /* coverity[check_return : Intentional] */
                admin_probe_task_module_send_status_report_for_task(cur_task, ADMIN_PROBE_STATUS_PROCESSING);
                stats.number_of_processing_status_sent++;

                task_module_state.number_of_tasks_in_state[task_pending]--;
                cur_task->cur_state = task_processing_sent;
                task_module_state.number_of_tasks_in_state[task_processing_sent]++;
            }
        }
    default:
        break;
    }
}

/**
 * Switches the state of the given admin probe task to "processing" for synchronous commands.
 *
 * Unlike asynchronous commands (e.g., tcpdump, dns) which inherently handle task state switching,
 * synchronous commands require explicit handling to update the task state. This function is used
 * to facilitate that state transition to "processing" for synchronous commands.
 *
 * @param cur_task Pointer to the admin probe task whose state needs to be updated.
 */
void admin_probe_task_module_switch_task_state_to_processing_for_synchronous_cmds(struct admin_probe_task *cur_task) {
    /* coverity[check_return : Intentional] */
    admin_probe_task_module_send_status_report_for_task(cur_task, ADMIN_PROBE_STATUS_PROCESSING);
    stats.number_of_processing_status_sent++;

    task_module_state.number_of_tasks_in_state[task_pending]--;
    cur_task->cur_state = task_processing_sent;
    task_module_state.number_of_tasks_in_state[task_processing_sent]++;
}

static void admin_probe_task_module_process_event_state_task_processing_sent(struct admin_probe_task *cur_task, enum admin_probe_task_event event_type)
{
    if (!cur_task) {
        ADMIN_PROBE_LOG(AL_ERROR, "invalid command_probe, can not proceed with event %d and state task_processing_sent", event_type);
        return;
    }

    if (!cur_task->command_uuid) {
        ADMIN_PROBE_LOG(AL_ERROR, "invalid command_uuid, can not proceed with event %d and state task_processing_sent", event_type);
        return;
    }

    int res;

    switch(event_type) {
    case pending_from_wally:
        ADMIN_PROBE_LOG(AL_ERROR, "%s : task is in task_processing_sent state, received unexpected PENDING status from wally", cur_task->command_uuid);
        stats.task_processing_sent_but_rx_pending++;
        break;
    case processing_from_wally:
        ADMIN_PROBE_DEBUG_TASK_MODULE("%s : sent out the PROCESSING status and confirmed it is received by the other side", cur_task->command_uuid);
        stats.task_processing_sent_and_also_processing_received++;
        break;
    case completed_from_wally:
    case failed_from_wally:
    case timed_out_from_wally:
    case cancelled_from_wally:
        /*
         * Our task is currenting executing(processing), but we receive a termination status from db.
         * let move the state to reaping.
         * once the task execution callback triggers, they will find out that the task is already reaping
         */
        task_module_state.number_of_tasks_in_state[task_processing_sent]--;
        cur_task->cur_state = task_reaping;
        task_module_state.number_of_tasks_in_state[task_reaping]++;


        ADMIN_PROBE_LOG(AL_ERROR, "%s : task is currently PROCESSING - executing, but receives termination from db,, cancelling the task to be in sync with db", cur_task->command_uuid);
        stats.task_processing_sent_but_rx_a_termination_update_from_db++;
        break;
    case cancel_request_from_wally:
        cur_task->err_message = ADMIN_PROBE_RECEIVED_CANCEL_REQUEST_FROM_USER;
        cur_task->end_time_cloud_s = admin_probe_callbacks.get_current_cloud_epoch_s_cb();
        res = admin_probe_task_module_send_status_report_for_task(cur_task, ADMIN_PROBE_STATUS_CANCELLED);
        if (res) {
            cur_task->need_send_termination_status = 1;
        } else {
            cur_task->need_send_termination_status = 0;
            admin_probe_task_module_update_cur_task_field_on_sending_first_termination_status_report(cur_task);
            stats.number_of_cancelled_status_sent++;

        }
        if (cur_task->callouts->cancel_f) {
            cur_task->callouts->cancel_f(cur_task);
        }

        task_module_state.number_of_tasks_in_state[task_processing_sent]--;
        cur_task->cur_state = task_cancelled_sent;
        stats.total_number_of_task_cancelled++;
        task_module_state.number_of_tasks_in_state[task_cancelled_sent]++;
        break;
    case internal_timeout_timer:
         cur_task->end_time_cloud_s = admin_probe_callbacks.get_current_cloud_epoch_s_cb();
        res = admin_probe_task_module_send_status_report_for_task(cur_task, ADMIN_PROBE_STATUS_TIMED_OUT);
        if (res) {
            cur_task->need_send_termination_status = 1;
        } else {
            cur_task->need_send_termination_status = 0;
            admin_probe_task_module_update_cur_task_field_on_sending_first_termination_status_report(cur_task);
            stats.number_of_timeout_status_sent++;
        }

        task_module_state.number_of_tasks_in_state[task_processing_sent]--;
        cur_task->cur_state = task_timeout_sent;
        stats.total_number_of_task_timeout++;
        task_module_state.number_of_tasks_in_state[task_timeout_sent]++;
        break;
    case internal_task_execute_done_success:
        if (cur_task->is_ut) {
            admin_probe_task_module_UT_task_state_transition(cur_task, ADMIN_PROBE_STATUS_COMPLETED);
            break;
        }
        res = admin_probe_task_module_send_status_report_for_task(cur_task, ADMIN_PROBE_STATUS_COMPLETED);
        if (res) {
            cur_task->need_send_termination_status = 1;
        } else {
            cur_task->need_send_termination_status = 0;
            admin_probe_task_module_update_cur_task_field_on_sending_first_termination_status_report(cur_task);
            stats.number_of_completed_status_sent++;
        }

        task_module_state.number_of_tasks_in_state[task_processing_sent]--;
        cur_task->cur_state = task_complete_sent;
        stats.total_number_of_task_success++;
        task_module_state.number_of_tasks_in_state[task_complete_sent]++;
        break;
    case internal_task_execute_done_but_pending_upload:
        task_module_state.number_of_tasks_in_state[task_uploading]++;
        cur_task->cur_state = task_uploading;
        break;
    case internal_task_execute_done_failed:
        if (cur_task->is_ut) {
            admin_probe_task_module_UT_task_state_transition(cur_task, ADMIN_PROBE_STATUS_FAILED);
            break;
        }
        res = admin_probe_task_module_send_status_report_for_task(cur_task, ADMIN_PROBE_STATUS_FAILED);
        if (res) {
            cur_task->need_send_termination_status = 1;
        } else {
            cur_task->need_send_termination_status = 0;
            admin_probe_task_module_update_cur_task_field_on_sending_first_termination_status_report(cur_task);
            stats.number_of_failed_status_sent++;
        }

        task_module_state.number_of_tasks_in_state[task_processing_sent]--;
        cur_task->cur_state = task_failed_sent;
        stats.total_number_of_task_failed++;
        task_module_state.number_of_tasks_in_state[task_failed_sent]++;
        break;
    case internal_task_upload_success:
    case internal_task_upload_failed:
    case internal_err:
        ADMIN_PROBE_LOG(AL_ERROR, "%s : unexpected event %s triggered for task_processing_sent state", cur_task->command_uuid, task_event_str[event_type]);
        stats.task_processing_but_rx_internal_err_unexpected++;
        break;
    case internal_check_task:
        if (cur_task->type == admin_probe_task_type_restart_process || cur_task->type == admin_probe_task_type_restart_system) {
            //we do not track restart command that is in processing state
            break;
        }
        if (ADMIN_PROBE_INVALID == admin_probe_task_module_check_task_in_processing(cur_task)) {
            cur_task->end_time_cloud_s = admin_probe_callbacks.get_current_cloud_epoch_s_cb();
            res = admin_probe_task_module_send_status_report_for_task(cur_task, ADMIN_PROBE_STATUS_TIMED_OUT);
            if (res) {
                cur_task->need_send_termination_status = 1;
            } else {
                cur_task->need_send_termination_status = 0;
                admin_probe_task_module_update_cur_task_field_on_sending_first_termination_status_report(cur_task);
                stats.number_of_timeout_status_sent++;
            }


            task_module_state.number_of_tasks_in_state[task_processing_sent]--;
            cur_task->cur_state = task_timeout_sent;
            stats.total_number_of_task_timeout++;
            task_module_state.number_of_tasks_in_state[task_timeout_sent]++;
        }
        break;
    default:
        break;

    }
}

static void admin_probe_task_module_process_event_state_task_uploading(struct admin_probe_task *cur_task, enum admin_probe_task_event event_type)
{
    if (!cur_task) {
        ADMIN_PROBE_LOG(AL_ERROR, "invalid command_probe, can not proceed with event %d and state task_uploading", event_type);
        return;
    }

    if (!cur_task->command_uuid) {
        ADMIN_PROBE_LOG(AL_ERROR, "invalid command_uuid, can not proceed with event %d and state task_uploading", event_type);
        return;
    }

    int res;

    switch(event_type) {
    case pending_from_wally:
        ADMIN_PROBE_LOG(AL_ERROR, "%s : task is in task_uploading state, received unexpected PENDING status from wally", cur_task->command_uuid);
        stats.task_uploading_but_rx_pending++;
        break;
    case processing_from_wally:

        ADMIN_PROBE_DEBUG_TASK_MODULE("%s : sent out the PROCESSING status and confirmed it is received by the other side", cur_task->command_uuid);
        stats.task_processing_sent_and_also_processing_received++;
        break;
    case completed_from_wally:
    case failed_from_wally:
    case timed_out_from_wally:
    case cancelled_from_wally:
        /*
         * Our task is currenting uploading, but we receive a termination status from db.
         * let move the state to reaping.
         * once the uploader callback triggers, they will find out that the task is already reaping
         */
        task_module_state.number_of_tasks_in_state[task_uploading]--;
        cur_task->cur_state = task_reaping;
        task_module_state.number_of_tasks_in_state[task_reaping]++;


        ADMIN_PROBE_LOG(AL_ERROR, "%s : task is currently PROCESSING - uploading, but receives termination from db,, cancelling the task to be in sync with db", cur_task->command_uuid);
        stats.task_uploading_but_rx_a_termination_update_from_db++;
        break;
    case cancel_request_from_wally:
        cur_task->err_message = ADMIN_PROBE_RECEIVED_CANCEL_REQUEST_FROM_USER;
        cur_task->end_time_cloud_s = admin_probe_callbacks.get_current_cloud_epoch_s_cb();
        res = admin_probe_task_module_send_status_report_for_task(cur_task, ADMIN_PROBE_STATUS_CANCELLED);
        if (res) {
            cur_task->need_send_termination_status = 1;
        } else {
            cur_task->need_send_termination_status = 0;
            admin_probe_task_module_update_cur_task_field_on_sending_first_termination_status_report(cur_task);
            stats.number_of_cancelled_status_sent++;
        }

        task_module_state.number_of_tasks_in_state[task_uploading]--;
        cur_task->cur_state = task_cancelled_sent;
        stats.total_number_of_task_cancelled++;
        task_module_state.number_of_tasks_in_state[task_cancelled_sent]++;
        break;
    case internal_timeout_timer:
        break;
    case internal_task_upload_success:
        if (cur_task->is_ut) {
            admin_probe_task_module_UT_task_state_transition(cur_task, ADMIN_PROBE_STATUS_COMPLETED);
            break;
        }
        res = admin_probe_task_module_send_status_report_for_task(cur_task, ADMIN_PROBE_STATUS_COMPLETED);
        if (res) {
            cur_task->need_send_termination_status = 1;
        } else {
            cur_task->need_send_termination_status = 0;
            admin_probe_task_module_update_cur_task_field_on_sending_first_termination_status_report(cur_task);
            stats.number_of_completed_status_sent++;
        }
        task_module_state.number_of_tasks_in_state[task_uploading]--;
        cur_task->cur_state = task_complete_sent;
        stats.total_number_of_task_success++;
        task_module_state.number_of_tasks_in_state[task_complete_sent]++;
        break;
    case internal_task_upload_failed:
        res = admin_probe_task_module_send_status_report_for_task(cur_task, ADMIN_PROBE_STATUS_FAILED);
        if (res) {
            cur_task->need_send_termination_status = 1;
        } else {
            cur_task->need_send_termination_status = 0;
            admin_probe_task_module_update_cur_task_field_on_sending_first_termination_status_report(cur_task);
            stats.number_of_failed_status_sent++;
        }

        task_module_state.number_of_tasks_in_state[task_uploading]--;
        cur_task->cur_state = task_failed_sent;
        stats.total_number_of_task_failed++;
        task_module_state.number_of_tasks_in_state[task_failed_sent]++;
        break;
    case internal_task_execute_done_success:
    case internal_task_execute_done_failed:
    case internal_err:
        ADMIN_PROBE_LOG(AL_ERROR, "%s : unexpected event %s triggered for task_uploading state", cur_task->command_uuid, task_event_str[event_type]);
        stats.task_uploading_but_rx_internal_err_unexpected++;
        break;
    case internal_check_task:
        /*do nothing, task is uploading, nothing to check*/
        break;
    default:
        break;

    }
}

static void admin_probe_task_module_process_event_state_task_termination_status_sent(struct admin_probe_task *cur_task,
                                                                                     enum admin_probe_task_event event_type,
                                                                                     enum admin_probe_task_state termination_state)
{
    if (!cur_task) {
        ADMIN_PROBE_LOG(AL_ERROR, "invalid command_probe, can not proceed with event %d and state task_cancelled_sent", event_type);
        return;
    }

    if (!cur_task->command_uuid) {
        ADMIN_PROBE_LOG(AL_ERROR, "invalid command_uuid, can not proceed with event %d and state task_cancelled_sent", event_type);
        return;
    }

    switch(event_type) {
    case pending_from_wally:
        ADMIN_PROBE_LOG(AL_ERROR, "%s : task is in one of the termination state, but received a pending status from wally, send termination status again", cur_task->command_uuid);
        admin_probe_task_module_task_in_task_termination_state_but_send_termination_status_again(cur_task, termination_state);
        stats.task_termination_status_sent_but_rx_pending++;
        break;
    case processing_from_wally:
        ADMIN_PROBE_DEBUG_TASK_MODULE("%s : sent out the PROCESSING status and confirmed it is received by the other side", cur_task->command_uuid);
        stats.task_processing_sent_and_also_processing_received++;
        break;
    case completed_from_wally:
    case failed_from_wally:
    case timed_out_from_wally:
    case cancelled_from_wally:
        /*
         * internally we are in termination state, and we also receive a termination state from db.
         * since both end terminating on this task, we can safely transition this task to task_reaping state.
         */
        switch (termination_state) {
        case task_cancelled_sent:
            task_module_state.number_of_tasks_in_state[task_cancelled_sent]--;
            break;
        case task_failed_sent:
            task_module_state.number_of_tasks_in_state[task_failed_sent]--;
            break;
        case task_timeout_sent:
            task_module_state.number_of_tasks_in_state[task_timeout_sent]--;
            break;
        case task_complete_sent:
            task_module_state.number_of_tasks_in_state[task_complete_sent]--;
            break;
        default:
            ADMIN_PROBE_LOG(AL_ERROR, "%s : weird state in processing termination status sent event", cur_task->command_uuid);
            stats.task_terminate_bad_state++;
            break;
        }

        cur_task->cur_state = task_reaping;
        task_module_state.number_of_tasks_in_state[task_reaping]++;

        ADMIN_PROBE_DEBUG_TASK_MODULE("%s : sent out the termination status, also received a terminatino status from db, since anyways its a termination state, ok to move to reaping", cur_task->command_uuid);
        stats.task_terminate_ourside_also_terminate_from_db++;
        break;
    case cancel_request_from_wally:
        ADMIN_PROBE_LOG(AL_ERROR, "%s : received cancel requset from user, but we have already terminated the task, send the termination status again", cur_task->command_uuid);
        admin_probe_task_module_task_in_task_termination_state_but_send_termination_status_again(cur_task, termination_state);
        break;
    case internal_timeout_timer:
        break;
    case internal_task_execute_done_success:
    case internal_task_execute_done_failed:
        stats.task_terminate_sent_rx_internal_task_execute_event++;
        break;
    case internal_task_upload_success:
    case internal_task_upload_failed:
        stats.task_terminate_sent_rx_internal_task_upload_event++;
        break;
    case internal_check_task:
        if (ADMIN_PROBE_TRUE == admin_probe_task_module_check_if_ok_to_move_to_reaping_from_termination(cur_task)) {

            switch (termination_state) {
            case task_cancelled_sent:
                task_module_state.number_of_tasks_in_state[task_cancelled_sent]--;
                break;
            case task_failed_sent:
                task_module_state.number_of_tasks_in_state[task_failed_sent]--;
                break;
            case task_timeout_sent:
                task_module_state.number_of_tasks_in_state[task_timeout_sent]--;
                break;
            case task_complete_sent:
                task_module_state.number_of_tasks_in_state[task_complete_sent]--;
                break;
            default:
                ADMIN_PROBE_LOG(AL_ERROR, "%s : invalid state in processing termination status sent event", cur_task->command_uuid);
                stats.task_terminate_bad_state++;
                break;
            }

            cur_task->cur_state = task_free;
            task_module_state.number_of_tasks_in_state[task_free]++;
        }
        break;

    default:
        break;
    }

}

static void admin_probe_task_module_process_event_state_task_reaping(struct admin_probe_task *cur_task, enum admin_probe_task_event event_type)
{
    if (!cur_task) {
        ADMIN_PROBE_LOG(AL_ERROR, "invalid command_probe, can not proceed with event %d and state task_reaping", event_type);
        return;
    }

    if (!cur_task->command_uuid) {
        ADMIN_PROBE_LOG(AL_ERROR, "invalid command_uuid, can not proceed with event %d and state task_reaping", event_type);
        return;
    }

    switch(event_type) {
    case pending_from_wally:
        stats.already_reaping_but_now_pending++;
        ADMIN_PROBE_LOG(AL_ERROR, "%s : received unexpected event %s in task_reaping state", cur_task->command_uuid, task_event_str[event_type]);
        break;
    case processing_from_wally:
        stats.already_reaping_but_now_processing++;
        ADMIN_PROBE_LOG(AL_ERROR, "%s : received unexpected event %s in task_reaping state", cur_task->command_uuid, task_event_str[event_type]);
        break;
    case completed_from_wally:
    case failed_from_wally:
    case timed_out_from_wally:
    case cancelled_from_wally:
        /*
         * when we reach reaping, it means that we db already told us its setting status to a termination status,
         * it is telling us again. maybe some other column getting updated?
         */
        stats.already_reapong_but_rx_termination_status_again_from_db++;
        break;
    case cancel_request_from_wally:
        stats.already_reaping_but_rx_a_cancel_request_from_user++;
        break;
    case internal_timeout_timer:
        if (cur_task->cleanup_criteria.is_expired_callback_outstanding) {
            cur_task->cleanup_criteria.is_expired_callback_outstanding = 0;
        }
        break;
    case internal_task_execute_done_success:
    case internal_task_execute_done_failed:
        break;
    case internal_task_upload_success:
    case internal_task_upload_failed:
        break;
    case internal_err:
        stats.reaping_bad_state++;
        ADMIN_PROBE_LOG(AL_ERROR, "%s : eceived unexpected event %s in task_reaping state", cur_task->command_uuid, task_event_str[event_type]);
        break;
    case internal_check_task:
        if (ADMIN_PROBE_TRUE == admin_probe_task_module_check_if_ok_to_move_to_free_from_reaping(cur_task)) {
            task_module_state.number_of_tasks_in_state[task_reaping]--;
            cur_task->cur_state = task_free;
            task_module_state.number_of_tasks_in_state[task_free]++;
        }
    default:
        break;
    }
}


void admin_probe_task_module_probe_process_event(void* command_probe,
                                                 int is_np_command_probe,
                                                 struct admin_probe_task* cur_task,
                                                 char* command_uuid,
                                                 enum admin_probe_task_event event_type)
{

    if (!command_probe && !cur_task) {
        ADMIN_PROBE_LOG(AL_ERROR, "Invalid command_probe or cur_task, can not proceed processing event, returning");
        return;
    }

    if (!command_uuid) {
        ADMIN_PROBE_LOG(AL_ERROR, "Invalid command_uuid, can not proceed processing event, returning");
        return;
    }

    if (!cur_task) {
        cur_task = zhash_table_lookup(task_module_state.task_hash_by_command_uuid, command_uuid, strlen(command_uuid), NULL);
    }

    if (!cur_task) {
        admin_probe_task_module_process_event_state_task_not_exist(command_probe, event_type, is_np_command_probe);
    } else {
        switch(cur_task->cur_state) {
        case task_pending:
            admin_probe_task_module_process_event_state_task_pending(cur_task, event_type);
            break;
        case task_processing_sent:
            admin_probe_task_module_process_event_state_task_processing_sent(cur_task, event_type);
            break;
        case task_uploading:
            admin_probe_task_module_process_event_state_task_uploading(cur_task, event_type);
            break;
        case task_cancelled_sent:
        case task_failed_sent:
        case task_timeout_sent:
        case task_complete_sent:
            admin_probe_task_module_process_event_state_task_termination_status_sent(cur_task, event_type, cur_task->cur_state);
            break;
        case task_reaping:
            admin_probe_task_module_process_event_state_task_reaping(cur_task, event_type);
            break;
        case task_free:
            stats.task_freeing_event_triggered++;
            ADMIN_PROBE_LOG(AL_ERROR, "%s : received unexpected event %s calls when tasks in task_reaping state", cur_task->command_uuid, task_event_str[event_type]);
            break;
        default:
            stats.invalid_event_happened++;
            ADMIN_PROBE_LOG(AL_ERROR, "%s : invalid task state type %d, can not process any events %s", cur_task->command_uuid, cur_task->cur_state, task_event_str[event_type]);
            break;
        }

    }

}

enum admin_probe_task_event admin_probe_task_module_probe_find_out_event_type_from_wally(char* status)
{
    if (!status) {
        ADMIN_PROBE_LOG(AL_ERROR, "invalid status, can not find out event type from wally");
        return invalid_event;
    }

    if (0 == strcmp(ADMIN_PROBE_STATUS_PENDING, status)) {
        return pending_from_wally;
    } else if (0 == strcmp(ADMIN_PROBE_STATUS_PROCESSING, status)) {
        return processing_from_wally;
    } else if (0 == strcmp(ADMIN_PROBE_STATUS_COMPLETED, status)) {
        return completed_from_wally;
    } else if  (0 == strcmp(ADMIN_PROBE_STATUS_FAILED, status)) {
        return failed_from_wally;
    } else if (0 == strcmp(ADMIN_PROBE_STATUS_TIMED_OUT, status)) {
        return timed_out_from_wally;
    } else if (0 == strcmp(ADMIN_PROBE_STATUS_CANCELLED, status)) {
        return cancelled_from_wally;
    } else if (0 == strcmp(ADMIN_PROBE_STATUS_CANCEL_REQUEST, status)) {
        return cancel_request_from_wally;
    } else {
        return invalid_event;
    }
}

void admin_probe_task_module_probe_zpn_command_probe_entry_update_with_argo_object_hold(struct zevent_base *base,
                                                                                        void *void_cookie,
                                                                                        int64_t int_cookie)
{
    struct argo_object* row= void_cookie;
    int is_np_command_probe = int_cookie;
    struct np_command_probe *np_command_probe = NULL;
    struct zpn_command_probe *zpn_command_probe = NULL;
    void *command_probe = row->base_structure_void;
    enum admin_probe_task_event incoming_event;

    char *command_uuid = NULL;
    char *action = NULL;
    char *status = NULL;
    int64_t deleted = 0;

    if (!command_probe) {
        ADMIN_PROBE_LOG(AL_ERROR, "Invalid command_probe pointer, can not proceed further, returning");
        argo_object_release(row);
        return;
    }

    if (is_np_command_probe) {
        np_command_probe = (struct np_command_probe *)command_probe;
    } else {
        zpn_command_probe = (struct zpn_command_probe *)command_probe;
    }

    command_uuid = is_np_command_probe ? np_command_probe->command_uuid : zpn_command_probe->command_uuid;
    action = is_np_command_probe ? np_command_probe->action : zpn_command_probe->action;
    status = is_np_command_probe ? np_command_probe->status : zpn_command_probe->status;
    deleted = is_np_command_probe ? np_command_probe->deleted : zpn_command_probe->deleted;


    if (!command_uuid || !action || !status) {
        ADMIN_PROBE_LOG(AL_ERROR, "Invalid command probe request format, can not proceed further, returning");
        argo_object_release(row);
        return;
    }

    if (admin_probe_callbacks.is_entity_paused && admin_probe_callbacks.is_entity_paused()) {
        ADMIN_PROBE_LOG(AL_NOTICE, "entity is currently in paused mode, not taking any probe commands");
        argo_object_release(row);
        return;
    }

    if (task_module_state.is_task_module_state_pause_for_restart) {
        ADMIN_PROBE_LOG(AL_NOTICE, "task module is currently in paused mode, not taking any probe commands");
        argo_object_release(row);
        return;
    }

    if (deleted) {
        ADMIN_PROBE_DEBUG_TASK_MODULE("%s : action : %s ignoring the row update as this is an update for deleted entry", command_uuid, action);
        argo_object_release(row);
        return;
    }

    incoming_event = admin_probe_task_module_probe_find_out_event_type_from_wally(status);

    admin_probe_task_module_probe_process_event(command_probe, is_np_command_probe, NULL, command_uuid, incoming_event);

    argo_object_release(row);
    return;

}

void admin_probe_task_module_command_probe_entry_update_from_fohh_thread(struct argo_object* row, int is_np_command_probe)
{
    if (!admin_probe_state.zbase) {
        ADMIN_PROBE_LOG(AL_ERROR, "rx command probe entries from db but admin probe thread is not init");
        return;
    }
    argo_object_hold(row);
    if (0 != zevent_base_call(admin_probe_state.zbase, admin_probe_task_module_probe_zpn_command_probe_entry_update_with_argo_object_hold, row, is_np_command_probe)) {
        argo_object_release(row);
    }

}

static int
admin_probe_task_module_ut_send_task(struct zpath_debug_state*  request_state,
                                     const char**               query_values,
                                     int                        query_value_count,
                                     void*                      cookie)
{
    if (!query_values[0] ||
        !query_values[1] ||
        !query_values[2] ||
        !query_values[3] ||
        !query_values[4] ||
        !query_values[5] ||
        !query_values[6] ||
        !query_values[7] ||
        !query_values[8]) {

        ZDP("Missing argument on command_uuid or action or target or creation_time");

        goto done;
    }

    int64_t entity_gid = strtoll(query_values[1], NULL, 10);
    int64_t customer_gid = strtoll(query_values[2], NULL, 10);
    int64_t creation_time_s = strtoll(query_values[8], NULL, 10);
    int64_t duration_s = strtoll(query_values[5], NULL, 10);
    int is_np_command_probe = 0;

    if (query_values[9]) {
        is_np_command_probe = atoi(query_values[9]);
    }

    // Use current time as creation_time for UT if it isn't passed by the caller.
    if (creation_time_s == 0) {
        creation_time_s = epoch_s();
    }

    admin_probe_task_module_create_new_task_from_pending_event((const char*)query_values[0],
                                                                entity_gid,
                                                                customer_gid,
                                                                (const char*)query_values[3],
                                                                ADMIN_PROBE_STATUS_PENDING,
                                                                (const char*)query_values[4],
                                                                NULL,
                                                                duration_s,
                                                                (const char*) query_values[7],
                                                                (const char*) query_values[6],
                                                                creation_time_s,
                                                                1, //is UT
                                                                pending_from_wally,
                                                                is_np_command_probe);

done:
    return ADMIN_PROBE_RESULT_NO_ERROR;
}

static int
admin_probe_task_module_dump_stats(struct zpath_debug_state*  request_state,
                                   const char**               query_values,
                                   int                        query_value_count,
                                   void*                      cookie)
{

    char jsonout[10000];

    (void)query_values;
    (void)query_value_count;
    (void)cookie;

    if (ARGO_RESULT_NO_ERROR == argo_structure_dump(admin_probe_task_module_stats_desciption,
                                                    &stats, jsonout, sizeof(jsonout), NULL, 1)) {
        ZDP("%s\n", jsonout);
    }

    for (int i = 0; i < max_task_state; i++) {
        ZDP("state : %s, number of task in this state: %"PRId64" \n", task_state_str[i], task_module_state.number_of_tasks_in_state[i]);
    }
    ZDP("number of task in task queue: %"PRId64" \n", task_module_state.number_of_tasks_in_task_queue);

    return ADMIN_PROBE_RESULT_NO_ERROR;
}

static int admin_probe_task_module_debug_init()
{
    int res;

    if (!(admin_probe_task_module_stats_desciption = argo_register_global_structure(ADMIN_PROBE_TASK_MODULE_STATS_HELPER))) {
        return ADMIN_PROBE_RESULT_ERR;
    }

    res = zpath_debug_add_read_command("dump the stats of admin_probe_task_module",
                                  "/admin_probe/task_module_stats",
                                  admin_probe_task_module_dump_stats,
                                  NULL,
                                  NULL);
    if (res){
        return ADMIN_PROBE_RESULT_ERR;
    }

    res = zpath_debug_add_admin_command("UT: send admin probe command",
                                  "/admin_probe/ut/send_task",
                                  admin_probe_task_module_ut_send_task,
                                  NULL,
                                  "command_uuid", "unique identifier of the command",
                                  "entity_gid", "connector gid",
                                  "customer_gid", "customer gid",
                                  "action", "action of the command",
                                  "target", "target of the command",
                                  "duration_s", "duration in seconds",
                                  "bucket_name", "S3 bucket name",
                                  "file_location", "file location in S3",
                                  "creation_time", "creation time in second of the command",
                                  "is_np_command_probe", "optional input, set 1 incase of np commands",
                                  NULL);
    if (res){
        return ADMIN_PROBE_RESULT_ERR;
    }

    return ADMIN_PROBE_RESULT_NO_ERROR;
}

int admin_probe_task_module_init(struct event_base *base)
{
    if (!base) {
        ADMIN_PROBE_LOG(AL_ERROR, "Can not init task_module - no event base");
        return ADMIN_PROBE_RESULT_ERR;
    }

    int res;

    task_module_state.task_hash_by_command_uuid = zhash_table_alloc(&admin_probe_allocator);
    if (!task_module_state.task_hash_by_command_uuid) {
        ADMIN_PROBE_LOG(AL_ERROR, "Cannot allocate hash table for task_module_state - task_hash_by_command_uuid");
        return ADMIN_PROBE_RESULT_ERR;
    }


    ZTAILQ_INIT(&(task_module_state.task_queue));

    /*debug init*/
    res = admin_probe_task_module_debug_init();
    if (res) {
        ADMIN_PROBE_LOG(AL_ERROR, "Could not init admin_probe_task_module_debug_init");
        return ADMIN_PROBE_RESULT_ERR;
    }

    res = admin_probe_task_module_icmp_debug_init();
    if (res) {
        ADMIN_PROBE_LOG(AL_ERROR, "Could not init admin_probe_task_module_icmp_debug_init");
        return ADMIN_PROBE_RESULT_ERR;
    }

    res = admin_probe_task_module_tcp_debug_init();
    if (res) {
        ADMIN_PROBE_LOG(AL_ERROR, "Could not init admin_probe_task_module_tcp_debug_init");
        return ADMIN_PROBE_RESULT_ERR;
    }

    res = admin_probe_task_module_common_debug_init();
    if (res) {
        ADMIN_PROBE_LOG(AL_ERROR, "Could not init admin_probe_task_module_common_debug_init");
        return ADMIN_PROBE_RESULT_ERR;
    }

    res = admin_probe_task_module_dns_debug_init();
    if (res) {
        ADMIN_PROBE_LOG(AL_ERROR, "Could not init admin_probe_task_module_dns_debug_init");
        return ADMIN_PROBE_RESULT_ERR;
    }


    struct timeval tv;
    struct event *check_task_timer;

    check_task_timer = event_new(base,
                                 -1,
                                 EV_PERSIST,
                                 admin_probe_task_module_check_tasks,
                                 NULL);
    if (!check_task_timer) {
        ADMIN_PROBE_LOG(AL_ERROR, "Could not activate timer for admin probe - check_task_timer");
        return ADMIN_PROBE_RESULT_ERR;
    }

    tv.tv_sec = 1;
    tv.tv_usec = 0;

    if (event_add(check_task_timer, &tv)) {
        ADMIN_PROBE_LOG(AL_ERROR, "Could not add timer for check_task_timer");
        return ADMIN_PROBE_RESULT_ERR;
    }

    ADMIN_PROBE_LOG(AL_NOTICE, "admin probe task module is fully initialized");

    return ADMIN_PROBE_RESULT_NO_ERROR;
}


int64_t admin_probe_get_total_number_of_task()
{
    return stats.total_number_of_task_created;
}


int64_t admin_probe_get_total_number_of_task_completed()
{
    return stats.total_number_of_task_success;
}

int64_t admin_probe_get_total_number_of_task_failed()
{
    return stats.total_number_of_task_failed;
}


int64_t admin_probe_get_total_number_of_task_cancelled()
{
    return stats.total_number_of_task_cancelled;
}


int64_t admin_probe_get_total_number_of_task_timeout()
{
    return stats.total_number_of_task_timeout;
}

int64_t admin_probe_get_total_number_restart_complete()
{
    return stats.restart_command_complete;
}


int64_t admin_probe_get_total_number_of_restart_fail()
{
    return stats.restart_command_fail;
}


int64_t admin_probe_get_total_number_of_restart_cancel()
{
    return stats.restart_command_cancel;
}


/*below s3 callback is not used in phase 1 and not code reviewed
void s3_output_task_module_cb(admin_probe_uploader_output uploader_output)
{

    struct admin_probe_task *cur_task;

    cur_task = zhash_table_lookup(task_module_state.task_hash_by_command_uuid, uploader_output.command_uuid, strlen(uploader_output.command_uuid), NULL);

    if (!cur_task) {
        ADMIN_PROBE_LOG(AL_NOTICE, "got uploader cb, but task is no longer in our mem");
        return;
    }

    if (cur_task->cleanup_criteria.is_uploader_callback_outstanding) {
        cur_task->cleanup_criteria.is_uploader_callback_outstanding = 0;
    }

    if (0 == uploader_output.status) {
        admin_probe_task_module_probe_process_event(NULL, cur_task->is_np_command_probe, cur_task, cur_task->command_uuid, internal_task_upload_success);
    } else {
        cur_task->err_message = uploader_output.error_message;
        admin_probe_task_module_probe_process_event(NULL, cur_task->is_np_command_probe, cur_task, cur_task->command_uuid, internal_task_upload_failed);

    }

}*/

/*
 * Commented below function for now as we are only support restart, DNS actions in admin_probe and these 2 actions does not need expiry timer.
 */
/*
static int admin_probe_task_module_set_expiry_on_executing_task(struct task_node *cur_task)
{
    if (!cur_task) {
        stats.set_expiry_timer_failed_no_cur_task++;
        ADMIN_PROBE_ASSERT_SOFT(0, "can't proceed to admin_probe_task_module_set_expiry_on_executing_task -- no curt_task");
        return ADMIN_PROBE_RESULT_ERR;
    }

    if (!cur_task->command_uuid) {
        stats.set_expiry_timer_failed_no_command_uuid++;
        ADMIN_PROBE_ASSERT_SOFT(0, "can't proceed to admin_probe_task_module_set_expiry_on_executing_task -- no command_uuid");
        return ADMIN_PROBE_RESULT_ERR;
    }

    if (!admin_probe_state.ebase) {
        stats.set_expiry_timer_failed_no_ebase++;
        ADMIN_PROBE_LOG(AL_NOTICE, "%s : no event base from admin_probe_state, will skip setting expiry", cur_task->command_uuid);
        return ADMIN_PROBE_RESULT_ERR;
    }

    struct timeval tv;
    struct event *expiry_timer;

    expiry_timer = event_new(admin_probe_state.ebase,
                             -1,
                             0,
                             admin_probe_task_module_task_expiry_timer,
                             cur_task);
    if (!expiry_timer) {
        stats.set_expiry_timer_failed_at_event_new++;
        ADMIN_PROBE_LOG(AL_ERROR, "Could not activate timer for admin probe - expiry_timer");
        return ADMIN_PROBE_RESULT_ERR;
    }

    tv.tv_sec = cur_task->duration_in_secs;
    tv.tv_usec = 0;

    if (event_add(expiry_timer, &tv)) {
        stats.set_expiry_timer_failed_at_event_add++;
        ADMIN_PROBE_LOG(AL_ERROR, "Could not activate time for expiry_timer");
        return ADMIN_PROBE_RESULT_ERR;
    }

    return ADMIN_PROBE_RESULT_NO_ERROR;
}*/

/* code not reviewed
void admin_probe_task_module_task_expiry_timer(evutil_socket_t sock, short flags, void *cookie)
{

    struct admin_probe_task *cur_task = (struct admin_probe_task *)cookie;

    if (!cur_task) {
        ADMIN_PROBE_LOG(AL_NOTICE, "no cookie coming back - maybe the task is already done and cleaned up");
        return;
    }

    if (!cur_task->command_uuid) {
        ADMIN_PROBE_LOG(AL_NOTICE, "no command_uuid coming back - maybe the task is already done and cleaned up");
        return;
    }

    ADMIN_PROBE_DEBUG_TASK_MODULE("%s expiry timer for action %s fired", cur_task->command_uuid, cur_task->action);

    if (cur_task->cleanup_criteria.is_execute_callback_outstanding) {
        cur_task->cleanup_criteria.is_execute_callback_outstanding = 0;
    }

    switch (cur_task->type) {
        case DNS:
        stats.expiry_fired_for_dns_task++;
        admin_probe_task_module_probe_process_event(NULL, cur_task->is_np_command_probe, cur_task, cur_task->command_uuid, internal_timeout_timer);

        break;
    default:

        break;
    }

}*/
