argo_parse_files(
    INPUT_FILES
        admin_probe_rpc.h
        zpn_command_probe.h
        np_command_probe.h
        admin_probe_rpc_send.c
        admin_probe_task_module.c
        admin_probe_task_module_common.c
        admin_probe_task_module_dns.c
        admin_probe_task_module_icmp.c
        admin_probe_task_module_tcp.c
        admin_probe_task_module_tcpdump.c
        admin_probe_task_module_frr_cmds.c
        admin_probe_uploader.c
    OUTPUT_DIR ${CMAKE_CURRENT_BINARY_DIR}
    OUTPUT_FILES_VAR generated_headers
)

add_library(
    admin_probe
    STATIC
    admin_probe.c
    admin_probe_lib.c
    admin_probe_private.c
    admin_probe_rate_limiting.c
    admin_probe_rpc_send.c
    admin_probe_rpc.c
    admin_probe_stats.c
    admin_probe_task_module.c
    admin_probe_task_module_common.c
    admin_probe_task_module_dns.c
    admin_probe_task_module_icmp.c
    admin_probe_task_module_tcp.c
    admin_probe_task_module_restart.c
    admin_probe_uploader.c
    admin_probe_task_module_tcpdump.c
    admin_probe_task_module_frr_cmds.c
    zpn_command_probe.c
    np_command_probe.c
    admin_probe_cfg_override_desc.c
    ${generated_headers}
)
target_link_libraries(admin_probe PUBLIC zhealth zpn_pcap zpath_lib)

add_library(admin_probe_task_module_dns_testing STATIC admin_probe_task_module_dns.c)
target_link_libraries(admin_probe_task_module_dns_testing PUBLIC admin_probe)
target_compile_definitions(admin_probe_task_module_dns_testing PRIVATE ADMIN_PROBE_TASK_MODULE_DNS_TESTING)

add_library(admin_probe_task_module_tcp_testing STATIC admin_probe_task_module_tcp.c)
target_link_libraries(admin_probe_task_module_tcp_testing PUBLIC admin_probe)
target_compile_definitions(admin_probe_task_module_tcp_testing PRIVATE ADMIN_PROBE_TASK_MODULE_TCP_TESTING)

add_subdirectory(tests)
