/*
 * admin_probe_lib.c. Copyright (C) 2021 Zscaler Inc. All Rights Reserved.
 */

#include "zpath_lib/zpath_debug.h"
#include "zpath_misc/zpath_misc.h"
#include "admin_probe/admin_probe_lib.h"

struct argo_log_collection *admin_probe_event_collection = NULL;
struct zpath_allocator admin_probe_allocator = ZPATH_ALLOCATOR_INIT("admin_probe");

uint64_t admin_probe_debug_log = 0;

uint64_t admin_probe_debug_log_catch_defaults =
         (ADMIN_PROBE_DEBUG_PROBE_BIT) |
         (ADMIN_PROBE_DEBUG_TABLE_BIT) |
         (ADMIN_PROBE_DEBUG_TASK_MODULE_BIT) |
         (ADMIN_PROBE_DEBUG_UPLOADER_BIT) |
         (ADMIN_PROBE_DEBUG_TASK_MODULE_DNS_BIT) |
         (ADMIN_PROBE_DEBUG_TASK_MODULE_ICMP_BIT) |
         (ADMIN_PROBE_DEBUG_TASK_MODULE_TCP_BIT) |
         (ADMIN_PROBE_DEBUG_PARSE_BIT) |
         0;

const char *admin_probe_debug_log_names[] = ADMIN_PROBE_DEBUG_LOG_NAMES;


int admin_probe_lib_init(struct argo_log_collection *event_log)
{
    int res = 0;
    static int admin_probe_lib_allocator_init_done = 0;

    if (!admin_probe_lib_allocator_init_done) {
        admin_probe_event_collection = event_log;
        zpath_debug_add_allocator(&admin_probe_allocator, "admin_probe");

        res = zpath_debug_add_flag(&admin_probe_debug_log, admin_probe_debug_log_catch_defaults, "admin_probe",
                                    admin_probe_debug_log_names);
        admin_probe_lib_allocator_init_done = 1;
    }
    return res;
}
