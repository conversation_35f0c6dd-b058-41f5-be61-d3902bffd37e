/*
 * admin_probe_format_probe_result.c. Copyright (C) 2021 Zscaler Inc, All Rights Reserved
 *
 */

#include "zpath_lib/zpath_debug.h"

#include "admin_probe/admin_probe_task_module_dns.h"
#include "admin_probe/admin_probe_private.h"
#include "admin_probe/admin_probe_rate_limiting.h"
#include "admin_probe/admin_probe_lib.h"
#include "admin_probe/admin_probe_public.h"
#include <netinet/in.h>
#include <arpa/inet.h>
#include "parson/parson.h"
#include "zcdns/zcdns.h"
#include <inttypes.h>

#include "argo/argo.h"

//Only include weak referencing when building the tests
#ifdef ADMIN_PROBE_TASK_MODULE_DNS_TESTING
#include "admin_probe/tests/admin_probe_task_module_dns_tests/test_headers/admin_probe_task_module_dns_test_weak_header.h"
#include <assert.h>
#else
#endif // ADMIN_PROBE_TASK_MODULE_DNS_TESTING

/*mockable functions*/
#ifdef ADMIN_PROBE_TASK_MODULE_DNS_TESTING
void* ADMIN_PROBE_TASK_MODULE_DNS_MALLOC(size_t s) {
    assert(0);
    return NULL;
}

void ADMIN_PROBE_TASK_MODULE_DNS_FREE(void* s) {
    assert(0);
}
#else
#define ADMIN_PROBE_TASK_MODULE_DNS_MALLOC(s) ADMIN_PROBE_MALLOC(s)
#define ADMIN_PROBE_TASK_MODULE_DNS_FREE(p) ADMIN_PROBE_FREE(p)
#endif // ADMIN_PROBE_TASK_MODULE_DNS_TESTING

#define ADMIN_PROBE_TASK_MODULE_DNS_QUERY_TYPE_A "A"
#define ADMIN_PROBE_TASK_MODULE_DNS_QUERY_TYPE_AAAA "AAAA"

static
struct admin_probe_task_module_dns_stats {                                  /* _ARGO: * object_definition */
    uint64_t number_of_buf_txt_result_success;                              /* _ARGO: integer */
    uint64_t number_of_buf_txt_result_fail;                                 /* _ARGO: integer */
    uint64_t number_of_buf_json_result_success;                             /* _ARGO: integer */
    uint64_t number_of_buf_json_result_fail;                                /* _ARGO: integer */
    uint64_t dns_request_do_srv;                                            /* _ARGO: integer */
    uint64_t dns_request_do_srv_success;                                    /* _ARGO: integer */
    uint64_t dns_request_do_srv_fail;                                       /* _ARGO: integer */
    uint64_t dns_request_do_aaaa;                                           /* _ARGO: integer */
    uint64_t dns_request_do_aaaa_success;                                   /* _ARGO: integer */
    uint64_t dns_request_do_aaaa_fail;                                      /* _ARGO: integer */
    uint64_t dns_request_do_a;                                              /* _ARGO: integer */
    uint64_t dns_request_do_a_success;                                      /* _ARGO: integer */
    uint64_t dns_request_do_a_fail;                                         /* _ARGO: integer */
    uint64_t failed_to_exec_dns_because_of_threshold_reached;               /* _ARGO: integer */
    uint64_t dns_result_released_in_free_state;                             /* _ARGO: integer */
    uint64_t dns_result_released_in_cb;                                     /* _ARGO: integer */
} stats;

#include "admin_probe/admin_probe_task_module_dns_compiled_c.h"
static struct argo_structure_description*  admin_probe_task_module_dns_stats_desciption;

static struct {
    int64_t total_dns_in_exec;
} state;

void admin_probe_task_module_dns_free_dns_task_state_f(struct admin_probe_task *cur_task)
{
    struct dns_task *dns_task_state = &(cur_task->task_state.dns_task_state);

    if (!dns_task_state) {
        return;
    }

    if (dns_task_state->results) {
        zcdns_result_release(dns_task_state->results);
        dns_task_state->results = NULL;
        stats.dns_result_released_in_free_state++;
    }
}



static const char *admin_probe_task_module_dns_rcode_lookup(int rcode)
{
    switch (rcode) {
    case 0:
        return ADMIN_PROBE_DNS_RCODE_NoError;
    case 1:
        return ADMIN_PROBE_DNS_RCODE_FormErr;
    case 2:
        return ADMIN_PROBE_DNS_RCODE_ServFail;
    case 3:
        return ADMIN_PROBE_DNS_RCODE_NXDomain;
    case 4:
        return ADMIN_PROBE_DNS_RCODE_NotImp;
    case 5:
        return ADMIN_PROBE_DNS_RCODE_Refused;
    case 6:
        return ADMIN_PROBE_DNS_RCODE_YXDomain;
    case 7:
        return ADMIN_PROBE_DNS_RCODE_YXRRSet;
    case 8:
        return ADMIN_PROBE_DNS_RCODE_NXRRSet;
    case 9:
        return ADMIN_PROBE_DNS_RCODE_NotAuth;
    case 10:
        return ADMIN_PROBE_DNS_RCODE_NotZone;
    case 11:
        return ADMIN_PROBE_DNS_RCODE_DSOTYPENI;
    case 16:
        return ADMIN_PROBE_DNS_RCODE_BADVERS_or_BADSIG;
    case 17:
        return ADMIN_PROBE_DNS_RCODE_BADKEY;
    case 18:
        return ADMIN_PROBE_DNS_RCODE_BADTIME;
    case 19:
        return ADMIN_PROBE_DNS_RCODE_BADMODE;
    case 20:
        return ADMIN_PROBE_DNS_RCODE_BADNAME;
    case 21:
        return ADMIN_PROBE_DNS_RCODE_BADALG;
    case 22:
        return ADMIN_PROBE_DNS_RCODE_BADTRUNC;
    case 23:
        return ADMIN_PROBE_DNS_RCODE_BADCOOKIE;
    default:
        return ADMIN_PROBE_Unknown_DNS_rcode;
    }
}

static const char *admin_probe_task_module_dns_get_dns_failure_message(int rcode)
{
    switch (rcode) {
    case 0:
        return ADMIN_PROBE_DNS_ERR_MSG;
    case 1:
        return ADMIN_PROBE_DNS_ERR_MSG_RCODE_FormErr;
    case 2:
        return ADMIN_PROBE_DNS_ERR_MSG_RCODE_ServFail;
    case 3:
        return ADMIN_PROBE_DNS_ERR_MSG_RCODE_NXDomain;
    case 4:
        return ADMIN_PROBE_DNS_ERR_MSG_RCODE_NotImp;
    case 5:
        return ADMIN_PROBE_DNS_ERR_MSG_RCODE_Refused;
    case 6:
        return ADMIN_PROBE_DNS_ERR_MSG_RCODE_YXDomain;
    case 7:
        return ADMIN_PROBE_DNS_ERR_MSG_RCODE_YXRRSet;
    case 8:
        return ADMIN_PROBE_DNS_ERR_MSG_RCODE_NXRRSet;
    case 9:
        return ADMIN_PROBE_DNS_ERR_MSG_RCODE_NotAuth;
    case 10:
        return ADMIN_PROBE_DNS_ERR_MSG_RCODE_NotZone;
    case 11:
        return ADMIN_PROBE_DNS_ERR_MSG_RCODE_DSOTYPENI;
    case 16:
        return ADMIN_PROBE_DNS_ERR_MSG_RCODE_BADVERS_or_BADSIG;
    case 17:
        return ADMIN_PROBE_DNS_ERR_MSG_RCODE_BADKEY;
    case 18:
        return ADMIN_PROBE_DNS_ERR_MSG_RCODE_BADTIME;
    case 19:
        return ADMIN_PROBE_DNS_ERR_MSG_RCODE_BADMODE;
    case 20:
        return ADMIN_PROBE_DNS_ERR_MSG_RCODE_BADNAME;
    case 21:
        return ADMIN_PROBE_DNS_ERR_MSG_RCODE_BADALG;
    case 22:
        return ADMIN_PROBE_DNS_ERR_MSG_RCODE_BADTRUNC;
    case 23:
        return ADMIN_PROBE_DNS_ERR_MSG_RCODE_BADCOOKIE;
    default:
        return ADMIN_PROBE_DNS_ERR_MSG;
    }
}


JSON_Value* admin_probe_task_module_dns_get_dns_result_srv(int priority, int weight, int port, const char *target, int ttl, int class)
{
    if (!target) {
        return NULL;
    }

    JSON_Value *srv_val = json_value_init_object();
    JSON_Object *srv_obj = json_value_get_object(srv_val);

    json_object_set_number(srv_obj, "priority", priority);
    json_object_set_number(srv_obj, "weight", weight);
    json_object_set_number(srv_obj, "port", port);
    json_object_set_string(srv_obj, "target", target);
    json_object_set_number(srv_obj, "ttl", ttl);

    return srv_val;
}


JSON_Value* admin_probe_task_module_dns_construct_cname_json_object(char *name, char *canonical_name, int ttl)
{
    if (!name) {
        ADMIN_PROBE_LOG(AL_ERROR, "null pointer on name, can not constrcut cname object.. returning");
        return NULL;
    }

    if (!canonical_name) {
        ADMIN_PROBE_LOG(AL_ERROR, "null pointer on canonical_name, an not constrcut cname object.. returning");
        return NULL;
    }

    JSON_Value *cname_val = json_value_init_object();
    JSON_Object *cname_obj = json_value_get_object(cname_val);

    json_object_set_string(cname_obj, "name", name);
    json_object_set_string(cname_obj, "CNAME", canonical_name);
    json_object_set_number(cname_obj, "ttl", ttl);

    return cname_val;
}

JSON_Value* admin_probe_task_module_dns_construct_ip_json_object(char *type, char *name, char *ip_str, int ttl, int class)
{
    if (!type) {
        ADMIN_PROBE_LOG(AL_ERROR, "null pointer on type, can not constrcut ip object.. returning");
        return NULL;
    }

    if (!name) {
        ADMIN_PROBE_LOG(AL_ERROR, "null pointer on name, an not constrcut ip object.. returning");
        return NULL;
    }
    if (!ip_str) {
        ADMIN_PROBE_LOG(AL_ERROR, "null pointer on ip_str, an not constrcut ip object.. returning");
        return NULL;
    }

    JSON_Value *ip_val = json_value_init_object();
    JSON_Object *ip_obj = json_value_get_object(ip_val);

    json_object_set_string(ip_obj, "name", name);
    json_object_set_string(ip_obj, type, ip_str);
    json_object_set_number(ip_obj, "ttl", ttl);

    return ip_val;
}

JSON_Value* admin_probe_task_module_dns_get_dns_results_add_answers_A_AAAA(char *type, char *domain, struct zcdns_result_addr_record * addr, int rcode, char **cnames, int cnames_count, int *canmes_ttl_arr)
{
    if (!domain) {
        ADMIN_PROBE_LOG(AL_ERROR, "null pointer on domain, can not format answer section for dns report on A/AAAA .. returning");
        return NULL;
    }

    if (!addr) {
        ADMIN_PROBE_DEBUG_TASK_MODULE_DNS("null pointer on addr, can not format answer section for dns report on A/AAAA .. returning");
        return NULL;
    }

    JSON_Value* a_aaaa_arr_json_val = json_value_init_array();
    JSON_Array  *a_aaaa_arr = json_value_get_array(a_aaaa_arr_json_val);

    char *cur_domain = domain;
    for (int i = 0; i < cnames_count ; i++) {
        JSON_Value *new_cnames_val = admin_probe_task_module_dns_construct_cname_json_object(cur_domain, cnames[i], canmes_ttl_arr[i]);
        if (!new_cnames_val) {
            continue;
        }
        json_array_append_value(a_aaaa_arr, new_cnames_val);
        cur_domain = cnames[i];
    }

    for (; addr; addr = addr->next) {
        char ip_str[INET6_ADDRSTRLEN];
        zcdns_sockaddr_storage_to_str(&(addr->addr), ip_str, sizeof(ip_str));

        JSON_Value *cur_ip = admin_probe_task_module_dns_construct_ip_json_object(type, cur_domain, ip_str, addr->a_ttl, addr->a_class);
        if (!cur_ip) {
            continue;
        }
        json_array_append_value(a_aaaa_arr, cur_ip);
    }

    return a_aaaa_arr_json_val;
}

JSON_Value* admin_probe_task_module_dns_get_dns_results_add_answers_SRV(struct zcdns_result_srv_record* addr)
{
    if (!addr) {
        ADMIN_PROBE_DEBUG_TASK_MODULE_DNS("null pointer on addr, can not format answer section for dns report on SRV .. returning");
        return NULL;
    }

    JSON_Value* srvs_val = json_value_init_array();
    JSON_Array  *srvs_array = json_value_get_array(srvs_val);

    for (; addr; addr = addr->next) {

        JSON_Value *cur_srv = admin_probe_task_module_dns_get_dns_result_srv(addr->priority, addr->weight, ntohs(addr->port_ne), addr->target, addr->a_ttl, addr->a_class);
        json_array_append_value(srvs_array, cur_srv);
    }


    return srvs_val;
}

JSON_Value* admin_probe_task_module_dns_get_dns_results_add_answers(struct zcdns_result* result, char *action, char *domain)
{
    if (!result) {
        ADMIN_PROBE_LOG(AL_ERROR, "null pointer on result, can not format answer section for dns report .. returning");
        return NULL;
    }

    if (!action) {
        ADMIN_PROBE_LOG(AL_ERROR, "null pointer on action, can not format answer section for dns report .. returning");
        return NULL;
    }

    if (!domain) {
        ADMIN_PROBE_LOG(AL_ERROR, "null pointer on domain, can not format answer section for dns report .. returning");
        return NULL;
    }

    if (strcmp(action, ADMIN_PROBE_ACTION_DNSLOOKUP_A_RECORD) == 0) {
        return admin_probe_task_module_dns_get_dns_results_add_answers_A_AAAA(ADMIN_PROBE_TASK_MODULE_DNS_QUERY_TYPE_A, domain, result->addr_a, result->a_rcode, (result->cnames), result->cnames_count, result->cnames_ttl);
    } else if (strcmp(action, ADMIN_PROBE_ACTION_DNSLOOKUP_AAAA_RECORD) == 0) {
        return admin_probe_task_module_dns_get_dns_results_add_answers_A_AAAA(ADMIN_PROBE_TASK_MODULE_DNS_QUERY_TYPE_AAAA, domain, result->addr_aaaa, result->aaaa_rcode, (result->cnames), result->cnames_count, result->cnames_ttl);
    } else if (strcmp(action, ADMIN_PROBE_ACTION_DNSLOOKUP_SRV_RECORD) == 0) {
        return admin_probe_task_module_dns_get_dns_results_add_answers_SRV(result->srv);
    } else {
        return NULL;
    }

    return NULL;
}

JSON_Value* admin_probe_task_module_dns_get_dns_results_add_question(char *action, char *domain)
{
    if (!action) {
        ADMIN_PROBE_LOG(AL_ERROR, "null pointer on action, can not format question section for dns report .. returning");
        return NULL;
    }

    if (!domain) {
        ADMIN_PROBE_LOG(AL_ERROR, "null pointer on domain, can not format question section for dns report .. returning");
        return NULL;
    }

    JSON_Value *question_val = json_value_init_object();
    JSON_Object *question_obj = json_value_get_object(question_val);

    json_object_set_string(question_obj, "action", action);
    json_object_set_string(question_obj, "target", domain);

    return question_val;
}



/* FULL A  or AAAA report looks like
 * {
 *   "command_uuid": "17fb1d5d-bc3f-4ea4-a49e-7a097ffd6792",
 *   "customer_gid": "217246660302995456",
 *   "entity_gid": "217246660303025225",
 *   "dns_result": {
 *       "question_section": {
 *           "action": "DNSLOOKUP_A_RECORD",
 *           "target": "ia2br.prod.zpath.net"
 *       },
 *       "server": "**********",
 *       "rcode": "NoError",
 *       "answer_section": [
 *           {
 *               "name": "ia2br.prod.zpath.net",
 *               "CNAME": "ia2br.gslb.prod.zpath.net",
 *               "ttl": 600
 *           },
 *           {
 *               "name": "ia2br.gslb.prod.zpath.net",
 *               "CNAME": "chi1.ia2br.gslb.prod.zpath.net",
 *               "ttl": 60
 *           },
 *           {
 *               "name": "chi1.ia2br.gslb.prod.zpath.net",
 *               "A": "165.225.1.251",
 *               "ttl": 300
 *           },
 *           {
 *               "name": "chi1.ia2br.gslb.prod.zpath.net",
 *               "A": "165.225.1.235",
 *               "ttl": 300
 *           },
 *           {
 *               "name": "chi1.ia2br.gslb.prod.zpath.net",
 *               "A": "165.225.1.252",
 *               "ttl": 300
 *           },
 *           {
 *               "name": "chi1.ia2br.gslb.prod.zpath.net",
 *               "A": "165.225.1.253",
 *               "ttl": 300
 *           },
 *           {
 *               "name": "chi1.ia2br.gslb.prod.zpath.net",
 *               "A": "165.225.1.234",
 *               "ttl": 300
 *           },
 *           {
 *               "name": "chi1.ia2br.gslb.prod.zpath.net",
 *               "A": "165.225.1.254",
 *               "ttl": 300
 *           },
 *           {
 *               "name": "chi1.ia2br.gslb.prod.zpath.net",
 *               "A": "165.225.1.236",
 *               "ttl": 300
 *           },
 *           {
 *               "name": "chi1.ia2br.gslb.prod.zpath.net",
 *               "A": "*************",
 *               "ttl": 300
 *           }
 *       ]
 *   }
 * }
 * FULL SRV report looks like
 *{
 *   "command_uuid": "c9d0a17a-83ad-4581-af75-c3e27675d2a9",
 *   "customer_gid": "217246660302995456",
 *   "entity_gid": "217246660303025225",
 *   "dns_result": {
 *       "question_section": {
 *           "action": "DNSLOOKUP_SRV_RECORD",
 *           "target": "_sip._udp.sip.voice.google.com"
 *       },
 *       "server": "**********",
 *       "rcode": "NoError",
 *       "answer_section": [
 *           {
 *               "priority": 10,
 *               "weight": 1,
 *               "port": 5060,
 *               "target": "sip-anycast-1.voice.google.com",
 *               "ttl": 102
 *           },
 *           {
 *               "priority": 20,
 *               "weight": 1,
 *               "port": 5060,
 *               "target": "sip-anycast-2.voice.google.com",
 *               "ttl": 102
 *           }
 *       ]
 *   }
 *}
 * this function take the zcdns_result, format the result into JSON, place the results buf on a newly allocated memory,
 * and return the result to the caller.
 *
 * Caller has to free the memory hold by *result_buf after use.
 * the return buffer is allocated by parson allocator (malloc), so the free on the buffer has to freed also by parson allocator.
 * to free result_buf, call should use admin_probe_task_module_free_serialized_str_using_parson_allocator() to free
 *
 */

static char* admin_probe_task_module_dns_report_json_in_str(struct zcdns_result* result,
                                                            char *action,
                                                            char *domain,
                                                            const char *rcode)
{
    if (!result) {
        ADMIN_PROBE_LOG(AL_ERROR, "no result to format dns report .. returning");
        return NULL;
    }

    if (!action) {
        ADMIN_PROBE_LOG(AL_ERROR, "no action to format dns report .. returning");
        return NULL;
    }

    if (!domain) {
        ADMIN_PROBE_LOG(AL_ERROR, "no domain to format dns report .. returning");
        return NULL;
    }

    char *buf;

    JSON_Value *report_val = json_value_init_object();
    JSON_Object *report_obj = json_value_get_object(report_val);


    JSON_Value *question_val;
    JSON_Value *answer_val;

    char resolv_str[INET6_ADDRSTRLEN];
    if (result->resolver) {
        zcdns_sockaddr_storage_to_str(result->resolver, resolv_str, sizeof(resolv_str));
    } else {
        snprintf(resolv_str, INET6_ADDRSTRLEN, "%s", "");
    }

    question_val = admin_probe_task_module_dns_get_dns_results_add_question(action, domain);
    if (!question_val) {
        json_value_free(report_val);
        return NULL;
    }

    answer_val = admin_probe_task_module_dns_get_dns_results_add_answers(result, action, domain);
    if (!answer_val) {
        json_value_free(question_val);
        json_value_free(report_val);
        return NULL;
    }

    char entity_id_str[ADMIN_PROBE_MAX_ENTITY_ID];

    snprintf(entity_id_str, sizeof(entity_id_str), "%"PRId64"", admin_probe_state.entity_gid);

    json_object_set_string(report_obj, "entity_name", admin_probe_state.entity_name);
    json_object_set_string(report_obj, "entity_gid", entity_id_str);
    json_object_set_value(report_obj, "question_section", question_val);
    json_object_set_string(report_obj, "server", resolv_str);

     json_object_set_string(report_obj, "rcode", rcode);
    json_object_set_value(report_obj, "answer_section", answer_val);

    buf =  json_serialize_to_string_pretty(report_val);

    json_value_free(report_val);

    return buf;
}


static void admin_probe_task_module_dns_get_class_str(int class, char *class_str, int class_str_len)
{
    if (class == ZCDNS_RR_CLASS_IN) {
            snprintf(class_str, class_str_len, "%s", "IN");
    } else {
            snprintf(class_str, class_str_len, "%s", " ");
    }
}

#define MINIMUM_RESIZE_MARGIN 100
#define MAX_DNS_RESULT_LEN 4000
/* This can be done in 2 passes. Calculate size on first pass based on number of records
 * and then allocate in second so repeated check can be avoided
 *
 * currently not in use, but keep it for now for unit test ref
 */
int admin_probe_task_module_dns_check_buf_size(char** s, char** e, char **buf) {
    if (!*s || !*e || !*buf) {
        ADMIN_PROBE_LOG(AL_ERROR, "invalid arguments in admin_probe_format_probe_result_dns_check_buf_size");
        return ADMIN_PROBE_RESULT_ERR;
    }

    if (*s > *e) {
        ADMIN_PROBE_LOG(AL_ERROR, "buffer overflowed, formating dns result error");
        return ADMIN_PROBE_RESULT_ERR;
    }

    if ((*e - *s) < MINIMUM_RESIZE_MARGIN) {
        int new_size = MAX_DNS_RESULT_LEN + strlen(*buf);
        char *new_buf = ADMIN_PROBE_TASK_MODULE_DNS_MALLOC(new_size);
        snprintf(new_buf, new_size, "%s", *buf);
        *s = new_buf + strlen(*buf);
        *e = new_buf + new_size;
        ADMIN_PROBE_TASK_MODULE_DNS_FREE(*buf);
        *buf = new_buf;
    }

    return ADMIN_PROBE_RESULT_NO_ERROR;
}

int admin_probe_task_module_dns_find_number_of_results_in_zcdns_result(struct zcdns_result* result,
                                                                       char *action)
{
    if (!result) {
        return 0;
    }

    if (!action) {
        return 0;
    }

    int answer_count;

    answer_count = 0;
    if (0 == strcmp(action, ADMIN_PROBE_ACTION_DNSLOOKUP_A_RECORD)) {
        struct zcdns_result_addr_record *addr_a;
        for(addr_a = result->addr_a; addr_a; addr_a = addr_a->next) {
            answer_count++;
        }
    } else if (0 == strcmp(action, ADMIN_PROBE_ACTION_DNSLOOKUP_AAAA_RECORD)) {
        struct zcdns_result_addr_record *addr_aaaa;
        for(addr_aaaa = result->addr_aaaa; addr_aaaa; addr_aaaa = addr_aaaa->next) {
            answer_count++;
        }
    } else if (0 == strcmp(action, ADMIN_PROBE_ACTION_DNSLOOKUP_SRV_RECORD))  {
        struct zcdns_result_srv_record *srv;
        for (srv = result->srv; srv; srv = srv->next) {
            answer_count++;
        }
    }

    answer_count+=(result->cnames_count);

    return answer_count;
}

/*
 *
 * DNSLOOKUP_A_RECORD : ia2br.prod.zpath.net
 * Server : **********
 * Rcode : NoError
 * ia2br.prod.zpath.net   600   CNAME   ia2br.gslb.prod.zpath.net
 * ia2br.gslb.prod.zpath.net   60   CNAME   sjc4.ia2br.gslb.prod.zpath.net
 * sjc4.ia2br.gslb.prod.zpath.net   300   A   IN   104.129.193.252
 * sjc4.ia2br.gslb.prod.zpath.net   300   A   IN   104.129.193.189
 * sjc4.ia2br.gslb.prod.zpath.net   300   A   IN   104.129.193.190
 * sjc4.ia2br.gslb.prod.zpath.net   300   A   IN   104.129.193.187
 * sjc4.ia2br.gslb.prod.zpath.net   300   A   IN   ***************
 * sjc4.ia2br.gslb.prod.zpath.net   300   A   IN   ***************
 * sjc4.ia2br.gslb.prod.zpath.net   300   A   IN   ***************
 * sjc4.ia2br.gslb.prod.zpath.net   300   A   IN   ***************
 *
 *
 * DNSLOOKUP_A_RECORD : compass.com
 * Server : **********
 * Rcode : NoError
 * compass.com   60   A   IN   *********
 * compass.com   60   A   IN   **********
 * compass.com   60   A   IN   **********
 * compass.com   60   A   IN   ***********
 *
 *
 *
 * DNSLOOKUP_SRV_RECORD : _sip._udp.sip.voice.google.com
 * Server : **********
 * Rcode : NoError
 * priority = 20, weight= 1, port = 5060, target = sip-anycast-2.voice.google.com  300   IN   SRV
 * priority = 10, weight= 1, port = 5060, target = sip-anycast-1.voice.google.com  300   IN   SRV
 *
 *
 *
 * this function take the zcdns_result, format the result, place the results buf on a newly allocated memory,
 * and return the result to the caller.
 *
 * Caller has to free the memory returned in this funcion using ADMIN_PROBE_FREE()
 */
#define CLASS_STR_LEN 10
#define MAX_DNS_RESULT_HEADER_LEN 512
#define MAX_DNS_EACH_ANSWER_RESULT_LEN 256
static char* admin_probe_task_module_dns_txt(struct zcdns_result* result,
                                            char *action,
                                            char *domain,
                                            const char *rcode)
{
    if (!result) {
        ADMIN_PROBE_LOG(AL_ERROR, "no zcdns result - admin_probe_task_module_dns_txt");
        return NULL;
    }

    if (!domain) {
        ADMIN_PROBE_LOG(AL_ERROR, "no domain - admin_probe_task_module_dns_txt");
        return NULL;
    }

    if (!action) {
        ADMIN_PROBE_LOG(AL_ERROR, "no action - admin_probe_task_module_dns_txt");
        return NULL;
    }

    int number_of_answers;

    number_of_answers = 0;
    number_of_answers = admin_probe_task_module_dns_find_number_of_results_in_zcdns_result(result, action);
    if (!number_of_answers) {
        ADMIN_PROBE_DEBUG_TASK_MODULE_DNS("no answers in zcdns_result, returning");
        return NULL;
    }

    int buf_size = MAX_DNS_RESULT_HEADER_LEN + (number_of_answers * MAX_DNS_EACH_ANSWER_RESULT_LEN);

    char *buf = ADMIN_PROBE_TASK_MODULE_DNS_MALLOC(buf_size);

    ADMIN_PROBE_DEBUG_TASK_MODULE_DNS("preparing dns result for %s, answer count: %d, with alloc size %d ", domain, number_of_answers, buf_size);
    char *s = buf;
    char *e = s + buf_size;

    struct zcdns_result_addr_record *addr_a;
    struct zcdns_result_addr_record *addr_aaaa;
    struct zcdns_result_srv_record *srv;

    if (!result->addr_a && !result->addr_aaaa && !result->srv) {
        goto failure;
    }

    s += sxprintf(s, e, "%s : %s\n",action, domain);
    s += sxprintf(s, e, "entity_name : %s\n",admin_probe_state.entity_name);
    s += sxprintf(s, e, "entity_gid : %"PRId64"\n",admin_probe_state.entity_gid);

    char resolv_str[INET6_ADDRSTRLEN];
    if (result->resolver) {
        zcdns_sockaddr_storage_to_str(result->resolver, resolv_str, sizeof(resolv_str));
    } else {
        snprintf(resolv_str, INET6_ADDRSTRLEN, "%s", "");
    }
    s += sxprintf(s, e, "Server : %s\n", resolv_str);

    s += sxprintf(s, e, "Rcode : %s \n", rcode);

    char *cur_domain = domain;
    for (int i = 0; i < result->cnames_count; i++) {
        s += sxprintf(s, e, "%s   %d   CNAME   %s\n", cur_domain, result->cnames_ttl[i], result->cnames[i]);
        cur_domain = result->cnames[i];
    }

    for (addr_a = result->addr_a; addr_a; addr_a = addr_a->next) {
        char str[INET6_ADDRSTRLEN];
        char class[CLASS_STR_LEN];
        zcdns_sockaddr_storage_to_str(&(addr_a->addr), str, sizeof(str));
        admin_probe_task_module_dns_get_class_str(addr_a->a_class, class, CLASS_STR_LEN);
        s += sxprintf(s, e, "%s   %d   A   %s   %s\n",cur_domain, addr_a->a_ttl, class, str);
    }

    for (addr_aaaa = result->addr_aaaa; addr_aaaa; addr_aaaa = addr_aaaa->next) {
        char str[INET6_ADDRSTRLEN];
        char class[CLASS_STR_LEN];
        admin_probe_task_module_dns_get_class_str(addr_aaaa->a_class, class, CLASS_STR_LEN);
        zcdns_sockaddr_storage_to_str(&(addr_aaaa->addr), str, sizeof(str));
        s += sxprintf(s, e, "%s   %d   AAAA   %s   %s\n",cur_domain, addr_aaaa->a_ttl, class, str);
    }

    for (srv = result->srv; srv; srv = srv->next) {
        char class[CLASS_STR_LEN];
        admin_probe_task_module_dns_get_class_str(srv->a_class, class, CLASS_STR_LEN);
        s += sxprintf(s, e, "priority = %d, weight= %d, port = %d, target = %s  %d   %s   SRV\n", srv->priority, srv->weight, ntohs(srv->port_ne), srv->target, srv->a_ttl, class);
    }

    return buf;

failure:

        if (buf) ADMIN_PROBE_TASK_MODULE_DNS_FREE(buf);
        return NULL;

}


/*
 * This api will format the zcdns_result into 2 allocated strings, one in plaintext format, one in JSON format.
 * This api does not free the 2 buffers, it is the caller's responsibility to free them.
 *
 * not that buf_json is allocated using parson allocator, so caller has to use the api:
 * admin_probe_task_module_dns_format_probe_result_free_serialized_str_using_parson_allocator to free it.
 *
 * buf_txt can be freed using ADMIN_PROBE_FREE()
 */
static void admin_probe_task_module_dns_format_probe_result_report(struct zcdns_result* result,
                                                                   char *action,
                                                                   char *domain,
                                                                   const char **rcode,
                                                                   char **buf_json,
                                                                   char **buf_txt,
                                                                   int *dns_rcode)
{
    if (!result) {
        ADMIN_PROBE_LOG(AL_ERROR, "no zcdns result - admin_probe_task_module_dns_format_probe_result_report");
        return;
    }

    if (!action) {
        ADMIN_PROBE_LOG(AL_ERROR, "no action - admin_probe_task_module_dns_format_probe_result_report");
        return;
    }

    if (!domain) {
        ADMIN_PROBE_LOG(AL_ERROR, "no domain - admin_probe_task_module_dns_format_probe_result_report");
        return;
    }

    if (!rcode) {
        ADMIN_PROBE_LOG(AL_ERROR, "no rcode - admin_probe_task_module_dns_format_probe_result_report");
        return;
    }

    if (strcmp(action, ADMIN_PROBE_ACTION_DNSLOOKUP_A_RECORD) == 0) {
        *rcode = admin_probe_task_module_dns_rcode_lookup(result->a_rcode);
        if (dns_rcode) {
            *dns_rcode = result->a_rcode;
        }
    } else if (strcmp(action, ADMIN_PROBE_ACTION_DNSLOOKUP_AAAA_RECORD) == 0) {
        *rcode = admin_probe_task_module_dns_rcode_lookup(result->aaaa_rcode);
        if (dns_rcode) {
            *dns_rcode = result->aaaa_rcode;
        }
    } else if (strcmp(action, ADMIN_PROBE_ACTION_DNSLOOKUP_SRV_RECORD) == 0) {
        *rcode = admin_probe_task_module_dns_rcode_lookup(result->srv_rcode);
        if (dns_rcode) {
            *dns_rcode = result->srv_rcode;
        }
    }

    if (!(*rcode)) {
        ADMIN_PROBE_LOG(AL_ERROR, "Format DNS result %s : %s : rcode should have assigned an valid value at shit point", action, domain);
        return;
    }

    if (buf_json) {
        *buf_json = admin_probe_task_module_dns_report_json_in_str(result, action, domain, *rcode);
        if (*buf_json) {
            stats.number_of_buf_txt_result_success++;
            ADMIN_PROBE_DEBUG_TASK_MODULE_DNS("dns result json - rcode %s \n %s",*rcode, *buf_json);
        } else {
            stats.number_of_buf_txt_result_fail++;
        }

    }

    if (buf_txt) {
        *buf_txt = admin_probe_task_module_dns_txt(result, action, domain, *rcode);
        if (*buf_txt) {
            stats.number_of_buf_json_result_success++;
            ADMIN_PROBE_DEBUG_TASK_MODULE_DNS("dns result txt- rcode %s\n %s",*rcode, *buf_txt);
        } else {
            stats.number_of_buf_json_result_fail++;
        }
    }

    return;
}

static void
admin_probe_task_module_dns_callback(void*                cb_void_cookie,
                                     int64_t              cb_int_cookie,
                                     struct zcdns_result* result)
{

    struct admin_probe_task *cur_task;
    const char *rcode = NULL;

    cur_task = (struct admin_probe_task *)cb_void_cookie;

    if (!cur_task) {
        ADMIN_PROBE_LOG(AL_ERROR, "invalid cur_task cookie, can not proceed with dns cb");
        goto done;
    }

    if (!cur_task->command_uuid) {
        ADMIN_PROBE_LOG(AL_ERROR, "invalid command_uuid, can not proceed with dns cb");
        goto done;
    }

    if (!cur_task->action) {
        ADMIN_PROBE_LOG(AL_ERROR, "%s : invalid action, can not proceed with dns cb", cur_task->command_uuid);
        goto done;
    }

    if (!cur_task->target) {
        ADMIN_PROBE_LOG(AL_ERROR, "%s : invalid target, can not proceed with dns cb", cur_task->command_uuid);
        goto done;
    }

    state.total_dns_in_exec--;
    cur_task->end_time_cloud_s = admin_probe_callbacks.get_current_cloud_epoch_s_cb();

    if (cur_task->cleanup_criteria.is_execute_callback_outstanding) {
        cur_task->cleanup_criteria.is_execute_callback_outstanding = 0;
    }


    admin_probe_task_module_dns_format_probe_result_report(result, cur_task->action, cur_task->target, &rcode, &(cur_task->result_json), &(cur_task->result_txt), &(cur_task->task_state.dns_task_state.rcode));
    if (!cur_task->result_json && !cur_task->result_txt) {

        switch (cur_task->task_state.dns_task_state.dns_type) {
            case dns_a:
                stats.dns_request_do_a_fail++;
                break;
            case dns_aaaa:
                stats.dns_request_do_aaaa_fail++;
                break;
            case dns_srv:
                stats.dns_request_do_srv_fail++;
                break;
            default:
                break;
        }

        cur_task->err_message = admin_probe_task_module_dns_get_dns_failure_message(cur_task->task_state.dns_task_state.rcode);

        admin_probe_task_module_probe_process_event(NULL, cur_task->is_np_command_probe, cur_task, cur_task->command_uuid, internal_task_execute_done_failed);

        goto done;
    } else {

        switch (cur_task->task_state.dns_task_state.dns_type) {
            case dns_a:
                stats.dns_request_do_a_success++;
                break;
            case dns_aaaa:
                stats.dns_request_do_aaaa_success++;
                break;
            case dns_srv:
                stats.dns_request_do_srv_success++;
                break;
            default:
                break;
        }

        /*
         * in the case of successfully generated results string, but the state become task_reaping.
         * we have wasted time on generating the reports there
         *
         * but i think its nicer to go through the proper state machine rather than adding another state check in this function
         */
        admin_probe_task_module_probe_process_event(NULL, cur_task->is_np_command_probe, cur_task, cur_task->command_uuid, internal_task_execute_done_success);
    }

done:
    if (result) {
        zcdns_result_release(result);
        if (cur_task) cur_task->task_state.dns_task_state.results = NULL;
        stats.dns_result_released_in_cb++;
    }

}


static void admin_probe_dns_zevent_defer_callback(void *cookie1, void *cookie2)
{
    struct admin_probe_task *cur_task;

    cur_task = cookie1;
    admin_probe_task_module_dns_callback(cur_task, 0, cur_task->task_state.dns_task_state.results);
}


int admin_probe_task_module_process_dns_task_f(struct admin_probe_task *cur_task)
{
    int do_a;
    int do_aaaa;
    int do_srv;

    int64_t dns_limit_exec;

    dns_limit_exec = admin_probe_rate_limiting_get_concurrent_executing_limit_per_task_type(admin_probe_task_type_dns);
    ADMIN_PROBE_DEBUG_TASK_MODULE_DNS("threshold check - 's limit is currently at %"PRId64" ", dns_limit_exec);

    if (state.total_dns_in_exec >= dns_limit_exec) {
        stats.failed_to_exec_dns_because_of_threshold_reached++;
        return ADMIN_PROBE_TASK_LIMIT_REACHED;
    }

    if (0 == strcmp(cur_task->action, ADMIN_PROBE_ACTION_DNSLOOKUP_SRV_RECORD)) {
        do_a = 0;
        do_aaaa = 0;
        do_srv = 1;
        cur_task->task_state.dns_task_state.dns_type = dns_srv;
        stats.dns_request_do_srv++;
    } else if (0 == strcmp(cur_task->action, ADMIN_PROBE_ACTION_DNSLOOKUP_AAAA_RECORD)) {
        do_a = 0;
        do_aaaa = 1;
        do_srv = 0;
        cur_task->task_state.dns_task_state.dns_type = dns_aaaa;
        stats.dns_request_do_aaaa++;
    } else {
        do_a = 1;
        do_aaaa = 0;
        do_srv = 0;
        cur_task->task_state.dns_task_state.dns_type = dns_a;
        stats.dns_request_do_a++;
    }

    cur_task->task_state.dns_task_state.dns_req = zcdns_resolve(admin_probe_state.zcdns_ctx,
                                                                cur_task->target,
                                                                do_a,
                                                                do_aaaa,
                                                                do_srv,
                                                                &(cur_task->task_state.dns_task_state.results),
                                                                admin_probe_task_module_dns_callback,
                                                                cur_task,
                                                                0);

    /*For dns task, we do not need expiry timer, and we do not do upload to s3 either*/
    cur_task->cleanup_criteria.is_execute_callback_outstanding = 1;
    state.total_dns_in_exec++;
    ADMIN_PROBE_DEBUG_TASK_MODULE_DNS("%s : task dns - SEND OUT dns request %s : %s", cur_task->command_uuid, cur_task->action, cur_task->action);
    if (!cur_task->task_state.dns_task_state.dns_req ) {
        ADMIN_PROBE_DEBUG_TASK_MODULE_DNS("%s : task dns - getting synchrnous dns result for domain %s", cur_task->command_uuid, cur_task->action);

        /*
         * Get a synchrouns result back, but lets not go there now. as that would mess up the state transitions.
         * Lets return here and do the status update on PROCESSING first.
         *
         */
        zevent_defer(admin_probe_dns_zevent_defer_callback, cur_task, NULL, 0);
    }

    return ADMIN_PROBE_RESULT_NO_ERROR;
}

static int
admin_probe_task_module_dns_dump_stats(struct zpath_debug_state*  request_state,
                                      const char**               query_values,
                                      int                        query_value_count,
                                      void*                      cookie)
{
    char jsonout[4000];

    (void)query_values;
    (void)query_value_count;
    (void)cookie;

    if (ARGO_RESULT_NO_ERROR == argo_structure_dump(admin_probe_task_module_dns_stats_desciption,
                                                    &stats, jsonout, sizeof(jsonout), NULL, 1)) {
        ZDP("%s\n", jsonout);
    }
    return ADMIN_PROBE_RESULT_NO_ERROR;
}

int admin_probe_task_module_dns_debug_init()
{
    int res;

    if (!(admin_probe_task_module_dns_stats_desciption = argo_register_global_structure(ADMIN_PROBE_TASK_MODULE_DNS_STATS_HELPER))) {
        return ADMIN_PROBE_RESULT_ERR;
    }

    res = zpath_debug_add_read_command("dump the stats of task_module_dns",
                                  "/admin_probe/task_module_dns",
                                  admin_probe_task_module_dns_dump_stats,
                                  NULL,
                                  NULL);
    if (res){
        return ADMIN_PROBE_RESULT_ERR;
    }

    return ADMIN_PROBE_RESULT_NO_ERROR;
}

int admin_probe_task_module_dns_init_dns_request_f()
{
    return ADMIN_PROBE_RESULT_NO_ERROR;
}


int admin_probe_task_module_dns_check_expiry_in_processing_state_f()
{
    return ADMIN_PROBE_RESULT_NO_ERROR;
}

int admin_probe_task_module_dns_cancel_f()
{
    return ADMIN_PROBE_RESULT_NO_ERROR;
}

int64_t admin_probe_get_total_number_of_task_dns_a()
{
    return stats.dns_request_do_a;
}


int64_t admin_probe_get_total_number_of_task_dns_aaaa()
{
    return stats.dns_request_do_aaaa;
}


int64_t admin_probe_get_total_number_of_task_dns_srv()
{
    return stats.dns_request_do_srv;
}


int64_t admin_probe_get_total_number_of_task_dns_a_success()
{
    return stats.dns_request_do_a_success;
}


int64_t admin_probe_get_total_number_of_task_dns_aaaa_success()
{
    return stats.dns_request_do_aaaa_success;
}


int64_t admin_probe_get_total_number_of_task_dns_srv_success()
{
    return stats.dns_request_do_srv_success;
}

struct admin_probe_task_module_callouts admin_probe_task_module_callouts_dns = {
    admin_probe_task_module_dns_init_dns_request_f,
    admin_probe_task_module_process_dns_task_f,
    admin_probe_task_module_dns_check_expiry_in_processing_state_f,
    admin_probe_task_module_dns_free_dns_task_state_f,
    admin_probe_task_module_dns_cancel_f,
};
