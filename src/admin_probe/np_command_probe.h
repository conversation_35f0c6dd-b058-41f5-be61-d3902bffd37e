/*
 * np_command_probe.h. Copyright (C) 2025 Zscaler Inc, All Rights Reserved
 */

#ifndef _NP_COMMAND_PROBE_H_
#define _NP_COMMAND_PROBE_H_

#include "zpath_lib/zpath_customer.h"
#include "argo/argo.h"
#include "wally/wally.h"


/*
 * np_command_probe is table currently resides in all NP shards in RDS.
 *
 * Each row in this table represent a np command probe request from UI.
 *
 * ie:
 * a row entry in this table
 *  - entity_gid : the gid of the process, for connector, its the assistant_gid, for pbroker, it is the pbroker_gid
 *  - customer_gid : customer gid who runs the process.
 *  - command_uuid : the unique id of the probe command
 *  - action : the command action of the probe request,
 *             currently only support RESTART_PROCESS/RESTART_SYSTEM/DNSLOOKUP_A_RECORD/DNSLOOKUP_AAAA_RECORD/DNSLOOKUP_SRV_RECORD/FRR_CMDS
 *  - target : the argument following the action, ie for dns request, we need the domain to do the dns, in this case, domain is the target.
 *             note that for each row, we only have one domain.
 *  - additional_arguments : optional, if certain action requires extra info.
 *  - start_time : the time the process execute the command probe request, set by Admin_Probe library
 *  - end_time : the time the the process finishes execute the command probe request, set by Admin_Probe library
 *  - status : the status of the command probe, currently we only support PENDING/PROCESSING/COMPLETED/FAILED/COMPLETED/TIMEDOUT/CANCELLED/CANCEL_REQUEST
 *  - file_location : the s3 bucket location that the final completed result reside.
 *  - bucket_name : the single bucket of the s3 that we should upload the data
 *  - content : we don't have to worry about this field
 *  - duration_in_secs : the expiry time of a command probe request. ie, if its 10, that means once we process the request, if its not done in 10 seconds, we should time this out.
 *  - error_message : if a probe request fails, we should give use the error message, we will pass such error message through RPC status report to kafka and they will set the error_message accordingly.
 *
 * This table should only be consumed by Admin_Probe library.
 *
 */
struct np_command_probe                       /* _ARGO: object_definition */
{
    int64_t id;                                /* _ARGO: integer, key */
    /* Standard SQL fields. */
    int64_t sequence;                          /* _ARGO: integer, sequence */
    int64_t deleted;                           /* _ARGO: integer, deleted */
    int64_t session_entity_id;                 /* _ARGO: integer */
    int64_t entity_gid;                        /* _ARGO: integer, index */
    int64_t customer_gid;                      /* _ARGO: integer, index */
    char *command_uuid;                        /* _ARGO: string, index */
    char *action;                              /* _ARGO: string */
    char *target;                              /* _ARGO: string */
    char *additional_arguments;                /* _ARGO: string */
    int64_t start_time;                        /* _ARGO: integer */
    int64_t end_time;                          /* _ARGO: integer */
    char *status;                              /* _ARGO: string */
    char *file_location;                       /* _ARGO: string */

    /*time should have size int64_t, int32_t is good for now but will run out in 2038*/
    int64_t modified_time;                     /* _ARGO: integer */
    int64_t creation_time;                     /* _ARGO: integer */
    int64_t modifiedby_userid;                 /* _ARGO: integer */

    int64_t duration_in_secs;                  /* _ARGO: integer */
    char *bucket_name;                         /* _ARGO: string */

};

extern struct argo_structure_description *np_command_probe_description;

void argo_np_command_probe_init();

int np_command_probe_init(struct wally *wally, int64_t entity_gid, int fully_load, int register_with_zpath_table);

int np_command_probe_get_by_entity_gid(int64_t entity_gid,
                                        struct np_command_probe **links,
                                        size_t *res_count,
                                        wally_response_callback_f callback_f,
                                        void *callback_cookie,
                                        int64_t callback_id);


int np_command_probe_register_by_entity_gid_using_default_registrant(int64_t                       customer_gid,
                                                                      int64_t                       entity_gid,
                                                                      wally_response_callback_f     *callback,
                                                                      void                          *void_cookie);

#endif /* _NP_COMMAND_PROBE_H_ */
