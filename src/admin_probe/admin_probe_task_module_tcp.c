/*
 * admin_probe_task_module_tcp.c. Copyright (C) 2021 Zscaler Inc, All Rights Reserved
 *
 */
#include "zpath_lib/zpath_debug.h"

#include "admin_probe/admin_probe_task_module_tcp.h"
#include "admin_probe/admin_probe_task_module_common.h"
#include "zhealth/zhealth_probe_lib.h"
#include "admin_probe/admin_probe_lib.h"
#include "admin_probe/admin_probe_private.h"
#include "zcdns/zcdns.h"
#include "argo/argo.h"
#include "admin_probe/admin_probe_rate_limiting.h"
#include <sys/types.h>    /* XXX temporary hack to get u_ types */
#include <arpa/inet.h>
#include <netinet/in.h>
#include <netinet/ip.h>
#include <netinet/ip_icmp.h>
#include <inttypes.h>
#include <openssl/rand.h>
#include "parson/parson.h"

#include "zhealth/zhealth.h"

static
struct admin_probe_task_module_tcp_stats {                        /* _ARGO: * object_definition */
    int64_t number_of_tcp_probe_request;                          /* _ARGO: integer */
    int64_t number_of_tcp_probe_success;                          /* _ARGO: integer */
    int64_t number_of_tcp_probe_failed;                           /* _ARGO: integer */
    int64_t number_of_tcp_probe_cancelled;                        /* _ARGO: integer */
    int64_t number_of_tcp_probe_timeout;                          /* _ARGO: integer */
    int64_t tcp_probe_sent;                                       /* _ARGO: integer */
    int64_t tcp_check_destroyed;                                  /* _ARGO: integer */
    int64_t tcp_check_creation_failure;                           /* _ARGO: integer */
    int64_t tcp_probe_rate_limited_exceeded;                      /* _ARGO: integer */
    int64_t request_domain_freed;                                 /* _ARGO: integer */
    int64_t state_change_but_no_cur_task;                         /* _ARGO: integer */
    int64_t number_of_tasks_expire_in_processing_state;           /* _ARGO: integer */
    int64_t number_of_task_cancelled_due_to_init_failure;         /* _ARGO: integer */
    int64_t number_of_tcp_cancelled_due_to_ip_info_not_ready;     /* _ARGO: integer */
} stats;

#include "admin_probe/admin_probe_task_module_tcp_compiled_c.h"
static struct argo_structure_description*  admin_probe_task_module_tcp_stats_desciption;

static struct {
    int64_t total_tcp_in_exec;
} state;

void admin_probe_task_module_tcp_free_tcp_task_state_f(struct admin_probe_task *cur_task)
{

    if (!cur_task) {
        return;
    }

    struct tcp_task *tcp_probe_info = &(cur_task->task_state.tcp_task_state);

    if (!tcp_probe_info) {
        return;
    }

    if (tcp_probe_info->request_domain) {
        ADMIN_PROBE_FREE(tcp_probe_info->request_domain);
        tcp_probe_info->request_domain = NULL;
        stats.request_domain_freed++;
    }

    if (tcp_probe_info->tcp_check) {
        zhealth_combo_destroy(tcp_probe_info->tcp_check);
        tcp_probe_info->tcp_check = NULL;
        stats.tcp_check_destroyed++;
        state.total_tcp_in_exec--;
    }
}

int admin_probe_task_module_tcp_cancel_f()
{
    return ADMIN_PROBE_RESULT_NO_ERROR;
}
/*
 *  {
 *   "action": "TCP_PING",
 *   "target": "iphone.com:80",
 *   "ip": "************",
 *   "statistic": {
 *       "probes_transmitted": 3,
 *       "probes_received": 3,
 *       "probes_loss_percentage": 0,
 *       "max_rtt_us": 10524,
 *       "min_rtt_us": 9189,
 *       "avg_rtt_us": 10043
 *   }
 * }
 */
JSON_Value* admin_probe_task_module_tcp_add_stats_json(struct tcp_task *tcp_probe_info)
{
    if (!tcp_probe_info) {
        ADMIN_PROBE_LOG(AL_ERROR, "no tcp_probe_info, can not proceed further formating statictic in json form");
        return NULL;
    }

    JSON_Value *stats_val = json_value_init_object();
    JSON_Object *stats_obj = json_value_get_object(stats_val);

    json_object_set_number(stats_obj, "probes_transmitted", tcp_probe_info->statistics.probes_transmitted);
    json_object_set_number(stats_obj, "probes_received", tcp_probe_info->statistics.probes_received);
    json_object_set_number(stats_obj, "probes_loss_percentage", tcp_probe_info->statistics.probes_loss_percent);

    if (tcp_probe_info->statistics.probes_loss_percent == 100) {
        json_object_set_string(stats_obj, "min_rtt_us", "Unavailable");
        json_object_set_string(stats_obj, "avg_rtt_us", "Unavailable");
        json_object_set_string(stats_obj, "max_rtt_us", "Unavailable");
    } else {
        json_object_set_number(stats_obj, "min_rtt_us", tcp_probe_info->statistics.min_rtt_us);
        json_object_set_number(stats_obj, "avg_rtt_us", tcp_probe_info->statistics.avg_rtt_us);
        json_object_set_number(stats_obj, "max_rtt_us", tcp_probe_info->statistics.max_rtt_us);
    }

    return stats_val;
}


static char* admin_probe_task_module_tcp_report_in_json(char* command_uuid, char *action, char *target, struct tcp_task *tcp_probe_info, char *dst_ip)
{
    if (!action) {
        ADMIN_PROBE_LOG(AL_ERROR, "no action, can not proceed further formating tcp result in txt form");
        return NULL;
    }

    if (!target) {
        ADMIN_PROBE_LOG(AL_ERROR, "no target, can not proceed further formating tcp result in txt form");
        return NULL;
    }

    if (!tcp_probe_info) {
        ADMIN_PROBE_LOG(AL_ERROR, "no tcp_probe_info, can not proceed further tcp formating result in txt form");
        return NULL;
    }

    if (!dst_ip) {
        ADMIN_PROBE_LOG(AL_ERROR, "no dst_ip, can not proceed further tcp formating result in txt form");
        return NULL;
    }

    char *buf;

    JSON_Value *report_val = json_value_init_object();
    JSON_Object *report_obj = json_value_get_object(report_val);

    JSON_Value *statistic_val;

    statistic_val = admin_probe_task_module_tcp_add_stats_json(tcp_probe_info);
    if (!statistic_val) {
        json_value_free(report_val);
        return NULL;
    }

    char entity_id_str[ADMIN_PROBE_MAX_ENTITY_ID];

    snprintf(entity_id_str, sizeof(entity_id_str), "%"PRId64"", admin_probe_state.entity_gid);

    json_object_set_string(report_obj, "action", action);
    json_object_set_string(report_obj, "target", target);
    json_object_set_string(report_obj, "entity_name", admin_probe_state.entity_name);
    json_object_set_string(report_obj, "entity_gid", entity_id_str);
    json_object_set_string(report_obj, "ip", dst_ip);
    json_object_set_value(report_obj, "statistic", statistic_val);

    buf = json_serialize_to_string_pretty(report_val);

    ADMIN_PROBE_DEBUG_TASK_MODULE_TCP("%s : TCP probe result (JSON) :\n %s", command_uuid?command_uuid:" ", buf);

    json_value_free(report_val);

    return buf;
}

/*
 * TCP_PING : iphone.com:80
 * ip : ************
 * 3 probes transmitted, 3 received, 0% probes loss
 * round-trip min/avg/max = 10524 /9189 /10043.000000 us
 */
#define MAX_TCP_RESPONSE_TXT_LEN 556
static char* admin_probe_task_module_tcp_report_in_txt(char* command_uuid, char *action, char *target, struct tcp_task *tcp_probe_info, char *dst_ip)
{
    if (!action) {
        ADMIN_PROBE_LOG(AL_ERROR, "no action, can not proceed further formating tcp result in txt form");
        return NULL;
    }

    if (!target) {
        ADMIN_PROBE_LOG(AL_ERROR, "no target, can not proceed further formating tcp result in txt form");
        return NULL;
    }

    if (!tcp_probe_info) {
        ADMIN_PROBE_LOG(AL_ERROR, "no tcp_probe_info, can not proceed further tcp formating result in txt form");
        return NULL;
    }

    if (!dst_ip) {
        ADMIN_PROBE_LOG(AL_ERROR, "no dst_ip, can not proceed further tcp formating result in txt form");
        return NULL;
    }

    char *buf = ADMIN_PROBE_MALLOC(MAX_TCP_RESPONSE_TXT_LEN);
    char *s = buf;
    char *e = s + MAX_TCP_RESPONSE_TXT_LEN;

    s += sxprintf(s, e, "%s : %s\n", action, target);
    s += sxprintf(s, e, "entity_name : %s\n",admin_probe_state.entity_name);
    s += sxprintf(s, e, "entity_gid : %"PRId64"\n",admin_probe_state.entity_gid);
    s += sxprintf(s, e, "ip : %s\n", dst_ip);

    s += sxprintf(s, e, "%d probes transmitted, %d received, %d%c probes loss \n", tcp_probe_info->statistics.probes_transmitted,
                                                                                   tcp_probe_info->statistics.probes_received,
                                                                                   tcp_probe_info->statistics.probes_loss_percent,
                                                                                   '%');
    if (tcp_probe_info->statistics.probes_loss_percent == 100) {
        s += sxprintf(s, e, "round-trip min/avg/max = %s", "Unavailable");
    } else {
        s += sxprintf(s, e, "round-trip min/avg/max = %"PRId64" /%f /%"PRId64" µs", tcp_probe_info->statistics.min_rtt_us,
                                                                                    tcp_probe_info->statistics.avg_rtt_us,
                                                                                    tcp_probe_info->statistics.max_rtt_us);
    }

    ADMIN_PROBE_DEBUG_TASK_MODULE_TCP("%s : tcp probe result (txt) :\n %s", command_uuid?command_uuid:" ", buf);

    return buf;
}


static void admin_probe_task_module_tcp_format_probe_result_report(struct admin_probe_task *cur_task)
{
    if (!cur_task) {
        ADMIN_PROBE_LOG(AL_ERROR, "no cur_task, can not proceed with formatting tcp probe result");
        return;
    }

    cur_task->result_txt = admin_probe_task_module_tcp_report_in_txt(cur_task->command_uuid, cur_task->action, cur_task->target, &(cur_task->task_state.tcp_task_state), cur_task->common_state.dst_ip);
    cur_task->result_json = admin_probe_task_module_tcp_report_in_json(cur_task->command_uuid, cur_task->action, cur_task->target, &(cur_task->task_state.tcp_task_state), cur_task->common_state.dst_ip);
}

static void admin_probe_task_module_tcp_format_probe_err_result_report(struct admin_probe_task *cur_task)
{
    if (!cur_task) {
        ADMIN_PROBE_LOG(AL_ERROR, "no cur_task, can not proceed with formatting err tcp probe result");
        return;
    }

    cur_task->err_message = admin_probe_task_module_tcp_report_in_txt(cur_task->command_uuid, cur_task->action, cur_task->target, &(cur_task->task_state.tcp_task_state), cur_task->common_state.dst_ip);
}

static void admin_probe_task_module_tcp_task_done_completely(struct admin_probe_task *cur_task)
{
    if (!cur_task) {
        stats.state_change_but_no_cur_task++;
        ADMIN_PROBE_LOG(AL_ERROR, "no cur_task, can not proceed to process tcp probe result");
        return;
    }

    cur_task->end_time_cloud_s = admin_probe_callbacks.get_current_cloud_epoch_s_cb();

    if (cur_task->task_state.tcp_task_state.statistics.probes_loss_percent == 100) {
        admin_probe_task_module_tcp_format_probe_err_result_report(cur_task);

        if (!cur_task->err_message) {
            cur_task->err_message = ADMIN_PROBE_COMMAND_FAIL_NO_REPORT;
        }

        admin_probe_task_module_probe_process_event(NULL, cur_task->is_np_command_probe, cur_task, cur_task->command_uuid, internal_task_execute_done_failed);
        return;

    } else {
        admin_probe_task_module_tcp_format_probe_result_report(cur_task);

        if (!cur_task->result_json && !cur_task->result_txt) {
            cur_task->err_message = ADMIN_PROBE_COMMAND_SUCCESS_BUT_FAIL_TO_GEN_REPORT;
            stats.number_of_tcp_probe_failed++;

            admin_probe_task_module_probe_process_event(NULL, cur_task->is_np_command_probe, cur_task, cur_task->command_uuid, internal_task_execute_done_failed);

            return;
        } else {
            stats.number_of_tcp_probe_success++;
            admin_probe_task_module_probe_process_event(NULL, cur_task->is_np_command_probe, cur_task, cur_task->command_uuid, internal_task_execute_done_success);

            return;
        }
    }
}


void admin_probe_task_module_tcp_update_tcp_response_stats(struct admin_probe_task *cur_task, int64_t rtt_us)
{

    if (!cur_task) {
        ADMIN_PROBE_LOG(AL_ERROR, "no cur_task - admin_probe_task_module_tcp_update_tcp_response_stats");
        return;
    }


    if (rtt_us > cur_task->task_state.tcp_task_state.statistics.max_rtt_us) {
        cur_task->task_state.tcp_task_state.statistics.max_rtt_us = rtt_us;
    }

    if (!cur_task->task_state.tcp_task_state.statistics.min_rtt_us) {
        cur_task->task_state.tcp_task_state.statistics.min_rtt_us = rtt_us;
    }

    if (rtt_us < cur_task->task_state.tcp_task_state.statistics.min_rtt_us) {
        cur_task->task_state.tcp_task_state.statistics.min_rtt_us = rtt_us;
    }

    cur_task->task_state.tcp_task_state.statistics.total_rx_rtt_us += rtt_us;

    cur_task->task_state.tcp_task_state.statistics.avg_rtt_us = (double)(cur_task->task_state.tcp_task_state.statistics.total_rx_rtt_us)/cur_task->task_state.tcp_task_state.statistics.probes_received;
}


static void admin_probe_task_module_tcp_update_tcp_final_packets_stats(struct admin_probe_task *cur_task)
{
    if (!cur_task) {
        ADMIN_PROBE_LOG(AL_ERROR, "no cur_task, can not to update final packets tcp stats");
        return;
    }

    int packet_loss;
    packet_loss = cur_task->task_state.tcp_task_state.statistics.probes_transmitted - cur_task->task_state.tcp_task_state.statistics.probes_received;

    cur_task->task_state.tcp_task_state.statistics.packet_loss = packet_loss;
    cur_task->task_state.tcp_task_state.statistics.probes_loss_percent = (packet_loss / cur_task->task_state.tcp_task_state.statistics.probes_transmitted) * 100;
}


static int admin_probe_task_module_tcp_exec_tcp_task_check_rate_limit(struct admin_probe_task *cur_task)
{
    ADMIN_PROBE_ASSERT_HARD((cur_task!=NULL), "cur_task is NULL when checking rate limit to exec tcp task");

    int64_t tcp_limit_exec = admin_probe_rate_limiting_get_concurrent_executing_limit_per_task_type(admin_probe_task_type_tcp);

    if (state.total_tcp_in_exec >= tcp_limit_exec) {
        ADMIN_PROBE_DEBUG_TASK_MODULE_TCP("threshold check limit reached - TCP probe limit is currently at %"PRId64" ", tcp_limit_exec);
        stats.tcp_probe_rate_limited_exceeded++;
        return ADMIN_PROBE_TASK_LIMIT_REACHED;
    }

    return ADMIN_PROBE_RESULT_NO_ERROR;
}

static void admin_probe_task_module_tcp_free_tcp_check(struct zevent_base *base,
                                                       void *void_cookie,
                                                       int64_t int_cookie)
{
    struct admin_probe_task *cur_task = (struct admin_probe_task *)void_cookie;

    if (!cur_task) {
        return;
    }

    if (!cur_task->task_state.tcp_task_state.tcp_check) {
        return;
    }

    zhealth_combo_destroy(cur_task->task_state.tcp_task_state.tcp_check);
    cur_task->task_state.tcp_task_state.tcp_check = NULL;
    stats.tcp_check_destroyed++;
    state.total_tcp_in_exec--;
    cur_task->cleanup_criteria.is_execute_callback_outstanding = 0;

}

static void
admin_probe_task_module_tcp_probe_admin_probe_thread_cb(struct zhealth_combo*      combo,
                                                        void*                      void_cookie,
                                                        int64_t                    int_cookie,
                                                        enum zhealth_check_status  status,
                                                        int                        consecutive_successes,
                                                        int                        consecutive_failures,
                                                        int64_t                    rtt_us,
                                                        int64_t                    avg_rtt_us,
                                                        int64_t                    lowest_rtt_us)
{
    struct admin_probe_task *cur_task = (struct admin_probe_task *)void_cookie;

    if (!cur_task) {
        ADMIN_PROBE_LOG(AL_ERROR, "tcp probe health callback - no cookie");
        return;
    }

    cur_task->task_state.tcp_task_state.statistics.probes_transmitted++;

    ADMIN_PROBE_DEBUG_TASK_MODULE_TCP("%s : tcp probe health callback", cur_task->command_uuid?cur_task->command_uuid:"");

    if (cur_task->task_state.tcp_task_state.state.is_health_done) {
       zevent_base_call(admin_probe_state.zbase, admin_probe_task_module_tcp_free_tcp_check, cur_task, 0);
       return;
    }

    if (status == zhealth_check_status_success) {
        cur_task->task_state.tcp_task_state.statistics.probes_received++;
        admin_probe_task_module_tcp_update_tcp_response_stats(cur_task, rtt_us);
    }

    int num_target_probes;
    num_target_probes = (int) admin_probe_rate_limiting_get_tcp_probes_per_tcp_ping_command();

    if (cur_task->task_state.tcp_task_state.statistics.probes_transmitted == num_target_probes) {
        admin_probe_task_module_tcp_update_tcp_final_packets_stats(cur_task);
        admin_probe_task_module_tcp_task_done_completely(cur_task);
        cur_task->task_state.tcp_task_state.state.is_health_done = 1;

        /*
         * can not call zhealth_combo_destroy() here in current stack as we are this callback is called with a lock.
         * zhealth_combo_destroy() acquires the same lock.
         */
        zevent_base_call(admin_probe_state.zbase, admin_probe_task_module_tcp_free_tcp_check, cur_task, 0);
    }

}

/*
 * cur state: PENDING
 *
 * goal: spin up the tcp_task in health thread, if failed, this task will get retried later, if retry keep failing,
 *       it will eventually get cancelled.
 */
static int admin_probe_task_module_tcp_exec_tcp_task_now(struct admin_probe_task *cur_task)
{
    if (!cur_task) {
        return ADMIN_PROBE_RESULT_ERR;
    }

    cur_task->task_state.tcp_task_state.tcp_check = zhealth_combo_create_tcp(admin_probe_state.zhealth_ctx,
                                                                             &(cur_task->common_state.ip_inet),
                                                                             (uint16_t)cur_task->task_state.tcp_task_state.request_port,
                                                                             admin_probe_task_module_tcp_probe_admin_probe_thread_cb,
                                                                             cur_task,
                                                                             0,
                                                                             0);
    if (!cur_task->task_state.tcp_task_state.tcp_check) {
        stats.tcp_check_creation_failure++;
        ADMIN_PROBE_LOG(AL_ERROR, "%s : could not create tcp health probe for %s", cur_task->command_uuid?cur_task->command_uuid:" ",
                                                                                   cur_task->target?cur_task->target:" ");
        return ADMIN_PROBE_RESULT_ERR;
    }


    ADMIN_PROBE_DEBUG_TASK_MODULE_TCP("%s:%s tcp probe fired", cur_task->command_uuid?cur_task->command_uuid:" ",
                                                               cur_task->target?cur_task->target:" ");
    stats.tcp_probe_sent++;
    cur_task->cleanup_criteria.is_execute_callback_outstanding = 1;
    cur_task->task_state.tcp_task_state.tcp_probes_start_time_s = epoch_s();

    return ADMIN_PROBE_RESULT_NO_ERROR;
}

/*
 * task module state module check each task every 1 second (almost),
 * current state: PENDING
 * next state: PENDING (stay) / PROCESSING / CANCELLED
 *
 * for a PENDING status TCP probe, we will PROCESS the task only if following criteria met:
 *
 *  1. ip_inet and port is ready
 *  2. rate limit met
 *
 * return
 *     - ADMIN_PROBE_RESULT_NO_ERROR : we successfully spin up the task,
 *                                     task module will move the task to PROCESSING state
 *
 *     - ADMIN_PROBE_RESULT_ERR : tried exec but failed, will have retry counter incremented, if exceeded retry limit(default 3 times),
 *                                task module will CANCEL this task.
 *
 *     - ADMIN_PROBE_RESULT_BAD_STATE : can not spin up this task never, task module will move this task to CANCEL state.
 *
 *     - ADMIN_PROBE_TASK_NOT_READY_TO_EXEC : not yet ready to spin up this task, this task will stay in PENDING state and come back to exec later.
 *
 *     - ADMIN_PROBE_TASK_LIMIT_REACHED : not yet ready to spin up this task, this task will stay in PENDING state and come back to exec later.
 */
int admin_probe_task_module_tcp_process_tcp_task_f(struct admin_probe_task *cur_task)
{
    if (!cur_task) {
        ADMIN_PROBE_LOG(AL_ERROR, "reaching admin_probe_task_module_tcp_process_tcp_task_f but no task");
        return ADMIN_PROBE_RESULT_ERR;
    }

    int res;

    res = admin_probe_task_module_common_check_is_ip_info_is_ready_before_executing_task(cur_task);
    if (res) {
        if (res == ADMIN_PROBE_RESULT_BAD_STATE) {
            stats.number_of_tcp_cancelled_due_to_ip_info_not_ready++;
        }
        return res;
    }

    res = admin_probe_task_module_tcp_exec_tcp_task_check_rate_limit(cur_task);
    if (res) {
        return res;
    }

    /*proceed to send TCP probe*/
    res = admin_probe_task_module_tcp_exec_tcp_task_now(cur_task);
    if (res == ADMIN_PROBE_RESULT_NO_ERROR) {
        state.total_tcp_in_exec++;
    }

    return res;
}


/*
 * cur state : PROCESSING
 * we have started health probes for a target and spcified we want some number of health callback to happend.
 *
 * if the health probe is too slow, that we have not yet collected the ideal number of health callback.
 *
 * we cancel it if by this time, we have not recieved any stats.
 * we complete it if we already have some stats and we will send whatever we have.
 *
 */
int admin_probe_task_module_tcp_check_expiry_in_processing_state_f(struct admin_probe_task *cur_task)
{
    if (!cur_task) {
        ADMIN_PROBE_LOG(AL_ERROR, "reaching admin_probe_task_module_tcp_check_expiry_in_processing_state_f but no task");
        return ADMIN_PROBE_RESULT_ERR;
    }

    int64_t cur_local_time_s = epoch_s();
    int64_t tcp_ping_expiry_in_processing_s = admin_probe_rate_limiting_get_tcp_ping_expiry_in_processing_state_s();

    if (cur_local_time_s - cur_task->task_state.tcp_task_state.tcp_probes_start_time_s > tcp_ping_expiry_in_processing_s) {

        stats.number_of_tasks_expire_in_processing_state++;

        cur_task->task_state.tcp_task_state.state.is_health_done = 1;
        zhealth_combo_destroy(cur_task->task_state.tcp_task_state.tcp_check);
        cur_task->task_state.tcp_task_state.tcp_check = NULL;
        stats.tcp_check_destroyed++;
        state.total_tcp_in_exec--;
        cur_task->cleanup_criteria.is_execute_callback_outstanding = 0;

        if (cur_task->task_state.tcp_task_state.statistics.probes_received) {
            admin_probe_task_module_tcp_update_tcp_final_packets_stats(cur_task);

            //this will have the state transition PROCESSING->COMPLETED/FAILED
            admin_probe_task_module_tcp_task_done_completely(cur_task);

        } else {
            cur_task->err_message = ADMIN_PROBE_COMMAND_FAIL_IN_GENERAL;
            stats.number_of_tcp_probe_timeout++;
            return ADMIN_PROBE_RESULT_ERR;
        }


    }

    return ADMIN_PROBE_RESULT_NO_ERROR;
}




/*
 * For a new TCP_PING request,
 * the target will look like: google.com:80, or 10.1.2.3:80
 *
 * admin_probe will first parse the request, to exract the domain and port out
 * and proceed further.
 *
 * if return err, task module state machine will cancel this task.
 * if return no err, task module state machine will proceed moving this task to PENDING state.
 *
 */
int admin_probe_task_module_tcp_init_tcp_request_f(struct admin_probe_task *cur_task)
{
    if (!cur_task) {
        ADMIN_PROBE_LOG(AL_ERROR, "invalid cur_task, can not parse tcp admin probe request");
        return ADMIN_PROBE_RESULT_ERR;
    }

    if (!cur_task->target) {
        ADMIN_PROBE_LOG(AL_ERROR, "invalid target, can not parse tcp admin probe request");
        return ADMIN_PROBE_RESULT_ERR;
    }

    int res;

    stats.number_of_tcp_probe_request++;

    cur_task->common_state.is_ipv4 = 1;
    cur_task->common_state.is_ipv6 = 1;

    res = admin_probe_task_module_tcp_parse_request(cur_task);
    if (res) {
        goto task_cancel;
    }

    res = admin_probe_task_module_common_fill_ip_info_if_already_presented_in_request(cur_task->task_state.tcp_task_state.request_domain, cur_task);
    if (res) {
        goto task_cancel;
    }

    if (!cur_task->common_state.state.is_ip_info_ready) {
        res = admin_probe_task_module_common_resolve_domain_to_ip(cur_task->task_state.tcp_task_state.request_domain, cur_task);
        if (res) {
            ADMIN_PROBE_LOG(AL_ERROR, "%s: can not resolve for an ip for tcp target %s", cur_task->command_uuid? cur_task->command_uuid: " ",
                                                                                         cur_task->target);
            goto task_cancel;
        }
    }

    return ADMIN_PROBE_RESULT_NO_ERROR;

task_cancel:
    cur_task->err_message = ADMIN_PROBE_COMMAND_CANCELLED_AT_INIT;
    stats.number_of_task_cancelled_due_to_init_failure++;
    stats.number_of_tcp_probe_cancelled++;
    return ADMIN_PROBE_RESULT_ERR;
}

static int
admin_probe_task_module_tcp_dump_stats(struct zpath_debug_state*  request_state,
                                       const char**               query_values,
                                       int                        query_value_count,
                                       void*                      cookie)
{
    char jsonout[10000];

    (void)query_values;
    (void)query_value_count;
    (void)cookie;

    if (ARGO_RESULT_NO_ERROR == argo_structure_dump(admin_probe_task_module_tcp_stats_desciption,
                                                    &stats, jsonout, sizeof(jsonout), NULL, 1)) {
        ZDP("%s\n", jsonout);
        ZDP("total tcp task in exec : %"PRId64"\n", state.total_tcp_in_exec);
    }
    return ADMIN_PROBE_RESULT_NO_ERROR;
}

int admin_probe_task_module_tcp_debug_init()
{
    int res;

    if (!(admin_probe_task_module_tcp_stats_desciption = argo_register_global_structure(ADMIN_PROBE_TASK_MODULE_TCP_STATS_HELPER))) {
        return ADMIN_PROBE_RESULT_ERR;
    }

    res = zpath_debug_add_read_command("dump the stats of task_module_tcp",
                                  "/admin_probe/task_module_tcp_stats",
                                  admin_probe_task_module_tcp_dump_stats,
                                  NULL,
                                  NULL);
    if (res){
        return ADMIN_PROBE_RESULT_ERR;
    }

    return ADMIN_PROBE_RESULT_NO_ERROR;
}


int64_t admin_probe_get_total_number_of_task_tcp_ping()
{
    return stats.number_of_tcp_probe_request;
}

int64_t admin_probe_get_total_number_of_task_tcp_ping_success()
{
    return stats.number_of_tcp_probe_success;
}


struct admin_probe_task_module_callouts admin_probe_task_module_callouts_tcp = {
    admin_probe_task_module_tcp_init_tcp_request_f,
    admin_probe_task_module_tcp_process_tcp_task_f,
    admin_probe_task_module_tcp_check_expiry_in_processing_state_f,
    admin_probe_task_module_tcp_free_tcp_task_state_f,
    admin_probe_task_module_tcp_cancel_f
};
