/*
 * admin_probe_rate_limiting.c. Copyright (C) 2021 Zscaler Inc, All Rights Reserved
 *
 */

#include "admin_probe/admin_probe_rate_limiting.h"
#include "zpath_lib/zpath_config_override.h"
#include "zpath_lib/zpath_config_override_keys.h"

int64_t admin_probe_rate_limiting_get_concurrent_executing_limit_per_task_type(enum admin_probe_task_type type)
{
    int64_t value;

    value = 0;
    if (type == admin_probe_task_type_dns) {
        value = zpath_config_override_get_config_int(ADMIN_PROBE_TASK_MODULE_DNS_RATE_LIMITING_CONCURRENT_EXECUTING,
                                                     &value,
                                                     ADMIN_PROBE_TASK_MODULE_DNS_RATE_LIMITING_CONCURRENT_EXECUTING_DEFAULT,
                                                     admin_probe_state.entity_gid,
                                                     admin_probe_state.customer_gid,
                                                     (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                     (int64_t)0);
        return value;
    } else if (type == admin_probe_task_type_icmp) {
        value = zpath_config_override_get_config_int(ADMIN_PROBE_TASK_MODULE_ICMP_RATE_LIMITING_CONCURRENT_EXECUTING,
                                                     &value,
                                                     ADMIN_PROBE_TASK_MODULE_ICMP_RATE_LIMITING_CONCURRENT_EXECUTING_DEFAULT,
                                                     admin_probe_state.entity_gid,
                                                     admin_probe_state.customer_gid,
                                                     (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                     (int64_t)0);
        return value;
    } else if (type == admin_probe_task_type_tcp) {
        value = zpath_config_override_get_config_int(ADMIN_PROBE_TASK_MODULE_TCP_RATE_LIMITING_CONCURRENT_EXECUTING,
                                                     &value,
                                                     ADMIN_PROBE_TASK_MODULE_TCP_RATE_LIMITING_CONCURRENT_EXECUTING_DEFAULT,
                                                     admin_probe_state.entity_gid,
                                                     admin_probe_state.customer_gid,
                                                     (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                     (int64_t)0);
    } else if (type == admin_probe_task_type_tcpdump) {
        value = zpath_config_override_get_config_int(ADMIN_PROBE_TASK_MODULE_TCPDUMP_RATE_LIMITING_CONCURRENT_EXECUTING,
                                                     &value,
                                                     ADMIN_PROBE_TASK_MODULE_TCPDUMP_RATE_LIMITING_CONCURRENT_EXECUTING_DEFAULT,
                                                     admin_probe_state.entity_gid,
                                                     admin_probe_state.customer_gid,
                                                     (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                     (int64_t)0);
     } else if (type >= admin_probe_task_type_ip_route && type <= admin_probe_task_type_reload_bgp) {
        value = zpath_config_override_get_config_int(ADMIN_PROBE_TASK_MODULE_FRR_CMDS_RATE_LIMITING_CONCURRENT_EXECUTING,
                                                     &value,
                                                     ADMIN_PROBE_TASK_MODULE_FRR_CMDS_RATE_LIMITING_CONCURRENT_EXECUTING_DEFAULT,
                                                     admin_probe_state.entity_gid,
                                                     admin_probe_state.customer_gid,
                                                     (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                     (int64_t)0);
     }



    return value;
}

int64_t admin_probe_rate_limiting_get_uploader_http_send_timeout_us()
{
    int64_t value;

    value = 0;
    value = zpath_config_override_get_config_int(ADMIN_PROBE_UPLOADER_HTTP_SEND_TIMEOUT_US,
                                                 &value,
                                                 ADMIN_PROBE_UPLOADER_HTTP_SEND_TIMEOUT_DEFAULT_US,
                                                 admin_probe_state.entity_gid,
                                                 admin_probe_state.customer_gid,
                                                 (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                 (int64_t)0);
    if (value) {
        return value;
    } else {
        return ADMIN_PROBE_UPLOADER_HTTP_SEND_TIMEOUT_DEFAULT_US;
    }
}

int64_t admin_probe_rate_limiting_get_uploader_total_timeout_us()
{
    int64_t value;

    value = 0;
    value = zpath_config_override_get_config_int(ADMIN_PROBE_UPLOADER_TOTAL_TIMEOUT_US,
                                                 &value,
                                                 ADMIN_PROBE_UPLOADER_TOTAL_TIMEOUT_DEFAULT_US,
                                                 admin_probe_state.entity_gid,
                                                 admin_probe_state.customer_gid,
                                                 (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                 (int64_t)0);
    if (value) {
        return value;
    } else {
        return ADMIN_PROBE_UPLOADER_HTTP_SEND_TIMEOUT_DEFAULT_US;
    }
}

int64_t admin_probe_rate_limiting_get_uploader_retry_count()
{
    int64_t value;

    value = 0;
    value = zpath_config_override_get_config_int(ADMIN_PROBE_UPLOADER_RETRY_COUNT,
                                                 &value,
                                                 ADMIN_PROBE_UPLOADER_RETRY_COUNT_DEFAULT,
                                                 admin_probe_state.entity_gid,
                                                 admin_probe_state.customer_gid,
                                                 (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                 (int64_t)0);
    if (value) {
        return value;
    } else {
        return ADMIN_PROBE_UPLOADER_RETRY_COUNT_DEFAULT;
    }
}

int64_t admin_probe_rate_limiting_get_tcpdump_max_file_size()
{
    int64_t value;

    value = 0;
    value = zpath_config_override_get_config_int(ADMIN_PROBE_TCPDUMP_MAX_FILE_SIZE_BYTES,
                                                 &value,
                                                 ADMIN_PROBE_TCPDUMP_MAX_FILE_SIZE_DEFAULT_BYTES,
                                                 admin_probe_state.entity_gid,
                                                 admin_probe_state.customer_gid,
                                                 (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                 (int64_t)0);
    if (value) {
        return value;
    } else {
        return ADMIN_PROBE_TCPDUMP_MAX_FILE_SIZE_DEFAULT_BYTES;
    }
}

int64_t admin_probe_rate_limiting_get_tcpdump_max_chunk_size()
{
    int64_t value;

    value = 0;
    value = zpath_config_override_get_config_int(ADMIN_PROBE_TCPDUMP_MAX_CHUNK_SIZE_BYTES,
                                                 &value,
                                                 ADMIN_PROBE_TCPDUMP_MAX_CHUNK_SIZE_DEFAULT_BYTES,
                                                 admin_probe_state.entity_gid,
                                                 admin_probe_state.customer_gid,
                                                 (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                 (int64_t)0);
    if (value) {
        return value;
    } else {
        return ADMIN_PROBE_TCPDUMP_MAX_CHUNK_SIZE_DEFAULT_BYTES;
    }
}
/*
 * Making creation validation time value configurable.
 * Though i do not think its needed right now, in case when werid timing issues happening in prod, we will at aleast have a backup way to help.
 *
 * looking at the OT feature where in the future we will have connector using satellite link with very poor connection.
 * this configuration is really needed that time.
 */
int64_t admin_probe_rate_limiting_get_task_creation_validation_allowed_time_in_general_s()
{
    int64_t value;

    value = 0;
    value = zpath_config_override_get_config_int(ADMIN_PROBE_TASK_CREATION_TIME_VALIDATION_ALLOWED_GENERIC_TASK,
                                                 &value,
                                                 ADMIN_PROBE_TASK_CREATION_TIME_VALIDATION_ALLOWED_GENERIC_TASK_DEFAULT_S,
                                                 admin_probe_state.entity_gid,
                                                 admin_probe_state.customer_gid,
                                                 (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                 (int64_t)0);
    if (value) {
        return value;
    } else {
        return ADMIN_PROBE_TASK_CREATION_TIME_VALIDATION_ALLOWED_GENERIC_TASK_DEFAULT_S;
    }
}

/*
 * Separate creatime time validation for pcap which runs longer
 */
int64_t admin_probe_rate_limiting_get_task_creation_validation_allowed_time_in_tcpdump_s()
{
    int64_t value;

    value = 0;
    value = zpath_config_override_get_config_int(ADMIN_PROBE_TASK_CREATION_TIME_VALIDATION_ALLOWED_TCPDUMP_TASK,
                                                 &value,
                                                 ADMIN_PROBE_TASK_CREATION_TIME_VALIDATION_ALLOWED_TCPDUMP_TASK_DEFAULT_S,
                                                 admin_probe_state.entity_gid,
                                                 admin_probe_state.customer_gid,
                                                 (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                 (int64_t)0);
    if (value) {
        return value;
    } else {
        return ADMIN_PROBE_TASK_CREATION_TIME_VALIDATION_ALLOWED_TCPDUMP_TASK_DEFAULT_S;
    }
}

int64_t admin_probe_rate_limiting_get_task_creation_validation_allowed_time_for_restart_s()
{
    int64_t value;

    value = 0;
    value = zpath_config_override_get_config_int(ADMIN_PROBE_TASK_CREATION_TIME_VALIDATION_ALLOWED_FOR_RESTART,
                                                 &value,
                                                 ADMIN_PROBE_TASK_CREATION_TIME_VALIDATION_ALLOWED_FOR_RESTART_DEFAULT_S,
                                                 admin_probe_state.entity_gid,
                                                 admin_probe_state.customer_gid,
                                                 (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                 (int64_t)0);
    if (value) {
        return value;
    } else {
        return ADMIN_PROBE_TASK_CREATION_TIME_VALIDATION_ALLOWED_FOR_RESTART_DEFAULT_S;
    }
}


int64_t admin_probe_rate_limiting_get_termination_status_first_resend_time_general_s()
{
    int64_t value;

    value = 0;
    value = zpath_config_override_get_config_int(ADMIN_PROBE_TASK_MODULE_FIRST_TERMINATION_RESEND_TIME_GENERAL_S,
                                                 &value,
                                                 ADMIN_PROBE_TASK_MODULE_FIRST_TERMINATION_RESEND_TIME_GENERAL_DEFAULT_S,
                                                 admin_probe_state.entity_gid,
                                                 admin_probe_state.customer_gid,
                                                 (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                 (int64_t)0);
    if (value) {
        return value;
    } else {
        return ADMIN_PROBE_TASK_MODULE_FIRST_TERMINATION_RESEND_TIME_GENERAL_DEFAULT_S;
    }
}


int64_t admin_probe_rate_limiting_get_termination_status_resend_factor_general()
{
    int64_t value;

    value = 0;
    value = zpath_config_override_get_config_int(ADMIN_PROBE_TASK_MODULE_TERMINATION_STATUS_RESEND_FACTOR_GENERAL,
                                                 &value,
                                                 ADMIN_PROBE_TASK_MODULE_TERMINATION_STATUS_RESEND_FACTOR_GENERAL_DEFAULT,
                                                 admin_probe_state.entity_gid,
                                                 admin_probe_state.customer_gid,
                                                 (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                 (int64_t)0);
    if (value) {
        return value;
    } else {
        return ADMIN_PROBE_TASK_MODULE_TERMINATION_STATUS_RESEND_FACTOR_GENERAL_DEFAULT;
    }
}


int64_t admin_probe_rate_limiting_get_termination_resend_expiry_general_s()
{
    int64_t value;

    value = 0;
    value = zpath_config_override_get_config_int(ADMIN_PROBE_TASK_MODULE_TERMINATION_STATUS_RESEND_EXPIRY_GENERAL_S,
                                                 &value,
                                                 ADMIN_PROBE_TASK_MODULE_TERMINATION_STATUS_RESEND_EXPIRY_GENERAL_DEFAULT_S,
                                                 admin_probe_state.entity_gid,
                                                 admin_probe_state.customer_gid,
                                                 (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                 (int64_t)0);
    if (value) {
        return value;
    } else {
        return ADMIN_PROBE_TASK_MODULE_TERMINATION_STATUS_RESEND_EXPIRY_GENERAL_DEFAULT_S;
    }
}

int64_t admin_probe_rate_limiting_get_icmp_probes_per_icmp_ping_command()
{
    int64_t value;

    value = 0;
    value = zpath_config_override_get_config_int(ADMIN_PROBE_TASK_MODULE_NUMBER_OF_ICMP_PROBES,
                                                 &value,
                                                 ADMIN_PROBE_TASK_MODULE_NUMBER_OF_ICMP_PROBES_DEFAULT,
                                                 admin_probe_state.entity_gid,
                                                 admin_probe_state.customer_gid,
                                                 (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                 (int64_t)0);
    if (value) {
        return value;
    } else {
        return ADMIN_PROBE_TASK_MODULE_NUMBER_OF_ICMP_PROBES_DEFAULT;
    }
}


int64_t admin_probe_rate_limiting_get_icmp_ping_expiry_in_processing_state_s()
{
    int64_t value;

    value = 0;
    value = zpath_config_override_get_config_int(ADMIN_PROBE_TASK_MODULE_ICMP_EXPIRY_IN_PROCESSING,
                                                 &value,
                                                 ADMIN_PROBE_TASK_MODULE_ICMP_EXPIRY_IN_PROCESSING_DEFAULT_S,
                                                 admin_probe_state.entity_gid,
                                                 admin_probe_state.customer_gid,
                                                 (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                 (int64_t)0);
    if (value) {
        return value;
    } else {
        return ADMIN_PROBE_TASK_MODULE_ICMP_EXPIRY_IN_PROCESSING_DEFAULT_S;
    }
}



int64_t admin_probe_rate_limiting_get_tcp_probes_per_tcp_ping_command()
{
    int64_t value;

    value = 0;
    value = zpath_config_override_get_config_int(ADMIN_PROBE_TASK_MODULE_NUMBER_OF_TCP_PROBES,
                                                 &value,
                                                 ADMIN_PROBE_TASK_MODULE_NUMBER_OF_TCP_PROBES_DEFAULT,
                                                 admin_probe_state.entity_gid,
                                                 admin_probe_state.customer_gid,
                                                 (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                 (int64_t)0);
    if (value) {
        return value;
    } else {
        return ADMIN_PROBE_TASK_MODULE_NUMBER_OF_TCP_PROBES_DEFAULT;
    }
}


int64_t admin_probe_rate_limiting_get_tcp_ping_expiry_in_processing_state_s()
{
    int64_t value;

    value = 0;
    value = zpath_config_override_get_config_int(ADMIN_PROBE_TASK_MODULE_TCP_EXPIRY_IN_PROCESSING,
                                                 &value,
                                                 ADMIN_PROBE_TASK_MODULE_TCP_EXPIRY_IN_PROCESSING_DEFAULT_S,
                                                 admin_probe_state.entity_gid,
                                                 admin_probe_state.customer_gid,
                                                 (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                 (int64_t)0);
    if (value) {
        return value;
    } else {
        return ADMIN_PROBE_TASK_MODULE_TCP_EXPIRY_IN_PROCESSING_DEFAULT_S;
    }
}


int64_t admin_probe_rate_limiting_overall_max_tasks_allow_in_task_queue()
{
    int64_t value;

    value = 0;
    value = zpath_config_override_get_config_int(ADMIN_PROBE_TASK_MODULE_MAX_TASKS_ALLOWED_IN_TASK_QUEUE,
                                                 &value,
                                                 ADMIN_PROBE_TASK_MODULE_MAX_TASKS_ALLOWED_IN_TASK_QUEUE_DEFAULT,
                                                 admin_probe_state.entity_gid,
                                                 admin_probe_state.customer_gid,
                                                 (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                 (int64_t)0);
    if (value) {
        return value;
    } else {
        return ADMIN_PROBE_TASK_MODULE_MAX_TASKS_ALLOWED_IN_TASK_QUEUE_DEFAULT;
    }
}
