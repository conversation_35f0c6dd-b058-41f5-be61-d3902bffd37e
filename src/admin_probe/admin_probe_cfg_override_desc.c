/*
 * admin_probe_cfg_override_desc.c . Copyright (C) 2023-2024 Zscaler Inc. All Rights Reserved.
 *
 * admin probe's config override description registration.
 */

#include "zpath_lib/zpath_config_override_desc.h"
#include "zpath_lib/zpath_config_override_keys.h"
#include "admin_probe/admin_probe_lib.h"

static struct zpath_config_override_desc admin_probe_config_override_flags_descriptions_appc_pbroker[] = {
        {
                .key                = ADMIN_PROBE_TASK_MODULE_DNS_RATE_LIMITING_CONCURRENT_EXECUTING,
                .feature_group      = FEATURE_GROUP_DNS_ADMIN_PROBE,
                .desc               = "numbers of concurrent outstanding DNS request triggered by admin probe",
                .details            = "Order of check: component id, customer gid, global\n"
                                      "default: 10 (we allow 10 concurrent outstanding DNS request at a time)\n"
                                      "1: does only one dns request at a time, till up to 20\n"
                                      "its okay to increase the upper limit if really required but always remember not to harm the app connector",
                .val_type           = config_type_int,
                .component_types    = config_component_pbroker | config_component_appc,
                .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_cust | config_target_gid_type_global,
                .int_range_lo       = LOW_ADMIN_PROBE_TASK_MODULE_DNS_RATE_LIMITING_CONCURRENT_EXECUTING,
                .int_range_hi       = HIGH_ADMIN_PROBE_TASK_MODULE_DNS_RATE_LIMITING_CONCURRENT_EXECUTING,
                .int_default        = ADMIN_PROBE_TASK_MODULE_DNS_RATE_LIMITING_CONCURRENT_EXECUTING_DEFAULT,
                .value_traits       = config_value_traits_normal,
        },
        {
                .key                = ADMIN_PROBE_TASK_MODULE_ICMP_RATE_LIMITING_CONCURRENT_EXECUTING,
                .feature_group      = FEATURE_GROUP_ICMP_ADMIN_PROBE,
                .desc               = "numbers of concurrent outstanding ICMP request triggered by admin probe",
                .details            = "Order of check: component id, customer gid, global\n"
                                      "default: 20 (we allow 10 concurrent outstanding ICMP request at a time)\n"
                                      "1: does only one dns request at a time, till up to 30\n"
                                      "its okay to increase the upper limit if really required but always remember not to harm the app connector",
                .val_type           = config_type_int,
                .component_types    = config_component_pbroker | config_component_appc,
                .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_cust | config_target_gid_type_global,
                .int_range_lo       = LOW_ADMIN_PROBE_TASK_MODULE_ICMP_RATE_LIMITING_CONCURRENT_EXECUTING,
                .int_range_hi       = HIGH_ADMIN_PROBE_TASK_MODULE_ICMP_RATE_LIMITING_CONCURRENT_EXECUTING,
                .int_default        = ADMIN_PROBE_TASK_MODULE_ICMP_RATE_LIMITING_CONCURRENT_EXECUTING_DEFAULT,
                .value_traits       = config_value_traits_normal,
        },
        {
                .key                = ADMIN_PROBE_TASK_MODULE_TCP_RATE_LIMITING_CONCURRENT_EXECUTING,
                .feature_group      = FEATURE_GROUP_TCP_ADMIN_PROBE,
                .desc               = "numbers of concurrent outstanding TCP request triggered by admin probe",
                .details            = "Order of check: component id, customer gid, global\n"
                                      "default: 20 (we allow 10 concurrent outstanding TCP request at a time)\n"
                                      "1: does only one dns request at a time, till up to 30\n"
                                      "its okay to increase the upper limit if really required but always remember not to harm the app connector",
                .val_type           = config_type_int,
                .component_types    = config_component_pbroker | config_component_appc,
                .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_cust | config_target_gid_type_global,
                .int_range_lo       = LOW_ADMIN_PROBE_TASK_MODULE_TCP_RATE_LIMITING_CONCURRENT_EXECUTING,
                .int_range_hi       = HIGH_ADMIN_PROBE_TASK_MODULE_TCP_RATE_LIMITING_CONCURRENT_EXECUTING,
                .int_default        = ADMIN_PROBE_TASK_MODULE_TCP_RATE_LIMITING_CONCURRENT_EXECUTING_DEFAULT,
                .value_traits       = config_value_traits_normal,
        },
        {
                .key                = ADMIN_PROBE_TASK_MODULE_TCPDUMP_RATE_LIMITING_CONCURRENT_EXECUTING,
                .feature_group      = FEATURE_GROUP_TCPDUMP_ADMIN_PROBE,
                .desc               = "numbers of concurrent outstanding TCPDUMP request triggered by admin probe",
                .details            = "Order of check: component id, customer gid, global\n"
                                      "default: 1 (we allow 10 concurrent outstanding TCPDUMP request at a time)\n"
                                      "1: does only one dns request at a time\n"
                                      "tcpdump will be a heavy load on app connector, we restrict the concurrent allowed tcpdump jobs to 1\n"
                                      "if we want to pump up the upper limit, we should have qa verified before we make the change",
                .val_type           = config_type_int,
                .component_types    = config_component_pbroker | config_component_appc,
                .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_cust | config_target_gid_type_global,
                .int_range_lo       = LOW_ADMIN_PROBE_TASK_MODULE_TCPDUMP_RATE_LIMITING_CONCURRENT_EXECUTING,
                .int_range_hi       = HIGH_ADMIN_PROBE_TASK_MODULE_TCPDUMP_RATE_LIMITING_CONCURRENT_EXECUTING,
                .int_default        = ADMIN_PROBE_TASK_MODULE_TCPDUMP_RATE_LIMITING_CONCURRENT_EXECUTING_DEFAULT,
                .value_traits       = config_value_traits_normal,
        },
        {
                .key                = ADMIN_PROBE_TASK_CREATION_TIME_VALIDATION_ALLOWED_TCPDUMP_TASK,
                .feature_group      = FEATURE_GROUP_TCPDUMP_ADMIN_PROBE,
                .desc               = "delta time between current time when admin probe TCPDUMP request is received and the the the request is created from admin UI",
                .details            = "Order of check: component id, customer gid, global\n"
                                      "By default, for all new task command other than restart, we validate the creation time of the task.\n"
                                      "if its with default allowed time, we take the request, otherwise we ignore it.\n"
                                      "default: 15mins, tcpdump allows a longer delta due to we only process tcpdump one by one and each tcpdump might take up to 5mins\n"
                                      "6 min: a good minimum delta for admin probe to work fine\n"
                                      "20 min: do not make it too long, otherwise we might end up keep taking duplicate request",
                .val_type           = config_type_int,
                .component_types    = config_component_pbroker | config_component_appc,
                .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_cust | config_target_gid_type_global,
                .int_range_lo       = LOW_ADMIN_PROBE_TASK_CREATION_TIME_VALIDATION_ALLOWED_TCPDUMP_TASK_S,
                .int_range_hi       = HIGH_ADMIN_PROBE_TASK_CREATION_TIME_VALIDATION_ALLOWED_TCPDUMP_TASK_S,
                .int_default        = ADMIN_PROBE_TASK_CREATION_TIME_VALIDATION_ALLOWED_TCPDUMP_TASK_DEFAULT_S,
                .value_traits       = config_value_traits_normal,
        },
        {
                .key                = ADMIN_PROBE_TASK_CREATION_TIME_VALIDATION_ALLOWED_FOR_RESTART,
                .feature_group      = FEATURE_GROUP_APP_CONNECTOR_ADMIN_PROBE_RESTART,
                .desc               = "delta time between current time when admin probe RESTART request is received and the the the request is created from admin UI",
                .details            = "Order of check: component id, customer gid, global\n"
                                      "By default, for all new task command other than restart, we validate the creation time of the task.\n"
                                      "if its with default allowed time, we take the request, otherwise we ignore it.\n"
                                      "default: 5 second, delta has to be shorter for restart command to prevent admin probe restart in loop\n"
                                      "1 second: a good minimum delta for admin probe to work fine\n"
                                      "10 second: do not make it too long, otherwise we might end up restarting in loop",
                .val_type           = config_type_int,
                .component_types    = config_component_pbroker | config_component_appc,
                .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_cust | config_target_gid_type_global,
                .int_range_lo       = LOW_ADMIN_PROBE_TASK_CREATION_TIME_VALIDATION_ALLOWED_FOR_RESTART_S,
                .int_range_hi       = HIGH_ADMIN_PROBE_TASK_CREATION_TIME_VALIDATION_ALLOWED_FOR_RESTART_S,
                .int_default        = ADMIN_PROBE_TASK_CREATION_TIME_VALIDATION_ALLOWED_FOR_RESTART_DEFAULT_S,
                .value_traits       = config_value_traits_normal,
        },
        {
                .key                = ADMIN_PROBE_TASK_MODULE_NUMBER_OF_ICMP_PROBES,
                .feature_group      = FEATURE_GROUP_ICMP_ADMIN_PROBE,
                .desc               = "Number of ICMP probes we want to collect for one probe command request",
                .details            = "Order of check: component id, customer gid, global\n"
                                      "admin probe send # of ICMP echo request based on this number\n"
                                      "default: 3, we do 3 probe request and get 3 reponse back to UI\n"
                                      "1 probe: minimum 1 request 1 response\n"
                                      "10 probes, maximum 10 probes 10 response",
                .val_type           = config_type_int,
                .component_types    = config_component_pbroker | config_component_appc,
                .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_cust | config_target_gid_type_global,
                .int_range_lo       = LOW_ADMIN_PROBE_TASK_MODULE_NUMBER_OF_ICMP_PROBES,
                .int_range_hi       = HIGH_ADMIN_PROBE_TASK_MODULE_NUMBER_OF_ICMP_PROBES,
                .int_default        = ADMIN_PROBE_TASK_MODULE_NUMBER_OF_ICMP_PROBES_DEFAULT,
                .value_traits       = config_value_traits_normal,
        },
        {
                .key                = ADMIN_PROBE_TASK_MODULE_ICMP_EXPIRY_IN_PROCESSING,
                .feature_group      = FEATURE_GROUP_ICMP_ADMIN_PROBE,
                .desc               = "Expiry of ICMP commands once a ICMP probe request is in processing state",
                .details            = "Order of check: component id, customer gid, global\n"
                                      "default: 5*60, if its more than 5mins and some probes are still not combing back with response,\n "
                                      "we times up and send back we have\n"
                                      "1*60: minimum 1 min\n"
                                      "8*60, maximum 8 mins wait time",
                .val_type           = config_type_int,
                .component_types    = config_component_pbroker | config_component_appc,
                .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_cust | config_target_gid_type_global,
                .int_range_lo       = LOW_ADMIN_PROBE_TASK_MODULE_ICMP_EXPIRY_IN_PROCESSING_S,
                .int_range_hi       = HIGH_ADMIN_PROBE_TASK_MODULE_ICMP_EXPIRY_IN_PROCESSING_S,
                .int_default        = ADMIN_PROBE_TASK_MODULE_ICMP_EXPIRY_IN_PROCESSING_DEFAULT_S,
                .value_traits       = config_value_traits_normal,
        },
        {
                .key                = ADMIN_PROBE_TASK_MODULE_NUMBER_OF_TCP_PROBES,
                .feature_group      = FEATURE_GROUP_TCP_ADMIN_PROBE,
                .desc               = "Number of TCP probes we want to collect for one probe command request",
                .details            = "Order of check: component id, customer gid, global\n"
                                      "admin probe send # of TCP request based on this number\n"
                                      "default: 3, we do 3 probe request and get 3 reponse back to UI\n"
                                      "1 probe: minimum 1 request 1 response\n"
                                      "10 probes, maximum 10 probes 10 response",
                .val_type           = config_type_int,
                .component_types    = config_component_pbroker | config_component_appc,
                .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_cust | config_target_gid_type_global,
                .int_range_lo       = LOW_ADMIN_PROBE_TASK_MODULE_NUMBER_OF_TCP_PROBES,
                .int_range_hi       = HIGH_ADMIN_PROBE_TASK_MODULE_NUMBER_OF_TCP_PROBES,
                .int_default        = ADMIN_PROBE_TASK_MODULE_NUMBER_OF_TCP_PROBES_DEFAULT,
                .value_traits       = config_value_traits_normal,
        },
        {
                .key                = ADMIN_PROBE_TASK_MODULE_TCP_EXPIRY_IN_PROCESSING,
                .feature_group      = FEATURE_GROUP_TCP_ADMIN_PROBE,
                .desc               = "Expiry of TCP commands once a TCP probe request is in processing state",
                .details            = "Order of check: component id, customer gid, global\n"
                                      "default: 5*60, if its more than 5mins and some probes are still not combing back with response, \n"
                                      "we times up and send back we have\n"
                                      "1*60: minimum 1 min\n"
                                      "8*60, maximum 8 mins wait time",
                .val_type           = config_type_int,
                .component_types    = config_component_pbroker | config_component_appc,
                .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_cust | config_target_gid_type_global,
                .int_range_lo       = LOW_ADMIN_PROBE_TASK_MODULE_TCP_EXPIRY_IN_PROCESSING_S,
                .int_range_hi       = HIGH_ADMIN_PROBE_TASK_MODULE_TCP_EXPIRY_IN_PROCESSING_S,
                .int_default        = ADMIN_PROBE_TASK_MODULE_TCP_EXPIRY_IN_PROCESSING_DEFAULT_S,
                .value_traits       = config_value_traits_normal,
        },
};

static struct zpath_config_override_desc admin_probe_config_override_flags_descriptions_np[] = {
        {
                .key                = ADMIN_PROBE_TASK_MODULE_FRR_CMDS_RATE_LIMITING_CONCURRENT_EXECUTING,
                .feature_group      = FEATURE_GROUP_FRR_ADMIN_PROBE,
                .desc               = "numbers of concurrent outstanding FRR CMDS request triggered by admin probe",
                .details            = "Order of check: component id, customer gid, global\n"
                                      "default: 10 (we allow 10 concurrent outstanding FRR CMDS request at a time)\n"
                                      "1: does only one frr cmd request at a time, till upto 20\n"
                                      "if we want to pump up the upper limit, we should have qa verified before we make the change",
                .val_type           = config_type_int,
                .component_types    = config_component_appc | config_component_np_gateway,
                .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_cust | config_target_gid_type_global,
                .int_range_lo       = LOW_ADMIN_PROBE_TASK_MODULE_FRR_CMDS_RATE_LIMITING_CONCURRENT_EXECUTING,
                .int_range_hi       = HIGH_ADMIN_PROBE_TASK_MODULE_FRR_CMDS_RATE_LIMITING_CONCURRENT_EXECUTING,
                .int_default        = ADMIN_PROBE_TASK_MODULE_FRR_CMDS_RATE_LIMITING_CONCURRENT_EXECUTING_DEFAULT,
                .value_traits       = config_value_traits_normal,
        },
};

static struct zpath_config_override_desc admin_probe_config_override_flags_descriptions_all[] = {
        {
                .key                = ADMIN_PROBE_UPLOADER_HTTP_SEND_TIMEOUT_US,
                .feature_group      = FEATURE_GROUP_ADMIN_PROBE,
                .desc               = "maximum time out admin probe would wait till the connection established successfully to s3 bucket for sending the pcap result in millesecond",
                .details            = "Order of check: component id, customer gid, global\n"
                                      "default: 60*(1000*1000) -> 60ms\n"
                                      "60*(1000*1000) us: the minimum required time out, we should not go under this time out value\n"
                                      "5*60*(1000*1000) us: we can wait till up to 300us for the connection to established\n"
                                      "if the connection is taking more than 300us, we drop the task",
                .val_type           = config_type_int,
                .component_types    = config_component_pbroker | config_component_appc | config_component_np_gateway,
                .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_cust | config_target_gid_type_global,
                .int_range_lo       = LOW_ADMIN_PROBE_UPLOADER_HTTP_SEND_TIMEOUT_US,
                .int_range_hi       = HIGH_ADMIN_PROBE_UPLOADER_HTTP_SEND_TIMEOUT_US,
                .int_default        = ADMIN_PROBE_UPLOADER_HTTP_SEND_TIMEOUT_DEFAULT_US,
                .value_traits       = config_value_traits_normal,
        },
        {
                .key                = ADMIN_PROBE_UPLOADER_TOTAL_TIMEOUT_US,
                .feature_group      = FEATURE_GROUP_ADMIN_PROBE,
                .desc               = "maximum time tcpdump uploader should be wait from uploading the whole pcap file over to s3, time out if its taking longer than this value",
                .details            = "Order of check: component id, customer gid, global\n"
                                      "default: (5*60)*(1000*1000) -> 300ms\n"
                                      "(5*60)*(1000*1000) us: the minimum required time out, we should not go under this time out value\n"
                                      "(10*60)*(1000*1000) us: we can wait till up to 600us for the connection to established\n"
                                      "if the connection is taking more than 600us, we drop the task",
                .val_type           = config_type_int,
                .component_types    = config_component_pbroker | config_component_appc | config_component_np_gateway,
                .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_cust | config_target_gid_type_global,
                .int_range_lo       = LOW_ADMIN_PROBE_UPLOADER_TOTAL_TIMEOUT_US,
                .int_range_hi       = HIGH_ADMIN_PROBE_UPLOADER_TOTAL_TIMEOUT_US,
                .int_default        = ADMIN_PROBE_UPLOADER_TOTAL_TIMEOUT_DEFAULT_US,
                .value_traits       = config_value_traits_normal,
        },
        {
                .key                = ADMIN_PROBE_UPLOADER_RETRY_COUNT,
                .feature_group      = FEATURE_GROUP_ADMIN_PROBE,
                .desc               = "maximum retry count tcpdump uploader should retry to send pcap to s3, if after certain retry and still fail, uploader gives up sending",
                .details            = "Order of check: component id, customer gid, global\n"
                                      "default: 1, retry only once\n"
                                      "0: do not retry at all if the first attempt on sending the pcap fails\n"
                                      "2: retry at most this amount, then we will give up sending",
                .val_type           = config_type_int,
                .component_types    = config_component_pbroker | config_component_appc | config_component_np_gateway,
                .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_cust | config_target_gid_type_global,
                .int_range_lo       = LOW_ADMIN_PROBE_UPLOADER_RETRY_COUNT,
                .int_range_hi       = HIGH_ADMIN_PROBE_UPLOADER_RETRY_COUNT,
                .int_default        = ADMIN_PROBE_UPLOADER_RETRY_COUNT_DEFAULT,
                .value_traits       = config_value_traits_normal,
        },
        {
                .key                = ADMIN_PROBE_TCPDUMP_MAX_FILE_SIZE_BYTES,
                .feature_group      = FEATURE_GROUP_TCPDUMP_ADMIN_PROBE,
                .desc               = "maximum file size of the pcap file we captured for each individual tcpdump admin probe request",
                .details            = "Order of check: component id, customer gid, global\n"
                                      "default: 1GB\n"
                                      "(1*1024) 1kB: minimum 1kB file\n"
                                      "(1*1024*1024*1024) 1GB: maximum 1GB file size, do not allow too large as app connector is not majorly for tcpdump task",
                .val_type           = config_type_int,
                .component_types    = config_component_pbroker | config_component_appc | config_component_np_gateway,
                .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_cust | config_target_gid_type_global,
                .int_range_lo       = LOW_ADMIN_PROBE_TCPDUMP_MAX_FILE_SIZE_BYTES,
                .int_range_hi       = HIGH_ADMIN_PROBE_TCPDUMP_MAX_FILE_SIZE_BYTES,
                .int_default        = ADMIN_PROBE_TCPDUMP_MAX_FILE_SIZE_DEFAULT_BYTES,
                .value_traits       = config_value_traits_normal,
        },
        {
                .key                = ADMIN_PROBE_TCPDUMP_MAX_CHUNK_SIZE_BYTES,
                .feature_group      = FEATURE_GROUP_TCPDUMP_ADMIN_PROBE,
                .desc               = "maximum file chunk size each time we deliver to s3 bucket",
                .details            = "Order of check: component id, customer gid, global\n"
                                      "default: 100MB\n"
                                      "(1*1024*1024) 1MB: minimum 1MB chunk file\n"
                                      "(1 * 1024 * 1024 * 1024) 1GB: maximum 100MB file chunk size, do not allow too large as app connector is not majorly for tcpdump task",
                .val_type           = config_type_int,
                .component_types    = config_component_pbroker | config_component_appc | config_component_np_gateway,
                .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_cust | config_target_gid_type_global,
                .int_range_lo       = LOW_ADMIN_PROBE_TCPDUMP_MAX_CHUNK_SIZE_BYTES,
                .int_range_hi       = HIGH_ADMIN_PROBE_TCPDUMP_MAX_CHUNK_SIZE_BYTES,
                .int_default        = ADMIN_PROBE_TCPDUMP_MAX_CHUNK_SIZE_DEFAULT_BYTES,
                .value_traits       = config_value_traits_normal,
        },
        {
                .key                = ADMIN_PROBE_TASK_CREATION_TIME_VALIDATION_ALLOWED_GENERIC_TASK,
                .feature_group      = FEATURE_GROUP_ADMIN_PROBE,
                .desc               = "delta time between current time when general admin probe request is received and the the the request is created from admin UI",
                .details            = "Order of check: component id, customer gid, global\n"
                                      "By default, for all new task command other than restart, we validate the creation time of the task\n"
                                      "if its with default allowed time, we take the request, otherwise we ignore it\n"
                                      "default: 5mins\n"
                                      "1 min: a good minimum delta for admin probe to work fine\n"
                                      "7 min: do not make it too long, otherwise we might end up keep taking duplicate request",
                .val_type           = config_type_int,
                .component_types    = config_component_pbroker | config_component_appc | config_component_np_gateway,
                .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_cust | config_target_gid_type_global,
                .int_range_lo       = LOW_ADMIN_PROBE_TASK_CREATION_TIME_VALIDATION_ALLOWED_GENERIC_TASK_S,
                .int_range_hi       = HIGH_ADMIN_PROBE_TASK_CREATION_TIME_VALIDATION_ALLOWED_GENERIC_TASK_S,
                .int_default        = ADMIN_PROBE_TASK_CREATION_TIME_VALIDATION_ALLOWED_GENERIC_TASK_DEFAULT_S,
                .value_traits       = config_value_traits_normal,
        },
        {
                .key                = ADMIN_PROBE_TASK_MODULE_FIRST_TERMINATION_RESEND_TIME_GENERAL_S,
                .feature_group      = FEATURE_GROUP_ADMIN_PROBE,
                .desc               = "time we should wait to do the first resend if we are not hearing ack back from very first termination status update",
                .details            = "Order of check: component id, customer gid, global\n"
                                      "default: 300s (5min), we wait for 5min to resend the terminatino status report if we are not hearing ack back from previous very first sent one \n"
                                      "60 (1min): minimum 1 min\n"
                                      "600 (10min), if its larger than 10min, it wouldnt make sense to resend, as the admin probe already considered timedout",
                .val_type           = config_type_int,
                .component_types    = config_component_pbroker | config_component_appc | config_component_np_gateway,
                .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_cust | config_target_gid_type_global,
                .int_range_lo       = LOW_ADMIN_PROBE_TASK_MODULE_FIRST_TERMINATION_RESEND_TIME_GENERAL_S,
                .int_range_hi       = HIGH_ADMIN_PROBE_TASK_MODULE_FIRST_TERMINATION_RESEND_TIME_GENERAL_S,
                .int_default        = ADMIN_PROBE_TASK_MODULE_FIRST_TERMINATION_RESEND_TIME_GENERAL_DEFAULT_S,
                .value_traits       = config_value_traits_normal,
        },
        {
                .key                = ADMIN_PROBE_TASK_MODULE_TERMINATION_STATUS_RESEND_FACTOR_GENERAL,
                .feature_group      = FEATURE_GROUP_ADMIN_PROBE,
                .desc               = "get the next resend time by multiply with ADMIN_PROBE_TASK_MODULE_FIRST_TERMINATION_RESEND_TIME_GENERAL_S value",
                .details            = "Order of check: component id, customer gid, global\n"
                                      "we want to keep extending the interval the next resend time, this factor helps with extending the interval\n"
                                      "default: 2, if we resend first termination report in 60 second (1min) and have default factor == 5\n"
                                      "the next resend time would be 60 * 2 (factor)\n"
                                      "1: multiple by 1, 1 is the minimum\n"
                                      "5, its not recommend to have a large factor, as this will prevent us sending termination status report when there is no ack",
                .val_type           = config_type_int,
                .component_types    = config_component_pbroker | config_component_appc | config_component_np_gateway,
                .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_cust | config_target_gid_type_global,
                .int_range_lo       = LOW_PROBE_TASK_MODULE_TERMINATION_STATUS_RESEND_FACTOR_GENERAL,
                .int_range_hi       = HIGH_PROBE_TASK_MODULE_TERMINATION_STATUS_RESEND_FACTOR_GENERAL,
                .int_default        = ADMIN_PROBE_TASK_MODULE_TERMINATION_STATUS_RESEND_FACTOR_GENERAL_DEFAULT,
                .value_traits       = config_value_traits_normal,
        },
        {
                .key                = ADMIN_PROBE_TASK_MODULE_TERMINATION_STATUS_RESEND_EXPIRY_GENERAL_S,
                .feature_group      = FEATURE_GROUP_ADMIN_PROBE,
                .desc               = "time out where we should stop waiting for ack for termination status report",
                .details            = "Order of check: component id, customer gid, global\n"
                                      "default: 10*60, if its been 10mins since we start reporting termination and we still not hearing ack, we give up\n"
                                      "1*60 : will stop sending termination report if not hearing ack back within 1 min\n"
                                      "10*60, maximum expiry as this is also the expiry on UI/API",
                .val_type           = config_type_int,
                .component_types    = config_component_pbroker | config_component_appc | config_component_np_gateway,
                .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_cust | config_target_gid_type_global,
                .int_range_lo       = LOW_ADMIN_PROBE_TASK_MODULE_TERMINATION_STATUS_RESEND_EXPIRY_GENERAL_S,
                .int_range_hi       = HIGH_ADMIN_PROBE_TASK_MODULE_TERMINATION_STATUS_RESEND_EXPIRY_GENERAL_S,
                .int_default        = ADMIN_PROBE_TASK_MODULE_TERMINATION_STATUS_RESEND_EXPIRY_GENERAL_DEFAULT_S,
                .value_traits       = config_value_traits_normal,
        },
        {
                .key                = ADMIN_PROBE_TASK_MODULE_FIRST_TERMINATION_RESEND_TIME_RESTART_S,
                .feature_group      = FEATURE_GROUP_APP_CONNECTOR_ADMIN_PROBE_RESTART,
                .desc               = "time out where we should stop waiting ack for termination status report for restart",
                .details            = "Order of check: component id, customer gid, global\n"
                                      "default: 30, if its been 30 seconds since we start reporting termination and we still not hearing ack, we give up\n"
                                      "5 : will stop sending termination report if not hearing ack back within 5 seconds\n"
                                      "30, maximum expiry waiting termination ack for restart task",
                .val_type           = config_type_int,
                .component_types    = config_component_pbroker | config_component_appc | config_component_np_gateway,
                .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_cust | config_target_gid_type_global,
                .int_range_lo       = LOW_ADMIN_PROBE_TASK_MODULE_FIRST_TERMINATION_RESEND_TIME_RESTART_S,
                .int_range_hi       = HIGH_ADMIN_PROBE_TASK_MODULE_FIRST_TERMINATION_RESEND_TIME_RESTART_S,
                .int_default        = ADMIN_PROBE_TASK_MODULE_FIRST_TERMINATION_RESEND_TIME_RESTART_DEFAULT_S,
                .value_traits       = config_value_traits_normal,
        },
        {
                .key                = ADMIN_PROBE_TASK_MODULE_TERMINATION_STATUS_RESEND_FACTOR_RESTART,
                .feature_group      = FEATURE_GROUP_APP_CONNECTOR_ADMIN_PROBE_RESTART,
                .desc               = "get the next resend time by multiply with ADMIN_PROBE_TASK_MODULE_FIRST_TERMINATION_RESEND_TIME_GENERAL_S value",
                .details            = "Order of check: component id, customer gid, global\n"
                                      "we want to keep extending the interval the next resend time, this factor helps with extending the interval\n"
                                      "default: 1\n"
                                      "1: multiple by 1, 1 is the minimum\n"
                                      "1, its not recommend to have a large factor, as this will prevent us sending termination status report when there is no ack",
                .val_type           = config_type_int,
                .component_types    = config_component_pbroker | config_component_appc | config_component_np_gateway,
                .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_cust | config_target_gid_type_global,
                .int_range_lo       = LOW_ADMIN_PROBE_TASK_MODULE_TERMINATION_STATUS_RESEND_FACTOR_RESTART,
                .int_range_hi       = HIGH_ADMIN_PROBE_TASK_MODULE_TERMINATION_STATUS_RESEND_FACTOR_RESTART,
                .int_default        = ADMIN_PROBE_TASK_MODULE_TERMINATION_STATUS_RESEND_FACTOR_RESTART_DEFAULT,
                .value_traits       = config_value_traits_normal,
        },
        {
                .key                = ADMIN_PROBE_TASK_MODULE_TERMINATION_STATUS_RESEND_EXPIRY_RESTART_S,
                .feature_group      = FEATURE_GROUP_APP_CONNECTOR_ADMIN_PROBE_RESTART,
                .desc               = "time out where we should stop waiting for ack for termination status report",
                .details            = "Order of check: component id, customer gid, global\n"
                                      "default: 5*60, if its been 5 mins since we start reporting termination and we still not hearing ack, we give up\n"
                                      "1*60 : will stop sending termination report if not hearing ack back within 1 min\n"
                                      "5*60, maximum expiry as this is also the expiry on UI/API",
                .val_type           = config_type_int,
                .component_types    = config_component_pbroker | config_component_appc | config_component_np_gateway,
                .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_cust | config_target_gid_type_global,
                .int_range_lo       = LOW_ADMIN_PROBE_TASK_MODULE_TERMINATION_STATUS_RESEND_EXPIRY_RESTART_S,
                .int_range_hi       = HIGH_ADMIN_PROBE_TASK_MODULE_TERMINATION_STATUS_RESEND_EXPIRY_RESTART_S,
                .int_default        = ADMIN_PROBE_TASK_MODULE_TERMINATION_STATUS_RESEND_EXPIRY_RESTART_DEFAULT_S,
                .value_traits       = config_value_traits_normal,
        },
        {
                .key                = ADMIN_PROBE_TASK_MODULE_MAX_TASKS_ALLOWED_IN_TASK_QUEUE,
                .feature_group      = FEATURE_GROUP_ADMIN_PROBE,
                .desc               = "maximum allowed tasks we allowed to keep in queue in our memory",
                .details            = "Order of check: component id, customer gid, global\n"
                                      "default: 150, we will only take new admin probe request if there is room in the queue\n"
                                      "0 : we should at least be taking 1 task\n"
                                      "300, app connector is not majorly feature for admin probe, so lets keep this limit",
                .val_type           = config_type_int,
                .component_types    = config_component_pbroker | config_component_appc | config_component_np_gateway,
                .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_cust | config_target_gid_type_global,
                .int_range_lo       = LOW_ADMIN_PROBE_TASK_MODULE_MAX_TASKS_ALLOWED_IN_TASK_QUEUE,
                .int_range_hi       = HIGH_ADMIN_PROBE_TASK_MODULE_MAX_TASKS_ALLOWED_IN_TASK_QUEUE,
                .int_default        = ADMIN_PROBE_TASK_MODULE_MAX_TASKS_ALLOWED_IN_TASK_QUEUE_DEFAULT,
                .value_traits       = config_value_traits_normal,
        }
};

int admin_probe_cfg_override_desc_register_all()
{
        int desc_count = sizeof(admin_probe_config_override_flags_descriptions_all) / sizeof(struct zpath_config_override_desc);
        int res = ADMIN_PROBE_RESULT_NO_ERROR;

        for (int i = 0; i < desc_count; i++) {
                res = zpath_config_override_desc_register(&admin_probe_config_override_flags_descriptions_all[i]);
                if (res) {
                        break;
                }
        }

        return res;

}

int admin_probe_cfg_override_desc_register_appc_pbroker()
{
        int desc_count = sizeof(admin_probe_config_override_flags_descriptions_appc_pbroker) / sizeof(struct zpath_config_override_desc);
        int res = ADMIN_PROBE_RESULT_NO_ERROR;

        for (int i = 0; i < desc_count; i++) {
                res = zpath_config_override_desc_register(&admin_probe_config_override_flags_descriptions_appc_pbroker[i]);
                if (res) {
                        break;
                }
        }

        return res;

}

int admin_probe_cfg_override_desc_register_np()
{
        int desc_count = sizeof(admin_probe_config_override_flags_descriptions_np) / sizeof(struct zpath_config_override_desc);
        int res = ADMIN_PROBE_RESULT_NO_ERROR;

        for (int i = 0; i < desc_count; i++) {
                res = zpath_config_override_desc_register(&admin_probe_config_override_flags_descriptions_np[i]);
                if (res) {
                        break;
                }
        }

        return res;

}
