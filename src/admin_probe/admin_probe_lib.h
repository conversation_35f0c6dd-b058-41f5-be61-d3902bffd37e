/*
 * admin_probe_lib.h. Copyright (C) 2021 Zscaler Inc, All Rights Reserved
 *
 */

#ifndef _ADMIN_PROBE_LIB_H_
#define _ADMIN_PROBE_LIB_H_

#include "zpath_lib/zpath_app.h"
#include "zpath_lib/zpath_assert.h"
#include "admin_probe/admin_probe_private.h"

#define ADMIN_PROBE_DEBUG_PROBE_BIT                       (uint64_t)0x00000001
#define ADMIN_PROBE_DEBUG_TABLE_BIT                       (uint64_t)0x00000002
#define ADMIN_PROBE_DEBUG_TASK_MODULE_BIT                 (uint64_t)0x00000004
#define ADMIN_PROBE_DEBUG_UPLOADER_BIT                    (uint64_t)0x00000008
#define ADMIN_PROBE_DEBUG_TASK_MODULE_DNS_BIT             (uint64_t)0x00000010
#define ADMIN_PROBE_DEBUG_TASK_MODULE_ICMP_BIT            (uint64_t)0x00000020
#define ADMIN_PROBE_DEBUG_TASK_MODULE_TCP_BIT             (uint64_t)0x00000040
#define ADMIN_PROBE_DEBUG_PARSE_BIT                       (uint64_t)0x00000080
#define ADMIN_PROBE_DEBUG_TASK_MODULE_TCPDUMP_BIT         (uint64_t)0x00000100
#define ADMIN_PROBE_DEBUG_TASK_MODULE_FRR_CMDS_BIT        (uint64_t)0x00000200


#define ADMIN_PROBE_RESULT_NO_ERROR          ZPATH_RESULT_NO_ERROR          /* AKA success */
#define ADMIN_PROBE_RESULT_ERR               ZPATH_RESULT_ERR               /* Generic error, when none other are appropriate */
#define ADMIN_PROBE_RESULT_NOT_FOUND         ZPATH_RESULT_NOT_FOUND         /* Could not find what was requested. */
#define ADMIN_PROBE_RESULT_NO_MEMORY         ZPATH_RESULT_NO_MEMORY         /* Could not allocate memory */
#define ADMIN_PROBE_RESULT_CANT_WRITE        ZPATH_RESULT_CANT_WRITE        /* Failure to write (output callback failed?) */
#define ADMIN_PROBE_RESULT_ERR_TOO_LARGE     ZPATH_RESULT_ERR_TOO_LARGE     /* Requested data doesn't fit in space provided */
#define ADMIN_PROBE_RESULT_BAD_ARGUMENT      ZPATH_RESULT_BAD_ARGUMENT      /* Asked for something wrong. */
#define ADMIN_PROBE_RESULT_INSUFFICIENT_DATA ZPATH_RESULT_INSUFFICIENT_DATA /* Was not provided enough data to perform operation */
#define ADMIN_PROBE_RESULT_NOT_IMPLEMENTED   ZPATH_RESULT_NOT_IMPLEMENTED   /* Yes, for features that are not yet implemented. */
#define ADMIN_PROBE_RESULT_BAD_DATA          ZPATH_RESULT_BAD_DATA          /* Tried to parse data, but format seemed wrong. */
#define ADMIN_PROBE_RESULT_WOULD_BLOCK       ZPATH_RESULT_WOULD_BLOCK       /* Attempting operation would result in blocking. Bad, naughty blocking. */
#define ADMIN_PROBE_RESULT_BAD_STATE         ZPATH_RESULT_BAD_STATE         /* Encountered bad internal state while attempting operation */
#define ADMIN_PROBE_RESULT_INCOMPLETE        ZPATH_RESULT_INCOMPLETE
#define ADMIN_PROBE_RESULT_ASYNCHRONOUS      ZPATH_RESULT_ASYNCHRONOUS
#define ADMIN_PROBE_RESULT_EXCESS_DYN_FIELDS ZPATH_RESULT_EXCESS_DYN_FIELDS /* The RPC has too many dynamic fields */
#define ADMIN_PROBE_RESULT_NOT_READY         ZPATH_RESULT_NOT_READY         /* Requested DB is not ready. */

#define ADMIN_PROBE_TASK_LIMIT_REACHED       (ZPATH_RESULT_MAX + 1)
#define ADMIN_PROBE_TASK_NOT_READY_TO_EXEC   (ZPATH_RESULT_MAX + 2)

extern struct zpath_allocator admin_probe_allocator;
extern struct argo_log_collection *admin_probe_event_collection;

#define ADMIN_PROBE_MALLOC(x) zpath_malloc(&admin_probe_allocator, x, __LINE__, __FILE__)
#define ADMIN_PROBE_FREE(x) zpath_free(x, __LINE__, __FILE__)
#define ADMIN_PROBE_FREE_SLOW(x) zpath_free_slow(x, __LINE__, __FILE__)
#define ADMIN_PROBE_CALLOC(x) zpath_calloc(&admin_probe_allocator, x, __LINE__, __FILE__)
#define ADMIN_PROBE_STRDUP(x, y) zpath_strdup(&admin_probe_allocator, x, y, __LINE__, __FILE__)
#define ADMIN_PROBE_SAFE_STRDUP(x) ZDX_STRDUP(x, strlen(x) + 1)

#define ADMIN_PROBE_COMMAND_TYPE_ZPN_PROBE      0
#define ADMIN_PROBE_COMMAND_TYPE_NP_PROBE       1

/*Task Action*/
#define ADMIN_PROBE_ACTION_RESTART_PROCESS "RESTART_PROCESS"
#define ADMIN_PROBE_ACTION_RESTART_SYSTEM "RESTART_SYSTEM"
#define ADMIN_PROBE_ACTION_DNSLOOKUP_A_RECORD "DNSLOOKUP_A_RECORD"
#define ADMIN_PROBE_ACTION_DNSLOOKUP_AAAA_RECORD "DNSLOOKUP_AAAA_RECORD"
#define ADMIN_PROBE_ACTION_DNSLOOKUP_SRV_RECORD "DNSLOOKUP_SRV_RECORD"
#define ADMIN_PROBE_ACTION_ICMP_PING "ICMP_PING"
#define ADMIN_PROBE_ACTION_TCP_PING "TCP_PING"
#define ADMIN_PROBE_ACTION_TCPDUMP "PCAP"

/* NP Task Actions */
#define ADMIN_PROBE_ACTION_IP_ROUTE "IP_ROUTE"
#define ADMIN_PROBE_ACTION_IP_INTERFACES "IP_INTERFACES"
#define ADMIN_PROBE_ACTION_IP_BGP "IP_BGP"
#define ADMIN_PROBE_ACTION_IP_BGP_NEIGHBORES "IP_BGP_NEIGHBORES"
#define ADMIN_PROBE_ACTION_IP_BGP_SUMMARY "IP_BGP_SUMMARY"
#define ADMIN_PROBE_ACTION_CLEAR_BGP "CLEAR_BGP"
#define ADMIN_PROBE_ACTION_RUNNING_CONFIG "RUNNING_CONFIG"
#define ADMIN_PROBE_ACTION_FAIELD_CONFIG "FAILED_CONFIG"
#define ADMIN_PROBE_ACTION_GENERATED_CONFIG "GENERATED_CONFIG"
#define ADMIN_PROBE_ACTION_CONFIG_VALIDATE "CONFIG_VALIDATE"
#define ADMIN_PROBE_ACTION_CONFIG_STATUS "CONFIG_STATUS"
#define ADMIN_PROBE_ACTION_CONFIG_STATUS_DETAILS "CONFIG_STATUS_DETAILS"
#define ADMIN_PROBE_ACTION_GET_LOGS "GET_LOGS"
#define ADMIN_PROBE_ACTION_STOP_BGP "STOP_BGP"
#define ADMIN_PROBE_ACTION_START_BGP "START_BGP"
#define ADMIN_PROBE_ACTION_RESTART_BGP "RESTART_BGP"
#define ADMIN_PROBE_ACTION_STATUS_BGP "STATUS_BGP"
#define ADMIN_PROBE_ACTION_RELOAD_BGP "RELOAD_BGP"

/*Task Status*/
#define ADMIN_PROBE_STATUS_PENDING        "PENDING"
#define ADMIN_PROBE_STATUS_PROCESSING     "PROCESSING"
#define ADMIN_PROBE_STATUS_COMPLETED      "COMPLETED"
#define ADMIN_PROBE_STATUS_CANCEL_REQUEST "CANCEL_REQUEST"
#define ADMIN_PROBE_STATUS_CANCELLED      "CANCELLED"
#define ADMIN_PROBE_STATUS_FAILED         "FAILED"
#define ADMIN_PROBE_STATUS_TIMED_OUT      "TIMED_OUT"

#define ADMIN_PROBE_VALID 1
#define ADMIN_PROBE_INVALID 0
#define ADMIN_PROBE_TRUE 1
#define ADMIN_PROBE_FALSE 0

/*ErrMessages*/
#define ADMIN_PROBE_ERR_IN_GENERAL "ADMIN_PROBE_GENERAL_FAILURE"
#define ADMIN_PROBE_FEATURE_NOT_ENABLED "ADMIN_PROBE_FEATURE_NOT_ENABLED"
#define ADMIN_PROBE_CAN_NOT_RESTART "ADMIN_PROBE_CAN_NOT_RESTART"
#define ADMIN_PROBE_FAILED_TO_UPLOAD_RESULT "ADMIN_PROBE_FAILED_TO_UPLOAD_RESULT"
#define ADMIN_PROBE_FAILED_TO_TRIGGER_UPLOAD "ADMIN_PROBE_FAILED_TO_TRIGGER_UPLOAD"
#define ADMIN_PROBE_UNKNOWN_BUCKET_OR_FILE "ADMIN_PROBE_UNKNOWN_BUCKET_FILE"
#define ADMIN_PROBE_BUFFER_SIZE_TOO_BIG "ADMIN_PROBE_BUFFER_SIZE_TOO_BIG"
#define ADMIN_PROBE_UPLOADER_INVALID_ARG "ADMIN_PROBE_UPLOADER_INVALID_ARG"
#define ADMIN_PROBE_UPLOAD_COMPLETE "ADMIN_PROBE_UPLOAD_COMPLETE"
#define ADMIN_PROBE_UPLOAD_RETRY_TIMEOUT "ADMIN_PROBE_UPLOAD_RETRY_TIMEOUT"
#define ADMIN_PROBE_DNS_SUCCEED_BUT_FAILED_ON_UPLOAD_ACTION "ADMIN_PROBE_DNS_SUCCEED_BUT_FAILED_ON_UPLOAD_ACTION"
#define ADMIN_PROBE_RECEIVED_CANCEL_REQUEST_FROM_USER "ADMIN_PROBE_RECEIVED_CANCEL_REQUEST_FROM_USER"
#define ADMIN_PROBE_CANCEL_RESTART_REQUEST_DUE_TO_ENTITY_JUST_GOT_RESTARTED "ADMIN_PROBE_CANCEL_RESTART_REQUEST_DUE_TO_ENTITY_JUST_GOT_RESTARTED"

#define ADMIN_PROBE_PCAP_SETUP_FAIL "ADMIN_PROBE_PCAP_SETUP_FAIL"
#define ADMIN_PROBE_CLIENT_NEXT_REQUEST_ERROR "ADMIN_PROBE_CLIENT_NEXT_REQUEST_ERROR"
#define ADMIN_PROBE_CLIENT_REQUEST_INVALID_DATA "ADMIN_PROBE_CLIENT_REQUEST_INVALID_DATA"


/*ErrMessages User Friendly*/
#define ADMIN_PROBE_COMMAND_FAIL_IN_GENERAL "Probe command failed to run"
#define ADMIN_PROBE_COMMAND_CANCELLED_AT_INIT "Probe command cancelled due to probe request init failure, please try again later"
#define ADMIN_PROBE_COMMAND_CANCELLED_DUE_TO_NO_DNS_RESULT "Probe command cancelled to run due to failed to get dns result for target, please make sure the target is valid"
#define ADMIN_PROBE_COMMAND_CANCELLED_DUE_TO_EXPIRY_GETTING_DNS_RESULT "Probe command cancelled to run due to dns request expired to get a result for target, please make sure there is a dns result for the dns request"
#define ADMIN_PROBE_COMMAND_CANCELLED_TASKS_QUEUE_FULL "Probe command cancelled due to total number of tasks in queue reached max limit, please try later"

#define ADMIN_PROBE_COMMAND_FAIL_NO_REPORT "Probe command results 100% loss, can not generate err report"
#define ADMIN_PROBE_COMMAND_SUCCESS_BUT_FAIL_TO_GEN_REPORT "Probe command results successful, but can not generate err report"
#define ADMIN_PROBE_COMMAND_ICMP6_NOT_SUPPORTED "Probe command cancelled due to ICMP target does not resolve to ipv4 ip, expecting ipv4 as icmp6 is not yet supported"

#define ADMIN_PROBE_COMMAND_FAIL_INVALID_INTERFACE "Probe command failed to run due to invalid interface, please make sure interface is valid"
#define ADMIN_PROBE_COMMAND_FAIL_INVALID_PORT "Probe command failed to run due to invalid port, please make sure port is valid"
#define ADMIN_PROBE_COMMAND_FAIL_INVALID_HOST "Probe command failed to run due to invalid hostname, please make sure hostname is valid"
#define ADMIN_PROBE_COMMAND_FAIL_TCPDUMP_EXEC "tcpdump command failed to execute, please try again later"

#define ADMIN_PROBE_COMMAND_UPLOADER_FAILED_FOHH_CLIENT_FAILURE "Probe command completed successfully but could not make connection to cloud to upload the result, please try again or contact support if continously seeing this issue"
#define ADMIN_PROBE_COMMAND_UPLOADER_FAILED_HTTP_ERR_RESPONSE "Probe command completed successfully but could not upload the result to cloud, please try again or contact support if continously seeing this issue"
#define ADMIN_PROBE_UPLOAD_REQUEST_TIMEOUT "Probe command completed succesfully but could not upload the result to cloud due to timeout, please try again later"
#define ADMIN_PROBE_CLIENT_REQUEST_ERROR "Probe command completed succesfully but could not upload the result to cloud due to bad data to write, please try again later"
#define ADMIN_PROBE_CLIENT_CREATE_ERROR "Probe command completed succesfully but could not upload result to cloud as it could not create client for http, please try again later"

/*DNS rcode*/
#define ADMIN_PROBE_DNS_RCODE_NoError "DNS_RESPONSE_RCODE_NoError"
#define ADMIN_PROBE_DNS_RCODE_FormErr "DNS_RESPONSE_RCODE_FormErr"
#define ADMIN_PROBE_DNS_RCODE_ServFail "DNS_RESPONSE_RCODE_ServFail"
#define ADMIN_PROBE_DNS_RCODE_NXDomain "DNS_RESPONSE_RCODE_NXDomain"
#define ADMIN_PROBE_DNS_RCODE_NotImp "DNS_RESPONSE_RCODE_NotImp"
#define ADMIN_PROBE_DNS_RCODE_Refused "DNS_RESPONSE_RCODE_Refused"
#define ADMIN_PROBE_DNS_RCODE_YXDomain "DNS_RESPONSE_RCODE_YXDomain"
#define ADMIN_PROBE_DNS_RCODE_YXRRSet "DNS_RESPONSE_RCODE_YXRRSet"
#define ADMIN_PROBE_DNS_RCODE_NXRRSet "DNS_RESPONSE_RCODE_NXRRSet"
#define ADMIN_PROBE_DNS_RCODE_NotAuth "DNS_RESPONSE_RCODE_NotAuth"
#define ADMIN_PROBE_DNS_RCODE_NotZone "DNS_RESPONSE_RCODE_NotZone"
#define ADMIN_PROBE_DNS_RCODE_DSOTYPENI "DNS_RESPONSE_RCODE_DSOTYPENI"
#define ADMIN_PROBE_DNS_RCODE_BADVERS_or_BADSIG "DNS_RESPONSE_RCODE_BADVERS_or_BADSIG"
#define ADMIN_PROBE_DNS_RCODE_BADKEY "DNS_RESPONSE_RCODE_BADKEY"
#define ADMIN_PROBE_DNS_RCODE_BADTIME "DNS_RESPONSE_RCODE_BADTIME"
#define ADMIN_PROBE_DNS_RCODE_BADMODE "DNS_RESPONSE_RCODE_BADMODE"
#define ADMIN_PROBE_DNS_RCODE_BADNAME "DNS_RESPONSE_RCODE_BADNAME"
#define ADMIN_PROBE_DNS_RCODE_BADALG "DNS_RESPONSE_RCODE_BADALG"
#define ADMIN_PROBE_DNS_RCODE_BADTRUNC "DNS_RESPONSE_RCODE_BADTRUNC"
#define ADMIN_PROBE_DNS_RCODE_BADCOOKIE "DNS_RESPONSE_RCODE_BADCOOKIE"
#define ADMIN_PROBE_Unknown_DNS_rcode "DNS_RESPONSE_RCODE_Unknown_DNS_rcode"


/*DNS failure message*/
#define ADMIN_PROBE_DNS_ERR_MSG_RCODE_FormErr  "DNS query failed with rcode FormErr"
#define ADMIN_PROBE_DNS_ERR_MSG_RCODE_ServFail "DNS query failed with rcode ServFail"
#define ADMIN_PROBE_DNS_ERR_MSG_RCODE_NXDomain "DNS query failed with rcode NXDomain"
#define ADMIN_PROBE_DNS_ERR_MSG_RCODE_NotImp   "DNS query failed with rcode NotImp"
#define ADMIN_PROBE_DNS_ERR_MSG_RCODE_Refused   "DNS query failed with rcode Refused"
#define ADMIN_PROBE_DNS_ERR_MSG_RCODE_YXDomain   "DNS query failed with rcode YXDomain"
#define ADMIN_PROBE_DNS_ERR_MSG_RCODE_YXRRSet   "DNS query failed with rcode YXRRSet"
#define ADMIN_PROBE_DNS_ERR_MSG_RCODE_NXRRSet   "DNS query failed with rcode NXRRSet"
#define ADMIN_PROBE_DNS_ERR_MSG_RCODE_NotAuth   "DNS query failed with rcode NotAuth"
#define ADMIN_PROBE_DNS_ERR_MSG_RCODE_NotZone   "DNS query failed with rcode NotZone"
#define ADMIN_PROBE_DNS_ERR_MSG_RCODE_DSOTYPENI   "DNS query failed with rcode DSOTYPENIs"
#define ADMIN_PROBE_DNS_ERR_MSG_RCODE_BADVERS_or_BADSIG   "DNS query failed with rcode BADVERS_or_BADSIG"
#define ADMIN_PROBE_DNS_ERR_MSG_RCODE_BADKEY   "DNS query failed with rcode BADKEY"
#define ADMIN_PROBE_DNS_ERR_MSG_RCODE_BADTIME   "DNS query failed with rcode BADTIME"
#define ADMIN_PROBE_DNS_ERR_MSG_RCODE_BADMODE   "DNS query failed with rcode BADMODE"
#define ADMIN_PROBE_DNS_ERR_MSG_RCODE_BADNAME   "DNS query failed with rcode BADNAME"
#define ADMIN_PROBE_DNS_ERR_MSG_RCODE_BADALG   "DNS query failed with rcode BADALG"
#define ADMIN_PROBE_DNS_ERR_MSG_RCODE_BADTRUNC   "DNS query failed with rcode BADTRUNC"
#define ADMIN_PROBE_DNS_ERR_MSG_RCODE_BADCOOKIE   "DNS query failed with rcode BADCOOKIE"
#define ADMIN_PROBE_DNS_ERR_MSG   "DNS query failed with no result"


extern uint64_t admin_probe_debug_log;
extern const char *admin_probe_debug_log_names[];

#define ADMIN_PROBE_DEBUG_LOG_NAMES {              \
        "probe",                                   \
        "table",                                   \
        "task_module",                             \
        "upload_module",                           \
        "task_module_dns",                         \
        "task_module_icmp",                        \
        "task_module_tcp",                         \
        "task_module_parse",                       \
        "task_module_tcpdump",                       \
        NULL                                       \
}

#define ADMIN_PROBE_LOG(priority, format...) if (1) ARGO_LOG(admin_probe_event_collection, priority, "admin_probe", ##format)
#define ADMIN_PROBE_DEBUG_LOG(condition, format...) if (1) ARGO_DEBUG_LOG(condition, admin_probe_event_collection, argo_log_priority_debug, "admin_probe", ##format)
#define ADMIN_PROBE_DEBUG_PROBE(format...) ADMIN_PROBE_DEBUG_LOG(admin_probe_debug_log & ADMIN_PROBE_DEBUG_PROBE_BIT, ##format)
#define ADMIN_PROBE_DEBUG_TABLE(format...) ADMIN_PROBE_DEBUG_LOG(admin_probe_debug_log & ADMIN_PROBE_DEBUG_TABLE_BIT, ##format)
#define ADMIN_PROBE_DEBUG_TASK_MODULE(format...) ADMIN_PROBE_DEBUG_LOG(admin_probe_debug_log & ADMIN_PROBE_DEBUG_TASK_MODULE_BIT, ##format)
#define ADMIN_PROBE_DEBUG_UPLOADER(format...) ADMIN_PROBE_DEBUG_LOG(admin_probe_debug_log & ADMIN_PROBE_DEBUG_UPLOADER_BIT, ##format)
#define ADMIN_PROBE_DEBUG_TASK_MODULE_DNS(format...) ADMIN_PROBE_DEBUG_LOG(admin_probe_debug_log & ADMIN_PROBE_DEBUG_TASK_MODULE_DNS_BIT, ##format)
#define ADMIN_PROBE_DEBUG_TASK_MODULE_ICMP(format...) ADMIN_PROBE_DEBUG_LOG(admin_probe_debug_log & ADMIN_PROBE_DEBUG_TASK_MODULE_ICMP_BIT, ##format)
#define ADMIN_PROBE_DEBUG_TASK_MODULE_TCP(format...) ADMIN_PROBE_DEBUG_LOG(admin_probe_debug_log & ADMIN_PROBE_DEBUG_TASK_MODULE_TCP_BIT, ##format)
#define ADMIN_PROBE_DEBUG_PARSE(format...) ADMIN_PROBE_DEBUG_LOG(admin_probe_debug_log & ADMIN_PROBE_DEBUG_PARSE_BIT, ##format)
#define ADMIN_PROBE_DEBUG_TASK_MODULE_TCPDUMP(format...) ADMIN_PROBE_DEBUG_LOG(admin_probe_debug_log & ADMIN_PROBE_DEBUG_TASK_MODULE_TCPDUMP_BIT, ##format)
#define ADMIN_PROBE_DEBUG_TASK_MODULE_FRR_CMDS(format...) ADMIN_PROBE_DEBUG_LOG(admin_probe_debug_log & ADMIN_PROBE_DEBUG_TASK_MODULE_TCPDUMP_BIT, ##format)

/*
 * Soft assert will crash in dev & QA environment. But just log a message in production environment.
 *
 * Q: When should you use it?
 * A: When you see an error condition that you think is not disrupting the production, but you want to cry out loud
 * in DEV/QA environment so that the bug is reported.
 */
#define ADMIN_PROBE_ASSERT_SOFT(condition, format...)   \
    ZPATH_ASSERT_SOFT(condition, "admin_probe", admin_probe_state.is_dev_env, "Admin probe", ##format)

/*
 * Hard assert will crash in all environment. Be extra extra careful when using this as we are talking about crashing
 * a production system.
 *
 * Q: When should you use it?
 * A: When you see an error condition that you think is NOT RECOVERABLE.
 */
#define ADMIN_PROBE_ASSERT_HARD(condition, format...)   \
    ZPATH_ASSERT_HARD(condition, "admin_probe", admin_probe_state.is_dev_env, "Admin probe", ##format)


int admin_probe_lib_init(struct argo_log_collection *event_log);;

#endif // _ADMIN_PROBE_LIB_H_
