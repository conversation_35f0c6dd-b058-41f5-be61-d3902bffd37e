/*
 * admin_probe_rpc_send.c. Copyright (C) 2021 Zscaler Inc, All Rights Reserved
 *
 */

#include "admin_probe/admin_probe_rpc_send.h"
#include "admin_probe/admin_probe_lib.h"
#include "admin_probe/admin_probe_private.h"
#include "zpath_lib/zpath_debug.h"
#include "argo/argo.h"


static
struct admin_probe_rpc_send_stats {                                                                   /* _ARGO: * object_definition */
    uint64_t number_of_failure_status_report_sent_because_of_invalid_arguments_command_uuid;          /* _ARGO: integer */
    uint64_t number_of_failure_status_report_sent_because_of_invalid_arguments_action;                /* _ARGO: integer */
    uint64_t number_of_failure_status_report_sent_because_of_invalid_arguments_status;                /* _ARGO: integer */
    uint64_t number_of_failure_status_report_sent;                                                    /* _ARGO: integer */
    uint64_t number_of_success_status_report_sent;                                                    /* _ARGO: integer */
    uint64_t number_of_status_report_allocated;                                                       /* _ARGO: integer */
    uint64_t number_of_status_report_freed;                                                           /* _ARGO: integer */
} stats;
#include "admin_probe/admin_probe_rpc_send_compiled_c.h"
static struct argo_structure_description*  admin_probe_rpc_send_stats_description;

void admin_probe_rpc_resend(struct zpn_command_probe_status *report);


static void admin_probe_rpc_send_free_status_report(struct zpn_command_probe_status *report)
{
    if (!report) {
        return;
    }

    if (report->command_uuid) ADMIN_PROBE_FREE(report->command_uuid);
    if (report->action) ADMIN_PROBE_FREE(report->action);
    if (report->content_json) ADMIN_PROBE_FREE(report->content_json);
    if (report->content_txt) ADMIN_PROBE_FREE(report->content_txt);
    if (report) ADMIN_PROBE_FREE(report);

    stats.number_of_status_report_freed++;
}

int admin_probe_rpc_send_tx_command_probe_status_report(char *command_uuid,
                                                        char *action,
                                                        int64_t entity_gid,
                                                        int64_t customer_gid,
                                                        const char *status,
                                                        int64_t start_time,
                                                        int64_t end_time,
                                                        char *content_json,
                                                        char *content_txt,
                                                        const char *error_message,
                                                        int is_np_command_probe)
{

    struct zpn_command_probe_status *status_report;
    int res = ADMIN_PROBE_RESULT_ERR;

    if (!command_uuid) {
        ADMIN_PROBE_LOG(AL_ERROR, "Invalid Argument on command_uuid, can not proceed to send status report");
        stats.number_of_failure_status_report_sent_because_of_invalid_arguments_command_uuid++;
        return res;
    }

    if (!action) {
        ADMIN_PROBE_LOG(AL_ERROR, "Invalid Argument on action, can not proceed to send status report");
        stats.number_of_failure_status_report_sent_because_of_invalid_arguments_action++;
        return res;
    }

    if (!status) {
        ADMIN_PROBE_LOG(AL_ERROR, "Invalid Argument on status, can not proceed to send status report");
        stats.number_of_failure_status_report_sent_because_of_invalid_arguments_status++;
        return res;
    }

    status_report = ADMIN_PROBE_CALLOC(sizeof(*status_report));
    if (!status_report) {
        ADMIN_PROBE_LOG(AL_ERROR, "admin probe status report failed - no memory");
        return res;
    }

    stats.number_of_status_report_allocated++;

    status_report->command_uuid = ADMIN_PROBE_STRDUP(command_uuid, strlen(command_uuid));
    status_report->action = ADMIN_PROBE_STRDUP(action, strlen(action));

    status_report->entity_gid = entity_gid;
    status_report->customer_gid = customer_gid;
    status_report->status = status;

    if (error_message) status_report->error_message = error_message;

    if (start_time) {
        status_report->start_time = start_time;
    }

    if (end_time) {
        status_report->end_time = end_time;
    } else if ((strcmp(status, ADMIN_PROBE_STATUS_PROCESSING)) && (strcmp(status, ADMIN_PROBE_STATUS_PENDING))) {
        ADMIN_PROBE_LOG(AL_ERROR, "Admin probe modules did not send endtime %s %s", command_uuid, action);
        /* Send the end time in any case as they result in sessions disappearing from UI */
        status_report->end_time = admin_probe_callbacks.get_current_cloud_epoch_s_cb();
    }

    if (content_json) {
        status_report->content_json = ADMIN_PROBE_STRDUP(content_json, strlen(content_json));
    }

    if (content_txt) {
        status_report->content_txt = ADMIN_PROBE_STRDUP(content_txt, strlen(content_txt));
    }

    if (!admin_probe_callbacks.status_update_cb) {
        ADMIN_PROBE_ASSERT_SOFT(0, "no status_update_cb, status_update_cb is required for initializing admin_probe library");
        goto done;
    }

    res = admin_probe_callbacks.status_update_cb(status_report, is_np_command_probe);
    if (res) {
        stats.number_of_failure_status_report_sent++;
        goto done;
    } else {
        stats.number_of_success_status_report_sent++;
        goto done;
    }

done:
    admin_probe_rpc_send_free_status_report(status_report);
    return res;

}


static int
admin_probe_rpc_send_dump_stats(struct zpath_debug_state*  request_state,
                                  const char**               query_values,
                                  int                        query_value_count,
                                  void*                      cookie)
{
    char jsonout[4000];

    (void)query_values;
    (void)query_value_count;
    (void)cookie;

    if (ARGO_RESULT_NO_ERROR == argo_structure_dump(admin_probe_rpc_send_stats_description,
                                                    &stats, jsonout, sizeof(jsonout), NULL, 1)) {
        ZDP("%s\n", jsonout);
    }

    return ADMIN_PROBE_RESULT_NO_ERROR;
}

static int admin_probe_rpc_send_debug_init()
{
    int res;

    if (!(admin_probe_rpc_send_stats_description = argo_register_global_structure(ADMIN_PROBE_RPC_SEND_STATS_HELPER))) {
        return ADMIN_PROBE_RESULT_ERR;
    }

    res = zpath_debug_add_read_command("dump the stats of admin_probe_rpc_send",
                                  "/admin_probe/rpc_send_stats",
                                  admin_probe_rpc_send_dump_stats,
                                  NULL,
                                  NULL);
    if (res){
        return ADMIN_PROBE_RESULT_ERR;
    }

    return ADMIN_PROBE_RESULT_NO_ERROR;
}


int admin_probe_rpc_send_init()
{
    int res;

    res = admin_probe_rpc_send_debug_init();
    if (res) {
        ADMIN_PROBE_LOG(AL_ERROR, "Could not init admin_probe_rpc_send_debug_init");
        return ADMIN_PROBE_RESULT_ERR;
    }

    return ADMIN_PROBE_RESULT_NO_ERROR;
}
