/*
 * admin_probe_task_module.h. Copyright (C) 2021 Zscaler Inc, All Rights Reserved
 *
 */

#ifndef _ADMIN_PROBE_TASK_MODULE_H_
#define _ADMIN_PROBE_TASK_MODULE_H_

#include "admin_probe/zpn_command_probe.h"
#include "admin_probe/np_command_probe.h"
#include <event2/event.h>


int admin_probe_task_module_init(struct event_base *base);

void admin_probe_task_module_command_probe_entry_update_from_fohh_thread(struct argo_object* row, int is_np_command_probe);

int64_t admin_probe_get_total_number_of_task();
int64_t admin_probe_get_total_number_of_task_completed();
int64_t admin_probe_get_total_number_of_task_failed();
int64_t admin_probe_get_total_number_of_task_cancelled();
int64_t admin_probe_get_total_number_of_task_timeout();

int64_t admin_probe_get_total_number_restart_complete();
int64_t admin_probe_get_total_number_of_restart_fail();
int64_t admin_probe_get_total_number_of_restart_cancel();


#endif // _ADMIN_PROBE_TASK_MODULE_H_
