/*
 * admin_probe_task_module_private.h. Copyright (C) 2021 Zscaler Inc, All Rights Reserved
 *
 */

#ifndef _ADMIN_PROBE_TASK_MODULE_PRIVATE_H_
#define _ADMIN_PROBE_TASK_MODULE_PRIVATE_H_

#include "zhash/zhash_table.h"
#include "admin_probe/zpn_command_probe.h"
#include "admin_probe/np_command_probe.h"
#include "admin_probe/admin_probe_private.h"
#include "zhealth/zhealth_probe_lib.h"
#include "argo/argo.h"
#include "zhealth/zhealth_combo.h"

struct admin_probe_task;

typedef int (admin_probe_task_module_callback_f)(struct admin_probe_task *cur_task);
typedef void (admin_probe_task_module_free_f)(struct admin_probe_task *cur_task);

struct admin_probe_task_module_callouts {

    admin_probe_task_module_callback_f *init_f;

    admin_probe_task_module_callback_f *process_f;

    admin_probe_task_module_callback_f *timeout_in_processing_f;

    admin_probe_task_module_free_f *free_f;

    admin_probe_task_module_callback_f *cancel_f;
};



/*
 * task initial state: task_pending or task_cancelled_sent

 * task_pending - if there is a new task come and we say ok to take the task
 * task_cancelled_sent - if there is a task come but we do not like it (ie: creation_time too old), we will cancel the request
 *
 *
 * when will task be in task_cancelled_sent/task_failed_sent/task_timeout_sent/task_complete_sent
 * - when from our point of view, we have 'finish' the task, but db still views the task not finished yet.
 * ie: when we complete a dns request, we will send 'COMPLETE' status update on the command and move the task to task_complete_sent state.
 *     once Kafaka receives the 'COMPLETE' status update and update the db, this 'COMPLETE' update will come back to use again.
 *     and this time, we know that api/ui knows that the task is complete, we can safely move the task to reaping state.
 *
 * when will task be in task_reaping state.
 * - when db says task is in termination state and we also have our internal state says terminiated.
 * - when db says task is in termination state, even though we have terminated the task yet, but we will force to terminate to be in sync with db.
 *
 * expected transition:
 *
 * task_pending -> task_processing_sent : happens when we execute the task from pending state
 * task_pending -> task_reaping : happens when rx cancel_request status update on this task
 *
 * task_processing_sent -> task_uploading : happens when task execution done succussfully and the result needs to go to s3.
 * task_processing_sent -> task_complete_sent : appens when task execution done succussfully and the result goes to kafka through status report complete.
 * task_processing_sent -> task_failed_sent : happens when task execution failed
 * task_processing_sent -> task_timeout_sent : happens when we have a timeout timer for this task, and the timerout timer triggered
 * task_processing_sent -> task_reaping : happens when rx cancel_request status update on this and we are able to cancel the task that is in execution
 *
 * task_uploading -> task_complete_sent : happens when task result uploaded successfully
 * task_uploading -> task_failed_sent : happens when task result failed to upload to s3.
 * task_uploading -> task_reaping : happens when rx cancel_request status update on this task
 *
 * task_cancelled_sent -> task_reaping : happens when we cancel a task and we receive the corresponding cancel db callback
 * task_failed_sent -> task_reaping : happens when we send fail status on a task and we receive the corresponding failed status from db callback
 * task_timeout_sent -> task_reaping : happens when we send timeout status on a task and we receive the corresponding timeout status from db callback
 * task_complete_sent -> task_reaping : happens when we send complete status on a task and we receive the corresponding complete status from db callback
 *
 * task_reaping -> task_free : when there is no more callbacks pending to trigger on this task, move to free.
 *
 */
enum admin_probe_task_state {
    task_not_exist, //invalid state.
    task_pending,
    task_processing_sent,
    task_uploading,
    task_cancelled_sent,
    task_failed_sent,
    task_timeout_sent,
    task_complete_sent,
    task_reaping,
    task_free,
    max_task_state,
};


enum admin_probe_task_event {
    invalid_event,
    pending_from_wally,
    processing_from_wally,
    completed_from_wally,
    failed_from_wally,
    timed_out_from_wally,
    cancelled_from_wally,
    cancel_request_from_wally,
    internal_timeout_timer,
    internal_task_execute_done_success,
    internal_task_execute_done_failed,
    internal_task_upload_success,
    internal_task_upload_failed,
    internal_err,
    internal_check_task,
    internal_task_execute_done_but_pending_upload,
    max_task_events,
};

enum dns_task_type {
    dns_unknown_type,
    dns_a,
    dns_aaaa,
    dns_srv,
};

struct dns_task {
    enum dns_task_type dns_type;
    struct zcdns_request *dns_req; //no need to free this one, zcdns lib will take care
    struct zcdns_result *results; //once we used the result, we have to release this memory
    int rcode;
};

struct icmp_task {
    char* request_domain;

    struct zhealth_icmp_info **icmpinfo_arr;
    int icmpinfo_arr_count;
    int icmp_probe_sent;
    int64_t icmp_probes_sent_time_s;

    int icmp_cb_done;
    int icmp_cb_done_failure;
    int icmp_cb_done_response;


    struct {
        int64_t max_rtt_us;
        int64_t min_rtt_us;
        double avg_rtt_us;
        int64_t total_rx_rtt_us;
        int probes_transmitted;
        int probes_received;
        int probes_loss_percent;
        int packet_loss;

    } statistics;

    struct {
        /*cancel the task*/
        unsigned is_dns_failed:1;
        unsigned is_exec_failed:1;
    } state;
};

struct tcp_task {
    char* request_domain;
    int request_port;
    struct zhealth_combo* tcp_check;
    int64_t tcp_probes_start_time_s;

    int tcp_cb_done;


    struct {
        int64_t max_rtt_us;
        int64_t min_rtt_us;
        double avg_rtt_us;
        int64_t total_rx_rtt_us;
        int probes_transmitted;
        int probes_received;
        int probes_loss_percent;
        int packet_loss;

    } statistics;


    struct {
        unsigned is_ip_given_in_request:1;
        unsigned is_exec_failed:1;
        unsigned is_health_done;
    } state;
};

struct task_common {
    struct zcdns_result *dns_results;
    int is_ipv4;
    int is_ipv6;
    int is_ipv4_addr_returned;
    int is_ipv6_addr_returned;
    struct argo_inet ip_inet;
    char dst_ip[INET6_ADDRSTRLEN];

    struct {
        unsigned is_dns_failed:1;
        unsigned is_dns_request_expired:1;
        unsigned is_ip_info_ready:1;
    } state;

};

ZTAILQ_HEAD(admin_probe_task_head, admin_probe_task);

struct admin_probe_task {
    enum admin_probe_task_type type;
    enum admin_probe_task_state cur_state;

    /*need free when cleaning*/
    char *command_uuid;
    char *action;
    char *target; //can be null
    char *file_location;
    char *bucket_name;
    char *additional_arguments; //can be null
    char* result_txt;
    char* result_json;
    char* tcpdump_interface;
    int tcpdump_port;

    union {
        struct dns_task dns_task_state;
        struct icmp_task icmp_task_state;
        struct tcp_task tcp_task_state;

    } task_state;

    struct task_common common_state;

    int64_t customer_gid;
    int64_t entity_gid;

    int64_t creation_time;
    int64_t start_time_cloud_s;
    int64_t end_time_cloud_s;
    int64_t duration_in_secs;
    int64_t task_creation_local_time_s;
    int64_t first_termination_sent_local_s;
    int64_t last_termination_sent_local_s;
    int64_t next_termination_status_resend_time_interval_s;
    int64_t termination_status_resend_expiry_s;

    unsigned need_send_termination_status;
    unsigned is_first_termination_status_report_sent;

    const char* err_message;

    int number_of_retries_executing;

    struct admin_probe_task_module_callouts *callouts;

    /*
     * cleanup_criteria is for moving task state from reaping queue to free queue.
     * once confirm that no callbacks are waiting to be triggered for this task, safely move it to free.
     */
    struct {
        unsigned is_execute_callback_outstanding:1;
        unsigned is_expired_callback_outstanding:1;
        unsigned is_uploader_callback_outstanding:1;
    } cleanup_criteria;

    unsigned int is_ut:1;
    unsigned int is_np_command_probe:1;


    ZTAILQ_ENTRY(admin_probe_task) task_list_entry;

};

void admin_probe_task_module_probe_process_event(void* command_probe,
                                                 int is_np_command_probe,
                                                 struct admin_probe_task* cur_task,
                                                 char* command_uuid,
                                                 enum admin_probe_task_event event_type);

#endif // _ADMIN_PROBE_TASK_MODULE_PRIVATE_H_
