/*
 * admin_probe_rpc_send.h. Copyright (C) 2021 Zscaler Inc, All Rights Reserved
 *
 */

#ifndef _ADMIN_PROBE_RPC_SEND_H_
#define _ADMIN_PROBE_RPC_SEND_H_

#include "admin_probe/admin_probe_rpc.h"

int admin_probe_rpc_send_init();


/*
 * this api will construct a status report in memory and send it to kafka through status_update_cb
 * the report will get resent until succeed.
 * Once the report gets sent successfully, the memory will get freed.
 *
 * Required argument:
 *  command_uuid, customer_gid, action, status
 *
 * Optional
 *  errorMessage, start_time and endtime.
 *
 * we make sure that we send the report out there (will keep resending the report if it fails)
 * this call will be called on both fohh_thread or admin_probe thread
 *
 */
int admin_probe_rpc_send_tx_command_probe_status_report(char *command_uuid,
                                                        char *action,
                                                        int64_t entity_gid,
                                                        int64_t customer_gid,
                                                        const char *status,
                                                        int64_t start_time,
                                                        int64_t end_time,
                                                        char *content_json,
                                                        char *content_txt,
                                                        const char *error_message,
                                                        int is_np_command_probe);

#endif // _ADMIN_PROBE_RPC_SEND_H_
