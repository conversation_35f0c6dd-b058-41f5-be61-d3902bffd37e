/*
 * np_command_probe.c. Copyright (C) 2025 Zscaler Inc. All Rights Reserved.
 */

#include "admin_probe/np_command_probe.h"
#include "admin_probe/np_command_probe_compiled.h"
#include "admin_probe/admin_probe_lib.h"
#include "admin_probe/admin_probe_task_module.h"
#include "admin_probe/admin_probe_private.h"

#include "zpath_lib/zpath_app.h"
#include "zpath_lib/zpath_cloud.h"

struct argo_structure_description *np_command_probe_description = NULL;
static struct wally_index_column **np_command_probe_command_customer_gid_column = NULL;
static struct wally_index_column **np_command_probe_command_entity_gid_column = NULL;

static struct {
    struct wally_table *table;
} state;

static int
np_command_probe_row_callback(void*                       cookie,
                               struct wally_registrant*    registrant,
                               struct wally_table*         table,
                               struct argo_object*         previous_row,
                               struct argo_object*         row,
                               int64_t                     request_id)
{


    if (admin_probe_debug_log & ADMIN_PROBE_DEBUG_TABLE_BIT) {
        char dump[8000];
        if (argo_object_dump(row, dump, sizeof(dump), NULL, 0) == ARGO_RESULT_NO_ERROR) {
            ADMIN_PROBE_LOG(AL_NOTICE, "Row callback: %s", dump);
        }
    }

    admin_probe_task_module_command_probe_entry_update_from_fohh_thread(row, ADMIN_PROBE_COMMAND_TYPE_NP_PROBE);

    return ADMIN_PROBE_RESULT_NO_ERROR;
}

void argo_np_command_probe_init()
{
    if(!argo_register_global_structure(NP_COMMAND_PROBE_HELPER))
        ADMIN_PROBE_LOG(AL_ERROR, "Could not register argo_np_command_probe_init argo object");
}

int np_command_probe_init(struct wally *wally, int64_t entity_gid, int fully_load, int register_with_zpath_table)
{
    int res;
    int64_t customer_gid;

    np_command_probe_description = argo_register_global_structure(NP_COMMAND_PROBE_HELPER);
    if (!np_command_probe_description) {
        return ADMIN_PROBE_RESULT_ERR;
    }

    customer_gid = ZPATH_GID_GET_CUSTOMER_GID(entity_gid);

    if (wally) {
        struct wally_table *table;
        int shard_index = ZPATH_SHARD_FROM_GID(entity_gid);

        np_command_probe_command_customer_gid_column = ADMIN_PROBE_CALLOC(ZPATH_MAX_SHARDS * sizeof(*np_command_probe_command_customer_gid_column));
        np_command_probe_command_entity_gid_column = ADMIN_PROBE_CALLOC(ZPATH_MAX_SHARDS * sizeof(*np_command_probe_command_entity_gid_column));

        if (fully_load) {
            res = zpath_app_fully_loaded_customer_table(&table,
                                                        customer_gid,
                                                        wally,
                                                        np_command_probe_description,
                                                        np_command_probe_row_callback,
                                                        NULL,
                                                        NULL,
                                                        register_with_zpath_table);
            if (res) {
                ADMIN_PROBE_LOG(AL_ERROR, "Could not fully load np_command_probe table on single tenant wally");
                return ADMIN_PROBE_RESULT_ERR;
            }
        } else {
           /*
            * we dont need multiple index consistency for now,
            * as we are only registering for one index, (entity_gid).
            *
            * if in the future, we are also registering on other index in this table, we have to turn this on.
            */
            table = wally_table_create(wally,
                                       0, //not writable
                                       np_command_probe_description,
                                       np_command_probe_row_callback,
                                       NULL,
                                       1, //use_all_origins
                                       0, //multiple_index_consistency
                                       NULL);

            if (!table) {
                ADMIN_PROBE_LOG(AL_ERROR, "Could not get np_command_probe table on single tenant wally");
                return ADMIN_PROBE_RESULT_ERR;
            }
        }

        state.table = table;

        np_command_probe_command_customer_gid_column[shard_index] = wally_table_get_index(table, "customer_gid");
        if (!np_command_probe_command_customer_gid_column[shard_index]) {
            ADMIN_PROBE_LOG(AL_ERROR, "Could not get customer_gid column from np_command_probe table");
            return ADMIN_PROBE_RESULT_ERR;
        }

        np_command_probe_command_entity_gid_column[shard_index] = wally_table_get_index(table, "entity_gid");
        if (!np_command_probe_command_entity_gid_column[shard_index]) {
            ADMIN_PROBE_LOG(AL_ERROR, "Could not get entity_gid column from np_command_probe table");
            return ADMIN_PROBE_RESULT_ERR;
        }

    } else {

        res = zpath_app_add_np_sharded_table(np_command_probe_description,
                                          np_command_probe_row_callback,
                                          NULL,
                                          0,
                                          NULL);
        if (res) {
            return res;
        }

        np_command_probe_command_customer_gid_column = zpath_app_get_np_sharded_index("np_command_probe", "customer_gid");
        if (!np_command_probe_command_customer_gid_column) {
            ADMIN_PROBE_LOG(AL_ERROR, "Could not get customer_gid column from np_command_probe table");
            return ADMIN_PROBE_RESULT_ERR;
        }

        np_command_probe_command_entity_gid_column = zpath_app_get_np_sharded_index("np_command_probe", "entity_gid");
        if (!np_command_probe_command_entity_gid_column) {
            ADMIN_PROBE_LOG(AL_ERROR, "Could not get entity_gid column from np_command_probe table");
            return ADMIN_PROBE_RESULT_ERR;
        }

    }

    return ADMIN_PROBE_RESULT_NO_ERROR;

}

int np_command_probe_register_by_entity_gid_using_default_registrant(int64_t                       customer_gid,
                                                                      int64_t                       entity_gid,
                                                                      wally_response_callback_f     *callback,
                                                                      void                          *void_cookie)
{
    int res;
    int shard_index;

    shard_index = ZPATH_SHARD_FROM_GID(customer_gid);

    if (!np_command_probe_command_entity_gid_column[shard_index]) {
        ADMIN_PROBE_LOG(AL_ERROR, "No entity_gid column initialized, should not use this method for creating registrant for np_command_probe");
        return ADMIN_PROBE_RESULT_ERR;
    }

    res = wally_table_register_for_row(NULL,
                                       np_command_probe_command_entity_gid_column[shard_index],
                                       &entity_gid,
                                       sizeof(entity_gid),
                                       0,
                                       0,
                                       0,
                                       0,
                                       0,
                                       callback,
                                       void_cookie);
    if (res && res != WALLY_RESULT_ASYNCHRONOUS) {
        ADMIN_PROBE_LOG(AL_ERROR, "np_command_probe register for row failed");
    }

    return ADMIN_PROBE_RESULT_NO_ERROR;

}


int np_command_probe_get_by_entity_gid(int64_t entity_gid,
                                        struct np_command_probe **links,
                                        size_t *res_count,
                                        wally_response_callback_f callback_f,
                                        void *callback_cookie,
                                        int64_t callback_id)
{
    int res;
    int shard_index = ZPATH_SHARD_FROM_GID(entity_gid);

    res = wally_table_get_rows_fast(np_command_probe_command_entity_gid_column[shard_index],
                                    &entity_gid,
                                    sizeof(entity_gid),
                                    (void **) links,
                                    res_count,
                                    1,   /* Register on miss! Next time we should have rule... */
                                    callback_f,
                                    callback_cookie,
                                    callback_id);

    return res;
}
