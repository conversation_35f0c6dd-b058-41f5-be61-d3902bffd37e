/*
 * admin_probe_task_module_tcpdump.c. Copyright (C) 2021 Zscaler Inc, All Rights Reserved
 *
 */


#include <sys/stat.h>
#include <sys/types.h>
#include <dirent.h>
#include "argo/argo.h"
#include "zhash/zhash_table.h"
#include "zpath_lib/zpath_debug.h"
#include "zpn_pcap/zpn_pcap.h"
#include "parson/parson.h"
#include "admin_probe/admin_probe_task_module_tcpdump.h"
#include "admin_probe/admin_probe_private.h"
#include "admin_probe/admin_probe_uploader.h"
#include "admin_probe/admin_probe_rate_limiting.h"
#include "admin_probe/admin_probe_task_module_common.h"

static struct {
    int64_t total_tcpdump_in_exec;
} tcpdump_state;

static struct admin_probe_task_module_tcpdump_stats{                        /* _ARGO: * object_definition */
    int64_t failed_to_exec_tcpdump_because_of_threshold_reached;            /* _ARGO: integer */
    int64_t create_directory_fail;                                          /* _ARGO: integer */
    int64_t open_directory_fail_init;                                       /* _ARGO: integer */
    int64_t invalid_data_read_file;                                         /* _ARGO: integer */
    int64_t num_tasks_expire_in_processing;                                 /* _ARGO: integer */
    int64_t invalid_cookie_pcap_callback;                                   /* _ARGO: integer */
    int64_t invalid_task_struct_pcap_callback;                              /* _ARGO: integer */
    int64_t invalid_cookie_s3_output_callback;                              /* _ARGO: integer */
    int64_t invalid_task_struct_s3_output_callback;                         /* _ARGO: integer */
    int64_t pcap_end_callback_error;                                        /* _ARGO: integer */
    int64_t pcap_setup_error;                                               /* _ARGO: integer */
    int64_t upload_setup_fail;                                              /* _ARGO: integer */
    int64_t no_memory_pcap_resp;                                            /* _ARGO: integer */
    int64_t tcpdump_total_success;                                          /* _ARGO: integer */
    int64_t tcpdump_total_failure;                                          /* _ARGO: integer */
    int64_t tcpdump_s3_output_failure;                                      /* _ARGO: integer */
    int64_t pcap_filter_setup_fail;                                         /* _ARGO: integer */
    int64_t tcpdump_total_cancel;                                           /* _ARGO: integer */
} tcpdump_stats;
#include "admin_probe/admin_probe_task_module_tcpdump_compiled_c.h"
static struct argo_structure_description*  admin_probe_task_module_tcpdump_stats_description;


int admin_probe_task_module_tcpdump_init() {
    int status;
    char buf[ADMIN_PROBE_UPLOADER_MAX_FILE_NAME];
    struct dirent *dir;

    admin_probe_task_module_tcpdump_debug_init();

    status = mkdir(ADMIN_PROBE_TCPDUMP_FILE_LOCATION, ADMIN_PROBE_TCPDUMP_DIR_MODE);
    if (status == -1) {
        if (errno != EEXIST) {
            tcpdump_stats.create_directory_fail++;
            ADMIN_PROBE_LOG(AL_ERROR, "Could not create sub directory for pcap %d", errno);
            return status;
        } else {
            /*Cleanup all old files after a restart*/
            DIR *dirp = opendir(ADMIN_PROBE_TCPDUMP_FILE_LOCATION);
            if (dirp == NULL) {
                ADMIN_PROBE_LOG(AL_ERROR, "Open directory failed during cleanup");
                tcpdump_stats.open_directory_fail_init++;
                goto init_error;
            }
            if (dirp) {
                while ((dir = readdir(dirp)) != NULL) {
                    if (!(strcmp(dir->d_name, ".")) || !(strcmp(dir->d_name, ".."))) {
                        continue;
                    }
                    snprintf(buf, sizeof(buf), "%s/%s", ADMIN_PROBE_TCPDUMP_FILE_LOCATION, dir->d_name);
                    unlink(buf);
                }
                closedir(dirp);
            }
            status = ADMIN_PROBE_RESULT_NO_ERROR;
        }
    }
init_error:
    return status;
}

void admin_probe_tcpdump_s3_output_cb (struct uploader_output uploader_output, void *cookie)
{
    tcpdump_state.total_tcpdump_in_exec--;
    struct admin_probe_task *cur_task = (struct admin_probe_task *) cookie;
    if (!cur_task) {
        ADMIN_PROBE_LOG(AL_ERROR, "invalid task in s3 output");
        tcpdump_stats.invalid_task_struct_s3_output_callback++;
        return;
    }
    if (cur_task->cleanup_criteria.is_uploader_callback_outstanding) {
        cur_task->cleanup_criteria.is_uploader_callback_outstanding = 0;
    }
    ADMIN_PROBE_DEBUG_UPLOADER("Upload has been completed for %s %d %s", cur_task->command_uuid, uploader_output.status,
                                uploader_output.error_message);
    cur_task->end_time_cloud_s = admin_probe_callbacks.get_current_cloud_epoch_s_cb();
    if (0 == uploader_output.status) {
        admin_probe_task_module_probe_process_event(NULL, cur_task->is_np_command_probe, cur_task, cur_task->command_uuid, internal_task_upload_success);
        tcpdump_stats.tcpdump_total_success++;
    } else {
        cur_task->err_message = uploader_output.error_message;
        admin_probe_task_module_probe_process_event(NULL, cur_task->is_np_command_probe, cur_task, cur_task->command_uuid, internal_task_upload_failed);
        tcpdump_stats.tcpdump_s3_output_failure++;
        tcpdump_stats.tcpdump_total_failure++;
    }

}

void
admin_probe_thread_pcap_end_callback(struct zevent_base *base, void *cookie, int64_t int_cookie)
{
    int status;
    struct admin_probe_task *cur_task = NULL;
    struct admin_probe_pcap_resp *pcap_resp = (struct admin_probe_pcap_resp *) cookie;
    if (!pcap_resp) {
        ADMIN_PROBE_LOG(AL_ERROR, "Current task invalid  in file upload after read");
        tcpdump_stats.invalid_cookie_pcap_callback++;
        goto pcap_end_err;
    }

    cur_task = (struct admin_probe_task *) pcap_resp->cookie;
    if (!cur_task) {
        ADMIN_PROBE_LOG(AL_ERROR, "Current task invalid in file upload after read");
        tcpdump_stats.invalid_task_struct_pcap_callback++;
        goto pcap_end_err;
    }

    if (cur_task->cleanup_criteria.is_execute_callback_outstanding) {
        cur_task->cleanup_criteria.is_execute_callback_outstanding = 0;
    }

    if (pcap_resp->reason) {
        if (!strcmp(pcap_resp->reason, ZPN_PCAP_CLOSE_TIME_UP) || !strcmp(pcap_resp->reason, ZPN_PCAP_CLOSE_FILE_LIMIT_EXCEEDED)) {
            cur_task->cleanup_criteria.is_uploader_callback_outstanding = 1;
            admin_probe_task_module_probe_process_event(NULL, cur_task->is_np_command_probe, cur_task, cur_task->command_uuid,
                                                        internal_task_execute_done_but_pending_upload);
            status = admin_probe_send_to_s3_uploader(cur_task->bucket_name, cur_task->file_location,
                                                     ADMIN_PROBE_TCPDUMP_FILE_LOCATION, NULL, cur_task->command_uuid,
                                                     admin_probe_tcpdump_s3_output_cb, cur_task);
            if (status == ADMIN_PROBE_RESULT_BAD_ARGUMENT) {
                cur_task->end_time_cloud_s = admin_probe_callbacks.get_current_cloud_epoch_s_cb();
                admin_probe_task_module_probe_process_event(NULL, cur_task->is_np_command_probe, cur_task, cur_task->command_uuid,
                                                            internal_task_upload_failed);
                tcpdump_state.total_tcpdump_in_exec--;
                tcpdump_stats.upload_setup_fail++;
                tcpdump_stats.tcpdump_total_failure++;
            }
            if (pcap_resp) {
                ADMIN_PROBE_FREE(pcap_resp);
            }
            return;
        }
    }
    tcpdump_stats.pcap_end_callback_error++;
pcap_end_err:
    if (cur_task) {
        cur_task->err_message = ADMIN_PROBE_COMMAND_FAIL_TCPDUMP_EXEC;
        cur_task->end_time_cloud_s = admin_probe_callbacks.get_current_cloud_epoch_s_cb();
        tcpdump_stats.tcpdump_total_failure++;
        admin_probe_task_module_probe_process_event(NULL, cur_task->is_np_command_probe, cur_task, cur_task->command_uuid,
                                                    internal_task_execute_done_failed);
    }
    if (pcap_resp) {
        ADMIN_PROBE_FREE(pcap_resp);
    }
    tcpdump_state.total_tcpdump_in_exec--;
    return;
}


int admin_probe_pcap_end_callback(struct zpn_pcap_params *params, char *job_id, int error, char *reason, void *cookie)
{
    ADMIN_PROBE_DEBUG_TASK_MODULE_TCPDUMP("Pcap has been generated and completed for %s %s", job_id, reason);
    struct admin_probe_pcap_resp *pcap_resp = ADMIN_PROBE_CALLOC(sizeof(struct admin_probe_pcap_resp));
    if (pcap_resp == NULL) {
        ADMIN_PROBE_LOG(AL_ERROR, "Could not allocate memory for pcap resp");
        tcpdump_stats.no_memory_pcap_resp++;
        tcpdump_state.total_tcpdump_in_exec--;
        tcpdump_stats.tcpdump_total_failure++;
        return ADMIN_PROBE_RESULT_NO_MEMORY;
    }
    pcap_resp->cookie = cookie;
    if (reason != NULL) {
        /* its guranteed that reason points to a valid static string*/
        pcap_resp->reason = reason;
    }
    zevent_base_call(admin_probe_state.zbase, admin_probe_thread_pcap_end_callback, (void*)pcap_resp, 0);
    return ADMIN_PROBE_RESULT_NO_ERROR;
}

/* Parses interface in the format:
 * {"interface":"eth0"}
 */
const char *admin_probe_tcpdump_parse_additional_arguments(char *additional_arguments, JSON_Value **jv)
{
    *jv = json_parse_string(additional_arguments);
    if (json_value_get_type(*jv) != JSONObject) {
       ADMIN_PROBE_LOG(AL_ERROR, "Failed to validate JSON");
        if (*jv) {
            json_value_free(*jv);
        }
        return NULL;
    }
    JSON_Object* jo = json_value_get_object(*jv);
    if (!jo) {
        ADMIN_PROBE_LOG(AL_ERROR, "Failed to validate JSON");
        if (*jv) {
            json_value_free(*jv);
        }
        return NULL;
    }
    const char* interface = json_object_get_string (jo, "interface");
    return interface;
}

int admin_probe_task_module_process_tcpdump_task_f(struct admin_probe_task *cur_task)
{
    int64_t     tcpdump_limit_exec;
    struct      zpn_pcap_params params;
    int64_t     file_chunk_size = admin_probe_rate_limiting_get_tcpdump_max_chunk_size();
    int64_t     max_limit_file_size = admin_probe_rate_limiting_get_tcpdump_max_file_size();
    char        file_name[ADMIN_PROBE_UPLOADER_MAX_FILE_NAME] = {0};
    int         port = 0;
    char        *ip = NULL;
    char        *interface = NULL;
    int         status;
    JSON_Value  *jv = NULL;


    tcpdump_limit_exec = admin_probe_rate_limiting_get_concurrent_executing_limit_per_task_type(admin_probe_task_type_tcpdump);
    ADMIN_PROBE_DEBUG_TASK_MODULE_TCPDUMP("threshold check - 's limit is currently at %"PRId64" ", tcpdump_limit_exec);

    if (tcpdump_state.total_tcpdump_in_exec >= tcpdump_limit_exec) {
        tcpdump_stats.failed_to_exec_tcpdump_because_of_threshold_reached++;
        cur_task->end_time_cloud_s = admin_probe_callbacks.get_current_cloud_epoch_s_cb();
        return ADMIN_PROBE_TASK_LIMIT_REACHED;
    }

    tcpdump_state.total_tcpdump_in_exec++;

    //check for filling port
     if (cur_task->target != NULL) {
        admin_probe_task_module_tcp_parse_request(cur_task);
        if (cur_task->task_state.tcp_task_state.request_domain != NULL) {
            ip = cur_task->task_state.tcp_task_state.request_domain;
            port = cur_task->task_state.tcp_task_state.request_port;
        } else {
            ip = cur_task->target;
        }
     }
    if (cur_task->additional_arguments) {
        interface = (char*) admin_probe_tcpdump_parse_additional_arguments(cur_task->additional_arguments, &jv);
        ADMIN_PROBE_DEBUG_TASK_MODULE_TCPDUMP("interface %s %s", interface, cur_task->command_uuid);
    }
    zpn_pcap_fill_params(&params, ip, port, interface, NULL, NULL);
    json_value_free(jv);

    snprintf(file_name, ADMIN_PROBE_UPLOADER_MAX_FILE_NAME, "%s-%s", ADMIN_PROBE_TCPDUMP_FILE_NAME, cur_task->command_uuid);
    ADMIN_PROBE_DEBUG_TASK_MODULE_TCPDUMP("Executing pcap %s %ld %s", cur_task->command_uuid, (long) cur_task->duration_in_secs, cur_task->target);

    status = zpn_pcap_setup(params, cur_task->command_uuid, 1, ADMIN_PROBE_TCPDUMP_FILE_LOCATION, file_name, file_chunk_size, max_limit_file_size,
                            cur_task->duration_in_secs, NULL, NULL, admin_probe_pcap_end_callback, (void *)cur_task);
    if (0 != status){
        if (status == ZPN_PCAP_RESULT_INVALID_INTERFACE) {
            cur_task->err_message = ADMIN_PROBE_COMMAND_FAIL_INVALID_INTERFACE;
        } else if (status == ZPN_PCAP_RESULT_INVALID_PORT) {
            cur_task->err_message = ADMIN_PROBE_COMMAND_FAIL_INVALID_PORT;
        } else if (status == ZPN_PCAP_RESULT_INVALID_HOST) {
            cur_task->err_message = ADMIN_PROBE_COMMAND_FAIL_INVALID_HOST;
        } else {
            cur_task->err_message = ADMIN_PROBE_COMMAND_FAIL_IN_GENERAL;
        }
        cur_task->end_time_cloud_s = admin_probe_callbacks.get_current_cloud_epoch_s_cb();
        tcpdump_stats.pcap_filter_setup_fail++;
        tcpdump_state.total_tcpdump_in_exec--;
        return ADMIN_PROBE_RESULT_ERR;
    }
    cur_task->cleanup_criteria.is_execute_callback_outstanding = 1;

    return ADMIN_PROBE_RESULT_NO_ERROR;

}

static int admin_probe_task_module_tcpdump_dump_stats(struct zpath_debug_state *request_state,
                                                      const char **query_values,
                                                      int query_value_count,
                                                      void *cookie) {
    char jsonout[10000];

    (void)query_values;
    (void)query_value_count;
    (void)cookie;

    if (ARGO_RESULT_NO_ERROR == argo_structure_dump(admin_probe_task_module_tcpdump_stats_description,
                                                    &tcpdump_stats, jsonout, sizeof(jsonout), NULL, 1)) {
        ZDP("%s\n", jsonout);
    }
    return ADMIN_PROBE_RESULT_NO_ERROR;
}

static int admin_probe_task_module_debug_rate_limit(struct zpath_debug_state *request_state,
                                                    const char **query_values,
                                                    int query_value_count,
                                                    void *cookie)
{
    if (!query_values[0]) {
        ZDP("Missing argument for rate limit\n");
        return ADMIN_PROBE_RESULT_ERR;
    }
    tcpdump_state.total_tcpdump_in_exec = strtoll(query_values[0], NULL, 10);
    ZDP("Rate limit set to %"PRId64"\n", tcpdump_state.total_tcpdump_in_exec);
    return ADMIN_PROBE_RESULT_NO_ERROR;
}

int
admin_probe_task_module_tcpdump_debug_init()
{
    int res;

    if (!(admin_probe_task_module_tcpdump_stats_description = argo_register_global_structure(ADMIN_PROBE_TASK_MODULE_TCPDUMP_STATS_HELPER))) {
        return ADMIN_PROBE_RESULT_ERR;
    }

    res = zpath_debug_add_read_command("dump the stats of task_module_tcp",
                                  "/admin_probe/task_module_tcpdump_stats",
                                  admin_probe_task_module_tcpdump_dump_stats,
                                  NULL,
                                  NULL);
    if (res) {
        return ADMIN_PROBE_RESULT_ERR;
    }

    res = zpath_debug_add_write_command("Set the tcpdump rate limit",
                                  "/admin_probe/task_module_tcpdump/rate_limit",
                                   admin_probe_task_module_debug_rate_limit, NULL,
                                  "value" , "rate limiting value",
                                   NULL);
    if (res) {
        return ADMIN_PROBE_RESULT_ERR;
    }

    return ADMIN_PROBE_RESULT_NO_ERROR;
}

int admin_probe_task_module_tcpdump_init_f()
{
    return ADMIN_PROBE_RESULT_NO_ERROR;
}

int admin_probe_task_module_tcpdump_check_expiry_in_processing_state_f(struct admin_probe_task *cur_task)
{
    return ADMIN_PROBE_RESULT_NO_ERROR;
}

void admin_probe_task_module_tcpdump_free_task_state_f(struct admin_probe_task *cur_task)
{
    if (cur_task->task_state.tcp_task_state.request_domain) {
        ADMIN_PROBE_FREE(cur_task->task_state.tcp_task_state.request_domain);
    }
    return;
}

int admin_probe_task_module_tcpdump_cancel_f(struct admin_probe_task *cur_task)
{
    int res;

    tcpdump_stats.tcpdump_total_cancel++;
    if (cur_task == NULL) {
        ADMIN_PROBE_LOG(AL_ERROR, "Null context in cancel");
        tcpdump_state.total_tcpdump_in_exec--;
        return ADMIN_PROBE_RESULT_ERR;
    }

    res = zpn_pcap_close(cur_task->command_uuid);
    if (res != ZPN_PCAP_RESULT_NO_ERROR) {
        ADMIN_PROBE_LOG(AL_ERROR, "Could not cancel pcap %s %d", cur_task->command_uuid, res);
    }
    cur_task->end_time_cloud_s = admin_probe_callbacks.get_current_cloud_epoch_s_cb();
    ADMIN_PROBE_DEBUG_TASK_MODULE_TCPDUMP("Cancelled pcap %s", cur_task->command_uuid);
   /* The callback gets called here, so we need not decrement rate limiter */

    return res;
}
struct admin_probe_task_module_callouts admin_probe_task_module_callouts_tcpdump = {
    admin_probe_task_module_tcpdump_init_f,
    admin_probe_task_module_process_tcpdump_task_f,
    admin_probe_task_module_tcpdump_check_expiry_in_processing_state_f,
    admin_probe_task_module_tcpdump_free_task_state_f,
    admin_probe_task_module_tcpdump_cancel_f
};


int64_t admin_probe_get_total_number_of_task_tcpdump_success()
{
    return tcpdump_stats.tcpdump_total_success;
}

int64_t admin_probe_get_total_number_of_task_tcpdump_fail()
{
    return tcpdump_stats.tcpdump_total_failure + tcpdump_stats.tcpdump_total_cancel;
}
