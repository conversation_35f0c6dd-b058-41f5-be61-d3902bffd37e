/*
 * admin_probe.c . Copyright (C) 2021 Zscaler Inc, All Rights Reserved
 */

#include "admin_probe/admin_probe.h"
#include "admin_probe/admin_probe_lib.h"
#include "zpath_lib/zpath_debug.h"
#include "admin_probe/zpn_command_probe.h"
#include "admin_probe/np_command_probe.h"
#include "admin_probe/admin_probe_task_module.h"
#include "admin_probe/admin_probe_rpc_send.h"
#include "admin_probe/admin_probe_uploader.h"
#include "zcdns/zcdns.h"
#include "zcdns/zcdns_libevent.h"
#include "admin_probe/admin_probe_private.h"
#include "admin_probe/admin_probe_task_module_tcpdump.h"
#include "admin_probe/admin_probe_task_module_frr_cmds.h"
#include "zpn_pcap/zpn_pcap_lib.h"
#include "zpn_pcap/zpn_pcap.h"

#include "zhealth/zhealth_probe_lib.h"
#include "zhealth/zhealth.h"

#include "admin_probe/admin_probe_cfg_override_desc.h"

#define MAX_ADMIN_PROBE_HEARTBEAT_TIMEOUT_S 180
#define ADMIN_PROBE_THREAD_STACK_SIZE_BYTE 16 * 1024 * 1024
#define ADMIN_PROBE_THREAD_USER_INT 60 * 1000 * 1000

#define ADMIN_PROBE_NUM_ADMIN_PROBE_EVENT_BASE      1
struct event_base *admin_probe_base_array[ADMIN_PROBE_NUM_ADMIN_PROBE_EVENT_BASE];
int g_is_np_command_probe = ADMIN_PROBE_COMMAND_TYPE_ZPN_PROBE;
enum admin_probe_app_type g_self_app_type = 0;

static void admin_probe_heartbeat_tickle(evutil_socket_t sock, short flags, void *cookie)
{
    if (admin_probe_state.tickle_me) zthread_heartbeat(admin_probe_state.tickle_me);

}

static void log_f(int priority, const char *format, va_list list)
{
    char dump[2000];
    vsnprintf(dump, sizeof(dump), format, list);
    if (priority < argo_log_priority_notice) {
        ADMIN_PROBE_LOG(priority, "%s", dump);
    } else {
        ADMIN_PROBE_DEBUG_TASK_MODULE("%s", dump);
    }
}


void *admin_probe_thread(struct zthread_info *zthread_arg, void *cookie) {

    struct event_base *base = NULL;
    struct event *HB_timer;
    struct timeval tv;
    int res;

    admin_probe_state.tickle_me = zthread_arg;

    base = event_base_new();
    if (!base) {
        ADMIN_PROBE_LOG(AL_ERROR, "Could not create event_base : admin_probe");
        goto fail;
    }

    admin_probe_state.ebase = base;
    admin_probe_state.zbase = zevent_attach(base);

    /*heartbeat init*/
    HB_timer = event_new(base,
                         -1,
                         EV_PERSIST,
                         admin_probe_heartbeat_tickle,
                         NULL);

    if (!HB_timer) {
        ADMIN_PROBE_LOG(AL_ERROR, "Could not activate timer for admin probe - HB_timer");
        goto fail;
    }

    tv.tv_sec = 1;
    tv.tv_usec = 0;

    if (event_add(HB_timer, &tv)) {
        ADMIN_PROBE_LOG(AL_ERROR, "Could not activate timer for HB_timer");
        goto fail;
    }

    /*zcdns ctx init*/
    admin_probe_state.zcdns_ctx = zcdns_libevent_create(base,
                                                        1,
                                                        NULL,
                                                        "/etc/resolv.conf",
                                                        "/etc/hosts",
                                                        log_f,
                                                        NULL);
    if (!admin_probe_state.zcdns_ctx) {
        ADMIN_PROBE_LOG(AL_ERROR, "Could not initialize ZCDNS for admin_probe. Ensure  /etc/resolv.conf and /etc/hosts are valid");
        goto fail;
    }

    admin_probe_base_array[0] = base;
    admin_probe_state.zhealth_ctx = zhealth_create(&admin_probe_base_array[0], ADMIN_PROBE_NUM_ADMIN_PROBE_EVENT_BASE, admin_probe_event_collection);
    if (!admin_probe_state.zhealth_ctx) {
        ADMIN_PROBE_LOG(AL_ERROR, "Could not create zhealth object");
        goto fail;
    }




    if (admin_probe_state.customer_gid == 0 || admin_probe_state.entity_gid == 0) {
        goto fail;
    }

    /*task module init*/

    res = admin_probe_task_module_init(base);
    if (res) {
        ADMIN_PROBE_LOG(AL_ERROR, "Could not init admin_probe_task_module_init");
        goto fail;
    }


    if (g_is_np_command_probe == ADMIN_PROBE_COMMAND_TYPE_ZPN_PROBE) {
        res = zpn_command_probe_register_by_entity_gid_using_default_registrant(admin_probe_state.customer_gid, admin_probe_state.entity_gid, NULL, NULL);
    } else {
        res = np_command_probe_register_by_entity_gid_using_default_registrant(admin_probe_state.customer_gid, admin_probe_state.entity_gid, NULL, NULL);
    }

    if (res) {
        ADMIN_PROBE_LOG(AL_ERROR, "command_registration failed. type:%d error:%s", g_is_np_command_probe, zpath_result_string(res));
        goto fail;
    }

    res = admin_probe_rpc_debug_init();
    if (res) {
        ADMIN_PROBE_LOG(AL_ERROR, "Could not init rpc debug");
        goto fail;
    }

    res = admin_probe_rpc_send_init();
    if (res) {
        ADMIN_PROBE_LOG(AL_ERROR, "Could not init rpc send");
        goto fail;
    }


    zevent_base_dispatch(base);

fail:
    /* Should watchdog... */
    while (1) {
        sleep(1);
    }
}

int admin_probe_allocator_init(struct argo_log_collection *event_log) {
    int res = 0;
    res = admin_probe_lib_init(event_log);
    if (res) {
        ADMIN_PROBE_LOG(AL_ERROR, "can not init admin probe lib");
        return ADMIN_PROBE_RESULT_ERR;
    }
    return res;
}

int admin_probe_init(struct wally *wally,
                    int64_t entity_gid,
                    char *entity_name,
                    int64_t customer_gid,
                    enum admin_probe_app_type app_type,
                    task_status_update_cb_f* status_update_cb,
                    check_feature_flag_cb_f* feature_flag_check_cb,
                    restart_cb_f* restart_cb,
                    frr_cmds_execute_cb_f* frr_cmds_execute_cb,
                    get_cloud_epoch_s_cb_f* get_current_cloud_epoch_s_cb,
                    int is_dev_env,
                    int is_np_command_probe,
                    get_remote_host_for_s3_upload_f* get_remote_host_for_s3_upload,
                    get_entity_state_paused_mode_f* get_entity_paused_mode_cb,
                    struct argo_log_collection *event_log,
                    int is_zpath_config_override_inited)
{
    int res;

    res = admin_probe_lib_init(event_log);
    if (res) {
        ADMIN_PROBE_LOG(AL_ERROR, "can not init admin probe lib");
        return ADMIN_PROBE_RESULT_ERR;
    }

    g_is_np_command_probe = is_np_command_probe;
    g_self_app_type = app_type;

    if (g_is_np_command_probe) {
        res = np_command_probe_init(wally, entity_gid, 0 /* no fully load */, 0);
        if (res) {
            ADMIN_PROBE_LOG(AL_ERROR, "np_command_probe_init failed");
            return ADMIN_PROBE_RESULT_ERR;
        }
    } else {
        res = zpn_command_probe_init(wally, entity_gid, 0 /* no fully load */, 0);
        if (res) {
            ADMIN_PROBE_LOG(AL_ERROR, "zpn_command_probe_init failed");
            return ADMIN_PROBE_RESULT_ERR;
        }
    }

    if (!status_update_cb || !get_current_cloud_epoch_s_cb) {
        ADMIN_PROBE_LOG(AL_ERROR, "There is either no status_update_cb or get_current_cloud_epoch_s_cb provided, can not init admin_probe");
        return ADMIN_PROBE_RESULT_ERR;
    }
    admin_probe_state.entity_gid = entity_gid;
    admin_probe_state.customer_gid = customer_gid;
    admin_probe_callbacks.status_update_cb = (task_status_update_cb_f *)status_update_cb;
    admin_probe_callbacks.feature_flag_check_cb = (check_feature_flag_cb_f *)feature_flag_check_cb;
    admin_probe_callbacks.restart_cb = (restart_cb_f *)restart_cb;
    admin_probe_callbacks.frr_cmds_execute_cb = (frr_cmds_execute_cb_f *)frr_cmds_execute_cb;
    admin_probe_callbacks.get_current_cloud_epoch_s_cb = (get_cloud_epoch_s_cb_f *)get_current_cloud_epoch_s_cb;
    admin_probe_callbacks.get_remote_host_for_s3_upload_cb = (get_remote_host_for_s3_upload_f *)get_remote_host_for_s3_upload;
    admin_probe_callbacks.is_entity_paused = (get_entity_state_paused_mode_f *)get_entity_paused_mode_cb;
    admin_probe_state.admin_probe_init_time_s = admin_probe_callbacks.get_current_cloud_epoch_s_cb();

    if (entity_name) {
        snprintf(admin_probe_state.entity_name, sizeof(admin_probe_state.entity_name), "%s", entity_name);
    } else {
        snprintf(admin_probe_state.entity_name, sizeof(admin_probe_state.entity_name), "%"PRId64"", entity_gid);
    }

    if (is_dev_env) {
        admin_probe_state.is_dev_env = 1;
    }

    if (g_self_app_type != admin_probe_app_type_np_gateway) {
        // Tcpdump is currently not supported for NP gateway
        res = admin_probe_task_module_tcpdump_init();
        if (res) {
            ADMIN_PROBE_LOG(AL_ERROR, "Could not init admin probe pcap module");
        return ADMIN_PROBE_RESULT_ERR;
        }
    }

    if (is_np_command_probe) {
        // Init task module frr commands only for NP connector and Gateway
        res = admin_probe_task_module_frr_cmds_init(g_self_app_type);
        if (res) {
            ADMIN_PROBE_LOG(AL_ERROR, "Could not init admin probe frr cmds module");
            return ADMIN_PROBE_RESULT_ERR;
        }
    }

    res = admin_probe_uploader_module_init();
    if (res) {
        ADMIN_PROBE_LOG(AL_ERROR, "Can not init admin_probe_uploader_module_init_1");
        return ADMIN_PROBE_RESULT_ERR;
    }

    if (is_zpath_config_override_inited) {
        res = admin_probe_cfg_override_desc_register_all();
        if (res) {
            ADMIN_PROBE_LOG(AL_ERROR, "admin_probe_cfg_override_desc_register_all failed");
            return res;
        }

        if (is_np_command_probe) {
            res = admin_probe_cfg_override_desc_register_np();
            if (res) {
                ADMIN_PROBE_LOG(AL_ERROR, "admin_probe_cfg_override_desc_register_np failed");
                return res;
            }
        }

        if (app_type == admin_probe_app_type_connector || app_type == admin_probe_app_type_private_broker) {
            res = admin_probe_cfg_override_desc_register_appc_pbroker();
            if (res) {
                ADMIN_PROBE_LOG(AL_ERROR, "admin_probe_cfg_override_desc_register_appc_prboker failed");
                return res;
            }
        }
    }

    res = zthread_create(&(admin_probe_state.thread),
                         admin_probe_thread,
                         NULL,
                         "admin_probe",
                         MAX_ADMIN_PROBE_HEARTBEAT_TIMEOUT_S,
                         ADMIN_PROBE_THREAD_STACK_SIZE_BYTE,
                         ADMIN_PROBE_THREAD_USER_INT,
                         NULL);
    if (res) return ADMIN_PROBE_RESULT_ERR;

    ADMIN_PROBE_LOG(AL_DEBUG, "admin probe init done");

    return ADMIN_PROBE_RESULT_NO_ERROR;
}
