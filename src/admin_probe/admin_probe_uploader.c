/*
 * admin_probe_uploader.c. Copyright (C) 2021 Zscaler Inc. All Rights Reserved.
 */
#include <stdio.h>
#include <sys/time.h>
#include <ctype.h>
#include <errno.h>
#include <fcntl.h>
#include <signal.h>
#include <sys/stat.h>
#include <utime.h>
#include <dirent.h>
#include "zpath_lib/zpath_debug.h"
#include "fohh/http_parser.h"
#include "admin_probe/admin_probe_uploader.h"

#include "fohh/fohh.h"
#include "fohh/fohh_http.h"

#include "admin_probe/admin_probe_rate_limiting.h"

#define ADMIN_PROBE_UPLOADER_PROXY_PORT 443
#define ADMIN_PROBE_UPLOADER_HTTP_TIMEOUT_US 60*(1000*1000)

typedef enum uploader_status {
  admin_probe_uploader_success,
  admin_probe_uploader_failed,
  admin_probe_uploader_success_after_retry,
  admin_probe_uploader_timeout
} admin_probe_uploader_status;
/*
 * Today there is a single task in execution. This is used just for debugging purposes.
 */
admin_probe_uploader_task *cur_upload_task;

static
struct admin_probe_uploader_stats {                                                      /* _ARGO: * object_definition */
     int64_t admin_probe_uploader_null_evbuf;                                           /* _ARGO: integer */
     int64_t admin_probe_uploader_add_evbuf;                                            /* _ARGO: integer */
     int64_t admin_probe_uploader_ssl_ctx;                                              /* _ARGO: integer */
     int64_t admin_probe_uploader_dns_timed_out;                                        /* _ARGO: integer */
     int64_t admin_probe_uploader_dns_fail;                                             /* _ARGO: integer */
     int64_t admin_probe_uploader_status_connect_timeout;                               /* _ARGO: integer */
     int64_t admin_probe_uploader_status_connect;                                       /* _ARGO: integer */
     int64_t admin_probe_uploader_fail_tls;                                             /* _ARGO: integer */
     int64_t admin_probe_uploader_fail_tls_cert;                                        /* _ARGO: integer */
     int64_t admin_probe_uploader_status_timeout;                                       /* _ARGO: integer */
     int64_t admin_probe_uploader_status_fail;                                          /* _ARGO: integer */
     int64_t admin_probe_uploader_header_fail;                                          /* _ARGO: integer */
     int64_t admin_probe_uploader_add_header_fail;                                      /* _ARGO: integer */
     int64_t admin_probe_uploader_client_request_status_failure;                        /* _ARGO: integer */
     int64_t admin_probe_uploader_client_request_status_not_success;                    /* _ARGO: integer */
     int64_t admin_probe_uploader_client_request_status_timeout;                        /* _ARGO: integer */
     int64_t admin_probe_uploader_fail_timestamp;                                       /* _ARGO: integer */
     int64_t admin_probe_uploader_null_upload_task;                                     /* _ARGO: integer */
     int64_t admin_probe_uploader_buffer_size_error;                                    /* _ARGO: integer */
     int64_t admin_probe_uploader_bucket_success;                                       /* _ARGO: integer */
     int64_t admin_probe_uploader_invalid_argument_sent_callback;                       /* _ARGO: integer */
     int64_t admin_probe_uploader_s3_callback;                                          /* _ARGO: integer */
     int64_t admin_probe_uploader_retry;                                                /* _ARGO: integer */
     int64_t admin_probe_uploader_retry_cb;                                             /* _ARGO: integer */
     int64_t admin_probe_uploader_max_retry_timeout;                                    /* _ARGO: integer */
     int64_t admin_probe_uploader_null_command_uuid;                                    /* _ARGO: integer */
     int64_t admin_probe_uploader_bucket_invalid;                                       /* _ARGO: integer */
     int64_t admin_probe_uploader_file_location_invalid;                                /* _ARGO: integer */
     int64_t admin_probe_uploader_null_context;                                         /* _ARGO: integer */
     int64_t admin_probe_uploader_invalid_command_uuid;                                 /* _ARGO: integer */
     int64_t admin_probe_uploader_null_file_linked_list;                                /* _ARGO: integer */
     int64_t admin_probe_uploader_file_open_fail;                                       /* _ARGO: integer */
     int64_t admin_probe_uploader_file_size_fail;                                       /* _ARGO: integer */
     int64_t admin_probe_uploader_invalid_directory_name;                               /* _ARGO: integer */
     int64_t admin_probe_uploader_null_directory_name;                                  /* _ARGO: integer */
     int64_t admin_probe_uploader_open_directory_fail;                                  /* _ARGO: integer */
     int64_t admin_probe_uploader_null_context_next_file;                               /* _ARGO: integer */
     int64_t admin_probe_uploader_null_head_next_file;                                  /* _ARGO: integer */
     int64_t admin_probe_uploader_null_context_req_timeout;                             /* _ARGO: integer */
     int64_t admin_probe_uploader_null_context_retry;                                   /* _ARGO: integer */
     int64_t admin_probe_uploader_null_context_next_req_cb;                             /* _ARGO: integer */
     int64_t admin_probe_uploader_null_context_req_cb;                                  /* _ARGO: integer */
     int64_t admin_probe_uploader_http_client_null_req_cb;                              /* _ARGO: integer */
     int64_t admin_probe_client_request_fail;                                           /* _ARGO: integer */
     int64_t admin_probe_uploader_new_client;                                           /* _ARGO: integer */
     int64_t admin_probe_uploader_s3_cb_invalid_cookie;                                 /* _ARGO: integer */
     int64_t admin_probe_uploader_s3_cb_invalid_task;                                   /* _ARGO: integer */
     int64_t admin_probe_uploader_null_task_req_cb;                                     /* _ARGO: integer */
     int64_t admin_probe_uploader_list_head_malloc_fail;                                /* _ARGO: integer */
     int64_t admin_probe_uploader_null_context_client_create_cb;                        /* _ARGO: integer */
     int64_t admin_probe_upload_task_mem_fail;                                          /* _ARGO: integer */
     int64_t admin_probe_s3_output_mem_fail;                                            /* _ARGO: integer */
     int64_t admin_probe_uploader_cur_list_malloc_fail;                                 /* _ARGO: integer */
     int64_t admin_probe_uploader_async_client_create_cb;                               /* _ARGO: integer */
     int64_t admin_probe_uploader_timeout_cb;                                           /* _ARGO: integer */
     int64_t admin_probe_uploader_async_client_request_cb;                              /* _ARGO: integer */
     int64_t admin_probe_uploader_stale_file_fail;                                      /* _ARGO: integer */
} stats;
#include "admin_probe/admin_probe_uploader_compiled_c.h"
static struct argo_structure_description*  admin_probe_uploader_stats_desciption;

enum admin_probe_async_type{
    admin_probe_uploader_async_client_create,
    admin_probe_uploader_async_client_request
};

static
struct global_pending {
    int create_client_count;
    int request_client_count;
} global_pending_request;

static int
admin_probe_upload_next_file(struct fohh_http_client *client, void *void_cookie);
/*
 * The async callbacks are called more than once when there is a failure.
  * Using synchronous callback is an option but a dns issue resulted in the thread waiting.
  * The heartbeat can be tuned but an async is better option. So we protect it to only service
  * if there is a pending request. If not it will be ignored.
 */
static int
admin_probe_uploader_async_call_count (enum admin_probe_async_type type)
{
    if (type == admin_probe_uploader_async_client_create) {
        return global_pending_request.create_client_count;
    } else {
         return global_pending_request.request_client_count;
    }
}
static void
admin_probe_uploader_decrement_count(enum admin_probe_async_type type)
{
    if (type == admin_probe_uploader_async_client_create) {
        global_pending_request.create_client_count--;
    } else {
        global_pending_request.request_client_count--;
    }
}
static void
admin_probe_uploader_increment_count(enum admin_probe_async_type type)
{
     if (type == admin_probe_uploader_async_client_create) {
        global_pending_request.create_client_count++;
    } else {
        global_pending_request.request_client_count++;
    }
}

static void
admin_probe_send_back_result_task_module(admin_probe_uploader_task *upload_task, int status, char *error_message)
{
    admin_probe_uploader_output out;
    struct admin_probe_uploader_file_list *first, *next;
    out.error_message = error_message;
    out.status = status;

    stats.admin_probe_uploader_s3_callback++;
    if (upload_task) {
        if (upload_task->client) {
            fohh_http_client_destroy_from_another_thread(upload_task->client);
            upload_task->client = NULL;
        }

        out.command_uuid = upload_task->command_uuid;
        if(upload_task->timeout_timer) {
            event_free(upload_task->timeout_timer);
            upload_task->timeout_timer = NULL;
        }
        if (upload_task->s3_output_cb) {
            upload_task->s3_output_cb(out, upload_task->cookie);
        }
        if (upload_task->command_uuid) {
            ADMIN_PROBE_FREE(upload_task->command_uuid);
            upload_task->command_uuid = NULL;
        }
        if (upload_task->file_location) {
            ADMIN_PROBE_FREE(upload_task->file_location);
             upload_task->file_location = NULL;
        }
        if (upload_task->bucket_name) {
            ADMIN_PROBE_FREE(upload_task->bucket_name);
             upload_task->bucket_name = NULL;
        }
        if (upload_task->ssl_ctx) {
            SSL_CTX_free(upload_task->ssl_ctx);
            upload_task->ssl_ctx = NULL;
        }
        if (upload_task->head) {
            if (!ZTAILQ_EMPTY(upload_task->head)) {
                ZTAILQ_FOREACH_SAFE(first, upload_task->head, file_list_entry, next) {
                    ZTAILQ_REMOVE(upload_task->head, first, file_list_entry);
                    close(first->fd);
                    unlink(first->file_name);
                    if (first->buf) {
                        evbuffer_free(first->buf);
                        first->buf = NULL;
                    }
                    if (first->extra_headers) {
                        evbuffer_free(first->extra_headers);
                        first->extra_headers = NULL;
                    }
                    ADMIN_PROBE_FREE(first);
                }
            }
            ADMIN_PROBE_FREE(upload_task->head);
            upload_task->head = NULL;
        }
        ADMIN_PROBE_FREE(upload_task);
        upload_task = NULL;
    }
    cur_upload_task = NULL;

}

char *admin_probe_uploader_error_string(char *command_uuid, int status, int http_status) {
   if (status == fohh_http_client_request_status_success && http_status != ADMIN_PROBE_UPLOADER_HTTP_OK) {
        ADMIN_PROBE_LOG(AL_ERROR, "%s: Admin probe upload failed due to error %s", command_uuid?command_uuid:"", (char*)http_status_str(http_status));
        return ADMIN_PROBE_COMMAND_UPLOADER_FAILED_HTTP_ERR_RESPONSE;
    } else if (status != fohh_http_client_request_status_success) {
        ADMIN_PROBE_LOG(AL_ERROR, "%s: Admin probe upload failed due to error %s",  command_uuid?command_uuid:"", fohh_http_client_status_str(status));
        return ADMIN_PROBE_COMMAND_UPLOADER_FAILED_FOHH_CLIENT_FAILURE;
    }
    ADMIN_PROBE_LOG(AL_NOTICE, "%s: Admin probe upload complete %s",  command_uuid? command_uuid:"", fohh_http_client_status_str(status));
    return ADMIN_PROBE_UPLOAD_COMPLETE;
}

static void
admin_probe_uploader_move_to_next_file (admin_probe_uploader_task *upload_task)
{
    int status;
    struct admin_probe_uploader_file_list *first;
    if (!upload_task) {
        ADMIN_PROBE_LOG(AL_ERROR, "Null context in client move file");
        stats.admin_probe_uploader_null_context_next_file++;
        status = ADMIN_PROBE_RESULT_BAD_DATA;
        goto ret_failure;
    }

    if (!(upload_task->head)) {
        ADMIN_PROBE_LOG(AL_ERROR, "Null context for file in client create in move file %s", upload_task->command_uuid);
        stats.admin_probe_uploader_null_head_next_file++;
        status = ADMIN_PROBE_RESULT_BAD_DATA;
        goto ret_failure;
    }

    if (!(ZTAILQ_EMPTY(upload_task->head))) {
        first = ZTAILQ_FIRST(upload_task->head);
        ZTAILQ_REMOVE(upload_task->head, first, file_list_entry);
        ADMIN_PROBE_DEBUG_UPLOADER("Admin probe deleting file %s %s", first->file_name, upload_task->command_uuid);
        close(first->fd);
        unlink(first->file_name);
        evbuffer_free(first->buf);
        first->buf = NULL;
        evbuffer_free(first->extra_headers);
        first->extra_headers = NULL;
        ADMIN_PROBE_FREE(first);
        first = NULL;
        first = ZTAILQ_FIRST(upload_task->head);
        if (first != NULL) {
            ADMIN_PROBE_DEBUG_UPLOADER("Admin probe next file upload same client %s", upload_task->command_uuid);
            zthread_heartbeat(NULL);
            upload_task->file_uploaded_cnt++;
            if (upload_task->file_uploaded_cnt <= ADMIN_PROBE_UPLOADER_MAX_FILE_CNT_FOR_UPLOAD) {
                admin_probe_upload_next_file(upload_task->client, upload_task);
            }
            return;
        }
    }

ret_failure:
    if (upload_task) {
        status = upload_task->upload_status;
    }
    if (status == ADMIN_PROBE_RESULT_BAD_DATA) {
        admin_probe_send_back_result_task_module(
                upload_task, admin_probe_uploader_failed, ADMIN_PROBE_CLIENT_REQUEST_ERROR);
    } else {
        if (status == fohh_http_client_request_status_success) {
            if (upload_task->http_status == ADMIN_PROBE_UPLOADER_HTTP_OK) {
                stats.admin_probe_uploader_bucket_success++;
            }
        }
        admin_probe_send_back_result_task_module(upload_task,
                                                 ((status == fohh_http_client_request_status_success) && (upload_task->http_status == ADMIN_PROBE_UPLOADER_HTTP_OK))
                                                         ? admin_probe_uploader_success
                                                         : admin_probe_uploader_failed,
                                                 admin_probe_uploader_error_string(upload_task->command_uuid, status, upload_task->http_status));
    }
}

static void
admin_probe_uploader_http_req_timeout_cb (evutil_socket_t sock, short flags, void *cookie)
{
    admin_probe_uploader_task *upload_task = (admin_probe_uploader_task *) cookie;
    if (!upload_task) {
        ADMIN_PROBE_LOG(AL_ERROR, "Null context in req timeout cb");
        stats.admin_probe_uploader_null_context_req_timeout++;
        return;
    }
    stats.admin_probe_uploader_timeout_cb++;
    upload_task->upload_status = admin_probe_uploader_timeout;
    upload_task->upload_error_message = ADMIN_PROBE_UPLOAD_REQUEST_TIMEOUT;
    admin_probe_send_back_result_task_module(
                upload_task, admin_probe_uploader_timeout, ADMIN_PROBE_UPLOAD_REQUEST_TIMEOUT);
}


static void
admin_probe_uploader_retry_cb (evutil_socket_t sock, short flags, void *cookie)
{
    admin_probe_uploader_task *upload_task = (admin_probe_uploader_task*) cookie;
    ADMIN_PROBE_DEBUG_UPLOADER( "Retry callback %s count %d", upload_task->command_uuid, upload_task->retry_count);

    stats.admin_probe_uploader_retry_cb++;
    admin_probe_upload_next_file(upload_task->client, upload_task);
    return;
}

/*
 * Retry - Will create a new client for upload where as a move_to_next_file uses the same client
 */
static int
admin_probe_retry_s3_upload (admin_probe_uploader_task *upload_task)
{

    struct event *ev_timer;
    struct timeval tv;
    int retry_count;

    if (!upload_task) {
         ADMIN_PROBE_LOG(AL_ERROR, "Retry timer argument error");
         stats.admin_probe_uploader_null_context_retry++;
         return ADMIN_PROBE_RESULT_BAD_ARGUMENT;
    }
    upload_task->retry_count++;
    stats.admin_probe_uploader_retry++;
    ADMIN_PROBE_DEBUG_UPLOADER("Retry now %d %s", upload_task->retry_count, upload_task->command_uuid);
    retry_count = admin_probe_rate_limiting_get_uploader_retry_count();
    if (upload_task->retry_count > retry_count) {
        ADMIN_PROBE_DEBUG_UPLOADER("Retry done %d %s", upload_task->retry_count, upload_task->command_uuid);
        admin_probe_send_back_result_task_module(upload_task, admin_probe_uploader_failed,
                                                 admin_probe_uploader_error_string(upload_task->command_uuid, upload_task->upload_status, upload_task->http_status));
        stats.admin_probe_uploader_max_retry_timeout++;
        return ADMIN_PROBE_RESULT_NO_ERROR;
    }

    ev_timer = event_new(admin_probe_state.ebase, -1, 0, admin_probe_uploader_retry_cb, (void *)upload_task);
    tv.tv_sec = ADMIN_PROBE_RETRY_START_TIME * upload_task->retry_count;
    tv.tv_usec = 0;
    event_add(ev_timer, &tv);

    return ADMIN_PROBE_RESULT_NO_ERROR;
}

static void
admin_probe_thread_uploader_http_request_cb (struct zevent_base *base, void *void_cookie, int64_t int_cookie)
{
    struct admin_probe_s3_output_resp *s3_resp = (struct admin_probe_s3_output_resp*)void_cookie;
    int status;
    admin_probe_uploader_task *upload_task = NULL;

    if (admin_probe_uploader_async_call_count(admin_probe_uploader_async_client_request) != 1) {
        ADMIN_PROBE_LOG(AL_ERROR, "Async cb returns twice");
        stats.admin_probe_uploader_async_client_request_cb++;
        return;
    }
    admin_probe_uploader_decrement_count(admin_probe_uploader_async_client_request);
    if (!s3_resp) {
        ADMIN_PROBE_LOG(AL_ERROR, "Null context in request cb");
        stats.admin_probe_uploader_null_context_req_cb++;
        goto ret_failure;
    }
    upload_task = (admin_probe_uploader_task *)s3_resp->cookie;
    if (!upload_task) {
        ADMIN_PROBE_LOG(AL_ERROR, "Null task in request cb");
        stats.admin_probe_uploader_null_task_req_cb++;
        goto ret_failure;
    }

    if (!upload_task->client) {
        ADMIN_PROBE_LOG(AL_ERROR, "Could not create HTTP context %s", upload_task->command_uuid);
        stats.admin_probe_uploader_http_client_null_req_cb++;
        goto ret_failure;
    }

    status = s3_resp->status;
    if (s3_resp->status == fohh_http_client_request_status_timeout) {
        ADMIN_PROBE_LOG(AL_ERROR, "Admin probe output upload failed: timeout %s", upload_task->command_uuid);
        stats.admin_probe_uploader_client_request_status_timeout++;
    } else if (status == fohh_http_client_request_status_failure) {
        ADMIN_PROBE_LOG(AL_ERROR, "Admin probe output failed: http protocol failure %s", upload_task->command_uuid);
        stats.admin_probe_uploader_client_request_status_failure++;
    } else if (status != fohh_http_client_request_status_success) {
        ADMIN_PROBE_LOG(AL_ERROR, "Admin probe output failed %s", upload_task->command_uuid);
        stats.admin_probe_uploader_client_request_status_not_success++;
    } else {
        ADMIN_PROBE_LOG(AL_DEBUG, "Admin probe output complete %d %s", s3_resp->req_status,
                        upload_task->command_uuid);
    }
    upload_task->upload_status = status;
    upload_task->http_status = s3_resp->req_status;

    if (status == fohh_http_client_request_status_success) {
        admin_probe_uploader_move_to_next_file(upload_task);
    } else {
        admin_probe_retry_s3_upload(upload_task);
    }
    ADMIN_PROBE_FREE(s3_resp);
    return;

ret_failure:
    if (upload_task) {
        admin_probe_send_back_result_task_module(upload_task, admin_probe_uploader_failed, ADMIN_PROBE_CLIENT_REQUEST_INVALID_DATA);
    }
    ADMIN_PROBE_FREE(s3_resp);
    return;
}

static int
admin_probe_uploader_http_request_cb (struct fohh_http_client *client,
                                      enum fohh_http_client_request_status status,
                                      int req_status,
                                      struct evbuffer *result_buf,
                                      void *void_cookie,
                                      int64_t int_cookie)
{
    struct admin_probe_s3_output_resp *s3_resp = ADMIN_PROBE_CALLOC(sizeof(struct admin_probe_s3_output_resp));
    if(s3_resp == NULL) {
        stats.admin_probe_s3_output_mem_fail++;
        ADMIN_PROBE_LOG(AL_ERROR, "Could not allocate s3 output resp");
        return ADMIN_PROBE_RESULT_NO_MEMORY;
    }
    s3_resp->cookie = void_cookie;
    s3_resp->status = status;
    s3_resp->req_status = req_status;
    zevent_base_call(admin_probe_state.zbase, admin_probe_thread_uploader_http_request_cb, (void*) s3_resp, 0);
    return ADMIN_PROBE_RESULT_NO_ERROR;
}

static int
admin_probe_upload_next_file (struct fohh_http_client *client, void *void_cookie)
{
    int     res;
    int64_t http_timeout_us;
    char    full_path[2000];
    int     status = ADMIN_PROBE_RESULT_NO_ERROR;


    admin_probe_uploader_task *upload_task = (admin_probe_uploader_task *)void_cookie;
    if (!void_cookie) {
        ADMIN_PROBE_LOG(AL_ERROR, "Null context in next file");
        stats.admin_probe_uploader_null_context++;
        goto ret_fail;
    }

    struct admin_probe_uploader_file_list *first = ZTAILQ_FIRST(upload_task->head);
    if (first == NULL) {
        ADMIN_PROBE_LOG(AL_ERROR, "first null %s", upload_task->command_uuid);
        stats.admin_probe_uploader_null_file_linked_list++;
        goto ret_fail;
    }

    first->buf = evbuffer_new();
    if (!first->buf) {
        ADMIN_PROBE_LOG(AL_ERROR, "Buffer null %s", upload_task->command_uuid);
        stats.admin_probe_uploader_null_evbuf++;
        goto ret_fail;
    }

    first->fd = open(first->file_name , O_RDONLY);
    if (first->fd == -1) {
        ADMIN_PROBE_LOG(AL_ERROR, " Reading open error %s", upload_task->command_uuid);
        stats.admin_probe_uploader_file_open_fail++;
        goto after_buf_fail;
    }

    ADMIN_PROBE_DEBUG_UPLOADER("evbuffer add for size %s", first->file_name);

    if (evbuffer_add_file(first->buf, first->fd, 0, -1)) {
        ADMIN_PROBE_LOG(AL_ERROR, "evbuffer add  file fail %s", upload_task->command_uuid);
        stats.admin_probe_uploader_add_evbuf++;
        goto after_file_fail;
    }

    if (first->is_override_default_upload_path) {
        snprintf(full_path, sizeof(full_path), "/%s/%s", upload_task->file_location, first->custom_upload_path);
    } else {
        snprintf(full_path, sizeof(full_path), "/%s/%s", upload_task->file_location, first->file_name);
    }

    ADMIN_PROBE_DEBUG_UPLOADER("Uploading command(%s) output file to:%s", upload_task->command_uuid, full_path);

    first->extra_headers = evbuffer_new();
    if (!first->extra_headers) {
        stats.admin_probe_uploader_header_fail++;
        ADMIN_PROBE_LOG(AL_ERROR, "extra header fail %s", upload_task->command_uuid);
        goto after_file_fail;
    }

    if (!evbuffer_add_printf(first->extra_headers, "x-amz-acl: bucket-owner-full-control\r\n")) {
        stats.admin_probe_uploader_add_header_fail++;
        ADMIN_PROBE_LOG(AL_ERROR, "extra header add fail %s", upload_task->command_uuid);
        goto after_header_fail;
    }
    http_timeout_us = admin_probe_rate_limiting_get_uploader_http_send_timeout_us();
    res = fohh_http_client_request(client,
                                   http_timeout_us,
                                   FOHH_HTTP_METHOD_PUT,
                                   full_path,
                                   first->extra_headers,
                                   "application/octet-stream",
                                   first->buf,
                                   admin_probe_uploader_http_request_cb,
                                   upload_task,
                                   0);
    if (res) {
        ADMIN_PROBE_LOG(AL_ERROR, "Could not upload admin probe output %s", upload_task->command_uuid);
        status = res;
        stats.admin_probe_client_request_fail++;
        goto after_header_fail;
    }
    admin_probe_uploader_increment_count(admin_probe_uploader_async_client_request);

    return status;

after_header_fail:
    evbuffer_free(first->extra_headers);
    first->extra_headers = NULL;
after_file_fail:
    close(first->fd);
after_buf_fail:
    evbuffer_free(first->buf);
    first->buf = NULL;
ret_fail:
    admin_probe_send_back_result_task_module(upload_task, admin_probe_uploader_failed, ADMIN_PROBE_CLIENT_REQUEST_ERROR);
    return status;
}

static
void admin_probe_thread_uploader_client_create_cb (struct zevent_base *base, void *void_cookie, int64_t int_cookie)
{
    struct  fohh_ssl_status *ssl_status;
    int status;
    admin_probe_uploader_task *upload_task = NULL;

    if (admin_probe_uploader_async_call_count(admin_probe_uploader_async_client_create) != 1) {
        ADMIN_PROBE_LOG(AL_ERROR, "Async cb returns twice");
        stats.admin_probe_uploader_async_client_create_cb++;
        return;
    }
    admin_probe_uploader_decrement_count(admin_probe_uploader_async_client_create);

    struct admin_probe_s3_output_resp *s3_resp = (struct admin_probe_s3_output_resp*)void_cookie;
    if (!s3_resp) {
        ADMIN_PROBE_LOG(AL_ERROR, "Null context in request cb");
        stats.admin_probe_uploader_null_context_client_create_cb++;
        goto ret_failure;
     }

    upload_task = (admin_probe_uploader_task *)s3_resp->cookie;
    if (!upload_task) {
         ADMIN_PROBE_LOG(AL_ERROR, "Null context in next file");
         stats.admin_probe_uploader_null_context++;
         goto ret_failure;
    }

    status = s3_resp->status;
    ADMIN_PROBE_DEBUG_UPLOADER("client status in callback %d %s", status, upload_task->command_uuid);
    if (status != fohh_http_client_status_success) {
        switch(status) {
        case fohh_http_client_status_dns_timeout:
            ADMIN_PROBE_LOG(AL_ERROR, "DNS timed out for %s %s", upload_task->bucket_name, upload_task->command_uuid);
            stats.admin_probe_uploader_dns_timed_out++;
            break;
        case fohh_http_client_status_dns_fail:
            ADMIN_PROBE_LOG(AL_ERROR, "DNS resolution failed for %s %s", upload_task->bucket_name, upload_task->command_uuid);
            stats.admin_probe_uploader_dns_fail++;
            break;
        case fohh_http_client_status_connect_timeout:
            ADMIN_PROBE_LOG(AL_ERROR, "TCP connection timed out to %s %s", upload_task->bucket_name, upload_task->command_uuid);
            stats.admin_probe_uploader_status_connect_timeout++;
            break;
        case fohh_http_client_status_connect_fail:
            ADMIN_PROBE_LOG(AL_ERROR, "TCP connection failed to %s %s", upload_task->bucket_name, upload_task->command_uuid);
            stats.admin_probe_uploader_status_connect++;
           break;
        case fohh_http_client_status_connect_fail_ssl:
            ssl_status = fohh_http_client_get_ssl_status(s3_resp->client);
            if (ssl_status->err) {
                ADMIN_PROBE_LOG(AL_ERROR, "TLS Verification Failure. Failed certificate check at depth=%d, where subject=%s, issuer=%s. Error=%s %s",
                         ssl_status->x509_err_depth,
                          ssl_status->err_subject,
                          ssl_status->err_issuer,
                          ssl_status->x509_err ? ssl_status->x509_err : "Unknown",
                          upload_task->command_uuid);

                stats.admin_probe_uploader_fail_tls++;
            } else {
                ADMIN_PROBE_LOG(AL_ERROR, "TLS connection failed before certificate verification %s", upload_task->command_uuid);
                stats.admin_probe_uploader_fail_tls_cert++;
            }
            break;
        case fohh_http_client_status_timeout:
            ADMIN_PROBE_LOG(AL_ERROR, "Timeout attempting to reach %s %s", upload_task->bucket_name, upload_task->command_uuid);
            stats.admin_probe_uploader_status_timeout++;
            break;
        case fohh_http_client_status_failure:
        default:
           ADMIN_PROBE_LOG(AL_ERROR, "Failure attempting to reach %s %s", upload_task->bucket_name, upload_task->command_uuid);
            stats.admin_probe_uploader_status_fail++;
            break;
       }

        upload_task->upload_status = status;
        goto ret_failure;
    }
    if (s3_resp->client != NULL) {
        upload_task->client = s3_resp->client;
        admin_probe_upload_next_file(upload_task->client, upload_task);
    }
    ADMIN_PROBE_FREE(s3_resp);
    return;
ret_failure:
    if (upload_task) {
        ADMIN_PROBE_LOG(AL_ERROR, "Failure at client create setup %s %d", upload_task->command_uuid, status);
        status = upload_task->upload_status;
        admin_probe_send_back_result_task_module(upload_task, admin_probe_uploader_failed, ADMIN_PROBE_CLIENT_CREATE_ERROR);
    }
    ADMIN_PROBE_FREE(s3_resp);
    return;
}

static int
admin_probe_uploader_client_create_cb(struct fohh_http_client *client,
                                      enum fohh_http_client_status status,
                                      void *void_cookie,
                                      int64_t int_cookie)
 {
   struct admin_probe_s3_output_resp *s3_resp = ADMIN_PROBE_CALLOC(sizeof(struct admin_probe_s3_output_resp));
   if (s3_resp == NULL) {
       ADMIN_PROBE_LOG(AL_ERROR, "Calloc failure in client create cb");
       return ADMIN_PROBE_RESULT_NO_MEMORY;
   }
    s3_resp->cookie = void_cookie;
    s3_resp->status = status;
    s3_resp->client = client;
    zevent_base_call(admin_probe_state.zbase, admin_probe_thread_uploader_client_create_cb, (void*) s3_resp, 0);
    return ADMIN_PROBE_RESULT_NO_ERROR;
}

static int
admin_probe_s3_upload (admin_probe_uploader_task *upload_task)
{
    char remote_host[1000] = {0};
    int http_timeout_us;
    enum fohh_http_client_status status = fohh_http_client_status_success;
    struct timeval tv;
    int no_proxy = 0;

    if (!upload_task) {
        ADMIN_PROBE_LOG(AL_ERROR, "upload task null in s3 upload");
        stats.admin_probe_uploader_null_upload_task++;
        goto ret_failure;
    }

    if (admin_probe_callbacks.get_remote_host_for_s3_upload_cb) {
        admin_probe_callbacks.get_remote_host_for_s3_upload_cb(&remote_host[0], sizeof(remote_host), &no_proxy);
    }

    upload_task->ssl_ctx = fohh_http_client_get_ssl_ctx();
    if (!upload_task->ssl_ctx) {
        stats.admin_probe_uploader_ssl_ctx++;
        goto ret_failure;
    }

    if (no_proxy) {
        ADMIN_PROBE_LOG(AL_DEBUG, "Connecting directly to %s for :%s", upload_task->bucket_name, upload_task->command_uuid);
    } else {
        ADMIN_PROBE_LOG(AL_DEBUG, "Connecting to %s via %s %s proxy_hostname:%s",upload_task->bucket_name, remote_host, upload_task->command_uuid, fohh_proxy_hostname);
    }

    http_timeout_us = admin_probe_rate_limiting_get_uploader_http_send_timeout_us();
    fohh_http_client_create(admin_probe_state.zcdns_ctx,
                            upload_task->ssl_ctx,
                            upload_task->bucket_name,
                            no_proxy ? upload_task->bucket_name : remote_host,
                            fohh_proxy_hostname,
                            ADMIN_PROBE_UPLOADER_PROXY_PORT,
                            fohh_proxy_port,
                            http_timeout_us,
                            admin_probe_uploader_client_create_cb,
                            upload_task,
                            0);

    if (upload_task->retry_count == 0) {
        upload_task->timeout_timer = event_new(admin_probe_state.ebase, -1, 0, admin_probe_uploader_http_req_timeout_cb,
                                               (void *)upload_task);
        tv.tv_sec = admin_probe_rate_limiting_get_uploader_total_timeout_us();
        tv.tv_usec = 0;
        event_add(upload_task->timeout_timer, &tv);
    }
    admin_probe_uploader_increment_count(admin_probe_uploader_async_client_create);
    return status;
ret_failure:
    if (upload_task) {
        ADMIN_PROBE_LOG(AL_ERROR, "Failure at client create setup %s", upload_task->command_uuid);
        status = upload_task->upload_status;
        SSL_CTX_free(upload_task->ssl_ctx);
    }
    admin_probe_send_back_result_task_module(upload_task, admin_probe_uploader_failed, ADMIN_PROBE_CLIENT_CREATE_ERROR);
    return status;
}



int
admin_probe_upload_file_list (char *bucket_name, char *file_location, struct admin_probe_uploader_file_list_head *head,
                              char* command_uuid, s3_output_cb_f *s3_output_cb, void *cookie)
{

    admin_probe_uploader_task *upload_task = NULL;
    admin_probe_uploader_status status = admin_probe_uploader_success;

    if (!bucket_name || !strcmp(bucket_name, "")) {
        ADMIN_PROBE_LOG(AL_ERROR, "invalid argument for bucket_name in admin_probe_send_to_s3_uploader %s", command_uuid);
        status = admin_probe_uploader_failed;
        stats.admin_probe_uploader_bucket_invalid++;
        goto upload_end;
    }

     if (!file_location || !strcmp(file_location, "")) {
        ADMIN_PROBE_LOG(AL_ERROR, "invalid argument for file_location in admin_probe_send_to_s3_uploader %s", command_uuid);
        status = admin_probe_uploader_failed;
        stats.admin_probe_uploader_file_location_invalid++;
        goto upload_end;
    }

    ADMIN_PROBE_DEBUG_UPLOADER("Sending to bucket token %s %s", bucket_name, command_uuid);

    upload_task = ADMIN_PROBE_CALLOC(sizeof(admin_probe_uploader_task));
    if (upload_task == NULL) {
       ADMIN_PROBE_LOG(AL_ERROR, "Calloc failure for upload context");
       stats.admin_probe_upload_task_mem_fail++;
       goto upload_end;
    }

    upload_task->s3_output_cb = s3_output_cb;
    upload_task->retry_count = 0;
    upload_task->head = head;
    upload_task->cookie = cookie;

    upload_task->command_uuid = ADMIN_PROBE_STRDUP(command_uuid, strlen(command_uuid));
    upload_task->bucket_name = ADMIN_PROBE_STRDUP(bucket_name, strlen(bucket_name));
    upload_task->file_location = ADMIN_PROBE_STRDUP(file_location, strlen(file_location));

    cur_upload_task = upload_task;

    admin_probe_s3_upload(upload_task);

upload_end:
    return status;
}

int
admin_probe_send_to_s3_uploader (char *bucket_name, char *file_location, char *dir_name, char *custom_upload_dir,
                                 char* command_uuid, s3_output_cb_f *s3_output_cb, void *cookie)
{
    struct dirent *dir;
    int num_fds = 0;
    struct admin_probe_uploader_file_list *cur_list;
    char buf[ADMIN_PROBE_UPLOADER_MAX_FILE_NAME];
    DIR *dirp = NULL;
    int status = admin_probe_uploader_success;
    struct admin_probe_uploader_file_list *first, *next;
    struct admin_probe_uploader_file_list_head *file_list_head;

    if (!command_uuid ||  !strcmp(command_uuid, "")) {
        ADMIN_PROBE_LOG(AL_ERROR, "null argument for command_uuid");
        stats.admin_probe_uploader_invalid_command_uuid++;
        goto init_end;
    }

    if (!dir_name || !strcmp(dir_name, "")) {
        ADMIN_PROBE_LOG(AL_ERROR, "null argument for dir name in admin_probe_send_to_s3_uploader %s", command_uuid);
        stats.admin_probe_uploader_invalid_directory_name++;
        goto init_end;
    }

    dirp = opendir(dir_name);
    if (dirp == NULL) {
        ADMIN_PROBE_LOG(AL_ERROR, "Open directory failed for %s", command_uuid);
        stats.admin_probe_uploader_open_directory_fail++;
        goto dir_end;
    }
    file_list_head = ADMIN_PROBE_CALLOC(sizeof(struct admin_probe_uploader_file_list_head));
    if (file_list_head == NULL) {
        ADMIN_PROBE_LOG(AL_ERROR, "list head malloc fail %s", command_uuid);
        stats.admin_probe_uploader_list_head_malloc_fail++;
        goto dir_end;
    }
    ZTAILQ_INIT(file_list_head);

    while ((dir = readdir(dirp)) != NULL) {
        if (!(strcmp(dir->d_name, ".")) || !(strcmp(dir->d_name, ".."))) {
            continue;
        }
        // This is not reusing a calloc'ed pointer. It gets stored in a linked list
        // coverity[RESOURCE_LEAK]
        cur_list = ADMIN_PROBE_CALLOC(sizeof(*cur_list));
        if (cur_list == NULL) {
            ADMIN_PROBE_LOG(AL_ERROR, "cur list malloc fail %s", command_uuid);
            stats.admin_probe_uploader_cur_list_malloc_fail++;
            goto list_node_end;
        }
        cur_list->file_seqno = num_fds;
        if (strstr(dir->d_name, command_uuid)) {
            snprintf(cur_list->file_name, sizeof(cur_list->file_name), "%s/%s", dir_name, dir->d_name);
            ADMIN_PROBE_DEBUG_UPLOADER("Reading file name %s %s", cur_list->file_name, command_uuid);
            // If upload path needs to be different than local path.
            if (custom_upload_dir) {
                cur_list->is_override_default_upload_path = 1;
                snprintf(cur_list->custom_upload_path, sizeof(cur_list->custom_upload_path), "%s/%s",
                 custom_upload_dir, dir->d_name);
                ADMIN_PROBE_DEBUG_UPLOADER("Custom upload path %s %s", cur_list->custom_upload_path, command_uuid);
            }
            ZTAILQ_INSERT_TAIL(file_list_head, cur_list, file_list_entry);
            num_fds++;
        } else {
            ADMIN_PROBE_LOG(AL_NOTICE, "Admin probe deleting stale file %s %s", dir->d_name,
                            command_uuid);
            snprintf(buf, sizeof(buf), "%s/%s", dir_name, dir->d_name);
            stats.admin_probe_uploader_stale_file_fail++;
            unlink(buf);
            ADMIN_PROBE_FREE(cur_list);
        }
    }
    if (dirp) {
        closedir(dirp);
        dirp =  NULL;
    }

    if (!ZTAILQ_EMPTY(file_list_head)) {
        int res = admin_probe_upload_file_list(bucket_name, file_location, file_list_head, command_uuid,
                                               s3_output_cb, cookie);
        if (res != admin_probe_uploader_success) {
            ADMIN_PROBE_LOG(AL_ERROR, "The file uploader setup failed %d %s", res, command_uuid);
            goto list_end;
        }
        return status;
    }
list_end:
    ZTAILQ_FOREACH_SAFE(first, file_list_head, file_list_entry, next) {
        ZTAILQ_REMOVE(file_list_head, first, file_list_entry);
        ADMIN_PROBE_FREE(first);
    }
list_node_end:
    ADMIN_PROBE_FREE(file_list_head);

dir_end:
    if (dirp) {
        closedir(dirp);
    }
init_end:
    status = ADMIN_PROBE_RESULT_BAD_ARGUMENT;
    return status;

}

static int
admin_probe_uploader_module_dump_stats (struct zpath_debug_state*  request_state,
                                        const char**               query_values,
                                        int                        query_value_count,
                                        void*                      cookie)
{
    char jsonout[10000];

    (void)query_values;
    (void)query_value_count;
    (void)cookie;

    if (ARGO_RESULT_NO_ERROR == argo_structure_dump(admin_probe_uploader_stats_desciption,
                                                    &stats, jsonout, sizeof(jsonout), NULL, 1)) {
        ZDP("%s\n", jsonout);
    }
    return ADMIN_PROBE_RESULT_NO_ERROR;
}


static int admin_probe_uploader_dump_current_upload_task(struct zpath_debug_state *request_state,
                                                         const char **query_values,
                                                         int query_value_count,
                                                         void *cookie)
{
    struct admin_probe_uploader_file_list *first, *next;
    if (cur_upload_task == NULL) {
        ZDP("No current upload tasks");
        goto done;
    }
    if (cur_upload_task->command_uuid) {
        ZDP("Command UUID            : %s\n", cur_upload_task->command_uuid);
    }
    if (cur_upload_task->bucket_name) {
        ZDP("S3 bucket name          : %s\n", cur_upload_task->bucket_name);
    }
    if (cur_upload_task->file_location) {
        ZDP("File location           : %s\n", cur_upload_task->file_location);
    }

    ZDP("File uploaded count     : %"PRId32"\n", cur_upload_task->file_uploaded_cnt);

    ZDP("Retry count             : %"PRId32"\n", cur_upload_task->retry_count);

    if (cur_upload_task->upload_error_message) {
        ZDP("Error message       : %s\n", cur_upload_task->upload_error_message);
    }

    ZDP("Upload status           : %"PRId32"\n", cur_upload_task->upload_status);

    ZDP("Http status             : %"PRId32"\n", cur_upload_task->http_status);
    if (!ZTAILQ_EMPTY(cur_upload_task->head)) {
        ZTAILQ_FOREACH_SAFE(first, cur_upload_task->head, file_list_entry, next) {
            if (first != NULL) {
                ZDP("File name            : %s\n", first->file_name);
            }
        }
    }
done:
    return ADMIN_PROBE_RESULT_NO_ERROR;
}

int admin_probe_uploader_module_init ()
{
    int res;

    if (!(admin_probe_uploader_stats_desciption = argo_register_global_structure(ADMIN_PROBE_UPLOADER_STATS_HELPER))) {
        return ADMIN_PROBE_RESULT_ERR;
    }

    res = zpath_debug_add_read_command("dump the stats of admin_probe_uploader_module",
                                  "/admin_probe/uploader_module_stats",
                                  admin_probe_uploader_module_dump_stats,
                                  NULL,
                                  NULL);
    if (res){
        return ADMIN_PROBE_RESULT_ERR;
    }

    res = zpath_debug_add_read_command("Dump the current upload task",
                                  "/admin_probe/uploader_module/current_upload_task",
                                  admin_probe_uploader_dump_current_upload_task, NULL, NULL);
    if (res) {
        return ADMIN_PROBE_RESULT_ERR;
    }

    return ADMIN_PROBE_RESULT_NO_ERROR;
}
