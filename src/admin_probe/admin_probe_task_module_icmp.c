/*
 * admin_probe_task_module_icmp.c. Copyright (C) 2021 Zscaler Inc, All Rights Reserved
 *
 */

#include "zpath_lib/zpath_debug.h"

#include "admin_probe/admin_probe_task_module_icmp.h"
#include "admin_probe/admin_probe_task_module_common.h"
#include "zhealth/zhealth_probe_lib.h"
#include "admin_probe/admin_probe_lib.h"
#include "admin_probe/admin_probe_private.h"
#include "zcdns/zcdns.h"
#include "argo/argo.h"
#include "admin_probe/admin_probe_rate_limiting.h"

#include <sys/types.h>    /* XXX temporary hack to get u_ types */
#include <arpa/inet.h>
#include <netinet/in.h>
#include <netinet/ip.h>
#include <netinet/ip_icmp.h>
#include <inttypes.h>
#include <openssl/rand.h>
#include "parson/parson.h"

static
struct admin_probe_task_module_icmp_stats {                                /* _ARGO: * object_definition */
    int64_t number_of_icmp_probe_request;                                  /* _ARGO: integer */
    int64_t number_of_icmp_probe_success;                                  /* _ARGO: integer */
    int64_t number_of_icmp_probe_failed;                                   /* _ARGO: integer */
    int64_t number_of_icmp_probe_cancelled;                                /* _ARGO: integer */
    int64_t number_of_icmp_probe_timeout;                                  /* _ARGO: integer */
    int64_t number_of_request_domain_allocated;                            /* _ARGO: integer */
    int64_t number_of_request_domain_freed;                                /* _ARGO: integer */
    int64_t number_of_icmpinfo_probe_info_created;                         /* _ARGO: integer */
    int64_t number_of_icmpinfo_probe_info_freed;                           /* _ARGO: integer */
    int64_t number_of_icmpinfo_allocated;                                  /* _ARGO: integer */
    int64_t number_of_icmpinfo_freed;                                      /* _ARGO: integer */
    int64_t number_of_icmpinfo_response_allocated;                         /* _ARGO: integer */
    int64_t number_of_icmpinfo_response_freed;                             /* _ARGO: integer */
    int64_t number_of_icmpinfo_payload_allocated;                          /* _ARGO: integer */
    int64_t number_of_icmpinfo_payload_freed;                              /* _ARGO: integer */
    int64_t number_of_icmpinfo_arr_allocated;                              /* _ARGO: integer */
    int64_t number_of_icmpinfo_arr_freed;                                  /* _ARGO: integer */

    int64_t number_of_tasks_expire_in_processing_state;                    /* _ARGO: integer */
    int64_t rate_limit_exceed;                                             /* _ARGO: integer */
    int64_t number_of_target_has_only_ipv6;                                /* _ARGO: integer */

} stats;

#include "admin_probe/admin_probe_task_module_icmp_compiled_c.h"
static struct argo_structure_description*  admin_probe_task_module_icmp_stats_desciption;

static struct {
    int64_t total_icmp_in_exec;
} state;

void admin_probe_task_module_icmp_free_icmpinfo(struct zhealth_icmp_info *icmpinfo)
{
    if (!icmpinfo) {
        return;
    }

    if (icmpinfo->response) {
        ADMIN_PROBE_FREE(icmpinfo->response);
        icmpinfo->response = NULL;
        stats.number_of_icmpinfo_response_freed++;
    }

    if (icmpinfo->icmp_payload) {
        ADMIN_PROBE_FREE(icmpinfo->icmp_payload);
        icmpinfo->icmp_payload = NULL;
        stats.number_of_icmpinfo_payload_freed++;
    }

    ADMIN_PROBE_FREE(icmpinfo);
    stats.number_of_icmpinfo_freed++;
}


void admin_probe_task_module_icmp_free_icmp_task_state_f(struct admin_probe_task *cur_task)
{
    if (!cur_task) {
        return;
    }

    struct icmp_task *icmp_task_state = &(cur_task->task_state.icmp_task_state);

    if (!icmp_task_state) {
        return;
    }

    if (icmp_task_state->request_domain) {
        ADMIN_PROBE_FREE(icmp_task_state->request_domain);
        icmp_task_state->request_domain = NULL;
        stats.number_of_request_domain_freed++;
    }

    for (int i = 0; i < icmp_task_state->icmpinfo_arr_count; i++) {
        struct zhealth_icmp_info *cur_icmpinfo = icmp_task_state->icmpinfo_arr[i];

        zhealth_probe_lib_probe_info_shutdown_probe(&(cur_icmpinfo->probe_info));
        zhealth_probe_lib_close_zhealth_owned_socket_async(&(cur_icmpinfo->probe_info));
        stats.number_of_icmpinfo_probe_info_freed++;

        admin_probe_task_module_icmp_free_icmpinfo(cur_icmpinfo);
    }

    ADMIN_PROBE_FREE(icmp_task_state->icmpinfo_arr);
    stats.number_of_icmpinfo_arr_freed++;
}

int admin_probe_task_module_icmp_cancel_f(struct admin_probe_task *cur_task) {
     cur_task->end_time_cloud_s = admin_probe_callbacks.get_current_cloud_epoch_s_cb();
    return ADMIN_PROBE_RESULT_NO_ERROR;
}

JSON_Value* admin_probe_task_module_icmp_add_stats_json(struct icmp_task *icmp_probe_info)
{
    if (!icmp_probe_info) {
        ADMIN_PROBE_LOG(AL_ERROR, "no icmp_probe_info, can not proceed further formating statictic in json form");
        return NULL;
    }

    JSON_Value *stats_val = json_value_init_object();
    JSON_Object *stats_obj = json_value_get_object(stats_val);

    json_object_set_number(stats_obj, "probes_transmitted", icmp_probe_info->statistics.probes_transmitted);
    json_object_set_number(stats_obj, "probes_received", icmp_probe_info->statistics.probes_received);
    json_object_set_number(stats_obj, "probes_loss_percentage", icmp_probe_info->statistics.probes_loss_percent);

    if (icmp_probe_info->statistics.probes_loss_percent == 100) {
        json_object_set_string(stats_obj, "min_rtt_us", "Unavailable");
        json_object_set_string(stats_obj, "avg_rtt_us", "Unavailable");
        json_object_set_string(stats_obj, "max_rtt_us", "Unavailable");
    } else {
        json_object_set_number(stats_obj, "min_rtt_us", icmp_probe_info->statistics.min_rtt_us);
        json_object_set_number(stats_obj, "avg_rtt_us", icmp_probe_info->statistics.avg_rtt_us);
        json_object_set_number(stats_obj, "max_rtt_us", icmp_probe_info->statistics.max_rtt_us);
    }

    return stats_val;
}


/*
 * {
 *   "action": "ICMP_PING",
 *   "target": "google.com",
 *   "ip": "**************",
 *   "statistic": {
 *       "probes_transmitted": 3,
 *       "probes_received": 3,
 *       "packet_loss_percentage": 0,
 *       "max_rtt_us": 18330,
 *       "min_rtt_us": 18185,
 *       "avg_rtt_us": 18243.666667
 *   }
 * }
 */
static char* admin_probe_task_module_icmp_report_in_json(char *command_uuid, char *action, char *target, struct icmp_task *icmp_probe_info, char *ip_str)
{
    if (!action) {
        ADMIN_PROBE_LOG(AL_ERROR, "no action, can not proceed further formating result in json form");
        return NULL;
    }

    if (!target) {
        ADMIN_PROBE_LOG(AL_ERROR, "no target, can not proceed further formating result in json form");
        return NULL;
    }

    if (!icmp_probe_info) {
        ADMIN_PROBE_LOG(AL_ERROR, "no icmp_probe_info, can not proceed further formating result in json form");
        return NULL;
    }

    if (!ip_str) {
        ADMIN_PROBE_LOG(AL_ERROR, "no ip_str, can not proceed further formating result in json form");
        return NULL;
    }

    char *buf;

    JSON_Value *report_val = json_value_init_object();
    JSON_Object *report_obj = json_value_get_object(report_val);

    JSON_Value *statistic_val;

    statistic_val = admin_probe_task_module_icmp_add_stats_json(icmp_probe_info);
    if (!statistic_val) {
        json_value_free(report_val);
        return NULL;
    }

    char entity_id_str[ADMIN_PROBE_MAX_ENTITY_ID];

    snprintf(entity_id_str, sizeof(entity_id_str), "%"PRId64"", admin_probe_state.entity_gid);

    json_object_set_string(report_obj, "action", action);
    json_object_set_string(report_obj, "target", target);
    json_object_set_string(report_obj, "entity_name", admin_probe_state.entity_name);
    json_object_set_string(report_obj, "entity_gid", entity_id_str);
    json_object_set_string(report_obj, "ip", ip_str);
    json_object_set_value(report_obj, "statistic", statistic_val);

    buf = json_serialize_to_string_pretty(report_val);

    ADMIN_PROBE_DEBUG_TASK_MODULE_ICMP("%s : icmp probe result (JSON) :\n %s", command_uuid?command_uuid:" ", buf);

    json_value_free(report_val);

    return buf;
}

/*
 * ICMP_PING : google.com
 * ip : **************
 * 3 probes transmitted, 3 received, 0% probes loss
 * round-trip min/avg/max = 18330 /18185 /18243.666667 us
 */

#define MAX_ICMP_RESPONSE_TXT_LEN 556
static char* admin_probe_task_module_icmp_report_in_txt(char* command_uuid, char *action, char *target, struct icmp_task *icmp_probe_info, char *ip_str)
{
    if (!action) {
        ADMIN_PROBE_LOG(AL_ERROR, "no action, can not proceed further formating result in txt form");
        return NULL;
    }

    if (!target) {
        ADMIN_PROBE_LOG(AL_ERROR, "no target, can not proceed further formating result in txt form");
        return NULL;
    }

    if (!icmp_probe_info) {
        ADMIN_PROBE_LOG(AL_ERROR, "no icmp_probe_info, can not proceed further formating result in txt form");
        return NULL;
    }

    if (!ip_str) {
        ADMIN_PROBE_LOG(AL_ERROR, "no ip_str, can not proceed further formating result in txt form");
        return NULL;
    }

    char *buf = ADMIN_PROBE_MALLOC(MAX_ICMP_RESPONSE_TXT_LEN);
    char *s = buf;
    char *e = s + MAX_ICMP_RESPONSE_TXT_LEN;

    s += sxprintf(s, e, "%s : %s\n", action, target);
    s += sxprintf(s, e, "entity_name : %s\n",admin_probe_state.entity_name);
    s += sxprintf(s, e, "entity_gid : %"PRId64"\n",admin_probe_state.entity_gid);
    s += sxprintf(s, e, "ip : %s\n", ip_str);

    s += sxprintf(s, e, "%d probes transmitted, %d received, %d%c probes loss \n", icmp_probe_info->statistics.probes_transmitted,
                                                                                    icmp_probe_info->statistics.probes_received,
                                                                                    icmp_probe_info->statistics.probes_loss_percent,
                                                                                    '%');

    if (icmp_probe_info->statistics.probes_loss_percent == 100) {
        s += sxprintf(s, e, "round-trip min/avg/max = %s", "Unavailable");
    } else {
        s += sxprintf(s, e, "round-trip min/avg/max = %"PRId64" /%f /%"PRId64" µs", icmp_probe_info->statistics.min_rtt_us,
                                                                                    icmp_probe_info->statistics.avg_rtt_us,
                                                                                    icmp_probe_info->statistics.max_rtt_us);
    }

    ADMIN_PROBE_DEBUG_TASK_MODULE_ICMP("%s : icmp probe result (txt) :\n %s", command_uuid?command_uuid:" ", buf);

    return buf;

}


static void admin_probe_task_module_icmp_format_probe_result_report(struct admin_probe_task *cur_task)
{
    if (!cur_task) {
        ADMIN_PROBE_LOG(AL_ERROR, "no icmpinfo, can not proceed with formatting icmp probe result");
        return;
    }

    cur_task->result_txt = admin_probe_task_module_icmp_report_in_txt(cur_task->command_uuid, cur_task->action, cur_task->target, &(cur_task->task_state.icmp_task_state), cur_task->common_state.dst_ip);
    cur_task->result_json = admin_probe_task_module_icmp_report_in_json(cur_task->command_uuid, cur_task->action, cur_task->target, &(cur_task->task_state.icmp_task_state), cur_task->common_state.dst_ip);
}

static void admin_probe_task_module_icmp_format_probe_err_result_report(struct admin_probe_task *cur_task)
{
    if (!cur_task) {
        ADMIN_PROBE_LOG(AL_ERROR, "no icmpinfo, can not proceed with formatting icmp err probe result");
        return;
    }

    cur_task->err_message = admin_probe_task_module_icmp_report_in_txt(cur_task->command_uuid, cur_task->action, cur_task->target, &(cur_task->task_state.icmp_task_state), cur_task->common_state.dst_ip);
}

static void admin_probe_task_module_icmp_task_done_completely(struct admin_probe_task *cur_task)
{
    if (!cur_task) {
        ADMIN_PROBE_LOG(AL_ERROR, "no cur_task, task gone, can not proceed to process icmp probe result");
        return;
    }

    cur_task->end_time_cloud_s = admin_probe_callbacks.get_current_cloud_epoch_s_cb();

    if (cur_task->cleanup_criteria.is_execute_callback_outstanding) {
        cur_task->cleanup_criteria.is_execute_callback_outstanding = 0;
    }

    if (cur_task->task_state.icmp_task_state.statistics.probes_loss_percent == 100) {
        admin_probe_task_module_icmp_format_probe_err_result_report(cur_task);
        stats.number_of_icmp_probe_failed++;

        if (!cur_task->err_message) {
            cur_task->err_message = ADMIN_PROBE_COMMAND_FAIL_NO_REPORT;
        }

        admin_probe_task_module_probe_process_event(NULL, cur_task->is_np_command_probe, cur_task, cur_task->command_uuid, internal_task_execute_done_failed);
        return;
    } else {
        admin_probe_task_module_icmp_format_probe_result_report(cur_task);
        if (!cur_task->result_json && !cur_task->result_txt) {
            cur_task->err_message = ADMIN_PROBE_COMMAND_SUCCESS_BUT_FAIL_TO_GEN_REPORT;
            stats.number_of_icmp_probe_failed++;

            admin_probe_task_module_probe_process_event(NULL, cur_task->is_np_command_probe, cur_task, cur_task->command_uuid, internal_task_execute_done_failed);

            return;
        } else {
            stats.number_of_icmp_probe_success++;
            admin_probe_task_module_probe_process_event(NULL, cur_task->is_np_command_probe, cur_task, cur_task->command_uuid, internal_task_execute_done_success);

            return;
        }
    }

}


static void admin_probe_task_module_icmp_update_icmp_response_stats(struct zhealth_icmp_info *icmpinfo,
                                                               struct admin_probe_task *cur_task,
                                                               int icmp_response_type)
{
    if (!icmpinfo) {
        ADMIN_PROBE_LOG(AL_ERROR, "no icmpinfo - admin_probe_task_module_icmp_update_icmp_response_stats");
        return;
    }

    if (!cur_task) {
        ADMIN_PROBE_LOG(AL_ERROR, "no cur_task - admin_probe_task_module_icmp_update_icmp_response_stats");
        return;
    }

    if (icmp_response_type != ICMP_ECHOREPLY) {
        ADMIN_PROBE_DEBUG_TASK_MODULE_ICMP("%s: rx one icmp response but not echo reply", cur_task->command_uuid? cur_task->command_uuid: "");
        return;
    } else {
        cur_task->task_state.icmp_task_state.statistics.probes_received++;
    }

    int64_t rtt_us;
    //int idx = icmpinfo->state.index;

    //rtt_us = cur_task->icmp_task_state.icmpinfo_arr[idx]->ix.rtt_us;
    rtt_us = icmpinfo->ix.rtt_us;

    if (rtt_us > cur_task->task_state.icmp_task_state.statistics.max_rtt_us) {
        cur_task->task_state.icmp_task_state.statistics.max_rtt_us = rtt_us;
    }

    if (!cur_task->task_state.icmp_task_state.statistics.min_rtt_us) {
        cur_task->task_state.icmp_task_state.statistics.min_rtt_us = rtt_us;
    }

    if (rtt_us < cur_task->task_state.icmp_task_state.statistics.min_rtt_us) {
        cur_task->task_state.icmp_task_state.statistics.min_rtt_us = rtt_us;
    }

    cur_task->task_state.icmp_task_state.statistics.total_rx_rtt_us += rtt_us;

    cur_task->task_state.icmp_task_state.statistics.avg_rtt_us = (double)(cur_task->task_state.icmp_task_state.statistics.total_rx_rtt_us)/cur_task->task_state.icmp_task_state.statistics.probes_received;
}


static void admin_probe_task_module_icmp_update_icmp_final_packets_stats(struct admin_probe_task *cur_task)
{
    if (!cur_task) {
        ADMIN_PROBE_LOG(AL_ERROR, "no cur_task, can not to update final packets icmp stats");
        return;
    }

    int packet_loss;

    packet_loss = cur_task->task_state.icmp_task_state.statistics.probes_transmitted - cur_task->task_state.icmp_task_state.statistics.probes_received;

    cur_task->task_state.icmp_task_state.statistics.packet_loss = packet_loss;
    cur_task->task_state.icmp_task_state.statistics.probes_loss_percent = (packet_loss / cur_task->task_state.icmp_task_state.statistics.probes_transmitted) * 100;
}

static void admin_probe_task_module_icmp_callback_internal(struct zhealth_icmp_info *icmpinfo,
                                                           struct admin_probe_task *cur_task,
                                                           int icmp_response_type,
                                                           int is_failure_cb)
{
    if (!icmpinfo) {
        ADMIN_PROBE_LOG(AL_ERROR, "no icmpinfo in internal icmp callback");
        return;
    }

    if (!cur_task) {
        ADMIN_PROBE_LOG(AL_ERROR, "no cur_task in internal icmp callback");
        return;
    }

    ADMIN_PROBE_DEBUG_TASK_MODULE_ICMP("%s : ICMP callback internal", cur_task->command_uuid?cur_task->command_uuid:"");

    cur_task->task_state.icmp_task_state.icmp_cb_done++;

    if (!is_failure_cb) {
        cur_task->task_state.icmp_task_state.icmp_cb_done_response++;
        admin_probe_task_module_icmp_update_icmp_response_stats(icmpinfo, cur_task, icmp_response_type);
    } else {
        cur_task->task_state.icmp_task_state.icmp_cb_done_failure++;
    }

    if (cur_task->task_state.icmp_task_state.icmp_cb_done == cur_task->task_state.icmp_task_state.statistics.probes_transmitted) {
        admin_probe_task_module_icmp_update_icmp_final_packets_stats(cur_task);
        admin_probe_task_module_icmp_task_done_completely(cur_task);
        state.total_icmp_in_exec--;
        return;
    }

    return;

}

void admin_probe_task_module_icmp_response_admin_probe_thread_cb(struct zevent_base *base,
                                                                 void *void_cookie,
                                                                 int64_t int_cookie)
{
    struct zhealth_icmp_info *icmpinfo = (struct zhealth_icmp_info *) void_cookie;
    int icmp_response_type = int_cookie;

    struct admin_probe_task * cur_task;
    cur_task = (struct admin_probe_task *) icmpinfo->response_cookie1;

    if (!cur_task) {
        ADMIN_PROBE_LOG(AL_ERROR, "ICMP resposne admin probe thread callback without cookie");
        return;
    }

    ADMIN_PROBE_DEBUG_TASK_MODULE_ICMP("%s : icmp response admin probe thread callback", cur_task->command_uuid?cur_task->command_uuid:"");

    admin_probe_task_module_icmp_callback_internal(icmpinfo, cur_task, icmp_response_type, 0);

}

int admin_probe_task_module_icmp_response_zhealth_probe_thread_cb(struct zhealth_icmp_info *icmpinfo, int icmp_response_type, int icmp_response_code)
{
    if (!icmpinfo) {
        ADMIN_PROBE_LOG(AL_ERROR, "no icmpinfo in response callback");
    }

    if (0 != zevent_base_call(admin_probe_state.zbase, admin_probe_task_module_icmp_response_admin_probe_thread_cb, icmpinfo, icmp_response_type)) {
        /*
         * DO NOTHING
         *
         * we got a probe callback from health thread, but somehow we can not make it to admin_probe thread.
         * its okay we do nothing there as each probe that admin_probe created will have timeout value
         */
    }

    return ADMIN_PROBE_RESULT_NO_ERROR;
}

void admin_probe_task_module_icmp_failure_admin_probe_thread_cb(struct zevent_base *base,
                                                                void *void_cookie,
                                                                int64_t int_cookie)
{
    struct zhealth_icmp_info *icmpinfo = (struct zhealth_icmp_info *) void_cookie;

    struct admin_probe_task * cur_task;
    cur_task = (struct admin_probe_task *) icmpinfo->response_cookie1;

    if (!cur_task) {
        ADMIN_PROBE_LOG(AL_ERROR, "ICMP failure admin probe thread callback without cookie");
        return;
    }

    ADMIN_PROBE_DEBUG_TASK_MODULE_ICMP("%s : icmp failure admin probe thread callback", cur_task->command_uuid?cur_task->command_uuid:"");

    admin_probe_task_module_icmp_callback_internal(icmpinfo, cur_task, 0, 1);
}


int admin_probe_task_module_icmp_failure_zhealth_probe_thread_cb(struct zhealth_icmp_info *icmpinfo, int64_t pending_us)
{
    if (!icmpinfo) {
        ADMIN_PROBE_LOG(AL_ERROR, "no icmpinfo in failure callback");
        return ADMIN_PROBE_RESULT_NO_ERROR;
    }


    if (0 != zevent_base_call(admin_probe_state.zbase, admin_probe_task_module_icmp_failure_admin_probe_thread_cb, icmpinfo, 0)) {
        /*
         * DO NOTHING
         *
         * we got a probe callback from health thread, but somehow we can not make it to admin_probe thread.
         * its okay we do nothing there as each probe that admin_probe created will have timeout value
         *
         */
    }

    return ADMIN_PROBE_RESULT_NO_ERROR;
}

/*
 * cur_task state : PENDING
 *
 * return:
 *      ADMIN_PROBE_RESULT_NO_ERROR -
 *                      met rate limit criteria, caller can proceed to exec the task
 *      ADMIN_PROBE_TASK_LIMIT_REACHED -
 *                      caller should wait till next time to exec the task
 *
 */
static int admin_probe_task_module_icmp_exec_icmp_task_check_rate_limit(struct admin_probe_task *cur_task)
{
    ADMIN_PROBE_ASSERT_HARD((cur_task!=NULL), "cur_task is NULL when checking rate limit to exec icmp task");

    int64_t icmp_limit_exec = admin_probe_rate_limiting_get_concurrent_executing_limit_per_task_type(admin_probe_task_type_icmp);
    ADMIN_PROBE_DEBUG_TASK_MODULE_ICMP("threshold check - 's limit is currently at %"PRId64" ", icmp_limit_exec);

    if (state.total_icmp_in_exec >= icmp_limit_exec) {
        stats.rate_limit_exceed++;
        return ADMIN_PROBE_TASK_LIMIT_REACHED;
    }

    return ADMIN_PROBE_RESULT_NO_ERROR;
}



#define ADMIN_PROBE_TASK_MODULE_ICMP_RESPONSE_LEN 140 /* Outer IP:60 + Outer ICMP:8 + Innert IP:60 + Echo Data:8 Considering IP options in req/resp */
#define ADMIN_PROBE_TASK_MODULE_ICMP_REQUEST_PACKET_SIZE_BYTES 64
#define ADMIN_PROBE_TASK_MODULE_ICMP_REQUEST_MAX_TTL 255
static int admin_probe_task_module_icmp_exec_icmp_task_now(struct admin_probe_task *cur_task)
{

    if (!cur_task) {
        ADMIN_PROBE_LOG(AL_ERROR, "cur_task is NULL, can not exec icmp probe now");
        return ADMIN_PROBE_RESULT_ERR;
    }

    cur_task->task_state.icmp_task_state.icmpinfo_arr_count = (int) admin_probe_rate_limiting_get_icmp_probes_per_icmp_ping_command();
    cur_task->task_state.icmp_task_state.icmpinfo_arr = (struct zhealth_icmp_info **)ADMIN_PROBE_MALLOC(sizeof(struct zhealth_icmp_info *) * cur_task->task_state.icmp_task_state.icmpinfo_arr_count);
    if (!cur_task->task_state.icmp_task_state.icmpinfo_arr) {
        cur_task->task_state.icmp_task_state.state.is_exec_failed = 1;
        cur_task->task_state.icmp_task_state.icmpinfo_arr_count = 0;
        return ADMIN_PROBE_RESULT_ERR;
    }
    stats.number_of_icmpinfo_arr_allocated++;

    for (int i = 0; i < cur_task->task_state.icmp_task_state.icmpinfo_arr_count; i++) {
        struct zhealth_icmp_info *icmpinfo = (struct zhealth_icmp_info *) ADMIN_PROBE_CALLOC(sizeof(struct zhealth_icmp_info));
        if (!icmpinfo) {
            ADMIN_PROBE_LOG(AL_ERROR, "%s: trying to alloc %d icmpinfo, failed alloc icmpinfo at index %d", cur_task->command_uuid?cur_task->command_uuid: " ",
                                                                                                            cur_task->task_state.icmp_task_state.icmpinfo_arr_count,
                                                                                                            i);
            cur_task->task_state.icmp_task_state.state.is_exec_failed = (i == 0) ? 1: 0;
            cur_task->task_state.icmp_task_state.icmpinfo_arr_count = i;
            break;
        }
        stats.number_of_icmpinfo_allocated++;

        cur_task->task_state.icmp_task_state.icmpinfo_arr[i] = icmpinfo;

        snprintf(icmpinfo->probe_info.dest_ip, sizeof(icmpinfo->probe_info.dest_ip), "%s", cur_task->common_state.dst_ip);
        icmpinfo->response_cb = admin_probe_task_module_icmp_response_zhealth_probe_thread_cb;
        icmpinfo->failure_cb = admin_probe_task_module_icmp_failure_zhealth_probe_thread_cb;
        icmpinfo->probe_info.max_ttl = ADMIN_PROBE_TASK_MODULE_ICMP_REQUEST_MAX_TTL;
        icmpinfo->response_cookie1 = cur_task;

        icmpinfo->response_len = ADMIN_PROBE_TASK_MODULE_ICMP_RESPONSE_LEN;
        icmpinfo->response = ADMIN_PROBE_CALLOC(icmpinfo->response_len);
        if (!icmpinfo->response) {
            admin_probe_task_module_icmp_free_icmpinfo(icmpinfo);
            cur_task->task_state.icmp_task_state.state.is_exec_failed = (i == 0) ? 1: 0;
            cur_task->task_state.icmp_task_state.icmpinfo_arr_count = i;
            break;
        }
        stats.number_of_icmpinfo_response_allocated++;

        icmpinfo->icmp_payload_len = ADMIN_PROBE_TASK_MODULE_ICMP_REQUEST_PACKET_SIZE_BYTES;
        icmpinfo->icmp_payload = (uint8_t *) ADMIN_PROBE_CALLOC(icmpinfo->icmp_payload_len);
        if (!icmpinfo->icmp_payload) {
            admin_probe_task_module_icmp_free_icmpinfo(icmpinfo);
            cur_task->task_state.icmp_task_state.state.is_exec_failed = (i == 0) ? 1: 0;
            cur_task->task_state.icmp_task_state.icmpinfo_arr_count = i;
            break;
        }
        stats.number_of_icmpinfo_payload_allocated++;

        /* RAND_bytes() returns 1 on success, 0 otherwise */
        int ret = RAND_bytes(icmpinfo->icmp_payload, ADMIN_PROBE_TASK_MODULE_ICMP_REQUEST_PACKET_SIZE_BYTES);

        if (!ret || (zhealth_icmp_gen_seqid(icmpinfo) != ADMIN_PROBE_RESULT_NO_ERROR)) {
            admin_probe_task_module_icmp_free_icmpinfo(icmpinfo);
            cur_task->task_state.icmp_task_state.state.is_exec_failed = (i == 0) ? 1: 0;
            cur_task->task_state.icmp_task_state.icmpinfo_arr_count = i;
            break;
        }

        /*handed off icmpinfo struct to zhealth thread, from now on, we dont need to modify this structure(icmpinfo) any more*/
        zhealth_send_icmp_probe(icmpinfo);
        stats.number_of_icmpinfo_probe_info_created++;

        cur_task->task_state.icmp_task_state.statistics.probes_transmitted++;

    }

    ADMIN_PROBE_ASSERT_HARD((cur_task->task_state.icmp_task_state.statistics.probes_transmitted == cur_task->task_state.icmp_task_state.icmpinfo_arr_count),
                             "probes_transmitted not matched icmpinfo_arr_count");

    if (cur_task->task_state.icmp_task_state.state.is_exec_failed) {
        return ADMIN_PROBE_RESULT_ERR;
    }

    cur_task->task_state.icmp_task_state.icmp_probes_sent_time_s = epoch_s();

    /*when we reach here, we have sent out AT LEAST one icmp probe request*/
    cur_task->cleanup_criteria.is_execute_callback_outstanding = 1;

    return ADMIN_PROBE_RESULT_NO_ERROR;

}

/*
 * task module state module check each task every 1 second (almost),
 * current state: PENDING
 * next state: PENDING (stay) / PROCESSING / CANCELLED
 *
 * for a PENDING status icmp probe, we will PROCESS the task only if following criteria met:
 *
 *  1. IP is ready
 *  2. rate limit met
 *
 * return
 *     - ADMIN_PROBE_RESULT_NO_ERROR : we successfully spin up the task,
 *                                     task module will move the task to PROCESSING state
 *
 *     - ADMIN_PROBE_RESULT_ERR : tried exec but failed, will have retry counter incremented, if exceeded retry limit(default 3 times),
 *                                task module will CANCEL this task.
 *
 *     - ADMIN_PROBE_RESULT_BAD_STATE : can not spin up this task never, task module will move this task to CANCEL state.
 *
 *     - ADMIN_PROBE_TASK_NOT_READY_TO_EXEC : not yet ready to spin up this task, this task will stay in PENDING state and come back to exec later.
 *
 *     - ADMIN_PROBE_TASK_LIMIT_REACHED : not yet ready to spin up this task, this task will stay in PENDING state and come back to exec later.
 */
int admin_probe_task_module_icmp_process_icmp_task_f(struct admin_probe_task *cur_task)
{

    if (!cur_task) {
        ADMIN_PROBE_LOG(AL_ERROR, "reaching admin_probe_task_module_icmp_process_icmp_task_f but no task");
        return ADMIN_PROBE_RESULT_ERR;
    }

    int res;

    /*check if we are ok to exec this task*/

    /*check 1 : if we have an ip for icmp probe*/
    res = admin_probe_task_module_common_check_is_ip_info_is_ready_before_executing_task(cur_task);
    if (res) {
        if (!cur_task->common_state.is_ipv4_addr_returned && !cur_task->common_state.state.is_dns_request_expired) {
            stats.number_of_target_has_only_ipv6++;
            cur_task->err_message = ADMIN_PROBE_COMMAND_ICMP6_NOT_SUPPORTED;
        }
        return res;
    }

    /*check 2: rate limit criteria*/
    res = admin_probe_task_module_icmp_exec_icmp_task_check_rate_limit(cur_task);
    if (res) {
        return res;
    }

    /*proceed to send ICMP probe*/
    res = admin_probe_task_module_icmp_exec_icmp_task_now(cur_task);
    if (res == ADMIN_PROBE_RESULT_NO_ERROR) {
        state.total_icmp_in_exec++;
    }

    return res;
}

/*
 * for the icmp probe request we send using zhealth_send_icmp_probe, we are gurantee to get a callback.
 * either the response callback where we get a response or the failure callback where the request got timeout and the zhealth timer timed it out.
 * we are gurantee to get a callback from zhealth probe library in 30 seconds.
 *
 * but the callback happens on zhealth thread and we are redirecting the callback to admin_probe thread.
 * if something goes wrong there, there is a chance that admin_probe thread misses the callback.
 * thus adding an expiry timer at admin_probe layer to prevent above scenario.
 *
 *
 */
int admin_probe_task_module_icmp_check_expiry_in_processing_state_f(struct admin_probe_task *cur_task)
{
    if (!cur_task) {
        ADMIN_PROBE_LOG(AL_ERROR, "reaching admin_probe_task_module_icmp_check_expiry_in_processing_state_f but no task");
        return ADMIN_PROBE_RESULT_ERR;
    }

    int64_t cur_local_time_s = epoch_s();
    int64_t tcp_ping_expiry_in_processing_s = admin_probe_rate_limiting_get_icmp_ping_expiry_in_processing_state_s();

    if (cur_local_time_s - cur_task->task_state.icmp_task_state.icmp_probes_sent_time_s > tcp_ping_expiry_in_processing_s) {
        cur_task->cleanup_criteria.is_execute_callback_outstanding = 0;
        cur_task->err_message = ADMIN_PROBE_COMMAND_FAIL_IN_GENERAL;
        state.total_icmp_in_exec--;
        stats.number_of_tasks_expire_in_processing_state++;
        stats.number_of_icmp_probe_timeout++;
        return ADMIN_PROBE_RESULT_ERR;
    }

    return ADMIN_PROBE_RESULT_NO_ERROR;
}



int admin_probe_task_module_icmp_init_icmp_request_f(struct admin_probe_task *cur_task)
{
    if (!cur_task) {
        ADMIN_PROBE_LOG(AL_ERROR, "invalid cur_task, can not parse icmp admin probe request");
        return ADMIN_PROBE_RESULT_ERR;
    }

    if (!cur_task->command_uuid) {
        ADMIN_PROBE_LOG(AL_ERROR, "invalid command_uuid, can not parse icmp admin probe request");
        return ADMIN_PROBE_RESULT_ERR;
    }

    if (!cur_task->target) {
        ADMIN_PROBE_LOG(AL_ERROR, "invalid target, can not parse icmp admin probe request");
        return ADMIN_PROBE_RESULT_ERR;
    }

    stats.number_of_icmp_probe_request++;

    cur_task->common_state.is_ipv4 = 1;
    cur_task->common_state.is_ipv6 = 0; //icmp6 is not supported in connector yet

    int res;

    res = zhealth_probe_lib_is_icmp_state_initialized();
    if (0 == res) {
        ADMIN_PROBE_LOG(AL_DEBUG, "%s : icmp state is not initialized, cancelling icmp probe request", cur_task->command_uuid);
        return ADMIN_PROBE_RESULT_ERR;
    }

    cur_task->task_state.icmp_task_state.request_domain = ADMIN_PROBE_STRDUP(cur_task->target, strlen(cur_task->target));
    if (!cur_task->task_state.icmp_task_state.request_domain) {
        goto task_cancel;
    }

    stats.number_of_request_domain_allocated++;

    res = admin_probe_task_module_common_fill_ip_info_if_already_presented_in_request(cur_task->task_state.icmp_task_state.request_domain, cur_task);
    if (res) {
        goto task_cancel;
    }

    if (!cur_task->common_state.state.is_ip_info_ready) {
        res = admin_probe_task_module_common_resolve_domain_to_ip(cur_task->task_state.icmp_task_state.request_domain, cur_task);
        if (res) {
            stats.number_of_icmp_probe_cancelled++;
            ADMIN_PROBE_LOG(AL_ERROR, "%s: can not resolve for an ip for icmp target %s", cur_task->command_uuid? cur_task->command_uuid: " ",
                                                                                          cur_task->target);
            return ADMIN_PROBE_RESULT_ERR;
        }
    }

    return ADMIN_PROBE_RESULT_NO_ERROR;

task_cancel:
    cur_task->err_message = ADMIN_PROBE_COMMAND_CANCELLED_AT_INIT;
    stats.number_of_icmp_probe_cancelled++;
    return ADMIN_PROBE_RESULT_ERR;
}


static int
admin_probe_task_module_icmp_dump_stats(struct zpath_debug_state*  request_state,
                                        const char**               query_values,
                                        int                        query_value_count,
                                        void*                      cookie)
{
    char jsonout[10000];

    (void)query_values;
    (void)query_value_count;
    (void)cookie;

    if (ARGO_RESULT_NO_ERROR == argo_structure_dump(admin_probe_task_module_icmp_stats_desciption,
                                                    &stats, jsonout, sizeof(jsonout), NULL, 1)) {
        ZDP("%s\n", jsonout);
        ZDP("total icmp task in exec : %"PRId64"\n", state.total_icmp_in_exec);
    }
    return ADMIN_PROBE_RESULT_NO_ERROR;
}

int admin_probe_task_module_icmp_debug_init()
{
    int res;

    if (!(admin_probe_task_module_icmp_stats_desciption = argo_register_global_structure(ADMIN_PROBE_TASK_MODULE_ICMP_STATS_HELPER))) {
        return ADMIN_PROBE_RESULT_ERR;
    }

    res = zpath_debug_add_read_command("dump the stats of task_module_icmp",
                                  "/admin_probe/task_module_icmp_stats",
                                  admin_probe_task_module_icmp_dump_stats,
                                  NULL,
                                  NULL);
    if (res){
        return ADMIN_PROBE_RESULT_ERR;
    }

    return ADMIN_PROBE_RESULT_NO_ERROR;
}

int64_t admin_probe_get_total_number_of_task_icmp_ping()
{
    return stats.number_of_icmp_probe_request;
}

int64_t admin_probe_get_total_number_of_task_icmp_ping_success()
{
    return stats.number_of_icmp_probe_success;
}


struct admin_probe_task_module_callouts admin_probe_task_module_callouts_icmp = {
    admin_probe_task_module_icmp_init_icmp_request_f,
    admin_probe_task_module_icmp_process_icmp_task_f,
    admin_probe_task_module_icmp_check_expiry_in_processing_state_f,
    admin_probe_task_module_icmp_free_icmp_task_state_f,
    admin_probe_task_module_icmp_cancel_f
};
