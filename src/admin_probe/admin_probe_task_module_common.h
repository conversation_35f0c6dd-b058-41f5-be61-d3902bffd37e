/*
 * admin_probe_task_module_common.h . Copyright (C) 2021 Zscaler Inc, All Rights Reserved
 *
 */

#ifndef _ADMIN_PROBE_TASK_MODULE_COMMON_H_
#define _ADMIN_PROBE_TASK_MODULE_COMMON_H_

#include "admin_probe/admin_probe_task_module_private.h"

/*
 * resolve input domain and store it to input cur_task->common_state.dns_results
 */
int admin_probe_task_module_common_resolve_domain_to_ip(char *domain, struct admin_probe_task *cur_task);

int admin_probe_task_module_common_fill_ip_info_if_already_presented_in_request(char *request_domain, struct admin_probe_task *cur_task);

void admin_probe_task_module_switch_task_state_to_processing_for_synchronous_cmds(struct admin_probe_task *cur_task);


/*
 * cur_task state : PENDING
 *
 * if the command target is a domain string and we can not resolve it to an ip,
 * we cancel this task (state machine: state: PENDING, event: internal_err)
 *
 * return:
 *      ADMIN_PROBE_RESULT_BAD_STATE -
 *                      this task is in PENDING state and we can not PROCESS this task,
 *                      returnr BAD STATE so that task module state machine will cancel this task.
 *      ADMIN_PROBE_TASK_NOT_READY_TO_EXEC -
 *                      still waiting on dns callback to proceed further.
 *                      task module state machine should do nothing for now.
 *
 *      ADMIN_PROBE_RESULT_NO_ERROR - caller should proceed to exec the icmp task
 *
 */
int admin_probe_task_module_common_check_is_ip_info_is_ready_before_executing_task(struct admin_probe_task *cur_task);
void admin_probe_task_module_common_free_common_state(struct task_common *common_state);
int admin_probe_task_module_common_debug_init();
int admin_probe_task_module_tcp_parse_request(struct admin_probe_task *cur_task);

#endif // _ADMIN_PROBE_TASK_MODULE_COMMON_H_
