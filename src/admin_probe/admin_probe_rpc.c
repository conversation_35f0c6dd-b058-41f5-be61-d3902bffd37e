/*
 * admin_probe_rpc.c. Copyright (C) 2021 Zscaler Inc, All Rights Reserved
 */

#include "admin_probe/admin_probe_rpc.h"
#include "admin_probe/admin_probe_rpc_compiled.h"
#include "admin_probe/admin_probe_lib.h"
#include "zpath_lib/zpath_debug.h"

struct argo_structure_description *zpn_command_probe_status_description;
struct argo_structure_description *np_command_probe_status_description;
struct argo_structure_description *admin_probe_stats_description;

static struct {
    int64_t number_of_failure_status_report_sent_on_fohh_because_of_incarnation_not_matched;
    int64_t number_of_failure_status_report_sent_on_fohh_because_of_fohh_not_connected;
} stats;

int admin_probe_rpc_init(void)
{
    zpn_command_probe_status_description = argo_register_global_structure(ZPN_COMMAND_PROBE_STATUS_HELPER);
    admin_probe_stats_description = argo_register_global_structure(ADMIN_PROBE_STATS_HELPER);
    np_command_probe_status_description = argo_register_global_structure(NP_COMMAND_PROBE_STATUS_HELPER);

    if (!zpn_command_probe_status_description ||
        !admin_probe_stats_description ||
        !np_command_probe_status_description) {
        return ADMIN_PROBE_RESULT_ERR;
    }

    return ADMIN_PROBE_RESULT_NO_ERROR;
}

int admin_probe_send_zpn_command_probe_status(struct fohh_connection *f_conn,
                                              int64_t f_conn_incarnation,
                                              struct zpn_command_probe_status *status)
{
    int64_t err_counter = 0;
    if (f_conn_incarnation && (f_conn_incarnation != fohh_connection_incarnation(f_conn))) {
        err_counter = stats.number_of_failure_status_report_sent_on_fohh_because_of_incarnation_not_matched++;
        ADMIN_PROBE_LOG(AL_ERROR, "Dropping command probe status for id:%s action:%s due to incarnation mismatch, total error count:%"PRId64"",
                                                     status->command_uuid, status->action, err_counter);
        return ADMIN_PROBE_RESULT_NO_ERROR;
    }

    if (fohh_get_state(f_conn) != fohh_connection_connected) {
        err_counter = stats.number_of_failure_status_report_sent_on_fohh_because_of_fohh_not_connected++;
        ADMIN_PROBE_LOG(AL_ERROR, "Dropping command probe status for id:%s action:%s due to error in cloud connection, total error count:%"PRId64"",
                                                     status->command_uuid, status->action, err_counter);
        return ADMIN_PROBE_RESULT_CANT_WRITE;
    }

    return fohh_argo_serialize(f_conn, zpn_command_probe_status_description, status, 0, fohh_queue_element_type_control);
}

int admin_probe_send_np_command_probe_status(struct fohh_connection *f_conn,
                                              int64_t f_conn_incarnation,
                                              struct np_command_probe_status *status)
{
    int64_t err_counter = 0;
    if (f_conn_incarnation && (f_conn_incarnation != fohh_connection_incarnation(f_conn))) {
        err_counter = stats.number_of_failure_status_report_sent_on_fohh_because_of_incarnation_not_matched++;
        ADMIN_PROBE_LOG(AL_ERROR, "Dropping np command probe status for id:%s action:%s due to incarnation mismatch, total error count:%"PRId64"",
                                                     status->command_uuid, status->action, err_counter);
        return ADMIN_PROBE_RESULT_NO_ERROR;
    }

    if (fohh_get_state(f_conn) != fohh_connection_connected) {
        err_counter = stats.number_of_failure_status_report_sent_on_fohh_because_of_fohh_not_connected++;
        ADMIN_PROBE_LOG(AL_ERROR, "Dropping np command probe status for id:%s action:%s due to error in cloud connection, total error count:%"PRId64"",
                                                     status->command_uuid, status->action, err_counter);
        return ADMIN_PROBE_RESULT_CANT_WRITE;
    }

    return fohh_argo_serialize(f_conn, np_command_probe_status_description, status, 0, fohh_queue_element_type_control);
}

static int
admin_probe_rpc_dump_stats(struct zpath_debug_state*  request_state,
                           const char**               query_values,
                           int                        query_value_count,
                           void*                      cookie)
{
    ZDP("number_of_failure_status_report_sent_on_fohh_because_of_incarnation_not_matched : %"PRId64" \n", stats.number_of_failure_status_report_sent_on_fohh_because_of_incarnation_not_matched);
    ZDP("number_of_failure_status_report_sent_on_fohh_because_of_fohh_not_connected : %"PRId64" \n", stats.number_of_failure_status_report_sent_on_fohh_because_of_fohh_not_connected);

    return ADMIN_PROBE_RESULT_NO_ERROR;
}

int admin_probe_rpc_debug_init()
{
    int res;

    res = zpath_debug_add_read_command("dump the stats of admin_probe_rpc_send",
                                  "/admin_probe/rpc_stats",
                                  admin_probe_rpc_dump_stats,
                                  NULL,
                                  NULL);
    if (res){
        return ADMIN_PROBE_RESULT_ERR;
    }

    return ADMIN_PROBE_RESULT_NO_ERROR;
}
