/*
 * natural.c. Copyright (C) 2021 Zscaler Inc. All Rights Reserved.
 *
 * Receive logs via the network. Send them appropriately to kafka.
 */

#include <stdlib.h>
#include <stdio.h>
#include <strings.h>
#include <unistd.h>
#include <signal.h>
#include <sys/stat.h>
#include <librdkafka/rdkafka.h>

#include "argo/argo_log.h"
#include "fohh/fohh_log.h"
#include "zvm/zvm.h"

#include "zpath_lib/zpath_app.h"
#include "zpath_lib/zpath_debug.h"

#include "zpn/zpn_rpc.h"

#include "zcrypt/zcrypt.h"

#include "parson/parson.h"

#include "zpath_misc/zpath_version.h"

#include "zpath_lib/sanitizer_config.h"

#include "zpn_event/zpn_event.h"
#include "zpath_misc/zpath_misc.h"

struct kafka_conn_state;
struct zpath_allocator natural_allocator = ZPATH_ALLOCATOR_INIT("natural");
#define NATURAL_MALLOC(x) zpath_malloc(&natural_allocator, x, __LINE__, __FILE__)
#define NATURAL_FREE(x) zpath_free(x, __LINE__, __FILE__)
#define NATURAL_FREE_SLOW(x) zpath_free_slow(x, __LINE__, __FILE__)
#define NATURAL_CALLOC(x) zpath_calloc(&natural_allocator, x, __LINE__, __FILE__)
#define NATURAL_STRDUP(x, y) zpath_strdup(&natural_allocator, x, y, __LINE__, __FILE__)

ZTAILQ_HEAD(kafka_ack_head, kafka_ack);
struct kafka_ack {
    struct fohh_log_custom_connection *custom_connection;
    struct kafka_conn_state *k_conn;
    int64_t custom_connection_incarnation;
    int64_t log_id;
    int64_t log_incarnation;
    int64_t bytes;
    ZTAILQ_ENTRY(kafka_ack) list;
    int ack_complete;
};

/*
 * This state will live so long as messages_queued (messages sent to
 * kafka) > (errors_received + acks_received) (messages back from
 * kafka).
 */
ZTAILQ_HEAD(kafka_conn_state_head, kafka_conn_state);
struct kafka_conn_state {                              /* _ARGO: object_definition */
    int64_t custom_connection_incarnation;             /* _ARGO: integer */
    int64_t messages_queued;                           /* _ARGO: integer */
    int64_t acks_received;                             /* _ARGO: integer */
    int64_t errors_received;                           /* _ARGO: integer */
    int64_t bytes_received;                            /* _ARGO: integer */
    int64_t rate_limited;                              /* _ARGO: integer */
    char *conn_desc;                                   /* _ARGO: string */
    uint64_t start_s;
    int64_t ref_cnt;
    int connection_gone;
    struct fohh_log_custom_connection *custom_connection;
    struct kafka_ack_head acks;
    struct argo_log_registered_structure *k_conn_rstruct;
    zpath_mutex_t lock;
    char name[128];
    uint64_t topics_received;
    ZTAILQ_ENTRY(kafka_conn_state) list;
};

struct global_kconns_ {
    struct kafka_conn_state_head k_conns;
    zpath_mutex_t                lock;
    int                          count;
};

struct global_kconns_ global_kconns;
/* Default max logging text for natural C - Producer */
#define MAX_TEXT_PER_LINE                       1000000
#define LOG_PRIORITY                            9
static int argo_log_max_text_per_line_per_second[LOG_PRIORITY];

char identity[1000]   = "test_program";
char stats_hostname[1000];
char ca_filename_str[1024];
char cert_filename_str[2048];
char key_filename_str[2048];
int debug_port        = 8052;
int debuglog          = 0;
int do_debug_conns    = 0;
int itasca_logging_port_he = 0;

//#define UNIT_TEST 1
#define MAX_PORTS_IN_PORT_GROUP 1000
#define MAX_PORT_GROUPS 1000
#define RATE_CALC_TIME_PERIOD_S 1
#define RATE_LIMITED 2
#define NOT_RATE_LIMITED 3
#define RATE_LIMIT_STATS_COLLEC_FREQ_S 10
#define RATE_LIMIT_CONFIG_FILE_CHECK_FREQ_S 60
#define RATE_LIMIT_STATS_UPLOAD_FREQ_S 300

#ifndef UNIT_TEST
    #define RATE_LIMIT_GARBAGE_STAY_TIME_S 1800
    #define RATE_LIMIT_GARBAGE_CLEAR_FREQ_S 3600
#else
    #define RATE_LIMIT_GARBAGE_STAY_TIME_S 120
    #define RATE_LIMIT_GARBAGE_CLEAR_FREQ_S 240
#endif

#define RATE_LIMIT_SAMPLE_CONF_FILE "/opt/zscaler/etc/rate_limit.json"
// enums for rate buckets and tables
typedef enum {  TOPIC = 1,
                CUSTOMER,
                PORTGROUP,
                CUST_BUCKETLIST,
                PG_BUCKETLIST }
    rate_bucket_type_t;
int g_skip_list_dummy = 1;
const char* g_rate_limit_config_file = NULL;
time_t g_rate_limit_config_mtime = 0;
int g_rate_limit_arg_present = 0;
int64_t g_rate_limit_stats_lines_count = 0;
const char* g_rate_limit_sample_conf = "{\n\t\"rate_limit_config\": {\n"
		                                "\t\t\"rate_limit_enabled\" : \"0\",\n"
		                                "\t\t\"rate_limit_min_duration\" : \"600\",\n"
                                        "\t\t\"rate_limit_simulation_mode\" : \"1\",\n"
                                        "\t\t\"rate_limit_deadletter_topic\" : \"1\",\n"
		                                "\t\t\"rate_limit_configs\" : [ ],\n"
		                                "\t\t\"port_groups\" : [ ],\n"
		                                "\t\t\"rate_limit_customer_skiplist\" : [ ],\n"
                                        "\t\t\"rate_limit_customer_pick_first_list\" : [ ],\n"
                                        "\t\t\"stats_upload\" : \"0\",\n"
	                                    "\t} \n"
                                        "}\n";

struct natural_config {               /* _ARGO: object_definition */
    char *identity;                   /* _ARGO: string */
    char *id_environment;             /* _ARGO: string */
    char *cloud;                      /* _ARGO: string */
    int nosend_stats;                 /* _ARGO: integer */
    char *stats_filename;             /* _ARGO: string */
    char *cert_path;                  /* _ARGO: string */
    char *ca_filename;                /* _ARGO: string */
    char *cert_filename;              /* _ARGO: string */
    char *key_filename;               /* _ARGO: string */
    char **listen_ips;                /* _ARGO: string */
    int listen_ips_count;             /* _ARGO: quiet, integer, count: listen_ips */
    int *listen_port_he;              /* _ARGO: integer, synonym: listen_ports */
    int listen_port_he_count;         /* _ARGO: quiet, integer, count: listen_port_he */
    int *listen_port_tlv_json_he;     /* _ARGO: integer, synonym: listen_ports_json */
    int listen_port_tlv_json_he_count;/* _ARGO: quiet, integer, count: listen_port_tlv_json_he */
    char *logfile;                    /* _ARGO: string */
    int debug_port;                   /* _ARGO: integer */
    int thread_count;                 /* _ARGO: integer */
    int64_t client_stats_interval_ms; /* _ARGO: integer */
    char *kafka_brokers;              /* _ARGO: string */
    char **kafka_configs;             /* _ARGO: string */
    int kafka_configs_count;          /* _ARGO: quiet, integer, count: kafka_configs */
    char **kafka_topics;              /* _ARGO: string */
    int kafka_topics_count;           /* _ARGO: quiet, integer, count: kafka_topics */
    char **kafka_topic_configs;       /* _ARGO: string */
    int kafka_topic_configs_count;    /* _ARGO: quiet, integer, count: kafka_topic_configs */
    char *api_sni;                    /* _ARGO: string */
    char *zistats_domain;             /* _ARGO: string */

};

struct rate_limit_config {                         /* _ARGO: object_definition */
    char **port_groups;                            /* _ARGO: string */
    int port_groups_count;                         /* _ARGO: quiet, integer, count: port_groups */
    int rate_limit_enabled;                        /* _ARGO: integer */
    int rate_limit_simulation_mode;                /* _ARGO: integer */
    int rate_limit_deadletter_topic;               /* _ARGO: integer */
    int rate_limit_min_duration;                   /* _ARGO: integer */
    char **rate_limit_configs;                     /* _ARGO: string */
    int rate_limit_configs_count;                  /* _ARGO: quiet, integer, count: rate_limit_configs */
    char **rate_limit_customer_skiplist;           /* _ARGO: string */
    int rate_limit_customer_skiplist_count;        /* _ARGO: quiet, integer, count: rate_limit_customer_skiplist */
    char **rate_limit_customer_pick_first_list;    /* _ARGO: string */
    int rate_limit_customer_pick_first_list_count; /* _ARGO: quiet, integer, count: rate_limit_customer_pick_first_list */
    int stats_upload;                              /* _ARGO: integer */
};

/* These come directly from librdkafka stats. Exact same names please */
struct natural_kafka_stats {         /* _ARGO: object_definition */
    int64_t replyq;                  /* _ARGO: integer */
    int64_t msg_cnt;                 /* _ARGO: integer */
    int64_t msg_size;                /* _ARGO: integer */
    int64_t tx;                      /* _ARGO: integer */
    int64_t tx_bytes;                /* _ARGO: integer */
    int64_t rx;                      /* _ARGO: integer */
    int64_t rx_bytes;                /* _ARGO: integer */
    int64_t txmsgs;                  /* _ARGO: integer */
    int64_t txmsg_bytes;             /* _ARGO: integer */
};
struct natural_kafka_broker_stats {  /* _ARGO: object_definition */
    int64_t outbuf_cnt;              /* _ARGO: integer */
    int64_t outbuf_msg_cnt;          /* _ARGO: integer */
    int64_t waitresp_cnt;            /* _ARGO: integer */
    int64_t waitresp_msg_cnt;        /* _ARGO: integer */
    int64_t tx;                      /* _ARGO: integer */
    int64_t txbytes;                 /* _ARGO: integer */
    int64_t txerrs;                  /* _ARGO: integer */
    int64_t txretries;               /* _ARGO: integer */
    int64_t req_timeouts;            /* _ARGO: integer */
    int64_t disconnects;             /* _ARGO: integer */
};
struct natural_kafka_partition_stats { /* _ARGO: object_definition */
    int64_t broker;                    /* _ARGO: integer */
    int64_t msgq_cnt;                  /* _ARGO: integer */
    int64_t msgq_bytes;                /* _ARGO: integer */
    int64_t xmit_msgq_cnt;             /* _ARGO: integer */
    int64_t xmit_msgq_bytes;           /* _ARGO: integer */
    int64_t msgs_inflight;             /* _ARGO: integer */
    int64_t txmsgs;                    /* _ARGO: integer */
    int64_t txbytes;                   /* _ARGO: integer */
    int64_t rxmsgs;                    /* _ARGO: integer */
    int64_t rxbytes;                   /* _ARGO: integer */
};
struct natural_kafka_topic_stats { /* _ARGO: object_definition */
    int64_t batchcnt_cnt;             /* _ARGO: integer */
    int64_t batchcnt_avg;             /* _ARGO: integer */
    int64_t batchcnt_min;             /* _ARGO: integer */
    int64_t batchcnt_max;             /* _ARGO: integer */
    int64_t batchcnt_p50;             /* _ARGO: integer */
    int64_t batchcnt_p75;             /* _ARGO: integer */
    int64_t batchcnt_p90;             /* _ARGO: integer */
    int64_t batchcnt_p95;             /* _ARGO: integer */
    int64_t batchcnt_p99;             /* _ARGO: integer */
    int64_t batchsize_cnt;             /* _ARGO: integer */
    int64_t batchsize_avg;             /* _ARGO: integer */
    int64_t batchsize_min;             /* _ARGO: integer */
    int64_t batchsize_max;             /* _ARGO: integer */
    int64_t batchsize_p50;             /* _ARGO: integer */
    int64_t batchsize_p75;             /* _ARGO: integer */
    int64_t batchsize_p90;             /* _ARGO: integer */
    int64_t batchsize_p95;             /* _ARGO: integer */
    int64_t batchsize_p99;             /* _ARGO: integer */
    int64_t msgq_cnt;                  /* _ARGO: integer */
    int64_t msgq_bytes;                /* _ARGO: integer */
    int64_t xmit_msgq_cnt;             /* _ARGO: integer */
    int64_t xmit_msgq_bytes;           /* _ARGO: integer */
    int64_t msgs_inflight;             /* _ARGO: integer */
    int64_t txmsgs;                    /* _ARGO: integer */
    int64_t txbytes;                   /* _ARGO: integer */
    int64_t rxmsgs;                    /* _ARGO: integer */
    int64_t rxbytes;                   /* _ARGO: integer */
};
struct natural_stats {                 /* _ARGO: object_definition */
    int64_t k_conn_count;              /* _ARGO: integer */
    int64_t k_conn_created;            /* _ARGO: integer */
    int64_t k_conn_destroyed;          /* _ARGO: integer */

    int64_t empty_queue_block;         /* _ARGO: integer */
    int64_t non_empty_queue_block;     /* _ARGO: integer */
    int64_t msg_count;                 /* _ARGO: integer */
    int64_t err_count;                 /* _ARGO: integer */
    int64_t ack_count;                 /* _ARGO: integer */
    int64_t ack_err_count;             /* _ARGO: integer */
    int64_t argo_deserial_err_count;   /* _ARGO: integer */
    int64_t produce_count;             /* _ARGO: integer */
    int64_t produce_err_count;         /* _ARGO: integer */
};

struct rate_bucket_stats{                   /* _ARGO: object_definition */
    uint64_t total_rx_logs;                 /* _ARGO: integer */
    uint64_t curr_rx_logs;                  /* _ARGO: integer */
    uint64_t logs_dropped;                  /* _ARGO: integer */
    uint64_t curr_logs_dropped;             /* _ARGO: integer */
    uint64_t total_bytes;                   /* _ARGO: integer */
    uint64_t bytes_dropped;                 /* _ARGO: integer */
    uint64_t log_rate;                      /* _ARGO: integer */
    uint64_t curr_bytes;                    /* _ARGO: integer */
    uint64_t curr_bytes_dropped;            /* _ARGO: integer */
    int64_t  last_rate_limit_start_time_s;  /* _ARGO: integer */
    int64_t  last_rate_limit_end_time_s;    /* _ARGO: integer */
    uint8_t  rate_limit_active;             /* _ARGO: integer */
    uint64_t rate_limit_activation_count;   /* _ARGO: integer */
};
#include "natural/natural_compiled_c.h"

/***************************************************
 * Kafka state
 */
struct kafka_topic {
    char *name;
    int consistent;
    int topic_index;
    rd_kafka_topic_t *topic;
};

struct kafka_conf {
    char *config_names[100];
    char *config_values[100];
    int config_count;
};
struct port_group_conf {
    char *name;
    char *ports;
    char *protocol;
};
ZTAILQ_HEAD(rx_logs_list_head, rx_logs_list);
struct rx_logs_list {
    uint64_t rx_logs;
    ZTAILQ_ENTRY(rx_logs_list) list;
};
struct rate_bucket {
    zpath_mutex_t lock;
    rate_bucket_type_t type;
    uint8_t is_trans_log;
    uint8_t is_rate_limit_triggered;
    uint8_t drop_child_max;
    int64_t rate_limit_start_time;
    int64_t rx_log_list_count;
    int64_t rx_log_list_sum;
    struct rx_logs_list_head queue;
    uint64_t rx_logs;
    uint64_t curr_rx_logs;
    uint64_t rx_logs_rate;
    uint64_t sum_picked_buckets_rate_curr;
    uint64_t sum_picked_buckets_rate_prev;
    char *port_group_name;
    int64_t customer_id;
    char *topic;
    struct rate_bucket *parent;
    int64_t low_watermark;
    int64_t high_watermark;
};

ZTAILQ_HEAD(rate_limit_bucket_stats_desc_head,rate_bucket_stats_desc);

struct rate_bucket_stats_desc {
    rate_bucket_type_t type;
    uint8_t desc_origin;
    char *key;
    uint64_t bytes_serialized;
    uint64_t log_dropped;
    uint64_t log_rxd;
    uint8_t is_active;
    uint8_t activation_count;
    int64_t last_rate_limit_start_time_s;
    int64_t last_rate_limit_end_time_s;
    int64_t rate;
    ZTAILQ_ENTRY(rate_bucket_stats_desc) list;
    ZTAILQ_ENTRY(rate_bucket_stats_desc) free_list;
};

struct rate_limit_bucket_stats_q {
    zpath_mutex_t lock;
    struct  rate_limit_bucket_stats_desc_head queue;
    struct  rate_limit_bucket_stats_desc_head free_queue;
    int64_t total_callocs;
    int64_t curr_free;
    int64_t curr_used;
};

struct rate_limit_bucket_stats_q g_rate_limit_stats_q;
zpath_mutex_t g_rate_limit_stats_tbl_lock;
ZLIST_HEAD(rate_bucket_list_head,rate_bucket_list_entry);

struct rate_bucket_list_entry {
    ZLIST_ENTRY(rate_bucket_list_entry)   list;
    struct rate_bucket *rate;
};

struct rate_bucket_list {
    zpath_mutex_t lock;
    struct rate_bucket_list_head list;
};

struct rate_limit_tbls_ref {
    struct rate_limit_config *rate_limit_config;
    uint8_t config_valid;
    struct port_group_conf    port_group_confs[MAX_PORT_GROUPS];
    int16_t                   port_group_count;
    struct argo_object        *rate_limit_config_obj;
    struct zhash_table        *rate_limit_skiplist_tbl;
    struct zhash_table        *rate_limit_pick_first_tbl;
    struct zhash_table        *topic_rate_tbl;
    struct zhash_table        *customer_topic_rate_tbl;
    struct zhash_table        *port_group_customer_topic_rate_tbl;
    struct zhash_table        *customer_list_per_topic_tbl;
    struct zhash_table        *port_group_list_per_topic_customer_tbl;
    zpath_mutex_t customer_topic_rate_tbl_lock;
    zpath_mutex_t port_group_customer_topic_rate_tbl_lock;
    zpath_mutex_t port_group_list_per_topic_customer_tbl_lock;
};

struct rate_limit_tbls_ref *g_rate_limit_tbls = NULL;
struct zhash_table       *g_topic_rate_stats_tbl = NULL;
struct zhash_table       *g_cust_topic_rate_stats_tbl = NULL;
struct zhash_table       *g_port_group_cust_topic_rate_stats_tbl = NULL;
uint64_t g_customer_rate_bucket_not_found = 0;
uint64_t g_def_pg_rate_bucket_not_found = 0;
ZTAILQ_HEAD(rate_limit_garbage_desc_head,rate_limit_garbage_desc);

struct rate_limit_garbage_desc {
    struct rate_limit_tbls_ref *rate_limit_tbls;
    int64_t free_req_time_s;
    ZTAILQ_ENTRY(rate_limit_garbage_desc) list;
};

struct tbl_free_tailq {
    zpath_mutex_t lock;
    struct rate_limit_garbage_desc_head queue;
    int64_t count;
    int64_t total_added;
    int64_t total_removed;
    int64_t last_run_time;
};

struct tbl_free_tailq g_rate_limit_garbage;

static void kafka_conn_destroy_if_possible(struct kafka_conn_state *k_conn);
static int print_k_conn_detail(struct kafka_conn_state *, char *, char *);


static char *default_listen_ip = "0.0.0.0";

struct natural_config *global_config = NULL;
static struct natural_stats global_natural_stats = {0};

char *conf_topics[1000];
int conf_topics_count = 0;
static zpath_mutex_t stats_lock;
char *last_kafka_stats = NULL;

/* kafka log variables to hold the last values seen */
int64_t prev_tx_cnt = 0;
int64_t prev_tx_bytes = 0;


#ifdef UNIT_TEST
    uint64_t logs_rxd = 0;
#endif

rd_kafka_t               *rk;
struct zevent_base       *rk_poll_base = NULL;
struct event             *rk_timer = NULL;

struct zhash_table       *all_topics = NULL;

struct kafka_conf kafka_conf;

struct argo_structure_description *natural_config_description = NULL;
struct argo_structure_description *natural_kafka_stats_description = NULL;
struct argo_structure_description *natural_kafka_topic_stats_description = NULL;
struct argo_structure_description *natural_kafka_broker_stats_description = NULL;
struct argo_structure_description *natural_kafka_partition_stats_description = NULL;
struct argo_structure_description *natural_stats_description = NULL;
struct argo_structure_description *k_conn_stats_description = NULL;
struct argo_structure_description *rate_bucket_stats_description = NULL;
struct argo_structure_description *rate_limit_config_description = NULL;

static void config_file_change_check(int sock, short events, void *cookie);
static void rate_limit_customer_skiplist_init(struct rate_limit_tbls_ref *l_ref);
static void rate_limit_customer_pick_first_init(struct rate_limit_tbls_ref *l_ref);
int rate_limit_config_validate( struct rate_limit_tbls_ref *l_ref );
int rate_limit_init( struct rate_limit_tbls_ref *l_ref );
int port_group_init( struct rate_limit_tbls_ref *l_ref );
void usage_and_exit(const char *bin_name)
{
    fprintf(stderr,
            "Usage: %s [-debuglog] [-printf] [-debugconns] [-itasca_logs PORT] [-maxlogmb MB] FILENAME [-rate_limit_config] RATELIMITFILENAME\n"
            "  FILENAME refers to a file containing a JSON object of the following form. If (without the comments, which are not JSON)\n"
            "  Any field that is not included uses default value, which is in the comment\n"
            "  The REQUIRED fields are: (identity OR cert_filename) AND cloud AND kafka_brokers AND kafka_topics\n"
            "    {\"natural_config\":{\n"
            "       \"identity\":STRING,                        DEFAULT: If unspecified, comes from certificate CN\n"
            "       \"id_environment\":STRING,                  DEFAULT: SYS_ID :Attempts to read the specified environment variable, and appends it to whatever identity specified\n"
            "                                                       This allows the administartor to identify specific containers, etc\n"
            "       \"cloud\":STRING,                           DEFAULT: unspecified, but required\n"
            "       \"nosend_stats\":INTEGER,                   DEFAULT: 0. Set to 1 to disable sending stats\n"
            "       \"stats_filename\":STRING,                  DEFAULT: unspecified: no stats file\n"
            "       \"cert_path\":STRING,                       DEFAULT: ./\n"
            "       \"ca_filename\":STRING,                     DEFAULT: $CLOUD.crt\n"
            "       \"cert_filename\":STRING,                   DEFAULT: $IDENTITY.crt\n"
            "       \"key_filename\":STRING,                    DEFAULT: $IDENTITY.key\n"
            "       \"listen_ips\":STRING_ARRAY,                DEFAULT: [0.0.0.0]\n"
            "       \"listen_ports\":INTEGER_ARRAY,             DEFAULT: [443]\n"
            "       \"listen_ports_json\":INTEGER_ARRAY,        DEFAULT: []\n"
            "       \"logfile\":STRING,                         DEFAULT: unspecified = none\n"
            "       \"debug_port\":INTEGER,                     DEFAULT: 8052\n"
            "       \"thread_count\":INTEGER,                   DEFAULT: 4\n"
            "       \"client_stats_interval_ms\":INTEGER        DEFAULT: 60000. This is the interval at which stats are logged for incoming connections\n"
            "       \"kafka_brokers\":STRING,                   DEFAULT: unspecified, required. CSV list of kafka brokers\n"
            "       \"kafka_configs\":STRING_ARRAY,             DEFAULT: unspecified, see below\n"
            "       \"kafka_topics\":STRING_ARRAY,              DEFAULT: unspecified, required. Allowed topics\n"
            "       \"kafka_topic_configs\":STRING_ARRAY,       DEFAULT: unspecified, see below\n"
            "       \"api_sni\":STRING_ARRAY,                   DEFAULT: If unspecified, uses default 'apilog'\n"
            "       \"zistats_domain\":STRING_ARRAY,            DEFAULT: If unspecified, uses default 'statslog-pg-ccf.<cloud>'\n"
            "    }}\n"
            "  kafka_configs: Array of strings, in pairs. Each pair of strings is a name,value pair for Kafka config. i.e. you could:\n"
            "                [\"queue.buffering.max.ms\",\"10\"]\n"
            "  kafka_topic_configs: Array of strings, in trios. Each trio of strings is a topic_name,config_name,config_value trio. i.e:\n"
            "                [\"zpn_txn\",\"partitioner\",\"consistent_random\",\n"
            "                 \"zpn_health\",\"partitioner\",\"consistent\"]\n"
            "  For topic configs: later configs override earlier configs. The topic named '*' will be applied to all topics\n"
            "  For overriding default server sni use api_sni attribute i.e. \"api_sni\": \"service.dev.zpath.net\"\n"
            "  For overriding default zistats dns name use zistats_domain attribute i.e. \"zistats_domain\": \"zistats.dev.zpath.net\"\n"
            "\n"
            "  RATELIMITFILENAME refers to a file containing a JSON object of the following form. If (without the comments, which are not JSON)\n"
            "  Any field that is not included uses default value, which is in the comment\n"
            "    {\"rate_limit_config\":{\n"
            "       \"rate_limit_enabled\":INTEGER,                       DEFAULT: 0. Set to 1 to enable rate limiting\n"
            "       \"rate_limit_min_duration\":INTEGER,                  DEFAULT: 600\n"
            "       \"rate_limit_simulation_mode\":INTEGER,               DEFAULT: 1. Set to 0 to disable simulation mode\n"
            "       \"rate_limit_deadletter_topic\":INTEGER,              DEFAULT: 1. Set to 0 to disable\n"
            "       \"rate_limit_configs\":STRING_ARRAY,                  DEFAULT: []\n"
            "       \"port_groups\":STRING_ARRAY,                         DEFAULT: []\n"
            "       \"rate_limit_customer_skiplist\":STRING_ARRAY,        DEFAULT: []\n"
            "       \"rate_limit_customer_pick_first_list\":STRING_ARRAY, DEFAULT: []\n"
            "    }}\n"
            "  Note: To set the kafka logging interval, set kafka config \"statistics.interval.ms\"\n"
            ,
            bin_name);
    fprintf(stderr, "Additional arguments:\n");
    zpath_app_logging_usage_print();

    exit(1);
}

int natural_rate_limit_gc_stats(struct zpath_debug_state *request_state,
                              const char **query_values,
                              int query_value_count,
                              void *cookie)
{
    ZPATH_MUTEX_LOCK(&(g_rate_limit_garbage.lock), __FILE__, __LINE__);
    ZDP("Garbage collection stats\n");
    ZDP("\t Removal pending count : %"PRId64 "\n", g_rate_limit_garbage.count );
    ZDP("\t Total items added : %"PRId64 "\n",g_rate_limit_garbage.total_added );
    ZDP("\t Total items removed : %"PRId64 "\n",g_rate_limit_garbage.total_removed );
    ZDP("\t Last GC run time : %"PRId64 "\n",g_rate_limit_garbage.last_run_time );
    ZPATH_MUTEX_UNLOCK(&(g_rate_limit_garbage.lock), __FILE__, __LINE__);
    return 0;
}

int natural_rate_limit_skipped_logs(struct zpath_debug_state *request_state,
                              const char **query_values,
                              int query_value_count,
                              void *cookie)
{
    ZDP("Skipped logs details:\n");
    ZDP("Skipped due to customer rate bucket not created: %"PRIu64" logs \n",
            g_customer_rate_bucket_not_found);

    ZDP("Skipped due to default port group rate bucket not created: %"PRIu64" logs \n",
            g_def_pg_rate_bucket_not_found);
    return 0;
}

int natural_rate_limit_stats_queue(struct zpath_debug_state *request_state,
                              const char **query_values,
                              int query_value_count,
                              void *cookie)
{
    ZPATH_MUTEX_LOCK(&(g_rate_limit_stats_q.lock), __FILE__, __LINE__);
    ZDP("Stats queue details:\n");
    ZDP("\t Total objects created : %"PRId64 "\n", g_rate_limit_stats_q.total_callocs );
    ZDP("\t Curr objects free : %"PRId64 "\n", g_rate_limit_stats_q.curr_free );
    ZDP("\t Curr objects used : %"PRId64 "\n", g_rate_limit_stats_q.curr_used);
    ZPATH_MUTEX_UNLOCK(&(g_rate_limit_stats_q.lock), __FILE__, __LINE__);
    return 0;
}
static int rate_limit_stats_upload_walk(void *cookie,
                                        void *object,
                                        void* key,
                                        size_t key_len)
{
    int res = 0;
    char stats_obj_name[128] = {'\0'};
    struct rate_bucket_stats *l_stats = ( struct rate_bucket_stats*)object;
    if (!l_stats->curr_rx_logs ) {
        return 0;
    }
    snprintf(stats_obj_name,sizeof(stats_obj_name),"%s_rate_stats",(char*)key);
    uint64_t orig_log_rate = l_stats->log_rate;
    l_stats->log_rate = l_stats->curr_rx_logs / RATE_LIMIT_STATS_UPLOAD_FREQ_S;
    res = argo_log_structure_immediate(zpath_stats_collection,
                                       argo_log_priority_info,
                                       1, // int include_role,
                                       stats_obj_name,
                                       rate_bucket_stats_description,
                                       l_stats);
    if (res) {
        ZPATH_LOG(AL_ERROR, "Could not log stats immediate for %s: %s",
                                stats_obj_name, argo_result_string(res));
    }
    l_stats->log_rate = orig_log_rate;
    l_stats->curr_bytes = 0;
    l_stats->curr_logs_dropped = 0;
    l_stats->curr_rx_logs = 0;
    l_stats->curr_bytes_dropped = 0;
    return res;
}

static int rate_bucket_stats_display_walk(void *cookie1, void *cookie2, void *cookie3, void *cookie4, void *cookie5,
                                        void *object,
                                        void* key,
                                        size_t key_len)
{
    struct rate_bucket_stats* l_stats = (struct rate_bucket_stats*) object;
    struct zpath_debug_state *request_state = (struct zpath_debug_state *) cookie1;
    char line_buf[2096] = {'\0'};
    snprintf(line_buf,sizeof(line_buf),"key:%s,rx_logs:%"PRIu64
            ",curr_logs_rate:%"PRIu64
            ",logs_dropped:%"PRIu64
            ",bytes:%"PRIu64
            ",bytes_dropped:%"PRId64
            ",last_start_time:%"PRId64
            ",last_end_time:%"PRId64
            ",active:%d,activation_count:%"PRId64"\n",
            (char *)key,
            l_stats->total_rx_logs,
            l_stats->log_rate,
            l_stats->logs_dropped,
            l_stats->total_bytes,
            l_stats->bytes_dropped,
            l_stats->last_rate_limit_start_time_s,
            l_stats->last_rate_limit_end_time_s,
            l_stats->rate_limit_active,
            l_stats->rate_limit_activation_count );
    if ( cookie2 ) {
        FILE *fp = (FILE *) cookie2;
        fprintf(fp,"%s",line_buf);
    }else {
        ZDP("%s",line_buf);
    }
    return 0;
}
int natural_rate_limit_config_dump(struct zpath_debug_state *request_state,
                                    const char **query_values,
                                    int query_value_count,
                                    void *cookie) {
    struct rate_limit_tbls_ref* l_ref = g_rate_limit_tbls;
    if ( !l_ref->rate_limit_config_obj ) {
        ZDP("No rate limit config object is present\n");
        return 0;
    }
    struct argo_object* obj = l_ref->rate_limit_config_obj;
    char buf[10000];
    int res = argo_object_dump(obj,buf,sizeof(buf),NULL,1);
    if ( res ) {
        ZDP("argo_object_dump failed for config dump\n");
        return 1;
    }
    ZDP("%s\n",buf);
    return 0;
}

int natural_rate_bucket_stats(struct zpath_debug_state *request_state,
                              const char **query_values,
                              int query_value_count,
                              void *cookie)
{
    const char* title_string[3] = { "TOPIC Level Stats" , "Customer Level Stats" ,"Port Group Level Stats" };
    struct zhash_table* tbls[3];
    tbls[0] = g_topic_rate_stats_tbl;
    tbls[1] = g_cust_topic_rate_stats_tbl;
    tbls[2] = g_port_group_cust_topic_rate_stats_tbl;
    FILE *fp = NULL;
    void *cookie2 = NULL;
    if ( query_values[0] ) {
        fp = fopen(query_values[0],"w");
        if ( !fp ) {
            ZDP("Failed to open file %s for dumping stats\n",query_values[0]);
            return 0;
        } else {
            cookie2 = fp;
            ZDP("Stats will be written to %s\n",query_values[0]);
        }
    }
    if ( g_rate_limit_stats_lines_count > 3000 && !query_values[0]) {
        ZDP("Too many stats; Please use file_name option to dump it to a file\n");
        return 0;
    }
    for ( int i = 0; i < 3; i++ ) {
        int64_t walk_key = 0;
        if ( query_values[0] )
            fprintf(fp,"%s:\n",title_string[i]);
        else
            ZDP("%s:\n",title_string[i]);
        ZPATH_MUTEX_LOCK(&(g_rate_limit_stats_tbl_lock), __FILE__, __LINE__);
        while ( zhash_table_walk2(tbls[i],
                                &walk_key,
                                rate_bucket_stats_display_walk,
                                request_state, cookie2, NULL, NULL, NULL) == ZHASH_RESULT_INCOMPLETE ) {}
        ZPATH_MUTEX_UNLOCK(&(g_rate_limit_stats_tbl_lock), __FILE__, __LINE__);
        if ( query_values[0])
            fprintf(fp,"\n");
        else
            ZDP("\n");
    }
    if ( query_values[0] ) {
        ZDP("Stats are dumped to file %s\n",query_values[0]);
        fclose(fp);
    }

    return 0;

}
struct rate_bucket_stats_desc* get_stats_desc(struct rate_bucket* rate,int set_rate ){
        char key[128] = {'\0'};
        struct rate_bucket_stats_desc *desc = NULL;
        ZPATH_MUTEX_LOCK(&(g_rate_limit_stats_q.lock), __FILE__, __LINE__);
        if ( !ZTAILQ_EMPTY(&(g_rate_limit_stats_q.free_queue)) ) {
            desc = ZTAILQ_FIRST(&(g_rate_limit_stats_q.free_queue));
            ZTAILQ_REMOVE(&(g_rate_limit_stats_q.free_queue),desc,free_list);
            g_rate_limit_stats_q.curr_free--;
        } else {
            desc = NATURAL_CALLOC(sizeof(*desc));
            g_rate_limit_stats_q.total_callocs++;
        }
        ZPATH_MUTEX_UNLOCK(&(g_rate_limit_stats_q.lock), __FILE__, __LINE__);
        desc->type = rate->type;
        if ( set_rate ) {
            desc->desc_origin = 1;
            desc->rate = rate->rx_logs_rate;
        }
        if ( desc->type == TOPIC )  {
            snprintf(key,sizeof(key),"%s",rate->topic);
        } else if ( desc->type == CUSTOMER ) {
            snprintf(key,sizeof(key),"%s_%"PRId64,rate->topic,rate->customer_id);
        } else if ( desc->type == PORTGROUP ) {
            snprintf(key,sizeof(key),"%s_%"PRId64"_%s",
                    rate->topic,
                    rate->customer_id,
                    rate->port_group_name);
        }
        desc->key = NATURAL_STRDUP(key,strlen(key));
        return desc;
}
void add_stats_desc_to_queue(struct rate_bucket_stats_desc *desc){
        ZPATH_MUTEX_LOCK(&(g_rate_limit_stats_q.lock), __FILE__, __LINE__);
        ZTAILQ_INSERT_TAIL(&(g_rate_limit_stats_q.queue),desc,list);
        g_rate_limit_stats_q.curr_used++;
        ZPATH_MUTEX_UNLOCK(&(g_rate_limit_stats_q.lock), __FILE__, __LINE__);
}
static int rate_bucket_list_walk(void *cookie, void *object, void* key1,
                    size_t key_len)
{
    struct rate_bucket_list *rate_list = (struct rate_bucket_list *) object;
    struct rate_limit_tbls_ref *l_ref = (struct rate_limit_tbls_ref *) cookie;
    struct rate_bucket_list_entry *entry = NULL, *temp = NULL;
    struct rate_bucket *max_rate = NULL;
    uint64_t max_rx_logs_rate = 0;
    uint64_t picked_max_rx_logs_rate = 0;
    int64_t now = epoch_s();
    char customer_id_str[64] = {'\0'};

    ZPATH_MUTEX_LOCK(&(rate_list->lock),__FILE__,__LINE__);
    ZLIST_FOREACH_SAFE(entry, &(rate_list->list), list, temp) {
        ZPATH_MUTEX_LOCK(&(entry->rate->lock),__FILE__,__LINE__);
        entry->rate->rx_log_list_count++;
        entry->rate->rx_log_list_sum += (entry->rate->rx_logs / RATE_CALC_TIME_PERIOD_S);
        struct rx_logs_list* newLog = NATURAL_MALLOC(sizeof(struct rx_logs_list));
        newLog->rx_logs = entry->rate->rx_logs;
        ZTAILQ_INSERT_TAIL(&(entry->rate->queue), newLog, list);
        if(entry->rate->rx_log_list_count > 60) {
            entry->rate->rx_log_list_count --;
            struct rx_logs_list* oldest = ZTAILQ_FIRST(&(entry->rate->queue));
            entry->rate->rx_log_list_sum -= (oldest->rx_logs / RATE_CALC_TIME_PERIOD_S);
            ZTAILQ_REMOVE(&(entry->rate->queue), oldest, list);
            ZLIB_FREE(oldest);
        }
        uint64_t rx_logs_rate = entry->rate->rx_log_list_sum / entry->rate->rx_log_list_count;
        entry->rate->rx_logs_rate = rx_logs_rate;
        uint8_t drop_max = entry->rate->parent->drop_child_max;
        uint8_t rate_limited = entry->rate->is_rate_limit_triggered;
        uint8_t parent_rate_limited = entry->rate->parent->is_rate_limit_triggered;
        int *present = NULL;
        snprintf(customer_id_str,sizeof(customer_id_str),"%"PRId64,entry->rate->customer_id);
        // Instead of updating stats to hash table directly, we insert a stats desc in a queue.
        // This way if we display stats too often or take long time to upload
        // we will not have to block datapath. The queue will be locked only if the hash table is locked
        // when processed. So if the hash table is already used, we will continue to grow the queue
        // from datapath without getting blocked. Once the table lock is acquired, the stats collector
        // remove the desc from queue and processes it.
        struct rate_bucket_stats_desc *desc = get_stats_desc(entry->rate,1);

        present = zhash_table_lookup(l_ref->rate_limit_pick_first_tbl,
                                customer_id_str,
                                strlen(customer_id_str),
                                NULL);
        if ( parent_rate_limited && present != NULL && drop_max && !rate_limited) {
            max_rx_logs_rate = UINT64_MAX;
            picked_max_rx_logs_rate = rx_logs_rate;
            max_rate = entry->rate;
        }

        if ( rate_limited ) {
            desc->is_active = 1;
            if ( !parent_rate_limited ) {
                entry->rate->is_rate_limit_triggered = 0;
                entry->rate->rate_limit_start_time = 0;
                desc->is_active = 0;
                desc->last_rate_limit_end_time_s = now;
            } else {
                if ( entry->rate->is_trans_log ) {
                    if ( entry->rate->type == PORTGROUP ) {
                        ZPATH_MUTEX_LOCK(&(entry->rate->parent->parent->lock),__FILE__,__LINE__);
                        entry->rate->parent->parent->sum_picked_buckets_rate_curr += entry->rate->rx_logs_rate;
                        ZPATH_MUTEX_UNLOCK(&(entry->rate->parent->parent->lock),__FILE__,__LINE__);
                    }
                } else {
                    ZPATH_MUTEX_LOCK(&(entry->rate->parent->lock),__FILE__,__LINE__);
                    entry->rate->parent->sum_picked_buckets_rate_curr += entry->rate->rx_logs_rate;
                    ZPATH_MUTEX_UNLOCK(&(entry->rate->parent->lock),__FILE__,__LINE__);
                }
            }
        }
        add_stats_desc_to_queue(desc);
        present = zhash_table_lookup(l_ref->rate_limit_skiplist_tbl,
                                customer_id_str,
                                strlen(customer_id_str),
                                NULL);
        if (  present != NULL || !drop_max )
            goto skip_max;

        if ( rx_logs_rate > max_rx_logs_rate && !rate_limited ){
            max_rx_logs_rate = rx_logs_rate;
            picked_max_rx_logs_rate = rx_logs_rate;
            max_rate = entry->rate;
        }
        skip_max:
            entry->rate->rx_logs = 0;
            entry->rate->drop_child_max = drop_max;
            ZPATH_MUTEX_UNLOCK(&(entry->rate->lock),__FILE__,__LINE__);
    }
    ZPATH_MUTEX_UNLOCK(&(rate_list->lock),__FILE__,__LINE__);

    if ( max_rate == NULL ) {
        return ZPATH_RESULT_NO_ERROR;
    }
    struct rate_bucket_stats_desc *desc = get_stats_desc(max_rate,1);
    ZPATH_MUTEX_LOCK(&(max_rate->lock),__FILE__,__LINE__);
    if ( max_rate->parent->is_rate_limit_triggered && !max_rate->is_rate_limit_triggered ) {
        max_rate->is_rate_limit_triggered = 1;
        max_rate->rate_limit_start_time = now;
        if ( max_rate->is_trans_log ) {
            if ( max_rate->type == PORTGROUP ) {
                ZPATH_MUTEX_LOCK(&(max_rate->parent->parent->lock),__FILE__,__LINE__);
                max_rate->parent->parent->sum_picked_buckets_rate_curr += picked_max_rx_logs_rate;
                ZPATH_MUTEX_UNLOCK(&(max_rate->parent->parent->lock),__FILE__,__LINE__);
            }
        } else {
            ZPATH_MUTEX_LOCK(&(max_rate->parent->lock),__FILE__,__LINE__);
            max_rate->parent->sum_picked_buckets_rate_curr += picked_max_rx_logs_rate;
            ZPATH_MUTEX_UNLOCK(&(max_rate->parent->lock),__FILE__,__LINE__);
        }
        desc->is_active = 1;
        desc->activation_count = 1;
        desc->last_rate_limit_start_time_s = now;
    } else if (!max_rate->parent->is_rate_limit_triggered && max_rate->is_rate_limit_triggered ){
        max_rate->is_rate_limit_triggered = 0;
        max_rate->rate_limit_start_time = 0;
        desc->is_active = 0;
        desc->last_rate_limit_end_time_s = now;
    }
    add_stats_desc_to_queue(desc);
    max_rate->rx_logs = 0;
    ZPATH_MUTEX_UNLOCK(&(max_rate->lock),__FILE__,__LINE__);
    return ZPATH_RESULT_NO_ERROR;
}

static int topic_rate_walk(void *cookie, void *object, void* key, size_t key_len)
{
    struct rate_bucket *rate = (struct rate_bucket*) object;
    struct rate_limit_tbls_ref *l_ref = (struct rate_limit_tbls_ref *) cookie;
    int64_t now = epoch_s();

    ZPATH_MUTEX_LOCK(&(rate->lock),__FILE__,__LINE__);
    rate->rx_log_list_count ++;
    rate->rx_log_list_sum += (rate->rx_logs / RATE_CALC_TIME_PERIOD_S);
    struct rx_logs_list* newLog = NATURAL_MALLOC(sizeof(struct rx_logs_list));
    newLog->rx_logs = rate->rx_logs;
    ZTAILQ_INSERT_TAIL(&(rate->queue), newLog, list);
    if(rate->rx_log_list_count > 60) {
        rate->rx_log_list_count --;
        struct rx_logs_list* oldest = ZTAILQ_FIRST(&(rate->queue));
        rate->rx_log_list_sum -= (oldest->rx_logs / RATE_CALC_TIME_PERIOD_S);
        ZTAILQ_REMOVE(&(rate->queue), oldest, list);
        ZLIB_FREE(oldest);
    }
    rate->rx_logs_rate = rate->rx_log_list_sum / rate->rx_log_list_count;
    struct rate_bucket_stats_desc *desc = get_stats_desc(rate,1);
    int64_t tot_rate_limit_time = rate->rate_limit_start_time +
                                     l_ref->rate_limit_config->rate_limit_min_duration;
    // If this topic is already rate limited, let us check if it is below hihg watermark
    // If it is below high watermark, we do not need to pick anymore buckets.
    // So we set drop_child_max to 0
    // Moreoever if the rate is greater than low watermark but below high water mark
    // for the duration specified in config, we can stop rate limiting.
    if( rate->rx_logs_rate < rate->sum_picked_buckets_rate_prev ) {
        rate->sum_picked_buckets_rate_prev = rate->rx_logs_rate;
    }
    uint64_t log_rate_delta = rate->rx_logs_rate - rate->sum_picked_buckets_rate_prev;
    if ( rate->is_rate_limit_triggered){
        desc->is_active = 1;
        if (rate->rx_logs_rate >= rate->high_watermark ) {
            if ( log_rate_delta >= rate->high_watermark  ) {
            // Continue to pick new child buckets for rate limit
            // since rate is still not lower than high water mark
                rate->drop_child_max = 1;
            } else {
                rate->drop_child_max = 0;
            }
        } else if (rate->rx_logs_rate < rate->high_watermark && rate->rx_logs_rate >= rate->low_watermark ) {
            if (rate->rate_limit_start_time && ( tot_rate_limit_time < now ) ) {
                rate->is_rate_limit_triggered = 0;
                rate->rate_limit_start_time = 0;
                desc->is_active = 0;
                desc->last_rate_limit_end_time_s = now;
            }
            rate->drop_child_max = 0;
        } else if ( rate->rx_logs_rate < rate->low_watermark) {
            // Rate went below low water mark. Stop rate limiting this topic.
            rate->is_rate_limit_triggered = 0;
            rate->rate_limit_start_time = 0;
            desc->is_active = 0;
            desc->last_rate_limit_end_time_s = now;
            rate->drop_child_max = 0;
        }
    } else {
        if (rate->rx_logs_rate >= rate->high_watermark ) {
            rate->is_rate_limit_triggered = 1;
            rate->rate_limit_start_time = now;
            desc->is_active = 1;
            desc->activation_count = 1;
            desc->last_rate_limit_start_time_s = now;
            rate->drop_child_max = 0;
        }
    }
    rate->rx_logs = 0;
    ZPATH_MUTEX_UNLOCK(&(rate->lock),__FILE__,__LINE__);
    add_stats_desc_to_queue(desc);
    return ZPATH_RESULT_NO_ERROR;
}

static int topic_rate_reset_walk(void *cookie, void *object, void* key, size_t key_len)
{
    struct rate_bucket *rate = (struct rate_bucket*) object;
    ZPATH_MUTEX_LOCK(&(rate->lock),__FILE__,__LINE__);
    rate->sum_picked_buckets_rate_prev = rate->sum_picked_buckets_rate_curr;
    rate->sum_picked_buckets_rate_curr = 0;
    ZPATH_MUTEX_UNLOCK(&(rate->lock),__FILE__,__LINE__);
    return ZPATH_RESULT_NO_ERROR;
}

void calc_rates(int sock, short events, void *cookie)
{
    //zthread_heartbeat(NULL);
    struct rate_limit_tbls_ref *l_ref = g_rate_limit_tbls;
    struct zhash_table* tbls[2];
    tbls[0] = l_ref->customer_list_per_topic_tbl;
    tbls[1] = l_ref->port_group_list_per_topic_customer_tbl;

    int64_t walk_key = 0;
    if (l_ref->rate_limit_config && !l_ref->rate_limit_config->rate_limit_enabled )
        return;

    while ( zhash_table_walk(l_ref->topic_rate_tbl,
                             &walk_key,
                             topic_rate_walk,
                             l_ref ) == ZHASH_RESULT_INCOMPLETE ) {}
    for ( int i = 0; i < 2; i++ ){
        walk_key = 0;
        while ( zhash_table_walk(tbls[i],
                             &walk_key,
                             rate_bucket_list_walk,
                             l_ref ) == ZHASH_RESULT_INCOMPLETE ) {}
    }
    walk_key = 0;
    while ( zhash_table_walk(l_ref->topic_rate_tbl,
                             &walk_key,
                             topic_rate_reset_walk,
                             NULL ) == ZHASH_RESULT_INCOMPLETE ) {}
}

static void empty_queue_wake(void *cookie1, void *cookie2)
{
    struct kafka_conn_state *k_conn = cookie1;

    /* Wake. Incarnation check will handle removed connection */
    fohh_log_custom_wake(k_conn->custom_connection,
                         k_conn->custom_connection_incarnation);

    /* Drop ref count for this callback */
    __sync_sub_and_fetch_8(&(k_conn->ref_cnt), 1);

    /* Check if the k_conn should be gone */
    kafka_conn_destroy_if_possible(k_conn);
}

/* To be strictly called only with k_conn lock held */
static void setup_kconn_for_logging(struct kafka_conn_state *k_conn, char *topic_name)
{
    char *name = NULL;
    char *token_context1 = NULL;
    char *token_context2 = NULL;
    char parse[128] = {'\0'};
    char stat_name[256] = {'\0'};

    snprintf(parse, sizeof(parse), "%s", k_conn->name);

    /* Sample string:
     * "[10.18.5.197]:445;et-backup-restore-service.dev.zpath.net:[10.18.14.186]:34625;1"
     */
    char *token = strtok_r(parse, ";", &token_context1);
    if (token) {
        token = strtok_r(NULL, ";", &token_context1); /* Grab the second field */
        if (token) {
            name = strtok_r(token, ":", &token_context2); /* Grab the first field which is likely the full identifier */
        }
    }

    snprintf(stat_name, sizeof(stat_name), "%s-%s", topic_name, name ? name : k_conn->name);

    k_conn->conn_desc = k_conn->name;
    k_conn->k_conn_rstruct = argo_log_register_structure(zpath_stats_collection,
                                                         stat_name,
                                                         AL_INFO,
                                                         5 * 60 * 1000 * 1000,     /* every 5 mins */
                                                         k_conn_stats_description,
                                                         k_conn,                           /* data */
                                                         0, /* we just started, nothing to log yet */
                                                         NULL,                         /* callback */
                                                         NULL);                          /* cookie */

}

static int free_rate_limit_tbl_entry(void *cookie,
                                        void *object,
                                        void* key,
                                        size_t key_len)
{
    int *type = (int *) cookie;
    if ( *type ==  TOPIC || *type == CUSTOMER ){
        struct rate_bucket *rate = (struct rate_bucket *)object;
        struct rx_logs_list* logentry = NULL;
        while((logentry = ZTAILQ_FIRST(&(rate->queue)))) {
            ZTAILQ_REMOVE(&(rate->queue), logentry, list);
            ZLIB_FREE(logentry);
        }
        NATURAL_FREE(rate->topic);
        NATURAL_FREE(rate);
    } else if ( *type == PG_BUCKETLIST || *type == CUST_BUCKETLIST ) {
        struct rate_bucket_list *pg_rate_list = (struct rate_bucket_list *) object;
        struct rate_bucket_list_entry *entry = NULL;
        struct rx_logs_list* logentry = NULL;
        // At this point, only the garbage collector will access this. So no locks required.
        while ( ( entry = ZLIST_FIRST(&(pg_rate_list->list)) ) ){
            ZLIST_REMOVE(entry,list);
            // rate in entry is already removed by customer topic rate tbl free for CUST_BUCKETLIST
            if ( *type == PG_BUCKETLIST ) {
                while((logentry = ZTAILQ_FIRST(&(entry->rate->queue)))) {
                    ZTAILQ_REMOVE(&(entry->rate->queue), logentry, list);
                    ZLIB_FREE(logentry);
                }
                NATURAL_FREE(entry->rate->topic);
                NATURAL_FREE(entry->rate->port_group_name);
                NATURAL_FREE(entry->rate);
            }
            NATURAL_FREE(entry);
        }
        NATURAL_FREE(pg_rate_list);
    }
    return ZPN_RESULT_NO_ERROR;
}

static void free_rate_limit_garbage_desc( struct rate_limit_garbage_desc* desc) {
    argo_object_release(desc->rate_limit_tbls->rate_limit_config_obj);
    int64_t walk_key = 0;
    rate_bucket_type_t table_type = TOPIC;
    while ( zhash_table_walk(desc->rate_limit_tbls->topic_rate_tbl,
                             &walk_key,
                             free_rate_limit_tbl_entry,
                             &table_type ) == ZHASH_RESULT_INCOMPLETE ) {}
    zhash_table_free(desc->rate_limit_tbls->topic_rate_tbl);
    table_type = CUSTOMER;
    while ( zhash_table_walk(desc->rate_limit_tbls->customer_topic_rate_tbl,
                            &walk_key,
                            free_rate_limit_tbl_entry,
                            &table_type ) == ZHASH_RESULT_INCOMPLETE ) {}
    zhash_table_free(desc->rate_limit_tbls->customer_topic_rate_tbl);

    zhash_table_free(desc->rate_limit_tbls->port_group_customer_topic_rate_tbl);

    table_type = CUST_BUCKETLIST;
    while ( zhash_table_walk(desc->rate_limit_tbls->customer_list_per_topic_tbl,
                            &walk_key,
                            free_rate_limit_tbl_entry,
                            &table_type ) == ZHASH_RESULT_INCOMPLETE ) {}
    zhash_table_free(desc->rate_limit_tbls->customer_list_per_topic_tbl);
    table_type = PG_BUCKETLIST;
    while ( zhash_table_walk(desc->rate_limit_tbls->port_group_list_per_topic_customer_tbl,
                            &walk_key,
                            free_rate_limit_tbl_entry,
                            &table_type ) == ZHASH_RESULT_INCOMPLETE ) {}
    zhash_table_free(desc->rate_limit_tbls->port_group_list_per_topic_customer_tbl);

    zhash_table_free(desc->rate_limit_tbls->rate_limit_skiplist_tbl);
    zhash_table_free(desc->rate_limit_tbls->rate_limit_pick_first_tbl);
    NATURAL_FREE(desc->rate_limit_tbls);
}

static void rate_limit_stats_collector(int sock, short events, void *cookie){
    struct rate_bucket_stats_desc *first = NULL , *next = NULL;
    struct zhash_table *l_stats_tbl = NULL;
    // Only if we can lock the table, let us lock the queue so we wont block datapath
    ZPATH_MUTEX_LOCK(&(g_rate_limit_stats_tbl_lock), __FILE__, __LINE__);
    ZPATH_MUTEX_LOCK(&(g_rate_limit_stats_q.lock), __FILE__, __LINE__);
    if ( ZTAILQ_EMPTY(&(g_rate_limit_stats_q.queue))){
        goto end;
    }
    ZTAILQ_FOREACH_SAFE(first, &(g_rate_limit_stats_q.queue), list , next) {
        if ( first->type == TOPIC ) {
            l_stats_tbl = g_topic_rate_stats_tbl;
        } else if ( first->type == CUSTOMER ) {
            l_stats_tbl = g_cust_topic_rate_stats_tbl;
        } else if ( first->type == PORTGROUP ) {
            l_stats_tbl = g_port_group_cust_topic_rate_stats_tbl;
        } else {
            ZPATH_LOG(AL_ERROR,"Unknown type in rate limit stats desc");
            goto stats_skip;
        }
        struct rate_bucket_stats *l_stats = zhash_table_lookup(l_stats_tbl,
                                                                first->key,
                                                                strlen(first->key),
                                                                 NULL);
        if ( !l_stats ) {
            l_stats = NATURAL_CALLOC(sizeof(*l_stats));
            g_rate_limit_stats_lines_count++;
            int key_len = strlen(first->key);
            char *real_key = NATURAL_STRDUP(first->key,key_len);
            zhash_table_store(l_stats_tbl,real_key,key_len,1,l_stats);
        }
        if ( first->desc_origin == 1 )  {
                l_stats->rate_limit_active = first->is_active;
                l_stats->rate_limit_activation_count += first->activation_count;
                if ( first->last_rate_limit_start_time_s )
                    l_stats->last_rate_limit_start_time_s = first->last_rate_limit_start_time_s;
                if( first->last_rate_limit_end_time_s )
                    l_stats->last_rate_limit_end_time_s = first->last_rate_limit_end_time_s;
                l_stats->log_rate = first->rate;
        } else {
                l_stats->logs_dropped += first->log_dropped;
                l_stats->curr_logs_dropped += first->log_dropped;
                if ( first->log_dropped ) {
                    l_stats->bytes_dropped += first->bytes_serialized;
                    l_stats->curr_bytes_dropped += first->bytes_serialized;
                }
                l_stats->total_rx_logs += first->log_rxd;
                l_stats->curr_rx_logs += first->log_rxd;
                l_stats->total_bytes += first->bytes_serialized;
                l_stats->curr_bytes += first->bytes_serialized;
        }


        stats_skip:
            ZTAILQ_REMOVE(&(g_rate_limit_stats_q.queue),first,list);
            g_rate_limit_stats_q.curr_used--;
            NATURAL_FREE(first->key);
            memset(first,0,sizeof(*first));
            g_rate_limit_stats_q.curr_free++;
            ZTAILQ_INSERT_TAIL(&(g_rate_limit_stats_q.free_queue),first,free_list);
        }
    end:
    ZPATH_MUTEX_UNLOCK(&(g_rate_limit_stats_q.lock), __FILE__, __LINE__);
    ZPATH_MUTEX_UNLOCK(&(g_rate_limit_stats_tbl_lock), __FILE__, __LINE__);

}

static void rate_limit_garbage_collector(int sock, short events, void *cookie){
    struct rate_limit_garbage_desc *first, *next;
    int64_t now = epoch_s();
    ZPATH_MUTEX_LOCK(&(g_rate_limit_garbage.lock), __FILE__, __LINE__);
    g_rate_limit_garbage.last_run_time = now;
    if ( !ZTAILQ_EMPTY(&(g_rate_limit_garbage.queue))){
        ZTAILQ_FOREACH_SAFE(first, &(g_rate_limit_garbage.queue), list , next) {
            if ( (now - first->free_req_time_s) > RATE_LIMIT_GARBAGE_STAY_TIME_S ) {
                free_rate_limit_garbage_desc(first);
                ZTAILQ_REMOVE(&(g_rate_limit_garbage.queue),first,list);
                NATURAL_FREE(first);
                g_rate_limit_garbage.count--;
                g_rate_limit_garbage.total_removed++;
            }
        }
    }
    ZPATH_MUTEX_UNLOCK(&(g_rate_limit_garbage.lock), __FILE__, __LINE__);
}

static int rate_buckets_create(struct rate_limit_tbls_ref *l_ref,
                                    char* topic_str,
                                    char* topic_customer_id_str,
                                    int64_t customer_gid ,
                                    uint8_t trans_log)
{
    uint8_t add_new_customer = 0;
    struct rate_bucket *rate = NULL;
    char *strtok_ptr = NULL;
    if ( ( l_ref->rate_limit_config && !(l_ref->rate_limit_config->rate_limit_enabled) ) ||
             topic_str == NULL || customer_gid == 0 )
        return ZPN_RESULT_NO_ERROR;

    // Static hash table; So no lock required.
    if ( zhash_table_lookup(l_ref->topic_rate_tbl,topic_str,strlen(topic_str),NULL) == NULL )
        return ZPN_RESULT_NO_ERROR;

    ZPATH_MUTEX_LOCK(&(l_ref->customer_topic_rate_tbl_lock), __FILE__, __LINE__);
    rate = zhash_table_lookup(l_ref->customer_topic_rate_tbl,
                                topic_customer_id_str,
                                strlen(topic_customer_id_str),
                                NULL);
    if ( rate == NULL )
    {
        // This is a customer who is sending logs for the first time
        add_new_customer = 1;
        rate = NATURAL_CALLOC(sizeof(*rate));
        rate->lock = ZPATH_MUTEX_INIT;
        rate->topic = NATURAL_STRDUP(topic_str,strlen(topic_str));
        rate->port_group_name = "CUSTOMER_ALL";
        rate->type = CUSTOMER;
        rate->is_trans_log = trans_log;
        rate->customer_id = customer_gid;
        rate->rx_log_list_sum = 0;
        rate->rx_log_list_count = 0;
        ZTAILQ_INIT(&(rate->queue));
        rate->parent = zhash_table_lookup(l_ref->topic_rate_tbl,topic_str,strlen(topic_str),NULL);
        zhash_table_store(l_ref->customer_topic_rate_tbl,
                            topic_customer_id_str,
                            strlen(topic_customer_id_str),
                            0,
                            rate);
    }
    ZPATH_MUTEX_UNLOCK(&(l_ref->customer_topic_rate_tbl_lock), __FILE__, __LINE__);
    if ( add_new_customer ){
        // Customer sending logs for the first time
        // So we need to add him to the customer_topic rate table
        // We also need to place this customer in the list in customer_list per topic tbl
        // if this log is a transaction logs, we also need to create the port groups
        struct rate_bucket_list_entry *entry  = NATURAL_CALLOC(sizeof(*entry));
        entry->rate = rate;
        struct rate_bucket_list *cust_rate_buckets_list = NULL;
        // Static hash table; So no lock required.
        cust_rate_buckets_list = zhash_table_lookup(l_ref->customer_list_per_topic_tbl,
                                                    topic_str,
                                                    strlen(topic_str),
                                                    NULL);
        if ( !cust_rate_buckets_list )
        {
            ZPATH_LOG(AL_ERROR, "Rate limit init error detected. List of customer rates not found");
            NATURAL_FREE(entry);
            return ZPN_RESULT_ERR;
        }
        ZPATH_MUTEX_LOCK(&(cust_rate_buckets_list->lock), __FILE__, __LINE__);
        ZLIST_INSERT_HEAD(&(cust_rate_buckets_list->list),entry,list);
        ZPATH_MUTEX_UNLOCK(&(cust_rate_buckets_list->lock), __FILE__, __LINE__);
        if ( ! trans_log) {
            // If not a transaction log, no port groups are required. So we return from here.
            return ZPN_RESULT_NO_ERROR;
        }
        // First we create a list for the port groups; This list is per customer
        struct rate_bucket_list *pg_rate_list = NATURAL_CALLOC(sizeof(struct rate_bucket_list ));
        pg_rate_list->lock = ZPATH_MUTEX_INIT;
        ZLIST_INIT(&(pg_rate_list->list));
        size_t i = 0;
        for ( i = 0; i < l_ref->port_group_count; i++ ) {
            struct rate_bucket *pg_rate = NATURAL_CALLOC(sizeof(*pg_rate));
            pg_rate->lock = ZPATH_MUTEX_INIT;
            pg_rate->topic = NATURAL_STRDUP(topic_str,strlen(topic_str));
            pg_rate->customer_id = customer_gid;
            pg_rate->type = PORTGROUP;
            pg_rate->rx_log_list_count = 0;
            pg_rate->rx_log_list_sum = 0;
            ZTAILQ_INIT(&(pg_rate->queue));
            rate->is_trans_log = trans_log;
            pg_rate->port_group_name = NATURAL_STRDUP(l_ref->port_group_confs[i].name,
                                                    strlen(l_ref->port_group_confs[i].name));
            pg_rate->parent = rate;
            struct rate_bucket_list_entry *entry = NATURAL_CALLOC(sizeof(*entry));
            entry->rate = pg_rate;
            // Lets add the rate bucket for this port group to the list
            ZLIST_INSERT_HEAD(&(pg_rate_list->list),entry,list);
            char *protocol = l_ref->port_group_confs[i].protocol;
            char *port = strtok_r(l_ref->port_group_confs[i].ports,",",&strtok_ptr);
            while ( port ){
                // here the same rate bucket created above is mapped to its port and protocol
                // This will be used in updating the logs received using hash lookup instead of list traversal
                // The logs received is incremented in rate_throttle_check function
                // This table doesnt own the rate bucket of the port group; So it should not free it.
                // The actual owner of the port group rate bucket will be port_group_list_per_topic_customer_tbl
                char topic_customer_id_port_protocol_str[128] = {'\0'};
                snprintf(topic_customer_id_port_protocol_str,
                            sizeof(topic_customer_id_port_protocol_str),
                            "%s_%"PRId64"_%s_%s",
                            topic_str,
                            customer_gid,protocol,port);
                ZPATH_MUTEX_LOCK(&(l_ref->port_group_customer_topic_rate_tbl_lock), __FILE__, __LINE__);
                zhash_table_store(l_ref->port_group_customer_topic_rate_tbl,
                                topic_customer_id_port_protocol_str,
                                strlen(topic_customer_id_port_protocol_str),
                                0,
                                pg_rate);
                ZPATH_MUTEX_UNLOCK(&(l_ref->port_group_customer_topic_rate_tbl_lock), __FILE__, __LINE__);
                port = strtok_r(NULL,",",&strtok_ptr);
            }
        }
        // For default i.e. everything else bucket
        struct rate_bucket *pg_rate = NATURAL_CALLOC(sizeof(*pg_rate));
        pg_rate->lock = ZPATH_MUTEX_INIT;
        struct rate_bucket_list_entry *entry1  = NATURAL_CALLOC(sizeof(*entry1));
        entry1->rate = pg_rate;
        pg_rate->parent = rate;
        pg_rate->type = PORTGROUP;
        pg_rate->rx_log_list_count = 0;
        pg_rate->rx_log_list_sum = 0;
        ZTAILQ_INIT(&(pg_rate->queue));
        pg_rate->is_trans_log = trans_log;
        pg_rate->customer_id = customer_gid;
        pg_rate->topic = NATURAL_STRDUP(topic_str,strlen(topic_str)+1);
        pg_rate->port_group_name = "default";
        ZLIST_INSERT_HEAD(&(pg_rate_list->list),entry1,list);
        char topic_customer_id_port_protocol_str[128] = {'\0'};
        snprintf(topic_customer_id_port_protocol_str,
                    sizeof(topic_customer_id_port_protocol_str),
                    "%s_%"PRId64"_default",
                    topic_str,customer_gid);
        ZPATH_MUTEX_LOCK(&(l_ref->port_group_customer_topic_rate_tbl_lock), __FILE__, __LINE__);
        zhash_table_store(l_ref->port_group_customer_topic_rate_tbl,
                            topic_customer_id_port_protocol_str,
                            strlen(topic_customer_id_port_protocol_str),
                            0,
                            pg_rate );
        ZPATH_MUTEX_UNLOCK(&(l_ref->port_group_customer_topic_rate_tbl_lock), __FILE__, __LINE__);
        ZPATH_MUTEX_LOCK(&(l_ref->port_group_list_per_topic_customer_tbl_lock), __FILE__, __LINE__);
        zhash_table_store(l_ref->port_group_list_per_topic_customer_tbl,
                        topic_customer_id_str,
                        strlen(topic_customer_id_str),
                        0,
                        pg_rate_list );
        ZPATH_MUTEX_UNLOCK(&(l_ref->port_group_list_per_topic_customer_tbl_lock), __FILE__, __LINE__);

    }
    return ZPN_RESULT_NO_ERROR;
}

static int rate_bucket_update( struct rate_bucket* rx_bucket,
                                int* no_drop,
                                uint8_t* rate_limited,
                                size_t bytes_serialized,
                                struct rate_bucket_stats_desc **desc_arr,
                                int desc_count ) {
    ZPATH_MUTEX_LOCK(&(rx_bucket->lock), __FILE__, __LINE__);
    rx_bucket->rx_logs++;
    rx_bucket->curr_rx_logs++;
    if (rx_bucket->curr_rx_logs >= 1000 ) {
        *no_drop = 1;
        rx_bucket->curr_rx_logs = 0;
    }
    if ( rx_bucket->is_rate_limit_triggered )
        *rate_limited = 1;
    ZPATH_MUTEX_UNLOCK(&(rx_bucket->lock), __FILE__, __LINE__);

    ZPATH_MUTEX_LOCK(&(g_rate_limit_stats_q.lock), __FILE__, __LINE__);
    for ( int i = TOPIC; i <= desc_count; i++ ) {
        desc_arr[i]->bytes_serialized = bytes_serialized;
        desc_arr[i]->log_rxd = 1;
        desc_arr[i]->log_dropped = *rate_limited;
        ZTAILQ_INSERT_TAIL(&(g_rate_limit_stats_q.queue),desc_arr[i],list);
        g_rate_limit_stats_q.curr_used++;
    }
    ZPATH_MUTEX_UNLOCK(&(g_rate_limit_stats_q.lock), __FILE__, __LINE__);
    return ZPN_RESULT_NO_ERROR;
}
// rate_throttle_check
// All we do in rate_throttle_check is pick the correct rate buckets from the hash table
// Increase their rx_logs count by 1
// Check if the rate bucket has been rate limited
// if so check if this is the 1000th log in the bucket. If it is mark for sending to kafka
// return the rate limit state to caller.
static int rate_throttle_check( struct rate_limit_tbls_ref *l_ref,
                                size_t bytes_serialized,
                                char* topic_str,
                                char* topic_customer_id_str,
                                int64_t customer_gid,
                                int64_t ip_proto,
                                int64_t d_port,
                                int* no_drop )
{

    struct rate_bucket  *rx_rate_topic = NULL,
                        *rx_rate_customer = NULL,
                        *rx_rate_port_group = NULL;
    struct rate_bucket_stats_desc   *stats_desc_arr[4] = { 0 };
    uint8_t rate_limited = 0;
    char curr_topic_customer_id_port_protocol_str[128] = {'\0'};

    if ( ( l_ref->rate_limit_config && !(l_ref->rate_limit_config->rate_limit_enabled) ) ||
            topic_str == NULL || customer_gid == 0 )
        return FOHH_RESULT_NO_ERROR;


    rx_rate_topic = zhash_table_lookup(l_ref->topic_rate_tbl,topic_str, strlen(topic_str), NULL);
    if ( rx_rate_topic == NULL ) {
        goto rate_done;
    } else {
        __sync_add_and_fetch_8(&(rx_rate_topic->rx_logs),1);
    }
    ZPATH_MUTEX_LOCK(&(l_ref->customer_topic_rate_tbl_lock), __FILE__, __LINE__);
    rx_rate_customer = zhash_table_lookup(l_ref->customer_topic_rate_tbl,
                                            topic_customer_id_str,
                                            strlen(topic_customer_id_str),
                                            NULL);
    ZPATH_MUTEX_UNLOCK(&(l_ref->customer_topic_rate_tbl_lock), __FILE__, __LINE__);
    if ( rx_rate_customer == NULL ) {
        //ZPATH_LOG(AL_CRITICAL,"Rate structure for topic customer id %s not initialized ", topic_customer_id_str);
         __sync_add_and_fetch_8(&(g_customer_rate_bucket_not_found),1);
        return ZPATH_RESULT_ERR;
    } else {
        if ( !rx_rate_customer->is_trans_log) { // Never changes once set so no lock
            stats_desc_arr[TOPIC] = get_stats_desc(rx_rate_topic,0);
            stats_desc_arr[CUSTOMER] = get_stats_desc(rx_rate_customer,0);
            rate_bucket_update(rx_rate_customer,no_drop,&rate_limited,bytes_serialized,stats_desc_arr,CUSTOMER);
            goto rate_done;
        } else {
             __sync_add_and_fetch_8(&(rx_rate_customer->rx_logs), 1);
        }
    }

    char default_port_grp_for_customer[128] = {'\0'};
    if ( ip_proto == IPPROTO_TCP ) {
        snprintf(curr_topic_customer_id_port_protocol_str,
                    sizeof(curr_topic_customer_id_port_protocol_str),
                    "%s_%"PRId64"_tcp_%"PRId64,
                    topic_str,
                    customer_gid,d_port);
    } else if (ip_proto == IPPROTO_UDP ) {
        snprintf(curr_topic_customer_id_port_protocol_str,
                sizeof(curr_topic_customer_id_port_protocol_str),
                "%s_%"PRId64"_udp_%"PRId64,
                topic_str,customer_gid,d_port);
    } else {
        snprintf(curr_topic_customer_id_port_protocol_str,
                    sizeof(curr_topic_customer_id_port_protocol_str),
                    "%s_%"PRId64"_default",
                    topic_str,customer_gid);
    }
    ZPATH_MUTEX_LOCK(&(l_ref->port_group_customer_topic_rate_tbl_lock), __FILE__, __LINE__);
    rx_rate_port_group = zhash_table_lookup(l_ref->port_group_customer_topic_rate_tbl,
                                            curr_topic_customer_id_port_protocol_str,
                                            strlen(curr_topic_customer_id_port_protocol_str),
                                            NULL);
    ZPATH_MUTEX_UNLOCK(&(l_ref->port_group_customer_topic_rate_tbl_lock), __FILE__, __LINE__);
    if ( rx_rate_port_group == NULL ) {
        sprintf(default_port_grp_for_customer,"%s_%"PRId64"_default",topic_str,customer_gid);
        ZPATH_MUTEX_LOCK(&(l_ref->port_group_customer_topic_rate_tbl_lock), __FILE__, __LINE__);
        rx_rate_port_group = zhash_table_lookup(l_ref->port_group_customer_topic_rate_tbl,
                                    default_port_grp_for_customer,
                                    strlen(default_port_grp_for_customer),
                                    NULL);
        ZPATH_MUTEX_UNLOCK(&(l_ref->port_group_customer_topic_rate_tbl_lock), __FILE__, __LINE__);
        if ( rx_rate_port_group == NULL ) {
            __sync_add_and_fetch_8(&(g_def_pg_rate_bucket_not_found),1);
            //ZPATH_LOG(AL_CRITICAL,"Default rate structure not found for %s ", default_port_grp_for_customer );
            return ZPATH_RESULT_ERR;
        }
    }
    stats_desc_arr[TOPIC] = get_stats_desc(rx_rate_topic,0);
    stats_desc_arr[CUSTOMER] = get_stats_desc(rx_rate_customer,0);
    stats_desc_arr[PORTGROUP] = get_stats_desc(rx_rate_port_group,0);
    rate_bucket_update(rx_rate_port_group,no_drop,&rate_limited,bytes_serialized,stats_desc_arr,PORTGROUP);
    rate_done:
    if ( rate_limited ) {
        return RATE_LIMITED;
    } else {
        return NOT_RATE_LIMITED;
    }
}

void get_customer_id_from_obj(struct argo_object* object, int64_t * customer_gid )
{
    if (!argo_object_read_int_by_column_name(object,"g_cst",customer_gid)) {
        //ZPATH_LOG(AL_NOTICE, "customer_gid: %"PRId64, customer_gid );
    } else if (!argo_object_read_int_by_column_name(object,"customer_id",customer_gid)) {
        //ZPATH_LOG(AL_NOTICE, "customer_gid: %"PRId64, customer_gid );
    } else if (!argo_object_read_int_by_column_name(object,"customer_gid",customer_gid)) {
        //ZPATH_LOG(AL_NOTICE, "customer_gid: %"PRId64, customer_gid );
    }
}

void check_update_trans_log(struct argo_object* object ,int64_t* ip_proto,int64_t* d_port, uint8_t* trans_log)
{
    if (!strcmp(argo_object_get_type(object), "zpn_trans_log")) {
        if (argo_object_read_int_by_column_name(object,"s_port",d_port)) {
            //ZPATH_LOG(AL_NOTICE, "d_port: %"PRId64, *d_port );
        }
        if (argo_object_read_int_by_column_name(object,"ip_proto",ip_proto)) {
           //ZPATH_LOG(AL_NOTICE, "ip_proto: %"PRId64, *ip_proto );
        }
        *trans_log = 1;
    }
}

void set_kafka_consistent_partition_key(struct kafka_topic *k_topic,
                                        struct argo_object* object_l1,
                                        struct argo_object* object_l2,
                                        char **key,
                                        const char *log_type )
{
    /* If less than 0, than we are specifically seeking ONLY pure random partitioning (no hash-key) to avoid hotspotting
       If consistent partitioning is needed, try finding key for it in this order: mtunnel_id -> tunnel_id -> l_inst
       Else, just uses l_inst as hash-key...since something is better than nothing */
    if (k_topic->consistent >= 0) {
        if (k_topic->consistent) {
            if ((argo_object_read_string_by_column_name(object_l2, "mtunnel_id", key)) &&
                (argo_object_read_string_by_column_name(object_l2, "tunnel_id", key))  &&
                (argo_object_read_string_by_column_name(object_l1, "l_inst", key))) {
                ZPATH_LOG(AL_WARNING, "[Consistent] No partitioning key found for log type %s", log_type);
            }
        } else {
            if (argo_object_read_string_by_column_name(object_l1, "l_inst", key)) {
                ZPATH_LOG(AL_WARNING, "No partitioning key found for log type %s", log_type);
            }
        }
    }

}
static int log_callback_f(struct fohh_log_custom_connection *custom_connection,
                  int64_t custom_connection_incarnation,
                  struct fohh_log_element *log,
                  int64_t *tx_bytes)
{
    __sync_add_and_fetch_8(&(global_natural_stats.msg_count), 1);
    struct kafka_topic *k_topic = NULL;
    struct kafka_topic *slow_topic = NULL;
    rd_kafka_topic_t *topic = NULL;
    int res = 0;
    char *key = NULL;
    struct argo_log *l = NULL;
    struct argo_object *object = NULL;
    char topic_str[64] = {'\0'};
    char topic_customer_id_str[128] = {'\0'};
    int64_t ip_proto = 0;
    int64_t d_port = 0;
    int64_t customer_gid = 0;
    uint8_t trans_log = 0;
    struct rate_limit_tbls_ref *l_ref = g_rate_limit_tbls;

    #ifdef UNIT_TEST
        __sync_add_and_fetch_8(&(logs_rxd),1);
    #endif

    if (!log->topic) {
        ZPATH_LOG(AL_ERROR, "No logging topic specified on received logs");
        return ZPATH_RESULT_ERR;
    }

    k_topic = zhash_table_lookup(all_topics, log->topic, strlen(log->topic), NULL);
    if (!k_topic) {
        ZPATH_LOG(AL_ERROR, "Specified logging topic: %s not permitted", log->topic);
        return ZPATH_RESULT_ERR;
    }
    topic = k_topic->topic;

    /* Check if there have been any critical kafka errors that should
     * cause us to close the connection. This will trigger a
     * status_callback for the connection going away. */
    struct kafka_conn_state *k_conn = fohh_log_custom_get_void(custom_connection);

    if (k_conn->errors_received) {
        ZPATH_LOG(AL_ERROR, "Kafka error; forcing connection closed");
        return ZPATH_RESULT_ERR;
    }

    char *out_buf = NULL;
    size_t out_buf_len = 0;

    res = argo_object_allocate_and_dump(log->object, &out_buf, &out_buf_len, 0);
    if (res) {
        ZPATH_LOG(AL_ERROR, "Argo object allocate and dump failed for [%s] due to error %s",
                                                   k_conn->name, argo_result_string(res));
        __sync_add_and_fetch_8(&(global_natural_stats.argo_deserial_err_count), 1);
        return ZPATH_RESULT_ERR;
    }

    object = log->object;
    l = object->base_structure_void;
    get_customer_id_from_obj(l->l_obj,&customer_gid);
    check_update_trans_log(l->l_obj,&ip_proto,&d_port,&trans_log);
    snprintf(topic_str,sizeof(topic_str),"%s",log->topic);
    snprintf(topic_customer_id_str,sizeof(topic_customer_id_str), "%s_%" PRIu64, log->topic , customer_gid);
    set_kafka_consistent_partition_key(k_topic,object,l->l_obj,&key,l->l_otyp);

    rate_buckets_create(l_ref,
                        topic_str,
                        topic_customer_id_str,
                        customer_gid,
                        trans_log);


    struct kafka_ack *msg = NATURAL_MALLOC(sizeof(*msg));
    msg->custom_connection = custom_connection;
    msg->custom_connection_incarnation = custom_connection_incarnation;
    msg->log_id = log->id;
    msg->log_incarnation = log->incarnation;
    msg->bytes = out_buf_len;
    msg->ack_complete = 0;
    *tx_bytes = out_buf_len;
    msg->k_conn = k_conn;

    int no_drop = 0;
    if ( l_ref->rate_limit_config )  {
        int rate_limit_simulation = l_ref->rate_limit_config->rate_limit_simulation_mode;
        int do_rate_limit = rate_throttle_check(l_ref,
                                                out_buf_len,
                                                topic_str,
                                                topic_customer_id_str,
                                                customer_gid,
                                                ip_proto,
                                                d_port,
                                                &no_drop);
        int slow_queue = l_ref->rate_limit_config->rate_limit_deadletter_topic;
        // Extra var to reduce complexity in the subsequent if condition
        int real_do_rate_limit = 0;
        if ( (do_rate_limit == RATE_LIMITED) && !rate_limit_simulation ) {
            real_do_rate_limit = 1;
        }
        // If slow queue option is present, then let us send to the corresponding slow topic
        if ( slow_queue && real_do_rate_limit ){
            char slow_topic_str[128] = {'\0'};
            snprintf(slow_topic_str,sizeof(slow_topic_str),"%s_slow",topic_str);
            slow_topic = zhash_table_lookup(all_topics, slow_topic_str, strlen(slow_topic_str), NULL);
            if ( slow_topic )
                topic = slow_topic->topic;
            goto queue_msg;
        } else if ( !slow_queue && real_do_rate_limit ) {
            ZPATH_MUTEX_LOCK(&(k_conn->lock), __FILE__, __LINE__);
            k_conn->rate_limited++;
            if ( !no_drop ) {
                msg->ack_complete = 1;
                ZTAILQ_INSERT_TAIL(&(k_conn->acks), msg, list);
                ZPATH_MUTEX_UNLOCK(&(k_conn->lock), __FILE__, __LINE__);
                argo_object_deallocate_dump(out_buf);
                out_buf = NULL;
                return FOHH_RESULT_NO_ERROR;
            } else {
                // Let one message in thousand go to kafka so that we can get the dr_msg_cb
                // This will send all the acks in the queue.
                ZPATH_MUTEX_UNLOCK(&(k_conn->lock), __FILE__, __LINE__);
            }
        }
    }
    queue_msg:
    ZPATH_MUTEX_LOCK(&(k_conn->lock), __FILE__, __LINE__);
    ZTAILQ_INSERT_TAIL(&(k_conn->acks), msg, list);
    k_conn->messages_queued++;
    k_conn->bytes_received += out_buf_len;

    if (!k_conn->topics_received) setup_kconn_for_logging(k_conn, k_topic->name);
    k_conn->topics_received |= (1 << k_topic->topic_index);
    ZPATH_MUTEX_UNLOCK(&(k_conn->lock), __FILE__, __LINE__);

    __sync_add_and_fetch_8(&(global_natural_stats.produce_count), 1);


    __sync_add_and_fetch_8(&(k_conn->ref_cnt), 1);
    /* Try send to kafka!! */
    res = rd_kafka_produce(topic,
                           RD_KAFKA_PARTITION_UA,  /* UnAssigned partitioner to let partition key be used to select partition */
                           RD_KAFKA_MSG_F_FREE,    /* Flag to let Kafka lib free message buffer when the msg gets delivered */
                           out_buf,                /* Do **NOT** free out_buf, it will be freed by Kafka lib using plain 'free' */
                           out_buf_len,
                           key,                    //const void *  key,
                           key ? strlen(key) : 0,  //size_t  keylen,
                           msg);                   //void *  msg_opaque

    if (res) {
        int is_empty = 0;
        ZPATH_MUTEX_LOCK(&(k_conn->lock), __FILE__, __LINE__);
        k_conn->messages_queued--;
        ZTAILQ_REMOVE(&(k_conn->acks), msg, list);
        if (!ZTAILQ_FIRST(&(k_conn->acks))) is_empty = 1;
        ZPATH_MUTEX_UNLOCK(&(k_conn->lock), __FILE__, __LINE__);
        ZLIB_FREE(msg);
        /* We **SHOULD** free the memory buffer used for serializing here since produce call has failed...*/
        argo_object_deallocate_dump(out_buf);
        out_buf = NULL;

        rd_kafka_resp_err_t err = rd_kafka_last_error();

        switch (err) {
        case RD_KAFKA_RESP_ERR__QUEUE_FULL:
            //ZPATH_LOG(AL_WARNING, "Kafka could not write to topic %s: %s, Blocking", log->topic, rd_kafka_err2str(err));
            /* Special case: If we have zero outstanding messages, we
             * will never get an acknowledgement, which would cause us
             * to never wake up. This is probably a scenario where we
             * cannot write to kafka for some reason that is NOT
             * really associated with blocking- i.e. if we cannot send
             * 1 message to kafka, we will likely never send any
             * messages to kafka. For this case, we will return an
             * error and thereby make the originating system try a
             * different producer. */
            if (is_empty) {
                /* Other connections could have consumed all of the
                 * available kafka output queue, so this is relatively
                 * common. We need to return would_block, but we also
                 * need to retry transmits. We will put in a small
                 * 'retry' timeout to do so. */
                /* Note: This has to occur on the thread managing
                 * transmissions, which we are on at the moment. */
                __sync_add_and_fetch_8(&(k_conn->ref_cnt), 1);
                /* Defer a short time- 10ms or so, but vary by about 10ms or so */
#define WAKE_DEFER_US 10000
                int64_t usec_defer = WAKE_DEFER_US + (random() % WAKE_DEFER_US);
                res = zevent_defer(empty_queue_wake, k_conn, NULL, usec_defer);
                if (res) {
                    __sync_sub_and_fetch_8(&(k_conn->ref_cnt), 1);
                    ZPATH_LOG(AL_CRITICAL, "zevent_defer failed: %s", log->topic);
                    return FOHH_RESULT_ERR;
                }
                __sync_add_and_fetch_8(&(global_natural_stats.empty_queue_block), 1);
                return FOHH_RESULT_WOULD_BLOCK;
            } else {
                __sync_add_and_fetch_8(&(global_natural_stats.non_empty_queue_block), 1);
                return FOHH_RESULT_WOULD_BLOCK;
            }
        case RD_KAFKA_RESP_ERR_MSG_SIZE_TOO_LARGE:
        case RD_KAFKA_RESP_ERR__UNKNOWN_PARTITION:
        case RD_KAFKA_RESP_ERR__UNKNOWN_TOPIC:
        case RD_KAFKA_RESP_ERR__FATAL:
        case RD_KAFKA_RESP_ERR__STATE:
        default:
            ZPATH_LOG(AL_ERROR, "Kafka could not write to topic %s: %s, Erroring", log->topic, rd_kafka_err2str(err));
            __sync_sub_and_fetch_8(&(k_conn->ref_cnt), 1);
            __sync_add_and_fetch_8(&(global_natural_stats.produce_err_count), 1);
            return FOHH_RESULT_ERR;
        }
    }

    __sync_sub_and_fetch_8(&(k_conn->ref_cnt), 1);
    return FOHH_RESULT_NO_ERROR;
}

static void kafka_conn_destroy_if_possible(struct kafka_conn_state *k_conn)
{
    if (k_conn->connection_gone) {
        __sync_add_and_fetch_8(&(k_conn->ref_cnt), 1);
        ZPATH_MUTEX_LOCK(&(k_conn->lock), __FILE__, __LINE__);
        if (k_conn->messages_queued == (k_conn->acks_received + k_conn->errors_received)) {
            if (ZTAILQ_FIRST(&(k_conn->acks))) {
                ZPATH_LOG(AL_CRITICAL, "k_conn:%p has acks when freed", k_conn);
            }
            ZPATH_MUTEX_UNLOCK(&(k_conn->lock), __FILE__, __LINE__);
            int64_t ref_cnt = __sync_sub_and_fetch_8(&(k_conn->ref_cnt), 1);
            if (ref_cnt < 1) {
                ZPATH_LOG(AL_NOTICE, "Attempting to double free k_conn:%p", k_conn);
            }
            if (1 == ref_cnt) {
                char k_conn_final_state[512] = {'\0'};
                char *s = k_conn_final_state;
                char *e = s + sizeof(k_conn_final_state);

                s += print_k_conn_detail(k_conn, s, e);
                ZPATH_LOG(AL_NOTICE, "Freed k_conn {%p}: %s", k_conn, k_conn_final_state);

                ZPATH_MUTEX_LOCK(&(global_kconns.lock), __FILE__, __LINE__);
                ZTAILQ_REMOVE(&(global_kconns.k_conns), k_conn, list);
                global_kconns.count--;
                ZPATH_MUTEX_UNLOCK(&(global_kconns.lock), __FILE__, __LINE__);

                /* Free the periodic stat structure which references this k_conn for stats */
                ZPATH_MUTEX_LOCK(&(k_conn->lock), __FILE__, __LINE__);
                if (k_conn->k_conn_rstruct) {
                    argo_log_deregister_structure(k_conn->k_conn_rstruct, 1);
                    k_conn->k_conn_rstruct = NULL;
                }
                ZPATH_MUTEX_UNLOCK(&(k_conn->lock), __FILE__, __LINE__);

                ZLIB_FREE_SLOW(k_conn);

                __sync_sub_and_fetch_8(&(global_natural_stats.k_conn_count), 1);
                __sync_add_and_fetch_8(&(global_natural_stats.k_conn_destroyed), 1);
            }
        } else {
            ZPATH_MUTEX_UNLOCK(&(k_conn->lock), __FILE__, __LINE__);
            __sync_sub_and_fetch_8(&(k_conn->ref_cnt), 1);
        }
    }
}

static void log_status_f(struct fohh_log_custom_connection *custom_connection, struct fohh_connection *f_conn, int alive, char *str, size_t str_len)
{
    struct kafka_conn_state *k_conn;
    if (alive) {
        /* New connection */
        k_conn = NATURAL_CALLOC(sizeof(*k_conn));
        k_conn->start_s = epoch_s();
        k_conn->custom_connection = custom_connection;
        k_conn->custom_connection_incarnation = fohh_log_custom_get_incarnation(custom_connection);

        ZTAILQ_INIT(&(k_conn->acks));
        k_conn->lock = ZPATH_MUTEX_INIT;
        k_conn->ref_cnt = 1;

        __sync_add_and_fetch_8(&(global_natural_stats.k_conn_count), 1);
        __sync_add_and_fetch_8(&(global_natural_stats.k_conn_created), 1);

        fohh_log_custom_set_void(custom_connection, k_conn);
        //ZPATH_LOG(AL_NOTICE, "Custom connection %s: Associated with k_conn %p", fohh_description(f_conn), k_conn);
        snprintf(k_conn->name, sizeof(k_conn->name), "%s", str);
        ZPATH_MUTEX_LOCK(&(global_kconns.lock), __FILE__, __LINE__);
        ZTAILQ_INSERT_TAIL(&(global_kconns.k_conns), k_conn, list);
        global_kconns.count++;
        ZPATH_MUTEX_UNLOCK(&(global_kconns.lock), __FILE__, __LINE__);
        ZPATH_LOG(AL_NOTICE, "Created k_conn {%p}: %s", k_conn, str);
    } else {
        k_conn = fohh_log_custom_get_void(custom_connection);
        //ZPATH_LOG(AL_NOTICE, "Custom connection %s: Dissociated with k_conn %p", fohh_description(f_conn), k_conn);
        fohh_log_custom_set_void(custom_connection, NULL);
        k_conn->connection_gone = 1;
        kafka_conn_destroy_if_possible(k_conn);
    }
}

/*
 * dr_msg_cb is called from a kafka thread, not from any internal 'natural' thread.
 *
 * We need to be certain to free and unlink any messages returned to
 * us by kafka, in all cases.
 *
 * msg->k_conn should be valid for all callbacks here, as that is a
 * hard enforcement on the lifetime of k_conn itself.
 *
 * If we cannot find a "msg" then it is a hard error, and we cannot do
 * much of anything. Might even be worth aborting.
 */
static void dr_msg_cb(rd_kafka_t *rk,
                      const rd_kafka_message_t *rkmessage,
                      void *opaque)
{
    struct kafka_ack *msg = opaque;

    __sync_add_and_fetch_8(&(global_natural_stats.ack_count), 1);

    if (!msg) {
        msg = rkmessage->_private;
    }

    if (!msg) {
        /* We can return directly here because, without a message, it
         * is impossible to know the context that has failed. We could
         * conceivably abort here. */
        if (rkmessage->err) {
            /* There was probably an error- log the extra info here. */
            ZPATH_LOG(AL_ERROR, "Kafka error on production: %s", rd_kafka_err2str(rkmessage->err));
        }
        ZPATH_LOG(AL_CRITICAL, "Kafka error on production: no cookie");
        __sync_add_and_fetch_8(&(global_natural_stats.ack_err_count), 1);
        return;
    }

    struct kafka_conn_state *k_conn = msg->k_conn;

    __sync_add_and_fetch_8(&(k_conn->ref_cnt), 1);
    ZPATH_MUTEX_LOCK(&(k_conn->lock), __FILE__, __LINE__);
    if (rkmessage->err) {
        /* This scenario has to kill the logging connection, as we
         * cannot recover from a missed ack. Just marking the
         * connection as gone will make it go away when we get our
         * next message to log- or the timer on the connection will
         * make it go away. Soon enough, in either case. */
        ZPATH_LOG(AL_ERROR, "Kafka error on production: [%s], desc: %s, incarnation: %"PRId64", seq-id: %"PRId64", msg-size: %"PRId64, rd_kafka_err2str(rkmessage->err), k_conn->name, msg->log_incarnation, msg->log_id, msg->bytes);
        k_conn->errors_received++;
        __sync_add_and_fetch_8(&(global_natural_stats.ack_err_count), 1);
    } else {
        k_conn->acks_received++;
    }
    msg->ack_complete = 1;


    /* NOTE: msg changes context here! */
    /* Send all the completed acks that exist at the front of the queue */
    while ((msg = ZTAILQ_FIRST(&(k_conn->acks)))) {
        if (msg->ack_complete) {
            /* Don't send acks if the connection is gone or if we have experienced any kafka error. */
            if (!(k_conn->connection_gone) && !(k_conn->errors_received))  {
                fohh_log_custom_ack(msg->custom_connection,
                                    msg->custom_connection_incarnation,
                                    msg->log_id,
                                    msg->log_incarnation,
                                    msg->bytes);
            }
            ZTAILQ_REMOVE(&(k_conn->acks), msg, list);
            ZLIB_FREE(msg);
        } else {
            break;
        }
    }
    ZPATH_MUTEX_UNLOCK(&(k_conn->lock), __FILE__, __LINE__);
    __sync_sub_and_fetch_8(&(k_conn->ref_cnt), 1);

    kafka_conn_destroy_if_possible(k_conn);
}

/* Log base kafka stats */
static void log_kafka_stats(const JSON_Object *root)
{
    struct natural_kafka_stats kstats;
    int res;

    memset(&kstats, 0, sizeof(kstats));
    kstats.replyq = (int64_t) json_object_get_number(root, "replyq");
    kstats.msg_cnt = (int64_t) json_object_get_number(root, "msg_cnt");
    kstats.msg_size = (int64_t) json_object_get_number(root, "msg_size");
    kstats.tx = (int64_t) json_object_get_number(root, "tx");
    kstats.tx_bytes = (int64_t) json_object_get_number(root, "tx_bytes");
    kstats.rx = (int64_t) json_object_get_number(root, "rx");
    kstats.rx_bytes = (int64_t) json_object_get_number(root, "rx_bytes");
    kstats.txmsgs = (int64_t) json_object_get_number(root, "txmsgs");
    kstats.txmsg_bytes = (int64_t) json_object_get_number(root, "txmsg_bytes");
    res = argo_log_structure_immediate(zpath_stats_collection,
                                       argo_log_priority_info,
                                       1, // int include_role,
                                       "base_stats",
                                       natural_kafka_stats_description,
                                       &kstats);
    if (res) {
        ZPATH_LOG(AL_ERROR, "Could not log stats immediate: %s", argo_result_string(res));
    }

    ZPATH_LOG(AL_NOTICE, "Log transmision rate in the last %"PRIu64" seconds: %.2f requests/sec, %.3f MB/sec",
        global_config->client_stats_interval_ms/1000,
        (double)(kstats.tx-prev_tx_cnt)/(global_config->client_stats_interval_ms/1000.0),
        (double)((kstats.tx_bytes-prev_tx_bytes)/(global_config->client_stats_interval_ms/1000.0))/1000000.0);

    /* update stats to find the difference next time stats are logged*/
    prev_tx_cnt = kstats.tx;
    prev_tx_bytes = kstats.tx_bytes;
}

static void log_broker_stats(const JSON_Object *root)
{
    int res;

    /* Iterate brokers */
    JSON_Object *brokers = json_object_get_object(root, "brokers");
    if (!brokers) {
        ZPATH_LOG(AL_ERROR, "No brokers object");
        return;
    }
    size_t broker_count = json_object_get_count(brokers);
    size_t i;
    for (i = 0; i < broker_count; i++) {
        const char *broker_name = json_object_get_name(brokers, i);
        if (!broker_name) continue;
        /* Get nodeid to see if we want to log it. We only log brokers with nodeid >= 0 */
        JSON_Object *this_broker = json_object_get_object(brokers, broker_name);
        if (!this_broker) continue;
        double nodeid = json_object_get_number(this_broker, "nodeid");
        if (nodeid < 0) continue;
        /* Okay, we have a broker name + node + stats here. Let's put it in an object and log it */
        char object_name[1000];
        snprintf(object_name, sizeof(object_name), "%s-%d", broker_name, (int) nodeid);
        struct natural_kafka_broker_stats stats;
        memset(&stats, 0, sizeof(stats));

        stats.outbuf_cnt = (int64_t) json_object_get_number(this_broker, "outbuf_cnt");
        stats.outbuf_msg_cnt = (int64_t) json_object_get_number(this_broker, "outbuf_msg_cnt");
        stats.waitresp_cnt = (int64_t) json_object_get_number(this_broker, "waitresp_cnt");
        stats.waitresp_msg_cnt = (int64_t) json_object_get_number(this_broker, "waitresp_msg_cnt");
        stats.tx = (int64_t) json_object_get_number(this_broker, "tx");
        stats.txbytes = (int64_t) json_object_get_number(this_broker, "txbytes");
        stats.txerrs = (int64_t) json_object_get_number(this_broker, "txerrs");
        stats.txretries = (int64_t) json_object_get_number(this_broker, "txretries");
        stats.req_timeouts = (int64_t) json_object_get_number(this_broker, "req_timeouts");
        stats.disconnects = (int64_t) json_object_get_number(this_broker, "disconnects");

        res = argo_log_structure_immediate(zpath_stats_collection,
                                           argo_log_priority_info,
                                           1, // int include_role,
                                           object_name,
                                           natural_kafka_broker_stats_description,
                                           &stats);
        if (res) {
            ZPATH_LOG(AL_ERROR, "Could not log stats immediate: %s", argo_result_string(res));
        }
    }
}

static void log_topic_stats(const JSON_Object *root)
{
    int res;

    /* Iterate topics */
    JSON_Object *topics = json_object_get_object(root, "topics");
    if (!topics) {
        ZPATH_LOG(AL_ERROR, "No topics?");
        return;
    }
    size_t topic_count = json_object_get_count(topics);
    size_t i;
    for (i = 0; i < topic_count; i++) {
        const char *topic_name = json_object_get_name(topics, i);
        if (!topic_name) continue;
        JSON_Object *topic = json_object_get_object(topics, topic_name);
        if (!topic) continue;
        /* Iterate partitions */
        struct natural_kafka_topic_stats topic_stats;
        memset(&topic_stats, 0, sizeof(topic_stats));
        JSON_Object *batchsize = json_object_get_object(topic,"batchsize");
        if ( batchsize ) {
            topic_stats.batchsize_cnt = (int64_t) json_object_get_number(batchsize,"cnt");
            topic_stats.batchsize_avg = (int64_t) json_object_get_number(batchsize,"avg");
            topic_stats.batchsize_max = (int64_t) json_object_get_number(batchsize,"max");
            topic_stats.batchsize_min = (int64_t) json_object_get_number(batchsize,"min");
            topic_stats.batchsize_p50 = (int64_t) json_object_get_number(batchsize,"p50");
            topic_stats.batchsize_p75 = (int64_t) json_object_get_number(batchsize,"p75");
            topic_stats.batchsize_p90 = (int64_t) json_object_get_number(batchsize,"p90");
            topic_stats.batchsize_p95 = (int64_t) json_object_get_number(batchsize,"p95");
            topic_stats.batchsize_p99 = (int64_t) json_object_get_number(batchsize,"p99");
        }
        JSON_Object *batchcnt = json_object_get_object(topic,"batchcnt");
        if ( batchcnt ) {
            topic_stats.batchcnt_cnt = (int64_t) json_object_get_number(batchcnt,"cnt");
            topic_stats.batchcnt_avg = (int64_t) json_object_get_number(batchcnt,"avg");
            topic_stats.batchcnt_max = (int64_t) json_object_get_number(batchcnt,"max");
            topic_stats.batchcnt_min = (int64_t) json_object_get_number(batchcnt,"min");
            topic_stats.batchcnt_p50 = (int64_t) json_object_get_number(batchcnt,"p50");
            topic_stats.batchcnt_p75 = (int64_t) json_object_get_number(batchcnt,"p75");
            topic_stats.batchcnt_p90 = (int64_t) json_object_get_number(batchcnt,"p90");
            topic_stats.batchcnt_p95 = (int64_t) json_object_get_number(batchcnt,"p95");
            topic_stats.batchcnt_p99 = (int64_t) json_object_get_number(batchcnt,"p99");
        }
        JSON_Object *partitions = json_object_get_object(topic, "partitions");
        if (!partitions) continue;
        size_t partition_count = json_object_get_count(partitions);
        size_t j;
        for (j = 0; j < partition_count; j++) {
            const char *partition_name = json_object_get_name(partitions, j);
            if (atoi(partition_name) < 0) continue;
            JSON_Object *partition = json_object_get_object(partitions, partition_name);
            if (!partition) continue;

            /* Skip aux partition */
            if (json_object_get_number(partition, "partition") < 0) continue;

            char object_name[1000];
            snprintf(object_name, sizeof(object_name), "%s-%s", topic_name, partition_name);

            struct natural_kafka_partition_stats stats;
            memset(&stats, 0, sizeof(stats));
            stats.broker = (int64_t) json_object_get_number(partition, "broker");
            stats.msgq_cnt = (int64_t) json_object_get_number(partition, "msgq_cnt");
            stats.msgq_bytes = (int64_t) json_object_get_number(partition, "msgq_bytes");
            stats.xmit_msgq_cnt = (int64_t) json_object_get_number(partition, "xmit_msgq_cnt");
            stats.xmit_msgq_bytes = (int64_t) json_object_get_number(partition, "xmit_msgq_bytes");
            stats.msgs_inflight = (int64_t) json_object_get_number(partition, "msgs_inflight");
            stats.txmsgs = (int64_t) json_object_get_number(partition, "txmsgs");
            stats.txbytes = (int64_t) json_object_get_number(partition, "txbytes");
            stats.rxmsgs = (int64_t) json_object_get_number(partition, "rxmsgs");
            stats.rxbytes = (int64_t) json_object_get_number(partition, "rxbytes");
            topic_stats.msgq_cnt += stats.msgq_cnt;
            topic_stats.msgq_bytes += stats.msgq_bytes;
            topic_stats.xmit_msgq_cnt += stats.xmit_msgq_cnt;
            topic_stats.xmit_msgq_bytes += stats.xmit_msgq_bytes;
            topic_stats.msgs_inflight += stats.msgs_inflight;
            topic_stats.txmsgs += stats.txmsgs;
            topic_stats.txbytes += stats.txbytes;
            topic_stats.rxmsgs += stats.rxmsgs;
            topic_stats.rxbytes += stats.rxbytes;

            res = argo_log_structure_immediate(zpath_stats_collection,
                                               argo_log_priority_info,
                                               1, // int include_role,
                                               object_name,
                                               natural_kafka_partition_stats_description,
                                               &stats);
            if (res) {
                ZPATH_LOG(AL_ERROR, "Could not log partition stats immediate: %s", argo_result_string(res));
            }
        }
        char object_name[1024];
        snprintf(object_name, sizeof(object_name), "%s_stats", topic_name);
        res = argo_log_structure_immediate(zpath_stats_collection,
                                            argo_log_priority_info,
                                            1, // int include_role,
                                            object_name,
                                            natural_kafka_topic_stats_description,
                                            &topic_stats);
        if (res) {
            ZPATH_LOG(AL_ERROR, "Could not log topic stats immediate: %s", argo_result_string(res));
        }
    }
}

static void log_natural_stats(void)
{
    int res;

    /* Log basic natural stats */
    res = argo_log_structure_immediate(zpath_stats_collection,
                                       argo_log_priority_info,
                                       1, // int include_role,
                                       "global",
                                       natural_stats_description,
                                       &global_natural_stats);
    if (res) {
        ZPATH_LOG(AL_ERROR, "Could not log stats immediate for global stats: %s", argo_result_string(res));
    }
}

static void log_rate_limit_stats(void){
    struct rate_limit_tbls_ref* l_ref = g_rate_limit_tbls;
    if ( !l_ref->rate_limit_config->rate_limit_enabled )
        return;
    int64_t walk_key = 0;
    struct zhash_table* tbls[3];
    int table_count = 0;
    tbls[0] = g_topic_rate_stats_tbl;
    tbls[1] = g_cust_topic_rate_stats_tbl;
    tbls[2] = g_port_group_cust_topic_rate_stats_tbl;
    if ( g_rate_limit_stats_lines_count >= 5000 || !(l_ref->rate_limit_config->stats_upload) ){
        table_count = 1;
    } else {
        table_count = 3;
    }
    ZPATH_MUTEX_LOCK(&(g_rate_limit_stats_tbl_lock), __FILE__, __LINE__);
    for ( int i = 0; i < table_count; i++ ) {
        walk_key = 0;
        while ( zhash_table_walk(tbls[i],
                                &walk_key,
                                rate_limit_stats_upload_walk,
                                NULL ) == ZHASH_RESULT_INCOMPLETE ) {}
    }
    ZPATH_MUTEX_UNLOCK(&(g_rate_limit_stats_tbl_lock), __FILE__, __LINE__);

}
static int dr_stats_cb(rd_kafka_t *rk, char *json_message, size_t json_message_len, void *cookie)
{

    JSON_Value *root_value = json_parse_string(json_message);

    //ZPATH_LOG(AL_DEBUG, "json stats: %s", json_message);

    ZPATH_MUTEX_LOCK(&stats_lock, __FILE__, __LINE__);
    if (last_kafka_stats) {
        free(last_kafka_stats);
    }
    last_kafka_stats = json_message;
    ZPATH_MUTEX_UNLOCK(&stats_lock, __FILE__, __LINE__);


    if (!root_value) {
        ZPATH_LOG(AL_ERROR, "Could not parse json statistics from rdkafka");
        goto cleanup;
    }
    if (json_value_get_type(root_value) != JSONObject) {
        ZPATH_LOG(AL_ERROR, "rdkafka stats callback is not a JSON object");
        goto cleanup;
    }
    JSON_Object *root = json_value_get_object(root_value);
    if (!root) {
        ZPATH_LOG(AL_ERROR, "No root object");
        goto cleanup;
    }

    log_kafka_stats(root);

    log_broker_stats(root);

    log_topic_stats(root);


 cleanup:
    if (root_value) json_value_free(root_value);

    log_natural_stats();
    return 1;
}

static void dr_log_cb(const rd_kafka_t *rk, int level, const char *fac, const char *buf)
{
    if ((level >= 0) && (level < argo_log_priority_none)) {
        ZPATH_LOG(level, "Received Kafka log, level = %d, fac=<%s>, buf=<%s>", level, fac, buf);
    } else {
        ZPATH_LOG(AL_NOTICE, "Received Kafka log, level = %d, fac=<%s>, buf=<%s>", level, fac, buf);
    }
}

void kafka_poll(int sock, short events, void *cookie)
{
    rd_kafka_poll(rk, 5 /* milliseonds */);
}

int kafka_init(const char *kafka_broker_names,
               char **kafka_config_strings, size_t kafka_config_strings_count,
               char **kafka_topic_strings, size_t kafka_topic_strings_count,
               char **kafka_topic_config_strings, size_t kafka_topic_config_strings_count)
{
    rd_kafka_conf_t *conf;  /* Temporary configuration object */
    char errstr[512];       /* librdkafka API error reporting buffer */
    int i;
    int j;

    ZPATH_LOG(AL_NOTICE, "Initializing Kafka...");

    if (!kafka_topic_strings_count) {
        ZPATH_LOG(AL_ERROR, "Require at least one topic\n");
        return ZPATH_RESULT_ERR;
    }
    if (!kafka_broker_names) {
        ZPATH_LOG(AL_ERROR, "Require broker name\n");
        return ZPATH_RESULT_ERR;
    }

    conf = rd_kafka_conf_new();

    if (rd_kafka_conf_set(conf, "bootstrap.servers", kafka_broker_names,
                          errstr, sizeof(errstr)) != RD_KAFKA_CONF_OK) {
        ZPATH_LOG(AL_ERROR, "Configuration set failed for bootstrap.servers: %s", errstr);
        return ZPATH_RESULT_ERR;
    }

    /* We set stats interval up front for default, and let user override */
    if (rd_kafka_conf_set(conf, "statistics.interval.ms", "60000", errstr, sizeof(errstr)) != RD_KAFKA_CONF_OK) {
        ZPATH_LOG(AL_ERROR, "Configuration set failed for statistics.interval.ms: %s", errstr);
        return ZPATH_RESULT_ERR;
    }
    for (i = 0; i + 1 < kafka_config_strings_count; i += 2) {
        rd_kafka_conf_res_t k_res;
        char str[512];
        ZPATH_LOG(AL_NOTICE, "Configuring Kafka, Name = %s, Value = %s", kafka_config_strings[i], kafka_config_strings[i + 1]);
        k_res = rd_kafka_conf_set(conf,
                                  kafka_config_strings[i],
                                  kafka_config_strings[i + 1],
                                  str,
                                  sizeof(str));
        switch(k_res) {
        case RD_KAFKA_CONF_UNKNOWN:
            ZPATH_LOG(AL_ERROR, "Kafka, config name = %s, Unknown configuration name.\n", kafka_conf.config_names[i]);
            return ZPATH_RESULT_ERR;
        case RD_KAFKA_CONF_INVALID:
            ZPATH_LOG(AL_ERROR, "Kafka, config name = %s, Invalid configuration value = %s\n", kafka_conf.config_names[i], kafka_conf.config_values[i]);
            return ZPATH_RESULT_ERR;
        case RD_KAFKA_CONF_OK:
            break;
        default:
            ZPATH_LOG(AL_ERROR, "Kafka, config name = %s, Bad result code %d\n", kafka_conf.config_names[i], k_res);
            return ZPATH_RESULT_ERR;
        }
    }

    ZPATH_LOG(AL_NOTICE, "Configuring Kafka:");

    rd_kafka_conf_set_stats_cb(conf, dr_stats_cb);
    rd_kafka_conf_set_dr_msg_cb(conf, dr_msg_cb);
    rd_kafka_conf_set_log_cb(conf, dr_log_cb);

    rk = rd_kafka_new(RD_KAFKA_PRODUCER, conf, errstr, sizeof(errstr));
    if (!rk) {
        ZPATH_LOG(AL_ERROR, "Failed to create new producer: %s", errstr);
        return ZPATH_RESULT_ERR;
    }

    {
        size_t dump_count = 0;
        const rd_kafka_conf_t *run_conf = rd_kafka_conf(rk);
        conf = rd_kafka_conf_dup(run_conf);
        const char **dump = rd_kafka_conf_dump(conf, &dump_count);
        for (int j = 0; j + 1 < dump_count; j+=2) {
            ZPATH_LOG(AL_NOTICE, "     Kafka configuration: %40s:%s", dump[j], dump[j + 1]);
        }
        rd_kafka_conf_dump_free(dump, dump_count);
        rd_kafka_conf_destroy(conf);
    }

    for (i = 0; i < kafka_topic_strings_count; i++) {
        struct kafka_topic *topic = NATURAL_CALLOC(sizeof(*topic));

        topic->name = kafka_topic_strings[i];
        topic->topic_index = i; //unique topic index for this topic string
        zhash_table_store(all_topics, topic->name, strlen(topic->name), 0, topic);
        rd_kafka_topic_conf_t *t_conf = rd_kafka_topic_conf_new();
        for (j = 0; j + 2 < kafka_topic_config_strings_count; j += 3) {
            /* Skip this config if it is not our topic */
            if ((strcmp(kafka_topic_strings[i], kafka_topic_config_strings[j])) &&
                (strcmp(kafka_topic_config_strings[j], "*"))) continue;

            rd_kafka_conf_res_t k_res;
            char str[512];
            if (strcmp(kafka_topic_config_strings[j + 1], "partitioner") == 0) {
                if (strcmp(kafka_topic_config_strings[j + 2], "consistent") == 0)
                    topic->consistent = 1; //use mtunnel_id or tunnel_id or l_inst as hash-key
                else if (strcmp(kafka_topic_config_strings[j + 2], "random") == 0)
                    topic->consistent = -1; //pure random i.e., no hash-key
                else
                    topic->consistent = 0; //use only l_inst as hash-key
            }
            ZPATH_LOG(AL_NOTICE, "Configuring Kafka Topic %s: %s: Name = %s, Value = %s", kafka_topic_strings[i], kafka_topic_config_strings[j], kafka_topic_config_strings[j + 1], kafka_topic_config_strings[j + 2]);
            k_res = rd_kafka_topic_conf_set(t_conf,
                                            kafka_topic_config_strings[j + 1],
                                            kafka_topic_config_strings[j + 2],
                                            str,
                                            sizeof(str));
            switch(k_res) {
            case RD_KAFKA_CONF_UNKNOWN:
                ZPATH_LOG(AL_ERROR, "Topic %s, config name = %s, Unknown configuration name.\n", kafka_topic_config_strings[j], kafka_topic_config_strings[j + 1]);
                return ZPATH_RESULT_ERR;
            case RD_KAFKA_CONF_INVALID:
                ZPATH_LOG(AL_ERROR, "Topic %s, config name = %s, Invalid configuration value = %s\n", kafka_topic_config_strings[j], kafka_topic_config_strings[j + 1], kafka_topic_config_strings[j + 2]);
                return ZPATH_RESULT_ERR;
            case RD_KAFKA_CONF_OK:
                break;
            default:
                ZPATH_LOG(AL_ERROR, "Kafka, config name = %s, Bad result code %d\n", kafka_conf.config_names[i], k_res);
                return ZPATH_RESULT_ERR;
            }
        }

        ZPATH_LOG(AL_NOTICE, "Configuring topic %s", kafka_topic_strings[i]);
        size_t dump_count = 0;
        const char **dump = rd_kafka_topic_conf_dump(t_conf, &dump_count);
        for (j = 0; j + 1< dump_count; j+=2) {
            ZPATH_LOG(AL_NOTICE, "  Topic %s: %40s:%s", kafka_topic_strings[i], dump[j], dump[j + 1]);
        }
        rd_kafka_conf_dump_free(dump, dump_count);

        topic->topic = rd_kafka_topic_new(rk, kafka_topic_strings[i], t_conf);
        if (!topic->topic) {
            ZPATH_LOG(AL_ERROR, "Could not create topic %s", kafka_topic_strings[i]);
            return ZPATH_RESULT_ERR;
        }
    }

    /* Start thread for kafka polling */
    rk_poll_base = zevent_handler_create("kafka_poll", 512*1024, 30);
    rk_timer = event_new(zevent_event_base(rk_poll_base), -1, EV_PERSIST, kafka_poll, NULL);
    struct timeval tv;
    tv.tv_sec = 0;
    tv.tv_usec = 5000;
    event_add(rk_timer, &tv);

    ZPATH_LOG(AL_NOTICE, "Initializing Kafka... COMPLETE!");
    return ZPATH_RESULT_NO_ERROR;
}
#ifdef UNIT_TEST
int natural_debug_logs_rxd_stats(struct zpath_debug_state *request_state,
                              const char **query_values,
                              int query_value_count,
                              void *cookie)
{
     ZDP("Total logs received so far %"PRIu64"\n", logs_rxd );
     return 0;
}
#endif

static int print_k_conn_detail(struct kafka_conn_state *k_conn, char *start, char *end)
{
    if (!k_conn || start > end) return 0;

    char topic_strings[256] = {'\0'};
    char *topic_s = topic_strings;
    char *topic_e = topic_s + sizeof(topic_strings);
    int counting_topic;
    int topic_present = 0;

    topic_s += sxprintf(topic_s, topic_e, "%s", ", Topic: ");

    for (counting_topic = 0; counting_topic < global_config->kafka_topics_count; counting_topic++) {
        if (k_conn->topics_received & (1 << counting_topic)) {
            topic_s += sxprintf(topic_s, topic_e, "%s ", global_config->kafka_topics[counting_topic]);
            topic_present++;
        }
    }

    return sxprintf(start, end, "ref: %"PRId64", lifetime: %"PRIu64"s, incarnation: %"PRId64
                        ", name: %s, msgs: %"PRId64", acks: %"PRId64", errors: %"PRId64
                        ", bytes: %"PRId64", rate limited:%"PRId64"%s\n",
           k_conn->ref_cnt, epoch_s() - k_conn->start_s, k_conn->custom_connection_incarnation,
           k_conn->name, k_conn->messages_queued, k_conn->acks_received, k_conn->errors_received,
           k_conn->bytes_received, k_conn->rate_limited, topic_present ? topic_strings : "");
}

int natural_debug_internal_stats(struct zpath_debug_state *request_state,
                              const char **query_values,
                              int query_value_count,
                              void *cookie)
{
    int i = 0, count = 0;
    struct kafka_conn_state *k_conns_list = NULL, *k_conn = NULL;
    char *out_buffer = NULL;
    char *s = NULL;
    char *e = NULL;
    int total = 0;
    int empty = 0;
    int only_show_total = 0;
    int display_brief = 0;
    struct natural_stats stats = {0};

    if (query_values[0]) {
        display_brief = 1; //Small query
    }

    if (query_values[1]) {
        display_brief = 0; //need to count all for total
        only_show_total = 1;
    }

    ZPATH_MUTEX_LOCK(&(global_kconns.lock), __FILE__, __LINE__);

    count = global_kconns.count;

    if (count) {
        k_conns_list = NATURAL_CALLOC(sizeof(struct kafka_conn_state) * count);
        if (!k_conns_list) {
            ZPATH_MUTEX_UNLOCK(&(global_kconns.lock), __FILE__, __LINE__);
            ZLIB_FREE(out_buffer);
            fprintf(stdout, "Memory allocation failed for debug stats\n");
            return ZPATH_RESULT_NO_ERROR;
        }

        ZTAILQ_FOREACH(k_conn, &(global_kconns.k_conns), list) {
            memcpy(&(k_conns_list[i++]), k_conn, sizeof(struct kafka_conn_state));
        }
    } else {
        ZPATH_MUTEX_UNLOCK(&(global_kconns.lock), __FILE__, __LINE__);
        return ZPATH_RESULT_NO_ERROR;
    }

    ZPATH_MUTEX_UNLOCK(&(global_kconns.lock), __FILE__, __LINE__);

    if (only_show_total == 0) {
        size_t buf_len = 512 * count;
        out_buffer = s = NATURAL_CALLOC(buf_len);
        if (!out_buffer) {
            ZPATH_MUTEX_UNLOCK(&(global_kconns.lock), __FILE__, __LINE__);
            fprintf(stdout, "Memory allocation failed for debug stats\n");
            return ZPATH_RESULT_NO_ERROR;
        }
        e = s + buf_len;
    }

    for (i = 0, k_conn = NULL; i < count; i++) {
        k_conn = &(k_conns_list[i]);

        total++;
        //Skip if small is set and no messages pending or acks received
        if (!(k_conn->messages_queued || k_conn->acks_received || k_conn->errors_received)) {
            empty++;
            if (display_brief) continue;
        }

        if (only_show_total == 0)
            s += print_k_conn_detail(k_conn, s, e);
    }

    if (k_conns_list) ZLIB_FREE(k_conns_list);

    if (total) {
        memcpy(&stats, &global_natural_stats, sizeof(struct natural_stats));
        ZDP("%s%sTotal = %d (Active: %d, Inert: %d)\n"
            "k_conn created: %"PRId64" k_conn destroyed: %"PRId64" empty_block: %"PRId64
            " non_empty_block: %"PRId64" msg_count: %"PRId64" err_count: %"PRId64
            " ack_count: %"PRId64" ack_err_count: %"PRId64" argo_err_count: %"PRId64
            " produce_count: %"PRId64" produce_err_count: %"PRId64"\n",
            out_buffer ? out_buffer : "", out_buffer ? "\n": "", total, total - empty, empty,
            stats.k_conn_created, stats.k_conn_destroyed,
            stats.empty_queue_block, stats.non_empty_queue_block,
            stats.msg_count, stats.err_count, stats.ack_count, stats.ack_err_count,
            stats.argo_deserial_err_count, stats.produce_count, stats.produce_err_count);
    }

    if (out_buffer) ZLIB_FREE(out_buffer);

    return ZPATH_RESULT_NO_ERROR;
}

int natural_debug_kafka_stats(struct zpath_debug_state *request_state,
                              const char **query_values,
                              int query_value_count,
                              void *cookie)
{
    ZPATH_MUTEX_LOCK(&stats_lock, __FILE__, __LINE__);
    if (last_kafka_stats) {
        ZDP("%s\n", last_kafka_stats);
    } else {
        ZDP("{}\n");
    }
    ZPATH_MUTEX_UNLOCK(&stats_lock, __FILE__, __LINE__);
    return ZPATH_RESULT_NO_ERROR;
}


int sub_main_init_globals(void)
{
    int res;

    memset(&kafka_conf, 0, sizeof(kafka_conf));
    all_topics = zhash_table_alloc(&zpath_lib_allocator);

    /* Rate limit tables initialization */
    g_rate_limit_tbls = NATURAL_CALLOC(sizeof(*g_rate_limit_tbls));
    g_rate_limit_tbls->topic_rate_tbl = zhash_table_alloc(&zpath_lib_allocator);
    g_rate_limit_tbls->customer_topic_rate_tbl = zhash_table_alloc(&zpath_lib_allocator);
    g_rate_limit_tbls->customer_topic_rate_tbl_lock = ZPATH_MUTEX_INIT;
    g_rate_limit_tbls->port_group_customer_topic_rate_tbl = zhash_table_alloc(&zpath_lib_allocator);
    g_rate_limit_tbls->port_group_customer_topic_rate_tbl_lock = ZPATH_MUTEX_INIT;
    g_rate_limit_tbls->customer_list_per_topic_tbl = zhash_table_alloc(&zpath_lib_allocator);
    g_rate_limit_tbls->port_group_list_per_topic_customer_tbl = zhash_table_alloc(&zpath_lib_allocator);
    g_rate_limit_tbls->port_group_list_per_topic_customer_tbl_lock = ZPATH_MUTEX_INIT;
    g_rate_limit_tbls->rate_limit_skiplist_tbl = zhash_table_alloc(&zpath_lib_allocator);
    g_rate_limit_tbls->rate_limit_pick_first_tbl = zhash_table_alloc(&zpath_lib_allocator);

    //Rate limit stats table initialization
    g_topic_rate_stats_tbl =  zhash_table_alloc(&zpath_lib_allocator);
    g_cust_topic_rate_stats_tbl =  zhash_table_alloc(&zpath_lib_allocator);;
    g_port_group_cust_topic_rate_stats_tbl =  zhash_table_alloc(&zpath_lib_allocator);

    stats_lock = ZPATH_MUTEX_INIT;
    global_kconns.lock = ZPATH_MUTEX_INIT;
    ZPATH_MUTEX_LOCK(&(global_kconns.lock), __FILE__, __LINE__);
    ZTAILQ_INIT(&(global_kconns.k_conns));
    ZPATH_MUTEX_UNLOCK(&(global_kconns.lock), __FILE__, __LINE__);

    g_rate_limit_garbage.lock = ZPATH_MUTEX_INIT;
    ZPATH_MUTEX_LOCK(&(g_rate_limit_garbage.lock), __FILE__, __LINE__);
    ZTAILQ_INIT(&(g_rate_limit_garbage.queue));
    g_rate_limit_garbage.count = 0;
    g_rate_limit_garbage.total_added = 0;
    g_rate_limit_garbage.total_removed = 0;
    g_rate_limit_garbage.last_run_time = 0;
    ZPATH_MUTEX_UNLOCK(&(g_rate_limit_garbage.lock), __FILE__, __LINE__);

    g_rate_limit_stats_q.lock = ZPATH_MUTEX_INIT;
    ZPATH_MUTEX_LOCK(&(g_rate_limit_stats_q.lock), __FILE__, __LINE__);
    ZTAILQ_INIT(&(g_rate_limit_stats_q.queue));
    ZTAILQ_INIT(&(g_rate_limit_stats_q.free_queue));
    g_rate_limit_stats_q.total_callocs = 0;
    g_rate_limit_stats_q.curr_free = 0;
    g_rate_limit_stats_q.curr_used = 0;
    ZPATH_MUTEX_UNLOCK(&(g_rate_limit_stats_q.lock), __FILE__, __LINE__);

    res = argo_library_init(2048);
    if (res) {
        fprintf(stderr, "%s:%s:%d: argo_library_init failed: %d\n", __FILE__, __FUNCTION__, __LINE__, res);
        return res;
    }

    natural_config_description = argo_register_global_structure(NATURAL_CONFIG_HELPER);
    natural_kafka_stats_description = argo_register_global_structure(NATURAL_KAFKA_STATS_HELPER);
    natural_kafka_broker_stats_description = argo_register_global_structure(NATURAL_KAFKA_BROKER_STATS_HELPER);
    natural_kafka_partition_stats_description = argo_register_global_structure(NATURAL_KAFKA_PARTITION_STATS_HELPER);
    natural_kafka_topic_stats_description = argo_register_global_structure(NATURAL_KAFKA_TOPIC_STATS_HELPER);
    natural_stats_description = argo_register_global_structure(NATURAL_STATS_HELPER);
    k_conn_stats_description = argo_register_global_structure(KAFKA_CONN_STATE_HELPER);
    rate_bucket_stats_description = argo_register_global_structure(RATE_BUCKET_STATS_HELPER);
    rate_limit_config_description = argo_register_global_structure(RATE_LIMIT_CONFIG_HELPER);
    return ZPATH_RESULT_NO_ERROR;
}

struct argo_object* parse_config_file(const char* filename, const char* config_obj ) {
            FILE *fp = fopen(filename, "r");
            if (!fp) {
                fprintf(stderr, "Could not open config file %s\n", filename);
                return NULL;
            }
            char *file_contents = malloc(100000);
            size_t bytes;
            bytes = fread(file_contents, 1, 100000, fp);
            if (!bytes) {
                fprintf(stderr, "Could not read data from file %s: Empty?\n", filename);
                free(file_contents);
                fclose(fp);
                return NULL;
            }

            struct argo_object *config_object = argo_deserialize_json(file_contents, bytes);
            if (!config_object) {
                fprintf(stderr, "Could not get configuration object from file %s\n", filename);
                free(file_contents);
                fclose(fp);
                return NULL;
            }

            if (strcmp(argo_object_get_type(config_object), config_obj )) {
                fprintf(stderr, "Read improper object type %s, was expecting type \"%s\"\n",
                        argo_object_get_type(config_object),config_obj);
                free(file_contents);
                fclose(fp);
                return NULL;
            }
            free(file_contents);
            fclose(fp);
            return config_object;
}

static void add_to_garbage_queue(struct rate_limit_tbls_ref *l_ref ){
    struct rate_limit_garbage_desc *garbage_item = NATURAL_CALLOC(sizeof(*garbage_item));
    garbage_item->free_req_time_s = epoch_s();
    garbage_item->rate_limit_tbls = l_ref;
    ZPATH_MUTEX_LOCK(&(g_rate_limit_garbage.lock), __FILE__, __LINE__);
    ZTAILQ_INSERT_TAIL(&(g_rate_limit_garbage.queue), garbage_item, list);
    g_rate_limit_garbage.count++;
    g_rate_limit_garbage.total_added++;
    ZPATH_MUTEX_UNLOCK(&(g_rate_limit_garbage.lock), __FILE__, __LINE__);
}
static void reset_rate_limit_config_file() {
    struct stat st;
    char buf[10000] = {'\0'};
    size_t written = 0;
    if ( !g_rate_limit_tbls->rate_limit_config_obj ) {
        ZPATH_LOG(AL_ERROR,"Failed to dump configuration; No valid config obj");
        return;
    }

    int res = argo_object_dump(g_rate_limit_tbls->rate_limit_config_obj,
                                    buf, sizeof(buf), &written, 1);
    if ( res ) {
        ZPATH_LOG(AL_ERROR,"Failed to dump the configuration; argo_object_dump failed with %d", res);
        return;
    }
    if ( ! (written < sizeof(buf) ) ) {
        ZPATH_LOG(AL_ERROR,"Failed to dump the configuration;Buffer size too small");
        return;
    }

    FILE *fp = fopen(g_rate_limit_config_file,"w");
    if ( !fp ) {
        ZPATH_LOG(AL_ERROR,"Failed to reset %s file to valid configuration",
                             g_rate_limit_config_file);
        return;
    }
    fprintf(fp,"%s\n",buf);
    fclose(fp);

    if (stat(g_rate_limit_config_file, &st) == 0) {
        g_rate_limit_config_mtime = st.st_mtime;
    }
    ZPATH_LOG(AL_NOTICE,"%s file is reset with valid configuration" , g_rate_limit_config_file);
    return;
}
static void config_file_change_check(int sock, short events, void *cookie)
{
    struct stat st;
    int changed = 0;
    struct argo_object *obj = NULL;
    static int called_count = 0;
    //Call stats every 5 minutes
    int div = RATE_LIMIT_STATS_UPLOAD_FREQ_S/RATE_LIMIT_CONFIG_FILE_CHECK_FREQ_S;
    if ( called_count % div == 0 ){
        called_count = 0;
        struct rate_limit_tbls_ref *l_ref = g_rate_limit_tbls;
        if ( l_ref->rate_limit_config &&
                l_ref->rate_limit_config->rate_limit_enabled)
        {
            log_rate_limit_stats();
        }
    }
    called_count++;
    if (stat(g_rate_limit_config_file, &st) == 0) {
        if (st.st_mtime != g_rate_limit_config_mtime ) {
            g_rate_limit_config_mtime = st.st_mtime;
            changed = 1;
            ZPATH_LOG(AL_NOTICE,"Change detected in rate limit config file");
        }
    }
    if ( changed ){
        obj = parse_config_file(g_rate_limit_config_file,"rate_limit_config");
        if ( !obj ) {
            char buf[8000];
            ZPATH_LOG(AL_ERROR,"Failed to parse edit rate limit config file %s",g_rate_limit_config_file);
            ZPATH_LOG(AL_ERROR,"Correct errors and retry! \n" );
            if ( g_rate_limit_tbls->rate_limit_config_obj) {
                ZPATH_LOG(AL_ERROR,"Continuing with original configuration as below");
                int res = argo_object_dump(g_rate_limit_tbls->rate_limit_config_obj,
                                            buf, sizeof(buf), NULL, 1);
                if ( res ) {
                    ZPATH_LOG(AL_ERROR,"Failed to dump the configuration");
                    return;
                }
                ZPATH_LOG(AL_NOTICE,"%s", buf);
                return;
            }
            return;
        }
        struct rate_limit_config *new_config = obj->base_structure_void;
        {
            int res = 0;
            struct rate_limit_tbls_ref *l_ref = NATURAL_CALLOC(sizeof(*l_ref));
            l_ref->rate_limit_config_obj = obj;
            l_ref->rate_limit_config = new_config;
            l_ref->topic_rate_tbl = zhash_table_alloc(&zpath_lib_allocator);
            l_ref->rate_limit_skiplist_tbl = zhash_table_alloc(&zpath_lib_allocator);
            l_ref->rate_limit_pick_first_tbl = zhash_table_alloc(&zpath_lib_allocator);
            l_ref->port_group_customer_topic_rate_tbl = zhash_table_alloc(&zpath_lib_allocator);
            l_ref->port_group_customer_topic_rate_tbl_lock = ZPATH_MUTEX_INIT;
            l_ref->port_group_list_per_topic_customer_tbl = zhash_table_alloc(&zpath_lib_allocator);
            l_ref->port_group_list_per_topic_customer_tbl_lock = ZPATH_MUTEX_INIT;
            l_ref->customer_topic_rate_tbl = zhash_table_alloc(&zpath_lib_allocator);
            l_ref->customer_topic_rate_tbl_lock = ZPATH_MUTEX_INIT;
            l_ref->customer_list_per_topic_tbl = zhash_table_alloc(&zpath_lib_allocator);
            ZPATH_LOG(AL_NOTICE,"Rate limit initialization for new config - STARTED");
            res = rate_limit_config_validate(l_ref);
            if ( res ){
                ZPATH_LOG(AL_ERROR,"New rate limit config validation FAILED");
                ZPATH_LOG(AL_NOTICE,"Rate limit initialization for new config - ABORTED");
                add_to_garbage_queue(l_ref);
                reset_rate_limit_config_file();
                return;
            }
            res = rate_limit_init(l_ref);
            if ( res ) {
                ZPATH_LOG(AL_ERROR,"Failed to update rate limit config; Using old config");
                ZPATH_LOG(AL_NOTICE,"Rate limit initialization for new config - ABORTED");
                add_to_garbage_queue(l_ref);
                reset_rate_limit_config_file();
                return;
            }
            res = port_group_init(l_ref);
            if ( res ) {
                ZPATH_LOG(AL_ERROR,"Failed to update port group config; Using old config");
                ZPATH_LOG(AL_NOTICE,"Rate limit initialization for new config - ABORTED");
                add_to_garbage_queue(l_ref);
                reset_rate_limit_config_file();
                return;
            }
            rate_limit_customer_skiplist_init(l_ref);
            rate_limit_customer_pick_first_init(l_ref);

            // Add old data to garbage queue for eventual freeing
            add_to_garbage_queue(g_rate_limit_tbls);
            // Update the config
            g_rate_limit_tbls = l_ref;
            ZPATH_LOG(AL_NOTICE,"Rate limit initialization for new config - COMPLETE");

        }
    }
}
void parse_rate_limit_config(void){
    struct argo_object* obj = parse_config_file(g_rate_limit_config_file,"rate_limit_config");
    if ( !obj ) {
        fprintf(stderr, "Error parsing rate limit config file %s \n", g_rate_limit_config_file );
        exit(1);
    }
    struct stat st;
    if (stat(g_rate_limit_config_file, &st) == 0)
        g_rate_limit_config_mtime = st.st_mtime;
    g_rate_limit_tbls->rate_limit_config = obj->base_structure_void;
    g_rate_limit_tbls->rate_limit_config_obj = obj;
}
struct natural_config *sub_main_parse_args(int argc, char *argv[])
{
    int i;
    int res;
    struct argo_object* obj = NULL;
    res = zpath_app_logging_parse_args(&argc, argv);
    if (res) return NULL;

    for (i = 1; i < argc; i++) {
        if (strcmp(argv[i], "-version") == 0) {
            fprintf(stdout, "%s\n", ZPATH_VERSION);
            exit(0);
        } else if (strcmp(argv[i], "-debuglog") == 0) {
            debuglog = 1;
        } else if (strcmp(argv[i], "-printf") == 0) {
            argo_log_use_printf(1);
        } else if (strcmp(argv[i], "-itasca_logs") == 0) {
            i++;
            itasca_logging_port_he = atoi(argv[i]);
        } else if (strcmp(argv[i], "-debugconns") == 0) {
            do_debug_conns = 1;
        } else if (strcmp(argv[i], "-maxlogmb") == 0) {
            i++;
            zpath_app_set_specific_max_logging_mb(atoi(argv[i]));
        } else if (strcmp(argv[i], "-rate_limit_config") == 0) {
            if ( (i + 1) >= argc ) {
                fprintf(stderr, "rate_limit_config needs a filename \n");
                exit(1);
            } else {
                g_rate_limit_config_file = argv[i+1];
                g_rate_limit_arg_present = 1;
                i++;
                parse_rate_limit_config();
            }
        } else {
            obj = parse_config_file(argv[i],"natural_config");
        }
    }
    if (obj)
        return obj->base_structure_void;
    return NULL;
}

int sub_main_check_config(struct natural_config *config)
{
    /* Process config into real config... (for those fields that need it) */
    if (!global_config->cloud) {
        fprintf(stderr, "'cloud' must be specified in configuration\n");
        return ZPATH_RESULT_ERR;
    }
    if (!global_config->kafka_brokers) {
        fprintf(stderr, "'kafka_brokers' must be specified in configuration\n");
        return ZPATH_RESULT_ERR;
    }
    if (!global_config->kafka_topics_count) {
        fprintf(stderr, "'kafka_topics' must be specified in configuration\n");
        return ZPATH_RESULT_ERR;
    }
    if (!global_config->identity && !global_config->cert_filename) {
        fprintf(stderr, "Either 'identity' or 'cert_filename' must be specified in configuration\n");
        return ZPATH_RESULT_ERR;
    }
    return ZPATH_RESULT_NO_ERROR;
}

int sub_main_process_config_into_globals(struct natural_config *config)
{
    char *s, *e;
    char path_str[1000];

    /* Set default logging intervals */
    if (!config->client_stats_interval_ms) {
        config->client_stats_interval_ms = 60*1000;
    }

    /* Generate path for certs... */
    if (global_config->cert_path) {
        snprintf(path_str, sizeof(path_str), "%s", global_config->cert_path);
    } else {
        snprintf(path_str, sizeof(path_str), "./");
    }

    /* Generate full identity, including environment variable value */
    s = identity;
    e = s + sizeof(identity);
    if (global_config->identity) {
        s += sxprintf(s, e, "%s", global_config->identity);
    } else {
        /* Get from certificate file */
        struct zcrypt_cert *cert;
        char cert_cn[256];

        snprintf(cert_filename_str, sizeof(cert_filename_str), "%s%s", path_str, global_config->cert_filename);
        cert = zcrypt_cert_read(cert_filename_str);
        if (!cert) {
            ZPATH_LOG(AL_ERROR, "Could not read cert file %s", cert_filename_str);
            return ZPATH_RESULT_ERR;
        }

        if (fohh_x509_get_cn(zcrypt_cert_get_x509(cert), cert_cn, sizeof(cert_cn))) {
            ZPATH_LOG(AL_ERROR, "Could not get CN from cert file %s", cert_filename_str);
            return ZPATH_RESULT_ERR;
        }
        zcrypt_cert_free(cert);
        s += sxprintf(s, e, "%s", cert_cn);
    }

    /* Set those fields that might depend on identity */
    if (!global_config->ca_filename) {
        snprintf(ca_filename_str, sizeof(ca_filename_str), "%s%s.crt", path_str, global_config->cloud);
    } else {
        snprintf(ca_filename_str, sizeof(ca_filename_str), "%s%s", path_str, global_config->ca_filename);
    }
    if (!global_config->cert_filename) {
        snprintf(cert_filename_str, sizeof(cert_filename_str), "%s%s.crt", path_str, identity);
    } else {
        snprintf(cert_filename_str, sizeof(cert_filename_str), "%s%s", path_str, global_config->cert_filename);
    }
    if (!global_config->key_filename) {
        snprintf(key_filename_str, sizeof(key_filename_str), "%s%s.key", path_str, identity);
    } else {
        snprintf(key_filename_str, sizeof(key_filename_str), "%s%s", path_str, global_config->key_filename);
    }

    /* Append system ID to identity... */
    char *sys_id;
    if (global_config->id_environment) {
        sys_id = getenv(global_config->id_environment);
        if (!sys_id) {
            fprintf(stderr, "Could not read requested environment variable %s\n", global_config->id_environment);
        }
    } else {
        sys_id = getenv("SYS_ID");
    }
    if (sys_id) {
        char sys_id_nospaces[256];
        char *w;
        snprintf(sys_id_nospaces, sizeof(sys_id_nospaces), "%s", sys_id);
        for (w = sys_id_nospaces; *w; w++) if ((*w) == ' ') *w = '-';
        s += sxprintf(s, e, "-%s", sys_id_nospaces);
    }
    return ZPATH_RESULT_NO_ERROR;
}
int rate_limit_config_validate(struct rate_limit_tbls_ref *l_ref ) {
    int64_t min_duration = l_ref->rate_limit_config->rate_limit_min_duration;
    if ( min_duration < 60 ) {
        ZPATH_LOG(AL_ERROR,"Rate limit minimum duration is less than 60");
        return ZPATH_RESULT_ERR;
    }
    return ZPATH_RESULT_NO_ERROR;
}

static int check_slow_queue(void *cookie,
                            void *object,
                            void* key,
                            size_t key_len)
{
    int *err = (int*) cookie;
    struct rate_bucket *rate = (struct rate_bucket*) object;
    char slow_topic_str[128] = {'\0'};
    snprintf(slow_topic_str,sizeof(slow_topic_str),"%s_slow",rate->topic);
    if (zhash_table_lookup(all_topics,slow_topic_str,strlen(slow_topic_str),NULL) == NULL ) {
        *err =1;
        ZPATH_LOG(AL_ERROR,"Deadletter topic use is specified."
                            "However topic %s was not found.",slow_topic_str);
    }
    return 0;
}
void free_topic_rate_tbl(struct rate_limit_tbls_ref *l_ref)
{
    int64_t walk_key = 0;
    rate_bucket_type_t table_type = TOPIC;
    while ( zhash_table_walk(l_ref->topic_rate_tbl,
                             &walk_key,
                             free_rate_limit_tbl_entry,
                             &table_type ) == ZHASH_RESULT_INCOMPLETE ) {}
}
int rate_limit_init( struct rate_limit_tbls_ref *l_ref )
{
    static int thread_started = 0;
    char** rate_limit_configs = l_ref->rate_limit_config->rate_limit_configs;
    size_t rate_limit_configs_count = l_ref->rate_limit_config->rate_limit_configs_count;
    if ( l_ref->rate_limit_config->rate_limit_enabled )
        ZPATH_LOG(AL_NOTICE,"Rate limit feature is enabled");
    else
        ZPATH_LOG(AL_NOTICE,"Rate limit feature is disabled");
    if ( l_ref->rate_limit_config->rate_limit_simulation_mode )
        ZPATH_LOG(AL_NOTICE,"Rate limit feature is in simulation mode");
    ZPATH_LOG(AL_NOTICE,"Minimum duration for rate limit is %d",
                l_ref->rate_limit_config->rate_limit_min_duration );
    int slow_queue = l_ref->rate_limit_config->rate_limit_deadletter_topic;

    struct rate_bucket *rate = NULL;
    for (int j = 0; j + 2 < rate_limit_configs_count ; j += 3) {
        if (!rate_limit_configs[j] || !rate_limit_configs[j+1] || !rate_limit_configs[j+2] )
            continue;
        char* topic_str = rate_limit_configs[j];
        ZPATH_LOG(AL_NOTICE,"Found topic %s in rate limit config",topic_str );
        if ( zhash_table_lookup ( all_topics , topic_str, strlen(topic_str), NULL) == NULL ) {
            ZPATH_LOG(AL_ERROR,"Topic %s for rate limit configuration is not initialized." , topic_str );
            continue;
        }
        if ( zhash_table_lookup(l_ref->topic_rate_tbl,topic_str,strlen(topic_str),NULL) == NULL ) {
            rate = NATURAL_CALLOC(sizeof(*rate));
            rate->topic = NATURAL_STRDUP(topic_str,strlen(topic_str));
            rate->port_group_name = "TOPIC_ALL";
            rate->type = TOPIC;
            rate->customer_id = 0;
            rate->lock = ZPATH_MUTEX_INIT;
            rate->parent = NULL;
            rate->rx_log_list_count = 0;
            rate->rx_log_list_sum = 0;
            ZTAILQ_INIT(&(rate->queue));
            int64_t limit = strtoul(rate_limit_configs[j+1],NULL,10);
            if ( limit == 0 ) {
                ZPATH_LOG(AL_ERROR, "Unable to use %s for rate limit low watermark ",
                                    rate_limit_configs[j+1] );
                NATURAL_FREE(rate->topic);
                NATURAL_FREE(rate);
                return 1;
            } else {
                rate->low_watermark = limit;
                ZPATH_LOG(AL_NOTICE,"Found low watermark as %"PRId64
                                    " per second for topic %s in rate limit config",
                                    limit,topic_str );
            }
            limit = strtoul(rate_limit_configs[j+2],NULL,10);
            if ( limit == 0 ) {
                ZPATH_LOG(AL_ERROR, "Unable to use %s for rate limit high watermark ",
                                     rate_limit_configs[j+2] );
                NATURAL_FREE(rate->topic);
                NATURAL_FREE(rate);
                return 1;
            } else {
                ZPATH_LOG(AL_NOTICE,"Found high watermark as %"PRId64
                                    " per second for topic %s in rate limit config",
                                    limit,topic_str );
                rate->high_watermark = limit;
            }
            if ( rate->low_watermark >= rate->high_watermark ){
                ZPATH_LOG(AL_ERROR, "Low water %"PRId64
                                    " is greater than high watermark %"PRId64,
                                    rate->low_watermark,rate->high_watermark);
                NATURAL_FREE(rate->topic);
                NATURAL_FREE(rate);
                return 1;
            }
            zhash_table_store(l_ref->topic_rate_tbl,topic_str,strlen(topic_str),0,rate);
            ZPATH_LOG(AL_NOTICE, "Storing rate limit structs for topic %s", topic_str );
        } else {
            ZPATH_LOG(AL_NOTICE, "Topic %s already present in rate table ", topic_str );
        }

        if ( slow_queue ) {
            int64_t walk_key = 0;
            int error = 0;
            while( zhash_table_walk(l_ref->topic_rate_tbl,
                                    &walk_key,
                                    check_slow_queue,
                                    &error) == ZHASH_RESULT_INCOMPLETE ) {}
            if ( error )
                return 1;

        }
        if ( zhash_table_lookup(l_ref->customer_list_per_topic_tbl,
                                topic_str,strlen(topic_str),NULL) == NULL ) {
            struct rate_bucket_list *rb_list = NATURAL_CALLOC(sizeof(*rb_list));
            rb_list->lock = ZPATH_MUTEX_INIT;
            ZLIST_INIT(&(rb_list->list));
            zhash_table_store(l_ref->customer_list_per_topic_tbl,topic_str,strlen(topic_str), 0 , rb_list);
            ZPATH_LOG(AL_NOTICE, "Storing rate bucket list for customers for topic %s", topic_str );
        } else {
            ZPATH_LOG(AL_NOTICE, "Topic %s already present in customer rate bucket list table ", topic_str );
        }
    }

    if ( !thread_started ){
        struct zevent_base *rt_event_base = zevent_handler_create("calc_rates",
                                                                    512*1024,
                                                                    60);
        struct event *rt_timer = event_new(zevent_event_base(rt_event_base),
                                                                -1,
                                                                EV_PERSIST,
                                                                calc_rates,
                                                                NULL);
        struct timeval tv;
        tv.tv_sec = RATE_CALC_TIME_PERIOD_S;
        tv.tv_usec = 0;
        event_add(rt_timer, &tv);

        struct zevent_base *gc_event_base = zevent_handler_create("rate_limit_garbage_collector",
                                                                    512*1024,
                                                                    2*RATE_LIMIT_GARBAGE_CLEAR_FREQ_S);
        struct event *gc_timer = event_new(zevent_event_base(gc_event_base),
                                                                -1,
                                                                EV_PERSIST,
                                                                rate_limit_garbage_collector,
                                                                NULL);
        struct timeval tv1;
        tv1.tv_sec = RATE_LIMIT_GARBAGE_CLEAR_FREQ_S;
        tv1.tv_usec = 0;
        event_add(gc_timer, &tv1);

        struct zevent_base *rl_stats_event_base = zevent_handler_create("rate_limit_stats_collector",
                                                                    512*1024,
                                                                    6*RATE_LIMIT_STATS_COLLEC_FREQ_S);
        struct event *rl_stats_timer = event_new(zevent_event_base(rl_stats_event_base),
                                                                -1,
                                                                EV_PERSIST,
                                                                rate_limit_stats_collector,
                                                                NULL);
        struct timeval tv2;
        tv2.tv_sec = RATE_LIMIT_STATS_COLLEC_FREQ_S;
        tv2.tv_usec = 0;
        event_add(rl_stats_timer, &tv2);

        struct zevent_base *rl_cfg_event_base = zevent_handler_create("config_file_change_check",
                                                                    512*1024,
                                                                    3*RATE_LIMIT_CONFIG_FILE_CHECK_FREQ_S);
        struct event *rl_cfg_timer = event_new(zevent_event_base(rl_cfg_event_base),
                                                                -1,
                                                                EV_PERSIST,
                                                                config_file_change_check,
                                                                NULL);
        struct timeval tv3;
        tv3.tv_sec = RATE_LIMIT_CONFIG_FILE_CHECK_FREQ_S;
        tv3.tv_usec = 0;
        event_add(rl_cfg_timer, &tv3);


        thread_started = 1;
    }
    return 0;
}
static void rate_limit_customer_pick_first_init(struct rate_limit_tbls_ref *l_ref)
{
    char **rate_limit_customer_pick_first_list =
                        l_ref->rate_limit_config->rate_limit_customer_pick_first_list;
    uint16_t rate_limit_customer_pick_first_list_count =
                        l_ref->rate_limit_config->rate_limit_customer_pick_first_list_count;

    for ( int i = 0; i < rate_limit_customer_pick_first_list_count; i++ ){
        if (!rate_limit_customer_pick_first_list[i]) // Handle empty string input
            continue;
        int *present = zhash_table_lookup(l_ref->rate_limit_skiplist_tbl,
                                rate_limit_customer_pick_first_list[i],
                                strlen(rate_limit_customer_pick_first_list[i]),
                                NULL);
        if ( present == NULL ) {
            ZPATH_LOG(AL_NOTICE,"Rate limit will be first applied for customer %s due to pick first list",
                     rate_limit_customer_pick_first_list[i]);
            zhash_table_store_not_exist(l_ref->rate_limit_pick_first_tbl,
                                        rate_limit_customer_pick_first_list[i],
                                        strlen(rate_limit_customer_pick_first_list[i]),
                                        0,
                                        &g_skip_list_dummy);
        } else {
            ZPATH_LOG(AL_ERROR,"Customer already present in skiplist; Ignoring");
        }
    }
}

static void rate_limit_customer_skiplist_init(struct rate_limit_tbls_ref *l_ref)
{
    char **rate_limit_customer_skiplist = l_ref->rate_limit_config->rate_limit_customer_skiplist;
    uint16_t rate_limit_customer_skiplist_count = l_ref->rate_limit_config->rate_limit_customer_skiplist_count;

    for ( int i = 0; i < rate_limit_customer_skiplist_count; i++ ){
        if ( !rate_limit_customer_skiplist[i] ) // handle NULL strings in input
            continue;
        ZPATH_LOG(AL_NOTICE,"Rate limit will be skipped for customer %s due to skiplist",
                     rate_limit_customer_skiplist[i]);
        zhash_table_store_not_exist(l_ref->rate_limit_skiplist_tbl,
                                    rate_limit_customer_skiplist[i],
                                    strlen(rate_limit_customer_skiplist[i]),
                                    0,
                                    &g_skip_list_dummy);
    }

}

int port_group_init( struct rate_limit_tbls_ref *l_ref )
{
    size_t j = 0;
    size_t i = 0;
    char **port_group_conf_string = l_ref->rate_limit_config->port_groups;
    uint16_t port_group_string_count = l_ref->rate_limit_config->port_groups_count;
    if ( port_group_string_count )
        ZPATH_LOG(AL_NOTICE,"Port Group Init - STARTED");
    if ( port_group_string_count >= MAX_PORT_GROUPS ) {
        ZPATH_LOG(AL_ERROR,"Port group count %d is more than the maximum supported %d",
                    port_group_string_count,MAX_PORT_GROUPS );
        return 1;
    }
    for (j = 0; j + 2 < port_group_string_count; j += 3) {
        if ( !port_group_conf_string[j] || !port_group_conf_string[j+1] || !port_group_conf_string[j+2] )
            continue;
        l_ref->port_group_confs[i].name = port_group_conf_string[j];
        l_ref->port_group_confs[i].ports = port_group_conf_string[j+1];
        l_ref->port_group_confs[i].protocol = port_group_conf_string[j+2];
        ZPATH_LOG(AL_NOTICE,"port group name: %s",l_ref->port_group_confs[i].name);
        ZPATH_LOG(AL_NOTICE,"port group ports: %s",l_ref->port_group_confs[i].ports);
        ZPATH_LOG(AL_NOTICE,"port group protocol: %s",l_ref->port_group_confs[i].protocol);
        i++;
    }
    l_ref->port_group_count = i;
    if ( port_group_string_count )
        ZPATH_LOG(AL_NOTICE,"Port Group Init - COMPLETE");
    return 0;
}

int sub_main_init_logging(void)
{
    int res;

    res = argo_log_init(identity,                // const char *instance_name,
                        0,                       // int64_t instance_gid,
                        "natural",               // const char *instance_role,
                        ZPATH_VERSION,           // const char *instance_build,
                        1);                      // int is_thin_app);
    if (res) {
        ZPATH_LOG(AL_ERROR, "Could not re-init argo_log");
        return res;
    }

    if (global_config->stats_filename) {
        struct argo_log_file *stats_file;
        struct argo_log_reader *stats_file_reader;

        stats_file = argo_log_file_create(zpath_stats_collection,
                                          global_config->stats_filename,
                                          global_config->stats_filename,
                                          1024*1024*1024,
                                          argo_serialize_binary);
        if (!stats_file) {
            ZPATH_LOG(AL_WARNING, "Could not create stats log file named %s", global_config->stats_filename);
        } else {
            stats_file_reader = argo_log_read(zpath_stats_collection, "stats_file", 0, 1, argo_log_file_callback, NULL, stats_file, zpath_stats_collection, 60*1000*1000);
            if (!stats_file_reader) {
                ZPATH_LOG(AL_WARNING, "Could not create file based stats log reader");
            }
        }
    }


    if (!global_config->nosend_stats) {
        /* Use Ziastats domain if overriden via cmd line */
        if (global_config->zistats_domain) {
            snprintf(stats_hostname, sizeof(stats_hostname), "%s", global_config->zistats_domain);
        } else {
            snprintf(stats_hostname, sizeof(stats_hostname), "statslog-pg-ccf.%s", global_config->cloud);
        }
        res = fohh_log_send(stats_hostname,
                               "statistics_log",
                               stats_hostname,
                               "apilog",
                               NULL,
                               "statslog",
                               htons(itasca_logging_port_he),
                               NULL, // ssl_ctx
                               0, 0,
                               NULL);

        if (res) {
            ZPATH_LOG(AL_ERROR, "Could not attach stats logs to network");
            return res;
        }
    }

    res = fohh_log_custom_init(zpath_stats_collection);
    if (res) {
        ZPATH_LOG(AL_ERROR, "could not fohh_log_custom_init(): %s", zpath_result_string(res));
        return res;
    }

    return res;
}

int sub_main_init_listen(struct natural_config *config)
{
    struct sockaddr_storage listen_addr;
    socklen_t slen;

    int default_port_he = 443;
    int default_port_tlv_json_he = 445;

    int i, j;
    int res = 0;

    if (!config->listen_ips_count) {
        config->listen_ips_count = 1;
        config->listen_ips = &default_listen_ip;
    }
    if (!config->listen_port_he_count) {
        config->listen_port_he_count = 1;
        config->listen_port_tlv_json_he_count = 1;
        config->listen_port_he = &default_port_he;
        config->listen_port_tlv_json_he = &default_port_tlv_json_he;
    }

    if (config->listen_port_he_count) {
        struct fohh_generic_server *server = fohh_generic_server_create(1,  // int do_sni,
                                                                        1); // int32_t log_connection_with_bad_sni);

        for (i = 0; i < config->listen_ips_count; i++) {
            for (j = 0; j < config->listen_port_he_count; j++) {
                memset(&listen_addr, 0, sizeof(listen_addr));
                res = fohh_str_to_sockaddr_storage(config->listen_ips[i], &listen_addr, &slen);
                if (res) {
                    ZPATH_LOG(AL_ERROR, "could not convert listen ip %s: %s", config->listen_ips[i], zpath_result_string(res));
                    // TODO:allocated fohh_generic_server has to be free
                    return res;
                }
                fohh_sockaddr_set_port(&listen_addr, htons(config->listen_port_he[j]));
                res = fohh_generic_server_listen(server,
                                                 (struct sockaddr *) &listen_addr,
                                                 slen,
                                                 0); // int do_proxy_protocol);
                if (res) {
                    ZPATH_LOG(AL_ERROR, "could not listen: %s", zpath_result_string(res));
                    return res;
                }
                ZPATH_LOG(AL_NOTICE, "Listening on %s:%d for binary argo", config->listen_ips[i], config->listen_port_he[j]);
            }
        }

        res = fohh_log_custom_receive(server,   // server
                                      config->api_sni?config->api_sni:"apilog", // domain / SNI
                                      0,        // int wildcard_prefix,
                                      NULL,     // fohh_ssl_ctx_get_f *ssl_ctx_callback,
                                      NULL,     // fohh_connection_verify_f *verify_callback,
                                      log_callback_f,  // fohh_log_custom_callback_f *custom_callback_f)
                                      log_status_f, // fohh_log_custom_status_callback_f *custom_status_callback_f)
                                      config->client_stats_interval_ms * 1000,
                                      0,        // Dont use TLV
                                      0);       // Dont use JSON
        if (res) {
            // TODO:allocated fohh_generic_server has to be free
            ZPATH_LOG(AL_ERROR, "could not custom_receive: %s", zpath_result_string(res));
            return res;
        }
    }


    if (config->listen_port_tlv_json_he_count) {
        struct fohh_generic_server *server = fohh_generic_server_create(1,  // int do_sni,
                                                                        1); // int32_t log_connection_with_bad_sni);

        for (i = 0; i < config->listen_ips_count; i++) {
            for (j = 0; j < config->listen_port_tlv_json_he_count; j++) {
                memset(&listen_addr, 0, sizeof(listen_addr));
                res = fohh_str_to_sockaddr_storage(config->listen_ips[i], &listen_addr, &slen);
                if (res) {
                    ZPATH_LOG(AL_ERROR, "could not convert listen ip %s: %s", config->listen_ips[i], zpath_result_string(res));
                    // TODO:allocated fohh_generic_server has to be free
                    return res;
                }
                fohh_sockaddr_set_port(&listen_addr, htons(config->listen_port_tlv_json_he[j]));
                res = fohh_generic_server_listen(server,
                                                 (struct sockaddr *) &listen_addr,
                                                 slen,
                                                 0); // int do_proxy_protocol);
                if (res) {
                    ZPATH_LOG(AL_ERROR, "could not listen: %s", zpath_result_string(res));
                    return res;
                }
                ZPATH_LOG(AL_NOTICE, "Listening on %s:%d for json tlv", config->listen_ips[i], config->listen_port_tlv_json_he[j]);
            }
        }

        res = fohh_log_custom_receive(server,   // server
                                      config->api_sni?config->api_sni:"apilog", // domain / SNI
                                      0,        // int wildcard_prefix,
                                      NULL,     // fohh_ssl_ctx_get_f *ssl_ctx_callback,
                                      NULL,     // fohh_connection_verify_f *verify_callback,
                                      log_callback_f,  // fohh_log_custom_callback_f *custom_callback_f)
                                      log_status_f, // fohh_log_custom_status_callback_f *custom_status_callback_f)
                                      config->client_stats_interval_ms * 1000,
                                      1,        // Use TLV
                                      1);       // Use JSON
        if (res) {
            // TODO:allocated fohh_generic_server has to be free
            ZPATH_LOG(AL_ERROR, "could not custom_receive: %s", zpath_result_string(res));
            return res;
        }
    }

    return res;
}


int sub_main(int argc, char *argv[])
{
    int res;
    int index;

    if ((res = sub_main_init_globals())) return res;

    if (!((global_config = sub_main_parse_args(argc, argv)))) {
        fprintf(stderr, "Require specifying configuration file\n");
        usage_and_exit(argv[0]);
    }

    if ((res = sub_main_check_config(global_config))) usage_and_exit(argv[0]);


    if ((res = sub_main_process_config_into_globals(global_config))) return res;

    /* Standard zpath app initialization. */
    struct zpath_simple_app_init_params app_params;
    zpath_simple_app_init_params_default(&app_params);
    app_params.instance_name = identity;
    app_params.role_name = "natural";
    app_params.root_cert_file = ca_filename_str;
    app_params.cert_chain_file = cert_filename_str;
    app_params.private_key_file = key_filename_str;
    app_params.fohh_thread_count = global_config->thread_count ? : 4;
    app_params.log_syslog = 0;
    app_params.log_filename = global_config->logfile;
    app_params.debug_port = global_config->debug_port ? : debug_port;
    app_params.debuglog = debuglog;
    app_params.rbac_support = 0;    /* set it to 1 when naturals support rbac */

    res = zpath_simple_app_init(&app_params);
    if (res) {
        ZPATH_LOG(AL_ERROR, "zpath_simple_app_init() failed : %s", zpath_result_string(res));
        return res;
    }

    fohh_set_debug_new_connections(do_debug_conns);

    ZPATH_LOG(AL_NOTICE, "Using system identity %s", identity);

    /* Turn off sigpipe... */
    signal(SIGPIPE, SIG_IGN);

    /* Init RPCs so natural knows more info about serializing
     * objects. (nozero, stringify, etc) */
    res = zpn_rpc_init();
    if (res) {
        ZPATH_LOG(AL_ERROR, "Could not init zpn_rpc: %s", zpath_result_string(res));
        return res;
    }

    /* Register zpn_event log argo descriptions so that natural can serialize
     * related argo objects properly. (nozero, stringify, etc) */
    res = zpn_event_register_descriptions();
    if (res) {
        ZPATH_LOG(AL_ERROR, "Could not register zpn_event log descriptions: %s", zpath_result_string(res));
        return res;
    }

    /* Init logging */
    if ((res = sub_main_init_logging())) return res;

    /* unlimit logging here */
    for (index = 0; index < LOG_PRIORITY; index++) {
        argo_log_max_text_per_line_per_second[index] = argo_log_get_max_text_per_line_per_s(index);
    }

    argo_log_set_max_text_per_line_per_s(MAX_TEXT_PER_LINE);  /* per second */

    res = kafka_init(global_config->kafka_brokers,
                     global_config->kafka_configs, global_config->kafka_configs_count,
                     global_config->kafka_topics, global_config->kafka_topics_count,
                     global_config->kafka_topic_configs, global_config->kafka_topic_configs_count);

    /* re-limit logging here */
    for (index = 0; index < LOG_PRIORITY; index++) {
        argo_log_reset_max_text_per_line_per_s(argo_log_max_text_per_line_per_second[index], index);
    }

    if (res) {
        ZPATH_LOG(AL_ERROR, "could not initializei kafka: %s", zpath_result_string(res));
        return res;
    }

    if ( !g_rate_limit_arg_present )
    {
        ZPATH_LOG(AL_NOTICE, "-rate_limit_config was not specified in the arguments");
        ZPATH_LOG(AL_NOTICE, "Auto generation of rate limit configuration - START ");
        ZPATH_LOG(AL_NOTICE, "Rate limit configuration file : %s", RATE_LIMIT_SAMPLE_CONF_FILE );
        FILE *fp = NULL;
        fp = fopen(RATE_LIMIT_SAMPLE_CONF_FILE, "w");
        if ( fp ) {
            fprintf(fp,"%s",g_rate_limit_sample_conf);
            fclose(fp);
            ZPATH_LOG(AL_NOTICE, "Auto generation of rate limit configuration - COMPLETE ");
            ZPATH_LOG(AL_NOTICE, "Editing/changing this configuration file reflects"
                                 "in c-pg without a restart");
            g_rate_limit_config_file = RATE_LIMIT_SAMPLE_CONF_FILE;
            parse_rate_limit_config();
        } else {
            ZPATH_LOG(AL_NOTICE, "Auto generation of rate limit configuration - FAILED ");
        }

    }
    struct rate_limit_tbls_ref *l_ref = g_rate_limit_tbls;

    if ( l_ref->rate_limit_config )
    {
        res = 0;
        ZPATH_LOG(AL_NOTICE,"Rate limit initialization - STARTED");
        res = rate_limit_config_validate(l_ref);
        if ( res ) {
            ZPATH_LOG(AL_ERROR, "Rate limit config validation failed");
            ZPATH_LOG(AL_NOTICE,"Rate limit initialization - ABORTED ");
            return res;
        }
        res = rate_limit_init(l_ref);
        if ( res ) {
             ZPATH_LOG(AL_ERROR, "Rate limit config configuration error" );
             ZPATH_LOG(AL_NOTICE,"Rate limit initialization - ABORTED ");
             return res;
        }
        res = port_group_init(l_ref);
        if ( res ) {
            ZPATH_LOG(AL_ERROR, "Port group configuration error" );
            ZPATH_LOG(AL_NOTICE,"Rate limit initialization - ABORTED ");
            return res;
        }
        rate_limit_customer_skiplist_init ( l_ref );
        rate_limit_customer_pick_first_init( l_ref );
        l_ref->config_valid = 1;
        ZPATH_LOG(AL_NOTICE,"Rate limit initialization - COMPLETE ");

    }

    if ((res = sub_main_init_listen(global_config))) return res;

    res = zpath_debug_add_read_command("Show last kafka stats sample json",
                                  "/kafka/stats",
                                  natural_debug_kafka_stats,
                                  NULL,
                                  NULL);

    res = zpath_debug_add_read_command("Show natural active connection stats",
                                  "/natural/stats",
                                  natural_debug_internal_stats,
                                  NULL,
                                  "small", "List only k_conns with non-zero message count",
                                  "total", "List only total",
                                  NULL);
    res =  zpath_debug_add_read_command("Show rate bucket stats",
                                   "/natural/rate_limit/rate_buckets",
                                   natural_rate_bucket_stats,
                                   NULL,
                                   "file_name", "file name to dump the stats",
                                   NULL);
    res =  zpath_debug_add_read_command("Show rate limit running config ",
                                   "/natural/rate_limit/config",
                                   natural_rate_limit_config_dump,
                                   NULL,
                                   NULL);
    res =  zpath_debug_add_read_command("Show rate limit garbage collector stats ",
                                   "/natural/rate_limit/gc_stats",
                                   natural_rate_limit_gc_stats,
                                   NULL,
                                   NULL);
    res =  zpath_debug_add_read_command("Show rate limit stats desc queue details ",
                                   "/natural/rate_limit/stats_queue",
                                   natural_rate_limit_stats_queue,
                                   NULL,
                                   NULL);
    res =  zpath_debug_add_read_command("Show rate limit skipped logs count",
                                   "/natural/rate_limit/skipped_logs",
                                   natural_rate_limit_skipped_logs,
                                   NULL,
                                   NULL);
    #ifdef UNIT_TEST
        res = zpath_debug_add_read_command("Show natural total logs received",
                                  "/natural/logs_rxd",
                                  natural_debug_logs_rxd_stats,
                                  NULL,
                                  NULL);
    #endif
    zpath_debug_add_allocator(&natural_allocator, "natural");
    ZPATH_LOG(AL_NOTICE, "Initialization Complete");
    zpath_registration_completed();

    while(1) sleep(1);
}


int main(int argc, char *argv[])
{
    int ret = sub_main(argc, argv);
    usleep(50000);
    return ret;
}
