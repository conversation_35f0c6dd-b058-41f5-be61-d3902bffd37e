#
# Makefile for doing what RPM needs. Note that we don't really use RPM
# to build. We're just putting things in the right places.
#

PACKAGE_NAME=zpa-pcc
APPLICATIONS_BIN=zpa-pcc insight
LICENSE=license.txt
SYSTEMD_UNIT_FILE=zpa-pcc.service
FULLDIR=$(DESTDIR)/opt/zscaler

install:
	mkdir -p $(FULLDIR)/bin
	mkdir -p $(FULLDIR)/share
	cp $(APPLICATIONS_BIN) $(FULLDIR)/bin
	cp $(LICENSE) $(FULLDIR)/share/$(PACKAGE_NAME).$(LICENSE)
