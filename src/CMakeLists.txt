include_directories(${CMAKE_CURRENT_SOURCE_DIR} ${CMAKE_CURRENT_BINARY_DIR})

add_subdirectory(argo)
add_subdirectory(avl)
add_subdirectory(cshash)
add_subdirectory(jenkins_hash)
add_subdirectory(base64)
add_subdirectory(icurl)
add_subdirectory(blockd)
add_subdirectory(cert)
add_subdirectory(cityhash-c)
add_subdirectory(diamond)
add_subdirectory(pattern_match)
add_subdirectory(exporter)
add_subdirectory(uatu)
add_subdirectory(fohh)
add_subdirectory(lookup)
add_subdirectory(lookup_lib)
add_subdirectory(npwg_lib)
add_subdirectory(np_connector)
add_subdirectory(object_store)
add_subdirectory(parson)
add_subdirectory(phash)
add_subdirectory(zpa_insight)
add_subdirectory(sarge)
add_subdirectory(shifty)
add_subdirectory(slogger)
add_subdirectory(udp_test)
add_subdirectory(wally)
add_subdirectory(wally_test)
add_subdirectory(wallyd)
add_subdirectory(zbuffer)
add_subdirectory(zudp_conn)
add_subdirectory(zcdns)
add_subdirectory(zcrypt)
add_subdirectory(zcrypto_lib)
add_subdirectory(zevent)
add_subdirectory(zhash)
add_subdirectory(zhealth)
add_subdirectory(zhw)
add_subdirectory(zlibevent)
add_subdirectory(zpath_lib)
add_subdirectory(zpath_misc)
add_subdirectory(np_lib)
add_subdirectory(zpn_inspection)
add_subdirectory(zpn_zdx)
add_subdirectory(zpn_waf)
add_subdirectory(zpn_dr)
add_subdirectory(admin_probe)
add_subdirectory(zpn_pcap)
add_subdirectory(zpn_event)
add_subdirectory(zpn_cache)
add_subdirectory(zpn_pdp)
add_subdirectory(zpn)
add_subdirectory(l4proxy)
add_subdirectory(zpn_ot)
add_subdirectory(zpn_assistantd)
add_subdirectory(zpm)
add_subdirectory(zblu)
add_subdirectory(zhm)
add_subdirectory(zpn_brokerd)
add_subdirectory(zpn_clientd)
add_subdirectory(zpn_dispatcherd)
add_subdirectory(zpn_l4proxyd)
add_subdirectory(zpn_enrollment_lib)
add_subdirectory(zpn_private_brokerd)
add_subdirectory(zpn_sitecd)
add_subdirectory(zpn_iparsd)
add_subdirectory(zradix)
add_subdirectory(zrdt)
add_subdirectory(zrdt_test)
add_subdirectory(c2c_fqdn_check_test)
add_subdirectory(zsaml)
add_subdirectory(zsdtls)
add_subdirectory(zthread)
add_subdirectory(ztimer)
add_subdirectory(ztlv)
add_subdirectory(zvm)
add_subdirectory(natural)
add_subdirectory(tools)
add_subdirectory(zber)
add_subdirectory(zpn_npgatewayd)
add_subdirectory(rebound)
