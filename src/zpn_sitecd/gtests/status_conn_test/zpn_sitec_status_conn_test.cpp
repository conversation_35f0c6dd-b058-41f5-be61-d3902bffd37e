/*
 * zpn_sitec_status_conn_test.cpp. Copyright (C) 2025 Zscaler, Inc. All Rights Reserved
 */

 #include <gtest/gtest.h>
 #include <gmock/gmock.h>

 #ifdef __clang__
 #pragma clang diagnostic push
 #pragma clang diagnostic ignored "-Wunused-parameter"
 #elif defined __GNUC__
 #pragma GCC diagnostic push
 #pragma GCC diagnostic ignored "-Wunused-parameter"
 #endif // __clang__

 #include <cstdint>

 #include "test_misc/ZscalerMockBase.h"

 extern "C" {

#define INSTANCE_ID_BYTES 32

#include "zpn/zpn_lib.h"
#include "fohh/fohh.h"
#include "zpn_sitecd/zpn_sitec_sitec_conn.h"
#include "zpn_sitecd/zpn_sitec_private.h"
#include "fohh/fohh_private.h"
#include "fohh/fohh.h"
#include "wally/wally.h"
#include "zpn/zpn_sitec_table.h"
#include "zpn/zpn_sitec_group.h"
#include "zpn/zpn_sitec_to_group.h"
#include "zpath_lib/zpath_customer.h"
#include "argo/argo_private.h"
#include "zpath_lib/zpath_cloud.h"
#include "zpath_lib/zpath_et_wally_userdb.h"
//#include "wally/wally_private.h"

struct zpn_sitec_global_state sitec_gs;

struct zpn_sitec_global_state* zpn_get_sitec_global_state(void)
{
    /* Return pointer to singleton private broker global state */
    return &sitec_gs;
}
extern int zpn_enroll_get_private_key(struct zcrypt_key *key, struct zcrypt_rsa_key *rsa_key, char *filename);
int zpn_sitec_create_wally_fohh_servers();
struct wally_fohh_server *wally_fohh_server_create(struct wally *wally,
                                                    enum argo_serialize_mode encoding,
                                                    enum fohh_connection_style style,
                                                    int no_service_ip,
                                                    struct argo_inet *service_ip,
                                                    uint16_t tcp_port_ne,
                                                    char *root_cert_file_name,
                                                    char *my_cert_file_name,
                                                    char *my_cert_key_file_name,
                                                    int use_ssl);
};

using namespace std;
class SitecStatusConnMock {
    public:
        MOCK_METHOD(int, zpn_sitec_is_split_brain_cfg_sync_enabled, ());
        MOCK_METHOD(int64_t, wally_get_total_max_seq_with_hash, (struct wally*, uint64_t*));
        MOCK_METHOD(void, fohh_connection_disconnect, (struct fohh_connection*, const char*));
        MOCK_METHOD(int, zpn_sitec_group_get_by_site_gid, (int64_t, struct zpn_site_controller_group **, size_t*, wally_response_callback_f, void*, int64_t));
        MOCK_METHOD(int, zpn_sitec_to_group_get_by_grpid, (int64_t, struct zpn_site_controller_to_group **, size_t*, wally_response_callback_f, void*, int64_t));
        MOCK_METHOD(int, zpn_sitec_group_get_by_gid, (int64_t, struct zpn_site_controller_group **, size_t*, int, wally_response_callback_f, void*, int64_t));
        MOCK_METHOD(int, zpath_customer_get, (int64_t, struct zpath_customer**, wally_response_callback_f, void*, int64_t));
        MOCK_METHOD(int, zpn_sitec_get_by_id, (int64_t, struct zpn_site_controller**, wally_response_callback_f, void*, int64_t));
        MOCK_METHOD(int, zpn_sitec_to_group_get_by_sitec_gid, (int64_t, struct zpn_site_controller_to_group**, size_t*, int, wally_response_callback_f, void*, int64_t));
        MOCK_METHOD(struct event_base*, zevent_event_base, (struct zevent_base*));
        MOCK_METHOD(struct wally_fohh_server*, zpath_et_wally_userdb_get_userdb_wally_server, (int64_t, int64_t));
};

#define MOCK_SPLIT_BRAIN_CFG_SYNC_ENABLED(T)                                \
    int zpn_sitec_is_split_brain_cfg_sync_enabled() {                       \
        return T::get_mock()->zpn_sitec_is_split_brain_cfg_sync_enabled();  \
    }

#define MOCK_WALLY_GET_TOTAL_MAX_SEQ_WITH_HASH(T)                                        \
    int64_t wally_get_total_max_seq_with_hash(struct wally *wally, uint64_t *hash) {     \
        return T::get_mock()->wally_get_total_max_seq_with_hash(wally, hash);            \
    }

#define MOCK_FOHH_CONNECTION_DISCONNECT(T)                                                    \
    void fohh_connection_disconnect(struct fohh_connection *f_conn, const char *reason) {     \
        return T::get_mock()->fohh_connection_disconnect(f_conn, reason);                     \
    }

#define MOCK_ZPN_SITEC_GROUP_GET_BY_SITE_GID(T)                                     \
    int zpn_sitec_group_get_by_site_gid(int64_t site_gid,                           \
                                        struct zpn_site_controller_group **group,   \
                                        size_t *row_count,                          \
                                        wally_response_callback_f callback_f,       \
                                        void *callback_cookie,                      \
                                        int64_t callback_id) {                      \
        return T::get_mock()->zpn_sitec_group_get_by_site_gid(site_gid, group, row_count, callback_f, callback_cookie, callback_id);                                              \
    }

#define MOCK_ZPN_SITEC_TO_GROUP_GET_BY_GRPID(T)                                     \
    int zpn_sitec_to_group_get_by_grpid(int64_t site_gid,                           \
                                        struct zpn_site_controller_to_group **group,\
                                        size_t *row_count,                          \
                                        wally_response_callback_f callback_f,       \
                                        void *callback_cookie,                      \
                                        int64_t callback_id) {                      \
        return T::get_mock()->zpn_sitec_to_group_get_by_grpid(site_gid, group, row_count, callback_f, callback_cookie, callback_id);                                              \
    }

#define MOCK_ZPN_SITEC_GROUP_GET_BY_GID(T)                                     \
    int zpn_sitec_group_get_by_gid(int64_t site_gid,                           \
                                   struct zpn_site_controller_group **group,   \
                                   size_t *row_count,                          \
                                   int register_miss,                          \
                                   wally_response_callback_f callback_f,       \
                                   void *callback_cookie,                      \
                                   int64_t callback_id) {                      \
        return T::get_mock()->zpn_sitec_group_get_by_gid(site_gid, group, row_count, register_miss, callback_f, callback_cookie, callback_id);                             \
    }

#define MOCK_ZPATH_CUSTOMER_GET(T)                                     \
    int zpath_customer_get(int64_t customer_gid,                       \
                           struct zpath_customer **customer,           \
                           wally_response_callback_f callback_f,       \
                           void *callback_cookie,                      \
                           int64_t callback_id) {                      \
        return T::get_mock()->zpath_customer_get(customer_gid, customer, callback_f, callback_cookie, callback_id);                                                  \
    }

#define MOCK_ZPN_SITEC_TO_GROUP_GET_BY_SITEC_GID(T)                               \
    int zpn_sitec_to_group_get_by_sitec_gid(int64_t site_gid,                     \
                                   struct zpn_site_controller_to_group **group,   \
                                   size_t *row_count,                             \
                                   int register_miss,                             \
                                   wally_response_callback_f callback_f,          \
                                   void *callback_cookie,                         \
                                   int64_t callback_id) {                         \
        return T::get_mock()->zpn_sitec_to_group_get_by_sitec_gid(site_gid, group, row_count, register_miss, callback_f, callback_cookie, callback_id);                               \
    }

#define MOCK_ZPN_SITEC_GET_BY_ID(T)                                     \
    int zpn_sitec_get_by_id(int64_t sitec_id,                           \
                            struct zpn_site_controller **sitec,         \
                            wally_response_callback_f callback_f,       \
                            void *callback_cookie,                      \
                            int64_t callback_id) {                      \
        return T::get_mock()->zpn_sitec_get_by_id(sitec_id, sitec, callback_f, callback_cookie, callback_id);                                                   \
    }

#define MOCK_ZEVENT_EVENT_BASE(T)                                       \
    struct event_base* zevent_event_base(struct zevent_base *ev_base) {     \
            return T::get_mock()->zevent_event_base(ev_base);               \
    }

#define MOCK_ZPATH_ET_WALLY_GET_USERDB_WALLY_SERVER(T)                                       \
    struct wally_fohh_server* zpath_et_wally_userdb_get_userdb_wally_server(int64_t zone, int64_t userdb) {     \
            return T::get_mock()->zpath_et_wally_userdb_get_userdb_wally_server(zone, userdb);               \
    }

class SitecStatusConnMockBase : public testing::StrictMock<SitecStatusConnMock> {};

// Test fixture
class SitecStatusConnTest : public ZscalerMockBase<SitecStatusConnMockBase>
{
protected:
    struct fohh_connection f_conn;
    struct fohh_connection f_conn2;
    struct fohh_connection f_conn3;
    struct zpn_sitec_status_info info;
    struct zpn_site_controller_group *group[3];
    struct zpn_site_controller_to_group *sc_to_group[3];
    struct event_base *base;
public:
    void SetUp() override {
        ZscalerMockBase::SetUp();
        argo_library_init(1024);
        zpn_sitec_sitec_conn_init();
        base = event_base_new();

        sitec_gs.sitec_state = (struct zpn_sitec_state *)ZPN_CALLOC(sizeof(struct zpn_sitec_state));
        sitec_gs.sitec_state->userdb_wally_conn = &f_conn3;
        sitec_gs.sitec_state->cfg_wally = (wally*)ZPN_CALLOC(320000);
        sitec_gs.sitec_state->rcfg_wally = (wally*)ZPN_CALLOC(320000);
        sitec_gs.sitec_state->ovd_wally = (wally*)ZPN_CALLOC(320000);
        zpn_sitec_create_wally_fohh_servers();

        snprintf(f_conn.default_remote_address_name, sizeof(f_conn.default_remote_address_name), "%s", "sc2br.dev.zpath.net");
        snprintf(f_conn2.default_remote_address_name, sizeof(f_conn2.default_remote_address_name), "%s", "sc2br.dev.zpath.net");
        snprintf(f_conn3.default_remote_address_name, sizeof(f_conn3.default_remote_address_name), "%s", "sc2br.dev.zpath.net");

        f_conn.incarnation = 1;
        f_conn2.incarnation = 2;
        f_conn3.incarnation = 3;

        f_conn.state = f_conn2.state = f_conn3.state = fohh_connection_connected;

        sitec_gs.lat = 12.97;
        sitec_gs.lon = 77.59;
        snprintf(sitec_gs.cc, sizeof(sitec_gs.cc), "%s", "IN");
        sitec_gs.sitec_id = 289397352052032163Ull;

        zpn_sitec_store_status_conn_by_gid(289397352052032173Ull, (char*)(new string("abcd"))->c_str(), &f_conn);
        zpn_sitec_store_status_conn_by_gid(289397352052032166Ull, (char*)(new string("efab"))->c_str(), &f_conn2);
        zpn_sitec_store_status_conn_by_gid(289397352052032170Ull, (char*)(new string("1234"))->c_str(), &f_conn3);

        info.sitec_gid = 289397352052032173Ull;
        info.sc_lat = 19.07;
        info.sc_lon = 72.87;
        info.mem_util = 1;
        info.cpu_util = 1;
        info.proc_fd_util = 1;
        info.status = 1;
        info.sc_cc = (char *)(new string("IN"))->c_str();
        info.shard_total_max_seq = 29145379;
        info.shard_total_max_seq_hash = 0x435cb39c5bec9f7c;
        info.master_total_max_seq = 17273243;
        info.master_total_max_seq_hash = 0x5814c8daa5e36375;
        info.static_total_max_seq = 0;
        info.static_total_max_seq_hash = 0;
        info.userdb_total_max_seq = 3539259326;
        info.userdb_total_max_seq_hash = 0xba2323a8f06aece5;
        zpn_sitec_gid_update_status_info(289397352052032173Ull, &info);

        info.sitec_gid = 289397352052032166Ull;
        info.sc_lat = 28.70;
        info.sc_lon = 77.10;
        info.mem_util = 1;
        info.cpu_util = 1;
        info.proc_fd_util = 1;
        info.status = 1;
        info.sc_cc = (char *)(new string("IN"))->c_str();
        info.shard_total_max_seq = 29145379;
        info.shard_total_max_seq_hash = 0x435cb39c5bec9f7c;
        info.master_total_max_seq = 17273243;
        info.master_total_max_seq_hash = 0x5814c8daa5e36375;
        info.static_total_max_seq = 0;
        info.static_total_max_seq_hash = 0;
        info.userdb_total_max_seq = 3539259326;
        info.userdb_total_max_seq_hash = 0xba2323a8f06aece5;
        zpn_sitec_gid_update_status_info(289397352052032166Ull, &info);

        info.sitec_gid = 289397352052032170Ull;
        info.sc_lat = 28.70;
        info.sc_lon = 77.10;
        info.mem_util = 1;
        info.cpu_util = 1;
        info.proc_fd_util = 1;
        info.status = 1;
        info.sc_cc = (char *)(new string("IN"))->c_str();
        info.shard_total_max_seq = 29145379;
        info.shard_total_max_seq_hash = 0x435cb39c5bec9f7c;
        info.master_total_max_seq = 17273243;
        info.master_total_max_seq_hash = 0x5814c8daa5e36375;
        info.static_total_max_seq = 0;
        info.static_total_max_seq_hash = 0;
        info.userdb_total_max_seq = 3539259326;
        info.userdb_total_max_seq_hash = 0xba2323a8f06aece5;
        zpn_sitec_gid_update_status_info(289397352052032170Ull, &info);

        int64_t gid = 289397352052032159Ull;
        for (int i=0; i < 3; i++) {
            group[i] = new struct zpn_site_controller_group;
            group[i]->gid = gid++;
            group[i]->deleted = 0;
            group[i]->enabled = 1;
            group[i]->country_code = (char*)(new string("IN"))->c_str();
            group[i]->customer_gid = 289397352052031488Ull;
            group[i]->site_gid = 289397352052032162Ull;
            sc_to_group[i] = new struct zpn_site_controller_to_group;
            sc_to_group[i]->site_controller_group_gid = group[i]->gid;
        }
        sc_to_group[0]->site_controller_gid = 289397352052032163Ull;
        sc_to_group[1]->site_controller_gid = 289397352052032166Ull;
        sc_to_group[2]->site_controller_gid = 289397352052032173Ull;

    }
    void TearDown() override {
        ZscalerMockBase::TearDown();
    }
};

extern "C" {
    MOCK_SPLIT_BRAIN_CFG_SYNC_ENABLED(SitecStatusConnTest);
    MOCK_WALLY_GET_TOTAL_MAX_SEQ_WITH_HASH(SitecStatusConnTest);
    MOCK_FOHH_CONNECTION_DISCONNECT(SitecStatusConnTest);
    MOCK_ZPN_SITEC_GROUP_GET_BY_SITE_GID(SitecStatusConnTest);
    MOCK_ZPN_SITEC_TO_GROUP_GET_BY_GRPID(SitecStatusConnTest);
    MOCK_ZPN_SITEC_GROUP_GET_BY_GID(SitecStatusConnTest);
    MOCK_ZPATH_CUSTOMER_GET(SitecStatusConnTest);
    MOCK_ZPN_SITEC_GET_BY_ID(SitecStatusConnTest);
    MOCK_ZPN_SITEC_TO_GROUP_GET_BY_SITEC_GID(SitecStatusConnTest);
    MOCK_ZEVENT_EVENT_BASE(SitecStatusConnTest);
    MOCK_ZPATH_ET_WALLY_GET_USERDB_WALLY_SERVER(SitecStatusConnTest);
}

ACTION_P(SetSitecGroup, ptr, count) {
    for (int i = 0; i < count; i++) {
        arg0[i] = ptr[i];
    }
}
ACTION_P(SetSitecToGroup, ptr, index) {
    arg0[0] = ptr[index];
}

ACTION_P(SetCustomer, ptr) {
    arg0[0] = ptr;
}

ACTION_P(SetSitec, ptr) {
    arg0[0] = ptr;
}
TEST_F(SitecStatusConnTest, DisableCfgSync) {
    enum fohh_switch_connection switch_to = fohh_switch_connection_dont_care;
    sitec_gs.split_brain_sync_enabled = 1;
    snprintf(f_conn.remote_address_name, sizeof(f_conn.remote_address_name), "%s", "bcpsp-12345678.ddiltest-od.com");

    f_conn.best_sitec_time_counter = 59;
    EXPECT_CALL(*get_mock(), zpn_sitec_is_split_brain_cfg_sync_enabled).Times(1).WillOnce(testing::Return(0));
    zpn_sitec_fohh_state_cb(&f_conn, 1, &switch_to);

    EXPECT_EQ(switch_to, fohh_switch_connection_to_cloud);
}

TEST_F(SitecStatusConnTest, DisableCfgSyncSameRemoteName) {
    enum fohh_switch_connection switch_to = fohh_switch_connection_dont_care;
    snprintf(f_conn.remote_address_name, sizeof(f_conn.remote_address_name), "%s", "sc2br.dev.zpath.net");

    f_conn.best_sitec_time_counter = 59;
    EXPECT_CALL(*get_mock(), zpn_sitec_is_split_brain_cfg_sync_enabled).Times(1).WillOnce(testing::Return(0));
    zpn_sitec_fohh_state_cb(&f_conn, 1, &switch_to);

    EXPECT_EQ(switch_to, fohh_switch_connection_dont_care);
}
TEST_F(SitecStatusConnTest, ConnDisconnectedTooLongCfg) {
    enum fohh_switch_connection switch_to = fohh_switch_connection_dont_care;
    sitec_gs.split_brain_sync_enabled = 0;

    snprintf(f_conn.sni_name, sizeof(f_conn.sni_name), "%s", "123456789.sccfg.dev.zpath.net");
    snprintf(f_conn.remote_address_name, sizeof(f_conn.remote_address_name), "%s", "sc2br.dev.zpath.net");

    f_conn.best_sitec_time_counter = 59;
    EXPECT_CALL(*get_mock(), zpn_sitec_is_split_brain_cfg_sync_enabled).Times(1).WillOnce(testing::Return(1));
    EXPECT_CALL(*get_mock(), wally_get_total_max_seq_with_hash).Times(1).WillOnce(testing::DoAll(testing::SetArgPointee<1>(0x435cb39c5bec9f6c),testing::Return(29145370)));

    zpn_sitec_fohh_state_cb(&f_conn, 1, &switch_to);

    EXPECT_EQ(switch_to, fohh_switch_connection_dont_care);
}

TEST_F(SitecStatusConnTest, ConnDisconnectedTooLongOvd) {
    enum fohh_switch_connection switch_to = fohh_switch_connection_dont_care;
    sitec_gs.split_brain_sync_enabled = 0;

    snprintf(f_conn.sni_name, sizeof(f_conn.sni_name), "%s", "123456789.scovd.dev.zpath.net");
    snprintf(f_conn.remote_address_name, sizeof(f_conn.remote_address_name), "%s", "sc2br.dev.zpath.net");

    f_conn.best_sitec_time_counter = 59;
    EXPECT_CALL(*get_mock(), zpn_sitec_is_split_brain_cfg_sync_enabled).Times(1).WillOnce(testing::Return(1));
    EXPECT_CALL(*get_mock(), wally_get_total_max_seq_with_hash).Times(1).WillOnce(testing::DoAll(testing::SetArgPointee<1>(0x435cb39c5bec9f6c),testing::Return(29145370)));

    zpn_sitec_fohh_state_cb(&f_conn, 1, &switch_to);

    EXPECT_EQ(switch_to, fohh_switch_connection_dont_care);
}

TEST_F(SitecStatusConnTest, ConnDisconnectedTooLongRcfg) {
    enum fohh_switch_connection switch_to = fohh_switch_connection_dont_care;
    sitec_gs.split_brain_sync_enabled = 0;

    snprintf(f_conn.sni_name, sizeof(f_conn.sni_name), "%s", "123456789.scrcfg.dev.zpath.net");
    snprintf(f_conn.remote_address_name, sizeof(f_conn.remote_address_name), "%s", "sc2br.dev.zpath.net");

    f_conn.best_sitec_time_counter = 59;
    EXPECT_CALL(*get_mock(), zpn_sitec_is_split_brain_cfg_sync_enabled).Times(1).WillOnce(testing::Return(1));
    EXPECT_CALL(*get_mock(), wally_get_total_max_seq_with_hash).Times(1).WillOnce(testing::DoAll(testing::SetArgPointee<1>(0x435cb39c5bec9f6c),testing::Return(29145370)));

    zpn_sitec_fohh_state_cb(&f_conn, 1, &switch_to);

    EXPECT_EQ(switch_to, fohh_switch_connection_dont_care);
}

TEST_F(SitecStatusConnTest, ConnDisconnectedTooLongUserdb) {
    enum fohh_switch_connection switch_to = fohh_switch_connection_dont_care;
    sitec_gs.split_brain_sync_enabled = 0;

    snprintf(f_conn.sni_name, sizeof(f_conn.sni_name), "%s", "123456789.scuserdb.dev.zpath.net");
    snprintf(f_conn.remote_address_name, sizeof(f_conn.remote_address_name), "%s", "sc2br.dev.zpath.net");

    f_conn.best_sitec_time_counter = 59;
    EXPECT_CALL(*get_mock(), zpn_sitec_is_split_brain_cfg_sync_enabled).Times(1).WillOnce(testing::Return(1));
    EXPECT_CALL(*get_mock(), wally_get_total_max_seq_with_hash).Times(1).WillOnce(testing::DoAll(testing::SetArgPointee<1>(0x435cb39c5bec9f6c),testing::Return(29145370)));

    zpn_sitec_fohh_state_cb(&f_conn, 1, &switch_to);

    EXPECT_EQ(switch_to, fohh_switch_connection_dont_care);
}

TEST_F(SitecStatusConnTest, ConnConnectedForceDisconnect) {
    enum fohh_switch_connection switch_to = fohh_switch_connection_dont_care;
    sitec_gs.split_brain_sync_enabled = 0;
    f_conn.site_conn_ep_type = fohh_connection_on_sitec;
    f_conn.peer_id = 289397352052032173Ull;
    sitec_gs.sitec_state->ctrl_fohh_conn = &f_conn2;
    f_conn2.state = fohh_connection_disconnected;

    snprintf(f_conn.sni_name, sizeof(f_conn.sni_name), "%s", "123456789.sccfg.dev.zpath.net");
    snprintf(f_conn.remote_address_name, sizeof(f_conn.remote_address_name), "%s", "sc2br.dev.zpath.net");

    EXPECT_CALL(*get_mock(), zpn_sitec_is_split_brain_cfg_sync_enabled).Times(1).WillOnce(testing::Return(1));
    EXPECT_CALL(*get_mock(), wally_get_total_max_seq_with_hash).Times(1).WillOnce(testing::DoAll(testing::SetArgPointee<1>(0x435cb39c5bec9f6c),testing::Return(29145370)));
    EXPECT_CALL(*get_mock(), fohh_connection_disconnect).Times(1).WillOnce(testing::Return());

    info.sitec_gid = 289397352052032173Ull;
    info.sc_lat = 19.07;
    info.sc_lon = 72.87;
    info.mem_util = 1;
    info.cpu_util = 1;
    info.proc_fd_util = 1;
    info.status = 0;
    info.sc_cc = (char *)(new string("IN"))->c_str();
    info.shard_total_max_seq = 29145379;
    info.shard_total_max_seq_hash = 0x435cb39c5bec9f7c;
    info.master_total_max_seq = 17273243;
    info.master_total_max_seq_hash = 0x5814c8daa5e36375;
    info.static_total_max_seq = 0;
    info.static_total_max_seq_hash = 0;
    info.userdb_total_max_seq = 3539259326;
    info.userdb_total_max_seq_hash = 0xba2323a8f06aece5;
    zpn_sitec_gid_update_status_info(289397352052032173Ull, &info);
    f_conn.best_sitec_time_counter = 59;

    zpn_sitec_fohh_state_cb(&f_conn, 0, &switch_to);

    EXPECT_EQ(switch_to, fohh_switch_connection_to_sitec);
}

TEST_F(SitecStatusConnTest, ConnConnectedNoSwitch) {
    enum fohh_switch_connection switch_to = fohh_switch_connection_dont_care;
    sitec_gs.split_brain_sync_enabled = 0;
    f_conn.site_conn_ep_type = fohh_connection_on_sitec;
    f_conn.peer_id = 289397352052032173Ull;
    sitec_gs.sitec_state->ctrl_fohh_conn = &f_conn2;
    f_conn2.state = fohh_connection_disconnected;

    snprintf(f_conn.sni_name, sizeof(f_conn.sni_name), "%s", "123456789.sccfg.dev.zpath.net");
    snprintf(f_conn.remote_address_name, sizeof(f_conn.remote_address_name), "%s", "sc2br.dev.zpath.net");

    EXPECT_CALL(*get_mock(), zpn_sitec_is_split_brain_cfg_sync_enabled).Times(1).WillOnce(testing::Return(1));
    EXPECT_CALL(*get_mock(), wally_get_total_max_seq_with_hash).Times(1).WillOnce(testing::DoAll(testing::SetArgPointee<1>(0x435cb39c5bec9f6c),testing::Return(29145370)));
    f_conn.best_sitec_time_counter = 59;

    zpn_sitec_fohh_state_cb(&f_conn, 0, &switch_to);

    EXPECT_EQ(switch_to, fohh_switch_connection_dont_care);
}

TEST_F(SitecStatusConnTest, BestPCCStatusUpDistance) {
    struct zpn_sitec_status_state state;

    state.status_obj = (struct zpn_sitec_status_obj *)SITEC_CALLOC(3 * sizeof(struct zpn_sitec_status_obj));
    state.count = 3;
    int64_t sitec_gid;

    zpn_sitec_get_status_obj(289397352052032173Ull, &state.status_obj[0]);
    zpn_sitec_get_status_obj(289397352052032166Ull, &state.status_obj[1]);
    zpn_sitec_get_status_obj(289397352052032170Ull, &state.status_obj[2]);

    sitec_gid = zpn_sitec_get_best_sitec(&state, 289397352052032163Ull, 0x435cb39c5bec9f7c, sitec_wally_conn_type_cfg, 0);

    EXPECT_EQ(sitec_gid, 289397352052032173Ull);
}

TEST_F(SitecStatusConnTest, BestPCCStatusUpEffectiveLoad) {
    struct zpn_sitec_status_state state;

    info.sitec_gid = 289397352052032173Ull;
    info.sc_lat = 19.07;
    info.sc_lon = 72.87;
    info.mem_util = 1;
    info.cpu_util = 1;
    info.proc_fd_util = 1;
    info.status = 0;
    info.sc_cc = (char *)(new string("IN"))->c_str();
    info.shard_total_max_seq = 29145379;
    info.shard_total_max_seq_hash = 0x435cb39c5bec9f7c;
    info.master_total_max_seq = 17273243;
    info.master_total_max_seq_hash = 0x5814c8daa5e36375;
    info.static_total_max_seq = 0;
    info.static_total_max_seq_hash = 0;
    info.userdb_total_max_seq = 3539259326;
    info.userdb_total_max_seq_hash = 0xba2323a8f06aece5;
    zpn_sitec_gid_update_status_info(289397352052032173Ull, &info);

    info.sitec_gid = 289397352052032166Ull;
    info.sc_lat = 28.70;
    info.sc_lon = 77.10;
    info.mem_util = 10;
    info.cpu_util = 30;
    info.proc_fd_util = 1;
    info.status = 1;
    info.sc_cc = (char *)(new string("IN"))->c_str();
    info.shard_total_max_seq = 29145379;
    info.shard_total_max_seq_hash = 0x435cb39c5bec9f7c;
    info.master_total_max_seq = 17273243;
    info.master_total_max_seq_hash = 0x5814c8daa5e36375;
    info.static_total_max_seq = 0;
    info.static_total_max_seq_hash = 0;
    info.userdb_total_max_seq = 3539259326;
    info.userdb_total_max_seq_hash = 0xba2323a8f06aece5;
    zpn_sitec_gid_update_status_info(289397352052032166Ull, &info);

    state.status_obj = (struct zpn_sitec_status_obj *)SITEC_CALLOC(3 * sizeof(struct zpn_sitec_status_obj));
    state.count = 3;
    int64_t sitec_gid;

    zpn_sitec_get_status_obj(289397352052032173Ull, &state.status_obj[0]);
    zpn_sitec_get_status_obj(289397352052032166Ull, &state.status_obj[1]);
    zpn_sitec_get_status_obj(289397352052032170Ull, &state.status_obj[2]);

    sitec_gid = zpn_sitec_get_best_sitec(&state, 29145379, 0x435cb39c5bec9f7c, sitec_wally_conn_type_cfg, 0);

    EXPECT_EQ(sitec_gid, 289397352052032170Ull);
}

TEST_F(SitecStatusConnTest, BestPCCStatusDownMaxSeq) {
    struct zpn_sitec_status_state state;

    info.sitec_gid = 289397352052032173Ull;
    info.sc_lat = 19.07;
    info.sc_lon = 72.87;
    info.mem_util = 1;
    info.cpu_util = 1;
    info.proc_fd_util = 1;
    info.status = 0;
    info.sc_cc = (char *)(new string("IN"))->c_str();
    info.shard_total_max_seq = 29145379;
    info.shard_total_max_seq_hash = 0x435cb39c5bec9f7c;
    info.master_total_max_seq = 17273243;
    info.master_total_max_seq_hash = 0x5814c8daa5e36375;
    info.static_total_max_seq = 0;
    info.static_total_max_seq_hash = 0;
    info.userdb_total_max_seq = 3539259326;
    info.userdb_total_max_seq_hash = 0xba2323a8f06aece5;
    zpn_sitec_gid_update_status_info(289397352052032173Ull, &info);

    info.sitec_gid = 289397352052032166Ull;
    info.sc_lat = 28.70;
    info.sc_lon = 77.10;
    info.mem_util = 10;
    info.cpu_util = 30;
    info.proc_fd_util = 1;
    info.status = 0;
    info.sc_cc = (char *)(new string("IN"))->c_str();
    info.shard_total_max_seq = 29145390;
    info.shard_total_max_seq_hash = 0x435cb39c5bec9f8c;
    info.master_total_max_seq = 17273243;
    info.master_total_max_seq_hash = 0x5814c8daa5e36375;
    info.static_total_max_seq = 0;
    info.static_total_max_seq_hash = 0;
    info.userdb_total_max_seq = 3539259326;
    info.userdb_total_max_seq_hash = 0xba2323a8f06aece5;
    zpn_sitec_gid_update_status_info(289397352052032166Ull, &info);

    info.sitec_gid = 289397352052032170Ull;
    info.sc_lat = 28.70;
    info.sc_lon = 77.10;
    info.mem_util = 1;
    info.cpu_util = 1;
    info.proc_fd_util = 1;
    info.status = 0;
    info.sc_cc = (char *)(new string("IN"))->c_str();
    info.shard_total_max_seq = 29145370;
    info.shard_total_max_seq_hash = 0x435cb39c5bec9e7c;
    info.master_total_max_seq = 17273243;
    info.master_total_max_seq_hash = 0x5814c8daa5e36375;
    info.static_total_max_seq = 0;
    info.static_total_max_seq_hash = 0;
    info.userdb_total_max_seq = 3539259326;
    info.userdb_total_max_seq_hash = 0xba2323a8f06aece5;
    zpn_sitec_gid_update_status_info(289397352052032170Ull, &info);

    state.status_obj = (struct zpn_sitec_status_obj *)SITEC_CALLOC(3 * sizeof(struct zpn_sitec_status_obj));
    state.count = 3;
    int64_t sitec_gid;

    zpn_sitec_get_status_obj(289397352052032173Ull, &state.status_obj[0]);
    zpn_sitec_get_status_obj(289397352052032166Ull, &state.status_obj[1]);
    zpn_sitec_get_status_obj(289397352052032170Ull, &state.status_obj[2]);

    sitec_gid = zpn_sitec_get_best_sitec(&state, 29145379, 0x435cb39c5bec9f7c, sitec_wally_conn_type_cfg, 0);

    EXPECT_EQ(sitec_gid, 289397352052032166Ull);
}

TEST_F(SitecStatusConnTest, BestPCCStatusDownMaxSeqBasedDistance) {
    struct zpn_sitec_status_state state;

    info.sitec_gid = 289397352052032166Ull;
    info.sc_lat = 28.70;
    info.sc_lon = 77.10;
    info.mem_util = 1;
    info.cpu_util = 1;
    info.proc_fd_util = 1;
    info.status = 0;
    info.sc_cc = (char *)(new string("IN"))->c_str();
    info.shard_total_max_seq = 29145390;
    info.shard_total_max_seq_hash = 0x435cb39c5bec9f8c;
    info.master_total_max_seq = 17273243;
    info.master_total_max_seq_hash = 0x5814c8daa5e36375;
    info.static_total_max_seq = 0;
    info.static_total_max_seq_hash = 0;
    info.userdb_total_max_seq = 3539259326;
    info.userdb_total_max_seq_hash = 0xba2323a8f06aece5;
    zpn_sitec_gid_update_status_info(289397352052032166Ull, &info);

    info.sitec_gid = 289397352052032170Ull;
    info.sc_lat = 28.70;
    info.sc_lon = 77.10;
    info.mem_util = 1;
    info.cpu_util = 1;
    info.proc_fd_util = 1;
    info.status = 0;
    info.sc_cc = (char *)(new string("IN"))->c_str();
    info.shard_total_max_seq = 29145370;
    info.shard_total_max_seq_hash = 0x435cb39c5bec9e7c;
    info.master_total_max_seq = 17273243;
    info.master_total_max_seq_hash = 0x5814c8daa5e36375;
    info.static_total_max_seq = 0;
    info.static_total_max_seq_hash = 0;
    info.userdb_total_max_seq = 3539259326;
    info.userdb_total_max_seq_hash = 0xba2323a8f06aece5;
    zpn_sitec_gid_update_status_info(289397352052032170Ull, &info);

    info.sitec_gid = 289397352052032173Ull;
    info.sc_lat = 19.07;
    info.sc_lon = 72.87;
    info.mem_util = 1;
    info.cpu_util = 1;
    info.proc_fd_util = 1;
    info.status = 0;
    info.sc_cc = (char *)(new string("IN"))->c_str();
    info.shard_total_max_seq = 29145390;
    info.shard_total_max_seq_hash = 0x435cb39c5bec9f8c;
    info.master_total_max_seq = 17273243;
    info.master_total_max_seq_hash = 0x5814c8daa5e36375;
    info.static_total_max_seq = 0;
    info.static_total_max_seq_hash = 0;
    info.userdb_total_max_seq = 3539259326;
    info.userdb_total_max_seq_hash = 0xba2323a8f06aece5;
    zpn_sitec_gid_update_status_info(289397352052032173Ull, &info);

    state.status_obj = (struct zpn_sitec_status_obj *)SITEC_CALLOC(3 * sizeof(struct zpn_sitec_status_obj));
    state.count = 3;
    int64_t sitec_gid;

    zpn_sitec_get_status_obj(289397352052032166Ull, &state.status_obj[0]);
    zpn_sitec_get_status_obj(289397352052032173Ull, &state.status_obj[1]);
    zpn_sitec_get_status_obj(289397352052032170Ull, &state.status_obj[2]);

    sitec_gid = zpn_sitec_get_best_sitec(&state, 29145379, 0x435cb39c5bec9f7c, sitec_wally_conn_type_cfg, 0);

    EXPECT_EQ(sitec_gid, 289397352052032173Ull);
}

TEST_F(SitecStatusConnTest, BestPCCStatusDownMaxSeqBasedEffectiveLoad) {
    struct zpn_sitec_status_state state;

    info.sitec_gid = 289397352052032166Ull;
    info.sc_lat = 28.70;
    info.sc_lon = 77.10;
    info.mem_util = 1;
    info.cpu_util = 30;
    info.proc_fd_util = 1;
    info.status = 0;
    info.sc_cc = (char *)(new string("IN"))->c_str();
    info.shard_total_max_seq = 29145390;
    info.shard_total_max_seq_hash = 0x435cb39c5bec9f8c;
    info.master_total_max_seq = 17273243;
    info.master_total_max_seq_hash = 0x5814c8daa5e36375;
    info.static_total_max_seq = 0;
    info.static_total_max_seq_hash = 0;
    info.userdb_total_max_seq = 3539259326;
    info.userdb_total_max_seq_hash = 0xba2323a8f06aece5;
    zpn_sitec_gid_update_status_info(289397352052032166Ull, &info);

    info.sitec_gid = 289397352052032170Ull;
    info.sc_lat = 28.70;
    info.sc_lon = 77.10;
    info.mem_util = 1;
    info.cpu_util = 1;
    info.proc_fd_util = 1;
    info.status = 0;
    info.sc_cc = (char *)(new string("IN"))->c_str();
    info.shard_total_max_seq = 29145390;
    info.shard_total_max_seq_hash = 0x435cb39c5bec9f8c;
    info.master_total_max_seq = 17273243;
    info.master_total_max_seq_hash = 0x5814c8daa5e36375;
    info.static_total_max_seq = 0;
    info.static_total_max_seq_hash = 0;
    info.userdb_total_max_seq = 3539259326;
    info.userdb_total_max_seq_hash = 0xba2323a8f06aece5;
    zpn_sitec_gid_update_status_info(289397352052032170Ull, &info);

    info.sitec_gid = 289397352052032173Ull;
    info.sc_lat = 19.07;
    info.sc_lon = 72.87;
    info.mem_util = 1;
    info.cpu_util = 1;
    info.proc_fd_util = 1;
    info.status = 0;
    info.sc_cc = (char *)(new string("IN"))->c_str();
    info.shard_total_max_seq = 29145379;
    info.shard_total_max_seq_hash = 0x435cb39c5bec9f7c;
    info.master_total_max_seq = 17273243;
    info.master_total_max_seq_hash = 0x5814c8daa5e36375;
    info.static_total_max_seq = 0;
    info.static_total_max_seq_hash = 0;
    info.userdb_total_max_seq = 3539259326;
    info.userdb_total_max_seq_hash = 0xba2323a8f06aece5;
    zpn_sitec_gid_update_status_info(289397352052032173Ull, &info);

    state.status_obj = (struct zpn_sitec_status_obj *)SITEC_CALLOC(3 * sizeof(struct zpn_sitec_status_obj));
    state.count = 3;
    int64_t sitec_gid;

    zpn_sitec_get_status_obj(289397352052032166Ull, &state.status_obj[0]);
    zpn_sitec_get_status_obj(289397352052032173Ull, &state.status_obj[1]);
    zpn_sitec_get_status_obj(289397352052032170Ull, &state.status_obj[2]);

    sitec_gid = zpn_sitec_get_best_sitec(&state, 29145379, 0x435cb39c5bec9f7c, sitec_wally_conn_type_cfg, 0);

    EXPECT_EQ(sitec_gid, 289397352052032170Ull);
}

TEST_F(SitecStatusConnTest, BestPCCStatusDownMaxSeqEqual) {
    struct zpn_sitec_status_state state;

    info.sitec_gid = 289397352052032166Ull;
    info.sc_lat = 28.70;
    info.sc_lon = 77.10;
    info.mem_util = 1;
    info.cpu_util = 30;
    info.proc_fd_util = 1;
    info.status = 0;
    info.sc_cc = (char *)(new string("IN"))->c_str();
    info.shard_total_max_seq = 29145390;
    info.shard_total_max_seq_hash = 0x435cb39c5bec9f8c;
    info.master_total_max_seq = 17273243;
    info.master_total_max_seq_hash = 0x5814c8daa5e36375;
    info.static_total_max_seq = 0;
    info.static_total_max_seq_hash = 0;
    info.userdb_total_max_seq = 3539259326;
    info.userdb_total_max_seq_hash = 0xba2323a8f06aece5;
    zpn_sitec_gid_update_status_info(289397352052032166Ull, &info);

    info.sitec_gid = 289397352052032170Ull;
    info.sc_lat = 28.70;
    info.sc_lon = 77.10;
    info.mem_util = 1;
    info.cpu_util = 1;
    info.proc_fd_util = 1;
    info.status = 0;
    info.sc_cc = (char *)(new string("IN"))->c_str();
    info.shard_total_max_seq = 29145390;
    info.shard_total_max_seq_hash = 0x435cb39c5bec9f8c;
    info.master_total_max_seq = 17273243;
    info.master_total_max_seq_hash = 0x5814c8daa5e36375;
    info.static_total_max_seq = 0;
    info.static_total_max_seq_hash = 0;
    info.userdb_total_max_seq = 3539259326;
    info.userdb_total_max_seq_hash = 0xba2323a8f06aece5;
    zpn_sitec_gid_update_status_info(289397352052032170Ull, &info);

    info.sitec_gid = 289397352052032173Ull;
    info.sc_lat = 19.07;
    info.sc_lon = 72.87;
    info.mem_util = 1;
    info.cpu_util = 1;
    info.proc_fd_util = 1;
    info.status = 0;
    info.sc_cc = (char *)(new string("IN"))->c_str();
    info.shard_total_max_seq = 29145390;
    info.shard_total_max_seq_hash = 0x435cb39c5bec9f8c;
    info.master_total_max_seq = 17273243;
    info.master_total_max_seq_hash = 0x5814c8daa5e36375;
    info.static_total_max_seq = 0;
    info.static_total_max_seq_hash = 0;
    info.userdb_total_max_seq = 3539259326;
    info.userdb_total_max_seq_hash = 0xba2323a8f06aece5;
    zpn_sitec_gid_update_status_info(289397352052032173Ull, &info);

    state.status_obj = (struct zpn_sitec_status_obj *)SITEC_CALLOC(3 * sizeof(struct zpn_sitec_status_obj));
    state.count = 3;
    int64_t sitec_gid;

    zpn_sitec_get_status_obj(289397352052032166Ull, &state.status_obj[0]);
    zpn_sitec_get_status_obj(289397352052032173Ull, &state.status_obj[1]);
    zpn_sitec_get_status_obj(289397352052032170Ull, &state.status_obj[2]);

    sitec_gid = zpn_sitec_get_best_sitec(&state, 29145390, 0x435cb39c5bec9f8c, sitec_wally_conn_type_cfg, 0);

    EXPECT_EQ(sitec_gid, 0);
}

TEST_F(SitecStatusConnTest, BestPCCStatusDownMaxSeqEqualHashDifferent) {
    struct zpn_sitec_status_state state;

    info.sitec_gid = 289397352052032166Ull;
    info.sc_lat = 28.70;
    info.sc_lon = 77.10;
    info.mem_util = 1;
    info.cpu_util = 30;
    info.proc_fd_util = 1;
    info.status = 0;
    info.sc_cc = (char *)(new string("IN"))->c_str();
    info.shard_total_max_seq = 29145390;
    info.shard_total_max_seq_hash = 0x435cb39c5bec9f8c;
    info.master_total_max_seq = 17273243;
    info.master_total_max_seq_hash = 0x5814c8daa5e36375;
    info.static_total_max_seq = 0;
    info.static_total_max_seq_hash = 0;
    info.userdb_total_max_seq = 3539259326;
    info.userdb_total_max_seq_hash = 0xba2323a8f06aece5;
    zpn_sitec_gid_update_status_info(289397352052032166Ull, &info);

    info.sitec_gid = 289397352052032170Ull;
    info.sc_lat = 28.70;
    info.sc_lon = 77.10;
    info.mem_util = 1;
    info.cpu_util = 1;
    info.proc_fd_util = 1;
    info.status = 0;
    info.sc_cc = (char *)(new string("IN"))->c_str();
    info.shard_total_max_seq = 29145390;
    info.shard_total_max_seq_hash = 0x435cb39c5bec9f7c;
    info.master_total_max_seq = 17273243;
    info.master_total_max_seq_hash = 0x5814c8daa5e36375;
    info.static_total_max_seq = 0;
    info.static_total_max_seq_hash = 0;
    info.userdb_total_max_seq = 3539259326;
    info.userdb_total_max_seq_hash = 0xba2323a8f06aece5;
    zpn_sitec_gid_update_status_info(289397352052032170Ull, &info);

    info.sitec_gid = 289397352052032173Ull;
    info.sc_lat = 19.07;
    info.sc_lon = 72.87;
    info.mem_util = 1;
    info.cpu_util = 1;
    info.proc_fd_util = 1;
    info.status = 0;
    info.sc_cc = (char *)(new string("IN"))->c_str();
    info.shard_total_max_seq = 29145390;
    info.shard_total_max_seq_hash = 0x435cb39c5bec9f8c;
    info.master_total_max_seq = 17273243;
    info.master_total_max_seq_hash = 0x5814c8daa5e36375;
    info.static_total_max_seq = 0;
    info.static_total_max_seq_hash = 0;
    info.userdb_total_max_seq = 3539259326;
    info.userdb_total_max_seq_hash = 0xba2323a8f06aece5;
    zpn_sitec_gid_update_status_info(289397352052032173Ull, &info);

    state.status_obj = (struct zpn_sitec_status_obj *)SITEC_CALLOC(3 * sizeof(struct zpn_sitec_status_obj));
    state.count = 3;
    int64_t sitec_gid;

    zpn_sitec_get_status_obj(289397352052032166Ull, &state.status_obj[0]);
    zpn_sitec_get_status_obj(289397352052032173Ull, &state.status_obj[1]);
    zpn_sitec_get_status_obj(289397352052032170Ull, &state.status_obj[2]);

    sitec_gid = zpn_sitec_get_best_sitec(&state, 29145390, 0x435cb39c5bec9f8c, sitec_wally_conn_type_cfg, 0);

    EXPECT_EQ(sitec_gid, 289397352052032170Ull);
}

TEST_F(SitecStatusConnTest, MyPCCBestStatusMaster) {
    struct zpn_sitec_status_state state;

    info.sitec_gid = 289397352052032166Ull;
    info.sc_lat = 28.70;
    info.sc_lon = 77.10;
    info.mem_util = 1;
    info.cpu_util = 30;
    info.proc_fd_util = 1;
    info.status = 0;
    info.sc_cc = (char *)(new string("IN"))->c_str();
    info.shard_total_max_seq = 29145390;
    info.shard_total_max_seq_hash = 0x435cb39c5bec9f8c;
    info.master_total_max_seq = 17273250;
    info.master_total_max_seq_hash = 0x5814c8daa5e36895;
    info.static_total_max_seq = 0;
    info.static_total_max_seq_hash = 0;
    info.userdb_total_max_seq = 3539259326;
    info.userdb_total_max_seq_hash = 0xba2323a8f06aece5;
    zpn_sitec_gid_update_status_info(289397352052032166Ull, &info);

    info.sitec_gid = 289397352052032170Ull;
    info.sc_lat = 28.70;
    info.sc_lon = 77.10;
    info.mem_util = 1;
    info.cpu_util = 1;
    info.proc_fd_util = 1;
    info.status = 0;
    info.sc_cc = (char *)(new string("IN"))->c_str();
    info.shard_total_max_seq = 29145390;
    info.shard_total_max_seq_hash = 0x435cb39c5bec9f7c;
    info.master_total_max_seq = 17273243;
    info.master_total_max_seq_hash = 0x5814c8daa5e36375;
    info.static_total_max_seq = 0;
    info.static_total_max_seq_hash = 0;
    info.userdb_total_max_seq = 3539259326;
    info.userdb_total_max_seq_hash = 0xba2323a8f06aece5;
    zpn_sitec_gid_update_status_info(289397352052032170Ull, &info);

    info.sitec_gid = 289397352052032173Ull;
    info.sc_lat = 19.07;
    info.sc_lon = 72.87;
    info.mem_util = 1;
    info.cpu_util = 1;
    info.proc_fd_util = 1;
    info.status = 0;
    info.sc_cc = (char *)(new string("IN"))->c_str();
    info.shard_total_max_seq = 29145390;
    info.shard_total_max_seq_hash = 0x435cb39c5bec9f8c;
    info.master_total_max_seq = 17273243;
    info.master_total_max_seq_hash = 0x5814c8daa5e36375;
    info.static_total_max_seq = 0;
    info.static_total_max_seq_hash = 0;
    info.userdb_total_max_seq = 3539259326;
    info.userdb_total_max_seq_hash = 0xba2323a8f06aece5;
    zpn_sitec_gid_update_status_info(289397352052032173Ull, &info);

    state.status_obj = (struct zpn_sitec_status_obj *)SITEC_CALLOC(3 * sizeof(struct zpn_sitec_status_obj));
    state.count = 3;
    int64_t sitec_gid;

    zpn_sitec_get_status_obj(289397352052032166Ull, &state.status_obj[0]);
    zpn_sitec_get_status_obj(289397352052032173Ull, &state.status_obj[1]);
    zpn_sitec_get_status_obj(289397352052032170Ull, &state.status_obj[2]);

    sitec_gid = zpn_sitec_get_best_sitec(&state, 29145390, 0x435cb39c5bec9f8c, sitec_wally_conn_type_ovd, 0);

    EXPECT_EQ(sitec_gid, 0);
}

TEST_F(SitecStatusConnTest, BestPCCStatusMaster) {
    struct zpn_sitec_status_state state;

    info.sitec_gid = 289397352052032166Ull;
    info.sc_lat = 28.70;
    info.sc_lon = 77.10;
    info.mem_util = 1;
    info.cpu_util = 30;
    info.proc_fd_util = 1;
    info.status = 0;
    info.sc_cc = (char *)(new string("IN"))->c_str();
    info.shard_total_max_seq = 29145390;
    info.shard_total_max_seq_hash = 0x435cb39c5bec9f8c;
    info.master_total_max_seq = 17273250;
    info.master_total_max_seq_hash = 0x5814c8daa5e36895;
    info.static_total_max_seq = 0;
    info.static_total_max_seq_hash = 0;
    info.userdb_total_max_seq = 3539259326;
    info.userdb_total_max_seq_hash = 0xba2323a8f06aece5;
    zpn_sitec_gid_update_status_info(289397352052032166Ull, &info);

    info.sitec_gid = 289397352052032170Ull;
    info.sc_lat = 28.70;
    info.sc_lon = 77.10;
    info.mem_util = 1;
    info.cpu_util = 1;
    info.proc_fd_util = 1;
    info.status = 0;
    info.sc_cc = (char *)(new string("IN"))->c_str();
    info.shard_total_max_seq = 29145390;
    info.shard_total_max_seq_hash = 0x435cb39c5bec9f7c;
    info.master_total_max_seq = 17273243;
    info.master_total_max_seq_hash = 0x5814c8daa5e36375;
    info.static_total_max_seq = 0;
    info.static_total_max_seq_hash = 0;
    info.userdb_total_max_seq = 3539259326;
    info.userdb_total_max_seq_hash = 0xba2323a8f06aece5;
    zpn_sitec_gid_update_status_info(289397352052032170Ull, &info);

    info.sitec_gid = 289397352052032173Ull;
    info.sc_lat = 19.07;
    info.sc_lon = 72.87;
    info.mem_util = 1;
    info.cpu_util = 1;
    info.proc_fd_util = 1;
    info.status = 0;
    info.sc_cc = (char *)(new string("IN"))->c_str();
    info.shard_total_max_seq = 29145390;
    info.shard_total_max_seq_hash = 0x435cb39c5bec9f8c;
    info.master_total_max_seq = 17273243;
    info.master_total_max_seq_hash = 0x5814c8daa5e36375;
    info.static_total_max_seq = 0;
    info.static_total_max_seq_hash = 0;
    info.userdb_total_max_seq = 3539259326;
    info.userdb_total_max_seq_hash = 0xba2323a8f06aece5;
    zpn_sitec_gid_update_status_info(289397352052032173Ull, &info);

    state.status_obj = (struct zpn_sitec_status_obj *)SITEC_CALLOC(3 * sizeof(struct zpn_sitec_status_obj));
    state.count = 3;
    int64_t sitec_gid;

    zpn_sitec_get_status_obj(289397352052032166Ull, &state.status_obj[0]);
    zpn_sitec_get_status_obj(289397352052032173Ull, &state.status_obj[1]);
    zpn_sitec_get_status_obj(289397352052032170Ull, &state.status_obj[2]);

    sitec_gid = zpn_sitec_get_best_sitec(&state, 17273244, 0x435cb39c5bec9f8c, sitec_wally_conn_type_ovd, 0);

    EXPECT_EQ(sitec_gid, 289397352052032166Ull);
}

TEST_F(SitecStatusConnTest, BestPCCStatusUserdb) {
    struct zpn_sitec_status_state state;

    info.sitec_gid = 289397352052032166Ull;
    info.sc_lat = 28.70;
    info.sc_lon = 77.10;
    info.mem_util = 1;
    info.cpu_util = 30;
    info.proc_fd_util = 1;
    info.status = 0;
    info.sc_cc = (char *)(new string("IN"))->c_str();
    info.shard_total_max_seq = 29145390;
    info.shard_total_max_seq_hash = 0x435cb39c5bec9f8c;
    info.master_total_max_seq = 17273250;
    info.master_total_max_seq_hash = 0x5814c8daa5e36895;
    info.static_total_max_seq = 0;
    info.static_total_max_seq_hash = 0;
    info.userdb_total_max_seq = 3539259326;
    info.userdb_total_max_seq_hash = 0xba2323a8f06aece5;
    zpn_sitec_gid_update_status_info(289397352052032166Ull, &info);

    info.sitec_gid = 289397352052032170Ull;
    info.sc_lat = 28.70;
    info.sc_lon = 77.10;
    info.mem_util = 1;
    info.cpu_util = 1;
    info.proc_fd_util = 1;
    info.status = 0;
    info.sc_cc = (char *)(new string("IN"))->c_str();
    info.shard_total_max_seq = 29145390;
    info.shard_total_max_seq_hash = 0x435cb39c5bec9f7c;
    info.master_total_max_seq = 17273243;
    info.master_total_max_seq_hash = 0x5814c8daa5e36375;
    info.static_total_max_seq = 0;
    info.static_total_max_seq_hash = 0;
    info.userdb_total_max_seq = 3539259320;
    info.userdb_total_max_seq_hash = 0xba2323a8f06adce5;
    zpn_sitec_gid_update_status_info(289397352052032170Ull, &info);

    info.sitec_gid = 289397352052032173Ull;
    info.sc_lat = 19.07;
    info.sc_lon = 72.87;
    info.mem_util = 1;
    info.cpu_util = 1;
    info.proc_fd_util = 1;
    info.status = 0;
    info.sc_cc = (char *)(new string("IN"))->c_str();
    info.shard_total_max_seq = 29145390;
    info.shard_total_max_seq_hash = 0x435cb39c5bec9f8c;
    info.master_total_max_seq = 17273243;
    info.master_total_max_seq_hash = 0x5814c8daa5e36375;
    info.static_total_max_seq = 0;
    info.static_total_max_seq_hash = 0;
    info.userdb_total_max_seq = 3539259320;
    info.userdb_total_max_seq_hash = 0xba2323a8f06adce5;
    zpn_sitec_gid_update_status_info(289397352052032173Ull, &info);

    state.status_obj = (struct zpn_sitec_status_obj *)SITEC_CALLOC(3 * sizeof(struct zpn_sitec_status_obj));
    state.count = 3;
    int64_t sitec_gid;

    zpn_sitec_get_status_obj(289397352052032166Ull, &state.status_obj[0]);
    zpn_sitec_get_status_obj(289397352052032173Ull, &state.status_obj[1]);
    zpn_sitec_get_status_obj(289397352052032170Ull, &state.status_obj[2]);

    sitec_gid = zpn_sitec_get_best_sitec(&state, 29145390, 0x435cb39c5bec9f8c, sitec_wally_conn_type_userdb, 0);

    EXPECT_EQ(sitec_gid, 289397352052032166Ull);
}

TEST_F(SitecStatusConnTest, BestPCCStatusRcfg) {
    struct zpn_sitec_status_state state;

    info.sitec_gid = 289397352052032166Ull;
    info.sc_lat = 28.70;
    info.sc_lon = 77.10;
    info.mem_util = 1;
    info.cpu_util = 30;
    info.proc_fd_util = 1;
    info.status = 0;
    info.sc_cc = (char *)(new string("IN"))->c_str();
    info.shard_total_max_seq = 29145390;
    info.shard_total_max_seq_hash = 0x435cb39c5bec9f8c;
    info.master_total_max_seq = 17273250;
    info.master_total_max_seq_hash = 0x5814c8daa5e36895;
    info.static_total_max_seq = 17273890;
    info.static_total_max_seq_hash = 0x5814c8daa5e36890;
    info.userdb_total_max_seq = 3539259326;
    info.userdb_total_max_seq_hash = 0xba2323a8f06aece5;
    zpn_sitec_gid_update_status_info(289397352052032166Ull, &info);

    info.sitec_gid = 289397352052032170Ull;
    info.sc_lat = 28.70;
    info.sc_lon = 77.10;
    info.mem_util = 1;
    info.cpu_util = 1;
    info.proc_fd_util = 1;
    info.status = 0;
    info.sc_cc = (char *)(new string("IN"))->c_str();
    info.shard_total_max_seq = 29145390;
    info.shard_total_max_seq_hash = 0x435cb39c5bec9f7c;
    info.master_total_max_seq = 17273243;
    info.master_total_max_seq_hash = 0x5814c8daa5e36375;
    info.static_total_max_seq = 17273856;
    info.static_total_max_seq_hash = 0x5814c8dba5e36375;
    info.userdb_total_max_seq = 3539259326;
    info.userdb_total_max_seq_hash = 0xba2323a8f06aece5;
    zpn_sitec_gid_update_status_info(289397352052032170Ull, &info);

    info.sitec_gid = 289397352052032173Ull;
    info.sc_lat = 19.07;
    info.sc_lon = 72.87;
    info.mem_util = 1;
    info.cpu_util = 1;
    info.proc_fd_util = 1;
    info.status = 0;
    info.sc_cc = (char *)(new string("IN"))->c_str();
    info.shard_total_max_seq = 29145390;
    info.shard_total_max_seq_hash = 0x435cb39c5bec9f8c;
    info.master_total_max_seq = 17273243;
    info.master_total_max_seq_hash = 0x5814c8daa5e36375;
    info.static_total_max_seq = 17273840;
    info.static_total_max_seq_hash = 0x5814c8dba5f36375;
    info.userdb_total_max_seq = 3539259326;
    info.userdb_total_max_seq_hash = 0xba2323a8f06aece5;
    zpn_sitec_gid_update_status_info(289397352052032173Ull, &info);

    state.status_obj = (struct zpn_sitec_status_obj *)SITEC_CALLOC(3 * sizeof(struct zpn_sitec_status_obj));
    state.count = 3;
    int64_t sitec_gid;

    zpn_sitec_get_status_obj(289397352052032166Ull, &state.status_obj[0]);
    zpn_sitec_get_status_obj(289397352052032173Ull, &state.status_obj[1]);
    zpn_sitec_get_status_obj(289397352052032170Ull, &state.status_obj[2]);

    sitec_gid = zpn_sitec_get_best_sitec(&state, 17273879, 0x435cb39c5bec9f8c, sitec_wally_conn_type_rcfg, 0);

    EXPECT_EQ(sitec_gid, 289397352052032166Ull);
}

TEST_F(SitecStatusConnTest, StatusConnRemoveFromHashTable) {
    int result = zpn_sitec_remove_status_conn_by_gid(289397352052032173Ull);
    EXPECT_EQ(result, ZPN_RESULT_NO_ERROR);
}

TEST_F(SitecStatusConnTest, GetStatusConnInfo) {
    struct fohh_connection *f_conn = new struct fohh_connection;

    sitec_gs.site_gid = 289397352052032162Ull;
    f_conn->state = fohh_connection_connected;
    sitec_gs.sitec_state->ctrl_fohh_conn = f_conn;
    sitec_gs.sitec_state->rcfg_wally = (struct wally*)f_conn;

    EXPECT_CALL(*get_mock(), zpn_sitec_group_get_by_site_gid).Times(1).WillOnce(testing::DoAll(testing::WithArg<1>(SetSitecGroup(group, 3)),testing::SetArgPointee<2>(3), testing::Return(0)));

    EXPECT_CALL(*get_mock(), zpn_sitec_to_group_get_by_grpid).Times(3)
        .WillOnce(testing::DoAll(testing::WithArg<1>(SetSitecToGroup(sc_to_group, 0)),testing::SetArgPointee<2>(1), testing::Return(0)))
        .WillOnce(testing::DoAll(testing::WithArg<1>(SetSitecToGroup(sc_to_group, 1)),testing::SetArgPointee<2>(1), testing::Return(0)))
        .WillOnce(testing::DoAll(testing::WithArg<1>(SetSitecToGroup(sc_to_group, 2)),testing::SetArgPointee<2>(1), testing::Return(0)));

    EXPECT_CALL(*get_mock(), wally_get_total_max_seq_with_hash).WillRepeatedly(testing::DoAll(testing::SetArgPointee<1>(0x435cb39c5bec9f6c),testing::Return(29145370)));

    char* result = zpn_sitec_get_status_conn_info(1);

    ASSERT_THAT(result, testing::NotNull());
}

TEST_F(SitecStatusConnTest, AddSplitBrainDebugCmds) {
    int result = zpn_sitec_add_split_brain_debug_commands();
    EXPECT_EQ(result, ZPN_RESULT_NO_ERROR);
}

TEST_F(SitecStatusConnTest, SendStatusToAllSitec) {
    struct fohh_connection *f_conn = new struct fohh_connection;
    f_conn->state = fohh_connection_connected;
    sitec_gs.sitec_state->ctrl_fohh_conn = f_conn;
    sitec_gs.sitec_group_id = 289397352052032159Ull;

    EXPECT_CALL(*get_mock(), zpn_sitec_group_get_by_gid).WillRepeatedly(testing::DoAll(testing::WithArg<1>(SetSitecGroup(group, 1)),testing::SetArgPointee<2>(1), testing::Return(0)));

    EXPECT_CALL(*get_mock(), wally_get_total_max_seq_with_hash).WillRepeatedly(testing::DoAll(testing::SetArgPointee<1>(0x435cb39c5bec9f6c),testing::Return(29145370)));

    zpn_sitec_send_status_to_all_sitec();
}

TEST_F(SitecStatusConnTest, IsStatusConnAvailable) {
    int result = zpn_sitec_is_status_conn_available();
    ASSERT_THAT(result, testing::Ne(0));
}

TEST_F(SitecStatusConnTest, AddSniListeners) {
    int result;
    struct zcrypt_rsa_key *cfg_rsa_key;
    sitec_gs.sni_server = fohh_generic_server_create(1, 1);
    cfg_rsa_key = zcrypt_rsa_key_create();
    char *cloud_file = (char*)(new string("./cloud.pem"))->c_str();
    char *cert_file = (char*)(new string("./cert.pem"))->c_str();
    char *key_file = (char*)(new string("./rsa_key.pem"))->c_str();
    result = fohh_ssl_init(NULL, 1);
    EXPECT_EQ(result, ZPATH_RESULT_NO_ERROR);

    result = fohh_init(1,
                        0,     // Don't disable SSL
                        NULL,  // No prefix
                        cloud_file,
                        cert_file,
                        key_file,
                        NULL,
                        0,
                        2000,
                        0);
    EXPECT_EQ(result, ZPATH_RESULT_NO_ERROR);

    result = zpn_enroll_get_private_key(&sitec_gs.cfg_hw_key, cfg_rsa_key, (char*)(new string("rsa"))->c_str());
    EXPECT_EQ(result, ZPATH_RESULT_NO_ERROR);

    result = fohh_reset_global_ssl_ctx(cloud_file,
                                       cert_file,
                                       key_file,
                                       sitec_gs.cfg_pkey,
                                       VERIFY_PEER);
    EXPECT_EQ(result, ZPATH_RESULT_NO_ERROR);

    result = zpn_sitec_add_sitec_listeners(sitec_gs.sni_server, (char*)(new string("ddiltest-od.com"))->c_str());
    EXPECT_EQ(result, ZPN_RESULT_NO_ERROR);

    zpn_sitec_enable_sitec_snis(0);
    zpn_sitec_enable_sitec_snis(1);
    zpn_sitec_update_sitec_listeners(sitec_gs.sni_server, (char*)(new string("ddiltest.com"))->c_str());
}

TEST_F(SitecStatusConnTest, SitecGidToStatusConn) {
    struct fohh_connection *f_conn = NULL;
    int64_t incarnation = 0;

    int result = zpn_sitec_gid_to_status_conn(289397352052032166Ull, &f_conn, &incarnation);
    ASSERT_THAT(f_conn, testing::NotNull());
    ASSERT_THAT(incarnation, testing::Ne(0));
    EXPECT_EQ(result, ZPATH_RESULT_NO_ERROR);
}

TEST_F(SitecStatusConnTest, SitecStatusConnMonitor) {
    struct fohh_connection *f_conn = new struct fohh_connection;
    struct zpn_sitec_status_fohh_state *sc_state = new struct zpn_sitec_status_fohh_state;
    struct zpath_customer *cust = new struct zpath_customer;
    struct fohh_connection *ctrl_f_conn = new struct fohh_connection;
    ctrl_f_conn->state = fohh_connection_connected;
    sitec_gs.sitec_state->ctrl_fohh_conn = ctrl_f_conn;

    f_conn->dynamic_cookie = sc_state;
    f_conn->peer_id = 289397352052032170Ull;
    sc_state->monitor_count = 5;
    cust->disable_traffic = 0;
    sitec_gs.sitec_group_id = 289397352052032159Ull;

    EXPECT_CALL(*get_mock(), zpn_sitec_group_get_by_gid).WillRepeatedly(testing::DoAll(testing::WithArg<1>(SetSitecGroup(group, 1)),testing::SetArgPointee<2>(1), testing::Return(0)));
    EXPECT_CALL(*get_mock(), wally_get_total_max_seq_with_hash).WillRepeatedly(testing::DoAll(testing::SetArgPointee<1>(0x435cb39c5bec9f6c),testing::Return(29145370)));
    EXPECT_CALL(*get_mock(), zpath_customer_get).Times(1).WillOnce(testing::DoAll(testing::WithArg<1>(SetCustomer(cust)), testing::Return(0)));

    zpn_sitec_status_conn_monitor_cb(10, 0, f_conn);
}

TEST_F(SitecStatusConnTest, SitecConfigConnMonitor) {
    struct fohh_connection *f_conn = new struct fohh_connection;
    struct zpn_site_controller_to_group *to_group = new struct zpn_site_controller_to_group;
    struct connected_sitec_sitec_config_fohh_state *sc_state = new struct connected_sitec_sitec_config_fohh_state;
    struct zpath_customer *cust = new struct zpath_customer;

    f_conn->dynamic_cookie = sc_state;
    f_conn->peer_id = 289397352052032170Ull;

    sc_state->f_conn = f_conn;
    sc_state->monitor_count = 5;

    cust->disable_traffic = 0;

    sitec_gs.sitec_group_id = 289397352052032159Ull;
    sitec_gs.disable_flags = 0;
    sitec_gs.site_gid = 289397352052032162Ull;

    to_group->site_controller_group_gid = 289397352052032161Ull;

    EXPECT_CALL(*get_mock(), zpn_sitec_group_get_by_gid).WillRepeatedly(testing::DoAll(testing::WithArg<1>(SetSitecGroup(group, 1)),testing::SetArgPointee<2>(1), testing::Return(0)));
    EXPECT_CALL(*get_mock(), zpath_customer_get).Times(1).WillOnce(testing::DoAll(testing::WithArg<1>(SetCustomer(cust)), testing::Return(0)));
    EXPECT_CALL(*get_mock(), zpn_sitec_to_group_get_by_sitec_gid).WillRepeatedly(testing::DoAll(testing::WithArg<1>(SetSitec(to_group)), testing::Return(0)));


    zpn_sitec_sitec_config_conn_monitor_cb(10, 0, sc_state);
}

TEST_F(SitecStatusConnTest, StatusConnCallback) {
    struct fohh_connection *f_conn = new struct fohh_connection;
    struct zpath_customer *cust = new struct zpath_customer;
    struct zpn_site_controller *sitec = new struct zpn_site_controller;
    struct zpn_site_controller_to_group *to_group = new struct zpn_site_controller_to_group;

    memset(f_conn, 0, sizeof(struct fohh_connection));
    cust->disable_traffic = 0;
    f_conn->state = fohh_connection_connected;
    f_conn->argo.rx_argo = new struct argo_state;
    f_conn->argo.rx_argo->structures = (struct argo_structure_state **)
    ARGO_ALLOC(sizeof(struct argo_structure_state *) * 20);
    f_conn->argo.rx_argo->registered_structures = zhash_table_alloc(&argo_allocator);
    f_conn->peer_id = 289397352052032170Ull;
    f_conn->type = fohh_connection_type_client;
    snprintf(f_conn->peer_common_name, sizeof(f_conn->peer_common_name), "%ld.sitec.dev.zpath.net", (unsigned long)f_conn->peer_id);

    sitec->scope_gid = 0;
    to_group->site_controller_group_gid = 289397352052032161Ull;

    EXPECT_CALL(*get_mock(), zpath_customer_get).WillRepeatedly(testing::DoAll(testing::WithArg<1>(SetCustomer(cust)), testing::Return(0)));
    EXPECT_CALL(*get_mock(), zpn_sitec_get_by_id).WillRepeatedly(testing::DoAll(testing::WithArg<1>(SetSitec(sitec)), testing::Return(0)));
    EXPECT_CALL(*get_mock(), zpn_sitec_to_group_get_by_sitec_gid).WillRepeatedly(testing::DoAll(testing::WithArg<1>(SetSitec(to_group)), testing::Return(0)));
    EXPECT_CALL(*get_mock(), zevent_event_base).WillRepeatedly(testing::Return(base));

    int result = zpn_sitec_status_conn_callback(f_conn, fohh_connection_connected, NULL);
    EXPECT_EQ(result, ZPATH_RESULT_NO_ERROR);
    ASSERT_THAT(f_conn->dynamic_cookie, testing::NotNull());

    result = zpn_sitec_status_conn_callback(f_conn, fohh_connection_disconnected, NULL);
    EXPECT_EQ(result, ZPATH_RESULT_NO_ERROR);

    //server side
    f_conn->state = fohh_connection_connected;
    f_conn->peer_id = 289397352052032170Ull;
    f_conn->type = fohh_connection_type_server;
    f_conn->dynamic_cookie = NULL;

    result = zpn_sitec_status_conn_callback(f_conn, fohh_connection_connected, NULL);
    EXPECT_EQ(result, ZPATH_RESULT_NO_ERROR);
}

TEST_F(SitecStatusConnTest, ConfigConnCallback) {
    struct fohh_connection *f_conn = new struct fohh_connection;
    struct zpath_customer *cust = new struct zpath_customer;

    memset(f_conn, 0, sizeof(struct fohh_connection));
    cust->disable_traffic = 0;
    f_conn->state = fohh_connection_connected;
    f_conn->argo.rx_argo = new struct argo_state;
    f_conn->argo.rx_argo->structures = (struct argo_structure_state **)
    ARGO_ALLOC(sizeof(struct argo_structure_state *) * 20);
    f_conn->argo.rx_argo->registered_structures = zhash_table_alloc(&argo_allocator);

    f_conn->argo.tx_argo = new struct argo_state;
    f_conn->argo.tx_argo->structures = (struct argo_structure_state **)
    ARGO_ALLOC(sizeof(struct argo_structure_state *) * 20);
    f_conn->argo.tx_argo->registered_structures = zhash_table_alloc(&argo_allocator);

    f_conn->peer_id = 289397352052032170Ull;
    zpath_cloud_global_state.current_config = new struct zpath_cloud;
    zpath_cloud_global_state.current_config->shard_count = 4;

    EXPECT_CALL(*get_mock(), zpath_customer_get).WillRepeatedly(testing::DoAll(testing::WithArg<1>(SetCustomer(cust)), testing::Return(0)));
    EXPECT_CALL(*get_mock(), zevent_event_base).WillRepeatedly(testing::Return(base));

    int result = zpn_sitec_sitec_cfg_conn_callback(f_conn, fohh_connection_connected, NULL);
    EXPECT_EQ(result, ZPATH_RESULT_NO_ERROR);

    result = zpn_sitec_sitec_cfg_conn_callback(f_conn, fohh_connection_disconnected, NULL);
    EXPECT_EQ(result, ZPATH_RESULT_NO_ERROR);
}

TEST_F(SitecStatusConnTest, RconfigConnCallback) {
    struct fohh_connection *f_conn = new struct fohh_connection;
    struct zpath_customer *cust = new struct zpath_customer;

    memset(f_conn, 0, sizeof(struct fohh_connection));
    cust->disable_traffic = 0;
    f_conn->state = fohh_connection_connected;
    f_conn->argo.rx_argo = new struct argo_state;
    f_conn->argo.rx_argo->structures = (struct argo_structure_state **)
    ARGO_ALLOC(sizeof(struct argo_structure_state *) * 20);
    f_conn->argo.rx_argo->registered_structures = zhash_table_alloc(&argo_allocator);

    f_conn->argo.tx_argo = new struct argo_state;
    f_conn->argo.tx_argo->structures = (struct argo_structure_state **)
    ARGO_ALLOC(sizeof(struct argo_structure_state *) * 20);
    f_conn->argo.tx_argo->registered_structures = zhash_table_alloc(&argo_allocator);

    f_conn->peer_id = 289397352052032170Ull;
    zpath_cloud_global_state.current_config = new struct zpath_cloud;
    zpath_cloud_global_state.current_config->shard_count = 4;

    EXPECT_CALL(*get_mock(), zpath_customer_get).WillRepeatedly(testing::DoAll(testing::WithArg<1>(SetCustomer(cust)), testing::Return(0)));
    EXPECT_CALL(*get_mock(), zevent_event_base).WillRepeatedly(testing::Return(base));

    int result = zpn_sitec_sitec_rcfg_conn_callback(f_conn, fohh_connection_connected, NULL);
    EXPECT_EQ(result, ZPATH_RESULT_NO_ERROR);

    result = zpn_sitec_sitec_rcfg_conn_callback(f_conn, fohh_connection_disconnected, NULL);
    EXPECT_EQ(result, ZPN_RESULT_ERR);
}

TEST_F(SitecStatusConnTest, UserdbConnCallback) {
    struct fohh_connection *f_conn = new struct fohh_connection;
    struct zpath_customer *cust = new struct zpath_customer;
    struct wally* userdb_wally = (wally*)ZPN_CALLOC(320000);
    struct wally_fohh_server *userdb_server = wally_fohh_server_create(userdb_wally, argo_serialize_binary, fohh_connection_style_argo, 1, NULL, 0, NULL, NULL, NULL, 0);

    memset(f_conn, 0, sizeof(struct fohh_connection));
    cust->disable_traffic = 0;
    f_conn->state = fohh_connection_connected;
    f_conn->argo.rx_argo = new struct argo_state;
    f_conn->argo.rx_argo->structures = (struct argo_structure_state **)
    ARGO_ALLOC(sizeof(struct argo_structure_state *) * 20);
    f_conn->argo.rx_argo->registered_structures = zhash_table_alloc(&argo_allocator);

    f_conn->argo.tx_argo = new struct argo_state;
    f_conn->argo.tx_argo->structures = (struct argo_structure_state **)
    ARGO_ALLOC(sizeof(struct argo_structure_state *) * 20);
    f_conn->argo.tx_argo->registered_structures = zhash_table_alloc(&argo_allocator);

    f_conn->peer_id = 289397352052032170Ull;
    snprintf(f_conn->sni_name, sizeof(f_conn->sni_name), "289397352052032163.1.1.scuserdb.dev.zpath.net");

    zpath_cloud_global_state.current_config = new struct zpath_cloud;
    zpath_cloud_global_state.current_config->shard_count = 4;

    EXPECT_CALL(*get_mock(), zpath_customer_get).WillRepeatedly(testing::DoAll(testing::WithArg<1>(SetCustomer(cust)), testing::Return(0)));
    EXPECT_CALL(*get_mock(), zevent_event_base).WillRepeatedly(testing::Return(base));
    EXPECT_CALL(*get_mock(), zpath_et_wally_userdb_get_userdb_wally_server).WillOnce(testing::Return(userdb_server));

    int result = zpn_sitec_sitec_userdb_conn_callback(f_conn, fohh_connection_connected, NULL);
    EXPECT_EQ(result, ZPATH_RESULT_NO_ERROR);

    result = zpn_sitec_sitec_userdb_conn_callback(f_conn, fohh_connection_disconnected, NULL);
    EXPECT_EQ(result, ZPN_RESULT_ERR);
}

TEST_F(SitecStatusConnTest, CreateStatusConnToAllSitec) {

    sitec_gs.site_gid = 289397352052032162Ull;
    sitec_gs.sitec_id = 289397352052032163Ull;

    EXPECT_CALL(*get_mock(), zpn_sitec_group_get_by_site_gid).Times(1).WillOnce(testing::DoAll(testing::WithArg<1>(SetSitecGroup(group, 3)),testing::SetArgPointee<2>(3), testing::Return(0)));

    EXPECT_CALL(*get_mock(), zpn_sitec_to_group_get_by_grpid).Times(3)
        .WillOnce(testing::DoAll(testing::WithArg<1>(SetSitecToGroup(sc_to_group, 0)),testing::SetArgPointee<2>(1), testing::Return(0)))
        .WillOnce(testing::DoAll(testing::WithArg<1>(SetSitecToGroup(sc_to_group, 1)),testing::SetArgPointee<2>(1), testing::Return(0)))
        .WillOnce(testing::DoAll(testing::WithArg<1>(SetSitecToGroup(sc_to_group, 2)),testing::SetArgPointee<2>(1), testing::Return(0)));


    int result = zpn_sitec_create_status_conn_to_all_sitec();
    EXPECT_EQ(result, ZPATH_RESULT_NO_ERROR);
}

TEST_F(SitecStatusConnTest, CreateStatusConn) {
    struct zpn_site_controller_to_group *to_group = new struct zpn_site_controller_to_group;

    sitec_gs.site_gid = 289397352052032162Ull;
    sitec_gs.sitec_id = 289397352052032163Ull;
    to_group->site_controller_group_gid = 289397352052032160Ull;

    EXPECT_CALL(*get_mock(), zpn_sitec_to_group_get_by_sitec_gid).WillRepeatedly(testing::DoAll(testing::WithArg<1>(SetSitec(to_group)), testing::Return(0)));
    EXPECT_CALL(*get_mock(), zpn_sitec_group_get_by_gid).WillRepeatedly(testing::DoAll(testing::WithArg<1>(SetSitecGroup(group, 1)),testing::SetArgPointee<2>(1), testing::Return(0)));

    int result = zpn_sitec_create_status_conn(289397352052032173Ull);
    EXPECT_EQ(result, ZPATH_RESULT_NO_ERROR);
}

TEST_F(SitecStatusConnTest, DeleteStatusConn) {
    struct fohh_connection *f_conn = (struct fohh_connection *)ZPN_CALLOC(sizeof(struct fohh_connection));
    snprintf(f_conn->default_remote_address_name, sizeof(f_conn->default_remote_address_name), "%s", "sc2br.dev.zpath.net");

    f_conn->incarnation = 1;
    f_conn->state = fohh_connection_connected;
    f_conn->fohh_thread_id = 0;

    fohh_g.threads[0].zevent_base = zevent_handler_create("thread0", 16*1024*1024, 30);

    zpn_sitec_store_status_conn_by_gid(289397352052032174Ull, (char*)(new string("PCC4"))->c_str(), f_conn);

    int result = zpn_sitec_delete_status_conn(289397352052032174Ull);
    EXPECT_EQ(result, ZPATH_RESULT_NO_ERROR);
}
