/*
 * zpn_sitesp_test.cpp. Copyright (C) 2025 Zscaler, Inc. All Rights Reserved
 */

#include <gtest/gtest.h>
#include <gmock/gmock.h>

#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wunused-parameter"
#elif defined __GNUC__
#pragma GCC diagnostic push
#pragma GCC diagnostic ignored "-Wunused-parameter"
#endif // __clang__

#include <cstdint>

#include "test_misc/ZscalerMockBase.h"

extern "C" {
#define INSTANCE_ID_BYTES 32

#include "zpn/zpn_lib.h"
#include "wally/wally.h"
#include "zpn_sitecd/zpn_sitesp.h"
#include "zpn_sitecd/zpn_sitec_private.h"
#include "zpn/zpn_idp.h"
#include "zpn/zpn_ddil_config.h"
#include "zsaml/zsaml.h"
#include "fohh/fohh_http_private.h"

struct zpn_sitec_global_state sitec_gs;

struct zpn_sitec_global_state* zpn_get_sitec_global_state(void)
{
    /* Return pointer to singleton private broker global state */
    return &sitec_gs;
}
void zpn_sitesp_comprehensive_stats_fill(struct zpn_sitec_comprehensive_stats*);

struct sitesp_session* sitesp_create_session(char *domain,
                                            char *org_url,
                                            char *code_challenge,
                                            char *code_challenge_method,
                                            int64_t idp_gid,
                                            char *id,
                                            char *session_cookie,
                                            char *idp_entity_id,
                                            char *idp_login_url,
                                            int64_t customer_gid);

int sitesp_remove_session(char *session_id);
};

using namespace std;
class SitespGenSamlMock {
    public:
        MOCK_METHOD(struct zpn_idp*, sitesp_fetch_domain_details, (char*));
        MOCK_METHOD(struct zpn_ddil_config*, sitesp_fetch_offline_config, ());
        MOCK_METHOD(struct event_base*, zevent_event_base, (struct zevent_base*));
};

#define MOCK_SITESP_FETCH_DOMAIN_DETAILS(T)                             \
    struct zpn_idp* sitesp_fetch_domain_details(char *domain) {         \
        return T::get_mock()->sitesp_fetch_domain_details(domain);      \
    }

#define MOCK_SITESP_FETCH_OFFLINE_CONFIG(T)                             \
    struct zpn_ddil_config* sitesp_fetch_offline_config() {             \
            return T::get_mock()->sitesp_fetch_offline_config();        \
    }

#define MOCK_ZEVENT_EVENT_BASE(T)                                       \
struct event_base* zevent_event_base(struct zevent_base *ev_base) {     \
        return T::get_mock()->zevent_event_base(ev_base);               \
}

class ZpnSitespGenSamlMockBase : public testing::StrictMock<SitespGenSamlMock> {};

// Test fixture
class SitespGenSamlTest : public ZscalerMockBase<ZpnSitespGenSamlMockBase> {
protected:
    struct fohh_http_request request_;
    struct fohh_http_response response_;
    char domain_[256];
    char org_url_[256];
    char code_challenge_[256];
    char code_challenge_method_[256];
    struct event_base *base;
    struct zpn_idp* idp;
    struct zpn_ddil_config* dc;
public:
    void SetUp() override {
        ZscalerMockBase::SetUp();
        zpn_sitesp_hash_table_alloc();
        zsaml_init();
        base = event_base_new();
        response_.body = evbuffer_new();
******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

        idp = new struct zpn_idp();
        idp->name = (char *)(new string("IDP-User-sso"))->c_str();
        idp->customer_gid = 289397352052031488Ull;
        idp->login_url = (char *)(new string("https://dev-40363338.okta.com/app/zscaler_private_access/exkg1rmq81L0TkEUB5d7/sso/saml"))->c_str();
        idp->entity_id = (char *)(new string("http://www.okta.com/exkg1rmq81L0TkEUB5d7"))->c_str();
        idp->domain_list = new (char *);
        idp->domain_list[0] = (char *)(new string("ddiltest.com"))->c_str();
        idp->domain_list_count = 1;
        idp->sso_type = new (char *);
        idp->sso_type[0] = (char *)(new string("USER"))->c_str();
        idp->sso_type_count = 1;
        idp->gid = 289397352052031507Ull;
        idp->disabled = false;
        idp->sign_saml_request = true;

        dc = new struct zpn_ddil_config();
        dc->gid = 289397352052031621Ull;
        dc->customer_gid = 289397352052031488Ull;
        dc->offline_domain = (char *)(new string("ddiltest-od.com"))->c_str();
        dc->use_existing_idp = false;
        dc->max_allowed_downtime = 120;
        dc->idp_login_url = (char *)(new string("https://dev-40363338.okta.com/app/zscaler_private_access/exkhkzfyjpUETzCwZ5d7/sso/saml"))->c_str();
        dc->idp_entity_id = (char *)(new string("http://www.okta.com/exkhkzfyjpUETzCwZ5d7"))->c_str();

        // Set up input parameters
        strcpy(domain_, "ddiltest.com");
        strcpy(org_url_, "https://bcpsp.ddiltest-od.com/test.html");
        strcpy(code_challenge_, "Dbc7ugtNAcJz85GOu6QPJmhKk10rFAj2ZXKRDz855to");
        strcpy(code_challenge_method_, "S256");
    }

    void TearDown() override {
        ZscalerMockBase::TearDown();
    }
};

extern "C" {
    MOCK_SITESP_FETCH_DOMAIN_DETAILS(SitespGenSamlTest);
    MOCK_SITESP_FETCH_OFFLINE_CONFIG(SitespGenSamlTest);
    MOCK_ZEVENT_EVENT_BASE(SitespGenSamlTest);
}

// Test cases
TEST_F(SitespGenSamlTest, HappyPath) {

    // Set up mock function to return disabled IDP
    EXPECT_CALL(*get_mock(), sitesp_fetch_domain_details).Times(1).WillOnce(testing::Return(idp));
    EXPECT_CALL(*get_mock(), sitesp_fetch_offline_config).Times(1).WillOnce(testing::Return(dc));
    EXPECT_CALL(*get_mock(), zevent_event_base).Times(1).WillOnce(testing::Return(base));

    // Call the function under test
    int result = sitesp_generate_saml_request(&request_, &response_, domain_, org_url_, code_challenge_, code_challenge_method_);

    // Verify the result
    EXPECT_EQ(result, ZPN_RESULT_NO_ERROR);
}

TEST_F(SitespGenSamlTest, InvalidDomain) {
    // Set up invalid domain
    strcpy(domain_, "");
    struct zpn_idp *dummy_idp = NULL;

    EXPECT_CALL(*get_mock(), sitesp_fetch_domain_details).Times(1).WillOnce(testing::Return(dummy_idp));
    // Call the function under test
    int result = sitesp_generate_saml_request(&request_, &response_, domain_, org_url_, code_challenge_, code_challenge_method_);

    // Verify the result
    EXPECT_EQ(result, ZPN_RESULT_ERR);
}

TEST_F(SitespGenSamlTest, InvalidOrgUrl) {
    // Set up invalid org URL
    strcpy(org_url_, "");

    EXPECT_CALL(*get_mock(), sitesp_fetch_domain_details).Times(1).WillOnce(testing::Return(idp));
    EXPECT_CALL(*get_mock(), sitesp_fetch_offline_config).Times(1).WillOnce(testing::Return(dc));

    // Call the function under test
    int result = sitesp_generate_saml_request(&request_, &response_, domain_, org_url_, code_challenge_, code_challenge_method_);

    // Verify the result
    EXPECT_EQ(result, SITESP_RESULT_INVALID_URL);
}

TEST_F(SitespGenSamlTest, DisabledIdp) {
    // Set up mock IDP structure with disabled IDP
    struct zpn_idp* idp = new struct zpn_idp();
    idp->disabled = true;
    idp->sign_saml_request = true;

    // Set up mock function to return disabled IDP
    EXPECT_CALL(*get_mock(), sitesp_fetch_domain_details).Times(1).WillOnce(testing::Return(idp));
    EXPECT_CALL(*get_mock(), sitesp_fetch_offline_config).Times(1).WillOnce(testing::Return(dc));

    // Call the function under test
    int result = sitesp_generate_saml_request(&request_, &response_, domain_, org_url_, code_challenge_, code_challenge_method_);

    // Verify the result
    EXPECT_EQ(result, SITESP_RESULT_IDP_DISABLED);
}

TEST_F(SitespGenSamlTest, SamlSignDisable) {
    idp->sign_saml_request = false;
    EXPECT_CALL(*get_mock(), sitesp_fetch_domain_details).Times(1).WillOnce(testing::Return(idp));
    EXPECT_CALL(*get_mock(), sitesp_fetch_offline_config).Times(1).WillOnce(testing::Return(dc));
    EXPECT_CALL(*get_mock(), zevent_event_base).Times(1).WillOnce(testing::Return(base));

    // Call the function under test
    int result = sitesp_generate_saml_request(&request_, &response_, domain_, org_url_, code_challenge_, code_challenge_method_);

    // Verify the result
    EXPECT_EQ(result, ZPN_RESULT_NO_ERROR);
}

TEST_F(SitespGenSamlTest, GenSamlExistingIDP) {
    dc->use_existing_idp = true;
    EXPECT_CALL(*get_mock(), sitesp_fetch_domain_details).Times(1).WillOnce(testing::Return(idp));
    EXPECT_CALL(*get_mock(), sitesp_fetch_offline_config).Times(1).WillOnce(testing::Return(dc));
    EXPECT_CALL(*get_mock(), zevent_event_base).Times(1).WillOnce(testing::Return(base));

    // Call the function under test
    int result = sitesp_generate_saml_request(&request_, &response_, domain_, org_url_, code_challenge_, code_challenge_method_);

    // Verify the result
    EXPECT_EQ(result, ZPN_RESULT_NO_ERROR);
}

TEST_F(SitespGenSamlTest, SitespCompStatsFill) {
    struct zpn_sitec_comprehensive_stats *out_data = new struct zpn_sitec_comprehensive_stats;
    zpn_sitesp_comprehensive_stats_fill(out_data);
}

TEST_F(SitespGenSamlTest, SitespSessionTest) {
    int64_t gid = 289397352052031507;
    int64_t cust_gid = 289397352052031500;
    char *session_id = (char*)(new string("clmqBaR16c6HTSaTEqpuAA=="))->c_str();

    EXPECT_CALL(*get_mock(), zevent_event_base).Times(1).WillOnce(testing::Return(base));

    struct sitesp_session *sess = sitesp_create_session((char*)(new string("ddiltest"))->c_str(),
                                                        (char*)(new string("https://bcpsp.ddiltest-od.com/test.html"))->c_str(),
                                                        (char*)(new string("Dbc7ugtNAcJz85GOu6QPJmhKk10rFAj2ZXKRDz855to"))->c_str(),
                                                        (char*)(new string("S256"))->c_str(),
                                                        gid,
                                                        (char*)(new string("id"))->c_str(),
                                                        session_id,
                                                        (char*)(new string("entity_id"))->c_str(),
                                                        (char*)(new string("login_url"))->c_str(),
                                                        cust_gid);

    // Assert the result
    ASSERT_THAT(sess, testing::NotNull());

    struct sitesp_session* lsess = sitesp_lookup_session(session_id);
    EXPECT_EQ(lsess, sess);

    int result = sitesp_remove_session(session_id);
    EXPECT_EQ(result, ZPN_RESULT_NO_ERROR);
}

int main(int argc, char **argv) {
    ::testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}
