/*
 * zpn_sitec_firedrill_test.cpp. Copyright (C) 2025 Zscaler, Inc. All Rights Reserved
 */

#include <gtest/gtest.h>
#include <gmock/gmock.h>

#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wunused-parameter"
#elif defined __GNUC__
#pragma GCC diagnostic push
#pragma GCC diagnostic ignored "-Wunused-parameter"
#endif // __clang__

#include <cstdint>

#include "test_misc/ZscalerMockBase.h"

#include "zpn/zpn_lib.h"
#include "fohh/fohh.h"
#include "zpn_sitecd/zpn_sitec_sitec_conn.h"
#include "zpn_sitecd/zpn_sitec_broker_conn.h"
#include "fohh/fohh_private.h"
#include "fohh/fohh.h"
#include "wally/wally.h"
#include "zpn/zpn_sitec_table.h"
#include "zpn/zpn_sitec_group.h"
#include "zpn/zpn_sitec_to_group.h"
#include "zpath_lib/zpath_customer.h"
#include "argo/argo_private.h"
#include "zpath_lib/zpath_cloud.h"
#include "zpath_lib/zpath_et_wally_userdb.h"



#if 0
TEST(SitecFiredrillTestSuite, DeleteBrokerConnections) {
    struct fohh_connection *f_conn = new struct fohh_connection;
    f_conn->state = fohh_connection_connected;
    const char *reason = "GTEST_CODE_DISCONNECT";

    zpn_sitec_delete_broker_connections(reason);

}
#endif
