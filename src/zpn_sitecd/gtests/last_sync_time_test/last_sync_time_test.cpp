/*
 * last_sync_time_test.cpp. Copyright (C) 2025 Zscaler, Inc. All Rights Reserved
 */

#include <gtest/gtest.h>
#include <gmock/gmock.h>

#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wunused-parameter"
#pragma clang diagnostic ignored "-Wwrite-strings"
#elif defined __GNUC__
#pragma GCC diagnostic push
#pragma GCC diagnostic ignored "-Wunused-parameter"
#pragma GCC diagnostic ignored "-Wwrite-strings"
#endif // __clang__

#include <cstdint>

#include "test_misc/ZscalerMockBase.h"

extern "C" {
    #define INSTANCE_ID_BYTES 32

    #include "zpn/zpn_lib.h"
    #include "fohh/fohh.h"
    #include "zpn_sitecd/zpn_sitec_private.h"
    #include "fohh/fohh_private.h"
    #include "fohh/fohh.h"
    #include "wally/wally.h"
    #include "zpn/zpn_sitec_table.h"
    #include "zpn/zpn_sitec_table_compiled.h"
    #include "zpn/zpn_sitec_group.h"
    #include "zpn/zpn_sitec_group_compiled.h"
    #include "zpn/zpn_sitec_to_group.h"
    #include "zpn/zpn_sitec_to_group_compiled.h"
    #include "zpn/zpn_sitec_version.h"
    #include "zpn/zpn_sitec_version_compiled.h"
    #include "zpn/zpn_scim_group.h"
    #include "zpn/zpn_scim_group_compiled.h"
    #include "zpn/zpn_scim_user.h"
    #include "zpn/zpn_scim_user_compiled.h"
    #include "zpn/zpn_scim_user_group.h"
    #include "zpn/zpn_scim_user_group_compiled.h"
    #include "zpath_lib/zpath_customer.h"
    #include "argo/argo_private.h"
    #include "zpath_lib/zpath_cloud.h"
    #include "zpath_lib/zpath_cloud_compiled.h"
    #include "zpath_lib/et_geoip_override.h"
    #include "zpath_lib/et_geoip_override_compiled.h"
    #include "zpath_lib/zpath_et_wally_userdb.h"
    #include "zpn/zpn_private_broker_load_table.h"
    #include "zpn/zpn_private_broker_load_table_compiled.h"

    struct zpn_sitec_global_state sitec_gs;

    struct zpn_sitec_global_state* zpn_get_sitec_global_state(void)
    {
        /* Return pointer to singleton private broker global state */
        return &sitec_gs;
    }

    extern int zpn_sitec_create_skip_table_for_sync_time_init();
    extern void zpn_sitec_get_database_sync_times(int64_t *master, int64_t *shard, int64_t *userdb);
};

using namespace std;
class LastSyncTimeMock {
    public:
        MOCK_METHOD(int, zpn_sitec_version_get_by_id, (int64_t, struct zpn_site_controller_version**, wally_response_callback_f, void*, int64_t));
};

#define MOCK_ZPN_SITEC_VERSION_GET_BY_ID(T)                                             \
    int zpn_sitec_version_get_by_id(int64_t sitec_gid,                                  \
                                    struct zpn_site_controller_version **sitec_version, \
                                    wally_response_callback_f callback_f,               \
                                    void *callback_cookie,                              \
                                    int64_t callback_id) {                              \
        return T::get_mock()->zpn_sitec_version_get_by_id(sitec_gid, sitec_version, callback_f, callback_cookie, callback_id);                                                  \
    }

class LastSyncTimeMockBase : public testing::StrictMock<LastSyncTimeMock> {};

    // Test fixture
class LastSyncTimeTest : public ZscalerMockBase<LastSyncTimeMockBase>
{
protected:
    struct fohh_connection *f_conn;
    struct wally *userdb_wally;
    struct wally *shard_wally;
    struct wally *master_wally;
    struct wally_fohh_client *wfc;
    struct argo_structure_description *pb_load_desc;
    struct wally_table *pb_load_table;
    struct argo_structure_description *sc_desc;
    struct wally_table *sc_table;
    struct argo_structure_description *sc_version_desc;
    struct wally_table *sc_version_table;
    struct argo_structure_description *sc_group_desc;
    struct wally_table *sc_group_table;
    struct argo_structure_description *sc_to_group_desc;
    struct wally_table *sc_to_group_table;
    struct argo_structure_description *scim_group_desc;
    struct wally_table *scim_group_table;
    struct argo_structure_description *scim_user_desc;
    struct wally_table *scim_user_table;
    struct argo_structure_description *scim_user_group_desc;
    struct wally_table *scim_user_group_table;
    struct argo_structure_description *zpath_cloud_desc;
    struct wally_table *zpath_cloud_table;
    struct argo_structure_description *et_geo_desc;
    struct wally_table *et_geo_table;

public:
    void SetUp() override {
        ZscalerMockBase::SetUp();
        argo_library_init(1024);
        int result;

        result = zpn_sitec_create_skip_table_for_sync_time_init();
        if (result != ZPATH_RESULT_NO_ERROR) {
            fprintf(stdout, "Unable to create skip table\n");
            exit(1);
        }

        userdb_wally = wally_create("userdb_wally", 0, NULL, NULL, NULL, NULL);
        if (!userdb_wally) {
            fprintf(stdout, "Err: Could not create wally\n");
            exit(1);
        }

        shard_wally = wally_create("shard_wally", 0, NULL, NULL, NULL, NULL);
        if (!shard_wally) {
            fprintf(stdout, "Err: Could not create wally\n");
            exit(1);
        }

        master_wally = wally_create("master_wally", 0, NULL, NULL, NULL, NULL);
        if (!master_wally) {
            fprintf(stdout, "Err: Could not create wally\n");
            exit(1);
        }

        pb_load_desc = argo_register_global_structure(ZPN_PRIVATE_BROKER_LOAD_HELPER);
        if (!pb_load_desc) {
            fprintf(stdout, "Err: Could not register pb load structure\n");
            exit(1);
        }

        sc_desc = argo_register_global_structure(ZPN_SITE_CONTROLLER_HELPER);
        if (!sc_desc) {
            fprintf(stdout, "Err: Could not register sc structure\n");
            exit(1);
        }

        sc_version_desc = argo_register_global_structure(ZPN_SITE_CONTROLLER_VERSION_HELPER);
        if (!sc_version_desc) {
            fprintf(stdout, "Err: Could not register sc_version_desc structure\n");
            exit(1);
        }

        sc_group_desc = argo_register_global_structure(ZPN_SITE_CONTROLLER_GROUP_HELPER);
        if (!sc_group_desc) {
            fprintf(stdout, "Err: Could not register sc_group_desc structure\n");
            exit(1);
        }

        sc_to_group_desc = argo_register_global_structure(ZPN_SITE_CONTROLLER_TO_GROUP_HELPER);
        if (!sc_to_group_desc) {
            fprintf(stdout, "Err: Could not register sc_to_group_desc structure\n");
            exit(1);
        }

        scim_group_desc = argo_register_global_structure(ZPN_SCIM_GROUP_HELPER);
        if (!scim_group_desc) {
            fprintf(stdout, "Err: Could not register scim_group_desc structure\n");
            exit(1);
        }

        scim_user_desc = argo_register_global_structure(ZPN_SCIM_USER_HELPER);
        if (!scim_user_desc) {
            fprintf(stdout, "Err: Could not register scim_user_desc structure\n");
            exit(1);
        }

        scim_user_group_desc = argo_register_global_structure(ZPN_SCIM_USER_GROUP_HELPER);
        if (!scim_user_group_desc) {
            fprintf(stdout, "Err: Could not register scim_user_group_desc structure\n");
            exit(1);
        }

        zpath_cloud_desc = argo_register_global_structure(ZPATH_CLOUD_HELPER);
        if (!zpath_cloud_desc) {
            fprintf(stdout, "Err: Could not register zpath_cloud_desc structure\n");
            exit(1);
        }

        et_geo_desc = argo_register_global_structure(ET_GEOIP_OVERRIDE_HELPER);
        if (!et_geo_desc) {
            fprintf(stdout, "Err: Could not register et_geo_desc structure\n");
            exit(1);
        }
        pb_load_table = wally_table_create(shard_wally,
                                        1,
                                        pb_load_desc,
                                        NULL,
                                        NULL,
                                        0,
                                        0,
                                        NULL);
        if (!pb_load_table) {
            fprintf(stdout, "ERR: table_create failed");
            exit(1);
        }

        sc_table = wally_table_create(shard_wally,
                                        1,
                                        sc_desc,
                                        NULL,
                                        NULL,
                                        0,
                                        0,
                                        NULL);
        if (!sc_table) {
            fprintf(stdout, "ERR: table_create failed");
            exit(1);
        }

        sc_group_table = wally_table_create(shard_wally,
                                        1,
                                        sc_group_desc,
                                        NULL,
                                        NULL,
                                        0,
                                        0,
                                        NULL);
        if (!sc_group_table) {
            fprintf(stdout, "ERR: table_create failed");
            exit(1);
        }

        sc_version_table = wally_table_create(shard_wally,
                                        1,
                                        sc_version_desc,
                                        NULL,
                                        NULL,
                                        0,
                                        0,
                                        NULL);
        if (!sc_version_table) {
            fprintf(stdout, "ERR: table_create failed");
            exit(1);
        }

        sc_to_group_table = wally_table_create(shard_wally,
                                        1,
                                        sc_to_group_desc,
                                        NULL,
                                        NULL,
                                        0,
                                        0,
                                        NULL);
        if (!sc_to_group_desc) {
            fprintf(stdout, "ERR: table_create failed");
            exit(1);
        }

        scim_group_table = wally_table_create(userdb_wally,
                                        1,
                                        scim_group_desc,
                                        NULL,
                                        NULL,
                                        0,
                                        0,
                                        NULL);
        if (!scim_group_table) {
            fprintf(stdout, "ERR: table_create failed");
            exit(1);
        }

        scim_user_table = wally_table_create(userdb_wally,
                                        1,
                                        scim_user_desc,
                                        NULL,
                                        NULL,
                                        0,
                                        0,
                                        NULL);
        if (!scim_user_table) {
            fprintf(stdout, "ERR: table_create failed");
            exit(1);
        }

        scim_user_group_table = wally_table_create(userdb_wally,
                                        1,
                                        scim_user_group_desc,
                                        NULL,
                                        NULL,
                                        0,
                                        0,
                                        NULL);
        if (!scim_user_group_table) {
            fprintf(stdout, "ERR: table_create failed");
            exit(1);
        }

        zpath_cloud_table = wally_table_create(master_wally,
                                        1,
                                        zpath_cloud_desc,
                                        NULL,
                                        NULL,
                                        0,
                                        0,
                                        NULL);
        if (!zpath_cloud_table) {
            fprintf(stdout, "ERR: table_create failed");
            exit(1);
        }

        et_geo_table = wally_table_create(master_wally,
                                        1,
                                        et_geo_desc,
                                        NULL,
                                        NULL,
                                        0,
                                        0,
                                        NULL);
        if (!et_geo_table) {
            fprintf(stdout, "ERR: table_create failed");
            exit(1);
        }

        f_conn = new struct fohh_connection;
        memset(f_conn, 0, sizeof(struct fohh_connection));
        wfc = (wally_fohh_client*)ZPN_CALLOC(162720);
        *(uint64_t *)wfc = (uint64_t)userdb_wally;
        f_conn->cookie = wfc;
        f_conn->incarnation = 1;
        f_conn->state = fohh_connection_connected;

        sitec_gs.sitec_state = (struct zpn_sitec_state *)ZPN_CALLOC(sizeof(struct zpn_sitec_state));
        sitec_gs.sitec_state->userdb_wally_conn = f_conn;
        sitec_gs.sitec_state->cfg_wally = shard_wally;
        sitec_gs.sitec_state->ovd_wally = master_wally;
        sitec_gs.sitec_id = 289397352052032163Ull;
    }
    void TearDown() override {
        ZscalerMockBase::TearDown();
    }
};

extern "C" {
    MOCK_ZPN_SITEC_VERSION_GET_BY_ID(LastSyncTimeTest);
}

ACTION_P(SetScVersion, ptr) {
    arg0[0] = ptr;
}

TEST_F(LastSyncTimeTest, GetDatabaseSyncTimeAllTablesZero) {
    int64_t master, shard, userdb;
    struct zpn_site_controller_version *ver = new struct zpn_site_controller_version;
    ver->master_last_sync_time = 1746683469;
    ver->shard_last_sync_time = 1746683459;
    ver->userdb_last_sync_time = 1746683449;

    EXPECT_CALL(*get_mock(), zpn_sitec_version_get_by_id).Times(1).WillOnce(testing::DoAll(testing::WithArg<1>(SetScVersion(ver)), testing::Return(0)));

    zpn_sitec_get_database_sync_times(&master, &shard, &userdb);

    EXPECT_EQ(master, 1746683469);
    EXPECT_EQ(shard, 1746683459);
    EXPECT_EQ(userdb, 1746683449);
}

TEST_F(LastSyncTimeTest, GetDatabaseSyncTime) {
    int64_t master, shard, userdb;
    struct zpn_site_controller_version *ver = new struct zpn_site_controller_version;
    ver->master_last_sync_time = 1746683469;
    ver->shard_last_sync_time = 1746683459;
    ver->userdb_last_sync_time = 1746683449;

    wally_ut_set_last_remote_sync_time(pb_load_table, 1746683460);
    wally_ut_set_last_remote_sync_time(sc_table, 1746683461);
    wally_ut_set_last_remote_sync_time(sc_group_table, 1746683462);
    wally_ut_set_last_remote_sync_time(sc_to_group_table, 1746683463);
    wally_ut_set_last_remote_sync_time(sc_version_table, 1746683464);

    wally_ut_set_last_remote_sync_time(scim_group_table, 1746683465);
    wally_ut_set_last_remote_sync_time(scim_user_table, 1746683466);
    wally_ut_set_last_remote_sync_time(scim_user_group_table, 1746683444);

    wally_ut_set_last_remote_sync_time(zpath_cloud_table, 1746683467);
    wally_ut_set_last_remote_sync_time(et_geo_table, 1746683468);

    EXPECT_CALL(*get_mock(), zpn_sitec_version_get_by_id).Times(1).WillOnce(testing::DoAll(testing::WithArg<1>(SetScVersion(ver)), testing::Return(0)));

    zpn_sitec_get_database_sync_times(&master, &shard, &userdb);

    EXPECT_EQ(master, 1746683468);
    EXPECT_EQ(shard, 1746683463);
    EXPECT_EQ(userdb, 1746683466);
}
