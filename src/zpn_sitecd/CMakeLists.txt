argo_parse_files(
    INPUT_FILES zpn_sitec_sitec_conn.h
    OUTPUT_DIR ${CMAKE_CURRENT_BINARY_DIR}
    OUTPUT_FILES_VAR generated_headers
)

add_library(
    sitec
    STATIC
    zpn_sitec.c
    zpn_sitesp.c
    zpn_sitec_alt_cloud.c
    zpn_sitec_asst_conn.c
    zpn_sitec_broker_conn.c
    zpn_sitec_client_conn.c
    zpn_sitec_pse_conn.c
    zpn_sitec_sitec_conn.c
    zpn_sitec_monitor.c
    zpn_sitec_proxy.c
    zpn_sitec_util.c
    zpn_sitec_siem.c
    ${generated_headers}
)
target_link_libraries(
    sitec
    PUBLIC zpn zpn_waf zpn_enrollment $<$<PLATFORM_ID:Linux>:LibCAP> zpath_app
)

add_executable(zpa-pcc-child zpn_sitecd.c)

target_link_libraries(zpa-pcc-child PRIVATE sitec)

add_rpm(
    NAME zpa-pcc-child
    SPEC rpm/zpa-pcc-child.spec
    MAKEFILE Makefile.rpm.zpa-pcc-child
    FILES
        zpa-pcc-child
        rpm/zpa-pcc-child.service
        rpm/51-zscaler.preset
        rpm/asan.options
        rpm/zpa-pcc-child.conf
        license.txt
)

add_bin(TARGET zpa-pcc-child DEFAULT_BUILD_ONLY DROPDB 1)
add_subdirectory(gtests)
