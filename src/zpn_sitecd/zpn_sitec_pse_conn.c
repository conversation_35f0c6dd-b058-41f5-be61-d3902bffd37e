/*
 * zpn_sitec_pse_conn.c. Copyright (C) 2024 Zscaler, Inc. All Rights Reserved.
 *
 */

#include <stdio.h>
#include <stdarg.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <pthread.h>
#include <sys/time.h>
#include <ctype.h>
#include <errno.h>
#include <fcntl.h>
#ifdef __linux__
#include <malloc.h>
#endif

#include <openssl/rand.h>

#include "zpath_lib/zpath_customer.h"
#include "zpath_lib/zpath_et_wally_userdb.h"
#include "zpn/zpn_lib.h"
#include "fohh/fohh.h"
#include "fohh/fohh_private.h"
#include "zpn_sitecd/zpn_sitec.h"
#include "wally/wally_filter_table.h"
#include "wally/wally_column_nullify.h"
#include "zpn/zpn_signing_cert.h"
#include "zpn/zpn_issuedcert.h"
#include "zpn/zpn_private_broker.h"
#include "zpn/zpn_pbroker_group.h"
#include "zpn/zpn_pbroker_to_group.h"
#include "zpath_lib/zpath_config_override.h"
#include "zpath_lib/zpath_feature_flag_keys.h"
#include "zpn/zpn_broker_client.h"
#include "zpn/zpn_broker_private.h"
#include "zpn/zbalance/zpn_balance_redirect.h"
#include "zpn/zpn_private_broker_table.h"
#include "fohh/fohh_log.h"
#include "zpn_sitecd/zpn_sitec_alt_cloud.h"
#include "zpn_sitecd/zpn_sitec_siem.h"
#include "zpn/zpn_sitec_table.h"
#include "zpn_sitecd/zpn_sitec_broker_conn.h"
#include "zpn/zpn_broker_pbroker.h"
#include "zpn/zpn_pbroker_to_group.h"
#include "zpn/zpn_pbrokergroup_pbroker_relation.h"
#include "zpn/zpn_rpc.h"
#include "zpn/zpn_broker_siem.h"

#define MAX_SITEC_PSE_SNIS 16
#define EXIT_FIREDRILL 1

struct zpn_sitec_sni zpn_sitec_pse_snis[MAX_SITEC_PSE_SNIS];
int zpn_sitec_pse_sni_count = 0;

struct zpn_sitec_pbroker_conn {
    zpath_mutex_t lock;
    struct zhash_table *table;
};

enum sitec_pbroker_config_conn_type {
    sitec_pbroker_config_conn,
    sitec_pbroker_static_config_conn,
    sitec_pbroker_override_conn,
    sitec_pbroker_userdb_conn,
};

typedef struct sitec_pbroker_entry_s {
    LIST_ENTRY(sitec_pbroker_entry_s) list_entry;
    struct fohh_connection *f_conn;
} sitec_pbroker_entry_t;

static LIST_HEAD(sitec_pbroker_list_head, sitec_pbroker_entry_s) g_sitec_pbroker_list = {};
static zpath_mutex_t g_sitec_pbroker_list_lock;

struct connected_sitec_pbroker_control_fohh_state {
    /* Information about the pbroker. */
    /* Allocated: (referenced by auth log) */
    char *log_version;
    char *log_sarge_version;
    char *log_type;
    char *log_dft_rt_intf;
    char *log_platform;
    char *client_type;
    char *close_reason;

    struct fohh_connection *f_conn;
    unsigned received_tcp_info:1;
    struct zpn_sys_auth_log log;
    unsigned int status_report_received;
    unsigned int first_auth_log_sent;

    struct event *timer;

    uint8_t tunnel_id_bin[ZPN_TUNNEL_ID_BYTES];
    char tunnel_id[ZPN_TUNNEL_ID_BYTES_TEXT + 1];
    uint64_t monitor_count;
};

struct connected_sitec_pbroker_config_fohh_state {
    const char *type;
    struct fohh_connection *f_conn;

    struct event *timer;

    uint8_t tunnel_id_bin[ZPN_TUNNEL_ID_BYTES];
    char tunnel_id[ZPN_TUNNEL_ID_BYTES_TEXT + 1];
    uint64_t monitor_count;
};

struct connected_sitec_pbroker_fohh_state {
    struct fohh_connection *f_conn;

    struct event *timer;

    uint8_t tunnel_id_bin[ZPN_TUNNEL_ID_BYTES];
    char tunnel_id[ZPN_TUNNEL_ID_BYTES_TEXT + 1];
    uint64_t monitor_count;
};

struct connected_sitec_pbroker_stats_info {
    struct fohh_connection *f_conn;

    struct event *timer;

    uint8_t tunnel_id_bin[ZPN_TUNNEL_ID_BYTES];
    char tunnel_id[ZPN_TUNNEL_ID_BYTES_TEXT + 1];
    uint64_t monitor_count;

    int64_t pb_gid_from_config;
    int64_t f_conn_incarnation;
    int64_t g_pb_grp;
    int64_t customer_gid;
    uint64_t rx_stats_upload;
};

static struct zpn_sitec_pbroker_conn zpn_sitec_pbroker_control_conn;
/* this contains config/r-config/ovd-config/userdb */
static struct zpn_sitec_pbroker_conn zpn_sitec_pbroker_config_conn;
static struct zpn_sitec_pbroker_conn zpn_sitec_pbroker_load_conn;
static struct zpn_sitec_pbroker_conn zpn_sitec_pbroker_log_conn;
static struct zpn_sitec_pbroker_conn zpn_sitec_pbroker_stats_conn;

static void zpn_sitec_pbroker_conn_tunnel_fohh_store(char *tunnel_id,
                                                     size_t tunnel_id_len,
                                                     void *info,
                                                     struct zpn_sitec_pbroker_conn *sitec_pbroker_conn) {
    ZPATH_MUTEX_LOCK(&sitec_pbroker_conn->lock, __FILE__, __LINE__);
    zhash_table_store(sitec_pbroker_conn->table, tunnel_id, tunnel_id_len, 0, info);
    ZPATH_MUTEX_UNLOCK(&sitec_pbroker_conn->lock, __FILE__, __LINE__);
};

static void zpn_sitec_pbroker_conn_tunnel_fohh_remove(char *tunnel_id, size_t tunnel_id_len, struct zpn_sitec_pbroker_conn *sitec_pbroker_conn) {
    ZPATH_MUTEX_LOCK(&sitec_pbroker_conn->lock, __FILE__, __LINE__);
    void *info = zhash_table_lookup(sitec_pbroker_conn->table, tunnel_id, tunnel_id_len, NULL);
    if (info) {
        zhash_table_remove(sitec_pbroker_conn->table, tunnel_id, tunnel_id_len, NULL);
    }
    ZPATH_MUTEX_UNLOCK(&sitec_pbroker_conn->lock, __FILE__, __LINE__);
};

static void zpn_sitec_pbroker_conn_tx_rx_stats_fill(struct fohh_connection *f_conn,
                                                    struct zpn_sitec_comprehensive_stats *out_data)
{
    uint64_t tx_b = 0;
    uint64_t rx_b = 0;
    uint64_t tx_o = 0;
    uint64_t rx_o = 0;
    uint64_t tx_raw_tlv = 0;
    uint64_t rx_raw_tlv = 0;

    fohh_connection_get_stats(f_conn, &tx_b, &rx_b, &tx_o, &rx_o, &tx_raw_tlv, &rx_raw_tlv);
    out_data->to_pbroker_bytes += tx_b;
    out_data->from_pbroker_bytes += rx_b;
}

static int zpn_sitec_pbroker_control_conn_stats_walk_f(void *cookie,
                                                       void *value,
                                                       void *key,
                                                       size_t key_len)
{
    struct zpn_sitec_comprehensive_stats *out_data = (struct zpn_sitec_comprehensive_stats *) cookie;
    struct connected_sitec_pbroker_control_fohh_state *info = (struct connected_sitec_pbroker_control_fohh_state *) value;
    zpn_sitec_pbroker_conn_tx_rx_stats_fill(info->f_conn, out_data);
    return ZPN_RESULT_NO_ERROR;
}

static int zpn_sitec_pbroker_config_conn_stats_walk_f(void *cookie,
                                                      void *value,
                                                      void *key,
                                                      size_t key_len)
{
    struct zpn_sitec_comprehensive_stats *out_data = (struct zpn_sitec_comprehensive_stats *) cookie;
    struct connected_sitec_pbroker_config_fohh_state *info = (struct connected_sitec_pbroker_config_fohh_state *) value;
    zpn_sitec_pbroker_conn_tx_rx_stats_fill(info->f_conn, out_data);
    return ZPN_RESULT_NO_ERROR;
}

static int zpn_sitec_pbroker_load_conn_stats_walk_f(void *cookie,
                                                    void *value,
                                                    void *key,
                                                    size_t key_len)
{
    struct zpn_sitec_comprehensive_stats *out_data = (struct zpn_sitec_comprehensive_stats *) cookie;
    struct connected_sitec_pbroker_fohh_state *info = (struct connected_sitec_pbroker_fohh_state *) value;
    zpn_sitec_pbroker_conn_tx_rx_stats_fill(info->f_conn, out_data);
    return ZPN_RESULT_NO_ERROR;
}

static int zpn_sitec_pbroker_log_conn_stats_walk_f(void *cookie,
                                                     void *value,
                                                     void *key,
                                                     size_t key_len)
{
    struct zpn_sitec_comprehensive_stats *out_data = (struct zpn_sitec_comprehensive_stats *) cookie;
    struct connected_sitec_pbroker_fohh_state *info = (struct connected_sitec_pbroker_fohh_state *) value;
    zpn_sitec_pbroker_conn_tx_rx_stats_fill(info->f_conn, out_data);
    return ZPN_RESULT_NO_ERROR;
}

static int zpn_sitec_pbroker_stats_conn_stats_walk_f(void *cookie,
                                                     void *value,
                                                     void *key,
                                                     size_t key_len)
{
    struct zpn_sitec_comprehensive_stats *out_data = (struct zpn_sitec_comprehensive_stats *) cookie;
    struct connected_sitec_pbroker_stats_info *info = (struct connected_sitec_pbroker_stats_info *) value;
    zpn_sitec_pbroker_conn_tx_rx_stats_fill(info->f_conn, out_data);
    return ZPN_RESULT_NO_ERROR;
}

void zpn_sitec_pbroker_conn_comprehensive_stats_fill(struct zpn_sitec_comprehensive_stats *out_data,
                                                     struct zpn_sitec_comprehensive_stats *last_comprehensive_stats_data) {
    out_data->active_conn_to_pbroker = zhash_table_get_size(zpn_sitec_pbroker_control_conn.table) +
                                       zhash_table_get_size(zpn_sitec_pbroker_config_conn.table) +
                                       zhash_table_get_size(zpn_sitec_pbroker_load_conn.table) +
                                       zhash_table_get_size(zpn_sitec_pbroker_log_conn.table) +
                                       zhash_table_get_size(zpn_sitec_pbroker_stats_conn.table);

    ZPATH_MUTEX_LOCK(&zpn_sitec_pbroker_control_conn.lock, __FILE__, __LINE__);
    zhash_table_walk(zpn_sitec_pbroker_control_conn.table, NULL, zpn_sitec_pbroker_control_conn_stats_walk_f, out_data);
    ZPATH_MUTEX_UNLOCK(&zpn_sitec_pbroker_control_conn.lock, __FILE__, __LINE__);

    ZPATH_MUTEX_LOCK(&zpn_sitec_pbroker_config_conn.lock, __FILE__, __LINE__);
    zhash_table_walk(zpn_sitec_pbroker_config_conn.table, NULL, zpn_sitec_pbroker_config_conn_stats_walk_f, out_data);
    ZPATH_MUTEX_UNLOCK(&zpn_sitec_pbroker_config_conn.lock, __FILE__, __LINE__);

    ZPATH_MUTEX_LOCK(&zpn_sitec_pbroker_load_conn.lock, __FILE__, __LINE__);
    zhash_table_walk(zpn_sitec_pbroker_load_conn.table, NULL, zpn_sitec_pbroker_load_conn_stats_walk_f, out_data);
    ZPATH_MUTEX_UNLOCK(&zpn_sitec_pbroker_load_conn.lock, __FILE__, __LINE__);

    ZPATH_MUTEX_LOCK(&zpn_sitec_pbroker_log_conn.lock, __FILE__, __LINE__);
    zhash_table_walk(zpn_sitec_pbroker_log_conn.table, NULL, zpn_sitec_pbroker_log_conn_stats_walk_f, out_data);
    ZPATH_MUTEX_UNLOCK(&zpn_sitec_pbroker_log_conn.lock, __FILE__, __LINE__);

    ZPATH_MUTEX_LOCK(&zpn_sitec_pbroker_stats_conn.lock, __FILE__, __LINE__);
    zhash_table_walk(zpn_sitec_pbroker_stats_conn.table, NULL, zpn_sitec_pbroker_stats_conn_stats_walk_f, out_data);
    ZPATH_MUTEX_UNLOCK(&zpn_sitec_pbroker_stats_conn.lock, __FILE__, __LINE__);

    out_data->to_pbroker_bytes_delta = out_data->to_pbroker_bytes - last_comprehensive_stats_data->to_pbroker_bytes;
    out_data->from_pbroker_bytes_delta = out_data->from_pbroker_bytes - last_comprehensive_stats_data->from_pbroker_bytes;
    out_data->to_pbroker_bytes_rate = out_data->to_pbroker_bytes_delta ?
            ((out_data->to_pbroker_bytes_delta * 1000000) / (out_data->cloud_time_us - last_comprehensive_stats_data->cloud_time_us)) : 0;
    out_data->from_pbroker_bytes_rate = out_data->from_pbroker_bytes_delta ?
            ((out_data->from_pbroker_bytes_delta * 1000000) / (out_data->cloud_time_us - last_comprehensive_stats_data->cloud_time_us)) : 0;
}

static int zpn_sitec_cmd_list_pbrokers(struct zpath_debug_state* request_state,
                                       const char **             query_values,
                                       int                       query_value_count,
                                       void*                     cookie)
{
    sitec_pbroker_entry_t *entry, *tmp;

    ZPATH_MUTEX_LOCK(&g_sitec_pbroker_list_lock, __FILE__, __LINE__);
    LIST_FOREACH_SAFE(entry, &g_sitec_pbroker_list, list_entry, tmp) {
        ZDP("%s %s\n", fohh_description(entry->f_conn), fohh_state(entry->f_conn));
    }
    ZPATH_MUTEX_UNLOCK(&g_sitec_pbroker_list_lock, __FILE__, __LINE__);

    return ZPN_RESULT_NO_ERROR;
}

static void pb_auth_log(struct fohh_connection *f_conn, enum fohh_connection_state state);
static void get_pbroker_group(struct fohh_connection *f_conn);

static int get_pbroker_resume(void *response_callback_cookie,
                              struct wally_registrant *registrant,
                              struct wally_table *table,
                              int64_t request_id,
                              int row_count)
{
    struct fohh_connection *f_conn = response_callback_cookie;
    if (fohh_connection_incarnation(f_conn) != request_id) {
        /* Connection changed behind our back */
        return ZPATH_RESULT_NO_ERROR;
    }
    get_pbroker_group(f_conn);
    return ZPATH_RESULT_NO_ERROR;
}

static void get_pbroker_group(struct fohh_connection *f_conn)
{
    struct connected_sitec_pbroker_control_fohh_state *pb_state;
    int res;
    struct zpn_private_broker_to_group *to_group;


    pb_state = fohh_connection_get_dynamic_cookie(f_conn);
    if (pb_state->log.grp_gid) return;

    res = zpn_pbroker_to_group_get_by_private_broker_gid(pb_state->log.gid,
                                                         &to_group,
                                                         1,
                                                         get_pbroker_resume,
                                                         f_conn,
                                                         fohh_connection_incarnation(f_conn));
    if (res == ZPN_RESULT_NO_ERROR) {
        pb_state->log.grp_gid = to_group->private_broker_group_gid;
        SITEC_LOG(AL_NOTICE, "%s: %ld: Retrieved pbroker group as %ld", fohh_description(f_conn), (long) pb_state->log.gid, (long) pb_state->log.grp_gid);

        /* If we have both reveived our first TCP info update as well
         * as figured out the pb group (we are here aren't we??) then
         * send pb auth log */
        if (pb_state->received_tcp_info) {
            pb_auth_log(f_conn, fohh_get_state(f_conn));
        }
    }
}

static int zpn_sitec_pse_init()
{
    int res;

    zpn_sitec_pbroker_control_conn.lock = ZPATH_MUTEX_INIT;
    zpn_sitec_pbroker_control_conn.table = zhash_table_alloc(&zpn_allocator);
    if (!zpn_sitec_pbroker_control_conn.table) {
        SITEC_LOG(AL_ERROR, "Couldn't allocate zpn_sitec_pbroker_control_conn table");
        return ZPN_RESULT_NO_MEMORY;
    }

    zpn_sitec_pbroker_config_conn.lock = ZPATH_MUTEX_INIT;
    zpn_sitec_pbroker_config_conn.table = zhash_table_alloc(&zpn_allocator);
    if (!zpn_sitec_pbroker_config_conn.table) {
        SITEC_LOG(AL_ERROR, "Couldn't allocate zpn_sitec_pbroker_config_conn table");
        return ZPN_RESULT_NO_MEMORY;
    }

    zpn_sitec_pbroker_load_conn.lock = ZPATH_MUTEX_INIT;
    zpn_sitec_pbroker_load_conn.table = zhash_table_alloc(&zpn_allocator);
    if (!zpn_sitec_pbroker_load_conn.table) {
        SITEC_LOG(AL_ERROR, "Couldn't allocate zpn_sitec_pbroker_load_conn table");
        return ZPN_RESULT_NO_MEMORY;
    }

    zpn_sitec_pbroker_log_conn.lock = ZPATH_MUTEX_INIT;
    zpn_sitec_pbroker_log_conn.table = zhash_table_alloc(&zpn_allocator);
    if (!zpn_sitec_pbroker_log_conn.table) {
        SITEC_LOG(AL_ERROR, "Couldn't allocate zpn_sitec_pbroker_log_conn table");
        return ZPN_RESULT_NO_MEMORY;
    }

    zpn_sitec_pbroker_stats_conn.lock = ZPATH_MUTEX_INIT;
    zpn_sitec_pbroker_stats_conn.table = zhash_table_alloc(&zpn_allocator);
    if (!zpn_sitec_pbroker_stats_conn.table) {
        SITEC_LOG(AL_ERROR, "Couldn't allocate zpn_sitec_pbroker_stats_conn table");
        return ZPN_RESULT_NO_MEMORY;
    }

    g_sitec_pbroker_list_lock = ZPATH_MUTEX_INIT;

    res = zpath_debug_add_read_command("list all pse connections",
                                  "/sitec/pbroker/fohh",
                                  zpn_sitec_cmd_list_pbrokers,
                                  NULL,
                                  NULL);
    if (res) {
        SITEC_LOG(AL_NOTICE, "Unable to add command for sending control message to pbrokers");
        return ZPN_RESULT_ERR;
    }

    return ZPN_RESULT_NO_ERROR;
}

void zpn_sitec_add_pse_sni(char *type, char *sni_str, int wildcard)
{
    ZPN_SITEC_ASSERT_HARD(zpn_sitec_pse_sni_count < MAX_SITEC_PSE_SNIS,
                    "sitec add sni: pse snis count exceeded!");
    zpn_sitec_pse_snis[zpn_sitec_pse_sni_count].sni_str = SITEC_STRDUP(sni_str, strlen(sni_str));
    zpn_sitec_pse_snis[zpn_sitec_pse_sni_count].type = SITEC_STRDUP(type, strlen(type));
    zpn_sitec_pse_snis[zpn_sitec_pse_sni_count].wild_card = wildcard;
    zpn_sitec_pse_sni_count++;
}

void zpn_sitec_enable_pse_snis(int enable)
{
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();
    int res;

    for (int count = 0; count < zpn_sitec_pse_sni_count; count++) {
        const char *type = zpn_sitec_pse_snis[count].type;
        const char *sni_str = zpn_sitec_pse_snis[count].sni_str;
        int wild_card = zpn_sitec_pse_snis[count].wild_card;

        res = fohh_generic_server_set_domain_disabled(gs->sni_server, sni_str, wild_card, !enable);

        if (res) {
            SITEC_LOG(AL_ERROR, "Could not %s %s, sni is %s", enable? "enable" : "disable", type, sni_str);
        }
    }
}

void zpn_sitec_update_pse_listeners(struct fohh_generic_server *sni_server,
                                    char *new_offline_domain)
{
    char sni_str[ZPN_MAX_SNI_NAME_LEN + 1];
    int res;

    for (int count = 0; count < zpn_sitec_pse_sni_count; count++) {
        snprintf(sni_str, sizeof(sni_str), "%s.%s", zpn_sitec_pse_snis[count].type, new_offline_domain);

        res = fohh_generic_server_re_register(sni_server,
                                                zpn_sitec_pse_snis[count].sni_str,
                                                sni_str,
                                                zpn_sitec_pse_snis[count].wild_card);
        if (res) {
            SITEC_LOG(AL_ERROR, "Could not re-register client type: %s  generic server for %s to %s",
                                zpn_sitec_pse_snis[count].type, zpn_sitec_pse_snis[count].sni_str, sni_str);
        }

        SITEC_FREE(zpn_sitec_pse_snis[count].sni_str);
        zpn_sitec_pse_snis[count].sni_str = SITEC_STRDUP(sni_str, strlen(sni_str));
    }
}

static void zpn_sitec_pbroker_auth_log(struct fohh_connection *f_conn, struct zpn_sys_auth_log *log, enum fohh_connection_state state)
{
    struct fohh_tcp_info info;
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();

    log->log_date = epoch_us();
    log->g_cst = ZPATH_GID_GET_CUSTOMER_GID(log->gid);
    log->g_brk = 0;
    log->g_sitec = gs->sitec_id;
    log->sitec_id = gs->sitec_id;
    log->client_type = zpn_client_type_string(zpn_client_type_private_broker);
    log->auth_type = zpn_auth_type_string(zpn_tunnel_auth_private_broker);

    if (!log->pub_ip.length) {
        uint16_t pub_port_ne;
        fohh_connection_address_and_port(f_conn, &(log->pub_ip), &pub_port_ne, NULL, NULL);
        log->pub_port = ntohs(pub_port_ne);
    }

    memset(&info, 0, sizeof(info));
    if (fohh_connection_get_tcp_info(f_conn, &info) == FOHH_RESULT_NO_ERROR) {
        log->tcpi_snd_mss = info.tcpi_snd_mss;
        log->tcpi_rcv_mss = info.tcpi_rcv_mss;
        log->tcpi_rtt = info.tcpi_rtt;
        log->tcpi_rttvar = info.tcpi_rttvar;
        log->tcpi_snd_cwnd = info.tcpi_snd_cwnd;
        log->tcpi_advmss = info.tcpi_advmss;
        log->tcpi_reordering = info.tcpi_reordering;
        log->tcpi_rcv_rtt = info.tcpi_rcv_rtt;
        log->tcpi_rcv_space = info.tcpi_rcv_space;
        log->tcpi_total_retrans = info.tcpi_total_retrans;
        log->tcpi_thru_put = info.tcpi_thru_put;
        log->tcpi_unacked = info.tcpi_unacked;
        log->tcpi_sacked = info.tcpi_sacked;
        log->tcpi_lost = info.tcpi_lost;
        log->tcpi_fackets = info.tcpi_fackets;

        log->tcpi_last_data_sent = info.tcpi_last_data_sent;
        log->tcpi_last_ack_sent = info.tcpi_last_ack_sent;
        log->tcpi_last_data_recv = info.tcpi_last_data_recv;
        log->tcpi_last_ack_recv = info.tcpi_last_ack_recv;
        log->tcpi_bytes_acked = info.tcpi_bytes_acked;
        log->tcpi_bytes_received = info.tcpi_bytes_received;
        log->tcpi_segs_out = info.tcpi_segs_out;
        log->tcpi_segs_in = info.tcpi_segs_in;

        if ((info.tcpi_rtt / 1000) > CONTROL_CONN_HIGH_RTT_LOG_THRESHOLD_MS) {
            SITEC_LOG(AL_NOTICE, "%s: Private Broker connection high RTT: %d ms, exceeded RTT threshold: %d ms", fohh_description(f_conn), info.tcpi_rtt / 1000, CONTROL_CONN_HIGH_RTT_LOG_THRESHOLD_MS);
        }
    }

    fohh_connection_get_stats(f_conn,
                              &(log->transmit_bytes),
                              &(log->receive_bytes),
                              &(log->transmit_objects),
                              &(log->receive_objects),
                              &(log->transmit_raw_tlv),
                              &(log->receive_raw_tlv));
    /* Skip quiet app connections */
    if (!fohh_connection_is_quiet(f_conn)) {
        log->app_rtt_us = fohh_connection_get_max_app_rtt(f_conn);
        if (fohh_histogram_get_counts_array(f_conn, FOHH_HISTOGRAM_APP_RTT, log->app_rtt, FOHH_HISTOGRAM_MAX_BUCKETS) == FOHH_RESULT_NO_ERROR) {
            log->app_rtt_count = FOHH_HISTOGRAM_MAX_BUCKETS;
        } else {
            SITEC_LOG(AL_DEBUG, "%s: Unable to get app rtt histogram", fohh_description(f_conn));
        }
    }

    /* FIXME: Do we need to send max value for all TCP_INFO metrics? */
    log->tcp_rtt_us = fohh_connection_get_max_tcp_rtt(f_conn);
    if (fohh_histogram_get_counts_array(f_conn, FOHH_HISTOGRAM_TCP_RTT, log->tcp_rtt, FOHH_HISTOGRAM_MAX_BUCKETS) == FOHH_RESULT_NO_ERROR) {
        log->tcp_rtt_count = FOHH_HISTOGRAM_MAX_BUCKETS;
    } else {
        SITEC_LOG(AL_DEBUG, "%s: Unable to get tcp rtt histogram", fohh_description(f_conn));
    }

    if (fohh_histogram_get_counts_array(f_conn, FOHH_HISTOGRAM_TCP_WIN, log->tcp_congestion_win, FOHH_HISTOGRAM_MAX_BUCKETS) == FOHH_RESULT_NO_ERROR) {
        log->tcp_congestion_win_count = FOHH_HISTOGRAM_MAX_BUCKETS;
    } else {
        SITEC_LOG(AL_DEBUG, "%s: Unable to get tcp congestion win histogram", fohh_description(f_conn));
    }

    if (state == fohh_connection_connected) {
        if (!log->auth_us) log->auth_us = epoch_us();
        log->status = ZPN_STATUS_AUTHENTICATED;
    } else {
        log->deauth_us = epoch_us();
        log->status = ZPN_STATUS_DISCONNECTED;
    }

    log->tlv_type = zpn_tlv_type_str(zpn_fohh_tlv);
    zpn_broker_pbroker_update_pb_lat_lon(log, f_conn);

    /* Log to local for debug purpose if enabled */
    if (zpn_sc_pb_auth_collection) {
        argo_log_structure_immediate(zpn_sc_pb_auth_collection,
                                     argo_log_priority_info,
                                     0,
                                     "sys_auth_log",
                                     zpn_sys_auth_log_description,
                                     log);
    }

    zpn_sitec_siem_pb_auth_log(log);
}

static void pb_auth_log(struct fohh_connection *f_conn, enum fohh_connection_state state)
{
    struct connected_sitec_pbroker_control_fohh_state *pb_state = fohh_connection_get_dynamic_cookie(f_conn);
    struct zpn_sys_auth_log *log = &(pb_state->log);

    if (pb_state->status_report_received == 0) {
        return;
    }

    log->tunnel_type = ZPN_TUNNEL_SITEC_CONTROL;

    zpn_sitec_pbroker_auth_log(f_conn, log, state);

    pb_state->first_auth_log_sent = 1;
}

static void zpn_sitec_pbroker_control_conn_monitor_cb(evutil_socket_t sock, short flags, void *cookie)
{
    int res;
    int64_t customer_id = 0;
    struct zpath_customer *customer = NULL;
    struct zpn_private_broker_group *group = NULL;
    struct zpn_private_broker_to_group *group_mappings;
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();
    struct connected_sitec_pbroker_control_fohh_state *pb_state = cookie;

    if (!pb_state) {
        SITEC_LOG(AL_NOTICE, "Private broker control conn monitor cb has no cookie");
        return;
    }

    pb_state->monitor_count++;

    struct fohh_connection *f_conn = pb_state->f_conn;
    int64_t pbroker_id = fohh_peer_get_id(f_conn);

    /* Drop connection when customer is disabled */
    customer_id = ZPATH_GID_GET_CUSTOMER_GID(fohh_peer_get_id(f_conn));
    if (customer_id != 0) {
        res = zpath_customer_get(customer_id,
                                 &customer,
                                 NULL,
                                 NULL,
                                 0);

        if (res == ZPN_RESULT_NOT_FOUND || (res == ZPN_RESULT_NO_ERROR && customer->disable_traffic == 1)) {
            SITEC_LOG(AL_NOTICE, "%s: Disconnecting pbroker(%"PRId64") as customer(%"PRId64") is not found or is disabled",
                            fohh_description(f_conn), fohh_peer_get_id(f_conn), customer_id);
            /* Kill the connection */
            fohh_connection_delete(f_conn, ZPN_ERR_CUSTOMER_DISABLED);
            return;
        }
    }

    if (gs->disable_flags) {
        SITEC_LOG(AL_ERROR, "%s: %s for %"PRId64,
                fohh_description(f_conn), zpn_sitec_get_disable_flag(), gs->sitec_id);
        fohh_connection_delete(f_conn, ZPN_ERR_SITEC_SERVICE_DISABLED);
        return;
    }

    res = zpn_pbroker_to_group_get_by_private_broker_gid(pbroker_id,
                                                         &group_mappings,
                                                         1,
                                                         NULL,
                                                         NULL,
                                                         0);
    if (res != ZPN_RESULT_NO_ERROR) {
        SITEC_LOG(AL_ERROR, "%s: Error fetching pbroker_to_group data for pbroker id: %ld: %s", fohh_peer_cn(f_conn), (long) pbroker_id, zpn_result_string(res));
        return;
    }

    res = zpn_pbroker_group_get_by_gid(group_mappings->private_broker_group_gid,
                                       &group,
                                       1,
                                       NULL,
                                       NULL,
                                       0);
    if (res != ZPN_RESULT_NO_ERROR) {
        SITEC_LOG(AL_ERROR, "%s: Error fetching pbroker group data for group id: %ld: %s", fohh_peer_cn(f_conn), (long) group_mappings->private_broker_group_gid, zpn_result_string(res));
        return;
    }

    if (group->site_gid != gs->site_gid) {
        SITEC_LOG(AL_WARNING, "%s: sitec %"PRId64" and pbroker %"PRId64" are part of different sites, hence disconnecting", fohh_description(f_conn), gs->sitec_id, pbroker_id);
        /* Kill the connection */
        fohh_connection_delete(f_conn, ZPN_ERR_SITE_MISMATCH);
        return;
    }

    if ((pb_state->monitor_count % ZPN_TUNNEL_MONITOR_LOG_INTERVAL_COUNT) == 0) {
        pb_auth_log(f_conn, fohh_get_state(f_conn));
    }
}

static void cleanup_sys_auth_log_brokers_unique(struct zpn_sys_auth_log *log) {
    if (log->log_brokers_uniq) {
        for (int i = 0; i < log->log_brokers_uniq_count; ++i) {
            SITEC_FREE(log->log_brokers_uniq[i]);
        }
        SITEC_FREE(log->log_brokers_uniq);
        log->log_brokers_uniq = NULL;
        log->log_brokers_uniq_count = 0;
    }
}

static void cleanup_sys_auth_log_all_log_brokers_data(struct zpn_sys_auth_log *log) {
    if (log->all_log_brokers_data) {
        for (int i = 0; i < log->all_log_brokers_data_count; ++i) {
            SITEC_FREE(log->all_log_brokers_data[i]);
            log->all_log_brokers_data[i] = NULL;
        }
        SITEC_FREE(log->all_log_brokers_data);
        log->all_log_brokers_data = NULL;
        log->all_log_brokers_data_count = 0;
    }
}

static int zpn_sitec_pbroker_status_report_cb(void *argo_cookie_ptr,
                                              void *argo_structure_cookie_ptr,
                                              struct argo_object *object)
{
    struct zpn_pbroker_status_report *pbreport = object->base_structure_void;
    struct fohh_connection *f_conn = argo_structure_cookie_ptr;
    struct connected_sitec_pbroker_control_fohh_state *pb_state;
    struct zpn_sys_auth_log *log;
    if (zpn_debug_get(ZPN_DEBUG_SITE_CONTROLLER_IDX)) {
        char buf[2000];
        if (argo_object_dump(object, buf, sizeof(buf), NULL, 0) == ARGO_RESULT_NO_ERROR) {
            ZPN_DEBUG_SITEC("%s: %ld: %s", fohh_description(f_conn), (long)fohh_peer_get_id(f_conn), buf);
        }
    }

    pb_state = fohh_connection_get_dynamic_cookie(f_conn);
    if (!pb_state) {
        SITEC_LOG(AL_NOTICE, "Failed to retrieve pb state from control connection");
        return ZPATH_RESULT_NO_ERROR;
    }
    log = &(pb_state->log);
    log->cpu_util = pbreport->cpu_util;
    log->mem_util =  pbreport->mem_util;
    log->udp4_port_util = pbreport->udp4_port_util;
    log->udp6_port_util = pbreport->udp6_port_util;
    log->sys_fd_util = pbreport->sys_fd_util;
    log->proc_fd_util = pbreport->proc_fd_util;

    log->vm_start_s = pbreport->sys_uptime_s;
    log->app_start_s = pbreport->pb_uptime_s;

    if (log->dft_rt_intf) {
        SITEC_FREE(log->dft_rt_intf);
        log->dft_rt_intf = NULL;
    }
    if (pbreport->dft_rt_intf) {
        log->dft_rt_intf = SITEC_STRDUP(pbreport->dft_rt_intf, strlen(pbreport->dft_rt_intf));
    }

    log->dft_rt_gw = pbreport->dft_rt_gw;
    log->resolver = pbreport->resolver;

    log->intf_count = pbreport->intf_count;
    log->intf_rb = pbreport->intf_rb;
    log->intf_rp = pbreport->intf_rp;
    log->intf_re = pbreport->intf_re;
    log->intf_rd = pbreport->intf_rd;
    log->intf_tb = pbreport->intf_tb;
    log->intf_tp = pbreport->intf_tp;
    log->intf_te = pbreport->intf_te;
    log->intf_td = pbreport->intf_td;

    log->delta_intf_rb = pbreport->delta_intf_rb;
    log->delta_intf_rp = pbreport->delta_intf_rp;
    log->delta_intf_re = pbreport->delta_intf_re;
    log->delta_intf_rd = pbreport->delta_intf_rd;
    log->delta_intf_tb = pbreport->delta_intf_tb;
    log->delta_intf_tp = pbreport->delta_intf_tp;
    log->delta_intf_te = pbreport->delta_intf_te;
    log->delta_intf_td = pbreport->delta_intf_td;

    log->total_intf_b = pbreport->total_intf_b;
    log->total_intf_p = pbreport->total_intf_p;
    log->total_intf_e = pbreport->total_intf_e;
    log->total_intf_d = pbreport->total_intf_d;

    log->delta_total_intf_b = pbreport->delta_total_intf_b;
    log->delta_total_intf_p = pbreport->delta_total_intf_p;
    log->delta_total_intf_e = pbreport->delta_total_intf_e;
    log->delta_total_intf_d = pbreport->delta_total_intf_d;
    zpn_broker_pbroker_update_pb_lat_lon(log, f_conn);

    snprintf(log->ovd_broker_cn, sizeof(log->ovd_broker_cn), "%s", pbreport->ovd_broker_cn);

    cleanup_sys_auth_log_brokers_unique(log);
    if (pbreport->log_brokers_uniq_count) {
        log->log_brokers_uniq = SITEC_CALLOC(sizeof(char*)* pbreport->log_brokers_uniq_count);
        int ii = 0;
        for (ii = 0; ii < pbreport->log_brokers_uniq_count; ii++) {
            log->log_brokers_uniq[ii] = SITEC_STRDUP(pbreport->log_brokers_uniq[ii], strlen(pbreport->log_brokers_uniq[ii]));
        }
        log->log_brokers_uniq_count = pbreport->log_brokers_uniq_count;
    }

    cleanup_sys_auth_log_all_log_brokers_data(log);
    if (pbreport->all_log_brokers_data_count) {
        log->all_log_brokers_data = SITEC_CALLOC(sizeof(char*)* pbreport->all_log_brokers_data_count);
        int ii = 0;
        for (ii = 0; ii < pbreport->all_log_brokers_data_count; ii++) {
            log->all_log_brokers_data[ii] = SITEC_STRDUP(pbreport->all_log_brokers_data[ii], strlen(pbreport->all_log_brokers_data[ii]));
        }
        log->all_log_brokers_data_count = pbreport->all_log_brokers_data_count;
    }

    pb_state->status_report_received = 1;
    if (pb_state->first_auth_log_sent == 0) {
        pb_auth_log(f_conn, fohh_get_state(f_conn));
    }
    return ZPATH_RESULT_NO_ERROR;
}

static int zpn_sitec_pbroker_environment_report_cb(void *argo_cookie_ptr,
                                                   void *argo_structure_cookie_ptr,
                                                   struct argo_object *object)
{
    struct zpn_pbroker_environment_report *report = object->base_structure_void;
    if (!report->sarge_version)
        goto done;

    struct fohh_connection *f_conn = argo_structure_cookie_ptr;
    struct connected_sitec_pbroker_control_fohh_state *pb_state = fohh_connection_get_dynamic_cookie(f_conn);
    pb_state->log_sarge_version = SITEC_STRDUP(report->sarge_version, strlen(report->sarge_version));
    pb_state->log.sarge_version = pb_state->log_sarge_version;

done:
    return ZPATH_RESULT_NO_ERROR;
}

static int zpn_sitec_pbroker_tcp_info_report_cb(void *argo_cookie_ptr,
                                                void *argo_structure_cookie_ptr,
                                                struct argo_object *object)
{
    struct fohh_connection *f_conn = argo_structure_cookie_ptr;
    struct connected_sitec_pbroker_control_fohh_state *pb_state = fohh_connection_get_dynamic_cookie(f_conn);
    struct zpn_tcp_info_report *req = object->base_structure_void;

    /* Do nothing for now, we will use this later */
    SITEC_LOG(AL_DEBUG, "Static Wally Capability string connection = %p value = %d", f_conn, req->static_wally_enabled);

    /* Only thing we are grabbing at the moment from the tcp_info
       state is the private IP and version */
    pb_state->log.priv_ip = req->priv_ip;
    if (pb_state->log_version) {
        SITEC_FREE(pb_state->log_version);
    }

    pb_state->log_version = SITEC_STRDUP(req->version, strlen(req->version));
    pb_state->log.version = pb_state->log_version;

    if (pb_state->log.platform) {
        SITEC_FREE(pb_state->log.platform);
        pb_state->log.platform = NULL;
    }

    if (pb_state->log.platform_detail) {
        SITEC_FREE(pb_state->log.platform_detail);
        pb_state->log.platform_detail = NULL;
    }

    if (pb_state->log.runtime_os) {
        SITEC_FREE(pb_state->log.runtime_os);
        pb_state->log.runtime_os = NULL;
    }

    if(pb_state->log.geoip_version) {
        SITEC_FREE(pb_state->log.geoip_version);
        pb_state->log.geoip_version = NULL;
    }

    if(pb_state->log.isp_version) {
        SITEC_FREE(pb_state->log.isp_version);
        pb_state->log.isp_version = NULL;
    }

    char *platform = req->platform ? req->platform : "UNKNOWN";
    pb_state->log.platform = SITEC_STRDUP(platform, strlen(platform));

    char *geoip_version = req->geoip_version ? req->geoip_version : "\0";
    char *isp_version = req->isp_version ? req->isp_version : "\0";
    pb_state->log.geoip_version = SITEC_STRDUP(geoip_version, strlen(geoip_version));
    pb_state->log.isp_version = SITEC_STRDUP(isp_version, strlen(isp_version));

    const char *platform_detail = req->platform_detail ? req->platform_detail : "UNKNOWN";
    pb_state->log.platform_detail = SITEC_STRDUP(platform_detail, strlen(platform_detail));

    const char *runtime_os = req->runtime_os ? req->runtime_os : "Unknown";
    pb_state->log.runtime_os = SITEC_STRDUP(runtime_os, strnlen(runtime_os, FOHH_MAX_NAMELEN));

    /* If we have already figured out private broker group, then send an auth log */
    if (!pb_state->received_tcp_info) {
        pb_state->received_tcp_info = 1;
        if (pb_state->log.grp_gid) {
            pb_auth_log(f_conn, fohh_get_state(f_conn));
        }
    }
    return ZPATH_RESULT_NO_ERROR;
}

static int zpn_sitec_pbroker_conn_callback(struct fohh_connection *connection,
                                            enum fohh_connection_state state,
                                            void *cookie)
{
    int res;
    int64_t pbroker_id;
    int64_t customer_gid;
    struct connected_sitec_pbroker_control_fohh_state *pb_state;
    struct zpn_private_broker *pbroker = NULL;
    struct zpath_customer *customer = NULL;

    if (state == fohh_connection_connected) {
        sitec_pbroker_entry_t *entry = SITEC_CALLOC(sizeof(sitec_pbroker_entry_t));
        struct argo_state *argo = fohh_argo_get_rx(connection);

        pbroker_id = fohh_peer_get_id(connection);

        SITEC_LOG(AL_DEBUG, "%s: Received pbroker control connection", fohh_description(connection));

        if (entry) {
            ZPATH_MUTEX_LOCK(&g_sitec_pbroker_list_lock, __FILE__, __LINE__);
            entry->f_conn = connection;
            LIST_INSERT_HEAD(&g_sitec_pbroker_list, entry, list_entry);
            ZPATH_MUTEX_UNLOCK(&g_sitec_pbroker_list_lock, __FILE__, __LINE__);
        } else {
            SITEC_LOG(AL_ERROR, "%s: Cannot allocate pbroker entry", fohh_description(connection));
        }

        /* Drop connection when customer is disabled */
        customer_gid = ZPATH_GID_GET_CUSTOMER_GID(pbroker_id);
        if (customer_gid != 0) {
            res = zpath_customer_get(customer_gid,
                                     &customer,
                                     NULL,
                                     NULL,
                                     0);

            if (res == ZPN_RESULT_NOT_FOUND || (res == ZPN_RESULT_NO_ERROR && customer->disable_traffic == 1)) {
                SITEC_LOG(AL_ERROR,"%s: Could not accept connection with pbroker(%"PRId64"), customer(%"PRId64") is not found or is disabled",
                            fohh_description(connection), pbroker_id, customer_gid);
                return ZPN_RESULT_ERR;
            }
        }

        if ((res = argo_register_structure(argo, zpn_pbroker_status_report_description, zpn_sitec_pbroker_status_report_cb, connection))) {
            SITEC_LOG(AL_ERROR, "Could not register zpn_pbroker_status_report for private broker for %s", fohh_description(connection));
            return res;
        }
        if ((res = argo_register_structure(argo, zpn_pbroker_environment_report_description, zpn_sitec_pbroker_environment_report_cb, connection))) {
            SITEC_LOG(AL_ERROR, "Could not register zpn_pbroker_environment_report for private broker for %s", fohh_description(connection));
            return res;
        }
        if ((res = argo_register_structure(argo, zpn_tcp_info_report_description, zpn_sitec_pbroker_tcp_info_report_cb, connection))) {
            SITEC_LOG(AL_ERROR, "Could not register zpn_tcp_info_report for connection %s", fohh_description(connection));
            return res;
        }

        pb_state = SITEC_CALLOC(sizeof(*pb_state));
        fohh_connection_set_dynamic_cookie(connection, pb_state);

        /* Generate an ID for this client */
        res = RAND_bytes(&(pb_state->tunnel_id_bin[0]), ZPN_TUNNEL_ID_BYTES);
        if (1 != res) {
            ZPN_SITEC_ASSERT_HARD(0, "Crypto Random Generator not seeded!");
            SITEC_FREE(pb_state);
            return FOHH_RESULT_ERR;
        }

        base64_encode_binary(pb_state->tunnel_id, pb_state->tunnel_id_bin, ZPN_TUNNEL_ID_BYTES);
        pb_state->f_conn = connection;
        pb_state->log.gid = pbroker_id;
        pb_state->log.g_cst = ZPATH_GID_GET_CUSTOMER_GID(pb_state->log.gid);
        pb_state->log.tunnel_id = pb_state->tunnel_id;

        /*
         * Fetch pbroker from zpn_private_broker table. Note:- this is
         * prefetched during authentication, so should be
         * available directly
         */
        res = zpn_private_broker_get_by_id(fohh_peer_get_id(connection),
                                           &pbroker,
                                           NULL,
                                           NULL,
                                           0);
        if (res) {
            SITEC_LOG(AL_WARNING, "Error fetching pbroker %ld: %s", (long) fohh_peer_get_id(connection), zpn_result_string(res));
        } else {
            pb_state->log.g_microtenant = is_scope_default(pbroker->scope_gid) ? 0 : pbroker->scope_gid;
        }

        get_pbroker_group(connection);
        pb_state->timer = event_new(zevent_event_base(zevent_self()),
                                    -1,
                                    EV_PERSIST,
                                    zpn_sitec_pbroker_control_conn_monitor_cb,
                                    pb_state);
        if (!pb_state->timer) {
            SITEC_LOG(AL_CRITICAL, "Memory");
            SITEC_FREE(pb_state);
            return FOHH_RESULT_NO_MEMORY;
        }

        struct timeval tv;
        tv.tv_sec = ZPN_TUNNEL_MONITOR_INTERVAL_S;
        tv.tv_usec = 0;
        if (event_add(pb_state->timer, &tv)) {
            SITEC_LOG(AL_CRITICAL, "Could not add pbroker control timer");
            event_free(pb_state->timer);
            SITEC_FREE(pb_state);
            return FOHH_RESULT_NO_MEMORY;
        }

        zpn_sitec_pbroker_conn_tunnel_fohh_store(pb_state->tunnel_id, ZPN_TUNNEL_ID_BYTES_TEXT, pb_state, &zpn_sitec_pbroker_control_conn);
    } else {
        /* Connection probably went away... */
        const char *reason = fohh_close_reason(connection);
        SITEC_LOG(AL_DEBUG, "%s: PBroker control connection DOWN: %s", fohh_description(connection), reason);

        sitec_pbroker_entry_t *entry, *tmp;
        ZPATH_MUTEX_LOCK(&g_sitec_pbroker_list_lock, __FILE__, __LINE__);
        LIST_FOREACH_SAFE(entry, &g_sitec_pbroker_list, list_entry, tmp) {
            if (entry->f_conn == connection) {
                LIST_REMOVE(entry, list_entry);
                SITEC_FREE(entry);
            }
        }
        ZPATH_MUTEX_UNLOCK(&g_sitec_pbroker_list_lock, __FILE__, __LINE__);

        pb_state = fohh_connection_get_dynamic_cookie(connection);
        if (pb_state) {
            pb_state->log.close_reason = reason;
            pb_auth_log(connection, fohh_connection_disconnected);

            fohh_connection_set_dynamic_cookie(connection, NULL);
            if (pb_state->log_version) {
                SITEC_FREE(pb_state->log_version);
                pb_state->log_version = NULL;
            }
            if (pb_state->log_sarge_version) {
                SITEC_FREE(pb_state->log_sarge_version);
                pb_state->log_sarge_version = NULL;
            }
            if (pb_state->log.platform) {
                SITEC_FREE(pb_state->log.platform);
                pb_state->log.platform = NULL;
            }
            if (pb_state->log.platform_detail) {
                SITEC_FREE(pb_state->log.platform_detail);
                pb_state->log.platform_detail = NULL;
            }
            if (pb_state->log.runtime_os) {
                SITEC_FREE(pb_state->log.runtime_os);
                pb_state->log.runtime_os = NULL;
            }

            if(pb_state->log.geoip_version) {
                SITEC_FREE(pb_state->log.geoip_version);
                pb_state->log.geoip_version = NULL;
            }

            if(pb_state->log.isp_version) {
                SITEC_FREE(pb_state->log.isp_version);
                pb_state->log.isp_version = NULL;
            }

            if (pb_state->timer) {
                event_del(pb_state->timer);
                event_free(pb_state->timer);
                pb_state->timer = NULL;
            }

            if (pb_state->log.cc) {
                SITEC_FREE(pb_state->log.cc);
                pb_state->log.cc = NULL;
            }

            if (pb_state->log.dft_rt_intf) {
                SITEC_FREE(pb_state->log.dft_rt_intf);
                pb_state->log.dft_rt_intf = NULL;
            }

            cleanup_sys_auth_log_brokers_unique(&pb_state->log);

            cleanup_sys_auth_log_all_log_brokers_data(&pb_state->log);

            if (pb_state->log.slogger_info) {
                SITEC_FREE(pb_state->log.slogger_info);
                pb_state->log.slogger_info = NULL;
            }

            zpn_sitec_pbroker_conn_tunnel_fohh_remove(pb_state->tunnel_id, ZPN_TUNNEL_ID_BYTES_TEXT, &zpn_sitec_pbroker_control_conn);
            SITEC_FREE(pb_state);
        }

        return FOHH_RESULT_NO_ERROR;
    }
    return FOHH_RESULT_NO_ERROR;
}

static int zpn_sitec_pbroker_unblock_callback(struct fohh_connection *connection,
                                               enum fohh_queue_element_type element_type,
                                               void *cookie)
{
    return FOHH_RESULT_NO_ERROR;
}


static void zpn_sitec_pbroker_config_conn_monitor_cb(evutil_socket_t sock, short flags, void *cookie)
{
    int res;
    int64_t customer_id = 0;
    struct zpath_customer *customer = NULL;
    struct zpn_private_broker_group *group = NULL;
    struct zpn_private_broker_to_group *group_mappings;
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();

    struct connected_sitec_pbroker_config_fohh_state *pb_state = cookie;
    if (!pb_state) {
        SITEC_LOG(AL_NOTICE, "Private broker config conn monitor cb has no cookie");
        return;
    }

    pb_state->monitor_count++;

    struct fohh_connection *f_conn = pb_state->f_conn;

    int64_t pbroker_id = fohh_peer_get_id(f_conn);

    /* Drop connection when customer is disabled */
    customer_id = ZPATH_GID_GET_CUSTOMER_GID(pbroker_id);
    if (customer_id != 0) {
        res = zpath_customer_get(customer_id,
                                 &customer,
                                 NULL,
                                 NULL,
                                 0);

        if (res == ZPN_RESULT_NOT_FOUND || (res == ZPN_RESULT_NO_ERROR && customer->disable_traffic == 1)) {
            SITEC_LOG(AL_NOTICE, "%s: Disconnecting pbroker(%"PRId64") as customer(%"PRId64") is not found or is disabled",
                            fohh_description(f_conn), pbroker_id, customer_id);
            /* Kill the connection */
            fohh_connection_delete(f_conn, ZPN_ERR_CUSTOMER_DISABLED);
            return;
        }
    }

    if (gs->disable_flags) {
        SITEC_LOG(AL_ERROR, "%s: %s for %"PRId64,
                fohh_description(f_conn), zpn_sitec_get_disable_flag(), gs->sitec_id);
        fohh_connection_delete(f_conn, ZPN_ERR_SITEC_SERVICE_DISABLED);
        return;
    }

    res = zpn_pbroker_to_group_get_by_private_broker_gid(pbroker_id,
                                                         &group_mappings,
                                                         1,
                                                         NULL,
                                                         NULL,
                                                         0);
    if (res != ZPN_RESULT_NO_ERROR) {
        SITEC_LOG(AL_ERROR, "%s: Error fetching pbroker_to_group data for pbroker id: %ld: %s", fohh_peer_cn(f_conn), (long) pbroker_id, zpn_result_string(res));
        return;
    }

    res = zpn_pbroker_group_get_by_gid(group_mappings->private_broker_group_gid,
                                       &group,
                                       1,
                                       NULL,
                                       NULL,
                                       0);
    if (res != ZPN_RESULT_NO_ERROR) {
        SITEC_LOG(AL_ERROR, "%s: Error fetching pbroker group data for group id: %ld: %s", fohh_peer_cn(f_conn), (long) group_mappings->private_broker_group_gid, zpn_result_string(res));
        return;
    }

    if (group->site_gid != gs->site_gid) {
        SITEC_LOG(AL_WARNING, "%s: sitec %"PRId64" and pbroker %"PRId64" are part of different sites, hence disconnecting", fohh_description(f_conn), gs->sitec_id, pbroker_id);
        /* Kill the connection */
        fohh_connection_delete(f_conn, ZPN_ERR_SITE_MISMATCH);
        return;
    }
}

static int zpn_sitec_pbroker_create_config_conn_cookie(struct fohh_connection *f_conn,
                                                        enum sitec_pbroker_config_conn_type type)
{
    static const char *conn_type_text[] = {
        "config",
        "static_config",
        "override",
        "userdb",
    };

    struct connected_sitec_pbroker_config_fohh_state *pb_state = SITEC_CALLOC(sizeof(*pb_state));

    /* Generate an ID for this client */
    const int res = RAND_bytes(&(pb_state->tunnel_id_bin[0]), ZPN_TUNNEL_ID_BYTES);
    if (1 != res) {
        ZPN_SITEC_ASSERT_HARD(0, "Crypto Random Generator not seeded!");
        SITEC_FREE(pb_state);
        return FOHH_RESULT_ERR;
    }
    base64_encode_binary(pb_state->tunnel_id, pb_state->tunnel_id_bin, ZPN_TUNNEL_ID_BYTES);

    pb_state->timer = event_new(zevent_event_base(zevent_self()),
                                -1,
                                EV_PERSIST,
                                zpn_sitec_pbroker_config_conn_monitor_cb,
                                pb_state);
    if (!pb_state->timer) {
        SITEC_LOG(AL_CRITICAL, "Memory");
        SITEC_FREE(pb_state);
        return FOHH_RESULT_NO_MEMORY;
    }

    struct timeval tv;
    tv.tv_sec = ZPN_TUNNEL_MONITOR_INTERVAL_S;
    tv.tv_usec = 0;
    if (event_add(pb_state->timer, &tv)) {
        SITEC_LOG(AL_CRITICAL, "Could not add pbroker config timer");
        event_free(pb_state->timer);
        SITEC_FREE(pb_state);
        return FOHH_RESULT_NO_MEMORY;
    }

    pb_state->type = conn_type_text[type];
    pb_state->f_conn = f_conn;
    zpn_sitec_pbroker_conn_tunnel_fohh_store(pb_state->tunnel_id, ZPN_TUNNEL_ID_BYTES_TEXT, pb_state, &zpn_sitec_pbroker_config_conn);
    fohh_connection_set_dynamic_cookie(f_conn, pb_state);
    return FOHH_RESULT_NO_ERROR;
}

static void zpn_sitec_pbroker_destroy_config_conn_cookie(struct fohh_connection *f_conn)
{
    struct connected_sitec_pbroker_config_fohh_state *pb_state = fohh_connection_get_dynamic_cookie(f_conn);
    if (pb_state) {
        if (pb_state->timer) {
            event_free(pb_state->timer);
        }
        zpn_sitec_pbroker_conn_tunnel_fohh_remove(pb_state->tunnel_id, ZPN_TUNNEL_ID_BYTES_TEXT, &zpn_sitec_pbroker_config_conn);
        fohh_connection_set_dynamic_cookie(f_conn, NULL);
        SITEC_FREE(pb_state);
    }
}

static int zpn_sitec_pbroker_config_conn_callback(struct fohh_connection *connection,
                                                   enum fohh_connection_state state,
                                                   void *cookie)
{
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();

    SITEC_LOG(AL_DEBUG, "%s: Config conn callback. Setting up filter tables", fohh_description(connection));
    static char *allow_tables[] =
        {
         "zpn_rule_condition_operand",
         "zpn_rule_condition_set",
         "zpn_rule_to_server_group",
         "zpn_rule_to_assistant_group",
         "zpn_client",
         "zpn_issuedcert",
         "zpn_private_broker_group",
         "zpn_private_broker_to_group",
         "zpn_machine",
         "zpn_machine_group",
         "zpn_machine_to_group",
         "zpn_inspection_application",
         "zpn_approval",
         "zpn_approval_mapping",
         "zpn_location"
        };
    static int allow_tables_count = sizeof(allow_tables) / sizeof(allow_tables[0]);

    static char *filter_tables[] =
        {
         "zpath_customer",
         "zpn_app_group_relation",
         "zpn_app_server",
         "zpn_application",
         "zpn_application_domain",
         "zpn_application_group",
         "zpn_application_group_application_mapping",
         "zpn_assistant",
         "zpn_assistant_group",
         "zpn_assistant_version",
         "zpn_assistantgroup_assistant_relation",
         "zpn_customer_config",
         "zpn_c2c_client_registration",
         "zpn_c2c_ip_ranges",
         "zpn_idp",
         "zpn_idp_cert",
         "zpn_inspection_application",
         "zpn_approval",
         "zpn_approval_mapping",
         "zpn_policy_set",
         "zpn_posture_profile_db",
         "zpn_private_broker",
         "zpn_private_broker_load",
         "zpn_private_broker_version",
         "zpn_step_up_auth_level",
         "zpn_rule_to_step_up_auth_level_mapping",
         "zpn_sub_module_upgrade",
         "zpn_customer_resiliency_settings",
         "zpn_privatebrokergroup_trustednetwork_mapping",
         "zpn_rule",
         "zpn_rule_to_pse_group",
         "zpn_saml_attrs",
         "zpn_scim_attr_header",
         "zpn_server_group",
         "zpn_server_group_assistant_group",
         "zpn_servergroup_server_relation",
         "zpn_shared_customer_domain",
         "zpn_signing_cert",
         "zpn_trusted_network",
         "zpn_znf",
         "zpn_znf_to_group",
         "zpn_znf_group",
         "zpn_command_probe",
         "zpn_scope",
         "zpn_branch_connector",
         "zpn_branch_connector_to_group",
         "zpn_branch_connector_group",
         "zpn_svcp_profile",
         "zpn_site",
         "zpn_site_controller",
         "zpn_site_controller_group",
         "zpn_site_controller_to_group",
         "zpn_ddil_config",
         "zpn_workload_tag_group",
         "zpn_firedrill_site"
        };
    static int filter_tables_count = sizeof(filter_tables) / sizeof(filter_tables[0]);

    if (state == fohh_connection_connected) {
        int res;
        struct zpath_customer *customer = NULL;
        /* Allocate state for this connection, so we can track its stuff... */
        int64_t gid = fohh_peer_get_id(connection);
        int64_t customer_gid = ZPATH_GID_MASK_CUSTOMER & gid;

        /* Drop connection when customer is disabled */
        if (customer_gid != 0) {
            res = zpath_customer_get(customer_gid,
                                     &customer,
                                     NULL,
                                     NULL,
                                     0);

            if (res == ZPN_RESULT_NOT_FOUND || (res == ZPN_RESULT_NO_ERROR && customer->disable_traffic == 1)) {
                SITEC_LOG(AL_NOTICE, "%s: Could not accept connection with pbroker(%"PRId64"), customer(%"PRId64") is not found or is disabled", fohh_description(connection), gid, customer_gid);
                return FOHH_RESULT_ERR;
            }
        }

        struct filter_entry entries[] = { /* TABLE NAME                     COL NAME        KEY             KEYSIZE  */
                                          {"zpn_rule_condition_operand",    "customer_gid", &customer_gid, sizeof(int64_t)},
                                          {"zpn_rule_condition_set",        "customer_gid", &customer_gid, sizeof(int64_t)},
                                          {"zpn_client",                    "customer_gid", &customer_gid, sizeof(int64_t)},
                                          {"zpn_issuedcert",                "customer_gid", &customer_gid, sizeof(int64_t)},
                                          {"zpn_private_broker_to_group",   "private_broker_gid", &gid, sizeof(int64_t)},
                                          {"zpn_private_broker_to_group",   "customer_gid",  &customer_gid, sizeof(int64_t)},
                                          {"zpn_private_broker_group",      "customer_gid",  &customer_gid, sizeof(int64_t)},
                                          {"zpn_inspection_application",    "customer_gid",  &customer_gid, sizeof(int64_t)},
                                          {"zpn_approval",                  "customer_gid",  &customer_gid, sizeof(int64_t)},
                                          {"zpn_approval_mapping",          "customer_gid",  &customer_gid, sizeof(int64_t)},
                                          {"zpn_location",                  "customer_gid",  &customer_gid, sizeof(int64_t)}
                                        };
        struct zhash_table* table_filter = NULL;

        if (add_multiple_filter_keys(entries, sizeof(entries)/sizeof(struct filter_entry), &table_filter) != WALLY_RESULT_NO_ERROR) {
            SITEC_LOG(AL_ERROR, "Failed to add filter table entries");
            return FOHH_RESULT_ERR;
        }

        if (!wally_fohh_create_fohh_from_client(gs->sitec_state->cfg_wally_server,
                                                connection,
                                                allow_tables,
                                                allow_tables_count,
                                                filter_tables,
                                                filter_tables_count,
                                                ZPATH_GID_MASK_CUSTOMER,
                                                ZPATH_GID_MASK_CUSTOMER & gid,
                                                0,
                                                table_filter,
                                                NULL,
                                                zpn_sitec_pbroker_config_conn_callback)) {
            SITEC_LOG(AL_ERROR, "Could not hand off FOHH to wally for pbroker config: %s", fohh_description(connection));
            return FOHH_RESULT_ERR;
        }


        res = zpn_sitec_pbroker_create_config_conn_cookie(connection, sitec_pbroker_config_conn);
        if (res) {
            SITEC_LOG(AL_ERROR, "Failed to create config connection cookie");
            return res;
        }

        SITEC_LOG(AL_NOTICE, "%s: Private broker config connection UP", fohh_description(connection));

    } else {
        /* Connection probably went away... */
        if (fohh_get_state(connection) == fohh_connection_connected) {
            /* We only count report connection going down when previous state is CONNECTED */
            SITEC_LOG(AL_NOTICE, "%s: Private broker config connection to sitec DOWN", fohh_description(connection));

            zpn_sitec_pbroker_destroy_config_conn_cookie(connection);
        }
        return FOHH_RESULT_ERR;
    }

    return FOHH_RESULT_NO_ERROR;
}

static int zpn_sitec_pbroker_config_unblock_callback(struct fohh_connection *connection,
                                                        enum fohh_queue_element_type element_type,
                                                        void *cookie)
{
    return FOHH_RESULT_NO_ERROR;
}

static int zpn_sitec_pbroker_static_config_conn_callback(struct fohh_connection *connection,
                                                         enum fohh_connection_state state,
                                                         void *cookie)
{
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();

    SITEC_LOG(AL_DEBUG, "%s: Static Config conn callback. Setting up filter tables", fohh_description(connection));

    static char *allow_static_tables[] =
        {
         "zpn_client",
        };
    static int allow_static_tables_count = sizeof(allow_static_tables) / sizeof(allow_static_tables[0]);

    static char **filter_static_tables = NULL;
    static int filter_static_tables_count = 0;

    if (state == fohh_connection_connected) {
        int res;
        struct zpath_customer *customer = NULL;
        /* Allocate state for this connection, so we can track its stuff... */
        int64_t gid = fohh_peer_get_id(connection);
        int64_t customer_gid = ZPATH_GID_MASK_CUSTOMER & gid;

        /* Drop connection when customer is disabled */
        if (customer_gid != 0) {
            res = zpath_customer_get(customer_gid,
                                     &customer,
                                     NULL,
                                     NULL,
                                     0);

            if (res == ZPN_RESULT_NOT_FOUND || (res == ZPN_RESULT_NO_ERROR && customer->disable_traffic == 1)) {
                SITEC_LOG(AL_NOTICE, "%s: Could not accept connection with pbroker(%"PRId64"), customer(%"PRId64") is not found or is disabled", fohh_description(connection), gid, customer_gid);
                return FOHH_RESULT_ERR;
            }
        }

        struct filter_entry static_entries[] = { /* TABLE NAME                     COL NAME        KEY             KEYSIZE  */
                                                  {"zpn_client",                    "customer_gid", &customer_gid, sizeof(int64_t)}
                                                 };
        struct zhash_table* static_table_filter = NULL;

        if (add_multiple_filter_keys(static_entries, sizeof(static_entries)/sizeof(struct filter_entry), &static_table_filter) != WALLY_RESULT_NO_ERROR) {
            SITEC_LOG(AL_ERROR, "Failed to add filter static table entries");
            return FOHH_RESULT_ERR;
        }

        if (!wally_fohh_create_fohh_from_client(gs->sitec_state->rcfg_wally_server,
                                                connection,
                                                allow_static_tables,
                                                allow_static_tables_count,
                                                filter_static_tables,
                                                filter_static_tables_count,
                                                ZPATH_GID_MASK_CUSTOMER,
                                                ZPATH_GID_MASK_CUSTOMER & gid,
                                                0,
                                                static_table_filter,
                                                NULL,
                                                zpn_sitec_pbroker_static_config_conn_callback)) {
            SITEC_LOG(AL_ERROR, "Could not hand off FOHH to wally for pbroker static tables config: %s", fohh_description(connection));
            return FOHH_RESULT_ERR;
        }

        res = zpn_sitec_pbroker_create_config_conn_cookie(connection, sitec_pbroker_static_config_conn);
        if (res) {
            SITEC_LOG(AL_ERROR, "Failed to create static config connection cokkie");
            return res;
        }

        SITEC_LOG(AL_NOTICE, "%s: Private broker static config connection to sitec UP", fohh_description(connection));

    } else {
        /* Connection probably went away... */
        if (fohh_get_state(connection) == fohh_connection_connected) {
            /* We only count report connection going down when previous state is CONNECTED */
            SITEC_LOG(AL_NOTICE, "%s: Private broker static config connection to sitec DOWN", fohh_description(connection));

            zpn_sitec_pbroker_destroy_config_conn_cookie(connection);
        }
        return FOHH_RESULT_ERR;
    }

    return FOHH_RESULT_NO_ERROR;
}

static int zpn_sitec_pbroker_static_config_unblock_callback(struct fohh_connection *connection,
                                                        enum fohh_queue_element_type element_type,
                                                        void *cookie)
{
    return FOHH_RESULT_NO_ERROR;
}

static int zpn_sitec_pbroker_config_override_conn_callback(struct fohh_connection *connection,
                                                            enum fohh_connection_state state,
                                                            void *cookie)
{
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();

    SITEC_LOG(AL_DEBUG, "%s: Config override conn callback. Setting up filter tables", fohh_description(connection));

    static char *filter_tables[] = {
            "zpath_config_override",
            "et_customer_userdb",
            "et_customer_zone"
    };
    static int filter_tables_count = sizeof(filter_tables) / sizeof(filter_tables[0]);

    static char *allow_tables[] = {
            "et_userdb",
            "et_translate",
            "et_translate_code",
            "zpath_cloud"
    };
    static int allow_tables_count = sizeof(allow_tables) / sizeof(allow_tables[0]);

    static struct wally_column_nullify_config nullify_cfg[] = {
            {"et_userdb", "name"},
            {"et_userdb", "description"},
            {"et_userdb", "userdb_url"},
            {"et_userdb", "userdb_username"},
            {"et_userdb", "userdb_password"},
            {"zpath_cloud", "ip_anchor_clouds"},
            {"zpath_cloud", "secret"},
            {"zpath_cloud", "file_key"},
            {"zpath_cloud", "file_key2"},
    };
    static int nullify_cfg_count = sizeof(nullify_cfg) / sizeof(nullify_cfg[0]);

    if (state == fohh_connection_connected) {
        int res;
        struct zpath_customer *customer = NULL;
        /* Allocate state for this connection, so we can track its stuff... */
        int64_t gid = fohh_peer_get_id(connection);
        int64_t customer_gid = ZPATH_GID_MASK_CUSTOMER & gid;
        int64_t global_gid = ZPATH_GLOBAL_CONFIG_OVERRIDE_GID;

        /* Drop connection when customer is disabled */
        if (customer_gid != 0) {
            res = zpath_customer_get(customer_gid,
                                     &customer,
                                     NULL,
                                     NULL,
                                     0);

            if (res == ZPN_RESULT_NOT_FOUND || (res == ZPN_RESULT_NO_ERROR && customer->disable_traffic == 1)) {
                SITEC_LOG(AL_NOTICE, "%s: Could not accept connection with pbroker(%"PRId64"), customer(%"PRId64") is not found or is disabled", fohh_description(connection), gid, customer_gid);
                return FOHH_RESULT_ERR;
            }
        }

        struct filter_entry entries[] = { /* TABLE NAME         COL NAME           KEY              KEYSIZE  */
                {"et_customer_userdb",    "customer_gid", &customer_gid, sizeof(int64_t)},
                {"et_customer_zone",      "customer_gid", &customer_gid, sizeof(int64_t)},
                {"zpath_config_override", "customer_gid", &customer_gid, sizeof(int64_t)},
                {"zpath_config_override", "customer_gid", &global_gid,   sizeof(int64_t)}
        };
        struct zhash_table* table_filter = NULL;

        if (add_multiple_filter_keys(entries, sizeof(entries)/sizeof(struct filter_entry), &table_filter) != WALLY_RESULT_NO_ERROR) {
            SITEC_LOG(AL_ERROR, "Failed to add filter table entries");
            return FOHH_RESULT_ERR;
        }

        struct zhash_table *nullify_state = wally_column_nullify_build_state(nullify_cfg, nullify_cfg_count);
        if (!nullify_state) {
            SITEC_LOG(AL_ERROR, "Could not initialize wally column nullify state");
            deallocate_table_filter(&table_filter);
            return FOHH_RESULT_ERR;
        }

        if (!wally_fohh_create_fohh_from_client(gs->sitec_state->ovd_wally_server,
                                                connection,
                                                allow_tables,
                                                allow_tables_count,
                                                filter_tables,
                                                filter_tables_count,
                                                ZPATH_GID_MASK_CUSTOMER,
                                                ZPATH_GID_MASK_CUSTOMER & gid,
                                                global_gid,
                                                table_filter,
                                                nullify_state,
                                                zpn_sitec_pbroker_config_override_conn_callback)) {
            SITEC_LOG(AL_ERROR, "Could not hand off FOHH to wally for pbroker override: %s", fohh_description(connection));
            return FOHH_RESULT_ERR;
        }

        res = zpn_sitec_pbroker_create_config_conn_cookie(connection, sitec_pbroker_override_conn);
        if (res) {
            return res;
        }

        SITEC_LOG(AL_NOTICE, "%s: Private broker override connection to sitec UP", fohh_description(connection));

    } else {
        /* Connection probably went away... */
        if (fohh_get_state(connection) == fohh_connection_connected) {
            /* We only count report connection going down when previous state is CONNECTED */
            SITEC_LOG(AL_NOTICE, "%s: Private broker override connection to sitec DOWN", fohh_description(connection));

            zpn_sitec_pbroker_destroy_config_conn_cookie(connection);
        }
        return FOHH_RESULT_ERR;
    }

    return FOHH_RESULT_NO_ERROR;
}

static int zpn_sitec_pbroker_config_override_unblock_callback(struct fohh_connection *connection,
                                                               enum fohh_queue_element_type element_type,
                                                               void *cookie)
{
    return FOHH_RESULT_NO_ERROR;
}

static int zpn_broker_pbroker_userdb_sni_get_identifier(const char *sni, int64_t *zone, int64_t *userdb)
{
    char *walk = NULL;
    int64_t cst_gid = strtoll(sni, &walk, 10);
    if (cst_gid == 0 || *walk != '.')
        return ZPN_RESULT_BAD_ARGUMENT;

    walk++;
    int64_t my_zone = strtoll(walk, &walk, 10);
    if (!my_zone || *walk != '.')
        return ZPN_RESULT_BAD_ARGUMENT;

    walk++;
    int64_t my_userdb = strtoll(walk, &walk, 10);
    if (!my_userdb)
        return ZPN_RESULT_BAD_ARGUMENT;

    *zone = my_zone;
    *userdb = my_userdb;

    return ZPN_RESULT_NO_ERROR;
}

static int zpn_sitec_pbroker_userdb_conn_callback(struct fohh_connection *connection,
                                                  enum fohh_connection_state state,
                                                  void *cookie)
{
    SITEC_LOG(AL_DEBUG, "%s: Userdb conn callback. Setting up filter tables", fohh_description(connection));

    static char *allow_tables[] = {
            "zpn_scim_user",
            "zpn_scim_user_attribute",
            "zpn_scim_user_group",
            "zpn_scim_group",
            "zpn_user_risk"
    };
    static int allow_tables_count = sizeof(allow_tables) / sizeof(allow_tables[0]);

    if (state == fohh_connection_connected) {
        int res;
        int64_t zone;
        int64_t userdb;
        struct zpath_customer *customer = NULL;
        /* Allocate state for this connection, so we can track its stuff... */
        int64_t gid = fohh_peer_get_id(connection);
        int64_t customer_gid = ZPATH_GID_MASK_CUSTOMER & gid;

        if (zpn_broker_pbroker_userdb_sni_get_identifier(connection->sni_name, &zone, &userdb)) {
            SITEC_LOG(AL_ERROR, "Failed to parse zone/userdb in pbuserdb sni (sni=%s)", connection->sni_name);
            return FOHH_RESULT_ERR;
        }

        struct wally_fohh_server *userdb_wally_server = zpath_et_wally_userdb_get_userdb_wally_server(zone, userdb);
        if (!userdb_wally_server) {
            SITEC_LOG(AL_ERROR, "Failed to find requested userdb wally (sni=%s)", connection->sni_name);
            return FOHH_RESULT_ERR;
        }

        /* Drop connection when customer is disabled */
        if (customer_gid != 0) {
            res = zpath_customer_get(customer_gid,
                                     &customer,
                                     NULL,
                                     NULL,
                                     0);

            if (res == ZPN_RESULT_NOT_FOUND || (res == ZPN_RESULT_NO_ERROR && customer->disable_traffic == 1)) {
                SITEC_LOG(AL_NOTICE, "%s: Could not accept connection with pbroker(%"PRId64"), customer(%"PRId64") is not found or is disabled", fohh_description(connection), gid, customer_gid);
                return FOHH_RESULT_ERR;
            }
        }

        struct filter_entry entries[] = {
                /*  TABLE NAME                 COL NAME         KEY           KEYSIZE  */
                {"zpn_scim_user",           "customer_gid", &customer_gid, sizeof(int64_t)},
                {"zpn_scim_user_attribute", "customer_gid", &customer_gid, sizeof(int64_t)},
                {"zpn_scim_user_group",     "customer_gid", &customer_gid, sizeof(int64_t)},
                {"zpn_scim_group",          "customer_gid", &customer_gid, sizeof(int64_t)},
                {"zpn_user_risk",           "customer_gid", &customer_gid, sizeof(int64_t)}
        };
        struct zhash_table* table_filter = NULL;

        if (add_multiple_filter_keys(entries, sizeof(entries)/sizeof(struct filter_entry), &table_filter) != WALLY_RESULT_NO_ERROR) {
            SITEC_LOG(AL_ERROR, "Failed to add filter table entries");
            return FOHH_RESULT_ERR;
        }

        if (!wally_fohh_create_fohh_from_client(userdb_wally_server,
                                                connection,
                                                allow_tables,
                                                allow_tables_count,
                                                NULL,
                                                0,
                                                0,
                                                0,
                                                0,
                                                table_filter,
                                                NULL,
                                                zpn_sitec_pbroker_userdb_conn_callback)) {
            SITEC_LOG(AL_ERROR, "Could not hand off FOHH to wally for pbroker userdb: %s", fohh_description(connection));
            return FOHH_RESULT_ERR;
        }

        res = zpn_sitec_pbroker_create_config_conn_cookie(connection, sitec_pbroker_userdb_conn);
        if (res) {
            return res;
        }

        /* Previous changes were because of ET-44125, we don't need that now, current changes are part of ET-89307 */
        zpn_fohh_worker_pbroker_connect_userdb(fohh_connection_get_thread_id(connection));
        SITEC_LOG(AL_NOTICE, "%s: Private broker userdb connection to sitec UP", fohh_description(connection));

    } else {
        /* Connection probably went away... */
        if (fohh_get_state(connection) == fohh_connection_connected) {
            /* We only count report connection going down when previous state is CONNECTED */
            SITEC_LOG(AL_NOTICE, "%s: Private broker userdb connection to sitec DOWN", fohh_description(connection));

            zpn_sitec_pbroker_destroy_config_conn_cookie(connection);
            zpn_fohh_worker_pbroker_disconnect_userdb(fohh_connection_get_thread_id(connection));
        }
        return FOHH_RESULT_ERR;
    }

    return FOHH_RESULT_NO_ERROR;
}

static int zpn_sitec_pbroker_userdb_unblock_callback(struct fohh_connection *connection,
                                                    enum fohh_queue_element_type element_type,
                                                    void *cookie)
{
    SITEC_LOG(AL_CRITICAL, "Implement me");
    return FOHH_RESULT_NO_ERROR;
}

static void zpn_sitec_pbroker_load_conn_monitor_cb(evutil_socket_t sock, short flags, void *cookie)
{
    int res;
    int64_t customer_id = 0;
    struct zpath_customer *customer = NULL;
    struct zpn_private_broker_group *group = NULL;
    struct zpn_private_broker_to_group *group_mappings;
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();

    struct fohh_connection *f_conn = cookie;
    struct connected_sitec_pbroker_fohh_state *pb_state = fohh_connection_get_dynamic_cookie(f_conn);
    if (!pb_state) {
        SITEC_LOG(AL_NOTICE, "%s: Private broker load conn monitor cb has no cookie", fohh_description(f_conn));
        return;
    }
    pb_state->monitor_count++;

    int64_t pbroker_id = fohh_peer_get_id(f_conn);

    /* Drop connection when customer is disabled */
    customer_id = ZPATH_GID_GET_CUSTOMER_GID(pbroker_id);
    if (customer_id != 0) {
        res = zpath_customer_get(customer_id,
                                 &customer,
                                 NULL,
                                 NULL,
                                 0);

        if (res == ZPN_RESULT_NOT_FOUND || (res == ZPN_RESULT_NO_ERROR && customer->disable_traffic == 1)) {
            SITEC_LOG(AL_NOTICE, "%s: Disconnecting pbroker(%"PRId64") as customer(%"PRId64") is not found or is disabled", fohh_description(f_conn), fohh_peer_get_id(f_conn), customer_id);
            /* Kill the connection */
            fohh_connection_delete(f_conn, ZPN_ERR_CUSTOMER_DISABLED);
            return;
        }
    }

    if (gs->disable_flags) {
        SITEC_LOG(AL_ERROR, "%s: %s for %"PRId64,
                fohh_description(f_conn), zpn_sitec_get_disable_flag(), gs->sitec_id);
        fohh_connection_delete(f_conn, ZPN_ERR_SITEC_SERVICE_DISABLED);
        return;
    }

    res = zpn_pbroker_to_group_get_by_private_broker_gid(pbroker_id,
                                                         &group_mappings,
                                                         1,
                                                         NULL,
                                                         NULL,
                                                         0);
    if (res != ZPN_RESULT_NO_ERROR) {
        SITEC_LOG(AL_ERROR, "%s: Error fetching pbroker_to_group data for pbroker id: %ld: %s", fohh_peer_cn(f_conn), (long) pbroker_id, zpn_result_string(res));
        return;
    }

    res = zpn_pbroker_group_get_by_gid(group_mappings->private_broker_group_gid,
                                       &group,
                                       1,
                                       NULL,
                                       NULL,
                                       0);
    if (res != ZPN_RESULT_NO_ERROR) {
        SITEC_LOG(AL_ERROR, "%s: Error fetching pbroker group data for group id: %ld: %s", fohh_peer_cn(f_conn), (long) group_mappings->private_broker_group_gid, zpn_result_string(res));
        return;
    }
}

static int zpn_sitec_private_broker_load_cb(void *argo_cookie_ptr,
                                      void *argo_structure_cookie_ptr,
                                      struct argo_object *object)
{
    struct zpn_private_broker_load *pbl = object->base_structure_void;
    struct zpn_private_broker *pb;
    struct fohh_connection *f_conn = argo_structure_cookie_ptr;
    int64_t gid;
    int res;
    if (zpn_debug_get(ZPN_DEBUG_SITE_CONTROLLER_IDX)) {
        char buf[2000];
        if (argo_object_dump(object, buf, sizeof(buf), NULL, 0) == ARGO_RESULT_NO_ERROR) {
            ZPN_DEBUG_SITEC("%s: %"PRId64": %s", fohh_description(f_conn), fohh_peer_get_id(f_conn), buf);
        }
    }

    gid = fohh_peer_get_id(f_conn);
    if (!gid) {
        SITEC_LOG(AL_ERROR, "%s: Invalid peer", fohh_description(f_conn));
        return ZPATH_RESULT_NO_ERROR;
    }

    if (ZPATH_GID_GET_CUSTOMER_GID(gid) != pbl->customer_gid) {
        SITEC_LOG(AL_ERROR, "%s: Invalid peer, is %ld, says customer is %ld", fohh_description(f_conn), (long) gid, (long) pbl->customer_gid);
        return ZPATH_RESULT_NO_ERROR;
    }

    res = zpn_private_broker_get_by_id(gid, &pb, NULL, NULL, 0);
    if (res) {
        if (res != ZPN_RESULT_ASYNCHRONOUS) {
            /* Some error. Yuck. */
            SITEC_LOG(AL_ERROR, "%s: Could not get private broker for gid = %ld: %s", fohh_description(f_conn), (long) gid, zpn_result_string(res));
        }
        return ZPATH_RESULT_NO_ERROR;
    }
    if (!pb->enabled) {
        SITEC_LOG(AL_NOTICE, "%s: private broker not enabled. Not reporting load", fohh_description(f_conn));
        return ZPATH_RESULT_NO_ERROR;
    }

    pbl->modified_time = epoch_s();
    pbl->gid = gid;

    res = zpn_balance_inst_load_update_pbroker(pbl->gid,
                                               pbl->deleted,
                                               pbl->publish,
                                               pbl->publish_count,
                                               pbl->cpu_util,
                                               pbl->mem_util,
                                               pbl->clients,
                                               pbl->modified_time);
    if (res) {
        SITEC_LOG(AL_ERROR, "%s: Error sitec private broker load: %s", fohh_description(f_conn), zpn_result_string(res));
    }

    res = zpn_sitec_asst_send_pbload_to_all_assistants(gid,
                                                       pbl->cpu_util,
                                                       pbl->mem_util,
                                                       pbl->active_mtun,
                                                       pbl->clients,
                                                       pbl->bytes_xfer,
                                                       pbl->publish,
                                                       pbl->publish_count);
    if (res) {
        SITEC_LOG(AL_ERROR, "%s: Error updating private broker load to assistants: %s", fohh_description(f_conn), zpn_result_string(res));
    }

    return ZPATH_RESULT_NO_ERROR;
}

static int zpn_pbload_conn_callback(struct fohh_connection *connection,
                                    enum fohh_connection_state state,
                                    void *vcookie)
{
    int64_t pb_gid;
    int64_t customer_gid = 0;
    struct zpath_customer *customer = NULL;
    struct connected_sitec_pbroker_fohh_state *cookie;
    int res;

    if (state == fohh_connection_connected) {
        SITEC_LOG(AL_NOTICE, "%"PRId64": %s: pb load connection to sitec connected", fohh_peer_get_id(connection), fohh_description(connection));

        cookie = fohh_connection_get_dynamic_cookie(connection);
        if (cookie) {
            SITEC_LOG(AL_ERROR, "%s: Pre-existing dynamic cookie on connected", fohh_description(connection));
            return ZPN_RESULT_ERR;
        }

        pb_gid = fohh_peer_get_id(connection);

        /* Drop connection when customer is disabled */
        customer_gid = ZPATH_GID_GET_CUSTOMER_GID(pb_gid);
        if (customer_gid != 0) {
            res = zpath_customer_get(customer_gid,
                                     &customer,
                                     NULL,
                                     NULL,
                                     0);

            if (res == ZPN_RESULT_NOT_FOUND ||
                (res == ZPN_RESULT_NO_ERROR && customer->disable_traffic == 1)) {
                SITEC_LOG(AL_NOTICE, "%s: Could not accept connection with pbroker(%"PRId64"), customer(%"PRId64") is not found or is disabled", fohh_description(connection), pb_gid, customer_gid);
                return ZPN_RESULT_ERR;
            }
        }

        cookie = SITEC_CALLOC(sizeof(*cookie));
        fohh_connection_set_dynamic_cookie(connection, cookie);

        /* Generate an ID for this connection */
        res = RAND_bytes(&(cookie->tunnel_id_bin[0]), ZPN_TUNNEL_ID_BYTES);
        if (1 != res) {
            ZPN_SITEC_ASSERT_HARD(0, "Crypto Random Generator not seeded!");
            SITEC_FREE(cookie);
            return res;
        }
        base64_encode_binary(cookie->tunnel_id, cookie->tunnel_id_bin, ZPN_TUNNEL_ID_BYTES);

        cookie->f_conn = connection;
        zpn_sitec_pbroker_conn_tunnel_fohh_store(cookie->tunnel_id, ZPN_TUNNEL_ID_BYTES_TEXT, cookie, &zpn_sitec_pbroker_load_conn);

        /* Set up monitor timer. Note: First auth log is generated when we get tcp_info_report. */
        cookie->timer = event_new(zevent_event_base(zevent_self()),
                                    -1,
                                    EV_PERSIST,
                                    zpn_sitec_pbroker_load_conn_monitor_cb,
                                    connection);

        if (!cookie->timer) {
            SITEC_LOG(AL_CRITICAL, "Memory");
            SITEC_FREE(cookie);
            return FOHH_RESULT_NO_MEMORY;
        }

        struct timeval tv;
        tv.tv_sec = ZPN_TUNNEL_MONITOR_INTERVAL_S;
        tv.tv_usec = 0;
        if (event_add(cookie->timer, &tv)) {
            SITEC_LOG(AL_CRITICAL, "Could not add sitec pbroker load timer");
            event_free(cookie->timer);
            SITEC_FREE(cookie);
            return FOHH_RESULT_NO_MEMORY;
        }

        /* Connected, register callbacks */
        struct argo_state *argo = fohh_argo_get_rx(connection);

        if ((res = argo_register_structure(argo, zpn_private_broker_load_description, zpn_sitec_private_broker_load_cb, connection))) {
            SITEC_LOG(AL_ERROR, "Could not register zpn_private_broker_load for private broker for %s", fohh_description(connection));
            return res;
        }
    } else {
        const char *reason = fohh_close_reason(connection);
        SITEC_LOG(AL_NOTICE, "%"PRId64": %s: Private broker load connection to sitec disconnected: %s",fohh_peer_get_id(connection), fohh_description(connection), reason);

        cookie = fohh_connection_get_dynamic_cookie(connection);
        if (cookie) {
            zpn_sitec_pbroker_conn_tunnel_fohh_remove(cookie->tunnel_id, ZPN_TUNNEL_ID_BYTES_TEXT, &zpn_sitec_pbroker_load_conn);
            fohh_connection_set_dynamic_cookie(connection, NULL);
            if (cookie->timer) {
                event_del(cookie->timer);
                event_free(cookie->timer);
                cookie->timer = NULL;
            }
            SITEC_FREE(cookie);
        }
    }

    return FOHH_RESULT_NO_ERROR;
}

static int zpn_pbload_unblock_callback(struct fohh_connection *connection,
                                       enum fohh_queue_element_type element_type,
                                       void *cookie)
{
    return FOHH_RESULT_NO_ERROR;
}

static void zpn_sitec_pbstats_conn_monitor_cb(evutil_socket_t sock, short flags, void *cookie)
{
    int res;
    int64_t customer_id = 0;
    struct zpath_customer *customer = NULL;
    struct zpn_private_broker_group *group = NULL;
    struct zpn_private_broker_to_group *group_mappings;
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();
    struct fohh_connection *f_conn = cookie;
    struct connected_sitec_pbroker_stats_info *pb_state = fohh_connection_get_dynamic_cookie(f_conn);
    if (!pb_state) {
        SITEC_LOG(AL_NOTICE, "%s: sitec pbstats monitor cb has no cookie", fohh_description(f_conn));
        return;
    }
    pb_state->monitor_count++;

    int64_t pbroker_id = fohh_peer_get_id(f_conn);

    /* Drop connection when customer is disabled */
    customer_id = ZPATH_GID_GET_CUSTOMER_GID(pbroker_id);
    if (customer_id != 0) {
        res = zpath_customer_get(customer_id,
                                 &customer,
                                 NULL,
                                 NULL,
                                 0);

        if (res == ZPN_RESULT_NOT_FOUND || (res == ZPN_RESULT_NO_ERROR && customer->disable_traffic == 1)) {
            SITEC_LOG(AL_NOTICE, "%s: Disconnecting pbroker(%"PRId64") as customer(%"PRId64") is not found or is disabled", fohh_description(f_conn), pbroker_id, customer_id);
            /* Kill the connection */
            fohh_connection_delete(f_conn, ZPN_ERR_CUSTOMER_DISABLED);
            return;
        }
    }

    if (gs->disable_flags) {
        SITEC_LOG(AL_ERROR, "%s: %s for %"PRId64,
                fohh_description(f_conn), zpn_sitec_get_disable_flag(), gs->sitec_id);
        fohh_connection_delete(f_conn, ZPN_ERR_SITEC_SERVICE_DISABLED);
        return;
    }

    res = zpn_pbroker_to_group_get_by_private_broker_gid(pbroker_id,
                                                         &group_mappings,
                                                         1,
                                                         NULL,
                                                         NULL,
                                                         0);
    if (res != ZPN_RESULT_NO_ERROR) {
        SITEC_LOG(AL_ERROR, "%s: Error fetching pbroker_to_group data for pbroker id: %ld: %s", fohh_peer_cn(f_conn), (long) pbroker_id, zpn_result_string(res));
        return;
    }

    res = zpn_pbroker_group_get_by_gid(group_mappings->private_broker_group_gid,
                                       &group,
                                       1,
                                       NULL,
                                       NULL,
                                       0);
    if (res != ZPN_RESULT_NO_ERROR) {
        SITEC_LOG(AL_ERROR, "%s: Error fetching pbroker group data for group id: %ld: %s", fohh_peer_cn(f_conn), (long) group_mappings->private_broker_group_gid, zpn_result_string(res));
        return;
    }

    if (group->site_gid != gs->site_gid) {
        SITEC_LOG(AL_WARNING, "%s: sitec %"PRId64" and pbroker %"PRId64" are part of different sites, hence disconnecting", fohh_description(f_conn), gs->sitec_id, pbroker_id);
        /* Kill the connection */
        fohh_connection_delete(f_conn, ZPN_ERR_SITE_MISMATCH);
        return;
    }
}

int zpn_sitec_pbstats_log_upload_cb(void *argo_cookie_ptr,
                                    void *argo_structure_cookie_ptr,
                                    struct argo_object *object)
{
    struct fohh_connection *f_conn = argo_structure_cookie_ptr;
    struct connected_sitec_pbroker_stats_info *pbroker;
    struct argo_log *log = object->base_structure_void;
    struct argo_object *copy_object;
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();
    int res;

    pbroker = fohh_connection_get_dynamic_cookie(f_conn);
    if (pbroker && 0 == strncmp("pbroker_stats_comprehensive",
                                 log->l_name,
                                 sizeof("pbroker_stats_comprehensive"))) {
        zpn_sitec_siem_pbroker_comprehensive_stats(log->l_obj->base_structure_void,
                                                   pbroker->customer_gid,
                                                   pbroker->tunnel_id,
                                                   pbroker->pb_gid_from_config,
                                                   pbroker->g_pb_grp);
    }

    copy_object = argo_object_copy(object);
    res = fohh_argo_serialize_object(gs->sitec_state->pbstats_fohh_conn, copy_object,
                                     0, fohh_queue_element_type_control);
    if (res) {
        SITEC_LOG(AL_WARNING, "sending pbstats failed with error %s", zpn_result_string(res));
    }
    argo_object_release(copy_object);

    return ZPN_RESULT_NO_ERROR;
}

static int zpn_sitec_pbstats_conn_callback(struct fohh_connection *connection,
                                           enum fohh_connection_state state,
                                           void *vcookie)
{
    int64_t pb_gid;
    int64_t customer_gid = 0;
    struct zpath_customer *customer = NULL;
    struct connected_sitec_pbroker_stats_info *cookie;
    int res;

    if (state == fohh_connection_connected) {
        SITEC_LOG(AL_NOTICE, "%"PRId64": %s: sitec pbstats connection connected", fohh_peer_get_id(connection), fohh_description(connection));

        cookie = fohh_connection_get_dynamic_cookie(connection);
        if (cookie) {
            SITEC_LOG(AL_ERROR, "%s: Pre-existing dynamic cookie on connected", fohh_description(connection));
            return ZPN_RESULT_ERR;
        }

        pb_gid = fohh_peer_get_id(connection);

        /* Drop connection when customer is disabled */
        customer_gid = ZPATH_GID_GET_CUSTOMER_GID(pb_gid);
        if (customer_gid != 0) {
            res = zpath_customer_get(customer_gid,
                                     &customer,
                                     NULL,
                                     NULL,
                                     0);

            if (res == ZPN_RESULT_NOT_FOUND ||
                (res == ZPN_RESULT_NO_ERROR && customer->disable_traffic == 1)) {
                SITEC_LOG(AL_NOTICE, "%s: Could not accept connection with pbroker(%"PRId64"), customer(%"PRId64") is not found or is disabled", fohh_description(connection), pb_gid, customer_gid);
                return ZPN_RESULT_ERR;
            }
        }

        cookie = SITEC_CALLOC(sizeof(*cookie));
        fohh_connection_set_dynamic_cookie(connection, cookie);

        /* Generate an ID for this connection */
        res = RAND_bytes(&(cookie->tunnel_id_bin[0]), ZPN_TUNNEL_ID_BYTES);
        if (1 != res) {
            ZPN_SITEC_ASSERT_HARD(0, "Crypto Random Generator not seeded!");
            SITEC_FREE(cookie);
            return res;
        }
        base64_encode_binary(cookie->tunnel_id, cookie->tunnel_id_bin, ZPN_TUNNEL_ID_BYTES);

        cookie->pb_gid_from_config = pb_gid;
        cookie->f_conn = connection;
        zpn_sitec_pbroker_conn_tunnel_fohh_store(cookie->tunnel_id, ZPN_TUNNEL_ID_BYTES_TEXT, cookie, &zpn_sitec_pbroker_stats_conn);

        /* Set up monitor timer. Note: First auth log is generated when we get tcp_info_report. */
        cookie->timer = event_new(zevent_event_base(zevent_self()),
                                    -1,
                                    EV_PERSIST,
                                    zpn_sitec_pbstats_conn_monitor_cb,
                                    connection);

        if (!cookie->timer) {
            SITEC_LOG(AL_CRITICAL, "Memory");
            SITEC_FREE(cookie);
            return FOHH_RESULT_NO_MEMORY;
        }

        struct timeval tv;
        tv.tv_sec = ZPN_TUNNEL_MONITOR_INTERVAL_S;
        tv.tv_usec = 0;
        if (event_add(cookie->timer, &tv)) {
            SITEC_LOG(AL_CRITICAL, "Could not add sitec pbstats timer");
            event_free(cookie->timer);
            SITEC_FREE(cookie);
            return FOHH_RESULT_NO_MEMORY;
        }

        struct zpn_private_broker_to_group *pb_grp = NULL;

        res = zpn_pbroker_to_group_get_by_private_broker_gid(pb_gid, &pb_grp, 0, NULL, NULL, 0);
        if (pb_grp) {
            // We will always succeed here since this has been called during verification before connection is up
            cookie->customer_gid = pb_grp->customer_gid;
            cookie->g_pb_grp = pb_grp->private_broker_group_gid;
        } else {
            SITEC_LOG(AL_NOTICE, "Cannot find the pbroker with Private broker ID = %"PRId64, pb_gid);
        }

        /* Connected, register callbacks */
        struct argo_state *argo = fohh_argo_get_rx(connection);
        if ((res = argo_register_structure(argo,
                                           global_argo_log_desc,
                                           zpn_sitec_pbstats_log_upload_cb,
                                           connection))) {
            SITEC_LOG(AL_ERROR, "Could not register sitec pbstats log upload for connection %s", fohh_description(connection));
            return res;
        }
    } else {
        const char *reason = fohh_close_reason(connection);
        SITEC_LOG(AL_NOTICE, "%"PRId64": %s: sitec pbstats connection to sitec disconnected: %s",fohh_peer_get_id(connection), fohh_description(connection), reason);

        cookie = fohh_connection_get_dynamic_cookie(connection);
        if (cookie) {
            zpn_sitec_pbroker_conn_tunnel_fohh_remove(cookie->tunnel_id, ZPN_TUNNEL_ID_BYTES_TEXT, &zpn_sitec_pbroker_stats_conn);
            fohh_connection_set_dynamic_cookie(connection, NULL);
            if (cookie->timer) {
                event_del(cookie->timer);
                event_free(cookie->timer);
                cookie->timer = NULL;
            }
            SITEC_FREE(cookie);
        }
    }

    return FOHH_RESULT_NO_ERROR;
}

static int zpn_sitec_pbstats_unblock_callback(struct fohh_connection *connection,
                                              enum fohh_queue_element_type element_type,
                                              void *cookie)
{
    return FOHH_RESULT_NO_ERROR;
}

static int zpn_sitec_clog_callback(struct customer_log_collection_info* cust_log_collection_info,
                                   void* data)
{
    //No need to stream the logs to service endpoint in sitec. Just return no error
    return ZPN_RESULT_NO_ERROR;
}

struct zhash_table *zpn_sitec_create_pblog_collection_map()
{
    struct customer_log_collection_info *customer_trans_log;
    struct customer_log_collection_info *customer_auth_log;
    struct customer_log_collection_info *customer_ast_auth_log;
    struct customer_log_collection_info *event_log;
    struct customer_log_collection_info *customer_dns_log;

    struct zhash_table* log_collection_map = zhash_table_alloc(&zpn_allocator);
    if (!log_collection_map) {
        SITEC_LOG(AL_ERROR, "Out of memory");
        return NULL;
    }

    customer_trans_log = create_customer_log_collection("transaction",
                                                        (int)zpath_customer_log_type_zpn_transaction,
                                                        zpn_trans_log_description,
                                                        zpn_transaction_collection,
                                                        1,
                                                        zpn_sitec_clog_callback,
                                                        zpn_sitec_pb_trans_log_siem_callback,
                                                        NULL,
                                                        0);

    char* transaction_collection_name = argo_log_get_name(zpn_transaction_collection);
    if (zhash_table_store(log_collection_map,
                          transaction_collection_name,
                          strlen(transaction_collection_name),
                          1,
                          customer_trans_log)) {
        SITEC_LOG(AL_ERROR, "Failed to setup transaction log rx");
    }

    customer_auth_log = create_customer_log_collection("client_auth",
                                                       (int)zpath_customer_log_type_zpn_auth,
                                                       zpn_auth_log_description,
                                                       zpn_pb_client_auth_collection,
                                                       1,
                                                       zpn_sitec_clog_callback,
                                                       zpn_sitec_pb_auth_log_siem_callback,
                                                       NULL,
                                                       0);

    char* auth_collection_name = "zpn_auth_log";
    if (zhash_table_store(log_collection_map,
                          auth_collection_name,
                          strlen(auth_collection_name),
                          1,
                          customer_auth_log)) {
        SITEC_LOG(AL_ERROR, "Failed to setup auth log rx");
    }

    customer_ast_auth_log = create_customer_log_collection("asst_auth",
                                                           (int)zpath_customer_log_type_zpn_ast_auth,
                                                           zpn_ast_auth_log_description,
                                                           zpn_ast_auth_collection,
                                                           1,
                                                           zpn_sitec_clog_callback,
                                                           zpn_sitec_ast_auth_log_siem_callback,
                                                           NULL,
                                                           0);

    char* ast_auth_collection_name = argo_log_get_name(zpn_ast_auth_collection);
    if (zhash_table_store(log_collection_map,
                          ast_auth_collection_name,
                          strlen(ast_auth_collection_name),
                          1,
                          customer_ast_auth_log)) {
        SITEC_LOG(AL_ERROR, "Failed to setup auth log rx");
    }

    event_log = create_customer_log_collection(NULL,
                                               0,
                                               NULL,
                                               zpn_pb_event_collection,
                                               0,
                                               NULL,
                                               NULL,
                                               NULL,
                                               0);

    char* zpn_event_collection_name = argo_log_get_name(zpn_event_collection);
    if (zhash_table_store(log_collection_map,
                           zpn_event_collection_name,
                           strlen(zpn_event_collection_name),
                           1,
                           event_log)) {
        SITEC_LOG(AL_ERROR, "Failed to setup event log rx");
    }

    customer_dns_log = create_customer_log_collection(NULL,
                                                      0,
                                                      NULL,
                                                      zpn_dns_collection,
                                                      0,
                                                      NULL,
                                                      NULL,
                                                      NULL,
                                                      0);

    char* dns_collection_name = argo_log_get_name(zpn_dns_collection);
    if (zhash_table_store(log_collection_map,
                          dns_collection_name,
                          strlen(dns_collection_name),
                          1,
                          customer_dns_log)) {
        SITEC_LOG(AL_ERROR, "Failed to setup dns log rx");
    }

    return log_collection_map;
}

int zpn_sitec_pse_log_conn_init()
{
    char        broker_name[1000];
    char*       cloud_name = zpn_sitec_get_cloud_name();
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();
    int result;

    snprintf(broker_name, sizeof(broker_name), "%s.%s", ZPN_SITEC_TO_PUBLIC_BROKER_DNS_NAME, cloud_name);

    result = fohh_log_send(argo_log_get_name(zpn_transaction_collection),
                            argo_log_get_name(zpn_transaction_collection),
                            broker_name,
                            sc_sni_customer_domain_pblog,
                            cloud_name,
                            NULL,
                            htons(ZPN_SITEC_TO_PUBLIC_BROKER_PORT),
                            NULL, 0, 0,
                            zpn_sitec_log_tx_conn_cb);
    if (result) {
        SITEC_LOG(AL_ERROR, "Failed to set up transaction log transmit: %s", zpath_result_string(result));
        return result;
    }

    fohh_connection_monitor_sanity(fohh_get_log_handle(zpn_transaction_collection),
                                    zpn_sitec_fohh_connection_sanity_callback,
                                    SITEC_ALT_CLOUD_CONN_MAX_INIT_TIME_S);

    fohh_connection_set_default_sni(fohh_get_log_handle(zpn_transaction_collection),
                                    (char *)gs->cfg_key_cloud);

    result = fohh_log_send(argo_log_get_name(zpn_pb_client_auth_collection),
                            argo_log_get_name(zpn_pb_client_auth_collection),
                            broker_name,
                            sc_sni_customer_domain_pblog,
                            cloud_name,
                            NULL,
                            htons(ZPN_SITEC_TO_PUBLIC_BROKER_PORT),
                            NULL, 0, 0,
                            zpn_sitec_log_tx_conn_cb);
    if (result) {
        SITEC_LOG(AL_ERROR, "Failed to set up auth log transmit: %s", zpath_result_string(result));
        return result;
    }

    fohh_connection_monitor_sanity(fohh_get_log_handle(zpn_pb_client_auth_collection),
                                    zpn_sitec_fohh_connection_sanity_callback,
                                    SITEC_ALT_CLOUD_CONN_MAX_INIT_TIME_S);

    fohh_connection_set_default_sni(fohh_get_log_handle(zpn_pb_client_auth_collection),
                                    (char *)gs->cfg_key_cloud);

    result = fohh_log_send(argo_log_get_name(zpn_ast_auth_collection),
                            argo_log_get_name(zpn_ast_auth_collection),
                            broker_name,
                            sc_sni_customer_domain_pblog,
                            cloud_name,
                            NULL,
                            htons(ZPN_SITEC_TO_PUBLIC_BROKER_PORT),
                            NULL, 0, 0,
                            zpn_sitec_log_tx_conn_cb);
    if (result) {
        SITEC_LOG(AL_ERROR, "Failed to set up asst auth log transmit: %s", zpath_result_string(result));
        return result;
    }

    fohh_connection_monitor_sanity(fohh_get_log_handle(zpn_ast_auth_collection),
                                    zpn_sitec_fohh_connection_sanity_callback,
                                    SITEC_ALT_CLOUD_CONN_MAX_INIT_TIME_S);

    fohh_connection_set_default_sni(fohh_get_log_handle(zpn_ast_auth_collection),
                                    (char *)gs->cfg_key_cloud);

    result = fohh_log_send(argo_log_get_name(zpn_dns_collection),
                            argo_log_get_name(zpn_dns_collection),
                            broker_name,
                            sc_sni_customer_domain_pblog,
                            cloud_name,
                            NULL,
                            htons(ZPN_SITEC_TO_PUBLIC_BROKER_PORT),
                            NULL, 0, 0,
                            zpn_sitec_log_tx_conn_cb);
    if (result) {
        SITEC_LOG(AL_ERROR, "Failed to set up dns log transmit: %s", zpath_result_string(result));
        return result;
    }

    fohh_connection_monitor_sanity(fohh_get_log_handle(zpn_dns_collection),
                                    zpn_sitec_fohh_connection_sanity_callback,
                                    SITEC_ALT_CLOUD_CONN_MAX_INIT_TIME_S);

    fohh_connection_set_default_sni(fohh_get_log_handle(zpn_dns_collection),
                                    (char *)gs->cfg_key_cloud);

    result = fohh_log_send(argo_log_get_name(zpn_pb_event_collection),
                            argo_log_get_name(zpn_pb_event_collection),
                            broker_name,
                            sc_sni_customer_domain_pblog,
                            cloud_name,
                            NULL,
                            htons(ZPN_SITEC_TO_PUBLIC_BROKER_PORT),
                            NULL, 0, 0,
                            zpn_sitec_log_tx_conn_cb);
    if (result) {
        SITEC_LOG(AL_ERROR, "Failed to set up pb event log transmit: %s", zpath_result_string(result));
        return result;
    }

    fohh_connection_monitor_sanity(fohh_get_log_handle(zpn_pb_event_collection),
                                    zpn_sitec_fohh_connection_sanity_callback,
                                    SITEC_ALT_CLOUD_CONN_MAX_INIT_TIME_S);

    fohh_connection_set_default_sni(fohh_get_log_handle(zpn_pb_event_collection),
                                    (char *)gs->cfg_key_cloud);

    zpath_debug_add_fohh_log_collection(zpn_transaction_collection);
    zpath_debug_add_fohh_log_collection(zpn_pb_client_auth_collection);
    zpath_debug_add_fohh_log_collection(zpn_ast_auth_collection);
    zpath_debug_add_fohh_log_collection(zpn_dns_collection);
    zpath_debug_add_fohh_log_collection(zpn_pb_event_collection);

    return result;
}

static void* zpn_sitec_pblog_app_info_callback(struct fohh_connection *f_conn)
{
    struct connected_sitec_pbroker_fohh_state *cookie;
    int64_t pb_gid;
    int res;

    pb_gid = fohh_peer_get_id(f_conn);
    if (!pb_gid) return NULL;

    cookie = SITEC_CALLOC(sizeof(*cookie));

    /* Generate an ID for this client */
    res = RAND_bytes(&(cookie->tunnel_id_bin[0]), ZPN_TUNNEL_ID_BYTES);
    if (1 != res) {
        ZPN_SITEC_ASSERT_HARD(0, "Crypto Random Generator not seeded!");
        SITEC_FREE(cookie);
        return NULL;
    }
    base64_encode_binary(cookie->tunnel_id, cookie->tunnel_id_bin, ZPN_TUNNEL_ID_BYTES);
    cookie->f_conn = f_conn;

    return cookie;
}

static void zpn_sitec_pblog_stats_cb(struct fohh_connection *f_conn, enum fohh_connection_state state)
{
    struct connected_sitec_pbroker_fohh_state *cookie = log_rx_get_app_info(f_conn);
    if (state == fohh_connection_connected) {
        zpn_sitec_pbroker_conn_tunnel_fohh_store(cookie->tunnel_id, ZPN_TUNNEL_ID_BYTES_TEXT, cookie, &zpn_sitec_pbroker_log_conn);
        zpn_fohh_worker_sitec_connect_log(f_conn->fohh_thread_id);
    } else {
        zpn_sitec_pbroker_conn_tunnel_fohh_remove(cookie->tunnel_id, ZPN_TUNNEL_ID_BYTES_TEXT, &zpn_sitec_pbroker_log_conn);
        zpn_fohh_worker_sitec_disconnect_log(f_conn->fohh_thread_id);
    }
}

static void zpn_sitec_pblog_auth_log(struct fohh_connection *f_conn,
                                     struct zpn_sys_auth_log *log,
                                     enum fohh_connection_state state)
{
    struct fohh_tcp_info info;
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();

    log->log_date = epoch_us();
    log->g_cst = ZPATH_GID_GET_CUSTOMER_GID(log->gid);
    log->g_brk = 0;
    log->g_sitec = gs->sitec_id;
    log->sitec_id = gs->sitec_id;
    log->client_type = zpn_client_type_string(zpn_client_type_private_broker);
    log->auth_type = zpn_auth_type_string(zpn_tunnel_auth_private_broker);

    if (!log->pub_ip.length) {
        uint16_t pub_port_ne;
        fohh_connection_address_and_port(f_conn, &(log->pub_ip), &pub_port_ne, NULL, NULL);
        log->pub_port = ntohs(pub_port_ne);
    }

    memset(&info, 0, sizeof(info));
    if (fohh_connection_get_tcp_info(f_conn, &info) == FOHH_RESULT_NO_ERROR) {
        log->tcpi_snd_mss = info.tcpi_snd_mss;
        log->tcpi_rcv_mss = info.tcpi_rcv_mss;
        log->tcpi_rtt = info.tcpi_rtt;
        log->tcpi_rttvar = info.tcpi_rttvar;
        log->tcpi_snd_cwnd = info.tcpi_snd_cwnd;
        log->tcpi_advmss = info.tcpi_advmss;
        log->tcpi_reordering = info.tcpi_reordering;
        log->tcpi_rcv_rtt = info.tcpi_rcv_rtt;
        log->tcpi_rcv_space = info.tcpi_rcv_space;
        log->tcpi_total_retrans = info.tcpi_total_retrans;
        log->tcpi_thru_put = info.tcpi_thru_put;
        log->tcpi_unacked = info.tcpi_unacked;
        log->tcpi_sacked = info.tcpi_sacked;
        log->tcpi_lost = info.tcpi_lost;
        log->tcpi_fackets = info.tcpi_fackets;

        log->tcpi_last_data_sent = info.tcpi_last_data_sent;
        log->tcpi_last_ack_sent = info.tcpi_last_ack_sent;
        log->tcpi_last_data_recv = info.tcpi_last_data_recv;
        log->tcpi_last_ack_recv = info.tcpi_last_ack_recv;
        log->tcpi_bytes_acked = info.tcpi_bytes_acked;
        log->tcpi_bytes_received = info.tcpi_bytes_received;
        log->tcpi_segs_out = info.tcpi_segs_out;
        log->tcpi_segs_in = info.tcpi_segs_in;

        if ((info.tcpi_rtt / 1000) > CONTROL_CONN_HIGH_RTT_LOG_THRESHOLD_MS) {
            SITEC_LOG(AL_NOTICE, "%s: Private Broker connection high RTT: %d ms, exceeded RTT threshold: %d ms", fohh_description(f_conn), info.tcpi_rtt / 1000, CONTROL_CONN_HIGH_RTT_LOG_THRESHOLD_MS);
        }
    }

    fohh_connection_get_stats(f_conn,
                              &(log->transmit_bytes),
                              &(log->receive_bytes),
                              &(log->transmit_objects),
                              &(log->receive_objects),
                              &(log->transmit_raw_tlv),
                              &(log->receive_raw_tlv));
    /* Skip quiet app connections */
    if (!fohh_connection_is_quiet(f_conn)) {
        log->app_rtt_us = fohh_connection_get_max_app_rtt(f_conn);
        if (fohh_histogram_get_counts_array(f_conn, FOHH_HISTOGRAM_APP_RTT, log->app_rtt, FOHH_HISTOGRAM_MAX_BUCKETS) == FOHH_RESULT_NO_ERROR) {
            log->app_rtt_count = FOHH_HISTOGRAM_MAX_BUCKETS;
        } else {
            SITEC_LOG(AL_DEBUG, "%s: Unable to get app rtt histogram", fohh_description(f_conn));
        }
    }

    /* FIXME: Do we need to send max value for all TCP_INFO metrics? */
    log->tcp_rtt_us = fohh_connection_get_max_tcp_rtt(f_conn);
    if (fohh_histogram_get_counts_array(f_conn, FOHH_HISTOGRAM_TCP_RTT, log->tcp_rtt, FOHH_HISTOGRAM_MAX_BUCKETS) == FOHH_RESULT_NO_ERROR) {
        log->tcp_rtt_count = FOHH_HISTOGRAM_MAX_BUCKETS;
    } else {
        SITEC_LOG(AL_DEBUG, "%s: Unable to get tcp rtt histogram", fohh_description(f_conn));
    }

    if (fohh_histogram_get_counts_array(f_conn, FOHH_HISTOGRAM_TCP_WIN, log->tcp_congestion_win, FOHH_HISTOGRAM_MAX_BUCKETS) == FOHH_RESULT_NO_ERROR) {
        log->tcp_congestion_win_count = FOHH_HISTOGRAM_MAX_BUCKETS;
    } else {
        SITEC_LOG(AL_DEBUG, "%s: Unable to get tcp congestion win histogram", fohh_description(f_conn));
    }

    if (state == fohh_connection_connected) {
        if (!log->auth_us) log->auth_us = epoch_us();
        log->status = ZPN_STATUS_AUTHENTICATED;
    } else {
        log->deauth_us = epoch_us();
        log->status = ZPN_STATUS_DISCONNECTED;
    }

    log->tlv_type = zpn_tlv_type_str(zpn_fohh_tlv);
    zpn_broker_pbroker_update_pb_lat_lon(log, f_conn);

    /* Log to local for debug purpose if enabled */
    if (zpn_sc_pb_auth_collection) {
        argo_log_structure_immediate(zpn_sc_pb_auth_collection,
                                     argo_log_priority_info,
                                     0,
                                     "sys_auth_log",
                                     zpn_sys_auth_log_description,
                                     log);
    }

    zpn_sitec_siem_pb_auth_log(log);
}

static void zpn_sitec_pblog_monitor_tunnel_timer_cb(evutil_socket_t sock, short flags, void *cookie)
{
    int res;
    int64_t customer_id = 0;
    struct zpath_customer *customer = NULL;
    struct zpn_private_broker_group *group = NULL;
    struct zpn_private_broker_to_group *group_mappings;
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();
    struct fohh_connection *f_conn = cookie;

    int64_t pbroker_id = fohh_peer_get_id(f_conn);

    /* Drop connection when customer is disabled */
    customer_id = ZPATH_GID_GET_CUSTOMER_GID(pbroker_id);
    if (customer_id != 0) {
        res = zpath_customer_get(customer_id,
                                 &customer,
                                 NULL,
                                 NULL,
                                 0);

        if (res == ZPN_RESULT_NOT_FOUND || (res == ZPN_RESULT_NO_ERROR && customer->disable_traffic == 1)) {
            SITEC_LOG(AL_NOTICE, "%s: Disconnecting pbroker(%"PRId64") as customer(%"PRId64") is not found or is disabled", fohh_description(f_conn), pbroker_id, customer_id);
            /* Kill the connection */
            fohh_connection_delete(f_conn, ZPN_ERR_CUSTOMER_DISABLED);
            return;
        }
    }

    if (gs->disable_flags) {
        SITEC_LOG(AL_ERROR, "%s: %s for %"PRId64,
                fohh_description(f_conn), zpn_sitec_get_disable_flag(), gs->sitec_id);
        fohh_connection_delete(f_conn, ZPN_ERR_SITEC_SERVICE_DISABLED);
        return;
    }

    res = zpn_pbroker_to_group_get_by_private_broker_gid(pbroker_id,
                                                         &group_mappings,
                                                         1,
                                                         NULL,
                                                         NULL,
                                                         0);
    if (res != ZPN_RESULT_NO_ERROR) {
        SITEC_LOG(AL_ERROR, "%s: Error fetching pbroker_to_group data for pbroker id: %ld: %s", fohh_peer_cn(f_conn), (long) pbroker_id, zpn_result_string(res));
        return;
    }

    res = zpn_pbroker_group_get_by_gid(group_mappings->private_broker_group_gid,
                                       &group,
                                       1,
                                       NULL,
                                       NULL,
                                       0);
    if (res != ZPN_RESULT_NO_ERROR) {
        SITEC_LOG(AL_ERROR, "%s: Error fetching pbroker group data for group id: %ld: %s", fohh_peer_cn(f_conn), (long) group_mappings->private_broker_group_gid, zpn_result_string(res));
        return;
    }

    if (group->site_gid != gs->site_gid) {
        SITEC_LOG(AL_WARNING, "%s: sitec %"PRId64" and pbroker %"PRId64" are part of different sites, hence disconnecting", fohh_description(f_conn), gs->sitec_id, pbroker_id);
        /* Kill the connection */
        fohh_connection_delete(f_conn, ZPN_ERR_SITE_MISMATCH);
        return;
    }
}

static void zpn_sitec_pblog_monitor_timer_cb(evutil_socket_t sock, short flags, void *cookie)
{
    int res;
    int64_t customer_id = 0;
    struct zpath_customer *customer = NULL;
    struct fohh_connection *f_conn = cookie;

    /* Drop connection when customer is disabled */
    customer_id = ZPATH_GID_GET_CUSTOMER_GID(fohh_peer_get_id(f_conn));
    if (customer_id != 0) {
        res = zpath_customer_get(customer_id,
                                 &customer,
                                 NULL,
                                 NULL,
                                 0);

        if (res == ZPN_RESULT_NOT_FOUND || (res == ZPN_RESULT_NO_ERROR && customer->disable_traffic == 1)) {
            SITEC_LOG(AL_NOTICE, "%s: Disconnecting pbroker(%"PRId64") as customer(%"PRId64") is not found or is disabled", fohh_description(f_conn), fohh_peer_get_id(f_conn), customer_id);
            /* Kill the connection */
            fohh_connection_delete(f_conn, ZPN_ERR_CUSTOMER_DISABLED);
            return;
        }
    }

    struct zpn_sys_auth_log log;
    struct zpn_private_broker *pbroker = NULL;
    const struct connected_sitec_pbroker_fohh_state *info = log_rx_get_app_info(f_conn);

    memset(&log, 0, sizeof(log));

    log.gid = fohh_peer_get_id(f_conn);
    log.tunnel_type = ZPN_TUNNEL_SITEC_LOG;
    if (info) {
        log.tunnel_id = info->tunnel_id;
    }

    struct zpn_private_broker_to_group *to_group;

    res = zpn_pbroker_to_group_get_by_private_broker_gid(log.gid,
                                                         &to_group,
                                                         1,
                                                         NULL,
                                                         NULL,
                                                         0);
    if (res == ZPN_RESULT_NO_ERROR) {
        log.grp_gid = to_group->private_broker_group_gid;
    } else {
        SITEC_LOG(AL_NOTICE, "%s: %ld: Could not retrieve pbroker group", fohh_description(f_conn), (long)log.gid);
    }

    res = zpn_private_broker_get_by_id(fohh_peer_get_id(f_conn),
                                        &pbroker,
                                        NULL,
                                        NULL,
                                        0);
    if (res) {
        SITEC_LOG(AL_WARNING, "Error fetching pbroker %ld: %s", (long) fohh_peer_get_id(f_conn), zpn_result_string(res));
    } else {
        log.g_microtenant = is_scope_default(pbroker->scope_gid) ? 0 : pbroker->scope_gid;
    }

    zpn_sitec_pblog_auth_log(f_conn,
                             &log,
                             fohh_get_state(f_conn));

    if(log.cc) {
        SITEC_FREE(log.cc);
        log.cc = NULL;
    }
}

int zpn_sitec_pbroker_verify_callback(struct fohh_connection *f_conn)
{
    int64_t pbroker_id;
    int res;
    int64_t customer_id;
    X509 *cert;
    struct zpn_private_broker *pbroker;
    int ip_acl_iter;
    struct argo_inet pbroker_addr;
    int ip_acl_validation_failed;
    struct zpn_private_broker_group *group = NULL;
    struct zpath_customer *customer = NULL;
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();
    const char* sni_suffix = fohh_connection_get_sni_suffix(f_conn);

    /* Get customer_id reliably */
    res = zpn_broker_client_peer_get_set_customer_id_fohh(f_conn);
    if (res) {
        /* the called function logs plenty */
        return res;
    }
    /* Guaranteed to have customer_id if we got here */
    customer_id = ZPATH_GID_GET_CUSTOMER_GID(fohh_peer_get_id(f_conn));

    /* Drop connection when customer is disabled */
    /* Here we are doing it again for log connections */
    if (customer_id != 0) {
        res = zpath_customer_get(customer_id,
                                 &customer,
                                 NULL,
                                 NULL,
                                 0);

        if (res == ZPN_RESULT_NOT_FOUND || (res == ZPN_RESULT_NO_ERROR && customer->disable_traffic == 1)) {
            SITEC_LOG(AL_NOTICE, "%s: Could not accept connection with pbroker(%"PRId64"), customer(%"PRId64") is not found or is disabled",
                        fohh_description(f_conn), fohh_peer_get_id(f_conn), customer_id);
            return ZPN_RESULT_ERR;
        }
    }

    char *cn = fohh_peer_cn(f_conn);
    if (!cn) {
        SITEC_LOG(AL_WARNING, "No peer CN");
        return ZPN_RESULT_ERR;
    }

    cert = fohh_peer_cert(f_conn);

    if (!cert) {
        SITEC_LOG(AL_WARNING, "No peer cert??");
        return ZPN_RESULT_ERR;
    }

    res = zpn_issuedcert_verify(customer_id,
                                cert,
                                cn,
                                "BROKER",
                                &pbroker_id,
                                zpn_broker_pbroker_ctx_callback_callback,
                                f_conn,
                                fohh_connection_incarnation(f_conn));
    X509_free(cert);

    if (res) {
        if (res == ZPN_RESULT_ASYNCHRONOUS) {
            ZPN_DEBUG_PRIVATE_BROKER("Asynchronous verification request for CN = %s", cn);
        } else {
            SITEC_LOG(AL_WARNING, "Error verifying request for CN = %s, = %s", cn, zpn_result_string(res));
        }
        return res;

    } else {
        ZPN_DEBUG_PRIVATE_BROKER("Verified cert for CN = %s", cn);
    }

    fohh_peer_set_id(f_conn, pbroker_id);

    res = zpn_private_broker_get_by_id(pbroker_id,
                                       &pbroker,
                                       zpn_broker_pbroker_ctx_callback_callback,
                                       f_conn,
                                       fohh_connection_incarnation(f_conn));
    if (res) {
        if (res == ZPN_RESULT_ASYNCHRONOUS) {
            ZPN_DEBUG_PRIVATE_BROKER("Asynchronous pbroker fetch for gid = %ld", (long) pbroker_id);
        } else {
            SITEC_LOG(AL_WARNING, "Error fetching pbroker %ld: %s", (long) pbroker_id, zpn_result_string(res));
        }
        return res;
    } else {
        ZPN_DEBUG_PRIVATE_BROKER("%s: Pvt broker  cert verified, pvt broker %ld fetched.", cn, (long) pbroker_id);
    }

    /*
     * Verify ACL. Since ACL is configured on the pbroker object itself, the first time when a pbroke comes up,
     * we will not have this entry. Only after that user will be allowed to configure this.
     */
    fohh_connection_address(f_conn, &pbroker_addr, NULL);
    if (0 == pbroker->ip_acl_count) {
        ip_acl_validation_failed = 0;
    } else {
        ip_acl_validation_failed = 1;
    }
    ip_acl_iter = 0;
    while (ip_acl_iter != pbroker->ip_acl_count) {
        if (argo_inet_is_contained(&pbroker->ip_acl[ip_acl_iter], &pbroker_addr)) {
            ip_acl_validation_failed = 0;
            break;
        }
        ip_acl_iter++;
    }

    if (ip_acl_validation_failed) {
        char pbroker_addr_str[ARGO_INET_ADDRSTRLEN];
        SITEC_LOG(AL_ERROR, "Pvt broker's IP(%s) didn't match with ACL - terminating the pvt broker",
                argo_inet_generate(pbroker_addr_str, &pbroker_addr));
        return ZPN_RESULT_ERR;
    }

    struct zpn_private_broker_to_group *group_mappings;
    res = zpn_pbroker_to_group_get_by_private_broker_gid(pbroker_id,
                                                         &group_mappings,
                                                         1,
                                                         zpn_broker_pbroker_ctx_callback_callback,
                                                         f_conn,
                                                         fohh_connection_incarnation(f_conn));
    if (res) {
        if (res != ZPN_RESULT_ASYNCHRONOUS) {
            SITEC_LOG(AL_ERROR, "Could not retrieve private broker group mapping info : %s", zpn_result_string(res));
        }
        return res;
    }

    res = zpn_pbroker_group_get_by_gid(group_mappings->private_broker_group_gid,
                                       &group,
                                       1,
                                       zpn_broker_pbroker_ctx_callback_callback,
                                       f_conn,
                                       fohh_connection_incarnation(f_conn));
    if (res) {
        if (res != ZPN_RESULT_ASYNCHRONOUS) {
            SITEC_LOG(AL_ERROR, "Could not retrieve  private broker group info : %s", zpn_result_string(res));
        }
        return res;
    }

    if (gs->disable_flags) {
        SITEC_LOG(AL_ERROR, "%s: %s for %"PRId64,
                fohh_description(f_conn), zpn_sitec_get_disable_flag(), gs->sitec_id);
        fohh_connection_delete(f_conn, ZPN_ERR_SITEC_SERVICE_DISABLED);
        return ZPN_RESULT_ERR;
    }

    if (strncmp(sni_suffix, "pbload.", 7)) {
        if (group && (group->site_gid != gs->site_gid)) {
            SITEC_LOG(AL_WARNING, "%s: sitec %"PRId64" and pbroker %"PRId64" are part of different sites, hence disconnecting", fohh_description(f_conn), gs->sitec_id, pbroker_id);
            return ZPN_RESULT_ERR;
        }
    }

    return ZPN_RESULT_NO_ERROR;
}

int zpn_sitec_add_pse_listeners(struct fohh_generic_server *sni_server, char *offline_domain)
{
    struct fohh_connection *f_conn = NULL;
    char sni_str[ZPN_MAX_SNI_NAME_LEN + 1];
    int res;

    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();

    gs->pblog_collection_map = zpn_sitec_create_pblog_collection_map();
    if (!gs->pblog_collection_map) {
        return ZPN_RESULT_ERR;
    }

    res = zpn_sitec_pse_init();
    if (res != ZPN_RESULT_NO_ERROR) {
        return res;
    }

    f_conn = fohh_server_create_ex(0, //int quiet,
                                   argo_serialize_binary, // enum argo_serialize_mode encoding,
                                   fohh_connection_style_argo, // enum fohh_connection_style style,
                                   NULL, // void *cookie,
                                   zpn_sitec_pbroker_conn_callback,
                                   NULL,
                                   zpn_sitec_pbroker_unblock_callback,
                                   NULL,
                                   NULL,
                                   0,
                                   NULL, // char *root_cert_file_name,
                                   NULL, // char *my_cert_file_name,
                                   NULL, // char *my_cert_key_file_name,
                                   gs->cfg_pkey,
                                   1, // int require pvt broker_cert,
                                   1, // int use_ssl);
                                   zpn_broker_client_ctx_callback,     // ssl ctx callback
                                   zpn_sitec_pbroker_verify_callback, // verify callback
                                   NULL, //post verify callback
                                   1, // Allow binary argo.
                                   ZPN_PBROKER_BROKER_RX_TIMEOUT_S); // timeout
    if (!f_conn) {
        return ZPN_RESULT_ERR;
    }

    snprintf(sni_str, sizeof(sni_str), "pbctl.%s", offline_domain);
    res = fohh_generic_server_register(sni_server,
                                       f_conn,
                                       sni_str,
                                       1,
                                       FOHH_WORKER_ZPN_SCCTL);
    if (res) {
        return res;
    }
    zpn_sitec_add_pse_sni("pbctl", sni_str, 1);

    f_conn = fohh_server_create_ex(0, //int quiet,
                                   argo_serialize_binary, // enum argo_serialize_mode encoding,
                                   fohh_connection_style_argo, // enum fohh_connection_style style,
                                   NULL, // void *cookie,
                                   zpn_sitec_pbroker_config_conn_callback,
                                   NULL,
                                   zpn_sitec_pbroker_config_unblock_callback,
                                   NULL,
                                   NULL,
                                   0,
                                   NULL, // char *root_cert_file_name,
                                   NULL, // char *my_cert_file_name,
                                   NULL, // char *my_cert_key_file_name,
                                   gs->cfg_pkey,
                                   1, // int require pvt broker cert,
                                   1, // int use_ssl);
                                   zpn_broker_client_ctx_callback, // ssl ctx callback
                                   zpn_sitec_pbroker_verify_callback, // verify callback
                                   NULL, //post verify callback
                                   1, // Allow binary argo.
                                   ZPN_PBROKER_BROKER_RX_TIMEOUT_S); // timeout
    if (!f_conn) {
        return ZPN_RESULT_ERR;
    }

    snprintf(sni_str, sizeof(sni_str), "pbcfg.%s", offline_domain);
    res = fohh_generic_server_register(sni_server,
                                       f_conn,
                                       sni_str,
                                       1,
                                       FOHH_WORKER_ZPN_SCCFG);
    if (res) {
        return res;
    }
    zpn_sitec_add_pse_sni("pbcfg", sni_str, 1);

    if (is_static_wally_enabled) {
        f_conn = fohh_server_create_ex(0, //int quiet,
                                       argo_serialize_binary, // enum argo_serialize_mode encoding,
                                       fohh_connection_style_argo, // enum fohh_connection_style style,
                                       NULL, // void *cookie,
                                       zpn_sitec_pbroker_static_config_conn_callback,
                                       NULL,
                                       zpn_sitec_pbroker_static_config_unblock_callback,
                                       NULL,
                                       NULL,
                                       0,
                                       NULL, // char *root_cert_file_name,
                                       NULL, // char *my_cert_file_name,
                                       NULL, // char *my_cert_key_file_name,
                                       gs->cfg_pkey,
                                       1, // int require pvt broker cert,
                                       1, // int use_ssl);
                                       zpn_broker_client_ctx_callback, // ssl ctx callback
                                       zpn_sitec_pbroker_verify_callback, // verify callback
                                       NULL, //post verify callback
                                       1, // Allow binary argo.
                                       ZPN_PBROKER_BROKER_RX_TIMEOUT_S); // timeout
        if (!f_conn) {
            return ZPN_RESULT_ERR;
        }

        snprintf(sni_str, sizeof(sni_str), "pbrcfg.%s", offline_domain);
        res = fohh_generic_server_register(sni_server,
                                           f_conn,
                                           sni_str,
                                           1,
                                           FOHH_WORKER_ZPN_SCSCFG);
        if (res) {
            return res;
        }
        zpn_sitec_add_pse_sni("pbrcfg", sni_str, 1);

    }

    f_conn = fohh_server_create_ex(0, //int quiet,
                                   argo_serialize_binary, // enum argo_serialize_mode encoding,
                                   fohh_connection_style_argo, // enum fohh_connection_style style,
                                   NULL, // void *cookie,
                                   zpn_sitec_pbroker_config_override_conn_callback,
                                   NULL,
                                   zpn_sitec_pbroker_config_override_unblock_callback,
                                   NULL,
                                   NULL,
                                   0,
                                   NULL, // char *root_cert_file_name,
                                   NULL, // char *my_cert_file_name,
                                   NULL, // char *my_cert_key_file_name,
                                   gs->cfg_pkey,
                                   1, // int require pvt broker cert,
                                   1, // int use_ssl);
                                   zpn_broker_client_ctx_callback, // ssl ctx callback
                                   zpn_sitec_pbroker_verify_callback, // verify callback
                                   NULL, //post verify callback
                                   1, // Allow binary argo.
                                   ZPN_PBROKER_BROKER_RX_TIMEOUT_S); // timeout
    if (!f_conn) {
        return ZPN_RESULT_ERR;
    }

    snprintf(sni_str, sizeof(sni_str), "pbovd.%s", offline_domain);
    res = fohh_generic_server_register(sni_server,
                                       f_conn,
                                       sni_str,
                                       1,
                                       FOHH_WORKER_ZPN_PBOVD);
    if (res) {
        return res;
    }
    zpn_sitec_add_pse_sni("pbovd", sni_str, 1);

    f_conn = fohh_server_create_ex(0, //int quiet,
                                   argo_serialize_binary, // enum argo_serialize_mode encoding,
                                   fohh_connection_style_argo, // enum fohh_connection_style style,
                                   NULL, // void *cookie,
                                   zpn_sitec_pbroker_userdb_conn_callback,
                                   NULL,
                                   zpn_sitec_pbroker_userdb_unblock_callback,
                                   NULL,
                                   NULL,
                                   0,
                                   NULL, // char *root_cert_file_name,
                                   NULL, // char *my_cert_file_name,
                                   NULL, // char *my_cert_key_file_name,
                                   gs->cfg_pkey,
                                   1, // int require pvt broker cert,
                                   1, // int use_ssl);
                                   zpn_broker_client_ctx_callback, // ssl ctx callback
                                   zpn_sitec_pbroker_verify_callback, // verify callback
                                   NULL, //post verify callback
                                   1, // Allow binary argo.
                                   ZPN_PBROKER_BROKER_RX_TIMEOUT_S); // timeout
    if (!f_conn) {
        return ZPN_RESULT_ERR;
    }

    snprintf(sni_str, sizeof(sni_str), "pbuserdb.%s", offline_domain);
    res = fohh_generic_server_register(sni_server,
                                       f_conn,
                                       sni_str,
                                       1,
                                       FOHH_WORKER_ZPN_PBUSERDB);
    if (res) {
        return res;
    }
    zpn_sitec_add_pse_sni("pbuserdb", sni_str, 1);

    f_conn = fohh_server_create_ex(0, //int quiet,
                                   argo_serialize_binary, // enum argo_serialize_mode encoding,
                                   fohh_connection_style_argo, // enum fohh_connection_style style,
                                   NULL, // void *cookie,
                                   zpn_pbload_conn_callback,
                                   NULL,
                                   zpn_pbload_unblock_callback,
                                   NULL,
                                   NULL,
                                   0,
                                   NULL, // char *root_cert_file_name,
                                   NULL, // char *my_cert_file_name,
                                   NULL, // char *my_cert_key_file_name,
                                   gs->cfg_pkey,
                                   1, // int require pvt broker cert,
                                   1, // int use_ssl);
                                   zpn_broker_client_ctx_callback, // ssl ctx callback
                                   zpn_sitec_pbroker_verify_callback, // verify callback
                                   NULL, //post verify callback
                                   1, // Allow binary argo.
                                   ZPN_PBROKER_BROKER_RX_TIMEOUT_S); // timeout
    if (!f_conn) {
        return ZPN_RESULT_ERR;
    }

    snprintf(sni_str, sizeof(sni_str), "pbload.%s", offline_domain);
    res = fohh_generic_server_register(sni_server,
                                       f_conn,
                                       sni_str,
                                       1,
                                       FOHH_WORKER_ZPN_PBOVD);
    if (res) {
        return res;
    }
    zpn_sitec_add_pse_sni("pbload", sni_str, 1);

    snprintf(sni_str, sizeof(sni_str), "pblog.%s", offline_domain);
    res = fohh_log_receive(sni_server,                                     //fohh_generic_server
                           sni_str,                                        //domain
                           1,                                              //wildcard_prefix
                           zpn_broker_client_ctx_callback,                 //ssl ctx oallback
                           zpn_sitec_pbroker_verify_callback,              //verify callback
                           fohh_log_conn_info_callback,                    //info callback
                           NULL,                                           // verify info callback
                           NULL,                                           // post verify callback
                           zpn_sitec_pblog_app_info_callback,              // app info callback
                           NULL,                                           //log conn redirect callback
                           zpn_sitec_pblog_stats_cb,                       //log stats callback
                           zpn_sitec_pblog_monitor_tunnel_timer_cb,        //log_tunnel_timer_callback
                           zpn_sitec_pblog_monitor_timer_cb,               //log_timer_callback
                           gs->pblog_collection_map,
                           gs->cfg_pkey,
                           0);                      //log collection map
    if (res) {
        SITEC_LOG(AL_ERROR, "Could not set up log receiver: %s", zpn_result_string(res));
        return res;
    }

    zpn_sitec_add_pse_sni("pblog", sni_str, 1);

    f_conn = fohh_server_create_ex(0, //int quiet,
                                   argo_serialize_binary, // enum argo_serialize_mode encoding,
                                   fohh_connection_style_argo, // enum fohh_connection_style style,
                                   NULL, // void *cookie,
                                   zpn_sitec_pbstats_conn_callback,
                                   NULL,
                                   zpn_sitec_pbstats_unblock_callback,
                                   NULL,
                                   NULL,
                                   0,
                                   NULL, // char *root_cert_file_name,
                                   NULL, // char *my_cert_file_name,
                                   NULL, // char *my_cert_key_file_name,
                                   gs->cfg_pkey,
                                   1, // int require pvt broker cert,
                                   1, // int use_ssl);
                                   zpn_broker_client_ctx_callback, // ssl ctx callback
                                   zpn_sitec_pbroker_verify_callback, // verify callback
                                   NULL, //post verify callback
                                   1, // Allow binary argo.
                                   ZPN_PBROKER_BROKER_RX_TIMEOUT_S); // timeout
    if (!f_conn) {
        return ZPN_RESULT_ERR;
    }

    snprintf(sni_str, sizeof(sni_str), "pbstats.%s", offline_domain);
    res = fohh_generic_server_register(sni_server,
                                       f_conn,
                                       sni_str,
                                       1,
                                       FOHH_WORKER_ZPN_PBSTATS);
    if (res) {
        return res;
    }
    zpn_sitec_add_pse_sni("pbstats", sni_str, 1);

    return res;
}

static int zpn_sitec_pse_firedrill_walk_f(void *cookie,
                                              void *value,
                                              void *key,
                                              size_t key_len)
{
    struct zpn_sitec_firedrill_exit *firedrill_obj = (struct zpn_sitec_firedrill_exit *) cookie;
    struct connected_sitec_pbroker_control_fohh_state *info = (struct connected_sitec_pbroker_control_fohh_state *) value;

    return fohh_argo_serialize(info->f_conn,
                                zpn_sitec_firedrill_exit_description,
                                firedrill_obj,
                                0,
                                fohh_queue_element_type_mission_critical);
}

void zpn_sitec_private_broker_send_firedrill_exit_rpc()
{
    struct zpn_sitec_firedrill_exit firedrill_msg;
    firedrill_msg.firedrill_exit = EXIT_FIREDRILL;

    ZPATH_MUTEX_LOCK(&zpn_sitec_pbroker_control_conn.lock, __FILE__, __LINE__);
    zhash_table_walk(zpn_sitec_pbroker_control_conn.table, NULL, zpn_sitec_pse_firedrill_walk_f, &firedrill_msg);
    ZPATH_MUTEX_UNLOCK(&zpn_sitec_pbroker_control_conn.lock, __FILE__, __LINE__);
}
