/*
 * zpn_sitec_asst_conn.c. Copyright (C) 2024 Zscaler, Inc. All Rights Reserved.
 *
 */

#include <stdio.h>
#include <stdarg.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <pthread.h>
#include <sys/time.h>
#include <ctype.h>
#include <errno.h>
#include <fcntl.h>
#ifdef __linux__
#include <malloc.h>
#endif
#include <openssl/rand.h>

#include "zpath_lib/zpath_customer.h"
#include "zpn/zpn_lib.h"
#include "zpn/zpn_assistant_table.h"
#include "fohh/fohh.h"
#include "zpn_sitecd/zpn_sitec.h"
#include "zpath_lib/zpath_config_override.h"
#include "zpn_waf/zpn_inspection_config_data.h"
#include "wally/wally_filter_table.h"
#include "zpn/zpn_signing_cert.h"
#include "zpn/zpn_issuedcert.h"
#include "zpn/zpn_assistantgroup_assistant_relation.h"
#include "zpn/zpn_assistant_group.h"
#include "zpn/zpn_private_broker_load_table.h"
#include "zpn/zpn_fohh_worker.h"
#include "zpn/zpn_broker_assistant.h"
#include "zpn_waf/zpn_waf_log.h"
#include "zpn_waf/zpn_waf_log_compiled.h"
#include "zpn_waf/zpn_app_inspection_log.h"
#include "zpn_waf/zpn_app_inspection_log_compiled.h"
#include "fohh/fohh_log.h"
#include "zpn_sitecd/zpn_sitec_siem.h"
#include "zpn_sitecd/zpn_sitec_broker_conn.h"
#include "zpn_sitecd/zpn_sitec_alt_cloud.h"
#include "zpn/zpn_sitec_table.h"
#include "zpn/zpn_broker_siem.h"
#include "zpn/zpn_scope.h"
#include "zpn/zpn_rpc.h"

#define MAX_SITEC_ASST_SNIS 16
#define EXIT_FIREDRILL 1

static struct zpn_sitec_sni zpn_sitec_asst_snis[MAX_SITEC_ASST_SNIS];
static int zpn_sitec_asst_sni_count = 0;

struct zpn_sitec_asst_conn {
    zpath_mutex_t lock;
    struct zhash_table *table;
};

enum A2SCAuth {
    AUTH_NOT_SEEN = 0,
    AUTH_UNSUCCESSFUL = 1,
    AUTH_SUCCESSFUL = 2
};

struct connected_sitec_asst_config_fohh_state {
    int is_override;
    int64_t assistant_gid_from_config;
    struct fohh_connection *f_conn;
    int64_t f_conn_incarnation;

    struct event *timer;

    uint8_t tunnel_id_bin[ZPN_TUNNEL_ID_BYTES];
    char tunnel_id[ZPN_TUNNEL_ID_BYTES_TEXT + 1];

    int64_t customer_gid;
    int64_t scope_gid;
    uint64_t monitor_count;
};

struct connected_sitec_asst_log_fohh_state {
    struct fohh_connection *f_conn;

    struct event *timer;

    uint8_t tunnel_id_bin[ZPN_TUNNEL_ID_BYTES];
    char tunnel_id[ZPN_TUNNEL_ID_BYTES_TEXT + 1];
    uint64_t monitor_count;
};

struct connected_sitec_asst_stats_fohh_state {
    int64_t assistant_gid_from_config;
    struct fohh_connection *f_conn;
    struct event *timer;
    int64_t g_ast_grp;

    struct zpn_ast_auth_log auth_log;

    /* 128 bit (16 bytes, 24 bytes base64) random ID for this TLS
     * connection, in base64 */
    uint8_t tunnel_id_bin[ZPN_TUNNEL_ID_BYTES];
    char tunnel_id[ZPN_TUNNEL_ID_BYTES_TEXT + 1];

    int64_t customer_gid;
    int64_t scope_gid;
    uint64_t monitor_count;
};

struct connected_sitec_asst_control_fohh_state {
    int64_t assistant_gid_from_config;
    struct fohh_connection *f_conn;
    int64_t f_conn_incarnation;

    /* Timer for authentication reports. */
    struct event *timer;
    int first_auth_sent;

    char *version;
    char *sarge_version;

    int version_major;
    int version_minor;

    unsigned tcp_info_ready:1;
    unsigned status_report_ready:1;

    struct zpn_ast_auth_log auth_log;
    struct {
        int64_t     delta_mtunnel_count;
    } delta_from_prev_auth_log;
    int64_t g_ast_grp;

    int log_upload;
    uint64_t debug_flag;

    int stats_upload;

    struct argo_inet a_ip;
    char a_cc[CC_STR_LEN + 1];             /* Need to NULL terminate */
    int a_lat;
    int a_lon;

    char *dft_rt_intf;
    char *platform;
    char *platform_detail;
    char *runtime_os;

    /* 128 bit (16 bytes, 24 bytes base64) random ID for this TLS
     * connection, in base64 */
    uint8_t tunnel_id_bin[ZPN_TUNNEL_ID_BYTES];
    char tunnel_id[ZPN_TUNNEL_ID_BYTES_TEXT + 1];

    int64_t customer_gid;
    int64_t scope_gid;

    int disabled;
    int32_t udp4_port_util;
    int32_t udp6_port_util;
    int32_t sys_fd_util;
    int32_t proc_fd_util;

    enum A2SCAuth scope_auth_status;

    uint64_t tx_auth_report_failed;
    uint64_t tx_auth_report_success;
    uint64_t rx_tcp_info_report;
    uint64_t rx_environment_report;
    uint64_t rx_health_report;
    uint64_t rx_app_route_registration;
    uint64_t rx_broker_request_ack;
    uint64_t rx_dns_assistant_check;
    uint64_t rx_log_stats_upload;
    uint64_t rx_log_control;
    uint64_t rx_stats_control;
    uint64_t rx_status_report;
    uint64_t rx_state;
    uint64_t rx_active_connection;
    uint64_t rx_waf_cert_prv_key_req;
    uint64_t rx_waf_cert_gen_req;
    uint64_t active_connection_switched_in;
    uint64_t active_connection_switched_out;
    uint64_t active_control_connection_currently:1;
    uint64_t monitor_count;
};

int zpn_sitec_asst_auth_log(struct fohh_connection *f_conn,
                            struct zpn_ast_auth_log *log,
                            int64_t asst_id,
                            char *type,
                            enum fohh_connection_state state);

static struct zpn_sitec_asst_conn zpn_sitec_asst_config_conn;
static struct zpn_sitec_asst_conn zpn_sitec_asst_alog_conn;
static struct zpn_sitec_asst_conn zpn_sitec_asst_astats_conn;
static struct zpn_sitec_asst_conn zpn_sitec_asst_control_conn;

static void zpn_sitec_asst_conn_tx_rx_stats_fill(struct fohh_connection *f_conn,
                                                 struct zpn_sitec_comprehensive_stats *out_data)
{
    uint64_t tx_b = 0;
    uint64_t rx_b = 0;
    uint64_t tx_o = 0;
    uint64_t rx_o = 0;
    uint64_t tx_raw_tlv = 0;
    uint64_t rx_raw_tlv = 0;

    fohh_connection_get_stats(f_conn, &tx_b, &rx_b, &tx_o, &rx_o, &tx_raw_tlv, &rx_raw_tlv);
    out_data->to_connector_bytes += tx_b;
    out_data->from_connector_bytes += rx_b;
}

static int zpn_sitec_asst_config_conn_stats_walk_f(void *cookie,
                                                   void *value,
                                                   void *key,
                                                   size_t key_len)
{
    struct zpn_sitec_comprehensive_stats *out_data = (struct zpn_sitec_comprehensive_stats *) cookie;
    struct connected_sitec_asst_config_fohh_state *info = (struct connected_sitec_asst_config_fohh_state *) value;
    zpn_sitec_asst_conn_tx_rx_stats_fill(info->f_conn, out_data);
    return ZPN_RESULT_NO_ERROR;
}

static int zpn_sitec_asst_control_conn_stats_walk_f(void *cookie,
                                                    void *value,
                                                    void *key,
                                                    size_t key_len)
{
    struct zpn_sitec_comprehensive_stats *out_data = (struct zpn_sitec_comprehensive_stats *) cookie;
    struct connected_sitec_asst_control_fohh_state *info = (struct connected_sitec_asst_control_fohh_state *) value;
    zpn_sitec_asst_conn_tx_rx_stats_fill(info->f_conn, out_data);
    return ZPN_RESULT_NO_ERROR;
}

static int zpn_sitec_asst_astats_conn_stats_walk_f(void *cookie,
                                                   void *value,
                                                   void *key,
                                                   size_t key_len)
{
    struct zpn_sitec_comprehensive_stats *out_data = (struct zpn_sitec_comprehensive_stats *) cookie;
    struct connected_sitec_asst_stats_fohh_state *info = (struct connected_sitec_asst_stats_fohh_state *) value;
    zpn_sitec_asst_conn_tx_rx_stats_fill(info->f_conn, out_data);
    return ZPN_RESULT_NO_ERROR;
}

static int zpn_sitec_asst_alog_conn_stats_walk_f(void *cookie,
                                                 void *value,
                                                 void *key,
                                                 size_t key_len)
{
    struct zpn_sitec_comprehensive_stats *out_data = (struct zpn_sitec_comprehensive_stats *) cookie;
    struct connected_sitec_asst_log_fohh_state *info = (struct connected_sitec_asst_log_fohh_state *) value;
    zpn_sitec_asst_conn_tx_rx_stats_fill(info->f_conn, out_data);
    return ZPN_RESULT_NO_ERROR;
}


void zpn_sitec_connector_conn_comprehensive_stats_fill(struct zpn_sitec_comprehensive_stats *out_data,
                                                       struct zpn_sitec_comprehensive_stats *last_comprehensive_stats_data) {
    out_data->active_conn_to_connector = zhash_table_get_size(zpn_sitec_asst_config_conn.table) +
                                         zhash_table_get_size(zpn_sitec_asst_alog_conn.table) +
                                         zhash_table_get_size(zpn_sitec_asst_astats_conn.table) +
                                         zhash_table_get_size(zpn_sitec_asst_control_conn.table);

    ZPATH_MUTEX_LOCK(&zpn_sitec_asst_config_conn.lock, __FILE__, __LINE__);
    zhash_table_walk(zpn_sitec_asst_config_conn.table, NULL, zpn_sitec_asst_config_conn_stats_walk_f, out_data);
    ZPATH_MUTEX_UNLOCK(&zpn_sitec_asst_config_conn.lock, __FILE__, __LINE__);

    ZPATH_MUTEX_LOCK(&zpn_sitec_asst_control_conn.lock, __FILE__, __LINE__);
    zhash_table_walk(zpn_sitec_asst_control_conn.table, NULL, zpn_sitec_asst_control_conn_stats_walk_f, out_data);
    ZPATH_MUTEX_UNLOCK(&zpn_sitec_asst_control_conn.lock, __FILE__, __LINE__);

    ZPATH_MUTEX_LOCK(&zpn_sitec_asst_astats_conn.lock, __FILE__, __LINE__);
    zhash_table_walk(zpn_sitec_asst_astats_conn.table, NULL, zpn_sitec_asst_astats_conn_stats_walk_f, out_data);
    ZPATH_MUTEX_UNLOCK(&zpn_sitec_asst_astats_conn.lock, __FILE__, __LINE__);

    ZPATH_MUTEX_LOCK(&zpn_sitec_asst_alog_conn.lock, __FILE__, __LINE__);
    zhash_table_walk(zpn_sitec_asst_alog_conn.table, NULL, zpn_sitec_asst_alog_conn_stats_walk_f, out_data);
    ZPATH_MUTEX_UNLOCK(&zpn_sitec_asst_alog_conn.lock, __FILE__, __LINE__);

    out_data->to_connector_bytes_delta = out_data->to_connector_bytes - last_comprehensive_stats_data->to_connector_bytes;
    out_data->from_connector_bytes_delta = out_data->from_connector_bytes - last_comprehensive_stats_data->from_connector_bytes;
    out_data->to_connector_bytes_rate = out_data->to_connector_bytes_delta ?
            ((out_data->to_connector_bytes_delta * 1000000) / (out_data->cloud_time_us - last_comprehensive_stats_data->cloud_time_us)) : 0;
    out_data->from_connector_bytes_rate = out_data->from_connector_bytes_delta ?
            ((out_data->from_connector_bytes_delta * 1000000) / (out_data->cloud_time_us - last_comprehensive_stats_data->cloud_time_us)) : 0;
}

static int zpn_sitec_asst_init()
{
    zpn_sitec_asst_config_conn.lock = ZPATH_MUTEX_INIT;
    zpn_sitec_asst_config_conn.table = zhash_table_alloc(&zpn_allocator);
    if (!zpn_sitec_asst_config_conn.table) {
        SITEC_LOG(AL_ERROR, "Couldn't allocate zpn_sitec_asst_config_conn table");
        return ZPN_RESULT_NO_MEMORY;
    }

    zpn_sitec_asst_alog_conn.lock = ZPATH_MUTEX_INIT;
    zpn_sitec_asst_alog_conn.table = zhash_table_alloc(&zpn_allocator);
    if (!zpn_sitec_asst_alog_conn.table) {
        SITEC_LOG(AL_ERROR, "Couldn't allocate zpn_sitec_asst_alog_conn table");
        return ZPN_RESULT_NO_MEMORY;
    }

    zpn_sitec_asst_astats_conn.lock = ZPATH_MUTEX_INIT;
    zpn_sitec_asst_astats_conn.table = zhash_table_alloc(&zpn_allocator);
    if (!zpn_sitec_asst_astats_conn.table) {
        SITEC_LOG(AL_ERROR, "Couldn't allocate zpn_sitec_asst_astats_conn table");
        return ZPN_RESULT_NO_MEMORY;
    }

    zpn_sitec_asst_control_conn.lock = ZPATH_MUTEX_INIT;
    zpn_sitec_asst_control_conn.table = zhash_table_alloc(&zpn_allocator);
    if (!zpn_sitec_asst_control_conn.table) {
        SITEC_LOG(AL_ERROR, "Couldn't allocate zpn_sitec_asst_control_conn table");
        return ZPN_RESULT_NO_MEMORY;
    }

    return ZPN_RESULT_NO_ERROR;
}

static void zpn_sitec_asst_conn_tunnel_fohh_store(char *tunnel_id,
                                                  size_t tunnel_id_len,
                                                  void *info,
                                                  struct zpn_sitec_asst_conn *sitec_asst_conn) {
    ZPATH_MUTEX_LOCK(&sitec_asst_conn->lock, __FILE__, __LINE__);
    zhash_table_store(sitec_asst_conn->table, tunnel_id, tunnel_id_len, 0, info);
    ZPATH_MUTEX_UNLOCK(&sitec_asst_conn->lock, __FILE__, __LINE__);
};

static void zpn_sitec_asst_conn_tunnel_fohh_remove(char *tunnel_id, size_t tunnel_id_len, struct zpn_sitec_asst_conn *sitec_asst_conn) {
    ZPATH_MUTEX_LOCK(&sitec_asst_conn->lock, __FILE__, __LINE__);
    void *info = zhash_table_lookup(sitec_asst_conn->table, tunnel_id, tunnel_id_len, NULL);
    if (info) {
        zhash_table_remove(sitec_asst_conn->table, tunnel_id, tunnel_id_len, NULL);
    }
    ZPATH_MUTEX_UNLOCK(&sitec_asst_conn->lock, __FILE__, __LINE__);
};

void zpn_sitec_add_asst_sni(char *type, char *sni_str, int wildcard)
{
    ZPN_SITEC_ASSERT_HARD(zpn_sitec_asst_sni_count < MAX_SITEC_ASST_SNIS,
                    "sitec add sni: asst snis count exceeded!");
    zpn_sitec_asst_snis[zpn_sitec_asst_sni_count].sni_str = SITEC_STRDUP(sni_str, strlen(sni_str));
    zpn_sitec_asst_snis[zpn_sitec_asst_sni_count].type = SITEC_STRDUP(type, strlen(type));
    zpn_sitec_asst_snis[zpn_sitec_asst_sni_count].wild_card = wildcard;
    zpn_sitec_asst_sni_count++;
}

void zpn_sitec_enable_asst_snis(int enable)
{
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();
    int res;

    for (int count = 0; count < zpn_sitec_asst_sni_count; count++) {
        const char *type = zpn_sitec_asst_snis[count].type;
        const char *sni_str = zpn_sitec_asst_snis[count].sni_str;
        int wild_card = zpn_sitec_asst_snis[count].wild_card;

        res = fohh_generic_server_set_domain_disabled(gs->sni_server, sni_str, wild_card, !enable);

        if (res) {
            SITEC_LOG(AL_ERROR, "Could not %s %s, sni is %s", enable? "enable" : "disable", type, sni_str);
        }
    }
}

static int zpn_sitec_asst_control_conn_walk_f(void *cookie,
                                              void *value,
                                              void *key,
                                              size_t key_len)
{
    struct zpn_private_broker_load *load = (struct zpn_private_broker_load *) cookie;
    struct connected_sitec_asst_control_fohh_state *info = (struct connected_sitec_asst_control_fohh_state *) value;

    return fohh_argo_serialize(info->f_conn, zpn_private_broker_load_description, load, 0,
                               fohh_queue_element_type_mission_critical);
}

int zpn_sitec_asst_send_pbload_to_all_assistants(int64_t gid,
                                                 uint16_t cpu_util,
                                                 uint16_t mem_util,
                                                 uint32_t active_mtun,
                                                 uint32_t clients,
                                                 uint32_t bytes_xfer,
                                                 char **publish,
                                                 size_t publish_count)
{
    struct zpn_private_broker_load load;
    int res = ZPN_RESULT_NO_ERROR;

    if (!gid) return ZPN_RESULT_BAD_ARGUMENT;

    memset(&load, 0, sizeof(load));

    load.customer_gid = ZPATH_GID_GET_CUSTOMER_GID(gid);
    load.gid = gid;
    load.cpu_util = cpu_util;
    load.mem_util = mem_util;
    load.active_mtun = active_mtun;
    load.clients = clients;
    load.bytes_xfer = bytes_xfer;
    load.publish = publish;
    load.publish_count = publish_count;
    load.modified_time = epoch_s();

    ZPATH_MUTEX_LOCK(&zpn_sitec_asst_control_conn.lock, __FILE__, __LINE__);
    zhash_table_walk(zpn_sitec_asst_control_conn.table, NULL, zpn_sitec_asst_control_conn_walk_f, &load);
    ZPATH_MUTEX_UNLOCK(&zpn_sitec_asst_control_conn.lock, __FILE__, __LINE__);

    return res;
}

void zpn_sitec_update_asst_listeners(struct fohh_generic_server *sni_server,
                                     char *new_offline_domain)
{
    char sni_str[ZPN_MAX_SNI_NAME_LEN + 1];
    int res;

    for (int count = 0; count < zpn_sitec_asst_sni_count; count++) {
        snprintf(sni_str, sizeof(sni_str), "%s.%s", zpn_sitec_asst_snis[count].type, new_offline_domain);

        res = fohh_generic_server_re_register(sni_server,
                                                zpn_sitec_asst_snis[count].sni_str,
                                                sni_str,
                                                zpn_sitec_asst_snis[count].wild_card);
        if (res) {
            SITEC_LOG(AL_ERROR, "Could not re-register client type: %s  generic server for %s to %s",
                                zpn_sitec_asst_snis[count].type, zpn_sitec_asst_snis[count].sni_str, sni_str);
        }

        SITEC_FREE(zpn_sitec_asst_snis[count].sni_str);
        zpn_sitec_asst_snis[count].sni_str = SITEC_STRDUP(sni_str, strlen(sni_str));
    }
}

static void zpn_sitec_asst_stats_conn_auth_log_send(struct connected_sitec_asst_stats_fohh_state *asst,
                                                    enum fohh_connection_state state)
{
    int res;

    if (NULL == asst) return;

    res = zpn_sitec_asst_auth_log(asst->f_conn,
                                  &(asst->auth_log),
                                  asst->assistant_gid_from_config,
                                  ZPN_ASSISTANT_SITEC_STATS,
                                  state);
    if (res) {
        SITEC_LOG(AL_WARNING, "%s: Could not generate stats auth log", fohh_peer_cn(asst->f_conn));
    }
}

static void zpn_sitec_asst_control_conn_auth_log_send(struct connected_sitec_asst_control_fohh_state *asst,
                                                      enum fohh_connection_state state)
{
    int res;

    if (NULL == asst) return;

    (asst->first_auth_sent) ? (asst->auth_log.first_log = 0) : (asst->auth_log.first_log = 1);

    asst->auth_log.delta_mtunnel_count = asst->delta_from_prev_auth_log.delta_mtunnel_count;
    bzero(&asst->delta_from_prev_auth_log, sizeof(asst->delta_from_prev_auth_log));

    res = zpn_sitec_asst_auth_log(asst->f_conn,
                                  &(asst->auth_log),
                                  asst->assistant_gid_from_config,
                                  ZPN_ASSISTANT_SITEC_CONTROL,
                                  state);
    if (res) {
        SITEC_LOG(AL_WARNING, "%s: Could not generate control auth log", fohh_peer_cn(asst->f_conn));
    } else {
        asst->first_auth_sent = 1;
    }

    /* Resetting all the deltas in assistant interface stats */
    asst->auth_log.delta_intf_rb = 0;
    asst->auth_log.delta_intf_rp = 0;
    asst->auth_log.delta_intf_re = 0;
    asst->auth_log.delta_intf_rd = 0;
    asst->auth_log.delta_intf_tb = 0;
    asst->auth_log.delta_intf_tp = 0;
    asst->auth_log.delta_intf_te = 0;
    asst->auth_log.delta_intf_td = 0;
    asst->auth_log.delta_total_intf_b = 0;
    asst->auth_log.delta_total_intf_p = 0;
    asst->auth_log.delta_total_intf_e = 0;
    asst->auth_log.delta_total_intf_d = 0;
}

static int zpn_sitec_asst_update_asst_lat_lon(struct connected_sitec_asst_control_fohh_state *asst)
{
    struct zpn_assistant_group *group;
    int                        res;

    res = zpn_assistant_group_get_by_gid(asst->g_ast_grp,
                                         &group,
                                         NULL,
                                         NULL,
                                         0);
    if (res == ZPATH_RESULT_ASYNCHRONOUS) {
        return ZPN_RESULT_NO_ERROR;
    }
    if (res) {
        return ZPN_RESULT_ERR;
    } else {
        asst->a_lat = group->latitude;
        asst->a_lon = group->longitude;
        asst->auth_log.a_lat = group->latitude;
        asst->auth_log.a_lon = group->longitude;
        if (group->country_code) {
            if (asst->auth_log.cc && strcmp(asst->auth_log.cc, group->country_code)) {
                SITEC_FREE(asst->auth_log.cc);
                asst->auth_log.cc = NULL;
            }

            if (NULL == asst->auth_log.cc) {
                asst->auth_log.cc = SITEC_STRDUP(group->country_code, strlen(group->country_code));
            }
        }
    }

    if (geoip_db_file && (asst->a_lat == 0) && (asst->a_lon == 0)) {
            fohh_connection_address(asst->f_conn, &asst->a_ip, NULL);
            char str[ARGO_INET_ADDRSTRLEN];

            if (zpath_geoip_lookup(&asst->a_ip, &asst->a_lat, &asst->a_lon, &asst->a_cc[0]) == ZPATH_RESULT_NO_ERROR) {
                ZPN_DEBUG_ASSISTANT("Assistant IP = %s, lat = %d, lon = %d, cc = %s",
                                    argo_inet_generate(str, &(asst->a_ip)), asst->a_lat, asst->a_lon, asst->a_cc);

                asst->auth_log.a_lat = asst->a_lat;
                asst->auth_log.a_lon = asst->a_lon;
                if (asst->auth_log.cc && strcmp(asst->a_cc, asst->auth_log.cc)){
                    SITEC_FREE(asst->auth_log.cc);
                    asst->auth_log.cc = NULL;
                }

                if (NULL == asst->auth_log.cc) {
                    asst->auth_log.cc = SITEC_STRDUP(asst->a_cc, strlen(asst->a_cc));
                }
            }
    }

    return ZPN_RESULT_NO_ERROR;
}

static void zpn_sitec_asst_control_conn_monitor_cb(evutil_socket_t sock, short flags, void *cookie)
{
    int res;
    struct zpath_customer *customer = NULL;
    struct connected_sitec_asst_control_fohh_state *asst = cookie;
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();
    struct zpn_assistant_group *group = NULL;
    struct zpn_assistantgroup_assistant_relation *ag_relation;
    size_t count = 1;

    if (asst) {
        asst->monitor_count++;
        int64_t asst_id = fohh_peer_get_id(asst->f_conn);

        if (asst->customer_gid != 0) {
            /* Drop connection when customer is disabled */
            res = zpath_customer_get(asst->customer_gid,
                                     &customer,
                                     NULL,
                                     NULL,
                                     0);

            if (res == ZPN_RESULT_NOT_FOUND || (res == ZPN_RESULT_NO_ERROR && customer->disable_traffic == 1)) {
                SITEC_LOG(AL_NOTICE, "%s: Disconnecting assistant(%"PRId64") as customer(%"PRId64") is not found or is disabled",
                            fohh_description(asst->f_conn), asst_id, asst->customer_gid);
                /* Kill the connection */
                fohh_connection_delete(asst->f_conn, ZPN_ERR_CUSTOMER_DISABLED);
                return;
            }
        }

        if (!is_scope_default(asst->scope_gid)) {

            int is_scope_enabled = 0;
            int result = ZPN_RESULT_NO_ERROR;
            enum A2SCAuth old_status = asst->scope_auth_status;

            result = zpn_is_scope_enabled(asst->scope_gid, &is_scope_enabled);
            if((result != ZPN_RESULT_NO_ERROR) || !is_scope_enabled ||
                    (!is_scope_default(gs->sitec_scope_id) && (asst->scope_gid != gs->sitec_scope_id))) {
                asst->scope_auth_status = AUTH_UNSUCCESSFUL;
            } else {
                asst->scope_auth_status = AUTH_SUCCESSFUL;
            }

            if(asst->scope_auth_status != old_status) {
                /*
                 * We log a message only when there is a change in status. This will limit the number of log messages being generated.
                 */

                SITEC_LOG(AL_DEBUG, "asst to sitec scope auth status changed. old_status:\"%s\" new_status:\"%s\". customer_gid:%"PRId64" sitec[gid:%"PRId64" scope_gid:%"PRId64"] "
                                  "assistant[gid:%"PRId64" scope_gid:%"PRId64" scope_enabled:%d]",
                                  (old_status == AUTH_SUCCESSFUL) ? "auth-successful" : "auth-unsuccessful",
                                  (asst->scope_auth_status == AUTH_SUCCESSFUL) ? "auth-successful" : "auth-unsuccessful",
                                  ZPATH_GID_GET_CUSTOMER_GID(gs->sitec_scope_id),
                                  gs->sitec_id,
                                  gs->sitec_scope_id,
                                  asst->scope_gid,
                                  asst->assistant_gid_from_config,
                                  is_scope_enabled);
            }
        }

        if (gs->disable_flags) {
            SITEC_LOG(AL_ERROR, "%s: %s for %"PRId64,
                    fohh_description(asst->f_conn), zpn_sitec_get_disable_flag(), gs->sitec_id);
            fohh_connection_delete(asst->f_conn, ZPN_ERR_SITEC_SERVICE_DISABLED);
            return;
        }

        res = zpn_assistantgroup_assistant_relation_get_by_assistant(asst_id,
                                                                     &ag_relation,
                                                                     &count,
                                                                     NULL,
                                                                     NULL,
                                                                     0);
        if (res != ZPN_RESULT_NO_ERROR) {
            SITEC_LOG(AL_ERROR, "%s: Error fetching assistantgroup_assistant_relation for assistant gid: %ld: %s", fohh_peer_cn(asst->f_conn), (long) asst_id, zpn_result_string(res));
            return;
        }

        res = zpn_assistant_group_get_by_gid(ag_relation->assistant_group_id,
                                             &group,
                                             NULL,
                                             NULL,
                                             0);
        if (res != ZPN_RESULT_NO_ERROR) {
            SITEC_LOG(AL_ERROR, "%s: Error fetching assistant group data for group id: %ld: %s", fohh_peer_cn(asst->f_conn), (long) ag_relation->assistant_group_id, zpn_result_string(res));
            return;
        }

        if (group->site_gid != gs->site_gid) {
            SITEC_LOG(AL_WARNING, "%s: sitec %"PRId64" and assistant %"PRId64" are part of different sites, hence disconnecting", fohh_description(asst->f_conn), gs->sitec_id, asst_id);
            /* Kill the connection */
            fohh_connection_delete(asst->f_conn, ZPN_ERR_SITE_MISMATCH);
            return;
        }

        if (((asst->monitor_count % ZPN_TUNNEL_MONITOR_LOG_INTERVAL_COUNT) == 0) &&
            (asst->status_report_ready && asst->tcp_info_ready)) {
            zpn_sitec_asst_update_asst_lat_lon(asst);
            zpn_sitec_asst_control_conn_auth_log_send(asst, fohh_get_state(asst->f_conn));
        }
    } else {
        SITEC_LOG(AL_NOTICE, "Assistant control conn monitor cb has no c_state");
    }
}

static void zpn_sitec_asst_control_destroy(struct connected_sitec_asst_control_fohh_state *asst)
{
    if (!asst) return;

    struct fohh_connection *f_conn = asst->f_conn;
    if (f_conn) {
        SITEC_LOG(AL_NOTICE, "%s: sitec Assistant control connection DOWN, Assistant ID = %"PRId64,
                fohh_description(f_conn), asst->assistant_gid_from_config);
    } else {
        SITEC_LOG(AL_NOTICE, "Stale sitec assistant control connection DOWN, Assistant ID = %"PRId64,
                asst->assistant_gid_from_config);
    }

    if (asst->timer) {
        event_free(asst->timer);
        asst->timer = NULL;
    }

    zpn_sitec_asst_control_conn_auth_log_send(asst, fohh_connection_disconnected);

    if (asst->version) SITEC_FREE(asst->version);
    if (asst->sarge_version) SITEC_FREE(asst->sarge_version);
    if (asst->dft_rt_intf) SITEC_FREE(asst->dft_rt_intf);
    if (asst->platform) SITEC_FREE(asst->platform);
    if (asst->platform_detail) SITEC_FREE(asst->platform_detail);
    if (asst->runtime_os) SITEC_FREE(asst->runtime_os);
    if (asst->auth_log.gids) {
        SITEC_FREE(asst->auth_log.gids);
        asst->auth_log.gids = NULL;
        asst->auth_log.gids_count = 0;
    }

    if (asst->auth_log.dft_rt_intf) {
        SITEC_FREE(asst->auth_log.dft_rt_intf);
        asst->auth_log.dft_rt_intf = NULL;
    }

    if (asst->auth_log.cc) {
        SITEC_FREE(asst->auth_log.cc);
        asst->auth_log.cc = NULL;
    }
    SITEC_FREE(asst);

    if (f_conn) {
        fohh_connection_set_dynamic_cookie(f_conn, NULL);
    }

    /*if (f_conn) {
        zpn_fohh_worker_assistant_disconnect_control(fohh_connection_get_thread_id(f_conn));
    }*/
}

static int zpn_sitec_asst_status_report_cb(void *argo_cookie_ptr,
                                           void *argo_structure_cookie_ptr,
                                           struct argo_object *object)
{
    struct fohh_connection *f_conn = argo_structure_cookie_ptr;
    struct zpn_assistant_status_report *req = object->base_structure_void;
    struct connected_sitec_asst_control_fohh_state *asst;

    asst = fohh_connection_get_dynamic_cookie(f_conn);
    if (asst) {
        asst->rx_status_report++;
        asst->auth_log.cpu_util = req->cpu_util;
        asst->auth_log.mem_util = req->mem_util;
        asst->auth_log.service_count = req->service_count;
        asst->proc_fd_util = req->proc_fd_util;
        asst->sys_fd_util = req->sys_fd_util;
        asst->udp4_port_util = req->udp4_port_util;
        asst->udp6_port_util = req->udp6_port_util;

        asst->delta_from_prev_auth_log.delta_mtunnel_count += req->delta_mtunnel_count;
        asst->auth_log.current_target_count = req->current_target_count;
        asst->auth_log.total_mtunnel_count = req->total_mtunnel_count;

        if (asst->auth_log.dft_rt_intf) {
            SITEC_FREE(asst->auth_log.dft_rt_intf);
        }
        if (req->dft_rt_intf) {
            asst->auth_log.dft_rt_intf = SITEC_STRDUP(req->dft_rt_intf, strlen(req->dft_rt_intf));
        } else {
            asst->auth_log.dft_rt_intf = NULL;
        }
        asst->auth_log.dft_rt_gw = req->dft_rt_gw;
        asst->auth_log.resolver = req->resolver;
        asst->auth_log.sys_uptime_s = req->sys_uptime_s;
        asst->auth_log.ast_uptime_s = req->ast_uptime_s;
        asst->auth_log.intf_count = req->intf_count;
        asst->auth_log.intf_rb = req->intf_rb;
        asst->auth_log.intf_rp = req->intf_rp;
        asst->auth_log.intf_re = req->intf_re;
        asst->auth_log.intf_rd = req->intf_rd;
        asst->auth_log.intf_tb = req->intf_tb;
        asst->auth_log.intf_tp = req->intf_tp;
        asst->auth_log.intf_te = req->intf_te;
        asst->auth_log.intf_td = req->intf_td;

        asst->auth_log.delta_intf_rb += req->intf_rb;
        asst->auth_log.delta_intf_rp += req->intf_rp;
        asst->auth_log.delta_intf_re += req->intf_re;
        asst->auth_log.delta_intf_rd += req->intf_rd;
        asst->auth_log.delta_intf_tb += req->intf_tb;
        asst->auth_log.delta_intf_tp += req->intf_tp;
        asst->auth_log.delta_intf_te += req->intf_te;
        asst->auth_log.delta_intf_td += req->intf_td;

        asst->auth_log.total_intf_b = req->total_intf_b;
        asst->auth_log.total_intf_p = req->total_intf_p;
        asst->auth_log.total_intf_e = req->total_intf_e;
        asst->auth_log.total_intf_d = req->total_intf_d;
        asst->auth_log.delta_total_intf_b += req->delta_total_intf_b;
        asst->auth_log.delta_total_intf_p += req->delta_total_intf_p;
        asst->auth_log.delta_total_intf_e += req->delta_total_intf_e;
        asst->auth_log.delta_total_intf_d += req->delta_total_intf_d;
        zpn_sitec_asst_update_asst_lat_lon(asst);

        if (req->platform) {
            if (asst->platform) SITEC_FREE(asst->platform);
            asst->platform = SITEC_STRDUP(req->platform, strlen(req->platform));
        }

        if (req->platform_detail) {
            if (asst->platform_detail) SITEC_FREE(asst->platform_detail);
            asst->platform_detail = SITEC_STRDUP(req->platform_detail, strlen(req->platform_detail));
        }

        if (req->runtime_os) {
            if (asst->runtime_os) SITEC_FREE(asst->runtime_os);
            asst->runtime_os = SITEC_STRDUP(req->runtime_os, strnlen(req->runtime_os, FOHH_MAX_NAMELEN));
        }

        asst->auth_log.platform = asst->platform;
        asst->auth_log.platform_detail = asst->platform_detail;
        asst->auth_log.runtime_os = asst->runtime_os;

        snprintf(asst->auth_log.log_broker_cn, sizeof(asst->auth_log.log_broker_cn), "%s", req->log_broker_cn);
        snprintf(asst->auth_log.ovd_broker_cn, sizeof(asst->auth_log.ovd_broker_cn), "%s", req->ovd_broker_cn);
        asst->status_report_ready = 1;

        /* Send the first auth log only when we have all the required info */
        if (!asst->first_auth_sent && asst->status_report_ready && asst->tcp_info_ready) {
            zpn_sitec_asst_control_conn_auth_log_send(asst, fohh_connection_connected);
        }
    }

    return ZPN_RESULT_NO_ERROR;
}

static int zpn_sitec_asst_tcp_info_report_control_cb(void *argo_cookie_ptr,
                                                     void *argo_structure_cookie_ptr,
                                                     struct argo_object *object)
{
    struct fohh_connection *f_conn = argo_structure_cookie_ptr;
    struct connected_sitec_asst_control_fohh_state *asst = fohh_connection_get_dynamic_cookie(f_conn);
    struct zpn_tcp_info_report *req = object->base_structure_void;

    if (asst) {
        asst->rx_tcp_info_report++;
        asst->auth_log.priv_ip = req->priv_ip;

        if (req->version) {
            if (asst->version) {
                SITEC_FREE(asst->version);
            }
            asst->version = SITEC_STRDUP(req->version, strlen(req->version));
            asst->auth_log.version = asst->version;
            asst->version_major = 0;
            asst->version_minor = 0;
            if (sscanf(asst->version, "%d.%d", &(asst->version_major), &(asst->version_minor)) != 2) {
                SITEC_LOG(AL_WARNING, "Could not pull assistant version from %s; assistant %ld", asst->version, (long)asst->assistant_gid_from_config);
            }
        } else {
            asst->version_major = 0;
            asst->version_minor = 0;
        }
        asst->tcp_info_ready = 1;

        /* Send the first auth log only when we have all the required info */
        if (!asst->first_auth_sent && asst->status_report_ready && asst->tcp_info_ready) {
            zpn_sitec_asst_control_conn_auth_log_send(asst, fohh_connection_connected);
        }
    } else {
        SITEC_LOG(AL_ERROR, "Could not find assistant for %s", fohh_peer_cn(f_conn));
    }

    return ZPN_RESULT_NO_ERROR;
}

static int zpn_sitec_asst_environment_report_cb(void*                 argo_cookie_ptr,
                                                void*                 argo_structure_cookie_ptr,
                                                struct argo_object*   object)
{
    struct fohh_connection *f_conn = argo_structure_cookie_ptr;
    struct connected_sitec_asst_control_fohh_state *asst = fohh_connection_get_dynamic_cookie(f_conn);
    struct zpn_asst_environment_report *msg = object->base_structure_void;
    if (NULL == asst) {
        goto done;
    }
    asst->rx_environment_report++;
    if (asst->sarge_version) {
        SITEC_FREE(asst->sarge_version);
    }
    asst->sarge_version = SITEC_STRDUP(msg->sarge_version, strlen(msg->sarge_version));
    asst->auth_log.sarge_version = asst->sarge_version;

done:
    return ZPN_RESULT_NO_ERROR;
}

static int zpn_sitec_asst_control_conn_callback(struct fohh_connection *connection,
                                                enum fohh_connection_state state,
                                                void *cookie)
{
    struct argo_state *argo;
    struct connected_sitec_asst_control_fohh_state *asst;
    struct zpn_assistant *assistant = NULL;

    if (state == fohh_connection_connected) {
        int res;
        int64_t asst_id = 0;
        int64_t customer_gid = 0;
        struct zpath_customer *customer = NULL;

        /* Allocate state for this connection, so we can track its stuff... */
        asst_id = fohh_peer_get_id(connection);
        if (!asst_id) return FOHH_RESULT_ERR;

        SITEC_LOG(AL_INFO, "%s: sitec received assistant control connection, Assistant ID = %"PRId64,
                            fohh_description(connection), asst_id);

        fohh_connection_set_dynamic_cookie(connection, NULL);

        /* Drop connection when customer is disabled */
        customer_gid = ZPATH_GID_GET_CUSTOMER_GID(asst_id);
        if (customer_gid != 0) {
            res = zpath_customer_get(customer_gid,
                                     &customer,
                                     NULL,
                                     NULL,
                                     0);

            if (res == ZPN_RESULT_NOT_FOUND || (res == ZPN_RESULT_NO_ERROR && customer->disable_traffic == 1)) {
                SITEC_LOG(AL_NOTICE, "%s: Could not accept connection with assistant(%"PRId64"), customer(%"PRId64") is not found or is disabled",
                            fohh_description(connection), asst_id, customer_gid);
                return ZPN_RESULT_ERR;
            }
        }

        argo = fohh_argo_get_rx(connection);

        if ((res = argo_register_structure(argo, zpn_assistant_status_report_description, zpn_sitec_asst_status_report_cb, connection))) {
            SITEC_LOG(AL_ERROR, "Could not register zpn_broker_assistant_status_report for broker connection %s", fohh_description(connection));
            return res;
        }

        if ((res = argo_register_structure(argo, zpn_tcp_info_report_description, zpn_sitec_asst_tcp_info_report_control_cb, connection))) {
            SITEC_LOG(AL_ERROR, "Could not register zpn_tcp_info_report for connection %s", fohh_description(connection));
            return res;
        }

        if ((res = argo_register_structure(argo, zpn_asst_environment_report_description,
                                                            zpn_sitec_asst_environment_report_cb, connection) )) {
            SITEC_LOG(AL_ERROR, "Could not register zpn_asst_environment_report for connection %s",
                    fohh_description(connection));
            return res;
        }

        asst = SITEC_CALLOC(sizeof(*asst));
        if (!asst) {
            SITEC_LOG(AL_ERROR, "Memory");
            return FOHH_RESULT_NO_MEMORY;
        }

        /* Generate an ID for this client */
        res = RAND_bytes(&(asst->tunnel_id_bin[0]), ZPN_TUNNEL_ID_BYTES);
        if (1 != res) {
            ZPN_SITEC_ASSERT_HARD(0, "Crypto Random Generator not seeded!");
            SITEC_FREE(asst);
            return res;
        }
        base64_encode_binary(asst->tunnel_id, asst->tunnel_id_bin, ZPN_TUNNEL_ID_BYTES);

        asst->auth_log.tunnel_id = asst->tunnel_id;
        asst->auth_log.status = ZPN_STATUS_AUTHENTICATED;

        asst->assistant_gid_from_config = asst_id;
        asst->f_conn = connection;
        asst->f_conn_incarnation = fohh_connection_incarnation(connection);
        zpn_sitec_asst_conn_tunnel_fohh_store(asst->tunnel_id, ZPN_TUNNEL_ID_BYTES_TEXT, asst, &zpn_sitec_asst_control_conn);
        fohh_connection_set_dynamic_cookie(connection, asst);

        {
            /* Fetch geoIP and gid from assistant group. Note- these were
             * prefetched during authentication, so should be
             * available directly */
            struct zpn_assistantgroup_assistant_relation *ag_relation;
            size_t count = 1;
            res = zpn_assistantgroup_assistant_relation_get_by_assistant(asst_id,
                                                                         &ag_relation,
                                                                         &count,
                                                                         NULL,
                                                                         NULL,
                                                                         0);
            if (res) {
                SITEC_LOG(AL_WARNING, "%s: Error fetching assistantgroup_assistant_relation: %ld: %s", fohh_peer_cn(connection), (long) asst_id, zpn_result_string(res));
            } else {
                asst->g_ast_grp = ag_relation->assistant_group_id;
                asst->auth_log.g_ast_grp = ag_relation->assistant_group_id;

                if (zpn_sitec_asst_update_asst_lat_lon(asst)){
                    SITEC_LOG(AL_WARNING, "Error fetching assistant_group: %ld: %s", (long) asst->g_ast_grp, zpn_result_string(res));
                }

                if ((asst->a_lat == 0) && (asst->a_lon == 0) && !geoip_db_file) {
                    SITEC_LOG(AL_ERROR, "geoip_db_file is NULL");
                }
            }
        }

        /*
         * Fetch assistant from zpn_assistant table. Note:- this is
         * prefetched during authentication, so should be
         * available directly
         */
        res = zpn_assistant_get_by_id(asst_id,
                                      &assistant,
                                      NULL,
                                      NULL,
                                      0);
        if (res) {
            SITEC_LOG(AL_WARNING, "Error fetching assistant %ld: %s", (long) asst_id,
                                 zpn_result_string(res));
        }

        if(res != ZPN_RESULT_NO_ERROR) {
            fohh_connection_set_dynamic_cookie(connection, NULL);
            SITEC_FREE(asst);
            return res;
        } else {
            asst->customer_gid = assistant->customer_gid;
            asst->scope_gid    = assistant->scope_gid;
            asst->auth_log.g_microtenant = is_scope_default(assistant->scope_gid) ? 0 : assistant->scope_gid;
        }

        asst->timer = event_new(fohh_get_thread_event_base(fohh_connection_get_thread_id(connection)),
                                -1,
                                EV_PERSIST,
                                zpn_sitec_asst_control_conn_monitor_cb,
                                asst);
        if (!asst->timer) {
            SITEC_LOG(AL_CRITICAL, "Memory");
            SITEC_FREE(asst);
            return FOHH_RESULT_NO_MEMORY;
        }

        struct timeval tv;
        tv.tv_sec = ZPN_TUNNEL_MONITOR_INTERVAL_S;
        tv.tv_usec = 0;
        if (event_add(asst->timer, &tv)) {
            SITEC_LOG(AL_CRITICAL, "Could not add assistant timer");
            event_free(asst->timer);
            SITEC_FREE(asst);
            return FOHH_RESULT_NO_MEMORY;
        }

        //zpn_fohh_worker_assistant_connect_control(fohh_connection_get_thread_id(connection));

        SITEC_LOG(AL_NOTICE, "%s: sitec asst control connection UP, Assistant ID = %"PRId64", Customer GID = %"PRId64,
                fohh_description(connection), asst_id, asst->customer_gid);

    } else {
        /* Connection probably went away... */
        const char *reason = fohh_close_reason(connection);
        SITEC_LOG(AL_NOTICE, "%s: sitec asst control connection DOWN due to %s",
                fohh_description(connection), reason);

        asst = fohh_connection_get_dynamic_cookie(connection);
        if (asst) {
            asst->auth_log.close_reason = reason;
            asst->auth_log.status = ZPN_STATUS_DISCONNECTED;
            zpn_sitec_asst_conn_tunnel_fohh_remove(asst->tunnel_id, ZPN_TUNNEL_ID_BYTES_TEXT, &zpn_sitec_asst_control_conn);
            zpn_sitec_asst_control_destroy(asst);
        }
    }

    return FOHH_RESULT_NO_ERROR;
}

static int zpn_sitec_asst_unblock_callback(struct fohh_connection *connection,
                                           enum fohh_queue_element_type element_type,
                                           void *cookie)
{
    return FOHH_RESULT_NO_ERROR;
}

static void zpn_sitec_asst_config_conn_monitor_cb(evutil_socket_t sock, short flags, void *cookie)
{
    int res;
    struct zpath_customer *customer = NULL;
    struct connected_sitec_asst_config_fohh_state *asst = cookie;
    struct zpn_assistant_group *group = NULL;
    struct zpn_assistantgroup_assistant_relation *ag_relation;
    size_t count = 1;
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();

    if (asst) {
        int64_t asst_id = fohh_peer_get_id(asst->f_conn);

        asst->monitor_count++;

        if (asst->customer_gid != 0) {
            /* Drop connection when customer is disabled */
            res = zpath_customer_get(asst->customer_gid,
                                     &customer,
                                     NULL,
                                     NULL,
                                     0);

            if (res == ZPN_RESULT_NOT_FOUND || (res == ZPN_RESULT_NO_ERROR && customer->disable_traffic == 1)) {
                SITEC_LOG(AL_NOTICE, "%s: Disconnecting assistant(%"PRId64") as customer(%"PRId64") is not found or is disabled",
                            fohh_description(asst->f_conn), asst_id, asst->customer_gid);
                /* Kill the connection */
                fohh_connection_delete(asst->f_conn, ZPN_ERR_CUSTOMER_DISABLED);
                return;
            }
        }

        if (gs->disable_flags) {
            SITEC_LOG(AL_ERROR, "%s: %s for %"PRId64,
                    fohh_description(asst->f_conn), zpn_sitec_get_disable_flag(), gs->sitec_id);
            fohh_connection_delete(asst->f_conn, ZPN_ERR_SITEC_SERVICE_DISABLED);
            return;
        }

        res = zpn_assistantgroup_assistant_relation_get_by_assistant(asst_id,
                                                                     &ag_relation,
                                                                     &count,
                                                                     NULL,
                                                                     NULL,
                                                                     0);
        if (res != ZPN_RESULT_NO_ERROR) {
            SITEC_LOG(AL_ERROR, "%s: Error fetching assistantgroup_assistant_relation for assistant gid: %ld: %s", fohh_peer_cn(asst->f_conn), (long) asst_id, zpn_result_string(res));
            return;
        }

        res = zpn_assistant_group_get_by_gid(ag_relation->assistant_group_id,
                                             &group,
                                             NULL,
                                             NULL,
                                             0);
        if (res != ZPN_RESULT_NO_ERROR) {
            SITEC_LOG(AL_ERROR, "%s: Error fetching assistant group data for group id: %ld: %s", fohh_peer_cn(asst->f_conn), (long) ag_relation->assistant_group_id, zpn_result_string(res));
            return;
        }

        if (group->site_gid != gs->site_gid) {
            SITEC_LOG(AL_WARNING, "%s: sitec %"PRId64" and assistant %"PRId64" are part of different sites, hence disconnecting", fohh_description(asst->f_conn), gs->sitec_id, asst_id);
            /* Kill the connection */
            fohh_connection_delete(asst->f_conn, ZPN_ERR_SITE_MISMATCH);
            return;
        }
    } else {
        SITEC_LOG(AL_NOTICE, "Assistant config conn monitor cb has no state");
    }
}

static int zpn_sitec_asst_get_scope_gid(int64_t asst_id, int64_t *scope_gid)
{
    struct zpn_assistant *assistant = NULL;
    int result = ZPN_RESULT_NO_ERROR;

    result = zpn_assistant_get_by_id(asst_id, &assistant, NULL, NULL, 0);
    if (result || !assistant) {
        SITEC_LOG(AL_ERROR, "Cannot find the assistant with Assistant ID = %" PRId64, asst_id);
        return ZPN_RESULT_ERR;
    }

    *scope_gid = assistant->scope_gid;
    return result;
}

static int zpn_sitec_asst_create_config_conn_cookie(struct fohh_connection *f_conn,
                                                    int64_t asst_gid,
                                                    int is_override)
{
    struct connected_sitec_asst_config_fohh_state *asst = SITEC_CALLOC(sizeof(*asst));
    int result;

    /* Generate an ID for this client */
    const int res = RAND_bytes(&(asst->tunnel_id_bin[0]), ZPN_TUNNEL_ID_BYTES);
    if (1 != res) {
        ZPN_SITEC_ASSERT_HARD(0, "Crypto Random Generator not seeded!");
        SITEC_FREE(asst);
        return FOHH_RESULT_ERR;
    }
    base64_encode_binary(asst->tunnel_id, asst->tunnel_id_bin, ZPN_TUNNEL_ID_BYTES);

    asst->timer = event_new(fohh_get_thread_event_base(fohh_connection_get_thread_id(f_conn)),
                            -1,
                            EV_PERSIST,
                            zpn_sitec_asst_config_conn_monitor_cb,
                            asst);
    if (!asst->timer) {
        SITEC_LOG(AL_CRITICAL, "Memory");
        SITEC_FREE(asst);
        return FOHH_RESULT_NO_MEMORY;
    }

    struct timeval tv;
    tv.tv_sec = ZPN_TUNNEL_MONITOR_INTERVAL_S;;
    tv.tv_usec = 0;
    if (event_add(asst->timer, &tv)) {
        SITEC_LOG(AL_CRITICAL, "Could not add assistant timer");
        event_free(asst->timer);
        SITEC_FREE(asst);
        return FOHH_RESULT_NO_MEMORY;
    }

    asst->is_override = is_override;
    asst->assistant_gid_from_config = asst_gid;
    asst->f_conn = f_conn;
    asst->f_conn_incarnation = fohh_connection_incarnation(f_conn);
    asst->customer_gid = ZPATH_GID_GET_CUSTOMER_GID(asst_gid);

    result = zpn_sitec_asst_get_scope_gid(asst_gid, &asst->scope_gid);
    if(result != ZPN_RESULT_NO_ERROR) {
        SITEC_LOG(LOG_ERR, "Failed to fetch scope_gid for assistant %"PRId64"\n", asst_gid);
        SITEC_FREE(asst);
        return FOHH_RESULT_ERR;
    }
    zpn_sitec_asst_conn_tunnel_fohh_store(asst->tunnel_id, ZPN_TUNNEL_ID_BYTES_TEXT, asst, &zpn_sitec_asst_config_conn);
    fohh_connection_set_dynamic_cookie(f_conn, asst);
    return FOHH_RESULT_NO_ERROR;
}

static void zpn_sitec_asst_destroy_config_conn_cookie(struct fohh_connection *f_conn)
{
    struct connected_sitec_asst_config_fohh_state *asst = fohh_connection_get_dynamic_cookie(f_conn);
    if (asst) {
        if (asst->timer) {
            event_free(asst->timer);
        }
        zpn_sitec_asst_conn_tunnel_fohh_remove(asst->tunnel_id, ZPN_TUNNEL_ID_BYTES_TEXT, &zpn_sitec_asst_config_conn);
        fohh_connection_set_dynamic_cookie(f_conn, NULL);
        SITEC_FREE(asst);
    }
}

static int zpn_sitec_asst_config_conn_callback(struct fohh_connection *connection,
                                               enum fohh_connection_state state,
                                               void *cookie)
{
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();

    static char *filter_tables[] = {
        "zpath_customer",
        "zpn_app_group_relation",
        "zpn_app_server",
        "zpn_application",
        "zpn_application_group",
        "zpn_application_group_application_mapping",
        "zpn_assistant",
        "zpn_assistant_group",
        "zpn_assistant_version",
        "zpn_assistantgroup_assistant_relation",
        "zpn_command_probe",
        "zpn_server_group",
        "zpn_server_group_assistant_group",
        "zpn_servergroup_server_relation",
        "zpn_private_broker_load",
        "zpn_private_broker",
        "zpn_private_broker_group",
        "zpn_private_broker_to_group",
        "zpn_inspection_application",
        "zpn_public_cert",
        "zpn_inspection_profile",
        "zpn_inspection_profile_to_control",
        "zpn_inspection_prof_to_zsdefined_ctrl",
        "zpn_sub_module_upgrade",
        "zpn_site",
        "zpn_ddil_config",
        "zpn_firedrill_site"
    };
    static int filter_tables_count = sizeof(filter_tables) / sizeof(filter_tables[0]);

    if (state == fohh_connection_connected) {
        /* Allocate state for this connection, so we can track its stuff... */
        int res;
        int64_t customer_gid = 0;
        struct zpath_customer *customer = NULL;
        int64_t gid = fohh_peer_get_id(connection);

        /* Drop connection when customer is disabled */
        customer_gid = ZPATH_GID_GET_CUSTOMER_GID(gid);
        if (customer_gid != 0) {
            res = zpath_customer_get(customer_gid,
                                     &customer,
                                     NULL,
                                     NULL,
                                     0);

            if (res == ZPN_RESULT_NOT_FOUND || (res == ZPN_RESULT_NO_ERROR && customer->disable_traffic == 1)) {
                SITEC_LOG(AL_NOTICE, "%s: Could not accept connection with assistant(%"PRId64"), customer(%"PRId64") is not found or is disabled",
                            fohh_description(connection), gid, customer_gid);
                return FOHH_RESULT_ERR;
            }
        }

        if (!wally_fohh_create_fohh_from_client(gs->sitec_state->cfg_wally_server,
                                                connection,
                                                NULL,
                                                0,
                                                filter_tables,
                                                filter_tables_count,
                                                ZPATH_GID_MASK_CUSTOMER,
                                                ZPATH_GID_MASK_CUSTOMER & gid,
                                                0,
                                                NULL,
                                                NULL,
                                                zpn_sitec_asst_config_conn_callback)) {
            SITEC_LOG(AL_ERROR, "%s: Could not hand off FOHH to wally for assistant config", fohh_description(connection));
            return FOHH_RESULT_ERR;
        }
        SITEC_LOG(AL_NOTICE, "%s: Assistant config connection to sitec UP", fohh_description(connection));

        res = zpn_sitec_asst_create_config_conn_cookie(connection, gid, 0);
        if (res != FOHH_RESULT_NO_ERROR) {
            return res;
        }

    } else {
        /* Connection probably went away... */
        if (fohh_get_state(connection) == fohh_connection_connected) {
            /* We only count report connection going down when previous state is CONNECTED */
            SITEC_LOG(AL_NOTICE, "%s: Assistant config connection DOWN", fohh_description(connection));

            zpn_sitec_asst_destroy_config_conn_cookie(connection);
        }
        return FOHH_RESULT_ERR;
    }

    return FOHH_RESULT_NO_ERROR;
}

static int zpn_sitec_asst_config_unblock_callback(struct fohh_connection *connection,
                                                  enum fohh_queue_element_type element_type,
                                                  void *cookie)
{
    return FOHH_RESULT_NO_ERROR;
}

static int zpn_sitec_asst_config_override_conn_callback(struct fohh_connection *connection,
                                                        enum fohh_connection_state state,
                                                        void *cookie)
{
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();

    static char *filter_tables[] = {
        "zpath_config_override",
        "zpn_inspection_config_data",
        "zpn_inspection_zsdefined_control",
    };
    static int filter_tables_count = sizeof(filter_tables) / sizeof(filter_tables[0]);

    if (state == fohh_connection_connected) {
        /* Allocate state for this connection, so we can track its stuff... */
        int res;
        int64_t customer_gid = 0;
        struct zpath_customer *customer = NULL;
        int64_t gid = fohh_peer_get_id(connection);
        if (!gid) {
            SITEC_LOG(AL_ERROR, "%s: Broker received incorrect assistant config override connection, Assistant ID = %"PRId64,
                    fohh_description(connection), gid);
            return FOHH_RESULT_ERR;
        }

        /* Drop connection when customer is disabled */
        customer_gid = ZPATH_GID_GET_CUSTOMER_GID(gid);
        if (customer_gid != 0) {
            res = zpath_customer_get(customer_gid,
                                     &customer,
                                     NULL,
                                     NULL,
                                     0);

            if (res == ZPN_RESULT_NOT_FOUND || (res == ZPN_RESULT_NO_ERROR && customer->disable_traffic == 1)) {
                SITEC_LOG(AL_NOTICE, "%s: Could not accept connection with assistant(%"PRId64"), customer(%"PRId64") is not found or is disabled",
                            fohh_description(connection), gid, customer_gid);
                return FOHH_RESULT_ERR;
            }
        }

        int64_t global_gid = ZPATH_GLOBAL_CONFIG_OVERRIDE_GID;
        int64_t global_insp_gid = ZPN_GLOBAL_INSPECTION_CONFIG_GID;
        struct filter_entry entries[] = {
                                         {"zpath_config_override",            "customer_gid",       &customer_gid, sizeof(int64_t)},
                                         {"zpath_config_override",            "customer_gid",       &global_gid, sizeof(int64_t)},
                                         {"zpn_inspection_config_data",       "customer_gid",       &customer_gid, sizeof(int64_t)},
                                         {"zpn_inspection_config_data",       "customer_gid",       &global_insp_gid, sizeof(int64_t)},
                                         {"zpn_inspection_zsdefined_control", "customer_gid",       &customer_gid, sizeof(int64_t)},
                                         {"zpn_inspection_zsdefined_control", "customer_gid",       &global_insp_gid, sizeof(int64_t)}
                                        };
        struct zhash_table* table_filter = NULL;
        if (add_multiple_filter_keys(entries, sizeof(entries)/sizeof(struct filter_entry), &table_filter) != WALLY_RESULT_NO_ERROR) {
            SITEC_LOG(AL_ERROR, "Failed to add filter table entries");
            return FOHH_RESULT_ERR;
        }

        if (!wally_fohh_create_fohh_from_client(gs->sitec_state->ovd_wally_server,
                                                connection,
                                                NULL,
                                                0,
                                                filter_tables,
                                                filter_tables_count,
                                                ZPATH_GID_MASK_CUSTOMER,
                                                customer_gid,
                                                global_gid,
                                                table_filter,
                                                NULL,
                                                zpn_sitec_asst_config_override_conn_callback)) {
            SITEC_LOG(AL_ERROR, "%s: Could not hand off FOHH to wally for assistant override", fohh_description(connection));
            return FOHH_RESULT_ERR;
        }
        SITEC_LOG(AL_NOTICE, "%s: Assistant config override connection UP", fohh_description(connection));

        res = zpn_sitec_asst_create_config_conn_cookie(connection, gid, 1);
        if (res != FOHH_RESULT_NO_ERROR) {
            return res;
        }
    } else {
        /* Connection probably went away... */
        if (fohh_get_state(connection) == fohh_connection_connected) {
            /* We only count report connection going down when previous state is CONNECTED */
            SITEC_LOG(AL_NOTICE, "%s: Assistant config override connection to sitec DOWN", fohh_description(connection));

            zpn_sitec_asst_destroy_config_conn_cookie(connection);
        }
        return FOHH_RESULT_ERR;
    }

    return FOHH_RESULT_NO_ERROR;
}

static int zpn_sitec_asst_config_override_unblock_callback(struct fohh_connection *connection,
                                                           enum fohh_queue_element_type element_type,
                                                           void *cookie)
{
    return FOHH_RESULT_NO_ERROR;
}

static void zpn_sitec_asst_stats_conn_monitor_cb(evutil_socket_t sock, short flags, void *cookie)
{
    int res;
    struct zpath_customer *customer = NULL;
    struct connected_sitec_asst_stats_fohh_state *asst = cookie;
    struct zpn_assistant_group *group = NULL;
    struct zpn_assistantgroup_assistant_relation *ag_relation;
    size_t count = 1;
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();

    if (asst) {
        int64_t asst_id = fohh_peer_get_id(asst->f_conn);

        asst->monitor_count++;

        if (asst->customer_gid != 0) {
            /* Drop connection when customer is disabled */
            res = zpath_customer_get(asst->customer_gid,
                                     &customer,
                                     NULL,
                                     NULL,
                                     0);

            if (res == ZPN_RESULT_NOT_FOUND || (res == ZPN_RESULT_NO_ERROR && customer->disable_traffic == 1)) {
                SITEC_LOG(AL_NOTICE, "%s: Disconnecting assistant(%"PRId64") as customer(%"PRId64") is not found or is disabled",
                            fohh_description(asst->f_conn), asst_id, asst->customer_gid);
                /* Kill the connection */
                fohh_connection_delete(asst->f_conn, ZPN_ERR_CUSTOMER_DISABLED);
                return;
            }
        }

        if (gs->disable_flags) {
            SITEC_LOG(AL_ERROR, "%s: %s for %"PRId64,
                    fohh_description(asst->f_conn), zpn_sitec_get_disable_flag(), gs->sitec_id);
            fohh_connection_delete(asst->f_conn, ZPN_ERR_SITEC_SERVICE_DISABLED);
            return;
        }

        res = zpn_assistantgroup_assistant_relation_get_by_assistant(asst_id,
                                                                     &ag_relation,
                                                                     &count,
                                                                     NULL,
                                                                     NULL,
                                                                     0);
        if (res != ZPN_RESULT_NO_ERROR) {
            SITEC_LOG(AL_ERROR, "%s: Error fetching assistantgroup_assistant_relation for assistant gid: %ld: %s", fohh_peer_cn(asst->f_conn), (long) asst_id, zpn_result_string(res));
            return;
        }

        res = zpn_assistant_group_get_by_gid(ag_relation->assistant_group_id,
                                             &group,
                                             NULL,
                                             NULL,
                                             0);
        if (res != ZPN_RESULT_NO_ERROR) {
            SITEC_LOG(AL_ERROR, "%s: Error fetching assistant group data for group id: %ld: %s", fohh_peer_cn(asst->f_conn), (long) ag_relation->assistant_group_id, zpn_result_string(res));
            return;
        }

        if (group->site_gid != gs->site_gid) {
            SITEC_LOG(AL_WARNING, "%s: sitec %"PRId64" and assistant %"PRId64" are part of different sites, hence disconnecting", fohh_description(asst->f_conn), gs->sitec_id, asst_id);
            /* Kill the connection */
            fohh_connection_delete(asst->f_conn, ZPN_ERR_SITE_MISMATCH);
            return;
        }

        if ((asst->monitor_count % ZPN_TUNNEL_MONITOR_LOG_INTERVAL_COUNT) == 0) {
            zpn_sitec_asst_stats_conn_auth_log_send(asst, fohh_get_state(asst->f_conn));
        }
    } else {
        SITEC_LOG(AL_NOTICE, "Assistant stats conn monitor cb has no state");
    }
}

int zpn_sitec_astats_log_upload_cb(void *argo_cookie_ptr,
                                   void *argo_structure_cookie_ptr,
                                   struct argo_object *object)
{
    struct fohh_connection *f_conn = argo_structure_cookie_ptr;
    struct connected_sitec_asst_stats_fohh_state *asst;
    struct argo_log *log = object->base_structure_void;
    struct argo_object *copy_object;
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();
    int res;

    asst = fohh_connection_get_dynamic_cookie(f_conn);
    if (asst && 0 == strncmp("assistant_stats_comprehensive",
                             log->l_name,
                             sizeof("assistant_stats_comprehensive"))) {
        zpn_sitec_siem_ast_comprehensive_stats(log->l_obj->base_structure_void,
                                                   asst->customer_gid,
                                                   asst->tunnel_id,
                                                   asst->assistant_gid_from_config,
                                                   asst->g_ast_grp);
    }

    copy_object = argo_object_copy(object);
    res = fohh_argo_serialize_object(gs->sitec_state->astats_fohh_conn, copy_object,
                                     0, fohh_queue_element_type_control);
    if (res) {
        SITEC_LOG(AL_WARNING, "sending astats failed with error %s", zpn_result_string(res));
    }
    argo_object_release(copy_object);

    return ZPN_RESULT_NO_ERROR;
}

static int zpn_sitec_asst_stats_conn_callback(struct fohh_connection *connection,
                                              enum fohh_connection_state state,
                                              void *cookie)
{
    struct argo_state *argo;
    struct connected_sitec_asst_stats_fohh_state *asst;

    if (state == fohh_connection_connected) {
        int res;
        int64_t asst_id = 0;
        int64_t customer_gid = 0;
        struct zpath_customer *customer = NULL;

        /* Allocate state for this connection, so we can track its stuff... */
        asst_id = fohh_peer_get_id(connection);
        if (!asst_id) return FOHH_RESULT_ERR;

        ZPN_DEBUG_ASSISTANT("%s: sitec received assistant stats connection, Assistant ID = %"PRId64,
                            fohh_description(connection), asst_id);

        fohh_connection_set_dynamic_cookie(connection, NULL);

        /* Drop connection when customer is disabled */
        customer_gid = ZPATH_GID_GET_CUSTOMER_GID(asst_id);
        if (customer_gid != 0) {
            res = zpath_customer_get(customer_gid,
                                     &customer,
                                     NULL,
                                     NULL,
                                     0);

            if (res == ZPN_RESULT_NOT_FOUND || (res == ZPN_RESULT_NO_ERROR && customer->disable_traffic == 1)) {
                SITEC_LOG(AL_NOTICE, "%s: Could not accept connection with assistant(%"PRId64"), customer(%"PRId64") is not found or is disabled",
                            fohh_description(connection), asst_id, customer_gid);
                return FOHH_RESULT_ERR;
            }
        }

        argo = fohh_argo_get_rx(connection);

        /* Register assistant log upload. Connector's stats come as log */
        if ((res = argo_register_structure(argo,
                                           global_argo_log_desc,
                                           zpn_sitec_astats_log_upload_cb,
                                           connection))) {
            SITEC_LOG(AL_ERROR, "Could not register assistant log upload for connection %s", fohh_description(connection));
            return res;
        }

        struct timeval tv;
        asst = SITEC_CALLOC(sizeof(*asst));
        if (!asst) {
            SITEC_LOG(AL_ERROR, "Memory");
            return FOHH_RESULT_NO_MEMORY;
        }

        /* Generate an ID for this client */
        res = RAND_bytes(&(asst->tunnel_id_bin[0]), ZPN_TUNNEL_ID_BYTES);
        if (1 != res) {
            ZPN_SITEC_ASSERT_HARD(0, "Crypto Random Generator not seeded!");
            SITEC_FREE(asst);
            return res;
        }
        base64_encode_binary(asst->tunnel_id, asst->tunnel_id_bin, ZPN_TUNNEL_ID_BYTES);
        asst->auth_log.tunnel_id = asst->tunnel_id;
        asst->auth_log.status = ZPN_STATUS_AUTHENTICATED;

        asst->assistant_gid_from_config = asst_id;
        asst->f_conn = connection;
        zpn_sitec_asst_conn_tunnel_fohh_store(asst->tunnel_id, ZPN_TUNNEL_ID_BYTES_TEXT, asst, &zpn_sitec_asst_astats_conn);
        fohh_connection_set_dynamic_cookie(connection, asst);

        {
            /* Fetch geoIP and gid from assistant group. Note- these were
             * prefetched during authentication, so should be
             * available directly */
            struct zpn_assistantgroup_assistant_relation *ag_relation;
            size_t count = 1;
            res = zpn_assistantgroup_assistant_relation_get_by_assistant(asst_id,
                                                                         &ag_relation,
                                                                         &count,
                                                                         NULL,
                                                                         NULL,
                                                                         0);
            if (res) {
                SITEC_LOG(AL_WARNING, "%s: Error fetching assistantgroup_assistant_relation. Assistant ID = %"PRId64": %s",
                        fohh_peer_cn(connection), asst_id, zpn_result_string(res));
            } else {
                asst->g_ast_grp = ag_relation->assistant_group_id;
            }
        }

        {
            /* Get customer_gid for the assistant */
            struct zpn_assistant *assistant = NULL;

            res = zpn_assistant_get_by_id(asst_id, &assistant, NULL, NULL, 0);
            if (assistant) {
                /* We will always succeed here since this has been called during verification before connection is up */
                asst->customer_gid = assistant->customer_gid;
            } else {
                SITEC_LOG(AL_NOTICE, "Cannot find the assistant with Assistant ID = %"PRId64, asst_id);
            }
        }

        asst->timer = event_new(fohh_get_thread_event_base(fohh_connection_get_thread_id(connection)),
                                -1,
                                EV_PERSIST,
                                zpn_sitec_asst_stats_conn_monitor_cb,
                                asst);
        if (!asst->timer) {
            SITEC_LOG(AL_CRITICAL, "Memory");
            SITEC_FREE(asst);
            return FOHH_RESULT_NO_MEMORY;
        }

        tv.tv_sec = ZPN_TUNNEL_MONITOR_INTERVAL_S;;
        tv.tv_usec = 0;
        if (event_add(asst->timer, &tv)) {
            SITEC_LOG(AL_CRITICAL, "Could not add assistant timer");
            event_free(asst->timer);
            SITEC_FREE(asst);
            return FOHH_RESULT_NO_MEMORY;
        }

        SITEC_LOG(AL_NOTICE, "%s: sitec astats connection UP, Assistant ID = %"PRId64", Customer GID = %"PRId64,
                fohh_description(connection), asst_id, asst->customer_gid);
    } else {
        /* Connection probably went away... */
        const char *reason = fohh_close_reason(connection);
        SITEC_LOG(AL_NOTICE, "%s: Assistant stats connection DOWN due to %s",
                fohh_description(connection), reason);

        asst = fohh_connection_get_dynamic_cookie(connection);
        if (asst) {
            zpn_sitec_asst_stats_conn_auth_log_send(asst, fohh_connection_disconnected);
            if (asst->timer) {
                event_free(asst->timer);
                asst->timer = NULL;
            }
            zpn_sitec_asst_conn_tunnel_fohh_remove(asst->tunnel_id, ZPN_TUNNEL_ID_BYTES_TEXT, &zpn_sitec_asst_astats_conn);
            fohh_connection_set_dynamic_cookie(connection, NULL);
            SITEC_FREE(asst);
        }
    }

    return FOHH_RESULT_NO_ERROR;
}

static int zpn_sitec_asst_stats_unblock_callback(struct fohh_connection *connection,
                                                 enum fohh_queue_element_type element_type,
                                                 void *cookie)
{
    return FOHH_RESULT_NO_ERROR;
}

static int zpn_sitec_asst_ctx_fohh_callback(void *response_callback_cookie,
                                            struct wally_registrant *registrant,
                                            struct wally_table *table,
                                            int64_t request_id,
                                            int row_count)
{
    struct fohh_connection *f_conn = response_callback_cookie;
    if (fohh_connection_incarnation(f_conn) != request_id) {
        return WALLY_RESULT_NO_ERROR;
    }

    fohh_connection_process(f_conn, request_id);
    return WALLY_RESULT_NO_ERROR;
}

static int zpn_sitec_asst_ctx_callback_common(void *conn_cookie,
                                              const char *sni,
                                              const char *sni_suffix,
                                              SSL_CTX **ssl_ctx,
                                              enum zpn_tlv_type tlv_type,
                                              const char *conn_description)
{
    char sub_sni[256];
    size_t sni_suffix_len;
    size_t sni_len;
    int res;
    const char *use_sni = NULL;
    int64_t customer_id = 0;
    int64_t assistant_id;
    struct zpn_assistant *assistant = NULL;
    struct zpath_customer *customer = NULL;
    int is_dtls = (tlv_type == zpn_fohh_tlv) ? 0 : 1;
    wally_response_callback_f *callback_f = (tlv_type == zpn_fohh_tlv) ? zpn_sitec_asst_ctx_fohh_callback : NULL;
    void *callback_cookie = (tlv_type == zpn_fohh_tlv) ? conn_cookie : NULL;
    int64_t cookie_int = (tlv_type == zpn_fohh_tlv) ? fohh_connection_incarnation(conn_cookie) : 0;

    if (isdigit(sni[0])) {
        /*
         * V2 style connector SNI: <assistant_id>.<broker_gid><actl|adata|acfg>.<cloud name>
         *
         * Note: Modern customer provision is domain as
         * CUSTOMER_GID.zpa-customer.com. Thus we check if the GID
         * passed is a customer_GID
         */
        assistant_id = strtoll(sni, NULL, 0);
        if ((assistant_id < 0x0100000000000000ll) ||
            assistant_id == ZPATH_GID_GET_CUSTOMER_GID(assistant_id)) {
            /* Assume it is V1 style enrollment if the ID isn't big enough to include shard. */
            assistant_id = 0;
            SITEC_LOG(AL_INFO, "%s - sni: %s, sni_suffix: %s - V1 style connector enrollment", conn_description, sni, sni_suffix ? sni_suffix : "NULL");
        } else {

            SITEC_LOG(AL_INFO, "%s - sni: %s, sni_suffix: %s, assistant_id: %ld - V2 style connector enrollment",
                                conn_description, sni, sni_suffix ? sni_suffix : "NULL", (long)assistant_id);

            res = zpn_assistant_get_by_id(assistant_id,
                                          &assistant,
                                          callback_f,
                                          callback_cookie,
                                          cookie_int);
            if (res) {
                if (res == ZPN_RESULT_ASYNCHRONOUS) {
                    SITEC_LOG(AL_INFO,"Asynchronous assistant fetch for gid = %ld", (long)assistant_id);
                } else {
                    SITEC_LOG(AL_WARNING, "Error fetching assistant %ld: %s", (long)assistant_id, zpn_result_string(res));
                }
                return res;
            } else {
                customer_id = assistant->customer_gid;
                SITEC_LOG(AL_INFO,"%s: Assistant %ld fetched, cert_id: %ld, customer_gid: %ld",
                                    conn_description, (long)assistant_id, (long) assistant->cert_id, (long) customer_id);

                res = zpath_customer_get(customer_id, &customer, callback_f, callback_cookie, cookie_int);
                if (res) {
                    if (res == ZPN_RESULT_ASYNCHRONOUS) {
                        SITEC_LOG(AL_INFO,"Asynchronous customer fetch for gid = %ld", (long)customer_id);
                    } else {
                        SITEC_LOG(AL_WARNING, "Error fetching customer %ld: %s", (long)customer_id, zpn_result_string(res));
                    }
                    return res;
                }
                use_sni = customer->domain_name;
                SITEC_LOG(AL_INFO,"Customer domain: %s, customer_gid: %ld", use_sni, (long)customer_id);
            }
        }
    } else {
        SITEC_LOG(AL_INFO,"%s - sni: %s, sni_suffix: %s - V1 style connector enrollmentco", conn_description, sni, sni_suffix ? sni_suffix : "NULL");
    }

    if (!sni_suffix) {
        use_sni = sni;
    } else if (!use_sni) {
        /* V1 style connector SNI: <domain>.<acfg|adata|actl>.<cloud name> */
        sni_suffix_len = strlen(sni_suffix) + 1;
        sni_len = strlen(sni);

        if (sni_len <= sni_suffix_len) {
            SITEC_LOG(AL_ERROR, "Expecting SNI longer than root. Sni = %s", sni);
            return ZPN_RESULT_ERR;
        }

        snprintf(sub_sni, sizeof(sub_sni), "%.*s", (int)(sni_len - sni_suffix_len), sni);
        use_sni = sub_sni;
    }

    res = zpn_signing_cert_get_verify_ctx(ssl_ctx,
                                          &customer_id,
                                          use_sni,
                                          0,
                                          callback_f,
                                          conn_cookie,
                                          cookie_int,
                                          is_dtls);
    if (res) {
        if (res == ZPN_RESULT_ASYNCHRONOUS) {
            SITEC_LOG(AL_INFO,"Asynchronous context request for sni = %s", sni);
        } else {
            SITEC_LOG(AL_INFO, "Error context request for sni = %s: %s", sni, zpn_result_string(res));
        }
    } else {
        SITEC_LOG(AL_INFO,"%s: SNI = %s has context", conn_description, sni);
    }
    return res;
}

static int zpn_sitec_asst_ctx_callback(struct fohh_connection *f_conn,
                                       const char *sni,
                                       const char *sni_suffix,
                                       SSL_CTX **ssl_ctx)
{
    return zpn_sitec_asst_ctx_callback_common(f_conn, sni, sni_suffix, ssl_ctx, zpn_fohh_tlv, fohh_description(f_conn));

}

static int zpn_sitec_asst_verify_callback_common(struct zpn_tlv *tlv, int verify_scope)
{
    int res;
    int64_t assistant_gid_derived_from_cert;
    int64_t assistant_gid_fetched_from_wally;
    int64_t customer_gid;
    char *cn = zpn_tlv_peer_cn(tlv);
    X509 *cert;
    struct zpn_assistant *assistant;
    int ip_acl_iter;
    struct argo_inet asst_addr;
    int ip_acl_validation_failed;
    char    *cn_iter;
    struct zpn_assistant_group *group = NULL;
    struct zpath_customer *customer = NULL;
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();
    wally_response_callback_f *callback_f = (tlv->type == zpn_fohh_tlv) ? zpn_sitec_asst_ctx_fohh_callback : NULL;
    void *callback_cookie = (tlv->type == zpn_fohh_tlv) ? (void *)(tlv->conn.f_conn) : (void *)(tlv->conn.z_conn);
    int64_t request_id = (tlv->type == zpn_fohh_tlv) ? fohh_connection_incarnation(tlv->conn.f_conn) : 0;

    if (!cn) {
        SITEC_LOG(AL_WARNING, "No peer CN");
        return ZPN_RESULT_ERR;
    }

    assistant_gid_derived_from_cert = 0;
    for (cn_iter = &(cn[0]); *cn_iter; cn_iter++) {
        if (isdigit(*cn_iter)) {
            assistant_gid_derived_from_cert = strtoll(cn_iter, &cn_iter, 0);
            break;
        }
    }
    if (0 == assistant_gid_derived_from_cert) {
        SITEC_LOG(AL_WARNING, "Could not derive assistant gid from cname(%s)", cn);
        return ZPN_RESULT_ERR;
    }

    /* Drop connection when customer is disabled */
    /* Here we are doing it again for log connections */
    customer_gid = ZPATH_GID_GET_CUSTOMER_GID(assistant_gid_derived_from_cert);
    if (customer_gid != 0) {
        res = zpath_customer_get(customer_gid,
                                 &customer,
                                 NULL,
                                 NULL,
                                 0);

        if (res == ZPN_RESULT_NOT_FOUND || (res == ZPN_RESULT_NO_ERROR && customer->disable_traffic == 1)) {
            SITEC_LOG(AL_NOTICE, "%s: Could not accept connection with assistant(%"PRId64"), customer(%"PRId64") is not found or is disabled",
                        fohh_description(tlv->conn.f_conn), assistant_gid_derived_from_cert, customer_gid);
            return ZPN_RESULT_ERR;
        }
    }

    cert = zpn_tlv_peer_cert(tlv);

    if (!cert) {
        SITEC_LOG(AL_WARNING, "No peer cert??");
        return ZPN_RESULT_ERR;
    }

    res = zpn_issuedcert_verify(ZPATH_GID_GET_CUSTOMER_GID(assistant_gid_derived_from_cert),
                                cert,
                                cn,
                                "ASSISTANT",
                                &assistant_gid_fetched_from_wally,
                                callback_f,
                                callback_cookie,
                                request_id);
    X509_free(cert);

    if (res) {
        if (res == ZPN_RESULT_ASYNCHRONOUS) {
            SITEC_LOG(AL_INFO, "Asynchronous verification request for CN = %s", cn);
        } else {
            SITEC_LOG(AL_WARNING, "Error verifying request for CN = %s, = %s", cn, zpn_result_string(res));
        }
        return res;

    } else {
        if (assistant_gid_fetched_from_wally != assistant_gid_derived_from_cert) {
            SITEC_LOG(AL_WARNING, "Error verifying request for CN = %s, gid mismatch(%ld vs %ld)", cn,
                    (long)assistant_gid_derived_from_cert, (long)assistant_gid_fetched_from_wally);
            return ZPN_RESULT_ERR;
        }
        SITEC_LOG(AL_INFO, "Verified cert for CN = %s", cn);
    }
    zpn_tlv_peer_set_id(tlv, assistant_gid_derived_from_cert);

    res = zpn_assistant_get_by_id(assistant_gid_derived_from_cert,
                                  &assistant,
                                  callback_f,
                                  callback_cookie,
                                  request_id);
    if (res) {
        if (res == ZPN_RESULT_ASYNCHRONOUS) {
            SITEC_LOG(AL_INFO, "Asynchronous assistant fetch for gid = %ld", (long) assistant_gid_derived_from_cert);
        } else {
            SITEC_LOG(AL_WARNING, "Error fetching assistant %ld: %s", (long) assistant_gid_derived_from_cert,
                    zpn_result_string(res));
        }
        return res;
    } else {
        SITEC_LOG(AL_INFO, "%s: Assistant cert verified, assistant %ld fetched.", cn, (long) assistant_gid_derived_from_cert);
    }

    zpn_tlv_address(tlv, &asst_addr, NULL);

    /*
     * Here we are prefetching the assistant group relation and
     * assistant group, because we can do it asynchronously. When the
     * control connection callback occurs, this data can be fetched
     * synchronously. (The control connection callback must be
     * synchronous)
     */
    struct zpn_assistantgroup_assistant_relation *ag_relation;
    size_t count = 1;
    res = zpn_assistantgroup_assistant_relation_get_by_assistant(assistant_gid_derived_from_cert,
                                                                 &ag_relation,
                                                                 &count,
                                                                 callback_f,
                                                                 callback_cookie,
                                                                 request_id);
    if (res) {
        if (res == ZPN_RESULT_ASYNCHRONOUS) {
            SITEC_LOG(AL_INFO,"Asynchronous assistantgroup_assistant_relation fetch for gid = %ld", (long) assistant_gid_derived_from_cert);
        } else {
            SITEC_LOG(AL_WARNING, "Error fetching assistantgroup_assistant_relation: %ld: %s", (long) assistant_gid_derived_from_cert, zpn_result_string(res));
            /* We don't error here because the relation may simply be changing, etc */
            res = ZPN_RESULT_NO_ERROR;
        }
        return res;
    } else {
        /* Now that we have an assistantgroup, fetch that too. */
        res = zpn_assistant_group_get_by_gid(ag_relation->assistant_group_id,
                                             &group,
                                             callback_f,
                                             callback_cookie,
                                             request_id);
        if (res) {
            if (res == ZPN_RESULT_ASYNCHRONOUS) {
                SITEC_LOG(AL_INFO,"Asynchronous assistant_group fetch for gid = %ld", (long) ag_relation->assistant_group_id);
            } else {
                SITEC_LOG(AL_WARNING, "Error fetching assistant_group: %ld: %s", (long) ag_relation->assistant_group_id, zpn_result_string(res));
                /* We don't error here because the relation may simply be changing, etc */
                res = ZPN_RESULT_NO_ERROR;
            }
        } else {
            /*
             * IP ACL is configured at connector group level.
             * So either customer do not configure them
             * or will have to configure IP ACL's for all the connectors in the group
             */
            if (0 == group->ip_acl_count) {
                ip_acl_validation_failed = 0;
            } else {
                ip_acl_validation_failed = 1;
            }
            ip_acl_iter = 0;
            while (ip_acl_iter != group->ip_acl_count) {
                if (argo_inet_is_contained(&group->ip_acl[ip_acl_iter], &asst_addr)) {
                    ip_acl_validation_failed = 0;
                    break;
                }
                ip_acl_iter++;
            }

            if (ip_acl_validation_failed) {
                char asst_addr_str[ARGO_INET_ADDRSTRLEN];
                SITEC_LOG(AL_ERROR, "Connector's %ld IP(%s) didn't match ACL "
                        "defined in Connector Group %ld - "
                        "terminating the connector",
                        (long)assistant_gid_derived_from_cert,
                        argo_inet_generate(asst_addr_str, &asst_addr),
                        (long)ag_relation->assistant_group_id);
                return ZPN_RESULT_ACCESS_DENIED;
            }
        }
    }

    if (gs->disable_flags) {
        SITEC_LOG(AL_ERROR, "%s: %s for %"PRId64,
                fohh_description(tlv->conn.f_conn), zpn_sitec_get_disable_flag(), gs->sitec_id);
        fohh_connection_delete(tlv->conn.f_conn, ZPN_ERR_SITEC_SERVICE_DISABLED);
        return ZPN_RESULT_ERR;
    }

    if (group && (group->site_gid != gs->site_gid)) {
        SITEC_LOG(AL_WARNING, "%s: sitec %"PRId64" and assistant %"PRId64" are part of different sites, hence disconnecting", fohh_description(tlv->conn.f_conn), gs->sitec_id, assistant_gid_derived_from_cert);
        return ZPN_RESULT_ERR;
    }

    return res;
}

static int zpn_sitec_asst_verify_callback(struct fohh_connection *f_conn)
{
    struct zpn_tlv tlv;

    zpn_tlv_init(&tlv, zpn_fohh_tlv, f_conn, 0);
    return zpn_sitec_asst_verify_callback_common(&tlv, 0);
}

static void* zpn_sitec_alog_app_info_callback(struct fohh_connection *f_conn)
{
    struct connected_sitec_asst_log_fohh_state *cookie;
    int64_t asst_gid;
    int res;

    asst_gid = fohh_peer_get_id(f_conn);
    if (!asst_gid) return NULL;

    cookie = SITEC_CALLOC(sizeof(*cookie));

    /* Generate an ID for this client */
    res = RAND_bytes(&(cookie->tunnel_id_bin[0]), ZPN_TUNNEL_ID_BYTES);
    if (1 != res) {
        ZPN_SITEC_ASSERT_HARD(0, "Crypto Random Generator not seeded!");
        SITEC_FREE(cookie);
        return NULL;
    }
    base64_encode_binary(cookie->tunnel_id, cookie->tunnel_id_bin, ZPN_TUNNEL_ID_BYTES);
    cookie->f_conn = f_conn;

    return cookie;
}

static void zpn_sitec_alog_stats_cb(struct fohh_connection *f_conn, enum fohh_connection_state state)
{
    struct connected_sitec_asst_log_fohh_state *asst = log_rx_get_app_info(f_conn);
    if (state == fohh_connection_connected) {
        zpn_sitec_asst_conn_tunnel_fohh_store(asst->tunnel_id, ZPN_TUNNEL_ID_BYTES_TEXT, asst, &zpn_sitec_asst_alog_conn);
        zpn_fohh_worker_sitec_connect_alog(fohh_connection_get_thread_id(f_conn));
    } else {
        zpn_sitec_asst_conn_tunnel_fohh_remove(asst->tunnel_id, ZPN_TUNNEL_ID_BYTES_TEXT, &zpn_sitec_asst_alog_conn);
        zpn_fohh_worker_sitec_disconnect_alog(fohh_connection_get_thread_id(f_conn));
    }
}

int zpn_sitec_asst_auth_log(struct fohh_connection *f_conn,
                            struct zpn_ast_auth_log *log,
                            int64_t asst_id,
                            char *type,
                            enum fohh_connection_state state)
{
    struct fohh_tcp_info info;
    struct zpn_assistantgroup_assistant_relation *ag_relation;
    int64_t assistant_grp_id = 0;
    size_t count = 1;
    int res = 0;
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();

    res = zpn_assistantgroup_assistant_relation_get_by_assistant(asst_id,
                                                                 &ag_relation,
                                                                 &count,
                                                                 NULL,
                                                                 NULL,
                                                                 0);
    if (res) {
        SITEC_LOG(AL_WARNING, "Error fetching assistantgroup_assistant_relation. Assistant ID = %"PRId64": %s",
                         asst_id, zpn_result_string(res));
    } else {
        assistant_grp_id = ag_relation->assistant_group_id;
    }

    log->log_date = epoch_us();
    log->g_cst = ZPATH_GID_GET_CUSTOMER_GID(asst_id);
    log->g_ast = asst_id;
    log->g_ast_grp = assistant_grp_id;
    log->type = type;
    log->g_brk = ZPN_BROKER_GET_GID();
    log->auth_type = zpn_auth_type_string(zpn_tunnel_auth_connector);
    log->sitec_id = gs->sitec_id;
    if (f_conn) {
        if (!log->pub_ip.length) {
            uint16_t pub_port_ne;
            fohh_connection_address_and_port(f_conn, &(log->pub_ip), &pub_port_ne, NULL, NULL);
            log->pub_port = ntohs(pub_port_ne);
        }

        memset(&info, 0, sizeof(info));
        if (fohh_connection_get_tcp_info(f_conn, &info) == FOHH_RESULT_NO_ERROR) {
            log->tcpi_snd_mss = info.tcpi_snd_mss;
            log->tcpi_rcv_mss = info.tcpi_rcv_mss;
            log->tcpi_rtt = info.tcpi_rtt;
            log->tcpi_rttvar = info.tcpi_rttvar;
            log->tcpi_snd_cwnd = info.tcpi_snd_cwnd;
            log->tcpi_advmss = info.tcpi_advmss;
            log->tcpi_reordering = info.tcpi_reordering;
            log->tcpi_rcv_rtt = info.tcpi_rcv_rtt;
            log->tcpi_rcv_space = info.tcpi_rcv_space;
            log->tcpi_total_retrans = info.tcpi_total_retrans;
            log->tcpi_thru_put = info.tcpi_thru_put;
            log->tcpi_unacked = info.tcpi_unacked;
            log->tcpi_sacked = info.tcpi_sacked;
            log->tcpi_lost = info.tcpi_lost;
            log->tcpi_fackets = info.tcpi_fackets;

            log->tcpi_last_data_sent = info.tcpi_last_data_sent;
            log->tcpi_last_ack_sent = info.tcpi_last_ack_sent;
            log->tcpi_last_data_recv = info.tcpi_last_data_recv;
            log->tcpi_last_ack_recv = info.tcpi_last_ack_recv;
            log->tcpi_bytes_acked = info.tcpi_bytes_acked;
            log->tcpi_bytes_received = info.tcpi_bytes_received;
            log->tcpi_segs_out = info.tcpi_segs_out;
            log->tcpi_segs_in = info.tcpi_segs_in;

            if ((info.tcpi_rtt / 1000) > ZPN_ASSISTANT_CONN_HIGH_RTT_LOG_THRESHOLD_MS) {
                SITEC_LOG(AL_NOTICE, "%s: Assistant connection high RTT: %d ms, exceeded RTT threshold: %d ms", fohh_description(f_conn), info.tcpi_rtt / 1000, ZPN_ASSISTANT_CONN_HIGH_RTT_LOG_THRESHOLD_MS);
            }
        }

        fohh_connection_get_stats(f_conn,
                                  &(log->transmit_bytes),
                                  &(log->receive_bytes),
                                  &(log->transmit_objects),
                                  &(log->receive_objects),
                                  &(log->transmit_raw_tlv),
                                  &(log->receive_raw_tlv));
        /* Skip quiet app connections */
        if (!fohh_connection_is_quiet(f_conn)) {
            log->app_rtt_us = fohh_connection_get_max_app_rtt(f_conn);
            if (fohh_histogram_get_counts_array(f_conn, FOHH_HISTOGRAM_APP_RTT, log->app_rtt, FOHH_HISTOGRAM_MAX_BUCKETS) == FOHH_RESULT_NO_ERROR) {
                log->app_rtt_count = FOHH_HISTOGRAM_MAX_BUCKETS;
            } else {
                SITEC_LOG(AL_DEBUG, "%s: Unable to get app rtt histogram", fohh_description(f_conn));
            }
            if (fohh_histogram_get_delta_app_rtt_counts_array(f_conn, log->delta_app_rtt, FOHH_HISTOGRAM_MAX_BUCKETS) == FOHH_RESULT_NO_ERROR) {
                log->delta_app_rtt_count = FOHH_HISTOGRAM_MAX_BUCKETS;
            } else {
                SITEC_LOG(AL_DEBUG, "%s: Unable to get delta app rtt histogram", fohh_description(f_conn));
            }
            if (fohh_histogram_update_prev_app_rtt_hostogram(f_conn) != FOHH_RESULT_NO_ERROR) {
                SITEC_LOG(AL_DEBUG, "%s: Unable to update previous app rtt histogram", fohh_description(f_conn));
            }
        }

        /* FIXME: Do we need to send max value for all TCP_INFO metrics? */
        log->tcp_rtt_us = fohh_connection_get_max_tcp_rtt(f_conn);
        if (fohh_histogram_get_counts_array(f_conn, FOHH_HISTOGRAM_TCP_RTT, log->tcp_rtt, FOHH_HISTOGRAM_MAX_BUCKETS) == FOHH_RESULT_NO_ERROR) {
            log->tcp_rtt_count = FOHH_HISTOGRAM_MAX_BUCKETS;
        } else {
            SITEC_LOG(AL_DEBUG, "%s: Unable to get tcp rtt histogram", fohh_description(f_conn));
        }

        if (fohh_histogram_get_counts_array(f_conn, FOHH_HISTOGRAM_TCP_WIN, log->tcp_congestion_win, FOHH_HISTOGRAM_MAX_BUCKETS) == FOHH_RESULT_NO_ERROR) {
            log->tcp_congestion_win_count = FOHH_HISTOGRAM_MAX_BUCKETS;
        } else {
            SITEC_LOG(AL_DEBUG, "%s: Unable to get tcp congestion win histogram", fohh_description(f_conn));
        }

        fohh_connection_reset_max_rtt(f_conn);

        /* This Log type will be present in case of fohh log connection
           it will be null for data */
        log->log_type = fohh_connection_get_log_collection_name(f_conn);
    }

    if (state == fohh_connection_connected) {
        if (!log->auth_us) log->auth_us = epoch_us();
        log->status = ZPN_STATUS_AUTHENTICATED;
    } else {
        log->deauth_us = epoch_us();
        log->status = ZPN_STATUS_DISCONNECTED;
    }

    int64_t siem_ids[ZPN_MAX_SIEM_IDS];
    size_t siem_ids_count = ZPN_MAX_SIEM_IDS;

    zpn_broker_siem_get_siem_gids_ast_auth_log(log, siem_ids, &siem_ids_count);
    if (siem_ids_count) {
        log->gids = SITEC_CALLOC(siem_ids_count * sizeof(*(log->gids)));
        for (size_t ii = 0; ii < siem_ids_count; ii++) {
            log->gids[ii] = siem_ids[ii];
        }
        log->gids_count = siem_ids_count;
    }

    log->tlv_type = zpn_tlv_type_str(zpn_fohh_tlv);

    if (zpn_sc_asst_auth_collection) {
        argo_log_structure_immediate(zpn_sc_asst_auth_collection,
                                     argo_log_priority_info,
                                     0,
                                     "ast_auth_log",
                                     zpn_ast_auth_log_description,
                                     log);

    }

    if (log->gids) {
        SITEC_FREE(log->gids);
        log->gids = NULL;
        log->gids_count = 0;
    }

    zpn_sitec_siem_ast_auth_log(log);

    return res;
}

int zpn_sitec_asst_log_conn_init()
{
    char        broker_name[1000];
    char*       cloud_name = zpn_sitec_get_cloud_name();
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();
    int result;

    snprintf(broker_name, sizeof(broker_name), "%s.%s", ZPN_SITEC_TO_PUBLIC_BROKER_DNS_NAME, cloud_name);

    result = fohh_log_send(argo_log_get_name(zpn_ast_waf_collection),
                            argo_log_get_name(zpn_ast_waf_collection),
                            broker_name,
                            sc_sni_customer_domain_alog,
                            cloud_name,
                            NULL,
                            htons(ZPN_SITEC_TO_PUBLIC_BROKER_PORT),
                            NULL, 0, 0,
                            zpn_sitec_log_tx_conn_cb);
    if (result) {
        SITEC_LOG(AL_ERROR, "Failed to set up ast waf log transmit: %s", zpath_result_string(result));
        return result;
    }

    fohh_connection_monitor_sanity(fohh_get_log_handle(zpn_ast_waf_collection),
                                    zpn_sitec_fohh_connection_sanity_callback,
                                    SITEC_ALT_CLOUD_CONN_MAX_INIT_TIME_S);

    fohh_connection_set_default_sni(fohh_get_log_handle(zpn_ast_waf_collection),
                                    (char *)gs->cfg_key_cloud);

    /* app_inspection log */
    result = fohh_log_send(argo_log_get_name(zpn_ast_app_inspection_collection),
                            argo_log_get_name(zpn_ast_app_inspection_collection),
                            broker_name,
                            sc_sni_customer_domain_alog,
                            cloud_name,
                            NULL,
                            htons(ZPN_SITEC_TO_PUBLIC_BROKER_PORT),
                            NULL, 0, 0,
                            zpn_sitec_log_tx_conn_cb);
    if (result) {
        SITEC_LOG(AL_ERROR, "Failed to set up ast app inspection log transmit: %s", zpath_result_string(result));
        return result;
    }

    fohh_connection_monitor_sanity(fohh_get_log_handle(zpn_ast_app_inspection_collection),
                                    zpn_sitec_fohh_connection_sanity_callback,
                                    SITEC_ALT_CLOUD_CONN_MAX_INIT_TIME_S);

    fohh_connection_set_default_sni(fohh_get_log_handle(zpn_ast_app_inspection_collection),
                                    (char *)gs->cfg_key_cloud);

    /* ldap_inspection log */
    result = fohh_log_send(argo_log_get_name(zpn_ast_ldap_inspection_collection),
                            argo_log_get_name(zpn_ast_ldap_inspection_collection),
                            broker_name,
                            sc_sni_customer_domain_alog,
                            cloud_name,
                            NULL,
                            htons(ZPN_SITEC_TO_PUBLIC_BROKER_PORT),
                            NULL, 0, 0,
                            zpn_sitec_log_tx_conn_cb);
    if (result) {
        SITEC_LOG(AL_ERROR, "Failed to set up ast ldap inspection log transmit: %s", zpath_result_string(result));
        return result;
    }

    fohh_connection_monitor_sanity(fohh_get_log_handle(zpn_ast_ldap_inspection_collection),
                                    zpn_sitec_fohh_connection_sanity_callback,
                                    SITEC_ALT_CLOUD_CONN_MAX_INIT_TIME_S);

    fohh_connection_set_default_sni(fohh_get_log_handle(zpn_ast_ldap_inspection_collection),
                                    (char *)gs->cfg_key_cloud);

    /* krb_inspection log */
    result = fohh_log_send(argo_log_get_name(zpn_ast_krb_inspection_collection),
                            argo_log_get_name(zpn_ast_krb_inspection_collection),
                            broker_name,
                            sc_sni_customer_domain_alog,
                            cloud_name,
                            NULL,
                            htons(ZPN_SITEC_TO_PUBLIC_BROKER_PORT),
                            NULL, 0, 0,
                            zpn_sitec_log_tx_conn_cb);
    if (result) {
        SITEC_LOG(AL_ERROR, "Failed to set up ast krb inspection log transmit: %s", zpath_result_string(result));
        return result;
    }

    fohh_connection_monitor_sanity(fohh_get_log_handle(zpn_ast_krb_inspection_collection),
                                    zpn_sitec_fohh_connection_sanity_callback,
                                    SITEC_ALT_CLOUD_CONN_MAX_INIT_TIME_S);

    fohh_connection_set_default_sni(fohh_get_log_handle(zpn_ast_krb_inspection_collection),
                                    (char *)gs->cfg_key_cloud);

    /* smb_inspection log */
    result = fohh_log_send(argo_log_get_name(zpn_ast_smb_inspection_collection),
                            argo_log_get_name(zpn_ast_smb_inspection_collection),
                            broker_name,
                            sc_sni_customer_domain_alog,
                            cloud_name,
                            NULL,
                            htons(ZPN_SITEC_TO_PUBLIC_BROKER_PORT),
                            NULL, 0, 0,
                            zpn_sitec_log_tx_conn_cb);
    if (result) {
        SITEC_LOG(AL_ERROR, "Failed to set up ast smb inspection log transmit: %s", zpath_result_string(result));
        return result;
    }

    fohh_connection_monitor_sanity(fohh_get_log_handle(zpn_ast_smb_inspection_collection),
                                    zpn_sitec_fohh_connection_sanity_callback,
                                    SITEC_ALT_CLOUD_CONN_MAX_INIT_TIME_S);

    fohh_connection_set_default_sni(fohh_get_log_handle(zpn_ast_smb_inspection_collection),
                                    (char *)gs->cfg_key_cloud);

    /* ptag log */
    result = fohh_log_send(argo_log_get_name(zpn_ast_ptag_collection),
                            argo_log_get_name(zpn_ast_ptag_collection),
                            broker_name,
                            sc_sni_customer_domain_alog,
                            cloud_name,
                            NULL,
                            htons(ZPN_SITEC_TO_PUBLIC_BROKER_PORT),
                            NULL, 0, 0,
                            zpn_sitec_log_tx_conn_cb);
    if (result) {
        SITEC_LOG(AL_ERROR, "Failed to set up zpn_ast_ptag_log transmit: %s", zpath_result_string(result));
        return result;
    }

    fohh_connection_monitor_sanity(fohh_get_log_handle(zpn_ast_ptag_collection),
                                    zpn_sitec_fohh_connection_sanity_callback,
                                    SITEC_ALT_CLOUD_CONN_MAX_INIT_TIME_S);

    fohh_connection_set_default_sni(fohh_get_log_handle(zpn_ast_ptag_collection),
                                    (char *)gs->cfg_key_cloud);

    /* API log */
    result = fohh_log_send(argo_log_get_name(zpn_ast_waf_api_collection),
                            argo_log_get_name(zpn_ast_waf_api_collection),
                            broker_name,
                            sc_sni_customer_domain_alog,
                            cloud_name,
                            NULL,
                            htons(ZPN_SITEC_TO_PUBLIC_BROKER_PORT),
                            NULL, 0, 0,
                            zpn_sitec_log_tx_conn_cb);
    if (result) {
        SITEC_LOG(AL_ERROR, "Failed to set up zpn ast waf api transmit: %s", zpath_result_string(result));
        return result;
    }

    fohh_connection_monitor_sanity(fohh_get_log_handle(zpn_ast_waf_api_collection),
                                    zpn_sitec_fohh_connection_sanity_callback,
                                    SITEC_ALT_CLOUD_CONN_MAX_INIT_TIME_S);

    fohh_connection_set_default_sni(fohh_get_log_handle(zpn_ast_waf_api_collection),
                                    (char *)gs->cfg_key_cloud);


    result = fohh_log_send(argo_log_get_name(zpn_asst_event_collection),
                            argo_log_get_name(zpn_asst_event_collection),
                            broker_name,
                            sc_sni_customer_domain_alog,
                            cloud_name,
                            NULL,
                            htons(ZPN_SITEC_TO_PUBLIC_BROKER_PORT),
                            NULL, 0, 0,
                            zpn_sitec_log_tx_conn_cb);
    if (result) {
        SITEC_LOG(AL_ERROR, "Failed to set up transaction log transmit: %s", zpath_result_string(result));
        return result;
    }

    fohh_connection_monitor_sanity(fohh_get_log_handle(zpn_asst_event_collection),
                                    zpn_sitec_fohh_connection_sanity_callback,
                                    SITEC_ALT_CLOUD_CONN_MAX_INIT_TIME_S);

    fohh_connection_set_default_sni(fohh_get_log_handle(zpn_asst_event_collection),
                                    (char *)gs->cfg_key_cloud);

    return ZPN_RESULT_NO_ERROR;
}

static void zpn_sitec_alog_monitor_tunnel_timer_cb(evutil_socket_t sock, short flags, void *cookie)
{
    int res;
    struct fohh_connection *connection = cookie;
    struct zpn_assistant_group *group = NULL;
    struct zpath_customer *customer = NULL;
    struct zpn_assistantgroup_assistant_relation *ag_relation;
    size_t count = 1;
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();
    int64_t asst_id = fohh_peer_get_id(connection);
    int64_t customer_gid = ZPATH_GID_GET_CUSTOMER_GID(asst_id);

    /* Drop connection when customer is disabled */
    if (customer_gid != 0) {
        res = zpath_customer_get(customer_gid,
                                 &customer,
                                 NULL,
                                 NULL,
                                 0);

        if (res == ZPN_RESULT_NOT_FOUND || (res == ZPN_RESULT_NO_ERROR && customer->disable_traffic == 1)) {
            SITEC_LOG(AL_NOTICE, "%s: Disconnecting assistant(%"PRId64") as customer(%"PRId64") is not found or is disabled",
                            fohh_description(connection), asst_id, customer_gid);
            /* Kill the connection */
            fohh_connection_delete(connection, ZPN_ERR_CUSTOMER_DISABLED);
            return;
        }
    }

    if (gs->disable_flags) {
        SITEC_LOG(AL_ERROR, "%s: %s for %"PRId64,
                fohh_description(connection), zpn_sitec_get_disable_flag(), gs->sitec_id);
        fohh_connection_delete(connection, ZPN_ERR_SITEC_SERVICE_DISABLED);
        return;
    }

    res = zpn_assistantgroup_assistant_relation_get_by_assistant(asst_id,
                                                                 &ag_relation,
                                                                 &count,
                                                                 NULL,
                                                                 NULL,
                                                                 0);
    if (res != ZPN_RESULT_NO_ERROR) {
        SITEC_LOG(AL_ERROR, "%s: Error fetching assistantgroup_assistant_relation for assistant gid: %ld: %s", fohh_peer_cn(connection), (long) asst_id, zpn_result_string(res));
        return;
    }

    res = zpn_assistant_group_get_by_gid(ag_relation->assistant_group_id,
                                         &group,
                                         NULL,
                                         NULL,
                                         0);
    if (res != ZPN_RESULT_NO_ERROR) {
        SITEC_LOG(AL_ERROR, "%s: Error fetching assistant group data for group id: %ld: %s", fohh_peer_cn(connection), (long) ag_relation->assistant_group_id, zpn_result_string(res));
        return;
    }

    if (group->site_gid != gs->site_gid) {
        SITEC_LOG(AL_WARNING, "%s: sitec %"PRId64" and assistant %"PRId64" are part of different sites, hence disconnecting", fohh_description(connection), gs->sitec_id, asst_id);
        /* Kill the connection */
        fohh_connection_delete(connection, ZPN_ERR_SITE_MISMATCH);
        return;
    }
}

static void zpn_sitec_alog_monitor_timer_cb(evutil_socket_t sock, short flags, void *cookie)
{
    struct fohh_connection *connection = cookie;
    struct zpn_ast_auth_log log;
    struct zpn_assistant *assistant = NULL;
    memset(&log, 0, sizeof(log));

    int res;

    res = zpn_assistant_get_by_id(fohh_peer_get_id(connection),
                                  &assistant,
                                  NULL,
                                  NULL,
                                  0);
    if (res) {
        SITEC_LOG(AL_WARNING, "Error fetching assistant %ld: %s", (long) fohh_peer_get_id(connection),
                             zpn_result_string(res));
    } else {
        log.g_microtenant = is_scope_default(assistant->scope_gid) ? 0 : assistant->scope_gid;
    }

    if (fohh_connection_get_log_type(connection) && (0 == strcmp(fohh_connection_get_log_type(connection), FOHH_LOG_INSPECTION_LOG))) {
        res = zpn_sitec_asst_auth_log(connection,
                                      &log,
                                      fohh_peer_get_id(connection),
                                      ZPN_ASSISTANT_SITEC_LOG_INSPECTION,
                                      fohh_get_state(connection));
    } else {
        res = zpn_sitec_asst_auth_log(connection,
                                      &log,
                                      fohh_peer_get_id(connection),
                                      ZPN_ASSISTANT_SITEC_LOG,
                                      fohh_get_state(connection));
    }

    if (res) {
        SITEC_LOG(AL_WARNING, "Assistant log connection auth log fail");
    }
}

static int zpn_sitec_asst_clog_callback(struct customer_log_collection_info* cust_log_collection_info,
                                        void* data)
{
    //No need to stream the logs to service endpoint in sitec. Just return no error
    return ZPN_RESULT_NO_ERROR;
}

struct zhash_table *zpn_sitec_create_alog_collection_map()
{
    struct zhash_table* log_collection_map = zhash_table_alloc(&sitec_allocator);
    if (!log_collection_map) {
        SITEC_LOG(AL_ERROR, "Out of memory");
        return NULL;
    }

    struct customer_log_collection_info *event_log;
    ZPN_SITEC_ASSERT_HARD((NULL != zpn_asst_event_collection),
                        "asst event collection is not yet initialized");
    event_log = create_customer_log_collection(NULL,0, NULL, zpn_asst_event_collection, 0, NULL, NULL, NULL, 0);
    if (!event_log) {
        SITEC_LOG(AL_ERROR, "Out of memory, when setting up log receiver for sitec assistant");
        zhash_table_free(log_collection_map);
        return NULL;
    }

    char* zpn_event_collection_name = argo_log_get_name(zpn_event_collection);
    if (zhash_table_store(log_collection_map,
                          zpn_event_collection_name,
                          strlen(zpn_event_collection_name),
                          1,
                          event_log)) {
        SITEC_LOG(AL_ERROR, "Failed to setup event log receiver for sitec assistant");
    }

    if (zpn_ast_waf_http_exchanges_log_description == NULL) {
        zpn_ast_waf_http_exchanges_log_description = argo_register_global_structure(ZPN_WAF_HTTP_EXCHANGES_LOG_HELPER);
        if (!zpn_ast_waf_http_exchanges_log_description) {
            SITEC_LOG(AL_ERROR, "Failed to set up sitec WAF http log description");
            zhash_table_free(log_collection_map);
            return NULL;
        }
    }

    struct customer_log_collection_info *waf_log;
    waf_log = create_customer_log_collection(NULL,
                                            zpath_customer_log_type_zpn_http_inspection,
                                            zpn_ast_waf_http_exchanges_log_description,
                                            zpn_ast_waf_collection,
                                            1, // transmit to log infra
                                            zpn_sitec_asst_clog_callback,
                                            zpn_sitec_inspection_log_siem_callback,
                                            NULL,
                                            0);
    if (!waf_log) {
        SITEC_LOG(AL_ERROR, "Out of memory, when setting up sitec WAF http log receiver for assistant");
        zhash_table_free(log_collection_map);
        return NULL;
    }

    /* add waf log collection to log collection hash-map */
    char* zpn_waf_log_name = argo_log_get_name(zpn_ast_waf_collection);
    if (zhash_table_store(log_collection_map,
                          zpn_waf_log_name,
                          strlen(zpn_waf_log_name),
                          1,
                          waf_log)) {
        SITEC_LOG(AL_ERROR, "Failed to setup event sitec WAF http log receiver for WAF assistant");
    }

    /* create app_inspection log collection */
    if (zpn_ast_app_inspection_log_description == NULL) {
        zpn_ast_app_inspection_log_description = argo_register_global_structure(ZPN_APP_INSPECTION_LOG_HELPER);
        if (!zpn_ast_app_inspection_log_description) {
            SITEC_LOG(AL_ERROR, "Failed to set up sitec app inspection log description");
            zhash_table_free(log_collection_map);
            return NULL;
        }
    }

    struct customer_log_collection_info *app_inspection_log;
    app_inspection_log = create_customer_log_collection(NULL,
                                            zpath_customer_log_type_zpn_app_inspection,
                                            zpn_ast_app_inspection_log_description,
                                            zpn_ast_app_inspection_collection,
                                            1, // transmit to log infra
                                            zpn_sitec_asst_clog_callback,
                                            zpn_sitec_app_inspection_log_siem_callback,
                                            NULL,
                                            0);
    if (!app_inspection_log) {
        SITEC_LOG(AL_ERROR, "Out of memory, when setting up sitec app inspection log receiver for assistant");
        zhash_table_free(log_collection_map);
        return NULL;
    }

    /* add app_inspection log collection to log collection hash-map */
    char* zpn_app_inspection_log_name = argo_log_get_name(zpn_ast_app_inspection_collection);
    size_t zpn_app_inspection_log_name_len = (zpn_app_inspection_log_name && zpn_app_inspection_log_name[0])?strlen(zpn_app_inspection_log_name):0;
    if (zhash_table_store(log_collection_map,
                          zpn_app_inspection_log_name,
                          zpn_app_inspection_log_name_len,
                          1,
                          app_inspection_log)) {
        SITEC_LOG(AL_ERROR, "Failed to setup event sitec app inspection log receiver for assistant");
    }

    /* Register krb log description */
    if (zpn_ast_krb_inspection_log_description == NULL) {
        zpn_ast_krb_inspection_log_description = argo_register_global_structure(ZPN_KRB_INSPECTION_LOG_HELPER);
        if (!zpn_ast_krb_inspection_log_description) {
            SITEC_LOG(AL_ERROR, "Failed to set up sitec KRB inspection log description");
            zhash_table_free(log_collection_map);
            return NULL;
        }
    }

    struct customer_log_collection_info *krb_inspection_log;
    krb_inspection_log = create_customer_log_collection(NULL,
                                            zpath_customer_log_type_zpn_krb_inspection,
                                            zpn_ast_krb_inspection_log_description,
                                            zpn_ast_krb_inspection_collection,
                                            1, // transmit to log infra
                                            zpn_sitec_asst_clog_callback,
                                            zpn_sitec_krb_inspection_log_siem_callback,
                                            NULL,
                                            0);
    if (!app_inspection_log) {
        SITEC_LOG(AL_ERROR, "Out of memory, when setting up sitec app inspection log receiver for assistant");
        zhash_table_free(log_collection_map);
        return NULL;
    }

    /* add krb_inspection log collection to log collection hash-map */
    char* zpn_krb_inspection_log_name = argo_log_get_name(zpn_ast_krb_inspection_collection);
    size_t zpn_krb_inspection_log_name_len = (zpn_krb_inspection_log_name && zpn_krb_inspection_log_name[0])?strlen(zpn_krb_inspection_log_name):0;
    if (zhash_table_store(log_collection_map,
                          zpn_krb_inspection_log_name,
                          zpn_krb_inspection_log_name_len,
                          1,
                          krb_inspection_log)) {
        SITEC_LOG(AL_ERROR, "Failed to setup event sitec krb inspection log receiver for assistant");
    }

    /* Register ldap log description */
    if (zpn_ast_ldap_inspection_log_description == NULL) {
        zpn_ast_ldap_inspection_log_description = argo_register_global_structure(ZPN_LDAP_INSPECTION_LOG_HELPER);
        if (!zpn_ast_ldap_inspection_log_description) {
            SITEC_LOG(AL_ERROR, "Failed to set up sitec LDAP inspection log description");
            zhash_table_free(log_collection_map);
            return NULL;
        }
    }

    struct customer_log_collection_info *ldap_inspection_log;
    ldap_inspection_log = create_customer_log_collection(NULL,
                                            zpath_customer_log_type_zpn_ldap_inspection,
                                            zpn_ast_ldap_inspection_log_description,
                                            zpn_ast_ldap_inspection_collection,
                                            1, // transmit to log infra
                                            zpn_sitec_asst_clog_callback,
                                            zpn_sitec_ldap_inspection_log_siem_callback,
                                            NULL,
                                            0);
    if (!ldap_inspection_log) {
        SITEC_LOG(AL_ERROR, "Out of memory, when setting up sitec ldap inspection log receiver for assistant");
        zhash_table_free(log_collection_map);
        return NULL;
    }

    /* add ldap_inspection log collection to log collection hash-map */
    char* zpn_ldap_inspection_log_name = argo_log_get_name(zpn_ast_ldap_inspection_collection);
    size_t zpn_ldap_inspection_log_name_len = (zpn_ldap_inspection_log_name && zpn_ldap_inspection_log_name[0])?strlen(zpn_ldap_inspection_log_name):0;
    if (zhash_table_store(log_collection_map,
                          zpn_ldap_inspection_log_name,
                          zpn_ldap_inspection_log_name_len,
                          1,
                          ldap_inspection_log)) {
        SITEC_LOG(AL_ERROR, "Failed to setup event sitec ldap inspection log receiver for assistant");
    }

    /* Register smb log description */
    if (zpn_ast_smb_inspection_log_description == NULL) {
        zpn_ast_smb_inspection_log_description = argo_register_global_structure(ZPN_SMB_INSPECTION_LOG_HELPER);
        if (!zpn_ast_smb_inspection_log_description) {
            SITEC_LOG(AL_ERROR, "Failed to set up sitec SMB inspection log description");
            zhash_table_free(log_collection_map);
            return NULL;
        }
    }

    struct customer_log_collection_info *smb_inspection_log;
    smb_inspection_log = create_customer_log_collection(NULL,
                                            zpath_customer_log_type_zpn_smb_inspection,
                                            zpn_ast_smb_inspection_log_description,
                                            zpn_ast_smb_inspection_collection,
                                            1, // transmit to log infra
                                            zpn_sitec_asst_clog_callback,
                                            zpn_sitec_smb_inspection_log_siem_callback,
                                            NULL,
                                            0);
    if (!smb_inspection_log) {
        SITEC_LOG(AL_ERROR, "Out of memory, when setting up sitec smb inspection log receiver for assistant");
        zhash_table_free(log_collection_map);
        return NULL;
    }

    /* add smb_inspection log collection to log collection hash-map */
    char* zpn_smb_inspection_log_name = argo_log_get_name(zpn_ast_smb_inspection_collection);
    size_t zpn_smb_inspection_log_name_len = (zpn_smb_inspection_log_name && zpn_smb_inspection_log_name[0])?strlen(zpn_smb_inspection_log_name):0;
    if (zhash_table_store(log_collection_map,
                          zpn_smb_inspection_log_name,
                          zpn_smb_inspection_log_name_len,
                          1,
                          smb_inspection_log)) {
        SITEC_LOG(AL_ERROR, "Failed to setup event sitec smb inspection log receiver for assistant");
    }

    if (zpn_ast_waf_http_exchanges_api_log_description == NULL) {
        zpn_ast_waf_http_exchanges_api_log_description = argo_register_global_structure(ZPN_WAF_HTTP_EXCHANGES_API_LOG_HELPER);
        if (!zpn_ast_waf_http_exchanges_api_log_description) {
            SITEC_LOG(AL_ERROR, "Failed to set up argo_structure_description for zpn_ast_waf_http_exchanges_api_log_description");
            zhash_table_free(log_collection_map);
            return NULL;
        }
    }

    struct customer_log_collection_info *api_log;
    api_log = create_customer_log_collection(NULL,
                                            zpath_customer_log_type_zpn_http_api_inspection,
                                            zpn_ast_waf_http_exchanges_api_log_description,
                                            zpn_ast_waf_api_collection,
                                            1, // transmit to log infra
                                            zpn_sitec_asst_clog_callback,
                                            zpn_sitec_api_log_siem_callback,
                                            NULL,
                                            0);
    if (!api_log) {
        SITEC_LOG(AL_ERROR, "Out of memory, when setting up sitec api log receiver for assistant");
        zhash_table_free(log_collection_map);
        return NULL;
    }

    /* add api log collection to log collection hash-map */
    char* zpn_api_log_name = argo_log_get_name(zpn_ast_waf_api_collection);
    size_t zpn_api_log_name_len = (zpn_api_log_name && zpn_api_log_name[0])?strlen(zpn_api_log_name):0;
    if (zhash_table_store(log_collection_map,
                          zpn_api_log_name,
                          zpn_api_log_name_len,
                          1,
                          api_log)) {
        SITEC_LOG(AL_ERROR, "Failed to setup event sitec api log receiver for assistant");
    }

    if (zpn_ast_ptag_log_description == NULL) {
        zpn_ast_ptag_log_description = argo_register_global_structure(ZPN_PTAG_LOG_HELPER);
        if (!zpn_ast_ptag_log_description) {
            SITEC_LOG(AL_ERROR, "Failed to set up argo_structure_description for zpn_ast_ptag_log_description");
            zhash_table_free(log_collection_map);
            return NULL;
        }
    }

    struct customer_log_collection_info *ptag_log;
    ptag_log = create_customer_log_collection(NULL,
                                            zpath_customer_log_type_zpn_ptag,
                                            zpn_ast_ptag_log_description,
                                            zpn_ast_ptag_collection,
                                            1, // transmit to log infra
                                            zpn_sitec_asst_clog_callback,
                                            NULL,
                                            NULL,
                                            0);
    if (!ptag_log) {
        SITEC_LOG(AL_ERROR, "Out of memory, when setting up sitec ptag log receiver for assistant");
        zhash_table_free(log_collection_map);
        return NULL;
    }

    /* add ptag log collection to log collection hash-map */
    char* zpn_ptag_log_name = argo_log_get_name(zpn_ast_ptag_collection);
    size_t zpn_ptag_log_name_len = (zpn_ptag_log_name && zpn_ptag_log_name[0])?strlen(zpn_ptag_log_name):0;
    if (zhash_table_store(log_collection_map,
                          zpn_ptag_log_name,
                          zpn_ptag_log_name_len,
                          1,
                          ptag_log)) {
        SITEC_LOG(AL_ERROR, "Failed to setup event sitec ptag log receiver for assistant");
    }

    return log_collection_map;
}

int zpn_sitec_add_asst_listeners(struct fohh_generic_server *sni_server, char *offline_domain)
{
    struct fohh_connection *f_conn = NULL;
    char sni_str[ZPN_MAX_SNI_NAME_LEN + 1];
    int res;
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();

    res = zpn_sitec_asst_init();
    if (res != ZPN_RESULT_NO_ERROR) {
        return res;
    }

    gs->alog_collection_map = zpn_sitec_create_alog_collection_map();
    if (!gs->alog_collection_map) {
        SITEC_LOG(AL_ERROR, "Unable to create sitec alog collection map");
        return ZPN_RESULT_ERR;
    }

    f_conn = fohh_server_create_ex(0, //int quiet,
                                   argo_serialize_binary, // enum argo_serialize_mode encoding,
                                   fohh_connection_style_argo, // enum fohh_connection_style style,
                                   NULL, // void *cookie,
                                   zpn_sitec_asst_control_conn_callback,
                                   NULL,
                                   zpn_sitec_asst_unblock_callback,
                                   NULL,
                                   NULL,
                                   0,
                                   NULL, // char *root_cert_file_name,
                                   NULL, // char *my_cert_file_name,
                                   NULL, // char *my_cert_key_file_name,
                                   gs->cfg_pkey,
                                   1, // int require_assistant_cert,
                                   1, // int use_ssl);
                                   zpn_sitec_asst_ctx_callback, // ssl ctx callback
                                   zpn_sitec_asst_verify_callback, // verify callback
                                   NULL, // post verify callback
                                   1, // Allow binary argo.
                                   ZPN_ASSISTANT_BROKER_RX_TIMEOUT_S); // timeout
    if (!f_conn) {
        return ZPN_RESULT_ERR;
    }

    snprintf(sni_str, sizeof(sni_str), "actl.%s", offline_domain);

    SITEC_LOG(AL_INFO, "Register SNI Server: *.%s", sni_str);
    res = fohh_generic_server_register(sni_server,
                                       f_conn,
                                       sni_str,
                                       1,
                                       FOHH_WORKER_ZPN_SCCTL);
    if (res) {
        return res;
    }

    zpn_sitec_add_asst_sni("actl", sni_str, 1);

    f_conn = fohh_server_create_ex(0, //int quiet,
                                   argo_serialize_json_no_newline, // enum argo_serialize_mode encoding,
                                   fohh_connection_style_argo, // enum fohh_connection_style style,
                                   NULL, // void *cookie,
                                   zpn_sitec_asst_config_conn_callback,
                                   NULL,
                                   zpn_sitec_asst_config_unblock_callback,
                                   NULL,
                                   NULL,
                                   0,
                                   NULL, // char *root_cert_file_name,
                                   NULL, // char *my_cert_file_name,
                                   NULL, // char *my_cert_key_file_name,
                                   gs->cfg_pkey,
                                   1, // int require_assistant_cert,
                                   1, // int use_ssl);
                                   zpn_sitec_asst_ctx_callback, // ssl ctx callback
                                   zpn_sitec_asst_verify_callback, // verify callback
                                   NULL, // post verify callback
                                   1, // Allow binary argo.
                                   ZPN_ASSISTANT_BROKER_RX_TIMEOUT_S); // timeout
    if (!f_conn) {
        return ZPN_RESULT_ERR;
    }

    snprintf(sni_str, sizeof(sni_str), "acfg.%s", offline_domain);
    SITEC_LOG(AL_INFO, "Register SNI Server: *.%s", sni_str);
    res = fohh_generic_server_register(sni_server,
                                        f_conn,
                                        sni_str,
                                        1,
                                        FOHH_WORKER_ZPN_ACFG);
    if (res) {
        return res;
    }
    zpn_sitec_add_asst_sni("acfg", sni_str, 1);

    f_conn = fohh_server_create_ex(0, //int quiet,
                                   argo_serialize_json_no_newline, // enum argo_serialize_mode encoding,
                                   fohh_connection_style_argo, // enum fohh_connection_style style,
                                   NULL, // void *cookie,
                                   zpn_sitec_asst_config_override_conn_callback,
                                   NULL,
                                   zpn_sitec_asst_config_override_unblock_callback,
                                   NULL,
                                   NULL,
                                   0,
                                   NULL, // char *root_cert_file_name,
                                   NULL, // char *my_cert_file_name,
                                   NULL, // char *my_cert_key_file_name,
                                   gs->cfg_pkey,
                                   1, // int require_assistant_cert,
                                   1, // int use_ssl);
                                   zpn_sitec_asst_ctx_callback, // ssl ctx callback
                                   zpn_sitec_asst_verify_callback, // verify callback
                                   NULL, //post verify callback
                                   1, // Allow binary argo.
                                   ZPN_ASSISTANT_BROKER_RX_TIMEOUT_S); // timeout
    if (!f_conn) {
        return ZPN_RESULT_ERR;
    }

    snprintf(sni_str, sizeof(sni_str), "aovd.%s", offline_domain);
    SITEC_LOG(AL_INFO, "Register SNI Server: *.%s", sni_str);
    res = fohh_generic_server_register(sni_server,
                                        f_conn,
                                        sni_str,
                                        1,
                                        FOHH_WORKER_ZPN_AOVD);
    if (res) {
        return res;
    }
    zpn_sitec_add_asst_sni("aovd", sni_str, 1);

    f_conn = fohh_server_create_ex(0, //int quiet,
                                   argo_serialize_binary, // enum argo_serialize_mode encoding,
                                   fohh_connection_style_argo, // enum fohh_connection_style style,
                                   NULL, // void *cookie,
                                   zpn_sitec_asst_stats_conn_callback,
                                   NULL,
                                   zpn_sitec_asst_stats_unblock_callback,
                                   NULL,
                                   NULL,
                                   0,
                                   NULL, // char *root_cert_file_name,
                                   NULL, // char *my_cert_file_name,
                                   NULL, // char *my_cert_key_file_name,
                                   gs->cfg_pkey,
                                   1, // int require_assistant_cert,
                                   1, // int use_ssl);
                                   zpn_sitec_asst_ctx_callback, // ssl ctx callback
                                   zpn_sitec_asst_verify_callback, // verify callback
                                   NULL, //post verify callback
                                   1, // Allow binary argo.
                                   ZPN_ASSISTANT_BROKER_RX_TIMEOUT_S); // timeout
    if (!f_conn) {
        return ZPN_RESULT_ERR;
    }

    snprintf(sni_str, sizeof(sni_str), "astats.%s", offline_domain);

    SITEC_LOG(AL_INFO, "Register SNI Server: *.%s", sni_str);
    res = fohh_generic_server_register(sni_server,
                                        f_conn,
                                        sni_str,
                                        1,
                                        FOHH_WORKER_ZPN_ASTATS);
    if (res) {
        return res;
    }
    zpn_sitec_add_asst_sni("astats", sni_str, 1);

    snprintf(sni_str, sizeof(sni_str), "alog.%s", offline_domain);
    res = fohh_log_receive(sni_server,                                     //fohh_generic_server
                           sni_str,                                        //domain
                           1,                                              //wildcard_prefix
                           zpn_sitec_asst_ctx_callback,                    //ssl ctx oallback
                           zpn_sitec_asst_verify_callback,                 //verify callback
                           fohh_log_conn_info_callback,                    //info callback
                           NULL,                                           // verify info callback
                           NULL,                                           // post verify callback
                           zpn_sitec_alog_app_info_callback,              // app info callback
                           NULL,                                           //log conn redirect callback
                           zpn_sitec_alog_stats_cb,                       //log stats callback
                           zpn_sitec_alog_monitor_tunnel_timer_cb,        //log_tunnel_timer_callback
                           zpn_sitec_alog_monitor_timer_cb,               //log_timer_callback
                           gs->alog_collection_map,
                           gs->cfg_pkey,
                           0);                      //log collection map
    if (res) {
        SITEC_LOG(AL_ERROR, "Could not set up log receiver: %s", zpn_result_string(res));
        return res;
    }

    zpn_sitec_add_asst_sni("alog", sni_str, 1);

    return ZPN_RESULT_NO_ERROR;
}

static int zpn_sitec_asst_firedrill_walk_f(void *cookie,
                                              void *value,
                                              void *key,
                                              size_t key_len)
{
    struct zpn_sitec_firedrill_exit *firedrill_obj = (struct zpn_sitec_firedrill_exit *) cookie;
    struct connected_sitec_asst_control_fohh_state *info = (struct connected_sitec_asst_control_fohh_state *) value;
    SITEC_LOG(AL_ERROR, "zpn_sitec_asst_firedrill_walk_f firedrill entered");

    return fohh_argo_serialize(info->f_conn, zpn_sitec_firedrill_exit_description, firedrill_obj, 0,
                               fohh_queue_element_type_mission_critical);
}

void zpn_sitec_assistant_send_firedrill_exit_rpc()
{
    struct zpn_sitec_firedrill_exit firedrill_msg;
    SITEC_LOG(AL_ERROR, "zpn_sitec_assistant_send_firedrill_exit_rpc firedrill entered");

    firedrill_msg.firedrill_exit = EXIT_FIREDRILL;
    ZPATH_MUTEX_LOCK(&zpn_sitec_asst_control_conn.lock, __FILE__, __LINE__);
    zhash_table_walk(zpn_sitec_asst_control_conn.table, NULL, zpn_sitec_asst_firedrill_walk_f, &firedrill_msg);
    ZPATH_MUTEX_UNLOCK(&zpn_sitec_asst_control_conn.lock, __FILE__, __LINE__);
}
