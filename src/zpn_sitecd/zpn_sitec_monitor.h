/*
 * zpn_sitec_monitor.h. Copyright (C) 2024 Zscaler Inc, All Rights Reserved
 */

#ifndef _ZPN_SITEC_MONITOR_H_
#define _ZPN_SITEC_MONITOR_H_

#define ZPN_SITEC_MONITOR_TIMER_S                               60

/*
 * 1. Re-enroll when we are >= 90% of the cert.pem's valid period. Done in enrollment library.
 * 2. Force-re-enroll when we are >= 99% of the cert.pem's valid period.
 */
#define ZPN_SITEC_FORCE_RE_ENROLL_TIME_FACTOR                   99

#define ZPN_SITEC_LOG_NAME                                      "Sitec"

extern struct event *ev_firedrill_timer;

int zpn_sitec_stats_monitor_init(void);
int zpn_sitec_monitor_init(struct event_base *base);
int zpn_sitec_monitor_debug_init();
int zpn_sitec_is_ready(void);
uint16_t zpn_sitec_get_cpu_util(void);
uint16_t zpn_sitec_get_cpu_steal_perc(void);
int zpn_sitec_get_system_mem_util(void);
int zpn_sitec_get_process_mem_util(void);

#endif /* _ZPN_SITEC_MONITOR_H_ */
