/*
 * zpn_sitecd.c. Copyright (C) 2024 Zscaler, Inc. All Rights Reserved.
 *
 */

#include <stdio.h>
#include <stdarg.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <pthread.h>
#include <sys/time.h>
#include <ctype.h>
#include <errno.h>
#include <fcntl.h>
#ifdef __linux__
#include <malloc.h>
#endif

#include "zpath_misc/zpath_misc.h"
#include "zpath_misc/zpath_version.h"
#include "zpath_lib/zpath_system.h"
#include "zpath_lib/zpath_system_linux.h"
#include "zpath_lib/zpath_app.h"
#include "zpath_lib/zpath_local.h"
#include "zpath_lib/zpath_debug.h"
#include "zhw/zhw_os.h"
#include "zthread/zthread.h"
#include "zcrypt/zcrypt_meta.h"

#include "zpn/zpn_lib.h"
#include "zpn_pcap/zpn_pcap.h"
#include "zpn/zpn_rpc.h"
#include "zcdns/zcdns_libevent.h"

#include "zpath_lib/zpath_et_wally_userdb.h"
#include "zpn_sitecd/zpn_sitec.h"
#include "zpn/zpn_sitec_log.h"
#include "zpn_sitecd/zpn_sitec_util.h"
#include "zpn_sitecd/zpn_sitec_monitor.h"
#include "zpn_sitecd/zpn_sitec_alt_cloud.h"
#include "zpn_sitecd/zpn_sitec_broker_conn.h"
#include "zpn_sitecd/zpn_sitec_proxy.h"
#include "zpn_sitecd/zpn_sitec_sitec_conn.h"
#include "zpn/zpn_file_fetch.h"
#include "zpn/zpn_broker_common.h"
#include "zpath_lib/zpath_geoip.h"
#include "fohh/fohh_private.h"
#include "zpn_zdx/zpn_zdx_webprobe_lib.h"
#include "zpn_zdx/zpn_zdx_webprobe_rate_limit.h"
#include "zpn/zpn_broker_client.h"
#include "zpath_lib/sanitizer_config.h"
#include "zpath_lib/zpa_cloud_config.h"
#include "zpath_lib/zpath_upgrade_utils.h"
#include "fohh/fohh_log.h"
#include "zpath_lib/zpath_oauth_utils.h"

//Global variables
static struct zpn_sitec_global_state sitec_gs;
int config_daemon = 0;
int sitec_fohh_thread_count = SITEC_DEFAULT_THREAD_CNT;
int sitec_non_fohh_thread_count = 45;
extern int debuglog;
int logfiles = 0;
const char *logfile = NULL;

int debug_port = 8000;
extern const char *dbhost;
const char *local_file = NULL;
static char *stackpath = NULL;
struct argo_log_collection *zpn_sitec_auth_collection;
extern int sub_module_upgrade_failed;
int sub_module_fail_restart_time;
extern char *geoip_db_file;
extern char *geoip_isp_db_file;
extern char zpath_geoip_filename[FILE_PATH_LEN];
extern char zpath_ispip_filename[FILE_PATH_LEN];
char mmdb_geoip_version_g[MAX_VERSION_LEN] = {0};
char mmdb_isp_version_g[MAX_VERSION_LEN] = {0};
static zpath_rwlock_t config_check_lock;
extern int64_t g_sitec_geoip_disable;
int d_authlog_sent = 0;
int e_authlog_sent = 0;
struct zpn_common_broker_cfg sitec_cfg = { 0 };

static char config_fetch_ca_file[MAX_CA_FILE_LEN];
extern int key_retry_flag[FILE_CODE_MAX];
extern struct ff_stats file_stats[FILE_CODE_MAX];

/*
 * Get site controller global state
 */
struct zpn_sitec_global_state* zpn_get_sitec_global_state(void)
{
    /* Return pointer to singleton private broker global state */
    return &sitec_gs;
}

int version_check_callback(int file_code, char *db_version, wally_version_resp_cb *callback_f, void *fi)
{
    if(file_code == MMDB_GEOIP) {
        if(geoip_db_file == NULL) {
            memset(db_version, 0, MAX_VERSION_LEN);
            snprintf(db_version, MAX_VERSION_LEN, "default");
        } else {
            memset(db_version, 0, MAX_VERSION_LEN);
            snprintf(db_version, MAX_VERSION_LEN, "%s",mmdb_geoip_version_g);
        }
    } else if(file_code == MMDB_ISP) {
        if(geoip_isp_db_file == NULL) {
            memset(db_version, 0, MAX_VERSION_LEN);
            snprintf(db_version, MAX_VERSION_LEN, "default");
        } else {
            memset(db_version, 0, MAX_VERSION_LEN);
            snprintf(db_version, MAX_VERSION_LEN, "%s",mmdb_isp_version_g);
        }
    } else {
         snprintf(db_version,MAX_VERSION_LEN, "0");
    }

    return ZPATH_RESULT_NO_ERROR;
}

int download_fail_callback(int file_code)
{
    if (file_code == MMDB_GEOIP) {
        if (geoip_db_file == NULL) {
            ZPN_LOG(AL_ERROR, "download_fail_callback: Too many failures for GeoIP2-City.mmdb. Exiting.");
            if (!sub_module_upgrade_failed) {
                zpn_sitec_write_to_file_and_update();
            }
            sub_module_upgrade_failed = 1;
        }
    }else if (file_code == MMDB_ISP) {
        if (geoip_isp_db_file == NULL) {
            ZPN_LOG(AL_ERROR, "download_fail_callback: Too many failures for GeoIP2-ISP.mmdb. Exiting.");
            if (!sub_module_upgrade_failed) {
                zpn_sitec_write_to_file_and_update();
            }
            sub_module_upgrade_failed = 1;
        }
    }

    return ZPATH_RESULT_NO_ERROR;
}

static void log_verify_version_f(int priority, char *log_buf)
{
    char buf[1000];

    snprintf(buf, sizeof(buf), "FILE_FETCH:%s", log_buf);
    ZPATH_LOG(priority, "%s", buf);
}

int64_t config_disable_check_callback()
{
    char file_version[FILE_PATH_LEN] = {0};
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();

    ZPATH_RWLOCK_WRLOCK(&(config_check_lock), __FILE__, __LINE__);

    if(g_sitec_geoip_disable) {
        e_authlog_sent = 0;
        if (!d_authlog_sent) {
            write_version(FILENAME_GEOIP_ENC, "disabled");
            write_version(FILENAME_ISP_ENC, "disabled");
            //update the auth log
            if (fohh_get_state(sitec_gs.sitec_state->ctrl_fohh_conn) == fohh_connection_connected) {
                zpn_send_zpn_tcp_info_report(gs->sitec_state->ctrl_fohh_conn,
                                            fohh_connection_incarnation(gs->sitec_state->ctrl_fohh_conn),
                                            gs->sitec_state->ctrl_fohh_conn,
                                            zvm_vm_type_to_str_concise(zvm_type_get()),
                                            gs->sitec_runtime_os);
            }
            d_authlog_sent = 1;
        }
    } else {
        d_authlog_sent = 0;
        if (!e_authlog_sent) {
            if (zpath_is_ispip_running()) {
                if (!zpath_is_ispip_fallback_running()) {
                    zcrypt_get_metadata_version(FILENAME_ISP_META, file_version, FILE_PATH_LEN, log_verify_version_f, 1);
                } else if (zpath_is_ispip_fallback_running()) {
                    zcrypt_get_metadata_version(FILENAME_ISP_FALLBACK_META, file_version, FILE_PATH_LEN, log_verify_version_f, 1);
                }
                ZPN_LOG(AL_NOTICE, "Config check: found version: %s for ISP DB", file_version);
                write_version(FILENAME_ISP_ENC, file_version);
                memset(file_version,0,FILE_PATH_LEN);
            }

            if (zpath_is_geoip_running()) {
                if (!zpath_is_geoip_fallback_running()) {
                    zcrypt_get_metadata_version(FILENAME_GEOIP_META, file_version, FILE_PATH_LEN, log_verify_version_f, 1);
                } else if (zpath_is_geoip_fallback_running()) {
                    zcrypt_get_metadata_version(FILENAME_GEOIP_FALLBACK_META, file_version, FILE_PATH_LEN, log_verify_version_f, 1);
                }
                ZPN_LOG(AL_NOTICE, "Config check: found version: %s for GEOIP DB", file_version);
                write_version(FILENAME_GEOIP_ENC, file_version);
            }

            //update the auth log
            if (fohh_get_state(sitec_gs.sitec_state->ctrl_fohh_conn) == fohh_connection_connected) {
                zpn_send_zpn_tcp_info_report(gs->sitec_state->ctrl_fohh_conn,
                                            fohh_connection_incarnation(gs->sitec_state->ctrl_fohh_conn),
                                            gs->sitec_state->ctrl_fohh_conn,
                                            zvm_vm_type_to_str_concise(zvm_type_get()),
                                            gs->sitec_runtime_os);
            }
            e_authlog_sent = 1;
        }
    }
    ZPATH_RWLOCK_UNLOCK(&(config_check_lock), __FILE__, __LINE__);

    return g_sitec_geoip_disable;
}

static void log_f(int priority, const char *format, va_list list)
{
    char dump[2000];
    vsnprintf(dump, sizeof(dump), format, list);
    if (priority < argo_log_priority_notice) {
        SITEC_LOG(priority, "%s", dump);
    } else {
        ZPN_DEBUG_HEALTH("%s", dump);
    }
}

void usage(const char *argv0, const char *format, ...)
    __attribute__((format(printf, 2, 3)));

/* ET-87286 usage() on PCC child will no longer exit
 * Caller may decide to exit if it's a critical error */
void usage(const char *argv0, const char *format, ...)
{
    va_list list;

    fprintf(stdout, "Error: ");
	va_start(list, format);
	vfprintf(stdout, format, list);

    fprintf(stdout, "%s: Usage:\n", argv0);
    fprintf(stdout,
            "  -version          : Opt : Display version and exit\n"
            "  -role             : Opt : Display role and exit\n"
            "  -platform         : Opt : Display platform and exit\n"
            "  -arch             : Opt : Display platform architecture and exit\n"
            "  -local_file FILE  : Specify that config should come\n"
            "                      from FILE rather than local_db\n"
            "  -quiet            : Operate without FOHH status messages\n"
            "  -debuglog         : Specify in order to send debug messages to syslog/stderr\n"
            "  -container        : Opt : Set current environment to container\n"
            "  -logfiles         : Generate event and statslog files. WARNING: unconstrained growth\n"
            "  -logfile FILE     : Send event logs to FILE (argo format) WARNING: unconstrained growth\n"
            "  -fproxy NAME      : Opt : Connect to brokers via forward proxy NAME\n"
            "  -fproxy_port #    : Opt : Connect to forward proxy via port #\n"
            "  -disable_heartbeat_monitor: Opt : Disable thread-hang detection\n"
            "  -dbhost NAME      : Specify DB host to connect to. Default 'localhost'\n"
            "  -threads COUNT    : Specify number of worker threads. Default 4\n"
            "  -daemon           : Run as daemon\n"
            "  -stackpath PATH   : Write cores to path specified. No trailing slash please\n"
            "  -always_re_enroll : Opt : Reenroll for cert.pem renewal\n"
            "  -no_auto_upgrade  : Opt : Disable auto-upgrade\n"
            "  -maxlogmb             : Opt : Specify maximum MB to use for argo logging.\n"
            "  -memory_arena_count # : Opt : Sets a hard limit on the maximum number of arenas that can be created. Linux only\n"
            "  -nosqlite           : do not use sqlt cache by default\n"
            "  --print-core FILE : Opt : Read FILE and print stack\n"
            "  --print-core-force FILE : Opt : Read FILE and print stack, without checking app name/version\n"

           );
    zpath_app_logging_usage_print();
    va_end(list);
}

int zpn_sitec_global_init()
{
    struct timespec ts = {0, 0};

    if (sitec_gs.magic == SITEC_GLOBAL_MAGIC) {
        fprintf(stdout,"ERROR: sitec global state already initilized\n");
        fflush(stdout);
        sleep(1);
        exit(1);
    }

    sitec_gs.lock = (pthread_mutex_t)PTHREAD_MUTEX_INITIALIZER;

    pthread_mutex_lock(&sitec_gs.lock);
    memset(&sitec_gs, 0, sizeof(struct zpn_sitec_global_state));
    sitec_gs.magic = SITEC_GLOBAL_MAGIC;
    sitec_gs.sc_start_time_s = epoch_s();;

    if (clock_gettime(CLOCK_MONOTONIC, &ts) == 0) {
        sitec_gs.sc_start_time_m_s = ts.tv_sec;
    }

    if ((sitec_gs.configured_cpus = sysconf(_SC_NPROCESSORS_CONF)) == -1) {
        SITEC_LOG(AL_NOTICE, "failed to read number of configured cpus - %s", strerror(errno));
    }

    if ((sitec_gs.available_cpus = sysconf(_SC_NPROCESSORS_ONLN)) == -1) {
        SITEC_LOG(AL_NOTICE, "failed to read number of available cpus - %s", strerror(errno));
    }

    sitec_gs.sitec_state = ZPN_CALLOC(sizeof(struct zpn_sitec_state));
    memset(sitec_gs.sitec_state, 0, sizeof(struct zpn_sitec_state));

    sitec_gs.firedrill_cfg.status = ZPN_SITEC_FIREDRILL_DISABLED;

    pthread_mutex_unlock(&sitec_gs.lock);

    return ZPATH_RESULT_NO_ERROR;
}

int zpn_sitec_time_differential(struct zpn_sitec_state *scs, int64_t *diff_us)
{
    static int ever = 0;
    static int64_t last_diff_us = 0;

    if (scs && scs->ctrl_fohh_conn && (fohh_get_state(scs->ctrl_fohh_conn) == fohh_connection_connected)) {
        int64_t new_diff_us;
        if (fohh_peer_epoch_us_diff(scs->ctrl_fohh_conn, &new_diff_us) == FOHH_RESULT_NO_ERROR) {
            ever = 1;
            last_diff_us = new_diff_us;
        }
    }
    if (!ever) return ZPN_RESULT_NOT_READY;
    *diff_us = last_diff_us;
    return ZPN_RESULT_NO_ERROR;
}

static void log_debug_verify_version_f(int priority, char *log_buf)
{
    char buf[1000];

    snprintf(buf, sizeof(buf), "%s: %s", SITEC_LOG_NAME, log_buf);

    /* Override log priority to be debug, not error or notice as we are trying to infer if develop mode is enabled or not by checking */
    ZPATH_LOG(LOG_DEBUG, "%s", buf);
}

static int zpn_sitec_verify_version(void)
{
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();
    char restart_version[500] = "";
    int verify_res = ZCRYPT_RESULT_NO_ERROR;
    int using_develop_certs;

    if (gs->develop_certs_mode != zcrypt_metadata_develop_mode_unknown) {

        /* We know the develop mode setting via env, this is new sarge */
        using_develop_certs = (gs->develop_certs_mode == zcrypt_metadata_develop_mode_enabled) ? 1 : 0;
        verify_res = zcrypt_metadata_verify_version(ZCRYPT_VERSION_CHECKER_DEFAULT_FILENAME,
                                                    ZCRYPT_VERSION_CHECKER_DEFAULT_METADATA,
                                                    NULL, restart_version, sizeof(restart_version),
                                                    log_verify_version_f, using_develop_certs, 1);
    } else {

        /* We dont have develop mode setting, old sarge, try both with/without develop mode enabled to be backward compatible */

        using_develop_certs = 0; /* Default for prod, first try with no develop cert */
        ZPN_LOG(AL_INFO, "Verify software with prod metatdata");
        verify_res = zcrypt_metadata_verify_version(ZCRYPT_VERSION_CHECKER_DEFAULT_FILENAME,
                                                    ZCRYPT_VERSION_CHECKER_DEFAULT_METADATA,
                                                    NULL, restart_version, sizeof(restart_version),
                                                    log_debug_verify_version_f, using_develop_certs, 1);

        if (verify_res != ZCRYPT_RESULT_NO_ERROR) {
            using_develop_certs = 1; /* Try again with develop certs */
            ZPN_LOG(AL_INFO, "Verify software with develop metatdata, prod metadata verify unsuccessful");
            verify_res = zcrypt_metadata_verify_version(ZCRYPT_VERSION_CHECKER_DEFAULT_FILENAME,
                                                        ZCRYPT_VERSION_CHECKER_DEFAULT_METADATA,
                                                        NULL, restart_version, sizeof(restart_version),
                                                        log_debug_verify_version_f, using_develop_certs, 1);
        } else {
            ZPN_LOG(AL_DEBUG, "Verify software with prod metatdata successful");
        }
    }

    /* Process results of checks by producing final logs */
    if ((verify_res == ZCRYPT_RESULT_NO_ERROR) && strlen(restart_version) > 0) {
        if (using_develop_certs) {
            ZPN_LOG(AL_NOTICE, "%s restart software version verified: %s, develop mode is enabled", SITEC_LOG_NAME, restart_version);
        } else {
            ZPN_LOG(AL_NOTICE, "%s restart software version verified: %s", SITEC_LOG_NAME, restart_version);
        }
    } else {
        ZPN_LOG(AL_ERROR, "%s restart software version not present or unable to verify", SITEC_LOG_NAME);
    }

    return verify_res;
}

int zpn_sitec_upgrade_prep(int64_t sitec_id,
                           void *upgrade_state,
                           int64_t time_delta,
                           int auto_upgrade_disabled)
{
    int result;

    /* Remember prior version and version state */
    static int seen_version = 0;
    static char written_version[1000] = "";

    struct zpn_site_controller_version *version;
    struct zpn_sub_module_upgrade *sm_upgrade[4] = {NULL};
    size_t sm_upgrade_count = sizeof(sm_upgrade)/sizeof(sm_upgrade[0]);
    static int prep_to_reboot = 0;
    int64_t now;
    struct zpn_sitec_state *sc = (struct zpn_sitec_state *)upgrade_state;
    int64_t sc_restart_time = 0;
    char *exp_version = NULL;
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();
    int i;
    static char sarge_written_version[ZPATH_VERSION_STR_MAX_LEN] = {'\0'};
    static int prev_os_upgrade_enabled = 0;

    /* Starts in microseconds, ends up seconds */
    now = epoch_us();

    now += time_delta;
    now /= 1000000l;

    /* Check for version updates for sitec */
    result = zpn_sitec_version_get_by_id(sitec_id,
                                         &version,
                                         NULL,
                                         NULL,
                                         0);

    if (auto_upgrade_disabled && ((ZPN_RESULT_NO_ERROR == result) || (ZPN_RESULT_NOT_FOUND == result))) {
        /*
         * I am here just to let sarge know that we have successfully communicated to the cloud and ask sarge to be
         * happy about me. Writing the current version, so that sarge don't try to upgrade at any cost.
         * ZPN_RESULT_NOT_FOUND - means we CONNECTED to the cloud, but cloud didn't find any entry for the private broker.
         * ZPN_RESULT_NO_ERROR - means we CONNECTED to the cloud and golden.
         */
        if (!seen_version) {
            if (zpn_enroll_update_version(FILENAME_VERSION,
                               ZPATH_VERSION,
                               written_version,
                               sizeof(written_version)) == ZPN_RESULT_NO_ERROR) {
                seen_version = 1;
            }
        }
    } else if (result == ZPN_RESULT_NO_ERROR && version) {

        if(!sub_module_fail_restart_time && sub_module_upgrade_failed)
           sub_module_fail_restart_time = now + SUB_MODULE_FAIL_TIME;

       if(sub_module_upgrade_failed){
            if(version->expected_version && version->current_version){
                exp_version = (strcmp(version->expected_version,version->current_version)?version->current_version:version->previous_version);
            }else if(!version->expected_version){
                exp_version = version->previous_version;
            }
            sc_restart_time = sub_module_fail_restart_time;
       }else{
            exp_version = version->expected_version;
            sc_restart_time = version->next_restart_time;
       }

        /* Update the restart time */
        ZPN_DEBUG_SITEC("Update %s next restart time, next_restart_time = %"PRId64", "
                           "time_delta_us = %"PRId64", now = %"PRId64"",
                            SITEC_LOG_NAME, sc_restart_time, time_delta, epoch_s());
        sc->next_restart_time = sc_restart_time;
        sc->time_delta_us = time_delta;

        if (now &&
            (now < sc_restart_time) &&
            (now > sc_restart_time - SLOW_STOP_TIME_S)) {
            /* We need to restart in the next 5 minutes. We should
             * soft-close connections, etc */
            if ((((sc_restart_time - now) % 10) == 0) ||
                ((sc_restart_time - now) < 10)) {

                if(sub_module_upgrade_failed)
                    ZPATH_LOG(AL_NOTICE, "%s restarting in %"PRId64" seconds due to sub module upgrade failure", SITEC_LOG_NAME, (sc_restart_time - now));
                else
                    ZPATH_LOG(AL_NOTICE, "%s restarting in %ld seconds", SITEC_LOG_NAME, (long)(sc_restart_time - now));
                prep_to_reboot = 1;
            }
        }
        if (now &&
            prep_to_reboot &&
            (now > sc_restart_time)) {
            zpn_sitec_verify_version();
            ZPATH_LOG(AL_NOTICE, "%s restarting", SITEC_LOG_NAME);
            sleep(1);
            exit(CHILD_UPGRADE_SUCCESS_EXIT_CODE);
        }

        if (exp_version && (exp_version[0]!='\0')) {
            if (!seen_version) {
                if (zpn_enroll_update_version(FILENAME_VERSION,
                                              exp_version,
                                              written_version,
                                              sizeof(written_version)) == ZPN_RESULT_NO_ERROR) {
                    ZPN_LOG(AL_NOTICE, "%s expected software version updated to: %s", SITEC_LOG_NAME, exp_version);
                    seen_version = 1;
                }
            } else {
                /* Prior written version with new expected version */
                if (strcmp(written_version, exp_version)) {
                    if (zpn_enroll_update_version(FILENAME_VERSION,
                                                  exp_version,
                                                  written_version,
                                                  sizeof(written_version)) == ZPN_RESULT_NO_ERROR) {
                        ZPN_LOG(AL_NOTICE, "%s expected software version updated to: %s", SITEC_LOG_NAME, exp_version);
                        seen_version = 1;
                    }
                }
            }
        } else if(sub_module_upgrade_failed && exp_version){
            if (zpn_enroll_update_version(FILENAME_VERSION,
                                          exp_version,
                                          written_version,
                                          sizeof(written_version)) == ZPN_RESULT_NO_ERROR) {
                ZPN_LOG(AL_NOTICE, "%s expected software version updated to: default", SITEC_LOG_NAME);
            }
        } else {
            /* Common, for brand new private brokers that haven't ever been upgraded */
            // No specific expected version of software is specified. Go ahead and write current running version
            if (!seen_version) {
                if (zpn_enroll_update_version(FILENAME_VERSION,
                                              ZPATH_VERSION,
                                              written_version,
                                              sizeof(written_version)) == ZPN_RESULT_NO_ERROR) {
                    seen_version = 1;
                }
            }
        }

        if (zvm_vm_type_is_zscaler_rh_image()) {
            /* Update sarge version into sarge_and_os_upgrade_cfg.json if it changed */
            if (version->expected_sarge_version && version->expected_sarge_version[0] != '\0') {
                if (strcmp(sarge_written_version, version->expected_sarge_version) &&
                    strcmp(gs->sarge_version, version->expected_sarge_version)) {
                    if (zpath_upgrade_set_sarge_version(version->expected_sarge_version,
                                    sarge_written_version,
                                    sizeof(sarge_written_version)) == ZPN_RESULT_NO_ERROR) {
                        ZPN_LOG(AL_NOTICE, "Sarge expected software version updating to %s", version->expected_sarge_version );
                    }
                }
            }

            /* Update os upgrade into sarge_and_os_upgrade_cfg.json if it changed*/
            if (version->os_upgrade_enabled != prev_os_upgrade_enabled) {
                if (zpath_upgrade_set_os_upgrade_enabled_cfg(version->os_upgrade_enabled) == ZPN_RESULT_NO_ERROR) {
                    ZPN_LOG(AL_NOTICE, "Os upgrade enabled is now %d", version->os_upgrade_enabled);
                    prev_os_upgrade_enabled = version->os_upgrade_enabled;
                }
            }
        }
    } else if (result == ZPN_RESULT_NOT_FOUND) {
        /* This happens all the time when the DB is not yet
         * updated with what version of software we should run. In
         * this case we just write our version out.
         *
         * Note that we cannot get a 'not found' response without
         * successfully communicating with the cloud.
         */
        if (!seen_version) {
            /* We do this relatively silently since it is no big deal */
            if (zpn_enroll_update_version(FILENAME_VERSION,
                               ZPATH_VERSION,
                               written_version,
                               sizeof(written_version)) == ZPN_RESULT_NO_ERROR) {
                ZPN_LOG(AL_DEBUG, "%s expected software version updated to zpath version: %s", SITEC_LOG_NAME, ZPATH_VERSION);
                seen_version = 1;
            }
        } else {
            /* We saw a version once, but now we're not
             * found. Odd, but not that noteworthy. */
        }
    }

    if(!prep_to_reboot) {
        result = zpn_sub_module_upgrade_get_by_entity_gid(gs->sitec_id,
                                                        &sm_upgrade[0],
                                                        &sm_upgrade_count,
                                                        NULL,
                                                        NULL,
                                                        0);

        if (result == ZPN_RESULT_NO_ERROR) {
            for (i = 0; i < sm_upgrade_count; i++) {
                //Check if geoip file needs to be updated
                if (sm_upgrade[i]->role && !strcmp(sm_upgrade[i]->role, "MMDB_GEOIP")) {
                    //Check if we have the expected version and its different from current
                    if (sm_upgrade[i]->expected_version &&
                        sm_upgrade[i]->current_version  &&
                        sm_upgrade[i]->expected_version[0]!='\0' &&
                        strcmp(sm_upgrade[i]->expected_version, sm_upgrade[i]->current_version)) {
                        //check if reload time has reached
                        if (now && (now > sm_upgrade[i]->upgrade_time) && sm_upgrade[i]->upgrade_time) {
                            memset(mmdb_geoip_version_g, 0, MAX_VERSION_LEN);
                            snprintf(mmdb_geoip_version_g, MAX_VERSION_LEN, "%s", sm_upgrade[i]->expected_version);
                        }
                    }
                }

                //Check if isp file needs to be updated
                if (sm_upgrade[i]->role && !strcmp(sm_upgrade[i]->role, "MMDB_ISP")) {
                    //Check if we have the expected version and its different from current
                    if (sm_upgrade[i]->expected_version &&
                        sm_upgrade[i]->current_version &&
                        sm_upgrade[i]->expected_version[0]!='\0' &&
                        strcmp(sm_upgrade[i]->expected_version, sm_upgrade[i]->current_version)) {
                        //check if reload time has reached
                        if (now && (now > sm_upgrade[i]->upgrade_time) && sm_upgrade[i]->upgrade_time) {
                            memset(mmdb_isp_version_g, 0, MAX_VERSION_LEN);
                            snprintf(mmdb_isp_version_g, MAX_VERSION_LEN, "%s", sm_upgrade[i]->expected_version);
                        }
                    }
                }
            }
        }else{
            //We might be downloading the shards or the data might not be available for this pb right now, in any case, we dont want to error out
        }
    }

    return ZPATH_RESULT_NO_ERROR;
}

int zpn_sitec_main_loop(void)
{
    int res;
    int once_waiting = 0;
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();
    int64_t last_log_time_s = 0;
    uint64_t q_depth = 0;
    uint64_t adj_count = 0;
    uint64_t skip_count = 0;
    static int  num_of_hw_id_changed = 0;
    uint64_t wait_count = 0;

    argo_set_adj_time(0);
    gs->cloud_time_delta_us = 0;

    while (1) {
        int64_t time_delta_us = 0;

        /* Keep watchdog happy */
        zthread_heartbeat(gs->zthread);

        /* Count iterations */
        adj_count++;

        if (zpn_sitec_time_differential(gs->sitec_state, &time_delta_us) != ZPN_RESULT_NO_ERROR) {
            if (wait_count%120 == 0) {
                //print every 2 mins...
                ZPATH_LOG(AL_NOTICE, "site controller - waiting for time synchronization");
            }
            wait_count++;
            once_waiting = 1;
            sleep(1);
            continue;
        }

        q_depth = fohh_connection_get_queue_depth(gs->sitec_state->ctrl_fohh_conn);

        /* Skip time adjustment if queue depth is too deep as time delta will not be accurate anymore */
        if (q_depth <= ZPN_SITEC_MAX_TIME_ADJ_FOHH_Q_DEPTH) {
            int64_t d;
            char neg = '-';

            argo_set_adj_time(time_delta_us);
            gs->cloud_time_delta_us = time_delta_us;

            /* Upgrade prepare, unlimited adjust for enroll */
            zpn_enroll_upgrade_prep(zpn_sitec_upgrade_prep,
                                    gs->sitec_id,
                                    (void *)gs->sitec_state,
                                    time_delta_us,
                                    gs->auto_upgrade_disabled);

            if (time_delta_us < 0) {
                d = 0 - time_delta_us;
            } else {
                d = time_delta_us;
                neg = '+';
            }

            if (once_waiting) {
                ZPATH_LOG(AL_NOTICE, "%s - Initial Time synchronized, Local time %c %ld.%06lds = cloud_time",
                          SITEC_LOG_NAME,
                          neg,
                          (long)d / 1000000, (long)d % 1000000);
            } else {
                if ((adj_count % ZPN_SITEC_TIME_ADJUST_LOG_COUNT) == 0) {
                    ZPATH_LOG(AL_NOTICE, "%s - Non-Initial Time synchronized, Local time %c %ld.%06lds = cloud_time",
                              SITEC_LOG_NAME,
                              neg,
                              (long)d / 1000000, (long)d % 1000000);
                }
            }
            once_waiting = 0;
        } else {
            skip_count++;
            if ((adj_count % ZPN_SITEC_TIME_ADJUST_LOG_COUNT) == 0) {
                if (skip_count > 0) {
                    ZPN_LOG(AL_NOTICE, "Skipped %"PRId64" log timestamp adjustments, control queue depth: %"PRId64"", skip_count, q_depth);
                    skip_count = 0;
                }
            }
        }

        if(labs(time_delta_us) > ZPN_SITEC_MAX_CLOUD_TIME_ADJUST_US){
            /* Log every log interval, log hold-down time */
            if (labs(epoch_s() - last_log_time_s) >= ZPN_SITEC_MAX_TIME_CRITICAL_LOG_FREQ_S) {
                ZPN_LOG(AL_CRITICAL, "Time difference with cloud exceeds: %ld.%06lds, current difference is: %ld.%06lds",
                        (long)(ZPN_SITEC_MAX_CLOUD_TIME_ADJUST_US / 1000000L),
                        (long)(ZPN_SITEC_MAX_CLOUD_TIME_ADJUST_US % 1000000L),
                        (long)(time_delta_us / 1000000L),
                        (long)(time_delta_us % 1000000L));
                last_log_time_s = epoch_s();
            }
        }

        zthread_heartbeat(gs->zthread);
        //sitec_state_pause_evaluate();
        //sitec_admin_probe_restart_evaluable();

        res = zhw_id_check_changed();
        if (res > num_of_hw_id_changed) {
            num_of_hw_id_changed = res;
            zpn_sitec_set_hw_id_changed_and_log_time_us(epoch_us(), num_of_hw_id_changed);
        }

        sleep(1);
    }
}

static int do_log_file(struct argo_log_collection *collection,
                       const char *name,
                       const char *path,
                       const char *filename)
{
    char full_file[1000];

    snprintf(full_file, sizeof(full_file), "%s/%s", path, filename);

    struct argo_log_file *log_file;
    log_file = argo_log_file_create(collection,
                                    full_file,
                                    NULL,
                                    1024 * 1024 * 1024,
                                    argo_serialize_binary);
    if (!log_file) {
        return ZPN_RESULT_ERR;
    }

    if (!argo_log_read(collection, name, 0, 1, argo_log_file_callback, NULL, log_file, NULL, 0)) {
        return ZPN_RESULT_ERR;
    }
    return ZPN_RESULT_NO_ERROR;
}

int file_fetch_callback(char *filename, int is_update, int fcode, char *version)
{
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();
    struct zpn_file_fetch_key key;
    memset(&key, 0, sizeof(struct zpn_file_fetch_key));
    key.filename = filename;
    key.is_update = is_update;
    key.version = version;
    key.enc_or_dec_pvt_key = NULL;
    key.enc_or_dec_pvt_key2 = NULL;

    file_stats[fcode].num_key_cb++;
    ZPN_LOG(AL_NOTICE, "File code: %d. Getting the key from public broker(Total public broker callbacks:%d)",fcode,file_stats[fcode].num_key_cb);
    if (zpn_send_zpn_file_fetch_key(gs->sitec_state->ctrl_fohh_conn,
                                    fohh_connection_incarnation(gs->sitec_state->ctrl_fohh_conn),
                                    &key)) {
        ZPN_LOG(AL_ERROR, "file_fetch_callback: Unable to fetch decryption key from broker, will retry");
        key_retry_flag[fcode] = 1;
    }

    return ZPATH_RESULT_NO_ERROR;
}

int zpn_sitec_load_isp_file(char *mmdb_fetch_proxyname, char *mmdb_fetch_hostname)
{
    int res = ZPN_RESULT_ERR;
    char *isp_filename = FILENAME_ISP_ENC;
    char isp_path_full[FILE_PATH_LEN] = "";
    char *geodb_path = FETCH_PATH_DEVELOP;
    int develop_mode = 1;
    char *ispip_data = NULL;
    char *ispip_fallback_data = NULL;
    size_t ispip_size = 0;
    size_t ispip_fallback_size = 0;
    int isp_sanity = 1;
    int result;

    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();

    if (gs->disable_geo_ip != zcrypt_metadata_disable_geoip_true) {
        memset(mmdb_isp_version_g, 0, MAX_VERSION_LEN);

        if (gs->develop_certs_mode == zcrypt_metadata_develop_mode_disabled) {
            geodb_path = FETCH_PATH;
            develop_mode = 0;
        }

        snprintf(isp_path_full, FILE_PATH_LEN, "%s/GeoIP2-ISP.mmdb", geodb_path);

        write_version(FILENAME_ISP_ENC, VERSION_NONE);

        //CHECK FOR ISPIP FILES
        if (file_exists(FILENAME_ISP_FALLBACK)) {
            ispip_fallback_data = decrypt_file((char*)gs->cfg_hw_key.data, FILENAME_ISP_FALLBACK, &ispip_fallback_size, 0);
            if (!ispip_fallback_data) {
                ZPN_LOG(AL_ERROR,"Unable to decrypt file:%s",FILENAME_ISP_FALLBACK);
            }
        }

        if (file_exists(FILENAME_ISP)) {
            ispip_data = decrypt_file((char*)gs->cfg_hw_key.data, FILENAME_ISP, &ispip_size, 0);
            if (!ispip_data) {
                ZPN_LOG(AL_ERROR,"Unable to decrypt file:%s",FILENAME_ISP);
            }
        }

        //INIT ISP DB IF ONE OF THEM EXISTS
        if (ispip_fallback_data || ispip_data) {
            ZPN_LOG(LOG_INFO,"Loading GeoIP-ISP database...\n");
            if (ispip_data) {
                snprintf(zpath_ispip_filename, sizeof(zpath_ispip_filename), "%s", FILENAME_ISP);
                geoip_isp_db_file = zpath_ispip_filename;
                result = zpath_ispip_init_from_memory(geoip_isp_db_file, ispip_data, ispip_size, ispip_fallback_data, ispip_fallback_size);
            } else {
                snprintf(zpath_ispip_filename, sizeof(zpath_ispip_filename), "%s", FILENAME_ISP_FALLBACK);
                geoip_isp_db_file = zpath_ispip_filename;
                ZPN_LOG(LOG_INFO,"Loading GeoIP-ISP database from fallback file...\n");
                result = zpath_ispip_init_from_memory(geoip_isp_db_file, ispip_fallback_data, ispip_fallback_size, NULL, 0);
            }
            if (result) {
                ZPN_LOG(AL_ERROR, "GeoIP-ISP database failed: %s",
                        zpath_result_string(result));
                geoip_isp_db_file = NULL;
            } else {
                result = zpath_ispip_sanity_verify();
                if (result) {
                    ZPN_LOG(AL_ERROR,"GeoIP-ISP db failed sanity check\n");
                    geoip_isp_db_file = NULL;
                    isp_sanity = 0;
                } else {
                    ZPN_LOG(LOG_INFO,"Successfully loaded GeoIP-ISP database..\n");
                    isp_sanity = 1;
                }

                if (!geoip_isp_db_file && ispip_fallback_data && !zpath_is_ispip_fallback_running()) {
                    snprintf(zpath_ispip_filename, sizeof(zpath_ispip_filename), "%s", FILENAME_ISP_FALLBACK);
                    geoip_isp_db_file = zpath_ispip_filename;
                    ZPN_LOG(LOG_INFO,"Loading GeoIP-ISP database from fallback file...\n");
                    result = zpath_trigger_ispip_reload(NULL, ispip_fallback_data, ispip_fallback_size, NULL, 0);
                    if (!result) {
                        result = zpath_ispip_sanity_verify();
                        if (!result) {
                            isp_sanity = 1;
                            ZPN_LOG(LOG_INFO,"Successfully loaded GeoIP-ISP database from fallback file..\n");
                        } else {
                            isp_sanity = 0;
                            ZPN_LOG(AL_ERROR,"GeoIP-ISP db failed sanity check\n");
                            geoip_isp_db_file = NULL;
                        }
                    } else {
                        ZPN_LOG(AL_ERROR, "GeoIP-ISP databse init failed from fallback file: %s",
                            zpath_result_string(result));
                        isp_sanity = 0;
                        geoip_isp_db_file = NULL;
                    }
                }
            }
        } else {
            ZPN_LOG(LOG_INFO,"GeoIP-ISP database not present.\n");
        }

        if (isp_sanity && zpath_is_ispip_fallback_running()) {
            char file_version[FILE_PATH_LEN] = {0};
            zcrypt_get_metadata_version(FILENAME_ISP_FALLBACK_META, file_version, FILE_PATH_LEN, log_verify_version_f, 0);
            write_version(FILENAME_ISP_ENC, file_version);
            ZPN_LOG(LOG_INFO,"Running GeoIP-ISP db version:%s", file_version);
            if (ispip_data) {
                ZPN_FF_FREE(ispip_data);
                ispip_data = NULL;
            }
        } else if(isp_sanity && zpath_is_ispip_running()) {
            char file_version[FILE_PATH_LEN] = {0};
            zcrypt_get_metadata_version(FILENAME_ISP_META, file_version, FILE_PATH_LEN, log_verify_version_f, 0);
            write_version(FILENAME_ISP_ENC, file_version);
            ZPN_LOG(LOG_INFO,"Running GeoIP-ISP db version:%s", file_version);
            if (ispip_fallback_data) {
                ZPN_FF_FREE(ispip_fallback_data);
                ispip_fallback_data = NULL;
            }
        } else {
            if (ispip_data) {
                ZPN_FF_FREE(ispip_data);
                ispip_data = NULL;
            }
            if (ispip_fallback_data) {
                ZPN_FF_FREE(ispip_fallback_data);
                ispip_fallback_data = NULL;
            }
        }

        ZPN_LOG(AL_NOTICE, "Starting mmdb fetch thread doe isp file");

        fetch_file(mmdb_fetch_proxyname,mmdb_fetch_hostname,isp_path_full,isp_filename,
                NULL, 0, 1, file_fetch_callback, 1, 1, version_check_callback, MMDB_ISP, 1, develop_mode,download_fail_callback, config_disable_check_callback,gs->zcdns);
        res = ZPN_RESULT_NO_ERROR;
    }

    return res;
}

int zpn_sitec_load_geoip_file(char *mmdb_fetch_proxyname, char *mmdb_fetch_hostname)
{
    int res = ZPN_RESULT_ERR;
    char *geodb_path = FETCH_PATH_DEVELOP;
    char *geoip_filename = FILENAME_GEOIP_ENC;
    char geodb_path_full[FILE_PATH_LEN] = "";
    int develop_mode = 1;
    char *geoip_data = NULL;
    char *geoip_fallback_data = NULL;
    size_t geoip_size = 0;
    size_t geoip_fallback_size = 0;
    int geo_sanity = 1;
    int result;

    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();

    if (gs->disable_geo_ip != zcrypt_metadata_disable_geoip_true) {
        memset(mmdb_geoip_version_g, 0, MAX_VERSION_LEN);

        if(gs->develop_certs_mode == zcrypt_metadata_develop_mode_disabled){
            geodb_path = FETCH_PATH;
            develop_mode = 0;
        }

        snprintf(geodb_path_full, FILE_PATH_LEN, "%s/GeoIP2-City.mmdb", geodb_path);
        write_version(FILENAME_GEOIP_ENC, VERSION_NONE);

        //CHECK FOR GEOIP FILES
        if (file_exists(FILENAME_GEOIP_FALLBACK)) {
            geoip_fallback_data = decrypt_file((char*)gs->cfg_hw_key.data,
                                                FILENAME_GEOIP_FALLBACK,
                                                &geoip_fallback_size,
                                                0);
            if (!geoip_fallback_data) {
                ZPN_LOG(AL_ERROR, "Unable to decrypt file:%s", FILENAME_GEOIP_FALLBACK);
            }
        }

        if (file_exists(FILENAME_GEOIP)) {
            geoip_data = decrypt_file((char*)gs->cfg_hw_key.data, FILENAME_GEOIP, &geoip_size, 0);
            if (!geoip_data) {
                ZPN_LOG(AL_ERROR,"Unable to decrypt file:%s",FILENAME_GEOIP);
            }
        }

        //if geoip, then usual, if only fallback, initialise with it and leave the fallback part null
        //INIT GEOIP DB IF ONE OF THEM IS PRESENT
        if (geoip_data || geoip_fallback_data) {
            ZPN_LOG(LOG_INFO,"Loading GeoIP-City database...\n");
            if (geoip_data) {
                snprintf(zpath_geoip_filename, sizeof(zpath_geoip_filename), "%s", FILENAME_GEOIP);
                geoip_db_file = zpath_geoip_filename;
                result = zpath_geoip_init_from_memory(geoip_db_file, geoip_data, geoip_size, geoip_fallback_data, geoip_fallback_size);
            } else {
                snprintf(zpath_geoip_filename, sizeof(zpath_geoip_filename), "%s", FILENAME_GEOIP_FALLBACK);
                geoip_db_file = zpath_geoip_filename;
                ZPN_LOG(LOG_INFO,"Loading GeoIP-City database from fallback file...\n");
                result = zpath_geoip_init_from_memory(geoip_db_file, geoip_fallback_data, geoip_fallback_size, NULL, 0);
            }
            if (result) {
                ZPN_LOG(AL_ERROR, "GeoIP-City databse init failed: %s",
                        zpath_result_string(result));
                geoip_db_file = NULL;
            } else {
                result = zpath_geoip_sanity_verify();
                if (result) {
                    ZPN_LOG(AL_ERROR, "GeoIP-City db failed sanity check\n");
                    geo_sanity = 0;
                    geoip_db_file = NULL;
                } else {
                    geo_sanity = 1;
                    ZPN_LOG(LOG_INFO, "Successfully loaded GeoIP-City database..\n");
                }

                if (!geoip_db_file && geoip_fallback_data && !zpath_is_geoip_fallback_running()) {
                    snprintf(zpath_geoip_filename, sizeof(zpath_geoip_filename), "%s", FILENAME_GEOIP_FALLBACK);
                    geoip_db_file = zpath_geoip_filename;
                    ZPN_LOG(LOG_INFO,"Loading GeoIP-City database from fallback file...\n");
                    result = zpath_trigger_geoip_reload(NULL, geoip_fallback_data, geoip_fallback_size, NULL, 0);
                    if (!result) {
                        result = zpath_geoip_sanity_verify();
                        if (!result) {
                            ZPN_LOG(LOG_INFO,"Successfully loaded GeoIP-City database from fallback file..\n");
                            geo_sanity = 1;
                        } else {
                            ZPN_LOG(AL_ERROR,"GeoIP-City db failed sanity check\n");
                            geo_sanity = 0;
                            geoip_db_file = NULL;
                        }
                    } else {
                        ZPN_LOG(AL_ERROR, "GeoIP-City databse init failed from fallback file: %s",
                            zpath_result_string(result));
                        geo_sanity = 0;
                        geoip_db_file = NULL;
                    }
                }
            }
        } else {
            ZPN_LOG(LOG_INFO,"GeoIP-City database not present.\n");
        }

        if (geo_sanity && zpath_is_geoip_fallback_running()) {
            char file_version[FILE_PATH_LEN] = {0};
            zcrypt_get_metadata_version(FILENAME_GEOIP_FALLBACK_META, file_version, FILE_PATH_LEN, log_verify_version_f, 0);
            write_version(FILENAME_GEOIP_ENC, file_version);
            ZPN_LOG(LOG_INFO,"Running GeoIP-City version:%s", file_version);
            if (geoip_data) {
                ZPN_FF_FREE(geoip_data);
                geoip_data = NULL;
            }
        } else if (geo_sanity && zpath_is_geoip_running()) {
            char file_version[FILE_PATH_LEN] = {0};
            zcrypt_get_metadata_version(FILENAME_GEOIP_META, file_version, FILE_PATH_LEN, log_verify_version_f, 0);
            ZPN_LOG(LOG_INFO,"Running GeoIP-City version:%s", file_version);
            write_version(FILENAME_GEOIP_ENC, file_version);
            if (geoip_fallback_data) {
                ZPN_FF_FREE(geoip_fallback_data);
                geoip_fallback_data = NULL;
            }
        } else {
            if (geoip_data) {
                ZPN_FF_FREE(geoip_data);
                geoip_data = NULL;
            }
            if (geoip_fallback_data) {
                ZPN_FF_FREE(geoip_fallback_data);
                geoip_fallback_data = NULL;
            }
        }

        fetch_file(mmdb_fetch_proxyname,mmdb_fetch_hostname,geodb_path_full,geoip_filename,
                NULL, 0, 1, file_fetch_callback, 1, 1, version_check_callback, MMDB_GEOIP, 1, develop_mode,download_fail_callback, config_disable_check_callback, gs->zcdns);

        res = ZPN_RESULT_NO_ERROR;
    }

    return res;
}

int get_sitec_additional_debug_logs(struct additional_debug_logs *dbg_logs) {
    char uptime_str[32];
    char sitec_status_data[1024];
    char connection_info[2048];
    char fohh_desc_info[1024];
    char sc_data[1024];
    struct fohh_connection *fohh_conn = NULL;
    char *s;
    char *e;
    uint64_t tx_b = 0;
    uint64_t rx_b = 0;
    uint64_t tx_o = 0;
    uint64_t rx_o = 0;
    uint64_t tx_raw_tlv = 0;
    uint64_t rx_raw_tlv = 0;
    int64_t sys_disk_total_bytes = 0;

    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();

    if (dbg_logs == NULL) {
        SITEC_LOG(AL_ERROR, "Invalid argument passed while gathering additional logs for sitec");
        return ZPATH_RESULT_ERR;
    }

    sitec_state_get_uptime_str(uptime_str, sizeof(uptime_str));
    zpath_system_get_disk_info(NULL, NULL, &sys_disk_total_bytes);

    /* Other stuff can be added here in the future */
    memset(sitec_status_data, 0, sizeof(sitec_status_data));
    snprintf(sitec_status_data, sizeof(sitec_status_data),
            "SiteC Status:ID=%"PRId64":Name=%s:Ver=%s:Mem(System|Process)=%d%%|%d%%:"
            "Mem_abs(MemTotal|MemFree|SwapTotal|SwapFree|SysUsed|ProcessUsed)= "
            "%" PRIu64 "kB |%" PRIu64 "kB |%" PRIu64 "kB |%" PRIu64 "kB |"
            "%" PRIu64 "kB |%" PRIu64 "kB : Disk(Avail||Total)=%.2fGB|%.2fGB:"
            "CPU(Util|Steal|Configured|Avail)=%d%%|%d%%|%d|%d:Uptime=%s\n",
           gs->sitec_id, gs->cfg_name, ZPATH_VERSION,
           gs->sys_stats.system_mem_util, gs->sys_stats.process_mem_util,
           gs->sys_stats.memtotal_abs_mem, gs->sys_stats.memfree_abs_mem, gs->sys_stats.swaptotal_abs_mem, gs->sys_stats.swapfree_abs_mem, gs->sys_stats.system_used_abs_mem, gs->sys_stats.process_used_abs_mem,
           (double)gs->sys_stats.free_disk_bytes_for_non_root_user / ZPN_SYSTEM_GB_TO_BYTES, (double)sys_disk_total_bytes / ZPN_SYSTEM_GB_TO_BYTES,
           gs->sys_stats.cpu_util, gs->sys_stats.cpu_steal_perc,
           gs->configured_cpus, gs->available_cpus, uptime_str);

    dbg_logs->num_bytes = strlen(sitec_status_data);

    memset(connection_info, 0, sizeof(connection_info));
    s = connection_info;

    fohh_conn = wally_fohh_client_get_f_conn(gs->sitec_state->cfg_wally_fohh_client_handle);
    get_fohh_connection_stats(fohh_conn, &tx_b, &rx_b, &tx_o, &rx_o, &tx_raw_tlv, &rx_raw_tlv);
    memset(fohh_desc_info, 0, sizeof(fohh_desc_info));
    snprintf(fohh_desc_info, sizeof(fohh_desc_info),
            "Config connection: %s, uptime_s=%" PRId64 ", disconnect_duration_s %"PRId64", tx_b %"PRIu64", rx_b %"PRIu64"\n",
            fohh_description(fohh_conn), fohh_get_uptime_s(fohh_conn), fohh_conn_get_current_disconnect_duration_s(fohh_conn), tx_b, rx_b);
    e = s + sizeof(connection_info);
    s += sxprintf(s, e, "%s", fohh_desc_info);
    dbg_logs->num_bytes += strnlen(fohh_desc_info, sizeof(fohh_desc_info));

    fohh_conn = wally_fohh_client_get_f_conn(gs->sitec_state->rcfg_wally_fohh_client_handle);
    get_fohh_connection_stats(fohh_conn, &tx_b, &rx_b, &tx_o, &rx_o, &tx_raw_tlv, &rx_raw_tlv);
    memset(fohh_desc_info, 0, sizeof(fohh_desc_info));
    snprintf(fohh_desc_info, sizeof(fohh_desc_info),
            "Static Config connection: %s, uptime_s=%" PRId64 ", disconnect_duration_s %"PRId64", tx_b %"PRIu64", rx_b %"PRIu64"\n",
            fohh_description(fohh_conn), fohh_get_uptime_s(fohh_conn), fohh_conn_get_current_disconnect_duration_s(fohh_conn), tx_b, rx_b);
    s += sxprintf(s, e, "%s", fohh_desc_info);
    dbg_logs->num_bytes += strnlen(fohh_desc_info, sizeof(fohh_desc_info));

    fohh_conn = wally_fohh_client_get_f_conn(gs->sitec_state->ovd_wally_fohh_client_handle);
    get_fohh_connection_stats(fohh_conn, &tx_b, &rx_b, &tx_o, &rx_o, &tx_raw_tlv, &rx_raw_tlv);
    memset(fohh_desc_info, 0, sizeof(fohh_desc_info));
    snprintf(fohh_desc_info, sizeof(fohh_desc_info),
            "Config Override connection: %s, uptime_s=%" PRId64 ", disconnect_duration_s %"PRId64", tx_b %"PRIu64", rx_b %"PRIu64"\n",
            fohh_description(fohh_conn), fohh_get_uptime_s(fohh_conn), fohh_conn_get_current_disconnect_duration_s(fohh_conn), tx_b, rx_b);
    s += sxprintf(s, e, "%s", fohh_desc_info);
    dbg_logs->num_bytes += strnlen(fohh_desc_info, sizeof(fohh_desc_info));

    fohh_conn = gs->sitec_state->ctrl_fohh_conn;
    get_fohh_connection_stats(fohh_conn, &tx_b, &rx_b, &tx_o, &rx_o, &tx_raw_tlv, &rx_raw_tlv);
    memset(fohh_desc_info, 0, sizeof(fohh_desc_info));
    snprintf(fohh_desc_info, sizeof(fohh_desc_info),
            "Control connection: %s, uptime_s=%" PRId64 ", disconnect_duration_s %"PRId64", tx_b %"PRIu64", rx_b %"PRIu64"\n",
            fohh_description(fohh_conn), fohh_get_uptime_s(fohh_conn), fohh_conn_get_current_disconnect_duration_s(fohh_conn), tx_b, rx_b);
    s += sxprintf(s, e, "%s", fohh_desc_info);
    dbg_logs->num_bytes += strnlen(fohh_desc_info, sizeof(fohh_desc_info));

    fohh_conn = fohh_get_log_handle(zpn_event_collection);
    get_fohh_connection_stats(fohh_conn, &tx_b, &rx_b, &tx_o, &rx_o, &tx_raw_tlv, &rx_raw_tlv);
    memset(fohh_desc_info, 0, sizeof(fohh_desc_info));
    snprintf(fohh_desc_info, sizeof(fohh_desc_info),
            "Log(event_log) connection: %s, uptime_s=%" PRId64 ", disconnect_duration_s %"PRId64", tx_b %"PRIu64", rx_b %"PRIu64"\n",
            fohh_description(fohh_conn), fohh_get_uptime_s(fohh_conn), fohh_conn_get_current_disconnect_duration_s(fohh_conn), tx_b, rx_b);
    s += sxprintf(s, e, "%s", fohh_desc_info);
    dbg_logs->num_bytes += strnlen(fohh_desc_info, sizeof(fohh_desc_info));

    fohh_conn = fohh_get_log_handle(zpn_sitec_auth_collection);
    get_fohh_connection_stats(fohh_conn, &tx_b, &rx_b, &tx_o, &rx_o, &tx_raw_tlv, &rx_raw_tlv);
    memset(fohh_desc_info, 0, sizeof(fohh_desc_info));
    snprintf(fohh_desc_info, sizeof(fohh_desc_info),
            "Log(zpn_sitec_auth_log) connection: %s, uptime_s=%" PRId64 ", disconnect_duration_s %"PRId64", tx_b %"PRIu64", rx_b %"PRIu64"\n",
            fohh_description(fohh_conn), fohh_get_uptime_s(fohh_conn), fohh_conn_get_current_disconnect_duration_s(fohh_conn), tx_b, rx_b);
    s += sxprintf(s, e, "%s", fohh_desc_info);
    dbg_logs->num_bytes += strnlen(fohh_desc_info, sizeof(fohh_desc_info));

    fohh_conn = gs->sitec_state->stats_fohh_conn;
    get_fohh_connection_stats(fohh_conn, &tx_b, &rx_b, &tx_o, &rx_o, &tx_raw_tlv, &rx_raw_tlv);
    memset(fohh_desc_info, 0, sizeof(fohh_desc_info));
    snprintf(fohh_desc_info, sizeof(fohh_desc_info),
            "Log(stats_log) connection: %s, uptime_s=%" PRId64 ", disconnect_duration_s %"PRId64", tx_b %"PRIu64", rx_b %"PRIu64"\n",
            fohh_description(fohh_conn), fohh_get_uptime_s(fohh_conn), fohh_conn_get_current_disconnect_duration_s(fohh_conn), tx_b, rx_b);
    s += sxprintf(s, e, "%s", fohh_desc_info);
    dbg_logs->num_bytes += strnlen(fohh_desc_info, sizeof(fohh_desc_info));

    memset(sc_data, 0, sizeof(sc_data));
    snprintf(sc_data, sizeof(sc_data), "Feature status: Swap status: %"PRId64"\n", gs->swap_config);
    dbg_logs->num_bytes += strnlen(sc_data, sizeof(sc_data));


    if (dbg_logs->num_bytes >= MAX_ADDITIONAL_DEBUG_LOGS_BYTES) {
        dbg_logs->num_bytes = MAX_ADDITIONAL_DEBUG_LOGS_BYTES - 1;
    }

    snprintf(dbg_logs->debug_logs, sizeof(dbg_logs->debug_logs), "%s%s%s", sitec_status_data, connection_info, sc_data);

    return ZPATH_RESULT_NO_ERROR;
}

void zpn_sitec_site_init_for_cfg_conns()
{
    char hostname[256];
    char *cloud_name = zpn_sitec_get_cloud_name();

    snprintf(hostname, sizeof(hostname), "%s.%s", ZPN_SITEC_TO_PUBLIC_BROKER_DNS_NAME, cloud_name);

    fohh_connection_site_init(sitec_gs.sitec_state->cfg_fohh_conn, sitec_gs.offline_domain, NULL, hostname, 0, 1, 0, sitec_gs.sitec_ssl_ctx, fohh_g.ssl_ctx);
    fohh_connection_site_init(sitec_gs.sitec_state->rcfg_fohh_conn, sitec_gs.offline_domain, NULL, hostname, 0, 1, 0, sitec_gs.sitec_ssl_ctx, fohh_g.ssl_ctx);
    fohh_connection_site_init(sitec_gs.sitec_state->ovd_fohh_conn, sitec_gs.offline_domain, NULL, hostname, 0, 1, 0, sitec_gs.sitec_ssl_ctx, fohh_g.ssl_ctx);
    fohh_connection_site_init(sitec_gs.sitec_state->userdb_wally_conn, sitec_gs.offline_domain, NULL, hostname, 0, 1, 0, sitec_gs.sitec_ssl_ctx, fohh_g.ssl_ctx);
}

void zpn_sitec_check_firedrill_mode_on_boot()
{
    int64_t current_time_s = 0;
    int64_t fd_leftover_s = 0;
    struct zpn_sitec_global_state *gs = zpn_get_sitec_global_state();

   /* Read the site_config.cfg file for firedrill status */
    current_time_s = epoch_s();
    fd_leftover_s = (current_time_s - gs->firedrill_cfg.start_time);
    if(gs->firedrill_cfg.status == ZPN_SITEC_FIREDRILL_ENABLED &&
        /* if the difference of current and start is less than the firedrill interval then we are in the firedrill window */
        fd_leftover_s < gs->firedrill_cfg.interval_s)  {

        gs->firedrill_cfg.leftover_interval_s = gs->firedrill_cfg.interval_s - fd_leftover_s;
        /*
           we are in firedrill mode before the restart, start timer for the remaining time
           we have to wait for pbroker_firedrill_timer to be initialised and then start the timer
           check the left over field here so that we know we have to start the firedrill
        */

        SITEC_LOG(AL_ERROR, "#### sitec booted in firedrill mode, start timer for the remaining duration and connect to sitec");
        SITEC_LOG(AL_ERROR, "#### firedrill interval: %"PRId64" start time: %"PRId64" leftover_interval_s: %"PRId64"",
                                        gs->firedrill_cfg.interval_s, gs->firedrill_cfg.start_time, gs->firedrill_cfg.leftover_interval_s);
    } else {

        gs->firedrill_cfg.leftover_interval_s = 0;
        gs->firedrill_cfg.status = ZPN_SITEC_FIREDRILL_DISABLED;
        gs->firedrill_cfg.start_time = 0;
        gs->firedrill_cfg.interval_s = 0;

        ZPN_LOG(AL_ERROR, "$$$$$$$ sitec boot check, not in the firedrill window");
    }
}

int sub_main(int argc, char *argv[])
{
    int always_re_enroll = 0;
    int disable_auto_upgrade = 0;
    char cl_arg[SITEC_COMMAND_LINE_ARG_LEN] = {'\0'};
    int result;
    int64_t swap_config;
    const char *sitec_name = NULL;
    char env_val_develop[64] = "";
    char env_val_disable_geoip[64] = "";
    int memory_arena_count;
    char *mmdb_fetch_proxyname;
    char *mmdb_fetch_hostname;
    int disable_geoip = zcrypt_metadata_disable_geoip_false;
    int is_dropdb = 0;
    int db_drop_res = 0;
    struct zpn_sitec_config_file site_config;
	char *env_oauth = NULL;

    config_check_lock = ZPATH_RWLOCK_INIT;

    config_fetch_ca_file[0] = '\0';

    zthread_init(APP_ROLE, ZPATH_VERSION, "unknown", NULL, NULL);
    zthread_do_stack_dump(&argc, argv);

    sitec_fohh_thread_count = zpn_sitec_get_max_fohh_threads();

    zpn_sitec_global_init();

    sitec_cfg.instance_type = ZPN_INSTANCE_TYPE_SITEC;
    g_broker_common_cfg = &sitec_cfg;

    sitec_gs.sarge_version = zpn_sitec_get_sarge_version();

    /* use sqlt cache for site controller by default */
    sitec_gs.use_sqlt = 1;

    /* Get the runtime OS information for the sitec */
    zhw_get_runtime_os(sitec_gs.sitec_runtime_os, sizeof(sitec_gs.sitec_runtime_os));

    memory_arena_count = -1;

    if (zpath_app_logging_parse_args(&argc, argv)) {
        SITEC_LOG(AL_ERROR, "zpath_app_logging_parse_args failed");
        sleep(1);
        exit(1);
    }

    for (int i = 1; i < argc; i++) {
        snprintf(cl_arg+strlen(cl_arg),SITEC_COMMAND_LINE_ARG_LEN - strlen(cl_arg)," %s",argv[i]);
        cl_arg[SITEC_COMMAND_LINE_ARG_LEN - 1] = '\0';
        /* Test for all one-word arguments. */
        if (strcmp(argv[i], "-daemon") == 0) {
            config_daemon = 1;
        } else if (strcmp(argv[i], "-version") == 0) {
            fprintf(stdout, "%s\n", ZPATH_VERSION);
            exit(1);
        } else if (strcmp(argv[i], "-role") == 0) {
            fprintf(stdout, "%s\n", APP_ROLE);
            exit(1);
        } else if (strcmp(argv[i], "-platform") == 0) {
            fprintf(stdout, "%s%d\n", ZPATH_PLATFORM_NAME, ZPATH_PLATFORM_VERSION);
            exit(1);
        } else if (strcmp(argv[i], "-arch") == 0) {
            fprintf(stdout, "%s\n", ZPATH_PLATFORM_ARCH);
            exit(1);
        } else if (strcmp(argv[i], "-quiet") == 0) {
            fohh_set_quiet(1);
        } else if (strcmp(argv[i], "-debuglog") == 0) {
            debuglog = 1;
        } else if (strcmp(argv[i], "-container") == 0) {
            sitec_gs.is_container_env = 1;
        } else if (strcmp(argv[i], "-logfiles") == 0) {
            logfiles = 1;
        } else if (strcmp(argv[i], "-disable_heartbeat_monitor") == 0) {
            zthread_disable_heartbeat_monitor();
        } else if (strcmp(argv[i], "-no_flow_control") == 0) {
            flow_control_enabled = 0;
        } else if (strcmp(argv[i], "-always_re_enroll") == 0) {
            always_re_enroll = 1;
        } else if (strcmp(argv[i], "-nosqlite") == 0) {
            sitec_gs.use_sqlt = 0;
        } else if (strcmp(argv[i], "-no_auto_upgrade") == 0) {
            disable_auto_upgrade = 1;
        }  else if (strcmp(argv[i], "-disable_geoip") == 0) {
            disable_geoip = zcrypt_metadata_disable_geoip_true;
        } else if (strcmp(argv[i], "-dropdb") == 0) {
            is_dropdb = 1;
        } else {
            /* Test for all two-word arguments. */
            if ((i + 1) >= argc) {
                /* There is not a pair of words... */
                usage(argv[0], "Improper argument- may be missing second field: %s\n", argv[i]);
                break;
            }
            snprintf(cl_arg+strlen(cl_arg),SITEC_COMMAND_LINE_ARG_LEN - strlen(cl_arg)," %s",argv[i+1]);
            cl_arg[SITEC_COMMAND_LINE_ARG_LEN - 1] = '\0';
            if (strcmp(argv[i], "-stackpath") == 0) {
                i++;
                stackpath = argv[i];
            } else if (strcmp(argv[i], "-local_file") == 0) {
                i++;
                local_file = argv[i];
            } else if (strcmp(argv[i], "-logfile") == 0) {
                i++;
                logfile = argv[i];
            } else if (strcmp(argv[i], "-dbhost") == 0) {
                i++;
                dbhost = argv[i];
            } else if (strcmp(argv[i], "-threads") == 0) {
                i++;
				int cli_fohh_thread = atoi(argv[i]);
                if ((cli_fohh_thread <= 0) || (cli_fohh_thread > sitec_fohh_thread_count)
                    || (cli_fohh_thread > FOHH_MAX_THREADS)) {
                    fprintf(stdout, "Thread count %d not supported in this hardware architecture max %d\n",
                            cli_fohh_thread, sitec_fohh_thread_count);
                    exit(1);
                }
                sitec_fohh_thread_count = cli_fohh_thread;
            } else if (strcmp(argv[i], "-maxlogmb") == 0) {
                i++;
                zpath_app_set_specific_max_logging_mb(atoi(argv[i]));
            } else if (strcmp(argv[i], "-memory_arena_count") == 0) {
                i++;
                memory_arena_count = atoi(argv[i]);
            } else if (strcmp(argv[i], "-fproxy") == 0) {
                i++;
                fohh_proxy_hostname = (argv[i]);
            } else if (strcmp(argv[i], "-fproxy_port") == 0) {
                i++;
                fohh_proxy_port = atoi(argv[i]);
            } else if (strcmp(argv[i], "-repo_ca_file") == 0) {
                i++;
                snprintf(config_fetch_ca_file, MAX_CA_FILE_LEN , "%s", argv[i]);
            } else {
                usage(argv[0], "Unrecognized argument: %s\n", argv[i]);
                // continue with the remaining arguments
                continue;
            }
        }
    }

    sitec_gs.disable_auto_upgrade = disable_auto_upgrade;
    sitec_gs.fohh_threads = sitec_fohh_thread_count;
    sitec_gs.disable_geo_ip = disable_geoip;

#ifdef __linux__
    if (sitec_gs.is_container_env) {
        sitec_gs.cgroup_version = zpath_system_check_if_cgroup_exists();
        if (sitec_gs.cgroup_version) {
            ZPN_LOG(AL_NOTICE, "Cgroups v%d detected!", sitec_gs.cgroup_version);
        }
    } else {
        sitec_gs.cgroup_version = ZPN_SYSTEM_USE_DEFAULT;
    }
#else
    sitec_gs.cgroup_version = ZPN_SYSTEM_USE_DEFAULT;
#endif

    if (-1 == memory_arena_count) {
        memory_arena_count = sitec_gs.fohh_threads + sitec_non_fohh_thread_count;
    }

#ifdef __linux__
    /*
     * Have only one main thread arena(sbrk done here) + ~30 thread arena(mmap done here). We don't want to aggressively
     * reduce the thread arena as that will lead to thread contention when accessing memory. Withtout the limit on
     * thread arena, we can see a huge growth in the number of thread arenas(say in some peak memory intensive events),
     * but the problem is that the entire arena have to be freed to release the memory back to the kernel.
     *
     * Allocator uses the main arena after it can't spawn any new thread arena. So there is no fear of memory
     * constraints.
     */
    // turn below on util we hit performance issue
    // mallopt(M_ARENA_MAX, memory_arena_count);
#endif

    if (sitec_gs.is_container_env) {
        zpath_app_enable_console_log();
    }

    if (config_daemon) {
        int fd;
        fprintf(stderr, "Daemonizing\n");
        /* Take off... */
        switch (fork()) {
		case 0:
			break;
		case -1:
            SITEC_LOG(AL_ERROR, "fork failed: %s", strerror(errno));
            return ZPN_RESULT_ERR;
		default:
			/* exit interactive session */
			exit(0);
        }
        if(setsid() == -1) {
            SITEC_LOG(AL_ERROR, "setsid failed: %s", strerror(errno));
            return ZPN_RESULT_ERR;
        }
        if ((fd = open("/dev/null", O_RDWR, 0)) != -1) {
            (void)dup2(fd, STDIN_FILENO);
            (void)dup2(fd, STDOUT_FILENO);
            (void)dup2(fd, STDERR_FILENO);
            if (fd > 2)
                (void)close(fd);
        }
    }

    if (debuglog) {
        zpath_debug |=
                    (ZPATH_DEBUG_CLOUD_CONFIG_BIT) |
                    0;
    }

    zpath_app_enable_console_log();

    struct zpath_simple_app_init_params app_params;
    zpath_simple_app_init_params_default(&app_params);
    app_params.instance_name = app_params.role_name = "zpa-pcc-child";
    app_params.fohh_thread_count = sitec_fohh_thread_count;
    app_params.fohh_watchdog_s = ZPN_MAX_HEARTBEAT_TIMEOUT;
    app_params.log_filename = logfile;
    app_params.debug_port = debug_port;
    app_params.debuglog = debuglog;
    app_params.personality = ZPATH_APP_PERSONALITY_MINIMUM_MEMORY_FOOTPRINT;
    if (!logfiles) {
        app_params.personality |= ZPATH_APP_PERSONALITY_NO_FILE_LOGGING;
    }
    app_params.load_zpa_cloud_config = 0;

    result = zpath_simple_app_init(&app_params);
    if (result) {
        SITEC_LOG(AL_ERROR, "Error: Could not intialize\n");
        sleep(1);
        exit(1);
    }

    if (is_dropdb) {
        //delete the database files.
        SITEC_LOG(AL_WARNING, "dropdb set. deleting files.");
        db_drop_res = zpn_sitec_drop_files("i.0");
        SITEC_LOG(AL_WARNING, "dropdb result:%s", zpath_result_string(db_drop_res));
    }

    char written_version[1000] = "";
    result = zpn_enroll_update_version(FILENAME_RUNNING_VERSION,
                                       ZPATH_VERSION,
                                       written_version,
                                       sizeof(written_version));

    if (result) {
        SITEC_LOG(AL_ERROR, "writing running version failed");
    } else {
        SITEC_LOG(AL_NOTICE, "writing running version %s to running_version file", written_version);
    }

    zpath_init_cloud_config();

    zpn_app_request_atleast_one = 0;

    if ( config_fetch_ca_file[0] != '\0' ) {
        fohh_add_ext_trusted_certs_from_file(config_fetch_ca_file);
    }

    /*
     * Read proxy bypass config
     */
    if (fohh_proxy_hostname) {
        result = fohh_read_bypass_from_file("proxy-bypass");
        if (result == FOHH_RESULT_NOT_FOUND) {
            /* This is okay- the file not being there doesn't hurt
             * anyone */
        } else if (result == FOHH_RESULT_NO_ERROR) {
            /* This is okay- we read proxy config */
        } else {
            /* This is not okay- there was some error. */
            SITEC_LOG(AL_ERROR, "Could not properly parse proxy-bypass file");
            sleep(1);
            exit(1);
        }
    }

#ifdef __linux__
    SITEC_LOG(AL_NOTICE, "memory_arena_count=%d", memory_arena_count);
#endif

    zpn_event_collection = zpath_event_collection;
    zpn_sitec_auth_collection = argo_log_create("zpn_sitec_auth_log", NULL, NULL); /* 64K entries */

    if (do_log_file(zpn_sitec_auth_collection,
                    "zpn_sitec_auth_log_file",
                    logfiles ? "." : NULL,
                    "sitec_auth.log")) {
        SITEC_LOG(AL_ERROR, "Could not create pb_auth_log reader");
    }

    if(argc > 1){
        SITEC_LOG(AL_NOTICE,"Starting Site Controller with \"%s\" argument", cl_arg);
    }else{
        SITEC_LOG(AL_NOTICE,"Starting Site Controller without extra arguments");
    }

    SITEC_LOG(AL_NOTICE, "%s version: %s", SITEC_LOG_NAME, ZPATH_VERSION);

    /* Check if we are enabled for dev certs */
    if (zcrypt_metadata_get_zpa_develop_env(env_val_develop, sizeof(env_val_develop)) == ZCRYPT_RESULT_NO_ERROR) {
        SITEC_LOG(AL_DEBUG, "Environment %s is set to %s", ZCRYPT_ZPA_DEVELOP_ENV, env_val_develop);
        sitec_gs.develop_certs_mode = zcrypt_metatdata_get_develop_mode_from_env(env_val_develop);
    } else {
        sitec_gs.develop_certs_mode = zcrypt_metadata_develop_mode_unknown;
    }

    /* Check if geoip is disabled */
    if (zcrypt_metadata_get_zpa_disable_geoip_env(env_val_disable_geoip, sizeof(env_val_disable_geoip)) == ZCRYPT_RESULT_NO_ERROR) {
        ZPN_LOG(AL_DEBUG, "Environment %s is set to %s", ZCRYPT_ZPA_DISABLE_GEOIP, env_val_disable_geoip);
        sitec_gs.disable_geo_ip = zcrypt_metatdata_get_disable_geoip_env(env_val_disable_geoip);
    }

    if ((env_oauth = getenv(ENV_ZPA_OAUTH_ENROLLMENT))) {
        if (!strncmp(env_oauth, "TRUE", 4) && !is_oauth_enrollment_disabled()) {
            sitec_gs.oauth_enroll = 1;
        }
    }

    sitec_gs.zcdns = zcdns_libevent_create(fohh_get_thread_event_base(0),
                                      1,
                                      NULL,
                                      "/etc/resolv.conf",
                                      "/etc/hosts",
                                      log_f,
                                      NULL);

    result = zpn_sitec_enroll(always_re_enroll);
    if (result) {
        SITEC_LOG(AL_ERROR, "Error: %s, Could not enroll %s", zpath_result_string(result), sitec_gs.role_name);
        sleep(1);
        exit(1);
    }

    zthread_init(APP_ROLE, ZPATH_VERSION, sitec_gs.cn, stackpath, get_sitec_additional_debug_logs);

    /* Need to read Organization Name out of certificate. */
    {
        struct zcrypt_cert *cert;
        char org_name[128] = {0};
        cert = zcrypt_cert_read(FILENAME_CERT);

        if (!cert) {
            SITEC_LOG(AL_ERROR, "Could not read cert file %s", FILENAME_CERT);
            sleep(1);
            exit(1);
        }

        if (!fohh_x509_get_on(zcrypt_cert_get_x509(cert), org_name, sizeof(org_name))) {
            zthread_set_org_name(org_name);
        } else {
            SITEC_LOG(AL_NOTICE, "Could not get ON from cert file %s", FILENAME_CERT);
            sleep(1);
        }

        zcrypt_cert_free(cert);
    }

    sitec_cfg.public_broker.broker_id = sitec_gs.sitec_id;

    result = zpn_pcap_lib_init(zpn_event_collection);
    if (result) {
        ZPN_LOG(AL_ERROR, "Could not init zpn_pcap_lib");
        sleep(1);
        exit(1);
    }

    result = zpn_pcap_init();
    if (result) {
        SITEC_LOG(AL_ERROR, "Could not init zpn_pcap");
        sleep(1);
        exit(1);
    }

    result = zpn_zdx_webprobe_lib_init(zpath_event_collection);
    if (result) {
        ZPN_LOG(AL_ERROR, "Unable to init the webprobe rate limit lib");
        sleep(1);
        exit(1);
    }

    result = zpn_zdx_webprobe_rate_limit_init();
    if (result) {
        ZPN_LOG(AL_ERROR, "Unable to init the webprobe rate limit");
        return result;
    }

    result = zpn_rpc_init();
    if (result) {
        SITEC_LOG(AL_ERROR, "Could not register RPCs");
        sleep(1);
        exit(1);
    }

    //fohh_debug = 0xffff;

    /* Initialize basics from zpath_local. This includes location of
     * certificates, etc, so we can operate much like a normal
     * broker. */
    char zpath_local[1000];
    size_t len;

    /***********************************************************************/
    /* PRODUCTION CODE HERE */
    len = snprintf(zpath_local, sizeof(zpath_local),
                   "{"
                   "    \"zpath_local\" : {"
                   "        \"id\" : 0,"
                   "        \"role\" : \"pbroker\","
                   "        \"sequence\" : 1,"
                   "        \"instance_name\" : \"%"PRId64"\","
                   "        \"cloud_name\" : \"%s\","
                   "        \"root_certificate_file\" : \"./cloud.pem\","
                   "        \"public_certificate_file\" : \"./cert.pem\","
                   "        \"private_key_file\" : \"./rsa_key.pem\","
                   "        \"event_log_file\" : \"./%"PRId64".%s.event.log\","
                   "        \"event_log_file_short\" : \"./event.log\","
                   "        \"stats_log_file\" : \"./%"PRId64".%s.stats.log\","
                   "        \"stats_log_file_short\" : \"./stats.log\""
                   "    }"
                   "}",
                   sitec_gs.sitec_id,
                   sitec_gs.cfg_key_cloud,
                   sitec_gs.sitec_id,
                   sitec_gs.cfg_key_cloud,
                   sitec_gs.sitec_id,
                   sitec_gs.cfg_key_cloud);
    if (len >= sizeof(zpath_local)) {
        SITEC_LOG(AL_ERROR, "String too large");
        return ZPATH_RESULT_ERR;
    }



    result = zpath_local_init_with_string(zpath_local);
    if (result) {
        SITEC_LOG(AL_ERROR, "Could not local init using <%s>", zpath_local);
        return ZPATH_RESULT_ERR;
    }

    zthread_record_cpu_time_since_last_heartbeat();

    zpn_sitec_alt_cloud_support_init();

    zpn_sitec_config_load(&site_config);
    /* a function is required for sitec like the below */
    //update_global_state_from_site_config(&site_config);

    /* check the firedrill mode */
    zpn_sitec_check_firedrill_mode_on_boot();

    result = zpn_sitec_conn_sni_init();
    if (result != ZPATH_RESULT_NO_ERROR) {
        SITEC_LOG(AL_ERROR, "Unable to init conn SNI");
        return ZPN_RESULT_ERR;
    }

    /*
     * simple_app_init have already init-ed role & build. We know the instance name only now and so let
     * us pass on that info to argo_log module.
     */
    if (argo_log_init(sitec_gs.cn, sitec_gs.sitec_id, NULL, NULL, 0)) {
        SITEC_LOG(AL_ERROR, "Could not init argo library");
        return ZPN_RESULT_ERR;
    }

    result = zpn_sitec_create_skip_table_for_sync_time_init();
    if (result != ZPATH_RESULT_NO_ERROR) {
        SITEC_LOG(AL_ERROR, "Unable to create skip table");
        return ZPN_RESULT_ERR;
    }

    zpn_sitec_log_init();

    result = zpn_sitec_sitec_conn_init();
    if (result != ZPN_RESULT_NO_ERROR) {
        return result;
    }

    result = zpn_sitec_cfg_conn_init();
    if (result != ZPATH_RESULT_NO_ERROR) {
        SITEC_LOG(AL_ERROR, "Unable to initiate cfg conn to broker");
        return ZPN_RESULT_ERR;
    }

    result = zpn_sitec_cfg_ovd_conn_init();
    if (result != ZPATH_RESULT_NO_ERROR) {
        SITEC_LOG(AL_ERROR, "Unable to initiate cfg ovd conn to broker");
        return ZPN_RESULT_ERR;
    }

    result = zpn_sitec_userdb_conn_init();
    if (result != ZPATH_RESULT_NO_ERROR) {
        SITEC_LOG(AL_ERROR, "Unable to initiate userdb conn to broker");
        return ZPN_RESULT_ERR;
    }

    result = zpn_sitec_ctrl_conn_init();
    if (result) {
        SITEC_LOG(AL_CRITICAL, "sitec control conn init to broker failed!");
        return ZPN_RESULT_ERR;
    }

    result = zpn_sitec_stats_conn_init();
    if (result) {
        SITEC_LOG(AL_CRITICAL, "sitec stats conn init to broker failed!");
        return ZPN_RESULT_ERR;
    }

    result = zpn_sitec_init();
    if (result) {
        SITEC_LOG(AL_CRITICAL, "sitec init failed!");
        return ZPN_RESULT_ERR;
    }

    zpath_init_common_config_overrides(sitec_gs.sitec_id, sitec_gs.customer_id, DEFAULT_CLIENT_CIPHERSUITE_INDEX);

    zpath_app_init_fohh_cipher_configuration(sitec_gs.sitec_id, sitec_gs.customer_id, DEFAULT_CLIENT_CIPHERSUITE_INDEX );

    fohh_reset_client_cipher_global_ssl_ctx();

    result = zpn_sitec_log_conn_init();
    if (result != ZPATH_RESULT_NO_ERROR) {
        SITEC_LOG(AL_ERROR, "Unable to initiate log conn to broker");
        return ZPN_RESULT_ERR;
    }

    result = zpn_sitec_astats_conn_init();
    if (result != ZPATH_RESULT_NO_ERROR) {
        SITEC_LOG(AL_ERROR, "Unable to initiate log conn to broker");
        return ZPN_RESULT_ERR;
    }

    result = zpn_sitec_pbstats_conn_init();
    if (result != ZPATH_RESULT_NO_ERROR) {
        SITEC_LOG(AL_ERROR, "Unable to initiate log conn to broker");
        return ZPN_RESULT_ERR;
    }

    zpn_sitec_alt_cloud_cfg_monitor_init(sitec_gs.sitec_id);

    result = zpn_sitec_decrypt_sitesp_private_keys();
    if (result) {
        SITEC_LOG(AL_CRITICAL, "zpn_sitesp_decrypt_private_keys failed!");
        return ZPN_RESULT_ERR;
    }

    sitec_name = sitec_get_name_by_id(sitec_gs.sitec_id);
    if(sitec_name) {
        snprintf(sitec_gs.cfg_name, sizeof(sitec_gs.cfg_name), "%s", sitec_name);
    }
    else {
        snprintf(sitec_gs.cfg_name, sizeof(sitec_gs.cfg_name), "%s", SITEC_LOG_NAME);
    }

    zpath_system_get_system_memory(NULL,
                                   NULL,
                                   NULL,
                                   &swap_config,
                                   NULL);

    zpn_sitec_set_swap_config(swap_config);

    result = zpn_sitec_stats_monitor_init();
    if (result) {
        ZPN_LOG(AL_ERROR, "pbroker_stats_monitor_init failed: %s",
                zpath_result_string(result));
        return result;
    }

    result = zpn_sitec_add_debug_allocator();
    if (result) {
        SITEC_LOG(AL_ERROR, "Could not add debug allocators");
        return ZPATH_RESULT_ERR;
    }

    result = zpn_sitec_add_debug_commands();
    if (result) {
        SITEC_LOG(AL_ERROR, "Could not add debug commands");
        return ZPATH_RESULT_ERR;
    }

    result = zpn_sitec_create_wally_fohh_servers();
    if (result) {
        SITEC_LOG(AL_ERROR, "wally fohh servers failed");
        return ZPATH_RESULT_ERR;
    }

    result = zpn_broker_client_init_debug();
    if (result) {
        SITEC_LOG(AL_ERROR, "client init debug failed");
        return ZPATH_RESULT_ERR;
    }

    result = zpn_sitec_init_site_info();
    if (result) {
        SITEC_LOG(AL_ERROR, "site info init failed");
        return ZPATH_RESULT_ERR;
    }

    result = zpn_sitec_listen_offline_domain(sitec_gs.sni_server);
    if (result) {
        SITEC_LOG(AL_ERROR, "Could not listen on offline domain");
        return ZPATH_RESULT_ERR;
    }

    result = zpn_sitec_stats_tx_init();
    if (result) {
        SITEC_LOG(AL_CRITICAL, "sitec stats conn init to broker failed!");
        return ZPN_RESULT_ERR;
    }

    result = zpn_sitesp_init();
    if (result) {
        SITEC_LOG(AL_ERROR, "Could not add sitesp endpoints");
        return ZPATH_RESULT_ERR;
    }

    sitec_gs.sitec_ssl_ctx = fohh_client_ssl_ctx_create(FILENAME_ROOT,
                                                        FILENAME_CERT,
                                                        FILENAME_KEY_PRIV,
                                                        sitec_gs.cfg_pkey);

    if (!sitec_gs.sitec_ssl_ctx) {
        SITEC_LOG(AL_ERROR, "Could not create sitec_ssl_ctx");
        return ZPATH_RESULT_ERR;
    }

    zpn_sitec_site_init_for_cfg_conns();

    result = zpn_sitec_create_status_conn_to_all_sitec();
    if (result) {
        SITEC_LOG(AL_ERROR, "Could not initiate sitec status conns");
        return ZPATH_RESULT_ERR;
    }

    // all SNIs are registered, so call update service to refresh
    zpn_sitec_update_service(sitec_gs.disable_flags, 0, 1);

    zpn_sitec_setup_fohh_conn_setup_timeout();

    zpn_sitec_setup_client_auth_complete_timeout();
    /* Cleanup upgrade config before writing*/
    zpath_upgrade_cfg_cleanup();
    if (zvm_vm_type_is_zscaler_rh_image()) {
        zpn_sitec_sarge_and_os_overrides_setup();
    }

    zpn_sitec_setup_oauth_enrollment();

    result = zpath_maxmind_status_init();
    if (result) {
        ZPN_LOG(AL_ERROR, "sitec Maxmind stats init error: %s",
                zpath_result_string(result));
        return result;
    }

    result = zpn_sitec_monitor_geoip_configuration();
    if (result) {
        ZPN_LOG(AL_ERROR, "unable to monitor sitec geoip enable/disable error: %s",
                zpath_result_string(result));
        return result;
    }

    /* initialize forward proxy module if we are on Zscaler RHEL image and we are
     * not on docker */
    if (zvm_vm_type_is_zscaler_rh_image() && !zvm_vm_type_is_docker()) {
        zpn_sitec_features_fproxy_cfg_monitor_init(zpn_get_sitec_global_state()->sitec_id);
    }

    //Create the directory in which the geoip files will go
    if(mkdir(MMDB_FILE_PATH, 0755) == -1) {
        if(errno != EEXIST){
            ZPN_LOG(AL_CRITICAL, "Failed to create the new directory for geoip files!: %s",strerror(errno));
            return ZPN_RESULT_ERR;
        }
    }

    struct zpath_cloud_config *cloud_config=zpath_get_cloud_config_from_name(sitec_gs.cfg_key_cloud);
    if(!cloud_config) {
        ZPN_LOG(AL_CRITICAL, "Unable to read zpa_cloud_config for cloud %s",sitec_gs.cfg_key_cloud);
        return ZPN_RESULT_ERR;
    }
    mmdb_fetch_hostname = cloud_config->sarge->dist_hostname;
    mmdb_fetch_proxyname = cloud_config->sarge->dist_proxyname;

    result = zpn_sitec_load_geoip_file(mmdb_fetch_proxyname, mmdb_fetch_hostname);
    if (result) {
        ZPN_LOG(AL_ERROR, "unable to laod geoip file error: %s mark as disabled",
                zpath_result_string(result));
        write_version(FILENAME_GEOIP_ENC, "disabled");
    }

    result = zpn_sitec_load_isp_file(mmdb_fetch_proxyname, mmdb_fetch_hostname);
    if (result) {
        ZPN_LOG(AL_ERROR, "unable to laod isp file error: %s mark d disabled",
                zpath_result_string(result));
        write_version(FILENAME_ISP_ENC, "disabled");
    }

    //update the auth log
    if (fohh_get_state(sitec_gs.sitec_state->ctrl_fohh_conn) == fohh_connection_connected) {
        zpn_send_zpn_tcp_info_report(sitec_gs.sitec_state->ctrl_fohh_conn,
                        fohh_connection_incarnation(sitec_gs.sitec_state->ctrl_fohh_conn),
                        sitec_gs.sitec_state->ctrl_fohh_conn,
                        zvm_vm_type_to_str_concise(zvm_type_get()), sitec_gs.sitec_runtime_os);
    }

    sitec_gs.zthread = zthread_register_self(sitec_gs.cn, 60);

    zpn_sitec_generic_listen();

    zpath_registration_completed();

    /* Start main loop */
    zpn_sitec_main_loop();

    sleep(1);

    return 0;
}

int main(int argc, char *argv[]) {
    int result;

    /* Turn off buffering of stdout. This needs to be called before any stdout operation */
    setvbuf(stdout, NULL, _IONBF, 0);

    result = sub_main(argc, argv);
    sleep(1);
    return result;
}
