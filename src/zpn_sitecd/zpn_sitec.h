/*
 * zpn_sitec.h. Copyright (C) 2024 Zscaler, Inc. All Rights Reserved.
 *
 */

#ifndef _ZPN_SITEC_H_
#define _ZPN_SITEC_H_

#include "zpn_enrollment_lib/zpn_enrollment.h" // TODO: Fix this dependency hell.

#include "zpn_sitecd/zpn_sitec_private.h"
#include "zpn_sitecd/zpn_sitesp.h"
#include "zpath_lib/zpath_assert.h"
#include "zpn/zpn_firedrill_site.h"

#define ZPN_SITEC_TO_PUBLIC_BROKER_DNS_NAME "sc2br"
#define ZPN_SITEC_TO_PUBLIC_BROKER_PORT 443
#define ZPN_SITEC_PORT 443

#define APP_ROLE "zpa-pcc-child"
#define SITEC_DEFAULT_THREAD_CNT 4
#define SITEC_PROVISION_KEY_PATH  "/opt/zscaler/var/pcc"

/* Only allow max 15 minutes time adjustment */
#define ZPN_SITEC_MAX_CLOUD_TIME_ADJUST_US                 (int64_t)(15*60*1000*1000L)

/* Max time critical log frequency */
#define ZPN_SITEC_MAX_TIME_CRITICAL_LOG_FREQ_S             (2*60)

/* Max allowed fohh queue depth for time adjustment */
#define ZPN_SITEC_MAX_TIME_ADJ_FOHH_Q_DEPTH                (200)

/* Log every 5 mins */
#define ZPN_SITEC_TIME_ADJUST_LOG_COUNT                    (5*60)

/* Default max logging size for pbroker is 64 MB */
#define DEFAULT_MAX_LOGGING_MB                                      64

#define ZPN_SITEC_ASSERT_HARD(condition, format...)   \
    ZPATH_ASSERT_HARD(condition, "sitec", 0, "sitec assertion", ##format)

#define SITEC_APP_CALC_THREADS 2

#define SITESP_SIGNING_PRIVATE_KEY_STR "SITESP_SIGNING_PRIVATE_KEY"
#define SITESP_PRIVATE_KEY_STR "SITESP_PRIVATE_KEY"
#define SITESP_SIGNING_PKEY_FILE "sitesp_signing_pkey.crypt"
#define SITESP_PKEY_FILE "sitesp_pkey.crypt"
struct zpn_sitec_sni {
    char *type;
    char *sni_str;
    int wild_card;
};

extern char sc_sni_customer_domain_ctl[SITEC_SNI_DOMAIN_LEN];
extern char sc_sni_customer_domain_cfg[SITEC_SNI_DOMAIN_LEN];
extern char sc_sni_customer_domain_rcfg[SITEC_SNI_DOMAIN_LEN];
extern char sc_sni_customer_domain_ovd[SITEC_SNI_DOMAIN_LEN];
extern char sc_sni_customer_domain_log[SITEC_SNI_DOMAIN_LEN];
extern char sc_sni_customer_domain_stats[SITEC_SNI_DOMAIN_LEN];
extern char sc_sni_customer_domain_pblog[SITEC_SNI_DOMAIN_LEN];
extern char sc_sni_customer_domain_alog[SITEC_SNI_DOMAIN_LEN];
extern char sc_sni_customer_domain_pbstats[SITEC_SNI_DOMAIN_LEN];
extern char sc_sni_customer_domain_astats[SITEC_SNI_DOMAIN_LEN];
extern char sc_sni_customer_domain_scmc[SITEC_SNI_DOMAIN_LEN];

extern struct argo_log_collection *zpn_sitec_auth_collection;
extern int sub_module_upgrade_failed;
extern char *geoip_db_file;
extern char *geoip_isp_db_file;

extern struct argo_log_collection *zpn_asst_event_collection;
extern struct argo_log_collection *zpn_pb_event_collection;
extern struct argo_log_collection *zpn_pb_client_auth_collection;
extern struct argo_log_collection *zpn_sc_asst_auth_collection;
extern struct argo_log_collection *zpn_sc_pb_auth_collection;

struct zpn_sitec_global_state *zpn_get_sitec_global_state();
int zpn_sitec_enroll(int always_re_enroll);
char *zpn_sitec_get_cloud_name();
int sitec_is_dev_environment(void);
int zpn_sitec_init();
int get_sitec_additional_debug_logs(struct additional_debug_logs *dbg_logs);
int zpn_sitec_conn_sni_init(void);
int zpn_sitec_add_debug_allocator();
int zpn_sitec_add_debug_commands();
int zpn_sitec_init_site_info();
char *zpn_sitec_get_offline_domain();
int zpn_sitec_get_new_user_support(int *allow);
void zpn_sitec_generic_listen();

void zpn_sitec_update_client_listeners(struct fohh_generic_server *sni_server,
                                       char *new_offline_domain);
void zpn_sitec_enable_client_listeners(struct fohh_generic_server *sni_server, int enable_c2site);
void zpn_sitec_update_pse_listeners(struct fohh_generic_server *sni_server,
                                    char *new_offline_domain);
void zpn_sitec_update_asst_listeners(struct fohh_generic_server *sni_server,
                                     char *new_offline_domain);
int zpn_sitec_asst_send_pbload_to_all_assistants(int64_t gid,
                                                 uint16_t cpu_util,
                                                 uint16_t mem_util,
                                                 uint32_t active_mtun,
                                                 uint32_t clients,
                                                 uint32_t bytes_xfer,
                                                 char **publish,
                                                 size_t publish_count);

int zpn_sitec_add_client_listeners(struct fohh_generic_server *sni_server, char *offline_domain, int enable_c2site);
int zpn_sitec_add_pse_listeners(struct fohh_generic_server *sni_server, char *offline_domain);
int zpn_sitec_add_asst_listeners(struct fohh_generic_server *sni_server, char *offline_domain);

int zpn_sitec_pse_log_conn_init();
int zpn_sitec_asst_log_conn_init();
int zpn_sitec_listen_offline_domain(struct fohh_generic_server *sni_server);
void zpn_sitec_enable_c2site_service(int enable);
void zpn_sitec_update_offline_domain(const char* offline_domain);
void zpn_sitec_setup_fohh_conn_setup_timeout();
void zpn_sitec_setup_client_auth_complete_timeout();
int zpn_sitec_create_wally_fohh_servers();
int zpn_sitec_monitor_geoip_configuration();


void zpn_sitec_siem_log_stats_fill(struct zpn_sitec_comprehensive_stats *out_data);

void zpn_sitec_private_broker_send_firedrill_exit_rpc();
void zpn_sitec_assistant_send_firedrill_exit_rpc();
void zpn_sitec_firedrill_fill_comprehensive_stats(struct zpn_sitec_comprehensive_stats *out_data);
int zpn_sitesp_update_sso_end_point(int64_t idp_gid, int register_endpoint);
int zpn_sitesp_update_sso_end_point(int64_t idp_gid, int register_endpoint);
int zpn_sitec_is_cloud_reachable();
int zpn_sitec_get_max_allowed_downtime();

int zpn_sitec_firedrill_start(int64_t firedrill_interval);

int zpn_sitec_firedrill_get_config(struct zpn_firedrill_site **firedrill_config);

int zpn_sitec_firedrill_session_request(struct zpn_sitec_global_state *gs);

void zpn_sitec_sarge_and_os_overrides_setup();
int zpn_sitesp_update_sso_end_point(int64_t idp_gid, int register_endpoint);
int zpn_sitec_is_cloud_reachable();
int zpn_sitec_get_max_allowed_downtime();
void zpn_sitec_get_database_sync_times(int64_t *master, int64_t *shard, int64_t *userdb);
int zpn_sitec_create_skip_table_for_sync_time_init();
void zpn_sitec_setup_oauth_enrollment();
#endif //_ZPN_SITEC_H_
