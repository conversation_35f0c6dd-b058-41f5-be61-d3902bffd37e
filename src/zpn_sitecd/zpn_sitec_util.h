/*
 * zpn_sitec_util.h. Copyright (C) 2024 Zscaler, Inc. All Rights Reserved.
 *
 */

#ifndef _ZPN_SITEC_UTIL_H_
#define _ZPN_SITEC_UTIL_H_

#define ZPN_SITEC_CONFIG_FILE                "site_config.cfg"
#define ZPN_SITEC_FIREDRILL_STATUS_STR       "FIREDRILL_STATUS"
#define ZPN_SITEC_FIREDRILL_INTERVAL_S_STR   "FIREDRILL_INTERVAL_S"
#define ZPN_SITEC_FIREDRILL_START_TIME_STR   "FIREDRILL_KICKOFF_TIME"

#define ZPN_SITE_CONFIG_MAX_LINE_SIZE  512
struct zpn_sitec_config_file {
    struct zpn_firedrill_config firedrill_cfg;
};

enum zpn_sitec_firedrill_status {
    ZPN_SITEC_FIREDRILL_DISABLED = 0,
    ZPN_SITEC_FIREDRILL_ENABLED,
    ZPN_SITEC_FIREDRILL_TRANSIT
};

char* sitec_state_get_uptime_str(char *buf, int buf_len);
int zpn_sitec_get_max_fohh_threads();
char *sitec_get_name_by_id(const int64_t sitec_id);
int zpn_sitec_state_get_configured_cpus();
int zpn_sitec_state_get_available_cpus();
int zpn_sitec_get_swap_config();
void zpn_sitec_set_swap_config(uint64_t bytes);
int64_t zpn_sitec_cloud_adjusted_epoch_us(void);
int64_t zpn_sitec_cloud_adjusted_epoch_s(void);
const char *zpn_sitec_get_sarge_version(void);
int64_t zpn_sitec_get_cloud_time_delta_us(void);
void zpn_sitec_set_hw_id_changed_and_log_time_us(int64_t id_changed_time_us, int num_of_hw_id_changed);
int zpn_sitec_get_num_hw_id_changed();
int64_t* zpn_sitec_get_hw_id_changed_time_us();
void zpn_sitec_set_firedrill_status(int fd_status);
void zpn_sitec_set_firedrill_starttime(int64_t fd_starttime);
void zpn_sitec_set_firedrill_interval(int64_t fd_interval);
int zpn_sitec_get_firedrill_state();
int64_t zpn_sitec_get_firedrill_starttime();
int64_t zpn_sitec_get_firedrill_interval();
int zpn_sitec_handle_config_change(struct zpn_firedrill_config *firedrill_cfg);
int zpn_sitec_init_cert_validity_counters(const char *cert_file_name);
void get_fohh_connection_stats(struct fohh_connection *fohh_conn, uint64_t *tx_b, uint64_t *rx_b,
                                uint64_t *tx_o, uint64_t *rx_o, uint64_t *tx_raw_tlv,
                                uint64_t *rx_raw_tlv);
int zpn_sitec_write_to_file_and_update();
int zpn_sitec_ispip_replace_and_trigger_reload(char *filename,
                                               struct zpn_file_fetch_key *key,
                                               struct zcrypt_key local_key);
int zpn_sitec_geoip_replace_and_trigger_reload(char *filename,
                                               struct zpn_file_fetch_key *key,
                                               struct zcrypt_key local_key);
void zpn_sitec_firedrill_stop(evutil_socket_t sock, int16_t flags, void *cookie);

void zpn_sitec_broker_mc_timer_cb(evutil_socket_t sock, int16_t flags, void *cookie);
int zpn_site_firedrill_timer_activate(int64_t firedrill_interval);
int zpn_sitec_drop_files(char *starts_with);
int zpn_sitec_config_load(struct zpn_sitec_config_file *sitec_cfg);
void zpn_sitec_update_firedrill_config( struct zpn_firedrill_config *firedrill_cfg);
#endif //_ZPN_SITEC_UTIL_H_
