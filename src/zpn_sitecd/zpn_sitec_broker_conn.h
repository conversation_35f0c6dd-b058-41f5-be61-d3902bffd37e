/*
 * zpn_sitec_broker_conn.h. Copyright (C) 2024 Zscaler, Inc. All Rights Reserved.
 *
 */

#ifndef _ZPN_SITEC_BROKER_CONN_H_
#define _ZPN_SITEC_BROKER_CONN_H_

#include "zpn/zpn_rpc.h"

#define SITEC_STATS_TX_MAX_OBJECTS 4

/* 1 sec */
#define  SITEC_STATS_TX_COMPREHENSIVE_MIN_TIMEPERIOD_USEC       ((int64_t)(1ll * 1000ll * 1000ll))
/* 5 mins */
#define  SITEC_STATS_TX_COMPREHENSIVE_TIMEPERIOD_USEC           ((int64_t)(5ll * 60ll * 1000ll * 1000ll))
/* 1 mins */
#define  SITEC_STATS_TX_SYSTEM_MEMORY_TIMEPERIOD_USEC           ((int64_t)(1ll * 60ll * 1000ll * 1000ll))
/* 1 mins */
#define  SITEC_STATS_SIEM_LOG_STATS_TX_TIMEPERIOD_USEC           ((int64_t)(1ll * 60ll * 1000ll * 1000ll))
/* 60 mins */
#define  SITEC_STATS_TX_FPROXY_USEC                             ((int64_t)(1ll * 60ll * 60ll * 1000ll * 1000ll))
/* 60 mins */
#define SITEC_STATS_TX_UPGRADE_USEC                             ((int64_t)(1ll * 60ll * 60ll * 1000ll * 1000ll))

struct zpn_sitec_stats_tx_cfg {
    char*                                   l_name;
    char*                                   l_otyp;
    int                                     enabled;
    int64_t                                 interval_us;
    struct argo_structure_description*      argo_description;
    int                                     size_of_object;
    argo_log_pre_log_callback_f*            pre_log_cb;
    void*                                   pre_log_cb_cookie;
    int                                     log_immediate;
};

int zpn_sitec_cfg_conn_init();
int zpn_sitec_cfg_ovd_conn_init();
int zpn_sitec_userdb_conn_init();
int zpn_sitec_rcfg_conn_init();
int zpn_sitec_ctrl_conn_init();
int zpn_sitec_log_conn_init();
int zpn_sitec_astats_conn_init();
int zpn_sitec_pbstats_conn_init();
int zpn_sitec_log_tx_conn_cb(struct fohh_connection *f_conn,
                            enum fohh_connection_state f_conn_state,
                            void *cookie);
int zpn_sitec_stats_conn_init();
int zpn_sitec_stats_tx_init();
typedef void (zpn_sitec_stats_tx_done_cb)(void* void_cookie,
                                          char* str_cookie,
                                          int   tx_ret_code);

void zpn_sitec_broker_conn_comprehensive_stats_fill(struct zpn_sitec_comprehensive_stats *out_data,
                                                    struct zpn_sitec_comprehensive_stats *last_comprehensive_stats_data);

void zpn_sitec_pbroker_conn_comprehensive_stats_fill(struct zpn_sitec_comprehensive_stats *out_data,
                                                     struct zpn_sitec_comprehensive_stats *last_comprehensive_stats_data);

void zpn_sitec_connector_conn_comprehensive_stats_fill(struct zpn_sitec_comprehensive_stats *out_data,
                                                       struct zpn_sitec_comprehensive_stats *last_comprehensive_stats_data);

void zpn_sitec_delete_broker_connections(const char* reason);

void zpn_sitec_switch_to_broker_connections();
void zpn_sitec_mission_critical_conn_create(struct fohh_thread *thread, void *cookie, int64_t int_cookie);
#endif //_ZPN_SITEC_BROKER_CONN_H_
