/*
 * zpn_assistant_private.h. Copyright (C) 2014 Zscaler Inc. All Rights Reserved.
 */

#ifndef _ZPN_ASSISTANT_PRIVATE_H_
#define _ZPN_ASSISTANT_PRIVATE_H_

#include "zhash/zhash_table.h"
#include "zcdns/zcdns.h"
#include "zhealth/zhealth_combo.h"
#include "zpn/zpn_lib.h"
#include "zpn/zpn_mconn_udp_bufferevent.h"
#include "zpn/zpn_mconn_fohh_tlv.h"
#include "zpn/zpn_mconn_zrdt_tlv.h"
#include "zpn/zpn_connector.h"
#include "zpn/zpn_rpc.h"
#include "zpn/zpn_system.h"
#include "zpn/zpn_mconn_zrdt_tlv.h"
#include "zpn_assistantd/assistant_cfg_map_assistant_to_assistant_group.h"
#include "zpn_assistantd/assistant_site.h"
#include "zpn/zpn_version_control/zpn_version_control_utils.h"
#include "zpath_lib/zpath_upgrade_utils.h"
#include "zpn/zpn_firedrill_site.h"


#define ZPN_ASSISTANT_BUCKETS 128
#define MTUNNEL_HASH_TO_BUCKET(hash) ((hash >> 32) & (ZPN_ASSISTANT_BUCKETS - 1))
#define DATA_BRK_HASH_TO_BUCKET(hash) ((hash >> 32) & (ZPN_ASSISTANT_BUCKETS - 1))

#define DUMP_CONFIG_WALLY_STATE_FILE        "wally_config_dump"
#define DUMP_CONFIG_WALLY_OVD_STATE_FILE    "wally_config_ovd_dump"
#define DOWNLOAD_IN_PROGRESS            1
#define DOWNLOAD_NOT_IN_PROGRESS        0

/* Maximum time to wait (12 hours) to restart the connector when download is in progress and control/config connection is down */
#define MAX_DOWNLOAD_IN_PROGRESS_TIME         (12*60*60)

TAILQ_HEAD(zpn_assistant_mtunnel_head_tailq, zpn_assistant_mtunnel);
TAILQ_HEAD(zpn_asst_usr_broker_head_tailq, zpn_asst_usr_broker);

LIST_HEAD(zpn_assistant_pool_server_link_head, zpn_assistant_pool_server_link);

LIST_HEAD(assistant_app_head, assistant_app);
LIST_HEAD(assistant_service_head, assistant_service);
ZTAILQ_HEAD(assistant_target_head, assistant_target);

struct __attribute__((__packed__)) zpn_assistant_broker_data_id {
    int64_t broker_id;
    int64_t end_broker_id;  /* for double hop proxy conn, broker_id: g_bfw and end_broker_id: broker_id  */
    enum zpn_tlv_type tlv_type;
};

struct zpn_assistant_broker_data {
    /* Standard TLV state must come first in this structure. */
    struct zpn_fohh_tlv broker_tlv_state;
    struct zpn_zrdt_tlv broker_zrdt_tlv_state;

    enum zpn_tlv_type tlv_type;
    int64_t label;

    pthread_mutex_t lock;
    int64_t broker_id;
    enum fohh_connection_state fohh_conn_state;
    enum zdtls_session_status zdtls_sess_status;
    struct argo_hash_table *mtunnels_by_tag;
    struct zpn_assistant_mtunnel_head_tailq mtunnel_list;
    struct event*                           periodic_logging_timer;
    struct {
        uint64_t    num_mtunnel;
    } stats;

    char *broker_name;
    char *broker_sni;

    uint32_t conn_type;

    uint32_t is_pbroker:1;
    uint32_t fohh_verification_failed:1;
    uint32_t deletion_in_progress:1;
    uint32_t idle_force_disconnect:1;

    /*
     * NOTE: hash_entry_key is passed around as cookie; hence it is slow freed. So this object will live for 30 seconds
     * more than the broker object.
     */
    struct zpn_assistant_broker_data_id*   hash_entry_key;

    int resolver_called_back;
};

struct zpn_assistant_bucket {
    pthread_mutex_t lock;
    struct argo_hash_table *mtunnel_by_id;
    struct zpn_assistant_mtunnel_head_tailq bucket_mtunnel_list;
    struct zpn_assistant_mtunnel_head_tailq bucket_reaped_list;
};

struct zpn_asst_usr_broker {
    char usr_brk_name[256];
    int64_t insert_us;
};

struct zpn_asst_usr_broker_bucket {
    pthread_mutex_t lock;
    struct argo_hash_table *usr_broker;
    struct zpn_asst_usr_broker_head_tailq bucket_usr_broker_list;
};

/*
 * FIXME: The stats are not thread safe. So there might be little discrepancy of data. We will have to move stats
 * per fohh thread to be thread safe and lockless.
 *
 * local_time_lags_cloud_time_delta_us => +5us if cloud time = 955us; local time is 950us;
 *                                     => -5us if cloud time = 945us; local time is 950us;
 */
struct zpn_assistant_state {                                                    /* _ARGO: object_definition */
    /* Config lock is used for configuration/health locking- which
     * means it is sort of needed for connection setup */
    pthread_mutex_t config_lock;
    /* dr_handler lock is used for DR configuration dump.
     * Currently used by DR_monitor(timer event) which is part of assistant monitor thread */
    pthread_mutex_t dr_handler_lock;

    int     drmode_enabled;
    char    dr_running_mode[DR_RUNNING_MODE_STATUS_LEN];

    /* The assistant GID. */
    int64_t gid;                                                                /* _ARGO: integer */

    int64_t grp_gids[ASSISTANT_CFG_MAP_ASSISTANT_TO_ASSISTANT_GROUP_MAX_ENTRIES];
    int grp_gids_len;

    int64_t customer_gid;                                                       /* _ARGO: integer */

    int64_t scope_gid;                                                          /* _ARGO: integer */
    int64_t site_gid;                                                           /* _ARGO: integer */

    /* Config incarnation is incremented each time we go through the
     * configuration. It is used to watch for configuration elements
     * that have disappeared from configuraiton. */
    int64_t incarnation;                                                        /* _ARGO: integer */

    /* SSL context for communicating (as a client) with public or
     * private cloud, respectively. (Mostly just a difference in what
     * we trust about the peer- for public cloud we trust the cloud
     * root cert. For private cloud we trust our tenant root cert. */
    SSL_CTX *assistant_to_public_cloud_ctx;
    SSL_CTX *assistant_to_private_cloud_ctx;

    SSL_CTX *assistant_to_public_cloud_ctx_dtls;
    SSL_CTX *assistant_to_private_cloud_ctx_dtls;

    /* zthread_info of main thread to tickle wherever needed */
    struct zthread_info *z_info;

    /* FOHH connection for control channel */
    struct fohh_connection *broker_control;
    int64_t                 broker_control_label;

    /* FOHH connection for mission control channel */
    struct fohh_connection *broker_mc;

    /* FOHH connection for stats channel */
    struct fohh_connection *broker_stats;

    /* Buckets and buckets of mtunnels. */
    struct zpn_assistant_bucket buckets[ZPN_ASSISTANT_BUCKETS];

    /* Control broker gid */
    int64_t control_brk_gid;

    /* control events log upload to control broker */
    int log_upload_enabled;                                                     /* _ARGO: integer */
    /* control stats log upload to control broker */
    int stats_upload_enabled;                                                   /* _ARGO: integer */
    /* debugging only. control stats log write to local disk */
    int stats_log_to_disk_enabled;                                              /* _ARGO: integer */
    int is_init_complete;                                                       /* _ARGO: integer */
    struct argo_log_reader *stats_upload_reader;
    struct argo_log_reader *stats_file_reader;

    int64_t num_mtunnel_end;                                                    /* _ARGO: integer */
    int64_t num_tag_pause;                                                      /* _ARGO: integer */
    int64_t num_tag_resume;                                                     /* _ARGO: integer */
    int64_t num_window_update;                                                  /* _ARGO: integer */
    int64_t num_app_route_disc;                                                 /* _ARGO: integer */
    int64_t num_dns_asst_check_rx_from_dsp;                                     /* _ARGO: integer */
    int64_t num_dns_asst_check_rx_from_pbrk;                                    /* _ARGO: integer */

    int64_t num_health_report;                                                  /* _ARGO: integer */
    int64_t num_health_report_send_fails;                                       /* _ARGO: integer */
    int64_t num_health_report_send_discards;                                    /* _ARGO: integer */
    int64_t num_brk_req_from_dsp;                                               /* _ARGO: integer */
    int64_t num_brk_req_from_ubrk;                                              /* _ARGO: integer */
    int64_t num_brk_req_from_pbrk;                                              /* _ARGO: integer */
    int64_t num_brk_req_ack_to_dsp;                                             /* _ARGO: integer */
    int64_t num_brk_req_ack_to_dsp_fails;                                       /* _ARGO: integer */
    int64_t num_brk_req_ack_to_pbrk;                                            /* _ARGO: integer */
    int64_t num_brk_req_ack_to_pbrk_fails;                                      /* _ARGO: integer */
    int64_t num_brk_req_ack_to_ubrk;                                            /* _ARGO: integer */
    int64_t num_brk_req_ack_to_ubrk_fails;                                      /* _ARGO: integer */
    int64_t num_bind;                                                           /* _ARGO: integer */
    int64_t num_bind_fails;                                                     /* _ARGO: integer */
    int64_t num_bind_ack;                                                       /* _ARGO: integer */
    int64_t num_app_route_reg;                                                  /* _ARGO: integer */
    int64_t num_app_route_reg_send_fails;                                       /* _ARGO: integer */
    int64_t num_dns_asst_check_tx_to_dsp;                                       /* _ARGO: integer */
    int64_t num_dns_asst_check_tx_to_dsp_fails;                                  /* _ARGO: integer */
    int64_t num_dns_asst_check_tx_fail_no_result;                               /* _ARGO: integer */
    int64_t num_dns_asst_check_tx_to_pbrk;                                      /* _ARGO: integer */
    int64_t num_dns_asst_check_tx_to_pbrk_fails;                                /* _ARGO: integer */
    int64_t num_state_tx;                                                       /* _ARGO: integer */
    int64_t num_comprehensive_stats_upload;                                    /* _ARGO: integer */
    int64_t num_comprehensive_stats_upload_fails;                               /* _ARGO: integer */

    int64_t num_mtunnels;                                                       /* _ARGO: integer */
    int64_t num_mtunnels_health_based;                                          /* _ARGO: integer */
    int64_t num_mtunnels_muted_health_based;                                    /* _ARGO: integer */
    int64_t num_mtunnels_pbroker;                                               /* _ARGO: integer */
    int64_t num_mtunnels_pbroker_health_based;                                  /* _ARGO: integer */
    int64_t num_mtunnels_pbroker_muted_health_based;                            /* _ARGO: integer */
    int64_t num_mtunnels_broker;                                                /* _ARGO: integer */
    int64_t num_mtunnels_broker_health_based;                                   /* _ARGO: integer */
    int64_t num_mtunnels_broker_muted_health_based;                             /* _ARGO: integer */
    int64_t num_mtunnels_freed;                                                 /* _ARGO: integer */
    int64_t num_mtunnels_active;                                                /* _ARGO: integer */
    int64_t num_mtunnels_active_health_based;                                   /* _ARGO: integer */
    int64_t num_mtunnels_active_muted_health_based;                             /* _ARGO: integer */
    int64_t num_mtunnels_peak_active;                                           /* _ARGO: integer */
    int64_t num_mtunnels_peak_active_us;                                        /* _ARGO: integer */
    int64_t num_mtunnels_reaped;                                                /* _ARGO: integer */

    int64_t num_mtunnels_inspect_http;                                          /* _ARGO: integer */
    int64_t num_mtunnels_inspect_DE_http;                                       /* _ARGO: integer */
    int64_t num_mtunnels_inspect_https;                                         /* _ARGO: integer */
    int64_t num_mtunnels_inspect_DE_https;                                      /* _ARGO: integer */
    int64_t num_mtunnels_inspect_ldap;                                          /* _ARGO: integer */
    int64_t num_mtunnels_inspect_DE_ldap;                                       /* _ARGO: integer */
    int64_t num_mtunnels_inspect_smb;                                           /* _ARGO: integer */
    int64_t num_mtunnels_inspect_DE_smb;                                        /* _ARGO: integer */
    int64_t num_mtunnels_inspect_krb;                                           /* _ARGO: integer */
    int64_t num_mtunnels_inspect_DE_krb;                                        /* _ARGO: integer */
    int64_t num_mtunnels_inspect_auto;                                          /* _ARGO: integer */
    int64_t num_mtunnels_inspect_DE_auto;                                       /* _ARGO: integer */
    int64_t num_mtunnels_inspect_auto_tls;                                      /* _ARGO: integer */
    int64_t num_mtunnels_inspect_DE_auto_tls;                                   /* _ARGO: integer */
    int64_t num_mtunnels_inspect_ptag;                                          /* _ARGO: integer */
    int64_t num_mtunnels_inspect_DE_ptag;                                       /* _ARGO: integer */
    int64_t num_mtunnels_inspect_disabled;                                      /* _ARGO: integer */

    int64_t num_mtunnels_inspect_http_active;                                   /* _ARGO: integer */
    int64_t num_mtunnels_inspect_https_active;                                  /* _ARGO: integer */
    int64_t num_mtunnels_inspect_ldap_active;                                   /* _ARGO: integer */
    int64_t num_mtunnels_inspect_smb_active;                                    /* _ARGO: integer */
    int64_t num_mtunnels_inspect_krb_active;                                    /* _ARGO: integer */
    int64_t num_mtunnels_inspect_auto_active;                                   /* _ARGO: integer */
    int64_t num_mtunnels_inspect_auto_tls_active;                               /* _ARGO: integer */
    int64_t num_mtunnels_inspect_ptag_active;                                   /* _ARGO: integer */
    int64_t num_mtunnels_inspect_appl_not_found;                                /* _ARGO: integer */

    /* WAF API stats */
    int64_t num_mtunnels_total_waf_api_traffic;                                 /* _ARGO: integer */
    int64_t num_mtunnels_active_waf_api_traffic;                                /* _ARGO: integer */
    int64_t num_mtunnels_inspect_waf_api_traffic;                               /* _ARGO: integer */

    int64_t num_inspect_appl_cert_key_retrieval_requests;                       /* _ARGO: integer */
    int64_t num_inspect_appl_cert_key_retrieval_failure;                        /* _ARGO: integer */
    int64_t num_inspect_appl_cert_gen_requests;                                 /* _ARGO: integer */
    int64_t num_inspect_appl_cert_gen_failure;                                  /* _ARGO: integer */
    int64_t num_inspect_prfl_construct_failure;                                 /* _ARGO: integer */
    int64_t num_mtunnel_inspt_appl_SSL_connect_failure;                         /* _ARGO: integer */
    int64_t num_inspect_connectors_double_free;                                 /* _ARGO: integer */

    int64_t num_mtunnels_request_pipeline_created;                              /* _ARGO: integer */
    int64_t num_mtunnels_response_pipeline_created;                             /* _ARGO: integer */
    int64_t num_mtunnels_request_pipeline_destroyed;                            /* _ARGO: integer */
    int64_t num_mtunnels_response_pipeline_destroyed;                           /* _ARGO: integer */

    int64_t num_websocket_upgrades;                                             /* _ARGO: integer */
    int64_t num_websocket_inspections;                                          /* _ARGO: integer */

    int64_t num_redirect;                                                       /* _ARGO: integer */



    int64_t app_count;                                                          /* _ARGO: integer */
    int64_t alive_app_count;                                                    /* _ARGO: integer */
    int64_t passive_app_health_count;                                           /* _ARGO: integer */
    int64_t passive_app_muted_health_count;                                     /* _ARGO: integer */
    int64_t active_app_health_count;                                            /* _ARGO: integer */
    int64_t service_count;                                                      /* _ARGO: integer */
    int64_t target_count;                                                       /* _ARGO: integer */
    int64_t alive_target_count;                                                 /* _ARGO: integer */

    uint32_t sys_uptime_s;                                                      /* _ARGO: integer */

    struct zpn_assistant_system_stats sys_stats;

    uint32_t start_s;                                                           /* _ARGO: integer */

    int64_t  free_disk_bytes_for_non_root_user;                                 /* _ARGO: integer */

    double a_lat;                                                               /* _ARGO: double */
    double a_lon;                                                               /* _ARGO: double */

    int64_t cert_validity_start_time_cloud_s;                                   /* _ARGO: integer */
    int64_t cert_validity_end_time_cloud_s;                                     /* _ARGO: integer */
    int64_t cert_force_re_enroll_time_cloud_s;                                  /* _ARGO: integer */

    int    auto_upgrade_disabled;                                               /* _ARGO: integer */

    int    enroll_version;                                                      /* _ARGO: integer */
    int    freebsd_compat_mode;                                                 /* _ARGO: integer */

    int    data_dtls_enabled;                                                   /* _ARGO: integer */
    int64_t num_udp_transactions;                                               /* _ARGO: integer */
    int64_t num_mtls_transactions;                                              /* _ARGO: integer */
    int64_t num_tcp_transactions;                                               /* _ARGO: integer */
    int64_t num_icmp_transactions;                                              /* _ARGO: integer */
    int64_t num_DE_transactions;                                                /* _ARGO: integer */
    int64_t num_udp_with_DE_transactions;                                       /* _ARGO: integer */
    int64_t num_tcp_with_DE_transactions;                                       /* _ARGO: integer */

    int64_t is_first_status_log_sent;                                           /* _ARGO: integer */

    int64_t num_mtunnels_pra;                                                   /* _ARGO: integer */
    int64_t num_mtunnels_freed_pra;                                             /* _ARGO: integer */

    /* Interval in seconds for receiving fohh_status_request messages from broker for various connections */
    int64_t stats_status_interval;                                              /* _ARGO: integer */
    int64_t log_status_interval;                                                /* _ARGO: integer */
    int64_t ctl_status_interval;                                                /* _ARGO: integer */
    int64_t cfg_status_interval;                                                /* _ARGO: integer */
    int64_t ovd_status_interval;                                                /* _ARGO: integer */
    int64_t data_status_interval;                                               /* _ARGO: integer */
    int64_t waf_status_interval;                                                /* _ARGO: integer */
    int64_t app_inspection_status_interval;                                     /* _ARGO: integer */
    int64_t ptag_status_interval;                                               /* _ARGO: integer */
    int download_in_progress;                                                   /* _ARGO: integer */
    int quickack;                                                               /* _ARGO: integer */
    int quickack_read;                                                          /* _ARGO: integer */
    int quickack_app;                                                           /* _ARGO: integer */
    int64_t num_mtunnel_drops_max_sessions_reached_pra;                         /* _ARGO: integer */
    int64_t num_mtunnel_drops_cpu_limit_reached_pra;                            /* _ARGO: integer */
    int64_t num_mtunnel_drops_mem_limit_reached_pra;                            /* _ARGO: integer */
    int64_t partition_gid;                                                      /* _ARGO: integer */
    int64_t num_mtunnel_drops_rate_limit_reached_pra;                           /* _ARGO: integer */
    struct zpn_assistant_dr_stats dr_stats;
    /* max pause time for connector to server connections */
    int64_t max_pause_time_interval;
    /* guacd subcomponent config overrides */
    int64_t guacd_optional;                                                     /* _ARGO: integer */
    int64_t guacd_optional_suppressed;                                          /* _ARGO: integer */

    int64_t num_ctrl_redirect;                                                  /* _ARGO: integer */
    int64_t num_cfg_redirect;                                                   /* _ARGO: integer */
    int64_t num_ovd_redirect;                                                   /* _ARGO: integer */
    int64_t num_stats_redirect;                                                 /* _ARGO: integer */
    int64_t num_log_redirect;                                                   /* _ARGO: integer */
    int64_t num_np_cfg_redirect;                                                /* _ARGO: integer */

    char* hardcoded_broker_name;

    /* Null check is required! */
    zpath_rwlock_t alt_cloud_lock;
    char*   alt_cloud;
    int64_t alt_cloud_enabled;                                                  /* _ARGO: integer */

    /* Null check is required! */
    zpath_rwlock_t ddil_lock;
    /* FOHH connection for control channel */
    struct fohh_connection *sitec_control;
    /* gets set/reset via a debug endpoint */
    int sitec_preferred_backdoor;
    int sitec_preferred;                                                        /* _ARGO: integer */
    int64_t max_allowed_downtime_s;                                             /* _ARGO: integer */
    int64_t is_site_offline_domain_exists;                                      /* _ARGO: integer */
    char *site_offline_domain;                                                  /* _ARGO: string */
    int64_t reenroll_period;                                                    /* _ARGO: integer */
    int64_t is_first_status_log_sent_to_sitec;                                  /* _ARGO: integer */
    int8_t site_is_active;

    /* assistant fproxy stats */
    int64_t fproxy_enabled;                                                     /* _ARGO: integer */
    int64_t num_fproxy_conn;                                                    /* _ARGO: integer */
    int64_t num_fproxy_conn_alive;                                              /* _ARGO: integer */
    int64_t num_fproxy_rx_bytes;                                                /* _ARGO: integer */
    int64_t num_fproxy_tx_bytes;                                                /* _ARGO: integer */
    int64_t num_doublehop_conn;                                                 /* _ARGO: integer */
    int64_t num_doublehop_proxy_conn_free;                                      /* _ARGO: integer */
    int64_t num_doublehop_control_brk_data_conn_free;                           /* _ARGO: integer */

    struct fohh_generic_server *proxy_server;
    pthread_mutex_t proxy_server_lock;
    int proxy_server_initialized;
    struct zevent_base *asst_monitor_base;
    struct event *asst_firedrill_timer;
    struct event *asst_mc_timer;

    uint8_t imds_disabled;
    uint8_t imdsv2_required;
    uint8_t disk_id_fail;

    /*np mode */
    enum connector_type cur_mode;                                               /* _ARGO: integer */
    int64_t np_connector_gid;                                                   /* _ARGO: integer */
    struct zpn_version_control_data version_data;
    struct zpn_firedrill_config firedrill_cfg;

    int64_t udp_health_timeout_failure_flag_enabled;
    int64_t os_upgrade_feature_flag;
    int64_t sarge_upgrade_feature_flag;
    int64_t sarge_backup_version_feature_flag;
    int64_t full_os_upgrade_feature_flag;
    uint64_t last_os_upgrade_time;
    uint64_t last_sarge_upgrade_time;
    enum connector_type app_role_type;                                          /* _ARGO: integer */
    char* platform_version;
    struct zpath_upgrade_stats upgrade_stats;
};

extern struct zpn_assistant_state global_assistant;

struct zpn_tlv *assistant_data_get_tlv(struct zpn_assistant_broker_data *broker);

enum assistant_broker_connection_type {
    invalid,
    broker,     /* any broker and any kind of tunnel (be it control or data) */
    cbroker,    /* Control connection to either public or private broker */
    dbroker,    /* Data connection to either public or private broker */
    czbroker,   /* Control connection to Zscaler broker */
    dzbroker,   /* Data Connection to Zscaler user/data broker */
    cpbroker,   /* Control connection to a private broker */
    dpbroker    /* Data connection to a private user/data pbroker */
};


int
zpn_assistant_global_assistant_get_quickack();
int
zpn_assistant_global_assistant_get_quickack_read();
int
zpn_assistant_global_assistant_get_quickack_app();
int
zpn_assistant_global_assistant_get_mem_usage();
int
zpn_assistant_global_assistant_get_firedrill_state();
void
zpn_assistant_mission_critical_conn_create(struct fohh_thread *thread, void *cookie, int64_t int_cookie);

void
zpn_assistant_site_fill_cfg_obj(struct zpn_assistant_site_config *asst_cfg);

void
assistant_update_firedrill_config_to_site_config_file(struct zpn_assistant_site_config *asst_cfg);

//return 0 if no need to log
typedef int (need2log_ssl_error_f)(int64_t inspt_app_gid, unsigned long ssl_err);
extern need2log_ssl_error_f* fp_n2log_ssl_err;

#endif /* _ZPN_ASSISTANT_PRIVATE_H_ */
