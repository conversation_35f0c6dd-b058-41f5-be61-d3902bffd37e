/*
 * assistant_control_tx.h. Copyright (C) 2019 Zscaler Inc, All Rights Reserved
 *
 */

#ifndef _ASSISTANT_CONTROL_TX_H_
#define _ASSISTANT_CONTROL_TX_H_

#include "zpn/zpn_rpc.h"
#include "admin_probe/admin_probe_rpc.h"

int
assistant_control_tx_init();

typedef void (assistant_control_tx_done_cb)(void* void_cookie,
                                            char* str_cookie,
                                            int   tx_ret_code);

/*
 * Send app route registration on all control channels.
 */
void
assistant_control_tx_app_route_registration(struct zpn_app_route_registration* rep,
                                            assistant_control_tx_done_cb       done_cb,
                                            void*                              done_cb_void_cookie,
                                            char*                              done_cb_str_cookie);

/*
 * Send target health report on all control channels
 */
void
assistant_control_tx_target_health(struct zpn_health_report*    rep,
                                   assistant_control_tx_done_cb done_cb,
                                   void*                        done_cb_void_cookie,
                                   char*                        done_cb_str_cookie);

/*
 * Send status message
 */
void
assistant_control_tx_status(struct zpn_assistant_status_report* rep,
                            assistant_control_tx_done_cb        done_cb,
                            void*                               done_cb_void_cookie,
                            char*                               done_cb_str_cookie);

/*
 * Sends the logs
 */
void
assistant_control_tx_log(struct argo_log*             log_object,
                         int64_t                      argo_log_sequnce,
                         assistant_control_tx_done_cb done_cb,
                         void*                        done_cb_void_cookie,
                         char*                        done_cb_str_cookie);

/*
 * Send BrkReqAck message on a given fohh connection handle.
 */
void
assistant_control_tx_brk_req_ack(struct fohh_connection*        f_conn,
                                 struct zpn_broker_request_ack* ack,
                                 assistant_control_tx_done_cb   done_cb,
                                 void*                          done_cb_void_cookie,
                                 char*                          done_cb_str_cookie);

int
assistant_control_tx_command_probe_status(void* status,
                                          assistant_control_tx_done_cb     done_cb,
                                          void*                            done_cb_void_cookie,
                                          char*                            done_cb_str_cookie,
                                          int is_np_command_probe);

#define ASSISTANT_FIRST_STATUS_REPORT_DELAY_US 60*1000*1000
#define ASSISTANT_ENVIRONMENT_REPORT_UPDATE_US (3*60*1000*1000)

#endif // _ASSISTANT_CONTROL_TX_H_
