/*
 * assistant_np_connector.c . Copyright (C) 2024 Zscaler Inc, All Rights Reserved
 */

#include "zpn_assistantd/assistant_np_connector.h"
#include "zpn_assistantd/zpn_assistant_private.h"
#include "zpn_assistantd/assistant_np_cfg.h"
#include "np_connector/np_connector.h"
#include "wally/wally_test_origin.h"
#include "wally/wally.h"
#include "zpn/zpn_lib.h"
#include "zpn_assistantd/assistant_rpc_tx.h"

struct wally *np_wally = NULL;
struct wally_test_origin *np_wally_test_origin = NULL;

int assistant_np_connector_tx_state_asst_state()
{
    if (global_assistant.is_first_status_log_sent) {
        assistant_rpc_tx_status_report();
    }

    return ZPN_RESULT_NO_ERROR;
}

int assistant_np_connector_init()
{
    int res;

    if (!np_wally) {
        np_wally = assistant_np_cfg_conn_init(global_assistant.hardcoded_broker_name, &np_wally_test_origin);
        if (!np_wally) {
            ZPN_LOG(AL_ERROR, "Could not create np_wally");
            return ZPN_RESULT_ERR;
        }
    }

    res = np_connector_init(global_assistant.customer_gid, global_assistant.gid, np_wally, assistant_np_connector_tx_state_asst_state);
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not init np_connector_init: %s", zpn_result_string(res));
        return ZPN_RESULT_ERR;
    }

    return ZPN_RESULT_NO_ERROR;
}

void assistant_np_connector_exit_callback()
{
    np_connector_terminate_wireguard_before_exit();
}

int
assistant_state_is_ip_forwarding_disabled() {
    int res = ZPATH_RESULT_ERR;
    FILE *file = fopen("/proc/sys/net/ipv4/ip_forward", "r");
    if (file == NULL) {
        ASSISTANT_LOG(AL_ERROR, "Failed to open /proc/sys/net/ipv4/ip_forward");
        return res;
    }

    int ip_forward = 0;
    if (fscanf(file, "%d", &ip_forward) != 1) {
        ASSISTANT_LOG(AL_ERROR, "Error reading /proc/sys/net/ipv4/ip_forward file");
        fclose(file);
        return res;
    }
    fclose(file);

    if (ip_forward == 1) {
        return ZPATH_RESULT_NO_ERROR;
    }
    ZPN_LOG(AL_NOTICE, "/proc/sys/net/ipv4/ip_forward : %d", ip_forward);

    return res;
}

static int np_connector_is_fips_mode_enabled_on_system()
{
    int fd = 0;
    int fips_mode_enabled = 0;
    fd = open("/proc/sys/crypto/fips_enabled", O_RDONLY);
    if (fd != -1) {
        char buffer[4096+1];
        int len;
        char *p;

        lseek(fd, 0, SEEK_SET);
        if ((len = read(fd, buffer, sizeof(buffer)-1)) > 0)
        {
            buffer[len] = '\0';

            p = buffer;
           fips_mode_enabled  = strtoul(p, &p, 0);
        }
        if (fips_mode_enabled) {
            ZPN_LOG(AL_NOTICE, "/proc/sys/crypto/fips_enabled : %d", fips_mode_enabled);
        }
        close(fd);
    } else {
        ZPN_LOG(AL_DEBUG, "failed to read /proc/sys/crypto/fips_enabled, assuming fips mode disabled");
    }

    return fips_mode_enabled;

}

int assistant_np_connector_check_prerequisite() {
    int rhel_version = 0;
    int is_rhel_os = sscanf(g_asst_runtime_os, "Red Hat Enterprise Linux %d", &rhel_version);

    if (!is_rhel_os || (rhel_version != 9) || assistant_state_is_container_env()) {
        ASSISTANT_LOG(AL_WARNING, "RHEL 9 OS and no container environment required for running App Connector in NP mode!");
        sleep(1);
        return ZPATH_RESULT_ERR;
    }

    if (assistant_state_is_ip_forwarding_disabled() || np_connector_is_fips_mode_enabled_on_system())
    {
        ASSISTANT_LOG(AL_ERROR, "App Connector VM not compatible for Network Presence");
        return ZPATH_RESULT_ERR;
    }
    return ZPATH_RESULT_NO_ERROR;
}
