/*
 * assistant_site.h. Copyright (C) 2024 Zscaler Inc, All Rights Reserved
 */

#ifndef _ASSISTANT_SITE_H_
#define _ASSISTANT_SITE_H_

#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <errno.h>
#include <string.h>
#include <sys/stat.h>
#include "zpath_misc/zpath_misc.h"
#include "zpath_lib/zpath_debug.h"

#include "zpn_assistantd/assistant_cfg.h"
#include "zpn_assistantd/assistant_cfg_override.h"
#include "zpn/assistant_log.h"
#include "zpn_assistantd/assistant_log_tx.h"
#include "zpn_assistantd/assistant_state.h"
#include "zpn/zpn_site.h"
#include "zpn/zpn_ddil_config.h"
#include "zpn_assistantd/zpn_assistant_private.h"
#include "fohh/fohh_history.h"

#define CFG_CONN_TO_SITEC        1
#define CFG_OVD_CONN_TO_SITEC    1
#define STATS_CONN_TO_SITEC      1
#define LOG_CONN_TO_SITEC        1
#define CTL_CONN_TO_SITEC        1

#define ASSISTANT_SITE_CONFIG_FILE                          "site_config.cfg"
#define ASSISTANT_SITE_OFFLINE_DOMAIN_STR                   "SITE_OFFLINE_DOMAIN"
#define ASSISTANT_SITE_REENROLL_PERIOD_STR                  "REENROLL_PERIOD"
#define ASSISTANT_SITE_SITE_IS_ACTIVE_STR                   "SITE_IS_ACTIVE"
#define ASSISTANT_SITE_SITEC_PREFERRED_STR                  "SITEC_PREFERRED"
#define ASSISTANT_SITE_MAX_ALLOWED_DOWNTIME_S_STR           "MAX_ALLOWED_DOWNTIME_S"
#define ASSISTANT_FIREDRILL_STATUS_STR                      "FIREDRILL_STATUS"
#define ASSISTANT_FIREDRILL_INTERVAL_S_STR                  "FIREDRILL_INTERVAL_S"
#define ASSISTANT_FIREDRILL_START_TIME_STR                  "FIREDRILL_KICKOFF_TIME"
#define ASSISTANT_SITE_OFFLINE_DOMAIN_MAX_LEN               FOHH_MAX_NAMELEN
#define ASSISTANT_SITE_CFG_FILE_MAX_SIZE                    512
#define ASSISTANT_SITE_DOMAIN_PREFIX                        "co2bcp"
#define ASSISTANT_SITE_OFFLINE_DOMAIN_HEALTH_CHECK_PORT     443

struct zpn_assistant_site_config {
    int sitec_preferred;
    int8_t site_is_active;
    int64_t max_allowed_downtime_s;
    char *offline_domain;
    int64_t reenroll_period;
    char site_offline_domain[ASSISTANT_SITE_OFFLINE_DOMAIN_MAX_LEN];
    /* firedrill fields */
    int64_t fd_status;
    int64_t fd_interval_s;
    int64_t fd_start_time;
};

int assistant_cfg_site_init(struct wally *asst_wally);

int assistant_site_control_init();

void assistant_site_cfg_process(int64_t site_gid);

void assistant_site_read_config_with_lock();

void assistant_site_get_offline_domain_with_lock(char *offline_domain);

int64_t assistant_site_get_reenroll_period_with_lock();

int assistant_site_get_sitec_preferred_with_lock();

int assistant_site_sitec_reachable();

int assistant_site_default_endpoint_reachable();

int64_t assistant_site_get_max_allowed_downtime_with_lock();

void assistant_sitec_control_tx_status();

int assistant_site_firedrill_config_fetch_callback(void *response_callback_cookie,
    struct wally_registrant *registrant,
    struct wally_table *table,
    int64_t request_id,
    int row_count);

int assistant_site_store_config_with_lock(struct zpn_assistant_site_config *asst_site_cfg);

#endif // _ASSISTANT_SITE_H_
