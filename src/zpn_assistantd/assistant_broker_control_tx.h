/*
 * assistant_broker_control_tx.h. Copyright (C) 2019 Zscaler Inc, All Rights Reserved
 *
 */

#ifndef _ASSISTANT_BROKER_CONTROL_TX_H_
#define _ASSISTANT_BROKER_CONTROL_TX_H_

#include "zpn/zpn_rpc.h"
#include "zpn_assistantd/assistant_control_tx.h"

int
assistant_broker_control_tx_init();

/*
 * Send app route registration on broker_control channel.
 */
void
assistant_broker_control_tx_app_route_registration(struct zpn_app_route_registration* rep,
                                                   assistant_control_tx_done_cb       done_cb,
                                                   void*                              done_cb_void_cookie,
                                                   char*                              done_cb_str_cookie);

/*
 * Send target health report on all broker_control channels
 */
void
assistant_broker_control_tx_target_health(struct zpn_health_report*    rep,
                                          assistant_control_tx_done_cb done_cb,
                                          void*                        done_cb_void_cookie,
                                          char*                        done_cb_str_cookie);

/*
 * Send status messages to broker_control channel
 */
void
assistant_broker_control_tx_status(struct zpn_assistant_status_report* rep,
                                   assistant_control_tx_done_cb        done_cb,
                                   void*                               done_cb_void_cookie,
                                   char*                               done_cb_str_cookie);

/*
 * Sends the stats control to broker_control channel
 */
void
assistant_broker_control_tx_stats_control();

/*
 * Sends the stats to broker_control channel
 */
void
assistant_broker_control_tx_stats(struct argo_object*          log_object,
                                  int64_t                      argo_log_sequnce,
                                  assistant_control_tx_done_cb done_cb,
                                  void*                        done_cb_void_cookie,
                                  char*                        done_cb_str_cookie);


/*
 * Sends the encrypted private key to broker_control channel
 */
void
assistant_broker_control_tx_enc_pvt_key(struct zpn_assistant_pvt_key_control *key_info);

/*
 * Sends the auto generation certificate request
 */
void
assistant_broker_control_tx_cert_req(struct zpn_assistant_gen_cert_control  *cert_info);

/*
 * Sends the log control to broker_control channel
 */
void
assistant_broker_control_tx_log_control();

/*
 * Sends the logs to broker_control channel
 */
void
assistant_broker_control_tx_log(struct argo_log*             log_object,
                                int64_t                      argo_log_sequnce,
                                assistant_control_tx_done_cb done_cb,
                                void*                        done_cb_void_cookie,
                                char*                        done_cb_str_cookie);

void assistant_broker_control_tx_state();

int
assistant_broker_control_tx_broker_request_ack(void*                           cookie,
                                               struct zpn_broker_request_ack*  ack);
int
assistant_broker_control_tx_dns_check(void*               cookie,
                                      struct argo_object* object);

int
assistant_broker_control_tx_command_probe_status(void* status,
                                                 assistant_control_tx_done_cb     done_cb,
                                                 void*                            done_cb_void_cookie,
                                                 char*                            done_cb_str_cookie,
                                                 int is_np_command_probe);

#endif // _ASSISTANT_BROKER_CONTROL_TX_H_
