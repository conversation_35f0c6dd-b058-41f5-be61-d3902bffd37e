/*
 * assistant_cfg_version.c. Copyright (C) 2019 Zscaler Inc. All Rights Reserved.
 *
 * All the config coming via zpn_assistant_version table. The version config is mocked via mocked_version object.
 */
#include "zpn_assistantd/zpn_assistant_private.h"
#include "zpn/zpn_assistant_version.h"
#include "zpn_assistantd/assistant_state.h"
#include "zpath_misc/zpath_version.h"
#include "zpn/assistant_log.h"
#include "zpn_assistantd/assistant_assert.h"


static struct {
    int     upgrade_info_mocked;
    int     frr_upgrade_info_mocked;
    int64_t next_restart_time_s;
    char    expected_version[ZPATH_VERSION_STR_MAX_LEN];
    char    expected_sarge_version[ZPATH_VERSION_STR_MAX_LEN];
    int     os_upgrade_enabled;
    char    expected_frr_version[ZPATH_VERSION_STR_MAX_LEN];

} mocked_version;

/*
 * Config that are previously returned to the callers. This can be either mocked or from wally, whatever is returned
 * to the user, the last time. Note that config is already cached in local wally layer too - so don't try to use it in
 * situations like config connection not available. This is used only to log when a config changes, so cache only those
 * configs which are worth logging of change.
 */
static struct {
    int     lone_warrior;
    int64_t next_restart_time_s;
    char    expected_version[ZPATH_VERSION_STR_MAX_LEN];
    char    expected_sarge_version[ZPATH_VERSION_STR_MAX_LEN];
    int     os_upgrade_enabled;
    char    expected_frr_version[ZPATH_VERSION_STR_MAX_LEN];
} cached_config;

static int
assistant_cfg_version_dbg_set_upgrade_info(struct zpath_debug_state*    request_state,
                                           const char**                 query_values,
                                           int                          query_value_count,
                                           void*                        cookie);
static int
assistant_cfg_version_dbg_set_sarge_upgrade_info(struct zpath_debug_state*    request_state,
                                           const char**                 query_values,
                                           int                          query_value_count,
                                           void*                        cookie);
static int
assistant_cfg_version_dbg_unset_upgrade_info(struct zpath_debug_state*  request_state,
                                             const char**               query_values,
                                             int                        query_value_count,
                                             void*                      cookie);

/*
 * Debug interface for setting the FRR Version. Mocks AUM setting the upgrade config for a connector.
 * (eg)
 * curl '127.0.0.1:9000/assistant/cfg/ut/frr_upgrade/unset'
 */
static int
assistant_cfg_version_dbg_unset_frr_upgrade_info(struct zpath_debug_state*           request_state,
                                                 __attribute__((unused))const char** query_values,
                                                 __attribute__((unused))int          query_value_count,
                                                 __attribute__((unused))void*        cookie)
{

    mocked_version.frr_upgrade_info_mocked = 0;
    return ZPATH_RESULT_NO_ERROR;
}

/*
 * Debug interface for setting the config override. Mocks AUM setting the upgrade config for a connector.
 * (eg)
 * curl '127.0.0.1:9000/assistant/cfg/ut/frr_upgrade/set"?delta_s=20&expected_version=10.2'
 */
static int
assistant_cfg_version_dbg_set_frr_upgrade_info(struct zpath_debug_state*    request_state,
                                               const char**                 query_values,
                                               __attribute__((unused))int   query_value_count,
                                               __attribute__((unused))void* cookie)
{
    if (!query_values[0] || !query_values[1]) {
        ZDP("Missing argument: one or more of: delta_s, expected_version\n");
        return ZPATH_RESULT_NO_ERROR;
    }

    mocked_version.next_restart_time_s = assistant_state_get_current_time_cloud_s() + strtol(query_values[0], NULL, 10);
    snprintf(mocked_version.expected_frr_version, sizeof(mocked_version.expected_frr_version), "%s", query_values[1]);
    mocked_version.frr_upgrade_info_mocked = 1;
    return ZPATH_RESULT_NO_ERROR;
}

int
assistant_cfg_version_int(struct wally *asst_wally, int64_t asst_gid)
{
    int res;

    res = zpn_assistant_version_init(asst_wally, asst_gid, 0 /* no fully load */, 0);
    if (res != ZPATH_RESULT_NO_ERROR) {
        ASSISTANT_LOG(AL_ERROR, "Could not initialize zpn_assistant_version table");
        goto done;
    }

    if (!assistant_state_is_dev_environment()) {
        goto done;
    }

    if (zpath_debug_add_write_command("Unit Test: set the next restart time & version. Override the config coming from AUM",
                                "/assistant/cfg/ut/upgrade/set",
                                assistant_cfg_version_dbg_set_upgrade_info,
                                NULL,
                                "delta_s", "restart in seconds",
                                "expected_version", "upgrade/downgrade to version",
                                NULL)) {
        ASSISTANT_LOG(AL_NOTICE, "Unable to register /assistant/cfg/ut/upgrade/set");
    }

    if (zpath_debug_add_admin_command("Unit Test: set the next restart time & sarge version. Override the config coming from AUM",
                                      "/assistant/cfg/ut/sarge_upgrade/set",
                                      assistant_cfg_version_dbg_set_sarge_upgrade_info,
                                      NULL,
                                      "delta_s", "restart in seconds",
                                      "expected_sarge_version", "upgrade/downgrade to sarge version",
                                      "os_upgrade_enabled", "enable/disable os upgrade ",
                                      NULL)) {
        ASSISTANT_LOG(AL_NOTICE, "Unable to register /assistant/cfg/ut/sarge_upgrade/set");
    }

    if (zpath_debug_add_admin_command("Unit Test: unset the next restart time & version. Override the config coming from AUM",
                                "/assistant/cfg/ut/upgrade/unset",
                                assistant_cfg_version_dbg_unset_upgrade_info,
                                NULL,
                                "delta_s", "restart in seconds",
                                "expected_version", "upgrade/downgrade to version",
                                NULL)) {
        ASSISTANT_LOG(AL_NOTICE, "Unable to register /assistant/cfg/ut/upgrade/unset");
    }

    if (zpath_debug_add_admin_command("Unit Test FRR: set the next restart time & FRR version. Override the config coming from AUM",
                                      "/assistant/cfg/ut/frr_upgrade/set",
                                      assistant_cfg_version_dbg_set_frr_upgrade_info,
                                      NULL,
                                      "delta_s", "restart in seconds",
                                      "expected_version", "upgrade/downgrade to FRR version",
                                      NULL)) {
        ASSISTANT_LOG(AL_NOTICE, "Unable to register /assistant/cfg/ut/frr_upgrade/set");
    }

    if (zpath_debug_add_admin_command("Unit Test FRR: unset the next restart time & FRR version",
                                      "/assistant/cfg/ut/frr_upgrade/unset",
                                      assistant_cfg_version_dbg_unset_frr_upgrade_info,
                                      NULL,
                                      NULL)) {
        ASSISTANT_LOG(AL_NOTICE, "Unable to register /assistant/cfg/ut/frr_upgrade/unset");
    }

done:
    return res;
}


/*
 * returns if the assistant is running as a lone warrior - means there is only one connector configured in the
 * connector group.
 *
 * If I couldn't talk to cloud for any reason, return the previous config. If I haven't connected to the cloud,
 * return 0 means that I am not a lone warrior.
 */
int
assistant_cfg_version_is_lone_warrior()
{
    struct zpn_assistant_version*   version;
    int                             result;
    int                             is_lone_warrior;

    is_lone_warrior = 0;
    result = zpn_assistant_version_get_by_id(global_assistant.gid, &version, NULL, NULL, 0);
    if (ZPN_RESULT_NO_ERROR == result) {
        is_lone_warrior = version->lone_warrior;
        goto done;
    }

done:
    if ((0 == cached_config.lone_warrior) && (1 == is_lone_warrior)) {
        ASSISTANT_LOG(AL_INFO, "Connector is detected to be running as a standalone unit");
    }
    cached_config.lone_warrior = is_lone_warrior;
    return is_lone_warrior;
}


/*
 * Result should be interpreted only if the return value is ZPN_RESULT_NO_ERROR. In all other cases callers should
 * not try to use cfg_upgrade_time_s & cfg_upgrade_version values.
 */
int
assistant_cfg_version_get_upgrade_info(int64_t* cfg_upgrade_time_s,
                                       char*    cfg_upgrade_version,
                                       char *   sarge_cfg_upgrade_version,
                                       size_t   max_version_len,
                                       int*     os_upgrade_enabled,
                                       char *   frr_cfg_upgrade_version,
                                       size_t   frr_cfg_upgrade_version_len)
{
    int                             result;
    struct zpn_assistant_version*   version;

    ASSISTANT_ASSERT_HARD(global_assistant.gid, "assistant's gid is not valid");

    *cfg_upgrade_time_s = 0;
    *cfg_upgrade_version = '\0';
    *sarge_cfg_upgrade_version = '\0';
    *frr_cfg_upgrade_version = '\0';

    if (mocked_version.upgrade_info_mocked) {
        *cfg_upgrade_time_s = mocked_version.next_restart_time_s;
        strcpy(cfg_upgrade_version, mocked_version.expected_version);
        strncpy(sarge_cfg_upgrade_version, mocked_version.expected_sarge_version, max_version_len);
        sarge_cfg_upgrade_version[max_version_len - 1] = '\0';
        *os_upgrade_enabled = mocked_version.os_upgrade_enabled;
        result = ZPN_RESULT_NO_ERROR;
        goto done;
    }

    result = zpn_assistant_version_get_by_id(global_assistant.gid, &version, NULL, NULL, 0);
    if (ZPN_RESULT_NO_ERROR == result) {
        *cfg_upgrade_time_s = version->next_restart_time;
        if (version->expected_version) {
            strcpy(cfg_upgrade_version, version->expected_version);
        }
        if (version->expected_sarge_version && version->expected_sarge_version[0] != '\0') {
            strncpy(sarge_cfg_upgrade_version, version->expected_sarge_version, max_version_len);
            sarge_cfg_upgrade_version[max_version_len - 1] = '\0';
        }
        *os_upgrade_enabled = version->os_upgrade_enabled;

        if (version->expected_frr_version && version->expected_frr_version[0] != '\0') {
            snprintf(frr_cfg_upgrade_version, frr_cfg_upgrade_version_len, "%s", version->expected_frr_version);
        }
    }

    if (mocked_version.frr_upgrade_info_mocked) {
        //frr mock is set, overwrite db entry
        *cfg_upgrade_time_s = mocked_version.next_restart_time_s;
        snprintf(frr_cfg_upgrade_version, frr_cfg_upgrade_version_len, "%s", mocked_version.expected_frr_version);
        result = ZPN_RESULT_NO_ERROR;
    }

done:
    if (ZPN_RESULT_NO_ERROR == result) {
        if ((cached_config.next_restart_time_s != *cfg_upgrade_time_s) ||
            (0 != strcmp(cached_config.expected_version, cfg_upgrade_version)) ) {
            ASSISTANT_LOG(AL_NOTICE, "Upgrade config changed, version(%s) next restart time (%"PRId64") epoch sec",
                          cfg_upgrade_version, *cfg_upgrade_time_s);
        }
        if (0 != strcmp(cached_config.expected_sarge_version, sarge_cfg_upgrade_version)) {
            ASSISTANT_LOG(AL_NOTICE, "Upgrade Sarge config changed, version(%s) next restart time (%"PRId64") epoch sec",
                          sarge_cfg_upgrade_version, *cfg_upgrade_time_s);
        }
        if ((cached_config.os_upgrade_enabled != *os_upgrade_enabled)) {
            ASSISTANT_LOG(AL_NOTICE, "OS Upgrade config changed to %d next restart time (%"PRId64") epoch sec",
                          *os_upgrade_enabled, *cfg_upgrade_time_s);
        }
        if (0 != strcmp(cached_config.expected_frr_version, frr_cfg_upgrade_version)) {
            ASSISTANT_LOG(AL_NOTICE, "Upgrade FRR config changed, version(%s) next restart time (%"PRId64") epoch sec",
                          frr_cfg_upgrade_version, *cfg_upgrade_time_s);
        }

        cached_config.next_restart_time_s = *cfg_upgrade_time_s;
        strcpy(cached_config.expected_version, cfg_upgrade_version);
        strncpy(cached_config.expected_sarge_version, sarge_cfg_upgrade_version, max_version_len);
        cached_config.os_upgrade_enabled = *os_upgrade_enabled;
        snprintf(cached_config.expected_frr_version, sizeof(cached_config.expected_frr_version), "%s", frr_cfg_upgrade_version);

    }
    return result;
}

/*
 * Debug interface for setting the config override. Mocks AUM setting the upgrade config for a connector.
 * (eg)
 * curl '127.0.0.1:9000/assistant/cfg/ut/upgrade/set?delta_s=20&expected_version=1.1'
 */
static int
assistant_cfg_version_dbg_set_upgrade_info(struct zpath_debug_state*    request_state,
                                           const char**                 query_values,
                                           int                          query_value_count,
                                           void*                        cookie)
{
    if (!query_values[0] || !query_values[1]) {
        ZDP("Missing argument: one or more of: delta_s, expected_version\n");
        return ZPATH_RESULT_NO_ERROR;
    }

    mocked_version.next_restart_time_s = assistant_state_get_current_time_cloud_s() + strtol(query_values[0], NULL, 10);
    strcpy(mocked_version.expected_version, query_values[1]);
    mocked_version.upgrade_info_mocked = 1;
    return ZPATH_RESULT_NO_ERROR;
}

/*
 * Debug interface for setting the config override. Mocks AUM setting the upgrade config for a connector.
 * (eg)
 * curl '127.0.0.1:9000/assistant/cfg/ut/sarge_upgrade/set?delta_s=20&expected_sarge_version=1.1&os_upgrade_enabled=1'
 */
static int
assistant_cfg_version_dbg_set_sarge_upgrade_info(struct zpath_debug_state*    request_state,
                                                 const char**                 query_values,
                                                 int                          query_value_count,
                                                 void*                        cookie)
{
    if (!query_values[0] || !query_values[1] || !query_values[2]) {
        ZDP("Missing argument: one or more of: delta_s, expected_sarge_version, os_upgrade_enabled\n");
        return ZPATH_RESULT_NO_ERROR;
    }

    mocked_version.next_restart_time_s = assistant_state_get_current_time_cloud_s() + strtol(query_values[0], NULL, 10);
    snprintf(mocked_version.expected_sarge_version, sizeof(mocked_version.expected_sarge_version), "%s",  query_values[1]);
    mocked_version.os_upgrade_enabled = atoi(query_values[2]);
    mocked_version.upgrade_info_mocked = 1;
    return ZPATH_RESULT_NO_ERROR;
}

/*
 * Debug interface for unsetting the config override. Mocks AUM removing a upgrade config for a connector.
 * (eg)
 * curl '127.0.0.1:9000/assistant/cfg/ut/upgrade/unset'
 */
static int
assistant_cfg_version_dbg_unset_upgrade_info(struct zpath_debug_state*  request_state,
                                             const char**               query_values,
                                             int                        query_value_count,
                                             void*                      cookie)
{
    mocked_version.upgrade_info_mocked = 0;
    return ZPATH_RESULT_NO_ERROR;
}
