/*
 * assistant_broker_control.c. Copyright (C) 2019 Zscaler Inc. All Rights Reserved.
 *
 * Control connection is more of less similar between broker & pbroker. The different is captured here and
 * similarities are captured in assistant_control.c
 *
 */
#include "fohh/fohh_private.h"

#include "zpn_assistantd/assistant_broker_control.h"
#include "zpn_assistantd/assistant_state.h"
#include "zpn_assistantd/assistant_stats_tx.h"
#include "zpn_assistantd/assistant_util.h"
#include "zpn_assistantd/zpn_assistant_private.h"
#include "zpn/zpn_fohh_worker.h"
#include "zpn_assistantd/assistant_monitor.h"
#include "zpn/assistant_log.h"
#include "zpn_assistantd/assistant_control.h"
#include "zpn_assistantd/assistant_broker_control_tx.h"
#include "zpn_assistantd/assistant_broker.h"
#include "zpn_assistantd/assistant_rpc_rx.h"
#include "zpn_assistantd/assistant_rpc_tx.h"
#include "zpn_assistantd/assistant_waf_ssl.h"
#include "zpn_dr/zpn_dr_lib.h"
#include "zpn/zpn_rpc.h"



static struct argo_structure_description* assistant_broker_control_stats_description;
static struct argo_structure_description* assistant_broker_control_fohh_stats_description;

static int
assistant_broker_control_stats_dump(struct zpath_debug_state*   request_state,
                                    const char **               query_values,
                                    int                         query_value_count,
                                    void*                       cookie);
static int
assistant_broker_control_conn_history_dump(struct zpath_debug_state*   request_state,
                                           const char **               query_values,
                                           int                         query_value_count,
                                           void*                       cookie);
static int
assistant_broker_control_ut_close_control_connection(struct zpath_debug_state*   request_state,
                                                     const char **               query_values,
                                                     int                         query_value_count,
                                                     void*                       cookie);

static
struct assistant_broker_control_stats {                                         /* _ARGO: object_definition */
    uint64_t conn_cb_connected;                                                 /* _ARGO: integer */
    uint64_t conn_cb_disconnected;                                              /* _ARGO: integer */
    uint64_t conn_unblock_cb;                                                   /* _ARGO: integer */
    uint64_t init_done;                                                         /* _ARGO: integer */
    uint64_t log_status_success;                                                /* _ARGO: integer */
    uint64_t log_status_none;                                                   /* _ARGO: integer */
    uint64_t log_upload_change;                                                 /* _ARGO: integer */
    uint64_t log_upload_flag_change;                                            /* _ARGO: integer */
    uint64_t log_upload_fail;                                                   /* _ARGO: integer */
    uint64_t connected;                                                         /* _ARGO: integer */
    uint64_t alt_cloud_resets;                                                   /* _ARGO: integer */
}stats;

struct assistant_broker_control_fohh_stats {                                    /* _ARGO: object_definition */
    uint64_t place_holder;                                                      /* _ARGO: integer */
};
#include "zpn_assistantd/assistant_broker_control_compiled_c.h"
#include "zpn_assistantd/assistant_assert.h"


static struct assistant_broker_control_fohh_stats fohh_stats[FOHH_MAX_THREADS];


static int
assistant_broker_control_conn_init(const char* broker);

/*
 * We are trying to do the sanity check from monitor thread (we monitor connector status every 1 min)
 * We are not relying sanity check from fohh library because connector is running a customer environment and we do not
 * have absolute control of their system time.
 * (if in the future, we want to do sanity check for pbroker, don't do in in fohh)
 *
 * Currently how we check if a connection insane:
 * monitor thread call this function every 1 min.
 * every min when this function get called, it checked if the connection is disconnected or not.
 * if it is disconnected, it incremented the counter by approximately 60s
 *
 * note that this is not a exact calculation, but approximate is fine there.
 * we just want to do if a connection is disconnected for a very long time.
 *
 * Note that this function will be called every ASSISTSANT_MONITOR_TIMER_S second
 */
void assistant_broker_control_check_insane()
{
    /*
     * In pbroker environment, even when the broker_control connection is down, we still want to keep operating. Being
     * able to operate when internet is DOWN is a requirement for pbroker deployment. So lets no try to do many tricks
     * to look at the sanity of the connector for NOW.
     */
    if (1 == assistant_state_is_pbroker_environment()) {
        return;
    }

    static int64_t counter_s = 0;

    if (is_zpn_drmode_enabled()) {
        return;
    }
    if ((global_assistant.broker_control) && (fohh_connection_connected == (global_assistant.broker_control)->state)) {
        counter_s = 0;
    } else {
        counter_s+=ASSISTANT_MONITOR_TIMER_S;
        if ((int64_t)counter_s >= assistant_state_get_max_allowed_connection_downtime_s()) {
           /*
            * If subcomponent download is in progress,
            * we still want to keep operating, even when control connection is down.
            * This is to enable download completion in low bandwidth environment
            */
           if ((global_assistant.download_in_progress == DOWNLOAD_IN_PROGRESS) && (counter_s < MAX_DOWNLOAD_IN_PROGRESS_TIME)) {
                ASSISTANT_LOG(AL_NOTICE, "Connector(ID = %"PRId64") (Name = %s) "
                                       "control connection %s down for %"PRId64" secs, Continuing download in progress.",
                                        global_assistant.gid, assistant_state_get_configured_name(),
                                        global_assistant.broker_control->connection_description, counter_s);
                return;
            }

            ASSISTANT_LOG(AL_CRITICAL, "Connector(ID = %"PRId64") (Name = %s) restarting as it is disconnected "
                                       "from control connection %s for %"PRId64" secs. Please check internet connectivity.",
                                        global_assistant.gid, assistant_state_get_configured_name(),
                                        global_assistant.broker_control->connection_description, counter_s);
            /*
             * Sleep so that the logs can be sent
             */
            sleep(1);
            exit(1);
        }
    }
}

static int
assistant_broker_control_conn_cb(struct fohh_connection*    connection,
                                 enum fohh_connection_state state,
                                 void*                      cookie)
{
    if (state == fohh_connection_connected) {
        ASSISTANT_LOG(AL_NOTICE, "Broker control connection successfully connected to Zscaler Cloud: %s",
                fohh_description(connection));
        ASSISTANT_DEBUG_BROKER_CONTROL("Broker control connection connected: %s", fohh_description(connection));
        stats.conn_cb_connected++;
        global_assistant.broker_control_label = assistant_broker_register(NULL,
                                                assistant_broker_control_tx_broker_request_ack,
                                                assistant_broker_control_tx_dns_check,
                                                __FUNCTION__, __LINE__);

        assistant_control_connected(connection, 0);
        assistant_broker_control_connected(connection);
        zpn_fohh_worker_assistant_connect_control(fohh_connection_get_thread_id(connection));
    } else {
        ASSISTANT_LOG(AL_NOTICE, "Broker control connection to Zscaler Cloud closed: %s %s",
                fohh_description(connection), fohh_close_reason(connection));
        ASSISTANT_DEBUG_BROKER_CONTROL("Broker control connection closed: %s", fohh_description(connection));
        assistant_broker_unregister(global_assistant.broker_control_label);
        global_assistant.broker_control_label = 0;
        stats.conn_cb_disconnected++;
        zpn_fohh_worker_assistant_disconnect_control(fohh_connection_get_thread_id(connection));
    }

    return ZPN_RESULT_NO_ERROR;
}


int
assistant_broker_control_init(const char* broker)
{
    int result;

    if (!(assistant_broker_control_stats_description =
                  argo_register_global_structure(ASSISTANT_BROKER_CONTROL_STATS_HELPER))) {
        return ZPATH_RESULT_ERR;
    }

    if (!(assistant_broker_control_fohh_stats_description =
                  argo_register_global_structure(ASSISTANT_BROKER_CONTROL_FOHH_STATS_HELPER))) {
        return ZPATH_RESULT_ERR;
    }

    result = zpath_debug_add_read_command("Dump info related to broker control operation.",
                                     "/assistant/broker/control/stats/dump", assistant_broker_control_stats_dump, NULL,
                                     NULL);
    if (result) {
        ASSISTANT_LOG(AL_ERROR, "couldn't add /assistant/broker/control/stats/dump");
        return result;
    }


    result = assistant_broker_control_conn_init(broker);
    if (result ) {
        ASSISTANT_LOG(AL_ERROR, "Could not init control connection to broker");
        return result;
    }

    stats.init_done++;
    return result;
}


static int
assistant_broker_control_conn_unblock_cb(struct fohh_connection*      connection,
                                         enum fohh_queue_element_type element_type,
                                         void*                        cookie)
{
    ASSISTANT_LOG(AL_NOTICE, "fohh unblock on broker control connection");
    stats.conn_unblock_cb++;
    return FOHH_RESULT_NO_ERROR;
}

/*
 * Intialize the connection with control broker. Note that we talk to dispatcher also via control broker.
 *
 * broker - can be NULL
 */
static int
assistant_broker_control_conn_init(const char* broker)
{
    char        broker_name[1000];
    char        broker_stats_name[1000 + 16];
    char        sni_customer_domain_ctl[1000];
    int         fohh_quiet;
    int         fohh_use_ssl;
    char*       cloud_name;
    char*       customer_domain;
    int         result;
    char        alt_cloud_name[256];

    if (global_assistant.alt_cloud && global_assistant.alt_cloud_enabled) {
        assistant_state_get_alt_cloud_with_lock(alt_cloud_name);
        cloud_name = alt_cloud_name;
    } else {
        cloud_name = assistant_state_get_cloud_name();
    }

    if (broker) {
        snprintf(broker_name, sizeof(broker_name), "%s", broker);
    } else {
        snprintf(broker_name, sizeof(broker_name), "any.co2br.%s", cloud_name);
    }
    customer_domain = assistant_state_get_customer_domain();

    ASSISTANT_DEBUG_BROKER_CONTROL("Init control, broker(%s) cloud_name(%s) customer_domain(%s)",
                                   broker_name, cloud_name, customer_domain);

    snprintf(broker_stats_name, sizeof(broker_stats_name), "brk-ctl-%s", broker_name);
    broker_stats_name[1000] = 0; /* limiting stats name to 256 bytes */

    if (1 == global_assistant.enroll_version) {
        /* V1 Style SNI: <customer domain>.actl.<cloud_name> */
        snprintf(sni_customer_domain_ctl, sizeof(sni_customer_domain_ctl), "%s.actl.%s", customer_domain, cloud_name);
    } else {
        /* V2 or V3 Style SNI: <assistant_id>.actl.<cloud_name> */
        snprintf(sni_customer_domain_ctl, sizeof(sni_customer_domain_ctl), "%ld.actl.%s", (long)global_assistant.gid,
                 cloud_name);
    }

    ASSISTANT_LOG(AL_DEBUG, "SNI for broker control connection(%s)", sni_customer_domain_ctl);

    global_assistant.broker_control = fohh_client_create(FOHH_WORKER_ZPN_ACTL,
                                                         broker_stats_name,
                                                         argo_serialize_binary,
                                                         fohh_connection_style_argo,
                                                         fohh_quiet = 0,
                                                         &global_assistant,
                                                         assistant_broker_control_conn_cb,
                                                         NULL,
                                                         assistant_broker_control_conn_unblock_cb,
                                                         NULL,
                                                         broker_name,
                                                         sni_customer_domain_ctl,
                                                         cloud_name,
                                                         htons(ZPN_ASSISTANT_BROKER_PORT),
                                                         global_assistant.assistant_to_public_cloud_ctx,
                                                         fohh_use_ssl = 1,
                                                         ZPN_ASSISTANT_BROKER_RX_TIMEOUT_S);

    if (!global_assistant.broker_control) {
        ASSISTANT_LOG(AL_ERROR, "Could not initialize broker control connection");
        return ZPATH_RESULT_ERR;
    }

    fohh_set_sticky(global_assistant.broker_control, 1);

    fohh_set_max_backoff(global_assistant.broker_control, ASSISTANT_STATE_ALT_CLOUD_CONN_MAX_INIT_TIME_S >> 1);
    fohh_connection_monitor_sanity(global_assistant.broker_control,
                                   assistant_state_fohh_connection_sanity_callback_with_lock,
                                   ASSISTANT_STATE_ALT_CLOUD_CONN_MAX_INIT_TIME_S);
    fohh_connection_set_default_sni(global_assistant.broker_control, assistant_state_get_cloud_name());
    /*
     * Set status interval address, which has the interval in seconds for sending fohh_status_request messages by connected broker.
     * Default status interval is 1 second for sending fohh_status_request messages.
     */
    fohh_set_status_interval(global_assistant.broker_control, &(global_assistant.ctl_status_interval));

    fohh_suppress_connection_event_logs(global_assistant.broker_control);

    fohh_history_enable(global_assistant.broker_control);
    result = zpath_debug_add_read_command("Dump history of events happened to broker control connection.",
                                     "/assistant/broker/control/conn/history/dump",
                                     assistant_broker_control_conn_history_dump,
                                     NULL, NULL);
    if (result) {
        ASSISTANT_LOG(AL_ERROR, "couldn't add /assistant/broker/control/conn/history/dump");
        return result;
    }

    if (!assistant_state_is_dev_environment()) {
        return ZPATH_RESULT_NO_ERROR;
    }

    result = zpath_debug_add_write_command("UT: Assistant receives a zpn broker request",
                                     "/assistant/broker/control/ut/rx_zpn_broker_request",
                                     assistant_control_rx_zpn_broker_request_dbg,
                                     NULL,
                                     "mtunnel_id", "Identifier for the mtunnel being created",
                                     "g_app", "The requested application",
                                     "g_app_grp", "The application group of the server to use",
                                     "g_ast_grp", "The assistant group of the server to use",
                                     "g_srv_grp","The server group of the server to use",
                                     "domain","name of the domain requested",
                                     "g_ast", "Assistant - Filled in by dispatcher when the dispatcher chooses",
                                     "g_brk", "The broker who is making the request on the client's behalf",
                                     "c_uid", "The client user ID making the original request. uid is generic 'text' and is simply the authenticated (via SAML/Cert) user name",
                                     "brk_name", "The resolvable domain name of the broker the client is connected to",
                                     "c_port", "The port (TCP) the client requested access to",
                                     "ip_protocol", "ip protocol",
                                     "g_aps", "Application server - Filled in by dispatcher when the dispatcher chooses an assistant/app_server to use to satisfy the mtunnel",
                                     "g_bfw", "The broker who forwards the request from dispatcher to assistant",
                                     "s_ip", "application server ip",
                                     "icmp_access_type", "0 - none, 1 - ping, 2 - traceroute",
                                     NULL);
    if (result) {
        ASSISTANT_LOG(AL_NOTICE, "Unable to register /assistant/broker/control/ut/rx_zpn_broker_request");
        return result;
    }

    result = zpath_debug_add_admin_command("UT: disconnect the broker control connection.",
                                     "/assistant/broker/control/ut/down",
                                     assistant_broker_control_ut_close_control_connection, NULL,
                                     NULL);
    if (result) {
        ASSISTANT_LOG(AL_ERROR, "couldn't add /assistant/broker/control/ut/down");
        return result;
    }

    // actl disabled in case rebooted in firedrill mode
    if(global_assistant.broker_control &&
        (global_assistant.firedrill_cfg.status == ZPN_ASSISTANT_FIREDRILL_ENABLED)) {
            ASSISTANT_LOG(AL_ERROR, "appc in firedrill, disable the connection");
            fohh_connection_disable_async(global_assistant.broker_control,
                                            fohh_connection_incarnation(global_assistant.broker_control),
                                            FOHH_CLOSE_REASON_ASSISTANT_FIREDRILL);
    }
    return ZPATH_RESULT_NO_ERROR;
}


void
assistant_broker_control_log_status()
{
    if (global_assistant.broker_control) {
        char uptime_str[FOHH_MAX_UPTIME_BUF_LEN];
        char max_rtt_us_str[FOHH_RTT_SINCE_LAST_QUERY_MSG_STR_MAX];
        fohh_connection_max_rtt_to_string(global_assistant.broker_control, max_rtt_us_str, sizeof(max_rtt_us_str));
        fohh_connection_reset_max_rtt(global_assistant.broker_control);

        ASSISTANT_LOG(AL_NOTICE, "Broker control connection, state %s, %s uptime %s%s, disconnect_duration_s %"PRId64"",
                      fohh_state(global_assistant.broker_control), fohh_description(global_assistant.broker_control),
                      fohh_get_uptime_str(global_assistant.broker_control, uptime_str, sizeof(uptime_str)),
                      (fohh_get_state(global_assistant.broker_control) == fohh_connection_connected)? max_rtt_us_str: "",
                      fohh_conn_get_current_disconnect_duration_s(global_assistant.broker_control));
        stats.log_status_success++;
    } else {
        ASSISTANT_LOG(AL_NOTICE, "Broker control connection - NONE");
        stats.log_status_none++;
    }
}


static int
assistant_broker_control_rx_log_control(void*               argo_cookie_ptr,
                                        void*               argo_structure_cookie_ptr,
                                        struct argo_object* object)
{
    struct zpn_assistant_log_control* req = object->base_structure_void;

    if (assistant_debug_log & ASSISTANT_DEBUG_BROKER_CONTROL_BIT) {
        char dump[5000];
        if (argo_object_dump(object, dump, sizeof(dump), NULL, 0) == ARGO_RESULT_NO_ERROR) {
            ASSISTANT_LOG(AL_DEBUG, "Rx: %s", dump);
        }
    }

    if (0 == req->type) {
        ASSISTANT_LOG(AL_INFO, "Logging %s from cloud", req->upload ? "enabled" : "disabled");
        global_assistant.log_upload_enabled = req->upload;
        stats.log_upload_change++;
    } else if (1 == req->type) {
        if (assistant_debug_log & (1u << req->flag)) {
            ZPN_LOG(AL_INFO, "Logging updated from cloud, turning OFF assistant debug flag %s , %"PRIu64,
                    assistant_log_debug_names[req->flag], req->flag);
            assistant_debug_log &= ~(1u << req->flag);
        } else {
            ZPN_LOG(AL_INFO, "Logging updated from cloud, turning ON assistant debug flag %s , %"PRIu64,
                    assistant_log_debug_names[req->flag], req->flag);
            assistant_debug_log |= (1u << req->flag);
        }
        stats.log_upload_flag_change++;
    } else {
        ASSISTANT_LOG(AL_ERROR, "Unknown log control type = %d, please report to Zscaler customer support team",
                      req->type);
        stats.log_upload_fail++;
    }

    assistant_broker_control_tx_log_control();

    return ZPN_RESULT_NO_ERROR;
}


static int
assistant_broker_control_restart(void*               argo_cookie_ptr,
                                 void*               argo_structure_cookie_ptr,
                                 struct argo_object* object)
{
    if (assistant_debug_log & ASSISTANT_DEBUG_BROKER_CONTROL_BIT) {
        char dump[5000];
        if (argo_object_dump(object, dump, sizeof(dump), NULL, 0) == ARGO_RESULT_NO_ERROR) {
            ASSISTANT_LOG(AL_DEBUG, "Rx: %s", dump);
        }
    }

    ASSISTANT_LOG(AL_ALERT, "Assistant(%s) restarting per the request of broker_control.",
                  assistant_state_get_configured_name());
    /* sleep to make sure the event log get sent to the broker */
    sleep(5);
    exit(1);

    return ZPN_RESULT_NO_ERROR;
}


static int
assistant_broker_control_rx_broker_request(void*               argo_cookie_ptr,
                                           void*               argo_structure_cookie_ptr,
                                           struct argo_object* object)
{
    struct zpn_broker_request *req = NULL;
    char connection_dbg_str[ASSISTANT_RPC_RX_CONNECTION_DBG_STR_LEN];
    if (assistant_debug_log & ASSISTANT_DEBUG_BROKER_CONTROL_BIT) {
        char dump[5000];
        if (argo_object_dump(object, dump, sizeof(dump), NULL, 0) == ARGO_RESULT_NO_ERROR) {
            ASSISTANT_LOG(AL_DEBUG, "Rx: %s", dump);
        }
    }

    req = object->base_structure_void;
    global_assistant.control_brk_gid = req->g_bfw;

    __sync_fetch_and_add_8(&global_assistant.num_brk_req_from_dsp, 1);
    snprintf(connection_dbg_str, sizeof(connection_dbg_str), "%s|%s", "Control", fohh_description(global_assistant.broker_control));
    assistant_rpc_rx_broker_request(global_assistant.broker_control_label, connection_dbg_str,
                                    req);
    return ZPN_RESULT_NO_ERROR;
}


static int
assistant_broker_control_rx_dns_check(void*               argo_cookie_ptr,
                                      void*               argo_structure_cookie_ptr,
                                      struct argo_object* object)
{
    if (assistant_debug_log & ASSISTANT_DEBUG_BROKER_CONTROL_BIT) {
        char dump[5000];
        if (argo_object_dump(object, dump, sizeof(dump), NULL, 0) == ARGO_RESULT_NO_ERROR) {
            ASSISTANT_LOG(AL_DEBUG, "Rx: %s", dump);
        }
    }

    __sync_fetch_and_add_8(&global_assistant.num_dns_asst_check_rx_from_dsp, 1);
    assistant_rpc_rx_dns_check(global_assistant.broker_control_label, object);
    return ZPN_RESULT_NO_ERROR;
}

static int
assistant_broker_rx_decrypted_pvt_key(void*               argo_cookie_ptr,
                                      void*               argo_structure_cookie_ptr,
                                      struct argo_object* object)
{
    struct zpn_assistant_pvt_key_control *key_info = object->base_structure_void;

    ZPN_LOG(AL_INFO, "==== Rcvd decrypted key for cert_id: %"PRId64"  =====", key_info->cert_id);

    /* assistant recd the decrypted key. Invoke the api to create ssl context */
    assistant_waf_invoke_decrypted_key_cb(key_info);
    return ZPN_RESULT_NO_ERROR;
}

static int
assistant_broker_rx_app_cert_n_pvt_key(void*         argo_cookie_ptr,
                                       void*         argo_structure_cookie_ptr,
                                       struct argo_object* object)
{
    struct zpn_assistant_app_cert_key_control *cert_info = object->base_structure_void;

    ZPN_LOG(AL_INFO, "==== [AUTO_CERT] Rcvd generated cert response [%s] app_id: %"PRId64" cert_id: %"PRId64" =====",
            zpath_result_string(cert_info->status), cert_info->app_gid,
            cert_info->cert_id);

    /* assistant recvd the auto gen cert and key. Invoke the api to create ssl context */
    assistant_waf_invoke_auto_cert_key_cb(cert_info);
    return ZPN_RESULT_NO_ERROR;
}


int
assistant_broker_control_connected(struct fohh_connection* connection)
{
    struct argo_state* argo;
    int                res;

    argo = fohh_argo_get_rx(connection);

    /* Register zpn_assistant_log_control */
    if ((res = argo_register_structure(argo, zpn_assistant_log_control_description,
                                       assistant_broker_control_rx_log_control, connection))) {
        ASSISTANT_LOG(AL_ERROR, "Could not register zpn_assistant_log_control for broker control connection %s",
                      fohh_description(connection));
        return res;
    }

    /* Register zpn_assistant_restart */
    if ((res = argo_register_structure(argo, zpn_assistant_restart_description,
                                       assistant_broker_control_restart, connection))) {
        ASSISTANT_LOG(AL_ERROR, "Could not register zpn_assistant_restart for broker control connection %s",
                      fohh_description(connection));
        return res;
    }

    /* Register zpn_broker_request */
    if ((res = argo_register_structure(argo, zpn_broker_request_description, assistant_broker_control_rx_broker_request,
                                       connection))) {
        ASSISTANT_LOG(AL_ERROR, "Could not register zpn_broker_request for broker connection %s",
                      fohh_description(connection));
        return res;
    }

    /* Register zpn_dns_assistant_check */
    if ((res = argo_register_structure(argo, zpn_dns_assistant_check_description, assistant_broker_control_rx_dns_check,
                                       connection))) {
        ASSISTANT_LOG(AL_ERROR, "Could not register zpn_dns_assistant_check for broker connection %s",
                      fohh_description(connection));
        return res;
    }

    if ((res = argo_register_structure(argo, zpn_assistant_pvt_key_control_description,
                                       assistant_broker_rx_decrypted_pvt_key, connection))) {
        ZPN_LOG(AL_ERROR, "Could not register zpn_broker_assistant_pvt_key_control for broker connection %s", fohh_description(connection));
        return res;
    }

    if ((res = argo_register_structure(argo, zpn_assistant_app_cert_key_control_description,
                                       assistant_broker_rx_app_cert_n_pvt_key, connection))) {
        ZPN_LOG(AL_ERROR, "Could not register zpn_broker_assistant_app_cert_key_control for broker connection %s", fohh_description(connection));
        return res;
    }

    assistant_broker_control_tx_log_control();
    assistant_broker_control_tx_stats_control();

    stats.connected++;
    return ZPATH_RESULT_NO_ERROR;
}


/*
 * 1 if the broker_control is connected, 0 otherwise
 */
int
assistant_broker_control_is_connected()
{
    if (!global_assistant.broker_control) {
        return 0;
    }

    if (fohh_connection_connected == fohh_get_state(global_assistant.broker_control)) {
        return 1;
    }

    return 0;
}


/*
 * Return approximately (remote_epoch_us - local_epoch_us)
 */
int
assistant_broker_control_get_time_differential(int64_t *diff_us)
{
    static int      ever = 0;
    static int64_t  last_diff_us = 0;
    struct fohh_connection *f_conn = NULL;

    if (global_assistant.broker_control &&
        (fohh_get_state(global_assistant.broker_control) == fohh_connection_connected)) {
        f_conn = global_assistant.broker_control;
    } else if (global_assistant.sitec_control &&
               (fohh_get_state(global_assistant.sitec_control) == fohh_connection_connected)) {
        f_conn = global_assistant.sitec_control;
    }

    int64_t new_diff_us;
    if (f_conn && fohh_peer_epoch_us_diff(f_conn, &new_diff_us) == FOHH_RESULT_NO_ERROR) {
        ever = 1;
        last_diff_us = new_diff_us;
    }

    if (!ever) return ZPN_RESULT_NOT_READY;
    *diff_us = last_diff_us;
    return ZPN_RESULT_NO_ERROR;
}


static int
assistant_broker_control_stats_dump(struct zpath_debug_state*   request_state,
                                    const char **               query_values,
                                    int                         query_value_count,
                                    void*                       cookie)
{
    char        jsonout[10000];

    if (ARGO_RESULT_NO_ERROR ==
        argo_structure_dump(assistant_broker_control_stats_description, &stats, jsonout, sizeof(jsonout), NULL,
                            1)){
        ZDP("%s\n", jsonout);
    }

    {
        int curr_fohh_thread;
        int max_fohh_thread = fohh_thread_count();
        for (curr_fohh_thread = 0; curr_fohh_thread < max_fohh_thread; curr_fohh_thread++) {
            if (ARGO_RESULT_NO_ERROR == argo_structure_dump(assistant_broker_control_fohh_stats_description,
                                            &fohh_stats[curr_fohh_thread], jsonout, sizeof(jsonout), NULL, 1)) {
                ZDP("%s\n", jsonout);
            }
        }
    }

    return ZPATH_RESULT_NO_ERROR;
}


static int
assistant_broker_control_conn_history_dump(struct zpath_debug_state*   request_state,
                                           const char **               query_values,
                                           int                         query_value_count,
                                           void*                       cookie)
{
    char* history_str;

    history_str = ASST_MALLOC(sizeof(char) * FOHH_HISTORY_STR_MIN_LEN);
    fohh_history_get_str(global_assistant.broker_control, history_str, FOHH_HISTORY_STR_MIN_LEN);
    ZDP("%s", history_str);
    ASST_FREE(history_str);

    return ZPATH_RESULT_NO_ERROR;
}

/*
 * Just for UT, don't expect anything to work after doing this test.
 */
static int
assistant_broker_control_ut_close_control_connection(struct zpath_debug_state*   request_state,
                                                     const char **               query_values,
                                                     int                         query_value_count,
                                                     void*                       cookie)
{
    fohh_connection_delete(global_assistant.broker_control, FOHH_CLOSE_REASON_UT_CLOSE_FROM_ASST);
    return ZPATH_RESULT_NO_ERROR;
}

void assistant_control_conn_reset_count()
{
    stats.alt_cloud_resets++;
}
