/*
 * assistant_control_tx.c. Copyright (C) 2019 Zscaler Inc. All Rights Reserved.
 *
 * TX operation on all the control channels. This will fan out into broker_control & pbroker_control.
 * This will help to make sure the core data structures do not have worry about what control channels available and what
 * optimization have to be done each types of control channels - that way it is an abstration layer to send any data
 * out to the control channel(s).
 */

#include "zpath_lib/zpath_debug.h"
#include "zpn_assistantd/assistant_control_tx.h"
#include "zpn_assistantd/assistant_broker_control_tx.h"
#include "zpn_assistantd/assistant_pbroker_control_tx.h"
#include "zpn/assistant_log.h"
#include "zpn_assistantd/assistant_monitor.h"
#include "zpn_assistantd/zpn_assistant_private.h"
#include "zpn_assistantd/assistant_data.h"
#include "zpn_dr/zpn_dr_lib.h"
#include "assistant_cfg_override_feature.h"

static struct argo_structure_description* assistant_control_tx_stats_description;
static struct argo_structure_description* assistant_control_tx_fohh_stats_description;

static
struct assistant_control_tx_stats {                                      /* _ARGO: object_definition */
    uint64_t app_route_registation_brk;                                  /* _ARGO: integer */
    uint64_t app_route_registation_pb;                                   /* _ARGO: integer */
    uint64_t target_health;                                              /* _ARGO: integer */
    uint64_t log;                                                        /* _ARGO: integer */
    uint64_t argo_object;                                                /* _ARGO: integer */
    uint64_t status;                                                     /* _ARGO: integer */
    uint64_t tx_state;                                                   /* _ARGO: integer */
    uint64_t stats;                                                      /* _ARGO: integer */
    uint64_t probe_status;                                               /* _ARGO: integer */
}stats;

struct assistant_control_tx_fohh_stats {                                 /* _ARGO: object_definition */
    uint64_t place_holder;                                               /* _ARGO: integer */
};
#include "zpn_assistantd/assistant_control_tx_compiled_c.h"

static struct assistant_control_tx_fohh_stats fohh_stats[FOHH_MAX_THREADS];

static int
assistant_control_tx_dump(struct zpath_debug_state*   request_state,
                          const char**                query_values,
                          int                         query_value_count,
                          void*                       cookie);


int
assistant_control_tx_init()
{
    int result;

    result = assistant_broker_control_tx_init();
    if (result) {
        ASSISTANT_LOG(AL_ERROR, "Could not init broker control TX");
        return result;
    }

    result = assistant_pbroker_control_tx_init();
    if (result) {
        ASSISTANT_LOG(AL_ERROR, "Could not init pbroker control TX");
        return result;
    }

    if (!(assistant_control_tx_stats_description =
                  argo_register_global_structure(ASSISTANT_CONTROL_TX_STATS_HELPER))) {
        return ZPATH_RESULT_ERR;
    }

    if (!(assistant_control_tx_fohh_stats_description =
                  argo_register_global_structure(ASSISTANT_CONTROL_TX_FOHH_STATS_HELPER))) {
        return ZPATH_RESULT_ERR;
    }

    result = zpath_debug_add_read_command("Dump info related to control TX operation.",
                                     "/assistant/control/tx/stats/dump", assistant_control_tx_dump, NULL,
                                     NULL);
    if (result) {
        ASSISTANT_LOG(AL_ERROR, "couldn't add /assistant/control/tx/stats/dump");
        return result;
    }

    return ZPN_RESULT_NO_ERROR;
}

void
assistant_control_tx_app_route_registration(struct zpn_app_route_registration *rep,
                                            assistant_control_tx_done_cb done_cb,
                                            void*                        done_cb_void_cookie,
                                            char*                        done_cb_str_cookie)
{
    if (!is_zpn_drmode_enabled() && !assistant_cfg_override_disable_tx_app_route_reg()) {
        assistant_broker_control_tx_app_route_registration(rep, done_cb, done_cb_void_cookie, done_cb_str_cookie);
        stats.app_route_registation_brk++;
    }
    assistant_pbroker_control_tx_app_route_registration(rep, done_cb, done_cb_void_cookie, done_cb_str_cookie);
    stats.app_route_registation_pb++;
}


void
assistant_control_tx_target_health(struct zpn_health_report*    rep,
                                   assistant_control_tx_done_cb done_cb,
                                   void*                        done_cb_void_cookie,
                                   char*                        done_cb_str_cookie)
{
    stats.target_health++;
    if (!is_zpn_drmode_enabled()) {
        assistant_broker_control_tx_target_health(rep, done_cb, done_cb_void_cookie, done_cb_str_cookie);
    }
    assistant_pbroker_control_tx_target_health(rep, done_cb, done_cb_void_cookie, done_cb_str_cookie);
}

void
assistant_control_tx_status(struct zpn_assistant_status_report* rep,
                            assistant_control_tx_done_cb        done_cb,
                            void*                               done_cb_void_cookie,
                            char*                               done_cb_str_cookie)
{
    stats.status++;
    if (!is_zpn_drmode_enabled()) {
        assistant_broker_control_tx_status(rep, done_cb, done_cb_void_cookie, done_cb_str_cookie);
    }
    assistant_pbroker_control_tx_status(rep, done_cb, done_cb_void_cookie, done_cb_str_cookie);
}

int
assistant_control_tx_command_probe_status(void* status,
                                          assistant_control_tx_done_cb     done_cb,
                                          void*                            done_cb_void_cookie,
                                          char*                            done_cb_str_cookie,
                                          int is_np_command_probe)
{
    int res;
    res = assistant_broker_control_tx_command_probe_status(status, done_cb, done_cb_void_cookie, done_cb_str_cookie, is_np_command_probe);
    if (res) {
        return res;
    }

    __sync_fetch_and_add_8(&(stats.probe_status), 1);

    return ZPN_RESULT_NO_ERROR;
}

static int
assistant_control_tx_dump(struct zpath_debug_state*   request_state,
                          const char**                query_values,
                          int                         query_value_count,
                          void*                       cookie)
{
    char        jsonout[10000];

    if (ARGO_RESULT_NO_ERROR == argo_structure_dump(assistant_control_tx_stats_description, &stats, jsonout,
                                                    sizeof(jsonout), NULL, 1)){
        ZDP("%s\n", jsonout);
    }

    {
        int curr_fohh_thread;
        int max_fohh_thread = fohh_thread_count();
        for (curr_fohh_thread = 0; curr_fohh_thread < max_fohh_thread; curr_fohh_thread++) {
            if (ARGO_RESULT_NO_ERROR ==  argo_structure_dump(assistant_control_tx_fohh_stats_description,
                                &fohh_stats[curr_fohh_thread], jsonout, sizeof(jsonout), NULL, 1)) {
                ZDP("%s\n", jsonout);
            }
        }
    }

    return ZPATH_RESULT_NO_ERROR;
}
