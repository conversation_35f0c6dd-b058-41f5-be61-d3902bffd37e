/*
 * zpn_assistant_private.c : Apis to read from global assistant
 *
 */
#include <pthread.h>
#include "zpn_assistantd/zpn_assistant_private.h"
#include "zpn_assistantd/assistant_util.h"
#include "zpn_assistantd/assistant_monitor.h"
#include "zpn/assistant_log.h"
#include "zpn_assistantd/assistant_site.h"

extern struct zpn_firedrill_stats asst_firedrill_stats_obj;
extern struct argo_structure_description *zpn_broker_mission_critical_description_resp;

extern struct zpn_firedrill_stats asst_firedrill_stats_obj;
extern struct argo_structure_description *zpn_broker_mission_critical_description_resp;

int
zpn_assistant_global_assistant_get_quickack()
{
    int quickack;
    pthread_mutex_lock(&global_assistant.config_lock);
    quickack = global_assistant.quickack;
    pthread_mutex_unlock(&global_assistant.config_lock);
    ASSISTANT_DEBUG_DATA("quickack from zpn_assistant_group %ld %d", (long) global_assistant.gid,
                                 quickack);
    return quickack;
}

int
zpn_assistant_global_assistant_get_quickack_read()
{
    int quickack_read;
    pthread_mutex_lock(&global_assistant.config_lock);
    quickack_read = global_assistant.quickack_read;
    pthread_mutex_unlock(&global_assistant.config_lock);
    ASSISTANT_DEBUG_DATA("quickack read from zpn_assistant_group %ld %d", (long) global_assistant.gid,
                                 quickack_read);
    return quickack_read;
}

int
zpn_assistant_global_assistant_get_quickack_app()
{
    int quickack_app;
    pthread_mutex_lock(&global_assistant.config_lock);
    quickack_app = global_assistant.quickack_app;
    pthread_mutex_unlock(&global_assistant.config_lock);
    ASSISTANT_DEBUG_DATA("quickack app from zpn_assistant_group %ld %d", (long) global_assistant.gid,
                                 quickack_app);
    return quickack_app;
}

int
zpn_assistant_global_assistant_get_mem_usage()
{
    const struct zpn_assistant_system_stats *sys_stats = &global_assistant.sys_stats;
    ASSISTANT_DEBUG_DATA("Connector Memory Usage from zpn_assistant_group %d", sys_stats->system_mem_util);
    return sys_stats->system_mem_util;
}

int
zpn_assistant_global_assistant_get_firedrill_state()
{
    return global_assistant.firedrill_cfg.status;
}

void zpn_assistant_send_mission_critical_req(void *cookie1, void *cookie2)
{
    struct zpn_broker_mission_critical mc_data = {0};
    struct fohh_connection *f_conn = cookie1;

    mc_data.site_gid = global_assistant.site_gid;
    if(zpn_broker_mission_critical_req(f_conn, 0, &mc_data)) {
        ASSISTANT_LOG(AL_ERROR, "failed to send mission critical request");
        return;
    }
    ASSISTANT_LOG(AL_ERROR, "firedrill assistant sent mission critical message request successfully");
}

int zpn_assistant_mission_critical_request_cb(void* argo_cookie_ptr,
                                                void* argo_structure_cookie_ptr,
                                                struct argo_object* object)
{
    struct zpn_broker_mission_critical_resp *mc_resp = object->base_structure_void;
    struct zpn_assistant_site_config *asst_cfg = (struct zpn_assistant_site_config *)ASST_MALLOC(sizeof(struct zpn_assistant_site_config));


    ASSISTANT_LOG(AL_NOTICE,"zpn_assistant_mission_critical_request_cb firedrill entered");

    /* disconnect the amc connection */
    if (global_assistant.broker_mc) {
        fohh_connection_delete(global_assistant.broker_mc, FOHH_CLOSE_REASON_MISSION_ACCOMPLISHED);
    }

    /* check the firedrill status */
    if(mc_resp->firedrill_status) {

        ASSISTANT_LOG(AL_NOTICE,"firedrill enabled, trigger firedrill again...");
        global_assistant.firedrill_cfg.interval_s = mc_resp->firedrill_interval;
        /*status change will be done in start call */
        if(zpn_assistant_firedrill_start(mc_resp->firedrill_interval)) {
            ASST_FREE(asst_cfg);
            return ZPN_RESULT_ERR;
        }
        __sync_add_and_fetch(&asst_firedrill_stats_obj.firedrill_transit_count, 1);

    } else {
        ASSISTANT_LOG(AL_NOTICE,"zpn_sitec_mission_critical_request_cb firedrill disabled, connecting back to cloud");

        zpn_assistant_switch_to_cloud();

        ZPATH_RWLOCK_WRLOCK(&(global_assistant.ddil_lock), __FILE__, __LINE__);

        global_assistant.firedrill_cfg.status = ZPN_ASSISTANT_FIREDRILL_DISABLED;

        asst_cfg->fd_status = ZPN_ASSISTANT_FIREDRILL_DISABLED;
        asst_cfg->fd_interval_s = global_assistant.firedrill_cfg.interval_s;
        asst_cfg->fd_start_time = global_assistant.firedrill_cfg.start_time;
        global_assistant.firedrill_cfg.leftover_interval_s = 0;

        ZPATH_RWLOCK_UNLOCK(&(global_assistant.ddil_lock), __FILE__, __LINE__);

        assistant_update_firedrill_config_to_site_config_file(asst_cfg);

        /* increment disconnect count */
        __sync_add_and_fetch(&asst_firedrill_stats_obj.firedrill_completed_count, 1);
    }
    ASST_FREE(asst_cfg);
    return ZPN_RESULT_NO_ERROR;
}

static int assistant_broker_mission_critical_conn_cb(struct fohh_connection*   connection,
                                                        enum fohh_connection_state state,
                                                        void *cookie)
{
    if (state == fohh_connection_connected) {

        /* mc conn successful, del the timer */
        if(global_assistant.asst_mc_timer) {
            event_del(global_assistant.asst_mc_timer);
        }

        ASSISTANT_LOG(AL_NOTICE, "firedrill: assistant amc connection successfully connected to Zscaler Cloud: %s",
                                                                                        fohh_description(connection));
        const int res = argo_register_structure(fohh_argo_get_rx(connection),
                                                zpn_broker_mission_critical_description_resp,
                                                zpn_assistant_mission_critical_request_cb,
                                                connection);
        if (res) {
            ZPN_LOG(AL_ERROR, "firedrill: could not register broker_redirect for scmc connection %s",
                    fohh_description(connection));
        }

       zevent_defer(zpn_assistant_send_mission_critical_req, connection, NULL, 0);

    } else {
        ASSISTANT_LOG(AL_NOTICE, "firedrill: assistant amc connection to Zscaler Cloud closed: %s %s",
                                            fohh_description(connection), fohh_close_reason(connection));
    }
    return ZPN_RESULT_NO_ERROR;
}

static int
assistant_broker_mission_control_conn_unblock_cb(struct fohh_connection*      connection,
                                                    enum fohh_queue_element_type element_type,
                                                    void*                        cookie)
{
    ASSISTANT_LOG(AL_NOTICE, "fohh unblock on broker mission control connection");
    return FOHH_RESULT_NO_ERROR;
}

void zpn_assistant_mission_critical_conn_create(struct fohh_thread *thread, void *cookie, int64_t int_cookie)
{
    char        broker_name[1000];
    char*       cloud_name = assistant_state_get_cloud_name();
    char        ast_sni_customer_domain_amc[1000];
    struct timeval tv = {0};

    /* activate the timer for amc conn failure */
    tv.tv_sec = 60;
    tv.tv_usec = 0;
    if (event_add(global_assistant.asst_mc_timer, &tv)) {
        ZPN_LOG(AL_ERROR, "Could not activate status timer for amc conn");
        return;
    }

    snprintf(ast_sni_customer_domain_amc, sizeof(ast_sni_customer_domain_amc), "%"PRId64".amc.%s", global_assistant.gid, cloud_name);

    ASSISTANT_LOG(AL_INFO, "SNI for amc connection(%s)", ast_sni_customer_domain_amc);

    snprintf(broker_name, sizeof(broker_name), "any.co2br.%s", cloud_name);

    /* connect to broker on scmc channel */
    global_assistant.broker_mc = fohh_client_create(FOHH_WORKER_ZPN_MC,
                                                NULL,
                                                argo_serialize_binary,
                                                fohh_connection_style_argo,
                                                0,
                                                &global_assistant,
                                                assistant_broker_mission_critical_conn_cb,
                                                NULL,
                                                assistant_broker_mission_control_conn_unblock_cb,
                                                NULL,
                                                broker_name,
                                                ast_sni_customer_domain_amc,
                                                cloud_name,
                                                htons(ZPN_ASSISTANT_BROKER_PORT),
                                                global_assistant.assistant_to_public_cloud_ctx,
                                                1,
                                                ZPN_ASSISTANT_BROKER_RX_TIMEOUT_S);
    if (!global_assistant.broker_mc) {
        ASSISTANT_LOG(AL_ERROR, "Could not initialize assistant mission control connection");
        return;
    }
    return;
}

void assistant_update_firedrill_config_to_site_config_file(struct zpn_assistant_site_config *asst_cfg)
{
    assistant_site_read_config_with_lock();

    /* this is for filling the fields other than firedrill */
    zpn_assistant_site_fill_cfg_obj(asst_cfg);

    assistant_site_store_config_with_lock(asst_cfg);

    ASSISTANT_LOG(AL_INFO,"firedrill config file update completed");
}

void  zpn_assistant_site_fill_cfg_obj(struct zpn_assistant_site_config *asst_cfg)
{

    ZPATH_RWLOCK_RDLOCK(&(global_assistant.ddil_lock), __FILE__, __LINE__);

    asst_cfg->reenroll_period =  global_assistant.reenroll_period;
    asst_cfg->site_is_active = global_assistant.site_is_active;
    asst_cfg->sitec_preferred = global_assistant.sitec_preferred;
    asst_cfg->max_allowed_downtime_s =  global_assistant.max_allowed_downtime_s;
    asst_cfg->offline_domain = global_assistant.site_offline_domain;

    ZPATH_RWLOCK_UNLOCK(&(global_assistant.ddil_lock), __FILE__, __LINE__);
}
