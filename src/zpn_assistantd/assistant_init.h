/*
 * assistant_init.h. Copyright (C) 2014-2020 Zscaler Inc, All Rights Reserved
 */

#ifndef _ASSISTANT_INIT_H_
#define _ASSISTANT_INIT_H_

struct zpn_assistant_state *assistant_init(int64_t      assistant_gid,
                                           char*        customer_domain,
                                           const char*  cloud_name,
                                           const char*  broker,
                                           const char*  asst_name,
                                           const char*  cert_file_name,
                                           int          auto_upgrade_disabled,
                                           int          stats_log_to_disk,
                                           int          enroll_version,
                                           int          ut_mode,
                                           SSL_CTX      *public_cloud_ctx,
                                           SSL_CTX      *private_cloud_ctx,
                                           SSL_CTX      *public_cloud_ctx_dtls,
                                           SSL_CTX      *private_cloud_ctx_dtls,
                                           int          health_tx_prefer_local_cache_over_dsp_cache,
                                           int          slow_asst_init,
                                           int          freebsd_compat_mode,
										   int 			disable_client_version_check);


int dump_dr_config(int force_dump_config, struct zthread_info *z_info);

#endif //_ASSISTANT_INIT_H_
