# This is an /etc/sudoers.d/ entry

zscaler ALL=(ALL) !ALL

# Allow zscaler user to execute wg as root
zscaler ALL=NOPASSWD: /opt/zscaler/bin/wg

# Allow zscaler user to execute wg-quick as root
zscaler ALL=NOPASSWD: /opt/zscaler/bin/wg-quick

# Allow zscaler user to execute /sbin/ip as root
zscaler ALL=NOPASSWD: /sbin/ip

# Allow zscaler user to execute vtysh as root
zscaler ALL=NOPASSWD: /bin/vtysh

# Allow zscaler user to execute systemctl as root
zscaler ALL=NOPASSWD: /bin/systemctl *

# Allow zscaler user to execute journalctl as root
zscaler ALL=NOPASSWD: /bin/journalctl *

# Allow zscaler user to execute frr-reload.py as root
zscaler ALL=NOPASSWD: /usr/lib/frr/frr-reload.py
