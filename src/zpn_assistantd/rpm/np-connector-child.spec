Name:           np-connector-child
Version:        %{_version}
Release:        1%{?dist}
Summary:        Zscaler Virtual Private Network Connector
Group:          Applications/Internet
License:        license.txt
Vendor:         Zscaler
URL:            https://www.zscaler.com
Source:         %{name}-%{version}.tar
Prefix:         /opt/zscaler
Requires(pre):  shadow-utils
Requires(pre):  frr >= 10.2
%{?systemd_requires}
BuildRequires:  systemd

%global debug_package %{nil}
%global __debug_install_post /bin/true
%global __os_install_post %{nil}

%description
np-connector-child is the connector for the Zscaler Virtual Private Network service.
%prep

%setup -q

%build

%pre
getent group zscaler >/dev/null || groupadd -r zscaler
getent passwd zscaler >/dev/null || \
    useradd -r -g zscaler -d /opt/zscaler -s /sbin/nologin \
    -c "Zscaler Service Account" zscaler
exit 0

%define _build_id_links none

%install
rm -rf $RPM_BUILD_ROOT
%make_install
mkdir -p $RPM_BUILD_ROOT%{_unitdir}
mkdir -p $RPM_BUILD_ROOT%{_unitdir}/../system-preset
install -p -D -m 644 53-zscaler.preset $RPM_BUILD_ROOT%{_unitdir}/../system-preset/
install -p -D -m 644 %{name}.service $RPM_BUILD_ROOT%{_unitdir}/
install -d -D -m 700 $RPM_BUILD_ROOT%{prefix}/var
install -p -D -m 644 %{name}.conf $RPM_BUILD_ROOT%{prefix}/etc/%{name}.conf
%if 0%{?_sanitized}
    install -d -m 755 $RPM_BUILD_ROOT%{prefix}/report
    install -p -D -m 644 asan.options $RPM_BUILD_ROOT%{prefix}/etc/asan.options
%endif

%clean
[ "$RPM_BUILD_ROOT" != "/" ] && rm -rf $RPM_BUILD_ROOT

%files
%defattr(-,zscaler,zscaler)
%{prefix}/bin/*
%{prefix}/share/*
%{_unitdir}/*
%{_unitdir}/../system-preset/*
%attr(644, root, root) %{_unitdir}/../system-preset/53-zscaler.preset
%attr(644, root, root) %{_unitdir}/%{name}.service
%dir %{prefix}/var
%config(noreplace) %{prefix}/etc/%{name}.conf
%if 0%{?_sanitized}
    %dir %{prefix}/report
    %config(noreplace) %{prefix}/etc/asan.options
%endif
%attr(700, zscaler, zscaler) %{prefix}/etc/wireguard
%attr(440, root, root) /etc/sudoers.d/15-zscaler-user

%doc

%post
%systemd_post %{name}.service
%if 0%{?_sanitized}
    setcap cap_sys_ptrace,cap_net_raw,cap_net_admin,cap_net_bind_service,cap_sys_boot,cap_sys_nice,cap_sys_time=ep %{prefix}/bin/np-connector-child
%else
    setcap cap_net_raw,cap_net_admin,cap_net_bind_service,cap_sys_boot,cap_sys_nice,cap_sys_time=ep %{prefix}/bin/np-connector-child
%endif
# Add the 'zscaler' user to the 'frrvty' group. This is required to grant
# permissions for the connector to access FRR's VTY sockets for management.
/usr/sbin/usermod -aG frrvty zscaler

# Set ownership and permissions on the FRR directory.
/usr/bin/chown frr:frrvty /etc/frr
/usr/bin/chown frr:frrvty /etc/frr/frr.conf
/usr/bin/chmod 770 /etc/frr
/usr/bin/chmod 660 /etc/frr/frr.conf

%preun
if [ "$1" -eq 0 ]; then
    # Only remove the file on full uninstall, not during upgrade
    rm -f /etc/sudoers.d/15-zscaler-user
fi
%systemd_preun %{name}.service

%postun
%systemd_postun %{name}.service

%changelog
