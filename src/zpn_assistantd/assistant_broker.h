/*
 * assistant_broker.h. Copyright (C) 2020 Zscaler Inc. All Rights Reserved.
 */

#ifndef _ASSISTANT_BROKER_H_
#define _ASSISTANT_BROKER_H_

typedef int (*assistant_broker_tx_broker_request_ack_fp)(void*, struct zpn_broker_request_ack*);
typedef int (*assistant_broker_tx_dns_check_fp)(void*, struct argo_object*);
int64_t
assistant_broker_register(void*                                     cookie,
                          assistant_broker_tx_broker_request_ack_fp tx_broker_request_ack,
                          assistant_broker_tx_dns_check_fp          tx_dns_check,
                          const char*                               function_of_caller,
                          int                                       line_number_of_caller);
void
assistant_broker_unregister(int64_t label);
int
assistant_broker_tx_broker_request_ack(int64_t                        label,
                                       struct zpn_broker_request_ack* tx_buffer);
int
assistant_broker_tx_dns_check(int64_t             label,
                              struct argo_object* object);

int
assistant_broker_init();
#endif // _ASSISTANT_BROKER_H_
