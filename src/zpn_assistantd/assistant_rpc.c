/*
 * assistant_rpc.c. Copyright (C) 2020 Zscaler Inc. All Rights Reserved.
 *
 * Book keeping of all the RPC messages goes here. The RPC is spread across the system(will be rx/tx over data/control),
 * so no attempt to bring it all under here will be made - only book keeping.
 */
#include <arpa/inet.h>
#include "argo/argo_hash.h"
#include "zhw/zhw_os.h"
#include "zvm/zvm.h"
#include "zpath_lib/zpath_system.h"
#include "zpath_misc/zpath_version.h"
#include "zpath_misc/zsysinfo.h"
#include "zpn_assistantd/zpn_assistant_health.h"
#include "zpn_assistantd/zpn_assistant_mtunnel.h"
#include "zpn_assistantd/assistant_state.h"
#include "zpn/assistant_log.h"
#include "zpn/zpn_system.h"
#include "zpn_assistantd/assistant_rpc.h"
#include "zpn_assistantd/assistant_log_tx.h"
#include "zpn_assistantd/assistant_dns.h"
#include "zpn_assistantd/assistant_data.h"
#include "zpn_assistantd/zpn_assistant_private.h"
#include "zpn/zpn_rpc.h"
#include "zpath_lib/zpath_system_stats.h"
#include "np_connector/np_connector.h"

static
struct {
    int64_t     total_mtunnel_count;
} prev_status_report;

static
struct zpn_assistant_interface_stats {
    int64_t intf_rb;
    int64_t intf_rp;
    int64_t intf_re;
    int64_t intf_rd;
    int64_t intf_tb;
    int64_t intf_tp;
    int64_t intf_te;
    int64_t intf_td;
} prev_intf_stats;

static struct zroute default_route = {0};
/*
 * fill the status report in the given, rpc object
 */
void
assistant_rpc_status_report_fill(struct zpn_assistant_status_report* data)
{
    struct zinterfaces interfaces[MAX_INTERFACES];
    int interface_count;

    struct sockaddr_storage resolver[ZCDNS_MAX_RESOLVERS];
    socklen_t resolver_len[ZCDNS_MAX_RESOLVERS];
    int resolver_count = 0;
    int i;
    int64_t rb = 0, rp = 0, re = 0, rd = 0, tb = 0, tp = 0, te = 0, td = 0;
    struct zpn_assistant_system_stats *sys_stats = &global_assistant.sys_stats;

    memset(interfaces, 0, sizeof(struct zinterfaces)*MAX_INTERFACES);
    memset(&default_route, 0, sizeof(struct zroute));
    memset(resolver, 0, sizeof(struct sockaddr_storage)*ZCDNS_MAX_RESOLVERS);
    memset(resolver_len, 0, sizeof(socklen_t)*ZCDNS_MAX_RESOLVERS);
    memset(data, 0, sizeof(*data));

    data->g_ast = global_assistant.gid;

    /* Get sysinfo */
    if (0 == zpath_system_get_uptime_s(&global_assistant.sys_uptime_s)) {
        /*
         * This becomes inaccurate when system clock changes after the start and now. This is ok as there is no
         * functional impact.
         */
        data->sys_uptime_s = epoch_s() - global_assistant.sys_uptime_s;
    } else {
        ASSISTANT_LOG(AL_INFO, "Could not get the system uptime info");
        data->sys_uptime_s = 0;
    }

    data->cpu_util = sys_stats->cpu_util;
    data->mem_util = sys_stats->system_mem_util;

    ASSISTANT_DEBUG_RPC("cpu_util = %d, mem_util = %d", data->cpu_util, data->mem_util);

    /* Get interfaces */
    if (get_interfaces(interfaces, &interface_count) == ZSYSINFO_SUCCESS) {
        ASSISTANT_DEBUG_RPC("Interface count = %d", interface_count);
        for (i = 0; i < interface_count; i++) {
            struct sockaddr_in *addr_in = (struct sockaddr_in *)&(interfaces[i].addr);

            rb += interfaces[i].rb;
            rp += interfaces[i].rp;
            re += interfaces[i].re;
            rd += interfaces[i].rd;
            tb += interfaces[i].tb;
            tp += interfaces[i].tp;
            te += interfaces[i].te;
            td += interfaces[i].td;

            ASSISTANT_DEBUG_RPC("  - %s: %s, rb=%ld, rp=%ld, re=%ld, rd=%ld, tb=%ld, tp=%ld, te=%ld, td=%ld",
                                    interfaces[i].name, inet_ntoa(addr_in->sin_addr),
                                    (long)interfaces[i].rb, (long)interfaces[i].rp, (long)interfaces[i].re, (long)interfaces[i].rd,
                                    (long)interfaces[i].tb, (long)interfaces[i].tp, (long)interfaces[i].te, (long)interfaces[i].td);
        }
    }

    /* Get default route */
    if (get_default_route(&default_route) == ZSYSINFO_SUCCESS) {
        struct sockaddr_in *gw_addr_in = (struct sockaddr_in *)&(default_route.gw);
        struct argo_inet inet;
        uint16_t port_ne;

        argo_sockaddr_to_inet((struct sockaddr *)&default_route.gw, &inet, &port_ne);

        ASSISTANT_DEBUG_RPC("Default route: interface = %s, gateway = %s", default_route.intf_name, inet_ntoa(gw_addr_in->sin_addr));
        data->dft_rt_intf = default_route.intf_name;
        data->dft_rt_gw = inet;
    }

    /* Get resolver */
    if (zah_dns) {
        zcdns_get_resolvers(zah_dns, resolver, resolver_len, &resolver_count);

        for (i = 0; i < resolver_count; i++) {
            char str[ARGO_INET_ADDRSTRLEN];
            struct sockaddr_storage *ss = &resolver[i];
            struct argo_inet inet;
            uint16_t port_ne;

            argo_sockaddr_to_inet((struct sockaddr *)ss, &inet, &port_ne);

            ASSISTANT_DEBUG_RPC("Resolver %d: addr = %s, port = %d", i+1, argo_inet_generate(str, &inet), ntohs(port_ne));
            if (i == 0) {
                data->resolver = inet;
            }
        }
    }

    data->service_count = global_assistant.service_count;
    data->current_target_count = global_assistant.target_count;
    data->delta_mtunnel_count = global_assistant.num_mtunnels - prev_status_report.total_mtunnel_count;
    prev_status_report.total_mtunnel_count = global_assistant.num_mtunnels;
    data->total_mtunnel_count = global_assistant.num_mtunnels;
    data->last_os_upgrade_time = global_assistant.last_os_upgrade_time;
    data->last_sarge_upgrade_time = global_assistant.last_sarge_upgrade_time;
    data->platform_version = global_assistant.platform_version;

    data->intf_count = interface_count;
    data->intf_rb = rb;
    data->intf_rp = rp;
    data->intf_re = re;
    data->intf_rd = rd;
    data->intf_tb = tb;
    data->intf_tp = tp;
    data->intf_te = te;
    data->intf_td = td;

    data->delta_intf_rb = rb - prev_intf_stats.intf_rb;
    data->delta_intf_rp = rp - prev_intf_stats.intf_rp;
    data->delta_intf_re = re - prev_intf_stats.intf_re;
    data->delta_intf_rd = rd - prev_intf_stats.intf_rd;
    data->delta_intf_tb = tb - prev_intf_stats.intf_tb;
    data->delta_intf_tp = tp - prev_intf_stats.intf_tp;
    data->delta_intf_te = te - prev_intf_stats.intf_te;
    data->delta_intf_td = td - prev_intf_stats.intf_td;
    data->total_intf_b = rb + tb;
    data->total_intf_p = rp + tp;
    data->total_intf_e = re + te;
    data->total_intf_d = rd + td;
    data->delta_total_intf_b = rb + tb - (prev_intf_stats.intf_rb + prev_intf_stats.intf_tb);
    data->delta_total_intf_p = rp + tp - (prev_intf_stats.intf_rp + prev_intf_stats.intf_tp);
    data->delta_total_intf_e = re + te - (prev_intf_stats.intf_re + prev_intf_stats.intf_te);
    data->delta_total_intf_d = rd + td - (prev_intf_stats.intf_rd + prev_intf_stats.intf_td);

    prev_intf_stats.intf_rb = rb;
    prev_intf_stats.intf_rp = rp;
    prev_intf_stats.intf_re = re;
    prev_intf_stats.intf_rd = rd;
    prev_intf_stats.intf_tb = tb;
    prev_intf_stats.intf_tp = tp;
    prev_intf_stats.intf_te = te;
    prev_intf_stats.intf_td = td;

    data->platform = zhw_platform();
    data->platform_detail = zvm_vm_type_to_str_concise(zvm_type_get());
    data->platform_arch = zhw_platform_arch();
    data->runtime_os = g_asst_runtime_os;

    ASSISTANT_DEBUG_RPC("Platform = %s, Platform detail = %s, Run-time OS = %s",
                        data->platform, data->platform_detail, data->runtime_os);

    if (!global_assistant.start_s) {
        global_assistant.start_s = (uint32_t)epoch_s();
    }

    data->ast_uptime_s = global_assistant.start_s;

    data->time_delta_us = assistant_state_get_time_lag_us();

    snprintf(data->log_broker_cn, sizeof(data->log_broker_cn), "%s", assistant_log_tx_get_logging_server_cn());
    data->udp4_port_util = zpath_system_get_udpv4_socket_util();
    data->udp6_port_util = zpath_system_get_udpv6_socket_util();
    data->tcp4_port_util = zpath_system_get_tcpv4_socket_util();
    data->tcp6_port_util = zpath_system_get_tcpv6_socket_util();
    data->sys_fd_util = zpath_system_get_system_fd_util();
    data->proc_fd_util = zpath_system_get_process_fd_util();

    ASSISTANT_DEBUG_RPC("System uptime = %d, current epoch = %d, start epoch = %d, ast uptime = %d",
                            global_assistant.sys_uptime_s, (uint32_t)epoch_s(), global_assistant.start_s, (uint32_t)epoch_s() - global_assistant.start_s);

    /*
     * don't send 0 as that broker can't differentiate if this is coming from a connector without this capability or
     * a real value of 0. So let the minimum be 1%
     */
    if (0 == data->cpu_util) {
        data->cpu_util = 1;
    }
    if (0 == data->mem_util) {
        data->mem_util = 1;
    }
    if (0 == data->udp4_port_util) {
        data->udp4_port_util = 1;
    }
    if (0 == data->udp6_port_util) {
        data->udp6_port_util = 1;
    }
    if (0 == data->sys_fd_util) {
        data->sys_fd_util = 1;
    }
    if (0 == data->proc_fd_util) {
        data->proc_fd_util = 1;
    }

    /* fill in NP datas */
    if (global_assistant.cur_mode == np_connector) {
        data->connector_type = np_connector;
        if (np_connector_get_state()) {
            //only report ttl if its online state
            data->np_state = online;
        } else {
            data->np_state = offline;
        }

        if (!global_assistant.np_connector_gid) {
            global_assistant.np_connector_gid = np_connector_get_connector_gid();
        }
        data->np_connector_gid = global_assistant.np_connector_gid;
        data->frr_version = np_connector_get_frr_version();

    } else {
        data->connector_type = app_connector;
    }
}

void
assistant_rpc_log_status()
{
    ZPN_LOG(AL_NOTICE, "RPC Messages, RX: BrkRq[Dsp %ld UBrk %ld PBrk %ld], BindAck %ld, AppRtDisc %ld, DnsChk[Dsp %ld Pbrk %ld], "
                       "MtunnelEnd %ld TagPause %ld TagResume %ld WinUpdate %ld Control Redirect %ld Config Redirect %ld Stats Redirect %ld"
                       " Log redirect %ld Override redirect %ld "
                       "TX|TX_DROP: BrkRqAck[Dsp %ld|%ld, UBrk %ld|%ld, Pbrk %ld|%ld], "
                       "BindReq %ld|%ld, AppRtReq %ld|%ld, "
                       "HealthRp %ld|%ld DnsChk[Dsp %ld|%ld PBrk %ld|%ld NoR %ld], "
                       "ComprehensiveStats %ld|%ld",
            (long) global_assistant.num_brk_req_from_dsp,
            (long) global_assistant.num_brk_req_from_ubrk,
            (long) global_assistant.num_brk_req_from_pbrk,
            (long) global_assistant.num_bind_ack,
            (long) global_assistant.num_app_route_disc,
            (long) global_assistant.num_dns_asst_check_rx_from_dsp,
            (long) global_assistant.num_dns_asst_check_rx_from_pbrk,
            (long) global_assistant.num_mtunnel_end,
            (long) global_assistant.num_tag_pause,
            (long) global_assistant.num_tag_resume,
            (long) global_assistant.num_window_update,
            (long) global_assistant.num_ctrl_redirect,
            (long) global_assistant.num_cfg_redirect,
            (long) global_assistant.num_stats_redirect,
            (long) global_assistant.num_log_redirect,
            (long) global_assistant.num_ovd_redirect,
            (long) global_assistant.num_brk_req_ack_to_dsp,
            (long) global_assistant.num_brk_req_ack_to_dsp_fails,
            (long) global_assistant.num_brk_req_ack_to_ubrk,
            (long) global_assistant.num_brk_req_ack_to_ubrk_fails,
            (long) global_assistant.num_brk_req_ack_to_pbrk,
            (long) global_assistant.num_brk_req_ack_to_pbrk_fails,
            (long) global_assistant.num_bind,
            (long) global_assistant.num_bind_fails,
            (long) global_assistant.num_app_route_reg,
            (long) global_assistant.num_app_route_reg_send_fails,
            (long) global_assistant.num_health_report,
            (long) global_assistant.num_health_report_send_fails,
            (long) global_assistant.num_dns_asst_check_tx_to_dsp,
            (long) global_assistant.num_dns_asst_check_tx_to_dsp_fails,
            (long) global_assistant.num_dns_asst_check_tx_to_pbrk,
            (long) global_assistant.num_dns_asst_check_tx_to_pbrk_fails,
            (long) global_assistant.num_dns_asst_check_tx_fail_no_result,
            (long) global_assistant.num_comprehensive_stats_upload,
            (long) global_assistant.num_comprehensive_stats_upload_fails);
}

int
assistant_rpc_stats_fill(void*     cookie,
                         int       counter,
                         void*     structure_data)
{
    struct zpn_assistant_rpc_stats*    out_data;

    (void)cookie;

    out_data = (struct zpn_assistant_rpc_stats*)structure_data;
    out_data->cloud_time_us = assistant_state_get_current_time_cloud_us();
    out_data->rx_brk_req_from_dsp = global_assistant.num_brk_req_from_dsp;
    out_data->rx_brk_req_from_ubrk = global_assistant.num_brk_req_from_ubrk;
    out_data->rx_brk_req_from_pbrk = global_assistant.num_brk_req_from_pbrk;
    out_data->rx_bind_ack = global_assistant.num_bind_ack;
    out_data->rx_route_disc = global_assistant.num_app_route_disc;
    out_data->rx_dns_check_from_dsp = global_assistant.num_dns_asst_check_rx_from_dsp;
    out_data->rx_dns_check_from_pbrk = global_assistant.num_dns_asst_check_rx_from_pbrk;
    out_data->rx_mtunnel_end = global_assistant.num_mtunnel_end;
    out_data->rx_tag_pause = global_assistant.num_tag_pause;
    out_data->rx_tag_resume = global_assistant.num_tag_resume;
    out_data->rx_win_update = global_assistant.num_window_update;
    out_data->rx_redirect = global_assistant.num_redirect;
    out_data->tx_brk_req_ack_to_dsp = global_assistant.num_brk_req_ack_to_dsp;
    out_data->tx_brk_req_ack_to_dsp_fail = global_assistant.num_brk_req_ack_to_dsp_fails;
    out_data->tx_brk_req_ack_to_ubrk = global_assistant.num_brk_req_ack_to_ubrk;
    out_data->tx_brk_req_ack_to_ubrk_fail = global_assistant.num_brk_req_ack_to_ubrk_fails;
    out_data->tx_brk_req_ack_to_pbrk = global_assistant.num_brk_req_ack_to_pbrk;
    out_data->tx_brk_req_ack_to_pbrk_fail = global_assistant.num_brk_req_ack_to_pbrk_fails;
    out_data->tx_bind_req = global_assistant.num_bind;
    out_data->tx_bind_req_fail = global_assistant.num_bind_fails;
    out_data->tx_route_reg = global_assistant.num_app_route_reg;
    out_data->tx_route_reg_fail = global_assistant.num_app_route_reg_send_fails;
    out_data->tx_health_rpt = global_assistant.num_health_report;
    out_data->tx_health_rpt_fail = global_assistant.num_health_report_send_fails;
    out_data->tx_dns_check_to_dsp = global_assistant.num_dns_asst_check_tx_to_dsp;
    out_data->tx_dns_check_to_dsp_fail = global_assistant.num_dns_asst_check_tx_to_dsp_fails;
    out_data->tx_dns_check_to_pbrk = global_assistant.num_dns_asst_check_tx_to_pbrk;
    out_data->tx_dns_check_to_pbrk_fail = global_assistant.num_dns_asst_check_tx_to_pbrk_fails;
    out_data->tx_dns_check_no_result = global_assistant.num_dns_asst_check_tx_fail_no_result;
    out_data->tx_comprehensive_stats = global_assistant.num_comprehensive_stats_upload;
    out_data->tx_comprehensive_stats_fail = global_assistant.num_comprehensive_stats_upload_fails;
    out_data->rx_ctrl_redirect = global_assistant.num_ctrl_redirect;
    out_data->rx_cfg_redirect = global_assistant.num_cfg_redirect;
    out_data->rx_ovd_redirect = global_assistant.num_ovd_redirect;
    out_data->rx_stats_redirect = global_assistant.num_stats_redirect;
    out_data->rx_log_redirect = global_assistant.num_log_redirect;

    return ZPATH_RESULT_NO_ERROR;
}
