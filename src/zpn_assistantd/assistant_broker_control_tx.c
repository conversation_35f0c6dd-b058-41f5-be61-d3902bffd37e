/*
 * assistant_broker_control_tx.c. Copyright (C) 2019 Zscaler Inc. All Rights Reserved.
 *
 * TX operation on the broker control channels.
 */
#include "zpath_lib/zpath_debug.h"
#include "zpn_assistantd/zpn_assistant_private.h"
#include "zpn_assistantd/assistant_monitor.h"
#include "zpn/assistant_log.h"
#include "zpn_assistantd/assistant_broker_control_tx.h"
#include "zpn_assistantd/assistant_assert.h"
#include "admin_probe/admin_probe_rpc.h"
#include "zpn_assistantd/assistant_additional_debug_logs.h"

static struct argo_structure_description* assistant_broker_control_tx_stats_description;
static struct argo_structure_description* assistant_broker_control_tx_fohh_stats_description;

static
struct assistant_broker_control_tx_stats {                                      /* _ARGO: object_definition */
    uint64_t route_registaration;                                               /* _ARGO: integer */
    uint64_t target_health;                                                     /* _ARGO: integer */
    uint64_t status;                                                            /* _ARGO: integer */
    uint64_t state;                                                             /* _ARGO: integer */
    uint64_t stats_control;                                                     /* _ARGO: integer */
    uint64_t log_control;                                                       /* _ARGO: integer */
    uint64_t log;                                                               /* _ARGO: integer */
    uint64_t probe_status;                                                      /* _ARGO: integer */
}stats;

struct assistant_broker_control_tx_fohh_stats {                                 /* _ARGO: object_definition */
    uint64_t place_holder;                                                      /* _ARGO: integer */
};
#include "zpn_assistantd/assistant_broker_control_tx_compiled_c.h"
#include "zpn_assistantd/assistant_state.h"

static struct assistant_broker_control_tx_fohh_stats fohh_stats[FOHH_MAX_THREADS];

static int
assistant_broker_control_tx_dump(struct zpath_debug_state*   request_state,
                                 const char**                query_values,
                                 int                         query_value_count,
                                 void*                       cookie);


void
assistant_broker_control_tx_app_route_registration(struct zpn_app_route_registration* rep,
                                                   assistant_control_tx_done_cb       done_cb,
                                                   void*                              done_cb_void_cookie,
                                                   char*                              done_cb_str_cookie)
{

    int         res;

    res = zpn_send_zpn_app_route_registration_struct(global_assistant.broker_control, 0, rep);
    if (done_cb) done_cb(done_cb_void_cookie, done_cb_str_cookie, res);
    stats.route_registaration++;
}


void
assistant_broker_control_tx_target_health(struct zpn_health_report*    rep,
                                          assistant_control_tx_done_cb done_cb,
                                          void*                        done_cb_void_cookie,
                                          char*                        done_cb_str_cookie)
{

    int         res;

    res = zpn_send_zpn_health_report_struct(global_assistant.broker_control, 0, rep);
    if (done_cb) done_cb(done_cb_void_cookie, done_cb_str_cookie, res);
    stats.target_health++;
}




void
assistant_broker_control_tx_status(struct zpn_assistant_status_report* rep,
                                   assistant_control_tx_done_cb        done_cb,
                                   void*                               done_cb_void_cookie,
                                   char*                               done_cb_str_cookie)
{
    zpn_send_zpn_assistant_status_report(global_assistant.broker_control,
                                         fohh_connection_incarnation(global_assistant.broker_control), rep);
    stats.status++;
}


void
assistant_broker_control_tx_state()
{
    assistant_state_send_zpn_asst_state_on_fohh(global_assistant.broker_control,
                                                fohh_connection_incarnation(global_assistant.broker_control));
    stats.state++;
}


void
assistant_broker_control_tx_stats_control()
{
    zpn_send_zpn_assistant_stats_control(global_assistant.broker_control,
                                         fohh_connection_incarnation(global_assistant.broker_control),
                                         global_assistant.gid,
                                         global_assistant.stats_upload_enabled);
    stats.stats_control++;
}

void
assistant_broker_control_tx_enc_pvt_key(struct zpn_assistant_pvt_key_control *key_info)
{
    zpn_send_zpn_assistant_enc_pvt_key(global_assistant.broker_control,
                                         fohh_connection_incarnation(global_assistant.broker_control),
                                         key_info);
}

void
assistant_broker_control_tx_cert_req(struct zpn_assistant_gen_cert_control  *cert_info)
{
    zpn_send_zpn_assistant_cert_req(global_assistant.broker_control,
                                    fohh_connection_incarnation(global_assistant.broker_control),
                                    cert_info);
}

void
assistant_broker_control_tx_log_control()
{
    zpn_send_zpn_assistant_log_control(global_assistant.broker_control,
                                       fohh_connection_incarnation(global_assistant.broker_control),
                                       global_assistant.gid,
                                       0,
                                       global_assistant.log_upload_enabled,
                                       assistant_debug_log);
    stats.log_control++;
}

int
assistant_broker_control_tx_command_probe_status(void* status,
                                                 assistant_control_tx_done_cb     done_cb,
                                                 void*                            done_cb_void_cookie,
                                                 char*                            done_cb_str_cookie,
                                                 int is_np_command_probe)
{
    int res;

    if (!global_assistant.broker_control) {
        ASSISTANT_LOG(AL_DEBUG, "assistant control connection is not there, can not proceed sending command_probe_status report");
        return ZPN_RESULT_ERR;
    }
    if (is_np_command_probe) {
        res = admin_probe_send_np_command_probe_status(global_assistant.broker_control,
                                                        fohh_connection_incarnation(global_assistant.broker_control), (struct np_command_probe_status *)status);
    } else {
        res = admin_probe_send_zpn_command_probe_status(global_assistant.broker_control,
                                                        fohh_connection_incarnation(global_assistant.broker_control), (struct zpn_command_probe_status *)status);
    }
    if (res) {
        return res;
    }
    __sync_fetch_and_add_8(&(stats.probe_status), 1);

    return ZPN_RESULT_NO_ERROR;
}


static int
assistant_broker_control_tx_dump(struct zpath_debug_state*   request_state,
                                 const char**                query_values,
                                 int                         query_value_count,
                                 void*                       cookie)
{
    char        jsonout[10000];

    if (ARGO_RESULT_NO_ERROR ==
        argo_structure_dump(assistant_broker_control_tx_stats_description, &stats, jsonout, sizeof(jsonout), NULL,
                            1)){
        ZDP("%s\n", jsonout);
    }

    {
        int curr_fohh_thread;
        int max_fohh_thread = fohh_thread_count();
        for (curr_fohh_thread = 0; curr_fohh_thread < max_fohh_thread; curr_fohh_thread++) {
            if (ARGO_RESULT_NO_ERROR == argo_structure_dump(assistant_broker_control_tx_fohh_stats_description,
                                &fohh_stats[curr_fohh_thread], jsonout, sizeof(jsonout), NULL, 1)) {
                ZDP("%s\n", jsonout);
            }
        }
    }

    return ZPATH_RESULT_NO_ERROR;
}


int
assistant_broker_control_tx_init()
{
    int result;

    if (!(assistant_broker_control_tx_stats_description =
                  argo_register_global_structure(ASSISTANT_BROKER_CONTROL_TX_STATS_HELPER))) {
        return ZPATH_RESULT_ERR;
    }

    if (!(assistant_broker_control_tx_fohh_stats_description =
                  argo_register_global_structure(ASSISTANT_BROKER_CONTROL_TX_FOHH_STATS_HELPER))) {
        return ZPATH_RESULT_ERR;
    }

    result = zpath_debug_add_read_command("Dump info related to broker control TX operation.",
                                     "/assistant/broker/control/tx/stats/dump", assistant_broker_control_tx_dump, NULL,
                                     NULL);
    if (result) {
        ASSISTANT_LOG(AL_ERROR, "couldn't add /assistant/broker/control/tx/stats/dump");
        return result;
    }

    return result;
}


int
assistant_broker_control_tx_broker_request_ack(void*                           cookie,
                                               struct zpn_broker_request_ack*  ack)
{
    int                     res;

    res = ZPN_RESULT_ERR;

    if (NULL == ack) {
        ASSISTANT_ASSERT_SOFT(0, "ack buffer is invalid");
        goto done;
    }

    res = zpn_send_zpn_broker_request_ack_struct(global_assistant.broker_control, 0, ack);

    done:
    if (ZPN_RESULT_NO_ERROR == res) {
        ASSISTANT_LOG(AL_NOTICE, "%s, domain:%s port:%d ip_protocol:%s: sent broker request ack(%s) to control broker(%s)", ack->mtunnel_id,
                      ack->domain, ack->s_port, get_assistant_ip_proto_string(ack->ip_protocol), ack->error, fohh_description(global_assistant.broker_control));
        __sync_add_and_fetch_8(&global_assistant.num_brk_req_ack_to_dsp, 1);
    } else {
        if (ack) {
            ASSISTANT_LOG(AL_NOTICE, "%s, domain:%s port:%d ip_protocol:%s: Could not send broker request ack(%s) to control broker(%s)", ack->mtunnel_id,
                                     ack->domain, ack->s_port, get_assistant_ip_proto_string(ack->ip_protocol), ack->error, fohh_description(global_assistant.broker_control));
        }
        __sync_add_and_fetch_8(&global_assistant.num_brk_req_ack_to_dsp_fails, 1);
    }
    return res;
}


int
assistant_broker_control_tx_dns_check(void*               cookie,
                                      struct argo_object* object)
{
    int                             res;
    struct zpn_dns_assistant_check* req;
    char dump[8000];

    res = ZPN_RESULT_ERR;

    if (NULL == object) {
        ASSISTANT_ASSERT_SOFT(0, "DNS object is invalid when trying to send out of broker control connection");
        goto done;
    }

    req = object->base_structure_void;

    if (argo_object_dump(object, dump, sizeof(dump), NULL, 0) == ARGO_RESULT_NO_ERROR) {
        ASSISTANT_DEBUG_CONTROL_TX("%s: Tx to: %s", dump, fohh_peer_cn(global_assistant.broker_control));
    }

    res = fohh_argo_serialize_object(global_assistant.broker_control, object, 0, fohh_queue_element_type_control);

done:
    if (ZPN_RESULT_NO_ERROR == res) {
        __sync_add_and_fetch_8(&global_assistant.num_dns_asst_check_tx_to_dsp, 1);
        ASSISTANT_DEBUG_CONTROL_TX("%s: sent dns check to control broker(%s)",
                                   req->name, fohh_description(global_assistant.broker_control));
    } else {
        ASSISTANT_DEBUG_CONTROL_TX("Could not send dns check to control broker(%s)",
                                   fohh_description(global_assistant.broker_control));
        __sync_add_and_fetch_8(&global_assistant.num_dns_asst_check_tx_to_dsp_fails, 1);
    }
    return res;
}
