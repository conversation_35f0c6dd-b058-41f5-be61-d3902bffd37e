/*
 * assistant_monitor.c. Copyright (C) 2014-2020 Zscaler Inc. All Rights Reserved.
 *
 * All the periodic monitoring stuff goes here
 */
#include "zpath_lib/zpath_debug.h"

#include <time.h>
#include <arpa/inet.h>
#include "argo/argo_hash.h"
#include <event2/bufferevent.h>
#include "zpath_lib/zpath_system.h"
#include "wally/wally_test_origin.h"
#include "zpath_misc/zpath_version.h"
#include "zpn_assistantd/assistant_monitor.h"
#include "zpn_assistantd/assistant_dr_interface.h"
#include "zpn_assistantd/zpn_assistant_health.h"
#include "zpn_assistantd/zpn_assistant_mtunnel.h"
#include "zpn_assistantd/assistant_app.h"
#include "zpn_assistantd/assistant_sticky_cache.h"
#include "zpn_assistantd/assistant_state.h"
#include "zpn_assistantd/assistant_stats.h"
#include "zpn_assistantd/assistant_stats_tx.h"
#include "zpn_assistantd/assistant_control.h"
#include "zpn_assistantd/assistant_broker_control.h"
#include "zpn/assistant_log.h"
#include "zpn_assistantd/assistant_control_tx.h"
#include "zpn_assistantd/assistant_assert.h"
#include "zpn_assistantd/assistant_cfg.h"
#include "zpn_assistantd/assistant_cfg_assistant.h"
#include "zpn_assistantd/assistant_cfg_override_feature.h"
#include "zpn/zpn_system.h"
#include "zpn_assistantd/assistant_rpc.h"
#include "zpn_assistantd/assistant_log_tx.h"
#include "zpn_assistantd/assistant_dns.h"
#include "zpn_assistantd/assistant_rpc_tx.h"
#include "zpn_assistantd/assistant_data.h"
#include "zpn_assistantd/assistant_cfg_override.h"
#include "zpn_assistantd/assistant_features.h"
#include "zpn_dr/zpn_dr_lib.h"
#include "zpn_assistantd/assistant_init.h"
#include "zpath_lib/zpath_config_override.h"
#include "zpath_lib/zpath_config_override_keys.h"
#include "zpn_assistantd/assistant_cfg_assistant_group.h"
#include "zpath_lib/zpath_customer.h"
#include "zpath_lib/zpath_system_stats.h"
#include "zudp_conn/zudp_conn.h"
#include "zpn_waf/zpn_waf_lib.h"
#include "zpn_assistantd/assistant_site.h"
#include "np_connector/np_connector.h"
#include "zpn_assistantd/assistant_np_cfg.h"
#include "zpn_assistantd/assistant_util.h"
#include "zpn/zpn_firedrill_site.h"
#include "zpn/zpn_version_control/zpn_version_control.h"
#include "zpn_assistantd/zpn_assistant_private.h"
static struct {
    int64_t stop_checking_mtunnel;
} ut_hook;

static struct {
    int64_t check_mtunnel_timer_alive_counter;

} stats;

static struct zthread_info *tickle_me = NULL;

/* timestamp logging vars */
time_t          now;
struct tm       tm;

struct zpn_firedrill_stats asst_firedrill_stats_obj = {0};
int g_assistant_firedrill_transit_check_flag = 1;

static int
assistant_monitor_toggle_stop_checking_mtunnel(struct zpath_debug_state* request_state,
                                                   const char**              query_values,
                                                   int                       query_value_count,
                                                   void*                     cookie)
{
    ut_hook.stop_checking_mtunnel = ut_hook.stop_checking_mtunnel ? 0 : 1;

    ZDP("stop checking mtunnel is now %s \n", ut_hook.stop_checking_mtunnel?"ON":"OFF");
    return ZPN_RESULT_NO_ERROR;
}

void assistant_monitor_timer_stats_fill(struct zpn_assistant_monitor_stats *out_data)
{
    if (!out_data) {
        return;
    }

    out_data->check_mtunnel_timer_alive_counter = stats.check_mtunnel_timer_alive_counter;
}

static void
assistant_monitor_check_mtunnel_timer_cb(evutil_socket_t sock, short flags, void *cookie)
{
    if (tickle_me) zthread_heartbeat(tickle_me);

    if (assistant_state_is_dev_environment() && ut_hook.stop_checking_mtunnel) {
        return;
    }

    zpn_assistant_check_mtunnel();
    if (tickle_me) zthread_heartbeat(tickle_me);
    zpn_assistant_free_mtunnel_mem();

    stats.check_mtunnel_timer_alive_counter++;
}

int assistant_monitor_debug_init()
{
    int res = ZPN_RESULT_NO_ERROR;

    if (!assistant_state_is_dev_environment()) {
        return res;
    }

    res = zpath_debug_add_write_command("dump the stats of admin_probe_task_module",
                                  "/assistant/monitor/ut/stop_check_mtunnel/toggle",
                                  assistant_monitor_toggle_stop_checking_mtunnel,
                                  NULL,
                                  NULL);

    return res;
}


/*
 * Called when the row is NOT_FOUND (hard delete or soft delete) in assistant table. Hard delete cannot happen in theory
 * as OPS team don't clear out any row entries. Soft delete happens when customer deletes a connector from admin page.
 */
#if 0
/*
 * Unlink the provision key file, so that a bunch of zombie connectors don't just sit in the field and keep polling
 * the broker. Note that this routine is not called when the customer first kill the connector process and later
 * delete the connector from ZPA Admin page - in this case these zombie connectors will keep polling the brokers as
 * the process will start and keep trying to make TLS session.
 *
 * NOTE: This is a copy of FILENAME_PROVISION_CRYPT macro in enrollment library. Move this to a common place.
 * Or when assistant moves to its own directory and standalone, this can straightaway be removed.
 *
 * Right now zpn have to compiled before enrollment lib and before that we don't have access to enrollment lib's
 * headers for compilation. Hence this hack.
 */
#define ASSISTANT_FILENAME_PROVISION_CRYPT "provision_key.crypt"
static void
zpn_assistant_cfg_deleted()
{
    ASSISTANT_LOG(AL_ALERT, "Assistant(%s) deleted from ZPA config, exiting.", assistant_state_get_configured_name());
    unlink(ASSISTANT_FILENAME_PROVISION_CRYPT);
    /* sleep to make sure the event log get sent to the broker */
    sleep(5);
    exit(1);
}
#else
/*
 * crash-to-generate-backtrace to call home. This means the process will restart and keep polling the brokers. The
 * above version of deleting the provision key is extreme and could result in nasty situation in field if this portion
 * of the code had any bugs.
 *
 * FIXME: If you see this code after 2021/July and you know based on the last 12 months history that this code is
 * stable, flip the switch and turn on the above commented out code to delete provision key.
 */
static void
assistant_monitor_cfg_deletion_detected()
{
    if (global_assistant.broker_control) {
        zpn_send_zpn_asst_restart_reason_on_fohh(global_assistant.broker_control,
                                                 fohh_connection_incarnation(global_assistant.broker_control),
                                                 global_assistant.gid,
                                                 ASST_RSTR_DEL_ZPA_PORTAL);

    } else {
        ZPN_LOG(AL_NOTICE, "Assistant %s with id %"PRId64" is unable to send assistant restart reason %s to broker because control connection is not established !",
                            assistant_state_get_configured_name(), global_assistant.gid, ASST_RSTR_DEL_ZPA_PORTAL);
    }

    ZPN_LOG(AL_CRITICAL, "Assistant(%s) deleted from ZPA config, exiting.", assistant_state_get_configured_name());
    sleep(1);
    exit(1);
}
#endif

static void
assistant_monitor_check_alt_cloud_status()
{
    int on_alt_cloud = 0;
    /* report on alt cloud if any of the following connections is on alt cloud */
    on_alt_cloud = (assistant_state_connection_is_on_alt_cloud(global_assistant.broker_control) ||
                    assistant_state_connection_is_on_alt_cloud(global_assistant.broker_stats) ||
                    assistant_state_connection_is_on_alt_cloud(get_cfg_channel()) ||
                    assistant_state_connection_is_on_alt_cloud(get_ovd_channel()) ||
                    assistant_state_connection_is_on_alt_cloud(assistant_log_tx_get_event_channel()) ||
                    assistant_state_connection_is_on_alt_cloud(assistant_log_tx_get_inspection_channel()) ||
                    assistant_state_connection_is_on_alt_cloud(assistant_log_tx_get_app_inspection_channel()) ||
                    assistant_state_connection_is_on_alt_cloud(assistant_log_tx_get_ptag_channel()));
    if (on_alt_cloud) {
        assistant_state_update_alt_cloud_status(1);
    } else {
        assistant_state_update_alt_cloud_status(0);
    }
}

/*
 * 1. See if I am still eligible to continue to run,
 *        a. the config is still valid, otherwise stop the connector & delete provision key.
 *        b. the mem util is <= 95%, otherwise stop the connector.
 * 2. send all connector status messages 1min
 * 3. send smaps stats every 24hrs.
 */
static void
assistant_monitor_status_report_timer_cb(evutil_socket_t sock, short flags, void *cookie)
{
    int64_t         now_cloud_s;
    int64_t         time_remain;
    static int64_t  asst_proc_smaps_sent_time = 0;
    int64_t         local_time_lags_cloud_time_delta_us;
    struct zpn_assistant_system_stats *sys_stats = &global_assistant.sys_stats;
    int             res;
    char            uptime_str[32];
    int64_t         site_reenroll_period = 0;
    int64_t         site_reenroll_period_s = 0;

    if (0 == global_assistant.gid) {
        /*
         * I don't know of any case that will get us here. But it is a old code, so adding a assert to make sure we
         * have a case, otherwise i will remove this as a dead code.
         */
        ASSISTANT_ASSERT_SOFT(global_assistant.gid, "assistant gid is null");
        return;
    }
    if (tickle_me) zthread_heartbeat(tickle_me);

    if (assistant_features_is_qbr_insights_feature_enabled(global_assistant.gid, &global_assistant.grp_gids[0], global_assistant.grp_gids_len)) {
        if (zvm_ensure_region_id_async() != ZVM_RESULT_NO_ERROR) {
            ASSISTANT_LOG(AL_ERROR, "Error occurred while retrieving region ID information from the IMDS service");
        }
    }

    /* Log assistant features stats */
    assistant_features_stats_log();

    ZPN_LOG(AL_INFO, "Current time zone is '%s'.\n", tm.tm_zone);
    ZPN_LOG(AL_INFO, "Offset to UTC is %ld hours and %ld minutes.\n", tm.tm_gmtoff/3600, labs(tm.tm_gmtoff/60)%60);

    char *sarge_minimum_version = assistant_features_get_sarge_minimum_version(global_assistant.gid,&global_assistant.grp_gids[0],global_assistant.grp_gids_len);
    if (sarge_minimum_version) {
        if ((compare_version_util(assistant_state_get_sarge_version(), sarge_minimum_version)) == -1) {
            ZPN_LOG(AL_INFO, "zpa-connector process requires an update. please do 'sudo yum update zpa-connector' or contact customer support");
        }
    }

    if (assistant_cfg_assistant_is_deleted()) {
        assistant_monitor_cfg_deletion_detected();
    }

    if (ZPATH_RESULT_NO_ERROR != zpath_system_get_disk_free_bytes_for_non_root_user(&sys_stats->free_disk_bytes_for_non_root_user)) {
        ASSISTANT_LOG(AL_INFO, "Could not get the disk usage info");
    }

#ifdef __linux__
    if (assistant_state_is_cgroup_enabled()) {
        //Containers
        zpn_assistant_system_get_cpu_util_from_cgroups(sys_stats, assistant_state_is_cgroup_enabled());

        if (zpath_system_get_system_and_process_memory_util_percentage_from_cgroups(&sys_stats->system_mem_util, &sys_stats->process_mem_util, assistant_state_is_cgroup_enabled())) {
            sys_stats->system_mem_util = 0;
            sys_stats->process_mem_util = 0;
            ASSISTANT_LOG(AL_INFO, "Could not get the Memory usage info");
        }

        if (zpn_system_get_memory_usage_info_from_cgroups(&sys_stats->memtotal_abs_mem, &sys_stats->memfree_abs_mem,
                                                          &sys_stats->swaptotal_abs_mem, &sys_stats->swapfree_abs_mem,
                                                          &sys_stats->system_used_abs_mem, &sys_stats->process_used_abs_mem,
                                                          assistant_state_is_cgroup_enabled()) == ZPN_RESULT_ERR) {
            sys_stats->memtotal_abs_mem = 0;
            sys_stats->memfree_abs_mem = 0;
            sys_stats->swaptotal_abs_mem = 0;
            sys_stats->swapfree_abs_mem = 0;
            sys_stats->system_used_abs_mem = 0;
            sys_stats->process_used_abs_mem = 0;
            ASSISTANT_LOG(AL_INFO, "Could not get the Absolute Memory usage info");
        }
    } else
#endif
    {
        //VMs
        zpn_assistant_system_get_cpu_util(sys_stats);

        if (zpath_system_get_system_and_process_memory_util_percentage(&sys_stats->system_mem_util, &sys_stats->process_mem_util)) {
            sys_stats->system_mem_util = 0;
            sys_stats->process_mem_util = 0;
            ASSISTANT_LOG(AL_INFO, "Could not get the Memory usage info");
        }

        if (zpn_system_get_memory_usage_info(&sys_stats->memtotal_abs_mem, &sys_stats->memfree_abs_mem,
                                             &sys_stats->swaptotal_abs_mem, &sys_stats->swapfree_abs_mem,
                                             &sys_stats->system_used_abs_mem, &sys_stats->process_used_abs_mem) == ZPN_RESULT_ERR) {
            sys_stats->memtotal_abs_mem = 0;
            sys_stats->memfree_abs_mem = 0;
            sys_stats->swaptotal_abs_mem = 0;
            sys_stats->swapfree_abs_mem = 0;
            sys_stats->system_used_abs_mem = 0;
            sys_stats->process_used_abs_mem = 0;
            ASSISTANT_LOG(AL_INFO, "Could not get the Absolute Memory usage info");
        }
    }

    assistant_state_get_uptime_str(uptime_str, sizeof(uptime_str));

#ifdef __linux__
    ZPN_LOG(AL_NOTICE, "-------- Connector Status:ID=%ld:Name=%s:Ver=%s:Config:%s:Mem(System|Process)=%d%%|%d%%:"
        "Mem_abs(MemTotal|MemFree|SwapTotal|SwapFree|SysUsed|ProcessUsed)= %" PRIu64 "kB |%" PRIu64 "kB |%" PRIu64 "kB |%" PRIu64 "kB |%" PRIu64 "kB |%" PRIu64 "kB :"
        "Disk Avail=%.2fGB:CPU(Util|Steal|Configured|Avail)=%d%%|%d%%|%d|%d:Uptime=%s --------",
        (long) global_assistant.gid, assistant_state_get_configured_name(), ZPATH_VERSION, assistant_state_is_cfg_enabled() ? "Enabled" : "Disabled",
        sys_stats->system_mem_util, sys_stats->process_mem_util,
        sys_stats->memtotal_abs_mem, sys_stats->memfree_abs_mem, sys_stats->swaptotal_abs_mem, sys_stats->swapfree_abs_mem, sys_stats->system_used_abs_mem, sys_stats->process_used_abs_mem,
        (double)sys_stats->free_disk_bytes_for_non_root_user/ZPN_SYSTEM_GB_TO_BYTES, sys_stats->cpu_util, sys_stats->cpu_steal_perc,
        assistant_state_get_configured_cpus(), assistant_state_get_available_cpus(), uptime_str);
#else
    ZPN_LOG(AL_NOTICE, "-------- Connector Status:ID=%ld:Name=%s:Ver=%s:Config:%s:Mem(System|Process)=%d%%|%d%%:"
        "Mem_abs(MemTotal|MemFree|SwapTotal|SwapFree|SysUsed|ProcessUsed)= %" PRIu64 "kB |%" PRIu64 "kB |%" PRIu64 "kB |%" PRIu64 "kB |%" PRIu64 "kB |%" PRIu64 "kB :"
        "Disk Avail=%.2fGB:CPU(Util|Steal|Configured|Avail)=%d%%|%d%%|%d|%d:Uptime=%s --------",
        (long) global_assistant.gid, assistant_state_get_configured_name(), ZPATH_VERSION, assistant_state_is_cfg_enabled() ? "Enabled" : "Disabled",
        sys_stats->system_mem_util, sys_stats->process_mem_util,
        sys_stats->memtotal_abs_mem, sys_stats->memfree_abs_mem, sys_stats->swaptotal_abs_mem, sys_stats->swapfree_abs_mem, sys_stats->system_used_abs_mem, sys_stats->process_used_abs_mem,
        (double)sys_stats->free_disk_bytes_for_non_root_user/ZPN_SYSTEM_GB_TO_BYTES, sys_stats->cpu_util, sys_stats->cpu_steal_perc,
        assistant_state_get_configured_cpus(), assistant_state_get_available_cpus(), uptime_str);
#endif

    /* cookie == NULL makes sure that we don't call this routine at the init time */
    if (cookie == NULL) {
        char* thread_cpu_usage_log = zthread_get_thread_cpu_usage_log();
        if (thread_cpu_usage_log != NULL) {
            ZPN_LOG(AL_NOTICE, "-------- Connector thread CPU usage: %s", thread_cpu_usage_log);
            ASST_FREE(thread_cpu_usage_log);
        }
    }

    if (assistant_state_get_swap_config()) {
        ZPN_LOG(AL_NOTICE, "Swap partition is found to be configured,"
			   " Zscaler does not recommend swap config as this could have impact on data transfer rate");
    }

    /* Certificate expiration */
    now_cloud_s = assistant_state_get_current_time_cloud_s();
    time_remain = global_assistant.cert_validity_end_time_cloud_s - now_cloud_s;
    ASSISTANT_ASSERT_HARD(global_assistant.cert_force_re_enroll_time_cloud_s, "unexpected code path, please report to customer support team");

    site_reenroll_period = assistant_site_get_reenroll_period_with_lock();
    site_reenroll_period_s = site_reenroll_period * 24 * 60 * 60;
    if (site_reenroll_period_s >= global_assistant.cert_validity_end_time_cloud_s - global_assistant.cert_validity_start_time_cloud_s) {
        site_reenroll_period = 90;
        ZPN_LOG(AL_CRITICAL, "reenroll_period %"PRId64" is greater than cert lifetime %"PRId64" , change it to %"PRId64" days",
                site_reenroll_period_s,
                global_assistant.cert_validity_end_time_cloud_s - global_assistant.cert_validity_start_time_cloud_s,
                site_reenroll_period);
        site_reenroll_period_s = site_reenroll_period * 24 * 60 * 60;
    }
    if (site_reenroll_period_s >= 0 &&
        (now_cloud_s > (global_assistant.cert_validity_end_time_cloud_s - site_reenroll_period_s))) {
        if (global_assistant.broker_control) {
            zpn_send_zpn_asst_restart_reason_on_fohh(global_assistant.broker_control,
                                                     fohh_connection_incarnation(global_assistant.broker_control),
                                                     global_assistant.gid,
                                                     ASST_RSTR_CERT_REENROLL);
        } else {
            ZPN_LOG(AL_NOTICE, "Assistant %s with id %"PRId64" is unable to send assistant restart reason %s to broker because control connection is not established !",
                                assistant_state_get_configured_name(), global_assistant.gid, ASST_RSTR_CERT_REENROLL);
        }

        ZPN_LOG(AL_CRITICAL, "Forcing certificate reenroll due to site config reenroll_period(%"PRId64" days), time remaining %"PRId64" days, %"PRId64" hours, %"PRId64" minutes, %"PRId64" seconds, restarting to re-enroll",
                site_reenroll_period, time_remain/86400, (time_remain % 86400)/3600, ((time_remain % 86400) % 3600)/60, ((time_remain % 86400) % 3600) % 60);
        if (global_assistant.broker_control && fohh_get_state(global_assistant.broker_control) == fohh_connection_connected) {
            sleep(1);
            exit(1);
        }
    }

    if (now_cloud_s > global_assistant.cert_force_re_enroll_time_cloud_s) {

        if (global_assistant.broker_control) {
            zpn_send_zpn_asst_restart_reason_on_fohh(global_assistant.broker_control,
                                                     fohh_connection_incarnation(global_assistant.broker_control),
                                                     global_assistant.gid,
                                                     ASST_RSTR_CERT_EXPIRING);
        } else {
            ZPN_LOG(AL_NOTICE, "Assistant %s with id %"PRId64" is unable to send assistant restart reason %s to broker because control connection is not established !",
                                assistant_state_get_configured_name(), global_assistant.gid, ASST_RSTR_CERT_EXPIRING);
        }

        ZPN_LOG(AL_CRITICAL, "Certificate will expire in %"PRId64" days, %"PRId64" hours, %"PRId64" minutes, %"PRId64" seconds, restarting to re-enroll",
                time_remain/86400, (time_remain % 86400)/3600, ((time_remain % 86400) % 3600)/60,
                ((time_remain % 86400) % 3600) % 60);
        sleep(1);
        exit(1);
    } else {
        ZPN_LOG(AL_NOTICE, "Certificate will expire in %"PRId64" days, %"PRId64" hours, %"PRId64" minutes, %"PRId64" seconds",
                time_remain/86400, (time_remain % 86400)/3600, ((time_remain % 86400) % 3600)/60,
                ((time_remain % 86400) % 3600) % 60);
    }

    if (global_assistant.auto_upgrade_disabled) {
        ZPN_LOG(AL_NOTICE, "Auto Upgrade disabled, currently at version(%s)", ZPATH_VERSION);
    }

    if (fohh_proxy_hostname) {
        ZPN_LOG(AL_NOTICE, "Using proxy server %s:%"PRIu16, fohh_proxy_hostname, fohh_proxy_port);
    }

    assistant_monitor_check_alt_cloud_status();
    ZPN_LOG(AL_NOTICE, "Status of alternate domain %s", global_assistant.alt_cloud_enabled? "enabled": "disabled");
    if (global_assistant.alt_cloud) {
         ZPN_LOG(AL_NOTICE, "Using alternate domain %s", global_assistant.alt_cloud);
    }

    assistant_cfg_log_status();
    assistant_cfg_check_insane();
    assistant_np_cfg_log_status();
    assistant_cfg_override_log_status();
    assistant_log_tx_log_status();
    assistant_stats_log_status();
    assistant_control_log_status();
    assistant_broker_control_check_insane();
    assistant_data_log_status();
    assistant_dns_log_status();
    assistant_rpc_log_status();
    assistant_cfg_check_mconn_track_perf_stats();
    assistant_features_fproxy_log_status();


    if (ZPATH_RESULT_NO_ERROR == assistant_app_update_global_stats()) {
        ZPN_LOG(AL_NOTICE, "Registered apps count %ld, alive apps %ld, apps with continuous health %ld, apps with on-access health %ld, "
                "apps with no health reporting %ld, service count %ld, target count %ld, target alive %ld",
                (long)global_assistant.app_count,
                (long)global_assistant.alive_app_count,
                (long)global_assistant.active_app_health_count,
                (long)global_assistant.passive_app_health_count,
                (long)global_assistant.passive_app_muted_health_count,
                (long)global_assistant.service_count,
                (long)global_assistant.target_count,
                (long)global_assistant.alive_target_count);
        if(global_assistant.service_count > 6000) {
            ZPN_LOG(AL_WARNING, "App count > 6K, Maximum allowed apps are 6K");
        }
    }
    ZPN_LOG(AL_NOTICE, "Zudp connection stats Connections:%"PRId64" Sockets:%"PRId64" ", zudp_conn_get_global_connection_count(), zudp_conn_get_global_socket_count());

    /* Clock skew */
    local_time_lags_cloud_time_delta_us = assistant_state_get_time_lag_us();
    if (local_time_lags_cloud_time_delta_us < 0) {
        ZPN_LOG(AL_NOTICE, "Time skew: local time is ahead of cloud time by %ld.%06lds",
                (long) (0 - local_time_lags_cloud_time_delta_us) / 1000000,
                (long) (0 - local_time_lags_cloud_time_delta_us) % 1000000);
    } else {
        ZPN_LOG(AL_NOTICE, "Time skew: local time lags cloud time by %ld.%06lds",
                (long) local_time_lags_cloud_time_delta_us / 1000000, (long) local_time_lags_cloud_time_delta_us % 1000000);
    }

    res = assistant_state_get_num_hw_id_changed();
    if (res) {
        int i;
        int64_t* time_arr = assistant_state_get_hw_id_changed_time_us();
        ASSISTANT_LOG(AL_NOTICE, "Detected %d device(s) id changed, note that the next connector process restart will fail", res);
        for (i = 0; i < res ; i++) {
            char time_str[128] = {0};
            int64_t changed_time_us = time_arr[i];
            argo_log_gen_time(changed_time_us, time_str, sizeof(time_str), 0, 1);
            ASSISTANT_LOG(AL_NOTICE, "%d/%d device id changed around time: %s", i+1, res, time_str);
        }
    }

    zpn_system_assistant_monitor(sys_stats);

    // after system monitor - check if adjustment of out queue is enabled based on libevent allocator
    if (assistant_features_allocator_libevent_out_queue_is_enabled(global_assistant.gid)) {

        int64_t allocator_libevent_out_queue_allowed_bytes;
        assistant_state_allocator_libevent_out_queue_calc_and_update(global_assistant.gid, &allocator_libevent_out_queue_allowed_bytes);

        // adjust out queue into mconn_bufferevent if different then current
        assistant_state_allocator_libevent_out_queue_set(allocator_libevent_out_queue_allowed_bytes);
    }

    /*
     * If we try to send stats or event log before the fohh is connected, it will get dropped (think fohh queue
     * flush) when it get connected. But smaps is a important baseline to miss. So lets make sure, we do it the first
     * time only when it is connected. Also, we are worried only about broker control connection here as that is
     * the the only one which is interested in the stats of the connector. Dirty hack, but for now..
     */
    if (assistant_stats_is_connected()) {
        if ((now_cloud_s - asst_proc_smaps_sent_time) > ASSISTANT_STATS_TX_PROC_SMAPS_TIMEPERIOD_SEC) {
            asst_proc_smaps_sent_time = now_cloud_s;
#ifdef __linux__
            /*
             * Make sure CONFIG_PROC_PAGE_MONITOR is enabled in the linux kernel.
             */
            ARGO_STATS_LOG_FILE_CONTENTS(argo_log_get("statistics_log"), "proc_smaps", "/proc/self/smaps");
#endif
        }
    }

    if (global_assistant.is_first_status_log_sent) {
        assistant_rpc_tx_status_report();
    }

    if (global_assistant.sitec_control && global_assistant.is_first_status_log_sent_to_sitec) {
        assistant_sitec_control_tx_status();
    }
    assistant_rpc_tx_state();

    if (global_assistant.cur_mode == np_connector) {
        np_connector_monitor_log();
    }
}

int zpn_assistant_firedrill_get_config(struct zpn_firedrill_site **firedrill_config)
{
    int res = 0;
    res = zpn_firedrill_site_by_site_gid(global_assistant.site_gid, global_assistant.customer_gid,
                                            assistant_site_firedrill_config_fetch_callback,
                                            firedrill_config);
    if(res && res != ZPN_RESULT_ASYNCHRONOUS) {
        ZPN_LOG(AL_ERROR, "firedrill config fetch failed for site_gid = %"PRId64" res: %s",global_assistant.site_gid, zpn_result_string(res));
        return res;
    } else if (res == ZPN_RESULT_ASYNCHRONOUS) {
        ZPN_LOG(AL_ERROR, "firedrill config fetch asynchronous site_gid = %"PRId64" res: %s",global_assistant.site_gid, zpn_result_string(res));
        return ZPN_RESULT_NO_ERROR;
    }
    return res;
}

int zpn_assistant_firedrill_start(int64_t firedrill_interval)

{
    if( (global_assistant.firedrill_cfg.status == ZPN_ASSISTANT_FIREDRILL_DISABLED ||
         global_assistant.firedrill_cfg.status == ZPN_ASSISTANT_FIREDRILL_TRANSIT ||
         (global_assistant.firedrill_cfg.leftover_interval_s && global_assistant.firedrill_cfg.status == ZPN_ASSISTANT_FIREDRILL_ENABLED))
         && firedrill_interval) {
        /* activate the timer */
        if(zpn_assistant_firedrill_timer_activate(firedrill_interval)) {
            ZPN_LOG(AL_ERROR, "firedrill timer activation failed");
            return ZPN_RESULT_ERR;
        }
        /* switch the connections to pcc */
        zpn_assistant_switch_to_pcc();
    }
    return ZPN_RESULT_NO_ERROR;
}

void zpn_assistant_mc_timer_cb(evutil_socket_t sock, int16_t flags, void *cookie)
{
    /* we are hitting the timer means, amc is not successful, enter firedrill mode */
    ZPN_LOG(AL_INFO, "firedrill, amc connection failed, entering firedrill mode again for interval %"PRId64"",global_assistant.firedrill_cfg.interval_s);

    /* disconnect the amc connection */
    if (global_assistant.broker_mc && (strcmp(global_assistant.broker_mc->close_reason, FOHH_CLOSE_REASON_SOCKET_ERR)) == 0) {
         fohh_connection_delete_async(global_assistant.broker_mc, global_assistant.broker_mc->incarnation, FOHH_CLOSE_REASON_MISSION_CRITICAL_CONN_FAILED);
    }

    if(global_assistant.asst_mc_timer) {
        event_del(global_assistant.asst_mc_timer);
    }
    __sync_add_and_fetch(&asst_firedrill_stats_obj.firedrill_transit_count, 1);

    if(global_assistant.firedrill_cfg.interval_s && zpn_assistant_firedrill_start(global_assistant.firedrill_cfg.interval_s)) {
        ZPN_LOG(AL_INFO, "firedrill, amc conn failed, retriggering firedrill for interval %"PRId64"",global_assistant.firedrill_cfg.interval_s);
        return;
    }
}


void zpn_assistant_firedrill_stop(evutil_socket_t sock, int16_t flags, void *cookie)
{
    struct zpn_assistant_site_config *asst_cfg = (struct zpn_assistant_site_config *)ASST_MALLOC(sizeof(struct zpn_assistant_site_config));

    ZPATH_RWLOCK_RDLOCK(&(global_assistant.ddil_lock), __FILE__, __LINE__);

    if( ZPN_ASSISTANT_FIREDRILL_DISABLED == global_assistant.firedrill_cfg.status) {
        ZPN_LOG(AL_ERROR, "assistant not in firedrill mode\n");
        ZPATH_RWLOCK_UNLOCK(&(global_assistant.ddil_lock), __FILE__, __LINE__);
        ASST_FREE(asst_cfg);
        return;
    }

    ZPATH_RWLOCK_UNLOCK(&(global_assistant.ddil_lock), __FILE__, __LINE__);


    ZPATH_RWLOCK_WRLOCK(&(global_assistant.ddil_lock), __FILE__, __LINE__);

    global_assistant.firedrill_cfg.status = ZPN_ASSISTANT_FIREDRILL_TRANSIT;

    asst_cfg->fd_status = ZPN_ASSISTANT_FIREDRILL_TRANSIT;
    asst_cfg->fd_interval_s = global_assistant.firedrill_cfg.interval_s;
    asst_cfg->fd_start_time = global_assistant.firedrill_cfg.start_time;
    global_assistant.firedrill_cfg.leftover_interval_s = 0;

    ZPATH_RWLOCK_UNLOCK(&(global_assistant.ddil_lock), __FILE__, __LINE__);

    // update the firedrill state in config file
    assistant_update_firedrill_config_to_site_config_file(asst_cfg);

    ZPN_LOG(AL_INFO, "assistant: firedrill interval completed, check firedrill status from amc channel");

    if(fohh_thread_call( fohh_worker_pool_get_thread_id(FOHH_WORKER_ZPN_MC), zpn_assistant_mission_critical_conn_create, NULL, 0)) {
        ZPN_LOG(AL_CRITICAL, "Implement me");
    }
    ASST_FREE(asst_cfg);
}

static int assistant_monitor_base_init(struct event_base *base)
{
    int init_f = 0;
    int res = 0;
    struct timeval tv;
    struct event *ev_timer;
    struct event *ev_status_timer;
    struct event *ev_sticky_cache_cleanup_timer;
    int64_t sticky_cache_cleanup_interval_us;

    /* get the timezone */

    time(&now);
    localtime_r(&now, &tm);

    ev_timer = event_new(base,
                         -1,
                         EV_PERSIST,
                         assistant_monitor_check_mtunnel_timer_cb,
                         NULL);
    if (!ev_timer) {
        ZPN_LOG(AL_ERROR, "Could not create timer");
        return ZPN_RESULT_ERR;
    }

    /*
     * Lets have the timer trigger every 3 seconds to clean up the mtunnel. This gives atleast 8 seconds for the broker
     * to detect the change. usec is zero as we are ok with seconds granularity.
     */
    tv.tv_sec = ((ZPN_MCONN_TIMEOUT_BRK - ZPN_MCONN_TIMEOUT_AST) / 1000000) / 3;
    tv.tv_usec = 0;
    if (event_add(ev_timer, &tv)) {
        ZPN_LOG(AL_ERROR, "Could not activate timer");
        return ZPN_RESULT_ERR;
    }

    ev_status_timer = event_new(base,
                                -1,
                                EV_PERSIST,
                                assistant_monitor_status_report_timer_cb,
                                NULL);
    if (!ev_status_timer) {
        ZPN_LOG(AL_ERROR, "Could not create status timer");
        return ZPN_RESULT_ERR;
    }

    /* Make it show health once right away */
    assistant_monitor_status_report_timer_cb(-1, 0, &init_f);

    tv.tv_sec = ASSISTANT_MONITOR_TIMER_S;
    tv.tv_usec = 0;
    if (event_add(ev_status_timer, &tv)) {
        ZPN_LOG(AL_ERROR, "Could not activate status timer");
        return ZPN_RESULT_ERR;
    }

    ev_sticky_cache_cleanup_timer = event_new(base, -1, EV_PERSIST, assistant_sticky_cache_periodic_monitor, NULL);
    if (!ev_sticky_cache_cleanup_timer) {
        ZPN_LOG(AL_ERROR, "Could not create sticky cache cleanup timer");
        return ZPN_RESULT_ERR;
    }

    sticky_cache_cleanup_interval_us = assistant_sticky_cache_get_cleanup_interval_us();
    tv.tv_sec = sticky_cache_cleanup_interval_us / (1000 * 1000);
    tv.tv_usec = sticky_cache_cleanup_interval_us % (1000 * 1000);
    if (event_add(ev_sticky_cache_cleanup_timer, &tv)) {
        ZPN_LOG(AL_ERROR, "Could not activate sticky cache cleanup timer");
        return ZPN_RESULT_ERR;
    }

    /* create the firedrill timer */
    /* activate it in the row callback when it is trigerred through adminUI */
    global_assistant.asst_firedrill_timer = event_new(base,
                                            -1,
                                            EV_TIMEOUT,
                                            zpn_assistant_firedrill_stop,
                                            NULL);
    if (!global_assistant.asst_firedrill_timer) {
        ZPN_LOG(AL_ERROR, "firedrill: Could not create firedrill timer for assistant");
        return ZPN_RESULT_ERR;
    }

    /* create the mc timer */
    /* activate it when creating the amc channel connection */
    global_assistant.asst_mc_timer = event_new(base,
                                        -1,
                                        EV_TIMEOUT,
                                        zpn_assistant_mc_timer_cb,
                                        NULL);
    if (!global_assistant.asst_mc_timer) {
        ZPN_LOG(AL_ERROR, "firedrill: Could not create mc timer for assistant");
        return ZPN_RESULT_ERR;
    }

    if(global_assistant.firedrill_cfg.leftover_interval_s) {
        ZPN_LOG(AL_NOTICE, "firedrill status enabled in site_confg.cfg file, enable the firedrill for the remaining duration %"PRId64"", global_assistant.firedrill_cfg.leftover_interval_s);
        res = zpn_assistant_firedrill_start(global_assistant.firedrill_cfg.leftover_interval_s);
        if(res) {
            ZPN_LOG(AL_ERROR,"firedrill activation for leftover time failed");
            return ZPN_RESULT_ERR;
        }
    }

    return ZPN_RESULT_NO_ERROR;
}

void *
assistant_monitor_thread(struct zthread_info *zthread_arg, void *cookie)
{
    struct event_base *base = NULL;
    int res;

    tickle_me = zthread_arg;

    base = event_base_new();
    if (!base) {
        ZPN_LOG(AL_ERROR, "Could not create event_base: zam_monitor");
        goto fail;
    }

    res = assistant_monitor_debug_init();
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not initialize assistant_monitor_debug_init, zam_health thread");
        goto fail;
    }


    res = assistant_monitor_base_init(base);
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not initialize event_base, zam_health thread");
        goto fail;
    }

    global_assistant.asst_monitor_base = zevent_attach(base);

    zpn_waf_monitor_init(base);

    zevent_base_dispatch(base);

    ZPN_LOG(AL_ERROR, "Not reachable, zpa connector monitor thread");
fail:
    /* Should watchdog... */
    while(1) {
        sleep(1);
    }
    return NULL;
}


int
assistant_monitor_init(struct event_base *base)
{
    if (base) {
        return assistant_monitor_base_init(base);
    } else {
        pthread_t thread;
        int res;
        res = zthread_create_with_priority(&thread,
                             assistant_monitor_thread,
                             NULL,
                             "assistant_monitor",
                             60,
                             16 * 1024 * 1024,
                             60 * 1000 * 1000,
                             NULL,
                             zthread_priority_high);
        if (res) return ZPN_RESULT_ERR;
        return ZPN_RESULT_NO_ERROR;
    }
}

int zpn_assistant_firedrill_timer_activate(int64_t firedrill_interval)
{
    struct timeval tv = {0};
    struct zpn_assistant_site_config *asst_cfg = (struct zpn_assistant_site_config *)ASST_MALLOC(sizeof(struct zpn_assistant_site_config));

    ZPN_LOG(AL_INFO, "firedrill triggered for interval: %"PRId64"", firedrill_interval);

    /* activate the timer */
    tv.tv_sec = firedrill_interval;
    tv.tv_usec = 0;
    if (event_add(global_assistant.asst_firedrill_timer, &tv)) {
        ZPN_LOG(AL_ERROR, "Could not add status timer for firedrill");
        ASST_FREE(asst_cfg);
        return ZPN_RESULT_ERR;
    }

    ZPATH_RWLOCK_WRLOCK(&(global_assistant.ddil_lock), __FILE__, __LINE__);

    asst_cfg->fd_status = ZPN_ASSISTANT_FIREDRILL_ENABLED;
    asst_cfg->fd_interval_s = firedrill_interval;
    asst_cfg->fd_start_time = epoch_s();

    ZPATH_RWLOCK_UNLOCK(&(global_assistant.ddil_lock), __FILE__, __LINE__);

    assistant_update_firedrill_config_to_site_config_file(asst_cfg);

    __sync_add_and_fetch(&asst_firedrill_stats_obj.firedrill_triggered_count, 1);

    ASST_FREE(asst_cfg);
    return ZPN_RESULT_NO_ERROR;
}

void zpn_assistant_switch_to_cloud()
{
    ASSISTANT_LOG(AL_ERROR, "ENABLE ASSISTANT BROKER CONTROL CHANNEL");
    /* enable the broker control channel */
    if (global_assistant.firedrill_cfg.status && global_assistant.broker_control) {
        fohh_connection_enable_async(global_assistant.broker_control,
                                      fohh_connection_incarnation(global_assistant.broker_control));
    }
    assistant_data_enable_adata_conns();
}

void zpn_assistant_switch_to_pcc()
{
    /* disable the broker control channel */
    if (global_assistant.firedrill_cfg.status && global_assistant.broker_control) {
        ASSISTANT_LOG(AL_ERROR, "DISABLE ASSISTANT BROKER CONTROL CHANNEL");
        fohh_connection_disable_async(global_assistant.broker_control,
                                      fohh_connection_incarnation(global_assistant.broker_control), FOHH_CLOSE_REASON_ASSISTANT_FIREDRILL);
    }
    assistant_data_disable_adata_conns();
}
