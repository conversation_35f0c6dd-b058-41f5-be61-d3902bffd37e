/*
 * assistant_cfg_version.h. Copyright (C) 2019 Zscaler Inc, All Rights Reserved
 *
 */

#ifndef _ASSISTANT_CFG_VERSION_H_

int assistant_cfg_version_is_lone_warrior();
int
assistant_cfg_version_get_upgrade_info(int64_t* cfg_upgrade_time_s,
                                       char*    cfg_upgrade_version,
                                       char *   sarge_cfg_upgrade_version,
                                       size_t   max_version_len,
                                       int*     os_upgrade_enabled,
                                       char *   frr_cfg_upgrade_version,
                                       size_t   frr_cfg_upgrade_version_len);

int assistant_cfg_version_int(struct wally *asst_wally, int64_t asst_gid);

#define _ASSISTANT_CFG_VERSION_H_
#endif // _ASSISTANT_CFG_VERSION_H_
