/*
 * assistant_cfg.c. Copyright (C) 2019 Zscaler Inc. All Rights Reserved.
 *
 * 1. A shim layer between assistant's operation & the ZPA config/wally
 * 2. Some properties, we don't configure. But hardcode the defaults in the code and move on. Those are also handled
 * by this layer.
 * 3. ZPN_RESULT_NOT_FOUND - This can happen when quering an entry in the following cases,
 *  a. The first time, when the process is coming up, broker itself will not have any entry. It will do a query to
 *  the upstream wally and send a NOT_FOUND error to the downstream connector.
 *  b. when a row is deleted by management API after being operational for a while (means that this connector have
 *  really seen an entry before, but now it is deleted in management API).
 *
 * Any access into the config (i.e coming from wally or non-configurable-defaults) from the assistant should go in via
 * this layer. The idea is that if the assistant wants to understand if it have property_x(think lone_warrior for
 * example), it should just query this config layer without the need to understand what config_element/table that
 * specific property_x is configured into. Down this line, this will allow us to easily create a mock layer of the
 * config and test connector without wally getting involved.
 *
 * FIXME: Today not every interaction of assistant is going via this shim layer, but this is just a start to simplify
 * the understanding of code flow.
 *
 */
#include "wally/wally_fohh_client.h"
#include "zpath_lib/zpath_debug_wally.h"
#include "zpath_lib/zpath_customer.h"
#include "fohh/fohh_private.h"
#include "zpn_dr/zpn_dr_lib.h"
#include "zpn/zpn_lib.h"
#include "wally/wally_test_origin.h"
#include "zpn_assistantd/zpn_assistant_private.h"
#include "zpn/zpn_fohh_worker.h"
#include "zpn_assistantd/assistant_state.h"
#include "zpn/assistant_log.h"
#include "zpn_assistantd/assistant_assert.h"
#include "zpn_assistantd/assistant_cfg_assistant.h"
#include "zpn_assistantd/assistant_cfg_version.h"
#include "zpn_assistantd/assistant_cfg_application.h"
#include "zpn_assistantd/assistant_cfg_map_assistant_to_assistant_group.h"
#include "zpn_assistantd/assistant_cfg_assistant_group.h"
#include "zpn_assistantd/assistant_cfg_map_assistant_group_to_server_group.h"
#include "zpn_assistantd/assistant_cfg_server_group.h"
#include "zpn_assistantd/assistant_cfg_map_server_group_to_app.h"
#include "zpn_assistantd/assistant_cfg_map_application_to_application_group.h"
#include "zpn_assistantd/assistant_cfg_map_server_group_to_server.h"
#include "zpn_assistantd/assistant_cfg_server.h"
#include "zpn_assistantd/assistant_cfg_pbroker_load.h"
#include "zpn_assistantd/assistant_cfg_pbroker.h"
#include "zpn_assistantd/assistant_site.h"
#include "zpn_assistantd/assistant_monitor.h"
#include "zpn_assistantd/assistant_cfg_application_group.h"
#include "zpn_assistantd/assistant_cfg_waf.h"
#include "zpn_assistantd/assistant_cfg_zpath_customer.h"
#include "zpn_assistantd/assistant_cfg_sub_module_upgrade.h"
#include "zpn_assistantd/assistant_pbroker_control.h"
#include "zpn_assistantd/assistant_rpc_tx.h"
#include "zpn/zpn_application.h"
#include "zpn/zpn_private_broker.h"
#include "zpn/zpn_version_control/zpn_version_control_utils.h"
#include "zpath_misc/zpath_version.h"



static struct {
    struct fohh_connection *cfg_channel;
    char alt_cloud_name[ZPN_MAX_DOMAIN_NAME_LEN];
} state;

static struct assistant_cfg_stats {                                     /* _ARGO: object_definition */
    uint64_t log_status_none;                                           /* _ARGO: integer */
    uint64_t log_status_success;                                        /* _ARGO: integer */
    uint64_t alt_cloud_resets;                                          /* _ARGO: integer */
    uint64_t fohh_info_capability_tx;                                   /* _ARGO: integer */
} stats;
#include "zpn_assistantd/assistant_cfg_compiled_c.h"

static struct argo_structure_description*  assistant_cfg_stats_description;

static int
assistant_cfg_dump_all(struct zpath_debug_state*  request_state,
                       const char**               query_values,
                       int                        query_value_count,
                       void*                      cookie);

void assistant_cfg_check_insane()
{
    if (1 == assistant_state_is_pbroker_environment()) {
        return;
    }
    if (is_zpn_drmode_enabled()) {
        return;
    }
    static int64_t counter_s = 0;
    if ((state.cfg_channel) && (fohh_connection_connected == (state.cfg_channel)->state)) {
        counter_s = 0;
    } else {

        counter_s += ASSISTANT_MONITOR_TIMER_S;

        /* Continue with normal beheviour if DR mode is disabled */
        if ((int64_t)counter_s >= assistant_state_get_max_allowed_connection_downtime_s()) {
           /*
            * If subcomponent download is in progress,
            * we still want to keep operating, even when config connection is down.
            * This is to enable download completion in low bandwidth environment.
            */
           if ((global_assistant.download_in_progress == DOWNLOAD_IN_PROGRESS) && (counter_s < MAX_DOWNLOAD_IN_PROGRESS_TIME)) {
                ASSISTANT_LOG(AL_NOTICE, "Connector(ID = %"PRId64") (Name = %s) "
                                       "config connection %s down for %"PRId64" secs, Continuing download in progress.",
                                        global_assistant.gid, assistant_state_get_configured_name(),
                                        state.cfg_channel->connection_description, counter_s);
                return;
            }

            if (global_assistant.broker_control) {
                zpn_send_zpn_asst_restart_reason_on_fohh(global_assistant.broker_control,
                                                         fohh_connection_incarnation(global_assistant.broker_control),
                                                         global_assistant.gid,
                                                         ASST_RSTR_DC_CONFIG_CON);
            } else {
                ASSISTANT_LOG(AL_NOTICE, "Assistant %s with id %"PRId64" is unable to send assistant restart reason %s to broker because control connection is not established !",
                                          assistant_state_get_configured_name(), global_assistant.gid, ASST_RSTR_DC_CONFIG_CON);
            }

            ASSISTANT_LOG(AL_CRITICAL, "Connector(ID = %"PRId64") (Name = %s) restarting as it is disconnected "
                          "from config connection %s for %"PRId64" secs. Please check internet connectivity.",
                                        global_assistant.gid, assistant_state_get_configured_name(),
                                        state.cfg_channel->connection_description, counter_s);
            /*
             * Sleep so that the logs can be sent
             */
            sleep(1);
            exit(1);
        }
    }
}

static int
assistant_cfg_broker_redirect(void*                cookie,
                              void*                structure_cookie,
                              struct argo_object*  object)
{

    struct fohh_connection *f_conn = structure_cookie;

    struct zpn_broker_redirect *req = object->base_structure_void;

    if (req->broker_count) {
        ASSISTANT_LOG(AL_INFO, "Received broker_redirect for config connection: %s", fohh_description(f_conn));

        if (fohh_redirect(f_conn, (const char **)req->brokers, req->broker_count, req->timestamp_s, req->sni_suffixes) != 0) {
            ASSISTANT_LOG(AL_INFO, "fohh_redirect failed in config connection");
        }
    } else {
        ASSISTANT_LOG(AL_WARNING, "broker redirect message returns 0 broker candidate for config connection");
    }

    if (req->alt_cloud) {
        assistant_state_store_alt_domain_config_with_lock(global_assistant.alt_cloud_enabled, req->alt_cloud);
    }

    global_assistant.num_cfg_redirect++;

    return ZPN_RESULT_NO_ERROR;
}

static int
zpn_assistant_version_ack_cb(void *argo_cookie_ptr __attribute__((unused)), void *argo_structure_cookie_ptr,
                             struct argo_object *object)
{
    struct fohh_connection *f_conn = argo_structure_cookie_ptr;
    const struct zpn_version_ack *ack = object->base_structure_void;

    global_assistant.version_data.zpn_version_check_end_us = epoch_us();
    if (assistant_debug_log & ASSISTANT_DEBUG_CONTROL_BIT) {
        char dump[5000];
        if (argo_object_dump(object, dump, sizeof(dump), NULL, 0) == ARGO_RESULT_NO_ERROR) {
            ASSISTANT_LOG(AL_NOTICE, "Rx version_ack: %s", dump);
        }
    }

    ZPN_LOG(AL_NOTICE, "Received version_ack from broker,took %" PRId64 " us : error = %s",
                (global_assistant.version_data.zpn_version_check_end_us -
                global_assistant.version_data.zpn_version_check_start_us), ack->error ? : "NONE");

    if (ack->error) {
        ZPN_LOG(AL_ERROR, "%s: assistant running incompatible version. version_ack from broker : error = %s", fohh_description(f_conn), ack->error);
        global_assistant.version_data.client_version = zpn_client_version_invalid;
    } else {
        global_assistant.version_data.client_version = zpn_client_version_valid;
    }

    return ZPN_RESULT_NO_ERROR;
}

static int
assistant_cfg_conn_cb(struct fohh_connection*    connection,
                      enum fohh_connection_state conn_state,
                      void*                      cookie)
{
    int res = ZPN_RESULT_NO_ERROR;
    if (conn_state == fohh_connection_connected) {

        struct zpn_tlv tlv;
        tlv.type = zpn_fohh_tlv;
        tlv.conn.f_conn = connection;
        tlv.conn_incarnation = fohh_connection_incarnation(connection);
        tlv.tlv_incarnation = 0;

        if ((res = argo_register_structure(fohh_argo_get_rx(connection),
                                           zpn_version_ack_description,
                                           zpn_assistant_version_ack_cb,
                                           connection))) {
            ASSISTANT_LOG(AL_ERROR, "Could not register zpn_version_ack for assistant control connection %s",
                            fohh_description(connection));
            return res;
        }
        global_assistant.version_data.zpn_version_check_start_us = epoch_us();
        zpn_send_zpn_version(&tlv, zpn_tlv_conn_incarnation(&tlv), ZPATH_VERSION_MAJOR, ZPATH_VERSION_MINOR);

        if (global_assistant.alt_cloud_enabled) {
            /* send fohh info with capabilities to peer */
            const char *asst_capabilities[] = {ZPN_CLIENT_CAPABILITY_ALT_CLOUD_AWARE_STR};
            struct fohh_info fohh_cookie;
            memset(&fohh_cookie, 0, sizeof(fohh_cookie));
            fohh_cookie.capabilities_count = 1;
            fohh_cookie.capabilities = asst_capabilities;
            if (global_assistant.alt_cloud) {
                assistant_state_get_alt_cloud_with_lock(state.alt_cloud_name);
                fohh_cookie.current_alt_cloud = state.alt_cloud_name;
            }
            stats.fohh_info_capability_tx++;
            fohh_send_info_with_capability(connection, &fohh_cookie);
        }

        res = argo_register_structure(fohh_argo_get_rx(connection),
                                      zpn_broker_redirect_description,
                                      assistant_cfg_broker_redirect,
                                      connection);
        if (res) {
            ASSISTANT_LOG(AL_ERROR, "Could not register broker_redirect for config connection %s",
                          fohh_description(connection));
            return res;
        }
        ASSISTANT_LOG(AL_NOTICE, "Broker config connection successfully connected to Zscaler Cloud: %s",
                                    fohh_description(connection));
        zpn_fohh_worker_assistant_connect_config(fohh_connection_get_thread_id(connection));
    } else {
        ASSISTANT_LOG(AL_NOTICE, "Broker config connection to Zscaler Cloud closed: %s %s",
                                    fohh_description(connection), fohh_close_reason(connection));
        zpn_fohh_worker_assistant_disconnect_config(fohh_connection_get_thread_id(connection));
    }

    return res;
}
void
assistant_cfg_set_disable_client_version_check()
{
	set_wally_client_incompatible_version_check_disable_cli();
}
/*
 * Init the config connection to the wally
 * broker - can be NULL
 */
struct wally *
assistant_cfg_conn_init(const char*  broker,
                        struct wally_test_origin** wally_test_origin)
{
    char                        hostname[1000];
    struct wally_fohh_client*   wally_fohh_client_handle;
    struct wally*               assistant_wally;
    struct wally_origin*        remote_db;
    char                        sni_customer_domain_cfg[1000];
    char*                       cloud_name;
    char                        alt_cloud_name[256];
    char                        site_offline_domain[ASSISTANT_SITE_OFFLINE_DOMAIN_MAX_LEN] = {0};
    char*                       customer_domain;
    char                        default_remote_address_name[256];
    int                         dp_we_try_sitec = (global_assistant.sitec_preferred && global_assistant.is_site_offline_domain_exists && CFG_CONN_TO_SITEC);

    if (global_assistant.is_site_offline_domain_exists) {
        assistant_site_get_offline_domain_with_lock(site_offline_domain);
    }

    if (dp_we_try_sitec) {
        cloud_name = site_offline_domain;
    } else if (global_assistant.alt_cloud && global_assistant.alt_cloud_enabled) {
        assistant_state_get_alt_cloud_with_lock(alt_cloud_name);
        cloud_name = alt_cloud_name;
    } else {
        cloud_name = assistant_state_get_cloud_name();
    }

    customer_domain = assistant_state_get_customer_domain();

    if (broker) {
        snprintf(hostname, sizeof(hostname), "%s", broker);
    } else if (dp_we_try_sitec) {
        snprintf(hostname, sizeof(hostname), "%s.%s", ASSISTANT_SITE_DOMAIN_PREFIX, cloud_name);
    } else {
        snprintf(hostname, sizeof(hostname), "co2br.%s", cloud_name);
    }

    snprintf(default_remote_address_name, sizeof(default_remote_address_name), "co2br.%s", assistant_state_get_cloud_name());

    ASSISTANT_DEBUG_CFG("Init cfg, broker(%s) cloud_name(%s) customer_domain(%s)",
                        hostname, cloud_name, customer_domain);
    assistant_wally = wally_create("Assistant_Wally",
                                   1,
                                   zpath_debug_wally_endpoints_init,
                                   NULL,
                                   NULL,
                                   NULL);
    if (!assistant_wally) {
        ASSISTANT_LOG(AL_ERROR, "Could not create zpa connector config channel");
        return NULL;
    }

    /* Set dr running mode to "off" by default */
    strcpy(global_assistant.dr_running_mode, "off");

    if (global_assistant.enroll_version == 1) {
        /* V1 Style SNI: <customer domain>.acfg.<cloud_name> */
        snprintf(sni_customer_domain_cfg, sizeof(sni_customer_domain_cfg), "%s.acfg.%s", customer_domain, cloud_name);
    } else {
        /* V2 or V3 Style SNI: <assistant_id>.acfg.<cloud_name> */
        snprintf(sni_customer_domain_cfg, sizeof(sni_customer_domain_cfg), "%ld.acfg.%s", (long)global_assistant.gid, cloud_name);
    }

    if (!is_zpn_drmode_enabled()) {
        ASSISTANT_DEBUG_CFG("SNI for config connection(%s)", sni_customer_domain_cfg);
        wally_fohh_client_handle = wally_fohh_client_create_using_ctx(assistant_wally,
                                                                  NULL,
                                                                  hostname,
                                                                  sni_customer_domain_cfg,
                                                                  cloud_name,
                                                                  htons(ZPN_ASSISTANT_BROKER_PORT),
                                                                  assistant_cfg_conn_cb,
                                                                  dp_we_try_sitec ? global_assistant.assistant_to_private_cloud_ctx : global_assistant.assistant_to_public_cloud_ctx);
        if (!wally_fohh_client_handle) {
            ASSISTANT_LOG(AL_ERROR, "Could not create fohh origin, domain(%s) cloud(%s) via(%s)",
                    customer_domain, cloud_name, hostname);
            return NULL;
        }
        state.cfg_channel = wally_fohh_client_get_f_conn(wally_fohh_client_handle);

        /* Make the config connection sticky, just like the control connection */
        fohh_set_sticky(state.cfg_channel, 1);
        /*
         * the max timeout to flip to broker is defined as 60s, so make sure the backoff is smaller
         * that it so that sitec connection has the chance to be retried.
         */
        fohh_set_max_backoff(state.cfg_channel, ASSISTANT_STATE_ALT_CLOUD_CONN_MAX_INIT_TIME_S >> 1);
        fohh_connection_site_init(state.cfg_channel, site_offline_domain, ASSISTANT_SITE_DOMAIN_PREFIX, default_remote_address_name, 0, CFG_CONN_TO_SITEC,
                                  dp_we_try_sitec, global_assistant.assistant_to_private_cloud_ctx, global_assistant.assistant_to_public_cloud_ctx);
        fohh_connection_monitor_sanity(state.cfg_channel,
                                       assistant_state_fohh_connection_sanity_callback_with_lock,
                                       ASSISTANT_STATE_ALT_CLOUD_CONN_MAX_INIT_TIME_S);
        fohh_connection_set_default_sni(state.cfg_channel, assistant_state_get_cloud_name());
        /*
         * Set status interval address, which has the interval in seconds for sending fohh_status_request messages by connected broker.
         * Default status interval is 1 second for sending fohh_status_request messages.
         */
        fohh_set_status_interval(state.cfg_channel, &(global_assistant.cfg_status_interval));
        fohh_suppress_connection_event_logs(state.cfg_channel);
        //fohh_set_debug(state.cfg_channel, 0xffffffff);

        remote_db = wally_add_origin(assistant_wally,
                                 hostname,
                                 wally_fohh_client_handle,
                                 wally_fohh_register_for_index,
                                 wally_fohh_deregister_for_index,
                                 wally_fohh_set_cookie,
                                 wally_fohh_get_status,
                                 wally_fohh_add_table,
                                 NULL,
                                 wally_fohh_dump_state,
                                 1);
        if (!remote_db) {
            ASSISTANT_LOG(AL_ERROR, "Could not add remote orgin, domain(%s) cloud(%s) via(%s)",
                    customer_domain, cloud_name, hostname);
            return NULL;
        }
    } else {
        /*
         * This will create a local dr_wally test origin for assistant config state.
         * Data/rows for this test origin will be statically loaded based on the config json file into appropriate tables.
         * Data/rows for Assistant_Wally will be served from dr_wally test origin based on registration request.
         */
        zpn_dr_wally_init( "dr_wally",
                           assistant_wally,
                           wally_test_origin,
                           &remote_db);
        global_assistant.drmode_enabled = 1;
        zpn_dr_fill_running_mode(global_assistant.dr_running_mode, sizeof(global_assistant.dr_running_mode));
    }

    zpath_debug_wally_add(assistant_wally, 0);

    if (!(assistant_cfg_stats_description = argo_register_global_structure(ASSISTANT_CFG_STATS_HELPER))) {
        ASSISTANT_LOG(AL_NOTICE, "Unable to create assistant config stats desc");
        return NULL;
    }

    if (zpath_debug_add_read_command("dump the states and stats of config",
                                  "/assistant/cfg/dump",
                                  assistant_cfg_dump_all,
                                  NULL,
                                  NULL)) {
        ASSISTANT_LOG(AL_ERROR, "Could not register /assistant/cfg/dump to debug");
        return NULL;
    }

    return assistant_wally;
}


/*
 * Initializes the config required for the connector - step1
 */
int
assistant_cfg_init(struct wally *asst_wally, int64_t asst_gid)
{
    int res;

    res = assistant_cfg_assistant_int(asst_wally, asst_gid);
    if (res != ZPATH_RESULT_NO_ERROR) {
        ASSISTANT_LOG(AL_ERROR, "Could not initialize assistant table");
        return res;
    }

    res = assistant_cfg_version_int(asst_wally, asst_gid);
    if (res != ZPATH_RESULT_NO_ERROR) {
        ASSISTANT_LOG(AL_ERROR, "Could not initialize version table");
        return res;
    }

    res = assistant_cfg_sub_module_upgrade_int(asst_wally, asst_gid);
    if (res != ZPATH_RESULT_NO_ERROR) {
        ASSISTANT_LOG(AL_ERROR, "Could not initialize sub module upgrade table");
        return res;
    }

    return res;
}


/*
 * Initializes the config required for the connector - step2
 */
int
assistant_cfg_init_2(struct wally *asst_wally)
{
    int res;

    res = assistant_cfg_application_init(asst_wally, global_assistant.gid);
    if (res) {
        ASSISTANT_LOG(AL_ERROR, "Could not initialize application config");
        goto done;
    }

    res = assistant_cfg_application_group_init(asst_wally, global_assistant.gid);
    if (res) {
        ASSISTANT_LOG(AL_ERROR, "Could not initialize application group config");
        goto done;
    }

    res = assistant_cfg_pbroker_init(asst_wally);
    if (res) {
        ASSISTANT_LOG(AL_ERROR, "Could not init pbroker config");
        goto done;
    }

    res = assistant_cfg_pbroker_load_init(asst_wally);
    if (res) {
        ASSISTANT_LOG(AL_ERROR, "Could not init pbroker_load config");
        goto done;
    }

    res = assistant_cfg_site_init(asst_wally);
    if (res) {
        ASSISTANT_LOG(AL_ERROR, "Could not init zpn_site table config");
        goto done;
    }

done:
    return res;
}


/*
 * Initializes the config required for the connector - step3
 */
int
assistant_cfg_init_3(struct wally *asst_wally)
{
    int res;

    res = zpn_application_asst_multi_match_feature_flag_init(global_assistant.gid);
    if (res) {
        ASSISTANT_LOG(AL_ERROR, "Could not init config override for application multi match feature");
        return res;
    }

    res = assistant_cfg_map_assistant_to_assistant_group_init(asst_wally);
    if (res) {
        ASSISTANT_LOG(AL_ERROR, "Could not init assistant to assistant group mapping config");
        return res;
    }

    res = assistant_cfg_assistant_group_init(asst_wally);
    if (res) {
        ASSISTANT_LOG(AL_ERROR, "Could not init assistant groups config");
        return res;
    }

    res = assistant_cfg_map_assistant_group_to_server_group_init(asst_wally);
    if (res) {
        ASSISTANT_LOG(AL_ERROR, "Could not init assistant group to server group mapping config");
        return res;
    }

    res = assistant_cfg_server_group_init(asst_wally);
    if (res) {
        ASSISTANT_LOG(AL_ERROR, "Could not init server group config");
        return res;
    }

    res = assistant_cfg_map_server_group_to_app_init(asst_wally);
    if (res) {
        ASSISTANT_LOG(AL_ERROR, "Could not init server group to app mapping config");
        return res;
    }

    res = assistant_cfg_map_application_to_application_group_init(asst_wally);
    if (res) {
        ASSISTANT_LOG(AL_ERROR, "Could not init application to application group mapping config");
        return res;
    }

    res = assistant_cfg_map_server_group_to_server_init(asst_wally);
    if (res) {
        ASSISTANT_LOG(AL_ERROR, "Could not init server group to server mapping config");
        return res;
    }

    res = assistant_cfg_server_init(asst_wally);
    if (res) {
        ASSISTANT_LOG(AL_ERROR, "Could not init server config");
        return res;
    }

    res = assistant_cfg_waf_init(asst_wally);
    if (res) {
        ASSISTANT_LOG(AL_ERROR, "Could not init waf config");
        return res;
    }

    res = assistant_cfg_zpath_customer_init(asst_wally);
    if (res) {
        ASSISTANT_LOG(AL_ERROR, "Could not init zpath_customer cfg");
        return res;
    }

    return res;
}

/*
 * Walk all the configuration from assistant table. This is the starting point of the config walk.
 */
void
assistant_cfg_process()
{
    static int64_t prev_now_s = 0;
    int64_t now_s = epoch_s();

    /* coverity[sleep] Justification: Intentional sleep while holding lock as system is not currently running with capability required for NP */
    pthread_mutex_lock(&(global_assistant.config_lock));
    assistant_cfg_assistant_process();
    /* Check for sub module update every 30 secs */
    if ((now_s > (prev_now_s + 30)) &&
        !assistant_state_is_paused()) {
        assistant_cfg_sub_module_upgrade_process();
        prev_now_s = epoch_s();
    }
    pthread_mutex_unlock(&(global_assistant.config_lock));

}

/*
 * Log the status of the config connections. Called periodically
 */
void
assistant_cfg_log_status()
{
    if (state.cfg_channel) {
        char uptime_str[FOHH_MAX_UPTIME_BUF_LEN];
        char max_rtt_us_str[FOHH_RTT_SINCE_LAST_QUERY_MSG_STR_MAX];
        fohh_connection_max_rtt_to_string(state.cfg_channel, max_rtt_us_str, sizeof(max_rtt_us_str));
        fohh_connection_reset_max_rtt(state.cfg_channel);

        ASSISTANT_LOG(AL_NOTICE, "Broker config connection, state %s, %s uptime %s%s, disconnect_duration_s %"PRId64"",
                      fohh_state(state.cfg_channel), fohh_description(state.cfg_channel),
                      fohh_get_uptime_str(state.cfg_channel, uptime_str, sizeof(uptime_str)),
                      (fohh_get_state(state.cfg_channel) == fohh_connection_connected)? max_rtt_us_str: "",
                      fohh_conn_get_current_disconnect_duration_s(state.cfg_channel));
        stats.log_status_success++;
    } else {
        ASSISTANT_LOG(AL_NOTICE, "Broker config connection - NONE");
        stats.log_status_none++;
    }
}

static void
assistant_cfg_stats_dump(struct zpath_debug_state*  request_state)
{
    char        jsonout[10000];

    if (ARGO_RESULT_NO_ERROR == argo_structure_dump(assistant_cfg_stats_description,
                                                    &stats, jsonout, sizeof(jsonout), NULL, 1)) {
        ZDP("%s\n", jsonout);
    }
}

static void
assistant_cfg_state_dump(struct zpath_debug_state*  request_state)
{
    if (state.cfg_channel) {
        char uptime_str[FOHH_MAX_UPTIME_BUF_LEN];
        char max_rtt_us_str[FOHH_RTT_SINCE_LAST_QUERY_MSG_STR_MAX];
        fohh_connection_max_rtt_to_string(state.cfg_channel, max_rtt_us_str, sizeof(max_rtt_us_str));
        ZDP("Broker config connection, state: %s, %s uptime %s%s, disconnect_duration_s %"PRId64"\n",
            fohh_state(state.cfg_channel), fohh_description(state.cfg_channel), fohh_get_uptime_str(state.cfg_channel, uptime_str, sizeof(uptime_str)),
            (fohh_get_state(state.cfg_channel) == fohh_connection_connected)? max_rtt_us_str: "",
            fohh_conn_get_current_disconnect_duration_s(state.cfg_channel));
    } else {
        ZDP("Broker config connection - NONE\n");
    }
}


static int
assistant_cfg_dump_all(struct zpath_debug_state*  request_state,
                       const char**               query_values,
                       int                        query_value_count,
                       void*                      cookie)
{
    assistant_cfg_state_dump(request_state);
    assistant_cfg_stats_dump(request_state);
    return ZPATH_RESULT_NO_ERROR;
}

const char*
assistant_cfg_get_connection_description()
{
    if (!state.cfg_channel) {
        return "";
    }

    return fohh_description(state.cfg_channel);
}

struct fohh_connection*
get_cfg_channel()
{
    return state.cfg_channel;
}
void assistant_cfg_conn_reset_count()
{
    stats.alt_cloud_resets++;
}
