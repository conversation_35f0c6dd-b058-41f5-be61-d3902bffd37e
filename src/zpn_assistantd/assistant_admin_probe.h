/*
 * assistant_admin_probe.h. Copyright (C) 2021 Zscaler Inc, All Rights Reserved
 */

#ifndef _ASSISTANT_ADMIN_PROBE_H_
#define _ASSISTANT_ADMIN_PROBE_H_

#include "wally/wally_private.h"
#include "admin_probe/admin_probe_rpc.h"


int assistant_admin_probe_init(struct wally *asst_wally, int64_t asst_gid, int64_t customer_gid, int is_np_command_probe, struct argo_log_collection *event_log, int is_zpath_config_override_inited);
int assistant_admin_probe_tx_task_update(void* status, int is_np_command_probe);
void assistant_admin_probe_restart_evaluable();

int
assistant_admin_probe_stats_fill(void*     cookie,
                                 int       counter,
                                 void*     structure_data);

#endif /* _ASSISTANT_ADMIN_PROBE_H_ */
