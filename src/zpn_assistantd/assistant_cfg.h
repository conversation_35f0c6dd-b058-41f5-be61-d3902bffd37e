/*
 * assistant_cfg.h. Copyright (C) 2019 Zscaler Inc, All Rights Reserved
 *
 */

#ifndef _ASSISTANT_CFG_H_

#include "wally/wally_test_origin.h"

struct wally *
assistant_cfg_conn_init(const char*  broker,
                        struct wally_test_origin **wally_test_origin);
/*
 * Phase1 init of config
 */
int assistant_cfg_init(struct wally *asst_wally, int64_t asst_gid);

/*
 * Phase2 init of config
 */
int assistant_cfg_init_2(struct wally *asst_wally);


/*
 * Phase3 init of config
 */
int
assistant_cfg_init_3(struct wally *asst_wally);

/*
 * Start reading the config.
 */
void
assistant_cfg_process();

/*
 * Log the status of the config component. Called every 1min
 */
void assistant_cfg_log_status();

void
assistant_cfg_check_insane();

const char*
assistant_cfg_get_connection_description();

struct fohh_connection*
get_cfg_channel();

void assistant_cfg_conn_reset_count();

void assistant_cfg_set_disable_client_version_check();

#define _ASSISTANT_CFG_H_
#endif // _ASSISTANT_CFG_H_
