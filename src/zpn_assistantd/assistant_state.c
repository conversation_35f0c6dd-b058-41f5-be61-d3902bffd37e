/*
 * assistant_state.c. Copyright (C) 2018 Zscaler Inc. All Rights Reserved.
 *
 * 1. This file is meant to be moved to zpn_assistantd directory, hence the file name prefix is not starting with zpn_
 * 2. struct zpn_assistant_state already captures lot of states of the connector. I see two problem with that,
 *  a. to track any manipulation of data in that state object, I will have to search around the entire codebase.
 *  b. All files which have to be aware of the state of the connector have to carry the burden of taking a pointer to
 *  the state object. It would be nice if the code don't have to worry about the state pointer to get to know about a
 *  particular attribute of the state object.
 * With this being said, the hope is that eventually struct zpn_assistant_state will move here. This file is a place
 * holder for that.
 *
 * This file holds all the runtime state of the connector. We want to collect all the runtime states at one place to
 * be able to easily see all the state all the other files/data structures depend upon.
 *
 * This file is meant to capture only the runtime states, if there is a config state, that will have to get into
 * assistant_cfg.c
 */
#include <sys/stat.h>
#if __linux__
#include <sys/statfs.h>
#include <linux/magic.h>
#endif
#include <limits.h>

#include "zpn_assistantd/zpn_assistant_private.h"
#include "zpn_assistantd/zpn_assistant_private_compiled.h"
#include "zpn_assistantd/assistant_monitor.h"
#include "zpath_misc/zpath_version.h"
#include "wally/wally_test_origin.h"

#include "zpn_assistantd/assistant_state.h"
#include "zpn_assistantd/assistant_features.h"
#include "zpn_assistantd/assistant_cfg_version.h"
#include "zpn_assistantd/assistant_cfg_assistant_group.h"
#include "zpn/assistant_log.h"
#include "zpn_assistantd/assistant_log_tx.h"
#include "zpn_assistantd/assistant_broker_control.h"
#include "zpn_assistantd/assistant_pbroker_control.h"
#include "zpn_assistantd/assistant_assert.h"
#include "zpn_assistantd/assistant_cfg.h"
#include "zpn_assistantd/assistant_cfg_override.h"
#include "zpn_assistantd/assistant_stats.h"
#include "zhw/zhw_id.h"
#include "zpn/zpn_rpc.h"
#include "zpath_lib/zpath_cloud.h"
#include "zpath_misc/zpath_misc.h"
#include <openssl/rand.h>
#include "zpn_waf/zpn_waf_lib.h"
#include "fohh/fohh.h"
#include "fohh/fohh_private.h"
#include "zpath_lib/zpath_system_linux.h"
#include "zpn_assistantd/assistant_np_connector.h"
#include "np_connector/np_connector.h"
#include "np_connector/np_connector_bgp.h"
#include "np_lib/np.h"
#include "np_lib/np_bgp.h"
#include "np_lib/np_frr_utils.h"
#include "zpath_lib/zpa_cloud_config.h"
#include "zpn/zpn_mconn_bufferevent.h"
#include "zpath_lib/zpath_config_override_keys.h"
#include "zpn_assistantd/assistant_features.h"
#include "zpath_lib/zpath_capability_util.h"

#define ASSISTANT_STATE_DAY_STR_LEN 21
#define ASSISTANT_STATE_HOUR_MIN_SEC_STR_LEN 5

#define ASSISTANT_ALT_CLOUD_CONFIG_FILE         "alt_domain.cfg"
#define ASSISTANT_ALT_CLOUD_FEATURE_STR         "ALT_CLOUD_ENABLED"
#define ASSISTANT_ALT_CLOUD_DOMAIN_STR          "ALT_DOMAIN"
#define ASSISTANT_ALT_DOMAIN_MAX_LEN            100
#define ASSISTANT_ALT_CLOUD_CFG_FILE_MAX_SIZE   512
#define ASSISTANT_FIREDRILL_SWITCH_VALUE        0xDEADBEEF

/* Global variable to keep the runtime OS information during asst start time */
char g_asst_runtime_os[FOHH_MAX_NAMELEN] = {0};

int effective_asst_configured_capabilities[ASSISTANT_STATE_MAX_CAPABILITIES];
int permitted_asst_configured_capabilities[ASSISTANT_STATE_MAX_CAPABILITIES];

const char *asst_configured_capabilities_str[ASSISTANT_STATE_MAX_CAPABILITIES] = {
    "cap_net_admin",
    "cap_net_bind_service",
    "cap_net_raw",
    "cap_sys_boot",
    "cap_sys_nice",
    "cap_sys_time"
};

static struct argo_structure_description*   zpn_assistant_state_desc;
static struct zpath_allocator asst_allocator = ZPATH_ALLOCATOR_INIT("asst");

static struct assistant_state {                                         /* _ARGO: object_definition */
    int                 is_ready;                                       /* _ARGO: integer */
    int                 is_paused;                                      /* _ARGO: integer */
    char                *paused_reason;                                 /* _ARGO: string */
    int64_t             max_allowed_fohh_downtime_s;                    /* _ARGO: integer */
    int                 is_admin_probe_pause_for_restart_process;       /* _ARGO: integer */
    int                 is_admin_probe_pause_for_restart_system;        /* _ARGO: integer */
    /*
     * derived name is asst-<assistant_gid><tenant_domain>. This is the CNAME coming from the certificate which is
     * assigned by the enrollment server. Use this only if you want to log the name before cfg_asst_name is found by
     * the process.
     */
    char                derived_asst_name[ASSISTANT_NAME_MAX_LEN];      /* _ARGO: string */
    /*
     * See struct zpn_assistant for the story behind this name. Summary: use this name for any logging in connector
     * code.
     */
    char                cfg_asst_name[ASSISTANT_NAME_MAX_LEN];          /* _ARGO: string */
    char                sarge_version[ZPATH_VERSION_STR_MAX_LEN];       /* _ARGO: string */
    char                cloud_name[ZPATH_VERSION_STR_MAX_LEN];          /* _ARGO: string */
    /*
     * FIXME: customer_domain is used only when v1 enrollment is used. Once v1 enrollment code is cleaned up,
     * we can remove this variable.
     */
    char                custome_domain[ZPATH_VERSION_STR_MAX_LEN];      /* _ARGO: string */
    char                current_working_directory[PATH_MAX];            /* _ARGO: string */
    int64_t             next_restart_time_cloud_s;                      /* _ARGO: integer */
    /*
     * if local time is 12:00, and cloud time is 12:01, this will value will be 1000000. (not negative)
     * if local time is 12:01, and cloud time is 12:00, this will value will be -1000000. (negative)
     */
    int64_t             local_time_lags_cloud_time_delta_us;            /* _ARGO: integer */
    struct event_base*  asst_health_base;
    struct zevent_base* asst_health_zbase;
    int                 is_reading_config;                              /* _ARGO: integer */
    /*
     * We assume that we are working in pbroker environment if we have seen atleast one config entry related to pbroker.
     */
    int                 is_pbroker_environment;                         /* _ARGO: integer */

    /*
     * We assume that we are working in pbroker dr mode environment if we have dr activated
     */
    int                 is_dr_mode_environment;                         /* _ARGO: integer */



    /*
     * is_dev_environment is TRUE when I am running in DEV or QA cloud, is_high_gov_environment is TRUE when I'm running in gov cloud,
     * is_moderate_gov_environment is TRUE when I'm in moderate gov cloud, is_high_staging_environment is TRUE when I'm runnning in
     * high staging cloud. It is a derived value, from cloud_name, so not settable
     */
    int                 is_dev_environment;                             /* _ARGO: integer */
    int                 is_high_gov_environment;                        /* _ARGO: integer */
    int                 is_moderate_gov_environment;                    /* _ARGO: integer */
    int                 is_high_staging_environment;                    /* _ARGO: integer */
    int                 is_container_env;                               /* _ARGO: integer */
    int                 is_zscaler_os;                                  /* _ARGO: integer */

    int                 is_atleast_one_asst_grp_cfg_enabled;            /* _ARGO: integer */

    int                 is_this_asst_cfg_enabled;                       /* _ARGO: integer */
    int                 cfg_disabled;                                   /* _ARGO: integer */
    /* translates to ASC in RPC messages towards ubroker to dispatcher */
    int                 capability_sticky_cache_enabled;                /* _ARGO: integer */
    /* translates to APU in RPC messages towards ubroker to dispatcher */
    int                 capability_pathing_from_ubrk_enabled;           /* _ARGO: integer */

    int                 number_of_hw_id_changed;                        /* _ARGO: integer */
    int64_t             hw_id_changed_time_us[ZHW_TOTAL_HW_USED];       /* _ARGO: string */

    int64_t             assistant_uptime_s;                             /* _ARGO: integer */
    uint32_t            configured_cpus;                                /* _ARGO: integer */
    uint32_t            available_cpus;                                 /* _ARGO: integer */
    uint32_t            fohh_threads;                                   /* _ARGO: integer */
    int                 swap_config;                                    /* _ARGO: integer */

    /* installed guacd subcomponent package version
       - "" indicates unable to identify the installed guacd package version info
       - "none" indicates the absence of guacd package  */
    char                guacd_version[ZPATH_VERSION_STR_MAX_LEN];       /* _ARGO: string */

    int                 on_alt_cloud;                                   /* _ARGO: integer */
    int                 alt_cloud_enabled;                              /* _ARGO: integer */
    int                 alt_cloud_resets;                               /* _ARGO: integer */
    int                 alt_cloud_resets_via_toggle;                    /* _ARGO: integer */
    int                 alt_cloud_conn_init_failures;                   /* _ARGO: integer */
    char                alt_cloud[FOHH_MAX_NAMELEN];                    /* _ARGO: string */
    int                 cgroup_version;                                 /* _ARGO: integer */

    int64_t             allocator_libevent_max_bytes;                   /* _ARGO: integer */
    int64_t             allocator_libevent_used_bytes;                  /* _ARGO: integer */
    int64_t             allocator_libevent_used_percent;                /* _ARGO: integer */
    int64_t             allocator_libevent_out_queue_allowed_bytes;     /* _ARGO: integer */
    int64_t             allocator_libevent_sys_mem_total_bytes;         /* _ARGO: integer */
} state;
#include "zpn_assistantd/assistant_state_compiled_c.h"


/*
 * Lets pre-cook and keep the state RPC ready to be sent out.
 */
static struct zpn_asst_state   asst_state_rpc;


static struct argo_structure_description* assistant_state_desc;
static void assistant_state_record_sarge_version();
static void assistant_state_dbg_init();


/*
 * Capability sanity check.
 * Connector should now have all the capabilities set by sarge:
 *     cap_net_admin, cap_net_bind_service, cap_net_raw, cap_sys_boot, cap_sys_nice, cap_sys_time
 *
 * Note: if connector is in container env, we don't enforce cap_sys_boot.
 */
#ifdef __linux__
#include <sys/capability.h>
static int assistant_state_get_capabilities(const char * const *cap_name, int cap_len, int *effective, int *permitted)
{
    pid_t pid;
    cap_t cap;
    cap_value_t cap_list[cap_len];
    cap_flag_value_t cap_flags_value;

    pid = getpid();
    cap = cap_get_pid(pid);
    if (cap == NULL) {
        return ZPATH_RESULT_ERR;
    }

    /* dump the capabilies */
    for (int i = 0; i < cap_len; i++) {
        cap_from_name(cap_name[i], &cap_list[i]);
        cap_get_flag(cap, cap_list[i], CAP_EFFECTIVE, &cap_flags_value);
        effective[i] = (cap_flags_value == CAP_SET) ? 1 : 0;
        cap_get_flag(cap, cap_list[i], CAP_PERMITTED, &cap_flags_value);
        permitted[i] = (cap_flags_value == CAP_SET) ? 1 : 0;
    }
    return ZPATH_RESULT_NO_ERROR;
}
#else
static int assistant_state_get_capabilities(const char * const *cap_name, int cap_len, int *effective, int *permitted)
{
    return ZPATH_RESULT_NOT_IMPLEMENTED;
}
#endif

/*
 * Container env:
 * Capabilities will be set manually when container is created. Usually cap_sys_boot is not set by customer (see ET-32579
 * for details). So we need to skip setting & checking the cap_sys_boot capability if we're in container env.
 */
static void
assistant_state_check_capabilities() {
    const char * const *cap_list = NULL;
    int cap_count = 0;
    int *effective = NULL;
    int *permitted = NULL;

    static int container_effective_caps[ASSISTANT_STATE_MAX_CAPABILITIES];
    static int container_permitted_caps[ASSISTANT_STATE_MAX_CAPABILITIES];

    int is_container = assistant_state_is_container_env();
    if (is_container) {
        cap_list = zpath_get_final_capabilities(&cap_count);
        effective = container_effective_caps;
        permitted = container_permitted_caps;
    } else {
        cap_list = asst_configured_capabilities_str;
        cap_count = ASSISTANT_STATE_MAX_CAPABILITIES;
        effective = effective_asst_configured_capabilities;
        permitted = permitted_asst_configured_capabilities;
    }

    if (!cap_list || cap_count == 0) {
        ASSISTANT_LOG(AL_ERROR, "No capabilities configured or retrieved");
        return;
    }

    int res = assistant_state_get_capabilities(cap_list, cap_count, effective, permitted);
    if (res) {
        ASSISTANT_LOG(AL_NOTICE, "Could not get assistant capability info: %s", zpath_result_string(res));
        return;
    }

    int capability_check_failed = 0;
    ASSISTANT_LOG(AL_INFO, "Running capability check; container env: %s, cap_count: %d",
              is_container ? "yes" : "no", cap_count); //TBD remove
    for (int i = 0; i < cap_count; i++) {
        ASSISTANT_LOG(AL_INFO, "Cap to check: %s", cap_list[i]); //TBD remove
        if (is_container && strcmp(cap_list[i], "cap_sys_boot") == 0) {
            continue;  // Skip cap_sys_boot in container
        }

        if (!effective[i] || !permitted[i]) {
            capability_check_failed = 1;
            ASSISTANT_LOG(AL_CRITICAL,
                          "Assistant capability %s not set! EFFECTIVE: %s, PERMITTED: %s",
                          cap_list[i],
                          effective[i] ? "SET" : "NOT SET",
                          permitted[i] ? "SET" : "NOT SET");
        }
    }

    if (capability_check_failed) {
        ASSISTANT_LOG(AL_ERROR, "Assistant capability check failed, please ensure the required capabilities are set");
    } else {
        const char *mode_str = getenv("ZPA_CAPABILITY_MODE");
        ASSISTANT_LOG(AL_NOTICE,
                      "Assistant capability check passed (mode: %s, container: %s)",
                      mode_str ? mode_str : "default",
                      is_container ? "yes" : "no");
    }
}

/*
 * The cloud can instruct me to PAUSE my operation for whatever reason. The expectation on other components in the
 * connector is to slow down and get ready to STOP - just like yellow light in traffic signal.
 *
 * No lock is taken as we expect the SET operation to be always called from ONE place - think the instruction from
 * cloud to pause could not be handled by multiple threads at the same time.
 *
 * We don't expect the state to move from PAUSE to ACTIVE, hence there is no reset_pause() function.
 *
 * We also set the pause reason when pause is set.
 *
 * We will be in a real bad shape if the connector is stuck in PAUSE state for a long time.
 * assistant_state_pause_breaker is intended to help this case where if it see the connector is hanging in PAUSE
 * state, it will restart the connector after 6 minutes. This code is not expected to be hit in production, hence the
 * hard assert. It is here, so that any bug creeping up in restart code logic don't hurt us.
 *
 * FIXME:
 * 1. Log the number of outstanding requests - every 1min
 * 2. Log the number of bits rx/tx of the paused state - every 1min & total
 * 3. In future, we can keep the system in PAUSED state from 5..360mins depending upon the number of outstanding
 * requests and the bits passing around. This will help cases where there is a large file being downloaded, uploaded
 * and would be nice to not break the connection.
 *  a. Before doing this, the GUI should start showing the system in PAUSED state - may be as yellow coloured connector.
 *  b. There should be a configurable option, in case the administrator wants to override this setting. There can be
 *  customers with 200 connectors in 100 connector group. 100 connector being in PAUSED state for 6hrs in a peak
 *  business hour is not desirable for some customers. Lets call this state CONTROL_STOP_EXTENDED state.
 * 4. I should call the state as ACTIVE and CONTROL_STOPPED state!
 */
#define ASSISTANT_STATE_PAUSE_BREAKER_TIME_US (int64_t)(6 * 60 * 1000 * 1000ll)
static void
assistant_state_pause_breaker(void* cookie1,
                              void* cookie2)
{
    if (global_assistant.broker_control) {
        zpn_send_zpn_asst_restart_reason_on_fohh(global_assistant.broker_control,
                                                 fohh_connection_incarnation(global_assistant.broker_control),
                                                 global_assistant.gid,
                                                 ASST_RSTR_IN_PAUSE_STATE);
    } else {
        ASSISTANT_LOG(AL_NOTICE, "Assistant %s with id %"PRId64" is unable to send assistant restart reason %s to broker because control connection is not established !",
                                  assistant_state_get_configured_name(), global_assistant.gid, ASST_RSTR_IN_PAUSE_STATE);
    }

    sleep(1); // Add sleep to allow async RPC to go through
    ASSISTANT_ASSERT_HARD(0, "Connector in PAUSE state for a long period, restarting..");
    exit(1);
}

static void
assistant_state_set_pause(int is_admin_probe, int is_ut)
{
#define PAUSE_REASON_CERT_EXPIRY "certificate expiry"
#define PAUSE_REASON_UPGRADE     "software upgrade"
#define PAUSE_REASON_ADMIN_PROBE_PROCESS_RESTART "admin probe process restart"
#define PAUSE_REASON_ADMIN_PROBE_SYSTEM_RESTART "admin probe system restart"
#define PAUSE_REASON_UT "UT pause"
    if (state.is_paused) {
        goto done;
    }
    state.is_paused = 1;

    zevent_defer(assistant_state_pause_breaker,  0, 0, ASSISTANT_STATE_PAUSE_BREAKER_TIME_US);

    if (is_admin_probe) {
        if (assistant_state_is_admin_probe_pause_for_restart_process()) {
            state.paused_reason = PAUSE_REASON_ADMIN_PROBE_PROCESS_RESTART;
        } else {
            state.paused_reason = PAUSE_REASON_ADMIN_PROBE_SYSTEM_RESTART;
        }
        goto done;
    }

    if (is_ut) {
        state.paused_reason = PAUSE_REASON_UT;
        goto done;
    }

    if (0 == state.next_restart_time_cloud_s) {
        state.paused_reason = PAUSE_REASON_CERT_EXPIRY;
    } else if (state.next_restart_time_cloud_s < assistant_state_get_current_time_cloud_s()) {
        state.paused_reason = PAUSE_REASON_CERT_EXPIRY;
    } else {
        ASSISTANT_ASSERT_HARD(global_assistant.cert_force_re_enroll_time_cloud_s, "unexpected code path, please report to customer support team");
        if (global_assistant.cert_force_re_enroll_time_cloud_s < state.next_restart_time_cloud_s) {
            state.paused_reason = PAUSE_REASON_CERT_EXPIRY;
        } else {
            state.paused_reason = PAUSE_REASON_UPGRADE;
        }
    }
done:
    return;
}
int
assistant_state_is_paused()
{
    return state.is_paused;
}
/*
 * Evaluate every second to see if this connector should enter into PAUSE-ed state. PAUSE if we passed the last active
 * time allowed for the connector.
 *
 * Note that connector PAUSE only in case of planned downtime - like cert expiry, planned reboot. It DOESNOT PAUSE in
 * case of unplanned events like runtime error, trouble connecting to cloud for a long time, etc..
 */
void
assistant_state_pause_evaluate()
{
    int64_t        cloud_now_s;
    int64_t        cloud_max_s;

    if (assistant_state_is_paused()) {
        return;
    }
    cloud_max_s = assistant_state_get_inactive_time_cloud_s();
    cloud_now_s = assistant_state_get_current_time_cloud_s();
    if (cloud_now_s >= cloud_max_s) {
        assistant_state_set_pause(0, 0);
        ASSISTANT_LOG(AL_INFO, "Connector(ID = %"PRId64") (Name = %s) entering PAUSE mode because of - %s",
                      global_assistant.gid, assistant_state_get_configured_name(), assistant_state_get_pause_reason());
    }

    if (assistant_state_is_admin_probe_pause_for_restart_process() || assistant_state_is_admin_probe_pause_for_restart_system()) {
        assistant_state_set_pause(1, 0);
        ASSISTANT_LOG(AL_INFO, "Connector(ID = %"PRId64") (Name = %s) entering PAUSE mode because of - %s",
                      global_assistant.gid, assistant_state_get_configured_name(), assistant_state_get_pause_reason());
    }
}
char *
assistant_state_get_pause_reason()
{
    if (!assistant_state_is_paused()) {
        return "N/A";
    }

    return state.paused_reason;
}

/*
 * state.is_admin_probe_pause_for_restart_process and state.is_admin_probe_pause_for_restart_system
 * will be checked every 1 second from main thread.
 *
 * once main thread finds out either value becomes one, it will enter pause state mode, and restart after 30s
 */
void
assistant_state_set_admin_probe_pause_for_process_restart()
{
    state.is_admin_probe_pause_for_restart_process = 1;
}


void
assistant_state_set_admin_probe_pause_for_system_restart()
{
    state.is_admin_probe_pause_for_restart_system = 1;
}

int
assistant_state_is_admin_probe_pause_for_restart_process()
{
    return state.is_admin_probe_pause_for_restart_process;
}

int
assistant_state_is_admin_probe_pause_for_restart_system()
{
    return state.is_admin_probe_pause_for_restart_system;
}


/*
 * The epoch_s cloud_time that this connector can say to the ZPA that it will not be able to serve. Remember that
 * this is till the time the connector can say that it will service, build states and so on - think control states
 * building and managing. But if there are any active forwarding going on, it will continue to happen till the
 * connector goes DOWN.
 *
 * In the last STOP_HEALTH_BEFORE_RESTART_TIME_SECS no change is allowed to happen. This is based on the assumption that
 * AUM gives atleast STOP_HEALTH_BEFORE_RESTART_TIME_SECS time before it ask the connector to restart. This will also
 * make sure that if we have found that the endpoint once, there is no going back - if we go back and forth, the
 * function becomes unpredictable. Also think about the cases where XUI/ZADMIN there is an option "Reboot" which
 * gives 60 seconds for the connector to restart.
 *
 * It is among any of these factors,
 *  a. global_assistant.cert_force_re_enroll_time_cloud_s (not global_assistant.cert_validity_end_time) - we can't send
 *  health validity more than this. We expect the cert validity to be initialized when we reach here. So assert if
 *  there is a mistake in the understanding. Note that we are not waiting till global_assistant.cert_validity_end_time
 *  to put on the brakes because after 99% of the cert expiry, the connector will keep restarting to make sure it
 *  re-enrolls with new certificate.
 *
 *  b. If this connector is a lone_warrior, it should serve till it go-down - serve till the last breath. In this
 *  case. So don't worry, when it is going to go restart, just serve till the certificate allows it to do so.
 *
 *  c. global_assistant.next_restart_time. Note that this can be NOT SET in case cloud haven't attempted to upgrade
 *  the connector. So check if it is non-zero. Also, ignore this if this a old config. We care only if it is a
 *  instruction to upgrade ourselves in the future.
 *
 *  d. STOP_HEALTH_BEFORE_RESTART_TIME_SECS before the connector is going to be in PAUSED state.
 *
 *  (eg)
 *  a. If current cloud time is 9:00am; next restart time is 9:30am; cert re-enroll is somewhere next year; return
 *  9:25am;
 *  b. If current cloud time is 9:27am; next restart time is 9:30am; cert re-enroll is somewhere next year; return
 *  9:25am;
 *  c. If current cloud time is 9:40am; next restart time is 9:30am; cert re-enroll is somewhere next year; return
 *  cert re-enroll time.
 */
int64_t
assistant_state_get_inactive_time_cloud_s()
{
    int64_t        last_expiry_epoch_cloud_s;
    static int64_t frozen_last_expiry_epoch_cloud_s = 0;
    int            freeze_expiry_time;
    int64_t        time_cloud_s;

    if (frozen_last_expiry_epoch_cloud_s) {
        return frozen_last_expiry_epoch_cloud_s;
    }

    assert(global_assistant.cert_force_re_enroll_time_cloud_s);
    last_expiry_epoch_cloud_s = global_assistant.cert_force_re_enroll_time_cloud_s;

    time_cloud_s = assistant_state_get_current_time_cloud_s();
    if ((state.next_restart_time_cloud_s) &&
        (state.next_restart_time_cloud_s > time_cloud_s) &&
        (last_expiry_epoch_cloud_s > state.next_restart_time_cloud_s)) {
        last_expiry_epoch_cloud_s = state.next_restart_time_cloud_s;
    }

    /* lone warrior or not, freeze 5mins before the inactive time */
    freeze_expiry_time = 0;
    if ((last_expiry_epoch_cloud_s - time_cloud_s) < STOP_HEALTH_BEFORE_RESTART_TIME_SECS) {
        freeze_expiry_time = 1;
    }
    if (assistant_cfg_version_is_lone_warrior()) {
        goto done;
    }
    last_expiry_epoch_cloud_s -= STOP_HEALTH_BEFORE_RESTART_TIME_SECS;

done:
    if (freeze_expiry_time) {
        frozen_last_expiry_epoch_cloud_s =  last_expiry_epoch_cloud_s;
    }
    return last_expiry_epoch_cloud_s;

}


/*
 * The last epoch_s local_time that this connector can say to the ZPA that it will be able to serve.
 */
int64_t
assistant_state_get_inactive_time_local_s()
{
    int64_t        asst_inactive_time_cloud_s;

    asst_inactive_time_cloud_s = assistant_state_get_inactive_time_cloud_s();
    return asst_inactive_time_cloud_s - (state.local_time_lags_cloud_time_delta_us/1000000l);
}

/*
 * Convert from current system time to cloud time (seconds)
 */
int64_t
assistant_state_get_current_time_cloud_s()
{
    return epoch_s() + (state.local_time_lags_cloud_time_delta_us/1000000l);
}

/*
 * Convert from current system time to cloud time (micro seconds)
 */
int64_t
assistant_state_get_current_time_cloud_us()
{
    return epoch_us() + state.local_time_lags_cloud_time_delta_us;
}

static void assistant_get_waf_state()
{
    global_assistant.num_websocket_upgrades = g_waf_stats.num_websocket_upgrades;
    global_assistant.num_websocket_inspections= g_waf_stats.num_websocket_inspections;
    global_assistant.num_mtunnels_total_waf_api_traffic = g_waf_stats.num_api_detection;
    global_assistant.num_mtunnels_inspect_waf_api_traffic = g_waf_stats.num_api_inspection;

    return;
}

int assistant_state_dump(struct zpath_debug_state*      request_state,
                         const char**                   query_values,
                         int                            query_value_count,
                         void*                          cookie)
{
    char        jsonout[10000];

    /* Populate the WAF stats */
    assistant_get_waf_state();

    ZDP("Dump state ..\n");
    if (ARGO_RESULT_NO_ERROR == argo_structure_dump(assistant_state_desc, &state, jsonout, sizeof(jsonout), NULL, 1)) {
        ZDP("%s\n", jsonout);
    }
    if (ARGO_RESULT_NO_ERROR == argo_structure_dump(zpn_assistant_state_desc, &global_assistant, jsonout,
                                                    sizeof(jsonout), NULL, 1)) {
        ZDP("%s\n", jsonout);
    }

    return ZPATH_RESULT_NO_ERROR;
}

int assistant_state_ut_pause(struct zpath_debug_state*      request_state,
                             const char**                   query_values,
                             int                            query_value_count,
                             void*                          cookie)
{
    assistant_state_set_pause(0, 1);
    return ZPATH_RESULT_NO_ERROR;
}


/*
 * Return the max number of seconds that either the wally or control channel can be DOWN.
 *
 * It can so happen that all the connectors in a connector group could be detected as down at the same time and
 * result in restart.  There are two levels of protection to cover 99.99% of the cases,
 * 1. There is already usec level of randmoness from the fohh, as it runs the state machine every second. So the two
 * connectors in a group can vary by upto a second on when it reboots.
 * 2. This function introduces a 20mins(in seconds unit) of randomnes by choosing a insane timer between [40..60]
 * minutes.
 * I still leave 0.01% out to luck - i.e both connector in the same connector group rebooting and causing service
 * outage at the same time.
 */
#define ASSISTANT_STATE_INSANE_TOLERANCE_TIME_MINS          40
#define ASSISTANT_STATE_INSANE_TOLERANCE_TIME_DELTA_MINS    20
int64_t
assistant_state_get_max_allowed_connection_downtime_s()
{

    if (0 == state.max_allowed_fohh_downtime_s) {
        int64_t     max_fohh_downtime_delta_s;
        int64_t     fohh_downtime_delta_s;
        uint64_t    random_number;
        max_fohh_downtime_delta_s = ASSISTANT_STATE_INSANE_TOLERANCE_TIME_DELTA_MINS * 60;
        if (1 != RAND_bytes((unsigned char *)&random_number, sizeof(uint64_t))) {
            ASSISTANT_LOG(AL_ERROR, "%"PRId64": Connection monitoring time is not randomized properly, please report to "
                    "customer support team", global_assistant.gid);
        }
        fohh_downtime_delta_s = random_number % max_fohh_downtime_delta_s;
        state.max_allowed_fohh_downtime_s = (ASSISTANT_STATE_INSANE_TOLERANCE_TIME_MINS * 60) + fohh_downtime_delta_s;
    }

    return state.max_allowed_fohh_downtime_s;
}


void assistant_state_set_derived_name(const char *asst_name)
{
    strncpy(state.derived_asst_name, asst_name, sizeof(state.derived_asst_name) - 1);
}
char* assistant_state_get_derived_name()
{
    return state.derived_asst_name;
}

void assistant_state_set_configured_name(const char *asst_name)
{
    if (NULL == asst_name) {
        ASSISTANT_ASSERT_SOFT((NULL != asst_name), "assistant name is configured as NULL - not expected");
        strncpy(state.cfg_asst_name, "Unknown name", sizeof(state.cfg_asst_name) - 1);
    } else {
        strncpy(state.cfg_asst_name, asst_name, sizeof(state.cfg_asst_name) - 1);
    }
}
char* assistant_state_get_configured_name()
{
    return state.cfg_asst_name;
}

int assistant_state_set_cloud_name(const char *cloud_name)
{
    strncpy(state.cloud_name, cloud_name, sizeof(state.cloud_name)-1);
    state.is_dev_environment = 0;

    const struct zpath_cloud_config *cloud_config=zpath_get_cloud_config_from_name(cloud_name);
    if (cloud_config) {
        if (zpath_is_cloud_dev_env(cloud_config->cloud_name)) {
            state.is_dev_environment = 1;
        }
        return ZPATH_RESULT_NO_ERROR;
    } else if  (zpath_is_flex_cloud((char *) cloud_name)) {
       state.is_dev_environment = 1;
    }
    return ZPATH_RESULT_ERR;
}
char* assistant_state_get_cloud_name()
{
    return state.cloud_name;
}
int assistant_state_is_dev_environment()
{
    return state.is_dev_environment;
}

void assistant_state_set_customer_domain(const char *customer_domain)
{
    strncpy(state.custome_domain, customer_domain, sizeof(state.custome_domain)-1);
}
char* assistant_state_get_customer_domain()
{
    return state.custome_domain;
}

/*
 * Returns Current Working Directory. Please expect invalid values in case I couldn't read the path. So don't do any
 * operation with the path, just use it for logging only.
 */
const char *
assistant_state_get_current_working_directory()
{
    return state.current_working_directory;
}

/*
 * Stores the current working directory in state. In case, the current working directory couldn't be obtained for
 * whatever reasons, have the string as "current working directory". This means that this variable should be used
 * only for printing and no operations to be done with it.
 */
static void
assistant_state_store_current_working_directory()
{
    if (NULL == getcwd(state.current_working_directory, sizeof(state.current_working_directory))) {
        snprintf(state.current_working_directory, sizeof(state.current_working_directory), "current working directory");
    }
}

int assistant_state_is_cgroup_enabled()
{
    return state.cgroup_version;
}

#ifdef __linux__
void assistant_state_check_if_cgroup_exists()
{
    state.cgroup_version = zpath_system_check_if_cgroup_exists();
    if (state.cgroup_version) {
        ASSISTANT_LOG(AL_NOTICE, "Cgroups v%d detected!", state.cgroup_version);
    }
}
#else
void assistant_state_check_if_cgroup_exists()
{
    return;
}
#endif

int
assistant_state_init()
{
    int capability_iter;

    if (zpath_debug_add_allocator(&asst_allocator, "assistant")) {
        ASSISTANT_LOG(AL_ERROR, "Could not initialize assistant allocator");
        return ZPN_RESULT_ERR;
    }

    global_assistant.alt_cloud_lock = ZPATH_RWLOCK_INIT;
    global_assistant.ddil_lock = ZPATH_RWLOCK_INIT;

    assistant_state_dbg_init();
    assistant_state_store_current_working_directory();
    assistant_state_record_sarge_version();

    /* we do not enforce hard exit when this fails */
    assistant_state_check_capabilities();

    state.capability_sticky_cache_enabled = 1;
    state.capability_pathing_from_ubrk_enabled = 1;
    asst_state_rpc.capabilities = ASST_CALLOC(ZPN_ASST_MAX_NUMBER_OF_CAPABILITIES * sizeof(char *));
    capability_iter = 0;
    asst_state_rpc.capabilities[capability_iter++] = ASST_STRDUP(ZPN_ASST_CAPABILITY_STR_ASC, sizeof(ZPN_ASST_CAPABILITY_STR_ASC));
    asst_state_rpc.capabilities[capability_iter++] = ASST_STRDUP(ZPN_ASST_CAPABILITY_STR_APU, sizeof(ZPN_ASST_CAPABILITY_STR_APU));
    asst_state_rpc.capabilities_count = capability_iter;

    if ((state.configured_cpus = sysconf(_SC_NPROCESSORS_CONF)) == -1) {
        ASSISTANT_LOG(AL_NOTICE, "failed to read number of configured cpus - %s", strerror(errno));
    }

    if ((state.available_cpus = sysconf(_SC_NPROCESSORS_ONLN)) == -1) {
        ASSISTANT_LOG(AL_NOTICE, "failed to read number of available cpus - %s", strerror(errno));
    }

#ifdef __linux__
    if (assistant_state_is_container_env()) {
        assistant_state_check_if_cgroup_exists();
    } else {
        state.cgroup_version = ZPN_SYSTEM_USE_DEFAULT;
    }
#else
    state.cgroup_version = ZPN_SYSTEM_USE_DEFAULT;
#endif

    state.is_ready = 1;
    ASSISTANT_DEBUG_STATE("state init done!");

    return ZPN_RESULT_NO_ERROR;
}


void assistant_state_set_next_restart_time_s(int64_t next_restart_time_cloud_s)
{
    int64_t time_remaining_before_restart_s;

    if (state.next_restart_time_cloud_s == next_restart_time_cloud_s) {
        return;
    }

    state.next_restart_time_cloud_s = next_restart_time_cloud_s;
    time_remaining_before_restart_s = next_restart_time_cloud_s - assistant_state_get_current_time_cloud_s();
    ASSISTANT_LOG(AL_NOTICE, "Connector software set to upgrade in %"PRId64" seconds", time_remaining_before_restart_s);
    if (time_remaining_before_restart_s < 0) {
        ASSISTANT_LOG(AL_NOTICE, "Connector software upgrade will be skipped as upgrade time passed by %"PRId64" seconds",
                (-time_remaining_before_restart_s));
    }
}
int64_t assistant_state_get_next_restart_time_s()
{
    return state.next_restart_time_cloud_s;
}


void assistant_state_set_time_lag_us(int64_t local_time_lags_cloud_time_delta_us)
{
    state.local_time_lags_cloud_time_delta_us = local_time_lags_cloud_time_delta_us;
}
int64_t assistant_state_get_time_lag_us()
{
    return state.local_time_lags_cloud_time_delta_us;
}

void assistant_state_set_hw_id_changed_and_log_time_us(int64_t id_changed_time_us, int num_of_hw_id_changed) {
    state.number_of_hw_id_changed = num_of_hw_id_changed;
    state.hw_id_changed_time_us[num_of_hw_id_changed-1] = id_changed_time_us;
}

int assistant_state_get_num_hw_id_changed() {
    return state.number_of_hw_id_changed;
}

int64_t* assistant_state_get_hw_id_changed_time_us() {
    return state.hw_id_changed_time_us;
}


/*
 * Record the version of sarge. When sarge is starting up it records the version in ZPATH_SARGE_FILENAME_UPDATER.
 *
 * Its a copy of read_version() functionally
 */
static void
assistant_state_record_sarge_version()
{
    struct stat     st;
    FILE*           fp;
    size_t          len;

    fp = NULL;
    if (stat(ZPATH_SARGE_FILENAME_UPDATER, &st) != 0) {
        goto bad_file;
    }

    fp = fopen(ZPATH_SARGE_FILENAME_UPDATER, "r");
    if (!fp) {
        goto bad_file;
    }

    memset(state.sarge_version, 0, ZPATH_VERSION_STR_MAX_LEN);
    len = fread(state.sarge_version, 1, ZPATH_VERSION_STR_MAX_LEN - 1, fp);
    if (len <= 0) {
        ASSISTANT_LOG(AL_INFO, "zero length sarge version file - please report to zscaler with output of %s file in %s",
                  ZPATH_SARGE_FILENAME_UPDATER, assistant_state_get_current_working_directory());
        goto bad_file;
    }

    do {
        len--;
        if (isspace(state.sarge_version[len])) {
            state.sarge_version[len] = 0;
        } else {
            if (!isprint(state.sarge_version[len])) {
                ASSISTANT_LOG(AL_ERROR, "Binary characters read from sarge version file - please report to zscaler with "
                          "output of %s file in %s",
                          ZPATH_SARGE_FILENAME_UPDATER, assistant_state_get_current_working_directory());
                goto bad_file;
            }
        }
    } while (len);

    len = strlen(state.sarge_version);
    if (!len) {
        ASSISTANT_LOG(AL_INFO, "invalid contents in sarge version file - please report to zscaler with output of %s file "
                           "in %s", ZPATH_SARGE_FILENAME_UPDATER, assistant_state_get_current_working_directory());
        goto bad_file;
    }
    goto good_file;

bad_file:
    snprintf(state.sarge_version, sizeof(state.sarge_version), "unknown");
good_file:
    if (fp) {
        fclose(fp);
    }
}

char*
assistant_state_get_sarge_version()
{
    return state.sarge_version;
}

int
assistant_state_get_configured_cpus()
{
    return state.configured_cpus;
}

int
assistant_state_get_available_cpus()
{
    if (!state.is_ready) {
        ASSISTANT_ASSERT_SOFT(0, "assistant state init is not ready when getting cpu");
    }
    return state.available_cpus;
}

void
assistant_state_set_fohh_threads(int threads)
{
   state.fohh_threads = threads;
}

uint32_t
assistant_state_get_fohh_threads()
{
   return state.fohh_threads;
}

void
assistant_state_set_swap_config (uint64_t bytes)
{
   state.swap_config = (bytes != 0)? 1: 0;
}
int
assistant_state_get_swap_config ()
{
   return state.swap_config;
}

void
assistant_state_delete_alt_cloud_config_file()
{
    struct stat st;
    ZPATH_RWLOCK_WRLOCK(&(global_assistant.alt_cloud_lock), __FILE__, __LINE__);
    if (stat(ASSISTANT_ALT_CLOUD_CONFIG_FILE, &st) == 0) {
        /* clear the file if it exists */
        if(unlink(ASSISTANT_ALT_CLOUD_CONFIG_FILE) == 0) {
            ZPATH_LOG(AL_NOTICE, "Successfully deleted alt domain file");
        } else {
            ZPATH_LOG(AL_ERROR, "Cannot delete alt domain file, error: %s", strerror(errno));
        }
    }
    ZPATH_RWLOCK_UNLOCK(&(global_assistant.alt_cloud_lock), __FILE__, __LINE__);
}

static int assistant_state_delete_alt_domain(struct zpath_debug_state *request_state,
                                             const char **query_values,
                                             int query_value_count,
                                             void *cookie) {
    ZDP("Deleting alt domain file. Printing alt domain %s\n", global_assistant.alt_cloud);
    assistant_state_delete_alt_cloud_config_file();
    return ZPATH_RESULT_NO_ERROR;
}

static int assistant_state_update_alt_domain_from_file(struct zpath_debug_state *request_state,
                                                       const char **query_values,
                                                       int query_value_count,
                                                       void *cookie) {
    ZDP("Before reading alt domain %s \n", global_assistant.alt_cloud);
    assistant_state_read_alt_domain_config_with_lock();
    ZDP("After reading alt domain %s  \n", global_assistant.alt_cloud);
    return ZPATH_RESULT_NO_ERROR;
}

static int assistant_state_show_alt_domain_file(struct zpath_debug_state *request_state,
                                                const char **query_values,
                                                int query_value_count,
                                                void *cookie) {
    char line[ASSISTANT_ALT_CLOUD_CFG_FILE_MAX_SIZE+24] = {0};
    FILE *fp;
    int enabled = 0;
    char alt_domain_string[ASSISTANT_ALT_DOMAIN_MAX_LEN] = {0};

    ZPATH_RWLOCK_WRLOCK(&(global_assistant.alt_cloud_lock), __FILE__, __LINE__);

    fp = fopen(ASSISTANT_ALT_CLOUD_CONFIG_FILE, "r");
    if (fp == NULL) {
        if (errno == ENOENT) {
            /* file not found, but it's ok, meaning alt_cloud is not cached, we will use default cloud*/
        } else {
            ZPATH_LOG(AL_ERROR, "Failed to open file %s to read, err: %s", ASSISTANT_ALT_CLOUD_CONFIG_FILE, strerror(errno));
        }
        ZPATH_RWLOCK_UNLOCK(&(global_assistant.alt_cloud_lock), __FILE__, __LINE__);
        return ZPATH_RESULT_BAD_ARGUMENT;
    }
    ZDP("Showing file contents: \n");
    while (fgets(line, sizeof(line), fp)) {
        if(strstr(line, ASSISTANT_ALT_CLOUD_DOMAIN_STR)) {
            sscanf(line, ASSISTANT_ALT_CLOUD_DOMAIN_STR":%99s", alt_domain_string);
        } else if(strstr(line, ASSISTANT_ALT_CLOUD_FEATURE_STR)) {
            sscanf(line, ASSISTANT_ALT_CLOUD_FEATURE_STR":%d", &enabled);
        }
        ZDP("%s", line);
    }

    ZDP("Alt domain: %s enabled: %d", alt_domain_string, enabled);
    fclose(fp);
    ZPATH_RWLOCK_UNLOCK(&(global_assistant.alt_cloud_lock), __FILE__, __LINE__);
    return ZPATH_RESULT_NO_ERROR;
}

static int assistant_state_reset_connections(struct zpath_debug_state *request_state,
                                             const char **query_values,
                                             int query_value_count,
                                             void *cookie) {
    int use_default = 0;
    int reset_pse_conns = 0;

    if (query_values[0]) {
        use_default = 1;
    }
    if (query_values[1]) {
        reset_pse_conns = 1;
    }
    assistant_state_reset_connections_alt_cloud_toggle(use_default, reset_pse_conns);
    ZDP("Reset broker connections%s Done %s\n", use_default ? "to default " : "", reset_pse_conns ? "Reset PSE connections Done" : "");
    return ZPATH_RESULT_NO_ERROR;
}

static const char*
assistant_get_dc_hosting_info()
{
    char* dc_hosting_info = NULL;
    int   is_async = 0;

    if (! global_assistant.grp_gids[0]) {
        ASSISTANT_LOG(AL_INFO, "Assistant group GID has not yet been assigned to this assistant.");
        return NULL;
    }

    assistant_cfg_assistant_group_get_dc_hosting_info_by_gid(global_assistant.grp_gids[0], &dc_hosting_info, &is_async);

    if (is_async) {
        ASSISTANT_DEBUG_STATE("Result is async while fetching dc_hosting_info for assistant group: %"PRId64"", global_assistant.grp_gids[0]);
        return NULL;
    }

    return dc_hosting_info;
}

static int assistant_state_dump_qbr_fields(struct zpath_debug_state *request_state,
                                           const char **query_values,
                                           int query_value_count,
                                           void *cookie) {
    char region_id[REGION_ID_INFO_LEN] = {0};

    zvm_fill_region_id(region_id, sizeof(region_id));

    const char *dc_hosting_info = assistant_get_dc_hosting_info();
    const char *public_cloud = zvm_vm_type_to_str_for_qbr_concise(zvm_type_get());

    ZDP("Feature flag: %d\n", assistant_features_is_qbr_insights_feature_enabled(global_assistant.gid, &global_assistant.grp_gids[0], global_assistant.grp_gids_len));
    ZDP("Public cloud: %s\n", (public_cloud) ? public_cloud : "");
    ZDP("Private cloud: %s\n", (dc_hosting_info) ? dc_hosting_info : "");
    ZDP("Region ID: %s\n", region_id);

    return ZPATH_RESULT_NO_ERROR;
}

static void
assistant_state_dbg_init()
{

    if (!(zpn_assistant_state_desc = argo_register_global_structure(ZPN_ASSISTANT_STATE_HELPER))) {
        ASSISTANT_LOG(AL_NOTICE, "Unable to create zpn assistant state desc");
    }
    if (!(assistant_state_desc = argo_register_global_structure(ASSISTANT_STATE_HELPER))) {
        ASSISTANT_LOG(AL_NOTICE, "Unable to create assistant state desc");
    }


    if (zpath_debug_add_read_command("Dump assistant state.",
                                "/assistant/state/dump",
                                assistant_state_dump,
                                NULL,
                                NULL)) {

        ASSISTANT_LOG(AL_NOTICE, "Unable to register zpn assistant state's dump command for debugging");
    }

    if (!assistant_state_is_dev_environment()) {
        goto done;
    }

    if (zpath_debug_add_write_command("Unit Test: Pause the operation.",
                                "/assistant/state/ut/pause",
                                assistant_state_ut_pause,
                                NULL,
                                NULL)) {

        ASSISTANT_LOG(AL_NOTICE, "Unable to register /assistant/state/ut/pause");
    }

    if (zpath_debug_add_admin_command("Alternate domain delete",
                                "/assistant/state/delete_alt_domain",
                                assistant_state_delete_alt_domain,
                                NULL,
                                NULL)) {

        ASSISTANT_LOG(AL_NOTICE, "Unable to register /assistant/state/delete_alt_domain");
    }

      if (zpath_debug_add_read_command("Alternate domain delete",
                                "/assistant/state/show_alt_domain_file",
                                assistant_state_show_alt_domain_file,
                                NULL,
                                NULL)) {

        ASSISTANT_LOG(AL_NOTICE, "Unable to register /assistant/state/show_alt_domain");
    }

    if (zpath_debug_add_write_command("Update alt domain from file",
                                "/assistant/state/update_alt_domain_from_file",
                                assistant_state_update_alt_domain_from_file,
                                NULL,
                                NULL)) {

        ASSISTANT_LOG(AL_NOTICE, "Unable to register /assistant/state/show_alt_domain");
    }

    if (zpath_debug_add_write_command("Reset connections, affects control, config, config override, stats, log and PSE control connections",
                                "/assistant/state/reset_connections",
                                assistant_state_reset_connections,
                                NULL,
                                "use_default", "whether to reset to default cloud",
                                "reset_pse_conns", "whether to reset pse connections",
                                NULL)) {

        ASSISTANT_LOG(AL_NOTICE, "Unable to register /assistant/state/reset_connections");
    }

    if (zpath_debug_add_read_command("Dump new QBR fields",
                                "/assistant/state/dump_qbr_fields",
                                assistant_state_dump_qbr_fields,
                                NULL,
                                NULL)) {

        ASSISTANT_LOG(AL_NOTICE, "Unable to register /assistant/state/dump_qbr_fields");
    }

done:
    ASSISTANT_DEBUG_STATE("state debug init done!");
}


void assistant_state_set_health_evbase(struct event_base*   asst_health_base)
{
    state.asst_health_base = asst_health_base;
}
struct event_base* assistant_state_get_health_evbase()
{
    return state.asst_health_base;
}
void assistant_state_set_health_zevbase(struct zevent_base*   asst_health_zbase)
{
    state.asst_health_zbase = asst_health_zbase;
}
struct zevent_base* assistant_state_get_health_zevbase()
{
    return state.asst_health_zbase;
}


void assistant_state_set_reading_config()
{
    state.is_reading_config = 1;
}
void assistant_state_unset_reading_config()
{
    state.is_reading_config = 0;
}
int assistant_state_is_reading_config()
{
    return state.is_reading_config;
}

struct zpath_allocator *
assistant_state_get_allocator()
{
    return &asst_allocator;
}

void
assistant_state_set_pbroker_environment()
{
    if (1 == state.is_pbroker_environment) {
        return;
    }
    state.is_pbroker_environment = 1;
}

int
assistant_state_is_pbroker_environment()
{
    return state.is_pbroker_environment;
}


static void
assistant_state_set_cfg_enabled()
{
    if (state.is_this_asst_cfg_enabled && state.is_atleast_one_asst_grp_cfg_enabled) {
        state.cfg_disabled = 0;
        asst_state_rpc.disabled = 0;
    } else {
        state.cfg_disabled = 1;
        asst_state_rpc.disabled = 1;
    }
}

void
assistant_state_set_asst_config_status(int is_config_enabled)
{
    state.is_this_asst_cfg_enabled = is_config_enabled;
    assistant_state_set_cfg_enabled();
}

void
assistant_state_set_np_connector_disable()
{
    int is_np_connector_currently_enabled = np_connector_get_enablement();

    if (np_connector_is_initialized() && is_np_connector_currently_enabled) {
        np_connector_disable();
    }
}

 /*
  * there are two steps to enable np connector moduel,
  * 1. first it has to be enabled at config override feature flag
  * 2. second zpn_assistant_group.enabled has to be enabled if zpn_assistant_group.connector_group_type is np.
  * **NOTE** if we reach the code reading zpn_assistant_group table, it means zpn_assistant.enabled is already enabled.
  *
  * np connector module enablement:
  * feature flag enabled  && assistant group enabled   => overall enabled
  * feature flag enabled  && assistant group disabled  => overall disabled
  * feature flag disabled && assistant group enabled   => overall disabled
  * feature flag disabled && assistant group disabled  => overall disabled
  *
  * to trigger np_connector_enable/np_connector_disable call
  *
  * overall enabled  && np currently running     => do nothing
  * overall enabled  && np connector not running => np_connector_enable/np_connector_is_initialized
  * overall disabled && np currently running     => np_connector_disable
  * overall disabled && np connector not running => do nothing
  *
  */
void assistant_state_set_np_connector_enablement(int asst_grp_enabled)
{
    int res;
    if (!np_is_initialized()) {
        res = np_init_appc(global_assistant.gid, global_assistant.grp_gids[0]);
        if (res) {
            ASSISTANT_LOG(AL_ERROR, "Could not init np_init_appc - %s", zpath_result_string(res));
        }

        res = np_bgp_init();
        if (res) {
            ASSISTANT_LOG(AL_ERROR, "Unable to initialize NP BGP %s", zpath_result_string(res));
        }

        res = np_connector_bgp_init();
        if (res) {
            ASSISTANT_LOG(AL_ERROR, "Unable to initialize NP connector bgp module %s", zpath_result_string(res));
        }
        return;
    }
    int overall_enable = 0;
    int is_np_connector_feature_enabled = np_is_feature_enabled(global_assistant.customer_gid);
    int is_np_connector_currently_enabled = np_connector_get_enablement();

    if (is_np_connector_feature_enabled && asst_grp_enabled) {
        overall_enable = 1;
    }

    if (overall_enable && !is_np_connector_currently_enabled) {
        if (!np_connector_is_initialized()) {
            res = assistant_np_connector_check_prerequisite();
            if (res) {
                ASSISTANT_LOG(AL_WARNING, "Detected NP connector capability but system does not meet requirements to run NP mode");
                return;
            }
            assistant_np_connector_init();
        } else {
            np_connector_enable();
        }
    } else if (!overall_enable && is_np_connector_currently_enabled) {
        np_connector_disable();
    }
}

void
assistant_state_set_asst_grp_config_status(int is_atleast_one_grp_cfg_enabled)
{
    state.is_atleast_one_asst_grp_cfg_enabled = is_atleast_one_grp_cfg_enabled;
    assistant_state_set_cfg_enabled();

    if (global_assistant.cur_mode == np_connector) {
        assistant_state_set_np_connector_enablement(is_atleast_one_grp_cfg_enabled);
    }
}
void
assistant_state_send_zpn_asst_state(struct zpn_tlv *tlv,
                                    int64_t tlv_incarnation)
{
    char region_id[REGION_ID_INFO_LEN] = {0};
    struct zpn_asst_state *data = assistant_state_get_zpn_asst_state_object();
    const char *private_cloud = NULL;

    if (assistant_features_is_qbr_insights_feature_enabled(global_assistant.gid, &global_assistant.grp_gids[0], global_assistant.grp_gids_len)) {
        data->public_cloud = zvm_vm_type_to_str_for_qbr_concise(zvm_type_get());

        zvm_fill_region_id(region_id, sizeof(region_id));
        data->region_id = (region_id[0] == '\0') ? NULL : &region_id[0];

        private_cloud = assistant_get_dc_hosting_info();
        data->private_cloud = (!private_cloud || strcmp(private_cloud, "") == 0) ? NULL : private_cloud;
    } else {
        data->public_cloud = data->private_cloud = data->region_id = NULL;
    }

    zpn_send_zpn_asst_state(tlv,
                            zpn_tlv_incarnation(tlv),
                            data);
}

void
assistant_state_send_zpn_asst_state_on_fohh(struct fohh_connection *f_conn,
                                            int64_t fohh_incarnation)
{
    char region_id[REGION_ID_INFO_LEN] = {0};
    struct zpn_asst_state *data = assistant_state_get_zpn_asst_state_object();
    const char *private_cloud = NULL;

    if (assistant_features_is_qbr_insights_feature_enabled(global_assistant.gid, &global_assistant.grp_gids[0], global_assistant.grp_gids_len)) {
        data->public_cloud = zvm_vm_type_to_str_for_qbr_concise(zvm_type_get());

        zvm_fill_region_id(region_id, sizeof(region_id));
        data->region_id = (region_id[0] == '\0') ? NULL : &region_id[0];

        private_cloud = assistant_get_dc_hosting_info();
        data->private_cloud = (!private_cloud || strcmp(private_cloud, "") == 0) ? NULL : private_cloud;
    } else {
        data->public_cloud = data->private_cloud = data->region_id = NULL;
    }

    zpn_send_zpn_asst_state_on_fohh(f_conn,
                                    fohh_incarnation,
                                    data);
}

int
assistant_state_is_cfg_enabled()
{
    return (0 == state.cfg_disabled);
}


struct zpn_asst_state*
assistant_state_get_zpn_asst_state_object()
{
    return &asst_state_rpc;
}

uint16_t
assistant_state_get_cpu_util(void)
{
    return (global_assistant.sys_stats.cpu_util);
}

uint16_t
assistant_state_get_cpu_steal_perc(void)
{
    return (global_assistant.sys_stats.cpu_steal_perc);
}

int
assistant_state_get_system_mem_util(void)
{
    return (global_assistant.sys_stats.system_mem_util);
}

int
assistant_state_get_process_mem_util(void)
{
    return (global_assistant.sys_stats.process_mem_util);
}
int
assistant_state_set_uptime_s(void)
{
    struct timespec ts = {0, 0};
    state.assistant_uptime_s = 0;
    if (clock_gettime(CLOCK_MONOTONIC, &ts) == 0) {
        state.assistant_uptime_s = ts.tv_sec;
    }
    return (0);
}

int64_t
assistant_state_get_uptime_s(void)
{
    return state.assistant_uptime_s;
}

/*
 *
 * Descrption:
 * The function returns assistant monotonic uptime in a human readable form in the input buf
 * and in the format xxDxxHxxMxxS which represents uptime xx Days, xx Hour, xx Mins xx Seconds.
 * The function is a modified version of the fohh_get_uptime_str function.
 *
 * note:
 * max length of buf is 30 taking consideration of the worst case scenario '9223372036854775807D23H59M59S'
 * max length of day_str is 21 taking consideration of the worst case scenario '9223372036854775807D:'
 * max length of hour_str is 5 taking consideration of the worst case scenario '23H:'
 * max length of min_str is 5 taking consideration of the worst case scenario '59M:'
 * max length of sec_str is 4 taking consideration of the worst case scenario '59S'
 *
 * example:
 * 2           secs : 2S
 * 86          secs : 1M:26S
 * 3608        secs : 1H:0M:8S
 * 3668        secs : 1H:1M:8S
 * 86400       secs : 1D:0H:0M:0S
 * 86400245250 secs : 1000002D:20H:7M:30S
 *
 */
char* assistant_state_get_uptime_str(char *buf, int buf_len)
{
    int64_t day;
    int64_t hour;
    int64_t min;
    int64_t sec;
    char    day_str[ASSISTANT_STATE_DAY_STR_LEN];
    char    hour_str[ASSISTANT_STATE_HOUR_MIN_SEC_STR_LEN];
    char    min_str[ASSISTANT_STATE_HOUR_MIN_SEC_STR_LEN];
    char    sec_str[ASSISTANT_STATE_HOUR_MIN_SEC_STR_LEN];

    struct timespec ts;

    if (clock_gettime(CLOCK_MONOTONIC, &ts) != 0) {
        ASSISTANT_LOG(AL_NOTICE, "clock_gettime() failed with errno %d: %s", errno, strerror(errno));
        return "-1";
    }

    sec = ts.tv_sec - state.assistant_uptime_s;

    day = sec / (24 * 60 * 60);
    sec %= (24 * 60 * 60);

    hour = sec / (60 * 60);
    sec %= (60 * 60);

    min = sec / 60;
    sec %= 60;

    snprintf_nowarn(day_str, sizeof(day_str), "%"PRId64"D:", day);
    snprintf_nowarn(hour_str, sizeof(hour_str), "%"PRId64"H:", hour);
    snprintf_nowarn(min_str, sizeof(min_str), "%"PRId64"M:", min);
    snprintf_nowarn(sec_str, sizeof(sec_str), "%"PRId64"S", sec);

    snprintf(buf, buf_len, "%s%s%s%s",  (day==0)? "": day_str,
                                            (hour==0 && day==0)? "": hour_str,
                                            (day==0 && hour==0 && min==0)? "": min_str,
                                            (day==0 && hour==0 && min==0 && sec==0)? "": sec_str);

    return buf;
}

void assistant_state_set_container_env()
{
    state.is_container_env = 1;
}

int assistant_state_is_container_env()
{
    return state.is_container_env;
}

void assistant_state_set_zscaler_os()
{
    state.is_zscaler_os = 1;
}

int assistant_state_is_zscaler_os()
{
    return state.is_zscaler_os;
}

/*
 * Descrption:
 * Function returns the number of processes in the system run queue
 * averaged over 1 min, 5 min and 15 min periods of time as a
 * percentage integer values. The average load values returned are
 * normalized over total online system cpus - per processor load
 * average is calculated. The obtained load averages are stored in
 * the provided output load_avgs[] array.
 *
 * load_avgs[0] contains 1 min % load average
 * load_avgs[1] contains 5 min % load average
 * load_avgs[2] contains 15 min % load average
 *
 * A load average value of > 100 implies system is under high load.
 *
 * When unable to obtain load averages the function returns value of -1
 * Otherwise returns the numbers of samples returned.
 */

int assistant_state_get_system_loadavg(int load_avgs[], int num_avgs)
{
    static int _sc_nprocessors_onln = -1;
    double lavgs[3];
    int result = 0;
    int i=0;

    if ((load_avgs == NULL) ||
        (num_avgs < 0) || (num_avgs > 3)) {
        return -1;
    }

    if (_sc_nprocessors_onln <= 0) {
        _sc_nprocessors_onln = (int) sysconf(_SC_NPROCESSORS_ONLN);
        if (_sc_nprocessors_onln <= 0) {
            return -1;
        }
    }

    result = getloadavg(lavgs, 3);
    if ( result == -1 ) {
        return -1;
    }

    memset(&load_avgs[0], 0, (sizeof(load_avgs[0]) * num_avgs));

    for (i = 0; (i < result) && (i < num_avgs); i++) {
        load_avgs[i] = ((int)(lavgs[i] * 100)) / _sc_nprocessors_onln;
    }

    return i;
}

void
assistant_state_read_alt_domain_config_with_lock()
{
    char line[ASSISTANT_ALT_DOMAIN_MAX_LEN+24] = {0};
    char alt_domain_string[ASSISTANT_ALT_DOMAIN_MAX_LEN] = {0};
    int enabled = 0;
    FILE *fp;

    ZPATH_RWLOCK_WRLOCK(&(global_assistant.alt_cloud_lock), __FILE__, __LINE__);
    if (global_assistant.alt_cloud != NULL) {
        ASST_FREE(global_assistant.alt_cloud);
    }
    global_assistant.alt_cloud = NULL;
    fp = fopen(ASSISTANT_ALT_CLOUD_CONFIG_FILE, "r");
    if (fp == NULL) {
        if (errno == ENOENT) {
            /* file not found, but it's ok, meaning alt_cloud is not cached, we will use default cloud*/
            global_assistant.alt_cloud_enabled = 0;
            assistant_state_set_alt_cloud_enabled(0);
            ZPATH_LOG(AL_NOTICE, "Alt cloud config not present, using default cloud, alt cloud feature set to disabled");
        } else {
            ZPATH_LOG(AL_ERROR, "Failed to open file %s to read, err: %s", ASSISTANT_ALT_CLOUD_CONFIG_FILE, strerror(errno));
        }
        ZPATH_RWLOCK_UNLOCK(&(global_assistant.alt_cloud_lock), __FILE__, __LINE__);
        return;
    }

    while (fgets(line, sizeof(line), fp)) {
        if(strstr(line, ASSISTANT_ALT_CLOUD_DOMAIN_STR)) {
            sscanf(line, ASSISTANT_ALT_CLOUD_DOMAIN_STR":%99s", alt_domain_string);
        } else if(strstr(line, ASSISTANT_ALT_CLOUD_FEATURE_STR)) {
            sscanf(line, ASSISTANT_ALT_CLOUD_FEATURE_STR":%d", &enabled);
        }
    }

    fclose(fp);
    if (strcmp(alt_domain_string, "") && strnlen(alt_domain_string, ASSISTANT_ALT_DOMAIN_MAX_LEN)) {
        global_assistant.alt_cloud = ASST_STRDUP(alt_domain_string, sizeof(alt_domain_string));
    }
    global_assistant.alt_cloud_enabled = enabled;
    assistant_state_set_alt_cloud_enabled(enabled);
    ZPATH_RWLOCK_UNLOCK(&(global_assistant.alt_cloud_lock), __FILE__, __LINE__);
    ZPATH_LOG(AL_NOTICE, "Successfully read cached alt cloud config, feature state: %s, alt domain: (%s)",
                          enabled ? "enabled" : "disabled", global_assistant.alt_cloud ? global_assistant.alt_cloud : "");
}

int
assistant_state_store_alt_domain_config_with_lock(int enabled, char *req_alt_cloud)
{
    FILE *fp;
    int written;
    char out_str[ASSISTANT_ALT_CLOUD_CFG_FILE_MAX_SIZE] = {0};
    int need_reset = 0;
    int need_update = 0;
    struct stat st;

    char *s = out_str;
    char *e = out_str + ASSISTANT_ALT_CLOUD_CFG_FILE_MAX_SIZE;

    ZPATH_RWLOCK_WRLOCK(&(global_assistant.alt_cloud_lock), __FILE__, __LINE__);

    /* Update feature state memory cache */
    assistant_state_set_alt_cloud_enabled(enabled);
    if (global_assistant.alt_cloud_enabled != enabled) {
        /* Feature flag state changed, need to reset all connections */
        need_reset = 1;
        need_update = 1;
    }
    global_assistant.alt_cloud_enabled = enabled;

    if (!enabled) {
        /* Case where it is disabled just free the alt domain and delete the file */
        if (global_assistant.alt_cloud) {
            ASST_FREE(global_assistant.alt_cloud);
            global_assistant.alt_cloud = NULL;
        }
        if (stat(ASSISTANT_ALT_CLOUD_CONFIG_FILE, &st) == 0) {
            /* clear the file if it exists */
            if (unlink(ASSISTANT_ALT_CLOUD_CONFIG_FILE) == 0) {
                ZPATH_LOG(AL_NOTICE, "Successfully deleted alt domain file");
            } else {
                ZPATH_LOG(AL_ERROR, "Cannot delete alt domain file, error: %s", strerror(errno));
            }
        }
        ZPATH_RWLOCK_UNLOCK(&(global_assistant.alt_cloud_lock), __FILE__, __LINE__);
        return need_reset;
    }

    /* Update feature state disk cache */
    s += sxprintf(s, e, "%s:%d\n", ASSISTANT_ALT_CLOUD_FEATURE_STR, enabled);

    if (req_alt_cloud) {
        if (global_assistant.alt_cloud) {
            /* We already have cached alt domain, check if we need to update */
            if (strcmp(global_assistant.alt_cloud, req_alt_cloud) == 0) {
                /* Received same alt domain as cached, no need to update */
                s += sxprintf(s, e, "%s:%s\n", ASSISTANT_ALT_CLOUD_DOMAIN_STR, req_alt_cloud);
                goto done;
            } else if (strnlen(req_alt_cloud, ASSISTANT_ALT_DOMAIN_MAX_LEN) == 0) {
                /* Received empty alt domain, perform update */
                ASSISTANT_LOG(AL_NOTICE, "Alt domain changed to NULL, previously cached: (%s), updating local cache",
                                        global_assistant.alt_cloud);
                ASST_FREE(global_assistant.alt_cloud);
                global_assistant.alt_cloud = NULL;
                s += sxprintf(s, e, "%s:%s\n", ASSISTANT_ALT_CLOUD_DOMAIN_STR, "");
            } else {
                /* Alt domain changed, perform update */
                ASSISTANT_LOG(AL_NOTICE, "Alt domain changed (%s) -> (%s), updating local cache",
                                        global_assistant.alt_cloud, req_alt_cloud);
                ASST_FREE(global_assistant.alt_cloud);
                global_assistant.alt_cloud = ASST_STRDUP(req_alt_cloud, strlen(req_alt_cloud));
                s += sxprintf(s, e, "%s:%s\n", ASSISTANT_ALT_CLOUD_DOMAIN_STR, req_alt_cloud);
            }
            need_update = 1;
        } else {
            /* We don't have cached alt domain */
            if (strnlen(req_alt_cloud, ASSISTANT_ALT_DOMAIN_MAX_LEN)) {
                /* we received one, perform update */
                ASSISTANT_LOG(AL_NOTICE, "Received alt domain (%s), updating local cache", req_alt_cloud);
                global_assistant.alt_cloud = ASST_STRDUP(req_alt_cloud, strlen(req_alt_cloud));
                s += sxprintf(s, e, "%s:%s\n", ASSISTANT_ALT_CLOUD_DOMAIN_STR, req_alt_cloud);
                need_update = 1;
            } else {
                /* Alt domain remains NULL, no need to update */
                s += sxprintf(s, e, "%s:%s\n", ASSISTANT_ALT_CLOUD_DOMAIN_STR, "");
            }
        }
    } else {
        /* When req_alt_cloud is NULL, this means the caller is from config override monitor callback
         * So we won't be touching the file unless feature state changes */
        s += sxprintf(s, e, "%s:%s\n", ASSISTANT_ALT_CLOUD_DOMAIN_STR, "");
    }


done:
    if (need_update) {
        fp = fopen(ASSISTANT_ALT_CLOUD_CONFIG_FILE, "w");
        if (fp == NULL) {
            ASSISTANT_LOG(AL_ERROR, "Failed to open file %s to write, err: %s", ASSISTANT_ALT_CLOUD_CONFIG_FILE, strerror(errno));
            ZPATH_RWLOCK_UNLOCK(&(global_assistant.alt_cloud_lock), __FILE__, __LINE__);
            return need_reset;
        }
        written = fwrite(out_str, strlen(out_str), 1, fp);
        if (written != 1) {
            ASSISTANT_LOG(AL_ERROR, "Failed to write alt domain feature state (%s) to file, err: %s",
                                    enabled ? "enabled" : "disabled", strerror(errno));
        } else {
            ASSISTANT_LOG(AL_NOTICE, "Successfully updated local cache, feature state: %s, alt domain: (%s)",
                                    enabled ? "enabled" : "disabled", req_alt_cloud ? req_alt_cloud : "");
        }
        fclose(fp);
    }
    ZPATH_RWLOCK_UNLOCK(&(global_assistant.alt_cloud_lock), __FILE__, __LINE__);
    return need_reset;
}

int
assistant_state_connection_is_on_alt_cloud(struct fohh_connection* connection)
{
    if (!strstr(fohh_description(connection), assistant_state_get_cloud_name())) {
        /* Not on default cloud */
        return 1;
    }
    return 0;
}

int
assistant_state_fohh_connection_sanity_callback_with_lock(struct fohh_connection* connection)
{
    int64_t now_s;
    int64_t current_disconnected_time_s;

    ASSISTANT_DEBUG_SITE("[%s], site_active[%d], sitec_reachable[%d], def_ep_reachable[%d], sitec_preferred[%d], sitec_preferred_backdoor[%d]",
                         fohh_description(connection),
                         global_assistant.site_is_active,
                         assistant_site_sitec_reachable(),
                         assistant_site_default_endpoint_reachable(),
                         global_assistant.sitec_preferred,
                         global_assistant.sitec_preferred_backdoor);
    /* Sanity check 1: switch connection between siteC or default broker if required */
    fohh_connection_site_state_check(connection,
                                        global_assistant.site_is_active,
                                        assistant_site_sitec_reachable(),
                                        assistant_site_default_endpoint_reachable(),
                                        assistant_site_get_sitec_preferred_with_lock(),
                                        (zpn_assistant_global_assistant_get_firedrill_state()? ASSISTANT_FIREDRILL_SWITCH_VALUE : assistant_site_get_max_allowed_downtime_with_lock()),
                                        NULL);

    /* Sanity check 2: further down we are only interested in unconnected connection */
    if (fohh_get_state(connection) == fohh_connection_connected) {
        return ZPATH_RESULT_NO_ERROR;
    }

    /* Sanity check 3: never connected to alt cloud */
    if (!global_assistant.alt_cloud || !global_assistant.alt_cloud_enabled) {
        /* feature not enabled/enforced, skipping sanity check */
        return ZPATH_RESULT_NO_ERROR;
    }

    now_s = epoch_s();
    current_disconnected_time_s = now_s - connection->last_disconnect_epoch_s;
    if ((current_disconnected_time_s > connection->max_allowed_downtime_s) &&
        assistant_state_connection_is_on_alt_cloud(connection)) {
        ASSISTANT_LOG(AL_NOTICE, "%s: Alternate cloud connection uninitialized for (%"PRId64") secs more than allowed downtime (%"PRId64") sec, "
                                 "fall back to default cloud: %s already contacted default %d printing pointer for conn id %p",
                                 connection->stats.description, current_disconnected_time_s, connection->max_allowed_downtime_s,
                                 assistant_state_get_cloud_name(), connection->times_contacted_default, connection);
        // We need 5x time as we do fohh redirect after 5 times connect. After 2 such fallbacks we go to min 30 s.
        connection->times_contacted_default++;
        if (connection->times_contacted_default < ASSISTANT_STATE_ALT_CLOUD_CONN_MAX_RETRIES) {
            connection->max_allowed_downtime_s = 3 * connection->max_allowed_downtime_s;
        } else {
             connection->times_contacted_default = 0;
            connection->max_allowed_downtime_s = ASSISTANT_STATE_ALT_CLOUD_CONN_MAX_INIT_TIME_S;
        }
        assistant_state_alt_cloud_reset_connection(connection, 1);
        state.alt_cloud_conn_init_failures++;
        /* Case where it is disabled just free the alt domain and delete the file */
        ZPATH_RWLOCK_WRLOCK(&(global_assistant.alt_cloud_lock), __FILE__, __LINE__);
        if (global_assistant.alt_cloud) {
            ASST_FREE(global_assistant.alt_cloud);
            global_assistant.alt_cloud = NULL;
        }
        ZPATH_RWLOCK_UNLOCK(&(global_assistant.alt_cloud_lock), __FILE__, __LINE__);
    }

    return ZPATH_RESULT_NO_ERROR;
}

int
assistant_state_alt_cloud_reset_connection(struct fohh_connection* connection, int use_default)
{
    char*   cloud_name;
    char    alt_cloud_name[256] = {0};
    char*   cloud_name_copy;
    int     res;

    if (!connection) {
        return ZPATH_RESULT_NO_ERROR;
    }

    if (global_assistant.hardcoded_broker_name) {
        ASSISTANT_LOG(AL_NOTICE, "Using broker: %s, skipping connection reset", global_assistant.hardcoded_broker_name);
        return ZPATH_RESULT_NO_ERROR;
    }
    if (use_default) {
        cloud_name = assistant_state_get_cloud_name();
    } else if (global_assistant.alt_cloud && global_assistant.alt_cloud_enabled) {
        assistant_state_get_alt_cloud_with_lock(alt_cloud_name);
        cloud_name = alt_cloud_name;
    } else {
        cloud_name = assistant_state_get_cloud_name();
    }

    ASSISTANT_LOG(AL_NOTICE, "%s: Reset connection to broker to switch cloud, using cloud_name(%s), alt cloud feature state: %s",
                              fohh_description(connection), cloud_name, global_assistant.alt_cloud_enabled ? "enabled":"disabled");

    cloud_name_copy = ASST_STRDUP(cloud_name, strlen(cloud_name));
    /*
     * Reset redirect timestamp as fwd broker has a check and wont redirect if its within 5 mins
     */
    connection->last_redirect_us = 0;

    res = fohh_connection_reset_alt_cloud(connection, cloud_name_copy);
    if (res != ZPATH_RESULT_NO_ERROR) {
        ASSISTANT_LOG(AL_ERROR, "%s: Failed to reset hostname & SNI", fohh_description(connection));
        ASST_FREE(cloud_name_copy);
    } else {
        state.alt_cloud_resets++;
    }

    return res;
}

void
assistant_state_reset_connections_alt_cloud_toggle(int use_default, int reset_pse_conns)
{
    int res = ZPATH_RESULT_NO_ERROR;

    state.alt_cloud_resets_via_toggle++;

    res = assistant_state_alt_cloud_reset_connection(get_cfg_channel(), use_default);
    if (res == ZPATH_RESULT_NO_ERROR) {
        assistant_cfg_conn_reset_count();
    }

    res = assistant_state_alt_cloud_reset_connection(get_ovd_channel(), use_default);
    if (res == ZPATH_RESULT_NO_ERROR) {
        assistant_cfg_override_conn_reset_count();
    }

    res = assistant_state_alt_cloud_reset_connection(global_assistant.broker_control, use_default);
    if (res == ZPATH_RESULT_NO_ERROR) {
        assistant_control_conn_reset_count();
    }

    res = assistant_state_alt_cloud_reset_connection(global_assistant.broker_stats, use_default);
    if (res == ZPATH_RESULT_NO_ERROR) {
        assistant_stats_conn_reset_count();
    }

    res = assistant_state_alt_cloud_reset_connection(assistant_log_tx_get_event_channel(), use_default);
    if (res == ZPATH_RESULT_NO_ERROR) {
        assistant_log_tx_event_conn_reset_count();
    }

    res = assistant_state_alt_cloud_reset_connection(assistant_log_tx_get_inspection_channel(), use_default);
    if (res == ZPATH_RESULT_NO_ERROR) {
        assistant_log_tx_inspection_conn_reset_count();
    }

    res = assistant_state_alt_cloud_reset_connection(assistant_log_tx_get_app_inspection_channel(), use_default);
    if (res == ZPATH_RESULT_NO_ERROR) {
        assistant_log_tx_app_inspection_conn_reset_count();
    }

    res = assistant_state_alt_cloud_reset_connection(assistant_log_tx_get_ptag_channel(), use_default);
    if (res == ZPATH_RESULT_NO_ERROR) {
        assistant_log_tx_ptag_conn_reset_count();
    }

    if (reset_pse_conns) {
        assistant_pbroker_control_conn_reset_alt_cloud_feature_toggle();
    }
}

void
assistant_state_get_alt_cloud_with_lock(char *alt_domain)
{
    if (alt_domain && global_assistant.alt_cloud) {
        ZPATH_RWLOCK_RDLOCK(&(global_assistant.alt_cloud_lock), __FILE__, __LINE__);
        snprintf(alt_domain, strlen(global_assistant.alt_cloud) + 1, "%s", global_assistant.alt_cloud);
        ZPATH_RWLOCK_UNLOCK(&(global_assistant.alt_cloud_lock), __FILE__, __LINE__);
    }
}

void
assistant_state_set_alt_cloud_enabled(int64_t enabled)
{
    state.alt_cloud_enabled = (int)enabled;
}

int
assistant_state_get_alt_cloud_resets_count()
{
    return state.alt_cloud_resets;
}

int
assistant_state_get_alt_cloud_status()
{
    return state.on_alt_cloud;
}

void
assistant_state_update_alt_cloud_status(int on_alt_cloud)
{
    state.on_alt_cloud = on_alt_cloud;
}


void
assistant_state_allocator_libevent_sys_mem_total_bytes_set(uint64_t bytes)
{
    state.allocator_libevent_sys_mem_total_bytes = bytes;
}

uint64_t
assistant_state_allocator_libevent_sys_mem_total_bytes_get()
{
    return state.allocator_libevent_sys_mem_total_bytes;
}

void
assistant_state_allocator_libevent_out_queue_set(int64_t allocator_libevent_out_queue_allowed_bytes)
{
    // compare with current and update only if differrent
    if (zpn_allocator_libevent_out_queue_allowed_bytes != allocator_libevent_out_queue_allowed_bytes) {
        __atomic_store_n(&zpn_allocator_libevent_out_queue_allowed_bytes, allocator_libevent_out_queue_allowed_bytes, __ATOMIC_RELAXED);
    }
}

int64_t
assistant_state_allocator_libevent_used_percent_get()
{
    return state.allocator_libevent_used_percent;
}

int64_t
assistant_state_allocator_libevent_out_queue_allowed_bytes_get()
{
    return state.allocator_libevent_out_queue_allowed_bytes;
}

int64_t
assistant_state_allocator_libevent_max_bytes_get()
{
    return state.allocator_libevent_max_bytes;
}

int64_t
assistant_state_allocator_libevent_used_bytes_get()
{
    return state.allocator_libevent_used_bytes;
}

static void
assistant_state_allocator_libevent_out_queue_metrics_update(int64_t allocator_libevent_max_bytes,
    int64_t allocator_libevent_used_bytes,
    int64_t allocator_libevent_used_percent,
    int64_t allocator_libevent_out_queue_allowed_bytes)
{
    state.allocator_libevent_max_bytes = allocator_libevent_max_bytes;
    state.allocator_libevent_used_bytes = allocator_libevent_used_bytes;
    state.allocator_libevent_used_percent = allocator_libevent_used_percent;
    state.allocator_libevent_out_queue_allowed_bytes = allocator_libevent_out_queue_allowed_bytes;
}

void
assistant_state_allocator_libevent_out_queue_calc(int64_t out_queue_min_len_bytes,
                                                                 int64_t out_queue_max_len_bytes,
                                                                 int64_t sys_mem_max_percent,
                                                                 int64_t sys_mem_total_bytes,
                                                                 int64_t allocator_libevent_start_thresh_percent,
                                                                 int64_t allocator_libevent_used_bytes,
                                                                 int64_t *allocator_libevent_max_bytes_p,
                                                                 int64_t *allocator_libevent_used_percent_p,
                                                                 int64_t *allocator_libevent_out_queue_allowed_bytes_p)
{
    // calc formula
    // output queue current allowed depth =
    // output queue max depth  (minus)
    // ( libevent allocator current percent value  (multiply)
    // output queue max range )
    // example = 1000KB - (25% x 900KB) = 1000KB - 225KB = 775KB

    int64_t allocator_libevent_max_bytes = sys_mem_total_bytes * sys_mem_max_percent / 100;

    // calculate metric:
    // double allocator_libevent_used_bytes_d = allocator_libevent_used_bytes;
    // double allocator_libevent_max_bytes_d = allocator_libevent_max_bytes;
    // double allocator_libevent_used_percent_d = (allocator_libevent_used_bytes_d / allocator_libevent_max_bytes_d) * 100;
    // int64_t allocator_libevent_used_percent = allocator_libevent_used_percent_d;

    int64_t allocator_libevent_used_percent = (100 * allocator_libevent_used_bytes) / allocator_libevent_max_bytes;

    // adjust output queue if libevent is above configured threshold, otherwise use max q len
    int64_t allocator_libevent_out_queue_allowed_bytes = out_queue_max_len_bytes;
    if (allocator_libevent_used_percent > allocator_libevent_start_thresh_percent) {

        // if above 100% , use min q len
        #define ALLOCATOR_LIBEVENT_USED_100_PERCENT 100
        if (allocator_libevent_used_percent > ALLOCATOR_LIBEVENT_USED_100_PERCENT) {
            allocator_libevent_out_queue_allowed_bytes = out_queue_min_len_bytes;
            // should we warn here ?, or metrics will be sufficient indication that it is above 100 percent
        } else {
            // otherwise adjust q length
            int64_t q_range = out_queue_max_len_bytes - out_queue_min_len_bytes;
            allocator_libevent_out_queue_allowed_bytes = out_queue_max_len_bytes - (allocator_libevent_used_percent*q_range / 100);
        }
    } else {
        allocator_libevent_out_queue_allowed_bytes = out_queue_max_len_bytes;
    }

    // defensive, it should not happen, but just in case - validate if out queue is within of config allowed range
    if (allocator_libevent_out_queue_allowed_bytes > CONFIG_FEATURE_ASSISTANT_ALLOCATOR_LIBEVENT_OUT_QUEUE_MAX_LEN_BYTES_MAX) {
        allocator_libevent_out_queue_allowed_bytes = CONFIG_FEATURE_ASSISTANT_ALLOCATOR_LIBEVENT_OUT_QUEUE_MAX_LEN_BYTES_MAX;
    }
    if (allocator_libevent_out_queue_allowed_bytes < CONFIG_FEATURE_ASSISTANT_ALLOCATOR_LIBEVENT_OUT_QUEUE_MIN_LEN_BYTES_MIN) {
        allocator_libevent_out_queue_allowed_bytes = CONFIG_FEATURE_ASSISTANT_ALLOCATOR_LIBEVENT_OUT_QUEUE_MIN_LEN_BYTES_MIN;
    }

    //return
    *allocator_libevent_max_bytes_p = allocator_libevent_max_bytes;
    *allocator_libevent_used_percent_p = allocator_libevent_used_percent;
    *allocator_libevent_out_queue_allowed_bytes_p = allocator_libevent_out_queue_allowed_bytes;
}

void
assistant_state_allocator_libevent_out_queue_calc_and_update(int64_t connector_gid, int64_t *allocator_libevent_out_queue_allowed_bytes_p)
{

    // if enabled - check remaining parameters
    int64_t out_queue_min_len_bytes;
    int64_t out_queue_max_len_bytes;
    int64_t sys_mem_max_percent;
    int64_t allocator_libevent_start_thresh_percent;

    assistant_features_allocator_libevent_out_queue_get_params(connector_gid,
                                                               &out_queue_min_len_bytes,
                                                               &out_queue_max_len_bytes,
                                                               &sys_mem_max_percent,
                                                               &allocator_libevent_start_thresh_percent);

    int64_t allocator_libevent_max_bytes = 0;
    int64_t allocator_libevent_used_percent = 0;
    int64_t allocator_libevent_out_queue_allowed_bytes = 0;

    // get total sys mem bytes
    int64_t sys_mem_total_bytes = assistant_state_allocator_libevent_sys_mem_total_bytes_get();

    // get allocator libevent used bytes
    int64_t allocator_libevent_used_bytes = zpath_allocator_by_name_used_bytes_get("libevent");

    // if we have some memory reading for sys mem and libevent - adjust out q
    if (sys_mem_total_bytes && allocator_libevent_used_bytes) {

        assistant_state_allocator_libevent_out_queue_calc(out_queue_min_len_bytes,
                                                          out_queue_max_len_bytes,
                                                          sys_mem_max_percent,
                                                          sys_mem_total_bytes,
                                                          allocator_libevent_start_thresh_percent,
                                                          allocator_libevent_used_bytes,
                                                          &allocator_libevent_max_bytes,
                                                          &allocator_libevent_used_percent,
                                                          &allocator_libevent_out_queue_allowed_bytes);

        // update metrics
        assistant_state_allocator_libevent_out_queue_metrics_update(allocator_libevent_max_bytes,
                                                                    allocator_libevent_used_bytes,
                                                                    allocator_libevent_used_percent,
                                                                    allocator_libevent_out_queue_allowed_bytes);
    } else {
        // otherwise make sure out queue len is at default - not intact
        allocator_libevent_out_queue_allowed_bytes = CONFIG_FEATURE_ASSISTANT_ALLOCATOR_LIBEVENT_OUT_QUEUE_MAX_LEN_BYTES_DEFAULT;
    }
    // return
    *allocator_libevent_out_queue_allowed_bytes_p = allocator_libevent_out_queue_allowed_bytes;

    ASSISTANT_LOG(AL_INFO, "allocator libevent out queue: "
                           "out_queue:%"PRId64", %"PRId64", "
                           "sys_mem: max_percent: %"PRId64", tot bytes: %"PRId64", "
                           "allocator libevent: used bytes: %"PRId64", max bytes: %"PRId64", "
                           "used percent: %"PRId64", start thresh percent: %"PRId64", "
                           "out_queue_allowed_bytes: %"PRId64" ",
                           out_queue_min_len_bytes, out_queue_max_len_bytes,
                           sys_mem_max_percent, sys_mem_total_bytes,
                           allocator_libevent_used_bytes, allocator_libevent_max_bytes,
                           allocator_libevent_used_percent, allocator_libevent_start_thresh_percent,
                           allocator_libevent_out_queue_allowed_bytes);

}
