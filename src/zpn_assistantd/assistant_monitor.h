/*
 * assistant_monitor.h. Copyright (C) 2014-2020 Zscaler Inc, All Rights Reserved
 */

#ifndef _ASSISTANT_MONITOR_H_
#define _ASSISTANT_MONITOR_H_

#include "ztlv/zpn_tlv.h"
#include "zpn/zpn_rpc.h"
#include "zpath_lib/zpath_debug.h"

int zpn_assistant_double_encrypt_ssl_ctx_init(const char *root_pem_filename,
                                              const char *certificate_pem_filename,
                                              EVP_PKEY *client_key);

int zpn_assistant_mtunnel_mtls_ssl_ctx_init(const char *root_pem_filename,
                                            const char *certificate_pem_filename,
                                            const char *private_key_pem_filename,
                                            EVP_PKEY   *client_key);
int zpn_assistant_ssl_tunnel_capable();

#define ASSISTANT_MONITOR_TIMER_S                               60

/*
 * 1. Re-enroll when we are >= 90% of the cert.pem's valid period. Done in enrollment library.
 * 2. Force-re-enroll when we are >= 99% of the cert.pem's valid period.
 */
#define ZPN_ASSISTANT_FORCE_RE_ENROLL_TIME_FACTOR               99

#define SLOW_STOP_TIME_S                                        300
#define CONNECTOR_BAIL_AFTER_STUCK_IN_INVALID_STATE_COUNTER     (3 * SLOW_STOP_TIME_S)
#define STOP_HEALTH_BEFORE_RESTART_TIME_SECS                    SLOW_STOP_TIME_S
#define ASSISTANT_NAME_MAX_LEN                                  256

#define ASSISTANT_DEFAULT_FOHH_THREADS                          5

#define ASSISTANT_MONITOR_CHECK_MTUNNEL_TIMER_MAX_MISSING_S     5
#define ASSISTANT_MONITOR_STATUS_REPORT_TIMER_MAX_MISSING_S     62

#define CONNECTOR_LOG_NAME                                      "Connector"

int assistant_monitor_init(struct event_base *base);
int assistant_monitor_debug_init();
void assistant_monitor_timer_stats_fill(struct zpn_assistant_monitor_stats *out_data);

void zpn_assistant_switch_to_pcc();

void zpn_assistant_switch_to_cloud();

int zpn_assistant_firedrill_timer_activate(int64_t firedrill_interval);

int zpn_assistant_firedrill_start(int64_t firedrill_interval);
#endif /* _ASSISTANT_MONITOR_H_ */
