/*
 * assistant_features.c. Copyright (C) 2019 Zscaler Inc. All Rights Reserved.
 *
 * This control the features that are pluggable (enabled or disabled). Any half-baked feature (i.e you are in the
 * process of developing a feature) that needs to go to develop can have this flag. Another use case is that
 * not-validated-by-QA features can have this flag and the feature turned off. Later when we want to enable this,
 * we can turn on the flag and have QA validate this.
 *
 * Q: Isn't this the same as config overide?
 * A: No. Config override is typically something that is tested by QA. And you intend to make it available by OPS.
 */

#include "zpath_lib/zpath_debug.h"
#include "zpath_lib/zpath_config_override.h"
#include "zpath_lib/zpath_config_override_keys.h"

#include "zpn_assistantd/assistant_control_tx.h"
#include "zpn/assistant_log.h"
#include "zpn_assistantd/assistant_features.h"
#include "zpn_assistantd/assistant_state.h"
#include "zpn_assistantd/assistant_util.h"
#include "zpn_zdx/zpn_zdx_feature.h"
#include "zpn_assistantd/zpn_assistant_private.h"
#include "fohh/fohh_fproxy.h"
#include "zpn/zpn_rpc.h"
#include "zpath_lib/zpath_lib.h"
#include "zpath_lib/zpa_cloud_config.h"

int64_t assistant_features_alt_cloud_enabled = 0;

/* assistant forward proxy sni server configs */
int64_t assistant_features_fproxy_enabled = 0;
char* proxy_listen_ip = "127.0.0.1";
/* listen port for forward proxy. Note: this is different from fohh proxy port */
int fproxy_port = 9010;

static
struct assistant_features {                                      /* _ARGO: object_definition */
    /*
     * UT mode is to run the connector in a mode that is meant only for testing itself. Each file or data structures
     * in turn will have more granular knob on what UT test to run which is effective only if this global knob is set.
     * We don't want to be running ut mode in production - code will make sure this assumption is always correct
     */
    int         ut_mode;                                         /* _ARGO: integer */

    /*
     * For a health based transaction, prefer local cache over dispatcher's cache.
     */
    int         health_txn_prefer_local_cache_over_dsp_cache;    /* _ARGO: integer */
    /*
     * Flag to enable/disable the use of assistant ncache to track failed app servers.
     */
    int         use_assistant_ncache_to_track_failed_app_servers; /* _ARGO: integer */

    int         config_quickack;                                  /* _ARGO: integer */
    int         config_quickack_read;                             /* _ARGO: integer */
    /*
     * Flag to enable/disable setting of Libevent low write watermark
     */
    int         libevent_low_write_watermark;                     /* _ARGO: integer */

    /*
     * Guacamole protocol forwarding to local guacd process -- DEFUNCT
     */
    int         guacamole_protocol_support;                      /* _ARGO: integer */

    int         config_app_buffer_tune;                          /* _ARGO: integer */

    int         fohh_flow_control_enhancements;                  /* _ARGO: integer */

    int         batched_mconn_window_updates;                    /* _ARGO: integer */

    int         syn_app_rtt;                                     /* _ARGO: integer */
    int         pipeline_latency_trace;                          /* _ARGO: integer */
} features;

static
struct assistant_features_stats {                                 /* _ARGO: object_definition */
    uint64_t place_holder;                                        /* _ARGO: integer */
} stats;
#include "zpn_assistantd/assistant_features_compiled_c.h"


static struct argo_structure_description* assistant_features_description;
static struct argo_structure_description* assistant_features_stats_description;

static int
assistant_features_dump(struct zpath_debug_state*   request_state,
                        const char **               query_values,
                        int                         query_value_count,
                        void*                       cookie);
static int
assistant_features_stats_dump(struct zpath_debug_state*   request_state,
                              const char **               query_values,
                              int                         query_value_count,
                              void*                       cookie);
static int
assistant_features_toggle_health_txn_prefer_local_cache_over_dsp_cache(struct zpath_debug_state* request_state,
                                                                       const char**              query_values,
                                                                       int                       query_value_count,
                                                                       void*                     cookie);


int
assistant_features_init()
{
    int result;

    if (!(assistant_features_stats_description=
                  argo_register_global_structure(ASSISTANT_FEATURES_STATS_HELPER))) {
        return ZPATH_RESULT_ERR;
    }

    if (!(assistant_features_description =
                  argo_register_global_structure(ASSISTANT_FEATURES_HELPER))) {
        return ZPATH_RESULT_ERR;
    }

    result = zpath_debug_add_read_command("Dump assistant's pluggable features info.",
                                     "/assistant/features/state/dump", assistant_features_dump, NULL,
                                     NULL);
    if (result) {
        ASSISTANT_LOG(AL_ERROR, "couldn't add /assistant/features/state/dump");
        return result;
    }

    result = zpath_debug_add_read_command("Dump stats related to assistant's enabled features.",
                                     "/assistant/features/stats/dump", assistant_features_stats_dump, NULL,
                                     NULL);
    if (result) {
        ASSISTANT_LOG(AL_ERROR, "couldn't add /assistant/features/stats/dump");
        return result;
    }


    /*
     * curl '127.0.0.1:9000/assistant/features/health_txn_prefer_local_cache_over_dsp_cache/toggle
     */
    if (zpath_debug_add_write_command("Turn on/off the feature to either prefer local cache over dispatcher cache in pathing decision or vice versa",
                                "/assistant/features/health_txn_prefer_local_cache_over_dsp_cache/toggle",
                                assistant_features_toggle_health_txn_prefer_local_cache_over_dsp_cache,
                                NULL, NULL)) {
        ASSISTANT_LOG(AL_NOTICE, "Unable to register /assistant/features/health_txn_prefer_local_cache_over_dsp_cache/toggle");
    }

    // Do not enable any of the features implicitly for the sake of clarity. Prevents any feature enabled accidentally.
    return ZPN_RESULT_NO_ERROR;
}

void
assistant_features_stats_log()
{
    char features_log[10000] = { '\0' };
    char *start;
    char *end;

    start = features_log;
    end = features_log + sizeof(features_log);

    int num_of_features_enabled = 0;

    if (features.ut_mode) {
        start += sxprintf(start, end, "ut_mode,");
        num_of_features_enabled ++;
    }
    if (features.use_assistant_ncache_to_track_failed_app_servers) {
        start += sxprintf(start, end, "use_assistant_ncache_to_track_failed_app_servers,");
        num_of_features_enabled ++;
    }
    if (features.health_txn_prefer_local_cache_over_dsp_cache) {
        start += sxprintf(start, end, "health_txn_prefer_local_cache_over_dsp_cache,");
        num_of_features_enabled ++;
    }
    if (features.config_quickack_read) {
        start += sxprintf(start, end, "config_quickack_read,");
        num_of_features_enabled ++;
    }
    if (features.libevent_low_write_watermark) {
        start += sxprintf(start, end, "libevent_low_write_watermark,");
        num_of_features_enabled ++;
    }
    if (features.config_quickack) {
        start += sxprintf(start, end, "config_quickack,");
        num_of_features_enabled ++;
    }

    if (features.fohh_flow_control_enhancements) {
        start += sxprintf(start, end, "fohh_flow_control_enhancements,");
        num_of_features_enabled ++;
    }

    if (features.batched_mconn_window_updates) {
        start += sxprintf(start, end, "batched_mconn_window_updates,");
        num_of_features_enabled ++;
    }

    if (features.syn_app_rtt) {
        start += sxprintf(start, end, "syn_app_rtt,");
        num_of_features_enabled ++;
	}
    if (features.pipeline_latency_trace) {
        start += sxprintf(start, end, "pipeline_latency_trace,");
        num_of_features_enabled ++;
    }

    if (!num_of_features_enabled) {
        return;
    }

    sxprintf(start - 1, end, (num_of_features_enabled == 1) ? " is enabled" : " are enabled");

    ZPN_DEBUG_COR("Assistant feature stats: %s", features_log);
}

void
assistant_features_enable_ut_mode()
{
    if (!assistant_state_is_dev_environment()) {
        return;
    }
    features.ut_mode= 1;
}

int
assistant_features_is_enabled_ut_mode()
{
    return features.ut_mode;

}


void
assistant_features_enable_health_txn_prefer_local_cache_over_dsp_cache()
{
    features.health_txn_prefer_local_cache_over_dsp_cache = 1;
}

void
assistant_features_disable_health_txn_prefer_local_cache_over_dsp_cache()
{
    features.health_txn_prefer_local_cache_over_dsp_cache = 0;
}

int
assistant_features_is_health_txn_prefer_local_cache_over_dsp_cache()
{
    return features.health_txn_prefer_local_cache_over_dsp_cache;
}

static int
assistant_features_toggle_health_txn_prefer_local_cache_over_dsp_cache(struct zpath_debug_state* request_state,
                                                                       const char**              query_values,
                                                                       int                       query_value_count,
                                                                       void*                     cookie)
{
    if (assistant_features_is_health_txn_prefer_local_cache_over_dsp_cache()) {
        assistant_features_disable_health_txn_prefer_local_cache_over_dsp_cache();
        ZDP("Feature turned off\n");
    } else {
        assistant_features_enable_health_txn_prefer_local_cache_over_dsp_cache();
        ZDP("Feature turned on\n");
    }
    return ZPATH_RESULT_NO_ERROR;
}


static int
assistant_features_dump(struct zpath_debug_state*   request_state,
                        const char **               query_values,
                        int                         query_value_count,
                        void*                       cookie)
{
    char        jsonout[10000];

    if (ARGO_RESULT_NO_ERROR ==
        argo_structure_dump(assistant_features_description, &features, jsonout, sizeof(jsonout), NULL, 1)){
        ZDP("%s\n", jsonout);
    }

    return ZPATH_RESULT_NO_ERROR;
}

static int
assistant_features_stats_dump(struct zpath_debug_state*   request_state,
                              const char **               query_values,
                              int                         query_value_count,
                              void*                       cookie)
{
    char        jsonout[10000];

    if (ARGO_RESULT_NO_ERROR ==
        argo_structure_dump(assistant_features_stats_description, &stats, jsonout, sizeof(jsonout), NULL, 1)){
        ZDP("%s\n", jsonout);
    }

    return ZPATH_RESULT_NO_ERROR;
}

int assistant_get_max_pause_time(int64_t connector_gid)
{
    int64_t config_value = 0;

    config_value = zpath_config_override_get_config_int(ASSISTANT_MAX_PAUSE_TIME_CONFIG,
                                                        &config_value,
                                                        DEFAULT_ASST_MAX_PAUSE_TIME,
                                                        connector_gid,
                                                        ZPATH_GID_GET_CUSTOMER_GID(connector_gid),
                                                        (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                        (int64_t)0);

    return (config_value) ? config_value :  DEFAULT_ASST_MAX_PAUSE_TIME;
}

static void assistant_monitor_max_pause_time_callback(const int64_t *config_value, int64_t impacted_gid)
{
    ASSISTANT_LOG(AL_NOTICE, "max pause time changed to %"PRId64"", (uint64_t)*config_value);
}

void assistant_monitor_max_pause_time(int64_t connector_gid)
{
    zpath_config_override_monitor_int(ASSISTANT_MAX_PAUSE_TIME_CONFIG,
                                      &global_assistant.max_pause_time_interval,
                                      assistant_monitor_max_pause_time_callback,
                                      DEFAULT_ASST_MAX_PAUSE_TIME,
                                      connector_gid,
                                      ZPATH_GID_GET_CUSTOMER_GID(connector_gid),
                                      (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                      (int64_t)0);

}
int assistant_features_is_dtls_enabled (int64_t connector_gid, int64_t *connector_grp_gid, int grp_gid_cnt)
{
    int64_t config_value = 0;
    int i;

    for (i = 0; i < grp_gid_cnt; i++) {
        config_value = zpath_config_override_get_config_int(DTLS_FEATURE,
                                                            &config_value,
                                                            DEFAULT_ASST_DTLS,
                                                            connector_gid,
                                                            *(connector_grp_gid + i),
                                                            ZPATH_GID_GET_CUSTOMER_GID(connector_gid),
                                                            (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                            (int64_t)0);
        if (config_value) break;
    }
    return config_value?1:0;
}

int assistant_to_broker_dtls_mtu (int64_t connector_gid, int64_t *connector_grp_gid, int grp_gid_cnt) {
    int64_t mtu = 0;
    int i;

    for (i = 0; i < grp_gid_cnt; i++) {
        mtu = zpath_config_override_get_config_int(DTLS_FEATURE_MTU,
                                                   &mtu,
                                                   DEFAULT_STREAM_MTU,
                                                   connector_gid,
                                                   *(connector_grp_gid + i),
                                                   ZPATH_GID_GET_CUSTOMER_GID(connector_gid),
                                                   (int64_t)0);
        if (mtu != DEFAULT_STREAM_MTU) {
            break;
        }
    }
    ZPN_DEBUG_COR("config value for DTLS connection to assistant gid %ld is %ld",
                  (long)connector_gid, (long)mtu);
    return mtu;
}

int assistant_features_is_assistant_ncache_enabled(void)
{
    return (features.use_assistant_ncache_to_track_failed_app_servers);
}

void assistant_features_enable_assistant_ncache(void)
{
    features.use_assistant_ncache_to_track_failed_app_servers = 1;
}

int assistant_features_is_quickack_enabled (int64_t connector_gid, int64_t *connector_grp_gid, int grp_gid_cnt)
{
    int64_t config_value = 0;
    int i;

    for (i = 0; i < grp_gid_cnt; i++) {
        config_value = zpath_config_override_get_config_int(QUICKACK_CONFIG_ASSISTANT,
                                                            &config_value,
                                                            DEFAULT_ASST_QUICKACK,
                                                            connector_gid,
                                                            *(connector_grp_gid + i),
                                                            ZPATH_GID_GET_CUSTOMER_GID(connector_gid),
                                                            (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                            (int64_t)0);
        if (config_value) break;
    }

    features.config_quickack = config_value;

    ASSISTANT_DEBUG_DATA("quickack config value for assistant_id = %ld is config_value = %ld", (long)connector_gid, (long)config_value);

    return config_value?1:0;
}

int assistant_features_is_quickack_read_enabled (int64_t connector_gid, int64_t *connector_grp_gid, int grp_gid_cnt)
{
    int64_t config_value = 0;
    int i;

    for (i = 0; i < grp_gid_cnt; i++) {
        config_value = zpath_config_override_get_config_int(QUICKACK_READ_CONFIG_ASSISTANT,
                                                            &config_value,
                                                            DEFAULT_ASST_QUICKACK_READ,
                                                            connector_gid,
                                                            *(connector_grp_gid + i),
                                                            ZPATH_GID_GET_CUSTOMER_GID(connector_gid),
                                                            (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                            (int64_t)0);
        if (config_value) break;
    }
    features.config_quickack_read = config_value;
    return config_value?1:0;
}

int assistant_features_is_low_write_watermark_enabled (int64_t connector_gid)
{
    int64_t config_value = 0;

    config_value = zpath_config_override_get_config_int(LIBEVENT_LOW_WRITE_WATERMARK_CONNECTOR,
                                                        &config_value,
                                                        LIBEVENT_LOW_WRITE_WATERMARK_DEFAULT,
                                                        ZPATH_GID_GET_CUSTOMER_GID(connector_gid),
                                                        (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                        (int64_t)0);
    features.libevent_low_write_watermark = config_value;
    return config_value?1:0;
}

int assistant_features_is_quickack_app_enabled (int64_t connector_gid, int64_t *connector_grp_gid, int grp_gid_cnt)
{
    int64_t config_value = 0;
    int i;

    for (i = 0; i < grp_gid_cnt; i++) {
        config_value = zpath_config_override_get_config_int(ASSISTANT_QUICKACK_APP_CONFIG,
                                                            &config_value,
                                                            DEFAULT_ASST_QUICKACK_APP,
                                                            connector_gid,
                                                            *(connector_grp_gid + i),
                                                            ZPATH_GID_GET_CUSTOMER_GID(connector_gid),
                                                            (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                            (int64_t)0);
        if (config_value) break;
    }
    features.config_quickack_read = config_value;
    return config_value?1:0;
}

char* assistant_features_get_sarge_minimum_version (int64_t connector_gid, int64_t *connector_grp_gid, int grp_gid_cnt)
{
    char* config_value = NULL;

    for (int i = 0; i < grp_gid_cnt; i++) {
        config_value = zpath_config_override_get_config_str(SARGE_MINIMUM_VERSION,
                                                            &config_value,
                                                            DEFAULT_SARGE_MINIMUM_VERSION,
                                                            connector_gid,
                                                            *(connector_grp_gid + i),
                                                            ZPATH_GID_GET_CUSTOMER_GID(connector_gid),
                                                            (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                            (int64_t)0);
        if (config_value) break;
    }
    return config_value;
}

int assistant_features_get_zhealth_probe_lib_socket_engine_cb(int sys_type, int64_t sys_gid, int64_t sys_grp_gid)
{
    int value = zpn_zdx_zhealth_probe_lib_config_get_socket_engine_cb(sys_type, sys_gid, sys_grp_gid);
    ASSISTANT_LOG(AL_NOTICE, "Assistant config get for zhealth probe lib socket engine %d", value);
    return value;
}

int assistant_features_is_app_buffer_tune_enabled(int64_t connector_gid, int64_t *connector_grp_gid, int grp_gid_cnt)
{
    int64_t config_value = 0;
    int i;

    for (i = 0; i < grp_gid_cnt; i++) {
        config_value = zpath_config_override_get_config_int(ASSISTANT_APP_BUFFER_TUNE_FEATURE,
                                                            &config_value,
                                                            ASSISTANT_APP_BUFFER_TUNE_FEATURE_DEFAULT_VALUE,
                                                            connector_gid,
                                                            *(connector_grp_gid + i),
                                                            ZPATH_GID_GET_CUSTOMER_GID(connector_gid),
                                                            (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                            (int64_t)0);
        if (config_value) break;
    }

    features.config_app_buffer_tune = config_value;

    ASSISTANT_DEBUG_DATA("app buffer tune feature config value for assistant_id = %ld is config_value = %ld", (long)connector_gid, (long)config_value);

    return config_value?1:0;
}


int64_t assistant_config_get_fohh_window(int64_t connector_gid, int64_t *connector_grp_gid, int grp_gid_cnt)
{
    int64_t config_value = 0;
    int i;

    for (i = 0; i < grp_gid_cnt; i++) {
        config_value = zpath_config_override_get_config_int(ASSISTANT_APP_BUFFER_CONFIG,
                                                            &config_value,
                                                            ASSISTANT_APP_BUFFER_CONFIG_DEFAULT_VALUE,
                                                            connector_gid,
                                                            *(connector_grp_gid + i),
                                                            ZPATH_GID_GET_CUSTOMER_GID(connector_gid),
                                                            (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                            (int64_t)0);
        if (config_value) break;
    }

    ASSISTANT_DEBUG_DATA("app buffer tune fohh window value for assistant_id = %ld is config_value = %ld", (long)connector_gid, (long)config_value);

    return config_value;
}

int64_t assistant_config_get_fohh_mconn_window(int64_t connector_gid, int64_t *connector_grp_gid, int grp_gid_cnt)
{
    int64_t config_value = 0;
    int i;

    for (i = 0; i < grp_gid_cnt; i++) {
        config_value = zpath_config_override_get_config_int(ASSISTANT_APP_BUFFER_MCONN_CONFIG,
                                                            &config_value,
                                                            ASSISTANT_APP_BUFFER_MCONN_CONFIG_DEFAULT_VALUE,
                                                            connector_gid,
                                                            *(connector_grp_gid + i),
                                                            ZPATH_GID_GET_CUSTOMER_GID(connector_gid),
                                                            (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                            (int64_t)0);
        if (config_value) break;
    }

    ASSISTANT_DEBUG_DATA("app buffer tune fohh mconn window value for assistant_id = %ld is config_value = %ld", (long)connector_gid, (long)config_value);

    return config_value;
}

int64_t assistant_config_get_fohh_watermark (int64_t connector_gid, int64_t *connector_grp_gid, int grp_gid_cnt)
{
    int64_t config_value = 0;
    int i;

    for (i = 0; i < grp_gid_cnt; i++) {
        config_value = zpath_config_override_get_config_int(ASSISTANT_APP_BUFFER_WATERMARK,
                                                            &config_value,
                                                            ASSISTANT_APP_BUFFER_WATERMARK_DEFAULT_VALUE,
                                                            connector_gid,
                                                            *(connector_grp_gid + i),
                                                            ZPATH_GID_GET_CUSTOMER_GID(connector_gid),
                                                            (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                            (int64_t)0);
        if (config_value) break;
    }

    ASSISTANT_DEBUG_DATA("app buffer tune fohh watermark value for assistant_id = %ld is config_value = %ld", (long)connector_gid, (long)config_value);

    return config_value;
}

int64_t assistant_config_get_fohh_mconn_watermark (int64_t connector_gid, int64_t *connector_grp_gid, int grp_gid_cnt)
{
    int64_t config_value = 0;
    int i;

    for (i = 0; i < grp_gid_cnt; i++) {
        config_value = zpath_config_override_get_config_int(ASSISTANT_APP_BUFFER_MCONN_WATERMARK,
                                                            &config_value,
                                                            ASSISTANT_APP_BUFFER_MCONN_WATERMARK_DEFAULT_VALUE,
                                                            connector_gid,
                                                            *(connector_grp_gid + i),
                                                            ZPATH_GID_GET_CUSTOMER_GID(connector_gid),
                                                            (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                            (int64_t)0);
        if (config_value) break;
    }

    ASSISTANT_DEBUG_DATA("app buffer tune fohh mconn watermark value for assistant_id = %ld is config_value = %ld", (long)connector_gid, (long)config_value);

    return config_value;
}

static void assistant_features_alt_cloud_cfg_monitor_callback(const int64_t *config_value, int64_t impacted_gid)
{
    int need_reset = assistant_state_store_alt_domain_config_with_lock(*config_value, NULL);
    if (need_reset) {
        ASSISTANT_LOG(AL_NOTICE, "Alternate cloud support changed to %s, resetting all connections",
                                 global_assistant.alt_cloud_enabled ? "enabled" : "disabled");
        assistant_state_reset_connections_alt_cloud_toggle(0/*Do not use default*/, 1/*reset pse conns*/);
    }
}


static void assistant_features_fproxy_config_override_monitor_callback(const int64_t *config_value, int64_t impacted_gid)
{
    int32_t feature_enabled = *config_value ? 1 : 0;

    ASSISTANT_LOG(AL_INFO, "Connector fproxy feature status set to %s", feature_enabled ? "enabled" : "disabled");

    if (feature_enabled && !global_assistant.fproxy_enabled) {
        /* feature status flipped to enabled, initializing fproxy module*/
        assistant_features_fproxy_init();
        global_assistant.fproxy_enabled = 1;
    } else if (!feature_enabled && global_assistant.fproxy_enabled) {
        /* feature status flipped to disabled, unregister domains
         * note: we do not close the proxy server, we only unregister domains */
        assistant_features_fproxy_unregister();
        global_assistant.fproxy_enabled = 0;
    }

    return;
}

int assistant_features_is_alt_cloud_enabled(int64_t connector_gid)
{
    int64_t config_value = 0;

    config_value = zpath_config_override_get_config_int(CONFIG_FEATURE_ALT_CLOUD,
                                                        &config_value,
                                                        CONFIG_FEATURE_ALT_CLOUD_DEFAULT_VALUE,
                                                        connector_gid,
                                                        ZPATH_GID_GET_CUSTOMER_GID(connector_gid),
                                                        (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                        (int64_t)0);
    return config_value?1:0;
}

void assistant_features_alt_cloud_cfg_monitor_init(int64_t connector_gid)
{
    zpath_config_override_monitor_int(CONFIG_FEATURE_ALT_CLOUD,
                                      &assistant_features_alt_cloud_enabled,
                                      assistant_features_alt_cloud_cfg_monitor_callback,
                                      CONFIG_FEATURE_ALT_CLOUD_DEFAULT_VALUE,
                                      connector_gid,
                                      ZPATH_GID_GET_CUSTOMER_GID(connector_gid),
                                      (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                      (int64_t)0);

}

int64_t assistant_fohh_flow_control_enhancement_enabled(int64_t customer_gid)
{
    int64_t value;
    value = zpath_config_override_get_config_int(FOHH_FLOW_CONTROL_ENHANCEMENTS,
                                                 &value,
                                                 DEFAULT_FOHH_FLOW_CONTROL_ENHANCEMENTS,
                                                 customer_gid,
                                                 (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                 (int64_t)0);
    return value;
}

int assistant_features_batched_mconn_window_updates_enabled(int64_t connector_gid, int64_t *connector_grp_gid, int grp_gid_cnt)
{
  assert(connector_grp_gid);
  int64_t batched_mconn_window_updates = CONFIG_FEATURE_CONNECTOR_MCONN_BATCH_WINDOW_UPDATES_DEFAULT;
  int i;
  for (i = 0; i < grp_gid_cnt; i++) {
    batched_mconn_window_updates = zpath_config_override_get_config_int(
      CONFIG_FEATURE_CONNECTOR_MCONN_BATCH_WINDOW_UPDATES,
      &batched_mconn_window_updates,
      CONFIG_FEATURE_CONNECTOR_MCONN_BATCH_WINDOW_UPDATES_DEFAULT,
      connector_gid,
      *(connector_grp_gid + i),
      ZPATH_GID_GET_CUSTOMER_GID(connector_gid),
      (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
      (int64_t)0);
    if (batched_mconn_window_updates) break;
  }
  features.batched_mconn_window_updates = (int)batched_mconn_window_updates;
  return batched_mconn_window_updates != 0;
}

int assistant_features_syn_app_rtt_enabled(int64_t connector_gid, int64_t *connector_grp_gid, int grp_gid_cnt)
{
  assert(connector_grp_gid);
  int64_t syn_app_rtt = CONFIG_FEATURE_CONNECTOR_SYN_APP_RTT_DEFAULT;
  int i;
  for (i = 0; i < grp_gid_cnt; i++) {
    syn_app_rtt = zpath_config_override_get_config_int(
      CONFIG_FEATURE_CONNECTOR_SYN_APP_RTT,
      &syn_app_rtt,
      CONFIG_FEATURE_CONNECTOR_SYN_APP_RTT_DEFAULT,
      connector_gid,
      *(connector_grp_gid + i),
      ZPATH_GID_GET_CUSTOMER_GID(connector_gid),
      (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
      (int64_t)0);
    if (syn_app_rtt) break;
  }
  features.syn_app_rtt = (int)syn_app_rtt;
  return syn_app_rtt != 0;
}

int assistant_features_pipeline_latency_trace_enabled(int64_t connector_gid, int64_t *connector_grp_gid, int grp_gid_cnt)
{
  assert(connector_grp_gid);
  int64_t pipeline_latency_trace = CONFIG_FEATURE_CONNECTOR_FOHH_PIPELINE_LATENCY_TRACE_DEFAULT;  // 0/disabled
  int i;
  for (i = 0; i < grp_gid_cnt; i++) {
    pipeline_latency_trace = zpath_config_override_get_config_int(
      CONFIG_FEATURE_CONNECTOR_FOHH_PIPELINE_LATENCY_TRACE,
      &pipeline_latency_trace,
      CONFIG_FEATURE_CONNECTOR_FOHH_PIPELINE_LATENCY_TRACE_DEFAULT,
      connector_gid,
      *(connector_grp_gid + i),
      ZPATH_GID_GET_CUSTOMER_GID(connector_gid),
      (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
      (int64_t)0);
    if (pipeline_latency_trace) break;
  }
  features.pipeline_latency_trace = (int)pipeline_latency_trace;
  return pipeline_latency_trace != 0;
}

static int
assistant_features_fproxy_stats_dump(struct zpath_debug_state*  request_state,
                                     const char**               query_values,
                                     int                        query_value_count,
                                     void*                      cookie)
{
    (void)query_values;
    (void)query_value_count;
    (void)cookie;

    struct fohh_fproxy_conn_stats stats = {0};
    if (!assistant_features_is_fproxy_enabled(global_assistant.gid)) {
        ZDP("fproxy feature is disabled!");
        return ZPATH_RESULT_NO_ERROR;
    }
    fohh_fproxy_get_stats(&stats);

    ZDP("num_allocations: %ld\n", (long)stats.num_allocations);
    ZDP("free_queue_count: %ld\n", (long)stats.free_queue_count);
    ZDP("num_fproxy_conn_alive: %ld\n", (long)stats.num_fproxy_conn_alive);
    ZDP("num_fproxy_conns: %ld\n", (long)stats.num_fproxy_conns);
    ZDP("fproxy_rx_bytes: %ld\n", (long)stats.fproxy_rx_bytes);
    ZDP("fproxy_tx_bytes: %ld\n", (long)stats.fproxy_tx_bytes);

    return ZPATH_RESULT_NO_ERROR;
}

static int
assistant_features_fproxy_registered_domains_dump(struct zpath_debug_state*  request_state,
                                                  const char**               query_values,
                                                  int                        query_value_count,
                                                  void*                      cookie)
{
    (void)query_values;
    (void)query_value_count;
    (void)cookie;

    struct zpath_authenticated_sni_map *zpath_authenticated_snis;
    char *cloud_name = assistant_state_get_cloud_name();
    size_t zpath_authenticated_sni_count = zpath_get_zpath_authenticated_sni_for_cloud(cloud_name, &zpath_authenticated_snis);
    ZDP("Domain list:\n");
    for (int i = 0; i < zpath_authenticated_sni_count; i++) {
        if (zpath_authenticated_snis[i].client_type != zpath_broker_proxy_ssl_client_type_connector) {
            continue;
        }
        ZDP("domain: %s, wildcard_prefix: %d\n", zpath_authenticated_snis[i].client_domain, zpath_authenticated_snis[i].client_domain_wildcard_prefix);
    }

    return ZPATH_RESULT_NO_ERROR;
}

static void assistant_features_fproxy_sni_update(char* domain,
                                                 void *cookie,
                                                 int64_t int_cookie)
{
    struct fproxy_conn *fp_conn = cookie;
    int need_cloud_suffix = int_cookie ? 1 : 0;
    char* cloud_name;
    char alt_cloud_name[256];

    if (global_assistant.alt_cloud && global_assistant.alt_cloud_enabled) {
        assistant_state_get_alt_cloud_with_lock(alt_cloud_name);
        cloud_name = alt_cloud_name;
    } else {
        cloud_name = assistant_state_get_cloud_name();
    }

    if (need_cloud_suffix) {
        snprintf(fp_conn->remote_sni, sizeof(fp_conn->remote_sni), "%ld.%s.%s", (long)global_assistant.gid, domain, cloud_name);
    } else {
        snprintf(fp_conn->remote_sni, sizeof(fp_conn->remote_sni), "%ld.%s", (long)global_assistant.gid, domain);
    }
}

int assistant_features_fproxy_init()
{
    char        broker_name[1000];
    char*       cloud_name;
    char        alt_cloud_name[256];
    int         res = ZPN_RESULT_NO_ERROR;

    if (global_assistant.alt_cloud && global_assistant.alt_cloud_enabled) {
        assistant_state_get_alt_cloud_with_lock(alt_cloud_name);
        cloud_name = alt_cloud_name;
    } else {
        cloud_name = assistant_state_get_cloud_name();
    }

    if (global_assistant.hardcoded_broker_name) {
        snprintf(broker_name, sizeof(broker_name), "%s", global_assistant.hardcoded_broker_name);
    } else {
        snprintf(broker_name, sizeof(broker_name), "any.co2ubr.%s", cloud_name);
    }

    if (global_assistant.proxy_server_initialized == 0) {
        res = fohh_fproxy_init(&(global_assistant.proxy_server),
                               proxy_listen_ip,
                               fproxy_port,
                               global_assistant.assistant_to_public_cloud_ctx,
                               broker_name,
                               assistant_features_fproxy_sni_update,
                               NULL); /*no specific worker pool*/
        if (!global_assistant.proxy_server) {
            ASSISTANT_LOG(AL_ERROR, "Cannot create or register proxy server on IP: %s, port: %d",
                                proxy_listen_ip, fproxy_port);
            return res;
        }

        global_assistant.proxy_server_lock = (pthread_mutex_t) PTHREAD_MUTEX_INITIALIZER;
        global_assistant.proxy_server_initialized = 1;

        res = zpath_debug_add_read_command("Dump assistant fproxy connection stats",
                                      "/assistant/features/fproxy/stats",
                                      assistant_features_fproxy_stats_dump,
                                      NULL,
                                      NULL);
        if (res) {
            ASSISTANT_LOG(AL_ERROR, "Could not register /assistant/features/fproxy/stats");
        }

        if (assistant_state_is_dev_environment()) {
            res = zpath_debug_add_read_command("Dump assistant fproxy registered domains (dev cloud only)",
                                        "/assistant/features/fproxy/dump_domains",
                                        assistant_features_fproxy_registered_domains_dump,
                                        NULL,
                                        NULL);
            if (res) {
                ASSISTANT_LOG(AL_ERROR, "Could not register /assistant/features/fproxy/dump_domains");
            }
        }
    }

    if (!global_assistant.proxy_server) {
        ASSISTANT_LOG(AL_ERROR, "fproxy server initialized but is null? this should never happen");
        return ZPN_RESULT_ERR;
    }

    struct zpath_authenticated_sni_map *zpath_authenticated_snis;
    size_t zpath_authenticated_sni_count = zpath_get_zpath_authenticated_sni_for_cloud(cloud_name, &zpath_authenticated_snis);
    /* the following can be called multiple times (i.e. feature toggle events) */
    pthread_mutex_lock(&(global_assistant.proxy_server_lock));
    for (int i = 0; i < zpath_authenticated_sni_count; i++) {
        if (zpath_authenticated_snis[i].client_type != zpath_broker_proxy_ssl_client_type_connector) {
            continue;
        }
        fohh_fproxy_proxy_add(zpath_authenticated_snis[i].client_domain,
                              zpath_authenticated_snis[i].client_domain_wildcard_prefix,
                              zpath_authenticated_snis[i].broker_domain,
                              zpath_authenticated_snis[i].broker_domain_cloud_suffix);
    }
    pthread_mutex_unlock(&(global_assistant.proxy_server_lock));
    return res;
}

void assistant_features_fproxy_unregister()
{
    struct zpath_authenticated_sni_map *zpath_authenticated_snis;
    char *cloud_name = assistant_state_get_cloud_name();
    size_t zpath_authenticated_sni_count = zpath_get_zpath_authenticated_sni_for_cloud(cloud_name, &zpath_authenticated_snis);
    pthread_mutex_lock(&(global_assistant.proxy_server_lock));
    for (int i = 0; i < zpath_authenticated_sni_count; i++) {
        if (zpath_authenticated_snis[i].client_type != zpath_broker_proxy_ssl_client_type_connector) {
            continue;
        }
        fohh_fproxy_proxy_delete(zpath_authenticated_snis[i].client_domain, zpath_authenticated_snis[i].client_domain_wildcard_prefix);
    }
    pthread_mutex_unlock(&(global_assistant.proxy_server_lock));
    return;
}

int assistant_features_is_fproxy_enabled(int64_t connector_gid)
{
    int64_t config_value = 0;

    config_value = zpath_config_override_get_config_int(CONFIG_FEATURE_ASSISTANT_FPROXY,
                                                        &config_value,
                                                        CONFIG_FEATURE_ASSISTANT_FPROXY_DEFAULT_STATUS,
                                                        connector_gid,
                                                        ZPATH_GID_GET_CUSTOMER_GID(connector_gid),
                                                        (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                        (int64_t)0);
    return config_value?1:0;
}

void assistant_features_fproxy_cfg_monitor_init(int64_t connector_gid)
{
    zpath_config_override_monitor_int(CONFIG_FEATURE_ASSISTANT_FPROXY,
                                      &assistant_features_fproxy_enabled,
                                      assistant_features_fproxy_config_override_monitor_callback,
                                      CONFIG_FEATURE_ASSISTANT_FPROXY_DEFAULT_STATUS,
                                      connector_gid,
                                      ZPATH_GID_GET_CUSTOMER_GID(connector_gid),
                                      (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                      (int64_t)0);
}

int assistant_features_is_qbr_insights_feature_enabled(int64_t connector_gid, int64_t *connector_grp_gid, int grp_gid_cnt)
{
    int64_t config_value = CONFIG_FEATURE_QBR_INSIGHTS_FEATURE_DEFAULT;
    int i;

    for (i = 0; i < grp_gid_cnt; i++) {
        config_value = zpath_config_override_get_config_int(CONFIG_FEATURE_CONN_QBR_INSIGHTS_FEATURE,
                                                            &config_value,
                                                            CONFIG_FEATURE_QBR_INSIGHTS_FEATURE_DEFAULT,
                                                            connector_gid,
                                                            *(connector_grp_gid + i),
                                                            ZPATH_GID_GET_CUSTOMER_GID(connector_gid),
                                                            (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                            (int64_t)0);
        if (config_value) break;
    }

    return config_value ? 1 : 0;
}

void assistant_features_fproxy_log_status()
{
    struct fohh_fproxy_conn_stats stats = {0};
    fohh_fproxy_get_stats(&stats);
    ASSISTANT_LOG(AL_NOTICE, "Local forward proxy conn stats (feature %s) alloc: %ld, free queue: %ld, num active conn: %ld, total conn: %ld, rx bytes: %ld, tx bytes: %ld",
                    assistant_features_is_fproxy_enabled(global_assistant.gid) ? "enabled" : "disabled",
                    (long)stats.num_allocations, (long)stats.free_queue_count, (long)stats.num_fproxy_conn_alive,
                    (long)stats.num_fproxy_conns, (long)stats.fproxy_rx_bytes, (long)stats.fproxy_tx_bytes);
}

int
assistant_fproxy_stats_fill(void*     cookie,
                            int       counter,
                            void*     structure_data)
{
    struct fohh_fproxy_conn_stats stats = {0};
    struct zpn_assistant_fproxy_stats* out_data;

    out_data = (struct zpn_assistant_fproxy_stats *)structure_data;

    if (assistant_features_is_fproxy_enabled(global_assistant.gid)) {
        fohh_fproxy_get_stats(&stats);
    }

    out_data->num_allocations = stats.num_allocations;
    out_data->free_queue_count = stats.free_queue_count;
    out_data->num_fproxy_conn_alive = stats.num_fproxy_conn_alive;
    out_data->num_fproxy_conns = stats.num_fproxy_conns;
    out_data->fproxy_rx_bytes = stats.fproxy_rx_bytes;
    out_data->fproxy_tx_bytes = stats.fproxy_tx_bytes;

    return ZPATH_RESULT_NO_ERROR;
}

int assistant_features_is_databroker_resilience_enabled(int64_t connector_gid, int64_t *connector_grp_gid, int grp_gid_cnt)
{
    int64_t config_value = 0;
    int i;

    for (i = 0; i < grp_gid_cnt; i++) {
        config_value = zpath_config_override_get_config_int(CONFIG_FEATURE_ASST_DATABROKER_RESILIENCE,
                                                            &config_value,
                                                            DEFAULT_ASST_DATABROKER_RESILIENCE,
                                                            connector_gid,
                                                            *(connector_grp_gid + i),
                                                            ZPATH_GID_GET_CUSTOMER_GID(connector_gid),
                                                            (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                            (int64_t)0);
        if (config_value) break;
    }
    //ASSISTANT_DEBUG_TWO_HOP("TWO_HOP ASST double hop config_value: %ld for assistant_id: %ld", (long)config_value, (long)connector_gid);
    return config_value?1:0;
}

int64_t assistant_features_mtunnel_fin_expire_us(int64_t connector_gid)
{
    int64_t config_value = 0;

    config_value = zpath_config_override_get_config_int(ZPN_ASSISTANT_MTUNNEL_FIN_EXPIRE_TIME_S,
                                                        &config_value,
                                                        ZPN_ASSISTANT_MTUNNEL_FIN_EXPIRE_TIME_S_DEFAULT,
                                                        connector_gid,
                                                        ZPATH_GID_GET_CUSTOMER_GID(connector_gid),
                                                        (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                        (int64_t)0);
    config_value = SECOND_TO_US(config_value);
    return config_value;
}

int assistant_get_doublehop_timeout(int64_t connector_gid)
{
    int64_t config_value = 0;

    config_value = zpath_config_override_get_config_int(ASSISTANT_DOUBLE_HOP_TIMEOUT,
                                                        &config_value,
                                                        DEFAULT_ASST_DOUBLE_HOP_TIMEOUT,
                                                        connector_gid,
                                                        ZPATH_GID_GET_CUSTOMER_GID(connector_gid),
                                                        (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                        (int64_t)0);

    return (config_value) ? config_value :  DEFAULT_ASST_DOUBLE_HOP_TIMEOUT;
}

int assistant_get_doublehop_switch_timeout(int64_t connector_gid)
{
    int64_t config_value = 0;

    config_value = zpath_config_override_get_config_int(ASSISTANT_DOUBLE_HOP_SWITCH_TIMEOUT,
                                                        &config_value,
                                                        DEFAULT_ASST_DOUBLE_HOP_SWITCH_TIMEOUT,
                                                        connector_gid,
                                                        ZPATH_GID_GET_CUSTOMER_GID(connector_gid),
                                                        (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                        (int64_t)0);

    return (config_value) ? config_value :  DEFAULT_ASST_DOUBLE_HOP_SWITCH_TIMEOUT;
}


int assistant_features_allocator_libevent_out_queue_is_enabled(int64_t connector_gid)
{
    int64_t config_value = 0;

    config_value = zpath_config_override_get_config_int(CONFIG_FEATURE_ASSISTANT_ALLOCATOR_LIBEVENT_OUT_QUEUE_ENABLED,
                                                        &config_value,
                                                        CONFIG_FEATURE_ASSISTANT_ALLOCATOR_LIBEVENT_OUT_QUEUE_ENABLED_DEFAULT,
                                                        connector_gid,
                                                        ZPATH_GID_GET_CUSTOMER_GID(connector_gid),
                                                        (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                        (int64_t)0);
    return config_value?1:0;
}

int64_t assistant_features_allocator_libevent_out_queue_min_len_bytes(int64_t connector_gid)
{
    int64_t config_value = 0;

    config_value = zpath_config_override_get_config_int(CONFIG_FEATURE_ASSISTANT_ALLOCATOR_LIBEVENT_OUT_QUEUE_MIN_LEN_BYTES,
                                                        &config_value,
                                                        CONFIG_FEATURE_ASSISTANT_ALLOCATOR_LIBEVENT_OUT_QUEUE_MIN_LEN_BYTES_DEFAULT,
                                                        connector_gid,
                                                        ZPATH_GID_GET_CUSTOMER_GID(connector_gid),
                                                        (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                        (int64_t)0);
    return config_value;
}

int64_t assistant_features_allocator_libevent_out_queue_max_len_bytes(int64_t connector_gid)
{
    int64_t config_value = 0;

    config_value = zpath_config_override_get_config_int(CONFIG_FEATURE_ASSISTANT_ALLOCATOR_LIBEVENT_OUT_QUEUE_MAX_LEN_BYTES,
                                                        &config_value,
                                                        CONFIG_FEATURE_ASSISTANT_ALLOCATOR_LIBEVENT_OUT_QUEUE_MAX_LEN_BYTES_DEFAULT,
                                                        connector_gid,
                                                        ZPATH_GID_GET_CUSTOMER_GID(connector_gid),
                                                        (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                        (int64_t)0);
    return config_value;
}

int64_t assistant_features_allocator_libevent_out_queue_sys_mem_max_percent(int64_t connector_gid)
{
    int64_t config_value = 0;

    config_value = zpath_config_override_get_config_int(CONFIG_FEATURE_ASSISTANT_ALLOCATOR_LIBEVENT_OUT_QUEUE_SYS_MEM_MAX_PERCENT,
                                                        &config_value,
                                                        CONFIG_FEATURE_ASSISTANT_ALLOCATOR_LIBEVENT_OUT_QUEUE_SYS_MEM_MAX_PERCENT_DEFAULT,
                                                        connector_gid,
                                                        ZPATH_GID_GET_CUSTOMER_GID(connector_gid),
                                                        (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                        (int64_t)0);
    return config_value;
}

int64_t assistant_features_allocator_libevent_out_queue_start_thresh_percent(int64_t connector_gid)
{
    int64_t config_value = 0;

    config_value = zpath_config_override_get_config_int(CONFIG_FEATURE_ASSISTANT_ALLOCATOR_LIBEVENT_OUT_QUEUE_START_THRESH_PERCENT,
                                                        &config_value,
                                                        CONFIG_FEATURE_ASSISTANT_ALLOCATOR_LIBEVENT_OUT_QUEUE_START_THRESH_PERCENT_DEFAULT,
                                                        connector_gid,
                                                        ZPATH_GID_GET_CUSTOMER_GID(connector_gid),
                                                        (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                        (int64_t)0);
    return config_value;
}

int assistant_features_allocator_libevent_out_queue_get_params(int64_t connector_gid,
                                                               int64_t *out_queue_min_len_bytes,
                                                               int64_t *out_queue_max_len_bytes,
                                                               int64_t *sys_mem_max_percent,
                                                               int64_t *start_thresh_percent)
{
    *out_queue_min_len_bytes = assistant_features_allocator_libevent_out_queue_min_len_bytes(connector_gid);
    *out_queue_max_len_bytes = assistant_features_allocator_libevent_out_queue_max_len_bytes(connector_gid);
    *sys_mem_max_percent = assistant_features_allocator_libevent_out_queue_sys_mem_max_percent(connector_gid);
    *start_thresh_percent = assistant_features_allocator_libevent_out_queue_start_thresh_percent(connector_gid);

    return ZPN_RESULT_NO_ERROR;
}
