/*
 * assistant_data.h. Copyright (C) 2019 Zscaler Inc, All Rights Reserved
 *
 * All processing related to Data TX/RX.
 *
 */

#ifndef _ASSISTANT_DATA_H_

#include "zhash/zhash_table.h"

int
assistant_data_init();

struct zpn_assistant_broker_data *
assistant_data_conn_lookup(int64_t broker_id, int64_t g_bfw, int dtls_enabled, int conn_type);

struct zpn_assistant_broker_data *
assistant_data_lookup_or_create(int64_t broker_id, const char *broker_name, int is_pbroker, int dtls_enabled, int allow_all_xport, char *alt_cloud, int alt_domain_enabled, int64_t g_bfw, const char *bfw_name, int conn_type);
void assistant_data_log_status();
int
assistant_data_stats_fill(void*     cookie,
                          int       counter,
                          void*     structure_data);
int
assistant_data_comprehensive_stats_fill(void* structure_data);
void
assistant_data_iterate_brokers_and_do_callback(zhash_table_walk_f cb,
                                               void*              cookie);

struct zpn_assistant_broker_data*
assistant_data_get_locked(int64_t broker_gid, int64_t end_broker_gid);

int
assistant_data_rpc_tx_broker_request_ack(void*                          cookie,
                                         struct zpn_broker_request_ack* ack);

int
assistant_data_rpc_tx_bind(struct zpn_assistant_broker_data* broker,
                           char*                             mtunnel_id,
                           int64_t                           server_rtt_us,
                           int64_t                           g_bfw,
                           int64_t                           bfw_us,
                           int64_t                           g_aps,
                           struct argo_inet                  brk_req_server_inet,
                           struct argo_inet                  server_inet,
                           uint16_t                          server_port_he,
                           struct argo_inet                  asst_inet,
                           uint16_t                          asst_port_he,
                           int64_t                           brk_req_dsp_tx_us,
                           int64_t                           ast_tx_us,
                           int64_t                           brk_req_ast_rx_us,
                           int64_t                           g_app,
                           int64_t                           g_app_grp,
                           int64_t                           g_ast_grp,
                           int64_t                           g_srv_grp,
                           int64_t                           g_dsp,
                           uint64_t                          path_decision,
                           int8_t                            dsp_bypassed,
                           uint8_t                           insp_status,
                           uint64_t                          ssl_err);

void
assistant_data_rpc_tx_state();
int
assistant_data_rpc_tx_state_one_broker(struct zpn_assistant_broker_data*   broker);

/*
 * Temporary workaround for getting alternate cloud name
 * assumes broker name has the following format:
 *       brokerN.loc.cloud_name
 * e.g.  broker1a.pdx2.dev.zpath.net
 * This function extracts the string after first 2 fragments ('.' separated)
 */
const char*
assistant_data_get_sni_suffix_from_broker_name(const char* buf, int buf_len);

int
assistant_data_mconn_stats(struct zpn_assistant_broker_data* broker,
                           struct zpn_mtunnel_data_mconn_stats  *data);

int
assistant_features_is_low_write_watermark_enabled(int64_t connector_gid);

void
assistant_data_disable_adata_conns();

void
assistant_data_enable_adata_conns();

int
assistant_features_batched_mconn_window_updates_enabled(int64_t connector_gid, int64_t *connector_grp_gid, int grp_gid_cnt);
int
assistant_features_syn_app_rtt_enabled(int64_t connector_gid, int64_t *connector_grp_gid, int grp_gid_cnt);
int
assistant_features_pipeline_latency_trace_enabled(int64_t connector_gid, int64_t *connector_grp_gid, int grp_gid_cnt);
#define _ASSISTANT_DATA_H_
#endif // _ASSISTANT_DATA_H_
