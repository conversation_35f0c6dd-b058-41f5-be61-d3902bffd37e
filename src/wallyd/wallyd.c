/*
 * wallyd.c. Copyright (C) 2013 Zscaler, Inc. All Rights Reserved.
 */

#define _GNU_SOURCE

#include <stdio.h>
#include <stdarg.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <pthread.h>
#include <sys/time.h>
#include <ctype.h>
#include <errno.h>
#include <fcntl.h>
#include <math.h>
#include <dirent.h>

#include "avl/avl.h"

#include "fohh/fohh.h"
#include "wally/wally_private.h"
#include "wally/wally.h"
#include "wally/wally_postgres.h"
#include "wally/wally_db.h"
#include "wally/wally_fohh_client.h"
#include "wally/wally_fohh_server.h"
#include "wally/wally_oper.h"
#include "zpath_misc/zpath_misc.h"

#include "zpath_lib/zpath_local.h"
#include "zpath_lib/zpath_debug.h"
#include "zpath_lib/zpath_app.h"

#include "zpath_lib/zpath_entity.h"
#include "zpath_lib/zpath_entity_compiled.h"
#include "zpath_lib/zpath_instance.h"
#include "zpath_lib/zpath_instance_compiled.h"
#include "zpath_lib/zpath_partition.h"
#include "zpath_lib/zpath_partition_compiled.h"
#include "zpath_lib/zpath_instance_group.h"
#include "zpath_lib/zpath_instance_group_compiled.h"
#include "zpath_lib/zpath_instance_group_partition.h"
#include "zpath_lib/zpath_instance_group_partition_compiled.h"
#include "zpath_lib/zpath_instance_partition_override.h"
#include "zpath_lib/zpath_instance_partition_override_compiled.h"
#include "zpath_lib/zpath_customer_partition_override.h"
#include "zpath_lib/zpath_customer_partition_override_compiled.h"
#include "zpath_lib/zpath_ip_entity.h"
#include "zpath_lib/zpath_ip_entity_compiled.h"
#include "zpath_lib/zpath_limit.h"
#include "zpath_lib/zpath_limit_compiled.h"
#include "zpath_lib/zpath_policy.h"
#include "zpath_lib/zpath_policy_compiled.h"
#include "zpath_lib/zpath_rule.h"
#include "zpath_lib/zpath_rule_compiled.h"
#include "zpath_lib/zpath_zurldb.h"
#include "zpath_lib/zpath_zurldb_compiled.h"
#include "zpath_lib/zpath_customer_log_config.h"
#include "zpath_lib/zpath_customer_log_config_compiled.h"
#include "zpath_lib/zpath_constellation.h"
#include "zpath_lib/zpath_constellation_compiled.h"
#include "zpath_lib/zpath_debug_wally.h"
#include "zpath_lib/zpath_domain_lookup.h"
#include "zpath_lib/zpath_domain_lookup_compiled.h"
#include "zpath_lib/zpath_domainlist.h"
#include "zpath_lib/zpath_domainlist_compiled.h"
#include "zpath_lib/zpath_customer_notification.h"
#include "zpath_lib/zpath_customer_notification_compiled.h"
#include "zpath_lib/zpath_category.h"
#include "zpath_lib/zpath_category_compiled.h"
#include "zpath_lib/zpath_cloud.h"
#include "zpath_lib/zpath_cloud_compiled.h"
#include "zpath_lib/zpath_ip_location.h"
#include "zpath_lib/zpath_ip_location_compiled.h"
#include "zpath_lib/zpath_table.h"
#include "zpath_lib/zpath_table_compiled.h"
#include "zpath_lib/zpath_tag_entity.h"
#include "zpath_lib/zpath_tag_entity_compiled.h"
#include "zpath_lib/zpath_customer_logo.h"
#include "zpath_lib/zpath_customer_logo_compiled.h"
#include "zpath_lib/zpath_location.h"
#include "zpath_lib/zpath_location_compiled.h"
#include "zpath_lib/zpath_tls.h"
#include "zpath_lib/zpath_tls_compiled.h"
#include "zpath_lib/zpath_customer.h"
#include "zpath_lib/zpath_customer_compiled.h"
#include "zpath_lib/zpath_log_config.h"
#include "zpath_lib/zpath_log_config_compiled.h"
#include "zpath_lib/zpath_log_store.h"
#include "zpath_lib/zpath_log_store_compiled.h"
#include "zpath_lib/zpath_service.h"
#include "zpath_lib/zpath_service_compiled.h"
#include "zpath_lib/zpath_system.h"
#include "zpath_lib/et_translate_wally.h"
#include "zpath_lib/et_translate_wally_compiled.h"

/* ZPN tables... */
#include "zpn/zpn_app_group_relation.h"
#include "zpn/zpn_app_group_relation_compiled.h"
#include "zpn/zpn_application.h"
#include "zpn/zpn_application_compiled.h"
#include "zpn/zpn_application_group.h"
#include "zpn/zpn_application_group_compiled.h"
#include "zpn/zpn_application_group_application_mapping.h"
#include "zpn/zpn_application_group_application_mapping_compiled.h"
#include "zpn/zpn_application_server.h"
#include "zpn/zpn_application_server_compiled.h"
#include "zpn/zpn_approval.h"
#include "zpn/zpn_approval_compiled.h"
#include "zpn/zpn_approval_mapping.h"
#include "zpn/zpn_approval_mapping_compiled.h"
#include "zpn_ot/zpn_credentials.h"
#include "zpn_ot/zpn_credentials_compiled.h"
#include "exporter/zpn_sra_application.h"
#include "exporter/zpn_sra_application_compiled.h"
#include "exporter/zpn_sra_console.h"
#include "exporter/zpn_sra_console_compiled.h"
#include "exporter/zpn_sra_portal.h"
#include "exporter/zpn_sra_portal_compiled.h"
#include "exporter/zpn_sra_portal_sra_console_mapping.h"
#include "exporter/zpn_sra_portal_sra_console_mapping_compiled.h"
#include "zpn_ot/zpn_credential_rule_mapping.h"
#include "zpn_ot/zpn_credential_rule_mapping_compiled.h"
#include "zpn/zpn_assistant_group.h"
#include "zpn/zpn_assistant_group_compiled.h"
#include "zpn/zpn_assistant_table.h"
#include "zpn/zpn_assistant_table_compiled.h"
#include "zpn/zpn_assistant_version.h"
#include "zpn/zpn_assistant_version_compiled.h"
#include "zpn/zpn_sub_module_upgrade.h"
#include "zpn/zpn_sub_module_upgrade_compiled.h"
#include "zpn/zpn_rule_to_pse_group.h"
#include "zpn/zpn_rule_to_pse_group_compiled.h"
#include "zpn/zpn_rule_to_step_up_auth_level_mapping.h"
#include "zpn/zpn_rule_to_step_up_auth_level_mapping_compiled.h"
#include "zpn/zpn_step_up_auth_level.h"
#include "zpn/zpn_step_up_auth_level_compiled.h"
#include "zpn/zpn_svcp_profile.h"
#include "zpn/zpn_svcp_profile_compiled.h"
#include "zpn/zpn_customer_resiliency_settings.h"
#include "zpn/zpn_customer_resiliency_settings_compiled.h"
#include "zpn/zpn_assistantgroup_assistant_relation.h"
#include "zpn/zpn_assistantgroup_assistant_relation_compiled.h"
#include "zpn/zpn_cbi_mapping.h"
#include "zpn/zpn_cbi_mapping_compiled.h"
#include "zpn/zpn_cbi_profile.h"
#include "zpn/zpn_cbi_profile_compiled.h"
#include "zpn/zpn_customer_version_profile.h"
#include "zpn/zpn_customer_version_profile_compiled.h"
#include "zpn/zpn_server_group.h"
#include "zpn/zpn_server_group_compiled.h"
#include "zpn/zpn_server_group_assistant_group.h"
#include "zpn/zpn_server_group_assistant_group_compiled.h"
#include "zpn/zpn_servergroup_server_relation.h"
#include "zpn/zpn_servergroup_server_relation_compiled.h"
#include "zpn/zpn_signing_cert.h"
#include "zpn/zpn_signing_cert_compiled.h"
#include "zpn/zpn_client_setting.h"
#include "zpn/zpn_client_setting_compiled.h"
#include "zpn/zpn_issuedcert.h"
#include "zpn/zpn_issuedcert_compiled.h"
#include "zpn/zpn_client_table.h"
#include "zpn/zpn_client_table_compiled.h"
#include "zpn/zpn_idp.h"
#include "zpn/zpn_idp_compiled.h"
#include "zpn/zpn_idp_cert.h"
#include "zpn/zpn_idp_cert_compiled.h"
#include "zpn/zpn_rule.h"
#include "zpn/zpn_rule_compiled.h"
#include "zpn/zpn_rule_condition_operand.h"
#include "zpn/zpn_policy_set.h"
#include "zpn/zpn_policy_set_compiled.h"
#include "zpn/zpn_rule_condition_operand_compiled.h"
#include "zpn/zpn_rule_condition_set.h"
#include "zpn/zpn_rule_condition_set_compiled.h"
#include "zpn/zpn_rule_to_assistant_group.h"
#include "zpn/zpn_rule_to_assistant_group_compiled.h"
#include "zpn/zpn_rule_to_server_group.h"
#include "zpn/zpn_rule_to_server_group_compiled.h"
#include "zpn/zpn_saml_attrs.h"
#include "zpn/zpn_saml_attrs_compiled.h"
#include "zpn/zpn_broker_load.h"
#include "zpn/zpn_broker_load_compiled.h"
#include "zpn/zpn_broker_balance_control.h"
#include "zpn/zpn_broker_balance_control_compiled.h"
#include "zpn/zpn_shared_customer_domain.h"
#include "zpn/zpn_shared_customer_domain_compiled.h"
#include "zpn/zpn_version.h"
#include "zpn/zpn_version_compiled.h"
#include "zpn/zpn_version_profile.h"
#include "zpn/zpn_version_profile_compiled.h"
#include "zpn/zpn_application_domain.h"
#include "zpn/zpn_application_domain_compiled.h"
#include "zpath_lib/zpath_et_zone.h"
#include "zpath_lib/zpath_et_zone_compiled.h"
#include "zpath_lib/zpath_et_customer_zone.h"
#include "zpath_lib/zpath_et_customer_zone_compiled.h"
#include "zpath_lib/zpath_et_service_endpoint.h"
#include "zpath_lib/zpath_et_service_endpoint_compiled.h"
#include "zpn/zpn_siem.h"
#include "zpn/zpn_siem_compiled.h"
#include "zpn/zpn_client_less.h"
#include "zpn/zpn_client_less_compiled.h"
#include "zpn/zpn_public_cert.h"
#include "zpn/zpn_public_cert_compiled.h"
#include "zpath_lib/et_geoip_override.h"
#include "zpath_lib/et_geoip_override_compiled.h"
#include "zpn/zpn_private_broker_table.h"
#include "zpn/zpn_private_broker_table_compiled.h"
#include "zpn/zpn_private_broker_version.h"
#include "zpn/zpn_private_broker_version_compiled.h"
#include "zpn/zpn_pbroker_group.h"
#include "zpn/zpn_pbroker_group_compiled.h"
#include "zpn/zpn_pbroker_to_group.h"
#include "zpn/zpn_pbroker_to_group_compiled.h"
#include "zpn/zpn_private_broker_load_table.h"
#include "zpn/zpn_private_broker_load_table_compiled.h"
#include "zpn/zpn_posture_profile.h"
#include "zpn/zpn_posture_profile_compiled.h"
#include "zpn/zpn_trusted_network.h"
#include "zpn/zpn_trusted_network_compiled.h"
#include "zpn/zpn_privatebrokergroup_trustednetwork_mapping.h"
#include "zpn/zpn_privatebrokergroup_trustednetwork_mapping_compiled.h"
#include "zpn/zpn_znf.h"
#include "zpn/zpn_znf_compiled.h"
#include "zpn/zpn_znf_group.h"
#include "zpn/zpn_znf_group_compiled.h"
#include "zpn/zpn_znf_to_group.h"
#include "zpn/zpn_znf_to_group_compiled.h"
#include "zpath_lib/zpath_partition_profile.h"
#include "zpath_lib/zpath_partition_profile_compiled.h"
#include "zpn/zpn_branch_connector.h"
#include "zpn/zpn_branch_connector_compiled.h"
#include "zpn/zpn_location.h"
#include "zpn/zpn_location_compiled.h"
#include "zpn/zpn_privileged_capabilities.h"
#include "zpn/zpn_privileged_capabilities_compiled.h"
#include "zpn/zpn_app_protection_csp_profile.h"
#include "zpn/zpn_app_protection_csp_profile_compiled.h"

#include "zpn/zpn_c2c_client_registration.h"
#include "zpn/zpn_c2c_client_registration_compiled.h"
#include "zpn/zpn_c2c_ip_ranges.h"
#include "zpn/zpn_c2c_ip_ranges_compiled.h"

#include "zpath_lib/zpath_config_override.h"
#include "zpath_lib/zpath_config_override_compiled.h"
#include "zpn/zpn_inspection_application.h"
#include "zpn/zpn_inspection_application_compiled.h"
#include "zpn_waf/zpn_inspection_config_data.h"
#include "zpn_waf/zpn_inspection_config_data_compiled.h"
#include "zpn_waf/zpn_inspection_profile_to_control.h"
#include "zpn_waf/zpn_inspection_profile_to_control_compiled.h"
#include "zpn_waf/zpn_inspection_profile.h"
#include "zpn_waf/zpn_inspection_profile_compiled.h"
#include "zpn_waf/zpn_inspection_custom_control.h"
#include "zpn_waf/zpn_inspection_custom_control_compiled.h"
#include "zpn_waf/zpn_inspection_predef_control.h"
#include "zpn_waf/zpn_inspection_predef_control_compiled.h"
#include "zpn_waf/zpn_inspection_zsdefined_control.h"
#include "zpn_waf/zpn_inspection_zsdefined_control_compiled.h"
#include "zpn_waf/zpn_inspection_prof_to_zsdefined_ctrl.h"
#include "zpn_waf/zpn_inspection_prof_to_zsdefined_ctrl_compiled.h"

#include "zpn/zpn_customer_config.h"
#include "zpn/zpn_customer_config_compiled.h"

#include "zpn/zpn_managed_chrome_extension.h"
#include "zpn/zpn_managed_chrome_extension_compiled.h"
#include "zpn/zpn_managed_browser_profile.h"
#include "zpn/zpn_managed_browser_profile_compiled.h"

#include "zpath_lib/zpath_broker_proxy_sni.h"
#include "zpath_lib/zpath_broker_proxy_sni_compiled.h"
#include "zpn/zpn_ratelimit_policy.h"
#include "zpn/zpn_ratelimit_policy_compiled.h"
#include "admin_probe/zpn_command_probe.h"
#include "admin_probe/np_command_probe.h"
#include "admin_probe/zpn_command_probe_compiled.h"
#include "admin_probe/np_command_probe_compiled.h"
#include "zpn/zpn_cxo_user.h"
#include "zpn/zpn_cxo_user_compiled.h"
#include "zpn/zpn_cxo_user_token.h"
#include "zpn/zpn_cxo_user_token_compiled.h"
#include "zpn/zpn_nanolog_association.h"
#include "zpn/zpn_nanolog_association_compiled.h"
#include "zpn/zpn_privileged_portal_rule.h"
#include "zpn/zpn_privileged_portal_rule_compiled.h"

/* exporter tables */
#include "exporter/exporter_user_portal_cfg_portal.h"
#include "exporter/exporter_user_portal_cfg_portal_compiled.h"
#include "exporter/exporter_user_portal_cfg_aup.h"
#include "exporter/exporter_user_portal_cfg_aup_compiled.h"
#include "exporter/exporter_user_portal_cfg_links.h"
#include "exporter/exporter_user_portal_cfg_links_compiled.h"
#include "exporter/exporter_user_portal_cfg_zapp_links.h"
#include "exporter/exporter_user_portal_cfg_zapp_links_compiled.h"
#include "exporter/exporter_user_portal_cfg_link_mapping.h"
#include "exporter/exporter_user_portal_cfg_link_mapping_compiled.h"

/* machine tables */
#include "zpn/zpn_machine_table.h"
#include "zpn/zpn_machine_table_compiled.h"
#include "zpn/zpn_machine_group.h"
#include "zpn/zpn_machine_group_compiled.h"
#include "zpn/zpn_machine_to_group.h"
#include "zpn/zpn_machine_to_group_compiled.h"

#include "zpath_lib/zpath_et_customer_userdb.h"
#include "zpath_lib/zpath_et_customer_userdb_compiled.h"
#include "zpath_lib/zpath_et_userdb.h"
#include "zpath_lib/zpath_et_userdb_compiled.h"
#include "zpn/zpn_scim_attr_header.h"
#include "zpn/zpn_scim_attr_header_compiled.h"
#include "wallyd/wallyd.h"

/* scim tables */
#include "zpn/zpn_aae_profile_conclusion.h"
#include "zpn/zpn_aae_profile_conclusion_compiled.h"
#include "zpn/zpn_aae_profile_definition.h"
#include "zpn/zpn_aae_profile_definition_compiled.h"
#include "zpn/zpn_scim_user_group.h"
#include "zpn/zpn_scim_user_group_compiled.h"
#include "zpn/zpn_scim_group.h"
#include "zpn/zpn_scim_group_compiled.h"
#include "zpn/zpn_scim_user_attribute.h"
#include "zpn/zpn_scim_user_attribute_compiled.h"
#include "zpn/zpn_scim_user.h"
#include "zpn/zpn_scim_user_compiled.h"
#include "zpn/zpn_user_risk.h"
#include "zpn/zpn_user_risk_compiled.h"
#include "zpn/zpn_workload_tag_group.h"
#include "zpn/zpn_workload_tag_group_compiled.h"

#include "zpn/zpn_scope.h"
#include "zpn/zpn_scope_compiled.h"

#include "zpn/zpn_user_to_event_notification.h"
#include "zpn/zpn_user_to_event_notification_compiled.h"
#include "zpn/zpn_event_notification.h"
#include "zpn/zpn_event_notification_compiled.h"
#include "zpn/zpath_user.h"
#include "zpn/zpath_user_compiled.h"

#include "zpath_lib/zpath_et_region_zone_mapping.h"
#include "zpath_lib/zpath_et_region_zone_mapping_compiled.h"
#include "zpath_lib/zpath_et_userdb_service_endpoint.h"
#include "zpath_lib/zpath_et_userdb_service_endpoint_compiled.h"
#include "zpn/zpn_version_control/zpn_version_control.h"
#include "zpn/zpn_version_control/zpn_version_control_compiled.h"
#include "zpn/zpn_save_point.h"
#include "zpn/zpn_save_point_compiled.h"

#include "zpath_misc/zpath_version.h"

#include "zpath_lib/zpath_config_override_keys.h"
#include "zpath_lib/zpath_config_override_desc.h"
#include "zpath_lib/sanitizer_config.h"

#include "argo/argo_compiled.h"
#include "zpn/zpn_system.h"
#include "zpn/zpn_system_compiled.h"
#include "zpath_lib/zpath_system_stats.h"
#include "zpath_lib/zpath_system_stats_compiled.h"

/*site controller headers*/
#include "zpn/zpn_ddil_config.h"
#include "zpn/zpn_ddil_config_compiled.h"
#include "zpn/zpn_site.h"
#include "zpn/zpn_site_compiled.h"
#include "zpn/zpn_sitec_table.h"
#include "zpn/zpn_sitec_table_compiled.h"
#include "zpn/zpn_sitec_group.h"
#include "zpn/zpn_sitec_group_compiled.h"
#include "zpn/zpn_sitec_to_group.h"
#include "zpn/zpn_sitec_to_group_compiled.h"
#include "zpn/zpn_sitec_version.h"
#include "zpn/zpn_sitec_version_compiled.h"
#include "zpn/zpn_restricted_entity.h"
#include "zpn/zpn_restricted_entity_compiled.h"
#include "zpn/zpn_server_group_to_location.h"
#include "zpn/zpn_server_group_to_location_compiled.h"
#include "zpn/zpn_server_group_to_location_group.h"
#include "zpn/zpn_server_group_to_location_group_compiled.h"
#include "zpn/zpn_extranet_resource.h"
#include "zpn/zpn_extranet_resource_compiled.h"
#include "zpn/zpn_location_group.h"
#include "zpn/zpn_location_group_compiled.h"
#include "zpn/zpn_location_group_to_location.h"
#include "zpn/zpn_location_group_to_location_compiled.h"
#include "zpn/zpn_rule_to_location.h"
#include "zpn/zpn_rule_to_location_compiled.h"
#include "zpn/zpn_rule_to_location_group.h"
#include "zpn/zpn_rule_to_location_group_compiled.h"
#include "zpn/zpn_firedrill_site.h"
#include "zpn/zpn_firedrill_site_compiled.h"


/* NP tables */
#include "np_lib/np_client_subnets.h"
#include "np_lib/np_client_subnets_compiled.h"
#include "np_lib/np_clients.h"
#include "np_lib/np_clients_compiled.h"
#include "np_lib/np_connector_groups.h"
#include "np_lib/np_connector_groups_compiled.h"
#include "np_lib/np_connectors.h"
#include "np_lib/np_connectors_compiled.h"
#include "np_lib/np_dns_ns_records.h"
#include "np_lib/np_dns_ns_records_compiled.h"
#include "np_lib/np_gateways.h"
#include "np_lib/np_gateways_compiled.h"
#include "np_lib/np_tenant_gateways.h"
#include "np_lib/np_tenant_gateways_compiled.h"
#include "np_lib/np_lan_subnets.h"
#include "np_lib/np_lan_subnets_compiled.h"
#include "np_lib/np_connector_groups_lan_subnets_mapping.h"
#include "np_lib/np_connector_groups_lan_subnets_mapping_compiled.h"
#include "np_lib/np_bgp_connectors_config.h"
#include "np_lib/np_bgp_connectors_config_compiled.h"
#include "np_lib/np_bgp_gateways_config.h"
#include "np_lib/np_bgp_gateways_config_compiled.h"
#include "np_lib/np_bgp_connector_session_config.h"
#include "np_lib/np_bgp_connector_session_config_compiled.h"
#include "np_lib/np_bgp_gateway_session_config.h"
#include "np_lib/np_bgp_gateway_session_config_compiled.h"

/* Region Restriction tables */
#include "zpn/et_blocked_country.h"
#include "zpn/et_blocked_country_compiled.h"
#include "zpn/et_exempt_customer.h"
#include "zpn/et_exempt_customer_compiled.h"
#include "zpn/et_exempt_sni.h"
#include "zpn/et_exempt_sni_compiled.h"
#include "wally/wally_table_queue.h"

// Do not optimize entire function.
#if defined(__clang__)
#define ZDO_NOT_OPTIMIZE __attribute__((optnone))
#elif defined(__GNUC__)
#define ZDO_NOT_OPTIMIZE __attribute__((optimize("O0")))
#else
#define ZDO_NOT_OPTIMIZE
#endif

#define WALLY_CLIENT_CONN       "client-connect"
#define WALLY_CLIENT_DISCONN    "client-disconnect"
#define WALLY_ORIGIN_CONN       "origin-connect"
#define WALLY_ORIGIN_DISCONN    "origin-disconnect"
#define WALLY_ACTIVE            "active"
#define WALLY_OFFLINE           "offline"

#define TABLE_NAME_MAX_SIZE 1024
#define WALLYD_DB_QUERY_MIN_BATCH_SIZE 1000L
#define WALLYD_DB_QUERY_MAX_BATCH_SIZE 180000L
#define WALLY_MAX_PORT_NUMBER  65535
#define WALLYD_MIN_POLL_RATE_TIMEOUT_MIN 10
#define WALLYD_MAX_POLL_RATE_TIMEOUT_MIN 60
#define WALLYD_DEFAULT_POLL_RATE_TIMEOUT_MIN 30

#define WALLY_MIN_TABLE_THREADS 2
#define WALLY_MAX_TABLE_THREADS 256

int config_daemon = 0;
int config_endpoint = 0;
int config_test = 0;
int config_fully_loaded = 0;
int fohh_threads = 8;
int config_dump_schema = 0;
char *config_read_js = "/zpath/etc/wallyd";
static bool disable_operational_mode = false; /* Operational mode FSM is disabled */

int do_cleanup = 0;
extern int32_t responses_waiting;
extern int32_t do_wts_done;

struct wallyd_termination_context g_wallyd_termination_ctx = {zpath_tc_invalid, -1};

#define FULLY_LOADED (config_fully_loaded | ZPATH_LOCAL_WALLYD_FULLY_LOADED)

const char *dbhost = "localhost";
const char *local_file = NULL;
int itasca_logs_port_he = 0;

#define WALLY_DEF_MIN_DRAIN_DELAY_MID  1    /* Min delay to be given after P1 client drain */
#define WALLY_DEF_MAX_DRAIN_DELAY_MID  300  /* Max delay to be given after P1 client drain */
#define WALLY_DEF_DRAIN_DELAY_MID      60  /* This is applicable only in two-phase draining.
                                              Default drain delay to be given after P1 clients drain */
#define WALLY_DEF_MIN_DRAIN_DELAY      1   /* Min delay to be given for each drain iteration */
#define WALLY_DEF_MAX_DRAIN_DELAY      60  /* Max delay to be given for each drain iteration */
#define WALLY_DEF_DRAIN_DELAY           5  /* This is applicable only in two-phase draining.
                                              Default drain delay to be given for each drain iteration */
uint64_t wallyd_drain_delay_mid = WALLY_DEF_DRAIN_DELAY_MID;
uint64_t wallyd_drain_delay     = WALLY_DEF_DRAIN_DELAY;
int wallyd_conns_per_db = 4;
extern int wally_db_init_read_nolock;

extern int zpath_debug_fohh_server_reset(struct zpath_debug_state *request_state,
                                  const char **query_values,
                                  int query_value_count,
                                  void *cookie);

struct wally *wallyd = NULL;
struct wally_origin *wallyd_slave_db = NULL;
struct wally_origin *wallyd_remote_db = NULL;
struct wally_fohh_server *wally_fohh_server_handle[WALLY_MAX_SERVER] = {NULL, NULL};
static uint32_t wally_server_max_index = 0;

struct zpn_system_fd_stats zpn_wally_fd_stats;
struct zpn_system_sock_stats zpn_wally_sock_stats;
struct zpn_system_cpu_stats zpn_wally_cpu_stats;
struct zpn_system_disk_stats zpn_wally_disk_stats;
struct zpn_system_memory_stats zpn_wally_memory_stats;

struct argo_structure_description *zpn_wally_disk_stats_description;
struct argo_structure_description *zpn_wally_cpu_stats_description;
struct argo_structure_description *zpn_wally_fd_stats_description;
struct argo_structure_description *zpn_wally_sock_stats_description;
struct argo_structure_description *zpn_wally_memory_stats_description;
struct argo_structure_description *zpn_wally_network_stats_description;
struct argo_structure_description *zpn_wally_assistant_stats_description;
struct argo_structure_description *zpn_wally_private_broker_stats_description;
struct argo_structure_description *zpn_wally_sitec_stats_description;
struct argo_structure_description *wallyd_global_stats_description;
struct argo_structure_description *wallyd_drain_stats_description;
struct argo_log_registered_structure *wallyd_global_stats_structure;

uint32_t wally_available_cpus = 0;
static int64_t start_time = 0;
static int64_t wallyd_bootup_time = -1;


struct wallyd_global_stats {        /* _ARGO: object_definition */
    int wally_app_state;                     /* _ARGO: integer */
    int64_t wallyd_bootup_time_s;            /* _ARGO: integer */
    int64_t wallyd_bootup_pending_tables;    /* _ARGO: integer */
    int wally_postgres_db_query_batch_size_cfg;  /* _ARGO: integer */
};

LIST_HEAD(wallyd_drain_hist_head, wallyd_drain_hist);

struct wallyd_drain_hist {
    struct timeval cmd_time;    /* Command started time */
    uint64_t clients_drained;   /* Number of clients drained*/
    uint64_t drain_failed;      /* Clients drain failed count*/
    uint64_t drain_requested;   /* Total number of clients requested for drain*/
    char domain[WALLY_DOMAIN_LEN]; /* User provided domain for drain */
    char port[WALLY_PORT_LEN];  /* User provided port for drain */
    uint32_t percentage;        /* Percentage for clients to be drained */
    uint32_t rate;              /* Percentage for client drain per second */
    uint32_t drain_rate;        /* Number of client drain per second */
    uint32_t force;             /* Force drain. Don't skip wally-leaf*/
    uint64_t drain_time_us;     /* Time taken to drain all the clients */
    uint32_t last_drain_server; /* Last drained server */
    bool oper_drain;            /* Draining due to operational change */
    uint32_t time_to_complete;   /* Time to complete the drain */
    LIST_ENTRY(wallyd_drain_hist) hist;
};

struct wallyd_drain_stats {        /* _ARGO: object_definition */
    uint64_t clients_drained_lifetime;  /* _ARGO: integer */
    uint64_t drain_cmd_count_lifetime;  /* _ARGO: integer */
    uint64_t drain_time_taken_us;       /* _ARGO: integer */
    uint64_t drain_epoch_us;            /* _ARGO: integer */
    uint64_t clients_drained;           /* _ARGO: integer */
    uint64_t drain_failed;              /* _ARGO: integer */
};

struct wallyd_drain_details {
    struct wallyd_drain_stats drain_stats;
    struct wallyd_drain_hist_head drain_hist;
    bool two_phase_drain;      /* Used only in Operational-mode triggerred drain */
    enum wally_drain_phase drain_phase;  /* Current drain phase, used only in Operational-mode triggerred drain */
    enum wally_drain_state drain_state;  /* Current drain state, used only in Operational-mode triggerred drain */
    uint8_t hist_count;  /* Max 5 */
};

static FILE *fp_drain = NULL; /* Drain log*/
static FILE *fp_client = NULL; /* Client list details */

struct wallyd_drain_details drain_details;
/* Running drain configuration */
struct wallyd_drain_hist drain_config;
/* Current drain statu, parallel drain is not supported  */
extern volatile enum wally_drain_status drain_status;

struct wallyd_global_stats wallyd_stats;

#include "wallyd/wallyd_compiled_c.h"



int wallyd_zpath_table_register_cb(struct wally* wallyd,struct wally_table *table){
        int res = 0;
        if (wallyd->origins_count > 1) {
            res = zpath_table_register(wallyd,
                                       table->name,
                                       zpath_table_default_callback,
                                       table);
            if (res != ZPATH_RESULT_NO_ERROR) {
                WALLYD_LOG(AL_ERROR, "Could not register table:%s for zpath_table_register error:%s",
                          table->name, zpath_result_string(res));
                return res;
            }
        }
        return ZPATH_RESULT_NO_ERROR;
}

static struct zpath_config_override_desc wally_override_desciptions[] = {
    {
        .key                = WALLY_FEATURE_DROP_INCOMPATIBLE_VERSION,
        .desc               = "feature flag to disconnect a client if the client is older than a certain version",
        .details            = "1: feature is enabled, disconnect the old clients.\n"
                              "0: feature is disabled, do not disconnect the old clients.\n"
                              "default: 0 (Disabled)",
        .val_type           = config_type_int,
        .component_types    = config_component_wally,
        .target_gid_types   = config_target_gid_type_global,
        .int_range_lo       = DROP_INCOMPATIBLE_VERSION_MIN,
        .int_range_hi       = DROP_INCOMPATIBLE_VERSION_MAX,
        .int_default        = DEFAULT_DROP_INCOMPATIBLE_VERSION,       /* Disabled by default */
        .value_traits       = config_value_traits_feature_enablement,
        .feature_group      = FEATURE_GROUP_WALLY
    },
    {
        .key                = WALLY_FEATURE_LEAF_RECONNECT_TABLE_REG_PRIORITY,
        .desc               = "feature flag to enable registration of table interests while leaf wally reconnects to origin to ensure faster convergence",
        .details            = "1: feature is enabled, leaf wally prioritizes table interest regns.\n"
                              "0: feature is disabled, leaf wally handles table interests with no special prioritisation\n"
                              "default: 0 (Disabled)",
        .val_type           = config_type_int,
        .component_types    = config_component_wally,
        .target_gid_types   = config_target_gid_type_global,
        .int_range_lo       = LEAF_WALLY_RECONN_TABLE_REGN_PRIORITY_MIN,
        .int_range_hi       = LEAF_WALLY_RECONN_TABLE_REGN_PRIORITY_MAX,
        .int_default        = LEAF_WALLY_RECONN_TABLE_REGN_PRIORITY_DEFAULT,       /* Disabled by default */
        .value_traits       = config_value_traits_feature_enablement,
        .feature_group      = FEATURE_GROUP_LEAF_WALLY_RECONNECT_TABLE_REG_PRIORITY
    },
    {
        .key                = WALLY_FEATURE_DONT_DUMP,
        .desc               = "feature flag to enable dont dump",
        .details            = "Don't dump for core memory can be enabled for wally\n"
                              "0: disable\n"
                              "1: enable\n"
                              "default: 0/disabled",
        .val_type           = config_type_int,
        .component_types    = config_component_wally,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_inst_grp | config_target_gid_type_global,
        .int_range_lo       = FEATURE_DONT_DUMP_DISABLED,
        .int_range_hi       = FEATURE_DONT_DUMP_ENABLED,
        .int_default        = FEATURE_DONT_DUMP_DEFAULT,       /* Disabled by default */
        .value_traits       = config_value_traits_feature_enablement,
        .feature_group      = FEATURE_GROUP_DONT_DUMP
    }
};

void zpn_register_config_override_wally_override_desciptions(void) {
    int i;
    int res = 0;
    for (i = 0; i < (sizeof(wally_override_desciptions) / sizeof(struct zpath_config_override_desc)); i++) {
        res = zpath_config_override_desc_register(&wally_override_desciptions[i]);
        if (res) ZPATH_LOG(AL_ERROR, "Unable to register wally_override_desciptions[%d] key: %s, err: %s",
                           i, wally_override_desciptions[i].key, zpath_result_string(res));
    }
}

struct argo_structure_description *wt_description = NULL;
struct argo_structure_description *argo_field_description_description = NULL;
enum load_tables_state {no_tables, static_tables, non_static_tables, all_tables, load_tables_end};
char load_tables_state_str [load_tables_end][MAX_TABLE_STATE_NAME_LEN] = { "NO Tables ", "STATIC Tables", "NON-STATIC Tables", "ALL Tables" };

enum load_tables_state load_tables = all_tables;
struct wt wts[] =
    {
     { 1, 0, 0, NULL, ZPATH_INSTANCE_HELPER },
     { 0, 0, 0, NULL, ZPATH_PARTITION_HELPER },
     { 0, 0, 0, NULL, ZPATH_INSTANCE_GROUP_HELPER },
     { 0, 0, 0, NULL, ZPATH_INSTANCE_GROUP_PARTITION_HELPER },
     { 1, 0, 0, NULL, ZPATH_CLOUD_HELPER },
     { 0, 0, 0, NULL, ZPATH_IP_ENTITY_HELPER },
     { 0, 0, 0, NULL, ZPATH_ENTITY_HELPER },
     { 0, 0, 0, NULL, ZPATH_LIMIT_HELPER },
     { 0, 0, 0, NULL, ZPATH_POLICY_HELPER },
     { 0, 0, 0, NULL, ZPATH_RULE_HELPER },
     { 0, 0, 0, NULL, ZPATH_CUSTOMER_LOG_CONFIG_HELPER },
     { 0, 0, 0, NULL, ZPATH_CONSTELLATION_HELPER },
     { 0, 0, 0, NULL, ZPATH_CONSTELLATION_INSTANCE_HELPER },
     { 0, 0, 0, NULL, ZPATH_CUSTOMER_TO_CONSTELLATION_HELPER },
     { 0, 0, 0, NULL, ZPATH_DOMAIN_LOOKUP_HELPER },
     { 0, 0, 0, NULL, ZPATH_DOMAINLIST_HELPER},
     { 0, 0, 0, NULL, ZPATH_USER_HELPER},
     { 0, 0, 0, NULL, ZURLDB_ACTIONS_HELPER },
     { 0, 0, 0, NULL, ZPATH_CUSTOMER_NOTIFICATION_HELPER },
     { 0, 0, 0, NULL, ZPATH_CATEGORY_HELPER },
     { 0, 0, 0, NULL, ZPATH_IP_LOCATION_HELPER },
     { 1, 0, 0, NULL, ZPATH_TABLE_HELPER },
     { 0, 0, 0, NULL, ZPATH_ZURLDB_DOMAIN_HELPER },
     { 0, 0, 0, NULL, ZPATH_TAG_ENTITY_HELPER },
     { 0, 0, 0, NULL, ZPATH_CUSTOMER_LOGO_HELPER },
     { 0, 0, 0, NULL, ZPATH_LOCATION_HELPER },
     { 0, 1, 0, NULL, ZPATH_TLS_HELPER },
     { 0, 0, 0, NULL, ZPATH_CUSTOMER_HELPER },
     { 0, 0, 0, NULL, ZPATH_LOG_CONFIG_HELPER },
     { 0, 0, 0, NULL, ZPATH_LOG_STORE_HELPER },
     { 0, 0, 0, NULL, ZPATH_SERVICE_HELPER },
     { 0, 0, 0, NULL, ZPN_APP_GROUP_RELATION_HELPER },
     { 0, 0, 0, NULL, ZPN_APPLICATION_HELPER },
     { 0, 0, 0, NULL, ZPN_APPLICATION_GROUP_HELPER },
     { 0, 0, 0, NULL, ZPN_APPLICATION_GROUP_APPLICATION_MAPPING_HELPER },
     { 0, 0, 0, NULL, ZPN_APP_SERVER_HELPER },
     { 0, 0, 0, NULL, ZPN_APPROVAL_HELPER },
     { 0, 0, 0, NULL, ZPN_APPROVAL_MAPPING_HELPER },
     { 0, 0, 0, NULL, ZPN_CREDENTIALS_HELPER },
     { 0, 0, 0, NULL, ZPN_SRA_APPLICATION_HELPER },
     { 0, 0, 0, NULL, ZPN_SRA_CONSOLE_HELPER },
     { 0, 0, 0, NULL, ZPN_SRA_PORTAL_HELPER },
     { 0, 0, 0, NULL, ZPN_SRA_PORTAL_SRA_CONSOLE_MAPPING_HELPER },
     { 0, 0, 0, NULL, ZPN_CREDENTIAL_RULE_MAPPING_HELPER },
     { 0, 0, 0, NULL, ZPN_ASSISTANT_HELPER },
     { 0, 0, 0, NULL, ZPN_ASSISTANT_GROUP_HELPER },
     { 0, 0, 0, NULL, ZPN_ASSISTANT_VERSION_HELPER },
     { 0, 0, 0, NULL, ZPN_RULE_TO_PSE_GROUP_HELPER },
     { 0, 0, 0, NULL, ZPN_RULE_TO_STEP_UP_AUTH_LEVEL_MAPPING_HELPER },
     { 0, 0, 0, NULL, ZPN_STEP_UP_AUTH_LEVEL_HELPER },
     { 0, 0, 0, NULL, ZPN_SVCP_PROFILE_HELPER },
     { 0, 0, 0, NULL, ZPN_ASSISTANTGROUP_ASSISTANT_RELATION_HELPER },
     { 0, 0, 0, NULL, ZPN_CBI_MAPPING_HELPER },
     { 0, 0, 0, NULL, ZPN_CBI_PROFILE_HELPER },
     { 0, 0, 0, NULL, ZPN_COMMAND_PROBE_HELPER },
	 { 0, 0, 0, NULL, ZPN_CUSTOMER_VERSION_PROFILE_HELPER },
     { 0, 0, 0, NULL, ZPN_EVENT_NOTIFICATION_HELPER },
     { 0, 0, 0, NULL, ZPN_SERVER_GROUP_HELPER },
     { 0, 0, 0, NULL, ZPN_SERVER_GROUP_ASSISTANT_GROUP_HELPER },
     { 0, 0, 0, NULL, ZPN_SERVERGROUP_SERVER_RELATION_HELPER },
     { 0, 0, 0, NULL, ZPN_SIGNING_CERT_HELPER },
     { 0, 0, 0, NULL, ZPN_CLIENT_SETTING_HELPER },
     { 0, 0, 0, NULL, ZPN_ISSUEDCERT_HELPER },
     { 0, 0, 0, NULL, ZPN_IDP_HELPER },
     { 0, 0, 0, NULL, ZPN_IDP_CERT_HELPER },
     { 0, 0, 0, NULL, ZPN_RULE_HELPER },
     { 0, 0, 0, NULL, ZPN_RULE_CONDITION_OPERAND_HELPER },
     { 0, 0, 0, NULL, ZPN_RULE_CONDITION_SET_HELPER },
     { 0, 0, 0, NULL, ZPN_RULE_TO_SERVER_GROUP_HELPER},
     { 0, 0, 0, NULL, ZPN_RULE_TO_ASSISTANT_GROUP_HELPER},
     { 0, 0, 0, NULL, ZPN_SAML_ATTRS_HELPER },
     { 0, 1, 0, NULL, ZPN_BROKER_LOAD_HELPER },
     { 0, 1, 0, NULL, ZPN_BROKER_BALANCE_CONTROL_HELPER },
     { 0, 0, 0, NULL, ZPN_POLICY_SET_HELPER },
     { 0, 0, 0, NULL, ZPN_SHARED_CUSTOMER_DOMAIN_HELPER },
     { 0, 0, 0, NULL, ZPN_VERSION_HELPER },
     { 0, 0, 0, NULL, ZPN_VERSION_PROFILE_HELPER },
     { 0, 0, 0, NULL, ZPN_APPLICATION_DOMAIN_HELPER },
     { 0, 0, 0, NULL, ET_ZONE_HELPER },
     { 0, 0, 0, NULL, ET_CUSTOMER_ZONE_HELPER },
     { 0, 0, 0, NULL, ET_SERVICE_ENDPOINT_HELPER },
     { 0, 0, 0, NULL, ZPN_SIEM_HELPER },
     { 0, 0, 0, NULL, ZPN_CLIENT_LESS_HELPER },
     { 0, 0, 0, NULL, ZPN_PUBLIC_CERT_HELPER },
     { 0, 0, 0, NULL, ET_TRANSLATE_HELPER },
     { 0, 0, 0, NULL, ET_TRANSLATE_CODE_HELPER },
     { 0, 0, 0, NULL, ET_GEOIP_OVERRIDE_HELPER },
     { 0, 0, 0, NULL, ZPN_USER_PORTAL_HELPER },
     { 0, 0, 0, NULL, ZPN_USER_PORTAL_AUP_HELPER },
     { 0, 0, 0, NULL, ZPN_USER_PORTAL_LINKS_HELPER },
     { 0, 0, 0, NULL, ZPN_USER_PORTAL_ZAPP_LINKS_HELPER },
     { 0, 0, 0, NULL, ZPN_USER_PORTAL_USER_PORTAL_LINK_MAPPING_HELPER },
     { 0, 0, 0, NULL, ZPN_USER_TO_EVENT_NOTIFICATION_HELPER },
     { 0, 0, 0, "zpn_posture_profile", ZPN_POSTURE_PROFILE_DB_HELPER },
     { 0, 0, 0, NULL, ZPN_TRUSTED_NETWORK_HELPER },
     { 0, 0, 0, NULL, ET_CUSTOMER_USERDB_HELPER },
     { 0, 0, 0, NULL, ET_USERDB_HELPER },
     { 0, 0, 0, NULL, ZPN_SCIM_ATTR_HEADER_HELPER },
     { 0, 0, 0, NULL, ZPN_PRIVATE_BROKER_VERSION_HELPER },
     { 0, 0, 0, NULL, ZPN_SUB_MODULE_UPGRADE_HELPER },
     { 0, 0, 0, NULL, ZPN_PRIVATE_BROKER_HELPER },
     { 0, 0, 0, NULL, ZPN_PRIVATE_BROKER_GROUP_HELPER },
     { 0, 0, 0, NULL, ZPN_PRIVATE_BROKER_TO_GROUP_HELPER },
     { 0, 1, 0, NULL, ZPN_PRIVATE_BROKER_LOAD_HELPER },
     { 0, 0, 0, NULL, ZPN_PRIVATEBROKERGROUP_TRUSTEDNETWORK_MAPPING_HELPER },
     { 0, 0, 0, NULL, ZPN_ZNF_HELPER},
     { 0, 0, 0, NULL, ZPN_ZNF_TO_GROUP_HELPER},
     { 0, 0, 0, NULL, ZPN_ZNF_GROUP_HELPER},
     { 0, 0, 0, NULL, ZPN_BRANCH_CONNECTOR_HELPER},
     { 0, 0, 0, NULL, ZPN_BRANCH_CONNECTOR_TO_GROUP_HELPER},
     { 0, 0, 0, NULL, ZPN_BRANCH_CONNECTOR_GROUP_HELPER},
     { 0, 0, 0, NULL, ZPN_LOCATION_HELPER},
     { 0, 0, 0, NULL, ZPN_WORKLOAD_TAG_GROUP_HELPER},
     { 0, 0, 0, NULL, ZPN_APP_PROTECTION_CSP_PROFILE_HELPER},
     { 0, 0, 0, NULL, ZPN_MACHINE_HELPER },
     { 0, 0, 0, NULL, ZPN_MACHINE_GROUP_HELPER },
     { 0, 0, 0, NULL, ZPN_MACHINE_TO_GROUP_HELPER },
     { 0, 0, 0, NULL, ZPATH_CONFIG_OVERRIDE_HELPER },
     { 0, 0, 0, NULL, ZPN_C2C_CLIENT_REGISTRATION_HELPER },
     { 0, 0, 0, NULL, ZPN_C2C_IP_RANGES_HELPER },
     { 0, 0, 0, NULL, ZPN_CUSTOMER_CONFIG_HELPER },
     { 0, 0, 0, NULL, ZPN_BROKER_PROXY_SNI_HELPER },
     { 0, 0, 0, NULL, ZPN_RATELIMIT_POLICY_HELPER },
     { 0, 0, 0, NULL, ZPN_CXO_USER_HELPER },
     { 0, 0, 0, NULL, ZPN_CXO_USER_TOKEN_HELPER },
     { 0, 0, 0, NULL, ZPN_INSPECTION_APPLICATION_HELPER },
     { 0, 0, 0, NULL, ZPN_INSPECTION_CONFIG_DATA_HELPER },
     { 0, 0, 0, NULL, ZPN_INSPECTION_PROFILE_TO_CONTROL_HELPER },
     { 0, 0, 0, NULL, ZPN_INSPECTION_PROFILE_HELPER },
     { 0, 0, 0, NULL, ZPN_INSPECTION_PROF_TO_ZSDEFINED_CTRL_HELPER },
     { 0, 0, 0, NULL, ZPN_INSPECTION_ZSDEFINED_CONTROL_HELPER },
     { 0, 0, 0, NULL, ZPN_INSPECTION_CUSTOM_CONTROL_HELPER },
     { 0, 0, 0, NULL, ZPN_INSPECTION_PREDEF_CONTROL_HELPER },
     { 0, 0, 0, NULL, ZPN_PRIVILEGED_CAPABILITIES_HELPER },
     { 0, 0, 0, NULL, ZPN_CUSTOMER_RESILIENCY_SETTINGS_HELPER },
     { 0, 0, 0, NULL, ZPN_NANOLOG_ASSOCIATION_HELPER},
     { 0, 0, 0, NULL, ET_REGION_ZONE_MAPPING_HELPER },
     { 0, 0, 0, NULL, ET_USERDB_SERVICE_ENDPOINT_HELPER },
     { 0, 0, 0, NULL, ZPN_SAVE_POINT_HELPER },
     { 0, 0, 0, NULL, ZPN_RESTRICTED_ENTITY_HELPER },
     // EXTRANET
     { 0, 0, 0, NULL, ZPN_SERVER_GROUP_TO_LOCATION_HELPER },
     { 0, 0, 0, NULL, ZPN_SERVER_GROUP_TO_LOCATION_GROUP_HELPER },
     { 0, 0, 0, NULL, ZPN_EXTRANET_RESOURCE_HELPER },
     { 0, 0, 0, NULL, ZPN_LOCATION_GROUP_HELPER },
     { 0, 0, 0, NULL, ZPN_LOCATION_GROUP_TO_LOCATION_HELPER },
     { 0, 0, 0, NULL, ZPN_RULE_TO_LOCATION_HELPER },
     { 0, 0, 0, NULL, ZPN_RULE_TO_LOCATION_GROUP_HELPER },
     { 0, 0, 0, NULL, ZPN_VERSION_CONTROL_HELPER },
     { 0, 0, 0, NULL, ZPN_PRIVILEGED_PORTAL_RULE_HELPER },

     //SCIM Tables
     { 0, 0, 0, NULL, ZPN_SCIM_USER_GROUP_HELPER },
     { 0, 0, 0, NULL, ZPN_SCIM_GROUP_HELPER },
     { 0, 0, 0, NULL, ZPN_SCIM_USER_ATTRIBUTE_HELPER },
     { 0, 0, 0, NULL, ZPN_SCIM_USER_HELPER },
     { 0, 0, 0, NULL, ZPN_USER_RISK_HELPER },
     { 0, 0, 0, NULL, ZPN_AAE_PROFILE_CONCLUSION_HELPER },
     //Below table is used by logging service and NOT by itasca
     { 0, 0, 0, NULL, ZPN_AAE_PROFILE_DEFINITION_HELPER },

     /* instance partition */
    { 0, 0, 0, NULL, ZPATH_PARTITION_PROFILE_HELPER },
    { 0, 0, 0, NULL, ZPATH_PARTITION_PROFILE_INSTANCE_HELPER },
    { 0, 0, 0, NULL, ZPATH_PARTITION_PROFILE_PARTITION_HELPER },

     /* zpath_instance_partition_override */
     { 0, 0, 0, NULL, ZPATH_INSTANCE_PARTITION_OVERRIDE_HELPER },

     /* zpath_customer_partition_override */
     { 0, 0, 0, NULL, ZPATH_CUSTOMER_PARTITION_OVERRIDE_HELPER },

     { 0, 0, 0, NULL, ZPN_SCOPE_HELPER },

     /* site controller tables */
     { 0, 0, 0, NULL, ZPN_DDIL_CONFIG_HELPER},
     { 0, 0, 0, NULL, ZPN_SITE_CONTROLLER_VERSION_HELPER },
     { 0, 0, 0, NULL, ZPN_SITE_CONTROLLER_HELPER },
     { 0, 0, 0, NULL, ZPN_SITE_CONTROLLER_GROUP_HELPER },
     { 0, 0, 0, NULL, ZPN_SITE_CONTROLLER_TO_GROUP_HELPER },
     { 0, 0, 0, NULL, ZPN_SITE_HELPER },

     /* Static Tables */
     { 0, 0, 1, NULL, ZPN_CLIENT_HELPER },

     /* NP Tables */
     { 0, 0, 0, NULL, NP_CLIENT_SUBNETS_HELPER },
     { 0, 0, 0, NULL, NP_CLIENTS_HELPER },
     { 0, 0, 0, NULL, NP_CONNECTOR_GROUPS_HELPER },
     { 0, 0, 0, NULL, NP_CONNECTORS_HELPER },
     { 0, 0, 0, NULL, NP_DNS_NS_RECORDS_HELPER },
     { 0, 0, 0, NULL, NP_GATEWAYS_HELPER },
     { 0, 0, 0, NULL, NP_TENANT_GATEWAYS_HELPER },
     { 0, 0, 0, NULL, NP_LAN_SUBNETS_HELPER },
     { 0, 0, 0, NULL, NP_COMMAND_PROBE_HELPER },
     { 0, 0, 0, NULL, NP_CONNECTOR_GROUPS_LAN_SUBNETS_MAPPING_HELPER },
     { 0, 0, 0, NULL, NP_BGP_CONNECTORS_CONFIG_HELPER },
     { 0, 0, 0, NULL, NP_BGP_GATEWAYS_CONFIG_HELPER },
     { 0, 0, 0, NULL, NP_BGP_CONNECTOR_SESSION_CONFIG_HELPER },
     { 0, 0, 0, NULL, NP_BGP_GATEWAY_SESSION_CONFIG_HELPER },

     /* Region Restriction Tables */
     { 0, 0, 0, NULL, ET_BLOCKED_COUNTRY_HELPER },
     { 0, 0, 0, NULL, ET_EXEMPT_CUSTOMER_HELPER },
     { 0, 0, 0, NULL, ET_EXEMPT_SNI_HELPER },

    /* Managed browser profile */
     { 0, 0, 0, NULL, ZPN_MANAGED_BROWSER_PROFILE_HELPER },
     { 0, 0, 0, NULL, ZPN_MANAGED_CHROME_EXTENSION_HELPER },

     /* ZPN Firedrill site table */
     { 0, 0, 0, NULL, ZPN_FIREDRILL_SITE_HELPER}

     /* ************************** */
};

void dump_schema(struct wt *wt)
{
    FILE *fp;
    char filename[1000];
    char str[10000];
    int i;

    snprintf(filename, sizeof(filename), "%s.js", wt->table_name);

    fp = fopen(filename, "w");
    if (!fp) return;

    /* Write header, which is a wt */
    argo_structure_dump(wt_description, wt, str, sizeof(str), NULL, 0);
    fprintf(fp, "%s\n", str);

    /* Write all the field descriptions */
    for (i = 0; i < wt->struct_field_count; i++) {
        argo_structure_dump(argo_field_description_description, &(wt->desc[i]), str, sizeof(str), NULL, 0);
        fprintf(fp, "%s\n", str);
    }
    fclose(fp);
    return;
}

/*
 * Perform put of specified row to TLS field
 *
 * query fields:
 *
 * 0: id : Text ID to put.
 * 1: deleted : integer value to put.
 * 2: vdi : string to put.
 * 3: instance_id : Instance ID to put.
 * 4: location_id : Location ID to put.
 * 5: ip : IP address to put.
 */
int wallyd_test_tls(struct zpath_debug_state *request_state,
                    const char **query_values,
                    int query_value_count,
                    void *cookie)
{
    struct zpath_tls tls;
    struct argo_object *obj;
    struct wally_table *table;
    int res;
    struct argo_structure_description *desc;
    static int initialized = 0;
    char str[10000];

    if (!initialized) {
        res = zpath_tls_init_registration(wallyd);
        if (res) {
            ZDP("Could not init zpath_tls: %s\n", zpath_result_string(res));
            return ZPATH_RESULT_NO_ERROR;
        }
        initialized = 1;
    }

    memset(&tls, 0, sizeof(tls));

    if (!query_values[0]) {
        ZDP("'id' field required\n");
        return ZPATH_RESULT_NO_ERROR;
    }

    if (!query_values[5]) {
        ZDP("'ip' field required\n");
        return ZPATH_RESULT_NO_ERROR;
    } else {
        ZDP("Parsing IP = <%s>\n", query_values[5]);
        if (argo_string_to_inet(query_values[5], &(tls.ip))) {
            ZDP("Invalid IP address\n");
            return ZPATH_RESULT_NO_ERROR;
        }
    }

    if (query_values[1]) {
        tls.deleted = atoi(query_values[1]);
    }

    if (query_values[2]) {
        tls.vdi = query_values[2];
    }

    if (query_values[3]) {
        tls.instance_id = strtoul(query_values[3], NULL, 0);
    }

    if (query_values[4]) {
        tls.location_id = strtoul(query_values[4], NULL, 0);
    }

    desc = argo_get_structure_description("zpath_tls");
    if (!desc) {
        ZDP("Could not get description for zpath_tls\n");
        ZPATH_LOG(AL_DEBUG, "here");
        return ZPATH_RESULT_NO_ERROR;
    }

    tls.id = query_values[0];
    obj = argo_object_create(desc, &tls);
    if (!obj) {
        ZDP("Could not create argo object\n");
        ZPATH_LOG(AL_DEBUG, "here");
        return ZPATH_RESULT_NO_ERROR;
    }

    argo_object_dump(obj, str, sizeof(str), NULL, 1);
    ZDP("Write object: %s\n", str);

    table = wally_table_get(wallyd, "zpath_tls");
    if (!table) {
        ZDP("Could not get wally table for 'zpath_tls'\n");
        ZPATH_LOG(AL_DEBUG, "here");
        return ZPATH_RESULT_NO_ERROR;
    }

    res = wally_table_write_origin_row(table, obj);
    argo_object_release(obj);
    if (res) {
        ZDP("Could not write table row: %s\n", zpath_result_string(res));
        ZPATH_LOG(AL_DEBUG, "here");
        return ZPATH_RESULT_NO_ERROR;
    }

    ZDP("Wrote row.\n");
    ZPATH_LOG(AL_DEBUG, "here");
    return ZPATH_RESULT_NO_ERROR;
}



/*
 *
 * Configuration knobs: (All of these come from local_db)
 *
 * wallyd_db_name - The name of the database wallyd is exporting.
 *
 * wallyd_is_global - An indication that we are replicating global
 *    tables, so we don't need to fetch global system config (instance
 *    table) from other sources.
 *
 * wallyd_fohh_remote_host - domain name if we are replicating a
 *    remote database. If not configured, will not be created.
 *
 * wallyd_fohh_remote_port - Port for remote FOHH connection.
 *    (Defaults to 443)
 *
 * wallyd_postgres_host - domain name of the postgres server we use
 *    for our local postgres database. If not configured, defaults to
 *    localhost. Note: This DB will only be configured 'writable' if
 *    there is also a wallyd_fohh_remote_host.
 *
 * wallyd_service_port - Port on which to provide service.
 */
void usage(const char *argv0, const char *format, ...)
    __attribute__((format(printf, 2, 3)));
void usage(const char *argv0, const char *format, ...)
{
    va_list list;

    fprintf(stdout, "Error: ");
	va_start(list, format);
	vfprintf(stdout, format, list);

    fprintf(stdout, "%s: Usage:\n", argv0);
    fprintf(stdout,
            "  -instance NUMBER           : Specify which instance ID this\n"
            "                               daemon will run as (default 0)\n"
            "  -local_file FILE           : Specify that config should come\n"
            "                               from FILE rather than local_db\n"
            "  -version                   : Display version and exit\n"
            "  -itasca_logs PORT          : Override HTTP POST logging, and\n"
            "                               send logs using argo encoding to port PORT for service endpoints\n"
            "  -daemon                    : Run as daemon\n"
            "  -endpoint                  : Run as an endpoint re: deleted rows\n"
            "  -dbhost NAME               : Run using specified dbhost\n"
            "  -fully_loaded              : Run with fully loaded DBs\n"
            "  -load_tables OPTION        : Specify which tables to load (static : Static Tables, non-static :  Non Static Tables, all : All Tables). Default is all : All Tables \n"
            "  -cleanup                   : Cleanup deleted rows\n"
            "  -dump_schema               : Output in current directory one file per table that this system knows about\n"
            "  -schema_dir DIR            : Specify a directory from which to read .js schema files. These schema files\n"
            "                               will not override compiled-in schema, but will augment compiled=in schema\n"
            "                               defaults to /zpath/etc/wallyd\n"

            "  -dbconns   NUMBER          : Opt : number of db connections to the db. Default 4.\n"
            "  -db_read_batch_size NUMBER : Opt : number of db rows to be read per DB query. Default 10000.\n"
            "  -concurrent_write          : Opt : allow concurrent write to local db\n"
            "  -db_init_read_nolock       : Opt : DB initial read without exclusive table lock\n"
            "  -enable_gap_verified_read  : Opt : Enable GAP verified read feature\n"
            "  -synchronous_commit_off    : Opt : turn synchronous_commit off. Per-session and overrides default config on postgres\n"
            "  -optimize_write            : Opt : equivalent to -synchronous_commit_off -dbconns 10 -concurrent_write\n"
            "  -enable_batch_write        : Opt : enable upsert(batch write) feature \n"
            "  -write_batch_size          : Opt : no of row objects for a batch \n"
            "  -psql_debug                : Opt : turn on SQL debugging. Useful only to debug startup SQL. Otherwise use /debug/wally command.\n"

            "  -stackpath PATH            : Write cores to path specified. No trailing slash please\n"
            "  -wally_threads             : Opt : no of wally threads to be created for wally table operations \n"
            "  -fohh_threads              : Opt : no of fohh threads \n"
            "  -disable_heartbeat_monitor : Opt : Disable thread-hang detection\n"
            "  -max_zhash_table_size_log2 : Opt : zhash_table max table size will not exceed 2^value\n"
            "  -timer_interval_us         : Opt : Set wally timer interval in us. Recommended value greater than 1000\n"
            "  -cleanup_delay_at_start_min: Opt : Delay wally trimmer cleanup for each table during start by given val in minutes\n"
            "  -disable_ext_heartbeat     : Opt : Disable extended heartbeat and set the thread's default timeout to 20 seconds\n"
            "  -disable_wally_tcp_config  : Opt : Disable Wally's TCP keepalive configuration and set Kernel's default TCP value\n"
            "  -tcp_keepalives_idle       : Opt : Connection inactivity after which TCP send a keepalive message to the server\n"
            "                                     Default 60 seconds; Range 10-7200 in seconds\n"
            "  -tcp_keepalives_interval   : Opt : TCP keepalive message that is not acknowledged by the server should be retransmitted\n"
            "                                     Default 20 seconds; Range 15-75 in seconds\n"
            "  -tcp_keepalives_count      : Opt : No of TCP keepalives that can be lost before the connection is considered dead\n"
            "                                     Default 3; Range 1-255\n"
            "  -tcp_user_timeout          : Opt : Transmitted data may remain unacknowledged before a connection is forcibly closed\n"
            "                                     Default 240000 ms; Range 60000-7875000 in milliseconds\n"
            "  -poll_rate_timeout_min     : Opt : Value in minutes to restart if origin-wally not polling ,value 0 to disable \n"
            "                                     Default value is 30 min : Range 10-60 min\n"
            "  -disable_operational_mode  : Opt : Disable operational mode feature\n"
            "  -statement_timeout         : Opt : Abort any SQL statement that takes more than the specified amount of time\n"
            "                                     Default disabled (0) : Range 0-30 minutes : Disable 0 \n"
            "  -lock_timeout              : Opt : Abort any SQL lock statement that waits more than the specified amount of time to acquire a lock\n"
            "                                     Default disabled (0) : Range 0-30 minutes : Disable 0 \n"
            "  -idle_session_timeout      : Opt : Abort any SQL session that remain idle for more than the specified amount of time.\n"
            "                                     Default disabled (0) : Range 0-120 minutes : Disable 0 \n"
            "  -idle_in_trans_session_timeout : Opt : Abort any SQL transaction that remain idle for more than the specified amount of time.\n"
            "                                         Default disabled (0) : Range 0-30 minutes : Disable 0 \n"
            "  -test                      : Enable test mode\n"
			"  -exportable_core_dumps: Pass this flag to enable exportable core dumps using packet buffer pools \n"
            "  -debuglog : Pass this flag to enable all debug flags during startup \n");
    zpath_app_logging_usage_print();

    exit(1);
}


static int zpath_debug_wally_display_static_tables(struct zpath_debug_state *request_state,
                                                    const char **query_values,
                                                    int query_value_count,
                                                    void *cookie)
{
    int i;
    int static_table_count = 0;

    if (load_tables == static_tables) {
        ZDP("Wallyd server Static tables are:\n");
        for (i = 0; i < (sizeof(wts) / sizeof(struct wt)); i++) {
            if (wts[i].is_static || !strncmp(wts[i].table_name, "zpath_table", strnlen(wts[i].table_name,TABLE_NAME_MAX_SIZE))) {
                ZDP("%s\n", wts[i].table_name);
                static_table_count++;
            }
        }
        ZDP("Wallyd server running with loaded %d Static tables only.\n", static_table_count);
    } else if (load_tables == non_static_tables) {
        ZDP("Wallyd server running with loaded Non Static tables only.\n");
    } else {
        ZDP("Wallyd server running with loaded all tables (Static and Non Static).\n");
    }

    return ZPATH_RESULT_NO_ERROR;
}


static int zpath_debug_wally_set_hb_tick (struct zpath_debug_state *request_state,
                                          const char **query_values,
                                          int query_value_count,
                                          void *cookie)
{
    int32_t hb_timeout_s = 0;
    int32_t hb_iteration = 0;

    if (!query_values[0] || !query_values[1]) {
        ZDP("'max_hb_iteration' and 'max_hb_miss_timeout_s' are required\n");
        return ZPATH_RESULT_NO_ERROR;
     }

    if (zthread_is_heartbeat_monitor_enabled() == 0) {
        ZDP("Heartbeat monitoring is disabled. Can't configure heartbeat parameters\n");
        return ZPATH_RESULT_NO_ERROR;
    }

    hb_iteration = strtol(query_values[0], NULL, BASE10);
    if (hb_iteration <= 0 || hb_iteration > WALLY_MAX_HB_CHECK_ITERATION) {
        ZDP("max_hb_iteration should be between 1 and %d\n", WALLY_MAX_HB_CHECK_ITERATION);
        return ZPATH_RESULT_NO_ERROR;
    }

    hb_timeout_s = strtol(query_values[1], NULL, BASE10);
    if (hb_timeout_s <= 0 || hb_timeout_s > wally_default_hb_timeout_s) {
        ZDP("max_hb_timeout_s should be between 1 and %d\n", wally_default_hb_timeout_s);
        return ZPATH_RESULT_NO_ERROR;
    }

    if (hb_timeout_s < WALLY_DIV_ROUND_CLOSEST(wally_default_hb_timeout_s, WALLY_ONE_THIRD_DIVISOR)) {
        ZDP("INFO: Default max_hb_miss_timeout_s: %d, configuring lower value %d\n",
                    WALLY_DIV_ROUND_CLOSEST(wally_default_hb_timeout_s, WALLY_ONE_THIRD_DIVISOR),
                    hb_timeout_s);
    }

    if (hb_iteration < WALLY_DEFAULT_MAX_HB_CHECK_ITERATION) {
        ZDP("INFO: Default max_hb_iteration: %d, configuring lower value %d\n",
                WALLY_DEFAULT_MAX_HB_CHECK_ITERATION, hb_iteration);
    }

    wally_max_hb_iteration = hb_iteration;
    wally_max_hb_miss_timeout_s = hb_timeout_s;
    ZDP("\nConfigured values hb_timeout_s %d hb_iteration %d\n", wally_max_hb_miss_timeout_s, wally_max_hb_iteration);

    return ZPATH_RESULT_NO_ERROR;
}

static void wally_set_hb_timeout()
{
    /* Extended HB is disabled, set default HB timeout to 20 seconds and max_hb_miss to 7 seconds */
    wally_default_hb_timeout_s = WALLY_DEFAULT_HB_TIMEOUT_S;
    wally_max_hb_miss_timeout_s = WALLY_DIV_ROUND_CLOSEST(WALLY_DEFAULT_HB_TIMEOUT_S, WALLY_ONE_THIRD_DIVISOR);
    fprintf(stdout , "Set HB value %d hb_threshold %d\n", wally_default_hb_timeout_s, wally_max_hb_miss_timeout_s);
}

/*
 * Routine to enable extended HB.
 * */
static void wally_enable_extended_hb_timeout()
{
    /* Extended HB is 300 seconds and max_hb_miss is 100 seconds */
    wally_default_hb_timeout_s = WALLY_DEFAULT_EXT_HB_TIMEOUT_S;
    wally_max_hb_miss_timeout_s = WALLY_DIV_ROUND_CLOSEST(WALLY_DEFAULT_EXT_HB_TIMEOUT_S, WALLY_ONE_THIRD_DIVISOR);
    fprintf(stdout , "Set extended HB value %d hb_threshold %d\n", wally_default_hb_timeout_s, wally_max_hb_miss_timeout_s);
}

static wally_row_fixup_f *get_row_fixup_f(const char *table_name)
{
    wally_row_fixup_f *fixup_f = NULL;
    /*
     * Below tables does not have customer_gid column. Making it available through row_fixup callback
     */
    if (!strcmp(table_name, "zpn_znf_to_group")) {
        fixup_f = zpn_znf_to_group_row_fixup;
    } else if (!strcmp(table_name, "zpn_branch_connector_to_group")) {
        fixup_f = zpn_branch_connector_to_grp_row_fixup;
    } else if (!strcmp(table_name, "zpn_machine_to_group")) {
        fixup_f = zpn_machine_to_group_row_fixup;
    }

    return fixup_f;
}

int do_wts(struct wt *wt) {
    wally_row_fixup_f *fixup_f = get_row_fixup_f(wt->table_name);
    struct table_cleanup_parameter cleanup_param = {0};
    cleanup_param.state = zpath_cleanup_state(wt->table_name);
    cleanup_param.max_cleanup_rows = ZPATH_LOCAL_WALLYD_MAX_CLEANUP_ROWS;
    cleanup_param.max_scan_rows = ZPATH_LOCAL_WALLYD_MAX_SCAN_ROWS;
    cleanup_param.cleanup_interval_us = ZPATH_LOCAL_WALLYD_CLEANUP_INTERVAL_US;
    cleanup_param.row_expire_sec = ZPATH_LOCAL_WALLYD_ROW_EXPIRE_SEC;
    cleanup_param.min_seq_auto_update = FULLY_LOADED && do_cleanup ? 1 : 0;
    if (IS_WALLY_LAYER_ENABLED) {
        wally_table_queue_enqueue_do_wts(wt,
                                        wallyd_slave_db,
                                        wallyd_remote_db,
                                        wallyd,
                                        FULLY_LOADED,
                                        cleanup_param,
                                        load_tables,
                                        fixup_f,
                                        ZPATH_LOCAL_WALLYD_IS_GLOBAL,
                                        wallyd_zpath_table_register_cb);
    } else {
        do_wts_internal(wt,
                    wallyd_slave_db,
                    wallyd_remote_db,
                    wallyd,
                    FULLY_LOADED,
                    cleanup_param,
                    load_tables,
                    fixup_f,
                    ZPATH_LOCAL_WALLYD_IS_GLOBAL,
                    wallyd_zpath_table_register_cb);
    }
    return WALLY_RESULT_NO_ERROR;
}


void read_schema(void)
{
    DIR *dp;
    struct dirent *entry;

    dp = opendir(config_read_js);
    if (!dp) {
        WALLYD_LOG(AL_NOTICE, "Could not read schema directory %s", config_read_js);
        return;
    }

    /* NOTE: This code never frees objects, as that memory wants to
     * live forever. Even if unused, this memory is not freed. */
    while ((entry = readdir(dp))) {
        if (entry->d_type == DT_REG) {
            char filename[1000];
            char line[10000];
            struct argo_object *obj;
            struct wt *wt;
            int i;

            snprintf(filename, sizeof(filename), "%s/%s", config_read_js, entry->d_name);
            FILE *fp = fopen(filename, "r");
            if (!fp) {
                WALLYD_LOG(AL_ERROR, "Could not read schema from %s: could not open file", filename);
                continue;
            }
            /* First, read wt */
            if (!fgets(line, sizeof(line), fp)) {
                WALLYD_LOG(AL_ERROR, "Could not read first line of schema from %s:", filename);
                fclose(fp);
                continue;
            }
            obj = argo_deserialize_json(line, strlen(line));
            if (!obj) {
                WALLYD_LOG(AL_ERROR, "Could not parse first line of schema from %s:", filename);
                fclose(fp);
                continue;
            }
            if (strcmp(argo_object_get_type(obj), "wt")) {
                WALLYD_LOG(AL_ERROR, "First line of schema does not contain wt structure in %s:", filename);
                fclose(fp);
                continue;
            }
            wt = obj->base_structure_void;
            wt->desc = ZLIB_CALLOC(sizeof(*(wt->desc)) * wt->struct_field_count);
            for (i = 0; i < wt->struct_field_count; i++) {
                struct argo_field_description *fd;
                if (!fgets(line, sizeof(line), fp)) {
                    WALLYD_LOG(AL_ERROR, "Could not read field %d of schema from %s:", i, filename);
                    break;
                }
                obj = argo_deserialize_json(line, strlen(line));
                if (!obj) {
                    WALLYD_LOG(AL_ERROR, "Field %d of %s did not deserialize", i, filename);
                    break;
                }
                if (strcmp(argo_object_get_type(obj), "argo_field_description")) {
                    WALLYD_LOG(AL_ERROR, "Field %d of %s does not contain argo_field_description", i, filename);
                    break;
                }
                fd = obj->base_structure_void;
                wt->desc[i] = (*fd);
            }
            fclose(fp);
            if (i != wt->struct_field_count) {
                continue;
            }

            /* Check if this thing is already registered */
            if (argo_get_structure_description(wt->table_name)) {
                WALLYD_LOG(AL_NOTICE, "Schema file exists for %s: %s, but it is already built in to wally; ignoring.", wt->table_name, filename);
            } else {
                if (load_tables == static_tables) {
                    if (wt->is_static || !strncmp(wt->table_name, "zpath_table", strnlen(wt->table_name,TABLE_NAME_MAX_SIZE))) {
                        __sync_add_and_fetch_4(&(do_wts_done),1);
                        do_wts(wt);
                    }
                } else if (load_tables == non_static_tables) {
                    if (!wt->is_static || !strncmp(wt->table_name, "zpath_table", strnlen(wt->table_name,TABLE_NAME_MAX_SIZE))) {
                        __sync_add_and_fetch_4(&(do_wts_done),1);
                        do_wts(wt);
                    }
                } else {
                    __sync_add_and_fetch_4(&(do_wts_done),1);
                    do_wts(wt);
                }
            }
        }
    }
    closedir(dp);
}

static int wally_system_cpu_stats_fill(void *cookie, int counter, void *structure_data)
{
    struct zpn_system_cpu_stats *stats = structure_data;

    static struct zpath_system_cpu_stats_util cpu_stats_util = {0};

    /* get avg_1min, avg_5min, avg_15min */
    int res = zpath_system_cpu_stats_fill(cookie, counter, stats);
    if (res) {
        WALLYD_LOG(AL_ERROR, "Could not fill system cpu stats");
        return res;
    }

    /* get cpu_util_percent, cpu_steal_percent */
    res = zpath_system_get_cpu_stats(&cpu_stats_util);
    if (res) {
        WALLYD_LOG(AL_ERROR, "Could not get system cpu stats");
        return res;
    }
    stats->cpu_util_percent = cpu_stats_util.cpu_util;
    stats->cpu_steal_percent = cpu_stats_util.cpu_steal_perc;

    /* Wally is part of cloud and its time is synced via NTP */
    stats->cloud_time_us = epoch_us();

    return WALLY_RESULT_NO_ERROR;
}

#define US_IN_ONE_SECOND    1000000

int
wallyd_global_stats_fill(void *cookie, int counter, void *structure_data)
{
    struct wallyd_global_stats *wallyd_stats = (struct wallyd_global_stats *) structure_data;
    /* Print time since startup until app moves to RUNNING state */
    if (wallyd_bootup_time == -1)
    {
        wallyd_stats->wallyd_bootup_time_s = (epoch_us() - start_time)/US_IN_ONE_SECOND;
    } else {
        wallyd_stats->wallyd_bootup_time_s = wallyd_bootup_time /US_IN_ONE_SECOND;
    }
    wallyd_stats->wallyd_bootup_pending_tables = responses_waiting;
    wallyd_stats->wally_app_state = get_wally_app_state();
    wallyd_stats->wally_postgres_db_query_batch_size_cfg = wally_gbl_cfg.wally_postgres_db_query_batch_size;

    /* Published after running state also to support future fields */
    return ARGO_RESULT_NO_ERROR;
}

int wally_system_rpc_init(){
    zpn_wally_disk_stats_description = argo_register_global_structure(ZPN_SYSTEM_DISK_STATS_HELPER);
    zpn_wally_cpu_stats_description = argo_register_global_structure(ZPN_SYSTEM_CPU_STATS_HELPER);
    zpn_wally_fd_stats_description = argo_register_global_structure(ZPN_SYSTEM_FD_STATS_HELPER);
    zpn_wally_sock_stats_description = argo_register_global_structure(ZPN_SYSTEM_SOCK_STATS_HELPER);
    zpn_wally_memory_stats_description = argo_register_global_structure(ZPN_SYSTEM_MEMORY_STATS_HELPER);
    zpn_wally_network_stats_description = argo_register_global_structure(ZPN_SYSTEM_NETWORK_STATS_HELPER);
    zpn_wally_private_broker_stats_description = argo_register_global_structure(ZPN_PRIVATE_BROKER_SYSTEM_STATS_HELPER);
    zpn_wally_assistant_stats_description = argo_register_global_structure(ZPN_ASSISTANT_SYSTEM_STATS_HELPER);
    zpn_wally_sitec_stats_description = argo_register_global_structure(ZPN_SITEC_SYSTEM_STATS_HELPER);


    if(!zpn_wally_disk_stats_description ||
       !zpn_wally_cpu_stats_description ||
       !zpn_wally_fd_stats_description ||
       !zpn_wally_sock_stats_description ||
       !zpn_wally_memory_stats_description ||
       !zpn_wally_private_broker_stats_description ||
       !zpn_wally_sitec_stats_description ||
       !zpn_wally_assistant_stats_description) {
        return ZPN_RESULT_ERR;
     }

     return ZPN_RESULT_NO_ERROR;
}

static int zpn_wally_drain_stats_init()
{
    wallyd_drain_stats_description = argo_register_global_structure(WALLYD_DRAIN_STATS_HELPER);
    if(!wallyd_drain_stats_description)
    {
        return ZPN_RESULT_ERR;
    }
    argo_log_register_structure(argo_log_get("statistics_log"),
                                "wallyd_drain_stats",
                                AL_INFO,
                                60*1000*1000,    /* 1 minute */
                                wallyd_drain_stats_description,
                                &drain_details.drain_stats, 0,
                                NULL, NULL);
    return ZPN_RESULT_NO_ERROR;
}

static int zpn_wally_global_stats_init()
{
    wallyd_global_stats_description = argo_register_global_structure(WALLYD_GLOBAL_STATS_HELPER);
    if(!wallyd_global_stats_description)
    {
        return ZPN_RESULT_ERR;
    }
    wallyd_global_stats_structure =  argo_log_register_structure(argo_log_get("statistics_log"),
                                "wallyd_global_stats",
                                AL_INFO,
                                60*1000*1000,    /* 1 minute */
                                wallyd_global_stats_description,
                                &wallyd_stats, 0,
                                wallyd_global_stats_fill, NULL);
    return ZPN_RESULT_NO_ERROR;
}
static int zpn_wally_stats_init()
{
    int res = 0;
    res = wally_system_rpc_init();

    if(res){
        return res;
    }

    if ((wally_available_cpus = sysconf(_SC_NPROCESSORS_ONLN)) == -1) {
        ZPATH_LOG(AL_ERROR, "failed to read number of available cpus - %s", strerror(errno));
    }

    /* Initialize fd and socket statistics. */
    argo_log_register_structure(argo_log_get("statistics_log"),
                                "zpn_wally_fd_stats",
                                AL_INFO,
                                5*60*1000*1000, /* 5 mins interval */
                                zpn_wally_fd_stats_description,
                                &zpn_wally_fd_stats,
                                1,
                                zpn_system_fd_stats_fill,
                                NULL);

    argo_log_register_structure(argo_log_get("statistics_log"),
                                "zpn_wally_sock_stats",
                                AL_INFO,
                                5*60*1000*1000, /* 5 mins interval */
                                zpn_wally_sock_stats_description,
                                &zpn_wally_sock_stats,
                                1,
                                zpn_system_sock_stats_fill,
                                NULL);

    argo_log_register_structure(argo_log_get("statistics_log"),
                                "zpn_wally_cpu_stats",
                                AL_INFO,
                                5*60*1000*1000, /* 5 mins interval */
                                zpn_wally_cpu_stats_description,
                                &zpn_wally_cpu_stats,
                                1,
                                wally_system_cpu_stats_fill,
                                &wally_available_cpus);

    argo_log_register_structure(argo_log_get("statistics_log"),
                                "zpn_wally_disk_stats",
                                AL_INFO,
                                5*60*1000*1000, /* 5 mins interval */
                                zpn_wally_disk_stats_description,
                                &zpn_wally_disk_stats,
                                1,
                                zpn_system_disk_stats_fill,
                                NULL);

    argo_log_register_structure(argo_log_get("statistics_log"),
                                "zpn_wally_memory_stats",
                                AL_INFO,
                                5*60*1000*1000, /* 5 mins interval */
                                zpn_wally_memory_stats_description,
                                &zpn_wally_memory_stats,
                                1,
                                zpn_system_memory_stats_fill,
                                NULL);


    return ZPN_RESULT_NO_ERROR;
}

/* Generate file name with UTC timestamp */
static void wally_gen_filename(const char *filename, char *buf, size_t buf_len)
{
    struct timeval tv;
    struct tm tm;

    memset (&tm, 0, sizeof (tm));

    gettimeofday(&tv, NULL);
    gmtime_r(&(tv.tv_sec), &tm);
    snprintf(buf, buf_len, "%s.UTC.%04d-%02d-%02d.%02d:%02d:%02d.%06d",
             filename,
             tm.tm_year + 1900,
             tm.tm_mon + 1,
             tm.tm_mday,
             tm.tm_hour,
             tm.tm_min,
             tm.tm_sec,
             (int)tv.tv_usec);
}

/*
 * wallyd_client_list_command
 *
 * Routine to display client details
 */
static int wallyd_client_list_command(struct zpath_debug_state *request_state,
                                             const char **query_values,
                                             int query_value_count,
                                             void *cookie)
{
    char filename[WALLY_LOG_FILE_LEN] = {0};
    char tmp_name[WALLY_LOG_FILE_LEN] = {0};
    int64_t port_num = 0;
    const char *port = NULL;
    int res = 0;
    uint32_t count = 0, wally_client = 0;
    char *endptr = NULL;
    uint32_t s_index = 0;

    if (fp_client != NULL) {
        ZDP("\nAnother client list command in progress\n");
		return ZPN_RESULT_NO_ERROR;
    }

    if (query_values[0]) {
        ZDP("\nClient domain: %s\n", query_values[0]);
    }

    if (query_values[1]) {
        port = query_values[1];
        port_num = strtoul(query_values[1], &endptr, BASE10);
        if (((endptr == query_values[1]) || (*endptr != '\0')) ||
                port_num < 0 || port_num > WALLY_MAX_PORT_NUMBER) {
            ZDP("Invalid port number. Should be between 0 and %d\n", WALLY_MAX_PORT_NUMBER);
            return ZPN_RESULT_NO_ERROR;
        }
        if (!isdigit(port[0])) {
            /* Positive/Negative value is included in the port
             * like, +122, +0, -0, remove the symbol and proceed */
            port = (port + 1);
        }
        ZDP("Client port: %s\n", port);
    }

    for (s_index = 0; s_index <= wally_server_max_index; s_index++) {
        res = wally_server_clients_count (wally_fohh_server_handle[s_index], query_values[0],
                port, 0, &count, &wally_client);
        if (res) {
            ZDP("Get client count failed\n");
            return res;
        }
    }

    if (count == 0 && wally_client == 0) {
        ZDP("No matching client found for given domain/port\n\n");
        return ZPN_RESULT_NO_ERROR;
    }

    snprintf(filename, sizeof(filename), "%s", WALLY_CLIENT_LOG);

    wally_gen_filename(filename, tmp_name, sizeof(tmp_name));

    ZDP("\nClient detail redirected to file  : %s\n", tmp_name);

	fp_client = fopen(tmp_name, "w");
	if (fp_client == NULL) {
		ZDP("File open failed for  %s: %s \n",
                filename, strerror(errno));
		return ZPN_RESULT_NO_ERROR;
	}

    /* Reset the variables */
    count = 0;
    wally_client = 0;

    for (s_index = 0; s_index <= wally_server_max_index; s_index++) {
        res = wally_server_client_list (wally_fohh_server_handle[s_index], fp_client,
                query_values[0], query_values[1], &count);
        if (res) {
            ZDP("Get client list failed\n");
            fclose(fp_client);
            fp_client = NULL;
            return res;
        }
    }

    ZDP("\n Number of clients detail redirected: %d\n", count);

    fclose(fp_client);
    fp_client = NULL;

    return ZPN_RESULT_NO_ERROR;
}

/* Get the last element from the list */
static struct wallyd_drain_hist * wallyd_get_drain_hist_last()
{
    struct wallyd_drain_hist *drain_hist = NULL;
    struct wallyd_drain_hist *tmp_drain_hist = NULL;
    struct wallyd_drain_hist *last_drain_hist = NULL;

    LIST_FOREACH_SAFE(drain_hist, &(drain_details.drain_hist), hist, tmp_drain_hist) {
        last_drain_hist = drain_hist;
    }
    return last_drain_hist;
}

int32_t wally_oper_drain_all_clients()
{
    /* Initialize to default values */
    uint64_t percentage = WALLY_DEFAULT_DRAIN_PC;
    uint64_t rate = WALLY_MIN_DRAIN_PC;
    char filename[WALLY_LOG_FILE_LEN] = {0};
    char tmp_name[WALLY_LOG_FILE_LEN] = {0};
    uint32_t count = 0, non_wally_client = 0, wally_client = 0;
    uint32_t percent_drain = 0,  time_to_complete = 0;
    int32_t force = 1;
    static uint32_t drain_rate = 0;
    int res = 0;
    uint32_t s_index = 0;

    if (drain_status != WALLY_DRAIN_NONE) {
        oper_mode_g.drain_pending = true;
        WALLY_LOG(AL_INFO, "Previous drain command in progress");
        return WALLY_RESULT_NO_ERROR;
    }

    oper_mode_g.drain_pending = false;
    /* show the input values */
    WALLY_LOG(AL_INFO, "Percentage %"PRId64"%% rate %"PRId64"%% force %d",
                percentage, rate, force);

    /* Get the number of matching clients for the given port and domain. This data needed to
     * identify the percentage of clients to be drained
     * */
    for (s_index = 0; s_index <= wally_server_max_index; s_index++) {
        res = wally_server_clients_count (wally_fohh_server_handle[s_index], NULL, NULL,
                false, &non_wally_client, &wally_client);
        if (res) {
            WALLY_LOG(AL_ERROR, "Get client count failed");
            return res;
        }
    }

    /* Drain should reset all the clients including leaf-wally */
    count = non_wally_client + wally_client;

    if (count == 0) {
        WALLY_LOG(AL_INFO, "   No clients connected to the Wally");
        return WALLY_RESULT_NO_ERROR;
    }

    WALLY_LOG(AL_INFO, "   Total clients connected                        : %d",
            non_wally_client);
    WALLY_LOG(AL_INFO, "   Total Wally connected                          : %d",
            wally_client);
    percent_drain = count;

    drain_rate = ceil(percent_drain * ((float)rate/100));
    WALLY_LOG(AL_INFO, "   Drain client per second at %3"PRId64" percent rate  : %d client/per-second",
            rate, drain_rate);

    if (drain_rate == 0) {
        WALLY_LOG(AL_INFO, "Client drain per second is 0, choose higher rate. exiting");
        return WALLY_RESULT_NO_ERROR;
    }
    time_to_complete = ((uint32_t)(ceil((double)percent_drain/drain_rate)
                *  wallyd_drain_delay) + wallyd_drain_delay_mid);
    WALLY_LOG(AL_INFO, "   Approx time to complete drain at %3"PRId64"%% rate  : %d seconds",
            rate, time_to_complete);

    count = 0;

    /* Mark the clients for drain. The reason for not reseting them immediately
     * 1. Reset happens in different thread
     * 2. if "rate" is configured, reset will happen in multiple batches
     *    so, in the mean time, any altration would have been happened in the list,
     *    like, new clients added or old clients removed or drained client reconnected.
     *    Drain command should not reset the same client again and again
     * 3. If marked client reset on its own then manual reset is not required
     * To resolve this, marking is needed. */
    /* Wally clients marking */
    for (s_index = 0; wally_client && s_index <= wally_server_max_index; s_index++) {
        res = wally_server_mark_drain (wally_fohh_server_handle[s_index], WALLY_DRAIN_FILTER, NULL,
                force, &count, percent_drain, true);
        if (res) {
            WALLY_LOG(AL_ERROR, "Marking drain list failed");
            return res;
        }
    }
    count = 0;
    /* Non-Wally clients marking */
    for (s_index = 0; non_wally_client && s_index <= wally_server_max_index; s_index++) {
        res = wally_server_mark_drain (wally_fohh_server_handle[s_index], NULL, NULL,
                0, &count, percent_drain, false);
        if (res) {
            WALLY_LOG(AL_ERROR, "Marking drain list failed");
            return res;
        }
    }

    count = 0;

    /* Copy the data to global structure, which will be used by cleanup thread
     * to drain the clients
     * */
    memset (&drain_config, 0, sizeof(drain_config));
    drain_config.percentage = (uint32_t)percentage;
    drain_config.rate = (uint32_t)rate;
    drain_config.drain_rate = drain_rate;
    drain_config.drain_requested = percent_drain;
    drain_config.force = force;
    drain_config.drain_time_us = 0;
    drain_config.last_drain_server = 0; /* Start the drain from first server */
    drain_config.oper_drain = true;
    drain_config.time_to_complete = time_to_complete;
    drain_details.two_phase_drain = true;
    drain_details.drain_phase = wally_client?WALLY_DRAIN_P1:WALLY_DRAIN_P2;
    drain_details.drain_state = wally_client?WALLY_DRAIN_S_P1:WALLY_DRAIN_S_P2;

    /* Set to true */
    WALLYD_LOG(AL_NOTICE, " Drain started.");
    drain_status = WALLY_DRAIN_IN_PROGRESS;
    oper_mode_g.drain_in_process = true;

    snprintf(filename, sizeof(filename), "%s", WALLY_DRAIN_LOG);
    wally_gen_filename(filename, tmp_name, sizeof(tmp_name));
    WALLY_LOG(AL_INFO, "Drain logs redirected to file: %s", tmp_name);

    fp_drain = fopen(tmp_name, "w");
    if (fp_drain == NULL) {
        WALLY_LOG(AL_ERROR, "File open failed for  %s: %s",
                filename, strerror(errno));
        return WALLY_RESULT_NO_ERROR;
    }

    return WALLY_RESULT_ASYNCHRONOUS;
}

/* Drain the marked clients, this calls the fohh wrapper to reset  */
static void wallyd_drain_clients ()
{
	static int index = 0;
	static int fail_index = 0;
	int drain_rate = drain_config.drain_rate;
	struct wallyd_drain_hist *drain_hist = NULL;
    struct timeval tv;
    uint64_t now_us = 0;
    bool is_completed = 0;
    struct tm tm;
    uint32_t reset_count = 0;
    uint32_t reset_failed_count = 0;
    int res = 0;
    uint32_t s_index = 0;

    memset (&tm, 0, sizeof (tm));
    memset (&tv, 0, sizeof (tv));

    if (index == 0) {
        memset (&drain_config.cmd_time, 0, sizeof (drain_config.cmd_time));
        gettimeofday(&drain_config.cmd_time, NULL);
        drain_details.drain_stats.drain_cmd_count_lifetime++;

        gettimeofday(&tv, NULL);
        gmtime_r(&(tv.tv_sec), &tm);
        fprintf(fp_drain, "Drain start time    :  %04d-%02d-%02d.%02d:%02d:%02d.%06d\n",
                tm.tm_year + 1900,
                tm.tm_mon + 1,
                tm.tm_mday,
                tm.tm_hour,
                tm.tm_min,
                tm.tm_sec,
                (int)tv.tv_usec);
        memset (&tm, 0, sizeof (tm));
    }

    for (s_index = drain_config.last_drain_server; s_index <= wally_server_max_index; s_index++) {
        is_completed = false;
        res = wally_server_fohh_reset_clients (wally_fohh_server_handle[s_index], &is_completed, fp_drain, drain_rate,
                &reset_count, &reset_failed_count, drain_details.two_phase_drain, drain_details.drain_phase);

        if (res) {
            WALLYD_LOG(AL_ERROR, "Wally FOHH reset failed");
            goto  EXIT;
        }

        if (is_completed) {
            if ((s_index < wally_server_max_index) && reset_count < drain_rate) {
                /* Drain on this server is completed, but
                 * 1. this is not the last server.
                 * 2. reset count is less than the drain_rate
                 * Every iteration should complete the "drain_rate" number of resets.
                 * So continue the next server to complete the remaining reset
                 * */
                continue;
            }
        }

        index += reset_count;
        fail_index += reset_failed_count;

        if (reset_count == drain_rate) {
            /* Reset count reached drain_rate for this iteration, there could be still drain
             * pending in next servers. So set the completed flag to flase to continue
             * the drain for next servers
             * If it is the last server, just keep the is_completed flag as it is.
             * */
            if (s_index < wally_server_max_index) {
                is_completed = false;
            }
            break;
        }
    }

    now_us = epoch_us();
    drain_config.drain_time_us = now_us -
        (US_IN_ONE_SECOND * drain_config.cmd_time.tv_sec + drain_config.cmd_time.tv_usec);
    if (is_completed == false ||
            (is_completed && drain_details.two_phase_drain &&
             drain_details.drain_phase == WALLY_DRAIN_P1)) {

        if (drain_details.two_phase_drain) {
            /* Two drain is active - Operational-mode initiated drain
             * Drain completed for P1, so start delay the WALLY_DRAIN_S_P1_P2_DELAY
             * and once sleep is done start the P2 drain */
            if (is_completed && drain_details.drain_phase == WALLY_DRAIN_P1) {
                drain_details.drain_state = WALLY_DRAIN_S_P1_P2_DELAY;
                drain_details.drain_phase = WALLY_DRAIN_P2;
                s_index = 0;
            }
            /* Drain is not completed, but this drain iteration is completed.
             * So just start the sleep */
            if (is_completed == false) {
                drain_details.drain_state = WALLY_DRAIN_S_DELAY;
            }
        }
        drain_config.clients_drained = index;
        drain_config.drain_failed = fail_index;
        drain_config.last_drain_server = s_index;
        return;
    }

    if (drain_details.hist_count < WALLY_MAX_DRAIN_HIST) {
        drain_hist = (struct wallyd_drain_hist *) WALLY_MALLOC(sizeof(*drain_hist));
        memset (drain_hist, 0, sizeof(*drain_hist));
        drain_details.hist_count++;
    } else {
        drain_hist = wallyd_get_drain_hist_last();
        /* Remove the last element, fill the new data and insert it to head */
        LIST_REMOVE(drain_hist, hist);
        memset (drain_hist, 0, sizeof(*drain_hist));
    }

    /* Update the history table */
    drain_hist->cmd_time.tv_sec = drain_config.cmd_time.tv_sec;
    drain_hist->cmd_time.tv_usec = drain_config.cmd_time.tv_usec;
    drain_hist->clients_drained = index;
    drain_hist->drain_failed = fail_index;
    drain_hist->drain_requested = drain_config.drain_requested;
    drain_hist->force = drain_config.force;
    snprintf(drain_hist->domain, sizeof(drain_hist->domain), "%s", drain_config.domain);
    snprintf(drain_hist->port, sizeof(drain_hist->port), "%s", drain_config.port);
    drain_hist->percentage = drain_config.percentage;
    drain_hist->rate = drain_config.rate;
    drain_hist->drain_rate = drain_config.drain_rate;
    drain_hist->oper_drain = drain_config.oper_drain;
    drain_hist->drain_time_us = drain_config.drain_time_us;


    /* Log the final details */
    fprintf(fp_drain, "  Drain completed\n");
    gmtime_r(&(drain_hist->cmd_time.tv_sec), &tm);
    fprintf(fp_drain, "   Data & Time    :  %04d-%02d-%02d.%02d:%02d:%02d\n",
            tm.tm_year + 1900,
            tm.tm_mon + 1,
            tm.tm_mday,
            tm.tm_hour,
            tm.tm_min,
            tm.tm_sec);
    fprintf(fp_drain, "   Client drained :  %"PRId64"\n", drain_hist->clients_drained);
    fprintf(fp_drain, "   Drain failed   :  %"PRId64"\n", drain_hist->drain_failed);
    /* During drain, few of the marked clients could have been reset automatically, instead
     * of drain by wally. So the difference between drain_requested and clients_drained
     * gives the count of automatically reset clients  */
    fprintf(fp_drain, "   Auto reset     :  %"PRId64"\n", (drain_hist->drain_requested -
                (drain_hist->drain_failed + drain_hist->clients_drained)));
    fprintf(fp_drain, "   Drain request  :  %"PRId64"\n", drain_hist->drain_requested);
    fprintf(fp_drain, "   Domain         :  %s\n", strlen(drain_hist->domain)?drain_hist->domain:"NULL");
    fprintf(fp_drain, "   Port           :  %s\n", strlen(drain_hist->port)? drain_hist->port:"NULL");
    fprintf(fp_drain, "   Percentage     :  %d %%\n", drain_hist->percentage);
    fprintf(fp_drain, "   Rate           :  %d %%\n", drain_hist->rate);
    fprintf(fp_drain, "   Force          :  %d\n", drain_hist->force);
    fprintf(fp_drain, "   Oper-drain     :  %d\n", drain_hist->oper_drain);
    fprintf(fp_drain, "   Drain rate     :  %d clients/second\n", drain_hist->drain_rate);
    fprintf(fp_drain, "   Time taken     :  %"PRId64" us\n", drain_hist->drain_time_us);

    drain_details.drain_stats.clients_drained_lifetime += index;
    drain_details.drain_stats.drain_time_taken_us = drain_hist->drain_time_us;
    drain_details.drain_stats.drain_epoch_us =  ((US_IN_ONE_SECOND *
                drain_hist->cmd_time.tv_sec) + drain_hist->cmd_time.tv_usec);
    drain_details.drain_stats.clients_drained = drain_hist->clients_drained;
    drain_details.drain_stats.drain_failed = drain_hist->drain_failed;
    LIST_INSERT_HEAD(&(drain_details.drain_hist), drain_hist, hist);

EXIT:
    WALLYD_LOG(AL_NOTICE, " Drain completed. Client count  %d", index);

    /* Reset the local static variables */
    drain_status = WALLY_DRAIN_DESTROY;
    drain_details.two_phase_drain = false;
    drain_details.drain_phase = WALLY_DRAIN_P_NONE;
    drain_details.drain_state = WALLY_DRAIN_S_P1;
    index = 0;
    memset (&drain_config.cmd_time, 0, sizeof (drain_config.cmd_time));

    if (fp_drain) {
        fclose (fp_drain);
        fp_drain = NULL;
    }
}

/*
 * Wallyd cleanup thread handler. */
static void wally_cleanup_handler(int sock, short flags, void *cookie)
{
    uint32_t s_index = 0;
    static uint32_t count = 0;
    uint64_t now_us = 0;

    zthread_heartbeat(NULL);
    if (drain_status == WALLY_DRAIN_IN_PROGRESS) {
        if (drain_details.two_phase_drain) {
            now_us = epoch_us();
            drain_config.drain_time_us = now_us -
                ((US_IN_ONE_SECOND * drain_config.cmd_time.tv_sec) + drain_config.cmd_time.tv_usec);
            switch (drain_details.drain_state) {
                case WALLY_DRAIN_S_P1:
                case WALLY_DRAIN_S_P2:
                    /* P1/P2 drain has to be done. Break here and drain the clients */
                    break;
                case WALLY_DRAIN_S_DELAY:
                    /* Delay is requested. This is 1 second timer callback. So run the counter
                     * to give proper delay */
                    if (count %  wallyd_drain_delay != 0) {
                        count++;
                        return;
                    }
                    break;
                case WALLY_DRAIN_S_P1_P2_DELAY:
                    /* P1 drain completed. Start the Mid/P1 delay */
                    if (count % wallyd_drain_delay_mid != 0) {
                        count++;
                        return;
                    }
                    break;
                default:
                    break;
            }
            count++;
        }
        wallyd_drain_clients ();
    }

    if (drain_status == WALLY_DRAIN_DESTROY) {
        for (s_index = 0; s_index <= wally_server_max_index; s_index++) {
            wally_server_destroy_clients (wally_fohh_server_handle[s_index]);
        }
        /* Client structures freed. Reset the status to NONE */
        drain_status = WALLY_DRAIN_NONE;
        /* Drain completed. Reset the delay back to default */
        wallyd_drain_delay_mid = WALLY_DEF_DRAIN_DELAY_MID;
        wallyd_drain_delay = WALLY_DEF_DRAIN_DELAY;
        /* Notify wally_oper_thread that drain is completed. Operation mode FSM can
         * take action if needed */
        if (zevent_base_call(oper_mode_g.oper_base, wally_oper_drain_completed, NULL, 0) != 0)
        {
            WALLY_LOG(AL_ERROR, "Queueing wally_oper_drain_completed failed ");
            return ;
        }
    }

    return;
}

void *wally_cleanup_thread(struct zthread_info *zthread_arg, void *cookie)
{
    struct event_base *ev_base = event_base_new();
    struct event * timeout_thread_event = event_new(ev_base, -1,
                                                    EV_PERSIST,
                                                    wally_cleanup_handler, NULL);
    struct timeval tv;

    tv.tv_sec = 1;
    tv.tv_usec = 0;
    event_add(timeout_thread_event, &tv);

    zevent_base_dispatch(ev_base);

    return NULL;
}

/* Operational mode set command handler */
static int wallyd_oper_mode_action_command (struct zpath_debug_state *request_state,
                                             const char **query_values,
                                             int query_value_count,
                                             void *cookie)
{
    enum wally_oper_modes mode = S_NA;
    enum wally_oper_events events = E_MAX;
    enum wally_oper_sub_events se = SE_MAX;
    uint64_t delay_mid = 0, delay = 0;
    char *endptr = NULL;

    if (!query_values[0] && !query_values[1] && !query_values[2] &&
            !query_values[3] && !query_values[4]) {
        ZDP("Argument is required\n");
        return ZPATH_RESULT_BAD_ARGUMENT;
    }

    if (oper_mode_g.termination){
        ZDP("Wally termination is in progress. Cannot change mode or action\n");
        return ZPN_RESULT_NO_ERROR;
    }

    if (!oper_mode_g.is_active_done){
        ZDP("Wally initialization is in progress. Cannot accept configuration\n");
        return ZPN_RESULT_NO_ERROR;
    }
    if (oper_mode_g.oper_state_change_in_progress) {
        ZDP("\nOperational mode change is in progress. Cannot apply new mode/action.\n");
        return ZPN_RESULT_NO_ERROR;
    }

    if (!query_values[0] && !query_values[1]) {
            ZDP("'mode' or 'action' is required\n");
            ZDP("Allowed modes {%s, %s}\n", WALLY_ACTIVE, WALLY_OFFLINE);
            ZDP("Allowed actions {%s, %s, %s, %s}\n", WALLY_CLIENT_CONN, WALLY_CLIENT_DISCONN, WALLY_ORIGIN_CONN, WALLY_ORIGIN_DISCONN);
            return ZPN_RESULT_NO_ERROR;
    }

    if (query_values[0]) {
        ZDP("Received Operational mode   : %s\n", query_values[0]);
        if (strcmp (query_values[0], WALLY_ACTIVE) == 0) {
            mode = S_ACTIVE;
        } else if (strcmp (query_values[0], WALLY_OFFLINE) == 0) {
            mode = S_OFFLINE;
            if (oper_mode_g.offline) {
                ZDP("Already Offline mode is enabled\n");
                return ZPN_RESULT_NO_ERROR;
            }
        } else {
            ZDP("Allowed modes {%s, %s}\n", WALLY_ACTIVE, WALLY_OFFLINE);
            return ZPN_RESULT_NO_ERROR;
        }
    }

    if (query_values[1]) {
        ZDP("Received Operational action : %s\n", query_values[1]);
        if (strcmp (query_values[1], WALLY_CLIENT_CONN) == 0) {
            events = E_CLIENT_CONNECT;
        } else if (strcmp (query_values[1], WALLY_CLIENT_DISCONN) == 0) {
            events = E_CLIENT_DISCONNECT;
        } else if (strcmp (query_values[1], WALLY_ORIGIN_CONN) == 0) {
            events = E_ORIGIN_CONNECT;
        } else if (strcmp (query_values[1], WALLY_ORIGIN_DISCONN) == 0) {
            events = E_ORIGIN_DISCONNECT;
        } else {
            ZDP("Allowed actions {%s, %s, %s, %s}\n", WALLY_CLIENT_CONN,
                    WALLY_CLIENT_DISCONN, WALLY_ORIGIN_CONN, WALLY_ORIGIN_DISCONN);
            return ZPN_RESULT_NO_ERROR;
        }

        if (oper_mode_g.offline) {
            ZDP("Offline is active. Cannot execute %s\n", query_values[1]);
            return ZPN_RESULT_NO_ERROR;
        }
        if (oper_mode_g.is_leaf_wally && (
                    (events == E_ORIGIN_CONNECT) ||
                    (events == E_ORIGIN_DISCONNECT))) {
            ZDP("\nCannot disconnect/connect Origin in Leaf-wally\n");
            return ZPN_RESULT_NO_ERROR;
        }

    }

    if (query_values[2]) {
        ZDP("Received drain              : %s\n", query_values[2]);
        if (strcmp (query_values[2], "true") == 0) {
            se = SE_DRAIN_MANUAL;
        } else if (strcmp (query_values[2], "false") == 0) {
            se = SE_NO_DRAIN_MANUAL;
        } else {
            ZDP("Drain: %s is not allowed. Allowed drain value {true, false}\n",
                     query_values[2]);
            return ZPN_RESULT_NO_ERROR;
        }
    } else {
        se = SE_MANUAL;
    }

    if (query_values[3]) {
        ZDP("Received Wally drain delay-1  : %s\n", query_values[3]);
        delay_mid = strtoul(query_values[3], &endptr, BASE10);
        if (((endptr == query_values[3]) || (*endptr != '\0')) ||
                delay_mid < WALLY_DEF_MIN_DRAIN_DELAY_MID
                || delay_mid > WALLY_DEF_MAX_DRAIN_DELAY_MID) {
            ZDP("Invalid leaf drain delay-1 provided. Expected Range: [%d, %d]\n",
                    WALLY_DEF_MIN_DRAIN_DELAY_MID, WALLY_DEF_MAX_DRAIN_DELAY_MID);
            return ZPN_RESULT_NO_ERROR;
        }
    }

    if (query_values[4]) {
        ZDP("Received client drain delay-2 : %s\n", query_values[4]);
         delay = strtoul(query_values[4], &endptr, BASE10);
        if (((endptr == query_values[4]) || (*endptr != '\0')) ||
                 delay < WALLY_DEF_MIN_DRAIN_DELAY ||  delay > WALLY_DEF_MAX_DRAIN_DELAY) {
            ZDP("Invalid client drain delay-2 provided. Expected Range: [%d, %d]\n",
                    WALLY_DEF_MIN_DRAIN_DELAY, WALLY_DEF_MAX_DRAIN_DELAY);
            return ZPN_RESULT_NO_ERROR;
        }
    }

    if (se == SE_NO_DRAIN_MANUAL &&
            (delay_mid || delay)) {
        ZDP("'drain=false' is requested. Drain-delay is not required\n");
        return ZPN_RESULT_NO_ERROR;
    }

    /* Convert mode to event and get the final event to push to FSM */
    switch (mode) {
        case S_OFFLINE:
            if (events != E_MAX) {
                ZDP("\nAction commands are not allowed in Mode 'Offline'\n");
                return ZPN_RESULT_NO_ERROR;
            }
            events = E_OFFLINE;
            break;
        case S_ACTIVE:
            if (events == E_MAX) {
                events = E_ACTIVE;
            }
        default:
            if (events == E_CLIENT_CONNECT) {
                if (!oper_mode_g.client_disconnect){
                    ZDP("\nAlready 'Client-connect' is enabled\n");
                    return ZPN_RESULT_NO_ERROR;
                }
            } else if (events == E_ORIGIN_CONNECT) {
                if (!oper_mode_g.origin_disconnect){
                    ZDP("\nAlready Origin is connected \n");
                    return ZPN_RESULT_NO_ERROR;
                }
            } else if (events == E_CLIENT_DISCONNECT) {
                if (oper_mode_g.client_disconnect){
                    ZDP("\nAlready client access is disconnected \n");
                    return ZPN_RESULT_NO_ERROR;
                }
            } else if (events == E_ORIGIN_DISCONNECT) {
                if (oper_mode_g.origin_disconnect){
                    ZDP("\nAlready Origin is disconnected \n");
                    return ZPN_RESULT_NO_ERROR;
                }
                if (se != SE_DRAIN_MANUAL && (delay_mid || delay)) {
                    ZDP("By default drain is disabled for 'origin-disconnect'. Drain-delay configuration is not required\n");
                    return ZPN_RESULT_NO_ERROR;
                }
            }
            break;
    }

    if (events == E_ORIGIN_CONNECT || events == E_CLIENT_CONNECT) {
        if (se == SE_DRAIN_MANUAL) {
            ZDP("Drain is not allowed in 'client-connect'/'origin-connect'\n");
            return ZPN_RESULT_NO_ERROR;
        }
        if (delay_mid || delay) {
            ZDP("Drain delay configuration is not allowed in 'client-connect'/'origin-connect'\n");
            return ZPN_RESULT_NO_ERROR;
        }
    }

    if (delay_mid) {
        /* Verification passed, save the delay*/
        wallyd_drain_delay_mid = delay_mid;
    }
    if (delay) {
        /* Verification passed, save the delay*/
        wallyd_drain_delay = delay;
    }

    if (wally_oper_event_post(events, se, NULL)) {
        ZDP("Unable to set operational mode curl command\n");
    } else {
        ZDP("\nCommand posted successfully.\n");
        ZDP("Execute the command '/wally/oper/mode/status' to check the operational status.\n");
    }

    return ZPN_RESULT_NO_ERROR;
}

/* Operational mode status command handler */
static int wallyd_oper_mode_status_command (struct zpath_debug_state *request_state,
                                             const char **query_values,
                                             int query_value_count,
                                             void *cookie)
{
    struct tm tm;

    memset (&tm, 0, sizeof (tm));
    ZDP("\n\n   Operational-mode status \n");
    ZDP("============================= \n\n");
    ZDP("   Operational-mode               : %s \n",
            wally_oper_mode_strings[oper_mode_g.mode]);
    if (oper_mode_g.oper_state_change_in_progress) {
        ZDP("   Mode change                    : In progress\n");
    }
    /* drain status */
    if (oper_mode_g.drain_pending) {
        ZDP("   Drain status                   : Drain pending\n");
    } else if (oper_mode_g.drain_in_process) {
        ZDP("   Drain status                   : Drain in progress\n");
    }
    if (oper_mode_g.origin_disconnect || oper_mode_g.offline ||
            oper_mode_g.client_disconnect) {
        ZDP("   Manual command                 : Yes\n");
    }
    ZDP("   Operational event              : %s \n",
            wally_oper_event_strings[oper_mode_g.event]);
    ZDP("   Operational sub-event          : %s \n",
            wally_oper_sub_event_strings[oper_mode_g.sub_event]);
    gmtime_r(&(oper_mode_g.oper_time.tv_sec), &tm);
    ZDP("   Mode set Data & Time           : %04d-%02d-%02d.%02d:%02d:%02d\n\n",
        tm.tm_year + 1900,
        tm.tm_mon + 1,
        tm.tm_mday,
        tm.tm_hour,
        tm.tm_min,
        tm.tm_sec);

    if (oper_mode_g.prev_mode != S_NA) {
        ZDP("   Previous Operational-mode      : %s \n",
                wally_oper_mode_strings[oper_mode_g.prev_mode]);
        ZDP("   Previous Operational-event     : %s \n",
                wally_oper_event_strings[oper_mode_g.prev_event]);
        ZDP("   Previous Operational sub-event : %s \n",
                wally_oper_sub_event_strings[oper_mode_g.prev_sub_event]);

        memset (&tm, 0, sizeof (tm));
        gmtime_r(&(oper_mode_g.oper_prev_time.tv_sec), &tm);
        ZDP("   Previous mode set Data & Time  : %04d-%02d-%02d.%02d:%02d:%02d\n",
                tm.tm_year + 1900,
                tm.tm_mon + 1,
                tm.tm_mday,
                tm.tm_hour,
                tm.tm_min,
                tm.tm_sec);
    }

    if (oper_mode_g.client_disconnect_count ||
            oper_mode_g.origin_disconnect_count ||
            oper_mode_g.offline_count) {
        ZDP("\n\n   Command execution count \n");
        ZDP("========================== \n\n");
    }
    if (oper_mode_g.client_disconnect_count) {
        ZDP("   Client disconnect count        : %d \n",
            oper_mode_g.client_disconnect_count);
    }
    if (oper_mode_g.origin_disconnect_count) {
        ZDP("   Origin disconnect count        : %d \n",
            oper_mode_g.origin_disconnect_count);
    }
    if (oper_mode_g.offline_count) {
        ZDP("   Offline count                  : %d \n",
            oper_mode_g.offline_count);
    }

    if (oper_mode_g.rds_db_conn_fail_count == 0 &&
            oper_mode_g.trasaction_fail == 0 &&
            oper_mode_g.rds_failure == 0 &&
            oper_mode_g.schema_mismatch == 0 &&
            oper_mode_g.client_disconnect == 0 &&
            oper_mode_g.origin_disconnect == 0 &&
            oper_mode_g.offline == 0 &&
            oper_mode_g.fohh_origin_fail == 0 &&
            oper_mode_g.poll_failure == 0 &&
            oper_mode_g.recovery_in_progress == 0 &&
            oper_mode_g.recovery_failures == 0 &&
            oper_mode_g.trimmer_stop == 0 &&
            oper_mode_g.ldb_failure == 0 &&
            oper_mode_g.ldb_write_failure == 0) {
        /* No failures, return here */
        return ZPN_RESULT_NO_ERROR;
    }

    ZDP("\n\n   Active Failures\n");
    ZDP("====================== \n\n");
    if (oper_mode_g.rds_db_conn_fail_count) {
        ZDP(" - RDS DB connection failed : %d\n",
                oper_mode_g.rds_db_conn_fail_count);
    }
    if (oper_mode_g.trasaction_fail) {
        ZDP(" - Transaction failure : %"PRId64"\n",
                oper_mode_g.trasaction_fail);
    }
    if (oper_mode_g.rds_failure) {
        ZDP(" - RDS failure\n");
    }
    if (oper_mode_g.trimmer_stop) {
        ZDP(" - Trimmer stopped \n");
    }
    if (oper_mode_g.client_disconnect) {
        ZDP(" - Client disconnect\n");
    }
    if (oper_mode_g.origin_disconnect) {
        ZDP(" - Origin disconnect\n");
    }
    if (oper_mode_g.offline) {
        ZDP(" - Offline\n");
    }
    if (oper_mode_g.termination) {
        ZDP(" - Termination in progress\n");
    }
    if (oper_mode_g.recovery_in_progress) {
        ZDP(" - Recovery in progress\n");
    }
    if (oper_mode_g.recovery_failures) {
        ZDP(" - Recovery failures : %d\n",
            oper_mode_g.recovery_failures);
    }
    if (oper_mode_g.schema_mismatch) {
        ZDP(" - Schema mismatch : %"PRId64"\n",
                oper_mode_g.schema_mismatch);
    }
    if (oper_mode_g.ldb_failure) {
        ZDP(" - Local-DB failure\n");
    }
    if (oper_mode_g.fohh_origin_fail) {
        ZDP(" - FOHH server failure : %d\n",
                oper_mode_g.fohh_origin_fail);
    }
    if (oper_mode_g.poll_failure) {
        ZDP(" - RDS polling failed\n");
    }
    if (oper_mode_g.ldb_write_failure) {
        ZDP(" - Local postgres write failure\n");
    }
    ZDP("\n");

    return ZPN_RESULT_NO_ERROR;
}

/*
 * wally_drain_client_command
 * debug command handler to drain connections of a customer
 */
static int wally_drain_client_command(struct zpath_debug_state *request_state,
                                             const char **query_values,
                                             int query_value_count,
                                             void *cookie)
{
    /* Initialize to default values */
    uint64_t percentage = 0;
    int64_t port_num = 0;
    const char *domain = NULL;
    const char *port = NULL;
    uint64_t rate = WALLY_DEFAULT_DRAIN_PC;
    char filename[WALLY_LOG_FILE_LEN] = {0};
    char tmp_name[WALLY_LOG_FILE_LEN] = {0};
    uint32_t count = 0, wally_client = 0;
    uint32_t percent_drain = 0, time_to_complete = 0;
    int32_t force = 0;
    static uint32_t drain_rate = 0;
    int res = 0;
    char *endptr = NULL;
    uint32_t s_index = 0;

    /* TERMINATE and OFFLINE modes doesn't have clients. So drain command is not
     * required for these states */
    if (oper_mode_g.termination ||
            oper_mode_g.mode == S_OFFLINE) {
        ZDP("\nCannot drain in Operational mode Offline/Terminate\n");
        return ZPN_RESULT_NO_ERROR;
    }

    if (drain_status != WALLY_DRAIN_NONE) {
        ZDP("Previous drain command in progress \n");
        ZDP("\nCurrent Progress : %d %% \n",
                (uint32_t)(((double)drain_config.clients_drained/drain_config.drain_requested)* 100));
        ZDP("Remaining clients : %"PRId64" \n",
                (drain_config.drain_requested - drain_config.clients_drained));
        time_to_complete = (drain_config.time_to_complete - (drain_config.drain_time_us/US_IN_ONE_SECOND));
        ZDP("Approx time to complete : %d seconds\n\n", time_to_complete);
        return ZPN_RESULT_NO_ERROR;
    }

    /* validation: we need percentage to drain; safeguard against unintentional drain of all  */
    if (!query_values[0] || !query_values[1]) {
        ZDP("'percentage' and 'rate' are required\n");
        return ZPN_RESULT_NO_ERROR;
    }

    /* Extract the customer value; this value is optional now; absence means all customers. */
    if (query_values[0]) {
        percentage = strtoul(query_values[0], &endptr, BASE10);
        if (((endptr == query_values[0]) || (*endptr != '\0')) ||
                percentage <= 0 || percentage > WALLY_DEFAULT_DRAIN_PC) {
            ZDP("Invalid percentage provided. Expected Range: [1, 100]\n");
            return ZPN_RESULT_NO_ERROR;
        }
    }

    if (query_values[1]) {
        rate = strtoul(query_values[1], &endptr, BASE10);
        if (((endptr == query_values[1]) || (*endptr != '\0')) ||
                rate <= 0 || rate > WALLY_DEFAULT_DRAIN_PC) {
            ZDP("Invalid rate value provided. Expected Range: [1, 100]\n");
            return ZPN_RESULT_NO_ERROR;
        }
    }

    if (query_values[2]) {
        domain = query_values[2];
    }

    if (query_values[3]) {
        port = query_values[3];
        port_num = strtoul(query_values[3], &endptr, BASE10);
        if (((endptr == query_values[3]) || (*endptr != '\0')) ||
                port_num < 0 || port_num > WALLY_MAX_PORT_NUMBER) {
            ZDP("Invalid port number. Should be between 0 and %d\n", WALLY_MAX_PORT_NUMBER);
            return ZPN_RESULT_NO_ERROR;
        }
        if (!isdigit(port[0])) {
            /* Positive/Negative value is included in the port
             * like, +122, +0, -0, remove the symbol and proceed */
            port = (port + 1);
        }

    }

    if (query_values[4]) {
        force = strtoul(query_values[4], &endptr, BASE10);
        if (((endptr == query_values[4]) || (*endptr != '\0')) ||
                force < 0 || force > 1) {
            ZDP("Invalid force provided. Should be 0 or 1\n");
            return ZPN_RESULT_NO_ERROR;
        }
    }
    /* show the input values */
    ZDP("\nPercentage %"PRId64"%% domain %s port %s rate %"PRId64"%% force %d\n\n",
                percentage, domain? domain:"NULL", port? port:"NULL", rate, force);

    /* Get the number of matching clients for the given port and domain. This data needed to
     * identify the percentage of clients to be drained
     * */
    for (s_index = 0; s_index <= wally_server_max_index; s_index++) {
        res = wally_server_clients_count (wally_fohh_server_handle[s_index], domain, port,
                force, &count, &wally_client);
        if (res) {
            ZDP("Get client count failed\n");
            return res;
        }
    }

    /* Force option is enabled, so drain should reset all the clients including
     * leaf-wally */
    if (force) {
        count = count + wally_client;
    }

    if (count == 0) {

        if (wally_client) {
            ZDP("No clients matched for the given domain/port %s/%s\n",
                    domain? domain : "NULL", port? port : "NULL");
            ZDP("Wally client match : %d\n", wally_client);
            ZDP("Note: To reset wally-client give the full domain name\n\n");
        } else {
            ZDP("No clients matched for the given domain/port: %s/%s\n\n",
                    domain? domain:"NULL", port? port:"NULL");
        }
        return ZPN_RESULT_NO_ERROR;
    }

    ZDP("   Total clients matched                        : %d \n",
            count);
    percent_drain = (count * ((float)percentage/100));
    ZDP("   %3"PRId64" percent of clients                       : %d\n",
            percentage, percent_drain);

    if (percent_drain == 0) {
        ZDP("Percentage of client is 0, choose higher percentage. exiting\n");
        return ZPN_RESULT_NO_ERROR;
    }

    drain_rate = ceil(percent_drain * ((float)rate/100));
    ZDP("   Drain client per second at %3"PRId64" percent rate  : %d client/per-second\n",
            rate, drain_rate);

    if (drain_rate == 0) {
        ZDP("Client drain per second is 0, choose higher rate. exiting\n");
        return ZPN_RESULT_NO_ERROR;
    }
    time_to_complete = (uint32_t)ceil((double)percent_drain/drain_rate);
    ZDP("   Approx time to complete drain at %3"PRId64"%% rate  : %d seconds\n\n",
            rate, time_to_complete);

	count = 0;

    /* Mark the clients for drain. The reason for not reseting them immediately
     * 1. Reset happens in different thread
     * 2. if "rate" is configured, reset will happen in multiple batches
     *    so, in the mean time, any altration would have been happened in the list,
     *    like, new clients added or old clients removed or drained client reconnected.
     *    Drain command should not reset the same client again and again
     * 3. If marked client reset on its own then manual reset is not required
     * To resolve this, marking is needed. */
    for (s_index = 0; s_index <= wally_server_max_index; s_index++) {
        res = wally_server_mark_drain (wally_fohh_server_handle[s_index], domain, port,
                force, &count, percent_drain, true);
        if (res) {
            ZDP("Marking drain list failed\n");
            return res;
        }

        if (count == percent_drain) {
            /* All the required clients are marked, so break the loop.
             * If not continue the marking in next client */
            break;
        }
    }

	count = 0;

    /* Copy the data to global structure, which will be used by cleanup thread
     * to drain the clients
     * */
    memset (&drain_config, 0, sizeof(drain_config));
    if (domain) {
        snprintf(drain_config.domain, sizeof(drain_config.domain), "%s", domain);
    }
    if (port) {
        snprintf(drain_config.port, sizeof(drain_config.port), "%s", port);
    }
    drain_config.percentage = (uint32_t)percentage;
    drain_config.rate = (uint32_t)rate;
    drain_config.drain_rate = drain_rate;
    drain_config.drain_requested = percent_drain;
    drain_config.force = force;
    drain_config.last_drain_server = 0; /* Start the drain from first server */
    drain_config.oper_drain = false;
    drain_config.time_to_complete = time_to_complete;
    drain_details.two_phase_drain = false;
    drain_details.drain_phase = WALLY_DRAIN_P_NONE;

    /* Set to true */
    WALLYD_LOG(AL_NOTICE, " Drain started.");
    drain_status = WALLY_DRAIN_IN_PROGRESS;

    snprintf(filename, sizeof(filename), "%s", WALLY_DRAIN_LOG);
    wally_gen_filename(filename, tmp_name, sizeof(tmp_name));
    ZDP("Drain logs redirected to file: %s\n\n", tmp_name);
    ZDP("Drain happens in background. To check the status execute, \n");
    ZDP("     itascurl /wally/client/drain/stats\n\n");

	fp_drain = fopen(tmp_name, "w");
	if (fp_drain == NULL) {
		ZDP("File open failed for  %s: %s \n",
                filename, strerror(errno));
		return ZPN_RESULT_NO_ERROR;
	}

    return ZPN_RESULT_NO_ERROR;
}
/*
 * wallyd_drain_stats_command
 *
 * Drain statistics
 */
static int wallyd_drain_stats_command(struct zpath_debug_state *request_state,
                                             const char **query_values,
                                             int query_value_count,
                                             void *cookie)
{
    uint32_t index = 0;
    struct wallyd_drain_hist *drain_hist = NULL;
    struct wallyd_drain_hist *tmp_drain_hist = NULL;
    struct tm tm;

    memset (&tm, 0, sizeof (tm));

    ZDP("\nLifetime stats\n");
    ZDP("---------------\n");
    ZDP("\nNo clients drained                : %"PRId64"\n",
            drain_details.drain_stats.clients_drained_lifetime);
    ZDP("No of times drain command executed: %"PRId64"\n",
            drain_details.drain_stats.drain_cmd_count_lifetime);

    if (drain_status != WALLY_DRAIN_NONE) {
        ZDP("\nDrain in progress\n");
        ZDP("-----------------\n");

        gmtime_r(&(drain_config.cmd_time.tv_sec), &tm);
        ZDP("\nProgress : %d %% \n",
                (uint32_t)(((double)drain_config.clients_drained/drain_config.drain_requested)* 100));
        ZDP("   Data & Time    :  %04d-%02d-%02d.%02d:%02d:%02d\n",
                tm.tm_year + 1900,
                tm.tm_mon + 1,
                tm.tm_mday,
                tm.tm_hour,
                tm.tm_min,
                tm.tm_sec);
        ZDP("   Client drained :  %"PRId64"\n", drain_config.clients_drained);
        ZDP("   Drained failed :  %"PRId64"\n", drain_config.drain_failed);
        ZDP("   Drain request  :  %"PRId64"\n", drain_config.drain_requested);
        ZDP("   Domain         :  %s\n", strlen(drain_config.domain)? drain_config.domain:"NULL");
        ZDP("   Port           :  %s\n", strlen(drain_config.port)? drain_config.port:"NULL");
        ZDP("   Percentage     :  %d %%\n", drain_config.percentage);
        ZDP("   Rate           :  %d %%\n", drain_config.rate);
        ZDP("   Force          :  %d\n", drain_config.force);
        if (drain_details.two_phase_drain) {
            ZDP("   Drain Phase    :  %d\n", drain_details.drain_phase);
            ZDP("   Drain delay    :  %"PRId64" second(s) in-progress\n",
                    drain_details.drain_state == WALLY_DRAIN_S_P1_P2_DELAY?
                    wallyd_drain_delay_mid:wallyd_drain_delay);
            ZDP("   Drain delay 1  :  %"PRId64"\n", wallyd_drain_delay_mid);
            ZDP("   Drain delay 2  :  %"PRId64"\n", wallyd_drain_delay);
            ZDP("   Drain rate     :  %d clients per %"PRId64" second(s)\n", drain_config.drain_rate,
                     wallyd_drain_delay);
        } else {
            ZDP("   Drain rate     :  %d clients/second\n", drain_config.drain_rate);
        }
        ZDP("   Oper-drain     :  %s\n", drain_config.oper_drain?"Yes":"No");
        ZDP("   Time taken     :  %"PRId64" us\n", drain_config.drain_time_us);
        ZDP("   Remaining time :  %d seconds\n", (drain_config.time_to_complete -
                     (uint32_t)(drain_config.drain_time_us/US_IN_ONE_SECOND)));
    }

    if (LIST_EMPTY(&(drain_details.drain_hist))) {
        return ZPATH_RESULT_NO_ERROR;
    }

    ZDP("\nHistory (Last 5 results)\n");
    ZDP("------------------------\n");

    LIST_FOREACH_SAFE(drain_hist, &(drain_details.drain_hist), hist, tmp_drain_hist) {
        index++;
        ZDP("%d\n", index);
        gmtime_r(&(drain_hist->cmd_time.tv_sec), &tm);
        ZDP("   Data & Time    :  %04d-%02d-%02d.%02d:%02d:%02d\n",
                tm.tm_year + 1900,
                tm.tm_mon + 1,
                tm.tm_mday,
                tm.tm_hour,
                tm.tm_min,
                tm.tm_sec);
        ZDP("   Client drained :  %"PRId64"\n", drain_hist->clients_drained);
        ZDP("   Drain failed   :  %"PRId64"\n", drain_hist->drain_failed);
        ZDP("   Auto reset     :  %"PRId64"\n", (drain_hist->drain_requested -
                (drain_hist->drain_failed + drain_hist->clients_drained)));
        ZDP("   Drain request  :  %"PRId64"\n", drain_hist->drain_requested);
        ZDP("   Domain         :  %s\n", strlen(drain_hist->domain)?drain_hist->domain:"NULL");
        ZDP("   Port           :  %s\n", strlen(drain_hist->port)? drain_hist->port:"NULL");
        ZDP("   Percentage     :  %d %%\n", drain_hist->percentage);
        ZDP("   Rate           :  %d %%\n", drain_hist->rate);
        ZDP("   Force          :  %d\n", drain_hist->force);
        ZDP("   Drain rate     :  %d clients/second\n", drain_hist->drain_rate);
        ZDP("   Oper-drain     :  %s\n", drain_hist->oper_drain?"Yes":"No");
        ZDP("   Time taken     :  %"PRId64" us\n", drain_hist->drain_time_us);
    }
    return ZPATH_RESULT_NO_ERROR;
}

static int zpath_debug_wally_dump_poll_interval(struct zpath_debug_state *request_state,
                                         const char **query_values,
                                         int query_value_count,
                                         void *cookie)
{
    struct wally *wally = cookie;
    struct wp_db  *wp_db;

    if (!wally || !(wally->origins[0])) {
        ZDP("Unable to get Wally poll interval \n");
        return ZPATH_RESULT_NO_ERROR;
    }

    wp_db = wally->origins[0]->callout_handle;
    ZDP("Wally poll interval is  %"PRId64" us\n", wp_db->polling_interval_us);
    return ZPATH_RESULT_NO_ERROR;
}

static int zpath_debug_wally_dump_table_poll_interval(struct zpath_debug_state *request_state,
                                         const char **query_values,
                                         int query_value_count,
                                         void *cookie)
{
    struct wally *wally = cookie;
    struct wp_db  *wp_db;
    struct wp_table *t;

    if (!wally || !(wally->origins[0])) {
        ZDP("Unable to get Wally table poll interval \n");
        return ZPATH_RESULT_NO_ERROR;
    }

    if (query_values[0] == NULL) return ZPATH_RESULT_BAD_ARGUMENT;

    const char *table_name = query_values[0];
    wp_db = wally->origins[0]->callout_handle;

    t = argo_hash_lookup(wp_db->tables_by_argo_name, table_name, strlen(table_name), NULL);
    if (!t) {
         ZDP("Table name is not found in %s wally\n", wallyd->name);
         return ZPATH_RESULT_NO_ERROR;
    }

    if (t->xpoll_poll_interval_us == 0) {
        ZDP("Poll interval is not set to %s Default Wally poll interval is %"PRId64" us\n", table_name,wp_db->polling_interval_us);
    } else {
        ZDP("Poll interval for the table %s is %"PRId64" us\n",table_name,t->xpoll_poll_interval_us );
    }

    return ZPATH_RESULT_NO_ERROR;
}

static int zpath_debug_wally_update_table_poll_interval(struct zpath_debug_state *request_state,
                                         const char **query_values,
                                         int query_value_count,
                                         void *cookie)
{
    struct wally *wally = cookie;
    struct wp_db  *wp_db;
    struct wp_table *t;
    if (query_values[0] == NULL || query_values[1] == NULL ) return ZPATH_RESULT_BAD_ARGUMENT;

    if (!wally || !(wally->origins[0])) {
        ZDP("Unable to update Wally table poll interval \n");
        return ZPATH_RESULT_NO_ERROR;
    }

    const char *table_name = query_values[0];
    wp_db = wally->origins[0]->callout_handle;

    t = argo_hash_lookup(wp_db->tables_by_argo_name, table_name, strlen(table_name), NULL);
    if (!t) {
         ZDP("Table name is not found in %s wally\n", wallyd->name);
         return ZPATH_RESULT_NO_ERROR;
    }

    int poll_time_s = strtol(query_values[1], NULL, 10);
    if ( poll_time_s < MIN_POLL_TIME || poll_time_s > MAX_POLL_TIME ) {
        ZDP("Poll time should be in range of %d-%d\n",MIN_POLL_TIME,MAX_POLL_TIME);
        return ZPATH_RESULT_NO_ERROR;
    }

    ZPATH_RWLOCK_RDLOCK(&(wp_db->lock), __FILE__, __LINE__);
    t->xpoll_poll_interval_us = poll_time_s* 1000000L;
    ZPATH_RWLOCK_UNLOCK(&(wp_db->lock), __FILE__, __LINE__);

    return ZPATH_RESULT_NO_ERROR;
}

static int zpath_debug_wally_update_poll_interval(struct zpath_debug_state *request_state,
                                         const char **query_values,
                                         int query_value_count,
                                         void *cookie)
{
    struct wally *wally = cookie;
    struct wp_db  *wp_db;
    int poll_time_s;

    if (query_values[0] == NULL) return ZPATH_RESULT_BAD_ARGUMENT;
    if (!wally || !(wally->origins[0])) {
        ZDP("Unable to Update Wally table poll interval \n");
        return ZPATH_RESULT_NO_ERROR;
    }

    poll_time_s = strtol(query_values[0],NULL,10);
    wp_db = wally->origins[0]->callout_handle;

    if ( poll_time_s < MIN_POLL_TIME || poll_time_s > MAX_POLL_TIME ) {
        ZDP("Poll time is not correct it should be in range of %d-%d\n",MIN_POLL_TIME,MAX_POLL_TIME);
        return ZPATH_RESULT_NO_ERROR;
    }

    ZPATH_RWLOCK_RDLOCK(&(wp_db->lock), __FILE__, __LINE__);
    wp_db->polling_interval_us = poll_time_s*1000000L;
    ZPATH_RWLOCK_UNLOCK(&(wp_db->lock), __FILE__, __LINE__);

    return ZPATH_RESULT_NO_ERROR;
}

/* Wally operational mode init routine */
int wallyd_oper_mode_init(int is_leaf_wally, struct wally *wally)
{
    int res = 0;

    res = wally_oper_mode_init(wally_oper_drain_all_clients, is_leaf_wally, wally);
    if(res) {
        WALLYD_LOG(AL_ERROR, "Initializing wally global oper data failed: %s", zpath_result_string(res));
        return res;
    }

    res = zpath_debug_add_admin_command("Wally set operational mode",
            "/wally/oper/mode/set",
            wallyd_oper_mode_action_command,
            NULL,
            "mode", "Accepted modes - {active, offline}",
            "action", "Accepted actions - {origin-connect, origin-disconnect, client-connect, client-disconnect}",
            "drain", "default: 'true' for 'client-disconnect'/'offline' and 'false' for 'origin-disconnect'/'active'. Accepted values {true, false}",
            "drain-delay1", "Optional. Delay to be introduced after leaf-wally drain. Default 60 seconds. Min - 1 seconds and Max 300 seconds",
            "drain-delay2", "Optional. Delay for every drain iteration. default 5 seconds. Min - 1 seconds and Max 60 seconds",
            NULL);
    if(res) {
        WALLYD_LOG(AL_ERROR, "Initializing wally oper mode set curl command failed: %s", zpath_result_string(res));
        return res;
    }

    res = zpath_debug_add_read_command("Client connections detail",
            "/wally/oper/mode/status",
            wallyd_oper_mode_status_command,
            NULL,
            NULL);
    if(res) {
        WALLYD_LOG(AL_ERROR, "Initializing wally client list curl command failed: %s", zpath_result_string(res));
        return res;
    }

    return res;
}

int wallyd_drain_init()
{
    int res = 0;
    pthread_t thread;

    res = zpn_wally_drain_stats_init();
    if(res) {
        WALLYD_LOG(AL_ERROR, "Initializing wally drain stats failed: %s", zpath_result_string(res));
        return res;
    }

    res = zpath_debug_add_admin_command("Drain client connections",
            "/wally/client/drain",
            wally_drain_client_command,
            NULL,
            "percentage", "Percentage of connections to drain. Acceptable: [1, 100]",
            "rate",   "Percentage of the client to drain per second, Acceptable: [1, 100]",
            "domain", "<optional> Domain name of the client for drain",
            "port",   "<optional> Port of the client for drain",
            "force",  "<optional> Drain all matching clients. Acceptable: [0, 1], default 0",
            NULL);
    if(res) {
        WALLYD_LOG(AL_ERROR, "Initializing wally drain curl command failed: %s", zpath_result_string(res));
        return res;
    }

    res = zpath_debug_add_read_command("Client connections detail",
            "/wally/client/list",
            wallyd_client_list_command,
            NULL,
            "domain", "<optional> Domain name of the client to filter list",
            "port",   "<optional> Port of the client to filter list. Acceptable: [0, 65535]",
            NULL);
    if(res) {
        WALLYD_LOG(AL_ERROR, "Initializing wally client list curl command failed: %s", zpath_result_string(res));
        return res;
    }

    res = zpath_debug_add_read_command("Client drain statistics",
            "/wally/client/drain/stats",
            wallyd_drain_stats_command,
            NULL,
            NULL);
    if(res) {
        WALLYD_LOG(AL_ERROR, "Initializing wally drain stats curl command failed: %s", zpath_result_string(res));
        return res;
    }

    LIST_INIT(&(drain_details.drain_hist));

    res = zthread_create(&thread,
            wally_cleanup_thread,
            NULL,
            "wally_cleanup",
            wally_default_hb_timeout_s,             /* 60s thread watchdog */
            16*1024*1024,   /* 16 MB stack. */
            60*1000*1000,   /* 60s Statistics interval */
            NULL);
    if(res) {
        WALLYD_LOG(AL_ERROR, "Wally cleanup thread init failed");
        return res;
    }

    return res;
}

int poll_intverval_debug_cmds_init()
{
    char path[200];
    int res;

    if (wallyd->origins_count == 1) {

        snprintf(path, sizeof(path), "/wally/%s/poll_interval", wallyd->name);
        res = zpath_debug_add_read_command("Get wally poll interval",
                                      path,
                                      zpath_debug_wally_dump_poll_interval,
                                      wallyd,
                                      NULL,
                                      NULL);
        if (res) {
            /* This can fail from duplicates. Ignore it */
            //ZPATH_LOG(AL_ERROR, "Could not register debug command for %s", path);
        }

        snprintf(path, sizeof(path), "/wally/%s/update_poll_interval", wallyd->name);
        res = zpath_debug_add_admin_command("Update wally poll interval",
                                      path,
                                      zpath_debug_wally_update_poll_interval,
                                      wallyd,
                                      "time_s", "time in seconds",
                                      NULL);
        if (res) {
            /* This can fail from duplicates. Ignore it */
            //ZPATH_LOG(AL_ERROR, "Could not register debug command for %s", path);
        }

        snprintf(path, sizeof(path), "/wally/%s/table_poll_interval", wallyd->name);
        res = zpath_debug_add_read_command("Get wally table poll interval",
                                      path,
                                      zpath_debug_wally_dump_table_poll_interval,
                                      wallyd,
                                      "table", "table_name",
                                      NULL);
        if (res) {
            /* This can fail from duplicates. Ignore it */
            //ZPATH_LOG(AL_ERROR, "Could not register debug command for %s", path);
        }

        snprintf(path, sizeof(path), "/wally/%s/update_poll_table_interval", wallyd->name);
        res = zpath_debug_add_admin_command("Update wally poll table interval",
                                      path,
                                      zpath_debug_wally_update_table_poll_interval,
                                      wallyd,
                                      "table", "table_name",
                                      "time_s", "time in seconds",
                                       NULL);
        if (res) {
            /* This can fail from duplicates. Ignore it */
            //ZPATH_LOG(AL_ERROR, "Could not register debug command for %s", path);
        }
    }
    return ZPATH_RESULT_NO_ERROR;
}

/*
 * wallyd_final_termination
 *  This routine does the final exits/aborts.; this is the terminal path.
 *  PS: avoid any logging/locking here.
 */
ZDO_NOT_OPTIMIZE void wallyd_final_termination(enum zpath_termination_code tc, int ctx_thread_num)
{
    char msg_str[WALLY_BUFF_LEN] = { 0 };

    if (!zthread_is_valid_thread_number(ctx_thread_num)) {
        exit(1);
    }

    /* Storing it in memory for easy debugging, it can be viewed in debugger */
    memset(msg_str, 0, sizeof(msg_str));
    snprintf(msg_str, sizeof(msg_str), "Termination code: %s, thread %d (%s)",
                zpath_get_termination_code_string(tc), ctx_thread_num, zthread_get_thread_name(ctx_thread_num));

    /* Wallyd registers only for zthread callback handler, so it receives
     * only "zpath_tc_heartbeat_exceeded".
     * Other error codes are only required for zpath_lib callback and wallyd
     * uses the default handler for it, so these termination code
     * handlers are not needed here
     * */
    switch (tc) {
        case zpath_tc_heartbeat_exceeded:
            zthread_exit_or_abort();
            break;

        default:
            /* Only heartbeat termination handled in wallyd, other
             * termination codes will not be received here. */
            exit(1);
    }
}

/*
 * wallyd_update_stats_and_terminate
 *   updates the stats and table; and go ahead with termination
 */
static inline void wallyd_update_stats_and_terminate(void)
{

   /* Any final updates should be called here */

    /* Call the actual exit fns */
    wallyd_final_termination(g_wallyd_termination_ctx.tc, g_wallyd_termination_ctx.thread_num);
}

/* Exit handler for heartbeat failures */
void wallyd_heartbeat_exit_cb(int thread_number)
{
    fprintf(stdout, "Invoking heartbeat_exceeded callback for thread: %d(%s)\n",
                        thread_number, zthread_get_thread_name(thread_number));

    /* Return if already in exit path */
    if (g_wallyd_termination_ctx.tc != zpath_tc_invalid) {
        fprintf(stdout, "Wallyd already in exit path with term. code: %u\n", g_wallyd_termination_ctx.tc);
        return;
    }

    /* Disable heartbeat from now on */
    zthread_disable_heartbeat_monitor();

    /* Form the args and set context */
    g_wallyd_termination_ctx.tc = zpath_tc_heartbeat_exceeded;
    g_wallyd_termination_ctx.thread_num = thread_number;

    /* Invoke the handler and call the actual exit handler*/
    wallyd_update_stats_and_terminate();
}

int wally_postgres_config(const char *argv, enum wally_postgres_config_e type)
{
    char *endptr = NULL;
    uint64_t val = 0;

    if (argv == NULL) {
        fprintf(stderr, "Invalid TCP argument\n");
        return WALLY_RESULT_BAD_ARGUMENT;
    }

    val = strtoul(argv, &endptr, BASE10);

    switch (type) {
        case WALLY_TCP_KEEPALIVES_IDLE:
            if ((endptr == argv || (*endptr != '\0')) ||
                    val < WALLY_TCP_KEEPALIVES_IDLE_MIN || val > WALLY_TCP_KEEPALIVES_IDLE_MAX) {
                fprintf(stderr, "Invalid tcp_keepalives_idle. Expected range %d - %d\n\n",
                        WALLY_TCP_KEEPALIVES_IDLE_MIN, WALLY_TCP_KEEPALIVES_IDLE_MAX);
                return WALLY_RESULT_BAD_ARGUMENT;
            }
            tcp_keepalives_idle = val;
            break;

        case WALLY_TCP_KEEPALIVES_INTERVAL:
            if ((endptr == argv || (*endptr != '\0')) ||
                    val < WALLY_TCP_KEEPALIVES_INTERVAL_MIN || val > WALLY_TCP_KEEPALIVES_INTERVAL_MAX) {
                fprintf(stderr, "Invalid tcp_keepalives_interval. Expected range %d - %d\n\n",
                        WALLY_TCP_KEEPALIVES_INTERVAL_MIN, WALLY_TCP_KEEPALIVES_INTERVAL_MAX);
                return WALLY_RESULT_BAD_ARGUMENT;
            }
            tcp_keepalives_interval = val;
            break;

        case WALLY_TCP_KEEPALIVES_COUNT:
            if ((endptr == argv || (*endptr != '\0')) ||
                    val < WALLY_TCP_KEEPALIVES_COUNT_MIN || val > WALLY_TCP_KEEPALIVES_COUNT_MAX) {
                fprintf(stderr, "Invalid tcp_keepalives_count. Expected range %d - %d\n\n",
                        WALLY_TCP_KEEPALIVES_COUNT_MIN, WALLY_TCP_KEEPALIVES_COUNT_MAX);
                return WALLY_RESULT_BAD_ARGUMENT;
            }
            tcp_keepalives_count = val;
            break;

        case WALLY_TCP_USR_TIMEOUT:
            if ((endptr == argv || (*endptr != '\0')) ||
                    val < WALLY_TCP_USER_TIMEOUT_MIN || val > WALLY_TCP_USER_TIMEOUT_MAX) {
                fprintf(stderr, "Invalid tcp_user_timeout. Expected range %d - %d\n\n",
                        WALLY_TCP_USER_TIMEOUT_MIN, WALLY_TCP_USER_TIMEOUT_MAX);
                return WALLY_RESULT_BAD_ARGUMENT;
            }
            tcp_user_timeout = val;
            break;

        case WALLY_STATEMENT_TIMEOUT:
            if ((endptr == argv || (*endptr != '\0')) ||
                    val < WALLY_STATEMENT_OUT_MIN || val > WALLY_STATEMENT_OUT_MAX) {
                fprintf(stderr, "Invalid statement_timeout. Expected range %d - %d\n\n",
                        WALLY_STATEMENT_OUT_MIN, WALLY_STATEMENT_OUT_MAX);
                return WALLY_RESULT_BAD_ARGUMENT;
            }
            wally_gbl_cfg.statement_timeout = val;
            break;

        case WALLY_LOCK_TIMEOUT:
            if ((endptr == argv || (*endptr != '\0')) ||
                    val < WALLY_LOCK_TIMEOUT_MIN || val > WALLY_LOCK_TIMEOUT_MAX) {
                fprintf(stderr, "Invalid lock_timeout. Expected range %d - %d\n\n",
                        WALLY_LOCK_TIMEOUT_MIN, WALLY_LOCK_TIMEOUT_MAX);
                return WALLY_RESULT_BAD_ARGUMENT;
            }
            wally_gbl_cfg.lock_timeout = val;
            break;

        case WALLY_IDLE_TRANS_SESS_TIMEOUT:
            if ((endptr == argv || (*endptr != '\0')) ||
                    val < WALLY_IDLE_TRANS_SESS_TIMEOUT_MIN || val > WALLY_IDLE_TRANS_SESS_TIMEOUT_MAX) {
                fprintf(stderr, "Invalid idle_in_trans_session_timeout. Expected range %d - %d\n\n",
                        WALLY_IDLE_TRANS_SESS_TIMEOUT_MIN, WALLY_IDLE_TRANS_SESS_TIMEOUT_MAX);
                return WALLY_RESULT_BAD_ARGUMENT;
            }
            wally_gbl_cfg.idle_in_trans_session_timeout = val;
            break;

        case WALLY_IDLE_SESS_TIMEOUT:
            if ((endptr == argv || (*endptr != '\0')) ||
                    val < WALLY_IDLE_SESS_TIMEOUT_MIN || val > WALLY_IDLE_SESS_TIMEOUT_MAX) {
                fprintf(stderr, "Invalid idle_session_timeout. Expected range %d - %d\n\n",
                        WALLY_IDLE_SESS_TIMEOUT_MIN, WALLY_IDLE_SESS_TIMEOUT_MAX);
                return WALLY_RESULT_BAD_ARGUMENT;
            }
            wally_gbl_cfg.idle_session_timeout = val;
            break;

        default:
            return WALLY_RESULT_BAD_ARGUMENT;
    }
    return WALLY_RESULT_NO_ERROR;
}
void poll_rate_check_init(int poll_rate_timeout_min)
{
    struct wp_db  *wp_db = NULL;

    if (wallyd->origins_count == 1) {
        if (wallyd->origins[0]) {
            wp_db = wallyd->origins[0]->callout_handle;
            if (wp_db && poll_rate_timeout_min > 0) {
                wally_db_enable_poll_rate_check(wp_db , poll_rate_timeout_min);
            }
        }
    }
}

static void dont_dump_monitor_callback(const int64_t *config_value, int64_t impacted_gid)
{
    zpath_dont_dump_enable_config_override((uint64_t)*config_value);
}

static void setup_dont_dump() {
    static int64_t dont_dump_value = FEATURE_DONT_DUMP_DEFAULT;
    zpath_config_override_monitor_int(WALLY_FEATURE_DONT_DUMP,
                                      &dont_dump_value,
                                      dont_dump_monitor_callback,
                                      FEATURE_DONT_DUMP_DEFAULT,
                                      zpath_instance_global_state.current_config->gid,
                                      (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                      (int64_t)0);
}

int sub_main(int argc, char *argv[])
{

    struct wally_fohh_client *wally_fohh_client_handle = NULL;

    int instance_id = 0;
    int result;
    size_t i;
    char *stackpath = NULL;
    start_time = epoch_us();
    int poll_time_s = MIN_POLL_TIME;
    int poll_rate_timeout_min = WALLYD_DEFAULT_POLL_RATE_TIMEOUT_MIN;
    int config_writable = 0;
    struct wally_oper_data oper_data;

    memset (&oper_data, 0, sizeof(oper_data));
    zthread_init("wallyd", ZPATH_VERSION, "unknown", stackpath, NULL);
    zthread_do_stack_dump(&argc, argv);

    set_wally_app_state(wally_state_app_initilizing);
    /* Set operational mode to INIT state */
    wally_oper_init_fsm_cb();
    oper_data.event = E_INIT;
    wally_oper_handler(&oper_data);

    if (zpath_app_logging_parse_args(&argc, argv)) {
        exit(1);
    }

    for (i = 1; i < argc; i++) {
        /* Test for all one-word arguments. */
        if (strcmp(argv[i], "-endpoint") == 0) {
            config_endpoint = 1;
        } else if (strcmp(argv[i], "-debuglog") == 0) {
			/* coverity[CONSTANT_EXPRESSION_RESULT: FALSE] */
            wally_debug |=  (uint64_t)0xFFFFFFFFFFFFFFFF;
        } else if (strcmp(argv[i], "-version") == 0) {
            fprintf(stdout, "%s\n", ZPATH_VERSION);
            exit(0);
        } else if (strcmp(argv[i], "-daemon") == 0) {
            config_daemon = 1;
        } else if (strcmp(argv[i], "-fully_loaded") == 0) {
            config_fully_loaded = 1;
        } else if (strcmp(argv[i], "-dump_schema") == 0) {
            config_dump_schema = 1;
        } else if (strcmp(argv[i], "-test") == 0) {
            config_test = 1;
        } else if (strcmp(argv[i], "-cleanup") == 0) {
            do_cleanup = 1;
        } else if (strcmp(argv[i], "-disable_heartbeat_monitor") == 0) {
            zthread_disable_heartbeat_monitor();
        } else if (strcmp(argv[i], "-disable_wally_tcp_config") == 0) {
            /* Disable Wally TCP configuration and set kernel defaults */
            disable_wally_tcp_config = true;
        } else if (strcmp(argv[i], "-disable_operational_mode") == 0) {
            /* Disable Wally Operational mode FSM handler.*/
            disable_operational_mode = true;
        } else if (strcmp(argv[i], "-tcp_keepalives_idle") == 0) {
            i++;
            result = wally_postgres_config (argv[i], WALLY_TCP_KEEPALIVES_IDLE);
            if (result != WALLY_RESULT_NO_ERROR) {
                usage(argv[0], "Invalid value %s : %s\n", argv[i-1], argv[i]);
            }
        } else if (strcmp(argv[i], "-tcp_keepalives_interval") == 0) {
            i++;
            result = wally_postgres_config (argv[i], WALLY_TCP_KEEPALIVES_INTERVAL);
            if (result != WALLY_RESULT_NO_ERROR) {
                usage(argv[0], "Invalid value %s : %s\n", argv[i-1], argv[i]);
            }
        } else if (strcmp(argv[i], "-tcp_keepalives_count") == 0) {
            i++;
            result = wally_postgres_config (argv[i], WALLY_TCP_KEEPALIVES_COUNT);
            if (result != WALLY_RESULT_NO_ERROR) {
                usage(argv[0], "Invalid value %s : %s\n", argv[i-1], argv[i]);
            }
        } else if (strcmp(argv[i], "-tcp_user_timeout") == 0) {
            i++;
            result = wally_postgres_config (argv[i], WALLY_TCP_USR_TIMEOUT);
            if (result != WALLY_RESULT_NO_ERROR) {
                usage(argv[0], "Invalid value %s : %s\n", argv[i-1], argv[i]);
            }
        } else if (strcmp(argv[i], "-disable_ext_heartbeat") == 0) {
            wally_set_hb_timeout();
        } else if (strcmp(argv[i], "-synchronous_commit_off") == 0) {
            wally_postgres_synchronous_commit_off = 1;
        } else if (strcmp(argv[i], "-concurrent_write") == 0) {
            wally_postgres_concurrent_write = 1;
        } else if (strcmp(argv[i], "-db_init_read_nolock") == 0) {
            wally_db_init_read_nolock = 1;
        } else if (strcmp(argv[i], "-enable_gap_verified_read") == 0) {
            wally_gbl_cfg.enable_db_gvr_mode = 1;
        } else if (strcmp(argv[i], "-enable_row_process_thread") == 0) {
            wally_gbl_cfg.enable_row_process_thread = 1;
        } else if (strcmp(argv[i], "-optimize_write") == 0) {
            wallyd_conns_per_db = 10;
            wally_postgres_synchronous_commit_off = 1;
            wally_postgres_concurrent_write = 1;
            WALLYD_LOG(AL_INFO, "(wallyd_conns_per_db: %d, wally_postgres_synchronous_commit_off: %d, wally_postgres_concurrent_write: %d",
                                    wallyd_conns_per_db, wally_postgres_synchronous_commit_off, wally_postgres_concurrent_write);
        } else if (strcmp(argv[i], "-enable_batch_write") == 0) {
            wally_gbl_cfg.enable_batch_write = 1;
            WALLYD_LOG(AL_INFO,"Batch write feature enabled");
        } else if (strcmp(argv[i], "-psql_debug") == 0) {
            wally_debug |= WALLY_DEBUG_POSTGRES_SQL_BIT;
        } else if (strcmp(argv[i], "-exportable_core_dumps") == 0) {
            zpath_dont_dump_enable_command_line();
        } else if (strcmp(argv[i], "-disable_version_check") == 0) {
            set_wally_client_incompatible_version_check_disable_cli();
        } else {
            /* Test for all two-word arguments. */
            if ((i + 1) >= argc) {
                /* There is not a pair of words... */
                usage(argv[0], "Improper argument- may be missing second field: %s\n", argv[i]);
                /* Exits */
            }
            if (strcmp(argv[i], "-instance") == 0) {
                instance_id = strtoul(argv[i+1], NULL, 0);
                i++;
            } else if (strcmp(argv[i], "-local_file") == 0) {
                i++;
                local_file = argv[i];
            } else if (strcmp(argv[i], "-itasca_logs") == 0) {
                i++;
                itasca_logs_port_he = atoi(argv[i]);
            } else if (strcmp(argv[i], "-schema_dir") == 0) {
                i++;
                config_read_js = argv[i];
            } else if (strcmp(argv[i], "-dbhost") == 0) {
                i++;
                dbhost = argv[i];
            } else if (strcmp(argv[i], "-stackpath") == 0) {
                i++;
                stackpath = argv[i];
            } else if (strcmp(argv[i], "-wally_threads") == 0) {
				i++;
				wally_gbl_cfg.wally_threads = strtoul(argv[i], NULL, 0);
				if (wally_gbl_cfg.wally_threads <  WALLY_MIN_TABLE_THREADS
						|| wally_gbl_cfg.wally_threads > WALLY_MAX_TABLE_THREADS) {
					usage(argv[0], "Invalid value wally_threads:%"PRId64", Expected range [%d %d]\n",
							wally_gbl_cfg.wally_threads, WALLY_MIN_TABLE_THREADS, WALLY_MAX_TABLE_THREADS);
				}
            } else if (strcmp(argv[i], "-fohh_threads") == 0) {
                i++;
                fohh_threads = strtoul(argv[i], NULL, 0);;
            }
             else if (strcmp(argv[i], "-load_tables") == 0) {
                i++;
                if (strcmp(argv[i], "static") == 0) {
                    load_tables = static_tables;
                } else if (strcmp(argv[i], "non-static") == 0) {
                    load_tables = non_static_tables;
                } else if (strcmp(argv[i], "all") == 0) {
                    load_tables = all_tables;
                } else {
                    usage(argv[0], "Unrecognized  value %s : %s\n", argv[i-1], argv[i]);
                }
            } else if (strcmp(argv[i], "-max_zhash_table_size_log2") == 0) {
                i++;
                zhash_max_table_size_log2_default = strtol(argv[i], NULL, 0);
                WALLYD_LOG(AL_WARNING, "zhash_max_table_size_log2_default set to %d.", zhash_max_table_size_log2_default);
            } else if (strcmp(argv[i], "-dbconns") == 0) {
                i++;
                wallyd_conns_per_db = strtol(argv[i], NULL, 0);
                WALLYD_LOG(AL_INFO, "wallyd_conns_per_db set to %d", wallyd_conns_per_db);
            } else if (strcmp(argv[i], "-db_read_batch_size") == 0) {
                char *endptr = NULL;
                int64_t batch_size = 0;
                i++;
                batch_size = strtol(argv[i], &endptr, BASE10);
                if (*endptr)
                {
                    usage(argv[0], "Invalid batch size value %s : %s\n", argv[i-1], argv[i]);
                }
                if ((batch_size < WALLYD_DB_QUERY_MIN_BATCH_SIZE) ||
                        (batch_size > WALLYD_DB_QUERY_MAX_BATCH_SIZE))
                {
                    usage(argv[0], "Unrecognized  value %s : %s\n", argv[i-1], argv[i]);
                } else if (batch_size < WP_MAX_ROWS )
                {
                    WALLYD_LOG(AL_WARNING, "Configured batch size is less than default value (%d).", WP_MAX_ROWS);
                    fprintf(stdout, "Configured batch size is less than default value (%d)\n", WP_MAX_ROWS);
                }
                wally_gbl_cfg.wally_postgres_db_query_batch_size = (int) batch_size;
                WALLYD_LOG(AL_INFO, "wallyd_db_query_batch_size set to %d", wally_gbl_cfg.wally_postgres_db_query_batch_size);
            } else if (strcmp(argv[i], "-timer_interval_us") == 0) {
                i++;
                timer_interval_us =  atoi(argv[i]);
                if (timer_interval_us < 1000) {
                    WALLYD_LOG(AL_WARNING, "Timer interval is too aggressive, resetting to default value of %d", WP_POLL_INTERVAL_US);
                    timer_interval_us = 1000;
                }
                WALLYD_LOG(AL_WARNING, "Timer interval set to run every %d us.", timer_interval_us);
            } else if (strcmp(argv[i], "-cleanup_delay_at_start_min") == 0) {
                i++;
                int64_t val =  atoi(argv[i]);
                if ((val * 60LL * 1000000LL) > cleanup_delay_at_start_us) {
                    WALLYD_LOG(AL_WARNING, "Cleanup delay at start is %"PRId64" minutes", val);
                    cleanup_delay_at_start_us = (val * 60LL * 1000000LL);
                }
            } else if (strcmp(argv[i], "-poll_rate_timeout_min") == 0) {
                i++;
                int64_t val =  strtol(argv[i], NULL, BASE10);
                if (val == 0) {
                    WALLYD_LOG(AL_WARNING, "Disabling poll rate reboot functionality");
                    val = 0;
                } else if (val < WALLYD_MIN_POLL_RATE_TIMEOUT_MIN || val > WALLYD_MAX_POLL_RATE_TIMEOUT_MIN ) {
                    WALLYD_LOG(AL_WARNING, "Poll rate time out value should be in range %d-%d min , 0 to disable it. resetting to default value %d",
                                 WALLYD_MIN_POLL_RATE_TIMEOUT_MIN , WALLYD_MIN_POLL_RATE_TIMEOUT_MIN,WALLYD_DEFAULT_POLL_RATE_TIMEOUT_MIN);
                    val = WALLYD_DEFAULT_POLL_RATE_TIMEOUT_MIN;
                }
                WALLYD_LOG(AL_INFO, "wallyd poll rate timeout is %"PRId64" minutes",val);
                poll_rate_timeout_min = val;
            } else if (strcmp(argv[i], "-statement_timeout") == 0) {
                i++;
                result = wally_postgres_config (argv[i], WALLY_STATEMENT_TIMEOUT);
                if (result != WALLY_RESULT_NO_ERROR) {
                    usage(argv[0], "invalid value %s : %s\n", argv[i-1], argv[i]);
                }
            } else if (strcmp(argv[i], "-lock_timeout") == 0) {
                i++;
                result = wally_postgres_config (argv[i], WALLY_LOCK_TIMEOUT);
                if (result != WALLY_RESULT_NO_ERROR) {
                    usage(argv[0], "invalid value %s : %s\n", argv[i-1], argv[i]);
                }
            } else if (strcmp(argv[i], "-idle_session_timeout") == 0) {
                i++;
                result = wally_postgres_config (argv[i], WALLY_IDLE_SESS_TIMEOUT);
                if (result != WALLY_RESULT_NO_ERROR) {
                    usage(argv[0], "invalid value %s : %s\n", argv[i-1], argv[i]);
                }
            } else if (strcmp(argv[i], "-write_batch_size") == 0) {
                i++;
                int64_t val =  strtol(argv[i], NULL, BASE10);
                if (val < WALLY_MIN_WRITE_BATCH_SIZE || val > WALLY_MAX_WRITE_BATCH_SIZE ) {
                    WALLYD_LOG(AL_WARNING, "Batch size should be in range %d-%d , resetting to default value %d",
                                 WALLY_MIN_WRITE_BATCH_SIZE, WALLY_MAX_WRITE_BATCH_SIZE, WALLY_DEFAULT_WRITE_BATCH_SIZE);
                    val = WALLY_DEFAULT_WRITE_BATCH_SIZE;
                }
                wally_gbl_cfg.write_batch_size =  val;
                WALLYD_LOG(AL_INFO, "Wally write batch size is %"PRId64" ",val);
            } else if (strcmp(argv[i], "-idle_in_trans_session_timeout") == 0) {
                i++;
                result = wally_postgres_config (argv[i], WALLY_IDLE_TRANS_SESS_TIMEOUT);
                if (result != WALLY_RESULT_NO_ERROR) {
                    usage(argv[0], "invalid value %s : %s\n", argv[i-1], argv[i]);
                }
            } else {
                usage(argv[0], "Unrecognized argument: %s\n", argv[i]);
                /* Exits */
            }
        }
    }

    /* Enable extended HB timeout values */
    wally_enable_extended_hb_timeout();

    if (config_daemon) {
        int fd;
        fprintf(stderr, "Daemonizing\n");
        /* Take off... */
        switch (fork()) {
		case 0:
			break;
		case -1:
            WALLYD_LOG(AL_ERROR, "fork failed: %s", strerror(errno));
            return WALLY_RESULT_ERR;
		default:
			/* exit interactive session */
			exit(0);
        }
        if(setsid() == -1) {
            WALLYD_LOG(AL_ERROR, "setsid failed: %s", strerror(errno));
            return WALLY_RESULT_ERR;
        }
        if ((fd = open("/dev/null", O_RDWR, 0)) != -1) {
            (void)dup2(fd, STDIN_FILENO);
            (void)dup2(fd, STDOUT_FILENO);
            (void)dup2(fd, STDERR_FILENO);
            if (fd > 2)
                (void)close(fd);
        }
    }

    if (itasca_logs_port_he) {
        update_itasca_logs_port(itasca_logs_port_he);
    }

    fohh_set_state_stats_logging(1);
    zthread_termination_handler_init(wallyd_heartbeat_exit_cb);

    /* Loading local_db/i_0 and g-wally RDS starts here */
    set_wally_app_state(wally_state_tables_loading);

    /* If we are replicating the global DB, then we pay attention to
     * remote_host configuration for this DB when we initialize the
     * global DB. */
    struct zpath_app_init_params app_params;
    zpath_app_init_params_default(&app_params);
    app_params.instance_id = instance_id;
    app_params.zpath_local_config_file = local_file;
    app_params.local_db_hostname = dbhost;
    app_params.role_name = "wallyd";
    app_params.fohh_thread_count = fohh_threads;
    app_params.fohh_watchdog_s = wally_default_hb_timeout_s;
    app_params.system_start_us = start_time;
    app_params.is_endpoint = config_endpoint;  // WallyD is NEVER an endpoint in production

    result = zpath_app_init(&app_params);
    if (result) {
        fprintf(stderr, "Error: Could not initalize\n");
        sleep(1);
        exit(1);
    }
    result = zpn_wally_global_stats_init();
    if(result) {
        WALLYD_LOG(AL_ERROR, "Initializing wally stats failed: %s", zpath_result_string(result));
        return result;
    }


    wt_description = get_wt_desc();
    argo_field_description_description = argo_register_global_structure(ARGO_FIELD_DESCRIPTION_HELPER);

    WALLYD_LOG(AL_NOTICE, "WALLYD version: %s", ZPATH_VERSION);
    zthread_init("wallyd", ZPATH_VERSION, ZPATH_LOCAL_FULL_NAME ? ZPATH_LOCAL_FULL_NAME : "unknown", stackpath, NULL);

    /* If we are replicating global config... */
    if (ZPATH_LOCAL_WALLYD_IS_GLOBAL) {
        wallyd = zpath_global_wally;
        wallyd_slave_db = zpath_global_slave_db;
        wallyd_remote_db = zpath_global_remote_db;
    } else {
        void *postgres_handle;

        if (ZPATH_LOCAL_WALLYD_FOHH_REMOTE_HOST) {
            config_writable = 1;
        } else {
            config_writable = 0;
        }

        if (!ZPATH_LOCAL_WALLYD_DB_NAME || strlen(ZPATH_LOCAL_WALLYD_DB_NAME) == 0) {
            ZPATH_LOG(AL_CRITICAL, "Want to export DB, but no DB name specified in local_db");
            return ZPATH_RESULT_ERR;
        }

        wallyd = wally_create(ZPATH_LOCAL_WALLYD_DB_NAME, config_endpoint, zpath_debug_wally_endpoints_init, NULL, NULL, NULL);
        if (!wallyd) {
            ZPATH_LOG(AL_CRITICAL, "Could not create wallyd\n");
            return ZPATH_RESULT_ERR;
        }

        wally_set_wallyd(wallyd);
        zpath_config_override_monitor_int(WALLY_FEATURE_DROP_INCOMPATIBLE_VERSION,
                                          &wallyd->drop_incompatible_version,
                                          NULL,
                                          DEFAULT_DROP_INCOMPATIBLE_VERSION,
                                          (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                          (int64_t)0);
        zpath_config_override_monitor_int(WALLY_FEATURE_LEAF_RECONNECT_TABLE_REG_PRIORITY,
                                          &wallyd->wally_tbl_intrst_reg_prio,
                                          NULL,
                                          LEAF_WALLY_RECONN_TABLE_REGN_PRIORITY_DEFAULT,
                                          (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                          (int64_t)0);
        setup_dont_dump();
        ZPATH_LOG(AL_NOTICE, "Creating Wally, using postgres DB %s as slave", ZPATH_LOCAL_WALLYD_DB_NAME);

        if (!ZPATH_LOCAL_WALLYD_FOHH_REMOTE_HOST) {
             if ( ZPATH_LOCAL_WALLYD_POLL_INTERVAL_SECONDS ) {
                 poll_time_s = ZPATH_LOCAL_WALLYD_POLL_INTERVAL_SECONDS ;
                 if ( poll_time_s < MIN_POLL_TIME || poll_time_s > MAX_POLL_TIME ) {
                    WALLYD_LOG(AL_ERROR, "Configured poll time is not within range.\
                               It should be in between min poll time %d and max poll time %d.\
                               Using default value %d",MIN_POLL_TIME,MAX_POLL_TIME,MIN_POLL_TIME);
                    poll_time_s = MIN_POLL_TIME;
                 }
             }
             if (ZPATH_LOCAL_WALLYD_POLL_INTERVAL_TABLES_SECONDS_COUNT) {
                 result = zpath_local_update_wally_table_poll_time();
                 if ( result != WALLY_RESULT_NO_ERROR ) {
                     WALLYD_LOG(AL_ERROR, "Failed to configure wally poll time ");
                     return WALLY_RESULT_ERR;
                 }
             }
        }

        char *postgres_username = ZPATH_LOCAL_WALLYD_POSTGRES_USERNAME ? ZPATH_LOCAL_WALLYD_POSTGRES_USERNAME : "postgres";
        postgres_handle =
            wally_postgres_create_with_schema(NULL,                                 /* Cookie for calling back into wally. */
                                              ZPATH_LOCAL_WALLYD_POSTGRES_HOST,     /* Host to connect to for database.    */
                                              postgres_username,                    /* User to connect as for database.    */
                                              ZPATH_LOCAL_WALLYD_DB_NAME,           /* Database name to use to connect.    */
                                              "wallyd_postgres_db",                 /* Name
                                                                                     * to give the thread running this DB. Chosen this way to be the same
                                                                                     * across all wallyd's. */
                                              ZPATH_LOCAL_WALLYD_POSTGRES_PASSWORD, /* Password to use for connection. */
                                              ZPATH_LOCAL_WALLYD_POSTGRES_SCHEMA,   /* Schema to look for tables. */
                                              wallyd_conns_per_db,
                                              1,              /* Row writable! */
                                              config_writable,/* Whether we can
                                                               * write to slave
                                                               * DB- really
                                                               * table_alterable. */
                                              !config_writable, /* Is this a true
                                                                 * origin */
                                              wally_is_endpoint(wallyd),
                                              config_writable?
                                              0:poll_time_s*1000000L);     /* Polling interval-
                                                               * don't poll if its
                                                               * a slave DB,
                                                               * otherwise every
                                                               * 1s */
        if (!postgres_handle) {
            ZPATH_LOG(AL_CRITICAL, "wally_postgres_create failed for database %s (It might not exist?)", ZPATH_LOCAL_WALLYD_DB_NAME);
            return ZPATH_RESULT_ERR;
        }


        wallyd_slave_db = wally_add_origin_1(wallyd,
                                            ZPATH_LOCAL_WALLYD_DB_NAME,
                                            postgres_handle,
                                            wally_db_register_for_index,
                                            wally_db_deregister_for_index,
                                            wally_db_set_cookie,
                                            wally_db_get_status,
                                            wally_db_add_table,
                                            config_writable ? wally_db_set_min_sequence : NULL,
                                            wally_db_dump_state,
                                            do_cleanup ? wally_postgres_cleanup : NULL,
                                            wally_db_cmd_execute,
                                            1);
        if (!wallyd_slave_db) {
            ZPATH_LOG(AL_ERROR, "wally_add_origin for postgres db %s failed", ZPATH_LOCAL_WALLYD_DB_NAME);
            return ZPATH_RESULT_ERR;
        }



        if (ZPATH_LOCAL_WALLYD_FOHH_REMOTE_HOST) {
            wally_fohh_client_handle = wally_fohh_client_create(wallyd,
                                                                NULL,
                                                                ZPATH_LOCAL_WALLYD_FOHH_REMOTE_HOST,
                                                                ZPATH_LOCAL_WALLYD_FOHH_REMOTE_HOST, // SNI
                                                                NULL,
                                                                htons(ZPATH_LOCAL_WALLYD_FOHH_REMOTE_PORT),
                                                                NULL);
            if (!wally_fohh_client_handle) {
                WALLYD_LOG(AL_ERROR, "Could not create wally_fohh client for accessing remote server %s:%d",
                           ZPATH_LOCAL_WALLYD_FOHH_REMOTE_HOST,
                           ZPATH_LOCAL_WALLYD_FOHH_REMOTE_PORT);
                return WALLY_RESULT_ERR;
            }

            /* Add origin DB to wally. */
            wallyd_remote_db = wally_add_origin(wallyd,
                                                "fohh",
                                                wally_fohh_client_handle,
                                                wally_fohh_register_for_index,
                                                wally_fohh_deregister_for_index,
                                                wally_fohh_set_cookie,
                                                wally_fohh_get_status,
                                                wally_fohh_add_table,
                                                NULL, // set_sequence
                                                wally_fohh_dump_state,
                                                1);

            if (!wallyd_remote_db) {
                WALLYD_LOG(AL_ERROR,
                           "failed registration of remote origin database to %s with Wally.",
                           ZPATH_LOCAL_WALLYD_FOHH_REMOTE_HOST);
                return WALLY_RESULT_ERR;
            }

            if (wallyd->origins_count > 1) {  //Applicable only to leaf wallys
                result = zpath_table_init(wallyd, wallyd_slave_db,
                                           wallyd_remote_db, 1);
                if (result != ZPATH_RESULT_NO_ERROR) {
                    WALLYD_LOG(AL_ERROR, "Failed to initialize zpath_table with error code: %s", wally_error_strings[result]);
                    return WALLY_RESULT_ERR;
                }
            }
        }
    }

    if (disable_operational_mode == false) {
        result = wallyd_oper_mode_init (config_writable, wallyd);
        if (result) {
            WALLYD_LOG(AL_ERROR, "drain_conn: Could not init wally oper-mode infra: %s", zpath_result_string(result));
            return result;
        }
    }

    do_wts_done = sizeof(wts) / sizeof(struct wt);
    WALLYD_LOG(AL_NOTICE, "Total table to do wts is %d",do_wts_done);
    /* Load Static tables only for this wally */
    if (load_tables == static_tables) {
        WALLYD_LOG(AL_NOTICE, "Running Wally with Static Tables only");
        for (i = 0; i < (sizeof(wts) / sizeof(struct wt)); i++) {
            if (wts[i].is_static || !strncmp(wts[i].table_name, "zpath_table", strnlen(wts[i].table_name,TABLE_NAME_MAX_SIZE))) {
                /* Skip tables we may have already created for the case of
                * global wallyd */
                if (config_dump_schema) {
                    dump_schema(&(wts[i]));
                }

                if (wts[i].skip_for_global_wally && ZPATH_LOCAL_WALLYD_IS_GLOBAL) {
					__sync_sub_and_fetch_4(&(do_wts_done),1);
					continue;
				}

                result = do_wts(&(wts[i]));
                if (result) {
                    WALLYD_LOG(AL_ERROR, "Could not init table info for %s", wts[i].table_name);
                    return WALLY_RESULT_ERR;
                }
            }
        }
    /* Load Non Static tables only for this wally */
    } else if (load_tables == non_static_tables) {
        WALLYD_LOG(AL_NOTICE, "Running Wally with Non Static Tables only");
        for (i = 0; i < (sizeof(wts) / sizeof(struct wt)); i++) {
            if (!wts[i].is_static || !strncmp(wts[i].table_name, "zpath_table", strnlen(wts[i].table_name,TABLE_NAME_MAX_SIZE))) {
                /* Skip tables we may have already created for the case of
                * global wallyd */
                if (config_dump_schema) {
                    dump_schema(&(wts[i]));
                }

                if (wts[i].skip_for_global_wally && ZPATH_LOCAL_WALLYD_IS_GLOBAL) {
					__sync_sub_and_fetch_4(&(do_wts_done),1);
					continue;
				}

                result = do_wts(&(wts[i]));
                if (result) {
                    WALLYD_LOG(AL_ERROR, "Could not init table info for %s", wts[i].table_name);
                    return WALLY_RESULT_ERR;
                }
            }
        }
    } else {
        WALLYD_LOG(AL_NOTICE, "Running Wally with all TabLes (Static + Non Static)");
        for (i = 0; i < (sizeof(wts) / sizeof(struct wt)); i++) {
            /* Skip tables we may have already created for the case of
            * global wallyd */
            if (config_dump_schema) {
                dump_schema(&(wts[i]));
            }

            if (wts[i].skip_for_global_wally && ZPATH_LOCAL_WALLYD_IS_GLOBAL) {
				__sync_sub_and_fetch_4(&(do_wts_done),1);
				continue;
			}

            result = do_wts(&(wts[i]));
            if (result) {
                WALLYD_LOG(AL_ERROR, "Could not init table info for %s", wts[i].table_name);
                return WALLY_RESULT_ERR;
            }
        }
    }
    while ( do_wts_done ) {
        WALLYD_LOG(AL_NOTICE, "Wallyd waiting for wts to be fully enqueued;do_wts_done is %d",do_wts_done);
        sleep(1);
    }

    // Read schema from file system, if it is there and has entries.
    read_schema();

    while ( do_wts_done ) {
        WALLYD_LOG(AL_NOTICE, "Wallyd waiting for wts to be fully enqueued in read_schema;do_wts_done is %d",do_wts_done);
        sleep(1);
    }

    if (wallyd != zpath_global_wally) {
        result = zpath_debug_wally_add(wallyd, ZPATH_WALLY_STATS_INTERVAL_US);
    }
    zpath_debug_wally_register_registrant_stats(wallyd);

    if (FULLY_LOADED) {
        while (responses_waiting) {
            if (responses_waiting) {
                WALLYD_LOG(AL_NOTICE, "Waiting for %d tables to be read...for load_tables = %s", (int)responses_waiting, load_tables_state_str[load_tables]);
            }
            sleep(1);
        }
        WALLYD_LOG(AL_NOTICE, "Initial DB read complete");
    }
    /* Tables are loaded and set state appropriately */
    set_wally_app_state(wally_state_tables_loaded);

    /* Add a command to display wally loaded table status */
    zpath_debug_add_read_command("Display Static tables loaded by wally.",
                            "/wally/show_static_tables",
                            zpath_debug_wally_display_static_tables,
                            NULL,
                            NULL);

    zpath_debug_add_admin_command("Set heartbeat tick timeout",
                            "/wally/set_heartbeat_tick",
                            zpath_debug_wally_set_hb_tick,
                            NULL,
                            "max_hb_iteration", "Maximum iteration per heartbeat tick",
                            "max_hb_miss_timeout_s", "Maximum allowed heartbeat timeout between ticks",
                            NULL);

    /* Now that we have all our origin databases attached, we can
     * finally export ourselves publicly, if we have been so
     * configured. */
    wally_fohh_server_handle[wally_server_max_index] =
        wally_fohh_server_create(wallyd,
                                 argo_serialize_binary,
                                 fohh_connection_style_argo,
                                 0,
                                 ZPATH_LOCAL_WALLYD_SERVICE_IP.length ? &(ZPATH_LOCAL_WALLYD_SERVICE_IP) : NULL,
                                 htons(ZPATH_LOCAL_WALLYD_SERVICE_PORT),
                                 NULL, // default CA cert
                                 NULL, // Default cert file
                                 NULL, // Default cert key
                                 ZPATH_LOCAL_WALLYD_SERVICE_NO_SSL ? 0 : 1);   // Use SSL
    if (!wally_fohh_server_handle[wally_server_max_index]) {
        WALLYD_LOG(AL_ERROR, "Could not create wally_fohh_server on port %d (permissions?)",
                   ZPATH_LOCAL_WALLYD_SERVICE_PORT);
        return WALLY_RESULT_ERR;
    }
    wally_server_max_index++;

    wally_fohh_server_handle[wally_server_max_index] =
        wally_fohh_server_create(wallyd,
                                 argo_serialize_json_no_newline,
                                 fohh_connection_style_argo_tlv,
                                 0,
                                 ZPATH_LOCAL_WALLYD_SERVICE_IP.length ? &(ZPATH_LOCAL_WALLYD_SERVICE_IP) : NULL,
                                 htons(ZPATH_LOCAL_WALLYD_TLV_SERVICE_PORT),
                                 NULL, // default CA cert
                                 NULL, // Default cert file
                                 NULL, // Default cert key
                                 ZPATH_LOCAL_WALLYD_SERVICE_NO_SSL ? 0 : 1);   // Use SSL
    if (!wally_fohh_server_handle[wally_server_max_index]) {
        WALLYD_LOG(AL_ERROR, "Could not create wally_fohh_server on port %d (permissions?)",
                   ZPATH_LOCAL_WALLYD_TLV_SERVICE_PORT);
        return WALLY_RESULT_ERR;
    }


    if (result) {
        /* Leave this an error, but continue, because it's sort of a
         * spectial case and can be overlooked. */
        WALLYD_LOG(AL_ERROR, "Could initialize wally debugging.");
    }

    if (config_test) {
        result = zpath_debug_add_write_command("Wallyd test writing TLS row",
                                         "/test/wallyd/tls",
                                         wallyd_test_tls,
                                         NULL,
                                         "id", "id to write- Required",
                                         "deleted", "deleted field to write",
                                         "vdi", "vdi field to write",
                                         "instance_id", "instance_id field to write",
                                         "location_id", "location_id field to write",
                                         "ip", "IP address to write",
                                         NULL);
        if (result) {
            WALLYD_LOG(AL_ERROR, "Could not add test debug command: %s", zpath_result_string(result));
            return result;
        }
    }

    WALLYD_LOG(AL_DEBUG, "Initializing zpath_config_override...");
    result = zpath_config_override_init(NULL, 0, 0, config_component_wally);
    if (result) {
        WALLYD_LOG(AL_ERROR, "zpath_config_override failed: %s", zpath_result_string(result));
        return result;
    }

    zpn_register_config_override_wally_override_desciptions();

    result = zpath_debug_mem_stats_init(zpath_service_wally);
    if(result) {
        WALLYD_LOG(AL_ERROR, "Initializing memory allocator stats failed: %s", zpath_result_string(result));
        return result;
    }


    result = wallyd_drain_init();
    if (result) {
        WALLYD_LOG(AL_ERROR, "drain_conn: Could not init wally drain infra: %s", zpath_result_string(result));
        return result;
    }

    result = zpn_wally_stats_init();
    if(result) {
        WALLYD_LOG(AL_ERROR, "Initializing wally stats failed: %s", zpath_result_string(result));
        return result;
    }

    WALLYD_LOG(AL_NOTICE, "Initialization Complete");
    zpath_registration_completed();
    wally_set_fully_load_complete(wallyd);
    set_wally_app_state(wally_state_app_running);
    //config poll interval commands will be available only for origin wally.
    if (!ZPATH_LOCAL_WALLYD_FOHH_REMOTE_HOST) {
        result = poll_intverval_debug_cmds_init();
        if(result) {
             WALLYD_LOG(AL_ERROR, "Poll interval debug cmds init fail: %s", zpath_result_string(result));
             return result;
        }
        poll_rate_check_init(poll_rate_timeout_min);
    }
    if (!ZPATH_LOCAL_WALLYD_FOHH_REMOTE_HOST) {
        poll_rate_check_init(poll_rate_timeout_min);
    }

    /* INIT done, Set operational mode to ACTIVE state */
    oper_data.event = E_ACTIVE;
    wally_oper_handler(&oper_data);
    wallyd_bootup_time = epoch_us() - start_time;
    WALLY_LOG(AL_NOTICE, "Total bootup time = %"PRId64"s ", (wallyd_bootup_time/US_IN_ONE_SECOND));
    argo_log_deregister_structure(wallyd_global_stats_structure, 1);
    while(1) sleep(1);
    /*return 0;*/
}


int main(int argc, char *argv[]) {
    int result;
    result = sub_main(argc, argv);
    sleep(1);
    return result;
}
