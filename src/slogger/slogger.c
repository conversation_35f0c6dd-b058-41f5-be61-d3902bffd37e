/*
 * slogger.c Copyright (C) 2014 Zscaler, Inc. All Rights Reserved.
 *
 */

#include <stdio.h>
#include <stdarg.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <pthread.h>
#include <sys/time.h>
#include <ctype.h>
#include <errno.h>
#include <fcntl.h>
#include "avl/avl.h"

#include "fohh/fohh.h"
#include "fohh/fohh_http.h"
#include "wally/wally.h"
#include "wally/wally_postgres.h"
#include "wally/wally_fohh_client.h"
#include "wally/wally_fohh_server.h"
#include "zpath_misc/zpath_misc.h"

#include "zpath_lib/zpath_local.h"
#include "zpath_lib/zpath_instance.h"
#include "zpath_lib/zpath_debug.h"
#include "zpath_lib/zpath_app.h"
#include "zpath_lib/zpath_domain_lookup.h"
#include "zpath_lib/zpath_et_zone.h"
#include "zpath_lib/zpath_et_customer_zone.h"
#include "zpath_lib/zpath_et_service_endpoint.h"
#include "zpath_lib/zpath_geoip.h"
#include "zpath_lib/zpath_config_override.h"
#include "zpath_lib/zpath_config_override_keys.h"
#include "zpath_lib/zpath_system.h"
#include "zpath_lib/zpath_system_stats.h"
#include "zpath_lib/zpath_system_stats_compiled.h"
#include "zthread/zthread.h"

#include "zpn/zpn_lib.h"
#include "zpn/zpn_siem.h"
#include "zpn/zpn_siem_rx.h"
#include "zpn/zpn_siem_rx_compiled.h"
#include "zpn/zpn_system.h"

#include "zpn/zpn_rpc.h"
#include "zpn/zpn_fohh_worker.h"

#include "lookup_lib/lookup_lib.h"

#include "slogger/slogger.h"
#include "slogger/slogger_work.h"

#include "zpath_misc/zpath_version.h"

#include "zpath_lib/sanitizer_config.h"

#include "zpn_waf/zpn_waf_log.h"
#include "zpn_waf/zpn_waf_log_compiled.h"

#include "zpn_waf/zpn_app_inspection_log.h"
#include "zpn_waf/zpn_app_inspection_log_compiled.h"
#define ZPN_SLOGGER_MONITOR_S 180

int config_daemon = 0;
int thread_count = 12;
int debuglog = 0;
int use_sqlt = 0;
int itasca_logs_port_he = 0;
int system_mem_util = 0;
int process_mem_util = 0;
const char *dbhost = "localhost";
const char *local_file = NULL;
static char *stackpath = NULL;
struct zpath_system_cpu_stats_util cpu_stats_util = {0};
struct zevent_base *slogger_monitor_base = NULL;
struct event *slogger_monitor_timer = NULL;
static struct argo_structure_description *s_zpn_waf_http_exchanges_log_description = NULL;
static struct argo_structure_description *s_zpn_waf_http_exchanges_api_log_description = NULL;
static struct argo_structure_description *s_zpn_ptag_log_description = NULL;
static struct argo_structure_description *s_zpn_app_inspection_log_description = NULL;
static struct argo_structure_description *s_zpn_krb_inspection_log_description = NULL;
static struct argo_structure_description *s_zpn_ldap_inspection_log_description = NULL;
static struct argo_structure_description *s_zpn_smb_inspection_log_description = NULL;
uint32_t zpn_slogger_available_cpus = 0;
struct zpn_system_fd_stats zpn_slogger_fd_stats;
struct zpn_system_sock_stats zpn_slogger_sock_stats;
struct zpn_system_cpu_stats zpn_slogger_cpu_stats;
struct zpn_system_disk_stats zpn_slogger_disk_stats;
struct zpn_system_memory_stats zpn_slogger_mem_stats;
struct zpn_siem_debug_stats_log zpn_siem_debug_stats_l;
struct zpn_siem_conn_details_log zpn_siem_conn_details_l;

struct argo_structure_description *zpn_slogger_disk_stats_description = NULL;
struct argo_structure_description *zpn_slogger_cpu_stats_description = NULL;
struct argo_structure_description *zpn_slogger_fd_stats_description = NULL;
struct argo_structure_description *zpn_slogger_sock_stats_description = NULL;
struct argo_structure_description *zpn_slogger_system_memory_stats_description = NULL;
struct argo_structure_description *zpn_slogger_system_network_stats_description = NULL;
struct argo_structure_description *zpn_siem_debug_stats_log_description = NULL;
struct argo_structure_description *zpn_siem_conn_details_log_description = NULL;

#define APP_ROLE "slogger"

void usage(const char *argv0, const char *format, ...)
    __attribute__((format(printf, 2, 3)));
void usage(const char *argv0, const char *format, ...)
{
    va_list list;

    fprintf(stdout, "Error: ");
	va_start(list, format);
	vfprintf(stdout, format, list);

    fprintf(stdout, "%s: Usage:\n", argv0);
    fprintf(stdout,
            "  -instance NUMBER  : Specify which instance ID this\n"
            "                      daemon will run as (default 0).\n"
            "  -local_file FILE  : Specify that config should come\n"
            "                      from FILE rather than local_db\n"
            "  -printf           : Use printf output for event logs\n"
            "  -itasca_logs PORT : Override HTTP POST logging, and send logs using argo encoding to port PORT for service endpoints\n"
            "  -quiet            : Operate without FOHH status messages\n"
            "  -version          : Display version and exit\n"
            "  -debuglog         : Specify in order to send debug messages to syslog/stderr\n"
            "  -dbhost NAME      : Specify DB host to connect to. Default 'localhost'\n"
            "  -threads COUNT    : Specify number of worker threads. Default 4\n"
            "  -daemon           : Run as daemon\n"
            "  -stackpath PATH   : Write cores to path specified. No trailing slash please\n"
            "  -sqlite           : Use SQLite for client or not. (if not will use postgres).\n"
            "  --print-core FILE : Opt : Read FILE and print stack\n"
            "  --print-core-force FILE : Opt : Read FILE and print stack, without checking app name/version\n"
            "  -disable_heartbeat_monitor: Opt : Disable thread-hang detection\n"
            );
    zpath_app_logging_usage_print();
    exit(1);
}

static struct zpath_config_override_desc slogger_override_desciptions[] = {
    {
        .key                = SLOGGER_REDIRECT_FEATURE_OVERRIDE,
        .desc               = "feature flag to set default redirect mode for slogger when contacting broker",
        .details            = "2: support initial redirect and any subsequent redirects\n"
                              "1: only respond to forced redirects (e.g. broker shutting down)\n"
                              "0: redirect is not supported\n"
                              "default: 1 (Forced redirects)\n",
        .val_type           = config_type_int,
        .component_types    = config_component_slogger,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_cust |config_target_gid_type_global,
        .int_range_lo       = SLOGGER_REDIRECT_MODE_MIN,
        .int_range_hi       = SLOGGER_REDIRECT_MODE_MAX,
        .int_default        = SLOGGER_REDIRECT_MODE_DEFAULT,
        .value_traits       = config_value_traits_normal,
        .feature_group      = FEATURE_GROUP_SLOGGER
    },
    {
        .key                = SLOGGER_ACK_DELAY_TIMEOUT_US_CONFIG_OVERRIDE,
        .desc               = "Amount of time to delay transmission of ack back to source",
        .details            = "1: minimum of 1 second delay (use ack_now for 0)\n"
                              "300: maximum of 5 mins delay\n"
                              "default: 5 seconds\n",
        .val_type           = config_type_int,
        .component_types    = config_component_slogger,
        .target_gid_types   = config_target_gid_type_global,
        .int_range_lo       = SLOGGER_ACK_DELAY_TIMEOUT_US_MIN,
        .int_range_hi       = SLOGGER_ACK_DELAY_TIMEOUT_US_MAX,
        .int_default        = SLOGGER_ACK_DELAY_TIMEOUT_US_DEFAULT,
        .value_traits       = config_value_traits_normal,
        .feature_group      = FEATURE_GROUP_SLOGGER
    }
};

void zpn_register_config_override_slogger_override_desciptions(void) {
    int i;
    int res = 0;
    for (i = 0; i < (sizeof(slogger_override_desciptions) / sizeof(struct zpath_config_override_desc)); i++) {
        res = zpath_config_override_desc_register(&slogger_override_desciptions[i]);
        if (res) ZPATH_LOG(AL_ERROR, "Unable to register slogger_override_desciptions[%d] key: %s, err: %s",
                           i, slogger_override_desciptions[i].key, zpath_result_string(res));
    }
}

static int slogger_listen(void)
{
    struct zpath_instance *instance;
    struct fohh_generic_server *sni_server;
    char sni_str[256];
    char sni_str_json[256];
    int res;
    int i;

    instance = zpath_instance_global_state.current_config;
    if (instance->ips_count == 0) {
        ZPATH_LOG(AL_ERROR, "No IPs configured");
        return ZPATH_RESULT_ERR;
    }

    sni_server = fohh_generic_server_create(1, 1);
    if (!sni_server) {
        ZPATH_LOG(AL_ERROR, "Could not create generic SNI dispatching server");
        return ZPATH_RESULT_ERR;
    }

    for (i = 0; i < instance->ips_count; i++) {
        int res;
        char str[ARGO_INET_ADDRSTRLEN];

        struct sockaddr_storage addr;
        socklen_t addr_len;

        argo_inet_to_sockaddr(&(instance->ips[i]), (struct sockaddr *)&addr, &addr_len, htons(SIEM_PORT));
        res = fohh_generic_server_listen(sni_server, (struct sockaddr *)&addr, addr_len, 0 /* Don't do proxy protocol */);
        if (res) {
            ZPATH_LOG(AL_CRITICAL, "Could not listen for clients, port %d, no proxy protocol, IP %s, reason = %s",
                      SIEM_PORT, argo_inet_generate(str, &(instance->ips[i])), fohh_result_strings[res]);
            /* Don't error out- minor misconfigurations can happen- they should catch these logs */
        }

        argo_inet_to_sockaddr(&(instance->ips[i]), (struct sockaddr *)&addr, &addr_len, htons(SIEM_PROXY_PROTOCOL_PORT));
        res = fohh_generic_server_listen(sni_server, (struct sockaddr *)&addr, addr_len, 1 /* Do proxy protocol */);
        if (res) {
            ZPATH_LOG(AL_CRITICAL, "Could not listen for clients, port %d, proxy protocol, IP %s, reason = %s",
                      SIEM_PROXY_PROTOCOL_PORT, argo_inet_generate(str, &(instance->ips[i])), fohh_result_strings[res]);
            /* Don't error out- minor misconfigurations can happen- they should catch these logs */
        }
    }

    snprintf(sni_str, sizeof(sni_str), "slogger.%s", ZPATH_LOCAL_CLOUD_NAME);
    snprintf(sni_str_json, sizeof(sni_str_json), "slogger.json.%s", ZPATH_LOCAL_CLOUD_NAME);
    res = zpn_siem_rx_listen(sni_server, sni_str, sni_str_json);

    return res;
}


static int lookup_lib_debug(struct zpath_debug_state *request_state,
                            const char **query_values,
                            int query_value_count,
                            void *cookie)
{
    int64_t gid;
    char buf[1000];
    int res;

    if (!query_values[0]) {
        ZDP("Must have specified a 'value=X' argumentt\n");
        return ZPATH_RESULT_ERR;
    }
    gid = strtoul(query_values[0], NULL, 0);

    res = lookup_lib_lookup(gid, buf, sizeof(buf));

    if (res) {
        ZDP("GID = %ld returned %s\n", (long) gid, zpath_result_string(res));
        return ZPATH_RESULT_NO_ERROR;
    }

    ZDP("%ld = %s\n", (long) gid, buf);
    return ZPATH_RESULT_NO_ERROR;
}

static int zpn_slogger_mem_stats_fill(void *cookie,
                                        int counter,
                                        void *structure_data)
{
    struct zpn_system_memory_stats *    out_data;
    (void)cookie;

    out_data = (struct zpn_system_memory_stats *)structure_data;

    out_data->cloud_time_us = zpn_system_get_current_time_cloud_us();
    zpath_system_get_system_memory(&out_data->system_total_bytes,
                                   &out_data->system_free_bytes,
                                   &out_data->system_used_buffer_bytes,
                                   &out_data->system_configured_swap_bytes,
                                   &out_data->system_free_swap_bytes);
    out_data->process_used_bytes = zpath_system_get_process_mem_usage_bytes();
    out_data->system_used_percent = system_mem_util ;
    out_data->process_used_percent = process_mem_util;
    return ZPATH_RESULT_NO_ERROR;
}

static int zpn_slogger_cpu_stats_fill(void *cookie,
                                   int counter,
                                   void *structure_data)
{

    const int res = zpn_system_cpu_stats_fill(cookie, counter, structure_data);
    if (res != ZPN_RESULT_NO_ERROR) {
         ZPN_LOG(AL_ERROR,"Failed to get slogger cpu stats");
         return ZPN_RESULT_NO_ERROR;
    }
    struct zpn_system_cpu_stats *out_data = structure_data;
    out_data->cpu_util_percent = cpu_stats_util.cpu_util;
    out_data->cpu_steal_percent = cpu_stats_util.cpu_steal_perc;
    return ZPN_RESULT_NO_ERROR;
}

static int zpn_siem_debug_stats_send_log(void *cookie,
                                         int counter,
                                         void *structure_data)
{
    struct zpn_siem_debug_stats local_siem_debug_stats = {0};
    struct zpn_siem_debug_stats *g_siem_debug_stats_ref = zpn_siem_get_global_debug_stats();
    memcpy(&local_siem_debug_stats, g_siem_debug_stats_ref, sizeof(struct zpn_siem_debug_stats));
    struct zpn_siem_debug_stats_log *out_data = (struct zpn_siem_debug_stats_log *)structure_data;

    out_data->mtunnel_not_allocated      =   local_siem_debug_stats.zpn_siem_error_type_count[mtunnel_not_allocated];
    out_data->ack_failure                =   local_siem_debug_stats.zpn_siem_error_type_count[ack_failure];
    out_data->no_timer_event             =   local_siem_debug_stats.zpn_siem_error_type_count[no_timer_event];
    out_data->siem_gid_not_found_count   =   local_siem_debug_stats.zpn_siem_error_type_count[siem_gid_not_found];
    out_data->no_zpn_fohh_client_slogger =   local_siem_debug_stats.zpn_siem_error_type_count[no_zpn_fohh_client_slogger];
    out_data->siem_serialize_failed      =   local_siem_debug_stats.zpn_siem_error_type_count[siem_serialize_failed];
    out_data->failed_object_copy         =   local_siem_debug_stats.zpn_siem_error_type_count[failed_object_copy];
    out_data->output_init_failed         =   local_siem_debug_stats.zpn_siem_error_type_count[output_init_failed];
    out_data->rs_siem_gid_changed        =   local_siem_debug_stats.zpn_siem_error_type_count[rs_siem_gid_changed];
    out_data->connection_going_away      =   local_siem_debug_stats.zpn_siem_error_type_count[connection_going_away];
    out_data->siem_out_failed            =   local_siem_debug_stats.zpn_siem_error_type_count[siem_out_failed];
    out_data->zfcs_mt_consume_blocked    =   local_siem_debug_stats.zpn_siem_error_type_count[zfcs_mt_consume_blocked];
    out_data->zfcs_mt_consume_failed     =   local_siem_debug_stats.zpn_siem_error_type_count[zfcs_mt_consume_failed];

    return ZPN_RESULT_NO_ERROR;
}

static int zpn_siem_send_conn_log(void *cookie,
                                  int counter,
                                  void *structure_data)
{
    struct zpn_siem_debug_stats *g_siem_debug_stats_ref = zpn_siem_get_global_debug_stats();
    uint64_t *total_incoming_rs_conn_ref = zpn_siem_get_incoming_client_connection_count();
    uint64_t g_broker_redirection_count  = zpn_siem_get_global_broker_redirection_count();
    uint64_t g_total_incoming_rs_conn_count = 0;
    memcpy(&g_total_incoming_rs_conn_count, total_incoming_rs_conn_ref, sizeof(uint64_t));
    if(g_siem_debug_stats_ref != NULL ){
        ZPATH_MUTEX_LOCK(&(g_siem_debug_stats_ref->siem_stats_lock), __FILE__, __LINE__);
        struct zpn_siem_conn_details_log *out_data = (struct zpn_siem_conn_details_log *)structure_data;
        out_data->total_incoming_rs_conn_count          =   g_total_incoming_rs_conn_count;
        out_data->total_active_rs_conn_count            =   g_siem_debug_stats_ref->active_rs_conn_count;
        out_data->total_destroyed_rs_conn_count         =   g_siem_debug_stats_ref->destroyed_rs_conn_count;
        out_data->total_log_received_count              =   g_siem_debug_stats_ref->log_received_count;
        out_data->total_log_transfered_count            =   g_siem_debug_stats_ref->log_transfered_count;
        out_data->total_log_dropped_count               =   g_siem_debug_stats_ref->log_dropped_count;
        out_data->total_rs_destroy_reason_type_generic  =   g_siem_debug_stats_ref->zpn_rs_destroy_reason_type_count[destroy_generic];
        out_data->total_rs_destroy_reason_type_timer    =   g_siem_debug_stats_ref->zpn_rs_destroy_reason_type_count[destroy_timer];
        out_data->total_rs_destroy_reason_type_fohh     =   g_siem_debug_stats_ref->zpn_rs_destroy_reason_type_count[destroy_fohh];
        out_data->total_rs_destroy_reason_type_zfcs     =   g_siem_debug_stats_ref->zpn_rs_destroy_reason_type_count[destroy_zfcs];
        out_data->total_rs_destroy_reason_type_mt       =   g_siem_debug_stats_ref->zpn_rs_destroy_reason_type_count[destroy_mt];
        out_data->total_broker_redirection_count        =   g_broker_redirection_count;
        ZPATH_MUTEX_UNLOCK(&(g_siem_debug_stats_ref->siem_stats_lock), __FILE__, __LINE__);
    }
    return ZPN_RESULT_NO_ERROR;
}

static void zpn_slogger_monitor_cb(evutil_socket_t sock, short flags, void *cookie)
{
    zthread_heartbeat(NULL);
    zpath_system_monitor_fd();
    zpath_system_monitor_socket();
    if (zpath_system_get_system_and_process_memory_util_percentage(&system_mem_util, &process_mem_util)) {
        system_mem_util = 0;
        process_mem_util = 0;
        ZPN_LOG(AL_NOTICE, "Could not get slogger system memory usage");
    }
    if (zpath_system_get_cpu_stats(&cpu_stats_util) ) {
         ZPN_LOG(AL_ERROR,"Failed to get slogger cpu util stats");
    }
}

static int zpn_slogger_stats_init(void){
    struct timeval tv;
    slogger_monitor_base = zevent_handler_create("slogger_monitor_thread", 512*1024, 360);

    slogger_monitor_timer = event_new(zevent_event_base(slogger_monitor_base),
                                        -1,
                                        EV_PERSIST,
                                        zpn_slogger_monitor_cb,
                                        NULL);

    if( !slogger_monitor_timer )  {
        ZPN_LOG(AL_ERROR, "Unable to init system stats timer");
        return ZPATH_RESULT_ERR;
    }

    tv.tv_sec = ZPN_SLOGGER_MONITOR_S;
    tv.tv_usec = 0;

    if (event_add(slogger_monitor_timer, &tv)) {
        ZPN_LOG(AL_ERROR, "Could not activate slogger stats monitor timer");
        return ZPATH_RESULT_ERR;
    }

    if ((zpn_slogger_available_cpus = sysconf(_SC_NPROCESSORS_ONLN)) == -1) {
        ZPATH_LOG(AL_ERROR, "failed to read number of available cpus - %s", strerror(errno));
    }
    zpn_slogger_disk_stats_description = argo_register_global_structure(ZPN_SYSTEM_DISK_STATS_HELPER);
    zpn_slogger_cpu_stats_description = argo_register_global_structure(ZPN_SYSTEM_CPU_STATS_HELPER);
    zpn_slogger_fd_stats_description = argo_register_global_structure(ZPN_SYSTEM_FD_STATS_HELPER);
    zpn_slogger_sock_stats_description = argo_register_global_structure(ZPN_SYSTEM_SOCK_STATS_HELPER);
    zpn_slogger_system_memory_stats_description = argo_register_global_structure(ZPN_SYSTEM_MEMORY_STATS_HELPER);
    zpn_slogger_system_network_stats_description = argo_register_global_structure(ZPN_SYSTEM_NETWORK_STATS_HELPER);
    zpn_siem_debug_stats_log_description = argo_register_global_structure(ZPN_SIEM_DEBUG_STATS_LOG_HELPER);
    zpn_siem_conn_details_log_description = argo_register_global_structure(ZPN_SIEM_CONN_DETAILS_LOG_HELPER);
    argo_log_register_structure(argo_log_get("statistics_log"),
                                    "zpn_slogger_fd_stats",
                                    AL_INFO,
                                    5*60*1000*1000, /* 5 mins interval */
                                    zpn_slogger_fd_stats_description,
                                    &zpn_slogger_fd_stats,
                                    1,
                                    zpn_system_fd_stats_fill,
                                    NULL);

    argo_log_register_structure(argo_log_get("statistics_log"),
                                    "zpn_slogger_sock_stats",
                                    AL_INFO,
                                    5*60*1000*1000, /* 5 mins interval */
                                    zpn_slogger_sock_stats_description,
                                    &zpn_slogger_sock_stats,
                                    1,
                                    zpn_system_sock_stats_fill,
                                    NULL);

    argo_log_register_structure(argo_log_get("statistics_log"),
                                    "zpn_slogger_cpu_stats",
                                    AL_INFO,
                                    5*60*1000*1000, /* 5 mins interval */
                                    zpn_slogger_cpu_stats_description,
                                    &zpn_slogger_cpu_stats,
                                    1,
                                    zpn_slogger_cpu_stats_fill,
                                    &zpn_slogger_available_cpus);

    argo_log_register_structure(argo_log_get("statistics_log"),
                                    "zpn_slogger_disk_stats",
                                    AL_INFO,
                                    5*60*1000*1000, /* 5 mins interval */
                                    zpn_slogger_disk_stats_description,
                                    &zpn_slogger_disk_stats,
                                    1,
                                    zpn_system_disk_stats_fill,
                                    NULL);

    argo_log_register_structure(argo_log_get("statistics_log"),
                                    "zpn_slogger_mem_stats",
                                    AL_INFO,
                                    5*60*1000*1000, /* 5 mins interval */
                                    zpn_slogger_system_memory_stats_description,
                                    &zpn_slogger_mem_stats,
                                    1,
                                    zpn_slogger_mem_stats_fill,
                                    NULL);

    argo_log_register_structure(argo_log_get("statistics_log"),
                                    "zpn_siem_debug_stats",
                                    AL_INFO,
                                    5*60*1000*1000, /* 5 mins interval */
                                    zpn_siem_debug_stats_log_description,
                                    &zpn_siem_debug_stats_l,
                                    1,
                                    zpn_siem_debug_stats_send_log,
                                    NULL);

    argo_log_register_structure(argo_log_get("statistics_log"),
                                    "zpn_siem_conn_details_log",
                                    AL_INFO,
                                    5*60*1000*1000, /* 5 mins interval */
                                    zpn_siem_conn_details_log_description,
                                    &zpn_siem_conn_details_l,
                                    1,
                                    zpn_siem_send_conn_log,
                                    NULL);


    return 0;
}

void zpn_siem_print_debug_stat(struct zpn_siem_debug_stats *slogger_debug_stats,
                               struct zpath_debug_state *request_state,
                               uint64_t is_global_siem_stats)
{
    if(slogger_debug_stats != NULL){
        ZPATH_MUTEX_LOCK(&(slogger_debug_stats->siem_stats_lock), __FILE__, __LINE__);
        if(is_global_siem_stats){
            uint64_t total_incoming_rs_conn = 0;
            uint64_t *incoming_rs_conn_ref = zpn_siem_get_incoming_client_connection_count();
            memcpy(&(total_incoming_rs_conn), incoming_rs_conn_ref, sizeof(uint64_t));
            uint64_t broker_redirection_count    = zpn_siem_get_global_broker_redirection_count();

            ZDP("\nGlobal stats per slogger instance(Cumulation of all the SIEM stats): \n");
            ZDP("\nConnection_details:\n\
                Total incoming RS connection                   :   %"PRId64"\n\
                Broker redirection count                       :   %"PRId64"\n",
                total_incoming_rs_conn,
                broker_redirection_count);

        } else {
            ZDP("\nStats per SIEM basis(Cumulation of stats from all of the RS States): \n");
            ZDP("\nSiem_gid:\t%"PRId64"\nCustomer_id:\t%"PRId64"\n",
                slogger_debug_stats->siem_gid,
                slogger_debug_stats->customer_id);
            ZDP("\nConnection_details:\n\
                Is SIEM connection active                    :   %"PRId64"\n",
                slogger_debug_stats->is_active);
        }
        ZDP("\t\tActive  SIEM connection count(Active RS State)   :   %"PRId64"\n\
            \tRemoved SIEM connection count(Destroyed RS State):   %"PRId64"\n\
            \tRS connection destroyed type generic             :   %"PRId64"\n\
            \tRS connection destroyed type timer               :   %"PRId64"\n\
            \tRS connection destroyed type fohh                :   %"PRId64"\n\
            \tRS connection destroyed type zfcs                :   %"PRId64"\n\
            \tRS connection destroyed type mt                  :   %"PRId64"\n\
            \tTotal log received count                         :   %"PRId64"\n\
            \tTotal log transfer count                         :   %"PRId64"\n\
            \tTotal log dropped  count                         :   %"PRId64"\n",
            slogger_debug_stats->active_rs_conn_count,
            slogger_debug_stats->destroyed_rs_conn_count,
            slogger_debug_stats->zpn_rs_destroy_reason_type_count[destroy_generic],
            slogger_debug_stats->zpn_rs_destroy_reason_type_count[destroy_timer],
            slogger_debug_stats->zpn_rs_destroy_reason_type_count[destroy_fohh],
            slogger_debug_stats->zpn_rs_destroy_reason_type_count[destroy_zfcs],
            slogger_debug_stats->zpn_rs_destroy_reason_type_count[destroy_mt],
            slogger_debug_stats->log_received_count,
            slogger_debug_stats->log_transfered_count,
            slogger_debug_stats->log_dropped_count);
        ZDP("Error Counts:\n\
            \tmtunnel_not_allocated                            :   %"PRId64"\n\
            \tack_failure                                      :   %"PRId64"\n\
            \tno_timer_event                                   :   %"PRId64"\n\
            \tsiem_gid_not_found_count                         :   %"PRId64"\n\
            \tno_zpn_fohh_client_slogger                       :   %"PRId64"\n\
            \tsiem_serialize_failed                            :   %"PRId64"\n\
            \tfailed_object_copy                               :   %"PRId64"\n\
            \toutput_init_failed                               :   %"PRId64"\n\
            \trs_siem_gid_changed                              :   %"PRId64"\n\
            \tconnection_going_away                            :   %"PRId64"\n\
            \tsiem_out_failed                                  :   %"PRId64"\n\
            \tzfcs_mt_consume_blocked                          :   %"PRId64"\n\
            \tzfcs_mt_consume_failed                           :   %"PRId64"\n",
            slogger_debug_stats->zpn_siem_error_type_count[mtunnel_not_allocated],
            slogger_debug_stats->zpn_siem_error_type_count[ack_failure],
            slogger_debug_stats->zpn_siem_error_type_count[no_timer_event],
            slogger_debug_stats->zpn_siem_error_type_count[siem_gid_not_found],
            slogger_debug_stats->zpn_siem_error_type_count[no_zpn_fohh_client_slogger],
            slogger_debug_stats->zpn_siem_error_type_count[siem_serialize_failed],
            slogger_debug_stats->zpn_siem_error_type_count[failed_object_copy],
            slogger_debug_stats->zpn_siem_error_type_count[output_init_failed],
            slogger_debug_stats->zpn_siem_error_type_count[rs_siem_gid_changed],
            slogger_debug_stats->zpn_siem_error_type_count[connection_going_away],
            slogger_debug_stats->zpn_siem_error_type_count[siem_out_failed],
            slogger_debug_stats->zpn_siem_error_type_count[zfcs_mt_consume_blocked],
            slogger_debug_stats->zpn_siem_error_type_count[zfcs_mt_consume_failed]);
        ZPATH_MUTEX_UNLOCK(&(slogger_debug_stats->siem_stats_lock), __FILE__, __LINE__);
    }
}

static int zpn_print_debug_stats_siem_gid(struct zpath_debug_state *request_state,
                                      const char **query_values,
                                      int query_value_count,
                                      void *cookie)
{
    if (!query_values[0]) {
        ZDP("Enter siem_gid: Example /slogger/debug/siem_gid_stats?value=<siem_gid>\n");
        return ZPATH_RESULT_ERR;
    } else {
        if(zpn_siem_debug_stats_table != NULL){
            uint64_t key = strtol(query_values[0], NULL, 0);
            ZPATH_MUTEX_LOCK(&(zpn_siem_debug_stats_table_lock), __FILE__, __LINE__);
            struct zpn_siem_debug_stats *slogger_debug_stats = zhash_table_lookup(zpn_siem_debug_stats_table,
                                                                                  &key,
                                                                                  sizeof(key),
                                                                                  NULL);
            ZPATH_MUTEX_UNLOCK(&(zpn_siem_debug_stats_table_lock), __FILE__, __LINE__);
            if ( slogger_debug_stats != NULL ) {
                zpn_siem_print_debug_stat(slogger_debug_stats, request_state, 0);
            } else{
                ZDP("%"PRId64" :siem_gid is not present in the zpn_siem_debug_stats_table\n",key);
            }
        } else {
            ZPATH_LOG(AL_NOTICE, "Waiting for zpn_siem_debug_stats_table to get initialised\n");
        }
        return ZPATH_RESULT_NO_ERROR;
    }
}

static int zpn_print_debug_global_stats(struct zpath_debug_state *request_state,
                                      const char **query_values,
                                      int query_value_count,
                                      void *cookie)
{
    struct zpn_siem_debug_stats *global_siem_debug_stats_reference = zpn_siem_get_global_debug_stats();
    zpn_siem_print_debug_stat(global_siem_debug_stats_reference, request_state, 1);
    return 0;
}

static int zpn_siem_reset_all_global_debug_stats()
{
    struct zpn_siem_debug_stats *g_siem_debug_stats_ref = zpn_siem_get_global_debug_stats();
    uint64_t *incoming_rs_conn_count = zpn_siem_get_incoming_client_connection_count();
    if(g_siem_debug_stats_ref != NULL){
        ZPATH_MUTEX_LOCK(&(g_siem_debug_stats_ref->siem_stats_lock), __FILE__, __LINE__);
        uint64_t active_rs_states = g_siem_debug_stats_ref->active_rs_conn_count;
        memset(g_siem_debug_stats_ref, 0, sizeof(struct zpn_siem_debug_stats));
        g_siem_debug_stats_ref->active_rs_conn_count = active_rs_states;
        memset(incoming_rs_conn_count, 0, sizeof(uint64_t));
        ZPATH_MUTEX_UNLOCK(&(g_siem_debug_stats_ref->siem_stats_lock), __FILE__, __LINE__);
    }
    return 0;
}

static int zpn_siem_print_siem_gid(void *cookie1,
                                   void *cookie2,
                                   void *cookie3,
                                   void *cookie4,
                                   void *cookie5,
                                   void *object,
                                   void* key,
                                   size_t key_len)
{
    struct zpn_siem_debug_stats *slogger_debug_stats = (struct zpn_siem_debug_stats*) object;
    struct zpath_debug_state *request_state = (struct zpath_debug_state *) cookie1;
    int *only_is_active_siem = (int *) cookie2;
    if(slogger_debug_stats != NULL){
        ZPATH_MUTEX_LOCK(&(slogger_debug_stats->siem_stats_lock), __FILE__, __LINE__);
        uint64_t res = *(only_is_active_siem) ? slogger_debug_stats->is_active : 1;
        if(res){
            ZDP("\t siem_gid:%"PRId64"      is_active:%"PRId64" \n",
                slogger_debug_stats->siem_gid,
                slogger_debug_stats->is_active);
        }
        ZPATH_MUTEX_UNLOCK(&(slogger_debug_stats->siem_stats_lock), __FILE__, __LINE__);
    }
    return 0;
}

static int zpn_siem_iterate_stats_table(struct zpath_debug_state *request_state,
                                        const char **query_values,
                                        int query_value_count,
                                        void *cookie)
{
    if(zpn_siem_debug_stats_table != NULL){
        if (query_values[0] != NULL){
            int query_param = atoi(query_values[0]);
            if(query_param < 0 || query_param > 1){
               ZDP("Enter only_is_active: Example /slogger/debug/print_siem_gid/only_is_active='1/0'\n");
               return ZPATH_RESULT_ERR;
            }
            int64_t walk_key = 0;
            ZPATH_MUTEX_LOCK(&(zpn_siem_debug_stats_table_lock), __FILE__, __LINE__);
            while ( zhash_table_walk2(zpn_siem_debug_stats_table,
                                    &walk_key,
                                    zpn_siem_print_siem_gid,
                                    request_state,
                                    &query_param,
                                    NULL,
                                    NULL,
                                    NULL) == ZHASH_RESULT_INCOMPLETE ) {}
            ZPATH_MUTEX_UNLOCK(&(zpn_siem_debug_stats_table_lock), __FILE__, __LINE__);
        }
    } else {
        ZPATH_LOG(AL_NOTICE, "Waiting for zpn_siem_debug_stats_table to get initialised");
    }
    return ZPATH_RESULT_NO_ERROR;
}
int sub_main(int argc, char *argv[])
{
    int instance_id = 0;
    int result = 0;
    int i = 0;

    zthread_init(APP_ROLE, ZPATH_VERSION, "unknown", NULL, NULL);
    zthread_do_stack_dump(&argc, argv);
    if (zpath_app_logging_parse_args(&argc, argv)) {
        exit(1);
    }
    result = fohh_worker_pool_parse_args(&argc, argv);
    if (result) {
        fprintf(stderr, "Could not parse worker pool args: %s\n", fohh_result_strings[result]);
        exit(1);
    }
    for (i = 1; i < argc; i++) {
        /* Test for all one-word arguments. */
        if (strcmp(argv[i], "-daemon") == 0) {
            config_daemon = 1;
        } else if (strcmp(argv[i], "-quiet") == 0) {
            fohh_set_quiet(1);
        } else if (strcmp(argv[i], "-version") == 0) {
            fprintf(stdout, "%s\n", ZPATH_VERSION);
            exit(0);
        } else if (strcmp(argv[i], "-printf") == 0) {
            argo_log_use_printf(1);
        } else if (strcmp(argv[i], "-debuglog") == 0) {
            debuglog = 1;
        } else if (strcmp(argv[i], "-sqlite") == 0) {
            use_sqlt = 1;
        } else if (strcmp(argv[i], "-disable_heartbeat_monitor") == 0) {
            zthread_disable_heartbeat_monitor();
        }else {
            /* Test for all two-word arguments. */
            if ((i + 1) >= argc) {
                /* There is not a pair of words... */
                usage(argv[0], "Improper argument- may be missing second field: %s\n", argv[i]);
                /* Exits */
            }
            if (strcmp(argv[i], "-instance") == 0) {
                instance_id = strtoul(argv[i+1], NULL, 0);
                i++;
            } else if (strcmp(argv[i], "-stackpath") == 0) {
                i++;
                stackpath = argv[i];
            } else if (strcmp(argv[i], "-local_file") == 0) {
                i++;
                local_file = argv[i];
            } else if (strcmp(argv[i], "-itasca_logs") == 0) {
                i++;
                itasca_logs_port_he = atoi(argv[i]);
            } else if (strcmp(argv[i], "-dbhost") == 0) {
                i++;
                dbhost = argv[i];
            } else if (strcmp(argv[i], "-threads") == 0) {
                i++;
                thread_count = atoi(argv[i]);
                if (thread_count <= 0) {
                    usage(argv[0], "Invalid thread count: %s", argv[i]);
                }
            } else {
                usage(argv[0], "Unrecognized argument: %s\n", argv[i]);
                /* Exits */
            }
        }
    }

    if (config_daemon) {
        int fd;
        fprintf(stderr, "Daemonizing\n");
        /* Take off... */
        switch (fork()) {
		case 0:
			break;
		case -1:
            ZPATH_LOG(AL_ERROR, "fork failed: %s", strerror(errno));
            return ZPATH_RESULT_ERR;
		default:
			/* exit interactive session */
			exit(0);
        }
        if(setsid() == -1) {
            ZPATH_LOG(AL_ERROR, "setsid failed: %s", strerror(errno));
            return ZPATH_RESULT_ERR;
        }
        if ((fd = open("/dev/null", O_RDWR, 0)) != -1) {
            (void)dup2(fd, STDIN_FILENO);
            (void)dup2(fd, STDOUT_FILENO);
            (void)dup2(fd, STDERR_FILENO);
            if (fd > 2)
                (void)close(fd);
        }
    }

    zpn_debug_set(ZPN_DEBUG_CLIENT_IDX);
    zpn_debug_set(ZPN_DEBUG_BROKER_IDX);
    zpn_debug_set(ZPN_DEBUG_APPLICATION_IDX);
    zpn_debug_set(ZPN_DEBUG_RULE_IDX);
    zpn_debug_set(ZPN_DEBUG_DISPATCHER_IDX);
    zpn_debug_set(ZPN_DEBUG_LEARN_IDX);
    zpn_debug_set(ZPN_DEBUG_APPLICATION_SERVER_IDX);
    zpn_debug_set(ZPN_DEBUG_ASSISTANT_IDX);
    zpn_debug_set(ZPN_DEBUG_BROKER_ASSISTANT_IDX);
    zpn_debug_set(ZPN_DEBUG_SIGNING_CERT_IDX);
    zpn_debug_set(ZPN_DEBUG_ISSUEDCERT_IDX);
    zpn_debug_set(ZPN_DEBUG_CLIENT_TABLE_IDX);
    zpn_debug_set(ZPN_DEBUG_IDP_IDX);
    zpn_debug_set(ZPN_DEBUG_TIMER_IDX);
    zpn_debug_set(ZPN_DEBUG_AUTH_IDX);
    zpn_debug_set(ZPN_DEBUG_APPLICATION_GROUP_IDX);
    zpn_debug_set(ZPN_DEBUG_HEALTH_REPORT_IDX);
    zpn_debug_set(ZPN_DEBUG_LOG_IDX);
    zpn_debug_set(ZPN_DEBUG_MTUNNEL_IDX);
    zpn_debug_set(ZPN_DEBUG_BROKER_LOAD_IDX);
    zpn_debug_set(ZPN_DEBUG_CONFIG_IDX);
    zpn_debug_set(ZPN_DEBUG_BALANCE_IDX);
    zpn_debug_set(ZPN_DEBUG_BROKER_DNS_IDX);
    zpn_debug_set(ZPN_DEBUG_FLOW_CONTROL_IDX);
    zpn_debug_set(ZPN_DEBUG_BALANCE_IDX);
    zpn_debug_set(ZPN_DEBUG_SIEM_IDX);
    zpn_debug_set(ZPN_DEBUG_SIEM_ALL_IDX);

    if (itasca_logs_port_he) {
        update_itasca_logs_port(itasca_logs_port_he);
    }
    zpn_siem_debug_stats_table = zhash_table_alloc(&zpath_lib_allocator);
    zpn_siem_debug_stats_table_lock = ZPATH_MUTEX_INIT;
    struct zpn_siem_debug_stats *global_siem_stats_reference = zpn_siem_get_global_debug_stats();
    global_siem_stats_reference->siem_stats_lock = ZPATH_MUTEX_INIT;
    zhash_table_set_lock_protected(zpn_siem_debug_stats_table);
    fohh_set_state_stats_logging(1);
    /* If we are replicating the global DB, then we pay attention to
     * remote_host configuration for this DB when we initialize the
     * global DB. */
    struct zpath_app_init_params app_params;
    zpath_app_init_params_default(&app_params);
    app_params.instance_id = instance_id;
    app_params.zpath_local_config_file = local_file;
    app_params.local_db_hostname = dbhost;
    app_params.role_name = "slogger";
    app_params.fohh_thread_count = thread_count;
    app_params.use_sqlt = use_sqlt;
    app_params.debuglog = debuglog;
    app_params.debugcmd_pool_support = 1;

    result = zpath_app_init(&app_params);
    if (result) {
        fprintf(stderr, "Error: Could not intialize\n");
        sleep(1);
        exit(1);
    }

    zthread_init(APP_ROLE, ZPATH_VERSION, ZPATH_LOCAL_FULL_NAME, stackpath, NULL);
    zpn_event_collection = zpath_event_collection;

    ZPATH_LOG(AL_NOTICE, "Slogger version: %s", ZPATH_VERSION);

    /* Init generic zpn worker state */
    result = zpn_fohh_worker_state_init(0);
    if (result) {
        ZPN_LOG(AL_ERROR, "zpn_fohh_worker_state_init failed: %s",
                zpath_result_string(result));
        return result;
    }

    ZPN_LOG(AL_NOTICE, "Initializing shard access...");
    result = zpath_app_shard_pre_init();
    if (result) {
        ZPN_LOG(AL_ERROR, "Shard access initialization failed");
        return result;
    }

    result = zpath_app_just_init_shards(use_sqlt);
    if (result != ZPATH_RESULT_NO_ERROR) {
        ZPN_LOG(AL_ERROR, "Could not initialize shards");
        return result;
    }

    ZPATH_LOG(AL_NOTICE, "Initializing zpath_config_override...");
    result = zpath_config_override_init(NULL, 0, 0, config_component_slogger);
    if (result) {
        ZPN_LOG(AL_ERROR, "zpath_config_override failed: %s", zpath_result_string(result));
        return result;
    }
    zpn_register_config_override_slogger_override_desciptions();
    ZPATH_LOG(AL_NOTICE, "Initializing zpath_config_override... Complete!");

    ZPATH_LOG(AL_NOTICE, "Initializing lookup tables...");
    result = lookup_lib_init(zpath_event_collection);
    if (result) {
        ZPATH_LOG(AL_ERROR, "Could not init lookup_lib: %s", zpn_result_string(result));
        return -1;
    }
    ZPATH_LOG(AL_NOTICE, "Initializing lookup tables... Complete!");

    result = zpath_debug_add_read_command("Do a GID lookup using the lookup service.",
                                     "/lookup/gid",
                                     lookup_lib_debug,
                                     NULL,
                                     "value", "GID to lookup",
                                     NULL);
    if (result) {
        ZPATH_LOG(AL_ERROR, "Could not init lookup_lib debug: %s", zpn_result_string(result));
        return -1;
    }
    result = zpath_debug_add_read_command("Print slogger debug stats for a particular siem_gid",
                                     "/slogger/debug/siem_gid_stats",
                                     zpn_print_debug_stats_siem_gid,
                                     NULL,
                                     "siem_gid", "Enter the siem_id for it's debug stats",
                                     NULL);
    if (result) {
        ZPATH_LOG(AL_ERROR, "Could not initialize the debug command: /slogger/debug/siem_gid_stats");
        return ZPATH_RESULT_ERR;
    }
    result = zpath_debug_add_read_command("Print slogger debug global stats",
                                     "/slogger/debug/global_stats",
                                     zpn_print_debug_global_stats,
                                     NULL,
                                     NULL);
    if (result) {
        ZPATH_LOG(AL_ERROR, "Could not initialize the debug command: /slogger/debug/global_stats");
        return ZPATH_RESULT_ERR;
    }
    result = zpath_debug_add_write_command("Reset slogger global debug stats for all siem_gid to zero",
                                     "/slogger/debug/global_stats/reset",
                                     zpn_siem_reset_all_global_debug_stats,
                                     NULL,
                                     NULL);
    if (result) {
        ZPATH_LOG(AL_ERROR, "Could not initialize the debug command: /slogger/debug/global_stats/reset");
        return ZPATH_RESULT_ERR;
    }
    result = zpath_debug_add_read_command("Print SIEM gid's being served by slogger",
                                     "/slogger/debug/print_siem_gid",
                                     zpn_siem_iterate_stats_table,
                                     NULL,
                                     "only_is_active", "1:Active SIEM 0:All SIEM",
                                     NULL);
    if (result) {
        ZPATH_LOG(AL_ERROR, "Could not initialize the debug command: /slogger/debug/print_siem_gid");
        return ZPATH_RESULT_ERR;
    }
    ZPATH_LOG(AL_NOTICE, "Initializing rpcs...");
    result = zpn_rpc_init();
    if (result) {
        ZPATH_LOG(AL_ERROR, "Could not init rpcs: %s", zpn_result_string(result));
        return -1;
    }
    s_zpn_waf_http_exchanges_log_description = argo_register_global_structure(ZPN_WAF_HTTP_EXCHANGES_LOG_HELPER);
    if (!s_zpn_waf_http_exchanges_log_description) {
        ZPATH_LOG(AL_ERROR, "Failed to set up WAF log description");
        return -1;
    }

    s_zpn_waf_http_exchanges_api_log_description = argo_register_global_structure(ZPN_WAF_HTTP_EXCHANGES_API_LOG_HELPER);
    if (!s_zpn_waf_http_exchanges_api_log_description) {
        ZPATH_LOG(AL_ERROR, "Failed to set up API WAF log description");
        return -1;
    }

    s_zpn_ptag_log_description = argo_register_global_structure(ZPN_PTAG_LOG_HELPER);
    if (!s_zpn_ptag_log_description) {
        ZPATH_LOG(AL_ERROR, "Failed to set up ptag log description");
        return -1;
    }
    s_zpn_app_inspection_log_description = argo_register_global_structure(ZPN_APP_INSPECTION_LOG_HELPER);
    if (!s_zpn_app_inspection_log_description) {
        ZPATH_LOG(AL_ERROR, "Failed to set up app inspection log description");
        return -1;
    }
    s_zpn_krb_inspection_log_description = argo_register_global_structure(ZPN_KRB_INSPECTION_LOG_HELPER);
    if (!s_zpn_krb_inspection_log_description) {
        ZPATH_LOG(AL_ERROR, "Failed to set up krb inspection log description");
        return -1;
    }
    s_zpn_ldap_inspection_log_description = argo_register_global_structure(ZPN_LDAP_INSPECTION_LOG_HELPER);
    if (!s_zpn_ldap_inspection_log_description) {
        ZPATH_LOG(AL_ERROR, "Failed to set up ldap inspection log description");
        return -1;
    }
    s_zpn_smb_inspection_log_description = argo_register_global_structure(ZPN_SMB_INSPECTION_LOG_HELPER);
    if (!s_zpn_smb_inspection_log_description) {
        ZPATH_LOG(AL_ERROR, "Failed to set up smb inspection log description");
        return -1;
    }

    ZPATH_LOG(AL_NOTICE, "Initializing rpcs... Complete!");

    ZPATH_LOG(AL_NOTICE, "Initializing siem library...");
    result = zpn_siem_init();
    if (result) {
        ZPATH_LOG(AL_ERROR, "Could not init siem library: %s", zpn_result_string(result));
        return -1;
    }
    ZPATH_LOG(AL_NOTICE, "Initializing siem library... Complete!");
    ZPATH_LOG(AL_NOTICE, "Initializing stats log...");
    result = zpn_slogger_stats_init();
    if (result) {
        ZPATH_LOG(AL_ERROR, "Could not init stats for slogger: %s", zpn_result_string(result));
        return -1;
    }
    ZPATH_LOG(AL_NOTICE, "Initializing stats log... Complete!");

    ZPATH_LOG(AL_NOTICE, "Initializing slogger...");

    result = zpath_debug_add_flag_ext(zpn_debug_cnxt(),
                                      zpn_debug_catch_cnxt(),
                                      zpn_debug_cnxt_cnt(),
                                      "zpn",
                                      zpn_debug_names);
    if (result) {
        ZPN_LOG(AL_ERROR, "zpath_debug_add_flag failed: %s",
                zpath_result_string(result));
        return result;
    }

    /*
     * Create server(s) for listening to connections across all
     * desired IPs, listening only for those connections matching SNI
     * slogger.[cloud]
     */
    result = slogger_listen();
    if (result) {
        ZPATH_LOG(AL_ERROR, "slogger_listen failed: %s", zpath_result_string(result));
        return result;
    }

    ZPATH_LOG(AL_NOTICE, "Initialization Complete");


    zpath_registration_completed();

    while(1) sleep(1);

    return 0;
}


int main(int argc, char *argv[]) {
    int result;
    result = sub_main(argc, argv);
    sleep(1);
    return result;
}
