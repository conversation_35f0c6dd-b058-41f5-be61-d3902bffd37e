Name:           zpn-brokerd
Version:        %{_version}
Release:        1%{?dist}
Summary:        Zscaler Private Access Broker
Group:          Applications/Internet
License:        license.txt
Vendor:         Zscaler
URL:            https://www.zscaler.com
Source:         %{name}-%{version}.tar
Prefix:         /opt/zscaler
Requires(pre):  shadow-utils
%{?systemd_requires}
BuildRequires:  systemd

%global ServiceName         zpn_brokerd
%global ZHM_Required            1
%global ZPM_Required            1
%global Zblu_Required           1
%global PostgresRBAC_Requried   1
%global Command_Logging_Required    1
%global Filesystem_RBAC_Required    1

%global APP_CAPABILITIES  CAP_NET_BIND_SERVICE,cap_net_raw,cap_net_admin

%include %{_cmake_rpm_path}/src/rpm/common/macros

%common_global

%description
zpn-brokerd is the connector for the Zscaler Private Access Broker. Deploys service zpn_brokerd.
%prep

%setup -q

%build

%pre

%define _build_id_links none

%install
%common_install %{ServiceName}
install -p -D -m 444 50-zscaler.preset $RPM_BUILD_ROOT%{_unitdir}/../system-preset/
install -p -D -m 755 -D setup_folders_and_permissions.sh $RPM_BUILD_ROOT%{prefix}/var/rbac/setup_folders_and_permissions.sh
install -p -D -m 644 -D itasca_folders_config_v1.conf $RPM_BUILD_ROOT%{prefix}/var/rbac/itasca_folders_config_v1.conf
install -p -D -m 644 -D itasca_folders_config_v2.conf $RPM_BUILD_ROOT%{prefix}/var/rbac/itasca_folders_config_v2.conf

%clean
%common_clean

%files
%common_files

%doc

%post
%common_post %{ServiceName}
bash %{prefix}/var/rbac/setup_folders_and_permissions.sh %{prefix}/var/rbac/itasca_folders_config_v2.conf

%preun
%common_preun %{ServiceName}
if [ $1 -eq 0 ]; then
    bash %{prefix}/var/rbac/setup_folders_and_permissions.sh %{prefix}/var/rbac/itasca_folders_config_v1.conf
fi

%postun
%common_postun %{ServiceName}

%posttrans
%common_posttrans %{ServiceName}

%changelog
