[Unit]
Description=Zscaler Private Access Broker
After=network-online.target
Wants=zpm.service
Wants=zhm.service

[Service]
Type=simple
Restart=always
User=zscaler
Group=zscaler
LimitNOFILE=102400
WorkingDirectory=/opt/zscaler
StandardError=journal
StandardOutput=journal
EnvironmentFile=-/opt/zscaler/etc/zpn_brokerd.conf
ExecStart=/opt/zscaler/bin/zpn_brokerd -geoip /opt/zscaler/var/geoip/GeoIP2-City.mmdb -geoipisp /opt/zscaler/var/geoip/GeoIP2-ISP.mmdb -stackpath /opt/zscaler/var $OPTIONS
LimitCORE=infinity
ExecStopPost=/opt/zscaler/bin/zblu
ExecStopPost=+/bin/bash -c '(/opt/zscaler/bin/failover_manager.py --service=zpn-brokerd --result=${SERVICE_RESULT} --code=${EXIT_CODE} --status=${EXIT_STATUS}) || true'

[Install]
WantedBy=multi-user.target
