/*
 * zthread.c. Copyright (C) 2014 Zscaler, Inc. All Rights Reserved.
 */

#define _GNU_SOURCE
#ifdef __linux__
#include <link.h>
#endif

#include <dlfcn.h>
#include <execinfo.h>
#include <errno.h>
#include <fcntl.h>
#include <pthread.h>
#include <signal.h>
#include <stddef.h>
#include <stdlib.h>
#include <string.h>
#include <errno.h>
#include <unistd.h>
#include <limits.h>
#include <sys/resource.h>
#include <sys/stat.h>
#include <sys/types.h>
#include <stdatomic.h>
#include "zpath_misc/zpath_misc.h"
#include "zpath_misc/zpath_platform.h"
#include "zthread/zthread.h"
#include "zpath_misc/zpath_version.h"

#define STACK_DUMP_MAGIC_NUMBER 0x40f619a1a22501a1l
#define MAX_SKIPS 2
#define ZTHREAD_MAX_MISSING_HEARTBEAT_S 2
#define BUFFER_SIZE 512

#define US_IN_SEC                                   (1000000)
#define USAGE_HISTORY_SHORT_INTERVAL_US             (1*US_IN_SEC)
#define USAGE_HISTORY_LONG_INTERVAL_US              (USAGE_HISTORY_SHORT_INTERVAL_US * 60)
#define NUM_SHORT_INTERVAL_USAGE_HISTORY_ENTRIES     60
#define NUM_LONG_INTERVAL_USAGE_HISTORY_ENTRIES      10

#define ZTHREAD_IS_HEALTHY                           0
#define ZTHREAD_IS_UNHEALTHY                         1
#define ZTHREAD_IS_STUCK                             2
#define RELEASE_APP_VERSION_LEN                      255

// Do not optimize entire function.
#if defined(__clang__)
#define ZDO_NOT_OPTIMIZE __attribute__((optnone))
#elif defined(__GNUC__)
#define ZDO_NOT_OPTIMIZE __attribute__((optimize("O0")))
#else
#define ZDO_NOT_OPTIMIZE
#endif

_Atomic int64_t zthread_curr_time;
int64_t zthread_usage_short_interval_us = USAGE_HISTORY_SHORT_INTERVAL_US;
int64_t zthread_usage_long_interval_us = USAGE_HISTORY_LONG_INTERVAL_US;

struct zpath_allocator zthread_allocator = ZPATH_ALLOCATOR_INIT("zthread");

static pthread_mutex_t lock = (pthread_mutex_t)PTHREAD_MUTEX_INITIALIZER;
struct zthread_info zthread_all_threads[ZTHREAD_MAX_THREADS];
int zthread_thread_num = 0;
int zthread_monitor_thread_num = 0;
int zthread_all_threads_count = 0;
int zthread_disable = 0;
int zthread_heartbeat_thread_number = -1;   /* 0 is zthread; mark as -1 to indicate not set */

int zpn_process_termination_monitor_flag = 0;

static pthread_mutex_t zthread_group_lock = (pthread_mutex_t)PTHREAD_MUTEX_INITIALIZER;
struct zthread_group_info zthread_groups[ZTHREAD_MAX_GROUPS];
int zthread_group_count = 0;
static void zthread_group_compute_stats(struct zthread_group_info* info);

static int zthread_env_simple_app = 0;
static int64_t zthread_last_heartbeat_monotime_s = 0;
static int64_t zthread_monitor_last_heartbeat_monotime_s = 0;
static int64_t zthread_maximum_heartbeat_delta_s = 0;

static char stack_trace_filename[256] = "application.stack";
static char config_app_name[128] = "application";
static char config_app_version[128] = "1.1.1";
static char config_instance[128] = "unknown";
static char config_cloud_name[128] = "unknown";
static char org_name[128] = "unknown";
static int (*additional_logs_cb)(struct additional_debug_logs *) = NULL;

static void (*zthread_exit_handler_cb)(int thread_number) = NULL;

static int abort_enabled = 1;
static int config_enable_stack_traces = 0;
static int disable_heartbeat_monitor = 0;
static volatile sig_atomic_t dump_stacks_on_abort = 1;
static int zthread_missed_prev_hb = 0;
static int zthread_monitor_missed_prev_hb = 0;
static int zthread_heartbeat_override_timeout = 0;

int64_t g_zthread_termination_stuck_zpm_bails_out = 0;
int64_t g_zthread_termination_deadlock_zpm_bails_out = 0;
int64_t g_zthread_stuck_termination_bails_out = 0;

static size_t max_stack_bytes = 0; /* Unlimited. (per-thread) */

#define ZTHREAD_ON_CRASH_DO_FEATURE_DEFAULT zthread_on_crash_do_no_action
int64_t zthread_on_crash_do_feature = ZTHREAD_ON_CRASH_DO_FEATURE_DEFAULT;
volatile sig_atomic_t zthread_on_crash_do_feature_sig = ZTHREAD_ON_CRASH_DO_FEATURE_DEFAULT;
volatile sig_atomic_t zthread_on_crash_do_close_sockets_max_num = 102400; /* by default max number of sockets, it will be overritten during zthread_init */

static int (* zthread_zpn_event_cpu_starvation_cb)(int) = NULL;

static int zthread_sock_fd_array[SOCK_FD_ARRAY_MAX] = {-1, -1, -1, -1, -1, -1, -1, -1, -1, -1};

static char release_app_version[RELEASE_APP_VERSION_LEN+1] = "RELEASE_VERSION_";

// Include the build version in binaries
static char zpath_zpi_version[ZPATH_VERSION_STR_MAX_LEN + 1] __attribute__((used)) __attribute__((unused)) = "ZPATH_ZPI_VERSION__" ZPATH_VERSION ;

struct zthread_stats zstats;

static struct {
    /*
     * When this is set, ignore any further heartbeats from any threads. This is set when zthread wants to do accounting
     * based on whatever heartbeats are done already. Yes, lock would be the right way to do this and there is no
     * guarantee to get it right with a shared variable - but 1. its ok, just a troubleshoting utility and wanted to
     * get hints from the field rather than being correct all the time. 2. if we get man false positives from the field,
     * then we can enable locking. Just a good first step without complexity.
     */
    int disable_further_heartbeats;
    /*
     * This is disabled by default. Can be enabled only for debugging in dev environment and definitely not in
     * production without doing bit of soak testing in dev.
     */
    int record_cpu_time_since_last_heartbeat;

    int64_t zthread_time_skipped;
    int64_t zthread_time_skipped_exceed_max;
    int64_t zthread_healthy_counter;
    int64_t zthread_unhealthy_counter;
    int64_t zthread_unhealthy_counter_saved;
    int64_t zthread_monitor_healthy_counter;
    int64_t zthread_monitor_unhealthy_counter;
    int64_t zthread_monitor_unhealthy_counter_saved;

} state;

/*
 * Header does not include magic number
 * cpu_time_recorded - callers are expected to set this to only when a thread's heartbeat timeout happened and
 * explicity asked by the process - i.e only in case of debugging. This is because, we do some signal unsafe
 * (getrusage) operations.
 */
struct dump_header {
    int32_t thread_count;  /* Number of thread entries we will have */
    int32_t thread_id;     /* The thread ID that generated the trace */
    int64_t epoch_s;       /* Current time, in epoch, in seconds */
    int32_t pid;           /* Process ID */
    char identifier[128];  /* GID/System Identifier */
    char name[128];        /* Application name */
    char version[128];     /* Application version */
    char platform[128];    /* System platform.arch */
    char details[128];     /* Event details */
    char cloud_name[128];
    char org_name[128];    /* Organization Name */
    int32_t cpu_time_recorded;                                 /* indicates if the below 3 fields are valid */
    int64_t maximum_heartbeat_delta_s[ZTHREAD_MAX_THREADS];
    int64_t delta_process_user_time_us[ZTHREAD_MAX_THREADS];   /* user time spent after the previous heartbeat - process wide */
    int64_t delta_process_system_time_us[ZTHREAD_MAX_THREADS]; /* system time spent after the previous heartbeat - process wide */
    int64_t delta_process_clock_time_us[ZTHREAD_MAX_THREADS];  /* clock time spent after the previous heartbeat - process wide */
    int32_t additional_dbg_log_bytes;
    int64_t delta_process_monotime_s[ZTHREAD_MAX_THREADS];    /* monotonic clock time spent after the previous heartbeat - process wide */
} __attribute__((packed));

struct zthread_stack_trace_sym {
    char file[64];
    char name[64];
    char sign;
    uintptr_t offset;
} __attribute((packed));


static void zthread_close_socket_fds(void)
{
    for (int i = 0; i < SOCK_FD_ARRAY_MAX && zthread_sock_fd_array[i] != -1; i++)
        close(zthread_sock_fd_array[i]);
}

void zthread_set_socket_fds(int *sock_fd_array, int sock_fd_count)
{
    if (!sock_fd_count || !sock_fd_array)
        return;
    if (sock_fd_count > SOCK_FD_ARRAY_MAX)
        sock_fd_count = SOCK_FD_ARRAY_MAX;
    for (int i = 0; i < sock_fd_count && sock_fd_array[i] != -1; i++)
        zthread_sock_fd_array[i] = sock_fd_array[i];
}

static inline void zthread_abort_no_stack_dump() {
    dump_stacks_on_abort = 0;
    abort();
}

/*
 * On crash perform extra operations based on config-override feature selection
 */
void zthread_on_crash_do(void)
{
    if (zthread_on_crash_do_feature_sig == zthread_on_crash_do_no_coredump) {
        fprintf(stdout,"zthread_on_crash_do: exiting\n");
        sleep(1);
        _exit(2); /* need async-signal-safe function when called from sig handler*/
    } else if (zthread_on_crash_do_feature_sig == zthread_on_crash_do_close_all_sockets) {
        /* close all socket connections, including all servers: listen, accept */
        for(int i = 0; i < zthread_on_crash_do_close_sockets_max_num; i++) {
            close(i);
        }
    }
    /* no action by default */
    return;
}

/*
 * On crash we may configure features/actions how to handle crash using config-override key: "config.feature.broker.on_crash_do"
 * Config-override option #0 - zthread_on_crash_do_no_action: no extra actions. Crash is handled using abort operation which may generate coredump.
 * Config-override option #1 - zthread_on_crash_do_no_coredump: quick exit and omit abort operation which may generate coredump
 * Config-override option #2 - zthread_on_crash_do_close_all_sockets: close all connection via closing all sockets
*/
void zthread_on_crash_do_config_override_monitor_callback(const int64_t *config_value, int64_t impacted_gid)
{
    zthread_on_crash_do_feature_sig = (int)*config_value;
    return;
}

static uintptr_t zthread_get_base_relocation() {
#ifdef __linux__
    return _r_debug.r_map->l_addr;
#else
    return 0;
#endif
}

static void *zthread_relocate_addr(void *addr, int to_base) {
    uintptr_t offset = zthread_get_base_relocation();
    if (to_base)
        return (void *)((uintptr_t)addr - offset);
    else
        return (void *)((uintptr_t)addr + offset);
}

static void zthread_relocate_stack(struct zthread_stack_trace *trace, int to_base) {
    for (int i = 0; i < trace->stack_depth; ++i) {
        trace->stack[i] = zthread_relocate_addr(trace->stack[i], to_base);
    }
}

static int zthread_create_internal(pthread_t *thread,
                                   zthread_f *thread_func,
                                   void *cookie,
                                   const char *thread_name,
                                   int max_missing_heartbeat_s,
                                   size_t stack_size_bytes,
                                   int64_t user_int,
                                   void *user_void,
                                   enum zthread_priority priority);

static int zthread_stack_dump_setup(void);
static void zthread_dump_stacks(int thread_number, char *sig_info,
                                int record_cpu_time_since_last_heartbeat,
                                int killall_threads);

static pthread_key_t zthread_key;

static zthread_log_f *zlog_f = NULL;

#define ZTHREAD_LOG(str) if (zlog_f) (zlog_f)(str);

void zthread_set_log(zthread_log_f *log_f)
{
    zlog_f = log_f;
}

static inline void zthread_dump_stuck_thread_message(int zthread_thread_num)
{
    char msg[BUFFER_SIZE] = "Error: thread stuck thread_id";
    snprintf(msg, sizeof(msg), "Error: thread stuck thread name: %s, thread_id: %d", zthread_get_thread_name(zthread_thread_num), zthread_thread_num);
    fprintf(stdout, "%s\n", msg);
    ZTHREAD_LOG(msg);
}

void zthread_set_abort(int enabled)
{
    abort_enabled = enabled;
}

/* check if zthread itself is missing heartbeats...
 * this probably indicate a VM freezing issue */
static int zthread_health_check()
{
    int64_t now_s;
    int64_t delta_s;

    now_s = monotime_s();
    delta_s = zthread_last_heartbeat_monotime_s ? (now_s - zthread_last_heartbeat_monotime_s) : 0;
    if (delta_s > zthread_maximum_heartbeat_delta_s) {
        /* zthread has missed its own heartbeats */
        if (zthread_missed_prev_hb || !zthread_env_simple_app) {
            /* currently zthread_env_simple_app is only set on connectors.
             * 1. we're a connector and zthread already missed a heartbeat in previous round - time to start checking heartbeat timeouts
             * 2. we're not a connector, then report zthread stuck immediately */
            return ZTHREAD_IS_STUCK;
        } else {
            /* we didn't miss heartbeat in previous round, let's pretend this
             * was caused by a sudden cpu steal and don't kill other heartbeat timeouts */
            zthread_missed_prev_hb = 1;
            return ZTHREAD_IS_UNHEALTHY;
        }
    }
    zthread_missed_prev_hb = 0;
    return ZTHREAD_IS_HEALTHY;
}

/* see zthread_health_check */
static int zthread_monitor_health_check()
{
    int64_t now_s;
    int64_t delta_s;

    now_s = monotime_s();
    delta_s = zthread_monitor_last_heartbeat_monotime_s ? (now_s - zthread_monitor_last_heartbeat_monotime_s) : 0;
    if (delta_s > zthread_maximum_heartbeat_delta_s) {
        if (zthread_monitor_missed_prev_hb || !zthread_env_simple_app) {
            return ZTHREAD_IS_STUCK;
        } else {
            zthread_monitor_missed_prev_hb = 1;
            return ZTHREAD_IS_UNHEALTHY;
        }
    }
    zthread_monitor_missed_prev_hb = 0;
    return ZTHREAD_IS_HEALTHY;
}

int zthread_is_valid_thread_number(int thread_number)
{
    if ((thread_number < 0) ||
        (thread_number >= zthread_all_threads_count)) {
        return 0;
    }

    return 1;
}

char* zthread_get_thread_name(int thread_number)
{
    if (!zthread_is_valid_thread_number(thread_number)) {
        return "";
    }
    return zthread_all_threads[thread_number].stack.thread_name;
}

int zthread_set_disable_heartbeat_for_thread(int disable, char *thread_name)
{
    int ret = 1;
    for (int i = 0; i < zthread_all_threads_count; i++) {
        if (strcmp(zthread_all_threads[i].stack.thread_name, thread_name) == 0) {
            zthread_all_threads[i].disable_heartbeat = disable;
            ret = 0;
            break;
        }
    }
    return ret;
}

void zthread_set_dont_kill_on_terminate_flag(int thread_num)
{
    if (!zthread_is_valid_thread_number(thread_num))
        return;

    zthread_all_threads[thread_num].flags |= ZTHREAD_INFO_FLAG_DO_NOT_KILL_ON_TERMINATION;
}

int zthread_get_dont_kill_on_terminate_flag(int thread_num)
{
    if (!zthread_is_valid_thread_number(thread_num))
        return 0;

    if (zthread_all_threads[thread_num].flags & ZTHREAD_INFO_FLAG_DO_NOT_KILL_ON_TERMINATION){
        return 1;
    }
    return 0;
}

void zthread_exit_or_abort(void)
{
    if (abort_enabled) {
        zthread_abort_no_stack_dump();
    } else {
        sleep(1);
        exit(0);
    }
}

/*
 * zthread_heartbeat_exceeded_termination_handler
 *  Handling a heartbeat exceeded condition is done here, as follows:
 *     - dump callstack of all threads except those marked non-killable on termination
 *     - callup exit_handler_cb if configured
 *     - Invoke abort() to ensure proper termination.
 *  For services apart from broker, exit_handler_cb is not set; so we dump all stacks and exit immediately.
 */
ZDO_NOT_OPTIMIZE void zthread_heartbeat_exceeded_termination_handler(int thread_number)
{
    /* First close all listening sockets to prevent new clients from connecting to us */
    zthread_close_socket_fds();
    int killall_threads;

    zpn_process_termination_monitor_flag = 1;
    fprintf(stdout,"zthread_heartbeat_exceeded_termination_handler broker termination monitoring started");

    zthread_heartbeat_thread_number = thread_number;

    zthread_dump_stuck_thread_message(zthread_heartbeat_thread_number);

    if(g_zthread_termination_stuck_zpm_bails_out) {
        fprintf(stdout, "zthread_heartbeat_exceeded_termination_handler stuck scenario");
        while(1);
    }

    if (!zthread_exit_handler_cb) {
        killall_threads = 1;
    } else {
        /*
         * if thread is marked non-killable on termination; and HB exceeded on that;
         *  then, do not invoke the cbs, exit immediately(old behavior)
         */
        killall_threads = zthread_get_dont_kill_on_terminate_flag(thread_number);
    }

    /* Dump the stacks first */
    char out_str[256];
    char msg[256];

    memset(out_str, 0, sizeof(out_str));
    memset(msg, 0, sizeof(msg));
    snprintf(msg, sizeof(msg), "In heartbeat exceeded handler.. thread: %d", thread_number);

    if (abort_enabled) {
           /*
            * earlier, zthread is calling fflush(NULL) before aborting the process, and we are removing fflush(NULL) call before we abort()
            * its okay to remove as fflush(NULL) is added here after we removed aggressive fflush() call from colletion_thread.
            * ref: https://bitbucket.corp.zscaler.com/projects/ET/repos/itasca/pull-requests/229/diff#src/argo/argo_log.c
            *
            * since now the every 0.1s fflush() logic in argo is back to collection_thread, zthread no longer needs to call fflush()
            *
            * for future ref, zthread is important in a way such that its the truth of source telling us how the process is behaving
            * if zthread itself is getting blocked, we are losing control of our process.
            * thus syscall that can block the callershould not be called on zthread
            */
        if (config_enable_stack_traces) {
            snprintf(out_str, sizeof(out_str), "Heartbeat Exceeded, thread %s",
                                    zthread_all_threads[thread_number].stack.thread_name);

            if (state.record_cpu_time_since_last_heartbeat) {
                state.disable_further_heartbeats = 1;
                zthread_dump_stacks(thread_number, out_str, 1, killall_threads);
            } else {
                zthread_dump_stacks(thread_number, out_str, 0, killall_threads);
            }
        }
    }

    if (killall_threads) {
        zthread_exit_or_abort();
    } else {
        if (zthread_exit_handler_cb) {
            zthread_exit_handler_cb(thread_number);
        } else {
            zthread_exit_or_abort();
        }
    }
    if(g_zthread_stuck_termination_bails_out) {
        /* set this so that termination thread kills the broker */
        zpn_process_termination_monitor_flag = 0;
    }
}

ZDO_NOT_OPTIMIZE static void *zthread_thread(struct zthread_info *zthread, void *arg)
{
    int     i;
    int64_t now_s;
    int64_t delta_s;
    int     zthread_monitor_health_state;
    int64_t temp_max_timeout;

    while (1) {
        usleep(500000);
        zthread_heartbeat(zthread);
        now_s = monotime_s();

        if (disable_heartbeat_monitor) {
            continue;
        }
        /* Add some tolerance on zthread_monitor. */
        zthread_monitor_health_state = zthread_monitor_health_check();
        switch (zthread_monitor_health_state) {
            case ZTHREAD_IS_STUCK:
                delta_s = now_s - zthread_monitor_last_heartbeat_monotime_s;
                zthread_heartbeat_exceeded_termination_handler(zthread_thread_num);
                break; // should never be hit

            case ZTHREAD_IS_UNHEALTHY:
                /* ET-31845 don't crash when zthread itself is not getting enough cpu cycles
                 * Only applicable on connector as of now
                 * we will keep moving to the next round, if zthread is unhealthy again, state will move to ZTHREAD_IS_STUCK */
                state.zthread_unhealthy_counter++;

                /* trigger zpn event cpu starvation notify callback,
                   if component has registered for the notification. */
                if (zthread_zpn_event_cpu_starvation_cb &&
                    (zthread_zpn_event_cpu_starvation_cb(zthread_state_get_total_unhealthy_count()) == 0)) {
                    zthread_reset_start_cpu_resource_check();
                }

                fprintf(stdout, "The process is not getting enough cpu cycles, "
                            "please ensure the system setup is correct and use top command to monitor cpu activity\n");
                break;
            default:
                state.zthread_healthy_counter++;
                /* monitor al threads heartbeats */
                for (i = 0; i < zthread_all_threads_count; i++) {
                    delta_s = now_s - zthread_all_threads[i].last_heartbeat_monotime_s;
                    if (zthread_disable) continue;
                    if (disable_heartbeat_monitor) continue;
                    if (zthread_all_threads[i].disable_heartbeat) continue;
                    temp_max_timeout=zthread_get_heartbeat_override_for_thread(i);
                    if (delta_s > temp_max_timeout) {
                        zthread_heartbeat_exceeded_termination_handler(i);
                    }
                }
        }

        /* compute usage avarages for thread groups */
        for (i = 0; i < zthread_group_count; i++) {
            zthread_group_compute_stats(&zthread_groups[i]);
        }
    }

    return NULL;
}

/* create a watchdog for zthread
 * this thread should invoke zero system call */
void *zthread_monitor_thread(struct zthread_info *zthread, void *cookie)
{
    int zthread_health_state;

    while(1) {
        usleep(500000);
        zthread_heartbeat(zthread);
        if (disable_heartbeat_monitor) {
            continue;
        }
        zthread_health_state = zthread_health_check();
        switch (zthread_health_state) {
            case ZTHREAD_IS_STUCK:
                zthread_heartbeat_exceeded_termination_handler(zthread->stack.thread_num);
                break; // should never be hit

            case ZTHREAD_IS_UNHEALTHY:
                state.zthread_monitor_unhealthy_counter++;

                /* trigger zpn event cpu starvation notify callback,
                   if itasca component has registered for it */
                if (zthread_zpn_event_cpu_starvation_cb &&
                    (zthread_zpn_event_cpu_starvation_cb(zthread_state_get_total_unhealthy_count()) == 0)) {
                    zthread_reset_start_cpu_resource_check();
                }

                fprintf(stdout, "The process is not getting enough cpu cycles, "
                                "please ensure the system setup is correct and use top command to monitor cpu activity\n");
                break;
            default:
                state.zthread_monitor_healthy_counter++;
        }
    }
}

static void zthread_monitor_init()
{
    static int zthread_monitor_initialized = 0;
    pthread_t thread;
    int res;
    char msg[128];

    if (zthread_monitor_initialized) return;
    zthread_monitor_initialized = 1;

    /* create another thread that monitors the zthread
     * reference: ET-36871 - we suspect zthread itself stopped working,
     * so adding this additional thread to ensure zthread is always operating normally */
    res = zthread_create_internal(&thread,
                                  zthread_monitor_thread,
                                  NULL,
                                  "zthread_monitor",
                                  ZTHREAD_MAX_MISSING_HEARTBEAT_S,
                                  16*1024*1024,
                                  0,
                                  NULL,
                                  zthread_priority_high);
    if (res) {
        snprintf(msg, sizeof(msg), "Error: Cannot create thread zthread_monitor (%d)", res);
        fprintf(stdout, "%s\n", msg);
        ZTHREAD_LOG(msg);
    }
}

static void zthread_init_internal(void)
{
    static int zthread_initialized = 0;

    pthread_t thread;
    int res;
    char msg[128];
    sigset_t signal_mask;

    if (zthread_initialized) return;
    zthread_initialized = 1;

    zthread_reset_start_cpu_resource_check();

    pthread_key_create(&zthread_key, NULL);

    /* Turn off SIGPIPE... openssl might write to a broken pipe and cause the program to exit.
     * https://www.gnu.org/software/libc/manual/html_mono/libc.html#Pipes-and-FIFOs
     * Writing to a pipe or FIFO that doesn’t have a reading process is treated as an error condition;
     * it generates a SIGPIPE signal, and fails with error code EPIPE if the signal is handled or blocked.
     */
    signal(SIGPIPE, SIG_IGN);
    sigemptyset(&signal_mask);
    sigaddset(&signal_mask, SIGPIPE);
    if (pthread_sigmask(SIG_BLOCK, &signal_mask, NULL) == -1) {
        perror("SIGPIPE");
    }

    if (config_enable_stack_traces) {
        zthread_stack_dump_setup();
    }
    zthread_maximum_heartbeat_delta_s = ZTHREAD_MAX_MISSING_HEARTBEAT_S;
    res = zthread_create_internal(&thread,
                                  zthread_thread,
                                  NULL,
                                  "zthread",
                                  ZTHREAD_MAX_MISSING_HEARTBEAT_S,
                                  16*1024*1024,
                                  0,
                                  NULL,
                                  zthread_priority_high);
    if (res) {
        snprintf(msg, sizeof(msg), "Error: Cannot create thread zthread (%d)", res);
        fprintf(stdout, "%s\n", msg);
        ZTHREAD_LOG(msg);
    }

    if (zthread_env_simple_app) {
        zthread_monitor_init();
    }
}

// glibc backtrace is not async signal safe, it might use heap during init.
// Dry run backtrace and preload/init everything before crash to increase reliability.
static void zthread_backtrace_dry_run_preload() {
#ifndef __FreeBSD__
    void *stack[10];
    int depth = backtrace(stack, 10);
    char **syms = backtrace_symbols(stack, depth);
    free(syms);
#endif
}

void zthread_set_max_stack_bytes(size_t max_stack_bytes_arg)
{
    max_stack_bytes = max_stack_bytes_arg;
}

void zthread_termination_handler_init(void (*exit_handler_fn)(int))
{
    zthread_exit_handler_cb = exit_handler_fn;
}

int zthread_init(char *name, char *version, char *instance, char *core_path_no_trailing_slash,
                 int (*additional_logs_fn)(struct additional_debug_logs *))
{
    zthread_backtrace_dry_run_preload();
    memset(release_app_version, '\0', sizeof(release_app_version));
    snprintf(config_app_name, sizeof(config_app_name), "%s", name);
    snprintf(config_app_version, sizeof(config_app_version), "%s", version);
    snprintf(release_app_version, RELEASE_APP_VERSION_LEN, "RELEASE_VERSION_%s", version);
    snprintf(config_instance, sizeof(config_instance), "%s", instance);
    additional_logs_cb = additional_logs_fn;

    if (core_path_no_trailing_slash) {
        snprintf(stack_trace_filename, sizeof(stack_trace_filename), "%s/%s.stack", core_path_no_trailing_slash, name);
    } else {
        snprintf(stack_trace_filename, sizeof(stack_trace_filename), "%s.stack", name);
    }

    config_enable_stack_traces = 1;

    struct rlimit  rlim;
    if (getrlimit(RLIMIT_NOFILE, &rlim) == 0) {
        zthread_on_crash_do_close_sockets_max_num = (int) rlim.rlim_cur;
    }

    pthread_mutex_lock(&lock);
    zthread_init_internal();
    pthread_mutex_unlock(&lock);

    return 0;
}

void
zthread_set_cloud_name(char* cloud_name)
{
    snprintf(config_cloud_name, sizeof(config_cloud_name), "%s", cloud_name);
}

char *
zthread_get_cloud_name()
{
    return config_cloud_name;
}

void
zthread_set_org_name(char* o_name){
    snprintf(org_name, sizeof(org_name), "%s", o_name);
}

/*
 * Disable heartbeat monitor. Use this only in debuging environment. Useful when you want to attach a debugger
 * and keep exploring a thread without letting zthread kill the process.
 */
void
zthread_disable_heartbeat_monitor(void)
{
    disable_heartbeat_monitor = 1;
}

void zthread_enable_heartbeat_monitor(void) {
    disable_heartbeat_monitor = 0;
}

int zthread_is_heartbeat_monitor_enabled(void) {
    return !disable_heartbeat_monitor;
}

static void zthread_usage_history_init(struct zthread_usage_history* history,
                                         int64_t interval_us,
                                         size_t capacity)
{
    history->interval_us = interval_us;
    history->last_update_us = 0;
    history->capacity = capacity;
    history->write_index = 0;
    history->wrapped = 0;
    history->buf = ZTHREAD_CALLOC(sizeof(struct zthread_usage_history_entry) * capacity);
}

void zthread_usage_history_get_info(struct zthread_usage_history* history,
                                                size_t* entry_count,
                                                int64_t* interval_us,
                                                int64_t* from_ts,
                                                int64_t* to_ts)
{
    size_t idx;
    size_t count = history->wrapped ? history->capacity : history->write_index;
    if (entry_count) {
        *entry_count = count;
    }
    if (interval_us) {
        *interval_us = history->interval_us;
    }
    if (from_ts) {
        if (count == 0) {
            *from_ts = 0;
        } else {
            idx = history->wrapped ? history->write_index : 0;
            *from_ts = history->buf[idx].ru_nowtime;
        }
    }
    if (to_ts) {
        if (count == 0) {
            *to_ts = 0;
        } else {
            idx = (history->write_index == 0) ? (history->capacity - 1) : (history->write_index - 1);
            *to_ts = history->buf[idx].ru_nowtime;
        }
    }
}

void zthread_usage_history_dump(struct zthread_usage_history* history, char* tmp_buf, int size)
{
    size_t count, idx, end_idx;
    int64_t interval_us, from_ts, to_ts;

    char *s = tmp_buf;
    char *e = tmp_buf + size;

    zthread_usage_history_get_info(history, &count, &interval_us, &from_ts, &to_ts);
    s += sxprintf(s, e, "%zu entries, interval %"PRId64" ms, window %"PRId64" ms\n",
                        count, interval_us/1000, (to_ts - from_ts)/1000);
    if (count == 0) return;

    idx = history->wrapped ? history->write_index : 0;
    end_idx = (history->write_index == 0) ? (history->capacity - 1) : (history->write_index - 1);
    while (idx != end_idx) {
        s += sxprintf(s, e, "%2zu now: %"PRId64", utime: %"PRId64", stime: %"PRId64"\n",
                            idx,
                            history->buf[idx].ru_nowtime,
                            history->buf[idx].ru_utime,
                            history->buf[idx].ru_stime);
        idx = (idx + 1) % history->capacity;
    }
    /* the last entry */
    s += sxprintf(s, e, "%2zu now: %"PRId64", utime: %"PRId64", stime: %"PRId64"\n",
                        idx,
                        history->buf[idx].ru_nowtime,
                        history->buf[idx].ru_utime,
                        history->buf[idx].ru_stime);
    idx = (idx + 1) % history->capacity;
}

/*
 * Compute thread CPU usage percentage (cpu time / elasped time * 100)
 */
static void zthread_usage_history_rollup(struct zthread_usage_history* history,
                                         int32_t *pct_utime,
                                         int32_t *pct_stime,
                                         int64_t *start_us,
                                         int64_t *end_us)
{
    size_t first_idx, last_idx;
    if (!history->wrapped && history->write_index < 2) {
        /* have 0 or 1 entries, can't compute diff/elapsed time yet */
        if (pct_utime) *pct_utime = 0;
        if (pct_stime) *pct_stime = 0;
        if (start_us)  *start_us = 0;
        if (end_us)    *end_us = 0;
        return;
    }

    first_idx = history->wrapped ? history->write_index : 0;
    last_idx = (history->write_index == 0) ? (history->capacity - 1) : (history->write_index - 1);
    int64_t elaspsed_us = history->buf[last_idx].ru_nowtime - history->buf[first_idx].ru_nowtime;
    if (elaspsed_us <= 0) {
        char msg[128];
        snprintf(msg, sizeof(msg),
                "time drift backwards? zthread throwing away %zu usage entries:"
                "elasped time is %"PRId64" us (from %"PRId64" to %"PRId64")",
                history->wrapped ? history->capacity : (last_idx - first_idx + 1),
                elaspsed_us,
                history->buf[first_idx].ru_nowtime,
                history->buf[last_idx].ru_nowtime);
        ZTHREAD_LOG(msg);

        /* ditch the history and rebuild over time */
        history->write_index = 0;
        history->last_update_us = 0;
        history->wrapped = 0;

        if (pct_utime) *pct_utime = 0;
        if (pct_stime) *pct_stime = 0;
        if (start_us)  *start_us = 0;
        if (end_us)    *end_us = 0;
        return;
    }

    if (pct_utime) *pct_utime = (history->buf[last_idx].ru_utime - history->buf[first_idx].ru_utime) * 100 / elaspsed_us;
    if (pct_stime) *pct_stime = (history->buf[last_idx].ru_stime - history->buf[first_idx].ru_stime) * 100 / elaspsed_us;
    if (start_us)  *start_us = history->buf[first_idx].ru_nowtime;
    if (end_us)    *end_us = history->buf[last_idx].ru_nowtime;
}

static void zthread_usage_history_update_1(struct zthread_usage_history* history,
                                           struct zthread_rusage *rusage)
{
    int64_t now = epoch_us();

    if (history->last_update_us && (now - history->last_update_us) < history->interval_us) {
        return;
    }
    history->last_update_us = now;
    history->buf[history->write_index].ru_nowtime = rusage->ru_nowtime;
    history->buf[history->write_index].ru_stime = rusage->ru_stime;
    history->buf[history->write_index].ru_utime = rusage->ru_utime;

    if ((history->write_index + 1) == history->capacity) {
        history->wrapped = 1;
    }
    history->write_index = (history->write_index + 1) % history->capacity;
}

/* rusage has been collected, now update history,
 * and compute average cpu usage for the thread */
static void zthread_usage_history_update(struct zthread_info* zthread)
{
    /* record history if needed */
    zthread_usage_history_update_1(&zthread->short_interval_usage_history,
                                   &zthread->rusage);
    zthread_usage_history_update_1(&zthread->long_interval_usage_history,
                                   &zthread->rusage);

    /* rollup historical usage info to zthread->rusage */
    zthread_usage_history_rollup(&zthread->short_interval_usage_history,
                                 &zthread->cpu.pct_s_intv_utime,
                                 &zthread->cpu.pct_s_intv_stime,
                                 &zthread->cpu.s_intv_start_us,
                                 &zthread->cpu.s_intv_end_us);
    zthread_usage_history_rollup(&zthread->long_interval_usage_history,
                                 &zthread->cpu.pct_l_intv_utime,
                                 &zthread->cpu.pct_l_intv_stime,
                                 &zthread->cpu.l_intv_start_us,
                                 &zthread->cpu.l_intv_end_us);
}

/* some attrs are not dumped, enable them as needed but don't go wild */
void zthread_usage_dump(struct zthread_rusage* rusage, struct zthread_cpu_usage* cpu_usage, char* tmp_buf, int size)
{
    char *s = tmp_buf;
    char *e = tmp_buf + size;
    s += sxprintf(s, e, "utime_s: %9"PRId64", ", rusage->ru_utime / US_IN_SEC);
    s += sxprintf(s, e, "stime_s: %9"PRId64", ", rusage->ru_stime / US_IN_SEC);
    s += sxprintf(s, e, "pct_s_intv_utime: %3d, ", cpu_usage->pct_s_intv_utime);
    s += sxprintf(s, e, "pct_s_intv_stime: %3d, ", cpu_usage->pct_s_intv_stime);
    s += sxprintf(s, e, "history_s: %2"PRId64"s, ", (cpu_usage->s_intv_end_us - cpu_usage->s_intv_start_us) / US_IN_SEC);
    s += sxprintf(s, e, "pct_l_intv_utime: %3d, ", cpu_usage->pct_l_intv_utime);
    s += sxprintf(s, e, "pct_l_intv_stime: %3d, ", cpu_usage->pct_l_intv_stime);
    s += sxprintf(s, e, "history_l: %3"PRId64"s, ", (cpu_usage->l_intv_end_us - cpu_usage->l_intv_start_us) / US_IN_SEC);

}


static struct zthread_info *zthread_heartbeat_create(const char*    thread_name,
                                                     int64_t        maximum_heartbeat_delta_s,
                                                     size_t         stack_size,
                                                     int64_t        user_int,
                                                     void*          user_void,
                                                     enum zthread_priority priority)
{
    struct zthread_info *result = NULL;

    if (zthread_all_threads_count >= ZTHREAD_MAX_THREADS) {
        return NULL;
    }

    result = &(zthread_all_threads[zthread_all_threads_count]);
    memset(result, 0, sizeof(struct zthread_info));
    result->stack.thread_num = zthread_all_threads_count;
    snprintf(result->stack.thread_name, sizeof(result->stack.thread_name), "%s", thread_name);
    result->stack_size = stack_size;
    result->maximum_heartbeat_delta_s = maximum_heartbeat_delta_s;
    result->user_int = user_int;
    result->user_void = user_void;
    result->priority = priority;
    result->flags = 0;

    zthread_usage_history_init(&result->short_interval_usage_history,
                                zthread_usage_short_interval_us,
                                NUM_SHORT_INTERVAL_USAGE_HISTORY_ENTRIES);
    zthread_usage_history_init(&result->long_interval_usage_history,
                                zthread_usage_long_interval_us,
                                NUM_LONG_INTERVAL_USAGE_HISTORY_ENTRIES);

    zthread_heartbeat(result);

    zthread_all_threads_count++;

    return result;
}

struct zthread_info *zthread_register_self(const char *thread_name, int64_t maximum_heartbeat_delta_s)
{
    struct zthread_info *result = NULL;

    pthread_mutex_lock(&lock);

    if (zthread_all_threads_count >= ZTHREAD_MAX_THREADS) {
        pthread_mutex_unlock(&lock);
        return NULL;
    }

    result = &(zthread_all_threads[zthread_all_threads_count]);

    result->thread = pthread_self();
    result->stack.thread_num = zthread_all_threads_count;
    snprintf(result->stack.thread_name, sizeof(result->stack.thread_name), "%s", thread_name);
    result->maximum_heartbeat_delta_s = maximum_heartbeat_delta_s;

    pthread_setspecific(zthread_key, result);

    zthread_usage_history_init(&result->short_interval_usage_history,
                                USAGE_HISTORY_SHORT_INTERVAL_US,
                                NUM_SHORT_INTERVAL_USAGE_HISTORY_ENTRIES);
    zthread_usage_history_init(&result->long_interval_usage_history,
                                USAGE_HISTORY_LONG_INTERVAL_US,
                                NUM_LONG_INTERVAL_USAGE_HISTORY_ENTRIES);

    zthread_heartbeat(result);

    zthread_all_threads_count++;

    pthread_mutex_unlock(&lock);

    return result;
}


/*
 * 1. Fill the thread based rusage
 * 2. Fill the thread cpu time delta between previous call and this call
 * 3. Fill the process cpu time - will help when there is a timeout and how much cpu time the process really got.
 */
static void
zthread_fill_rusage_current_thread(struct zthread_info *zthread)
{
    struct rusage process_ru;

#if (defined(__APPLE__) && defined(__MACH__))
    zthread_fill_rusage(RUSAGE_SELF, &zthread->rusage);
#else
    zthread_fill_rusage(RUSAGE_THREAD, &zthread->rusage);
#endif

    if (0 == getrusage(RUSAGE_SELF, &process_ru)) {
        zthread->rusage.process_ru_utime_us = (int64_t)process_ru.ru_utime.tv_sec * (int64_t)1000000;
        zthread->rusage.process_ru_utime_us += (int64_t)process_ru.ru_utime.tv_usec;
        zthread->rusage.process_ru_stime_us = (int64_t)process_ru.ru_stime.tv_sec * (int64_t)1000000;
        zthread->rusage.process_ru_stime_us += (int64_t)process_ru.ru_stime.tv_usec;
    } else {
        zthread->rusage.process_ru_utime_us = -1;
        zthread->rusage.process_ru_stime_us = -1;
    }
}


void zthread_heartbeat(struct zthread_info *zthread)
{
    if (state.disable_further_heartbeats) return;
    if (!zthread) zthread = zthread_self();
    //int ptr;
    //zthread->stack_base = &ptr;
    zthread->heartbeats++;
    int64_t now_s = monotime_s();
    int64_t now_epoch_s = epoch_s();
    zthread_curr_time = now_epoch_s * 1000000l;

    if (zthread->last_heartbeat_monotime_s != now_s) {
        zthread->last_heartbeat_epoch_s = now_epoch_s;
        zthread->last_heartbeat_monotime_s = now_s;
        if (strcmp(zthread->stack.thread_name, "zthread") == 0) {
            zthread_thread_num = zthread->stack.thread_num;
            zthread_last_heartbeat_monotime_s = zthread->last_heartbeat_monotime_s;
        } else if (strcmp(zthread->stack.thread_name, "zthread_monitor") == 0) {
            zthread_monitor_thread_num = zthread->stack.thread_num;
            zthread_monitor_last_heartbeat_monotime_s = zthread->last_heartbeat_monotime_s;
        }
        zthread_fill_rusage_current_thread(zthread);
        zthread_usage_history_update(zthread);
    }
}

void zthread_fill_rusage(int who, struct zthread_rusage *rusage)
{
    int i;
    struct rusage ru;

    i = getrusage(who, &ru);
    if (i) return;

    rusage->ru_nowtime = epoch_us();
    rusage->ru_utime = (int64_t)ru.ru_utime.tv_sec * (int64_t)1000000;
    rusage->ru_utime += (int64_t)ru.ru_utime.tv_usec;
    rusage->ru_stime = (int64_t)ru.ru_stime.tv_sec * (int64_t)1000000;
    rusage->ru_stime += (int64_t)ru.ru_stime.tv_usec;
    rusage->ru_maxrss = ru.ru_maxrss;
    rusage->ru_minflt = ru.ru_minflt;
    rusage->ru_majflt = ru.ru_majflt;
    rusage->ru_inblock = ru.ru_inblock;
    rusage->ru_oublock = ru.ru_oublock;
    rusage->ru_nvcsw = ru.ru_nvcsw;
    rusage->ru_nivcsw = ru.ru_nivcsw;
}

struct zthread_info *zthread_state_get(int *total_zthreads)
{
    *total_zthreads = zthread_all_threads_count;
    return &(zthread_all_threads[0]);
}

struct zthread_group_info *zthread_group_state_get(int *total_groups)
{
    *total_groups = zthread_group_count;
    return &(zthread_groups[0]);
}

const char *zthread_name(void)
{
    struct zthread_info *zthread = pthread_getspecific(zthread_key);
    if (!zthread) {
        return "init_thread";
    } else {
        return zthread->stack.thread_name;
    }
}

void* zthread_get_cookie(void)
{
    struct zthread_info *zthread = pthread_getspecific(zthread_key);
    if (zthread) {
        return zthread->thread_cookie;
    }
    return NULL;
}

struct zthread_info *zthread_self(void)
{
    return pthread_getspecific(zthread_key);
}

/*
 * This function only exists so that thread name appears in stack traces :)
 * Make sure it's not optimized away
 */
ZDO_NOT_OPTIMIZE static void *zthread_launch_2(void *cookie, char *name)
{
    struct zthread_info *zthread = cookie;

    zthread->thread = pthread_self();

#ifdef __linux__
    /* Refer to: https://man7.org/linux/man-pages/man2/nice.2.html
     * Other options tried (to no avail):
     *   * https://man7.org/linux/man-pages/man2/sched_setattr.2.html - first appeared in Linux 3.14
     *   * https://man7.org/linux/man-pages/man3/pthread_setschedparam.3.html - only worked for SCHED_FIFO and SCHED_RR
     */
#define ZTHREAD_PRIORITY_STEP 10
    switch(zthread->priority) {
    case zthread_priority_high:
        zthread->nice = nice(0 - ZTHREAD_PRIORITY_STEP);
        break;
    case zthread_priority_low:
        zthread->nice = nice(ZTHREAD_PRIORITY_STEP);
        break;
    case zthread_priority_normal:
    default:
        zthread->nice = nice(0);
        break;
    }
    if (zthread->nice == -1) { /* nice() call failure */
        fprintf(stderr, "unable to set thread nice value: %s\n", strerror(errno));
        zthread->nice = 0; /* normal nice level */
    }
#endif

    pthread_setspecific(zthread_key, zthread);
    /*
     * Lets not worry about BSD for now to set the thread name.
     */
#if (defined(__APPLE__) && defined(__MACH__))
    pthread_setname_np(name);
#elif (defined(__linux__))
    pthread_setname_np(pthread_self(), name);
#endif

    return (zthread->thread_function)(zthread, zthread->thread_cookie);
}

static void *zthread_launch(void *cookie)
{
    struct zthread_info *zthread = cookie;
    return zthread_launch_2(cookie, zthread->stack.thread_name);
}

static int zthread_create_internal(pthread_t *thread,
                                   zthread_f *thread_func,
                                   void *cookie,
                                   const char *thread_name,
                                   int max_missing_heartbeat_s,
                                   size_t stack_size_bytes,
                                   int64_t user_int,
                                   void *user_void,
                                   enum zthread_priority priority)
{
    pthread_attr_t attr;
    struct zthread_info *zthread;

    if (pthread_attr_init(&attr)) {
        fprintf(stderr, "pthread_attr_init failed! error = %s\n", strerror(errno));
        return -1;
    }
    if (pthread_attr_setstacksize(&attr, stack_size_bytes)) {
        fprintf(stderr, "pthread_attr_setstacksize failed! error = %s\n", strerror(errno));
        return -1;
    }

    zthread = zthread_heartbeat_create(thread_name,
                                       max_missing_heartbeat_s,
                                       stack_size_bytes,
                                       user_int,
                                       user_void,
                                       priority);
    if (!zthread) {
        fprintf(stderr, "zthread_heartbeat_create failed! error = %s\n", strerror(errno));
        return -1;
    }

    zthread->thread_cookie = cookie;
    zthread->thread_function = thread_func;

    return pthread_create(thread, &attr, zthread_launch, zthread);
}

int zthread_create_with_priority(pthread_t *thread,
                                zthread_f *thread_func,
                                void *cookie,
                                const char *thread_name,
                                int max_missing_heartbeat_s,
                                size_t stack_size_bytes,
                                int64_t user_int,
                                void *user_void,
                                enum zthread_priority prio)
{
    int res;
    char msg[128];

    pthread_mutex_lock(&lock);

    zthread_init_internal();

    if (max_stack_bytes && stack_size_bytes > max_stack_bytes) {
        stack_size_bytes = max_stack_bytes;
    }

    res = zthread_create_internal(thread,
                                  thread_func,
                                  cookie,
                                  thread_name,
                                  max_missing_heartbeat_s,
                                  stack_size_bytes,
                                  user_int,
                                  user_void,
                                  prio);
    if (res) {
        snprintf(msg, sizeof(msg), "Error: Cannot create thread %s (%d)", thread_name, res);
        fprintf(stdout, "%s\n", msg);
        ZTHREAD_LOG(msg);
    }
    pthread_mutex_unlock(&lock);

    return res;
}

int zthread_create(pthread_t *thread,
                   zthread_f *thread_func,
                   void *cookie,
                   const char *thread_name,
                   int max_missing_heartbeat_s,
                   size_t stack_size_bytes,
                   int64_t user_int,
                   void *user_void)
{
    return zthread_create_with_priority(thread,
                                        thread_func,
                                        cookie,
                                        thread_name,
                                        max_missing_heartbeat_s,
                                        stack_size_bytes,
                                        user_int,
                                        user_void,
                                        zthread_priority_normal);
}

struct zthread_group_info* zthread_group_get_or_create(const char* name)
{
    struct zthread_group_info *group = NULL;
    char msg[128];

    pthread_mutex_lock(&zthread_group_lock);

    if (zthread_group_count > ZTHREAD_MAX_GROUPS) {
        pthread_mutex_unlock(&zthread_group_lock);

        snprintf(msg, sizeof(msg), "zthread group count %d exceeded allowed max", zthread_group_count);
        ZTHREAD_LOG(msg);
        return NULL;
    }

    /* linear search here... this is not called often. */
    for (int g = 0; g < zthread_group_count; g++) {
        if (!strcmp(zthread_groups[g].name, name)) {
            group = &zthread_groups[g];
            break;
        }
    }
    if (group) {
        pthread_mutex_unlock(&zthread_group_lock);
        return group;
    }

    if (zthread_group_count == ZTHREAD_MAX_GROUPS) {
        pthread_mutex_unlock(&zthread_group_lock);

        snprintf(msg, sizeof(msg), "zthread group count %d reached allowed max", zthread_group_count);
        ZTHREAD_LOG(msg);
        return NULL;
    }

    group = &(zthread_groups[zthread_group_count]);
    memset(group, 0, sizeof(*group));

    group->lock = ZPATH_MUTEX_INIT;
    snprintf(group->name, sizeof(group->name), "%s", name);
    group->thread_count = 0;

    zthread_group_count++;

    pthread_mutex_unlock(&zthread_group_lock);
    return group;
}


int zthread_group_add_thread(struct zthread_group_info* group, int thread_num)
{
    int i;
    if (!group || thread_num < 0 || thread_num >= zthread_all_threads_count) {
        return -1;
    }

    ZPATH_MUTEX_LOCK(&(group->lock), __FILE__, __LINE__);

    /* linear search here... this is not called often. */
    for (i = 0; i < group->thread_count; i++) {
        if (group->thread_nums[i] == thread_num) break;
    }
    if (i == group->thread_count) { /* not found */
        group->thread_nums[group->thread_count] = thread_num;
        group->thread_count++;
    }

    ZPATH_MUTEX_UNLOCK(&(group->lock), __FILE__, __LINE__);
    return 0;
}

/* This function simply takes average of all attributes over threads in the group.
 * Note: average of certain values may not make sense, for now we are doing it blindly.
 */
static void zthread_group_compute_stats(struct zthread_group_info* info)
{
    struct zthread_rusage group_ru;
    memset(&group_ru, 0, sizeof(group_ru));

    /* sum 'em across all threads in the group */
    for (int i = 0; i < info->thread_count; i++) {
        struct zthread_rusage* thread_ru = &(zthread_all_threads[info->thread_nums[i]].rusage);
        group_ru.ru_startime += thread_ru->ru_startime;
        group_ru.ru_nowtime += thread_ru->ru_nowtime;
        group_ru.ru_utime += thread_ru->ru_utime;
        group_ru.ru_stime += thread_ru->ru_stime;
        group_ru.ru_minflt += thread_ru->ru_minflt;
        group_ru.ru_majflt += thread_ru->ru_majflt;
        group_ru.ru_inblock += thread_ru->ru_inblock;
        group_ru.ru_oublock += thread_ru->ru_oublock;
        group_ru.ru_nvcsw += thread_ru->ru_nvcsw;
        group_ru.process_ru_utime_us += thread_ru->process_ru_utime_us;
        group_ru.process_ru_stime_us += thread_ru->process_ru_stime_us;
    }

    struct zthread_cpu_usage group_cpu;
    memset(&group_cpu, 0, sizeof(group_cpu));

    for (int i = 0; i < info->thread_count; i++) {
        struct zthread_cpu_usage* thread_cpu = &(zthread_all_threads[info->thread_nums[i]].cpu);
        group_cpu.pct_s_intv_utime += thread_cpu->pct_s_intv_utime;
        group_cpu.pct_s_intv_stime += thread_cpu->pct_s_intv_stime;
        group_cpu.s_intv_start_us += thread_cpu->s_intv_start_us;
        group_cpu.s_intv_end_us += thread_cpu->s_intv_end_us;
        group_cpu.pct_l_intv_utime += thread_cpu->pct_l_intv_utime;
        group_cpu.pct_l_intv_stime += thread_cpu->pct_l_intv_stime;
        group_cpu.l_intv_start_us += thread_cpu->l_intv_start_us;
        group_cpu.l_intv_end_us += thread_cpu->l_intv_end_us;
    }

    /* take average */
    if (info->thread_count > 1) {
        group_ru.ru_startime /= info->thread_count;
        group_ru.ru_nowtime /= info->thread_count;
        group_ru.ru_utime /= info->thread_count;
        group_ru.ru_stime /= info->thread_count;
        group_ru.ru_minflt /= info->thread_count;
        group_ru.ru_majflt /= info->thread_count;
        group_ru.ru_inblock /= info->thread_count;
        group_ru.ru_oublock /= info->thread_count;
        group_ru.ru_nvcsw /= info->thread_count;
        group_ru.process_ru_utime_us /= info->thread_count;
        group_ru.process_ru_stime_us /= info->thread_count;
        group_cpu.pct_s_intv_utime /= info->thread_count;
        group_cpu.pct_s_intv_stime /= info->thread_count;
        group_cpu.s_intv_start_us /= info->thread_count;
        group_cpu.s_intv_end_us /= info->thread_count;
        group_cpu.pct_l_intv_utime /= info->thread_count;
        group_cpu.pct_l_intv_stime /= info->thread_count;
        group_cpu.l_intv_start_us /= info->thread_count;
        group_cpu.l_intv_end_us /= info->thread_count;
    }

    /* update destination */
    ZPATH_MUTEX_LOCK(&(info->lock), __FILE__, __LINE__);
    memcpy(&info->rusage, &group_ru, sizeof(group_ru));
    memcpy(&info->cpu, &group_cpu, sizeof(group_cpu));
    ZPATH_MUTEX_UNLOCK(&(info->lock), __FILE__, __LINE__);
}


// dladdr is used inside glibc backtrace_symbols_fd which is considered async signal safe.
static int zthread_backtrace_symbolize_addr(const void *addr, struct zthread_stack_trace_sym *sym) {
#ifndef __FreeBSD__
    Dl_info info;
    int res = dladdr(addr, &info);

    if (res) {
        const char *fname = info.dli_fname ? info.dli_fname : "";
        const char *sname = info.dli_sname ? info.dli_sname : "";
        strncpy(sym->file, fname, sizeof(sym->file));
        sym->file[sizeof(sym->file) - 1] = '\0';
        strncpy(sym->name, sname, sizeof(sym->name));
        sym->name[sizeof(sym->name) - 1] = '\0';

        if (addr >= info.dli_saddr) {
            sym->sign = '+';
            sym->offset = (uintptr_t)((char *)addr - (char *)info.dli_saddr);
        } else {
            sym->sign = '-';
            sym->offset = (uintptr_t)((char *)info.dli_saddr - (char *)addr);
        }
        return 1;
    }
#endif
    return 0;
}

void zthread_backtrace_self(void)
{
#ifndef __FreeBSD__
    int i;
    pthread_t self = pthread_self();

    for (i = 0; i < zthread_all_threads_count; i++) {
        if (zthread_all_threads[i].thread == self) {
            //fprintf(stderr, "Getting self backtrace\n");
            zthread_all_threads[i].stack.stack_depth = backtrace(&(zthread_all_threads[i].stack.stack[0]), ZTHREAD_STACK_SIZE);
            break;
        }
    }
#endif
}

/*
 * zthread_should_skip_kill
 *  During a voluntary termination(HB, assert etc.); we do not want to kill certain threads
 *   These threads have been marked with a flag in zthread_info.
 *  If its decided to killall, these flags are irrelevant; else check them and return status.
 *  Return 1 if we need to skip kill of this thread, else 0.
 */
int zthread_should_skip_kill(int killall_threads, int thread_num)
{
    if (killall_threads) {
        return 0;
    } else {
        return zthread_get_dont_kill_on_terminate_flag(thread_num);
    }
}

void zthread_backtrace_all(int killall_threads)
{
#ifndef __FreeBSD__
    int i;
    pthread_t self = pthread_self();

    for (i = 0; i < zthread_all_threads_count; i++) {
        if (zthread_all_threads[i].thread == self) {
            //fprintf(stderr, "Getting self backtrace\n");
            zthread_all_threads[i].stack.stack_depth = backtrace(&(zthread_all_threads[i].stack.stack[0]), ZTHREAD_STACK_SIZE);
        } else {
            if (zthread_should_skip_kill(killall_threads, i)) {
                continue;
            }
            zthread_all_threads[i].stack.stack_depth = 0;
            pthread_kill(zthread_all_threads[i].thread, ZTHREAD_STACK_SIGNAL);
        }
    }
    /* Give them time to do it- but no interlocking */
    sleep(3);
#endif
}

static int zthread_stack_frame_dump_to_file(FILE *fp,
                                            int frame,
                                            const void *addr,
                                            int addr_found,
                                            const struct zthread_stack_trace_sym *sym) {
    char buf[256];

    if (addr_found && sym->name[0] != '\0') {
        snprintf(buf, sizeof(buf), "%s(%s%c%#tx) [%p]", sym->file, sym->name, sym->sign, sym->offset, addr);
    } else if (addr_found && sym->file[0] != '\0') {
        snprintf(buf, sizeof(buf), "%s [%p]", sym->file, addr);
    } else {
        snprintf(buf, sizeof(buf), "[%p]", addr);
    }

    int res = fprintf(fp, "  Stack frame %2d: %s\n", frame, buf);

    return (res <= 0) ? -1 : 0;
}

int zthread_stack_dump_file(FILE *fp, struct zthread_stack_trace *trace)
{
    if (fprintf(fp, "Thread %2d: %s\n", trace->thread_num, trace->thread_name) <= 0) {
        return -1;
    }
    if (trace->stack_depth) {
        int i;
        for (i = 0; i < trace->stack_depth; i++) {
            struct zthread_stack_trace_sym sym;
            int addr_found = zthread_backtrace_symbolize_addr(trace->stack[i], &sym);
            if (zthread_stack_frame_dump_to_file(fp, i, trace->stack[i], addr_found, &sym)) {
                return -1;
            }
        }
    } else {
        if (fprintf(fp, "  No Stack\n") <= 0) {
            return -1;
        }
    }
    return 0;
}

int zthread_stack_dump_file_to_file(FILE *fp_in, FILE *fp_out)
{
    uint64_t magic_number;
    struct dump_header header;
    int i;
    time_t when;

    struct tm tm1, tm2;
    char time_str1[100], time_str2[100];

    if (fread(&magic_number, sizeof(magic_number), 1, fp_in) != 1) {
        fprintf(stderr, "Could not read magic number\n");
        return -1;
    }
    if (magic_number != STACK_DUMP_MAGIC_NUMBER) {
        fprintf(stderr, "Macic number does not matcn\n");
        return -1;
    }
    if (fread(&header, sizeof(header), 1, fp_in) != 1) {
        fprintf(stderr, "Could not read dump header\n");
        return -1;
    }

    /* Make sure strings are NULL terminated */
    header.identifier[sizeof(header.identifier) - 1] = 0;
    header.name[sizeof(header.name) - 1] = 0;
    header.version[sizeof(header.version) - 1] = 0;
    header.platform[sizeof(header.platform) - 1] = 0;
    header.details[sizeof(header.details) - 1] = 0;
    header.cloud_name[sizeof(header.cloud_name) - 1] = 0;
    header.org_name[sizeof(header.org_name) - 1] = 0;
    when = header.epoch_s;

    if (fprintf(fp_out,
                "Application         : %s\n"
                "Application Version : %s\n"
                "Platform.Arch       : %s\n"
                "Cloud               : %s\n"
                "Instance Identifier : %s\n"
                "Time (UTC)          : %s"
                "Time (Local)        : %s"
                "PID                 : %d\n"
                "Error Details       : %s\n"
                "Customer Name       : %s\n"
                "Threads             : %d\n"
                "Errored Thread      : %d\n",
                header.name,
                header.version,
                header.platform,
                header.cloud_name,
                header.identifier,
                asctime_r(gmtime_r(&when, &tm1), time_str1),
                asctime_r(localtime_r(&when, &tm2), time_str2),
                header.pid,
                header.details,
                header.org_name,
                header.thread_count,
                header.thread_id) <= 0) {
        fprintf(stderr, "Could not write header information\n");
        return -1;
    }
    if (header.cpu_time_recorded) {
        fprintf(fp_out, "Process delta, thread|monotime_s|maxmimum_heartbeat_s|clock_us|user_time_us|system_time_us :");
        for (i = 0; i < header.thread_count; i++) {
            if ((long long)(header.delta_process_monotime_s[i]) > (long long)(header.maximum_heartbeat_delta_s[i])) {
                fprintf(fp_out, "[*%d|%lld|%lld|%lld|%lld|%lld*] ", i, (long long)header.delta_process_monotime_s[i], (long long)header.maximum_heartbeat_delta_s[i],
                    (long long)header.delta_process_clock_time_us[i], (long long)header.delta_process_user_time_us[i],
                    (long long)header.delta_process_system_time_us[i]);
            } else {
                fprintf(fp_out, "[%d|%lld|%lld|%lld|%lld|%lld] ", i, (long long)header.delta_process_monotime_s[i], (long long)header.maximum_heartbeat_delta_s[i],
                    (long long)header.delta_process_clock_time_us[i], (long long)header.delta_process_user_time_us[i],
                    (long long)header.delta_process_system_time_us[i]);
            }
        }
        fprintf(fp_out, "\n");
    }

    for (i = 0; i < header.thread_count; i++) {
        struct zthread_stack_trace trace;
        if (fread(&trace, sizeof(trace), 1, fp_in) != 1) {
            fprintf(stderr, "Could not read stack for thread %d\n", i);
            return -1;
        }

        if (fprintf(fp_out, "Thread %2d: %s\n", trace.thread_num, trace.thread_name) <= 0) {
            return -1;
        }

        for (int j = 0; j < trace.stack_depth; ++j) {
            uint8_t has_sym;
            if (fread(&has_sym, sizeof(has_sym), 1, fp_in) != 1) {
                fprintf(stderr, "Could not read stack frame symbol indicator for thread %d\n", i);
                return -1;
            }

            struct zthread_stack_trace_sym sym;
            int addr_found = 0;
            if (has_sym) {
                if (fread(&sym, sizeof(sym), 1, fp_in) != 1) {
                    fprintf(stderr, "Could not read stack frame symbol for thread %d\n", i);
                    return -1;
                }
            } else {
                addr_found = zthread_backtrace_symbolize_addr(zthread_relocate_addr(trace.stack[j], 0), &sym);
            }

            if (zthread_stack_frame_dump_to_file(fp_out, j, trace.stack[j], has_sym | addr_found, &sym)) {
                fprintf(stderr, "Could not dump stack frame\n");
                return -1;
            }
        }

        if (trace.stack_depth == 0) {
            if (fprintf(fp_out, "  No Stack\n") <= 0) {
                return -1;
            }
        }
    }

    if (header.additional_dbg_log_bytes) {
        char *additional_logs = ZTHREAD_CALLOC(header.additional_dbg_log_bytes + 1);
        if (fread(additional_logs, header.additional_dbg_log_bytes, 1, fp_in) != 1) {
            fprintf(stderr, "Could not read additional debug information\n");
            ZTHREAD_FREE(additional_logs);
            return -1;
        }
        fprintf(fp_out, "Additional logs:\n======================================\n"
                        "%s\n", additional_logs);
        ZTHREAD_FREE(additional_logs);
    }

    return 0;
}

void zthread_stack_dump_file_all(FILE *fp)
{
    int i;
    for (i = 0; i < zthread_all_threads_count; i++) {
        zthread_stack_dump_file(fp, &(zthread_all_threads[i].stack));
    }
}

/*
 * record_cpu_time_since_last_heartbeat - we should record the user and system cpu time spent after the last
 * heartbeat. This will help the to identify - the system does a hearbeat timeout, because a process didn't get enough
 * cpu slice vs thread gets into a bad loop and didn't do a heartbeat. It can also help find why zthread is sometimes
 * doing heartbeat.
 */
static void zthread_dump_stacks(int thread_number, char *sig_info,
                                int record_cpu_time_since_last_heartbeat,
                                int killall_threads)
{
    struct dump_header header;
    uint64_t magic_number;
    int sock;
    int i;

    /* Get and write stack trace */
    magic_number = STACK_DUMP_MAGIC_NUMBER;
    memset(&header, 0, sizeof(header));
    header.thread_count = zthread_all_threads_count;
    header.thread_id = thread_number;
    header.epoch_s = time(NULL);
    header.pid = getpid();
    header.additional_dbg_log_bytes = 0;
    snprintf(header.name, sizeof(header.name), "%s", config_app_name);
    snprintf(header.version, sizeof(header.version), "%s", config_app_version);
    snprintf(header.details, sizeof(header.details), "%s", sig_info);
    snprintf(header.identifier, sizeof(header.identifier), "%s", config_instance);
    snprintf(header.platform, sizeof(header.platform), "%s.%d.%s", ZPATH_PLATFORM_NAME, ZPATH_PLATFORM_VERSION, ZPATH_PLATFORM_ARCH);
    snprintf(header.cloud_name, sizeof(header.cloud_name), "%s", config_cloud_name);
    snprintf(header.org_name, sizeof(header.org_name), "%s", org_name);

    zthread_backtrace_all(killall_threads);

    if (record_cpu_time_since_last_heartbeat) {
        struct rusage           process_ru;
        int64_t                 process_ru_utime_us;
        int64_t                 process_ru_stime_us;
        int64_t                 now_epoch_us;
        int64_t                 now_monotime_s;
        int                     iter;
        /*
         * RUSAGE_SELF is going to return thread for osx (no way to get for process wide). it returns process
         * wide stats for linux - which is what we care more for.
         */
        if (0 == getrusage(RUSAGE_SELF, &process_ru)) {
            now_epoch_us = epoch_us();
            now_monotime_s = monotime_s();
            process_ru_utime_us = (int64_t)process_ru.ru_utime.tv_sec * (int64_t)1000000;
            process_ru_utime_us += (int64_t)process_ru.ru_utime.tv_usec;
            process_ru_stime_us = (int64_t)process_ru.ru_stime.tv_sec * (int64_t)1000000;
            process_ru_stime_us += (int64_t)process_ru.ru_stime.tv_usec;
            for (iter = 0; iter < zthread_all_threads_count; iter++) {
                header.maximum_heartbeat_delta_s[iter] = zthread_all_threads[iter].maximum_heartbeat_delta_s;
                header.delta_process_monotime_s[iter] = now_monotime_s - zthread_all_threads[iter].last_heartbeat_monotime_s;
                header.delta_process_clock_time_us[iter] = now_epoch_us - zthread_all_threads[iter].rusage.ru_nowtime;
                header.delta_process_user_time_us[iter] = process_ru_utime_us - zthread_all_threads[iter].rusage.process_ru_utime_us;
                header.delta_process_system_time_us[iter] = process_ru_stime_us - zthread_all_threads[iter].rusage.process_ru_stime_us;
            }
            header.cpu_time_recorded = 1;
        } else {
            header.cpu_time_recorded = 0;
        }
    } else {
        header.cpu_time_recorded = 0;
    }

    unlink(stack_trace_filename);
    sock = creat(stack_trace_filename, S_IRUSR | S_IWUSR);
    if (sock >= 0) {
        int res;
        struct additional_debug_logs dbg_logs_data;
        memset(&dbg_logs_data, 0, sizeof(dbg_logs_data));
        if (additional_logs_cb) {
            res = additional_logs_cb(&dbg_logs_data);
            if (res == 0) {
                header.additional_dbg_log_bytes = dbg_logs_data.num_bytes;
            }
        }
        write_nowarn(sock, &magic_number, sizeof(magic_number));
        write_nowarn(sock, &header, sizeof(header));

        for (i = 0; i < header.thread_count; i++) {
            struct zthread_stack_trace *trace = &zthread_all_threads[i].stack;
            zthread_relocate_stack(trace, 1);
            write_nowarn(sock, trace, sizeof(*trace));
            zthread_relocate_stack(trace, 0);

            for (int j = 0; j < trace->stack_depth; ++j) {
                uint8_t has_sym;
                struct zthread_stack_trace_sym sym;
                int addr_found = zthread_backtrace_symbolize_addr(trace->stack[j], &sym);

                if (addr_found && sym.file[0] != '\0' && strstr(sym.file, ".so")) {
                    // Write symbols for shared libs only.
                    has_sym = 1;
                    write_nowarn(sock, &has_sym, sizeof(has_sym));
                    write_nowarn(sock, &sym, sizeof(sym));
                } else {
                    has_sym = 0;
                    write_nowarn(sock, &has_sym, sizeof(has_sym));
                }
            }
        }
        if (header.additional_dbg_log_bytes) {
            write_nowarn(sock, dbg_logs_data.debug_logs, header.additional_dbg_log_bytes);
        }
        close(sock);
    };
}

/*
 * Even before ZTHREAD_STACK_SIGNAL is raised, fflush is called which will mean all the logs are flushed out.
 * But for any other signals, no luck - we might lose the last bit of the logs!
 */
static void zthread_signal_handler(int sig_num, siginfo_t *sig_info, void *data)
{
    /* First close all listening sockets to prevent new clients from connecting to us */
    zthread_close_socket_fds();

    char out_text[128];

    if (sig_num == SIGABRT && !dump_stacks_on_abort)
        goto done;

    if (sig_num == ZTHREAD_STACK_SIGNAL) {
        zthread_backtrace_self();
        while (1) sleep(1); // Do not return from signal handler in order to preserve program state in core.
    }

    switch (sig_num) {
    case SIGILL:
        snprintf(out_text, sizeof(out_text), "ILLEGAL INSTRUCTION");
        break;
    case SIGFPE:
        snprintf(out_text, sizeof(out_text), "FLOATING POINT EXCEPTION");
        break;
    case SIGSEGV:
        snprintf(out_text, sizeof(out_text), "SEGMENTATION VIOLATION");
        break;
    case SIGBUS:
        snprintf(out_text, sizeof(out_text), "BUS ERROR");
        break;
    case SIGABRT:
        snprintf(out_text, sizeof(out_text), "ABORT");
        break;
    default:
        return;
    }

    /*
     * fatal signal hit the service
     * mask SIGABRT to avoid possible double shot from zpm
    */
    sigset_t sigblock;
    sigaddset(&sigblock, SIGABRT);
    if(sigprocmask(SIG_BLOCK, &sigblock, NULL) != 0) {
		fprintf(stderr,"failed to mask SIGABRT, error : %s", strerror(errno));
    }

    int i;
    for (i = 0; i < zthread_all_threads_count; i++) {
        if (pthread_self() == zthread_all_threads[i].thread) break;
    }
    if (i == zthread_all_threads_count) i = -1;

    zthread_dump_stacks(i, out_text, 0, 1);

done:;

    zthread_on_crash_do();

    // Re-raise the signal.
    struct sigaction sig = {};
    sig.sa_handler = SIG_DFL;
    sigaction(sig_num, &sig, NULL);
    raise(sig_num);
}


static int zthread_stack_dump_setup(void)
{
    struct sigaction sig;

    memset(&sig, 0, sizeof(sig));

    sig.sa_sigaction = zthread_signal_handler;
    sig.sa_flags = SA_SIGINFO;

    if (sigaction(ZTHREAD_STACK_SIGNAL, &sig, NULL)) {
        fprintf(stderr, "Could not install signal handler\n");
        return -1;
    }

    if (sigaction(SIGILL, &sig, NULL)) {
        fprintf(stderr, "Could not install signal handler\n");
        return -1;
    }

    if (sigaction(SIGFPE, &sig, NULL)) {
        fprintf(stderr, "Could not install signal handler\n");
        return -1;
    }

    if (sigaction(SIGSEGV, &sig, NULL)) {
        fprintf(stderr, "Could not install signal handler\n");
        return -1;
    }

    if (sigaction(SIGBUS, &sig, NULL)) {
        fprintf(stderr, "Could not install signal handler\n");
        return -1;
    }

    if (sigaction(SIGABRT, &sig, NULL)) {
        fprintf(stderr, "Could not install signal handler\n");
        return -1;
    }

    return 0;
}

/*
 * Returns 0 if it did the stack dump, non-zero otherwise
 */
int zthread_do_stack_dump(int *argc, char *argv[])
{
    char *filename;
    if (!zpath_pull_arg(argc, argv, "--print-core", &filename)) {
        int res;
        FILE *fp;
        fp = fopen(filename, "r");
        if (!fp) {
            fprintf(stdout,"ERROR: Could not open core dump file %s\n", filename);
            fflush(stdout);
            sleep(1);
            exit(1);
        }
        res = zthread_stack_dump_file_to_file(fp, stdout);
        if (res){
            fprintf(stdout,"ERROR: zthread_do_stack_dump:Exiting\n");
            fflush(stdout);
            sleep(1);
            exit(1);
        }
        exit(0);
    }
    if (!zpath_pull_arg(argc, argv, "--print-core-force", &filename)) {
        int res;
        FILE *fp;
        fp = fopen(filename, "r");
        if (!fp) {
            fprintf(stdout,"ERROR: Could not open core dump file %s\n", filename);
            fflush(stdout);
            sleep(1);
            exit(1);
        }
        res = zthread_stack_dump_file_to_file(fp, stdout);
        if (res){
            fprintf(stdout,"ERROR: zthread_do_stack_dump:Exiting\n");
            fflush(stdout);
            sleep(1);
            exit(1);
        }
        exit(0);
    }
    return -1;
}

/* Data is assumed to start with a magic number */
static struct dump_header *zthread_parse_dump_header(void *data, size_t data_len) {
    struct dump_header *hdr;
    size_t header_and_magic_size = sizeof(uint64_t) + sizeof(struct dump_header);
    uint64_t *magic = data;
    if (data_len < header_and_magic_size) {
        return NULL;
    }
    if ((*magic) != STACK_DUMP_MAGIC_NUMBER) {
        return NULL;
    }

    hdr = (struct dump_header *)(&(magic[1]));

    return hdr;
}

static int zthread_stack_string_field_is_valid(char *field, size_t sz) {
    size_t i;
    for (i = 0; i < sz && isprint(field[i]); i++);

    if (i == 0 || i >= sz || field[i] != 0)
        return 0;

    return 1;
}

/*
 * Returns the software version of the stack trace in memory
 */
char *zthread_stack_version(void *data, size_t data_len)
{
    struct dump_header *hdr = zthread_parse_dump_header(data, data_len);
    if (!hdr)
        return NULL;
    if (!zthread_stack_string_field_is_valid(hdr->version, sizeof(hdr->version)))
        return NULL;

    return hdr->version;
}

char *zthread_stack_platform(void *data, size_t data_len) {
    struct dump_header *hdr = zthread_parse_dump_header(data, data_len);
    if (!hdr)
        return NULL;
    if (!zthread_stack_string_field_is_valid(hdr->platform, sizeof(hdr->platform)))
        return NULL;

    return hdr->platform;
}

int zthread_get_self_thread_id(void)
{
    int thread_id;
    for (thread_id = 0; thread_id < zthread_all_threads_count; thread_id++) {
        if (pthread_self() == zthread_all_threads[thread_id].thread) {
            break;
        }
    }
    if (thread_id == zthread_all_threads_count) {
        thread_id = -1;
    }

    return thread_id;
}

ZDO_NOT_OPTIMIZE void zthread_assert_text(char *text)
{
    char    out_str[4096];

    memset(out_str, 0, sizeof(out_str));
    if (abort_enabled) {
        if (config_enable_stack_traces) {
            snprintf(out_str, sizeof(out_str), "Assertion failure!\nAssertion           : %s\n", text);
            int offending_thread_id;
            for (offending_thread_id = 0; offending_thread_id < zthread_all_threads_count; offending_thread_id++) {
                if (pthread_self() == zthread_all_threads[offending_thread_id].thread) break;
            }
            zthread_dump_stacks(offending_thread_id, out_str, 0, 1);
        }
        zthread_abort_no_stack_dump();
    } else {
        fprintf(stdout, "zthread_assert_text: exiting\n");
        sleep(1);
        exit(0);
    }
}

/*
 * zthread_assert_dump_stacks
 *  this routine just dumps stacks for all threads; actual assert is done separately
 */
__inline__ void zthread_assert_dump_stacks(int offending_thread_id,
                                                 char *text,
                                                 int killall_threads)
{
    char    out_str[4096];

    memset(out_str, 0, sizeof(out_str));
    if (abort_enabled) {
        if (config_enable_stack_traces) {
            snprintf(out_str, sizeof(out_str), "Assertion failure!\nAssertion           : %s\n", text);
            zthread_dump_stacks(offending_thread_id, out_str, 0, killall_threads);
        }
    }
}

void zthread_record_cpu_time_since_last_heartbeat()
{
    state.record_cpu_time_since_last_heartbeat = 1;
}

void zthread_set_env_simple_app()
{
    zthread_env_simple_app = 1;
}


void zthread_stats_get(struct zthread_stats* stats)
{
    if (!stats) {
        return;
    }

    stats->zthread_healthy_counter = state.zthread_healthy_counter;
    stats->zthread_unhealthy_counter = state.zthread_unhealthy_counter;
    stats->zthread_monitor_healthy_counter = state.zthread_monitor_healthy_counter;
    stats->zthread_monitor_unhealthy_counter = state.zthread_monitor_unhealthy_counter;
    stats->zthread_time_skipped = state.zthread_time_skipped;
    stats->zthread_time_skipped_exceed_max = state.zthread_time_skipped_exceed_max;
}

char *zthread_get_thread_cpu_usage_log()
{
    int idx = 0;
    char *start, *end;
    char *buff = NULL;

    /* gets freed from the caller */
    buff = ZTHREAD_CALLOC(ZTHREAD_BUFFER_SIZE);
    start = buff;
    end = start + ZTHREAD_BUFFER_SIZE;
    struct zthread_info *thread_info;

    for (idx = 0; idx < zthread_all_threads_count; idx++)
    {
        thread_info = &(zthread_all_threads[idx]);
        int time = thread_info->cpu.pct_s_intv_stime + thread_info->cpu.pct_s_intv_utime;
        if(time < 0) {
            time = 0;
        }
        start += sxprintf(start, end, "%s=%d%% ", thread_info->stack.thread_name, time);
    }
    return buff;
}

int zthread_state_get_total_unhealthy_count()
{
    int64_t count = 0;

    int64_t zthread_unhealthy_count = (state.zthread_unhealthy_counter > state.zthread_unhealthy_counter_saved) ? (state.zthread_unhealthy_counter - state.zthread_unhealthy_counter_saved) : 0;

    int64_t zthread_monitor_unhealthy_count = (state.zthread_monitor_unhealthy_counter > state.zthread_monitor_unhealthy_counter_saved) ? (state.zthread_monitor_unhealthy_counter - state.zthread_monitor_unhealthy_counter_saved) : 0;
    count = zthread_unhealthy_count + zthread_monitor_unhealthy_count;
    if (count < 0) { count = 0; }
    return (count > INT_MAX) ? INT_MAX : (int)count;
}

void zthread_reset_start_cpu_resource_check()
{
    state.zthread_unhealthy_counter_saved = state.zthread_unhealthy_counter;
    state.zthread_monitor_unhealthy_counter_saved = state.zthread_monitor_unhealthy_counter;
}

int zthread_register_zpn_event_cpu_starvation_cb(int (*cb)(int))
{
    zthread_zpn_event_cpu_starvation_cb = cb;
    return 0;
}


void zthread_set_heartbeat_override(int override_timeout) {
    zthread_heartbeat_override_timeout  = override_timeout;
}

void zthread_get_heartbeat_override(int *override_timeout) {
    *override_timeout=zthread_heartbeat_override_timeout;
}

int64_t zthread_get_heartbeat_override_for_thread(int thread_id) {
    int64_t temp_max_timeout=zthread_all_threads[thread_id].maximum_heartbeat_delta_s;
    if ( (zthread_heartbeat_override_timeout > 0) &&
        (zthread_heartbeat_override_timeout > zthread_all_threads[thread_id].maximum_heartbeat_delta_s)) {
        temp_max_timeout = zthread_heartbeat_override_timeout;
    }
    return temp_max_timeout;
}

void zthread_set_heartbeat_override_for_thread(int thread_num, int64_t max_hearbeat_value) {
    if (!zthread_is_valid_thread_number(thread_num)) {
        return;
    }
    zthread_all_threads[thread_num].maximum_heartbeat_delta_s = max_hearbeat_value;
}
