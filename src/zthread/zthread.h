/*
 * zthread.h. Copyright (C) 2014 Zscaler, Inc. All Rights Reserved
 */

#ifndef __ZTHREAD_H__
#define __ZTHREAD_H__

#include <signal.h>
#include "zpath_misc/zpath_misc.h"

#define ZTHREAD_BUFFER_SIZE 8192
#define ZTHREAD_MAX_THREADS 1024
#define ZTHREAD_MAX_GROUPS 256
#define ZTHREAD_STACK_SIZE 48

#define SOCK_FD_ARRAY_MAX 10

struct zthread_info;
struct zthread_group_info;

extern struct zpath_allocator zthread_allocator;
extern int zthread_disable;

#define ZTHREAD_MALLOC(x) zpath_malloc(&zthread_allocator, (x), __LINE__, __FILE__)
#define ZTHREAD_FREE(x) zpath_free((x), __LINE__, __FILE__)
#define ZTHREAD_CALLOC(x) zpath_calloc(&zthread_allocator, (x), __LINE__, __FILE__)
#define ZTHREAD_STRDUP(x, l) zpath_strdup(&zthread_allocator, (x), (l), __LINE__, __FILE__)

#ifdef __APPLE__
#define ZTHREAD_STACK_SIGNAL (SIGUSR1)
#else
#define ZTHREAD_STACK_SIGNAL (SIGRTMIN + 0)
#endif

typedef void *(zthread_f)(struct zthread_info *zthread,
                          void *cookie);

typedef void (zthread_log_f)(const char *message);


/*
 * The only reason this really differs from struct rusage is that I
 * didn't want structures for ru_utime and ru_stime. I can also put a
 * current timestamp in there.
 *
 * ru_startime;        thread or process start time, in epoch_us.
 * ru_nowtime;         current time, epoch, in us
 * ru_utime;           user time used
 * ru_stime;           system time used
 * ru_maxrss;          max resident set size
 * ru_minflt;          page reclaims
 * ru_majflt;          page faults
 * ru_inblock;         block input operations
 * ru_oublock;         block output operations
 * ru_nvcsw;           voluntary context switches
 * ru_nivcsw;          involuntary context switches
 * process_ru_*time    all the above details are for thread; process is for the whole process
 */
struct zthread_rusage {    /* _ARGO: object_definition */
    int64_t ru_startime;   /* _ARGO: integer */
    int64_t ru_nowtime;    /* _ARGO: integer */
    int64_t ru_utime;      /* _ARGO: integer */
    int64_t ru_stime;      /* _ARGO: integer */
    int64_t ru_maxrss;     /* _ARGO: integer */
    int64_t ru_minflt;     /* _ARGO: integer */
    int64_t ru_majflt;     /* _ARGO: integer */
    int64_t ru_inblock;    /* _ARGO: integer */
    int64_t ru_oublock;    /* _ARGO: integer */
    int64_t ru_nvcsw;      /* _ARGO: integer */
    int64_t ru_nivcsw;     /* _ARGO: integer */
    int64_t process_ru_utime_us;      /* _ARGO: integer */
    int64_t process_ru_stime_us;      /* _ARGO: integer */
};

/*
 * thread cpu usage info
 * pct_utime_*_intv         % of user CPU time over elasped time in the tracked interval
 * pct_stime_*_intv         % of syst CPU time over elasped time in the tracked interval
 * _intv_start_us/end_us    time window where pct_utime and pct_stime are tracked
 */
struct zthread_cpu_usage {
    int32_t pct_s_intv_utime;
    int32_t pct_s_intv_stime;
    int64_t s_intv_start_us;
    int64_t s_intv_end_us;
    int32_t pct_l_intv_utime;
    int32_t pct_l_intv_stime;
    int64_t l_intv_start_us;
    int64_t l_intv_end_us;
};

/*
 * Thread dump info, for stack dumps
 */
struct zthread_stack_trace {
    char thread_name[128];
    int thread_num;
    int stack_depth;
    void *stack[ZTHREAD_STACK_SIZE];
};

/*
 * Currently we are only tracking CPU over a period of time.
 * Add other attributes as needed.
 */
struct zthread_usage_history_entry {
    int64_t ru_nowtime;    /* time of collection */
    int64_t ru_utime;      /* system time for this thread */
    int64_t ru_stime;      /* user time for this thread */
};

extern int64_t zthread_usage_short_interval_us;
extern int64_t zthread_usage_long_interval_us;

/* A circular buffer maintaining up to <capacity> zthread_usage_history_entry's */
struct zthread_usage_history {
    size_t capacity;
    int64_t interval_us;
    int64_t last_update_us;
    size_t write_index;
    int wrapped;
    struct zthread_usage_history_entry* buf;
};

/* thread priority */
enum zthread_priority {
    zthread_priority_normal = 0,
    zthread_priority_high = 1,
    zthread_priority_low = 2
};

/* zthread_info flags bits */
/*
 * DO_NOT_KILL_ON_TERMINATION: this flag indicates,
 *   on term. handler, we dont want to kill it; they have some purpose to be kept alive
 */
#define ZTHREAD_INFO_FLAG_DO_NOT_KILL_ON_TERMINATION    (uint64_t)0x00000001

/*
 * Super simple thread watchdog. Threads can register/deregister
 * themselves along with a minimum heartbeat rate. If any thread does
 * not tickle its heartbeat at least as often as specified, then this
 * routine will abort the application.
 *
 * Eventually someone ought to make it so threads can be removed. For
 * the time being, they cannot.
 *
 * This library does not rely on any locking to perform its heartbeat
 * checks. It only uses locking to do heartbeat allocations.
 *
 * rusage is updated on every heartbeat.
 *
 * user_state is available for applications to use as they see fit.
 * user_void is also available for apps to use as they see fit.
 * priority: low/normal/high for thread scheduling, translates to thread's nice value.
 * nice: the actual nice value of the os thread.
 */
struct zthread_info {
    pthread_t thread;
    enum zthread_priority priority;
    int nice;
    size_t stack_size;
    int64_t last_heartbeat_epoch_s;
    int64_t last_heartbeat_monotime_s;
    int64_t maximum_heartbeat_delta_s;
    void *thread_cookie;
    zthread_f *thread_function;
    int64_t heartbeats;
    int64_t user_int;
    void *user_void;
    uint64_t flags;
    struct zthread_rusage rusage;
    struct zthread_cpu_usage cpu;
    struct zthread_stack_trace stack;
    struct zthread_usage_history short_interval_usage_history;
    struct zthread_usage_history long_interval_usage_history;
    int disable_heartbeat;
};

struct zthread_group_info {
    zpath_mutex_t lock;
    char name[128];

    /* All thread numbers in this group.
     * Note: these are the 'zthread' thread number.
     */
    int thread_nums[ZTHREAD_MAX_THREADS];
    int thread_count;

    /* Average values for all threads in this group.
     * Some attributes may not make physical sense.
     */
    struct zthread_rusage rusage;
    struct zthread_cpu_usage cpu;
};

#define MAX_ADDITIONAL_DEBUG_LOGS_BYTES  8192

struct additional_debug_logs {
    char debug_logs[MAX_ADDITIONAL_DEBUG_LOGS_BYTES];
    int num_bytes;
};

/*
 * following stats helps to keep track zthread's behavior.
 * ie, to know whether zthread got cpu cycle to run, is vmotion happening on this vm .. etc
 *
 * note, all counters there are monotonic increasing counters.
 */
struct zthread_stats {
    int64_t zthread_time_skipped;
    int64_t zthread_monitor_time_skipped;

    /*tell number of consecutive vm time skew exceed max allowance, might not be useful as at this point we are restarting our process*/
    int64_t zthread_time_skipped_exceed_max;
    int64_t zthread_monitor_time_skipped_exceed_max;

    int64_t zthread_healthy_counter;
    int64_t zthread_unhealthy_counter;
    int64_t zthread_monitor_healthy_counter;
    int64_t zthread_monitor_unhealthy_counter;
    int64_t zthread_test_healthy_counter;
    int64_t zthread_test_unhealthy_counter;
};

/*
 * on crash do extra actions specified by following types, which is
 * configured by config-override
 */
enum zthread_on_crash_do_feature_types {
    zthread_on_crash_do_no_action = 0,
    zthread_on_crash_do_no_coredump = 1,
    zthread_on_crash_do_close_all_sockets = 2
};
extern int64_t zthread_on_crash_do_feature;

/*
 * get log content of cpu stats for all threads
 */
char *zthread_get_thread_cpu_usage_log(void);

/*
 * config-override helper for extra actions on crash
 */
void zthread_on_crash_do_config_override_monitor_callback(const int64_t *config_value, int64_t impacted_gid);

/*
 * beat thread's heart. That sounds morbid. Oh well.
 *
 * If zthread is NULL, assumes current (running) thread.
 */
void zthread_heartbeat(struct zthread_info *zthread);

/*
 * Set whether abort (core) is enabled or not
 */
void zthread_set_abort(int enabled);

/*
 * Set whether abort (core) is enabled or not
 */
void zthread_set_log(zthread_log_f *log_f);

/*
 * Fill in zthread_rusage structure with current stats, via a call and
 * translation of getrusage
 */
void zthread_fill_rusage(int who, struct zthread_rusage *rusage);

/*
 * Get all currently registered threads' state.
 *
 * DO NOT FREE returned state.
 *
 * Returns the total number of threads represented by the returned array
 */
struct zthread_info *zthread_state_get(int *total_zthreads);

/*
 * Register self as a thread. This is for cases where zthread isn't
 * launching a thread. (i.e. main() thread)
 */
struct zthread_info *zthread_register_self(const char *thread_name, int64_t maximum_heartbeat_delta_s);


/*
 * Create and execute specified thread. The thread must perform a
 * heartbeat at least once per max_missing_heartbeat_s.
 */
int zthread_create(pthread_t *thread,
                   zthread_f *thread_func,
                   void *cookie,
                   const char *thread_name,
                   int max_missing_heartbeat_s,
                   size_t stack_size_bytes,
                   int64_t user_int,
                   void *user_void);

/*
 * Same as zthread_create, but allows coarse-grained thread priority,
 * i.e. zthread_priority_normal/low/high.
 *
 * Note: actual 'nice' value can be queried with /thread/status command.
 * - negative nice values means higher than normal priority
 * - positive nice values means lower than normal priority
 * - default is 0.
 *
 * Limitations:
 * - Best effort only, not failing thread creation if unable to set priority.
 * - On linux, requires cap_sys_nice set on the executable. This routine
 *   does not attempt to setcap. Currently cap_sys_nice is set on connector
 *   and pbroker, but not on hosted services (which is OK for now).
 * - No-op on other platforms.
 */
int zthread_create_with_priority(pthread_t *thread,
                                zthread_f *thread_func,
                                void *cookie,
                                const char *thread_name,
                                int max_missing_heartbeat_s,
                                size_t stack_size_bytes,
                                int64_t user_int,
                                void *user_void,
                                enum zthread_priority priority);
/*
 * Stash a copy of current stack in thread state. (zthread_info)
 */

void zthread_backtrace_self(void);
void zthread_backtrace_all(int);
int zthread_stack_dump_file(FILE *fp, struct zthread_stack_trace *trace);
void zthread_stack_dump_file_all(FILE *fp);
int zthread_stack_dump_file_to_file(FILE *fp_in, FILE *fp_out);

/* Can be called multiple times to change the names of things */
int zthread_init(char *name, char *version, char *instance, char *core_path_no_trailing_slash, int (*additional_logs_fn)(struct additional_debug_logs *));
void zthread_set_max_stack_bytes(size_t max_stack_bytes);
void zthread_disable_heartbeat_monitor(void);
void zthread_enable_heartbeat_monitor(void);
int zthread_is_heartbeat_monitor_enabled(void);

void zthread_exit_or_abort(void);

int zthread_is_valid_thread_number(int);
char* zthread_get_thread_name(int);
void zthread_termination_handler_init(void (*exit_handler_fn)(int));
/*
 *  Note: Exits if stack dump arg exists.
 *
 * --print-core FILENAME        : Prints core, but only if core version matches software version
 * --print-core-force FILENAME  : Prints core, regardless of software+versions matching
 */
int zthread_do_stack_dump(int *argc, char *argv[]);

/* Get the version of the stack dump that exists in 'data'. Returns
 * NULL on any kind of failure */
char *zthread_stack_version(void *data, size_t data_len);

/* Get the platform of the stack dump or NULL on failure */
char *zthread_stack_platform(void *data, size_t data_len);

/*
 * Based on config, either 1. Abort with stacktrace or 2. exits
 */
void zthread_assert_text(char *text);

void zthread_assert_dump_stacks(int offending_thread_id, char *text, int killall_threads);
void zthread_assert_termination_handler(char *text);
int zthread_get_self_thread_id(void);

/* Return current thread name */
const char *zthread_name(void);
void *zthread_get_cookie(void);

/* Get current (running) thread info */
struct zthread_info *zthread_self(void);

void zthread_set_dont_kill_on_terminate_flag(int thread_num);
int zthread_get_dont_kill_on_terminate_flag(int thread_num);

/*
 * CAUTION: to be enabled only in debugging environment. definitely not in production environment.
 * This will do additional getrusage information gathering after a heartbeat timeout has happened.
 */
void zthread_record_cpu_time_since_last_heartbeat();

void
zthread_set_cloud_name(char* cloud_name);

void
zthread_set_org_name(char* o_name);

/* When called, we'll assume ourself running in a resource restricted environment
 * We'll have some tolerance on zthread missing its own heartbeats, plus a zthread watchdog
 * "zthread_monitor" will be initialized. */
void zthread_set_env_simple_app();
char *
zthread_get_cloud_name();

/* Create an empty thread group. Add threads to group later via zthread_group_add_thread.
 * Returns NULL when it's not found and cannot be created (i.e. too many thread groups).
 */
struct zthread_group_info* zthread_group_get_or_create(const char* name);

/* Add a thread to a group.
 * Note: not supporting removal yet - no use cases.
 * Note: thread_num is the zthread thread number.
 * Returns 0 for success, -1 otherwise.
 */
int zthread_group_add_thread(struct zthread_group_info* info, int thread_num);

/*
 * Returns all thread groups..
 * total_groups: output, size of the returned array.
 * Important: NOT a copy, do not free.
 */
struct zthread_group_info *zthread_group_state_get(int *total_groups);

/* helpers */
void zthread_usage_history_get_info(struct zthread_usage_history* history,
                                    size_t* entry_count,
                                    int64_t* interval_us,
                                    int64_t* from_ts,
                                    int64_t* to_ts);

void zthread_usage_history_dump(struct zthread_usage_history* history, char* tmp_buf, int size);

void zthread_usage_dump(struct zthread_rusage* rusage, struct zthread_cpu_usage* cpu_usage, char* tmp_buf, int size);

void zthread_stats_get(struct zthread_stats* stats);

void zthread_reset_start_cpu_resource_check();

int zthread_state_get_total_unhealthy_count();

int zthread_register_zpn_event_cpu_starvation_cb(int(*)(int));

void zthread_set_heartbeat_override(int override_timeout);
void zthread_get_heartbeat_override(int *override_timeout);
int64_t zthread_get_heartbeat_override_for_thread(int thread_id);

void zthread_heartbeat_exceeded_termination_handler(int thread_number);

/* In case signal handler gets invoked, we will like to close down all listening sockets at
 * the onset of handler processing, so that new clients can no longer connect to the instance.
 * This is very useful on brokers where writing a core might take finite time during which
 * we want to avoid load balancers sending new clients towards such broker instances
 */
void zthread_set_socket_fds(int *sock_fd_array, int sock_fd_count);

int zthread_set_disable_heartbeat_for_thread(int disable, char *thread_name);
void zthread_set_heartbeat_override_for_thread(int thread_num, int64_t max_hearbeat_value);
#endif /* __ZTHREAD_H__ */
