/*
 * np_frr_util_test.c. Copyright (C) 2025 Zscaler Inc. All Rights Reserved.
 */

#include <stdio.h>
#include <string.h>
#include <stdlib.h>

#include "argo/argo.h"

#include "np_lib/np.h"
#include "np_lib/np_private.h"
#include "np_lib/np_frr_utils.h"

int main(int argc, char** argv)
{
    char* test_title;
    int test_fail = 0;
    int res = 0;

    argo_log_use_printf(1);

    if (argo_library_init(1024)) {
        fprintf(stderr, "Error: Argo library init failed\n");
        return 1;
    }

    char *buf = NP_BGP_CALLOC(sizeof(char) * 10000);

    test_title = "TEST FRR RELOAD OVERRIDE FORCE RELOAD";
    res = np_frr_reload_conf(1, 1, buf, 10000);
    if (res) {
        fprintf(stderr, "FAIL - %s, error: %d, buf: %s\n", test_title, res, buf);
        test_fail = 1;
    } else {
        fprintf(stdout, "PASS - %s, buf: %s\n", test_title, buf);
    }

    test_title = "TEST FRR RELOAD GENERATED FORCE RELOAD";
    res = np_frr_reload_conf(0, 1, buf, 10000);
    if (res) {
        fprintf(stderr, "FAIL - %s, error: %d, buf: %s\n", test_title, res, buf);
        test_fail = 1;
    } else {
        fprintf(stdout, "PASS - %s, buf: %s\n", test_title, buf);
    }

    test_title = "TEST FRR RELOAD GENERATED INCREMENTAL RELOAD";
    res = np_frr_reload_conf(0, 0, buf, 10000);
    if (res) {
        fprintf(stderr, "FAIL - %s, error: %d, buf: %s\n", test_title, res, buf);
        test_fail = 1;
    } else {
        fprintf(stdout, "PASS - %s, buf: %s\n", test_title, buf);
    }

    if (buf) NP_BGP_FREE(buf);

    return test_fail;
}
