/*
 * np_bgp_config_frr_test.c. Copyright (C) 2025 Zscaler Inc. All Rights Reserved.
 */

#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include "argo/argo.h"
#include "np_lib/np_bgp_config.h"
#include "np_lib/np_bgp_config_compiled.h"
#include "np_lib/np_bgp_config_frr.h"

static struct argo_object *generate_peer_config_connector_bgp_1()
{
    struct argo_inet peer_inet = {0};
    argo_string_to_inet("**********/32", &peer_inet);
    struct np_bgp_peer_config peer_config = {
        .ip = peer_inet,
        .interface = "wg1",
        .instance_type = "CONNECTOR",
        .asn = 200,
        .use_bfd_timers = 0,
        .bgp_keepalive_interval = 5,
        .bgp_holdtime_interval = 15,
    };

    return argo_object_create(np_bgp_peer_config_description, &peer_config);
}

static struct argo_object *generate_peer_config_connector_bgp_2()
{
    struct argo_inet peer_inet = {0};
    argo_string_to_inet("**********/32", &peer_inet);
    struct np_bgp_peer_config peer_config = {
        .ip = peer_inet,
        .interface = "wg2",
        .instance_type = "CONNECTOR",
        .asn = 200,
        .use_bfd_timers = 0,
        .bgp_keepalive_interval = 5,
        .bgp_holdtime_interval = 15
    };

    return argo_object_create(np_bgp_peer_config_description, &peer_config);
}

static struct argo_object *generate_peer_config_connector_bfd_1()
{
    struct argo_inet peer_inet = {0};
    argo_string_to_inet("**********/32", &peer_inet);
    struct np_bgp_peer_config peer_config = {
        .ip = peer_inet,
        .interface = "wg2",
        .instance_type = "CONNECTOR",
        .asn = 200,
        .use_bfd_timers = 1,
        .bgp_keepalive_interval = 5,
        .bgp_holdtime_interval = 15,
        .bfd_detect_multiplier = 3,
        .bfd_rx_interval = 300,
        .bfd_tx_interval = 300
    };

    return argo_object_create(np_bgp_peer_config_description, &peer_config);
}

static struct argo_object *generate_peer_config_connector_bfd_2()
{
    struct argo_inet peer_inet = {0};
    argo_string_to_inet("**********/32", &peer_inet);
    struct np_bgp_peer_config peer_config = {
        .ip = peer_inet,
        .interface = "wg2",
        .instance_type = "CONNECTOR",
        .asn = 200,
        .use_bfd_timers = 1,
        .bgp_keepalive_interval = 5,
        .bgp_holdtime_interval = 15,
        .bfd_detect_multiplier = 3,
        .bfd_rx_interval = 500,
        .bfd_tx_interval = 500
    };

    return argo_object_create(np_bgp_peer_config_description, &peer_config);
}

static struct argo_object *generate_peer_config_connector_bfd_3()
{
    struct argo_inet peer_inet = {0};
    argo_string_to_inet("**********/32", &peer_inet);
    struct np_bgp_peer_config peer_config = {
        .ip = peer_inet,
        .interface = "wg2",
        .instance_type = "CONNECTOR",
        .asn = 200,
        .use_bfd_timers = 1,
        .bgp_keepalive_interval = 5,
        .bgp_holdtime_interval = 15,
        .bfd_detect_multiplier = 3,
        .bfd_rx_interval = 1000,
        .bfd_tx_interval = 1000
    };

    return argo_object_create(np_bgp_peer_config_description, &peer_config);
}

static struct argo_object *generate_peer_config_gateway_bgp_1()
{
    struct argo_inet peer_inet = {0};
    argo_string_to_inet("**********/32", &peer_inet);
    struct np_bgp_peer_config peer_config = {
        .ip = peer_inet,
        .interface = "wg0",
        .instance_type = "GATEWAY",
        .asn = 100,
        .use_bfd_timers = 0,
        .bgp_keepalive_interval = 5,
        .bgp_holdtime_interval = 15
    };

    return argo_object_create(np_bgp_peer_config_description, &peer_config);
}

static struct argo_object *generate_peer_config_gateway_bfd_1()
{
    struct argo_inet peer_inet = {0};
    argo_string_to_inet("**********/32", &peer_inet);
    struct np_bgp_peer_config peer_config = {
        .ip = peer_inet,
        .interface = "wg0",
        .instance_type = "GATEWAY",
        .asn = 100,
        .use_bfd_timers = 1,
        .bgp_keepalive_interval = 5,
        .bgp_holdtime_interval = 15,
        .bfd_detect_multiplier = 3,
        .bfd_rx_interval = 300,
        .bfd_tx_interval = 300
    };

    return argo_object_create(np_bgp_peer_config_description, &peer_config);
}

static struct argo_object *generate_peer_config_gateway_bfd_2()
{
    struct argo_inet peer_inet = {0};
    argo_string_to_inet("**********/32", &peer_inet);
    struct np_bgp_peer_config peer_config = {
        .ip = peer_inet,
        .interface = "wg0",
        .instance_type = "GATEWAY",
        .asn = 100,
        .use_bfd_timers = 1,
        .bgp_keepalive_interval = 5,
        .bgp_holdtime_interval = 15,
        .bfd_detect_multiplier = 3,
        .bfd_rx_interval = 500,
        .bfd_tx_interval = 500
    };

    return argo_object_create(np_bgp_peer_config_description, &peer_config);
}

static struct argo_object *generate_peer_config_gateway_bfd_3()
{
    struct argo_inet peer_inet = {0};
    argo_string_to_inet("**********/32", &peer_inet);
    struct np_bgp_peer_config peer_config = {
        .ip = peer_inet,
        .interface = "wg0",
        .instance_type = "GATEWAY",
        .asn = 100,
        .use_bfd_timers = 1,
        .bgp_keepalive_interval = 5,
        .bgp_holdtime_interval = 15,
        .bfd_detect_multiplier = 3,
        .bfd_rx_interval = 1000,
        .bfd_tx_interval = 1000
    };

    return argo_object_create(np_bgp_peer_config_description, &peer_config);
}

static struct argo_object *generate_peer_config_router_1()
{
    struct argo_inet peer_inet = {0};
    argo_string_to_inet("**********/32", &peer_inet);
    struct np_bgp_peer_config peer_config = {
        .ip = peer_inet,
        .interface = "eth0",
        .instance_type = "ROUTER",
        .asn = 300,
        .ebgp_multihop_enabled = 1,
        .bgp_keepalive_interval = 5,
        .bgp_holdtime_interval = 15
    };

    return argo_object_create(np_bgp_peer_config_description, &peer_config);
}

static struct argo_object *generate_peer_config_router_ipv6()
{
    struct argo_inet peer_inet = {0};
    argo_string_to_inet("2605:4300:fe00:5001:250:56ff:fe9b:beeb/128", &peer_inet);
    struct np_bgp_peer_config peer_config = {
        .ip = peer_inet,
        .interface = "eth0",
        .instance_type = "ROUTER",
        .asn = 300,
        .ebgp_multihop_enabled = 1,
        .use_bfd_timers = 1,
        .bgp_keepalive_interval = 5,
        .bgp_holdtime_interval = 15
    };

    return argo_object_create(np_bgp_peer_config_description, &peer_config);
}

static int np_bgp_config_frr_test_connector_frr_config_1()
{
    int res;
    struct argo_inet inet = {0};
    char inet_str[ARGO_INET_ADDRSTRLEN];
    argo_string_to_inet("**********/32", &inet);
    struct np_bgp_config_connector config = {
        .version = "10.2.1",
        .router_id = argo_inet_generate(inet_str, &inet),
        .ip = inet,
        .asn = 200,
        .disable_local_sourcing = 1
    };

    struct argo_object *peer_gateway_config_1 = NULL;
    peer_gateway_config_1 = generate_peer_config_gateway_bgp_1();
    if (peer_gateway_config_1) {
        config.gateways[config.gateways_count++] = peer_gateway_config_1;
    }

    struct argo_object *peer_router_config_1 = NULL;
    peer_router_config_1 = generate_peer_config_router_1();
    if (peer_router_config_1) {
        config.routers[config.routers_count++] = peer_router_config_1;
    }

    struct argo_inet client_subnet1 = {0};
    argo_string_to_inet("10.0.0.0/24", &client_subnet1);
    config.client_subnets[0] = &client_subnet1;
    struct argo_inet client_subnet2 = {0};
    argo_string_to_inet("**********/31", &client_subnet2);
    config.client_subnets[1] = &client_subnet2;
    config.client_subnets_count = 2;

    struct argo_inet lan_subnet1 = {0};
    argo_string_to_inet("*******/32", &lan_subnet1);
    config.lan_subnets[0] = &lan_subnet1;
    struct argo_inet lan_subnet2 = {0};
    argo_string_to_inet("*******/31", &lan_subnet2);
    config.lan_subnets[1] = &lan_subnet2;
    config.lan_subnets_count = 2;

    res = np_bgp_config_generate_connector_frr_conf(&config, "connector_1.conf");

    for (int i = 0; i < config.gateways_count; i++) {
        argo_object_release(config.gateways[i]);
    }
    for (int i = 0; i < config.routers_count; i++) {
        argo_object_release(config.routers[i]);
    }
    return res;
}

static int np_bgp_config_frr_test_connector_frr_config_2()
{
    int res;
    struct argo_inet inet = {0};
    char inet_str[ARGO_INET_ADDRSTRLEN];
    argo_string_to_inet("**********/32", &inet);
    struct np_bgp_config_connector config = {
        .version = "10.2.1",
        .router_id = argo_inet_generate(inet_str, &inet),
        .ip = inet,
        .asn = 200,
        .disable_local_sourcing = 0
    };

    struct argo_object *peer_gateway_config_1 = NULL;
    peer_gateway_config_1 = generate_peer_config_gateway_bgp_1();
    if (peer_gateway_config_1) {
        config.gateways[config.gateways_count++] = peer_gateway_config_1;
    }

    struct argo_inet client_subnet1 = {0};
    argo_string_to_inet("10.0.0.0/24", &client_subnet1);
    config.client_subnets[0] = &client_subnet1;
    struct argo_inet client_subnet2 = {0};
    argo_string_to_inet("**********/31", &client_subnet2);
    config.client_subnets[1] = &client_subnet2;
    config.client_subnets_count = 2;

    struct argo_inet lan_subnet1 = {0};
    argo_string_to_inet("*******/32", &lan_subnet1);
    config.lan_subnets[0] = &lan_subnet1;
    struct argo_inet lan_subnet2 = {0};
    argo_string_to_inet("*******/31", &lan_subnet2);
    config.lan_subnets[1] = &lan_subnet2;
    config.lan_subnets_count = 2;

    res = np_bgp_config_generate_connector_frr_conf(&config, "connector_2.conf");

    for (int i = 0; i < config.gateways_count; i++) {
        argo_object_release(config.gateways[i]);
    }
    return res;
}

static int np_bgp_config_frr_test_connector_frr_config_bfd_1()
{
    int res;
    struct argo_inet inet = {0};
    char inet_str[ARGO_INET_ADDRSTRLEN];
    argo_string_to_inet("**********/32", &inet);
    struct np_bgp_config_connector config = {
        .version = "10.2.1",
        .router_id = argo_inet_generate(inet_str, &inet),
        .ip = inet,
        .asn = 200,
        .disable_local_sourcing = 0
    };

    struct argo_object *peer_gateway_config_1 = NULL;
    peer_gateway_config_1 = generate_peer_config_gateway_bfd_1();
    if (peer_gateway_config_1) {
        config.gateways[config.gateways_count++] = peer_gateway_config_1;
    }

    struct argo_object *peer_gateway_config_2 = NULL;
    peer_gateway_config_2 = generate_peer_config_gateway_bfd_2();
    if (peer_gateway_config_2) {
        config.gateways[config.gateways_count++] = peer_gateway_config_2;
    }

    struct argo_object *peer_gateway_config_3 = NULL;
    peer_gateway_config_3 = generate_peer_config_gateway_bfd_3();
    if (peer_gateway_config_3) {
        config.gateways[config.gateways_count++] = peer_gateway_config_3;
    }

    struct argo_inet client_subnet1 = {0};
    argo_string_to_inet("10.0.0.0/24", &client_subnet1);
    config.client_subnets[0] = &client_subnet1;
    config.client_subnets_count = 1;

    struct argo_inet lan_subnet1 = {0};
    argo_string_to_inet("*******/32", &lan_subnet1);
    config.lan_subnets[0] = &lan_subnet1;
    config.lan_subnets_count = 1;

    res = np_bgp_config_generate_connector_frr_conf(&config, "connector_bfd_1.conf");

    for (int i = 0; i < config.gateways_count; i++) {
        argo_object_release(config.gateways[i]);
    }
    return res;
}

static int np_bgp_config_frr_test_connector_frr_config_ipv6()
{
    int res;
    struct argo_inet inet = {0};
    char inet_str[ARGO_INET_ADDRSTRLEN];
    argo_string_to_inet("2001:0000:3238:DFE1:0063:FEF8:0000:FEFB/128", &inet);
    struct np_bgp_config_connector config = {
        .version = "10.2.1",
        .router_id = argo_inet_generate(inet_str, &inet),
        .ip = inet,
        .asn = 200,
        .disable_local_sourcing = 1
    };

    struct argo_object *peer_gateway_config_1 = NULL;
    peer_gateway_config_1 = generate_peer_config_gateway_bgp_1();
    if (peer_gateway_config_1) {
        config.gateways[config.gateways_count++] = peer_gateway_config_1;
    }

    struct argo_object *peer_router_config_1 = NULL;
    peer_router_config_1 = generate_peer_config_router_ipv6();
    if (peer_router_config_1) {
        config.routers[config.routers_count++] = peer_router_config_1;
    }

    struct argo_inet client_subnet1 = {0};
    argo_string_to_inet("2001:0000:3238:DFE1:0063:FEF8:0000:0000/107", &client_subnet1);
    config.client_subnets[0] = &client_subnet1;
    config.client_subnets_count = 1;

    struct argo_inet lan_subnet1 = {0};
    argo_string_to_inet("2001:0000:3238:DFE1:0063:0000:0000:FEFB/128", &lan_subnet1);
    config.lan_subnets[0] = &lan_subnet1;
    config.lan_subnets_count = 1;

    res = np_bgp_config_generate_connector_frr_conf(&config, "connector_ipv6.conf");

    for (int i = 0; i < config.gateways_count; i++) {
        argo_object_release(config.gateways[i]);
    }
    for (int i = 0; i < config.routers_count; i++) {
        argo_object_release(config.routers[i]);
    }
    return res;
}

static int np_bgp_config_frr_test_connector_frr_config_large()
{
    int res;
    struct argo_inet inet = {0};
    char inet_str[ARGO_INET_ADDRSTRLEN];
    argo_string_to_inet("**********/32", &inet);
    struct np_bgp_config_connector config = {
        .version = "10.2.1",
        .router_id = argo_inet_generate(inet_str, &inet),
        .ip = inet,
        .asn = 200,
        .disable_local_sourcing = 1
    };

    struct argo_object *peer_gateway_config_1 = NULL;
    peer_gateway_config_1 = generate_peer_config_gateway_bgp_1();
    if (peer_gateway_config_1) {
        config.gateways[config.gateways_count++] = peer_gateway_config_1;
    }

    for (int i = 0; i < NP_BGP_MAX_PEERS; i++) {
        struct argo_object *peer_router_config_1 = NULL;
        peer_router_config_1 = generate_peer_config_router_1();
        if (peer_router_config_1) {
            config.routers[config.routers_count++] = peer_router_config_1;
        }
    }

    struct argo_inet client_subnet1 = {0};
    argo_string_to_inet("10.0.0.0/24", &client_subnet1);
    config.client_subnets[0] = &client_subnet1;
    config.client_subnets_count = 1;

    struct argo_inet lan_subnet1 = {0};
    argo_string_to_inet("*******/32", &lan_subnet1);
    config.lan_subnets[0] = &lan_subnet1;
    config.lan_subnets_count = 1;

    res = np_bgp_config_generate_connector_frr_conf(&config, "connector_large.conf");

    for (int i = 0; i < config.gateways_count; i++) {
        argo_object_release(config.gateways[i]);
    }
    for (int i = 0; i < config.routers_count; i++) {
        argo_object_release(config.routers[i]);
    }
    return res;
}

static int np_bgp_config_frr_test_connector_frr_config_xlarge()
{
    int res;
    struct argo_inet inet = {0};
    char inet_str[ARGO_INET_ADDRSTRLEN];
    argo_string_to_inet("**********/32", &inet);
    struct np_bgp_config_connector config = {
        .version = "10.2.1",
        .router_id = argo_inet_generate(inet_str, &inet),
        .ip = inet,
        .asn = 200,
        .disable_local_sourcing = 1
    };

    for (int i = 0; i < NP_BGP_MAX_PEERS; i++) {
        struct argo_object *peer_gateway_config_1 = NULL;
        peer_gateway_config_1 = generate_peer_config_gateway_bgp_1();
        if (peer_gateway_config_1) {
            config.gateways[config.gateways_count++] = peer_gateway_config_1;
        }
    }

    for (int i = 0; i < NP_BGP_MAX_PEERS; i++) {
        struct argo_object *peer_router_config_1 = NULL;
        peer_router_config_1 = generate_peer_config_router_1();
        if (peer_router_config_1) {
            config.routers[config.routers_count++] = peer_router_config_1;
        }
    }

    struct argo_inet client_subnet1 = {0};
    argo_string_to_inet("10.0.0.0/24", &client_subnet1);
    for (int i = 0; i < NP_BGP_MAX_SUBNETS; i++) {
        config.client_subnets[config.client_subnets_count++] = &client_subnet1;
    }

    struct argo_inet lan_subnet1 = {0};
    argo_string_to_inet("*******/32", &lan_subnet1);
    for (int i = 0; i < NP_BGP_MAX_SUBNETS; i++) {
        config.lan_subnets[config.lan_subnets_count] = &lan_subnet1;
        config.lan_subnets_count++;
    }

    res = np_bgp_config_generate_connector_frr_conf(&config, "connector_xlarge.conf");

    for (int i = 0; i < config.gateways_count; i++) {
        argo_object_release(config.gateways[i]);
    }
    for (int i = 0; i < config.routers_count; i++) {
        argo_object_release(config.routers[i]);
    }
    return res;
}

static int np_bgp_config_frr_test_gateway_frr_config_1()
{
    int res;
    struct argo_inet inet = {0};
    char inet_str[ARGO_INET_ADDRSTRLEN];
    argo_string_to_inet("**********/32", &inet);
    struct np_bgp_config_gateway config = {
        .version = "10.2.1",
        .router_id = argo_inet_generate(inet_str, &inet),
        .ip = inet,
        .asn = 100,
        .vrf_name = "vrf100",
        .wg_interface_name = "npwg0"
    };

    struct argo_object *peer_connector_config_1 = NULL;
    peer_connector_config_1 = generate_peer_config_connector_bgp_1();
    if (peer_connector_config_1) {
        config.connectors[config.connectors_count++] = peer_connector_config_1;
    }

    struct argo_object *peer_connector_config_2 = NULL;
    peer_connector_config_2 = generate_peer_config_connector_bgp_2();
    if (peer_connector_config_2) {
        config.connectors[config.connectors_count++] = peer_connector_config_2;
    }

    struct argo_inet client_subnet1 = {0};
    argo_string_to_inet("10.0.0.0/24", &client_subnet1);
    config.client_subnets[0] = &client_subnet1;
    config.client_subnets_count = 1;

    struct argo_inet client_subnet2 = {0};
    argo_string_to_inet("********/32", &client_subnet2);
    config.client_subnets[1] = &client_subnet2;
    config.client_subnets_count = 2;

    res = np_bgp_config_generate_gateway_frr_conf(&config, "gateway_1.conf");

    for (int i = 0; i < config.connectors_count; i++) {
        argo_object_release(config.connectors[i]);
    }
    return res;
}

static int np_bgp_config_frr_test_gateway_frr_config_bfd_1()
{
    int res;
    struct argo_inet inet = {0};
    char inet_str[ARGO_INET_ADDRSTRLEN];
    argo_string_to_inet("**********/32", &inet);
    struct np_bgp_config_gateway config = {
        .version = "10.2.1",
        .router_id = argo_inet_generate(inet_str, &inet),
        .ip = inet,
        .asn = 100,
        .vrf_name = "vrf100",
        .wg_interface_name = "npwg0"
    };

    struct argo_object *peer_connector_config_1 = NULL;
    peer_connector_config_1 = generate_peer_config_connector_bfd_1();
    if (peer_connector_config_1) {
        config.connectors[config.connectors_count++] = peer_connector_config_1;
    }

    struct argo_object *peer_connector_config_2 = NULL;
    peer_connector_config_2 = generate_peer_config_connector_bfd_2();
    if (peer_connector_config_2) {
        config.connectors[config.connectors_count++] = peer_connector_config_2;
    }

    struct argo_object *peer_connector_config_3 = NULL;
    peer_connector_config_3 = generate_peer_config_connector_bfd_3();
    if (peer_connector_config_3) {
        config.connectors[config.connectors_count++] = peer_connector_config_3;
    }

    struct argo_inet client_subnet1 = {0};
    argo_string_to_inet("10.0.0.0/24", &client_subnet1);
    config.client_subnets[0] = &client_subnet1;
    config.client_subnets_count = 1;

    struct argo_inet client_subnet2 = {0};
    argo_string_to_inet("********/32", &client_subnet2);
    config.client_subnets[1] = &client_subnet2;
    config.client_subnets_count = 2;

    res = np_bgp_config_generate_gateway_frr_conf(&config, "gateway_bfd_1.conf");

    for (int i = 0; i < config.connectors_count; i++) {
        argo_object_release(config.connectors[i]);
    }
    return res;
}

static int np_bgp_config_frr_test_gateway_frr_config_large()
{
    int res;
    struct argo_inet inet = {0};
    char inet_str[ARGO_INET_ADDRSTRLEN];
    argo_string_to_inet("**********/32", &inet);
    struct np_bgp_config_gateway config = {
        .version = "10.2.1",
        .router_id = argo_inet_generate(inet_str, &inet),
        .ip = inet,
        .asn = 100,
        .vrf_name = "vrf100",
        .wg_interface_name = "npwg0"
    };

    for (int i = 0; i < NP_BGP_MAX_PEERS; i++) {
        struct argo_object *peer_connector_config_1 = NULL;
        peer_connector_config_1 = generate_peer_config_connector_bgp_1();
        if (peer_connector_config_1) {
            config.connectors[config.connectors_count++] = peer_connector_config_1;
        }
    }

    struct argo_inet client_subnet1 = {0};
    argo_string_to_inet("10.0.0.0/24", &client_subnet1);
    config.client_subnets[0] = &client_subnet1;
    config.client_subnets_count = 1;

    struct argo_inet client_subnet2 = {0};
    argo_string_to_inet("********/32", &client_subnet2);
    config.client_subnets[1] = &client_subnet2;
    config.client_subnets_count = 2;

    res = np_bgp_config_generate_gateway_frr_conf(&config, "gateway_large.conf");

    for (int i = 0; i < config.connectors_count; i++) {
        argo_object_release(config.connectors[i]);
    }
    return res;
}

static int np_bgp_config_frr_test_gateway_frr_config_xlarge()
{
    int res;
    struct argo_inet inet = {0};
    char inet_str[ARGO_INET_ADDRSTRLEN];
    argo_string_to_inet("**********/32", &inet);
    struct np_bgp_config_gateway config = {
        .version = "10.2.1",
        .router_id = argo_inet_generate(inet_str, &inet),
        .ip = inet,
        .asn = 100,
        .vrf_name = "vrf100",
        .wg_interface_name = "npwg0"
    };

    for (int i = 0; i < NP_BGP_MAX_PEERS; i++) {
        struct argo_object *peer_connector_config_1 = NULL;
        peer_connector_config_1 = generate_peer_config_connector_bgp_1();
        if (peer_connector_config_1) {
            config.connectors[config.connectors_count++] = peer_connector_config_1;
        }
    }

    struct argo_inet client_subnet1 = {0};
    argo_string_to_inet("10.0.0.0/24", &client_subnet1);
    for (int i = 0; i < NP_BGP_MAX_SUBNETS; i++) {
        config.client_subnets[config.client_subnets_count++] = &client_subnet1;
    }

    res = np_bgp_config_generate_gateway_frr_conf(&config, "gateway_xlarge.conf");

    for (int i = 0; i < config.connectors_count; i++) {
        argo_object_release(config.connectors[i]);
    }
    return res;
}

int main(int argc, char** argv)
{
    char* test_title;
    int test_fail = 0;
    int res = NP_BGP_CONFIG_NO_ERROR;

    argo_log_use_printf(1);
    if (argo_library_init(1024)) {
        fprintf(stderr, "Error: Argo library init failed\n");
        return 1;
    }

    np_bgp_peer_config_description = argo_register_global_structure(NP_BGP_PEER_CONFIG_HELPER);
    np_bgp_config_gateway_description = argo_register_global_structure(NP_BGP_CONFIG_GATEWAY_HELPER);
    np_bgp_config_connector_description = argo_register_global_structure(NP_BGP_CONFIG_CONNECTOR_HELPER);

    if (!np_bgp_peer_config_description ||
        !np_bgp_config_gateway_description ||
        !np_bgp_config_connector_description) {
        return 1;
    }

    test_title = "TEST CONNECTOR FRR CONFIG GENERATION - 1";
    res = np_bgp_config_frr_test_connector_frr_config_1();
    if (res) {
        fprintf(stderr, "FAIL - %s, error: %d\n", test_title, res);
        test_fail = 1;
    } else {
        fprintf(stdout, "PASS - %s\n", test_title);
    }

    test_title = "TEST CONNECTOR FRR CONFIG GENERATION - 2";
    res = np_bgp_config_frr_test_connector_frr_config_2();
    if (res) {
        fprintf(stderr, "FAIL - %s, error: %d\n", test_title, res);
        test_fail = 1;
    } else {
        fprintf(stdout, "PASS - %s\n", test_title);
    }

    test_title = "TEST CONNECTOR FRR CONFIG GENERATION - bfd - 1";
    res = np_bgp_config_frr_test_connector_frr_config_bfd_1();
    if (res) {
        fprintf(stderr, "FAIL - %s, error: %d\n", test_title, res);
        test_fail = 1;
    } else {
        fprintf(stdout, "PASS - %s\n", test_title);
    }

    test_title = "TEST CONNECTOR FRR CONFIG GENERATION - ipv6";
    res = np_bgp_config_frr_test_connector_frr_config_ipv6();
    if (res) {
        fprintf(stderr, "FAIL - %s, error: %d\n", test_title, res);
        test_fail = 1;
    } else {
        fprintf(stdout, "PASS - %s\n", test_title);
    }

    test_title = "TEST CONNECTOR FRR CONFIG GENERATION - large";
    res = np_bgp_config_frr_test_connector_frr_config_large();
    if (res) {
        fprintf(stderr, "FAIL - %s, error: %d\n", test_title, res);
        test_fail = 1;
    } else {
        fprintf(stdout, "PASS - %s\n", test_title);
    }

    test_title = "TEST CONNECTOR FRR CONFIG GENERATION - xlarge";
    res = np_bgp_config_frr_test_connector_frr_config_xlarge();
    if (res) {
        fprintf(stderr, "FAIL - %s, error: %d\n", test_title, res);
        test_fail = 1;
    } else {
        fprintf(stdout, "PASS - %s\n", test_title);
    }

    test_title = "TEST GATEWAY FRR CONFIG GENERATION - 1";
    res = np_bgp_config_frr_test_gateway_frr_config_1();
    if (res) {
        fprintf(stderr, "FAIL - %s, error: %d\n", test_title, res);
        test_fail = 1;
    } else {
        fprintf(stdout, "PASS - %s\n", test_title);
    }

    test_title = "TEST GATEWAY FRR CONFIG GENERATION - bfd - 1";
    res = np_bgp_config_frr_test_gateway_frr_config_bfd_1();
    if (res) {
        fprintf(stderr, "FAIL - %s, error: %d\n", test_title, res);
        test_fail = 1;
    } else {
        fprintf(stdout, "PASS - %s\n", test_title);
    }

    test_title = "TEST GATEWAY FRR CONFIG GENERATION - large";
    res = np_bgp_config_frr_test_gateway_frr_config_large();
    if (res) {
        fprintf(stderr, "FAIL - %s, error: %d\n", test_title, res);
        test_fail = 1;
    } else {
        fprintf(stdout, "PASS - %s\n", test_title);
    }

    test_title = "TEST GATEWAY FRR CONFIG GENERATION - xlarge";
    res = np_bgp_config_frr_test_gateway_frr_config_xlarge();
    if (res) {
        fprintf(stderr, "FAIL - %s, error: %d\n", test_title, res);
        test_fail = 1;
    } else {
        fprintf(stdout, "PASS - %s\n", test_title);
    }

    return test_fail;
}
