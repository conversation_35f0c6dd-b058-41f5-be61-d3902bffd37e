{"version": "10.2.1", "has_version": 0, "router_id": "**********", "ip": "**********", "asn": 200, "gateways": [{"asn": 100, "ip": "**********", "bgp_keepalive_interval": 5, "bgp_holdtime_interval": 15, "use_bfd_timers": 0, "bfd_tx_interval": 0, "bfd_rx_interval": 0, "bfd_detect_multiplier": 0, "afi_safi_count": 0, "ebgp_multihop_enabled": 0, "interface": "wg0"}], "gateways_count": 1, "routers": [{"asn": 300, "ip": "**********", "bgp_keepalive_interval": 5, "bgp_holdtime_interval": 15, "use_bfd_timers": 0, "bfd_tx_interval": 0, "bfd_rx_interval": 0, "bfd_detect_multiplier": 0, "afi_safi_count": 0, "ebgp_multihop_enabled": 1, "interface": "eth0"}], "routers_count": 1, "client_subnets": ["10.0.0.0/24", "**********/31"], "client_subnets_count": 2, "disable_local_sourcing": 1, "lan_subnets": ["*******", "*******/31"], "lan_subnets_count": 2}