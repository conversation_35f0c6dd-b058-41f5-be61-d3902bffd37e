/*
 * np_bgp_test.c. Copyright (C) 2025 Zscaler Inc. All Rights Reserved.
 */

#include <stdio.h>
#include <string.h>
#include <stdlib.h>

#include "argo/argo.h"
#include "zthread/zthread.h"
#include "zevent/zevent.h"

#include "np_lib/np.h"
#include "np_lib/np_private.h"
#include "np_lib/np_bgp.h"
#include "np_lib/np_bgp_config.h"
#include "np_lib/np_bgp_gateway_session_config.h"
#include "np_lib/np_bgp_connector_session_config.h"
#include "np_lib/np_bgp_gateway_session_config_compiled.h"
#include "np_lib/np_bgp_connector_session_config_compiled.h"

#include <event2/event.h>
#include <event2/thread.h>

struct event_base *script_ev_base;

struct argo_structure_description *np_bgp_gateway_session_config_description = NULL;
struct argo_structure_description *np_bgp_connector_session_config_description = NULL;

static struct np_bgp_gateway_session_config *generate_gateway_session_config_1(int iter)
{
    struct np_bgp_gateway_session_config *session_config = NP_BGP_CALLOC(sizeof(struct np_bgp_gateway_session_config));
    struct argo_inet *peer_inet = NP_BGP_CALLOC(sizeof(struct argo_inet));
    char **afi_safi_strings = NP_BGP_CALLOC(2 * sizeof(char *));
    afi_safi_strings[0] = NP_BGP_STRDUP(NP_BGP_AFI_SAFI_IPV4_UNICAST, strlen(NP_BGP_AFI_SAFI_IPV4_UNICAST));
    afi_safi_strings[1] = NP_BGP_STRDUP(NP_BGP_AFI_SAFI_IPV6_UNICAST, strlen(NP_BGP_AFI_SAFI_IPV6_UNICAST));

    argo_string_to_inet("**********/32", peer_inet);
    session_config->gid = 10000+iter;
    session_config->neighbor_config_gid = 10000000+iter;
    session_config->neighbor_asn = 200;
    session_config->neighbor_ip = peer_inet;
    session_config->use_bfd_timers = 1;
    session_config->bgp_keepalive_interval = 5;
    session_config->bgp_holdtime_interval = 15;
    session_config->bfd_detect_multiplier = 3;
    session_config->bfd_rx_interval = 1000;
    session_config->bfd_tx_interval = 1000;
    session_config->afi_safi = afi_safi_strings;
    session_config->afi_safi_count = 2;

    return session_config;
}

static struct np_bgp_connector_session_config *generate_connector_session_config_gw_1(int iter)
{
    struct np_bgp_connector_session_config *session_config = NP_BGP_CALLOC(sizeof(struct np_bgp_connector_session_config));
    struct argo_inet *peer_inet = NP_BGP_CALLOC(sizeof(struct argo_inet));
    char **afi_safi_strings = NP_BGP_CALLOC(2 * sizeof(char *));
    afi_safi_strings[0] = NP_BGP_STRDUP(NP_BGP_AFI_SAFI_IPV4_UNICAST, strlen(NP_BGP_AFI_SAFI_IPV4_UNICAST));
    afi_safi_strings[1] = NP_BGP_STRDUP(NP_BGP_AFI_SAFI_IPV6_UNICAST, strlen(NP_BGP_AFI_SAFI_IPV6_UNICAST));

    argo_string_to_inet("**********/32", peer_inet);
    session_config->gid = 20000+iter;
    session_config->neighbor_config_gid = 20000000+iter;
    session_config->neighbor_ip = peer_inet;
    session_config->neighbor_asn = 100;
    session_config->neighbor_instance_type = "GATEWAY";
    session_config->use_bfd_timers = 1;
    session_config->bgp_keepalive_interval = 5;
    session_config->bgp_holdtime_interval = 15;
    session_config->bfd_detect_multiplier = 3;
    session_config->bfd_rx_interval = 1000;
    session_config->bfd_tx_interval = 1000;
    session_config->afi_safi = afi_safi_strings;
    session_config->afi_safi_count = 2;

    return session_config;
}

static struct np_bgp_connector_session_config *generate_connector_session_config_router_1(int iter)
{
    struct np_bgp_connector_session_config *session_config = NP_BGP_CALLOC(sizeof(struct np_bgp_connector_session_config));
    struct argo_inet *peer_inet = NP_BGP_CALLOC(sizeof(struct argo_inet));
    char **afi_safi_strings = NP_BGP_CALLOC(2 * sizeof(char *));
    afi_safi_strings[0] = NP_BGP_STRDUP(NP_BGP_AFI_SAFI_IPV4_UNICAST, strlen(NP_BGP_AFI_SAFI_IPV4_UNICAST));
    afi_safi_strings[1] = NP_BGP_STRDUP(NP_BGP_AFI_SAFI_IPV6_UNICAST, strlen(NP_BGP_AFI_SAFI_IPV6_UNICAST));

    argo_string_to_inet("*******/32", peer_inet);
    session_config->gid = 30000+iter;
    session_config->neighbor_config_gid = 30000000+iter;
    session_config->neighbor_ip = peer_inet;
    session_config->neighbor_asn = 300;
    session_config->neighbor_instance_type = "ROUTER";
    session_config->bgp_keepalive_interval = 5;
    session_config->bgp_holdtime_interval = 15;
    session_config->afi_safi = afi_safi_strings;
    session_config->afi_safi_count = 2;

    return session_config;
}

static int start_peer_config_update_tests_gateway(struct np_bgp_state *bgp_state,
                                                  struct np_bgp_gateway_session_config **session_config_gw)
{
    char* test_title;
    int test_fail = 0;
    int res = NP_RESULT_NO_ERROR;
    int rc;
    int i;

    test_title = "TEST BGP PEER CONFIG UPDATE ON GATEWAY";
    for (i = 0; i < NP_BGP_MAX_PEERS; i++) {
        if (session_config_gw[i]) {
            rc = np_bgp_gateway_add_or_update_peer_config(bgp_state, session_config_gw[i], "npwg12345");
            if (rc) res = rc;
        }
    }
    if (res) {
        fprintf(stderr, "FAIL - %s, error: %d\n", test_title, res);
        test_fail = 1;
    } else {
        fprintf(stdout, "PASS - %s\n", test_title);
    }

    return test_fail;
}

static int start_peer_config_delete_tests_gateway(struct np_bgp_state *bgp_state,
                                                  struct np_bgp_gateway_session_config **session_config_gw)
{
    char* test_title;
    int test_fail = 0;
    int res = NP_RESULT_NO_ERROR;
    int i;

    test_title = "TEST BGP PEER CONFIG DELETE ON GATEWAY";
    for (i = 0; i < NP_BGP_MAX_PEERS; i++) {
        if (session_config_gw[i]) {
            res = np_bgp_gateway_delete_peer_config(bgp_state, session_config_gw[i]);
            if (res) test_fail = res;
        }
    }
    if (test_fail) {
        fprintf(stderr, "FAIL - %s, error: %d\n", test_title, res);
    } else {
        fprintf(stdout, "PASS - %s\n", test_title);
    }

    return test_fail;
}


static int start_peer_config_update_tests_connector(struct np_bgp_state *bgp_state,
                                                    struct np_bgp_connector_session_config **session_config_gw,
                                                    struct np_bgp_connector_session_config **session_config_router)
{
    char* test_title;
    int test_fail = 0;
    int res = NP_RESULT_NO_ERROR;
    int rc;
    int i;

    test_title = "TEST BGP GATEWAY PEER CONFIG UPDATE ON CONNECTOR";
    for (i = 0; i < NP_BGP_MAX_PEERS; i++) {
        if (session_config_gw[i]) {
            rc = np_bgp_connector_add_or_update_peer_config(bgp_state, session_config_gw[i]);
            if (rc) res = rc;
        }
    }
    if (res) {
        fprintf(stderr, "FAIL - %s, error: %d\n", test_title, res);
        test_fail = 1;
    } else {
        fprintf(stdout, "PASS - %s\n", test_title);
    }

    test_title = "TEST BGP ROUTER PEER CONFIG UPDATE ON CONNECTOR";
    for (i = 0; i < NP_BGP_MAX_PEERS; i++) {
        if (session_config_router[i]) {
            rc = np_bgp_connector_add_or_update_peer_config(bgp_state, session_config_router[i]);
            if (rc) res = rc;
        }
    }
    if (res) {
        fprintf(stderr, "FAIL - %s, error: %d\n", test_title, res);
        test_fail = 1;
    } else {
        fprintf(stdout, "PASS - %s\n", test_title);
    }

    return test_fail;
}

static int start_peer_config_delete_tests_connector(struct np_bgp_state *bgp_state,
                                                    struct np_bgp_connector_session_config **session_config_gw,
                                                    struct np_bgp_connector_session_config **session_config_router)
{
    char* test_title;
    int test_fail = 0;
    int res = NP_RESULT_NO_ERROR;
    int rc;
    int i;

    test_title = "TEST BGP GATEWAY PEER CONFIG DELETE ON CONNECTOR";
    for (i = 0; i < NP_BGP_MAX_PEERS; i++) {
        if (session_config_gw[i]) {
            rc = np_bgp_connector_delete_peer_config(bgp_state, session_config_gw[i]);
            if (rc) res = rc;
        }
    }
    if (res) {
        fprintf(stderr, "FAIL - %s, error: %d\n", test_title, res);
        test_fail = 1;
    } else {
        fprintf(stdout, "PASS - %s\n", test_title);
    }

    test_title = "TEST BGP ROUTER PEER CONFIG DELETE ON CONNECTOR";
    for (i = 0; i < NP_BGP_MAX_PEERS; i++) {
        if (session_config_router[i]) {
            rc = np_bgp_connector_delete_peer_config(bgp_state, session_config_router[i]);
            if (rc) res = rc;
        }
    }
    if (res) {
        fprintf(stderr, "FAIL - %s, error: %d\n", test_title, res);
        test_fail = 1;
    } else {
        fprintf(stdout, "PASS - %s\n", test_title);
    }

    return test_fail;
}

/* Subnet Config Update Tests */
static int start_subnet_update_tests(struct np_bgp_state *bgp_state, enum np_bgp_instance_type instance_type,
                                     struct argo_inet *inet1, struct argo_inet *inet2, struct argo_inet *inet3, struct argo_inet *inet4)
{
    char* test_title;
    int test_fail = 0;
    int res = NP_RESULT_NO_ERROR;
    int rc;

    if (instance_type == np_bgp_instance_gateway) {
        test_title = "TEST BGP CLIENT SUBNET UPDATE ON GATEWAY";
        for (int i = 0; i < NP_BGP_MAX_SUBNETS; i++) {
            if (i % 4 == 0) {
                rc = np_bgp_add_subnet_config(bgp_state, inet1, 0);
                if (rc) res = rc;
            } else if (i % 4 == 1) {
                rc = np_bgp_add_subnet_config(bgp_state, inet2, 0);
                if (rc) res = rc;
            } else if (i % 4 == 2) {
                rc = np_bgp_add_subnet_config(bgp_state, inet3, 0);
                if (rc) res = rc;
            } else {
                rc = np_bgp_add_subnet_config(bgp_state, inet4, 0);
                if (rc) res = rc;
            }
        }
        if (res) {
            fprintf(stderr, "FAIL - %s - %s, error: %d\n", test_title, np_bgp_instance_type_str(instance_type), res);
            test_fail = 1;
        } else {
            fprintf(stdout, "PASS - %s - %s\n", test_title, np_bgp_instance_type_str(instance_type));
        }
    } else if (instance_type == np_bgp_instance_connector) {
        test_title = "TEST BGP CLIENT SUBNET UPDATE ON CONNECTOR";
        for (int i = 0; i < NP_BGP_MAX_SUBNETS; i++) {
            if (i % 4 == 0) {
                rc = np_bgp_add_subnet_config(bgp_state, inet1, 0);
                if (rc) res = rc;
            } else if (i % 4 == 1) {
                rc = np_bgp_add_subnet_config(bgp_state, inet2, 0);
                if (rc) res = rc;
            } else if (i % 4 == 2) {
                rc = np_bgp_add_subnet_config(bgp_state, inet3, 0);
                if (rc) res = rc;
            } else {
                rc = np_bgp_add_subnet_config(bgp_state, inet4, 0);
                if (rc) res = rc;
            }
        }
        if (res) {
            fprintf(stderr, "FAIL - %s - %s, error: %d\n", test_title, np_bgp_instance_type_str(instance_type), res);
            test_fail = 1;
        } else {
            fprintf(stdout, "PASS - %s - %s\n", test_title, np_bgp_instance_type_str(instance_type));
        }

        test_title = "TEST BGP LAN SUBNET UPDATE ON CONNECTOR";
        for (int i = 0; i < NP_BGP_MAX_SUBNETS; i++) {
            if (i % 4 == 0) {
                rc = np_bgp_add_subnet_config(bgp_state, inet1, 1);
                if (rc) res = rc;
            } else if (i % 4 == 1) {
                rc = np_bgp_add_subnet_config(bgp_state, inet2, 1);
                if (rc) res = rc;
            } else if (i % 4 == 2) {
                rc = np_bgp_add_subnet_config(bgp_state, inet3, 1);
                if (rc) res = rc;
            } else {
                rc = np_bgp_add_subnet_config(bgp_state, inet4, 1);
                if (rc) res = rc;
            }
        }
        if (res) {
            fprintf(stderr, "FAIL - %s - %s, error: %d\n", test_title, np_bgp_instance_type_str(instance_type), res);
            test_fail = 1;
        } else {
            fprintf(stdout, "PASS - %s - %s\n", test_title, np_bgp_instance_type_str(instance_type));
        }
    }

    return test_fail;
}


/* Subnet Delete Tests */
static int start_subnet_delete_tests(struct np_bgp_state *bgp_state, enum np_bgp_instance_type instance_type,
                                     struct argo_inet *inet1, struct argo_inet *inet2, struct argo_inet *inet3, struct argo_inet *inet4)
{
    char* test_title;
    int test_fail = 0;
    int res = NP_RESULT_NO_ERROR;
    int rc;
    if (instance_type == np_bgp_instance_gateway) {
        test_title = "TEST BGP CLIENT SUBNET DELETE ON GATEWAY";
        for (int i = 0; i < NP_BGP_MAX_SUBNETS; i++) {
            if (i % 4 == 0) {
                rc = np_bgp_delete_subnet_config(bgp_state, inet1, 0);
                if (rc) res = rc;
            } else if (i % 4 == 1) {
                rc = np_bgp_delete_subnet_config(bgp_state, inet2, 0);
                if (rc) res = rc;
            } else if (i % 4 == 2) {
                rc = np_bgp_delete_subnet_config(bgp_state, inet3, 0);
                if (rc) res = rc;
            } else {
                rc = np_bgp_delete_subnet_config(bgp_state, inet4, 0);
                if (rc) res = rc;
            }
        }
        if (res) {
            fprintf(stderr, "FAIL - %s - %s, error: %d\n", test_title, np_bgp_instance_type_str(instance_type), res);
            test_fail = 1;
        } else {
            fprintf(stdout, "PASS - %s - %s\n", test_title, np_bgp_instance_type_str(instance_type));
        }
    } else if (instance_type == np_bgp_instance_connector) {
        test_title = "TEST BGP CLIENT SUBNET DELETE ON CONNECTOR";
        for (int i = 0; i < NP_BGP_MAX_SUBNETS; i++) {
            if (i % 4 == 0) {
                rc = np_bgp_delete_subnet_config(bgp_state, inet1, 0);
                if (rc) res = rc;
            } else if (i % 4 == 1) {
                rc = np_bgp_delete_subnet_config(bgp_state, inet2, 0);
                if (rc) res = rc;
            } else if (i % 4 == 2) {
                rc = np_bgp_delete_subnet_config(bgp_state, inet3, 0);
                if (rc) res = rc;
            } else {
                rc = np_bgp_delete_subnet_config(bgp_state, inet4, 0);
                if (rc) res = rc;
            }
        }
        if (res) {
            fprintf(stderr, "FAIL - %s - %s, error: %d\n", test_title, np_bgp_instance_type_str(instance_type), res);
            test_fail = 1;
        } else {
            fprintf(stdout, "PASS - %s - %s\n", test_title, np_bgp_instance_type_str(instance_type));
        }

        test_title = "TEST BGP LAN SUBNET DELETE ON CONNECTOR";
        for (int i = 0; i < NP_BGP_MAX_SUBNETS; i++) {
            if (i % 4 == 0) {
                rc = np_bgp_delete_subnet_config(bgp_state, inet1, 1);
                if (rc) res = rc;
            } else if (i % 4 == 1) {
                rc = np_bgp_delete_subnet_config(bgp_state, inet2, 1);
                if (rc) res = rc;
            } else if (i % 4 == 2) {
                rc = np_bgp_delete_subnet_config(bgp_state, inet3, 1);
                if (rc) res = rc;
            } else {
                rc = np_bgp_delete_subnet_config(bgp_state, inet4, 1);
                if (rc) res = rc;
            }
        }
        if (res) {
            fprintf(stderr, "FAIL - %s - %s, error: %d\n", test_title, np_bgp_instance_type_str(instance_type), res);
            test_fail = 1;
        } else {
            fprintf(stdout, "PASS - %s - %s\n", test_title, np_bgp_instance_type_str(instance_type));
        }
    }

    return test_fail;
}

int sub_main(enum np_bgp_instance_type instance_type)
{
    char* test_title;
    int test_fail = 0;
    int rc;
    struct argo_inet router_id;
    int res = NP_RESULT_NO_ERROR;

    int64_t customer_gid = 72057594037927936;
    struct np_bgp_state *bgp_state = NULL;

    test_title = "TEST BGP STATE INIT";
    bgp_state = np_bgp_state_create(customer_gid, instance_type);
    if (!bgp_state) {
        fprintf(stderr, "FAIL - %s - %s, error: %d\n", test_title, np_bgp_instance_type_str(instance_type), res);
        test_fail = 1;
    } else {
        fprintf(stdout, "PASS - %s - %s\n", test_title, np_bgp_instance_type_str(instance_type));
    }

    test_title = "TEST BGP INSTANCE CONFIG UPDATE - NULL";
    res = np_bgp_update_instance_config(bgp_state, 1, 1, NULL, NULL, NULL, 1);
    if (!res) {
        fprintf(stderr, "FAIL - %s - %s, error: %d\n", test_title, np_bgp_instance_type_str(instance_type), res);
        test_fail = 1;
    } else {
        fprintf(stdout, "PASS - %s - %s\n", test_title, np_bgp_instance_type_str(instance_type));
    }
    sleep(1);

    test_title = "TEST BGP INSTANCE CONFIG UPDATE - SINGLE OVERRIDE";
    argo_string_to_inet("240.1.1.1", &router_id);
    res = np_bgp_update_instance_config(bgp_state, 1, 1, &router_id, "OVERRIDE", "FAKE OVERRIDE CONFIG", 1);
    if (res) {
        fprintf(stderr, "FAIL - %s - %s, error: %d\n", test_title, np_bgp_instance_type_str(instance_type), res);
        test_fail = 1;
    } else {
        fprintf(stdout, "PASS - %s - %s\n", test_title, np_bgp_instance_type_str(instance_type));
    }
    sleep(1);

    test_title = "TEST BGP INSTANCE CONFIG UPDATE - SINGLE GENERATED";
    argo_string_to_inet("240.1.1.1", &router_id);
    res = np_bgp_update_instance_config(bgp_state, 1, 1, &router_id, "GENERATED", "", 1);
    if (res) {
        fprintf(stderr, "FAIL - %s - %s, error: %d\n", test_title, np_bgp_instance_type_str(instance_type), res);
        test_fail = 1;
    } else {
        fprintf(stdout, "PASS - %s - %s\n", test_title, np_bgp_instance_type_str(instance_type));
    }
    sleep(1);

    test_title = "TEST BGP INSTANCE CONFIG UPDATE - BATCH OVERRIDE";
    argo_string_to_inet("240.1.1.1", &router_id);

    for (int i = 0; i < 100; i++) {
        rc = np_bgp_update_instance_config(bgp_state, 1, 1, &router_id, "OVERRIDE", "FAKE OVERRIDE CONFIG", 1);
        if (rc) res = rc;
    }
    if (res) {
        fprintf(stderr, "FAIL - %s - %s, error: %d\n", test_title, np_bgp_instance_type_str(instance_type), res);
        test_fail = 1;
    } else {
        fprintf(stdout, "PASS - %s - %s\n", test_title, np_bgp_instance_type_str(instance_type));
    }
    sleep(1);

    test_title = "TEST BGP INSTANCE CONFIG UPDATE - BATCH GENERATED";
    argo_string_to_inet("240.1.1.1", &router_id);
    for (int i = 0; i < 100; i++) {
        rc = np_bgp_update_instance_config(bgp_state, 1, 1, &router_id, "GENERATED", "", 1);
        if (rc) res = rc;
    }
    if (res) {
        fprintf(stderr, "FAIL - %s - %s, error: %d\n", test_title, np_bgp_instance_type_str(instance_type), res);
        test_fail = 1;
    } else {
        fprintf(stdout, "PASS - %s - %s\n", test_title, np_bgp_instance_type_str(instance_type));
    }
    sleep(1);//let frr timer run

    if (instance_type == np_bgp_instance_gateway) {
        struct np_bgp_gateway_session_config **session_config = NP_BGP_CALLOC(NP_BGP_MAX_PEERS * sizeof(struct np_bgp_gateway_session_config *));
        int i;
        for (i = 0; i < NP_BGP_MAX_PEERS; i++) {
            session_config[i] = generate_gateway_session_config_1(i);
        }
        res = start_peer_config_update_tests_gateway(bgp_state, session_config);
        if (res) test_fail = 1;
        for (i = 0; i < NP_BGP_MAX_PEERS; i++) {
            if (session_config[i]) {
                if (session_config[i]->neighbor_ip) NP_BGP_FREE(session_config[i]->neighbor_ip);
                for (int j = 0; j < session_config[i]->afi_safi_count; j++) {
                    if (session_config[i]->afi_safi[j]) NP_BGP_FREE(session_config[i]->afi_safi[j]);
                }
                NP_BGP_FREE(session_config[i]);
            }
        }
        NP_BGP_FREE(session_config);
    } else if (instance_type == np_bgp_instance_connector) {
        struct np_bgp_connector_session_config **session_config_gw = NP_BGP_CALLOC(NP_BGP_MAX_PEERS * sizeof(struct np_bgp_connector_session_config *));
        struct np_bgp_connector_session_config **session_config_router = NP_BGP_CALLOC(NP_BGP_MAX_PEERS * sizeof(struct np_bgp_connector_session_config *));
        int i;
        for (i = 0; i < NP_BGP_MAX_PEERS; i++) {
            session_config_gw[i] = generate_connector_session_config_gw_1(i);
            session_config_router[i] = generate_connector_session_config_router_1(i);
        }
        res = start_peer_config_update_tests_connector(bgp_state, session_config_gw, session_config_router);
        if (res) test_fail = 1;
        for (i = 0; i < NP_BGP_MAX_PEERS; i++) {
            if (session_config_gw[i]) {
                if (session_config_gw[i]->neighbor_ip) NP_BGP_FREE(session_config_gw[i]->neighbor_ip);
                for (int j = 0; j < session_config_gw[i]->afi_safi_count; j++) {
                    if (session_config_gw[i]->afi_safi[j]) NP_BGP_FREE(session_config_gw[i]->afi_safi[j]);
                }
                NP_BGP_FREE(session_config_gw[i]);
            }
        }
        for (i = 0; i < NP_BGP_MAX_PEERS; i++) {
            if (session_config_router[i]) {
                if (session_config_router[i]->neighbor_ip) NP_BGP_FREE(session_config_router[i]->neighbor_ip);
                for (int j = 0; j < session_config_router[i]->afi_safi_count; j++) {
                    if (session_config_router[i]->afi_safi[j]) NP_BGP_FREE(session_config_router[i]->afi_safi[j]);
                }
                NP_BGP_FREE(session_config_router[i]);
            }
        }
        NP_BGP_FREE(session_config_gw);
        NP_BGP_FREE(session_config_router);
    }
    sleep(1);//let frr timer run


    struct argo_inet ip4, ip6, cidr4, cidr6;
    argo_string_to_inet("*******/32", &ip4);
    argo_string_to_inet("2001:0000:3238:DFE1:0063:0000:0000:FEFB/128", &ip6);
    argo_string_to_inet("*******/16", &cidr4);
    argo_string_to_inet("2001:0000:3238:DFE1:0065:0000:0000:0000/96", &cidr6);

    res = start_subnet_update_tests(bgp_state, instance_type, &ip4, &ip6, &cidr4, &cidr6);
    if (res) test_fail = 1;
    sleep(1);//let frr timer run

    res = start_subnet_delete_tests(bgp_state, instance_type, &ip4, &ip6, &cidr4, &cidr6);
    if (res) test_fail = 1;
    sleep(1);//let frr timer run


    if (instance_type == np_bgp_instance_gateway) {
        struct np_bgp_gateway_session_config **session_config = NP_BGP_CALLOC(NP_BGP_MAX_PEERS * sizeof(struct np_bgp_gateway_session_config *));
        int i;
        for (i = 0; i < NP_BGP_MAX_PEERS; i++) {
            session_config[i] = generate_gateway_session_config_1(i);
        }
        res = start_peer_config_delete_tests_gateway(bgp_state, session_config);
        if (res) test_fail = 1;
        for (i = 0; i < NP_BGP_MAX_PEERS; i++) {
            if (session_config[i]) NP_BGP_FREE(session_config[i]);
        }
        NP_BGP_FREE(session_config);
    } else if (instance_type == np_bgp_instance_connector) {
        struct np_bgp_connector_session_config **session_config_gw = NP_BGP_CALLOC(NP_BGP_MAX_PEERS * sizeof(struct np_bgp_connector_session_config *));
        struct np_bgp_connector_session_config **session_config_router = NP_BGP_CALLOC(NP_BGP_MAX_PEERS * sizeof(struct np_bgp_connector_session_config *));
        int i;
        for (i = 0; i < NP_BGP_MAX_PEERS; i++) {
            session_config_gw[i] = generate_connector_session_config_gw_1(i);
            session_config_router[i] = generate_connector_session_config_router_1(i);
        }
        res = start_peer_config_delete_tests_connector(bgp_state, session_config_gw, session_config_router);
        if (res) test_fail = 1;
        for (i = 0; i < NP_BGP_MAX_PEERS; i++) {
            if (session_config_gw[i]) NP_BGP_FREE(session_config_gw[i]);
        }
        for (i = 0; i < NP_BGP_MAX_PEERS; i++) {
            if (session_config_router[i]) NP_BGP_FREE(session_config_router[i]);
        }
        NP_BGP_FREE(session_config_gw);
        NP_BGP_FREE(session_config_router);
    }
    sleep(1);//let frr timer run


    sleep(2); // wait for the other thread to finish
    test_title = "TEST BGP STATE DESTROY";
    res = np_bgp_state_destroy(bgp_state);
    if (res) {
        fprintf(stderr, "FAIL - %s - %s, error: %d\n", test_title, np_bgp_instance_type_str(instance_type), res);
        test_fail = 1;
    } else {
        fprintf(stdout, "PASS - %s - %s\n", test_title, np_bgp_instance_type_str(instance_type));
    }
    sleep(1); // wait for the other thread to finish

    return test_fail;
}

static int argo_desc_init()
{
    np_bgp_gateway_session_config_description = argo_register_global_structure(NP_BGP_GATEWAY_SESSION_CONFIG_HELPER);
    if (!np_bgp_gateway_session_config_description) {
        return NP_RESULT_ERR;
    }

    np_bgp_connector_session_config_description = argo_register_global_structure(NP_BGP_CONNECTOR_SESSION_CONFIG_HELPER);
    if (!np_bgp_connector_session_config_description) {
        return NP_RESULT_ERR;
    }

    return NP_RESULT_NO_ERROR;
}
int main(int argc, char** argv)
{
    int test_fail = 0;
    int res;

    argo_log_use_printf(1);

    if (evthread_use_pthreads() != 0) {
        fprintf(stderr, "Can't run pthreads\n");
        exit(1);
    }

    if (argo_library_init(1024)) {
        fprintf(stderr, "Error: Argo library init failed\n");
        return 1;
    }

    if (argo_desc_init()) {
        fprintf(stderr, "Can't init argo descs\n");
        exit(1);
    }

    zthread_init("Test", "version", "instance", NULL, NULL);
    zthread_disable_heartbeat_monitor(); // so that we can debug without getting killed
    zevent_init();

    zthread_register_self("main", 200);
    script_ev_base = event_base_new();
    zevent_attach(script_ev_base);

    event_base_loop(script_ev_base, EVLOOP_NONBLOCK);

    res = np_bgp_init();
    if (res) {
        fprintf(stderr, "FAIL - np_bgp_init(), error: %d\n", res);
        test_fail = 1;
    } else {
        fprintf(stdout, "np_bgp_init() complete\n");
    }

    res = sub_main(np_bgp_instance_gateway);
    if (res) test_fail = 1;

    res = sub_main(np_bgp_instance_connector);
    if (res) test_fail = 1;

    return test_fail;
}
