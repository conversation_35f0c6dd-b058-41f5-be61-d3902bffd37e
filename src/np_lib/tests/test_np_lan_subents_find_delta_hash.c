#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include "argo/argo.h"
#include "zhash/zhash_table.h"
#include "wally/wally.h"
#include "np_lib/np_lan_subnets.h"

struct argo_inet *create_mock_inet(const char *address_with_netmask) {
    struct argo_inet *inet = malloc(sizeof(struct argo_inet));
    if (!inet) {
        fprintf(stderr, "Failed to allocate memory for argo_inet\n");
        exit(EXIT_FAILURE);
    }

    if (argo_string_to_inet(address_with_netmask, inet) != 0) {
        fprintf(stderr, "Failed to parse address: %s\n", address_with_netmask);
        free(inet);
        exit(EXIT_FAILURE);
    }

    return inet;
}

void free_mock_inets(struct argo_inet **inets, int count) {
    for (int i = 0; i < count; i++) {
        free(inets[i]);
    }
}

int test_case_empty_subnets_2() {
    printf("\n=== Test Case: Empty Subnets ===\n");

    struct argo_inet *old_subnets[] = {};
    struct argo_inet *new_subnets[] = {};

    struct argo_inet *ips_to_add[10];
    struct argo_inet *ips_to_delete[10];
    int ips_to_add_count = 0;
    int ips_to_delete_count = 0;

    np_lan_subnet_find_lan_subnets_delta_hash_table((const struct argo_inet **)new_subnets, 0,
                                           (const struct argo_inet **)old_subnets, 0,
                                           ips_to_add, 10, &ips_to_add_count,
                                           ips_to_delete, 10, &ips_to_delete_count,
                                           12345, 67890);

    printf("ADD Count: %d, DELETE Count: %d\n", ips_to_add_count, ips_to_delete_count);

    if (ips_to_add_count == 0 && ips_to_delete_count == 0) {
        printf("Output matches the expected result. Test passed.\n");
    } else {
        printf("Counts do NOT match expected values. Expected ADD Count: 0, DELETE Count: 0.\n");
    }

    return 0;
}


int test_case_identical_subnets_2() {
    printf("\n=== Test Case: Identical Subnets ===\n");

    struct argo_inet *old_subnets[] = {
        create_mock_inet("***********/24"),
        create_mock_inet("********/24"),
        create_mock_inet("**********/24")
    };
    struct argo_inet *new_subnets[] = {
        create_mock_inet("***********/24"),
        create_mock_inet("********/24"),
        create_mock_inet("**********/24")
    };

    struct argo_inet *ips_to_add[10];
    struct argo_inet *ips_to_delete[10];
    int ips_to_add_count = 0;
    int ips_to_delete_count = 0;

    np_lan_subnet_find_lan_subnets_delta_hash_table((const struct argo_inet **)new_subnets, 3,
                                           (const struct argo_inet **)old_subnets, 3,
                                           ips_to_add, 10, &ips_to_add_count,
                                           ips_to_delete, 10, &ips_to_delete_count,
                                           12345, 67890);

    printf("ADD Count: %d, DELETE Count: %d\n", ips_to_add_count, ips_to_delete_count);

    if (ips_to_add_count == 0 && ips_to_delete_count == 0) {
        printf("Output matches the expected result. Test passed.\n");
    } else {
        printf("Counts do NOT match expected values. Expected ADD Count: 0, DELETE Count: 0.\n");
    }

    free_mock_inets(old_subnets, 3);
    free_mock_inets(new_subnets, 3);

    return 0;
}

int test_case_all_new_subnets_2() {
    printf("\n=== Test Case: All New Subnets ===\n");

    struct argo_inet *old_subnets[] = {};
    struct argo_inet *new_subnets[] = {
        create_mock_inet("***********/24"),
        create_mock_inet("**********/24"),
        create_mock_inet("**********/24")
    };

    struct argo_inet *ips_to_add[10];
    struct argo_inet *ips_to_delete[10];
    int ips_to_add_count = 0;
    int ips_to_delete_count = 0;

    np_lan_subnet_find_lan_subnets_delta_hash_table((const struct argo_inet **)new_subnets, 3,
                                           (const struct argo_inet **)old_subnets, 0,
                                           ips_to_add, 10, &ips_to_add_count,
                                           ips_to_delete, 10, &ips_to_delete_count,
                                           12345, 67890);

    printf("ADD Count: %d, DELETE Count: %d\n", ips_to_add_count, ips_to_delete_count);

    if (ips_to_add_count == 3 && ips_to_delete_count == 0) {
        printf("Output matches the expected result. Test passed.\n");
    } else {
        printf("Counts do NOT match expected values. Expected ADD Count: 3, DELETE Count: 0.\n");
    }

    free_mock_inets(new_subnets, 3);

    return 0;
}


int test_np_lan_subnet_find_lan_subnets_delta_hash_table() {
    struct argo_inet *old_subnets[] = {
        create_mock_inet("***********/24"),
        create_mock_inet("10.0.0.0/24")
    };
    struct argo_inet *new_subnets[] = {
        create_mock_inet("***********/24"),
        create_mock_inet("**********/24"),
        create_mock_inet("**********/24")
    };

    struct argo_inet *ips_to_add[10];
    struct argo_inet *ips_to_delete[10];
    int ips_to_add_count = 0;
    int ips_to_delete_count = 0;

    np_lan_subnet_find_lan_subnets_delta_hash_table((const struct argo_inet **)new_subnets, 3,
                                           (const struct argo_inet **)old_subnets, 2,
                                           ips_to_add, 10, &ips_to_add_count,
                                           ips_to_delete, 10, &ips_to_delete_count,
                                           12345, 67890);

    char ip_buffer[128];

    printf("\nResults:\n");
    printf("ADD Count: %d, DELETE Count: %d\n", ips_to_add_count, ips_to_delete_count);
    for (int i = 0; i < ips_to_add_count; i++) {
        argo_inet_generate(ip_buffer, ips_to_add[i]);
        printf("Added IP: %s\n", ip_buffer);
    }

    for (int i = 0; i < ips_to_delete_count; i++) {
        argo_inet_generate(ip_buffer, ips_to_delete[i]);
        printf("Deleted IP: %s\n", ip_buffer);
    }

    if (ips_to_add_count == 2 && ips_to_delete_count == 1) {
        const char *expected_adds[] = {"**********/24", "**********/24"};

        const char *expected_deletes[] = {"10.0.0.0/24"};

        int valid = 1;
        for (int i = 0; i < ips_to_add_count; i++) {
            argo_inet_generate(ip_buffer, ips_to_add[i]);
            if (strcmp(ip_buffer, expected_adds[i]) != 0) {
                printf("Validation Error: Added IP does not match! Expected: %s, Got: %s\n",
                       expected_adds[i], ip_buffer);
                valid = 0;
            }
        }

        for (int i = 0; i < ips_to_delete_count; i++) {
            argo_inet_generate(ip_buffer, ips_to_delete[i]);
            if (strcmp(ip_buffer, expected_deletes[i]) != 0) {
                printf("Validation Error: Deleted IP does not match! Expected: %s, Got: %s\n",
                       expected_deletes[i], ip_buffer);
                valid = 0;
            }
        }

        if (valid) {
            printf("Output matches the expected result. Test passed.\n");
        } else {
            printf("Test failed due to validation errors.\n");
        }
    } else {
        printf("Validation Error: Counts do NOT match expected values!\n");
        printf("Expected ADD Count: 2, DELETE Count: 1\n");
        printf("Got ADD Count: %d, DELETE Count: %d\n", ips_to_add_count, ips_to_delete_count);
    }

    free_mock_inets(old_subnets, 2);
    free_mock_inets(new_subnets, 3);

    return 0; // Success
}


int test_case_all_deleted_subnets() {
    printf("\n=== Test Case: All Subnets Deleted ===\n");

    struct argo_inet *old_subnets[] = {
        create_mock_inet("***********/24"),
        create_mock_inet("********/24"),
        create_mock_inet("**********/24")
    };
    struct argo_inet *new_subnets[] = {};

    struct argo_inet *ips_to_add[10];
    struct argo_inet *ips_to_delete[10];
    int ips_to_add_count = 0;
    int ips_to_delete_count = 0;

    np_lan_subnet_find_lan_subnets_delta_hash_table((const struct argo_inet **)new_subnets, 0,
                                         (const struct argo_inet **)old_subnets, 3,
                                         ips_to_add, 10, &ips_to_add_count,
                                         ips_to_delete, 10, &ips_to_delete_count,
                                         12345, 67890);

    printf("ADD Count: %d, DELETE Count: %d\n", ips_to_add_count, ips_to_delete_count);

    if (ips_to_add_count == 0 && ips_to_delete_count == 3) {
        printf("Output matches the expected result. Test passed.\n");
    } else {
        printf("Counts do NOT match expected values. Expected ADD Count: 0, DELETE Count: 3.\n");
    }

    free_mock_inets(old_subnets, 3);

    return 0;
}

// Test Case: Partially Overlapping Subnets
int test_case_partial_overlap() {
    printf("\n=== Test Case: Partially Overlapping Subnets ===\n");

    struct argo_inet *old_subnets[] = {
        create_mock_inet("***********/24"),
        create_mock_inet("********/24")
    };
    struct argo_inet *new_subnets[] = {
        create_mock_inet("***********/24"),
        create_mock_inet("**********/24")
    };

    struct argo_inet *ips_to_add[10];
    struct argo_inet *ips_to_delete[10];
    int ips_to_add_count = 0;
    int ips_to_delete_count = 0;

    np_lan_subnet_find_lan_subnets_delta_hash_table((const struct argo_inet **)new_subnets, 2,
                                         (const struct argo_inet **)old_subnets, 2,
                                         ips_to_add, 10, &ips_to_add_count,
                                         ips_to_delete, 10, &ips_to_delete_count,
                                         12345, 67890);

    printf("ADD Count: %d, DELETE Count: %d\n", ips_to_add_count, ips_to_delete_count);

    if (ips_to_add_count == 1 && ips_to_delete_count == 1) {
        printf("Output matches the expected result. Test passed.\n");
    } else {
        printf("Counts do NOT match expected values. Expected ADD Count: 1, DELETE Count: 1.\n");
    }

    free_mock_inets(old_subnets, 2);
    free_mock_inets(new_subnets, 2);

    return 0;
}




int main() {
    test_np_lan_subnet_find_lan_subnets_delta_hash_table();
    test_case_empty_subnets_2();                    // Edge: Empty subnets
    test_case_identical_subnets_2();                // Edge: Identical subnets
    test_case_all_new_subnets_2();                  // Edge: All new subnets
    test_case_all_deleted_subnets();              // All subnets deleted
    test_case_partial_overlap();                  // Partially overlapping subnets
    return 0;
}
