/*
 * Performance Test Program for np_lan_subnet_find_lan_subnets_delta and np_lan_subnet_find_lan_subnets_delta_hash_table
 *
 * Goals:
 * - Compare the execution performance of two functions: np_lan_subnet_find_lan_subnets_delta (Nested Loop, O(n^2)) and
 *   np_lan_subnet_find_lan_subnets_delta_hash_table (Hash Table Look Up, O(n)).
 * - Test their behavior with small-scale (few subnets) and large-scale (1000 subnets) input datasets
 *   under various data conditions.
 * - Evaluate edge cases such as all new subnets, all deleted subnets, and minimal overlap scenarios.
 *
 * How it works:
 * 1. The program tests two functions under five scenarios:
 *    - Small dataset (fixed arrays of 2-3 subnets, iterated 10,000 times).
 *    - Large dataset (dynamically generated datasets with 1000 subnets, iterated 100 times).
 *    - Large dataset with all subnets new.
 *    - Large dataset with all subnets deleted.
 *    - Large dataset with minimal overlap (mixed additions and deletions).
 * 2. Subnets are dynamically created, and the results of each function are recorded.
 * 3. The time taken for both functions is measured, and the performance differences are reported in seconds.
 *
 * Test Results Summary:
 *
 * === SMALL TEST ===
 * - Nested Loop Way (np_lan_subnet_find_lan_subnets_delta):  0.002149 seconds
 * - Hash Table Way (np_lan_subnet_find_lan_subnets_delta_hash_table): 0.037434 seconds
 * In the small test (10,000 iterations), the Nested Loop way is significantly faster than the Hash Table way.

 * === LARGE TEST ===
 * - Nested Loop Way (np_lan_subnet_find_lan_subnets_delta):  0.106114 seconds
 * - Hash Table Way (np_lan_subnet_find_lan_subnets_delta_hash_table): 0.114547 seconds
 * In the large test (100 iterations, 1000 subnets), both functions show comparable performance, though the Nested Loop way
 * is slightly faster than the Hash Table way.

 * === Test Case: Large Dataset with All New Subnets ===
 * - Nested Loop Way (np_lan_subnet_find_lan_subnets_delta):  0.000003 seconds
 * - Hash Table Way (np_lan_subnet_find_lan_subnets_delta_hash_table): 0.000560 seconds
 * In the all-new subnets test, the Nested Loop way is extremely fast for this scenario, processing in near-zero time.

 * === Test Case: Large Dataset with All Subnets Deleted ===
 * - Nested Loop Way (np_lan_subnet_find_lan_subnets_delta):  0.000002 seconds
 * - Hash Table Way (np_lan_subnet_find_lan_subnets_delta_hash_table): 0.000538 seconds
 * Both functions show excellent performance when processing deletions with the Nested Loop way being marginally faster.

 * === Test Case: Large Dataset with Minimal Overlap ===
 * - Nested Loop Way (np_lan_subnet_find_lan_subnets_delta):  0.005083 seconds
 * - Hash Table Way (np_lan_subnet_find_lan_subnets_delta_hash_table): 0.001130 seconds
 * In the minimal overlap case, the Hash Table way outperforms the Nested Loop way by a significant margin due to
 * its better scalability in managing mixed additions and deletions.
 *
 * Observations:
 * - The Nested Loop way (O(n^2)) outperforms the Hash Table way (O(n)) for very small datasets and edge cases dealing
 *   with mostly uniform input.
 * - The Hash Table way demonstrates superior performance when dealing with complex datasets involving overlapping,
 *   mixed additions, and deletions due to its efficient lookup mechanism.
 * - Both techniques show scalable behavior for large datasets with only small differences in runtime performance.
 */
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <time.h>
#include "argo/argo.h"
#include "zhash/zhash_table.h"
#include "wally/wally.h"
#include "np_lib/np_lan_subnets.h"


struct argo_inet *create_mock_inet(const char *address_with_netmask) {
    struct argo_inet *inet = malloc(sizeof(struct argo_inet));
    if (!inet) {
        fprintf(stderr, "Failed to allocate memory for argo_inet\n");
        exit(EXIT_FAILURE);
    }

    if (argo_string_to_inet(address_with_netmask, inet) != 0) {
        fprintf(stderr, "Failed to parse address: %s\n", address_with_netmask);
        free(inet);
        exit(EXIT_FAILURE);
    }

    return inet;
}

void free_mock_inets(struct argo_inet **inets, int count) {
    for (int i = 0; i < count; i++) {
        free(inets[i]);
    }
}

struct argo_inet **generate_mock_subnets(int count, const char *base_ip, int base_netmask) {
    struct argo_inet **subnets = malloc(count * sizeof(struct argo_inet *));
    if (!subnets) {
        fprintf(stderr, "Failed to allocate memory for subnets\n");
        exit(EXIT_FAILURE);
    }

    for (int i = 0; i < count; i++) {
        char subnet[64];
        snprintf(subnet, sizeof(subnet), "%s.%d/%d", base_ip, i % 256, base_netmask);
        subnets[i] = malloc(sizeof(struct argo_inet));
        if (!subnets[i]) {
            fprintf(stderr, "Failed to allocate memory for argo_inet\n");
            exit(EXIT_FAILURE);
        }
        if (argo_string_to_inet(subnet, subnets[i]) != 0) {
            fprintf(stderr, "Failed to parse address: %s\n", subnet);
            free(subnets[i]);
            exit(EXIT_FAILURE);
        }
    }

    return subnets;
}

void free_mock_subnets(struct argo_inet **subnets, int count) {
    for (int i = 0; i < count; i++) {
        if (subnets[i]) {
            free(subnets[i]);
        }
    }
    free(subnets);
}


void compare_small_performance() {
    printf("=== SMALL TEST ===\n");
    struct argo_inet *old_subnets[] = {
        create_mock_inet("***********/24"),
        create_mock_inet("10.0.0.0/24")
    };
    struct argo_inet *new_subnets[] = {
        create_mock_inet("***********/24"),
        create_mock_inet("**********/24"),
        create_mock_inet("**********/24")
    };

    struct argo_inet *ips_to_add[10];
    struct argo_inet *ips_to_delete[10];
    int ips_to_add_count = 0;
    int ips_to_delete_count = 0;

    const int iterations = 10000;
    clock_t start, end;

    start = clock();
    for (int i = 0; i < iterations; i++) {
        np_lan_subnet_find_lan_subnets_delta((const struct argo_inet **)new_subnets, 3,
                                             (const struct argo_inet **)old_subnets, 2,
                                             ips_to_add, 10, &ips_to_add_count,
                                             ips_to_delete, 10, &ips_to_delete_count,
                                             12345, 67890);
    }
    end = clock();
    double time_a = ((double)(end - start)) / CLOCKS_PER_SEC;

    ips_to_add_count = 0;
    ips_to_delete_count = 0;

    start = clock();
    for (int i = 0; i < iterations; i++) {
        np_lan_subnet_find_lan_subnets_delta_hash_table((const struct argo_inet **)new_subnets, 3,
                                               (const struct argo_inet **)old_subnets, 2,
                                               ips_to_add, 10, &ips_to_add_count,
                                               ips_to_delete, 10, &ips_to_delete_count,
                                               12345, 67890);
    }
    end = clock();
    double time_b = ((double)(end - start)) / CLOCKS_PER_SEC;

    printf("\nSmall Performance Results (%d iterations):\n", iterations);
    printf("Nested Loop way (np_lan_subnet_find_lan_subnets_delta): %.6f seconds\n", time_a);
    printf("Hash table (np_lan_subnet_find_lan_subnets_delta_hash_table): %.6f seconds\n", time_b);

    free_mock_inets(old_subnets, 2);
    free_mock_inets(new_subnets, 3);
}

void compare_large_performance(int subnet_count) {
    printf("\n=== LARGE TEST ===\n");
    struct argo_inet **old_subnets = generate_mock_subnets(subnet_count, "192.168.0", 24);
    struct argo_inet **new_subnets = generate_mock_subnets(subnet_count, "192.168.0", 24);

    for (int i = 0; i < subnet_count / 10; i++) {
        char new_subnet[64];
        snprintf(new_subnet, sizeof(new_subnet), "172.16.%d.%d/24", i / 256, i % 256);
        argo_string_to_inet(new_subnet, new_subnets[i]);
    }

    struct argo_inet **ips_to_add = malloc(subnet_count * sizeof(struct argo_inet *));
    struct argo_inet **ips_to_delete = malloc(subnet_count * sizeof(struct argo_inet *));
    int ips_to_add_count = 0;
    int ips_to_delete_count = 0;

    const int iterations = 100;
    clock_t start, end;

    start = clock();
    for (int i = 0; i < iterations; i++) {
        np_lan_subnet_find_lan_subnets_delta((const struct argo_inet **)new_subnets, subnet_count,
                                             (const struct argo_inet **)old_subnets, subnet_count,
                                             ips_to_add, subnet_count, &ips_to_add_count,
                                             ips_to_delete, subnet_count, &ips_to_delete_count,
                                             12345, 67890);
    }
    end = clock();
    double time_a = ((double)(end - start)) / CLOCKS_PER_SEC;

    ips_to_add_count = 0;
    ips_to_delete_count = 0;

    start = clock();
    for (int i = 0; i < iterations; i++) {
        np_lan_subnet_find_lan_subnets_delta_hash_table((const struct argo_inet **)new_subnets, subnet_count,
                                               (const struct argo_inet **)old_subnets, subnet_count,
                                               ips_to_add, subnet_count, &ips_to_add_count,
                                               ips_to_delete, subnet_count, &ips_to_delete_count,
                                               12345, 67890);
    }
    end = clock();
    double time_b = ((double)(end - start)) / CLOCKS_PER_SEC;

    printf("\nLarge Performance Results (%d iterations, %d subnets):\n", iterations, subnet_count);
    printf("Nested loop way (np_lan_subnet_find_lan_subnets_delta): %.6f seconds\n", time_a);
    printf("Hash table way (np_lan_subnet_find_lan_subnets_delta_hash_table): %.6f seconds\n", time_b);

    free_mock_subnets(old_subnets, subnet_count);
    free_mock_subnets(new_subnets, subnet_count);
    free(ips_to_add);
    free(ips_to_delete);
}

void test_case_large_all_new(int subnet_count) {
    printf("\n=== Test Case: Large Dataset with All New Subnets ===\n");

    struct argo_inet **old_subnets = malloc(0); // Empty old_subnets
    struct argo_inet **new_subnets = generate_mock_subnets(subnet_count, "192.168.0", 24);

    struct argo_inet **ips_to_add = malloc(subnet_count * sizeof(struct argo_inet *));
    struct argo_inet **ips_to_delete = malloc(subnet_count * sizeof(struct argo_inet *));
    int ips_to_add_count = 0;
    int ips_to_delete_count = 0;

    clock_t start, end;

    start = clock();
    np_lan_subnet_find_lan_subnets_delta((const struct argo_inet **)new_subnets, subnet_count,
                                         (const struct argo_inet **)old_subnets, 0,
                                         ips_to_add, subnet_count, &ips_to_add_count,
                                         ips_to_delete, subnet_count, &ips_to_delete_count,
                                         12345, 67890);
    end = clock();
    double time_a = ((double)(end - start)) / CLOCKS_PER_SEC;

    start = clock();
    np_lan_subnet_find_lan_subnets_delta_hash_table((const struct argo_inet **)new_subnets, subnet_count,
                                           (const struct argo_inet **)old_subnets, 0,
                                           ips_to_add, subnet_count, &ips_to_add_count,
                                           ips_to_delete, subnet_count, &ips_to_delete_count,
                                           12345, 67890);
    end = clock();
    double time_b = ((double)(end - start)) / CLOCKS_PER_SEC;

    printf("\nPerformance Results (%d subnets - All New):\n", subnet_count);
    printf("Function A: %.6f seconds\n", time_a);
    printf("Function B: %.6f seconds\n", time_b);

    free_mock_subnets(new_subnets, subnet_count);
    free(ips_to_add);
    free(ips_to_delete);
}

void test_case_large_all_deleted(int subnet_count) {
    printf("\n=== Test Case: Large Dataset with All Subnets Deleted ===\n");

    struct argo_inet **old_subnets = generate_mock_subnets(subnet_count, "192.168.0", 24);
    struct argo_inet **new_subnets = malloc(0); // Empty new_subnets

    struct argo_inet **ips_to_add = malloc(subnet_count * sizeof(struct argo_inet *));
    struct argo_inet **ips_to_delete = malloc(subnet_count * sizeof(struct argo_inet *));
    int ips_to_add_count = 0;
    int ips_to_delete_count = 0;

    clock_t start, end;

    start = clock();
    np_lan_subnet_find_lan_subnets_delta((const struct argo_inet **)new_subnets, 0,
                                         (const struct argo_inet **)old_subnets, subnet_count,
                                         ips_to_add, subnet_count, &ips_to_add_count,
                                         ips_to_delete, subnet_count, &ips_to_delete_count,
                                         12345, 67890);
    end = clock();
    double time_a = ((double)(end - start)) / CLOCKS_PER_SEC;

    start = clock();
    np_lan_subnet_find_lan_subnets_delta_hash_table((const struct argo_inet **)new_subnets, 0,
                                           (const struct argo_inet **)old_subnets, subnet_count,
                                           ips_to_add, subnet_count, &ips_to_add_count,
                                           ips_to_delete, subnet_count, &ips_to_delete_count,
                                           12345, 67890);
    end = clock();
    double time_b = ((double)(end - start)) / CLOCKS_PER_SEC;

    printf("\nPerformance Results (%d subnets - All Deleted):\n", subnet_count);
    printf("Nest loop way: %.6f seconds\n", time_a);
    printf("Hash table way: %.6f seconds\n", time_b);

    free_mock_subnets(old_subnets, subnet_count);
    free(ips_to_add);
    free(ips_to_delete);
}

void test_case_minimal_overlap(int subnet_count) {
    printf("\n=== Test Case: Large Dataset with Minimal Overlap ===\n");

    struct argo_inet **old_subnets = generate_mock_subnets(subnet_count, "192.168.0", 24);
    struct argo_inet **new_subnets = generate_mock_subnets(subnet_count, "10.0.0", 24);

    for (int i = 0; i < 5; i++) {
        free(new_subnets[i]);
        new_subnets[i] = create_mock_inet("***********/24");
    }

    struct argo_inet **ips_to_add = malloc(subnet_count * sizeof(struct argo_inet *));
    struct argo_inet **ips_to_delete = malloc(subnet_count * sizeof(struct argo_inet *));
    int ips_to_add_count = 0;
    int ips_to_delete_count = 0;

    clock_t start, end;

    start = clock();
    np_lan_subnet_find_lan_subnets_delta((const struct argo_inet **)new_subnets, subnet_count,
                                         (const struct argo_inet **)old_subnets, subnet_count,
                                         ips_to_add, subnet_count, &ips_to_add_count,
                                         ips_to_delete, subnet_count, &ips_to_delete_count,
                                         12345, 67890);
    end = clock();
    double time_a = ((double)(end - start)) / CLOCKS_PER_SEC;

    start = clock();
    np_lan_subnet_find_lan_subnets_delta_hash_table((const struct argo_inet **)new_subnets, subnet_count,
                                           (const struct argo_inet **)old_subnets, subnet_count,
                                           ips_to_add, subnet_count, &ips_to_add_count,
                                           ips_to_delete, subnet_count, &ips_to_delete_count,
                                           12345, 67890);
    end = clock();
    double time_b = ((double)(end - start)) / CLOCKS_PER_SEC;

    printf("\nPerformance Results (%d subnets - Minimal Overlap):\n", subnet_count);
    printf("Nested loop way: %.6f seconds\n", time_a);
    printf("Hash table way: %.6f seconds\n", time_b);

    free_mock_subnets(old_subnets, subnet_count);
    free_mock_subnets(new_subnets, subnet_count);
    free(ips_to_add);
    free(ips_to_delete);
}

int main() {
    int subnet_count = 1000;

    compare_small_performance();
    compare_large_performance(subnet_count);
    test_case_large_all_new(subnet_count);
    test_case_large_all_deleted(subnet_count);
    test_case_minimal_overlap(subnet_count);

    return 0;
}
