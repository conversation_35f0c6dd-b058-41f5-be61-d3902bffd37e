/*
 * np_bgp.c. Copyright (C) 2025 Zscaler Inc. All Rights Reserved.
 */

#include "zpath_lib/zpath_debug.h"
#include "zpath_misc/zpath_misc.h"

#include "np_lib/np_private.h"
#include "np_lib/np_lan_subnets.h"
#include "np_lib/np_bgp.h"
#include "np_lib/np_bgp_config.h"
#include "np_lib/np_bgp_connector_session_config.h"
#include "np_lib/np_bgp_connectors_config.h"
#include "np_lib/np_bgp_gateways_config.h"
#include "np_lib/np_bgp_gateway_session_config.h"
#include "np_lib/np_frr_utils.h"
#include "np_lib/np_rpc.h"

#define NP_BGP_PEER_HASH_KEY_LEN 50

#define FRR_RELOAD_OUTPUT_MAX_LEN 10000

#define NP_BGP_FRR_TIMER_PUSH_US (1000*1000) /* 1s */

static struct zevent_base *np_bgp_zevent_base = NULL;

struct argo_structure_description *np_bgp_memory_allocation_stats_description;

static struct np_bgp_memory_allocation_stats{           /* _ARGO: object_definition */
    int64_t num_gateway_peers_allocated;                /* _ARGO: integer */
    int64_t num_gateway_peers_freed;                    /* _ARGO: integer */
    int64_t num_connector_peers_allocated;              /* _ARGO: integer */
    int64_t num_connector_peers_freed;                  /* _ARGO: integer */
    int64_t num_router_peers_allocated;                 /* _ARGO: integer */
    int64_t num_router_peers_freed;                     /* _ARGO: integer */
    int64_t num_client_subnets_allocated;               /* _ARGO: integer */
    int64_t num_client_subnets_freed;                   /* _ARGO: integer */
    int64_t num_lan_subnets_allocated;                  /* _ARGO: integer */
    int64_t num_lan_subnets_freed;                      /* _ARGO: integer */
} mem_alloc_stats;
#include "np_lib/np_bgp_compiled_c.h"

struct timer_parameter {
    struct np_bgp_state *bgp_state;
    int64_t start_time_mono_s;
    uint8_t override_config_changed:1;
};

const char *np_bgp_instance_type_strings[] = {
    [np_bgp_instance_none] = "NONE",
    [np_bgp_instance_gateway] = "GATEWAY",
    [np_bgp_instance_connector] = "CONNECTOR",
    [np_bgp_instance_router] = "ROUTER"
};

const char *np_bgp_instance_type_str(int result)
{
    if (result >= (sizeof(np_bgp_instance_type_strings) / sizeof (const char *))) return "INVALID";
    if (result < 0) return "INVALID";
    if (np_bgp_instance_type_strings[result] == NULL) return "INVALID";
    return np_bgp_instance_type_strings[result];
}

static int
np_bgp_dump_mem_stats(struct zpath_debug_state  *request_state,
                      const char                **query_values __attribute__((unused)),
                      int                       query_value_count __attribute__((unused)),
                      void                      *cookie __attribute__((unused)))
{
    char jsonout[10000];

    if (ARGO_RESULT_NO_ERROR == argo_structure_dump(np_bgp_memory_allocation_stats_description,
                                                    &mem_alloc_stats, jsonout, sizeof(jsonout), NULL, 1)) {
        ZDP("%s\n", jsonout);
    }
    return NP_RESULT_NO_ERROR;
}

static int
np_bgp_debug_init()
{
    int res;
    np_wally_debug =
                    (NP_DEBUG_BGP_CONFIG_BIT) |
                    (NP_DEBUG_BGP_INSTANCE_BIT) |
                    (NP_DEBUG_BGP_SESSION_BIT) |
                    0;
    np_bgp_memory_allocation_stats_description = argo_register_global_structure(NP_BGP_MEMORY_ALLOCATION_STATS_HELPER);

    res = zpath_debug_add_read_command("dump BGP memory allocation stats",
                                        "/np/bgp/dump_mem_stats",
                                        np_bgp_dump_mem_stats,
                                        NULL,
                                        NULL);
    if (res){
        NP_LOG(AL_ERROR, "Couldn't add curl debug command /np/bgp/dump_mem_stats");
        return res;
    }

    return NP_RESULT_NO_ERROR;
}

static void
free_bgp_peer_object(void *element, void *cookie)
{
    struct argo_object *object = element;
    const char *instance_type = (const char *)cookie;
    struct np_bgp_peer_config *peer = object->base_structure_void;

    if (!peer) return;
    if (IS_NP_DEBUG_BGP_CONFIG()) {
        char str[ARGO_INET_ADDRSTRLEN];
        NP_LOG(AL_NOTICE, "Releasing peer %s, config gid: %"PRId64", asn: %"PRId64", ip: %s",
                peer->instance_type, peer->bgp_config_gid, peer->asn, argo_inet_generate(str, &(peer->ip)));
        np_bgp_config_dump_peer_config(peer);
    }
    argo_object_release(object);

    /* stats */
    if (!instance_type) return;
    if (0 == strcmp(instance_type, np_bgp_instance_type_str(np_bgp_instance_gateway))) {
        __sync_add_and_fetch_8(&mem_alloc_stats.num_gateway_peers_freed, 1);
    } else if (0 == strcmp(instance_type, np_bgp_instance_type_str(np_bgp_instance_connector))) {
        __sync_add_and_fetch_8(&mem_alloc_stats.num_connector_peers_freed, 1);
    } else if (0 == strcmp(instance_type, np_bgp_instance_type_str(np_bgp_instance_router))) {
        __sync_add_and_fetch_8(&mem_alloc_stats.num_router_peers_freed, 1);
    } else {
        NP_LOG(AL_ERROR, "unrecognized bgp instance type: %s", instance_type);
    }

    return;
}

static void
free_peer_ip_to_config_gid_mapping_count(void *element, void *cookie __attribute__((unused)))
{
    struct np_bgp_neighbor_ip_to_gid_mapping *ip_to_gid_element = (struct np_bgp_neighbor_ip_to_gid_mapping *)element;
    if (!ip_to_gid_element) return;

    NP_DEBUG_BGP_CONFIG("Releasing ip_to_config_gid_mapping for:%"PRId64"", ip_to_gid_element->bgp_config_gid);
    NP_BGP_FREE(ip_to_gid_element);
    return;
}

static void
free_client_subnet(void *element, void *cookie __attribute__((unused)))
{
    struct argo_inet *inet = element;
    if (!inet) return;
    char str[ARGO_INET_ADDRSTRLEN];
    NP_DEBUG_BGP_CONFIG("Releasing client subnet %s", argo_inet_generate(str, inet));
    NP_BGP_FREE(inet);

    /* stats */
    __sync_add_and_fetch_8(&mem_alloc_stats.num_client_subnets_freed, 1);
    return;
}

static void
free_lan_subnet(void *element, void *cookie __attribute__((unused)))
{
    struct argo_inet *inet = element;
    if (!inet) return;
    char str[ARGO_INET_ADDRSTRLEN];
    NP_DEBUG_BGP_CONFIG("Releasing lan subnet %s", argo_inet_generate(str, inet));
    NP_BGP_FREE(inet);

    /* stats */
    __sync_add_and_fetch_8(&mem_alloc_stats.num_lan_subnets_freed, 1);
    return;
}

static int
gateway_frr_load_connector_peers(void *cookie, void *value,
                                 void *key __attribute__((unused)),
                                size_t key_len __attribute__((unused)))
{
    struct np_bgp_config_gateway *config = (struct np_bgp_config_gateway *)cookie;
    struct argo_object *peer = (struct argo_object *)value;

    if (config->connectors_count >= NP_BGP_MAX_PEERS) {
        NP_LOG(AL_WARNING, "Exceeding maximum number of connector peers!");
        return NP_RESULT_NO_ERROR;
    }
    config->connectors[config->connectors_count++] = peer;
    return NP_RESULT_NO_ERROR;
}

static int
gateway_frr_load_client_subnets(void *cookie, void *value,
                                void *key __attribute__((unused)),
                                size_t key_len __attribute__((unused)))
{
    struct np_bgp_config_gateway *config = (struct np_bgp_config_gateway *)cookie;
    struct argo_inet *subnet = (struct argo_inet *)value;

    if (config->client_subnets_count >= NP_BGP_MAX_SUBNETS) {
        NP_LOG(AL_WARNING, "Exceeding maximum number of client subnets!");
        return NP_RESULT_NO_ERROR;
    }
    config->client_subnets[config->client_subnets_count++] = subnet;
    return NP_RESULT_NO_ERROR;
}

static int
connector_frr_load_gateway_peers(void *cookie, void *value,
                                 void *key __attribute__((unused)),
                                 size_t key_len __attribute__((unused)))
{
    struct np_bgp_config_connector *config = (struct np_bgp_config_connector *)cookie;
    struct argo_object *peer = (struct argo_object *)value;

    if (config->gateways_count >= NP_BGP_MAX_PEERS) {
        NP_LOG(AL_WARNING, "Exceeding maximum number of gateway peers!");
        return NP_RESULT_NO_ERROR;
    }
    config->gateways[config->gateways_count++] = peer;
    return NP_RESULT_NO_ERROR;
}

static int
connector_frr_load_router_peers(void *cookie, void *value,
                                void *key __attribute__((unused)),
                                size_t key_len __attribute__((unused)))
{
    struct np_bgp_config_connector *config = (struct np_bgp_config_connector *)cookie;
    struct argo_object *peer = (struct argo_object *)value;

    if (config->routers_count >= NP_BGP_MAX_PEERS) {
        NP_LOG(AL_WARNING, "Exceeding maximum number of router peers!");
        return NP_RESULT_NO_ERROR;
    }
    config->routers[config->routers_count++] = peer;
    return NP_RESULT_NO_ERROR;
}

static int
connector_frr_load_client_subnets(void *cookie, void *value,
                                  void *key __attribute__((unused)),
                                  size_t key_len __attribute__((unused)))
{
    struct np_bgp_config_connector *config = (struct np_bgp_config_connector *)cookie;
    struct argo_inet *subnet = (struct argo_inet *)value;

    if (config->client_subnets_count >= NP_BGP_MAX_SUBNETS) {
        NP_LOG(AL_WARNING, "Exceeding maximum number of client subnets!");
        return NP_RESULT_NO_ERROR;
    }
    config->client_subnets[config->client_subnets_count++] = subnet;
    return NP_RESULT_NO_ERROR;
}

static int
connector_frr_load_lan_subnets(void *cookie, void *value,
                               void *key __attribute__((unused)),
                               size_t key_len __attribute__((unused)))
{
    struct np_bgp_config_connector *config = (struct np_bgp_config_connector *)cookie;
    struct argo_inet *subnet = (struct argo_inet *)value;

    if (config->lan_subnets_count >= NP_BGP_MAX_SUBNETS) {
        NP_LOG(AL_WARNING, "Exceeding maximum number of lan subnets!");
        return NP_RESULT_NO_ERROR;
    }
    config->lan_subnets[config->lan_subnets_count++] = subnet;
    return NP_RESULT_NO_ERROR;
}

/* Generate frr config for gateway
 * Assuming bgp_state lock held */
static int
np_bgp_generate_gateway_frr_conf(struct np_bgp_state *bgp_state)
{
    int res = NP_RESULT_NO_ERROR;

    if (bgp_state->deletion_in_progress) {
        NP_LOG(AL_NOTICE, "BGP state is being deleted, skip frr config generation");
        return NP_RESULT_NO_ERROR;
    }

    struct np_bgp_config_gateway *config = NP_BGP_CALLOC(sizeof(struct np_bgp_config_gateway));
    if (!config) return NP_RESULT_NO_MEMORY;

    if (bgp_state->frr_version) {
        config->version = bgp_state->frr_version;
        config->has_version = 1;
    } else {
        config->has_version = 0;
    }
    config->router_id = bgp_state->router_id;
    config->ip = bgp_state->ip;
    config->asn = bgp_state->asn;
    config->vrf_name = bgp_state->vrf_name;
    config->wg_interface_name = bgp_state->wg_interface_name;

    /* load connector peers from the bgp state peer hash */
    zhash_table_walk(bgp_state->peer_connectors, NULL, gateway_frr_load_connector_peers, config);

    /* load client subnets from the bgp state subnet hash */
    zhash_table_walk(bgp_state->client_subnets, NULL, gateway_frr_load_client_subnets, config);

    res = np_bgp_config_generate_gateway_frr_conf(config, NP_BGP_FRR_CONFIG_FILENAME_GENERATED);
    if (res) {
        NP_LOG(AL_ERROR, "Failed to generate gateway frr config %s", zpath_result_string(res));
    }
    NP_BGP_FREE(config);

    return res;
}

/* Generate frr config for connector
 * Assuming bgp_state lock held */
static int
np_bgp_generate_connector_frr_conf(struct np_bgp_state *bgp_state)
{
    int res = NP_RESULT_NO_ERROR;

    if (bgp_state->deletion_in_progress) {
        NP_LOG(AL_NOTICE, "BGP state is being deleted, skip frr config generation");
        return NP_RESULT_NO_ERROR;
    }

    struct np_bgp_config_connector *config = NP_BGP_CALLOC(sizeof(struct np_bgp_config_connector));
    if (!config) return NP_RESULT_NO_MEMORY;

    if (bgp_state->frr_version) {
        config->version = bgp_state->frr_version;
        config->has_version = 1;
    } else {
        config->has_version = 0;
    }
    config->router_id = bgp_state->router_id;
    config->ip = bgp_state->ip;
    config->asn = bgp_state->asn;
    config->disable_local_sourcing = bgp_state->disable_local_sourcing;

    /* load gateway and router peers from the bgp state peer hash */
    zhash_table_walk(bgp_state->peer_gateways, NULL, connector_frr_load_gateway_peers, config);
    zhash_table_walk(bgp_state->peer_routers, NULL, connector_frr_load_router_peers, config);

    /* load client subnets and lan subnets peers from the bgp state subnet hash */
    zhash_table_walk(bgp_state->client_subnets, NULL, connector_frr_load_client_subnets, config);
    zhash_table_walk(bgp_state->lan_subnets, NULL, connector_frr_load_lan_subnets, config);

    res = np_bgp_config_generate_connector_frr_conf(config, NP_BGP_FRR_CONFIG_FILENAME_GENERATED);
    if (res) {
        NP_LOG(AL_ERROR, "Failed to generate connector frr config %s", zpath_result_string(res));
    }
    NP_BGP_FREE(config);

    return res;
}

/* Generate frr config for connector
 * Assuming bgp_state lock held */
static int
np_bgp_generate_override_frr_conf(const char *override_config)
{
    FILE *fp;
    fp = fopen(NP_BGP_FRR_CONFIG_FILENAME_OVERRIDE, "w");
    if (!fp) {
        NP_LOG(AL_ERROR, "Failed to open file %s to write override config: %s", NP_BGP_FRR_CONFIG_FILENAME_OVERRIDE, strerror(errno));
        return NP_RESULT_ERR;
    }

    if (fwrite(override_config, strlen(override_config), 1, fp) != 1) {
        NP_LOG(AL_ERROR, "Failed to write override config to file %s: %s", NP_BGP_FRR_CONFIG_FILENAME_OVERRIDE, strerror(errno));
        fclose(fp);
        unlink(NP_BGP_FRR_CONFIG_FILENAME_OVERRIDE);
        return NP_RESULT_ERR;
    }
    fclose(fp);

    return NP_RESULT_NO_ERROR;
}

static void
frr_timer(int sock __attribute__((unused)), short flags __attribute__((unused)), void *cookie)
{
    struct timer_parameter *timer_param = cookie;
    struct np_bgp_state *bgp_state = timer_param->bgp_state;
    uint8_t override_config_changed = timer_param->override_config_changed;
    int do_reload = 0;
    int res = NP_RESULT_NO_ERROR;

    zthread_heartbeat(NULL);

    ZPATH_MUTEX_LOCK(&(bgp_state->lock), __FILE__, __LINE__);

    NP_DEBUG_BGP_CONFIG("%"PRId64" NP FRR timer fired", bgp_state->customer_gid);

    NP_BGP_FREE(timer_param);
    if (bgp_state->frr_timer) {
        event_free(bgp_state->frr_timer);
        bgp_state->frr_timer = NULL;
    }

    if (bgp_state->config_mode == np_bgp_config_mode_override) {
        /* override mode, reload frr config only if override_config changed */
        if (override_config_changed) {
            res = np_bgp_generate_override_frr_conf(bgp_state->override_config);
            if (res) {
                /* failed to write override config to local, report stats and skip config reload */
                np_frr_cfg_summary_stats_inc_generate_error_cnt(1/*is_override*/);
            } else {
                NP_DEBUG_BGP_CONFIG("Updated override NP BGP FRR config");
                do_reload = 1;
                np_frr_cfg_summary_stats_inc_generate_success_cnt(1/*is_override*/);
            }
        } else {
            NP_LOG(AL_NOTICE, "Skipping NP BGP FRR generation because config mode is set to OVERRIDE and override config hasn't changed");
        }
    } else {
#if 0
        /* refresh frr version each time */
        if (bgp_state->frr_version) NP_BGP_FREE(bgp_state->frr_version);
        bgp_state->frr_version = np_frr_parse_version();
#endif
        if (bgp_state->instance_type == np_bgp_instance_connector) {
            res = np_bgp_generate_connector_frr_conf(bgp_state);
            if (res) {
                /* something bad happened when generating frr.conf, report stats and skip the config reload */
                np_frr_cfg_summary_stats_inc_generate_error_cnt(0/*is_override*/);
            } else {
                NP_DEBUG_BGP_CONFIG("Generated NP Connector BGP FRR config");
                do_reload = 1;
                np_frr_cfg_summary_stats_inc_generate_success_cnt(0/*is_override*/);
            }
        } else if (bgp_state->instance_type == np_bgp_instance_gateway) {
            res = np_bgp_generate_gateway_frr_conf(bgp_state);
            if (res) {
                /* something bad happened when generating frr.conf, report stats and skip the config reload */
                np_frr_cfg_summary_stats_inc_generate_error_cnt(0/*is_override*/);
            } else {
                NP_DEBUG_BGP_CONFIG("Generated NP Gateway BGP FRR config");
                do_reload = 1;
                np_frr_cfg_summary_stats_inc_generate_success_cnt(0/*is_override*/);
            }
        } else {
            NP_LOG(AL_WARNING, "Unsupported instance type: %d", bgp_state->instance_type);
        }
    }

    if (do_reload) {
        char *buf = NP_BGP_CALLOC(sizeof(char) * FRR_RELOAD_OUTPUT_MAX_LEN);
        int use_override = (bgp_state->config_mode == np_bgp_config_mode_override) ? 1 : 0;
        np_bgp_frr_set_cfg_status(CONFIG_VERIFYING);
        res = np_frr_reload_conf(use_override, bgp_state->force_reload_config, buf, FRR_RELOAD_OUTPUT_MAX_LEN);
        if (res) {
            NP_LOG(AL_ERROR, "Failed to reload FRR config, mode: %s, force reload: %s",
                        use_override ? "OVERRIDE" : "GENERATED", bgp_state->force_reload_config ? "enabled" : "disabled");
            if (buf && IS_NP_DEBUG_BGP_CONFIG()) {
                NP_LOG(AL_INFO, "Error: %s", buf);
            }
            np_bgp_frr_set_cfg_status(CONFIG_ERROR);
        }

        np_bgp_frr_set_cfg_status(CONFIG_VALID);
        if (buf) NP_BGP_FREE(buf);
    }

    ZPATH_MUTEX_UNLOCK(&(bgp_state->lock), __FILE__, __LINE__);
}

/*
 * Schedule or refresh FRR config instantiate timer on np bgp thread
 * bgp_state lock must not be held when calling
 */
static void
schedule_frr_timer(struct np_bgp_state *bgp_state, int override_config_changed)
{
    struct timeval tv;
    struct timer_parameter *timer_param;
    int64_t delayed_s = 0;

    if (!bgp_state || bgp_state->deletion_in_progress) return;

    ZPATH_MUTEX_LOCK(&(bgp_state->lock), __FILE__, __LINE__);

    if (!bgp_state->frr_timer) {
        timer_param = NP_BGP_CALLOC(sizeof(*timer_param));
        if (!timer_param) {
            NP_LOG(AL_ERROR, "No memory for frr timer!");
            ZPATH_MUTEX_UNLOCK(&(bgp_state->lock), __FILE__, __LINE__);
            return;
        }
        timer_param->bgp_state = bgp_state;
        timer_param->override_config_changed = override_config_changed ? 1 : 0;
        timer_param->start_time_mono_s = monotime_s();
        bgp_state->frr_timer = event_new(zevent_event_base(bgp_state->zevent_base), -1, 0, frr_timer, timer_param);
        NP_DEBUG_BGP_CONFIG("%"PRId64" bgp state timer create, bgp config gid %"PRId64"",
                            bgp_state->customer_gid, bgp_state->bgp_config_gid);
    } else {
        /* Update timer parameters */
        timer_param = (struct timer_parameter *)event_get_callback_arg(bgp_state->frr_timer);
        NP_DEBUG_BGP_CONFIG("%"PRId64" frr timer push", bgp_state->customer_gid);
    }

    delayed_s = monotime_s() - timer_param->start_time_mono_s;
    if (delayed_s >= MAX_TIMER_DELAY_S) {
        /* We have already delayed 60 seconds on it, fire immediately */
        tv.tv_usec = 0;
        tv.tv_sec = 0;
        if (event_add(bgp_state->frr_timer, &tv) != 0) {
            NP_LOG(AL_ERROR, "%"PRId64" failed to schedule instant timer push", bgp_state->customer_gid);
        } else {
            NP_LOG(AL_NOTICE, "%"PRId64" already delayed np frr config generation for more than: %"PRId64" seconds, forcing instant instantiation",
                        bgp_state->customer_gid, delayed_s);
        }
    } else {
        /* This will schedule or reschedule the timer into the future */
        tv.tv_usec = NP_BGP_FRR_TIMER_PUSH_US % 1000000;
        tv.tv_sec = NP_BGP_FRR_TIMER_PUSH_US / 1000000;
        if (event_add(bgp_state->frr_timer, &tv) != 0) {
            NP_LOG(AL_ERROR, "%"PRId64": Failed to schedule instant timer push", bgp_state->customer_gid);
        }
    }
    ZPATH_MUTEX_UNLOCK(&(bgp_state->lock), __FILE__, __LINE__);
}

/* Generate frr config if force_regenerate is true and reload the frr conf.*/
int np_bgp_reload_frr_conf(struct np_bgp_state *bgp_state, enum np_bgp_instance_type inst_type, int force_regenerate) {

    if (!bgp_state) {
        return NP_RESULT_BAD_ARGUMENT;
    }
    int res = 0;

    ZPATH_MUTEX_LOCK(&(bgp_state->lock), __FILE__, __LINE__);
    // force generate frr conf if current mode is generated.
    if (force_regenerate && bgp_state->config_mode == np_bgp_config_mode_generated) {
        if (inst_type == np_bgp_instance_gateway) {
            np_bgp_generate_gateway_frr_conf(bgp_state);
        } else {
            np_bgp_generate_connector_frr_conf(bgp_state);
        }
    }

    char *buf = NP_BGP_CALLOC(sizeof(char) * FRR_RELOAD_OUTPUT_MAX_LEN);
    int use_override = (bgp_state->config_mode == np_bgp_config_mode_override) ? 1 : 0;

    res = np_frr_reload_conf(use_override, bgp_state->force_reload_config, buf, FRR_RELOAD_OUTPUT_MAX_LEN);
    if (res) {
        NP_LOG(AL_ERROR, "Failed to reload FRR config, mode: %s, force reload: %s force_generate: %d error:%s",
                    use_override ? "OVERRIDE" : "GENERATED", bgp_state->force_reload_config ? "enabled" : "disabled",
                    force_regenerate, zpath_result_string(res));
        if (buf && IS_NP_DEBUG_BGP_CONFIG()) {
            NP_LOG(AL_INFO, "Error: %s", buf);
        }
    }

    NP_BGP_FREE(buf);
    ZPATH_MUTEX_UNLOCK(&(bgp_state->lock), __FILE__, __LINE__);

    return res;
}

/*
 * Delete a peer from its peer hash table
 * Assuming bgp_state lock held
 */
static int
np_bgp_peer_hash_delete(struct zhash_table      *peer_hash,
                        int64_t                 peer_bgp_config_gid,
                        const char              *peer_instance_type)
{
    struct argo_object *existing_peer_obj = zhash_table_lookup(peer_hash,
                                                               &peer_bgp_config_gid,
                                                               sizeof(peer_bgp_config_gid),
                                                               NULL);

    if (!existing_peer_obj) {
        NP_LOG(AL_INFO, "Peer bgp config gid: %"PRId64" not found in hash", peer_bgp_config_gid);
        return NP_RESULT_NOT_FOUND;
    }

    zhash_table_remove(peer_hash,
                       &(peer_bgp_config_gid),
                       sizeof(peer_bgp_config_gid),
                       NULL);
    free_bgp_peer_object(existing_peer_obj, (void *)peer_instance_type);
    NP_DEBUG_BGP_CONFIG("Successfully deleted BGP peer gkd %"PRId64" from hash", peer_bgp_config_gid);
    return NP_RESULT_NO_ERROR;
}

/*
 * Delete a peer from its peer hash table, must be called on np bgp thread
 * Assuming bgp_state lock held
 * TODO: see if we can optimize this routine a bit. Currently for every update it always delete the object and create a new one
 */
static int
np_bgp_peer_hash_add_or_update(struct zhash_table           *peer_hash,
                               struct np_bgp_peer_config    *peer_config,
                               int64_t                      *peer_config_count)
{
    int updated = 0;

    if (!peer_hash || !peer_config) {
        NP_LOG(AL_NOTICE, "peer_hash or peer_config is null!");
        return updated;
    }

    struct argo_object *existing_peer_obj = zhash_table_lookup(peer_hash,
                                                               &(peer_config->bgp_config_gid),
                                                               sizeof(peer_config->bgp_config_gid),
                                                               NULL);

    if (existing_peer_obj) {
        NP_LOG(AL_INFO, "Found existing peer object: %p, bgp config gid: %"PRId64", updating...",
                            existing_peer_obj, peer_config->bgp_config_gid);
        struct np_bgp_peer_config *existing_peer = existing_peer_obj->base_structure_void;
        if (existing_peer && IS_NP_DEBUG_BGP_CONFIG()) {
            NP_LOG(AL_NOTICE, "Modifying existing peer config");
            np_bgp_config_dump_peer_config(existing_peer);
        }
        zhash_table_remove(peer_hash,
                           &(peer_config->bgp_config_gid),
                           sizeof(peer_config->bgp_config_gid),
                           NULL);
        free_bgp_peer_object(existing_peer_obj, (void *)peer_config->instance_type);
        if (peer_config_count) *peer_config_count -= 1;
    }

    /* add the peer config to peer hash */
    struct argo_object *peer_obj = NULL;
    peer_obj = argo_object_create(np_bgp_peer_config_description, peer_config);
    if (!peer_obj) {
        NP_LOG(AL_ERROR, "Could not create object for peer config gid %"PRId64" instance type: %s",
                            peer_config->bgp_config_gid, peer_config->instance_type);
        return updated;
    }
    if (IS_NP_DEBUG_BGP_CONFIG()) {
        NP_LOG(AL_NOTICE, "Adding peer config");
        np_bgp_config_dump_peer_config(peer_config);
    }
    zhash_table_store(peer_hash, &(peer_config->bgp_config_gid), sizeof(peer_config->bgp_config_gid), 0, peer_obj);
    if (peer_config_count) *peer_config_count += 1;
    NP_DEBUG_BGP_CONFIG("Successfully stored BGP peer with bgp config gid: %"PRId64" in hash", peer_config->bgp_config_gid);
    updated = 1;

    /* stats */
    if (!peer_config->instance_type) return updated;
    if (strcmp(peer_config->instance_type, np_bgp_instance_type_str(np_bgp_instance_gateway)) == 0) {
        __sync_add_and_fetch_8(&mem_alloc_stats.num_gateway_peers_allocated, 1);
    } else if (strcmp(peer_config->instance_type, np_bgp_instance_type_str(np_bgp_instance_connector)) == 0) {
        __sync_add_and_fetch_8(&mem_alloc_stats.num_connector_peers_allocated, 1);
    } else if (strcmp(peer_config->instance_type, np_bgp_instance_type_str(np_bgp_instance_router)) == 0) {
        __sync_add_and_fetch_8(&mem_alloc_stats.num_router_peers_allocated, 1);
    } else {
        NP_LOG(AL_ERROR, "unrecognized bgp instance type: %s", peer_config->instance_type);
    }

    return updated;
}

static int
np_bgp_peer_ip_to_config_gid_map_delete(struct np_bgp_state *bgp_state, void *config)
{
    char hash_key[1024] = {0};
    struct np_bgp_connector_session_config *connector_session_config = NULL;
    struct np_bgp_gateway_session_config *gateway_session_config = NULL;
    struct np_bgp_neighbor_ip_to_gid_mapping *mapping_obj = NULL;
    char bgp_peer_ip_str[ARGO_INET_ADDRSTRLEN] = {0};
    struct argo_inet *neighbor_ip = 0;
    int64_t neighbor_asn = 0;

    if (!bgp_state || !config) {
        NP_LOG(AL_NOTICE, "Invalid argument");
        return NP_RESULT_BAD_ARGUMENT;
    }

    if (bgp_state->instance_type == np_bgp_instance_gateway) {
        gateway_session_config = (struct np_bgp_gateway_session_config *)config;
        neighbor_ip = gateway_session_config->neighbor_ip;
        neighbor_asn = gateway_session_config->neighbor_asn;
    } else if (bgp_state->instance_type == np_bgp_instance_connector) {
        connector_session_config = (struct np_bgp_connector_session_config *)config;

        neighbor_ip = connector_session_config->neighbor_ip;
        neighbor_asn = connector_session_config->neighbor_asn;
    } else {
        return NP_RESULT_BAD_STATE;
    }

    argo_inet_generate(bgp_peer_ip_str, neighbor_ip);

    // hash key format : <neighbor_ip>$<neighbor_asn>
    snprintf(hash_key, sizeof(hash_key), "%s$%"PRId64"", bgp_peer_ip_str, neighbor_asn);

    mapping_obj = zhash_table_lookup(bgp_state->peer_ip_to_config_gid_mapping,
                                    hash_key,
                                    strnlen(hash_key, sizeof(hash_key)),
                                    NULL);

    if (mapping_obj) {
        zhash_table_remove(bgp_state->peer_ip_to_config_gid_mapping,
                        hash_key,
                        strnlen(hash_key, sizeof(hash_key)),
                        NULL);
        bgp_state->peer_ip_to_config_gid_mapping_count--;

        NP_DEBUG_BGP_CONFIG("Successfully removed BGP peer ip to config gid mapping. ip:%s asn:%"PRId64" gid:%"PRId64" from hash, active entries count:%"PRId64"",
                                     bgp_peer_ip_str, neighbor_asn, mapping_obj->bgp_config_gid, bgp_state->peer_ip_to_config_gid_mapping_count);

        NP_BGP_FREE(mapping_obj);
    } else {
        NP_DEBUG_BGP_CONFIG("BGP peer ip to config gid mapping not found for ip:%s asn:%"PRId64"",
                                     bgp_peer_ip_str, neighbor_asn);
    }

    return NP_RESULT_NO_ERROR;
}

static struct np_bgp_neighbor_ip_to_gid_mapping *np_bgp_peer_ip_to_config_gid_lookup(struct np_bgp_state *bgp_state,
                                                     const char *neighbor_ip, int64_t neighbor_asn) {
    char hash_key[1024] = {0};
    struct np_bgp_neighbor_ip_to_gid_mapping *mapping_obj = NULL;

    if (!bgp_state || !neighbor_ip) {
        NP_LOG(AL_NOTICE, "Invalid argument neighbor ip or bgp_state");
        return NULL;
    }
    // hash key format : <neighbor_ip>$<neighbor_asn>
    snprintf(hash_key, sizeof(hash_key), "%s$%"PRId64"", neighbor_ip, neighbor_asn);

    mapping_obj = zhash_table_lookup(bgp_state->peer_ip_to_config_gid_mapping,
                                    hash_key,
                                    strnlen(hash_key, sizeof(hash_key)),
                                    NULL);
    if (!mapping_obj) {
        NP_LOG(AL_ERROR, "BGP peer ip to config gid mapping doesn't exist for neighbor ip:%s asn:%"PRId64"", neighbor_ip, neighbor_asn);
    } else {
        NP_DEBUG_BGP_CONFIG("BGP peer ip to config gid lookup success for ip:%s asn:%"PRId64", gid:%"PRId64" type:%d",
                                     neighbor_ip, neighbor_asn, mapping_obj->bgp_config_gid, mapping_obj->inst_type);
    }

    return mapping_obj;
}

int np_bgp_peer_ip_to_instance_gid_lookup(struct np_bgp_state *bgp_state, const char *bgp_peer_ip_str,
                                 int64_t bgp_peer_asn, int64_t *peer_inst_gid, int *peer_inst_type) {
    int res = 0;
    struct np_bgp_neighbor_ip_to_gid_mapping *mapping_obj = NULL;

    if (!bgp_state || !bgp_peer_ip_str || !peer_inst_gid || !peer_inst_type) {
        NP_LOG(AL_ERROR, "Invalid argument for ip to instance gid lookup");
        return NP_RESULT_BAD_ARGUMENT;
    }

    res = NP_RESULT_NOT_FOUND;
    // Get config gid of peer from np_bgp_gateway/connector/router table
    mapping_obj = np_bgp_peer_ip_to_config_gid_lookup(bgp_state, bgp_peer_ip_str, bgp_peer_asn);

    if (mapping_obj) {
        res = NP_RESULT_NO_ERROR;
        if (mapping_obj->inst_type == np_bgp_instance_gateway) {
            struct np_bgp_gateways_config *gw_config = NULL;
            np_bgp_gateways_config_get_by_gid_immediate(mapping_obj->bgp_config_gid, &gw_config);
            if (gw_config) {
                *peer_inst_gid = gw_config->gateway_gid;
                *peer_inst_type = np_bgp_instance_gateway;
            }
        } else if (mapping_obj->inst_type == np_bgp_instance_connector) {
            struct np_bgp_connectors_config *conn_config = NULL;
            np_bgp_connectors_config_get_by_gid_immediate(mapping_obj->bgp_config_gid, &conn_config);
            if (conn_config) {
                *peer_inst_gid = conn_config->connector_gid;
                *peer_inst_type = np_bgp_instance_connector;
            }
        } else if (mapping_obj->inst_type == np_bgp_instance_router) {
            // Return external router config gid
            *peer_inst_gid = mapping_obj->bgp_config_gid;
            *peer_inst_type = np_bgp_instance_router;
        } else {
            res = NP_RESULT_BAD_STATE;
        }
    }

    return res;
}

static int get_neighbor_info_from_config(struct np_bgp_state *bgp_state, void *config,
                            int64_t *neighbor_config_gid, struct argo_inet **neighbor_ip,
                            int64_t *neighbor_asn, enum np_bgp_instance_type *neighbor_type) {

    struct np_bgp_connector_session_config *connector_session_config = NULL;
    struct np_bgp_gateway_session_config *gateway_session_config = NULL;

    if (!bgp_state || !config) {
        return NP_RESULT_BAD_ARGUMENT;
    }

    if (bgp_state->instance_type == np_bgp_instance_gateway) {
        gateway_session_config = (struct np_bgp_gateway_session_config *)config;
        *neighbor_config_gid = gateway_session_config->neighbor_config_gid;
        *neighbor_ip = gateway_session_config->neighbor_ip;
        *neighbor_asn = gateway_session_config->neighbor_asn;
        *neighbor_type = np_bgp_instance_connector;
    } else if (bgp_state->instance_type == np_bgp_instance_connector) {
        connector_session_config = (struct np_bgp_connector_session_config *)config;
        *neighbor_config_gid = connector_session_config->neighbor_config_gid;
        *neighbor_ip = connector_session_config->neighbor_ip;
        *neighbor_asn = connector_session_config->neighbor_asn;
        if (strcmp(connector_session_config->neighbor_instance_type, np_bgp_instance_type_str(np_bgp_instance_gateway)) == 0) {
            *neighbor_type = np_bgp_instance_gateway;
        } else if (strcmp(connector_session_config->neighbor_instance_type, np_bgp_instance_type_str(np_bgp_instance_router)) == 0) {
            *neighbor_type = np_bgp_instance_router;
        } else {
            // Invalid Type
            return NP_RESULT_BAD_STATE;
        }
    } else {
        return NP_RESULT_BAD_STATE;
    }

    return NP_RESULT_NO_ERROR;
}

static int
np_bgp_peer_ip_to_config_gid_map_add_or_update(struct np_bgp_state *bgp_state, void *config) {
    if (!bgp_state || !config) {
        NP_LOG(AL_ERROR, "Invalid argument: bgp_state or config is NULL");
        return NP_RESULT_BAD_ARGUMENT;
    }

    char hash_key[1024] = {0};
    char bgp_peer_ip_str[ARGO_INET_ADDRSTRLEN] = {0};
    struct np_bgp_neighbor_ip_to_gid_mapping *mapping_obj = NULL;
    int64_t neighbor_config_gid = 0;
    struct argo_inet *neighbor_ip = NULL;
    enum np_bgp_instance_type neighbor_type = 0;
    int64_t neighbor_asn = 0;

    // Extract neighbor info from the provided config
    int res = get_neighbor_info_from_config(bgp_state, config, &neighbor_config_gid, &neighbor_ip, &neighbor_asn, &neighbor_type);
    if (res) {
        NP_LOG(AL_ERROR, "Failed to retrieve neighbor info from config. Error: %s", zpath_result_string(res));
        return res;
    }

    // Generate peer IP string and construct hash key (<neighbor_ip>$<neighbor_asn>)
    argo_inet_generate(bgp_peer_ip_str, neighbor_ip);
    snprintf(hash_key, sizeof(hash_key), "%s$%"PRId64"", bgp_peer_ip_str, neighbor_asn);

    // Lookup existing mapping object in the hash table
    mapping_obj = zhash_table_lookup(
        bgp_state->peer_ip_to_config_gid_mapping,
        hash_key, strnlen(hash_key, sizeof(hash_key)),
        NULL
    );

    if (mapping_obj) {
        // Update existing mapping object
        NP_DEBUG_BGP_CONFIG(
            "Updating existing peer IP-to-GID mapping: %p (IP: %s, ASN: %"PRId64", GID: %" PRId64 ")",
            mapping_obj, bgp_peer_ip_str, neighbor_asn, neighbor_config_gid
        );
        mapping_obj->bgp_config_gid = neighbor_config_gid;
        mapping_obj->inst_type = neighbor_type;
        mapping_obj->neighbor_asn = neighbor_asn;
    } else {
        // Create new mapping object and store it in the hash table
        mapping_obj = NP_BGP_CALLOC(sizeof(struct np_bgp_neighbor_ip_to_gid_mapping));
        if (!mapping_obj) {
            NP_LOG(AL_ERROR, "Memory allocation failed for peer IP-to-GID mapping");
            return NP_RESULT_NO_MEMORY;
        }

        mapping_obj->bgp_config_gid = neighbor_config_gid;
        mapping_obj->inst_type = neighbor_type;
        mapping_obj->neighbor_asn = neighbor_asn;

        zhash_table_store(
            bgp_state->peer_ip_to_config_gid_mapping,
            hash_key, strnlen(hash_key, sizeof(hash_key)),
            0, mapping_obj
        );

        bgp_state->peer_ip_to_config_gid_mapping_count++;
        NP_DEBUG_BGP_CONFIG(
            "Added new peer IP-to-GID mapping (IP: %s, ASN: %"PRId64", GID: %" PRId64
            "). Active mapping count: %" PRId64,
            bgp_peer_ip_str, neighbor_asn, neighbor_config_gid,
            bgp_state->peer_ip_to_config_gid_mapping_count
        );
    }

    return NP_RESULT_NO_ERROR;
}

/*
 * Add subnet to hash, ignore duplicates
 * Caller must ensure this is called on NP BGP thread
 */
int
np_bgp_add_subnet_config(struct np_bgp_state *bgp_state, const struct argo_inet *inet, int is_lan_subnet)
{
    if (!bgp_state || bgp_state->deletion_in_progress) return NP_RESULT_NO_ERROR;

    if (!inet) {
        NP_LOG(AL_ERROR, "%s subnet is NULL when storing into BGP state!", is_lan_subnet ? "LAN" : "CLIENT");
        return NP_RESULT_BAD_DATA;
    }

    char inet_str[ARGO_INET_ADDRSTRLEN] = {0};
    argo_inet_generate(inet_str, inet);
    ZPATH_MUTEX_LOCK(&(bgp_state->lock), __FILE__, __LINE__);
    if (is_lan_subnet) {
        if (zhash_table_lookup(bgp_state->lan_subnets, &(inet_str[0]), strnlen(inet_str, sizeof(inet_str)), NULL)) {
            /* already found in existing hash, ignoring */
            ZPATH_MUTEX_UNLOCK(&(bgp_state->lock), __FILE__, __LINE__);
            return NP_RESULT_NO_ERROR;
        }
        struct argo_inet *inet_copy = NP_BGP_CALLOC(sizeof(struct argo_inet));
        if (!inet_copy) {
            NP_LOG(AL_CRITICAL, "No memory");
            return NP_RESULT_NO_MEMORY;
        }
        *inet_copy = *inet;
        zhash_table_store(bgp_state->lan_subnets, &(inet_str[0]), strnlen(inet_str, sizeof(inet_str)), 0, inet_copy);
        bgp_state->lan_subnets_count++;
        __sync_add_and_fetch_8(&mem_alloc_stats.num_lan_subnets_allocated, 1);
        NP_LOG(AL_NOTICE, "Successfully stored %s into hash", inet_str);
    } else {
        if (zhash_table_lookup(bgp_state->client_subnets, &(inet_str[0]), strnlen(inet_str, sizeof(inet_str)), NULL)) {
            /* already found in existing hash, ignoring */
            ZPATH_MUTEX_UNLOCK(&(bgp_state->lock), __FILE__, __LINE__);
            return NP_RESULT_NO_ERROR;
        }
        struct argo_inet *inet_copy = NP_BGP_CALLOC(sizeof(struct argo_inet));
        if (!inet_copy) {
            NP_LOG(AL_CRITICAL, "No memory");
            return NP_RESULT_NO_MEMORY;
        }
        *inet_copy = *inet;
        zhash_table_store(bgp_state->client_subnets, &(inet_str[0]), strnlen(inet_str, sizeof(inet_str)), 0, inet_copy);
        bgp_state->client_subnets_count++;
        __sync_add_and_fetch_8(&mem_alloc_stats.num_client_subnets_allocated, 1);
        NP_LOG(AL_NOTICE, "Successfully stored %s into hash", inet_str);
    }
    ZPATH_MUTEX_UNLOCK(&(bgp_state->lock), __FILE__, __LINE__);

    schedule_frr_timer(bgp_state, 0);
    return NP_RESULT_NO_ERROR;
}

/*
 * Delete subnet from hash, ignore if not found
 * Caller must ensure this is called on NP BGP thread
 */
int
np_bgp_delete_subnet_config(struct np_bgp_state *bgp_state, const struct argo_inet *inet, int is_lan_subnet)
{
    if (!bgp_state || bgp_state->deletion_in_progress) return NP_RESULT_NO_ERROR;

    if (!inet) {
        NP_LOG(AL_ERROR, "%s subnet is NULL when deleting from BGP state!", is_lan_subnet ? "LAN" : "CLIENT");
        return NP_RESULT_BAD_DATA;
    }

    char inet_str[ARGO_INET_ADDRSTRLEN] = {0};
    argo_inet_generate(inet_str, inet);
    ZPATH_MUTEX_LOCK(&(bgp_state->lock), __FILE__, __LINE__);
    if (is_lan_subnet) {
        struct argo_object *existing_inet = zhash_table_lookup(bgp_state->lan_subnets,
                                                               &(inet_str[0]),
                                                               strnlen(inet_str, sizeof(inet_str)),
                                                               NULL);

        if (!existing_inet) {
            /* subnet not found in hash, ignoring */
            ZPATH_MUTEX_UNLOCK(&(bgp_state->lock), __FILE__, __LINE__);
            return NP_RESULT_NO_ERROR;
        }
        zhash_table_remove(bgp_state->lan_subnets,
                           inet_str,
                           strnlen(inet_str, sizeof(inet_str)),
                           NULL);
        free_lan_subnet(existing_inet, NULL);
        bgp_state->lan_subnets_count--;
    } else {
        struct argo_object *existing_inet = zhash_table_lookup(bgp_state->client_subnets,
                                                               &(inet_str[0]),
                                                               strnlen(inet_str, sizeof(inet_str)),
                                                               NULL);

        if (!existing_inet) {
            /* subnet not found in hash, ignoring */
            ZPATH_MUTEX_UNLOCK(&(bgp_state->lock), __FILE__, __LINE__);
            return NP_RESULT_NO_ERROR;
        }
        zhash_table_remove(bgp_state->client_subnets,
                           &(inet_str[0]),
                           strnlen(inet_str, sizeof(inet_str)),
                           NULL);
        free_client_subnet(existing_inet, NULL);
        bgp_state->client_subnets_count--;
    }

    ZPATH_MUTEX_UNLOCK(&(bgp_state->lock), __FILE__, __LINE__);

    schedule_frr_timer(bgp_state, 0);
    return NP_RESULT_NO_ERROR;
}

/* Caller must ensure this is called on NP BGP thread */
int
np_bgp_update_instance_config(struct np_bgp_state       *bgp_state,
                              int64_t                   bgp_config_gid,
                              int64_t                   asn,
                              struct argo_inet          *router_id_inet,
                              const char                *config_mode_str,
                              const char                *override_config,
                              int                       force_reload_config)
{
    int updated = 0;
    int override_config_changed = 0;

    if (!bgp_state || bgp_state->deletion_in_progress) return NP_RESULT_NO_ERROR;

    ZPATH_MUTEX_LOCK(&(bgp_state->lock), __FILE__, __LINE__);
    bgp_state->bgp_config_gid = bgp_config_gid;

    /* Fill in config generation mode & override config */
    if (config_mode_str && (strcmp(config_mode_str, "OVERRIDE") == 0)) {
        if (bgp_state->config_mode != np_bgp_config_mode_override) {
            override_config_changed = 1;
            updated = 1;
        }
        bgp_state->config_mode = np_bgp_config_mode_override;
        np_bgp_frr_set_cfg_mode(np_bgp_config_mode_override);
        if (override_config) {
            if (!bgp_state->override_config || (strcmp(bgp_state->override_config, override_config) != 0)) {
                override_config_changed = 1;
                updated = 1;
            }
            if (bgp_state->override_config) NP_BGP_FREE(bgp_state->override_config);
            bgp_state->override_config = NP_BGP_STRDUP(override_config, strlen(override_config));
        } else {
            NP_LOG(AL_ERROR, "bgp_config_gid: %"PRId64" override config mode but no override config?", bgp_config_gid);
            ZPATH_MUTEX_UNLOCK(&(bgp_state->lock), __FILE__, __LINE__);
            return NP_RESULT_ERR;
        }
    } else {
        /* Default mode: GENERATED */
        if (bgp_state->config_mode != np_bgp_config_mode_generated) {
            override_config_changed = 1;
            updated = 1;
        }
        bgp_state->config_mode = np_bgp_config_mode_generated;
        np_bgp_frr_set_cfg_mode(np_bgp_config_mode_generated);
    }

    /* Fill in router id & ip
     * For initial phase, connector/gateway instance BGP config router_id is the same as its IP (classE) */
    if (router_id_inet) {
        if (!argo_inet_is_same(&(bgp_state->ip), router_id_inet)) {
            bgp_state->ip = *router_id_inet;
            char router_id_str[ARGO_INET_ADDRSTRLEN];
            argo_inet_generate(router_id_str, router_id_inet);
            if (bgp_state->router_id) NP_BGP_FREE(bgp_state->router_id);
            bgp_state->router_id = NP_BGP_STRDUP(router_id_str, strnlen(router_id_str, ARGO_INET_ADDRSTRLEN));
            updated = 1;
        }
    } else {
        NP_LOG(AL_ERROR, "bgp_config_gid: %"PRId64" router_id is NULL", bgp_config_gid);
        ZPATH_MUTEX_UNLOCK(&(bgp_state->lock), __FILE__, __LINE__);
        return NP_RESULT_ERR;
    }

    /* update asn, force_reload_config */
    if (bgp_state->asn != asn) updated = 1;
    bgp_state->asn = asn;
    /* don't need to trigger instant update if force_reload_config changes */
    bgp_state->force_reload_config = force_reload_config;
    np_bgp_frr_set_cfg_reload_option(force_reload_config);

    ZPATH_MUTEX_UNLOCK(&(bgp_state->lock), __FILE__, __LINE__);

    if (updated) {
        /* schedule frr config instantiation */
        schedule_frr_timer(bgp_state, override_config_changed);
    }

    return NP_RESULT_NO_ERROR;
}

/*
 * Update a peer's BGP session config in bgp_state, called from gateway
 * Caller must ensure this is called on NP BGP thread
 */
int
np_bgp_gateway_add_or_update_peer_config(struct np_bgp_state *bgp_state, void *config, const char *interface)
{
    struct np_bgp_gateway_session_config *session_config = (struct np_bgp_gateway_session_config *)config;
    if (!bgp_state || bgp_state->deletion_in_progress) return NP_RESULT_NO_ERROR;

    ZPATH_MUTEX_LOCK(&(bgp_state->lock), __FILE__, __LINE__);
    struct np_bgp_peer_config peer_config = {0};

    peer_config.asn = session_config->neighbor_asn;
    peer_config.bgp_config_gid = session_config->neighbor_config_gid;

    peer_config.instance_type = np_bgp_instance_type_str(np_bgp_instance_connector);

    if (session_config->neighbor_ip) {
        peer_config.ip = *(session_config->neighbor_ip);
    } else {
        NP_LOG(AL_ERROR, "Empty neighbor_ip when configuring peer, session config gid: %"PRId64", neighbor_config_gid: %"PRId64"",
                            session_config->gid, session_config->neighbor_config_gid);
        ZPATH_MUTEX_UNLOCK(&(bgp_state->lock), __FILE__, __LINE__);
        return NP_RESULT_BAD_DATA;
    }

    if (session_config->afi_safi && (session_config->afi_safi_count > 0)) {
        for (int i = 0; i < session_config->afi_safi_count; i++) {
            if (i >= NP_BGP_MAX_AFI_SAFI) {
                NP_LOG(AL_ERROR, "Exceeded maximum AFI_SAFI count when configuring peer, session config gid: %"PRId64", neighbor_config_gid: %"PRId64"",
                            session_config->gid, session_config->neighbor_config_gid);
                break;
            }
            if (session_config->afi_safi[i] && (strcmp(session_config->afi_safi[i], NP_BGP_AFI_SAFI_IPV4_UNICAST))) {
                peer_config.afi_safi[peer_config.afi_safi_count++] = np_bgp_afi_safi_code_ipv4_unicast;
            } else if (session_config->afi_safi[i] && (strcmp(session_config->afi_safi[i], NP_BGP_AFI_SAFI_IPV4_MULTICAST))) {
                peer_config.afi_safi[peer_config.afi_safi_count++] = np_bgp_afi_safi_code_ipv4_multicast;
            }  else if (session_config->afi_safi[i] && (strcmp(session_config->afi_safi[i], NP_BGP_AFI_SAFI_IPV6_UNICAST))) {
                peer_config.afi_safi[peer_config.afi_safi_count++] = np_bgp_afi_safi_code_ipv6_unicast;
            }  else if (session_config->afi_safi[i] && (strcmp(session_config->afi_safi[i], NP_BGP_AFI_SAFI_IPV6_MULTICAST))) {
                peer_config.afi_safi[peer_config.afi_safi_count++] = np_bgp_afi_safi_code_ipv6_multicast;
            } else {
                NP_LOG(AL_ERROR, "Invalid AFI_SAFI value %s when configuring peer, session config gid: %"PRId64", neighbor_config_gid: %"PRId64"",
                           session_config->afi_safi[i] ? session_config->afi_safi[i] : "null", session_config->gid, session_config->neighbor_config_gid);
                continue;;
            }
        }
    } else {
        NP_LOG(AL_ERROR, "Empty afi_safi when configuring peer, session config gid: %"PRId64", neighbor_config_gid: %"PRId64"",
                            session_config->gid, session_config->neighbor_config_gid);
        ZPATH_MUTEX_UNLOCK(&(bgp_state->lock), __FILE__, __LINE__);
        return NP_RESULT_BAD_DATA;
    }

    /* BGP timers config */
    peer_config.bgp_keepalive_interval = session_config->bgp_keepalive_interval;
    peer_config.bgp_holdtime_interval = session_config->bgp_holdtime_interval;

    /* BFD timers config */
    peer_config.use_bfd_timers = session_config->use_bfd_timers;
    peer_config.bfd_tx_interval = session_config->bfd_tx_interval;
    peer_config.bfd_rx_interval = session_config->bfd_rx_interval;
    peer_config.bfd_detect_multiplier = session_config->bfd_detect_multiplier;

    //set peer_config.interface to the dedicated wireguard interface name
    peer_config.interface = interface;

    if (IS_NP_DEBUG_BGP_CONFIG()) {
        char jsonout[1000];
        if (ARGO_RESULT_NO_ERROR == argo_structure_dump(np_bgp_peer_config_description,
                                                        &peer_config, jsonout, sizeof(jsonout), NULL, 1)) {
            NP_LOG(AL_INFO, "%s", jsonout);
        }
    }

    int updated = np_bgp_peer_hash_add_or_update(bgp_state->peer_connectors, &peer_config, &(bgp_state->peer_connectors_count));

    np_bgp_peer_ip_to_config_gid_map_add_or_update(bgp_state, session_config);

    ZPATH_MUTEX_UNLOCK(&(bgp_state->lock), __FILE__, __LINE__);

    if (updated) {
        schedule_frr_timer(bgp_state, 0);
    }
    return NP_RESULT_NO_ERROR;
}

/*
 * Delete a peer's BGP session config in bgp_state, called from gateway
 * Caller must ensure this is called on NP BGP thread
 */
int np_bgp_gateway_delete_peer_config(struct np_bgp_state *bgp_state, void *config)
{
    struct np_bgp_gateway_session_config *session_config = (struct np_bgp_gateway_session_config *)config;
    int updated = 0;

    if (!bgp_state || bgp_state->deletion_in_progress) return NP_RESULT_NO_ERROR;

    ZPATH_MUTEX_LOCK(&(bgp_state->lock), __FILE__, __LINE__);
    if (bgp_state->peer_connectors) {
        if (np_bgp_peer_hash_delete(bgp_state->peer_connectors,
                                    session_config->neighbor_config_gid,
                                    np_bgp_instance_type_str(np_bgp_instance_connector)) == NP_RESULT_NO_ERROR) {
            bgp_state->peer_connectors_count--;
            updated = 1;
        }
    }

    np_bgp_peer_ip_to_config_gid_map_delete(bgp_state, session_config);

    ZPATH_MUTEX_UNLOCK(&(bgp_state->lock), __FILE__, __LINE__);

    if (updated) {
        schedule_frr_timer(bgp_state, 0);
    }

    return NP_RESULT_NO_ERROR;
}

/*
 * Update a peer's BGP session config in bgp_state, called from connector
 * Caller must ensure this is called on NP BGP thread
 */
int
np_bgp_connector_add_or_update_peer_config(struct np_bgp_state *bgp_state, void *config)
{
    struct np_bgp_connector_session_config *session_config = (struct np_bgp_connector_session_config *)config;
    if (!bgp_state || bgp_state->deletion_in_progress) return NP_RESULT_NO_ERROR;

    ZPATH_MUTEX_LOCK(&(bgp_state->lock), __FILE__, __LINE__);
    struct np_bgp_peer_config peer_config = {0};

    peer_config.asn = session_config->neighbor_asn;
    peer_config.bgp_config_gid = session_config->neighbor_config_gid;

    if (session_config->neighbor_ip) {
        peer_config.ip = *(session_config->neighbor_ip);
    } else {
        NP_LOG(AL_ERROR, "Empty neighbor_ip when configuring peer, session config gid: %"PRId64", neighbor_config_gid: %"PRId64"",
                            session_config->gid, session_config->neighbor_config_gid);
        ZPATH_MUTEX_UNLOCK(&(bgp_state->lock), __FILE__, __LINE__);
        return NP_RESULT_BAD_DATA;
    }

    if (session_config->neighbor_instance_type) {
        peer_config.instance_type = session_config->neighbor_instance_type;
    } else {
        NP_LOG(AL_ERROR, "Empty instance_type when configuring peer, session config gid: %"PRId64", neighbor_config_gid: %"PRId64"",
                            session_config->gid, session_config->neighbor_config_gid);
        ZPATH_MUTEX_UNLOCK(&(bgp_state->lock), __FILE__, __LINE__);
        return NP_RESULT_BAD_DATA;
    }

    if (session_config->afi_safi && (session_config->afi_safi_count > 0)) {
        for (int i = 0; i < session_config->afi_safi_count; i++) {
            if (i >= NP_BGP_MAX_AFI_SAFI) {
                NP_LOG(AL_ERROR, "Exceeded maximum AFI_SAFI count when configuring peer, session config gid: %"PRId64", neighbor_config_gid: %"PRId64"",
                            session_config->gid, session_config->neighbor_config_gid);
                break;
            }
            if (session_config->afi_safi[i] && (strcmp(session_config->afi_safi[i], NP_BGP_AFI_SAFI_IPV4_UNICAST))) {
                peer_config.afi_safi[peer_config.afi_safi_count++] = np_bgp_afi_safi_code_ipv4_unicast;
            } else if (session_config->afi_safi[i] && (strcmp(session_config->afi_safi[i], NP_BGP_AFI_SAFI_IPV4_MULTICAST))) {
                peer_config.afi_safi[peer_config.afi_safi_count++] = np_bgp_afi_safi_code_ipv4_multicast;
            }  else if (session_config->afi_safi[i] && (strcmp(session_config->afi_safi[i], NP_BGP_AFI_SAFI_IPV6_UNICAST))) {
                peer_config.afi_safi[peer_config.afi_safi_count++] = np_bgp_afi_safi_code_ipv6_unicast;
            }  else if (session_config->afi_safi[i] && (strcmp(session_config->afi_safi[i], NP_BGP_AFI_SAFI_IPV6_MULTICAST))) {
                peer_config.afi_safi[peer_config.afi_safi_count++] = np_bgp_afi_safi_code_ipv6_multicast;
            } else {
                NP_LOG(AL_ERROR, "Invalid AFI_SAFI value %s when configuring peer, session config gid: %"PRId64", neighbor_config_gid: %"PRId64"",
                            session_config->afi_safi[i] ? session_config->afi_safi[i] : "null", session_config->gid, session_config->neighbor_config_gid);
                continue;;
            }
        }
    } else {
        NP_LOG(AL_ERROR, "Empty afi_safi when configuring peer, session config gid: %"PRId64", neighbor_config_gid: %"PRId64"",
                            session_config->gid, session_config->neighbor_config_gid);
        ZPATH_MUTEX_UNLOCK(&(bgp_state->lock), __FILE__, __LINE__);
        return NP_RESULT_BAD_DATA;
    }

    /* BGP timers config */
    peer_config.bgp_keepalive_interval = session_config->bgp_keepalive_interval;
    peer_config.bgp_holdtime_interval = session_config->bgp_holdtime_interval;

    /* BFD timers config */
    peer_config.use_bfd_timers = session_config->use_bfd_timers;
    peer_config.bfd_tx_interval = session_config->bfd_tx_interval;
    peer_config.bfd_rx_interval = session_config->bfd_rx_interval;
    peer_config.bfd_detect_multiplier = session_config->bfd_detect_multiplier;

    /* Router specific config */
    peer_config.ebgp_multihop_enabled = session_config->ebgp_multihop_enabled;
    if (session_config->update_source_interface) {
        peer_config.update_source_interface = session_config->update_source_interface;
    }
    if (session_config->md5_hash) {
        peer_config.md5_hash = session_config->md5_hash;
    }

    if (IS_NP_DEBUG_BGP_CONFIG()) {
        char jsonout[1000];
        if (ARGO_RESULT_NO_ERROR == argo_structure_dump(np_bgp_peer_config_description,
                                                        &peer_config, jsonout, sizeof(jsonout), NULL, 1)) {
            NP_LOG(AL_INFO, "%s", jsonout);
        }
    }

    int updated = 0;
    if (session_config->neighbor_instance_type) {
        if (strcmp(session_config->neighbor_instance_type, np_bgp_instance_type_str(np_bgp_instance_gateway)) == 0) {
            //set peer_config.interface to wireguard interface
            peer_config.interface = NP_WG_INTERFACE_NAME;
            updated = np_bgp_peer_hash_add_or_update(bgp_state->peer_gateways, &peer_config, &(bgp_state->peer_gateways_count));
        } else if (strcmp(session_config->neighbor_instance_type, np_bgp_instance_type_str(np_bgp_instance_router)) == 0){
            updated = np_bgp_peer_hash_add_or_update(bgp_state->peer_routers, &peer_config, &(bgp_state->peer_routers_count));
        } else {
            NP_LOG(AL_ERROR, "Invalid neighbor_instance_type: %s, session config gid: %"PRId64", neighbor_config_gid: %"PRId64"",
                                session_config->neighbor_instance_type, session_config->gid, session_config->neighbor_config_gid);
            ZPATH_MUTEX_UNLOCK(&(bgp_state->lock), __FILE__, __LINE__);
            return NP_RESULT_BAD_DATA;
        }
    } else {
        NP_LOG(AL_ERROR, "NULL neighbor_instance_type! session config gid: %"PRId64", neighbor_config_gid: %"PRId64"",
                        session_config->gid, session_config->neighbor_config_gid);
        ZPATH_MUTEX_UNLOCK(&(bgp_state->lock), __FILE__, __LINE__);
        return NP_RESULT_BAD_DATA;
    }

    np_bgp_peer_ip_to_config_gid_map_add_or_update(bgp_state, session_config);

    ZPATH_MUTEX_UNLOCK(&(bgp_state->lock), __FILE__, __LINE__);

    if (updated) {
        schedule_frr_timer(bgp_state, 0);
    }
    return NP_RESULT_NO_ERROR;
}

/*
 * Delete a peer's BGP session config in bgp_state, called from connector
 * Caller must ensure this is called on NP BGP thread
 */
int np_bgp_connector_delete_peer_config(struct np_bgp_state *bgp_state, void *config)
{
    struct np_bgp_connector_session_config *session_config = (struct np_bgp_connector_session_config *)config;
    int updated = 0;

    if (!bgp_state || bgp_state->deletion_in_progress) return NP_RESULT_NO_ERROR;

    ZPATH_MUTEX_LOCK(&(bgp_state->lock), __FILE__, __LINE__);
    if (session_config->neighbor_instance_type) {
        if (bgp_state->peer_gateways &&
            (strcmp(session_config->neighbor_instance_type, np_bgp_instance_type_str(np_bgp_instance_gateway)) == 0)) {
            if (np_bgp_peer_hash_delete(bgp_state->peer_gateways,
                                        session_config->neighbor_config_gid,
                                        session_config->neighbor_instance_type) == NP_RESULT_NO_ERROR) {
                bgp_state->peer_gateways_count--;
                updated = 1;
            }
        } else if (bgp_state->peer_routers &&
                (strcmp(session_config->neighbor_instance_type, np_bgp_instance_type_str(np_bgp_instance_router)) == 0)){
            if (np_bgp_peer_hash_delete(bgp_state->peer_routers,
                                        session_config->neighbor_config_gid,
                                        session_config->neighbor_instance_type) == NP_RESULT_NO_ERROR) {
                bgp_state->peer_routers_count--;
                updated = 1;
            }
        } else {
            NP_LOG(AL_ERROR, "Invalid neighbor_instance_type: %s, session config gid: %"PRId64", neighbor_config_gid: %"PRId64"",
                                session_config->neighbor_instance_type, session_config->gid, session_config->neighbor_config_gid);
            ZPATH_MUTEX_UNLOCK(&(bgp_state->lock), __FILE__, __LINE__);
            return NP_RESULT_BAD_DATA;
        }
    } else {
        NP_LOG(AL_ERROR, "NULL neighbor_instance_type! session config gid: %"PRId64", neighbor_config_gid: %"PRId64"",
                            session_config->gid, session_config->neighbor_config_gid);
        ZPATH_MUTEX_UNLOCK(&(bgp_state->lock), __FILE__, __LINE__);
        return NP_RESULT_BAD_DATA;
    }

    np_bgp_peer_ip_to_config_gid_map_delete(bgp_state, config);

    ZPATH_MUTEX_UNLOCK(&(bgp_state->lock), __FILE__, __LINE__);

    if (updated) {
        schedule_frr_timer(bgp_state, 0);
    }

    return NP_RESULT_NO_ERROR;
}

/* see header */
struct np_bgp_state *
np_bgp_state_create(int64_t customer_gid, enum np_bgp_instance_type instance_type)
{
    struct np_bgp_state *bgp_state = NP_BGP_CALLOC(sizeof(struct np_bgp_state));
    if (!bgp_state) {
        NP_LOG(AL_ERROR, "Failed to allocate memory for np_bgp_state");
        return NULL;
    }

    bgp_state->lock = ZPATH_MUTEX_INIT;
    bgp_state->zevent_base = np_bgp_zevent_base;
    if (!bgp_state->zevent_base) {
        NP_LOG(AL_ERROR, "NP BGP zevent base is null? initialization must have failed!");
        goto error;
    }

    bgp_state->customer_gid = customer_gid;
    bgp_state->peer_ip_to_config_gid_mapping = zhash_table_alloc(&np_bgp_allocator);

    if (instance_type == np_bgp_instance_connector) {
        bgp_state->instance_type = np_bgp_instance_connector;
        bgp_state->peer_routers = zhash_table_alloc(&np_bgp_allocator);
        bgp_state->peer_gateways = zhash_table_alloc(&np_bgp_allocator);
        bgp_state->lan_subnets = zhash_table_alloc(&np_bgp_allocator);
        bgp_state->client_subnets = zhash_table_alloc(&np_bgp_allocator);

        if (!bgp_state->peer_routers || !bgp_state->peer_gateways || !bgp_state->lan_subnets || !bgp_state->client_subnets) {
            NP_LOG(AL_ERROR, "Failed to allocate memory for connector's bgp hash tables");
            goto error;
        }
    } else if (instance_type == np_bgp_instance_gateway) {
        bgp_state->instance_type = np_bgp_instance_gateway;
        /* vrf_name is hardcoded to NP_VRF_NAME on gateway */
        bgp_state->vrf_name = NP_BGP_STRDUP(NP_VRF_NAME, strlen(NP_VRF_NAME));
        bgp_state->wg_interface_name = NP_BGP_STRDUP(NP_WG_INTERFACE_NAME, strlen(NP_WG_INTERFACE_NAME));
        bgp_state->peer_connectors = zhash_table_alloc(&np_bgp_allocator);
        bgp_state->client_subnets = zhash_table_alloc(&np_bgp_allocator);
        if (!bgp_state->vrf_name || !bgp_state->wg_interface_name || !bgp_state->peer_connectors || !bgp_state->client_subnets) {
            NP_LOG(AL_ERROR, "Failed to allocate memory for gateway's bgp hash tables and interface names");
            goto error;
        }
    } else {
        NP_LOG(AL_WARNING, "Unsupported instance_type: %d", instance_type);
        goto error;
    }
    return bgp_state;

error:
    if (instance_type == np_bgp_instance_connector) {
        if (bgp_state->peer_routers) NP_BGP_FREE(bgp_state->peer_routers);
        if (bgp_state->peer_gateways) NP_BGP_FREE(bgp_state->peer_gateways);
        if (bgp_state->lan_subnets) NP_BGP_FREE(bgp_state->lan_subnets);
        if (bgp_state->client_subnets) NP_BGP_FREE(bgp_state->client_subnets);
    } else if (instance_type == np_bgp_instance_gateway) {
        if (bgp_state->vrf_name) NP_BGP_FREE(bgp_state->vrf_name);
        if (bgp_state->wg_interface_name) NP_BGP_FREE(bgp_state->wg_interface_name);
        if (bgp_state->peer_connectors) NP_BGP_FREE(bgp_state->peer_connectors);
        if (bgp_state->client_subnets) NP_BGP_FREE(bgp_state->client_subnets);
    }
    if (bgp_state->peer_ip_to_config_gid_mapping) NP_BGP_FREE(bgp_state->peer_ip_to_config_gid_mapping);
    if (bgp_state) NP_BGP_FREE(bgp_state);
    return NULL;
}

static void
np_bgp_state_destroy_on_np_bgp_thread(struct zevent_base *base __attribute__((unused)),
                                      void               *void_cookie,
                                      int64_t            int_cookie __attribute__((unused)))
{
    struct np_bgp_state *bgp_state = (struct np_bgp_state *)void_cookie;
    if (!bgp_state) return;

    ZPATH_MUTEX_LOCK(&(bgp_state->lock), __FILE__, __LINE__);

    if (bgp_state->frr_timer) {
        /* free the timer param and cancel the timer event */
        struct timer_parameter *timer_param;
        timer_param = (struct timer_parameter *)event_get_callback_arg(bgp_state->frr_timer);
        NP_BGP_FREE(timer_param);
        if (bgp_state->frr_timer) {
            /* event_free should cancel pending timer events */
            event_free(bgp_state->frr_timer);
            bgp_state->frr_timer = NULL;
        }
    }

    bgp_state->zevent_base = NULL; /* do not free the event base */
    if (bgp_state->frr_version) {
        NP_BGP_FREE(bgp_state->frr_version);
        bgp_state->frr_version = NULL;
    }
    if (bgp_state->router_id) {
        NP_BGP_FREE(bgp_state->router_id);
        bgp_state->router_id = NULL;
    }
    if (bgp_state->vrf_name) {
        NP_BGP_FREE(bgp_state->vrf_name);
        bgp_state->vrf_name = NULL;
    }
    if (bgp_state->wg_interface_name) {
        NP_BGP_FREE(bgp_state->wg_interface_name);
        bgp_state->wg_interface_name = NULL;
    }
    if (bgp_state->override_config) {
        NP_BGP_FREE(bgp_state->override_config);
        bgp_state->override_config = NULL;
    }

    if (bgp_state->instance_type == np_bgp_instance_connector) {
        if (bgp_state->peer_gateways) {
            zhash_table_free_and_call(bgp_state->peer_gateways, free_bgp_peer_object, (void *)np_bgp_instance_type_str(np_bgp_instance_gateway));
            bgp_state->peer_gateways = NULL;
            bgp_state->peer_gateways_count = 0;
        }
        if (bgp_state->peer_routers) {
            zhash_table_free_and_call(bgp_state->peer_routers, free_bgp_peer_object, (void *)np_bgp_instance_type_str(np_bgp_instance_router));
            bgp_state->peer_routers = NULL;
            bgp_state->peer_routers_count = 0;
        }
        if (bgp_state->client_subnets) {
            zhash_table_free_and_call(bgp_state->client_subnets, free_client_subnet, NULL);
            bgp_state->client_subnets = NULL;
            bgp_state->client_subnets_count = 0;
        }
        if (bgp_state->lan_subnets) {
            zhash_table_free_and_call(bgp_state->lan_subnets, free_lan_subnet, NULL);
            bgp_state->lan_subnets = NULL;
            bgp_state->lan_subnets_count = 0;
        }

    } else if (bgp_state->instance_type == np_bgp_instance_gateway) {
        if (bgp_state->peer_connectors) {
            zhash_table_free_and_call(bgp_state->peer_connectors, free_bgp_peer_object, (void *)np_bgp_instance_type_str(np_bgp_instance_connector));
            bgp_state->peer_connectors = NULL;
            bgp_state->peer_connectors_count = 0;
        }
        if (bgp_state->client_subnets) {
            zhash_table_free_and_call(bgp_state->client_subnets, free_client_subnet, NULL);
            bgp_state->client_subnets = NULL;
            bgp_state->client_subnets_count = 0;
        }
    } else {
        NP_LOG(AL_WARNING, "Unsupported instance type: %d", bgp_state->instance_type);
    }

    if (bgp_state->peer_ip_to_config_gid_mapping) {
        zhash_table_free_and_call(bgp_state->peer_ip_to_config_gid_mapping, free_peer_ip_to_config_gid_mapping_count, NULL);
        bgp_state->peer_ip_to_config_gid_mapping = NULL;
        bgp_state->peer_ip_to_config_gid_mapping_count = 0;
    }

    ZPATH_MUTEX_UNLOCK(&(bgp_state->lock), __FILE__, __LINE__);

    ZPATH_MUTEX_DESTROY(&(bgp_state->lock), __FILE__, __LINE__);
    NP_BGP_FREE(bgp_state);
}

int
np_bgp_state_destroy(struct np_bgp_state *bgp_state)
{
    if (!bgp_state) return NP_RESULT_NO_ERROR;
    ZPATH_MUTEX_LOCK(&(bgp_state->lock), __FILE__, __LINE__);
    if (bgp_state->deletion_in_progress) {
        NP_LOG(AL_NOTICE, "BGP state is already being freed, skipping np_bgp_state_destroy");
        ZPATH_MUTEX_UNLOCK(&(bgp_state->lock), __FILE__, __LINE__);
        return NP_RESULT_NO_ERROR;
    }
    bgp_state->deletion_in_progress = 1;
    if (0 != zevent_base_call(bgp_state->zevent_base, np_bgp_state_destroy_on_np_bgp_thread, bgp_state, 0)) {
        NP_LOG(AL_ERROR, "Failed to schedule NP BGP state destroy on NP BGP thread");
        ZPATH_MUTEX_UNLOCK(&(bgp_state->lock), __FILE__, __LINE__);
        return NP_RESULT_ERR;
    }
    ZPATH_MUTEX_UNLOCK(&(bgp_state->lock), __FILE__, __LINE__);
    return NP_RESULT_NO_ERROR;
}

static void
np_bgp_set_local_sourcing_on_np_bgp_thread(struct zevent_base *base __attribute__((unused)),
                                           void               *void_cookie,
                                           int64_t            int_cookie)
{
    struct np_bgp_state *bgp_state = (struct np_bgp_state *)void_cookie;
    int64_t advertise_lan_segments_disabled = int_cookie;
    int do_update = 0;

    if (!bgp_state || bgp_state->deletion_in_progress) return;
    ZPATH_MUTEX_LOCK(&(bgp_state->lock), __FILE__, __LINE__);
    do_update = (bgp_state->disable_local_sourcing == advertise_lan_segments_disabled) ? 0 : 1;
    bgp_state->disable_local_sourcing = advertise_lan_segments_disabled;
    ZPATH_MUTEX_UNLOCK(&(bgp_state->lock), __FILE__, __LINE__);
    if (do_update) {
        schedule_frr_timer(bgp_state, 0);
    }
}

/* Called from another thread */
void
np_bgp_set_local_sourcing(struct np_bgp_state *bgp_state, int advertise_lan_segments_disabled)
{
    if (!bgp_state || bgp_state->deletion_in_progress) return;
    if (0 != zevent_base_call(bgp_state->zevent_base, np_bgp_set_local_sourcing_on_np_bgp_thread, bgp_state, advertise_lan_segments_disabled)) {
        NP_LOG(AL_ERROR, "Failed to call np_bgp_set_local_sourcing on NP BGP thread");
    }
    return;
}

/* bgp_state lock must be held if called from another thread */
int
np_bgp_get_config_mode(struct np_bgp_state *bgp_state)
{
    if (!bgp_state || bgp_state->deletion_in_progress) return -1;
    return bgp_state->config_mode;
}

/* Initialize NP BGP config and debug module */
int
np_bgp_init()
{
    int res = NP_RESULT_NO_ERROR;
    static int initialized = 0;

    if (initialized) return NP_RESULT_NO_ERROR;

    /*
     * This thread is created to process the following:
     * - BGP config row callbacks
     * - BGP FRR config rendering
     * - BGP FRR config instantiate and reload
     */
    np_bgp_zevent_base = zevent_handler_create(NP_BGP_THREAD, 16*1024*1024, 60);
    if (np_bgp_zevent_base) {
        zevent_add_to_class(np_bgp_zevent_base, NP_BGP_THREAD_POOL);
    } else {
        NP_LOG(AL_ERROR, "Could not create NP BGP thread event base");
        return NP_RESULT_ERR;
    }

    res = np_bgp_config_init();
    if (res) {
        NP_LOG(AL_ERROR, "Failed to init NP BGP config module, error: %s", zpath_result_string(res));
        return res;
    }

    res = np_bgp_debug_init();
    if (res) {
        NP_LOG(AL_ERROR, "Failed to init NP debug module, error: %s", zpath_result_string(res));
        return res;
    }

    initialized = 1;
    return res;
}
