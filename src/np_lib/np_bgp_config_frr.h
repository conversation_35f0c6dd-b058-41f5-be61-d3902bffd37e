/*
 * np_bgp_config_frr.h. Copyright (C) 2025 Zscaler Inc, All Rights Reserved
 * BGP is currently supported via FRR: https://docs.frrouting.org/en/latest/bgp.html
 */

#ifndef _NP_BGP_CONFIG_FRR_H_
#define _NP_BGP_CONFIG_FRR_H_

#ifdef __cplusplus
extern "C" {
#endif

struct bfd_config {
    char name[10];
    int bfd_detect_multiplier;
    int bfd_rx_interval;
    int bfd_tx_interval;
};

enum bfd_profile_type {
    bfd_profile_fast = 0,
    bfd_profile_medium,
    bfd_profile_slow,
    bfd_profile_total_count,/* MUST BE LAST */
};

enum np_bgp_config_frr_error_code {
    NP_BGP_CONFIG_NO_ERROR,
    NP_BGP_CONFIG_PARSER_ERROR,
    NP_BGP_CONFIG_DATA_ERROR,
    NP_BGP_CONFIG_FILE_ERROR,
    NP_BGP_CONFIG_RENDER_ERROR,
    NP_BGP_CONFIG_ARGUMENT_ERROR,
    NP_BGP_CONFIG_UNKNOWN_ERROR
};

enum np_bgp_config_frr_template_type {
    NP_BGP_CONFIG_TEMPLATE_NONE,
    NP_BGP_CONFIG_TEMPLATE_GATEWAY,
    NP_BGP_CONFIG_TEMPLATE_CONNECTOR
};

int np_bgp_config_frr_generate_from_str(const char *config_json_str,
                                        const char *output_file_path,
                                        enum np_bgp_config_frr_template_type template_type);

int np_bgp_config_frr_generate_from_file(const char* template_file_path,
                                         const char* data_file_path,
                                         const char* output_file_path);

#ifdef __cplusplus
} // extern "C"
#endif

#endif /* _NP_BGP_CONFIG_FRR_H_ */
