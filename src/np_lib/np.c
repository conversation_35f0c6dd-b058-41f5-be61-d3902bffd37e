/*
 * np.c. Copyright (C) 2024-2025 Zscaler, Inc. All Rights Reserved
 */

#include <stdio.h>
#include <unistd.h>

#include "zpath_lib/zpath_debug.h"
#include "zpath_lib/zpath_instance.h"
#include "zpath_lib/zpath_config_override.h"
#include "zpath_lib/zpath_config_override_keys.h"

#include "np_lib/np.h"
#include "np_lib/np_private.h"
#include "np_lib/np_rpc.h"
#include "np_lib/np_frr_utils.h"

#define NETWORK_PRESENCE_INVALID   (-100)
#define NETWORK_PRESENCE_INVALID_1 (-101)
#define NETWORK_PRESENCE_DISABLE 0
#define NETWORK_PRESENCE_ENABLE 1

#define NP_FEATURE_TOGGLE_THREAD "np_feature_toggle_thread"
#define NP_FEATURE_TOGGLE_THREAD_POOL "np_feature_toggle"

struct zpath_allocator np_allocator = ZPATH_ALLOCATOR_INIT("np");
struct zpath_allocator np_bgp_allocator = ZPATH_ALLOCATOR_INIT("np_bgp");

struct argo_log_collection *np_event_log = NULL;

uint64_t np_wally_debug =
    (!NP_DEBUG_CLIENT_SUBNETS_BIT) |
    (!NP_DEBUG_DNS_NS_RECORDS_BIT) |
    (!NP_DEBUG_LAN_SUBNETS_BIT) |
    (!NP_DEBUG_CLIENTS_BIT) |
    (!NP_DEBUG_CONNECTORS_BIT) |
    (!NP_DEBUG_CONNECTOR_GROUPS_BIT) |
    (!NP_DEBUG_GATEWAYS_BIT) |
    (!NP_DEBUG_TENANT_GATEWAYS_BIT) |
    0;

static uint64_t np_wally_catch =
    (!NP_DEBUG_CLIENT_SUBNETS_BIT) |
    (!NP_DEBUG_DNS_NS_RECORDS_BIT) |
    (!NP_DEBUG_LAN_SUBNETS_BIT) |
    (!NP_DEBUG_CLIENTS_BIT) |
    (!NP_DEBUG_CONNECTORS_BIT) |
    (!NP_DEBUG_CONNECTOR_GROUPS_BIT) |
    (!NP_DEBUG_GATEWAYS_BIT) |
    (!NP_DEBUG_TENANT_GATEWAYS_BIT) |
    0;

static const char *np_wally_debug_names[] = NP_WALLY_DEBUG_NAMES;

uint64_t np_frr_debug = 0;
uint64_t np_frr_debug_defaults = 0;

const char *np_frr_debug_log_names[] = NP_FRR_DEBUG_LOG_NAMES;

static int64_t g_instance_id = 0;
static int64_t g_connector_gid = 0;
static int64_t g_connector_grp_gid = 0;

static int np_feature_flag_initialized = 0;

static int64_t g_is_np_hard_disabled = NETWORK_PRESENCE_HARD_DISABLED_DEFAULT;
static int64_t g_is_np_policy_hard_disabled = NETWORK_PRESENCE_ACCESS_POLICY_HARD_DISABLED_DEFAULT;

static np_feature_instance_monitor_flag_cb *np_feature_instance_monitor_cb = NULL;
static np_feature_customer_monitor_flag_cb *np_feature_customer_monitor_cb = NULL;
static np_feature_customer_monitor_flag_cb *np_redundancy_feature_customer_monitor_cb = NULL;

/* for testing only, comment out for production */
/* #define MY_NP_FLAG_TEST */
#ifdef MY_NP_FLAG_TEST
void my_test_np_feature_instance_monitor_cb(int enabled)
{
    NP_LOG(AL_NOTICE, "NP_FEATURE_FLAG: instance_monitor_cb triggered: enable=%d", enabled);
}

void my_test_np_feature_customer_monitor_cb(int64_t customer_gid, int enabled)
{
    NP_LOG(AL_NOTICE, "NP_FEATURE_FLAG: customer_monitor_cb triggered: customer=%"PRId64", enable=%d", customer_gid, enabled);
}
#endif

struct customer_state {
    int64_t customer_gid;
    int64_t is_np_enabled;                   /* monitored feature flag value */
    int64_t prev_enable_status;              /* feature + hard disable values*/
    int64_t np_policy_feature;               /* monitored np policy feature flag value */
    int64_t np_policy_feature_prev_status;   /* feature previous status */
    int64_t np_redundancy_feature;                  /* monitored np redundancy feature flag value */
    int64_t np_redundancy_feature_prev_status;      /* np redundancy feature previous status */
};

static struct zhash_table *customer_states = NULL;
static zpath_mutex_t customer_states_lock;

static int create_np_feature_toggle_event_base()
{
    static struct zevent_base *zbase = NULL;
    int res = NP_RESULT_NO_ERROR;
    if (!zbase) {
        zbase = zevent_handler_create(NP_FEATURE_TOGGLE_THREAD, 16*1024*1024, 30);
        if (zbase) {
            zevent_add_to_class(zbase, NP_FEATURE_TOGGLE_THREAD_POOL);
        } else {
            NP_LOG(AL_ERROR, "Could not create event for np_feature_toggle");
            res = NP_RESULT_ERR;
        }
    }
    return res;
}

inline static int is_feature_flag_needed()
{
    return g_instance_id || (g_connector_gid && g_connector_grp_gid);
}

static int is_customer_np_feature_updated(struct customer_state *state)
{
    int updated = 0;
    int enable_status = (!g_is_np_hard_disabled) && state->is_np_enabled;
    updated = state->prev_enable_status != enable_status;
    if (updated) state->prev_enable_status = enable_status;

    return updated;
}

static int is_customer_np_redundancy_feature_updated(struct customer_state *state)
{
    int updated = 0;
    int enable_status = state->np_redundancy_feature;
    updated = state->np_redundancy_feature_prev_status != enable_status;
    if (updated) state->np_redundancy_feature_prev_status = enable_status;

    return updated;
}

static void np_hard_disabled_monitor_cb(struct zevent_base *event_base __attribute__((unused)),
                                        void *void_cookie __attribute__((unused)),
                                        int64_t hard_disabled_value)
{
    NP_LOG(AL_NOTICE, "NP_FEATURE_FLAG: hard disable flag notification: value=%"PRId64, hard_disabled_value);

    if (np_feature_instance_monitor_cb) {
        np_feature_instance_monitor_cb(hard_disabled_value != NETWORK_PRESENCE_ENABLE);
    }
}

static void np_feature_monitor_cb(struct zevent_base *base,
                                  void               *void_cookie __attribute__((unused)),
                                  int64_t            customer_gid,
                                  void               *extra_cookie1 __attribute__((unused)),
                                  void               *extra_cookie2 __attribute__((unused)),
                                  void               *extra_cookie3 __attribute__((unused)),
                                  int64_t            config_value)
{
    const int enabled = config_value ? 1 : 0;
    struct customer_state *state= zhash_table_lookup(customer_states, &customer_gid, sizeof(customer_gid), NULL);
    if (state && is_customer_np_feature_updated(state) && np_feature_customer_monitor_cb) {
        NP_LOG(AL_NOTICE, "NP_FEATURE_FLAG: Notify customer:%"PRId64" for NP enable change, enabled=%d", state->customer_gid, enabled);
        np_feature_customer_monitor_cb(customer_gid, enabled);
    }
}

static int np_feature_changed_customer_walk(void *cookie __attribute__((unused)), void *object, void *key, size_t key_len)
{
    struct customer_state *state = object;
    if (state && is_customer_np_feature_updated(state) && np_feature_customer_monitor_cb) {
        int enabled = (!g_is_np_hard_disabled) && state->is_np_enabled;
        NP_LOG(AL_NOTICE, "NP_FEATURE_FLAG: Notify customer:%"PRId64" for NP enable change, enabled=%d", state->customer_gid, enabled);
        np_feature_customer_monitor_cb(state->customer_gid, enabled);
    }
    return ZPATH_RESULT_NO_ERROR;
}

static void np_feature_global_monitor_cb(struct zevent_base *event_base,
                                         void *void_cookie __attribute__((unused)),
                                         int64_t int_cookie __attribute__((unused)))
{
    int64_t key = 0;
    if (zhash_table_get_size(customer_states)) {
        zhash_table_walk(customer_states, &key, np_feature_changed_customer_walk, NULL);
    }
}

static void np_feature_flag_changed_cb(const int64_t *config_value, int64_t key)
{
    /* if it is already hard disabled, don't need to check the feature toggling */
    if (!np_is_hard_disabled()) {
        if (key == 0 || key == ZPATH_GLOBAL_CONFIG_OVERRIDE_GID) {
            /* global level */
            /* ET-62154: the toggle of global value trigger the callback multiple times for a single config update */
            /* we only process it if there is really a difference with previous value */
            static int64_t value_0 = NETWORK_PRESENCE_INVALID;
            static int64_t value_1 = NETWORK_PRESENCE_INVALID_1;
            int64_t *val = (key == 0) ? &value_0 : &value_1;
            if (*config_value != *val) {
                *val = *config_value;
                zevent_base_call(zevent_get_for_class(NP_FEATURE_TOGGLE_THREAD_POOL), np_feature_global_monitor_cb, NULL, 0);
            }
        } else if (zhash_table_lookup(customer_states, &key, sizeof(key), NULL)) {
            /* customer level */
            NP_LOG(AL_NOTICE, "NP_FEATURE_FLAG: feature changed for customer:%"PRId64" value=%"PRId64, key, *config_value);
            zevent_base_big_call(zevent_get_for_class(NP_FEATURE_TOGGLE_THREAD_POOL), np_feature_monitor_cb, NULL, key, NULL, NULL, NULL, *config_value);
        } else if (key == g_connector_gid || key == g_connector_grp_gid) {
            NP_LOG(AL_NOTICE, "NP_FEATURE_FLAG: feature changed for connector/grp:%"PRId64" value=%"PRId64, key, *config_value);
            zevent_base_call(zevent_get_for_class(NP_FEATURE_TOGGLE_THREAD_POOL), np_feature_global_monitor_cb, NULL, 0);
        } else {
            /* either invalid, or the instance has not called np_is_feature_enabled() for the customer yet */
            NP_LOG(AL_NOTICE, "NP_FEATURE_FLAG: feature changed for key:%"PRId64" value=%"PRId64, key, *config_value);
        }
    }
}

static void np_redundancy_feature_monitor_cb(struct zevent_base *base,
                                             void               *void_cookie __attribute__((unused)),
                                             int64_t            customer_gid,
                                             void               *extra_cookie1 __attribute__((unused)),
                                             void               *extra_cookie2 __attribute__((unused)),
                                             void               *extra_cookie3 __attribute__((unused)),
                                             int64_t            config_value)
{
    const int enabled = config_value ? 1 : 0;
    struct customer_state *state= zhash_table_lookup(customer_states, &customer_gid, sizeof(customer_gid), NULL);
    if (state && is_customer_np_redundancy_feature_updated(state) && np_redundancy_feature_customer_monitor_cb) {
        NP_LOG(AL_NOTICE, "NP_FEATURE_FLAG: Notify customer:%"PRId64" for NP redundancy feature state change, enabled=%d",
                        state->customer_gid, enabled);
        np_redundancy_feature_customer_monitor_cb(customer_gid, enabled);
    }
}

static int np_redundancy_feature_changed_customer_walk(void     *cookie __attribute__((unused)),
                                                       void     *object,
                                                       void     *key,
                                                       size_t   key_len)
{
    struct customer_state *state = object;
    if (state && is_customer_np_redundancy_feature_updated(state) && np_redundancy_feature_customer_monitor_cb) {
        int enabled = (!g_is_np_hard_disabled) && state->is_np_enabled;
        NP_LOG(AL_NOTICE, "NP_FEATURE_FLAG: Notify customer:%"PRId64" for NP redundancy feature state change, enabled=%d",
                        state->customer_gid, enabled);
        np_redundancy_feature_customer_monitor_cb(state->customer_gid, enabled);
    }
    return ZPATH_RESULT_NO_ERROR;
}

static void np_redundancy_feature_global_monitor_cb(struct zevent_base *event_base,
                                                    void *void_cookie __attribute__((unused)),
                                                    int64_t int_cookie __attribute__((unused)))
{
    int64_t key = 0;
    if (zhash_table_get_size(customer_states)) {
        zhash_table_walk(customer_states, &key, np_redundancy_feature_changed_customer_walk, NULL);
    }
}

static void np_redundancy_feature_flag_changed_cb(const int64_t *config_value, int64_t key)
{
    /* if either np or np redundancy feature is already hard disabled, don't need to check the feature toggling */
    if (!np_is_hard_disabled()) {
        if (key == 0 || key == ZPATH_GLOBAL_CONFIG_OVERRIDE_GID) {
            static int64_t value_0 = NETWORK_PRESENCE_INVALID;
            static int64_t value_1 = NETWORK_PRESENCE_INVALID_1;
            int64_t *val = (key == 0) ? &value_0 : &value_1;
            if (*config_value != *val) {
                NP_LOG(AL_NOTICE, "NP_FEATURE_FLAG: NP redundancy feature changed for global, value=%"PRId64"", *config_value);
                *val = *config_value;
                zevent_base_call(zevent_get_for_class(NP_FEATURE_TOGGLE_THREAD_POOL), np_redundancy_feature_global_monitor_cb, NULL, 0);
            }
        } else if (zhash_table_lookup(customer_states, &key, sizeof(key), NULL)) {
            /* customer level */
            NP_LOG(AL_NOTICE, "NP_FEATURE_FLAG: NP redundancy feature changed for customer:%"PRId64" value=%"PRId64"", key, *config_value);
            zevent_base_big_call(zevent_get_for_class(NP_FEATURE_TOGGLE_THREAD_POOL), np_redundancy_feature_monitor_cb, NULL, key, NULL, NULL, NULL, *config_value);
        } else if (key == g_connector_gid || key == g_connector_grp_gid) {
            NP_LOG(AL_NOTICE, "NP_FEATURE_FLAG: NP redundancy feature changed for connector/grp:%"PRId64" value=%"PRId64"", key, *config_value);
            zevent_base_call(zevent_get_for_class(NP_FEATURE_TOGGLE_THREAD_POOL), np_redundancy_feature_global_monitor_cb, NULL, 0);
        } else {
            /* either invalid, or the instance has not called np_is_redundancy_feature_enabled() for the customer yet */
            NP_LOG(AL_NOTICE, "NP_FEATURE_FLAG: NP redundancy feature changed for key:%"PRId64" value=%"PRId64"", key, *config_value);
        }
    }
}

static void np_hard_disabled_flag_changed_cb(const int64_t *config_value, int64_t key __attribute__((unused)))
{
    /* ET-62154: the toggle of global value trigger the callback multiple time for a single config update */
    /* we only process it if there is really a difference with previous value */
    static int64_t hard_disabled_value = NETWORK_PRESENCE_INVALID;
    if (hard_disabled_value != *config_value) {
        hard_disabled_value = *config_value;
        zevent_base_call(zevent_get_for_class(NP_FEATURE_TOGGLE_THREAD_POOL), np_hard_disabled_monitor_cb, NULL, hard_disabled_value);
        zevent_base_call(zevent_get_for_class(NP_FEATURE_TOGGLE_THREAD_POOL), np_feature_global_monitor_cb, NULL, 0);
    }
}

int is_np_customer_connected(int64_t customer_gid) {
    struct customer_state *state = zhash_table_lookup(customer_states, &customer_gid, sizeof(customer_gid), NULL);
    return state ? 1 : 0;
}

static struct customer_state *get_customer_state(int64_t customer_gid)
{
    int res = NP_RESULT_NO_ERROR;
    struct customer_state *state;
    state = zhash_table_lookup(customer_states, &customer_gid, sizeof(customer_gid), NULL);
    if (!state) {
        ZPATH_MUTEX_LOCK(&customer_states_lock, __FILE__, __LINE__);
        /* Repeat lookup because it could have been added before we got lock */
        state = zhash_table_lookup(customer_states, &customer_gid, sizeof(customer_gid), NULL);
        if (!state) {
            state = NP_CALLOC(sizeof(*state));
            if (state) {
                state->customer_gid = customer_gid;
                state->np_policy_feature_prev_status = -1;
                state->np_redundancy_feature_prev_status = -1;
                if (g_instance_id) {
                    /* non app connector */
                    state->is_np_enabled = zpath_config_override_get_config_int(NETWORK_PRESENCE_FEATURE,
                                                                                &(state->is_np_enabled),
                                                                                NETWORK_PRESENCE_FEATURE_DEFAULT,
                                                                                customer_gid,
                                                                                (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                                                (int64_t)0);

                    zpath_config_override_monitor_int(NETWORK_PRESENCE_FEATURE,
                                                      &(state->is_np_enabled),
                                                      np_feature_flag_changed_cb,
                                                      NETWORK_PRESENCE_FEATURE_DEFAULT,
                                                      customer_gid,
                                                      (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                      (int64_t)0);

                    state->np_policy_feature = zpath_config_override_get_config_int(NETWORK_PRESENCE_ACCESS_POLICY_FEATURE,
                                                                                    &(state->np_policy_feature),
                                                                                    NETWORK_PRESENCE_ACCESS_POLICY_FEATURE_DEFAULT,
                                                                                    customer_gid,
                                                                                    (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                                                    (int64_t)0);

                    zpath_config_override_monitor_int(NETWORK_PRESENCE_ACCESS_POLICY_FEATURE,
                                                      &(state->np_policy_feature),
                                                      NULL,
                                                      NETWORK_PRESENCE_ACCESS_POLICY_FEATURE_DEFAULT,
                                                      customer_gid,
                                                      (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                      (int64_t)0);
                    state->np_redundancy_feature = zpath_config_override_get_config_int(NETWORK_PRESENCE_REDUNDANCY_FEATURE,
                                                                                    &(state->np_redundancy_feature),
                                                                                    NETWORK_PRESENCE_REDUNDANCY_FEATURE_DEFAULT,
                                                                                    customer_gid,
                                                                                    (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                                                    (int64_t)0);

                    zpath_config_override_monitor_int(NETWORK_PRESENCE_REDUNDANCY_FEATURE,
                                                      &(state->np_redundancy_feature),
                                                      np_redundancy_feature_flag_changed_cb,
                                                      NETWORK_PRESENCE_REDUNDANCY_FEATURE_DEFAULT,
                                                      customer_gid,
                                                      (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                      (int64_t)0);

                } else if (g_connector_gid && g_connector_grp_gid) {
                    /* np connector */
                    state->is_np_enabled = zpath_config_override_get_config_int(NETWORK_PRESENCE_FEATURE,
                                                                                &(state->is_np_enabled),
                                                                                NETWORK_PRESENCE_FEATURE_DEFAULT,
                                                                                g_connector_gid,
                                                                                g_connector_grp_gid,
                                                                                customer_gid,
                                                                                (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                                                (int64_t)0);

                    zpath_config_override_monitor_int(NETWORK_PRESENCE_FEATURE,
                                                      &(state->is_np_enabled),
                                                      np_feature_flag_changed_cb,
                                                      NETWORK_PRESENCE_FEATURE_DEFAULT,
                                                      g_connector_gid,
                                                      g_connector_grp_gid,
                                                      customer_gid,
                                                      (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                      (int64_t)0);
                    state->np_redundancy_feature = zpath_config_override_get_config_int(NETWORK_PRESENCE_REDUNDANCY_FEATURE,
                                                                                        &(state->np_redundancy_feature),
                                                                                        NETWORK_PRESENCE_REDUNDANCY_FEATURE_DEFAULT,
                                                                                        g_connector_gid,
                                                                                        g_connector_grp_gid,
                                                                                        customer_gid,
                                                                                        (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                                                        (int64_t)0);

                    zpath_config_override_monitor_int(NETWORK_PRESENCE_REDUNDANCY_FEATURE,
                                                      &(state->np_redundancy_feature),
                                                      np_redundancy_feature_flag_changed_cb,
                                                      NETWORK_PRESENCE_REDUNDANCY_FEATURE_DEFAULT,
                                                      customer_gid,
                                                      (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                      (int64_t)0);
                }

                state->prev_enable_status = state->is_np_enabled && (!g_is_np_hard_disabled);

                res = zhash_table_store(customer_states, &customer_gid, sizeof(customer_gid), 0, state);
                if (res) {
                    NP_LOG(AL_ERROR, "Customer:%"PRId64" State initialization fail to store the state, %s", customer_gid, zpath_result_string(res));
                }
            } else {
                /* This should not happen unless it is out of memeory and will cause crash */
                NP_LOG(AL_CRITICAL, "Customer:%"PRId64" State initialization fail: out of memeory", customer_gid);
            }
            NP_LOG(AL_NOTICE, "Customer:%"PRId64" State initialized successfully", customer_gid);
        }
        ZPATH_MUTEX_UNLOCK(&customer_states_lock, __FILE__, __LINE__);
    }
    return state;
}

int np_is_hard_disabled()
{
    return (g_is_np_hard_disabled != 0);
}

void np_set_all_disabled_config_monitor(np_feature_instance_monitor_flag_cb *callback)
{
    np_feature_instance_monitor_cb = callback;
}

void np_set_feature_config_monitor(np_feature_customer_monitor_flag_cb *callback)
{
    np_feature_customer_monitor_cb = callback;
}

void np_set_redundancy_feature_config_monitor(np_feature_customer_monitor_flag_cb *callback)
{
    np_redundancy_feature_customer_monitor_cb = callback;
}

int np_is_feature_enabled(int64_t customer_gid)
{
    int is_enabled = 0;
    if (is_feature_flag_needed()) {
        if (!np_is_hard_disabled()) {
            struct customer_state *state = get_customer_state(customer_gid);
            is_enabled = (state->is_np_enabled != 0);
        }
    } else {
        /* The component (e.g. np_gateway) should not call this function */
        NP_LOG(AL_ERROR, "Feature flag is not needed for this component");
    }
    return is_enabled;
}

int np_policy_is_hard_disabled()
{
    return (g_is_np_policy_hard_disabled != 0);
}

int np_policy_feature_is_enabled(int64_t customer_gid, int* is_policy_feature_toggle)
{
    struct customer_state *state = get_customer_state(customer_gid);
    int feature_status = !(np_policy_is_hard_disabled()) && state->np_policy_feature;
    if (feature_status != state->np_policy_feature_prev_status) {
        NP_LOG(AL_NOTICE, "NP policy feature is changed and set to %d for the customer %"PRId64, feature_status, customer_gid);
        state->np_policy_feature_prev_status = feature_status;
        if (is_policy_feature_toggle) *is_policy_feature_toggle = 1;
    }

    return feature_status;
}

uint8_t np_is_redundancy_feature_enabled(int64_t customer_gid)
{
    struct customer_state *state = get_customer_state(customer_gid);

    return state->np_redundancy_feature ? 1 : 0;
}

static int np_enable_status(struct zpath_debug_state *request_state,
                            const char **query_values,
                            int query_value_count __attribute__((unused)),
                            void *cookie __attribute__((unused)))
{
    int64_t customer_gid = 0;

    if (query_values[0]) {
        char *end_ptr;
        customer_gid = strtoul(query_values[0], &end_ptr, 0);
        if (*end_ptr != '\0') { /* couldn't completely convert to number */
            ZDP("customer gid is invalid!\n");
            customer_gid = 0;
        }
    } else {
        ZDP("customer gid is required!\n");
    }

    if (customer_gid) {
        struct customer_state *state = zhash_table_lookup(customer_states, &customer_gid, sizeof(customer_gid), NULL);
        if (state) {
            ZDP("Network_Presence feature is %s for customer %"PRId64 "\n", (np_is_feature_enabled(customer_gid) ? "enabled" : "disabled"), customer_gid);
            ZDP("Network_Presence feature is %s\n", np_is_hard_disabled() ? "hard disabled" : "not hard disabled");
            if (query_values[1]) { // check NP redundancy feature status
                ZDP("NP Redundancy feature is %s for customer %"PRId64 "\n",
                        (np_is_redundancy_feature_enabled(customer_gid) ? "enabled" : "disabled"), customer_gid);
            }
        } else {
            ZDP("The customer %"PRId64" has not connected yet\n", customer_gid);
        }
    }

    return NP_RESULT_NO_ERROR;
}

static int np_allocators_init()
{
    static int allocator_initialized = 0;
    if (!allocator_initialized) {
        if (zpath_debug_add_allocator(&np_allocator, "np")) {
            NP_LOG(AL_ERROR, "Could not add np allocator");
            return NP_RESULT_ERR;
        }
        if (zpath_debug_add_allocator(&np_bgp_allocator, "np_bgp")) {
            NP_LOG(AL_ERROR, "Could not add np bgp allocator");
            return NP_RESULT_ERR;
        }
        allocator_initialized = 1;
    }
    return NP_RESULT_NO_ERROR;
}

static int np_init_internal(int64_t instance_gid, int64_t connector_gid, int64_t connector_grp_gid)
{
    static int initialized = 0;
    int res;

    if (initialized) {
        return NP_RESULT_NO_ERROR;
    }

    np_event_log = argo_log_get("event_log");

    res = zpath_debug_add_flag(&np_wally_debug, np_wally_catch, "np_wally", np_wally_debug_names);
    if (res) {
        NP_LOG(AL_ERROR, "Could not initialize NP wally debug flags, res: %s", zpath_result_string(res));
        return res;
    }

    res = zpath_debug_add_flag(&np_frr_debug, np_frr_debug_defaults, "np_frr", np_frr_debug_log_names);
    if (res) {
        NP_LOG(AL_ERROR, "Could not initialize NP FRR debug flags, res: %s", zpath_result_string(res));
    }

    if (instance_gid) {
        g_instance_id = instance_gid;
    } else if (connector_gid && connector_grp_gid){
        g_connector_gid = connector_gid;
        g_connector_grp_gid = connector_grp_gid;
    }

    if (np_allocators_init()) {
        NP_LOG(AL_ERROR, "Could not add np allocators");
        return NP_RESULT_ERR;
    }

    if (is_feature_flag_needed()) {
        if (create_np_feature_toggle_event_base()) {
            return NP_RESULT_ERR;
        }

#ifdef MY_NP_FLAG_TEST
        np_set_all_disabled_config_monitor(my_test_np_feature_instance_monitor_cb);
        np_set_feature_config_monitor(my_test_np_feature_customer_monitor_cb);
#endif

        customer_states = zhash_table_alloc(&np_allocator);
        customer_states_lock = ZPATH_MUTEX_INIT;

        g_is_np_hard_disabled = zpath_config_override_get_config_int(NETWORK_PRESENCE_HARD_DISABLED,
                                                                   &g_is_np_hard_disabled,
                                                                   NETWORK_PRESENCE_HARD_DISABLED_DEFAULT,
                                                                   (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                                   (int64_t)0);

        zpath_config_override_monitor_int(NETWORK_PRESENCE_HARD_DISABLED,
                                          &g_is_np_hard_disabled,
                                          np_hard_disabled_flag_changed_cb,
                                          NETWORK_PRESENCE_HARD_DISABLED_DEFAULT,
                                          (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                          (int64_t)0);

        if (g_instance_id) {
            /* non app connector */
            g_is_np_policy_hard_disabled = zpath_config_override_get_config_int(NETWORK_PRESENCE_ACCESS_POLICY_HARD_DISABLED,
                                                                                &g_is_np_policy_hard_disabled,
                                                                                NETWORK_PRESENCE_ACCESS_POLICY_HARD_DISABLED_DEFAULT,
                                                                                (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                                                (int64_t)0);

            zpath_config_override_monitor_int(NETWORK_PRESENCE_ACCESS_POLICY_HARD_DISABLED,
                                              &g_is_np_policy_hard_disabled,
                                              NULL,
                                              NETWORK_PRESENCE_ACCESS_POLICY_HARD_DISABLED_DEFAULT,
                                              (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                              (int64_t)0);
        }

        zpath_debug_add_safe_read_command("Check np feature enablement for a customer",
                                          "/np/enable_status",
                                          np_enable_status,
                                          NULL,
                                          "customer", "Required. GID of the customer",
                                          "redundancy", "Optional. Check redundancy feature status",
                                          NULL);
    }

    initialized = 1;
    np_feature_flag_initialized = 1;
    return NP_RESULT_NO_ERROR;
}

/* for non-app connector components, */
/* pass instance_gid as 0 if the component don't need NP feature flags */
int np_init(int64_t instance_gid)
{
    return np_init_internal(instance_gid, 0, 0);
}

/* init for app connector */
int np_init_appc(int64_t connector_gid, int64_t connector_grp_gid)
{
    return np_init_internal(0, connector_gid, connector_grp_gid);
}


int np_init_pre_appc()
{
    if (np_allocators_init()) {
        NP_LOG(AL_ERROR, "Could not add np allocators");
        return NP_RESULT_ERR;
    }

    return NP_RESULT_NO_ERROR;
}

int np_is_initialized()
{
    return np_feature_flag_initialized;
}
