/*
 * np_connectors.h. Copyright (C) 2024 Zscaler Inc, All Rights Reserved
 */

#ifndef _NP_CONNECTORS_H_
#define _NP_CONNECTORS_H_

#include "argo/argo.h"

struct np_connectors                           /* _ARGO: object_definition */
{
    /* Standard SQL fields. */
    int64_t gid;                               /* _ARGO: integer, index, key */
    int64_t sequence;                          /* _ARGO: integer, sequence */
    int64_t deleted;                           /* _ARGO: integer, deleted */
    int64_t modified_time;                     /* _ARGO: integer */
    int64_t creation_time;                     /* _ARGO: integer */
    int64_t modifiedby_userid;                 /* _ARGO: integer */

    int64_t customer_gid;                      /* _ARGO: integer, index */
    const char *public_key;                    /* _ARGO: string */
    int64_t public_key_expiry;                 /* _ARGO: integer */
    int16_t state;                             /* _ARGO: integer */
    int64_t connector_group_gid;               /* _ARGO: integer, index */
    int64_t assistant_gid;                     /* _ARGO: integer, index */
    int gateway_listener_port;                 /* _ARGO: integer */
};

/*
 * Must be called after np_init()
 * set single_tenant_wally + tenant_id only if single tenant.
 * NOTE: NOT fully loaded, registration should only happen on one column!
 */
int
np_connectors_table_init(struct wally *single_tenant_wally,
                         int64_t tenant_id,
                         wally_row_callback_f *all_rows_callback,
                         void *all_rows_callback_cookie,
                         int single_tenant_fully_loaded);

/*
 * Get a np connector given a connector ID.
 * Returns in-memory result, or failure.
 */
int
np_connectors_get_by_id_immediate(int64_t connector_id,
                                  struct np_connectors **connector);

/*
 * Get all np connectors for a given customer. This only returns what is in memory.
 */
int
np_connectors_get_by_customer_gid_immediate(int64_t customer_gid,
                                            struct np_connectors **connectors,
                                            size_t *connectors_count);

/*
 * Get all np connectors for a given connector group gid. This only returns what is in memory.
 */
int
np_connectors_get_by_connector_group_gid_immediate(int64_t connector_group_gid,
                                                   struct np_connectors **connectors,
                                                   size_t *connectors_count);

/*
 * Get one np connector for a given assistant gid. This also does registration.
 */
int
np_connectors_get_by_assistant_gid(int64_t assistant_gid,
                                   struct np_connectors **connector,
                                   wally_response_callback_f callback_f,
                                   void *callback_cookie,
                                   int64_t callback_id);

#endif /* _NP_CONNECTORS_H_ */
