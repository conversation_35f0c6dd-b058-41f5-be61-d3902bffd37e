/*
 * np_bgp_config.h. Copyright (C) 2025 Zscaler Inc, All Rights Reserved
 */

#ifndef _NP_BGP_CONFIG_H_
#define _NP_BGP_CONFIG_H_

#include "argo/argo.h"
#include "np_lib/np.h"

extern struct argo_structure_description *np_bgp_peer_config_description;
extern struct argo_structure_description *np_bgp_config_gateway_description;
extern struct argo_structure_description *np_bgp_config_connector_description;

/* NP BGP peer config structure
 * ARGO object type needs to be specified if it's required for template rendering via argo json serialize
 */
struct np_bgp_peer_config {                                         /* _ARGO: object_definition */
    int64_t asn;                                                    /* _ARGO: integer */
    struct argo_inet ip;                                            /* _ARGO: inet */

    /* instance_type: GATEWAY, ROUTER, or NULL (for gateway's peers, which can only be connectors)
     * bgp_config_gid is the gid in the corresponding np_bgp_[gateways|routers]_config table */
    int64_t bgp_config_gid;
    const char *instance_type;

    int bgp_keepalive_interval;                                     /* _ARGO: integer */
    int bgp_holdtime_interval;                                      /* _ARGO: integer */

    int use_bfd_timers;                                             /* _ARGO: integer */
    int bfd_tx_interval;                                            /* _ARGO: integer */
    int bfd_rx_interval;                                            /* _ARGO: integer */
    int bfd_detect_multiplier;                                      /* _ARGO: integer */

    /* AFI-SAFI example: "IPv4-Unicast" */
    int afi_safi[NP_BGP_MAX_AFI_SAFI];                              /* _ARGO: integer */
    int afi_safi_count;                                             /* _ARGO: integer, count: afi_safi */

    /* Router specific configs */
    int ebgp_multihop_enabled;                                      /* _ARGO: integer */
    const char *update_source_interface;                            /* _ARGO: string */
    const char *md5_hash;                                           /* _ARGO: string */

    /* Interface name which we will use to peer with the neighbor
     * use wireguard interface name for connector/gateway peers */
    const char *interface;                                          /* _ARGO: string */
};

struct np_bgp_config_gateway {                                      /* _ARGO: object_definition */
    /* FRR version */
    char *version;                                                  /* _ARGO: string */
    int has_version;                                                /* _ARGO: integer */

    /* Gateway only, default value: NPWG_VRF_NAME */
    char *vrf_name;                                                 /* _ARGO: string */
    /* Gateway only, interface name for NP client peers.
     * Default value: NP_WG_INTERFACE_NAME */
    const char *wg_interface_name;                                  /* _ARGO: string */

    /* Local BGP config */
    char *router_id;                                                /* _ARGO: string */
    struct argo_inet ip;                                            /* _ARGO: inet */
    int64_t asn;                                                    /* _ARGO: integer */

    /* An array of connector peer configs */
    struct argo_object *connectors[NP_BGP_MAX_PEERS];               /* _ARGO: argo_object */
    int connectors_count;                                           /* _ARGO: integer, count: connectors */

    /* CLIENT subnets */
    struct argo_inet *client_subnets[NP_BGP_MAX_SUBNETS];           /* _ARGO: inet */
    int client_subnets_count;                                       /* _ARGO: integer, count: client_subnets */
};

struct np_bgp_config_connector {                                    /* _ARGO: object_definition */
    /* FRR version */
    char *version;                                                  /* _ARGO: string */
    int has_version;                                                /* _ARGO: integer */

    /* Local BGP config */
    char *router_id;                                                /* _ARGO: string */
    struct argo_inet ip;                                            /* _ARGO: inet */
    int64_t asn;                                                    /* _ARGO: integer */

    /* An array of gateway peer configs */
    struct argo_object *gateways[NP_BGP_MAX_PEERS];                 /* _ARGO: argo_object */
    int gateways_count;                                             /* _ARGO: integer, count: gateways */

    /* Customer routers */
    struct argo_object *routers[NP_BGP_MAX_PEERS];                  /* _ARGO: argo_object */
    int routers_count;                                              /* _ARGO: integer, count: routers*/

    /* CLIENT subnets */
    struct argo_inet *client_subnets[NP_BGP_MAX_SUBNETS];           /* _ARGO: inet */
    int client_subnets_count;                                       /* _ARGO: integer, count: client_subnets */

    int disable_local_sourcing;                                     /* _ARGO: integer */

    struct argo_inet *lan_subnets[NP_BGP_MAX_SUBNETS];              /* _ARGO: inet */
    int lan_subnets_count;                                          /* _ARGO: integer, count: lan_subnets */
};

void np_bgp_config_dump_peer_config(const struct np_bgp_peer_config *peer_config);

const char *np_bgp_config_error_string(int result);

int np_bgp_config_generate_gateway_frr_conf(const struct np_bgp_config_gateway *gw_config, const char *output_file_path);

int np_bgp_config_generate_connector_frr_conf(const struct np_bgp_config_connector *conn_config, const char *output_file_path);

int np_bgp_config_stats_init(int64_t customer_gid);

int np_bgp_config_init();

#endif /* _NP_BGP_CONFIG_H_ */
