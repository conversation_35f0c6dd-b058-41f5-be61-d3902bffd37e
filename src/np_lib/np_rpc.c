/*
 * np_rpc.c. Copyright (C) 2024 <PERSON>scaler, Inc. All Rights Reserved.
 */

#include "np_lib/np_rpc.h"
#include "np_lib/np_rpc_compiled.h"

struct argo_structure_description *zpn_np_config_request_description;
struct argo_structure_description *zpn_np_gateway_config_description;
struct argo_structure_description *zpn_np_config_description;
struct argo_structure_description *zpn_np_gateway_select_description;
struct argo_structure_description *zpn_np_gateway_select_ack_description;
struct argo_structure_description *zpn_np_broker_keep_alive_description;
struct argo_structure_description *zpn_np_config_update_description;
struct argo_structure_description *zpn_np_client_app_description;
struct argo_structure_description *zpn_np_client_app_complete_description;
struct argo_structure_description *zpn_np_app_domain_description;
struct argo_structure_description *zpn_np_app_domain_complete_description;

struct argo_structure_description *zpn_np_bgp_peer_stats_description;
struct argo_structure_description *zpn_np_bgp_config_stats_description;
struct argo_structure_description *zpn_np_ip_route_next_hop_description;
struct argo_structure_description *zpn_np_ip_route_description;
struct argo_structure_description *zpn_np_bgp_route_next_hop_description;
struct argo_structure_description *zpn_np_bgp_route_description;
struct argo_structure_description *zpn_np_neighbor_description;
struct argo_structure_description *zpn_np_nw_comp_stats_description;
struct argo_structure_description *zpn_np_route_comp_stats_description;

struct argo_structure_description *zpn_np_cumulative_bgp_peer_stats_description;
struct argo_structure_description *zpn_np_cumulative_bgp_route_stats_description;
struct argo_structure_description *zpn_np_frr_svc_stats_description;
struct argo_structure_description *zpn_np_frr_cfg_summary_stats_description;

static int initialized = 0;


const char *zpn_np_config_status_code_to_string(enum zpn_np_config_status_code status_code)
{
    switch (status_code) {
        case zpn_np_config_success:
            return "success";
        case zpn_np_config_error_bad_request:
            return "error_bad_request";
        case zpn_np_config_error_forbidden:
            return "error_forbidden";
        case zpn_np_config_error_no_gateway_available:
            return "error_no_gateway_available";
        case zpn_np_config_error_no_ip_available:
            return "error_no_ip_available";
        case zpn_np_config_error_service_not_available:
            return "error_service_not_available";
        case zpn_np_config_error_stale_connection:
            return "error_stale_connection";
        case zpn_np_config_error_service_disabled:
            return "error_service_disabled";
        case zpn_np_config_error_not_supported:
            return "error_not_supported";
        case zpn_np_config_error_app_registration_fail:
            return "error_app_registration_fail";
        default:
            return "unknown";
    }
}

int np_rpc_broker_init()
{
    if (initialized) {
        return 0;
    }

    zpn_np_config_request_description =
        argo_register_global_structure(ZPN_NP_CONFIG_REQUEST_HELPER);
    zpn_np_gateway_config_description =
        argo_register_global_structure(ZPN_NP_GATEWAY_CONFIG_HELPER);
    zpn_np_config_description = argo_register_global_structure(ZPN_NP_CONFIG_HELPER);
    zpn_np_gateway_select_description =
        argo_register_global_structure(ZPN_NP_GATEWAY_SELECT_HELPER);
    zpn_np_gateway_select_ack_description =
        argo_register_global_structure(ZPN_NP_GATEWAY_SELECT_ACK_HELPER);
    zpn_np_broker_keep_alive_description =
        argo_register_global_structure(ZPN_NP_BROKER_KEEP_ALIVE_HELPER);
    zpn_np_config_update_description =
        argo_register_global_structure(ZPN_NP_CONFIG_UPDATE_HELPER);
    zpn_np_client_app_description =
        argo_register_global_structure(ZPN_NP_CLIENT_APP_HELPER);
    zpn_np_client_app_complete_description =
        argo_register_global_structure(ZPN_NP_CLIENT_APP_COMPLETE_HELPER);
    zpn_np_app_domain_description =
        argo_register_global_structure(ZPN_NP_APP_DOMAIN_HELPER);
    zpn_np_app_domain_complete_description =
        argo_register_global_structure(ZPN_NP_APP_DOMAIN_COMPLETE_HELPER);

    if (!zpn_np_config_request_description ||
        !zpn_np_gateway_config_description ||
        !zpn_np_config_description ||
        !zpn_np_gateway_select_description ||
        !zpn_np_gateway_select_ack_description ||
        !zpn_np_broker_keep_alive_description ||
        !zpn_np_config_update_description ||
        !zpn_np_client_app_description ||
        !zpn_np_client_app_complete_description ||
        !zpn_np_app_domain_description ||
        !zpn_np_app_domain_complete_description) {
        return -1;
    }

    initialized = 1;
    return 0;
}

int np_rpc_stats_init()
{
    static int initialized = 0;
    if (initialized) {
        return 0;
    }

    zpn_np_bgp_peer_stats_description = argo_register_global_structure(ZPN_NP_BGP_PEER_STATS_HELPER);
    zpn_np_bgp_config_stats_description = argo_register_global_structure(ZPN_NP_BGP_CONFIG_STATS_HELPER);
    zpn_np_ip_route_next_hop_description = argo_register_global_structure(ZPN_NP_IP_ROUTE_NEXT_HOP_HELPER);
    zpn_np_ip_route_description = argo_register_global_structure(ZPN_NP_IP_ROUTE_HELPER);
    zpn_np_bgp_route_next_hop_description = argo_register_global_structure(ZPN_NP_BGP_ROUTE_NEXT_HOP_HELPER);
    zpn_np_bgp_route_description = argo_register_global_structure(ZPN_NP_BGP_ROUTE_HELPER);
    zpn_np_neighbor_description = argo_register_global_structure(ZPN_NP_NEIGHBOR_HELPER);
    zpn_np_nw_comp_stats_description = argo_register_global_structure(ZPN_NP_NW_COMP_STATS_HELPER);
    zpn_np_route_comp_stats_description = argo_register_global_structure(ZPN_NP_ROUTE_COMP_STATS_HELPER);

    zpn_np_cumulative_bgp_peer_stats_description = argo_register_global_structure(ZPN_NP_CUMULATIVE_BGP_PEER_STATS_HELPER);
    zpn_np_cumulative_bgp_route_stats_description = argo_register_global_structure(ZPN_NP_CUMULATIVE_BGP_ROUTE_STATS_HELPER);
    zpn_np_frr_svc_stats_description = argo_register_global_structure(ZPN_NP_FRR_SVC_STATS_HELPER);
    zpn_np_frr_cfg_summary_stats_description = argo_register_global_structure(ZPN_NP_FRR_CFG_SUMMARY_STATS_HELPER);

    if (!zpn_np_bgp_peer_stats_description ||
        !zpn_np_bgp_config_stats_description ||
        !zpn_np_ip_route_next_hop_description ||
        !zpn_np_ip_route_description ||
        !zpn_np_bgp_route_next_hop_description ||
        !zpn_np_bgp_route_description ||
        !zpn_np_neighbor_description ||
        !zpn_np_nw_comp_stats_description ||
        !zpn_np_route_comp_stats_description ||
        !zpn_np_cumulative_bgp_peer_stats_description ||
        !zpn_np_cumulative_bgp_route_stats_description ||
        !zpn_np_frr_svc_stats_description ||
        !zpn_np_frr_cfg_summary_stats_description) {
        return -1;
    }

    initialized = 1;
    return 0;
}
