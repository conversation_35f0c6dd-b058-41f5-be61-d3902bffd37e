/*
 * np_frr_utils.h. Copyright (C) 2025 Zscaler Inc, All Rights Reserved
 */

#ifndef _NP_FRR_UTILS_H_
#define _NP_FRR_UTILS_H_

enum zpn_np_instance_type_e {
    zpn_np_instance_type_unknown = 0,
    zpn_np_instance_type_gateway = 1,
    zpn_np_instance_type_connector = 2
};

enum zpn_np_router_peer_type_e {
    zpn_np_router_peer_type_unknown = 0,
    zpn_np_router_peer_type_gateway = 1,
    zpn_np_router_peer_type_connector = 2,
    zpn_np_router_peer_type_external = 3
};

#define NP_STATS_BGP_SUMMARY_NAME "zpn_np_nw_comp_stats"
#define NP_STATS_BGP_ROUTES_NAME "zpn_np_route_comp_stats"

#define NP_STATS_SYSTYPE_STR_GATEWAY "vse"
#define NP_STATS_SYSTYPE_STR_CONNECTOR "npc"

#define NP_STATS_SYSTYPE_INT_GATEWAY 1
#define NP_STATS_SYSTYPE_INT_CONNECTOR 2

#define INITIAL_BUFFER_SIZE (64 * 1024)                // Initial buffer size for output

#define SECONDS_IN_MINUTE 60
#define SECONDS_IN_HOUR 3600
#define SECONDS_IN_DAY 86400
#define SECONDS_IN_WEEK 604800

// Constants for splitting limits
#define MAX_PEERS_PER_MESSAGE 25
#define MAX_BGP_ROUTES_PER_MESSAGE 12
#define MAX_IP_ROUTES_PER_MESSAGE 12

#define BGP_NETWORK_STATS 1
#define BGP_ROUTE_STATS 2

#define NP_STATS_FRR_MONITOR_S                            (1 * 60)
#define NP_STATS_FRR_ROUTE_STATS_INTERVAL_S               (15 * 60)
#define NP_STATS_FRR_MONITOR_HEARTBEAT_TIMEOUT_S          (NP_STATS_FRR_MONITOR_S * 2)
#define NP_STATS_FRR_MONITOR_THREAD_STACK_SIZE            (16 * 1024 * 1024)
#define NP_STATS_FRR_MONITOR_THREAD_STATS_US              MINUTE_TO_US(5)

#define SUDO_PATH "/usr/bin/sudo"
#define VTYSH SUDO_PATH " vtysh -c"
#define SHOW "show"
#define CLEAR "clear"
#define INTERFACE "interface"
#define IP "ip"
#define BGP "bgp"
#define VRF "vrf"
#define ROUTE "route"
#define NEIGHBORS "neighbors"
#define SUMMARY "summary"
#define RUNNING_CONFIG "running-config"
#define ALL "all"
#define JSON "json"
#define FRR "frr"
#define SYSTEMCTL SUDO_PATH " systemctl"
#define START "start"
#define STOP "stop"
#define RESTART "restart"
#define RELOAD "reload"
#define STATUS "status"
#define VERSION "version"
#define FRR_RELOAD SUDO_PATH " /usr/lib/frr/frr-reload.py"
#define TEST "test"
#define STDOUT "stdout"
#define INTERFACE_MAX_LEN 15
#define JOURNALCTL SUDO_PATH " journalctl"
#define MAX_LOG_LINES 5000
#define DEFAULT_LOG_LINES 1000

#define CONFIG_UNVERIFIED "CONFIG_UNVERIFIED"
#define CONFIG_VERIFYING "CONFIG_VERIFYING"
#define CONFIG_VALID "CONFIG_VALID"
#define CONFIG_ERROR "CONFIG_ERROR"

extern uint64_t np_frr_debug;
#define NP_FRR_DEBUG_BIT                BIT_FLAG_U64(0)

#define NP_FRR_DEBUG_LOG_NAMES {             \
        "np_frr_debug",                      \
        NULL                                 \
        }

#define NP_FRR_DEBUG(format...)      NP_DEBUG_LOG(np_frr_debug & NP_FRR_DEBUG_BIT, ##format)

struct np_stats_frr_monitor {
    pthread_t                               monitor_thread_id;
    struct event_base                       *monitor_thread_base;
    struct zthread_info                     *monitor_hb_thread; /* Heartbeat tickle */
    struct event                            *timer;
    void                                    *config_cookie;
    int64_t                                 monitor_count;
    zpath_mutex_t                           np_frr_stats_lock;
    int                                     initialized;
    int                                     redundancy_feature_enabled;
    char                                    self_router_id[256];
    char                                    frr_cfg_status[256];
    int32_t                                 config_mode;
    int32_t                                 frr_config_reload;
    int64_t                                 self_local_as;
    int64_t                                 customer_gid;
    int64_t                                 inst_gid;
    int64_t                                 nw_stats_update_id;
    int64_t                                 route_stats_update_id;
    int64_t                                 np_frr_stats_monitor_enabled;
    int64_t                                 np_frr_bgp_route_stats_enabled;
    int                                     np_frr_stats_registered;
};

void np_frr_util_set_redundancy_feature_flag_state(int enabled);

int zpn_np_prepare_bgp_get_logs_cmd(int number, int since_mins,
                             int until_mins, char *cmd, int size_cmd);

int zpn_np_prepare_bgp_config_validate_cmd(int config_type, char *cmd, int size_cmd);
int zpn_np_prepare_bgp_running_config_cmd(char *cmd, int size_cmd);
int zpn_np_prepare_clear_ip_bgp_cmd(char *target, char *cmd, int size_cmd);
int zpn_np_prepare_show_ip_bgp_summary_cmd(char *cmd, int size_cmd);
int zpn_np_prepare_show_ip_bgp_neighbors_cmd(char *target, char *cmd, int size_cmd);
int zpn_np_prepare_show_ip_bgp_cmd(char *target, char *cmd, int size_cmd);
int zpn_np_prepare_show_ip_route_cmd(char *target, char *cmd, int size_cmd);
int zpn_np_prepare_show_ip_interface_cmd(char *interface, char *cmd, int size_cmd);
int zpn_np_prepare_stop_bgp_cmd(char *cmd, int size_cmd);
int zpn_np_prepare_start_bgp_cmd(char *cmd, int size_cmd);
int zpn_np_prepare_restart_bgp_cmd(char *cmd, int size_cmd);
int zpn_np_prepare_status_bgp_cmd(char *cmd, int size_cmd);

int np_frr_util_write_buffer_to_file(const char *buffer, size_t buffer_size, const char *file_path);

int np_frr_util_is_file_exist(const char *file_path);
int np_frr_util_file_copy(const char *src_file, const char *dest_file);

int np_frr_util_file_copy_to_buffer(const char *src_file, char *out_buf, size_t buf_size);

int np_frr_test_config_status(const char *config_file_loc, int *is_config_valid);

typedef int (get_bgp_peer_config_info_cb_f(const char *neighbor_ip, int64_t neighbor_asn,
                                                 int64_t *peer_gid, int *peer_type));

void np_frr_release_buf(void *buf);

int np_frr_util_execute_command(const char *command, char **out);

void np_frr_set_instance_type(enum zpn_np_instance_type_e inst_type);

int np_parse_bgp_stats(const char *stats_str, bool is_summary, void *out_stats);

void np_ip_bgp_routes_stats_free(void *stats, bool is_bgp);

int np_parse_ip_bgp_routes_stats(const char *route_str, bool is_bgp, void *out_stats);

void np_bgp_stats_free(void *nw_stats);

void np_frr_cfg_summary_stats_inc_generate_success_cnt(int is_override);
void np_frr_cfg_summary_stats_inc_generate_error_cnt(int is_override);

void **np_split_stats_object_to_messages(int type_flag, void *input, int *num_messages);

int np_stats_frr_monitor_init();

char* np_frr_parse_version();

char* np_frr_parse_status();

char *np_frr_parse_status_from_output(char *out_buf, char *cmd);

int np_frr_start(char *out_buf, size_t out_buf_len);

int np_frr_reload_conf(int use_override, int force_reload, char *out_buf, size_t out_buf_len);

int np_frr_utils_init(enum zpn_np_instance_type_e inst_type, int64_t inst_gid,
                         int64_t customer_id, get_bgp_peer_config_info_cb_f *get_bgp_peer_config_info_cb);

void np_bgp_frr_set_cfg_mode(int32_t cfg_mode);

void np_bgp_frr_set_cfg_status(char *cfg_status);

void np_bgp_frr_set_cfg_reload_option(int32_t force_reload_config);

#endif /* _NP_FRR_UTILS_H_ */
