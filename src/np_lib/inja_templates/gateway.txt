! FRR Config File for NP Gateway
! Generated on {{ datetime }}
{% if has_version %}frr version {{ version }}{% endif %}
frr defaults traditional
allow-reserved-ranges
log syslog informational
!
ip prefix-list NET0 seq 5 permit 0.0.0.0/0 le 32
!
vrf {{ vrf_name }}
{% for i in range(client_subnets_count) %}
 ip route {{ netmask(at(client_subnets, i)) }} {{ wg_interface_name }}
{% endfor %}
{% if connectors_count %}
{% for connector in connectors %}
 ip route {{ netmask(connector.ip) }} {{ connector.interface }}
{% endfor %}
{% endif %}
exit-vrf
!
interface dummy0
 ip address {{ netmask(ip) }}
exit
bfd
{{ bfd_write_profiles() }}
exit
!
router bgp {{ asn }} vrf {{ vrf_name }}
 bgp router-id {{ router_id }}
 bgp log-neighbor-changes
 no bgp network import-check
{% if connectors_count %}
{% for connector in connectors %}
 neighbor CONNECTOR{{ loop.index1 }} peer-group
 neighbor CONNECTOR{{ loop.index1 }} remote-as {{ connector.asn }}
 neighbor CONNECTOR{{ loop.index1 }} ebgp-multihop
 neighbor CONNECTOR{{ loop.index1 }} disable-connected-check
 neighbor CONNECTOR{{ loop.index1 }} update-source dummy0
{% if connector.use_bfd_timers %}
 neighbor CONNECTOR{{ loop.index1 }} bfd {{ bfd_get_profile(connector.bfd_detect_multiplier, connector.bfd_rx_interval, connector.bfd_tx_interval) }}
{% else %}
 neighbor CONNECTOR{{ loop.index1 }} timers {{ connector.bgp_keepalive_interval }} {{ connector.bgp_holdtime_interval }}
{% endif %}
 neighbor {{ connector.ip }} peer-group CONNECTOR{{ loop.index1 }}
{% endfor %}
{% endif %}
 address-family ipv4 unicast
{% for i in range(client_subnets_count) %}
  network {{ netmask(at(client_subnets, i)) }}
{% endfor %}
{% if connectors_count %}
{% for connector in connectors %}
  neighbor CONNECTOR{{ loop.index1 }} activate
  neighbor CONNECTOR{{ loop.index1 }} route-map PREFIX_POLICY_ALLOW_ALL out
  neighbor CONNECTOR{{ loop.index1 }} route-map PREFIX_POLICY_ALLOW_ALL in
{% endfor %}
{% endif %}
  maximum-paths 128
 exit-address-family
exit
!
route-map PREFIX_POLICY_ALLOW_ALL permit 10
 match ip address prefix-list NET0
exit
!
