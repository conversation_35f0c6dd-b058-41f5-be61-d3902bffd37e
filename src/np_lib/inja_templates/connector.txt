! FRR Config File for NP Connector
! Generated on {{ datetime }}
{% if has_version %}frr version {{ version }}{% endif %}
frr defaults traditional
allow-reserved-ranges
log syslog informational
! client subnet section
{% for i in range(client_subnets_count) %}
ip prefix-list CLIENT seq {{ 5 * loop.index1 }} permit {{ netmask(at(client_subnets, i)) }}
{% endfor %}
! lan subnet section
{% for i in range(lan_subnets_count) %}
ip prefix-list LAN seq {{ 10000 + 5 * loop.index1 }} permit {{ netmask(at(lan_subnets, i)) }}
{% endfor %}
! gateway section
{% if gateways_count %}
{% for gateway in gateways %}
ip route {{ netmask(gateway.ip) }} {{ gateway.interface }}
{% endfor %}
{% endif %}
!
interface dummy0
 ip address {{ netmask(ip) }}
exit
bfd
{{ bfd_write_profiles() }}
exit
!
router bgp {{ asn }}
 bgp router-id {{ router_id }}
 bgp log-neighbor-changes
 no bgp network import-check
{% if gateways_count %}
{% for gateway in gateways %}
 neighbor GATEWAY{{ loop.index1 }} peer-group
 neighbor GATEWAY{{ loop.index1 }} remote-as {{ gateway.asn }}
 neighbor GATEWAY{{ loop.index1 }} ebgp-multihop
 neighbor GATEWAY{{ loop.index1 }} disable-connected-check
 neighbor GATEWAY{{ loop.index1 }} update-source dummy0
{% if gateway.use_bfd_timers %}
 neighbor GATEWAY{{ loop.index1 }} bfd {{ bfd_get_profile(gateway.bfd_detect_multiplier, gateway.bfd_rx_interval, gateway.bfd_tx_interval) }}
{% else %}
 neighbor GATEWAY{{ loop.index1 }} timers {{ gateway.bgp_keepalive_interval }} {{ gateway.bgp_holdtime_interval }}
{% endif %}
 neighbor {{ gateway.ip }} peer-group GATEWAY{{ loop.index1 }}
{% endfor %}
{% endif %}
{% if disable_local_sourcing %}
{% if routers_count %}
{% for router in routers %}
 neighbor ROUTER{{ loop.index1 }} peer-group
 neighbor ROUTER{{ loop.index1 }} remote-as {{ router.asn }}
{% if router.ebgp_multihop_enabled %}
 neighbor ROUTER{{ loop.index1 }} ebgp-multihop
 neighbor ROUTER{{ loop.index1 }} disable-connected-check
 neighbor ROUTER{{ loop.index1 }} update-source {{ router.interface }}
{% endif %}
 neighbor ROUTER{{loop.index1}} timers {{ router.bgp_keepalive_interval }} {{ router.bgp_holdtime_interval }}
 neighbor {{ router.ip }} peer-group ROUTER{{ loop.index1 }}
{% endfor %}
{% endif %}
{% endif %}
 address-family ipv4 unicast
{% if disable_local_sourcing == 0 %}
{% for i in range(lan_subnets_count) %}
  network {{ netmask(at(lan_subnets, i)) }}
{% endfor %}
{% endif %}
{% if gateways_count %}
{% for gateway in gateways %}
  neighbor GATEWAY{{ loop.index1 }} activate
  neighbor GATEWAY{{ loop.index1 }} route-map PREFIX_POLICY_LAN out
  neighbor GATEWAY{{ loop.index1 }} route-map PREFIX_POLICY_CLIENT in
{% endfor %}
{% endif %}
{% if disable_local_sourcing %}
{% if routers_count %}
{% for router in routers %}
  neighbor ROUTER{{ loop.index1 }} activate
  neighbor ROUTER{{ loop.index1 }} route-map PREFIX_POLICY_LAN in
  neighbor ROUTER{{ loop.index1 }} route-map PREFIX_POLICY_CLIENT out
{% endfor %}
{% endif %}
{% endif %}
  maximum-paths 128
 exit-address-family
exit
!
route-map PREFIX_POLICY_CLIENT permit 10
 match ip address prefix-list CLIENT
exit
!
route-map PREFIX_POLICY_LAN permit 20
 match ip address prefix-list LAN
exit
!
