/*
 * np_bgp_connectors_config.h. Copyright (C) 2025 Zscaler Inc, All Rights Reserved
 */

#ifndef _NP_BGP_CONNECTORS_CONFIG_H_
#define _NP_BGP_CONNECTORS_CONFIG_H_

#include "argo/argo.h"

extern struct wally_index_column **np_bgp_connectors_config_customer_gid_column;
extern struct wally_index_column **np_bgp_connectors_config_connector_gid_column;

extern struct argo_structure_description *np_bgp_connectors_config_description;

struct np_bgp_connectors_config                 /* _ARGO: object_definition */
{
    /* Standard SQL fields. */
    int64_t gid;                                /* _ARGO: integer, index, key */
    int64_t sequence;                           /* _ARGO: integer, sequence */
    int64_t deleted;                            /* _ARGO: integer, deleted */
    int64_t modified_time;                      /* _ARGO: integer */
    int64_t creation_time;                      /* _ARGO: integer */
    int64_t modifiedby_userid;                  /* _ARGO: integer */

    int64_t customer_gid;                       /* _ARGO: integer, index */
    /* np connector gid */
    int64_t connector_gid;                      /* _ARGO: integer, index */
    int64_t asn;                                /* _ARGO: integer */
    struct argo_inet *router_id;                /* _ARGO: inet */

    /* config_mode: GENERATED, OVERRIDE */
    char *config_mode;                          /* _ARGO: string */
    char *override_config;                      /* _ARGO: string */

    int force_reload_config;                    /* _ARGO: integer */
};


/*
 * Must be called after np_init()
 * set single_tenant_wally + tenant_id only if single tenant.
 * NOTE: NOT fully loaded, registration should only happen on one column!
 */
int
np_bgp_connectors_config_table_init(struct wally *single_tenant_wally,
                                    int64_t tenant_id,
                                    wally_row_callback_f *all_rows_callback,
                                    void *all_rows_callback_cookie,
                                    int single_tenant_fully_loaded);

/*
 * Get the connector BGP config for a given gid. This only returns what is in memory.
 */
int
np_bgp_connectors_config_get_by_gid_immediate(int64_t gid, struct np_bgp_connectors_config **bgp_conn);

/*
 * Get the connector BGP config for a given connector gid. This also does registration.
 */
int
np_bgp_connectors_config_get_by_connector_gid(int64_t connector_gid,
                                              struct np_bgp_connectors_config **bgp_conn,
                                              wally_response_callback_f callback_f,
                                              void *callback_cookie,
                                              int64_t callback_id);

/*
 * Get all connector BGP configs for a given customer. This only returns what is in memory.
 */
int
np_bgp_connectors_config_get_by_customer_gid_immediate(int64_t customer_gid,
                                                       struct np_bgp_connectors_config **bgp_conn,
                                                       size_t *bgp_conn_count);

#endif /* _NP_BGP_CONNECTORS_CONFIG_H_ */
