/*
 * np_bgp_config.c. Copyright (C) 2025 Zscaler Inc, All Rights Reserved
 */

#include "argo/argo_private.h"  // Needed for ARGO_FREE :(
#include "np_lib/np_private.h"
#include "np_lib/np_rpc.h"
#include "np_lib/np_bgp_config.h"
#include "np_lib/np_bgp_config_compiled.h"
#include "np_lib/np_bgp_config_frr.h"
#include "zpath_lib/zpath_debug.h"

struct argo_structure_description *np_bgp_peer_config_description;
struct argo_structure_description *np_bgp_config_gateway_description;
struct argo_structure_description *np_bgp_config_connector_description;

static struct zpn_np_bgp_config_stats g_np_bgp_config_stats;

const char *np_bgp_config_error_strings[] = {
    [NP_BGP_CONFIG_NO_ERROR] = "NP_BGP_CONFIG_NO_ERROR",
    [NP_BGP_CONFIG_PARSER_ERROR] = "NP_BGP_CONFIG_PARSER_ERROR",
    [NP_BGP_CONFIG_DATA_ERROR] = "NP_BGP_CONFIG_DATA_ERROR",
    [NP_BGP_CONFIG_FILE_ERROR] = "NP_BGP_CONFIG_FILE_ERROR",
    [NP_BGP_CONFIG_RENDER_ERROR] = "NP_BGP_CONFIG_RENDER_ERROR",
    [NP_BGP_CONFIG_ARGUMENT_ERROR] = "NP_BGP_CONFIG_ARGUMENT_ERROR",
    [NP_BGP_CONFIG_UNKNOWN_ERROR] = "NP_BGP_CONFIG_UNKNOWN_ERROR"
};

static void np_bgp_config_stats_counter_inc(int res)
{
    if (res == NP_BGP_CONFIG_NO_ERROR) {
        __sync_add_and_fetch_8(&g_np_bgp_config_stats.num_of_config_generation_success, 1);
    } else if (res == NP_BGP_CONFIG_PARSER_ERROR) {
        __sync_add_and_fetch_8(&g_np_bgp_config_stats.num_of_config_generation_parser_error, 1);
    } else if (res == NP_BGP_CONFIG_DATA_ERROR) {
        __sync_add_and_fetch_8(&g_np_bgp_config_stats.num_of_config_generation_data_error, 1);
    } else if (res == NP_BGP_CONFIG_FILE_ERROR) {
        __sync_add_and_fetch_8(&g_np_bgp_config_stats.num_of_config_generation_file_error, 1);
    } else if (res == NP_BGP_CONFIG_RENDER_ERROR) {
        __sync_add_and_fetch_8(&g_np_bgp_config_stats.num_of_config_generation_render_error, 1);
    } else if (res == NP_BGP_CONFIG_ARGUMENT_ERROR) {
        __sync_add_and_fetch_8(&g_np_bgp_config_stats.num_of_argument_error, 1);
    } else {
         __sync_add_and_fetch_8(&g_np_bgp_config_stats.num_of_config_generation_unknown_error, 1);
    }
}

void np_bgp_config_dump_peer_config(const struct np_bgp_peer_config *peer_config)
{
    if (!peer_config) return;
    char jsonout[1000];
    if (ARGO_RESULT_NO_ERROR == argo_structure_dump(np_bgp_peer_config_description,
                                                    (void *)peer_config, jsonout, sizeof(jsonout), NULL, 1)) {
        NP_LOG(AL_INFO, "peer config: %s", jsonout);
    }
}

/* Returned str buf must be freed after use! */
static inline char *gateway_config_to_json_str(const struct np_bgp_config_gateway *gw_config)
{
    return argo_structure_dump_allocated_no_header(np_bgp_config_gateway_description, (void *)gw_config, 1);
}

/* Returned str buf must be freed after use! */
static inline char *connector_config_to_json_str(const struct np_bgp_config_connector *conn_config)
{
    return argo_structure_dump_allocated_no_header(np_bgp_config_connector_description, (void *)conn_config, 1);
}

/* Returns True only when all fields are valid */
static int is_gateway_config_valid(const struct np_bgp_config_gateway *gw_config)
{
    if (!gw_config) return 0;
    if (argo_inet_is_cidr(&(gw_config->ip))) return 0; // CIDR is invalid
    if (gw_config->asn <= 0) return 0;
    if (!gw_config->vrf_name) return 0;
    if (!gw_config->wg_interface_name) return 0;
    if (gw_config->connectors_count > NP_BGP_MAX_PEERS) return 0;
    if (gw_config->client_subnets_count > NP_BGP_MAX_SUBNETS) return 0;

    return 1;
}

/* Returns True only when all fields are valid */
static int is_connector_config_valid(const struct np_bgp_config_connector *conn_config)
{
    if (!conn_config) return 0;
    if (argo_inet_is_cidr(&(conn_config->ip))) return 0; // CIDR is invalid
    if (conn_config->asn <= 0) return 0;
    if (conn_config->gateways_count > NP_BGP_MAX_PEERS) return 0;
    if (conn_config->routers_count > NP_BGP_MAX_PEERS) return 0;
    if (conn_config->client_subnets_count > NP_BGP_MAX_SUBNETS) return 0;
    if (conn_config->lan_subnets_count > NP_BGP_MAX_SUBNETS) return 0;

    return 1;
}

#define DEBUG_TEMPLATE_FILENAME "template.txt"
#define DEBUG_DATA_FILENAME "data.json"
#define DEBUG_FRR_CONF_FILENAME "frr.conf"
/*
 * Sample template files: np_lib/templates/connector.txt, np_lib/templates/gateway.txt
 * Sample data fileds: np_lib/tests/connector.json, np_lib/tests/gateway.json
 */
static int np_bgp_config_generate_config_debug(struct zpath_debug_state* request_state,
                                               const char** query_values,
                                               int query_value_count __attribute__((unused)),
                                               void* cookie __attribute__((unused)))
{
    const char *file_template;
    const char *file_data;
    int res;

    if (!query_values[0]) {
        ZDP("Template file is not specified, using default file name under current working directory:%s\n", DEBUG_TEMPLATE_FILENAME);
        file_template = DEBUG_TEMPLATE_FILENAME;
    } else {
        file_template = query_values[0];
    }
    if (!query_values[1]) {
        ZDP("Data file is not specified, using default file name under current working directory:%s\n", DEBUG_DATA_FILENAME);
        file_data = DEBUG_DATA_FILENAME;
    } else {
        file_data = query_values[1];
    }

    res = np_bgp_config_frr_generate_from_file(file_template, file_data, DEBUG_FRR_CONF_FILENAME);
    if (res) {
        ZDP("Failed to generate BGP frr config file: %s\n", np_bgp_config_error_string(res));
    }

    return NP_RESULT_NO_ERROR;
}

const char *np_bgp_config_error_string(int result)
{
    if (result >= (sizeof(np_bgp_config_error_strings) / sizeof (const char *))) return "INVALID_RESULT";
    if (result < 0) return "INVALID_RESULT";
    if (np_bgp_config_error_strings[result] == NULL) return "INVALID_RESULT";
    return np_bgp_config_error_strings[result];
}

int np_bgp_config_generate_gateway_frr_conf(const struct np_bgp_config_gateway *gw_config, const char *output_file_path)
{
    int res = NP_RESULT_NO_ERROR;

    if (!gw_config) {
        __sync_add_and_fetch_8(&g_np_bgp_config_stats.num_of_argument_error, 1);
        NP_LOG(AL_ERROR, "attempting to generate frr config but gw_config is NULL");
        return NP_RESULT_ERR;
    }

#ifdef USE_INJA
    /* support inja json structure rendering
     * currently we are using argo structure dump which should work fine for all cases, if we notice any
     * issues with argo in the future, let's support config rendering via inja::json */
    NP_LOG(AL_CRITICAL, "Implement me");
    return NP_RESULT_NOT_IMPLEMENTED;
#else
    char *gw_config_json_str = NULL;
    /* Use argo to dump config to json string */
    gw_config_json_str = gateway_config_to_json_str(gw_config);
    if (!gw_config_json_str) {
        __sync_add_and_fetch_8(&g_np_bgp_config_stats.num_of_argo_json_serialize_error, 1);
        NP_LOG(AL_ERROR, "Failed to serialize gateway FRR config to string array");
        return NP_RESULT_ERR;
    }

    NP_LOG(AL_INFO, "Gateway FRR config json: %s", gw_config_json_str);
    if (!is_gateway_config_valid(gw_config)) {
        NP_LOG(AL_ERROR, "Invalid gateway FRR config");
        __sync_add_and_fetch_8(&g_np_bgp_config_stats.num_of_invalid_config, 1);
        ARGO_FREE(gw_config_json_str);
        return NP_RESULT_BAD_DATA;
    }

    res = np_bgp_config_frr_generate_from_str(gw_config_json_str, output_file_path, NP_BGP_CONFIG_TEMPLATE_GATEWAY);
    if (res) {
        NP_LOG(AL_ERROR, "Failed to generate gateway FRR config: %s", np_bgp_config_error_string(res));
    }
    np_bgp_config_stats_counter_inc(res);
    ARGO_FREE(gw_config_json_str);
#endif

    return res;
}

int np_bgp_config_generate_connector_frr_conf(const struct np_bgp_config_connector *conn_config, const char *output_file_path)
{
    int res = NP_RESULT_NO_ERROR;

    if (!conn_config) {
        NP_LOG(AL_ERROR, "attempting to generate frr config but conn_config is NULL");
        return NP_RESULT_ERR;
    }

#ifdef USE_INJA
    /* support inja json structure rendering
     * currently we are using argo structure dump which should work fine for all cases, if we notice any
     * issues with argo in the future, let's support config rendering via inja::json */
    NP_LOG(AL_CRITICAL, "Implement me");
    return NP_RESULT_NOT_IMPLEMENTED;

#else
    char *conn_config_json_str = NULL;
    /* Use argo to dump config to json string */
    conn_config_json_str = connector_config_to_json_str(conn_config);
    if (!conn_config_json_str) {
        __sync_add_and_fetch_8(&g_np_bgp_config_stats.num_of_argo_json_serialize_error, 1);
        NP_LOG(AL_ERROR, "Failed to serialize connector FRR config to string array");
        return NP_RESULT_ERR;
    }

    NP_LOG(AL_INFO, "Connector FRR config json: %s", conn_config_json_str);
    if (!is_connector_config_valid(conn_config)) {
        NP_LOG(AL_ERROR, "Invalid connector FRR config");
        __sync_add_and_fetch_8(&g_np_bgp_config_stats.num_of_invalid_config, 1);
        ARGO_FREE(conn_config_json_str);
        return NP_RESULT_BAD_DATA;
    }

    res = np_bgp_config_frr_generate_from_str(conn_config_json_str, output_file_path, NP_BGP_CONFIG_TEMPLATE_CONNECTOR);
    if (res) {
        NP_LOG(AL_ERROR, "Failed to generate connector FRR config: %s", np_bgp_config_error_string(res));
    }
    np_bgp_config_stats_counter_inc(res);
    ARGO_FREE(conn_config_json_str);
#endif

    return res;
}

static int
np_bgp_config_dump_stats(struct zpath_debug_state  *request_state,
                         const char                **query_values __attribute__((unused)),
                         int                       query_value_count __attribute__((unused)),
                         void                      *cookie __attribute__((unused)))
{
    char jsonout[10000];

    if (ARGO_RESULT_NO_ERROR == argo_structure_dump(zpn_np_bgp_config_stats_description,
                                                    &g_np_bgp_config_stats, jsonout, sizeof(jsonout), NULL, 1)) {
        ZDP("%s\n", jsonout);
    }
    return NP_RESULT_NO_ERROR;
}

int np_bgp_config_stats_init(int64_t customer_gid)
{
    static int initialized = 0;
    if (initialized) {
        NP_LOG(AL_INFO, "NP BGP config stats is already initialized");
        return NP_RESULT_NO_ERROR;
    }

    if (!np_is_redundancy_feature_enabled(customer_gid)) {
        NP_LOG(AL_INFO, "NP Redundancy feature is disabled, skipping BGP config stats init");
        return NP_RESULT_NO_ERROR;
    }

    if (!argo_log_register_structure(argo_log_get("statistics_log"),
                                     "bgp_config_stats",
                                     AL_INFO,
                                     MINUTE_TO_US(5), /* 5 mins interval */
                                     zpn_np_bgp_config_stats_description,
                                     &g_np_bgp_config_stats,
                                     1,
                                     NULL,
                                     NULL)) {
        NP_LOG(AL_CRITICAL, "Could not register zpn_np_bgp_config_stats. statistics_log not initialized?");
        return NP_RESULT_ERR;
    }
    initialized = 1;

    return NP_RESULT_NO_ERROR;
}

int np_bgp_config_init()
{
    static int initialized = 0;

    if (initialized) {
        return NP_RESULT_NO_ERROR;
    }

    np_bgp_peer_config_description = argo_register_global_structure(NP_BGP_PEER_CONFIG_HELPER);
    np_bgp_config_gateway_description = argo_register_global_structure(NP_BGP_CONFIG_GATEWAY_HELPER);
    np_bgp_config_connector_description = argo_register_global_structure(NP_BGP_CONFIG_CONNECTOR_HELPER);

    if (!np_bgp_peer_config_description ||
        !np_bgp_config_gateway_description ||
        !np_bgp_config_connector_description) {
        return NP_RESULT_ERR;
    }

    if (zpath_is_dev_environment()) {
        if (zpath_debug_add_admin_command("Debug endpoint for generating FRR configuration file",
                                        "/np/bgp/generate_config",
                                        np_bgp_config_generate_config_debug,
                                        NULL,
                                        "template", "Optional. File path for the inja template, default is template.txt in current working directory",
                                        "data", "Optional. File path for the data, default is data.json in current working directory",
                                        NULL)) {
            NP_LOG(AL_ERROR, "Couldn't add curl debug command /np/bgp/generate_config");
            return NP_RESULT_ERR;
        }
    }

    if (zpath_debug_add_read_command("dump BGP config instantiation stats",
                                     "/np/bgp/dump_config_stats",
                                     np_bgp_config_dump_stats,
                                     NULL,
                                     NULL)) {
        NP_LOG(AL_ERROR, "Couldn't add curl debug command /np/bgp/dump_config_stats");
        return NP_RESULT_ERR;
    }

    initialized = 1;

    return NP_RESULT_NO_ERROR;
}
