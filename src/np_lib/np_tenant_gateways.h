/*
 * np_tenant_gateways.h. Copyright (C) 2024 Zscaler Inc, All Rights Reserved
 */

#ifndef _NP_TENANT_GATEWAYS_H_
#define _NP_TENANT_GATEWAYS_H_

#include "argo/argo.h"

struct np_tenant_gateways                         /* _ARGO: object_definition */
{
    /* Standard SQL fields. */
    int64_t gid;                                  /* _ARGO: integer, index, key */
    int64_t sequence;                             /* _ARGO: integer, sequence */
    int64_t deleted;                              /* _ARGO: integer, deleted */
    int64_t modified_time;                        /* _ARGO: integer */
    int64_t creation_time;                        /* _ARGO: integer */
    int64_t modifiedby_userid;                    /* _ARGO: integer */

    int64_t customer_gid;                         /* _ARGO: integer, index */
    const char *name;                             /* _ARGO: string */
    const char *description;                      /* _ARGO: string */
    int64_t np_gateways_gid;                      /* _ARGO: integer, index */
    const char *public_key;                       /* _ARGO: string */
    int64_t public_key_expiry;                    /* _ARGO: integer */
    const struct argo_inet *listener_ip_address;  /* _ARGO: inet */
    int listener_port;                            /* _ARGO: integer */
    int16_t gateway_state;                        /* _ARGO: integer */
    int mtu;                                      /* _ARGO: integer */
    int redundant_mode_enabled;                   /* _ARGO: integer */
};

typedef enum np_tenant_gateway_state
{
    NP_TENANT_GATEWAY_STATE_DISABLED     = 0,
    NP_TENANT_GATEWAY_STATE_ENROLLED     = 1,
    NP_TENANT_GATEWAY_STATE_ONLINE       = 2,
    NP_TENANT_GATEWAY_STATE_UNHEALTHY    = 3,
    NP_TENANT_GATEWAY_STATE_ENROLLING    = 4,
    NP_TENANT_GATEWAY_STATE_UNENROLLING  = 5,
    NP_TENANT_GATEWAY_STATE_COUNT        = 6
} np_tenant_gateway_state_t;


/*
 * Must be called after np_init()
 * set single_tenant_wally + tenant_id only if single tenant.
 * NOTE: NOT fully loaded, registration should only happen on one column!
 */
int
np_tenant_gateways_table_init(struct wally *single_tenant_wally,
                              int64_t tenant_id,
                              wally_row_callback_f *all_rows_callback,
                              void *all_rows_callback_cookie,
                              int single_tenant_fully_loaded);

/* load the tenant gateway config per customer */
int
np_tenant_gateways_load(int64_t customer_gid,
                        wally_response_callback_f callback_f,
                        void *callback_cookie,
                        int64_t callback_id);

/*
 * Get a np tenant gateway given a tenant gateway ID.
 * Returns in-memory result, or failure.
 */
int
np_tenant_gateways_get_by_id_immediate(int64_t tenant_gateway_id,
                                       struct np_tenant_gateways **tenant_gateway);

/*
 * Get all np tenant gateways for a given customer. This only returns what is in memory.
 */
int
np_tenant_gateways_get_by_customer_gid_immediate(int64_t customer_gid,
                                                struct np_tenant_gateways **tenant_gateways,
                                                size_t *tenant_gateways_count);

const char *gateway_health_to_str(const enum np_tenant_gateway_state gateway_state,
                                  size_t *length);

#endif /* _NP_TENANT_GATEWAYS_H_ */
