/*
* np_connector_groups_lan_subnets_mapping.h. Copyright (C) 2025 Zscaler Inc, All Rights Reserved
*/

#ifndef _NP_CONNECTOR_GROUPS_LAN_SUBNETS_MAPPING_H_
#define _NP_CONNECTOR_GROUPS_LAN_SUBNETS_MAPPING_H_

#include "argo/argo.h"

#define NP_CONNECTOR_GROUPS_LAN_SUBNETS_MAPPING_MAX_COUNT 1000

extern struct wally_index_column **np_connector_groups_lan_subnets_mapping_customer_gid_column;
extern struct wally_index_column **np_connector_groups_lan_subnets_mapping_connector_group_gid_column;

extern struct argo_structure_description *np_np_connector_groups_lan_subnets_mapping_description;

struct np_connector_groups_lan_subnets_mapping  /* _ARGO: object_definition */
{
    /* Standard SQL fields. */
    int64_t gid;                                /* _ARGO: integer, index, key */
    int64_t sequence;                           /* _ARGO: integer, sequence */
    int64_t deleted;                            /* _ARGO: integer, deleted */
    int64_t modified_time;                      /* _ARGO: integer */
    int64_t creation_time;                      /* _ARGO: integer */
    int64_t modifiedby_userid;                  /* _ARGO: integer */

    int64_t customer_gid;                       /* _ARGO: integer, index */
    /* np connector group gid */
    int64_t connector_group_gid;                /* _ARGO: integer, index */
    int64_t lan_subnet_gid;                     /* _ARGO: integer, index */
};


/*
* Must be called after np_init()
* set single_tenant_wally + tenant_id only if single tenant.
* NOTE: NOT fully loaded, registration should only happen on one column!
*/
int
np_connector_groups_lan_subnets_mapping_table_init(struct wally *single_tenant_wally,
                                                   int64_t tenant_id,
                                                   wally_row_callback_f *all_rows_callback,
                                                   void *all_rows_callback_cookie,
                                                   int single_tenant_fully_loaded);

/*
 * Register np_connector_groups table by gid and get all rows matching with group_gid
 * This routine will block until all rows are read
 */
int
np_connector_groups_lan_subnets_mapping_register_by_connector_group_gid(int64_t group_gid);

/*
* Get all lan subnet gids mapped to a given connector group gid. This only returns what is in memory.
*/
int
np_connector_groups_lan_subnets_mapping_get_subnets_by_group_gid_immediate(int64_t connector_group_gid,
                                                                           int64_t *lan_subnet_gids,
                                                                           size_t *lan_subnets_count);

/*
* Get all connector group gids mapped to a given lan subnet gid. This only returns what is in memory.
*/
int
np_connector_groups_lan_subnets_mapping_get_groups_by_subnet_gid_immediate(int64_t lan_subnet_gid,
                                                                           int64_t *connector_group_gids,
                                                                           size_t *connector_groups_count);

/*
* Get all connector group to lan subnet mappings for a given customer. This only returns what is in memory.
*/
int
np_connector_groups_lan_subnets_mapping_get_by_customer_gid_immediate(int64_t customer_gid,
                                                                      struct np_connector_groups_lan_subnets_mapping **mapping,
                                                                      size_t *mapping_count);

#endif /* _NP_CONNECTOR_GROUPS_LAN_SUBNETS_MAPPING_H_ */
