find_package(inja REQUIRED)

argo_parse_files(
    INPUT_FILES
        np_rpc.h
        np_gateways.h
        np_tenant_gateways.h
        np_client_subnets.h
        np_dns_ns_records.h
        np_lan_subnets.h
        np_connectors.h
        np_clients.h
        np_connector_groups.h
        np_connector_groups_lan_subnets_mapping.h
        np_bgp_connectors_config.h
        np_bgp_gateways_config.h
        np_bgp_connector_session_config.h
        np_bgp_gateway_session_config.h
        np_bgp.h
        np_bgp.c
        np_bgp_config.h
        np_bgp_config.c
        np_bgp_config_frr.h
    OUTPUT_DIR ${CMAKE_CURRENT_BINARY_DIR}
    OUTPUT_FILES_VAR generated_headers
)

xxd_files(
    FILES inja_templates/connector.txt inja_templates/gateway.txt
    OUTPUT_DIR ${CMAKE_CURRENT_BINARY_DIR}
    OUTPUT_FILES_VAR generated_headers
)

add_custom_command(
    OUTPUT ${CMAKE_CURRENT_BINARY_DIR}/frr.gateway.h ${CMAKE_CURRENT_BINARY_DIR}/frr.connector.h
    COMMAND cat ${CMAKE_CURRENT_SOURCE_DIR}/inja_templates/connector.txt > frr.connector
    COMMAND xxd -i frr.connector frr.connector.h
    COMMAND cat ${CMAKE_CURRENT_SOURCE_DIR}/inja_templates/gateway.txt > frr.gateway
    COMMAND xxd -i frr.gateway frr.gateway.h
)

set(lib_sources
    np.c
    np_rpc.c
    np_client_subnets.c
    np_dns_ns_records.c
    np_lan_subnets.c
    np_clients.c
    np_gateways.c
    np_tenant_gateways.c
    np_connectors.c
    np_connector_groups.c
    np_frr_utils.c
    np_connector_groups_lan_subnets_mapping.c
    np_bgp_connectors_config.c
    np_bgp_gateways_config.c
    np_bgp_connector_session_config.c
    np_bgp_gateway_session_config.c
    np_bgp.c
    np_bgp_config.c
    np_bgp_config_frr.cpp
)

add_library(
    np_lib
    STATIC
    ${lib_sources}
    ${CMAKE_CURRENT_BINARY_DIR}/frr.gateway.h
    ${CMAKE_CURRENT_BINARY_DIR}/frr.connector.h
    ${generated_headers}
)
target_link_libraries(np_lib PUBLIC zpath_lib)

set(TESTS_DIR ${CMAKE_CURRENT_SOURCE_DIR}/tests)
add_subdirectory(tests)
