/*
 * np_bgp_connector_session_config.h. Copyright (C) 2025 Zscaler Inc, All Rights Reserved
 */

#ifndef _NP_BGP_CONNECTOR_SESSION_CONFIG_H_
#define _NP_BGP_CONNECTOR_SESSION_CONFIG_H_

#include "argo/argo.h"

extern struct wally_index_column **np_bgp_connector_session_config_customer_gid_column;
extern struct wally_index_column **np_bgp_connector_session_config_bgp_config_gid_column;

extern struct argo_structure_description *np_bgp_connector_session_config_description;

struct np_bgp_connector_session_config         /* _ARGO: object_definition */
{
   /* Standard SQL fields. */
   int64_t gid;                                /* _ARGO: integer, index, key */
   int64_t sequence;                           /* _ARGO: integer, sequence */
   int64_t deleted;                            /* _ARGO: integer, deleted */
   int64_t modified_time;                      /* _ARGO: integer */
   int64_t creation_time;                      /* _ARGO: integer */
   int64_t modifiedby_userid;                  /* _ARGO: integer */

   int64_t customer_gid;                       /* _ARGO: integer, index */
   int64_t bgp_config_gid;                     /* _ARGO: integer, index */

   /* neighbor_instance_type: GATEWAY, ROUTER
   * neighbor_config_gid is the gid in the corresponding np_bgp_[gateways|routers]_config table */
   char *neighbor_instance_type;               /* _ARGO: string */
   int64_t neighbor_config_gid;                /* _ARGO: integer */

   struct argo_inet *neighbor_ip;              /* _ARGO: inet */
   int64_t neighbor_asn;                       /* _ARGO: integer */

   int ebgp_multihop_enabled;                  /* _ARGO: integer */
   char *update_source_interface;              /* _ARGO: string */

   int use_bfd_timers;                         /* _ARGO: integer */
   int bgp_keepalive_interval;                 /* _ARGO: integer */
   int bgp_holdtime_interval;                  /* _ARGO: integer */
   int bfd_tx_interval;                        /* _ARGO: integer */
   int bfd_rx_interval;                        /* _ARGO: integer */
   int bfd_detect_multiplier;                  /* _ARGO: integer */

   char *md5_hash;                             /* _ARGO: string */
   char **afi_safi;                            /* _ARGO: string */
   int afi_safi_count;                         /* _ARGO: integer, quiet, count: afi_safi */
};


/*
 * Must be called after np_init()
 * set single_tenant_wally + tenant_id only if single tenant.
 * NOTE: NOT fully loaded, registration should only happen on one column!
 */
int
np_bgp_connector_session_config_table_init(struct wally *single_tenant_wally,
                                           int64_t tenant_id,
                                           wally_row_callback_f *all_rows_callback,
                                           void *all_rows_callback_cookie,
                                           int single_tenant_fully_loaded);

/*
 * Must be called after np_bgp_connector_session_config_table_init()
 * Register the table by bgp_config_gid, block until all rows arrived.
 */
int
np_bgp_connector_session_config_register_by_bgp_config_gid(int64_t bgp_config_gid);

/*
 * Get all BGP session configs for a given customer. This only returns what is in memory.
 */
int
np_bgp_connector_session_config_get_by_customer_gid_immediate(int64_t customer_gid,
                                                              struct np_bgp_connector_session_config **session_config,
                                                              size_t *session_config_count);

#endif /* _NP_BGP_CONNECTOR_SESSION_CONFIG_H_ */
