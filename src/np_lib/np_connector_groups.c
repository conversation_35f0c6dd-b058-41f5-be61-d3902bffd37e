/*
 * np_connector_groups.c. Copyright (C) 2024 Zscaler Inc, All Rights Reserved
 */

#include "np_lib/np_private.h"
#include "np_lib/np_connector_groups.h"
#include "np_lib/np_connector_groups_compiled.h"
#include "wally/wally.h"
#include "zpath_lib/zpath_app.h"
#include "zpath_lib/zpath_debug.h"
#include "np_lib/np.h"

#define NP_CONNECTOR_GROUPS_MAX_CONNECTOR_GROUPS_PER_CUSTOMER 100

struct argo_structure_description *np_connector_groups_description = NULL;

struct wally_index_column **np_connector_groups_index_column = NULL;
struct wally_index_column **np_connector_groups_customer_gid_column = NULL;

static void
np_connector_groups_row_fixup(struct argo_object *row)
{
    // scope gid needs to be fixed when we support it in future

    return;
}

static int
np_connector_groups_dump_by_customer(struct zpath_debug_state* request_state,
                                     const char** query_values,
                                     int query_value_count,
                                     void* cookie)
{
    int res;
    int64_t customer_gid = 0;
    struct np_connector_groups *connector_groups[NP_CONNECTOR_GROUPS_MAX_CONNECTOR_GROUPS_PER_CUSTOMER];
    char jsonout[10000];
    size_t connector_groups_count;
    size_t i;

    (void) query_value_count;
    (void) cookie;

    if (query_values[0]) {
        customer_gid = strtoul(query_values[0], NULL, 10);
    } else {
        ZDP("Customer gid is required!\n");
        return NP_RESULT_NO_ERROR;
    }

    connector_groups_count = NP_CONNECTOR_GROUPS_MAX_CONNECTOR_GROUPS_PER_CUSTOMER;
    res = np_connector_groups_get_by_customer_gid_immediate(customer_gid, connector_groups, &connector_groups_count);
    if (res != NP_RESULT_NO_ERROR) {
        ZDP("couldn't query np_connector_groups table by customer_gid, wally returned (%s)\n", zpath_result_string(res));
        return NP_RESULT_NO_ERROR;
    }

    for (i = 0; i < connector_groups_count; i++) {
        if (argo_structure_dump(np_connector_groups_description, connector_groups[i], jsonout, sizeof(jsonout), NULL, 1) == ARGO_RESULT_NO_ERROR) {
            ZDP("%s\n", jsonout);
        }
    }
    if (connector_groups_count) {
        ZDP("%zu matching np connector groups in np_connector_groups table\n", connector_groups_count);
    }

    return NP_RESULT_NO_ERROR;
}

static int
np_connector_groups_table_response_callback(void *cookie,
                                            struct wally_registrant *registrant,
                                            struct wally_table *table,
                                            int64_t sequence,
                                            int row_count)
{
    struct wally_interlock *lock = (struct wally_interlock *)cookie;
    if (lock) {
        wally_interlock_release(lock);
    }
    NP_LOG(AL_INFO, "Loaded %d rows from np_connector_groups", row_count);

    return WALLY_RESULT_NO_ERROR;
}

static inline int wally_table_register_by_group_gid(int64_t group_gid, struct wally_interlock *lock)
{
    int shard_index = ZPATH_SHARD_FROM_GID(group_gid);
    int res = wally_table_register_for_row(NULL, np_connector_groups_index_column[shard_index],
                &group_gid, sizeof(group_gid), 0, 0, REQUEST_AT_LEAST_ONE, 0, 0, np_connector_groups_table_response_callback, lock);
    if (res == WALLY_RESULT_NOT_READY) {
        wally_table_deregister_for_row(NULL, np_connector_groups_index_column[shard_index], NULL, 0);
    }
    return res;
}

int
np_connector_groups_register_by_connector_group_gid(int64_t group_gid)
{
    struct wally_interlock lock;
    int not_ready_count = 0;
    int res = ZPATH_RESULT_NO_ERROR;

    wally_interlock_lock_1(&lock);
    while ((res = wally_table_register_by_group_gid(group_gid, &lock)) == WALLY_RESULT_NOT_READY) {
        if ((not_ready_count++ % 10) == 0) {
            NP_LOG(AL_NOTICE, "Wally is not ready for registering np_connector_groups table");
        }
        /* Heartbeat so we don't crash when wally connection is down */
        zthread_heartbeat(NULL);
        sleep(1);
    }
    if (res != WALLY_RESULT_NO_ERROR && res != WALLY_RESULT_ASYNCHRONOUS) {
        NP_LOG(AL_ERROR, "Failed to register wally table for row: %s",  zpath_result_string(res));
        return res;
    }

    NP_LOG(AL_NOTICE, "Registering np_connector_groups table with group_gid %"PRId64"", group_gid);
    /* Deadlock until our callback releases this lock */
    wally_interlock_lock_2(&lock);
    NP_LOG(AL_NOTICE, "Registering np_connector_groups table with group_gid %"PRId64"... Complete", group_gid);

    return NP_RESULT_NO_ERROR;
}

int
np_connector_groups_table_init(struct wally *single_tenant_wally,
                               int64_t tenant_id,
                               wally_row_callback_f *all_rows_callback,
                               void *all_rows_callback_cookie,
                               int single_tenant_fully_loaded)
{
    int res;

    np_connector_groups_description = argo_register_global_structure(NP_CONNECTOR_GROUPS_HELPER);
    if (!np_connector_groups_description) {
        return NP_RESULT_ERR;
    }

    if (single_tenant_wally) {
        struct wally_table *table;
        int shard_index = ZPATH_SHARD_FROM_GID(tenant_id);
        np_connector_groups_index_column = NP_CALLOC(ZPATH_MAX_SHARDS * sizeof(*np_connector_groups_index_column));
        np_connector_groups_customer_gid_column = NP_CALLOC(ZPATH_MAX_SHARDS * sizeof(*np_connector_groups_customer_gid_column));

        if (single_tenant_fully_loaded) {
            res = zpath_app_fully_loaded_customer_table(&table,
                                                        tenant_id,
                                                        single_tenant_wally,
                                                        np_connector_groups_description,
                                                        all_rows_callback,
                                                        all_rows_callback_cookie,
                                                        np_connector_groups_row_fixup,
                                                        0); // Do not register with zpath_table
            if (res) {
                NP_LOG(AL_ERROR, "Could not get np_connector_groups fully loaded");
                return NP_RESULT_ERR;
            }
        } else {
            table = wally_table_create(single_tenant_wally,
                                       1,
                                       np_connector_groups_description,
                                       all_rows_callback,
                                       all_rows_callback_cookie,
                                       1,
                                       0,
                                       np_connector_groups_row_fixup);
            if (!table) {
                NP_LOG(AL_ERROR, "Could not get np_connector_groups table");
                return NP_RESULT_ERR;
            }
        }

        np_connector_groups_index_column[shard_index] = wally_table_get_index(table, "gid");
        if (!np_connector_groups_index_column[shard_index]) {
            NP_LOG(AL_ERROR, "Could not get index column");
            return NP_RESULT_ERR;
        }

        np_connector_groups_customer_gid_column[shard_index] = wally_table_get_index(table, "customer_gid");
        if (!np_connector_groups_customer_gid_column[shard_index]) {
            NP_LOG(AL_ERROR, "Could not get customer gid column");
            return NP_RESULT_ERR;
        }

    } else {
        res = zpath_app_add_np_sharded_table(np_connector_groups_description,
                                             all_rows_callback,
                                             all_rows_callback_cookie,
                                             0,
                                             np_connector_groups_row_fixup);
        if (res) {
            return res;
        }

        np_connector_groups_index_column = zpath_app_get_np_sharded_index("np_connector_groups", "gid");
        if (!np_connector_groups_index_column) {
            NP_LOG(AL_ERROR, "Could not get index column");
            return NP_RESULT_ERR;
        }

        np_connector_groups_customer_gid_column = zpath_app_get_np_sharded_index("np_connector_groups", "customer_gid");
        if (!np_connector_groups_customer_gid_column) {
            NP_LOG(AL_ERROR, "Could not get customer gid column");
            return NP_RESULT_ERR;
        }
    }

    res = zpath_debug_add_safe_read_command("Dump all connector groups registered per customer.",
                                       "/np/connector_groups/customer_dump",
                                       np_connector_groups_dump_by_customer,
                                       NULL,
                                       "customer", "Required. Customer GID",
                                       NULL);
    if (res) {
        NP_LOG(AL_ERROR, "Couldn't add curl debug command /np/connector_groups/customer_dump");
        return res;
    }

    return NP_RESULT_NO_ERROR;
}

int np_connector_groups_get_by_id_immediate(int64_t gid,
                                            struct np_connector_groups **connector_group)
{
    size_t row_count = 1;
    int res;

    int shard_index = ZPATH_SHARD_FROM_GID(gid);

    res = wally_table_get_rows_fast(np_connector_groups_index_column[shard_index],
                                    &gid,
                                    sizeof(gid),
                                    (void **) connector_group,
                                    &row_count,
                                    0,
                                    NULL,
                                    NULL,
                                    0);
    return res;
}

int
np_connector_groups_get_by_customer_gid_immediate(int64_t customer_gid,
                                                  struct np_connector_groups **connector_groups,
                                                  size_t *connector_groups_count)
{
    int res;

    int shard_index = ZPATH_SHARD_FROM_GID(customer_gid);

    res = wally_table_get_rows_fast(np_connector_groups_customer_gid_column[shard_index],
                                    &customer_gid,
                                    sizeof(customer_gid),
                                    (void **)connector_groups,
                                    connector_groups_count,
                                    0,
                                    NULL,
                                    NULL,
                                    0);
    return res;
}
