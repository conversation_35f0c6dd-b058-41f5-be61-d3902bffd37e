/*
 * np_bgp_connectors_config.c. Copyright (C) 2025 Zscaler Inc, All Rights Reserved
 */

#include "zhash/zhash_table.h"
#include "wally/wally.h"
#include "wally/wally_private.h"
#include "zpath_lib/zpath_app.h"
#include "zpath_lib/zpath_debug.h"
#include "np_lib/np_private.h"
#include "np_lib/np_bgp_connectors_config.h"
#include "np_lib/np_bgp_connectors_config_compiled.h"
#include "np_lib/np.h"

#define NP_BGP_MAX_CONNECTORS_PER_CUSTOMER 1000

struct argo_structure_description *np_bgp_connectors_config_description = NULL;

struct wally_index_column **np_bgp_connectors_config_gid_column = NULL;
struct wally_index_column **np_bgp_connectors_config_connector_gid_column = NULL;
struct wally_index_column **np_bgp_connectors_config_customer_gid_column = NULL;

static void
np_bgp_connectors_config_row_fixup(struct argo_object *row)
{
    // scope gid needs to be fixed when we support it in future
    return;
}

static int
np_bgp_connectors_config_row_callback(void *cookie,
                                      struct wally_registrant *registrant,
                                      struct wally_table *table,
                                      struct argo_object *previous_row,
                                      struct argo_object *row,
                                      int64_t request_id)
{
    if (IS_NP_DEBUG_BGP_INSTANCE()) {
        char dump[8000];
        if (argo_object_dump(row, dump, sizeof(dump), NULL, 0) == ARGO_RESULT_NO_ERROR) {
            NP_DEBUG_BGP_INSTANCE("np_bgp_connectors_config row callback: %s", dump);
        }
    }
    return NP_RESULT_NO_ERROR;
}

/* Gets in memory results, no wally registration */
static int
np_bgp_connectors_config_dump_by_customer(struct zpath_debug_state* request_state,
                                          const char** query_values,
                                          int query_value_count,
                                          void* cookie)
{
    int res;
    int64_t customer_gid = 0;
    struct np_bgp_connectors_config *bgp_conn[NP_BGP_MAX_CONNECTORS_PER_CUSTOMER];
    char jsonout[10000];
    size_t bgp_conn_count;
    size_t i;

    (void) query_value_count;
    (void) cookie;

    if (query_values[0]) {
        customer_gid = strtoul(query_values[0], NULL, 10);
    } else {
        ZDP("Customer gid is required!\n");
        return NP_RESULT_NO_ERROR;
    }

    bgp_conn_count = NP_BGP_MAX_CONNECTORS_PER_CUSTOMER;
    res = np_bgp_connectors_config_get_by_customer_gid_immediate(customer_gid, bgp_conn, &bgp_conn_count);
    if (res != NP_RESULT_NO_ERROR) {
        ZDP("couldn't query np_bgp_connectors_config table by customer_gid, wally returned (%s)\n", zpath_result_string(res));
        return NP_RESULT_NO_ERROR;
    }

    for (i = 0; i < bgp_conn_count; i++) {
        if (argo_structure_dump(np_bgp_connectors_config_description, bgp_conn[i], jsonout, sizeof(jsonout), NULL, 1) == ARGO_RESULT_NO_ERROR) {
            ZDP("%s\n", jsonout);
        }
    }
    if (bgp_conn_count) {
        ZDP("%zu matching BGP connector config in np_bgp_connectors_config table\n", bgp_conn_count);
    }

    return NP_RESULT_NO_ERROR;
}

int
np_bgp_connectors_config_table_init(struct wally *single_tenant_wally,
                                    int64_t tenant_id,
                                    wally_row_callback_f *all_rows_callback,
                                    void *all_rows_callback_cookie,
                                    int single_tenant_fully_loaded)
{
    static int initialized = 0;
    int res;

    if (initialized) return NP_RESULT_NO_ERROR;

    np_bgp_connectors_config_description = argo_register_global_structure(NP_BGP_CONNECTORS_CONFIG_HELPER);
    if (!np_bgp_connectors_config_description) {
        return NP_RESULT_ERR;
    }

    if (single_tenant_wally) {
        struct wally_table *table;
        int shard_index = ZPATH_SHARD_FROM_GID(tenant_id);

        np_bgp_connectors_config_gid_column = NP_CALLOC(ZPATH_MAX_SHARDS * sizeof(*np_bgp_connectors_config_gid_column));
        np_bgp_connectors_config_connector_gid_column = NP_CALLOC(ZPATH_MAX_SHARDS * sizeof(*np_bgp_connectors_config_connector_gid_column));
        np_bgp_connectors_config_customer_gid_column = NP_CALLOC(ZPATH_MAX_SHARDS * sizeof(*np_bgp_connectors_config_customer_gid_column));

        if (single_tenant_fully_loaded) {
            res = zpath_app_fully_loaded_customer_table(&table,
                                                        tenant_id,
                                                        single_tenant_wally,
                                                        np_bgp_connectors_config_description,
                                                        (all_rows_callback ? all_rows_callback : &np_bgp_connectors_config_row_callback),
                                                        all_rows_callback_cookie,
                                                        np_bgp_connectors_config_row_fixup,
                                                        0); // Do not register with zpath_table
            if (res) {
                NP_LOG(AL_ERROR, "Could not get np_bgp_connectors_config fully loaded");
                return NP_RESULT_ERR;
            }
        } else {
            table = wally_table_create(single_tenant_wally,
                                       1,
                                       np_bgp_connectors_config_description,
                                       (all_rows_callback ? all_rows_callback : &np_bgp_connectors_config_row_callback),
                                       all_rows_callback_cookie,
                                       1,
                                       0,
                                       np_bgp_connectors_config_row_fixup);
            if (!table) {
                NP_LOG(AL_ERROR, "Could not get np_bgp_connectors_config table");
                return NP_RESULT_ERR;
            }
        }

        np_bgp_connectors_config_gid_column[shard_index] = wally_table_get_index(table, "gid");
        if (!np_bgp_connectors_config_gid_column[shard_index]) {
            NP_LOG(AL_ERROR, "Could not get gid column from np_bgp_connectors_config table");
            return NP_RESULT_ERR;
        }

        np_bgp_connectors_config_connector_gid_column[shard_index] = wally_table_get_index(table, "connector_gid");
        if (!np_bgp_connectors_config_connector_gid_column[shard_index]) {
            NP_LOG(AL_ERROR, "Could not get connector gid column from np_bgp_connectors_config table");
            return NP_RESULT_ERR;
        }

        np_bgp_connectors_config_customer_gid_column[shard_index] = wally_table_get_index(table, "customer_gid");
        if (!np_bgp_connectors_config_customer_gid_column[shard_index]) {
            NP_LOG(AL_ERROR, "Could not get customer gid column from np_bgp_connectors_config table");
            return NP_RESULT_ERR;
        }
    } else {
        res = zpath_app_add_np_sharded_table(np_bgp_connectors_config_description,
                                             (all_rows_callback ? all_rows_callback : &np_bgp_connectors_config_row_callback),
                                             all_rows_callback_cookie,
                                             0,
                                             np_bgp_connectors_config_row_fixup);
        if (res) {
            return res;
        }

        np_bgp_connectors_config_gid_column = zpath_app_get_np_sharded_index("np_bgp_connectors_config", "gid");
        if (!np_bgp_connectors_config_gid_column) {
            NP_LOG(AL_ERROR, "Could not get gid column from np_bgp_connectors_config table");
            return NP_RESULT_ERR;
        }

        np_bgp_connectors_config_connector_gid_column = zpath_app_get_np_sharded_index("np_bgp_connectors_config", "connector_gid");
        if (!np_bgp_connectors_config_connector_gid_column) {
            NP_LOG(AL_ERROR, "Could not get connector gid column from np_bgp_connectors_config table");
            return NP_RESULT_ERR;
        }

        np_bgp_connectors_config_customer_gid_column = zpath_app_get_np_sharded_index("np_bgp_connectors_config", "customer_gid");
        if (!np_bgp_connectors_config_customer_gid_column) {
            NP_LOG(AL_ERROR, "Could not get customer gid column from np_bgp_connectors_config table");
            return NP_RESULT_ERR;
        }
    }

    res = zpath_debug_add_safe_read_command("Dump all connectors registered per customer.",
                                             "/np/bgp/connectors_config/customer_dump",
                                             np_bgp_connectors_config_dump_by_customer,
                                             NULL,
                                             "customer", "Required. Customer GID",
                                             NULL);
    if (res) {
        NP_LOG(AL_ERROR, "Couldn't add curl debug command /np/bgp/connectors_config/customer_dump");
        return res;
    }

    initialized = 1;

    return NP_RESULT_NO_ERROR;
}

int
np_bgp_connectors_config_get_by_customer_gid_immediate(int64_t customer_gid,
                                                       struct np_bgp_connectors_config **bgp_conn,
                                                       size_t *bgp_conn_count)
{
    int res;

    int shard_index = ZPATH_SHARD_FROM_GID(customer_gid);

    res = wally_table_get_rows_fast(np_bgp_connectors_config_customer_gid_column[shard_index],
                                    &customer_gid,
                                    sizeof(customer_gid),
                                    (void **)bgp_conn,
                                    bgp_conn_count,
                                    0,
                                    NULL,
                                    NULL,
                                    0);
    return res;
}

int
np_bgp_connectors_config_get_by_gid_immediate(int64_t gid, struct np_bgp_connectors_config **bgp_conn)
{
    int res;
    size_t row_count = 1;
    int shard_index = ZPATH_SHARD_FROM_GID(gid);

    if (!np_bgp_connectors_config_gid_column) return NP_RESULT_NOT_READY;

    res = wally_table_get_rows_fast(np_bgp_connectors_config_gid_column[shard_index],
                                    &gid,
                                    sizeof(gid),
                                    (void **)bgp_conn,
                                    &row_count,
                                    0,
                                    NULL,
                                    NULL,
                                    0);
    return res;
}


int
np_bgp_connectors_config_get_by_connector_gid(int64_t connector_gid,
                                              struct np_bgp_connectors_config **bgp_conn,
                                              wally_response_callback_f callback_f,
                                              void *callback_cookie,
                                              int64_t callback_id)
{
    int res;
    size_t row_count = 1;
    int shard_index = ZPATH_SHARD_FROM_GID(connector_gid);
    struct wally_index_column *column;

    if (!np_bgp_connectors_config_connector_gid_column) return NP_RESULT_NOT_READY;

    column = np_bgp_connectors_config_connector_gid_column[shard_index];
    res = wally_table_get_rows_fast(np_bgp_connectors_config_connector_gid_column[shard_index],
                                    &connector_gid,
                                    sizeof(connector_gid),
                                    (void **)bgp_conn,
                                    &row_count,
                                    1,  /* Register on miss! Next time we should have it... */
                                    callback_f,
                                    callback_cookie,
                                    callback_id);

    if ((res == WALLY_RESULT_NOT_FOUND) &&
        (column && (wally_origin_status_not_ready == wally_table_get_origin_status(column->table, 1)))) {
        res = WALLY_RESULT_NOT_READY;
    }

    return res;
}
