/*
 * np_connector_groups.h. Copyright (C) 2024 Zscaler Inc, All Rights Reserved
 */

#ifndef _NP_CONNECTOR_GROUPS_H_
#define _NP_CONNECTOR_GROUPS_H_

#include "argo/argo.h"

struct np_connector_groups                     /* _ARGO: object_definition */
{
    /* Standard SQL fields. */
    int64_t gid;                               /* _ARGO: integer, index, key */
    int64_t sequence;                          /* _ARGO: integer, sequence */
    int64_t deleted;                           /* _ARGO: integer, deleted */
    int64_t modified_time;                     /* _ARGO: integer */
    int64_t creation_time;                     /* _ARGO: integer */
    int64_t modifiedby_userid;                 /* _ARGO: integer */

    int64_t customer_gid;                      /* _ARGO: integer, index */
    int64_t assistant_group_gid;               /* _ARGO: integer, index */
    int mtu;                                   /* _ARGO: integer */
    int redundant_mode_enabled;                /* _ARGO: integer */
    int advertise_lan_segments_disabled;       /* _ARGO: integer */
};

/*
 * Must be called after np_init()
 * set single_tenant_wally + tenant_id only if single tenant.
 * NOTE: NOT fully loaded, registration should only happen on one column!
 */
int
np_connector_groups_table_init(struct wally *single_tenant_wally,
                               int64_t tenant_id,
                               wally_row_callback_f *all_rows_callback,
                               void *all_rows_callback_cookie,
                               int single_tenant_fully_loaded);

/*
 * Get a np connector group given GID.
 * Returns in-memory result, or failure.
 */
int
np_connector_groups_get_by_id_immediate(int64_t gid,
                                        struct np_connector_groups **connector);

/*
 * Get all np connector groups for a given customer. This only returns what is in memory.
 */
int
np_connector_groups_get_by_customer_gid_immediate(int64_t customer_gid,
                                                  struct np_connector_groups **connector_groups,
                                                  size_t *connector_groups_count);

/*
 * Register np_connector_groups table by gid and get all rows matching with group_gid
 * This routine will block until all rows are read, because we need to get the group level configuration before
 * proceeding further
 */
int
np_connector_groups_register_by_connector_group_gid(int64_t group_gid);

#endif /* _NP_CONNECTOR_GROUPS_H_ */
