/*
 * np_private.h. Copyright (C) 2024 Zscaler, Inc. All Rights Reserved
 */

#ifndef __NP_PRIVATE_H__
#define __NP_PRIVATE_H__

#include "argo/argo.h"
#include "argo/argo_log.h"
#include "zpath_lib/zpath_lib.h"
#include "zpath_misc/zpath_misc.h"

/*
 * Return error codes. Taken directly from argo.
 */
#define NP_RESULT_NO_ERROR          ARGO_RESULT_NO_ERROR          /* AKA success */
#define NP_RESULT_ERR               ARGO_RESULT_ERR               /* Generic error, when none other are appropriate */
#define NP_RESULT_NOT_FOUND         ARGO_RESULT_NOT_FOUND         /* Could not find what was requested. */
#define NP_RESULT_NO_MEMORY         ARGO_RESULT_NO_MEMORY         /* Could not allocate memory */
#define NP_RESULT_CANT_WRITE        ARGO_RESULT_CANT_WRITE        /* Failure to write (output callback failed?) */
#define NP_RESULT_ERR_TOO_LARGE     ARGO_RESULT_ERR_TOO_LARGE     /* Requested data doesn't fit in space provided */
#define NP_RESULT_BAD_ARGUMENT      ARGO_RESULT_BAD_ARGUMENT      /* Asked for something wrong. */
#define NP_RESULT_INSUFFICIENT_DATA ARGO_RESULT_INSUFFICIENT_DATA /* Was not provided enough data to perform operation */
#define NP_RESULT_NOT_IMPLEMENTED   ARGO_RESULT_NOT_IMPLEMENTED   /* Yes, for features that are not yet implemented. */
#define NP_RESULT_BAD_DATA          ARGO_RESULT_BAD_DATA          /* Tried to parse data, but format seemed wrong. */
#define NP_RESULT_WOULD_BLOCK       ARGO_RESULT_WOULD_BLOCK       /* Attempting operation would result in blocking. Bad, naughty blocking. */
#define NP_RESULT_BAD_STATE         ARGO_RESULT_BAD_STATE         /* Encountered bad internal state while attempting operation */
#define NP_RESULT_INCOMPLETE        ARGO_RESULT_INCOMPLETE
#define NP_RESULT_ASYNCHRONOUS      ARGO_RESULT_ASYNCHRONOUS      /* Asynchronous means the request will still complete, but at a
                                                                   * later time, with a callback. As opposed to would_block, which
                                                                   * did not do the job, nor will it. */
#define NP_RESULT_EXCESS_DYN_FIELDS ARGO_RESULT_EXCESS_DYN_FIELDS /* The RPC has too many dynamic fields */
#define NP_RESULT_NOT_READY         (ARGO_RESULT_MAX+1)           /* Requested DB is not ready. */
#define NP_RESULT_MAX               NP_RESULT_NOT_READY


/* NP Wally debug info */
extern uint64_t np_wally_debug;

#define NP_DEBUG_CLIENT_SUBNETS_BIT             BIT_FLAG_U64(0)
#define NP_DEBUG_DNS_NS_RECORDS_BIT             BIT_FLAG_U64(1)
#define NP_DEBUG_LAN_SUBNETS_BIT                BIT_FLAG_U64(2)
#define NP_DEBUG_CLIENTS_BIT                    BIT_FLAG_U64(3)
#define NP_DEBUG_CONNECTORS_BIT                 BIT_FLAG_U64(4)
#define NP_DEBUG_CONNECTOR_GROUPS_BIT           BIT_FLAG_U64(5)
#define NP_DEBUG_GATEWAYS_BIT                   BIT_FLAG_U64(6)
#define NP_DEBUG_TENANT_GATEWAYS_BIT            BIT_FLAG_U64(7)
#define NP_DEBUG_BGP_INSTANCE_BIT               BIT_FLAG_U64(8)
#define NP_DEBUG_BGP_SESSION_BIT                BIT_FLAG_U64(9)
#define NP_DEBUG_BGP_CONFIG_BIT                 BIT_FLAG_U64(10)

#define NP_WALLY_DEBUG_NAMES { \
        "client_subnets",      \
        "dns_ns_records",      \
        "lan_subnets",         \
        "clients",             \
        "connectors",          \
        "connector_groups",    \
        "gateways",            \
        "tenant_gateways",     \
        "bgp_instance",        \
        "bgp_session",         \
        "bgp_config",          \
        NULL                   \
        }

#define IS_NP_DEBUG_CLIENT_SUBNETS()            (np_wally_debug & NP_DEBUG_CLIENT_SUBNETS_BIT)
#define IS_NP_DEBUG_DNS_NS_RECORDS()            (np_wally_debug & NP_DEBUG_DNS_NS_RECORDS_BIT)
#define IS_NP_DEBUG_LAN_SUBNETS()               (np_wally_debug & NP_DEBUG_LAN_SUBNETS_BIT)
#define IS_NP_DEBUG_CLIENTS()                   (np_wally_debug & NP_DEBUG_CLIENTS_BIT)
#define IS_NP_DEBUG_CONNECTORS()                (np_wally_debug & NP_DEBUG_CONNECTORS_BIT)
#define IS_NP_DEBUG_CONNECTOR_GROUPS()          (np_wally_debug & NP_DEBUG_CONNECTOR_GROUPS_BIT)
#define IS_NP_DEBUG_GATEWAYS()                  (np_wally_debug & NP_DEBUG_GATEWAYS_BIT)
#define IS_NP_DEBUG_TENANT_GATEWAYS()           (np_wally_debug & NP_DEBUG_TENANT_GATEWAYS_BIT)
#define IS_NP_DEBUG_BGP_INSTANCE()              (np_wally_debug & NP_DEBUG_BGP_INSTANCE_BIT)
#define IS_NP_DEBUG_BGP_SESSION()               (np_wally_debug & NP_DEBUG_BGP_SESSION_BIT)
#define IS_NP_DEBUG_BGP_CONFIG()                (np_wally_debug & NP_DEBUG_BGP_CONFIG_BIT)

#define NP_DEBUG_CLIENT_SUBNETS(format...)      NP_DEBUG_LOG(IS_NP_DEBUG_CLIENT_SUBNETS(), ##format)
#define NP_DEBUG_DNS_NS_RECORDS(format...)      NP_DEBUG_LOG(IS_NP_DEBUG_DNS_NS_RECORDS(), ##format)
#define NP_DEBUG_LAN_SUBNETS(format...)         NP_DEBUG_LOG(IS_NP_DEBUG_LAN_SUBNETS(), ##format)
#define NP_DEBUG_CLIENTS(format...)             NP_DEBUG_LOG(IS_NP_DEBUG_CLIENTS(), ##format)
#define NP_DEBUG_CONNECTORS(format...)          NP_DEBUG_LOG(IS_NP_DEBUG_CONNECTORS(), ##format)
#define NP_DEBUG_CONNECTOR_GROUPS(format...)    NP_DEBUG_LOG(IS_NP_DEBUG_CONNECTOR_GROUPS(), ##format)
#define NP_DEBUG_GATEWAYS(format...)            NP_DEBUG_LOG(IS_NP_DEBUG_GATEWAYS(), ##format)
#define NP_DEBUG_TENANT_GATEWAYS(format...)     NP_DEBUG_LOG(IS_NP_DEBUG_TENANT_GATEWAYS(), ##format)
#define NP_DEBUG_BGP_INSTANCE(format...)        NP_DEBUG_LOG(IS_NP_DEBUG_BGP_INSTANCE(), ##format)
#define NP_DEBUG_BGP_SESSION(format...)         NP_DEBUG_LOG(IS_NP_DEBUG_BGP_SESSION(), ##format)
#define NP_DEBUG_BGP_CONFIG(format...)          NP_DEBUG_LOG(IS_NP_DEBUG_BGP_CONFIG(), ##format)

#endif /* __NP_PRIVATE_H__ */
