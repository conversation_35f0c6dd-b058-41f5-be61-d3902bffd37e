/*
* np_connector_groups_lan_subnets_mapping.c. Copyright (C) 2025 Zscaler Inc, All Rights Reserved
*/

#include "zhash/zhash_table.h"
#include "wally/wally.h"
#include "zpath_lib/zpath_app.h"
#include "zpath_lib/zpath_debug.h"
#include "np_lib/np_private.h"
#include "np_lib/np_connector_groups_lan_subnets_mapping.h"
#include "np_lib/np_connector_groups_lan_subnets_mapping_compiled.h"
#include "np_lib/np.h"

struct argo_structure_description *np_connector_groups_lan_subnets_mapping_description = NULL;

struct wally_index_column **np_connector_groups_lan_subnets_mapping_customer_gid_column = NULL;
struct wally_index_column **np_connector_groups_lan_subnets_mapping_connector_group_gid_column = NULL;
struct wally_index_column **np_connector_groups_lan_subnets_mapping_lan_subnet_gid_column = NULL;

static void
np_connector_groups_lan_subnets_mapping_row_fixup(struct argo_object *row)
{
    // scope gid needs to be fixed when we support it in future
    return;
}

static int
np_connector_groups_lan_subnets_mapping_row_callback(void *cookie,
                                                     struct wally_registrant *registrant,
                                                     struct wally_table *table,
                                                     struct argo_object *previous_row,
                                                     struct argo_object *row,
                                                     int64_t request_id)
{
    if (IS_NP_DEBUG_CONNECTOR_GROUPS()) {
        char dump[8000];
        if (argo_object_dump(row, dump, sizeof(dump), NULL, 0) == ARGO_RESULT_NO_ERROR) {
            NP_DEBUG_CONNECTOR_GROUPS("np_connector_groups_lan_subnets_mapping row callback: %s", dump);
        }
    } else if (IS_NP_DEBUG_LAN_SUBNETS()) {
        char dump[8000];
        if (argo_object_dump(row, dump, sizeof(dump), NULL, 0) == ARGO_RESULT_NO_ERROR) {
            NP_DEBUG_LAN_SUBNETS("np_connector_groups_lan_subnets_mapping row callback: %s", dump);
        }
    }

    return NP_RESULT_NO_ERROR;
}

/* Gets in memory results, no wally registration */
static int
np_connector_groups_lan_subnets_mapping_dump_by_customer(struct zpath_debug_state* request_state,
                                                         const char** query_values,
                                                         int query_value_count,
                                                         void* cookie)
{
    int res;
    int64_t customer_gid = 0;
    struct np_connector_groups_lan_subnets_mapping *mapping[100];
    char jsonout[10000];
    size_t mapping_count = 100;
    size_t i;

    (void) query_value_count;
    (void) cookie;

    if (query_values[0]) {
        customer_gid = strtoul(query_values[0], NULL, 10);
    } else {
        ZDP("Customer gid is required!\n");
        return NP_RESULT_NO_ERROR;
    }

    res = np_connector_groups_lan_subnets_mapping_get_by_customer_gid_immediate(customer_gid, mapping, &mapping_count);
    if (res != NP_RESULT_NO_ERROR) {
        ZDP("couldn't query np_connector_groups_lan_subnets_mapping table by customer_gid, wally returned (%s)\n", zpath_result_string(res));
        return NP_RESULT_NO_ERROR;
    }

    for (i = 0; i < mapping_count; i++) {
        if (argo_structure_dump(np_connector_groups_lan_subnets_mapping_description, mapping[i], jsonout, sizeof(jsonout), NULL, 1) == ARGO_RESULT_NO_ERROR) {
            ZDP("%s\n", jsonout);
        }
    }
    if (mapping_count) {
        ZDP("%zu matching BGP  config in np_connector_groups_lan_subnets_mapping table\n", mapping_count);
    }

    return NP_RESULT_NO_ERROR;
}


static int
np_connector_groups_table_response_callback(void *cookie,
                                            struct wally_registrant *registrant,
                                            struct wally_table *table,
                                            int64_t sequence,
                                            int row_count)
{
    struct wally_interlock *lock = (struct wally_interlock *)cookie;
    if (lock) {
        wally_interlock_release(lock);
    }
    NP_LOG(AL_INFO, "Loaded %d rows from np_connector_groups_lan_subnets_mapping", row_count);

    return WALLY_RESULT_NO_ERROR;
}

static inline int wally_table_register_by_group_gid(int64_t group_gid, struct wally_interlock *lock)
{
    int shard_index = ZPATH_SHARD_FROM_GID(group_gid);
    int res = wally_table_register_for_row(NULL, np_connector_groups_lan_subnets_mapping_connector_group_gid_column[shard_index],
                &group_gid, sizeof(group_gid), 0, 0, REQUEST_AT_LEAST_ONE, 0, 0, np_connector_groups_table_response_callback, lock);
    if (res == WALLY_RESULT_NOT_READY) {
        wally_table_deregister_for_row(NULL, np_connector_groups_lan_subnets_mapping_connector_group_gid_column[shard_index], NULL, 0);
    }
    return res;
}

int
np_connector_groups_lan_subnets_mapping_register_by_connector_group_gid(int64_t group_gid)
{
    struct wally_interlock lock;
    int not_ready_count = 0;
    int res = ZPATH_RESULT_NO_ERROR;

    wally_interlock_lock_1(&lock);
    while ((res = wally_table_register_by_group_gid(group_gid, &lock)) == WALLY_RESULT_NOT_READY) {
        if ((not_ready_count++ % 10) == 0) {
            NP_LOG(AL_NOTICE, "Wally is not ready for registering np_connector_groups_lan_subnets_mapping table");
        }
        /* Heartbeat so we don't crash when wally connection is down */
        zthread_heartbeat(NULL);
        sleep(1);
    }
    if (res != WALLY_RESULT_NO_ERROR && res != WALLY_RESULT_ASYNCHRONOUS) {
        NP_LOG(AL_ERROR, "Failed to register wally table for row: %s",  zpath_result_string(res));
        return res;
    }

    NP_LOG(AL_NOTICE, "Registering np_connector_groups_lan_subnets_mapping table with group_gid %"PRId64"", group_gid);
    /* Deadlock until our callback releases this lock */
    wally_interlock_lock_2(&lock);
    NP_LOG(AL_NOTICE, "Registering np_connector_groups_lan_subnets_mapping table with group_gid %"PRId64"... Complete", group_gid);

    return NP_RESULT_NO_ERROR;
}

int
np_connector_groups_lan_subnets_mapping_table_init(struct wally *single_tenant_wally,
                                                   int64_t tenant_id,
                                                   wally_row_callback_f *all_rows_callback,
                                                   void *all_rows_callback_cookie,
                                                   int single_tenant_fully_loaded)
{
    int res;

    np_connector_groups_lan_subnets_mapping_description = argo_register_global_structure(NP_CONNECTOR_GROUPS_LAN_SUBNETS_MAPPING_HELPER);
    if (!np_connector_groups_lan_subnets_mapping_description) {
        return NP_RESULT_ERR;
    }

    if (single_tenant_wally) {
        struct wally_table *table;
        int shard_index = ZPATH_SHARD_FROM_GID(tenant_id);

        np_connector_groups_lan_subnets_mapping_connector_group_gid_column = NP_CALLOC(ZPATH_MAX_SHARDS * sizeof(*np_connector_groups_lan_subnets_mapping_connector_group_gid_column));
        np_connector_groups_lan_subnets_mapping_lan_subnet_gid_column = NP_CALLOC(ZPATH_MAX_SHARDS * sizeof(*np_connector_groups_lan_subnets_mapping_lan_subnet_gid_column));
        np_connector_groups_lan_subnets_mapping_customer_gid_column = NP_CALLOC(ZPATH_MAX_SHARDS * sizeof(*np_connector_groups_lan_subnets_mapping_customer_gid_column));

        if (single_tenant_fully_loaded) {
            res = zpath_app_fully_loaded_customer_table(&table,
                                                        tenant_id,
                                                        single_tenant_wally,
                                                        np_connector_groups_lan_subnets_mapping_description,
                                                        (all_rows_callback ? all_rows_callback : &np_connector_groups_lan_subnets_mapping_row_callback),
                                                        all_rows_callback_cookie,
                                                        np_connector_groups_lan_subnets_mapping_row_fixup,
                                                        0); // Do not register with zpath_table
            if (res) {
                NP_LOG(AL_ERROR, "Could not get np_connector_groups_lan_subnets_mapping fully loaded");
                return NP_RESULT_ERR;
            }
        } else {
            table = wally_table_create(single_tenant_wally,
                                       1,
                                       np_connector_groups_lan_subnets_mapping_description,
                                       (all_rows_callback ? all_rows_callback : &np_connector_groups_lan_subnets_mapping_row_callback),
                                       all_rows_callback_cookie,
                                       1,
                                       0,
                                       np_connector_groups_lan_subnets_mapping_row_fixup);
            if (!table) {
                NP_LOG(AL_ERROR, "Could not get np_connector_groups_lan_subnets_mapping table");
                return NP_RESULT_ERR;
            }
        }

        np_connector_groups_lan_subnets_mapping_connector_group_gid_column[shard_index] = wally_table_get_index(table, "connector_group_gid");
        if (!np_connector_groups_lan_subnets_mapping_connector_group_gid_column[shard_index]) {
            NP_LOG(AL_ERROR, "Could not get connector_group_gid column from np_connector_groups_lan_subnets_mapping table");
            return NP_RESULT_ERR;
        }

        np_connector_groups_lan_subnets_mapping_lan_subnet_gid_column[shard_index] = wally_table_get_index(table, "lan_subnet_gid");
        if (!np_connector_groups_lan_subnets_mapping_lan_subnet_gid_column[shard_index]) {
            NP_LOG(AL_ERROR, "Could not get lan_subnet_gid column from np_connector_groups_lan_subnets_mapping table");
            return NP_RESULT_ERR;
        }

        np_connector_groups_lan_subnets_mapping_customer_gid_column[shard_index] = wally_table_get_index(table, "customer_gid");
        if (!np_connector_groups_lan_subnets_mapping_customer_gid_column[shard_index]) {
            NP_LOG(AL_ERROR, "Could not get customer_gid column from np_connector_groups_lan_subnets_mapping table");
            return NP_RESULT_ERR;
        }
    } else {
        res = zpath_app_add_np_sharded_table(np_connector_groups_lan_subnets_mapping_description,
                                             (all_rows_callback ? all_rows_callback : &np_connector_groups_lan_subnets_mapping_row_callback),
                                             all_rows_callback_cookie,
                                             0,
                                             np_connector_groups_lan_subnets_mapping_row_fixup);
        if (res) {
            return res;
        }

        np_connector_groups_lan_subnets_mapping_connector_group_gid_column = zpath_app_get_np_sharded_index("np_connector_groups_lan_subnets_mapping", "connector_group_gid");
        if (!np_connector_groups_lan_subnets_mapping_connector_group_gid_column) {
            NP_LOG(AL_ERROR, "Could not get connector_group_gid column from np_connector_groups_lan_subnets_mapping table");
            return NP_RESULT_ERR;
        }

        np_connector_groups_lan_subnets_mapping_lan_subnet_gid_column = zpath_app_get_np_sharded_index("np_connector_groups_lan_subnets_mapping", "lan_subnet_gid");
        if (!np_connector_groups_lan_subnets_mapping_lan_subnet_gid_column) {
            NP_LOG(AL_ERROR, "Could not get lan_subnet_gid column from np_connector_groups_lan_subnets_mapping table");
            return NP_RESULT_ERR;
        }

        np_connector_groups_lan_subnets_mapping_customer_gid_column = zpath_app_get_np_sharded_index("np_connector_groups_lan_subnets_mapping", "customer_gid");
        if (!np_connector_groups_lan_subnets_mapping_customer_gid_column) {
            NP_LOG(AL_ERROR, "Could not get customer_gid column from np_connector_groups_lan_subnets_mapping table");
            return NP_RESULT_ERR;
        }
    }

    res = zpath_debug_add_safe_read_command("Dump all connector group to lan subnet mappings registered per customer.",
                                            "/np/connector_groups_lan_subnets_mapping/customer_dump",
                                            np_connector_groups_lan_subnets_mapping_dump_by_customer,
                                            NULL,
                                            "customer", "Required. Customer GID",
                                            NULL);
    if (res) {
        NP_LOG(AL_ERROR, "Couldn't add curl debug command /np/connector_groups_lan_subnets_mapping/customer_dump");
        return res;
    }

    return NP_RESULT_NO_ERROR;
}

int
np_connector_groups_lan_subnets_mapping_get_by_customer_gid_immediate(int64_t customer_gid,
                                                                      struct np_connector_groups_lan_subnets_mapping **mapping,
                                                                      size_t *mapping_count)
{
    int res;

    int shard_index = ZPATH_SHARD_FROM_GID(customer_gid);

    res = wally_table_get_rows_fast(np_connector_groups_lan_subnets_mapping_customer_gid_column[shard_index],
                                    &customer_gid,
                                    sizeof(customer_gid),
                                    (void **)mapping,
                                    mapping_count,
                                    0,
                                    NULL,
                                    NULL,
                                    0);
    return res;
}

int
np_connector_groups_lan_subnets_mapping_get_subnets_by_group_gid_immediate(int64_t connector_group_gid,
                                                                           int64_t *lan_subnet_gids,
                                                                           size_t *lan_subnets_count)
{
    int res;
    int shard_index = ZPATH_SHARD_FROM_GID(connector_group_gid);
    struct np_connector_groups_lan_subnets_mapping *rows[NP_CONNECTOR_GROUPS_LAN_SUBNETS_MAPPING_MAX_COUNT];
    size_t row_count = sizeof(rows) / sizeof(rows[0]);
    if (row_count > (*lan_subnets_count)) {
        row_count = *lan_subnets_count;
    }
    size_t i;

    if (!np_connector_groups_lan_subnets_mapping_connector_group_gid_column) return NP_RESULT_NOT_READY;

    res = wally_table_get_rows_fast(np_connector_groups_lan_subnets_mapping_connector_group_gid_column[shard_index],
                                    &connector_group_gid,
                                    sizeof(connector_group_gid),
                                    (void **)&(rows[0]),
                                    &row_count,
                                    0,
                                    NULL,
                                    NULL,
                                    0);

    if (res) {
        *lan_subnets_count = 0;
        return res;
    }

    for (i = 0; i < row_count; i++) {
        lan_subnet_gids[i] = rows[i]->lan_subnet_gid;
    }
    *lan_subnets_count = row_count;

    return res;
}

int
np_connector_groups_lan_subnets_mapping_get_groups_by_subnet_gid_immediate(int64_t lan_subnet_gid,
                                                                           int64_t *connector_group_gids,
                                                                           size_t *connector_groups_count)
{
    int res;
    int shard_index = ZPATH_SHARD_FROM_GID(lan_subnet_gid);
    struct np_connector_groups_lan_subnets_mapping *rows[NP_CONNECTOR_GROUPS_LAN_SUBNETS_MAPPING_MAX_COUNT];
    size_t row_count = sizeof(rows) / sizeof(rows[0]);
    if (row_count > (*connector_groups_count)) {
        row_count = *connector_groups_count;
    }
    size_t i;

    if (!np_connector_groups_lan_subnets_mapping_lan_subnet_gid_column) return NP_RESULT_NOT_READY;

    res = wally_table_get_rows_fast(np_connector_groups_lan_subnets_mapping_lan_subnet_gid_column[shard_index],
                                    &lan_subnet_gid,
                                    sizeof(lan_subnet_gid),
                                    (void **)&(rows[0]),
                                    &row_count,
                                    0,
                                    NULL,
                                    NULL,
                                    0);

    if (res) {
        *connector_groups_count = 0;
        return res;
    }

    for (i = 0; i < row_count; i++) {
        connector_group_gids[i] = rows[i]->connector_group_gid;
    }
    *connector_groups_count = row_count;

    return res;
}
