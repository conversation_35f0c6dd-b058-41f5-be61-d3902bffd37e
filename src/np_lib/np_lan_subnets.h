/*
 * np_lan_subnets.h. Copyright (C) 2024 Zscaler Inc, All Rights Reserved
 */

#ifndef _NP_LAN_SUBNETS_H_
#define _NP_LAN_SUBNETS_H_

#include "argo/argo.h"

extern struct wally_index_column **np_lan_subnets_customer_gid_column;

extern struct argo_structure_description *np_lan_subnets_description;

//FIXME confirm with CP team
#define NETWORK_SEGMENT_MAX_LAN_SUBNETS 1024

struct np_lan_subnets                          /* _ARGO: object_definition */
{
    /* Standard SQL fields. */
    int64_t gid;                               /* _ARGO: integer, index, key */
    int64_t sequence;                          /* _ARGO: integer, sequence */
    int64_t deleted;                           /* _ARGO: integer, deleted */
    int64_t modified_time;                     /* _ARGO: integer */
    int64_t creation_time;                     /* _ARGO: integer */
    int64_t modifiedby_userid;                 /* _ARGO: integer */

    int64_t customer_gid;                      /* _ARGO: integer, index */
    int64_t connector_group_gid;               /* _ARGO: integer, index */
    const struct argo_inet *subnet;            /* _ARGO: inet */
    struct argo_inet **lan_subnets;            /* _ARGO: inet */
    int lan_subnets_count;                     /* _ARGO: quiet, integer, count: lan_subnets */
};


/*
 * Must be called after np_init()
 * set single_tenant_wally + tenant_id only if single tenant.
 * NOTE: NOT fully loaded, registration should only happen on one column!
 */
int
np_lan_subnets_table_init(struct wally *single_tenant_wally,
                          int64_t tenant_id,
                          wally_row_callback_f *all_rows_callback,
                          void *all_rows_callback_cookie,
                          int single_tenant_fully_loaded);

/* load the lan subnet config per customer */
int
np_lan_subnets_load(int64_t customer_gid,
                    wally_response_callback_f callback_f,
                    void *callback_cookie,
                    int64_t callback_id);

/*
 * Get a np lan subnet given a lan subnet ID.
 * Returns in-memory result, or failure.
 */
int
np_lan_subnets_get_by_id_immediate(int64_t subnet_id,
                                   struct np_lan_subnets **subnet);

/*
 * Get all np lan subnets for a given customer. This only returns what is in memory.
 */
int
np_lan_subnets_get_by_customer_gid_immediate(int64_t customer_gid,
                                             struct np_lan_subnets **subnets,
                                             size_t *subnets_count);

/*
 * Get all np lan subnets for a given connector group gid. This only returns what is in memory.
 */
int
np_lan_subnets_get_by_connector_group_gid_immediate(int64_t connector_group_gid,
                                                    struct np_lan_subnets **subnets,
                                                    size_t *subnets_count);


/*
 * This hash table based implementation (np_lan_subnet_find_lan_subnets_delta_hash_table) is chosen for finding LAN subnet deltas
 * over a nested loop approach due to better performance in less overlapping test case as demonstrated by unit tests.
 *
 * For a detailed performance comparison,
 * please see the tests in test_np_lan_subnets_cmp_performance.c.
 */
void np_lan_subnet_find_lan_subnets_delta(const struct argo_inet **new_subnet, int new_subnet_count,
                                          const struct argo_inet **old_subnet, int old_subnet_count,
                                          struct argo_inet **ips_to_add, int ips_to_add_len, int *ips_to_add_count,
                                          struct argo_inet **ips_to_delete,int ips_to_delete_len, int *ips_to_delete_count,
                                          int64_t old_subnet_gid, int64_t new_subnet_gid);

int np_lan_subnet_find_lan_subnets_delta_hash_table(const struct argo_inet **new_subnet, int new_subnet_count,
                                                    const struct argo_inet **old_subnet, int old_subnet_count,
                                                    struct argo_inet **ips_to_add, int ips_to_add_len, int *ips_to_add_count,
                                                    struct argo_inet **ips_to_delete, int ips_to_delete_len, int *ips_to_delete_count,
                                                    int64_t old_subnet_gid, int64_t new_subnet_gid);
#endif /* _NP_LAN_SUBNETS_H_ */
