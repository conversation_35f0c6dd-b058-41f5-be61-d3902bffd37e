/*
 * np_bgp.h. Copyright (C) 2025 Zscaler Inc, All Rights Reserved
 *
 */

#ifndef _NP_BGP_H_
#define _NP_BGP_H_

#ifdef __cplusplus
extern "C" {
#endif

#define NP_BGP_THREAD "np_bgp_thread"
#define NP_BGP_THREAD_POOL "np_bgp"

/*
 * Maximum time each frr timer is allowed to be delayed, in seconds
 */
#define MAX_TIMER_DELAY_S 60

enum np_bgp_instance_type {
    np_bgp_instance_none = 0,
    np_bgp_instance_gateway = 1,
    np_bgp_instance_connector = 2,
    np_bgp_instance_router = 3,
};

enum np_bgp_config_mode {
    np_bgp_config_mode_generated = 0,
    np_bgp_config_mode_override = 1
};

struct np_bgp_neighbor_ip_to_gid_mapping {
    int64_t bgp_config_gid;
    enum np_bgp_instance_type inst_type;
    int64_t neighbor_asn;
};

/* In memory BGP state */
struct np_bgp_state {

    zpath_mutex_t lock;

    struct zevent_base *zevent_base;

    /* If the timer exists, then we are delaying frr config instantiation until the timer fires */
    struct event *frr_timer;

    int64_t customer_gid;

    enum np_bgp_instance_type instance_type;

    /* gid in np_bgp_[connectors|gateways]_config table */
    int64_t bgp_config_gid;

    char *frr_version;

    char *router_id;
    struct argo_inet ip;
    int64_t asn;

    /* Gateway only, default value: NPWG_VRF_NAME */
    char *vrf_name;

    /* Gateway only, interface name for NP client peers.
     * Default value: NP_WG_INTERFACE_NAME */
    char *wg_interface_name;

    enum np_bgp_config_mode config_mode;
    char *override_config;
    int force_reload_config;

    /* connector specific config */
    int disable_local_sourcing;

    struct zhash_table *peer_gateways;
    int64_t peer_gateways_count;

    struct zhash_table *peer_routers;
    int64_t peer_routers_count;

    /* gateway specific config */
    struct zhash_table *peer_connectors;
    int64_t peer_connectors_count;

    struct zhash_table *client_subnets;
    int64_t client_subnets_count;

    struct zhash_table *lan_subnets;
    int64_t lan_subnets_count;

    struct zhash_table *peer_ip_to_config_gid_mapping;
    int64_t peer_ip_to_config_gid_mapping_count;

    uint8_t deletion_in_progress:1;
};

const char *np_bgp_instance_type_str(int result);

/*
 * Create a BGP state, each BGP instance should have its own state
 *
 * Connector's in memory BGP state:
 *      instance:   asn, ip, router_id, disable_local_sourcing
 *      peers:      gateways, routers
 *      subnets:    client subnets, lan subnets
 * Gateway's in memory BGP state:
 *      instance:   asn, ip, router_id
 *      peers:      connectors
 *      subnets:    client subnets
 */
struct np_bgp_state *np_bgp_state_create(int64_t customer_gid, enum np_bgp_instance_type instance_type);

/*
 * Makes thread call to np bgp thread for state deletion
 */
int np_bgp_state_destroy(struct np_bgp_state *bgp_state);

void np_bgp_set_local_sourcing(struct np_bgp_state *bgp_state, int advertise_lan_segments_disabled);

int np_bgp_update_instance_config(struct np_bgp_state       *bgp_state,
                                  int64_t                   bgp_config_gid,
                                  int64_t                   asn,
                                  struct argo_inet          *router_id_inet,
                                  const char                *config_mode_str,
                                  const char                *override_config,
                                  int                       force_reload_config);

int np_bgp_reload_frr_conf(struct np_bgp_state *bgp_state, enum np_bgp_instance_type inst_type, int force_regenerate);

int np_bgp_gateway_add_or_update_peer_config(struct np_bgp_state *bgp_state, void *config, const char *interface);

int np_bgp_gateway_delete_peer_config(struct np_bgp_state *bgp_state, void *config);

int np_bgp_connector_add_or_update_peer_config(struct np_bgp_state *bgp_state, void *config);

int np_bgp_connector_delete_peer_config(struct np_bgp_state *bgp_state, void *config);

int np_bgp_peer_ip_to_instance_gid_lookup(struct np_bgp_state *bgp_state, const char *bgp_peer_ip_str,
                                 int64_t bgp_peer_asn, int64_t *peer_inst_gid, int *peer_inst_type);

int np_bgp_add_subnet_config(struct np_bgp_state *bgp_state, const struct argo_inet *inet, int is_lan_subnet);

int np_bgp_delete_subnet_config(struct np_bgp_state *bgp_state, const struct argo_inet *inet, int is_lan_subnet);

int np_bgp_get_config_mode(struct np_bgp_state *bgp_state);

int np_bgp_init();

#ifdef __cplusplus
} // extern "C"
#endif

#endif /* _NP_BGP_H_ */
