/*
 * zcrypt_meta.h. Copyright(C) 2016 Zscaler Inc. All Rights Reserved
 */

#ifndef _ZCRYPT_META_H
#define _ZCRYPT_META_H

#include "zcrypt/zcrypt.h"

#define ZCRYPT_VERSION_CHECKER_DEFAULT_FILENAME "image.bin"
#define ZCRYPT_VERSION_CHECKER_DEFAULT_METADATA "metadata"

#define ZCRYPT_ZPA_DEVELOP_ENV         "ZPA_DEVELOP_ENV"
#define ZCRYPT_ZPA_DISABLE_GEOIP       "ZPA_DISABLE_GEOIP"

enum zcrypt_metadata_develop_mode {
    zcrypt_metadata_develop_mode_unknown,
    zcrypt_metadata_develop_mode_enabled,
    zcrypt_metadata_develop_mode_disabled
};

enum zcrypt_metadata_disable_geoip_mode {
    zcrypt_metadata_disable_geoip_unknown,
    zcrypt_metadata_disable_geoip_true,
    zcrypt_metadata_disable_geoip_false
};

/*
 * The format of a metadata string/file is a JSON object of thefollowing form:
 *
 * {
 *    "zscaler_metadata":{
 *       "signed_data":{
 *          "arch":"x86_64",
 *          "binary_sha256":"BASE64 Sha256 of Image without whitespace",
 *          "platform":"PLATFORM: osx, debian, freebsd",
 *          "role":"ROLE. i.e. ‘zpn_assistantd’",
 *          "version":"Version String"
 *        },
 *        "signature_value":"BASE64 Signature of all name + value strings (JSON decoded, but NOT base64 decoded)
 *                           of signature_data, in lexicographic order of their names",
 *        "signing_certificate":"PEM-FORMAT certificate that signed signature",
 *        "signing_certificate_chain":["PEM-FORMAT intermediate", ...]
 *    }
 * }
 *
 *
 * platform is of the form 'platform.version', where 'platform' is one of:
 *
 * osx, debian, ubuntu, centos, freebsd
 *
 * and 'version' is a single decimal number representing the platform major version.
 *
 */

struct zcrypt_metadata;

/*
 * Read a JSON string into a metadata structure.
 */
struct zcrypt_metadata *zcrypt_metadata_read(const char *str);

/*
 * Read a JSON file into a metadata structure.
 */
struct zcrypt_metadata *zcrypt_metadata_read_file(const char *filename);

/*
 * Create a metadata structure without the signing pieces filled in.
 *
 * This routine copies all the input data.
 *
 * origin_SHA256_hash is pure binary format, not base64. All others
 * are simple strings. The certificates are PEM format.
 */
struct zcrypt_metadata *zcrypt_metadata_create(uint8_t *origin_SHA256_hash,
                                               size_t origin_hash_len,
                                               char *platform,
                                               char *arch,
                                               char *role,
                                               char *version,
                                               char *dropdb,
                                               char *signing_certificate,
                                               char **certificate_chain,
                                               int certificate_chain_count);

/*
 * Write out a metadata object in json form
 */
int zcrypt_metadata_write(struct zcrypt_metadata *metadata, char *out_buf, size_t out_buf_len);

/*
 * Sign a metadata object. This replaces any signature that had
 * previously been there. If the resulting certificate does not
 * verify, the signing fails. (The verification piece is why root_pem
 * is needed)
 */
int zcrypt_metadata_sign(struct zcrypt_metadata *metadata, struct zcrypt_rsa_key *key, char **root_pems, int root_pems_count, char *result, size_t result_len);

/*
 * Verify that metadata is properly signed
 */
int zcrypt_metadata_verify(struct zcrypt_metadata *metadata, char **root_pems, int root_pems_count, char *result, size_t result_len);

/*
 * Verify that metadata sha256 matches the passed in sha.
 */
int zcrypt_metadata_match_sha256(struct zcrypt_metadata *metadata, void *hash_data, int hash_data_len);

/*
 * Get the sha256 from metafile
 */
int zcrypt_get_sha256_from_metadata(struct zcrypt_metadata *metadata,uint8_t *meta_sha,size_t *meta_sha_len);

/*
 * Get reference to the version string within metadata. This return
 * value is valid until metadata is freed.
 */
char *zcrypt_metadata_version(struct zcrypt_metadata *metadata);
char *zcrypt_metadata_platform(struct zcrypt_metadata *metadata);
char *zcrypt_metadata_arch(struct zcrypt_metadata *metadata);
char *zcrypt_metadata_role(struct zcrypt_metadata *metadata);

/*
 * Free up metadata
 */
void zcrypt_metadata_free(struct zcrypt_metadata *metadata);

typedef void (zcrypyt_file_verify_log_f)(int syslog_priority,
                                         char *log_buf);


/*
 * Verify version
 */
int zcrypt_metadata_verify_version(char *bin_file, char *meta_file, const char *version, char *version_out, size_t version_out_len, zcrypyt_file_verify_log_f log_fn, int verify_develop_cert, int log_verbose);
int zcrypt_metadata_verify_version_in_memory(const char *data, size_t size, char *meta_file, const char *version, char *version_out, size_t version_out_len, zcrypyt_file_verify_log_f log_fn, int verify_develop_cert, int log_verbose);
/* Verify platform*/
int zcrypt_metadata_verify_platform(char *meta_file, const char *runtime_platform, zcrypyt_file_verify_log_f log_fn, int log_verbose);
/*
 * Get ZPA develop flag from the env
 */
int zcrypt_metadata_get_zpa_develop_env(char *env, size_t env_len);
/*
 * Get ZPA disable_geoip flag from the env
 */
int zcrypt_metadata_get_zpa_disable_geoip_env(char *env, size_t env_len);

/*
 * Get ZPA develop mode from the env string
 */
enum zcrypt_metadata_develop_mode zcrypt_metatdata_get_develop_mode_from_env(char *env);

/*
 * Get ZPA disable_geoip mode from the env string
 */
enum zcrypt_metadata_disable_geoip_mode zcrypt_metatdata_get_disable_geoip_env(const char *env);

/*
 * Get version from metafile
 */
int zcrypt_get_metadata_version(char *meta_file, char *version, int version_len, zcrypyt_file_verify_log_f log_fn, int log_verbose);
int zcrypt_get_metadata_dropdb(char *meta_file, char *dropdb, int len, zcrypyt_file_verify_log_f log_fn, int log_verbose);
#endif /* _ZCRYPT_META_H */
