/*
 * zcrpyt_meta.c. Copyright(C) 2016 Zscaler Inc. All Rights Reserved.
 *
 * Routines for handling (creating/signing/verifying) metadata files.
 */


#include <stdlib.h>
#include <sys/stat.h>

#include "parson/parson.h"

#include "base64/base64.h"

#include "zcrypt/zcrypt.h"
#include "zcrypt/zcrypt_private.h"
#include "zcrypt/zcrypt_meta.h"

#include "zpath_misc/zpath_misc.h"

#include <syslog.h>

#define ZCRYPT_META_MAX_CERTS  5

static char *config_root_pems[] = {
    /***************************************************/
    /* PRODUCTION CERTIFICATE, second, expires 2038, CN=Zscaler ET Production Root CA - G2 */
    "-----BEGIN CERTIFICATE-----\n"
    "MIIEJzCCAw+gAwIBAgIUQcEg0RqEVvJXi+Jt28zWmU6wkdcwDQYJKoZIhvcNAQEL\n"
    "BQAwgZoxCzAJBgNVBAYTAlVTMRMwEQYDVQQIEwpDYWxpZm9ybmlhMREwDwYDVQQH\n"
    "EwhTYW4gSm9zZTEWMBQGA1UEChMNWnNjYWxlciwgSW5jLjEeMBwGA1UECxMVRW1l\n"
    "cmdpbmcgVGVjaG5vbG9naWVzMSswKQYDVQQDEyJac2NhbGVyIEVUIFByb2R1Y3Rp\n"
    "b24gUm9vdCBDQSAtIEcyMB4XDTE2MDUwNTA2MTAwMFoXDTM4MDExOTAzMTQwN1ow\n"
    "gZoxCzAJBgNVBAYTAlVTMRMwEQYDVQQIEwpDYWxpZm9ybmlhMREwDwYDVQQHEwhT\n"
    "YW4gSm9zZTEWMBQGA1UEChMNWnNjYWxlciwgSW5jLjEeMBwGA1UECxMVRW1lcmdp\n"
    "bmcgVGVjaG5vbG9naWVzMSswKQYDVQQDEyJac2NhbGVyIEVUIFByb2R1Y3Rpb24g\n"
    "Um9vdCBDQSAtIEcyMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA0u/Z\n"
    "tKIBr3XSWEC5d5ySkKkgDR9fwqhKreYw12jnPH/aHDRqVOWcWJ2WTYwlHt6qhSBt\n"
    "bMvMY/XqGk2Ig3CTxHroMA5x1+IO/dSkrHxA8cJdB5QZmA3qew+HMbZywo5ggJ11\n"
    "utsYDEnVHazBoAEflt9NzMwJKa1zzHq2bDJyUpFmWHg1BwJWwB2MvJGYEoMWg4Fh\n"
    "fT/tpXddVi0fM7dvy4sRU1sZHiqGdtjXqqV9oIaYsEhRyh+RXdMcJYxa+iQMj7sV\n"
    "HNO0HCZeR7IpaGoWaoFiRjXSrv1GxzcWo7NdLQzhOOxm1P1BLByv3U1NFjSdUYtE\n"
    "Nrf75MQLWi3ZHzielwIDAQABo2MwYTAOBgNVHQ8BAf8EBAMCAQYwDwYDVR0TAQH/\n"
    "BAUwAwEB/zAdBgNVHQ4EFgQUrUI7dVLsNPeonH2fGQvB/FEp1L8wHwYDVR0jBBgw\n"
    "FoAUrUI7dVLsNPeonH2fGQvB/FEp1L8wDQYJKoZIhvcNAQELBQADggEBABSf54t6\n"
    "mptrAXxnPYFamELMwoZaDPVzUH9yNrVS+LCZ7gzOnM5FMUrEEn7N5jK9wQdhrMEc\n"
    "hGnugpnSpLwu1nljTUKDCDBA7005RFndLaNo65xiMW0OCcOS4gkGwyngj+PjKWb1\n"
    "13Y+V/Q4eaouXkrnyzhPtnFtgvtR/HrV98DGjD2QtazFj8p/URrH6j4M+QPF4144\n"
    "SAzRVE52aU7xRAOgZXKUHKrMb0OUqJxLcRYb+TppJpFkc3n9Q0/RslJ0UHOvyR/2\n"
    "LYePbXlsZvCm34a3oa2gwkgNSua55Wy7psmqEPw5Gw01nrwA7kEK7Ff4JaXErBNt\n"
    "wwxn0F6BKQJ3nbE=\n"
    "-----END CERTIFICATE-----\n",

    /***************************************************/
    /* New DEV CERTIFICATE, expires Sep  1 02:47:49 2038 GMT */
    "-----BEGIN CERTIFICATE-----\n"
    "MIIDkzCCAnugAwIBAgIJAKNsfPq/e+rjMA0GCSqGSIb3DQEBCwUAMGAxCzAJBgNV\n"
    "BAYTAlVTMRMwEQYDVQQIDApDYWxpZm9ybmlhMRAwDgYDVQQKDAdac2NhbGVyMSow\n"
    "KAYDVQQDDCFFVCBkZXYuenBhdGgubmV0IFJvb3QgQ2VydGlmaWNhdGUwHhcNMTgw\n"
    "OTA2MDI0NzQ5WhcNMzgwOTAxMDI0NzQ5WjBgMQswCQYDVQQGEwJVUzETMBEGA1UE\n"
    "CAwKQ2FsaWZvcm5pYTEQMA4GA1UECgwHWnNjYWxlcjEqMCgGA1UEAwwhRVQgZGV2\n"
    "LnpwYXRoLm5ldCBSb290IENlcnRpZmljYXRlMIIBIjANBgkqhkiG9w0BAQEFAAOC\n"
    "AQ8AMIIBCgKCAQEA20mjKCLQE9a2w/xgLl43kCQnZBJnu8TOX1b7CG3MnC001VFL\n"
    "MW9KvGq7f+FK3MeVOlEExpYtdivhbG+P5JOie2MlF+X70muKVYsoulWH2Z20b/cC\n"
    "hRR/OwY58bl+KoBYrMuUjEtvfkjht6THnMyHXLN+6uusYyx7XSptRwR3cI8jHyUp\n"
    "odHW3Qd877Bqg/+QntcTHTwa8PAHwzYObD1cCCz4VAa3zbjikwRreRa3FoeAOpLH\n"
    "8psMX7RtMvZNW9iqjKs9G6nVZA9MJxX0mqC5dJ2HCGYK8CriIn2AHMxSgnw7RdQA\n"
    "f0WF7uhc/z7xP4l6U9qDAUJV1H39yteLjrQl+QIDAQABo1AwTjAdBgNVHQ4EFgQU\n"
    "9rhD/54rmnW0vMPxbDiJE5kavicwHwYDVR0jBBgwFoAU9rhD/54rmnW0vMPxbDiJ\n"
    "E5kavicwDAYDVR0TBAUwAwEB/zANBgkqhkiG9w0BAQsFAAOCAQEAeYBPz/mjqfw4\n"
    "+xmV6/nuVLvqd8BuSj1r7TpiLHg3thUNHwzEPQdp6KSkrNuQHj+ovaQJGGhApDCv\n"
    "8st5yr3o/1AIC1zBrQ+CuvJ+tk8XwgxgGNvMfUPPAFXEQHkgT79ybClg1GeKsh34\n"
    "7zl70L5NjFfwnHOnqjWA6cksUy0ZTpQv9OVgxFaBp/Vh0HNOe9g6pdxDolu7Ixc4\n"
    "/0vnijSzMCnIxfQFF5DHp4V+p6X3t/ML5rKCoMXciogP+bZ/4IjPLj39orjcLop/\n"
    "dj/3pLxcxqpFUDIoxxst5xXUwS3g9OLpGoEnLiy/xBdIw2fTD/DJ/XmLtxweEoFS\n"
    "zbRUQKgqlA==\n"
    "-----END CERTIFICATE-----\n",
};

/* XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
 * XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
 * XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
 *
 * DO NOT CHANGE THIS FROM 1. IT IS PURPOSELY SET TO ONLY INCLUDE THE
 * PRODUCTION ROOT CERTIFICATE. THIS VALUE IS OVERRIDDEN BY COMMAND
 * LINE ARGUMENT FOR THE CASE WHERE YOU WANT TO TRUST DEV CERTIFICATE
 * TOO
 */
int config_root_pems_count = 1;
/* XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
 * XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
 * XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
 */





/* All members are allocated */
struct zcrypt_metadata {
    struct {
        char *arch;                       // string
        char *binary_sha256;              // base64
        char *platform;                   // string
        char *role;                       // string
        char *version;                    // string
        char *dropdb;                     // string
    } signed_data;
    char *signature_value;                // base64
    char *signing_certificate;          // string, PEM format
    char *signing_certificate_chain[ZCRYPT_META_MAX_CERTS];  // string, PEM format
    int chain_count;
};


static int fill_field(JSON_Object *obj, char *path, char **dest, int required)
{
    const char *str = json_object_dotget_string(obj, path);

    if (!str) {
        if (required) {
            fprintf(stderr, "Metadata doesn't contain %s\n", path);
            return ZCRYPT_RESULT_ERR;
        } else {
            if (*dest) ZCRYPT_FREE(*dest);
            *dest = NULL;
            return ZCRYPT_RESULT_NO_ERROR;
        }
    }
    *dest = ZCRYPT_STRDUP(str, strlen(str));
    return ZCRYPT_RESULT_NO_ERROR;
}

static struct zcrypt_metadata *read_meta(const char *str, int is_filename)
{
    JSON_Value *json;
    JSON_Object *object;
    JSON_Array *certs;
    struct zcrypt_metadata *meta;

    json = NULL;
    meta = ZCRYPT_CALLOC(sizeof(*meta));
    if (meta) {
        if (is_filename) {
            json = json_parse_file(str);
        } else {
            json = json_parse_string(str);
        }
        if (!json) {
            //fprintf(stderr, "Could not parse JSON for metadata. JSON = <%s>\n", str);
            goto erk;;
        }

        object = json_value_get_object(json);
        if (!object) {
            fprintf(stderr, "JSON metadata was not an object\n");
            goto erk;
        }

        if (fill_field(object, "zscaler_metadata.signed_data.arch", &(meta->signed_data.arch), 1)) goto erk;
        if (fill_field(object, "zscaler_metadata.signed_data.binary_sha256", &(meta->signed_data.binary_sha256), 1)) goto erk;
        if (fill_field(object, "zscaler_metadata.signed_data.platform", &(meta->signed_data.platform), 1)) goto erk;
        if (fill_field(object, "zscaler_metadata.signed_data.role", &(meta->signed_data.role), 1)) goto erk;
        if (fill_field(object, "zscaler_metadata.signed_data.version", &(meta->signed_data.version), 1)) goto erk;
        if (fill_field(object, "zscaler_metadata.signed_data.dropdb", &(meta->signed_data.dropdb), 0)) goto erk;
        if (fill_field(object, "zscaler_metadata.signature_value", &(meta->signature_value), 1)) goto erk;
        if (fill_field(object, "zscaler_metadata.signing_certificate", &(meta->signing_certificate), 1)) goto erk;

        certs = json_object_dotget_array(object, "zscaler_metadata.signing_certificate_chain");
        if (certs) {
            int i;
            int count = json_array_get_count(certs);
            if (count > ZCRYPT_META_MAX_CERTS) {
                fprintf(stderr, "JSON metadata contains more than %d intermediate certificates\n", ZCRYPT_META_MAX_CERTS);
                goto erk;
            }
            for (i = 0; i < count; i++) {
                const char *str;
                str = json_array_get_string(certs, i);
                if (!str) {
                    fprintf(stderr, "JSON metadata contains a cert in cert chain that is not a string- index %d\n", i);
                    goto erk;
                }
                meta->signing_certificate_chain[i] = ZCRYPT_STRDUP(str, strlen(str));
                meta->chain_count = i + 1;
            }
        } else {
            /* Remove any certs that were there. */
            while(meta->chain_count) {
                meta->chain_count--;
                if (meta->signing_certificate_chain[meta->chain_count]) ZCRYPT_FREE(meta->signing_certificate_chain[meta->chain_count]);
                meta->signing_certificate_chain[meta->chain_count] = 0;
            }
        }
    }
    if (json) json_value_free(json);

    return meta;

erk:
    if (meta) zcrypt_metadata_free(meta);
    if (json) json_value_free(json);
    return NULL;
}

struct zcrypt_metadata *zcrypt_metadata_read(const char *str)
{
    return read_meta(str, 0);
}

struct zcrypt_metadata *zcrypt_metadata_read_file(const char *str)
{
    return read_meta(str, 1);
}

struct zcrypt_metadata *zcrypt_metadata_create(uint8_t *origin_SHA256_hash,
                                               size_t origin_hash_len,
                                               char *platform,
                                               char *arch,
                                               char *role,
                                               char *version,
                                               char *dropdb,
                                               char *signing_certificate,
                                               char **certificate_chain,
                                               int certificate_chain_count)
{
    struct zcrypt_metadata *meta;
    char tmp_str[1000];
    int i;

    if (certificate_chain_count > ZCRYPT_META_MAX_CERTS) {
        fprintf(stderr, "More than %d intermediate certificates supplied\n", ZCRYPT_META_MAX_CERTS);
        return NULL;
    }
    meta = ZCRYPT_CALLOC(sizeof(*meta));
    if (meta) {
        if (base64_encoded_size(origin_hash_len) > sizeof(tmp_str) - 1) {
            fprintf(stderr, "origin sha too large\n");
            goto erk;
        }
        base64_encode_binary(tmp_str, origin_SHA256_hash, origin_hash_len);
        tmp_str[base64_encoded_size(origin_hash_len)] = 0;
        meta->signed_data.binary_sha256 = ZCRYPT_STRDUP(tmp_str, strlen(tmp_str));
        meta->signed_data.platform = ZCRYPT_STRDUP(platform, strlen(platform));
        meta->signed_data.arch = ZCRYPT_STRDUP(arch, strlen(arch));
        meta->signed_data.role = ZCRYPT_STRDUP(role, strlen(role));
        meta->signed_data.version = ZCRYPT_STRDUP(version, strlen(version));
        if (dropdb != NULL) {
            meta->signed_data.dropdb = ZCRYPT_STRDUP(dropdb, strlen(dropdb));
        }
        meta->signing_certificate = ZCRYPT_STRDUP(signing_certificate, strlen(signing_certificate));
        for (i = 0; certificate_chain && certificate_chain[i] && (i < certificate_chain_count); i++) {
            meta->signing_certificate_chain[i] = ZCRYPT_STRDUP(certificate_chain[i], strlen(certificate_chain[i]));
            meta->chain_count = i + 1;
        }
    }
    return meta;

 erk:
    if (meta) zcrypt_metadata_free(meta);
    return NULL;
}


/*
 * Write out a metadata object in json form
 */
int zcrypt_metadata_write(struct zcrypt_metadata *meta, char *out_buf, size_t out_buf_len)
{
    JSON_Value *json = NULL;
    JSON_Object *object = NULL;
    JSON_Value *certsValue = NULL;
    JSON_Array *certs = NULL;
    int i;

    json = json_value_init_object();
    if (!json) goto erk;
    object = json_value_get_object(json);
    if (!object) goto erk;

    if (meta->signed_data.arch &&
        json_object_dotset_string(object, "zscaler_metadata.signed_data.arch", meta->signed_data.arch) != JSONSuccess) goto erk;
    if (meta->signed_data.binary_sha256 &&
        json_object_dotset_string(object, "zscaler_metadata.signed_data.binary_sha256", meta->signed_data.binary_sha256) != JSONSuccess) goto erk;
    if (meta->signed_data.platform &&
        json_object_dotset_string(object, "zscaler_metadata.signed_data.platform", meta->signed_data.platform) != JSONSuccess) goto erk;
    if (meta->signed_data.role &&
        json_object_dotset_string(object, "zscaler_metadata.signed_data.role", meta->signed_data.role) != JSONSuccess) goto erk;
    if (meta->signed_data.version &&
        json_object_dotset_string(object, "zscaler_metadata.signed_data.version", meta->signed_data.version) != JSONSuccess) goto erk;
    if (meta->signed_data.dropdb &&
        json_object_dotset_string(object, "zscaler_metadata.signed_data.dropdb", meta->signed_data.dropdb) != JSONSuccess) goto erk;
    if (meta->signature_value &&
        json_object_dotset_string(object, "zscaler_metadata.signature_value", meta->signature_value) != JSONSuccess) goto erk;
    if (meta->signing_certificate &&
        json_object_dotset_string(object, "zscaler_metadata.signing_certificate", meta->signing_certificate) != JSONSuccess) goto erk;
    if (meta->chain_count) {
        certsValue = json_value_init_array();
        if (!certsValue) goto erk;
        certs = json_value_get_array(certsValue);
        if (!certs) goto erk;
        for (i = 0; i < meta->chain_count; i++) {
            if (json_array_append_string(certs, meta->signing_certificate_chain[i]) != JSONSuccess) goto erk;
        }
        if (json_object_dotset_value(object, "zscaler_metadata.signing_certificate_chain", certsValue) != JSONSuccess) goto erk;
    }
    if (json_serialize_to_buffer_pretty(json, out_buf, out_buf_len) != JSONSuccess) goto erk;

    //if (certsValue) json_value_free(certsValue);
    if (json) json_value_free(json);
    return ZCRYPT_RESULT_NO_ERROR;

 erk:
    //if (certsValue) json_value_free(certsValue);
    if (json) json_value_free(json);
    return ZCRYPT_RESULT_ERR;
}


static void hash_signed_data(struct zcrypt_key *key, struct zcrypt_metadata *meta)
{
    char hashme[5000];
    size_t len;
    if (meta->signed_data.dropdb != NULL) {
        len = szprintf(hashme, sizeof(hashme),
                    "arch%sbinary_sha256%splatform%srole%sversion%sdropdb:%s",
                    meta->signed_data.arch,
                    meta->signed_data.binary_sha256,
                    meta->signed_data.platform,
                    meta->signed_data.role,
                    meta->signed_data.version,
                    meta->signed_data.dropdb);
    } else {
        len = szprintf(hashme, sizeof(hashme),
                    "arch%sbinary_sha256%splatform%srole%sversion%s",
                    meta->signed_data.arch,
                    meta->signed_data.binary_sha256,
                    meta->signed_data.platform,
                    meta->signed_data.role,
                    meta->signed_data.version);
    }
    if (zcrypt_gen_key(key, hashme, len)) {
        /* REALLY should not happen */
        fprintf(stderr, "sarge: Could not hash metadata\n");
    }
}


/*
 * Sign a metadata object. This replaces any signature that had
 * previously been there. If the resulting certificate does not
 * verify, the signing fails. (The verification piece is why root_pem
 * is needed)
 */
int zcrypt_metadata_sign(struct zcrypt_metadata *meta, struct zcrypt_rsa_key *rsa_key, char **root_pems, int root_pems_count, char *reason, size_t reason_len)
{
    uint8_t signature[1024];
    char signature_b64[2048];
    size_t signature_len;
    struct zcrypt_key key;
    int res;

    signature_len = sizeof(signature);
    hash_signed_data(&key, meta);

    res = zcrypt_sign(&(key.data[0]), sizeof(key.data), signature, &signature_len, rsa_key);
    if (res) {
        if (reason) snprintf(reason, reason_len, "Signing failed");
        //fprintf(stderr, "Signing failed\n");
        return res;
    }
    base64_encode_binary(signature_b64, signature, signature_len);
    signature_b64[base64_encoded_size(signature_len)] = 0;
    if (meta->signature_value) {
        ZCRYPT_FREE(meta->signature_value);
    }
    meta->signature_value = ZCRYPT_STRDUP(signature_b64, strlen(signature_b64));

    //fprintf(stderr, "Signing complete. Verifying...\n");

    return(zcrypt_metadata_verify(meta, root_pems, root_pems_count, reason, reason_len));
}

/*
 * Verify that metadata is properly signed
 */
int zcrypt_metadata_verify(struct zcrypt_metadata *metadata, char **root_pems, int root_pems_count, char *reason, size_t reason_len)
{
    uint8_t signature[1024];
    long int signature_len;
    struct zcrypt_key key;
    int res;

    if (reason) *reason = 0;

    if (!metadata->signed_data.arch ||
        !metadata->signed_data.binary_sha256 ||
        !metadata->signed_data.platform ||
        !metadata->signed_data.role ||
        !metadata->signed_data.version ||
        !metadata->signature_value ||
        !metadata->signing_certificate) {
        if (reason) snprintf(reason, reason_len, "Metadata incomplete");
        return ZCRYPT_RESULT_ERR;
    }

    /* Convert base64 signature to binary */
    signature_len = strlen(metadata->signature_value);
    if (base64_decoded_size(metadata->signature_value, signature_len) >= sizeof(signature)) {
        if (reason) snprintf(reason, reason_len, "Metadata signature too big to verify");
        return ZCRYPT_RESULT_ERR;
    }
    signature_len = base64_decode_binary(signature, metadata->signature_value, signature_len);
    if (signature_len < 1) {
        if (reason) snprintf(reason, reason_len, "Metadata signature too small/invalid");
        return ZCRYPT_RESULT_ERR;
    }

    /* Hash the data that was actually signed. */
    hash_signed_data(&key, metadata);

    /* Verify the resultant hash was signed by the certificate in the
     * metadata */
    res = zcrypt_verify_signed(&(key.data[0]),
                               sizeof(key.data),
                               signature,
                               signature_len,
                               metadata->signing_certificate);
    if (res) {
        if (reason) snprintf(reason, reason_len, "Metadata signature fails to verify");
        return ZCRYPT_RESULT_ERR;
    }

    //fprintf(stderr, "Signature verifies, but does the certificate chain?\n");

    /* Verify the trust chain to the root that was passed in */
#if 0
    int i;
    for (i = 0; i < root_pems_count; i++) {
        fprintf(stderr, "Root pems %d:\n%s", i, root_pems[i]);
    }
    for (i = 0; i < metadata->chain_count; i++) {
        fprintf(stderr, "Chain pems %d:\n%s", i, metadata->signing_certificate_chain[i]);
    }
    fprintf(stderr, "Signing cert:\n%s", metadata->signing_certificate);
#endif // 0
    res = zcrypt_verify_chain(root_pems,
                              root_pems_count,
                              NULL,
                              0,
                              &(metadata->signing_certificate_chain[0]),
                              metadata->chain_count,
                              metadata->signing_certificate,
                              zcrypt_cert_purpose_code_signing,
                              reason,
                              reason_len, NULL, 0);
    if (res) {
        if (reason && !(*reason)) {
            snprintf(reason, reason_len, "Metadata certificate chain fails to verify");
        }
        return ZCRYPT_RESULT_ERR;
    }

    return ZCRYPT_RESULT_NO_ERROR;
}

int zcrypt_get_sha256_from_metadata(struct zcrypt_metadata *metadata,uint8_t *meta_sha,size_t *meta_sha_len){
    size_t len;

    if (!metadata->signed_data.binary_sha256) return ZCRYPT_RESULT_ERR;

    len = strlen(metadata->signed_data.binary_sha256);
    *meta_sha_len = base64_decode_binary(meta_sha, metadata->signed_data.binary_sha256, len);
    return ZCRYPT_RESULT_NO_ERROR;
}

int zcrypt_metadata_match_sha256(struct zcrypt_metadata *metadata, void *hash_data, int hash_data_len)
{
    uint8_t meta_sha[256];
    size_t meta_sha_len;
    size_t len;

    if (!metadata->signed_data.binary_sha256) return ZCRYPT_RESULT_ERR;
    len = strlen(metadata->signed_data.binary_sha256);
    if (base64_decoded_size(metadata->signed_data.binary_sha256, len) >= sizeof(meta_sha)) {
        fprintf(stderr, "sha too big to match\n");
        return ZCRYPT_RESULT_ERR;
    }
    meta_sha_len = base64_decode_binary(meta_sha, metadata->signed_data.binary_sha256, len);
    if (meta_sha_len == -1 || meta_sha_len != hash_data_len) {
        return ZCRYPT_RESULT_ERR;
    }
    if (memcmp(meta_sha, hash_data, hash_data_len)) {
        return ZCRYPT_RESULT_ERR;
    }
    return ZCRYPT_RESULT_NO_ERROR;
}

char *zcrypt_metadata_version(struct zcrypt_metadata *metadata)
{
    if (!metadata) return NULL;
    return metadata->signed_data.version;
}

char *zcrypt_metadata_dropdb(struct zcrypt_metadata *metadata)
{
    if (!metadata) return NULL;
    return metadata->signed_data.dropdb;
}

char *zcrypt_metadata_platform(struct zcrypt_metadata *metadata)
{
    if (!metadata) return NULL;
    return metadata->signed_data.platform;
}

char *zcrypt_metadata_arch(struct zcrypt_metadata *metadata)
{
    if (!metadata) return NULL;
    return metadata->signed_data.arch;
}

char *zcrypt_metadata_role(struct zcrypt_metadata *metadata)
{
    if (!metadata) return NULL;
    return metadata->signed_data.role;
}

void zcrypt_metadata_free(struct zcrypt_metadata *metadata)
{
    int i;

    if (metadata) {
        if (metadata->signed_data.binary_sha256) ZCRYPT_FREE(metadata->signed_data.binary_sha256);
        if (metadata->signed_data.platform) ZCRYPT_FREE(metadata->signed_data.platform);
        if (metadata->signed_data.arch) ZCRYPT_FREE(metadata->signed_data.arch);
        if (metadata->signed_data.role) ZCRYPT_FREE(metadata->signed_data.role);
        if (metadata->signed_data.version) ZCRYPT_FREE(metadata->signed_data.version);
        if (metadata->signed_data.dropdb) ZCRYPT_FREE(metadata->signed_data.dropdb);
        if (metadata->signature_value) ZCRYPT_FREE(metadata->signature_value);
        if (metadata->signing_certificate) ZCRYPT_FREE(metadata->signing_certificate);

        for (i = 0; i < metadata->chain_count; i++) {
            if (metadata->signing_certificate_chain[i]) ZCRYPT_FREE(metadata->signing_certificate_chain[i]);
        }

        ZCRYPT_FREE(metadata);
    }
}


int zcrypt_metadata_verify_version(char *bin_file, char *meta_file, const char *version, char *version_out, size_t version_out_len, zcrypyt_file_verify_log_f log_fn, int verify_develop_cert, int log_verbose)
{
    struct zcrypt_metadata *metadata;
    struct zcrypt_key hash;
    struct stat st;
    char *meta_version;
    char str[1000];
    char log_buf[1088];
    int verify_root_certs_count = verify_develop_cert ? (config_root_pems_count + 1) : config_root_pems_count; /* Exclude dev certs unless requested */

    /* Are args supplied ? */
    if (!bin_file || !meta_file || !log_fn) {
        return ZCRYPT_RESULT_ERR;
    }

    if (stat(meta_file, &st) != 0) {
        if (log_verbose) {
            /* This is pretty common- don't bother logging if not strict */
            snprintf(log_buf, sizeof(log_buf), "Metadata file not found: %s", meta_file);
            log_fn(LOG_NOTICE, log_buf);
        }
        return ZCRYPT_RESULT_ERR;
    }

    metadata = zcrypt_metadata_read_file(meta_file);
    if (!metadata) {
        snprintf(log_buf, sizeof(log_buf), "Metadata file corrupt: %s", meta_file);
        log_fn(LOG_NOTICE, log_buf);
        return ZCRYPT_RESULT_ERR;
    }

    if (stat(bin_file, &st) != 0) {
        zcrypt_metadata_free(metadata);
        if (log_verbose) {
            snprintf(log_buf, sizeof(log_buf), "No binary downloaded: %s", bin_file);
            log_fn(LOG_ERR, log_buf);
        }
        return ZCRYPT_RESULT_ERR;
    }

    if (zcrypt_gen_key_file(&hash, bin_file)) {
        zcrypt_metadata_free(metadata);
        snprintf(log_buf, sizeof(log_buf), "Could not generate hash of binary %s", bin_file);
        log_fn(LOG_ERR, log_buf);
        return ZCRYPT_RESULT_ERR;
    }

    if (zcrypt_metadata_verify(metadata, config_root_pems, verify_root_certs_count, str, sizeof(str))) {
        zcrypt_metadata_free(metadata);
        snprintf(log_buf, sizeof(log_buf), "Metadata file %s fails signing check: %s", meta_file, str);
        log_fn(LOG_ERR, log_buf);
        return ZCRYPT_RESULT_ERR;
    }

    if (zcrypt_metadata_match_sha256(metadata, &hash, sizeof(hash)) != ZCRYPT_RESULT_NO_ERROR) {
        zcrypt_metadata_free(metadata);
        snprintf(log_buf, sizeof(log_buf), "Hash of file %s does not match hash in metadata file %s (probably need to download new version)", bin_file, meta_file);
        log_fn(LOG_NOTICE, log_buf);
        return ZCRYPT_RESULT_ERR;
    }

    meta_version = zcrypt_metadata_version(metadata);
    if (!meta_version) {
        zcrypt_metadata_free(metadata);
        snprintf(log_buf, sizeof(log_buf), "Metadata file %s contains no version", meta_file);
        log_fn(LOG_ERR, log_buf);
        return ZCRYPT_RESULT_ERR;
    }

    if (version &&
        (strcmp(version, meta_version) != 0) &&
        (strcmp(version, "default") != 0)) {
        snprintf(log_buf, sizeof(log_buf), "Binary image version is %s, upgrading to %s", meta_version, version);
        log_fn(LOG_NOTICE, log_buf);
        zcrypt_metadata_free(metadata);
        return ZCRYPT_RESULT_ERR;
    }

    if (version_out) snprintf(version_out, version_out_len, "%s", meta_version);

    zcrypt_metadata_free(metadata);

    return ZCRYPT_RESULT_NO_ERROR;
}

int zcrypt_metadata_verify_platform(char *meta_file, const char *platform, zcrypyt_file_verify_log_f log_fn, int log_verbose) {

    struct stat st;
    struct zcrypt_metadata *metadata;
    char log_buf[1088];

    if (stat(meta_file, &st) != 0) {
        if (log_verbose) {
            /* This is pretty common- don't bother logging if not strict */
            snprintf(log_buf, sizeof(log_buf), "Metadata file not found: %s", meta_file);
            log_fn(LOG_NOTICE, log_buf);
        }
        return ZCRYPT_RESULT_NO_ERROR;
    }

    metadata = zcrypt_metadata_read_file(meta_file);
    if (!metadata) {
        snprintf(log_buf, sizeof(log_buf), "Metadata file corrupt: %s", meta_file);
        log_fn(LOG_NOTICE, log_buf);
        return ZCRYPT_RESULT_NO_ERROR;
    }

    char *metadata_platform;
    metadata_platform = zcrypt_metadata_platform(metadata);
    if (metadata_platform && (strcmp(metadata_platform, platform) != 0)) {
        snprintf(log_buf, sizeof(log_buf), "Platform in metadata not matching with current, metadata platform %s, current platform %s", metadata_platform, platform);
        log_fn(LOG_NOTICE, log_buf);
        zcrypt_metadata_free(metadata);
        return ZCRYPT_RESULT_ERR;
    }
    zcrypt_metadata_free(metadata);
    return ZCRYPT_RESULT_NO_ERROR;

}

int zcrypt_metadata_verify_version_in_memory(const char *data, size_t size, char *meta_file, const char *version, char *version_out, size_t version_out_len, zcrypyt_file_verify_log_f log_fn, int verify_develop_cert, int log_verbose)
{
    struct zcrypt_metadata *metadata;
    struct zcrypt_key hash;
    struct stat st;
    char *meta_version;
    char str[1000];
    char log_buf[1088];
    int verify_root_certs_count = verify_develop_cert ? (config_root_pems_count + 1) : config_root_pems_count; /* Exclude dev certs unless requested */

    /* Are args supplied ? */
    if (!data || !meta_file || !log_fn) {
        return ZCRYPT_RESULT_ERR;
    }

    if (stat(meta_file, &st) != 0) {
        if (log_verbose) {
            /* This is pretty common- don't bother logging if not strict */
            snprintf(log_buf, sizeof(log_buf), "Metadata file not found: %s", meta_file);
            log_fn(LOG_NOTICE, log_buf);
        }
        return ZCRYPT_RESULT_ERR;
    }

    metadata = zcrypt_metadata_read_file(meta_file);
    if (!metadata) {
        snprintf(log_buf, sizeof(log_buf), "Metadata file corrupt: %s", meta_file);
        log_fn(LOG_NOTICE, log_buf);
        return ZCRYPT_RESULT_ERR;
    }

    if (zcrypt_gen_key(&hash, (void*)data, size)) {
        zcrypt_metadata_free(metadata);
        snprintf(log_buf, sizeof(log_buf), "Could not generate hash of provided data");
        log_fn(LOG_ERR, log_buf);
        return ZCRYPT_RESULT_ERR;
    }

    if (zcrypt_metadata_verify(metadata, config_root_pems, verify_root_certs_count, str, sizeof(str))) {
        zcrypt_metadata_free(metadata);
        snprintf(log_buf, sizeof(log_buf), "Metadata file %s fails signing check: %s", meta_file, str);
        log_fn(LOG_ERR, log_buf);
        return ZCRYPT_RESULT_ERR;
    }

    if (zcrypt_metadata_match_sha256(metadata, &hash, sizeof(hash)) != ZCRYPT_RESULT_NO_ERROR) {
        zcrypt_metadata_free(metadata);
        snprintf(log_buf, sizeof(log_buf), "Hash of data does not match hash in metadata file %s", meta_file);
        log_fn(LOG_NOTICE, log_buf);
        return ZCRYPT_RESULT_ERR;
    }

    meta_version = zcrypt_metadata_version(metadata);
    if (!meta_version) {
        zcrypt_metadata_free(metadata);
        snprintf(log_buf, sizeof(log_buf), "Metadata file %s contains no version", meta_file);
        log_fn(LOG_ERR, log_buf);
        return ZCRYPT_RESULT_ERR;
    }

    if (version &&
        (strcmp(version, meta_version) != 0) &&
        (strcmp(version, "default") != 0)) {
        snprintf(log_buf, sizeof(log_buf), "Binary image version is %s, metfile version %s - version mismatch.", meta_version, version);
        log_fn(LOG_NOTICE, log_buf);
        zcrypt_metadata_free(metadata);
        return ZCRYPT_RESULT_ERR;
    }

    if (version_out) snprintf(version_out, version_out_len, "%s", meta_version);

    zcrypt_metadata_free(metadata);

    return ZCRYPT_RESULT_NO_ERROR;
}

int zcrypt_get_metadata_version(char *meta_file, char *version, int version_len, zcrypyt_file_verify_log_f log_fn, int log_verbose)
{
    struct zcrypt_metadata *metadata;
    struct stat st;
    char *meta_version;
    char log_buf[1088];

    /* Are args supplied ? */
    if (!meta_file || !log_fn || version == NULL) {
        return ZCRYPT_RESULT_ERR;
    }

    if (stat(meta_file, &st) != 0) {
        if (log_verbose) {
            /* This is pretty common- don't bother logging if not strict */
            snprintf(log_buf, sizeof(log_buf), "Metadata file not found: %s", meta_file);
            log_fn(LOG_NOTICE, log_buf);
        }
        return ZCRYPT_RESULT_ERR;
    }

    metadata = zcrypt_metadata_read_file(meta_file);
    if (!metadata) {
        snprintf(log_buf, sizeof(log_buf), "Metadata file corrupt: %s", meta_file);
        log_fn(LOG_NOTICE, log_buf);
        return ZCRYPT_RESULT_ERR;
    }

    meta_version = zcrypt_metadata_version(metadata);
    if (!meta_version) {
        zcrypt_metadata_free(metadata);
        snprintf(log_buf, sizeof(log_buf), "Metadata file %s contains no version", meta_file);
        log_fn(LOG_ERR, log_buf);
        return ZCRYPT_RESULT_ERR;
    }

    memset(version,'\0',version_len);
    snprintf(version, version_len, "%s", meta_version);
    version[version_len-1]='\0';

    zcrypt_metadata_free(metadata);

    return ZCRYPT_RESULT_NO_ERROR;
}

int zcrypt_get_metadata_dropdb(char *meta_file, char *dropdb, int len, zcrypyt_file_verify_log_f log_fn, int log_verbose)
{
    struct zcrypt_metadata *metadata;
    struct stat st;
    char *ldropdb;
    char log_buf[1088];

    /* Are args supplied ? */
    if (!meta_file || !log_fn || dropdb == NULL) {
        return ZCRYPT_RESULT_ERR;
    }

    if (stat(meta_file, &st) != 0) {
        if (log_verbose) {
            /* This is pretty common- don't bother logging if not strict */
            snprintf(log_buf, sizeof(log_buf), "Metadata file not found: %s", meta_file);
            log_fn(LOG_NOTICE, log_buf);
        }
        return ZCRYPT_RESULT_ERR;
    }

    metadata = zcrypt_metadata_read_file(meta_file);
    if (!metadata) {
        snprintf(log_buf, sizeof(log_buf), "Metadata file corrupt: %s", meta_file);
        log_fn(LOG_NOTICE, log_buf);
        return ZCRYPT_RESULT_ERR;
    }

    ldropdb = zcrypt_metadata_dropdb(metadata);
    if (!ldropdb) {
        zcrypt_metadata_free(metadata);
        snprintf(log_buf, sizeof(log_buf), "Metadata file %s contains no dropdb", meta_file);
        log_fn(LOG_ERR, log_buf);
        return ZCRYPT_RESULT_ERR;
    }

    memset(dropdb,'\0',len);
    snprintf(dropdb, len, "%s", ldropdb);
    dropdb[len-1]='\0';

    zcrypt_metadata_free(metadata);

    return ZCRYPT_RESULT_NO_ERROR;
}

int zcrypt_metadata_get_zpa_develop_env(char *env, size_t env_len)
{
    char *e = getenv(ZCRYPT_ZPA_DEVELOP_ENV);
    if (e) {
        size_t len = strlen(e);
        if (len <= env_len) {
            snprintf(env, env_len, "%s", e);
            return ZCRYPT_RESULT_NO_ERROR;
        }
    }
    return ZCRYPT_RESULT_ERR;
}


enum zcrypt_metadata_develop_mode zcrypt_metatdata_get_develop_mode_from_env(char *env)
{
    enum zcrypt_metadata_develop_mode mode = zcrypt_metadata_develop_mode_unknown;

    if (strcmp(env, "ENABLED") == 0) {
        mode = zcrypt_metadata_develop_mode_enabled;
    } else if (strcmp(env, "DISABLED") == 0) {
        mode = zcrypt_metadata_develop_mode_disabled;
    }

    return mode;
}

int zcrypt_metadata_get_zpa_disable_geoip_env(char *env, size_t env_len)
{
    char *e = getenv(ZCRYPT_ZPA_DISABLE_GEOIP);
    if (e && e[0]!='\0') {
        size_t len = strlen(e);
        if (len <= env_len) {
            snprintf(env, env_len, "%s", e);
            return ZCRYPT_RESULT_NO_ERROR;
        }
    }
    return ZCRYPT_RESULT_ERR;
}

enum zcrypt_metadata_disable_geoip_mode zcrypt_metatdata_get_disable_geoip_env(const char *env)
{
    enum zcrypt_metadata_disable_geoip_mode mode = zcrypt_metadata_disable_geoip_false;

    if (strcmp(env, "TRUE") == 0) {
        mode = zcrypt_metadata_disable_geoip_true;
    } else if (strcmp(env, "FALSE") == 0) {
        mode = zcrypt_metadata_disable_geoip_false;
    }

    return mode;
}
