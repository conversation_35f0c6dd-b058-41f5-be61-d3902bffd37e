/*
 * zsign.c. Copyright(C) 2016 Zscaler Inc. All Rights Reserved.
 */

/*
 * This file can be used to sign and verify metadata files.
 */

#include <stdlib.h>
#include <stdio.h>
#include <stdarg.h>
#include <string.h>


#include <openssl/conf.h>
#include <errno.h>

#include "zcrypt/zcrypt_meta.h"
#include "zcrypt/zcrypt.h"
#include "base64/base64.h"


char *config_binary_filename = NULL;
char *config_metadata_filename = NULL;
char *config_key_filename = NULL;
char *config_cert_filename = NULL;
char *config_intermediate_filename = NULL;
char *config_role = NULL;
char *config_platform = NULL;
char *config_arch = NULL;
char *config_version = NULL;
char *config_root = NULL;
char *dropdb = NULL;
char *config_sym_key_filename = NULL;
char *config_encrypt_filename = NULL;
char *config_decrypt_filename = NULL;
int config_sign = 0;
int config_verify = 0;
int encrypt_file = 0;
int decrypt_file = 0;


static void usage(const char *argv0, const char *format, ...)
    __attribute__((format(printf, 2, 3)));
static void usage(const char *argv0, const char *format, ...)
{
    va_list list;

    if (format) {
        fprintf(stdout, "Error: ");
        va_start(list, format);
        vfprintf(stdout, format, list);
        fprintf(stdout, "\n\n");
    }

    fprintf(stdout, "%s: [options]\n", argv0);
    fprintf(stdout,
            " **************************************************\n"
            " Options for signing:\n"
            "\n"
            " -sign : Indicate that signing is required.\n"
            "\n"
            " -metadata FILE : The metadata file to create. Overwrites metadata\n"
            "                  file if it was there.\n"
            "\n"
            " -key FILE : The private key to use for signing. (PEM format)\n"
            "\n"
            " -cert FILE : The public certificate used for signing. (PEM format)\n"
            "\n"
            " -intermediate FILE : Optional : An intermediate certificate to\n"
            "                      complete the chain from 'cert' to\n"
            "                      root. Eventually a full chain will be accepted\n"
            "                      here, but for now a single certificate.\n"
            "\n"
            " -root FILE : The root of the chain of trust for signing (PEM\n"
            "              format). Used to verify that signing was successful\n"
            "\n"
            " -role ROLE : The role of the software being signed. zpa_connector,\n"
            "              etc.\n"
            "\n"
            " -platform PLATFORM : The platform for the executable. debian8,\n"
            "                      freebsd8, etc.\n"
            "\n"
            " -arch ARCH : The hardware architecture that is running. x86_64\n"
            "\n"
            " -version VERSION : The version of the executable.\n"
            "\n"
            " FILE : The file to sign.\n"
            "\n"
            " **************************************************\n"
            " Options for verifying:\n"
            "\n"
            " -verify : Indicate that verification is required.\n"
            "\n"
            " -metadata FILE : The metadata file to use to verify the binary.\n"
            "\n"
            " -root FILE : The root of the chain of trust for signing (PEM\n"
            "              format)\n"
            "\n"
            " FILE : The file to verify.\n"
            "\n"
            " **************************************************\n"
            " Options for encrypting:\n"
            "\n"
            " -encrypt : Indicate that encryption is required.\n"
            "\n"
            " -sym_key FILE : The file which contains base64 encoded AES256 sym key.\n"
            "\n"
            " -file FILE: file that needs to be encrypted.\n"
            "\n"
            " Will generate FILE_enc - the encrypted file.\n"
            "\n"
            " **************************************************\n"
            " Options for derypting:\n"
            "\n"
            " -decrypt : Indicate that decryption is required.\n"
            "\n"
            " -sym_key FILE : The file which contains base64 encoded AES256 sym key.\n"
            "\n"
            " -file FILE: file that needs to be decrypted.\n"
            "\n"
            " Will generate FILE_dec - the decrypted file.\n"
            "\n"
            );
    exit(1);
}


char *read_file(const char *filename)
{
    FILE *fp;
    ssize_t len;
    char buf[1000];
    ssize_t total_len = 0;
    char *ret;

    fp = fopen(filename, "r");
    if (!fp) {
        fprintf(stderr, "Cannot open %s\n", filename);
        return NULL;
    }

    while ((len = fread(buf, 1, sizeof(buf), fp))) total_len += len;
    if (!total_len) {
        fclose(fp);
        fprintf(stderr, "Empty file %s\n", filename);
        return NULL;
    }
    if (fseek(fp, 0, SEEK_SET)) {
        fclose(fp);
        return NULL;
    }
    ret = malloc(total_len + 1);
    if (ret) {
        len = fread(ret, 1, total_len, fp);
        if (len != total_len) {
            fprintf(stderr, "Cannot reread %s\n", filename);
            fclose(fp);
            free(ret);
            return NULL;
        }
        ret[total_len] = 0;
    }
    fclose(fp);
    return ret;
}

void encrypt_file_f(char *argv){
        char *buffer = NULL;
        char *out_buffer = NULL;
        char *out_buffer2 = NULL;
        struct zcrypt_key key;
        FILE *fp = NULL;
        long filelen;
        char filename_enc_dec[256] = {0};
        size_t out_len = 0;
        char decoded[ZCRYPT_KEY_SIZE] = {0};
        size_t decoded_len = 0;
        int read_bytes = 0;

        if (!config_sym_key_filename) usage(argv, "Must specify symmetric key file");
        if (!config_encrypt_filename) usage(argv, "Must specify file to be encrypted");

        fprintf(stderr, "Encrypting %s...\n", config_encrypt_filename);
        fp = fopen(config_sym_key_filename,"rb");
        if(!fp){
            fprintf(stderr, "Unable to open keyfile:%s. Exiting!!\n",strerror(errno));
            goto DONE;
        }
        if (fseek(fp, 0, SEEK_END) != 0) {
            fprintf(stderr, "Seek to end of file failed! Exiting!\n");
            goto DONE;
        }
        filelen = ftell(fp);
        if (filelen < 0) {
            fprintf(stderr, "Failed to get length of file! Exiting!\n");
            goto DONE;
        }
        if ( fseek(fp, 0L, SEEK_SET) != 0 ) {
            fprintf(stderr, "Seek to beginning failed! Exiting!\n");
            goto DONE;
        }
        buffer = (char*)malloc(filelen*sizeof(char)+1);
        if(!buffer){
            goto DONE;
        }

        memset(buffer,'\0',filelen+1);
        read_bytes = fread(buffer, 1, filelen, fp);
        if(!read_bytes){
            fprintf(stderr, "Unable to read key file:%s",strerror(errno));
            goto DONE;
        }
        buffer[read_bytes-1] = '\0';
        decoded_len = base64_decode_binary((uint8_t*)decoded, buffer, read_bytes-1);
        if (decoded_len != ZCRYPT_KEY_SIZE) {
            fprintf(stderr, "Unable to decode key. decoded size = %d, expected = %d\n", (int)decoded_len, ZCRYPT_KEY_SIZE);
            goto DONE;
        }
        memcpy(key.data, decoded, ZCRYPT_KEY_SIZE);

        fclose(fp);
        fp = NULL;
        fp = fopen(config_encrypt_filename,"rb");
        if(!fp){
           fprintf(stderr, "Unable to open file:%s:%s. Exiting!\n", config_encrypt_filename,strerror(errno));
           goto DONE;
        }
        if(fseek(fp, 0, SEEK_END)){
            fprintf(stderr, "Seek to end failed! Exiting!\n");
            goto DONE;
        }
        filelen = ftell(fp);
        if (filelen < 0) {
            fprintf(stderr, "Failed to get length of file! Exiting!\n");
            goto DONE;
        }
        if ( fseek(fp, 0L, SEEK_SET) != 0 ) {
            fprintf(stderr, "Seek to beginning failed! Exiting!\n");
            goto DONE;
        }
        out_buffer2 = (char*)malloc(filelen*sizeof(char)+1);
        out_buffer = (char*)malloc(filelen*sizeof(char)*2);
        if(!out_buffer || !out_buffer2){
            goto DONE;
        }

        read_bytes = fread(out_buffer2, 1, filelen, fp);
        if(!read_bytes){
            fprintf(stderr, "Unable to read file:%s",strerror(errno));
            goto DONE;
        }

        out_len = filelen*2;
        if(zcrypt_encrypt(&key, out_buffer2, read_bytes, out_buffer, &out_len)){
            fprintf(stderr, "Encrypt failed!\n");
            goto DONE;
        }
        fclose(fp);
        fp = NULL;

        snprintf(filename_enc_dec, 256, "%s_enc", config_encrypt_filename);
        fp = fopen(filename_enc_dec, "wb");
        if(!fp){
           fprintf(stderr, "Unable to open file:%s. Exiting!\n",filename_enc_dec);
           goto DONE;
        }
        if(fwrite(out_buffer, 1 ,  out_len, fp ) == 0){
            fprintf(stderr, "unable to write to file, error:%s",strerror(errno));
            goto DONE;
        }
        fclose(fp);
        fp = NULL;
        fprintf(stderr, "Encrypted and created %s.\n", filename_enc_dec);
   DONE:
        if(fp)
            fclose(fp);
        if(buffer)
            free(buffer);
        if(out_buffer)
            free(out_buffer);
        if(out_buffer2)
            free(out_buffer2);

}

void decrypt_file_f(char *argv){
        char *buffer = NULL;
        char *out_buffer = NULL;
        char *out_buffer2 = NULL;
        struct zcrypt_key key;
        FILE *fp = NULL;
        long filelen = 0;
        char filename_enc_dec[256] = {0};
        size_t out_len = 0;
        char decoded[ZCRYPT_KEY_SIZE] = {0};
        size_t decoded_len = 0;
        int read_bytes = 0;

        if (!config_sym_key_filename) usage(argv, "Must specify symmetric key file");
        if (!config_decrypt_filename) usage(argv, "Must specify file to be decrypted");

        fprintf(stderr, "Decrypting %s...\n", config_decrypt_filename);
        fp = fopen(config_sym_key_filename,"rb");
        if(!fp){
            fprintf(stderr, "Unable to open keyfile:%s. Exiting!!\n",strerror(errno));
            goto DONE2;
        }

        if(fseek(fp, 0, SEEK_END)){
            fprintf(stderr, "Seek to end failed! Exiting!\n");
            goto DONE2;
        }
        filelen = ftell(fp);
        if (filelen < 0) {
            fprintf(stderr, "Failed to get length of file! Exiting!\n");
            goto DONE2;
        }
        if ( fseek(fp, 0L, SEEK_SET) != 0 ) {
            fprintf(stderr, "Seek to beginning failed! Exiting!\n");
            goto DONE2;
        }
        buffer = (char*)malloc(filelen*sizeof(char)+1);
        if(!buffer){
            goto DONE2;
        }
        memset(buffer,'\0',filelen+1);
        read_bytes = fread(buffer, 1, filelen, fp);
        if(!read_bytes){
            fprintf(stderr, "Unable to read key file:%s",strerror(errno));
            goto DONE2;
        }
        buffer[read_bytes-1] = '\0';
        decoded_len = base64_decode_binary((uint8_t*)decoded, buffer, read_bytes-1);
        if (decoded_len != ZCRYPT_KEY_SIZE) {
            fprintf(stderr, "Unable to decode key. decoded size = %d, expected = %d\n", (int)decoded_len, ZCRYPT_KEY_SIZE);
            goto DONE2;
        }
        memcpy(key.data, decoded, ZCRYPT_KEY_SIZE);

        fclose(fp);
        fp = NULL;
        fp = fopen(config_decrypt_filename,"rb");
        if(!fp){
           fprintf(stderr, "Unable to open file:%s:%s. Exiting!\n", config_decrypt_filename,strerror(errno));
           goto DONE2;
        }
        if(fseek(fp, 0, SEEK_END)){
            fprintf(stderr, "Seek to end failed! Exiting!\n");
            goto DONE2;
        }
        filelen = ftell(fp);
        if (filelen < 0) {
            fprintf(stderr, "Failed to get length of file! Exiting!\n");
            goto DONE2;
        }
        if ( fseek(fp, 0L, SEEK_SET) != 0 ) {
            fprintf(stderr, "Seek to beginning failed! Exiting!\n");
            goto DONE2;
        }
        out_buffer2 = (char*)malloc(filelen*sizeof(char)+1);
        out_buffer = (char*)malloc(filelen*sizeof(char)*2);
        if(!out_buffer || !out_buffer2){
            goto DONE2;
        }

        read_bytes = fread(out_buffer2, 1, filelen, fp);
        if(!read_bytes){
            fprintf(stderr, "Unable to read file:%s",strerror(errno));
            goto DONE2;
        }
        out_len = filelen*2;
        if (zcrypt_decrypt(&key, out_buffer2, read_bytes, out_buffer, &out_len) != ZCRYPT_RESULT_NO_ERROR) {
            fprintf(stderr, "Unable to decrypt file");
            goto DONE2;
        }
        fclose(fp);
        fp = NULL;

        snprintf(filename_enc_dec, 256, "%s_dec", config_decrypt_filename);
        fp = fopen(filename_enc_dec, "wb");
        if(!fp){
           fprintf(stderr, "Unable to open file:%s:%s. Exiting!\n",filename_enc_dec,strerror(errno));
           goto DONE2;
        }
        if(fwrite(out_buffer, 1 ,  out_len, fp ) == 0){
            fprintf(stderr, "unable to write to file, error:%s",strerror(errno));
            goto DONE2;
        }
        fclose(fp);
        fp = NULL;
        fprintf(stderr, "Decrypted and created %s.\n", filename_enc_dec);
   DONE2:
        if(fp)
            fclose(fp);
        if(buffer)
            free(buffer);
        if(out_buffer)
            free(out_buffer);
        if(out_buffer2)
            free(out_buffer2);
}


int main(int argc, char *argv[])
{
    int i;
    struct zcrypt_metadata *meta;
    char str[1000];
    int file_arg = 0;

    SSL_load_error_strings();
    SSL_library_init();
    OPENSSL_no_config();
    OpenSSL_add_all_algorithms();

    for (i = 1; i < argc; i++) {
        if (strcmp(argv[i], "-sign") == 0) {
            config_sign = 1;
        } else if (strcmp(argv[i], "-verify") == 0) {
            config_verify = 1;
        } else if (strcmp(argv[i], "-encrypt") == 0) {
            encrypt_file = 1;
        } else if (strcmp(argv[i], "-decrypt") == 0) {
            decrypt_file = 1;
        }else {
            if (argv[i][0] != '-') {
                if (config_binary_filename) {
                    usage(argv[0], "Unexpected argument %s", argv[i]);
                }
                config_binary_filename = argv[i];
                fprintf(stderr, "Signing %s...\n", config_binary_filename);
            } else {
                if ((i + 1) >= argc) {
                    /* anything else needs at least two more args */
                    usage(argv[0], "Bad argument");
                }

                if (strcmp(argv[i], "-metadata") == 0) {
                    i++;
                    config_metadata_filename = argv[i];
                } else if (strcmp(argv[i], "-key") == 0) {
                    i++;
                    config_key_filename = argv[i];
                } else if (strcmp(argv[i], "-cert") == 0) {
                    i++;
                    config_cert_filename = argv[i];
                } else if (strcmp(argv[i], "-intermediate") == 0) {
                    i++;
                    config_intermediate_filename = argv[i];
                } else if (strcmp(argv[i], "-role") == 0) {
                    i++;
                    config_role = argv[i];
                } else if (strcmp(argv[i], "-platform") == 0) {
                    i++;
                    config_platform = argv[i];
                } else if (strcmp(argv[i], "-arch") == 0) {
                    i++;
                    config_arch = argv[i];
                } else if (strcmp(argv[i], "-version") == 0) {
                    i++;
                    config_version = argv[i];
                } else if (strcmp(argv[i], "-root") == 0) {
                    i++;
                    config_root = argv[i];
                } else if (strcmp(argv[i], "-sym_key") == 0) {
                    i++;
                    config_sym_key_filename = argv[i];
                } else if (strcmp(argv[i], "-file") == 0) {
                    i++;
                    file_arg = i;
                } else if (strcmp(argv[i], "-dropdb") == 0) {
                    i++;
                    dropdb = argv[i];
                } else {
                    usage(argv[0], "Bad argument: %s", argv[i]);
                }
            }
        }
    }

    if(file_arg){
        if(encrypt_file){
            config_encrypt_filename = argv[file_arg];
        }else if(decrypt_file){
            config_decrypt_filename = argv[file_arg];
        }
    }

    if (!((config_sign ^ config_verify ^ (encrypt_file || decrypt_file)) && !(config_sign && config_verify && (encrypt_file || decrypt_file)))) {
        usage(argv[0], "Exactly one of -sign, -verify, -encrypt/-decrypt must be specified");
    }

    if(config_sign || config_verify){
        if (!config_binary_filename) usage(argv[0], "Must specify binary file to sign/verify");
        if (!config_metadata_filename) usage(argv[0], "Must specify metadata file");
        if (!config_root) usage(argv[0], "Must specify root certificate file");
    }

    if (config_sign) {
        char **root_pems = NULL;
        char **chain_pems = NULL;
        int root_pems_count = 0;
        int chain_pems_count = 0;
        struct zcrypt_key key;
        struct zcrypt_rsa_key *rsa_key;
        char metabuf[65536];
        FILE *fp;

        if (!config_key_filename) usage(argv[0], "Must specify private key file");
        if (!config_cert_filename) usage(argv[0], "Must specify certificate file");
        if (!config_role) usage(argv[0], "Must specify role");
        if (!config_platform) usage(argv[0], "Must specify platform");
        if (!config_arch) usage(argv[0], "Must specify architecture");
        if (!config_version) usage(argv[0], "Must specify version");

        if (config_intermediate_filename) {
            chain_pems = zcrypt_read_pems_from_file(config_intermediate_filename, &chain_pems_count);
            if (!chain_pems) {
                fprintf(stderr, "Could not read pems from intermediate file %s\n", config_intermediate_filename);
                exit(1);
            }
            //fprintf(stderr, "Read %d pems for chain\n", chain_pems_count);
        }

        root_pems = zcrypt_read_pems_from_file(config_root, &root_pems_count);
        if (!root_pems) {
            fprintf(stderr, "Could not read pems from root file %s\n", config_root);
            exit(1);

        }
        //fprintf(stderr, "Read %d pems for root\n", root_pems_count);
        if (root_pems_count == 0) {
            fprintf(stderr, "Must have at least one root certificate\n");
            exit(1);
        }

        if (zcrypt_gen_key_file(&key, config_binary_filename)) {
            fprintf(stderr, "Could not hash binary file\n");
            exit(1);
        }

        char *cert = read_file(config_cert_filename);
        if (!cert) {
            exit(1);
        }
        meta = zcrypt_metadata_create((uint8_t *)&key,
                                       sizeof(key),
                                       config_platform,
                                       config_arch,
                                       config_role,
                                       config_version,
                                       dropdb,
                                       cert,
                                       chain_pems,
                                       chain_pems_count);
        if (!meta) {
            fprintf(stderr, "Could not create metadata with given input\n");
            exit(1);
        }

        rsa_key = zcrypt_rsa_key_create();
        if (!rsa_key) {
            fprintf(stderr, "Cannot create rsa key\n");
            exit(1);
        }
        if (zcrypt_rsa_key_read_priv(NULL, rsa_key, config_key_filename, 0)) {
            fprintf(stderr, "Cannot read private key from %s\n", config_key_filename);
            exit(1);
        }

        if (zcrypt_metadata_sign(meta, rsa_key, root_pems, root_pems_count, str, sizeof(str))) {
            fprintf(stderr, "Metadata signing failed: %s\n", str);
            exit(1);
        }

        if (zcrypt_metadata_write(meta, metabuf, sizeof(metabuf))) {
            fprintf(stderr, "Metadata serialization failed\n");
            exit(1);
        }

        fp = fopen(config_metadata_filename, "w");
        if (!fp) {
            fprintf(stderr, "Metadata file %s could not be opened for writing\n", config_metadata_filename);
            exit(1);
        }
        size_t len = strlen(metabuf);
        if (fwrite(metabuf, len, 1, fp) != 1) {
            fprintf(stderr, "Metadata file %s failed writing\n", config_metadata_filename);
            exit(1);
        }
        fclose(fp);

        if (root_pems) zcrypt_pems_free(root_pems);
        root_pems = NULL;
        if (chain_pems) zcrypt_pems_free(chain_pems);
        chain_pems = NULL;
        zcrypt_metadata_free(meta);
        zcrypt_rsa_key_destroy(rsa_key);
        free(cert);

        fprintf(stdout, "Signing complete\n");
    } else if (config_verify){
        char **root_pems = NULL;
        int root_pems_count = 0;
        struct zcrypt_key key;
        char *metafromfile;

        root_pems = zcrypt_read_pems_from_file(config_root, &root_pems_count);
        if (!root_pems) {
            fprintf(stderr, "Could not read pems from root file %s\n", config_root);
            exit(1);

        }
        fprintf(stderr, "Read %d pems for root\n", root_pems_count);
        if (root_pems_count == 0) {
            fprintf(stderr, "Must have at least one root certificate\n");
            exit(1);
        }

        if (zcrypt_gen_key_file(&key, config_binary_filename)) {
            fprintf(stderr, "Could not hash binary file\n");
            exit(1);
        }

        metafromfile = read_file(config_metadata_filename);
        if (!metafromfile) {
            fprintf(stderr, "Could not read metadata file %s\n", config_metadata_filename);
            exit(1);
        }

        meta = zcrypt_metadata_read(metafromfile);
        if (!meta) {
            fprintf(stderr, "Could not parse metadata from file %s\n", config_metadata_filename);
            exit(1);
        }

        if (zcrypt_metadata_match_sha256(meta, &key, sizeof(key))) {
            fprintf(stderr, "Metadata does not match file %s\n", config_binary_filename);
            exit(1);
        }

        if (zcrypt_metadata_verify(meta, root_pems, root_pems_count, str, sizeof(str))) {
            fprintf(stderr, "Metadata invalid: %s\n", str);
            exit(1);
        }

        fprintf(stdout, "Verification complete.\n");
    }else if (encrypt_file){
        encrypt_file_f(argv[0]);
    }else if (decrypt_file){
        decrypt_file_f(argv[0]);
    }

    return 0;
}
