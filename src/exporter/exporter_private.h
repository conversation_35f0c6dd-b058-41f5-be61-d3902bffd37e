/*
 * exporter_private.h. Copyright (C) 2017 Zscaler Inc. All Rights Reserved
 */

#ifndef __EXPORTER_PRIVATE_H__
#define __EXPORTER_PRIVATE_H__

#include <sys/socket.h>
#include <openssl/ssl.h>
#include <event2/event.h>
#include "zhash/zhash_table.h"
#include "zpath_misc/zmicro_heap.h"
#include "zpath_misc/zmicro_hash.h"
#include "fohh/http_parser.h"
#include "exporter/exporter_guac_parser.h"
#include "exporter/exporter_websocket_parser.h"
#include "zpn/zpn_fohh_client_exporter.h"
#include "zpn/zpn_lib.h"
#include "argo/argo.h"

extern const char *exporter_uportal_ba_app_suffix;
extern const char *exporter_uportal_pra_host_suffix;
extern const char *exporter_uportal_host_suffix;

extern const char *exporter_auth_domain;
extern const char *exporter_saml_auth_domain;
extern const char *exporter_broker_domain;
extern const char *exporter_exporter_domain;

extern char *exporter_cookie_domain;
extern char *exporter_cookie_crypto;

#define EXPORTER_CRYPTO_STORE_KEY_ID "ZPNCustomerCrypto"

#define EXPORTER_COOKIE_MAX_SIZE 256

#define EXPORTER_REQUEST_HEAP_START_SIZE (64*1024)
#define EXPORTER_REQUEST_HEAP_MAX_SIZE (5*1024*1024)

/* unified-portal - Limit to 1 MB only - Due to existing issue if heap is resized we get a crash in ARGO */
#define EXPORTER_RESPONSE_HEAP_START_SIZE (1024*1024)
#define EXPORTER_RESPONSE_HEAP_MAX_SIZE (1024*1024)

#define EXPORTER_MAX_THREADS 128

/* unified-portal */
/*
 * Browsers do impose cookie limits. A browser should be able to
 * accept at least 300 cookies with a maximum size of 4096 bytes,
 * as stipulated by RFC 2109 (#6.3), RFC 2965 (#5.3), and RFC 6265.
 * (including all parameters, so not just the value itself).
 */

/*
 From RFC 2109
6.3  Implementation Limits

   Practical user agent implementations have limits on the number and
   size of cookies that they can store.  In general, user agents' cookie
   support should have no fixed limits.  They should strive to store as
   many frequently-used cookies as possible.  Furthermore, general-use
   user agents should provide each of the following minimum capabilities
   individually, although not necessarily simultaneously:

      * at least 300 cookies

      * at least 4096 bytes per cookie (as measured by the size of the
        characters that comprise the cookie non-terminal in the syntax
        description of the Set-Cookie header)

      * at least 20 cookies per unique host or domain name

   User agents created for specific purposes or for limited-capacity
   devices should provide at least 20 cookies of 4096 bytes, to ensure
   that the user can interact with a session-based origin server.

   The information in a Set-Cookie response header must be retained in
   its entirety.  If for some reason there is inadequate space to store
   the cookie, it must be discarded, not truncated.

   Applications should use as few and as small cookies as possible, and
   they should cope gracefully with the loss of a cookie.

*/

/*
 * From RFC1035

 2.3.4. Size limits

 Various objects and parameters in the DNS have size limits.  They are
 listed below.  Some could be easily changed, others are more
 fundamental.

 labels          63 octets or less
 names           255 octets or less

 The labels must follow the rules for ARPANET host names.  They must
 start with a letter, end with a letter or digit, and have as interior
 characters only letters, digits, and hyphen.  There are also some
 restrictions on the length.  Labels must be 63 characters or less.

 */

#define EXPORTER_DOMAIN_NAME_MAX_SIZE 255
#define EXPORTER_LABEL_NAME_MAX_SIZE 63
#define MANAGED_BA_TLD_APP_MAX_SIZE 48 //max is for 'pra.<cloud>.zscalerportal.net' (exporter_uportal_pra_host_suffix)

/*
 * Max size of HTTP response header for curl is 307200, based on 256 headers
 * Max size of each header is 307200/256 = 1200 bytes
 *
 * If we sent more data than this we get below error
 * curl: (56) Too large response headers: 310192 > 307200
 *
 * Similar restriction is with browsers also
 *
 * 256 headers in response is a huge number with each length around 1200 bytes
 *
 * We will terminate response if headers go beyond 256, and if size if higher than 307200 bytes
 * curl/browsers themselves terminate response with error "Header too large"
 *
 * Safari is able to load 256 respone headers with each cookie 4096 bytes - 1 MB
 *
 * Due to this reason we shall restrict upto 1MB for Heap Size and 256 for response headers
 *
 */

#define EXPORTER_RESPONSE_MAX_HEADERS 256 /* As per RFC 300, but 256 is also a huge number */
#define EXPORTER_REQUEST_MAX_HEADERS  32

#define IS_HTTPS_DOMAIN(r)            ( r->conn->sni && r->conn->exporter_domain && r->conn->exporter_domain->domain ? 1 : 0 )
#define IS_UNIFIED_PORTAL(r)          ( (IS_HTTPS_DOMAIN(r) && r->conn->exporter_domain->is_ot && r->conn->exporter_domain->user_portal_gid && is_unified_portal_feature_enabled(r->conn->exporter_domain->customer_gid)) ? 1 : 0 )
#define IS_MANAGED_UNIFIED_PORTAL(r)  ( (IS_UNIFIED_PORTAL(r) && r->conn->exporter_domain->is_managed_ba) ? 1 : 0 )
#define IS_MANAGED_APP(r)             ((IS_HTTPS_DOMAIN(r) && (r->conn->exporter_domain->is_managed_ba && r->conn->exporter_domain->cfg_domain && 0 == r->conn->exporter_domain->is_ot && 0 == r->conn->exporter_domain->is_user_portal && 0 == r->conn->exporter_domain->is_auth_domain)) ? 1 : 0 )
#define IS_HTTP_REDIRECT_CODE(x) ((x>299)&&(x<400))  /*HTTP redirection 300-399 */

#define EXPORTER_SESSION_PROCTORING_FREE_SLOTS 6

#define EXPORTER_URL_MAX_ENCODE_SIZE 2048
#define EXPORTER_MAX_HEADER_LINE_SIZE 2048

#define HTTP_RESPONSE_MAX_BUFFER_DATA        (64*1024)

/* Magic cookie used for identifying exporter path. 64 bits is pretty
 * random. This was generated by openssl rand */
#define EXPORTER_MAGIC "IevTunx4Bg"

/* 0 = session cookie. Kind of bad to make cookies live excessively
 * long. The crypto cookie is a per-browser cookie that we want to
 * live a very long time */
#define EXPORTER_COOKIE_DEFAULT_LIFETIME_S 0
#define EXPORTER_COOKIE_CRYPTO_LIFETIME_S (86400*365*5)

#define EXPORTER_COOKIE_DOMAIN_X "d" EXPORTER_MAGIC
#define EXPORTER_COOKIE_DOMAIN exporter_cookie_domain
#define EXPORTER_COOKIE_SESSION "s" EXPORTER_MAGIC
#define EXPORTER_COOKIE_CRYPTO_X "c" EXPORTER_MAGIC
#define EXPORTER_COOKIE_CRYPTO exporter_cookie_crypto

#define EXPORTER_GDCOOKIE_DOMAIN "gd" EXPORTER_MAGIC

#define EXPORTER_DOMAIN_AUTH exporter_auth_domain

/* unified-portal */
#define EXPORTER_DOMAIN_UNIFIED_PORTAL_SUFFIX_BA    exporter_uportal_ba_app_suffix
#define EXPORTER_DOMAIN_UNIFIED_PORTAL_SUFFIX_PRA   exporter_uportal_pra_host_suffix
#define EXPORTER_DOMAIN_UNIFIED_PORTAL_SUFFIX_UP    exporter_uportal_host_suffix

#define EXPORTER_QUERY_NAME_ORIGINAL_URL "origurl"
#define EXPORTER_QUERY_NAME_REQUIRE_REAUTH "reauth" // = true
#define EXPORTER_QUERY_NAME_TO_SP_REDIRECT_URL "redrurl"
#define EXPORTER_QUERY_NAME_TO_SP_CUSTOMER_DOMAIN "domain"
#define EXPORTER_QUERY_NAME_FROM_SP_OBJECT_KEY "oskey"
#define EXPORTER_QUERY_NAME_FROM_SP_ENCRYPTED_OBJECT_KEY "eoskey"
#define EXPORTER_QUERY_NAME_FROM_SP_ORIGINAL_URL "origurl"
#define EXPORTER_QUERY_NAME_FROM_SP_ERR_CODE "ecode"
#define EXPORTER_QUERY_NAME_FROM_SP_ERR_STRING "emsg"
#define EXPORTER_QUERY_NAME_IDP_SELECT_DOMAIN "domain"
#define EXPORTER_QUERY_NAME_TO_SP_SSOTYPE "ssotype"
#define EXPORTER_QUERY_NAME_TO_SP_SSOTYPE_SELF "exporter"
#define EXPORTER_QUERY_NAME_TO_SP_CRYPTO_KEY "ek"
#define EXPORTER_QUERY_NAME_TO_DOMAIN_SET_COOKIE "cookie"
#define EXPORTER_QUERY_NAME_TO_DOMAIN_SET_CRYPTO_COOKIE "cryptcookie"
#define EXPORTER_QUERY_NAME_TO_DOMAIN_SET_ENCRYPTED_COOKIE "ecookie"
#define EXPORTER_QUERY_NAME_TO_LOGIN_HINT "login_hint"
#define EXPORTER_QUERY_NAME_ASSERTION_COOKIE "acookie"
#define EXPORTER_CORS_TOKEN "ctoken"
#define EXPORTER_CBI_PROOF "proof"
#define EXPORTER_QUERY_NAME_TO_IDP_ID "idpid"
#define EXPORTER_QUERY_NAME_TO_SP_VERSION "v2"
#define EXPORTER_QUERY_NAME_TO_SP_OS_ENCKEY "crn"
#define EXPORTER_QUERY_NAME_TO_SP_CRYPTO_ENCKEY "crk"
#define EXPORTER_QUERY_NAME_TO_SP_CRYPTO_IV "cri"
#define EXPORTER_QUERY_NAME_FROM_CAA_GKEY "gkey"
#define EXPORTER_QUERY_NAME_CUSTOMER_GID "customer_gid"
#define EXPORTER_QUERY_NAME_TO_SP_LEVELID "levelId"

#ifdef HAVE_EXPORTER_INSTANCE_NAME
#define EXPORTER_SERVER_NAME "exporter/1.0"
#else
#define EXPORTER_SERVER_NAME "ZBA/1.0"
#endif
#define EXPORTER_VIA_STRING "1.1 Zscaler"
#define MAX_AGE_HSTS "31536000"  // 1 year = 60 * 60 * 24 * 365

#define EXPORTER_RESTRICTIVE_CSP "Content-Security-Policy: default-src 'none'; connect-src 'self' wss://%s https://caa.%s; font-src 'self'; img-src data:; script-src 'self'; style-src 'self' 'unsafe-inline'\r\n"

/* unified-portal - Add wildcard managed user portals since this will be CORS request from unified portal */
#define EXPORTER_RESTRICTIVE_CSP_UNIFIED_PORTAL "Content-Security-Policy: default-src 'none'; connect-src 'self' wss://%s https://caa.%s https://*.%s %s%s https://%s; font-src 'self'; img-src 'self' data:; script-src 'self'; style-src 'self' 'unsafe-inline'\r\n"
/* ***************************  Begin Debugging MACROS ****************************** */

/* Uncomment this if we want to enable debugging */
//#define EXPORTER_DEBUGGING

#define EXPORTER_OBJ_VALID_SIGNATURE    0xcafebabe
#define EXPORTER_OBJ_INVALID_SIGNATURE  0xdeadbeef

#ifdef EXPORTER_DEBUGGING

#define EXPORTER_OBJ_DEBUG_INFO                 \
    uint32_t signature;                         \
    void *stack[ZTHREAD_STACK_SIZE];            \
    uint8_t trace_ix;                           \
    uint32_t trace_line[256];                   \
    const char *trace_file[256];                \
    const char *trace_thread[256];              \
    uint64_t trace_time[256];

#define EXPORTER_OBJ_VALID(__x, __file, __line) do {                \
        assert((__x)->signature == EXPORTER_OBJ_VALID_SIGNATURE);   \
        uint8_t ix;                                                 \
        ix = __sync_add_and_fetch_1(&((__x)->trace_ix), 1);         \
        (__x)->trace_file[ix] = __file;                             \
        (__x)->trace_line[ix] = __line;                             \
        (__x)->trace_time[ix] = epoch_us();                         \
        (__x)->trace_thread[ix] = zthread_name();                   \
    } while (0);

#define SET_EXPORTER_OBJ_VALID(x) do {                              \
        (x)->signature = EXPORTER_OBJ_VALID_SIGNATURE;              \
    } while (0)

#define SET_EXPORTER_OBJ_INVALID(x) do {                            \
        (x)->signature = EXPORTER_OBJ_INVALID_SIGNATURE;            \
    } while (0)

#define SET_EXPORTER_BACKTRACE(x) do {                              \
        backtrace(&((x)->stack[0]), ZTHREAD_STACK_SIZE);            \
    } while (0)

#else

#define EXPORTER_OBJ_DEBUG_INFO
#define EXPORTER_OBJ_VALID(__x, __file, __line)
#define SET_EXPORTER_OBJ_VALID(x)
#define SET_EXPORTER_OBJ_INVALID(x)
#define SET_EXPORTER_BACKTRACE(x)
#endif

/* ***************************  End of Debugging MACROS ****************************** */

/* This path is used on reception to a customer domain when the system
 * is trying to install a cookie on a customer domain.
 */
#define EXPORTER_PATH_CUSTOMER_DOMAIN_INSTALL_COOKIE "/" EXPORTER_MAGIC "/setcdcookie"

/*
 * This path is used on reception to our auth domain when we are
 * checking if the user is already authenticated
 *
 */
#define EXPORTER_PATH_AUTH_DOMAIN_FROM_CUSTOMER_DOMAIN "/" EXPORTER_MAGIC "/doauth"
#define EXPORTER_PATH_AUTH_DOMAIN_FROM_UNIFIED_PORTAL "/" EXPORTER_MAGIC "/doauthup"

/*
 * This path is where the browser is redirected from SP, after SAML
 * authentication
 */
#define EXPORTER_PATH_AUTH_DOMAIN_FROM_SP "/" EXPORTER_MAGIC "/fromsp"

#define EXPORTER_PATH_IDP_QUERY           "/" EXPORTER_MAGIC "/exporter_idp_query.js"

/* Query and Paths for canvas fingerprint */
#define EXPORTER_PATH_CANVAS_FINGERPRINT_QUERY          "/" EXPORTER_MAGIC "/exporter_csp.js"
#define EXPORTER_PATH_GET_CANVAS_FINGERPRINT            "/" EXPORTER_MAGIC "/getcsp"
#define EXPORTER_PATH_GET_CANVAS_FINGERPRINT_GEO        "/" EXPORTER_MAGIC "/getcsp-g"
#define EXPORTER_PATH_GET_CANVAS_FINGERPRINT_ENC        "/" EXPORTER_MAGIC "/getcsp-e"
#define EXPORTER_PATH_GET_CANVAS_FINGERPRINT_GEO_ENC    "/" EXPORTER_MAGIC "/getcsp-ge"
#define EXPORTER_PATH_STORE_CANVAS_FINGERPRINT_FROMSP   "/" EXPORTER_MAGIC "/setcspfromsp"
#define EXPORTER_PATH_STORE_CANVAS_FINGERPRINT_DOAUTH   "/" EXPORTER_MAGIC "/setcspdoauth"
#define EXPORTER_PATH_STORE_CANVAS_FINGERPRINT_CONT     "/" EXPORTER_MAGIC "/setcspcontinous"
#define EXPORTER_PATH_CSP_EXCEPTION_FINGERPRINT_FROMSP  "/" EXPORTER_MAGIC "/fromsp2"
#define EXPORTER_PATH_CSP_EXCEPTION_FINGERPRINT_DOAUTH  "/" EXPORTER_MAGIC "/doauth2"
#define EXPORTER_PATH_CSP_EXCEPTION_FINGERPRINT_CONT    "/" EXPORTER_MAGIC "/setcspcontinous2"
#define EXPORTER_PATH_SET_GDCOOKIE                      "/" EXPORTER_MAGIC "/setgdcookie"

/*
 * This path is where a simple http health chek can be performed. The
 * response is an empty 200 status
 */
#define EXPORTER_PATH_ALIVE "/" EXPORTER_MAGIC "/alive"


#define EXPORTER_PATH_SAML_AUTH_PATH "/auth/v2/login"

#define EXPORTER_MAX_BUFFER_USER_DATA        (128*1024)      /* We buffer max 128 KB from user */
#define EXPORTER_OT_MAX_BUFFER_USER_DATA     (7*1024*1024)   /* We buffer max 7 MB from user in OT Exporter*/

#define EXPORTER_PATH_GOOGLE_POSTURE "/start"

ZTAILQ_HEAD(exporter_domain_head, exporter_domain);
ZTAILQ_HEAD(exporter_conn_waiting_head, exporter_conn_waiting);
ZTAILQ_HEAD(exporter_request_head, exporter_request);
ZTAILQ_HEAD(exporter_conn_head, exporter_conn);

/*
 * Some of the states a request can be in while deciding what to do with an HTTP request
 */
enum exporter_request_async_state {
    async_state_min_invalid = -1,
    /* State when performing async requests. This state helps control
     * process resumption. */
    async_state_reprocess = 0,
    async_state_from_sp_wrote_session,
    async_state_refresh_auth,            /* Used to indicate that
                                          * existing auth is too old
                                          * and must be refreshed */
    async_state_reprocess_portal_api,
    async_state_reprocess_guac_api,
    async_state_done,

    async_state_response_done,
    async_state_close_client_conn,

    async_state_mt_connection_err,

    async_state_mt_remote_disconnect,

    async_state_hard_err,                /* Generally means the TCP
                                          * connection to the browser
                                          * is gone, or some other
                                          * situation that would cause
                                          * that to need to occur. */
    async_state_reprocess_priv_capability,
    async_state_reprocess_file_scan,
    async_state_get_assertion,
    async_state_fetch_policy,

    /* States for CSP Handling */
    async_state_csp_from_sp_process_data,
    async_state_csp_from_sp_process_data_wrote_session,
    async_state_csp_from_sp_process_data_wrote_domain,
    async_state_csp_fetch_profile,
    async_state_csp_fetch_policy,
    async_state_csp_doauth_process_data,
    async_state_csp_doauth_get_session,
    async_state_csp_doauth_wrote_session,
    async_state_csp_doauth_wrote_domain,
    async_state_csp_periodic_process_data,
    async_state_csp_periodic_get_session,
    async_state_csp_periodic_wrote_domain,
    async_state_csp_periodic_wrote_session,
    async_state_csp_get_nonce,
    async_state_csp_get_ctx_domain,
    /* States for CSP Handling */

    async_state_fetch_privileged_credentials,
    async_state_store_guac_sess_data,
    async_state_check_user_shared_session,
    async_state_reprocess_privileged_file_upload_request,
    async_state_refresh_auth_levelid,
    async_state_max_invalid,
};
const char * exporter_request_async_state_get_str(enum exporter_request_async_state state);

/*
   Sec-Fetch-Mode: cors
   Sec-Fetch-Mode: navigate
   Sec-Fetch-Mode: no-cors
   Sec-Fetch-Mode: same-origin
   Sec-Fetch-Mode: websocket
 */
enum http_sec_fetch_mode {
    HTTP_SEC_FETCH_MODE_CORS,
    HTTP_SEC_FETCH_MODE_NAVIGATE,
    HTTP_SEC_FETCH_MODE_NO_CORS,
    HTTP_SEC_FETCH_MODE_SAME_ORIGIN,
    HTTP_SEC_FETCH_MODE_WEBSOCKET,
    HTTP_SEC_FETCH_MODE_INVALID,
    HTTP_SEC_FETCH_MODE_NOT_PRESENT
};

/*
   Sec-Fetch-Site: cross-site
   Sec-Fetch-Site: same-origin
   Sec-Fetch-Site: same-site
   Sec-Fetch-Site: none
 */
enum http_sec_fetch_site {
    HTTP_SEC_FETCH_SITE_CROSS_SITE,
    HTTP_SEC_FETCH_SITE_SAME_ORIGIN,
    HTTP_SEC_FETCH_SITE_SAME_SITE,
    HTTP_SEC_FETCH_SITE_NONE,
    HTTP_SEC_FETCH_SITE_INVALID,
    HTTP_SEC_FETCH_SITE_NOT_PRESENT
};

enum http_sec_fetch_dest {
    HTTP_SEC_FETCH_DEST_DOCUMENT,
    HTTP_SEC_FETCH_DEST_INVALID,
    HTTP_SEC_FETCH_DEST_NOT_PRESENT,
};


enum exporter_request_input_state {
    /* State when accumulating a full HTTP request (without body. Body
     * can arrive later) */
    input_state_get_request,

    /* State where we have full HTTP request (perhaps without body)
     * but we have not yet fetched all the data we need to complete
     * this request. In particular asynchronous requests for cookies,
     * assertions, etc may all be in process */
    input_state_process_request,

    /* State where we are simply waiting for the request to complete
     * before sending a response. (Generally redirect or such,
     * associated with authentication) */
    input_state_drain,
    input_state_drain_and_process_request_data,

    /*
     * Note: Some events occur asynchronous to these state changes. In
     * particular, whether a full request/response has been seen is
     * indicated by the variables http_request_complete and
     * http_response_complete.
     */
};

enum zpa_cors_token_status {
    ZPA_CORS_TOKEN_MIN_INVALID = -1,
    ZPA_CORS_TOKEN_NOT_REQUIRED = 0,
    ZPA_CORS_TOKEN_CREATED,
    ZPA_CORS_TOKEN_CREATION_FAILED,
    ZPA_CORS_TOKEN_VALID,
    ZPA_CORS_TOKEN_INVALID,
    ZPA_CORS_TOKEN_MAX_INVALID,
};

const char* get_cors_token_status_str(enum zpa_cors_token_status);

/*
 *  User portal API types
 */
enum zpa_user_portal_api_type {
    ZPA_USER_PORTAL_API_TYPE_INVALID,

    ZPA_USER_PORTAL_API_TYPE_INDEX,
    ZPA_USER_PORTAL_API_TYPE_USER,
    ZPA_USER_PORTAL_API_TYPE_COMPANY,
    ZPA_USER_PORTAL_API_TYPE_COMPANY_LOGO,
    ZPA_USER_PORTAL_API_TYPE_PORTAL,
    ZPA_USER_PORTAL_API_TYPE_PORTAL_LOGO,
    ZPA_USER_PORTAL_API_TYPE_AUP,
    ZPA_USER_PORTAL_API_TYPE_LINKS,
    ZPA_USER_PORTAL_API_TYPE_LINKS_ICONS,
    ZPA_SRA_PORTAL_API_TYPE_CONSOLE_LINKS_ICONS,
    ZPA_USER_PORTAL_API_TYPE_ZAPP_LINKS,
    ZPA_USER_PORTAL_API_TYPE_LINK_ICON,
    ZPA_USER_PORTAL_API_TYPE_LOGOUT,
    ZPA_SRA_PORTAL_API_TYPE_GUAC_WEBSOCKET_TUNNEL,
    ZPA_SRA_PORTAL_API_TYPE_GUAC_JS,
    ZPA_SRA_PORTAL_API_TYPE_ZCONSOLE,
    ZPA_SRA_PORTAL_API_TYPE_CONSOLE_INFO,
    ZPA_SRA_PORTAL_API_TYPE_GUAC_PRA_CONSOLE_JS,
    ZPA_SRA_PORTAL_API_TYPE_GUAC_PRA_CONSOLE_CSS,
    ZPA_SRA_PORTAL_API_TYPE_GUAC_PRA_CONSOLE_WOFF2,
    ZPA_SRA_PORTAL_API_TYPE_GUAC_PRA_CONSOLE_INTER_REGULAR_WOFF2,
    ZPA_SRA_PORTAL_API_TYPE_GUAC_PRA_CONSOLE_INTER_REGULAR_WOFF,
    ZPA_SRA_PORTAL_API_TYPE_GUAC_PRA_CONSOLE_FA_SOLID_WOFF2,
    ZPA_SRA_PORTAL_API_TYPE_GUAC_PRA_CONSOLE_FA_SOLID_TTF,
    ZPA_SRA_PORTAL_API_TYPE_SHARED_CONSOLE_LINKS_ICONS,
    ZPA_SRA_PORTAL_API_TYPE_SCOPE_LINKS_ICONS,
    ZPA_SRA_PORTAL_API_TYPE_ZSCOPE,
    ZPA_SRA_PORTAL_API_TYPE_HTTP_PROXY,
    ZPA_SRA_PORTAL_API_TYPE_MY_FILES_PROXY,
    ZPA_SRA_PORTAL_API_TYPE_MY_FILES_VIEW_UNINSPECTED,
    ZPA_SRA_PORTAL_API_TYPE_MY_FILES_DOWNLOAD_FILE_ADMIN,
    ZPA_SRA_PORTAL_API_TYPE_MY_FILES_DELETE_FILE,
    ZPA_SRA_PORTAL_API_TYPE_MY_FILES_UPLOAD_INTIATE,
    ZPA_SRA_PORTAL_API_TYPE_MY_FILES_UPLOAD,
    ZPA_SRA_PORTAL_API_TYPE_MY_FILES_UPLOAD_COMPLETE,
    ZPA_SRA_PORTAL_API_TYPE_MY_FILES_UPLOAD_ABORT,
    ZPA_SRA_PORTAL_API_TYPE_PRIVILEGED_APPROVALS_REQUESTABLE_CONSOLES,
    ZPA_SRA_PORTAL_API_TYPE_APPROVAL_SCOPES_LINKS_ICONS,
    ZPA_SRA_PORTAL_API_TYPE_PRIVILEGED_APPROVALS,
    ZPA_SRA_PORTAL_API_TYPE_PRIVILEGED_APPROVAL_REQUEST,
    ZPA_SRA_PORTAL_API_TYPE_GET_PRIVILEGED_APPROVAL_REQUESTS,
    ZPA_SRA_PORTAL_API_TYPE_PRIVILEGED_DESKTOPS,
    ZPA_SRA_PORTAL_API_TYPE_PRIVILEGED_DESKTOPS_AMI_LIST,
    ZPA_SRA_PORTAL_API_TYPE_PRIVILEGED_DESKTOPS_VM,
    ZPA_SRA_PORTAL_API_TYPE_PRIVILEGED_DESKTOPS_ACTIVE_LIST,
    ZPA_SRA_PORTAL_API_TYPE_PRIVILEGED_DESKTOPS_DJBINFO,
    ZPA_USER_PORTAL_API_TYPE_LAST
};

/*
 * Guacamole API types
 */
enum zpa_guac_api_type {
    ZPA_GUAC_API_TYPE_INVALID,

    ZPA_GUAC_API_TYPE_WEBSOCKET_TUNNEL,

    ZPA_GUAC_API_TYPE_LAST
};


/* User portal API Version - List all valid versions here and handle them in exporter_get_user_portal_api_version() */
enum zpa_user_portal_api_version {
    ZPA_USER_PORTAL_API_VERSION_INVALID = 0,
    ZPA_USER_PORTAL_API_VERSION_1       = 1
};

/* Portal api initial version is 1 */
#define ZPA_USER_PORTAL_API_CURRENT_VERSION         ZPA_USER_PORTAL_API_VERSION_1

struct exporter_request_policy_state;
/* Forward declaration for portal_info */
struct exporter_user_portal_request_info;
struct exporter_user_portal_request_user_state;
/* Forward declaration for guac_info */
struct exporter_guac_request_info;
struct djb_data;

/* Handy domain macros */
#define EXPORTER_GET_REQUEST_DOMAIN(request)        ((request != NULL) && (request->conn != NULL) && (request->conn->exporter_domain != NULL)) ? (request->conn->exporter_domain->domain) : NULL
#define EXPORTER_GET_REQUEST_DOMAIN_LEN(request)    ((request != NULL) && (request->conn != NULL) && (request->conn->exporter_domain != NULL)) ? (request->conn->exporter_domain->domain_len) : 0

#define EXPORTER_PRA_BROWSER_NOTIFICATIONS_MAX_LIMIT 16

struct session_share_context {
    unsigned  key_mouse_enabled:1;
    unsigned  user_connect_allowed:1;
    unsigned  user_diag_sent:1;
    char      *infer_key;
    uint32_t  diag_entry_count;
    /* For session proctoring join event */
    struct    argo_object *old_session_data;
    struct    exporter_shared_session_data_participant *participant; /* from orig_request->old_session_data */
    struct    evbuffer *browser_notification_evbuf;
    int       browser_notification_cnt;
    char      *browser_notification[EXPORTER_PRA_BROWSER_NOTIFICATIONS_MAX_LIMIT];
};

enum conn_webserver_type {
    exporter_conn_webserver_type_portal_page = 1,
    exporter_conn_webserver_type_session_store,
    exporter_conn_webserver_type_pra_service,
    exporter_conn_webserver_type_pra_desktops,
    exporter_conn_webserver_type_max,
};

struct exporter_caa_data {     /* _ARGO: object_definition */
    char *redurl;              /* _ARGO: string */
    char *origurl;             /* _ARGO: string */
    int64_t customer_gid;      /* _ARGO: integer */
};

/*
 * Exporter_request:
 *
 * Owned by Conn, Itself (async_count != 0), zpa_client (via mtunnel)
 *
 * Can only be released if async count drops to zero and mt is NULL.
 */
struct exporter_request {
    /* The list of requests on a connection */
    ZTAILQ_ENTRY(exporter_request) list;

    char *name;
    char *nameid;

    /* Lock for accessing data in this request. */
    zpath_mutex_t lock;

    struct http_parser_url url_parser;

    /* The conn of which this parser is a member. Conn contains true
     * remote IP address, sni, etc. */
    struct exporter_conn *conn;

    /* The number of this request within the connection */
    int request_num;

    /* We can get the scope GID directly from
     * the attribute to scope hash table
     */
    int64_t scope_gid;
    char attr[MAX_ATTR_SIZE]; /* [attr name]|[attr value] */

    /* a heap for processing the entire HTTP request header. This is
     * the max amount of data we allow. We reset the heap for every
     * request. Contains header fields, etc. Allocated, must be
     * freed */
    /* For the request header, we store all the relevant data here in
     * the form of parser state, url, and header fields. We do not
     * store the original request in its original form */
    struct zmicro_heap heap;

    /* The GID that this request matches from publishing space... */
    int64_t publish_gid;

    /* Offsets into heap_buf: */
    int url_ofs;
    int header_name_ofs[EXPORTER_REQUEST_MAX_HEADERS];
    int header_value_ofs[EXPORTER_REQUEST_MAX_HEADERS];
    int total_headers;  /*request hdrs */

    int resp_header_name_ofs[EXPORTER_RESPONSE_MAX_HEADERS];
    int resp_header_value_ofs[EXPORTER_RESPONSE_MAX_HEADERS];
    int total_resp_headers;  /*response headers */
    int response_status_code;
    size_t response_len; /* of each evbuf chunk */
    size_t resp_hdr_len;

    /* References into header data. These fields are only populated
     * once the request headers have been completely parsed. */
    const char *url;
    size_t url_len;

    const char *header_sec_fetch_mode;
    const char *header_sec_fetch_site;
    const char *header_sec_fetch_dest;
    const char *header_accept;
    const char *header_cookie;
    const char *header_xff;
    const char *header_forwarded;
    const char *header_host;
    const char *header_user_agent;
    const char* header_origin;
    const char* header_acrm;
    const char* header_acrh;
    const char *header_via;
    const char *header_connection_keep_alive;
    const char *authorization;
    const char *response_status_str;

    enum http_sec_fetch_mode sec_fetch_mode;
    enum http_sec_fetch_site sec_fetch_site;
    enum http_sec_fetch_dest sec_fetch_dest;
    int cors_request;
    int cors_request_with_token;

    /* Cached request parsing state (it gets tossed by parser, so we
     * need to copy it) */
    int req_http_minor;
    int req_http_major;
    enum http_method req_method;

    struct evbuffer *request_body;
    struct evbuffer *response_data;
    struct evbuffer *mged_ba_response_evbuf;

    struct evbuffer *generated_request_data;

    /*
     * Note: For response_complete, there may still be data in
     * response_data! Response_complete just means that we have
     * buffered + sent it 'all', not that we have sent it all.
     */
    unsigned http_request_complete:1;
    unsigned http_response_complete:1;
    unsigned http_upgrade:1;
    unsigned http_request_generated:1;
    unsigned mt_destroy_sent:1;
    unsigned mt_release_received:1;
    unsigned data_sent_to_client:1;
    unsigned is_proxy_conn:1;
    unsigned join_event:1;
    unsigned http_edit_response_from_server:1;
    unsigned http_response_generated:1;
    unsigned is_proxy_conn_async;
    struct   session_share_context share_ctxt;
    int64_t  proxy_conn_reauth_timeout;

    /*
     * This is set when either the broker(in case of clientless apps) or webserver(in case of user portal) needs to be
     * unblocked from reading pov. Read blocking is done to throttle the amount of bytes that are outstanding in
     * exporter.
     */
    unsigned need_to_unblock_source:1;
    /* Request is authenticated */
    unsigned is_authenticated:1;
    /* Flag to skip response data dump */
    unsigned http_res_skip_raw_data_dump:1;

    enum conn_webserver_type webserver_type;
    enum exporter_request_input_state input_state;
    /*
     * To Do: Auth state + back end connection state
     */

    struct zpn_fohh_client_exporter_mt *mt;
    int64_t mt_incarnation;

    struct evbuffer *new_header;

    /* Outbound parser state - parse response from server*/
    struct http_parser parser;

    /*
     * The number of asynchronous calls this request has outstanding. Note that you can have only one async call
     * outstanding at any point in time. Also note that async_count is closely tied to async_state => to help drive
     * the internal state machine. If your locking is not related to async_state, consider using ref_count.
     */
    enum exporter_request_async_state async_state;
    int32_t async_count;

    /* if this object is passed around to another thread context reference it here */
    int ref_count;

    /* Used for callback processing. */
    char cb_session_cookie[EXPORTER_COOKIE_MAX_SIZE]; /* session cookie */
    char cb_domain_cookie[EXPORTER_COOKIE_MAX_SIZE]; /* domain cookie */

    struct zpn_http_trans_log log;

    /*
     * If CSP domain key entry is found in fingerprint data object store
     * then this flag will be set. This flag shall be used in sending
     * the log to producer.
     */
    uint8_t  is_csp_log;

    /* CSP */

    /* If URL is for CSP processing */
    unsigned is_csp_request:1;

    /*
     * If MONITOR is enabled for APP segment in /fromsp
     * When we come in body cb for /fromsp we don't want to fetch
     * policy again hence this is needed to store APP monitor status
     * Based on this m_bfp is also populated in domain
     */
    unsigned is_mon_enabled:1;

    /*
     * If CSP feature is enabled for request
     * At entry point of URL processing we set this from config override
     * In between request processing even if CSP is turned OFF, this request
     * will continue to complete with CSP ON
     */
    unsigned is_csp_enabled:1;
    unsigned is_collect_location:1;
    unsigned is_csp_exception:1;
    unsigned js_encryption_enabled:1;
    unsigned is_guac_sess_proctoring_request:1;
    unsigned is_guac_sess_primary_exporter:1;
    unsigned is_large_portal_request:1;
    unsigned is_djb:1;
    /*
     * All CSP paths are auth domain and APP domain comes in origurl
     * We need to extract this and store so that policy checks can
     * be done on origurl and not on SNI
     */
    char orig_domain[EXPORTER_URL_MAX_ENCODE_SIZE];
    char orig_url[EXPORTER_URL_MAX_ENCODE_SIZE];
    int64_t orig_customer_gid;
    int orig_application_port_he;

    char assertion_key[EXPORTER_URL_MAX_ENCODE_SIZE];
    int64_t criteria_flags_mask;
    int64_t csp_profile_gid;
    int64_t csp_rule_gid;
    int64_t app_gid;
    struct exporter_user_csp_info   *csp_data;
    struct argo_object              *csp_os_data;

    int csp_timeout;
    /* CSP */

    /* Policy state, for doing policy evaluation */
    struct exporter_request_policy_state *policy_state;
    /* User portal state, set only for portal api requests */
    struct exporter_user_portal_request_info *portal_info;
    struct exporter_user_portal_request_user_state *user_portal_request_user_state;
    /* Guacamole info, set only for guacamole requests */
    struct exporter_guac_request_info *guac_info;
    //XXX: SESS_PROC garbage collect
    char *guac_proctored_session_id;
    enum zpa_guac_api_type guac_api_type;
    char *sra_host_protocol;
    int   sra_host_port;
    char *sra_host_fqdn;
    int64_t pra_console_id;
    char *pra_console_name;
    int64_t approval_id;
    struct argo_object *user_sessions;
    int shared_sess_count;
    int shared_has_count;
    struct exporterSharedConsoleLinksResponseInt *shared_console_links_response;
    /* For handling session proctoring join/disconnect events by the primary exporter */
    struct argo_object *cached_sess_data;
    int max_join_users_limit;

    char *sra_host_conn_security;
    struct zpn_fohh_client_exporter *zfce_exporter;
    int destroy_session_entry;

    /* Information related to SRA application */
    char *sra_zconsole_url;
    char *sra_remote_ip;
    int64_t server_gid;
    int64_t app_id;

    /*XXX SESS_PROC move this to guac_info */
    int64_t remote_exporter_id;
    char remote_exporter_domain[256];

    /* Field to indicate if the PRA request is for a Third-Party login */
    unsigned int is_ot_third_party_login;

    TAILQ_ENTRY(exporter_request)  queue_entry;
    int64_t incarnation;

    /* Debugging stuff */
    EXPORTER_OBJ_DEBUG_INFO;

    /* Google chrome posture payload */
    const struct zpn_managed_chrome_payload *gposture;
    struct argo_object *gposture_object;

    /* CAA redirection data */
    const struct exporter_caa_data *caa_data;
    struct argo_object *caa_object;
    struct zpn_managed_browser_profiles *gprofiles;

    /* PRA my-files */
    int64_t portal_policy_rule_id;
    uint64_t  portal_policy_capabilities_bitmap;
    char c_cc[CC_STR_LEN + 1];

    struct djb_data *djb_info;
};


/*
 * A connection, either TLS or not...
 *
 * Always exists until all requests are gone.
 */
struct exporter_conn {
    /* The list of connections on a thread */
    ZTAILQ_ENTRY(exporter_conn) list;
    struct exporter_thread *thread;

    struct exporter_domain *exporter_domain;

    char *name;

    /* Inbound parser state - parse bytes from client/browser */
    struct http_parser parser;

    /* Guacomole Parser - parse guacomole instructions from client/browser */
    guac_proto_parser guac_parser;

    /* Websocket Parser     */
    struct websocket_parser ws_parser;
    struct websocket_parser_settings ws_settings;

    /* Our socket. */
    int sock;

    /* The remote IP address and port */
    uint16_t remote_port_he;
    struct argo_inet remote_ip;
    struct argo_inet local_ip;

    /* The port on which the request was received */
    int local_port_he;

    /*
     * For TLS connections, will always be specified. The full SNI on
     * arrival. This is static, and must be verified against all host
     * headers, unless NULL (in which case it is not TLS).
     *
     * This field can be used to represent the the domain part of the
     * host header. (The full host header can contain a port
     * representation as in 'company.com:8443')
     */
    char *sni;
    char *sni_orig;

    unsigned char *client_hello_pkt;
    int  client_hello_pkt_len;

    /* Bufferevent (either TLS or unencrypted) for the connection */
    struct bufferevent *bev;

    /* Our requests, in-order */
    struct exporter_request_head requests;
    struct exporter_request *current_request;
    int requests_count;
    int total_requests;

    /* This number accounts for all the heap space of requests + body
     * bytes of requests. It is used to determine when we should stop
     * processing input data. It must be > max header size, or
     * progress will never be made on large header requests. */
    size_t inbound_memory;

    /* An indication that we are flushing input on this connection
     * (probably because of a parsing error) */
    unsigned flush_input:1;
    /* An indication that we have disabled traffic reception for this
     * connection because we have backed up the requests too much. */
    unsigned input_paused:1;

    /* An indication that this connection wants to/is in the process
     * of being destroyed. */
    unsigned destroying:1;

    /* Timeout for this connection. If this timer expires, the whole
     * connection will be torn down */
    struct event *timeout;
    int64_t timeout_set_s;  /* time when the timeout is set */
    int64_t timeout_s;      /* configured connection idle timeout in seconds */

    struct argo_inet remote_ip_inet;

    int32_t async_count;

    int nailed_up_mt;
    /* If true, will not reuse any mTunnel/backend-connection, driven by config-override
     * on a per customer basis.
     * Note: a nailed_up conn only ensures that certain requests (with recognized auth
     * negotiation headers) will not send subsequent requests to a mTunnel that did not
     * handle the initial request, it can, however, still pick up any mTunnel initially.
     * This flag even disables that behavior, making mTunnel-conn strictly 1-1.
     */
    int no_mt_reuse;
    int32_t connection_id;

    TAILQ_ENTRY(exporter_conn)  queue_entry;
    int64_t incarnation;

    int32_t destroy_count;

    /* Debugging stuff */
    EXPORTER_OBJ_DEBUG_INFO;
};




/*
 * List of connections waiting for SSL context. These live on
 * exporter_domain.
 */
struct exporter_conn_waiting {
    ZTAILQ_ENTRY(exporter_conn_waiting) list;
    struct sockaddr_storage sa;
    int sa_len;
    int socket;
    struct fohh_thread *thread;
    char *sni;
    char *sni_suffix;
    unsigned char *pkt;
    int pkt_len;
    struct exporter_domain *exporter_domain;
};

/*
 * Set of paths. These live on exporter_domains
 */
struct exporter_path {
    /* Path includes wildcard suffix */
    char *path;
    size_t path_len;
    int path_wildcard;

    /* The GID of this publishing entry. */
    int64_t path_gid;

    /* The domain we are a part of. Useful for lookup/removal by
     * GID */
    struct exporter_domain *domain;
};


/*
 * Allocated, and never freed. When the number of paths associated
 * drops to 0 paths this entry becomes idle.
 *
 * Global state.
 */
struct exporter_domain {
    ZTAILQ_ENTRY(exporter_domain) all_domains_list;

    /* An ID used to look up this domain in a global hash
     * table. Primarily used for callbacks */
    int64_t ephemeral_id;

    struct exporter *exporter;

    /* Key data: */
    char *domain; /* Domain name or SNI used by the browser. Same as cfg_domain for BA1.0 */
    char *cfg_domain; /* Internal App Domain name configured by admin */
    char is_deleted;
    size_t domain_len;
    unsigned domain_wildcard;

    int application_port_he;

    int64_t gid;
    int64_t customer_gid;

    int64_t cert_id;

    /* All paths, including the null (full wildcard) are here. Indexed
     * both by publishing gid and path. We use GID to remove updated
     * entries. */
    struct diamond_state *paths_by_path;
    size_t paths_count;

    /* This lock only locks the waiting list and SSL context retrieval */
    zpath_mutex_t lock;
    struct exporter_conn_waiting_head waiting_list;

    /* Null until needed/initialized. When cert_id/cert_sequence
     * changed, is freed/removed. We keep ssl_ctx around long enough
     * for two cycles, to keep there from not being one when
     * needed. */
    SSL_CTX *ssl_ctx;
    SSL_CTX *ssl_ctx_old;

    /* Time at which we transmitted our last decrypt request. We retry
     * no more often than once per EXPORTER_SSH_CRYPTO_TIMEOUT_S */
    int64_t last_decrypt_req_us;


    /* An indication that this domain is used as the authentication
     * domain */
    unsigned is_auth_domain:1;
    unsigned is_backend_tls:1;
    unsigned is_user_portal:1;
    unsigned is_ot:1; /* PRA portal */
    unsigned is_managed_ba:1; /* ZS managed BA app or User/PRA portal */
    unsigned is_tld_managed_domain:1; /* Is this a TLD special purpose BA app*/

    int ignore_trust_verification;
    int noauth_options;

    /* unified-portal - Save user portal context for PRA for Content-Security-Policy */
    int64_t user_portal_gid;
    char user_portal_host[EXPORTER_DOMAIN_NAME_MAX_SIZE+1];//extra 1 for null char
};


/* Per-thread stats */
struct exporter_stats {
    int64_t conn_current;
    int64_t conn_created;
    int64_t conn_destroy;
};

/*
 * Per-thread state for exporter. This is where stats, connections, etc all live.
 */
struct exporter_thread {
    int fohh_thread_id;

    struct exporter_stats stats;

    struct exporter_conn_head conns;

    struct event_base *ev_base;
};


struct exporter {
    /* For locking global state, such as domain state, etc */
    zpath_rwlock_t lock;

    char broker_domain_name[256];

    struct exporter_thread thread_state[EXPORTER_MAX_THREADS];

    struct fohh_generic_server *https_sni_server;
    struct fohh_generic_server *http_server;

    /* Exported domains are unique, thus per-customer lookup is not
     * required. Domains_count is used as an ID for each domain
     * created. Domains are never destroyed. (Though they might not be
     * registered with anything. The domains_hash table is indexed by
     * the ephemeral ID of an exporter_domain. The ephemeral ID is
     * just the domains_count value when the domain was
     * created/added. */
    struct diamond_state *domains;
    struct zhash_table *domains_by_ephemeral_id;
    int64_t domains_count;

    /* All the domains in list form. Note that a domain may not have
     * any paths associated with it, in which case it's just a
     * placeholder. (Keeps callbacks from being complicated) */
    struct exporter_domain_head all_domains;

    /* Exported published paths, by GID. */
    struct zhash_table *path_by_gid;

    /* Set of resources that are forbidden without auth.
     * Requests to these resources are not redirected to the
     * auth domain.
     */
    struct zhash_table *unavailable_resources;

    char* current_secret;
    char* previous_secret;

    int download_in_progress;

    /* Hash used for Exporter Session proctoring to keep conn ids*/
    struct zhash_table *proxy_conn_id;

    /* Hash used for Exporter Session proctoring to keep proxy mtunnel ids*/
    struct zhash_table *proxy_mtunnel_id;
};

extern struct exporter global_exporter;
void exporter_request_free_q_init();
void exporter_conn_free_q_init();

const char *exporter_request_input_state_get_str(struct exporter_request *req);

/* exposed for testing */
struct http_parser_settings* exporter_conn_get_parser_settings();

extern void exporter_subcomponents_init_guacd_mutex(void);


/* Enable (1) or Disable (0) monitoring of guacd processes */
extern void exporter_subcomponents_set_monitor_guacd_process(int val);

/* Get current knob status for monitoring flag */
extern int exporter_subcomponents_get_monitor_guacd_process(void);

extern void exporter_subcomponents_init_guacd_mutex(void);

#endif /* __EXPORTER_PRIVATE_H__ */
