/*
 * exporter_guac_util.h Copyright (C) 2022 Zscaler Inc. All Rights Reserved.
 */
#ifndef SRC_EXPORTER_GUAC_UTIL_H_
#define SRC_EXPORTER_GUAC_UTIL_H_

#include "exporter/exporter_guac_api.h"

#define FILE_TYPE_EXCEPTION_LIST_MAX_LENGTH 2048
#define GUAC_PATH_MAX_LEN                   1024
#define EXPORTER_SHA256_HASH_SIZE           32

#define SESSION_RECORDING_MULTIPART_PART_SZ_MB  5
#define SESSION_RECORDING_LIFETIME_YR           365
#define EXPORTER_CUSTOMER_GID_LENGTH        128

#define EXPORTER_SESSION_RECORDING_SERVICE_NAME       "service.zpa.pra.session.recording.bucket"
#define EXPORTER_MY_FILES_SERVICE_NAME                "service.zpa.pra.my_files.bucket"

#define REF_CNT_DECR(request) do {    \
        if (request->ref_count > 0) { \
            request->ref_count--;     \
        } else {                      \
            EXPORTER_LOG(AL_ERROR, "Incorrect request ref count during file transfer or session recording"); \
            assert(request->ref_count > 0);  \
        }                             \
  } while(0)

enum exporter_session_pra_kafka_event_type {
    INITIATED,
    RECORDING_COMPLETED,
    TERMINATED
};
/*
 *  Exporter session recording metadata
 */
struct exporter_guac_session_recording_metadata {/* _ARGO: object_definition */
    char     *event_type;                        /* _ARGO: string */
    char     *session_id;                        /* _ARGO: string */
    char     *s3_key;                            /* _ARGO: string */
    char     *login_username;                    /* _ARGO: string */
    char     *session_proto;                     /* _ARGO: string */
    char     *initiator_user_id;                 /* _ARGO: string */
    char     *recording_status;                  /* _ARGO: string */
    int64_t  recording_enabled;                  /* _ARGO: integer */
    int64_t  recording_lifetime_in_days;         /* _ARGO: integer */
    int64_t  customer_id;                        /* _ARGO: integer, stringify */
    int64_t  session_start_time;                 /* _ARGO: integer */
    int64_t  policy_rule_id;                     /* _ARGO: integer, stringify */
    int64_t  pra_console_id;                     /* _ARGO: integer, stringify */
    int64_t  duration_in_sec;                    /* _ARGO: integer */
    int64_t  recording_available_time;           /* _ARGO: integer */
    int64_t  size_in_bytes;                      /* _ARGO: integer */
    int64_t  session_end_time;                   /* _ARGO: integer */
    int64_t  scope_id;                           /* _ARGO: integer, stringify */
    char     *participant_list;                  /* _ARGO: string */
};

/*
 * Exporter Email notification metadata
 * NOTE : DO NOT modify fields in this structure. Fields are used in Global Email Service.
 */
struct exporter_guac_invite_user_email_metadata {       /* _ARGO: object_definition */
    char     *session_id;                        /* _ARGO: string */
    char     *pra_portal_url;                    /* _ARGO: string */
    char     *user_join_link;                    /* _ARGO: string */
    char     *host_email;                        /* _ARGO: string */
    char     *to_email;                          /* _ARGO: string */
    char     *customer_id;                       /* _ARGO: string */
    char     *first_name;                        /* _ARGO: string */
};

int is_pra_session_monitoring_globally_disabled();
int is_pra_session_monitoring_disabled(int64_t customer_gid);
int is_pra_desktops_globally_disabled();
int is_pra_desktops_disabled(int64_t customer_gid);
int is_pra_session_recording_globally_disabled();
int is_pra_session_recording_disabled(int64_t customer_gid);
uint32_t get_pra_session_recording_lifetime(int64_t customer_gid);

int exporter_get_tenant_s3url(int64_t customer_gid, char *service, char **url);
int exporter_get_tenant_session_store(int64_t customer_gid, char **url);
int is_pra_ft_globally_disabled();
int is_pra_ft_disabled(int64_t customer_gid);
int is_pra_faster_ft_disabled(int64_t customer_gid);
int get_max_file_size(int64_t customer_gid, int inspection_on);
int is_file_type_excepted_for_customer(int64_t customer_gid, const char *filetype);
int exporter_guac_send_to_browser(struct exporter_request *request, struct evbuffer *buf, int len);
int exporter_guac_send_exporter_log_data(struct exporter_request *request, const char *status);
int exporter_guac_generate_json_scan_response(struct exporter_request *request, const char *md5, char **json_response);
int exporter_guac_generate_json_capability_response(struct exporter_request * request,
                                                    int64_t capabilities_policy_bitmap,
                                                    int status,
                                                    char *response_buf,
                                                    size_t size_response_buf);
int exporter_guac_get_dir_nodes_count(struct exporter_request *request, const char *dir_list);

int is_exporter_wss_conn_alive(struct exporter_request *request);
int is_pra_clipboard_globally_disabled();
int is_pra_clipboard_disabled(int64_t customer_gid);
int get_max_clipboard_size(int64_t customer_gid);
int is_clipboard_max_char_exceeded(struct exporter_request *request, const char *data, size_t datalen);
int exporter_guac_send_recording_metadata(enum exporter_session_pra_kafka_event_type ev_type,
        struct exporter_session_recording_meta *sess_rec_meta_data);
int pra_session_recording_multipart_chunk_size();
int exporter_get_policy_name_by_rule_id(struct exporter_request *request);
int exporter_guac_update_mt_session_sharing_diag_log(struct exporter_request *request);
int exporter_send_session_sharing_log_data(struct exporter_request *request);
int exporter_log_update_json_list(struct exporter_request *request,
                                  void *participant,
                                  enum exporter_guac_json_node node);
int exporter_sess_sharing_parse_json(struct exporter_request *request, char *shared_users_list);
void exporter_request_free_session_share_log(struct exporter_request *request);
struct exporter_request *lookup_orig_guac_request(struct exporter_request *request);
int exporter_guac_send_user_email_metadata(struct exporter_request *request,
                                           char *user_email,
                                           char *sessionId,
                                           char *portalUrl);
int64_t get_allowed_large_portal_access_count();
int get_pra_session_proctoring_zpa_max_join_users_limit(int64_t customer_gid);
int is_pra_email_notification_disabled_for_customer(int64_t customer_gid);
int is_pra_global_email_service_enabled();
int exporter_conn_guac_parse_json_session_control_args(const char *session_control_buffer,
                                                  char **user_email,
                                                  char **type,
                                                  char *error_reason);
int exporter_guac_generate_json_session_control_response(struct exporter_request *request,
                                                    char *type,
                                                    char *user_email,
                                                    char *error_reason,
                                                    int status,
                                                    char *response_buf,
                                                    size_t size_response_buf);
int exporter_guac_generate_json_console_info_response(struct exporter_request * request,
                                                    char *response_buf,
                                                    size_t size_response_buf);

int is_pra_advanced_file_transfer_disabled(int64_t customer_gid);

int exporter_multipart_upload_request_append_fields(struct exporter_request *request);
int64_t exporter_portal_file_transfer_enabled(int64_t customer_gid);
int exporter_get_sha256_hash_hex_str(const char *input, size_t input_len, char *hash_hex_str, size_t hash_hex_len);
int is_pra_display_quality_disabled(int64_t customer_gid);
int is_pra_enduser_approvals_disabled(int64_t customer_gid);
int get_pra_credential_pool_api_retry_limit();
int is_pra_health_check_enabled();

int dump_json_data(struct evbuffer *body, char *buf, size_t buf_len);

#endif /* SRC_EXPORTER_GUAC_UTIL_H_ */
