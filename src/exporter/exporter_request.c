/*
 * exporter_request.c. Copyright (C) 2017 Zscaler Inc. All Rights Reserved
 */

#include <event2/event.h>
#include <event2/buffer.h>

#include "diamond/diamond.h"

#include "fohh/fohh.h"

#include "zpath_lib/zpath_local.h"
#include "zpath_lib/zpath_customer.h"
#include "zpath_lib/zpath_instance.h"
#include "zpath_lib/zpath_cloud.h"
#include "zpath_lib/zpath_config_override_keys.h"
#include "zpath_lib/et_translate.h"
#include "zpn/zpn_cbi_profile.h"
#include "zcrypt/zcrypt.h"

#include "zpn/zpn_broker_policy.h"
#include "zpn/zpn_idp.h"
#include "zpn/zpn_idp_cert.h"
#include "zsaml/zsaml.h"
#include "zpn/zpn_customer_config.h"
#include "zpn/zpn_managed_browser_profile.h"
#include "zpn/zpn_managed_chrome_extension.h"

#include "object_store/object_store.h"
#include "argo/argo.h"
#include "argo/argo_log.h"

#include "base64/base64.h"

#include "exporter/exporter.h"
#include "exporter/exporter_request.h"
#include "exporter/exporter_private.h"
#include "exporter/exporter_conn.h"
#include "exporter/exporter_domain.h"
#include "exporter/exporter_session.h"
#include "exporter/exporter_zpa.h"
#include "exporter/exporter_siem.h"
#include "exporter/exporter_fohh_worker.h"
#include "exporter/exporter_request_util.h"
#include "exporter/exporter_cors.h"
#include "exporter/exporter_cbi.h"
#include "exporter/exporter_assert.h"

/* CSP */
#include "exporter/exporter_csp.h"
#include "exporter/exporter_csp.html_generated.h"
#include "exporter/exporter_csp-g.html_generated.h"
#include "exporter/exporter_csp-e.html_generated.h"
#include "exporter/exporter_csp-ge.html_generated.h"
#include "exporter/exporter_csp.js_generated.h"
#include "exporter/exporter_context.h"
/* CSP */

#include "exporter/exporter_idp_query.html_generated.h"
#include "exporter/exporter_guac_index.html_generated.h"
#include "exporter/exporter_guac_all_min.js_generated.h"
#include "exporter/exporter_idp_query.js_generated.h"

#include "exporter/exporter_user_portal_request.h"
#include "exporter/exporter_user_portal_api.h"
#include "exporter/exporter_user_portal_conn_webserver.h"
#include "exporter/exporter_user_portal_cfg_portal.h"
#include "exporter/zpn_sra_portal.h"

#include "exporter/exporter_guac_api.h"
#include "exporter/exporter_guac_proxy.h"
#include "exporter/exporter_privileged_policy.h"
#include "exporter/exporter_guac_api_zia.h"
#include "exporter/exporter_privileged_file_system.h"

#include "exporter/exporter_request_policy_state.h"
#include "zpn/zpn_policy_engine.h"
#include "exporter/exporter_guacd.h"
#include "exporter/exporter_guac_sess_sharing.h"
#include "exporter/exporter_guac_util.h"
#include "exporter/exporter_private_compiled.h"
#include "exporter/exporter_user_portal_request_state.h"
#include "exporter/guacd_perf_limits.h"

static const char eun_sub_txt[] = {
#include "exporter/exporter_eun.html_generated_raw.h"
    , 0x00
};

char *exporter_google_posture_domain = NULL;

#define SAFE_FREE_DECODE_POSTURE(ptr)    \
    do {                                 \
        if ((ptr)) {                     \
            free((ptr));                 \
            (ptr) = NULL;                \
        }                                \
    } while (0)


#define HTTP_XLATE_MAP(XX)                      \
    XX(0, '-', "&#x2d;")                        \
    XX(1, '_', "&#x5f;")                        \
    XX(2, '.', "&#x2e;")                        \
    XX(3, '!', "&#x21;")                        \
    XX(4, '~', "&#x7e;")                        \
    XX(5, '*', "&#x2a;")                        \
    XX(6, '(', "&#40;")                         \
    XX(7, ')', "&#41;")                         \
    XX(8, '\\', "&#x27;")                       \
    XX(9, '&', "&amp;")                         \
    XX(10, '<', "&lt;")                         \
    XX(11, '>', "&gt;")                         \
    XX(12, '"', "&quot;")                       \
    XX(13, '\'', "&#x27;")                      \
    XX(14, '/', "&#x2F")


#define XX(in_num, in_char, in_str) [in_char] = in_str,
static const char *xlate_http_text[256] = {
                                           HTTP_XLATE_MAP(XX)
};
#undef XX
struct xlate_struct {
    char c;
    char *str;
};
#define XX(in_num, in_char, in_str) {in_char, in_str},
static struct xlate_struct xlate_http_struct[] = {
                                                  HTTP_XLATE_MAP(XX)
};
#undef XX

#define EXPORTER_CSP_NONCE_OBJ_STORE_STR "{\"exporter_csp_encryption_context\":{\"key\":\"%s\",\"iv\":\"%s\"}}"
#define EXPORTER_CSP_OBJECT_ENC_KEY "uWm8YIjXYFeX64EI6fqXI99eBc5NBrUJ"

#define EXPORTER_CAA_OBJ_STORE_STR "{\"exporter_caa_data\":{\"redurl\":\"%s\",\"origurl\":\"%s\",\"customer_gid\":\"%ld\"}}"

struct argo_log_collection *exporter_transaction_collection;
struct argo_log_collection *exporter_csp_collection;

char g_dummy_mtunnel_id[ZPN_MTUNNEL_ID_BYTES_TEXT + 1];
static char *idp_select_js;

typedef int (path_callback_f)(struct exporter_request *request);
struct path_callbacks {
    const char *path;
    path_callback_f *callback;
};

struct request_callback_state {
    request_callback_f *callback_f;
    struct exporter_request *request;
    void *void_data_copy;                  /* free after use */
    int64_t int_data;
};

static struct diamond_state *path_search;
static struct diamond_state *body_path_search;

/* Taken from http_parser.h, to get text names for error codes */
#define XX(num, name, string) [num]=#string,
const char *http_status_names[] =
    {
        HTTP_STATUS_MAP(XX)
    };
#undef XX
#define XX(num, name, string) [num]=#string,
const char *http_method_names[] =
    {
        HTTP_METHOD_MAP(XX)
    };
#undef XX

int exporter_request_decrypt_assertion(struct exporter_request *request,
        const char *in_assertion_key,
        const char *in_assertion,
        const char *in_cookie_crypto_value,
        uint8_t **out_unbase64,
        int *out_b64_len,
        uint8_t **out_decrypted);
int exporter_url_decode(char *str);
static int string_replace(const char *orig_str, char *new_str, size_t new_str_len, ...);
static int is_exporter_login_hint_feature_enabled(int64_t customer_gid);

static void make_cookie(const char *domain_name, const char *cookie_name, const char *cookie_value, const char* samesite_val, int64_t lifetime_s, char *str, size_t str_len, char *str_legacy, size_t str_legacy_len);
int query_string_find(const char *url, struct http_parser_url *url_parser, const char *findme, char *value, size_t value_len);
//static int query_string_remove(struct exporter_request *request, char *url, struct http_parser_url *url_parser, const char *findme);
static int find_cookie(struct exporter_request *request, const char *cookie_name, int find_newest, char *cookie_value, size_t cookie_len);
static int exporter_request_respond_no_content(struct exporter_request *request, enum http_status status, enum exporter_error_codes error);
static int exporter_request_respond_with_csv_error(struct exporter_request *request,
                                                   enum http_status status,
                                                   enum exporter_error_codes error,
                                                   const char *base_error_str,
                                                   const char *csv_result);
static int exporter_request_respond_data(struct exporter_request *request, void *data, size_t data_len);
static int exporter_request_respond_js_data(struct exporter_request *request, void *data, size_t data_len);

/* unified-portal - Browser sends HTTP_OPTIONS request for CORS request for PRA */
static int exporter_request_respond_acao_unified_portal(struct exporter_request *request,
                                              enum http_status status,
                                              enum exporter_error_codes error,
                                              int keep_alive)
{
    const char *status_name = http_status_names[(int)status] ? http_status_names[(int)status] : "unknown";
    //const char *api_name = get_portal_api_name(exporter_user_portal_request_state_get_api_type(request->portal_info));
    char *domain = EXPORTER_GET_REQUEST_DOMAIN(request);
    int total_async = 0;//exporter_user_portal_request_get_total_async_count(request);
    const char *conn_control = keep_alive ? "Keep-Alive" : "Close";

    char std_hdrs[EXPORTER_STANDARD_RESPONSE_HEADER_BUF_LEN];
    exporter_request_print_standard_response_headers(request, status, std_hdrs, sizeof(std_hdrs));

    EXPORTER_DEBUG_USER_PORTAL_API_DETAIL("%s: Returning status %s", request->name, status_name);

    /* Sample HTTP header options recommended for security audit */
    /* ============================================================
        access-control-allow-credentials: true
        access-control-allow-headers: Origin, X-Requested-With, Content-Type, Accept, Authorization
        access-control-allow-methods: POST, GET, PUT, DELETE,OPTIONS
        access-control-allow-origin: https://admin-release.dev.zpath.net
        cache-control: no-cache, no-store, max-age=0, must-revalidate
        content-type: application/json;charset=utf-8
        date: Thu, 30 May 2019 21:37:23 GMT
        expires: 0
        pragma: no-cache
        status: 200
        x-content-type-options: nosniff
        x-frame-options: DENY
        x-xss-protection: 1; mode=block
    */

    request->log.response_status = (int)status;

    if (!request->response_data) {
        request->response_data = evbuffer_new();
    }

    if (IS_UNIFIED_PORTAL(request)) {
        evbuffer_add_printf(request->response_data,
                "%s"
                "Content-Length: %d\r\n"
                "Content-Type: application/json;charset=utf-8\r\n"
                "Access-Control-Allow-Credentials: true\r\n"
                "Access-Control-Allow-Headers: Origin, X-Requested-With, Content-Type, Accept, Authorization\r\n"
                "Access-Control-Allow-Methods: POST, GET, PUT, DELETE, OPTIONS\r\n"
                "Access-Control-Allow-Origin: %s%s\r\n"
                "Connection: %s\r\n"
                "Exporter-Error-Code: %d\r\n"
                "Strict-Transport-Security: max-age=" MAX_AGE_HSTS "; includeSubDomains; preload\r\n"
                "Cache-Control: no-cache, no-store, max-age=0, must-revalidate\r\n"
                "Pragma: no-cache\r\n"
                "X-Content-Type-Options: nosniff\r\n"
                "X-Frame-Options: DENY\r\n"
                "X-XSS-Protection: 1; mode = block\r\n"
                EXPORTER_RESTRICTIVE_CSP_UNIFIED_PORTAL
                "\r\n",
                std_hdrs,
                0,
                request->header_origin ? "" : "https://", /* unified-portal */
                request->header_origin ? request->header_origin : domain, /* unified-portal */
                conn_control,
                error,
                domain,
                ZPATH_LOCAL_CLOUD_NAME,
                EXPORTER_DOMAIN_UNIFIED_PORTAL_SUFFIX_UP,
                request->conn->exporter_domain->user_portal_host[0] ? "https://" : "",
                request->conn->exporter_domain->user_portal_host[0] ? request->conn->exporter_domain->user_portal_host : "",
                EXPORTER_DOMAIN_AUTH);
    } else {
        evbuffer_add_printf(request->response_data,
                "%s"
                "Content-Length: %d\r\n"
                "Content-Type: application/json;charset=utf-8\r\n"
                "Access-Control-Allow-Credentials: true\r\n"
                "Access-Control-Allow-Headers: Origin, X-Requested-With, Content-Type, Accept, Authorization\r\n"
                "Access-Control-Allow-Methods: POST, GET, PUT, DELETE, OPTIONS\r\n"
                "Access-Control-Allow-Origin: %s%s\r\n"
                "Connection: %s\r\n"
                "Exporter-Error-Code: %d\r\n"
                "Strict-Transport-Security: max-age=" MAX_AGE_HSTS "; includeSubDomains; preload\r\n"
                "Cache-Control: no-cache, no-store, max-age=0, must-revalidate\r\n"
                "Pragma: no-cache\r\n"
                "X-Content-Type-Options: nosniff\r\n"
                "X-Frame-Options: DENY\r\n"
                "X-XSS-Protection: 1; mode = block\r\n"
                EXPORTER_RESTRICTIVE_CSP
                "\r\n",
                std_hdrs,
                0,
                request->header_origin ? "" : "https://", /* unified-portal */
                request->header_origin ? request->header_origin : domain, /* unified-portal */
                conn_control,
                error,
                domain,
                ZPATH_LOCAL_CLOUD_NAME);
     }

    /* Mark response as complete/drain to trigger connection thread to send out response */
    request->input_state = input_state_drain;
    request->http_response_complete = 1;

    EXPORTER_LOG(AL_NOTICE, "%s: portal: %s, api: %s, set response of size: %d, total async count: %d", request->name, domain, "pra_api", 0, total_async);
    exporter_conn_wake_from_other_thread(request->conn);

    return ZPATH_RESULT_NO_ERROR;
}




static int exporter_request_respond_acao_status_and_data(struct exporter_request *request, enum http_status status, enum exporter_error_codes error, void *data, size_t data_len);
/* Per-path callbacks, mostly auth. In approximately the order they normally occur, for full authentication: */
static int request_path_default_cb(struct exporter_request *request);
static int request_path_auth_domain_from_customer_domain(struct exporter_request *request);
static int request_path_auth_domain_from_unified_portal(struct exporter_request *request);
static int request_path_auth_domain_from_sp(struct exporter_request *request);
static int request_path_customer_domain_install_cookie(struct exporter_request *request);
static int request_path_idp_query(struct exporter_request *request);
static int request_path_canvas_fingerprint_js(struct exporter_request *request);
static int request_path_get_canvas_fingerprint(struct exporter_request *request);
static int request_path_get_canvas_fingerprint_geo(struct exporter_request *request);
static int request_path_get_canvas_fingerprint_enc(struct exporter_request *request);
static int request_path_get_canvas_fingerprint_geo_enc(struct exporter_request *request);
static int request_path_store_canvas_fingerprint_fromsp(struct exporter_request *request);
static int request_path_store_canvas_fingerprint_fromsp_body(struct exporter_request *request);
static int request_path_store_canvas_fingerprint_doauth(struct exporter_request *request);
static int request_path_store_canvas_fingerprint_periodic(struct exporter_request *request);
static int request_path_store_canvas_fingerprint_periodic_auth_domain(struct exporter_request *request);
static int request_path_store_canvas_fingerprint_doauth_body(struct exporter_request *request);
static int request_path_store_canvas_fingerprint_periodic_body(struct exporter_request *request);
static int request_path_csp_exception_fromsp(struct exporter_request *request);
static int request_path_csp_exception_doauth(struct exporter_request *request);
static int request_path_csp_exception_continous(struct exporter_request *request);
static int exporter_csp_encrypt_nonce(struct exporter_request *request, void *nonce_value, size_t max_size, char *encoded_nonce);
static int request_path_install_gcookie_from_caa(struct exporter_request *request);

static const char* extract_msg_between_tokens(char *buf, int size, const char* log, const char* start_token, const char* end_token);
/* Encrypt and decrypt cookies sent in the URL */
static int exporter_encrypt_dom_cookie(struct exporter_request *request, const char *domain_cookie_value, size_t max_size, char *encoded_ecookie_domain);
static int is_url_cookie_encryption_enabled(int64_t customer_gid);
static int exporter_load_policy(struct exporter_request *request,
                                const char* assertion_cookie,
                                const char* session_crypto_value);
static int exporter_read_csp_config(struct exporter_request* request);
static int exporter_csp_create_nonce(struct exporter_request *request, const char *refer_key, char *nonce, size_t nonce_len);

static int exporter_csp_handle_exception(struct exporter_request *request, int is_fromsp);
static int exporter_csp_get_encryption_context(struct exporter_request *request, const char *refer_key,
                                               char *nonce, size_t nonce_len, char* enc_key, size_t enc_key_len,
                                               char* iv, size_t iv_len);

extern int zpn_customer_config_get_by_customer_gid(int64_t scope_gid,
                                            struct zpn_customer_config **configs,
                                            size_t *count);

static int handle_pra_sftp_async_callback_on_thread(struct exporter_request *request, int64_t int_cookie);
/********************************************************************************/

static struct path_callbacks callbacks[] = {
    {"/*", request_path_default_cb},
    {EXPORTER_PATH_AUTH_DOMAIN_FROM_CUSTOMER_DOMAIN, request_path_auth_domain_from_customer_domain},
    {EXPORTER_PATH_AUTH_DOMAIN_FROM_UNIFIED_PORTAL, request_path_auth_domain_from_unified_portal},
    {EXPORTER_PATH_AUTH_DOMAIN_FROM_SP, request_path_auth_domain_from_sp},
    {EXPORTER_PATH_CUSTOMER_DOMAIN_INSTALL_COOKIE, request_path_customer_domain_install_cookie},
    {EXPORTER_PATH_IDP_QUERY, request_path_idp_query},
    /* CSP Browser fingerprinting */
    /* JS */
    {EXPORTER_PATH_CANVAS_FINGERPRINT_QUERY, request_path_canvas_fingerprint_js},
    /* HTML */
    {EXPORTER_PATH_GET_CANVAS_FINGERPRINT, request_path_get_canvas_fingerprint},
    {EXPORTER_PATH_GET_CANVAS_FINGERPRINT_GEO, request_path_get_canvas_fingerprint_geo},
    /* HTML with encryption */
    {EXPORTER_PATH_GET_CANVAS_FINGERPRINT_ENC, request_path_get_canvas_fingerprint_enc},
    {EXPORTER_PATH_GET_CANVAS_FINGERPRINT_GEO_ENC, request_path_get_canvas_fingerprint_geo_enc},
    /* XHR */
    {EXPORTER_PATH_CSP_EXCEPTION_FINGERPRINT_FROMSP, request_path_csp_exception_fromsp},
    {EXPORTER_PATH_CSP_EXCEPTION_FINGERPRINT_DOAUTH, request_path_csp_exception_doauth},
    {EXPORTER_PATH_STORE_CANVAS_FINGERPRINT_FROMSP, request_path_store_canvas_fingerprint_fromsp}, /* session + domain - cfg_change 0 */
    {EXPORTER_PATH_STORE_CANVAS_FINGERPRINT_DOAUTH, request_path_store_canvas_fingerprint_doauth}, /* get session + domain */
    {EXPORTER_PATH_CSP_EXCEPTION_FINGERPRINT_CONT, request_path_csp_exception_continous},
    {EXPORTER_PATH_STORE_CANVAS_FINGERPRINT_CONT, request_path_store_canvas_fingerprint_periodic}, /* get session + domain */
    /* CSP Browser fingerprinting */
    {EXPORTER_PATH_SET_GDCOOKIE, request_path_install_gcookie_from_caa}, /* Install gdcookie from CAA service */
};

static struct path_callbacks body_callbacks[] = {
    /* CSP Browser fingerprinting */
    {EXPORTER_PATH_STORE_CANVAS_FINGERPRINT_FROMSP, request_path_store_canvas_fingerprint_fromsp_body},
    {EXPORTER_PATH_STORE_CANVAS_FINGERPRINT_DOAUTH, request_path_store_canvas_fingerprint_doauth_body},
    {EXPORTER_PATH_STORE_CANVAS_FINGERPRINT_CONT, request_path_store_canvas_fingerprint_periodic_body},
    /* CSP Browser fingerprinting */
};

static const char *str_exporter_request_input_state[] = {
    [input_state_get_request] = "GET",
    [input_state_process_request] = "PROCESS",
    [input_state_drain] = "DRAIN",
    [input_state_drain_and_process_request_data] = "DRAIN_DATA"
};

static int g_exporter_ot_health_check_count = 0;

const char *exporter_request_input_state_get_str(struct exporter_request *req)
{
    int result = req->input_state;
    if (result >= (sizeof(str_exporter_request_input_state) / sizeof (const char *))) return "INVALID_RESULT";
    if (result < 0) return "INVALID_RESULT";
    if (str_exporter_request_input_state[result] == NULL) return "INVALID_RESULT";
    return str_exporter_request_input_state[result];
}

/* Check if clear cookie feature is enabled for this gid */
static int64_t exporter_clear_cookies_enabled_for_customer(int64_t customer_gid)
{
    int64_t config_val = zpath_config_override_get_config_int(EXPORTER_CLEAR_COOKIES_FEATURE,
                                                              &config_val,
                                                              DEFAULT_EXPORTER_CLEAR_COOKIES_FEATURE,
                                                              customer_gid,
                                                              (int64_t)0);
    return config_val ? 1 : 0;
}

/* Check if chrome-managed feature is enabled for the customer */
static int exporter_managed_chrome_enabled_for_customer(int64_t customer_gid)
{
    int64_t config_val = zpath_config_override_get_config_int(EXPORTER_MANAGED_CHROME_FEATURE,
                                                              &config_val,
                                                              EXPORTER_MANAGED_CHROME_FEATURE_DEFAULT,
                                                              customer_gid,
                                                              (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                              (int64_t)0);

    return config_val ? 1 : 0;
}

static int is_exporter_managed_chrome_globally_disabled()
{
    int64_t config_val = zpath_config_override_get_config_int(EXPORTER_MANAGED_CHROME_HARD_DISABLED,
                                                              &config_val,
                                                              EXPORTER_MANAGED_CHROME_HARD_DISABLED_DEFAULT,
                                                              (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                              (int64_t)0);

    return config_val ? 1 : 0;
}

static int64_t exporter_managed_chrome_expiry_time_for_customer(int64_t customer_gid)
{
    int64_t config_val = zpath_config_override_get_config_int(EXPORTER_MANAGED_CHROME_EXPIRY_IN_S,
                                                              &config_val,
                                                              EXPORTER_MANAGED_CHROME_EXPIRY_IN_S_DEFAULT,
                                                              customer_gid,
                                                              (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                              (int64_t)0);

    return config_val ? config_val : EXPORTER_MANAGED_CHROME_EXPIRY_IN_S_DEFAULT;
}

static int is_customer_chrome_enterprise_enabled(struct exporter_request *request)
{
    struct zpn_customer_config *configs[ZPATH_MAX_TABLE_ROWS_GET];
    size_t count = ZPATH_MAX_TABLE_ROWS_GET;
    int i;
    int res;
    const char key[] = {"chrome.enterprise.settings.validDomains"};
    int64_t customer_gid = request->orig_customer_gid;

	/* Wally lookup from zpn_customer_config table */
	res = zpn_customer_config_load(customer_gid,
                                   exporter_request_wally_callback,
                                   request,
                                   0);
	if (res == ZPN_RESULT_ASYNCHRONOUS) {
		__sync_fetch_and_add_4(&(request->async_count), 1);
        request->async_state = async_state_reprocess;
		EXPORTER_LOG(AL_DEBUG, "%s: Asynchronous wally lookup for zpn_customer_config [%"PRId64"] - async_count (%d->%d)",
				     request->name, customer_gid, request->async_count - 1, request->async_count);
		return res;
	} else if (res != ZPATH_RESULT_NO_ERROR) {
		EXPORTER_LOG(AL_ERROR, "%s: lookup failed for zpn_customer_config [%"PRId64"], err = %s",
				     request->name, customer_gid, zpath_result_string(res));
		return res;
	}

    res = zpn_customer_config_get_by_customer_gid(customer_gid, configs, &count);
    if (res != WALLY_RESULT_NO_ERROR) {
        EXPORTER_LOG(AL_DEBUG, "customer_gid: [%"PRId64"] failed to get zpn_customer_config, res=%s",
                     customer_gid, zpath_result_string(res));
        return res;
    }

    for (i = 0; i < count; i++) {
        if (strcasecmp(configs[i]->key, key) == 0 && configs[i]->deleted == 0) {
            break;
        }
    }

    if (i >= count) {
        EXPORTER_LOG(AL_DEBUG, "For customer_gid: [%"PRId64"] key %s is not available or deleted from wally",
                     customer_gid, key);
        return ZPATH_RESULT_NOT_FOUND;
    }

    EXPORTER_LOG(AL_DEBUG, "%s: customer: [%"PRId64"] is enabled with chrome enterprise",
                 request->name, customer_gid);
    return ZPATH_RESULT_NO_ERROR;
}

int is_exporter_managed_chrome_enabled(struct exporter_request *request, uint32_t *is_mgd_chrome)
{
    int res = ZPATH_RESULT_NO_ERROR;

    *is_mgd_chrome = 0;
    if (is_exporter_managed_chrome_globally_disabled())
        return ZPATH_RESULT_NO_ERROR;
    if (!exporter_managed_chrome_enabled_for_customer(request->orig_customer_gid))
        return ZPATH_RESULT_NO_ERROR;
    res = is_customer_chrome_enterprise_enabled(request);
    if (!res)
        *is_mgd_chrome = 1;
    return res;
}

/* Check if chrome-managed-2 feature is enabled for the customer */
static int exporter_managed_chrome_2_enabled_for_customer(int64_t customer_gid)
{
    int64_t config_val = zpath_config_override_get_config_int(EXPORTER_MANAGED_CHROME_2_FEATURE,
                                                              &config_val,
                                                              EXPORTER_MANAGED_CHROME_2_FEATURE_DEFAULT,
                                                              customer_gid,
                                                              (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                              (int64_t)0);

    return config_val ? 1 : 0;
}

int is_exporter_managed_chrome_2_enabled(struct exporter_request *request, uint32_t *is_mgd_chrome)
{
    *is_mgd_chrome = 0;
    if (!exporter_managed_chrome_2_enabled_for_customer(request->orig_customer_gid))
        return ZPATH_RESULT_NO_ERROR;
    return is_exporter_managed_chrome_enabled(request, is_mgd_chrome);
}

/*
 * Called generally with more request state- cookie value, etc
 */
#if EXPORTER_USE_ZEVENT
static void request_async_callback_on_thread(struct zevent_base *zbase, void *cookie, int64_t int_cookie)
#else
static void request_async_callback_on_thread(struct fohh_thread *thread, void *cookie, int64_t int_cookie)
#endif
{
    struct exporter_request *request = cookie;
    int res = 0;

    EXPORTER_OBJ_VALID(request, __FILE__, __LINE__);

    __sync_fetch_and_sub_4(&(request->async_count), 1);

    EXPORTER_DEBUG_AUTH("%s: Asynchronous request complete, async_count (%d->%d), int_cookie = %ld, req_state: {%s,%s,%d%d,%s,%d,%d,%d}",
            request->name, request->async_count + 1, request->async_count, (long) int_cookie,
            request->log.url,
            exporter_request_input_state_get_str(request),
            request->http_request_complete, request->http_response_complete,
            exporter_request_async_state_get_str(request->async_state),
            request->async_count,
            request->request_body ? (int)evbuffer_get_length(request->request_body):-1,
            request->response_data ? (int)evbuffer_get_length(request->response_data): -1);

    /* Check if it belongs to SFTP PRA mtunnel */
    if (handle_pra_sftp_async_callback_on_thread(request, int_cookie)) return;
    /*
     * At any point in time, we expect only one outstanding to help simplify the state machine.
     */
    if (request->async_count) {
        EXPORTER_LOG(AL_ERROR, "%s: async_count = %d, ignoring the wakeup call", request->name, request->async_count);
        return;
    }

    if (int_cookie) {
        /* Note: The if statement means you cannot switch to reprocess state */
        EXPORTER_DEBUG_AUTH("%s: old state(%s), new state(%s)", request->name,
                            exporter_request_async_state_get_str(request->async_state),
                            exporter_request_async_state_get_str(int_cookie));
        request->async_state = int_cookie;
    }

    switch (request->async_state) {
    case async_state_refresh_auth:
        {
            char orig_url[EXPORTER_URL_MAX_ENCODE_SIZE];
            EXPORTER_DEBUG_AUTH("%s: Received request to requthenticate", request->name);
            if (request->sra_zconsole_url) {
	        snprintf(orig_url, sizeof(orig_url), "https://%s:%d%s", request->conn->sni, request->conn->local_port_he, request->sra_zconsole_url);
	    } else {
                snprintf(orig_url, sizeof(orig_url), "https://%s:%d%s", request->conn->sni, request->conn->local_port_he, &(request->url[request->url_parser.field_data[UF_PATH].off]));
	    }
            EXPORTER_LOG(AL_ERROR, "%s: Received request needing reauthentication. Redirecting to auth domain", request->name);
            exporter_guac_disconnect(request);
            res = exporter_request_redirect_encode(request,
                                                   NULL,  /* No cookie */
                                                   NULL,  /* No cookie */
                                                   NULL,  /* No cookie */
                                                   NULL,  /* No cookie */
                                                   EXPORTER_DOMAIN_AUTH,
                                                   443,
                                                   EXPORTER_PATH_AUTH_DOMAIN_FROM_CUSTOMER_DOMAIN,
                                                   EXPORTER_QUERY_NAME_ORIGINAL_URL, orig_url,
                                                   EXPORTER_QUERY_NAME_REQUIRE_REAUTH, "true",
                                                   NULL);
        }
        break;
    case async_state_refresh_auth_levelid:
        {
            char orig_url[EXPORTER_URL_MAX_ENCODE_SIZE];
            EXPORTER_DEBUG_AUTH("%s: Received request to requthenticate due to mismatch in levelID", request->name);
            if (request->sra_zconsole_url) {
	        snprintf(orig_url, sizeof(orig_url), "https://%s:%d%s", request->conn->sni, request->conn->local_port_he, request->sra_zconsole_url);
	        } else {
                snprintf(orig_url, sizeof(orig_url), "https://%s:%d%s", request->conn->sni, request->conn->local_port_he, &(request->url[request->url_parser.field_data[UF_PATH].off]));
	        }
            exporter_guac_disconnect(request);
            res = exporter_request_redirect_encode(request,
                                                   NULL,  /* No cookie */
                                                   NULL,  /* No cookie */
                                                   NULL,  /* No cookie */
                                                   NULL,  /* No cookie */
                                                   EXPORTER_DOMAIN_AUTH,
                                                   443,
                                                   EXPORTER_PATH_AUTH_DOMAIN_FROM_CUSTOMER_DOMAIN,
                                                   EXPORTER_QUERY_NAME_ORIGINAL_URL, orig_url,
                                                   EXPORTER_QUERY_NAME_REQUIRE_REAUTH, "true",
                                                   EXPORTER_QUERY_NAME_TO_SP_LEVELID, request->mt->stepup_auth_level_id,
                                                   NULL);
        }
        break;
		/* CSP states handling */
	case async_state_csp_periodic_process_data:
	case async_state_csp_periodic_get_session:
	case async_state_csp_periodic_wrote_domain:
	case async_state_csp_periodic_wrote_session:
	case async_state_csp_doauth_process_data:
	case async_state_csp_doauth_get_session:
	case async_state_csp_doauth_wrote_domain:
	case async_state_csp_doauth_wrote_session:
	case async_state_csp_from_sp_process_data:
	case async_state_csp_from_sp_process_data_wrote_session:
	case async_state_csp_from_sp_process_data_wrote_domain:
		res = exporter_request_process_data(request);
		break;
	case async_state_csp_fetch_profile:
	case async_state_csp_fetch_policy:
	case async_state_csp_get_nonce:
	case async_state_csp_get_ctx_domain:
		res = exporter_request_process(request);
		break;
    case async_state_store_guac_sess_data:
        res = exporter_update_pra_sess_ostore_data_create_event(request);
        break;
    case async_state_check_user_shared_session:
        res = exporter_session_share_check_user(request);
        break;
        /* CSP states handling */
    case async_state_from_sp_wrote_session:
    case async_state_reprocess:
    case async_state_get_assertion:
    case async_state_fetch_policy:
        res = exporter_request_process(request);
        break;
    case async_state_response_done:
        /*
         * If we are parsing the response of portal state, give a callback to user portal handler.
         */
        if (request->portal_info) {
            exporter_user_portal_conn_webserver_message_complete_cb(request);
        }

        break;
    case async_state_reprocess_portal_api:
        res = handle_reprocess_portal_api_request(request, NULL);
        break;
    case async_state_hard_err:
        EXPORTER_DEBUG_HTTP("%s: Received hard close, trying to close conn", request->name);
        res = ZPATH_RESULT_NO_ERROR;
        exporter_guac_disconnect(request);
        break;
    case async_state_mt_connection_err:
        exporter_guac_disconnect(request);
        if (request->guac_info && !request->guac_info->is_header && request->guac_info->async_cb) {
            res = request->guac_info->async_cb(request);
            if (res == ZPATH_RESULT_ASYNCHRONOUS) return;
            break;
        }
        res = exporter_request_respond_with_csv_error(request,
                                                  HTTP_STATUS_INTERNAL_SERVER_ERROR,
                                                  EXPORTER_ERROR_CODE_CONNECTION_SETUP_FAILED,
                                                  "Could not set up connection to server",
                                                  request->log.mt_reason ? request->log.mt_reason : "Internal Server Error");
        break;
    case async_state_close_client_conn:
        res = ZPATH_RESULT_NO_ERROR;
        break;

    case async_state_mt_remote_disconnect:
        exporter_guac_disconnect(request);
        if (request->data_sent_to_client) {
            /* We are in the middle of sending response, so just abort it */
            request->http_response_complete = 1;
            request->log.rsp_rx_done_us = epoch_us();
        } else {
            if (request->guac_info && !request->guac_info->is_header && request->guac_info->async_cb) {
                res = request->guac_info->async_cb(request);
                if (res == ZPATH_RESULT_ASYNCHRONOUS) return;
                break;
            }
            /* We haven't sent anything back yet, send a response to user */
            res = exporter_request_respond_with_csv_error(request,
                                                      HTTP_STATUS_INTERNAL_SERVER_ERROR,
                                                      EXPORTER_ERROR_CODE_DISCONNECTED_FROM_SERVER,
                                                      "Disconnected from server",
                                                      request->log.mt_reason ? request->log.mt_reason : "Internal Server Error");
        }
        break;
    case async_state_reprocess_priv_capability:
        res = exporter_process_priv_capabilities(request);
        break;
    case async_state_reprocess_file_scan:
        res = exporter_zia_scan_process_cb(request, 0);
        break;
    case async_state_fetch_privileged_credentials:
        res = exporter_reprocess_fetch_privileged_credentials(request);
        if (res != ZPATH_RESULT_ERR) {
            // To avoid reprocessing of guac instruction
            return;
        }
        break;
    case async_state_reprocess_privileged_file_upload_request:
        res = exporter_reprocess_privileged_file_upload_request(request);
        break;
    case async_state_done:
    default:
        EXPORTER_LOG(AL_ERROR, "%s: async state = %d, sending forbidden", request->name, request->async_state);
        exporter_request_respond(request, HTTP_STATUS_FORBIDDEN, EXPORTER_ERROR_CODE_INVALID_ASYNC_STATE);
        break;
    }

    if (res) {
        EXPORTER_LOG(AL_ERROR, "%s: Received error on exporter_request_async_callback: Implement me/destroying request", request->name);
        /* Best we can do is kill the request. This will kill the conn
         * if the request is not complete/safe to close */
        exporter_request_destroy(request);
    } else {
        EXPORTER_DEBUG_CONN("%s: Successful asynchronous request_process", request->name);
        res = exporter_conn_wake_from_other_thread(request->conn);
        if (res) {
            EXPORTER_LOG(AL_ERROR, "%s: Received error on conn_wake: Implement me/destroy request", request->name);
            exporter_request_destroy(request);
        } else {
            /* Happiness */
        }
    }

    return;
}


/* Returns 1 if request is indeed a PRA SFTP mtunnel request */
static int handle_pra_sftp_async_callback_on_thread(struct exporter_request *request, int64_t int_cookie)
{
    int cleanup_sftp_conn = 0;
    /* If its not related to PRA SFTP or async_count > 1, skip */
    if ((request->async_count > 1) ||
        (!request->guac_info) ||
        (!request->guac_info->gd_proxy.guacd_childproc_sftp) ||
        (request->guac_info->gd_proxy.sftp_exporter_request != request)) return 0;

    switch (int_cookie) {
        case async_state_hard_err:
        case async_state_mt_connection_err:
        case async_state_mt_remote_disconnect:
            cleanup_sftp_conn = 1;
            break;
        default:
            break;
    }
    if (!cleanup_sftp_conn) {
        EXPORTER_LOG(AL_DEBUG, "No need to clean up SFTP connection for this async state");
        return 1;
    }

    if (request->guac_info->gd_proxy.guacd_childproc_sftp &&
        request->guac_info->gd_proxy.guacd_childproc_sftp->request) {
        /* Guacd does not seem to bring down session if SFTP conn is closed
         * under load. Tear down both child connections - protocol, SFTP
         */
        exporter_guacd_child_conn_close(request->guac_info->gd_proxy.guacd_childproc_sftp->request);
        EXPORTER_LOG(AL_DEBUG, "Closing both Protocol and SFTP connections");
    } else {
        EXPORTER_LOG(AL_DEBUG, "Connections are already closed");
    }
    exporter_conn_wake_from_other_thread(request->conn);
    return 1;
}

static void exporter_request_destroy_callback_state(struct request_callback_state *state)
{
    if (state) {
        //if (state->void_data_copy) EXPORTER_FREE(state->void_data_copy);
        EXPORTER_FREE(state);
    }
}

#if EXPORTER_USE_ZEVENT
static void request_on_thread(struct zevent_base *zbase, void *cookie, int64_t int_cookie)
#else
static void request_on_thread(struct fohh_thread *thread, void *cookie, int64_t int_cookie)
#endif
{
    struct request_callback_state *state = cookie;
    int res;
    struct exporter_request *request = state->request;

    EXPORTER_DEBUG_CSP("%s: request_on_thread, async_count (%d->%d)",
                        request->name, request->async_count, request->async_count - 1);

    __sync_fetch_and_sub_4(&(request->async_count), 1);
    EXPORTER_ASSERT_SOFT((request->async_count >= 0), "Expected non-negative async_count on the request: %d",
                         request->async_count);
    res = (state->callback_f)(state->request, state->void_data_copy, state->int_data);
    if (res) {
        EXPORTER_LOG(AL_WARNING, "Request on thread failed: %s", zpath_result_string(res));
    }
    exporter_request_destroy_callback_state(state);
}

int exporter_request_on_thread(request_callback_f *callback, struct exporter_request *request, void *void_data_copy, int64_t int_data)
{
    struct request_callback_state *state;
    int res;

    EXPORTER_OBJ_VALID(request, __FILE__, __LINE__);
    EXPORTER_OBJ_VALID(request->conn, __FILE__, __LINE__);

    EXPORTER_DEBUG_CSP("%s: exporter_request_on_thread, async_count (%d->%d)",
                        request->name, request->async_count, request->async_count + 1);

    __sync_fetch_and_add_4(&(request->async_count), 1);

    state = EXPORTER_CALLOC(sizeof(*state));
    state->callback_f = callback;
    state->request = request;
    state->void_data_copy = void_data_copy;
    state->int_data = int_data;

    if (request->conn) {
#if EXPORTER_USE_ZEVENT
        res = fohh_thread_call_zevent(request->conn->thread->fohh_thread_id, request_on_thread, state, 0);
#else
        res = fohh_thread_call(request->conn->thread->fohh_thread_id, request_on_thread, state, 0);
#endif
        if (res) {
            __sync_fetch_and_sub_4(&(request->async_count), 1);
            exporter_request_destroy_callback_state(state);
        }
    } else {
        /* conn has already been detached from the request */
        EXPORTER_LOG (AL_NOTICE, "%s: Connection detached from request", request->name);
        __sync_fetch_and_sub_4(&(request->async_count), 1);
        exporter_request_destroy_callback_state(state);
        return ZPATH_RESULT_NO_ERROR;
    }
    return res;
}

void exporter_set_async_state_for_priv_capabilities(struct exporter_request *request)
{
    __sync_fetch_and_add_4(&(request->async_count), 1);
    request->async_state = async_state_reprocess_priv_capability;
    EXPORTER_LOG(AL_DEBUG, "%s: Transitioning to state async_state_reprocess_priv_capability", request->name);
}

void exporter_set_async_state_for_file_scan(struct exporter_request *request)
{
    __sync_fetch_and_add_4(&(request->async_count), 1);
    request->async_state = async_state_reprocess_file_scan;
    EXPORTER_LOG(AL_DEBUG, "%s: Transitioning to state async_state_reprocess_file_scan", request->name);
}

void exporter_set_async_state_for_reprocess_privileged_file_upload_request(struct exporter_request *request)
{
    __sync_fetch_and_add_4(&(request->async_count), 1);
    request->async_state = async_state_reprocess_privileged_file_upload_request;
    EXPORTER_LOG(AL_DEBUG, "%s: Transitioning to state async_state_reprocess_privileged_file_upload_request", request->name);
}

void exporter_set_async_state_and_inc_count(struct exporter_request *request, enum exporter_request_async_state state)
{
    __sync_fetch_and_add_4(&(request->async_count), 1);
    request->async_state = state;
}

void exporter_set_async_state(struct exporter_request *request, enum exporter_request_async_state state)
{
    request->async_state = state;
}

int exporter_request_enter_state(struct exporter_request *request, enum exporter_request_async_state state)
{
    int res;

    EXPORTER_OBJ_VALID(request, __FILE__, __LINE__);
    EXPORTER_OBJ_VALID(request->conn, __FILE__, __LINE__);

    __sync_fetch_and_add_4(&(request->async_count), 1);

    EXPORTER_DEBUG_AUTH("%s: Making thread_call to enter state %s async_count (%d->%d) req_state: {%s,%s,%d%d,%s,%d,%d,%d}",
            request->name ? request->name: "Nil" , exporter_request_async_state_get_str(state),
            request->async_count-1, request->async_count,
            request->log.url ? request->log.url : "Nil",
            exporter_request_input_state_get_str(request),
            request->http_request_complete, request->http_response_complete,
            exporter_request_async_state_get_str(request->async_state),
            request->async_count,
            request->request_body ? (int)evbuffer_get_length(request->request_body):-1,
            request->response_data ? (int)evbuffer_get_length(request->response_data): -1);

    if (request->conn) {
#if EXPORTER_USE_ZEVENT
        res = fohh_thread_call_zevent(request->conn->thread->fohh_thread_id, request_async_callback_on_thread, request, state);
#else
        res = fohh_thread_call(request->conn->thread->fohh_thread_id, request_async_callback_on_thread, request, state);
#endif
        if (res) {
            __sync_fetch_and_sub_4(&(request->async_count), 1);
            EXPORTER_LOG(AL_ERROR, "Cannot thread_call");
            return ZPATH_RESULT_ERR;
        }
    } else {
        /* conn has already been detached from the request */
        __sync_fetch_and_sub_4(&(request->async_count), 1);
        EXPORTER_LOG (AL_NOTICE, "%s: Connection detached from request", request->name);
        return ZPATH_RESULT_NO_ERROR;
    }

    return ZPATH_RESULT_NO_ERROR;
}


int exporter_request_async_callback(void *callback_data, int64_t callback_int)
{
    struct exporter_request *request;
    int res;

    /* The fact that we will make request_async_callback_on_thread with callback_data, it has to be request */
    request = callback_data;

    EXPORTER_OBJ_VALID(request, __FILE__, __LINE__);
    EXPORTER_OBJ_VALID(request->conn, __FILE__, __LINE__);

    if (request->conn) {
#if EXPORTER_USE_ZEVENT
        res = fohh_thread_call_zevent(request->conn->thread->fohh_thread_id, request_async_callback_on_thread, callback_data, 0);
#else
        res = fohh_thread_call(request->conn->thread->fohh_thread_id, request_async_callback_on_thread, callback_data, 0);
#endif
        if (res) {
            EXPORTER_LOG(AL_ERROR, "Cannot thread_call");
            return ZPATH_RESULT_ERR;
        }
    } else {
        /* conn has already been detached from the request */
        EXPORTER_LOG (AL_NOTICE, "%s: Connection detached from request", request->name);
        return ZPATH_RESULT_NO_ERROR;
    }

    return ZPATH_RESULT_NO_ERROR;
}

int exporter_request_wally_callback(void *response_callback_cookie,
                                  struct wally_registrant *registrant,
                                  struct wally_table *table,
                                  int64_t request_id,
                                  int row_count)
{
    struct exporter_request *request = response_callback_cookie;
    int res;

    EXPORTER_OBJ_VALID(request, __FILE__, __LINE__);
    EXPORTER_OBJ_VALID(request->conn, __FILE__, __LINE__);

    if (request->conn) {
#if EXPORTER_USE_ZEVENT
        res = fohh_thread_call_zevent(request->conn->thread->fohh_thread_id, request_async_callback_on_thread, response_callback_cookie, 0);
#else
        res = fohh_thread_call(request->conn->thread->fohh_thread_id, request_async_callback_on_thread, response_callback_cookie, 0);
#endif
        if (res) {
            EXPORTER_LOG(AL_ERROR, "Cannot thread_call");
            return ZPATH_RESULT_ERR;
        }
    } else {
        /* conn has already been detached from the request */
        EXPORTER_LOG (AL_NOTICE, "%s: Connection detached from request", request->name);
        return ZPATH_RESULT_NO_ERROR;
    }
    return ZPATH_RESULT_NO_ERROR;
}

/*
 * Given request, + customer_gid + session_key,
 *
 * return assertion_key, but only if the full assertion is actually available from object store.
 */
static int request_authenticate_session_key(struct exporter_request *request, int64_t customer_gid, const char *session_key, char *assertion_key_arg, size_t assertion_key_len)
{
    struct argo_object *session;
    const char *assertion_key;
    const char *assertion;
    int res;

    res = exporter_session_get(request->name,
                               session_key,
                               &session,
                               exporter_request_async_callback,
                               request,
                               0);
    if (res) {
        if (res == ZPATH_RESULT_ASYNCHRONOUS) {
            __sync_fetch_and_add_4(&(request->async_count), 1);
            EXPORTER_DEBUG_AUTH("%s: Fetching session object asynchronously, async_count(%d->%d)", request->name,
                    request->async_count -1, request->async_count);
        } else if (res == ZPATH_RESULT_NOT_FOUND) {
            /* We just redirect to auth to restart auth... */
            EXPORTER_DEBUG_AUTH("%s: Fetching session object- not found, falling through to normal auth", request->name);
        } else {
            /* Weird error? */
            EXPORTER_LOG(AL_ERROR, "%s: Bad session_get: %s", request->name, zpath_result_string(res));
        }
    } else {
        /* We have a session. */
        res = exporter_session_get_auth_assertion_key(session, customer_gid, &assertion_key);
        if (res) {
            EXPORTER_DEBUG_AUTH("%s: No assertion for customer %ld", request->name, (long) customer_gid);
        } else {
            /* We have a session, and an assertion key... Let's verify the assertion exists */
            snprintf(request->assertion_key, sizeof(request->assertion_key), "%s", assertion_key);
            res = object_store_get(request->name,
                                   assertion_key,
                                   &assertion,
                                   exporter_request_async_callback,
                                   request,
                                   0,
                                   ostore_role_object_store);

            if (res) {
                if (res == ZPATH_RESULT_ASYNCHRONOUS) {
                    __sync_fetch_and_add_4(&(request->async_count), 1);
                    EXPORTER_DEBUG_AUTH("%s: Fetching assertion asynchronously - async_count (%d->%d)",
                                        request->name, request->async_count - 1, request->async_count);
                } else if (res == ZPATH_RESULT_NOT_FOUND) {
                    EXPORTER_DEBUG_AUTH("%s: Fetching assertion- not found", request->name);
                } else {
                    EXPORTER_LOG(AL_ERROR, "%s: Bad assertion_get: %s", request->name, zpath_result_string(res));
                }
            } else {
                EXPORTER_DEBUG_AUTH("%s: Fully authenticated with assertion", request->name);

                if (assertion_key_arg) {
                    snprintf(assertion_key_arg, assertion_key_len, "%s", assertion_key);
                }
            }
        }
        exporter_session_free(session);
    }
    return res;
}

static int64_t url_get_cors_token(struct exporter_request *request, const char *url, struct http_parser_url *url_parser, const char *qs_name, char *cors_token, size_t cors_token_len, struct http_parser_url *cors_token_parser)
{
    int res;
    res = query_string_find(url, url_parser, qs_name, cors_token, cors_token_len);
    if (res) {
        EXPORTER_DEBUG_AUTH("%s: Did not find a cors token in the url", request->name);
        return 0;
    }
    return 1;
}

/*
 * Take url, extract and url decode orig_url, get customer ID from it, return customer ID and orig_url
 */
static int64_t url_get_customer_gid(struct exporter_request *request, const char *url, struct http_parser_url *url_parser, const char *qs_name, char *orig_url, size_t orig_url_len, struct http_parser_url *orig_url_parser)
{
    int res;
    int64_t customer_gid;

    /* No matter what, we need to get our customer_gid. We get this from the original url query value */
    res = query_string_find(url, url_parser, qs_name, orig_url, orig_url_len);
    if (res) {
        EXPORTER_LOG(AL_ERROR, "%s: Could not extract customer_gid from url", request->name);
        return 0;
    }
    exporter_url_decode(orig_url);
    http_parser_url_init(orig_url_parser);
    res = http_parser_parse_url(orig_url, strlen(orig_url), 0, orig_url_parser);
    if (res) {
        EXPORTER_LOG(AL_ERROR, "%s: Could not parse url", request->name);
        return 0;
    }
    if (!(orig_url_parser->field_set & (1 << UF_HOST))) {
        EXPORTER_LOG(AL_ERROR, "%s: Received request without host in original url", request->name);
        return 0;
    }

    /* We do a little verification of origurl here to make sure everything is still https, which is required. */
    if (!(orig_url_parser->field_set & (1 << UF_SCHEMA))) {
        EXPORTER_LOG(AL_ERROR, "%s: Received %s in query string without schema", request->name, qs_name);
        return 0;
    }

    if (orig_url_parser->field_data[UF_SCHEMA].len != strlen("https")) {
        EXPORTER_LOG(AL_ERROR, "%s: Received schema %.*s, expecting https", request->name, (int)orig_url_parser->field_data[UF_SCHEMA].len, &(orig_url[orig_url_parser->field_data[UF_SCHEMA].off]));
        return 0;
    }

    if (strncasecmp("https", &(orig_url[orig_url_parser->field_data[UF_SCHEMA].off]), orig_url_parser->field_data[UF_SCHEMA].len)) {
        EXPORTER_LOG(AL_ERROR, "%s: Received schema %.*s, expecting https", request->name, (int)orig_url_parser->field_data[UF_SCHEMA].len, &(orig_url[orig_url_parser->field_data[UF_SCHEMA].off]));
        return 0;
    }

    customer_gid = exporter_domain_to_customer_gid(&global_exporter, &(orig_url[orig_url_parser->field_data[UF_HOST].off]), orig_url_parser->field_data[UF_HOST].len);;
    if (!customer_gid) {
        EXPORTER_LOG(AL_ERROR, "%s: Received request with unrecognized domain in original url: <%s> <%.*s>",
                     request->name, orig_url,
                     orig_url_parser->field_data[UF_HOST].len, &(orig_url[orig_url_parser->field_data[UF_HOST].off]));
        return 0;
    }

    /* unified-portal - If we are coming from auth domain d.zpa-auth.net then origurl will be external FQDN
     * We must get internal app name from external FQDN and orig_domain must have internal app name
     */

    if (!request->orig_customer_gid) {
        request->orig_customer_gid = customer_gid;
        /* ET-58904 - Get correct port for applicatio in AUTH domain URL
         * Eg. If URL is d.zpa-auth.net then we have port 80
         * But we need port for origurl for lookup
         */
        request->orig_application_port_he = exporter_domain_to_application_port_he(&global_exporter,
                &(orig_url[orig_url_parser->field_data[UF_HOST].off]),
                orig_url_parser->field_data[UF_HOST].len);
        memset(request->orig_domain, 0, sizeof(request->orig_domain));
        memcpy(request->orig_domain, &(orig_url[orig_url_parser->field_data[UF_HOST].off]),
                orig_url_parser->field_data[UF_HOST].len);
        memset(request->orig_url, 0, sizeof(request->orig_url));
        snprintf(request->orig_url, sizeof(request->orig_url), "%s", orig_url);

        /*
         * If we are coming from CSP urls means feature was enabled
         * when JS was injected, so we must keep it ON
         */
        request->is_csp_enabled = is_csp_feature_enabled(request->orig_customer_gid) || request->is_csp_request;
        request->js_encryption_enabled = exporter_get_js_encryption_state_for_customer(request->orig_customer_gid)?1:0;

        /* If this is an exception we need to disable CSP and let the normal flow resume */
        if (request->is_csp_exception) request->is_csp_enabled = 0;
    }

    return customer_gid;
}

#define MAX_NOAUTH_URL_LEN 2000

static int exporter_validate_no_auth_options_request(struct exporter_request *request)
{
    /*
     * Add noauth request validation checks here.
     */
    if (request->req_method != HTTP_OPTIONS) {
        EXPORTER_LOG(AL_CRITICAL, "%s %s: Rejecting no auth request: Not an OPTIONS request",
                                request->name, http_method_str(request->req_method));
        return  ZPATH_RESULT_ERR;
    }

    if (request->url_len > MAX_NOAUTH_URL_LEN) {
        EXPORTER_LOG(AL_CRITICAL, "%s %s: Rejecting no auth request: Url len too large %ld",
                                request->name, http_method_str(request->req_method), (long)request->url_len);
        return  ZPATH_RESULT_ERR;
    }

    if (!request->header_origin) {
        EXPORTER_LOG(AL_CRITICAL, "%s %s: Rejecting no auth request: No origin header present",
                                request->name, http_method_str(request->req_method));
        return  ZPATH_RESULT_ERR;
    }

    if (!request->header_acrm) {
        EXPORTER_LOG(AL_CRITICAL, "%s %s: Rejecting no auth request: No Access-Control-Request-Method header",
                                request->name, http_method_str(request->req_method));
        return  ZPATH_RESULT_ERR;
    }

    if (!request->header_acrh) {
        EXPORTER_LOG(AL_CRITICAL, "%s %s: Rejecting no auth request: No Access-Control-Request-Headers header",
                                request->name, http_method_str(request->req_method));
        return  ZPATH_RESULT_ERR;
    }

    if (request->request_body && evbuffer_get_length(request->request_body)) {
        EXPORTER_LOG(AL_CRITICAL, "%s %s: Rejecting no auth request: Payload present",
                                request->name, http_method_str(request->req_method));
        return  ZPATH_RESULT_ERR;
    }

    EXPORTER_DEBUG_AUTH("%s, Valid no auth request: origin = %s, acrm = %s, acrh = %s, method = %s",
                        request->name, request->header_origin, request->header_acrm, request->header_acrh,
                        http_method_str(request->req_method));

    return ZPATH_RESULT_NO_ERROR;
}

static int exporter_request_is_currently_unavailable(struct exporter_request *request)
{
    if (zhash_table_lookup(global_exporter.unavailable_resources, request->url, strlen(request->url), NULL)) {
        EXPORTER_LOG(AL_WARNING, "Exporter to return 503: name = %s, url = %s", request->name, request->url);
        return ZPATH_RESULT_ERR;
    }
    return ZPATH_RESULT_NO_ERROR;
}

extern int is_csp_periodic_fingerprint_enabled(struct exporter_request *request);

static int exporter_request_assign_gposture(struct exporter_request *request, const char *gposture_value)
{
    struct argo_object *object = NULL;

    if (request->gposture_object && request->gposture)
        return ZPATH_RESULT_NO_ERROR;

    object = argo_deserialize_json(gposture_value, strlen(gposture_value));
    if (!object) {
        EXPORTER_LOG(AL_ERROR, "[GOOGLE_POSTURE_PARSER]: Failed to deserialize json <%s>, returning error", gposture_value);
        return ZPATH_RESULT_ERR;
    }

    request->gposture_object = object;
    request->gposture = request->gposture_object->base_structure_void;
    return ZPATH_RESULT_NO_ERROR;
}

static int exporter_request_assign_caa(struct exporter_request *request, const char *etag_caa)
{
    struct argo_object *object = NULL;

    if (request->caa_object && request->caa_data)
        return ZPATH_RESULT_NO_ERROR;

    if (!etag_caa) {
        EXPORTER_LOG(AL_ERROR, "[ETAG_CAA_PARSER]: etag_caa is NULL, returning error");
        return ZPATH_RESULT_ERR;
    }

    EXPORTER_LOG(AL_DEBUG, "[ETAG_CAA_PARSER]: etag_caa %s", etag_caa);

    object = argo_deserialize_json(etag_caa, strlen(etag_caa));
    if (!object) {
        EXPORTER_LOG(AL_ERROR, "[ETAG_CAA_PARSER]: Failed to deserialize json <%s>, returning error", etag_caa);
        return ZPATH_RESULT_ERR;
    }

    request->caa_object = object;
    request->caa_data = object->base_structure_void;
    return ZPATH_RESULT_NO_ERROR;
}

static int exporter_request_get_gkey_from_caa(struct exporter_request *request)
{
    char redirect_url[EXPORTER_URL_MAX_ENCODE_SIZE];
    char orig_url[EXPORTER_URL_MAX_ENCODE_SIZE];
    char orig_url_encoded[EXPORTER_URL_MAX_ENCODE_SIZE];
    char key_caa[EXPORTER_KEY_CAA_MAX_SIZE] = {0};
    char etag_caa[EXPORTER_COOKIE_MAX_SIZE] = {0};
    int res;

    snprintf(redirect_url, sizeof(redirect_url), "https://%s%s",
             request->conn->sni, EXPORTER_PATH_SET_GDCOOKIE);

    if (request->sra_zconsole_url) {
        snprintf(orig_url, sizeof(orig_url), "https://%s:%d%s", request->conn->sni, request->conn->local_port_he, request->sra_zconsole_url);
    } else {
        snprintf(orig_url, sizeof(orig_url), "https://%s:%d%s", request->conn->sni, request->conn->local_port_he,
                 &(request->url[request->url_parser.field_data[UF_PATH].off]));
    }

    url_encode(orig_url, orig_url_encoded, sizeof(orig_url_encoded));

    /* generate a random key of object store */
    res = object_store_gen_key(OBJECT_STORE_TYPE_CAA, NULL, etag_caa,
                               sizeof(etag_caa), ostore_role_object_store);
    if (res) {
        EXPORTER_LOG(AL_ERROR, "%s: Could not generate caa etag", request->name);
        return res;
    }

    memset(key_caa, 0, EXPORTER_KEY_CAA_MAX_SIZE);
    snprintf(key_caa, sizeof(key_caa), EXPORTER_CAA_OBJ_STORE_STR, redirect_url, orig_url_encoded, (long)request->orig_customer_gid);

    EXPORTER_LOG(AL_DEBUG, "%s, etag_caa: %s, key_caa: %s\n", request->name, etag_caa, key_caa);

    res = object_store_set(request->name, etag_caa, key_caa, NULL, NULL, 0, ostore_role_object_store);
    if (res && (res != ZPATH_RESULT_ASYNCHRONOUS)) {
        EXPORTER_LOG(AL_ERROR, "%s: Could not store key_caa into object store: %s", request->name,
                     zpath_result_string(res));
        return res;
    }

	EXPORTER_GLOBAL_USAGE_STATS_FIELD_INC(exporter_posture_to_caa_redirect);
    return exporter_request_redirect_encode_caa(request,
                                                NULL, NULL, NULL, NULL,
                                                exporter_google_posture_domain,
                                                0, EXPORTER_PATH_GOOGLE_POSTURE,
                                                "etag_caa", etag_caa,
                                                NULL);
}

static int compareVersion(char *min_ver, const char *posture_ver)
{
    int result = 0;

    /* loop through each level of the version string */
    while (result == 0) {

        /* extract leading version numbers */
        char* tail1;
        char* tail2;
        unsigned long ver1 = strtoul(min_ver, &tail1, 10 );
        unsigned long ver2 = strtoul(posture_ver, &tail2, 10 );

        /* if numbers differ, then set the result */
        if (ver1 < ver2)
            result = -1;
        else if (ver1 > ver2)
            result = +1;
        else {
            /* if numbers are the same, go to next level */
            min_ver = tail1;
            posture_ver = tail2;

            /* if we reach the end of both, then they are identical */
            if (*min_ver == '\0' && *posture_ver == '\0')
                break;

            /* if we reach the end of one only, it is the smaller */
            else if (*min_ver == '\0')
                result = -1;
            else if (*posture_ver == '\0')
                result = +1;

            /*  not at end ... so far they match so keep going */
            else {
                min_ver++;
                posture_ver++;
            }
        }
    }
    return result;
}

int exporter_request_fetch_browser_profile(struct exporter_request *request)
{
    int res = ZPATH_RESULT_NO_ERROR;
    struct zpn_managed_browser_profile *profiles[1000];
    size_t profile_count = sizeof(profiles) / sizeof(profiles[0]);
    size_t scope_profile_count = 0;
    struct zpn_managed_chrome_extension *ext_profiles[1000];
    size_t ext_profile_count = sizeof(ext_profiles) / sizeof(ext_profiles[0]);

    /* Retrieve profiles based on scope GID */
    res = zpn_managed_browser_profile_get_by_scope_gid(
            request->scope_gid,
            &(profiles[0]),
            &profile_count,
            exporter_request_wally_callback,
            request, 0);
    if (res == ZPN_RESULT_ASYNCHRONOUS) {
        EXPORTER_LOG(AL_DEBUG, "%s: Asynchronous lookup of table zpn_managed_browser_profile, async_count: (%d->%d)",
                     request->name, request->async_count, request->async_count + 1);
        return res;
    } else if (res != ZPATH_RESULT_NO_ERROR) {
        EXPORTER_LOG(AL_DEBUG, "%s: Fetching browser profile results in res: %d\n", request->name, res);
        return res;
    }

    /* Retrieve profiles based on original customer GID if different from scope GID */
    if (request->scope_gid != request->orig_customer_gid) {
        scope_profile_count = sizeof(profiles) / sizeof(profiles[0]) - profile_count;

        res = zpn_managed_browser_profile_get_by_scope_gid(
                request->orig_customer_gid,
                &(profiles[profile_count]),
                &scope_profile_count,
                exporter_request_wally_callback,
                request, 0);
        if (res == ZPN_RESULT_ASYNCHRONOUS) {
            EXPORTER_LOG(AL_DEBUG, "%s: Asynchronous lookup of table zpn_managed_browser_profile, for different scope async_count: (%d->%d)",
                         request->name, request->async_count, request->async_count + 1);
            return res;
        } else if (res != ZPATH_RESULT_NO_ERROR) {
            EXPORTER_LOG(AL_ERROR, "%s: Fetching browser profile results for different scope in res: %d\n", request->name, res);
            return res;
        }
    }

    /* Initialize gprofiles if not already initialized */
    if (!request->gprofiles) {
        request->gprofiles = (struct zpn_managed_browser_profiles *)EXPORTER_CALLOC(sizeof(struct zpn_managed_browser_profiles));
        if (!request->gprofiles) {
            EXPORTER_LOG(AL_ERROR, "%s: Not able to allocate memory for gprofiles", request->name);
            return ZPATH_RESULT_NO_MEMORY;
        }
    }

    if (!request->gprofiles->gprofile_gids) {
        request->gprofiles->gprofile_gids = EXPORTER_CALLOC(sizeof(int64_t) * (profile_count + scope_profile_count));
        if (!request->gprofiles->gprofile_gids) {
            EXPORTER_LOG(AL_ERROR, "%s: Not able to allocate memory for gprofile_gids", request->name);
            return ZPATH_RESULT_NO_MEMORY;
        }
    }
    request->gprofiles->gprofile_gid_count = 0;

    /* Iterate through profile_count for the customer and check browser_type to read extension table */
    for (size_t ii = 0; ii < (profile_count + scope_profile_count); ii++) {
         if (strcmp(profiles[ii]->browser_type, "CHROME") != 0) {
              EXPORTER_LOG(AL_DEBUG, "%s: Chrome profile is not present for the customer [%"PRId64"] for gid: %"PRId64,
                           request->name, request->orig_customer_gid, profiles[ii]->gid);
              continue;
         }

         res = zpn_managed_chrome_extension_get_by_profile_gid(
                  profiles[ii]->gid,
                  &(ext_profiles[0]),
                  &ext_profile_count,
                  exporter_request_wally_callback,
                  request, 0);
         if (res == ZPN_RESULT_ASYNCHRONOUS) {
             EXPORTER_LOG(AL_DEBUG, "%s: Asynchronous lookup of table zpn_managed_chrome_extension, async_count: (%d->%d)",
                    request->name, request->async_count, request->async_count + 1);
             return res;
         } else if (!ext_profile_count || (res != ZPATH_RESULT_NO_ERROR)) {
             EXPORTER_LOG(AL_ERROR, "%s: ext_profile_count: %zu, res: %d", request->name, ext_profile_count, res);
             return res;
         }

         if (ext_profile_count > 1) {
            EXPORTER_LOG(AL_DEBUG, "%s: ext_profile_count: %zu, [Multiple extension profile matched for profile_gid: %"PRId64": will be using the first one for profile eval]", request->name, ext_profile_count, profiles[ii]->gid);
         }

         int matched = 1;
         int match_params = 0;
         int one_attr_checked = 0;
         if (ext_profiles[0]->min_browser_version) {
             if (request->gposture->browser_version && compareVersion(ext_profiles[0]->min_browser_version, request->gposture->browser_version) <= 0) {
                 match_params = 1;
                 one_attr_checked = 1;
             } else {
                 match_params = 0;
             }
         } else {
             match_params = 1;
         }

         if (match_params) {
             if (ext_profiles[0]->key_trust_level) {
                 matched = matched && request->gposture->key_trust_level && (strcmp(request->gposture->key_trust_level, ext_profiles[0]->key_trust_level) == 0 ? 1 : 0);
                 one_attr_checked = 1;
             }

             if (ext_profiles[0]->operating_system) {
                 matched = matched && request->gposture->operating_system &&  (strcmp(request->gposture->operating_system, ext_profiles[0]->operating_system) == 0 ? 1 : 0);
                 one_attr_checked = 1;
             }

             if (ext_profiles[0]->disk_encryption) {
                 matched = matched && request->gposture->disk_encryption && (strcmp(request->gposture->disk_encryption, ext_profiles[0]->disk_encryption) == 0 ? 1 : 0);
                 one_attr_checked = 1;
             }

             if (ext_profiles[0]->os_firewall) {
                 matched = matched && request->gposture->os_firewall && (strcmp(request->gposture->os_firewall, ext_profiles[0]->os_firewall) == 0 ? 1 : 0);
                 one_attr_checked = 1;
             }

             if (ext_profiles[0]->secure_boot_mode) {
                 matched = matched && request->gposture->secure_boot_mode && (strcmp(request->gposture->secure_boot_mode, ext_profiles[0]->secure_boot_mode) == 0 ? 1 : 0);
                 one_attr_checked = 1;
             }

             if (ext_profiles[0]->screen_lock_secured) {
                 matched = matched && request->gposture->screen_lock_secured && (strcmp(request->gposture->screen_lock_secured, ext_profiles[0]->screen_lock_secured) == 0 ? 1 : 0);
                 one_attr_checked = 1;
             }

             if (ext_profiles[0]->safe_browsing_protection_level) {
                 matched = matched && request->gposture->safe_browsing_protection_level && (strcmp(request->gposture->safe_browsing_protection_level, ext_profiles[0]->safe_browsing_protection_level) == 0 ? 1 : 0);
                 one_attr_checked = 1;
             }

             if (ext_profiles[0]->crowd_strike_agent) {
                 matched = matched && request->gposture->crowd_strike_agent && (strcmp(request->gposture->crowd_strike_agent, ext_profiles[0]->crowd_strike_agent) == 0 ? 1 : 0);
                 one_attr_checked = 1;
             }

             if (matched && one_attr_checked) {
                 EXPORTER_LOG(AL_DEBUG, "%s: Profiles are matched with gid: %"PRId64, request->name, profiles[ii]->gid);
                 request->gprofiles->gprofile_gids[request->gprofiles->gprofile_gid_count] = profiles[ii]->gid;
                 request->gprofiles->gprofile_gid_count++;
                 request->gprofiles->managed_browser_payload_version = 1;
            }
         }
    }
    return res;
}

static int request_path_default_cb(struct exporter_request *request)
{
    char domain_cookie_value[EXPORTER_URL_MAX_ENCODE_SIZE];
    char cookie_crypto_value[EXPORTER_URL_MAX_ENCODE_SIZE];
    char assertion_key[EXPORTER_URL_MAX_ENCODE_SIZE];
    char session_cookie_value[EXPORTER_URL_MAX_ENCODE_SIZE];
    char domain_buf[EXPORTER_URL_MAX_ENCODE_SIZE];
    const char *domain = NULL;
    const char *session_key_with_domain;
    const char *walk;
    int require_reauth = 0;
    int res;
    struct zpath_customer *customer;
    char domain_gdcookie_value[EXPORTER_URL_MAX_ENCODE_SIZE];
    const char *gposture_value = NULL;
    char *decoded_gposture = NULL;
    uint32_t is_mgd_chrome = 0;

    EXPORTER_DEBUG_CSP("%s: request_path_default_cb req_state: {%s,%s,%d%d,%s,%d,%d,%d}",
            request->name,
            request->log.url,
            exporter_request_input_state_get_str(request),
            request->http_request_complete, request->http_response_complete,
            exporter_request_async_state_get_str(request->async_state),
            request->async_count,
            request->request_body ? (int)evbuffer_get_length(request->request_body):-1,
            request->response_data ? (int)evbuffer_get_length(request->response_data): -1);

    if (request->conn->exporter_domain->is_auth_domain) {
        EXPORTER_LOG(AL_ERROR, "%s: Received request for path %s for auth domain", request->name, request->url);
        return exporter_request_respond(request, HTTP_STATUS_NOT_FOUND, EXPORTER_ERROR_CODE_INVALID_AUTH_DOMAIN);
    }

    /* URL being requested is the the customer URL only and not AUTH domain
     * So we can get customer_gid directly from exporter_domain
     */
    if (!request->orig_customer_gid) {
        request->orig_customer_gid = request->conn->exporter_domain->customer_gid;
        snprintf(request->orig_domain, sizeof(request->orig_domain), "%s", request->conn->sni);
        request->is_csp_enabled = is_csp_feature_enabled(request->orig_customer_gid);
        request->js_encryption_enabled = exporter_get_js_encryption_state_for_customer(request->orig_customer_gid)?1:0;
        request->orig_application_port_he = request->conn->exporter_domain->application_port_he;
    }

    /* Need to fetch customer to find a customer domain */
    res = zpath_customer_get(request->conn->exporter_domain->customer_gid,
                             &customer,
                             exporter_request_wally_callback,
                             request,
                             0);
    if (res) {
        if (res == ZPATH_RESULT_ASYNCHRONOUS) {
            EXPORTER_DEBUG_AUTH("%s: Fetching customer asynchronously for customer gid = %ld - async_count (%d->%d)",
                                request->name, (long) request->conn->exporter_domain->customer_gid, request->async_count, request->async_count + 1);
            __sync_fetch_and_add_4(&(request->async_count), 1);
            return ZPATH_RESULT_NO_ERROR;
        } else {
            EXPORTER_LOG(AL_ERROR, "%s: Fetching Customer failed for customer gid = %ld: %s", request->name, (long) request->conn->exporter_domain->customer_gid, zpath_result_string(res));
            return exporter_request_respond(request, HTTP_STATUS_FORBIDDEN, EXPORTER_ERROR_CODE_CUSTOMER_NOT_FOUND);
        }
    }

    if (customer->disable_traffic) {
        EXPORTER_LOG(AL_WARNING, "%s: customer %ld has traffic disabled", request->name, (long) request->conn->exporter_domain->customer_gid);
        return exporter_request_respond(request, HTTP_STATUS_FORBIDDEN, EXPORTER_ERROR_CODE_CUSTOMER_TRAFFIC_DISABLED);
    }

    res = find_cookie(request, EXPORTER_COOKIE_CRYPTO, 0, cookie_crypto_value, sizeof(cookie_crypto_value));
    if (res == ZPATH_RESULT_NO_ERROR) {
        res = find_cookie(request, EXPORTER_COOKIE_DOMAIN, 1, domain_cookie_value, sizeof(domain_cookie_value));
        if (res == ZPATH_RESULT_NO_ERROR) {

            /* If crypto and domain cookies are present, and manage chrome hard disabled is not enabled and
             * feature flag is enabled for the customer
             */
            res = is_exporter_managed_chrome_enabled(request, &is_mgd_chrome);
            if (res != ZPATH_RESULT_NO_ERROR) {
                if (res == ZPATH_RESULT_ASYNCHRONOUS) {
                    return ZPATH_RESULT_NO_ERROR;
                } else if (res != ZPATH_RESULT_NOT_FOUND) {
                    EXPORTER_LOG(AL_ERROR, "%s: Checking managed chrome failed %s", request->name, zpath_result_string(res));
                    return res;
                }
            } else if (is_mgd_chrome) {
                res = find_cookie(request, EXPORTER_GDCOOKIE_DOMAIN, 0, domain_gdcookie_value, sizeof(domain_gdcookie_value));
                if (res != ZPATH_RESULT_NO_ERROR) {
                    EXPORTER_LOG(AL_DEBUG, "%s, EXPORTER_GDCOOKIE_DOMAIN: %s is not found, redirect to CAA",
                                 request->name, EXPORTER_GDCOOKIE_DOMAIN);
                    return exporter_request_get_gkey_from_caa(request);
                }

                /* Get Chrome Posture Value */
                res = object_store_get(request->name,
                                       domain_gdcookie_value,
                                       &gposture_value,
                                       exporter_request_async_callback,
                                       request,
                                       0,
                                       ostore_role_object_store);
                if (res) {
                    if (res == ZPATH_RESULT_ASYNCHRONOUS) {
                        __sync_fetch_and_add_4(&(request->async_count), 1);
                        EXPORTER_LOG(AL_DEBUG, "%s: Fetching gposture value asynchronously - async_count (%d->%d)",
                                     request->name, request->async_count - 1, request->async_count);
                       return ZPATH_RESULT_NO_ERROR;
                    } else if (res == ZPATH_RESULT_NOT_FOUND) {
                       gposture_value = NULL;
                       request->gposture = NULL;
                       EXPORTER_LOG(AL_ERROR, "%s: Failed to retrieve gposture value from object store", request->name);
                       EXPORTER_GLOBAL_USAGE_STATS_FIELD_INC(exporter_posture_not_found_in_object_store);
                       goto fetch_session_cookie;
                    } else {
                       gposture_value = NULL;
                       EXPORTER_LOG(AL_ERROR, "%s: Bad gposture value get: %s", request->name, zpath_result_string(res));
                       EXPORTER_GLOBAL_USAGE_STATS_FIELD_INC(exporter_posture_not_found_in_object_store);
                       goto fetch_session_cookie;
                    }
                }

                if (!gposture_value) {
                    EXPORTER_LOG(AL_ERROR, "%s, CHROME_POSTURE: gposture value with payload key not found. Unable to send Chrome Posture to the Broker", request->name);
                    EXPORTER_GLOBAL_USAGE_STATS_FIELD_INC(exporter_posture_not_found_in_object_store);
                    goto fetch_session_cookie;
                }

                EXPORTER_LOG(AL_DEBUG, "%s, CHROME_POSTURE: gposture value with payload key = %s", request->name, gposture_value);
                decoded_gposture = base64_decode(gposture_value);
                if (!decoded_gposture) {
                    EXPORTER_LOG(AL_ERROR, "%s: decoding of gposture failed", request->name);
                    goto fetch_session_cookie;
                }

                EXPORTER_LOG(AL_DEBUG, "%s, CHROME_POSTURE: decoded gposture value with payload key = %s", request->name, decoded_gposture);
                res = exporter_request_assign_gposture(request, decoded_gposture);
                if (res) {
                    EXPORTER_LOG(AL_ERROR, "%s, CHROME_POSTURE: gposture value parsing error: %s. Unable to send Chrome Posture to the Broker",
                                 request->name, zpath_result_string(res));
                    SAFE_FREE_DECODE_POSTURE(decoded_gposture);
                    EXPORTER_GLOBAL_USAGE_STATS_FIELD_INC(exporter_posture_failure_in_parsing);
                    goto fetch_session_cookie;
                }
                memset((char *) request->gposture->gd_cookie_domain_id, 0, sizeof(request->gposture->gd_cookie_domain_id));

                res = is_exporter_managed_chrome_2_enabled(request, &is_mgd_chrome);
                if (res != ZPATH_RESULT_NO_ERROR) {
                    if (res == ZPATH_RESULT_ASYNCHRONOUS) {
                        SAFE_FREE_DECODE_POSTURE(decoded_gposture);
                        return ZPATH_RESULT_NO_ERROR;
                    } else if (res != ZPATH_RESULT_NOT_FOUND) {
                        SAFE_FREE_DECODE_POSTURE(decoded_gposture);
                        EXPORTER_LOG(AL_ERROR, "%s: Checking managed chrome failed %s", request->name, zpath_result_string(res));
                        return res;
                    }
                } else if (is_mgd_chrome) {
                    get_context_gd_cookie_domain_id(domain_gdcookie_value, (char *) request->gposture->gd_cookie_domain_id);
                }
                SAFE_FREE_DECODE_POSTURE(decoded_gposture);

                EXPORTER_LOG(AL_DEBUG, "%s, CHROME_POSTURE: able to parse the posture", request->name);

                if (request->gposture && request->gposture->creation_time) {
                    int64_t creation_time = request->gposture->creation_time / 1000;
                    int64_t expiry_time = exporter_managed_chrome_expiry_time_for_customer(request->orig_customer_gid);
                    int64_t now = epoch_s();
                    if (now > (creation_time + expiry_time)) {
                        if (request->req_method == HTTP_GET && request->sec_fetch_mode == HTTP_SEC_FETCH_MODE_NAVIGATE) {
                            EXPORTER_LOG(AL_DEBUG, "%s, CHROME_POSTURE: posture is expired, redirect to CAA for the new posture", request->name);
                            return exporter_request_get_gkey_from_caa(request);
                        } else {
                            EXPORTER_LOG(AL_DEBUG, "%s, CHROME_POSTURE: posture is expired for not GET or non Navigate request, request will not be redirected to CAA, old posture will be used", request->name);
                        }
                    }
                }
            }

fetch_session_cookie:
            /* Get session cookie for this domain, if it's there. */
            res = object_store_get(request->name,
                                   domain_cookie_value,
                                   &session_key_with_domain,
                                   exporter_request_async_callback,
                                   request,
                                   0,
                                   ostore_role_object_store);

            if (res) {
                if (res == ZPATH_RESULT_ASYNCHRONOUS) {
                    __sync_fetch_and_add_4(&(request->async_count), 1);
                    EXPORTER_DEBUG_AUTH("%s: Fetching session key asynchronously - async_count (%d->%d)",
                                        request->name, request->async_count - 1, request->async_count);
                    return ZPATH_RESULT_NO_ERROR;
                } else if (res == ZPATH_RESULT_NOT_FOUND) {
                    session_key_with_domain = NULL;
                } else {
                    if (object_store_ready_count(ostore_role_object_store)) {
                        /* There is an object store... fall through to reauth. */
                        EXPORTER_LOG(AL_ERROR, "%s: Object store read failed. Restarting auth to try a better store", request->name);
                        /* Fall through to re-auth... */
                    } else {
                        /* No object stores available. We're pretty much
                         * done here. */
                        EXPORTER_LOG(AL_ERROR, "%s: No object stores available", request->name);
                        return exporter_request_respond(request, HTTP_STATUS_INTERNAL_SERVER_ERROR, EXPORTER_ERROR_CODE_OBJECT_STORES_UNAVAILABLE);
                    }
                }
            } else {
                /*
                 * Extract the session_key_with_domain into session_key and domain
                 */
                for (walk = session_key_with_domain; *walk; walk++) {
                    if ((*walk) == ',') domain = walk + 1;
                }
                if (!domain) {
                    EXPORTER_LOG(AL_WARNING, "%s: Bad object: Has no domain, reauthing", request->name);
                    /* Fall through to re-auth... */
                } else {
                    /* Extract just the beginning of the session key- the part without the domain */
                    snprintf(session_cookie_value, sizeof(session_cookie_value), "%.*s",
                            (int)((domain - 1) - session_key_with_domain), session_key_with_domain);
                    snprintf(domain_buf, sizeof(domain_buf), "%s", domain);
                    zpath_downcase(domain_buf);
                    domain = domain_buf;

                    /* Verify this key is for our domain... This is a substring match, but is probably appropriate */

                    /*
                     * Re-evaluate - substr match is a match for wildcard also, if customer has 2 domains
                     * parent.com and child.parent.com
                     * parent.com is accessed first cookies are stored with parent.com
                     * Now child.parent.com is accessed and browser since its using wildcard cookies always will send
                     * cookies for parent.com
                     * Since we check for strstr we don't generate cookies for child.parent.com and continue
                     *
                     * Ideally exporter must generate cookies for each domain different
                     *
                     * If child.parent.com is accessed first and then parent.com we generate 2 sets of cookies
                     */
                    if (!strstr(request->conn->sni, domain)) {
                        /* No match */
                        EXPORTER_LOG(AL_WARNING, "%s: Session key domain mismatch, reauthing. req SNI = %s, value = %s, domain = %s",
                                     request->name,
                                     request->conn->sni, session_key_with_domain, domain);
                        /* Fall through to re-auth... */
                    } else {
                        EXPORTER_LOG(AL_DEBUG, "[BFP_LOG] Authenticating session_key: %s domain: %s", session_cookie_value, domain);
                        res = request_authenticate_session_key(request, request->conn->exporter_domain->customer_gid, session_cookie_value, assertion_key, sizeof(assertion_key));
                        if (res) {
                            if (res == ZPATH_RESULT_ASYNCHRONOUS) {
                                EXPORTER_DEBUG_AUTH("%s: Authenticating- asynchronous fetch", request->name);
                                return ZPATH_RESULT_NO_ERROR;
                            } else {
                                /* Auth failed. */
                                EXPORTER_DEBUG_AUTH("%s: Authentication fetch failed", request->name);
                                /* Fall through to re-auth */
                            }
                        } else {
                            /* Authenticated! */
                            const char *assertion;
                            res = object_store_get(request->name,
                                                   assertion_key,
                                                   &assertion,
                                                   NULL,
                                                   NULL,
                                                   0,
                                                   ostore_role_object_store);

                            if (res) {
                                EXPORTER_LOG(AL_ERROR, "%s: Could not retrieve assertion we just retrieved...", request->name);
                                /* Fall through to reauth... */
                            } else {

                                /* Decrypt assertion if necessary */
                                char *typ = strchr(assertion_key, ',');
                                if (!typ) {
                                    EXPORTER_LOG(AL_ERROR, "%s: Could not get valid data from assertion key, doing reauth...", request->name);
                                    /* Fall through to reauth... */
                                } else {
                                    /* base64 encrypted & decrypted respectively */
                                    uint8_t *encrypted = NULL;
                                    uint8_t *decrypted = NULL;
                                    uint8_t *unbase64 = NULL;
                                    int b64_len = 0;
                                    if (strncmp(typ, ",spe,", 5) == 0) {
                                        /* Need to decrypt. */
                                        struct zcrypt_key key = {{0}};
                                        size_t str_len = strlen(cookie_crypto_value);
                                        size_t bin_len;
                                        size_t decrypted_len;
                                        char nameid[1000];
                                        char fname[1000];
                                        char lname[1000];
                                        EXPORTER_LOG(AL_CRITICAL, "Implement Me: Only decrypt once, please!!!!");
                                        if (base64_decoded_size(cookie_crypto_value, str_len) <= sizeof(struct zcrypt_key)) {
                                            base64_decode_binary((unsigned char *)&key, cookie_crypto_value, str_len);
                                        } else {
                                            EXPORTER_LOG(AL_ERROR, "%s: Crypto key too large, doing reauth...", request->name);
                                            goto auth_failed;
                                        }
                                        str_len = strlen(assertion);
                                        bin_len = base64_decoded_size(assertion, str_len);
                                        /* Allocate space for
                                         * encrypted and decrypted
                                         * assertion. Note: Decrypted
                                         * assertion will always be
                                         * smaller than encrypted
                                         * assertion due to IV */
                                        encrypted = EXPORTER_MALLOC(bin_len + 1);
                                        decrypted = EXPORTER_MALLOC(bin_len + 1);
                                        unbase64 = EXPORTER_MALLOC(bin_len + 1);
                                        decrypted_len = bin_len;
                                        base64_decode_binary(encrypted, assertion, str_len);
                                        res = zcrypt_decrypt(&key, encrypted, bin_len, decrypted, &decrypted_len);
                                        if (res) {
                                            EXPORTER_LOG(AL_ERROR, "%s: Failed to decrypt assertion, doing reauth...", request->name);
                                            EXPORTER_FREE(encrypted);
                                            EXPORTER_FREE(decrypted);
                                            EXPORTER_FREE(unbase64);
                                            require_reauth = 1;
                                            goto auth_failed;
                                        }
                                        EXPORTER_FREE(encrypted);
                                        /* Null terminate... */
                                        if (decrypted) {
                                            decrypted[decrypted_len] = 0;
                                        }
                                        EXPORTER_DEBUG_AUTH("%s: Decrypted assertion to %.*s", request->name, EXPORTER_DEBUG_BYTES, decrypted);

                                        b64_len = base64_decode_binary(unbase64, (const char *)decrypted, decrypted_len);
                                        if (b64_len > 0) {
                                            if (unbase64) {
                                                unbase64[b64_len] = 0;
                                            }
                                            res = zsaml_get_nameid((const char *)unbase64,
                                                                   b64_len,
                                                                   nameid,
                                                                   sizeof(nameid));
                                            if (res == ZSAML_RESULT_NO_ERROR) {
                                                zpath_downcase(nameid); /* required for SCIM user lookup */
                                                if (request->log.nameid) {
                                                    EXPORTER_FREE(request->log.nameid);
                                                    request->log.nameid = NULL;
                                                }
                                                request->log.nameid = EXPORTER_STRDUP(nameid, strlen(nameid));
                                                request->is_authenticated = 1;
                                                res = zsaml_get_username((const char *)unbase64,
                                                        b64_len,
                                                        fname,
                                                        lname,
                                                        sizeof(fname));
                                                if (res == ZSAML_RESULT_NO_ERROR) {
                                                    if (request->log.lname) {
                                                        EXPORTER_FREE(request->log.lname);
                                                        request->log.lname = NULL;
                                                    }
                                                    if (request->log.fname) {
                                                        EXPORTER_FREE(request->log.fname);
                                                        request->log.fname = NULL;
                                                    }
                                                    request->log.lname = EXPORTER_STRDUP(lname, strlen(lname));
                                                    request->log.fname = EXPORTER_STRDUP(fname, strlen(fname));
                                                } else {
                                                    EXPORTER_LOG(AL_ERROR, "%s: Could not extract first/last name from assertion", request->name);
                                                }
                                            } else {
                                                EXPORTER_LOG(AL_ERROR, "%s: Could not extract nameid from assertion", request->name);
                                                if (unbase64) EXPORTER_FREE(unbase64);
                                                if (decrypted) EXPORTER_FREE(decrypted);
                                                return res;
                                            }
                                        } else {
                                            EXPORTER_LOG(AL_ERROR, "%s: Could not extract assertion from b64 assertion", request->name);
                                            if (unbase64) EXPORTER_FREE(unbase64);
                                            if (decrypted) EXPORTER_FREE(decrypted);
                                            return ZPATH_RESULT_ERR;
                                        }

                                        encrypted = NULL;
                                    } else {
                                        /* No need to decrypt... assertion is already correct */
                                        //EXPORTER_DEBUG_CSP("%s: No need to decrypt... assertion is already correct", request->name);
                                    }

                                    if (request->conn->exporter_domain->is_ot) {
                                        if (!g_exporter_ot_mode) {
                                            EXPORTER_LOG(AL_ERROR, "%s: PRA is not supported on regular exporters", request->name);
                                            if (unbase64) EXPORTER_FREE(unbase64);
                                            if (decrypted) EXPORTER_FREE(decrypted);
                                            return exporter_request_respond_with_text(request, HTTP_STATUS_SERVICE_UNAVAILABLE,
                                                                                        EXPORTER_ERROR_CODE_PRA_DISABLED,
                                                                                        "The PRA Portal has moved. Contact Zscaler Support");
                                        }
                                    }

                                    /* load the state for policy check */
                                    res = exporter_request_policy_state(request, unbase64, b64_len);
                                    if (unbase64) EXPORTER_FREE(unbase64);
                                    if (res) {
                                        if (decrypted) EXPORTER_FREE(decrypted);
                                        switch (res) {
                                            case ZPATH_RESULT_ASYNCHRONOUS:
                                                return ZPATH_RESULT_NO_ERROR;
                                            case ZPN_RESULT_EXPIRED:
                                                return ZPATH_RESULT_NO_ERROR;
                                            default:
                                                return res;
                                        }
                                    } else if (request->log.response_status != 0 &&
                                               (request->log.response_status < 200 || request->log.response_status > 299)) {
                                        EXPORTER_LOG(AL_NOTICE, "%s: Response completed with status %d, return to display the response banner to user",
                                                     request->name, request->log.response_status);
                                        return res;
                                    }

                                    /*
                                     * CSP - We need to read policy and profile both.
                                     * - Policy is needed to check if app is being monitored or not.
                                     * - Profile is need to know if we need to inject fingerprint or not
                                     */
                                    struct exporter_context_domain ctx_domain = {.last_bfp_ts = 0x7fffffffffffffff};
                                    int is_header_valid_for_js_injection = 0;

                                    if (request->is_csp_enabled) {
                                        /* Store domain and session cookie */
                                        memset(request->cb_domain_cookie, 0, EXPORTER_COOKIE_MAX_SIZE);
                                        memcpy(request->cb_domain_cookie, domain_cookie_value, EXPORTER_COOKIE_MAX_SIZE);
                                        memset(request->cb_session_cookie, 0, EXPORTER_COOKIE_MAX_SIZE);
                                        memcpy(request->cb_session_cookie, session_cookie_value, EXPORTER_COOKIE_MAX_SIZE);

                                        res = exporter_read_csp_config(request);
                                        if (res == ZPN_RESULT_ASYNCHRONOUS) {
                                            if (decrypted) EXPORTER_FREE(decrypted);
                                            return ZPATH_RESULT_NO_ERROR;
                                        }

                                        /* Always read context - We can integrate this with CSP context */
                                        struct argo_object *ctx_dom_argo_object = NULL;
                                        res = exporter_context_domain_read(request, &ctx_dom_argo_object, exporter_request_async_callback, request, 0);
                                        switch (res) {
                                            case ZPATH_RESULT_NO_ERROR:
                                                if (ctx_dom_argo_object && ctx_dom_argo_object->base_structure_void) {
                                                    memcpy(&ctx_domain, ctx_dom_argo_object->base_structure_void, sizeof(struct exporter_context_domain));
                                                }
                                                break;
                                            case ZPATH_RESULT_ASYNCHRONOUS:
                                                EXPORTER_DEBUG_CSP("%s: CSP_EXPIRY url: [%s] Fetching ctx domain asynchronously - async_count (%d->%d)",
                                                        request->name, request->log.url,
                                                        request->async_count, request->async_count + 1);
                                                exporter_set_async_state_and_inc_count(request, async_state_csp_get_ctx_domain);
                                                if (decrypted) EXPORTER_FREE(decrypted);
                                                if (ctx_dom_argo_object) { exporter_context_free(ctx_dom_argo_object); ctx_dom_argo_object = NULL; }
                                                return ZPATH_RESULT_NO_ERROR;
                                            case ZPN_RESULT_NOT_FOUND:
                                                break;
                                            default:
                                                break;
                                        }
                                        if (ctx_dom_argo_object) { exporter_context_free(ctx_dom_argo_object); ctx_dom_argo_object = NULL; }

                                        int64_t now_s = epoch_s();
                                        int is_expired = (now_s > (ctx_domain.last_bfp_ts/1000 + request->csp_timeout)) ? 1 : 0;
                                        int64_t elapsed = (ctx_domain.last_bfp_ts/1000 + request->csp_timeout) - now_s;

                                        if (request->is_mon_enabled && is_expired && request->csp_timeout) {
                                            /*
                                               Known issue : csp_timeout is from latest profile but last_bfp_ts is from object store
                                               So we should recalculate this again
                                             */

                                            EXPORTER_DEBUG_CSP(
                                                    "%s: CSP_EXPIRY TRUE now: %ld, lastfp: {%ld,%ld,%ld}, timeout: %ld, elapsed: %ld",
                                                    request->name, (long) now_s,
                                                    (long) ctx_domain.last_bfp_ts/1000,
                                                    (long) ctx_domain.n_bfp_redirect,
                                                    (long) ctx_domain.n_bfp_logged,
                                                    (long) request->csp_timeout, (long) elapsed);
                                            /*
                                             * Fingerprint header condition
                                             *  - Accept: text/html - Mandatory condition
                                             *  - Sec-Fetch-Mode: navigate -
                                             *  - Sec-Fetch-Site: This can be ignored but included for stricter check
                                             *      - none - proceed with CSP
                                             *      - cross-site - If user tries to refresh page since referrer is
                                             *      is auth domain https://d.zpa-auth.net/
                                             *
                                             */

                                            //PRCOMMENTS - Move all logic to seperate function
                                            int is_accept_html = 0;

                                            if (request->header_accept) {
                                                is_accept_html = strstr(request->header_accept, "text/html,") != NULL ? 1 : 0;
                                                is_accept_html = is_accept_html || (strstr(request->header_accept, "text/html;") != NULL ? 1 : 0);
                                                is_accept_html = is_accept_html || (strcmp(request->header_accept, "*/*") == 0 ? 1 : 0);
                                            }

                                            if (
                                                    (request->sec_fetch_mode == HTTP_SEC_FETCH_MODE_NAVIGATE
                                                     || request->sec_fetch_mode == HTTP_SEC_FETCH_MODE_NOT_PRESENT
                                                    )
                                                    &&
                                                    (request->sec_fetch_dest == HTTP_SEC_FETCH_DEST_DOCUMENT
                                                     || request->sec_fetch_dest == HTTP_SEC_FETCH_DEST_NOT_PRESENT
                                                    )
                                                    && is_accept_html
                                                    && (request->cors_request == 0)
                                               )
                                            {
                                                is_header_valid_for_js_injection = 1;
                                            }

                                            EXPORTER_DEBUG_CSP("%s: CSP_EXPIRY js_inject: %d, Sec-Fetch: {%s,%s,%s}, Cors: %d, Accept: %d,%s, Filename: %s",
                                                    request->name,
                                                    is_header_valid_for_js_injection,
                                                    request->header_sec_fetch_mode,
                                                    request->header_sec_fetch_site,
                                                    request->header_sec_fetch_dest,
                                                    request->cors_request,
                                                    is_accept_html, request->header_accept,
                                                    request->url);
                                        } else {
                                            /* We can remove this log to avoid flooding */
#if 0
                                            EXPORTER_DEBUG_CSP(
                                                    "%s: CSP_EXPIRY FALSE now: %ld, lastfp: {%ld,%ld,%ld}, timeout: %ld, elapsed: %ld",
                                                    request->name, (long) now_s,
                                                    (long) ctx_domain.last_bfp_ts/1000,
                                                    (long) ctx_domain.n_bfp_redirect,
                                                    (long) ctx_domain.n_bfp_logged,
                                                    (long) request->csp_timeout, (long) elapsed);
#endif
                                        }
                                    }

                                    /* We first check if we are going towards user portal
                                     * Then we do isolation policy check and redirect to cbi if needed
                                     * In case of bypass cbi, we treat it as normal exporter request and do access policy check
                                     */
                                    if (request->conn->exporter_domain->is_user_portal ||
                                        request->conn->exporter_domain->is_ot) {

                                        /* CSP_DATA Start */
                                        //Logging
                                        if (request->is_csp_enabled) {
                                            struct argo_object *csp_obj = NULL;
                                            res = exporter_get_csp_data(request->name,
                                                    OBJECT_STORE_TYPE_FINGERPRINT,
                                                    domain_cookie_value, &csp_obj,
                                                    exporter_request_async_callback,
                                                    request,
                                                    0);

                                            if (res) {
                                                if (res == ZPATH_RESULT_ASYNCHRONOUS) {
                                                    __sync_fetch_and_add_4(&(request->async_count), 1);
                                                    EXPORTER_DEBUG_AUTH("%s: CSP_DATA Fetching csp_domain_key asynchronously - async_count (%d->%d) res %s",
                                                            request->name, request->async_count - 1, request->async_count,zpath_result_string(res));
                                                    if (decrypted) EXPORTER_FREE(decrypted);
                                                    return ZPATH_RESULT_NO_ERROR;
                                                } else if (res == ZPATH_RESULT_NOT_FOUND) {
                                                    /* Revisit this: Verify if this is 1st time not found if mon enabled, then its a bug
                                                     * If its not found on second mtunnel req that is expected
                                                     * We need to differentiate these 2 scenarios for logging
                                                     */
                                                    EXPORTER_DEBUG_AUTH("%s: CSP_DATA Fetching csp_domain_key res %s",
                                                                        request->name, zpath_result_string(res));
                                                    /* CSP Periodic */
                                                    if (request->is_mon_enabled) {

                                                        /* Config override to check if feature is disabled */
                                                        int is_csp_timeout_enabled = get_csp_timeout(request->orig_customer_gid);

                                                        if (is_csp_timeout_enabled && is_header_valid_for_js_injection)
                                                        {
                                                            char orig_url[EXPORTER_URL_MAX_ENCODE_SIZE];
                                                            int ret_encryption = ZPATH_RESULT_NO_ERROR;

                                                            if (request->sra_zconsole_url) {
                                                                snprintf(orig_url, sizeof(orig_url), "https://%s:%d%s", request->conn->sni, request->conn->local_port_he,
                                                                        request->sra_zconsole_url);
                                                            } else {
                                                                snprintf(orig_url, sizeof(orig_url), "https://%s:%d%s", request->conn->sni, request->conn->local_port_he,
                                                                        &(request->url[request->url_parser.field_data[UF_PATH].off]));
                                                            }

                                                            EXPORTER_DEBUG_AUTH("%s: Fingerprint expired, redirecting to collect BFP", request->name);

                                                            char nonce_object_key[EXPORTER_COOKIE_MAX_SIZE] = {0};
                                                            char crypto_nonce[EXPORTER_CSP_ENC_KEY_LEN + 1] = {0};
                                                            char crypto_iv[EXPORTER_CSP_ENC_IV_LEN + 1] = {0};
                                                            char encrypted_nonce_object_key[EXPORTER_COOKIE_MAX_SIZE] = {0};

                                                            if (request->js_encryption_enabled) {
                                                                ret_encryption = exporter_csp_get_encryption_context(request, request->cb_session_cookie,
                                                                        nonce_object_key, sizeof(nonce_object_key),
                                                                        crypto_nonce, sizeof(crypto_nonce),
                                                                        crypto_iv, sizeof(crypto_iv));
                                                                if (ret_encryption) {
                                                                    EXPORTER_LOG(AL_ERROR, "%s: [CSP NONCE ] Could not generate nonce, Key, IV", request->name);
                                                                } else {
                                                                    ret_encryption = exporter_csp_encrypt_nonce(request, nonce_object_key, EXPORTER_COOKIE_MAX_SIZE,
                                                                            encrypted_nonce_object_key);
                                                                    if (!ret_encryption) {
                                                                        EXPORTER_LOG(AL_DEBUG, "%s [CSP_ENCRYPT_NONCE] Here is the encrypted nonce [%s]",
                                                                                request->name,
                                                                                encrypted_nonce_object_key);
                                                                    }
                                                                }
                                                            }

                                                            if (decrypted) EXPORTER_FREE(decrypted);

                                                            /* Update domain context with last periodic redirection */
                                                            struct exporter_context_domain ctx = {
                                                                .last_bfp_ts = epoch_us_accuracy_us()/1000,
                                                                .n_bfp_redirect = ctx_domain.n_bfp_redirect + 1,
                                                                .n_bfp_logged = ctx_domain.n_bfp_logged
                                                            };
                                                            exporter_context_domain_write(request, &ctx, NULL, NULL, 0);

                                                            //PRCOMMENTS - Make CSP collection random 2 hours  - 2 hours -+ 10 min
                                                            //If once we fail to redirect then always redirect on next timeout
                                                            //UI limit min 5 min (4-6) - max 1 day (+-1,2 hours)
                                                            //Make domain_type as string

                                                            /* Redirect on auth domain so that geo can be collected */
                                                            if (request->js_encryption_enabled && ret_encryption != ZPATH_RESULT_NO_ERROR) {
                                                                // PRCOMMENTS - Should we collect CSP without encryption ? or this is very rare so do not collect
                                                                EXPORTER_LOG(AL_ERROR, "%s: [CSP_ENCRYPT_NONCE] Failed to generate Nonce, not redirecting [%s]!!!",
                                                                                       request->name, zpath_result_string(ret_encryption));
                                                            } else {
                                                                if (request->js_encryption_enabled) {
                                                                    return exporter_request_redirect_encode(request,
                                                                            NULL,
                                                                            NULL,
                                                                            NULL,
                                                                            NULL,
                                                                            EXPORTER_DOMAIN_AUTH,
                                                                            443,
                                                                            (request->is_collect_location ? EXPORTER_PATH_GET_CANVAS_FINGERPRINT_GEO_ENC: EXPORTER_PATH_GET_CANVAS_FINGERPRINT_ENC),
                                                                            EXPORTER_QUERY_NAME_ORIGINAL_URL, orig_url,
                                                                            EXPORTER_QUERY_NAME_TO_DOMAIN_SET_COOKIE, domain_cookie_value,
                                                                            "periodicorigurl", orig_url,
                                                                            EXPORTER_QUERY_NAME_TO_SP_OS_ENCKEY, encrypted_nonce_object_key,
                                                                            EXPORTER_QUERY_NAME_TO_SP_CRYPTO_ENCKEY, crypto_nonce,
                                                                            EXPORTER_QUERY_NAME_TO_SP_CRYPTO_IV, crypto_iv,
                                                                            NULL);
                                                                } else {
                                                                    return exporter_request_redirect_encode(request,
                                                                            NULL,
                                                                            NULL,
                                                                            NULL,
                                                                            NULL,
                                                                            EXPORTER_DOMAIN_AUTH,
                                                                            443,
                                                                            (request->is_collect_location ? EXPORTER_PATH_GET_CANVAS_FINGERPRINT_GEO : EXPORTER_PATH_GET_CANVAS_FINGERPRINT),
                                                                            EXPORTER_QUERY_NAME_ORIGINAL_URL, orig_url,
                                                                            EXPORTER_QUERY_NAME_TO_DOMAIN_SET_COOKIE, domain_cookie_value,
                                                                            "periodicorigurl", orig_url,
                                                                            NULL);
                                                                }
                                                            }//ret_encryption!=ZPATH_RESULT_NO_ERROR
                                                        }
                                                    }
                                                    /* CSP Periodic */
                                                } else {
                                                    EXPORTER_LOG(AL_ERROR, "%s: CSP_DATA csp_domain_key: %s", request->name,
                                                            zpath_result_string(res));
                                                }
                                            } else {
                                                request->is_csp_log = 1;
                                                request->csp_os_data = csp_obj;

                                                /* CSP Periodic */
                                                /* Update domain context with last periodic redirection */
                                                int64_t now_s = epoch_us_accuracy_us()/1000;
                                                struct exporter_context_domain ctx = {
                                                    .last_bfp_ts = now_s,
                                                    .n_bfp_redirect = ctx_domain.n_bfp_redirect,
                                                    .n_bfp_logged = ctx_domain.n_bfp_logged + 1
                                                };
                                                exporter_context_domain_write(request, &ctx, NULL, NULL, 0);

                                                int64_t profile_bfp_ts = now_s - ctx_domain.last_bfp_ts;
                                                EXPORTER_DEBUG_AUTH("%s: CSP_EXPIRY Logging fingerprint took: %ld msec", request->name, (long)profile_bfp_ts);

                                                request->log.session_id = EXPORTER_MALLOC(EXPORTER_CONTEXT_SESSION_ID_LEN + 1);
                                                if (request->log.session_id) {
                                                    memset(request->log.session_id, 0, EXPORTER_CONTEXT_SESSION_ID_LEN + 1);
                                                    get_context_session_id(request, request->log.session_id);
                                                }

                                                request->log.domain_id = EXPORTER_MALLOC(EXPORTER_CONTEXT_DOMAIN_ID_LEN + 1);
                                                if (request->log.domain_id) {
                                                    memset(request->log.domain_id, 0, EXPORTER_CONTEXT_DOMAIN_ID_LEN + 1);
                                                    get_context_domain_id(request, request->log.domain_id);
                                                }

                                                EXPORTER_DEBUG_CSP("%s: HTTP_REQ_CTX session_id: %.*s, domain_id: %.*s", request->name,
                                                        EXPORTER_CONTEXT_SESSION_ID_LEN, request->log.session_id ? request->log.session_id : "nil",
                                                        EXPORTER_CONTEXT_DOMAIN_ID_LEN, request->log.domain_id ? request->log.domain_id : "nil");

                                                /* CSP Periodic */

                                                exporter_update_csp_log_info(request);

                                                uint8_t mtunnel_bin[ZPN_MTUNNEL_ID_BYTES];
                                                memset(mtunnel_bin, 0, sizeof(mtunnel_bin));

                                                /* mtunnel ID is combination of tunnel ID + more random bytes, both base64, delimited by a comma */
                                                res = RAND_bytes((unsigned char *)&mtunnel_bin, ZPN_MTUNNEL_ID_BYTES - ZPN_TUNNEL_ID_BYTES);

                                                if (res == 0) {
                                                    memcpy(&g_dummy_mtunnel_id[ZPN_TUNNEL_ID_BYTES_TEXT + 1],
                                                            "GS+GfFocVMRRNzNxiCNH", ZPN_TUNNEL_ID_BYTES_TEXT);
                                                } else {
                                                    base64_encode_binary(&g_dummy_mtunnel_id[ZPN_TUNNEL_ID_BYTES_TEXT + 1], mtunnel_bin,
                                                            ZPN_MTUNNEL_ID_BYTES - ZPN_TUNNEL_ID_BYTES);
                                                }

                                                request->log.mtunnel_id = EXPORTER_MALLOC(ZPN_MTUNNEL_ID_BYTES_TEXT + 1);
                                                memset(request->log.mtunnel_id, 0, ZPN_MTUNNEL_ID_BYTES_TEXT + 1);
                                                memcpy(request->log.mtunnel_id, g_dummy_mtunnel_id,  ZPN_MTUNNEL_ID_BYTES_TEXT + 1);
                                                request->log.domain_type = zpn_domain_type_user_portal;
                                            }
                                        }
                                        /* CSP_DATA End*/

                                        /* For PRA Portal API handling, need to copy decrypted assertion, assertion required by zevent callbacks
                                         */
                                        char *assertion_key_copy = EXPORTER_STRDUP((const char *)assertion_key, strlen((const char *)assertion_key));
                                        char *assertion_copy = NULL;
                                        if (decrypted) {
                                            assertion_copy = EXPORTER_STRDUP((const char *)decrypted, strlen((const char *)decrypted));
                                        } else {
                                            assertion_copy = EXPORTER_STRDUP((const char *)assertion, strlen((const char *)assertion));
                                        }
                                        res = exporter_user_portal_request(request, assertion_key_copy, assertion_copy);
                                    } else {
                                        /* Start isolation policy check */
                                        int64_t cbi_profile_gid = 0;
                                        enum zpe_access_action matched_action;
                                        int ret;

                                        char * link = request->conn->exporter_domain->cfg_domain?request->conn->exporter_domain->cfg_domain:request->conn->sni;
                                        ret = zpn_broker_policy_locked_link_policy_check(request->scope_gid,
                                                    request->attr,
                                                    request->policy_state->idp_gid,
                                                    zpe_policy_type_isolate,
                                                    link,
                                                    request->conn->exporter_domain->application_port_he,
                                                    request->policy_state->general_state_hash,
                                                    request->policy_state->saml_policy_enabled ? request->policy_state->saml_state_hash : NULL,
                                                    request->policy_state->scim_policy_enabled ? request->policy_state->scim_state_hash : NULL,
                                                    &matched_action,
                                                    NULL,
                                                    NULL,
                                                    &cbi_profile_gid,
                                                    exporter_request_wally_callback,
                                                    request,
                                                    &request->app_gid);

                                        EXPORTER_LOG(AL_INFO, "%s: Fetching broker linked policy for sni:domain %s:%s returned %s",request->name,
                                                              request->conn->sni, link, zpath_result_string(ret));
                                        if (ret == ZPATH_RESULT_NO_ERROR) {
                                            if (matched_action == zpe_access_action_isolate) {
                                                /* the url is isolated to CBI */
                                                EXPORTER_DEBUG_AUTH("%s: URL: %s, is isolated, redirecting to cbi...", request->name, domain);

                                                /*get cbi profile id*/
                                                struct zpn_cbi_profile *cbi_profile = NULL;

                                                res = zpn_cbi_profile_get_by_id(cbi_profile_gid,
                                                                                &cbi_profile,
                                                                                exporter_request_wally_callback,
                                                                                request,
                                                                                0);

                                                if (res == ZPATH_RESULT_ASYNCHRONOUS) {
                                                    EXPORTER_DEBUG_AUTH("%s: Fetching cbi profile asynchronously for application domain %s"
                                                                        "asynchronously, async_count - (%d->%d)", request->name,
                                                                        request->conn->sni, request->async_count, request->async_count + 1);
                                                    __sync_fetch_and_add_4(&(request->async_count), 1);
                                                    if (decrypted) EXPORTER_FREE(decrypted);
                                                    return ZPATH_RESULT_NO_ERROR;
                                                } else if (res || !cbi_profile) {
                                                    EXPORTER_LOG(AL_WARNING, "%s: Could not fetch cbi profile for application domain %s",
                                                                        request->name, request->conn->sni);
                                                    if (decrypted) EXPORTER_FREE(decrypted);
                                                    return exporter_request_respond(request, HTTP_STATUS_FORBIDDEN, EXPORTER_ERROR_CODE_CBI_PROFILE_NOT_FOUND);
                                                }

                                                char *cbi_profile_id = cbi_profile->cbi_profile_id;
                                                char *redirect_url = cbi_profile->cbi_profile_url;
                                                char *app_domain = request->conn->exporter_domain->cfg_domain?request->conn->exporter_domain->cfg_domain:request->conn->sni;

                                                /*Get local_domain if available or send the original domain*/
                                                struct zpn_client_less *cl = zpn_client_less_search_by_domain(request->conn->exporter_domain->customer_gid, app_domain,
                                                                                                              strnlen(app_domain,EXPORTER_DOMAIN_NAME_MAX_SIZE));

                                                if (!cl) {
                                                    EXPORTER_LOG(AL_WARNING, "%s:  App %s, not clientless app?", request->name, app_domain);
                                                    if (decrypted) EXPORTER_FREE(decrypted);
                                                    return ZPN_RESULT_NOT_FOUND;
                                                }
                                                char *transformed_domain = NULL;

                                                if (cl->local_domain) {
                                                    transformed_domain = cl->local_domain;
                                                    EXPORTER_DEBUG_AUTH("%s:  Got local domain %s for application domain %s", request->name, cl->local_domain, app_domain);
                                                } else {
                                                    /* unified-portal - In case of a ZS managed domain, the configured fqdn should be sent,
                                                     * though the request->conn->sni came */
                                                    transformed_domain = app_domain;
                                                    EXPORTER_DEBUG_AUTH("%s:  Could not get local domain for application domain %s", request->name, app_domain);
                                                }

                                                /*Create a CBI token*/
                                                char timestamp[64 + 1] = {0};
                                                char proof[EXPORTER_PROOF_ENCODED_SIZE + 1] = {0};
                                                res = get_cbi_proof(proof, sizeof(proof) - 1, cbi_profile_id, transformed_domain, timestamp, sizeof(timestamp) - 1, request);

                                                if (res != ZPATH_RESULT_NO_ERROR) {
                                                    EXPORTER_LOG(AL_ERROR, "%s: Failed to generate cbi proof: %s", request->name, zpath_result_string(res));
                                                    if (decrypted) EXPORTER_FREE(decrypted);
                                                    return exporter_request_respond(request, HTTP_STATUS_FORBIDDEN, EXPORTER_ERROR_CODE_CBI_PROOF_GENERATION_FAILED);
                                                }

                                                char orig_url[EXPORTER_URL_MAX_ENCODE_SIZE];
                                                if (cl->app_type == zpn_client_less_application_https) {
                                                    snprintf(orig_url, sizeof(orig_url), "https://%s:%d%s", request->conn->sni, cl->application_port, &(request->url[request->url_parser.field_data[UF_PATH].off]));
                                                } else {
                                                    snprintf(orig_url, sizeof(orig_url), "http://%s:%d%s", request->conn->sni, cl->application_port, &(request->url[request->url_parser.field_data[UF_PATH].off]));
                                                }
                                                res = exporter_request_redirect_encode(request,
                                                                                    NULL,  /* No cookie */
                                                                                    NULL,  /* No cookie */
                                                                                    NULL,  /* No cookie */
                                                                                    NULL,  /* No cookie */
                                                                                    redirect_url,
                                                                                    0,
                                                                                    NULL,
                                                                                    EXPORTER_QUERY_NAME_ASSERTION_COOKIE, assertion_key,
                                                                                    EXPORTER_QUERY_NAME_TO_DOMAIN_SET_CRYPTO_COOKIE, cookie_crypto_value,
                                                                                    EXPORTER_QUERY_NAME_ORIGINAL_URL, orig_url,
                                                                                    "domain", transformed_domain,
                                                                                    "timestamp", timestamp,
                                                                                    EXPORTER_CBI_PROOF, proof,
                                                                                    "cloud_name", ZPATH_LOCAL_CLOUD_NAME,
                                                                                    NULL);
                                                EXPORTER_DEBUG_AUTH("%s: redirect_encode returns: %s", request->name, zpath_result_string(res));
                                                if (decrypted) EXPORTER_FREE(decrypted);
                                                return ZPATH_RESULT_NO_ERROR;
                                            }
                                            /* If the url is not isolated,
                                             * we continue to treat it as a normal exporter request */
                                        } else if (ret == ZPATH_RESULT_ASYNCHRONOUS) {
                                            EXPORTER_DEBUG_AUTH("%s: checking isolate policy for customer link: %s asynchronously"
                                                                   " - async_count (%d->%d)", request->name, domain,
                                                                   request->async_count, request->async_count + 1);
                                            __sync_fetch_and_add_4(&(request->async_count), 1);
                                            if (decrypted) EXPORTER_FREE(decrypted);
                                            return ZPATH_RESULT_NO_ERROR;
                                        } else {
                                            EXPORTER_LOG(AL_ERROR, "%s: Isolation policy check failed %s, send HTTP 403",
                                                                    request->name, zpn_result_string(ret));
                                            if (decrypted) EXPORTER_FREE(decrypted);
                                            return exporter_request_respond(request, HTTP_STATUS_FORBIDDEN, EXPORTER_ERROR_CODE_ISOLATION_POLICY_CHECK_FAILED);
                                        }

                                        /* CSP_DATA Start */
                                        //CSP Logging
                                        if (request->is_csp_enabled) {
                                            struct argo_object *csp_obj = NULL;
                                            res = exporter_get_csp_data(request->name,
                                                    OBJECT_STORE_TYPE_FINGERPRINT,
                                                    domain_cookie_value, &csp_obj,
                                                    exporter_request_async_callback,
                                                    request,
                                                    0);

                                            if (res) {
                                                if (res == ZPATH_RESULT_ASYNCHRONOUS) {
                                                    __sync_fetch_and_add_4(&(request->async_count), 1);
                                                    EXPORTER_DEBUG_AUTH("%s: CSP_DATA Fetching csp_domain_key asynchronously - async_count (%d->%d) res %s",
                                                            request->name, request->async_count - 1, request->async_count,zpath_result_string(res));
                                                    if (decrypted) EXPORTER_FREE(decrypted);
                                                    return ZPATH_RESULT_NO_ERROR;
                                                } else if (res == ZPATH_RESULT_NOT_FOUND) {
                                                    /* Revisit this: Verify if this is 1st time not found if mon enabled, then its a bug
                                                     * If its not found on second mtunnel req that is expected
                                                     * We need to differentiate these 2 scenarios for logging
                                                     */
                                                    EXPORTER_DEBUG_AUTH("%s: CSP_DATA Fetching csp_domain_key res %s",
                                                                        request->name, zpath_result_string(res));

                                                    /* CSP Periodic */
                                                    if (request->is_mon_enabled) {

                                                        /* Config override to check if feature is disabled */
                                                        int is_csp_timeout_enabled = get_csp_timeout(request->orig_customer_gid);

                                                        if (is_csp_timeout_enabled && is_header_valid_for_js_injection)
                                                        {
                                                            char orig_url[EXPORTER_URL_MAX_ENCODE_SIZE];
                                                            int ret_encryption = ZPATH_RESULT_NO_ERROR;

                                                            if (request->sra_zconsole_url) {
                                                                snprintf(orig_url, sizeof(orig_url), "https://%s:%d%s", request->conn->sni, request->conn->local_port_he,
                                                                        request->sra_zconsole_url);
                                                            } else {
                                                                snprintf(orig_url, sizeof(orig_url), "https://%s:%d%s", request->conn->sni, request->conn->local_port_he,
                                                                        &(request->url[request->url_parser.field_data[UF_PATH].off]));
                                                            }

                                                            EXPORTER_DEBUG_AUTH("%s: Fingerprint expired, redirecting to collect BFP", request->name);

                                                            char nonce_object_key[EXPORTER_COOKIE_MAX_SIZE] = {0};
                                                            char crypto_nonce[EXPORTER_CSP_ENC_KEY_LEN + 1] = {0};
                                                            char crypto_iv[EXPORTER_CSP_ENC_IV_LEN + 1] = {0};
                                                            char encrypted_nonce_object_key[EXPORTER_COOKIE_MAX_SIZE] = {0};

                                                            if (request->js_encryption_enabled) {
                                                                ret_encryption = exporter_csp_get_encryption_context(request, request->cb_session_cookie,
                                                                        nonce_object_key, sizeof(nonce_object_key),
                                                                        crypto_nonce, sizeof(crypto_nonce),
                                                                        crypto_iv, sizeof(crypto_iv));
                                                                if (ret_encryption) {
                                                                    EXPORTER_LOG(AL_ERROR, "%s: [CSP_ENCRYPT_NONCE] Could not generate nonce, Key, IV", request->name);
                                                                } else {
                                                                    ret_encryption = exporter_csp_encrypt_nonce(request, nonce_object_key, EXPORTER_COOKIE_MAX_SIZE,
                                                                            encrypted_nonce_object_key);
                                                                    if (!ret_encryption) {
                                                                        EXPORTER_LOG(AL_DEBUG, "%s [CSP_ENCRYPT_NONCE] Here is the encrypted nonce [%s]",
                                                                                request->name,
                                                                                encrypted_nonce_object_key);
                                                                    }
                                                                }
                                                            }

                                                            if (decrypted) EXPORTER_FREE(decrypted);

                                                            /* Update domain context with last periodic redirection */
                                                            struct exporter_context_domain ctx = {
                                                                .last_bfp_ts = epoch_us_accuracy_us()/1000,
                                                                .n_bfp_redirect = ctx_domain.n_bfp_redirect + 1,
                                                                .n_bfp_logged = ctx_domain.n_bfp_logged
                                                            };
                                                            exporter_context_domain_write(request, &ctx, NULL, NULL, 0);

                                                            /* Redirect on auth domain so that geo can be collected */
                                                            if (request->js_encryption_enabled && ret_encryption != ZPATH_RESULT_NO_ERROR) {
                                                                // Don't re-direct
                                                                EXPORTER_LOG(AL_ERROR, "%s: [CSP_ENCRYPT_NONCE] Failed to generate Nonce, not redirecting [%s]!!!",
                                                                                       request->name, zpath_result_string(ret_encryption));
                                                            } else {
                                                                if (request->js_encryption_enabled) {
                                                                    return exporter_request_redirect_encode(request,
                                                                            NULL,
                                                                            NULL,
                                                                            NULL,
                                                                            NULL,
                                                                            EXPORTER_DOMAIN_AUTH,
                                                                            443,
                                                                            (request->is_collect_location ? EXPORTER_PATH_GET_CANVAS_FINGERPRINT_GEO_ENC: EXPORTER_PATH_GET_CANVAS_FINGERPRINT_ENC),
                                                                            EXPORTER_QUERY_NAME_ORIGINAL_URL, orig_url,
                                                                            EXPORTER_QUERY_NAME_TO_DOMAIN_SET_COOKIE, domain_cookie_value,
                                                                            "periodicorigurl", orig_url,
                                                                            EXPORTER_QUERY_NAME_TO_SP_OS_ENCKEY, encrypted_nonce_object_key,
                                                                            EXPORTER_QUERY_NAME_TO_SP_CRYPTO_ENCKEY, crypto_nonce,
                                                                            EXPORTER_QUERY_NAME_TO_SP_CRYPTO_IV, crypto_iv,
                                                                            NULL);
                                                                } else {
                                                                    return exporter_request_redirect_encode(request,
                                                                            NULL,
                                                                            NULL,
                                                                            NULL,
                                                                            NULL,
                                                                            EXPORTER_DOMAIN_AUTH,
                                                                            443,
                                                                            (request->is_collect_location ? EXPORTER_PATH_GET_CANVAS_FINGERPRINT_GEO : EXPORTER_PATH_GET_CANVAS_FINGERPRINT),
                                                                            EXPORTER_QUERY_NAME_ORIGINAL_URL, orig_url,
                                                                            EXPORTER_QUERY_NAME_TO_DOMAIN_SET_COOKIE, domain_cookie_value,
                                                                            "periodicorigurl", orig_url,
                                                                            NULL);
                                                                }
                                                            }//ret_encryption!=ZPATH_RESULT_NO_ERROR
                                                        }
                                                    }
                                                    /* CSP Periodic */
                                                } else {
                                                    EXPORTER_LOG(AL_ERROR, "%s: CSP_DATA csp_domain_key: %s", request->name,
                                                            zpath_result_string(res));
                                                }
                                            } else {
                                                request->is_csp_log = 1;
                                                request->csp_os_data = csp_obj;

                                                /* CSP Periodic */
                                                /* Update domain context with last periodic redirection */
                                                int64_t now_s = epoch_us_accuracy_us()/1000;
                                                struct exporter_context_domain ctx = {
                                                    .last_bfp_ts = now_s,
                                                    .n_bfp_redirect = ctx_domain.n_bfp_redirect,
                                                    .n_bfp_logged = ctx_domain.n_bfp_logged + 1
                                                };
                                                exporter_context_domain_write(request, &ctx, NULL, NULL, 0);

                                                int64_t profile_bfp_ts = now_s - ctx_domain.last_bfp_ts;
                                                EXPORTER_DEBUG_CSP("%s: CSP_EXPIRY Logging fingerprint took: %ld msec", request->name, (long)profile_bfp_ts);

                                                request->log.session_id = EXPORTER_MALLOC(EXPORTER_CONTEXT_SESSION_ID_LEN + 1);
                                                if (request->log.session_id) {
                                                    memset(request->log.session_id, 0, EXPORTER_CONTEXT_SESSION_ID_LEN + 1);
                                                    get_context_session_id(request, request->log.session_id);
                                                }

                                                request->log.domain_id = EXPORTER_MALLOC(EXPORTER_CONTEXT_DOMAIN_ID_LEN + 1);
                                                if (request->log.domain_id) {
                                                    memset(request->log.domain_id, 0, EXPORTER_CONTEXT_DOMAIN_ID_LEN + 1);
                                                    get_context_domain_id(request, request->log.domain_id);
                                                }

                                                EXPORTER_DEBUG_CSP("%s: HTTP_REQ_CTX session_id: %.*s, domain_id: %.*s", request->name,
                                                        EXPORTER_CONTEXT_SESSION_ID_LEN, request->log.session_id ? request->log.session_id : "nil",
                                                        EXPORTER_CONTEXT_DOMAIN_ID_LEN, request->log.domain_id ? request->log.domain_id : "nil");

                                                /* CSP Periodic */

                                                exporter_update_csp_log_info(request);
                                            }
                                        }
                                        /* CSP_DATA End*/

                                        /* Even in error cases, we don't want to kill the exporter request
                                         * since if the customer is not provisioned with cbi then the policy
                                         * evaluation will return error (zpe_get_current_policy failed).
                                         * So we will continue and treat it as normal BA request */

                                        EXPORTER_ASSERT_SOFT((request->guac_info == NULL), "%s: expected null guac_info in BA request(%p)",
                                                             request->name, request->guac_info);
                                        res = exporter_zpa_request(request, assertion_key,
                                                                   decrypted ? (const char *) decrypted : assertion, 0);
                                    }

                                    if (decrypted) EXPORTER_FREE(decrypted);
                                    if ((ZPN_RESULT_ASYNCHRONOUS == res) || (ZPATH_RESULT_NO_ERROR == res)) {
                                        /* Happiness */
                                        return ZPATH_RESULT_NO_ERROR;
                                    } else {
                                        /* logging is better within the callee, so ignore logging here */
                                        EXPORTER_DEBUG_HTTP("%s: request_path_default_cb processing failed: %s", request->name, zpath_result_string(res));
                                        return exporter_request_respond(request, HTTP_STATUS_INTERNAL_SERVER_ERROR, EXPORTER_ERROR_CODE_DEFAULT_PATH_CALLBACK_FAILED);
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
 auth_failed:
    /*
     * This is a special unauthenticated request. It needs to be passed to the backend app.
     */
    if (request->req_method == HTTP_OPTIONS) {

        if (!request->conn->exporter_domain->noauth_options) {
            EXPORTER_LOG(AL_WARNING, "%s: Received unauthenticated request. No auth options is disabled for this app", request->name);
            return exporter_request_respond(request, HTTP_STATUS_FORBIDDEN, EXPORTER_ERROR_CODE_NO_AUTH_OPTIONS_DISABLED);
        }

        if (exporter_validate_no_auth_options_request(request) != ZPATH_RESULT_NO_ERROR) {
            EXPORTER_LOG(AL_ERROR, "%s: Received unauthenticated request. Invalid request", request->name);
            return exporter_request_respond(request, HTTP_STATUS_FORBIDDEN, EXPORTER_ERROR_CODE_UNAUTHENTICATED_INVALID_OPTIONS_REQUEST);
        }

        /* unified-portal - HTTP_OPTIONS handling - Verify for PRA we have options code already how will this work ?
         * Since options is coming from same-origin for PRA 1.0 ideally we should never hit here since it will have cookies
         * Browser will send cookies for OPTIONS here if from same-origin and we will find saml auth and normal workflow should
         * resume
         * Only in case of CORS and OPTIONS cookies is not sent by browser
         * Additionally we can check the request headers if request is same-origin CORS and if yes bypass below code
         */
        if (request->conn->exporter_domain->is_ot || request->conn->exporter_domain->is_user_portal) {
            if (is_unified_portal_feature_enabled(request->conn->exporter_domain->customer_gid)) {
                EXPORTER_DEBUG_AUTH("%s: Received unauthenticated request for portal, sending 200 ok", request->name);
                return exporter_request_respond_acao_unified_portal(request, HTTP_STATUS_OK, EXPORTER_ERROR_CODE_NO_ERROR, 1);
            } else {
                EXPORTER_DEBUG_AUTH("%s: Received unauthenticated request for portal, feature turned off sending 403 error", request->name);
                return exporter_request_respond(request, HTTP_STATUS_FORBIDDEN, EXPORTER_ERROR_CODE_UNAUTHENTICATED_INVALID_OPTIONS_REQUEST);
            }
        }

        EXPORTER_DEBUG_AUTH("%s: Received unauthenticated request. Forwarding to the application", request->name);
        return exporter_zpa_request(request, NULL, NULL, 1);
    }

    if (exporter_request_is_currently_unavailable(request)) {
        return exporter_request_respond(request, HTTP_STATUS_SERVICE_UNAVAILABLE, EXPORTER_ERROR_CODE_REQUEST_UNAVAILABLE);
    }

    /***************************************************
     *
     * Authentication failed- restart authentication follows.
     *
     */
    if (request->req_method != HTTP_GET) {
        /* It's not get. Return forbidden */
        return exporter_request_respond(request, HTTP_STATUS_FORBIDDEN, EXPORTER_ERROR_CODE_NOT_GET_REQUEST);
    } else {
        /* Redirect to get one. */
        char orig_url[EXPORTER_URL_MAX_ENCODE_SIZE];

        if (request->sra_zconsole_url) {
            snprintf(orig_url, sizeof(orig_url), "https://%s:%d%s", request->conn->sni, request->conn->local_port_he, request->sra_zconsole_url);
        } else {
            snprintf(orig_url, sizeof(orig_url), "https://%s:%d%s", request->conn->sni, request->conn->local_port_he,
                    &(request->url[request->url_parser.field_data[UF_PATH].off]));
        }

        EXPORTER_DEBUG_AUTH("%s: Received unauthenticated request. Redirecting to auth domain", request->name);

        /* unified-portal - If cookies are expired we hit here reauth doesn't works for CORS since it goes to authsp and CORS doesnt support opening JS */
        if (require_reauth) {
            return exporter_request_redirect_encode(request,
                                                    NULL,
                                                    NULL,
                                                    NULL,
                                                    NULL,
                                                    EXPORTER_DOMAIN_AUTH,
                                                    443,
                                                    EXPORTER_PATH_AUTH_DOMAIN_FROM_CUSTOMER_DOMAIN,
                                                    EXPORTER_QUERY_NAME_ORIGINAL_URL, orig_url,
                                                    EXPORTER_QUERY_NAME_REQUIRE_REAUTH, "true",
                                                    NULL);
        } else {
            if (exporter_client_less_cors_enabled(request->conn->exporter_domain->customer_gid, request))
            {
                char ctoken_tx[EXPORTER_CORS_TOKEN_SIZE] = {0};
                res = create_cors_token(request, ctoken_tx, sizeof(ctoken_tx)-1);
                if (res != ZPATH_RESULT_NO_ERROR) {
                    EXPORTER_LOG(AL_ERROR, "%s: Failed to generate cors token: %s", request->name, zpath_result_string(res));
                    return exporter_request_respond(request, HTTP_STATUS_FORBIDDEN, EXPORTER_ERROR_CODE_CORS_TOKEN_GENERATION_FAILED);
                }
                /* unified-portal - For CORS Redirect to unified portal domain in Origin header
                 * With this browser can send the cookies since its samesite and we can get hold of session cookies
                 * Exporter can then generate cookies for User Portal FQDN and we can bypass strict security to block
                 * 3rd party cookies
                 *
                 * If we use auth domain d.zpa-auth.net and add CSP headers then also browser will block the request
                 * since User Portal FQDN will redirect to d.zpa-auth.net
                 *
                 * Browser blocks below
                 * pra.com --> CORS for user.com --> CORS for d.zpa-auth.net/doauth (cookies are never sent for d.zpa-auth.net)
                 *
                 * Browser does not blocks below
                 * pra.com --> CORS for user.com --> CORS for pra.com/doauthup (cookies are sent)
                 */

                if (request->conn->exporter_domain->is_user_portal) {
                    char target_domain[1024];
                    snprintf(target_domain, sizeof(target_domain), "%s%s", request->header_origin, EXPORTER_PATH_AUTH_DOMAIN_FROM_UNIFIED_PORTAL);
                    return exporter_request_redirect_encode(request,
                            NULL,
                            NULL,
                            NULL,
                            NULL,
                            target_domain,
                            0,
                            NULL,
                            EXPORTER_QUERY_NAME_ORIGINAL_URL, orig_url,
                            EXPORTER_CORS_TOKEN, ctoken_tx,
                            NULL);

                }

                return exporter_request_redirect_encode(request,
                                                        NULL,
                                                        NULL,
                                                        NULL,
                                                        NULL,
                                                        EXPORTER_DOMAIN_AUTH,
                                                        443,
                                                        EXPORTER_PATH_AUTH_DOMAIN_FROM_CUSTOMER_DOMAIN,
                                                        EXPORTER_QUERY_NAME_ORIGINAL_URL, orig_url,
                                                        EXPORTER_CORS_TOKEN, ctoken_tx,
                                                        NULL);

            } else {
                return exporter_request_redirect_encode(request,
                                                        NULL,
                                                        NULL,
                                                        NULL,
                                                        NULL,
                                                        EXPORTER_DOMAIN_AUTH,
                                                        443,
                                                        EXPORTER_PATH_AUTH_DOMAIN_FROM_CUSTOMER_DOMAIN,
                                                        EXPORTER_QUERY_NAME_ORIGINAL_URL, orig_url,
                                                        NULL);
            }
        }
    }
}


// API to generate the Object store Key and Value.
// Value will have encryption key and IV
static int exporter_csp_get_encryption_context(struct exporter_request *request, const char *refer_key,
                                               char *nonce, size_t nonce_len, char* enc_key, size_t enc_key_len,
                                               char* iv, size_t iv_len) {
    int res;
    char crypto_iv[EXPORTER_CSP_ENC_IV_LEN + 1] = {0};
    char crypto_nonce[EXPORTER_CSP_ENC_KEY_LEN + 1] = {0};
    char key_iv[EXPORTER_CSP_OS_ENC_KEY_IV_LEN] = {0};

    memset(nonce, 0, nonce_len);
    memset(iv, 0, iv_len);
    memset(enc_key, 0, enc_key_len);
    memset(key_iv, 0, EXPORTER_CSP_OS_ENC_KEY_IV_LEN);

    res = object_store_gen_key(OBJECT_STORE_TYPE_FINGERPRINT_NONCE, refer_key, nonce, nonce_len, ostore_role_object_store);
    if (res) {
        EXPORTER_LOG(AL_ERROR, "%s: Could not generate nonce", request->name);
        return res;
    }

    // Update the seed with current time
    srandom((unsigned int)time(NULL));

    for (int i=0; i<EXPORTER_CSP_ENC_KEY_LEN; i++) {
        crypto_nonce[i] = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789"[random () % 62];
    }
    for (int i=0; i<EXPORTER_CSP_ENC_IV_LEN; i++) {
        crypto_iv[i] = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789"[random () % 62];
    }

    snprintf(enc_key, enc_key_len, "%s", crypto_nonce);
    snprintf(iv, iv_len, "%s", crypto_iv);

    snprintf(key_iv, sizeof(key_iv), EXPORTER_CSP_NONCE_OBJ_STORE_STR, crypto_nonce, crypto_iv);

    EXPORTER_LOG(AL_DEBUG, "%s: [CSP NONCE] nonce = [%.*s] Key:IV = [%s:%s] Argo string = [%s]",
                        request->name, EXPORTER_CSP_DEBUG_BYTES, nonce, enc_key, iv, key_iv);

    res = object_store_set(request->name, nonce, key_iv, NULL, NULL, 0, ostore_role_object_store);
    if (res && (res != ZPATH_RESULT_ASYNCHRONOUS)) {
        EXPORTER_LOG(AL_ERROR, "%s: Could not store nonce cookie %.*s: %s", request->name,
                EXPORTER_CSP_DEBUG_BYTES, nonce, zpath_result_string(res));
        return res;
    }

    return ZPATH_RESULT_NO_ERROR;
}

/*
 * Function for encrypting nonce
 * Nonce is the object store key which has Key:IV pair for encryting JS payload
 */
static int exporter_csp_encrypt_nonce(struct exporter_request *request, void *nonce_value, size_t max_size, char *encoded_nonce)
{
    char encrypted[EXPORTER_URL_MAX_ENCODE_SIZE] = {0};
    size_t encrypted_len = sizeof(encrypted);
    size_t encoded_len = 0;
    struct zcrypt_key key = {{EXPORTER_CSP_OBJECT_ENC_KEY}};
    int res = ZPATH_RESULT_NO_ERROR;

    memset(encrypted, 0, EXPORTER_URL_MAX_ENCODE_SIZE);

    // Encrypt
    res = zcrypt_encrypt(&key, nonce_value, strlen(nonce_value), encrypted, &encrypted_len);
    if (res != ZCRYPT_RESULT_NO_ERROR) {
        EXPORTER_LOG(AL_ERROR, "%s: Unable to encrypt nonce %s", request->name, zpath_result_string(res));
        return ZPATH_RESULT_ERR;
    }

    //base64 encode
    encoded_len = base64_encoded_size(encrypted_len);
    if (encoded_len > max_size) {
        EXPORTER_LOG(AL_ERROR, "%s: Encoded length cannot be greater than MAX url size", request->name);
        return ZPATH_RESULT_ERR;
    }
    base64_encode_binary(encoded_nonce, (uint8_t*)encrypted, encrypted_len);

    EXPORTER_LOG(AL_DEBUG, "%s: [CSP_ENCRYPT_NONCE] encoded_nonce = %s", request->name, encoded_nonce);

    return ZPATH_RESULT_NO_ERROR;
}

/*
 * This API is used to decrypt the nonce
 * Idea is to have separate functions defined for CSP and do CSP
 * specific processing here
 */
static int exporter_csp_decrypt_nonce(struct exporter_request *request, char *nonce_value, char *decrypted_nonce)
{
    size_t nonce_len = 0;
    size_t nonce_binary_len = 0;
    size_t nonce_decrypted_len = 0;
    struct zcrypt_key key = {{EXPORTER_CSP_OBJECT_ENC_KEY}};
    unsigned char *encrypted = NULL;
    unsigned char *decrypted = NULL;
    int res = ZPATH_RESULT_NO_ERROR;
    int decoded_len = 0;

    EXPORTER_LOG(AL_DEBUG, "%s: exporter_csp_decrypt_nonce [CSP_DECRYPT_NONCE] nonce = [%s]",
                                                                               request->name,
                                                                               nonce_value);
    memset(decrypted_nonce, 0, EXPORTER_URL_MAX_ENCODE_SIZE);

    nonce_len = strlen(nonce_value);
    if (!nonce_len) {
        EXPORTER_LOG(AL_ERROR, "%s: Failed to get nonce_value length", request->name);
        return ZPATH_RESULT_ERR;
    }
    nonce_binary_len = base64_decoded_size(nonce_value, nonce_len);
    EXPORTER_LOG(AL_DEBUG, "%s: exporter_csp_decrypt_nonce [CSP_DECRYPT_NONCE] nonce = [%s]"
                           "nonce_len = [%ld]", request->name, nonce_value, nonce_binary_len);

    encrypted = EXPORTER_CALLOC(nonce_binary_len + 1);
    decrypted = EXPORTER_CALLOC(nonce_binary_len + 1);
    nonce_decrypted_len = nonce_binary_len;
    decoded_len = base64_decode_binary(encrypted, nonce_value, nonce_len);
    if (decoded_len <= 0 ) {
        EXPORTER_FREE(encrypted);
        EXPORTER_FREE(decrypted);
        return ZPATH_RESULT_BAD_ARGUMENT;
    }

    /* Decrypt 'encrypted' nonce using key */
    res = zcrypt_decrypt(&key, encrypted, nonce_binary_len, decrypted, &nonce_decrypted_len);
    if(res) {
        EXPORTER_LOG(AL_ERROR, "%s: Failed to decrypt nonce", request->name);
        EXPORTER_FREE(encrypted);
        EXPORTER_FREE(decrypted);
        return ZPATH_RESULT_BAD_ARGUMENT;
    }

    decrypted[nonce_decrypted_len] = '\0';
    EXPORTER_LOG(AL_DEBUG, "%s: Decrypted nonce = [%.*s] (len=%lu)", request->name, (int)nonce_decrypted_len,
                                                                     decrypted, nonce_decrypted_len);

    snprintf(decrypted_nonce, EXPORTER_URL_MAX_ENCODE_SIZE, "%s", decrypted);

    EXPORTER_FREE(encrypted);
    EXPORTER_FREE(decrypted);

    return res;
}

/*
 * exporter_create_pruned_alt_authsp_domain
 *  remove leading https:// from the domain; if any .
 *  This is needed, as we already prepend https:// while crafting the url.
 */
char* exporter_create_pruned_alt_authsp_domain(char *alt_domain)
{
    if (!alt_domain)
        return NULL;

    char *https_str = "https://";
    char *pruned_domain = NULL;

    /* alt_domain has to be longer than https prefix; else it isnt a valid one */
    if (strlen(alt_domain) <= strlen(https_str)) {
        return NULL;
    }

    /* alt_domain has to start with https:// ; else invalid */
    if (strncmp(alt_domain, https_str, strlen(https_str)) == 0) {
        char *new_pos = alt_domain + strlen(https_str);
        pruned_domain = ZPN_STRDUP(new_pos, strlen(new_pos));
        return pruned_domain;
    }

    /* fall through case, return NULL */
    return NULL;
}

static int request_path_auth_domain_from_unified_portal(struct exporter_request *request)
{
    char session_cookie_value[EXPORTER_COOKIE_MAX_SIZE];
    char domain_cookie_value[EXPORTER_COOKIE_MAX_SIZE];
    char session_crypto_value[EXPORTER_COOKIE_MAX_SIZE];
    const char *session_key_with_domain = NULL;
    char orig_url[EXPORTER_URL_MAX_ENCODE_SIZE];
    struct http_parser_url orig_url_parser;
    int64_t customer_gid = 0;
    int res = 0;
    char *s = NULL;
    char *e = NULL;
    const char *walk = NULL;
    char domain_buf[EXPORTER_URL_MAX_ENCODE_SIZE];
    const char *domain = NULL;
    char assertion_key[EXPORTER_URL_MAX_ENCODE_SIZE];

    /* Init all locals */
    memset(session_cookie_value, 0, sizeof(session_cookie_value));
    memset(domain_cookie_value, 0, sizeof(domain_cookie_value));
    memset(session_crypto_value, 0, sizeof(session_crypto_value));
    memset(orig_url, 0, sizeof(orig_url));
    memset(domain_buf, 0, sizeof(domain_buf));
    memset(assertion_key, 0, sizeof(assertion_key));

    /* No matter what, we need to get our customer_gid. We get this from the original url query value */
    customer_gid = url_get_customer_gid(request, request->url, &(request->url_parser), EXPORTER_QUERY_NAME_ORIGINAL_URL, orig_url, sizeof(orig_url), &orig_url_parser);
    if (!customer_gid) {
        EXPORTER_LOG(AL_WARNING, "%s: [UNIP_LOG_AUTH]Could not retrieve customer_gid given original request, url = %s", request->name, request->url);
        return exporter_request_respond(request, HTTP_STATUS_BAD_REQUEST, EXPORTER_ERROR_CODE_CUSTOMER_GID_NOT_FOUND);
    }

    char ctoken_rx[EXPORTER_CORS_TOKEN_SIZE] = {0};
    int64_t cors_found = url_get_cors_token(request, request->url, &(request->url_parser), EXPORTER_CORS_TOKEN, ctoken_rx, sizeof(ctoken_rx)-1, &orig_url_parser);
    if (cors_found) {
        request->log.cors_token_status = get_cors_token_status_str(ZPA_CORS_TOKEN_VALID);
        if (validate_cors_token(ctoken_rx, request) != ZPATH_RESULT_NO_ERROR) {
            EXPORTER_LOG(AL_ERROR, "%s: [UNIP_LOG_AUTH] invalid cors token", request->name);
            request->log.cors_token_status = get_cors_token_status_str(ZPA_CORS_TOKEN_INVALID);
            return exporter_request_respond(request, HTTP_STATUS_FORBIDDEN, EXPORTER_ERROR_CODE_INVALID_CORS_TOKEN);
        }
        request->cors_request_with_token = 1;
    }

    if (request->req_method != HTTP_GET) {
        EXPORTER_LOG(AL_ERROR, "%s: [UNIP_LOG_AUTH] Received %s auth domain request", request->name, http_method_names[request->req_method]);
        if (request->cors_request_with_token && request->req_method == HTTP_OPTIONS) {
            if (exporter_validate_no_auth_options_request(request) == ZPATH_RESULT_NO_ERROR) {
                return exporter_request_respond_no_content(request, HTTP_STATUS_NO_CONTENT, EXPORTER_ERROR_CODE_NO_ERROR);
            }
        }
        EXPORTER_LOG(AL_ERROR, "%s: [UNIP_LOG_AUTH] Method %s error in auth domain cors req token: %d", request->name,
                                http_method_names[request->req_method], request->cors_request_with_token );
        return exporter_request_respond(request, HTTP_STATUS_FORBIDDEN, EXPORTER_ERROR_CODE_INVALID_METHOD_FOR_AUTH);
    }

    res = find_cookie(request, EXPORTER_COOKIE_CRYPTO, 0, session_crypto_value, sizeof(session_crypto_value));
    if (res == ZPATH_RESULT_NO_ERROR) {
        res = find_cookie(request, EXPORTER_COOKIE_DOMAIN, 1, domain_cookie_value, sizeof(domain_cookie_value));
        if (res == ZPATH_RESULT_NO_ERROR) {

            /* If crypto and domain cookies are present, and manage chrome hard disabled is not enabled and
             * feature flag is enabled for the customer
             */

            /* Get session cookie for this domain, if it's there. */
            res = object_store_get(request->name,
                    domain_cookie_value,
                    &session_key_with_domain,
                    exporter_request_async_callback,
                    request,
                    0,
                    ostore_role_object_store);

            if (res) {
                if (res == ZPATH_RESULT_ASYNCHRONOUS) {
                    __sync_fetch_and_add_4(&(request->async_count), 1);
                    EXPORTER_DEBUG_AUTH("%s: [UNIP_LOG_AUTH] Fetching domain key asynchronously - async_count (%d->%d)",
                            request->name, request->async_count - 1, request->async_count);
                    return ZPATH_RESULT_NO_ERROR;
                } else if (res == ZPATH_RESULT_NOT_FOUND) {
                    session_key_with_domain = NULL;
                } else {
                    if (object_store_ready_count(ostore_role_object_store)) {
                        /* There is an object store... fall through to reauth. */
                        EXPORTER_LOG(AL_ERROR, "%s: [UNIP_LOG_AUTH] Object store read failed. Restarting auth to try a better store", request->name);
                        /* Fall through to re-auth... */
                    } else {
                        /* No object stores available. We're pretty much
                         * done here. */
                        EXPORTER_LOG(AL_ERROR, "%s: [UNIP_LOG_AUTH] No object stores available", request->name);
                        return exporter_request_respond(request, HTTP_STATUS_INTERNAL_SERVER_ERROR, EXPORTER_ERROR_CODE_OBJECT_STORES_UNAVAILABLE);
                    }
                }
            } else {
                /*
                 * Extract the session_key_with_domain into session_key and domain
                 */
                for (walk = session_key_with_domain; *walk; walk++) {
                    if ((*walk) == ',') domain = walk + 1;
                }
                if (!domain) {
                    EXPORTER_LOG(AL_WARNING, "%s: [UNIP_LOG_AUTH] Bad object: Has no domain, reauthing", request->name);
                    /* Fall through to re-auth... */
                } else {
                    /* Extract just the beginning of the session key- the part without the domain */
                    snprintf(session_cookie_value, sizeof(session_cookie_value), "%.*s",
                            (int)((domain - 1) - session_key_with_domain), session_key_with_domain);
                    snprintf(domain_buf, sizeof(domain_buf), "%s", domain);
                    zpath_downcase(domain_buf);
                    domain = domain_buf;

                    /* Verify this key is for our domain... This is a substring match, but is probably appropriate */
                    if (!strstr(request->conn->sni, domain)) {
                        /* No match */
                        EXPORTER_LOG(AL_WARNING, "%s: [UNIP_LOG_AUTH] Session key domain mismatch, reauthing. req SNI = %s, value = %s, domain = %s",
                                request->name,
                                request->conn->sni, session_key_with_domain, domain);
                        /* Fall through to re-auth... */
                    } else {
                        res = request_authenticate_session_key(request, request->conn->exporter_domain->customer_gid, session_cookie_value, assertion_key, sizeof(assertion_key));
                        if (res) {
                            if (res == ZPATH_RESULT_ASYNCHRONOUS) {
                                EXPORTER_DEBUG_AUTH("%s: [UNIP_LOG_AUTH] Authenticating - asynchronous fetch", request->name);
                                return ZPATH_RESULT_NO_ERROR;
                            } else {
                                /* Auth failed. */
                                EXPORTER_DEBUG_AUTH("%s: [UNIP_LOG_AUTH] Authentication fetch failed", request->name);
                                /* Fall through to re-auth */
                            }
                        } else {
                            /* Authenticated! */
                            const char *assertion;
                            res = object_store_get(request->name,
                                    assertion_key,
                                    &assertion,
                                    NULL,
                                    NULL,
                                    0,
                                    ostore_role_object_store);

                            if (res) {
                                EXPORTER_LOG(AL_ERROR, "%s: [UNIP_LOG_AUTH] Could not retrieve assertion we just retrieved...", request->name);
                                /* Fall through to reauth... */
                            } else {

                                /* Decrypt assertion if necessary */
                                char *typ = strchr(assertion_key, ',');
                                if (!typ) {
                                    EXPORTER_LOG(AL_ERROR, "%s: [UNIP_LOG_AUTH] No valid data from assertion key, doing reauth...", request->name);
                                    /* Fall through to reauth... */
                                } else {
                                    /* base64 encrypted & decrypted respectively */
                                    uint8_t *encrypted = NULL;
                                    uint8_t *decrypted = NULL;
                                    uint8_t *unbase64 = NULL;
                                    int b64_len = 0;
                                    if (strncmp(typ, ",spe,", 5) == 0) {
                                        /* Need to decrypt. */
                                        struct zcrypt_key key = {{0}};
                                        size_t str_len = strnlen(session_crypto_value,EXPORTER_COOKIE_MAX_SIZE);
                                        size_t bin_len;
                                        size_t decrypted_len;
                                        char nameid[1000];
                                        char fname[1000];
                                        char lname[1000];
                                        EXPORTER_LOG(AL_CRITICAL, "Implement Me: Only decrypt once, please!!!!");
                                        if (base64_decoded_size(session_crypto_value, str_len) <= sizeof(struct zcrypt_key)) {
                                            base64_decode_binary((unsigned char *)&key, session_crypto_value, str_len);
                                        } else {
                                            EXPORTER_LOG(AL_ERROR, "%s: [UNIP_LOG_AUTH] Crypto key too large, doing reauth...", request->name);
                                            goto auth_failed;
                                        }
                                        str_len = strlen(assertion);
                                        bin_len = base64_decoded_size(assertion, str_len);
                                        /* Allocate space for
                                         * encrypted and decrypted
                                         * assertion. Note: Decrypted
                                         * assertion will always be
                                         * smaller than encrypted
                                         * assertion due to IV */
                                        encrypted = EXPORTER_MALLOC(bin_len + 1);
                                        decrypted = EXPORTER_MALLOC(bin_len + 1);
                                        unbase64 = EXPORTER_MALLOC(bin_len + 1);
                                        decrypted_len = bin_len;
                                        base64_decode_binary(encrypted, assertion, str_len);
                                        res = zcrypt_decrypt(&key, encrypted, bin_len, decrypted, &decrypted_len);
                                        if (res) {
                                            EXPORTER_LOG(AL_ERROR, "%s: [UNIP_LOG_AUTH] Failed to decrypt assertion, do reauth...", request->name);
                                            EXPORTER_FREE(encrypted);
                                            EXPORTER_FREE(decrypted);
                                            EXPORTER_FREE(unbase64);
                                            //require_reauth = 1;
                                            goto auth_failed;
                                        }
                                        EXPORTER_FREE(encrypted);
                                        /* Null terminate... */
                                        if (decrypted) {
                                            decrypted[decrypted_len] = 0;
                                        }
                                        EXPORTER_DEBUG_AUTH("%s: [UNIP_LOG_AUTH] Decrypted assertion to %.*s", request->name,
                                                            EXPORTER_DEBUG_BYTES, decrypted);

                                        b64_len = base64_decode_binary(unbase64, (const char *)decrypted, decrypted_len);
                                        if (b64_len > 0) {
                                            if (unbase64) {
                                                unbase64[b64_len] = 0;
                                            }
                                            res = zsaml_get_nameid((const char *)unbase64,
                                                    b64_len,
                                                    nameid,
                                                    sizeof(nameid));
                                            if (res == ZSAML_RESULT_NO_ERROR) {
                                                zpath_downcase(nameid); /* required for SCIM user lookup */
                                                if (request->log.nameid) {
                                                    EXPORTER_FREE(request->log.nameid);
                                                    request->log.nameid = NULL;
                                                }
                                                request->log.nameid = EXPORTER_STRDUP(nameid, strlen(nameid));
                                                request->is_authenticated = 1;
                                                res = zsaml_get_username((const char *)unbase64,
                                                        b64_len,
                                                        fname,
                                                        lname,
                                                        sizeof(fname));
                                                if (res == ZSAML_RESULT_NO_ERROR) {
                                                    if (request->log.lname) {
                                                        EXPORTER_FREE(request->log.lname);
                                                        request->log.lname = NULL;
                                                    }
                                                    if (request->log.fname) {
                                                        EXPORTER_FREE(request->log.fname);
                                                        request->log.fname = NULL;
                                                    }
                                                    request->log.lname = EXPORTER_STRDUP(lname, strlen(lname));
                                                    request->log.fname = EXPORTER_STRDUP(fname, strlen(fname));
                                                } else {
                                                    EXPORTER_LOG(AL_ERROR, "%s: [UNIP_LOG_AUTH] Could not extract first/last name from assertion", request->name);
                                                    res = ZSAML_RESULT_NO_ERROR;
                                                }
                                            } else {
                                                EXPORTER_LOG(AL_ERROR, "%s: [UNIP_LOG_AUTH] Could not extract nameid from assertion", request->name);
                                                if (unbase64) EXPORTER_FREE(unbase64);
                                                if (decrypted) EXPORTER_FREE(decrypted);
                                                return res;
                                            }
                                        } else {
                                            EXPORTER_LOG(AL_ERROR, "%s: [UNIP_LOG_AUTH] Could not extract assertion from b64 assertion", request->name);
                                            if (unbase64) EXPORTER_FREE(unbase64);
                                            if (decrypted) EXPORTER_FREE(decrypted);
                                            return ZPATH_RESULT_ERR;
                                        }

                                        encrypted = NULL;
                                    } else {
                                        /* No need to decrypt... assertion is already correct */
                                        //EXPORTER_DEBUG_CSP("%s: No need to decrypt... assertion is already correct", request->name);
                                    }

                                    if (decrypted) EXPORTER_FREE(decrypted);
                                    if (unbase64) EXPORTER_FREE(unbase64);
                                    if ((ZPN_RESULT_ASYNCHRONOUS == res) || (ZPATH_RESULT_NO_ERROR == res)) {
                                        /* Happiness */

                                        //generate cookies for pra portal
                                    } else {
                                        /* logging is better within the callee, so ignore logging here */
                                        EXPORTER_DEBUG_HTTP("%s: [UNIP_LOG_AUTH] request_path_default_cb processing failed: %s", request->name,
                                                            zpath_result_string(res));
                                        return exporter_request_respond(request, HTTP_STATUS_INTERNAL_SERVER_ERROR, EXPORTER_ERROR_CODE_DEFAULT_PATH_CALLBACK_FAILED);
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    /* Authenticated! Domain clearly doesn't have cookie, so
     * create/set one, unless we are explicitly doing re-auth
     * at the behest of ZPA */
    char pra_domain_cookie_value[EXPORTER_COOKIE_MAX_SIZE];
    char pra_session_key_with_domain[EXPORTER_COOKIE_MAX_SIZE];
    char orig_domain[EXPORTER_URL_MAX_ENCODE_SIZE];

    /* We must not have had a domain cookie pointing at this
     * session state, so redirect back to domain and set it up!
     * For this we need a */
    res = object_store_gen_key(OBJECT_STORE_TYPE_DOMAIN, session_cookie_value, pra_domain_cookie_value,
            sizeof(pra_domain_cookie_value), ostore_role_object_store);
    if (res) {
        EXPORTER_LOG(AL_ERROR, "%s: [UNIP_LOG_AUTH] Could not generate key", request->name);
        return exporter_request_respond(request, HTTP_STATUS_INTERNAL_SERVER_ERROR, EXPORTER_ERROR_CODE_DOMAIN_COOKIE_GENERATION_FAILED);
    }
    /* Get original domain, without trailing :port */
    snprintf(orig_domain, sizeof(orig_domain), "%.*s", orig_url_parser.field_data[UF_HOST].len, &(orig_url[orig_url_parser.field_data[UF_HOST].off]));
    /* Write cookie to object store including original domain */
    s = pra_session_key_with_domain;
    e = s + sizeof(pra_session_key_with_domain);
    s += sxprintf(s, e, "%s,%s", session_cookie_value, orig_domain);

    /* Install the cookie in the cookie store. We do this on
     * the assumption that the install will beat the eventual
     * query for it, which is very likely. */
    res = object_store_set(request->name,
            pra_domain_cookie_value,
            pra_session_key_with_domain,
            NULL,
            NULL,
            0,
            ostore_role_object_store);
    if (res && (res != ZPATH_RESULT_ASYNCHRONOUS)) {
        /* Note: no async count change because we set no callback */
        EXPORTER_LOG(AL_ERROR, "%s: [UNIP_LOG_AUTH] Could not set cookie", request->name);
        return exporter_request_respond(request, HTTP_STATUS_INTERNAL_SERVER_ERROR, EXPORTER_ERROR_CODE_DOMAIN_COOKIE_SET_FAILED);
    }

    char *DOM_COOKIE = EXPORTER_QUERY_NAME_TO_DOMAIN_SET_COOKIE; /* cookie */
    char *url_domain_cookie = pra_domain_cookie_value;
    char encoded_ecookie_domain[EXPORTER_URL_MAX_ENCODE_SIZE] = {'\0'};
    if (is_url_cookie_encryption_enabled(customer_gid)) {
        /* encrypt domain cookie in the URL */
        res = exporter_encrypt_dom_cookie(request, pra_domain_cookie_value, EXPORTER_URL_MAX_ENCODE_SIZE, encoded_ecookie_domain);
        if (res) {
            return exporter_request_respond(request, HTTP_STATUS_BAD_REQUEST, EXPORTER_ERROR_CODE_DOMAIN_COOKIE_ENCRYPTION_FAILED);
        }
        DOM_COOKIE = EXPORTER_QUERY_NAME_TO_DOMAIN_SET_ENCRYPTED_COOKIE; /* ecookie */
        url_domain_cookie = encoded_ecookie_domain;
    }

    /*
     * Send redirect...
     */
    /* Redirect back to domain with this cookie for installation */
    if (request->cors_request_with_token) {
        return exporter_request_redirect_encode(request,
                NULL,
                NULL,
                NULL,
                NULL,
                orig_domain,
                (orig_url_parser.field_set & (1 << UF_PORT)) ? orig_url_parser.port : 443,
                EXPORTER_PATH_CUSTOMER_DOMAIN_INSTALL_COOKIE,
                EXPORTER_QUERY_NAME_ORIGINAL_URL, orig_url,
                DOM_COOKIE, url_domain_cookie,
                EXPORTER_QUERY_NAME_TO_DOMAIN_SET_CRYPTO_COOKIE, session_crypto_value,
                EXPORTER_CORS_TOKEN, ctoken_rx,
                NULL);

    }

auth_failed:
    return res;
}

static int request_path_auth_domain_from_customer_domain(struct exporter_request *request)
{
    char session_cookie_value[200];
    char session_crypto_value[200];
    char orig_url[EXPORTER_URL_MAX_ENCODE_SIZE];
    char set_cookie[EXPORTER_URL_MAX_ENCODE_SIZE];
    char set_cookie_legacy[EXPORTER_URL_MAX_ENCODE_SIZE];
    struct http_parser_url orig_url_parser;
    struct zpn_idp *idps[1000];
    struct zpath_customer *customer = NULL;
    char *idp_domain = NULL;
    char idp_gid[32];
    size_t count = sizeof(idps)/sizeof(idps[0]);
    int64_t customer_gid;
    int res;
    int set_crypto_cookie_value = 0;
    char *s;
    char *e;
    int ret = -1;
    int arb_domain_feature_enabled = 0;
    char levelid[100] = {0};
    int is_levelid = 0;

    /* Verify that the request is to the auth host */
    if (!request->conn->exporter_domain->is_auth_domain) {
        EXPORTER_LOG(AL_ERROR, "%s: Received request for auth path that isn't to auth domain", request->name);
        return exporter_request_respond(request, HTTP_STATUS_NOT_FOUND, EXPORTER_ERROR_CODE_INVALID_AUTH_DOMAIN);
    }

    /* No matter what, we need to get our customer_gid. We get this from the original url query value */
    customer_gid = url_get_customer_gid(request, request->url, &(request->url_parser), EXPORTER_QUERY_NAME_ORIGINAL_URL, orig_url, sizeof(orig_url), &orig_url_parser);
    if (!customer_gid) {
        EXPORTER_LOG(AL_WARNING, "%s: Could not retrieve customer_gid given original request, url = %s", request->name, request->url);
        return exporter_request_respond(request, HTTP_STATUS_BAD_REQUEST, EXPORTER_ERROR_CODE_CUSTOMER_GID_NOT_FOUND);
    }

    arb_domain_feature_enabled = is_arbitrary_auth_domain_enabled(customer_gid);
    char ctoken_rx[EXPORTER_CORS_TOKEN_SIZE] = {0};
    int64_t cors_found = url_get_cors_token(request, request->url, &(request->url_parser), EXPORTER_CORS_TOKEN, ctoken_rx, sizeof(ctoken_rx)-1, &orig_url_parser);
    if (cors_found) {
        request->log.cors_token_status = get_cors_token_status_str(ZPA_CORS_TOKEN_VALID);
        if (validate_cors_token(ctoken_rx, request) != ZPATH_RESULT_NO_ERROR) {
            EXPORTER_LOG(AL_ERROR, "%s: invalid cors token", request->name);
            request->log.cors_token_status = get_cors_token_status_str(ZPA_CORS_TOKEN_INVALID);
            return exporter_request_respond(request, HTTP_STATUS_FORBIDDEN, EXPORTER_ERROR_CODE_INVALID_CORS_TOKEN);
        }
        request->cors_request_with_token = 1;
    }

    if (request->req_method != HTTP_GET) {
        EXPORTER_LOG(AL_ERROR, "%s: Received %s auth domain request", request->name, http_method_names[request->req_method]);
        if (request->cors_request_with_token && request->req_method == HTTP_OPTIONS) {
            if (exporter_validate_no_auth_options_request(request) == ZPATH_RESULT_NO_ERROR) {
                return exporter_request_respond_no_content(request, HTTP_STATUS_NO_CONTENT, EXPORTER_ERROR_CODE_NO_ERROR);
            }
        }
        EXPORTER_LOG(AL_ERROR, "%s: Method %s error in auth domain cors req token: %d", request->name, http_method_names[request->req_method],
                                                                                          request->cors_request_with_token );
        return exporter_request_respond(request, HTTP_STATUS_FORBIDDEN, EXPORTER_ERROR_CODE_INVALID_METHOD_FOR_AUTH);
    }

    /* Get crypto value, and/or generate it. */
    res = find_cookie(request, EXPORTER_COOKIE_CRYPTO, 0, session_crypto_value, sizeof(session_crypto_value));
    if (res) {
        int64_t rbytes[4];
        EXPORTER_DEBUG_AUTH("%s: No crypto cookie, generating one.", request->name);
        if (RAND_bytes((unsigned char *)rbytes, sizeof(rbytes)) == 0) {
            /* Error generating random number */
            EXPORTER_LOG(AL_CRITICAL, "%s: Could not generate random bytes for crypto key", request->name);
            return exporter_request_respond(request, HTTP_STATUS_INTERNAL_SERVER_ERROR, EXPORTER_ERROR_CODE_CRYPTO_COOKIE_GENERATION_FAILED);
        }
        size_t len = base64_encoded_size(sizeof(rbytes));
        if ((len + 1) >= sizeof(session_crypto_value)) {
            EXPORTER_LOG(AL_CRITICAL, "%s: Crypto key too big", request->name);
            return exporter_request_respond(request, HTTP_STATUS_INTERNAL_SERVER_ERROR, EXPORTER_ERROR_CODE_CRYPTO_COOKIE_GENERATION_FAILED);
        }
        base64_encode_binary(session_crypto_value, (const unsigned char *)rbytes, sizeof(rbytes));

        set_crypto_cookie_value = 1;
        make_cookie(EXPORTER_DOMAIN_AUTH, EXPORTER_COOKIE_CRYPTO, session_crypto_value, "None", EXPORTER_COOKIE_CRYPTO_LIFETIME_S, set_cookie, sizeof(set_cookie), set_cookie_legacy, sizeof(set_cookie_legacy));
    }



    /* Get session cookie. */
    res = find_cookie(request, EXPORTER_COOKIE_SESSION, 1, session_cookie_value, sizeof(session_cookie_value));
    if (res) {
        EXPORTER_DEBUG_AUTH("%s: No session cookie", request->name);
        /* Fall through to re-auth */
    } else {
        res = request_authenticate_session_key(request, customer_gid, session_cookie_value, NULL, 0);
        if (res) {
            if (res == ZPATH_RESULT_ASYNCHRONOUS) {
                EXPORTER_DEBUG_AUTH("%s: Authenticating- asynchronous fetch", request->name);
                return ZPATH_RESULT_NO_ERROR;
            } else {
                /* Auth failed. */
                EXPORTER_DEBUG_AUTH("%s: Authentication fetch failed, re-authing", request->name);
                /* Fall through to re-auth */
            }
        } else {
            /* Authenticated! Domain clearly doesn't have cookie, so
             * create/set one, unless we are explicitly doing re-auth
             * at the behest of ZPA */
            char domain_cookie_value[EXPORTER_COOKIE_MAX_SIZE];
            char session_key_with_domain[EXPORTER_COOKIE_MAX_SIZE];
            char orig_domain[EXPORTER_URL_MAX_ENCODE_SIZE];

            res = query_string_find(request->url, &(request->url_parser), EXPORTER_QUERY_NAME_REQUIRE_REAUTH, NULL, 0);
            if (res == ZPATH_RESULT_NO_ERROR) {
                /*
                 * Looks like the domain asked us to re-auth, so we will toss this customer
                 * Deauthentication, single company:
                 *
                 * 1. Redirect to authSP.
                 *
                 * 2. On return from authSP, will recognize that token
                 *    has changed. Use the opportunity to delete old
                 *    auth entry, rewrite session to a new ID, and
                 *    rewrite cookie to the new ID
                 */
                EXPORTER_DEBUG_AUTH("%s: Domain has requested reauthentication. Reprocessing URL. url = %s", request->name, request->url);
                res = query_string_find(request->url, &(request->url_parser), EXPORTER_QUERY_NAME_TO_SP_LEVELID, levelid, sizeof(levelid));
                if (res == ZPATH_RESULT_NO_ERROR) {
                    EXPORTER_DEBUG_AUTH("%s: Reauth is due to wrong levelid: %s", request->name, levelid);
                    is_levelid = 1;
                }
                /* Fall through to reauth. */
            } else {
                res = query_string_find(request->url, &(request->url_parser), EXPORTER_QUERY_NAME_IDP_SELECT_DOMAIN, NULL, 0);
                if (res == ZPATH_RESULT_NO_ERROR) {
                    /*
                     * Looks like the query has a domain selected from
                     * challenge window for IDP selection, which means
                     * we are doing reauth. Thus we fall through to
                     * reauth, just like the case immediately above.
                     */
                    EXPORTER_DEBUG_AUTH("%s: Domain has requested reauthentication (IDP selec). Reprocessing URL. url = %s", request->name, request->url);
                    /* Fall through to reauth. */
                } else {

                    /* CSP: Redirect to getcanvasfingerprint with entire url as query in fromsporigurl
                     * Only if request is authenticated else fingerprint will be sent from fromsp
                     */

                    /* CSP_POLICY START*/
                    EXPORTER_DEBUG_CSP("%s: CSP_FEATURE status %s for customer: [%"PRId64"] domain: [%s] in path: [%s] cors: %d", request->name,
                            request->is_csp_enabled ? "enabled" : "disabled",
                            request->orig_customer_gid, request->orig_domain, request->log.url,
                            request->cors_request_with_token);

                    const char *assertion_cookie = request->assertion_key;

                    /* Do not inject JS if this is a CORS request */
                    if (request->is_csp_enabled && 0 == request->cors_request_with_token) {
                        res = exporter_load_policy(request, assertion_cookie, session_crypto_value);
                        if (res) {
                            if (res == ZPATH_RESULT_ASYNCHRONOUS) {
                                return ZPATH_RESULT_NO_ERROR;
                            } else if (res == ZPN_RESULT_EXPIRED) {
                                /* Reauth code must have set redirection so we are returning */
                                return ZPATH_RESULT_NO_ERROR;
                            } else {
                                /* We are in doauth - policy read fails so we do not know status
                                 * if app needs to be monitored or not, so we must continue normal
                                 * flow to give access to app
                                 */
                            }
                        }
                        res = exporter_read_csp_config(request);
                        if (res == ZPN_RESULT_ASYNCHRONOUS) {
                            return ZPATH_RESULT_NO_ERROR;
                        }

                        if (request->is_mon_enabled) {
                            char redirect_url[2*EXPORTER_URL_MAX_ENCODE_SIZE];
                            char buf[1024];
                            const char *s_msg;
                            int ret_encryption = ZPATH_RESULT_NO_ERROR;

                            memset(buf, 0, sizeof(buf));
                            memset(redirect_url, 0, sizeof(redirect_url));
                            s_msg = extract_msg_between_tokens(buf, sizeof(buf), request->url, "/", "/doauth?");

                            /* Nonce handling so that nobody can bypass fingerprinting using exception URL */
                            char nonce_cookie_value[EXPORTER_COOKIE_MAX_SIZE] = {0};
                            char nonce_object_key[EXPORTER_COOKIE_MAX_SIZE] = {0};
                            char encrypted_nonce_object_key[EXPORTER_COOKIE_MAX_SIZE] = {0};
                            char crypto_nonce[EXPORTER_CSP_ENC_KEY_LEN + 1] = {0};
                            char crypto_iv[EXPORTER_CSP_ENC_IV_LEN + 1] = {0};

                            if (request->js_encryption_enabled) {
                                ret_encryption = exporter_csp_get_encryption_context(request, request->cb_session_cookie,
                                                     nonce_object_key, sizeof(nonce_object_key),
                                                     crypto_nonce, sizeof(crypto_nonce),
                                                     crypto_iv, sizeof(crypto_iv));
                                if (ret_encryption) {
                                    EXPORTER_LOG(AL_ERROR, "%s: [CSP_ENCRYPT_NONCE] Could not generate nonce, Key, IV", request->name);
                                } else {
                                    ret_encryption = exporter_csp_encrypt_nonce(request, nonce_object_key, EXPORTER_COOKIE_MAX_SIZE,
                                                                     encrypted_nonce_object_key);
                                    if (!ret_encryption) {
                                        EXPORTER_LOG(AL_DEBUG, "%s [CSP_ENCRYPT_NONCE] Here is the encrypted nonce [%s]",
                                                                 request->name,
                                                                 encrypted_nonce_object_key);
                                    }
                                }
                            }

                            res = exporter_csp_create_nonce(request, assertion_cookie,
                                    nonce_cookie_value, sizeof(nonce_cookie_value));

                            if (res) {
                                EXPORTER_LOG(AL_ERROR, "%s: Could not generate nonce", request->name);
                                snprintf(redirect_url, sizeof(redirect_url), "https://%s:%d/%s/doauth2?%s",
                                        EXPORTER_DOMAIN_AUTH, 443, buf, s_msg);
                            } else {
                                snprintf(redirect_url, sizeof(redirect_url), "https://%s:%d/%s/doauth2?%s&nonce=%s",
                                        EXPORTER_DOMAIN_AUTH, 443, buf, s_msg, nonce_cookie_value);
                            }

                            EXPORTER_DEBUG_CSP("CSP Changing to doauth2 url: [%s]",
                                    redirect_url);

                            /* We can send data here also but in redirect message page is not loaded
                             * So we send redirect and page is loaded from there
                             */
                            if (request->js_encryption_enabled && ret_encryption != ZPATH_RESULT_NO_ERROR) {
                                EXPORTER_LOG(AL_ERROR, "%s: [CSP_ENCRYPT_NONCE] Failed to generate Nonce, not redirecting [%s]!!!",
                                                       request->name, zpath_result_string(ret_encryption));
                            } else {

                                if (request->js_encryption_enabled) {
                                    return exporter_request_redirect_encode(request,
                                            NULL,
                                            NULL,
                                            NULL,
                                            NULL,
                                            EXPORTER_DOMAIN_AUTH,
                                            443,
                                            (request->is_collect_location ? EXPORTER_PATH_GET_CANVAS_FINGERPRINT_GEO_ENC : EXPORTER_PATH_GET_CANVAS_FINGERPRINT_ENC),
                                            "doauthorigurl", redirect_url,
                                            EXPORTER_QUERY_NAME_ORIGINAL_URL, orig_url,
                                            EXPORTER_QUERY_NAME_TO_SP_OS_ENCKEY, encrypted_nonce_object_key,
                                            EXPORTER_QUERY_NAME_TO_SP_CRYPTO_ENCKEY, crypto_nonce,
                                            EXPORTER_QUERY_NAME_TO_SP_CRYPTO_IV, crypto_iv,
                                            NULL);
                                } else {
                                    return exporter_request_redirect_encode(request,
                                            NULL,
                                            NULL,
                                            NULL,
                                            NULL,
                                            EXPORTER_DOMAIN_AUTH,
                                            443,
                                            (request->is_collect_location ? EXPORTER_PATH_GET_CANVAS_FINGERPRINT_GEO : EXPORTER_PATH_GET_CANVAS_FINGERPRINT),
                                            "doauthorigurl", redirect_url,
                                            EXPORTER_QUERY_NAME_ORIGINAL_URL, orig_url,
                                            NULL);
                                }
                            } //ret_encryption!=ZPATH_RESULT_NO_ERROR
                        }
                    }//is_csp_enabled
                    /* CSP_POLICY END */

                    /* We must not have had a domain cookie pointing at this
                     * session state, so redirect back to domain and set it up!
                     * For this we need a */
                    res = object_store_gen_key(OBJECT_STORE_TYPE_DOMAIN, session_cookie_value, domain_cookie_value,
                                               sizeof(domain_cookie_value), ostore_role_object_store);
                    if (res) {
                        EXPORTER_LOG(AL_ERROR, "Could not generate key");
                        return exporter_request_respond(request, HTTP_STATUS_INTERNAL_SERVER_ERROR, EXPORTER_ERROR_CODE_DOMAIN_COOKIE_GENERATION_FAILED);
                    }
                    /* Get original domain, without trailing :port */
                    snprintf(orig_domain, sizeof(orig_domain), "%.*s", orig_url_parser.field_data[UF_HOST].len, &(orig_url[orig_url_parser.field_data[UF_HOST].off]));
                    /* Write cookie to object store including original domain */
                    s = session_key_with_domain;
                    e = s + sizeof(session_key_with_domain);
                    s += sxprintf(s, e, "%s,%s", session_cookie_value, orig_domain);

                    /* Install the cookie in the cookie store. We do this on
                     * the assumption that the install will beat the eventual
                     * query for it, which is very likely. */
                    res = object_store_set(request->name,
                                           domain_cookie_value,
                                           session_key_with_domain,
                                           NULL,
                                           NULL,
                                           0,
                                           ostore_role_object_store);
                    if (res && (res != ZPATH_RESULT_ASYNCHRONOUS)) {
                        /* Note: no async count change because we set no callback */
                        EXPORTER_LOG(AL_ERROR, "%s: Could not set cookie", request->name);
                        return exporter_request_respond(request, HTTP_STATUS_INTERNAL_SERVER_ERROR, EXPORTER_ERROR_CODE_DOMAIN_COOKIE_SET_FAILED);
                    }
                    if (request->is_csp_enabled) {
                        /* If CSP feature is enabled but user is not monitored then also we
                         * need to have CSP transaction which capture non-monitored users.
                         * For this add an entry in object store with '0' data. In default pathcb this
                         * data will be used to mark information with/without bfp data*/
                        EXPORTER_DEBUG_CSP("%s: CSP_DATA: user not monitored domain cookie %.*s adding in object store",
                                     request->name, EXPORTER_CSP_DEBUG_BYTES, domain_cookie_value);
                        res = exporter_write_domain_csp_data(request->name,
                                                             domain_cookie_value,
                                                             0,//fill mbfp
                                                             request->csp_data,
                                                             NULL,
                                                             NULL,
                                                             NULL,
                                                             0);

                        if (res && (res != ZPATH_RESULT_ASYNCHRONOUS)) {
                            EXPORTER_LOG(AL_ERROR, "%s: CSP_DATA do_auth default handling Could not set csp_dom_key  %.*s: %s",
                                         request->name, EXPORTER_CSP_DEBUG_BYTES, domain_cookie_value, zpath_result_string(res));
                        }
                    }

                    char *DOM_COOKIE = EXPORTER_QUERY_NAME_TO_DOMAIN_SET_COOKIE; /* cookie */
                    char *url_domain_cookie = domain_cookie_value;
                    char encoded_ecookie_domain[EXPORTER_URL_MAX_ENCODE_SIZE] = {'\0'};
                    if (is_url_cookie_encryption_enabled(customer_gid)) {
                        /* encrypt domain cookie in the URL */
                        res = exporter_encrypt_dom_cookie(request, domain_cookie_value, EXPORTER_URL_MAX_ENCODE_SIZE, encoded_ecookie_domain);
                        if (res) {
                            return exporter_request_respond(request, HTTP_STATUS_BAD_REQUEST, EXPORTER_ERROR_CODE_DOMAIN_COOKIE_ENCRYPTION_FAILED);
                        }
                        DOM_COOKIE = EXPORTER_QUERY_NAME_TO_DOMAIN_SET_ENCRYPTED_COOKIE; /* ecookie */
                        url_domain_cookie = encoded_ecookie_domain;
                    }

                    /*
                     * Send redirect...
                     */
                    /* Redirect back to domain with this cookie for installation */
                    if (request->cors_request_with_token) {
                        return exporter_request_redirect_encode(request,
                                                                set_crypto_cookie_value ? set_cookie : NULL,
                                                                set_crypto_cookie_value ? set_cookie_legacy : NULL,
                                                                NULL,
                                                                NULL,
                                                                orig_domain,
                                                                (orig_url_parser.field_set & (1 << UF_PORT)) ? orig_url_parser.port : 443,
                                                                EXPORTER_PATH_CUSTOMER_DOMAIN_INSTALL_COOKIE,
                                                                EXPORTER_QUERY_NAME_ORIGINAL_URL, orig_url,
                                                                DOM_COOKIE, url_domain_cookie,
                                                                EXPORTER_QUERY_NAME_TO_DOMAIN_SET_CRYPTO_COOKIE, session_crypto_value,
                                                                EXPORTER_CORS_TOKEN, ctoken_rx,
                                                                NULL);

                    } else {
                        return exporter_request_redirect_encode(request,
                                                                set_crypto_cookie_value ? set_cookie : NULL,
                                                                set_crypto_cookie_value ? set_cookie_legacy : NULL,
                                                                NULL,
                                                                NULL,
                                                                orig_domain,
                                                                (orig_url_parser.field_set & (1 << UF_PORT)) ? orig_url_parser.port : 443,
                                                                EXPORTER_PATH_CUSTOMER_DOMAIN_INSTALL_COOKIE,
                                                                EXPORTER_QUERY_NAME_ORIGINAL_URL, orig_url,
                                                                DOM_COOKIE, url_domain_cookie,
                                                                EXPORTER_QUERY_NAME_TO_DOMAIN_SET_CRYPTO_COOKIE, session_crypto_value,
                                                                NULL);
                    }
                }
            }
        }
    }

    /* unified-portal - For a CORS request redirect to authsp or showing idp login page will not work
     * The page has to be refreshed and origin has to be reauth and only then CORS work
     * We don't want to change the flow for exporter but for unified-portal we will simply return error here
     */

    /* Start auth process. Note: We don't know/care about what session
     * exists here. We update that stuff on return from IDP */


    /* Need to get/select IDPs */
    res = zpn_idp_get_customer_gid(customer_gid,
                                   &(idps[0]),
                                   &count,
                                   exporter_request_wally_callback,
                                   request,
                                   0);
    if (res) {
        if (res == ZPATH_RESULT_ASYNCHRONOUS) {
            __sync_fetch_and_add_4(&(request->async_count), 1);
            /* Do nothing. */
            EXPORTER_DEBUG_AUTH("%s: Fetching IDP asynchronously for customer gid = %ld async_count (%d->%d)",
                    request->name, (long)customer_gid, request->async_count -1, request->async_count);
            return ZPATH_RESULT_NO_ERROR;
        } else {
            EXPORTER_LOG(AL_ERROR, "%s: Fetching IDP failed for customer gid = %ld: %s", request->name, (long) customer_gid, zpath_result_string(res));
            return exporter_request_respond(request, HTTP_STATUS_FORBIDDEN, EXPORTER_ERROR_CODE_IDP_FETCH_FAILED);
        }
    }

    int idp_id = 0;
    int encrypt = 0;
    int found_username = 0;
    char q_domain[EXPORTER_URL_MAX_ENCODE_SIZE] = {0};
    if (count > 1) {
        size_t i;
        int j = 0;
        char *q_domain_ptr;
        char buf[64*1024];
        memset(buf, 0, sizeof(buf));
        for (i = 0; i < count; i++) {
            if (!(idps[i]->is_user_idp)) continue;
            if (idps[i]->oneidentity_enabled
                    && idps[i]->iam_idp_id && !strncmp(idps[i]->iam_idp_id, "Hosted", sizeof("Hosted") - 1))
            {
                idp_id = i;
                j = 1;              //If one idp is enabled we have to ignore the other idps
                break;
            }
            j++;
            idp_id = i;
        }
        if (j > 1) {
            res = query_string_find(request->url, &(request->url_parser), EXPORTER_QUERY_NAME_IDP_SELECT_DOMAIN, q_domain, sizeof(q_domain));
            if (res) {
                /* No idp domain specified... ask for one. */
                res = string_replace(idp_select_js, buf, sizeof(buf), "error", "", NULL);
                if (res) {
                    EXPORTER_LOG(AL_ERROR, "%s: Could not string replace", request->name);
                    return exporter_request_respond(request, HTTP_STATUS_INTERNAL_SERVER_ERROR, EXPORTER_ERROR_CODE_IDP_REPLACE_FAILED);
                }
                if (!request->header_origin) {
                    return exporter_request_respond_data(request, buf, strlen(buf));
                } else {
                    return exporter_request_respond_acao_status_and_data(request, HTTP_STATUS_OK, EXPORTER_ERROR_CODE_NO_ERROR, buf, strlen(buf));
                }
            } else {
                /* Have a domain in query string... Need to see if it
                 * matches one of our idp domains. */
                /* For each IDP... */
                found_username = 1;
                exporter_url_decode(q_domain);
                if ((q_domain_ptr = strchr(q_domain, '@'))) {
                    q_domain_ptr ++;
                } else {
                    q_domain_ptr = &(q_domain[0]);
                }

                for (i = 0; (idp_domain == NULL) && (i < count); i++) {
                    /* Skip admin IDPs */
                    if (!(idps[i]->is_user_idp)) continue;

                    /*For each domain specified in the IDP */
                    for (j = 0; j < idps[i]->domain_list_count; j++) {
                        if (strcasecmp(q_domain_ptr, idps[i]->domain_list[j]) == 0) {
                            idp_domain = idps[i]->domain_list[j];
                            break;
                        }
                    }
                }
                if (arb_domain_feature_enabled && !idp_domain) {
                    /* Does one of the IdPs have `arbitrary_domains_accepted==1`? */
                    for (int p = 0; (idp_domain == NULL) && (p < count); p++) {
                        if ((idps[p]->is_user_idp) && (idps[p]->arbitrary_domains_accepted)) {
                            if (idps[p]->domain_list) {
                                idp_domain = idps[p]->domain_list[0];
                            }
                            ret = snprintf(idp_gid, sizeof(idp_gid), "%"PRId64, idps[p]->gid);
                            if (ret < 0 || ret >= sizeof(idp_gid)) {
                                EXPORTER_LOG(AL_ERROR, "Could not write IDP ID %"PRId64, idps[p]->gid);
                                return exporter_request_respond(request, HTTP_STATUS_INTERNAL_SERVER_ERROR, EXPORTER_ERROR_CODE_IDP_WRITE_FAILED);
                            }
                        }
                    }
                }
                if (!idp_domain && ret < 0) {
                    /* No idp domain specified... ask for one. */
                    res = string_replace(idp_select_js, buf, sizeof(buf), "error", "\"Invalid Domain Specified\"", NULL);
                    if (res) {
                        EXPORTER_LOG(AL_CRITICAL, "%s: Could not string replace", request->name);
                        return exporter_request_respond(request, HTTP_STATUS_FORBIDDEN, EXPORTER_ERROR_CODE_IDP_REPLACE_FAILED);
                    }

                    if (!request->header_origin) {
                        return exporter_request_respond_data(request, buf, strlen(buf));
                    } else {
                        return exporter_request_respond_acao_status_and_data(request, HTTP_STATUS_OK, EXPORTER_ERROR_CODE_NO_ERROR, buf, strlen(buf));
                    }
                }
            }
        }
    }

    if (!idp_domain && ret < 0) {
        if (idps[idp_id]->domain_list_count == 0) {
            /* Need to fetch customer to find a customer domain */
            res = zpath_customer_get(customer_gid,
                                     &customer,
                                     exporter_request_wally_callback,
                                     request,
                                     0);
            if (res) {
                if (res == ZPATH_RESULT_ASYNCHRONOUS) {
                    EXPORTER_DEBUG_AUTH("%s: Fetching customer asynchronously for customer gid = %ld - async_count (%d->%d)",
                                        request->name, (long) customer_gid, request->async_count, request->async_count + 1);
                    __sync_fetch_and_add_4(&(request->async_count), 1);
                    return ZPATH_RESULT_NO_ERROR;
                } else {
                    EXPORTER_LOG(AL_ERROR, "%s: Fetching Customer failed for customer gid = %ld: %s", request->name, (long) customer_gid, zpath_result_string(res));
                    return exporter_request_respond(request, HTTP_STATUS_FORBIDDEN, EXPORTER_ERROR_CODE_CUSTOMER_NOT_FOUND);
                }
            }
            idp_domain = customer->domain_name;
            EXPORTER_DEBUG_AUTH("%s: Fetched IDP domain = %s from customer table for gid = %ld", request->name, idp_domain, (long) customer_gid);
        } else {
            idp_domain = idps[idp_id]->domain_list[0];
            EXPORTER_DEBUG_AUTH("%s: Fetched IDP domain = %s from idp table for gid = %ld", request->name, idp_domain, (long) customer_gid);
        }
    }

    if (!idp_domain && ret < 0) {
        EXPORTER_LOG(AL_ERROR, "%s: No valid customer domain for customer gid = %ld", request->name, (long) customer_gid);
        return exporter_request_respond(request, HTTP_STATUS_FORBIDDEN, EXPORTER_ERROR_CODE_INVALID_CUSTOMER_DOMAIN);
    }

    /* Note: Re-using the orig_url buffer from above. Don't get confused. */
    if (request->url_parser.field_set & (1 << UF_QUERY)) {
        snprintf(orig_url, sizeof(orig_url), "https://%s%s?%.*s",
                 request->conn->sni,
                 //request->conn->local_port_he,
                 EXPORTER_PATH_AUTH_DOMAIN_FROM_SP,
                 request->url_parser.field_data[UF_QUERY].len,
                 &(request->url[request->url_parser.field_data[UF_QUERY].off]));
    } else {
        EXPORTER_LOG(AL_ERROR, "%s: No query string", request->name);
        return exporter_request_respond(request, HTTP_STATUS_INTERNAL_SERVER_ERROR, EXPORTER_ERROR_CODE_NO_QUERY_STRING);
    }

    /* Fetching alt_authsp for the customer */
    char *full_alt_authsp_domain = NULL;
    res = zpn_customer_config_get_alt_authsp_domain(customer_gid,
                                            &full_alt_authsp_domain,
                                            request->name,
                                            exporter_request_wally_callback,
                                            request,
                                            0);
    if (res) {
        if (res == ZPATH_RESULT_ASYNCHRONOUS) {
            __sync_fetch_and_add_4(&(request->async_count), 1);
            /* Do nothing. */
            EXPORTER_DEBUG_AUTH("%s: Fetching alt_authsp asynchronously for customer gid = %"PRId64" async_count (%d->%d)",
                    request->name, customer_gid, request->async_count -1, request->async_count);
            return ZPATH_RESULT_NO_ERROR;
        } else {
            EXPORTER_LOG(AL_ERROR, "%s: Fetching alt_authsp failed for customer gid = %"PRId64": %s",
                                request->name, customer_gid, zpath_result_string(res));
            return exporter_request_respond(request, HTTP_STATUS_FORBIDDEN, EXPORTER_ERROR_CODE_CUSTOMER_ALT_AUTHSP_FETCH_FAILED);
        }
    }

    /* prune the authsp domain to remove leading 'https://' */
    char *alt_authsp_domain = NULL;
    if (full_alt_authsp_domain) {
        alt_authsp_domain = exporter_create_pruned_alt_authsp_domain(full_alt_authsp_domain);
        /* free the old domain */
        ZPN_FREE(full_alt_authsp_domain);
    }

    encrypt = is_url_cookie_encryption_enabled(customer_gid);
    if (arb_domain_feature_enabled && ret > 0) {
        EXPORTER_DEBUG_AUTH("%s: Redirecting to IDP, idp id = %s, orig_url = %s (will be urlencoded again)", request->name, idp_gid, orig_url);
        if (is_levelid) {
            EXPORTER_GLOBAL_USAGE_STATS_FIELD_INC(exporter_redirect_to_authsp_levelid);
            res = exporter_request_redirect_encode(request,
                                               set_crypto_cookie_value ? set_cookie : NULL,
                                               set_crypto_cookie_value ? set_cookie_legacy : NULL,
                                               NULL,
                                               NULL,
                                               (alt_authsp_domain != NULL) ? alt_authsp_domain : exporter_saml_auth_domain,
                                               0,
                                               EXPORTER_PATH_SAML_AUTH_PATH,
                                               EXPORTER_QUERY_NAME_TO_SP_SSOTYPE, EXPORTER_QUERY_NAME_TO_SP_SSOTYPE_SELF,
                                               EXPORTER_QUERY_NAME_TO_IDP_ID, idp_gid,
                                               EXPORTER_QUERY_NAME_TO_SP_REDIRECT_URL, orig_url,
                                               EXPORTER_QUERY_NAME_TO_SP_CRYPTO_KEY, session_crypto_value,
                                               EXPORTER_QUERY_NAME_TO_LOGIN_HINT, ((is_exporter_login_hint_feature_enabled(customer_gid) && found_username) ? q_domain : ""),
                                               /* Add version=v2 and send to saml sp if url_cookie_encryption is enabled */
                                               (encrypt) ? "version" :  NULL, (encrypt) ? EXPORTER_QUERY_NAME_TO_SP_VERSION : NULL,
                                               EXPORTER_QUERY_NAME_TO_SP_LEVELID, levelid,
                                               NULL);
       } else {
            res = exporter_request_redirect_encode(request,
                                               set_crypto_cookie_value ? set_cookie : NULL,
                                               set_crypto_cookie_value ? set_cookie_legacy : NULL,
                                               NULL,
                                               NULL,
                                               (alt_authsp_domain != NULL) ? alt_authsp_domain : exporter_saml_auth_domain,
                                               0,
                                               EXPORTER_PATH_SAML_AUTH_PATH,
                                               EXPORTER_QUERY_NAME_TO_SP_SSOTYPE, EXPORTER_QUERY_NAME_TO_SP_SSOTYPE_SELF,
                                               EXPORTER_QUERY_NAME_TO_IDP_ID, idp_gid,
                                               EXPORTER_QUERY_NAME_TO_SP_REDIRECT_URL, orig_url,
                                               EXPORTER_QUERY_NAME_TO_SP_CRYPTO_KEY, session_crypto_value,
                                               EXPORTER_QUERY_NAME_TO_LOGIN_HINT, ((is_exporter_login_hint_feature_enabled(customer_gid) && found_username) ? q_domain : ""),
                                               /* Add version=v2 and send to saml sp if url_cookie_encryption is enabled */
                                               (encrypt) ? "version" :  NULL, (encrypt) ? EXPORTER_QUERY_NAME_TO_SP_VERSION : NULL,
                                               NULL);
       }
    } else {
        EXPORTER_DEBUG_AUTH("%s: Redirecting to IDP, idp_domain = %s, orig_url = %s (will be urlencoded again)", request->name, idp_domain, orig_url);
        if (is_levelid) {
            EXPORTER_GLOBAL_USAGE_STATS_FIELD_INC(exporter_redirect_to_authsp_levelid);
            res = exporter_request_redirect_encode(request,
                                               set_crypto_cookie_value ? set_cookie : NULL,
                                               set_crypto_cookie_value ? set_cookie_legacy : NULL,
                                               NULL,
                                               NULL,
                                               (alt_authsp_domain != NULL) ? alt_authsp_domain : exporter_saml_auth_domain,
                                               0,
                                               EXPORTER_PATH_SAML_AUTH_PATH,
                                               EXPORTER_QUERY_NAME_TO_SP_SSOTYPE, EXPORTER_QUERY_NAME_TO_SP_SSOTYPE_SELF,
                                               EXPORTER_QUERY_NAME_TO_SP_CUSTOMER_DOMAIN, idp_domain,
                                               EXPORTER_QUERY_NAME_TO_SP_REDIRECT_URL, orig_url,
                                               EXPORTER_QUERY_NAME_TO_SP_CRYPTO_KEY, session_crypto_value,
                                               EXPORTER_QUERY_NAME_TO_LOGIN_HINT, ((is_exporter_login_hint_feature_enabled(customer_gid) && found_username) ? q_domain : ""),
                                               /* Add version=v2 and send to saml sp if url_cookie_encryption is enabled */
                                               (encrypt) ? "version" :  NULL, (encrypt) ? EXPORTER_QUERY_NAME_TO_SP_VERSION : NULL,
                                               EXPORTER_QUERY_NAME_TO_SP_LEVELID, levelid,
                                               NULL);
         } else {
            res = exporter_request_redirect_encode(request,
                                               set_crypto_cookie_value ? set_cookie : NULL,
                                               set_crypto_cookie_value ? set_cookie_legacy : NULL,
                                               NULL,
                                               NULL,
                                               (alt_authsp_domain != NULL) ? alt_authsp_domain : exporter_saml_auth_domain,
                                               0,
                                               EXPORTER_PATH_SAML_AUTH_PATH,
                                               EXPORTER_QUERY_NAME_TO_SP_SSOTYPE, EXPORTER_QUERY_NAME_TO_SP_SSOTYPE_SELF,
                                               EXPORTER_QUERY_NAME_TO_SP_CUSTOMER_DOMAIN, idp_domain,
                                               EXPORTER_QUERY_NAME_TO_SP_REDIRECT_URL, orig_url,
                                               EXPORTER_QUERY_NAME_TO_SP_CRYPTO_KEY, session_crypto_value,
                                               EXPORTER_QUERY_NAME_TO_LOGIN_HINT, ((is_exporter_login_hint_feature_enabled(customer_gid) && found_username) ? q_domain : ""),
                                               /* Add version=v2 and send to saml sp if url_cookie_encryption is enabled */
                                               (encrypt) ? "version" :  NULL, (encrypt) ? EXPORTER_QUERY_NAME_TO_SP_VERSION : NULL,
                                               NULL);
         }
    }

    /* free alt_authsp_domain after use */
    if (alt_authsp_domain) {
        ZPN_FREE(alt_authsp_domain);
    }
    return res; /* returns the result of redirect IDP above */
}

static int check_expiry_of_urlcookie(struct exporter_request *request, const unsigned char *decrypted)
{
    /* SAML SP adds epoch in millisec */
    int64_t now_ms = epoch_s() * 1000;
    char *timestamp_from_url = NULL;
    /* format keystring=xxx&expires=xxx */
    if ((timestamp_from_url = strstr((const char *)decrypted, "expires="))) {
        timestamp_from_url = timestamp_from_url + strlen("expires=");
        int64_t timestamp = strtoll(timestamp_from_url, NULL, 10);
        if (now_ms > timestamp) {
            EXPORTER_LOG(AL_ERROR, "%s: URL cookie expired(%s)", request->name, decrypted);
            return ZPATH_RESULT_ERR;
        }
    } else {
        EXPORTER_LOG(AL_ERROR, "%s: Missing timestamp(expires=) in url cookie(%s)", request->name, decrypted);
        return ZPATH_RESULT_ERR;
    }
    return ZPATH_RESULT_NO_ERROR;
}

static int find_cookie_from_decrypted(struct exporter_request *request, const unsigned char *decrypted,
				      const char *keystring, char *urlcookie, size_t urlcookielen)
{
    char *oskey_start = strstr((const char *)decrypted, keystring);
    char *oskey_end = NULL;
    if (oskey_start) {
        oskey_start = oskey_start + strlen(keystring);
        oskey_end = strstr((const char *)decrypted, "&");
        size_t len = (oskey_end) ? (oskey_end - oskey_start) : strlen(oskey_start);
        if (len >= urlcookielen) {
            EXPORTER_LOG(AL_ERROR, "%s: oskey too long (len = %lu, urlcookielen = %lu)", request->name, len, urlcookielen);
            return ZPATH_RESULT_ERR;
        }
        memcpy(urlcookie, oskey_start, len);
        urlcookie[len] = '\0';
        EXPORTER_LOG(AL_DEBUG, "%s: Extracted urlcookie = %s", request->name, urlcookie);
    } else {
        EXPORTER_LOG(AL_ERROR, "%s: Failed parsing oskey format. Expected(oskey=xxx&expires=xxx) Found(%s)", request->name, decrypted);
        return ZPATH_RESULT_ERR;
    }
    return ZPATH_RESULT_NO_ERROR;
}

static int exporter_validate_timestamp_of_urlcookie(struct exporter_request *request, struct zcrypt_key *key, char *urlcookie, size_t urlcookielen, const char *key_string_match)
{
    size_t cookie_len = 0;
    size_t cookie_binary_len = 0;
    size_t cookie_decrypted_len = 0;
    unsigned char *encrypted = NULL;
    unsigned char *decrypted = NULL;
    int res = ZPATH_RESULT_NO_ERROR;
    int decoded_len = 0;
    /* 1. Key used to decrypt is already passed as input arg */

    /* 2. Decode base64 urlcookie */
    exporter_url_decode(urlcookie);
    cookie_len = strlen(urlcookie);
    if (!cookie_len) {
        EXPORTER_LOG(AL_ERROR, "%s: Failed to get urlcookie length", request->name);
        return ZPATH_RESULT_ERR;
    }
    cookie_binary_len = base64_decoded_size(urlcookie, cookie_len);

    encrypted = EXPORTER_MALLOC(cookie_binary_len + 1);
    decrypted = EXPORTER_MALLOC(cookie_binary_len + 1);
    cookie_decrypted_len = cookie_binary_len;
    decoded_len = base64_decode_binary(encrypted, urlcookie, cookie_len);
    if (decoded_len <= 0 ) {
        EXPORTER_FREE(encrypted);
        EXPORTER_FREE(decrypted);
        return ZPATH_RESULT_BAD_ARGUMENT;
    }
    /* 3. Decrypt 'encrypted' urlcookie using key */
    res = zcrypt_decrypt(key, encrypted, cookie_binary_len, decrypted, &cookie_decrypted_len);
    if(res) {
        EXPORTER_LOG(AL_ERROR, "%s: Failed to decrypt urlcookie", request->name);
        EXPORTER_FREE(encrypted);
        EXPORTER_FREE(decrypted);
        return ZPATH_RESULT_BAD_ARGUMENT;
    }
    EXPORTER_FREE(encrypted);

    decrypted[cookie_decrypted_len] = '\0';
    EXPORTER_LOG(AL_DEBUG, "%s: Decrypted url cookie = %s (len=%lu)", request->name, decrypted, cookie_decrypted_len);

    /* 4a. Extract timestamp and check validity */
    res = check_expiry_of_urlcookie(request, decrypted);
    if (res) {
        EXPORTER_FREE(decrypted);
        return res;
    }

    /* 4b. Extract the oskey from decrypted */
    res = find_cookie_from_decrypted(request, decrypted, key_string_match, urlcookie, urlcookielen);

    /* 5. Done */
    EXPORTER_FREE(decrypted);
    return res;
}


/*
 * 1. Verify 'validity' from authsp- correct cookies, etc.
 *
 * 2. If invalid, return forbidden
 *
 * 3. If we have a session:
 *
 * 3A. If the session has an old assertion for this customer:
 *
 * 3A1. Delete old assertion
 *
 * 3B. Create new session with old state + new state
 *
 * 3C. Delete old session
 *
 * 4. If we did not have a session at #3
 *
 * 4A. Create a new session
 *
 * 5. Write new assertion
 *
 * 6. Write new session, and wait for it to complete.
 *
 * 7. On session write complete, redirect back to original domain while updating session.
 *
 * Basically, the only difference in the (successful) paths is whether
 * or not we are creating a session from scratch.
 *
 */

/*
 * Usage: Extract message between start and end, then append to buf
 * Returns:
 *   - NULL if start/end tokens are not found or msg cannot be extracted
 *   - Pointer to start of end_token from where next parsing can be started if needed
 */

static const char* extract_msg_between_tokens(char *buf, int size, const char* log, const char* start_token, const char* end_token)
{
    if (!log || !start_token || !end_token) return NULL;

    if (*start_token == 0 || *end_token == 0) {
        return NULL;
    }

    const char *s_msg = strstr(log, start_token);
    if (!s_msg) {
        sxprintf(buf+strlen(buf), buf+size, " ");
        EXPORTER_DEBUG_CSP("Incorrect format for log, start {%s} not found", start_token);
        return NULL;
    }
    s_msg += strlen(start_token);

    const char *e_msg = strstr(s_msg, end_token);
    if (!e_msg) {
        sxprintf(buf+strlen(buf), buf+size, " ");
        EXPORTER_DEBUG_CSP("Incorrect format for log, end {%s} not found", end_token);
        return NULL;
    }
    e_msg -= 1;

    if (e_msg < s_msg) {
        /*
         * end keyword before start keyword, return empty string
         * In log if we have this trace - [rev ""]
         * start_token will be [rev " and end_token will be just after start "]
         * So we have zero bytes here but valid scenario when
         * s_msg == e_msg+1
         */

        sxprintf(buf+strlen(buf), buf+size, " ");

        if (s_msg == e_msg+1) {
            return (const char *)(e_msg+1);
        }
        EXPORTER_DEBUG_CSP("Incorrect format for log, keywords out of order");
        return NULL;
    }

#define MAX_DEBUG_MESSAGE_SIZE 4096
    char debug_message[MAX_DEBUG_MESSAGE_SIZE] = {0};

    /*
     * +1 for counting correct chars
     * +1 for including null char
     * If e_msg and s_msg is same then we have 1 byte to print and 1 for EOF
     */
    size_t n_chars = e_msg-s_msg+2 < MAX_DEBUG_MESSAGE_SIZE ? e_msg-s_msg+2 : MAX_DEBUG_MESSAGE_SIZE;
    snprintf(debug_message, n_chars, "%s", s_msg);

    /* Always append at the end of buffer */
    sxprintf(buf+strlen(buf), buf+size, "%s", debug_message);

    /* Return start of end token, this is where we start parsing for next token */
    return (const char *)(e_msg+strlen(end_token)+1);
}

int exporter_request_decrypt_assertion(struct exporter_request *request,
        const char *in_assertion_key,
        const char *in_assertion,
        const char *in_cookie_crypto_value,
        uint8_t **out_unbase64,
        int *out_b64_len,
        uint8_t **out_decrypted) {

    int res = ZPATH_RESULT_NO_ERROR;

    uint8_t *unbase64 = NULL;
    int b64_len = 0;
    uint8_t *decrypted = NULL;

    *out_unbase64 = unbase64;
    *out_b64_len = b64_len;
    *out_decrypted = decrypted;

    /* Decrypt assertion if necessary */
    char *typ = strchr(in_assertion_key, ',');
    if (!typ) {
        EXPORTER_LOG(AL_ERROR, "%s: Could not get valid data from assertion key, doing reauth...", request->name);
        /* Fall through to reauth... */
    } else {
        /* base64 encrypted & decrypted respectively */
        uint8_t *encrypted = NULL;
        if (strncmp(typ, ",spe,", 5) == 0) {
            /* Need to decrypt. */
            struct zcrypt_key key = {{0}};
            size_t str_len = strlen(in_cookie_crypto_value);
            size_t bin_len;
            size_t decrypted_len;
            char nameid[1000];
            EXPORTER_LOG(AL_CRITICAL, "Implement Me: Only decrypt once, please!!!!");
            if (base64_decoded_size(in_cookie_crypto_value, str_len) <= sizeof(struct zcrypt_key)) {
                base64_decode_binary((unsigned char *)&key, in_cookie_crypto_value, str_len);
            } else {
                /*
                 * We are not going to do reauth on this, normal flow without fingerprint
                 * will continue, and this error will come on app access and then reauth will be done
                 */
                EXPORTER_LOG(AL_ERROR, "%s: Crypto key too large, doing reauth...", request->name);
                //goto auth_failed;
                return ZPATH_RESULT_ERR;
            }
            str_len = strlen(in_assertion);
            bin_len = base64_decoded_size(in_assertion, str_len);
            /* Allocate space for
             * encrypted and decrypted
             * assertion. Note: Decrypted
             * assertion will always be
             * smaller than encrypted
             * assertion due to IV */
            encrypted = EXPORTER_MALLOC(bin_len + 1);
            decrypted = EXPORTER_MALLOC(bin_len + 1);
            unbase64 = EXPORTER_MALLOC(bin_len + 1);
            decrypted_len = bin_len;
            base64_decode_binary(encrypted, in_assertion, str_len);
            res = zcrypt_decrypt(&key, encrypted, bin_len, decrypted, &decrypted_len);
            if (res) {
                EXPORTER_LOG(AL_ERROR, "%s: Failed to decrypt assertion, doing reauth...", request->name);
                EXPORTER_FREE(encrypted);
                EXPORTER_FREE(decrypted);
                EXPORTER_FREE(unbase64);
                //require_reauth = 1;
                //goto auth_failed;
                /*
                 * We are not going to do reauth on this, normal flow without fingerprint
                 * will continue, and this error will come on app access and then reauth will be done
                 */
                return ZPATH_RESULT_ERR;
            }
            EXPORTER_FREE(encrypted);
            /* Null terminate... */
            if (decrypted) {
                decrypted[decrypted_len] = 0;
            }
            EXPORTER_DEBUG_AUTH("%s: Decrypted assertion to %.*s", request->name, EXPORTER_DEBUG_BYTES, decrypted);

            b64_len = base64_decode_binary(unbase64, (const char *)decrypted, decrypted_len);
            if (b64_len > 0) {
                if (unbase64) {
                    unbase64[b64_len] = 0;
                }
                res = zsaml_get_nameid((const char *)unbase64,
                                       b64_len,
                                       nameid,
                                       sizeof(nameid));
                if (res == ZSAML_RESULT_NO_ERROR) {
                    zpath_downcase(nameid); /* required for SCIM user lookup */
                    if (request->log.nameid) {
                        EXPORTER_FREE(request->log.nameid);
                        request->log.nameid = NULL;
                    }
                    request->log.nameid = EXPORTER_STRDUP(nameid, strlen(nameid));
                    request->is_authenticated = 1;
                } else {
                    EXPORTER_LOG(AL_ERROR, "%s: Could not extract nameid from assertion", request->name);
                    if (unbase64) EXPORTER_FREE(unbase64);
                    if (decrypted) EXPORTER_FREE(decrypted);
                    return res;
                }
            } else {
                EXPORTER_LOG(AL_ERROR, "%s: Could not extract assertion from b64 assertion", request->name);
                if (unbase64) EXPORTER_FREE(unbase64);
                if (decrypted) EXPORTER_FREE(decrypted);
                return ZPATH_RESULT_ERR;
            }

            encrypted = NULL;
        } else {
            /* No need to decrypt... assertion is already correct */
        }

        *out_unbase64 = unbase64;
        *out_b64_len = b64_len;
        *out_decrypted = decrypted;
    }

    return ZPATH_RESULT_NO_ERROR;
}

static int exporter_csp_create_nonce(struct exporter_request *request, const char *refer_key, char *nonce, size_t nonce_len) {
    /*
     * Generate nonce and store for one time use for fromsp2 exception handling
     * Size of encoded key returned from object store is 44 bytes without any prefixes
     * Object store - Y9GLK+27MG4eLGUWZSXp+uHt6s6WEf8OvNID7NXAkig=
     * With prefix -  p1|csp|non|Y9GLK+27MG4eLGUWZSXp+uHt6s6WEf8OvNID7NXAkig=
     */
    int res;
    memset(nonce, 0, nonce_len);

    res = object_store_gen_key(OBJECT_STORE_TYPE_FINGERPRINT_NONCE, refer_key, nonce, nonce_len, ostore_role_object_store);
    if (res) {
        EXPORTER_LOG(AL_ERROR, "%s: Could not generate nonce", request->name);
        return res;
    }

    /*
     * At present we do not want to store any value in nonce
     * In phase2 we need to store policy id and we can convert
     * nonce to session id and store all data there in ARGO format
     */
    res = object_store_set(request->name,
            nonce,
            "N.A",
            NULL,
            NULL,
            0,
            ostore_role_object_store);

    if (res && (res != ZPATH_RESULT_ASYNCHRONOUS)) {
        EXPORTER_LOG(AL_ERROR, "%s: Could not store nonce cookie %.*s: %s", request->name,
                EXPORTER_CSP_DEBUG_BYTES, nonce, zpath_result_string(res));
        return res;
    }

    return ZPATH_RESULT_NO_ERROR;
}

static int
exporter_request_log_chrome_posture_payload_to_kafka(struct exporter_request *request, char *domain_gdcookie_value)
{
    const char *gposture_value = NULL;
    char *decoded_gposture = NULL;
    struct argo_object *posture_obj = NULL;
    struct zpn_managed_chrome_payload *payload = NULL;
    uint32_t is_mgd_chrome = 0;
    int res;

    request->orig_customer_gid = request->caa_data->customer_gid;
    res = is_exporter_managed_chrome_2_enabled(request, &is_mgd_chrome);
    if (res != ZPATH_RESULT_NO_ERROR) {
        if (res == ZPATH_RESULT_ASYNCHRONOUS) {
            return res;
        } else if (res != ZPATH_RESULT_NOT_FOUND) {
            EXPORTER_LOG(AL_ERROR, "%s: Checking managed chrome failed %s", request->name, zpath_result_string(res));
            return res;
        }
    } else if (!is_mgd_chrome) {
        /* Don't log if the feature is disabled. */
        return ZPATH_RESULT_NO_ERROR;
    }

    if (!domain_gdcookie_value) {
        EXPORTER_LOG(AL_ERROR, "%s: gdcookie is NULL, can't log to kafka", request->name);
        return ZPATH_RESULT_ERR;
    }

    /* Get chrome posture value and log to kafka. */
    res = object_store_get(request->name,
                           domain_gdcookie_value,
                           &gposture_value,
                           exporter_request_async_callback,
                           request,
                           0,
                           ostore_role_object_store);
    if (res) {
        if (res == ZPATH_RESULT_ASYNCHRONOUS) {
            __sync_fetch_and_add_4(&(request->async_count), 1);
            EXPORTER_LOG(AL_DEBUG, "%s: Fetching gposture value asynchronously - async_count (%d->%d)",
                         request->name, request->async_count - 1, request->async_count);
            return res;
        } else if (res == ZPATH_RESULT_NOT_FOUND) {
            gposture_value = NULL;
            EXPORTER_LOG(AL_ERROR, "%s: Failed to retrieve gposture value from object store", request->name);
            EXPORTER_GLOBAL_USAGE_STATS_FIELD_INC(exporter_posture_not_found_in_object_store);
            return res;
        } else {
            gposture_value = NULL;
            EXPORTER_LOG(AL_ERROR, "%s: Bad gposture value get: %s", request->name, zpath_result_string(res));
            EXPORTER_GLOBAL_USAGE_STATS_FIELD_INC(exporter_posture_not_found_in_object_store);
            return res;
        }
    }

    if (!gposture_value) {
        EXPORTER_LOG(AL_ERROR, "%s, CHROME_POSTURE: gposture value with payload key not found. Unable to log to kafka.", request->name);
        EXPORTER_GLOBAL_USAGE_STATS_FIELD_INC(exporter_posture_not_found_in_object_store);
        return ZPATH_RESULT_ERR;
    }

    decoded_gposture = base64_decode(gposture_value);
    if (!decoded_gposture) {
        EXPORTER_LOG(AL_ERROR, "%s: decoding of gposture failed", request->name);
        return ZPATH_RESULT_ERR;
    }

    posture_obj = argo_deserialize_json(decoded_gposture, strlen(decoded_gposture));
    if (!posture_obj) {
        EXPORTER_LOG(AL_ERROR, "[GOOGLE_POSTURE_PARSER]: Failed to deserialize json <%s>, returning error", decoded_gposture);
        SAFE_FREE_DECODE_POSTURE(decoded_gposture);
        return ZPATH_RESULT_ERR;
    }

    payload = posture_obj->base_structure_void;
    if (!payload->success || (strcasecmp(payload->success, "true") != 0)) {
        /* success is not set to true - don't log to kafka, just ignore. */
        argo_object_release(posture_obj);
        SAFE_FREE_DECODE_POSTURE(decoded_gposture);
        return ZPATH_RESULT_NO_ERROR;
    }
    memset(payload->gd_cookie_domain_id, 0, sizeof(payload->gd_cookie_domain_id));
    get_context_gd_cookie_domain_id(domain_gdcookie_value, payload->gd_cookie_domain_id);
    payload->g_cst = request->caa_data->customer_gid;
    /* UI expects this time to be in micro seconds, so multiply by 1000 as it's in milli seconds right now. */
    payload->creation_time *= 1000;

    res = zpath_customer_log_struct(request->caa_data->customer_gid,
                                    zpath_customer_log_type_zpn_managed_browser_payload,
                                    "mb_transaction",
                                    NULL,
                                    NULL,
                                    NULL,
                                    NULL,
                                    zpn_managed_chrome_payload_description,
                                    payload);
    if (res) {
        EXPORTER_LOG(AL_ERROR, "%s: Could not log chrome posture payload to kafka: %s",
                     request->name, zpath_result_string(res));
    }

    argo_object_release(posture_obj);
    SAFE_FREE_DECODE_POSTURE(decoded_gposture);

    return res;
}

static int request_path_install_gcookie_from_caa(struct exporter_request *request)
{
    char object_store_gkey[EXPORTER_URL_MAX_ENCODE_SIZE];
    int res;
    char set_cookie[EXPORTER_URL_MAX_ENCODE_SIZE];
    char etag_caa[EXPORTER_COOKIE_MAX_SIZE];
    const char *etag_caa_value = NULL;

    res = query_string_find(request->url, &(request->url_parser), EXPORTER_QUERY_NAME_FROM_CAA_GKEY, object_store_gkey, sizeof(object_store_gkey));
    if (res) {
        EXPORTER_LOG(AL_ERROR, "%s: Didn't receive gkey from CAA", request->name);
        EXPORTER_GLOBAL_USAGE_STATS_FIELD_INC(exporter_posture_failed_gkey_from_caa);
        return exporter_request_respond(request, HTTP_STATUS_BAD_REQUEST, EXPORTER_ERROR_CODE_GKEY_NOT_FOUND);
    }

    res = query_string_find(request->url, &(request->url_parser), "etag_caa", etag_caa, sizeof(etag_caa));
    if (res) {
        EXPORTER_LOG(AL_ERROR, "no etag_caa found in url");
        return exporter_request_respond(request, HTTP_STATUS_FORBIDDEN, EXPORTER_ERROR_CODE_CAA_DATA_NOT_FOUND);
    }

    exporter_url_decode(etag_caa);
    EXPORTER_LOG(AL_DEBUG, "etag_caa: %s\n", etag_caa);

    res = object_store_get(request->name,
                           etag_caa,
                           &etag_caa_value,
                           exporter_request_async_callback,
                           request,
                           0,
                           ostore_role_object_store);
    if (res) {
        if (res == ZPATH_RESULT_ASYNCHRONOUS) {
            __sync_fetch_and_add_4(&(request->async_count), 1);
            EXPORTER_LOG(AL_DEBUG, "%s: Fetching etag_caa asynchronously - async_count (%d->%d)",
                         request->name, request->async_count - 1, request->async_count);
            return ZPATH_RESULT_NO_ERROR;
        } else if (res == ZPATH_RESULT_NOT_FOUND) {
            /* Entry not found in object store. */
            EXPORTER_LOG(AL_ERROR, "%s: Failed to retrieve etag_caa value from object store", request->name);
        } else {
                /* Error occured while retrieving info from object store */
                EXPORTER_LOG(AL_ERROR, "%s: Error occured while retrieving etag_caa from object store", request->name);
        }
        return exporter_request_respond_with_text(request, HTTP_STATUS_INTERNAL_SERVER_ERROR,
                                                  EXPORTER_ERROR_CODE_CAA_DATA_NOT_FOUND, "CAA data not found");
    }

    res = exporter_request_assign_caa(request, etag_caa_value);
    if (res)
        return res;

    EXPORTER_LOG(AL_DEBUG, "customer_gid: [%"PRId64"], origurl: %s", request->caa_data->customer_gid, request->caa_data->origurl);

    if ((request->req_method == HTTP_OPTIONS) && (request->sec_fetch_mode == HTTP_SEC_FETCH_MODE_CORS) &&
        is_cors_enabled(request->caa_data->customer_gid)) {
        EXPORTER_LOG(AL_ERROR, "%s: Received %s install gdcookie from caa", request->name, http_method_names[request->req_method]);
        if (exporter_validate_no_auth_options_request(request) == ZPATH_RESULT_NO_ERROR) {
            res = exporter_request_log_chrome_posture_payload_to_kafka(request, object_store_gkey);
            if (res) {
                if (res == ZPATH_RESULT_ASYNCHRONOUS) {
                    return ZPATH_RESULT_NO_ERROR;
                }
                EXPORTER_LOG(AL_WARNING, "%s: Could not log chrome posture payload to kafka: %s",
                             request->name, zpath_result_string(res));
            }
            return exporter_request_respond_no_content(request, HTTP_STATUS_NO_CONTENT, EXPORTER_ERROR_CODE_NO_ERROR);
        }
        res = exporter_request_log_chrome_posture_payload_to_kafka(request, object_store_gkey);
        if (res) {
            if (res == ZPATH_RESULT_ASYNCHRONOUS) {
                return ZPATH_RESULT_NO_ERROR;
            }
            EXPORTER_LOG(AL_WARNING, "%s: Could not log chrome posture payload to kafka: %s",
                         request->name, zpath_result_string(res));
        }
        return exporter_request_respond(request, HTTP_STATUS_FORBIDDEN, EXPORTER_ERROR_CODE_INVALID_OPTIONS_REQUEST);
    }

    char *samesite = is_samesite_cookie_none(request->caa_data->customer_gid) ? "None" : "Lax";

    EXPORTER_LOG(AL_DEBUG, "request->sec_fetch_mode: %d, is_cors_enabled(request->caa_data->customer_gid): %d",
                 request->sec_fetch_mode, is_cors_enabled(request->caa_data->customer_gid));

    if ((request->sec_fetch_mode == HTTP_SEC_FETCH_MODE_CORS) && is_cors_enabled(request->caa_data->customer_gid)) {
        samesite = "None";
    }

    make_cookie(request->conn->sni, EXPORTER_GDCOOKIE_DOMAIN, object_store_gkey, samesite, EXPORTER_COOKIE_DEFAULT_LIFETIME_S, set_cookie, sizeof(set_cookie), NULL, 0);

    res = exporter_request_log_chrome_posture_payload_to_kafka(request, object_store_gkey);
    if (res) {
        if (res == ZPATH_RESULT_ASYNCHRONOUS) {
            return ZPATH_RESULT_NO_ERROR;
        }
        EXPORTER_LOG(AL_ERROR, "%s: Could not log chrome posture payload to kafka: %s",
                     request->name, zpath_result_string(res));
        return res;
    }

    exporter_url_decode(request->caa_data->origurl);

    return exporter_request_redirect_encode_caa(request,
            set_cookie,
            NULL,
            NULL,
            NULL,
            request->caa_data->origurl,
            0,
            NULL,
            NULL);
}

static int request_path_auth_domain_from_sp(struct exporter_request *request)
{
    struct http_parser_url orig_url_parser;
    char session_crypto_value[200];
    char orig_url_query_value[EXPORTER_URL_MAX_ENCODE_SIZE];
    char assertion_key_query_value[EXPORTER_URL_MAX_ENCODE_SIZE];
    char session_key[EXPORTER_URL_MAX_ENCODE_SIZE];
    char session_key_with_domain[EXPORTER_URL_MAX_ENCODE_SIZE];
    char domain_cookie_value[EXPORTER_URL_MAX_ENCODE_SIZE];
    char orig_domain[EXPORTER_URL_MAX_ENCODE_SIZE];
    char err_code[100];
    char err_string[EXPORTER_URL_MAX_ENCODE_SIZE];
    int64_t customer_gid;
    int res;
    struct argo_object *session = NULL;
    char *s;
    char *e;

    /* Verify that the request is to the auth host */
    if (!request->conn->exporter_domain->is_auth_domain) {
        EXPORTER_LOG(AL_ERROR, "%s: Received request for auth path that isn't to auth domain", request->name);
        return exporter_request_respond(request, HTTP_STATUS_BAD_REQUEST, EXPORTER_ERROR_CODE_INVALID_AUTH_DOMAIN);
    }

    if (request->req_method != HTTP_GET) {
        /* It's not get. Return auth required */
        return exporter_request_respond(request, HTTP_STATUS_FORBIDDEN, EXPORTER_ERROR_CODE_NOT_GET_REQUEST);
    }

    res = find_cookie(request, EXPORTER_COOKIE_CRYPTO, 0, session_crypto_value, sizeof(session_crypto_value));
    if (res) {
        EXPORTER_LOG(AL_ERROR, "%s: No crypto key returning from SP", request->name);
        return exporter_request_respond(request, HTTP_STATUS_INTERNAL_SERVER_ERROR, EXPORTER_ERROR_CODE_CRYPTO_COOKIE_NOT_FOUND);
    }

    res = query_string_find(request->url, &(request->url_parser), EXPORTER_QUERY_NAME_FROM_SP_ERR_CODE, err_code, sizeof(err_code));
    if (res == ZPATH_RESULT_NO_ERROR) {
        exporter_url_decode(err_code);
        res = query_string_find(request->url, &(request->url_parser), EXPORTER_QUERY_NAME_FROM_SP_ERR_STRING, err_string, sizeof(err_string));
        if (res == ZPATH_RESULT_NO_ERROR) {
            exporter_url_decode(err_string);
            return exporter_request_respond_with_text(request, HTTP_STATUS_INTERNAL_SERVER_ERROR, EXPORTER_ERROR_CODE_URL_DECODE_FAILED, "Err %s: %s", err_code, err_string);
        } else {
            return exporter_request_respond_with_text(request, HTTP_STATUS_INTERNAL_SERVER_ERROR, EXPORTER_ERROR_CODE_ERROR_STRING_NOT_FOUND, "Err %s", err_code);
        }
    }

    /* Get customer_gid and orig_url from query string */
    customer_gid = url_get_customer_gid(request, request->url, &(request->url_parser), EXPORTER_QUERY_NAME_FROM_SP_ORIGINAL_URL, orig_url_query_value, sizeof(orig_url_query_value), &orig_url_parser);
    if (!customer_gid) {
        /* Not so good... */
        EXPORTER_LOG(AL_ERROR, "%s: Received redirect from SP from which we could not extract customer_gid", request->name);
        return exporter_request_respond(request, HTTP_STATUS_BAD_REQUEST, EXPORTER_ERROR_CODE_CUSTOMER_GID_NOT_FOUND);
    }

    /* Find query param oskey */
    res = query_string_find(request->url, &(request->url_parser), EXPORTER_QUERY_NAME_FROM_SP_OBJECT_KEY, assertion_key_query_value, sizeof(assertion_key_query_value));
    if (res) {
        /* 'oskey' is not sent by samlsp. Search for 'eoskey' (oskey in encrypted form) */
        res = query_string_find(request->url, &(request->url_parser), EXPORTER_QUERY_NAME_FROM_SP_ENCRYPTED_OBJECT_KEY, assertion_key_query_value, sizeof(assertion_key_query_value));
        if (res) {
            /* Not so good... */
            EXPORTER_LOG(AL_ERROR, "%s: Received redirect from SP that doesn't have key: %s", request->name, request->url);
            return exporter_request_respond(request, HTTP_STATUS_BAD_REQUEST, EXPORTER_ERROR_CODE_OSKEY_NOT_FOUND);
        }
        /* Found eoskey (oskey is encrypted along with timestamp). Decrypt using crypto, validate timestamp and update assertion_key_query_value  */
        char key_string_match[200] = {'\0'};
        size_t crypto_value_len = strlen(session_crypto_value);
        struct zcrypt_key key = {{0}};
        snprintf(key_string_match, sizeof(key_string_match), "%s=", EXPORTER_QUERY_NAME_FROM_SP_OBJECT_KEY); /* oskey= */
        /* Get the key used for encryption and decryption of oskey */
        if (crypto_value_len && (base64_decoded_size(session_crypto_value, crypto_value_len) <= sizeof(struct zcrypt_key))) {
            base64_decode_binary((unsigned char *)&key, session_crypto_value, crypto_value_len);
        } else {
            EXPORTER_LOG(AL_ERROR, "%s: Crypto key too large or too small (len= %lu)", request->name, crypto_value_len);
            return ZPATH_RESULT_ERR;
        }
        res = exporter_validate_timestamp_of_urlcookie(request, &key, assertion_key_query_value, sizeof(assertion_key_query_value), key_string_match);
        if (res) {
            return exporter_request_respond(request, HTTP_STATUS_BAD_REQUEST, EXPORTER_ERROR_CODE_INVALID_COOKIE_TIMESTAMP);
        }
    } else {
        exporter_url_decode(assertion_key_query_value);
    }
    /* Get the original domain- we need it for a couple cases below. */
    snprintf(orig_domain, sizeof(orig_domain), "%.*s", orig_url_parser.field_data[UF_HOST].len, &(orig_url_query_value[orig_url_parser.field_data[UF_HOST].off]));

    /* Redirect to getcanvasfingerprint with entire url as query in fromsporigurl */

    /* CSP_POLICY START */

    /* Decrypt */

    EXPORTER_DEBUG_CSP("%s: CSP_FEATURE status %s for customer: [%"PRId64"] domain: [%s] in path: [%s]", request->name,
            request->is_csp_enabled ? "enabled" : "disabled",
            request->orig_customer_gid, request->orig_domain, request->log.url);

    const char *assertion_cookie = assertion_key_query_value;

    if (request->is_csp_enabled) {

        res = exporter_load_policy(request, assertion_cookie, session_crypto_value);
        if (res) {
            if (res == ZPATH_RESULT_ASYNCHRONOUS) {
                return ZPATH_RESULT_NO_ERROR;
            } else if (res == ZPN_RESULT_EXPIRED) {
                /* Reauth code must have set redirection so we are returning */
                return ZPATH_RESULT_NO_ERROR;
            } else {
                /*
                 * We are in fromsp - Even if policy fails we continue
                 * since we have to log from SSO
                 */
            }
        }
        res = exporter_read_csp_config(request);
        if (res == ZPN_RESULT_ASYNCHRONOUS) {
            return ZPATH_RESULT_NO_ERROR;
        }

        char redirect_url[2*EXPORTER_URL_MAX_ENCODE_SIZE];
        char buf[1024];
        const char *s_msg;
        int ret_encryption = ZPATH_RESULT_NO_ERROR;

        memset(buf, 0, sizeof(buf));
        memset(redirect_url, 0, sizeof(redirect_url));
        s_msg = extract_msg_between_tokens(buf, sizeof(buf), request->url, "/", "/fromsp?");

        /*
         * Added handling for nonce so that this exception
         * is only valid for one time use
         * We do not want this API to be exploited by intentially sending same
         * oskey and we proceed to create session
         *
         * In phase2 we will create a nonce with JWT sign it and sent it to client
         * Client will sent the same back and we will verify and only then handle
         * oskey and create a new session
         */

        char nonce_cookie_value[EXPORTER_COOKIE_MAX_SIZE] = {0};
        char nonce_object_key[EXPORTER_COOKIE_MAX_SIZE] = {0};
        char encrypted_nonce_object_key[EXPORTER_COOKIE_MAX_SIZE] = {0};
        char crypto_nonce[EXPORTER_CSP_ENC_KEY_LEN + 1] = {0};
        char crypto_iv[EXPORTER_CSP_ENC_IV_LEN + 1] = {0};

        if (request->js_encryption_enabled) {
            ret_encryption = exporter_csp_get_encryption_context(request, request->cb_session_cookie,
                        nonce_object_key, sizeof(nonce_object_key),
                        crypto_nonce, sizeof(crypto_nonce),
                        crypto_iv, sizeof(crypto_iv));
            if (ret_encryption) {
                EXPORTER_LOG(AL_ERROR, "%s: [CSP_ENCRYPT_NONCE] Could not generate nonce, Key, IV", request->name);
            } else {
                ret_encryption = exporter_csp_encrypt_nonce(request, nonce_object_key, EXPORTER_COOKIE_MAX_SIZE,
                                                 encrypted_nonce_object_key);
                if (!ret_encryption) {
                    EXPORTER_LOG(AL_DEBUG, "%s [CSP_ENCRYPT_NONCE] Here is the encrypted nonce [%s]",
                                             request->name,
                                             encrypted_nonce_object_key);
                }
            }
        }

        res = exporter_csp_create_nonce(request, assertion_key_query_value,
                                        nonce_cookie_value,
                                        sizeof(nonce_cookie_value));

        if (res) {
            EXPORTER_LOG(AL_ERROR, "%s: Could not generate nonce", request->name);
            snprintf(redirect_url, sizeof(redirect_url), "https://%s:%d/%s/fromsp2?%s",
                    EXPORTER_DOMAIN_AUTH, 443, buf, s_msg);
        } else {
            snprintf(redirect_url, sizeof(redirect_url), "https://%s:%d/%s/fromsp2?%s&nonce=%s",
                    EXPORTER_DOMAIN_AUTH, 443, buf, s_msg, nonce_cookie_value);
        }

        EXPORTER_DEBUG_CSP("CSP Changing to fromsp2 url: [%s]",
                redirect_url);

        /* We can send data here also but in redirect message page is not loaded
         * So we send redirect and page is loaded from there
         */
        if (request->js_encryption_enabled && ret_encryption != ZPATH_RESULT_NO_ERROR) {
            // Don't re-direct
            EXPORTER_LOG(AL_ERROR, "%s: [CSP_ENCRYPT_NONCE] Failed to generate Nonce, not redirecting [%s]!!!",
                                   request->name, zpath_result_string(ret_encryption));
        } else {
            if (request->js_encryption_enabled) {
                return exporter_request_redirect_encode(request,
                        NULL,
                        NULL,
                        NULL,
                        NULL,
                        EXPORTER_DOMAIN_AUTH,
                        443,
                        (request->is_collect_location ? EXPORTER_PATH_GET_CANVAS_FINGERPRINT_GEO_ENC : EXPORTER_PATH_GET_CANVAS_FINGERPRINT_ENC),
                        "fromsporigurl", redirect_url,
                        EXPORTER_QUERY_NAME_ORIGINAL_URL, orig_url_query_value,
                        EXPORTER_QUERY_NAME_TO_SP_OS_ENCKEY, encrypted_nonce_object_key,
                        EXPORTER_QUERY_NAME_TO_SP_CRYPTO_ENCKEY, crypto_nonce,
                        EXPORTER_QUERY_NAME_TO_SP_CRYPTO_IV, crypto_iv,
                        NULL);
            } else {
                return exporter_request_redirect_encode(request,
                        NULL,
                        NULL,
                        NULL,
                        NULL,
                        EXPORTER_DOMAIN_AUTH,
                        443,
                        (request->is_collect_location ? EXPORTER_PATH_GET_CANVAS_FINGERPRINT_GEO : EXPORTER_PATH_GET_CANVAS_FINGERPRINT),
                        "fromsporigurl", redirect_url,
                        EXPORTER_QUERY_NAME_ORIGINAL_URL, orig_url_query_value,
                        NULL);
            }
        } //ret_encryption!=ZPATH_RESULT_NO_ERROR
    }//is_csp_enabled
    /* CSP_POLICY END */

    /* 7: If we are here because we are reprocessing a request, we
     * don't want to get/write objects again- we just want to set
     * cookie for our new session and redirect */
    if (request->async_state == async_state_from_sp_wrote_session) {
        char set_cookie[EXPORTER_URL_MAX_ENCODE_SIZE];
        char set_cookie_legacy[EXPORTER_URL_MAX_ENCODE_SIZE];
        make_cookie(EXPORTER_DOMAIN_AUTH, EXPORTER_COOKIE_SESSION, request->cb_session_cookie, "None", EXPORTER_COOKIE_DEFAULT_LIFETIME_S, set_cookie, sizeof(set_cookie), set_cookie_legacy, sizeof(set_cookie_legacy));
        EXPORTER_DEBUG_AUTH("%s: Set cookie to %.*s", request->name, EXPORTER_DEBUG_BYTES, set_cookie);
        EXPORTER_DEBUG_AUTH("%s: Set legacy cookie to %.*s", request->name, EXPORTER_DEBUG_BYTES, set_cookie_legacy);
        EXPORTER_DEBUG_AUTH("%s: Orig URL =  %.*s", request->name, EXPORTER_DEBUG_BYTES, orig_url_query_value);

        /* encrypt domain cookie in the URL */
        char *DOM_COOKIE = EXPORTER_QUERY_NAME_TO_DOMAIN_SET_COOKIE; /* cookie */
        char *url_domain_cookie = request->cb_domain_cookie;
        char encoded_ecookie_domain[EXPORTER_URL_MAX_ENCODE_SIZE] = {'\0'};
        if (is_url_cookie_encryption_enabled(customer_gid)) {
            res = exporter_encrypt_dom_cookie(request, request->cb_domain_cookie, EXPORTER_URL_MAX_ENCODE_SIZE, encoded_ecookie_domain);
            if (res) {
                return exporter_request_respond(request, HTTP_STATUS_BAD_REQUEST, EXPORTER_ERROR_CODE_DOMAIN_COOKIE_ENCRYPTION_FAILED);
            }
            DOM_COOKIE = EXPORTER_QUERY_NAME_TO_DOMAIN_SET_ENCRYPTED_COOKIE; /* ecookie */
            url_domain_cookie = encoded_ecookie_domain;
        }
        return exporter_request_redirect_encode(request,
                                                set_cookie,
                                                set_cookie_legacy,
                                                NULL,
                                                NULL,
                                                orig_domain,
                                                (orig_url_parser.field_set & (1 << UF_PORT)) ? orig_url_parser.port : 443,
                                                EXPORTER_PATH_CUSTOMER_DOMAIN_INSTALL_COOKIE,
                                                EXPORTER_QUERY_NAME_ORIGINAL_URL, orig_url_query_value,
                                                DOM_COOKIE, url_domain_cookie,
                                                EXPORTER_QUERY_NAME_TO_DOMAIN_SET_CRYPTO_COOKIE, session_crypto_value,
                                                NULL);

    }

    /*
     * Get either modified session or new session:
     */
    res = find_cookie(request, EXPORTER_COOKIE_SESSION, 1, session_key, sizeof(session_key));
    if (res == ZPATH_RESULT_NO_ERROR) {
        /* Have a session cookie. Check if session really exists... */
        res = exporter_session_get(request->name,
                                   session_key,
                                   &session,
                                   exporter_request_async_callback,
                                   request,
                                   0);
        if (res) {
            if (res == ZPATH_RESULT_ASYNCHRONOUS) {
                EXPORTER_DEBUG_AUTH("%s: Looking up session %.*s asynchronously - async_count (%d->%d)", request->name,
                                    EXPORTER_DEBUG_BYTES, session_key, request->async_count, request->async_count + 1);
                __sync_fetch_and_add_4(&(request->async_count), 1);
                return ZPATH_RESULT_NO_ERROR;
            } else {
                /* Fall through- we will not have a session and will end up making one.
                 * Note: we do not differentiate NOT_FOUND from other reasons (e.g. object store is down, ref: ET-29450).
                 */
                session = NULL;
            }
        } else {
            const char *assertion_key;
            /* We have a session already. */

            /* If the session has a key for this customer_gid, AND if
             * it is different from the key we were just given, delete
             * the old key. */
            res = exporter_session_get_auth_assertion_key(session, customer_gid, &assertion_key);
            if (res == ZPATH_RESULT_NO_ERROR) {
                if (strcmp(assertion_key, assertion_key_query_value) == 0) {
                    /* This is the same key we got back from somewhere
                     * else. This is really weird- We'll do the same
                     * processing, but I sort of want to log this */
                    EXPORTER_LOG(AL_WARNING, "%s: Got duplicate key from SP: %.*s", request->name, EXPORTER_DEBUG_BYTES, assertion_key);
                }

                if (request->is_csp_exception) {
                    /* This is a valid request, do not delete assertion key and use it to give access to app */
                    EXPORTER_DEBUG_AUTH("%s: /fromsp CSP exception do not delete assertion key %.*s", request->name, EXPORTER_DEBUG_BYTES, assertion_key);
                } else {
                    /*
                     * We delete assertion key and proceed to create session
                     * Then in request_path_default_cb
                     * where we try to access APP for first time assertion lookup fails
                     * and we are redirected to doauth again
                     *
                     * Alternate way is after deleting assertion key we give error
                     * since we suspect this might be coming from attacker
                     * User will open the website again and will be redirected to /doauth
                     * since assertion key is not present
                     *
                     */

                    EXPORTER_DEBUG_AUTH("%s: Deleting old assertion key %.*s", request->name, EXPORTER_DEBUG_BYTES, assertion_key);
                    res = object_store_delete(request->name, assertion_key, NULL, NULL, 0, ostore_role_object_store);
                    if (res && (res != ZPATH_RESULT_ASYNCHRONOUS)) {
                        EXPORTER_LOG(AL_ERROR, "%s: Could not delete assertion key %.*s", request->name, EXPORTER_DEBUG_BYTES, assertion_key);
                    }
                }
            } else if (res == ZPATH_RESULT_NOT_FOUND) {
                /* This check has been added to avoid man in the middle attack on origurl parameter, which
                 * can result in to redirect to some other tenant's domain. This will trigger if there is a mismatch in customer_gid
                 * for domain present in current request and customer_gid for session stored in object store */
                EXPORTER_LOG(AL_ERROR, "%s: assertion key is not found for domain %s", request->name, orig_url_query_value);
                if (!exporter_clear_cookies_enabled_for_customer(request->orig_customer_gid)) {
                    return exporter_request_respond(request, HTTP_STATUS_FORBIDDEN, EXPORTER_ERROR_CODE_ASSERTION_KEY_NOT_FOUND_FOR_CUSTOMER_DOMAIN);
                }
                res = object_store_delete(request->name, session_key, NULL, NULL, 0, ostore_role_object_store);
                if (res && (res != ZPATH_RESULT_ASYNCHRONOUS)) {
                    EXPORTER_LOG(AL_ERROR,"%s: Could not delete session key ", request->name);
                }
                return exporter_request_redirect_encode(request,
                                                    NULL,
                                                    NULL,
                                                    NULL,
                                                    NULL,
                                                    EXPORTER_DOMAIN_AUTH,
                                                    443,
                                                    EXPORTER_PATH_AUTH_DOMAIN_FROM_CUSTOMER_DOMAIN,
                                                    EXPORTER_QUERY_NAME_ORIGINAL_URL, orig_url_query_value,
                                                    EXPORTER_QUERY_NAME_REQUIRE_REAUTH, "true",
                                                    NULL);
            }

            /* Delete the old session fingerprint data */
            res = exporter_delete_session_csp_data(request->name, session_key, NULL, NULL, 0);
            if (res && (res != ZPATH_RESULT_ASYNCHRONOUS)) {
                EXPORTER_LOG(AL_ERROR, "%s: Could not delete session fp data%.*s", request->name, EXPORTER_DEBUG_BYTES, session_key);
            }
            /* Delete the old session, because in any case we will be
             * creating a new one */
            EXPORTER_DEBUG_AUTH("%s: Deleting old session key %.*s for %s", request->name, EXPORTER_DEBUG_BYTES, session_key, orig_domain);
            res = object_store_delete(request->name, session_key, NULL, NULL, 0, ostore_role_object_store);
            if (res && (res != ZPATH_RESULT_ASYNCHRONOUS)) {
                EXPORTER_LOG(AL_ERROR, "%s: Could not delete session key %.*s", request->name, EXPORTER_DEBUG_BYTES, session_key);
            }
            /* Now we have a session to write... */
        }
    }

    if (!session) {
        /* No session cookie- definitely need to create a session */
        res = object_store_gen_key(OBJECT_STORE_TYPE_SESSION, assertion_key_query_value, session_key,
                                   sizeof(session_key), ostore_role_object_store);
        if (res) {
            EXPORTER_LOG(AL_ERROR, "%s: Could not gen key", request->name);
            return exporter_request_respond(request, HTTP_STATUS_BAD_REQUEST, EXPORTER_ERROR_CODE_SESSION_KEY_GENERATION_FAILED);
        }
        session = exporter_session_create(session_key);
        if (!session) {
            EXPORTER_LOG(AL_ERROR, "%s: Could not gen session", request->name);
            return exporter_request_respond(request, HTTP_STATUS_BAD_REQUEST, EXPORTER_ERROR_CODE_SESSION_CREATION_FAILED);
        } else {
            EXPORTER_DEBUG_AUTH("%s: Created session %.*s", request->name, EXPORTER_DEBUG_BYTES, session_key);
        }
    }

    /* We now have a session. Don't forget to free it for all cases! */
    res = object_store_gen_key(OBJECT_STORE_TYPE_SESSION, assertion_key_query_value, session_key, sizeof(session_key),
                               ostore_role_object_store);
    if (res) {
        EXPORTER_LOG(AL_ERROR, "%s: Could not gen key", request->name);
        exporter_session_free(session);
        return exporter_request_respond(request, HTTP_STATUS_BAD_REQUEST, EXPORTER_ERROR_CODE_SESSION_KEY_GENERATION_FAILED);
    }

    /* Add the assertion key to the current session */
    EXPORTER_DEBUG_AUTH("%s: Adding assertion key %.*s to session %.*s", request->name, EXPORTER_DEBUG_BYTES, assertion_key_query_value, EXPORTER_DEBUG_BYTES, session_key);
    res = exporter_session_add_auth_token(&session, session_key, assertion_key_query_value, customer_gid);
    if (res) {
        EXPORTER_LOG(AL_ERROR, "%s: Could not add auth token to session %.*s", request->name, EXPORTER_DEBUG_BYTES, session_key);
        exporter_session_free(session);
        return exporter_request_respond(request, HTTP_STATUS_BAD_REQUEST, EXPORTER_ERROR_CODE_AUTH_TOKEN_ADD_TO_SESSION_FAILED);
    }

    /* We need a couple keys to reference back to this session- and we
     * need to remember them through a callback. */
    res = object_store_gen_key(OBJECT_STORE_TYPE_DOMAIN, /* Hint */ assertion_key_query_value, domain_cookie_value,
                               sizeof(domain_cookie_value), ostore_role_object_store);
    if (res) {
        EXPORTER_LOG(AL_ERROR, "%s: Could not gen key", request->name);
        exporter_session_free(session);
        return exporter_request_respond(request, HTTP_STATUS_BAD_REQUEST, EXPORTER_ERROR_CODE_DOMAIN_COOKIE_GENERATION_FAILED);
    }

    s = request->cb_session_cookie;
    e = s + sizeof(request->cb_session_cookie);
    s += sxprintf(s, e, "%s", session_key);
    /* We write the session key to the DB with the domain attached to
     * it so that we can do domain verification on cookie use. This
     * prevents a cookie from one domain from being used on another
     * domain. */
    s = session_key_with_domain;
    e = s + sizeof(session_key_with_domain);
    s += sxprintf(s, e, "%s,%s", session_key, orig_domain);

    s = request->cb_domain_cookie;
    e = s + sizeof(request->cb_domain_cookie);
    s += sxprintf(s, e, "%s", domain_cookie_value);

    EXPORTER_LOG(AL_DEBUG, "[BFP_LOG] Writing new session_key: %s domain: %s", session_key, orig_domain);
    /* We write the domain cookie asynchronously, and the session synchronously */
    res = object_store_set(request->name,
                           domain_cookie_value,
                           session_key_with_domain,
                           NULL,
                           NULL,
                           0,
                           ostore_role_object_store);
    if (res && (res != ZPATH_RESULT_ASYNCHRONOUS)) {
        EXPORTER_LOG(AL_ERROR, "%s: Could not object_set domain cookie %.*s: %s", request->name, EXPORTER_DEBUG_BYTES, domain_cookie_value, zpath_result_string(res));
        exporter_session_free(session);
        return exporter_request_respond(request, HTTP_STATUS_BAD_REQUEST, EXPORTER_ERROR_CODE_DOMAIN_COOKIE_SET_FAILED);
    }


    /* Flush session to object store- set async state to control
     * where we come back. We absolutely want to complete our
     * write before resuming here */

    /* We are in function request_path_auth_domain_from_sp*/

    res = exporter_session_write(request->name,
                                 session,
                                 exporter_request_async_callback,
                                 request,
                                 0);
    exporter_session_free(session);
    if (res) {
        if (res == ZPATH_RESULT_ASYNCHRONOUS) {
            __sync_fetch_and_add_4(&(request->async_count), 1);
            EXPORTER_DEBUG_AUTH("%s: session key wrote asynchronously, async_count (%d->%d)", request->name,
                    request->async_count - 1, request->async_count);
            request->async_state = async_state_from_sp_wrote_session;
            return ZPATH_RESULT_NO_ERROR;
        } else {
            EXPORTER_LOG(AL_ERROR, "%s: Could not write session key: %s", request->name, zpath_result_string(res));
            return exporter_request_respond(request, HTTP_STATUS_BAD_REQUEST, EXPORTER_ERROR_CODE_SESSION_KEY_WRITE_FAILED);
        }
    } else {
        EXPORTER_LOG(AL_ERROR, "%s: Successfully wrote session key synchronously? Huh?", request->name);
        return exporter_request_respond(request, HTTP_STATUS_BAD_REQUEST, EXPORTER_ERROR_CODE_SESSION_KEY_WRITE_FAILED);
    }

    /* Not reachable */
    return exporter_request_respond(request, HTTP_STATUS_BAD_REQUEST, EXPORTER_ERROR_CODE_NOT_REACHABLE);
}

int get_crypto_key_from_zpath_cloud_secret(struct zcrypt_key *key, int get_previous)
{
    const char *secret = (get_previous) ? zpath_get_decrypted_previous_secret() :
                         zpath_get_decrypted_current_secret();
     if (!secret) {
         EXPORTER_LOG(AL_ERROR, "Unable to create key with a null secret for url cookie encryption");
         return ZPATH_RESULT_ERR;
     }

     if (zcrypt_gen_key(key, (void *)secret, strlen(secret)) != ZCRYPT_RESULT_NO_ERROR) {
         EXPORTER_LOG(AL_ERROR, "Unable to create key for url cookie encryption");
         return ZPATH_RESULT_ERR;
     }

     return ZPATH_RESULT_NO_ERROR;
}


/* Input: request, max_size
 * Input: domain_cookie Decrypted domain cookie
 * Output: encoded_ecookie_domain
 */
static int exporter_encrypt_dom_cookie(struct exporter_request *request, const char *domain_cookie_value, size_t max_size, char *encoded_ecookie_domain)
{
    /* expiry set to max 5 minutes from current time */
    int64_t now_plus_5min_ms = (epoch_s() + 300) * 1000;
    char encrypted[EXPORTER_URL_MAX_ENCODE_SIZE] = {0};
    size_t encrypted_len = sizeof(encrypted);
    size_t encoded_len = 0;
    struct zcrypt_key key = {{0}};
    char ecookie_domain[EXPORTER_URL_MAX_ENCODE_SIZE] = {0};
    int res = ZPATH_RESULT_NO_ERROR;
     // 1. snprintf domain cookie + expires ...
    snprintf(ecookie_domain, sizeof(ecookie_domain),
             "%s=%s&expires=%"PRId64,
             EXPORTER_QUERY_NAME_TO_DOMAIN_SET_COOKIE, domain_cookie_value,
             now_plus_5min_ms);
     // 2a. Generate the key used for encryption
    res = get_crypto_key_from_zpath_cloud_secret(&key, 0);
    if (res) return res;
     // 2b. Encrypt 'domain cookie + timestamp' with decoded key
    res = zcrypt_encrypt(&key, ecookie_domain, strlen(ecookie_domain), encrypted, &encrypted_len);
    if (res != ZCRYPT_RESULT_NO_ERROR)
    {
        EXPORTER_LOG(AL_ERROR, "%s: Unable to encrypt domain cookie, crypto cookie and timestamp (%s)", request->name, zpath_result_string(res));
        return ZPATH_RESULT_ERR;
    }
     // 3. base64 encode
    encoded_len = base64_encoded_size(encrypted_len);
    if (encoded_len > max_size){
        EXPORTER_LOG(AL_ERROR, "%s: Encoded length cannot be greater than MAX url size", request->name);
        return ZPATH_RESULT_ERR;
    }
    base64_encode_binary(encoded_ecookie_domain, (uint8_t*)encrypted, encrypted_len);
    EXPORTER_LOG(AL_DEBUG, "%s: encoded_ecookie_domain = %s", request->name, encoded_ecookie_domain);
    return ZPATH_RESULT_NO_ERROR;
}

/* Input: request, max_size
 * Output: Decrypted domain cookie
 */
static int exporter_decrypt_dom_cookie(struct exporter_request *request,
                                            char *domain_cookie_value,
                                            size_t max_size)
{
    int res = ZPATH_RESULT_NO_ERROR;
    char key_string_match[200] = {'\0'};
    struct zcrypt_key key = {{0}};

    snprintf(key_string_match, 200, "%s=", EXPORTER_QUERY_NAME_TO_DOMAIN_SET_COOKIE); /* cookie= */

    res = query_string_find(request->url, &(request->url_parser), EXPORTER_QUERY_NAME_TO_DOMAIN_SET_ENCRYPTED_COOKIE, domain_cookie_value, max_size);
    if (res) {
        EXPORTER_LOG(AL_ERROR, "%s: No %s: %s", request->name, EXPORTER_QUERY_NAME_TO_DOMAIN_SET_ENCRYPTED_COOKIE, zpath_result_string(res));
        return res;
    }

    /* Decrypt with current secret */
    res = get_crypto_key_from_zpath_cloud_secret(&key, 0);
    if(res) return res;

    /* decrypt, validate timestamp and get domain cookie from ecookie*/
    res = exporter_validate_timestamp_of_urlcookie(request, &key, domain_cookie_value, max_size, key_string_match);
    if (res == ZPATH_RESULT_BAD_ARGUMENT) {
        /* Try again - decrypt with 'previous' cloud secret */
        memset(&key, 0 , sizeof(key));
        res = get_crypto_key_from_zpath_cloud_secret(&key, 1);
        if(res) return res;
        res = exporter_validate_timestamp_of_urlcookie(request, &key, domain_cookie_value, max_size, key_string_match);
    }

    return res;
}

/*
 * Data from HTTPS request will be encrypted and base64 encoded
 * here are the steps to parse data:
 * 1.) Get the nonce value from the request query params
 * 2.) Nonce will be encrypted and base64 decoded. Decrypt the nonce
 * 3.) Use the nonce to get the encryption Key & IV from object store
 * 4.) Decode base64 the request payload
 * 5.) Decrypt it using the key obtained in step 2
 * 6.) Delete the key from object store to avoid replay attack
 * Pass the decrypted data everywhere to keep the fingerprint creation flow same as earlier
 */
static int exporter_csp_decrypt_payload(struct exporter_request *request,
                                        char* data, int content_length,
                                        uint8_t** output_data, int *output_len) {
    int res = 0;
    char encoded_object_key[EXPORTER_URL_MAX_ENCODE_SIZE] = {0};
    char decrypted_object_key[EXPORTER_URL_MAX_ENCODE_SIZE] = {0};
    char enc_key[EXPORTER_CSP_ENC_KEY_LEN + 1] = {0};
    char enc_iv[EXPORTER_CSP_ENC_IV_LEN + 1] = {0};
    struct zcrypt_key key = {{0}};
    struct argo_object *csp_enc_non = NULL;
    struct exporter_csp_encryption_context *enc_context;

    memset(enc_key, 0, sizeof(enc_key));
    memset(enc_iv, 0, sizeof(enc_iv));
    memset(encoded_object_key, 0, sizeof(encoded_object_key));
    memset((unsigned char *)&key, 0, sizeof(struct zcrypt_key));
    memset(decrypted_object_key, 0, sizeof(decrypted_object_key));

    int re = query_string_find(request->url,
                               &(request->url_parser),
                               EXPORTER_QUERY_NAME_TO_SP_OS_ENCKEY,
                               encoded_object_key,
                               sizeof(encoded_object_key));
    if (re == ZPATH_RESULT_NOT_FOUND) {
        EXPORTER_LOG(AL_ERROR, "%s: CSP_DECRYPT_NONCE query param [%s] not found",
                                request->name, EXPORTER_QUERY_NAME_TO_SP_OS_ENCKEY);
        return re;
    }
    exporter_url_decode(encoded_object_key);

    re = exporter_csp_decrypt_nonce(request, encoded_object_key, decrypted_object_key);
    if (re) {
        EXPORTER_LOG(AL_ERROR, "%s: CSP_DECRYPT_NONCE Failed to decrypt nonce: %s",
                                    request->name, zpath_result_string(res));
    }
    EXPORTER_LOG(AL_DEBUG, "%s: CSP_DECRYPT_NONCE Here is the decrypted nonce: [%.*s]",
                                    request->name, EXPORTER_CSP_DEBUG_BYTES, decrypted_object_key);
    res = exporter_get_encryption_nonce(request->name, OBJECT_STORE_TYPE_FINGERPRINT_NONCE,
                                        decrypted_object_key, &csp_enc_non,
                                        exporter_request_async_callback,
                                        request,
                                        0);

    if (res) {
        if (res == ZPATH_RESULT_ASYNCHRONOUS) {
             __sync_fetch_and_add_4(&(request->async_count), 1);
             EXPORTER_DEBUG_AUTH("%s: CSP_DECRYPT_NONCE get nonce asynchronously, async_count (%d->%d)", request->name,
                     request->async_count - 1, request->async_count);
             return res;
        } else {
            EXPORTER_LOG(AL_ERROR, "%s: CSP_DECRYPT_DATA Failed to decrypt payload: %s",
                         request->name, zpath_result_string(res));
        }
    } else {
        enc_context = csp_enc_non->base_structure_void;
        snprintf(enc_key, EXPORTER_CSP_ENC_KEY_LEN + 1, "%s", enc_context->key);
        snprintf(enc_iv, EXPORTER_CSP_ENC_IV_LEN + 1, "%s", enc_context->iv);
        EXPORTER_DEBUG_CSP("%s: CSP_DECRYPT_DATA enc_key = [%.*s] enc_iv = [%.*s]",
                            request->name, EXPORTER_CSP_ENC_KEY_LEN, enc_key,
                            EXPORTER_CSP_ENC_IV_LEN, enc_iv);

        memcpy((unsigned char *)&key, enc_key, EXPORTER_CSP_ENC_KEY_LEN);
        EXPORTER_DEBUG_CSP("%s: CSP_DECRYPT_DATA nonce = [%s] key = [%s] key_len = [%ld] "
                           "IV = [%s] IV_len = [%ld] zkey = [%.*s] zkey_len = [%ld] re=[%d]",
                            request->name,
                            decrypted_object_key,
                            enc_key,
                            strlen(enc_key),
                            enc_iv,
                            strlen(enc_iv),
                            EXPORTER_CSP_ENC_KEY_LEN,
                            (unsigned char *)&key,
                            sizeof(key),
                            re);

        /*
         * zcrypt_decrypt API expects the IV to be prefixed in the encrypted payload.
         * So, we have to some some preprocessing here, before calling the decryption
         * API.
         */
        size_t decoded_len = base64_decoded_size(data, content_length);
        uint8_t *encrypted_data = EXPORTER_CALLOC(EXPORTER_CSP_ENC_IV_LEN+decoded_len+1);
        snprintf((char *)encrypted_data, EXPORTER_CSP_ENC_IV_LEN + 1, "%s", enc_iv);

        uint8_t *decrypted_data = EXPORTER_CALLOC(EXPORTER_CSP_ENC_IV_LEN+decoded_len+1);
        size_t decrypted_data_len = decoded_len+EXPORTER_CSP_ENC_IV_LEN;
        int64_t decode_payload_len = base64_decode_binary(encrypted_data+EXPORTER_CSP_ENC_IV_LEN,
                                                         data, content_length);

        if (decode_payload_len < 0) {
            EXPORTER_DEBUG_CSP("%s: CSP_DECRYPT_DATA decoding data failed len=[%"PRId64"]",
                                    request->name, decode_payload_len);
            EXPORTER_FREE(decrypted_data);
            EXPORTER_FREE(encrypted_data);
            return ZPATH_RESULT_ERR;
        }

        EXPORTER_DEBUG_CSP("%s: CSP_DECRYPT_DATA decode_payload_len = [%"PRId64"]",
                                request->name, decode_payload_len);

        res = zcrypt_decrypt(&key, encrypted_data, EXPORTER_CSP_ENC_IV_LEN+decoded_len,
                                  decrypted_data, &decrypted_data_len);
        if (res < 0) {
            EXPORTER_FREE(encrypted_data);
            EXPORTER_FREE(decrypted_data);
            exporter_csp_free(csp_enc_non);
            return ZPATH_RESULT_ERR;
        }

        if (decrypted_data) {
            decrypted_data[decrypted_data_len] = 0;
        }
        EXPORTER_DEBUG_CSP("%s: CSP_DECRYPT_DATA decoded_len [%ld] "
                           "decrypted_data_len = [%ld] encrypted data [%.*s] decrypted data [%.*s]",
                               request->name,
                               decoded_len,
                               decrypted_data_len,
                               content_length,
                               data ? data : "nil",
                               (int)decrypted_data_len,
                               decrypted_data ? (char *)decrypted_data : "nil");

        /*
         * After done with decrypting payload, simply update the data pointer and content length
         * to keep the rest of the flow same
         */
        *output_data = decrypted_data;
        *output_len = (int)decrypted_data_len;

        res = object_store_delete(request->name, decrypted_object_key, NULL, NULL, 0, ostore_role_object_store);
        if (res && res != ZPATH_RESULT_ASYNCHRONOUS) {
            EXPORTER_DEBUG_CSP("%s: CSP_DECRYPT_DATA Failed to delete nonce [%s] Error [%s]!!",
                                  request->name, decrypted_object_key, zpath_result_string(res));
        }

        EXPORTER_FREE(encrypted_data);
        exporter_csp_free(csp_enc_non);

        /*
         * End of encryption/decryption changes
         */
    }

    return ZPATH_RESULT_NO_ERROR;
}

static int request_path_store_canvas_fingerprint_periodic_body(struct exporter_request *request)
{
    int res = ZPATH_RESULT_NO_ERROR;
    int content_length = 0;
    char *data = NULL;

    request->is_csp_request = 1;

    if (request->request_body) {
        content_length = (int)evbuffer_get_length(request->request_body);
    }

    EXPORTER_DEBUG_CSP("%s: setcspcontinousbody req_state: {%s,%s,%d%d,%s,%d,%d,%d}",
            request->name,
            request->log.url,
            exporter_request_input_state_get_str(request),
            request->http_request_complete, request->http_response_complete,
            exporter_request_async_state_get_str(request->async_state),
            request->async_count,
            request->request_body ? (int)evbuffer_get_length(request->request_body):-1,
            request->response_data ? (int)evbuffer_get_length(request->response_data): -1);

    if (request->input_state == input_state_drain_and_process_request_data) {

        if (request->async_state == async_state_csp_periodic_wrote_session) {

            EXPORTER_DEBUG_CSP("%s: setcspcontinousbody session csp written success state=drain content_len: [%d] data: [%.*s]",
                               request->name, content_length, content_length, data ? data : "nil");

            /* Reverify - memleak for body free here */
            return ZPATH_RESULT_NO_ERROR;
        }

        if (request->request_body) {
            content_length = (int)evbuffer_get_length(request->request_body);
        }

        EXPORTER_DEBUG_CSP("%s: setcspcontinousbody content_len: [%d] data: [%.*s]",
                request->name, content_length, content_length, data ? data : "nil");

        if (content_length) {
            /*
               1. Get Session object data, because we have to consider config mask and profile id from session data
               2. Parse the received data and compare to find change mask
               3. if the fingerprint data was never sent earlier then we have to copy the session obj inside domain
               4. Store the domain specific finger print data which will be deleted once we send it in exporter logs
             */
            struct argo_object *csp_obj = NULL;
            res = exporter_get_csp_data(request->name, OBJECT_STORE_TYPE_FINGERPRINT,
                    request->cb_session_cookie, &csp_obj,
                    exporter_request_async_callback,
                    request,
                    0);

            if (res) {
                if (res == ZPATH_RESULT_ASYNCHRONOUS) {
                    /* BUG: We have already filled response in previous callback, hence this will not be processed
                     */
                    __sync_fetch_and_add_4(&(request->async_count), 1);
                    request->async_state = async_state_csp_periodic_get_session;
                    EXPORTER_DEBUG_CSP("%s: CSP_DATA Fetching csp_session_key async_count (%d->%d) res=%s",
                                       request->name, request->async_count - 1, request->async_count, zpath_result_string(res));
                    request->input_state = input_state_drain_and_process_request_data;
                    return ZPATH_RESULT_NO_ERROR;
                } else {
                    EXPORTER_LOG(AL_ERROR, "%s: CSP_DATA csp_session_key get error: %s",
                                 request->name, zpath_result_string(res));
                }
            } else {
                EXPORTER_DEBUG_CSP("%s: CSP_DATA Got csp_session_key", request->name);
                if (request->async_state == async_state_csp_periodic_wrote_domain) {

                    if (exporter_is_first_bfp(csp_obj)) {
                        // call to session object store
                        res = exporter_set_first_fp_sent_flag(request->name,
                                                              csp_obj,
                                                              exporter_request_async_callback,
                                                              request,
                                                              0);
                        /* That means session data was never sent we have to encode
                           session object also in master fp */
                        if (res) {
                            if (res == ZPATH_RESULT_ASYNCHRONOUS) {
                                __sync_fetch_and_add_4(&(request->async_count), 1);
                                request->async_state = async_state_csp_periodic_wrote_session;
                                EXPORTER_DEBUG_CSP("%s: CSP_DATA write csp_sess_key 1st fp change asyn async_count (%d->%d)",
                                                    request->name, request->async_count - 1, request->async_count);
                                request->input_state = input_state_drain_and_process_request_data;
                                exporter_csp_free(csp_obj);
                                return ZPATH_RESULT_NO_ERROR;
                            } else {
                                EXPORTER_LOG(AL_ERROR, "%s: CSP_DATA csp_sess_key 1st fp change get error: %s",
                                             request->name, zpath_result_string(res));
                            }
                        } else {
                            EXPORTER_LOG(AL_DEBUG,"%s: CSP_DATA successfully written csp_sess_key 1st fp change", request->name);
                        }
                    } else {
                        EXPORTER_LOG(AL_DEBUG,"%s: CSP_DATA nothing left to write, just return", request->name);
                    }
                } else {
                    data = (char *)evbuffer_pullup(request->request_body, content_length);
                    uint8_t *decrypted_data = NULL;
                    int decrypted_data_len = 0;

                    if (request->js_encryption_enabled) {
                        int ret_val = exporter_csp_decrypt_payload(request, data, content_length,
                                                                   &decrypted_data, &decrypted_data_len);
                        if (ret_val) {
                            if (ret_val == ZPATH_RESULT_ASYNCHRONOUS) {
                                exporter_csp_free(csp_obj);
                                return ZPATH_RESULT_NO_ERROR;
                            } else {
                                EXPORTER_LOG(AL_ERROR, "%s: setcspcontinousbody CSP_NONCE_PARSER decryption failed %s",
                                             request->name, zpath_result_string(ret_val));
                            }
                        } else {
                            data = (char *)decrypted_data;
                            content_length = decrypted_data_len;
                        }
                    }

                    if (ZPATH_RESULT_NO_ERROR == exporter_parse_csp_json_data(request,
                                                                              data,
                                                                              content_length,
                                                                              csp_obj,
                                                                              1,//since we are in doauth_body MON is already checked before we injected JS
                                                                              exporter_csp_get_cfg_mask(csp_obj),
                                                                              exporter_csp_get_profile_id(csp_obj))) {

                        exporter_compare_n_update_csp_data(csp_obj, request->csp_data);

                        uint8_t is_first = exporter_is_first_bfp(csp_obj);

                        /* If it is first bfp data to be sent then set the bfp status as update sent */
                        /* if the session object monitoring was off and no session fingerprint
                         * data is sent then dump the json object in session structure
                         */
                        res = exporter_write_domain_csp_data(request->name,
                                                             request->cb_domain_cookie,
                                                             0,//fill mbfp
                                                             request->csp_data,
                                                             is_first ? csp_obj : NULL,
                                                             exporter_request_async_callback,
                                                             request,
                                                             0);

                        if (res) {
                            if (res == ZPATH_RESULT_ASYNCHRONOUS) {
                                __sync_fetch_and_add_4(&(request->async_count), 1);
                                request->async_state = async_state_csp_periodic_wrote_domain;
                                EXPORTER_LOG(AL_DEBUG, "%s: CSP_DATA write csp_dom_key asyn async_count (%d->%d) res=%s",
                                                    request->name, request->async_count - 1, request->async_count,
                                                    zpath_result_string(res));
                                request->input_state = input_state_drain_and_process_request_data;
                                exporter_csp_free(csp_obj);
                                if (decrypted_data) EXPORTER_FREE(decrypted_data);
                                return ZPATH_RESULT_NO_ERROR;
                            } else {
                                EXPORTER_LOG(AL_ERROR, "%s: CSP_DATA csp_dom_key get error: %s",
                                             request->name, zpath_result_string(res));
                            }
                        } else {
                            EXPORTER_LOG(AL_DEBUG,"%s: CSP_DATA successfully written csp_dom_key", request->name);
                        }
                    } else {
                        EXPORTER_LOG(AL_ERROR, "%s: CSP_DATA JSON parse failed", request->name);
                    }
                    if (decrypted_data) EXPORTER_FREE(decrypted_data);
                }
                exporter_csp_free(csp_obj);
            }// found csp session key
        }//content length
    } else {
        EXPORTER_DEBUG_CSP("%s: setcspcontinousbody state!=drain session not found", request->name);
    }

    return ZPATH_RESULT_NO_ERROR;
}

static int request_path_store_canvas_fingerprint_fromsp_body(struct exporter_request *request)
{
    int res = 0;
    int content_length = 0;
    char *data = NULL;

    request->is_csp_request = 1;

    if (request->request_body) {
        content_length = evbuffer_get_length(request->request_body);
        data = (char *)evbuffer_pullup(request->request_body, content_length);
    }

    EXPORTER_DEBUG_CSP("%s: setcspfromspbody req_state: {%s,%s,%d%d,%s,%d,%d,%d}",
            request->name,
            request->log.url,
            exporter_request_input_state_get_str(request),
            request->http_request_complete, request->http_response_complete,
            exporter_request_async_state_get_str(request->async_state),
            request->async_count,
            request->request_body ? (int)evbuffer_get_length(request->request_body):-1,
            request->response_data ? (int)evbuffer_get_length(request->response_data): -1);

    // State can be drain if async is complete in fromsp but body is still to come
    // so content_length will be 0
    if (request->input_state != input_state_drain_and_process_request_data || 0 == content_length) {
        EXPORTER_DEBUG_CSP("%s: setcspfromspbody POST state!=drain content_len: [%d] data: [%.*s]",
                request->name, content_length, content_length, data ? data : "nil");
        return 0;
    }

    EXPORTER_DEBUG_CSP("%s: setcspfromspbody POST state=drain content_len: [%d] data: [%.*s]",
            request->name, content_length, content_length, data ? data : "nil");

    if (request->async_state == async_state_csp_from_sp_process_data_wrote_domain) {
        /* we can enter in this state when async domain csp data
           write in object store is success or monitoring is off on
           session and we skip writing domain specific entry
         */
        EXPORTER_DEBUG_CSP("%s: CSP_DATA setcspfromspbody domain fp write complete, set response url: [%s]",
                           request->name, request->log.url);
        return ZPATH_RESULT_NO_ERROR;
    }

    if (request->async_state == async_state_csp_from_sp_process_data_wrote_session) {

        EXPORTER_DEBUG_CSP("%s: CSP_DATA setcspfromspbody session fp write complete, set response url: [%s]",
                           request->name, request->log.url);
        res = exporter_write_domain_csp_data(request->name,
                                             request->cb_domain_cookie,
                                             1,//this is master data in domain
                                             request->csp_data,
                                             NULL,
                                             exporter_request_async_callback,
                                             request,
                                             0);

        if (res) {
            if (res == ZPATH_RESULT_ASYNCHRONOUS) {
                EXPORTER_DEBUG_CSP("%s: CSP_DATA Writing domain object asynchronously, async_count(%d->%d) res %s",
                                    request->name,
                                    request->async_count, request->async_count+1,zpath_result_string(res));
                __sync_fetch_and_add_4(&(request->async_count), 1);
                request->async_state = async_state_csp_from_sp_process_data_wrote_domain;
                return ZPATH_RESULT_NO_ERROR;
            } else {
                /* Note: no async count change because we set no callback */
                EXPORTER_LOG(AL_ERROR, "%s: CSP_DATA domain fp write fail %s",
                             request->name, zpath_result_string(res));
            }
        } else {
            EXPORTER_DEBUG_CSP("%s: CSP_DATA domain write success", request->name);
        }
    }

    /* If we are expecting buffer but libev did 2 calls and buffer comes in second call
     * then state will be async_state_from_sp_wrote_session since in /fromsp
     * buffer will be zero and async_state_csp_from_sp_process_data not set
     *
     * Since we have to process anyways we change the state here
     */
    if (request->async_state == async_state_from_sp_wrote_session) {
        request->async_state = async_state_csp_from_sp_process_data;
    }

    if (request->async_state == async_state_csp_from_sp_process_data) {
            uint8_t *decrypted_data = NULL;
            int decrypted_data_len = 0;
            /*
             *  We only come here from SSO
             * 1. Get exporter session from session key
             * 2. Set bfp using session key
             */

            /*
             * Drain all POST data once it has been stored
             * All waiting conn->requests will wake up and first they will call
             * exporter_request_send_data
             * Since function exporter_request_send_data has check for mtunnel, POST
             * data is not sent
             *
             * But its good to drain this data once its been read
             *
             * For async call to object store we don't hit exporter_conn_process_request
             * Because of this if we change state to drain data is never drained
             *
             * Ideally we should call exporter_conn_process_request instead of
             * exporter_request_process, but we don't want to change design for CSP
             */

            EXPORTER_DEBUG_CSP("%s: setcspfromspbody drain data req_state: {%s,%s,%d%d,%s,%d,%d,%d}",
                    request->name,
                    request->log.url,
                    exporter_request_input_state_get_str(request),
                    request->http_request_complete, request->http_response_complete,
                    exporter_request_async_state_get_str(request->async_state),
                    request->async_count,
                    request->request_body ? (int)evbuffer_get_length(request->request_body):-1,
                    request->response_data ? (int)evbuffer_get_length(request->response_data): -1);

            if (request->js_encryption_enabled) {
                int ret_val = exporter_csp_decrypt_payload(request, data, content_length,
                                                           &decrypted_data, &decrypted_data_len);
                if (ret_val) {
                    if (ret_val == ZPATH_RESULT_ASYNCHRONOUS) {
                        return ZPATH_RESULT_NO_ERROR;
                    } else {
                        EXPORTER_LOG(AL_ERROR, "%s: setcspfromspbody CSP_NONCE_PARSER decryption failed %s",
                                     request->name, zpath_result_string(ret_val));
                    }
                } else {
                    data = (char *)decrypted_data;
                    content_length = decrypted_data_len;
                }
            }

            res = exporter_parse_csp_json_data(request,
                    data,
                    content_length,
                    /* Since we are parsing session fingerprint data which has user location,
                     finding change is not relevant as this itself is master data */
                    NULL,
                    request->is_mon_enabled,
                    request->criteria_flags_mask,
                    request->csp_profile_gid);

            if (res == ZPATH_RESULT_NO_ERROR) {
                // object store write
                /* is_mon_enabled = true
                    - save session and domain
                    - session flag should be is_1st_bfp = 0
                    - data is sent from domain is_1st_bfp = 1
                   is_mon_enabled = false
                    - save session only with is_1st_bfp = 1
                 */
                res = exporter_write_session_csp_data(request->name,
                                                      request->cb_session_cookie,
                                                      (request->is_mon_enabled)?0:1,//do not send mbfp in domain, if monitoring was enabled
                                                      request->csp_data,
                                                      exporter_request_async_callback,
                                                      request,
                                                      0);

                if (res) {
                    if (res == ZPATH_RESULT_ASYNCHRONOUS) {
                        EXPORTER_DEBUG_CSP("%s: CSP_DATA Writing session object asynchronously, async_count(%d->%d) res=%s",
                                           request->name,
                                           request->async_count, request->async_count+1, zpath_result_string(res));
                        __sync_fetch_and_add_4(&(request->async_count), 1);
                        /* Move to write domain key when session key response is received*/
                        request->async_state = async_state_csp_from_sp_process_data_wrote_session;
                        EXPORTER_DEBUG_CSP("%s: CSP_DATA move to async_state_csp_from_sp_process_data_wrote_session state", request->name);
                        if (decrypted_data) EXPORTER_FREE(decrypted_data);
                        return ZPATH_RESULT_NO_ERROR;
                    } else {
                        /* Note: no async count change because we set no callback */
                        EXPORTER_LOG(AL_ERROR, "%s: CSP_DATA session write fail, res= %s", request->name, zpath_result_string(res));
                    }
                } else {
                    EXPORTER_LOG(AL_DEBUG, "%s: CSP_DATA session write success", request->name);
                }
            //json parse success
            } else {
                EXPORTER_LOG(AL_ERROR, "%s: CSP_DATA JSON parsing failed res=%s",request->name, zpath_result_string(res));
            }

            /* Reverify - This will not get hit for async call, possible memleak */
            if (request->request_body) {
                request->conn->inbound_memory -= evbuffer_get_length(request->request_body);
                evbuffer_free(request->request_body);
                request->request_body = NULL;
            }
            if (decrypted_data) EXPORTER_FREE(decrypted_data);
    }

    return ZPATH_RESULT_NO_ERROR;
}


/*

-----------------------------------------------
Below are the changes to handle exception
-----------------------------------------------
Before redirecting to fingerprint URL
- Exporter will embed the entire URL into query param fromsporigurl or doauthorigurl
- So we have ?fromsporigurl="url_exception"
- In case of any exception JS will redirect browser to url_exception
- When exporter gets this URL it will turn OFF CSP for that HTTP request and flow will resume normally

-----------------------------------------------
Stopping user from exploiting exception URL
to bypass fingerprint
-----------------------------------------------
-  Since we will be using this special URL to bypass CSP this can be exploited by user
-  User can send this request again in fromsp/doauth which will result into opening apps without CSP
-  Also in fromsp we have oskey and we do not want user to use same assertion key by exploiting this URL
    -  In current design assertion key is always used once by exporter
    -  If in fromsp same oskey comes again exporter deletes this oskey
    -  On app access assertion fails since oskey is not found and authentication is done again
-  Now this can be exploited by user since if oskey is already used and then exception happens we
   will end up using same assertion key again, and user can use same oskey again and again and also bypass CSP

-----------------------------------------------
Why nonce is needed
-----------------------------------------------
- While redirecting to CSP we add nonce now
- In case of excpetion this nonce comes back to us and exporter validates and delete this
    - Access is provided to APP
- Next time if user uses same exception URL, nonce is not valid and reuse of API is denied
- This makes sure
    - Replay attack and reuse of oskey is not exploited
    - Exception url is not used as means to bypass CSP if enabled for tenant

-----------------------------------------------
Use cases for JS exception
-----------------------------------------------
- Handled timeout for XHR
- Error handling in XHR for incorrect FQDN
- Error handling from exporter
- For each exception JS will open a EXCEPTION URL provided by Exporter

-----------------------------------------------
Phase 2 work and enchancements
-----------------------------------------------
- We can use encrypted nonce + JWT
- We can extend this model of nonce to sessionid and maintain session context
    - To store profile/policy info, which currently we have to re-fetch
    - To store timestamp for profiling start/end of fingerprint
        - Currently we can calculate fingerprint from event logs which have timestamp
    - We will make a random key/value like below
        - Key = session id
        - Value = nonce + policy + profile + timestamp etc.
        - Delete sessionid once APP access is provided


-----------------------------------------------
Flow from browser
-----------------------------------------------

==> Step1: Original fromsp comes and we redirect to CSP which has nonce also

REQUEST: https://d.zpa-auth.net/IevTunx4Bg/fromsp?origurl=https%3A%2F%2Fserver2.ba.app1.mockfirm.com%3A443%2F&oskey=p1%2Cspe%2C44a7b51b-b5a7-426b-9bad-114b6aee049b

{
    "GET": {
        "scheme": "https",
        "host": "d.zpa-auth.net",
        "filename": "/IevTunx4Bg/fromsp",
        "query": {
            "origurl": "https://server2.ba.app1.mockfirm.com:443/",
            "oskey": "p1,spe,44a7b51b-b5a7-426b-9bad-114b6aee049b"
        },
        "remote": {
            "Address": "************:443"
        }
    }
}

RESPONSE: has fromsporigurl which is exception URL with
nonce=p1|csp|non|Y9NAspJ8HrWgVHoUxdL0/OrThnG26r8WqnowGF5zgW8=

Location: https://d.zpa-auth.net:443/IevTunx4Bg/getcsp-g?fromsporigurl=https%3A%2F%2Fd%2Ezpa%2Dauth%2Enet%3A443%2FIevTunx4Bg%2Ffromsp2%3Forigurl%3Dhttps%253A%252F%252Fserver2%2Eba%2Eapp1%2Emockfirm%2Ecom%253A443%252F%26oskey%3Dp1%252Cspe%252C44a7b51b%2Db5a7%2D426b%2D9bad%2D114b6aee049b%26nonce%3Dp1%7Ccsp%7Cnon%7CY9NAspJ8HrWgVHoUxdL0%2FOrThnG26r8WqnowGF5zgW8%3D&origurl=https%3A%2F%2Fserver2%2Eba%2Eapp1%2Emockfirm%2Ecom%3A443%2F


==> Step2: We sent setcspfromsp and inject some error in JS**

REQUEST: https://d.zpa-auth.net/IevTunx4Bg/setcspfromsp?fromsporigurl=https%3A%2F%2Fd.zpa-auth.net%3A443%2FIevTunx4Bg%2Ffromsp2%3Forigurl%3Dhttps%253A%252F%252Fserver2.ba.app1.mockfirm.com%253A443%252F%26oskey%3Dp1%252Cspe%252C44a7b51b-b5a7-426b-9bad-114b6aee049b%26nonce%3Dp1%7Ccsp%7Cnon%7CY9NAspJ8HrWgVHoUxdL0%2FOrThnG26r8WqnowGF5zgW8%3D&origurl=https%3A%2F%2Fserver2.ba.app1.mockfirm.com%3A443%2F

{
    "POST": {
        "scheme": "https",
        "host": "d.zpa-auth.net",
        "filename": "/IevTunx4Bg/setcspfromsp",
        "query": {
            "fromsporigurl": "https://d.zpa-auth.net:443/IevTunx4Bg/fromsp2?origurl=https%3A%2F%2Fserver2.ba.app1.mockfirm.com%3A443%2F&oskey=p1%2Cspe%2C44a7b51b-b5a7-426b-9bad-114b6aee049b&nonce=p1|csp|non|Y9NAspJ8HrWgVHoUxdL0/OrThnG26r8WqnowGF5zgW8=",
            "origurl": "https://server2.ba.app1.mockfirm.com:443/"
        },
        "remote": {
            "Address": "************:443"
        }
    }
}

==> Step3: JS will open URL from query param fromsporigurl**

REQUEST: https://d.zpa-auth.net/IevTunx4Bg/fromsp2?origurl=https%3A%2F%2Fserver2.ba.app1.mockfirm.com%3A443%2F&oskey=p1%2Cspe%2C44a7b51b-b5a7-426b-9bad-114b6aee049b&nonce=p1%7Ccsp%7Cnon%7CY9NAspJ8HrWgVHoUxdL0%2FOrThnG26r8WqnowGF5zgW8%3D

{
    "GET": {
        "scheme": "https",
        "host": "d.zpa-auth.net",
        "filename": "/IevTunx4Bg/fromsp2",
        "query": {
            "origurl": "https://server2.ba.app1.mockfirm.com:443/",
            "oskey": "p1,spe,44a7b51b-b5a7-426b-9bad-114b6aee049b",
            "nonce": "p1|csp|non|Y9NAspJ8HrWgVHoUxdL0/OrThnG26r8WqnowGF5zgW8="
        },
        "remote": {
            "Address": "************:443"
        }
    }
}

==> Step4: Exporter reads nonce and validates and delete in object store

exporter_request.c  :     debug: 2920:0x7f81a82356c8 ***************:36285->************:443 s=867 sni=d.zpa-auth.net id=12:6:0x7f819c1bcf78: csp nonce found key: [p1|csp|non|Y9NAspJ8HrWgVHoUxdL0/OrThnG26r8WqnowGF5zgW8=] value: [N.A]
exporter_request.c  :     debug: 2946:0x7f81a82356c8 ***************:36285->************:443 s=867 sni=d.zpa-auth.net id=12:6:0x7f819c1bcf78: csp nonce deleted: [p1|csp|non|Y9NAspJ8HrWgVHoUxdL0/OrThnG26r8WqnowGF5zgW8=]


==> Step5: We cannot use the same URL again to bypass CSP or reuse oskey, nonce is invalid, and user get error

exporter_request.c  :     debug: 2910:0x7f81a824df78 ***************:9663->************:443 s=797 sni=d.zpa-auth.net id=16:0:0x7f819c1bcf78: csp nonce url: [/IevTunx4Bg/fromsp2] Fetching nonce: [p1|csp|non|Y9NAspJ8HrWgVHoUxdL0/OrThnG26r8WqnowGF5zgW8=] result: [ZPATH_RESULT_NOT_FOUND]
exporter_request.c  :     debug: 4959:0x7f81a824df78 ***************:9663->************:443 s=797 sni=d.zpa-auth.net id=16:0:0x7f819c1bcf78: Returning status 401

*/

static int exporter_csp_delete_nonce(struct exporter_request *request, char *nonce_cookie_value) {
    int res;
    /* Look in object store and delete once found */
    const char *value;
    res = object_store_get(request->name,
            nonce_cookie_value,
            &value,
            exporter_request_async_callback,
            request,
            0,
            ostore_role_object_store);

    if (res) {
        if (res == ZPATH_RESULT_ASYNCHRONOUS) {
            EXPORTER_DEBUG_AUTH("%s: csp nonce url: [%s] Fetching nonce asynchronously - async_count (%d->%d)",
                    request->name, request->log.url,
                    request->async_count, request->async_count + 1);
            exporter_set_async_state_and_inc_count(request, async_state_csp_get_nonce);
            return ZPATH_RESULT_ASYNCHRONOUS;
        } else {
            /* Either assertion is not found or we have object store error */
            EXPORTER_DEBUG_AUTH("%s: csp nonce url: [%s] Fetching nonce: [%s] result: [%s]",
                    request->name,
                    request->log.url,
                    nonce_cookie_value,
                    zpath_result_string(res));
            return res;
        }
    }

    EXPORTER_DEBUG_CSP("%s: csp nonce found deleting key: [%.*s] value: [%s]", request->name, EXPORTER_CSP_DEBUG_BYTES, nonce_cookie_value, value);

    /* Delete nonce after a single time use */
    res = object_store_delete(request->name, nonce_cookie_value, NULL, NULL, 0, ostore_role_object_store);
    if (res && (res != ZPATH_RESULT_ASYNCHRONOUS)) {
        /*
         * Critical error someone can replay if we fail to delete, so we will not give access
         * User can refresh and page will open if next time we are able to delete
         */
        EXPORTER_LOG(AL_ERROR, "%s: csp nonce delete failed nonce key: [%s]",
                request->name, nonce_cookie_value);
        return ZPATH_RESULT_ERR;
    } else {
        EXPORTER_DEBUG_CSP("%s: csp nonce deleted: [%.*s]", request->name, EXPORTER_CSP_DEBUG_BYTES, nonce_cookie_value);
    }
    return ZPATH_RESULT_NO_ERROR;
}

static int exporter_csp_handle_exception(struct exporter_request *request, int is_fromsp) {

    if (request->is_csp_exception == 0) {

        char nonce_cookie_value[EXPORTER_COOKIE_MAX_SIZE];
        int res;

        memset(nonce_cookie_value, 0, sizeof(nonce_cookie_value));
        res = query_string_find(request->url, &(request->url_parser), "nonce", nonce_cookie_value, sizeof(nonce_cookie_value));
        if (res) {
            EXPORTER_LOG(AL_ERROR, "%s: csp nonce not found %s", request->name, zpath_result_string(res));
            return exporter_request_respond(request, HTTP_STATUS_FORBIDDEN, EXPORTER_ERROR_CODE_CSP_NONCE_NOT_FOUND);
        }

        /* Look in object store and delete once found */
        const char *value;
        res = object_store_get(request->name,
                nonce_cookie_value,
                &value,
                exporter_request_async_callback,
                request,
                0,
                ostore_role_object_store);

        if (res) {
            if (res == ZPATH_RESULT_ASYNCHRONOUS) {
                EXPORTER_DEBUG_AUTH("%s: csp nonce url: [%s] Fetching nonce asynchronously - async_count (%d->%d)",
                        request->name, request->log.url,
                        request->async_count, request->async_count + 1);
                exporter_set_async_state_and_inc_count(request, async_state_csp_get_nonce);
                return ZPATH_RESULT_NO_ERROR;
            } else {
                /* Either assertion is not found or we have object store error */
                EXPORTER_DEBUG_AUTH("%s: csp nonce url: [%s] Fetching nonce: [%s] result: [%s]",
                        request->name,
                        request->log.url,
                        nonce_cookie_value,
                        zpath_result_string(res));
            }
            /* Critical error someone is trying to exploit this API and reuse oskey */
            return exporter_request_respond(request, HTTP_STATUS_UNAUTHORIZED, EXPORTER_ERROR_CODE_CSP_NONCE_INVALID);
        }

        EXPORTER_DEBUG_CSP("%s: csp nonce found key: [%s] value: [%s]", request->name, nonce_cookie_value, value);

        /*
         * Phase2: Add JWT handling and encrypt and sign nonce
         * Check if JWT is valid from objstore, if yes delete it after validation
         */
        uint8_t is_jwt_verified = 1;

        if (is_jwt_verified) {
            /* Valid request */
        } else {
            /* Critical error nonce found but JWT is not signed by us */
            return exporter_request_respond(request, HTTP_STATUS_UNAUTHORIZED, EXPORTER_ERROR_CODE_CSP_JWT_NOT_SIGNED);
        }

        /* Delete nonce after a single time use */
        res = object_store_delete(request->name, nonce_cookie_value, NULL, NULL, 0, ostore_role_object_store);
        if (res && (res != ZPATH_RESULT_ASYNCHRONOUS)) {
            /*
             * Critical error someone can replay if we fail to delete, so we will not give access
             * User can refresh and page will open if next time we are able to delete
             */
            EXPORTER_LOG(AL_ERROR, "%s: csp nonce delete failed nonce key: [%s]",
                    request->name, nonce_cookie_value);
            return exporter_request_respond(request, HTTP_STATUS_UNAUTHORIZED, EXPORTER_ERROR_CODE_CSP_NONCE_DELETION_FAILED);
        } else {
            EXPORTER_DEBUG_CSP("%s: csp nonce deleted: [%s]", request->name, nonce_cookie_value);
        }
    }

    /* Nonce is verified proceed to handle this exception */
    request->is_csp_exception = 1;

    if (is_fromsp == 1) {
        return request_path_auth_domain_from_sp(request);
    } else if (is_fromsp == 0) {
        return request_path_auth_domain_from_customer_domain(request);
    } else if (is_fromsp == 2) {
        //Known issue - Handle exception URL by recording lasttimestamp, this will bypass fingerprint next time app is accessed
        return exporter_request_respond(request, HTTP_STATUS_FORBIDDEN, EXPORTER_ERROR_CODE_CSP_NONCE_NOT_FOUND);
    }
    return exporter_request_respond(request, HTTP_STATUS_FORBIDDEN, EXPORTER_ERROR_CODE_CSP_NONCE_NOT_FOUND);
}

static int request_path_csp_exception_fromsp(struct exporter_request *request) {

    EXPORTER_DEBUG_CSP("%s: fromsp2 req_state: {%s,%s,%d%d,%s,%d,%d,%d}",
            request->name,
            request->log.url,
            exporter_request_input_state_get_str(request),
            request->http_request_complete, request->http_response_complete,
            exporter_request_async_state_get_str(request->async_state),
            request->async_count,
            request->request_body ? (int)evbuffer_get_length(request->request_body):-1,
            request->response_data ? (int)evbuffer_get_length(request->response_data): -1);

    return exporter_csp_handle_exception(request, 1);
}

static int request_path_csp_exception_continous(struct exporter_request *request) {

    EXPORTER_DEBUG_CSP("%s: continous2 req_state: {%s,%s,%d%d,%s,%d,%d,%d}",
            request->name,
            request->log.url,
            exporter_request_input_state_get_str(request),
            request->http_request_complete, request->http_response_complete,
            exporter_request_async_state_get_str(request->async_state),
            request->async_count,
            request->request_body ? (int)evbuffer_get_length(request->request_body):-1,
            request->response_data ? (int)evbuffer_get_length(request->response_data): -1);

    return exporter_csp_handle_exception(request, 2);
}

static int request_path_csp_exception_doauth(struct exporter_request *request) {

    EXPORTER_DEBUG_CSP("%s: doauth2 req_state: {%s,%s,%d%d,%s,%d,%d,%d}",
            request->name,
            request->log.url,
            exporter_request_input_state_get_str(request),
            request->http_request_complete, request->http_response_complete,
            exporter_request_async_state_get_str(request->async_state),
            request->async_count,
            request->request_body ? (int)evbuffer_get_length(request->request_body):-1,
            request->response_data ? (int)evbuffer_get_length(request->response_data): -1);

    return exporter_csp_handle_exception(request, 0);
}

static int request_path_store_canvas_fingerprint_fromsp(struct exporter_request *request)
{
    char orig_url_query_value[EXPORTER_URL_MAX_ENCODE_SIZE];
    int64_t customer_gid;
    struct http_parser_url parser;
    int res;
    struct http_parser_url orig_url_parser;
    char session_crypto_value[200];
    char assertion_key_query_value[EXPORTER_URL_MAX_ENCODE_SIZE];
    char session_key[EXPORTER_URL_MAX_ENCODE_SIZE];
    char session_key_with_domain[EXPORTER_URL_MAX_ENCODE_SIZE];
    char domain_cookie_value[EXPORTER_URL_MAX_ENCODE_SIZE];
    char orig_domain[EXPORTER_URL_MAX_ENCODE_SIZE];
    char err_code[100];
    char err_string[EXPORTER_URL_MAX_ENCODE_SIZE];
    struct argo_object *session = NULL;
    char *s;
    char *e;

    EXPORTER_DEBUG_CSP("%s: setcspfromsp req_state: {%s,%s,%d%d,%s,%d,%d,%d}",
	    request->name,
	    request->log.url,
	    exporter_request_input_state_get_str(request),
	    request->http_request_complete, request->http_response_complete,
	    exporter_request_async_state_get_str(request->async_state),
	    request->async_count,
	    request->request_body ? (int)evbuffer_get_length(request->request_body):-1,
	    request->response_data ? (int)evbuffer_get_length(request->response_data): -1);

    request->is_csp_request = 1;

    customer_gid = url_get_customer_gid(request, request->url, &(request->url_parser),
	    EXPORTER_QUERY_NAME_ORIGINAL_URL, orig_url_query_value, sizeof(orig_url_query_value), &parser);

    if (customer_gid == 0) {
	EXPORTER_LOG(AL_ERROR, "%s: CSP POST Could not get customer_gid from original URL (malformed?)", request->name);
	return exporter_request_respond(request, HTTP_STATUS_BAD_REQUEST, EXPORTER_ERROR_CODE_CUSTOMER_GID_NOT_FOUND);
    }

    // call to create domain cookies

    /* Original code fromsp modified for devicefingerprint starts here */

    /* Verify that the request is to the auth host */
    if (!request->conn->exporter_domain->is_auth_domain) {
	EXPORTER_LOG(AL_ERROR, "%s: Received request for auth path that isn't to auth domain", request->name);
	return exporter_request_respond(request, HTTP_STATUS_BAD_REQUEST, EXPORTER_ERROR_CODE_INVALID_AUTH_DOMAIN);
    }

    /* This should be POST request only */
    if (request->req_method != HTTP_POST) {
	return exporter_request_respond(request, HTTP_STATUS_FORBIDDEN, EXPORTER_ERROR_CODE_NOT_POST_REQUEST);
    }

    /* Verify if we are coming from fromsp */
    char redirect_origurl[2048];
    struct http_parser_url redirect_origurl_parser;

    res = find_cookie(request, EXPORTER_COOKIE_CRYPTO, 0, session_crypto_value, sizeof(session_crypto_value));
    if (res) {
	EXPORTER_LOG(AL_ERROR, "%s: No crypto key returning from SP", request->name);
	return exporter_request_respond(request, HTTP_STATUS_INTERNAL_SERVER_ERROR, EXPORTER_ERROR_CODE_CRYPTO_COOKIE_NOT_FOUND);
    }

    res = query_string_find(request->url, &(request->url_parser),
	    "fromsporigurl", redirect_origurl, sizeof(redirect_origurl));
    if (res == ZPATH_RESULT_NO_ERROR) {
	exporter_url_decode(redirect_origurl);
	EXPORTER_DEBUG_CSP("%s: CSP fromsporigurl: [%s]", request->name, redirect_origurl);
	http_parser_url_init(&redirect_origurl_parser);
	res = http_parser_parse_url(redirect_origurl, strlen(redirect_origurl), 0, &redirect_origurl_parser);
	if (res) {
	    EXPORTER_LOG(AL_ERROR, "%s: CSP Could not parse fromsporigurl", request->name);
            return 0;
        }
    }

    /*
     * Delete nonce if state has not reache async_state_from_sp_wrote_session
     * If state is async_state_from_sp_wrote_session then we have already deleted nonce
     * We must never change state async_state_from_sp_wrote_session
     */
    if (request->async_state != async_state_from_sp_wrote_session) {
        char nonce_cookie_value[EXPORTER_COOKIE_MAX_SIZE];
        memset(nonce_cookie_value, 0, sizeof(nonce_cookie_value));
        res = query_string_find(redirect_origurl, &redirect_origurl_parser, "nonce", nonce_cookie_value, sizeof(nonce_cookie_value));
        if (res) {
            EXPORTER_LOG(AL_ERROR, "%s: csp nonce not found %s", request->name, zpath_result_string(res));
        } else {
            res = exporter_csp_delete_nonce(request, nonce_cookie_value);
            if (res == ZPATH_RESULT_ASYNCHRONOUS) {
                return ZPATH_RESULT_NO_ERROR;
            }
        }
    }

    res = query_string_find(redirect_origurl, &redirect_origurl_parser,
            EXPORTER_QUERY_NAME_FROM_SP_ERR_CODE, err_code, sizeof(err_code));
    if (res == ZPATH_RESULT_NO_ERROR) {
        exporter_url_decode(err_code);
        res = query_string_find(redirect_origurl, &redirect_origurl_parser,
                EXPORTER_QUERY_NAME_FROM_SP_ERR_STRING, err_string, sizeof(err_string));
        if (res == ZPATH_RESULT_NO_ERROR) {
            exporter_url_decode(err_string);
            return exporter_request_respond_with_text(request, HTTP_STATUS_INTERNAL_SERVER_ERROR, EXPORTER_ERROR_CODE_URL_DECODE_FAILED, "Err %s: %s", err_code, err_string);
        } else {
            return exporter_request_respond_with_text(request, HTTP_STATUS_INTERNAL_SERVER_ERROR, EXPORTER_ERROR_CODE_ERROR_STRING_NOT_FOUND, "Err %s", err_code);
        }
    }

    /* Get customer_gid and orig_url from query string */
    customer_gid = url_get_customer_gid(request, redirect_origurl, &redirect_origurl_parser, EXPORTER_QUERY_NAME_FROM_SP_ORIGINAL_URL, orig_url_query_value, sizeof(orig_url_query_value), &orig_url_parser);
    if (!customer_gid) {
        /* Not so good... */
        EXPORTER_LOG(AL_ERROR, "%s: Received redirect from SP from which we could not extract customer_gid", request->name);
        return exporter_request_respond(request, HTTP_STATUS_BAD_REQUEST, EXPORTER_ERROR_CODE_CUSTOMER_GID_NOT_FOUND);
    }

    res = query_string_find(redirect_origurl, &redirect_origurl_parser, EXPORTER_QUERY_NAME_FROM_SP_OBJECT_KEY, assertion_key_query_value, sizeof(assertion_key_query_value));
    if (res) {
        /* 'oskey' is not sent by samlsp. Search for 'eoskey' (oskey in encrypted form) */
        res = query_string_find(redirect_origurl, &redirect_origurl_parser, EXPORTER_QUERY_NAME_FROM_SP_ENCRYPTED_OBJECT_KEY, assertion_key_query_value, sizeof(assertion_key_query_value));
        if (res) {
            /* Not so good... */
            EXPORTER_LOG(AL_ERROR, "%s: Received redirect from SP that doesn't have key: %s",
                    request->name, redirect_origurl);
            return exporter_request_respond(request, HTTP_STATUS_BAD_REQUEST, EXPORTER_ERROR_CODE_OSKEY_NOT_FOUND);
        }
        /* Found eoskey (oskey is encrypted along with timestamp). Decrypt using crypto, validate timestamp and update assertion_key_query_value  */
        char key_string_match[200] = {'\0'};
        size_t crypto_value_len = strlen(session_crypto_value);
        struct zcrypt_key key = {{0}};
        snprintf(key_string_match, sizeof(key_string_match), "%s=", EXPORTER_QUERY_NAME_FROM_SP_OBJECT_KEY); /* oskey= */
        /* Get the key used for encryption and decryption of oskey */
        if (crypto_value_len && (base64_decoded_size(session_crypto_value, crypto_value_len) <= sizeof(struct zcrypt_key))) {
            base64_decode_binary((unsigned char *)&key, session_crypto_value, crypto_value_len);
        } else {
            EXPORTER_LOG(AL_ERROR, "%s: Crypto key too large or too small (len= %lu)", request->name, crypto_value_len);
            return ZPATH_RESULT_ERR;
        }
        res = exporter_validate_timestamp_of_urlcookie(request, &key, assertion_key_query_value, sizeof(assertion_key_query_value), key_string_match);
        if (res) {
            return exporter_request_respond(request, HTTP_STATUS_BAD_REQUEST, EXPORTER_ERROR_CODE_INVALID_COOKIE_TIMESTAMP);
        }
    } else {
        exporter_url_decode(assertion_key_query_value);
    }
    /* Get the original domain- we need it for a couple cases below. */
    snprintf(orig_domain, sizeof(orig_domain), "%.*s", orig_url_parser.field_data[UF_HOST].len, &(orig_url_query_value[orig_url_parser.field_data[UF_HOST].off]));

    /* CSP_POLICY START*/
    const char *assertion_cookie = assertion_key_query_value;
    if (request->is_csp_enabled) {
        res = exporter_load_policy(request, assertion_cookie, session_crypto_value);
        if (res) {
            if (res == ZPATH_RESULT_ASYNCHRONOUS) {
                return ZPATH_RESULT_NO_ERROR;
            } else if (res == ZPN_RESULT_EXPIRED) {
                /* Reauth code must have set redirection so we are returning */
                return ZPATH_RESULT_NO_ERROR;
            } else {
                /* We are in setcspfromsp - Coming from SSO,
                 * we must capture fingerprint even if policy fails */
            }
        }

        res = exporter_read_csp_config(request);
        if (res == ZPN_RESULT_ASYNCHRONOUS) {
            return ZPATH_RESULT_NO_ERROR;
        }
    }//is_csp_enabled
    /* CSP_POLICY END */

    if (request->async_state == async_state_from_sp_wrote_session) {
        char set_cookie[EXPORTER_URL_MAX_ENCODE_SIZE];
        char set_cookie_legacy[EXPORTER_URL_MAX_ENCODE_SIZE];
        make_cookie(EXPORTER_DOMAIN_AUTH, EXPORTER_COOKIE_SESSION,
                request->cb_session_cookie, "None",
                EXPORTER_COOKIE_DEFAULT_LIFETIME_S,
                set_cookie, sizeof(set_cookie),
                set_cookie_legacy, sizeof(set_cookie_legacy));
        EXPORTER_DEBUG_AUTH("%s: Set cookie to %.*s", request->name, EXPORTER_DEBUG_BYTES, set_cookie);
        EXPORTER_DEBUG_AUTH("%s: Set legacy cookie to %.*s", request->name, EXPORTER_DEBUG_BYTES, set_cookie_legacy);
        EXPORTER_DEBUG_AUTH("%s: Orig URL =  %.*s", request->name, EXPORTER_DEBUG_BYTES, orig_url_query_value);

        /* encrypt domain cookie in the URL */
        char *DOM_COOKIE = EXPORTER_QUERY_NAME_TO_DOMAIN_SET_COOKIE; /* cookie */
        char *url_domain_cookie = request->cb_domain_cookie;
        char encoded_ecookie_domain[EXPORTER_URL_MAX_ENCODE_SIZE] = {'\0'};
        char is_ecookie = 0;
        if (is_url_cookie_encryption_enabled(customer_gid)) {
            res = exporter_encrypt_dom_cookie(request, request->cb_domain_cookie, EXPORTER_URL_MAX_ENCODE_SIZE, encoded_ecookie_domain);
            if (res) {
                return exporter_request_respond(request, HTTP_STATUS_BAD_REQUEST, EXPORTER_ERROR_CODE_DOMAIN_COOKIE_ENCRYPTION_FAILED);
            }
            DOM_COOKIE = EXPORTER_QUERY_NAME_TO_DOMAIN_SET_ENCRYPTED_COOKIE; /* ecookie */
            url_domain_cookie = encoded_ecookie_domain;
            is_ecookie = 1;
        }

        (void)DOM_COOKIE;
        char json_data[EXPORTER_URL_MAX_ENCODE_SIZE];

        //encode cookies
        char encoded_url_domain_cookie[2*EXPORTER_COOKIE_MAX_SIZE];
        char encoded_session_crypto_value[2*EXPORTER_COOKIE_MAX_SIZE];

        url_encode(url_domain_cookie, encoded_url_domain_cookie, sizeof(encoded_url_domain_cookie));
        url_encode(session_crypto_value, encoded_session_crypto_value, sizeof(encoded_session_crypto_value));

        EXPORTER_DEBUG_CSP("%s: Create response url: [%s] domain: [%s] session: [%s] cookie: [%s] is_ecookie: %d",
                request->name, request->log.url, orig_domain,
                request->cb_session_cookie, url_domain_cookie, is_ecookie);

        snprintf(json_data, sizeof(json_data),
                "{\"%s\": \"%s\", \"cryptcookie\": \"%s\"}",
                is_ecookie ? "ecookie" : "cookie", encoded_url_domain_cookie,
                encoded_session_crypto_value);

        res = exporter_request_respond_status_data_content_type_json(
                request,
                set_cookie,
                set_cookie_legacy,
                HTTP_STATUS_OK,
                //HTTP_STATUS_INTERNAL_SERVER_ERROR,//uncomment for exception testing
                json_data, strlen(json_data),
                "application/json",
                strlen(json_data),
                0);

        /*
         * Set state to drain since body cb will be called and we must not discard
         * data there
         * Body cb will be called 2 times
         * 1. When we come here for first time then http parser will call body cb
         * 2. After async call is complete we will set async state to async_state_csp_from_sp_process_data
         *    and call exporter_request_process_data
         */
        request->input_state = input_state_drain_and_process_request_data;

        /*
         * CSP_DATA - We come here for the first time
         * This is master data, store it
         */

        if (request->request_body && evbuffer_get_length(request->request_body)) {

            /*
             *  We only come here from SSO
             * 1. Get exporter session from session key
             * 2. Set bfp using session key
             */

            /*
             * Drain all POST data once it has been stored
             * All waiting conn->requests will wake up and first they will call
             * exporter_request_send_data
             * Since function exporter_request_send_data has check for mtunnel, POST
             * data is not sent
             *
             * But its good to drain this data once its been read
             *
             * For async call to object store we don't hit exporter_conn_process_request
             * Because of this if we change state to drain data is never drained
             *
             * Ideally we should call exporter_conn_process_request instead of
             * exporter_request_process, but we don't want to change design for CSP
             */

            EXPORTER_DEBUG_CSP("%s: setcspfromsp drain data req_state: {%s,%s,%d%d,%s,%d,%d,%d}",
                    request->name,
                    request->log.url,
                    exporter_request_input_state_get_str(request),
                    request->http_request_complete, request->http_response_complete,
                    exporter_request_async_state_get_str(request->async_state),
                    request->async_count,
                    request->request_body ? (int)evbuffer_get_length(request->request_body):-1,
                    request->response_data ? (int)evbuffer_get_length(request->response_data): -1);

            /* We will parse data in body cb function */
            exporter_request_enter_state(request, async_state_csp_from_sp_process_data);
        }//content length != 0

        return ZPATH_RESULT_NO_ERROR;
    }
    /* CSP_POLICY */

    /*
     * Get either modified session or new session:
     */
    res = find_cookie(request, EXPORTER_COOKIE_SESSION, 1, session_key, sizeof(session_key));
    if (res == ZPATH_RESULT_NO_ERROR) {
        /* Have a session cookie. Check if session really exists... */
        res = exporter_session_get(request->name,
                session_key,
                &session,
                exporter_request_async_callback,
                request,
                0);
        if (res) {
            if (res == ZPATH_RESULT_ASYNCHRONOUS) {
                EXPORTER_DEBUG_AUTH("%s: Looking up session %.*s asynchronously - async_count (%d->%d)", request->name,
                        EXPORTER_DEBUG_BYTES, session_key, request->async_count, request->async_count + 1);
                __sync_fetch_and_add_4(&(request->async_count), 1);
                return ZPATH_RESULT_NO_ERROR;
            } else {
                /* Fall through- we will not have a session and will end up making one.
                 * Note: we do not differentiate NOT_FOUND from other reasons (e.g. object store is down, ref: ET-29450).
                 */
                session = NULL;
            }
        } else {
            const char *assertion_key;
            /* We have a session already. */

            /* If the session has a key for this customer_gid, AND if
             * it is different from the key we were just given, delete
             * the old key. */
            res = exporter_session_get_auth_assertion_key(session, customer_gid, &assertion_key);
            if (res == ZPATH_RESULT_NO_ERROR) {
                if (strcmp(assertion_key, assertion_key_query_value) == 0) {
                    /* This is the same key we got back from somewhere
                     * else. This is really weird- We'll do the same
                     * processing, but I sort of want to log this */
                    EXPORTER_LOG(AL_WARNING, "%s: Got duplicate key from SP: %.*s", request->name, EXPORTER_DEBUG_BYTES, assertion_key);
                }
                EXPORTER_DEBUG_AUTH("%s: Deleting old assertion key %.*s", request->name, EXPORTER_DEBUG_BYTES, assertion_key);
                res = object_store_delete(request->name, assertion_key, NULL, NULL, 0, ostore_role_object_store);
                if (res && (res != ZPATH_RESULT_ASYNCHRONOUS)) {
                    EXPORTER_LOG(AL_ERROR, "%s: Could not delete assertion key %.*s", request->name, EXPORTER_DEBUG_BYTES, assertion_key);
                }
            }

            /* Delete the old session fingerprint data */
            res = exporter_delete_session_csp_data(request->name, session_key, NULL, NULL, 0);
            if (res && (res != ZPATH_RESULT_ASYNCHRONOUS)) {
                EXPORTER_LOG(AL_ERROR, "%s: Could not delete session fp data%.*s", request->name, EXPORTER_DEBUG_BYTES, session_key);
            }
            /* Delete the old session, because in any case we will be
             * creating a new one */
            EXPORTER_DEBUG_AUTH("%s: Deleting old session key %.*s for %s", request->name, EXPORTER_DEBUG_BYTES, session_key, orig_domain);
            /* Call delete for bfp */
            res = object_store_delete(request->name, session_key, NULL, NULL, 0, ostore_role_object_store);
            if (res && (res != ZPATH_RESULT_ASYNCHRONOUS)) {
                EXPORTER_LOG(AL_ERROR, "%s: Could not delete session key %.*s", request->name, EXPORTER_DEBUG_BYTES, session_key);
            }
            /* Now we have a session to write... */
        }
    }

    if (!session) {
        /* No session cookie- definitely need to create a session */
        res = object_store_gen_key(OBJECT_STORE_TYPE_SESSION, assertion_key_query_value, session_key,
                                   sizeof(session_key), ostore_role_object_store);
        if (res) {
            EXPORTER_LOG(AL_ERROR, "%s: Could not gen key", request->name);
            return exporter_request_respond(request, HTTP_STATUS_BAD_REQUEST, EXPORTER_ERROR_CODE_SESSION_KEY_GENERATION_FAILED);
        }
        session = exporter_session_create(session_key);
        if (!session) {
            EXPORTER_LOG(AL_ERROR, "%s: Could not gen session", request->name);
            return exporter_request_respond(request, HTTP_STATUS_BAD_REQUEST, EXPORTER_ERROR_CODE_SESSION_CREATION_FAILED);
        } else {
            EXPORTER_DEBUG_AUTH("%s: Created session %.*s", request->name, EXPORTER_DEBUG_BYTES, session_key);
        }
    }

    /* We now have a session. Don't forget to free it for all cases! */
    res = object_store_gen_key(OBJECT_STORE_TYPE_SESSION, assertion_key_query_value, session_key,
                               sizeof(session_key), ostore_role_object_store);
    if (res) {
        EXPORTER_LOG(AL_ERROR, "%s: Could not gen key", request->name);
        exporter_session_free(session);
        return exporter_request_respond(request, HTTP_STATUS_BAD_REQUEST, EXPORTER_ERROR_CODE_SESSION_KEY_GENERATION_FAILED);
    }

    /* Add the assertion key to the current session */
    EXPORTER_DEBUG_AUTH("%s: Adding assertion key %.*s to session %.*s", request->name, EXPORTER_DEBUG_BYTES, assertion_key_query_value, EXPORTER_DEBUG_BYTES, session_key);
    res = exporter_session_add_auth_token(&session, session_key, assertion_key_query_value, customer_gid);
    if (res) {
        EXPORTER_LOG(AL_ERROR, "%s: Could not add auth token to session %.*s", request->name, EXPORTER_DEBUG_BYTES, session_key);
        exporter_session_free(session);
        return exporter_request_respond(request, HTTP_STATUS_BAD_REQUEST, EXPORTER_ERROR_CODE_AUTH_TOKEN_ADD_TO_SESSION_FAILED);
    }

    /* We need a couple keys to reference back to this session- and we
     * need to remember them through a callback. */

    res = object_store_gen_key(OBJECT_STORE_TYPE_DOMAIN, /* Hint */ assertion_key_query_value, domain_cookie_value,
                               sizeof(domain_cookie_value), ostore_role_object_store);
    if (res) {
        EXPORTER_LOG(AL_ERROR, "%s: Could not gen key", request->name);
        exporter_session_free(session);
        return exporter_request_respond(request, HTTP_STATUS_BAD_REQUEST, EXPORTER_ERROR_CODE_DOMAIN_COOKIE_GENERATION_FAILED);
    }

    s = request->cb_session_cookie;
    e = s + sizeof(request->cb_session_cookie);
    s += sxprintf(s, e, "%s", session_key);
    /* We write the session key to the DB with the domain attached to
     * it so that we can do domain verification on cookie use. This
     * prevents a cookie from one domain from being used on another
     * domain. */
    s = session_key_with_domain;
    e = s + sizeof(session_key_with_domain);
    s += sxprintf(s, e, "%s,%s", session_key, orig_domain);

    s = request->cb_domain_cookie;
    e = s + sizeof(request->cb_domain_cookie);
    s += sxprintf(s, e, "%s", domain_cookie_value);

    /* We write the domain cookie asynchronously, and the session synchronously */
    res = object_store_set(request->name,
            domain_cookie_value,
            session_key_with_domain,
            NULL,
            NULL,
            0,
            ostore_role_object_store);
    if (res && (res != ZPATH_RESULT_ASYNCHRONOUS)) {
        EXPORTER_LOG(AL_ERROR, "%s: Could not object_set domain cookie %.*s: %s", request->name, EXPORTER_DEBUG_BYTES, domain_cookie_value, zpath_result_string(res));
        exporter_session_free(session);
        return exporter_request_respond(request, HTTP_STATUS_BAD_REQUEST, EXPORTER_ERROR_CODE_DOMAIN_COOKIE_SET_FAILED);
    }

    /* Flush session to object store- set async state to control
     * where we come back. We absolutely want to complete our
     * write before resuming here */


    /* Do not write object store since POST payload needs to be processed */

    EXPORTER_LOG(AL_DEBUG, "[BFP_LOG] Canvas Writing new session_key: %s domain: %s",
            session_key, orig_domain);
    /* We are in function request_path_store_canvas_fingerprint_post */

    res = exporter_session_write(request->name,
            session,
            exporter_request_async_callback,
            request,
            0);
    exporter_session_free(session);

    if (res) {
        if (res == ZPATH_RESULT_ASYNCHRONOUS) {
            __sync_fetch_and_add_4(&(request->async_count), 1);
            EXPORTER_DEBUG_AUTH("%s: session key wrote asynchronously, async_count (%d->%d)", request->name,
                    request->async_count - 1, request->async_count);
            request->async_state = async_state_from_sp_wrote_session;
            return ZPATH_RESULT_NO_ERROR;
        } else {
            EXPORTER_LOG(AL_ERROR, "%s: Could not write session key: %s", request->name, zpath_result_string(res));
            return exporter_request_respond(request, HTTP_STATUS_BAD_REQUEST, EXPORTER_ERROR_CODE_SESSION_KEY_WRITE_FAILED);
        }
    } else {
        EXPORTER_LOG(AL_ERROR, "%s: Successfully wrote session key synchronously? Huh?", request->name);
        return exporter_request_respond(request, HTTP_STATUS_BAD_REQUEST, EXPORTER_ERROR_CODE_SESSION_KEY_WRITE_FAILED);
    }

    /* Not reachable */
    return exporter_request_respond(request, HTTP_STATUS_BAD_REQUEST, EXPORTER_ERROR_CODE_NOT_REACHABLE);
}

/* The request path store function shall be called in case of CSP feature is enabled
 * Any failure happening in this feature shall not impact normal flow i.e. the application
 * should be accessible. This function shall never return failure. In case of failure it logs it.
 */
static int request_path_store_canvas_fingerprint_doauth_body(struct exporter_request *request)
{
    int res = ZPATH_RESULT_NO_ERROR;
    int content_length = 0;
    char *data = NULL;

    request->is_csp_request = 1;

    if (request->request_body) {
        content_length = evbuffer_get_length(request->request_body);
    }

    EXPORTER_DEBUG_CSP("%s: setcspdoauthbody req_state: {%s,%s,%d%d,%s,%d,%d,%d}",
            request->name,
            request->log.url,
            exporter_request_input_state_get_str(request),
            request->http_request_complete, request->http_response_complete,
            exporter_request_async_state_get_str(request->async_state),
            request->async_count,
            request->request_body ? (int)evbuffer_get_length(request->request_body):-1,
            request->response_data ? (int)evbuffer_get_length(request->response_data): -1);

    if (request->input_state == input_state_drain_and_process_request_data) {

        if (request->async_state == async_state_csp_doauth_wrote_session) {

            EXPORTER_DEBUG_CSP("%s: setcspdoauthbody session csp written success state=drain content_len: [%d] data: [%.*s]",
                               request->name, content_length, content_length, data ? data : "nil");

            /* Reverify - memleak for body free here */
            return ZPATH_RESULT_NO_ERROR;
        }

        if (request->request_body) {
            content_length = evbuffer_get_length(request->request_body);
        }

        EXPORTER_DEBUG_CSP("%s: setcspdoauthbody content_len: [%d] data: [%.*s]",
                request->name, content_length, content_length, data ? data : "nil");

        if (content_length) {
            /*
               1. Get Session object data, because we have to consider config mask and profile id from session data
               2. Parse the received data and compare to find change mask
               3. if the fingerprint data was never sent earlier then we have to copy the session obj inside domain
               4. Store the domain specific finger print data which will be deleted once we send it in exporter logs
             */
            struct argo_object *csp_obj = NULL;
            res = exporter_get_csp_data(request->name, OBJECT_STORE_TYPE_FINGERPRINT,
                    request->cb_session_cookie, &csp_obj,
                    exporter_request_async_callback,
                    request,
                    0);

            if (res) {
                if (res == ZPATH_RESULT_ASYNCHRONOUS) {
                    /* BUG: We have already filled response in previous callback, hence this will not be processed
                     */
                    __sync_fetch_and_add_4(&(request->async_count), 1);
                    request->async_state = async_state_csp_doauth_get_session;
                    EXPORTER_DEBUG_CSP("%s: CSP_DATA Fetching csp_session_key async_count (%d->%d) res=%s",
                                       request->name, request->async_count - 1, request->async_count, zpath_result_string(res));
                    request->input_state = input_state_drain_and_process_request_data;
                    return ZPATH_RESULT_NO_ERROR;
                } else {
                    EXPORTER_LOG(AL_ERROR, "%s: CSP_DATA csp_session_key get error: %s",
                                 request->name, zpath_result_string(res));
                }
            } else {
                EXPORTER_DEBUG_CSP("%s: CSP_DATA Got csp_session_key", request->name);
                if (request->async_state == async_state_csp_doauth_wrote_domain) {

                    if (exporter_is_first_bfp(csp_obj)) {
                        // call to session object store
                        res = exporter_set_first_fp_sent_flag(request->name,
                                                              csp_obj,
                                                              exporter_request_async_callback,
                                                              request,
                                                              0);
                        /* That means session data was never sent we have to encode
                           session object also in master fp */
                        if (res) {
                            if (res == ZPATH_RESULT_ASYNCHRONOUS) {
                                __sync_fetch_and_add_4(&(request->async_count), 1);
                                request->async_state = async_state_csp_doauth_wrote_session;
                                EXPORTER_DEBUG_CSP("%s: CSP_DATA write csp_sess_key 1st fp change asyn async_count (%d->%d)",
                                                    request->name, request->async_count - 1, request->async_count);
                                request->input_state = input_state_drain_and_process_request_data;
                                exporter_csp_free(csp_obj);
                                return ZPATH_RESULT_NO_ERROR;
                            } else {
                                EXPORTER_LOG(AL_ERROR, "%s: CSP_DATA csp_sess_key 1st fp change get error: %s",
                                             request->name, zpath_result_string(res));
                            }
                        } else {
                            EXPORTER_LOG(AL_DEBUG,"%s: CSP_DATA successfully written csp_sess_key 1st fp change", request->name);
                        }
                    } else {
                        EXPORTER_LOG(AL_DEBUG,"%s: CSP_DATA nothing left to write, just return", request->name);
                    }
                } else {
                    uint8_t *decrypted_data = NULL;
                    int decrypted_data_len = 0;
                    data = (char *)evbuffer_pullup(request->request_body, content_length);

                    if (request->js_encryption_enabled) {
                        int ret_val = exporter_csp_decrypt_payload(request, data, content_length,
                                                                   &decrypted_data, &decrypted_data_len);
                        if (ret_val) {
                            if (ret_val == ZPATH_RESULT_ASYNCHRONOUS) {
                                exporter_csp_free(csp_obj);
                                return ZPATH_RESULT_NO_ERROR;
                            } else {
                                EXPORTER_LOG(AL_ERROR, "%s: setcspdoauthbody CSP_NONCE_PARSER decryption failed %s",
                                    request->name, zpath_result_string(ret_val));
                            }
                        } else {
                            data = (char *)decrypted_data;
                            content_length = decrypted_data_len;
                        }
                    }

                    if (ZPATH_RESULT_NO_ERROR == exporter_parse_csp_json_data(request,
                                                                              data,
                                                                              content_length,
                                                                              csp_obj,
                                                                              1,//since we are in doauth_body MON is already checked before we injected JS
                                                                              exporter_csp_get_cfg_mask(csp_obj),
                                                                              exporter_csp_get_profile_id(csp_obj))) {

                        exporter_compare_n_update_csp_data(csp_obj, request->csp_data);

                        uint8_t is_first = exporter_is_first_bfp(csp_obj);

                        /* If it is first bfp data to be sent then set the bfp status as update sent */
                        /* if the session object monitoring was off and no session fingerprint
                         * data is sent then dump the json object in session structure
                         */
                        res = exporter_write_domain_csp_data(request->name,
                                                             request->cb_domain_cookie,
                                                             0,//fill mbfp
                                                             request->csp_data,
                                                             (is_first)?csp_obj:NULL,
                                                             exporter_request_async_callback,
                                                             request,
                                                             0);

                        if (res) {
                            if (res == ZPATH_RESULT_ASYNCHRONOUS) {
                                __sync_fetch_and_add_4(&(request->async_count), 1);
                                request->async_state = async_state_csp_doauth_wrote_domain;
                                EXPORTER_LOG(AL_DEBUG, "%s: CSP_DATA write csp_dom_key asyn async_count (%d->%d) res=%s",
                                                    request->name, request->async_count - 1, request->async_count,
                                                    zpath_result_string(res));
                                request->input_state = input_state_drain_and_process_request_data;
                                exporter_csp_free(csp_obj);
                                if (decrypted_data) EXPORTER_FREE(decrypted_data);
                                return ZPATH_RESULT_NO_ERROR;
                            } else {
                                EXPORTER_LOG(AL_ERROR, "%s: CSP_DATA csp_dom_key get error: %s",
                                             request->name, zpath_result_string(res));
                            }
                        } else {
                            EXPORTER_LOG(AL_DEBUG,"%s: CSP_DATA successfully written csp_dom_key", request->name);
                        }
                    } else {
                        EXPORTER_LOG(AL_ERROR, "%s: CSP_DATA JSON parse failed", request->name);
                    }
                    if (decrypted_data) EXPORTER_FREE(decrypted_data);
                }
                exporter_csp_free(csp_obj);
            }// found csp session key
        }//content length
    } else {
        EXPORTER_DEBUG_CSP("%s: setcspdoauthbody state!=drain session not found", request->name);
    }

    return ZPATH_RESULT_NO_ERROR;
}

static int request_path_store_canvas_fingerprint_periodic(struct exporter_request *request) {
    return request_path_store_canvas_fingerprint_periodic_auth_domain(request);
}

static int request_path_store_canvas_fingerprint_periodic_auth_domain(struct exporter_request *request)
{
    char orig_url_query_value[EXPORTER_URL_MAX_ENCODE_SIZE];
    int64_t customer_gid;
    struct http_parser_url parser;
    int res;
    int content_length = 0;
    char *data = NULL;
    char session_crypto_value[200];

    request->is_csp_request = 1;

    if (request->request_body) {
        content_length = (int)evbuffer_get_length(request->request_body);
    }

    EXPORTER_DEBUG_CSP("%s: setcspcontinuous req_state: {%s,%s,%d%d,%s,%d,%d,%d}",
            request->name,
            request->log.url,
            exporter_request_input_state_get_str(request),
            request->http_request_complete, request->http_response_complete,
            exporter_request_async_state_get_str(request->async_state),
            request->async_count,
            request->request_body ? (int)evbuffer_get_length(request->request_body):-1,
            request->response_data ? (int)evbuffer_get_length(request->response_data): -1);

    if (request->input_state == input_state_drain_and_process_request_data) {
        if (request->request_body) {
            content_length = (int)evbuffer_get_length(request->request_body);
            data = (char *)evbuffer_pullup(request->request_body, content_length);
        }
        EXPORTER_DEBUG_CSP("%s: CSP POST state=drain content_len: [%d] data: [%.*s]",
                request->name, content_length, content_length, data ? data : "nil");

    } else {
        if (request->request_body) {
            content_length = (int)evbuffer_get_length(request->request_body);
            data = (char *)evbuffer_pullup(request->request_body, content_length);
        }
        EXPORTER_DEBUG_CSP("%s: CSP POST state!=drain content_len: [%d] data: [%.*s]",
                request->name, content_length, content_length, data ? data : "nil");
    }

    if (request->request_body) {
        content_length = (int)evbuffer_get_length(request->request_body);
        EXPORTER_DEBUG_CSP("%s: CSP POST state=req json_payload_len: [%d]", request->name, content_length);
    }

    // Get original customer gid from origurl query
    customer_gid = url_get_customer_gid(request, request->url, &(request->url_parser),
            EXPORTER_QUERY_NAME_ORIGINAL_URL, orig_url_query_value, sizeof(orig_url_query_value), &parser);
    if (customer_gid == 0) {
        EXPORTER_LOG(AL_ERROR, "%s: CSP POST Could not get customer_gid from original URL (malformed?)", request->name);
        return exporter_request_respond(request, HTTP_STATUS_BAD_REQUEST, EXPORTER_ERROR_CODE_CUSTOMER_GID_NOT_FOUND);
    }

    /* Verify that the request is to the auth host */
    if (!request->conn->exporter_domain->is_auth_domain) {
        EXPORTER_LOG(AL_ERROR, "%s: Received request for auth path that isn't to auth domain", request->name);
        return exporter_request_respond(request, HTTP_STATUS_BAD_REQUEST, EXPORTER_ERROR_CODE_INVALID_AUTH_DOMAIN);
    }

    /* This should be POST request only */
    if (request->req_method != HTTP_POST) {
        return exporter_request_respond(request, HTTP_STATUS_FORBIDDEN, EXPORTER_ERROR_CODE_NOT_POST_REQUEST);
    }

    res = find_cookie(request, EXPORTER_COOKIE_CRYPTO, 0, session_crypto_value, sizeof(session_crypto_value));
    if (res) {
        EXPORTER_LOG(AL_ERROR, "%s: No crypto key returning from SP", request->name);
        return exporter_request_respond(request, HTTP_STATUS_INTERNAL_SERVER_ERROR, EXPORTER_ERROR_CODE_CRYPTO_COOKIE_NOT_FOUND);
    }

    char session_cookie_value[EXPORTER_COOKIE_MAX_SIZE];

    /* Get session cookie. */
    res = find_cookie(request, EXPORTER_COOKIE_SESSION, 1, session_cookie_value, sizeof(session_cookie_value));
    if (res) {
        EXPORTER_DEBUG_AUTH("%s: No session cookie", request->name);
        return exporter_request_respond(request, HTTP_STATUS_INTERNAL_SERVER_ERROR, EXPORTER_ERROR_CODE_INVALID_SESSION);
    } else {
        res = request_authenticate_session_key(request, customer_gid, session_cookie_value, NULL, 0);
        if (res) {
            if (res == ZPATH_RESULT_ASYNCHRONOUS) {
                EXPORTER_DEBUG_AUTH("%s: Authenticating- asynchronous fetch", request->name);
                return ZPATH_RESULT_NO_ERROR;
            } else {
                /* Auth failed. */
                EXPORTER_DEBUG_AUTH("%s: Authentication fetch failed, re-authing", request->name);
                /* Fall through to re-auth */
            }
        } else {

            /* CSP_POLICY START*/
            const char *assertion_cookie = request->assertion_key;
            if (request->is_csp_enabled) {
                int ret = exporter_load_policy(request, assertion_cookie, session_crypto_value);
                if (ret) {
                    if (ret == ZPATH_RESULT_ASYNCHRONOUS) {
                        return ZPATH_RESULT_NO_ERROR;
                    } else if (ret == ZPN_RESULT_EXPIRED) {
                        /* Reauth code must have set redirection so we are returning */
                        return ZPATH_RESULT_NO_ERROR;
                    } else {
                        /* We are in setcspcontinuous - In douth we already read policy and status was monitored
                         * Here we only fetch policy to get policyid again since this is a
                         * different HTTP request
                         * This APP will always be monitored and on failure policy will show
                         * zero which is correct
                         * We will not check again if this APP needs to be monitored or not
                         */
                    }
                }
                /* We really dont use profile here, this can be removed */
                ret = exporter_read_csp_config(request);
                if (ret == ZPN_RESULT_ASYNCHRONOUS) {
                    return ZPATH_RESULT_NO_ERROR;
                }
            }//is_csp_enabled
            /* CSP_POLICY END */

            /* Authenticated! Domain clearly doesn't have cookie, so
             * create/set one, unless we are explicitly doing re-auth
             * at the behest of ZPA */
            char domain_cookie_value[EXPORTER_COOKIE_MAX_SIZE];

            res = query_string_find(request->url, &(request->url_parser), EXPORTER_QUERY_NAME_TO_DOMAIN_SET_COOKIE, domain_cookie_value, sizeof(domain_cookie_value));
            if (res) {
                /* No cookie found, check for ecookie(encrypted domain cookie), validate timestamp and decrypt*/
                res = exporter_decrypt_dom_cookie(request, domain_cookie_value, EXPORTER_URL_MAX_ENCODE_SIZE);
                if (res) {
                    EXPORTER_LOG(AL_ERROR, "%s: No %s: %s", request->name, EXPORTER_QUERY_NAME_TO_DOMAIN_SET_COOKIE, zpath_result_string(res));
                    return exporter_request_respond(request, HTTP_STATUS_FORBIDDEN, EXPORTER_ERROR_CODE_ECOOKIE_NOT_FOUND);
                }
            } else {
                exporter_url_decode(domain_cookie_value);
            }

            /* CSP_DATA - Store domain and session cookie */
            memset(request->cb_domain_cookie, 0, EXPORTER_COOKIE_MAX_SIZE);
            memcpy(request->cb_domain_cookie, domain_cookie_value, EXPORTER_COOKIE_MAX_SIZE);
            memset(request->cb_session_cookie, 0, EXPORTER_COOKIE_MAX_SIZE);
            memcpy(request->cb_session_cookie, session_cookie_value, EXPORTER_COOKIE_MAX_SIZE);

            exporter_request_respond(request, HTTP_STATUS_OK, EXPORTER_ERROR_CODE_NO_ERROR);

            request->input_state = input_state_drain_and_process_request_data;

            //Same as /fromsp if we get data in this state process it in body function
            if (request->request_body && evbuffer_get_length(request->request_body)) {
                EXPORTER_DEBUG_CSP("%s: setcspcontinuous drain data req_state: {%s,%s,%d%d,%s,%d,%d,%d}",
                        request->name,
                        request->log.url,
                        exporter_request_input_state_get_str(request),
                        request->http_request_complete, request->http_response_complete,
                        exporter_request_async_state_get_str(request->async_state),
                        request->async_count,
                        request->request_body ? (int)evbuffer_get_length(request->request_body):-1,
                        request->response_data ? (int)evbuffer_get_length(request->response_data): -1);

                /* We will parse data in body cb function */
                exporter_request_enter_state(request, async_state_csp_periodic_process_data);
            }//content length != 0
        }//authenticated
    }//session cookie found

    return ZPATH_RESULT_NO_ERROR;
}

static int request_path_store_canvas_fingerprint_doauth(struct exporter_request *request)
{
    char orig_url_query_value[EXPORTER_URL_MAX_ENCODE_SIZE];
    int64_t customer_gid;
    struct http_parser_url parser;
    int res;
    int content_length = 0;
    char *data = NULL;
    struct http_parser_url orig_url_parser;
    char session_crypto_value[200];
    char *s;
    char *e;

    request->is_csp_request = 1;

    if (request->request_body) {
        content_length = evbuffer_get_length(request->request_body);
    }

    EXPORTER_DEBUG_CSP("%s: setcspdoauth req_state: {%s,%s,%d%d,%s,%d,%d,%d}",
            request->name,
            request->log.url,
            exporter_request_input_state_get_str(request),
            request->http_request_complete, request->http_response_complete,
            exporter_request_async_state_get_str(request->async_state),
            request->async_count,
            request->request_body ? (int)evbuffer_get_length(request->request_body):-1,
            request->response_data ? (int)evbuffer_get_length(request->response_data): -1);

    if (request->input_state == input_state_drain_and_process_request_data) {
        if (request->request_body) {
            content_length = evbuffer_get_length(request->request_body);
            data = (char *)evbuffer_pullup(request->request_body, content_length);
        }
        EXPORTER_DEBUG_CSP("%s: CSP POST state=drain content_len: [%d] data: [%.*s]",
                request->name, content_length, content_length, data ? data : "nil");

    } else {
        if (request->request_body) {
            content_length = evbuffer_get_length(request->request_body);
            data = (char *)evbuffer_pullup(request->request_body, content_length);
        }
        EXPORTER_DEBUG_CSP("%s: CSP POST state!=drain content_len: [%d] data: [%.*s]",
                request->name, content_length, content_length, data ? data : "nil");
    }

    if (request->request_body) {
        content_length = evbuffer_get_length(request->request_body);
        EXPORTER_DEBUG_CSP("%s: CSP POST state=req json_payload_len: [%d]", request->name, content_length);
    }

    customer_gid = url_get_customer_gid(request, request->url, &(request->url_parser),
            EXPORTER_QUERY_NAME_ORIGINAL_URL, orig_url_query_value, sizeof(orig_url_query_value), &parser);
    if (customer_gid == 0) {
        EXPORTER_LOG(AL_ERROR, "%s: CSP POST Could not get customer_gid from original URL (malformed?)", request->name);
        return exporter_request_respond(request, HTTP_STATUS_BAD_REQUEST, EXPORTER_ERROR_CODE_CUSTOMER_GID_NOT_FOUND);
    }

    // call to create domain cookies

    /* Original code fromsp modified for devicefingerprint starts here */

    /* Verify that the request is to the auth host */
    if (!request->conn->exporter_domain->is_auth_domain) {
        EXPORTER_LOG(AL_ERROR, "%s: Received request for auth path that isn't to auth domain", request->name);
        return exporter_request_respond(request, HTTP_STATUS_BAD_REQUEST, EXPORTER_ERROR_CODE_INVALID_AUTH_DOMAIN);
    }

    /* This should be POST request only */
    if (request->req_method != HTTP_POST) {
        return exporter_request_respond(request, HTTP_STATUS_FORBIDDEN, EXPORTER_ERROR_CODE_NOT_POST_REQUEST);
    }

    /* Verify if we are coming from fromsp or doauth */
    char redirect_origurl[2048];
    struct http_parser_url redirect_origurl_parser;

    res = find_cookie(request, EXPORTER_COOKIE_CRYPTO, 0, session_crypto_value, sizeof(session_crypto_value));
    if (res) {
        EXPORTER_LOG(AL_ERROR, "%s: No crypto key returning from SP", request->name);
        return exporter_request_respond(request, HTTP_STATUS_INTERNAL_SERVER_ERROR, EXPORTER_ERROR_CODE_CRYPTO_COOKIE_NOT_FOUND);
    }

    int set_crypto_cookie_value = 0;
    char session_cookie_value[EXPORTER_COOKIE_MAX_SIZE];
    char set_cookie[EXPORTER_URL_MAX_ENCODE_SIZE];
    char set_cookie_legacy[EXPORTER_URL_MAX_ENCODE_SIZE];
    //arb_domain_feature_enabled = is_arbitrary_auth_domain_enabled(customer_gid);
    char ctoken_rx[EXPORTER_CORS_TOKEN_SIZE] = {0};

    res = query_string_find(request->url, &(request->url_parser),
            "doauthorigurl", redirect_origurl, sizeof(redirect_origurl));
    if (res == ZPATH_RESULT_NO_ERROR) {
        exporter_url_decode(redirect_origurl);
        EXPORTER_DEBUG_CSP("%s: CSP doauthorigurl: [%s]", request->name, redirect_origurl);
        http_parser_url_init(&redirect_origurl_parser);
        res = http_parser_parse_url(redirect_origurl, strlen(redirect_origurl), 0, &redirect_origurl_parser);
        if (res) {
            EXPORTER_LOG(AL_ERROR, "%s: Could not parse doauthorigurl", request->name);
            return 0;
        }
    }

    /* Delete nonce */
    char nonce_cookie_value[EXPORTER_COOKIE_MAX_SIZE];
    memset(nonce_cookie_value, 0, sizeof(nonce_cookie_value));
    res = query_string_find(redirect_origurl, &redirect_origurl_parser, "nonce", nonce_cookie_value, sizeof(nonce_cookie_value));
    if (res) {
        EXPORTER_LOG(AL_ERROR, "%s: csp nonce not found %s", request->name, zpath_result_string(res));
    } else {
        res = exporter_csp_delete_nonce(request, nonce_cookie_value);
        if (res == ZPATH_RESULT_ASYNCHRONOUS) {
            return ZPATH_RESULT_NO_ERROR;
        }
    }

    /* Get customer_gid and orig_url from query string */
    customer_gid = url_get_customer_gid(request, redirect_origurl,
            &redirect_origurl_parser, EXPORTER_QUERY_NAME_ORIGINAL_URL,
            orig_url_query_value, sizeof(orig_url_query_value), &orig_url_parser);
    if (!customer_gid) {
        /* Not so good... */
        EXPORTER_LOG(AL_ERROR, "%s: Received redirect from SP from which we could not extract customer_gid", request->name);
        return exporter_request_respond(request, HTTP_STATUS_BAD_REQUEST, EXPORTER_ERROR_CODE_CUSTOMER_GID_NOT_FOUND);
    }

    int64_t cors_found = url_get_cors_token(request, redirect_origurl,
            &redirect_origurl_parser, EXPORTER_CORS_TOKEN, ctoken_rx, sizeof(ctoken_rx)-1, &orig_url_parser);

    if (cors_found) {
        request->log.cors_token_status = get_cors_token_status_str(ZPA_CORS_TOKEN_VALID);
        if (validate_cors_token(ctoken_rx, request) != ZPATH_RESULT_NO_ERROR) {
            EXPORTER_LOG(AL_ERROR, "%s: invalid cors token", request->name);
            request->log.cors_token_status = get_cors_token_status_str(ZPA_CORS_TOKEN_INVALID);
            return exporter_request_respond(request, HTTP_STATUS_FORBIDDEN, EXPORTER_ERROR_CODE_INVALID_CORS_TOKEN);
        }
        request->cors_request_with_token = 1;
    }

    /* Get crypto value, and/or generate it. */
    res = find_cookie(request, EXPORTER_COOKIE_CRYPTO, 0, session_crypto_value, sizeof(session_crypto_value));
    if (res) {
        int64_t rbytes[4];
        EXPORTER_DEBUG_AUTH("%s: No crypto cookie, generating one.", request->name);
        if (RAND_bytes((unsigned char *)rbytes, sizeof(rbytes)) == 0) {
            /* Error generating random number */
            EXPORTER_LOG(AL_CRITICAL, "%s: Could not generate random bytes for crypto key", request->name);
            return exporter_request_respond(request, HTTP_STATUS_INTERNAL_SERVER_ERROR, EXPORTER_ERROR_CODE_CRYPTO_COOKIE_GENERATION_FAILED);
        }
        size_t len = base64_encoded_size(sizeof(rbytes));
        if ((len + 1) >= sizeof(session_crypto_value)) {
            EXPORTER_LOG(AL_CRITICAL, "%s: Crypto key too big", request->name);
            return exporter_request_respond(request, HTTP_STATUS_INTERNAL_SERVER_ERROR, EXPORTER_ERROR_CODE_CRYPTO_COOKIE_GENERATION_FAILED);
        }
        base64_encode_binary(session_crypto_value, (const unsigned char *)rbytes, sizeof(rbytes));

        /* Ideally we should set set_cookie to 0 and if non-zero use it */
        set_crypto_cookie_value = 1;
        make_cookie(EXPORTER_DOMAIN_AUTH, EXPORTER_COOKIE_CRYPTO, session_crypto_value,
                "None", EXPORTER_COOKIE_CRYPTO_LIFETIME_S, set_cookie, sizeof(set_cookie),
                set_cookie_legacy, sizeof(set_cookie_legacy));
    }

    /* Get session cookie. */
    res = find_cookie(request, EXPORTER_COOKIE_SESSION, 1, session_cookie_value, sizeof(session_cookie_value));
    if (res) {
        EXPORTER_DEBUG_AUTH("%s: No session cookie", request->name);
        /* Fall through to re-auth */
    } else {
        res = request_authenticate_session_key(request, customer_gid, session_cookie_value, NULL, 0);
        if (res) {
            if (res == ZPATH_RESULT_ASYNCHRONOUS) {
                EXPORTER_DEBUG_AUTH("%s: Authenticating- asynchronous fetch", request->name);
                return ZPATH_RESULT_NO_ERROR;
            } else {
                /* Auth failed. */
                EXPORTER_DEBUG_AUTH("%s: Authentication fetch failed, re-authing", request->name);
                /* Fall through to re-auth */
            }
        } else {

            /* CSP_POLICY START*/
            const char *assertion_cookie = request->assertion_key;
            if (request->is_csp_enabled) {
                int ret = exporter_load_policy(request, assertion_cookie, session_crypto_value);
                if (ret) {
                    if (ret == ZPATH_RESULT_ASYNCHRONOUS) {
                        return ZPATH_RESULT_NO_ERROR;
                    } else if (ret == ZPN_RESULT_EXPIRED) {
                        /* Reauth code must have set redirection so we are returning */
                        return ZPATH_RESULT_NO_ERROR;
                    } else {
                        /* We are in setcspdoauth - In douth we already read policy and status was monitored
                         * Here we only fetch policy to get policyid again since this is a
                         * different HTTP request
                         * This APP will always be monitored and on failure policy will show
                         * zero which is correct
                         * We will not check again if this APP needs to be monitored or not
                         */
                    }
                }
                /* We really dont use profile here, this can be removed */
                ret = exporter_read_csp_config(request);
                if (ret == ZPN_RESULT_ASYNCHRONOUS) {
                    return ZPATH_RESULT_NO_ERROR;
                }
            }//is_csp_enabled
            /* CSP_POLICY END */

            /* Authenticated! Domain clearly doesn't have cookie, so
             * create/set one, unless we are explicitly doing re-auth
             * at the behest of ZPA */
            char domain_cookie_value[EXPORTER_COOKIE_MAX_SIZE];
            char session_key_with_domain[EXPORTER_COOKIE_MAX_SIZE];
            char orig_domain[EXPORTER_URL_MAX_ENCODE_SIZE];

            res = query_string_find(redirect_origurl, &redirect_origurl_parser, EXPORTER_QUERY_NAME_REQUIRE_REAUTH, NULL, 0);
            if (res == ZPATH_RESULT_NO_ERROR) {
                /*
                 * Looks like the domain asked us to re-auth, so we will toss this customer
                 * Deauthentication, single company:
                 *
                 * 1. Redirect to authSP.
                 *
                 * 2. On return from authSP, will recognize that token
                 *    has changed. Use the opportunity to delete old
                 *    auth entry, rewrite session to a new ID, and
                 *    rewrite cookie to the new ID
                 */
                EXPORTER_DEBUG_AUTH("%s: Domain has requested reauthentication. Reprocessing URL. url = %s", request->name, request->url);
                /* Fall through to reauth. */
            } else {
                res = query_string_find(redirect_origurl, &redirect_origurl_parser, EXPORTER_QUERY_NAME_IDP_SELECT_DOMAIN, NULL, 0);
                if (res == ZPATH_RESULT_NO_ERROR) {
                    /*
                     * Looks like the query has a domain selected from
                     * challenge window for IDP selection, which means
                     * we are doing reauth. Thus we fall through to
                     * reauth, just like the case immediately above.
                     */
                    EXPORTER_DEBUG_AUTH("%s: Domain has requested reauthentication (IDP selec). Reprocessing URL. url = %s",
                            request->name, redirect_origurl);
                    /* Fall through to reauth. */
                } else {

                    /* We must not have had a domain cookie pointing at this
                     * session state, so redirect back to domain and set it up!
                     * For this we need a */
                    res = object_store_gen_key(OBJECT_STORE_TYPE_DOMAIN,
                            session_cookie_value, domain_cookie_value, sizeof(domain_cookie_value),
                            ostore_role_object_store);
                    if (res) {
                        EXPORTER_LOG(AL_ERROR, "Could not generate key");
                        return exporter_request_respond(request, HTTP_STATUS_INTERNAL_SERVER_ERROR, EXPORTER_ERROR_CODE_DOMAIN_COOKIE_GENERATION_FAILED);
                    }
                    /* Get original domain, without trailing :port */
                    snprintf(orig_domain, sizeof(orig_domain), "%.*s", orig_url_parser.field_data[UF_HOST].len,
                            &(orig_url_query_value[orig_url_parser.field_data[UF_HOST].off]));
                    /* Write cookie to object store including original domain */
                    s = session_key_with_domain;
                    e = s + sizeof(session_key_with_domain);
                    s += sxprintf(s, e, "%s,%s", session_cookie_value, orig_domain);

                    /* Install the cookie in the cookie store. We do this on
                     * the assumption that the install will beat the eventual
                     * query for it, which is very likely. */

                    /* CSP_DATA - Store domain and session cookie */
                    memset(request->cb_domain_cookie, 0, EXPORTER_COOKIE_MAX_SIZE);
                    memcpy(request->cb_domain_cookie, domain_cookie_value, EXPORTER_COOKIE_MAX_SIZE);
                    memset(request->cb_session_cookie, 0, EXPORTER_COOKIE_MAX_SIZE);
                    memcpy(request->cb_session_cookie, session_cookie_value, EXPORTER_COOKIE_MAX_SIZE);

                    res = object_store_set(request->name,
                            domain_cookie_value,
                            session_key_with_domain,
                            NULL,
                            NULL,
                            0,
                            ostore_role_object_store);

                    if (res && (res != ZPATH_RESULT_ASYNCHRONOUS)) {
                        /* Note: no async count change because we set no callback */
                        EXPORTER_LOG(AL_ERROR, "%s: Could not set cookie", request->name);
                        return exporter_request_respond(request, HTTP_STATUS_INTERNAL_SERVER_ERROR, EXPORTER_ERROR_CODE_DOMAIN_COOKIE_SET_FAILED);
                    }

                    char *DOM_COOKIE = EXPORTER_QUERY_NAME_TO_DOMAIN_SET_COOKIE; /* cookie */
                    char *url_domain_cookie = domain_cookie_value;
                    char encoded_ecookie_domain[EXPORTER_URL_MAX_ENCODE_SIZE] = {'\0'};
                    char is_ecookie = 0;
                    if (is_url_cookie_encryption_enabled(customer_gid)) {
                        /* encrypt domain cookie in the URL */
                        res = exporter_encrypt_dom_cookie(request, domain_cookie_value,
                                EXPORTER_URL_MAX_ENCODE_SIZE, encoded_ecookie_domain);
                        if (res) {
                            return exporter_request_respond(request, HTTP_STATUS_BAD_REQUEST, EXPORTER_ERROR_CODE_DOMAIN_COOKIE_ENCRYPTION_FAILED);
                        }
                        DOM_COOKIE = EXPORTER_QUERY_NAME_TO_DOMAIN_SET_ENCRYPTED_COOKIE; /* ecookie */
                        url_domain_cookie = encoded_ecookie_domain;
                        is_ecookie = 1;
                    }

                    (void)DOM_COOKIE;

                    /*
                     * Send redirect...
                     */
                    /* Redirect back to domain with this cookie for installation */

                    //encode cookies
                    char encoded_url_domain_cookie[2*EXPORTER_COOKIE_MAX_SIZE];
                    char encoded_session_crypto_value[2*EXPORTER_COOKIE_MAX_SIZE];

                    url_encode(url_domain_cookie, encoded_url_domain_cookie, sizeof(encoded_url_domain_cookie));
                    url_encode(session_crypto_value, encoded_session_crypto_value, sizeof(encoded_session_crypto_value));

                    char json_data[EXPORTER_URL_MAX_ENCODE_SIZE];

                    EXPORTER_DEBUG_CSP("%s: Create response url: [%s] domain: [%s] session: [%s] cookie: [%s] is_ecookie: %d",
                            request->name, request->log.url, orig_domain,
                            request->cb_session_cookie, url_domain_cookie, is_ecookie);

                    snprintf(json_data, sizeof(json_data),
                            "{\"%s\": \"%s\", \"cryptcookie\": \"%s\"}",
                            is_ecookie ? "ecookie" : "cookie", encoded_url_domain_cookie,
                            encoded_session_crypto_value);

                    res = exporter_request_respond_status_data_content_type_json(
                            request,
                            set_crypto_cookie_value ? set_cookie : NULL,
                            set_crypto_cookie_value ? set_cookie_legacy : NULL,
                            HTTP_STATUS_OK,
                            //HTTP_STATUS_INTERNAL_SERVER_ERROR,//uncomment for exception testing
                            json_data, strlen(json_data),
                            "application/json",
                            strlen(json_data),
                            0);

                    request->input_state = input_state_drain_and_process_request_data;

                    //Same as /fromsp if we get data in this state process it in body function
                    if (request->request_body && evbuffer_get_length(request->request_body)) {
                        EXPORTER_DEBUG_CSP("%s: setcspdoauth drain data req_state: {%s,%s,%d%d,%s,%d,%d,%d}",
                                request->name,
                                request->log.url,
                                exporter_request_input_state_get_str(request),
                                request->http_request_complete, request->http_response_complete,
                                exporter_request_async_state_get_str(request->async_state),
                                request->async_count,
                                request->request_body ? (int)evbuffer_get_length(request->request_body):-1,
                                request->response_data ? (int)evbuffer_get_length(request->response_data): -1);

                        /* We will parse data in body cb function */
                        exporter_request_enter_state(request, async_state_csp_doauth_process_data);
                    }//content length != 0
                }//generate domain cookie
            }//check idp domain
        }//authenticated
    }//authenticate

    return ZPATH_RESULT_NO_ERROR;
}

static int request_path_get_canvas_fingerprint_geo(struct exporter_request *request)
{
    char orig_url_query_value[EXPORTER_URL_MAX_ENCODE_SIZE];
    int64_t customer_gid;
    struct http_parser_url parser;

    EXPORTER_DEBUG_CSP("%s: CSP Get canvas fingerprint! url: [%s]", request->name, request->url);

    customer_gid = url_get_customer_gid(request, request->url,
            &(request->url_parser), EXPORTER_QUERY_NAME_ORIGINAL_URL, orig_url_query_value, sizeof(orig_url_query_value), &parser);
    if (customer_gid == 0) {
        EXPORTER_LOG(AL_ERROR, "%s: CSP Could not get customer_gid from original URL (malformed?)", request->name);
        return exporter_request_respond(request, HTTP_STATUS_BAD_REQUEST, EXPORTER_ERROR_CODE_CUSTOMER_GID_NOT_FOUND);
    }

    return exporter_request_respond_status_and_data_csp(request,
            HTTP_STATUS_OK,
            exporter_csp_g_html,
            exporter_csp_g_html_len,
            NULL);
}

static int request_path_get_canvas_fingerprint(struct exporter_request *request)
{
    char orig_url_query_value[EXPORTER_URL_MAX_ENCODE_SIZE];
    int64_t customer_gid;
    struct http_parser_url parser;

    EXPORTER_DEBUG_CSP("%s: CSP Get canvas fingerprint! url: [%s]", request->name, request->url);

    customer_gid = url_get_customer_gid(request, request->url,
            &(request->url_parser), EXPORTER_QUERY_NAME_ORIGINAL_URL, orig_url_query_value, sizeof(orig_url_query_value), &parser);
    if (customer_gid == 0) {
        EXPORTER_LOG(AL_ERROR, "%s: CSP Could not get customer_gid from original URL (malformed?)", request->name);
        return exporter_request_respond(request, HTTP_STATUS_BAD_REQUEST, EXPORTER_ERROR_CODE_CUSTOMER_GID_NOT_FOUND);
    }

    return exporter_request_respond_status_and_data_csp(request,
            HTTP_STATUS_OK,
            exporter_csp_html,
            exporter_csp_html_len,
            NULL);
}

static int request_path_get_canvas_fingerprint_enc(struct exporter_request *request)
{
    char orig_url_query_value[EXPORTER_URL_MAX_ENCODE_SIZE];
    int64_t customer_gid;
    struct http_parser_url parser;

    EXPORTER_DEBUG_CSP("%s: CSP ENC Get canvas fingerprint! url: [%s]", request->name, request->url);

    customer_gid = url_get_customer_gid(request, request->url,
                                        &(request->url_parser),
                                        EXPORTER_QUERY_NAME_ORIGINAL_URL,
                                        orig_url_query_value,
                                        sizeof(orig_url_query_value),
                                        &parser);
    if (customer_gid == 0) {
        EXPORTER_LOG(AL_ERROR, "%s: CSP Could not get customer_gid from original URL (malformed?)", request->name);
        return exporter_request_respond(request, HTTP_STATUS_BAD_REQUEST, EXPORTER_ERROR_CODE_CUSTOMER_GID_NOT_FOUND);
    }

    return exporter_request_respond_status_and_data_csp(request,
                                                        HTTP_STATUS_OK,
                                                        exporter_csp_e_html,
                                                        exporter_csp_e_html_len,
                                                        NULL);
}

static int request_path_get_canvas_fingerprint_geo_enc(struct exporter_request *request)
{
    char orig_url_query_value[EXPORTER_URL_MAX_ENCODE_SIZE];
    int64_t customer_gid;
    struct http_parser_url parser;

    EXPORTER_DEBUG_CSP("%s: CSP GEO ENC Get canvas fingerprint! url: [%s]", request->name, request->url);

    customer_gid = url_get_customer_gid(request, request->url,
                                        &(request->url_parser),
                                        EXPORTER_QUERY_NAME_ORIGINAL_URL,
                                        orig_url_query_value,
                                        sizeof(orig_url_query_value),
                                        &parser);
    if (customer_gid == 0) {
        EXPORTER_LOG(AL_ERROR, "%s: CSP Could not get customer_gid from original URL (malformed?)", request->name);
        return exporter_request_respond(request, HTTP_STATUS_BAD_REQUEST, EXPORTER_ERROR_CODE_CUSTOMER_GID_NOT_FOUND);
    }

    return exporter_request_respond_status_and_data_csp(request,
                                                        HTTP_STATUS_OK,
                                                        exporter_csp_ge_html,
                                                        exporter_csp_ge_html_len,
                                                        NULL);
}

/* Validate the domain in the session key matches the domain specified in the orig url */
static int validate_domain_in_origurl(const char *session_key_with_domain, char *orig_url, struct http_parser_url *orig_url_parser)
{
    const char *domain = NULL;
    char domain_buf[EXPORTER_URL_MAX_ENCODE_SIZE] = {'\0'};
    const char *walk = NULL;

    if (!session_key_with_domain || !orig_url || !orig_url_parser) {
        EXPORTER_LOG(AL_DEBUG, "Null input arguments found in validate_domain_in_origurl");
        return ZPATH_RESULT_ERR;
    }

    for (walk = session_key_with_domain; *walk; walk++) {
        if ((*walk) == ',') domain = walk + 1;
    }

    if (!domain) {
        EXPORTER_LOG(AL_DEBUG, "Parsing for domain in the session key failed");
        return ZPATH_RESULT_ERR;
    }

    snprintf(domain_buf, sizeof(domain_buf), "%s", domain);
    zpath_downcase(domain_buf);
    domain = domain_buf;

    if (strncasecmp(domain, &(orig_url[orig_url_parser->field_data[UF_HOST].off]), orig_url_parser->field_data[UF_HOST].len) != 0) {
        EXPORTER_LOG(AL_DEBUG, "String match of domain %s in session key with domain %.*s in original url failed",
                     domain, orig_url_parser->field_data[UF_HOST].len, &(orig_url[orig_url_parser->field_data[UF_HOST].off]));
        return ZPATH_RESULT_ERR;
    }
    return ZPATH_RESULT_NO_ERROR;
}

/*
 * In query string: cookie and original URL
 */
static int request_path_customer_domain_install_cookie(struct exporter_request *request)
{
    char orig_url_query_value[EXPORTER_URL_MAX_ENCODE_SIZE];
    char domain_cookie_value[EXPORTER_URL_MAX_ENCODE_SIZE];
    char crypto_cookie_value[EXPORTER_URL_MAX_ENCODE_SIZE];
    char set_cookie[EXPORTER_URL_MAX_ENCODE_SIZE];
    char set_cookie_legacy[EXPORTER_URL_MAX_ENCODE_SIZE];
    char set_cookie2[EXPORTER_URL_MAX_ENCODE_SIZE];
    char set_cookie2_legacy[EXPORTER_URL_MAX_ENCODE_SIZE];
    int res;
    int64_t customer_gid;
    struct http_parser_url parser;
    const char *session_key_with_domain = NULL;

    EXPORTER_DEBUG_AUTH("%s: Install domain cookie!", request->name);

    customer_gid = url_get_customer_gid(request, request->url, &(request->url_parser), EXPORTER_QUERY_NAME_ORIGINAL_URL, orig_url_query_value, sizeof(orig_url_query_value), &parser);
    if (customer_gid == 0) {
        EXPORTER_LOG(AL_ERROR, "%s: Could not get customer_gid from original URL (malformed?)", request->name);
        return exporter_request_respond(request, HTTP_STATUS_BAD_REQUEST, EXPORTER_ERROR_CODE_CUSTOMER_GID_NOT_FOUND);
    }
    char ctoken_rx[EXPORTER_CORS_TOKEN_SIZE] = {0};
    int64_t cors_found = url_get_cors_token(request, request->url, &(request->url_parser), EXPORTER_CORS_TOKEN, ctoken_rx, sizeof(ctoken_rx)-1, &parser);
    if (cors_found) {
        request->log.cors_token_status = get_cors_token_status_str(ZPA_CORS_TOKEN_VALID);
        if (validate_cors_token(ctoken_rx, request) != ZPATH_RESULT_NO_ERROR) {
            EXPORTER_LOG(AL_ERROR, "%s: Invalid cors token", request->name);
            request->log.cors_token_status = get_cors_token_status_str(ZPA_CORS_TOKEN_INVALID);
            return exporter_request_respond(request, HTTP_STATUS_FORBIDDEN, EXPORTER_ERROR_CODE_INVALID_CORS_TOKEN);
        }
        request->cors_request_with_token = 1;
    }

    if (request->req_method == HTTP_OPTIONS && request->cors_request_with_token) {
        EXPORTER_LOG(AL_ERROR, "%s: Received %s install domain request", request->name, http_method_names[request->req_method]);
        if (exporter_validate_no_auth_options_request(request) == ZPATH_RESULT_NO_ERROR) {
            return exporter_request_respond_no_content(request, HTTP_STATUS_NO_CONTENT, EXPORTER_ERROR_CODE_NO_ERROR);
        }
        return exporter_request_respond(request, HTTP_STATUS_FORBIDDEN, EXPORTER_ERROR_CODE_INVALID_OPTIONS_REQUEST);
    }

    res = query_string_find(request->url, &(request->url_parser), EXPORTER_QUERY_NAME_TO_DOMAIN_SET_CRYPTO_COOKIE, crypto_cookie_value, sizeof(crypto_cookie_value));
    if (res) {
        EXPORTER_LOG(AL_ERROR, "%s: No %s: %s", request->name, EXPORTER_QUERY_NAME_TO_DOMAIN_SET_CRYPTO_COOKIE, zpath_result_string(res));
        return exporter_request_respond(request, HTTP_STATUS_FORBIDDEN, EXPORTER_ERROR_CODE_CRYPTO_COOKIE_NOT_FOUND);
    }
    exporter_url_decode(crypto_cookie_value);

    res = query_string_find(request->url, &(request->url_parser), EXPORTER_QUERY_NAME_TO_DOMAIN_SET_COOKIE, domain_cookie_value, sizeof(domain_cookie_value));
    if (res) {
        /* No cookie found, check for ecookie(encrypted domain cookie), validate timestamp and decrypt*/
        res = exporter_decrypt_dom_cookie(request, domain_cookie_value, EXPORTER_URL_MAX_ENCODE_SIZE);
        if (res) {
            EXPORTER_LOG(AL_ERROR, "%s: No %s: %s", request->name, EXPORTER_QUERY_NAME_TO_DOMAIN_SET_COOKIE, zpath_result_string(res));
            return exporter_request_respond(request, HTTP_STATUS_FORBIDDEN, EXPORTER_ERROR_CODE_ECOOKIE_NOT_FOUND);
        }
    } else {
        exporter_url_decode(domain_cookie_value);
    }

    res = object_store_get(request->name,
                           domain_cookie_value,
                           &session_key_with_domain,
                           exporter_request_async_callback,
                           request,
                           0,
                           ostore_role_object_store);
    if (res == ZPATH_RESULT_ASYNCHRONOUS) {
        __sync_fetch_and_add_4(&(request->async_count), 1);
        EXPORTER_DEBUG_AUTH("%s: Fetching session key asynchronously - async_count (%d->%d)",
                            request->name, request->async_count - 1, request->async_count);
        return ZPATH_RESULT_NO_ERROR;
    } else if (res == ZPATH_RESULT_NOT_FOUND) {
         session_key_with_domain = NULL;
         EXPORTER_LOG(AL_ERROR, "%s: Failed to retrieve session key with domain from object store", request->name);
         return exporter_request_respond(request, HTTP_STATUS_FORBIDDEN, EXPORTER_ERROR_CODE_FAILED_TO_RETRIEVE_SESSION_KEY_FROM_OBJECT_STORE);
    } else if (res == ZPATH_RESULT_NO_ERROR) {
        /* setcdcookie should have correct domain in origurl */
        res = validate_domain_in_origurl(session_key_with_domain, orig_url_query_value, &parser);
        if (res) {
            EXPORTER_LOG(AL_ERROR, "%s: Failed to validate domain in origurl %s with session key in object store",
                         request->name, orig_url_query_value);
            return exporter_request_respond(request, HTTP_STATUS_FORBIDDEN, EXPORTER_ERROR_CODE_FAILED_TO_VALIDATE_DOMAIN_FROM_ORIGURL);
        }
    } else {
        EXPORTER_LOG(AL_ERROR, "%s: No Object stores available: err=%s object store ready count=%d",
                     request->name, zpath_result_string(res), object_store_ready_count(ostore_role_object_store));
        return exporter_request_respond(request, HTTP_STATUS_INTERNAL_SERVER_ERROR, EXPORTER_ERROR_CODE_OBJECT_STORES_UNAVAILABLE);
    }
    /*
     * Browsers now default the samesite cookie attribute to Lax, if it is not set explicitly by the application.
     * Cookies with samesite attribute set to Lax are not included in requests originating from XHR.
     * CORS applications require the samesite attribute set to None.
     * The original application domain can have samesite attribute set to Lax.
     */

    if (request->cors_request_with_token) {
        make_cookie(request->conn->sni, EXPORTER_COOKIE_DOMAIN, domain_cookie_value, "None", EXPORTER_COOKIE_DEFAULT_LIFETIME_S, set_cookie, sizeof(set_cookie), set_cookie_legacy, sizeof(set_cookie_legacy));
        make_cookie(request->conn->sni, EXPORTER_COOKIE_CRYPTO, crypto_cookie_value, "None", EXPORTER_COOKIE_DEFAULT_LIFETIME_S, set_cookie2, sizeof(set_cookie2), set_cookie2_legacy, sizeof(set_cookie2_legacy));
    } else {
        char* samesite = is_samesite_cookie_none(customer_gid) ? "None" : "Lax";

        make_cookie(request->conn->sni, EXPORTER_COOKIE_DOMAIN, domain_cookie_value, samesite, EXPORTER_COOKIE_DEFAULT_LIFETIME_S, set_cookie, sizeof(set_cookie), set_cookie_legacy, sizeof(set_cookie_legacy));
        make_cookie(request->conn->sni, EXPORTER_COOKIE_CRYPTO, crypto_cookie_value, samesite, EXPORTER_COOKIE_DEFAULT_LIFETIME_S, set_cookie2, sizeof(set_cookie2), set_cookie2_legacy, sizeof(set_cookie2_legacy));
    }

    return exporter_request_redirect_encode(request,
            set_cookie,
            set_cookie_legacy,
            set_cookie2,
            set_cookie2_legacy,
            orig_url_query_value,
            0,
            NULL,
            NULL);
}

static int request_path_canvas_fingerprint_js(struct exporter_request *request)
{
    return exporter_request_respond_js_data(request, exporter_csp_js, exporter_csp_js_len);
}

static int request_path_idp_query(struct exporter_request *request)
{
    return exporter_request_respond_js_data(request, exporter_idp_query_js, exporter_idp_query_js_len);
}

/********************************************************************************/


/*
 * lifetime = 0 is session cookie.
 */
static void make_cookie(const char *domain_name, const char *cookie_name, const char *cookie_value, const char* samesite_val, int64_t lifetime_s, char *str, size_t str_len, char *str_legacy, size_t str_legacy_len)
{
    static const char *weekdays[]={"Sun","Mon","Tue","Wed","Thu","Fri","Sat","Sun"};
    static const char *months[]={"Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec","Dec"};
    const time_t clk = epoch_s() + lifetime_s;
    struct tm tm;

    char cookie_name_legacy[256];
    char *s, *e;
    s = cookie_name_legacy;
    e = cookie_name_legacy + sizeof(cookie_name_legacy);
    s += sxprintf(s, e, "%s", cookie_name);
    s += sxprintf(s, e, "_legacy");
    if (lifetime_s) {
        gmtime_r(&clk, &tm);
        snprintf(str, str_len, "%s=%s; expires=%s %02d-%s-%04d %02d:%02d:%02d GMT; path=/; domain=%s; Secure; SameSite=%s; HttpOnly",
                 cookie_name, cookie_value,
                 weekdays[tm.tm_wday],
                 tm.tm_mday,
                 months[tm.tm_mon],
                 tm.tm_year+1900,
                 tm.tm_hour,
                 tm.tm_min,
                 tm.tm_sec,
                 domain_name,
                 samesite_val);

        snprintf(str_legacy, str_legacy_len, "%s=%s; expires=%s %02d-%s-%04d %02d:%02d:%02d GMT; path=/; domain=%s; Secure; HttpOnly",
                 cookie_name_legacy, cookie_value,
                 weekdays[tm.tm_wday],
                 tm.tm_mday,
                 months[tm.tm_mon],
                 tm.tm_year+1900,
                 tm.tm_hour,
                 tm.tm_min,
                 tm.tm_sec,
                 domain_name);
    } else {
        snprintf(str, str_len, "%s=%s; path=/; domain=%s; Secure; SameSite=%s; HttpOnly",
                 cookie_name, cookie_value,
                 domain_name,
                 samesite_val);
        snprintf(str_legacy, str_legacy_len, "%s=%s; path=/; domain=%s; Secure; HttpOnly",
                 cookie_name_legacy, cookie_value,
                 domain_name);
    }
}

int query_string_find(const char *url, struct http_parser_url *url_parser, const char *findme, char *value, size_t value_len)
{
    const char *query;
    const char *query_string_end;
    char *value_end;
    int findme_len;

    if (!(url_parser->field_set & (1 << UF_QUERY))) {
        return ZPATH_RESULT_NOT_FOUND;
    }

    query = &(url[url_parser->field_data[UF_QUERY].off]);
    query_string_end = query + url_parser->field_data[UF_QUERY].len;
    value_end = value + value_len;
    findme_len = strlen(findme);

    while (query < (query_string_end - findme_len)) {
        if (strncmp(findme, query, findme_len) == 0) {
            if (query + findme_len + 1 < query_string_end) {
                if (query[findme_len] == '=') {
                    /* Found it... just return success, and copy it out if they want the value. */
                    if (!value) return ZPATH_RESULT_NO_ERROR;
                    query += findme_len + 1;
                    while ((query < query_string_end) && ((*query) != '&') && (value < (value_end - 1))) {
                        *value = *query;
                        value++;
                        query++;
                    }
                    *value = 0;
                    return ZPATH_RESULT_NO_ERROR;
                }
            }
        }
        /* Search for '&'... */
        while ((query < query_string_end) && ((*query) != '&')) query++;
        if ((*query) == '&') query++;
    }
    return ZPATH_RESULT_NOT_FOUND;
}

#if 0
static int query_string_remove(struct exporter_request *request, char *url, struct http_parser_url *url_parser, const char *findme)
{
    char *query;
    char *query_value_start;
    char *query_string_end;
    char *w, *e;
    int findme_len;
    int res;

    if (!(url_parser->field_set & (1 << UF_QUERY))) {
        return ZPATH_RESULT_NOT_FOUND;
    }

    query = &(url[url_parser->field_data[UF_QUERY].off]);
    query_string_end = query + url_parser->field_data[UF_QUERY].len;
    findme_len = strlen(findme);

    while (query < (query_string_end - findme_len)) {
        if (strncmp(findme, query, findme_len) == 0) {
            /* Remember start of query... */
            query_value_start = query;
            if (query + findme_len + 1 < query_string_end) {
                if (query[findme_len] == '=') {
                    /* Found it... Now find the end of this query value- just find '&' or end of string */
                    query += findme_len + 1;
                    while ((query < query_string_end) && ((*query) != '&')) {
                        query++;
                    }
                    if (query == query_string_end) {
                        /* We remove the preceeding '?' or '&' */
                        query_value_start--;
                        if ((query_value_start < url) || (((*query_value_start) != '&') &&
                                                          ((*query_value_start) != '?'))) {
                            /* Bad format? */
                            return ZPATH_RESULT_NOT_FOUND;
                        }
                    } else if ((*query) == '&') {
                        /* We remove the trailing '&' as well... */
                        query++;
                    } else {
                        /* Bad format ? */
                        return ZPATH_RESULT_NOT_FOUND;
                    }
                    /* Remove characters from query_value_start to
                     * query... And don't overrun the end */
                    for (w = query, e = query_string_end; w < query_string_end; w++) {
                        if (e >= query_string_end) {
                            *w = '\0';
                        } else {
                            *w = *e;
                            w++;
                            e++;
                        }
                    }

                    /* Now re-parse the URL so that url_parser state
                     * is correct. url is always null terminated */
                    http_parser_url_init(url_parser);
                    res = http_parser_parse_url(url, strlen(url), request->req_method == HTTP_CONNECT ? 1 : 0, url_parser);

                    return res;
                }
            }
        }
        /* Search for '&'... */
        while ((query < query_string_end) && ((*query) != '&')) query++;
        if ((*query) == '&') query++;
    }
    return ZPATH_RESULT_NOT_FOUND;
}
#endif // 0




/*
 * If find_newest is set, then the cookie searched for is of the form
 * 'txt,txt,value' where value is base64 encoded, and the first 4
 * bytes of the value once decoded is the unsigned epoch of cookie
 * creation
 */
static int find_cookie_internal(struct exporter_request *request, const char *cookie_name, int find_newest, char *cookie_value, size_t cookie_len)
{
    const char *w;
    size_t i;
    int foundone = 0;
    uint32_t epoch = 0;
    uint32_t new_epoch = 0;
    char local_cookie[EXPORTER_URL_MAX_ENCODE_SIZE];
    size_t cookie_name_len = strlen(cookie_name);

    if (!request->header_cookie) {
        return ZPATH_RESULT_NOT_FOUND;
    }

    w = request->header_cookie;

    while (*w) {
        /* Skip whitespace */
        while ((*w) && (((*w) == ';') || isspace(*w))) w++;
        if (strncmp(w, cookie_name, cookie_name_len) == 0) {
            /* Cookie_Nameed cookie name */
            w += cookie_name_len;
            if ((*w) == '=') {
                i = 0;
                w++;
                if (epoch) {
                    /* save the old cookie we already read. This code
                     * only occurs for duplicate named cookies. */
                    snprintf(local_cookie, sizeof(local_cookie), "%s", cookie_value);
                }
                while ((i < (cookie_len - 1)) && (*w) && (!isspace(*w)) && ((*w) != ';')) {
                    cookie_value[i] = *w;
                    w++;
                    i++;
                }
                cookie_value[i] = 0;
                if (find_newest) {
                    foundone = 1;
                    if (!epoch) {
                        epoch = object_store_key_get_epoch(cookie_value);
                        EXPORTER_DEBUG_HTTP("%s: Found cookie for %s: %.*s with timestamp %ld", request->name, cookie_name, EXPORTER_DEBUG_BYTES, cookie_value, (long) epoch);
                    } else {
                        new_epoch = object_store_key_get_epoch(cookie_value);
                        if (new_epoch && (new_epoch < epoch)) {
                            EXPORTER_DEBUG_HTTP("%s: Found cookie for %s: %.*s with timestamp %ld, is newer than timestamp %ld", request->name, cookie_name, EXPORTER_DEBUG_BYTES, cookie_value, (long) new_epoch, (long) epoch);
                            snprintf(cookie_value, cookie_len, "%s", local_cookie);
                            epoch = new_epoch;
                        } else {
                            EXPORTER_DEBUG_HTTP("%s: Found cookie for %s: %.*s with timestamp %ld, is older than timestamp %ld", request->name, cookie_name, EXPORTER_DEBUG_BYTES, cookie_value, (long) new_epoch, (long) epoch);
                        }
                    }
                } else {
                    /* Only looking for one... */
                    EXPORTER_DEBUG_HTTP("%s: Found cookie for %s: %.*s", request->name, cookie_name, EXPORTER_DEBUG_BYTES, cookie_value);
                    return ZPATH_RESULT_NO_ERROR;
                }
            }
        }
        while ((*w) && ((*w) != ';')) w++;
    }
    if (foundone) {
        return ZPATH_RESULT_NO_ERROR;
    } else {
        return ZPATH_RESULT_NOT_FOUND;
    }
}

static int find_cookie(struct exporter_request *request, const char *cookie_name, int find_newest, char *cookie_value, size_t cookie_len)
{
    int ret;
    ret = find_cookie_internal(request, cookie_name, find_newest, cookie_value, cookie_len);
    if (ret == ZPATH_RESULT_NO_ERROR) {
        return ZPATH_RESULT_NO_ERROR;
    }
    char cookie_name_legacy[256];
    char *s, *e;
    s = cookie_name_legacy;
    e = cookie_name_legacy + sizeof(cookie_name_legacy);
    s += sxprintf(s, e, "%s",cookie_name);
    s += sxprintf(s, e, "_legacy");
    ret = find_cookie_internal(request, cookie_name_legacy, find_newest, cookie_value, cookie_len);
    return ret;
}

int exporter_request_find_cookie(void *request_void, const char *cookie_name, int find_newest, char *cookie_value, size_t cookie_len)
{
    struct exporter_request *request = (struct exporter_request *)request_void;

    return find_cookie(request, cookie_name, find_newest, cookie_value, cookie_len);
}


void exporter_http_encode(const char *str, char *out_str, size_t out_str_len)
{
    char *s, *e;

    s = out_str;
    e = s + out_str_len;
    *out_str = 0;
    while ((*str) && (s < (e - 1))) {
        unsigned char ix = *str;
        if (xlate_http_text[ix]) {
            s += sxprintf(s, e, "%s", xlate_http_text[ix]);
        } else {
            *s = ix;
            s++;
            *s = 0;
        }
        str++;
    }
}

/* Always shrinks. */
void exporter_http_decode(char *str)
{
    char *r, *w;
    size_t i;

    r = str;
    w = str;

    while (*r) {
        if ((*r) == '&') {
            for (i = 0; i < (sizeof(xlate_http_struct) / sizeof(xlate_http_struct[0])); i++) {
                if (strncmp(r, xlate_http_struct[i].str, strlen(xlate_http_struct[i].str)) == 0) {
                    *w = xlate_http_struct[i].c;
                    r += strlen(xlate_http_struct[i].str);
                    w++;
                    break;
                }
            }
            if (i == (sizeof(xlate_http_struct) / sizeof(xlate_http_struct[0]))) {
                (*w) = (*r);
                w++;
                r++;
            }
        } else {
            (*w) = (*r);
            w++;
            r++;
        }
    }
    (*w) = (*r);
}

int exporter_url_decode(char *str)
{
    static const char xlate[256] =
        {
            ['0'] = 0,
            ['1'] = 1,
            ['2'] = 2,
            ['3'] = 3,
            ['4'] = 4,
            ['5'] = 5,
            ['6'] = 6,
            ['7'] = 7,
            ['8'] = 8,
            ['9'] = 9,
            ['A'] = 10,
            ['B'] = 11,
            ['C'] = 12,
            ['D'] = 13,
            ['E'] = 14,
            ['F'] = 15,
            ['a'] = 10,
            ['b'] = 11,
            ['c'] = 12,
            ['d'] = 13,
            ['e'] = 14,
            ['f'] = 15,
        };
    static const unsigned char valid[256] =
        {
            ['0'] = 1,
            ['1'] = 1,
            ['2'] = 1,
            ['3'] = 1,
            ['4'] = 1,
            ['5'] = 1,
            ['6'] = 1,
            ['7'] = 1,
            ['8'] = 1,
            ['9'] = 1,
            ['A'] = 1,
            ['B'] = 1,
            ['C'] = 1,
            ['D'] = 1,
            ['E'] = 1,
            ['F'] = 1,
            ['a'] = 1,
            ['b'] = 1,
            ['c'] = 1,
            ['d'] = 1,
            ['e'] = 1,
            ['f'] = 1,
        };

    char *rd = str;
    char *wr = str;

    while (*rd) {
        if (*rd == '%') {
            if (rd[1] == '%') {
                *wr = '%';
                rd+=2;
                wr++;
            } else {
                if (!valid[(unsigned char)rd[1]] ||
                    !valid[(unsigned char)rd[2]]) {
                    *wr = *rd;
                    wr++;
                    rd++;
                } else {
                    *wr = (xlate[(unsigned char)rd[1]] << 4) | (xlate[(unsigned char)rd[2]]);
                    rd += 3;
                    wr ++;
                }
            }
        } else if (*rd == '+') {
            *wr = ' ';
            wr++;
            rd++;
        } else {
            *wr = *rd;
            wr++;
            rd++;
        }
    }
    *wr = 0;
    return ZPATH_RESULT_NO_ERROR;
}

/* for b64, needs minimum of 4*(n/3) and padded to 4 byte boundary (used 16 here just to be sure) */
#define MAX_REQUEST_NAME_LEN (256)
#define MAX_REQUEST_NAME_LEN_B64 (4 * MAX_REQUEST_NAME_LEN / 3 + 16)
void exporter_request_print_standard_response_headers(struct exporter_request *request, enum http_status status, char* buf, size_t buf_size) {
    static const char* hdr_fmt =
            "HTTP/1.1 %d %s\r\n"
            "Date: %s\r\n"
            "Expires: 0\r\n"
            "Server: " EXPORTER_SERVER_NAME "\r\n"
            "X-ZS-BA-Gid: %lld\r\n"
            "X-ZS-BA-Request-Id: %s\r\n";

    char request_name_b64[MAX_REQUEST_NAME_LEN_B64];
    char* request_name = request->name ? request->name : "unknown";
    int request_name_len = strlen(request_name);
    char date_str[128];
    struct tm ts = { 0 };
    time_t now = time(0);
    gmtime_r(&now, &ts);
    strftime(date_str, sizeof(date_str), "%a, %d %b %Y %H:%M:%S %Z", &ts);

    /* unified-portal - We have to set it here again even for internal app
     * If we get response from server but later we prefer to send our response due to some handling
     * Then we don't have to process it
     *
     * Also for setcdcookie it will be managed ba and we don't have to process response since
     * exporter is handling that endpoint
     */
    request->http_edit_response_from_server = 0;

    /* do b64 encoding to avoid strange characters in headers */
    base64_encode_binary(request_name_b64, (const unsigned char*)request_name,
                        request_name_len > MAX_REQUEST_NAME_LEN ? MAX_REQUEST_NAME_LEN : request_name_len);

    snprintf(buf, buf_size, hdr_fmt,
            status,
            http_status_names[status] ? http_status_names[status] : "unknown",
            date_str,
            zpath_instance_global_state.current_config ? zpath_instance_global_state.current_config->gid : 0,
            request_name_b64);
}

int exporter_request_respond_no_content(struct exporter_request *request, enum http_status status, enum exporter_error_codes error)
{
    char std_hdrs[EXPORTER_STANDARD_RESPONSE_HEADER_BUF_LEN];
    exporter_request_print_standard_response_headers(request, status, std_hdrs, sizeof(std_hdrs));

    EXPORTER_DEBUG_HTTP("%s: Returning status %d", request->name, status);

    request->log.response_status = status;

    if (!request->response_data) {
        request->response_data = evbuffer_new();
    }
    if (request->conn->exporter_domain && request->conn->exporter_domain->is_auth_domain) {
        const char *hdrs = "X-Content-Type-Options: nosniff\r\n"
                           "Cache-Control: no-cache, no-store, max-age=0, must-revalidate\r\n"
                           "X-XSS-Protection: 1; mode = block\r\n";
        strncat (std_hdrs, hdrs, EXPORTER_STANDARD_RESPONSE_HEADER_BUF_LEN - strlen(std_hdrs));
    }
    if (IS_UNIFIED_PORTAL(request)) {
    evbuffer_add_printf(request->response_data,
                        "%s"
                        "Content-Length: %d\r\n"
                        "Access-Control-Allow-Origin: %s\r\n"
                        "Access-Control-Allow-Methods: %s\r\n"
                        "Access-Control-Allow-Headers: %s\r\n"
                        "Access-Control-Allow-Credentials: true\r\n"
                        "Connection: Keep-Alive\r\n"
                        "Exporter-Error-Code: %d\r\n"
                        "Strict-Transport-Security: max-age=" MAX_AGE_HSTS "; includeSubDomains; preload\r\n"
                        EXPORTER_RESTRICTIVE_CSP_UNIFIED_PORTAL
                        "\r\n",
                        std_hdrs,
                        0, request->header_origin,
                        request->header_acrm, request->header_acrh, error, request->conn->sni, ZPATH_LOCAL_CLOUD_NAME,
                        EXPORTER_DOMAIN_UNIFIED_PORTAL_SUFFIX_UP,
                        request->conn->exporter_domain->user_portal_host[0] ? "https://" : "",
                        request->conn->exporter_domain->user_portal_host[0] ? request->conn->exporter_domain->user_portal_host : "",
                        EXPORTER_DOMAIN_AUTH);
    } else {
      evbuffer_add_printf(request->response_data,
                        "%s"
                        "Content-Length: %d\r\n"
                        "Access-Control-Allow-Origin: %s\r\n"
                        "Access-Control-Allow-Methods: %s\r\n"
                        "Access-Control-Allow-Headers: %s\r\n"
                        "Access-Control-Allow-Credentials: true\r\n"
                        "Connection: Keep-Alive\r\n"
                        "Exporter-Error-Code: %d\r\n"
                        "Strict-Transport-Security: max-age=" MAX_AGE_HSTS "; includeSubDomains; preload\r\n"
                        EXPORTER_RESTRICTIVE_CSP
                        "\r\n",
                        std_hdrs,
                        0, request->header_origin,
                        request->header_acrm, request->header_acrh, error, request->conn->sni, ZPATH_LOCAL_CLOUD_NAME);
   }

    request->input_state = input_state_drain;
    request->http_response_complete = 1;

    return ZPATH_RESULT_NO_ERROR;
}

static int exporter_request_respond_acao_status_and_data(struct exporter_request *request, enum http_status status, enum exporter_error_codes error, void *data, size_t data_len)
{
    char std_hdrs[EXPORTER_STANDARD_RESPONSE_HEADER_BUF_LEN];

    exporter_request_print_standard_response_headers(request, status, std_hdrs, sizeof(std_hdrs));

    if (!request->response_data) {
        request->response_data = evbuffer_new();
    }

    if (request->conn->exporter_domain && request->conn->exporter_domain->is_auth_domain) {
        const char *hdrs = "X-Content-Type-Options: nosniff\r\n"
                           "Cache-Control: no-cache, no-store, max-age=0, must-revalidate\r\n"
                           "X-XSS-Protection: 1; mode = block\r\n";
        strncat (std_hdrs, hdrs, EXPORTER_STANDARD_RESPONSE_HEADER_BUF_LEN - strlen(std_hdrs));
    }

    EXPORTER_DEBUG_HTTP("%s: Returning status %d, buffer length = %ld", request->name, status, (long) data_len);

    request->log.response_status = status;

    if (IS_UNIFIED_PORTAL(request)) {
    evbuffer_add_printf(request->response_data,
                        "%s"
                        "Content-Length: %d\r\n"
                        "Access-Control-Allow-Origin: %s\r\n"
                        "Access-Control-Allow-Methods: %s\r\n"
                        "Access-Control-Allow-Headers: %s\r\n"
                        "Access-Control-Allow-Credentials: true\r\n"
                        "Content-Type: text/html\r\n"
                        "Connection: Keep-Alive\r\n"
                        "Exporter-Error-Code: %d\r\n"
                        "Strict-Transport-Security: max-age=" MAX_AGE_HSTS "; includeSubDomains; preload\r\n"
                        EXPORTER_RESTRICTIVE_CSP_UNIFIED_PORTAL
                        "\r\n",
                        std_hdrs,
                        (int) data_len, request->header_origin,
                        request->header_acrm, request->header_acrh, error, request->conn->sni, ZPATH_LOCAL_CLOUD_NAME,
                        EXPORTER_DOMAIN_UNIFIED_PORTAL_SUFFIX_UP,
                        request->conn->exporter_domain->user_portal_host[0] ? "https://" : "",
                        request->conn->exporter_domain->user_portal_host[0] ? request->conn->exporter_domain->user_portal_host : "",
                        EXPORTER_DOMAIN_AUTH);
    } else {
        evbuffer_add_printf(request->response_data,
                        "%s"
                        "Content-Length: %d\r\n"
                        "Access-Control-Allow-Origin: %s\r\n"
                        "Access-Control-Allow-Methods: %s\r\n"
                        "Access-Control-Allow-Headers: %s\r\n"
                        "Access-Control-Allow-Credentials: true\r\n"
                        "Content-Type: text/html\r\n"
                        "Connection: Keep-Alive\r\n"
                        "Exporter-Error-Code: %d\r\n"
                        "Strict-Transport-Security: max-age=" MAX_AGE_HSTS "; includeSubDomains; preload\r\n"
                        EXPORTER_RESTRICTIVE_CSP
                        "\r\n",
                        std_hdrs,
                        (int) data_len, request->header_origin,
                        request->header_acrm, request->header_acrh, error, request->conn->sni, ZPATH_LOCAL_CLOUD_NAME);
    }

    evbuffer_add(request->response_data, data, data_len);

    request->input_state = input_state_drain;
    request->http_response_complete = 1;

    return ZPATH_RESULT_NO_ERROR;
}

/* for b64, needs minimum of 4*(n/3) and padded to 4 byte boundary (used 16 here just to be sure) */
#define MAX_REQUEST_NAME_LEN (256)
#define MAX_REQUEST_NAME_LEN_B64 (4 * MAX_REQUEST_NAME_LEN / 3 + 16)
int exporter_request_respond_with_upgrade_error(struct exporter_request *request, enum http_status status)
{
    char request_name_b64[MAX_REQUEST_NAME_LEN_B64];
    char* request_name = request->name ? request->name : "unknown";
    size_t request_name_len = strlen(request_name);

    request->log.response_status = status;
    /* do b64 encoding to avoid strange characters in headers */
    base64_encode_binary(request_name_b64, (const unsigned char*)request_name,
                        request_name_len > MAX_REQUEST_NAME_LEN ? MAX_REQUEST_NAME_LEN : request_name_len);

   const char *fmt =
            "HTTP/1.1 %d %s\r\n"
            "Server: " EXPORTER_SERVER_NAME "\r\n"
            "Date: %s\r\n"
            "Access-Control-Allow-Headers: Origin, X-Requested-With, Content-Type, Accept, Authorization\r\n"
            "Access-Control-Allow-Methods: GET, OPTIONS\r\n"
            "Access-Control-Allow-Origin: https://%s\r\n"
            "Strict-Transport-Security: max-age=" MAX_AGE_HSTS "; includeSubDomains; preload\r\n"
            "Cache-Control: no-cache, no-store, max-age=0, must-revalidate\r\n"
            "Pragma: no-cache\r\n"
            "Expires: 0\r\n"
            "X-Content-Type-Options: nosniff\r\n"
            "X-Frame-Options: DENY\r\n"
            "X-XSS-Protection: 1; mode = block\r\n"
            "X-ZS-BA-Gid: %lld\r\n"
            "X-ZS-BA-Request-Id: %s\r\n"
            "\r\n";
    char tm_buf[128] = "";
    char *domain = EXPORTER_GET_REQUEST_DOMAIN(request);

    /* unified-portal - We have to set it here again even for internal app
     * If we get response from server but later we prefer to send our response due to some handling
     * Then we don't have to process it
     */
    request->http_edit_response_from_server = 0;

    size_t domain_len = 0;
    if (domain != NULL){
        domain_len = strlen(domain);
    }

    time_t now = time(0);
    struct tm ts = { 0 };

    gmtime_r(&now, &ts);
    strftime(tm_buf, sizeof(tm_buf), "%a, %d %b %Y %H:%M:%S %Z", &ts);

    char http_headers_buf[strlen(fmt) + 3 + 512
            + domain_len + strlen(tm_buf) + MAX_REQUEST_NAME_LEN_B64 + sizeof(int64_t)];
    http_headers_buf[0] = '\0';

    snprintf(http_headers_buf, sizeof(http_headers_buf), fmt, status, http_status_names[status] ? http_status_names[status] : "unknown", tm_buf, domain, zpath_instance_global_state.current_config ? zpath_instance_global_state.current_config->gid : 0,
            request_name_b64);

    EXPORTER_DEBUG_GUAC_API_DETAIL("%s: Sending response headers and status: %s", request->name, http_status_names[status] ? http_status_names[status] : "unknown");

    if (!request->response_data) {
        request->response_data = evbuffer_new();
    }
    evbuffer_prepend(request->response_data, http_headers_buf, strlen(http_headers_buf));

    return ZPATH_RESULT_NO_ERROR;
}

int exporter_request_respond(struct exporter_request *request, enum http_status status, enum exporter_error_codes error)
{
    char std_hdrs[EXPORTER_STANDARD_RESPONSE_HEADER_BUF_LEN];
    exporter_request_print_standard_response_headers(request, status, std_hdrs, sizeof(std_hdrs));

    static char *temp_body="<html><body><h3>Result = %d %s</h3></body></html>\n";
    char body_buf[2000];
    const char *hdrs = "X-Content-Type-Options: nosniff\r\n"
                        "Cache-Control: no-cache, no-store, max-age=0, must-revalidate\r\n"
                        "X-XSS-Protection: 1; mode = block\r\n";

    EXPORTER_DEBUG_HTTP("%s: Returning status %d", request->name, status);

    request->log.response_status = status;

    if (!request->response_data) {
        request->response_data = evbuffer_new();
    }

    if (request->conn->exporter_domain && request->conn->exporter_domain->is_auth_domain) {
        strncat (std_hdrs, hdrs, EXPORTER_STANDARD_RESPONSE_HEADER_BUF_LEN - strlen(std_hdrs));
    }

    snprintf(body_buf, sizeof(body_buf), temp_body, status, http_status_names[status] ? http_status_names[status] : "unknown");

    if (IS_UNIFIED_PORTAL(request)) {
    evbuffer_add_printf(request->response_data,
                        "%s"
                        "Content-Length: %d\r\n"
                        "Content-Type: text/html\r\n"
                        "Connection: Keep-Alive\r\n"
                        "Exporter-Error-Code: %d\r\n"
                        "Strict-Transport-Security: max-age=" MAX_AGE_HSTS "; includeSubDomains; preload\r\n"
                        EXPORTER_RESTRICTIVE_CSP_UNIFIED_PORTAL
                        "\r\n%s",
                        std_hdrs,
                        (int)strnlen(body_buf, (sizeof(body_buf)-1)),
                        error,
                        request->conn->sni,
                        ZPATH_LOCAL_CLOUD_NAME,
                        EXPORTER_DOMAIN_UNIFIED_PORTAL_SUFFIX_UP,
                        request->conn->exporter_domain->user_portal_host[0] ? "https://" : "",
                        request->conn->exporter_domain->user_portal_host[0] ? request->conn->exporter_domain->user_portal_host : "",
                        EXPORTER_DOMAIN_AUTH,
                        body_buf);
    } else {
        evbuffer_add_printf(request->response_data,
                        "%s"
                        "Content-Length: %d\r\n"
                        "Content-Type: text/html\r\n"
                        "Connection: Keep-Alive\r\n"
                        "Exporter-Error-Code: %d\r\n"
                        "Strict-Transport-Security: max-age=" MAX_AGE_HSTS "; includeSubDomains; preload\r\n"
                        EXPORTER_RESTRICTIVE_CSP
                        "\r\n%s",
                        std_hdrs,
                        (int)strlen(body_buf),
                        error,
                        request->conn->sni,
                        ZPATH_LOCAL_CLOUD_NAME,
                        body_buf);
    }

    request->input_state = input_state_drain;
    request->http_response_complete = 1;

    return ZPATH_RESULT_NO_ERROR;
}

int exporter_request_respond_with_text(struct exporter_request *request, enum http_status status, enum exporter_error_codes error, char *format_str, ...)
{
    char body_buf[2000];
    va_list vl;

    va_start(vl, format_str);
    vsnprintf(body_buf, sizeof(body_buf), format_str, vl);
    va_end(vl);

    return exporter_request_respond_with_csv_error(request, status, error, NULL, body_buf);
}

/*
 * result is of the form "STRING1[,STRING2[,STRING3[,STRING4]]]"
 *
 * Where STRING1 = Generic reason for the result. (i.e. "could not connect")
 *       STRING2 = More specific reason. (i.e. TLS failed)
 *       STRING3 = More specific reason. (i.e. certificate expired)
 *       STRING4 = More specific reason/details. (i.e. details of certificate)
 *
 * If any of the strings is zero length, it is simply not displayed.
 *
 * The output format is:
 *
 * Heading 1: HTTP status
 * Heading 2: All errors from CSV except the last.
 * Paragraph: The last from CSV
 */
static int exporter_request_respond_with_csv_error(struct exporter_request *request,
                                                   enum http_status status,
                                                   enum exporter_error_codes error,
                                                   const char *base_error_str,
                                                   const char *csv_result)
{
    #define MAX_STR_COUNT 4
    char body_buf[32000];
    memset(body_buf, 0, sizeof(body_buf));
    char csv_strings_encoded[10000];
    char csv_strings[10000];
    char status_num[10];

    char *w2;
    char *c;
    int n_str = 0;
    char *str[MAX_STR_COUNT];
    char debugstring[10200];
    char timestr[ARGO_LOG_GEN_TIME_STR_LEN];
    char str0[400];

    char std_hdrs[EXPORTER_STANDARD_RESPONSE_HEADER_BUF_LEN];
    exporter_request_print_standard_response_headers(request, status, std_hdrs, sizeof(std_hdrs));

    snprintf(csv_strings_encoded, sizeof(csv_strings_encoded), "%s", csv_result);
    exporter_url_decode(csv_strings_encoded);
    exporter_http_decode(csv_strings_encoded);

    exporter_http_encode(csv_strings_encoded, csv_strings, sizeof(csv_strings));

    argo_log_gen_time(epoch_us(), timestr, sizeof(timestr), 0, 1);




    /* Seperate by commas */
    w2 = csv_strings;
    do {
        str[n_str] = w2;
        if (n_str < (MAX_STR_COUNT - 1)) {
            c = strchr(w2, ',');
            if (!c) {
                n_str++;
                break;
            }
            *c = 0;
            w2 = c + 1;
        }
        n_str++;
    } while (n_str < MAX_STR_COUNT);

    /* First string needs to be NOT HTTP encoded... Pull it off the handy non-encoded string we have... */
    str[0] = csv_strings_encoded;
    if ((c = strchr(csv_strings_encoded, ','))) {
        *c = 0;
    }

    EXPORTER_DEBUG_HTTP("%s: Returning status %d, base string = %s, csv_result = %s, str1=%s, str2=%s, str3=%s str4=%s",
                        request->name, status,
                        base_error_str,
                        csv_result,
                        n_str > 0 ? str[0] : "N/A",
                        n_str > 1 ? str[1] : "N/A",
                        n_str > 2 ? str[2] : "N/A",
                        n_str > 3 ? str[3] : "N/A");

    snprintf(status_num, sizeof(status_num), "%d", status);

    if (n_str == 0) {
        snprintf(debugstring, sizeof(debugstring), "%s %s %ld", timestr, request->log.mtunnel_id ? request->log.mtunnel_id : "", (long) (ZPATH_INSTANCE_GID%10000));
        string_replace(eun_sub_txt, body_buf, sizeof(body_buf),
                       "debugstring", debugstring,
                       "httpstatus", status_num,
                       NULL);
    } else {
        const char *friendlyname;
        snprintf(debugstring, sizeof(debugstring), "%s %s %ld %s", timestr, request->log.mtunnel_id ? request->log.mtunnel_id : "", (long) (ZPATH_INSTANCE_GID%10000), str[0]);
        et_translate_get(str[0], "en", &friendlyname, NULL, NULL);
        exporter_http_encode(str[0], str0, sizeof(str0));
        if (n_str == 1) {
            string_replace(eun_sub_txt, body_buf, sizeof(body_buf),
                           "debugstring", debugstring,
                           "rawname", str0,
                           "httpstatus", status_num,
                           "friendlyname", friendlyname,
                           NULL);
        } else if (n_str == 2) {
            string_replace(eun_sub_txt, body_buf, sizeof(body_buf),
                           "debugstring", debugstring,
                           "rawname", str0,
                           "httpstatus", status_num,
                           "friendlyname", friendlyname,
                           "stringone", str[1],
                           NULL);
        } else if (n_str == 3) {
            string_replace(eun_sub_txt, body_buf, sizeof(body_buf),
                           "debugstring", debugstring,
                           "rawname", str0,
                           "httpstatus", status_num,
                           "friendlyname", friendlyname,
                           "stringone", str[1],
                           "stringtwo", str[2],
                           NULL);
        } else if (n_str > 3) {
            string_replace(eun_sub_txt, body_buf, sizeof(body_buf),
                           "debugstring", debugstring,
                           "rawname", str0,
                           "httpstatus", status_num,
                           "friendlyname", friendlyname,
                           "stringone", str[1],
                           "stringtwo", str[2],
                           "stringthree", str[3],
                           NULL);
        }
    }

    request->log.response_status = status;

    if (!request->response_data) {
        request->response_data = evbuffer_new();
    }

    if (IS_UNIFIED_PORTAL(request)) {
    evbuffer_add_printf(request->response_data,
                        "%s"
                        "Content-Length: %d\r\n"
                        "Content-Type: text/html\r\n"
                        "Connection: Keep-Alive\r\n"
                        "Exporter-Error-Code: %d\r\n"
                        "Strict-Transport-Security: max-age=" MAX_AGE_HSTS "; includeSubDomains; preload\r\n"
                        EXPORTER_RESTRICTIVE_CSP_UNIFIED_PORTAL
                        "\r\n%s",
                        std_hdrs,
                        (int)strnlen(body_buf, (sizeof(body_buf)-1)),
                        error,
                        request->conn->sni,
                        ZPATH_LOCAL_CLOUD_NAME,
                        EXPORTER_DOMAIN_UNIFIED_PORTAL_SUFFIX_UP,
                        request->conn->exporter_domain->user_portal_host[0] ? "https://" : "",
                        request->conn->exporter_domain->user_portal_host[0] ? request->conn->exporter_domain->user_portal_host : "",
                        EXPORTER_DOMAIN_AUTH,
                        body_buf);
    } else {
    evbuffer_add_printf(request->response_data,
                        "%s"
                        "Content-Length: %d\r\n"
                        "Content-Type: text/html\r\n"
                        "Connection: Keep-Alive\r\n"
                        "Exporter-Error-Code: %d\r\n"
                        "Strict-Transport-Security: max-age=" MAX_AGE_HSTS "; includeSubDomains; preload\r\n"
                        EXPORTER_RESTRICTIVE_CSP
                        "\r\n%s",
                        std_hdrs,
                        (int)strlen(body_buf),
                        error,
                        request->conn->sni,
                        ZPATH_LOCAL_CLOUD_NAME,
                        body_buf);
    }

    request->input_state = input_state_drain;
    request->http_response_complete = 1;

    return ZPATH_RESULT_NO_ERROR;
}

static int get_ctype_string (enum http_content_type ctype, char *buf, int buf_len)
{
   switch (ctype)
   {
      case HTTP_CONTENT_TYPE_JS:
           snprintf (buf, buf_len, "text/javascript");
           break;

      case HTTP_CONTENT_TYPE_HTML:
      default:
           snprintf (buf, buf_len, "text/html");
           break;
   }
   return 0;
}

int exporter_request_respond_status_and_data_with_ctype(struct exporter_request *request, enum http_status status, enum exporter_error_codes error, void *data, size_t data_len, enum http_content_type ctype)
{
    char std_hdrs[EXPORTER_STANDARD_RESPONSE_HEADER_BUF_LEN];
    char ctype_str[256];
    const char *hdrs = "X-Content-Type-Options: nosniff\r\n"
                        "Cache-Control: no-cache, no-store, max-age=0, must-revalidate\r\n"
                        "X-XSS-Protection: 1; mode = block\r\n";

    exporter_request_print_standard_response_headers(request, status, std_hdrs, sizeof(std_hdrs));

    if (!request->response_data) {
        request->response_data = evbuffer_new();
    }

    EXPORTER_DEBUG_HTTP("%s: Returning status %d, buffer length = %ld", request->name, status, (long) data_len);

    request->log.response_status = status;
    if (request->conn->exporter_domain && request->conn->exporter_domain->is_auth_domain) {
        strncat (std_hdrs, hdrs, EXPORTER_STANDARD_RESPONSE_HEADER_BUF_LEN - strlen(std_hdrs));
        get_ctype_string(ctype, ctype_str, sizeof(ctype_str));
    } else {
        get_ctype_string(HTTP_CONTENT_TYPE_HTML, ctype_str, sizeof(ctype_str));
    }

    if (IS_UNIFIED_PORTAL(request)) {
    evbuffer_add_printf(request->response_data,
                        "%s"
                        "Content-Length: %d\r\n"
                        "Content-Type: %s\r\n"
                        "Connection: Keep-Alive\r\n"
                        "Exporter-Error-Code: %d\r\n"
                        "Strict-Transport-Security: max-age=" MAX_AGE_HSTS "; includeSubDomains; preload\r\n"
                        "X-Frame-Options: DENY\r\n"
                        EXPORTER_RESTRICTIVE_CSP_UNIFIED_PORTAL
                        "\r\n",
                        std_hdrs,
                        (int) data_len,
                        ctype_str,
                        error,
                        request->conn->sni,
                        ZPATH_LOCAL_CLOUD_NAME,
                        EXPORTER_DOMAIN_UNIFIED_PORTAL_SUFFIX_UP,
                        request->conn->exporter_domain->user_portal_host[0] ? "https://" : "",
                        request->conn->exporter_domain->user_portal_host[0] ? request->conn->exporter_domain->user_portal_host : "",
                        EXPORTER_DOMAIN_AUTH);
    } else {
        evbuffer_add_printf(request->response_data,
                        "%s"
                        "Content-Length: %d\r\n"
                        "Content-Type: %s\r\n"
                        "Connection: Keep-Alive\r\n"
                        "Exporter-Error-Code: %d\r\n"
                        "Strict-Transport-Security: max-age=" MAX_AGE_HSTS "; includeSubDomains; preload\r\n"
                        "X-Frame-Options: DENY\r\n"
                        EXPORTER_RESTRICTIVE_CSP
                        "\r\n",
                        std_hdrs,
                        (int) data_len,
                        ctype_str,
                        error,
                        request->conn->sni,
                        ZPATH_LOCAL_CLOUD_NAME);
    }

    evbuffer_add(request->response_data, data, data_len);

    request->input_state = input_state_drain;
    request->http_response_complete = 1;

    return ZPATH_RESULT_NO_ERROR;
}

static int exporter_request_respond_data(struct exporter_request *request, void *data, size_t data_len) {
    return exporter_request_respond_status_and_data(request, HTTP_STATUS_OK, EXPORTER_ERROR_CODE_NO_ERROR, data, data_len);
}

static int exporter_request_respond_js_data(struct exporter_request *request, void *data, size_t data_len) {
    return exporter_request_respond_status_and_data_with_ctype(request, HTTP_STATUS_OK, EXPORTER_ERROR_CODE_NO_ERROR, data, data_len, HTTP_CONTENT_TYPE_JS);
}

int exporter_request_respond_status_and_data (struct exporter_request *request, enum http_status status, enum exporter_error_codes error, void *data, size_t data_len){
  return exporter_request_respond_status_and_data_with_ctype(request, status, error, data, data_len, HTTP_CONTENT_TYPE_HTML);
}

int exporter_request_respond_status_data_content_type(struct exporter_request *request, enum http_status status, enum exporter_error_codes error, const void *data, size_t data_len, const char *content_type)
{
    char std_hdrs[EXPORTER_STANDARD_RESPONSE_HEADER_BUF_LEN];
    exporter_request_print_standard_response_headers(request, status, std_hdrs, sizeof(std_hdrs));

    if (!request->response_data) {
        request->response_data = evbuffer_new();
    }

    EXPORTER_DEBUG_HTTP("%s: Returning status %d, buffer length = %ld", request->name, status, (long) data_len);

    request->log.response_status = status;

    if (IS_UNIFIED_PORTAL(request)) {
    evbuffer_add_printf(request->response_data,
                        "%s"
                        "Content-Length: %d\r\n"
                        "Content-Type: %s\r\n"
                        "Connection: Keep-Alive\r\n"
                        "Exporter-Error-Code: %d\r\n"
                        "Strict-Transport-Security: max-age=" MAX_AGE_HSTS "; includeSubDomains; preload\r\n"
                        "X-Frame-Options: DENY\r\n"
                        EXPORTER_RESTRICTIVE_CSP_UNIFIED_PORTAL
                        "\r\n",
                        std_hdrs,
                        (int) data_len,
                        content_type,
                        error,
                        request->conn->sni,
                        ZPATH_LOCAL_CLOUD_NAME,
                        EXPORTER_DOMAIN_UNIFIED_PORTAL_SUFFIX_UP,
                        request->conn->exporter_domain->user_portal_host[0] ? "https://" : "",
                        request->conn->exporter_domain->user_portal_host[0] ? request->conn->exporter_domain->user_portal_host : "",
                        EXPORTER_DOMAIN_AUTH);
    } else {
        evbuffer_add_printf(request->response_data,
                        "%s"
                        "Content-Length: %d\r\n"
                        "Content-Type: %s\r\n"
                        "Connection: Keep-Alive\r\n"
                        "Exporter-Error-Code: %d\r\n"
                        "Strict-Transport-Security: max-age=" MAX_AGE_HSTS "; includeSubDomains; preload\r\n"
                        "X-Frame-Options: DENY\r\n"
                        EXPORTER_RESTRICTIVE_CSP
                        "\r\n",
                        std_hdrs,
                        (int) data_len,
                        content_type,
                        error,
                        request->conn->sni,
                        ZPATH_LOCAL_CLOUD_NAME);
    }

    evbuffer_add(request->response_data, data, data_len);

    request->input_state = input_state_drain;
    request->http_response_complete = 1;

    return ZPATH_RESULT_NO_ERROR;
}

int exporter_request_respond_status_data_content_type_json(
        struct exporter_request *request,
        const char *set_cookie_header_value,
        const char *set_cookie_header_legacy_value,
        enum http_status status,
        const void *data, size_t data_len,
        const char *content_type, int content_len,
        int http_response_complete)
{
    char std_hdrs[EXPORTER_STANDARD_RESPONSE_HEADER_BUF_LEN];
    exporter_request_print_standard_response_headers(request, status, std_hdrs, sizeof(std_hdrs));

    if (!request->response_data) {
        request->response_data = evbuffer_new();
    }

    EXPORTER_DEBUG_HTTP("%s: Returning status %d, buffer length = %ld", request->name, status, (long) data_len);

    request->log.response_status = status;

    if (IS_UNIFIED_PORTAL(request)) {
    evbuffer_add_printf(request->response_data,
                        "%s"
                        "Set-Cookie: %s\r\n"
                        "Set-Cookie: %s\r\n"
                        "Content-Length: %d\r\n"
                        "Content-Type: %s\r\n"
                        "Connection: Keep-Alive\r\n"
                        "Strict-Transport-Security: max-age=" MAX_AGE_HSTS "; includeSubDomains; preload\r\n"
                        "X-Frame-Options: DENY\r\n"
                        EXPORTER_RESTRICTIVE_CSP_UNIFIED_PORTAL
                        "\r\n",
                        std_hdrs,
                        set_cookie_header_value,
                        set_cookie_header_legacy_value,
                        (int) content_len,
                        content_type,
                        request->conn->sni,
                        ZPATH_LOCAL_CLOUD_NAME,
                        EXPORTER_DOMAIN_UNIFIED_PORTAL_SUFFIX_UP,
                        request->conn->exporter_domain->user_portal_host[0] ? "https://" : "",
                        request->conn->exporter_domain->user_portal_host[0] ? request->conn->exporter_domain->user_portal_host : "",
                        EXPORTER_DOMAIN_AUTH);
    } else {
        evbuffer_add_printf(request->response_data,
                        "%s"
                        "Set-Cookie: %s\r\n"
                        "Set-Cookie: %s\r\n"
                        "Content-Length: %d\r\n"
                        "Content-Type: %s\r\n"
                        "Connection: Keep-Alive\r\n"
                        "Strict-Transport-Security: max-age=" MAX_AGE_HSTS "; includeSubDomains; preload\r\n"
                        "X-Frame-Options: DENY\r\n"
                        EXPORTER_RESTRICTIVE_CSP
                        "\r\n",
                        std_hdrs,
                        set_cookie_header_value,
                        set_cookie_header_legacy_value,
                        (int) content_len,
                        content_type,
                        request->conn->sni,
                        ZPATH_LOCAL_CLOUD_NAME);
    }

    evbuffer_add(request->response_data, data, data_len);

    request->input_state = input_state_drain;
    request->http_response_complete = http_response_complete;

    return ZPATH_RESULT_NO_ERROR;
}

static int exporter_request_redirect_to_https(struct exporter_request *request)
{
    int res;

    char orig_url[EXPORTER_URL_MAX_ENCODE_SIZE];
    const char *host_header;
    const char *w;

    if (!request->header_host) {
        /* No host header... */
        EXPORTER_LOG(AL_WARNING, "%s: No host header", request->name);
        return exporter_request_respond(request, HTTP_STATUS_BAD_REQUEST, EXPORTER_ERROR_CODE_NO_HOST_HEADER);
    }
    host_header = request->header_host;

    /* Find where host header ends, because we force everything to :443 */
    for (w = host_header; ((*w) && ((*w) != ':')); w++);

    snprintf(orig_url, sizeof(orig_url), "https://%.*s%s", (int)(w - host_header), host_header, &(request->url[request->url_parser.field_data[UF_PATH].off]));
    EXPORTER_DEBUG_HTTP("%s: Received unauthenticated http request. Redirecting to https: <%s>", request->name, orig_url);
    res = exporter_request_redirect_encode(request,
                                           NULL,      /* No cookie */
                                           NULL,      /* No cookie */
                                           NULL,
                                           NULL,
                                           orig_url,  /* Full everything domain+path */
                                           0,
                                           NULL,
                                           NULL);
    return res;
}

int exporter_request_process_data(struct exporter_request *request)
{
    path_callback_f *callback_f;
    int count;

    EXPORTER_DEBUG_CSP("%s: process data req_state: {%s,%s,%d%d,%s,%d,%d,%d}", request->name,
            request->log.url,
            exporter_request_input_state_get_str(request),
            request->http_request_complete, request->http_response_complete,
            exporter_request_async_state_get_str(request->async_state),
            request->async_count,
            request->request_body ? (int)evbuffer_get_length(request->request_body):-1,
            request->response_data ? (int)evbuffer_get_length(request->response_data): -1);

    /* There are 2 entry points for this function
       - exporter_conn_process_request
       - request_async_callback_on_thread
     */

    /* This will only happen on async call req to object store where we
     * do not set input_state
     * We can remove this since state check is already there in function
     * exporter_conn_process_request
     */

    /*
     * If req coming from async ehet_async_callback_on_thread  we must
     * check if state is correct
     */
    if (request->input_state != input_state_drain_and_process_request_data) {
        EXPORTER_LOG(AL_ERROR, "%s: Invalid state", request->name);
        return ZPATH_RESULT_ERR;
    }

    /* Perform path lookup to see what handler we wish to run. No
     * locking needed because this is a static (initialized at
     * startup) diamond table. NOTE: We verified path exists in a
     * check above. */
    count = diamond_search(body_path_search,
                           (const uint8_t *)&(request->url[request->url_parser.field_data[UF_PATH].off]),
                           request->url_parser.field_data[UF_PATH].len,
                           (void**)&(callback_f),
                           NULL,
                           1);
    if (!count) {
        EXPORTER_LOG(AL_ERROR, "%s: Could not find receiver for path %s", request->name, request->url);
        return exporter_request_respond(request, HTTP_STATUS_INTERNAL_SERVER_ERROR, EXPORTER_ERROR_CODE_DIAMOND_SEARCH_FAILED);
    }

    return (*callback_f)(request);
}

int exporter_request_process(struct exporter_request *request)
{
    path_callback_f *callback_f;
    size_t sni_len;
    int count;

    EXPORTER_DEBUG_CSP("%s: process req_state: {%s,%s,%d%d,%s,%d,%d,%d}", request->name,
            request->log.url,
            exporter_request_input_state_get_str(request),
            request->http_request_complete, request->http_response_complete,
            exporter_request_async_state_get_str(request->async_state),
            request->async_count,
            request->request_body ? (int)evbuffer_get_length(request->request_body):-1,
            request->response_data ? (int)evbuffer_get_length(request->response_data): -1);

    if (request->input_state != input_state_process_request) {
        EXPORTER_LOG(AL_ERROR, "%s: Invalid state", request->name);
        return ZPATH_RESULT_ERR;
    }

    if ((request->req_http_minor != 1) ||
        (request->req_http_major != 1)) {
        EXPORTER_LOG(AL_WARNING, "%s: Only accept HTTP/1.1", request->name);
        return exporter_request_respond(request, HTTP_STATUS_HTTP_VERSION_NOT_SUPPORTED, EXPORTER_ERROR_CODE_UNSUPPORTED_VERSION);
    }

    /* If not SSL, redirect to SSL */
    if (!request->conn->sni) {
        if ((request->url_parser.field_data[UF_PATH].len == strlen(EXPORTER_PATH_ALIVE)) &&
            (strncmp(&(request->url[request->url_parser.field_data[UF_PATH].off]),
                     EXPORTER_PATH_ALIVE, request->url_parser.field_data[UF_PATH].len) == 0)) {
            if (g_exporter_ot_mode) {
             /* If the OT Exporter is overloaded respond with 503
              * so that requests are not dispatched to the loaded exporter
              * Update global g_exporter_ot_overloaded every few min and not with
              * every health check request */
                g_exporter_ot_health_check_count++;
                if (g_exporter_ot_health_check_count > EXP_PRA_HEALTH_CHECK_COUNT) {
                    g_exporter_ot_health_check_count = 0;
                    check_and_reset_ot_exporter_health_state();
                }
                if (g_exporter_ot_overloaded) {
                     return exporter_request_respond(request, HTTP_STATUS_SERVICE_UNAVAILABLE, EXPORTER_ERROR_CODE_NO_ERROR);
                } else {
                     return exporter_request_respond(request, HTTP_STATUS_OK, EXPORTER_ERROR_CODE_NO_ERROR);
                }
            /* end of ot mode checks */
            } else {
                 /* This is a health check. Just return an empty 200. */
                 return exporter_request_respond(request, HTTP_STATUS_OK, EXPORTER_ERROR_CODE_NO_ERROR);
            }
        }
        EXPORTER_DEBUG_HTTP("%s: Redirecting to HTTPs", request->name);
        return exporter_request_redirect_to_https(request);
    }

    /* If we reach here its HTTPS */

    /* Verify that, if SSL, our host header matches SNI */
    if (!request->header_host) {
        /* We require HTTP 1.1, which always has a host header */
        return exporter_request_respond_with_text(request, HTTP_STATUS_BAD_REQUEST, EXPORTER_ERROR_CODE_NO_HOST_HEADER, "Missing host header");
    }
    /* We only compare as many bytes of SNI as exist, since the host
     * header may be suffixed with arbitrary port indication */
    sni_len = strlen(request->conn->sni);
    if (strncasecmp(request->conn->sni, request->header_host, sni_len) != 0) {
        /* Mismatch host header */
        EXPORTER_LOG(AL_WARNING, "%s: Mismatch host header, sni = %s, host header = %s", request->name, request->conn->sni, request->header_host);
        return exporter_request_respond(request, HTTP_STATUS_BAD_REQUEST, EXPORTER_ERROR_CODE_HOST_SNI_MISMATCH);
    }
    /* Verify that the host header is now ':' or \0 */
    if ((request->header_host[sni_len] != '\0') &&
        (request->header_host[sni_len] != ':')) {
        EXPORTER_LOG(AL_WARNING, "%s: Invalid host header, sni = %s, host header = %s", request->name,
                     request->conn->sni, request->header_host);
        return exporter_request_respond(request, HTTP_STATUS_BAD_REQUEST, EXPORTER_ERROR_CODE_INVALID_HOST_HEADER);
    }
    if (request->url_parser.field_set & (1 << UF_HOST)) {
        /* We have a host field in the URL. Verify THAT as well? Only if not connect ? */
        if (request->req_method != HTTP_CONNECT) {
            EXPORTER_LOG(AL_WARNING, "%s: Have host %.*s in URI, and method is %s. host header = %s", request->name,
                         request->url_parser.field_data[UF_HOST].len, &(request->url[request->url_parser.field_data[UF_HOST].off]),
                         http_method_names[request->req_method], request->header_host);
            return exporter_request_respond(request, HTTP_STATUS_BAD_REQUEST, EXPORTER_ERROR_CODE_HOST_IN_URL_WITHOUT_HTTP_CONNECT);
        }

        if ((request->url_parser.field_data[UF_HOST].len != sni_len) ||
            strncasecmp(&(request->url[request->url_parser.field_data[UF_HOST].off]), request->conn->sni, sni_len)) {
            EXPORTER_LOG(AL_WARNING, "%s: Host in URI does not match SNI/Host HEader, Have host %.*s in URI, and method is %s. host header = %s", request->name,
                         request->url_parser.field_data[UF_HOST].len, &(request->url[request->url_parser.field_data[UF_HOST].off]),
                         http_method_names[request->req_method], request->header_host);
            return exporter_request_respond(request, HTTP_STATUS_BAD_REQUEST, EXPORTER_ERROR_CODE_HOST_SNI_MISMATCH);
        }
    }

    /* Verify that the request domain is still valid- (app may have been disabled) */
    if (!(request->conn->exporter_domain->customer_gid)) {
        EXPORTER_LOG(AL_WARNING, "%s: Request but customer_gid is zero.", request->name);
        return exporter_request_respond(request, HTTP_STATUS_NOT_FOUND, EXPORTER_ERROR_CODE_CUSTOMER_GID_NOT_FOUND);
    }

    if (request->header_via) {
        if (strstr(request->header_via, EXPORTER_DOMAIN_AUTH)) {
            /* We were already forwarded by ourself! */
            EXPORTER_LOG(AL_WARNING, "%s: Proxy loop detected", request->name);
            return exporter_request_respond_with_csv_error(request,
                                                           HTTP_STATUS_BAD_REQUEST,
                                                           EXPORTER_ERROR_CODES_PROXY_LOOP_DETECTED,
                                                           "Could not process HTTP request",
                                                           "ZPA BA: Proxy loop detected");

        }
    }

    /* Perform path lookup to see what handler we wish to run. No
     * locking needed because this is a static (initialized at
     * startup) diamond table. NOTE: We verified path exists in a
     * check above. */
    count = diamond_search(path_search,
                           (const uint8_t *)&(request->url[request->url_parser.field_data[UF_PATH].off]),
                           request->url_parser.field_data[UF_PATH].len,
                           (void**)&(callback_f),
                           NULL,
                           1);
    if (!count) {
        EXPORTER_LOG(AL_ERROR, "%s: Could not find receiver for path %s", request->name, request->url);
        return exporter_request_respond(request, HTTP_STATUS_INTERNAL_SERVER_ERROR, EXPORTER_ERROR_CODE_DIAMOND_SEARCH_FAILED);
    }

    return (*callback_f)(request);
}


static int string_replace(const char *orig_str, char *new_str, size_t new_str_len, ...)
{
    const char *replace_str[100];
    const char *with_str[100];
    int replace_len[100];
    va_list vl;
    int count = 0;
    int i;

    if (new_str_len == 0) {
        EXPORTER_LOG(AL_ERROR, "Expected Data for replacing");
        return ZPATH_RESULT_ERR;
    }

    char *s = new_str;
    char *e = new_str + new_str_len;

    /* Grab args... */
    va_start(vl, new_str_len);
    while ((count < 100) && ((replace_str[count] = va_arg(vl, const char *)))) {
        if (!((with_str[count] = va_arg(vl, const char *)))) {
            EXPORTER_LOG(AL_ERROR, "Bad argument count");
            *new_str = 0;
            va_end(vl);
            return ZPATH_RESULT_ERR;
        }
        replace_len[count] = strlen(replace_str[count]);
        count++;
    }
    va_end(vl);
    if (!count) {
        EXPORTER_LOG(AL_ERROR, "Bad argument count");
        *new_str = 0;
        return ZPATH_RESULT_ERR;
    }

    while((*orig_str) && (s < e)) {
        if ((orig_str[0] == '$') &&
            (orig_str[1] == '$') &&
            (orig_str[2] == '$')) {
            for (i = 0; i < count; i++) {
                if (strncmp(&(orig_str[3]), replace_str[i], replace_len[i]) == 0) {
                    orig_str += replace_len[i] + 3;
                    const char *w = with_str[i];
                    while ((*w) && (s < e)) {
                        *s = *w;
                        s++;
                        w++;
                    }
                    break;
                }
            }
            if (i == count) {
                /* No replacement. Just remove... */
                orig_str += 3;
                while (((*orig_str) >= 'a') &&
                       ((*orig_str) <= 'z')) {
                    orig_str++;
                }
            } else {
                /* Case handled already */
            }
        } else {
            *s = *orig_str;
            s++;
            orig_str++;
        }
    }
    if (s == e) {
        EXPORTER_LOG(AL_ERROR, "String too long");
        s--;
        *s = *orig_str;
        return ZPATH_RESULT_ERR;
    } else {
        *s = *orig_str;
        return ZPATH_RESULT_NO_ERROR;
    }
}

static struct argo_structure_description *exporter_caa_data_description;

int exporter_request_init(void)
{
    size_t i;

    /* Make string.... We copy the javascript and change every '%' to
     * a '%%', and change every $string into a '%' */
    idp_select_js = EXPORTER_MALLOC(exporter_idp_query_html_len + 1);
    memcpy(idp_select_js, exporter_idp_query_html, exporter_idp_query_html_len);
    idp_select_js[exporter_idp_query_html_len] = 0;

    path_search = diamond_create(1, NULL, 10);
    body_path_search = diamond_create(1, NULL, 10);

    EXPORTER_DEBUG_HTTP("Output string = <%s>", idp_select_js);

    for (i = 0; i < (sizeof(callbacks) / sizeof(callbacks[0])); i++) {
        const char *start;
        size_t len;
        int wildcard_suffix = 0;
        int res;

        len = strlen(callbacks[i].path);
        start = callbacks[i].path;
        if (start[len - 1] == '*') {
            len--;
            wildcard_suffix = 1;
        }
        res = diamond_add(path_search, (uint8_t *) start, len, 0, wildcard_suffix, 0, 0, 0, callbacks[i].callback);
        if (res){
            EXPORTER_LOG(AL_ERROR, "Err");
            return ZPATH_RESULT_ERR;
        } else {
            EXPORTER_LOG(AL_NOTICE, "Prepped for traffic to %s (%.*s)", callbacks[i].path, (int)len, start);
        }
    }

    for (i = 0; i < (sizeof(body_callbacks) / sizeof(body_callbacks[0])); i++) {
        const char *start;
        size_t len;
        int wildcard_suffix = 0;
        int res;

        len = strlen(body_callbacks[i].path);
        start = body_callbacks[i].path;
        if (start[len - 1] == '*') {
            len--;
            wildcard_suffix = 1;
        }
        res = diamond_add(body_path_search, (const uint8_t *) start, len, 0, wildcard_suffix, 0, 0, 0, body_callbacks[i].callback);
        if (res){
            EXPORTER_LOG(AL_ERROR, "Err");
            return ZPATH_RESULT_ERR;
        } else {
            EXPORTER_LOG(AL_NOTICE, "Prepped for traffic to %s (%.*s)", body_callbacks[i].path, (int)len, start);
        }
    }

    /* Initialize logging */
    exporter_transaction_collection = argo_log_create("exporter_transaction_log", NULL, NULL);
    if (!exporter_transaction_collection) {
        EXPORTER_LOG(AL_ERROR, "Could not create transaction logs");
        return ZPATH_RESULT_ERR;
    }

    if (!argo_log_read(exporter_transaction_collection,
                       "exporter_transaction_log_file",
                       0,
                       1,
                       argo_log_file_callback,
                       NULL,
                       argo_log_file_create(exporter_transaction_collection,
                                            "/zpath/log/exporter_transaction.log",
                                            "exporter_transaction",
                                            1024*1024*1024,
                                            argo_serialize_binary),
                       NULL,
                       0)) {
        ZPN_LOG(AL_ERROR, "Could not create transaction reader");
    }
    /* Initialize logging for CSP feature */
    exporter_csp_collection = argo_log_create("exporter_csp_log", NULL, NULL);
    if (!exporter_csp_collection) {
        EXPORTER_LOG(AL_ERROR, "Could not create csp transaction logs");
        return ZPATH_RESULT_ERR;
    }

    if (!argo_log_read(exporter_csp_collection,
                       "exporter_csp_log_file",
                       0,
                       1,
                       argo_log_file_callback,
                       NULL,
                       argo_log_file_create(exporter_csp_collection,
                                            "/zpath/log/exporter_csp.log",
                                            "exporter_csp",
                                            1024*1024*1024,
                                            argo_serialize_binary),
                       NULL,
                       0)) {
        ZPN_LOG(AL_ERROR, "Could not create csp transaction reader");
    }

    zpn_fohh_client_exporter_init(g_exporter_ot_mode);
    if (!(exporter_caa_data_description = argo_register_global_structure(EXPORTER_CAA_DATA_HELPER)))
        return ZPATH_RESULT_ERR;
    return ZPATH_RESULT_NO_ERROR;
}

int exporter_request_log(struct exporter_request *request)
{
    int res = ZPATH_RESULT_NO_ERROR;

    request->log.log_date = epoch_us();

    /* Detect fingerprint whatchanged here */
    //doauth

#define TIMEFROM_A_B(xx, yy) (((xx) && (yy)) ? (yy - xx) : 0)
    request->log.req_rx_total_time_us = TIMEFROM_A_B(request->log.req_rx_start_us, request->log.req_rx_done_us);
    request->log.req_tx_total_time_us = TIMEFROM_A_B(request->log.req_tx_start_us, request->log.req_tx_done_us);

    request->log.req_tx_setup_time_us = TIMEFROM_A_B(request->log.req_rx_hdr_done_us, request->log.req_tx_start_us);
    request->log.req_tx_to_rsp_rx_time_us = TIMEFROM_A_B(request->log.req_tx_done_us, request->log.rsp_rx_start_us);

    request->log.rsp_rx_total_time_us = TIMEFROM_A_B(request->log.rsp_rx_start_us, request->log.rsp_rx_done_us);
    request->log.rsp_tx_total_time_us = TIMEFROM_A_B(request->log.rsp_tx_start_us, request->log.rsp_tx_done_us);

    /* Some requests aren't really associated with a domain. */
    if (!request->conn ||
        !request->conn->exporter_domain) return ZPATH_RESULT_NO_ERROR;

    if (exporter_transaction_collection) {
        if ((res = argo_log_structure_immediate(exporter_transaction_collection,
                                                argo_log_priority_info,
                                                0,
                                                "exporter_transaction",
                                                zpn_http_trans_log_description,
                                                &(request->log)))) {
            ZPN_LOG(AL_WARNING, "%s: File transaction log fail: %s", request->name, zpath_result_string(res));
        }
    }

    res = zpath_customer_log_struct(request->conn->exporter_domain->customer_gid,
                                    zpath_customer_log_type_zpn_clientless_transaction,
                                    "exporter_transaction",
                                    NULL,
                                    NULL,
                                    NULL,
                                    NULL,
                                    zpn_http_trans_log_description,
                                    &(request->log));

    if (res) {
        ZPN_LOG(AL_WARNING, "%s: Producer transaction log fail: %s", request->name, zpath_result_string(res));
    }

    if (request->conn && request->conn->exporter_domain) {
        res = exporter_siem_log_transaction(request->conn->exporter_domain->customer_gid,
                zpn_http_trans_log_description,
                &(request->log));
        if (res) {
            ZPN_LOG(AL_WARNING, "%s: SIEM transaction log fail: %s", request->name, zpath_result_string(res));
        }
    }

    /* If CSP request send is enable, i.e. send the first http request
     * carrying CSP fingerprint data then write this data to log collection
     * send it to producer group */
    if (request->is_csp_log) {

        /* if csp collection is defined the log it locally */
        if (exporter_csp_collection) {
            /* Log the data into CSP log file */
            if ((res = argo_log_structure_immediate(exporter_csp_collection,
                                                    argo_log_priority_info,
                                                    0,
                                                    "exporter_csp",
                                                    zpn_http_trans_log_description,
                                                    &(request->log)))) {
                ZPN_LOG(AL_WARNING, "%s: File transaction csp log fail: %s", request->name, zpath_result_string(res));
            }
        }

        /* Send this log to producers */
        res = zpath_customer_log_struct(request->conn->exporter_domain->customer_gid,
                zpath_customer_log_type_zpn_csp,
                "exporter_csp",
                NULL,
                NULL,
                NULL,
                NULL,
                zpn_http_trans_log_description,
                &(request->log));

        if (res) {
            ZPN_LOG(AL_WARNING, "%s: Producer csp transaction log fail: %s", request->name, zpath_result_string(res));
        }

        /* delete the CSP object store entry */
        exporter_delete_domain_csp_data(request->name, request->csp_os_data, NULL,NULL,0);
    }

    /* Log to workers */
    exporter_fohh_worker_num_requests(request->conn->thread->fohh_thread_id);
    exporter_fohh_worker_num_responses(request->conn->thread->fohh_thread_id, request->log.response_status);

    return res;
}

int exporter_request_log_cleanup(struct exporter_request *request)
{
    struct zpn_http_trans_log *log = &(request->log);

    if (log->mtunnel_id) {
        EXPORTER_FREE(log->mtunnel_id);
        log->mtunnel_id = NULL;
    }

    if (log->mt_reason) {
        EXPORTER_FREE(log->mt_reason);
        log->mt_reason = NULL;
    }

    if (log->nameid) {
        EXPORTER_FREE(log->nameid);
        log->nameid = NULL;
    }

    if (log->portal_api_status) {
        EXPORTER_FREE(log->portal_api_status);
        log->portal_api_status = NULL;
    }

    if (log->fname) {
        EXPORTER_FREE(log->fname);
        log->fname = NULL;
    }
    if (log->lname) {
        EXPORTER_FREE(log->lname);
        log->lname = NULL;
    }

    if (log->session_id) {
        EXPORTER_FREE(log->session_id);
        log->session_id = NULL;
    }

    if (log->domain_id) {
        EXPORTER_FREE(log->domain_id);
        log->domain_id = NULL;
    }

    return ZPATH_RESULT_NO_ERROR;
}

/*
 * Utility function
 */
const char * exporter_request_async_state_get_str( enum exporter_request_async_state state)
{
    const char *exporter_request_async_state_str[] =  {
        [async_state_reprocess] = "async_state_reprocess",
        [async_state_from_sp_wrote_session] = "async_state_from_sp_wrote_session",
        [async_state_refresh_auth] = "async_state_refresh_auth",
        [async_state_reprocess_portal_api] = "async_state_reprocess_portal_api",
        [async_state_reprocess_guac_api] = "async_state_reprocess_guac_api",
        [async_state_done] = "async_state_done",
        [async_state_response_done] = "async_state_response_done",
        [async_state_close_client_conn] = "async_state_close_client_conn",
        [async_state_mt_connection_err] = "async_state_mt_connection_err",
        [async_state_mt_remote_disconnect] = "async_state_mt_remote_disconnect",
        [async_state_hard_err] = "async_state_hard_err",
        [async_state_reprocess_priv_capability] = "async_state_reprocess_priv_capability",
        [async_state_reprocess_file_scan] = "async_state_reprocess_file_scan",
        [async_state_get_assertion] = "async_state_get_assertion",
        [async_state_fetch_policy] = "async_state_fetch_policy",
        [async_state_csp_fetch_profile] = "async_state_csp_fetch_profile",
        [async_state_csp_fetch_policy] = "async_state_csp_fetch_policy",
        [async_state_csp_from_sp_process_data] = "async_state_csp_from_sp_process_data",
        [async_state_csp_from_sp_process_data_wrote_session] = "async_state_csp_from_sp_process_data_wrote_session",
        [async_state_csp_from_sp_process_data_wrote_domain] = "async_state_csp_from_sp_process_data_wrote_domain",
        [async_state_csp_doauth_process_data] = "async_state_csp_doauth_process_data",
        [async_state_csp_doauth_get_session] = "async_state_csp_doauth_get_session",
        [async_state_csp_doauth_wrote_session] = "async_state_csp_doauth_wrote_session",
        [async_state_csp_doauth_wrote_domain] = "async_state_csp_doauth_wrote_domain",
        [async_state_csp_periodic_process_data] = "async_state_csp_periodic_process_data",
        [async_state_csp_periodic_get_session] = "async_state_csp_periodic_get_session",
        [async_state_csp_periodic_wrote_domain] = "async_state_csp_periodic_wrote_domain",
        [async_state_csp_periodic_wrote_session] = "async_state_csp_periodic_wrote_session",
        [async_state_csp_get_nonce] = "async_state_csp_get_nonce",
        [async_state_csp_get_ctx_domain] = "async_state_csp_get_ctx_domain",
        [async_state_fetch_privileged_credentials] = "async_state_fetch_privileged_credentials",
        [async_state_store_guac_sess_data] = "async_state_store_guac_sess_data",
        [async_state_check_user_shared_session] = "async_state_check_user_shared_session",
        [async_state_reprocess_privileged_file_upload_request] = "async_state_reprocess_privileged_file_upload_request",
        [async_state_refresh_auth_levelid] = "async_state_refresh_auth_levelid",
    };

    assert(state < async_state_max_invalid);
    assert(state > async_state_min_invalid);
    if ((state <= async_state_min_invalid) || (state >= async_state_max_invalid)) {
        return "async_state_invalid";
    }

    return exporter_request_async_state_str[state];

}

/* In wally, check if there is a global feature flag set */
int is_arbitrary_auth_domain_globally_suppressed()
{
    char    *config_val         = NULL;
    int     ret                 = 0;
    int64_t root_customer_gid = ZPATH_GID_GET_CUSTOMER_GID(zpath_instance_global_state.current_config->gid);
    config_val = zpath_config_override_get_config_str(ARBITRARY_AUTH_DOMAIN_FEATURE_GLOBALLY_SUPPRESSED,
                                                      &config_val,
                                                      DEFAULT_ARBITRARY_AUTH_DOMAIN_FEATURE_GLOBAL_DISABLE,
                                                      root_customer_gid,
                                                      (int64_t)0); // Argument list terminator
    if (strcmp(config_val, DEFAULT_ARBITRARY_AUTH_DOMAIN_FEATURE_GLOBAL_DISABLE) == 0) {
        // Feature is not suppressed. Return False
        ret = 0;
    } else {
        // Feature is supressed/disabled. Return True
        ret = 1;
    }
    return ret;
}

/* Check if arbitrary auth domain feature is disabled for this gid */
int is_arbitrary_auth_domain_enabled_for_customer(int64_t customer_gid)
{
    char    *config_val         = NULL;
    int     ret                 = 0;
    int64_t root_customer_gid = ZPATH_GID_GET_CUSTOMER_GID(zpath_instance_global_state.current_config->gid);

    config_val = zpath_config_override_get_config_str( ARBITRARY_AUTH_DOMAIN_FEATURE,
                                                       &config_val,
                                                       DEFAULT_ARBITRARY_AUTH_DOMAIN_FEATURE,
                                                       customer_gid,
                                                       root_customer_gid,
                                                       (int64_t) 0 );
    if (strcmp(config_val, DEFAULT_ARBITRARY_AUTH_DOMAIN_FEATURE) == 0) {
        // Feature flag is disabled. return False
        ret = 0;
    } else {
        // Feature flag is enabled. return True
        ret = 1;
    }
    return ret;
}

int is_arbitrary_auth_domain_enabled(int64_t customer_gid)
{
    /* Global disable takes precedence */
    if (is_arbitrary_auth_domain_globally_suppressed()) return 0;

    /* If global disable is not set then check if arbitrary auth domain is disabled
     * on a specific customer gid
     */
    return is_arbitrary_auth_domain_enabled_for_customer(customer_gid);

}

/* Check if addition of xff header feature is enabled for this gid */
int64_t exporter_xff_header_enabled_for_customer(int64_t customer_gid)
{
    int64_t config_val = zpath_config_override_get_config_int(EXPORTER_XFF_HEADER_FEATURE,
                                                              &config_val,
                                                              DEFAULT_EXPORTER_XFF_HEADER_FEATURE,
                                                              customer_gid,
                                                              (int64_t)0);


    return config_val ? 1 : 0;
}

/* Check if addition of forwarded header feature is enabled for this gid */
int64_t exporter_forwarded_header_enabled_for_customer(int64_t customer_gid)
{
    int64_t config_val = zpath_config_override_get_config_int(EXPORTER_FORWARDED_FOR_HEADER_FEATURE,
                                                              &config_val,
                                                              DEFAULT_EXPORTER_FORWARDED_FOR_HEADER_FEATURE,
                                                              customer_gid,
                                                              (int64_t)0);


    return config_val ? 1 : 0;
}

static int is_url_cookie_encryption_enabled(int64_t customer_gid)
{
    int64_t config_val = DEFAULT_URL_COOKIE_ENCRYPTION;
    int64_t root_customer_gid = ZPATH_GID_GET_CUSTOMER_GID(zpath_instance_global_state.current_config->gid);
    config_val = zpath_config_override_get_config_int(EXPORTER_URL_COOKIE_ENCRYPTION,
                                                      &config_val,
                                                      DEFAULT_URL_COOKIE_ENCRYPTION, /* 1 Default enabled */
                                                      customer_gid,
                                                      zpath_instance_global_state.current_config->gid,
                                                      root_customer_gid,
                                                      (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                      (int64_t)0);

    return config_val ? 1 : 0;
}

static int is_exporter_login_hint_feature_enabled(int64_t customer_gid)
{
    char *config_val = DEFAULT_EXPORTER_LOGIN_HINT_FEATURE;
    int64_t root_customer_gid = ZPATH_GID_GET_CUSTOMER_GID(zpath_instance_global_state.current_config->gid);
    config_val = zpath_config_override_get_config_str(EXPORTER_LOGIN_HINT_FEATURE,
                                                      &config_val,
                                                      DEFAULT_EXPORTER_LOGIN_HINT_FEATURE,
                                                      customer_gid,
                                                      zpath_instance_global_state.current_config->gid,
                                                      root_customer_gid,
                                                      (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                      (int64_t)0);

    EXPORTER_LOG(AL_DEBUG, "Exporter Login Hint config value = %s", config_val);

    if (strcmp(config_val, "disabled") == 0) {
        return 0;
    } else if (strcmp(config_val, "enabled") == 0) {
        return 1;
    }

    EXPORTER_LOG(AL_DEBUG, "Invalid Exporter Login Hint config value = %s using value = %s instead", config_val, DEFAULT_EXPORTER_LOGIN_HINT_FEATURE);
    if (strcmp(DEFAULT_EXPORTER_LOGIN_HINT_FEATURE, "enabled") == 0) {
        return 1;
    }

    return 0;
}

/*
 * Using this function as API
 *   - res is ZPN_RESULT_EXPIRED
 *     Caller must return with ZPATH_RESULT_NO_ERROR
 *     Redirection is already set by policy code and caller must not continue
 *   - res is ZPATH_RESULT_ASYNCHRONOUS
 *     Caller must return with ZPATH_RESULT_NO_ERROR
 *     State is already sent to async_state correctly
 *   - res is ZPATH_RESULT_NOT_FOUND
 *     This can be due to assertion not found in object store or policy not found
 *     If assertion is not found then request->policy_state is null
 */
static int exporter_load_policy(struct exporter_request *request,
                                const char* assertion_cookie,
                                const char* session_crypto_value)
{
    int         res = ZPATH_RESULT_NO_ERROR;
    int         b64_len = 0;
    uint8_t     *unbase64 = NULL;
    uint8_t     *decrypted = NULL;
    const char  *assertion = NULL;

    /* We have an assertion key... Let's verify the assertion exists */
    res = object_store_get(request->name,
                           assertion_cookie,
                           &assertion,
                           exporter_request_async_callback,
                           request,
                           0,
                           ostore_role_object_store);
    if (res) {
        if (res == ZPATH_RESULT_ASYNCHRONOUS) {
            EXPORTER_DEBUG_AUTH("%s: CSP_POLICY url: [%s] Fetching assertion asynchronously - async_count (%d->%d)",
                                request->name, request->log.url,
                                request->async_count, request->async_count + 1);
            exporter_set_async_state_and_inc_count(request, async_state_get_assertion);
        } else {
            /* Either assertion is not found or we have object store error */
            EXPORTER_DEBUG_AUTH("%s: CSP_POLICY url: [%s] Fetching assertion, result[%s], need to reauth",
                                request->name, zpath_result_string(res), request->log.url);
        }

        /*
         * If we hit here and request->policy_state will be null
         * since we are unable to load policy due to invalid assertion
         * If this is case of fromsp, we have 3 options
         *  - Capture the fingerprint with no policy
         *    - Even if we continue we will eventually fail since assertion is
         *      not there and redirected to authsp
         *  - Or redirect to auth domain since user is giving incorrect
         *    assertion
         *  - Or bypass fingerprint, not a good idea what if object store was down ?
         *    If we do this then all later requests will never have mbfp
         *    if this was coming from SSO
         */
        return res;
    }

    res = exporter_request_decrypt_assertion(request,
                                             assertion_cookie,
                                             assertion,
                                             session_crypto_value,
                                             &unbase64,
                                             &b64_len,
                                             &decrypted);

    if (NULL != unbase64 && NULL != decrypted && 0 != b64_len) {
        EXPORTER_DEBUG_CSP("%s: CSP_POLICY url: [%s] Decrypted with success",
                           request->name, request->log.url);

        /* load the state for policy check */
        res = exporter_request_policy_state(request, unbase64, b64_len);
        if (unbase64) EXPORTER_FREE(unbase64);
        if (decrypted) EXPORTER_FREE(decrypted);
        if (res) {
            switch (res) {
                case ZPATH_RESULT_ASYNCHRONOUS:
                    EXPORTER_DEBUG_CSP("%s: CSP_POLICY url: [%s] exporter_request_policy_state async",
                                       request->name, request->log.url);
                    exporter_set_async_state(request, async_state_fetch_policy);
                    break;
                case ZPN_RESULT_EXPIRED:
                    /* We need to break the flow on expired and return from parent function without any error */
                    EXPORTER_DEBUG_CSP("%s: CSP_POLICY url: [%s] exporter_request_policy_state expired need to reauth",
                                       request->name, request->log.url);
                    //res = ZPATH_RESULT_NO_ERROR;
                    break;
                default:
                    EXPORTER_DEBUG_CSP("%s: CSP_POLICY url: [%s] exporter_request_policy_state Error",
                                       request->name, request->log.url);
                    //Even on policy failure continue to capture mbfp and try to get CSP policy
                    //exporter_request_policy_state can fail due to reauth/other non CSP tables
                    //We must fetch CSP tables since we don't know what failure happened and
                    //then continue

                    /* Handle this in parent since this is generic function to load policy */
                    //res = ZPATH_RESULT_NO_ERROR;
                    break;
            }
        } else {
            EXPORTER_DEBUG_CSP("%s: CSP_POLICY Successfully loaded policy url: [%s] exporter_request_policy_state",
                               request->name, request->log.url);
        }
    } else {
        EXPORTER_DEBUG_CSP("%s: CSP_POLICY url: [%s] Decryption failed disable CSP",
                request->name, request->log.url);
    }
    return res;
}

static int exporter_read_csp_config(struct exporter_request* request) {
    /* Check for status now */
    int ret = ZPATH_RESULT_ERR;
    enum zpe_access_action matched_action = zpe_csp_policy_do_not_monitor;

    ret = exporter_request_fetch_csp_policy_action(request,
            &request->csp_rule_gid,
            &matched_action,
            &request->app_gid);

    EXPORTER_LOG(AL_INFO, "CSP_POLICY evaluation returned : %s ", zpath_result_string(ret));
    //Return on policy async
    if (ret == ZPN_RESULT_ASYNCHRONOUS) {
        EXPORTER_DEBUG_CSP("%s: CSP_POLICY url: [%s] async for csp_policy domain: [%s]",
                request->name, request->log.url, request->orig_domain);
        return ret;
    }

    //There is error this can be PRA portal also, we need to log it
    /* NULL check for policy state for JIRA ET-69564 */
    if (ret != ZPATH_RESULT_NO_ERROR && NULL != request->policy_state) {
        EXPORTER_LOG(AL_INFO, "CSP_POLICY Evaluate User Portal");
        /* If unable to find policy always continue without fingerprint
         * For priv portal we will always get failure since we are looking
         * in table
         */
        uint8_t is_user_portal = 0;
        uint8_t is_csp_enabled = 0;
        const char *policy_domain;
        policy_domain = exporter_domain_fetch_mapped_domain(request->orig_domain);

        int res = (int)exporter_domain_get_ot_csp_flags(&global_exporter,
                request->orig_domain, strlen(request->orig_domain),
                &is_user_portal, &is_csp_enabled);

        struct zpn_user_portal *user_portal = NULL;
        int64_t portal_id = 0;

        /* CSP_POLICY - Get domain from origurl but for console we need to get from sra_host_fqdn */
        const char *domain = (request->sra_host_fqdn) ? request->sra_host_fqdn :
            (request->is_csp_enabled ? policy_domain: request->conn->exporter_domain->cfg_domain);

        if (is_user_portal && res == ZPATH_RESULT_NO_ERROR) {
            ret = zpn_user_portal_table_get_domain_by_scope(
                    request->orig_customer_gid,
                    request->scope_gid, domain, &user_portal,
                    exporter_request_wally_callback, request, 0);

            if((ret == ZPATH_RESULT_NO_ERROR) && user_portal) {
                portal_id = user_portal->gid;
            } else {
                EXPORTER_LOG(AL_ERROR, "%s - error: %s fetching user portal for domain: %s", request->name, zpath_result_string(ret), domain);
            }
        }

        if (portal_id) {
            request->app_gid = portal_id;
            ret = zpn_broker_csp_policy_evaluate(request->is_csp_enabled ? request->orig_customer_gid : request->conn->exporter_domain->customer_gid,
                    portal_id,
                    zpe_policy_type_csp_policy,
                    request->policy_state->general_state_hash,
                    request->policy_state->saml_policy_enabled ? request->policy_state->saml_state_hash : NULL,
                    request->policy_state->scim_policy_enabled ? request->policy_state->scim_state_hash : NULL,
                    &matched_action,
                    &request->csp_rule_gid);
            if (ret == ZPATH_RESULT_NO_ERROR) {
                if (request->csp_rule_gid && matched_action == zpe_csp_policy_monitor) {
                    EXPORTER_LOG(AL_INFO, "%s, CSP_POLICY evaluation returned matched_action = %s, result = %s, portal Id %"PRId64"",
                            request->name, zpe_access_action_string(matched_action),
                            zpath_result_string(ret), portal_id);
                    is_csp_enabled = 1;
                }
            } else {
                EXPORTER_LOG(AL_INFO, "%s: No matching rule is found in Bsp policy for portal id %"PRId64"", request->name, portal_id);
            }
        }

        EXPORTER_DEBUG_CSP("%s: CSP_POLICY url: [%s] error for csp_policy [%s] domain: [%s] is_user_portal: %d is_csp_enabled: %d",
                request->name, request->log.url, zpath_result_string(ret), policy_domain, is_user_portal, is_csp_enabled);
    }


    /* Even if the policy fails consider it is a non-monitor case */
    //Get monitor status on success
    request->is_mon_enabled = (matched_action == zpe_csp_policy_monitor) ? 1 : 0;

    EXPORTER_DEBUG_CSP("%s: CSP_POLICY url: [%s] domain: [%s] matched_action: [%s]",
            request->name, request->log.url, request->orig_domain, request->is_mon_enabled ? "MONITOR": "DONOTMONITOR");

    //Even on failure if we are coming from SSO we must capture fp, add profile always
    //Lifetime of session always use one profile - Reverify this
    //policy will be ZERO in server1 but server2 will have policy id since its monitored
    EXPORTER_DEBUG_CSP("%s: CSP_POLICY url: [%s] exporter_request_fetch_csp_policy_action res [%s] domain: [%s]",
            request->name, request->log.url, zpath_result_string(ret), request->orig_domain);

    //Get profile always even on policy failure
    ret = exporter_request_fetch_csp_profile_mask(request,
            &request->criteria_flags_mask,
            &request->csp_profile_gid);

    //Return on profile async
    if (ret == ZPN_RESULT_ASYNCHRONOUS) {
        EXPORTER_DEBUG_CSP("%s: CSP_POLICY url: [%s] async for csp_profile domain: [%s]",
                request->name, request->log.url, request->orig_domain);
        return ret;
    }

    //Check if we want to collect geo
    request->is_collect_location = exporter_csp_is_collect_location(request->criteria_flags_mask);

    EXPORTER_DEBUG_CSP("%s: CSP_POLICY status fp url: [%s] for {%s,%s,%"PRId64",0x%"PRIx64",%d,%d}",
            request->name,
            request->log.url,
            request->orig_domain,
            request->is_mon_enabled ? "MONITOR": "DONOTMONITOR",
            request->csp_profile_gid,
            request->criteria_flags_mask,
            request->is_collect_location,
            request->csp_timeout);

    return ret;
}

/*
 * Request processing once request is complete
 */
int exporter_request_complete_process(struct exporter_request* request) {
    int res = ZPATH_RESULT_NO_ERROR;

    if ((request == NULL) || (request->portal_info == NULL)) {
        return ZPATH_RESULT_NO_ERROR;
    }

    enum zpa_user_portal_api_type api_type = exporter_user_portal_request_state_get_api_type(request->portal_info);

    if ((api_type == ZPA_SRA_PORTAL_API_TYPE_MY_FILES_UPLOAD_INTIATE) ||
        (api_type == ZPA_SRA_PORTAL_API_TYPE_MY_FILES_UPLOAD) ||
        (api_type == ZPA_SRA_PORTAL_API_TYPE_MY_FILES_UPLOAD_COMPLETE) ||
        (api_type == ZPA_SRA_PORTAL_API_TYPE_MY_FILES_UPLOAD_ABORT)) {

        EXPORTER_DEBUG_ADVANCED_FILE_TRANSFER("%s: Re-intiating PRA-service Proxy API", request->name);

        res = exporter_sra_portal_http_pra_service_proxy_api(request);
        if (res) {
            EXPORTER_LOG(AL_ERROR, "%s: pra service proxy returned %s", request->name, zpath_result_string(res));
            return res;
        }
        return ZPATH_RESULT_NO_ERROR;
    }
    return res;
}

char * exporter_get_managed_domain_name(int64_t customer_gid, const char* domain_name, size_t domain_name_len)
{

    struct zpn_client_less *client_less =  zpn_client_less_search_by_domain(customer_gid, domain_name, domain_name_len);

    if (!client_less) {
        return NULL;
    }
    char *managed_domain = NULL;

    if (client_less->ext_domain) {
        /* This is a managed domain as it has ext_domain */
        const char * prefix = exporter_get_tld_customer_name(0, 0);
        if (prefix) {
            exporter_domain_generate_managed_app_fqdn(prefix, client_less->ext_domain, client_less->ext_label,
                                                      client_less->ext_id,  &managed_domain, customer_gid);
            return (managed_domain);
        }
    }

    return NULL;
}
