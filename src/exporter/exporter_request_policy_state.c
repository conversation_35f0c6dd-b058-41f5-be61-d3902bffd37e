/*
 * exporter_request_state.c. Copyright (C) 2021 Zscaler Inc. All Rights Reserved
 *
 * Includes functions for the following
 * a. Initializes and maintains some of the exporter request state, including:
 * hashed state, saml attributes
 * b. Does saml assertion validation
 */
#include "zpath_lib/zpath_instance.h"
#include "zpn/zpn_idp_cert.h"
#include "zpn/zpn_idp.h"
#include "zsaml/zsaml.h"
#include "fohh/fohh.h"
#include "zpn/zpn_rule.h"
#include "zpn/zpn_session_aggregate_store.h"
#include "zpn/zpn_application.h"
#include "exporter/zpn_sra_application.h"
#include "zpn/zpn_application_domain.h"
#include "zpn/zpn_application_group_application_mapping.h"
#include "zpn/zpn_application_group.h"
#include "zpn/zpn_broker_policy.h"
#include "zpn/zpn_client_less.h"
#include "zpn/zpn_policy_engine.h"
#include "zpn/zpn_policy_set.h"
#include "zpn/zpn_saml_attrs.h"
#include "zpn/zpn_scim_user.h"
#include "zpn/zpn_scim_user_attribute.h"
#include "zpn/zpn_scim_attr_header.h"
#include "zpn/zpn_scim_group.h"
#include "zpn/zpn_scim_user_group.h"
#include "zpn/zpn_userdb_common.h"
#include "zpath_lib/zpath_et_userdb.h"
#include "zpath_lib/zpath_et_customer_userdb.h"
#include "zpn/zpn_customer.h"
#include "zpn/zpn_scope.h"
#include "zpn/zpn_scope_ready.h"
#include "zpn/zpn_approval.h"
#include "zpn/zpn_approval_mapping.h"
#include "zpn/zpn_scope.h"
#include "zpn/zpn_scope_engine.h"
#include "zpn/zpn_privileged_capabilities.h"
#include "zpath_lib/zpath_local.h"
#include "zpn_ot/zpn_ot.h"
#include "exporter/zpn_sra_portal.h"
#include "zpn/zpn_privileged_portal_rule.h"
#include "zpn/zpn_policy_engine_private.h"
#include "zpn/zpn_managed_browser_profile.h"
#include "zpn/zpn_managed_chrome_extension.h"

#include "zpn/zpn_customer_config.h"
#include "zpn/zpn_app_protection_csp_profile.h"
#include "exporter/exporter.h"
#include "exporter/exporter_request_util.h"
#include "exporter/exporter_guac_util.h"
#include "exporter/exporter_guac_util_compiled.h"
#include "exporter/exporter_request_policy_state.h"
#include "exporter/exporter_private.h"
#include "exporter/exporter_request.h"
#include "exporter/exporter_privileged_policy.h"
#include "exporter/exporter_guac_sess_sharing.h"
#include "exporter/exporter_user_portal_api.h"

#include "parson/parson.h"
#include "zpath_lib/zpath_config_override_keys.h"

#define IDPS_MAX_SIZE 100
#define ASSERTION_MAX_LEN 1024*8

// Since these values are static between users and read only
// we can just alloc them once here and use them for any user who needs them
// Removes the need to attach these values directly to the request
static char *EXPORTER_CLIENT_TYPE = "CLIENT_TYPE|id|zpn_client_type_exporter";
//TODO: static char *MACHINE_TUNNEL_CLIENT_TYPE = "CLIENT_TYPE|id|zpn_client_type_machine_tunnel";

static struct argo_structure_description* exporter_request_policy_state_stats_description;
struct argo_structure_description* exporter_guac_session_recording_metadata_description;
struct argo_structure_description* exporter_guac_invite_user_email_metadata_description;

static int exporter_request_policy_state_ready;

struct exporter_request_policy_state_stats_s {                             /* _ARGO: object_definition */
    uint64_t create;                                                            /* _ARGO: integer */
    uint64_t reset;                                                             /* _ARGO: integer */

    uint64_t init_done;                                                         /* _ARGO: integer */
    uint64_t init_policy_state_failed;                                          /* _ARGO: integer */
    uint64_t request_failed_init_not_done;                                      /* _ARGO: integer */

    uint64_t request;                                                           /* _ARGO: integer */
    uint64_t request_internal_auth_validation_async;                            /* _ARGO: integer */
    uint64_t request_internal_auth_validation_err;                              /* _ARGO: integer */
    uint64_t request_internal_auth_validation_success;                          /* _ARGO: integer */

    uint64_t request_user_state_collection_async;                               /* _ARGO: integer */
    uint64_t request_user_state_collection_fail_err;                            /* _ARGO: integer */

    uint64_t request_auth_assertion_validation;                                 /* _ARGO: integer */
    uint64_t request_auth_assertion_validation_async;                           /* _ARGO: integer */
    uint64_t request_auth_assertion_validation_fail_expired;                    /* _ARGO: integer */
    uint64_t request_auth_assertion_validation_fail_memory;                     /* _ARGO: integer */
    uint64_t request_auth_assertion_validation_fail_err;                        /* _ARGO: integer */
    uint64_t request_auth_assertion_validation_success;                         /* _ARGO: integer */

    uint64_t request_auth_reauth_check_success;                                 /* _ARGO: integer */
    uint64_t request_auth_reauth_check_async;                                   /* _ARGO: integer */
    uint64_t request_auth_reauth_check_fail_expired;                                    /* _ARGO: integer */
}stats;
#include "exporter/exporter_request_policy_state_compiled_c.h"

int g_exporter_ot_mode = 0;

int
exporter_request_policy_state_init()
{
    int result;
    /*
     * Initialize SCIM Data Tables
     * ORDER IS IMPORTANT!
     * 1. Init each table inside the userdb that we need an index for
     * 2. Init the et_userdb as the row callbacks require all the index information from 1 to function
     */
    result = zpn_application_init(NULL, 0, 1/*  We are an exporter */,
                                  1 /* Fully load application table in the event that we ever have single tenant exporter? */,
                                  0 /* Support pattern match, exporter Multi match support not added */,
                                  0 /* Register with zpath_table */);
    if (result) {
        __sync_add_and_fetch_8(&stats.init_policy_state_failed, 1);
        EXPORTER_LOG(AL_ERROR, "Could not init application table: %s", zpn_result_string(result));
        return result;
    }

    result = zpn_sra_application_init(NULL, 0);
    if (result) {
        __sync_add_and_fetch_8(&stats.init_policy_state_failed, 1);
        EXPORTER_LOG(AL_ERROR, "Could not init zpn_sra_application table: %s", zpn_result_string(result));
        return result;
    }

    result = zpn_application_domain_init(NULL, 0, 0);
    if (result) {
        __sync_add_and_fetch_8(&stats.init_policy_state_failed, 1);
        EXPORTER_LOG(AL_ERROR, "Could not init user portal application domain table: %s", zpn_result_string(result));
        return result;
    }

    result = zpn_application_group_init(NULL, 0, 0 /* no fully load */, 0);
    if (result) {
        __sync_add_and_fetch_8(&stats.init_policy_state_failed, 1);
        EXPORTER_LOG(AL_ERROR, "Could not init zpn_application_group table: %s", zpn_result_string(result));
        return result;
    }

    result = zpn_scope_init(NULL, 0, 0, 0);
    if(result) {
        __sync_add_and_fetch_8(&stats.init_policy_state_failed, 1);
		EXPORTER_LOG(AL_ERROR, "Could not initialize zpn scope table: %s", zpn_result_string(result));
		return result;
    }

    result = zpn_application_group_application_mapping_init(NULL, 0, 1, 0, 0);
    if (result) {
        __sync_add_and_fetch_8(&stats.init_policy_state_failed, 1);
        EXPORTER_LOG(AL_ERROR, "Could not init zpn_application_group_application_mapping table: %s", zpn_result_string(result));
        return result;
    }

    result = zpn_scim_user_attribute_init();
    if (result) {
        __sync_add_and_fetch_8(&stats.init_policy_state_failed, 1);
        ZPN_LOG(AL_ERROR, "Could not initialize scim_user_attribute table: %s", zpn_result_string(result));
        return result;
    }

    result = zpn_scim_group_init();
    if (result) {
        __sync_add_and_fetch_8(&stats.init_policy_state_failed, 1);
        ZPN_LOG(AL_ERROR, "Could not initialize scim_group table: %s", zpn_result_string(result));
        return result;
    }

    result = zpn_scim_user_group_init();
    if (result) {
        __sync_add_and_fetch_8(&stats.init_policy_state_failed, 1);
        ZPN_LOG(AL_ERROR, "Could not initialize scim_user_group table: %s", zpn_result_string(result));
        return result;
    }

    result = zpn_scim_user_init();
    if (result) {
        __sync_add_and_fetch_8(&stats.init_policy_state_failed, 1);
        ZPN_LOG(AL_ERROR, "Could not initialize scim_user table: %s", zpn_result_string(result));
        return result;
    }

    ZPN_LOG(AL_NOTICE, "Initializing et_userdb...");
    result = zpath_et_userdb_init(zpath_global_wally, 1, 0);
    if (result) {
        __sync_add_and_fetch_8(&stats.init_policy_state_failed, 1);
        ZPN_LOG(AL_ERROR, "zpath_et_userdb_init failed: %s", zpath_result_string(result));
        return result;
    }

    ZPN_LOG(AL_NOTICE, "Initializing zpath_et_customer_userdb...");
    result = zpath_et_customer_userdb_init(NULL, 0, NULL);
    if (result) {
        __sync_add_and_fetch_8(&stats.init_policy_state_failed, 1);
        ZPN_LOG(AL_ERROR, "Initializing zpath_et_customer_userdb failed: %s",
                zpath_result_string(result));
        return result;
    }

    result = zpn_policy_set_init(NULL, 0, 0 /* no fully load */, 0);
    if (result) {
        __sync_add_and_fetch_8(&stats.init_policy_state_failed, 1);
        EXPORTER_LOG(AL_ERROR, "Could not initialize zpn policy set table: %s", zpn_result_string(result));
        return result;
    }

    result = zpn_rule_init(NULL, 0, 0 /* no fully load */, 0);
    if (result) {
        __sync_add_and_fetch_8(&stats.init_policy_state_failed, 1);
        EXPORTER_LOG(AL_ERROR, "Could not initialize zpn rule table: %s", zpn_result_string(result));
        return result;
    }
    result = zpn_rule_condition_set_init(NULL, 0, 0 /* no fully load */, 0);
    if (result) {
        __sync_add_and_fetch_8(&stats.init_policy_state_failed, 1);
        EXPORTER_LOG(AL_ERROR, "Could not initialize zpn condition set table: %s", zpn_result_string(result));
	return result;
    }
    result = zpn_rule_condition_operand_init(NULL, 0, 0 /* no fully load */, 0);
    if (result) {
        __sync_add_and_fetch_8(&stats.init_policy_state_failed, 1);
        EXPORTER_LOG(AL_ERROR, "Could not initialize zpn condition operand table: %s", zpn_result_string(result));
        return result;
    }

    /* DR mode is not applicable for exporter */
    result = zse_init(0);
	if (result) {
        __sync_add_and_fetch_8(&stats.init_policy_state_failed, 1);
		EXPORTER_LOG(AL_ERROR, "Could not initialize zpn scope engine table: %s", zpn_result_string(result));
		return result;
	}

    result = zpath_debug_add_allocator(&zse_allocator, "zse");
    if (result) {
        EXPORTER_LOG(AL_ERROR, "Could not add zse allocator to debug");
        return result;
    }

    result = zpn_scope_ready_init();
	if (result) {
        __sync_add_and_fetch_8(&stats.init_policy_state_failed, 1);
		EXPORTER_LOG(AL_ERROR, "Could not initialize zpn scope ready : %s", zpn_result_string(result));
		return result;
	}

    result = zpe_init(1);
    if (result) {
        __sync_add_and_fetch_8(&stats.init_policy_state_failed, 1);
        EXPORTER_LOG(AL_ERROR, "Could not initialize zpn policy engine table: %s", zpn_result_string(result));
        return result;
    }

    result = zpath_debug_add_allocator(&zpe_allocator, "zpe");
    if (result) {
        EXPORTER_LOG(AL_ERROR, "Could not add zpe allocator to debug");
        return result;
    }

    result = zpn_saml_attrs_init(NULL, 0, 0 /* no fully load */, 0);
    if (result) {
        __sync_add_and_fetch_8(&stats.init_policy_state_failed, 1);
        ZPN_LOG(AL_ERROR, "Could not initialize zpn saml attr table: %s", zpn_result_string(result));
        return result;
    }

    result = zpn_scim_attr_header_init(NULL, 0, 0);
    if (result) {
        __sync_add_and_fetch_8(&stats.init_policy_state_failed, 1);
        EXPORTER_LOG(AL_ERROR, "Could not initialize zpn scim attr header table: %s", zpn_result_string(result));
        return result;
    }

    if (!(exporter_request_policy_state_stats_description =
                  argo_register_global_structure(EXPORTER_REQUEST_POLICY_STATE_STATS_S_HELPER))) {
        return ZPATH_RESULT_ERR;
    }

    result = zpn_idp_cert_init(NULL, 0, 0, 0);
    if (result) {
        __sync_add_and_fetch_8(&stats.init_policy_state_failed, 1);
        ZPN_LOG(AL_ERROR, "Could not init zpn_idp_cert table");
        return result;
    }

    result = zpn_approval_init(NULL, 0, 0 /* no fully load */, 0);
    if (result) {
        __sync_add_and_fetch_8(&stats.init_policy_state_failed, 1);
        ZPN_LOG(AL_ERROR, "Could not init zpn_approval table");
        return result;
    }

    result = zpn_approval_mapping_init(NULL, 0, 0 /* no fully load */, 0);
    if (result) {
        __sync_add_and_fetch_8(&stats.init_policy_state_failed, 1);
        ZPN_LOG(AL_ERROR, "Could not init zpn_approval_mapping table");
        return result;
    }

    if (g_exporter_ot_mode) {
        if (!(exporter_guac_session_recording_metadata_description =
                    argo_register_global_structure(EXPORTER_GUAC_SESSION_RECORDING_METADATA_HELPER))) {
            return ZPATH_RESULT_ERR;
        }
        if (!(exporter_guac_invite_user_email_metadata_description =
                    argo_register_global_structure(EXPORTER_GUAC_INVITE_USER_EMAIL_METADATA_HELPER))) {
            return ZPATH_RESULT_ERR;
        }
        result = zpn_privileged_capabilities_init(NULL, 0);
        if (result) {
            __sync_add_and_fetch_8(&stats.init_policy_state_failed, 1);
            ZPN_LOG(AL_ERROR, "Could not init zpn_privileged_capabilities table");
            return result;
        }
        if (!zpn_ot_init(zpath_service_exporter) ) {
            __sync_add_and_fetch_8(&stats.init_policy_state_failed, 1);
	    return ZPATH_RESULT_ERR;
        }

        result = exporter_privileged_policy_stats_init();
        if (result) {
            ZPN_LOG(AL_ERROR, "Could not init exporter_privileged_policy_stats");
            return result;
        }

        result = exporter_guac_sess_sharing_init();
        if (result) {
            ZPN_LOG(AL_ERROR, "Could not init exporter_guac_sess_sharing_init");
            return result;
        }
        result = zpn_privileged_portal_rule_init(NULL, 0);
        if (result) {
            __sync_add_and_fetch_8(&stats.init_policy_state_failed, 1);
            ZPN_LOG(AL_ERROR, "Could not init zpn_privileged_portal_rule table");
            return result;
        }
    }

    result = zpn_app_protection_csp_profile_init(NULL, 0);
    if (result) {
        __sync_add_and_fetch_8(&stats.init_policy_state_failed, 1);
        ZPN_LOG(AL_ERROR, "Could not init zpn_app_protection_csp_profile table");
        return result;
    }

    ZPN_LOG(AL_NOTICE, "Initializing zpn_managed_browser_profile_init");
    result = zpn_managed_browser_profile_init(NULL, 0, 0, 0);
    if (result) {
        __sync_add_and_fetch_8(&stats.init_policy_state_failed, 1);
        ZPN_LOG(AL_ERROR, "zpn_managed_browser_profile init failed: %s", zpath_result_string(result));
        return result;
    }
    ZPN_LOG(AL_NOTICE, "Initializing zpn_managed_browser_profile_init....Complete!");

    ZPN_LOG(AL_NOTICE, "Initializing zpn_managed_chrome_extension_init");
    result = zpn_managed_chrome_extension_init(NULL, 0, 0, 0);
    if (result) {
        __sync_add_and_fetch_8(&stats.init_policy_state_failed, 1);
        ZPN_LOG(AL_ERROR, "zpn_managed_chrome_extension_init init failed: %s", zpath_result_string(result));
        return result;
    }
    ZPN_LOG(AL_NOTICE, "Initializing zpn_managed_chrome_extension_init.....Complete!");

    __sync_add_and_fetch_8(&stats.init_done, 1);
    exporter_request_policy_state_ready = 1;

    EXPORTER_LOG(AL_INFO, "Initializing exporter policy state... done!");
    return ZPN_RESULT_NO_ERROR;
}

void
exporter_request_policy_state_free_and_reset(struct exporter_request* request)
{
    __sync_add_and_fetch_8(&stats.reset, 1);

    if (request->policy_state != NULL) {
        request->policy_state->assertion_xml_len = 0;
        if (request->policy_state->assertion_xml) {
            EXPORTER_FREE(request->policy_state->assertion_xml);
            request->policy_state->assertion_xml = NULL;
        }

        if (request->policy_state->general_state_hash != NULL) {
            zhash_table_free(request->policy_state->general_state_hash);
            request->policy_state->general_state_hash = NULL;
        }

        if (request->policy_state->saml_state_hash != NULL) {
            zhash_table_free(request->policy_state->saml_state_hash);
            request->policy_state->saml_state_hash = NULL;
        }

        if (request->policy_state->scim_state_hash != NULL) {
            zhash_table_free(request->policy_state->scim_state_hash);
            request->policy_state->scim_state_hash = NULL;
        }

        if (request->policy_state->user_email) {
            request->policy_state->user_email = NULL;
        }

        EXPORTER_FREE(request->policy_state);
        request->policy_state = NULL;
    }
}


/*
 * Timeout policy check and determine if reauth is needed.
 * If timeout policy check fails, exporter will pick up the shortest timeout for reauth evaluation.
 */
static int64_t
exporter_request_policy_state_check_reauth(struct exporter_request *request)
{
    int res;
    int64_t reauth_timeout;
    char tm_left_str[128];
    const char *app_fqdn;

    /* unified-portal - In reauth part origurl is always external FQDN, domain lookup will fail and we will get NULL
     *
     * We must always lookup internal app from origurl, and must code walkthrough exporter code to see orig_domain is always name of internal app
     */

    /* The final app-server domain to be figured out. Incase of auth(d.zpa-auth.net) or similar endpoints,
     *  orig_domain  parsed from the URL will carry the application name. Figure out the admin configured
     *  app-name from the managed ba app-name. If not managed app, this is no-op
     */
    app_fqdn = exporter_domain_fetch_mapped_domain(request->orig_domain);

    /* CSP_INTEGRATION */
    const char *domain = (request->sra_host_fqdn) ? request->sra_host_fqdn :
            request->is_csp_enabled ? app_fqdn: request->conn->exporter_domain->cfg_domain;

    /* For PRA consoles, all policies including reauth are matched strictly against domain and port */
    if (request->conn->exporter_domain->is_ot && request->sra_host_fqdn) {
        res = zpn_broker_policy_locked_link_policy_pra_check(request->is_csp_enabled ? request->orig_customer_gid : request->scope_gid,
                                                     request->attr,
                                                     request->conn->exporter_domain->is_ot,
                                                     request->policy_state->idp_gid,
                                                     zpe_policy_type_reauth,
                                                     domain,
                                                     request->policy_state->general_state_hash,
                                                     request->policy_state->saml_policy_enabled ? request->policy_state->saml_state_hash : NULL,
                                                     request->policy_state->scim_policy_enabled ? request->policy_state->scim_state_hash : NULL,
                                                     NULL, NULL,
                                                     NULL,
                                                     request->sra_host_port,
                                                     NULL,
                                                     &reauth_timeout,
                                                     NULL,
                                                     NULL,
                                                     exporter_request_wally_callback,
                                                     request,
                                                     NULL,
                                                     0);
    } else {
        res = zpn_broker_policy_locked_link_policy_check(request->is_csp_enabled ? request->orig_customer_gid : request->scope_gid,
                                                     request->attr,
                                                     request->policy_state->idp_gid,
                                                     zpe_policy_type_reauth,
                                                     domain,
                                                     request->is_csp_enabled ? request->orig_application_port_he : request->conn->exporter_domain->application_port_he,
                                                     request->policy_state->general_state_hash,
                                                     request->policy_state->saml_policy_enabled ? request->policy_state->saml_state_hash : NULL,
                                                     request->policy_state->scim_policy_enabled ? request->policy_state->scim_state_hash : NULL,
                                                     NULL,
                                                     &reauth_timeout,
                                                     NULL,
                                                     NULL,
                                                     exporter_request_wally_callback,
                                                     request,
                                                     NULL);

    }
    if (res == ZPATH_RESULT_ASYNCHRONOUS) {
        EXPORTER_DEBUG_REQUEST_POLICY_STATE("%s: checking timeout policy for customer link: %s asynchronously - async_count: (%d->%d)",
                                             request->name, domain, request->async_count, request->async_count + 1);
        __sync_fetch_and_add_4(&(request->async_count), 1);
        return res;
    } else if (res != ZPATH_RESULT_NO_ERROR) {
        int ret;
        if (request->conn->exporter_domain->is_user_portal) {
            ret = zpn_rule_customer_get_default_rule_reauth(request->conn->exporter_domain->customer_gid, &reauth_timeout);
            if (ret) {
                reauth_timeout = ZPN_DEFAULT_SAML_EXPIRE_TIME;
                EXPORTER_LOG(AL_ERROR, "%s: Failed to get default rule timeout for customer_gid: %"PRId64", reason: %s, applying default timeout...",
                             request->name, request->conn->exporter_domain->customer_gid, zpn_result_string(ret));
            }
        } else if (request->conn->exporter_domain->is_ot && !request->sra_host_fqdn) {
            ret = zpn_rule_scope_get_default_rule_shortest_reauth(request->scope_gid, &reauth_timeout);
            if (ret) {
                reauth_timeout = ZPN_DEFAULT_SAML_EXPIRE_TIME;
                EXPORTER_LOG(AL_ERROR,"%s: Failed to get default rule timeout for customer: %"PRId64" scope: %"PRId64", reason: %s, applying default timeout...",
                             request->name, request->conn->exporter_domain->customer_gid, request->scope_gid, zpn_result_string(ret));
            }
        } else {
            EXPORTER_LOG(AL_ERROR, "%s: Timeout policy check failed for domain: %s, reason: %s, applying shortest timeout...",
                                    request->name, domain, zpn_result_string(res));

            ret = zpn_rule_scope_get_shortest_reauth(
                    request->is_csp_enabled ? request->orig_customer_gid : request->scope_gid,
                    &reauth_timeout);

            if (ret) {
                EXPORTER_LOG(AL_ERROR, "%s: Failed to get shortest timeout for customer_gid: %ld, reason: %s, applying default timeout...",
                                request->name,
                                (long)(request->is_csp_enabled ? request->orig_customer_gid : request->conn->exporter_domain->customer_gid),
                                zpn_result_string(ret));
                reauth_timeout = ZPN_DEFAULT_SAML_EXPIRE_TIME;
            }
        }
    }

    // For PRA proctoring session, record reauth value in the request
    if (request->is_proxy_conn) {
        request->proxy_conn_reauth_timeout = reauth_timeout + (reauth_timeout > 0 ? 5*60 : 0);
        EXPORTER_DEBUG_REQUEST_POLICY_STATE("%s: Setting timeout for proctoring session to %"PRId64"",
                                    request->name, request->proxy_conn_reauth_timeout);
    }

    int64_t now_s = epoch_s();
    int64_t elapsed = now_s - request->policy_state->saml_not_before;


    /*
     * Reverify this if this is as per design or bug
     * As soon as SAML is created saml_not_before is already 5 minutes behind
     * Due to this when we come here elapsed time is already 300 seconds
     * So if we set reauth_timeout to 300 for testing, authentication is already expired !!
     * For UT set it to 360 seconds
     */
    //reauth_timeout = 6 * 60;// Enable for Unit Testing only

    const char *centos_logo = "/img/centos-logo.png";
    int is_expired = strcmp(request->url, centos_logo) == 0 ? 1:0;

    is_expired = 0;
    if (is_expired || ((reauth_timeout > 0) && (now_s > (request->policy_state->saml_not_before + reauth_timeout)))) {
        EXPORTER_DEBUG_REQUEST_POLICY_STATE("%s: AUTHENTICATION EXPIRED: now: %ld, saml_not_before: %ld, timeout: %ld, elapsed: %ld",
                request->name, (long) now_s,
                (long)request->policy_state->saml_not_before, (long)reauth_timeout, (long)elapsed);
        return ZPN_RESULT_EXPIRED;
    } else {
        (reauth_timeout > 0) ? snprintf(tm_left_str, 128, ", left: %ld",(long)(reauth_timeout - elapsed)):
                                           snprintf(tm_left_str, 128, " ");
        EXPORTER_DEBUG_REQUEST_POLICY_STATE("%s: AUTHENTICATION TIME LEFT: now: %ld, saml_not_before: %ld, timeout: %ld, elapsed: %ld %s",
                request->name, (long) now_s,
                (long)request->policy_state->saml_not_before, (long)reauth_timeout, (long)elapsed, tm_left_str);
    }

    return ZPATH_RESULT_NO_ERROR;
}

int
exporter_request_fetch_csp_policy_action(struct exporter_request *request,
                                         int64_t *matched_action_id,
                                         enum zpe_access_action *matched_action,
                                         int64_t *fetch_app_id)
{
    int res;
    const char *policy_domain;

    /* We come here even if assertion fails so request->policy_state can be 0x0 */

    /*
     * If this is case of fromsp, we have 3 options
     *  - Capture the fingerprint with no policy
     *    - Even if we continue we will eventually fail since assertion is
     *      not there and redirected to authsp
     *  - Or redirect to authsp domain since user is giving incorrect
     *    assertion
     *  - Or bypass fingerprint, not a good idea what if object store was down ?
     *    If we do this then all later requests will never have mbfp
     *    if this was coming from SSO
     *
     * So we simply return with ERR here and caller takes care of handling the same
     */

    policy_domain = exporter_domain_fetch_mapped_domain(request->orig_domain);

    if (NULL == request->policy_state) {
        EXPORTER_DEBUG_CSP("%s: CSP_POLICY policy state null url: [%s] async for csp_policy domain: [%s]",
                request->name, request->log.url, policy_domain);
        return ZPATH_RESULT_ERR;
    }

    /* CSP_POLICY - Get domain from origurl but for console we need to get from sra_host_fqdn */
    const char *domain = (request->sra_host_fqdn) ? request->sra_host_fqdn :
                          (request->is_csp_enabled ? policy_domain: request->conn->exporter_domain->cfg_domain);

    res = zpn_broker_csp_policy_check(
            request->is_csp_enabled ? request->orig_customer_gid : request->conn->exporter_domain->customer_gid,
            request->attr,
            request->policy_state->idp_gid,
            zpe_policy_type_csp_policy,
            domain,
            request->is_csp_enabled ? request->orig_application_port_he : request->conn->exporter_domain->application_port_he,
            request->policy_state->general_state_hash,
            request->policy_state->saml_policy_enabled ? request->policy_state->saml_state_hash : NULL,
            request->policy_state->scim_policy_enabled ? request->policy_state->scim_state_hash : NULL,
            matched_action,
            NULL,
            NULL,
            matched_action_id,
            exporter_request_wally_callback,
            request,
            fetch_app_id);

    if (res == ZPATH_RESULT_ASYNCHRONOUS) {
        EXPORTER_DEBUG_CSP("%s: Check CSP policy for customer link,(%s)  async_count: (%d->%d)",
                request->name, domain, request->async_count, request->async_count + 1);
        exporter_set_async_state_and_inc_count(request, async_state_csp_fetch_policy);
        /* CSP_INTEGRATION - State must be set by caller to resume */
        return res;
    } else if (res != ZPATH_RESULT_NO_ERROR) {
        EXPORTER_DEBUG_REQUEST_POLICY_STATE("%s: Get CSP policy for %s without rebuild returned %s", request->name,
                domain, zpn_result_string(res));
        return res;
    }

    EXPORTER_LOG(AL_INFO, "%s, CSP_POLICY evaluation [domain:%s -> %s] returned matched_action = %s, result = %s, matched_action_id: %"PRId64" fetch_app_id: %"PRId64"",
                 request->name, request->orig_domain, domain,
                 zpe_access_action_string(*matched_action),
                 zpath_result_string(res),
                 *matched_action_id,
                 *fetch_app_id);
    return res;
}

extern int zpn_customer_config_get_by_customer_gid(int64_t scope_gid,
                                            struct zpn_customer_config **configs,
                                            size_t *count);

int is_csp_periodic_fingerprint_enabled(struct exporter_request *request)
{
    struct zpn_customer_config *configs[ZPATH_MAX_TABLE_ROWS_GET];
    size_t count = ZPATH_MAX_TABLE_ROWS_GET;
    int i;
    int res;
    const char key[] = {"csp.session.continous_fingerprint.timer"};
    int64_t scope_gid = request->conn->exporter_domain->customer_gid;

    /* This featured is disabled for the customer by ops */
    //if (!config_override_sess_term_on_reauth) {
    //    return 0;
    //}

	/* Wally lookup from zpn_customer_config table */
	res = zpn_customer_config_load(request->conn->exporter_domain->customer_gid,
			exporter_request_wally_callback,
			request,
			0);

	if (res == ZPN_RESULT_ASYNCHRONOUS) {
		__sync_fetch_and_add_4(&(request->async_count), 1);
		request->async_state = async_state_reprocess;
		EXPORTER_DEBUG_FILE_TRANSFER( "%s: Asynchronous wally lookup for zpn_customer_config %ld",
				request->name, (long)request->conn->exporter_domain->customer_gid);
		return res;
	} else if (res != ZPATH_RESULT_NO_ERROR) {
		EXPORTER_LOG(AL_ERROR, "%s: lookup failed for zpn_customer_config %ld, err = %s",
				request->name, (long)request->conn->exporter_domain->customer_gid,
				zpath_result_string(res));
		return res;
	}

    res = zpn_customer_config_get_by_customer_gid(scope_gid, configs, &count);

    if (res != WALLY_RESULT_NO_ERROR) {
        EXPORTER_DEBUG_CSP("scope: %ld failed to get zpn_customer_config, res=%s",
                                  (long) scope_gid, zpath_result_string(res));
        return 0;
    }

    for (i = 0; i < count; i++) {
        if (strcasecmp(configs[i]->key, key) == 0 && configs[i]->deleted == 0) {
            break;
        }
    }

    if (i >= count) {
        EXPORTER_DEBUG_CSP("scope: %ld key %s is not available or deleted from wally",
                                  (long) scope_gid, key);
        return 0;
    }

    EXPORTER_DEBUG_CSP("%s: CSP EXPIRY Reading periodic fp: [%s]", request->name, configs[i]->value);

    return 1;
}

/*

   PROFILE FORMAT
   criteria_flags      | {"fingerPrintCriteria":{"browser":{"browser_name":true,"browser_version":true,"browser_eng":true,"browser_eng_ver":true,"is_local_storage":true,"is_sess_storage":true,"is_cookie":true,"plugin":true,"flash_ver":true,"silverlight_ver":true,"ja3":false,"mime":true,"fp_usr_agent_str":true,"canvas":true},"system":{"cpu_arch":true,"os_name":true,"os_version":true,"curr_screen_resolution":true,"avail_screen_resolution":true,"tz":true,"usr_lang":true,"sys_lang":true,"monitor_mobile":true,"mobile_dev_type":true,"font":true,"java_ver":true},"location":{"lat":true,"lon":true},"collect_location":true}}

   CONFIG OVERRIDE FORMAT
   config_value_str  | {"name":"Zs Recommended profile","description":"Zs Recommended profile","criteria":{"fingerPrintCriteria":{"browser":{"ja3":false,"canvas":true,"browser_name":true,"browser_version":true,"browser_eng":true,"browser_eng_ver":true,"is_local_storage":true,"is_sess_storage":true,"is_cookie":true,"fp_usr_agent_str":true,"mime":true,"plugin":true,"flash_ver":true,"silverlight_ver":true},"system":{"cpu_arch":true,"os_name":true,"os_version":true,"curr_screen_resolution":true,"avail_screen_resolution":true,"tz":true,"usr_lang":true,"sys_lang":true,"monitor_mobile":true,"mobile_dev_type":true,"font":true,"java_ver":true},"location":{"lat": false,"lon": false},"collect_location": false}}}

 */

int get_fingerprint_timeout(const char *body, const char* key) {
    JSON_Object                   *root_object = NULL;
    JSON_Value                    *root_value  = NULL;
    JSON_Value                    *json_data = NULL;
    int timeout = 0;

    root_value = json_parse_string(body);
    if (json_value_get_type(root_value) != JSONObject) {
        EXPORTER_DEBUG_CSP("[CSP_PROFILE] failed to parse json string\n");
        goto cleanup;
    }

    root_object = json_value_get_object(root_value);
    if (!root_object) {
        EXPORTER_DEBUG_CSP("[CSP_PROFILE] failed to get json object\n");
        goto cleanup;
    }

    json_data = json_object_dotget_value(root_object, key);

    if (json_data) {
        if (JSONNumber == json_value_get_type(json_data)) {
            timeout = (int)json_value_get_number(json_data);

            /* Assuming 1 week max 604800 seconds */
            if (timeout < 0 || timeout > (7*24*3600)) {
                EXPORTER_DEBUG_CSP("[CSP_PROFILE] timeout out of range value: [%d] type: [%d]\n",
                        timeout, json_value_get_type(json_data));
                timeout = 0;
            }
        } else if (JSONString == json_value_get_type(json_data)) {
            char *str_timeout = (char *)json_value_get_string(json_data);
            if (str_timeout) timeout = atoi(str_timeout);

            /* Assuming 1 week max 604800 seconds */
            if (timeout < 0 || timeout > (7*24*3600)) {
                EXPORTER_DEBUG_CSP("[CSP_PROFILE] timeout out of range value: [%d] type: [%d]\n",
                        timeout, json_value_get_type(json_data));
                timeout = 0;
            }
        } else {
            EXPORTER_DEBUG_CSP("[CSP_PROFILE] wrong type for key fingerprint timeout\n");
            goto cleanup;
        }
    } else {
        EXPORTER_DEBUG_CSP("[CSP_PROFILE] failed to get key for fingerprint timeout\n");
        goto cleanup;
    }

cleanup:
    if (root_value)
        json_value_free(root_value);

    return timeout;
}

char *get_default_csp_criteria()
{
    char *config_val = NULL;

    config_val = zpath_config_override_get_config_str(APP_PROTECTION_CSP_PROFILE,
                                                      &config_val,
                                                      APP_PROTECTION_CSP_PROFILE_DEFAULT_CRITERIA,
                                                      (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                      (int64_t)0); // Argument list terminator

    if (!config_val) {
        EXPORTER_LOG(AL_ERROR, "[CSP_PROFILE]: Failed to read profile criteria <%s>", APP_PROTECTION_CSP_PROFILE);
        config_val = APP_PROTECTION_CSP_PROFILE_DEFAULT_CRITERIA;
    }

    return config_val;
}

int
exporter_request_fetch_csp_profile_mask(struct exporter_request *request,
        int64_t *criteria_flags_mask ,
        int64_t *profile_gid)
{
    int res = ZPATH_RESULT_NO_ERROR;
    int is_active = 0;
    struct zpn_app_protection_csp_profile *profiles[10];
    size_t profile_count = 10;
    int ii = 0;
    char *criteria_flags = NULL;
    int64_t customer_gid = request->is_csp_enabled ? request->orig_customer_gid : request->conn->exporter_domain->customer_gid;

    res = zpn_app_protection_csp_profile_get_by_customer_gid(
            customer_gid,
            &profiles[0],
            &profile_count,
            exporter_request_wally_callback,
            request, 0);

    EXPORTER_DEBUG_REQUEST_POLICY_STATE("%s: Fetched %ld profiles for given customer_gid: %"PRId64" ",
            request->name,
            profile_count,
            customer_gid);

    if (res == ZPN_RESULT_ASYNCHRONOUS) {
        EXPORTER_DEBUG_CSP("%s: Asynchronous lookup of table zpn_app_protection_csp_profile, async_count: (%d->%d)",
                request->name, request->async_count, request->async_count + 1);

        exporter_set_async_state_and_inc_count(request, async_state_csp_fetch_profile);

        return res;
    }

    /* Read from config override table */
    if (res != ZPATH_RESULT_NO_ERROR) {
        /* In case of any error just get the config mask
           from config override table. The profile id will be GID 1*/
        *criteria_flags_mask = get_default_csp_bitmask();
        *profile_gid = ZPATH_GLOBAL_CONFIG_OVERRIDE_GID;

        criteria_flags = get_default_csp_criteria();
        request->csp_timeout = get_fingerprint_timeout(criteria_flags,
                "criteria.fingerPrintCriteria.fingerprint_timeout");

        EXPORTER_DEBUG_REQUEST_POLICY_STATE("%s: Profile fetch returned error: %s setting profile to default: %"PRId64" ",
				request->name,
                zpath_result_string(res),
                *criteria_flags_mask);

        return ZPATH_RESULT_NO_ERROR;
    }

    for (ii = 0; ii < profile_count; ii++) {
        EXPORTER_DEBUG_REQUEST_POLICY_STATE("%s: Fetched Profiles for given customer_gid: %"PRId64" , %"PRId64", %"PRId64", %s, %d ",
                request->name,
                profiles[ii]->customer_gid,
                profiles[ii]->gid,
                profiles[ii]->criteria_flags_mask,
                profiles[ii]->name, profiles[ii]->is_active);

        if (!profiles[ii]->deleted && profiles[ii]->is_active) {
            *criteria_flags_mask = profiles[ii]->criteria_flags_mask;
            *profile_gid = profiles[ii]->gid;

            criteria_flags = profiles[ii]->criteria_flags;
            request->csp_timeout = get_fingerprint_timeout(criteria_flags,
                    "fingerPrintCriteria.fingerprint_timeout");

            is_active = 1;
            break;
        }
    }

    if (!is_active) {
        // Fetch from default profile in config override
        *criteria_flags_mask = get_default_csp_bitmask();
        *profile_gid = ZPATH_GLOBAL_CONFIG_OVERRIDE_GID;

        /* Config override to see if fingerprint timeout is enabled */
        criteria_flags = get_default_csp_criteria();
        request->csp_timeout = get_fingerprint_timeout(criteria_flags,
                "criteria.fingerPrintCriteria.fingerprint_timeout");
        }

    EXPORTER_DEBUG_REQUEST_POLICY_STATE("%s: Profile bitmask for customer profile_mask: %"PRId64" profile_gid: %"PRId64" ",
            request->name,
            *criteria_flags_mask,
            *profile_gid);

    return ZPATH_RESULT_NO_ERROR;
}

static int64_t
exporter_request_policy_state_get_assertion_expire_interval()
{
    /* With ET-30402, exporter will be able to evaluate timeout policy.
     * Hence we set the saml expire timeout to be as long as possible (100 years...)
     * Refer to exporter_request_policy_state_check_reauth() for reauth timeout handling.
     */
    return 100l*365*86400;
}

char*
exporter_request_policy_state_get_assertion_xml(struct exporter_request_policy_state* state)
{
    return state->assertion_xml;
}

int
exporter_request_policy_state_get_assertion_xml_len(struct exporter_request_policy_state* state)
{
    return state->assertion_xml_len;
}

/* For OT, attach saml attribute nameid if not already in the existing attribute list */
static void exporter_request_policy_state_attach_attr_nameid(struct exporter_request *request)
{
    char attribute_string[2000];
    size_t attribute_string_len;
    struct exporter_request_policy_state *state = request->policy_state;

    if (!request->conn->exporter_domain->is_ot) return;

    attribute_string_len = sxprintf(attribute_string,
                                    attribute_string + sizeof(attribute_string),
                                    "%ld|SAML|%s|%s",
                                    (long) state->idp_gid,
                                    "nameid",
                                    state->user_email);

    if (zhash_table_lookup(state->saml_state_hash, attribute_string, strlen(attribute_string), NULL)) return;
    zhash_table_store(state->saml_state_hash,
                      attribute_string,
                      attribute_string_len,
                      0,
                      state->general_state_hash);

}

/*
 * Attaches saml attrs to the hashed state in exporter request so that we can perform policy evaluation
 * on the user's request
 */
static int
exporter_request_policy_state_attach_saml_attrs(struct exporter_request *request,
                                                char *entity_id,
                                                char *attribute_names[],
                                                char *attribute_values[],
                                                size_t attribute_count,
                                                int is_oneidentity_auth)
{
    int res;
    struct zpn_idp *idps[IDPS_MAX_SIZE];
    size_t idp_count = IDPS_MAX_SIZE;

    struct exporter_request_policy_state *state = request->policy_state;

    char idp_gid_str[100];
    size_t idp_gid_str_len;

    // We have already fully processed the attributes and attached idp information
    // We don't want to reprocess everything if the user has all their attributes and
    // has their idp information attached - so just move on
    // Fully processed means:
    // - idp_gid is attached
    // - all assertion strings were attached
    if (state->saml_attr_attach_finished) {
        return ZPN_RESULT_NO_ERROR;
    }

    // If we had were called in a partial state things may have changed
    // de-allocate the old state hash
    if (state->general_state_hash) {
        zhash_table_free(state->general_state_hash);
    }
    state->general_state_hash = zhash_table_alloc(&exporter_allocator);

    if (state->saml_state_hash) {
        zhash_table_free(state->saml_state_hash);
    }
    state->saml_state_hash = zhash_table_alloc(&exporter_allocator);

    if (state->general_state_hash == NULL || state->saml_state_hash == NULL) {
        EXPORTER_LOG(AL_ERROR, "%s: Policy State Authentication: Cannot allocate string hash", request->name);
        return ZPATH_RESULT_NO_MEMORY;
    }

    // user must be at least on BA to get here so we will give them that client type
    // *** do this when allocating general state hash
    if (zhash_table_store(state->general_state_hash,
                        EXPORTER_CLIENT_TYPE,
                        strlen(EXPORTER_CLIENT_TYPE),
                        0,
                        state->general_state_hash)) {
        EXPORTER_LOG(AL_ERROR, "%s: could not add exporter client type to exporter request state hash", request->name);
        return ZPATH_RESULT_ERR;
    }

    state->idp_gid = 0;

    res = zpn_idp_get_customer_gid(request->is_csp_enabled ? request->orig_customer_gid : request->conn->exporter_domain->customer_gid,
                                   idps,
                                   &idp_count,
                                   exporter_request_wally_callback,
                                   request,
                                   fohh_get_current_thread_id());

    if (res == ZPN_RESULT_NO_ERROR) {
        size_t idps_index;
        for (idps_index = 0; idps_index < idp_count; idps_index++) {
            if (idps[idps_index]->entity_id &&
                idps[idps_index]->sso_type_count &&
                0 == strcmp(idps[idps_index]->entity_id, entity_id)) {
                int idp_type_i = 0;
                for (idp_type_i = 0; idp_type_i < idps[idps_index]->sso_type_count; idp_type_i++) {
                    if ((strcasecmp(idps[idps_index]->sso_type[idp_type_i], "user") == 0) &&
                         zpn_idp_oneidentity_hosted_validate(is_oneidentity_auth, idps[idps_index])) {
                        state->idp_gid = idps[idps_index]->gid;
                        state->saml_policy_enabled = !idps[idps_index]->disable_saml_based_policy;
                        state->scim_policy_enabled =
                                idps[idps_index]->scim_enabled && idps[idps_index]->enable_scim_based_policy;
                        EXPORTER_DEBUG_REQUEST_POLICY_STATE("%s: Updating exporter state idp_gid to %"PRId64" for customer: %"PRId64" 1ID_auth: %s SCIM: %d",
                                    request->name, state->idp_gid,
                                    request->is_csp_enabled ? request->orig_customer_gid : request->conn->exporter_domain->customer_gid,
                                    is_oneidentity_auth ? "Yes" : "No", state->scim_policy_enabled);
                        break;
                    }
                }
                if (idp_type_i < idps[idps_index]->sso_type_count) break;
            }
        }

        if (state->idp_gid) {
            size_t attribute_index;

            idp_gid_str_len = sxprintf(idp_gid_str, idp_gid_str + sizeof(idp_gid_str), "IDP|%ld", (long) state->idp_gid);

            // user must have an IDP or we would have failed
            if (zhash_table_store(state->general_state_hash,
                                idp_gid_str,
                                idp_gid_str_len,
                                0,
                                state->general_state_hash)) {
                EXPORTER_LOG(AL_ERROR, "%s: could not add idp to exporter request state hash", request->name);
                return ZPATH_RESULT_ERR;
            }

            for (attribute_index = 0; attribute_index < attribute_count; attribute_index++) {
                /* Add all these entries into the client hash table */
                char attribute_string[ASSERTION_MAX_LEN];
                size_t attribute_string_len;

                zpath_downcase(attribute_names[attribute_index]);
                zpath_downcase(attribute_values[attribute_index]);

                attribute_string_len = sxprintf(attribute_string,
                                       attribute_string + sizeof(attribute_string),
                                       "%ld|SAML|%s|%s",
                                       (long) state->idp_gid,
                                       attribute_names[attribute_index],
                                       attribute_values[attribute_index]);


                EXPORTER_DEBUG_REQUEST_POLICY_STATE_DETAIL(
                        "%s: Attribute name %d/%d = %s, Attribute value = %s, total = %s",
                        request->name,
                        (int) (attribute_index + 1),
                        (int) attribute_count,
                        attribute_names[attribute_index],
                        attribute_values[attribute_index],
                        attribute_string);

                if (zhash_table_store(state->saml_state_hash,
                                    attribute_string,
                                    attribute_string_len,
                                    0,
                                    state->general_state_hash)) {
                    EXPORTER_LOG(AL_ERROR,
                                "%s: Policy State Authentication: Cannot add attribute %s to hash; failing auth",
                                request->name,
                                attribute_string);
                    return ZPN_RESULT_NO_MEMORY;
                } else {
                    EXPORTER_DEBUG_REQUEST_POLICY_STATE("%s: SAML assertion added to user state [%s]",
                                                        request->name,
                                                        attribute_string);
                }
            }
            exporter_request_policy_state_attach_attr_nameid(request);
        } else {
            EXPORTER_LOG(AL_NOTICE, "%s: No IDP was found for request  - %s", request->name, zpn_result_string(res));
            return ZPATH_RESULT_ERR;
        }
    } else if (res == ZPN_RESULT_ASYNCHRONOUS) {
        // we need to ensure the caller doesn't have to manage the refcount for us
        // so we will increment our ref_count since the asynch handler will now have a ref
        EXPORTER_DEBUG_REQUEST_POLICY_STATE("%s: wait async for assertion validation - async_count: (%d->%d)",
                                                request->name, request->async_count, request->async_count + 1);
        __sync_fetch_and_add_4(&(request->async_count), 1);
        return res;
    } else {
        EXPORTER_LOG(AL_NOTICE,
                     "%s: assertion validation failed to attach saml attrs- %s",
                     request->name,
                     zpn_result_string(res));
    }

    if (res == ZPATH_RESULT_NO_ERROR) {
        EXPORTER_DEBUG_REQUEST_POLICY_STATE_DETAIL("%s: SAML Attributes were successfully attached to exporter request",
                                                  request->name);
        state->saml_attr_attach_finished = 1;
    }

    return res;
}

static int
exporter_request_init_scim(struct exporter_request *request) {
    int result;

    struct exporter_request_policy_state *state = request->policy_state;

    char scim_idp_gid_username[ZPN_USERDB_IDP_GID_USERNAME_MAX_LEN];

    result = zpn_userdb_gen_idp_gid_username(state->idp_gid,
                                             request->log.nameid,
                                             scim_idp_gid_username,
                                             sizeof(scim_idp_gid_username));
    if (result) {
        EXPORTER_LOG(AL_WARNING,
                     "%s: could not generate scim user idp_gid_username '%s' - %s",
                     request->name,
                     request->log.nameid ? request->log.nameid : "",
                     zpn_result_string(result));
    }

    struct zpn_scim_user *user;
    size_t user_count = 1;

    result = zpn_scim_user_get_by_idp_gid_username(request->is_csp_enabled ? request->orig_customer_gid : request->conn->exporter_domain->customer_gid,
                                                   scim_idp_gid_username,
                                                   &user,
                                                   &user_count,
                                                   exporter_request_wally_callback,
                                                   request,
                                                   fohh_get_current_thread_id());

    if (result == ZPN_RESULT_ASYNCHRONOUS) {
        stats.request_user_state_collection_async++;
        EXPORTER_DEBUG_REQUEST_POLICY_STATE("%s: wait async for get scim user - async_count: (%d->%d)",
                                                 request->name, request->async_count, request->async_count + 1);
        __sync_fetch_and_add_4(&(request->async_count), 1);
        return result;
    } else if (result) {
        stats.request_user_state_collection_fail_err++;
        EXPORTER_LOG(AL_NOTICE,
                     "%s: scim init failed when looking up user %s - %s",
                     request->name,
                     scim_idp_gid_username,
                     zpn_result_string(result));
        return result;
    }

    state->scim_user_id = user->id;

    struct zpn_scim_attr_header *scim_attr_headers[SCIM_ATTR_HEADER_CUSTOMER_MAX_COUNT];
    size_t scim_attr_headers_count = SCIM_ATTR_HEADER_CUSTOMER_MAX_COUNT;

    result = zpn_scim_attr_header_get_by_idp_gid(state->idp_gid,
                                                 &(scim_attr_headers[0]),
                                                 &scim_attr_headers_count);

    ZPN_LOG(AL_DEBUG,
                "%s: getting active attribute with idp gid- %ld",
                request->name,
                (long)state->idp_gid);
    if (result == ZPN_RESULT_ASYNCHRONOUS) {
        ZPN_LOG(AL_INFO,
                "%s: scim_attr_header by idp returned async, that should not happen - %s",
                request->name,
                zpn_result_string(result));
        return result;
    } else if (result) {
        ZPN_LOG(AL_WARNING,
                "%s: could not get scim attributes to search for Active attribute - %s",
                request->name,
                zpn_result_string(result));
        return result;
    }

    size_t i;
    for (i = 0; i < scim_attr_headers_count; i++) {
        if (strcasecmp(scim_attr_headers[i]->name, "active") == 0) {
            state->active_attr_gid = scim_attr_headers[i]->gid;
            break;
        }
    }

    if (!state->active_attr_gid) {
        ZPN_LOG(AL_WARNING,
                "%s: customer does not have an active scim attribute, cannot apply scim policies...",
                request->name);
        return ZPN_RESULT_ERR;
    }

    return ZPN_RESULT_NO_ERROR;
}

/*
 * Takes a user request and locates that users current scim groups (if any) from the userdbs
 * Attaches those scim groups to the hashed state so that they can later be used in policy evaluation
 *
 */
static int
exporter_request_attach_scim_groups(struct exporter_request *request) {
    assert(request != NULL);
    char        scim_group_string[SCIM_GROUP_MAX_RULE_STRING_SIZE];
    size_t      scim_group_string_len;
    int         result;
    struct      exporter_request_policy_state *state = request->policy_state;

    //scim groups could have changed on the userdb and we have no way
    //of counting on their state between reprocessing asyncs so lets clear any
    //previously calculated state and finalize
    if (state->scim_group_attach_finished) {
        return ZPN_RESULT_NO_ERROR;
    }

    size_t user_groups_count = ZPN_SCIM_USER_GROUP_PER_USER_MAX;
    struct zpn_scim_user_group *user_groups[ZPN_SCIM_USER_GROUP_PER_USER_MAX];
    result = zpn_scim_user_group_get_by_user_gid(request->is_csp_enabled ? request->orig_customer_gid : request->conn->exporter_domain->customer_gid,
                                                 state->scim_user_id,
                                                 user_groups,
                                                 &user_groups_count,
                                                 1,
                                                 exporter_request_wally_callback,
                                                 request,
                                                 fohh_get_current_thread_id());

    if (result == ZPN_RESULT_ASYNCHRONOUS) {
        EXPORTER_DEBUG_REQUEST_POLICY_STATE("%s: wait async for fetching scim user groups - async_count: (%d->%d)",
                                                     request->name, request->async_count, request->async_count + 1);
        __sync_fetch_and_add_4(&(request->async_count), 1);
        return result;
    } else if (result && result != ZPN_RESULT_NOT_FOUND) {
        EXPORTER_LOG(AL_NOTICE,
                     "%s: scim groups failed when looking up user - %s",
                     request->name,
                     zpn_result_string(result));
        return result;
    } else {
        size_t i;
        for (i = 0; i < user_groups_count; i++) {

            scim_group_string_len = zpn_scim_group_to_rule_string_from_group(scim_group_string,
                                                                             sizeof(scim_group_string),
                                                                             user_groups[i]);

            if (zhash_table_store(state->scim_state_hash,
                                scim_group_string,
                                scim_group_string_len,
                                0,
                                state->scim_state_hash)) {
                EXPORTER_LOG(AL_ERROR,
                            "%s: Policy State Authentication: Cannot add scim group %s to request to hash",
                            request->name,
                            scim_group_string);
                return ZPN_RESULT_NO_MEMORY;
            } else {
                EXPORTER_DEBUG_REQUEST_POLICY_STATE("%s: SCIM group added to user state [%s]",
                                                    request->name,
                                                    scim_group_string);
            }
        }
    }
    state->scim_group_attach_finished = 1;
    return ZPN_RESULT_NO_ERROR;
}

/*
 * Takes a user request and locates that users current scim attributes (if any) from the userdbs
 * Attaches those scim attributes to the user's request so that they can later be used in policy evaluation
 */
static int
exporter_request_attach_scim_attributes(struct exporter_request *request) {
    assert(request != NULL);

    int      result;
    size_t      i;

    struct exporter_request_policy_state *state = request->policy_state;

    //scim groups could have changed on the userdb and we have no way
    //of counting on their state between reprocessing asyncs so lets clear any
    //previously calculated state and finalize
    if (state->scim_attr_attach_finished) {
        return ZPN_RESULT_NO_ERROR;
    }

    size_t user_attributes_count = ZPN_SCIM_USER_ATTRIBUTE_PER_USER_MAX;
    struct zpn_scim_user_attribute *user_attributes[ZPN_SCIM_USER_ATTRIBUTE_PER_USER_MAX];
    result = zpn_scim_user_attribute_get_by_user_gid(request->is_csp_enabled ? request->orig_customer_gid : request->conn->exporter_domain->customer_gid,
                                                     state->scim_user_id,
                                                     1,
                                                     user_attributes,
                                                     &user_attributes_count,
                                                     exporter_request_wally_callback,
                                                     request,
                                                     fohh_get_current_thread_id());

    if (result == ZPN_RESULT_ASYNCHRONOUS) {
        EXPORTER_DEBUG_REQUEST_POLICY_STATE("%s: wait async for fetching scim user attributes - async_count: (%d->%d)",
                                                    request->name, request->async_count, request->async_count + 1);
        __sync_fetch_and_add_4(&(request->async_count), 1);
        return result;
    } else if (result && result != ZPN_RESULT_NOT_FOUND) {
        EXPORTER_LOG(AL_NOTICE,
                     "%s: scim attributes failed when looking up user - %s",
                     request->name,
                     zpn_result_string(result));
        return result;
    } else if (result == ZPN_RESULT_NOT_FOUND) {
        EXPORTER_LOG(AL_DEBUG,
                     "%s: scim user had no attributes so cannot be active we will mark that and handle it later",
                     request->name);
        request->policy_state->active = 0;
    } else {
        //Each row can have values nested so aggregate the number of values internally
        for (i = 0; i < user_attributes_count; i++) {
            if (user_attributes[i]) {
                // Scan the current attributes for active
                if (user_attributes[i]->attribute_gid == request->policy_state->active_attr_gid) {
                    request->policy_state->active = !user_attributes[i]->deleted &&
                                    user_attributes[i]->attribute_values_count &&
                                    strcasecmp(user_attributes[i]->attribute_values[0], "true") == 0;
                }
            }
        }
        /* Attach scim attributes to scim hash */
        result = zpn_scim_user_attribute_to_hash(&(user_attributes[0]),
                                                 user_attributes_count,
                                                 state->scim_state_hash);

        if (result) {
            EXPORTER_LOG(AL_ERROR,
                            "%s: Policy State Authentication: Cannot add scim attributes to request to hash: %s",
                            request->name, zpn_result_string(result));
            return result;
        }
    }
    EXPORTER_DEBUG_REQUEST_POLICY_STATE("%s: SCIM attributes added to user state", request->name);
    state->scim_attr_attach_finished = 1;
    return ZPN_RESULT_NO_ERROR;
}

/*
 * Takes a user request and attaches the Browser profile evaluations to the  request so that they can later be used in policy evaluation
 */
static int
exporter_request_attach_browser_profiles(struct exporter_request *request)
{
    char tmp_buf[1024];
    uint32_t is_mgd_chrome = 0;
    int res = ZPATH_RESULT_NO_ERROR;

    sxprintf(tmp_buf, tmp_buf + sizeof(tmp_buf), "CHROME_ENTERPRISE|%s|%s", "managed", request->gposture->success);
    if (zhash_table_store(request->policy_state->general_state_hash,
                           tmp_buf,
                           strlen(tmp_buf),
                           0,
                           request->policy_state->general_state_hash)) {
        EXPORTER_LOG(AL_ERROR,
                     "%s: Policy State Authentication: Cannot add Managed Chrome Postures %s to request to hash: failing auth",
                     request->name,
                     tmp_buf);
        return ZPN_RESULT_NO_MEMORY;
    } else {
       EXPORTER_DEBUG_REQUEST_POLICY_STATE("%s: Managed Browser Posture added to user state [%s]",
                                           request->name,
                                           tmp_buf);
    }

    res = is_exporter_managed_chrome_2_enabled(request, &is_mgd_chrome);
    if (res != ZPATH_RESULT_NO_ERROR) {
        if (res == ZPATH_RESULT_ASYNCHRONOUS) {
            return res;
        } else if (res != ZPATH_RESULT_NOT_FOUND) {
            EXPORTER_LOG(AL_ERROR, "%s: Checking managed chrome failed %s", request->name, zpath_result_string(res));
            return res;
        }
    } else if (is_mgd_chrome && request->gprofiles) {
        for (int i = 0; i < request->gprofiles->gprofile_gid_count; i++) {
            char tmp_buf[1024];
            sxprintf(tmp_buf, tmp_buf + sizeof(tmp_buf), "CHROME_POSTURE_PROFILE|%"PRId64"", request->gprofiles->gprofile_gids[i]);
            if (zhash_table_store(request->policy_state->general_state_hash,
                                  tmp_buf,
                                  strlen(tmp_buf),
                                  0,
                                  request->policy_state->general_state_hash)) {
                EXPORTER_LOG(AL_ERROR,
                             "%s: Policy State Authentication: Cannot add Managed browser posture profile %s to request to hash: failing auth",
                             request->name,
                             tmp_buf);
                return ZPN_RESULT_NO_MEMORY;
            } else {
                EXPORTER_DEBUG_REQUEST_POLICY_STATE("%s: Browser posture profile added to user state [%s]",
                                                   request->name,
                                                   tmp_buf);
            }
        }
    }
    return ZPN_RESULT_NO_ERROR;
}

/*
 * Call into saml library to do assertion validation.
 * remember to increase async_count whenever wally callback returns async.
 */
static int
exporter_request_auth_assertion_validate(struct exporter_request* request)
{
    struct zmicro_heap      local_heap;
    int                     res;
    char                    buf[ZSAML_MAX_ASSERTION_XML_LEN];
    char*                   nameid = NULL;
    char*                   attribute_names[ZSAML_MAX_NUM_OF_ATTRIBUTES];
    char*                   attribute_values[ZSAML_MAX_NUM_OF_ATTRIBUTES];
    size_t                  attribute_count = ZSAML_MAX_NUM_OF_ATTRIBUTES;
    char*                   entity_id;
    int                     is_oneidentity_auth = 0;

    __sync_add_and_fetch_8(&stats.request_auth_assertion_validation, 1);

    char *alt_authsp_domain = NULL;
    res = zpn_customer_config_get_alt_authsp_domain(request->is_csp_enabled ? request->orig_customer_gid : request->conn->exporter_domain->customer_gid,
                                            &alt_authsp_domain,
                                            request->name,
                                            exporter_request_wally_callback,
                                            request,
                                            fohh_get_current_thread_id());

    if (res == ZPN_RESULT_ASYNCHRONOUS) {
       EXPORTER_DEBUG_REQUEST_POLICY_STATE("%s: wait async for alternate authsp domain - async_count: (%d->%d)",
                                           request->name, request->async_count, request->async_count + 1);
        __sync_fetch_and_add_4(&(request->async_count), 1);
       return res;
    }

    zmicro_heap_init(&local_heap, buf, sizeof(buf));
    /* CSP_INTEGRATION */
    struct validate_saml_resp args;
    args.epoch_not_before = &(request->policy_state->saml_not_before);
    args.epoch_not_on_or_after = &(request->policy_state->saml_not_on_or_after);
    args.nameid = &nameid;
    args.attribute_names = &(attribute_names[0]);
    args.attribute_values = &(attribute_values[0]);
    args.attribute_count = &attribute_count;
    args.entity_id = &entity_id;
    args.is_oneidentity_auth = &is_oneidentity_auth;
    args.is_offline_idp =  NULL;

    res = zpn_idp_cert_validate_saml(request->is_csp_enabled ? request->orig_customer_gid : request->conn->exporter_domain->customer_gid,
                                     (const char *) exporter_request_policy_state_get_assertion_xml(request->policy_state),
                                     (size_t) exporter_request_policy_state_get_assertion_xml_len(request->policy_state),
                                     &local_heap,
                                     exporter_request_policy_state_get_assertion_expire_interval(),
                                     exporter_request_wally_callback,
                                     request,
                                     fohh_get_current_thread_id(),
                                     request->name,
                                     alt_authsp_domain,
                                     &args);
    /* free alt_authsp_domain after use */
    if (alt_authsp_domain) {
        ZPN_FREE(alt_authsp_domain);
    }

    EXPORTER_DEBUG_REQUEST_POLICY_STATE("%s: received (%d)", request->name, res);
    EXPORTER_DEBUG_REQUEST_POLICY_STATE("%s: customer_gid: %"PRId64" 1ID_auth: %d",
                request->name,
                request->is_csp_enabled ? request->orig_customer_gid : request->conn->exporter_domain->customer_gid,
                is_oneidentity_auth);
    switch (res) {
        case ZPN_RESULT_NO_ERROR:
            __sync_add_and_fetch_8(&stats.request_auth_assertion_validation_success, 1);
            zpath_downcase(nameid);
            if (!request->nameid) {
                request->nameid = EXPORTER_PORTAL_SAFE_STRDUP(nameid);
                EXPORTER_DEBUG_REQUEST_POLICY_STATE("%s: SAML validated nameid: [%s]", request->name, request->nameid);
            }
            EXPORTER_DEBUG_REQUEST_POLICY_STATE_DETAIL("%s: assertion validation is successful. user(%s) not before(%"PRId64") "
                                                                                                                           "not after (%"PRId64")",
                                                      request->name,
                                                      nameid,
                                                      request->policy_state->saml_not_before,
                                                      request->policy_state->saml_not_on_or_after);
            res = exporter_request_policy_state_attach_saml_attrs(request,
                                                                  entity_id,
                                                                  attribute_names,
                                                                  attribute_values,
                                                                  attribute_count,
                                                                  is_oneidentity_auth);
            return res;
        case ZPN_RESULT_ASYNCHRONOUS:
            __sync_add_and_fetch_8(&stats.request_auth_assertion_validation_async, 1);
            EXPORTER_DEBUG_REQUEST_POLICY_STATE("%s: wait async for assertion validation - async_count: (%d->%d)",
                                                       request->name, request->async_count, request->async_count + 1);

            __sync_fetch_and_add_4(&(request->async_count), 1);
            return res;
        case ZPN_RESULT_EXPIRED:
            __sync_add_and_fetch_8(&stats.request_auth_assertion_validation_fail_expired, 1);
            goto fail;
        case ZPN_RESULT_NO_MEMORY:
            __sync_add_and_fetch_8(&stats.request_auth_assertion_validation_fail_memory, 1);
            goto fail;
        default:
            __sync_add_and_fetch_8(&stats.request_auth_assertion_validation_fail_err, 1);
            goto fail;
    }
fail:
    EXPORTER_LOG(AL_NOTICE, "%s: assertion validation failed - %s", request->name, zpn_result_string(res));
    return res;
}

/*
 * 1. Reset the existing user state
 * 2. Create the new policy state
 * 3. Attach assertion xml
 */
void
exporter_request_policy_state_create(struct exporter_request*   request,
                                        u_int8_t*               assertion_xml,
                                        int                     assertion_xml_len)
{
    __sync_add_and_fetch_8(&stats.create, 1);

    exporter_request_policy_state_free_and_reset(request);

    if (NULL == request->policy_state) {
        request->policy_state = EXPORTER_CALLOC(sizeof(struct exporter_request_policy_state));
    }

    if (assertion_xml) {
        request->policy_state->assertion_xml = EXPORTER_STRDUP((char *)assertion_xml, assertion_xml_len);
        request->policy_state->assertion_xml_len = assertion_xml_len;
    }

}

/*
 * This function is used to check if a user is a Third-Party login to a scope.
 * A user is a Third-Party login to an MT, if the authdomain of the user does
 * not match with the authdomain of the MT. No user is a Third-Party login to
 * default scope.
 */
int
zpn_is_pra_third_party_login(struct exporter_request* request, int64_t scope_gid, unsigned int *is_ot_third_party_login)
{
    int64_t customer_gid = request->is_csp_enabled ? request->orig_customer_gid : request->conn->exporter_domain->customer_gid;

    if (scope_gid == customer_gid) {
        /* User is accessing default scope portal, set Third-Party login to no. */
        *is_ot_third_party_login = 0;
        return ZPATH_RESULT_NO_ERROR;
    }

    int64_t auth_scope_gid = zpn_get_scope_by_domain(customer_gid,
                                                     "AuthDomain",
                                                     request->nameid,
                                                     request->attr,
                                                     sizeof(request->attr));
    if (!auth_scope_gid) {
        EXPORTER_LOG(AL_ERROR, "%s: Customer %"PRId64" failed to fetch requested scope for auth domain : %s",
                                request->name, customer_gid, request->nameid);
        return ZPATH_RESULT_ERR;
    }

    if (scope_gid != auth_scope_gid) {
        *is_ot_third_party_login = 1;
    }
    return ZPATH_RESULT_NO_ERROR;
}

static int
zpn_get_scope_by_sra_portal(struct exporter_request* request, int64_t *scope_gid_ptr)
{
    struct zpn_sra_portal *sra_portal = NULL;
    int64_t customer_gid = request->is_csp_enabled ? request->orig_customer_gid : request->conn->exporter_domain->customer_gid;
    int ret = 0;

    ret = zpn_sra_portal_get_portal_by_domain_immediate(customer_gid, request->conn->exporter_domain->domain, &sra_portal);
    if (ret != ZPATH_RESULT_NO_ERROR || !sra_portal || (sra_portal && !sra_portal->enabled)) {
        EXPORTER_LOG(AL_ERROR, "%s: Customer %"PRId64" failed to fetch requested domain : %s res : %s", request->name, customer_gid, request->conn->exporter_domain->domain, zpath_result_string(ret));
        /* Requested portal is not valid, return error */
        return ZPATH_RESULT_ERR;
    }

    int64_t scope_gid = sra_portal->scope_gid;

    /* Check if this a global portal and /zscope is requested */
    if (exporter_get_user_portal_zscope_api_type(request) == ZPA_SRA_PORTAL_API_TYPE_ZSCOPE) {
        if (sra_portal->scope_gid != sra_portal->customer_gid) {
            EXPORTER_LOG(AL_ERROR, "%s: /zscope URI can be requested only on default scope domain : %s customer : %"PRId64,
                    request->name, request->conn->exporter_domain->domain, customer_gid);
            /* Requested portal is not valid, return error */
            return ZPATH_RESULT_ERR;
        }

        int64_t zscope_gid = get_scope_id_from_zscope_request(request);
        if (!zscope_gid) {
            EXPORTER_LOG(AL_ERROR, "%s: Customer %"PRId64" has requested /zscope without ID", request->name, customer_gid);
            return ZPATH_RESULT_ERR;
        }

        if (!is_valid_scope(zscope_gid)) {
            EXPORTER_LOG(AL_ERROR, "%s: Customer %"PRId64" has requested invalid zscope %"PRId64"", request->name, customer_gid, zscope_gid);
            return ZPATH_RESULT_ERR;
        }

        /* Validate if the scope GID belongs to this customer */
        if (customer_gid != ZPATH_GID_GET_CUSTOMER_GID(zscope_gid)) {
            EXPORTER_LOG(AL_ERROR, "%s: Customer %"PRId64" has requested zscope %"PRId64" that he does not have access to",
                                    request->name, customer_gid, zscope_gid);
            return ZPATH_RESULT_ERR;
        }
        scope_gid = zscope_gid;
    }

    EXPORTER_DEBUG_REQUEST_POLICY_STATE("%s: Customer %"PRId64" requested portal %s belonging to scope %"PRId64,
            request->name, customer_gid, request->conn->exporter_domain->domain, scope_gid);
    *scope_gid_ptr = scope_gid;
    return ZPATH_RESULT_NO_ERROR;
}

static int
zpn_get_scope_for_pra_request(struct exporter_request* request)
{
    int64_t customer_gid = request->is_csp_enabled ? request->orig_customer_gid : request->conn->exporter_domain->customer_gid;
    int64_t scope_gid = 0;
    int enabled = 0;
    int ret = 0;

    if (exporter_is_pra_shared_session_request(request)) {
        ret = exporter_get_scope_from_shared_session(request, &scope_gid);
        if (ret == ZPATH_RESULT_ASYNCHRONOUS) {
            EXPORTER_DEBUG_REQUEST_POLICY_STATE("%s: Fetching PRA session data async for customer gid = %"PRId64"",
                    request->name, customer_gid);
            return ret;
        } else if (ret != ZPATH_RESULT_NO_ERROR) {
            EXPORTER_LOG(AL_ERROR,"%s: Failed to eval scope for domain %s customer gid = %"PRId64", error : %s",
                    request->name, request->conn->exporter_domain->domain, customer_gid, zpath_result_string(ret));
            return ret;
        }
    } else {
        ret = zpn_get_scope_by_sra_portal(request, &scope_gid);
        if (ret == ZPATH_RESULT_ASYNCHRONOUS) {
            EXPORTER_DEBUG_REQUEST_POLICY_STATE("%s: Async PRA portal %s scope eval for for customer gid = %"PRId64"",
                    request->name, request->conn->exporter_domain->domain, customer_gid);
            return ret;
        } else if (ret != ZPATH_RESULT_NO_ERROR) {
            EXPORTER_LOG(AL_ERROR,"%s: Failed to eval scope for domain %s for customer gid = %"PRId64", error : %s",
                    request->name, request->conn->exporter_domain->domain, customer_gid, zpath_result_string(ret));
            return ret;
        }

        /*
         * Check if the user is a Third-Party login to a scope being accessed.
         */
        ret = zpn_is_pra_third_party_login(request, scope_gid, &(request->is_ot_third_party_login));
        if (ret != ZPATH_RESULT_NO_ERROR) {
            EXPORTER_LOG(AL_ERROR,"%s: Failed to eval if the user %s is a Third-Party login to"
                                  " scope gid =  %"PRId64", customer gid =  %"PRId64"",
                                   request->name, request->nameid, scope_gid, customer_gid);
            return ret;
        }
    }

    if (scope_gid != customer_gid) {
        if (!zpn_get_delegated_admin_status(request->conn->exporter_domain->customer_gid)) {
            EXPORTER_LOG(AL_ERROR,"%s: Customer %"PRId64" requested portal %s or session belonging to scope %"PRId64", "
                    "but DTA FF is not enabled for customer.", request->name, customer_gid, request->conn->exporter_domain->domain, scope_gid);
            return ZPN_RESULT_ACCESS_DENIED;
        }

        /*
         * Check if scope and privileged approval is enabled only for
         * non-default scopes. Default scope is enabled by default,
         * and Privileged Approval is also enabled by default.
         */
        ret = zpn_is_scope_enabled(scope_gid, &enabled);
        if (ret != ZPN_RESULT_NO_ERROR) {
            EXPORTER_LOG(AL_ERROR, "%s: zpn get scope %"PRId64" enabled status failed %s", request->name, scope_gid, zpath_result_string(ret));
            return ret;
        }
        if (!enabled) {
            EXPORTER_LOG(AL_ERROR,"%s: Customer %"PRId64" requested portal %s or session belonging to scope %"PRId64", "
                    "scope is not enabled.", request->name, customer_gid, request->conn->exporter_domain->domain, scope_gid);
            return ZPN_RESULT_ACCESS_DENIED;
        }

        if (request->is_ot_third_party_login) {
            ret = zpn_scope_get_privilged_approvals_enabled_status(scope_gid, &enabled);
            if (ret != ZPN_RESULT_NO_ERROR) {
                EXPORTER_LOG(AL_ERROR, "zpn get scope %"PRId64" approval status failed %s", scope_gid, zpath_result_string(ret));
                return ZPATH_RESULT_ERR;
            }
            if (!enabled) {
                EXPORTER_LOG(AL_ERROR,"%s: Customer %"PRId64" requested portal %s belonging to scope %"PRId64", "
                        "scope does not support Third-Party login.", request->name, customer_gid, request->conn->exporter_domain->domain, scope_gid);
                return ZPN_RESULT_ACCESS_DENIED;
            }
        }
    }
    request->scope_gid = scope_gid;
    return ZPATH_RESULT_NO_ERROR;
}

/*
 * This routine is used to check if the portal requested belongs to default scope
 */
int
zpn_is_pra_default_scope_access(struct exporter_request* request)
{
    struct zpn_sra_portal *sra_portal = NULL;
    int64_t customer_gid = request->is_csp_enabled ? request->orig_customer_gid : request->conn->exporter_domain->customer_gid;
    int64_t scope_gid = 0;
    int ret = 0;

    /* This is a shared session request */
    if (exporter_is_pra_shared_session_request(request)) {
        ret = exporter_get_scope_from_shared_session(request, &scope_gid);
        if (ret == ZPATH_RESULT_ASYNCHRONOUS) {
            EXPORTER_DEBUG_REQUEST_POLICY_STATE("%s: Fetching PRA session data async for customer gid = %"PRId64"",
                    request->name, customer_gid);
            return ret;
        } else if (ret != ZPATH_RESULT_NO_ERROR) {
            EXPORTER_LOG(AL_ERROR,"%s: Fetching PRA session data async for customer gid = %"PRId64", error : %s",
                    request->name, customer_gid, zpath_result_string(ret));
            return ret;
        }
        /* Shared session belongs to non-default scope, do not serve this request */
        if (scope_gid != customer_gid) {
            EXPORTER_LOG(AL_ERROR,"%s: DTA-PRA FF is disabled for customer %"PRId64", cannot access shared session belonging to scope %"PRId64,
                    request->name, customer_gid, scope_gid);
            return ZPATH_RESULT_ERR;
        }
    } else {
        ret = zpn_sra_portal_get_portal_by_domain_immediate(customer_gid, request->conn->exporter_domain->domain, &sra_portal);
        if (ret != ZPATH_RESULT_NO_ERROR || !sra_portal) {
            EXPORTER_LOG(AL_ERROR, "%s: Customer %"PRId64" failed to fetch requested domain : %s res : %s",
                    request->name, customer_gid, request->conn->exporter_domain->domain, zpath_result_string(ret));
            /* Requested portal is not valid, return error */
            return ZPATH_RESULT_ERR;
        }

        /* Portal belongs to non-default scope, do not serve this request */
        if (sra_portal->scope_gid != customer_gid) {
            EXPORTER_LOG(AL_ERROR,"%s: DTA-PRA FF is disabled for customer %"PRId64", cannot access domain %s belonging to scope %"PRId64,
                    request->name, customer_gid, request->conn->exporter_domain->domain, sra_portal->scope_gid);
            return ZPATH_RESULT_ERR;
        }

        /* User is requesting /zscope on default scope portal, do not serve this request */
        if (exporter_get_user_portal_zscope_api_type(request) == ZPA_SRA_PORTAL_API_TYPE_ZSCOPE) {
            EXPORTER_LOG(AL_ERROR,"%s: DTA-PRA FF is disabled for customer %"PRId64", cannot request /zscope URI in domain %s, Portal URI path : %s",
                    request->name, customer_gid, request->conn->exporter_domain->domain, &request->url[request->url_parser.field_data[UF_PATH].off]);
            return ZPATH_RESULT_ERR;
        }
    }
    return ZPATH_RESULT_NO_ERROR;
}

static int exporter_load_browser_profile(struct exporter_request* request)
{
    int res = ZPATH_RESULT_NO_ERROR;
    struct zpn_managed_browser_profile *profiles[1000];
    size_t profile_count = sizeof(profiles) / sizeof(profiles[0]);

    /* Retrieve profiles based on customer GID to fetch all the rows*/
    res = zpn_managed_browser_profile_get_by_customer_gid(
            request->orig_customer_gid,
            &(profiles[0]),
            &profile_count,
            exporter_request_wally_callback,
            request, 0);
    if (res == ZPN_RESULT_ASYNCHRONOUS) {
        EXPORTER_LOG(AL_DEBUG, "%s: Asynchronous lookup of table zpn_managed_browser_profile for row fixup, async_count: (%d->%d)",
                     request->name, request->async_count, request->async_count + 1);
        return res;
    } else if (res != ZPATH_RESULT_NO_ERROR) {
        EXPORTER_LOG(AL_ERROR, "%s: Fetching browser profile for row fixup results in res: %d\n", request->name, res);
        return res;
    }

    return res;
}

static int
exporter_request_policy_state_internal(struct exporter_request* request)
{
    int res;
    uint32_t is_mgd_chrome = 0;
    res = exporter_request_auth_assertion_validate(request);

    if (ZPATH_RESULT_ASYNCHRONOUS == res) {
        __sync_add_and_fetch_8(&stats.request_internal_auth_validation_async, 1);
        return res;
    }
    if (ZPATH_RESULT_NO_ERROR != res) {
        __sync_add_and_fetch_8(&stats.request_internal_auth_validation_err, 1);
        EXPORTER_DEBUG_REQUEST_POLICY_STATE("%s: assertion validation failed(%s), send HTTP 401", request->name, zpn_result_string(res));
        exporter_request_respond(request, HTTP_STATUS_UNAUTHORIZED, EXPORTER_ERROR_CODE_ASSERTION_VALIDATION_FAILED);
        return res;
    }
    __sync_add_and_fetch_8(&stats.request_internal_auth_validation_success, 1);

    //SCOPE_POLICY
    res = zse_load_scope(request->conn->exporter_domain->customer_gid,
                         exporter_request_wally_callback,
                         request,
                         fohh_get_current_thread_id());
    if (res == ZPATH_RESULT_ASYNCHRONOUS) {
        EXPORTER_DEBUG_REQUEST_POLICY_STATE("%s: zpn scope load async, prefetching data... async_count: (%d->%d)",
                                            request->name, request->async_count, request->async_count + 1);
        __sync_fetch_and_add_4(&(request->async_count), 1);
        return res;
    } else if (res != ZPATH_RESULT_NO_ERROR) {
        EXPORTER_LOG(AL_ERROR, "%s: zpn scope load failed %s", request->name, zpath_result_string(res));
        return res;
    }
    if (request->conn->exporter_domain->is_ot &&
        !is_pra_delegated_admin_disabled(request->is_csp_enabled ? request->orig_customer_gid : request->conn->exporter_domain->customer_gid)) {
        if (request->nameid && !request->scope_gid) {
            res = zpn_get_scope_for_pra_request(request);
            if (res == ZPATH_RESULT_ASYNCHRONOUS) {
                EXPORTER_DEBUG_REQUEST_POLICY_STATE(
                        "%s: zpn scope evaluation async for domain = %s, prefetching data... async_count: (%d->%d)", request->name,
                        request->conn->exporter_domain->domain, request->async_count, request->async_count + 1);
                __sync_fetch_and_add_4(&(request->async_count), 1);
                return res;
            } else if (res == ZPN_RESULT_ACCESS_DENIED) {
                EXPORTER_LOG(AL_ERROR, "%s: Access denied for domain = %s, error %s", request->name, request->conn->exporter_domain->domain,
                                        zpath_result_string(res));
                exporter_request_respond(request, HTTP_STATUS_UNAUTHORIZED, EXPORTER_ERROR_CODE_SRA_PORTAL_UNAUTHORIZED_USER);
                return res;
            } else if (res !=  ZPATH_RESULT_NO_ERROR) {
                EXPORTER_LOG(AL_ERROR, "%s: zpn scope evaluation failed for domain = %s, error %s", request->name, request->conn->exporter_domain->domain,
                                        zpath_result_string(res));
                exporter_request_respond(request, HTTP_STATUS_UNAUTHORIZED, EXPORTER_ERROR_CODE_SRA_PORTAL_SCOPE_INIT_FAILED);
                return res;
            }
            EXPORTER_DEBUG_REQUEST_POLICY_STATE("%s: For customer_gid = %"PRId64" , portal domain = %s, client domain = %s, "
                                                "scope_gid = %"PRId64" PRA Third-Party Login = %u",
                                                request->name,
                                                request->is_csp_enabled ? request->orig_customer_gid : request->conn->exporter_domain->customer_gid,
                                                request->conn->exporter_domain->domain,
                                                request->nameid,
                                                request->scope_gid,
                                                request->is_ot_third_party_login);
        }
    } else {
        if (request->nameid && !request->scope_gid) {
            /* As pert of DTA M1 from the nameid extract domain and from that extraced domain get scope id */
            request->scope_gid = zpn_get_scope_by_domain(
                    request->is_csp_enabled ? request->orig_customer_gid : request->conn->exporter_domain->customer_gid,
                    "AuthDomain",
                    request->nameid,
                    request->attr,
                    sizeof(request->attr));
            EXPORTER_DEBUG_REQUEST_POLICY_STATE("%s: For customer_gid = %ld, client domain = %s, scope_gid = %ld",
                    request->name,
                    request->is_csp_enabled ? (long) request->orig_customer_gid : (long)request->conn->exporter_domain->customer_gid,
                    request->nameid,
                    (long)request->scope_gid);
        }
    }

    //SCOPE_POLICY
    if (request->conn->exporter_domain->is_ot &&
        (is_pra_delegated_admin_disabled(request->is_csp_enabled ? request->orig_customer_gid : request->conn->exporter_domain->customer_gid) ||
        (!zpn_get_delegated_admin_status(request->conn->exporter_domain->customer_gid)))) {
        res = zpn_is_pra_default_scope_access(request);
        if (res) {
            EXPORTER_LOG(AL_ERROR, "%s: DTA-PRA FF is disabled, domain = %s, error %s", request->name, request->conn->exporter_domain->domain,
                                    zpath_result_string(res));
            exporter_request_respond(request, HTTP_STATUS_SERVICE_UNAVAILABLE, EXPORTER_ERROR_CODE_PRA_DTA_DISABLED);
            return res;
        }
    }

    // Update SRA application host FQDN after scope evaluation - ET-64368
    if (request->conn->exporter_domain->is_ot) {
        res = sra_update_application_domain(request);
        if (res == ZPN_RESULT_ASYNCHRONOUS) {
            EXPORTER_DEBUG_REQUEST_POLICY_STATE("%s: sra_update_application_domain is async", request->name);
            __sync_fetch_and_add_4(&(request->async_count), 1);
            return res;
        } else if (ZPATH_RESULT_NO_ERROR != res) {
             EXPORTER_LOG(AL_ERROR, "%s: Failed to update sra app domain (%s)", request->name, zpath_result_string(res));
             return res;
        }
    }


    // Attach SCIM before reauth ET-53016
    if (request->policy_state->scim_policy_enabled) {
        res = exporter_request_init_scim(request);
        if (ZPATH_RESULT_ASYNCHRONOUS == res) {
            EXPORTER_DEBUG_REQUEST_POLICY_STATE("%s: initing scim async", request->name);
            return res;
        } else if (ZPATH_RESULT_NO_ERROR != res) {
            EXPORTER_LOG(AL_ERROR, "%s: initing scim failed(%s), send HTTP 401",
                                               request->name,
                                               zpath_result_string(res));
            res = exporter_request_respond_with_text(request, HTTP_STATUS_UNAUTHORIZED, EXPORTER_ERROR_CODE_INIT_SCIM_FAILED, "Request could not be authenticated");
            return res;
        }

        if (request->policy_state->scim_state_hash) {
            zhash_table_free(request->policy_state->scim_state_hash);
        }
        request->policy_state->scim_state_hash = zhash_table_alloc(&exporter_allocator);
        if (request->policy_state->scim_state_hash == NULL) {
            EXPORTER_LOG(AL_ERROR, "%s: could not allocate scim_state_hash table", request->name);
            return ZPATH_RESULT_NO_MEMORY;
        }

        res = exporter_request_attach_scim_groups(request);
        if (ZPATH_RESULT_ASYNCHRONOUS == res) {
            EXPORTER_DEBUG_REQUEST_POLICY_STATE("%s: fetching scim groups async", request->name);
            return res;
        } else if (ZPATH_RESULT_NO_ERROR != res) {
            EXPORTER_LOG(AL_ERROR, "%s: attaching user scim groups failed(%s), send HTTP 401",
                                               request->name,
                                               zpath_result_string(res));
            exporter_request_respond(request, HTTP_STATUS_UNAUTHORIZED, EXPORTER_ERROR_CODE_ATTACHING_SCIM_GROUP_FAILED);
            return res;
        }

        res = exporter_request_attach_scim_attributes(request);
        if (ZPATH_RESULT_ASYNCHRONOUS == res) {
            EXPORTER_DEBUG_REQUEST_POLICY_STATE("%s: fetching scim attributes async", request->name);
            return res;
        } else if (ZPATH_RESULT_NO_ERROR != res) {
            EXPORTER_LOG(AL_ERROR, "%s: attaching user scim attributes failed(%s), send HTTP 401",
                                               request->name,
                                               zpath_result_string(res));
            exporter_request_respond(request, HTTP_STATUS_UNAUTHORIZED, EXPORTER_ERROR_CODE_ATTACHING_SCIM_ATTRIBUTES_FAILED);
            return res;
        }
    }

    res = exporter_request_policy_state_check_reauth(request);
    if (ZPATH_RESULT_ASYNCHRONOUS == res) {
        __sync_add_and_fetch_8(&stats.request_auth_reauth_check_async, 1);
        return res;
    } else if (ZPN_RESULT_EXPIRED == res) {
        char orig_url[EXPORTER_URL_MAX_ENCODE_SIZE];
        __sync_add_and_fetch_8(&stats.request_auth_reauth_check_fail_expired, 1);
        if (request->sra_zconsole_url) {
            snprintf(orig_url, sizeof(orig_url), "https://%s:%d%s",
                    request->conn->sni, request->conn->local_port_he, request->sra_zconsole_url);
        } else if (request->is_csp_enabled && request->orig_url[0] != 0) {
            /* If we are coming from auth domain this will be already there in origurl query param */
            snprintf(orig_url, sizeof(orig_url), "%s", request->orig_url);
        } else {
            snprintf(orig_url, sizeof(orig_url), "https://%s:%d%s", request->conn->sni,
                    request->conn->local_port_he, &(request->url[request->url_parser.field_data[UF_PATH].off]));
        }
        EXPORTER_DEBUG_REQUEST_POLICY_STATE("%s: assertion is expired... redirecting to idp origurl: [%s]", request->name, orig_url);

        res = exporter_request_redirect_encode(request,
                                                NULL,
                                                NULL,
                                                NULL,
                                                NULL,
                                                EXPORTER_DOMAIN_AUTH,
                                                443,
                                                EXPORTER_PATH_AUTH_DOMAIN_FROM_CUSTOMER_DOMAIN,
                                                EXPORTER_QUERY_NAME_ORIGINAL_URL, orig_url,
                                                EXPORTER_QUERY_NAME_REQUIRE_REAUTH, "true",
                                                NULL);
        if (res) {
             EXPORTER_LOG(AL_ERROR, "%s: redirect_encode failed: %s", request->name, zpath_result_string(res));
             return res;
        } else {
            /* we need to return ZPN_RESULT_EXPIRED to force the exporter request to terminate,
             * then we can do redirect to idp for re-auth */
            return ZPN_RESULT_EXPIRED;
        }
    }
    __sync_add_and_fetch_8(&stats.request_auth_reauth_check_success, 1);

    uint64_t customer_gid = request->is_csp_enabled ? request->orig_customer_gid : request->conn->exporter_domain->customer_gid;
    res = zpn_customer_ready_short(customer_gid,
                                   exporter_request_wally_callback,
                                   request,
                                   fohh_get_current_thread_id());

    if (res == ZPATH_RESULT_ASYNCHRONOUS) {
        EXPORTER_DEBUG_REQUEST_POLICY_STATE("%s: zpn customer ready async, prefetching data... async_count: (%d->%d)",
                                            request->name, request->async_count, request->async_count + 1);
        __sync_fetch_and_add_4(&(request->async_count), 1);
        return res;
    } else if (res != ZPATH_RESULT_NO_ERROR) {
        EXPORTER_LOG(AL_ERROR, "%s: zpn customer ready failed %s", request->name, zpath_result_string(res));
        return res;
    }

    res = zpn_scope_ready(request->scope_gid,
                          exporter_request_wally_callback,
                          request,
                          fohh_get_current_thread_id());
    if (res == ZPATH_RESULT_ASYNCHRONOUS) {
        EXPORTER_DEBUG_REQUEST_POLICY_STATE("%s: zpn scope ready async, prefetching data... async_count: (%d->%d)",
                                            request->name, request->async_count, request->async_count + 1);
        __sync_fetch_and_add_4(&(request->async_count), 1);
        return res;
    } else if (res != ZPATH_RESULT_NO_ERROR) {
        EXPORTER_LOG(AL_ERROR, "%s: zpn scope ready failed %s", request->name, zpath_result_string(res));
        return res;
    }

    res = is_exporter_managed_chrome_2_enabled(request, &is_mgd_chrome);
    if (res != ZPATH_RESULT_NO_ERROR) {
        if (res == ZPATH_RESULT_ASYNCHRONOUS) {
            return res;
        } else if (res != ZPATH_RESULT_NOT_FOUND) {
            EXPORTER_LOG(AL_ERROR, "%s: Checking managed chrome failed %s", request->name, zpath_result_string(res));
            return res;
        }
    } else if (is_mgd_chrome && request->scope_gid && request->gposture && request->gposture->success &&
        (strcasecmp(request->gposture->success, "true") == 0)) {
        /* Even if we don't find the profile, we should not drop the connection, infact we won't send the
         * gid to the broker, so, policy won't be applied and hence user would be blocked.
         */
        res = exporter_load_browser_profile(request);
        if (res == ZPATH_RESULT_ASYNCHRONOUS) {
            __sync_fetch_and_add_4(&(request->async_count), 1);
            return res;
        } else if (res == ZPATH_RESULT_NO_ERROR) {
            res = exporter_request_fetch_browser_profile(request);
            if (res == ZPATH_RESULT_ASYNCHRONOUS) {
                __sync_fetch_and_add_4(&(request->async_count), 1);
                return res;
            } else if (res == ZPATH_RESULT_NO_MEMORY) {
                EXPORTER_LOG(AL_ERROR, "%s: Fetching browser profile failed %s", request->name, zpath_result_string(res));
                return res;
            }
        }
    }

    /* Attach Managed Browser Profiles */
    res = is_exporter_managed_chrome_enabled(request, &is_mgd_chrome);
    if (res != ZPATH_RESULT_NO_ERROR) {
        if (res == ZPATH_RESULT_ASYNCHRONOUS) {
            return res;
        } else if (res != ZPATH_RESULT_NOT_FOUND) {
            EXPORTER_LOG(AL_ERROR, "%s: Checking managed chrome failed %s", request->name, zpath_result_string(res));
            return res;
        }
    } else if (is_mgd_chrome && request->gposture && request->gposture->success) {
        res = exporter_request_attach_browser_profiles(request);
        if (res) {
            EXPORTER_LOG(AL_ERROR, "%s: attaching user managed browser profiles failed(%s), send HTTP 401",
                         request->name, zpath_result_string(res));
            exporter_request_respond(request, HTTP_STATUS_UNAUTHORIZED, EXPORTER_ERROR_CODE_BROWSER_PROFILE_ATTACH_FAILED);
            return res;
        }
    }

    /* Register our customer so we can use this data for handling various request */
    EXPORTER_DEBUG_REQUEST_POLICY_STATE_DETAIL("%ld: Registering for applications", (long) customer_gid);
    res = zpn_application_customer_register_with_app_match(customer_gid, exporter_request_wally_callback, request, 0, 0);
    if (ZPATH_RESULT_ASYNCHRONOUS == res) {
        EXPORTER_DEBUG_REQUEST_POLICY_STATE("%s: Registering for applications asynchronously - async_count: (%d->%d)",
                                                    request->name, request->async_count, request->async_count + 1);
        __sync_fetch_and_add_4(&(request->async_count), 1);
        return res;
    }
    EXPORTER_DEBUG_REQUEST_POLICY_STATE_DETAIL("%ld: Registered for applications", (long) customer_gid);

    EXPORTER_DEBUG_REQUEST_POLICY_STATE_DETAIL("%ld: Registering for clientless applications", (long) customer_gid);
    res = zpn_client_less_customer_register(customer_gid, exporter_request_wally_callback, request, 0);
    if (ZPATH_RESULT_ASYNCHRONOUS == res) {
        EXPORTER_DEBUG_REQUEST_POLICY_STATE("%s: Registering for clientless applications asynchronously - async_count: (%d->%d)",
                                                   request->name, request->async_count, request->async_count + 1);
        __sync_fetch_and_add_4(&(request->async_count), 1);
        return res;
    }
    EXPORTER_DEBUG_REQUEST_POLICY_STATE_DETAIL("%ld: Registered for clientless applications", (long) customer_gid);

    return res;
}

/*
 * 1. Create policy state for saving user policy state info
 *    - this will clean up existing user policy state and attach assertion
 * 2. Do assertion validation - this can be async
 * 3. Register customers (clientless) applications - async
 */

/* CSP_INTEGRATION - Call this first */
int
exporter_request_policy_state(struct exporter_request* request, u_int8_t* assertion_xml, int assertion_xml_len)
{
    __sync_add_and_fetch_8(&stats.request, 1);

    if (0 == exporter_request_policy_state_ready) {
        /* exporter_request_policy_state_init hasn't finished yet */
        __sync_add_and_fetch_8(&stats.request_failed_init_not_done, 1);
        return ZPATH_RESULT_BAD_STATE;
    }

    exporter_request_policy_state_create(request, assertion_xml, assertion_xml_len);
    request->policy_state->user_email = request->log.nameid;
    return exporter_request_policy_state_internal(request);
}

int exporter_request_policy_state_stats_dump(struct zpath_debug_state*   request_state,
                                             const char**                query_values,
                                             int                         query_value_count,
                                             void*                       cookie)
{
    char        jsonout[10000];

    if (ARGO_RESULT_NO_ERROR ==
            argo_structure_dump(exporter_request_policy_state_stats_description, &stats, jsonout, sizeof(jsonout),
                                NULL, 1)){
        ZDP("%s\n", jsonout);
    }

    if (g_exporter_ot_mode) {
        exporter_privileged_policy_stats_dump(request_state, query_values, query_value_count, cookie);
    }

    return ZPATH_RESULT_NO_ERROR;
}

/*
 * Perform policy evaluation for privileged capabilities such as file transfer, scans
 * 1. Find a matching rule of type 'zpe_policy_type_priv_capabilities'  based on criteria and request's hash tables
 *    (int_hash, general_state_hash, saml, scim)
 * 2. Use the above matched rule ID to perform lookup into the zpn_privileged_capabilities table
 * Return the capabilities bitmap for matched row
 * Support async handling of policy evaluation and table lookups
 * Set 'async_state_reprocess_priv_capability' for request in async scenarios
 */
int  exporter_privileged_capabilities_get(struct exporter_request *request,
                                          int64_t *capabilities_policy_id,
                                          uint64_t *capabilities_policy_bitmap)
{
    int res = ZPATH_RESULT_NO_ERROR;
    int64_t matched_rule = 0; // Is 0 a valid rule id?
    enum zpe_access_action matched_action = zpe_access_action_none;

    if (!request->sra_host_fqdn) {
        EXPORTER_DEBUG_FILE_TRANSFER("%s: Domain name missing for this console. Cannot evaluate capabilities policies.", request->name);
        return ZPATH_RESULT_ERR;
    }

    res = zpn_broker_policy_locked_link_policy_pra_check(request->scope_gid,
                                                     request->attr,
                                                     request->conn->exporter_domain->is_ot,
                                                     request->policy_state->idp_gid,
                                                     zpe_policy_type_priv_capabilities,
                                                     request->sra_host_fqdn,
                                                     request->policy_state->general_state_hash,
                                                     request->policy_state->saml_policy_enabled ? request->policy_state->saml_state_hash : NULL,
                                                     request->policy_state->scim_policy_enabled ? request->policy_state->scim_state_hash : NULL,
                                                     NULL, NULL,
                                                     NULL,
                                                     request->sra_host_port,
                                                     &matched_action,
                                                     NULL,
                                                     NULL,
                                                     &matched_rule, /* zpn_rule table rule gid */
                                                     exporter_request_wally_callback,
                                                     request,
                                                     NULL,
                                                     0);

    if (res == ZPN_RESULT_ASYNCHRONOUS) {
        EXPORTER_DEBUG_FILE_TRANSFER("Asynchronous evaluation of policy for priv capabilities");
        return res;
    } else if (res != ZPATH_RESULT_NO_ERROR) {
        EXPORTER_DEBUG_FILE_TRANSFER("Get policy without rebuild returned %s", zpn_result_string(res));
        return res;
    }

    if ((matched_action == zpe_access_action_check_priv_capabilities) && matched_rule) {
        // now get the priv capabailities bitmap from wally
        struct zpn_privileged_capabilities *mapping = NULL;
        size_t mapping_count = 1;
        res = zpn_privileged_capabilities_get_by_zpn_rule_gid(matched_rule, &mapping, &mapping_count, exporter_request_wally_callback, request, 0);

        if ((res == ZPATH_RESULT_NO_ERROR) && mapping &&
            (!mapping->deleted) &&
            (mapping->customer_gid == (request->is_csp_enabled ? request->orig_customer_gid : request->conn->exporter_domain->customer_gid)) &&
            (mapping->scope_gid && (mapping->scope_gid == request->scope_gid || mapping->scope_gid == mapping->customer_gid))) {
            *capabilities_policy_id = matched_rule;
            *capabilities_policy_bitmap = mapping->capabilities_mask;
            return res;
        } else if (res == ZPN_RESULT_ASYNCHRONOUS) {
            EXPORTER_DEBUG_FILE_TRANSFER("Asynchronous lookup of table zpn_privileged_capabilities");
            return res;
        }
    }

    EXPORTER_LOG(AL_ERROR, "%s, Policy evaluation returned matched_action = %s,  matched_rule = %"PRId64", result = %s",
                 request->name, zpe_access_action_string(matched_action), matched_rule, zpath_result_string(res));
    return ZPATH_RESULT_ERR;
}

#ifndef UNIT_TEST
int exporter_privileged_portal_policy_evaluate(struct exporter_request *request,
                                          int64_t *portal_policy_id,
                                          uint64_t *portal_policy_capabilities_bitmap)
{
    struct zpn_privileged_portal_rule *mapping = NULL;
    struct zpn_sra_portal *sra_portal = NULL;
    char *domain = EXPORTER_GET_REQUEST_DOMAIN(request);
    enum zpe_access_action matched_action = zpe_access_action_none;
    int64_t matched_rule_id = 0;
    int res = ZPATH_RESULT_NO_ERROR;

    /* Fetch zpn_sra_portal */
    res = zpn_sra_portal_get_portal_by_domain_immediate(request->conn->exporter_domain->customer_gid, domain, &sra_portal);
    if ((res != ZPATH_RESULT_NO_ERROR) || (!sra_portal)) {
        EXPORTER_LOG(AL_ERROR, "%s: Error %s retrieving portal for domain %s", request->name, zpath_result_string(res), domain);
        return res;
    }

    res = zpn_broker_privileged_portal_policy_evaluate(sra_portal->scope_gid,
                                                       sra_portal->gid,
                                                       zpe_policy_type_priv_portal_policy,
                                                       request->policy_state->general_state_hash,
                                                       request->policy_state->saml_policy_enabled ? request->policy_state->saml_state_hash : NULL,
                                                       request->policy_state->scim_policy_enabled ? request->policy_state->scim_state_hash : NULL,
                                                       &matched_action,
                                                       &matched_rule_id);
    if (res == ZPN_RESULT_ASYNCHRONOUS) {
        ZPN_DEBUG_PRIV_CAPABILITIES("%s: Asynchronous evaluation of policy for privileged portal", request->name);
        return res;
    } else if (res != ZPATH_RESULT_NO_ERROR) {
        EXPORTER_LOG(AL_ERROR,"%s: Get policy without rebuild returned %s", request->name, zpn_result_string(res));
        return res;
    }

    if ((matched_action != zpe_access_action_check_priv_portal_capabilities) || !matched_rule_id) {
        EXPORTER_LOG(AL_ERROR, "%s: Policy evaluation returned matched_action = %s,  matched_rule = %"PRId64", result = %s",
                request->name, zpe_access_action_string(matched_action), matched_rule_id, zpath_result_string(res));
        return ZPN_RESULT_BAD_DATA;
    }

    ZPN_DEBUG_PRIV_CAPABILITIES("%s: Policy eval returned matched_action = %s,  matched_rule = %"PRId64"",
                                request->name, zpe_access_action_string(matched_action), matched_rule_id);

    size_t mapping_count = 1;

    // now get the priv portal capability from wally
    res = zpn_privileged_portal_rule_get_by_zpn_rule_gid(matched_rule_id, &mapping, &mapping_count, exporter_request_wally_callback, request, 0);
    if (res == ZPN_RESULT_ASYNCHRONOUS) {
        ZPN_DEBUG_PRIV_CAPABILITIES("%s: Asynchronous lookup of table zpn_privileged_portal_rule", request->name);
        return res;
    } else if (res != ZPATH_RESULT_NO_ERROR) {
        EXPORTER_LOG(AL_ERROR,"%s : zpn_privileged_portal_rule lookup returned %s", request->name, zpn_result_string(res));
        return res;
    }

    if ((!mapping) || (mapping->deleted) ||
            (mapping->customer_gid != request->conn->exporter_domain->customer_gid) ||
            (mapping->scope_gid != sra_portal->scope_gid)) {
        EXPORTER_LOG(AL_ERROR, "%s: Lookup of the rule did not match (customer gid, scope gid) or is deleted", request->name);
        return ZPATH_RESULT_ERR;
    }

    *portal_policy_id = matched_rule_id;
    *portal_policy_capabilities_bitmap = mapping->capabilities_mask;
    ZPN_DEBUG_PRIV_CAPABILITIES("%s: Portal policy bitmask %"PRId64"", request->name, mapping->capabilities_mask);

    return ZPATH_RESULT_NO_ERROR;
}
#endif


int exporter_request_policy_state_check_access_for_djb(struct exporter_request *request)
{
    int res = ZPATH_RESULT_NO_ERROR;
    const char *domain = request->sra_host_fqdn;
    enum zpe_access_action matched_action = zpe_access_action_deny;

    if (!domain) {
      EXPORTER_LOG(AL_ERROR, "%s: Empty sra_host_fqdn for DJB", request->name);
      return ZPATH_RESULT_ERR;
    }

    res = zpn_broker_policy_locked_link_policy_pra_check(request->scope_gid,
                                                     request->attr,
                                                     request->conn->exporter_domain->is_ot,
                                                     request->policy_state->idp_gid,
                                                     zpe_policy_type_access,
                                                     domain,
                                                     request->policy_state->general_state_hash,
                                                     request->policy_state->saml_policy_enabled ? request->policy_state->saml_state_hash : NULL,
                                                     request->policy_state->scim_policy_enabled ? request->policy_state->scim_state_hash : NULL,
                                                     NULL,
                                                     NULL,
                                                     NULL,
                                                     request->sra_host_port,
                                                     &matched_action,
                                                     NULL,
                                                     NULL,
                                                     NULL,
                                                     exporter_request_wally_callback,
                                                     request,
                                                     &request->app_gid,
                                                     0);
    if (res == ZPATH_RESULT_ASYNCHRONOUS) {
        __sync_fetch_and_add_4(&(request->async_count), 1);
        return res;
    }

    if (matched_action == zpe_access_action_deny)
    {
        EXPORTER_LOG(AL_ERROR, "%s: Policy Deny for DJB. Expected an access policy for DJB IPAddr subnet ", request->name);
        return ZPATH_RESULT_ERR;

    }
    return res;
}
