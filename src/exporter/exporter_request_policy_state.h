/*
 * exporter_request_policy_state.h. Copyright (C) 2021 Zscaler Inc. All Rights Reserved
 *
 * Handles saml attribute attach and assertion validation.
 */

#ifndef __EXPORTER_REQUEST_POLICY_STATE_H__
#define __EXPORTER_REQUEST_POLICY_STATE_H__

#include "zpn/zpn_scim_user.h"
#include "zpn/zpn_policy_engine.h"
#include "exporter/exporter_private.h"

/*
 * exporter_request_policy_state
 *
 * Owned by an exporter_request
 *
 * Can only be released when its exporter_request is being released
 *
 * Contains request time information for a user's policy request
 * Add any new exporter request policy state here
 * Built during the handling of api requests contains
 * - SAML attrs
 * - SCIM state
 * - Login IDP GID
 */
struct exporter_request_policy_state {
    /*
     * Attach state information to the request
     * to be used in further processing
     * mostly for policy evaluation
     */
    struct zhash_table *general_state_hash;
    struct zhash_table *saml_state_hash;
    struct zhash_table *scim_state_hash;

    const char *user_email;
    int64_t idp_gid;

    /* assertion info */
    char* assertion_xml;
    int   assertion_xml_len;

    /* SCIM State */
    int64_t scim_user_id;
    int64_t active_attr_gid;
    int64_t active;

    int64_t saml_not_before;
    int64_t saml_not_on_or_after;

    int scim_policy_enabled;
    int saml_policy_enabled;

    int saml_attr_attach_finished;
    int scim_attr_attach_finished;
    int scim_group_attach_finished;
};

int
exporter_request_fetch_csp_policy_action(struct exporter_request *request,
                                         int64_t *matched_action_id,
                                         enum zpe_access_action *matched_action,
                                         int64_t *fetch_app_id);


int
exporter_request_fetch_csp_profile_mask(struct exporter_request *request, int64_t *criteria_flags_mask , int64_t *profile_gid);

int
exporter_request_policy_state(struct exporter_request*      request,
                              u_int8_t*                     assertion_xml,
                              int                           assertion_xml_len);

void
exporter_request_policy_state_create(struct exporter_request*   request,
                                     u_int8_t*                  assertion_xml,
                                     int                        assertion_xml_len);

char* exporter_request_policy_state_get_assertion_xml(struct exporter_request_policy_state* state);
int exporter_request_policy_state_get_assertion_xml_len(struct exporter_request_policy_state* state);

void exporter_request_policy_state_free_and_reset(struct exporter_request *request);

int exporter_request_policy_state_init();

int exporter_request_policy_state_stats_dump(struct zpath_debug_state*   request_state,
                                             const char**                query_values,
                                             int                         query_value_count,
                                             void*                       cookie);
int  exporter_privileged_capabilities_get(struct exporter_request *request,
                                          int64_t *capabilities_policy_id,
                                          uint64_t *capabilities_policy_bitmap);

int zpn_is_pra_third_party_login(struct exporter_request* request,
                                 int64_t scope_gid,
                                 unsigned int *is_ot_third_party_login);

int exporter_privileged_portal_policy_evaluate(struct exporter_request *request,
                                               int64_t *portal_policy_id,
                                               uint64_t *portal_policy_capabilities_bitmap);
int exporter_request_policy_state_check_access_for_djb(struct exporter_request *request);
#endif /* __EXPORTER_USER_PORTAL_REQUEST_H__ */
