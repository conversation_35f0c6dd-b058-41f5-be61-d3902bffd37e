@import "../shared.scss";

.nav-bar-container {
    .wrapper {
        .session-info {
            justify-content: flex-end;

            .duration {
                text-align: center;
                display: flex;
                flex-wrap: nowrap;
                align-items: center;
                .unit {
                    display: flex;
                    flex-direction: column;
                    margin-right: 5px;
                    color: $textColor;
                    span.unit-value {
                        border: 1px solid $border;
                        padding: 2px 4px;
                        width: 26px;
                        border-radius: 4px;
                        display: inline-block;
                    }

                    span.unit-name {
                        font-size: 10px;
                        text-transform: capitalize;
                    }
                }
            }

            button.session-info {
                border: none;
                color: $white;
                border-bottom-right-radius: 8px;
                cursor: pointer;
                padding: 0 20px;
                font-size: 14px;
                height: 100%;
            }

            button.end-session {
                background-color: #ce4036;
            }

            button.join-session {
                background-color: #62ab57;
            }

            button.waiting-session {
                background-color: #2160e1;
            }
        }
    }
}
