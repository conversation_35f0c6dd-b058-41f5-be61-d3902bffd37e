@import "./shared.scss";

.nav-bar-container {
    display: flex;
    flex-direction: column;
    margin: 0 auto;
    position: fixed;
    inset: 0;
    transition: top 0.5s;
    font-size: 14px;
    z-index: 999;
    width: 85%;
    height: 45px;
    max-width: 1200px;

    &.close {
        top: -35px;
        width: 60px;
        transition: top 0.5s, width .8s ease;
    }

    .wrapper {
        display: flex;
        height: 45px;
        width: 100%;
        justify-content: center;
        background-color: $white;
        border-bottom-left-radius: 8px;
        border-bottom-right-radius: 8px;
        border: 1px solid $border;
        border-top: none;

        > div {
            display: flex;
            align-items: stretch;
            flex: 1;

            > div {
                align-content: center;
            }
        }
    }

    .nav-bar-handle {
        color: $white;
        text-align: center;
        background: $handleColor;
        width: 60px;
        display: flex;
        justify-content: center;
        align-self: center;
        padding: 2px 0;
        position: relative;
        font-size: 12px;
        cursor: pointer;
        border-bottom-left-radius: 5px;
        border-bottom-right-radius: 5px;
    }
}
