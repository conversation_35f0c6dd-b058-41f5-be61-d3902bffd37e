import React, { useEffect, useState } from 'react';
import Modal, {ModalProps} from '../../modal/Modal';
import InviteUserModal from './../inviteModal/InviteUserModal';
import { getIdValueFromURL, djbQueryString } from '../../../utils';
import Spinner from '../../spinner/Spinner';
import './ParticipantsListModal.scss';

const ParticipantsListModalContent = (props:any) => {
    const zConsoleId: string = getIdValueFromURL('zconsole');
    const scopeIdUrl: string = getIdValueFromURL('zscope');
    const { scopeId, customerGid, inferKey, sessionId, setHideKeyboardIcons, setNotificationText, setShowNotificationModal, sendEmailNotifcation, websocketTunnel, fileTransferInProgress } = props;
    const [showInvite, setShowInvite] = useState<boolean>(false);
    const [participantsList, setParticipantsList] = useState<[]>([]);
    const [showSpinner, setShowSpinner] = useState<boolean>(false);
    const handleOnInviteClick = () => {
        setShowInvite(true);
    }
    const handleCopyInvitation = () => {
        const invitationString = `You have been invited to join the privileged session.\n\nYou can click on the link:\n${location.origin}/v1/zconsole/$${sessionId} \n\nor you can go to the portal - ${location.origin} and access the session from the shared sessions list \n\nYou will be required to authenticate to join the session.`;
        navigator.clipboard.writeText(invitationString)
        .then(() => {
            setNotificationText(`Copied invitation to the clipboard successfully

            Make sure that the user has been invited before sharing the invitation`);
            setShowNotificationModal(true);
        })
        .catch((err) => {
            setNotificationText("Error copying invitation to the clipboard");
            setShowNotificationModal(true);
        });
    }
    const handleOnInviteClose = () => {
        setShowInvite(false);
    }
    const handleControlTransferClick = (user_email:string) => {
        setShowSpinner(true);
        websocketTunnel.sendMessage("sessionControl", JSON.stringify({ type: "transfer", user_email: user_email}))
    }
    const handleEjectClick = (user_email:string) => {
        setShowSpinner(true);
        websocketTunnel.sendMessage("sessionControl", JSON.stringify({ type: "eject", user_email: user_email}))
    }
    const handleWebsocketResponse= (detail:any) => {
        setShowSpinner(false);
        switch(detail.type) {
            case 'transfer':
                if(detail.status === 'success')
                    setNotificationText(`Successfully sent the Control Transfer request ${detail.user_email}`)
                else if(detail.status === 'error')
                    setNotificationText(`Error sending the Control Transfer request ${detail.user_email} ${detail.reason}`)
                setShowNotificationModal(true);
                break
            case 'eject':
                if(detail.status === 'success') {
                    setNotificationText(`Successfully sent the Eject User request`)
                    const ejectHttpRequest = new XMLHttpRequest();
                    let ejectParticipantUrl;
                    if(scopeIdUrl) {
                        ejectParticipantUrl = `${origin}/v1/zscope/${scopeId}/customers/${customerGid}/prasession/${sessionId}/eject?prefix=${inferKey}&userEmail=${encodeURIComponent(detail.user_email)}&zconsole=${zConsoleId}&scopeId=${scopeId}`;
                    } else {
                        ejectParticipantUrl = `${origin}/v1/customers/${customerGid}/prasession/${sessionId}/eject?prefix=${inferKey}&userEmail=${encodeURIComponent(detail.user_email)}&zconsole=${zConsoleId}&scopeId=${scopeId}`;
                    }
                    if(djbQueryString === '1')
                        ejectParticipantUrl += '&djb=1';
                    ejectHttpRequest.overrideMimeType("application/json");
                    ejectHttpRequest.withCredentials = true;
                    ejectHttpRequest.open('GET', ejectParticipantUrl, true);
                    ejectHttpRequest.onload = function() {
                        if(ejectHttpRequest.status === 200) {
                            console.log("Removed the user from the session successfully");
                        }
                    }
                    ejectHttpRequest.send(null);
                }
                else if(detail.status === 'error')
                    setNotificationText(`Error sending the Eject User request ${detail.reason}`)
                setShowNotificationModal(true);
                break;
            case 'email':
                if(detail.status === 'success')
                    console.log(`Successfully sent email participant request for ${detail.user_email}`);
                else if(detail.status === 'error')
                    console.error(`Error sending email participant request for ${detail.user_email} ${detail.reason}`);
                break
            default:
                console.log("default case")
                break;
        }
    }
    const getParticipantsList = () => {
        setShowSpinner(true);
        const origin = new URL(window.location.href)?.origin;
        let url;
        if(scopeIdUrl) {
            url = `${origin}/v1/zscope/${scopeId}/customers/${customerGid}/prasession/${sessionId}?prefix=${inferKey}&zconsole=${zConsoleId}&scopeId=${scopeId}`;
        } else {
            url = `${origin}/v1/customers/${customerGid}/prasession/${sessionId}?prefix=${inferKey}&zconsole=${zConsoleId}&scopeId=${scopeId}`;
        }
        if(djbQueryString === '1')
            url += '&djb=1';
        const httpRequest = new XMLHttpRequest();
        httpRequest.overrideMimeType("application/json");
        httpRequest.withCredentials = true;
        httpRequest.open('GET', url, true);
        httpRequest.onload  = function() {
            setShowSpinner(false);
            var jsonResponse = JSON.parse(httpRequest.responseText);
            if(httpRequest.status === 200) {
                //Check if host has the control, setHideKeyboardIcons to false
                if(jsonResponse.participants[0].exporter_shared_session_data_participant.control_mode)
                    setHideKeyboardIcons(false);
                else
                    setHideKeyboardIcons(true);
                setParticipantsList(jsonResponse.participants)
            } else {
                let errorResponse;
                try {
                    errorResponse = JSON.parse(httpRequest.responseText).exporterUserPortalApiErrorResponse.reason;
                }
                catch(e) {
                    errorResponse = httpRequest.responseText;
                }
                setNotificationText(`Error in getting the Participants List - ${errorResponse}`);
                setShowNotificationModal(true);
            }
        }
        httpRequest.send(null);
    }

    useEffect(() => {
        getParticipantsList();
        document.addEventListener('keyMouseRefresh',function(e:any) {
            getParticipantsList();
        });
        document.addEventListener('sessionControlResponse',function(e:any) {
            handleWebsocketResponse(JSON.parse(e.detail));
        });
    },[])

    return (
        showSpinner === true ? <Spinner loading={showSpinner}/> :
        <div className='participantsBox'>
            {showInvite ? <InviteUserModal showModal={showInvite} onModalClose={handleOnInviteClose} scopeId={scopeId} customerGid={customerGid} inferKey={inferKey} sessionId={sessionId} setNotificationText={setNotificationText} setShowNotificationModal={setShowNotificationModal} sendEmailNotifcation={sendEmailNotifcation} websocketTunnel={websocketTunnel}/> : null}
            <div>
                <button className='participants-primary-button' onClick={handleOnInviteClick}>Invite</button>
                <button className='participants-primary-button' onClick={handleCopyInvitation}>Copy Invitation</button>
            </div>
            {participantsList.map((ele:any, index) => {
                return (
                    <div className='participantsInnerBox'>
                        {index === 0 ?
                            <div className='participantContainer'>
                                <div> Host : {ele.exporter_shared_session_data_participant.participant_name} </div>
                                <button disabled={ele.exporter_shared_session_data_participant.user_disconnect_time ||  ele.exporter_shared_session_data_participant.control_mode} className='participants-primary-button' onClick={() => handleControlTransferClick(ele.exporter_shared_session_data_participant.participant_name)}> Control </button>
                            </div>
                            :
                            <div className='participantContainer'>
                                <div> Participant Name : {ele.exporter_shared_session_data_participant.participant_name} {ele.exporter_shared_session_data_participant.user_disconnect_time ? '(Inactive)' : '(Active)'}</div>
                                <button disabled={ele.exporter_shared_session_data_participant.user_disconnect_time || ele.exporter_shared_session_data_participant.control_mode || fileTransferInProgress} className='participants-primary-button' onClick={() => handleControlTransferClick(ele.exporter_shared_session_data_participant.participant_name)}> Control </button>
                                <button disabled={ele.exporter_shared_session_data_participant.user_disconnect_time} className='participants-primary-button eject-button' onClick={() => handleEjectClick(ele.exporter_shared_session_data_participant.participant_name)}> Eject </button>
                            </div>
                        }
                    </div>
                )
            })}

        </div>
    )
}

const ParticipantsListModal = (props:any) => {

    const { showModal, onModalClose, scopeId, customerGid, inferKey, sessionId, setHideKeyboardIcons, setNotificationText, setShowNotificationModal, sendEmailNotifcation, websocketTunnel, fileTransferInProgress } = props;

    return (
        <Modal
            showModal={showModal}
            shouldCloseOnBackgroundClick={false}
            title={`Participants`}
            onModalClose={onModalClose}
            hideCloseButton={false}
        >
            <ParticipantsListModalContent scopeId={scopeId} customerGid={customerGid} inferKey={inferKey} sessionId={sessionId} setHideKeyboardIcons={setHideKeyboardIcons} setNotificationText={setNotificationText} setShowNotificationModal={setShowNotificationModal} sendEmailNotifcation={sendEmailNotifcation} websocketTunnel={websocketTunnel} fileTransferInProgress={fileTransferInProgress}/>
        </Modal>

    )
}

export default React.memo(ParticipantsListModal);
