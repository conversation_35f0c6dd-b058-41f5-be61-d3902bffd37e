import React, { useState } from "react";
import Modal, { ModalProps } from "../../modal/Modal";
import { getIdValueFromURL, djbQueryString } from '../../../utils';
import './EmergencyAccess.scss';

interface EmergencyAccesModalProps {
    showModal: boolean;
    userEmail: string;
    onModalClose: () => void;
    customerGid: number;
    inferKey: string;
    scopeId: string;
    sessionId: string;
    zConsoleId: string;
    setNotificationText: (notificationString: string) => void;
    setShowNotificationModal: (showNotificationModal:boolean) => void;
    sendEmailNotifcation: boolean;
    websocketTunnel: any;
}

const EmergencyAccessModal = (props: EmergencyAccesModalProps) => {
  const scopeIdUrl: string = getIdValueFromURL('zscope');
  const { userEmail, showModal, onModalClose, customerGid, inferKey, scopeId, sessionId, zConsoleId, setNotificationText, setShowNotificationModal, sendEmailNotifcation, websocketTunnel} = props;
  const [firstName, setFirstName] = useState<string>("");
  const [lastName, setLastName] = useState<string>("");
  const [emergencyaccessCreateClicked, setEmergencyaccessCreateClicked] = useState<boolean>(false);

  const handleFirstNameChange = (e:any) => {
    setFirstName(e.target.value);
  };

  const handleLastNameChange = (e:any) => {
    setLastName(e.target.value);
  };

  const isValidName = (name:string) => {
    return name.length > 0 && name.length <= 72;
  }

  const handleCreateEmergencyAccessUser = () => {
    setEmergencyaccessCreateClicked(true);
    if(isValidName(firstName) && isValidName(lastName)) {
      const data = {
          firstName: firstName,
          lastName: lastName,
          userEmail: userEmail,
          sessionId: sessionId,
          prefix: inferKey,
      }
      const origin = new URL(window.location.href)?.origin;
      let url;
      if(scopeIdUrl) {
          url = `${origin}/v1/zscope/${scopeId}/customers/${customerGid}/emergencyaccess/create-invite?zconsole=${zConsoleId}&scopeId=${scopeId}`;
      } else {
          url = `${origin}/v1/customers/${customerGid}/emergencyaccess/create-invite?zconsole=${zConsoleId}&scopeId=${scopeId}`;
      }
      if(djbQueryString === '1')
        url += '&djb=1';
      const httpRequest = new XMLHttpRequest();
      httpRequest.overrideMimeType("application/json");
      httpRequest.open("POST", url, true);
      httpRequest.setRequestHeader("Content-Type", "application/json");
      httpRequest.onload = function () {
        if (httpRequest.status === 200) {
          setNotificationText(`Successful created Emergency Access user for  ${userEmail}`);
          if(sendEmailNotifcation) {
            websocketTunnel.sendMessage("sessionControl", JSON.stringify({ type: "email", user_email: userEmail }))
          }
        } else {
          setNotificationText(`Error Creating Emergency Access user for ${userEmail}`);
        }
        setShowNotificationModal(true);
      };
      httpRequest.send(JSON.stringify(data));
      onModalClose();
    }
  };

  return (
    <Modal
      showModal={showModal}
      onModalClose={onModalClose}
      primaryButton={"Create"}
      onPrimaryButtonClick={handleCreateEmergencyAccessUser}
      isPrimaryButtonDisabled={emergencyaccessCreateClicked && !(isValidName(firstName) && isValidName(lastName))}
    >
      <div className="emergencyAccessCreateContainer">
        <div className="p-10">
          <label> User Email </label>
          <label> {userEmail} </label>
        </div>
        <div className="p-10">
          <label> First Name </label>
          <input
            className="input-field"
            type="text"
            name="userEmail"
            id="userEmail"
            onChange={handleFirstNameChange}
          ></input>
          <label className={`${emergencyaccessCreateClicked && !isValidName(firstName) ? '' : 'd-none'} error-label`}> First Name Invalid </label>
        </div>
        <div className="p-10">
          <label> Last Name </label>
          <input
            className="input-field"
            type="text"
            name="userEmail"
            id="userEmail"
            onChange={handleLastNameChange}
          ></input>
          <label className={`${emergencyaccessCreateClicked && !isValidName(lastName) ? '' : 'd-none'} error-label`}> Last Name Invalid </label>
        </div>
      </div>
    </Modal>
  );
};

export default EmergencyAccessModal;
