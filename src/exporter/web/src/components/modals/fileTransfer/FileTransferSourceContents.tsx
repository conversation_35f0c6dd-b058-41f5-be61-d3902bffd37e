import React, { useEffect, useState } from "react";
import {convertBytesToString, DataSizeUnit, convertUnixTimeStampIntoString} from "../../../utils/Util"
import zephyr from "zephyr";
import sha256 from 'crypto-js/sha256';

const FileTransferSourceContents = (props: any) => {
    const {onFileItemSelect, customerGid, userId, fileSelected, fileTransferCapabilites} = props;
    const [sourceFiles, setSourceFiles] = useState<any>([]);

    useEffect(() => {
        // Fetch Console Info
        const origin = new URL(window.location.href)?.origin;
        const userIdhash = sha256(userId).toString();
        let url = `${origin}/v1/my-files/customers/${customerGid}/users/${userIdhash}/files`;
        const httpRequest = new XMLHttpRequest();

        httpRequest.overrideMimeType("application/json");
        httpRequest.open('GET', url, true);

        httpRequest.onload  = function() {
            var jsonResponse = JSON.parse(httpRequest.responseText);
            if(httpRequest.status === 200) {
                const filesList = JSON.parse(JSON.stringify(jsonResponse.list)) || [];
                if(fileTransferCapabilites.inspect_upload === "true") {
                    for (let i = 0; i < filesList.length ; i++ ) {
                        if(filesList[i].status !== "SUCCESS") {
                            filesList[i].disable = true;
                        }
                    };
                } else if(fileTransferCapabilites.upload === "true") {
                    for (let i = 0; i < filesList.length ; i++ ) {
                        if(filesList[i].status !== "SUCCESS" && filesList[i].status !== "UNINSPECTED") {
                            filesList[i].disable = true;
                        }
                    };
                }
                for (let i = 0; i < filesList.length ; i++ ) {
                    filesList[i].id = `${filesList[i].id}`;
                    filesList[i].name = filesList[i].fileName;
                    filesList[i].title = filesList[i].fileName;
                    if(fileSelected && filesList[i].name === fileSelected.name)
                        filesList[i].disable = true;
                }
                setSourceFiles(filesList);
            } else {
                console.error('MY FILES API ERROR!');
            }
        };
        httpRequest.send(null);
    }, []);

    const listItemRenderStructure = (item: any, checkboxJSX: any) => {
        return (
            <React.Fragment>
                {checkboxJSX}
                <div className='file-list-item-container'>
                    <div className='file-list-item-inner-container'>
                        <span title={item.name} className="zs-text-truncate file-list-name">{item.name}</span>
                        <p className='file-list-item-desc'>{convertBytesToString(item.fileSize)} | {convertUnixTimeStampIntoString(item.modifiedTime * 1000)}</p>
                    </div>
                </div>
            </React.Fragment>
        );
    }

    return (
        <zephyr.List
            items={sourceFiles}
            itemsColumn={1}
            isMultiSelect={false}
            itemClass="list-item"
            noSelection={false}
            disableField='disable'
            itemsSelected={fileSelected? [fileSelected]: []}
            onItemSelect={(item:any) => {onFileItemSelect(item)}}
            renderItem={listItemRenderStructure}
        />
    );
};

export default React.memo(FileTransferSourceContents);
