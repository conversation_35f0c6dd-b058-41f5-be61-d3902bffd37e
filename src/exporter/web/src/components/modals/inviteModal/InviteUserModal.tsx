import React, { useState } from 'react';
import './InviteUserModal.scss';
import Modal, {ModalProps} from '../../modal/Modal';
import { getIdValueFromURL, djbQueryString } from '../../../utils';
import EmergencyAccessModal from '../emergencyAccessModal/EmergencyAccessModal';
import EmergencyAccessConfirmationModal from '../emergencyAccessModal/EmergencyAccessConfirmationModal';
import Spinner from '../../spinner/Spinner';
import { validateEmail } from '../../../utils/Util';

interface InviteUserModalContentProps {
    onSetUserEmail?: (email:string) => void;
    isEmailValid: boolean;
    setIsEmailValid: (isEmailValid: boolean) => void;
    isInviteClicked: boolean;
}

const InviteUserModalContent = (props: InviteUserModalContentProps) => {

    const { onSetUserEmail, isEmailValid, setIsEmailValid, isInviteClicked } = props;

    const handleUserEmailChange = (event: any) => {
        event.preventDefault();
        const email = event.target.value.trim();
        onSetUserEmail(email);
        setIsEmailValid(validateEmail(email));
    }
    return (
        <div className="p-10 inviteContainer">
            <label> User Email </label>
            <input className="input-field" type="text" name="userEmail" id="userEmail" onChange={handleUserEmailChange}></input>
            <label className={`${ isInviteClicked && !isEmailValid ? '' : 'd-none'} error-label`}> User Email Invalid </label>
        </div>
    )
}

interface InviteUserModalProps extends ModalProps {
    scopeId:string;
    sendEmailNotifcation: boolean;
    setNotificationText: (notificationString: string) => void;
    setShowNotificationModal: (showNotificationModal:boolean) => void;
    showModal: boolean;
    customerGid: number;
    inferKey: string;
    sessionId: string;
    userEmail?: string;
    websocketTunnel: any;
}

const InviteUserModal = (props:InviteUserModalProps) => {
    const zConsoleId: string = getIdValueFromURL('zconsole');
    const scopeIdUrl: string = getIdValueFromURL('zscope');
    const { showModal, onModalClose, customerGid, inferKey, sessionId, scopeId, setNotificationText, setShowNotificationModal, sendEmailNotifcation, websocketTunnel } = props;
    const [userEmail, setUserEmail] = React.useState<string>('');
    const [showEmergencyConfirmationModal, setShowEmergencyConfirmationModal] = useState<boolean>(false);
    const [showEmergencyModal, setShowEmergencyModal] = useState<boolean>(false);
    const [showSpinner, setShowSpinner] = useState<boolean>(false);
    const [isEmailValid, setIsEmailValid] = React.useState(false);
    const [isInviteClicked, setIsInviteClicked] = React.useState(false);

    const onSetUserEmail = (email:string) => {
        setUserEmail(email);
    }

    const hideEmergencyConfirmationModal = () => {
        setShowEmergencyConfirmationModal(false);
    }

    const showEmergencyModalFn = () => {
        setShowEmergencyConfirmationModal(false);
        setShowEmergencyModal(true);
    }

    const hideEmergencyModal = () => {
        setShowEmergencyModal(false);
        onModalClose();
    }

    const onInviteClick = () => {
        setIsInviteClicked(true);
        if(isEmailValid) {
            setIsEmailValid(false);
            setShowSpinner(true);
            const origin = new URL(window.location.href)?.origin;
            let url;
            if(scopeIdUrl) {
                url = `${origin}/v1/zscope/${scopeId}/customers/${customerGid}/prasession/invite?userEmail=${encodeURIComponent(userEmail)}&prefix=${inferKey}&sessionId=${sessionId}&zconsole=${zConsoleId}&scopeId=${scopeId}`;
            } else {
                url = `${origin}/v1/customers/${customerGid}/prasession/invite?userEmail=${encodeURIComponent(userEmail)}&prefix=${inferKey}&sessionId=${sessionId}&zconsole=${zConsoleId}&scopeId=${scopeId}`;
            }
            if(djbQueryString === '1')
                url += '&djb=1';
            const httpRequest = new XMLHttpRequest();
            httpRequest.overrideMimeType("application/json");
            httpRequest.withCredentials = true;
            httpRequest.open('GET', url, true);
            httpRequest.onload  = function() {
                setShowSpinner(false);
                if(httpRequest.status === 200) {
                    setNotificationText(`Successfully invited the user ${userEmail}`);
                    setShowNotificationModal(true);
                    if(sendEmailNotifcation) {
                        websocketTunnel.sendMessage("sessionControl", JSON.stringify({ type: "email", user_email: userEmail }))
                    }
                    onModalClose();
                } else if(httpRequest.status === 400 && JSON.parse(httpRequest.responseText)?.id === "ea.user.not.found") {
                    setShowEmergencyConfirmationModal(true);
                }
                else {
                    let errorResponse;
                    try {
                        errorResponse = JSON.parse(httpRequest.responseText).exporterUserPortalApiErrorResponse.reason;
                    }
                    catch(e) {
                        errorResponse = httpRequest.responseText;
                    }
                    setNotificationText(`Error in inviting the user ${userEmail} - ${errorResponse}`);
                    setShowNotificationModal(true);
                    onModalClose();
                }
            }
            httpRequest.send(null);
        }
    }

    return (
      <Modal
        showModal={showModal}
        shouldCloseOnBackgroundClick={false}
        title={`Invite User`}
        onModalClose={onModalClose}
        primaryButton="Invite"
        onPrimaryButtonClick={onInviteClick}
        isPrimaryButtonDisabled={isInviteClicked && !isEmailValid}
        hideCloseButton={false}
      >
        {showSpinner ? (
          <Spinner loading={showSpinner} />
        ) : (
          <>
            <InviteUserModalContent
              onSetUserEmail={onSetUserEmail}
              setIsEmailValid={setIsEmailValid}
              isEmailValid={isEmailValid}
              isInviteClicked={isInviteClicked}
            />
            <EmergencyAccessConfirmationModal
              showModal={showEmergencyConfirmationModal}
              onModalClose={hideEmergencyConfirmationModal}
              onPrimaryButtonClick={showEmergencyModalFn}
            />
            <EmergencyAccessModal
              showModal={showEmergencyModal}
              onModalClose={hideEmergencyModal}
              userEmail={userEmail}
              customerGid={customerGid}
              inferKey={inferKey}
              scopeId={scopeId}
              sessionId={sessionId}
              zConsoleId={zConsoleId}
              setNotificationText={setNotificationText}
              setShowNotificationModal={setShowNotificationModal}
              sendEmailNotifcation={sendEmailNotifcation}
              websocketTunnel={websocketTunnel}
            />
          </>
        )}
      </Modal>
    );
}

export default InviteUserModal;
