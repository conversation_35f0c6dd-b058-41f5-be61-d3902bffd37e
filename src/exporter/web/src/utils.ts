export const getIdValueFromURL: (id:string) => string = function(id) {
    const pathName: string = new URL(window?.location?.href)?.pathname;
    const values: Array<string> = pathName?.split('/');
    if (values?.length > 0){
        for (let i:number = 0; i < values.length-1; i++) {
            if (values[i].localeCompare(id) === 0) {
                return values[i+1];
            }
        }
    }
    return "";
}

export const djbQueryString = new URLSearchParams(location.search).get('djb');

export const getDisplayHeight: () => number = function() {
    return Math.max((window.innerHeight || document.documentElement.clientHeight || document.body.clientHeight), 480);
}

export const getDisplayWidth: () => number = function() {
    return Math.max((window.innerWidth || document.documentElement.clientWidth || document.body.clientWidth), 640);
}

export const toggleFullscreen = () => {
    const docElmFullScreenFunctions = document.documentElement as HTMLElement & {
        mozRequestFullScreen(): Promise<void>;
        webkitRequestFullscreen(): Promise<void>;
        msRequestFullscreen(): Promise<void>;
    };
    const docExitFullScreenFunctions = document as Document & {
        mozCancelFullScreen(): Promise<void>;
        webkitExitFullscreen(): Promise<void>;
        msExitFullscreen(): Promise<void>;
    };

    if (!document.fullscreenElement) {
        if (document.documentElement.requestFullscreen) {
            document.documentElement.requestFullscreen();
        } else if (docElmFullScreenFunctions.mozRequestFullScreen) { // Firefox
            docElmFullScreenFunctions.mozRequestFullScreen();
        } else if (docElmFullScreenFunctions.webkitRequestFullscreen) { // Chrome, Safari, Opera
            docElmFullScreenFunctions.webkitRequestFullscreen();
        } else if (docElmFullScreenFunctions.msRequestFullscreen) { // IE/Edge
            docElmFullScreenFunctions.msRequestFullscreen();
        }
    } else {
        if (document.exitFullscreen) {
            document.exitFullscreen();
        } else if (docExitFullScreenFunctions.mozCancelFullScreen) { // Firefox
            docExitFullScreenFunctions.mozCancelFullScreen();
        } else if (docExitFullScreenFunctions.webkitExitFullscreen) { // Chrome, Safari, Opera
            docExitFullScreenFunctions.webkitExitFullscreen();
        } else if (docExitFullScreenFunctions.msExitFullscreen) { // IE/Edge
            docExitFullScreenFunctions.msExitFullscreen();
        }
    }
};
