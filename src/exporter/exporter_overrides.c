#include "zpath_lib/zpath_config_override_keys.h"
#include "exporter/exporter.h"
#include "exporter/exporter_overrides.h"
#include "exporter/exporter_guac_sess_sharing.h"

#define EXPORTER_CATEGORY  "Exporter flag"
#define PRA_CATEGORY  "ZPA Privileged Remote Access"
#define PRA_FILE_TRANSFER_CATEGORY "PRA File Transfer"
#define PRA_VNC_CATEGORY "ZPA Privileged Remote Access VNC Access"
#define PRA_JIT_CATEGORY "ZPA Privileged Remote Access Just In Time Privileged Approvals"
#define PRA_GUACD_CATEGORY "ZPA Privileged Remote Access Guacd Service on Exporter"
#define PRA_SESSION_RECORDING_CATEGORY "ZPA Privileged Remote Access Session Recording on Exporter"
#define PRA_CLIPBOARD_CATEGORY "ZPA Privileged Remote Access Clipboard"
#define PRA_CREDENTIAL_MAPPING_CATEGORY "ZPA Privileged Remote Access Credential Mapping"
#define PRA_SESSION_MONITORING_CATEGORY "ZPA Privileged Remote Access Session Monitoring on Exporter"
#define PRA_PRIVILEGED_DESKTOPS_CATEGORY "ZPA Privileged Remote Access Desktops on Exporter"


int exporter_config_override_str_validator(const char *value_str)
{
    int valid = 1;

    if (!value_str) {
        return !valid;
    }

    return valid;
}

// Config Override Descriptions

static struct zpath_config_override_desc exporter_descriptions[] = {
    {
        .key                = EXPORTER_CONFIG_OVERRIDE_IDEL_TIMEOUT_S,
        .desc               = "This is the idle connection time out value for Exporter - Browser connection in seconds",
        .details            = "If the connection between Exporter and Browser stays idle for this much amount of time then the connection is terminated\n"
                              "min: 60 sec\n"
                              "max: 21600 sec\n"  //6 hours
                              "default: 300 sec",
        .val_type           = config_type_int,
        .component_types    = config_component_exporter,
        .target_gid_types   = config_target_gid_type_domain |  config_target_gid_type_cust | config_target_gid_type_inst | config_target_gid_type_inst_grp | config_target_gid_type_global | config_target_gid_type_zsroot,
        .int_range_lo       = EXPORTER_CONFIG_OVERRIDE_IDLE_TIMEOUT_S_MIN,
        .int_range_hi       = EXPORTER_CONFIG_OVERRIDE_IDLE_TIMEOUT_S_MAX,
        .int_default        = EXPORTER_DEFAULT_CONN_TIMEOUT_S,
        .value_traits       = config_value_traits_normal,
        .feature_group      = FEATURE_GROUP_EXPORTER
    },
    {
        .key                = EXPORTER_CONFIG_OVERRIDE_MTUNNEL_REUSE_DISABLED,
        .desc               = "Exporter feature for mtunnel reuse. If this flag is enabled then a new mtunnel will be allocated for every request for a connection and no idle mtunnel will be reused",
        .details            = "0: disable (mtunnles will be reused for a connection)\n"
                              "1: enable (mtunnel will not be resued for a connection and new mtunnels will be allocated)\n"
                              "default: 0 (i.e. mtunnles will be reused for a connection for better optimization)",
        .val_type           = config_type_int,
        .component_types    = config_component_exporter,
        .target_gid_types   = config_target_gid_type_domain |  config_target_gid_type_cust | config_target_gid_type_inst | config_target_gid_type_inst_grp | config_target_gid_type_global | config_target_gid_type_zsroot,
        .int_range_lo       = EXPORTER_CONFIG_OVERRIDE_MTUNNEL_REUSE_DISABLED_MIN,
        .int_range_hi       = EXPORTER_CONFIG_OVERRIDE_MTUNNEL_REUSE_DISABLED_MAX,
        .int_default        = DEFAULT_CONFIG_VAL_EXPORTER_MTUNNEL_REUSE_DISABLED,
        .value_traits       = config_value_traits_feature_enablement,
        .feature_group      = FEATURE_GROUP_MTUNNEL_REUSE
    },
    {
        .key                = EXPORTER_REDIRECT_FEATURE_OVERRIDE,
        .desc               = "Exporter support for broker redirect, ",
        .details            = "0: redirects not supported or is broker redirecting is disabled\n"
                              "1: only respond to forced redirects (e.g. broker shutting down).\n"
                              "2: support initial redirect and any subsequent redirects, regardless of redirect's attribute.\n"
                              "default: 1 (i.e. only respond to forced redirects (e.g. broker shutting down)).",
        .val_type           = config_type_int,
        .component_types    = config_component_exporter,
        .target_gid_types   = config_target_gid_type_inst |  config_target_gid_type_cust | config_target_gid_type_global | config_target_gid_type_zsroot,
        .int_range_lo       = EXPORTER_REDIRECT_FEATURE_OVERRIDE_MIN,
        .int_range_hi       = EXPORTER_REDIRECT_FEATURE_OVERRIDE_MAX,
        .int_default        = EXPORTER_REDIRECT_MODE_DEFAULT,
        .value_traits       = config_value_traits_normal,
        .feature_group      = FEATURE_GROUP_EXPORTER
    },
    {
        .key                = EXPORTER_PEERIP_INCLUDED,
        .desc               = "Exporter feature to include peer ip for CORS token generation. If this feature is enabled then exporter will include the peer ip of the remote Browser in order to create the CORS token",
        .details            = "0: disabled (peer ip is not included in CORS token)\n"
                              "1: enabled (peer ip is included in CORS token).\n"
                              "default: 1 (i.e. peer ip is always included as for CORS token generation)",
        .val_type           = config_type_int,
        .component_types    = config_component_exporter,
        .target_gid_types   = config_target_gid_type_cust |  config_target_gid_type_inst | config_target_gid_type_inst_grp | config_target_gid_type_global |config_target_gid_type_zsroot,
        .int_range_lo       = EXPORTER_PEERIP_INCLUDED_MIN,
        .int_range_hi       = EXPORTER_PEERIP_INCLUDED_MAX,
        .int_default        = DEFAULT_PEERIP_INCLUDED_FOR_CUSTOMER,
        .value_traits       = config_value_traits_feature_enablement,
        .feature_group      = FEATURE_GROUP_EXPORTER_CORS_TOKEN
    },
    {
        .key                = EXPORTER_SAMESITE_NONE,
        .desc               = "Exporter feature to set domain and crypto cookie samesite value to None. If this feature is enabled then same site attribute value for the domain and crypto cookies for the requested domain will be set to None",
        .details            = "0: disabled (samesite attribute of domain and crypto cookie is set to Lax)\n"
                              "1: enabled (samesite attribute of domain and crypto cookie set to None).\n"
                              "default: 0 (i.e. samesite attribute of domain and crypto cookie is set to Lax)",
        .val_type           = config_type_int,
        .component_types    = config_component_exporter,
        .target_gid_types   = config_target_gid_type_cust |  config_target_gid_type_inst | config_target_gid_type_inst_grp | config_target_gid_type_global |config_target_gid_type_zsroot,
        .int_range_lo       = EXPORTER_SAMESITE_NONE_MIN,
        .int_range_hi       = EXPORTER_SAMESITE_NONE_MAX,
        .int_default        = DEFAULT_SAMESITE_NONE_FOR_CUSTOMER,
        .value_traits       = config_value_traits_feature_enablement,
        .feature_group      = FEATURE_GROUP_SAMESITE_COOKIE_ATTRIBUTE
    },
    {
        .key                = EXPORTER_LOGIN_HINT_FEATURE,
        .desc               = "Exporter feature to send login_hint to samlsp. If this feature is enabled then exporter will send username as the value for the login_hint query parameter as a part of redirect to samlsp",
        .details            = "disabled: disabled (login_hint query parameter value will be empty)\n"
                              "enabled: enabled (login_hint query parameter will be the username of the user).\n"
                              "default: disabled (i.e. login_hint query parameter value will be empty)",
        .val_type           = config_type_str,
        .component_types    = config_component_exporter,
        .target_gid_types   = config_target_gid_type_cust |  config_target_gid_type_inst | config_target_gid_type_inst_grp | config_target_gid_type_global |config_target_gid_type_zsroot,
        .str_default        = DEFAULT_EXPORTER_LOGIN_HINT_FEATURE,
        .value_traits       = config_value_traits_feature_enablement,
        .feature_group      = FEATURE_GROUP_EXPORTER,
        ZPATH_CONFIG_OVERRIDE_DESC_INLINE_SET_STR_VALID_CB(exporter_config_override_str_validator)
    },
    {
        .key                = EXPORTER_URL_COOKIE_ENCRYPTION,
        .desc               = "Exporter feature to encrypt the domain cookie in the exporter urls",
        .details            = "0: disabled (domain coookie will not be encrypted)\n"
                              "1: enabled (domain cookie will be encrypted).\n"
                              "default: 1 (i.e. domain cookie will be encrypted)",
        .val_type           = config_type_int,
        .component_types    = config_component_exporter,
        .target_gid_types   = config_target_gid_type_cust |  config_target_gid_type_inst | config_target_gid_type_global |config_target_gid_type_zsroot,
        .int_range_lo       = EXPORTER_URL_COOKIE_ENCRYPTION_MIN,
        .int_range_hi       = EXPORTER_URL_COOKIE_ENCRYPTION_MAX,
        .int_default        = DEFAULT_URL_COOKIE_ENCRYPTION,
        .value_traits       = config_value_traits_feature_enablement,
        .feature_group      = FEATURE_GROUP_URL_COOKIE_ENCRYPTION
    },
    {
        .key                = ZTHREAD_ON_CRASH_DO_EXPORTER_CONFIG_OVERRIDE_KEY,
        .desc               = "Action to do after exporter crashes",
        .details            = "Do not use this config in production\n"
                              "0: do no action.\n"
                              "1. do no core dump\n"
                              "2. close all sockets\n"
                              "default: 2 (i.e close all sockets)",
        .val_type           = config_type_int,
        .component_types    = config_component_exporter,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_zsroot | config_target_gid_type_global,
        .int_range_lo       = ZTHREAD_ON_CRASH_DO_EXPORTER_CONFIG_OVERRIDE_KEY_MIN,
        .int_range_hi       = ZTHREAD_ON_CRASH_DO_EXPORTER_CONFIG_OVERRIDE_KEY_MAX,
        .int_default        = ZTHREAD_ON_CRASH_DO_EXPORTER_CONFIG_OVERRIDE_DEFAULT,
        .value_traits       = config_value_traits_normal,
        .feature_group      = FEATURE_GROUP_EXPORTER
    },
    {
        .key                = PRA_FEATURE_GLOBAL_DISABLE,
        .desc               = "Kill switch for PRA suite of features",
        .details            = "0: PRA is enabled\n"
                              "1: PRA is disabled\n"
                              "Order of check - Public: global\n"
                              "default: 0 - PRA is enabled",
        .val_type           = config_type_int,
        .component_types    = config_component_exporter | config_component_ot_exporter | config_component_ot_broker,
        .target_gid_types   = config_target_gid_type_global,
        .int_range_lo       = 0,
        .int_range_hi       = 1,
        .int_default        = DEFAULT_PRA_FEATURE_GLOBAL_DISABLE,
        .feature_group      = FEATURE_GROUP_PRIVILEGED_REMOTE_ACCESS_ESSENTIALS,
        .value_traits       = config_value_traits_systemwide,
    },
    {
        .key                = PRA_FEATURE,
        .desc               = "PRA feature config disablement per customer",
        .details            = "turn off feature: disabled\n"
                              "turn on feature: enabled\n"
                              "default: disabled",
        .val_type           = config_type_str,
        .component_types    = config_component_exporter | config_component_ot_exporter | config_component_ot_broker,
        .target_gid_types   = config_target_gid_type_cust,
        .str_default        = DEFAULT_PRA_FEATURE,
        .feature_group      = FEATURE_GROUP_PRIVILEGED_REMOTE_ACCESS_ESSENTIALS,
        .value_traits       = config_value_traits_feature_enablement,
        ZPATH_CONFIG_OVERRIDE_DESC_INLINE_SET_STR_VALID_CB(exporter_config_override_str_validator)
    },
    {
        .key                = PRA_VNC_GLOBAL_DISABLE,
        .desc               = "Global config override to disable PRA VNC",
        .details            = "0: PRA VNC is enabled\n"
                              "1: PRA VNC is disabled\n"
                              "Order of check - Public: global\n"
                              "default: 0 - PRA VNC is enabled",
        .val_type           = config_type_int,
        .component_types    = config_component_exporter | config_component_ot_exporter | config_component_ot_broker,
        .target_gid_types   = config_target_gid_type_global,
        .int_range_lo       = 0,
        .int_range_hi       = 1,
        .int_default        = DEFAULT_PRA_VNC_GLOBAL_DISABLE,
        .feature_group      = FEATURE_GROUP_PRIVILEGED_REMOTE_ACCESS_ESSENTIALS,
        .value_traits       = config_value_traits_systemwide,
    },
    {
        .key                = PRA_REALVNC_GLOBAL_DISABLE,
        .desc               = "Global config override to disable PRA REALVNC",
        .details            = "0: PRA REALVNC is enabled\n"
                              "1: PRA REALVNC is disabled\n"
                              "Order of check - Public: global\n"
                              "default: 0 - PRA REALVNC is enabled",
        .val_type           = config_type_int,
        .component_types    = config_component_exporter | config_component_ot_exporter | config_component_ot_broker,
        .target_gid_types   = config_target_gid_type_global,
        .int_range_lo       = 0,
        .int_range_hi       = 1,
        .int_default        = DEFAULT_PRA_REALVNC_GLOBAL_DISABLE,
        .feature_group      = FEATURE_GROUP_PRIVILEGED_REMOTE_ACCESS_ESSENTIALS,
        .value_traits       = config_value_traits_systemwide,
    },

    {
        .key                = FILE_TRANSFER_FEATURE_GLOBAL_DISABLE,
        .desc               = "Global config override to disable PRA File Transfer",
        .details            = "0: PRA File Transfer is enabled\n"
                              "1: PRA File Transfer is disabled\n"
                              "Order of check - Public: global\n"
                              "default: 0 - PRA File Transfer is enabled",
        .val_type           = config_type_int,
        .component_types    = config_component_exporter | config_component_ot_exporter | config_component_ot_broker,
        .target_gid_types   = config_target_gid_type_global,
        .int_range_lo       = 0,
        .int_range_hi       = 1,
        .int_default        = DEFAULT_FILE_TRANSFER_FEATURE_GLOBAL_DISABLE,
        .feature_group      = FEATURE_GROUP_PRIVILEGED_REMOTE_ACCESS_ESSENTIALS,
        .value_traits       = config_value_traits_systemwide,
    },
    {
        .key                = FILE_SCAN_MAX_SIZE,
        .desc               = "PRA File Transfer: config override for max file size to be allowed when ZIA scanning required",
        .details            = "This value is expressed in MB\n"
                              "File Transfer is rejected if the file is larger than 20MB.\n"
                              "Order of check - Public: customer id, global\n"
                              "default: 20 - PRA File Transfer allows file up to 20MB large to be transferred when ZIA scan is required",
        .val_type           = config_type_int,
        .component_types    = config_component_exporter | config_component_ot_exporter,
        .target_gid_types   = config_target_gid_type_cust | config_target_gid_type_global,
        .int_range_lo       = 0,
        .int_range_hi       = MAX_ZIA_SCAN_FILE_SIZE,
        .int_default        = MAX_ZIA_SCAN_FILE_SIZE,
        .feature_group      = FEATURE_GROUP_PRIVILEGED_REMOTE_ACCESS_ESSENTIALS,
        .value_traits       = config_value_traits_normal,
    },
    {
        .key                = FILE_TRANSFER_MAX_SIZE,
        .desc               = "PRA File Transfer: config override for max file size to be allowed when ZIA scanning is not required",
        .details            = "This value is expressed in MB\n"
                              "File Transfer is rejected if the file is larger than this value in MB\n"
                              "Order of check - Public: customer id, global\n"
                              "default: 50 - PRA File Transfer allows file up to 100MB large to be transferred when ZIA scan is not required",
        .val_type           = config_type_int,
        .component_types    = config_component_exporter | config_component_ot_exporter,
        .target_gid_types   = config_target_gid_type_cust | config_target_gid_type_global,
        .int_range_lo       = 0,
        .int_range_hi       = MAX_TRANSFER_FILE_SIZE_RANGE_HI,
        .int_default        = MAX_TRANSFER_FILE_SIZE,
        .feature_group      = FEATURE_GROUP_PRIVILEGED_REMOTE_ACCESS_ESSENTIALS,
        .value_traits       = config_value_traits_normal,
    },
    {
        .key                = FILE_TRANSFER_FILE_SCAN_EXCEPTION_LIST,
        .desc               = "PRA File Transfer: comma separated list of file types that are excluded from ZIA scan",
        .details            = "This exclusion list is only to be used if ZIA Scan cannot process a given file type\n"
                              "and customer explicitly requests for this file type to be allowed to be uploaded\n"
                              "Order of check - Public: customer id, global\n"
                              "default: empty - PRA File Transfer does not exempt any file type from ZIA Scan",
        .val_type           = config_type_str,
        .component_types    = config_component_exporter | config_component_ot_exporter,
        .target_gid_types   = config_target_gid_type_cust | config_target_gid_type_global,
        .str_default        = NULL,
        .feature_group      = FEATURE_GROUP_PRIVILEGED_REMOTE_ACCESS_ESSENTIALS,
        .value_traits       = config_value_traits_normal,
        ZPATH_CONFIG_OVERRIDE_DESC_INLINE_SET_STR_VALID_CB(exporter_config_override_str_validator)
    },
    {
        .key = JIT_FEATURE_GLOBAL_DISABLE,
        .desc = "Global config override to disable PRA JIT Priv Approvals",
        .details =  "0: PRA JIT is enabled\n"
                    "1: PRA JIT is disabled\n"
                    "Order of check - Public: global\n"
                    "default: 0 - PRA JIT is enabled",
        .val_type = config_type_int,
        .component_types = config_component_exporter | config_component_ot_exporter | config_component_ot_broker,
        .target_gid_types = config_target_gid_type_global,
        .int_range_lo = 0,
        .int_range_hi = 1,
        .int_default = DEFAULT_JIT_FEATURE_GLOBAL_DISABLE,
        .feature_group      = FEATURE_GROUP_PRIVILEGED_REMOTE_ACCESS_ESSENTIALS,
        .value_traits       = config_value_traits_systemwide,
    },
    {
        .key = PRA_GUACD_SERVICE_GLOBAL_DISABLE,
        .desc = "Global config override to disable PRA GUACD on Exporter",
        .details =  "0: PRA GUACD service on Exporter is enabled\n"
                    "1: PRA GUACD service on Exporter is disabled\n"
                    "Order of check - Public: global\n"
                    "default: 0 - PRA GUACD service on Exporter is enabled",
        .val_type = config_type_int,
        .component_types = config_component_exporter | config_component_ot_exporter,
        .target_gid_types = config_target_gid_type_global,
        .int_range_lo = 0,
        .int_range_hi = 1,
        .int_default = DEFAULT_PRA_GUACD_SERVICE_GLOBAL_DISABLE,
        .feature_group      = FEATURE_GROUP_GUACD_OPTIONAL,
        .value_traits       = config_value_traits_systemwide,
    },
    {
        .key                = EXPORTER_MGED_BA_FEATURE_GLOBAL_DISABLE,
        .desc               = "ZS managed BA app feature systemwide override",
        .details            = "enable feature : 0\n"
                              "disable feature: 1\n"
                              "default: 0",
        .val_type           = config_type_int,
        .component_types    = config_component_exporter,
        .target_gid_types   = config_target_gid_type_global,
        .int_range_lo       = 0,
        .int_range_hi       = 1,
        .int_default        = EXPORTER_MGED_BA_FEATURE_GLOBAL_DISABLE_DEFAULT,
        .value_traits       = config_value_traits_systemwide,
        .feature_group      = FEATURE_GROUP_EXPORTER
    },
    {
        .key                = EXPORTER_UNIFIED_PORTAL_FEATURE,
        .desc               = "ZS managed unified portal feature override",
        .details            = "enable : 1\n"
                              "disable: 0\n"
                              "default: 0",
        .val_type           = config_type_int,
        .component_types    = config_component_exporter,
        .target_gid_types   = config_target_gid_type_cust,
        .int_range_lo       = EXPORTER_UNIFIED_PORTAL_FEATURE_DISABLE,
        .int_range_hi       = EXPORTER_UNIFIED_PORTAL_FEATURE_ENABLE,
        .int_default        = EXPORTER_UNIFIED_PORTAL_FEATURE_DEFAULT,
        .value_traits       = config_value_traits_feature_enablement,
        .feature_group      = FEATURE_GROUP_PRIVILEGED_REMOTE_ACCESS_ESSENTIALS
    },
    {
        .key                = EXPORTER_MGED_BA_FEATURE,
        .desc               = "ZS managed BA app feature override",
        .details            = "enable : 1\n"
                              "disable: 0\n"
                              "default: 0",
        .val_type           = config_type_int,
        .component_types    = config_component_exporter,
        .target_gid_types   = config_target_gid_type_cust,
        .int_range_lo       = EXPORTER_MGED_BA_FEATURE_DISABLE,
        .int_range_hi       = EXPORTER_MGED_BA_FEATURE_ENABLE,
        .int_default        = EXPORTER_MGED_BA_FEATURE_DEFAULT,
        .value_traits       = config_value_traits_feature_enablement,
        .feature_group      = FEATURE_GROUP_EXPORTER
    },
    /* WAF CONFIGS */
    {
        .key                = EXPORTER_CSP_FEATURE_GLOBAL_DISABLE,
        .desc               = "CSP config to globally disable fingerprinting",
        .details            = "min: 0\n"
                              "max: 1\n"
                              "default: 0",
        .val_type           = config_type_int,
        .component_types    = config_component_exporter,
        .target_gid_types   = config_target_gid_type_global,
        .int_range_lo       = 0,
        .int_range_hi       = 1,
        .int_default        = EXPORTER_CSP_FEATURE_GLOBAL_DISABLE_DEFAULT,
        .value_traits       = config_value_traits_systemwide,
        .feature_group      = FEATURE_GROUP_APP_PROTECTION
    },
    {
        .key                = EXPORTER_CSP_TIMEOUT_FEATURE,
        .desc               = "CSP feature to disable periodic fingerprinting that is set in CSP profile",
        .details            = "min: 0\n"
                              "max: 1\n"
                              "default: 1",
        .val_type           = config_type_int,
        .component_types    = config_component_exporter,
        .target_gid_types   = config_target_gid_type_cust | config_target_gid_type_global,
        .int_range_lo       = 0,
        .int_range_hi       = 1,
        .int_default        = EXPORTER_CSP_TIMEOUT_FEATURE_DEFAULT,
        .value_traits       = config_value_traits_feature_enablement,
        .feature_group      = FEATURE_GROUP_APP_PROTECTION
    },
    {
        .key                = EXPORTER_CSP_FEATURE,
        .desc               = "Enable/disable browser fingerprint feature for BA apps",
        .details            = "turn off feature: disabled\n"
                              "turn on feature: enabled\n"
                              "default: disabled",
        .val_type           = config_type_str,
        .component_types    = config_component_exporter,
        .target_gid_types   = config_target_gid_type_cust | config_target_gid_type_global,
        .str_default        = EXPORTER_CSP_FEATURE_DEFAULT,
        .value_traits       = config_value_traits_feature_enablement,
        .feature_group      = FEATURE_GROUP_APP_PROTECTION,
        ZPATH_CONFIG_OVERRIDE_DESC_INLINE_SET_STR_VALID_CB(exporter_config_override_str_validator)
    },
    {
        .key                = EXPORTER_SSL_ENABLE_PFS_CONFIG,
        .desc               = "To enable PFS for SSL session use this config, by default SSL option SESSION TICKET is enabled and reused",
        .details            = "min: 0\n"
                              "max: 1\n"
                              "default: 0",
        .val_type           = config_type_int,
        .component_types    = config_component_exporter,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_global,
        .int_range_lo       = 0,
        .int_range_hi       = 1,
        .int_default        = EXPORTER_DEFAULT_SSL_ENABLE_PFS_CONFIG,
        .value_traits       = config_value_traits_feature_enablement,
        .feature_group      = FEATURE_GROUP_APP_PROTECTION
    },
    {
        .key                = APP_PROTECTION_CSP_PROFILE,
        .desc               = "Default ZS Recommended Profile for Browser Fingerprint, this should be in sync with its string config",
        .details            = "min: 2 \n"
                              "max: 0x1FFFFFFF, 536,870,911 \n"
                              "default: 0x1F3FFFFF/524,287,999",
        .val_type           = config_type_int,
        .component_types    = config_component_exporter,
        .target_gid_types   = config_target_gid_type_global,
        .int_range_lo       = 2,
        .int_range_hi       = 0x1FFFFFFF,
        .int_default        = APP_PROTECTION_CSP_PROFILE_DEFAULT_BITMASK,
        .value_traits       = config_value_traits_normal,
        .feature_group      = FEATURE_GROUP_APP_PROTECTION
    },
    {
        .key                = CONFIG_FEATURE_CSP_BITMASK,
        .desc               = "CSP config override to define bit mapping of attributes present in browser protection profile",
        .details            = "json validation needed\n",
        .val_type           = config_type_str,
        .component_types    = config_component_exporter,
        .target_gid_types   = config_target_gid_type_global,
        .str_default        = CSP_BITMASK_DEFAULT,
        .value_traits       = config_value_traits_normal,
        .feature_group      = FEATURE_GROUP_APP_PROTECTION,
        ZPATH_CONFIG_OVERRIDE_DESC_INLINE_SET_STR_VALID_CB(exporter_config_override_str_validator)
    },
    {
        .key                = EXPORTER_CSP_FEATURE_JA3,
        .desc               = "CSP feature to record ja3hash for fingerprint",
        .details            = "min: 0\n"
                              "max: 1\n"
                              "default: 1",
        .val_type           = config_type_int,
        .component_types    = config_component_exporter,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_zsroot | config_target_gid_type_global,
        .int_range_lo       = 0,
        .int_range_hi       = 1,
        .int_default        = EXPORTER_CSP_FEATURE_JA3_DEFAULT,
        .value_traits       = config_value_traits_feature_enablement,
        .feature_group      = FEATURE_GROUP_APP_PROTECTION
    },
    {
        .key                = CONFIG_FEATURE_CSP_LOCATION_CHANGE_THRESHOLD,
        .desc               = "CSP feature to record location change for a user in meter",
        .details            = "min: 50, Location change will contribute iff user traveled atleast 50m \n"
                              "max: 20038, Max distance between any 2 points on earth\n"
                              "default: 50",
        .val_type           = config_type_int,
        .component_types    = config_component_exporter,
        .target_gid_types   = config_target_gid_type_cust | config_target_gid_type_inst | config_target_gid_type_global,
        .int_range_lo       = 50,
        .int_range_hi       = 20038,
        .int_default        = LOCATION_CHANGE_THRESHOLD_DEFAULT,
        .value_traits       = config_value_traits_normal,
        .feature_group      = FEATURE_GROUP_APP_PROTECTION
    },
    {
        .key                = CONFIG_FEATURE_CSP_JS_ENCRYPTION_STATE,
        .desc               = "CSP feature to enable encryption for fingerprint being sent",
        .details            = "min: 0\n"
                              "max: 1\n"
                              "default: 1",
        .val_type           = config_type_int,
        .component_types    = config_component_exporter,
        .target_gid_types   = config_target_gid_type_cust | config_target_gid_type_inst | config_target_gid_type_global,
        .int_range_lo       = 0,
        .int_range_hi       = 1,
        .int_default        = DEFAULT_CONFIG_FEATURE_CSP_JS_ENCRYPTION_STATE,
        .value_traits       = config_value_traits_feature_enablement,
        .feature_group      = FEATURE_GROUP_APP_PROTECTION
    },
    {
        .key = PRA_GUACD_SERVICE_ZPA_CPU_LOAD_LIMIT,
        .desc = "Set 1min CPU load average max limit for PRA GUACD on Exporter",
        .details =  "PRA GUACD service on Exporter accepts requests until this limit is reached\n"
                    "Order of check - Public: global\n"
                    "default: 30 - 1min system load avg can be set between 0 - 100",
        .val_type = config_type_int,
        .component_types = config_component_exporter | config_component_ot_exporter,
        .target_gid_types = config_target_gid_type_global,
        .int_range_lo = 0,
        .int_range_hi = 100,
        .int_default = DEFAULT_CPU_LOAD_LIMIT,
        .feature_group      = FEATURE_GROUP_GUACD_OPTIONAL,
        .value_traits       = config_value_traits_normal,
    },
    {
        .key = PRA_GUACD_SERVICE_ZPA_MEM_LOAD_LIMIT,
        .desc = "Set limits for PRA GUACD based on system memory utilization in percentage",
        .details =  "PRA GUACD service on Exporter accepts requests until this memory limit is reached\n"
                    "Order of check - Public: global\n"
                    "default: 85 - PRA GUACD service on Exporter will accept requests until 85% mem util",
        .val_type = config_type_int,
        .component_types = config_component_exporter | config_component_ot_exporter,
        .target_gid_types = config_target_gid_type_global,
        .int_range_lo = 0,
        .int_range_hi = 100,
        .int_default = DEFAULT_MEM_LOAD_LIMIT,
        .feature_group      = FEATURE_GROUP_GUACD_OPTIONAL,
        .value_traits       = config_value_traits_normal,
    },
    {
        .key = PRA_GUACD_SERVICE_ZPA_MAX_SESSIONS_LIMIT,
        .desc = "Set maximum concurrent session count limit for PRA GUACD on Exporter",
        .details =  "-1: Max concurrent session count is calculated based on the CPU cores and Mem on Exporter\n"
                    "0: PRA GUACD service on Exporter will reject all incoming requests \n"
                    "20000: Max 20000 concurrent PRA sessions will be supported on Exporter\n"
                    "Order of check - Public: global\n"
                    "default: -1 Max concurrent session count calculated based on Exporter resources",
        .val_type = config_type_int,
        .component_types = config_component_exporter | config_component_ot_exporter,
        .target_gid_types = config_target_gid_type_global,
        .int_range_lo = DEFAULT_MAX_SESSIONS_LIMIT,
        .int_range_hi = PRA_MAX_SESSIONS_LIMIT_HI,
        .int_default = DEFAULT_MAX_SESSIONS_LIMIT,
        .feature_group      = FEATURE_GROUP_GUACD_OPTIONAL,
        .value_traits       = config_value_traits_normal,
    },
    {
        .key = PRA_HEALTH_CHECK_ENABLE,
        .desc = "Enable PRA health check monitoring for OT Exporters based on PRA session limits and system resource limits",
        .details =  "0: PRA Exporters will have normal responses to aliveness checks\n"
                    "1: PRA Exporters will respond with 503 if PRA limits are reached\n"
                    "Order of check - Public: component id, component grp id, global\n"
                    "default: 0 PRA Exporters will have normal responses to aliveness checks",
        .val_type = config_type_int,
        .component_types = config_component_exporter | config_component_ot_exporter,
        .target_gid_types = config_target_gid_type_inst | config_target_gid_type_inst_grp | config_target_gid_type_global,
        .int_range_lo = DEFAULT_PRA_HEALTH_CHECK_ENABLE,
        .int_range_hi = PRA_HEALTH_CHECK_ENABLE_HI,
        .int_default = DEFAULT_PRA_HEALTH_CHECK_ENABLE,
        .feature_group      = FEATURE_GROUP_GUACD_OPTIONAL,
        .value_traits       = config_value_traits_normal,
    },
    {
        .key = PRA_SESSION_PROCTORING_ZPA_MAX_JOIN_USERS_LIMIT,
        .desc = "Set maximum users join limit for PRA session proctoring on Exporter",
        .details = "1: Only 1 user can join a particular PRA session on Exporter \n"
                    "10: Max 10 users can join a particular PRA session on Exporter\n"
                    "Order of check - Public: customer id, global\n"
                    "default: 10 Max users can join a particular PRA session on Exporter",
        .val_type = config_type_int,
        .component_types = config_component_exporter | config_component_ot_exporter,
        .target_gid_types = config_target_gid_type_cust | config_target_gid_type_global,
        .int_range_lo = PRA_SESSION_PARTICIPANT_JOIN_MIN_COUNT,
        .int_range_hi = PRA_SESSION_PARTICIPANT_JOIN_MAX_COUNT,
        .int_default = DEFAULT_PRA_SESSION_PARTICIPANT_JOIN_COUNT,
        .feature_group      = FEATURE_GROUP_PRIVILEGED_REMOTE_ACCESS_ADVANCED,
        .value_traits       = config_value_traits_normal
    },
    {
        .key                = SESSION_RECORDING_FEATURE_GLOBAL_DISABLE,
        .desc               = "Global config override to disable PRA Session Recording",
        .details            = "0: PRA Session Recording is enabled\n"
            "1: PRA Session Recording is disabled\n"
            "Order of check - Public: global\n"
            "default: 0 - PRA Session Recording is enabled",
        .val_type           = config_type_int,
        .component_types    = config_component_exporter | config_component_ot_exporter,
        .target_gid_types   = config_target_gid_type_global,
        .int_range_lo       = 0,
        .int_range_hi       = 1,
        .int_default        = DEFAULT_SESSION_RECORDING_FEATURE_GLOBAL_DISABLE,
        .feature_group      = FEATURE_GROUP_PRIVILEGED_REMOTE_ACCESS_ADVANCED,
        .value_traits       = config_value_traits_systemwide,
    },
    {
        .key = CLIPBOARD_FEATURE_GLOBAL_DISABLE,
        .desc = "Global config override to disable PRA Clipboard",
        .details =  "0: PRA Clipboard is enabled\n"
                    "1: PRA Clipboard is disabled\n"
                    "Order of check - Public: global\n"
                    "default: 0 - PRA File Transfer is enabled",
        .val_type = config_type_int,
        .component_types = config_component_exporter | config_component_ot_exporter,
        .target_gid_types = config_target_gid_type_global,
        .int_range_lo = 0,
        .int_range_hi = 1,
        .int_default = DEFAULT_CLIPBOARD_FEATURE_GLOBAL_DISABLE,
        .feature_group      = FEATURE_GROUP_PRIVILEGED_REMOTE_ACCESS_ESSENTIALS,
        .value_traits       = config_value_traits_systemwide,
    },
    {
        .key = ENDUSER_APPROVALS_FEATURE_GLOBAL_DISABLE,
        .desc = "Global config override to disable EndUser Approvals",
        .details =  "0: EndUser Approvals  is enabled\n"
                    "1: EndUser Approvals is disabled\n"
                    "Order of check - Public: global\n"
                    "default: 0 - PRA EndUser Approvals is enabled",
        .val_type = config_type_int,
        .component_types = config_component_exporter | config_component_ot_exporter,
        .target_gid_types = config_target_gid_type_global,
        .int_range_lo = 0,
        .int_range_hi = 1,
        .int_default = DEFAULT_ENDUSER_APPROVALS_FEATURE_GLOBAL_DISABLE,
        .feature_group      = FEATURE_GROUP_PRIVILEGED_REMOTE_ACCESS_ESSENTIALS,
        .value_traits       = config_value_traits_systemwide,
    },
    {
        .key = CLIPBOARD_MAX_SIZE,
        .desc = "PRA Clipboard: config override for max clipboard data to be allowed",
        .details =  "This value is expressed in bytes\n"
                    "Clipboard data is rejected if it is larger than the configured value.\n"
                    "Order of check - Public: customer id, global\n"
                    "default: 4096 - PRA Clipboard allows clipboard data upto 4096 to be transferred to/from the remote system",
        .val_type = config_type_int,
        .component_types = config_component_exporter | config_component_ot_exporter,
        .target_gid_types = config_target_gid_type_cust | config_target_gid_type_global,
        .int_range_lo = 1,
        .int_range_hi = CLIPBOARD_SIZE_VALUE,
        .int_default = DEFAULT_CLIPBOARD_SIZE,
        .feature_group      = FEATURE_GROUP_PRIVILEGED_REMOTE_ACCESS_ESSENTIALS,
        .value_traits       = config_value_traits_normal,
    },
    {
      .key = DIAGS_LOG_RPC_GLOBAL_DISABLE,
      .desc = "Global config override to disable sending PRA log data from Exporter to Broker",
      .details =  "0: Sending log data is enabled \n"
                  "1: Sending PRA log data is disabled\n"
                  "Order of check : global\n"
                  "default: 0 - Sending PRA log data is enabled",
      .val_type = config_type_int,
      .component_types = config_component_exporter | config_component_ot_exporter | config_component_ot_broker,
      .target_gid_types = config_target_gid_type_global,
      .int_range_lo = 0,
      .int_range_hi = 1,
      .int_default =  DEFAULT_DIAGS_LOG_RPC_GLOBAL_DISABLE,
      .feature_group      = FEATURE_GROUP_PRIVILEGED_REMOTE_ACCESS_ESSENTIALS,
      .value_traits       = config_value_traits_systemwide,
    },
    {
      .key = DIAGS_LOG_RPC,
      .desc = "Disable sending PRA log data from Exporter to Broker",
      .details =  "1: Sending log data is enabled \n"
                  "0: Sending PRA log data is disabled\n"
                  "Order of check - Public: customer id, global\n"
                  "default: 1 - Sending PRA log data is enabled",
      .val_type = config_type_int,
      .component_types = config_component_exporter | config_component_ot_exporter | config_component_ot_broker,
      .target_gid_types = config_target_gid_type_cust | config_target_gid_type_global,
      .int_range_lo = 0,
      .int_range_hi = 1,
      .int_default = DEFAULT_DIAGS_LOG_RPC,
      .feature_group      = FEATURE_GROUP_PRIVILEGED_REMOTE_ACCESS_ESSENTIALS,
      .value_traits       = config_value_traits_feature_enablement,
    },
    {
      .key = CREDENTIALS_FEATURE_GLOBAL_DISABLE,
      .desc = "Global config override to disable Credential mapping globally",
      .details =  "0: PRA Credential mapping is enabled \n"
                  "1: PRA Credential mapping is disabled\n"
                  "Order of check : global\n"
                  "default: 0 - PRA Credential mapping is enabled",
      .val_type = config_type_int,
      .component_types = config_component_exporter | config_component_ot_exporter,
      .target_gid_types = config_target_gid_type_global,
      .int_range_lo = 0,
      .int_range_hi = 1,
      .int_default = DEFAULT_CREDENTIALS_FEATURE_GLOBAL_DISABLE,
      .feature_group      = FEATURE_GROUP_PRIVILEGED_REMOTE_ACCESS_ADVANCED,
      .value_traits       = config_value_traits_systemwide,
    },
    {
      .key = CREDENTIAL_POOL_FEATURE_GLOBAL_DISABLE,
      .desc = "Global config override to disable Credential pooling globally",
      .details =  "0: PRA Credential pooling is enabled \n"
                  "1: PRA Credential pooling is disabled\n"
                  "Order of check : global\n"
                  "default: 0 - PRA Credential pooling is enabled",
      .val_type = config_type_int,
      .component_types = config_component_exporter | config_component_ot_exporter,
      .target_gid_types = config_target_gid_type_global,
      .int_range_lo = 0,
      .int_range_hi = 1,
      .int_default = DEFAULT_CREDENTIAL_POOL_FEATURE_GLOBAL_DISABLE,
      .feature_group      = FEATURE_GROUP_PRIVILEGED_REMOTE_ACCESS_ADVANCED,
      .value_traits       = config_value_traits_systemwide,
    },
    {
        .key = PRA_CREDENTIAL_POOL_API_RETRY_LIMIT,
        .desc = "Set API max retry limit for PRA credential pool on Exporter",
        .details =  "PRA credential pool thread retry sending API to CPS until this limit is reached\n"
                    "Order of check - Public: global\n"
                    "default: 10 - API retry limit can be set between 0 - 100",
        .val_type = config_type_int,
        .component_types = config_component_exporter | config_component_ot_exporter,
        .target_gid_types = config_target_gid_type_inst | config_target_gid_type_global,
        .int_range_lo = PRA_CREDENTIAL_POOL_API_RETRY_LOW,
        .int_range_hi = PRA_CREDENTIAL_POOL_API_RETRY_HI,
        .int_default = DEFAULT_PRA_CREDENTIAL_POOL_API_RETRY_LIMIT,
        .feature_group      = FEATURE_GROUP_PRIVILEGED_REMOTE_ACCESS_ADVANCED,
        .value_traits       = config_value_traits_normal,
    },
    {
      .key = ARBITRARY_AUTH_DOMAIN_FEATURE_GLOBALLY_SUPPRESSED,
      .desc = "Global config override to disable Arbitrary domains globally",
      .details =  "Disabled Arbitrary domains is enabled \n"
                  "Enabled: Arbitrary domains is disabled\n"
                  "Order of check : global\n"
                  "default: disabled - Arbitrary domains is enabled",
      .val_type = config_type_str,
      .component_types = config_component_exporter | config_component_ot_exporter,
      .target_gid_types = config_target_gid_type_global,
      .str_default = DEFAULT_ARBITRARY_AUTH_DOMAIN_FEATURE_GLOBAL_DISABLE,
      .feature_group      = FEATURE_GROUP_ARBITRARY_DOMAINS,
      .value_traits       = config_value_traits_systemwide,
      ZPATH_CONFIG_OVERRIDE_DESC_INLINE_SET_STR_VALID_CB(exporter_config_override_str_validator)
    },
    {
        .key                = PRIVILEGED_CREDENTIALS_FEATURE,
        .desc               = "Enable/disable Credential mapping",
        .details            = "turn off feature: disabled\n"
                              "turn on feature: enabled\n"
                              "default: disabled",
        .val_type           = config_type_str,
        .component_types    = config_component_exporter | config_component_ot_exporter,
        .target_gid_types   = config_target_gid_type_cust | config_target_gid_type_zsroot,
        .str_default        = DEFAULT_PRIVILEGED_CREDENTIALS_FEATURE,
        .feature_group      = FEATURE_GROUP_PRIVILEGED_REMOTE_ACCESS_ADVANCED,
        .value_traits       = config_value_traits_feature_enablement,
        ZPATH_CONFIG_OVERRIDE_DESC_INLINE_SET_STR_VALID_CB(exporter_config_override_str_validator)
    },
    {
        .key                = PRIVILEGED_CREDENTIAL_POOL_FEATURE,
        .desc               = "Enable/disable Credential pooling",
        .details            = "turn off feature: disabled\n"
                              "turn on feature: enabled\n"
                              "default: disabled",
        .val_type           = config_type_str,
        .component_types    = config_component_exporter | config_component_ot_exporter,
        .target_gid_types   = config_target_gid_type_cust | config_target_gid_type_zsroot,
        .str_default        = DEFAULT_PRIVILEGED_CREDENTIAL_POOL_FEATURE,
        .feature_group      = FEATURE_GROUP_PRIVILEGED_REMOTE_ACCESS_ADVANCED,
        .value_traits       = config_value_traits_feature_enablement,
        ZPATH_CONFIG_OVERRIDE_DESC_INLINE_SET_STR_VALID_CB(exporter_config_override_str_validator)
    },
    {
        .key                = JIT_APPROVAL_BASED_ACCESS,
        .desc               = "Enable/disable Privileged Approvals JIT",
        .details            = "turn off feature: disabled\n"
                              "turn on feature: enabled\n"
                              "default: disabled",
        .val_type           = config_type_str,
        .component_types    = config_component_exporter | config_component_ot_exporter,
        .target_gid_types   = config_target_gid_type_cust | config_target_gid_type_global,
        .str_default        = DEFAULT_JIT_APPROVAL_BASED_ACCESS,
        .feature_group      = FEATURE_GROUP_PRIVILEGED_REMOTE_ACCESS_ESSENTIALS,
        .value_traits       = config_value_traits_feature_enablement,
        ZPATH_CONFIG_OVERRIDE_DESC_INLINE_SET_STR_VALID_CB(exporter_config_override_str_validator)
    },
    {
        .key                = PORTAL_MARKDOWN_CONTENT,
        .desc               = "Enable/disable portal markdown content",
        .details            = "turn off feature: disabled\n"
                              "turn on feature: enabled\n"
                              "default: disabled",
        .val_type           = config_type_str,
        .component_types    = config_component_exporter | config_component_ot_exporter,
        .target_gid_types   = config_target_gid_type_cust | config_target_gid_type_zsroot | config_target_gid_type_inst| config_target_gid_type_global,
        .str_default        = DEFAULT_PORTAL_MARKDOWN_CONTENT,
        .feature_group      = FEATURE_GROUP_PRIVILEGED_REMOTE_ACCESS_ESSENTIALS,
        .value_traits       = config_value_traits_feature_enablement,
        ZPATH_CONFIG_OVERRIDE_DESC_INLINE_SET_STR_VALID_CB(exporter_config_override_str_validator)
    },
    {
        .key                = PRA_VNC_ACCESS,
        .desc               = "Enable/disable PRA VNC",
        .details            = "turn off feature: disabled\n"
                              "turn on feature: enabled\n"
                              "default: disabled",
        .val_type           = config_type_str,
        .component_types    = config_component_exporter | config_component_ot_exporter,
        .target_gid_types   = config_target_gid_type_cust | config_target_gid_type_zsroot | config_target_gid_type_inst| config_target_gid_type_global,
        .str_default        = DEFAULT_PRA_VNC_ACCESS,
        .feature_group      = FEATURE_GROUP_PRIVILEGED_REMOTE_ACCESS_ESSENTIALS,
        .value_traits       = config_value_traits_feature_enablement,
        ZPATH_CONFIG_OVERRIDE_DESC_INLINE_SET_STR_VALID_CB(exporter_config_override_str_validator)
    },
    {
        .key                = PRA_REALVNC_ACCESS,
        .desc               = "Enable/disable PRA REALVNC",
        .details            = "turn off feature: disabled\n"
                              "turn on feature: enabled\n"
                              "default: disabled",
        .val_type           = config_type_str,
        .component_types    = config_component_exporter | config_component_ot_exporter,
        .target_gid_types   = config_target_gid_type_cust | config_target_gid_type_zsroot | config_target_gid_type_inst| config_target_gid_type_global,
        .str_default        = DEFAULT_PRA_REALVNC_ACCESS,
        .feature_group      = FEATURE_GROUP_PRIVILEGED_REMOTE_ACCESS_ESSENTIALS,
        .value_traits       = config_value_traits_feature_enablement,
        ZPATH_CONFIG_OVERRIDE_DESC_INLINE_SET_STR_VALID_CB(exporter_config_override_str_validator)
    },
    {
        .key                = FILE_TRANSFER,
        .desc               = "Enable/disable File Transfer",
        .details            = "turn off feature: disabled\n"
                              "turn on feature: enabled\n"
                              "default: disabled",
        .val_type           = config_type_str,
        .component_types    = config_component_exporter | config_component_ot_exporter,
        .target_gid_types   = config_target_gid_type_cust | config_target_gid_type_zsroot | config_target_gid_type_global,
        .str_default        = DEFAULT_FILE_TRANSFER,
        .feature_group      = FEATURE_GROUP_PRIVILEGED_REMOTE_ACCESS_ESSENTIALS,
        .value_traits       = config_value_traits_feature_enablement,
        ZPATH_CONFIG_OVERRIDE_DESC_INLINE_SET_STR_VALID_CB(exporter_config_override_str_validator)
    },
    {
        .key                = FASTER_FILE_TRANSFER,
        .desc               = "Enable/disable Faster File Transfer",
        .details            = "turn off feature: disabled\n"
                              "turn on feature: enabled\n"
                              "default: disabled",
        .val_type           = config_type_str,
        .component_types    = config_component_exporter | config_component_ot_exporter,
        .target_gid_types   = config_target_gid_type_cust | config_target_gid_type_zsroot | config_target_gid_type_global,
        .str_default        = DEFAULT_FASTER_FILE_TRANSFER,
        .feature_group      = FEATURE_GROUP_PRIVILEGED_REMOTE_ACCESS_ESSENTIALS,
        .value_traits       = config_value_traits_feature_enablement,
        ZPATH_CONFIG_OVERRIDE_DESC_INLINE_SET_STR_VALID_CB(exporter_config_override_str_validator)
    },
    {
        .key                = ARBITRARY_AUTH_DOMAIN_FEATURE,
        .desc               = "Enable/disable Arbitrary Auth Domain",
        .details            = "turn off feature: disabled\n"
                              "turn on feature: enabled\n"
                              "default: disabled",
        .val_type           = config_type_str,
        .component_types    = config_component_exporter | config_component_ot_exporter,
        .target_gid_types   = config_target_gid_type_cust | config_target_gid_type_zsroot,
        .str_default        = DEFAULT_ARBITRARY_AUTH_DOMAIN_FEATURE,
        .feature_group      = FEATURE_GROUP_ARBITRARY_DOMAINS,
        .value_traits       = config_value_traits_feature_enablement,
        ZPATH_CONFIG_OVERRIDE_DESC_INLINE_SET_STR_VALID_CB(exporter_config_override_str_validator)
    },
    {
        .key                = CLIPBOARD_FEATURE,
        .desc               = "Enable/disable PRA Clipboard feature",
        .details            = "turn off feature: disabled\n"
                              "turn on feature: enabled\n"
                              "default: disabled",
        .val_type           = config_type_str,
        .component_types    = config_component_exporter | config_component_ot_exporter,
        .target_gid_types   = config_target_gid_type_cust | config_target_gid_type_zsroot | config_target_gid_type_global,
        .str_default        = DEFAULT_CLIPBOARD,
        .feature_group      = FEATURE_GROUP_PRIVILEGED_REMOTE_ACCESS_ESSENTIALS,
        .value_traits       = config_value_traits_feature_enablement,
        ZPATH_CONFIG_OVERRIDE_DESC_INLINE_SET_STR_VALID_CB(exporter_config_override_str_validator)
    },
    {
        .key                = ENDUSER_APPROVALS,
        .desc               = "Enable/disable Enduser Approvals feature",
        .details            = "turn off feature: disabled\n"
                              "turn on feature: enabled\n"
                              "default: disabled",
        .val_type           = config_type_str,
        .component_types    = config_component_exporter | config_component_ot_exporter,
        .target_gid_types   = config_target_gid_type_cust | config_target_gid_type_zsroot | config_target_gid_type_global,
        .str_default        = DEFAULT_ENDUSER_APPROVALS,
        .feature_group      = FEATURE_GROUP_PRIVILEGED_REMOTE_ACCESS_ESSENTIALS,
        .value_traits       = config_value_traits_feature_enablement,
        ZPATH_CONFIG_OVERRIDE_DESC_INLINE_SET_STR_VALID_CB(exporter_config_override_str_validator)
    },
    {
        .key                = SESSION_RECORDING,
        .desc               = "Enable/disable PRA Session Recording feature",
        .details            = "turn off feature: disabled\n"
                              "turn on feature: enabled\n"
                              "default: disabled",
        .val_type           = config_type_str,
        .component_types    = config_component_exporter | config_component_ot_exporter,
        .target_gid_types   = config_target_gid_type_cust | config_target_gid_type_zsroot | config_target_gid_type_global,
        .str_default        = DEFAULT_SESSION_RECORDING,
        .feature_group      = FEATURE_GROUP_PRIVILEGED_REMOTE_ACCESS_ADVANCED,
        .value_traits       = config_value_traits_feature_enablement,
        ZPATH_CONFIG_OVERRIDE_DESC_INLINE_SET_STR_VALID_CB(exporter_config_override_str_validator)
    },
    {
        .key                = EXPORTER_CORS_FEATURE,
        .desc               = "Determines if cors is enabled/disabled",
        .details            = "turn off feature: disabled\n"
                              "turn on feature: enabled\n"
                              "default: disabled\n"
                              "NOTE: this is not currently safe so this description takes no effect\n"
                              "      It also has different defaults based on customer or domain lookups...",
        .val_type           = config_type_int,
        .component_types    = config_component_exporter | config_component_ot_exporter,
        .target_gid_types   = config_target_gid_type_cust | config_target_gid_type_inst | config_target_gid_type_global,
        .int_default        = 0,
        .int_range_lo       = 0,
        .int_range_hi       = 1,
        .value_traits       = config_value_traits_feature_enablement,
        .feature_group      = FEATURE_GROUP_CORS_REQUEST
    },
    {
        .key                = SESSION_MONITORING,
        .desc               = "Enable/disable PRA Session Monitoring feature",
        .details            = "turn off feature: disabled\n"
                              "turn on feature: enabled\n"
                              "default: disabled",
        .val_type           = config_type_str,
        .component_types    = config_component_exporter | config_component_ot_exporter,
        .target_gid_types   = config_target_gid_type_cust | config_target_gid_type_zsroot | config_target_gid_type_global,
        .str_default        = DEFAULT_SESSION_MONITORING,
        .feature_group      = FEATURE_GROUP_PRIVILEGED_REMOTE_ACCESS_ADVANCED,
        .value_traits       = config_value_traits_feature_enablement,
        ZPATH_CONFIG_OVERRIDE_DESC_INLINE_SET_STR_VALID_CB(exporter_config_override_str_validator)
    },
    {
        .key                = SESSION_MONITORING_FEATURE_GLOBAL_DISABLE,
        .desc               = "Global config override to disable PRA Session Monitoring",
        .details            = "0: PRA Session Monitoring is enabled\n"
            "1: PRA Session Monitoring is disabled\n"
            "Order of check - Public: global\n"
            "default: 0 - PRA Session Monitoring is enabled",
        .val_type           = config_type_int,
        .component_types    = config_component_exporter | config_component_ot_exporter,
        .target_gid_types   = config_target_gid_type_global,
        .int_range_lo       = 0,
        .int_range_hi       = 1,
        .int_default        = DEFAULT_SESSION_MONITORING_FEATURE_GLOBAL_DISABLE,
        .feature_group      = FEATURE_GROUP_PRIVILEGED_REMOTE_ACCESS_ADVANCED,
        .value_traits       = config_value_traits_systemwide,
    },
    {
        .key                = PRIVILEGED_DESKTOPS,
        .desc               = "Enable/disable PRA Desktops feature",
        .details            = "turn off feature: disabled\n"
                              "turn on feature: enabled\n"
                              "default: disabled",
        .val_type           = config_type_str,
        .component_types    = config_component_exporter | config_component_ot_exporter,
        .target_gid_types   = config_target_gid_type_cust | config_target_gid_type_zsroot | config_target_gid_type_global,
        .str_default        = DEFAULT_PRIVILEGED_DESKTOPS,
        .feature_group      = FEATURE_GROUP_PRIVILEGED_REMOTE_ACCESS_ADVANCED,
        .value_traits       = config_value_traits_feature_enablement,
    },
    {
        .key                = PRIVILEGED_DESKTOPS_FEATURE_GLOBAL_DISABLE,
        .desc               = "Global config override to disable PRA Desktops",
        .details            = "0: PRA Desktops is enabled\n"
            "1: PRA Desktops is disabled\n"
            "Order of check - Public: global\n"
            "default: 0 - PRA Desktops is enabled",
        .val_type           = config_type_int,
        .component_types    = config_component_exporter | config_component_ot_exporter,
        .target_gid_types   = config_target_gid_type_global,
        .int_range_lo       = 0,
        .int_range_hi       = 1,
        .int_default        = DEFAULT_PRIVILEGED_DESKTOPS_FEATURE_GLOBAL_DISABLE,
        .feature_group      = FEATURE_GROUP_PRIVILEGED_REMOTE_ACCESS_ADVANCED,
        .value_traits       = config_value_traits_systemwide,
    },
    {
        .key = PRA_RATE_LIMIT_LARGE_PORTAL_ACCESS,
        .desc = "Limit the number of concurrent large PRA portal access(scope, console API calls) on Exporter",
        .details =  "0: Access to all large PRA portals will be disabled\n"
                    "300: Maximum number of concurrent large PRA portal access allowed\n"
                    "Order of check - Public: component id, component grp id, global\n"
                    "default: 4 - Default number of concurrent Portal Scope API, Console API allowed",
        .val_type = config_type_int,
        .component_types = config_component_exporter | config_component_ot_exporter,
        .target_gid_types = config_target_gid_type_inst | config_target_gid_type_inst_grp | config_target_gid_type_global,
        .int_range_lo = 0,
        .int_range_hi = 300,
        .int_default = DEFAULT_PRA_RATE_LIMIT_LARGE_PORTAL_ACCESS,
        .feature_group      = FEATURE_GROUP_PRIVILEGED_REMOTE_ACCESS_ESSENTIALS,
        .value_traits       = config_value_traits_normal,
    },
    {
        .key                = EXPORTER_DISABLE_WINDOW_UPDATE_INNER_TUNNEL,
        .desc               = "Exporter support to disable window update by inner tunnel, ",
        .details            = "0: window update is enabled\n"
                              "1: no window update by inner tunnel\n",
        .val_type           = config_type_int,
        .component_types    = config_component_exporter | config_component_ot_exporter,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_global,
        .int_range_lo       = 0,
        .int_range_hi       = 1,
        .int_default        = DEFAULT_EXPORTER_DISABLE_WINDOW_UPDATE_INNER_TUNNEL,
        .feature_group      = FEATURE_GROUP_DISABLE_WINDOW_UPDATE_INNER_TUNNEL,
        .value_traits       = config_value_traits_feature_enablement
    },
    {
        .key                = EXPORTER_XFF_HEADER_FEATURE,
        .desc               = "Exporter feature to enable XFF header",
        .details            = "Exporter feature to enable XFF header\n"
                              "min: 0\n"
                              "max: 1\n"
                              "default: 0",
        .val_type           = config_type_int,
        .component_types    = config_component_exporter | config_component_ot_exporter,
        .target_gid_types   = config_target_gid_type_cust,
        .int_range_lo       = 0,
        .int_range_hi       = 1,
        .int_default        = DEFAULT_EXPORTER_XFF_HEADER_FEATURE,
        .feature_group      = FEATURE_GROUP_EXPORTER_XFF_HEADER,
        .value_traits       = config_value_traits_feature_enablement
    },
    {
        .key                = EXPORTER_FORWARDED_FOR_HEADER_FEATURE,
        .desc               = "Exporter feature to enable Forwarded header",
        .details            = "Exporter feature to enable Forwarded header\n"
                              "min: 0\n"
                              "max: 1\n"
                              "default: 0",
        .val_type           = config_type_int,
        .component_types    = config_component_exporter | config_component_ot_exporter,
        .target_gid_types   = config_target_gid_type_cust,
        .int_range_lo       = 0,
        .int_range_hi       = 1,
        .int_default        = DEFAULT_EXPORTER_FORWARDED_FOR_HEADER_FEATURE,
        .feature_group      = FEATURE_GROUP_EXPORTER_FORWARDED_FOR_HEADER,
        .value_traits       = config_value_traits_feature_enablement
    },
    {
        .key                = POLICY_REBUILD_BACKOFF_FEATURE,
        .desc               = "feature flag to backoff policy rebuilding",
        .details            = "1: feature is enabled\n"
                              "0: feature is disabled\n"
                              "default: 0 (Disabled, until enabled for all customers or a specific customer)\n",
        .val_type           = config_type_int,
        .component_types    = config_component_exporter | config_component_ot_exporter,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_cust | config_target_gid_type_global,
        .int_range_lo       = POLICY_REBUILD_BACKOFF_FEATURE_MIN,
        .int_range_hi       = POLICY_REBUILD_BACKOFF_FEATURE_MAX,
        .int_default        = POLICY_REBUILD_BACKOFF_FEATURE_DEFAULT,       /* Disabled by default */
        .value_traits       = config_value_traits_feature_enablement,
        .feature_group      = FEATURE_GROUP_POLICY_REBUILD
    },
    {
        .key                = POLICY_REBUILD_BACKOFF_INTERVAL_SEC,
        .desc               = "Time in seconds for policy rebuild backoff interval",
        .details            = "default: 5 sec (This is the time for which the policy rebuild will be deferred)\n",
        .val_type           = config_type_int,
        .component_types    = config_component_exporter | config_component_ot_exporter,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_cust | config_target_gid_type_global,
        .int_range_lo       = POLICY_REBUILD_BACKOFF_INTERVAL_SEC_MIN,
        .int_range_hi       = POLICY_REBUILD_BACKOFF_INTERVAL_SEC_MAX,
        .int_default        = POLICY_REBUILD_BACKOFF_INTERVAL_SEC_DEFAULT,
        .value_traits       = config_value_traits_normal,
        .feature_group      = FEATURE_GROUP_POLICY_REBUILD
    },
    {
        .key                = POLICY_REBUILD_BACKOFF_PERIODIC_CHECK_INTERVAL_SEC,
        .desc               = "Time in seconds for after which policy will be rebuilt if not built already",
        .details            = "default: 60 sec (This is time after which we periodically check if we have to rebuild policy if not already built)\n",
        .val_type           = config_type_int,
        .component_types    = config_component_exporter | config_component_ot_exporter,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_cust | config_target_gid_type_global,
        .int_range_lo       = POLICY_REBUILD_BACKOFF_PERIODIC_CHECK_INTERVAL_SEC_MIN,
        .int_range_hi       = POLICY_REBUILD_BACKOFF_PERIODIC_CHECK_INTERVAL_SEC_MAX,
        .int_default        = POLICY_REBUILD_BACKOFF_PERIODIC_CHECK_INTERVAL_SEC_DEFAULT,
        .value_traits       = config_value_traits_normal,
        .feature_group      = FEATURE_GROUP_POLICY_REBUILD
    },
    {
        .key                = POLICY_REBUILD_BACKOFF_FEATURE_HARD_DISABLED,
        .desc               = "Hard Disable flag to disable Policy Rebuild Backoff feature for all the customers",
        .details            = "1: feature is hard disabled for all customers\n"
                              "0: feature is not hard disabled for all customers\n"
                              "default: 0 (feature is not hard disabled for all customers)\n",
        .val_type           = config_type_int,
        .component_types    = config_component_exporter | config_component_ot_exporter,
        .target_gid_types   = config_target_gid_type_global,
        .int_range_lo       = POLICY_REBUILD_BACKOFF_FEATURE_HARD_DISABLED_MIN,
        .int_range_hi       = POLICY_REBUILD_BACKOFF_FEATURE_HARD_DISABLED_MAX,
        .int_default        = POLICY_REBUILD_BACKOFF_FEATURE_HARD_DISABLED_DEFAULT,       /* Disabled by default */
        .value_traits       = config_value_traits_systemwide,
        .feature_group      = FEATURE_GROUP_POLICY_REBUILD
    },
    {
        .key                = EXPORTER_CLEAR_COOKIES_FEATURE,
        .desc               = "Exporter feature to clear cookies on logout",
        .details            = "Exporter feature to clear cookies on logout\n"
                              "min: 0\n"
                              "max: 1\n"
                              "default: 0",
        .val_type           = config_type_int,
        .component_types    = config_component_exporter | config_component_ot_exporter,
        .target_gid_types   = config_target_gid_type_cust,
        .int_range_lo       = EXPORTER_CLEAR_COOKIES_FEATURE_MIN,
        .int_range_hi       = EXPORTER_CLEAR_COOKIES_FEATURE_MAX,
        .int_default        = DEFAULT_EXPORTER_CLEAR_COOKIES_FEATURE,
        .feature_group      = FEATURE_GROUP_EXPORTER_CLEAR_COOKIES,
        .value_traits       = config_value_traits_feature_enablement
    },
    {
        .key                = EXPORTER_SSL_ENABLE_CIPHER_KEY_256,
        .desc               = "Exporter feature to enabled the 256 bit cipher key",
        .details            = "Exporter feature to enable/disable 256 bit cipher key\n"
                              "min: 0\n"
                              "max: 1\n"
                              "default: 0",
        .val_type           = config_type_int,
        .component_types    = config_component_exporter | config_component_ot_exporter,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_global,
        .int_range_lo       = EXPORTER_SSL_ENABLE_CIPHER_KEY_256_MIN,
        .int_range_hi       = EXPORTER_SSL_ENABLE_CIPHER_KEY_256_MAX,
        .int_default        = EXPORTER_DEFAULT_SSL_ENABLE_CIPHER_KEY_256,
        .feature_group      = FEATURE_GROUP_EXPORTER_CIPHER_KEY,
        .value_traits       = config_value_traits_feature_enablement
    },
    {
        .key                = PRA_SESSION_PROCTORING_EMAIL_NOTIFICATION_FEATURE,
        .desc               = "Enable/disable session proctoring email notifications",
        .details            = "turn off feature: disabled\n"
                              "turn on feature: enabled\n"
                              "default: disabled",
        .val_type           = config_type_str,
        .component_types    = config_component_exporter | config_component_ot_exporter,
        .target_gid_types   = config_target_gid_type_cust | config_target_gid_type_zsroot,
        .str_default        = DEFAULT_PRA_SESSION_PROCTORING_EMAIL_NOTIFICATION_FEATURE,
        .feature_group      = FEATURE_GROUP_PRIVILEGED_REMOTE_ACCESS_ADVANCED,
        .value_traits       = config_value_traits_feature_enablement,
        ZPATH_CONFIG_OVERRIDE_DESC_INLINE_SET_STR_VALID_CB(exporter_config_override_str_validator)
    },
    {
        .key                = PRA_SESSION_PROCTORING_GOLBAL_EMAIL_SVC,
        .desc               = "Enable/disable session proctoring global email service usage",
        .details            = "0: turn off usage of global email svc: disabled\n"
                              "1: turn on usage of global email svc: enabled\n"
                              "default: disabled",
        .val_type           = config_type_int,
        .component_types    = config_component_exporter | config_component_ot_exporter,
        .target_gid_types   = config_target_gid_type_zsroot | config_target_gid_type_global,
        .int_range_lo       = 0,
        .int_range_hi       = 1,
        .int_default        = DEFAULT_PRA_SESSION_PROCTORING_GOLBAL_EMAIL_SVC,
        .feature_group      = FEATURE_GROUP_PRIVILEGED_REMOTE_ACCESS_ADVANCED,
        .value_traits       = config_value_traits_feature_enablement
    },
    {
        .key                = EXPORTER_MANAGED_CHROME_HARD_DISABLED,
        .desc               = "Exporter feature to hard disable the managed-chrome feature",
        .details            = "Exporter feature to hard disable the managed-chrome feature\n"
                              "min: 0\n"
                              "max: 1\n"
                              "default: 0",
        .val_type           = config_type_int,
        .component_types    = config_component_exporter | config_component_ot_exporter,
        .target_gid_types   = config_target_gid_type_global,
        .int_range_lo       = EXPORTER_MANAGED_CHROME_HARD_DISABLED_MIN,
        .int_range_hi       = EXPORTER_MANAGED_CHROME_HARD_DISABLED_MAX,
        .int_default        = EXPORTER_MANAGED_CHROME_HARD_DISABLED_DEFAULT,
        .feature_group      = FEATURE_GROUP_EXPORTER_MANAGED_CHROME,
        .value_traits       = config_value_traits_systemwide
    },
    {
        .key                = EXPORTER_MANAGED_CHROME_FEATURE,
        .desc               = "Exporter feature to enable the managed-chrome feature",
        .details            = "Exporter feature to enable/disable the managed-chrome feature\n"
                              "min: 0\n"
                              "max: 1\n"
                              "default: 0",
        .val_type           = config_type_int,
        .component_types    = config_component_exporter | config_component_ot_exporter,
        .target_gid_types   = config_target_gid_type_cust | config_target_gid_type_global,
        .int_range_lo       = EXPORTER_MANAGED_CHROME_FEATURE_MIN,
        .int_range_hi       = EXPORTER_MANAGED_CHROME_FEATURE_MAX,
        .int_default        = EXPORTER_MANAGED_CHROME_FEATURE_DEFAULT,
        .feature_group      = FEATURE_GROUP_EXPORTER_MANAGED_CHROME,
        .value_traits       = config_value_traits_feature_enablement
    },
    {
        .key                = EXPORTER_MANAGED_CHROME_EXPIRY_IN_S,
        .desc               = "Exporter feature to configure the expiry time in sec for managed-chrome feature",
        .details            = "Exporter feature to configure the expiry time in sec for managed-chrome feature\n"
                              "min: 5*60\n"
                              "max: 30*60\n"
                              "default: 15*60",
        .val_type           = config_type_int,
        .component_types    = config_component_exporter | config_component_ot_exporter,
        .target_gid_types   = config_target_gid_type_cust | config_target_gid_type_global,
        .int_range_lo       = EXPORTER_MANAGED_CHROME_EXPIRY_IN_S_MIN,
        .int_range_hi       = EXPORTER_MANAGED_CHROME_EXPIRY_IN_S_MAX,
        .int_default        = EXPORTER_MANAGED_CHROME_EXPIRY_IN_S_DEFAULT,
        .feature_group      = FEATURE_GROUP_EXPORTER_MANAGED_CHROME,
        .value_traits       = config_value_traits_normal
    },
    {
        .key                 = CONFIG_FEATURE_ENQUEUE_DEQUEUE_FIX,
        .desc                = "Exporter Enq Deq Fix feature for customers",
        .details             = "0: Disable Enq Deq Fix feature\n"
                               "1: Enable Enq Deq Fix feature\n"
                               "default: 1 (Enable this feature by default)",
        .val_type            = config_type_int,
        .component_types     = config_component_exporter | config_component_ot_exporter,
        .target_gid_types    = config_target_gid_type_cust | config_target_gid_type_global,
        .int_range_lo        = CONFIG_FEATURE_ENQUEUE_DEQUEUE_FIX_MIN,
        .int_range_hi        = CONFIG_FEATURE_ENQUEUE_DEQUEUE_FIX_MAX,
        .int_default         = CONFIG_FEATURE_ENQUEUE_DEQUEUE_FIX_DEFAULT,
        .feature_group       = FEATURE_GROUP_ENQUEUE_DEQUEUE_FIX,
        .value_traits        = config_value_traits_feature_enablement,
    },
    {
        .key                 = CONFIG_FEATURE_EXPORTER_PROPAGATE_FIN_FROM_REMOTE,
        .desc                = "Propagate FIN to browser",
        .details             = "0: Dont Propagate FIN message\n"
                               "1: Propagate FIN message\n"
                               "default: 0 (Disable this feature by default)",
        .val_type            = config_type_int,
        .component_types     = config_component_exporter | config_component_ot_exporter,
        .target_gid_types    = config_target_gid_type_cust | config_target_gid_type_inst | config_target_gid_type_global,
        .int_range_lo        = MIN_CONFIG_FEATURE_EXPORTER_PROPAGATE_FIN_FROM_REMOTE,
        .int_range_hi        = MAX_CONFIG_FEATURE_EXPORTER_PROPAGATE_FIN_FROM_REMOTE,
        .int_default         = DEFAULT_CONFIG_FEATURE_PROPAGATE_FIN_FROM_REMOTE,
        .feature_group       = FEATURE_GROUP_EXPORTER_BROWSER_FIN_PROPAGATION,
        .value_traits        = config_value_traits_feature_enablement,
    },
    {
        .key                 = CONFIG_FEATURE_EXPORTER_FIN_DAMPENING_TIMEOUT,
        .desc                = "Dampening timeout for evbuf flush",
        .details             = "1-10 : Dampening timeout in seconds"
                               "default: 2 seconds",
        .val_type            = config_type_int,
        .component_types     = config_component_exporter | config_component_ot_exporter,
        .target_gid_types    = config_target_gid_type_cust | config_target_gid_type_global,
        .int_range_lo        = MIN_CONFIG_FEATURE_EXPORTER_FIN_DAMPENING_TIMEOUT,
        .int_range_hi        = MAX_CONFIG_FEATURE_EXPORTER_FIN_DAMPENING_TIMEOUT,
        .int_default         = DEFAULT_CONFIG_FEATURE_EXPORTER_FIN_DAMPENING_TIMEOUT,
        .feature_group       = FEATURE_GROUP_EXPORTER_BROWSER_FIN_PROPAGATION,
        .value_traits        = config_value_traits_normal,
    },
    {
        .key                = PRA_ADVANCED_FILE_TRANSFER ,
        .desc               = "Enable/disable PRA My-files FT feature",
        .details            = "turn off feature: disabled\n"
                              "turn on feature: enabled\n"
                              "default: disabled",
        .val_type           = config_type_str,
        .component_types    = config_component_exporter | config_component_ot_exporter,
        .target_gid_types   = config_target_gid_type_cust | config_target_gid_type_zsroot | config_target_gid_type_global,
        .str_default        = DEFAULT_ADVANCED_FILE_TRANSFER,
        .feature_group      = FEATURE_GROUP_PRIVILEGED_REMOTE_ACCESS_ADVANCED,
        .value_traits       = config_value_traits_feature_enablement,
        ZPATH_CONFIG_OVERRIDE_DESC_INLINE_SET_STR_VALID_CB(exporter_config_override_str_validator)
    },
    {
        .key                = ADVANCED_FILE_TRANSFER_FEATURE_GLOBAL_DISABLE,
        .desc               = "Global config override to disable PRA My-files feature",
        .details            = "0: PRA My-Files is enabled\n"
                              "1: PRA My-Files is disabled\n"
                              "Order of check - Public: global\n"
                              "default: 0 - PRA My-files is enabled",
        .val_type           = config_type_int,
        .component_types    = config_component_exporter | config_component_ot_exporter,
        .target_gid_types   = config_target_gid_type_global,
        .int_range_lo       = 0,
        .int_range_hi       = 1,
        .int_default        = DEFAULT_ADVANCED_FILE_TRANSFER_FEATURE_GLOBAL_DISABLE,
        .feature_group      = FEATURE_GROUP_PRIVILEGED_REMOTE_ACCESS_ADVANCED,
        .value_traits       = config_value_traits_systemwide,
    },
    {
        .key                = PRA_PORTAL_FILE_TRANSFER,
        .desc               = "Enable/disable PRA portal based file transfer",
        .details            = "0: turn off usage of portal file transfer: disabled\n"
                              "1: turn on usage of portal file transfer: enabled\n"
                              "default: disabled",
        .val_type           = config_type_int,
        .component_types    = config_component_exporter | config_component_ot_exporter,
        .target_gid_types   = config_target_gid_type_cust | config_target_gid_type_zsroot | config_target_gid_type_global,
        .int_range_lo       = 0,
        .int_range_hi       = 1,
        .int_default        = DEFAULT_PRA_PORTAL_FILE_TRANSFER,
        .feature_group      = FEATURE_GROUP_PRIVILEGED_REMOTE_ACCESS_ADVANCED,
        .value_traits       = config_value_traits_feature_enablement
    },
    {
        .key                = UX_DISPLAY_QUALITY,
        .desc               = "Enable/disable PRA UX-Display quality feature",
        .details            = "turn off feature: disabled\n"
                              "turn on feature: enabled\n"
                              "default: disabled",
        .val_type           = config_type_str,
        .component_types    = config_component_exporter | config_component_ot_exporter,
        .target_gid_types   = config_target_gid_type_cust | config_target_gid_type_zsroot | config_target_gid_type_global,
        .str_default        = DEFAULT_UX_DISPLAY_QUALITY,
        .feature_group      = FEATURE_GROUP_PRIVILEGED_REMOTE_ACCESS_ADVANCED,
        .value_traits       = config_value_traits_feature_enablement,
        ZPATH_CONFIG_OVERRIDE_DESC_INLINE_SET_STR_VALID_CB(exporter_config_override_str_validator)
    },
    {
        .key                = UX_DISPLAY_QUALITY_FEATURE_GLOBAL_DISABLE,
        .desc               = "Global config override to disable PRA UX-Display quality feature",
        .details            = "0: PRA UX-Display quality is enabled\n"
                              "1: PRA UX-Display quality is disabled\n"
                              "Order of check - Public: global\n"
                              "default: 0 - PRA UX-Display quality is enabled",
        .val_type           = config_type_int,
        .component_types    = config_component_exporter | config_component_ot_exporter,
        .target_gid_types   = config_target_gid_type_global,
        .int_range_lo       = 0,
        .int_range_hi       = 1,
        .int_default        = DEFAULT_UX_DISPLAY_QUALITY_FEATURE_GLOBAL_DISABLE,
        .feature_group      = FEATURE_GROUP_PRIVILEGED_REMOTE_ACCESS_ADVANCED,
        .value_traits       = config_value_traits_systemwide,
    },
    {
        .key                = EXPORTER_MANAGED_CHROME_2_FEATURE,
        .desc               = "Exporter feature to enable the managed-chrome-2 feature",
        .details            = "Exporter feature to enable/disable the managed-chrome-2 feature\n"
                              "min: 0\n"
                              "max: 1\n"
                              "default: 0",
        .val_type           = config_type_int,
        .component_types    = config_component_exporter,
        .target_gid_types   = config_target_gid_type_cust | config_target_gid_type_global,
        .int_range_lo       = EXPORTER_MANAGED_CHROME_2_FEATURE_MIN,
        .int_range_hi       = EXPORTER_MANAGED_CHROME_2_FEATURE_MAX,
        .int_default        = EXPORTER_MANAGED_CHROME_2_FEATURE_DEFAULT,
        .feature_group      = FEATURE_GROUP_EXPORTER_MANAGED_CHROME,
        .value_traits       = config_value_traits_feature_enablement
    },
};

int  exporter_register_config_overrides(void) {
    int res;
    int len = sizeof(exporter_descriptions) / sizeof(struct zpath_config_override_desc);

    for (int i = 0; i < len; i++) {
        res = zpath_config_override_desc_register(&exporter_descriptions[i]);

        if (res) {
             EXPORTER_LOG(AL_ERROR, "Error registering for exporter config overrides");
             return res;
        }
    }

    return ZPATH_RESULT_NO_ERROR;
}
