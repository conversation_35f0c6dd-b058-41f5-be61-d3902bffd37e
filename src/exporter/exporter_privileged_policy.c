#include "zpath_lib/zpath_lib.h"
#include "zcrypto_lib/zcrypto_lib.h"
#include "zpath_lib/zpath_instance.h"
#include "exporter/exporter.h"
#include "exporter/exporter_conn.h"
#include "exporter/exporter_request.h"
#include "exporter/exporter_guac_api.h"
#include "zpn_ot/zpn_ot.h"
#include "exporter/exporter_private.h"
#include "exporter/exporter_guac_proxy.h"
#include "exporter/exporter_guac_cred_pool.h"

//Smaller limit is for username, password and passphrase, which has UI limit of 72 chars
#define SMALLER_CREDENTIALS_FIELD_LIMIT  1*1024
//Bigger limit is for SSH Key, which has UI limit as 8192, but encoding limit is 4096
#define BIGGER_CREDENTIALS_FIELD_LIMIT   4*4096

#define GID_STR_LEN 100

static
struct exporter_privileged_policy_stats_s {                                     /* _ARGO: object_definition */
    uint64_t request_privileged_cryptostore_cred_collection_async;              /* _ARGO: integer */
    uint64_t request_privileged_cryptostore_cred_collection_success;            /* _ARGO: integer */
    uint64_t request_privileged_cryptostore_cred_collection_failed;             /* _ARGO: integer */
    uint64_t request_privileged_cryptostore_cred_collection_not_found;             /* _ARGO: integer */
}stats;
#include "exporter/exporter_privileged_policy_compiled_c.h"

static struct argo_structure_description* exporter_privileged_policy_stats_description = NULL;

static int exporter_credential_get_by_id(struct exporter_request *request, int64_t customer_gid);
int is_privileged_credential_pool_disabled(int64_t customer_gid);
static int fetch_privileged_credentials_djb(struct exporter_request *request);

int exporter_fetch_mapped_credentials(void *callback_cookie, int64_t zpn_rule_gid, int64_t customer_gid)
{
    struct exporter_request *request = NULL;
    struct zpn_credential_rule_mapping *console_credential_rule_mapping[1] = {NULL};
    int res = ZPATH_RESULT_NO_ERROR;

    if (!callback_cookie) {
        EXPORTER_LOG(AL_ERROR, "invalid args callback_cookie %p", callback_cookie);
        return ZPATH_RESULT_ERR;
    }
    request = callback_cookie;

    // for async workflow of credential id when request->guac_info->credential_id
    // is already done once, skip the following mapping table or pool look up
    // jump directly to credential ID lookup
    if (!request->guac_info->credential_id) {
        if (!is_privileged_credential_pool_disabled(customer_gid)) {
            res = exporter_fetch_unused_privileged_credential_id_from_pool(request);
            if (res != ZPATH_RESULT_NO_ERROR)  goto process_result;
        } else {
            res = zpn_credential_rule_mapping_get_by_zpn_rule_gid(zpn_rule_gid,
                   console_credential_rule_mapping, exporter_request_wally_callback, callback_cookie, 0);
            if (res == ZPATH_RESULT_NOT_FOUND) {
                EXPORTER_LOG(AL_ERROR, "Credential Rule mapping by GID not found by Wally");
                exporter_guac_api_stats_increment(CREDENTIAL_POOL_ERR_CRED_NOT_FOUND_IN_DB);
            }
            if (res != ZPATH_RESULT_NO_ERROR) goto process_result;
            if (!console_credential_rule_mapping[0]) {
                EXPORTER_LOG(AL_DEBUG, "console_credential_rule_mapping entry is NULL");
                return res;
            }
            if (!console_credential_rule_mapping[0]->credential_id) {
                EXPORTER_LOG(AL_ERROR, "console_credential_rule_mapping entry is missing credential_id");
                return res;
            }
            EXPORTER_LOG(AL_DEBUG, "console_credential_rule_mapping entry is found for zpn_rule_gid %"PRId64""
                         " id %"PRId64" credential_id %"PRId64"", zpn_rule_gid, console_credential_rule_mapping[0]->id, console_credential_rule_mapping[0]->credential_id);
            request->guac_info->credential_id = console_credential_rule_mapping[0]->credential_id;
        }
    }
    res = exporter_credential_get_by_id(request, customer_gid);
process_result:
    if (res == ZPATH_RESULT_ASYNCHRONOUS) {
        __sync_fetch_and_add_4(&(request->async_count), 1);
        request->async_state = async_state_fetch_privileged_credentials;
        EXPORTER_LOG(AL_DEBUG, "Fetching credential information asynchronously");
    }
    return res;
}

static int exporter_credential_get_by_id(struct exporter_request *request, int64_t customer_gid)
{
    int res = ZPATH_RESULT_NO_ERROR;
    struct zpn_credentials *credential[1] = {NULL};
    void *callback_cookie  = request;

    res = zpn_credentials_get_by_id(request->guac_info->credential_id, customer_gid,
                                    credential, exporter_request_wally_callback, callback_cookie, 0);
    if (res == ZPATH_RESULT_NO_ERROR && credential[0]) {
        if (!request->guac_info->diag_info.credential_id) {
            request->guac_info->diag_info.credential_id = EXPORTER_CALLOC(GID_STR_LEN);
            snprintf(request->guac_info->diag_info.credential_id, GID_STR_LEN, "%"PRId64"", request->guac_info->credential_id);
        }
        if (credential[0]->password && !request->guac_info->exporter_credentials.password) {
            request->guac_info->exporter_credentials.password_len = strnlen(credential[0]->password, SMALLER_CREDENTIALS_FIELD_LIMIT);
            request->guac_info->exporter_credentials.password = EXPORTER_STRDUP(credential[0]->password, request->guac_info->exporter_credentials.password_len);
        }
        if (credential[0]->passphrase && !request->guac_info->exporter_credentials.passphrase) {
            request->guac_info->exporter_credentials.passphrase_len = strnlen(credential[0]->passphrase, SMALLER_CREDENTIALS_FIELD_LIMIT);
            request->guac_info->exporter_credentials.passphrase = EXPORTER_STRDUP(credential[0]->passphrase, request->guac_info->exporter_credentials.passphrase_len);
        }
        if (credential[0]->private_key && !request->guac_info->exporter_credentials.private_key) {
            if (!request->guac_info->exporter_credentials.pkey_done) {
                request->guac_info->exporter_credentials.private_key_len = strnlen(credential[0]->private_key, BIGGER_CREDENTIALS_FIELD_LIMIT);
                request->guac_info->exporter_credentials.private_key = EXPORTER_STRDUP(credential[0]->private_key, request->guac_info->exporter_credentials.private_key_len);
            }
        }
        if (credential[0]->user_name && !request->guac_info->exporter_credentials.user_name) {
            request->guac_info->exporter_credentials.user_name_len = strnlen(credential[0]->user_name, SMALLER_CREDENTIALS_FIELD_LIMIT);
            request->guac_info->exporter_credentials.user_name = EXPORTER_STRDUP(credential[0]->user_name, request->guac_info->exporter_credentials.user_name_len);
        }
        if (credential[0]->user_domain && !request->guac_info->exporter_credentials.user_domain) {
            request->guac_info->exporter_credentials.user_domain_len = strnlen(credential[0]->user_domain, SMALLER_CREDENTIALS_FIELD_LIMIT);
            request->guac_info->exporter_credentials.user_domain = EXPORTER_STRDUP(credential[0]->user_domain, request->guac_info->exporter_credentials.user_domain_len);
        }
        if (credential[0]->cred_type && !request->guac_info->exporter_credentials.cred_type) {
            request->guac_info->exporter_credentials.cred_type = EXPORTER_STRDUP(credential[0]->cred_type, strnlen(credential[0]->cred_type, SMALLER_CREDENTIALS_FIELD_LIMIT));
        }
        EXPORTER_LOG(AL_DEBUG, "Credential ID %"PRId64" is found", credential[0]->id);
    } else if (res == ZPATH_RESULT_NOT_FOUND) {
        exporter_guac_api_stats_increment(CREDENTIAL_POOL_ERR_CRED_NOT_FOUND_IN_DB);
        EXPORTER_LOG(AL_ERROR, "Credential ID not found by wally");
    } else {
        EXPORTER_LOG(AL_DEBUG, "Requested data not found for table zpn_credentials, res=%d", res);
    }

    return res;
}

static int exporter_credentials_crypto_cb(void *callback_data, int64_t callback_int, int status, const char *status_msg)
{
    struct exporter_request *request = (struct exporter_request*)callback_data;
    int res = ZPATH_RESULT_NO_ERROR;

    if (!request) {
        return ZPATH_RESULT_ERR;
    }
    if (status != 200) {
        EXPORTER_LOG(AL_ERROR, "%s: Exporter decrypt credentials failed: %d %s", request->name, status, status_msg ? status_msg : "");
        /* Async callback is needed to release the async_count and cleanup */
        exporter_set_async_state(request, async_state_done);
        (void)exporter_request_async_callback(callback_data, async_state_done);
        __sync_add_and_fetch_8(&stats.request_privileged_cryptostore_cred_collection_failed, 1);
    } else {
        EXPORTER_LOG(AL_DEBUG, "%s: Exporter decrypt credentials successful", request->name);
        res = exporter_request_async_callback(callback_data, callback_int);
        if (res) {
            EXPORTER_LOG(AL_ERROR, "%s: Received error on conn_wake: Implement me/destroy request", request->name);
            exporter_request_destroy(request);
        }
    }
    return res;
}

void exporter_cleanup_credentials(struct exporter_request *request)
{
    uint16_t user_name_len = request->guac_info->exporter_credentials.user_name_len;
    uint16_t user_domain_len = request->guac_info->exporter_credentials.user_domain_len;

    if (request->guac_info->exporter_credentials.user_name) {
        if (request->mt) {
            request->mt->console_user = EXPORTER_CALLOC(user_name_len + user_domain_len + 2);
            memcpy(request->mt->console_user, request->guac_info->exporter_credentials.user_name, user_name_len);
        }
        EXPORTER_FREE(request->guac_info->exporter_credentials.user_name);
        request->guac_info->exporter_credentials.user_name = NULL;
    }
    if (request->guac_info->exporter_credentials.cred_type) {
        EXPORTER_FREE(request->guac_info->exporter_credentials.cred_type);
        request->guac_info->exporter_credentials.cred_type = NULL;
    }
    if (request->guac_info->exporter_credentials.user_domain) {
        if (request->mt && request->mt->console_user) {
            memcpy(request->mt->console_user+user_name_len, "@", 1);
            memcpy(request->mt->console_user+user_domain_len+1, request->guac_info->exporter_credentials.user_domain, user_domain_len);
        }
        EXPORTER_FREE(request->guac_info->exporter_credentials.user_domain);
        request->guac_info->exporter_credentials.user_domain = NULL;
    }
    if (request->guac_info->exporter_credentials.password) {
        memset(request->guac_info->exporter_credentials.password, '\0', request->guac_info->exporter_credentials.password_len);
        EXPORTER_FREE(request->guac_info->exporter_credentials.password);
        request->guac_info->exporter_credentials.password = NULL;
    }
    if (request->guac_info->exporter_credentials.passphrase) {
        memset(request->guac_info->exporter_credentials.passphrase, '\0', request->guac_info->exporter_credentials.passphrase_len);
        EXPORTER_FREE(request->guac_info->exporter_credentials.passphrase);
        request->guac_info->exporter_credentials.passphrase = NULL;
    }
    if (request->guac_info->exporter_credentials.private_key) {
        memset(request->guac_info->exporter_credentials.private_key, '\0', request->guac_info->exporter_credentials.private_key_len);
        EXPORTER_FREE(request->guac_info->exporter_credentials.private_key);
        request->guac_info->exporter_credentials.private_key = NULL;
    }
}

int exporter_decrypt_credentials_all_type(struct exporter_request *request, char *encrypted_str, enum zpn_console_login_type login_type)
{
    const char *decrypted = NULL;
    int res = 0;
    /* Decrypt the encrypted credentials if they are present */
    res = zcrypto_lib_get(EXPORTER_CRYPTO_STORE_KEY_ID,
                          encrypted_str,
                          &decrypted,
                          exporter_credentials_crypto_cb,
                          request,
                          0);
    if (res) {
        EXPORTER_LOG(AL_DEBUG, "%s:zcrypto_lib_get returned %s\n", request->name, zpath_result_string(res));
        if (res == ZPATH_RESULT_ASYNCHRONOUS) {
            EXPORTER_LOG(AL_DEBUG, "%s: Fetching encrypted key asynchronously", request->name);
            __sync_fetch_and_add_4(&(request->async_count), 1);
            request->async_state = async_state_fetch_privileged_credentials;
            __sync_add_and_fetch_8(&stats.request_privileged_cryptostore_cred_collection_async, 1);
            return res;
        } else if (res == ZPATH_RESULT_NOT_FOUND) {
           EXPORTER_LOG(AL_ERROR, "%s: zcrypto_lib_get result not found", request->name);
            __sync_add_and_fetch_8(&stats.request_privileged_cryptostore_cred_collection_not_found, 1);
        } else {
            EXPORTER_LOG(AL_ERROR, "%s: zcrypto_lib_get returned %s", request->name, zpath_result_string(res));
            __sync_add_and_fetch_8(&stats.request_privileged_cryptostore_cred_collection_failed, 1);
        }
        goto err;
    } else if (decrypted) {
        __sync_add_and_fetch_8(&stats.request_privileged_cryptostore_cred_collection_success, 1);
        switch (login_type) {
            case zpn_console_login_type_password:
                request->guac_info->exporter_credentials.decrypted_password_len = strnlen(decrypted, SMALLER_CREDENTIALS_FIELD_LIMIT);
                request->guac_info->exporter_credentials.decrypted_password = EXPORTER_STRDUP((char *)decrypted, request->guac_info->exporter_credentials.decrypted_password_len);
                memset(request->guac_info->exporter_credentials.password, '\0', request->guac_info->exporter_credentials.password_len);
                EXPORTER_FREE(request->guac_info->exporter_credentials.password);
                request->guac_info->exporter_credentials.password = NULL;
                break;
            case zpn_console_login_type_passphrase:
                request->guac_info->exporter_credentials.decrypted_passphrase_len = strnlen(decrypted, SMALLER_CREDENTIALS_FIELD_LIMIT);
                request->guac_info->exporter_credentials.decrypted_passphrase = EXPORTER_STRDUP((char *)decrypted, request->guac_info->exporter_credentials.decrypted_passphrase_len);
                memset(request->guac_info->exporter_credentials.passphrase, '\0', request->guac_info->exporter_credentials.passphrase_len);
                EXPORTER_FREE(request->guac_info->exporter_credentials.passphrase);
                request->guac_info->exporter_credentials.passphrase = NULL;
                break;
            case zpn_console_login_type_private_key:
                request->guac_info->exporter_credentials.decrypted_private_key_len = strnlen(decrypted, BIGGER_CREDENTIALS_FIELD_LIMIT);
                request->guac_info->exporter_credentials.decrypted_private_key = EXPORTER_STRDUP((char *)decrypted, request->guac_info->exporter_credentials.decrypted_private_key_len);
                request->guac_info->exporter_credentials.pkey_done = 1;
                memset(request->guac_info->exporter_credentials.private_key, '\0', request->guac_info->exporter_credentials.private_key_len);
                EXPORTER_FREE(request->guac_info->exporter_credentials.private_key);
                request->guac_info->exporter_credentials.private_key = NULL;
                break;
            default:
                break;
        }
    }
    return res;

err:
    exporter_cleanup_credentials(request);
    return res;
}

int exporter_decrypt_credentials(struct exporter_request *request)
{
    /* Decrypt the encrypted credentials if they are present */
    int res = 0;
    if (request->guac_info->exporter_credentials.password) {
        return exporter_decrypt_credentials_all_type(request, request->guac_info->exporter_credentials.password, zpn_console_login_type_password);
    } else if (request->guac_info->exporter_credentials.private_key || request->guac_info->exporter_credentials.passphrase) {
        if (!request->guac_info->exporter_credentials.pkey_done && request->guac_info->exporter_credentials.private_key) {
            res = exporter_decrypt_credentials_all_type(request, request->guac_info->exporter_credentials.private_key, zpn_console_login_type_private_key);
            if (res != ZPATH_RESULT_NO_ERROR) {
                return res;
            }
        }
        if (request->guac_info->exporter_credentials.pkey_done && request->guac_info->exporter_credentials.passphrase) {
            return exporter_decrypt_credentials_all_type(request, request->guac_info->exporter_credentials.passphrase, zpn_console_login_type_passphrase);
        }
    }
    return res;
}

int exporter_privileged_policy_stats_dump(struct zpath_debug_state*   request_state,
                                             const char**                query_values,
                                             int                         query_value_count,
                                             void*                       cookie)
{
    char jsonout[10000];

    if (ARGO_RESULT_NO_ERROR ==
            argo_structure_dump(exporter_privileged_policy_stats_description, &stats, jsonout, sizeof(jsonout),
                                NULL, 1)){
        ZDP("%s\n", jsonout);
    }

    return ZPATH_RESULT_NO_ERROR;
}

int exporter_privileged_policy_stats_init()
{
    const struct argo_log_registered_structure *s = NULL;
    if (!(exporter_privileged_policy_stats_description =
                argo_register_global_structure(EXPORTER_PRIVILEGED_POLICY_STATS_S_HELPER))) {
        return ZPATH_RESULT_ERR;
    }
    s = argo_log_register_structure(argo_log_get("statistics_log"),
                                     "exporter_privileged_policy",
                                     AL_INFO,
                                     60*1000*1000,
                                     exporter_privileged_policy_stats_description,
                                     &stats,
                                     1,
                                     NULL,
                                     NULL);
    if (!s) {
        EXPORTER_LOG(AL_CRITICAL, "Could not register exporter_privileged_policy stats - statistics_log not initialized?");
        return ZPATH_RESULT_ERR;
    }
    return ZPATH_RESULT_NO_ERROR;
}


int exporter_reprocess_fetch_privileged_credentials(struct exporter_request *request)
{
    int res = exporter_guac_proxy_process_server_args_cb(&request->guac_info->guac_parser, NULL);
    if (res < 0) {
        res = ZPATH_RESULT_ERR;
    }
    return res;
}

static char* exporter_get_entity_id_str(uint64_t id)
{
    char *s = NULL;
    char entity_id_str[256];
    snprintf(entity_id_str, 256, "%"PRId64"", id);
    s = EXPORTER_STRDUP(entity_id_str, strlen(entity_id_str));
    return s;
}

#define CRYPTO_STORE_ERROR_STR "Error in fetching credentials"
int exporter_fetch_privileged_credentials(struct exporter_request *request)
{
    int res = ZPATH_RESULT_NO_ERROR;

    if (request->is_djb) {
        return fetch_privileged_credentials_djb(request);
    }

    res = exporter_fetch_mapped_credentials((void*)request, request->guac_info->cred_map_rule_id,
            request->conn->exporter_domain->customer_gid);
    if (res == ZPATH_RESULT_ASYNCHRONOUS) {
        return res;
    } else if (res != ZPATH_RESULT_NO_ERROR) {
        EXPORTER_LOG(AL_ERROR, "%s: FATAL: couldn't find credentials, can't continue", request->name);
        goto err;
    }
    res = exporter_decrypt_credentials(request);

    if (res == ZPATH_RESULT_ASYNCHRONOUS) {
        return res;
    } else if (res != ZPATH_RESULT_NO_ERROR) {
        EXPORTER_LOG(AL_ERROR, "%s: FATAL: couldn't decrypt credentials, can't continue", request->name);
        if (request->mt && request->mt->guac_error_string) {
            EXPORTER_FREE(request->mt->guac_error_string);
        }
        if (request->mt) {
            request->mt->guac_error_string = EXPORTER_STRDUP(CRYPTO_STORE_ERROR_STR, strlen(CRYPTO_STORE_ERROR_STR));
            request->mt->cred_policy_id = request->guac_info->cred_map_rule_id;
            if (!request->mt->credential_id && request->guac_info->diag_info.credential_id) {
                request->mt->credential_id = EXPORTER_STRDUP(request->guac_info->diag_info.credential_id, strlen(request->guac_info->diag_info.credential_id));
            }
            if (request->guac_info->credential_ctxt) {
                struct cred_ctxt *ctxt = request->guac_info->credential_ctxt;
                if (!request->mt->credential_id && ctxt->credential_id) {
                    request->mt->credential_id = exporter_get_entity_id_str(ctxt->credential_id);
                }
                if (!request->mt->credential_pool_id && ctxt->pool_id) {
                    request->mt->credential_pool_id = exporter_get_entity_id_str(ctxt->pool_id);
                }
            }
            zpn_fohh_client_exporter_send_exporter_log_data(request->mt);
        } else {
            request->guac_info->diag_info.cred_policy_id = request->guac_info->cred_map_rule_id;
        }
        goto err;
    }
    if (request->mt) {
        request->mt->cred_policy_id = request->guac_info->cred_map_rule_id;
        if (!request->mt->credential_id && request->guac_info->diag_info.credential_id) {
            request->mt->credential_id = EXPORTER_STRDUP(request->guac_info->diag_info.credential_id, strlen(request->guac_info->diag_info.credential_id));
        }
        if (request->guac_info->credential_ctxt) {
            struct cred_ctxt *ctxt = request->guac_info->credential_ctxt;
            if (!request->mt->credential_id && ctxt->credential_id) {
                request->mt->credential_id = exporter_get_entity_id_str(ctxt->credential_id);
            }
            if (!request->mt->credential_pool_id && ctxt->pool_id) {
                request->mt->credential_pool_id = exporter_get_entity_id_str(ctxt->pool_id);
            }
        }
    } else {
        EXPORTER_LOG(AL_INFO, "%s: Not found mt, copying cred_map_rule_id to diag_info, %"PRId64", credential_id%"PRId64,
                     request->name, request->guac_info->cred_map_rule_id, request->guac_info->credential_id);
        request->guac_info->diag_info.cred_policy_id = request->guac_info->cred_map_rule_id;
    }
    return ZPATH_RESULT_NO_ERROR;

err:
    exporter_guac_send_response(request,
            HTTP_STATUS_INTERNAL_SERVER_ERROR,
            "%s",
            "Disconnected from server : Internal Server Error");
    return ZPATH_RESULT_ERR;
}

enum zpn_console_credential_type  get_cred_type( char *cred_type) {

    if (!strcmp(cred_type,"USERNAME_PASSWORD")) {
        return zpn_console_credential_type_username_password;
    } else if (!strcmp(cred_type,"SSH_KEY")) {
        return zpn_console_credential_type_ssh_key;
    } else if (!strcmp(cred_type,"PASSWORD")) {
        return zpn_console_credential_type_password;
    } else {
        return zpn_console_credential_type_unknown;
    }
}

/* Will be invoked during Guacd handshake args callback via exporter_fetch_privileged_credentials for DJB */
static int fetch_privileged_credentials_djb(struct exporter_request *request)
{
    /* copy details from request->djb_info
     * into request->guac_info->exporter_credentials
     * Data will be plugged into guac connect instruction
     */
    if (!request->djb_info->admin_username || !request->djb_info->admin_pwd) {
        EXPORTER_LOG(AL_ERROR, "%s: Failing DJB websocket tunnel request due to Empty credentials", request->name);
        return ZPATH_RESULT_ERR;
    }

    if (!request->guac_info->exporter_credentials.user_name) {
        request->guac_info->exporter_credentials.user_name_len = (uint16_t)strnlen(request->djb_info->admin_username, SMALLER_CREDENTIALS_FIELD_LIMIT);
        request->guac_info->exporter_credentials.user_name = EXPORTER_STRDUP(request->djb_info->admin_username, request->guac_info->exporter_credentials.user_name_len);
    }

    if (!request->guac_info->exporter_credentials.decrypted_password) {
        request->guac_info->exporter_credentials.decrypted_password_len = (uint16_t)strnlen(request->djb_info->admin_pwd, SMALLER_CREDENTIALS_FIELD_LIMIT);
        request->guac_info->exporter_credentials.decrypted_password = EXPORTER_STRDUP(request->djb_info->admin_pwd, request->guac_info->exporter_credentials.decrypted_password_len);
        EXPORTER_FREE(request->djb_info->admin_pwd);
        request->djb_info->admin_pwd = NULL;
    }
    if (!request->guac_info->exporter_credentials.cred_type) {
        request->guac_info->exporter_credentials.cred_type = EXPORTER_STRDUP("USERNAME_PASSWORD", strlen("USERNAME_PASSWORD"));
    }

    return ZPATH_RESULT_NO_ERROR;
}
