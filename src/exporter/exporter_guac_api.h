/*
 * exporter_guac_api.h. Copyright (C) 2021 Zscaler Inc. All Rights Reserved
 *
 * Handle Guacamole APIs.
 */
#ifndef __EXPORTER_GUAC_API_H__
#define __EXPORTER_GUAC_API_H__

#include "exporter/exporter_private.h"
#include "zpath_lib/zpath_config_override_keys.h"
#include "zcrypt/zcrypt.h"
#include "exporter/exporter_recording.h"
#include "exporter/exporter_guac_sess_sharing.h"
#include "fohh/fohh_http_private.h"

#define UNDEFINED_CAPABILITIES (uint64_t)0x0
#define FILE_UPLOAD            (uint64_t)0x00000001
#define FILE_DOWNLOAD          (uint64_t)0x00000002
#define INSPECT_FILE_UPLOAD    (uint64_t)0x00000004
#define INSPECT_FILE_DOWNLOAD  (uint64_t)0x00000008
#define RECORD_SESSION         (1 << 6)  /* 64 */
#define SHARE_SESSION          (uint64_t)0x00000080
#define SESSION_SHARE_MONITOR  (uint64_t)0x00000100
#define SESSION_SHARE_CONTROL  (uint64_t)0x00000200

/* File transfer action */
#define FILE_ACTION_DOWNLOAD    0
#define FILE_ACTION_UPLOAD      1

#define FILE_INSPECTED_FALSE    0
#define FILE_INSPECTED_TRUE     1

#define MAX_CRED_LEN 2*8192
#define EXPORTER_GUACD_IPADDR    "127.0.0.1"
#define EXPORTER_GUACD_PORT    4822

#define EXPORTER_GUACD_CHILDPROC_LISTENER_IP  "127.0.0.1"
#define EXPORTER_GDPROXY_LISTENER_MINPORT_NUMBER 33000
#define EXPORTER_GDPROXY_LISTENER_MAXPORT_NUMBER 65000

#define GUAC_PROCTORED_SESS_ID_LEN 64
#define PROXY_MTUNNEL_ID_BYTES_TEXT_MIN 32

#define UNDEFINED_PRIV_PORTAL_CAPABILITIES          (uint64_t)0x0
#define PRIV_PORTAL_DELETE_FILE                     (uint64_t)0x00000001
#define PRIV_PORTAL_ACCESS_UNINSPECTED_FILE         (uint64_t)0x00000002
#define PRIV_PORTAL_UPLOAD_INSPECTED_SANDBOX        (uint64_t)0x00000004
#define PRIV_PORTAL_UPLOAD_INSPECTED_DISCAN         (uint64_t)0x00000008
#define PRIV_PORTAL_REQUEST_APPROVALS               (uint64_t)0x00000010
#define PRIV_PORTAL_REVIEW_APPROVALS                (uint64_t)0x00000020
#define PRIV_PORTAL_PRIVILEGED_DESKTOPS             (uint64_t)0x00000040

extern struct zcdns *exporter_zcdns;

extern int is_pra_disabled(int64_t customer_gid);
extern int is_pra_guacd_service_disabled();

void set_guacd_service_stat_to_enabled(void);
void set_guacd_service_stat_to_disabled(void);

/*
 * Handle Guacamole API request
 */
int handle_guac_api_request(struct exporter_request *request, const char *auth_cookie, const char *assertion, int is_no_auth);

/*
 * Handle reprocess Guacamole API request
 */
int handle_reprocess_guac_api_request(struct exporter_request *request, const char *auth_cookie, const char *assertion, int is_no_auth, uint8_t *cookie);

/*
 * Initialize guacamole api global request processing state
 */
int exporter_guac_api_request_init(struct exporter *exporter);

int exporter_guac_request_log(struct exporter_request *request);
int exporter_guac_request_log_cleanup(struct exporter_request *request);
int guacd_subcomponent_active_session_count(void);
int get_large_portal_access_active_count();

/* API URI suffixes */
#define EXPORTER_GUAC_API_URI_WEBSOCKET_TUNNEL           "/zconsole/websocket-tunnel"

enum exporter_ux_display_quality {
    EXPORTER_UX_DISPLAY_QUALITY_NORMAL = 0,
    EXPORTER_UX_DISPLAY_QUALITY_HIGH,
};

enum exporter_ux_screen_resolution {
    EXPORTER_UX_RESOLUTION_DEFAULT = 0,                  /* fit to bounds */
    EXPORTER_UX_RESOLUTION_640x480,
    EXPORTER_UX_RESOLUTION_800x600,
    EXPORTER_UX_RESOLUTION_1040x668,
    EXPORTER_UX_RESOLUTION_1048x768,
    EXPORTER_UX_RESOLUTION_1280x1024,
    EXPORTER_UX_RESOLUTION_1400x1050,
    EXPORTER_UX_RESOLUTION_1600x900,
    EXPORTER_UX_RESOLUTION_1920x1080,

    EXPORTER_UX_RESOLUTION_MAX
};

enum guac_stats_counter {
    GUAC_TUNNEL_DISCONNECT_RECV = 0,
    GUAC_WS_REQUEST_DESTROY,
    GUAC_WS_PARSER_ERROR,
    GUAC_RESPONSE_ERROR,
    GUAC_FILE_TRANSFER_UPLOAD,
    GUAC_FILE_TRANSFER_DOWNLOAD,
    GUAC_FILE_TRANSFER_UPLOAD_FAILURE,
    GUAC_FILE_TRANSFER_DOWNLOAD_FAILURE,
    GUAC_FILE_TRANSFER_UPLOAD_INSPECT,
    GUAC_FILE_TRANSFER_UPLOAD_INSPECT_DENY,
    GUAC_FILE_TRANSFER_UPLOAD_INSPECT_FAIL,
    GUAC_FILE_TRANSFER_CRYPTO_SERVICE_FAIL,
    GUAC_FILE_TRANSFER_UPLOAD_POLICY_DENY,
    GUAC_FILE_TRANSFER_DOWNLOAD_POLICY_DENY,
    GUAC_CLIPBOARD_COPY,
    GUAC_CLIPBOARD_PASTE,
    GUAC_CLIPBOARD_COPY_EXCEED_LIMIT,
    GUAC_CLIPBOARD_PASTE_EXCEED_LIMIT,
    GUAC_CLIPBOARD_COPY_POLICY_DENY,
    GUAC_CLIPBOARD_PASTE_POLICY_DENY,
    GUACD_SERVICE_HANDSHAKE_FAILED,
    GUACD_SERVICE_GD_PROXY_LISTENER_CREATE_FAILED,
    GUACD_SERVICE_CONNECT_FAILED,
    GUACD_SERVICE_MT_CONSUME_ON_REQUEST_THREAD_FAILED,
    GUACD_SERVICE_CHILD_PROXY_CONNECTION_FAILED,
    GUACD_SERVICE_STICHING_CONNECTION_FAILED,
    GUACD_SERVICE_CHILD_PROXY_SERVER_CREATE_FAILED,
    GUACD_SERVICE_CHILD_PROXY_SERVER_LISTEN_FAILED,
    GUACD_SERVICE_CHILD_PROXY_SERVER_ACCEPT_FAILED,
    GUACD_SERVICE_INC_ACTIVE_SESSION_COUNT,
    GUACD_SERVICE_DEC_ACTIVE_SESSION_COUNT,
    GUACD_SERVICE_MTUNNEL_DROPS_CPU_LIMIT_REACHED,
    GUACD_SERVICE_MTUNNEL_DROPS_MEM_LIMIT_REACHED,
    GUACD_SERVICE_MTUNNEL_DROPS_MAX_SESSIONS_REACHED,
    GUACD_PARSER_ERROR_500,
    GUACD_SERVICE_RDP_SESSION_COUNT,
    GUACD_SERVICE_VNC_SESSION_COUNT,
    GUACD_SERVICE_REALVNC_SESSION_COUNT,
    GUACD_SERVICE_SSH_SESSION_COUNT,
    LARGE_PORTAL_ACCESS_ACTIVE_COUNT,
    LARGE_PORTAL_ACCESS_BLOCKED_COUNT,
    CREDENTIAL_POOL_CONN_COUNT,
    CREDENTIAL_POOL_CONN_FAILED,
    CREDENTIAL_POOL_DELETE_API_FAILED,
    CREDENTIAL_POOL_INTERNAL_CALLBACK_FAILURE,
    CREDENTIAL_POOL_CRED_ID_FOUND,
    CREDENTIAL_POOL_ERR_NO_CRED_AVAILABLE,
    CREDENTIAL_POOL_ERR_SERVICE_UNAVAILABLE,
    CREDENTIAL_POOL_ERR_CRED_NOT_FOUND_IN_DB,
    CREDENTIAL_POOL_REQUEST_STATUS_CHECK_FAILURE,
    CREDENTIAL_POOL_ERR_JSON_RESPONSE,
    CREDENTIAL_THREAD_CPS_CONN_FAILED,
    CREDENTIAL_THREAD_NULL_DATA,
    CREDENTIAL_THREAD_CPS_REQUEST_ERROR,
    CREDENTIAL_THREAD_CPS_REQUEST_RETRY_COUNT,
    CREDENTIAL_THREAD_CPS_TASK_ENQ_FAIL,
    CREDENTIAL_THREAD_CPS_REQUEST_MAXRETRY_FAIL,
    CREDENTIAL_THREAD_CPS_DEL_ALL_RETRY_COUNT,
    CREDENTIAL_THREAD_CPS_DEL_RETRY_COUNT,
    CREDENTIAL_THREAD_CPS_GET_RETRY_COUNT,
    PRA_HEALTH_CHECK_503_COUNT,
    DJB_LAUNCH_COUNT,
    DJB_VALIDATION_FAILURE_COUNT,
    DJB_DELETE_COUNT,
    DJB_CONNECT_COUNT,
    DJB_PORTAL_POLICY_FAIL_COUNT,
    DJB_SERVICE_RESP_FAIL_COUNT,
};

struct exporter_credentials {
    char      *user_name;
    char      *user_domain;
    char      *password;
    char      *private_key;
    char      *passphrase;
    char      *decrypted_password;
    char      *decrypted_private_key;
    char      *decrypted_passphrase;
    char      *cred_type;
    uint32_t  pkey_done;
    uint16_t  user_name_len;
    uint16_t  user_domain_len;
    uint16_t  password_len;
    uint16_t  private_key_len;
    uint16_t  passphrase_len;
    uint16_t  decrypted_password_len;
    uint16_t  decrypted_private_key_len;
    uint16_t  decrypted_passphrase_len;
};

/*
 * Portal API stats clear
 */
void exporter_guac_api_stats_clear();

/*
 * Portal API stats dump
 */
void exporter_guac_api_stats_dump(struct zpath_debug_state *request_state);
void exporter_guac_api_stats_increment(enum guac_stats_counter counter);
void exporter_guac_api_stats_decrement(enum guac_stats_counter counter);
void exporter_guac_api_proto_stats_decrement(struct exporter_request* request);

int exporter_guac_connect(struct exporter_request *request, int r_cnt);
int exporter_conn_guac_args_cb(struct exporter_request *request, const char *args);
int exporter_conn_guac_ready_cb(struct exporter_request *request);
int exporter_guac_api_respond_to_write(struct exporter_request *request);
int exporter_request_check_redirect_for_ot(struct exporter_request *request, const char *target_domain);
int sra_update_application_domain(struct exporter_request *request);
void exporter_request_free_guac_info(struct exporter_request *request);
int exporter_request_send_approval_timeout_message(struct exporter_request *request);
int exporter_guac_send_response_internal(struct exporter_request *request, enum http_status status, int clear_response_data,char *format_str, ...);
void exporter_request_free_file_log(struct exporter_request *request);
int exporter_get_zconsole_connect_id_len(struct exporter_request *request);
int64_t get_console_id_from_first_zconsole_request(struct exporter_request *request);
int64_t get_scope_id_from_zscope_request(struct exporter_request *request);
void exporter_populate_log_conn_id(struct exporter_request *request, const char *conn_id);
#define exporter_guac_send_response(request, status, format_str, ...) \
    exporter_guac_send_response_internal(request,status, 1, format_str, ##__VA_ARGS__)

/* SFTP mtunnel setup */
int exporter_guac_setup_mtunnel(struct exporter_request *request);
int is_guac_sftp_mt_alive(struct exporter_request *request);

void  exporter_guac_disconnect(struct exporter_request *request);
int64_t get_console_id_from_zconsole_param(struct exporter_request *request);

/*
 * Hash of Guacamole tunnels.
 * Maps Guacamole tunnel ids to instances of durable Guacamole exporter_request handling communication
 * with guacd connection id.
 */
extern struct zhash_table *guac_tunnels;

struct file_transfer_info {
    int       upload_file_size;
};

struct file_scan_info {
    uint32_t  is_zia_scan_req:1;
    FILE      *upload_file;
    int       upload_file_fd;
    char      *zia_cloud_domain;
    char      *zia_username;
    char      *zia_password;
    char      *zia_cloud_service_api_key;
    char      *zia_sandbox_api_token;
    char      *zia_auth_sess_id;
    char      *file_md5;
};

struct file_log {
    uint32_t  file_action:1;
    uint32_t  file_inspected:1;
    char      *file_name;
    char      *file_type;
    char      *file_md5;
    char      *status;
    char      *inspection_verdict;
    uint64_t  start_ts;
    uint64_t  end_ts;
    uint64_t  inspection_start_ts;
    uint64_t  inspection_end_ts;
    int64_t   privileged_file_id;
};

struct guacd_proxy_childproc {
    struct event_base *child_proc_base;
    struct bufferevent *child_proc_bev;
    // get exporter request based on connection
    struct exporter_request *request;
};

struct guacd_server_proxy {
    struct fohh_generic_server *guacd_child_proxy_server;
    struct evbuffer  *gd_proxy_request;
    struct evbuffer  *gd_proxy_response;
    struct bufferevent *gd_proxy_bev;
    struct guacd_proxy_childproc *guacd_childproc_data;

    /* For SFTP connections */
    struct guacd_proxy_childproc *guacd_childproc_sftp;
    struct exporter_request *sftp_exporter_request;
    int32_t sftp_conn_id;
    uint16_t sftp_port;
    void *cookie;
  // char *sftp_listener_addr;
  // int sftp_listener_port;

    int connection_id;
    // child proc listener info
    char *guacd_childproc_listener_addr;
    int guacd_childproc_listener_port;
    int gd_proxy_thread_id;
    int protocol_server_thread_id;
};

struct guacd_remote_server_proxy {
    struct fohh_generic_server *guacd_remote_proxy_server;

    char *guacd_remote_server_proxy_listener_addr;
    int guacd_remote_server_proxy_listener_port;
    int gd_remote_server_proxy_thread_id;

    int remote_console_id;

    struct bufferevent *local_guacd_bev; /* Conn to guacd when proxy and original exporter are the same */
    struct bufferevent *remote_proxy_bev; /* Conn to guacd server proxy */
    int remote_proxy_sock;
};

enum exporter_guac_json_node {
    exporter_guac_json_node_shared_user = 0,

    exporter_guac_json_node_max,
};

struct exporter_guac_json_node_list_t {
    enum exporter_guac_json_node id;
    const char *json_node_name;
};

struct zpn_exporter_mtunnel_elem {
    ZLIST_ENTRY(zpn_exporter_mtunnel_elem) list;
    void *mtunnel;
};
ZLIST_HEAD(exporter_guac_proxy_mtunnels, zpn_exporter_mtunnel_elem);

struct zpn_exporter_bev_elem {
    ZLIST_ENTRY(zpn_exporter_bev_elem) list;
    void **bev;
    struct exporter_request *proxy_request;
};
ZLIST_HEAD(exporter_guac_proxy_bevs, zpn_exporter_bev_elem);

struct exporter_mtunnel_diag_info {
    uint32_t  is_diag_info_consumed:1;
    uint32_t  console_cred_type;
    int64_t   capabilities_policy_id;
    int64_t   cred_policy_id;
    char      *console_user;
    char      *console_conn_type;
    char      *credential_id;
};

enum keyboard_layout {
    pt_br_qwerty = 0,
    en_us_qwerty,
    en_gb_qwerty,
    fr_fr_azerty,
    fr_be_azerty,
    fr_ch_qwertz,
    de_de_qwertz,
    de_ch_qwertz,
    hu_hu_qwertz,
    it_it_qwerty,
    ja_jp_qwerty,
    no_no_qwerty,
    es_es_qwerty,
    es_latam_qwerty,
    sv_se_qwerty,
    tr_tr_qwerty,
    end_of_layouts
};

#define exporter_guac_stream_max_op         2
enum exporter_guac_stream_index {
    file_transfer = 0,
    clipboard,
};

enum exporter_guac_stream_type {
    exporter_guac_invalid = -1,
    exporter_guac_file_upload,
    exporter_guac_file_download,
    exporter_guac_dir_download,
    exporter_guac_clipboard_copy,
    exporter_guac_clipboard_paste,
    exporter_guac_advanced_file_upload
};

struct exporter_stream_op {
    int     stream_id;
    int     is_stream_active;
    enum exporter_guac_stream_type stream_type;
    int     stream_transmit;
    int     stream_receive;
    struct evbuffer  *stream_request;
    struct evbuffer  *stream_response;
};

enum exporter_guac_cred_api {
    credential_invalid_api = -1,
    credential_get_api,
    credential_del_api,
    credential_del_all_api
};

struct cred_ctxt {
    enum fohh_http_client_status cred_pool_service_connect_status;
    struct fohh_http_client *cred_pool_service_client;
    char      *cred_pool_service_domain;
    int64_t   cred_map_rule_id;
    char      *cred_policy_name;
    int64_t   credential_id;
    int64_t   credential_state_id;
    int64_t   pool_id;
    char      *console_user;
    int64_t   exporter_id;
    int64_t   console_id;
    int64_t   customer_gid;
    int64_t   scope_gid;
    void      *cookie;
    enum exporter_guac_cred_api cred_api;
    int       fohh_thread_id;
};

struct djb_data {
    char *ipaddr;
    char *username;
    char *pwd;
    char *admin_username;
    char *admin_pwd;
    char *os_type;
    int   port;
    char *user_email;
    int64_t djb_id;
    int64_t rule_id;
    size_t  bytes_len_from_server;
    int djb_server_request_complete;
    int djb_console_details_complete;
    int djb_api_type_reset_count;
    struct evbuffer *djb_response_data;
};

struct exporter_guac_request_info {
    uint32_t  guacd_service:1;
    uint32_t  is_header:1;
    uint32_t  is_websocket:1;
    uint32_t  is_guacd_ready:1;
    /* To indicate whether PRA Interactive Auth is disabled for this request */
    unsigned is_pra_interactive_auth_disabled:1;
    uint32_t  is_sftp_disabled:1;
    uint32_t  is_ft_async_active:1;
    uint32_t  is_cancel_scan_active:1;
    uint32_t  is_stream_timeout_triggered:1;
    uint32_t  is_sftp_connection:1;
    uint32_t  is_headless:1;
    uint32_t  is_session_share_enabled:1;
    uint32_t  is_session_share_monitor:1;
    uint32_t  is_session_share_control:1;
    uint32_t  session_sharing_check_done:1;
    uint16_t  is_assertion_freed:1;
    int       rdp_dir_list_cnt;
    uint32_t  clipboard_size;
    char      *policy_name;
    int64_t   session_start_time;
    uint32_t  ref_count;
    uint32_t  viewport_width;
    uint32_t  viewport_height;
    uint32_t  viewport_dpi;
    uint32_t  ux_quality;
    uint32_t  ux_resolution;
    enum keyboard_layout  server_layout;
    int64_t   cred_map_rule_id;
    int64_t   credential_id;
    char      *remote_ip;
    int       (*async_cb)(struct exporter_request *request);
    char      *creds;
    struct evbuffer  *websocket_input;
    struct evbuffer  *stream_response;
    struct evbuffer  *decrypted_buffer;
    int64_t   capabilities_policy_id;
    uint64_t  capabilities_policy_bitmap;
    uint64_t  created_ts;

    /* Guacomole Parser - parse guacomole instructions from guacd */
    guac_proto_parser guac_parser;

    /* Stream Guacamole Parser - parse guacamole instructions from stream request */
    guac_proto_parser stream_parser;

    struct    event *zia_scan_timeout;

    struct event *stream_create_timeout;

    /* Struct holding file transfer scan references */
    struct    file_scan_info file_scan;

    struct    file_log       file_log;

    struct    file_transfer_info  file_info;

    struct  exporter_credentials exporter_credentials;

    struct    guacd_server_proxy gd_proxy;
    struct    guacd_remote_server_proxy gd_remote_server_proxy;
    struct    session_recording_context recording_ctxt;
    char      *shared_user_list;

    /* The list of proctoring mtunnels associated with this request */
    struct exporter_guac_proxy_mtunnels mtunnels_list;
    struct exporter_guac_proxy_bevs bevs_list;

    struct    exporter_mtunnel_diag_info diag_info;
    struct    exporter_stream_op guac_in_stream[exporter_guac_stream_max_op];
    struct    exporter_stream_op guac_out_stream[exporter_guac_stream_max_op];
    zpath_mutex_t shared_user_list_lock;
    struct cred_ctxt *credential_ctxt;
};

/* Upload status code */
#define UPLOAD_STATUS_SCAN_FAIL             6
#define UPLOAD_STATUS_INTERNAL_ERROR        9

int get_app_server(struct exporter_request *request, int64_t app_id);
enum zpn_console_credential_type get_cred_type(char *cred_type) ;
void exporter_cleanup_credentials(struct exporter_request *request);
void exporter_increment_credentials_policy_reject_stats();
int64_t
exporter_request_policy_state_check_approval(struct exporter_request *request, char *approval_info, size_t approval_info_len);
void exporter_free_mtunnels_list(struct exporter_request *request);
void exporter_free_bevs_list(struct exporter_request *request);
void exporter_free_bevs_list_by_user(struct exporter_request *request, char *user);
int exporter_session_share_check_user(struct exporter_request *request);
int remove_bevs_for_proxy_request(struct exporter_request *request);
int exporter_session_share_user_join_success(struct exporter_proctoring_session_event_info *info);
int exporter_is_pra_shared_session_request(struct exporter_request *request);
int exporter_extract_shared_session_id_from_all_request_type(struct exporter_request *, char *, size_t);
int extract_session_string_from_request(struct exporter_request *request, char *session_string, int offset);
void exporter_diagostic_info_cleanup(struct exporter_request *request);
void decrement_large_portal_access_count(struct exporter_request *request);
int get_guac_connect_instruction(struct exporter_request *request, const char *args, char *connect_instr, size_t length);
#endif /* __EXPORTER_GUAC_API_H__ */
