/*
 * exporter_guac_desktops_test.c. Copyright (C) 2025 Zscaler Inc. All Rights Reserved
 *
 * Run the following binary
 * [jenkins@localhost itasca]$ ./out/x64-linux-debug/src/exporter/exporter_guac_desktops_test
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>

#include "zpath_misc/zpath_misc.h"
#include "zpath_lib/zpath_instance.h"
#include "zpath_lib/zpath_local.h"
#include "zpath_lib/zpath_debug.h"
#include "fohh/http_parser.h"
#include "fohh/fohh_private.h"
#include "fohh/fohh_http.h"
#include "argo/argo_log.h"
#include "exporter/exporter.h"
#include "exporter/exporter_private.h"
#include "exporter/exporter_request.h"
#include "exporter/exporter_domain.h"
#include "exporter/exporter_request_policy_state.h"
#include "exporter/zpn_sra_portal.h"
#include "zpn/zpn_rule.h"
#include "zpn/zpn_scope.h"
#include "zpn/zpn_scope_ready.h"
#include "fohh/fohh_http_private.h"
#include "parson/parson.h"

#include "exporter/exporter_user_portal_api.h"
#include "exporter/exporter_user_portal_conn_webserver.h"
#include "exporter/exporter_user_portal_request_state.h"
#include "exporter/exporter_user_portal_request.h"
#include "exporter/exporter_guac_api.h"
#include "exporter/exporter_guac_util.h"
#include "exporter/exporter_guac_proxy.h"
#include "exporter/exporter_guac_desktops.h"
#include "exporter/exporter_guac_desktops_test.h"

struct exporter global_exporter;
const char *exporter_auth_domain;
const char *exporter_saml_auth_domain;
const char *exporter_broker_domain;
const char *exporter_uportal_ba_app_suffix = NULL;
const char *exporter_uportal_host_suffix = NULL;
const char *exporter_uportal_pra_host_suffix = NULL;
char *exporter_cookie_domain = NULL;
char *exporter_cookie_crypto = NULL;

const char *exporter_exporter_domain;

struct zpath_instance zpath;

#define CAPABILITIES_BITMAP 77
#define PORTAL_POLICY_RULE_ID 145255670470411223
#define PORTAL_SNI "djb.com"
#define TEST_USEREMAIL  "<EMAIL>"
#define PORTAL_DOMAIN "djb.com"
#define AMI_URI "/v1/dispjumpbox/customers/145255670470410241/ami?userEmail=<EMAIL>"
#define APPS_URI "/v1/dispjumpbox/customers/145255670470410241/apps?userEmail=<EMAIL>"
#define DESKTOPS_URI "/v1/dispjumpbox/customers/145255670470410241/desktops?userEmail=<EMAIL>"
#define VM_URI "/v1/dispjumpbox/customers/145255670470410241/vm?userEmail=<EMAIL>"
#define ACTIVE_URI "/v1/dispjumpbox/customers/145255670470410241/active?userEmail=<EMAIL>"
#define WSS_URI "/v1/zconsole/websocket-tunnel?zconsole=145255670470411243&GUAC_WIDTH=788&GUAC_HEIGHT=879&GUAC_DPI=96&keyboardLayout=en-us-qwerty&dr=0&dq=0&djb=1"
#define WRONG_DJB_RESPONSE_DATA "{\"user_email\":\"<EMAIL>\",\"id\":\"1748391047766274\",\"ip_address\":\"***********\",\"admin_userid\":\"djbadmin\",\"admin_userpwd\":\"L0nTyqQE28d^t_1Ro^7w\",\"os_type\":\"Windows\",\"port\":3389}"
#define DJB_RESPONSE_DATA "{\"user_email\":\"<EMAIL>\",\"id\":\"145255670470411243\",\"ip_address\":\"***********\",\"admin_userid\":\"djbadmin\",\"admin_userpwd\":\"L0nTyqQE28d^t_1Ro^7w\",\"os_type\":\"Windows\",\"port\":3389}"
#define WRONG_CUSTOMER_GID 145255670470410111
#define CORRECT_CUSTOMER_GID 145255670470410241
#define CONSOLE_ID 145255670470411243

void exporter_request_reset(struct exporter_request *request);

struct exporter_user_portal_request_info {
    uint32_t                                    is_valid:1;
    uint32_t                                    is_reserved:31;
    enum  zpa_user_portal_api_type              api_type;
    enum  zpa_user_portal_api_version           api_version;
    char*                                       name_id;
    struct conn_webserver*                      conn;
    int                                         total_async_count;
    char*                                       assertion_key;
    char*                                       assertion;
};

int mock_exporter_user_portal_conn_webserver(struct exporter_request *request)
{
    return 0;
}

int mock_is_pra_disabled(int64_t customer_gid) {
    printf("PRA is enabled \n");
    return 0;
}

int mock_is_pra_desktops_disabled(int64_t customer_gid) {
    printf("PRA Privileged Desktops enabled! \n");
    return 0;
}
int mock_exporter_privileged_portal_policy_evaluate(struct exporter_request *request,
        int64_t *portal_policy_id, uint64_t *portal_policy_capabilities_bitmap) {
    return 0;
}

void test_api_ami(struct exporter_request *req)
{
    printf(" Test DJB API ami call ");
    req->name = "test_api_ami";
    req->http_upgrade = 1;
    char uri[1024] = {'\0'};
    snprintf(uri, 1024, AMI_URI);
    printf("URI: %s \n",uri);
    req->url = uri;
    req->url_len = strlen(AMI_URI);
    req->url_ofs = 0;
    req->req_method = HTTP_POST;
    req->is_authenticated = 1;

    http_parser_url_init(&(req->url_parser));
    (void )http_parser_parse_url(req->url, req->url_len, 0, &(req->url_parser));
    exporter_request_reset(req);

    int result = ZPATH_RESULT_NO_ERROR;

    result = exporter_sra_portal_handle_privileged_desktops_api(req);
    assert(result == ZPATH_RESULT_NO_ERROR);
    assert(req->portal_info->api_type == ZPA_SRA_PORTAL_API_TYPE_PRIVILEGED_DESKTOPS_AMI_LIST);
    printf("PASS API AMI - REQ METHOD AND API TYPE VALIDATION\n\n");

    req->req_method = HTTP_DELETE;
    result = exporter_sra_portal_handle_privileged_desktops_api(req);
    assert(result == ZPATH_RESULT_ERR);
    printf("PASS API AMI - INCORRECT REQ METHOD VALIDATION\n\n");

    req->req_method = HTTP_POST;
    req->portal_info->name_id = "<EMAIL>";
    result = exporter_sra_portal_handle_privileged_desktops_api(req);
    assert(result == ZPATH_RESULT_ERR);
    printf("PASS API AMI - VALIDATION FOR INCORRECT USER IN THE URI\n\n");
}

void test_api_apps(struct exporter_request *req)
{
    printf(" Test DJB API apps call ");
    req->name = "test_api_apps";
    req->http_upgrade = 1;
    char uri[1024] = {'\0'};
    snprintf(uri, 1024, APPS_URI);
    printf("URI: %s \n",uri);
    req->url = uri;
    req->url_len = strlen(APPS_URI);
    req->url_ofs = 0;
    req->req_method = HTTP_POST;
    req->is_authenticated = 1;

    http_parser_url_init(&(req->url_parser));
    (void )http_parser_parse_url(req->url, req->url_len, 0, &(req->url_parser));
    exporter_request_reset(req);

    int result = ZPATH_RESULT_NO_ERROR;

    result = exporter_sra_portal_handle_privileged_desktops_api(req);
    assert(result == ZPATH_RESULT_NO_ERROR);
    assert(req->portal_info->api_type == ZPA_SRA_PORTAL_API_TYPE_PRIVILEGED_DESKTOPS_AMI_LIST);
    printf("PASS API APPS - REQ METHOD AND API TYPE VALIDATION\n\n");

    req->req_method = HTTP_DELETE;
    result = exporter_sra_portal_handle_privileged_desktops_api(req);
    assert(result == ZPATH_RESULT_ERR);
    printf("PASS API APPS - INCORRECT REQ METHOD VALIDATION\n\n");

    req->req_method = HTTP_POST;
    req->portal_info->name_id = "<EMAIL>";
    result = exporter_sra_portal_handle_privileged_desktops_api(req);
    assert(result == ZPATH_RESULT_ERR);
    printf("PASS API APPS - VALIDATION FOR INCORRECT USER IN THE URI\n\n");
}

void test_api_desktops(struct exporter_request *req)
{
    printf(" Test DJB API desktops call ");
    req->name = "test_api_desktops";
    req->http_upgrade = 1;
    char uri[1024] = {'\0'};
    snprintf(uri, 1024, DESKTOPS_URI);
    printf("URI: %s \n",uri);
    req->url = uri;
    req->url_len = strlen(DESKTOPS_URI);
    req->url_ofs = 0;
    req->req_method = HTTP_POST;
    req->is_authenticated = 1;

    http_parser_url_init(&(req->url_parser));
    (void )http_parser_parse_url(req->url, req->url_len, 0, &(req->url_parser));
    exporter_request_reset(req);

    int result = ZPATH_RESULT_NO_ERROR;

    result = exporter_sra_portal_handle_privileged_desktops_api(req);
    assert(result == ZPATH_RESULT_NO_ERROR);
    assert(req->portal_info->api_type == ZPA_SRA_PORTAL_API_TYPE_PRIVILEGED_DESKTOPS_AMI_LIST);
    printf("PASS API DESKTOPS - REQ METHOD AND API TYPE VALIDATION\n\n");

    req->req_method = HTTP_DELETE;
    result = exporter_sra_portal_handle_privileged_desktops_api(req);
    assert(result == ZPATH_RESULT_ERR);
    printf("PASS API DESKTOPS - INCORRECT REQ METHOD VALIDATION\n\n");

    req->req_method = HTTP_POST;
    req->portal_info->name_id = "<EMAIL>";
    result = exporter_sra_portal_handle_privileged_desktops_api(req);
    assert(result == ZPATH_RESULT_ERR);
    printf("PASS API DESKTOPS - VALIDATION FOR INCORRECT USER IN THE URI\n\n");
}

void test_api_vm(struct exporter_request *req)
{
    printf(" Test DJB API vm call for create/delete DJBs ");
    req->name = "test_api_vm";
    req->http_upgrade = 1;
    char uri[1024] = {'\0'};
    snprintf(uri, 1024, VM_URI);
    printf("URI: %s \n",uri);
    req->url = uri;
    req->url_len = strlen(VM_URI);
    req->url_ofs = 0;
    req->req_method = HTTP_POST;

    req->is_authenticated = 1;

    http_parser_url_init(&(req->url_parser));
    (void )http_parser_parse_url(req->url, req->url_len, 0, &(req->url_parser));
    exporter_request_reset(req);

    int result = exporter_sra_portal_handle_privileged_desktops_api(req);
    assert(result == ZPATH_RESULT_NO_ERROR);
    assert(req->portal_info->api_type == ZPA_SRA_PORTAL_API_TYPE_PRIVILEGED_DESKTOPS_VM);
    printf("PASS API VM CREATE - REQ METHOD AND API TYPE VALIDATION\n\n");

    req->req_method = HTTP_DELETE;
    result = exporter_sra_portal_handle_privileged_desktops_api(req);
    assert(result == ZPATH_RESULT_NO_ERROR);
    assert(req->portal_info->api_type == ZPA_SRA_PORTAL_API_TYPE_PRIVILEGED_DESKTOPS_VM);
    printf("PASS API VM DELETE - REQ METHOD AND API TYPE VALIDATION\n\n");

    req->req_method = HTTP_GET;
    result = exporter_sra_portal_handle_privileged_desktops_api(req);
    assert(result == ZPATH_RESULT_ERR);
    printf("PASS API VM - INCORRECT REQ METHOD VALIDATION\n\n");

    req->req_method = HTTP_POST;
    req->portal_info->name_id = "<EMAIL>";
    result = exporter_sra_portal_handle_privileged_desktops_api(req);
    assert(result == ZPATH_RESULT_ERR);
    printf("PASS API VM - VALIDATION FOR INCORRECT USER IN THE URI\n\n");

    req->req_method = HTTP_POST;
    req->portal_info->name_id = TEST_USEREMAIL;
    req->conn->exporter_domain->customer_gid = WRONG_CUSTOMER_GID;
    result = exporter_sra_portal_handle_privileged_desktops_api(req);
    assert(result == ZPATH_RESULT_ERR);
    printf("PASS API VM - VALIDATION FOR INCORRECT CUSTOMER_GID IN THE URI\n\n");
}

void test_api_active(struct exporter_request *req)
{
    printf(" Test DJB API active - Fetch active instance DJBs ");
    req->name = "test_api_active";
    req->http_upgrade = 1;
    char uri[1024] = {'\0'};
    snprintf(uri, 1024, ACTIVE_URI);
    printf("URI: %s \n",uri);
    req->url = uri;
    req->url_len = strlen(ACTIVE_URI);
    req->url_ofs = 0;
    req->req_method = HTTP_GET;

    req->is_authenticated = 1;

    http_parser_url_init(&(req->url_parser));
    (void )http_parser_parse_url(req->url, req->url_len, 0, &(req->url_parser));
    exporter_request_reset(req);

    int result = exporter_sra_portal_handle_privileged_desktops_api(req);
    assert(result == ZPATH_RESULT_NO_ERROR);
    assert(req->portal_info->api_type == ZPA_SRA_PORTAL_API_TYPE_PRIVILEGED_DESKTOPS_ACTIVE_LIST);
    printf("PASS API ACTIVE LIST - REQ METHOD AND API TYPE VALIDATION\n\n");

    req->req_method = HTTP_DELETE;
    result = exporter_sra_portal_handle_privileged_desktops_api(req);
    assert(result == ZPATH_RESULT_ERR);
    printf("PASS API ACTIVE LIST - INCORRECT REQ METHOD VALIDATION\n\n");
}

void test_api_wss(struct exporter_request *req)
{
    printf(" Test DJB API wss call ");
    req->name = "test_api_wss";
    req->http_upgrade = 1;
    char uri[1024] = {'\0'};
    snprintf(uri, 1024, WSS_URI);
    printf("URI: %s \n",uri);
    req->url = uri;
    req->url_len = strlen(WSS_URI);
    req->url_ofs = 0;
    req->req_method = HTTP_POST;
    req->is_authenticated = 1;

    http_parser_url_init(&(req->url_parser));
    (void )http_parser_parse_url(req->url, req->url_len, 0, &(req->url_parser));
    exporter_request_reset(req);

    int result = ZPATH_RESULT_NO_ERROR;
    req->is_djb = 1;
    result = zpn_sra_djb_get_info(req, CONSOLE_ID);

    assert(result == ZPATH_RESULT_ASYNCHRONOUS);
    assert(req->is_djb);
    assert(req->djb_info);
    assert(req->portal_info->api_type == ZPA_SRA_PORTAL_API_TYPE_PRIVILEGED_DESKTOPS_DJBINFO);
    printf("PASS API WSS - Websocket-tunnel call with DJBINFO API TYPE VALIDATION\n\n");

    req->djb_info->djb_server_request_complete = 1;
    result = zpn_sra_djb_get_info(req, CONSOLE_ID);
    assert(result == ZPATH_RESULT_ERR);
    printf("PASS API WSS - Websocket-tunnel call with missing response from DJBINFO API CALL to Priv Desktop Service \n\n");

    req->djb_info->djb_response_data = evbuffer_new();
    evbuffer_add_printf(req->djb_info->djb_response_data, "%s", WRONG_DJB_RESPONSE_DATA);
    result = zpn_sra_djb_get_info(req, CONSOLE_ID);
    assert(result == ZPATH_RESULT_ERR);
    evbuffer_free(req->djb_info->djb_response_data);
    printf("PASS API WSS - Websocket-tunnel call with incorrect DJB ID response from DJBINFO API CALL to Priv Desktop Service \n\n");


    req->djb_info->djb_response_data = evbuffer_new();
    evbuffer_add_printf(req->djb_info->djb_response_data, "%s", DJB_RESPONSE_DATA);
    result = zpn_sra_djb_get_info(req, CONSOLE_ID);
    assert(result == ZPATH_RESULT_NO_ERROR);
    printf("PASS API WSS - Websocket-tunnel call with complete DJB response from DJBINFO API CALL to Priv Desktop Service \n\n");

    exporter_request_free_djb_info(req);
}

void exporter_request_reset(struct exporter_request *req)
{
    req->conn->exporter_domain->customer_gid = CORRECT_CUSTOMER_GID;
    req->portal_policy_rule_id = PORTAL_POLICY_RULE_ID;
    req->portal_policy_capabilities_bitmap = CAPABILITIES_BITMAP;
    req->portal_info->name_id = TEST_USEREMAIL;
    req->portal_info->is_valid = 1;
}

int main()
{
    zpath_instance_global_state.current_config = &zpath;
    zpath_instance_global_state.current_config->gid=10000;

    struct exporter_request *request = NULL;
    request = (struct exporter_request *)EXPORTER_CALLOC(sizeof(struct exporter_request));
    const char *name ="test request";
    request->name = EXPORTER_STRDUP(name, strlen(name));

    struct exporter_guac_request_info *guac_info = EXPORTER_CALLOC(sizeof(struct exporter_guac_request_info));
    request->guac_info = guac_info;

    struct exporter_conn *conn = (struct exporter_conn *)EXPORTER_CALLOC(sizeof(struct exporter_conn));
    conn->total_requests = 1;
    conn->name = EXPORTER_STRDUP(name, strlen(name));
    conn->current_request = request;
    conn->sni = PORTAL_SNI;
    request->conn = conn;
    conn->thread = (struct exporter_thread *)EXPORTER_CALLOC(sizeof(struct exporter_thread));
    ZTAILQ_INIT(&(conn->requests));
    ZTAILQ_INSERT_TAIL(&(conn->requests), request, list);
    exporter_request_free_q_init();
    g_exporter_ot_mode = 1;
    request->log.nameid = "<EMAIL>";

    struct exporter_domain *exp_dom = (struct exporter_domain *)EXPORTER_CALLOC(sizeof(struct exporter_domain));
    request->conn->exporter_domain = exp_dom;
    request->conn->exporter_domain->domain = PORTAL_DOMAIN;
    exporter_zcdns = NULL;
    exporter_debug = 0xffffffff;
    request->conn->exporter_domain->is_ot = 1;
    request->portal_info = (struct exporter_user_portal_request_info *) EXPORTER_CALLOC(sizeof(struct exporter_user_portal_request_info));
    request->user_portal_request_user_state = (struct exporter_user_portal_request_user_state *)EXPORTER_CALLOC(sizeof(struct exporter_user_portal_request_user_state));
    request->policy_state = (struct exporter_request_policy_state *) EXPORTER_CALLOC(sizeof(struct exporter_request_policy_state));
    request->policy_state->user_email = TEST_USEREMAIL;
    request->response_data = evbuffer_new();
    request->request_body = evbuffer_new();
    request->is_authenticated = 1;
    argo_log_use_printf(1);

    test_api_ami(request);
    test_api_apps(request);
    test_api_desktops(request);
    test_api_vm(request);
    test_api_active(request);
    test_api_wss(request);

    EXPORTER_FREE(conn->thread);
    EXPORTER_FREE(conn->name);
    EXPORTER_FREE(conn);
    EXPORTER_FREE(exp_dom);
    EXPORTER_FREE(request->guac_info);
    EXPORTER_FREE(request->policy_state);
    EXPORTER_FREE(request->portal_info);
    EXPORTER_FREE(request->user_portal_request_user_state);
    if (request->response_data ) evbuffer_free(request->response_data);
    if (request->request_body ) evbuffer_free(request->request_body);
    // request->name free'd by request free
    EXPORTER_FREE(request);
    return 0;
}
