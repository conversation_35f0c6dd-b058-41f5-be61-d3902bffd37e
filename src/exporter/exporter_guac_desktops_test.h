#ifndef EXPORTER_GUAC_DESKTOPS_TEST_H
#define EXPORTER_GUAC_DESKTOPS_TEST_H

#define is_pra_disabled(x) mock_is_pra_disabled(x)
#define is_pra_desktops_disabled(x) mock_is_pra_desktops_disabled(x)
#define exporter_user_portal_conn_webserver(req) mock_exporter_user_portal_conn_webserver(req)
#define exporter_user_portal_conn_webserver_message_complete_cb(x)
#define exporter_portal_api_error_response(x,...)
#define exporter_portal_defer_api_request_async(x)
#define exporter_guac_api_stats_increment(x)
#define exporter_request_policy_state_check_access_for_djb(x) 0
#define exporter_privileged_portal_policy_evaluate(x,y,z) mock_exporter_privileged_portal_policy_evaluate(x,y,z)
int mock_is_pra_disabled(int64_t customer_gid);
int mock_is_pra_desktops_disabled(int64_t customer_gid);
int mock_exporter_privileged_portal_policy_evaluate(struct exporter_request *request,
int64_t *portal_policy_id, uint64_t *portal_policy_capabilities_bitmap);
int mock_exporter_user_portal_conn_webserver(struct exporter_request *request);
#endif
