/*
 * exporter_user_portal_conn_webserver.c. Copyright (C) 2018 Zscaler Inc. All Rights Reserved
 *
 * Deals with talking to webserver (or CDN) and fetching the user portal base page on behalf of the client. So this is
 * like a web proxy - take the client request, send it to server, take the server reponse and send it to client. Since
 * we are acting like a webproxy, we should insert "Via" HTTP header.
 *
 *
 * Point of entry into this component/file:
 * 1. During the INIT, exporter_user_portal_conn_webserver_init() is called.
 * 2. When exporter found a user portal request to be served after auth, exporter_user_portal_conn_webserver() is called
 * 3. When DNS resolution is done - exporter_user_portal_conn_webserver_dns_cb()
 * 4. When web page is fetched - exporter_user_portal_conn_webserver_read_cb()
 *
 *
 * We had a choice of caching the user portal page, but didn't take that route for these reasons,
 * 1. CDN is anyway there for caching the page, no need add another cache layer and add to complexity.
 * 2. It is not going to save much bandwidth cost (read CDN bill)
 * Having said that, I am still thinking of this scenario,
 * 1. Think about Infosys,India which have 300k people logging in at 9:00am every morning and exporter getting
 * the portal request from all these people in 15mins window. Wouldn't it make sense to cache the page (from end-to-end
 * delivery speed) point of view?
 *
 * @startuml
 * autonumber
 * title User Poral Fetching Base Page
 * actor browser #violet
 * participant request_object#indigo
 * participant conn_webserver #blue
 * participant exporter_zcdns #green
 * participant zcdns #yellow
 * participant webserver #red
 *
 * browser->request_object: pls get me user portal base page
 * request_object->conn_webserver: exporter_user_portal_conn_webserver
 * conn_webserver->exporter_zcdns: zcdns_resolve
 * exporter_zcdns->zcdns: zcdns_resolve
 * zcdns->exporter_zcdns: exporter_user_portal_conn_webserver_dns_cb
 * exporter_zcdns->conn_webserver: exporter_user_portal_conn_webserver_dns_cb
 * conn_webserver->webserver: exporter_user_portal_conn_webserver_connect
 * conn_webserver<-webserver: exporter_user_portal_conn_webserver_status_cb/i am connected now
 * conn_webserver->webserver: exporter_user_portal_conn_webserver_request/pls fetch page
 * alt repeat
 *  conn_webserver<-webserver: exporter_user_portal_conn_webserver_read_cb/here is the page
 *  conn_webserver->request_object: copy to request->response_data/pls send these data to #browser
 *  request_object->browser: part of http data
 *  request_object->conn_webserver: wakeup if already blocked/i am ready to process more data
 * end
 * request_object->request_object: end of http data detected
 * @enduml
 *
 * Thread usage:
 * 1. exporter's connection infrastructure happens in fohh_thread_y context (i.e read data from browser - the socket
 * operation, creating the request object..)
 * 2. connection->request happens in fohh_thread_y context (i.e conn->thread).
 * 3. THIS module gets called in the context of fohh_thread_y and so all the operations happens in its context. Only
 * exception is when zcdns don't have an entry in its cache - at that time zcdns callback comes in at the
 * context of exporter_zcdns_thread context.
 * 4. connection->response happens in another thread context. So remember to switch thread when dealing with
 * response - be it calling into response_object or processing the callback from response_object. Or am I wrong?
 */
#define _GNU_SOURCE

#include <sys/types.h>
#include <netinet/tcp.h>
#include "zpath_lib/zpath_local.h"
#include "exporter/exporter.h"
#include "exporter/exporter_request.h"
#include "exporter/exporter_conn.h"
#include "exporter/exporter_user_portal_request_state.h"
#include "exporter/exporter_user_portal_cfg_portal.h"
#include "zcdns/zcdns_libevent.h"
#include "zlibevent/zlibevent_bufferevent.h"
#include "exporter/exporter_zcdns.h"
#include "exporter/exporter_assert.h"
#include "zpn/zpn_jit_approval_policy.h"
#include "exporter/exporter_guac_util.h"
#include "exporter/exporter_guac_sess_sharing.h"
#include "exporter/exporter_user_portal_conn_webserver.h"
#include "exporter/exporter_privileged_file_system.h"
#include "zpath_lib/zpa_cloud_config.h"
#include "zpath_lib/zpath_cloud.h"
#include "exporter/exporter_guac_desktops.h"
#include "exporter/exporter_user_portal_api.h"
/*
 * It is normal for the state to transistion from BEING_RECEIVED_AND_PARSED to BEING_PARSED to
 * BEING_RECEIVED_AND_PARSED. BEING_RECEIVED state is there just to indicated that EOF is received on the event
 * callback channel from libevent. read callback could happen after event callback.
 */
enum conn_webserver_state {
    ZPA_USER_PORTAL_CONN_WEBSERVER_SETTING_UP,
    ZPA_USER_PORTAL_CONN_WEBSERVER_DNS_REQUESTED,
    ZPA_USER_PORTAL_CONN_WEBSERVER_DNS_DONE,
    ZPA_USER_PORTAL_CONN_WEBSERVER_DNS_DONE_ERR,
    ZPA_USER_PORTAL_CONN_WEBSERVER_CONNECTING,
    ZPA_USER_PORTAL_CONN_WEBSERVER_PAGE_REQUESTED,
    ZPA_USER_PORTAL_CONN_WEBSERVER_PAGE_BEING_RECEIVED_AND_PARSED,
    ZPA_USER_PORTAL_CONN_WEBSERVER_PAGE_BEING_PARSED,
    ZPA_USER_PORTAL_CONN_WEBSERVER_PAGE_DONE,
    ZPA_USER_PORTAL_CONN_WEBSERVER_PAGE_DONE_ERR,
};

#define DNS_INT_COOKIE_ASYNC 0
#define DNS_INT_COOKIE_SYNC  1
#define DEFAULT_PORTAL_PORT 443
/*
 * We consider a conn_webserver object as zombie if it receives a socket error (EOF only as of today) as event cb and
 * we think there can be data sitting in read pipe. Lets give 1s for the data to be drained before knocking out
 * the states. If we don't track the zombies, they can just live here as stale entries and bloat the memory and FD
 * that it consumes.
 */
#define ZOMBIE_CLEANUP_WAIT_TIMER_US 1000000
/*
 * If for 5 seconds we don't hear anything from the server, we should TIMEOUT the connection.
 * Why 5 seconds?
 * a. Because 5 seconds is more than, more than enough for a websever/CDN to respond to
 * b. exporter's conn timneout is 20 seconds, we want to be less than that to make sure we cleanup before
 * exporter's comes up and yells at us.
 */
#define CONN_WEBSERVER_READ_TIMEOUT 5000000


/*
 * Each connection to the webserver will have an object of this structure. zombie_preventer will be activated only
 * when the code flow think that this object may become stale. If zombie_preventer is activated(by virtue of arming
 * the timer), then the timer expiry will clean this object.
 */
struct conn_webserver {
    char*                                   url;
    char*                                   url_version;
    struct zcdns_request*                   dns_request;
    int64_t                                 dns_request_start_time_us;
    enum conn_webserver_state               state;
    int64_t                                 diag_web_server_start_time_us;
    struct sockaddr_storage                 server_sa;
    int                                     server_sa_len;
    struct bufferevent*                     server_bev;
    struct {
        struct exporter_request*            request;
        struct event*                       timer;
    } zombie_preventer;
    struct {
        int64_t                             tx_bytes;
        int64_t                             rx_bytes;
    } stats;
    int32_t                                 destroy_count;
};

static SSL_CTX*                           conn_webserver_ssl_ctx;
static SSL_CTX*                           conn_http_proxy_server_ssl_ctx;
static struct argo_structure_description* exporter_user_portal_conn_webserver_stats_description;
static struct argo_structure_description* exporter_user_portal_conn_webserver_fohh_stats_description;
static struct argo_structure_description* exporter_user_portal_conn_webserver_audit_description;

static
struct exporter_user_portal_conn_webserver_stats_s {                            /* _ARGO: object_definition */
    /* stats that are done in the context of init thread */
    uint64_t init;                                                              /* _ARGO: integer */
    uint64_t init_fail_no_ssl_ctx;                                              /* _ARGO: integer */
    uint64_t init_done;                                                         /* _ARGO: integer */
    uint64_t init_fohh_stats;                                                   /* _ARGO: integer */

    /* stats that are done in the context of exporter_zcdns thread */
    uint64_t dns_async_cb_off_thread;                                           /* _ARGO: integer */
    uint64_t dns_async_cb_off_thread_fail;                                      /* _ARGO: integer */
}stats;

/*
 * have stats object per thread. that will let the system without thread contention - lockless. This will also avoid
 * the slightly expensive atomic operations on the variables.
 */
struct exporter_user_portal_conn_webserver_fohh_stats_s {                       /* _ARGO: object_definition */
    uint64_t alloc;                                                             /* _ARGO: integer */
    uint64_t free;                                                              /* _ARGO: integer */

    uint64_t generate_new_header;                                               /* _ARGO: integer */
    uint64_t generate_new_header_fail_already_exists;                           /* _ARGO: integer */
    uint64_t generate_new_header_update_via;                                    /* _ARGO: integer */
    uint64_t generate_new_header_update_host;                                   /* _ARGO: integer */
    uint64_t generate_new_header_update_path;                                   /* _ARGO: integer */
    uint64_t generate_new_header_add_via;                                       /* _ARGO: integer */

    uint64_t request;                                                           /* _ARGO: integer */
    uint64_t request_header_built;                                              /* _ARGO: integer */
    uint64_t request_header_sent;                                               /* _ARGO: integer */
    uint64_t request_header_send_fail;                                          /* _ARGO: integer */
    uint64_t request_header_null_bytes;                                         /* _ARGO: integer */
    uint64_t request_body_send_err;                                             /* _ARGO: integer */
    uint64_t request_body_sent;                                                 /* _ARGO: integer */
    uint64_t request_body_null_bytes;                                           /* _ARGO: integer */
    uint64_t request_body_null;                                                 /* _ARGO: integer */

    uint64_t read_cb;                                                           /* _ARGO: integer */
    uint64_t read_cb_fail_null_input_buf;                                       /* _ARGO: integer */
    uint64_t read_cb_fail_zero_len_input_buf;                                   /* _ARGO: integer */
    uint64_t read_cb_created_new_response_data;                                 /* _ARGO: integer */
    uint64_t read_cb_fail_reponse_data_full;                                    /* _ARGO: integer */
    uint64_t read_cb_fail_no_bytes_able_to_accept;                              /* _ARGO: integer */

    uint64_t message_complete_cb;                                               /* _ARGO: integer */
    uint64_t message_complete_cb_no_conn;                                       /* _ARGO: integer */
    uint64_t message_complete_cb_in_unexpected_state;                           /* _ARGO: integer */
    uint64_t message_complete_cb_in_received_and_parsed_state;                  /* _ARGO: integer */
    uint64_t message_complete_cb_in_parsed_state;                               /* _ARGO: integer */

    uint64_t status_cb;                                                         /* _ARGO: integer */
    uint64_t status_cb_connected;                                               /* _ARGO: integer */
    uint64_t status_cb_eof;                                                     /* _ARGO: integer */
    uint64_t status_cb_err;                                                     /* _ARGO: integer */
    uint64_t status_cb_timeout;                                                 /* _ARGO: integer */

    uint64_t rx_internal_err_before_response_complete;                          /* _ARGO: integer */
    uint64_t rx_internal_err_after_response_complete;                           /* _ARGO: integer */

    uint64_t zombie_possibily;                                                  /* _ARGO: integer */
    uint64_t zombie_possibily_err_oom;                                          /* _ARGO: integer */
    uint64_t zombie_possibily_err_add;                                          /* _ARGO: integer */
    uint64_t zombie_possibily_timer_fired;                                      /* _ARGO: integer */
    uint64_t zombie_possibily_timer_alloc;                                      /* _ARGO: integer */
    uint64_t zombie_possibily_timer_free;                                       /* _ARGO: integer */

    uint64_t connect_ssl;                                                       /* _ARGO: integer */
    uint64_t connect_ssl_bev_err;                                               /* _ARGO: integer */
    uint64_t connect_err;                                                       /* _ARGO: integer */

    uint64_t dns_async_cb_on_thread;                                            /* _ARGO: integer */
    uint64_t dns_cb_sync;                                                       /* _ARGO: integer */
    uint64_t dns_cb_async;                                                      /* _ARGO: integer */
    uint64_t dns_cb_fail_no_result;                                             /* _ARGO: integer */
    uint64_t dns_cb_addr_a;                                                     /* _ARGO: integer */
    uint64_t dns_cb_addr_aaaa;                                                  /* _ARGO: integer */
    uint64_t dns_cb_fail_resolve;                                               /* _ARGO: integer */

    uint64_t world_poked_me;                                                    /* _ARGO: integer */
    uint64_t world_poked_me_err_url;                                            /* _ARGO: integer */
    uint64_t world_poked_me_err_url_ver;                                        /* _ARGO: integer */

    uint64_t tx_bytes;                                                          /* _ARGO: integer */
    uint64_t rx_bytes;                                                          /* _ARGO: integer */

    uint64_t response_buffer_space_available_on_thread_no_op;                   /* _ARGO: integer */
    uint64_t response_buffer_space_available_on_thread;                         /* _ARGO: integer */
};
static struct exporter_user_portal_conn_webserver_fohh_stats_s *fohh_stats[FOHH_MAX_THREADS];


static
struct exporter_user_portal_conn_webserver_audit_s {                            /* _ARGO: object_definition */
    uint64_t    stats_cleared;                                                  /* _ARGO: integer */
}audit;
#include "exporter/exporter_user_portal_conn_webserver_compiled_c.h"
#include "exporter/zpn_sra_portal.h"

static void
exporter_user_portal_conn_webserver_state_chg(struct exporter_request*      request,
                                              enum conn_webserver_state     state);
static void
exporter_user_portal_conn_webserver_dns_cb(void*                cb_void_cookie,
                                           int64_t              cb_int_cookie,
                                           struct zcdns_result* result);
static void
exporter_user_portal_conn_webserver_rx_internal_error(struct exporter_request* request);

void *
exporter_user_portal_conn_webserver_create(struct exporter_request* request)
{
    struct conn_webserver* conn;

    fohh_stats[request->conn->thread->fohh_thread_id]->alloc++;
    conn = EXPORTER_CALLOC(sizeof(*conn));
    assert(conn);
    EXPORTER_DEBUG_USER_PORTAL_CONN_WEBSERVER("%s: conn_webserver object created", request->name);
    exporter_user_portal_request_state_set_conn(request->portal_info, conn);

    exporter_user_portal_conn_webserver_state_chg(request, ZPA_USER_PORTAL_CONN_WEBSERVER_SETTING_UP);

    return conn;
}


/*
 * Once the request not useful for conn_webserver (i.e on error or when it is completely server from conn_webserver
 * pov), free the conn object.
 */
void
exporter_user_portal_conn_webserver_free(struct exporter_request* request)
{
    struct conn_webserver*  conn;
    int32_t destroy_count;

    EXPORTER_ASSERT_SOFT((fohh_get_current_thread_id() == request->conn->thread->fohh_thread_id),
                         "expected thread(%d) != real thread(%d)", request->conn->thread->fohh_thread_id,
                         fohh_get_current_thread_id());
    conn = exporter_user_portal_request_state_get_conn(request->portal_info);
    EXPORTER_DEBUG_USER_PORTAL_CONN_WEBSERVER("%s: deleting conn_webserver, tx bytes(%"PRId64") rx bytes(%"PRId64")",
            request->name, conn->stats.tx_bytes, conn->stats.rx_bytes);

    destroy_count = __sync_add_and_fetch_4(&(conn->destroy_count), 1);
    if (destroy_count > 1) {
        EXPORTER_LOG(AL_WARNING, "%s: deleting conn_webserver already active, destroy_count: %d, server_bev: %p",
                     request->name, destroy_count, conn->server_bev);
        return;
    } else {
        EXPORTER_LOG(AL_INFO, "%s: deleting conn_webserver, destroy allowed, destroy_count: %d, server_bev: %p",
                     request->name, destroy_count, conn->server_bev);
    }

    fohh_stats[request->conn->thread->fohh_thread_id]->free++;
    if (conn->url) {
        EXPORTER_FREE(conn->url);
        conn->url = NULL;
    }
    if (conn->url_version) {
        EXPORTER_FREE(conn->url_version);
        conn->url_version = NULL;
    }
    if (conn->zombie_preventer.timer) {
        event_free(conn->zombie_preventer.timer);
        conn->zombie_preventer.timer = NULL;
        fohh_stats[request->conn->thread->fohh_thread_id]->zombie_possibily_timer_free++;
    }
    if (conn->server_bev) {
        struct evbuffer *input = bufferevent_get_input(conn->server_bev);
        size_t len = evbuffer_get_length(input);
        evutil_socket_t bev_fd;

        /* Disable events */
        bufferevent_disable(conn->server_bev, EV_READ | EV_WRITE);

        if (len) {
            EXPORTER_LOG(AL_ERROR, "%s: %zd bytes pending to read when the conn webserver is deleted, this would "
                                   "have caused http parser error", request->name, len);
        }

        bev_fd = bufferevent_getfd(conn->server_bev);
        if (bev_fd > 0) {
            /* Cleanup to avoid fd leak */
            close(bev_fd);
            bufferevent_setfd(conn->server_bev, -1);
        }

        SSL *bev_ssl = bufferevent_openssl_get_ssl(conn->server_bev);
        if (bev_ssl) {
            SSL_free(bev_ssl);
        }

        zlibevent_bufferevent_free(conn->server_bev);
        conn->server_bev = NULL;
    }
    EXPORTER_FREE(conn);
    exporter_user_portal_request_state_set_conn(request->portal_info, NULL);
}


static const char *
exporter_user_portal_conn_webserver_get_state_str(enum conn_webserver_state state)
{
    const char *str[] =  {
            "ZPA_USER_PORTAL_CONN_WEBSERVER_SETTING_UP",
            "ZPA_USER_PORTAL_CONN_WEBSERVER_DNS_REQUESTED",
            "ZPA_USER_PORTAL_CONN_WEBSERVER_DNS_DONE",
            "ZPA_USER_PORTAL_CONN_WEBSERVER_DNS_DONE_ERR",
            "ZPA_USER_PORTAL_CONN_WEBSERVER_CONNECTING",
            "ZPA_USER_PORTAL_CONN_WEBSERVER_PAGE_REQUESTED",
            "ZPA_USER_PORTAL_CONN_WEBSERVER_PAGE_BEING_RECEIVED_AND_PARSED",
            "ZPA_USER_PORTAL_CONN_WEBSERVER_PAGE_BEING_PARSED",
            "ZPA_USER_PORTAL_CONN_WEBSERVER_PAGE_DONE",
            "ZPA_USER_PORTAL_CONN_WEBSERVER_PAGE_DONE_ERR"
    };

    return str[state];
}


static void
exporter_user_portal_conn_webserver_state_chg(struct exporter_request*      request,
                                              enum conn_webserver_state     state)
{
    struct conn_webserver* conn = exporter_user_portal_request_state_get_conn(request->portal_info);
    EXPORTER_DEBUG_USER_PORTAL_CONN_WEBSERVER_DETAIL("%s: user portal state change %s -> %s", request->name,
                                  exporter_user_portal_conn_webserver_get_state_str(conn->state),
                                  exporter_user_portal_conn_webserver_get_state_str(state));
    conn->state = state;
}


/*
 * Rewrite the headers for the requests that are going to be sent to the webserver
 * 1. add/update 'via' header
 * 2. Change 'path' header to be always '/'
 * 3. Changes 'host' header to be the webserver's host name. Browser targets this 'host' header to exporter's name.
 */
static void
exporter_user_portal_conn_webserver_generate_new_header(struct exporter_request*    request)
{
    int                      i;
    int                      updated_via = 0;
    struct conn_webserver*   conn = exporter_user_portal_request_state_get_conn(request->portal_info);
    char remote_ip_str[ARGO_INET_ADDRSTRLEN] = {0};
    char local_ip_str[ARGO_INET_ADDRSTRLEN] = {0};
    int64_t customer_gid = request->orig_customer_gid;
    int updated_xff = 0;
    int updated_forwarded = 0;
    int update_content_len = 0;
    int update_url = 0;

    argo_inet_generate(remote_ip_str, &(request->conn->remote_ip));
    argo_inet_generate(local_ip_str, &(request->conn->local_ip));

    enum zpa_user_portal_api_type api_type = exporter_user_portal_request_state_get_api_type(request->portal_info);

    if ((api_type == ZPA_SRA_PORTAL_API_TYPE_MY_FILES_UPLOAD_INTIATE) ||
        (api_type == ZPA_SRA_PORTAL_API_TYPE_MY_FILES_UPLOAD_COMPLETE) ||
        (api_type == ZPA_SRA_PORTAL_API_TYPE_PRIVILEGED_DESKTOPS_AMI_LIST) ||
        (api_type == ZPA_SRA_PORTAL_API_TYPE_PRIVILEGED_DESKTOPS_VM) ||
        (api_type == ZPA_SRA_PORTAL_API_TYPE_PRIVILEGED_DESKTOPS_DJBINFO) ||
        (api_type == ZPA_SRA_PORTAL_API_TYPE_PRIVILEGED_APPROVAL_REQUEST)) {
        update_content_len = 1;
    }

    if (((api_type == ZPA_SRA_PORTAL_API_TYPE_PRIVILEGED_DESKTOPS_DJBINFO) ||
         (api_type == ZPA_SRA_PORTAL_API_TYPE_PRIVILEGED_APPROVALS_REQUESTABLE_CONSOLES))
        && request->generated_request_data != NULL) {
		update_url = 1;
	}

    fohh_stats[request->conn->thread->fohh_thread_id]->generate_new_header++;
    if (request->new_header) {
        fohh_stats[request->conn->thread->fohh_thread_id]->generate_new_header_fail_already_exists++;
        return;
    }

    request->new_header = evbuffer_new();
    assert(request->new_header);

    if (request->is_djb) {
        EXPORTER_DEBUG_USER_PORTAL_CONN_WEBSERVER_DETAIL("%s: %s%"PRId64"%s",
                                                  request->name, DJB_INFO_API_CALL_PREFIX, customer_gid, DJB_INFO_API_CALL_SUFFIX);
        evbuffer_add_printf(request->new_header,
                        "%s %s%"PRId64"%s%s HTTP/%d.%d\r\n",
                        http_method_names[request->req_method],
                        DJB_INFO_API_CALL_PREFIX,
                        customer_gid,
                        DJB_INFO_API_CALL_SUFFIX,
						update_url == 1 ? (char *) evbuffer_pullup(request->generated_request_data, -1) : "",
                        request->req_http_major,
                        request->req_http_minor);
    } else {
    EXPORTER_DEBUG_USER_PORTAL_CONN_WEBSERVER_DETAIL("%s: request->url_ofs %s",
                                                  request->name, zmicro_heap_ref(&request->heap, request->url_ofs));
    /* Generate the new http request header */
    evbuffer_add_printf(request->new_header,
                        "%s %s%s HTTP/%d.%d\r\n",
                        http_method_names[request->req_method],
                        zmicro_heap_ref(&request->heap, request->url_ofs),
						update_url == 1 ? (char *) evbuffer_pullup(request->generated_request_data, -1) : "",
                        request->req_http_major,
                        request->req_http_minor);
    }

    if (update_url) {
    	evbuffer_drain(request->generated_request_data, evbuffer_get_length(request->generated_request_data));
    }

    for (i = 0; i < request->total_headers; i++) {
        /* Update Via Header if possible */
        if (strcmp(zmicro_heap_ref(&request->heap, request->header_name_ofs[i]), "Via") == 0) {
            evbuffer_add_printf(request->new_header,
                                "%s: %s, %s\r\n",
                                zmicro_heap_ref(&request->heap, request->header_name_ofs[i]),
                                zmicro_heap_ref(&request->heap, request->header_value_ofs[i]),
                                EXPORTER_VIA_STRING);
            updated_via = 1;
            fohh_stats[request->conn->thread->fohh_thread_id]->generate_new_header_update_via++;
        } else if (strcmp(zmicro_heap_ref(&request->heap, request->header_name_ofs[i]), "Host") == 0) {
            evbuffer_add_printf(request->new_header,
                                "%s: %s\r\n",
                                zmicro_heap_ref(&request->heap, request->header_name_ofs[i]),
                                conn->url);
            fohh_stats[request->conn->thread->fohh_thread_id]->generate_new_header_update_host++;
        } else if (strcmp(zmicro_heap_ref(&request->heap, request->header_name_ofs[i]), "path") == 0) {
            evbuffer_add_printf(request->new_header,
                                "%s: %s/\r\n",
                                zmicro_heap_ref(&request->heap, request->header_name_ofs[i]),
                                conn->url_version);
            fohh_stats[request->conn->thread->fohh_thread_id]->generate_new_header_update_path++;
        } else if((strcmp(zmicro_heap_ref(&request->heap, request->header_name_ofs[i]), "X-Forwarded-For") == 0) &&
                   (exporter_xff_header_enabled_for_customer(customer_gid))) {
			evbuffer_add_printf(request->new_header,
							"%s: %s, %s\r\n",
							zmicro_heap_ref(&request->heap, request->header_name_ofs[i]),
							zmicro_heap_ref(&request->heap, request->header_value_ofs[i]),
							local_ip_str);
            updated_xff = 1;
        } else if((strcmp(zmicro_heap_ref(&request->heap, request->header_name_ofs[i]), "Forwarded") == 0) &&
                   (exporter_forwarded_header_enabled_for_customer(customer_gid))) {
			if (request->conn->local_ip.length == 4) {
			    evbuffer_add_printf(request->new_header,
							    "%s: %s, for=%s\r\n",
                                zmicro_heap_ref(&request->heap, request->header_name_ofs[i]),
                                zmicro_heap_ref(&request->heap, request->header_value_ofs[i]),
                                local_ip_str);
		    } else {
			    evbuffer_add_printf(request->new_header,
                                "%s: %s, for=\"[%s]\"\r\n",
                                zmicro_heap_ref(&request->heap, request->header_name_ofs[i]),
                                zmicro_heap_ref(&request->heap, request->header_value_ofs[i]),
                                local_ip_str);
            }
            updated_forwarded = 1;
        } else if(update_content_len &&
                    (strcmp(zmicro_heap_ref(&request->heap, request->header_name_ofs[i]), "Content-Length") == 0)) {
            EXPORTER_DEBUG_USER_PORTAL_CONN_WEBSERVER_DETAIL("%s: Content length updated", request->name);
            evbuffer_add_printf(request->new_header, "Content-Length: %zu \r\n", evbuffer_get_length(request->request_body));
            update_content_len = 0;
        } else if (request->is_djb &&
                   (strcmp(zmicro_heap_ref(&request->heap, request->header_name_ofs[i]), "Connection") == 0)) {
            evbuffer_add_printf(request->new_header, "Connection: keep-alive \r\n");
        } else if (request->is_djb &&
                   (strcmp(zmicro_heap_ref(&request->heap, request->header_name_ofs[i]), "Accept") == 0)) {
            evbuffer_add_printf(request->new_header, "Accept: application/json \r\n");
        } else if (request->is_djb &&
                   (strcmp(zmicro_heap_ref(&request->heap, request->header_name_ofs[i]), "Sec-WebSocket") == 0)) {
            EXPORTER_DEBUG_USER_PORTAL_CONN_WEBSERVER_DETAIL("%s: DJB Requests do not need these headers", request->name);
        } else if (request->is_djb &&
                   (strcmp(zmicro_heap_ref(&request->heap, request->header_name_ofs[i]), "Content-Type") == 0)) {
            evbuffer_add_printf(request->new_header, "Content-Type: application/json \r\n");
        } else {
            evbuffer_add_printf(request->new_header,
                                "%s: %s\r\n",
                                zmicro_heap_ref(&request->heap, request->header_name_ofs[i]),
                                zmicro_heap_ref(&request->heap, request->header_value_ofs[i]));
        }
    }

    if (!updated_via) {
        evbuffer_add_printf(request->new_header, "Via: %s\r\n", EXPORTER_VIA_STRING);
        fohh_stats[request->conn->thread->fohh_thread_id]->generate_new_header_add_via++;
    }

	if (exporter_xff_header_enabled_for_customer(customer_gid) && !updated_xff) {
        evbuffer_add_printf(request->new_header, "X-Forwarded-For: %s, %s\r\n", remote_ip_str, local_ip_str);
        updated_xff = 1;
    }

	if (exporter_forwarded_header_enabled_for_customer(customer_gid) && !updated_forwarded) {
        if ((request->conn->local_ip.length == 4) && (request->conn->remote_ip.length == 4)) {
            evbuffer_add_printf(request->new_header, "Forwarded: for=%s, for=%s\r\n", remote_ip_str, local_ip_str);
        } else if ((request->conn->local_ip.length == 16) && (request->conn->remote_ip.length == 16)) {
            evbuffer_add_printf(request->new_header, "Forwarded: for=\"[%s]\", for=\"[%s]\"\r\n", remote_ip_str, local_ip_str);
        } else if (request->conn->local_ip.length == 16) {
            evbuffer_add_printf(request->new_header, "Forwarded: for=%s, for=\"[%s]\"\r\n", remote_ip_str, local_ip_str);
        } else {
            evbuffer_add_printf(request->new_header, "Forwarded: for=\"[%s]\", for=%s\r\n", remote_ip_str, local_ip_str);
        }
        updated_forwarded = 1;
    }

    evbuffer_add_printf(request->new_header, "Strict-Transport-Security: max-age=" MAX_AGE_HSTS "; includeSubDomains; preload\r\n");

    evbuffer_add_printf(request->new_header, "\r\n");

#if 0
    /*
     * Have cookie info, so print only if you really require for a debugging.
     */
    if (exporter_debug & EXPORTER_DEBUG_USER_PORTAL_CONN_WEBSERVER_DETAIL_BIT) {
        char buffer[1024*8];

        memset(buffer, 0, sizeof(buffer));
        evbuffer_copyout(request->new_header, buffer, sizeof(buffer));
        EXPORTER_DEBUG_USER_PORTAL_CONN_WEBSERVER_DETAIL("%s: Generated header %s", request->name, buffer);
    }
#endif
}


/*
 * Send out GET request to the webserver
 *
 * 1. generate new headers
 * 2. Just send the newly generated header & body
 */
static int
exporter_user_portal_conn_webserver_request(struct exporter_request*    request)
{
    int                     res;
    struct conn_webserver*  conn = exporter_user_portal_request_state_get_conn(request->portal_info);
    struct bufferevent*     server_bev = conn->server_bev;
    size_t                  header_tx_len;
    size_t                  body_tx_len;

    if (request->http_request_complete == 0) {
        EXPORTER_LOG(AL_ERROR, "%s: http request incomplete for url %s request_num %d req_method %s",
                     request->name, request->url, request->request_num, http_method_str(request->req_method));
        exporter_user_portal_conn_webserver_rx_internal_error(request);
        return ZPATH_RESULT_NO_ERROR;
    }

    fohh_stats[request->conn->thread->fohh_thread_id]->request++;
    if (!request->http_request_generated) {
        EXPORTER_DEBUG_USER_PORTAL_CONN_WEBSERVER_DETAIL("%s: Generating new headers", request->name);
        exporter_user_portal_conn_webserver_generate_new_header(request);
        request->http_request_generated = 1;
        fohh_stats[request->conn->thread->fohh_thread_id]->request_header_built++;
    }
    EXPORTER_DEBUG_USER_PORTAL_CONN_WEBSERVER_DETAIL("%s: Preparing to send request to webserver to fetch user portal page",
                                              request->name);

    assert(0 == request->log.req_tx_start_us);
    request->log.req_tx_start_us = epoch_us();

    header_tx_len = evbuffer_get_length(request->new_header);
    if (header_tx_len) {
        EXPORTER_DEBUG_USER_PORTAL_CONN_WEBSERVER_DETAIL("%s: Sending header with %zu bytes to webserver to fetch user "
                                                  "portal page", request->name, header_tx_len);
        res = bufferevent_write_buffer(server_bev, request->new_header);
        if (res) {
            fohh_stats[request->conn->thread->fohh_thread_id]->request_header_send_fail++;
            EXPORTER_LOG(AL_ERROR, "%s: Sending header to webserver to fetch user portal page, returned %s",
                         request->name, zpath_result_string(res));
            return res;
        } else {
            fohh_stats[request->conn->thread->fohh_thread_id]->request_header_sent++;
            conn->stats.tx_bytes += header_tx_len;
            fohh_stats[request->conn->thread->fohh_thread_id]->tx_bytes += header_tx_len;
        }
    } else {
        EXPORTER_DEBUG_USER_PORTAL_CONN_WEBSERVER_DETAIL("%s: Sending header to webserver, but seeing zero bytes",
                                                  request->name);
        fohh_stats[request->conn->thread->fohh_thread_id]->request_header_null_bytes++;
    }
    evbuffer_free(request->new_header);
    request->new_header = NULL;

    if (request->request_body) {
        body_tx_len = evbuffer_get_length(request->request_body);
        if (body_tx_len) {
            EXPORTER_DEBUG_USER_PORTAL_CONN_WEBSERVER_DETAIL("%s: Sending body with %zu bytes to webserver to fetch user "
                                                      "portal page", request->name, body_tx_len);
            enum zpa_user_portal_api_type api_type = exporter_user_portal_request_state_get_api_type(request->portal_info);
            if ((api_type == ZPA_SRA_PORTAL_API_TYPE_PRIVILEGED_APPROVAL_REQUEST) ||
                (api_type == ZPA_SRA_PORTAL_API_TYPE_PRIVILEGED_DESKTOPS_AMI_LIST) ||
                (api_type == ZPA_SRA_PORTAL_API_TYPE_PRIVILEGED_DESKTOPS_VM))
              {
                // Dumping the request body being sent to PRA service
                char tmp_buf[1000] = {0};
                res = dump_json_data(request->request_body, tmp_buf, sizeof(tmp_buf));
                if (res == ZPATH_RESULT_NO_ERROR) {
                    EXPORTER_DEBUG_USER_PORTAL_CONN_WEBSERVER_DETAIL("%s: JSON data being sent %s \n",
                        request->name, tmp_buf);
                }
            }
            res = bufferevent_write_buffer(server_bev, request->request_body);
            if (res) {
                fohh_stats[request->conn->thread->fohh_thread_id]->request_body_send_err++;
                EXPORTER_LOG(AL_ERROR, "%s: Sending body to webserver to fetch user portal page, returned %d",
                             request->name, res);
                return res;
            } else {
                fohh_stats[request->conn->thread->fohh_thread_id]->request_body_sent++;
                conn->stats.tx_bytes += body_tx_len;
                fohh_stats[request->conn->thread->fohh_thread_id]->tx_bytes += header_tx_len;
            }
        } else {
            EXPORTER_DEBUG_USER_PORTAL_CONN_WEBSERVER_DETAIL("%s: Sending body to webserver, but seeing zero bytes",
                                                      request->name);
            fohh_stats[request->conn->thread->fohh_thread_id]->request_body_null_bytes++;
        }
    } else {
        fohh_stats[request->conn->thread->fohh_thread_id]->request_body_null++;
    }

    request->log.req_tx_done_us = epoch_us();
    exporter_user_portal_conn_webserver_state_chg(request, ZPA_USER_PORTAL_CONN_WEBSERVER_PAGE_REQUESTED);
    return ZPATH_RESULT_NO_ERROR;
}


/*
 * copy of exporter_request_mt_consume_callback()
 *
 * Read data from the server's input buffer.
 *
 * Read callback happens from two places,
 * a. When the bufferevent wanted us to read the input buffer and
 * b. when the request object wanted us to read the input buffer
 */
static void
exporter_user_portal_conn_webserver_read_cb(struct bufferevent* bev,
                                            void*               cookie)
{
    struct exporter_request*    request;
    struct conn_webserver*      conn;
    struct evbuffer*            read_buf;
    size_t                      bytes_len_from_server;
    size_t                      max_bytes_to_be_received_from_server;
    size_t                      actual_bytes_read_from_server;
    size_t                      bytes_already_in_response_buffer;

    read_buf = bufferevent_get_input(bev);
    request = cookie;

    fohh_stats[request->conn->thread->fohh_thread_id]->read_cb++;
    if (!read_buf) {
        EXPORTER_LOG(AL_WARNING, "%s: data available to read from server, but evbuffer is NULL!", request->name);
        fohh_stats[request->conn->thread->fohh_thread_id]->read_cb_fail_null_input_buf++;
        return;
    }

    bytes_len_from_server = evbuffer_get_length(read_buf);
    if (0 == bytes_len_from_server) {
        EXPORTER_LOG(AL_WARNING, "%s: data available to read from server, but evbuffer don't have bytes!",
                     request->name);
        fohh_stats[request->conn->thread->fohh_thread_id]->read_cb_fail_zero_len_input_buf++;
        return;
    }
    EXPORTER_DEBUG_USER_PORTAL_CONN_WEBSERVER_DETAIL("%s: %zd bytes of data available to read from server",
                                                     request->name, bytes_len_from_server);
    ZPATH_MUTEX_LOCK(&(request->lock), __FILE__, __LINE__);
    if (!request->response_data) {
        request->response_data = evbuffer_new();
        fohh_stats[request->conn->thread->fohh_thread_id]->read_cb_created_new_response_data++;
        EXPORTER_DEBUG_USER_PORTAL_CONN_WEBSERVER_DETAIL("%s: new response data(%p) for the request allocated",
                                                  request->name, request->response_data);
        request->log.rsp_rx_start_us = epoch_us();
    }

    bytes_already_in_response_buffer = evbuffer_get_length(request->response_data);
    if (bytes_already_in_response_buffer > HTTP_RESPONSE_MAX_BUFFER_DATA) {
        EXPORTER_ASSERT_SOFT(0, "%s: Not expecting to overfeed(%s) the response buffer, its a bug", request->name,
                             bytes_already_in_response_buffer);
        fohh_stats[request->conn->thread->fohh_thread_id]->read_cb_fail_reponse_data_full++;
        max_bytes_to_be_received_from_server = 0;
    } else if ((bytes_len_from_server + bytes_already_in_response_buffer)  < HTTP_RESPONSE_MAX_BUFFER_DATA) {
        max_bytes_to_be_received_from_server = bytes_len_from_server;
    } else {
        max_bytes_to_be_received_from_server = HTTP_RESPONSE_MAX_BUFFER_DATA - bytes_already_in_response_buffer;
    }

    if (max_bytes_to_be_received_from_server <= 0) {
        /*
         * the response buffer is not ready to receive data from the server now. The input buffer of the bufferevent is
         * 128KB long. So even if we stop reading data from the server, the outstanding buffer in userspace is only
         * 128KB.
         */
        EXPORTER_DEBUG_USER_PORTAL_CONN_WEBSERVER("%s: response buffer already have %zd bytes for http parsing, so disable further reading from server",
                                                  request->name, bytes_already_in_response_buffer);
        fohh_stats[request->conn->thread->fohh_thread_id]->read_cb_fail_no_bytes_able_to_accept++;
        bufferevent_disable(bev, EV_READ);
        request->need_to_unblock_source = 1;
        ZPATH_MUTEX_UNLOCK(&(request->lock), __FILE__, __LINE__);
        return;
    }

    /*
     * If we have already received more bytes from server, but not drained it, we may not get read callback. So ask
     * the response buffer to give us a callback once it has drained some bytes.
     */
    if (bytes_len_from_server > max_bytes_to_be_received_from_server) {
        EXPORTER_DEBUG_USER_PORTAL_CONN_WEBSERVER("%s: received (%zd) bytes from server already, but now pulling only (%zd) bytes to the response buffer - expect wakeup call from response buffer",
                                                  request->name, bytes_len_from_server, max_bytes_to_be_received_from_server);
        request->need_to_unblock_source = 1;
    }

    if (request->is_djb && request->djb_info) {
        /* Add to a new buffer and adjust bytes_already_in_response_buffer */
        actual_bytes_read_from_server = evbuffer_remove_buffer(read_buf, request->djb_info->djb_response_data,
                                                           max_bytes_to_be_received_from_server);
#if 0
        /* For internal debugging only - dump the response data being read from Desktop service */
        char tmp_buf[1000] = {0};
        int res = dump_json_data(request->djb_info->djb_response_data, tmp_buf, sizeof(tmp_buf));
        if (res == ZPATH_RESULT_NO_ERROR) {
            EXPORTER_DEBUG_USER_PORTAL_CONN_WEBSERVER_DETAIL("%s: (After transfer) JSON data being sent %s \n",
                        request->name, tmp_buf);
        }
#endif
    } else {
        actual_bytes_read_from_server = evbuffer_remove_buffer(read_buf, request->response_data,
                                                           max_bytes_to_be_received_from_server);
    }
    EXPORTER_DEBUG_USER_PORTAL_CONN_WEBSERVER_DETAIL("%s: %zd bytes of data enqueued to response buffer (it already had %zd bytes) for http parsing",
                                              request->name, actual_bytes_read_from_server,
                                              bytes_already_in_response_buffer);
    conn = exporter_user_portal_request_state_get_conn(request->portal_info);
    conn->stats.rx_bytes += actual_bytes_read_from_server;
    fohh_stats[request->conn->thread->fohh_thread_id]->rx_bytes += actual_bytes_read_from_server;

    if (request->is_djb) {
        int res = ZPATH_RESULT_NO_ERROR;
        if (request->djb_info) {
            request->djb_info->bytes_len_from_server = bytes_len_from_server;
            request->djb_info->djb_server_request_complete = 1;
        } else {
            EXPORTER_LOG(AL_ERROR, "%s: Request DJB Info has been deleted. Should not happen as its freed with request destroy", request->name);
            if (request->response_data) evbuffer_drain(request->response_data, evbuffer_get_length(request->response_data));
        }
        EXPORTER_DEBUG_USER_PORTAL_CONN_WEBSERVER_DETAIL("%s: DJB Info is not sent back to the browser", request->name);
        /* Drain ruleID added in request body */
        if (request->request_body) evbuffer_drain(request->request_body, evbuffer_get_length(request->request_body));
        exporter_request_djb_reset_api_type(request);
        res = exporter_request_async_callback((void *)request, 0);
        if (res) {
            EXPORTER_LOG(AL_ERROR, "%s: Unable to enqueue zevent for DJB Info response processing", request->name);
        }
        ZPATH_MUTEX_UNLOCK(&(request->lock), __FILE__, __LINE__);
        exporter_user_portal_conn_webserver_state_chg(request, ZPA_USER_PORTAL_CONN_WEBSERVER_PAGE_BEING_RECEIVED_AND_PARSED);
        return;
    }

    /*
     * We remember if we have sent any data to the client because that affects how we close connections when they
     * error (mid-stream vs. not)
     */
    request->data_sent_to_client = 1;
    ZPATH_MUTEX_UNLOCK(&(request->lock), __FILE__, __LINE__);

    exporter_conn_wake_from_other_thread(request->conn);
    exporter_user_portal_conn_webserver_state_chg(request, ZPA_USER_PORTAL_CONN_WEBSERVER_PAGE_BEING_RECEIVED_AND_PARSED);
}


static void
exporter_user_portal_conn_webserver_response_buffer_space_available_on_thread(struct zevent_base*     zbase,
                                                                              void*                   cookie,
                                                                              int64_t                 int_cookie)
{
    struct exporter_request*    request;
    struct conn_webserver*      conn_webserver;

    request = cookie;
    conn_webserver = exporter_user_portal_request_state_get_conn(request->portal_info);
    if (NULL == conn_webserver) {
        /*
         * This can happen if the conn_webserver object is deleted as the HTTP parser said all the bytes are in. But
         * even before that, in the read callback, conn_webserver asked for a callback from reponse object.
         */
        EXPORTER_DEBUG_USER_PORTAL_CONN_WEBSERVER_DETAIL("%s: response buffer have space available now, so good to read data from server now - on thread, but conn deleted already!", request->name);
        fohh_stats[request->conn->thread->fohh_thread_id]->response_buffer_space_available_on_thread_no_op++;
        request->ref_count--;
        return;
    }

    fohh_stats[request->conn->thread->fohh_thread_id]->response_buffer_space_available_on_thread++;
    EXPORTER_DEBUG_USER_PORTAL_CONN_WEBSERVER_DETAIL("%s: response buffer have space available now, so good to read data from server now - on thread", request->name);
    bufferevent_enable(conn_webserver->server_bev, EV_READ);
    request->ref_count--;
    exporter_user_portal_conn_webserver_read_cb(conn_webserver->server_bev, request);
}


/*
 * The response buffer processing module calls this function to let me know that it have some buffer available. Called
 * with lock held on response object.
 *
 * Ok, now its upto me to takes the bytes from server and give it to response buffer.
 *
 * It is called from response buffer handling which is in another thread context. So switch it back to our thread
 * context.
 */
void
exporter_user_portal_conn_webserver_response_buffer_space_available_another_thread(struct exporter_request *request)
{
    int res;
    EXPORTER_ASSERT_SOFT((fohh_get_current_thread_id() == request->conn->thread->fohh_thread_id),
                         "expected thread(%d) != real thread(%d)", request->conn->thread->fohh_thread_id,
                         fohh_get_current_thread_id());
    request->need_to_unblock_source = 0;
    request->ref_count++;
    res = fohh_thread_call_zevent(request->conn->thread->fohh_thread_id, exporter_user_portal_conn_webserver_response_buffer_space_available_on_thread, request, 0);
    if (res) {
        EXPORTER_ASSERT_SOFT(0, "%s: couldn't wake up thread after response buffer space became available, this request is stuck",
                             request->name);
    }
}


/*
 * We have no way of knowing when the page is complete except from HTTP parser. Hence this hook from http parser.
 * We expect to receive this message in,
 * 1. BEING_RECEIVED state when the page is actually fetched and sent to the browser.
 * 2. In any other state, when there is a error and we are sending the browser a HTTP error code back.
 */
void
exporter_user_portal_conn_webserver_message_complete_cb(struct exporter_request*    request)
{
    struct conn_webserver*  conn = exporter_user_portal_request_state_get_conn(request->portal_info);
    /*
     * We are bothered about this callback only when conn_webserver is involved. In all other cases(eg. API's
     * response), we just ignore this. conn is NULL when it is not a connection request (i.e API request).
     */
    EXPORTER_ASSERT_SOFT((fohh_get_current_thread_id() == request->conn->thread->fohh_thread_id),
                         "expected thread(%d) != real thread(%d)", request->conn->thread->fohh_thread_id,
                         fohh_get_current_thread_id());
    fohh_stats[request->conn->thread->fohh_thread_id]->message_complete_cb++;
    if (!conn) {
        fohh_stats[request->conn->thread->fohh_thread_id]->message_complete_cb_no_conn++;
        return;
    }

    if (ZPA_USER_PORTAL_CONN_WEBSERVER_PAGE_BEING_PARSED == conn->state) {
        EXPORTER_DEBUG_USER_PORTAL_CONN_WEBSERVER_DETAIL("%s: HTTP response from web server is complete, page fetch "
                                                  "took %"PRId64" usec",
                                                  request->name, (epoch_us()- conn->diag_web_server_start_time_us));
        fohh_stats[request->conn->thread->fohh_thread_id]->message_complete_cb_in_parsed_state++;
    } else if (ZPA_USER_PORTAL_CONN_WEBSERVER_PAGE_BEING_RECEIVED_AND_PARSED == conn->state) {
        EXPORTER_DEBUG_USER_PORTAL_CONN_WEBSERVER_DETAIL("%s: HTTP response from web server is complete, page fetch "
                                                  "took %"PRId64" usec",
                                                  request->name, (epoch_us()- conn->diag_web_server_start_time_us));
        fohh_stats[request->conn->thread->fohh_thread_id]->message_complete_cb_in_received_and_parsed_state++;
    } else {
        EXPORTER_DEBUG_USER_PORTAL_CONN_WEBSERVER_DETAIL("%s: HTTP request from web server failed", request->name);
        fohh_stats[request->conn->thread->fohh_thread_id]->message_complete_cb_in_unexpected_state++;
    }

    exporter_user_portal_conn_webserver_state_chg(request, ZPA_USER_PORTAL_CONN_WEBSERVER_PAGE_DONE);
    exporter_user_portal_conn_webserver_free(request);
}


/*
 * conn_webserver receive processing encountered a unsolvable error. Send internal server error
 */
static void
exporter_user_portal_conn_webserver_rx_internal_error(struct exporter_request*    request)
{

    if (0 == request->http_response_complete) {
        if (NULL == request->header_connection_keep_alive || NULL != strstr(request->header_connection_keep_alive, "keep")) {
            EXPORTER_DEBUG_USER_PORTAL_CONN_WEBSERVER("%s: internal error when receiving data from conn webserver before "
                                                  "response complete", request->name);
            /*
             * If http response is not complete and we received an error, send err to browser
             * If http response is complete - it might be that we got err after the data is sent to browser. That is ok.
             */
            exporter_request_respond(request, HTTP_STATUS_INTERNAL_SERVER_ERROR, EXPORTER_ERROR_CODE_ERROR_BEFORE_RESPONSE_COMPLETE);
            exporter_user_portal_conn_webserver_state_chg(request, ZPA_USER_PORTAL_CONN_WEBSERVER_PAGE_DONE_ERR);
            request->log.req_tx_done_us = epoch_us();
            request->http_response_complete = 1;
            fohh_stats[request->conn->thread->fohh_thread_id]->rx_internal_err_before_response_complete++;
            exporter_conn_wake_from_other_thread(request->conn);
        } else {
            request->conn->destroying = 1;
            exporter_request_destroy(request);
        }
    } else {
        EXPORTER_DEBUG_USER_PORTAL_CONN_WEBSERVER("%s: internal error when receiving data from conn webserver after "
                                                  "response complete", request->name);
        fohh_stats[request->conn->thread->fohh_thread_id]->rx_internal_err_after_response_complete++;
        exporter_user_portal_conn_webserver_free(request);
    }
    if (request->is_djb) {
        EXPORTER_LOG(AL_ERROR, "%s: Privileged Desktop Service Connection Error", request->name);
        if (request->djb_info) {
            request->djb_info->djb_server_request_complete = 1;
            exporter_request_djb_reset_api_type(request);
            (void)exporter_request_async_callback((void *)request, 0);
        }
    }
}


/*
 * We earlier tagged this conn object as MAY BE zombie. Now that the timer is expired, we can confirm that it is a
 * zombie. And we should clean it up now with the assumption that receving from the webserver resulted in a internal
 * error.
 */
static void
exporter_user_portal_conn_webserver_clean_zombie(evutil_socket_t    fd,
                                                 short              what,
                                                 void*              arg)
{
    struct conn_webserver*      conn;

    conn = arg;

    fohh_stats[conn->zombie_preventer.request->conn->thread->fohh_thread_id]->zombie_possibily_timer_fired++;
    EXPORTER_DEBUG_USER_PORTAL_CONN_WEBSERVER("%s: timer fired to clean the zombie conn webserver object",
                                              conn->zombie_preventer.request->name);

    exporter_user_portal_conn_webserver_rx_internal_error(conn->zombie_preventer.request);
}


/*
 * Create a timer to cleanup any stale conn_webserver object. Create only in the context of the fohh thread that you
 * the request is being processed. No thread switching to make life easier. We are passing the request object into
 * libevent's context without taking a refrence counter. This is ok, as if the request gets deleted, the conn_webserver
 * will get deleted and this event will also get deleted.
 */
static void
exporter_user_portal_conn_webserver_possibly_zombie(struct exporter_request*    request,
                                                    struct conn_webserver*      conn)
{
    struct timeval tv;

    exporter_user_portal_conn_webserver_state_chg(request, ZPA_USER_PORTAL_CONN_WEBSERVER_PAGE_BEING_PARSED);
    fohh_stats[request->conn->thread->fohh_thread_id]->zombie_possibily++;
    EXPORTER_DEBUG_USER_PORTAL_CONN_WEBSERVER("%s: possibily a zombie conn_webserver object, prepare to clean it",
                                              request->name);

    tv.tv_sec = ZOMBIE_CLEANUP_WAIT_TIMER_US / 1000000;
    tv.tv_usec = ZOMBIE_CLEANUP_WAIT_TIMER_US % 1000000;

    conn->zombie_preventer.request = request;
    conn->zombie_preventer.timer = event_new(request->conn->thread->ev_base, -1, 0,
                                             exporter_user_portal_conn_webserver_clean_zombie, conn);
    if (!conn->zombie_preventer.timer) {
        fohh_stats[request->conn->thread->fohh_thread_id]->zombie_possibily_err_oom++;
        EXPORTER_LOG(AL_ERROR, "%s: Could not create zombie preventer timer - out of memory", request->name);
        return exporter_user_portal_conn_webserver_clean_zombie(0, 0, conn);
    }
    fohh_stats[request->conn->thread->fohh_thread_id]->zombie_possibily_timer_alloc++;

    if (event_add(conn->zombie_preventer.timer, &tv)) {
        fohh_stats[request->conn->thread->fohh_thread_id]->zombie_possibily_err_add++;
        EXPORTER_LOG(AL_ERROR, "%s: Could not create zombie preventer timer - timer arming problem", request->name);
        return exporter_user_portal_conn_webserver_clean_zombie(0, 0, conn);
    }
}

/*
 * called on error cases or when connection to webserver is closed.
 */
static void
exporter_user_portal_conn_webserver_status_cb(struct bufferevent*   bev,
                                              short                 style,
                                              void*                 cookie)
{
    struct exporter_request* request;
    struct conn_webserver*   conn;
    char*                    b_reading = "";
    char*                    b_writing = "";
    char*                    b_eof = "";
    char*                    b_error = "";
    char*                    b_timeout = "";
    char*                    b_connected = "";

    if (style & BEV_EVENT_READING) b_reading = " BEV_EVENT_READING";
    if (style & BEV_EVENT_WRITING) b_writing = " BEV_EVENT_WRITING";
    if (style & BEV_EVENT_EOF) b_eof = " BEV_EVENT_EOF";
    if (style & BEV_EVENT_ERROR) b_error = " BEV_EVENT_ERROR";
    if (style & BEV_EVENT_TIMEOUT) b_timeout = " BEV_EVENT_TIMEOUT";
    if (style & BEV_EVENT_CONNECTED) b_connected = " BEV_EVENT_CONNECTED";

    request = cookie;
    conn = exporter_user_portal_request_state_get_conn(request->portal_info);
    EXPORTER_DEBUG_USER_PORTAL_CONN_WEBSERVER_DETAIL("%s: Received event %s%s%s%s%s%s", request->name, b_reading, b_writing,
                                              b_eof, b_error, b_timeout, b_connected);
    fohh_stats[request->conn->thread->fohh_thread_id]->status_cb++;

    if (style & BEV_EVENT_CONNECTED) {
        fohh_stats[request->conn->thread->fohh_thread_id]->status_cb_connected++;
        int tmp_val = 1;
        int sock_fd = bufferevent_getfd(bev);
        EXPORTER_DEBUG_USER_PORTAL_CONN_WEBSERVER_DETAIL("%s: successfully connected to webserver, fd(%d), now ready to "
                                                  "request user portal page", request->name, sock_fd);
#if (defined(__APPLE__) && defined(__MACH__)) || defined(__FreeBSD__)
        setsockopt(sock_fd, SOL_SOCKET, SO_NOSIGPIPE, (void *) &tmp_val, sizeof(tmp_val));
#endif
        setsockopt(sock_fd, IPPROTO_TCP, TCP_NODELAY, (void *) &tmp_val, sizeof(tmp_val));
        exporter_user_portal_conn_webserver_request(request);
    } else if (style & BEV_EVENT_EOF) {
        /*
         * Looking at http://www.wangafu.net/~nickm/libevent-book/Ref6_bufferevent.html , it is clear that when EOF
         * is received, we can still have data in the buffer and we should drain it. Now to drain it inline or in
         * read callback? We should drain it in the read callback a. as that will do it in proper chunks b. we for
         * sure will receive the read callback as long as there are more than 0 bytes in the read evbuffer. This
         * makes sense if we think that event cb and read cb are totally out of sync.
         *
         * If the server (or a firewall in the middle) has really sent TCP RST without sending the full contents, we
         * wait for 1 seconds and kill the conn_webserver. We should wait because the server would have really sent
         * all the contents down to us and sent a TCP RST.
         */
        struct evbuffer *input = bufferevent_get_input(bev);
        size_t len = evbuffer_get_length(input);
        fohh_stats[request->conn->thread->fohh_thread_id]->status_cb_eof++;
        EXPORTER_DEBUG_USER_PORTAL_CONN_WEBSERVER_DETAIL("%s: disconnected from webserver, EOF - ignore as http parser will "
                                                  "take care of this. conn active for %"PRId64" usec",
                                                  request->name, (epoch_us() - conn->diag_web_server_start_time_us));
        if (len) {
            EXPORTER_LOG(AL_ERROR, "%s: %zd bytes pending to read, when the conn webserver received EOF",
                         request->name, len);
        }
        exporter_user_portal_conn_webserver_possibly_zombie(request, conn);
    } else if (style & (BEV_EVENT_ERROR)) {
        /*
         * Assuming that ERROR is caused during connection setup phase and we don't have to worry about data pending
         * in the socket as most likely there will not be any data. If we find a case, where we got all the data and
         * then the socket received a ERROR, we will have to handle it like we do for BEV_EVENT_EOF case above.
         */
        fohh_stats[request->conn->thread->fohh_thread_id]->status_cb_err++;
        EXPORTER_LOG(AL_ERROR, "%s: Received event %s%s%s%s%s%s - send internal server err to client",
                     request->name, b_reading, b_writing, b_eof, b_error, b_timeout, b_connected);
        goto err;
    } else if (style & BEV_EVENT_TIMEOUT) {
        fohh_stats[request->conn->thread->fohh_thread_id]->status_cb_timeout++;
        EXPORTER_LOG(AL_ERROR, "%s: Received event %s%s%s%s%s%s - send internal server err to client",
                     request->name, b_reading, b_writing, b_eof, b_error, b_timeout, b_connected);
        goto err;
    }
    return;
err:
    exporter_user_portal_conn_webserver_rx_internal_error(request);
}


/*
 * connect to webserver in the thread context that the request came in.
 */
static void
exporter_user_portal_conn_webserver_connect(struct exporter_request* request)
{
    struct bufferevent* bev;
    SSL *               ssl;
    struct timeval      socket_read_timeout;

    struct conn_webserver* conn = exporter_user_portal_request_state_get_conn(request->portal_info);

    conn->diag_web_server_start_time_us = epoch_us();

    /*
     * ssl is freed in bufferevent_openssl.c in success case.
     */
    if ((request->webserver_type != exporter_conn_webserver_type_session_store) &&
        (request->webserver_type != exporter_conn_webserver_type_pra_service) &&
        (request->webserver_type != exporter_conn_webserver_type_pra_desktops)) {
        ssl = SSL_new(conn_webserver_ssl_ctx);
        if (!ssl) {
            goto err;
        }
    } else {
        ssl = SSL_new(conn_http_proxy_server_ssl_ctx);
        if (!ssl) {
            goto err;
        }
    }
    SSL_set_tlsext_host_name(ssl, conn->url);

    /*
     * Specificially not using BEV_OPT_THREADSAFE as we don't expect multiple threads to operate on this thread.
     * results in slightly optimized code.
     *
     * libevent have a bug in ssl + BEV_OPT_CLOSE_ON_FREE combo that it makes a bad openssl cleanup and we won't be
     * able to take advantage of the openssl session cache.
     * http://www.wangafu.net/~nickm/libevent-book/Ref6a_advanced_bufferevents.html
     * We have workaround to make openssl happy, but will do it only if not using openssl session cache is a problem
     * in the current scenarion.
     */
    bev = bufferevent_openssl_socket_new(fohh_get_current_thread_event_base(), -1, ssl, BUFFEREVENT_SSL_CONNECTING,
                                         BEV_OPT_THREADSAFE | BEV_OPT_DEFER_CALLBACKS);
                                         //BEV_OPT_DEFER_CALLBACKS | BEV_OPT_CLOSE_ON_FREE);
    fohh_stats[request->conn->thread->fohh_thread_id]->connect_ssl++;
    if (!bev) {
        fohh_stats[request->conn->thread->fohh_thread_id]->connect_ssl_bev_err++;
        EXPORTER_LOG(AL_ERROR, "%s: Could not create BEV, when connecting to webserver", request->name);
        goto err;
    }
    bufferevent_setcb(bev, exporter_user_portal_conn_webserver_read_cb, NULL,
                      exporter_user_portal_conn_webserver_status_cb, request);
    socket_read_timeout.tv_sec = CONN_WEBSERVER_READ_TIMEOUT / 1000000;
    socket_read_timeout.tv_usec = CONN_WEBSERVER_READ_TIMEOUT % 1000000;
    bufferevent_set_timeouts(bev, &socket_read_timeout, NULL);
    bufferevent_enable(bev, EV_READ|EV_WRITE);

    bufferevent_setwatermark(bev, EV_READ, 0, g_exporter_ot_mode ? EXPORTER_OT_MAX_BUFFER_USER_DATA : EXPORTER_MAX_BUFFER_USER_DATA);
    conn->server_bev = bev;
    /*
     * below connect is non-blocking as libevent creates a socket and makes it non blocking before connecting.
     */
    if (bufferevent_socket_connect(bev, (struct sockaddr *)&conn->server_sa, conn->server_sa_len)) {
        fohh_stats[request->conn->thread->fohh_thread_id]->connect_err++;
        EXPORTER_LOG(AL_ERROR, "%s: Could not connect to webserver - user portal page fetch failed", request->name);
        goto err;
    }
    exporter_user_portal_conn_webserver_state_chg(request, ZPA_USER_PORTAL_CONN_WEBSERVER_CONNECTING);
    return;

err:
    exporter_user_portal_conn_webserver_rx_internal_error(request);
}


/*
 * called in the context of fohh_thread_x
 */
static void
exporter_user_portal_conn_webserver_dns_async_cb_on_thread(struct zevent_base*      zbase,
                                                           void*                    cookie1,
                                                           int64_t                  int_cookie,
                                                           void*                    cookie2,
                                                           void*                    cookie3,
                                                           void*                    cookie4,
                                                           int64_t                  extra_int_cookie)
{
    struct exporter_request*    request;

    request = cookie1;

    fohh_stats[request->conn->thread->fohh_thread_id]->dns_async_cb_on_thread++;
    exporter_user_portal_conn_webserver_dns_cb(request, DNS_INT_COOKIE_ASYNC, cookie2);
}


/*
 * This is called in the context of exporter_zcdns_thread context. We need to message fohh_thread_y to pick the
 * dns response and restart process of the conn_webserver object.
 *
 * We need to make sure we don't delete the conn_webserver object when we are waiting for this cb.
 */
static void
exporter_user_portal_conn_webserver_dns_async_cb(void*                cb_void_cookie,
                                                 int64_t              cb_int_cookie,
                                                 struct zcdns_result* result)
{
    int                                                         res;
    struct exporter_request*                                    request;

    stats.dns_async_cb_off_thread++;
    request = cb_void_cookie;
    res = fohh_thread_call_big_zevent(request->conn->thread->fohh_thread_id,
            exporter_user_portal_conn_webserver_dns_async_cb_on_thread, cb_void_cookie, cb_int_cookie, result, NULL);
    if (res) {
        stats.dns_async_cb_off_thread_fail++;
        EXPORTER_LOG(AL_ERROR, "Could not thread_call: %s", zpath_result_string(res));
    }
}

/*
 * Now we have DNS info to reach the webserver/CDN.
 * The end goal of this function is to send the request to the webserver
 *
 * This can be called from two thread context,
 * 1. the thread context of request object - when zcdns result is synchronous
 * 2. the exporter_zcdns_thread context - when zcdns result is asynchronous.
 *
 * We are in fohh_thread_y context.
 */
static void
exporter_user_portal_conn_webserver_dns_cb(void*                cb_void_cookie,
                                           int64_t              cb_int_cookie,
                                           struct zcdns_result* result)
{
    struct exporter_request* request;
    char                     addr_str[INET6_ADDRSTRLEN];

    request = cb_void_cookie;
    /* release the lock on request object, that we took when sending the request object to zcdns's land */
    request->ref_count--;
    struct conn_webserver* conn = exporter_user_portal_request_state_get_conn(request->portal_info);
    exporter_user_portal_conn_webserver_state_chg(request, ZPA_USER_PORTAL_CONN_WEBSERVER_DNS_DONE);
    EXPORTER_DEBUG_USER_PORTAL_CONN_WEBSERVER_DETAIL("%s: zcdns came back %s with webserver IP in (%"PRId64") us",
                                              request->name, cb_int_cookie? "sync" : "async",
                                              (epoch_us()- conn->dns_request_start_time_us));
    if (DNS_INT_COOKIE_SYNC == cb_int_cookie) {
        fohh_stats[request->conn->thread->fohh_thread_id]->dns_cb_sync++;
    } else {
        fohh_stats[request->conn->thread->fohh_thread_id]->dns_cb_async++;
    }
    if (!result) {
        fohh_stats[request->conn->thread->fohh_thread_id]->dns_cb_fail_no_result++;
        EXPORTER_LOG(AL_ERROR, "%s: couldn't resolve the webserver to fetch base page", request->name);
        goto err;
    }

    if (result->addr_a) {
        fohh_stats[request->conn->thread->fohh_thread_id]->dns_cb_addr_a++;
        conn->server_sa = (result->addr_a->addr);
        conn->server_sa_len = sizeof(struct sockaddr_in);
        ((struct sockaddr_in *) &(conn->server_sa))->sin_port = htons(DEFAULT_PORTAL_PORT);
        zcdns_sockaddr_storage_to_str(&result->addr_a->addr, addr_str, sizeof(addr_str));
        EXPORTER_DEBUG_USER_PORTAL_CONN_WEBSERVER_DETAIL("%s: dns of webserver resolved to %s", request->name, addr_str);
    } else if (result->addr_aaaa) {
        fohh_stats[request->conn->thread->fohh_thread_id]->dns_cb_addr_aaaa++;
        conn->server_sa = (result->addr_aaaa->addr);
        conn->server_sa_len = sizeof(struct sockaddr_in6);
        ((struct sockaddr_in6 *) &(conn->server_sa))->sin6_port = htons(DEFAULT_PORTAL_PORT);
        zcdns_sockaddr_storage_to_str(&result->addr_aaaa->addr, addr_str, sizeof(addr_str));
        EXPORTER_DEBUG_USER_PORTAL_CONN_WEBSERVER_DETAIL("%s: dns of webserver resolved to %s", request->name, addr_str);
    } else {
        fohh_stats[request->conn->thread->fohh_thread_id]->dns_cb_fail_resolve++;
        EXPORTER_LOG(AL_ERROR, "%s: couldn't resolve the webserver to fetch base page", request->name);
        goto err;
    }

    exporter_user_portal_conn_webserver_connect(request);
    zcdns_result_release(result);
    return;

err:
    if (result) {
        zcdns_result_release(result);
    }
    exporter_user_portal_conn_webserver_state_chg(request, ZPA_USER_PORTAL_CONN_WEBSERVER_DNS_DONE_ERR);
    exporter_request_respond(request, HTTP_STATUS_INTERNAL_SERVER_ERROR, EXPORTER_ERROR_CODE_WEBSERVER_USER_PORTAL_DNS_CALLBACK_FAILED);
    request->http_response_complete = 1;
    exporter_conn_wake_from_other_thread(request->conn);
}


/*
 * This is called from external world - to trigger fetching the portal base page. The 'request' object is already
 * created/set when we are here. The conn_webserver object have to be created here.
 *
 * Request DNS resolution via FOHH_y.
 * The result will be synchronous if zcdns have it in cache, otherwise async.
 *
 * No error at this point in time.
 */

#define DEFAULT_PORTAL_VERSION                        ""

/*
 * This is called from external world - to trigger fetching the portal base page. The 'request' object is already
 * created/set when we are here. The conn_webserver object have to be created here.
 *
 * Request DNS resolution via FOHH_y.
 * The result will be synchronous if zcdns have it in cache, otherwise async.
 *
 * No error at this point in time.
 */

int
exporter_user_portal_conn_webserver(struct exporter_request*    request)
{
    int                         ret;
    struct zcdns_result*        result = NULL;
    struct zpn_user_portal*     user_portal = NULL;
    struct zpn_sra_portal*      sra_portal = NULL;

    EXPORTER_ASSERT_SOFT((fohh_get_current_thread_id() == request->conn->thread->fohh_thread_id),
                         "expected thread(%d) != real thread(%d)", request->conn->thread->fohh_thread_id,
                         fohh_get_current_thread_id());

    struct conn_webserver* conn = exporter_user_portal_conn_webserver_create(request);

    conn->dns_request_start_time_us = epoch_us();

    fohh_stats[request->conn->thread->fohh_thread_id]->world_poked_me++;

    if (request->webserver_type == exporter_conn_webserver_type_portal_page) {
        char *domain = EXPORTER_GET_REQUEST_DOMAIN(request);

        if (request->conn->exporter_domain->is_ot && is_pra_disabled(request->conn->exporter_domain->customer_gid)) {
            EXPORTER_DEBUG_USER_PORTAL_CONN_WEBSERVER_DETAIL("%s: PRA is disabled", request->name);
            exporter_request_respond_with_text(request, HTTP_STATUS_SERVICE_UNAVAILABLE, EXPORTER_ERROR_CODE_PRA_DISABLED, "PRA is not available");
            request->http_response_complete = 1;
            exporter_conn_wake_from_other_thread(request->conn);
            return ZPATH_RESULT_NOT_FOUND;
        }

        if (request->conn->exporter_domain->is_ot) {
            ret = zpn_sra_portal_get_portal_by_domain_immediate(request->conn->exporter_domain->customer_gid,
                                                                domain,
                                                                &sra_portal);
        } else {
            ret = zpn_user_portal_table_get_domain_by_scope(request->conn->exporter_domain->customer_gid,
                                                            request->scope_gid, EXPORTER_GET_REQUEST_DOMAIN(request),
                                                            &user_portal, NULL, NULL, 0);
        }
        if (ret != ZPATH_RESULT_NO_ERROR) {
            EXPORTER_DEBUG_USER_PORTAL_CONN_WEBSERVER_DETAIL("%s: entry not in user portal config(%s)", request->name,
                                                    zpath_result_string(ret));
            if(ret == ZPATH_RESULT_NOT_FOUND && !request->conn->exporter_domain->is_ot) {
                exporter_request_respond(request, HTTP_STATUS_NOT_FOUND, EXPORTER_ERROR_CODE_FAILED_TO_FIND_SRA_PORTAL_OR_USER_PORTAL);
                request->http_response_complete = 1;
                exporter_conn_wake_from_other_thread(request->conn);
            } else {
                exporter_user_portal_conn_webserver_free(request);
            }
            return ret;
        }
        int is_flex_cloud = zpath_is_flex_cloud(ZPATH_LOCAL_CLOUD_NAME);
        const struct zpath_exporter_cloud_config *exporter_config=zpath_get_exporter_config(ZPATH_LOCAL_CLOUD_NAME);
        if((!exporter_config) && (!is_flex_cloud)){
            EXPORTER_DEBUG_USER_PORTAL_CONN_WEBSERVER_DETAIL("%s: portal configuration not available", request->name);
            exporter_user_portal_conn_webserver_free(request);
            return ZPATH_RESULT_ERR;
        }
        conn->url = NULL;
        if (user_portal && user_portal->cdn_url_override) {
            conn->url = EXPORTER_STRDUP(user_portal->cdn_url_override, strlen(user_portal->cdn_url_override));
        } else if (request->conn->exporter_domain->is_ot) {
            if(exporter_config != NULL) {
                conn->url = EXPORTER_STRDUP(exporter_config->pra_portal_hostname, strlen(exporter_config->pra_portal_hostname));
            } else if(is_flex_cloud) {
                char *default_pra_portal_hostname = EXPORTER_CALLOC(strlen(ZPATH_LOCAL_CLOUD_NAME) + strlen("pra.") + 1);
                snprintf(default_pra_portal_hostname, strlen(ZPATH_LOCAL_CLOUD_NAME) + strlen("pra.") + 1, "pra.%s", ZPATH_LOCAL_CLOUD_NAME);
                conn->url = EXPORTER_STRDUP(default_pra_portal_hostname, strlen(ZPATH_LOCAL_CLOUD_NAME) + strlen("pra."));
                EXPORTER_FREE(default_pra_portal_hostname);
            }
        } else {
            if(exporter_config != NULL) {
                conn->url = EXPORTER_STRDUP(exporter_config->portal_hostname, strlen(exporter_config->portal_hostname));
            } else  if (is_flex_cloud) {
                char *default_portal_hostname = EXPORTER_MALLOC(strlen(ZPATH_LOCAL_CLOUD_NAME) + strlen("userportal.") + 1);
                snprintf(default_portal_hostname, strlen(ZPATH_LOCAL_CLOUD_NAME) + strlen("userportal.") + 1, "userportal.%s", ZPATH_LOCAL_CLOUD_NAME);
                conn->url = EXPORTER_STRDUP(default_portal_hostname, strlen(ZPATH_LOCAL_CLOUD_NAME) + strlen("userportal."));
                EXPORTER_FREE(default_portal_hostname);
            }
        }
        if (user_portal && user_portal->cdn_url_version_override) {
            conn->url_version = EXPORTER_STRDUP(user_portal->cdn_url_version_override,
                                                strlen(user_portal->cdn_url_version_override));
        } else {
            if((exporter_config!=NULL) && (exporter_config->portal_version != NULL)) {
                conn->url_version = EXPORTER_STRDUP(exporter_config->portal_version, strlen(exporter_config->portal_version));
            } else if (is_flex_cloud) {
                conn->url_version = EXPORTER_STRDUP(DEFAULT_PORTAL_VERSION, strlen(DEFAULT_PORTAL_VERSION));
            }
        }
    } else if (request->webserver_type == exporter_conn_webserver_type_session_store) {
        char *url = NULL;
        int res = exporter_get_tenant_session_store(request->conn->exporter_domain->customer_gid, &url);
        if (res != ZPATH_RESULT_NO_ERROR || !url) {
            EXPORTER_LOG(AL_ERROR, "[SESS_PROC]: session store url not configured for the tenant%"PRId64, request->conn->exporter_domain->customer_gid);
            exporter_user_portal_conn_webserver_free(request);
            return ZPATH_RESULT_ERR;
        }

        conn->url = NULL;
        exporter_guac_sess_get_domain_from_url(url, &conn->url);

        if (url) {
            EXPORTER_FREE(url);
            url = NULL;
        }
    } else if (request->webserver_type == exporter_conn_webserver_type_pra_service) {
        exporter_get_pra_service_hostname(&conn->url);
    } else if (request->webserver_type == exporter_conn_webserver_type_pra_desktops) {
        exporter_get_pra_djbservice_hostname(&conn->url);
    }

    if (!conn->url) {
        EXPORTER_DEBUG_USER_PORTAL_CONN_WEBSERVER_DETAIL("%s: connection url is null", request->name);
        exporter_user_portal_conn_webserver_free(request);
        return ZPATH_RESULT_ERR;
    }

    EXPORTER_DEBUG_USER_PORTAL_CONN_WEBSERVER_DETAIL("%s: Requesting zcdns to resolve portal base page url (%s)",
                                              request->name, conn->url);

    /*
     * take a refernce count on request object as we are sending sending the request pointer as cookie to
     * exporter_zcdns_thread and we want to make sure that pointer is valid when it comes back.
     */
    request->ref_count++;
    conn->dns_request = zcdns_resolve(exporter_zcdns_hdl, conn->url, 1, 1, 0, &result,
                               exporter_user_portal_conn_webserver_dns_async_cb, request, DNS_INT_COOKIE_ASYNC);

    exporter_user_portal_conn_webserver_state_chg(request, ZPA_USER_PORTAL_CONN_WEBSERVER_DNS_REQUESTED);
    if (!conn->dns_request && result) {
        /* Immediate result */
        exporter_user_portal_conn_webserver_dns_cb(request, DNS_INT_COOKIE_SYNC, result);
    }

    return ZPATH_RESULT_NO_ERROR;
}


/*
 * Called in the context of init_thread.
 */
int
exporter_user_portal_conn_webserver_init()
{
    stats.init++;

    if (!(exporter_user_portal_conn_webserver_stats_description =
                  argo_register_global_structure(EXPORTER_USER_PORTAL_CONN_WEBSERVER_STATS_S_HELPER))) {
        return ZPATH_RESULT_ERR;
    }

    if (!(exporter_user_portal_conn_webserver_fohh_stats_description =
                  argo_register_global_structure(EXPORTER_USER_PORTAL_CONN_WEBSERVER_FOHH_STATS_S_HELPER))) {
        return ZPATH_RESULT_ERR;
    }

    if (!(exporter_user_portal_conn_webserver_audit_description =
                  argo_register_global_structure(EXPORTER_USER_PORTAL_CONN_WEBSERVER_AUDIT_S_HELPER))) {
        return ZPATH_RESULT_ERR;
    }

    conn_webserver_ssl_ctx = fohh_web_client_ssl_ctx_create();
    if (!conn_webserver_ssl_ctx) {
        stats.init_fail_no_ssl_ctx++;
        EXPORTER_LOG(AL_ERROR, "Cannot get SSL context to establish connection with user portal CDN, user portal requests can't be served");
        return ZPATH_RESULT_ERR;
    }

    const char *root_cert_file_name = ZPATH_LOCAL_ROOT_CERTIFICATE_FILE;
    const char *my_cert_file_name = ZPATH_LOCAL_PUBLIC_CERTIFICATE_FILE;
    const char *my_cert_key_file_name = ZPATH_LOCAL_PRIVATE_KEY_FILE;

    if (root_cert_file_name && my_cert_file_name && my_cert_key_file_name) {
        conn_http_proxy_server_ssl_ctx = fohh_web_client_ssl_ctx_create_with_cert(root_cert_file_name, my_cert_file_name, my_cert_key_file_name, NULL);
        if (!conn_http_proxy_server_ssl_ctx) {
            stats.init_fail_no_ssl_ctx++;
            EXPORTER_LOG(AL_ERROR, "Cannot get SSL context to establish connection with session store, session store https requests can't be served");
            return ZPATH_RESULT_ERR;
        }
    }

    /*
     * Set up stats object for the fohh threads to operate upon.
     */
    {
        int curr_fohh_thread;
        int max_fohh_thread = fohh_thread_count();
        for (curr_fohh_thread = 0; curr_fohh_thread < max_fohh_thread; curr_fohh_thread++) {
            fohh_stats[curr_fohh_thread] = EXPORTER_CALLOC(sizeof(struct exporter_user_portal_conn_webserver_fohh_stats_s));
            stats.init_fohh_stats++;
        }
    }

    stats.init_done++;

    return ZPATH_RESULT_NO_ERROR;
}


void
exporter_user_portal_conn_webserver_stats_dump(struct zpath_debug_state *request_state)
{
    char        jsonout[10000];

    if (ARGO_RESULT_NO_ERROR ==
        argo_structure_dump(exporter_user_portal_conn_webserver_audit_description, &audit, jsonout, sizeof(jsonout), NULL, 1)){
        ZDP("%s\n", jsonout);
    }
    if (ARGO_RESULT_NO_ERROR ==
        argo_structure_dump(exporter_user_portal_conn_webserver_stats_description, &stats, jsonout, sizeof(jsonout), NULL,
                            1)){
        ZDP("%s\n", jsonout);
    }

    {
        int curr_fohh_thread;
        int max_fohh_thread = fohh_thread_count();
        for (curr_fohh_thread = 0; curr_fohh_thread < max_fohh_thread; curr_fohh_thread++) {
            if (ARGO_RESULT_NO_ERROR == argo_structure_dump(exporter_user_portal_conn_webserver_fohh_stats_description,
                    fohh_stats[curr_fohh_thread], jsonout, sizeof(jsonout), NULL, 1)) {
                ZDP("%s\n", jsonout);
            }
        }
    }

}


void
exporter_user_portal_conn_webserver_stats_clear()
{
    bzero(&stats, sizeof(stats));
    {
        int curr_fohh_thread;
        int max_fohh_thread = fohh_thread_count();
        for (curr_fohh_thread = 0; curr_fohh_thread < max_fohh_thread; curr_fohh_thread++) {
            bzero(fohh_stats[curr_fohh_thread], sizeof(*fohh_stats[curr_fohh_thread]));
        }
    }
    __sync_add_and_fetch_8(&audit.stats_cleared, 1);
}
