/*
 * exporter_zpa.c. Copyright (C) 2018 Zscaler Inc. All Rights Reserved
 */

#include "zpath_lib/zpath_local.h"

#include "exporter/exporter.h"
#include "exporter/exporter_request.h"
#include "exporter/exporter_zpa.h"
#include "exporter/exporter_conn.h"
#include "exporter/exporter_guac_api.h"
#include "exporter/exporter_guacd.h"
#include "zpn/zpn_scope.h"

struct cb_data {
    char *reason;
    char *mtunnel_id;
};

static int exporter_request_generate_new_header(struct exporter_request *request)
{
    int i;
    char remote_ip_str[ARGO_INET_ADDRSTRLEN] = {0};
    char local_ip_str[ARGO_INET_ADDRSTRLEN] = {0};
    int64_t customer_gid = request->orig_customer_gid;
    int updated_via = 0;
    int updated_xff = 0;
    int updated_forwarded = 0;

    argo_inet_generate(remote_ip_str, &(request->conn->remote_ip));
    argo_inet_generate(local_ip_str, &(request->conn->local_ip));

    if (!request->new_header) {
        request->new_header = evbuffer_new();
        if (!request->new_header) {
            EXPORTER_LOG(AL_CRITICAL, "Fail to allocate buffer for new request header");
            return ZPATH_RESULT_ERR;
        }
    } else {
        /* Do nothing */
        return ZPATH_RESULT_NO_ERROR;
    }

    /* Generate the new http request header */
    evbuffer_add_printf(request->new_header,
                        "%s %s HTTP/%d.%d\r\n",
                        http_method_names[request->req_method],
                        zmicro_heap_ref(&request->heap, request->url_ofs),
                        request->req_http_major,
                        request->req_http_minor);

    for (i = 0; i < request->total_headers; i++) {
        /* Update Via Header if possible */
        if (strcmp(zmicro_heap_ref(&request->heap, request->header_name_ofs[i]), "Via") == 0) {
            evbuffer_add_printf(request->new_header,
                                "%s: %s, %s-%s\r\n",
                                zmicro_heap_ref(&request->heap, request->header_name_ofs[i]),
                                zmicro_heap_ref(&request->heap, request->header_value_ofs[i]),
                                EXPORTER_VIA_STRING,
                                EXPORTER_DOMAIN_AUTH);
            updated_via = 1;
        } else if((strcmp(zmicro_heap_ref(&request->heap, request->header_name_ofs[i]), "X-Forwarded-For") == 0) &&
                   (exporter_xff_header_enabled_for_customer(customer_gid))) {
			evbuffer_add_printf(request->new_header,
							"%s: %s, %s\r\n",
							zmicro_heap_ref(&request->heap, request->header_name_ofs[i]),
							zmicro_heap_ref(&request->heap, request->header_value_ofs[i]),
							local_ip_str);
            updated_xff = 1;
        } else if((strcmp(zmicro_heap_ref(&request->heap, request->header_name_ofs[i]), "Forwarded") == 0) &&
                   (exporter_forwarded_header_enabled_for_customer(customer_gid))) {
            if (request->conn->local_ip.length == 4) {
                evbuffer_add_printf(request->new_header,
                                "%s: %s, for=%s\r\n",
                                zmicro_heap_ref(&request->heap, request->header_name_ofs[i]),
                                zmicro_heap_ref(&request->heap, request->header_value_ofs[i]),
                                local_ip_str);
		    } else {
               evbuffer_add_printf(request->new_header,
                               "%s: %s, for=\"[%s]\"\r\n",
                               zmicro_heap_ref(&request->heap, request->header_name_ofs[i]),
                               zmicro_heap_ref(&request->heap, request->header_value_ofs[i]),
                               local_ip_str);
            }
            updated_forwarded = 1;
        } else if(strcmp(zmicro_heap_ref(&request->heap, request->header_name_ofs[i]), "Host") == 0) {
            /* unified-portal - Change host header */
            /* Host: grafana.devbox.com:443 */
            if (request->conn->exporter_domain->is_managed_ba) {
                if (request->conn->exporter_domain->cfg_domain) {
                    EXPORTER_DEBUG_LOG(1, "%s: [UNIP_LOG] Generated host header ext_domain: %s, int_app: %s", request->name,
                            request->conn->exporter_domain->domain, request->conn->exporter_domain->cfg_domain);
                    evbuffer_add_printf(request->new_header,
                            "%s: %s:%d\r\n",
                            zmicro_heap_ref(&request->heap, request->header_name_ofs[i]),
                            request->conn->exporter_domain->cfg_domain,
                            request->conn->exporter_domain->application_port_he);
                }
            } else {
                evbuffer_add_printf(request->new_header,
                        "%s: %s\r\n",
                        zmicro_heap_ref(&request->heap, request->header_name_ofs[i]),
                        zmicro_heap_ref(&request->heap, request->header_value_ofs[i]));
            }
        } else if(strcmp(zmicro_heap_ref(&request->heap, request->header_name_ofs[i]), "Origin") == 0) {
            /* unified-portal - Change origin header */
            /* Origin: http://grafana.devbox.com:443 */
            //TODO - Handle null origin
            if (request->conn->exporter_domain->is_managed_ba) {
                if (request->conn->exporter_domain->cfg_domain) {
                    EXPORTER_DEBUG_LOG(1, "%s: [UNIP_LOG] Generated origin header ext_domain: %s, int_app: %s", request->name,
                            request->conn->exporter_domain->domain, request->conn->exporter_domain->cfg_domain);
                    evbuffer_add_printf(request->new_header,
                            "%s: %s://%s:%d\r\n",
                            zmicro_heap_ref(&request->heap, request->header_name_ofs[i]),
                            request->conn->exporter_domain->is_backend_tls ? "https": "http",
                            request->conn->exporter_domain->cfg_domain,
                            request->conn->exporter_domain->application_port_he);
                }
            } else {
               evbuffer_add_printf(request->new_header,
                        "%s: %s\r\n",
                        zmicro_heap_ref(&request->heap, request->header_name_ofs[i]),
                        zmicro_heap_ref(&request->heap, request->header_value_ofs[i]));
            }
        } else {
            evbuffer_add_printf(request->new_header,
                                "%s: %s\r\n",
                                zmicro_heap_ref(&request->heap, request->header_name_ofs[i]),
                                zmicro_heap_ref(&request->heap, request->header_value_ofs[i]));
        }
    }

    if (!updated_via) {
        evbuffer_add_printf(request->new_header,
                            "Via: %s-%s\r\n",
                            EXPORTER_VIA_STRING,
                            EXPORTER_DOMAIN_AUTH);
        updated_via = 1;
    }

	if (exporter_xff_header_enabled_for_customer(customer_gid) && !updated_xff) {
        evbuffer_add_printf(request->new_header, "X-Forwarded-For: %s, %s\r\n", remote_ip_str, local_ip_str);
        updated_xff = 1;
    }

	if (exporter_forwarded_header_enabled_for_customer(customer_gid) && !updated_forwarded) {
        if ((request->conn->local_ip.length == 4) && (request->conn->remote_ip.length == 4)) {
            evbuffer_add_printf(request->new_header, "Forwarded: for=%s, for=%s\r\n", remote_ip_str, local_ip_str);
        } else if ((request->conn->local_ip.length == 16) && (request->conn->remote_ip.length == 16)) {
            evbuffer_add_printf(request->new_header, "Forwarded: for=\"[%s]\", for=\"[%s]\"\r\n", remote_ip_str, local_ip_str);
        } else if (request->conn->local_ip.length == 16) {
            evbuffer_add_printf(request->new_header, "Forwarded: for=%s, for=\"[%s]\"\r\n", remote_ip_str, local_ip_str);
        } else {
            evbuffer_add_printf(request->new_header, "Forwarded: for=\"[%s]\", for=%s\r\n", remote_ip_str, local_ip_str);
        }
        updated_forwarded = 1;
    }

    evbuffer_add_printf(request->new_header, "Strict-Transport-Security: max-age=" MAX_AGE_HSTS "; includeSubDomains; preload\r\n");

    evbuffer_add_printf(request->new_header, "\r\n");

    /* Print out the generate header */
    if (exporter_debug & EXPORTER_DEBUG_HTTP_BIT) {
        char buffer[1024*8];

        memset(buffer, 0, sizeof(buffer));
        evbuffer_copyout(request->new_header, buffer, sizeof(buffer));
        EXPORTER_DEBUG_HTTP("%s: Generated header.",  request->name);
    }

    return ZPATH_RESULT_NO_ERROR;
}

static void exporter_request_send_data_to_guacd_service(struct exporter_request *request)
{
    /* zfce_mt_consume from the guacd child proc conn when mt is in connected state
     * When mtunnel gets connected, process pending read and write messages (if any)
     * between proxy and guacd child. */
    exporter_guacd_proxy_wake_on_request_thread(request, 0);

    /* Send guac instructions held in request_body (received from browser) to guacd service */
    if (request->guac_info && request->guac_info->gd_proxy.gd_proxy_bev) {
        struct evbuffer  *output_buffer = bufferevent_get_output(request->guac_info->gd_proxy.gd_proxy_bev);
        int enq_len = 0;
        if (request->guac_info->gd_proxy.gd_proxy_request &&
            evbuffer_get_length(request->guac_info->gd_proxy.gd_proxy_request)) {
            enq_len = evbuffer_remove_buffer(request->guac_info->gd_proxy.gd_proxy_request,
                                                     output_buffer,
                                                     evbuffer_get_length(request->guac_info->gd_proxy.gd_proxy_request));
            EXPORTER_DEBUG_GUACD("DBG_GUACD %s Sent %d bytes of handshake from exporter server proxy to guacd server", request->name, enq_len);
        }
        if (request->request_body && evbuffer_get_length(request->request_body)) {
            enq_len = evbuffer_remove_buffer(request->request_body,
                                                     output_buffer,
                                                     evbuffer_get_length(request->request_body));
            EXPORTER_DEBUG_GUACD("DBG_GUACD %s Sent %d bytes of browser data from exporter server proxy to guacd server", request->name, enq_len);
        }
    } else {
        EXPORTER_DEBUG_GUACD("DBG_GUACD %s Missing gd_proxy_bev for guacd server conn", request->name);
    }

}

static void exporter_resend_diagostic_info(struct exporter_request *request)
{
    if (!request->guac_info) {
        return;
    }

    if (!request->guac_info->diag_info.is_diag_info_consumed) {
        if (!request->mt->capabilities_policy_id) {
            request->mt->capabilities_policy_id = request->guac_info->diag_info.capabilities_policy_id;
        }
        if (!request->mt->console_cred_type) {
            request->mt->console_cred_type = request->guac_info->diag_info.console_cred_type;
        }
        if (!request->mt->cred_policy_id) {
            request->mt->cred_policy_id = request->guac_info->diag_info.cred_policy_id;
        }
        if (!request->mt->credential_id && request->guac_info->diag_info.credential_id) {
            request->mt->credential_id = EXPORTER_STRDUP(request->guac_info->diag_info.credential_id, strlen(request->guac_info->diag_info.credential_id));
        }
        if (!request->mt->pra_conn_id && request->guac_info->guac_parser.guac_tunnel_params.conn_id) {
            request->mt->pra_conn_id = EXPORTER_STRDUP(request->guac_info->guac_parser.guac_tunnel_params.conn_id,
                                            strlen(request->guac_info->guac_parser.guac_tunnel_params.conn_id));
        }
        if (!request->mt->console_user && request->guac_info->diag_info.console_user) {
            request->mt->console_user = EXPORTER_STRDUP(request->guac_info->diag_info.console_user,
                                            strlen(request->guac_info->diag_info.console_user));
        }
        if (!request->mt->console_conn_type && request->sra_host_protocol) {
            request->mt->console_conn_type = EXPORTER_STRDUP(request->sra_host_protocol, strlen(request->sra_host_protocol));
        }

        EXPORTER_LOG(AL_INFO,
            "Sending mt diagnostic info %s, capabilities_policy_id %"PRId64", console_cred_type %u,"
                     "cred_policy_id %"PRId64", credential_id %"PRId64", console_user %s, console_conn_type %s",
            request->name,
            request->mt->capabilities_policy_id,
            request->mt->console_cred_type,
            request->mt->cred_policy_id,
            request->guac_info->credential_id,
            request->mt->console_user,
            request->mt->console_conn_type);

        zpn_fohh_client_exporter_send_exporter_log_data(request->mt);

        request->guac_info->diag_info.is_diag_info_consumed = 1;
    }
}

int exporter_request_send_data(struct exporter_request *request)
{
    int res = ZPATH_RESULT_NO_ERROR;
    char* mtunnel_id = (request && request->mt && request->mt->mtunnel_id) ? request->mt->mtunnel_id : "<unknown>";

    if (request) EXPORTER_DEBUG_HTTP("%s: exporter_request_send_data()", request->name);

    if (request && request->mt && (request->mt->status == zfce_mt_connected)) {

        if (request->mt && !request->log.mtunnel_id) {
            request->log.mtunnel_id = EXPORTER_STRDUP(request->mt->mtunnel_id, strlen(request->mt->mtunnel_id));
            mtunnel_id = request->mt->mtunnel_id;
        }

        if (!request->http_request_generated) {
            res = exporter_request_generate_new_header(request);
            if (res) {
                EXPORTER_LOG(AL_CRITICAL, "Implement me");
            }
            request->http_request_generated = 1;
        }

        EXPORTER_DEBUG_HTTP("%s: mtunnel_id = %s  Try to send request ...", request->name, mtunnel_id);

        if (request->new_header) {
            EXPORTER_DEBUG_HTTP("%s: mtunnel_id = %s  Has header ...", request->name, mtunnel_id);

            if (!request->log.req_tx_start_us) {
                request->log.req_tx_start_us = epoch_us();
            }

            if (evbuffer_get_length(request->new_header)) {
                EXPORTER_DEBUG_HTTP("%s: mtunnel_id = %s, Sending header with %zu bytes ...", request->name, mtunnel_id, evbuffer_get_length(request->new_header));
                res = zfce_mt_consume(request->mt,
                                      request->new_header,
                                      evbuffer_get_length(request->new_header),
                                      request->mt_incarnation);
                if (res) {
                    EXPORTER_DEBUG_HTTP("%s: mtunnel_id = %s  Send new header returned %s", request->name, mtunnel_id, zpath_result_string(res));
                    return res;
                }
            }

            if (!evbuffer_get_length(request->new_header)) {
                evbuffer_free(request->new_header);
                request->new_header = NULL;
            }
        }

        if (request->request_body && (!request->guac_info || !request->guac_info->guacd_service)) {
            EXPORTER_DEBUG_HTTP("%s: mtunnel_id = %s  Has body ...", request->name, mtunnel_id);
            if (evbuffer_get_length(request->request_body)) {
                EXPORTER_DEBUG_HTTP("%s: mtunnel_id = %s, Sending body with %zu bytes ...", request->name,  mtunnel_id, evbuffer_get_length(request->request_body));
                if (request->is_proxy_conn) {
                    EXPORTER_DEBUG_SESSION_SHARING("SESS_PROC %s %s Proxy req Trying to send data len %zu on mtunnel ", request->name,  mtunnel_id, evbuffer_get_length(request->request_body));
                }
                res = zfce_mt_consume(request->mt,
                                      request->request_body,
                                      evbuffer_get_length(request->request_body),
                                      request->mt_incarnation);
                if (res) {
                    EXPORTER_DEBUG_HTTP("%s: mtunnel_id = %s  Send body returned %s", request->name, mtunnel_id, zpath_result_string(res));
                    return res;
                }
            }
        }//request_body

        if (request->guac_info && request->guac_info->guacd_service) {
             exporter_request_send_data_to_guacd_service(request);
        }

        if (request->http_request_complete && (!request->request_body || !evbuffer_get_length(request->request_body))) {
            /* We have finished sending the request */
            request->log.req_tx_done_us = epoch_us();
        }
    } else if (request && request->is_proxy_conn && request->is_guac_sess_primary_exporter) {
        EXPORTER_DEBUG_SESSION_SHARING("SESS_PROC %s: Sending data to guacd service in proxy context", request->name);
        if (request->guac_info && request->guac_info->guacd_service) {
            exporter_request_send_data_to_guacd_service(request);
        }
        if (request->http_request_complete && (!request->request_body || !evbuffer_get_length(request->request_body))) {
            /* We have finished sending the request */
            request->log.req_tx_done_us = epoch_us();
        }
    } else {
        /*
         * POST will always have request body and this data should not goto mtunnel
         * /fromsp does async write to objstore
         * If there is payload we come here and do nothing and return
         */
        if (request)
            EXPORTER_DEBUG_CSP("%s: mtunnel not connected url: [%s] req_state: [%s %d%d] async_state: [%s] cntlen: [%d]",
                request->name,
                request->log.url,
                exporter_request_input_state_get_str(request),
                request->http_request_complete, request->http_response_complete,
                exporter_request_async_state_get_str(request->async_state),
                request->request_body ? (int)evbuffer_get_length(request->request_body): -1);
    }

    return res;
}

/* This call is on fohh connection thread on ZPA side */
static int exporter_zpa_app_query_ack_callback(int64_t query_id,
                                               int64_t customer_id,
                                               char *assertion_key,
                                               char *host_name,
                                               int proto,
                                               uint16_t port,
                                               int tls,
                                               int64_t publish_gid,
                                               const char *result,
                                               const char *reason)
{
    EXPORTER_LOG(AL_DEBUG, "exporter_zpa_app_query_ack_callback(), query_id = %ld, customer_gid = %ld, host = %s, proto = %d, port = %d, tls = %d, result = %s, reason = %s",
                 (long)query_id, (long)customer_id, host_name, proto, port, tls, result ? result : "NULL", reason ? reason : "Unknown");
    return ZPATH_RESULT_NO_ERROR;
}

static int exporter_request_mt_status_callback_on_thread(struct exporter_request *request, void *data, int64_t int_data)
{
    struct cb_data *cb_data = data;
    enum zpn_fohh_client_exporter_mt_status status = int_data;
    char *error = NULL;
    char *reason = cb_data ? cb_data->reason : NULL;
    char *mtunnel_id = cb_data ? cb_data->mtunnel_id : NULL;

    if (cb_data) EXPORTER_FREE(cb_data);

    if (!request) {
        EXPORTER_DEBUG_HTTP("No request??");
        if (reason) EXPORTER_FREE(reason);
        if (mtunnel_id) EXPORTER_FREE(mtunnel_id);
        return ZPATH_RESULT_ERR;
    }

    if (mtunnel_id) {
        if (request->log.mtunnel_id) {
            EXPORTER_FREE(request->log.mtunnel_id);
        }
        request->log.mtunnel_id = mtunnel_id;
    }

    EXPORTER_DEBUG_HTTP("%s: mtunnel_id = %s, ZPN Fohh client exporter mtunnel status now %s, reason = %s", request->name,  mtunnel_id ? mtunnel_id : "<unknown>", zfce_mt_status_string(status), reason ? reason : "null");

    if (status == zfce_mt_release_request) {

        request->mt_release_received = 1;

        if (request->mt) {
            /* Normal - we remove the MT now */
            ZPATH_MUTEX_LOCK(&(request->mt->lock), __FILE__, __LINE__);
            EXPORTER_LOG(AL_DEBUG, "Unattaching request: %s from mtunnel: %s", request->name, request->mt->dbg_str);
            snprintf(request->mt->dbg_str, sizeof(request->mt->dbg_str), "Unattached");
            ZPATH_MUTEX_UNLOCK(&(request->mt->lock), __FILE__, __LINE__);
            request->mt = NULL;
        } else {
            /* Abnormal - It is already gone!? */
            EXPORTER_LOG(AL_ERROR, "%s: mtunnel_id = %s  ZPN Fohh client exporter mtunnel release when already released", request->name, mtunnel_id ? mtunnel_id : "<unknown>");
        }

        exporter_conn_wake(request->conn);

        if (reason) {
            EXPORTER_FREE(reason);
        }

        return ZPATH_RESULT_NO_ERROR;
    }

    /* Save the status and reason */
    request->log.mt_status = zfce_mt_status_string(status);
    if (request->log.mt_reason) {
        EXPORTER_FREE(request->log.mt_reason);
        request->log.mt_reason = NULL;
    }
    if (reason) {
        char *found = NULL;

        request->log.mt_reason = EXPORTER_STRDUP(reason, strlen(reason));

        /* strip off ',' if there is one */
        found = strchr(reason, ',');
        if (found) {
            size_t len;

            len = found - reason;
            error = EXPORTER_CALLOC(len + 1);
            memcpy(error, reason, len);
        } else {
            error = EXPORTER_STRDUP(reason, strlen(reason));
        }
    }

    if (status == zfce_mt_connected) {
        exporter_session_recording_send_start_rpc(request);
        exporter_resend_diagostic_info(request);
        exporter_request_send_data(request);
    } else {

        /* For ALL cases here, it's as though we completed an mt_release_request */
        if (request->mt) {
            ZPATH_MUTEX_LOCK(&(request->mt->lock), __FILE__, __LINE__);
            EXPORTER_LOG(AL_DEBUG, "Unattaching request: %s from mtunnel: %s", request->name, request->mt->dbg_str);
            snprintf(request->mt->dbg_str, sizeof(request->mt->dbg_str), "Unattached");
            ZPATH_MUTEX_UNLOCK(&(request->mt->lock), __FILE__, __LINE__);
        } else {
            EXPORTER_LOG(AL_ERROR, "%s: mtunnel_id = %s  ZPN Fohh client exporter mtunnel is NULL", request->name, mtunnel_id ? mtunnel_id : "<unknown>");
        }

        request->mt_release_received = 1;

        if (status == zfce_mt_connect_error) {
            EXPORTER_DEBUG_HTTP("%s: mtunnel_id = %s  Exporter mtunnel setup error, should release from the request: %s", request->name, mtunnel_id ? mtunnel_id : "<unknown>", reason ? reason : "null");
            if (error && ((strcmp(error, "BRK_MT_SETUP_FAIL_SAML_EXPIRED") == 0) || strcmp(error, "EXPTR_MT_TERMINATED_TIMEOUT") == 0)) {
                exporter_request_enter_state(request, async_state_refresh_auth);
            } else if (error && ((strcmp(error, "BRK_MT_SETUP_FAIL_REAUTH_WITH_AL") == 0) || strcmp(error, "BRK_MT_SETUP_FAIL_REAUTH_EXPIRED_WITH_AL") == 0)) {
                exporter_request_enter_state(request, async_state_refresh_auth_levelid);
            } else {
                exporter_request_enter_state(request, async_state_mt_connection_err);
            }
        } else if (status == zfce_mt_remote_disconnect)  {
            EXPORTER_DEBUG_HTTP("%s: mtunnel_id = %s   Exporter mtunnel remote disconnect, should release from the request", request->name, mtunnel_id ? mtunnel_id : "<unknown>");
            if (error && ((strcmp(error, "BRK_MT_SETUP_FAIL_SAML_EXPIRED") == 0) || strcmp(error, "EXPTR_MT_TERMINATED_TIMEOUT") == 0)) {
                exporter_request_enter_state(request, async_state_refresh_auth);
            } else if (error && (strcmp(error,"MT_CLOSED_TERMINATED") == 0)){
                exporter_request_enter_state(request, async_state_close_client_conn);
            }else {
                exporter_request_enter_state(request, async_state_mt_remote_disconnect);
            }
        } else {
          if (error && ((strcmp(error, "BRK_MT_SETUP_FAIL_SAML_EXPIRED") == 0) || strcmp(error, "EXPTR_MT_TERMINATED_TIMEOUT") == 0)) {
                exporter_request_enter_state(request, async_state_refresh_auth);
            } else {
                EXPORTER_DEBUG_HTTP("%s: mtunnel_id = %s  Status callback with no effect? This is usually a hard error.", request->name, mtunnel_id ? mtunnel_id : "<unknown>");
                exporter_request_enter_state(request, async_state_hard_err);
            }
        }
    }

    if (error) {
        EXPORTER_FREE(error);
    }

    if (reason) {
        EXPORTER_FREE(reason);
    }

    return ZPATH_RESULT_NO_ERROR;
}

/*
 * This request can come in on the wrong thead. In many cases we need
 * to get back on the right thread.
 */
static int exporter_request_mt_status_callback(struct zpn_fohh_client_exporter_mt *mt,
                                               void *cookie_void,
                                               int64_t cookie_int,
                                               enum zpn_fohh_client_exporter_mt_status status,
                                               const char *reason)
{
    struct exporter_request *request = cookie_void;
    int res;

    struct cb_data *data = EXPORTER_CALLOC(sizeof(*data));

    if (reason) data->reason = EXPORTER_STRDUP(reason, strlen(reason));
    if (mt->mtunnel_id) {
        EXPORTER_LOG(AL_DEBUG, "%s: Received mt status callback with mtunnel id %s", request->name, mt->mtunnel_id);
        data->mtunnel_id = EXPORTER_STRDUP(mt->mtunnel_id, strlen(mt->mtunnel_id));
    } else {
        EXPORTER_LOG(AL_DEBUG, "%s: Received mt status callback without mtunnel id", request->name);
    }

    /* We make a copy of reason, because we don't know how volatile it is */
    res = exporter_request_on_thread(exporter_request_mt_status_callback_on_thread, request, data, status);
    if (res) {
        EXPORTER_LOG(AL_ERROR, "%s: Received %s trying to do status callback", request->name, zpath_result_string(res));
        if (data) {
            if (data->reason) EXPORTER_FREE(data->reason);
            if (data->mtunnel_id) EXPORTER_FREE(data->mtunnel_id);
            EXPORTER_FREE(data);
        }
    }
    return res;
}

/*
 * This occurs in the context of the ZPA f_conn thread. We enqueue the
 * data here, but we trigger waking and output processing on the other
 * side rather than calling it directly.
 */
static int exporter_request_mt_consume_callback(struct zpn_fohh_client_exporter_mt *mt,
                                                struct evbuffer *buf,
                                                size_t len,
                                                void *cookie_void,
                                                int64_t cookie_int)
{
    struct exporter_request *request = cookie_void;
    struct evbuffer *out_buf = NULL;
    char *mtunnel_id = mt->mtunnel_id ? mt->mtunnel_id : "<unknown>";

    EXPORTER_DEBUG_HTTP("%s: mtunnel_id = %s, ZPN Fohh client exporter mtunnel send us %d bytes of data", request->name, mtunnel_id, (int)len);

    ZPATH_MUTEX_LOCK(&(request->lock), __FILE__, __LINE__);
    if (!request->response_data) {
        request->response_data = evbuffer_new();
        request->log.rsp_rx_start_us = epoch_us();
    }

    if (evbuffer_get_length(request->response_data) > HTTP_RESPONSE_MAX_BUFFER_DATA) {
        /* We have buffered more than 64K (HTTP_RESPONSE_MAX_BUFFER_DATA)  bytes, start back pressuring */
        request->need_to_unblock_source = 1;
        ZPATH_MUTEX_UNLOCK(&(request->lock), __FILE__, __LINE__);
        return ZPATH_RESULT_WOULD_BLOCK;
    }

    if (!request->guac_info || !request->guac_info->guacd_service) {
        evbuffer_remove_buffer(buf, request->response_data, len);

    /* We remember if we have sent any data to the client because that
     * affects how we close connections when they error (mid-stream
     * vs. not) */
        request->data_sent_to_client = 1;
    } else {
        if (request->is_proxy_conn) {
            if (!request->guac_info->gd_remote_server_proxy.remote_proxy_bev) {
                request->need_to_unblock_source = 1;
                EXPORTER_DEBUG_SESSION_SHARING("SESS_PROC %s Guacd local bev for remote server proxy connection not available", request->name);
                ZPATH_MUTEX_UNLOCK(&(request->lock), __FILE__, __LINE__);
                return ZPATH_RESULT_WOULD_BLOCK;
            }
            out_buf = bufferevent_get_output(request->guac_info->gd_remote_server_proxy.remote_proxy_bev);
        } else {
            if (!request->guac_info->gd_proxy.guacd_childproc_data) {
                request->need_to_unblock_source = 1;
                EXPORTER_DEBUG_GUACD("DBG_GUACD %s Guacd child proc connection not available", request->name);
                ZPATH_MUTEX_UNLOCK(&(request->lock), __FILE__, __LINE__);
                return ZPATH_RESULT_WOULD_BLOCK;
            }
            // send to guacd child proc
            if (!request->guac_info->gd_proxy.cookie || (request->guac_info->gd_proxy.cookie != mt)) {
                out_buf = bufferevent_get_output(request->guac_info->gd_proxy.guacd_childproc_data->child_proc_bev);
                EXPORTER_DEBUG_GUACD("DBG_GUACD %s Send to protocol conn of guacd child", request->name);
            } else if (request->guac_info->gd_proxy.guacd_childproc_sftp &&
                       request->guac_info->gd_proxy.guacd_childproc_sftp->child_proc_bev) { /* Send to sftp conn if there is a valid gd_proxy.guacd_childproc_sftp */
                out_buf = bufferevent_get_output(request->guac_info->gd_proxy.guacd_childproc_sftp->child_proc_bev);
                EXPORTER_DEBUG_GUACD("DBG_GUACD %s Send to SFTP conn of Guacd child", request->name);
            }
        }
        if (!out_buf) {
            EXPORTER_DEBUG_GUACD("DBG_GUACD %s Cannot send as out buf is missing", request->name);
        } else {
            len = evbuffer_remove_buffer(buf, out_buf, len);
            EXPORTER_DEBUG_GUACD("DBG_GUACD %s Sending %zu bytes from mtunnel to guacd child/remote server proxy conn", request->name, len);
            EXPORTER_DEBUG_SESSION_SHARING("SESS_PROC %s Sending %zu bytes from mtunnel to guacd child/remote server proxy conn", request->name, len);
            request->data_sent_to_client = 1;
        }
    }
    ZPATH_MUTEX_UNLOCK(&(request->lock), __FILE__, __LINE__);

    return exporter_conn_wake_from_other_thread(request->conn);
}

static int exporter_request_mt_unblock_callback(struct zpn_fohh_client_exporter_mt *mt,
                                                void *cookie_void,
                                                int64_t cookie_int)
{
    struct exporter_request *request = cookie_void;
    char *mtunnel_id = mt->mtunnel_id ? mt->mtunnel_id : "<unknown>";

    EXPORTER_DEBUG_HTTP("%s: mtunnel_id = %s ZPN Fohh client exporter mtunnel tell us to resume sending", request->name, mtunnel_id);

    exporter_guacd_proxy_wake_on_request_thread(request, 1);
    return exporter_conn_wake_from_other_thread(request->conn);
}

static struct zpn_http_trans_log *exporter_request_mt_trans_log_callback(void *cookie_void, int64_t cookie_int)
{
    struct exporter_request *request = cookie_void;

    return &(request->log);
}


int exporter_zpa_request(struct exporter_request *request, const char *auth_cookie, const char *assertion, int is_no_auth)
{
    struct zpn_fohh_client_exporter *zfce = NULL;
    struct zpn_fohh_client_exporter_mt *mt = NULL;
    char fake_no_auth_cookie[256];
    const char *req_auth_cookie;
    const char *req_assertion;
    int is_guac_mt = 0;
    int is_pra_mt = 0;
    int is_guac_sftp_mt = 0;
    int32_t conn_id = 0;
    uint16_t port = 0;
    const char *domain = NULL;
    char lookup_id[1024] = {0};
    enum zpn_server_type remote_server_type = zpn_server_type_broker;
    int64_t pra_scope_gid = 0;
    int is_pra_third_party_login = 0;
    char *host_name = NULL;

    EXPORTER_OBJ_VALID(request, __FILE__, __LINE__)
    EXPORTER_DEBUG_SESSION_SHARING("SESS_PROC %s: Handle request for domain = %s, local_port = %d, application_port = %d, customer_id = %ld, is_tls = %d, is_no_auth = %d",
                 request->name, request->conn->exporter_domain->domain, request->conn->local_port_he,
                 (port) ? (int)port : request->conn->exporter_domain->application_port_he,
                 (long)request->conn->exporter_domain->customer_gid, request->conn->exporter_domain->is_backend_tls, is_no_auth);

    EXPORTER_OBJ_VALID(request, __FILE__, __LINE__);

    if (request->guac_info) {
        /* Remove this once guacd is out of app connector */
        if (!request->guac_info->guacd_service) {
            is_guac_mt = 1;
        } else {
            if ((request->guac_info->is_sftp_connection) &&
                (request->guac_info->gd_proxy.sftp_exporter_request == request)) {
                is_guac_sftp_mt = 1;
                // stub out certain areas in request to prevent adverse affects
                // on main wss connection
                conn_id = request->guac_info->gd_proxy.sftp_conn_id;
                port = request->guac_info->gd_proxy.sftp_port;
            } else {
              port = (uint16_t) request->sra_host_port;
            }
            is_pra_mt = 1;
        }

        /* Assign PRA specific Scope GID for PRA sessions */
        if (!is_pra_delegated_admin_disabled(request->conn->exporter_domain->customer_gid)) {
            pra_scope_gid = request->scope_gid;
            is_pra_third_party_login = request->is_ot_third_party_login;
        }
    }

    if (is_no_auth) {
        // Fake auth cookie, one per customer_id, so we dont get overrun by having too many in flight for single customer
        snprintf(fake_no_auth_cookie, sizeof(fake_no_auth_cookie), "%s.%ld",
                 EXPORTER_NO_AUTH_TOKEN_PREFIX,
                 (long)request->conn->exporter_domain->customer_gid);
        EXPORTER_DEBUG_SESSION_SHARING("SESS_PROC now in no auth case when trying to get the zfce");
        req_auth_cookie = &fake_no_auth_cookie[0];
        zfce = zpn_fohh_client_exporter_get(fake_no_auth_cookie);
        req_assertion = NULL;
    } else {
        if (!request->is_proxy_conn && !pra_scope_gid) {
            snprintf(lookup_id, 1024, "%s", auth_cookie);
            remote_server_type = zpn_server_type_broker;
        } if (!request->is_proxy_conn && pra_scope_gid) {
            snprintf(lookup_id, 1024, "%s:%"PRId64"", auth_cookie, pra_scope_gid);
            remote_server_type = zpn_server_type_broker;
        } else {
            remote_server_type = zpn_server_type_exporter;
            snprintf(lookup_id, 1024, "%s:%s", auth_cookie, request->remote_exporter_domain);
        }
        EXPORTER_DEBUG_SESSION_SHARING("SESS_PROC Doing zfce lookup with id = %s", lookup_id);
        req_auth_cookie = auth_cookie;
        zfce = zpn_fohh_client_exporter_get(lookup_id);
        req_assertion = assertion;
    }

    EXPORTER_DEBUG_SESSION_SHARING("SESS_PROC %s: Handle request for domain = %s, local_port = %d, application_port = %d, auth_cookie = %.*s, customer_id = %ld, is_tls = %d, is_no_auth = %d",
                 request->name, request->conn->exporter_domain->domain, request->conn->local_port_he,
                 (port) ? (int)port : request->conn->exporter_domain->application_port_he,
                 EXPORTER_DEBUG_BYTES, req_auth_cookie, (long)request->conn->exporter_domain->customer_gid,
                 request->conn->exporter_domain->is_backend_tls, is_no_auth);
    if (!zfce) {
        EXPORTER_DEBUG_SESSION_SHARING("SESS_PROC %s: zfce for auth_cookie %.*s doesn't exist, need to create one", request->name, EXPORTER_DEBUG_BYTES, auth_cookie);

        if (request->is_proxy_conn) {
            domain = request->remote_exporter_domain;
        } else {
            domain = exporter_broker_domain;
        }
        EXPORTER_DEBUG_SESSION_SHARING("SESS_PROC Doing zfce create to domain %s", domain);
        zfce = zpn_fohh_client_exporter_create(domain,
                                               remote_server_type,
                                               ZPATH_LOCAL_ROOT_CERTIFICATE_FILE,
                                               ZPATH_LOCAL_PUBLIC_CERTIFICATE_FILE,
                                               ZPATH_LOCAL_PRIVATE_KEY_FILE,
                                               ZPATH_LOCAL_CLOUD_NAME,
                                               request->conn->exporter_domain->customer_gid,
                                               req_auth_cookie,
                                               req_assertion,
                                               &(request->conn->remote_ip),             // Public IP
                                               &(request->log.client_private_ip),       // Private IP. Sometimes variable, but we log the first one.
                                               pra_scope_gid,                           // Scope GID to be used for PRA sessions
                                               is_pra_third_party_login,                // Flag to indicate Third-Party to PRA sessions
                                               exporter_zpa_app_query_ack_callback);
        if (!zfce) {
            EXPORTER_LOG(AL_ERROR, "SESS_PROC %s: Cannot get exporter to send the request", request->name);
            return ZPATH_RESULT_ERR;
        }

        EXPORTER_DEBUG_SESSION_SHARING("SESS_PROC %s: Got exporter to send the request", request->name);
    }

    if (!is_guac_sftp_mt) {
        conn_id = request->conn->nailed_up_mt ? request->conn->connection_id : 0;
        if (!is_pra_mt) {
            port = request->conn->exporter_domain->application_port_he;
        }
    }

    /* unified-portal
     *
     * For PRA apps we overwrite sni with below
     * request->conn->sni = request->sra_host_fqdn;
     *     There will be no exporter_domain entry created for PRA apps.
     *
     * For BA apps
     * request->conn->sni = request->conn->exporter_domain->domain = request->conn->exporter_domain->cfg_domain
     *
     * For Managed BA apps
     * request->conn->sni = request->conn->exporter_domain->domain and
     *   request->conn->exporter_domain->cfg_domain = admin configured internal application hostname.
     */

    host_name = request->conn->sni;

    /*
     * We don't need to handle the scenario if Managed BA is turned off in datapath but config exists
     *
     * Since we never add internal app to exporter_domain we will not allow traffic at TLS level
     *
     * In case of a bug and we turned off Managed BA, access to apps will be broken which is expected
     * Customer must downgrade config from 2.0 to 1.0 via Admin UI to provide app access
     * since for 1.0 we also need certs and not just feature flag off !!
     */

    if (IS_MANAGED_APP(request)) {
        host_name = request->conn->exporter_domain->cfg_domain;
    }

    mt = zpn_fohh_client_export_mt_get(request->name,
                                       zfce,
                                       host_name,
                                       IPPROTO_TCP,
                                       port,
                                       request->conn->exporter_domain->is_backend_tls, // TLS
                                       exporter_request_mt_status_callback,
                                       exporter_request_mt_consume_callback,
                                       exporter_request_mt_unblock_callback,
                                       exporter_request_mt_trans_log_callback,
                                       request,
                                       0,
                                       request->publish_gid,
                                       request->conn->exporter_domain->ignore_trust_verification ? 0 : 1,
                                       conn_id,
                                       request->conn->no_mt_reuse,
                                       request->http_upgrade,
                                       is_guac_mt,
                                       request->is_proxy_conn,
                                       &request->gposture_object,
                                       &request->gprofiles);
    if (!mt) {
        EXPORTER_DEBUG_SESSION_SHARING("SESS_PROC %s: zpn_fohh_client_export_mt_get failed, cannot get mt", request->name);
        return ZPATH_RESULT_ERR;
    }


    /* Log a link log even when mtunnel logging is disabled for basic broker side troubleshooting */

    ZPATH_MUTEX_LOCK(&(zfce->zfc->lock), __FILE__, __LINE__);
    EXPORTER_DEBUG_SESSION_SHARING("SESS_PROC %s: Link request: %s, with mtunnel_id: %s, zfce: %s, tag_id: %d",
                 request->name, request->name,
                 mt->mtunnel_id ? mt->mtunnel_id : "<unknown>",
                 ZFC_DBG(zfce->zfc),
                 mt->tag_id);
    ZPATH_MUTEX_UNLOCK(&(zfce->zfc->lock), __FILE__, __LINE__);

    request->mt = mt;
    request->mt_incarnation = mt->incarnation;
    request->log.mt_status = zfce_mt_status_string(mt->status);

    if (is_guac_sftp_mt) {
        request->guac_info->gd_proxy.cookie = mt;
    }

    if (is_pra_mt) {
        request->mt->is_pra_session = 1;
    }

    /* Try to send out the request (if body available), unless request is already marked as upgrade */
    if (!request->guac_info || (request->guac_info && request->request_body && evbuffer_get_length(request->request_body))) {
        exporter_request_send_data(request);
    }

    return ZPATH_RESULT_NO_ERROR;
}


int exporter_zpa_terminate(struct exporter_request *request)
{
    if (request->mt_destroy_sent) {
        if (request->mt) {
            /* Just waiting... (This is somewhat common) */
            if (request->mt_release_received) {
                /* not logging m_tunnel_id intentionally - mt is no longer safe to use */
                EXPORTER_DEBUG_HTTP("%s: zpa_terminate called, but already sent termination. Got response and reset mt.", request->name);
                request->mt = NULL;
            } else {
                ZPATH_MUTEX_LOCK(&(request->mt->lock), __FILE__, __LINE__);
                EXPORTER_DEBUG_HTTP("%s: mtunnel_id = %s, zpa_terminate called, but already sent termination. Haven't gotten response yet.",
                                    request->name,
                                    request->mt->mtunnel_id ? request->mt->mtunnel_id : "<unknown>");
                ZPATH_MUTEX_UNLOCK(&(request->mt->lock), __FILE__, __LINE__);
            }
            return ZPATH_RESULT_ASYNCHRONOUS;
        } else {
            /* Already terminated. Why are we here? Probably called
             * idempotently */
            EXPORTER_LOG(AL_ERROR, "%s: zpa_terminate called, connection already terminated", request->name);
            return ZPATH_RESULT_NO_ERROR;
        }
    } else {
        if (request->mt) {
            int http_transaction_error = 0;
            int res;
            /* Okay, kill it. */
            request->mt_destroy_sent = 1;

            /* Check if we are terminating the transaction without completing the request and response */
            if (!request->http_request_complete || !request->http_response_complete) {
                http_transaction_error = 1;
            }

            res = zpn_fohh_client_export_mt_release(request->mt, http_transaction_error);
            if (res) {
                ZPATH_MUTEX_LOCK(&(request->mt->lock), __FILE__, __LINE__);
                EXPORTER_LOG(AL_ERROR, "%s: mtunnel_id = %s, Got %s trying to destroy mt",
                                        request->name,
                                        request->mt->mtunnel_id ? request->mt->mtunnel_id : "<unknown>",
                                        zpath_result_string(res));
                ZPATH_MUTEX_UNLOCK(&(request->mt->lock), __FILE__, __LINE__);
                return res;
            } else {
                ZPATH_MUTEX_LOCK(&(request->mt->lock), __FILE__, __LINE__);
                EXPORTER_DEBUG_HTTP("%s: mtunnel_id = %s Sent mt release",
                                    request->name,
                                    request->mt->mtunnel_id ? request->mt->mtunnel_id : "<unknown>");
                ZPATH_MUTEX_UNLOCK(&(request->mt->lock), __FILE__, __LINE__);
            }
            return ZPATH_RESULT_ASYNCHRONOUS;
        } else {
            /* Uh, bad. */
            EXPORTER_LOG(AL_ERROR, "%s: Got zpa_terminate message without having an mt", request->name);
            return ZPATH_RESULT_ERR;
        }
    }
    return ZPATH_RESULT_NO_ERROR;
}

int exporter_zpa_app_query(int64_t query_id,
                           int64_t customer_gid,
                           const char *auth_cookie,
                           const char *assertion,
                           const char *hostname,
                           int proto,
                           uint16_t port,
                           int tls,
                           int64_t publish_gid)
{
    struct zpn_fohh_client_exporter *zfce = NULL;
    int res;

    zfce = zpn_fohh_client_exporter_get(auth_cookie);
    if (!zfce) {
        EXPORTER_DEBUG_HTTP("zfce for auth_cookie %.*s customer_gid %ld doesn't exist, need to create one", EXPORTER_DEBUG_BYTES, auth_cookie, (long)customer_gid);

        zfce = zpn_fohh_client_exporter_create(exporter_broker_domain,
                                               zpn_server_type_broker,
                                               ZPATH_LOCAL_ROOT_CERTIFICATE_FILE,
                                               ZPATH_LOCAL_PUBLIC_CERTIFICATE_FILE,
                                               ZPATH_LOCAL_PRIVATE_KEY_FILE,
                                               ZPATH_LOCAL_CLOUD_NAME,
                                               customer_gid,
                                               auth_cookie,
                                               assertion,
                                               NULL, // No public IP
                                               NULL, // No private IP
                                               0, // No PRA scope GID
                                               0, // No Third-Party PRA login
                                               exporter_zpa_app_query_ack_callback);
        if (!zfce) {
            EXPORTER_LOG(AL_ERROR, "%s: Cannot create exporter to send the query", auth_cookie);
            return ZPATH_RESULT_ERR;
        }

        EXPORTER_LOG(AL_DEBUG, "%s: Got exporter to send the query", auth_cookie);
    }

    res = zpn_fohh_client_app_query(zfce, query_id, hostname, proto, port, tls, publish_gid);
    if (res) {
        EXPORTER_LOG(AL_ERROR, "%ld:%.*s App query failed for %s:%d:%d:%d", (long)customer_gid, EXPORTER_DEBUG_BYTES, auth_cookie, hostname, proto, port, tls);
        return res;
    }

    return ZPATH_RESULT_NO_ERROR;
}
