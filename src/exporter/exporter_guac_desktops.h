/*
 * exporter_guac_desktops.h Copyright (C) 2025 Zscaler Inc. All Rights Reserved
 *
 * Disposable jumpbox management in PRA
 */

#define DJB_CONSOLE_NAME "Privileged Desktop"
#define DJ<PERSON>_CONSOLE_WIN_OS "Windows"
#define DJ<PERSON>_CONSOLE_WIN_PROTOCOL "RDP"
#define DJB_CONSOLE_LINUX_PROTOCOL "SSH"
#define DJB_RDP_CONN_SECURITY "Any"
#define DJB_INFO_API_CALL_PREFIX "/v1/dispjumpbox/customers/"
#define DJB_INFO_API_CALL_SUFFIX "/djbInfo"

void exporter_get_pra_djbservice_hostname(char **hostname);
int exporter_sra_portal_handle_privileged_desktops_api(struct exporter_request *request);
int check_query_param_djb(struct exporter_request *request);
int zpn_sra_djb_get_info(struct exporter_request *request, int64_t console_id);
void exporter_request_free_djb_info(struct exporter_request *request);
void exporter_request_djb_reset_api_type(struct exporter_request *request);
int is_request_for_static_zconsole_content(struct exporter_request *request);
