/*
 * exporter_guac_proxy.c Copyright (C) 2021 Zscaler Inc. All Rights Reserved.
 */

#if __linux__
#define _XOPEN_SOURCE 700
#include <string.h>
#include <strings.h>
#endif

#include <stdio.h>
#include "base64/base64.h"
#include <sys/stat.h>
#include "zcrypt/zcrypt.h"
#include "zpath_lib/zpath_local.h"
#include <ftw.h>
#include <errno.h>

#include "fohh/fohh_http_private.h"
#include "zcdns/zcdns_libevent.h"

#include "exporter/exporter_private.h"
#include "exporter/exporter.h"
#include "exporter/exporter_guac_util.h"
#include "exporter/exporter_guac_proxy.h"
#include "exporter/exporter_guac_api_zia.h"
#include "exporter/exporter_conn.h"
#include "exporter/exporter_request_policy_state.h"
#include "exporter/exporter_guac_parser.h"
#include "exporter/exporter_privileged_policy.h"
#include "exporter/exporter_request.h"
#include "exporter/exporter_guac_sess_sharing.h"
#include "zpath_lib/zpath_instance.h"
#include "exporter/exporter_user_portal_request_state.h"
#include "exporter/exporter_guac_session_control_host.h"
#include "parson/parson.h"
#include "exporter/exporter_user_portal_api.h"
#include "exporter/exporter_privileged_file_system.h"

char *strcasestr(const char *haystack, const char *needle);

extern char *exporter_guac_rdprd_path;

static inline void exporter_guac_reset_filetransfer_active_bits(struct exporter_request *request)
{
    request->guac_info->is_ft_async_active = 0;
    request->guac_info->is_cancel_scan_active = 0;
}

static inline void exporter_guac_reset_clipboard_bits(struct exporter_request *request)
{
    request->guac_info->clipboard_size = 0;
}

static inline void exporter_guac_drain_stream(struct exporter_stream_op *stream_op)
{
    // Drain stream request and response
    if (stream_op->stream_request) {
        evbuffer_drain(stream_op->stream_request,
                evbuffer_get_length(stream_op->stream_request));
    }

    if (stream_op->stream_response) {
        evbuffer_drain(stream_op->stream_response,
                evbuffer_get_length(stream_op->stream_response));
    }
}

void exporter_guac_reset_stream_params(struct exporter_request *request)
{
    request->guac_info->file_scan.upload_file_fd = -1;
    request->guac_info->is_stream_timeout_triggered = 0;
    if (request->guac_info->file_scan.upload_file) {
        fclose(request->guac_info->file_scan.upload_file);
        request->guac_info->file_scan.upload_file = NULL;
    }
    if (request->guac_info->decrypted_buffer) {
        evbuffer_free(request->guac_info->decrypted_buffer);
        request->guac_info->decrypted_buffer = NULL;
    }
    request->guac_info->file_info.upload_file_size = 0;
    exporter_guac_reset_filetransfer_active_bits(request);
    exporter_guac_reset_clipboard_bits(request);
}

static int exporter_guac_unlink_cb(const char *fpath, const struct stat *sb, int typeflag, struct FTW *ftwbuf)
{
    int rv = remove(fpath);

    if (rv)
        perror(fpath);

    return rv;
}

/* Function to allocate streams */
void exporter_guac_alloc_stream(struct exporter_stream_op *stream_op)
{
    stream_op->stream_id = -1;
    stream_op->is_stream_active = 0;
    stream_op->stream_type = exporter_guac_invalid;
    stream_op->stream_transmit = 0;
    stream_op->stream_receive = 0;
    if (!stream_op->stream_request) {
        stream_op->stream_request = evbuffer_new();
    }
    if (!stream_op->stream_response) {
        stream_op->stream_response = evbuffer_new();
    }
}

/* Function to initialise stream */
void exporter_guac_init_stream(struct exporter_stream_op *stream_op,
                               int stream_id,
                               enum exporter_guac_stream_type stream_type)
{
    stream_op->stream_id = stream_id;
    stream_op->is_stream_active = 1;
    stream_op->stream_type = stream_type;
    stream_op->stream_transmit = 0;
    stream_op->stream_receive = 0;
}

/* Function to check if the stream is active */
int exporter_guac_is_stream_active(struct exporter_stream_op *stream_op)
{
    if (stream_op->is_stream_active) {
        return 1;
    }
    return 0;
}

/* Function to reset stream */
int exporter_guac_reset_stream(struct exporter_request *request,
                               struct exporter_stream_op *stream_op)
{
    if (!request || !request->guac_info) {
        EXPORTER_LOG(AL_ERROR, "request or guac context is NULL, nothing to do");
        return  ZPATH_RESULT_ERR;
    }

    exporter_guac_reset_stream_params(request);

    stream_op->stream_id = -1;
    stream_op->is_stream_active = 0;
    stream_op->stream_type = exporter_guac_invalid;
    stream_op->stream_transmit = 0;
    stream_op->stream_receive = 0;
    return ZPATH_RESULT_NO_ERROR;
}

/* Function to free streams */
void exporter_guac_free_stream(struct exporter_request *request)
{
    if (!request || !request->guac_info) {
        return;
    }

    for (int i = 0; i < exporter_guac_stream_max_op; i++) {
        if (request->guac_info->guac_in_stream[i].stream_request) {
            evbuffer_free(request->guac_info->guac_in_stream[i].stream_request);
            request->guac_info->guac_in_stream[i].stream_request = NULL;
        }
        if (request->guac_info->guac_in_stream[i].stream_response) {
            evbuffer_free(request->guac_info->guac_in_stream[i].stream_response);
            request->guac_info->guac_in_stream[i].stream_response = NULL;
        }
        if (request->guac_info->guac_out_stream[i].stream_request) {
            evbuffer_free(request->guac_info->guac_out_stream[i].stream_request);
            request->guac_info->guac_out_stream[i].stream_request = NULL;
        }
        if (request->guac_info->guac_out_stream[i].stream_response) {
            evbuffer_free(request->guac_info->guac_out_stream[i].stream_response);
            request->guac_info->guac_out_stream[i].stream_response = NULL;
        }
    }
    /* Cleanup if PRA conn down when FT inspection is in progress */
    if (exporter_guac_is_stream_active(&request->guac_info->guac_in_stream[file_transfer]) &&
            (request->guac_info->guac_in_stream[file_transfer].stream_type == exporter_guac_file_upload)) {
        exporter_guac_proxy_fail(request, UPLOAD_STATUS_INTERNAL_ERROR, GUAC_UPLOAD_CONNECTION_GONE);
    }
}

void exporter_delete_remote_drive_directory(struct exporter_request *request, struct exporter_conn *conn){
    char dir_path[GUAC_PATH_MAX_LEN] = {0};
    int ret = ZPATH_RESULT_NO_ERROR;

    snprintf(dir_path, GUAC_PATH_MAX_LEN, "%s/%"PRId64"/%d_%"PRId64,
            exporter_guac_rdprd_path,
            conn->exporter_domain->customer_gid,
            request->conn->connection_id,
            request->conn->incarnation);

    ret = nftw(dir_path, exporter_guac_unlink_cb, 64, FTW_DEPTH | FTW_PHYS);
    if (ret < 0) {
        EXPORTER_LOG(AL_ERROR, "%s: Failed to remove RDP-RD directory: %s, err: %s", request->name, dir_path, strerror(errno));
    } else {
        EXPORTER_DEBUG_GUACD("DBG_GUACD %s Removed RDP-RD directory: %s", request->name, dir_path);
    }
}

int exporter_guac_proxy_send(struct exporter_request *request, struct exporter_stream_op *stream_op) {
    size_t len = evbuffer_get_length(stream_op->stream_request);

    // No more data in proxy request buffer, done with proxy.
    if (len <= 0) {

       // Send upload status to Browser
        exporter_guac_stream_instr_push(stream_op->stream_response, registered_op_codes[OP_CODE_UPLOAD],
                stream_op->stream_id, "END", 5);

        // Upload done, send exporter log
        exporter_guac_send_exporter_log_data(request, "Success");

        exporter_guac_reset_stream(request, stream_op);
        return ZPATH_RESULT_NO_ERROR;
    }

    // Parse proxy request buffer, one instruction at time

    // No callbacks
    guac_proto_parser_settings settings = {
            .stop_after_first_full_instruction = 1
    };

    // The whole instruction must already be in the request buffer
    size_t guac_bytes_parsed = guac_parser_execute(&request->guac_info->stream_parser,
            &settings,
            (const char *) evbuffer_pullup(stream_op->stream_request, len),
            len);

    if (guac_bytes_parsed == 0 ||
            request->guac_info->guac_parser.state == PARSING_ERROR) {
        return ZPATH_RESULT_ERR;
    }

    // Append data to request_body (must be on instructions boundary)
    evbuffer_remove_buffer(stream_op->stream_request,
            request->request_body,
            guac_bytes_parsed);

    if ((exporter_guac_is_stream_active(stream_op)) &&
        ((stream_op->stream_type == exporter_guac_file_upload) ||
        (stream_op->stream_type == exporter_guac_advanced_file_upload))) {
        if (stream_op->stream_type == exporter_guac_file_upload) {
            // send progress update if the upload is active
            exporter_guac_stream_instr_push(stream_op->stream_response, registered_op_codes[OP_CODE_UPLOAD],
                    stream_op->stream_id, "SEND", 4);
        }
        stream_op->stream_receive = 1;
    }
    /* For file download/clipboard paste, send the get/clipboard instruction
     * from stream request to guacd but the above upload should not be sent
     * reset stream_transmit. stream_receive is not needed for downloads/clipboard */
    stream_op->stream_transmit = 0;

    return ZPATH_RESULT_NO_ERROR;
}

int exporter_guac_proxy_file(struct exporter_request *request, const char *data, size_t len) {
    // buffer to hold the temporary file name
    char nameBuff[64] = "/tmp/file-XXXXXX";

    // Open tmpfile
    request->guac_info->file_scan.upload_file_fd = mkstemp(nameBuff);
    unlink(nameBuff);

    if (request->guac_info->file_scan.upload_file_fd == -1) {
        EXPORTER_LOG(AL_ERROR, "%s: failed to create upload tmp file", request->name);
        return ZPATH_RESULT_ERR;
    }

    request->guac_info->file_scan.upload_file = fdopen(request->guac_info->file_scan.upload_file_fd, "rb+");

    if (!request->guac_info->file_scan.upload_file) {
        EXPORTER_LOG(AL_ERROR, "%s: failed to open upload tmp file", request->name);
        return ZPATH_RESULT_ERR;
    }

    return ZPATH_RESULT_NO_ERROR;
}

int exporter_guac_proxy_blob(struct exporter_request *request, const unsigned char *buff, size_t len) {
    // Append to tmpfile
    fwrite(buff, len, 1, request->guac_info->file_scan.upload_file);
    return ZPATH_RESULT_NO_ERROR;
}

int encrypt_scan_file(struct exporter_request **request){
    char *buffer = NULL;
    int64_t read_bytes = 0;
    struct zcrypt_key key = {{0}};
    unsigned long buffer_out_len = 0;
    unsigned long max_out_len = 0;
    char *buffer_out = NULL;
    int res = ZPATH_RESULT_NO_ERROR;

     // 2a. Generate the key used for encryption
    if (get_crypto_key_from_zpath_cloud_secret(&key, 0) == ZPATH_RESULT_ERR) {
        EXPORTER_LOG(AL_ERROR, "Unable to generate key for encryption.");
        return ZPATH_RESULT_ERR;
    }

    buffer = (char *)EXPORTER_CALLOC((*request)->guac_info->file_info.upload_file_size * sizeof(char) + 1);
    if(!buffer){
        EXPORTER_LOG(AL_ERROR, "Unable to allocate memory.");
        res = ZPATH_RESULT_ERR;
        goto DONE;
    }

    rewind((*request)->guac_info->file_scan.upload_file);
    read_bytes = fread(buffer, 1, (*request)->guac_info->file_info.upload_file_size, (*request)->guac_info->file_scan.upload_file);
    if (!read_bytes){
        EXPORTER_LOG(AL_ERROR, "Unable to read file.");
        res = ZPATH_RESULT_ERR;
        goto DONE;
    }

    buffer_out_len = ZCRYPT_KEY_SIZE + (*request)->guac_info->file_info.upload_file_size*2;
    buffer_out = (char *)EXPORTER_CALLOC(buffer_out_len* sizeof(char) + 1);
    if (NULL == buffer_out) {
        EXPORTER_LOG(AL_ERROR, "Unable to allocate memory");
        res = ZPATH_RESULT_ERR;
        goto DONE;
    }

    max_out_len = buffer_out_len + 1;

   /*Encrypt the contents*/
    if (zcrypt_encrypt(&key, (unsigned char *)buffer, read_bytes, buffer_out, &buffer_out_len) != ZCRYPT_RESULT_NO_ERROR) {
        EXPORTER_LOG(AL_ERROR, "Unable to encrypt file");
        res = ZPATH_RESULT_ERR;
        goto DONE;
    }

    if (buffer_out_len > max_out_len) {
        EXPORTER_LOG(AL_ERROR, "Unable to encrypt file, len = %lu", buffer_out_len);
        res = ZPATH_RESULT_ERR;
        goto DONE;
    }

    rewind((*request)->guac_info->file_scan.upload_file);
    int ret_len = fwrite(buffer_out, buffer_out_len, 1, (*request)->guac_info->file_scan.upload_file);
    if (ret_len!=1){
        EXPORTER_LOG(AL_ERROR,"Failed to add encrypted data to file");
        res = ZPATH_RESULT_ERR;
        goto DONE;
    }

    (*request)->guac_info->file_info.upload_file_size = buffer_out_len;

DONE:
    if(buffer_out)
        EXPORTER_FREE(buffer_out);
    if(buffer)
        EXPORTER_FREE(buffer);
    return res;
}

/* Caller of the routine should wake up the conn to process the stream response in guac_info */
int exporter_guac_proxy_end(struct exporter_request *request) {
    struct stat file_info;
    const char *err_str = "Internal Error in upload";
    int   res = ZPATH_RESULT_NO_ERROR;

    if (!(request->guac_info->capabilities_policy_bitmap & INSPECT_FILE_UPLOAD)) {
        EXPORTER_DEBUG_FILE_TRANSFER("%s: File scan bypassed for uploaded files due to policy ", request->name);
        if (request->guac_info->file_scan.upload_file) {
            fclose(request->guac_info->file_scan.upload_file);
            request->guac_info->file_scan.upload_file = NULL;
        }
        return ZPATH_RESULT_NO_ERROR;
    }

    if (fstat(fileno(request->guac_info->file_scan.upload_file), &file_info) != 0) {
        exporter_guac_proxy_fail(request, UPLOAD_STATUS_INTERNAL_ERROR, err_str);
        return ZPATH_RESULT_ERR;
    }

    if (request->guac_info->file_info.upload_file_size == 0) {
        exporter_guac_proxy_fail(request, UPLOAD_STATUS_SCAN_FAIL, "Empty File");
        return 0;
    }

    if (encrypt_scan_file(&request) == ZPATH_RESULT_ERR) {
        exporter_guac_proxy_fail(request, UPLOAD_STATUS_INTERNAL_ERROR, "File encryption failed");
        return ZPATH_RESULT_ERR;
    }

    // Initiate scan
    request->guac_info->file_scan.is_zia_scan_req = 1;
    res = exporter_zia_scan_process(request);
    if (res != ZPATH_RESULT_NO_ERROR) {
        EXPORTER_LOG(AL_ERROR, "%s: file scan failed, err : %s", request->name, zpath_result_string(res));
        exporter_guac_proxy_fail(request,
                                UPLOAD_STATUS_SCAN_FAIL,
                                GUAC_UPLOAD_ZIA_ERR_MSG);
        return 0;
    }
    request->guac_info->is_ft_async_active = 1;
    exporter_guac_api_stats_increment(GUAC_FILE_TRANSFER_UPLOAD_INSPECT);
    return ZPATH_RESULT_NO_ERROR;
}

void
exporter_guac_proxy_sandbox_info_free(struct exporter_request *request) {

    if (!request || !request->guac_info) {
        return;
    }
    if (request->guac_info->file_scan.file_md5) {
        EXPORTER_FREE(request->guac_info->file_scan.file_md5);
        request->guac_info->file_scan.file_md5 = NULL;
    }
    if (request->guac_info->file_scan.zia_auth_sess_id) {
        if (request->guac_info->file_scan.zia_auth_sess_id[0] != '\0') {
            memset(request->guac_info->file_scan.zia_auth_sess_id, '\0',
                    strnlen(request->guac_info->file_scan.zia_auth_sess_id, 256));
        }
        EXPORTER_FREE(request->guac_info->file_scan.zia_auth_sess_id);
        request->guac_info->file_scan.zia_auth_sess_id = NULL;
    }
    if (request->guac_info->file_scan.zia_cloud_domain) {
        EXPORTER_FREE(request->guac_info->file_scan.zia_cloud_domain);
        request->guac_info->file_scan.zia_cloud_domain = NULL;
    }
    if (request->guac_info->file_scan.zia_username) {
        if (request->guac_info->file_scan.zia_username[0] != '\0') {
            memset(request->guac_info->file_scan.zia_username, '\0',
                    strnlen(request->guac_info->file_scan.zia_username, 256));
        }
        EXPORTER_FREE(request->guac_info->file_scan.zia_username);
        request->guac_info->file_scan.zia_username = NULL;
    }
    if (request->guac_info->file_scan.zia_password) {
        if (request->guac_info->file_scan.zia_password[0] != '\0') {
            memset(request->guac_info->file_scan.zia_password, '\0',
                    strnlen(request->guac_info->file_scan.zia_password, 256));
        }
        EXPORTER_FREE(request->guac_info->file_scan.zia_password);
        request->guac_info->file_scan.zia_password = NULL;
    }
    if (request->guac_info->file_scan.zia_cloud_service_api_key) {
        if (request->guac_info->file_scan.zia_cloud_service_api_key[0] != '\0') {
            memset(request->guac_info->file_scan.zia_cloud_service_api_key,'\0',
                    strnlen(request->guac_info->file_scan.zia_cloud_service_api_key, 256));
        }
        EXPORTER_FREE(request->guac_info->file_scan.zia_cloud_service_api_key);
        request->guac_info->file_scan.zia_cloud_service_api_key = NULL;
    }
    if (request->guac_info->file_scan.zia_sandbox_api_token) {
        if (request->guac_info->file_scan.zia_sandbox_api_token[0] != '\0') {
            memset(request->guac_info->file_scan.zia_sandbox_api_token,'\0',
                    strnlen(request->guac_info->file_scan.zia_sandbox_api_token, 256));
        }
        EXPORTER_FREE(request->guac_info->file_scan.zia_sandbox_api_token);
        request->guac_info->file_scan.zia_sandbox_api_token = NULL;
    }
}

int exporter_guac_proxy_denied(struct exporter_request *request, const char *zia_response) {
    if (request->guac_info->is_cancel_scan_active) {
        exporter_guac_proxy_fail(request,
                                 UPLOAD_STATUS_SCAN_FAIL,
                                 GUAC_UPLOAD_CANCELLED_BY_USER);
        return ZPATH_RESULT_ERR;
    }
    struct exporter_stream_op *stream_op = &request->guac_info->guac_in_stream[file_transfer];

    // Delete stream request as we refuse to send this file to guacd
    if (stream_op->stream_request) {
        evbuffer_drain(stream_op->stream_request,
                evbuffer_get_length(stream_op->stream_request));
    }

    // De-allocate memory allocated for ZIA file scan
    exporter_guac_proxy_sandbox_info_free(request);

    // Delete ZIA scan timeout event since file upload is denied
    exporter_zia_scan_del_timeout(request);

    const char *message = EXPORTER_STRDUP(zia_response, strlen(zia_response));
    exporter_guac_stream_instr_push(stream_op->stream_response, registered_op_codes[OP_CODE_UPLOAD],
            stream_op->stream_id, message, 2);
    EXPORTER_FREE((void *) message);

    // Scan has failed, send exporter log
    exporter_guac_send_exporter_log_data(request, "Inspection denied upload");

    exporter_guac_api_stats_increment(GUAC_FILE_TRANSFER_UPLOAD_INSPECT_DENY);
    exporter_guac_api_stats_increment(GUAC_FILE_TRANSFER_UPLOAD_FAILURE);

    exporter_guac_reset_stream(request, stream_op);
    return ZPATH_RESULT_NO_ERROR;
}


int exporter_guac_proxy_wait(struct exporter_request *request, const char *zia_response) {
    if (request->guac_info->is_cancel_scan_active) {
        exporter_guac_proxy_fail(request,
                                 UPLOAD_STATUS_SCAN_FAIL,
                                 GUAC_UPLOAD_CANCELLED_BY_USER);
        return ZPATH_RESULT_ERR;
    }
    struct exporter_stream_op *stream_op = &request->guac_info->guac_in_stream[file_transfer];

    stream_op->stream_transmit = 0;
    stream_op->stream_receive = 0;

    // De-allocate memory allocated for ZIA file scan
    exporter_guac_proxy_sandbox_info_free(request);

    // Set ZIA scan timeout event if not already set
    exporter_zia_scan_set_timeout(request);

    const char *message = EXPORTER_STRDUP(zia_response, strlen(zia_response));
    exporter_guac_stream_instr_push(stream_op->stream_response, registered_op_codes[OP_CODE_UPLOAD],
            stream_op->stream_id, message, 3);
    EXPORTER_FREE((void *) message);

    EXPORTER_DEBUG_FILE_TRANSFER("%s: exporter_guac_proxy_wait zia response %s", request->name, zia_response);
    return ZPATH_RESULT_NO_ERROR;
}

int exporter_guac_proxy_allowed(struct exporter_request *request, const char *zia_response) {
    if (request->guac_info->is_cancel_scan_active) {
        exporter_guac_proxy_fail(request,
                                 UPLOAD_STATUS_SCAN_FAIL,
                                 GUAC_UPLOAD_CANCELLED_BY_USER);
        return ZPATH_RESULT_ERR;
    }
    struct exporter_stream_op *stream_op = &request->guac_info->guac_in_stream[file_transfer];

    // De-allocate memory allocated for ZIA file scan
    exporter_guac_proxy_sandbox_info_free(request);

    // Delete ZIA scan timeout event since file upload is allowed.
    exporter_zia_scan_del_timeout(request);

    if (request->guac_info->file_scan.upload_file_fd == -1) {
        exporter_guac_proxy_fail(request,
                                 UPLOAD_STATUS_SCAN_FAIL,
                                 GUAC_UPLOAD_ZIA_ERR_MSG);
        return ZPATH_RESULT_ERR;
    }

    if (request->guac_info->decrypted_buffer) {
        evbuffer_free(request->guac_info->decrypted_buffer);
    }

    request->guac_info->decrypted_buffer = evbuffer_new();
    if (!request->guac_info->decrypted_buffer) {
        EXPORTER_LOG(AL_ERROR, "Could not allocate evbuffer.");
        exporter_guac_proxy_fail(request,
                                 UPLOAD_STATUS_SCAN_FAIL,
                                 GUAC_UPLOAD_ZIA_ERR_MSG);
        return ZPATH_RESULT_ERR;
    }

    if (decrypt_scan_file(&request, request->guac_info->decrypted_buffer) == ZPATH_RESULT_ERR){
        if (request->guac_info->decrypted_buffer) {
            evbuffer_free(request->guac_info->decrypted_buffer);
        }
        exporter_guac_proxy_fail(request,
                                 UPLOAD_STATUS_SCAN_FAIL,
                                 GUAC_UPLOAD_CANCELLED_BY_USER);
        return ZPATH_RESULT_ERR;
    }

    fclose(request->guac_info->file_scan.upload_file);
    request->guac_info->file_scan.upload_file = NULL;

    const char *message = EXPORTER_STRDUP(zia_response, strlen(zia_response));
    exporter_guac_stream_instr_push(stream_op->stream_response, registered_op_codes[OP_CODE_UPLOAD],
            stream_op->stream_id, message, 1);
    EXPORTER_FREE((void *) message);

    // Start timeout timer
    exporter_stream_create_set_timeout(request);
    EXPORTER_DEBUG_FILE_TRANSFER("%s: exporter_guac_proxy_allowed zia response", request->name);

    // Set proxy forward control knobs only at this point
    stream_op->stream_transmit = 1;
    stream_op->stream_receive = 0;
    request->guac_info->is_ft_async_active = 0;

    // Sent data has to be aligned with the input from Browser
    exporter_conn_trigger_read_cb(request->conn);
    return ZPATH_RESULT_NO_ERROR;
}

int exporter_guac_proxy_fail(struct exporter_request *request,
                             const int status_code,
                             const char *error_string) {
    if (!request || !request->guac_info) {
        EXPORTER_LOG(AL_ERROR, "request or guac context is NULL, cannot send failure msg");
        return  ZPATH_RESULT_ERR;
    }
    struct exporter_stream_op *stream_op = &request->guac_info->guac_in_stream[file_transfer];

    // Delete stream request as we refuse to send this file to guacd
    if (stream_op->stream_request) {
        evbuffer_drain(stream_op->stream_request,
                evbuffer_get_length(stream_op->stream_request));
    }

    // De-allocate memory allocated for ZIA file scan
    exporter_guac_proxy_sandbox_info_free(request);

    // Delete ZIA scan timeout event since file upload failed.
    exporter_zia_scan_del_timeout(request);

    if (stream_op->stream_response) {
        exporter_guac_stream_instr_push(stream_op->stream_response, registered_op_codes[OP_CODE_UPLOAD],
                stream_op->stream_id, error_string, status_code);
    }

    EXPORTER_DEBUG_FILE_TRANSFER("%s: exporter_guac_proxy_fail %s", request->name, error_string);

    // Upload has failed, send exporter log
    exporter_guac_send_exporter_log_data(request, error_string);

    exporter_guac_api_stats_increment(GUAC_FILE_TRANSFER_UPLOAD_FAILURE);
    if (status_code == UPLOAD_STATUS_SCAN_FAIL) {
        exporter_guac_api_stats_increment(GUAC_FILE_TRANSFER_UPLOAD_INSPECT_FAIL);
    }

    exporter_guac_reset_stream(request, stream_op);

    return ZPATH_RESULT_NO_ERROR;
}

size_t exporter_guac_proxy_process_server_error_cb(guac_proto_parser *parser)
{
    struct exporter_conn *conn = parser->data;
    char *error_string = NULL;
    if (conn && conn->current_request && conn->current_request->mt) {
        struct exporter_request *request = conn->current_request;
        /* Special handling: currently guacd returns error code 519 for VNC/REALVNC in case of incorrect credentials */
        if (request->sra_host_protocol && (!strncasecmp(request->sra_host_protocol, "VNC", 3) || !strncasecmp(request->sra_host_protocol, "REALVNC", 7)) &&
            (parser->guac_error_params.status == 519)) {
            error_string = get_guacd_err_string(769);
        } else {
            error_string = get_guacd_err_string(parser->guac_error_params.status);
        }
        if (request->mt && request->mt->guac_error_string) {
            EXPORTER_FREE(request->mt->guac_error_string);
        }
        if (request->mt) {
            request->mt->guac_error_string = EXPORTER_STRDUP(error_string, strlen(error_string));
            zpn_fohh_client_exporter_send_exporter_log_data(request->mt);
        } else {
             EXPORTER_LOG(AL_ERROR, "%s: Received error %s from guacd. mtunnel is already deleted or not setup!",
			      request->name, error_string);
        }
    }
    return parser->offset - parser->instruction_start;
}

/*
 * This function removes an instruction from response data based on the offset
 * available from the parser.
 */
int exporter_guac_delete_instr_from_response_data(struct exporter_request *request,
                                                  guac_proto_parser *parser) {
    struct evbuffer *modified_response_data = evbuffer_new();

    ev_ssize_t response_data_len = evbuffer_get_length(request->response_data);

    /*
     * buffer_instruction_start indicates the starting position of the instruction to delete
     * in response_data. If buffer_instruction_start is non-zero, copy everything before
     * the offset.
     * Eg, response->data = "1.a;3.abc;2.a"
     * response_data_len = 13, buffer_instruction_start = 4,
     * parser->buffer_instruction_start = 4, parser->offset = 10
     *
     * response->data = "3.abc;2.a", modified_response_data = "1.a;"
     */
    if (parser->buffer_instruction_start) {
        evbuffer_remove_buffer(request->response_data, modified_response_data, parser->buffer_instruction_start);
    }
    response_data_len = evbuffer_get_length(request->response_data);
    /*
     * Drain the instruction
     * response->data = "3.abc;2.a", modified_response_data = "1.a;"
     * response->data = "2.a", modified_response_data = "1.a;"
     */
    evbuffer_drain(request->response_data, parser->offset - parser->instruction_start);

    /*
     * Copy remaining data if there are any
     * response->data = "2.a", modified_response_data = "1.a;"
     * response->data = "", modified_response_data = "1.a;2.a"
     */
    response_data_len = evbuffer_get_length(request->response_data);
    if (response_data_len) {
        evbuffer_remove_buffer(request->response_data, modified_response_data, response_data_len);
    }

    /* Copy the modified response back to response_data */
    ev_ssize_t modified_response_data_len = evbuffer_get_length(modified_response_data);
    if (modified_response_data_len) {
        evbuffer_remove_buffer(modified_response_data, request->response_data, modified_response_data_len);
    }
    evbuffer_free(modified_response_data);

    return ZPATH_RESULT_NO_ERROR;
}

#define MAX_DATA_CHUNK_SIZE 60000
size_t exporter_guac_proxy_process_server_ack_cb(guac_proto_parser *parser, const char *data) {
    struct exporter_conn *conn = parser->data;
    if (conn && conn->current_request && conn->current_request->guac_info) {
        // Process server ACK's only for file uploads
        if (exporter_guac_is_stream_active(&conn->current_request->guac_info->guac_in_stream[file_transfer]) ||
            (conn->current_request->guac_info->guac_in_stream[file_transfer].stream_id ==
                    parser->guac_ack_params.stream_id)) {
            // If the request is in the process of transferring a file listen to ACKs send next blob
            // Stop stream create timer
            struct exporter_stream_op *stream_op = &conn->current_request->guac_info->guac_in_stream[file_transfer];
            exporter_stream_create_del_timeout(conn->current_request);
            if (stream_op->stream_receive) {
                // Check for status code in ACK's before sending next blob
                if (parser->guac_ack_params.status_code) {
                    if (stream_op->stream_type == exporter_guac_file_upload) {
                        // Essential FT failure handling
                        exporter_guac_proxy_fail(conn->current_request,
                                                 UPLOAD_STATUS_INTERNAL_ERROR,
                                                 GUAC_UPLOAD_REMOTE_FAIL);
                    } else {
                        // Advanced FT failure handling
                        exporter_privileged_file_upload_fail(conn->current_request,
                                                             256,
                                                             GUAC_UPLOAD_REMOTE_FAIL);
                        goto exit;
                    }
                }  else if (conn->current_request->guac_info->decrypted_buffer) {
                    // Successful ACK, send next blob
                    stream_op->stream_receive = 0;
                    stream_op->stream_transmit = 1;

                    // Append next blob
                    unsigned char *in = EXPORTER_MALLOC(MAX_DATA_CHUNK_SIZE);
                    size_t bytes_in = evbuffer_remove(conn->current_request->guac_info->decrypted_buffer, in, MAX_DATA_CHUNK_SIZE);
                    if (bytes_in > 0) {
                        // uu64 encode
                        char *out = EXPORTER_MALLOC(base64_encoded_size(MAX_DATA_CHUNK_SIZE)+1);
                        base64_encode_binary(out, in, bytes_in);
                        exporter_guac_blob_instr_push(stream_op->stream_request,
                                parser->guac_ack_params.stream_id,
                                out);
                        exporter_stream_create_set_timeout(conn->current_request);
                        EXPORTER_FREE(out);
                    } else {
                        // Send end of the upload to guacd
                        exporter_guac_end_instr_push(stream_op->stream_request,
                                parser->guac_ack_params.stream_id);
                        if (conn->current_request->guac_info->decrypted_buffer) {
                            evbuffer_free(conn->current_request->guac_info->decrypted_buffer);
                            conn->current_request->guac_info->decrypted_buffer = NULL;
                        }
                    }
                    EXPORTER_FREE(in);
                } else if (stream_op->stream_type == exporter_guac_advanced_file_upload) {
                    //Advanced FT, guacd sends ACK until EOF to forward to Browser
                    goto exit;
                } else {
                    // Essential FT
                    if (conn->current_request->guac_info->capabilities_policy_bitmap & INSPECT_FILE_UPLOAD) {
                        // This is the last ACK for the stream, proxy will remove it in input thread
                        stream_op->stream_receive = 0;
                        stream_op->stream_transmit = 1;
                    } else {
                        // File not inspected, expecting more data from Browser
                        goto exit;
                    }
                }
                // Sent data has to be aligned with the input from Browser
                exporter_conn_trigger_read_cb(conn);
                if (stream_op->stream_type == exporter_guac_file_upload &&
                    conn->current_request->guac_info->capabilities_policy_bitmap & INSPECT_FILE_UPLOAD) {
                    exporter_guac_delete_instr_from_response_data(conn->current_request, parser);
                    return ZPATH_RESULT_NO_ERROR;
                }
            } else if ((stream_op->stream_type == exporter_guac_advanced_file_upload) ||
                 ((stream_op->stream_type == exporter_guac_file_upload) &&
                  (!(conn->current_request->guac_info->capabilities_policy_bitmap & INSPECT_FILE_UPLOAD)))) {
                if (stream_op->stream_type == exporter_guac_file_upload) {
                    // Progress update to Browser, end of stream in Essentials FT
                    exporter_guac_stream_instr_push(stream_op->stream_response,
                            registered_op_codes[OP_CODE_UPLOAD], stream_op->stream_id, "END", 5);
                }
                // File not inspected, close stream
                exporter_guac_reset_stream(conn->current_request, stream_op);
                exporter_guac_send_exporter_log_data(conn->current_request, "Success");
            }
        }
    }

exit:
    return parser->offset - parser->instruction_start;
}

size_t exporter_guac_proxy_process_server_body_cb(guac_proto_parser *parser, const char *data) {
    struct exporter_conn *conn = parser->data;
    if (conn && conn->current_request && conn->current_request->guac_info) {
        if (exporter_guac_is_stream_active(&conn->current_request->guac_info->guac_out_stream[file_transfer])) {
            struct exporter_stream_op *stream_op = &conn->current_request->guac_info->guac_out_stream[file_transfer];
            stream_op->stream_id = parser->guac_stream_params.stream_id;
            if (stream_op->stream_type == exporter_guac_file_download) {
                EXPORTER_DEBUG_FILE_TRANSFER("%s: Successfully processed body instruction sent by guacd for file download",
                        conn->current_request->name);

                // Stop stream create timer
                exporter_stream_create_del_timeout(conn->current_request);
            } else if (stream_op->stream_type == exporter_guac_dir_download) {
                // If we get a body for non-directory type here, disconnect - someone is messing with us
                if (strncmp(data + parser->guac_stream_params.mimetype.offset,
                            "application/vnd.glyptodon.guacamole.stream-index+json",
                            parser->guac_stream_params.mimetype.length)) {

                    conn->current_request->http_response_complete = 1;
                }
                // Stop stream create timer
                exporter_stream_create_del_timeout(conn->current_request);
            }
        }
    } else {
        EXPORTER_LOG(AL_ERROR, "Received body for file download but the conn/request is going away");
    }
    return parser->offset - parser->instruction_start;
}

size_t exporter_guac_proxy_process_server_sync_cb(guac_proto_parser *parser, const char *data __attribute__((unused)))
{
    exporter_guac_proxy_push_notification_to_browser(parser);

    /* Proxy will forward the sync instruction to browser */
    return parser->offset - parser->instruction_start;
}

size_t exporter_guac_proxy_process_server_clipboard_cb(guac_proto_parser *parser, const char *data) {
    struct exporter_conn *conn = parser->data;
    struct exporter_stream_op *stream_op = NULL;
    if (conn && conn->current_request && conn->current_request->guac_info) {

        EXPORTER_DEBUG_CLIPBOARD("%s: Received clipboard instruction from guacd for clipboard copy",
                                 conn->current_request->name);
        stream_op = &conn->current_request->guac_info->guac_out_stream[clipboard];

        if (is_pra_clipboard_disabled(conn->exporter_domain->customer_gid)) {
            exporter_guac_clipboardfailure_push(stream_op->stream_response, "Clipboard operation not supported");
            exporter_guac_delete_instr_from_response_data(conn->current_request, parser);
            EXPORTER_LOG(AL_ERROR, "%s: Clipboard feature flag is disabled, clipboard paste denied", conn->current_request->name);
            EXPORTER_DEBUG_CLIPBOARD("%s: Clipboard feature flag is disabled, clipboard paste denied", conn->current_request->name);
            return ZPATH_RESULT_NO_ERROR;
        }

        if (exporter_guac_is_stream_active(&conn->current_request->guac_info->guac_out_stream[clipboard]) ||
            exporter_guac_is_stream_active(&conn->current_request->guac_info->guac_in_stream[clipboard])) {
            exporter_guac_clipboardfailure_push(stream_op->stream_response,
                                                "CLIPBOARD OPERATION ALREADY IN PROGRESS");
            exporter_guac_delete_instr_from_response_data(conn->current_request, parser);
            EXPORTER_LOG(AL_ERROR, "%s: CLIPBOARD OPERATION ALREADY IN PROGRESS(stream_id %d)",
                         conn->current_request->name, parser->guac_stream_params.stream_id);
            return ZPATH_RESULT_NO_ERROR;
        }

        int stream_id = parser->guac_stream_params.stream_id;
        exporter_guac_init_stream(stream_op, stream_id, exporter_guac_clipboard_copy);

        exporter_guac_api_stats_increment(GUAC_CLIPBOARD_COPY);

        // If capability policy denies the action, delete the instr
        if (exporter_process_priv_capabilities_clipboard_copy(conn->current_request)) {
            EXPORTER_LOG(AL_ERROR, "%s: Capabilities check has failed", conn->current_request->name);
            exporter_guac_delete_instr_from_response_data(conn->current_request, parser);
            return ZPATH_RESULT_NO_ERROR;
        }
    } else {
        EXPORTER_LOG(AL_ERROR, "Received clipboard instruction from guacd for copy but the conn/request is going away");
    }
    return parser->offset - parser->instruction_start;
}
size_t exporter_guac_proxy_process_server_blob_cb(guac_proto_parser *parser, const char *data) {
    struct exporter_conn *conn = parser->data;
    unsigned char *buff = NULL;
    int nodes = 0;
    struct exporter_stream_op *stream_op = NULL;

    if (conn && conn->current_request && conn->current_request->guac_info) {
        // Does it match the current stream?
        if (exporter_guac_is_stream_active(&conn->current_request->guac_info->guac_out_stream[file_transfer]) ||
                exporter_guac_is_stream_active(&conn->current_request->guac_info->guac_out_stream[clipboard])) {

            if (conn->current_request->guac_info->guac_out_stream[clipboard].stream_id ==
                    parser->guac_stream_params.stream_id) {
                EXPORTER_DEBUG_CLIPBOARD("%s: Received blob instruction from guacd for clipboard copy",
                                         conn->current_request->name);

                stream_op = &conn->current_request->guac_info->guac_out_stream[clipboard];

                if (is_clipboard_max_char_exceeded(conn->current_request,
                                                   data + parser->guac_stream_params.blob.offset,
                                                   parser->guac_stream_params.blob.length)) {
                    char msg[256] = {'\0'};
                    snprintf(msg, sizeof(msg), "Clipboard operation not supported for characters > %d", get_max_clipboard_size(conn->exporter_domain->customer_gid));
                    exporter_guac_clipboardfailure_push(stream_op->stream_response, msg);
                    exporter_guac_delete_instr_from_response_data(conn->current_request, parser);
                        exporter_guac_reset_stream(conn->current_request, stream_op);
                    EXPORTER_LOG(AL_ERROR, "%s: Clipboard copy characters exceeded allowed limit", conn->current_request->name);
                    exporter_guac_api_stats_increment(GUAC_CLIPBOARD_COPY_EXCEED_LIMIT);
                    return ZPATH_RESULT_NO_ERROR;
                }
            // This is a get directory response
            } else if ((conn->current_request->guac_info->guac_out_stream[file_transfer].stream_id ==
                                        parser->guac_stream_params.stream_id) &&
                    (conn->current_request->guac_info->guac_out_stream[file_transfer].stream_type ==
                                        exporter_guac_dir_download)) {
                // Enforce limit of nodes in directory for RDP
                if (strncasecmp(conn->current_request->sra_host_protocol, "RDP", 3) == 0) {
                    /*
                     * Extract number of nodes in the directory - if larger than allowed, future uploads
                     * and downloads are denied.
                     */

                    buff = EXPORTER_MALLOC(BLOB_BUFFER_SIZE);

                    // Decode directory blob
                    int n_bytes = base64_decode_binary(buff,
                            data + parser->guac_stream_params.blob.offset,
                            parser->guac_stream_params.blob.length);

                    // How many nodes in directory list?
                    if (n_bytes > 0) {
                        buff[n_bytes] = '\0';
                        nodes = exporter_guac_get_dir_nodes_count(conn->current_request, (char *) buff);
                     }

                     conn->current_request->guac_info->rdp_dir_list_cnt = nodes > RDP_MAX_NODES_CNT + 1 ? RDP_MAX_NODES_CNT + 1 : nodes;
                     EXPORTER_FREE(buff);
                     buff = NULL;
                }
            }
        }
    } else {
        EXPORTER_LOG(AL_ERROR, "Received body for file download/clipboard but the conn/request is going away");
    }
    return parser->offset - parser->instruction_start;
}

size_t exporter_guac_proxy_process_server_end_cb(guac_proto_parser *parser, const char *data) {
    struct exporter_conn *conn = parser->data;
    if (conn && conn->current_request && conn->current_request->guac_info) {
        if (exporter_guac_is_stream_active(&conn->current_request->guac_info->guac_out_stream[file_transfer]) ||
            exporter_guac_is_stream_active(&conn->current_request->guac_info->guac_out_stream[clipboard])) {

            if (conn->current_request->guac_info->guac_out_stream[clipboard].stream_id ==
                        parser->guac_stream_params.stream_id) {
                EXPORTER_DEBUG_CLIPBOARD("%s: Received end instruction from guacd for clipboard copy",
                                         conn->current_request->name);
                exporter_guac_reset_stream(conn->current_request,
                                               &conn->current_request->guac_info->guac_out_stream[clipboard]);
            } else if ((conn->current_request->guac_info->guac_out_stream[file_transfer].stream_id ==
                                        parser->guac_stream_params.stream_id) &&
                        (conn->current_request->guac_info->guac_out_stream[file_transfer].stream_type ==
                                        exporter_guac_file_download)) {
                /* Reset stream id at the end of the file download
                 * guacd sends an end instruction after the download is complete
                 */
                EXPORTER_DEBUG_FILE_TRANSFER("%s: Successfully processed end instruction sent by guacd for file download",
                        conn->current_request->name);
                // Log only if the download is for a file, not a directory
                if (conn->current_request->guac_info->file_log.file_name) {
                    // Download is successful, send exporter log
                    exporter_guac_send_exporter_log_data(conn->current_request, "Success");

                }
                exporter_guac_reset_stream(conn->current_request, &conn->current_request->guac_info->guac_out_stream[file_transfer]);
            } else if ((conn->current_request->guac_info->guac_out_stream[file_transfer].stream_id ==
                                        parser->guac_stream_params.stream_id) &&
                        (conn->current_request->guac_info->guac_out_stream[file_transfer].stream_type ==
                                        exporter_guac_dir_download)) {
                /* Reset stream if for getDir end */
                exporter_guac_reset_stream(conn->current_request, &conn->current_request->guac_info->guac_out_stream[file_transfer]);
            }
        }
    } else {
        EXPORTER_LOG(AL_ERROR, "Received an end after file download/clipboard but the conn/request is going away");
    }
    return parser->offset - parser->instruction_start;
}

int exporter_guac_proxy_process_server_args_cb(guac_proto_parser *parser, const char *data) {
    struct exporter_conn *conn = parser->data;
    size_t len = parser->offset - parser->instruction_start;
    struct exporter_request *request;
    int res = 0;

    if (!(conn && conn->current_request && conn->current_request->guac_info)) {
        parser->state = PARSING_ERROR;
        return 0;
    }

    request = conn->current_request;

    if (!request) {
        parser->state = PARSING_ERROR;
        return 0;
    }

    if (request->guac_info->guac_parser.guac_conn_state == GUAC_STATE_HANDSHAKE_SELECT_SENT) {
        if (!request->is_proxy_conn && request->guac_info->is_pra_interactive_auth_disabled) {
            res = exporter_fetch_privileged_credentials(request);
            if (res == ZPATH_RESULT_ASYNCHRONOUS) {
                return 0;
            } else if (res != ZPATH_RESULT_NO_ERROR) {
                // Drain as this should not go back to Browser
                evbuffer_drain(request->response_data, len);
                return -1;
            }
            if (!len) len = evbuffer_get_length(request->response_data);
        }

        // Arguments received from guacd, do next step in the handshake.
        char *args = EXPORTER_CALLOC(len + 1);
        evbuffer_copyout(request->response_data, args, len);
        args[len] = '\0';

        // Switch to the new state
        request->guac_info->guac_parser.guac_conn_state = GUAC_STATE_HANDSHAKE_ARGS_RCVD;

        if (exporter_conn_guac_args_cb(request, args) != ZPATH_RESULT_NO_ERROR) {
            EXPORTER_FREE(args);
            parser->state = PARSING_ERROR;
            request->guac_info->guac_parser.guac_conn_state = GUAC_STATE_ERROR;
            EXPORTER_LOG(AL_ERROR, "%s: Guac args callback to send connect handshake instruction failed", request->name);
            return 0;
        }
        EXPORTER_FREE(args);

        // Drain as this should not go back to Browser
        evbuffer_drain(request->response_data, len);

        // Nothing to send to Browser
        len = 0;
    } else {
        parser->state = PARSING_ERROR;
        request->guac_info->guac_parser.guac_conn_state = GUAC_STATE_ERROR;
        len = 0;
    }

    return len;
}

static void exporter_guac_proxy_process_session_proctoring_on_primary(struct exporter_request *request)
{
    /* by default enable keyboard mouse for host, consider if control capability not set */
    if (!request->is_proxy_conn) {
        exporter_pra_session_control_enable_host_key_mouse_default(request);
    }
    /* this is a primary-exporter case where user is creating a PRA session entry */
    if (request->guac_info->is_session_share_enabled && !request->is_proxy_conn) {
        char *conn_id = request->guac_info->guac_parser.guac_tunnel_params.conn_id;
        if (!conn_id) {
            return;
        }

        exporter_session_update_request_infer_key(request);
        request->guac_info->session_start_time = epoch_us() / 1000l;

        int res = exporter_update_pra_sess_ostore_data_create_event(request);
        if (res && (res != ZPATH_RESULT_ASYNCHRONOUS)) {
            EXPORTER_LOG(AL_ERROR, "[SESS_PROC] %s: Create event failed: %s", request->name, zpath_result_string(res));
        } else {
            EXPORTER_DEBUG_SESSION_SHARING("[SESS_PROC] %s: Create event is successful: %s",
                request->name, zpath_result_string(res));
        }

        /* store conn id in hash */
        EXPORTER_DEBUG_SESSION_SHARING("SESS_PROC storing conn id %s in proxy_mtunnel_id hash",conn_id+1);
        int conn_id_len = strnlen(conn_id, GUAC_PROCTORED_SESS_ID_LEN);
        if (conn_id_len >= GUAC_PROCTORED_SESS_ID_LEN) {
            EXPORTER_LOG(AL_ERROR, "[SESS_PROC] %s: Failed to store conn ID in hash.: Conn ID length exceeded", request->name);
        } else {
            ZPATH_RWLOCK_WRLOCK(&(global_exporter.lock), __FILE__, __LINE__);
            res = zhash_table_store(global_exporter.proxy_conn_id, conn_id+1, conn_id_len-1, 0, request);
            if (res) {
                EXPORTER_DEBUG_SESSION_SHARING("SESS_PROC: Failed to store conn id %s in proxy_mtunnel_id hash",conn_id+1);
            }
            ZPATH_RWLOCK_UNLOCK(&(global_exporter.lock), __FILE__, __LINE__);
        }

        exporter_sharing_protocol_stats_update(request, 1);
        exporter_sharing_stats_inc(proctor_success_type);
    }
}

/* store proxy request for mtunnel id in hash */
void exporter_store_proxy_request(struct exporter_request *request) {
    EXPORTER_DEBUG_SESSION_SHARING("SESS_PROC %s: storing mtunnel id %s in proxy_mtunnel_id hash", request->name, request->mt->mtunnel_id);
    size_t mtunnel_len = strlen(request->mt->mtunnel_id);
    if (mtunnel_len < PROXY_MTUNNEL_ID_BYTES_TEXT_MIN) {
        EXPORTER_LOG(AL_ERROR, "[SESS_PROC] %s: Failed to store mtunnel id in hash: invalid mtunnel_id %s", request->name, request->mt->mtunnel_id);
    } else {
        ZPATH_RWLOCK_WRLOCK(&(global_exporter.lock), __FILE__, __LINE__);
        int res = zhash_table_store(global_exporter.proxy_mtunnel_id, request->mt->mtunnel_id, mtunnel_len, 0, request);
        if (res) {
            EXPORTER_LOG(AL_ERROR, "SESS_PROC %s: Failed to store mtunnel id %s in proxy_mtunnel_id hash", request->name, request->mt->mtunnel_id);
        }
        ZPATH_RWLOCK_UNLOCK(&(global_exporter.lock), __FILE__, __LINE__);
    }
}

static void exporter_guac_proxy_process_session_proctoring_on_proxy(struct exporter_request *request)
{
    if (request->is_proxy_conn && request->guac_info->is_session_share_monitor && request->share_ctxt.user_connect_allowed) {
        char *user_name = exporter_user_portal_request_state_get_name(request->portal_info);
        if (request->is_guac_sess_primary_exporter) {
            EXPORTER_DEBUG_SESSION_SHARING("SESS_PROC: user %s join request, single exporter case", user_name);
            struct exporter_request *orig_request = NULL;
            orig_request = lookup_orig_guac_request(request);
            if (orig_request) {
                exporter_proctoring_schedule_user_event(orig_request,
                    exporter_shares_session_event_join, user_name, request->guac_info->capabilities_policy_id, request);

                /* Single exporter case, push console information to browser */
                exporter_guac_proxy_browser_push_notification_console_info(request, orig_request, NULL);

                exporter_sharing_stats_inc(proctor_session_join_success_type);
            }
        } else {
            /* 2 exporter case. Send user join event to primary exporter */
            request->mt->event_type = exporter_shares_session_event_join;
            if (user_name && !request->mt->user_email) {
                char *user = EXPORTER_STRDUP(user_name, strlen(user_name));
                request->mt->user_email = user;
            }

            if (request->mt->mtunnel_id) {
                exporter_store_proxy_request(request);
            }

            zpn_exporter_pra_guac_proxy_data(request->mt);
            EXPORTER_DEBUG_SESSION_SHARING("SESS_PROC: Sending conn_id %s to primary exporter for %s, username %s, event %d",
                request->mt->pra_conn_id, request->name, user_name, request->mt->event_type);
        }
    }
}

static void exporter_guac_proxy_process_session_proctoring(struct exporter_request *request)
{
    exporter_guac_proxy_process_session_proctoring_on_primary(request);
    exporter_guac_proxy_process_session_proctoring_on_proxy(request);
}

size_t exporter_guac_proxy_process_server_ready_cb(guac_proto_parser *parser, const char *data) {
    struct exporter_conn *conn = parser->data;
    size_t len = parser->offset - parser->instruction_start;
    struct exporter_request *request;

    if (!(conn && conn->current_request && conn->current_request->guac_info)) {
        parser->state = PARSING_ERROR;
        return 0;
    }

    request = conn->current_request;

    if (!request) {
        parser->state = PARSING_ERROR;
        return 0;
    }

    /* Drain just the ready instruction */
    evbuffer_drain(request->response_data, len);
    len = 0;
    request->guac_info->guac_parser.guac_conn_state = GUAC_STATE_HANDSHAKE_READY_RCVD;
    char *conn_id = request->guac_info->guac_parser.guac_tunnel_params.conn_id;
    if (conn_id) {
        // Ready signal received from guacd, handshake done.
        if (exporter_conn_guac_ready_cb(request) == ZPATH_RESULT_NO_ERROR) {
            EXPORTER_LOG(AL_INFO,"%s: Connection ID from ready instruction: %s", request->name, conn_id);
            exporter_populate_log_conn_id(request, conn_id);
            exporter_guac_proxy_process_session_proctoring(request);
        } else {
            parser->state = PARSING_ERROR;
            request->guac_info->guac_parser.guac_conn_state = GUAC_STATE_ERROR;
            len = 0;
        }
    } else {
        parser->state = PARSING_ERROR;
        request->guac_info->guac_parser.guac_conn_state = GUAC_STATE_ERROR;
        len = 0;
    }

    if (request->mt) {
        zpn_fohh_client_exporter_send_exporter_log_data(request->mt);
    }

    return len;
}

int exporter_guac_proxy_process_ping_cb(guac_proto_parser *parser, const char *data) {
    struct exporter_conn *conn = parser->data;
    struct exporter_request *request;
    int res = ZPATH_RESULT_NO_ERROR;

    if (conn) {
        request = conn->current_request;
        if (request) {

            ZPATH_MUTEX_LOCK(&(request->lock), __FILE__, __LINE__);

            // If this is is a proxy connection to other exporter, enforce reauth timeout here
            if (request->is_proxy_conn) {
                // Enforce reauth timeout here

                int64_t now_s = epoch_s();
                int64_t elapsed = now_s - request->policy_state->saml_not_before;

                if ((request->proxy_conn_reauth_timeout > 0) &&
                        (now_s > (request->policy_state->saml_not_before + request->proxy_conn_reauth_timeout))) {
                    EXPORTER_DEBUG_REQUEST_POLICY_STATE("%s: AUTHENTICATION EXPIRED: now: %ld, saml_not_before: %ld, timeout: %ld, elapsed: %ld",
                            request->name, (long) now_s,
                            (long)request->policy_state->saml_not_before, (long)request->proxy_conn_reauth_timeout, (long)elapsed);
                    res = exporter_request_check_redirect_for_ot(request, EXPORTER_DOMAIN_AUTH);
                    if (res != ZPATH_RESULT_NO_ERROR) {
                        EXPORTER_LOG(AL_ERROR, "%s: Reauth message to browser failure.", conn->current_request->name);
                    }
                    goto exit;
                }
            }

            // Copy PING instruction from input
            evbuffer_prepend(request->response_data,
                    data + parser->instruction_start,
                    parser->offset - parser->instruction_start);

            // Send PING response back
            exporter_guac_send_to_browser(request,
                    request->response_data,
                    parser->offset - parser->instruction_start);

exit:
            ZPATH_MUTEX_UNLOCK(&(conn->current_request->lock), __FILE__, __LINE__);

        }
    }

    return res;
}

int exporter_guac_proxy_process_get_cb(guac_proto_parser *parser, const char *data) {
    struct exporter_conn *conn = parser->data;
    if (conn && conn->current_request && conn->current_request->guac_info) {

        struct exporter_stream_op *stream_op = &conn->current_request->guac_info->guac_out_stream[file_transfer];

        if (is_pra_ft_disabled(conn->exporter_domain->customer_gid)) {
            exporter_guac_instr_push(stream_op->stream_response,
                                     registered_op_codes[OP_CODE_DOWNLOAD_FAILURE],
                                     "CLIENT FORBIDDEN",
                                     0);
            return ZPATH_RESULT_ERR;
        }

        if (exporter_guac_is_stream_active(&conn->current_request->guac_info->guac_out_stream[file_transfer]) ||
                exporter_guac_is_stream_active(&conn->current_request->guac_info->guac_in_stream[file_transfer]) ||
                conn->current_request->guac_info->is_cancel_scan_active) {
            // Other file transfer operation already in progress
            exporter_guac_instr_push(stream_op->stream_response,
                                     registered_op_codes[OP_CODE_DOWNLOAD_FAILURE],
                                     "FILE OPERATION ALREADY IN PROGRESS",
                                     0);
            EXPORTER_LOG(AL_ERROR, "%s: FILE OPERATION ALREADY IN PROGRESS(object_id %d)",
                    conn->current_request->name, parser->guac_stream_params.object_id);
            return ZPATH_RESULT_NO_ERROR;
        }

        exporter_guac_init_stream(stream_op, -1, exporter_guac_file_download);

        /* Reset previous file transfer details */
        if (conn->current_request->guac_info->file_log.file_name) {
            exporter_request_free_file_log(conn->current_request);
        }
        memset(&conn->current_request->guac_info->file_log, '\0', sizeof(conn->current_request->guac_info->file_log));

        /*
         * Copy stream name, start time for diag log.
         */
        conn->current_request->guac_info->file_log.file_name = EXPORTER_MALLOC(parser->guac_stream_params.stream_name.length+1);
        memcpy(conn->current_request->guac_info->file_log.file_name,
                data + parser->guac_stream_params.stream_name.offset,
                parser->guac_stream_params.stream_name.length);
        conn->current_request->guac_info->file_log.file_name[parser->guac_stream_params.stream_name.length] = '\0';
        conn->current_request->guac_info->file_log.start_ts = epoch_s();

        exporter_guac_api_stats_increment(GUAC_FILE_TRANSFER_DOWNLOAD);

        if (exporter_process_priv_capabilities_download(conn->current_request)) {
            EXPORTER_LOG(AL_ERROR, "%s: Capabilities check has failed", conn->current_request->name);
            return ZPATH_RESULT_ERR;
        }
        // Forward instruction to guacd
        exporter_guac_proxy_forward_cb(parser, data);
    } else {
        EXPORTER_LOG(AL_ERROR, "Unable to check policy for file download as conn or req is NULL");
    }
    return ZPATH_RESULT_NO_ERROR;
}

int exporter_guac_proxy_process_get_dir_cb(guac_proto_parser *parser, const char *data) {
    struct exporter_conn *conn = parser->data;
    if (conn && conn->current_request && conn->current_request->guac_info) {

        struct exporter_stream_op *stream_op = &conn->current_request->guac_info->guac_out_stream[file_transfer];

        if (exporter_guac_is_stream_active(&conn->current_request->guac_info->guac_out_stream[file_transfer]) ||
                exporter_guac_is_stream_active(&conn->current_request->guac_info->guac_in_stream[file_transfer]) ||
                conn->current_request->guac_info->is_cancel_scan_active) {
            // Other file transfer operation already in progress
            exporter_guac_instr_push(stream_op->stream_response,
                                     registered_op_codes[OP_CODE_DOWNLOAD_FAILURE],
                                     "FILE OPERATION ALREADY IN PROGRESS",
                                     0);
            EXPORTER_LOG(AL_ERROR, "%s: FILE OPERATION ALREADY IN PROGRESS(object_id %d)",
                    conn->current_request->name, parser->guac_stream_params.object_id);
            return ZPATH_RESULT_ERR;
        }

        if (is_pra_ft_disabled(conn->exporter_domain->customer_gid)) {
            // Send downloadFailure with status code 1 to indicate getDir failure
            exporter_guac_instr_push(stream_op->stream_response, registered_op_codes[OP_CODE_DOWNLOAD_FAILURE], "Policy Error", 1);
            return ZPATH_RESULT_ERR;
        }

        exporter_guac_init_stream(stream_op, -1, exporter_guac_dir_download);

        // Extract stream name from getDir
        char stream_name[parser->guac_stream_params.stream_name.length + 1];
        memcpy(stream_name,
                data + parser->guac_stream_params.stream_name.offset,
                parser->guac_stream_params.stream_name.length);
        stream_name[parser->guac_stream_params.stream_name.length] = '\0';

        // Change directory for RDP is not allowed!
        if ((strncasecmp(conn->current_request->sra_host_protocol, "RDP", 3) == 0) &&
                !(parser->guac_stream_params.stream_name.length == 1 &&
                        *(data + parser->guac_stream_params.stream_name.offset) == '/')) {
            exporter_guac_reset_stream(conn->current_request, stream_op);
            return ZPATH_RESULT_ERR;
        }

        // Send get instruction to guacd
        exporter_guac_get_instr_push(conn->current_request->request_body,
                parser->guac_stream_params.object_id, stream_name);
        // Create timeout
        exporter_stream_create_set_timeout(conn->current_request);
    } else {
        EXPORTER_LOG(AL_ERROR, "Unable to serve getDir as conn or req is NULL");
    }
    return ZPATH_RESULT_NO_ERROR;
}

int exporter_guac_proxy_process_put_cb(guac_proto_parser *parser, const char *data) {
    struct exporter_conn *conn = parser->data;
    struct exporter_stream_op *stream_op = NULL;

    if (conn && conn->current_request && conn->current_request->guac_info) {

        stream_op = &conn->current_request->guac_info->guac_in_stream[file_transfer];

        if (is_pra_ft_disabled(conn->exporter_domain->customer_gid)) {
            exporter_guac_stream_instr_push(stream_op->stream_response, registered_op_codes[OP_CODE_ACK],
                                            parser->guac_stream_params.stream_id, "CLIENT FORBIDDEN", 771);
            return ZPATH_RESULT_ERR;
        }

        if (exporter_guac_is_stream_active(&conn->current_request->guac_info->guac_out_stream[file_transfer]) ||
                exporter_guac_is_stream_active(&conn->current_request->guac_info->guac_in_stream[file_transfer]) ||
                conn->current_request->guac_info->is_cancel_scan_active) {
            exporter_guac_stream_instr_push(stream_op->stream_response, registered_op_codes[OP_CODE_ACK],
                    parser->guac_stream_params.stream_id, "FILE OPERATION ALREADY IN PROGRESS", 517);
            EXPORTER_LOG(AL_ERROR, "%s: FILE OPERATION ALREADY IN PROGRESS(stream_id %d)",
                    conn->current_request->name, parser->guac_stream_params.stream_id);
            return ZPATH_RESULT_NO_ERROR;
        }

        int stream_id = parser->guac_stream_params.stream_id;
        exporter_guac_init_stream(stream_op, stream_id, exporter_guac_file_upload);

        /* Reset previous file transfer details */
        if (conn->current_request->guac_info->file_log.file_name) {
            exporter_request_free_file_log(conn->current_request);
        }
        memset(&conn->current_request->guac_info->file_log, '\0', sizeof(conn->current_request->guac_info->file_log));

        conn->current_request->guac_info->file_log.file_name = EXPORTER_MALLOC(parser->guac_stream_params.stream_name.length+1);
        memcpy(conn->current_request->guac_info->file_log.file_name,
                data + parser->guac_stream_params.stream_name.offset,
                parser->guac_stream_params.stream_name.length);
        conn->current_request->guac_info->file_log.file_name[parser->guac_stream_params.stream_name.length] = '\0';
        conn->current_request->guac_info->file_log.start_ts = epoch_s();

        if (exporter_guac_proxy_file(conn->current_request,
                data + parser->instruction_start, parser->offset - parser->instruction_start) != ZPATH_RESULT_NO_ERROR) {
            EXPORTER_LOG(AL_ERROR, "Failed to start file upload");
            exporter_guac_proxy_fail(conn->current_request,
                                     UPLOAD_STATUS_INTERNAL_ERROR,
                                     GUAC_UPLOAD_FILE_FAILED);
        }
        evbuffer_add(stream_op->stream_request,
                data + parser->instruction_start, parser->offset - parser->instruction_start);

        exporter_guac_api_stats_increment(GUAC_FILE_TRANSFER_UPLOAD);

        if (exporter_process_priv_capabilities_upload(conn->current_request)) {
            EXPORTER_LOG(AL_ERROR, "%s: Capabilities check has failed", conn->current_request->name);
            return ZPATH_RESULT_ERR;
        }
    } else {
        EXPORTER_LOG(AL_ERROR, "Unable to check policy for file upload as conn or req is NULL");
    }
    return ZPATH_RESULT_NO_ERROR;
}

int exporter_guac_proxy_process_blob_cb(guac_proto_parser *parser, const char *data) {
    struct exporter_conn *conn = parser->data;
    unsigned char *buff = NULL;
    char msg[256];
    int res = ZPATH_RESULT_NO_ERROR;
    struct exporter_stream_op *stream_op = NULL;

    if (conn && conn->current_request && conn->current_request->guac_info) {

    if (exporter_guac_is_stream_active(&conn->current_request->guac_info->guac_in_stream[file_transfer]) ||
        exporter_guac_is_stream_active(&conn->current_request->guac_info->guac_in_stream[clipboard])) {

            if (conn->current_request->guac_info->guac_in_stream[clipboard].stream_id ==
                                        parser->guac_stream_params.stream_id) {

                stream_op = &conn->current_request->guac_info->guac_in_stream[clipboard];
                EXPORTER_DEBUG_CLIPBOARD("%s: Received blob instruction from browser for clipboard paste",
                                         conn->current_request->name);
                if (is_clipboard_max_char_exceeded(conn->current_request,
                                                   data + parser->guac_stream_params.blob.offset,
                                                   parser->guac_stream_params.blob.length)) {
                    char msg[256] = {'\0'};
                    snprintf(msg, sizeof(msg), "Clipboard operation not supported for characters > %d", get_max_clipboard_size(conn->exporter_domain->customer_gid));
                    evbuffer_drain(stream_op->stream_request, evbuffer_get_length(stream_op->stream_request));
                    exporter_guac_stream_instr_push(stream_op->stream_response, registered_op_codes[OP_CODE_ACK],
                            parser->guac_stream_params.stream_id, msg, 768);
                    exporter_guac_reset_stream(conn->current_request, stream_op);
                    EXPORTER_LOG(AL_ERROR, "%s: Clipboard paste characters exceeded allowed limit", conn->current_request->name);
                    exporter_guac_api_stats_increment(GUAC_CLIPBOARD_PASTE_EXCEED_LIMIT);
                    return ZPATH_RESULT_NO_ERROR;
                }
                // Save clipboard blob and send ACK to Browser
                evbuffer_add(stream_op->stream_request,
                        data + parser->instruction_start, parser->offset - parser->instruction_start);

                exporter_guac_stream_instr_push(stream_op->stream_response,
                                                registered_op_codes[OP_CODE_ACK],
                                                parser->guac_stream_params.stream_id,
                                                "OK (DATA RECEIVED)", 0);
                return ZPATH_RESULT_NO_ERROR;
            } else if (conn->current_request->guac_info->guac_in_stream[file_transfer].stream_id ==
                                            parser->guac_stream_params.stream_id) {
                stream_op = &conn->current_request->guac_info->guac_in_stream[file_transfer];

                // Allocate space for extracted blob
                buff = EXPORTER_MALLOC(BLOB_BUFFER_SIZE);

                // Decode
                int n_bytes = base64_decode_binary(buff,
                        data + conn->guac_parser.guac_stream_params.blob.offset,
                        conn->guac_parser.guac_stream_params.blob.length);

                if (n_bytes < 0) {
                    // Send ack with error: CLIENT_BAD_REQUEST
                    exporter_guac_stream_instr_push(
                            stream_op->stream_response, registered_op_codes[OP_CODE_ACK],
                            conn->guac_parser.guac_stream_params.stream_id, GUAC_UPLOAD_FAILED_TO_DECODE, 768);
                    exporter_guac_proxy_fail(conn->current_request,
                            UPLOAD_STATUS_INTERNAL_ERROR,
                            GUAC_UPLOAD_FAILED_TO_DECODE);
                    res = ZPATH_RESULT_ERR;
                    goto do_exit;
                }

                size_t max_file_size = get_max_file_size(conn->exporter_domain->customer_gid,
                        conn->current_request->guac_info->capabilities_policy_bitmap & INSPECT_FILE_UPLOAD);
                if (conn->current_request->guac_info->file_info.upload_file_size > max_file_size * 1024 * 1024) {
                    EXPORTER_DEBUG_FILE_TRANSFER("%s: uploaded file exceeds maximum size supported for upload",
                            conn->current_request->name);
                    if (!(conn->current_request->guac_info->capabilities_policy_bitmap & INSPECT_FILE_UPLOAD)) {
                        // Send end of the stream to guacd so it closes the file
                        exporter_guac_stream_instr_push(stream_op->stream_request,
                                registered_op_codes[OP_CODE_END],
                                conn->guac_parser.guac_stream_params.stream_id, msg, 783);
                        exporter_guac_proxy_send(conn->current_request, stream_op);
                    }
                    snprintf(msg, sizeof(msg), "%s %luMB", GUAC_UPLOAD_FILESIZE_ERR_MSG, max_file_size);
                    // Send ack with error: CLIENT_TOO_MANY
                    exporter_guac_stream_instr_push(stream_op->stream_response,
                            registered_op_codes[OP_CODE_ACK],
                            conn->guac_parser.guac_stream_params.stream_id, msg, 797);
                    exporter_guac_proxy_fail(conn->current_request, UPLOAD_STATUS_INTERNAL_ERROR, msg);

                    res = ZPATH_RESULT_ERR;
                    goto do_exit;
                }

                conn->current_request->guac_info->file_info.upload_file_size += n_bytes;

                if (conn->current_request->guac_info->file_scan.upload_file) {

                    // Add this chunk to temp file
                    res = exporter_guac_proxy_blob(conn->current_request, buff, n_bytes);
                    if (res == ZPATH_RESULT_NO_ERROR) {
                        // ACK back to Browser
                        exporter_guac_stream_instr_push(stream_op->stream_response,
                                registered_op_codes[OP_CODE_ACK], parser->guac_stream_params.stream_id, "OK (DATA RECEIVED)", 0);
                    } else {
                        goto do_exit;
                    }
                } else {
                    // File not inspected, just forward
                    exporter_guac_proxy_forward_cb(parser, data);
                }
            }
        }
    }
do_exit:

    if (buff) {
        EXPORTER_FREE(buff);
    }
    return res;
}

int exporter_guac_proxy_process_end_cb(guac_proto_parser *parser, const char *data) {
    struct exporter_conn *conn = parser->data;
    struct exporter_stream_op *stream_op = NULL;
    if (conn && conn->current_request && conn->current_request->guac_info) {
        if (exporter_guac_is_stream_active(&conn->current_request->guac_info->guac_in_stream[file_transfer]) ||
                exporter_guac_is_stream_active(&conn->current_request->guac_info->guac_in_stream[clipboard])) {

            if (conn->current_request->guac_info->guac_in_stream[clipboard].stream_id ==
                    parser->guac_stream_params.stream_id) {

                stream_op = &conn->current_request->guac_info->guac_in_stream[clipboard];
                EXPORTER_DEBUG_CLIPBOARD("%s: Received end instruction from browser for clipboard paste",
                                         conn->current_request->name);
                // Forward clipboard instructions now
                evbuffer_add(stream_op->stream_request,
                        data + parser->instruction_start, parser->offset - parser->instruction_start);

                evbuffer_remove_buffer(stream_op->stream_request,
                        conn->current_request->request_body,
                        evbuffer_get_length(stream_op->stream_request));

                exporter_guac_reset_stream(conn->current_request, stream_op);
                return ZPATH_RESULT_NO_ERROR;
            } if (conn->current_request->guac_info->guac_in_stream[file_transfer].stream_id ==
                                    parser->guac_stream_params.stream_id) {

                stream_op = &conn->current_request->guac_info->guac_in_stream[file_transfer];

                if (conn->current_request->guac_info->file_scan.upload_file) {
                    exporter_guac_stream_instr_push(stream_op->stream_response,
                            registered_op_codes[OP_CODE_ACK], parser->guac_stream_params.stream_id, "OK (STREAM END)", 0);
                    EXPORTER_DEBUG_FILE_TRANSFER("%s: Successfully proxied the end instruction", conn->current_request->name);
                    // Send file to ZIA scan
                    if (exporter_guac_proxy_end(conn->current_request) != ZPATH_RESULT_NO_ERROR) {
                        EXPORTER_LOG(AL_ERROR, "%s: Processing end guac instruction and scan invocation errored out", conn->current_request->name);
                        return ZPATH_RESULT_NO_ERROR;
                    }
                } else {
                    // File not inspected, pass "end" to guacd
                    exporter_guac_proxy_forward_cb(parser, data);
                    // No more data from Browser, stream ends once guacd sends ack
                    stream_op->stream_receive = 0;
                }
            }
        }
    }
    return ZPATH_RESULT_NO_ERROR;
}

void exporter_guac_proxy_process_cancel_cb(guac_proto_parser *parser, const char *data) {
    struct exporter_conn *conn = parser->data;
    if (conn && conn->current_request && conn->current_request->guac_info) {
          /* Send an ack with status 518. Status code other than 0 will end the stream
           * For Get - send an ack to guacd and clean up resetting params
           * For Put - without inspect - send an ack to guacd and clean up resetting params
           * For Put - with inspect - if scan is in progress, send message to browser - cancel in progress
           *                        - do not cleanup until ZIA callback or timeout is triggered
           *                        - cancel will complete once the ZIA callback is triggered
           *                        - if scan is done - send an ack to guacd and clean up resetting params
           */

        struct exporter_stream_op *stream_op = NULL;
        int is_download_active = 0;
        int is_upload_active = 0;
        if (exporter_guac_is_stream_active(&conn->current_request->guac_info->guac_in_stream[file_transfer])) {
            stream_op = &conn->current_request->guac_info->guac_in_stream[file_transfer];
            is_upload_active = 1;
        } else {
            stream_op = &conn->current_request->guac_info->guac_out_stream[file_transfer];
            is_download_active = 1;
        }
        if (conn->current_request->guac_info->is_ft_async_active) {
            if (conn->current_request->guac_info->is_cancel_scan_active) {
                //Received a cancel again. Nothing to do as cancel is already in progress.
                EXPORTER_LOG(AL_ERROR, "%s: Received cancel while cancel scan is in progress", conn->current_request->name);
                return;
            }
            conn->current_request->guac_info->is_cancel_scan_active = 1;
            /* Send message to browser that an async cancel is in progress*/
            exporter_guac_stream_instr_push(stream_op->stream_response, registered_op_codes[OP_CODE_ACK],
                    parser->guac_stream_params.stream_id, "Cancel in progress", 0);
            return;
        } else if (is_upload_active || is_download_active) {
            exporter_guac_drain_stream(stream_op);
            exporter_guac_stream_instr_push(stream_op->stream_response, registered_op_codes[OP_CODE_ACK],
                    parser->guac_stream_params.stream_id, "File operation cancelled", 518);
            exporter_guac_stream_instr_push(stream_op->stream_response, registered_op_codes[OP_CODE_ACK],
                    stream_op->stream_id, "File operation cancelled", 518);
            exporter_guac_stream_instr_push(conn->current_request->request_body, registered_op_codes[OP_CODE_ACK],
                    stream_op->stream_id, "File operation cancelled", 518);
            exporter_guac_end_instr_push(stream_op->stream_response, stream_op->stream_id);
            exporter_guac_end_instr_push(conn->current_request->request_body, stream_op->stream_id);

            if (exporter_guac_is_stream_active(&conn->current_request->guac_info->guac_out_stream[file_transfer])) {
                exporter_guac_send_exporter_log_data(conn->current_request, "Download has been cancelled");
                exporter_guac_api_stats_increment(GUAC_FILE_TRANSFER_DOWNLOAD_FAILURE);
            } else if (exporter_guac_is_stream_active(&conn->current_request->guac_info->guac_in_stream[file_transfer])) {
                exporter_guac_send_exporter_log_data(conn->current_request, GUAC_UPLOAD_CANCELLED_BY_USER);
                exporter_guac_api_stats_increment(GUAC_FILE_TRANSFER_UPLOAD_FAILURE);
            }

            exporter_guac_reset_stream(conn->current_request, stream_op);
        } else {
            EXPORTER_DEBUG_FILE_TRANSFER("%s: Received cancel request. No active file transfer found", conn->current_request->name);
        }
    }
}

int exporter_guac_proxy_process_scan_report_cb(guac_proto_parser *parser, const char *data) {
    struct exporter_conn *conn = parser->data;
    int md5_len = parser->guac_scan_params.length;
    int res = ZPATH_RESULT_NO_ERROR;

    if (conn && conn->current_request && conn->current_request->guac_info) {
        if (!exporter_guac_is_stream_active(&conn->current_request->guac_info->guac_in_stream[file_transfer])) {
            return ZPATH_RESULT_NO_ERROR;
        }
        if (md5_len != MAX_ZIA_MD5_HASH_SIZE) {
            EXPORTER_LOG(AL_ERROR, "%s: Failed to receive correct MD5 hash size", conn->current_request->name);
            exporter_guac_proxy_fail(conn->current_request,
                                     UPLOAD_STATUS_SCAN_FAIL,
                                     GUAC_UPLOAD_MD5_SIZE_ERR_MSG);
            return ZPATH_RESULT_ERR;
        }

        // Allocate space
        if (!conn->current_request->guac_info->file_scan.file_md5) {
            conn->current_request->guac_info->file_scan.file_md5 = EXPORTER_MALLOC(MAX_ZIA_MD5_HASH_SIZE+1);
        }

        // Copy MD5 from instruction
        memcpy(conn->current_request->guac_info->file_scan.file_md5,
                data + parser->guac_scan_params.offset,
                md5_len);
        conn->current_request->guac_info->file_scan.file_md5[md5_len] = '\0';

        // Ask for the report
        conn->current_request->guac_info->file_scan.is_zia_scan_req = 0;
        res = exporter_zia_scan_process(conn->current_request);
        if (res != ZPATH_RESULT_NO_ERROR) {
            EXPORTER_LOG(AL_ERROR, "%s: file report failed, err : %s", conn->current_request->name, zpath_result_string(res));
            exporter_guac_proxy_fail(conn->current_request,
                                     UPLOAD_STATUS_SCAN_FAIL,
                                     GUAC_UPLOAD_ZIA_ERR_MSG);
            return ZPATH_RESULT_ERR;
        }
    }

    return ZPATH_RESULT_ERR;
}

int exporter_guac_proxy_process_get_capabilities_cb(guac_proto_parser *parser, const char *data) {
    struct exporter_conn *conn = parser->data;

    if (conn && conn->current_request) {
        if (conn->current_request->guac_info) {
            EXPORTER_DEBUG_FILE_TRANSFER("%s: Processing getCapabilities instruction", conn->current_request->name);
            /* Get file transfer policy to send it back to browser */
            (void)exporter_process_priv_capabilities(conn->current_request);
        }
    }
    return ZPATH_RESULT_NO_ERROR;
}

int exporter_guac_proxy_process_clipboard_cb(guac_proto_parser *parser, const char *data) {
    struct exporter_conn *conn = parser->data;
    struct exporter_stream_op *stream_op = NULL;

    if (conn && conn->current_request && conn->current_request->guac_info) {

        EXPORTER_DEBUG_CLIPBOARD("%s: Received clipboard instruction from browser for clipboard paste",
                                  conn->current_request->name);
        stream_op = &conn->current_request->guac_info->guac_in_stream[clipboard];

        if (is_pra_clipboard_disabled(conn->exporter_domain->customer_gid)) {
            EXPORTER_LOG(AL_ERROR, "%s: Clipboard feature flag is disabled, clipboard paste denied", conn->current_request->name);
            exporter_guac_stream_instr_push(stream_op->stream_response, registered_op_codes[OP_CODE_ACK],
                                            parser->guac_stream_params.stream_id, "Clipboard operation not supported", 771);
            return ZPATH_RESULT_ERR;
        }

        if (exporter_guac_is_stream_active(&conn->current_request->guac_info->guac_out_stream[clipboard]) ||
            exporter_guac_is_stream_active(&conn->current_request->guac_info->guac_in_stream[clipboard])) {
            exporter_guac_stream_instr_push(stream_op->stream_response, registered_op_codes[OP_CODE_ACK],
                    parser->guac_stream_params.stream_id, "CLIPBOARD OPERATION ALREADY IN PROGRESS", 517);
            EXPORTER_LOG(AL_ERROR, "%s: CLIPBOARD OPERATION ALREADY IN PROGRESS(stream_id %d)",
                                    conn->current_request->name, parser->guac_stream_params.stream_id);
            return ZPATH_RESULT_NO_ERROR;
        }

        int stream_id = parser->guac_stream_params.stream_id;
        exporter_guac_init_stream(stream_op, stream_id, exporter_guac_clipboard_paste);

        exporter_guac_api_stats_increment(GUAC_CLIPBOARD_PASTE);

        if (exporter_process_priv_capabilities_clipboard_paste(conn->current_request)) {
            EXPORTER_LOG(AL_ERROR, "%s: Capabilities check has failed", conn->current_request->name);
            return ZPATH_RESULT_ERR;
        }
        evbuffer_add(stream_op->stream_request,
                data + parser->instruction_start, parser->offset - parser->instruction_start);
    } else {
        EXPORTER_LOG(AL_ERROR, "Unable to check policy for clipboard paste as conn or req is NULL");
    }
    return ZPATH_RESULT_NO_ERROR;
}

/* file download policy hooks */
int exporter_process_priv_capabilities_download(struct exporter_request *request)
{
    struct exporter_stream_op *stream_op = &request->guac_info->guac_out_stream[file_transfer];

    request->guac_info->file_log.file_action = FILE_ACTION_DOWNLOAD;
    if ((request->guac_info->capabilities_policy_bitmap & FILE_DOWNLOAD)
            && !(request->guac_info->rdp_dir_list_cnt > RDP_MAX_NODES_CNT + 1)) {
        exporter_stream_create_set_timeout(request);
        EXPORTER_DEBUG_FILE_TRANSFER("File Download allowed by policy");
        return ZPATH_RESULT_NO_ERROR;
    } else {
        /*
         * Download policy has changed. Since GET doesnt have an ACK to indicate
         * policy error, send CAPABILITIES response to browser.
         */
        exporter_guac_instr_push(stream_op->stream_response, registered_op_codes[OP_CODE_DOWNLOAD_FAILURE],
                request->guac_info->rdp_dir_list_cnt > RDP_MAX_NODES_CNT + 1 ? GUAC_TRANSFER_LIMIT_EXCEEDED : "Policy Error", 0);

        //Download is denied, send exporter log
        exporter_guac_send_exporter_log_data(request, request->guac_info->rdp_dir_list_cnt > RDP_MAX_NODES_CNT + 1 ?
                GUAC_TRANSFER_LIMIT_EXCEEDED : GUAC_UPLOAD_POLICY_DENY);
        exporter_guac_api_stats_increment(GUAC_FILE_TRANSFER_DOWNLOAD_POLICY_DENY);
        exporter_guac_api_stats_increment(GUAC_FILE_TRANSFER_DOWNLOAD_FAILURE);

        exporter_guac_reset_stream(request, stream_op);
        exporter_guac_proxy_sandbox_info_free(request);
        EXPORTER_DEBUG_FILE_TRANSFER("%s: exporter_process_priv_capabilities_download file download denied by policy", request->name);
        return ZPATH_RESULT_ERR;
    }
}

/*file upload policy hooks */
int exporter_process_priv_capabilities_upload(struct exporter_request *request)
{
    request->guac_info->file_log.file_action = FILE_ACTION_UPLOAD;
    struct exporter_stream_op *stream_op = NULL;
    stream_op = &request->guac_info->guac_in_stream[file_transfer];

    if ((request->guac_info->capabilities_policy_bitmap & FILE_UPLOAD) &&
            !(request->guac_info->rdp_dir_list_cnt > RDP_MAX_NODES_CNT)) {
        if (request->guac_info->capabilities_policy_bitmap & INSPECT_FILE_UPLOAD) {
            exporter_guac_stream_instr_push(stream_op->stream_response,
                    registered_op_codes[OP_CODE_ACK],
                    stream_op->stream_id,"OK (STREAM BEGIN)", 0);
        } else {
            // Send "put" instruction
            exporter_guac_proxy_send(request, stream_op);
            exporter_stream_create_set_timeout(request);
            // Close temp file - we will not keep transfer file for inspection
            if (request->guac_info->file_scan.upload_file) {
                fclose(request->guac_info->file_scan.upload_file);
                request->guac_info->file_scan.upload_file = NULL;
            }
        }
        return ZPATH_RESULT_NO_ERROR;
    } else {
        evbuffer_drain(stream_op->stream_request, evbuffer_get_length(stream_op->stream_request));
        exporter_guac_stream_instr_push(stream_op->stream_response, registered_op_codes[OP_CODE_ACK],
                stream_op->stream_id, request->guac_info->rdp_dir_list_cnt > RDP_MAX_NODES_CNT ?
                        GUAC_TRANSFER_LIMIT_EXCEEDED : GUAC_UPLOAD_POLICY_DENY, 256);

        //Upload is denied, send exporter log
        exporter_guac_send_exporter_log_data(request, request->guac_info->rdp_dir_list_cnt > RDP_MAX_NODES_CNT ?
                GUAC_TRANSFER_LIMIT_EXCEEDED : GUAC_UPLOAD_POLICY_DENY);

        exporter_guac_reset_stream(request, stream_op);
        exporter_guac_proxy_sandbox_info_free(request);
        exporter_guac_api_stats_increment(GUAC_FILE_TRANSFER_UPLOAD_POLICY_DENY);
        exporter_guac_api_stats_increment(GUAC_FILE_TRANSFER_UPLOAD_FAILURE);

        EXPORTER_DEBUG_FILE_TRANSFER("%s: exporter_process_priv_capabilities_upload file upload denied by policy", request->name);
        return ZPATH_RESULT_ERR;
    }
}

int exporter_process_priv_capabilities_clipboard_copy(struct exporter_request *request)
{
    struct exporter_stream_op *stream_op = &request->guac_info->guac_out_stream[clipboard];;
    if (request->guac_info->capabilities_policy_bitmap & CLIPBOARD_COPY) {
        EXPORTER_DEBUG_CLIPBOARD("%s: exporter_process_priv_capabilities_clipboard_copy clipboard copy allowed by policy", request->name);
        return ZPATH_RESULT_NO_ERROR;
    } else {
        exporter_guac_clipboardfailure_push(stream_op->stream_response,
                "Clipboard copy is denied due to Capability Policy");
        exporter_guac_reset_stream(request, stream_op);
        exporter_guac_api_stats_increment(GUAC_CLIPBOARD_COPY_POLICY_DENY);
        EXPORTER_DEBUG_CLIPBOARD("%s: exporter_process_priv_capabilities_clipboard_copy clipboard copy denied by policy", request->name);
        return ZPATH_RESULT_ERR;
    }
}

int exporter_process_priv_capabilities_clipboard_paste(struct exporter_request *request)
{
    struct exporter_stream_op *stream_op = &request->guac_info->guac_in_stream[clipboard];
    if (request->guac_info->capabilities_policy_bitmap & CLIPBOARD_PASTE) {
        /* Send ACK to Browser */
        exporter_guac_stream_instr_push(stream_op->stream_response,
                                        registered_op_codes[OP_CODE_ACK],
                                        stream_op->stream_id,"OK (STREAM BEGIN)", 0);
        EXPORTER_DEBUG_CLIPBOARD("%s: exporter_process_priv_capabilities_clipboard_paste clipboard paste allowed by policy", request->name);
        return ZPATH_RESULT_NO_ERROR;
    } else {
        exporter_guac_stream_instr_push(stream_op->stream_response,
                                        registered_op_codes[OP_CODE_ACK],
                                        stream_op->stream_id, "Clipboard paste is denied due to Capability Policy", 256);
        exporter_guac_reset_stream(request, stream_op);
        exporter_guac_api_stats_increment(GUAC_CLIPBOARD_PASTE_POLICY_DENY);
        EXPORTER_DEBUG_CLIPBOARD("%s: exporter_process_priv_capabilities_clipboard_paste clipboard paste denied by policy", request->name);
        return ZPATH_RESULT_ERR;
    }
}


/* Fetch privileged capabilities rules and capabilities bit map from wally
 * Will be an async lookup into zpn_rule and zpn_privileged_capabilities tables
 * Will be called more than once in async cases
 * Respective functions will be called to process the policy for capability
 * exporter_process_priv_capabilities_upload - called for PUT instruction
 * exporter_process_priv_capabilities_download - called for GET instruction
 * exporter_process_priv_capabilities_clipboard_copy - called for clipboard instruction from guacd
 * exporter_process_priv_capabilities_clipboard_paste - called for clipboard instruction from browser
 * exporter_guac_connect - called for SSH/VNC/REALVNC Guacamole handshake to provide capability policy bits */
int exporter_process_priv_capabilities(struct exporter_request *request)
{
    char response_buf[1024] = {'\0'};
    int res = ZPATH_RESULT_NO_ERROR;
    int64_t   capabilities_policy_id = 0;
    uint64_t  capabilities_policy_bitmap = 0;

    /* Look up from wally and get the capabilities bitmap. Will be async */
    res = exporter_privileged_capabilities_get(request,
                                               &capabilities_policy_id,
                                               &capabilities_policy_bitmap);
    if (res == ZPATH_RESULT_ASYNCHRONOUS) {
        exporter_set_async_state_for_priv_capabilities(request);
        return res;
    } else if (res != ZPATH_RESULT_NO_ERROR) {
        /* Failure will be handled based on the type on instr cb */
        EXPORTER_LOG(AL_ERROR, "%s: failed to get policy capabilities res=%s", request->name, zpath_result_string(res));
    }

    /*
     * The Policy ID and bitmap will be maintained for the duration of the session.
     * We do not want to overwrite the policy ID and bitmap value
     */
    if (!request->guac_info->capabilities_policy_id) {
        request->guac_info->capabilities_policy_id = capabilities_policy_id;
    }
    if (!request->guac_info->capabilities_policy_bitmap) {
        request->guac_info->capabilities_policy_bitmap = capabilities_policy_bitmap;
    }

    /*
     * Session recording decision is made once at the begining of the session.
     * If session recording was disabled, and later policy-change enables the
     * session-recording, DO NOT start the recording in between for
     * an active session.
     */
    if (request->guac_info->recording_ctxt.recording_check_done == 0) {
        /* check if session recording is enabled for the customer */
        if (!is_pra_session_recording_disabled(request->conn->exporter_domain->customer_gid)
            && (request->guac_info->capabilities_policy_bitmap & RECORD_SESSION) && !request->is_proxy_conn) {
            request->guac_info->recording_ctxt.recording_enabled = 1;
        }
        EXPORTER_DEBUG_SESSION_RECORDING("REC_UPLOAD: recording enabled %d, request %p",
            request->guac_info->recording_ctxt.recording_enabled, request);
        request->guac_info->recording_ctxt.recording_check_done = 1;
    }

    /*
     * Session sharing decision is made once at the begining of the session.
     * If session sharing was disabled, and later policy-change enables the
     * session sharing, DO NOT start the sharing in between for
     * an active session.
     */
    if (request->guac_info->session_sharing_check_done == 0) {
        /* check if session sharing is enabled for the customer */
        if (!is_pra_session_monitoring_disabled(request->conn->exporter_domain->customer_gid)) {
            if (request->guac_info->capabilities_policy_bitmap & SHARE_SESSION) {
                request->guac_info->is_session_share_enabled = 1;
            }
            if (request->guac_info->capabilities_policy_bitmap & SESSION_SHARE_MONITOR) {
                EXPORTER_DEBUG_SESSION_SHARING("SESS_PROC Setting is_session_share_monitor mode to 1 for %s",request->name);
                request->guac_info->is_session_share_monitor = 1;
            }
			if (request->guac_info->capabilities_policy_bitmap & SESSION_SHARE_CONTROL) {
                request->guac_info->is_session_share_control = 1;
            }
        }
        EXPORTER_DEBUG_SESSION_SHARING("[SESSION_SHARING] %s: session sharing enabled %d, session sharing monitor enabled %d, session sharing control enabled %d", request->name, request->guac_info->is_session_share_enabled, request->guac_info->is_session_share_monitor, request->guac_info->is_session_share_control);
        request->guac_info->session_sharing_check_done = 1;
    }

   if (request->guac_info->guac_parser.guac_conn_state == GUAC_STATE_UNKNOWN &&
               !request->guac_info->creds && !request->guac_info->is_pra_interactive_auth_disabled) {
        /* Invoked on getting capabilities for session sharing */
        EXPORTER_DEBUG_SESSION_SHARING("SESS_PROC: Could not parse credentials. Return without calling guac-connect");
        exporter_sharing_stats_inc(proctor_policy_fail_type);
        return ZPATH_RESULT_NO_ERROR;
    } else if (request->guac_info->guac_parser.guac_conn_state == GUAC_STATE_UNKNOWN) {
        // Capabilities loaded for SSH or VNC connection, proceed connecting
        res = exporter_guac_connect(request, 1);
        if (request->is_proxy_conn_async && (res == ZPATH_RESULT_ASYNCHRONOUS)) {
             EXPORTER_DEBUG_SESSION_SHARING("SESS_PROC: %s, request %s, received async from guac_connect", __func__, request->name);
            return ZPATH_RESULT_NO_ERROR;
        } else if (res != ZPATH_RESULT_NO_ERROR) {
            EXPORTER_LOG(AL_ERROR, "%s, request %s, Error from guac_connect. Sending Failed to connect to user", __func__, request->name);
            exporter_guac_send_response(request,
                    HTTP_STATUS_INTERNAL_SERVER_ERROR,
                    "%s",
                    "Disconnected from server : Failed to connect");
        }
    } else {
        /* Invoked on getCapabilities instr */
        exporter_guac_generate_json_capability_response(request,
                                                        request->guac_info->capabilities_policy_bitmap,
                                                        res,
                                                        response_buf,
                                                        sizeof(response_buf));
        if (!request->guac_info->stream_response) {
            request->guac_info->stream_response = evbuffer_new();
        }
        exporter_guac_capabilities_push(request->guac_info->stream_response, response_buf);
        return ZPATH_RESULT_NO_ERROR;
    }

    /* Async cases must always return NO error to avoid request being destroyed */
    return ZPATH_RESULT_NO_ERROR;
}
//#endif

#define MAX_CRED_LEN 2*8192
int exporter_guac_proxy_process_cred_cb(guac_proto_parser *parser, const char *data) {
    struct exporter_conn *conn = parser->data;

    if (conn && conn->current_request && conn->current_request->guac_info) {
        ZPATH_MUTEX_LOCK(&(conn->current_request->lock), __FILE__, __LINE__);
        if (conn->current_request->guac_info->guac_parser.guac_conn_state == GUAC_STATE_UNKNOWN) {
            if (parser->guac_cred_params.length > MAX_CRED_LEN) {
                EXPORTER_LOG(AL_ERROR, "%s: Guacamole handshake failed: credentials size larger than allowed maximum of %d",
                        conn->current_request->name, MAX_CRED_LEN);
                exporter_guac_send_response(conn->current_request,
                        HTTP_STATUS_INTERNAL_SERVER_ERROR,
                        "%s %d",
                        "Disconnected from server : Credentials size larger than allowed maximum of", MAX_CRED_LEN);
                ZPATH_MUTEX_UNLOCK(&(conn->current_request->lock), __FILE__, __LINE__);
                return ZPATH_RESULT_ERR;
            }
            conn->current_request->guac_info->creds = EXPORTER_MALLOC(parser->element_length+1);
            memcpy(conn->current_request->guac_info->creds,
                    data + parser->guac_cred_params.offset,
                    parser->guac_cred_params.length);
            conn->current_request->guac_info->creds[parser->guac_cred_params.length] = '\0';
            int res = exporter_guac_connect(conn->current_request, 0);
            if (conn->current_request->is_proxy_conn_async && (res == ZPATH_RESULT_ASYNCHRONOUS)) {
                EXPORTER_DEBUG_SESSION_SHARING("SESS_PROC: %s, request %s, received async from guac_connect", __func__, conn->current_request->name);
            } else if (res != ZPATH_RESULT_NO_ERROR) {
                EXPORTER_LOG(AL_ERROR, "%s, request %s, Error from guac_connect. Sending Failed to connect to user", __func__, conn->current_request->name);
                exporter_guac_send_response(conn->current_request,
                        HTTP_STATUS_INTERNAL_SERVER_ERROR,
                        "%s",
                        "Disconnected from server : Failed to connect");
            }
        }
        ZPATH_MUTEX_UNLOCK(&(conn->current_request->lock), __FILE__, __LINE__);
    }


    return ZPATH_RESULT_NO_ERROR;
}

int exporter_guac_proxy_process_session_control_cb(guac_proto_parser *parser, const char *data) {
    struct exporter_conn *conn = parser->data;
    char *user_email = NULL;
    char *session_id = NULL;
    char *session_control_buffer = NULL;
    char *type = NULL;
    int ret = ZPATH_RESULT_NO_ERROR;
    char response_buf[EXPORTER_USER_PORTAL_NAME_STR_LEN] = {'\0'};
    char error_reason[EXPORTER_HTTP_PROXY_ERROR_REASON_LEN] = "";

    if (conn && conn->current_request && conn->current_request->guac_info) {
        session_control_buffer = EXPORTER_MALLOC(parser->element_length+1);
        if (!session_control_buffer) {
            EXPORTER_LOG(AL_ERROR, "%s: Could not allocate memory for session control buffer", conn->current_request->name);
            snprintf(error_reason, EXPORTER_USER_PORTAL_ERROR_REASON_LEN, "Could not allocate memory");
            ret = ZPATH_RESULT_ERR;
            goto do_exit;
        }
        memcpy(session_control_buffer,
                data + parser->guac_session_control_params.offset,
                parser->guac_session_control_params.length);
        session_control_buffer[parser->guac_session_control_params.length] = '\0';

        /* Parse required JSON parameters for session control operations */
        ret = exporter_conn_guac_parse_json_session_control_args((const char *) session_control_buffer, &user_email,
                &type, error_reason);
        if (session_control_buffer) {
            EXPORTER_FREE(session_control_buffer);
        }
        if (ret != ZPATH_RESULT_NO_ERROR) {
            goto do_exit;
        }

        char *conn_id = conn->current_request->guac_info->guac_parser.guac_tunnel_params.conn_id;
        if (conn_id) {
            /* need to skip $ in connection ID */
            session_id = conn_id+1;
        } else {
            snprintf(error_reason, EXPORTER_USER_PORTAL_ERROR_REASON_LEN, "Connection id is not available");
            ret = ZPATH_RESULT_ERR;
            goto do_exit;
        }

        /* Perform session control operation. Allowed operation types are "transfer", "eject", "email" */
        if (strcmp(type, "transfer") == 0) {
            ret = exporter_session_control_transfer_to_participant(conn->current_request, user_email, session_id, error_reason);
        } else if (strcmp(type, "eject") == 0) {
            ret = exporter_session_control_eject_participant(conn->current_request, user_email, session_id, error_reason);
        } else if (strcmp(type, "email") == 0) {
            int64_t customer_gid = conn->exporter_domain->customer_gid;
            char portal_url[EXPORTER_HTTP_PROXY_ERROR_REASON_LEN] = {0};
            snprintf(portal_url, EXPORTER_HTTP_PROXY_ERROR_REASON_LEN, "%s%s", "https://", EXPORTER_GET_REQUEST_DOMAIN(conn->current_request));
            ret = exporter_send_email_to_participant(conn->current_request, customer_gid, user_email, session_id, portal_url, error_reason);
        } else {
            snprintf(error_reason, EXPORTER_USER_PORTAL_ERROR_REASON_LEN, "Invalid session control type parameter");
            ret = ZPATH_RESULT_ERR;
        }
    } else {
        EXPORTER_LOG(AL_ERROR, "conn or req is NULL");
        return ret;
    }

do_exit:
    /* Send session control response for the operation to browser */
    if (!conn->current_request->guac_info->stream_response) {
        conn->current_request->guac_info->stream_response = evbuffer_new();
    }
    /* Generate JSON session control response */
    exporter_guac_generate_json_session_control_response(conn->current_request,
                                                    type,
                                                    user_email,
                                                    error_reason,
                                                    ret,
                                                    response_buf,
                                                    sizeof(response_buf));
    /* Push session control response instruction */
    exporter_guac_session_control_response_push(conn->current_request->guac_info->stream_response, response_buf);
    if (user_email) {
        EXPORTER_FREE(user_email);
    }
    if (type) {
        EXPORTER_FREE(type);
    }
    return ret;
}

int exporter_guac_proxy_process_put_mf_cb(guac_proto_parser *parser, const char *data) {
    struct exporter_conn *conn = parser->data;

    if (conn && conn->current_request && conn->current_request->guac_info) {

        struct exporter_stream_op *stream_op = &conn->current_request->guac_info->guac_in_stream[file_transfer];

        if (is_pra_advanced_file_transfer_disabled(conn->exporter_domain->customer_gid)) {
            exporter_guac_stream_instr_push(stream_op->stream_response, registered_op_codes[OP_CODE_ACK],
                    parser->guac_stream_params.stream_id, "CLIENT FORBIDDEN", 771);
            return ZPATH_RESULT_ERR;
        }

        if (exporter_guac_is_stream_active(&conn->current_request->guac_info->guac_out_stream[file_transfer]) ||
                exporter_guac_is_stream_active(&conn->current_request->guac_info->guac_in_stream[file_transfer]) ||
                conn->current_request->guac_info->is_cancel_scan_active) {
            exporter_guac_stream_instr_push(stream_op->stream_response, registered_op_codes[OP_CODE_ACK],
                    parser->guac_stream_params.stream_id, "FILE OPERATION ALREADY IN PROGRESS", 517);
            EXPORTER_LOG(AL_ERROR, "%s: FILE OPERATION ALREADY IN PROGRESS(stream_id %d)",
                    conn->current_request->name, parser->guac_stream_params.stream_id);
            return ZPATH_RESULT_NO_ERROR;
        }

        int stream_id = parser->guac_stream_params.stream_id;
        exporter_guac_init_stream(stream_op, stream_id, exporter_guac_advanced_file_upload);

        // Add the prefix as the last element (caller did not supply this)
        char *prefix = EXPORTER_MALLOC(GUAC_MAX_PUT_MF_PREFIX_LENGTH);

        // Calculate SHA256 of the user's email
        char hashed_user_name[2 * EXPORTER_SHA256_HASH_SIZE + 1];
        exporter_get_sha256_hash_hex_str(conn->current_request->policy_state->user_email,
                                         strlen(conn->current_request->policy_state->user_email),
                                         hashed_user_name,
                                         sizeof(hashed_user_name) - 1);

        // Create prefix
        snprintf(prefix, GUAC_MAX_PUT_MF_PREFIX_LENGTH, "my-files/%" PRId64 "/%s/",
                conn->exporter_domain->customer_gid, hashed_user_name);

        // Find AWS S3 bucket and region
        char *s3_url = NULL;
        const char *bucket = NULL;
        const char *region = NULL;
        if (exporter_get_tenant_s3url(conn->exporter_domain->customer_gid, EXPORTER_SESSION_RECORDING_SERVICE_NAME, &s3_url)
                == ZPATH_RESULT_NO_ERROR) {

            char *rest = s3_url;
            if (rest != NULL) {
                /* skip https:// or http:// */
                if (strcasestr(rest, "https://") != NULL) {
                    rest += strlen("https://");
                } else if (strcasestr(rest, "http://") != NULL) {
                    rest += strlen("http://");
                }

                bucket = strtok_r(rest, ".", &rest); /* S3 bucket */
            }
            if (rest != NULL) {
                strtok_r(rest, ".", &rest); /* skip 's3' literal */
            }
            if (rest != NULL) {
                region = strtok_r(rest, ".", &rest); /* S3 region */
            }
        }

        if (!bucket || !region) {
            // My Files bucket and region must be configured for the tenant to proceed
            exporter_guac_stream_instr_push(stream_op->stream_response, registered_op_codes[OP_CODE_ACK],
                     parser->guac_stream_params.stream_id, "CUSTOMER NOT CONFIGURED WITH SERVICE ENDPOINT FOR THIS OPERATION", 516);
            EXPORTER_FREE(prefix);
            if (s3_url != NULL) {
                EXPORTER_FREE(s3_url);
            }
            exporter_guac_reset_stream(conn->current_request, stream_op);
            return ZPATH_RESULT_ERR;
        }

        /* Reset previous file transfer details */
        if (conn->current_request->guac_info->file_log.file_name) {
            exporter_request_free_file_log(conn->current_request);
        }
        memset(&conn->current_request->guac_info->file_log, '\0', sizeof(conn->current_request->guac_info->file_log));

        conn->current_request->guac_info->file_log.file_action = FILE_ACTION_UPLOAD;
        conn->current_request->guac_info->file_log.file_name = EXPORTER_CALLOC(parser->guac_stream_params.stream_name.length+1);
        memcpy(conn->current_request->guac_info->file_log.file_name,
                data + parser->guac_stream_params.stream_name.offset,
                parser->guac_stream_params.stream_name.length);
        conn->current_request->guac_info->file_log.privileged_file_id = parser->guac_stream_params.file_id;
        conn->current_request->guac_info->file_log.start_ts = epoch_s();

        char *mime_type = EXPORTER_CALLOC(parser->guac_stream_params.mimetype.length+1);
        memcpy(mime_type, data + parser->guac_stream_params.mimetype.offset, parser->guac_stream_params.mimetype.length);

        char *s3_obj_name = EXPORTER_CALLOC(parser->guac_stream_params.s3_obj_name.length+1);
        memcpy(s3_obj_name, data + parser->guac_stream_params.s3_obj_name.offset, parser->guac_stream_params.s3_obj_name.length);
        /* Form put_mf instr to be sent to guacd */
        exporter_guac_put_mf_instr_push(stream_op->stream_request,
                                        registered_op_codes[OP_CODE_PUT_MF],
                                        parser->guac_stream_params.object_id,
                                        parser->guac_stream_params.stream_id,
                                        mime_type,
                                        conn->current_request->guac_info->file_log.file_name,
                                        s3_obj_name,
                                        prefix, bucket, region);

        EXPORTER_FREE(mime_type);
        EXPORTER_FREE(s3_obj_name);
        EXPORTER_FREE(prefix);
        if (s3_url != NULL) {
            EXPORTER_FREE(s3_url);
        }
        exporter_guac_api_stats_increment(GUAC_FILE_TRANSFER_UPLOAD);

        /* File transfer policy checks before upload can proceed */
        exporter_process_privileged_file_upload_request(conn->current_request);

    } else {
        EXPORTER_LOG(AL_ERROR, "Unable to check policy for file upload as conn or req is NULL");
    }
    return ZPATH_RESULT_NO_ERROR;
}

int exporter_guac_proxy_forward_cb(guac_proto_parser *parser, const char *data) {
    struct exporter_conn *conn = parser->data;

    // Send to guacd - if guacd is ready to receive messages from Browser
    if (conn && conn->current_request && conn->current_request->guac_info
        && conn->current_request->guac_info->is_guacd_ready) {

        evbuffer_add(conn->current_request->request_body,
                data + parser->instruction_start,
                parser->offset - parser->instruction_start);
        EXPORTER_DEBUG_FILE_TRANSFER("Request %s, Guacd proxy sending %lu bytes to guacd.", conn->current_request->name, parser->offset - parser->instruction_start);
    }

    return ZPATH_RESULT_NO_ERROR;
}

size_t exporter_guac_proxy_disconnect_cb(guac_proto_parser *parser, const char *data) {
    struct exporter_conn *conn = parser->data;

    if (conn && conn->current_request) {
        conn->current_request->http_response_complete = 1;
        exporter_guac_api_stats_increment(GUAC_TUNNEL_DISCONNECT_RECV);
    }

    return parser->offset - parser->instruction_start;
}

size_t exporter_guac_process_instruction_from_client_cb(guac_proto_parser *parser, const char *data) {
    struct exporter_conn *conn = parser->data;

    if (conn && conn->current_request && conn->current_request->guac_info
            && !conn->current_request->guac_info->is_guacd_ready) {

        // Except for cred and capabilities, ignore Browser messages until guacd is ready
        if (parser->opcode != OP_CODE_CRED && parser->opcode != OP_CODE_GET_CAPABILITIES) {
            return parser->offset - parser->instruction_start;
        }

    }
    // Process messages that come from the Browser
    switch (parser->opcode) {
    case OP_CODE_PING:
        exporter_guac_proxy_process_ping_cb(parser, data);
        break;
    case OP_CODE_FILE:
        // Drop
        break;
    case OP_CODE_PUT:
        exporter_guac_proxy_process_put_cb(parser, data);
        break;
    case OP_CODE_GET:
        exporter_guac_proxy_process_get_cb(parser, data);
        break;
    case OP_CODE_GET_DIR:
        exporter_guac_proxy_process_get_dir_cb(parser, data);
        break;
    case OP_CODE_BLOB:
        exporter_guac_proxy_process_blob_cb(parser, data);
        break;
    case OP_CODE_END:
        exporter_guac_proxy_process_end_cb(parser, data);
        break;
    case OP_CODE_CRED:
        exporter_guac_proxy_process_cred_cb(parser, data);
        break;
    case OP_CODE_GET_SCAN_STATUS:
        exporter_guac_proxy_process_scan_report_cb(parser, data);
        break;
    case OP_CODE_CANCEL:
        exporter_guac_proxy_process_cancel_cb(parser, data);
        break;
    case OP_CODE_GET_CAPABILITIES:
        exporter_guac_proxy_process_get_capabilities_cb(parser, data);
        break;
    case OP_CODE_CLIPBOARD:
        exporter_guac_proxy_process_clipboard_cb(parser, data);
        break;
    case OP_CODE_SIZE:
    case OP_CODE_MOUSE:
    case OP_CODE_KEYBOARD:
        if (conn && conn->current_request && conn->current_request->guac_info &&
            !conn->current_request->guac_info->is_headless &&
            exporter_pra_session_control_allow_key_mouse(conn->current_request)) {
            exporter_guac_proxy_forward_cb(parser, data);
        }
        break;
    case OP_CODE_SESSION_CONTROL:
        exporter_guac_proxy_process_session_control_cb(parser, data);
        break;
    case OP_CODE_PUT_MF:
        exporter_guac_proxy_process_put_mf_cb(parser, data);
        break;
    default:
        exporter_guac_proxy_forward_cb(parser, data);
        break;
    }

    // Anything from Proxy to send to Browser?
    if (conn && conn->current_request) {
        struct exporter_request *request = conn->current_request;
        // Process stream responses if found any
        if (request->guac_info && request->guac_info->stream_response) {
            // Synchronize with the server (guacd) messages sent to the Browser
            ZPATH_MUTEX_LOCK(&(request->lock), __FILE__, __LINE__);
            size_t prepend_len = evbuffer_get_length(request->guac_info->stream_response);
            // Insert Proxy response if present
            if (prepend_len) {
                exporter_guac_send_to_browser(request, request->guac_info->stream_response, (int) prepend_len);
            }
            ZPATH_MUTEX_UNLOCK(&(request->lock), __FILE__, __LINE__);
        }
        for (int i = 0; i < exporter_guac_stream_max_op; i++) {
            if (request->guac_info &&
                (request->guac_info->guac_in_stream[i].stream_response)) {
                // Synchronize with the server (guacd) messages sent to the Browser
                ZPATH_MUTEX_LOCK(&(request->lock), __FILE__, __LINE__);
                size_t prepend_len = evbuffer_get_length(request->guac_info->guac_in_stream[i].stream_response);
                // Insert Proxy response if present
                if (prepend_len) {
                    exporter_guac_send_to_browser(request, request->guac_info->guac_in_stream[i].stream_response, (int) prepend_len);
                }
                ZPATH_MUTEX_UNLOCK(&(request->lock), __FILE__, __LINE__);
            }
        }
    }

    return parser->offset - parser->instruction_start;
}

size_t exporter_guac_process_instruction_from_server_cb(guac_proto_parser *parser, const char *data) {
    struct exporter_conn *conn = parser->data;

    if (!conn) {
        return 0;
    }

    struct exporter_request *request = conn->current_request;
    int res = 0;

    exporter_guac_proxy_record_instruction(request, parser, data);

    // Process messages that come from the guacd
    size_t processed_len = 0;
    switch (parser->opcode) {
    case OP_CODE_ARGS:
        res = exporter_guac_proxy_process_server_args_cb(parser, data);
        processed_len = (res < 0) ? 0: res;
        break;
    case OP_CODE_READY:
        processed_len = exporter_guac_proxy_process_server_ready_cb(parser, data);
        break;
    case OP_CODE_ACK:
        processed_len = exporter_guac_proxy_process_server_ack_cb(parser, data);
        break;
    case OP_CODE_DISCONNECT:
        processed_len = exporter_guac_proxy_disconnect_cb(parser, data);
        break;
    case OP_CODE_BODY:
        processed_len = exporter_guac_proxy_process_server_body_cb(parser, data);
        break;
    case OP_CODE_BLOB:
        processed_len = exporter_guac_proxy_process_server_blob_cb(parser, data);
        break;
    case OP_CODE_END:
        processed_len = exporter_guac_proxy_process_server_end_cb(parser, data);
        break;
    case OP_CODE_ERROR:
        processed_len = exporter_guac_proxy_process_server_error_cb(parser);
        break;
    case OP_CODE_CLIPBOARD:
        processed_len = exporter_guac_proxy_process_server_clipboard_cb(parser, data);
        break;
    case OP_CODE_SYNC:
        processed_len = exporter_guac_proxy_process_server_sync_cb(parser, data);
        break;
    default:
        // Instruction are sent automatically
        if (request->guac_info->is_headless && request->guac_info->is_guacd_ready &&
            !(parser->opcode == OP_CODE_PING
             || parser->opcode == OP_CODE_ACK
             || parser->opcode == OP_CODE_CONSOLE_INFO
             || parser->opcode == OP_CODE_ARGV
             || parser->opcode == OP_CODE_FS
             || parser->opcode == OP_CODE_MFSFTP)) {
            exporter_guac_delete_instr_from_response_data(conn->current_request, parser);
            processed_len = 0;
        } else {
            processed_len = parser->offset - parser->instruction_start;
        }
        break;
    }

    // Anything from Proxy to send to Browser?
    if (conn->current_request && conn->current_request->guac_info) {
        if (request->guac_info->stream_response) {
            size_t prepend_len = evbuffer_get_length(request->guac_info->stream_response);
            // Insert Proxy response if present
            if (prepend_len) {
                exporter_guac_send_to_browser(request, request->guac_info->stream_response, (int) prepend_len);
            }
        }
        for (int i = 0; i < exporter_guac_stream_max_op; i++) {
            if (request->guac_info->guac_out_stream[i].stream_response) {
                size_t prepend_len = evbuffer_get_length(request->guac_info->guac_out_stream[i].stream_response);
                // Insert Proxy response if present
                if (prepend_len) {
                    exporter_guac_send_to_browser(request, request->guac_info->guac_out_stream[i].stream_response, (int) prepend_len);
                }
            }
        }
    }

    return processed_len;
}

int exporter_guac_proxy_stream_clean(struct exporter_request *request) {
    if (!request || !request->guac_info) {
        EXPORTER_LOG(AL_ERROR, "request or guac context is NULL, nothing to do");
        return  ZPATH_RESULT_ERR;
    }
    int len = 0;
    int is_download_active = 0;
    int is_upload_active = 0;
    struct exporter_stream_op *stream_op = NULL;

    if (exporter_guac_is_stream_active(&request->guac_info->guac_in_stream[file_transfer])) {
        stream_op = &request->guac_info->guac_in_stream[file_transfer];
        is_upload_active = 1;
    } else {
        stream_op = &request->guac_info->guac_out_stream[file_transfer];
        is_download_active = 1;
    }

    // Delete stream request and response
    exporter_guac_drain_stream(stream_op);

    // Message to Browser
    if (is_download_active) {
        len = exporter_guac_instr_push(stream_op->stream_response, registered_op_codes[OP_CODE_DOWNLOAD_FAILURE], "Remote Failure", 0);
    } else {
        len = exporter_guac_stream_instr_push(stream_op->stream_response, registered_op_codes[OP_CODE_END],
                stream_op->stream_id, "", 512);
    }
    exporter_guac_send_to_browser(request, stream_op->stream_response, len);

    if (is_upload_active || (is_download_active && stream_op->stream_type == exporter_guac_file_download)) {
        // Remote system failure, send exporter log only for file upload/download, not for directory
        exporter_guac_send_exporter_log_data(request, GUAC_UPLOAD_REMOTE_FAIL);
    }

    if (is_download_active && stream_op->stream_type == exporter_guac_file_download) {
        exporter_guac_api_stats_increment(GUAC_FILE_TRANSFER_DOWNLOAD_FAILURE);
    } else if (is_upload_active) {
        exporter_guac_api_stats_increment(GUAC_FILE_TRANSFER_UPLOAD_FAILURE);
    }

    // De-allocate memory allocated for ZIA file scan
    exporter_guac_proxy_sandbox_info_free(request);

    // Delete stream create timeout event.
    exporter_stream_create_del_timeout(request);

    exporter_guac_reset_stream(request, stream_op);

    return ZPATH_RESULT_NO_ERROR;
}

#ifndef UNIT_TEST
/*
 * Callback function for stream creation timeout.
 * Indicate timeout failure to receive response from guacd and cleanup
 */
static void
exporter_stream_create_timeout(evutil_socket_t sock, int16_t flags, void *cookie) {
    struct exporter_request *request = cookie;

    if (!request) {
        EXPORTER_LOG(AL_ERROR, "Invalid values");
        return;
    }
    EXPORTER_LOG(AL_NOTICE, "%s: stream get/put response timeout", request->name);
    if (request->guac_info) {
        request->guac_info->is_stream_timeout_triggered = 1;
        exporter_conn_trigger_read_cb(request->conn);
    }
}

/*
 * Create event for stream create timeout.
 */
void
exporter_stream_create_set_timeout(struct exporter_request *request) {
    struct timeval tv;
    tv.tv_usec = 0;
    tv.tv_sec = STREAM_CREATE_DEFAULT_TIMEOUT;

    /* Timeout event already set */
    if (request->guac_info->stream_create_timeout) {
        return;
    }

    request->guac_info->stream_create_timeout = event_new(request->conn->thread->ev_base, -1, 0, exporter_stream_create_timeout, request);
    if (event_add(request->guac_info->stream_create_timeout, &tv)) {
        EXPORTER_LOG(AL_CRITICAL, "%s: Failed to create stream create timeout event", request->name);
        return;
    }
    return;
}
#endif

/*
 * Delete event for stream create timeout.
 */
void
exporter_stream_create_del_timeout(struct exporter_request *request) {
    if (!request || !request->guac_info || !request->guac_info->stream_create_timeout) {
        return;
    }

    event_free(request->guac_info->stream_create_timeout);
    request->guac_info->stream_create_timeout = NULL;
}
