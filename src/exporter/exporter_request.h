/*
 * exporter_request.h. Copyright (C) 2017 Zscaler Inc. All Rights Reserved
 *
 * Handle HTTP request processing. (Parsing is handled by the
 * exporter_conn)
 */

#ifndef __EXPORTER_REQUEST_H__
#define __EXPORTER_REQUEST_H__

#include "exporter/exporter_private.h"
#include "exporter/exporter_error_codes.h"

#include "zpath_lib/zpath_config_override.h"
#include "zcrypt/zcrypt.h"

extern const char *http_status_names[];
extern const char *http_method_names[];

typedef int (request_callback_f)(struct exporter_request *request, void *void_data, int64_t int_data);
int exporter_request_on_thread(request_callback_f *callback, struct exporter_request *request, void *void_data_copy, int64_t int_data);
int get_crypto_key_from_zpath_cloud_secret(struct zcrypt_key *key, int get_previous);
/*
 * exporter_request_process
 *
 * Take an exporter request (in any state) and do what processing needs done.
 *
 * This generally includes both authentication and handoff.
 *
 * Any error returned indicates something very unrecoverable has
 * occurred- the request should simply be terminated.
 *
 * This routine may queue data, but does not itself actually send
 * anything. (It is the responsibility of the connection handler to
 * deal with data arrival/transmission)
 *
 * The request sequence is as follows:
 *
 * 1. original request..... : domain.com/path without domcookie: redirect to cust.auth.com/path1(orig_url). (cust is
 *                            based on customer ID)
 *
 * 2. redir from domain.com : cust.auth.com/path1(orig_url) without authcookie: Figure out IDP, and redirect to
 *                            samlsp(orig_url, idp auth_domain), where auth_domain is one of the IDP auth domains.
 *
 * 3. redir from sp........ : cust.auth.com/path2(orig_url, cookie, idp) (redirect will set-cookie)
 *
 * 4. redir from auth.com.. : auth.com/path1(orig_url) with cookie (will create cookie for orig_url domain, set it in cookie store, and redirect to domain.com/path2
 *
 * 5. redir from auth.com.. : domain.com/path2(orig_url, cookie) (redirect will set-cookie)
 *
 * 6. redir from domain.com : domain.com/path with cookie
 */
int exporter_request_process_data(struct exporter_request *request);
int exporter_request_process(struct exporter_request *request);

/*
 * Cause the request to enter the desired state. This can be called from any thread.
 */
int exporter_request_enter_state(struct exporter_request *request, enum exporter_request_async_state state);

void exporter_set_async_state_for_priv_capabilities(struct exporter_request *request);

void exporter_set_async_state_for_file_scan(struct exporter_request *request);
void exporter_set_async_state_for_reprocess_privileged_file_upload_request(struct exporter_request *request);
void exporter_set_async_state_and_inc_count(struct exporter_request *request, enum exporter_request_async_state state);
void exporter_set_async_state(struct exporter_request *request, enum exporter_request_async_state state);

/*
 * Initialize global state.
 */
int exporter_request_init(void);

int exporter_request_log(struct exporter_request *request);
int exporter_request_log_cleanup(struct exporter_request *request);


/* Prototypes exist only for running test code: */
void exporter_http_decode(char *str);
int exporter_url_decode(char *str);
void exporter_http_encode(const char *str, char *out_str, size_t out_str_len);

/* Wally async request callback */
int exporter_request_wally_callback(void *response_callback_cookie,
                           struct wally_registrant *registrant,
                           struct wally_table *table,
                           int64_t request_id,
                           int row_count);

/* Async request callback */
int exporter_request_async_callback(void *callback_data, int64_t callback_int);

#define EXPORTER_STANDARD_RESPONSE_HEADER_BUF_LEN 2000
/*
 * Prints to output buf:
 *   HTTP 1.1 <status-code> <status-message>
 *   Date header
 *   Server header
 *   Debug headers (server id, request id)
 * Make sure buf is big enough. Use EXPORTER_STANDARD_RESPONSE_HEADER_BUF_LEN to be safe.
 */
void exporter_request_print_standard_response_headers(struct exporter_request *request, enum http_status status, char* buf, size_t buf_size);
int exporter_request_respond(struct exporter_request *request, enum http_status status, enum exporter_error_codes error);
int exporter_request_respond_with_text(struct exporter_request *request, enum http_status status, enum exporter_error_codes error, char *format_str, ...);
int exporter_request_respond_with_upgrade_error(struct exporter_request *request, enum http_status status);
int exporter_request_respond_status_and_data(struct exporter_request *request, enum http_status status, enum exporter_error_codes error, void *data, size_t data_len);
int exporter_request_respond_status_data_content_type(struct exporter_request *request, enum http_status status, enum exporter_error_codes error, const void *data, size_t data_len, const char *content_type);


enum http_content_type
{
  HTTP_CONTENT_TYPE_HTML,
  HTTP_CONTENT_TYPE_JS,

  HTTP_CONTENT_TYPE_MAX
};

/* CSP */
int exporter_request_respond_status_and_data_csp(
        struct exporter_request *request,
        enum http_status status,
        void *data,
        size_t data_len,
        const char *target_domain);

int exporter_request_respond_status_data_content_type_json(struct exporter_request *request,
        const char *set_cookie_header_value,
        const char *set_cookie_header_legacy_value,
        enum http_status status,
        const void *data,
        size_t data_len,
        const char *content_type,
        int content_len,
        int http_response_complete);
/* CSP */

/* Exporter find cookie */
int exporter_request_find_cookie(void *request_void, const char *cookie_name, int find_newest, char *cookie_value, size_t cookie_len);

/*
 * Extracts a parameter from a parsed URL.
 * findme is the parameter name.
 * value is set to the value of the parameter
 * Returns ZPATH_RESULT_NOT_FOUND if the parameter is not present
 */
int query_string_find(const char *url, struct http_parser_url *url_parser, const char *findme, char *value, size_t value_len);

int is_arbitrary_auth_domain_enabled(int64_t customer_gid);

const char *exporter_request_input_state_get_str(struct exporter_request *req);
int get_csp_timeout(int64_t customer_gid);
int64_t exporter_xff_header_enabled_for_customer(int64_t customer_gid);
int64_t exporter_forwarded_header_enabled_for_customer(int64_t customer_gid);
int is_exporter_managed_chrome_enabled(struct exporter_request *request, uint32_t *is_mgd_chrome);
int is_exporter_managed_chrome_2_enabled(struct exporter_request *request, uint32_t *is_mgd_chrome);
int exporter_request_complete_process(struct exporter_request* request);
char * exporter_get_managed_domain_name(int64_t customer_gid, const char* domain_name, size_t domain_name_len);
int exporter_request_fetch_browser_profile(struct exporter_request *request);
#endif /* __EXPORTER_REQUEST_H__ */
