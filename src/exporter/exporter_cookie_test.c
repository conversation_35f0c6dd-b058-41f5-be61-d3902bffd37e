#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdarg.h>
#include "argo/argo_log.h"
#include "exporter/exporter.h"
#include "exporter/exporter_private.h"
#include "exporter/exporter_request_util.h"

struct exporter global_exporter;
const char *exporter_auth_domain;
const char *exporter_saml_auth_domain;
const char *exporter_broker_domain;
const char *exporter_uportal_ba_app_suffix = NULL;
const char *exporter_uportal_host_suffix = NULL;
const char *exporter_uportal_pra_host_suffix = NULL;

char *exporter_cookie_domain = NULL;
char *exporter_cookie_crypto = NULL;
const char *exporter_exporter_domain;

struct result {
    const char *input;
    const char *output;
    const char *output_exp;
    int status;
};

static void redirect_header_edit_test();

int main () {
    int result; (void)result;
    argo_log_use_printf(1);
    result = argo_library_init(512);
    (void)argo_log_init_with_maxmem("unknown", 0, "hello", "argo", 1, 0);

    EXPORTER_DEBUG_HTTP("Hello test");

    redirect_header_edit_test();

    struct result test_values[] = {
        {
            .input = "  a =  3   ;a;;;  cookie testing-ut1  =    thisis   =  junkvalue35   ;;; DomaIn = testdomain.com  ;;; Domain = hello.com;   expires=Wed, 21 Oct 2035 07:28:00 GMT;HttpOnly ; Max-Age = 86400 ; Partitioned ; Path = / ; SameSite = Lax ; Secure=               ;adfadf  =   ",
            .output_exp = "a=3;a;cookie testing-ut1=thisis   =  junkvalue35;expires=Wed, 21 Oct 2035 07:28:00 GMT;HttpOnly;Max-Age=86400;Partitioned;Path=/;SameSite=Lax;Secure=;adfadf=;",
        },
        {
            .input = "Domain=google1.com;cookie1;domaina=testdomain.com;secureX;domain=;",
            .output_exp = "cookie1;domaina=testdomain.com;secureX;Secure;"
        },
        {
            .input = "Domain=google1.com;cookie1;domain=testdomain.com;secure;domain=;",
            .output_exp = "cookie1;secure;"
        },
        {
            .input = "Domain=google1.com;cookie1;domain=testdomain.com;domain=;Secure;",
            .output_exp = "cookie1;Secure;"
        },
        {
            .input = "Domain=google1.com;cookie1;domain=testdomain.com;domain=;Secure",
            .output_exp = "cookie1;Secure;"
        },
        {
            .input = "Domain=google1.com;cookie1;domain=testdomain.com;domain=;",
            .output_exp = "cookie1;Secure;"
        },
        {
            .input = "Domain=google1.com;cookie1;domain=testdomain.com;domain=",
            .output_exp = "cookie1;Secure;"
        },
        {
            .input = "key=value",
            .output_exp = "key=value;Secure;",
        },
        {
            .input = "key=value ",
            .output_exp = "key=value ;Secure;",
        },
        {
            .input = "key=value;",
            .output_exp = "key=value;;Secure;",
        },
        {
            .input = "key=value ; ",
            .output_exp = "key=value ; ;Secure;",
        },
        {
            .input = "key",
            .output_exp = "key;Secure;",
        },
        {
            .input = "key=",
            .output_exp = "key=;Secure;",
        },
        {
            .input = "key= ",
            .output_exp = "key= ;Secure;",
        },
        {
            .input = "key=;",
            .output_exp = "key=;;Secure;",
        },
        {
            .input = "key= ;",
            .output_exp = "key= ;;Secure;",
        },
        {
            .input = "key= ; ",
            .output_exp = "key= ; ;Secure;",
        },
        {
            .input = "key =",
            .output_exp = "key =;Secure;",
        },
        {
            .input = "key = ",
            .output_exp = "key = ;Secure;",
        },
        {
            .input = "key =;",
            .output_exp = "key =;;Secure;",
        },
        {
            .input = "key = ;",
            .output_exp = "key = ;;Secure;",
        },
        {
            .input = "key = ; ",
            .output_exp = "key = ; ;Secure;",
        },
        {
            .input = "key;",
            .output_exp = "key;;Secure;",
        },
        {
            .input = "key ;",
            .output_exp = "key ;;Secure;",
        },
        {
            .input = "key ; ",
            .output_exp = "key ; ;Secure;",
        },
        {
            .input = "cookie1;domain=testdomain.com",
            .output_exp = "cookie1;Secure;",
        },
        {
            .input = "cookie1;domain=testdomain.com;",
            .output_exp = "cookie1;Secure;",
        },
        {
            .input = "cookie1;domain=testdomain.com ;",
            .output_exp = "cookie1;Secure;",
        },
        {
            .input = "cookie1;domain= testdomain.com ;",
            .output_exp = "cookie1;Secure;",
        },
        {
            .input = "cookie1;domain = testdomain.com ;",
            .output_exp = "cookie1;Secure;",
        },
        {
            .input = "cookie1; domain = testdomain.com ;",
            .output_exp = "cookie1;Secure;",
        },
        {
            .input = "cookie1 ; domain = testdomain.com ;",
            .output_exp = "cookie1;Secure;",
        },
        {
            .input = " cookie1 ; domain = testdomain.com ;",
            .output_exp = "cookie1;Secure;",
        },
        {
            .input = " coo kie1 ; domain = testdomain.com ;",
            .output_exp = "coo kie1;Secure;",
        },
        {
            .input = " coo kie1 ; do main = testdomain.com ;",
            .output_exp = " coo kie1 ; do main = testdomain.com ;;Secure;",
        },
        {
            .input = " coo kie1 ; do main = goo gle.com ;",
            .output_exp = " coo kie1 ; do main = goo gle.com ;;Secure;",
        },
        {
            .input = "cookie = value;domain=testdomain.com",
            .output_exp = "cookie=value;Secure;",
        },
        {
            .input = "cookie = value=hello;domain=testdomain.com",
            .output_exp = "cookie=value=hello;Secure;",
        },
        {
            .input = "cookie = value=hello;domain=.testdomain.com",
            .output_exp = "cookie=value=hello;Secure;",
        },
        {
            .input = "cookie = value=hello;adomain=.testdomain.com",
            .output_exp = "cookie = value=hello;adomain=.testdomain.com;Secure;",
        },
        {
            .input = "cookie==;domain=.testdomain.com",
            .output_exp = "cookie==;Secure;",
        },
        {
            .input = "=;domain=.testdomain.com",
            .output_exp = "=;Secure;",
        },
        {
            .input = "cookie=;domain=.testdomain.com;path=/;",
            .output_exp = "cookie=;path=/;Secure;"
        },
        {
            .input = "cookie=;domain=.testdomain.com;path=/",
            .output_exp = "cookie=;path=/;Secure;"
        },
        {
            .input = "cookie=;domain=.testdomain.com;path=",
            .output_exp = "cookie=;path=;Secure;"
        },
        {
            .input = "cookie=;domain=.google2.com;path=",
            .output_exp = "cookie=;path=;Secure;",
        },
        {
            .input = "cookie=;domain=.d.zsproxy.net;path=",
            .output_exp = "cookie=;path=;Secure;",
        },
      };

    int itr = 0;
    char *updated_cookie = NULL;

    int n_tests = sizeof(test_values)/sizeof(struct result);
    int start_itr = 0;

    for (itr = start_itr; itr < n_tests; itr++) {
        printf("###### Executing Unit test %.2d ######\n", itr+1);

        updated_cookie = exporter_util_edit_cookie_value(test_values[itr].input, strlen(test_values[itr].input),
                "testdomain.com",
                "service1-mockfirm-com.d.zsproxy.net",
                1,
                EXPORTER_MANAGED_BA_DELETE_ALWAYS);

        printf("Set-Cookie: [%.*s]\n", (int)strlen(test_values[itr].input), test_values[itr].input);
        printf("Expected Set-Cookie: [%.*s]\n", test_values[itr].output_exp ? (int)strlen(test_values[itr].output_exp):0,
                test_values[itr].output_exp ? test_values[itr].output_exp : "null");
        printf("Updated Set-Cookie: [%.*s]\n", updated_cookie ? (int)strlen(updated_cookie):0, updated_cookie?updated_cookie:"");
        test_values[itr].status = updated_cookie == test_values[itr].output_exp;
        if (updated_cookie && test_values[itr].output_exp)
        test_values[itr].status  = !strcmp(updated_cookie, test_values[itr].output_exp);
        printf("Result for Test Case %.2d %s\n", itr+1, test_values[itr].status  ? "PASS" : "FAIL");
        printf("####################################\n");

        if (updated_cookie) EXPORTER_FREE(updated_cookie);
    }

    for (itr = start_itr; itr < n_tests; itr++) {
        printf("###### Executing Unit test %.2d ######\n", itr+1);

        updated_cookie = exporter_util_edit_cookie_value(test_values[itr].input, strlen(test_values[itr].input),
                "testdomain.com",
                "service1-mockfirm-com.d.zsproxy.net",
                1,
                EXPORTER_MANAGED_BA_DELETE_ALWAYS);
        printf("Set-Cookie: [%.*s]\n", (int)strlen(test_values[itr].input), test_values[itr].input);
        printf("Updated Set-Cookie: [%.*s]\n", updated_cookie ? (int)strlen(updated_cookie):0, updated_cookie?updated_cookie:"");
        printf("####################################\n");

        if (updated_cookie) EXPORTER_FREE(updated_cookie);
    }

    int all_cases_passed = 1;
    for (itr = start_itr; itr < n_tests; itr++) {
        all_cases_passed = all_cases_passed & test_values[itr].status;
        printf("Result for Test Case %.2d %s\n", itr+1, test_values[itr].status  ? "PASS" : "FAIL");
    }

    if (all_cases_passed) return 0;

    return 1;
}


void redirect_header_edit_test()
{
  struct result location_hdr_test[] = {
        {
            .input = "https://internal.com/x/y/z",
            .output_exp = "https://external-mockfirm-com.d.zsproxy.net/x/y/z"
        },
        {
            .input = "/x/y/z",
            .output_exp = "/x/y/z"
        },
        {
            .input = "/x/y?z=1",
            .output_exp = "/x/y?z=1"
        },
        {
            .input = "https://internalab.com/x/y/z",
            .output_exp = "https://internalab.com/x/y/z"
        },
        {
            .input = "http://internal.com/x/y/z",
            .output_exp = "http://external-mockfirm-com.d.zsproxy.net/x/y/z"
        },
        {
            .input = "https://internal.comat",
            .output_exp = "https://internal.comat"
        },
        {
            .input = "https://internal.com",
            .output_exp = "https://external-mockfirm-com.d.zsproxy.net"
        },
        {
            .input = "https://internal.com/",
            .output_exp = "https://external-mockfirm-com.d.zsproxy.net/"
        },
        {
            .input = "https://internal.coma/e/r",
            .output_exp = "https://internal.coma/e/r"
        },
   };

    int itr = 0;
    int start_itr = 0;
    char *updated_location_hdr = NULL;

    int n_tests = sizeof(location_hdr_test)/sizeof(struct result);

    printf("###### Executing Redirect hdr rewrite Unit test  ######\n");
    for (itr = start_itr; itr < n_tests; itr++) {

        /* internal.com should be replaced with external-mockfirm-com.d.zsproxy.net */
        updated_location_hdr = exporter_util_edit_redirect_header(location_hdr_test[itr].input,
                 strlen(location_hdr_test[itr].input),
                "internal.com",
                "external-mockfirm-com.d.zsproxy.net");

        printf("%.2d\nIncoming Location hdr: [%.*s]\n", itr+1, (int)strlen(location_hdr_test[itr].input), location_hdr_test[itr].input);
        printf("Expected Location hdr: [%.*s]\n", location_hdr_test[itr].output_exp ? (int)strlen(location_hdr_test[itr].output_exp):0,
                location_hdr_test[itr].output_exp ? location_hdr_test[itr].output_exp : "null");

       /* updated_location_hdr is NULL, that means we didnt find a match and hencewe didnt replace */
        if (!updated_location_hdr &&
            !(strcmp(location_hdr_test[itr].input, location_hdr_test[itr].output_exp)) ) {
            printf("Result for Location Header edit Test Case %.2d PASS \n", itr+1);
            printf("####################################\n");
            continue;
        }

        printf("Updated location hdr: [%.*s]\n", updated_location_hdr ? (int)strlen(updated_location_hdr):0,
                                                 updated_location_hdr?updated_location_hdr:"");
        location_hdr_test[itr].status = updated_location_hdr == location_hdr_test[itr].output_exp;
        if (updated_location_hdr && location_hdr_test[itr].output_exp)
            location_hdr_test[itr].status  = !strcmp(updated_location_hdr, location_hdr_test[itr].output_exp);
        printf("Result for Location Header edit Test Case %.2d %s\n", itr+1, location_hdr_test[itr].status  ? "PASS" : "FAIL");
        printf("####################################\n");

        if (updated_location_hdr) EXPORTER_FREE(updated_location_hdr);
    }

    return;
}
