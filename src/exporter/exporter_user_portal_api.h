/*
 * exporter_user_portal_api.h. Copyright (C) 2018 Zscaler Inc. All Rights Reserved
 *
 * Handle Portal API's.
 */


#ifndef __EXPORTER_USER_PORTAL_API_H__
#define __EXPORTER_USER_PORTAL_API_H__

#include "exporter/exporter_private.h"
#include "exporter/exporter_error_codes.h"

/*
 * Handle portal API request
 */
int handle_portal_api_request(struct exporter_request *request);

/*
 * Handle reprocess portal API request
 */
int handle_reprocess_portal_api_request(struct exporter_request *request, uint8_t *cookie);

/*
 * Initialize portal api global request processing state
 */
int exporter_portal_api_request_init(struct exporter *exporter);

int exporter_portal_request_log(struct exporter_request *request);
int exporter_portal_request_log_cleanup(struct exporter_request *request);

/* API URI suffixes */
#define EXPORTER_PORTAL_API_URI_USER            "/user"
#define EXPORTER_PORTAL_API_URI_COMPANY         "/company"
#define EXPORTER_PORTAL_API_URI_COMPANY_LOGO    "/company/logo"
#define EXPORTER_PORTAL_API_URI_PORTAL          "/portal"
#define EXPORTER_PORTAL_API_URI_PRA_PORTAL      "/praPortal"
#define EXPORTER_PORTAL_API_URI_PORTAL_LOGO     "/portal/logo"
#define EXPORTER_PORTAL_API_URI_AUP             "/aup"
#define EXPORTER_PORTAL_API_URI_LINKS           "/links"
#define EXPORTER_PORTAL_API_URI_LINKS_ICONS     "/links_icons"
#define EXPORTER_SRA_PORTAL_API_URI_CONSOLE_LINKS_ICONS "/console_links_icons"
#define EXPORTER_PORTAL_API_URI_ZAPP_LINKS      "/zapp_links"
#define EXPORTER_PORTAL_API_URI_LINK_LOGO       "/link/"
#define EXPORTER_PORTAL_API_URI_LOGOUT          "/logout"
#define EXPORTER_SRA_PORTAL_API_URI_GUAC_JS_LEGACY         "/zconsole/guacamole-common-js/all.min.js"
#define EXPORTER_SRA_PORTAL_API_URI_GUAC_JS                "/guacamole-common-js/all.min.js"
#define EXPORTER_SRA_PORTAL_API_URI_ZCONSOLE               "/zconsole"
#define EXPORTER_SRA_PORTAL_API_URI_GUAC_WEBSOCKET_TUNNEL  "/zconsole/websocket-tunnel"
#define EXPORTER_SRA_PORTAL_API_URI_CONSOLE_INFO           "/console_info"
#define EXPORTER_SRA_PORTAL_API_URI_SHARED_CONSOLE_LINKS_ICONS    "/shared_console_links_icons"
#define EXPORTER_SRA_PORTAL_API_URI_SEND_JS_FILE_LEGACY    "/zconsole/exporter_guac_pra_console.js"
#define EXPORTER_SRA_PORTAL_API_URI_SEND_JS_FILE           "/exporter_guac_pra_console.js"
#define EXPORTER_SRA_PORTAL_API_URI_SEND_CSS_FILE_LEGACY   "/zconsole/exporter_guac_pra_console.css"
#define EXPORTER_SRA_PORTAL_API_URI_SEND_CSS_FILE          "/exporter_guac_pra_console.css"
#define EXPORTER_SRA_PORTAL_API_URI_SEND_WOFF2_FILE_LEGACY "/zconsole/exporter_guac_pra_console.woff2"
#define EXPORTER_SRA_PORTAL_API_URI_SEND_WOFF2_FILE        "/exporter_guac_pra_console.woff2"
#define EXPORTER_SRA_PORTAL_API_URI_SEND_INTER_REGULAR_WOFF2  "/Inter-Regular.woff2"
#define EXPORTER_SRA_PORTAL_API_URI_SEND_INTER_REGULAR_WOFF  "/Inter-Regular.woff"
#define EXPORTER_SRA_PORTAL_API_URI_SEND_FA_SOLID_900_WOFF2  "/fa-solid-900.woff2"
#define EXPORTER_SRA_PORTAL_API_URI_SEND_FA_SOLID_900_TTF   "/fa-solid-900.ttf"
#define EXPORTER_SRA_PORTAL_API_URI_SCOPE_LINKS_ICONS       "/scope_links_icons"
#define EXPORTER_SRA_PORTAL_API_URL_ZSCOPE                  "/zscope"
#define EXPORTER_SRA_PORTAL_API_URL_CUSTOMERS               "/customers"
#define EXPORTER_SRA_PORTAL_API_URL_USERS                   "/users"
#define EXPORTER_SRA_PORTAL_API_URI_MY_FILES                "/my-files"
#define EXPORTER_SRA_PORTAL_API_URI_MY_FILES_USER_FILE            "/files/"
#define EXPORTER_SRA_PORTAL_API_URI_MY_FILES_USER_FILE_DOWNLOAD   "/downloadUrl"
#define EXPORTER_SRA_PORTAL_API_URI_MY_FILES_USER_FILES           "/files"
#define EXPORTER_SRA_PORTAL_API_URI_MY_FILES_COUNT                "/files/count"
#define EXPORTER_SRA_PORTAL_API_URI_MY_FILES_VIEW_UNINSPECTED     "/files?status=UNINSPECTED"
#define EXPORTER_SRA_PORTAL_API_URI_MY_FILES_UPLOAD_INITIATE      "/multipart-upload/initiate"
#define EXPORTER_SRA_PORTAL_API_URI_MY_FILES_UPLOAD               "/multipart-upload/upload"
#define EXPORTER_SRA_PORTAL_API_URI_MY_FILES_UPLOAD_COMPLETE      "/multipart-upload/complete"
#define EXPORTER_SRA_PORTAL_API_URI_MY_FILES_UPLOAD_ABORT         "/multipart-upload/abort"
#define EXPORTER_SRA_PORTAL_API_URI_REQUESTABLE_CONSOLE_LINKS_ICONS    "/privileged-consoles"
#define EXPORTER_SRA_PORTAL_API_URI_APPROVAL_SCOPES                    "/approval_scopes"
#define EXPORTER_SRA_PORTAL_API_URI_PRIVILEGED_APPROVALS               "/privileged-approvals"
#define EXPORTER_SRA_PORTAL_API_URI_PRIVILEGED_APPROVAL_REQUEST        "/approval-request"
#define EXPORTER_SRA_PORTAL_API_URI_GET_PRIVILEGED_APPROVAL_REQUESTS   "/approval-requests"
#define EXPORTER_SRA_PORTAL_API_URI_PORTAL                             "/portals"
#define EXPORTER_SRA_PORTAL_API_URI_DISPOSABLE_JUMPBOX         "/dispjumpbox"
#define EXPORTER_SRA_PORTAL_API_URI_DISPOSABLE_JUMPBOX_AMI_LIST "/ami"
#define EXPORTER_SRA_PORTAL_API_URI_DISPOSABLE_JUMPBOX_APPS_LIST "/apps"
#define EXPORTER_SRA_PORTAL_API_URI_DISPOSABLE_JUMPBOX_DESKTOPS_LIST "/desktops"
#define EXPORTER_SRA_PORTAL_API_URI_DISPOSABLE_JUMPBOX_VM       "/vm" /* create/delete */
#define EXPORTER_SRA_PORTAL_API_URI_DISPOSABLE_JUMPBOX_ACTIVE_LIST "/active"
#define EXPORTER_SRA_PORTAL_API_URI_DISPOSABLE_JUMPBOX_DJBINFO     "/djbInfo"

/* Length/size definitions */
#define EXPORTER_USER_PORTAL_AUTH_STATE_LEN                 (100)
#define EXPORTER_USER_PORTAL_API_RESPONSE_BUF_SIZE          (10 * 1024)
#define EXPORTER_USER_PORTAL_API_XLARGE_RESPONSE_BUF_SIZE   (10 * 1024 * 1024)
#define EXPORTER_USER_PORTAL_API_LARGE_RESPONSE_BUF_SIZE    (3 * 1024 * 1024)
#define EXPORTER_USER_PORTAL_API_MEDIUM_RESPONSE_BUF_SIZE   (2 * 1024 * 1024)
#define EXPORTER_USER_PORTER_API_SMALL_RESPONSE_BUFF_SIZE   (512)
#define EXPORTER_USER_PORTAL_ERROR_REASON_LEN               (100)
#define EXPORTER_HTTP_PROXY_ERROR_REASON_LEN                (512)
#define EXPORTER_USER_PORTAL_ID_STR_LEN                     (64)
#define EXPORTER_USER_PORTAL_NAME_STR_LEN                   (1024)
#define EXPORTER_USER_PORTAL_IMAGE_STR_LEN                  (200 * 1024)
#define EXPORTER_USER_PORTAL_FAVICON_IMAGE_STR_LEN          (12 * 1024)
#define EXPORTER_USER_PORTAL_MAX_LINKS                      (500)
#define EXPORTER_CUSTOMER_GID_LEN                           (128)
#define EXPORTER_USER_EMAIL_LEN                             (300)

/* Portal API error codes */
#define EXPORTER_PORTAL_API_ERROR_USER_FAIL         "portal.user.fail"
#define EXPORTER_PORTAL_API_ERROR_USER_LOGOUT_FAIL  "portal.user.logout.fail"
#define EXPORTER_PORTAL_API_ERROR_COMPANY_FAIL      "portal.company.fail"
#define EXPORTER_PORTAL_API_ERROR_PORTAL_FAIL       "portal.portal.fail"
#define EXPORTER_PORTAL_API_ERROR_AUP_FAIL          "portal.aup.fail"
#define EXPORTER_PORTAL_API_ERROR_LINKS_FAIL        "portal.links.fail"
#define EXPORTER_PORTAL_API_ERROR_ZAPP_LINKS_FAIL   "portal.zapp.links.fail"
#define EXPORTER_PORTAL_API_ERROR_LINKS_ICONS_FAIL  "portal.links.icons.fail"
#define EXPORTER_PORTAL_API_ERROR_CONSOLE_LINKS_ICONS_FAIL "portal.console.links.icons.fail"
#define EXPORTER_PORTAL_API_ERROR_COMPANY_LOGO_FAIL "portal.company.logo.fail"
#define EXPORTER_PORTAL_API_ERROR_PORTAL_LOGO_FAIL  "portal.portal.logo.fail"
#define EXPORTER_PORTAL_API_ERROR_LINK_ICON_FAIL    "portal.portal.link.icon.fail"
#define EXPORTER_PORTAL_API_ERROR_CONSOLE_INFO_FAIL "portal.console.info.fail"
#define EXPORTER_PORTAL_API_ERROR_CONSOLE_HTTP_PROXY_FAIL "portal.console.http_proxy.fail"
#define EXPORTER_PORTAL_API_ERROR_SCOPE_ICONS_FAIL  "portal.scope.links.icons.fail"
#define EXPORTER_PORTAL_API_ERROR_PRA_SERVICE_PROXY_FAIL  "portal.pra.service.proxy.fail"
#define EXPORTER_PORTAL_API_ERROR_PRA_DESKTOPS_FAIL "portal.pra.desktops.fail"
int
resolve_console_id_from_remote_guac_session_string(struct exporter_request *request,
						                           char *session_string);

/*
 * Get portal api type from request
 */
enum zpa_user_portal_api_type exporter_get_user_portal_api_type(struct exporter_request *request);

enum zpa_user_portal_api_type exporter_get_user_portal_zscope_api_type(struct exporter_request *request);

/*
 * Get portal api version from request
 */
int exporter_get_user_portal_api_version_len(enum zpa_user_portal_api_version api_version);
enum zpa_user_portal_api_version exporter_get_user_portal_api_version(struct exporter_request *request);

/*
 * Get portal zscope/zconcole URI identifier len from request
 */
int exporter_get_user_portal_id_len(int64_t zconsole_id);

/*
 * Portal API stats clear
 */
void exporter_portal_api_stats_clear();

/*
 * Portal API stats dump
 */
void exporter_portal_api_stats_dump(struct zpath_debug_state *request_state);
int is_privileged_policy_disabled(int64_t customer_gid);

/*
 * PRA VNC config disable
 */
int is_vnc_globally_disabled();
int is_vnc_disabled(int64_t customer_gid);

/*
 * PRA REALVNC config disable
 */
int is_realvnc_globally_disabled();
int is_realvnc_disabled(int64_t customer_gid);

/* Valid for User and PRA portal */
int is_markdown_content_enabled(int64_t customer_gid);

/*
 * Get console ID
 */
int
extract_sessionid_from_request(struct exporter_request *request, char *console_id_string, int console_id_len);
int exporter_portal_api_error_response(struct exporter_request *request, enum http_status status, enum exporter_error_codes error, char *id, char *reason, char *domain, char *name_id);
int exporter_portal_defer_api_request_async(struct exporter_request *request);


int
extract_query_value_from_request(struct exporter_request *request,
                                char *query_value,
                                size_t length,
                                const char *parameter);

enum zpa_user_portal_api_type exporter_get_portal_my_files_api_type(struct exporter_request *request);
int exporter_sra_portal_http_pra_service_proxy_api(struct exporter_request *request);
#endif /* __EXPORTER_USER_PORTAL_API_H__ */
