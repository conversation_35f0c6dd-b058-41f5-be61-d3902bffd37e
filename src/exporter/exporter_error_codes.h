/*
 * exporter_error_codes.h. Copyright (C) 2023 Zscaler Inc. All Rights Reserved
 */

/* This file includes the error codes the exporter should return in the response
 * If status is 200, 201, etc (In case of no error) return the error code EXPORTER_ERROR_CODE_NO_ERROR
 * If status is 403, 404, 500, etc (In case of error) return one of the error codes below if it matches
 * your error reason or create a new one. Please update the comments and add the proper error
 * code number in the comments in case you create a new error code. Please also update the following
 * confluence page https://confluence.corp.zscaler.com/pages/viewpage.action?pageId=389406664
 * if you create a new error code
 */


#ifndef __EXPORTER_ERROR_CODES_H__
#define __EXPORTER_ERROR_CODES_H__


#include <stdio.h>

enum exporter_error_codes {
    EXPORTER_ERROR_CODE_NO_ERROR,                                                  /* Error Code Number: 0
                                                                                    * No error, sent with response code 200 and similar successful response types
                                                                                    */

    EXPORTER_ERROR_CODE_NO_HOST_HEADER,                                            /* Error Code Number: 1
                                                                                    * No Host header present in the request. A Host header field must be sent in
                                                                                    * all HTTP/1.1 request messages. A 400 (Bad Request) status code may be sent
                                                                                    * to any HTTP/1.1 request message that lacks or contains more than one Host
                                                                                    * header field.
                                                                                    */

    EXPORTER_ERROR_CODE_UNSUPPORTED_VERSION,                                       /* Error Code Number: 2
                                                                                    * Unsupported Http Version in the request header. Exporter only supports HTTP/1.1
                                                                                    */

    EXPORTER_ERROR_CODE_HOST_SNI_MISMATCH,                                         /* Error Code Number: 3
                                                                                    * Hostname in the Host header is not matching wiht the TLS connection SNI. The
                                                                                    * request is not meant for this TLS connection!!
                                                                                    */

    EXPORTER_ERROR_CODE_ECOOKIE_NOT_FOUND,                                         /* Error Code Number: 4
                                                                                    * Could not find domain cookie in the request or was unable to decrypt it (Encryption
                                                                                    * & Decryption of domain cookie is part of the url encryption feature which can be
                                                                                    * enabled per customer
                                                                                    */

    EXPORTER_ERROR_CODE_CRYPTO_COOKIE_NOT_FOUND,                                   /* Error Code Number: 5
                                                                                    * Could not find crypto cookie in the request. Crypto cookie must be present in the
                                                                                    * requests to the exporter. The initial request to the BA application may not have a
                                                                                    * Crypto Cookie in it, all requests after that must have the cookie.
                                                                                    */

    EXPORTER_ERROR_CODE_INVALID_HOST_HEADER,                                       /* Error Code Number: 6
                                                                                    * There was an error parsing the host header in the request. This happened mostly
                                                                                    * due to host header not being same as the SNI.
                                                                                    */

    EXPORTER_ERROR_CODE_INVALID_OPTIONS_REQUEST,                                   /* Error Code Number: 7
                                                                                    * Unauthenticated OPTIONS request, we consider an unauthenticated OPTIONS request
                                                                                    * valid if it meets the conditions of exporter_validate_no_auth_options_request()
                                                                                    * function
                                                                                    */

    EXPORTER_ERROR_CODE_INVALID_ASYNC_STATE,                                       /* Error Code Number: 8
                                                                                    * Invalid value was set for async_state field for exporter request {} refer
                                                                                    * enum exporter_request_async_state {} inside exporter_private.h
                                                                                    */

    EXPORTER_ERROR_CODE_INVALID_AUTH_DOMAIN,                                       /* Error Code Number: 9
                                                                                    * Received request for auth domain on application domain path. is_auth_domain was
                                                                                    * was set to true for the application domain which is not an auth domain!!
                                                                                    */

    EXPORTER_ERROR_CODE_OBJECT_STORES_UNAVAILABLE,                                 /* Error Code Number: 10
                                                                                    * No Object Store is connected to the exporter
                                                                                    */

    EXPORTER_ERROR_CODE_DIAMOND_SEARCH_FAILED,                                     /* Error Code Number: 11
                                                                                    * Path in the request did not match any path that the exporter services. We have
                                                                                    * 1. default_path  = "/ *"
                                                                                    * 2. auth_domain_from_customer_domain = '/sdasda/doauth'
                                                                                    * 3. auth_domain_from_sp = '/sdasda/fromsp'
                                                                                    * 4. customer_domain_install_cookie = '/sdasda/setcdcookie'
                                                                                    * 5. idp_query = '/sdasda/exporter_idp_query.js'
                                                                                    * this error is unlikely since the default path should cover all paths.
                                                                                    */

    EXPORTER_ERROR_CODE_CUSTOMER_GID_NOT_FOUND,                                    /* Error Code Number: 12
                                                                                    * Could not find customer gid in the orig_url. The orig_url has the customer domain
                                                                                    * with ?domain= query parameter. Either is the domain is missing or we do not have
                                                                                    * a gid for this customer domain in our database.
                                                                                    */

    EXPORTER_ERROR_CODE_INVALID_METHOD_FOR_AUTH,                                   /* Error Code Number: 13
                                                                                    * Invialid Http method on auth domain. We only allow GET or a valid OPTIONS request
                                                                                    * on auth domain. we consider an unauthenticated OPTIONS request valid if it meets
                                                                                    * the conditions of exporter_validate_no_auth_options_request() function.
                                                                                    */

    EXPORTER_ERROR_CODE_HOST_IN_URL_WITHOUT_HTTP_CONNECT,                          /* Error Code Number: 14
                                                                                    * Hostname is present in the url but HTTP method is not CONNECT
                                                                                    */

    EXPORTER_ERROR_CODE_CBI_PROFILE_NOT_FOUND,                                     /* Error Code Number: 15
                                                                                    * Could not get the profile for CBI. The exporter redirects the to the CBI environment
                                                                                    * if we have an isolation policy set for the application. The CBI profile is information
                                                                                    * is requeired to be passed in the redirect. we get that information from the
                                                                                    * zpn_cbi_profile_table. Possibly a config issue.
                                                                                    */

    EXPORTER_ERROR_CODE_CBI_PROOF_GENERATION_FAILED,                               /* Error Code Number: 16
                                                                                    * Could not generate a proof for CBI. CBI proof is sent as a part of redirect by the
                                                                                    * exporter to the CBI environment. It is sent as ?proof= in the redirect.
                                                                                    */

    EXPORTER_ERROR_CODE_INVALID_CORS_TOKEN,                                        /* Error Code Number: 17
                                                                                    * Unable to validate the signature on the CORS token. CORS token is found in the query
                                                                                    * parameter of the request ?ctoken=
                                                                                    */

    EXPORTER_ERROR_CODE_ISOLATION_POLICY_CHECK_FAILED,                             /* Error Code Number: 18
                                                                                    * Isolation policy check failed for the request. application not provisioned for CBI
                                                                                    */

    EXPORTER_ERROR_CODE_DEFAULT_PATH_CALLBACK_FAILED,                              /* Error Code Number: 19
                                                                                    * Request to the default_path ie '/ *' for the application domain failed
                                                                                    */

    EXPORTER_ERROR_CODE_NO_AUTH_OPTIONS_DISABLED,                                  /* Error Code Number: 20
                                                                                    * Unauthenticated OPTIONS request with no_auth_options disabled in zpn_client_less
                                                                                    * table.
                                                                                    */

    EXPORTER_ERROR_CODE_UNAUTHENTICATED_OPTIONS_USER_PORTAL_REQUEST,               /* Error Code Number: 21
                                                                                    * Unauthenticated OPTIONS request for User Portal. User Portal does not serve to
                                                                                    * unauthenticated OPTIONS requests.
                                                                                    */

    EXPORTER_ERROR_CODE_UNAUTHENTICATED_INVALID_OPTIONS_REQUEST,                   /* Error Code Number: 22
                                                                                    * Unauthenticated OPTIONS request is not valid. we consider an unauthenticated OPTIONS
                                                                                    * request valid if it meets the conditions of exporter_validate_no_auth_options_request()
                                                                                    * function.
                                                                                    */

    EXPORTER_ERROR_CODE_REQUEST_UNAVAILABLE,                                       /* Error Code Number: 23
                                                                                    * Request url belongs to the unavailable resources list of the exporter. This list
                                                                                    * currently only has "/favicon.ico"
                                                                                    */

    EXPORTER_ERROR_CODE_NOT_GET_REQUEST,                                           /* Error Code Number: 24
                                                                                    * We only support unauthenticated GET requests and OPTIONS (if no_auth_options enabled in
                                                                                    * zpn_client_less able and it the request meets the conditions of
                                                                                    * exporter_validate_no_auth_options_request() function.) requests.
                                                                                    */

    EXPORTER_ERROR_CODE_CORS_TOKEN_GENERATION_FAILED,                              /* Error Code Number: 25
                                                                                    * Could not generate a CORS token. CORS token is send as a part of CORS request by the
                                                                                    * exporter. It is sent as '?ctoken=' query parameter
                                                                                    */

    EXPORTER_ERROR_CODE_CRYPTO_COOKIE_GENERATION_FAILED,                           /* Error Code Number: 26
                                                                                    * Crypto Cookie generation failed. We generate a Crypto Cookie if the initial BA
                                                                                    * application request does not have one.
                                                                                    */

    EXPORTER_ERROR_CODE_DOMAIN_COOKIE_GENERATION_FAILED,                           /* Error Code Number: 27
                                                                                    * Domain cookie generation failed. Domain cookie is associated with the BA app domain that
                                                                                    * that was requested. We pass it as '?cookie=' query parameter, which we later set to the
                                                                                    * app domain in the redirect to install cookie '/setdcookie'.
                                                                                    */

    EXPORTER_ERROR_CODE_DOMAIN_COOKIE_SET_FAILED,                                  /* Error Code Number: 28
                                                                                    * Failed to set the Domain cookie in the Object Store
                                                                                    */

    EXPORTER_ERROR_CODE_DOMAIN_COOKIE_ENCRYPTION_FAILED,                           /* Error Code Number: 29
                                                                                    * Failed to encrypt the Domain cookie. (Encryption & Decryption of domain cookie is part
                                                                                    * of the url encryption feature which can be enabled per customer.
                                                                                    */

    EXPORTER_ERROR_CODE_IDP_FETCH_FAILED,                                          /* Error Code Number: 30
                                                                                    * Failed to fetch IDP list for the customer from wally.
                                                                                    */

    EXPORTER_ERROR_CODE_IDP_REPLACE_FAILED,                                        /* Error Code Number: 31
                                                                                    * String replace operation failed while trying to replace idp_select_js with error string
                                                                                    */

    EXPORTER_ERROR_CODE_IDP_WRITE_FAILED,                                          /* Error Code Number: 32
                                                                                    * snprintf() operation failed while writing idp gid
                                                                                    */

    EXPORTER_ERROR_CODE_INVALID_CUSTOMER_DOMAIN,                                   /* Error Code Number: 33
                                                                                    * Customer has invalid domain name. Maybe domain name is not set right for the customer.
                                                                                    */

    EXPORTER_ERROR_CODE_NO_QUERY_STRING,                                           /* Error Code Number: 34
                                                                                    * Could not find query string in the request
                                                                                    */

    EXPORTER_ERROR_CODE_OSKEY_NOT_FOUND,                                           /* Error Code Number: 35
                                                                                    * Oskey (Assertion key) not sent by Samlsp. The oskey is sent in the query parameter by
                                                                                    * Samlsp redirect to the exporter '?oskey='. This is the key that is used by samlsp to store
                                                                                    * the user's assertion in the Object Store.
                                                                                    */

    EXPORTER_ERROR_CODE_INVALID_COOKIE_TIMESTAMP,                                  /* Error Code Number: 36
                                                                                    * Invalid timestamp on the Assertion Cookie sent by Samlsp
                                                                                    */

    EXPORTER_ERROR_CODE_SESSION_KEY_GENERATION_FAILED,                             /* Error Code Number: 37
                                                                                    * Session key genetration failed. This session key will be used to generate the Session
                                                                                    * cookie
                                                                                    */

    EXPORTER_ERROR_CODE_SESSION_CREATION_FAILED,                                   /* Error Code Number: 38
                                                                                    * Failed to create a Session by using the session key.
                                                                                    */

    EXPORTER_ERROR_CODE_AUTH_TOKEN_ADD_TO_SESSION_FAILED,                          /* Error Code Number: 39
                                                                                    * Failed to add token to a session. for more information see 'Current format of session state:'
                                                                                    * inside exporter_session.c
                                                                                    */

    EXPORTER_ERROR_CODE_SESSION_KEY_WRITE_FAILED,                                  /* Error Code Number: 40
                                                                                    * Failed to write sesion into the Object Store
                                                                                    */

    EXPORTER_ERROR_CODE_NOT_REACHABLE,                                             /* Error Code Number: 41
                                                                                    * This part of code is not reachable. Should never get this error :p
                                                                                    */

    EXPORTER_ERROR_CODE_URL_DECODE_FAILED,                                         /* Error Code Number: 42
                                                                                    * Failed to url decode the error that we got from Samlsp. The error is sent by the Samlsp as a
                                                                                    * query parameter '?emsg='
                                                                                    */

    EXPORTER_ERROR_CODE_ERROR_STRING_NOT_FOUND,                                    /* Error Code Number: 43
                                                                                    * Expected error message from Samlsp but could not find it. The error is sent by the Samlsp as a
                                                                                    * query parameter '?emsg='
                                                                                    */

    EXPORTER_ERROR_CODES_PROXY_LOOP_DETECTED,                                      /* Error Code Number: 44
                                                                                    * We got a request whose Via header is exportor domain. So exporter sent forwarded to itself!!
                                                                                    */

    EXPORTER_ERROR_CODE_DISCONNECTED_FROM_SERVER,                                  /* Error Code Number: 45
                                                                                    * Exporter mtunnel to Broker got disconnected.
                                                                                    */

    EXPORTER_ERROR_CODE_CONNECTION_SETUP_FAILED,                                   /* Error Code Number: 46
                                                                                    * Exporter failed to setup mtunnel connection to the Broker
                                                                                    */

    EXPORTER_ERROR_CODE_INVALID_SESSION,                                           /* Error Code Number: 47
                                                                                    * Could not fetch the session using the session key from the Object Store
                                                                                    */

    EXPORTER_ERROR_CODE_API_USER_RESPONSE_FAILED,                                  /* Error Code Number: 48
                                                                                    * User Portal api response for path '/user' failed. Failed to fetch name_id, domain, auth_state
                                                                                    * details for this user.
                                                                                    */

    EXPORTER_ERROR_CODE_API_COMPANY_RESPONSE_FAILED,                               /* Error Code Number: 49
                                                                                    * User portal api response for path '/company' failed. Failed to fetch company details from
                                                                                    * zpath_customer table.
                                                                                    */

    EXPORTER_ERROR_CODE_API_PORTAL_RESPONSE_FAILED,                                /* Error Code Number: 50
                                                                                    * User portal api response for path '/portal' failed. Failed to fetch user / sra portal details
                                                                                    * from zpn_user_portal / zpn_sra_portal table
                                                                                    */

    EXPORTER_ERROR_CODE_API_PORTAL_AUP_RESPONSE_FAILED,                            /* Error Code Number: 51
                                                                                    * User portal api response for path '/aup' failed. Failed to fetch customer details from
                                                                                    * zpn_user_portal table or failed to fetch aup details from customer gid from zpn_user_portal_aup
                                                                                    * table. The above mentioned could be some of the reasons for failure.
                                                                                    */

    EXPORTER_ERROR_CODE_API_LINKS_RESPONSE_FAILED,                                 /* Error Code Number: 52
                                                                                    * User portal api response for path '/links' failed. Failed to fetch portal details from
                                                                                    * zpn_user_portal / zpn_sra_portal table or failed to fetch portal to link mapping information from
                                                                                    * zpn_user_portal_user_portal_link_mapping table or failed to fetch application links from
                                                                                    * zpn_user_portal_links table. The above mentioned could be some of the reasons for failure.
                                                                                    */

    EXPORTER_ERROR_CODE_API_LINKS_ICONS_RESPONSE_FAILED,                           /* Error Code Number: 53
                                                                                    * User portal api response for path '/link_icons' failed. Failed to fetch portal details from
                                                                                    * zpn_user_portal table or failed to fetch portal to link mapping information from
                                                                                    * zpn_user_portal_user_portal_link_mapping table or failed to fetch application links from
                                                                                    * zpn_user_portal_links table. The above mentioned could be some of the reasons for failure.
                                                                                    */

    EXPORTER_ERROR_CODE_API_SRA_LINKS_ICONS_RESPONSE_FAILED,                       /* Error Code Number: 54
                                                                                    * User portal api response for path '/console_links_icons' failed. Failed to fetch portal details from
                                                                                    * zpn_sra_portal table or failed to fetch portal to link mapping information from
                                                                                    * zpn_sra_portal_sra_console_mapping table or failed to fetch application links from
                                                                                    * zpn_sra_console table. The above mentioned could be some of the reasons for failure.
                                                                                    */

    EXPORTER_ERROR_CODE_API_SRA_PORTAL_RESPONSE_FAILED,                            /* Error Code Number: 55
                                                                                    * User portal api response for path '/console_info' failed. Failed to fetch console details from
                                                                                    * zpn_sra_console table or zpn_sra_application table. The above mentioned could be some of the reasons
                                                                                    * for failure
                                                                                    */

    EXPORTER_ERROR_CODE_API_ZAPP_LINKS_RESPONSE_FAILED,                            /* Error Code Number: 56
                                                                                    * User portal api response for path '/zapp_links' failed. Failed to fetch portal details from
                                                                                    * zpn_user_portal table. The above mentioned could be some of the reasons for failure.
                                                                                    */

    EXPORTER_ERROR_CODE_API_COMPANY_ICON_RESPONSE_FAILED,                          /* Error Code Number: 57
                                                                                    * User portal api response for path '/company/logo' failed. Failed to fetch portal details from
                                                                                    * zpn_user_portal table or failed to fetch company logo from zpath_customer_logo table.
                                                                                    * The above mentioned could be some of the reasons for failure.
                                                                                    */

    EXPORTER_ERROR_CODE_API_PORTAL_ICON_RESPONSE_FAILED,                           /* Error Code Number: 58
                                                                                    * User portal api response for path '/portal/logo' failed. Failed to fetch portal details from
                                                                                    * zpn_user_portal table. The above mentioned could be some of the reasons for failure.
                                                                                    */

    EXPORTER_ERROR_CODE_API_ICON_RESPONSE_FAILED,                                  /* Error Code Number: 59
                                                                                    * User portal api response for path '/link/' failed. Failed to fetch portal details from
                                                                                    * zpn_user_portal table or failed to fetch link information form zpn_user_portal_links table.
                                                                                    * The above mentioned could be some of the reasons for failure.
                                                                                    */

    EXPORTER_ERROR_CODE_API_USER_LOGOUT_RESPONSE_FAILED,                           /* Error Code Number: 60
                                                                                    * User portal api response for path '/logout' failed. Failed to clear assertion state from the
                                                                                    * Object Store. The above mentioned could be some of the reasons for failure.
                                                                                    */

    EXPORTER_ERROR_CODE_PRA_DISABLED,                                              /* Error Code Number: 61
                                                                                    * PRA / SRA is disabled for this api call.
                                                                                    */

    EXPORTER_ERROR_CODE_GUAC_MTUNNEL_NOT_FOUND,                                    /* Error Code Number: 62
                                                                                    * Could not create or get an mtunnel form the idle queue for this guac connection request
                                                                                    * to the Broker
                                                                                    */

    EXPORTER_ERROR_CODE_GUAC_API_WEBSOCKET_CONSOLE_WALLY_FAILED,                   /* Error Code Number: 63
                                                                                    * Failed to fetch console details from zpn_sra_console table.
                                                                                    */

    EXPORTER_ERROR_CODE_GUAC_API_WEBSOCKET_VNC_FEATURE_FLAG_SRA_DISABLED,          /* Error Code Number: 64
                                                                                    * VNC feature flag for sra_application is disabled. VNC applications are disabled by default in
                                                                                    * itasca and UI
                                                                                    */

    EXPORTER_ERROR_CODE_GUAC_API_WEBSOCKET_TUNNEL_RESPONSE_FAILED,                 /* Error Code Number: 65
                                                                                    * Guac api response for path '/v1/zconsole/websocket-tunnel' failed. Failed to fetch console details
                                                                                    * from zpn_sra_console table or request is not a websocket request with connection upgraded to web
                                                                                    * socket or VNC feature flag for sra_application is disabled.
                                                                                    */

    EXPORTER_ERROR_CODE_ATTACHING_USER_POSTURES_FAILED,                            /* Error Code Number: 66
                                                                                    * Failed to attach user postures to the user portal request.
                                                                                    */

    EXPORTER_ERROR_CODE_ATTACHING_TRUSTED_NETWORKS_FAILED,                         /* Error Code Number: 67
                                                                                    * Failed to attach trusted networks to the user portal request.
                                                                                    */

    EXPORTER_ERROR_CODE_ATTACHING_USER_STATE_FAILED,                               /* Error Code Number: 68
                                                                                    * Failed to attach hashed state to the user portal request.
                                                                                    */

    EXPORTER_ERROR_CODE_ERROR_BEFORE_RESPONSE_COMPLETE,                            /* Error Code Number: 69
                                                                                    * If http response is not complete and we received an error, send err to browser
                                                                                    */

    EXPORTER_ERROR_CODE_WEBSERVER_USER_PORTAL_DNS_CALLBACK_FAILED,                 /* Error Code Number: 70
                                                                                    * We have a dns resolution for the user portal and we failed to send request to the resolved web server
                                                                                    */

    EXPORTER_ERROR_CODE_ASSERTION_VALIDATION_FAILED,                               /* Error Code Number: 71
                                                                                    * SAML assertion validation failed for this exporter request
                                                                                    */

    EXPORTER_ERROR_CODE_ATTACHING_SCIM_GROUP_FAILED,                               /* Error Code Number: 72
                                                                                    * Failed to attach scim groups to this exporter request.
                                                                                    */

    EXPORTER_ERROR_CODE_ATTACHING_SCIM_ATTRIBUTES_FAILED,                          /* Error Code Number: 73
                                                                                    * Failed to attach scim attributes to this exporter request.
                                                                                    */

    EXPORTER_ERROR_CODE_INIT_SCIM_FAILED,                                          /* Error Code Number: 74
                                                                                    * Scim intialization failed
                                                                                    */

    EXPORTER_ERROR_CODE_ASSERTION_KEY_NOT_FOUND_FOR_CUSTOMER_DOMAIN,               /* Error Code Number: 75
                                                                                    * Assertion key was not not found in the session for the customer gid mapped to the customer domain
                                                                                    * got from the original url in the request.
                                                                                    */

    EXPORTER_ERROR_CODE_FAILED_TO_RETRIEVE_SESSION_KEY_FROM_OBJECT_STORE,          /* Error Code Number: 76
                                                                                    * Failed to retrive the session key from the Object Store by using the domain cookie.
                                                                                    */

    EXPORTER_ERROR_CODE_FAILED_TO_VALIDATE_DOMAIN_FROM_ORIGURL,                    /* Error Code Number: 77
                                                                                    * Faild to match the domain in the session key with the domain in the original url.
                                                                                    */

    EXPORTER_ERROR_CODE_FAILED_TO_FIND_SRA_PORTAL_OR_USER_PORTAL,                   /* Error Code Number: 78
                                                                                    * Failed to find sra portal or user portal details using customer_gid / customer domain.
                                                                                    */

    /* CSP */
    EXPORTER_ERROR_CODE_CSP_NONCE_NOT_FOUND,                                       /* Error Code Number: 79
                                                                                    * Failed to find CSP nonce to handle exception URL
                                                                                    */

    EXPORTER_ERROR_CODE_CSP_NONCE_INVALID,                                         /* Error Code Number: 80
                                                                                    * Invalid CSP nonce or replay attack being done
                                                                                    */

    EXPORTER_ERROR_CODE_CSP_JWT_NOT_SIGNED,                                        /* Error Code Number: 81
                                                                                    * Valid nonce but JWT signature is invalid
                                                                                    */

    EXPORTER_ERROR_CODE_CSP_NONCE_DELETION_FAILED,                                 /* Error Code Number: 82
                                                                                    * Failed to delete nonce
                                                                                    */

    EXPORTER_ERROR_CODE_NOT_POST_REQUEST,                                          /* Error Code Number: 83
                                                                                    * Only POST requests are allowed for fingerprint
                                                                                    */


    /* CSP */

    EXPORTER_ERROR_CODE_CUSTOMER_NOT_FOUND,                                        /* Error Code Number 84
                                                                                    * Unable to fetch customer from zpath_customer table from the customer_gid.
                                                                                    */

    EXPORTER_ERROR_CODE_CUSTOMER_TRAFFIC_DISABLED,                                 /* Error Code Number 85
                                                                                    * Customer Traffic is disbaled for this customer.
                                                                                    */

    EXPORTER_ERROR_CODE_API_SRA_SHARED_LINKS_ICONS_RESPONSE_FAILED,                /* Error Code Number: 86
                                                                                    * User portal api response for path '/shared_console_links_icons' failed.
                                                                                    */

    EXPORTER_ERROR_CODE_SRA_PORTAL_UNAUTHORIZED_USER,                               /* Error Code Number 87
                                                                                     * User is not authrorized to access the requested PRA portal
                                                                                     */

    EXPORTER_ERROR_CODE_SRA_PORTAL_SCOPE_INIT_FAILED,                                /* Error Code Number 88
                                                                                      * Unable to assign scope for the PRA request
                                                                                      */

    EXPORTER_ERROR_CODE_PRA_DTA_DISABLED,                                            /* Error Code Number: 89
                                                                                      * DTA or PRA-DTA is disabled for this api call.
                                                                                      */

    EXPORTER_ERROR_CODE_API_SRA_SCOPE_LINKS_ICONS_RESPONSE_FAILED,                   /* Error Code Number: 90
                                                                                      * User portal api response for path '/scope_links_icons' failed.
                                                                                      * Failed to fetch scope details from zpn_scope table or failed to get
                                                                                      * consoles mapped to the portal for the scope or failed to evaluate
                                                                                      * policy.
                                                                                      * The above mentioned could be some of the reasons for failure.
                                                                                      */

    EXPORTER_ERROR_CODE_API_SRA_PORTAL_HTTP_PROXY_RESPONSE_FAILED,                    /* Error Code Number: 91
                                                                                      * User portal api response for http proxy api's with path '/customers' failed.
                                                                                      */

    EXPORTER_ERROR_CODE_CUSTOMER_ALT_AUTHSP_FETCH_FAILED,                              /* Error Code Number: 92
                                                                                       * Failed to fetch alt_authsp for the customer from wally.
                                                                                       */
    EXPORTER_ERROR_CODE_GKEY_NOT_FOUND,                                                 /* Error Code Number: 93
                                                                                        * Failed to fetch gkey from access aware.
                                                                                       */
    EXPORTER_ERROR_CODE_CAA_DATA_NOT_FOUND,                                             /* Error Code Number: 94
                                                                                        * Failed to fetch caa data from object store.
                                                                                        */

    EXPORTER_ERROR_CODE_PRA_SERVICE_HTTP_PROXY_RESPONSE_FAILED,                        /* Error Code Number: 95
                                                                                       * PRA-Service Proxy error.
                                                                                       */
    EXPORTER_ERROR_CODE_PRA_CREDENTIAL_SERVICE_FAILURE,                                /* Error Code Number: 96
                                                                                       * PRA Credential Service Internal Failure
                                                                                       */
    EXPORTER_ERROR_CODE_PRA_NO_CREDENTIAL_AVAILABLE,                                    /* Error Code Number: 97
                                                                                        * PRA Credential Service Internal Failure
                                                                                        * All credentials from pool based on cred pool policy are already in-use
                                                                                        */

    EXPORTER_ERROR_CODE_BROWSER_PROFILE_ATTACH_FAILED,                                  /* Error Code Number: 98
                                                                                        * Failed to Attach Managed Browser profile state to policy
                                                                                        */
    EXPORTER_ERROR_CODE_PRA_DESKTOPS_DISABLED,                                          /* Error Code Number: 99
                                                                                         * PRA Desktops Capability is not enabled
                                                                                         */
    EXPORTER_ERROR_CODE_PRA_DESKTOPS_SERVICE_FAILURE,                                   /* Error Code Number: 100
                                                                                         * PRA Desktops Service Internal Failure
                                                                                         */

};


#endif /* __EXPORTER_ERROR_CODES_H__ */
