/*
 * exporter_guac_util.c Copyright (C) 2022 Zscaler Inc. All Rights Reserved.
 */

#include "parson/parson.h"
#include "base64/base64.h"
#include "exporter/exporter.h"
#include "exporter/exporter_guac_util.h"
#include "exporter/exporter_guac_proxy.h"
#include "exporter/exporter_guac_api_zia.h"
#include "zpath_lib/zpath_config_override.h"
#include "zpath_lib/zpath_instance.h"
#include "exporter/guacd_perf_limits.h"
#include "exporter/exporter_guac_api.h"
#include "zpath_lib/zpath_et_customer_zone.h"
#include "zpath_lib/zpath_et_service_endpoint.h"
#include "zpath_lib/zpath_customer.h"
#include "zpn/zpn_rule.h"
#include "exporter/exporter_user_portal_request_state.h"
#include "exporter/exporter_guac_sess_sharing.h"
#include "exporter/exporter_user_portal_api.h"
#include "exporter/zpn_sra_portal.h"
#ifdef UNIT_TEST
#include "exporter/exporter_myfiles_test.h"
#endif

#define EXPORTER_TENANT_ZONE_GET_CNT                  1000
#define EXPORTER_TENANT_ZONE_SERVICE_ENDPOINT_GET_CNT 1000
#define EXPORTER_SESSION_STORE_SERVICE_NAME           "service.zpa.pra.session.monitoring.sstore"
extern struct argo_structure_description* exporter_guac_session_recording_metadata_description;
extern struct argo_structure_description* exporter_guac_invite_user_email_metadata_description;
extern const char * keyboard_layout_names[];

/*
 * Return 0, if UX-improvements is globally enabled, i.e. if globalDisable = 0
 * config.feature.displayquality.globalDisable = 0 (Default)
 * config.feature.displayquality.globalDisable = 1 (Globally disable the feature)
 */
int
is_pra_display_quality_globally_disabled()
{
    int64_t config_value = 0;

    /* UX-improvements is not supported on non-ot exporter */
    if (!g_exporter_ot_mode) {
        return 1;
    }

    config_value = zpath_config_override_get_config_int(
                       UX_DISPLAY_QUALITY_FEATURE_GLOBAL_DISABLE,
                       &config_value,
                       DEFAULT_UX_DISPLAY_QUALITY_FEATURE_GLOBAL_DISABLE,
                       (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                       (int64_t)0); // Argument list terminator
    return config_value ? 1 : 0;
}

int
is_pra_display_quality_disabled(int64_t customer_gid)
{
    char *config_value        = NULL;
    int64_t root_customer_gid = 0;

    if (is_pra_display_quality_globally_disabled()) {
        return 1;
    }

    root_customer_gid = ZPATH_GID_GET_CUSTOMER_GID(zpath_instance_global_state.current_config->gid);

    /* check whether the value in config table for this customer is "disabled" */
    config_value = zpath_config_override_get_config_str(UX_DISPLAY_QUALITY,
                       &config_value,
                       DEFAULT_UX_DISPLAY_QUALITY,
                       customer_gid,
                       root_customer_gid,
                       (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                       (int64_t)0); // Argument list terminator
    if (strcmp(config_value, DEFAULT_UX_DISPLAY_QUALITY) == 0) {
        return 1; /* UX-improvements is disabled for the customer */
    } else {
        return 0; /* UX-improvements is enabled for the customer */
    }
}

static int
is_pra_enduser_approvals_globally_disabled()
{
    if (!g_exporter_ot_mode) {
        return 1;
    }

    int64_t config_value = zpath_config_override_get_config_int(
                       ENDUSER_APPROVALS_FEATURE_GLOBAL_DISABLE,
                       &config_value,
                       DEFAULT_ENDUSER_APPROVALS_FEATURE_GLOBAL_DISABLE,
                       (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                       (int64_t)0); // Argument list terminator
    return config_value ? 1 : 0;
}

int
is_pra_enduser_approvals_disabled(int64_t customer_gid)
{
    char *config_value        = NULL;
    int64_t root_customer_gid = 0;

    if (is_pra_enduser_approvals_globally_disabled()) {
        return 1;
    }

    root_customer_gid = ZPATH_GID_GET_CUSTOMER_GID(zpath_instance_global_state.current_config->gid);

    /* check whether the value in config table for this customer is "disabled" */
    config_value = zpath_config_override_get_config_str(ENDUSER_APPROVALS,
                       &config_value,
                       DEFAULT_ENDUSER_APPROVALS,
                       customer_gid,
                       root_customer_gid,
                       (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                       (int64_t)0); // Argument list terminator
    return ((strcmp(config_value, DEFAULT_ENDUSER_APPROVALS) == 0));
}

/*
 * Return 0, if session-monitoring is globally enabled, i.e. if globalDisable = 0
 * config.feature.sessionmonitoring.globalDisable = 0 (Default)
 * config.feature.sessionmonitoring.globalDisable = 1 (Globally disable the feature)
 */
int
is_pra_session_monitoring_globally_disabled()
{
    int64_t config_value = 0;

    /* session monitoring is not supported on non-ot exporter */
    if (!g_exporter_ot_mode) {
        return 1;
    }

    config_value = zpath_config_override_get_config_int(
                       SESSION_MONITORING_FEATURE_GLOBAL_DISABLE,
                       &config_value,
                       DEFAULT_SESSION_MONITORING_FEATURE_GLOBAL_DISABLE,
                       (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                       (int64_t)0); // Argument list terminator
    return config_value ? 1 : 0;
}

int
is_pra_session_monitoring_disabled(int64_t customer_gid)
{
    char *config_value        = NULL;
    int64_t root_customer_gid = 0;

    if (is_pra_session_monitoring_globally_disabled()) {
        return 1;
    }

    root_customer_gid = ZPATH_GID_GET_CUSTOMER_GID(zpath_instance_global_state.current_config->gid);

    /* check whether the value in config table for this customer is "disabled" */
    config_value = zpath_config_override_get_config_str(SESSION_MONITORING,
                       &config_value,
                       DEFAULT_SESSION_MONITORING,
                       customer_gid,
                       root_customer_gid,
                       (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                       (int64_t)0); // Argument list terminator
    if (strcmp(config_value, DEFAULT_SESSION_MONITORING) == 0) {
        return 1; /* session monitoring is disabled for the customer */
    } else {
        return 0; /* session monitoring is enabled for the customer */
    }
}

/*
 * Return 0, if privileged desktops is globally enabled, i.e. if globalDisable = 0
 * config.feature.privilegeddesktops.globalDisable = 0 (Default)
 * config.feature.privilegeddesktops.globalDisable = 1 (Globally disable the feature)
 */
int
is_pra_desktops_globally_disabled()
{
    int64_t config_value = 0;

    /* privileged desktops is not supported on non-ot exporter */
    if (!g_exporter_ot_mode) {
        return 1;
    }

    config_value = zpath_config_override_get_config_int(
                       PRIVILEGED_DESKTOPS_FEATURE_GLOBAL_DISABLE,
                       &config_value,
                       DEFAULT_PRIVILEGED_DESKTOPS_FEATURE_GLOBAL_DISABLE,
                       (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                       (int64_t)0); // Argument list terminator
    return config_value ? 1 : 0;
}

int
is_pra_desktops_disabled(int64_t customer_gid)
{
    char *config_value        = NULL;
    int64_t root_customer_gid = 0;

    if (is_pra_desktops_globally_disabled()) {
        return 1;
    }

    root_customer_gid = ZPATH_GID_GET_CUSTOMER_GID(zpath_instance_global_state.current_config->gid);

    /* check whether the value in config table for this customer is "disabled" */
    config_value = zpath_config_override_get_config_str(PRIVILEGED_DESKTOPS,
                       &config_value,
                       DEFAULT_PRIVILEGED_DESKTOPS,
                       customer_gid,
                       root_customer_gid,
                       (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                       (int64_t)0); // Argument list terminator
    if (strcmp(config_value, DEFAULT_PRIVILEGED_DESKTOPS) == 0) {
        return 1; /* privileged desktops is disabled for the customer */
    } else {
        return 0; /* privileged desktops is enabled for the customer */
    }
}

/* Check if pra session proctoring email notification feature is disabled for this gid */
int is_pra_email_notification_disabled_for_customer(int64_t customer_gid)
{
    char *config_value = NULL;
    int64_t root_customer_gid = ZPATH_GID_GET_CUSTOMER_GID(zpath_instance_global_state.current_config->gid);

    config_value = zpath_config_override_get_config_str(PRA_SESSION_PROCTORING_EMAIL_NOTIFICATION_FEATURE,
                                                        &config_value,
                                                        DEFAULT_PRA_SESSION_PROCTORING_EMAIL_NOTIFICATION_FEATURE,
                                                        customer_gid,
                                                        root_customer_gid,
                                                        (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                        (int64_t)0); // Argument list terminator

    /* Value in config table is "disabled" for this customer */
    if (strcasecmp(config_value, DEFAULT_PRA_SESSION_PROCTORING_EMAIL_NOTIFICATION_FEATURE) == 0) {
        return 1;
    } else {
        return 0;
    }
}

/* Large Portals with more than 100 micro-tenants or more than 1000 consoles are rate limited */
int64_t get_allowed_large_portal_access_count()
{
    int64_t config_value = 0;

    config_value = zpath_config_override_get_config_int(PRA_RATE_LIMIT_LARGE_PORTAL_ACCESS,
                                                        &config_value,
                                                        DEFAULT_PRA_RATE_LIMIT_LARGE_PORTAL_ACCESS,
                                                        zpath_instance_global_state.current_config->gid,
                                                        ZPATH_GID_GET_CUSTOMER_GID(zpath_instance_global_state.current_config->gid),
                                                        (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                        (int64_t)0); // Argument list terminator
    return config_value;
}

static int exporter_get_service_endpoint_internal(int64_t zone_id, const char *service_name, char **url)
{
    struct et_service_endpoint *endpoints[EXPORTER_TENANT_ZONE_SERVICE_ENDPOINT_GET_CNT] = {0};
    size_t endpoints_count = EXPORTER_TENANT_ZONE_SERVICE_ENDPOINT_GET_CNT;

    int res = zpath_et_service_endpoint_get(zone_id, endpoints, &endpoints_count);
    if (res != ZPATH_RESULT_NO_ERROR) {
        EXPORTER_DEBUG_SESSION_RECORDING("REC_UPLOAD: Failed to get service endpoint zone %"PRId64, zone_id);
        return ZPATH_RESULT_ERR;
    }

    /* retry with more memory */
    if (endpoints_count >= EXPORTER_TENANT_ZONE_SERVICE_ENDPOINT_GET_CNT) {
        endpoints_count = 2 * EXPORTER_TENANT_ZONE_SERVICE_ENDPOINT_GET_CNT;

        struct et_service_endpoint **endpoints_new = ((struct et_service_endpoint **)
            EXPORTER_CALLOC(endpoints_count * sizeof(struct et_service_endpoint *)));

        res = zpath_et_service_endpoint_get(zone_id, endpoints_new, &endpoints_count);
        if (res) {
            EXPORTER_DEBUG_SESSION_RECORDING("REC_UPLOAD: Failed to get endpoint, zone %"PRId64", %s\n",
                zone_id, zpath_result_string(res));

            EXPORTER_FREE(endpoints_new);
            return ZPATH_RESULT_ERR;
        }

        /* we have to iterate from 0 to cover deleted-entries scenario */
        for (size_t i = 0; i < endpoints_count; i++) {
            if (!endpoints[i]->name || !endpoints[i]->service_url) {
                continue;
            }
            if (0 == strcmp(service_name, endpoints[i]->name)) {
                EXPORTER_DEBUG_SESSION_RECORDING("REC_UPLOAD: zone_id %"PRId64", service-url %s",
                        zone_id, endpoints[i]->service_url);

                *url = EXPORTER_STRDUP(endpoints[i]->service_url, strlen(endpoints[i]->service_url));

                EXPORTER_FREE(endpoints_new);
                return ZPATH_RESULT_NO_ERROR;
            }
        }
    } else {
        for (size_t i = 0; i < endpoints_count; i++) {
            if (!endpoints[i]->name || !endpoints[i]->service_url) {
                continue;
            }
            if (0 == strcmp(service_name, endpoints[i]->name)) {
                EXPORTER_DEBUG_SESSION_RECORDING("REC_UPLOAD: zone_id %"PRId64", service-url %s",
                        zone_id, endpoints[i]->service_url);

                *url = EXPORTER_STRDUP(endpoints[i]->service_url, strlen(endpoints[i]->service_url));
                return ZPATH_RESULT_NO_ERROR;
            }
        }
    }
    return ZPATH_RESULT_ERR;
}

/* Get policy name based on the policy rule id */
int exporter_get_policy_name_by_rule_id(struct exporter_request *request) {
    int    res = ZPATH_RESULT_NO_ERROR;
    struct zpn_rule *rule;
    size_t rule_count = 1;

    int64_t policy_rule_id = request->guac_info->capabilities_policy_id;
    res = zpn_rule_get_by_gid(policy_rule_id, &rule, &rule_count);
    if (res == ZPATH_RESULT_NO_ERROR && rule && rule->name && !request->guac_info->policy_name) {
        request->guac_info->policy_name = EXPORTER_STRDUP(rule->name, strlen(rule->name));
        EXPORTER_DEBUG_SESSION_SHARING("[SESS_PROC] Got policy name=%s for the policy_rule_id=%"PRId64"",
            request->guac_info->policy_name, policy_rule_id);
    } else {
        if (res == ZPATH_RESULT_ASYNCHRONOUS) {
            EXPORTER_DEBUG_SESSION_SHARING("[SESS_PROC] Rule_get returned asynchronous policy_rule_id=%"PRId64"", policy_rule_id);
            __sync_fetch_and_add_4(&(request->async_count), 1);
            request->async_state = async_state_store_guac_sess_data;
        } else {
            EXPORTER_DEBUG_SESSION_SHARING("[SESS_PROC] Failed to get policy name for rule id %"PRId64", %s\n",
                policy_rule_id, zpath_result_string(res));
        }
        return res;
    }

    return res;
}

/*
 * Get service-url from et_service_endpoint based on tenant zone
 */
static int exporter_get_tenant_zone_service_url(int64_t customer_gid, const char *service_name, char **url)
{
    int        res              = ZPATH_RESULT_NO_ERROR;
    size_t     cust_zones_count = EXPORTER_TENANT_ZONE_GET_CNT;
    struct     et_customer_zone *cust_zones[EXPORTER_TENANT_ZONE_GET_CNT] = {0};

    res = zpath_et_customer_zone_get(customer_gid, cust_zones, &cust_zones_count);
    if (res) {
        EXPORTER_DEBUG_SESSION_RECORDING("REC_UPLOAD: Failed to get customer_zone, gid %"PRId64", %s\n",
            customer_gid, zpath_result_string(res));
        return ZPATH_RESULT_ERR;
    }

    /* retry with more memory */
    if (cust_zones_count >= EXPORTER_TENANT_ZONE_GET_CNT) {
        cust_zones_count = 2 * EXPORTER_TENANT_ZONE_GET_CNT;

        struct et_customer_zone **cust_zones_new = ((struct et_customer_zone **)
            EXPORTER_CALLOC(cust_zones_count * sizeof(struct et_customer_zone *)));

        res = zpath_et_customer_zone_get(customer_gid, cust_zones_new, &cust_zones_count);
        if (res) {
            EXPORTER_DEBUG_SESSION_RECORDING("REC_UPLOAD: Failed to get zone, gid %"PRId64", %s\n",
                customer_gid, zpath_result_string(res));

            EXPORTER_FREE(cust_zones_new);
            return ZPATH_RESULT_ERR;
        }

        /* compare all zones of the tenant against session recording zones */
        for (size_t i = 0; i < cust_zones_count; i++) {
            if (cust_zones_new[i] && cust_zones_new[i]->is_default) {
                int64_t zone_id = cust_zones_new[i]->zone_id;

                EXPORTER_FREE(cust_zones_new);
                return exporter_get_service_endpoint_internal(zone_id, service_name, url);
            }
        }
    } else {
        for (size_t i = 0; i < cust_zones_count; i++) {
            if (cust_zones[i] && cust_zones[i]->is_default) {
                return exporter_get_service_endpoint_internal(cust_zones[i]->zone_id, service_name, url);
            }
        }
    }
    return ZPATH_RESULT_ERR;
}

/* current default is 5MB */
int
pra_session_recording_multipart_chunk_size()
{
    return SESSION_RECORDING_MULTIPART_PART_SZ_MB;
}

/*
 * Return 0, if session recording is globally enabled, globalDisable = 0
 * config.feature.privileged.sessionrecording.globalDisable = 0 (Default)
 * config.feature.privileged.sessionrecording.globalDisable = 1 (Globally disable)
 */
int
is_pra_session_recording_globally_disabled()
{
    int64_t config_value = 0;

    /* session recording is not supported on non-ot exporter */
    if (!g_exporter_ot_mode) {
        return 1;
    }

    config_value = zpath_config_override_get_config_int(
                       SESSION_RECORDING_FEATURE_GLOBAL_DISABLE,
                       &config_value,
                       DEFAULT_SESSION_RECORDING_FEATURE_GLOBAL_DISABLE,
                       (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                       (int64_t)0); // Argument list terminator
    return config_value ? 1 : 0;
}

/*
 * For a customer, session secording is disabled by default
 * return 1 as feature is disabled by default.
 * return 1 if "feature.privileged.sessionrecording" is "disabled" for a
 *   specific customer or for all customers with global GID or global disable
 * return 0 if the "feature.privileged.sessionrecording" is "enabled" for a
 *   specific customer and globally enabled
 */
int
is_pra_session_recording_disabled(int64_t customer_gid)
{
    char *config_value = NULL;
    int64_t root_customer_gid = 0;

    if (is_pra_session_recording_globally_disabled()) {
        return 1;
    }

    root_customer_gid = ZPATH_GID_GET_CUSTOMER_GID(zpath_instance_global_state.current_config->gid);

    /* check whether the value in config table for this customer is "disabled" */
    config_value = zpath_config_override_get_config_str(SESSION_RECORDING,
                       &config_value,
                       DEFAULT_SESSION_RECORDING,
                       customer_gid,
                       root_customer_gid,
                       (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                       (int64_t)0); // Argument list terminator
    if (strcmp(config_value, DEFAULT_SESSION_RECORDING) == 0) {
        return 1; /* session recording is disabled for the customer */
    } else {
        return 0; /* session recording is enabled for the customer */
    }
}

/*
 * lifetime in terms of number of days.
 * If session recording is disabled, session should not be recorded.
 */
uint32_t
get_pra_session_recording_lifetime(int64_t customer_gid __attribute__((unused)))
{
    return SESSION_RECORDING_LIFETIME_YR;
}

/*
 * Return Object store URL configured for a customer.
 * Caller to free the url-memory after use.
 */
int exporter_get_tenant_session_store(int64_t customer_gid, char **url)
{
    if (is_pra_session_monitoring_disabled(customer_gid)) {
        return ZPATH_RESULT_ERR;
    }
    return exporter_get_tenant_zone_service_url(customer_gid,
        EXPORTER_SESSION_STORE_SERVICE_NAME, url);
}

#ifndef UNIT_TEST
/*
 * Return AWS S3 URL configured for a customer.
 * return NULL if session recording is disabled. Session should not be recorded.
 *
 * The upload API should perform the sanity of the returned string and
 * free the url memory after using it.
 */
int exporter_get_tenant_s3url(int64_t customer_gid, char *service, char **url)
{
    return exporter_get_tenant_zone_service_url(customer_gid, service, url);
}
#endif

/*
 * config.feature.privileged.filetransfer.globalDisable = 1 (Globally disable File Transfer)
 * config.feature.privileged.filetransfer.globalDisable = 0 (Default - File Transfer not globally disabled)
 */
int is_pra_ft_globally_disabled()
{
    int64_t config_value = 0;

    config_value = zpath_config_override_get_config_int(FILE_TRANSFER_FEATURE_GLOBAL_DISABLE,
                                                      &config_value,
                                                      DEFAULT_FILE_TRANSFER_FEATURE_GLOBAL_DISABLE,
                                                      (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                      (int64_t)0); // Argument list terminator
    EXPORTER_DEBUG_FILE_TRANSFER("File transfer global disable override = %"PRId64, config_value);
    return (config_value) ? 1 : 0;
}

/*
 * File Transfer is disabled by default in itasca and UI
 * is_pra_ft_disabled - returns 1 by default as feature is disabled by default.
 * is_pra_ft_disabled - returns 1 if "feature.privileged.filetransfer" = "disabled" for
 * a specific customer or for all customers with global GID or global disable
 */
#ifndef UNIT_TEST
int is_pra_ft_disabled(int64_t customer_gid)
{
    char *config_value = NULL;
    int64_t root_customer_gid   = 0;

    if (is_pra_ft_globally_disabled()) return 1;

    if (!g_exporter_ot_mode) return 1;

    root_customer_gid = ZPATH_GID_GET_CUSTOMER_GID(zpath_instance_global_state.current_config->gid);
    config_value = zpath_config_override_get_config_str(FILE_TRANSFER,
                                                      &config_value,
                                                      DEFAULT_FILE_TRANSFER,
                                                      customer_gid,
                                                      root_customer_gid,
                                                      (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                      (int64_t)0); // Argument list terminator
    EXPORTER_DEBUG_FILE_TRANSFER("File transfer feature flag  = %s", config_value);
    /* Value in config table is "disabled" for this customer */
    if (strcmp(config_value, DEFAULT_FILE_TRANSFER) == 0) {
        return 1;
    } else {
        return 0;
    }
}
#endif

/*
 * Faster File Transfer is disabled by default in UI
 * is_pra_faster_ft_disabled - returns 1 by default as feature is disabled by default.
 * is_pra_faster_ft_disabled - returns 1 if "feature.privileged.filetransfer" = "disabled" for
 * a specific customer or for all customers with global GID or global disable
 */
int is_pra_faster_ft_disabled(int64_t customer_gid)
{
    char *config_value = NULL;
    int64_t root_customer_gid   = 0;

    if (!g_exporter_ot_mode) return 1;

    root_customer_gid = ZPATH_GID_GET_CUSTOMER_GID(zpath_instance_global_state.current_config->gid);
    config_value = zpath_config_override_get_config_str(FASTER_FILE_TRANSFER,
                                                      &config_value,
                                                      DEFAULT_FASTER_FILE_TRANSFER,
                                                      customer_gid,
                                                      root_customer_gid,
                                                      (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                      (int64_t)0); // Argument list terminator
    EXPORTER_DEBUG_FILE_TRANSFER("Faster file transfer feature flag  = %s", config_value);
    /* Value in config table is "disabled" for this customer */
    if (strcmp(config_value, DEFAULT_FASTER_FILE_TRANSFER) == 0) {
        return 1;
    } else {
        return 0;
    }
}

int get_pra_guacd_service_zpa_cpu_load_limit()
{
    int64_t config_value = 0;

    config_value = zpath_config_override_get_config_int(PRA_GUACD_SERVICE_ZPA_CPU_LOAD_LIMIT,
                                                      &config_value,
                                                      DEFAULT_CPU_LOAD_LIMIT,
                                                      (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                      (int64_t)0); // Argument list terminator
    return config_value;
}

int get_pra_guacd_service_zpa_mem_load_limit()
{
    int64_t config_value = 0;

    config_value = zpath_config_override_get_config_int(PRA_GUACD_SERVICE_ZPA_MEM_LOAD_LIMIT,
                                                      &config_value,
                                                      DEFAULT_MEM_LOAD_LIMIT,
                                                      (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                      (int64_t)0); // Argument list terminator
    return config_value;
}

int get_pra_guacd_service_zpa_max_sessions_limit()
{
    int64_t config_value = 0;

    config_value = zpath_config_override_get_config_int(PRA_GUACD_SERVICE_ZPA_MAX_SESSIONS_LIMIT,
                                                      &config_value,
                                                      -1,
                                                      (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                      (int64_t)0); // Argument list terminator
    return config_value;
}

int is_pra_health_check_enabled()
{
    int64_t config_value = 0;

    config_value = zpath_config_override_get_config_int(PRA_HEALTH_CHECK_ENABLE,
                                                      &config_value,
                                                      DEFAULT_PRA_HEALTH_CHECK_ENABLE,
                                                      zpath_instance_global_state.current_config->gid,
                                                      ZPATH_GID_GET_CUSTOMER_GID(zpath_instance_global_state.current_config->gid),

                                                      (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                      (int64_t)0); // Argument list terminator
    EXPORTER_LOG(AL_WARNING, "PRA Health Check config override status = %"PRId64, config_value);
    return config_value;
}

int get_pra_session_proctoring_zpa_max_join_users_limit(int64_t customer_gid)
{
    int64_t config_value = 0;

    config_value = zpath_config_override_get_config_int(PRA_SESSION_PROCTORING_ZPA_MAX_JOIN_USERS_LIMIT,
                                                      &config_value,
                                                      DEFAULT_PRA_SESSION_PARTICIPANT_JOIN_COUNT,
                                                      customer_gid,
                                                      (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                      (int64_t)0); // Argument list terminator
    return (int)config_value;
}

/*
 * config.feature.guacdservice.globalDisable = 1 (Globally disable Guacd Service feature)
 * config.feature.guacdservice.globalDisable = 0 (Default - Guacd Service feature not globally disabled)
 */
int is_pra_guacd_service_globally_disabled()
{
    int64_t config_value = 0;

    config_value = zpath_config_override_get_config_int(PRA_GUACD_SERVICE_GLOBAL_DISABLE,
                                                      &config_value,
                                                      DEFAULT_PRA_GUACD_SERVICE_GLOBAL_DISABLE,
                                                      (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                      (int64_t)0); // Argument list terminator
    return (config_value) ? 1 : 0;
}

/*
 * Guacd Service feature is not disabled by default in itasca and UI
 * is_pra_guacd_service_disabled - returns 1 if "feature.guacdservice.globalDisable" = 1 for
 * all customers with global GID or global disable
 */
int is_pra_guacd_service_disabled()
{
    if (is_pra_guacd_service_globally_disabled()) {
        set_guacd_service_stat_to_disabled();
        return 1;
    }

    if (!g_exporter_guacd_tunnel_forward) {
        set_guacd_service_stat_to_disabled();
        return 1;
    }

    set_guacd_service_stat_to_enabled();
    return 0;

}

int get_max_file_size(int64_t customer_gid, int inspection_on) {

    int64_t config_value = 0;

    if (inspection_on) {
        config_value = zpath_config_override_get_config_int(FILE_SCAN_MAX_SIZE,
                                                      &config_value,
                                                      MAX_ZIA_SCAN_FILE_SIZE,
                                                      customer_gid,
                                                      (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                      (int64_t)0); // Argument list terminator
    } else {
        config_value = zpath_config_override_get_config_int(FILE_TRANSFER_MAX_SIZE,
                                                      &config_value,
                                                      MAX_TRANSFER_FILE_SIZE,
                                                      customer_gid,
                                                      (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                      (int64_t)0); // Argument list terminator
    }
    return (int) config_value;
}

static char *get_file_exception_list_for_customer(int64_t customer_gid) {
    char *config_value = NULL;
    int64_t root_customer_gid   = 0;

    root_customer_gid = ZPATH_GID_GET_CUSTOMER_GID(zpath_instance_global_state.current_config->gid);
    config_value = zpath_config_override_get_config_str(FILE_TRANSFER_FILE_SCAN_EXCEPTION_LIST,
                                                      &config_value,
                                                      NULL, // Default value
                                                      customer_gid,
                                                      root_customer_gid,
                                                      (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                      (int64_t)0); // Argument list terminator

    return config_value;
}

/*
 * Retrieve config.feature.privileged.filetransfer.fileException value.
 * config.feature.privileged.filetransfer.fileException is a comma-separated
 * string, example, sh,jpeg,html
 * If file type is in the exception list, return 1.
 * Else return 0, if config.feature.privileged.filetransfer.fileException is
 * not configured or file type is not present.
 */
int is_file_type_excepted_for_customer(int64_t customer_gid, const char *filetype) {
    char    *config_value         = NULL;
    char    *delim = ",";

    config_value = get_file_exception_list_for_customer(customer_gid);
    if (!config_value || config_value[0] == '\0') {
        // Exception list is not configured for the customer, return false
        return 0;
    }

    char    file_type_list[strnlen(config_value, FILE_TYPE_EXCEPTION_LIST_MAX_LENGTH) + 1];
    memcpy(file_type_list, config_value, sizeof(file_type_list) - 1);
    file_type_list[sizeof(file_type_list) - 1] = '\0';

    char *token = strtok(file_type_list, delim);
    while (token != NULL) {
        if (!strcasecmp(token, filetype)) {
            // Match , return true
            return 1;
        }
        token = strtok(NULL,delim);
    }
    return 0;
}

int exporter_guac_send_to_browser(struct exporter_request *request, struct evbuffer *buf, int len) {
    evbuffer_pullup(buf, len);
    len = websocket_build_frame(
            buf,
            len,
            WS_OP_TEXT | WS_FINAL_FRAME,
            NULL);
    len = evbuffer_remove_buffer(buf,
            bufferevent_get_output(request->conn->bev),
            len);
    return len;
}

/* Check if the connection is alive */
int is_exporter_wss_conn_alive(struct exporter_request *request)
{
    int alive = 0;
    if (request && request->mt && request->conn && !request->conn->destroying) {
        alive = 1;
    }
    return alive;
}

#ifndef UNIT_TEST
/*
 * Create a JSon Value entry for each file transfer operation to be
 * added to JSon array.
 */
static JSON_Value *exporter_log_create_file_entry_json(struct file_log *file_log) {
    JSON_Value *json_log_val = json_value_init_object();
    if (!json_log_val) {
        EXPORTER_LOG(AL_ERROR,"failed to init json object for file entry");
        return NULL;
    }

    JSON_Object *json_log_obj = json_value_get_object(json_log_val);
    if (!json_log_obj) {
        EXPORTER_LOG(AL_ERROR,"failed to get json object for file entry");
        return NULL;
    }

    if (file_log->file_name) {
        json_object_set_string(json_log_obj, "name", file_log->file_name);
    } else {
        json_object_set_string(json_log_obj, "name", "Unavailable");
    }

    if (file_log->file_action) {
        json_object_set_string(json_log_obj, "action", "Upload");
    } else {
        json_object_set_string(json_log_obj, "action", "Download");
    }

    if (file_log->status) {
        json_object_set_string(json_log_obj, "status", file_log->status);
    } else {
        json_object_set_string(json_log_obj, "status", "Unavailable");
    }

    if (file_log->start_ts) {
        json_object_set_number(json_log_obj, "start_ts", file_log->start_ts);
    } else {
        json_object_set_number(json_log_obj, "start_ts", 0);
    }

    if (file_log->end_ts) {
        json_object_set_number(json_log_obj, "end_ts", file_log->end_ts);
    } else {
        json_object_set_number(json_log_obj, "end_ts", 0);
    }

    /* Logs specific to file upload */
    if (!file_log->file_action) {
        return json_log_val;
    }

    if (file_log->privileged_file_id) {
        char file_id[EXPORTER_USER_PORTAL_ID_STR_LEN] = {'\0'};
        snprintf(file_id, sizeof(file_id), "%"PRId64, file_log->privileged_file_id);
        json_object_set_string(json_log_obj, "privileged_file_id", file_id);
        /*
         * privileged_file_id is used for Advanced FT. Inspection specific logs
         * can be skipped.
         */
        return json_log_val;
    }

    if (file_log->file_inspected) {
        json_object_set_string(json_log_obj, "inspected", "True");
    } else {
        json_object_set_string(json_log_obj, "inspected", "False");
    }

    if (file_log->file_type) {
        json_object_set_string(json_log_obj, "file_type", file_log->file_type);
    } else {
        json_object_set_string(json_log_obj, "file_type", "Unavailable");
    }

    if (file_log->file_md5) {
        json_object_set_string(json_log_obj, "md5", file_log->file_md5);
    } else {
        json_object_set_string(json_log_obj, "md5", "Unavailable");
    }

    if (file_log->inspection_verdict) {
        json_object_set_string(json_log_obj, "inspection_verdict", file_log->inspection_verdict);
    } else {
        json_object_set_string(json_log_obj, "inspection_verdict", "Unavailable");
    }

    if ((file_log->inspection_start_ts && file_log->inspection_end_ts) &&
        (file_log->inspection_end_ts > file_log->inspection_start_ts)) {
        char  time[32] = {'\0'};
        int offset = 0;
        int64_t time_taken = file_log->inspection_end_ts - file_log->inspection_start_ts;
        int min = time_taken / 60;
        int sec = time_taken % 60;
        if (min) {
            offset = snprintf(time, sizeof(time), "%d %s ", min, (min>1? "minutes":"minute"));
        }
        if (sec) {
            snprintf(time + offset, sizeof(time) - offset,
                    "%d %s", sec, (sec>1? "seconds":"second"));
        }
        json_object_set_string(json_log_obj, "inspection_time", time);
    } else if (file_log->inspection_end_ts == file_log->inspection_start_ts) {
        json_object_set_string(json_log_obj, "inspection_time", "less than 1 second");
    } else {
        json_object_set_string(json_log_obj, "inspection_time", "Unavailable");
    }
    return json_log_val;
}
/*
 * Create a new file transaction list for the first file
 * tranfer operation.
 */
static int exporter_log_create_file_transfer_list(struct exporter_request *request,
                                                    char **response_buf) {
    JSON_Value *json_root_val = NULL;
    JSON_Object *json_root_obj = NULL;
    int     res = ZPATH_RESULT_NO_ERROR;

    /* Init JSon response for the first entry */
    json_root_val = json_value_init_object();
    if (!json_root_val) {
        EXPORTER_LOG(AL_ERROR,"%s: failed to init json object to create file list", request->name);
        res = ZPATH_RESULT_ERR;
        goto do_exit;
    }

    json_root_obj = json_value_get_object(json_root_val);
    if (!json_root_obj) {
        EXPORTER_LOG(AL_ERROR,"%s: failed to get json object to create file list", request->name);
        res = ZPATH_RESULT_ERR;
        goto do_exit;
    }

    /* Create JSon Array for the first entry */
    JSON_Value* json_file_arr_val = json_value_init_array();
    if (!json_file_arr_val) {
        EXPORTER_LOG(AL_ERROR,"%s: failed to init json array to create file list", request->name);
        res = ZPATH_RESULT_ERR;
        goto do_exit;
    }
    JSON_Array  *json_file_arr = json_value_get_array(json_file_arr_val);
    if (!json_file_arr) {
        EXPORTER_LOG(AL_ERROR,"%s: failed to get json array to create file list", request->name);
        res = ZPATH_RESULT_ERR;
        goto do_exit;
    }

    /* Create JSon entry for the current file transfer */
    JSON_Value *json_file_entry_val = exporter_log_create_file_entry_json(&request->guac_info->file_log);
    if (!json_file_entry_val) {
        EXPORTER_LOG(AL_ERROR,"%s: failed to get create file entry json to create file list", request->name);
        res = ZPATH_RESULT_ERR;
        goto do_exit;
    }

    /* Add entry to the JSon array */
    if (json_array_append_value(json_file_arr, json_file_entry_val) != JSONSuccess) {
        EXPORTER_LOG(AL_ERROR,"%s: failed to append file entry json to file list", request->name);
        res = ZPATH_RESULT_ERR;
        goto do_exit;
    }

    if (json_object_set_value(json_root_obj, "file_list", json_file_arr_val) != JSONSuccess) {
        EXPORTER_LOG(AL_ERROR,"%s: failed to add json arry to file list", request->name);
        res = ZPATH_RESULT_ERR;
        goto do_exit;
    }

    size_t needed_size_in_bytes = json_serialization_size(json_root_val);
    *response_buf = EXPORTER_CALLOC(needed_size_in_bytes);
    if (!*response_buf) {
        EXPORTER_LOG(AL_ERROR,"%s: failed to allocate memory", request->name);
        res = ZPATH_RESULT_NO_MEMORY;
        goto do_exit;
    }

    /* Serialise JSon into response buffer */
    if (json_serialize_to_buffer(json_root_val, *response_buf, needed_size_in_bytes) != JSONSuccess) {
        EXPORTER_LOG(AL_ERROR,"%s: failed to serialise file list into buffer", request->name);
        EXPORTER_FREE(*response_buf);
        res = ZPATH_RESULT_ERR;
        goto do_exit;
    }
do_exit:
    if (json_root_val) {
        json_value_free(json_root_val);
    }
    return res;
}
/*
 * Appends new file trasaction entry to existing list of file
 * transaction.
 */
static int exporter_log_append_file_transfer_list(struct exporter_request *request,
                                                    char *file_list,
                                                    char **response_buf) {
    JSON_Value *json_root_val = NULL;
    JSON_Value  *json_root_val_raw_copy = NULL;
    JSON_Value *nodes_array_value_out = NULL;
    JSON_Object *json_root_obj = NULL;
    JSON_Array *nodes_array_in = NULL;
    JSON_Array *nodes_array_out = NULL;
    int     res = ZPATH_RESULT_NO_ERROR;

    /* Parse current file list to get JSon root object */
    json_root_val = json_parse_string(file_list);
    if (!json_root_val) {
        EXPORTER_LOG(AL_ERROR,"%s: failed to init json object to append to file list", request->name);
        res = ZPATH_RESULT_ERR;
        goto do_exit;
    }

    /* Deep copy existing JSon root object */
    json_root_val_raw_copy = json_value_deep_copy(json_root_val);
    if (!json_root_val_raw_copy) {
        EXPORTER_LOG(AL_ERROR,"%s: failed to copy existing file list", request->name);
        res = ZPATH_RESULT_ERR;
        goto do_exit;
    }

    json_root_obj = json_value_get_object(json_root_val_raw_copy);
    if (!json_root_obj) {
        EXPORTER_LOG(AL_ERROR,"%s: failed to get json object to append to file list", request->name);
        res = ZPATH_RESULT_ERR;
        goto do_exit;
    }

    /* Get existing JSon array */
    nodes_array_in = json_object_get_array(json_root_obj, "file_list");
    if (!nodes_array_in) {
        EXPORTER_LOG(AL_ERROR,"%s: failed to get json arr to append to file list", request->name);
        res = ZPATH_RESULT_ERR;
        goto do_exit;
    }

    int nodes_count = json_array_get_count(nodes_array_in);

    /* Build new nodes */
    nodes_array_value_out = json_value_init_array();
    if (!nodes_array_value_out) {
        EXPORTER_LOG(AL_ERROR,"%s: failed to init new json arr to append to file list", request->name);
        res = ZPATH_RESULT_ERR;
        goto do_exit;
    }
    nodes_array_out = json_value_get_array(nodes_array_value_out);
    if (!nodes_array_value_out) {
        EXPORTER_LOG(AL_ERROR,"%s: failed to get json object from new arr", request->name);
        res = ZPATH_RESULT_ERR;
        goto do_exit;
    }

    /* Deep copy each node from nodes_array_in to nodes_array_out */
    for (int i = 0; i < nodes_count; i++) {
        JSON_Value *in_value = json_array_get_value(nodes_array_in, i);
        if (json_array_append_value(nodes_array_out, json_value_deep_copy(in_value)) != JSONSuccess) {
            EXPORTER_LOG(AL_ERROR,"%s: failed to copy existing json arr to new json arr", request->name);
            res = ZPATH_RESULT_ERR;
            goto do_exit;
        }
    }

    /* Create JSon entry for the current file transfer */
    JSON_Value *json_file_entry_val = exporter_log_create_file_entry_json(&request->guac_info->file_log);
    if (!json_file_entry_val) {
        EXPORTER_LOG(AL_ERROR,"%s: failed to get create file entry json to create file list", request->name);
        res = ZPATH_RESULT_ERR;
        goto do_exit;
    }

    /* Append to the new array */
    if (json_array_append_value(nodes_array_out, json_file_entry_val) != JSONSuccess) {
        EXPORTER_LOG(AL_ERROR,"%s: failed to append file entry json to file list", request->name);
        res = ZPATH_RESULT_ERR;
        goto do_exit;
    }

    if (json_object_set_value(json_root_obj, "file_list", nodes_array_value_out) != JSONSuccess) {
        EXPORTER_LOG(AL_ERROR,"%s: failed to add json arry to file list", request->name);
        res = ZPATH_RESULT_ERR;
        goto do_exit;
    }

    size_t needed_size_in_bytes = json_serialization_size(json_root_val_raw_copy);
    *response_buf = EXPORTER_CALLOC(needed_size_in_bytes);
    if (!*response_buf) {
        EXPORTER_LOG(AL_ERROR,"%s: failed to allocate memory", request->name);
        res = ZPATH_RESULT_NO_MEMORY;
        goto do_exit;
    }

    /* Serialise the latest JSon array */
    if (json_serialize_to_buffer(json_root_val_raw_copy, *response_buf, needed_size_in_bytes) != JSONSuccess) {
        EXPORTER_LOG(AL_ERROR,"%s: failed to serialise file list into buffer", request->name);
        EXPORTER_FREE(*response_buf);
        res = ZPATH_RESULT_ERR;
        goto do_exit;
    }
do_exit:
    if (json_root_val_raw_copy) {
        json_value_free(json_root_val_raw_copy);
        json_root_val_raw_copy = NULL;
    }
    if (json_root_val) {
        json_value_free(json_root_val);
        json_root_val = NULL;
    }
    return res;
}

static int exporter_log_file_transfer_json(struct exporter_request *request, char **file_list) {
    int     res = ZPATH_RESULT_NO_ERROR;
    char    *new_file_list = NULL;

    if (!(*file_list)) {
        /* Create a new list */
        res = exporter_log_create_file_transfer_list(request, &new_file_list);
        if (res != ZPATH_RESULT_NO_ERROR) {
            EXPORTER_LOG(AL_ERROR, "%s: failed to create file list ", request->name);
            return ZPATH_RESULT_ERR;
        }
    } else {
        /* Append to existing list */
        res = exporter_log_append_file_transfer_list(request, *file_list, &new_file_list);
        if (res != ZPATH_RESULT_NO_ERROR) {
            EXPORTER_LOG(AL_ERROR, "%s: failed to append file list ", request->name);
            return ZPATH_RESULT_ERR;
        }
    }

    if (*file_list) {
        EXPORTER_FREE(*file_list);
        *file_list = NULL;
    }
    *file_list = new_file_list;
    if (!*file_list) {
        return ZPATH_RESULT_ERR;
    }
    return ZPATH_RESULT_NO_ERROR;
}

/*
 * Main function to send exporter log data.
 */
int exporter_guac_send_exporter_log_data(struct exporter_request *request, const char* status) {
    int res = 0;

    if (!is_exporter_wss_conn_alive(request) || !request->mt) {
        EXPORTER_LOG(AL_ERROR, "Skip exporter log call as connection is closing down");
        exporter_request_free_file_log(request);
        return ZPATH_RESULT_ERR;
    }

    request->mt->capabilities_policy_id = request->guac_info->capabilities_policy_id;

    if (status) {
        request->guac_info->file_log.status = EXPORTER_STRDUP(status, strlen(status));
    }
    request->guac_info->file_log.end_ts = epoch_s();

    res = exporter_log_file_transfer_json(request, &request->mt->file_transfer_list);
    if (res != ZPATH_RESULT_NO_ERROR) {
        EXPORTER_LOG(AL_ERROR, "%s: failed to update file list exporter log", request->name);
        exporter_request_free_file_log(request);
        return ZPATH_RESULT_NO_ERROR;
    }

    zpn_fohh_client_exporter_send_exporter_log_data(request->mt);

    // De-allocate memory allocated for file log
    exporter_request_free_file_log(request);
    return ZPATH_RESULT_NO_ERROR;
}
#endif

int exporter_guac_generate_json_scan_response(struct exporter_request *request,
                                              const char *md5,
                                              char **json_response) {
    char        response_buf[1024] = {'\0'};
    int         res = ZPATH_RESULT_NO_ERROR;
    JSON_Value  *rootValue = NULL;
    JSON_Object *rootObject = NULL;

    rootValue = json_value_init_object();
    if (!rootValue) {
        EXPORTER_LOG(AL_ERROR, "%s: Failed to init JSon obj to generate scan response", request->name);
        res = ZPATH_RESULT_ERR;
        goto do_exit;
    }

    rootObject = json_value_get_object(rootValue);
    if (!rootObject) {
        EXPORTER_LOG(AL_ERROR, "%s: Failed to get JSon obj to generate scan response", request->name);
        res = ZPATH_RESULT_ERR;
        goto do_exit;
    }

    if (json_object_set_string(rootObject, "md5", md5) != JSONSuccess) {
        EXPORTER_LOG(AL_ERROR, "%s: Failed to add md5 value to scan response", request->name);
        res = ZPATH_RESULT_ERR;
        goto do_exit;
    }

    if (json_serialize_to_buffer(rootValue, response_buf, sizeof(response_buf)) != JSONSuccess) {
        EXPORTER_LOG(AL_ERROR, "%s: Failed to prepare json response", request->name);
        res = ZPATH_RESULT_ERR;
        goto do_exit;
    }

    *json_response = EXPORTER_STRDUP(response_buf, strlen(response_buf));
    if (!*json_response) {
        res = ZPATH_RESULT_ERR;
        goto do_exit;
    }

do_exit:
    if (rootValue) {
        json_value_free(rootValue);
    }

    return res;
}

/* Generate console info response json */
int exporter_guac_generate_json_console_info_response(struct exporter_request * request,
                                                    char *msg,
                                                    size_t size_msg)
{
    int         res = ZPATH_RESULT_NO_ERROR;
    JSON_Value  *rootValue = NULL;
    JSON_Object *rootObject = NULL;
    char response_buf[EXPORTER_CONSOLE_INFO_MSG_LEN] = {'\0'};

    rootValue = json_value_init_object();
    if (!rootValue) {
        EXPORTER_LOG(AL_ERROR, "SESS_PROC %s: Failed to init Json obj to generate console info response", request->name);
        res = ZPATH_RESULT_ERR;
        goto do_exit;
    }

    rootObject = json_value_get_object(rootValue);
    if (!rootObject) {
        EXPORTER_LOG(AL_ERROR, "SESS_PROC %s: Failed to get Json obj to generate console info response", request->name);
        res = ZPATH_RESULT_ERR;
        goto do_exit;
    }

    if (request->guac_info->recording_ctxt.recording_enabled) {
        json_object_set_string(rootObject, "recording", "true");
    } else {
        json_object_set_string(rootObject, "recording", "false");
    }

    if (request->guac_info->server_layout) {
        json_object_set_string(rootObject, "keyboard_layout", keyboard_layout_names[request->guac_info->server_layout]);
    }

    if (json_serialize_to_buffer(rootValue, response_buf, sizeof(response_buf)) != JSONSuccess) {
        EXPORTER_LOG(AL_ERROR, "SESS_PROC %s: Failed to generate console info response", request->name);
        res = ZPATH_RESULT_ERR;
        goto do_exit;
    }

    snprintf(msg, size_msg, "%lu.%s,%lu.%s;",
            strlen(registered_op_codes[OP_CODE_CONSOLE_INFO]), registered_op_codes[OP_CODE_CONSOLE_INFO],
            strlen(response_buf), response_buf);
    EXPORTER_DEBUG_SESSION_SHARING("SESS_PROC %s: Console info response %s", request->name, msg);
do_exit:
    if (rootValue) {
        json_value_free(rootValue);
    }
    return res;
}

int exporter_guac_generate_json_capability_response(struct exporter_request * request,
                                                    int64_t capabilities_policy_bitmap,
                                                    int status,
                                                    char *response_buf,
                                                    size_t size_response_buf) {
    int         res = ZPATH_RESULT_NO_ERROR;
    JSON_Value  *rootValue = NULL;
    JSON_Object *rootObject = NULL;

    rootValue = json_value_init_object();
    if (!rootValue) {
        EXPORTER_LOG(AL_ERROR, "%s: Failed to init JSon obj to generate capability response", request->name);
        res = ZPATH_RESULT_ERR;
        goto do_exit;
    }

    rootObject = json_value_get_object(rootValue);
    if (!rootObject) {
        EXPORTER_LOG(AL_ERROR, "%s: Failed to get JSon obj to generate capability response", request->name);
        res = ZPATH_RESULT_ERR;
        goto do_exit;
    }

    if (status) {
        json_object_set_string(rootObject, "status", "ERROR");
    } else {
        json_object_set_string(rootObject, "status", "OK");
    }

    if (status) {
        json_object_set_string(rootObject, "message", "Policy Error");
    } else {
        if (request->guac_info->recording_ctxt.recording_enabled && !request->is_proxy_conn) {
            json_object_set_string(rootObject, "recording", "true");
        } else {
            json_object_set_string(rootObject, "recording", "false");
        }

        if (request->guac_info->is_session_share_control && !request->is_proxy_conn) {
            json_object_set_string(rootObject, "isSessionControlBitEnabled", "true");
        } else {
            json_object_set_string(rootObject, "isSessionControlBitEnabled", "false");
        }

        if (request->guac_info->is_session_share_monitor && request->is_proxy_conn) {
            json_object_set_string(rootObject, "isMonitoringMode", "true");
        } else {
            json_object_set_string(rootObject, "isMonitoringMode", "false");
        }

        if (capabilities_policy_bitmap & FILE_UPLOAD) {
            json_object_set_string(rootObject, "upload", "true");
        } else {
            json_object_set_string(rootObject, "upload", "false");
        }

        if (capabilities_policy_bitmap & FILE_DOWNLOAD) {
            json_object_set_string(rootObject, "download", "true");
        } else {
            json_object_set_string(rootObject, "download", "false");
        }

        if (capabilities_policy_bitmap & INSPECT_FILE_UPLOAD) {
            json_object_set_string(rootObject, "inspect_upload", "true");
        } else {
            json_object_set_string(rootObject, "inspect_upload", "false");
        }

        if (capabilities_policy_bitmap & INSPECT_FILE_DOWNLOAD) {
            json_object_set_string(rootObject, "inspect_download", "true");
        } else {
            json_object_set_string(rootObject, "inspect_download", "false");
        }

        if (capabilities_policy_bitmap & CLIPBOARD_COPY) {
            json_object_set_string(rootObject, "clipboard_copy", "true");
        } else {
            json_object_set_string(rootObject, "clipboard_copy", "false");
        }

        if (capabilities_policy_bitmap & CLIPBOARD_PASTE) {
            json_object_set_string(rootObject, "clipboard_paste", "true");
        } else {
            json_object_set_string(rootObject, "clipboard_paste", "false");
        }

        json_object_set_number(rootObject, "scan_max_file_size", get_max_file_size(
                request->conn->exporter_domain->customer_gid, 1));

        json_object_set_number(rootObject, "transfer_max_file_size", get_max_file_size(
                request->conn->exporter_domain->customer_gid, 0));

        json_object_set_number(rootObject, "clipboard_max_size", get_max_clipboard_size(
                request->conn->exporter_domain->customer_gid));
    }

    if (json_serialize_to_buffer(rootValue, response_buf, size_response_buf) != JSONSuccess) {
        EXPORTER_LOG(AL_ERROR, "%s: Failed to generate capability json response", request->name);
        res = ZPATH_RESULT_ERR;
        goto do_exit;
    }
do_exit:
    if (rootValue) {
        json_value_free(rootValue);
    }
    return res;
}

int exporter_guac_get_dir_nodes_count(struct exporter_request *request, const char *dir_list) {
    int count = 0;

    /* Parse current file list to get JSon root object */
    JSON_Value *json_root_val = json_parse_string(dir_list);
    if (!json_root_val) {
        EXPORTER_LOG(AL_ERROR,"%s: failed to init json object holding the directory list", request->name);
        goto do_exit;
    }

    const JSON_Object *json_root_object = json_value_get_object(json_root_val);
    if (!json_root_object) {
        EXPORTER_LOG(AL_ERROR, "%s: Failed to get JSon obj of directory list", request->name);
        goto do_exit;
    }

    count = (int) json_object_get_count(json_root_object);

do_exit:
    if (json_root_val) {
        json_value_free(json_root_val);
    }

    return count;
}

/*
 * config.feature.privileged.clipboard.globalDisable = 1 (Globally disable clipboard)
 * config.feature.privileged.clipboard.globalDisable = 0 (Default - Clipboard not globally disabled)
 */
int is_pra_clipboard_globally_disabled()
{
    int64_t config_value = 0;

    config_value = zpath_config_override_get_config_int(CLIPBOARD_FEATURE_GLOBAL_DISABLE,
                                                      &config_value,
                                                      DEFAULT_CLIPBOARD_FEATURE_GLOBAL_DISABLE,
                                                      (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                      (int64_t)0); // Argument list terminator
    return (config_value) ? 1 : 0;
}

/*
 * Clipboard is disabled by default in itasca and UI
 * is_pra_clipboard_disabled - returns 1 by default as feature is disabled by default.
 * is_pra_clipboard_disabled - returns 1 if "feature.privileged.clipboard" = "disabled" for
 * a specific customer or for all customers with global GID or global disable
 */
int is_pra_clipboard_disabled(int64_t customer_gid)
{
    char *config_value = NULL;
    int64_t root_customer_gid   = 0;

    if (!g_exporter_ot_mode) return 1;

    if (is_pra_clipboard_globally_disabled()) return 1;

    root_customer_gid = ZPATH_GID_GET_CUSTOMER_GID(zpath_instance_global_state.current_config->gid);
    config_value = zpath_config_override_get_config_str(CLIPBOARD_FEATURE,
                                                      &config_value,
                                                      DEFAULT_CLIPBOARD,
                                                      customer_gid,
                                                      root_customer_gid,
                                                      (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                      (int64_t)0); // Argument list terminator
    /* Value in config table is "disabled" for this customer */
    if (strcmp(config_value, DEFAULT_CLIPBOARD) == 0) {
        return 1;
    } else {
        return 0;
    }
}

/*
 * Gets the value of config.feature.privilegd.clipboard.maxSize config flag
 * Default - 4096
 */
int get_max_clipboard_size(int64_t customer_gid) {

    int64_t config_value = 0;

    config_value = zpath_config_override_get_config_int(CLIPBOARD_MAX_SIZE,
                                                        &config_value,
                                                        DEFAULT_CLIPBOARD_SIZE,
                                                        customer_gid,
                                                        (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                        (int64_t)0); // Argument list terminator

    return (int) config_value;
}

/*
 * Checks if clipboard characters have exceeded allowed limit
 */
int is_clipboard_max_char_exceeded(struct exporter_request *request, const char *data, size_t datalen)
{
    unsigned char *buff = NULL;

    if (!request || !data ){
        EXPORTER_LOG(AL_ERROR, "Invalid values received");
        return (-1);
    }

    buff = EXPORTER_MALLOC(BLOB_BUFFER_SIZE);
    if (!buff) {
        EXPORTER_LOG(AL_ERROR, "%s: Failed to allocate memory buff", request->name);
        return (-1);
    }

    int n_bytes = base64_decode_binary(buff, data, datalen);

    int max_clipboard_size = get_max_clipboard_size(request->conn->exporter_domain->customer_gid);

    if ((request->guac_info->clipboard_size + n_bytes) > max_clipboard_size) {
        EXPORTER_FREE(buff);
        return (1);
    }

    request->guac_info->clipboard_size += n_bytes;
    EXPORTER_FREE(buff);
    return (0);
}

int exporter_guac_send_pra_session_event(int64_t customer_id, enum zpath_customer_log_type log_type,
        struct argo_structure_description *log_desc, void *log_data)
{
    int res = zpath_customer_log_struct(customer_id, log_type, "zpn_pra_session",
                                        NULL, NULL, NULL, NULL,
                                        log_desc, log_data);
    if (res) {
        EXPORTER_LOG(AL_ERROR, "REC_UPLOAD: zpn_pra_session event log failed, res %d (%s)",
                res, zpn_result_string(res));
    } else {
        EXPORTER_DEBUG_SESSION_RECORDING("REC_UPLOAD: zpn_pra_session event log successfull");
    }
    return res;
}

int exporter_guac_send_recording_metadata(enum exporter_session_pra_kafka_event_type ev_type,
        struct exporter_session_recording_meta *metadata)
{
    char *ev_state[] = {"INITIATED", "RECORDING_COMPLETED", "TERMINATED"};
    char *rec_status[] = {"DISABLED", "STARTED", "COMPLETED", "FAILED", "AVAILABLE", "RESOURCE_UNAVAILABLE", "INVALID_CREDENTIALS", "INVALID"};
    struct exporter_guac_session_recording_metadata rpc_metadata;
    int res = ZPATH_RESULT_NO_ERROR;

    if (metadata == NULL) {
        EXPORTER_LOG(AL_ERROR, "REC_UPLOAD: arg metadata is null");
        return ZPATH_RESULT_ERR;
    }
    memset(&rpc_metadata, 0, sizeof(rpc_metadata));
    switch (ev_type) {
        case INITIATED:
            rpc_metadata.policy_rule_id = metadata->rpc.user_policy_id;
            rpc_metadata.pra_console_id = metadata->rpc.pra_console_id;
            if (metadata->key_session_rec_file) {
                rpc_metadata.s3_key = EXPORTER_STRDUP(metadata->key_session_rec_file, strlen(metadata->key_session_rec_file));
            }
            if (metadata->rpc.user_email) {
                rpc_metadata.login_username = EXPORTER_STRDUP(metadata->rpc.user_email, strlen(metadata->rpc.user_email));
            }
            if (metadata->rpc.session_proto) {
                rpc_metadata.session_proto = EXPORTER_STRDUP(metadata->rpc.session_proto, strlen(metadata->rpc.session_proto));
            }
            rpc_metadata.recording_lifetime_in_days = metadata->rpc.recording_lifetime_days;
            rpc_metadata.session_start_time = metadata->rpc.session_start_time;
            rpc_metadata.recording_enabled = 1;
            break;
        case RECORDING_COMPLETED:
            if (!metadata->start_rpc_sent) {
                EXPORTER_DEBUG_SESSION_RECORDING("REC_UPLOAD: Ignoring event RECORDING_COMPLETED for gid %"PRId64", session %s ",
                        metadata->rpc.customer_gid, metadata->rpc.session_conn_id);
                return ZPATH_RESULT_NO_ERROR;
            }
            rpc_metadata.participant_list = metadata->rpc.participant_list;
            rpc_metadata.duration_in_sec = metadata->rpc.session_duration_seconds;
            rpc_metadata.size_in_bytes = metadata->rpc.recording_size_bytes;
            rpc_metadata.recording_available_time = metadata->rpc.recording_available_time;
            break;
        case TERMINATED:
            rpc_metadata.session_end_time = metadata->rpc.session_end_time;
            break;
        default:
            EXPORTER_LOG(AL_ERROR, "ev_type %d not supported", ev_type);
            return ZPATH_RESULT_ERR;
    }

    rpc_metadata.event_type = EXPORTER_STRDUP(ev_state[ev_type], strlen(ev_state[ev_type]));
    if (metadata->rpc.session_conn_id) {
        rpc_metadata.session_id = EXPORTER_STRDUP(metadata->rpc.session_conn_id, strlen(metadata->rpc.session_conn_id));
    }
    rpc_metadata.customer_id = metadata->rpc.customer_gid;
    rpc_metadata.scope_id = (metadata->rpc.scope_gid == metadata->rpc.customer_gid) ? 0 : metadata->rpc.scope_gid;
    int i = metadata->rpc.recording_status;
    if (i < 0 || i > exporter_recording_status_recording_upload_max) {
        i = 0;
    }
    rpc_metadata.recording_status = EXPORTER_STRDUP(rec_status[i], strlen(rec_status[i]));

    /* send rpc event to kafka */
    res = exporter_guac_send_pra_session_event(rpc_metadata.customer_id, zpath_customer_log_type_zpn_pra_session,
            exporter_guac_session_recording_metadata_description, &rpc_metadata);
    if (res) {
        EXPORTER_LOG(AL_ERROR, "zpn_session_recording_metadata log failed, res %d (%s)",
                res, zpn_result_string(res));
    }

    EXPORTER_DEBUG_SESSION_RECORDING("REC_UPLOAD: zpn_session_recording_metadata for gid %"PRId64", session %s ",
        rpc_metadata.customer_id, rpc_metadata.session_id);

    /* done with the rpc, perform cleanup */
    if (rpc_metadata.s3_key) {
        EXPORTER_FREE(rpc_metadata.s3_key);
    }
    if (rpc_metadata.login_username) {
        EXPORTER_FREE(rpc_metadata.login_username);
    }
    if (rpc_metadata.event_type) {
        EXPORTER_FREE(rpc_metadata.event_type);
    }
    if (rpc_metadata.session_id) {
        EXPORTER_FREE(rpc_metadata.session_id);
    }
    if (rpc_metadata.session_proto) {
        EXPORTER_FREE(rpc_metadata.session_proto);
    }
    if (rpc_metadata.recording_status) {
        EXPORTER_FREE(rpc_metadata.recording_status);
    }

    return res;
}

int exporter_guac_send_email_event(int64_t customer_id,
                                   enum zpath_customer_log_type log_type,
                                   struct argo_structure_description *log_desc,
                                   void *log_data)
{
    char kafka_topic_name[64] = "zpn_pra_session_email";
    if (log_type == zpath_customer_log_type_zpn_email) {
        snprintf(kafka_topic_name, 64, "zpn_email");
    }

    int res = zpath_customer_log_struct(customer_id, log_type, kafka_topic_name,
                                        NULL, NULL, NULL, NULL,
                                        log_desc, log_data);
    if (res) {
        EXPORTER_LOG(AL_ERROR, "USER_EMAIL: event log failed using topic %s, res %d (%s)",
            kafka_topic_name, res, zpn_result_string(res));
    } else {
        EXPORTER_DEBUG_SESSION_SHARING("USER_EMAIL: Invitation email sent using topic %s", kafka_topic_name);
    }
    return res;
}

int exporter_guac_send_user_email_metadata(struct exporter_request *request,
                                           char *user_email,
                                           char *sessionId,
                                           char *portalUrl)
{
    struct exporter_guac_invite_user_email_metadata rpc_metadata;
    int res = ZPATH_RESULT_NO_ERROR;

    if (!request || !request->conn) {
        EXPORTER_LOG(AL_ERROR, "USER_EMAIL: Invalid request received");
        return ZPATH_RESULT_ERR;
    }

    if (!user_email || !sessionId || !portalUrl) {
        EXPORTER_LOG(AL_ERROR, "USER_EMAIL: Invalid input data received");
        return ZPATH_RESULT_ERR;
    }

    memset(&rpc_metadata, 0, sizeof(rpc_metadata));

    rpc_metadata.pra_portal_url = EXPORTER_STRDUP(portalUrl, strlen(portalUrl));
    rpc_metadata.session_id     = EXPORTER_STRDUP(sessionId, strlen(sessionId));
    rpc_metadata.to_email       = EXPORTER_STRDUP(user_email, strlen(user_email));

    char *rest = user_email;
    const char *first_name = strtok_r(rest, "@", &rest);
    if (first_name) {
        rpc_metadata.first_name = EXPORTER_STRDUP(first_name, strlen(first_name));
    } else {
        rpc_metadata.first_name = EXPORTER_STRDUP(user_email, strlen(user_email));
    }

    char portal_url_link[1024] = {0};
    snprintf(portal_url_link, 1024, "%s/v1/zconsole/$%s/index.html", portalUrl, rpc_metadata.session_id);

    rpc_metadata.customer_id = EXPORTER_CALLOC(EXPORTER_CUSTOMER_GID_LENGTH);
    snprintf(rpc_metadata.customer_id, EXPORTER_CUSTOMER_GID_LENGTH, "%"PRId64"", request->conn->exporter_domain->customer_gid);

    rpc_metadata.user_join_link = EXPORTER_STRDUP(portal_url_link, strlen(portal_url_link));
    rpc_metadata.host_email     = request->nameid;
    if (request->portal_info) {
        char *user = exporter_user_portal_request_state_get_name(request->portal_info);
        rpc_metadata.host_email     = EXPORTER_STRDUP(user, strlen(user));
    }

    /* Send rpc */
    res = exporter_guac_send_email_event(request->conn->exporter_domain->customer_gid,
            zpath_customer_log_type_zpn_email,
            exporter_guac_invite_user_email_metadata_description, &rpc_metadata);
    if (res) {
        EXPORTER_LOG(AL_ERROR, "zpn_pra_session_email_metadata log failed, res %d (%s)",
                res, zpn_result_string(res));
    }

    EXPORTER_DEBUG_SESSION_SHARING("USER_EMAIL: zpn_pra_session_email_metadata \
                                    use global-email svc, customer_id %s, session %s, \
                                    pra_portal_url %s, user_join_link %s, \
                                    Host email: %s User email: %s, first_name %s", \
                                    rpc_metadata.customer_id, rpc_metadata.session_id,
                                    rpc_metadata.pra_portal_url, rpc_metadata.user_join_link,
                                    rpc_metadata.host_email, rpc_metadata.to_email,
                                    rpc_metadata.first_name);

    /* Done with rpc*/
    if (rpc_metadata.session_id) {
        EXPORTER_FREE(rpc_metadata.session_id);
        rpc_metadata.session_id = NULL;
    }
    if (rpc_metadata.pra_portal_url) {
        EXPORTER_FREE(rpc_metadata.pra_portal_url);
        rpc_metadata.pra_portal_url = NULL;
    }
    if (rpc_metadata.user_join_link) {
        EXPORTER_FREE(rpc_metadata.user_join_link);
        rpc_metadata.user_join_link = NULL;
    }
    if (rpc_metadata.to_email) {
        EXPORTER_FREE(rpc_metadata.to_email);
        rpc_metadata.to_email = NULL;
    }
    if (request->portal_info && rpc_metadata.host_email) {
        EXPORTER_FREE(rpc_metadata.host_email);
        rpc_metadata.host_email = NULL;
    }
    if (rpc_metadata.customer_id) {
        EXPORTER_FREE(rpc_metadata.customer_id);
        rpc_metadata.customer_id = NULL;
    }
    if (rpc_metadata.first_name) {
        EXPORTER_FREE(rpc_metadata.first_name);
        rpc_metadata.first_name = NULL;
    }
    return res;
}

struct exporter_guac_json_node_list_t exporter_guac_json_node_list[] = {
    {   .id = exporter_guac_json_node_shared_user, .json_node_name = "shared_user_list"}

};

/*
 * Create a JSon Value entry for each shared user to be * added to JSon array.
 */
static JSON_Value *exporter_log_create_shared_user_entry_json(void *event_info)
{
    struct exporter_shared_session_participant_diag_event *event = (struct exporter_shared_session_participant_diag_event *) event_info;
    JSON_Value *json_log_val = json_value_init_object();
    if (!json_log_val) {
        EXPORTER_LOG(AL_ERROR,"failed to init json object for file entry");
        return NULL;
    }

    JSON_Object *json_log_obj = json_value_get_object(json_log_val);
    if (!json_log_obj) {
        EXPORTER_LOG(AL_ERROR,"failed to get json object for file entry");
        return NULL;
    }

    if (event->participant_name) {
        json_object_set_string(json_log_obj, "name", event->participant_name);
    } else {
        json_object_set_string(json_log_obj, "name", "Unavailable");
    }
    if (event->event_name) {
        json_object_set_string(json_log_obj, "event_type", event->event_name);
        if (strcmp(event->event_name, "join") == 0) {
            if (event->capability_policy_id) {
                json_object_set_string(json_log_obj, "capability_policy_id", event->capability_policy_id);
            } else {
                json_object_set_string(json_log_obj, "capability_policy_id", "");
            }
        }
    } else {
        json_object_set_string(json_log_obj, "event_type", "");
    }
    if (event->event_time) {
        json_object_set_number(json_log_obj, "event_ts", event->event_time);
    } else {
        json_object_set_number(json_log_obj, "event_ts", 0);
    }
    return json_log_val;
}

/*
 * Create a new json list for the first node.
 * To reuse the code for new feature update switch case.
 */
static int exporter_log_create_json_list(struct exporter_request *request,
                                         void *participant,
                                         char **response_buf,
                                         enum exporter_guac_json_node node)
{
    JSON_Value *json_root_val = NULL;
    JSON_Object *json_root_obj = NULL;
    int     res = ZPATH_RESULT_NO_ERROR;

    /* Init JSon response for the first entry */
    json_root_val = json_value_init_object();
    if (!json_root_val) {
        EXPORTER_LOG(AL_ERROR,"%s: failed to init json object to create %s list",
            request->name, exporter_guac_json_node_list[node].json_node_name);
        res = ZPATH_RESULT_ERR;
        goto do_exit;
    }

    json_root_obj = json_value_get_object(json_root_val);
    if (!json_root_obj) {
        EXPORTER_LOG(AL_ERROR,"%s: failed to get json object to create %s list",
            request->name, exporter_guac_json_node_list[node].json_node_name);
        res = ZPATH_RESULT_ERR;
        goto do_exit;
    }

    /* Create JSon Array for the first entry */
    JSON_Value* json_arr_val = json_value_init_array();
    if (!json_arr_val) {
        EXPORTER_LOG(AL_ERROR,"%s: failed to init json array to create %s list",
            request->name, exporter_guac_json_node_list[node].json_node_name);
        res = ZPATH_RESULT_ERR;
        goto do_exit;
    }
    JSON_Array  *json_arr = json_value_get_array(json_arr_val);
    if (!json_arr) {
        EXPORTER_LOG(AL_ERROR,"%s: failed to get json array to create %s list",
            request->name, exporter_guac_json_node_list[node].json_node_name);
        res = ZPATH_RESULT_ERR;
        goto do_exit;
    }

    /* Create JSon entry for the existing list */
    JSON_Value *json_entry_val = NULL;
    switch (node) {
        case exporter_guac_json_node_shared_user:
            json_entry_val = exporter_log_create_shared_user_entry_json(participant);
            if (!json_entry_val) {
                EXPORTER_LOG(AL_ERROR,"%s: failed to get create entry json to create %s list",
                    request->name, exporter_guac_json_node_list[node].json_node_name);
                res = ZPATH_RESULT_ERR;
                goto do_exit;
            }
            break;
        default:
                EXPORTER_LOG(AL_ERROR,"%s: Invalid json node %d to create json list", request->name, node);
    }

    /* Add entry to the JSon array */
    if (json_array_append_value(json_arr, json_entry_val) != JSONSuccess) {
        EXPORTER_LOG(AL_ERROR,"%s: failed to append entry json to %s list",
            request->name, exporter_guac_json_node_list[node].json_node_name);
        res = ZPATH_RESULT_ERR;
        goto do_exit;
    }

    if (json_object_set_value(json_root_obj, exporter_guac_json_node_list[node].json_node_name, json_arr_val) != JSONSuccess) {
        EXPORTER_LOG(AL_ERROR,"%s: failed to add json arry to %s list",
            request->name, exporter_guac_json_node_list[node].json_node_name);
        res = ZPATH_RESULT_ERR;
        goto do_exit;
    }

    size_t needed_size_in_bytes = json_serialization_size(json_root_val);
    *response_buf = EXPORTER_CALLOC(needed_size_in_bytes);
    if (!*response_buf) {
        EXPORTER_LOG(AL_ERROR,"%s: failed to allocate memory for json list", request->name);
        res = ZPATH_RESULT_NO_MEMORY;
        goto do_exit;
    }

    /* Serialise JSon into response buffer */
    if (json_serialize_to_buffer(json_root_val, *response_buf, needed_size_in_bytes) != JSONSuccess) {
        EXPORTER_LOG(AL_ERROR,"%s: failed to serialise %s list into buffer",
            request->name, exporter_guac_json_node_list[node].json_node_name);
        EXPORTER_FREE(*response_buf);
        res = ZPATH_RESULT_ERR;
        goto do_exit;
    }
do_exit:
    if (json_root_val) {
        json_value_free(json_root_val);
    }
    return res;
}

/*
 * Appends new shared user entry to existing list of shared-user-list
 */
static int exporter_log_append_json_list(struct exporter_request *request,
                                         void *data,
                                         char *node_list,
                                         char **response_buf,
                                         enum exporter_guac_json_node node)
{
    JSON_Value *json_root_val = NULL;
    JSON_Value  *json_root_val_raw_copy = NULL;
    JSON_Value *nodes_array_value_out = NULL;
    JSON_Object *json_root_obj = NULL;
    JSON_Array *nodes_array_in = NULL;
    JSON_Array *nodes_array_out = NULL;
    int     res = ZPATH_RESULT_NO_ERROR;

    /* Parse current node list to get JSon root object */
    json_root_val = json_parse_string(node_list);
    if (!json_root_val) {
        EXPORTER_LOG(AL_ERROR,"%s: failed to init json object to append to %s list",
            request->name, exporter_guac_json_node_list[node].json_node_name);
        res = ZPATH_RESULT_ERR;
        goto do_exit;
    }

    /* Deep copy existing JSon root object */
    json_root_val_raw_copy = json_value_deep_copy(json_root_val);
    if (!json_root_val_raw_copy) {
        EXPORTER_LOG(AL_ERROR,"%s: failed to copy existing %s list",
            request->name, exporter_guac_json_node_list[node].json_node_name);
        res = ZPATH_RESULT_ERR;
        goto do_exit;
    }

    json_root_obj = json_value_get_object(json_root_val_raw_copy);
    if (!json_root_obj) {
        EXPORTER_LOG(AL_ERROR,"%s: failed to get json object to append to %s list",
            request->name, exporter_guac_json_node_list[node].json_node_name);
        res = ZPATH_RESULT_ERR;
        goto do_exit;
    }

    /* Get existing JSon array */
    nodes_array_in = json_object_get_array(json_root_obj, exporter_guac_json_node_list[node].json_node_name);
    if (!nodes_array_in) {
        EXPORTER_LOG(AL_ERROR,"%s: failed to get json arr to append to %s list",
            request->name, exporter_guac_json_node_list[node].json_node_name);
        res = ZPATH_RESULT_ERR;
        goto do_exit;
    }

    int nodes_count = json_array_get_count(nodes_array_in);

    /* Build new nodes */
    nodes_array_value_out = json_value_init_array();
    if (!nodes_array_value_out) {
        EXPORTER_LOG(AL_ERROR,"%s: failed to init new json arr to append to %s list",
            request->name, exporter_guac_json_node_list[node].json_node_name);
        res = ZPATH_RESULT_ERR;
        goto do_exit;
    }
    nodes_array_out = json_value_get_array(nodes_array_value_out);
    if (!nodes_array_value_out) {
        EXPORTER_LOG(AL_ERROR,"%s: failed to get json object from new arr", request->name);
        res = ZPATH_RESULT_ERR;
        goto do_exit;
    }

    /* Deep copy each node from nodes_array_in to nodes_array_out */
    for (int i = 0; i < nodes_count; i++) {
        JSON_Value *in_value = json_array_get_value(nodes_array_in, i);
        if (json_array_append_value(nodes_array_out, json_value_deep_copy(in_value)) != JSONSuccess) {
            EXPORTER_LOG(AL_ERROR,"%s: failed to copy existing json arr to new json arr for %s list",
                request->name, exporter_guac_json_node_list[node].json_node_name);
            res = ZPATH_RESULT_ERR;
            goto do_exit;
        }
    }

    /* Create JSon entry for the current node-list */
    JSON_Value *json_entry_val = NULL;
    switch (node) {
        case exporter_guac_json_node_shared_user:
            json_entry_val = exporter_log_create_shared_user_entry_json(data);
            if (!json_entry_val) {
                EXPORTER_LOG(AL_ERROR,"%s: failed to get create entry json to create %s list",
                    request->name, exporter_guac_json_node_list[node].json_node_name);
                res = ZPATH_RESULT_ERR;
                goto do_exit;
            }
            break;
        default:
            EXPORTER_LOG(AL_ERROR,"%s: Invalid json node %d to append json list", request->name, node);

    }

    /* Append to the new array */
    if (json_array_append_value(nodes_array_out, json_entry_val) != JSONSuccess) {
        EXPORTER_LOG(AL_ERROR,"%s: failed to append entry json to %s list",
            request->name, exporter_guac_json_node_list[node].json_node_name);
        res = ZPATH_RESULT_ERR;
        goto do_exit;
    }

    if (json_object_set_value(json_root_obj, exporter_guac_json_node_list[node].json_node_name, nodes_array_value_out) != JSONSuccess) {
        EXPORTER_LOG(AL_ERROR,"%s: failed to add json arry to %s list",
            request->name, exporter_guac_json_node_list[node].json_node_name);
        res = ZPATH_RESULT_ERR;
        goto do_exit;
    }

    size_t needed_size_in_bytes = json_serialization_size(json_root_val_raw_copy);
    *response_buf = EXPORTER_CALLOC(needed_size_in_bytes);
    if (!*response_buf) {
        EXPORTER_LOG(AL_ERROR,"%s: failed to allocate memory in append %s list",
            request->name, exporter_guac_json_node_list[node].json_node_name);
        res = ZPATH_RESULT_NO_MEMORY;
        goto do_exit;
    }

    /* Serialise the latest JSon array */
    if (json_serialize_to_buffer(json_root_val_raw_copy, *response_buf, needed_size_in_bytes) != JSONSuccess) {
        EXPORTER_LOG(AL_ERROR,"%s: failed to serialise %s list into buffer",
            request->name, exporter_guac_json_node_list[node].json_node_name);
        EXPORTER_FREE(*response_buf);
        res = ZPATH_RESULT_ERR;
        goto do_exit;
    }
do_exit:
    if (json_root_val_raw_copy) {
        json_value_free(json_root_val_raw_copy);
        json_root_val_raw_copy = NULL;
    }
    if (json_root_val) {
        json_value_free(json_root_val);
        json_root_val = NULL;
    }
    return res;
}

int exporter_log_update_json_list(struct exporter_request *request,
                                  void *participant,
                                  enum exporter_guac_json_node node)
{
    int     res = ZPATH_RESULT_NO_ERROR;
    char    *new_list = NULL;
    char    *cur_list = NULL;

    if (!request || !participant) {
         return res;
    }
    if (request->is_proxy_conn) {
        return res;
    }
    if (node >= exporter_guac_json_node_max) {
        return res;
    }

    if (request->share_ctxt.diag_entry_count >= EXPORTER_PRA_SESS_DIAG_ENTRY_MAX_CNT) {
        EXPORTER_LOG(AL_NOTICE, "%s: Failed to update json list. Max limit reached %u",
            request->name, request->share_ctxt.diag_entry_count);
        return ZPATH_RESULT_ERR;
    }

    ZPATH_MUTEX_LOCK(&(request->guac_info->shared_user_list_lock), __FILE__, __LINE__);

    /* final data will be updated here */
    if (node == exporter_guac_json_node_shared_user) {
        cur_list = request->guac_info->shared_user_list;
    }

    if (!cur_list) {
        /* Create a new list */
        res = exporter_log_create_json_list(request, participant, &new_list, node);
        if (res != ZPATH_RESULT_NO_ERROR) {
            EXPORTER_LOG(AL_ERROR, "%s: failed to create json list ", request->name);
            ZPATH_MUTEX_UNLOCK(&(request->guac_info->shared_user_list_lock), __FILE__, __LINE__);
            return ZPATH_RESULT_ERR;
        }
    } else {
        /* Append is required only on primary exporter */
        res = exporter_log_append_json_list(request, participant, cur_list, &new_list, node);
        if (res != ZPATH_RESULT_NO_ERROR) {
            EXPORTER_LOG(AL_ERROR, "%s: failed to append json list ", request->name);
            ZPATH_MUTEX_UNLOCK(&(request->guac_info->shared_user_list_lock), __FILE__, __LINE__);
            return ZPATH_RESULT_ERR;
        }

        /* refresh the list, free the old and start using new */
        if (cur_list) {
            EXPORTER_FREE(cur_list);
        }
    }

    if (node == exporter_guac_json_node_shared_user) {
        request->guac_info->shared_user_list = new_list;
        __sync_fetch_and_add_4(&request->share_ctxt.diag_entry_count, 1);
        EXPORTER_DEBUG_SESSION_SHARING("%s: [SESS_PROC]: updated shared_user json_list %s", request->name, new_list);
    }

    ZPATH_MUTEX_UNLOCK(&(request->guac_info->shared_user_list_lock), __FILE__, __LINE__);

    if (!new_list) {
        return ZPATH_RESULT_ERR;
    }
    return ZPATH_RESULT_NO_ERROR;
}

struct exporter_request *lookup_orig_guac_request(struct exporter_request *request)
{
    struct exporter_request *orig_request = NULL;

    ZPATH_RWLOCK_WRLOCK(&(global_exporter.lock), __FILE__, __LINE__);
    orig_request = zhash_table_lookup(global_exporter.proxy_conn_id, request->guac_proctored_session_id,
                                      strnlen(request->guac_proctored_session_id, GUAC_PROCTORED_SESS_ID_LEN), NULL);
    ZPATH_RWLOCK_UNLOCK(&(global_exporter.lock), __FILE__, __LINE__);
    if (!orig_request) {
        EXPORTER_LOG(AL_WARNING, "SESS_PROC Could not get request for conn id %s", request->guac_proctored_session_id);
        return NULL;
    }
    return orig_request;
}

int exporter_conn_guac_parse_json_session_control_args(const char *session_control_buffer,
                                                  char **user_email,
                                                  char **type,
                                                  char *error_reason)
{
    JSON_Object     *jo = NULL;
    JSON_Value      *jv = NULL;

    if (!session_control_buffer) {
        EXPORTER_LOG(AL_ERROR, "No payload");
        snprintf(error_reason, EXPORTER_USER_PORTAL_ERROR_REASON_LEN, "No payload");
        return ZPATH_RESULT_ERR;
    }

    jv = json_parse_string(session_control_buffer);
    if (json_value_get_type(jv) != JSONObject) {
        EXPORTER_LOG(AL_ERROR, "Failed to validate session_control_buffer JSON");
        snprintf(error_reason, EXPORTER_USER_PORTAL_ERROR_REASON_LEN, "Failed to validate session_control_buffer JSON");
        if (jv) {
            json_value_free(jv);
        }
        return ZPATH_RESULT_ERR;
    }
    jo = json_value_get_object(jv);
    if (!jo) {
        EXPORTER_LOG(AL_ERROR, "Failed to validate session_control_buffer JSON");
        snprintf(error_reason, EXPORTER_USER_PORTAL_ERROR_REASON_LEN, "Failed to validate session_control_buffer JSON");
        json_value_free(jv);
        return ZPATH_RESULT_ERR;
    }

    const char *type_ptr = json_object_get_string (jo, "type");
    const char *user_email_ptr = json_object_get_string (jo, "user_email");
    if (!user_email_ptr || !strlen(user_email_ptr) ||
        !type_ptr || !strlen(type_ptr)) {
        /* Both username and type is mandatory */
        EXPORTER_LOG(AL_ERROR, "Failed to get mandatory session_control_buffer parameters");
        snprintf(error_reason, EXPORTER_USER_PORTAL_ERROR_REASON_LEN, "Failed to get mandatory session_control_buffer parameters");
        json_value_free(jv);
        return ZPATH_RESULT_ERR;
    }
    *user_email = EXPORTER_STRDUP(user_email_ptr, strlen(user_email_ptr));
    *type = EXPORTER_STRDUP(type_ptr, strlen(type_ptr));
    if (!(*user_email) || !(*type)) {
        EXPORTER_LOG(AL_ERROR, "Failed to allocate memory");
        snprintf(error_reason, EXPORTER_USER_PORTAL_ERROR_REASON_LEN, "Failed to allocate memory");
        json_value_free(jv);
        if (*user_email) {
            memset(*user_email,'\0', strlen(*user_email));
            EXPORTER_FREE(*user_email);
        }
        if (*type) {
            memset(*type,'\0', strlen(*type));
            EXPORTER_FREE(*type);
        }
        return ZPATH_RESULT_ERR;
    }

    json_value_free(jv);
    return ZPATH_RESULT_NO_ERROR;
}

int exporter_guac_generate_json_session_control_response(struct exporter_request *request,
                                                    char *type,
                                                    char *user_email,
                                                    char *error_reason,
                                                    int status,
                                                    char *response_buf,
                                                    size_t size_response_buf)
{
    int res = ZPATH_RESULT_NO_ERROR;
    JSON_Value  *rootValue = NULL;
    JSON_Object *rootObject = NULL;

    rootValue = json_value_init_object();
    if (!rootValue) {
        EXPORTER_LOG(AL_ERROR, "%s: Failed to init JSon obj to generate session control response", request->name);
        res = ZPATH_RESULT_ERR;
        goto do_exit;
    }

    rootObject = json_value_get_object(rootValue);
    if (!rootObject) {
        EXPORTER_LOG(AL_ERROR, "%s: Failed to get JSon obj to generate session control response", request->name);
        res = ZPATH_RESULT_ERR;
        goto do_exit;
    }

    if (type) {
        json_object_set_string(rootObject, "type", type);
    }

    if (user_email) {
        json_object_set_string(rootObject, "user_email", user_email);
    }

    if (status) {
        json_object_set_string(rootObject, "status", "error");
    } else {
        json_object_set_string(rootObject, "status", "success");
    }

    if (status) {
        json_object_set_string(rootObject, "reason", error_reason);
    }

    if (json_serialize_to_buffer(rootValue, response_buf, size_response_buf) != JSONSuccess) {
        EXPORTER_LOG(AL_ERROR, "%s: Failed to generate session control json response", request->name);
        res = ZPATH_RESULT_ERR;
        goto do_exit;
    }
do_exit:
    if (rootValue) {
        json_value_free(rootValue);
    }
    return res;
}


/*
 * Return 0, if my-files is globally enabled, globalDisable = 0
 * config.feature.privileged.myfiles.globalDisable = 0 (Default)
 * config.feature.privileged.myfiles.globalDisable = 1 (Globally disable)
 */
int
is_pra_advanced_file_transfer_globally_disabled()
{
    int64_t config_value = 0;

    if (!g_exporter_ot_mode) {
        return 1;
    }

    config_value = zpath_config_override_get_config_int(ADVANCED_FILE_TRANSFER_FEATURE_GLOBAL_DISABLE ,
                                                        &config_value,
                                                        DEFAULT_ADVANCED_FILE_TRANSFER_FEATURE_GLOBAL_DISABLE,
                                                        (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                        (int64_t)0); // Argument list terminator
    return config_value ? 1 : 0;
}


#ifndef UNIT_TEST
/*
 * For a customer, advanced file transfer is disabled by default
 * return 1 as feature is disabled by default.
 * return 1 if "feature.privileged.myfiles" is "disabled" for a
 *   specific customer or for all customers with global GID or global disable
 * return 0 if the "feature.privileged.myfiles" is "enabled" for a
 *   specific customer and globally enabled
 */
int
is_pra_advanced_file_transfer_disabled(int64_t customer_gid)
{
    char *config_value = NULL;
    int64_t root_customer_gid = 0;

    if (is_pra_advanced_file_transfer_globally_disabled()) {
        return 1;
    }

    root_customer_gid = ZPATH_GID_GET_CUSTOMER_GID(zpath_instance_global_state.current_config->gid);

    /* check whether the value in config table for this customer is "disabled" */
    config_value = zpath_config_override_get_config_str(PRA_ADVANCED_FILE_TRANSFER,
                                                        &config_value,
                                                        DEFAULT_ADVANCED_FILE_TRANSFER,
                                                        customer_gid,
                                                        root_customer_gid,
                                                        (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                        (int64_t)0); // Argument list terminator
    if (strcmp(config_value, DEFAULT_ADVANCED_FILE_TRANSFER) == 0) {
        return 1; /* my-files is disabled for the customer */
    } else {
        return 0; /* my-files is enabled for the customer */
    }
}
#endif

int64_t
exporter_portal_file_transfer_enabled(int64_t customer_gid)
{
    int64_t config_value = 0;

    if (is_pra_advanced_file_transfer_disabled(customer_gid)) {
        return 0;
    }
    int64_t root_customer_gid = ZPATH_GID_GET_CUSTOMER_GID(zpath_instance_global_state.current_config->gid);
    config_value = zpath_config_override_get_config_int(PRA_PORTAL_FILE_TRANSFER,
                                                        &config_value,
                                                        DEFAULT_PRA_PORTAL_FILE_TRANSFER,
                                                        customer_gid,
                                                        root_customer_gid,
                                                        (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                        (int64_t)0); // Argument list terminator
    /* Value in config table is 0 for disabled at root level */
    return config_value;
}

/* Function to add more fileds to upload-init request body */
int exporter_multipart_upload_request_append_fields(struct exporter_request *request)
{
    enum zpa_user_portal_api_type api_type = exporter_user_portal_request_state_get_api_type(request->portal_info);
    JSON_Value *json_root_val = NULL;
    JSON_Object *json_root_obj = NULL;
    unsigned char *data = NULL;
    char *out_buf = NULL;
    int res = ZPATH_RESULT_NO_ERROR;
    struct zpn_rule *privileged_portal_rule  = NULL;
    struct zpn_sra_portal *sra_portal = NULL;
    size_t rule_count = 1;
    char *domain = EXPORTER_GET_REQUEST_DOMAIN(request);

    if (request->request_body == NULL) {
        EXPORTER_LOG(AL_ERROR, "%s: My-files multipart upload request with NULL request_body", request->name);
        return ZPATH_RESULT_ERR;
    }

    size_t datalen = evbuffer_get_length(request->request_body);
    data = evbuffer_pullup(request->request_body, datalen);
    if (data == NULL) {
        EXPORTER_LOG(AL_ERROR, "%s: My-files multipart upload request failed pullup request_body", request->name);
        return ZPATH_RESULT_ERR;
    }

    // JSON parse of the request body
    json_root_val = json_parse_string((const char *)data);
    if (!json_root_val) {
        EXPORTER_LOG(AL_ERROR, "%s: JSON string parse failed for request_body", request->name);
        return ZPATH_RESULT_ERR;
    }

    json_root_obj = json_value_get_object(json_root_val);
    if (!json_root_obj) {
        EXPORTER_LOG(AL_ERROR, "%s: JSON get object failed for request_body", request->name);
        json_value_free(json_root_val);
        return ZPATH_RESULT_ERR;
    }

    if (api_type == ZPA_SRA_PORTAL_API_TYPE_MY_FILES_UPLOAD_INTIATE) {
        // Add client remote IP
        char remote_ip_str[ARGO_INET_ADDRSTRLEN] = {0};
        argo_inet_generate(remote_ip_str, &(request->conn->remote_ip));
        json_object_set_string(json_root_obj, "ipAddress", remote_ip_str);

        // Add country code
        json_object_set_string(json_root_obj, "countryCode", request->c_cc);

        // Fetch zpn_sra_portal
        res = zpn_sra_portal_get_portal_by_domain_immediate(request->conn->exporter_domain->customer_gid, domain, &sra_portal);
        if ((res != ZPATH_RESULT_NO_ERROR) || (!sra_portal)) {
            EXPORTER_LOG(AL_ERROR, "%s: Error %s retrieving portal for domain %s", request->name, zpath_result_string(res), domain);
            return res;
        }

        //Add Portal Name
        json_object_set_string(json_root_obj, "portalName", sra_portal->name);

        //Scope
        if (sra_portal->scope_gid == sra_portal->customer_gid) {
            json_object_set_string(json_root_obj, "scopeId", "0");
        } else {
            char portal_scope_gid[EXPORTER_USER_PORTAL_ID_STR_LEN] = {'\0'};
            snprintf(portal_scope_gid, sizeof(portal_scope_gid), "%"PRId64, sra_portal->scope_gid);
            json_object_set_string(json_root_obj, "scopeId", portal_scope_gid);
        }

        // Add portal policy rule GID
        char portal_polic_rule_gid[EXPORTER_USER_PORTAL_ID_STR_LEN] = {'\0'};
        snprintf(portal_polic_rule_gid, sizeof(portal_polic_rule_gid), "%"PRId64, request->portal_policy_rule_id);
        json_object_set_string(json_root_obj, "portalPolicyGID", portal_polic_rule_gid);

        // Add portal policy name
        res = zpn_rule_get_by_gid(request->portal_policy_rule_id, &privileged_portal_rule, &rule_count);
        if (res) {
            EXPORTER_LOG(AL_ERROR, "%s: Failed to fetch zpn_rule %"PRId64, request->name, request->portal_policy_rule_id);
        }
        json_object_set_string(json_root_obj, "policyName", privileged_portal_rule->name);

    }
    // Add inspection type
    if (request->portal_policy_capabilities_bitmap & PRIV_PORTAL_UPLOAD_INSPECTED_SANDBOX) {
        json_object_set_string(json_root_obj, "inspectionType", "SANDBOX");
    } else if (request->portal_policy_capabilities_bitmap & PRIV_PORTAL_UPLOAD_INSPECTED_DISCAN) {
        json_object_set_string(json_root_obj, "inspectionType", "DISCAN");
    } else {
        json_object_set_string(json_root_obj, "inspectionType", "NONE");
    }

    size_t needed_size_in_bytes = json_serialization_size(json_root_val);
    out_buf = EXPORTER_CALLOC(needed_size_in_bytes);
    if (!out_buf) {
        EXPORTER_LOG(AL_ERROR,"%s: failed to allocate memory", request->name);
        json_value_free(json_root_val);
        return ZPATH_RESULT_NO_MEMORY;
    }

    /* Serialise JSon into buffer */
    if (json_serialize_to_buffer(json_root_val, out_buf , needed_size_in_bytes) != JSONSuccess) {
        EXPORTER_LOG(AL_ERROR,"%s: failed to serialise json into buffer", request->name);
        json_value_free(json_root_val);
        EXPORTER_FREE(out_buf);
        return ZPATH_RESULT_ERR;
    }

    // Drain current body and replace it with prepared body
    evbuffer_drain(request->request_body, datalen);

    evbuffer_add_printf(request->request_body, "%s", out_buf);

    if (out_buf) {
        EXPORTER_FREE(out_buf);
    }
    json_value_free(json_root_val);
    return ZPATH_RESULT_NO_ERROR;
}

/* Function to generate SHA256 hash */
int exporter_get_sha256_hash(const char* input, size_t input_len, unsigned char hash[], size_t *hash_length)
{
    uint8_t sha[SHA256_DIGEST_LENGTH];
    EVP_MD_CTX *ctx;

    if (!(ctx = EVP_MD_CTX_create())) {
        EXPORTER_LOG(AL_ERROR, "Could not SHA create");
        return ZPATH_RESULT_ERR;
    }

    if (1 != EVP_DigestInit_ex(ctx, EVP_sha256(), NULL)) {
        EVP_MD_CTX_destroy(ctx);
        EXPORTER_LOG(AL_ERROR, "Could not SHA init");
        return ZPATH_RESULT_ERR;
    }

    if (1 != EVP_DigestUpdate(ctx, input, input_len)) {
        EVP_MD_CTX_destroy(ctx);
        EXPORTER_LOG(AL_ERROR, "Could not SHA update");
        return ZPATH_RESULT_ERR;
    }

    unsigned int shalen = sizeof(sha);
    if (1 != EVP_DigestFinal_ex(ctx, sha, &shalen)) {
        EXPORTER_LOG(AL_ERROR, "Could not SHA final");
        EVP_MD_CTX_destroy(ctx);
        return ZPATH_RESULT_ERR;
    }
    EVP_MD_CTX_destroy(ctx);

    if (shalen != sizeof(sha)) {
        EXPORTER_LOG(AL_ERROR, "Bad SHA length");
        return ZPATH_RESULT_ERR;
    }

    if (*hash_length < shalen) {
        EXPORTER_LOG(AL_ERROR, "Buffer too small. cannot write hash");
        return ZPATH_RESULT_ERR;
    }

    *hash_length = shalen;
    memcpy(hash, sha, shalen);

    return ZPATH_RESULT_NO_ERROR;
}

/* Calculate SHA256 of the content */
int exporter_get_sha256_hash_hex_str(const char *input, size_t input_len, char *hash_hex_str, size_t hash_hex_len)
{
    unsigned char hash[EXPORTER_SHA256_HASH_SIZE];  // Generating a 256 bit hash
    int i;
    int res;

    if (input_len <= 0 || (hash_hex_len < EXPORTER_SHA256_HASH_SIZE)) {
        return ZPATH_RESULT_ERR;
    }
    /* Hash of the content in a form of hex string */
    size_t hash_len = sizeof(hash);
    res = exporter_get_sha256_hash(input, input_len, hash, &hash_len);
    if (res != ZPATH_RESULT_NO_ERROR || hash_len != EXPORTER_SHA256_HASH_SIZE) {
        EXPORTER_LOG(AL_ERROR,"Failed to generate SHA256 hash");
        return res;
    }

    /* Convert hash byte array to hex string */
    char *curr = hash_hex_str;
    for (i = 0; i < hash_len; i++) {
        curr += sprintf(curr, "%02x", hash[i]);
    }
    return ZPATH_RESULT_NO_ERROR;
}


/*
 * config.feature.credentials_global_disable = 1 (Globally disable Privileged Policy)
 * config.feature.credentials_global_disable = 0 (Default - Privileged Policy globally not disabled)
 */
int is_privileged_policy_globally_disabled()
{
    int64_t config_value = 0;

    config_value = zpath_config_override_get_config_int(CREDENTIALS_FEATURE_GLOBAL_DISABLE,
                                                      &config_value,
                                                      DEFAULT_CREDENTIALS_FEATURE_GLOBAL_DISABLE,
                                                      (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                      (int64_t)0); // Argument list terminator
    return (config_value) ? 1 : 0;
}

/*
 * Privileged Policy is disabled by default in itasca
 * is_privileged_policy_disabled - returns 0 if "feature.privileged.credentials" == "enabled" for a
 specific customer or for all customers and globally enabled.
 * is_privileged_policy_disabled - returns 1
 * 1. PRA is disabled for this customer or globally
 * 2. Feature is disabled globally
 * 3. if "feature.privileged.credentials" = "disabled" for a specific customer or for all customers with global GID
 */
int is_privileged_policy_disabled(int64_t customer_gid)
{
    char *config_value = NULL;
    int64_t root_customer_gid = ZPATH_GID_GET_CUSTOMER_GID(zpath_instance_global_state.current_config->gid);

    if (is_pra_disabled(customer_gid)) return 1;

    if (is_privileged_policy_globally_disabled()) return 1;

    config_value = zpath_config_override_get_config_str(PRIVILEGED_CREDENTIALS_FEATURE,
                                                      &config_value,
                                                      DEFAULT_PRIVILEGED_CREDENTIALS_FEATURE,
                                                      customer_gid,
                                                      root_customer_gid,
                                                      (int64_t)0); // Argument list terminator
    /* Value in config table is "disabled" for this customer */
    if (strcmp(config_value, DEFAULT_PRIVILEGED_CREDENTIALS_FEATURE) == 0) {
        return 1;
    } else {
        return 0;
    }
}

int get_pra_credential_pool_api_retry_limit()
{
    int64_t config_value = 0;

    config_value = zpath_config_override_get_config_int(PRA_CREDENTIAL_POOL_API_RETRY_LIMIT,
                                                      &config_value,
                                                      DEFAULT_PRA_CREDENTIAL_POOL_API_RETRY_LIMIT,
                                                      zpath_instance_global_state.current_config->gid,
                                                      (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                      (int64_t)0); // Argument list terminator
    return (int)config_value;
}

int is_privileged_credential_pool_globally_disabled()
{
    int64_t config_value = 0;

    config_value = zpath_config_override_get_config_int(CREDENTIAL_POOL_FEATURE_GLOBAL_DISABLE,
                                                      &config_value,
                                                      DEFAULT_CREDENTIAL_POOL_FEATURE_GLOBAL_DISABLE,
                                                      (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                      (int64_t)0); // Argument list terminator
    return (config_value) ? 1 : 0;
}

int is_privileged_credential_pool_disabled(int64_t customer_gid)
{
    char *config_value = NULL;
    int64_t root_customer_gid = ZPATH_GID_GET_CUSTOMER_GID(zpath_instance_global_state.current_config->gid);

    if (is_pra_disabled(customer_gid)) return 1;

    if (is_privileged_credential_pool_globally_disabled()) return 1;

    config_value = zpath_config_override_get_config_str(PRIVILEGED_CREDENTIAL_POOL_FEATURE,
                                                      &config_value,
                                                      DEFAULT_PRIVILEGED_CREDENTIAL_POOL_FEATURE,
                                                      customer_gid,
                                                      root_customer_gid,
                                                      (int64_t)0); // Argument list terminator
    /* Value in config table is "disabled" for this customer */
    if (strcmp(config_value, DEFAULT_PRIVILEGED_CREDENTIAL_POOL_FEATURE) == 0) {
        return 1;
    } else {
        return 0;
    }
}

int dump_json_data(struct evbuffer *body, char *buf, size_t buf_len)
{
    size_t len;
    char tmp_buf[1000] = {0};
    JSON_Value *json;

    if (!body) {
        return ZPATH_RESULT_BAD_ARGUMENT;
    }
    len = evbuffer_get_length(body);
    if (len > (sizeof(tmp_buf) - 1)) {
        ZPATH_LOG(AL_ERROR, "Unexpectedly long body!");
        return ZPATH_RESULT_ERR_TOO_LARGE;
    }
    evbuffer_copyout(body, tmp_buf, len);
    tmp_buf[len] = 0;
    EXPORTER_DEBUG_USER_PORTAL_API("Read data: (%d) %s\n", (int)len, tmp_buf);
    json = json_parse_string(tmp_buf);
    if (!json) {
        ZPATH_LOG(AL_ERROR, "Could not parse JSON: <%s>", tmp_buf);
        return ZPATH_RESULT_BAD_DATA;
    }

    if (json_serialize_to_buffer_pretty(json, buf, buf_len) == JSONSuccess) {
        json_value_free(json);
        return ZPATH_RESULT_NO_ERROR;
    }

    json_value_free(json);
    return ZPATH_RESULT_ERR;
}
