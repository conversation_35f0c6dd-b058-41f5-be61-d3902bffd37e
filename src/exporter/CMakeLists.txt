argo_parse_files(
    INPUT_FILES
        exporter_fohh_worker.h
        exporter_request_policy_state.h
        exporter_user_portal_cfg_portal.h
        exporter_user_portal_cfg_links.h
        exporter_user_portal_cfg_zapp_links.h
        exporter_user_portal_cfg_aup.h
        exporter_user_portal_api.h
        exporter_user_portal_cfg_link_mapping.h
        zpn_sra_application.h
        zpn_sra_console.h
        zpn_sra_portal.h
        zpn_sra_portal_sra_console_mapping.h
        exporter_session.c
        exporter_context.h
        exporter_request_policy_state.c
        exporter_user_portal_cfg_portal.c
        exporter_user_portal.c
        exporter_user_portal_cfg_links.c
        exporter_user_portal_cfg_link_mapping.c
        exporter_user_portal_cfg_zapp_links.c
        exporter_user_portal_cfg_aup.c
        exporter_user_portal_api.c
        exporter_user_portal_request.c
        exporter_user_portal_request_state.c
        exporter_user_portal_conn_webserver.c
        exporter_guac_request.h
        exporter_guac_api.h
        exporter_recording_pool.h
        exporter_guac_request.c
        exporter_guac_api.c
        exporter_recording_pool.c
        exporter_guac_proxy.h
        exporter_guac_api_zia.h
        exporter_guac_util.h
        exporter_guac_proxy.c
        exporter_guac_api_zia.c
        exporter_guac_util.c
        zpn_sra_portal_sra_console_mapping.c
        exporter_privileged_policy.c
        exporter_csp.h
        exporter_private.h
        guacd_perf_limits.h
        guacd_perf_limits.c
        exporter_guac_sess_sharing.h
        exporter_cred_thread.h
    OUTPUT_DIR ${CMAKE_CURRENT_BINARY_DIR}
    OUTPUT_FILES_VAR generated_headers
)

xxd_files(
    FILES
        web/dist/exporter_guac_index.html
        web/dist/exporter_guac_all_min.js
        web/dist/exporter_guac_pra_console.js
        web/dist/exporter_guac_pra_console.css
        exporter_csp.html
        exporter_csp-g.html
        exporter_csp-e.html
        exporter_csp-ge.html
        exporter_csp.js
        exporter_idp_query.html
        exporter_idp_query.js
        exporter_guac_pra_console.woff2
        Inter-Regular.woff2
        Inter-Regular.woff
        fa-solid-900.woff2
        fa-solid-900.ttf
    OUTPUT_DIR ${CMAKE_CURRENT_BINARY_DIR}
    OUTPUT_FILES_VAR generated_headers
)
xxd_files(RAW FILES exporter_eun.html OUTPUT_DIR ${CMAKE_CURRENT_BINARY_DIR} OUTPUT_FILES_VAR generated_headers)

add_library(
    exporter_lib
    STATIC
    exporter_conn.c
    exporter_cors.c
    exporter_cbi.c
    exporter_domain.c
    exporter_fohh_worker.c
    exporter_lib.c
    exporter_request.c
    exporter_request_policy_state.c
    exporter_request_util.c
    exporter_secret.c
    exporter_context.c
    exporter_session.c
    exporter_siem.c
    exporter_user_portal.c
    exporter_user_portal_api.c
    exporter_user_portal_cfg_aup.c
    exporter_user_portal_cfg_link_mapping.c
    exporter_user_portal_cfg_links.c
    exporter_user_portal_cfg_portal.c
    exporter_user_portal_cfg_zapp_links.c
    exporter_user_portal_conn_webserver.c
    exporter_user_portal_request.c
    exporter_user_portal_request_state.c
    exporter_guac_request.c
    exporter_guac_api.c
    exporter_guac_api_zia.c
    exporter_guac_util.c
    exporter_guac_parser.c
    exporter_guac_proxy.c
    exporter_guacd.c
    exporter_websocket_parser.c
    exporter_util.c
    exporter_zcdns.c
    exporter_zpa.c
    exporter_fohh_client_util.c
    exporter_privileged_policy.c
    zpn_sra_application.c
    zpn_sra_console.c
    zpn_sra_portal.c
    zpn_sra_portal_sra_console_mapping.c
    exporter_csp.c
    guacd_perf_limits.c
    exporter_recording.c
    exporter_recording_upload.c
    exporter_recording_pool.c
    exporter_recording_multipart.c
    exporter_overrides.c
    exporter_guac_sess_sharing.c
    exporter_guac_session_control_host.c
    exporter_http_proxy_session_store.c
    exporter_privileged_file_system.c
    exporter_guac_cred_pool.c
    exporter_cred_thread.c
    exporter_guac_desktops.c
    ${generated_headers}
)
target_link_libraries(exporter_lib PUBLIC zpn zpn_ot)

add_simple_apps(
    SOURCES
        exporter.c
        exporter_test.c
        exporter_csp_test.c
        exporter_cookie_test.c
        exporter_multipart_upload_test.c
        exporter_sess_sharing_test.c
        exporter_request_destroy_test.c
    DEPS exporter_lib zpath_app
)

add_executable(exporter_guac_desktops_test exporter_guac_desktops_test.c exporter_guac_desktops.c)
target_compile_definitions(exporter_guac_desktops_test PRIVATE UNIT_TEST)
target_link_libraries(exporter_guac_desktops_test PRIVATE exporter_lib zpath_app)

add_executable(exporter_guac_cred_pool_test exporter_guac_cred_pool_test.c exporter_guac_cred_pool.c)
target_compile_definitions(exporter_guac_cred_pool_test PRIVATE TEST_CRED_POOL)
target_link_libraries(exporter_guac_cred_pool_test PRIVATE exporter_lib zpath_app)

add_executable(guac_parser_test exporter_guac_parser_test.c exporter_guac_parser.c)
target_compile_definitions(guac_parser_test PRIVATE GUAC_PARSER_UNIT_TEST)
target_link_libraries(guac_parser_test PRIVATE exporter_lib zpath_app)

add_executable(exporter_session exporter_session.c)
target_compile_definitions(exporter_session PRIVATE TEST_BUILD)
target_link_libraries(exporter_session PRIVATE object_store exporter_lib zpath_app)

add_executable(
    exporter_myfiles_test
    exporter_myfiles_test.c
    exporter_guac_util.c
    exporter_guac_proxy.c
    exporter_privileged_file_system.c
    exporter_guac_api.c
    exporter_request_policy_state.c
)
target_compile_definitions(exporter_myfiles_test PRIVATE UNIT_TEST)
target_link_libraries(exporter_myfiles_test PRIVATE exporter_lib zpath_app)

add_executable(
    exporter_enduser_approvals_test
    exporter_enduser_approvals_test.c
    exporter_user_portal_api.c
    exporter_user_portal_conn_webserver.c
)
target_compile_definitions(exporter_enduser_approvals_test PRIVATE UNIT_TEST)
target_link_libraries(exporter_enduser_approvals_test PRIVATE exporter_lib zpath_app)

add_simple_tests(
    exporter_test
    exporter_csp_test
    exporter_cookie_test
    exporter_multipart_upload_test
    exporter_myfiles_test
    exporter_enduser_approvals_test
    guac_parser_test
    exporter_guac_desktops_test
)

add_rpm(
    NAME zpn-exporterd
    SPEC rpm/zpn-exporterd.spec
    COMMON_FILES ../rpm/common/files
    MAKEFILE Makefile.rpm.zpn-exporterd
    FILES exporter rpm/zpn_exporterd.service rpm/52-zscaler.preset
)

set(guacd_branch_name release/25.129.x)
set(guacd_package_version 25.129.1)
set(guacd_path
    "/zscaler-et-builds/zpa-pra-guacd/${guacd_branch_name}/${PKG_PLATFORM}/zpn-ot-guacd-${guacd_package_version}-${PKG_PLATFORM}-${PKG_ARCH}.tgz"
)
fetch_s3(PATH ${guacd_path} OUTPUT guacd.tgz)

add_rpm(
    NAME zpn-ot-exporterd
    SPEC rpm/zpn-ot-exporterd.spec
    COMMON_FILES ../rpm/common/files
    MAKEFILE Makefile.rpm.zpn-ot-exporterd
    FILES
        exporter
        ${CMAKE_CURRENT_BINARY_DIR}/guacd.tgz
        rpm/guacd.conf
        rpm/52-zscaler-ot.preset
        rpm/zpn_ot_exporterd.service
)
