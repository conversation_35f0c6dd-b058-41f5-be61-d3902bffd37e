#!/usr/bin/env python3
# 2022 <PERSON>
# setup and enroll branch_connector

import argparse
import json
from shutil import copyfile

import requests
import subprocess
import uuid
# from StringIO import StringIO
from base64 import b64decode
from bs4 import BeautifulSoup
from subprocess import call, check_output
from tempfile import NamedTemporaryFile
import ipaddress
import os
from colorama import Fore, Back, Style
from requests import HTTPError
import datetime
import time
from configparser import ConfigParser, ParsingError
from datetime import datetime, timedelta

# examples:
#    python3 bc_enroll.py -config <CONFIG FILE> -show

args = None  # program args
auth_token = None
mfa = False

env = None
username = None
password = None
customer_id = None
headers_json = None
## config file
# [api]
# environment=api-f5.dev.zpath.net
# api-user=<EMAIL>
# api-pass=xxxx
# customer_gid=73191810075197440
#

# defines mandatory config
config_def = {
    "api": ['environment', 'api-user', 'api-pass']
}

epoch = datetime(1970, 1, 1)
# check for existance first
def ch(ll, val):
    return ll[val] if val in ll else "<null>"

def load_values(parser):
    global env
    global username
    global password
    global mfa
    # global customer_id

    # load the values
    env = parser.get('api', 'environment')
    username = parser.get('api', 'api-user')
    password = parser.get('api', 'api-pass')
    mfa = bool(parser.get('api', 'mfa', fallback=False))

    # validate values ranges/types if needed

    if args.api_env:
        env = args.api_env
    return True


#
# config validation and parsing
# validates only presence of mandatory sections and options, not data's types, ranges etc.
#
def check_config(cfg_parser, config_definition):
    for section in config_definition.keys():
        if not cfg_parser.has_section(section):
            print(f"{Fore.RED}ERROR: Invalid config file, required section [{section}] is missing.{Fore.RESET}")
            return False

        for option in config_definition[section]:
            if not cfg_parser.has_option(section, option):
                print(f"{Fore.RED}ERROR: Invalid config file, required option '[{section}]/{option}' is missing {Fore.RESET}")
                return False

    # valid
    return True


def validate_config(is_verbose):
    result = parse_config(args.config_file, load_values, is_verbose)

    if result:
        print(f"{Fore.GREEN}config file: '{args.config_file}' is valid{Fore.RESET}")

    return result


def parse_config(config_file_name, load_func, is_verbose):
    try:

        if is_verbose:
            print(f"{Fore.YELLOW}reading config file: '{config_file_name}'{Fore.RESET}")

        parser = ConfigParser()

        # What a stupid api ! 'read_file' would throw error, but 'read' wont, if the file doesnt exist
        parser.read_file(open(config_file_name))

        # check config file
        if not check_config(parser, config_def):
            return False

    except Exception as err:
        print(f"{Fore.RED}ERROR: {err} [{type(err)}]{Fore.RESET}")
        return False

    # load the values
    return load_func(parser)


def pretty_print_POST(req):
    print('{}\n{}\n{}\n\n{}'.format(
        '----------------------',
        req.method + ' ' + req.url,
        '\n'.join('{}: {}'.format(k, v) for k, v in req.headers.items()),
        req.body,
    ))


def post_api(url, payload, headers, output=True):
    global args

    if output and not args.verbose:
        print(f"{Fore.YELLOW}POST {url} {Fore.RESET}")

    if args.verbose:
        prep = requests.Request('POST', url,
                                data=payload,
                                headers=headers)

        prepared = prep.prepare()
        pretty_print_POST(prepared)

    # the actual call
    result = requests.post(url, data=payload, headers=headers)

    if result.status_code != 200:
        try:
            result.raise_for_status()
        except HTTPError as err:
            print(f"{Fore.RED}POST Response:\n{url}\n{err}\n{result.content} {Fore.RESET}")

        return False, result  # error
    else:
        if args.verbose:
            print(f"{Fore.GREEN}Post success: {result.status_code}\n\t{result.content.decode('ascii')}{Fore.RESET}\n")
        return True, result

def put_api(url, payload, headers, output=True):
    global args

    if output and not args.verbose:
        print(f"{Fore.YELLOW}PUT {url} {Fore.RESET}")

    if args.verbose:
        prep = requests.Request('PUT', url,
                                data=payload,
                                headers=headers)

        prepared = prep.prepare()
        pretty_print_POST(prepared)

    # the actual call
    result = requests.put(url, data=payload, headers=headers)

    if result.status_code != 200:
        try:
            result.raise_for_status()
        except HTTPError as err:
            print(f"{Fore.RED}PUT Response:\n{url}\n{err}\n{result.content} {Fore.RESET}")

        return False, result  # error
    else:
        if args.verbose:
            print(f"{Fore.GREEN}PUT success: {result.status_code}\n\t{result.content.decode('ascii')}{Fore.RESET}\n")
        return True, result

def get_api(url, label):
    global headers_json

    if args.verbose:
        print(f"{Fore.YELLOW}GET {url}{Fore.RESET}\n{headers_json}")

    if label:
        print(f"{Fore.YELLOW}{label}{Fore.RESET}")

    result = requests.get(url, headers=headers_json)

    if result.status_code != 200:

        try:
            result.raise_for_status()
        except HTTPError as err:
            print(f"{Fore.RED}GET Failed. Response:\n{url}\n{err}\n{result.content} {Fore.RESET}")

        return False, result
    else:
        if args.verbose:
            print(f"{Fore.GREEN}GET Success: {result.status_code}\n\t{result.content.decode('ascii')}{Fore.RESET}\n")
        return True, result


def authenticate():
    print(f"{Fore.MAGENTA}Authenticating user={username} for '{env}' {Fore.RESET}")

    headers_auth = {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Accept': '*/*',
        'Host': 'authn1.dev.zpath.net',
        'Origin': f'https://{env}',
        'Referer': f'https://{env}/',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-site',
        'Sec-GPC': '1',
        'TE': 'trailers',
        'DNT': '1'
    }

    url = "https://authn1.dev.zpath.net/authn/v1/oauth/token?grant_type=USER"

    auth_payload = dict()

    auth_payload['username'] = username
    auth_payload['password'] = password

    out, result = post_api(url, auth_payload, headers_auth, output=False)
    if out:
        global auth_token
        auth_token = json.loads(result.content)['Z-AUTH-TOKEN']

        # prepare the headers
        global headers_json
        headers_json = {'Accept': '*/*',
                        'Content-Type': 'application/json',
                        'Authorization': f'Bearer {auth_token}'}

        if args.verbose:
            print(headers_json)

        return True
    else:
        return False


#
# generic get and show
#
def show(url, label, dt_type=0):
    url_final = f"https://{env}{url}"

    out, result = get_api(url_final, label)
    if out:
        js = json.loads(result.content)

        if args.compact and dt_type > 0 :
            cnt = 0
            if dt_type == 1:
                # extranets
                xn = js['list']
                for y in xn:
                    cnt += 1
                    print(f"{cnt}. {y['id']} = '{y['name']}'")
        else:
            print(f"{Fore.GREEN}{json.dumps(js, indent=3)}{Fore.RESET}\n")


def show_locations():
    # 2024, July - remove summary,list, as it appears not be supported anymore
    url_summary = f"https://{env}/zpn/api/v1/admin/customers/{customer_id}/location/"
    create_mock_locations = ""

    out, result = get_api(url_summary, "Locations:")
    if out:

        js = json.loads(result.content)

        if args.verbose:
            print(f"{Fore.GREEN}{json.dumps(js, indent=3)}{Fore.RESET}\n")

        count = 0
        for i in js:
            count += 1
            try:
                if not args.create_mock_locations:
                    print(
                        f"{Fore.CYAN}{count:4}. id={i['id']} loc={i['ziaLocationId']} '{i['name']}' "
                        f"cloud='{ch(i, 'ziaCloud')}' org={ch(i, 'ziaOrgId')}  er={ch(i, 'ziaErId')} grp={ch(i, 'ziaLocationGroup')} "
                        f"type={i['type']} parent={ch(i, 'ziaParentLocationId')} gid={i['id']}" + Fore.RESET)

                if args.create_mock_locations and i['type'] == 'EXTRANET':
                    if len(create_mock_locations) > 0:
                        create_mock_locations += "\n"

                    ln = {"zpn_zia_location_update": {"msg_id": count,
                                                      "zia_cloud_name": i['ziaCloud'],
                                                      "zia_org_id": i['ziaOrgId'],
                                                      "zia_partner_id": i['ziaErId'],
                                                      "zia_location_id": i['ziaLocationId'],
                                                      "zia_instance_id": 4000,
                                                      "zia_tunnel_id": count,
                                                      "tunnel_status": 1,
                                                      "ttl_secs": 300,
                                                      "tunnel_score": 91,
                                                      "instance_score": 94}}

                    create_mock_locations += json.dumps(ln)

            except Exception as err:
                print(f"{Fore.RED}{count:4}.ERROR: {err} [{type(err)}]{Fore.RESET}")

        if args.create_mock_locations:
            print(create_mock_locations)

def show_loc_refs():
    if not args.ziaCloud or not args.ziaOrgId or not args.ziaLocationId:
        print(f"{Fore.RED}Missing required parameters for action 'show_loc_refs':  -ziaCloud, -ziaOrgId , -ziaLocationId {Fore.RESET}")
        return

    global headers_json
    url = f"https://{env}/zpn/api/v1/admin/customers/{customer_id}/location/referenceCheck?ziaLocationId={args.ziaLocationId}&ziaOrgId={args.ziaOrgId}&ziaCloud={args.ziaCloud}"

    out, result = get_api(url, "Locations:")
    if out:
        js = json.loads(result.content)
        print(f"{Fore.GREEN}{json.dumps(js, indent=3)}{Fore.RESET}\n")


def show_extranet_refs():
    if not args.ziaCloud or not args.ziaOrgId or not args.ziaErId:
        print(f"{Fore.RED}Missing required parameters for action 'show_extranet_refs':  -ziaCloud, -ziaOrgId , -ziaErId {Fore.RESET}")
        return

    global headers_json
    url = f"https://{env}/zpn/api/v1/admin/customers/{customer_id}/extranetResource/referenceCheck?ziaErId={args.ziaErId}&ziaOrgId={args.ziaOrgId}&ziaCloud={args.ziaCloud}"

    out, result = get_api(url, "Locations:")

    if out:
        js = json.loads(result.content)
        print(f"{Fore.GREEN}{json.dumps(js, indent=3)}{Fore.RESET}\n")
        if result.status_code == 200:
            print(f"{Fore.GREEN}Found extranetResource for ziaCloud={args.ziaCloud}, ziaOrgId={args.ziaOrgId} , ziaErId={args.ziaErId}{Fore.RESET}")
    else:
        print(f"{Fore.RED}Not Found extranetResource for ziaCloud={args.ziaCloud}, ziaOrgId={args.ziaOrgId} , ziaErId={args.ziaErId}{Fore.RESET}")


def add_service_topic(name, desc, serviceUrl, zoneId):
    global headers_json
    url = f"https://{env}/zpn/api/v1/admin/zones/{zoneId}/endpoints"

    payload = {
        "name": name,
        "description": desc,
        "zoneId": zoneId,
        "serviceUrl": serviceUrl}

    out, result = post_api(url, json.dumps(payload), headers_json)

    if out:
        js = json.loads(result.content)
        print(f"{Fore.GREEN}{json.dumps(js, indent=3)}{Fore.RESET}\n")


def show_zones():
    global headers_json
    url = f"https://{env}/zpn/api/v1/admin/zones"

    out, result = get_api(url, "Zones:")

    if out:
        js = json.loads(result.content)
        # print(f"{Fore.GREEN}{json.dumps(js, indent=3)}{Fore.RESET}\n")
        if result.status_code == 200:

            if args.verbose:
                print(f"{len(js)}")
                for i in js:
                    print(f"{Fore.GREEN}Found {json.dumps(i, indent=3)}{Fore.RESET}")
            count = 0
            for i in js:
                count += 1

                if 'serviceEndpoints' in i:
                    sep = len(i['serviceEndpoints'])
                else:
                    sep = 0
                print(
                    f"{Fore.CYAN if i['name'] == 'Default' else Fore.YELLOW}{count:3}.{i['name']:40} id={i['id']:5} {'enabled' if i['id'] else 'disabled'} {'openForProvisioning' if i['id'] else 'NotOpenForProvisioning'} serviceEndpoints={sep}{Fore.RESET}")

                if sep > 0:
                    sp = i['serviceEndpoints']
                    kount = 0
                    topic_found = False
                    zpn_health_url = None
                    zpn_health_zone = None
                    for k in i['serviceEndpoints']:
                        if 'service.zpa.log.zpn.zia_health' in k['name']:
                            topic_found = True
                        if 'service.zpa.log.zpn.health' in k['name']:
                            zpn_health_url = k['serviceUrl'] if 'serviceUrl' in k else ''
                            zpn_health_zone = k['zoneId']

                        if not args.compact and (args.all or 'service.zpa.log.zpn.health' in k['name'] or 'service.zpa.log.zpn.zia_health' in k['name']):
                            kount += 1
                            print(f"{Fore.MAGENTA if 'zia_health' in k['name'] else Fore.WHITE}     {kount:3} {k['name']:50} zone={k['zoneId']:5} {k['serviceUrl'] if 'serviceUrl' in k else ''} ")

                    if not topic_found and args.add_topic and zpn_health_url:
                        zpn_health_url = zpn_health_url.replace('zpn_health', 'zia_health')
                        print(f"{Fore.RED}About to add topic zia_health: zone={zpn_health_zone} - {zpn_health_url}{Fore.RESET}")
                        input("Press Enter to continue...")
                        add_service_topic("service.zpa.log.zpn.zia_health", "ZIA Health Logs", zpn_health_url, zpn_health_zone)



    else:
        print(f"{Fore.RED}Not Found{Fore.RESET}")


def show_bc():
    url_summary = f"https://{env}/zpn/api/v1/admin/customers/{customer_id}/branchConnectorGroup?page=1&pagesize=20"

    out, result = get_api(url_summary, "Branch Connectors Groups:")
    if out:
        js = json.loads(result.content)
        print(f"{Fore.GREEN}{json.dumps(js, indent=3)}{Fore.RESET}\n")

    # url_summary = f"https://{env}/zpn/api/v1/admin/customers/{customer_id}/location/search?page=1&pagesize=20"

    # out, result = get_api(url_summary, "Locations:")
    # if out:
    #     js = json.loads(result.content)
    #     print(f"{Fore.GREEN}{json.dumps(js, indent=3)}{Fore.RESET}\n")

def show_extranets():
    url_summary = f"https://{env}/zpn/api/v1/admin/customers/{customer_id}/extranetResource/summary?page=1&pagesize=1000"

    out, result = get_api(url_summary, "Extranets:")
    if out:

        js = json.loads(result.content)

        if args.verbose:
            print(f"{Fore.GREEN}{json.dumps(js, indent=3)}{Fore.RESET}\n")

        if not args.compact:
            count = 0
            for i in js['list']:

                count += 1
                print(
                    Fore.CYAN + f"{count:4}. id={i['id']} '{i['name']}' " + Fore.RESET)
                # else:
                #     print(f"{Fore.GREEN}{count}. {i['id']}:{Fore.RESET}")
                #     print(f"{Fore.GREEN}{json.dumps(js2, indent=3)}{Fore.RESET}")

        print(Fore.YELLOW + f"Pages={js['totalPages']} Count={js['totalCount']}" + Fore.RESET)

def create_extranet():
    if not args.ziaCloud or not args.ziaOrgId or not args.ziaErId:
        print(f"{Fore.RED}Missing required parameters for action 'create_extranet':  -ziaCloud, -ziaOrgId , -ziaErId {Fore.RESET}")
        return

    global headers_json
    url = f"https://{env}/zpn/api/v1/admin/customers/{customer_id}/extranetResource"

    multi = int(args.multi)

    for i in range(multi):
        er = int(args.ziaErId)
        er += i
        payload = {
            "ziaErName": f"partner_{er}",
            "ziaCloud": f"{args.ziaCloud}",
            "ziaOrgId": f"{args.ziaOrgId}",
            "ziaErId": f"{er}"}

        if args.test:
            # just print
            print(f"{i} {payload}")
        else:
            # execute
            out, result = put_api(url, json.dumps(payload), headers_json)

            if out:
                js = json.loads(result.content)
                print(f"{Fore.GREEN}{i}.{json.dumps(js, indent=3)}{Fore.RESET}\n")

def create_extranet_location():
    if not args.ziaCloud or not args.ziaOrgId or not args.ziaErId or not args.ziaLocationId:
        print(f"{Fore.RED}Missing required parameters for action 'create_extranet_location':  -ziaCloud, -ziaOrgId , -ziaErId, -ziaLocationId {Fore.RESET}")
        return

    global headers_json
    url = f"https://{env}/zpn/api/v1/admin/customers/{customer_id}/location"

    multi = int(args.multi)

    for i in range(multi):
        er = int(args.ziaErId)
        er += i

        loc = int(args.ziaLocationId)
        loc += i
        payload = {
            "locationType": "PARENT_LOCATION",
            "type": "EXTRANET",
            "ziaLocationId": f"{loc}",
            "name": f"loc_{loc}_{er}",
            "ziaCloud": f"{args.ziaCloud}",
            "ziaOrgId": f"{args.ziaOrgId}",
            "ziaErId": f"{er}",
            "ziaLocationGroup": [0]
        }

        if args.test:
            # just print
            print(f"{i} {payload}")
        else:
            out, result = post_api(url, json.dumps(payload), headers_json)

            if out:
                js = json.loads(result.content)
                print(f"{Fore.GREEN}{i}.{json.dumps(js, indent=3)}{Fore.RESET}\n")

def create_location():
    # check params
    if not args.name or not args.ziaCloud or not args.ziaOrgId or not args.ziaLocationId:
        print(f"{Fore.RED}Missing required parameters for action 'create_location': -name -ziaCloud, -ziaOrgId , -ziaLocationId {Fore.RESET}")
        return

    global headers_json
    url = f"https://{env}/zpn/api/v1/admin/customers/{customer_id}/location"

    payload = {
        "name": f"{args.name}",
        "locationType": "PARENT_LOCATION",
        "ziaCloud": f"{args.ziaCloud}",
        "ziaOrgId": f"{args.ziaOrgId}",
        "ziaLocationId": f"{args.ziaLocationId}"}

    out, result = post_api(url, json.dumps(payload), headers_json)

    if out:
        js = json.loads(result.content)
        print(f"{Fore.GREEN}{json.dumps(js, indent=3)}{Fore.RESET}\n")


def nonce(assos_type):
    # check params
    if not args.nonce_name or not args.nonce_cert_id or not args.nonce_grp_id:
        print(f"{Fore.RED}Missing required parameters for action 'nonce': -nonce_name, -nonce_cert_id , -nonce_grp_id {Fore.RESET}")
        return None, None

    global headers_json
    url = f"https://{env}/zpn/api/v1/admin/customers/{customer_id}/associationType/{assos_type}/nonce"

    payload = {
        "autoSign": 1,
        "maxUsage": 1,
        "name": args.nonce_name,
        "nonceAssociationType": assos_type,
        "signingCertId": args.nonce_cert_id,
        "zcomponentId": args.nonce_grp_id
    }

    if args.payload:
        print(url)
        print(payload)

    out, result = post_api(url, json.dumps(payload), headers_json)

    if args.payload:
        print(result)
        print(out)

    return out, result


### ================================================================
###  get nonce for branch connector
###
def nonce_bc():
    out, result = nonce("BRANCH_CONNECTOR_GRP")

    if out:
        js = json.loads(result.content)
        print(f"{Fore.GREEN}{json.dumps(js, indent=3)}{Fore.RESET}\n")


def bc():
    # check params
    if not args.bc_finger or not args.bc_nonce:
        print(f"{Fore.RED}Missing required parameters for action 'bc': -bc_finger, -bc_nonce {Fore.RESET}")
        return

    global headers_json
    url = f"https://{env}/zpn/api/v2/admin/BRANCH_CONNECTOR/onboard/details"

    tm_s = int(time.time())

    payload = {
        "fingerprint": f"{args.bc_finger}",
        "nonce": f"{args.bc_nonce}",
        "timestampInSec": f"{tm_s}"
    }

    if args.payload:
        print(url)
        print(payload)

    out, result = post_api(url, json.dumps(payload), headers_json)

    if args.payload:
        print(result)

    if out:
        js = json.loads(result.content)
        print(f"{Fore.GREEN}{json.dumps(js, indent=3)}{Fore.RESET}\n")


def cert(csr_or_cert):
    # check params
    if not args.bc_finger or not args.bc_nonce or not args.cert_csr:
        print(f"{Fore.RED}Missing required parameters for action 'cert': -bc_finger, -bc_nonce, -cert_csr {Fore.RESET}")
        return

    csr = ""
    with open(args.cert_csr) as f:
        csr = f.read()

    print(csr)

    global headers_json
    action = "csr" if csr_or_cert == 1 else "certificate"
    url = f"https://{env}/zpn/api/v2/admin/BRANCH_CONNECTOR/onboard/{action}"

    tm_s = int(time.time())

    payload = {
        "csr": csr,
        "fingerprint": f"{args.bc_finger}",
        "nonce": f"{args.bc_nonce}",
        "timestampInSec": f"{tm_s}"
    }

    out, result = post_api(url, json.dumps(payload), headers_json)

    if out:
        js = ""

        if result.content:
            if csr_or_cert == 1:

                js = json.loads(result.content)
                print(f"{Fore.GREEN}success: {json.dumps(js, indent=3)}{Fore.RESET}\n")
            else:
                print(f"{Fore.GREEN}success: {result.content}{Fore.RESET}\n")
                with open("bc_certificate.pem" if args.cert_pem_out is None else args.cert_pem_out, 'wb') as f:
                    f.write(result.content)


# helper
def j(dict_val, name):
    if name not in dict_val:
        return ""
    return dict_val[name]


# show assistants
def show_assistants():
    url_summary = f"https://{env}/shift/api/v1/admin/customers/{customer_id}/assistantGroup?page=1&pagesize=20"

    out, result = get_api(url_summary, "Assistant Group:")
    if out:
        js = json.loads(result.content)

        if args.verbose:
            print(f"{Fore.GREEN}{json.dumps(js, indent=3)}{Fore.RESET}\n")
        else:
            if "list" in js:
                for grp in js["list"]:
                    is_enabled = grp['enabled']
                    print(f"{Fore.YELLOW}  GRP={j(grp, 'id')} '{j(grp, 'name')}' '{j(grp, 'location')}'{'' if is_enabled else 'disabled'} {Fore.RESET}")

                    if 'assistants' in grp:
                        for conn in grp['assistants']:
                            is_enabled = conn['enabled']
                            print(f"       Assistant={j(conn, 'id')} '{j(conn, 'name')}' cert={j(conn, 'issuedCertId')} {'' if is_enabled else 'disabled'}")
            else:
                print(f"{Fore.RED} No assistants {Fore.RESET}")


### ================================================================
### enroll edge connector
### assos_type="BRANCH_CONNECTOR_GRP" or "EDGE_CONNECTOR_GRP"
###
def enroll_ec():
    enroll("EDGE_CONNECTOR_GRP")
def enroll_bc():
    enroll("BRANCH_CONNECTOR_GRP")

def enroll(assos_type):
    # check input params
    if not args.nonce_id:
        print(f"{Fore.RED}Missing required parameters for action 'enroll_ec': -nonce_id {Fore.RESET}")
        return

    url_summary = f"https://{env}/shift/api/v1/admin/customers/{customer_id}/associationType/{assos_type}/nonce/{args.nonce_id}"
    out, result = get_api(url_summary, "Nonce:")
    if out:
        js = json.loads(result.content)
        if args.verbose or args.payload:
            print(f"{Fore.GREEN}{json.dumps(js, indent=3)}{Fore.RESET}\n")

        print(f"\t{js['nonce']}")

        out_json = {"zpa_clouds": [
            {
                "name": "dev.zpath.net",
                "zpa_api_server": env,
                "fingerprint": f"finger-{js['name']}",
                "provision_key": js['nonce'],
            }]}

        print(f"{Fore.YELLOW}wrote znf_enrollment.json{Fore.RESET}")
        with open('znf_enrollment.json', 'w') as f:
            json.dump(out_json, f)

        if args.verbose or args.payload:
            print(f"znf_enrollment.json: {out_json}")

        print(f"{Fore.CYAN}1. call python3 itasca/src/tools/ec_enroll/ec_enroll.py -client_type 0|1|2 (znf|BRANCH_CONNECTOR|ZVPN) {Fore.RESET}")
        print(f"{Fore.CYAN}2. call python3 itasca/src/tools/bc_enroll/bc_enroll.py -config bc.config -extract_cert XXX {Fore.RESET}")


def write_cert(file_name, txt):
    with open(file_name, 'w') as f:
        f.write(txt)


### python3  ~/code/itasca2/src/tools/bc_enroll/bc_enroll.py -customer 73191810075197440 -config bc.config -extract_cert ec1
def extract_cert():
    with open('znf_enrollment.json', 'r') as f:
        js = json.load(f)

    if args.verbose:
        print(f"{Fore.LIGHTBLACK_EX}{json.dumps(js, indent=3)}{Fore.RESET}\n")

    ls = js["zpa_clouds"][0]
    print(f"cname={ls['cname']} organization={ls['organization']}\n")

    write_cert(f"{args.extract_cert}.private.key", ls['private_key'])
    write_cert(f"{args.extract_cert}.chain.crt", ls['cert_chain'])

    print(f"{Fore.YELLOW}wrote: private.{args.extract_cert}.key{Fore.RESET}")
    print(f"{Fore.YELLOW}wrote: chain.{args.extract_cert}.crt{Fore.RESET}")

    sni = ls['cname']

    run_type = "ec"
    sni = sni.replace("znf", "ec")
    sni = sni.replace("branchconnector", "bc")

    if sni.find("bc_"):
       run_type = "bc"

    cmd = f"-{run_type} -ca dev.zpath.net.crt -key {args.extract_cert}.private.key -cert {args.extract_cert}.chain.crt -sni {sni}"

    print(f"{Fore.CYAN}zpn_clientd params: {cmd}{Fore.RESET}")

    file_name = f"{args.extract_cert}.{run_type}.config"
    with open(file_name, 'w') as f:
        f.write(cmd)

    print(f"{Fore.YELLOW}wrote config file:{file_name}{Fore.RESET}")


### ================================================================
###  show signing certs, ec groups,etc
###
def show_ec():
    global customer_id

    url_summary = f"https://{env}/zpn/api/v1/admin/customers/{customer_id}/edgeConnectorGroup"
    out, result = get_api(url_summary, "Edge Connector groups:")
    if out:
        js = json.loads(result.content)
        if args.verbose:
            print(f"{Fore.GREEN}{json.dumps(js, indent=3)}{Fore.RESET}\n")
        else:
            for i in range(0, len(js['list'])):
                item = js['list'][i]
                cr_s = int(item['creationTime'])

                tm = str(epoch + timedelta(seconds=cr_s))
                print(f"\t{i}. id={item['id']} name='{item['name']}' ziaCloud='{item['ziaCloud']}' ziaOrgId='{item['ziaOrgId']}' "
                      f"{'enabled' if item['enabled'] else 'disabled'} mod={tm} by {item['modifiedBy']}  connectors={len(item['edgeConnectors']) if 'edgeConnectors' in item else 0}")
                if 'edgeConnectors' in item:
                    k = 0
                    for x in item['edgeConnectors']:
                        k = k + 1
                        print(f"\t\t{k}.{x['id']} '{x['name']}' finger={x['fingerprint']}")

## assos_type: EDGE_CONNECTOR_GRP|BRANCH_CONNECTOR_GRP
def show_groups(assos_type):
    url_summary = f"https://{env}/shift/api/v1/admin/customers/{customer_id}/associationType/{assos_type}/nonce"
    out, result = get_api(url_summary, f"{assos_type} Nonces:")
    if out:
        js = json.loads(result.content)
        if args.verbose:
            print(f"{Fore.GREEN}{json.dumps(js, indent=3)}{Fore.RESET}\n")
        else:
            if 'list' in js:
                for i in range(0, len(js['list'])):
                    item = js['list'][i]
                    cr_s = int(item['creationTime'])
                    tm = str(epoch + timedelta(seconds=cr_s))
                    print(f"{i}. id={item['id']} name='{item['name']}' usageCount='{item['usageCount']}' maxUsage='{item['maxUsage']}' "
                        f"signingCertId={item['signingCertId']}|'{item['signingCertName']}' zcomponentId={item['zcomponentId']}|'{item['zcomponentName']}'  ")
                    if args.payload:
                        print(f"{Fore.LIGHTBLACK_EX}{json.dumps(item, indent=3)}{Fore.RESET}\n")

def show_certs():
    global customer_id
    url_summary = f"https://{env}/zpn/api/v1/admin/customers/{customer_id}/signingCert"

    out, result = get_api(url_summary, "Signing Certs:")
    if out:
        js = json.loads(result.content)
        if args.verbose:
            print(f"{Fore.GREEN}{json.dumps(js, indent=3)}{Fore.RESET}\n")
        else:
            for i in range(0, len(js)):
                # if 'parentCertId' not in js[i]:
                print(f"{i}. id={js[i]['id']} allowSigning={js[i]['allowSigning']} name='{js[i]['name']}' parent={js[i]['parentCertId'] if 'parentCertId' in js[i] else ''} ")
                if args.payload:
                    print(f"{Fore.LIGHTBLACK_EX}{json.dumps(js[i], indent=3)}{Fore.RESET}\n")

    show_groups("EDGE_CONNECTOR_GRP")
    show_groups("BRANCH_CONNECTOR_GRP")
    show_groups("ZVPN_GRP")

    if not args.payload:
        print("HINT: use -p to print full response")

#
# ==========================
def create_ec_group():
    if not args.name or not args.ziaOrgId or not args.ziaCloud:
        print(f"{Fore.RED}Missing required parameters for action 'create_ec_group': -name -ziaOrgId -ziaCloud {Fore.RESET}")
        return

    global headers_json
    global customer_id
    url = f"https://{env}/zpn/api/v1/admin/customers/{customer_id}/edgeConnectorGroup"

    payload = {
        "name": f"{args.name}",
        "ziaCloud": f"{args.ziaCloud}",
        "ziaOrgId": f"{args.ziaOrgId}",
        "enabled": "true",
    }
    out, result = post_api(url, json.dumps(payload), headers_json)

    if out:
        js = json.loads(result.content)
        print(f"{Fore.GREEN}{json.dumps(js, indent=3)}{Fore.RESET}\n")

def bi_cert():
    # check params
    if not args.cert_csr or not args.cert_pem_out:
        print(f"{Fore.RED}Missing required parameters for action '-bi_cert':  -cert_csr , -cert_pem_out {Fore.RESET}")
        return

    csr = ""
    with open(args.cert_csr) as f:
        csr = f.read()
    # remove new lines
    csr = csr.replace("\n", "\\n")
    print(csr)

    global headers_json
    global customer_id
    url = f"https://{env}/zpn/api/v1/admin/client/isolation/onboard"

    payload = {
        "csr": f"{csr}",
        "customerId": f"{customer_id}",
    }
    out, result = post_api(url, json.dumps(payload), headers_json)

    if out:
        cert = result.content.decode('ascii').replace("\\n", "\n")
        cert = cert.replace("--|--", "--\n--")
        cert = cert.replace('"', '')

        print(f"{Fore.GREEN}{cert}{Fore.RESET}\n")

        write_cert(args.cert_pem_out, cert)

### ================================================================
###  main
###
def main():
    arg_parser = argparse.ArgumentParser(description='Mgmt API setup tool for connectors, extranets, locations, certs')
    arg_parser.add_argument('-api_cfg', '--config-file', default="", help='specify the config file ')
    arg_parser.add_argument('-api_env', '--api_env', default="", help='specify the api_env, overwrites config file ')
    arg_parser.add_argument('-v', '--verbose', action='store_true', default=False, help='PARAM: verbose output')
    arg_parser.add_argument('-customer', '--customer', default=None, help='specify customer id')
    arg_parser.add_argument('-p', '--payload', action='store_true', default=False, help='PARAM: payload output for command only')

    ## validate config
    arg_parser.add_argument('-validate', '--validate_config', action='store_true', default=False, help='ACTION: validate config file and exit')

    ## show existing data
    arg_parser.add_argument('-show_locations', '--show_locations', action='store_true', default=False, help='ACTION: show locations')
    arg_parser.add_argument('-show_loc_groups', '--show_loc_groups', action='store_true', default=False, help='ACTION: show location groups')
    arg_parser.add_argument('-show_loc_refs', '--show_loc_refs', action='store_true', default=False, help='ACTION: show locations reference')
    arg_parser.add_argument('-show_extranets', '--show_extranets', action='store_true', default=False, help='ACTION: show extranets')
    arg_parser.add_argument('-show_extranets_refs', '--show_extranets_refs', action='store_true', default=False, help='ACTION: show extranets references')
    arg_parser.add_argument('-show_partners', '--show_partners', action='store_true', default=False, help='ACTION: show extranets partners')
    arg_parser.add_argument('-show_bc', '--show_bc', action='store_true', default=False, help='ACTION: show branch connectors')
    arg_parser.add_argument('-show_certs', '--show_certs', action='store_true', default=False, help='ACTION: show certs and nounces')
    arg_parser.add_argument('-show_customer', '--show_customer', action='store_true', default=False, help='ACTION: show customer')
    # assistant/groups
    arg_parser.add_argument('-show_assistants', '--get_assistants', action='store_true', default=False, help='ACTION: show assistant')

    # get the zones and end points
    arg_parser.add_argument('-show_zones', '--show_zones', action='store_true', default=False, help='ACTION: show zones')

    ## create location
    arg_parser.add_argument('-create_location', '--create_location', action='store_true', default=False, help='ACTION: create location')
    arg_parser.add_argument('-name', '--name', help='PARAM: new name, applies to location and groups')
    arg_parser.add_argument('-create_extranet', '--create_extranet', action='store_true', default=False, help='ACTION: create extranet')
    arg_parser.add_argument('-create_extranet_location', '--create_extranet_location', action='store_true', default=False, help='ACTION: create extranet location')
    arg_parser.add_argument('-loc_name', '--loc_name', help='PARAM: location name')
    arg_parser.add_argument('-ziaCloud', '--ziaCloud', help='PARAM: ziaCloud e.g. zscaler.net')
    arg_parser.add_argument('-ziaOrgId', '--ziaOrgId', help='PARAM: ziaOrgId e.g. 12345')
    arg_parser.add_argument('-ziaErId', '--ziaErId', help='PARAM: ziaErId e.g. 12345')

    arg_parser.add_argument('-ziaLocationId', '--ziaLocationId', help='PARAM: ziaLocationId e.g. 2')

    # nonce
    arg_parser.add_argument('-nonce', '--nonce', action='store_true', default=False, help='get nonce for branch connector')
    arg_parser.add_argument('-nonce_bc', '--nonce_bc', action='store_true', default=False, help='get nonce for branch connector')
    arg_parser.add_argument('-nonce_name', '--nonce_name', help='PARAM: nonce_name')
    arg_parser.add_argument('-nonce_cert_id', '--nonce_cert_id', help='PARAM: nonce_cert_id')
    arg_parser.add_argument('-nonce_grp_id', '--nonce_grp_id', help='PARAM: nonce_grp_id')

    # provision bc
    arg_parser.add_argument('-bc', '--bc', action='store_true', default=False, help='ACTION: setup branch connector')
    arg_parser.add_argument('-bc_finger', '--bc_finger', help='PARAM: branch connector fingerprint')
    arg_parser.add_argument('-bc_nonce', '--bc_nonce', help='PARAM: branch connector bc_nonce from -nonce output')

    # provision csr
    arg_parser.add_argument('-csr', '--csr', action='store_true', default=False, help='ACTION: setup branch csr. ')
    arg_parser.add_argument('-cert_csr', '--cert_csr', help='PARAM: path to file with csr. To generate CSR use: openssl req -out CSR.crt -new -newkey rsa:2038 -nodes -keyout private.key')

    # get the cert
    arg_parser.add_argument('-get_cert', '--get_cert', action='store_true', default=False, help='ACTION: get branch certificate')
    arg_parser.add_argument('-cert_pem_out', '--cert_pem_out', help='PARAM: path to output file with pem')

    arg_parser.add_argument('-add_topic', '--add_topic', action='store_true', default=False, help='ACTION: with show_zones, add zia_health topic to all')

    # common
    arg_parser.add_argument('-all', '--all', action='store_true', default=False, help='show all i.e. dont filter')
    arg_parser.add_argument('-c', '--compact', action='store_true', default=False, help='uses compact format whenever applies')
    arg_parser.add_argument('-test', '--test', action='store_true', default=False, help='no actions, just test and outputs')
    arg_parser.add_argument('-multi', '--multi', default=1, help='multiplies action, default is 1')
    arg_parser.add_argument('-create_mock_locations', '--create_mock_locations', action='store_true', default=False, help='use with show_locations')

    # enroll edge connector
    arg_parser.add_argument('-enroll_ec', '--enroll_ec', action='store_true', default=False, help='ACTION: enroll cloud connector.')
    arg_parser.add_argument('-enroll_bc', '--enroll_bc', action='store_true', default=False, help='ACTION: enroll branch connector.')
    arg_parser.add_argument('-show_ec', '--show_ec', action='store_true', default=False, help='ACTION: show signing certs, ec group, etc')
    arg_parser.add_argument('-nonce_id', '--nonce_id', help='PARAM: nonce_id')
    arg_parser.add_argument('-extract_cert', '--extract_cert', default=None, help='ACTION: extract certs from znf_enrollment.json, extract_cert <OUTPUT FILE NAME>')

    arg_parser.add_argument('-create_ec_group', '--create_ec_group', action='store_true', default=False, help='ACTION: creates edge connector group.')

    arg_parser.add_argument('-bi_cert', '--bi_cert', action='store_true', default=False, help='ACTION: creates bi cert')

    global args
    args = arg_parser.parse_args()

    if not args.config_file:
        print(f"{Fore.RED}Missing required parameter: -api_cfg {Fore.RESET}")

        print(Fore.YELLOW)
        arg_parser.print_help()
        print(Fore.RESET)
        return

    if args.validate_config:
        validate_config(args.verbose)
        return

    # parse config
    if not parse_config(args.config_file, load_values, args.verbose):
        return

    # check
    global customer_id
    if args.customer is not None:
        customer_id = args.customer
    else:
        print(f"{Fore.RED}Missing required parameters: -customer {Fore.RESET}")
        return

    if not authenticate():
        return
    # dont need Customer
    if args.show_zones:
        show_zones()
        return
    if args.customer is not None:
        customer_id = args.customer
        if args.verbose:
            print(f"Customer: {customer_id}")
    else:
        print(f"{Fore.RED}Missing required parameters: -customer {Fore.RESET}")
        return
    if args.show_locations:
        # show(f"/zpn/api/v1/admin/customers/{customer_id}/location/summary/", "Locations:")
        show_locations()
        return
    if args.show_loc_refs:
        show_loc_refs()
        return
    if args.show_extranets_refs:
        show_extranet_refs()
        return

    if args.show_extranets:
        show_extranets()
        return
    if args.show_loc_groups:
        show(f"/zpn/api/v1/admin/customers/{
             customer_id}/locationGroup?page=1&pagesize=1000", "Location Groups:")
        return
    if args.show_partners:
        show(f"/zpn/api/v1/admin/customers/{
             customer_id}/extranetResource/partner?page=1&pagesize=1000", "Extranets Partners:")
        return
    if args.show_bc:
        show(f"/zpn/api/v1/admin/customers/{
             customer_id}/branchConnectorGroup?page=1&pagesize=1000", "Branch Connectors Groups:")
        return
    if args.show_customer:
        show(f"/base/api/v1/admin/customers/{customer_id}", "Customer: ")
        return

    if args.show_certs:
        show_certs()
        return

    if args.create_location:
        create_location()
        return
    if args.create_extranet:
        create_extranet()
        return
    if args.create_extranet_location:
        create_extranet_location()
        return

    if args.nonce_bc:
        nonce_bc()
        return

    if args.bc:
        bc()
        return

    if args.csr:
        cert(1)
        return

    if args.get_cert:
        cert(0)
        return

    if args.get_assistants:
        show_assistants()
        return

    if args.enroll_ec:
        enroll_ec()
        return
    if args.enroll_bc:
        enroll_bc()
        return

    if args.show_ec:
        show_ec()
        return

    if args.extract_cert:
        extract_cert()
        return

    if args.create_ec_group:
        create_ec_group()
        return

    if args.bi_cert:
        bi_cert()
        return

    # no action selected, remind we need params
    print(f"{Fore.RED} no action param: -show -nonce -bc -cert ")
    print(Fore.YELLOW)
    arg_parser.print_help()
    print(Fore.RESET)


if __name__ == "__main__":
    main()
