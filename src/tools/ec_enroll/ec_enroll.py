#!/usr/bin/env python

# Author: <PERSON><PERSON>
# Created: May 9, 2019
# updated : <PERSON> Dec 6 2024
# requires python3 , add support for branch connector, zvpn

from OpenSSL import crypto
import requests, base64, time
import json, traceback, sys, os
from datetime import datetime, timedelta
import argparse

# the type of the client,
# znf - default - Cloud Counnector
# BRANCH_CONNECTOR -  Branch Connector
# ZVPN - ZVPN
client_type = "znf"

znf_config_json = {
	"zpa_clouds": [
		{
			"csr":"",
			"city":"",
			"name":"",
			"cname":"",
			"state":"",
			"country":"",
			"cert_chain":"",
			"fingerprint":"",
			"private_key":"",
			"address_pool":"",
			"organization":"",
			"provision_key":"",
			"conn_pool_size":4,
			"zpa_api_server":"",
			"customers": [217305040719708160],
			"brokers": [
				{
					"sni":"",
					"port":443,
					"hostname":""
				}
			]
		}
	]
}


http_req_headers = {
	"Accept": "application/json",
	"Content-type": "application/json"
}


config_filename = "znf_enrollment.json"
args = None

def jsonToDict(json_str):
	dict_obj = {}
	try:
		dict_obj = json.loads(json_str)
	except:
		print("Failed to convert to dictionary: " + traceback.format_exc())
	return dict_obj


def dictToJson(dict_obj):
	json_str = ""
	try:
		json_str = json.dumps(dict_obj, indent = 4)
	except:
		print("Failed to convert to json: " + traceback.format_exc())
	return json_str


def loadConfigJson():
	try:
		global znf_config_json
		config_file = open(config_filename, "r")
		znf_config_json = jsonToDict(config_file.read())
		config_file.close()
	except:
		print("Config read error: " + traceback.format_exc())


def saveConfigJson():
	try:
		config_file = open(config_filename, "w")
		config_file.write(dictToJson(znf_config_json))
		config_file.close()
	except:
		print("Config save error: " + traceback.format_exc())


def checkCmdArguments():
	global config_filename
	if args and args.config_file:
		if os.path.isfile(args.config_file):
			config_filename = args.config_file
		else:
			print("ERROR: Enrollment warn: argument is not a file")
			exit(1)

	print("using config '" + config_filename + "'")


def getEpochSecTimestamp():
	return str(int(time.time()))


def getCloudServer(zpa_cloud_entry):
	return "https://" + zpa_cloud_entry["zpa_api_server"]


def checkInputJsonField(cloud_entry, field_name):
	field_valid = False
	if field_name in cloud_entry:
		if cloud_entry[field_name].strip():
			field_valid = True
	return field_valid


def validateCertificateChain(zpa_cloud_entry):
	cert_valid = False
	if checkInputJsonField(zpa_cloud_entry, "private_key"):
		if checkInputJsonField(zpa_cloud_entry, "cert_chain"):
			try:
				cert_chain = crypto.load_certificate(crypto.FILETYPE_PEM, zpa_cloud_entry["cert_chain"].encode("utf-8"))
				sign_key = crypto.load_privatekey(crypto.FILETYPE_PEM, zpa_cloud_entry["private_key"].encode("utf-8"))
				expire_time = datetime.strptime(cert_chain.get_notAfter().decode("utf-8"), "%Y%m%d%H%M%SZ")
				print("Certificate expiration date: " + expire_time.strftime("%B %d, %Y %I:%M %p UTC"))
				if cert_chain.has_expired():
					print("Certificate has expired for: " + zpa_cloud_entry["name"])
				else:
					if datetime.today() > (expire_time - timedelta(days=15)):
						print("Certificate will expire soon for: " + zpa_cloud_entry["name"])
					else:
						print("Valid certificate exists for: " + zpa_cloud_entry["name"])
						cert_valid = True
			except:
				print("Failed to validate cert chain: " + traceback.format_exc())
	return cert_valid


def getAPISignature(zpa_cloud_entry):
	api_sign = ""
	if checkInputJsonField(zpa_cloud_entry, "private_key"):
		try:
			sign_key = crypto.load_privatekey(crypto.FILETYPE_PEM, zpa_cloud_entry["private_key"].encode("utf-8"))
			sha_digest = crypto.sign(sign_key, getEpochSecTimestamp().encode("utf-8"), "sha256")
			api_sign = base64.b64encode(sha_digest).decode()
		except:
			print("Failed to get signature: " + traceback.format_exc())
	return api_sign


def generateCSR(zpa_cloud_entry):
	csr_req = crypto.X509Req()
	private_key = crypto.PKey()
	if checkInputJsonField(zpa_cloud_entry, "city"):
		csr_req.get_subject().L = zpa_cloud_entry["city"]
	if checkInputJsonField(zpa_cloud_entry, "state"):
		csr_req.get_subject().ST = zpa_cloud_entry["state"]
	if checkInputJsonField(zpa_cloud_entry, "cname"):
		csr_req.get_subject().CN = zpa_cloud_entry["cname"]
	if checkInputJsonField(zpa_cloud_entry, "country"):
		csr_req.get_subject().C = zpa_cloud_entry["country"]
	if checkInputJsonField(zpa_cloud_entry, "org_unit"):
		csr_req.get_subject().OU = zpa_cloud_entry["org_unit"]
	if checkInputJsonField(zpa_cloud_entry, "organization"):
		csr_req.get_subject().O = zpa_cloud_entry["organization"]
	private_key.generate_key(crypto.TYPE_RSA, 2048)
	csr_req.set_pubkey(private_key)
	csr_req.sign(private_key, "sha256")
	zpa_cloud_entry["csr"] = crypto.dump_certificate_request(crypto.FILETYPE_PEM, csr_req).decode("utf-8")
	zpa_cloud_entry["private_key"] = crypto.dump_privatekey(crypto.FILETYPE_PEM, private_key).decode("utf-8")


def executeAPI(api_url, req_dict):
	zpa_api_resp = {}
	http_resp = requests.post(api_url, headers = http_req_headers, data = dictToJson(req_dict))
	zpa_api_resp["http_code"] = http_resp.status_code
	zpa_api_resp["req_body"] = http_resp.request.body
	zpa_api_resp["resp_body"] = http_resp.content.decode("utf-8")
	return zpa_api_resp;


def getCommonAPIFields(zpa_cloud_entry):
	common_json = {}
	common_json["fingerprint"] = zpa_cloud_entry["fingerprint"]
	common_json["signature"] = getAPISignature(zpa_cloud_entry)
	common_json["nonce"] = zpa_cloud_entry["provision_key"]
	common_json["timestampInSec"] = getEpochSecTimestamp()
	return common_json


def executeCertificateAPI(zpa_cloud_entry):
	znf_cert_json = getCommonAPIFields(zpa_cloud_entry)
	znf_cert_json["csr"] = zpa_cloud_entry["csr"]
	api_url = getCloudServer(zpa_cloud_entry) + "/zpn/api/v2/admin/" + client_type + "/onboard/certificate"
	zpa_api_resp = executeAPI(api_url, znf_cert_json)
	if (zpa_api_resp["http_code"] == 200):
		zpa_cloud_entry["cert_chain"] = zpa_api_resp["resp_body"]
		print("Enrollment certificates response: Success")
		saveConfigJson()
	else:
		print("Enrollment certificates request: " + zpa_api_resp["req_body"])
		print("Enrollment certificates error: " + str(zpa_api_resp["http_code"]) + ": " + zpa_api_resp["resp_body"])


def executeCSRAPI(zpa_cloud_entry):
	znf_csr_json = getCommonAPIFields(zpa_cloud_entry)
	generateCSR(zpa_cloud_entry)
	znf_csr_json["csr"] = zpa_cloud_entry["csr"]
	api_url = getCloudServer(zpa_cloud_entry) + "/zpn/api/v2/admin/" + client_type + "/onboard/csr"
	zpa_api_resp = executeAPI(api_url, znf_csr_json)
	if (zpa_api_resp["http_code"] == 204):
		if not zpa_api_resp["resp_body"]:
			print("Enrollment CSR response: 204: Success")
		else:
			print("Enrollment CSR response: " + zpa_api_resp["resp_body"])
		saveConfigJson()
		executeCertificateAPI(zpa_cloud_entry)
	else:
		print("Enrollment CSR request: " + zpa_api_resp["req_body"])
		print("Enrollment CSR error: " + str(zpa_api_resp["http_code"]) + ": " + zpa_api_resp["resp_body"])


def executeDetailsAPI(zpa_cloud_entry):
	znf_details_json = getCommonAPIFields(zpa_cloud_entry)
	api_url = getCloudServer(zpa_cloud_entry) + "/zpn/api/v2/admin/" + client_type + "/onboard/details"
	zpa_api_resp = executeAPI(api_url, znf_details_json)
	if (zpa_api_resp["http_code"] == 200):
		zpa_cloud_entry.update(jsonToDict(zpa_api_resp["resp_body"]))
		print("Got enrollment details: " + zpa_api_resp["resp_body"])
		saveConfigJson()
		executeCSRAPI(zpa_cloud_entry)
	else:
		print("Enrollment details request: " + zpa_api_resp["req_body"])
		print("Enrollment details error: " + str(zpa_api_resp["http_code"]) + ": " + zpa_api_resp["resp_body"])


def enrollEdgeConnector():
	checkCmdArguments()
	loadConfigJson()
	if "zpa_clouds" in znf_config_json:
		for zpa_cloud_entry in znf_config_json["zpa_clouds"]:
			if not checkInputJsonField(zpa_cloud_entry, "name"):
				print("Enrollment error: name not found for zpa cloud entry")
				continue
			if not checkInputJsonField(zpa_cloud_entry, "fingerprint"):
				print("Enrollment error: fingerprint not found for zpa cloud: " + zpa_cloud_entry["name"])
				continue
			if not checkInputJsonField(zpa_cloud_entry,"provision_key"):
				print("Enrollment error: provision_key not found for zpa cloud: " + zpa_cloud_entry["name"])
				continue
			if not checkInputJsonField(zpa_cloud_entry,"zpa_api_server"):
				print("Enrollment error: zpa_api_server not found for zpa cloud: " + zpa_cloud_entry["name"])
				continue
			if not checkInputJsonField(zpa_cloud_entry, "private_key"):
				print("Enrollment warn: no existing private_key found for zpa cloud: " + zpa_cloud_entry["name"])
			if not validateCertificateChain(zpa_cloud_entry):
				executeDetailsAPI(zpa_cloud_entry)

if __name__ == "__main__":
	arg_parser = argparse.ArgumentParser(description='Mgmt API cert setup tool for connectors')
	arg_parser.add_argument('-api_cfg', '--config-file', default="", help='specify the config file ')
	arg_parser.add_argument('-client_type', '--client_type', default=0, help='PARAM: 0 - znf (default), 1-BRANCH_CONNECTOR, 2-ZVPN')

	args = arg_parser.parse_args()
	if args.client_type == 1:
		client_type = "BRANCH_CONNECTOR"
	elif  args.client_type == 2:
		client_type = "ZVPN"

	print("Client type is '" + client_type+ "'")
	enrollEdgeConnector()
