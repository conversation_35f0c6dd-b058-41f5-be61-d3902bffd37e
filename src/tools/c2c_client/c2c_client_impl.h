/*
 * zpn_client.h. Copyright (C) 2014 Zscaler, Inc. All Rights Reserved.
 *
 * These are the routines and definitions required for zpn client
 * functionality.
 *
 * A proxy could use these routines in order to leverage zpn
 * functionality.
 *
 * FYI, zc_ comes from zpn_client_
 *
 */

#ifndef _ZPN_CLIENT_H
#define _ZPN_CLIENT_H

#include <stdint.h>
#include <openssl/ssl.h>

#include <event2/event.h>
#include <event2/listener.h>
#include <event2/bufferevent_ssl.h>
#include <event2/thread.h>
#include <event2/buffer.h>
#include "zpn/zpn_mconn_fohh_tlv.h"
#include "zpn/zpn_mconn_zrdt_tlv.h"
#include "zpn/zpn_mconn_udp_tlv.h"
#include "zpn/zpn_mconn.h"
#include "zpn/zpn_mconn_bufferevent.h"
#include "zpn/zpn_mconn_udp_bufferevent.h"
#include "argo/argo.h"

#include "ztlv/zpn_tlv.h"

#define MAX_NUMBER_OF_CLIENTS 100

/*
 * Note: 'zc_' is short for zpn_client... sometimed I'm lazy.
 */


/* The state associated with one TLS connection to a broker: */
struct zc_state;


/* The state associated with one microtunnel within one TLS connection
 * to a broker: */
struct zc_mtunnel;


/* Status of zc_state: */
enum zc_status {
    zc_ready,          /* Client is ready for use. */
    zc_authenticate,   /* Client is almost ready, but needs
                        * authentication */
    zc_not_connected,  /* Client is currently not connected to
                        * a broker, but will periodically try
                        * to reconnect. */
    zc_cert_error,     /* A problem with the client
                        * certificate. No new connection
                        * attempts will be made. */
    zc_error,          /* Connection attempt resulted in some
                        * unrecoverable error. No new
                        * connection attempts will be made */
    zc_invalid,        /* Some nasty invalid state */
};


/* Status of zc_mtunnel's */
enum zc_mtunnel_status {
    zc_mtunnel_init = 0,   /* Tunnel is initializing/being
                            * created. */
    zc_mtunnel_creating,   /* Tunnel being created */
    zc_mtunnel_ready,      /* Tunnel is ready. */
    zc_mtunnel_closed,     /* Tunnel is closing. */
};

/*
 * Posture profile in the format
 *     "id_str": "5a3a5ea5-8566-49e7-bed9-de6bb5b1f5ed", "matches": 1
 * being send to the broker
 */
struct zc_posture_profile {
    char *udid;
    int matches;
};
/*
 * configurations in json
 */
#define MAX_NUMMBER_SRV_GRP 5
#define MAX_NUMMBER_SIPA_USERS 10
struct sipa_test {
    // as mtunnel_request
    char *o_identity_name;
    int64_t o_user_id;                 /* _ARGO: integer */
    int64_t o_location_id;             /* _ARGO: integer */
    // parent location id.
    int64_t o_plocation_id;
    const char* app_type;

    int64_t server_group_gid[MAX_NUMMBER_SRV_GRP];
    size_t server_group_gid_count;
};
struct json_cfg {
    const char *config_json_filename;

    // sipa_tests
    struct sipa_test sp_test[MAX_NUMMBER_SIPA_USERS];
    size_t sp_test_count;
    size_t sp_current_test;

    // Workload tags
    uint32_t *o_dwgs;
    int o_dwgs_count;

    // config lock
    pthread_mutex_t lock;

    unsigned create_http:1;
};
int parse_json_cfg(struct json_cfg *cfg);
int process_json_cfg(struct json_cfg *cfg);
void print_json_cfg(struct json_cfg *cfg);
void add_sipa_test_to_mtunel(struct zpn_mtunnel_request *data, struct json_cfg *cfg);
#define MAX_LISTEN 50

typedef int (zc_generic_tcp_request_cb)(struct bufferevent *bev,
                                        uint16_t listening_port,
                                        int fohh_thread_id);

struct zpn_generic_tcp_listener {
    struct evconnlistener *ev_listener;
    int fohh_thread_id;
    uint16_t listening_port;
    struct zc_state *zc;
    zc_generic_tcp_request_cb   *request_cb_generic_tcp;
};

TAILQ_HEAD(zc_mtunnel_head_tailq, zc_mtunnel);
ZTAILQ_HEAD(zc_application_head_tailq, zc_application);
typedef int
(zc_app_create_callback)(const char *domain_name, int tcp_port, int udp_port, int double_encrypt);


/*
 * Callback called as zc_state status changes.
 */
typedef int
(zc_state_callback_f)(struct zc_state *state,
                      void *cookie_void,
                      int64_t cookie_int,
                      enum zc_status status,
                      const char *error_string);

struct zc_state {

    pthread_mutex_t lock;

    zc_state_callback_f *state_cb;
    int64_t cookie_int;
    void *cookie_void;

    char *broker_name;
    char *saml_assertion;

    char *hw_id;
    char *cpu_id;
    char *storage_id;
    char *login_name;
    const char *client_fqdn;

    const char *ca_filename;
    const char *cert_filename;
    const char *key_filename;

    const char *customer_domain;
    const char *cloud_name;
    const char *platform;
    const char *sni;
    const char *pub_ip;

    unsigned version_acked:1;
    unsigned authenticated:1;
    unsigned connected:1;

    char **capabilities;
    int capabilities_count;

    int passive;

    // SME/SIPA
    int64_t orgid;
    int64_t o_user_id;
    int64_t o_location_id;
    int64_t o_plocation_id;

    u_int16_t si_device_trust;
    int si_device_id;
    char *o_identity_name;
    char *external_device_id;

    struct argo_inet *o_sip;
    struct argo_inet *o_dip;
    int32_t o_sport;
    int32_t o_dport;

    int64_t customer_gid;

    enum zc_status status;

    int32_t next_id;

    enum zpn_tlv_type tlv_type;

    struct fohh_connection *broker_fohh;
    struct zpn_fohh_tlv broker_tlv_state;
    struct zpn_zrdt_tlv broker_zrdt_tlv_state;

    struct zpn_udp_tlv_head udp_tlv_list;
    struct zpn_icmp_tlv *icmp_tlv_state;

    struct zc_mtunnel_head_tailq mtunnels;
    struct zc_mtunnel_head_tailq reaped;

    int64_t num_cbi_reauth_required;

    zc_app_create_callback  *app_create;

    /* Need to make this a list to allow listening on multiple ports */
    struct zpn_generic_tcp_listener tcp_listen[MAX_LISTEN];
    int tcp_listen_count;
    enum zpn_client_type client_type;
    struct zc_application_head_tailq    apps;

    struct diamond_state *domain_lookup;
    struct zradix_tree *ipv4_lookup;
    struct zradix_tree *ipv6_lookup;

    int fohh_thread_id;

    struct argo_hash_table *mtunnels_by_tag;

    char **dns_search_suffix;
    int dns_search_suffix_count;

    struct zhash_table *dns_suffix_callback_hash;  /* hash table to map dns check request id to dns_search_suffix_outstanding_check */
    zpath_mutex_t dns_suffix_callback_hash_lock;

    struct zc_posture_profile **posture_list;

    const char *username;
    int64_t last_auth_time;

    struct zhash_table *outstanding_app_check_hash;
    zpath_mutex_t outstanding_app_check_hash_lock;

    int64_t app_download_start_us;
    int64_t number_of_apps_downloaded;

    int64_t zradix_ipv4_cnt;  // no of elements in ipv4 radix
    int64_t zradix_ipv6_cnt;  // no of elements in ipv6 radix

    int64_t number_of_apps_deleted;
    int64_t number_of_apps_bypassed;
};
// for outstanding_app_check_hash
struct outstanding_app_check {
    char *lookup_domain;
    // for mtunnel req
    struct bufferevent *bev;
    int fohh_thread_id;
    int port;
    int double_encrypt;
};

struct zpn_tlv *zc_get_tlv(struct zc_state *zc);
/*
 * Callback called as zc_state status changes.
 */
typedef int
(zc_state_callback_f)(struct zc_state *state,
                      void *cookie_void,
                      int64_t cookie_int,
                      enum zc_status status,
                      const char *error_string);

/*
 * Callback made when mtunnel status changes. If the status change is
 * the result of some processing error, then 'error_string' will
 * contain a least a little explanation.
 */
typedef int
(zc_mtunnel_status_callback_f)(struct zc_mtunnel *mtunnel,
                               void *cookie_void,
                               int64_t cookie_int,
                               enum zc_mtunnel_status status,
                               const char *error_string);

typedef int
(zc_app_create_callback)(const char *domain_name, int tcp_port, int udp_port, int double_encrypt);

const char *zc_status_string(enum zc_status status);
const char *zc_mtunnel_status_string(enum zc_mtunnel_status status);


/*
 * Create a zpn client.
 *
 * broker_name - generally broker.[cloud]. (broker.dev.zpath.net,
 *      etc). At some point in the future, this might become
 *      [customerdomain].broker.[cloud] in order to support private
 *      brokers. But not yet.
 *
 * customer_domain - The customer domain. 'chanak.com', for
 *      example. Should be all lower-case.
 *
 * saml_assertion_xml - SAML assertion to use to authenticate the
 *      user, in XML form. (NOT base-64 saml)
 *
 * hw_fingerprint - The same HW fingerprint used during
 *      provisioning. Used as an exact comparison with the
 *      provisioning fingerprint.
 *
 * client_certificate_pem - PEM format client certificate. This is the
 *      certificate that was created for this client during
 *      provisioning. Should include intermediates.
 *
 * client_key_pem - The key to go along with the client certificate.
 *
 * client_root_pem - The root certificate/signing authority of our
 *      client certificate. This root certificate is used to validate
 *      peers when doing triple-encryption between client and
 *      assistant.
 *
 * cloud_root_pem - The root certificate for cloud services-
 *      i.e. 'dev.zpath.net.crt' or equivalent. Note this is generally
 *      tied to the cloud.
 *
 * callback - Callback that is called whenever state changes.
 *      (connected, authenticating, etc) This can be left NULL if you
 *      prefer to poll state.
 *
 * cookie_void - void* cookie passed back with callback.
 *
 * cookie_int - 64 bit int cookie passed back with callback.
 *
 * debug - if set, turns on full TLV debugging
 *
 * The initial client state is zc_not_connected, and no
 * initial callback is made for this initial state. The first callback
 * is (common case) zc_ready, but any status is possible on
 * first callback.
 */
struct zc_state *zc_state_create(const char *broker_name,
                                 const char *customer_domain,
                                 const char *saml_assertion_xml,
                                 const char *client_certificate_pem_filename,
                                 const char *client_key_pem_filename,
                                 const char *cloud_root_pem_filename,
                                 zc_state_callback_f callback,
                                 void *cookie_void,
                                 int64_t cookie_int,
                                 int debug,
                                 char **capabilities,
                                 int capabilities_count,
                                 int64_t customer_gid,
                                 int64_t orgid,
                                 int64_t o_location_id,
                                 int64_t o_plocation_id,
                                 char *o_identity_name,
                                 int64_t o_user_id,
                                 char* external_device_id,
                                 const char *hw_id,
                                 const char *cpu_id,
                                 const char *storage_id,
                                 const char *login_name,
                                 const char *cloud_name,
                                 const char *platform,
                                 const char *sni,
                                 const char *pub_ip,
                                 const char *client_fqdn,
                                 enum zpn_client_type client_type,
                                 enum zpn_tlv_type tlv_type,
                                 int passive,
                                 const char *username,
                                 int64_t last_auth_timestamp);

/*
 * Destroy zpn client state.
 *
 * Destruction of the client state causes a series of synchronous
 * zc_mtunnel_status_callback_f callbacks on all mtunnels owned by
 * this client.
 *
 * No callbacks for any mtunnels of this client or for the client
 * itself will occur after this call RETURNS.
 */
int zc_state_destroy(struct zc_state *state);

/*
 * Ask the broker whether or not the requested domain is available via
 * ZPN
 */
int zc_state_dns_check(struct zc_state *state,
                       char *domain,
                       char *type);

/*
 * Ask the broker whether or not the requested domain is available via
 * ZPN
 */
int zc_state_app_check(struct zc_state *state,
                       char *domain,
                       char *type,
                       uint8_t strict);
/*
 * Check to see if the specified app should be available for
 * service. The domain should be all lower-case. This is *NOT* a 'best
 * match' lookup. i.e. lookup for 'prefix.service.company.com' will
 * result in NOT_FOUND if the only app available in the company is
 * 'service.company.com'.
 *
 * This is a synchronous call- the set of available apps is cached
 * during authentication and is maintained up-to-date by the client.
 *
 * The TCP ports used for the application are returned via
 * port_pairs_he in PAIRS representing a range. port_pairs_he are all
 * host endian ports.
 *
 * An app using a single port (i.e. 80) would return [80, 80], for
 * example.
 *
 * An app using port 80 and ports 1000 through 1015 inclusive would
 * return [80, 80, 1000, 1015]
 *
 * port_pairs_count specifies how many PAIRS can fit in port_pairs_he.
 * (i.e. port_pairs_count of 10 has room for 20 16 bit integers in
 * port_pairs_he)
 *
 * returns:
 *
 * ZPN_RESULT_BAD_STATE - If we are not connected/authenticated.
 * ZPN_RESULT_NOT_FOUND - If the specified domain is not available.
 * ZPN_RESULT_NO_ERROR - If the specified domain is available.
 */
int zc_state_lookup_app(struct zc_state *state,
                        const char *domain,
                        uint16_t *port_pairs_he,
                        size_t *port_pairs_count);

/*
 * Get the current status of the client.
 *
 * Note that no lock is held after returning from this call, so the
 * client status may change by the time you are able to do anything
 * based on the prior status. Sometimes writing software is hard.
 */
enum zc_status zc_state_get_status(const struct zc_state *state);


/*
 * Create a microtunnel on the specified zc_state.
 *
 * bev is a connected libevent bufferevent. (i.e. a socket +
 * bufferevent) If you want to attach a simple socket instead, just
 * make a bufferevent for the socket...
 *
 * Once handed off, the callbacks for bev will be overwritten by
 * zc_state.
 */
struct zc_mtunnel *
zc_mtunnel_create(struct zc_state *zc,
                  void *owner, // struct bufferevent *bev,
                  const char *application_name,
                  uint16_t ip_proto,
                  uint16_t port_he,
                  int fohh_thread_id,
                  int double_encrypt,
                  zc_mtunnel_status_callback_f *state_cb,
                  struct bufferevent *state_cb_cookie_void,
                  int64_t state_cb_cookie_int);

/*
 * Terminate a microtunnel.
 *
 * Once this routine is called, then no further mtunnel callbacks will
 * ever occur.
 *
 * terminate_fast is an indication that even buffered data is to be
 * tossed.
 */
int mtunnel_terminate(struct zc_mtunnel *mtunnel,
                         int terminate_fast,
                         const char *err);

enum zc_mtunnel_status zc_mtunnel_get_status(struct zc_mtunnel *mtunnel);

int zc_inner_tunnel_ssl_ctx_init(const char *root_pem_filename,
                                 const char *certificate_pem_filename,
                                 const char *key_pem_filename);

typedef int (zc_udp_request_cb)(struct sockaddr_storage addr,
                                uint16_t listening_port,
                                int fohh_thread_id);



int zc_udp_server_listen(struct zc_state *zc,
                         struct argo_inet *inet,
                         uint16_t port_he,
                         int fohh_thread_id,
                         zc_udp_request_cb *request_cb);

int zc_generic_tcp_listen(struct zc_state *zc,
                         struct argo_inet *inet,
                         uint16_t port_he,
                         int fohh_thread_id,
                         zc_generic_tcp_request_cb *request_cb);

int zc_state_register_app_create_callback(struct zc_state *zc, zc_app_create_callback *app_create_callback);

struct zpn_client_app *zc_is_registered_app(struct zc_state *zc, const char *domain, int16_t port);

int zpn_client_register_client_broker_app(struct zc_state *client, const char *app_domain);

/*
 * Set global capability
 */
void zpn_clientd_set_capability(const char *capability) ;

/*
 * @startuml
 * za_request_received : setup connection to the server app
 * za_server_conn_open : setup connection to data_broker
 * za_broker_conn_open : tx zpn_mtunnel_bind msg to data_broker
 *
 * [*] --> za_request_received : rx zpn_broker_request from control_broker
 * za_request_received --> za_server_conn_open  : connection with server app is done
 * za_server_conn_open --> za_broker_conn_open : connection with data_broker is done
 * za_broker_conn_open --> za_complete : data_broker ACKed the connection
 *
 * @enduml
 */
enum zpn_assistant_mtunnel_state {
    za_request_received = 0,
    za_server_conn_open,
    za_broker_conn_open,
    za_complete,
    za_reaping,
    za_free
};

/*
 * The mtunnels known by this assistant.
 * g_aps -> gid of application server
 * path_decision -> flag of path decision taken at differnt legs in the system.
 */
struct zpn_assistant_mtunnel {
    int self_type; // 2
    /* Everyone needs a bucket list */
    TAILQ_ENTRY(zpn_assistant_mtunnel) bucket_list;
    TAILQ_ENTRY(zpn_assistant_mtunnel) broker_list;

    uint64_t mtunnel_id_hash;
    enum zpn_assistant_mtunnel_state state;
    uint64_t start_us;
    uint64_t server_connection_setup_start_us;
    uint64_t server_connection_setup_end_us;

    /* The following fields mirror the broker_request: */
    char *mtunnel_id;
    int64_t g_brk;
    int64_t g_bfw;
    int64_t g_cst;
    int64_t g_app;
    int64_t g_aps;
    int64_t g_ast;
    int64_t g_dsp;
    int64_t g_app_grp;
    int64_t g_ast_grp;
    int64_t g_srv_grp;
    struct argo_inet brk_req_server_inet;
    int64_t brk_req_bfw_us;
    int64_t brk_req_dsp_us;
    int64_t brk_req_ast_rx_us;
    int64_t bind_tx_cloud_us;
    char brk_name[256];
    char domain[256];
    char user_id_str[FOHH_MAX_NAMELEN];

    uint16_t client_port_he;
    uint16_t ip_protocol;
    enum zpn_mtunnel_type mtunnel_type;

    /* End of fields mirroring broker_request */

    /*
     * brk_req_server_inet will be equal to server_inet if connector chooses the same IP that dispatcher wished for.
     * But that is not always the case.
     */
    struct argo_inet server_inet;
    uint16_t server_port_he;
    int64_t  server_rtt_us;
    struct argo_inet assistant_inet;
    uint16_t assistant_port_he;

    struct zc_state *broker;
    int32_t broker_tag;

    char *err;

    struct zpn_mconn_bufferevent mconn_bufferevent_server;
    struct zpn_mconn_udp_bufferevent mconn_udp_server;
    struct zpn_mconn_fohh_tlv mconn_fohh_tlv_broker;
    struct zpn_mconn_zrdt_tlv mconn_zrdt_tlv_broker;

    enum zpn_tlv_type tlv_type;

    pthread_mutex_t lock;
    int termination_started;

    struct zpn_trans_log log;

    int double_encrypt;
    struct zpn_connector *connector;
    int64_t delayed_fin_forward_us;              /* Delay forwarding FIN from broker to server */

    /* Temporarily store bev before it is assigned to local owner */
    struct bufferevent *bev_tmp;

    int64_t incarnation;

    int32_t brkreq_seq_num;

    uint64_t path_decision;
    int lss_encrypted_sock;

    SSL *lss_encrypted_ssl;

    /* Error state from openssl. This is a bit big, but worth it. */
    struct fohh_ssl_status lss_encrypted_ssl_status;

    struct {
        /* Connect status flags */
        unsigned connect_requested:1;
        unsigned connect_success:1;
        unsigned connect_timeout:1;
        unsigned connect_eof:1;
        unsigned connect_error:1;
        /* It is muted health (aka no-health report based application) if the server ip is not valid. */
        unsigned is_muted_health_app:1;
        /* did user broker send the broker request directly, bypassing the dispatcher? */
        unsigned dsp_bypassed :1;
        unsigned broker_bind_sent:1;
        unsigned broker_end_done:1;
        unsigned in_broker_list:1;
        unsigned is_terminating_due_to_data_conn_lost:1;
    } flag;
    int allow_all_xport;
    int64_t free_time_s;
};
struct zpn_assistant_mtunnel *zc_mtunnel_allocate_and_bucket_lock(const char *mtunnel_id,
                                                                  uint64_t mtunnel_id_hash,
                                                                  char **err,
                                                                  uint16_t ip_protocol);

struct zc_mtunnel {
    int self_type; //1
    TAILQ_ENTRY(zc_mtunnel) zc_list;

    struct zc_state *state;

    zc_mtunnel_status_callback_f *mtunnel_cb;
    void *cookie_void;
    int64_t cookie_int;

    struct zpn_mconn_bufferevent mconn_bufferevent;
    struct zpn_mconn_udp_tlv mconn_udp_tlv;
    struct zpn_mconn_fohh_tlv mconn_fohh_tlv;
    struct zpn_mconn_zrdt_tlv mconn_zrdt_tlv;
    enum zpn_tlv_type tlv_type;

    struct bufferevent *bev; /* client side bufferevent */

    unsigned attached_to_client : 1;
    unsigned attached_to_server : 1;

    /* The tag that we have. Only valid when non-zero. */
    int32_t tag_id;

    enum zc_mtunnel_status status;

    char *application_name;
    uint16_t ip_proto;
    uint16_t port_he;
    char *mtunnel_id;

    pthread_mutex_t lock;

    struct zpn_trans_log log;

    char c_rxbytes_str[128]; /* bytes received from client */
    char c_txbytes_str[128]; /* bytes sent to client */
    char a_rxbytes_str[128]; /* bytes received from assistant */
    char a_txbytes_str[128]; /* bytes sent to assistant */

    uint64_t double_encrypt;
    struct zpn_connector *connector;

    struct zpn_udp_tlv *udp_tlv_state;

    int64_t incarnation;

    zc_mtunnel_status_callback_f *state_cb;
    void *state_cb_cookie_void;
    int64_t state_cb_cookie_int;
};
int zpn_client_search_apps(struct zc_state *zc, const char *domain, int port, int protocol, int *is_double_encrypt);

void dump_structure(struct argo_structure_description *description,
                    void *data,
                    const char *label,
                    const char *func,
                    const char *file,
                    int line);

#define DUMP_STRUCT(desc, struct, label) dump_structure(desc, struct, label, __FUNCTION__, __FILE__, __LINE__);


#endif /* _ZPN_CLIENT_H */
