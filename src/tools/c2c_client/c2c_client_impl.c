/*
 * zpn_client.c. Copyright (C) 2014 Zscaler Inc. All Rights Reserved
 */

#include "argo/argo_hash.h"
#include "fohh/fohh.h"
#include "fohh/fohh_http.h"
#include "fohh/fohh_resolver.h"
#include "zpath_misc/zpath_version.h"
#include <fcntl.h>
#include "zpn/zpn_lib.h"
#include <arpa/inet.h>
#include <sys/socket.h>

#include "zpn/zpn_rpc.h"
#include "zpn/zpn_mconn.h"
#include "zpn/zpn_mconn_bufferevent.h"

#include "zpn/zpn_fohh_worker.h"
#include "zpn/zpn_connector_ssl_helpers.h"
#include "zpn/zpn_connector_tun.h"
#include "zpn/zpn_zrdt_debug.h"

#include "tools/c2c_client/c2c_data.h"

#include "tools/c2c_client/c2c_client_impl.h"
#include "tools/c2c_client/c2c_brk_request.h"
#include "tools/c2c_client/c2c_mtunnel.h"
#include "fohh/fohh_private.h"
#include "zradix/zradix.h"
#include "zpn/zpn_application.h"


int number_of_apps_recv = 0;
int backoff_max_s = 5;

static SSL_CTX *ssl_ctx_connector = NULL;
extern int number_of_apps_matched;
extern struct evbuffer *udp_packetize(char *buf, ssize_t len, uint16_t src, uint16_t dst);

extern u_int32_t si_device_id;
extern u_int16_t si_device_trust;
extern int send_trusted_nets;
extern char* zia_cloud;
extern int no_zia_identity;
extern enum zpn_client_type g_client_type;
extern int g_dns_strict;
extern struct json_cfg g_cfg ;

int zc_mtunnel_terminate(struct zc_state *zc, struct zc_mtunnel *c_mt, int terminate_fast, const char *err);
void log_msg_time(const char* msg, const char* extra, int reset_timer);
static int zc_mtunnel_state_machine(struct zc_mtunnel *mt);
void zc_mtunnel_locked_state_machine(struct zpn_assistant_mtunnel *mtunnel);

struct zc_state *zc_st[MAX_NUMBER_OF_CLIENTS];
int zc_st_next = 0;
extern uint16_t port_offset_local;
extern struct zc_state *g_zc;

struct zc_application {
    ZTAILQ_ENTRY(zc_application) entry;
    struct zpn_client_app app;
    enum zpn_probe_type zpn_probe_type;
    struct argo_object *app_object;
};

int generic_status_bev(struct zc_mtunnel *mtunnel,
                       void *cookie_void,
                       int64_t cookie_int,
                       enum zc_mtunnel_status status,
                       const char *error_string);

int zpn_client_mtunnel_log(struct zc_mtunnel *mtunnel);

void log_msg_time(const char* msg, const char* extra, int reset_timer);

static int global_bind_client_cb(void *mconn_base,
                                 void *mconn_self,
                                 void *global_owner,
                                 void *global_owner_key,
                                 size_t global_owner_key_length,
                                 int64_t *global_owner_incarnation);
static int global_unbind_client_cb(void *mconn_base,
                                   void *mconn_self,
                                   void *global_owner,
                                   void *global_owner_key,
                                   size_t global_owner_key_length,
                                   int64_t global_owner_incarnation,
                                   int drop_buffered_data,
                                   int dont_propagate,
                                   const char *err);
static int global_bind_server_cb(void *mconn_base,
                                 void *mconn_self,
                                 void *global_owner,
                                 void *global_owner_key,
                                 size_t global_owner_key_length,
                                 int64_t *global_owner_incarnation);
static int global_unbind_server_cb(void *mconn_base,
                                   void *mconn_self,
                                   void *global_owner,
                                   void *global_owner_key,
                                   size_t global_owner_key_length,
                                   int64_t global_owner_incarnation,
                                   int drop_buffered_data,
                                   int dont_propagate,
                                   const char *err);
static void global_lock_cb(void *mconn_base,
                           void *mconn_self,
                           void *global_owner,
                           void *global_owner_key,
                           size_t global_owner_key_length);
static void global_unlock_cb(void *mconn_base,
                             void *mconn_self,
                             void *global_owner,
                             void *global_owner_key,
                             size_t global_owner_key_length);

static void global_terminate_cb(void *mconn_base,
                                void *mconn_self,
                                void *global_owner,
                                void *global_owner_key,
                                size_t global_owner_key_length,
                                char *error);

static int global_ip_proto_cb(void *mconn_base,
                              void *mconn_self,
                              void *global_owner,
                              void *global_owner_key,
                              size_t global_owner_key_length);

static int global_double_encrypt_cb(void *mconn_base,
                                    void *mconn_self,
                                    void *global_owner,
                                    void *global_owner_key,
                                    size_t global_owner_key_length);

static struct zpn_mconn *global_outer_mconn_cb(void *mconn_base,
                                               void *mconn_self,
                                               void *global_owner,
                                               void *global_owner_key,
                                               size_t global_owner_key_length);

static int64_t global_incarnation_cb(void *mconn_base,
                                     void *mconn_self,
                                     void *global_owner,
                                     void *global_owner_key,
                                     size_t global_owner_key_length);

static int global_validate_incarnation_cb(void *mconn_base,
                                          void *mconn_self,
                                          void *global_owner,
                                          void *global_owner_key,
                                          size_t global_owner_key_length,
                                          int64_t original_incarnation);

static char *global_mtunnel_id(void *mconn_base,
                               void *mconn_self,
                               void *global_owner,
                               void *global_owner_key,
                               size_t global_owner_key_length);

int zpn_client_mtunnel_log(struct zc_mtunnel *mtunnel);
static int zc_send_trusted_networks(struct zc_state *zc);

static const struct zpn_mconn_global_owner_calls client_global_call_set = {
        global_bind_client_cb,    global_unbind_client_cb,
        global_lock_cb,           global_unlock_cb,
        global_terminate_cb,      global_ip_proto_cb,
        global_double_encrypt_cb, global_outer_mconn_cb,
        global_incarnation_cb,    global_validate_incarnation_cb,
        global_mtunnel_id,        global_get_peer_no_op,
        global_get_customer_gid_no_op};

static const struct zpn_mconn_global_owner_calls server_global_call_set = {
        global_bind_server_cb,    global_unbind_server_cb,
        global_lock_cb,           global_unlock_cb,
        global_terminate_cb,      global_ip_proto_cb,
        global_double_encrypt_cb, global_outer_mconn_cb,
        global_incarnation_cb,    global_validate_incarnation_cb,
        global_mtunnel_id,        global_get_peer_no_op,
        global_get_customer_gid_no_op};

const char *zc_status_string(enum zc_status status) {
    switch (status) {
    case zc_ready:
        return "zc_ready";
    case zc_authenticate:
        return "zc_authenticating";
    case zc_not_connected:
        return "zc_not_connected";
    case zc_cert_error:
        return "zc_cert_error";
    case zc_error:
        return "zc_error";
    default:
        return "ERROR";
    }
}

const char *zc_mtunnel_status_string(enum zc_mtunnel_status status) {
    switch (status) {
    case zc_mtunnel_init:
        return "zc_mtunnel_init";
    case zc_mtunnel_ready:
        return "zc_mtunnel_ready";
    case zc_mtunnel_closed:
        return "zc_mtunnel_closed";
    case zc_mtunnel_creating:
        return "zc_mtunnel_creating";
    default:
        return "ERROR";
    }
}

void dump_structure(struct argo_structure_description *description,
                    void *data,
                    const char *label,
                    const char *func,
                    const char *file,
                    int line) {
    struct argo_object *object = argo_object_create(description, data);

    char dump[2000] = {0};
    int res = argo_object_dump(object, dump, sizeof(dump), NULL, 0);
    if (res == ARGO_RESULT_NO_ERROR || res == ARGO_RESULT_ERR_TOO_LARGE) {
        argo_log_text(zpn_event_collection, argo_log_priority_debug, "c2c", file, func, line, "%s %s", label, dump);
    } else {
        ZPN_LOG(AL_ERROR, "dump_structure failed '%s'", zpn_result_string(res));
    }

    argo_object_release(object);
}
void dump_argo(struct argo_object *object,
               struct zpn_tlv *tlv,
               const char *label,
               const char *label2,
               const char *func,
               const char *file,
               int line) {
    if (object) {
        char buf[4000];
        if (argo_object_dump(object, buf, sizeof(buf), NULL, Z_FALSE) == ARGO_RESULT_NO_ERROR) {
            argo_log_text(zpn_event_collection,
                          argo_log_priority_debug,
                          "client",
                          file,
                          func,
                          line,
                          "%s %s %s (%s)",
                          label ?: "",
                          label2 ?: "",
                          buf,
                          tlv ? zpn_tlv_description(tlv) : "");
        }
    }
}
#define DUMP_ARGO(obj, tlv, label) dump_argo(obj, tlv, label, NULL, __FUNCTION__, __FILE__, __LINE__)

static void zc_mtunnel_update_state(struct zc_mtunnel *c_mt, enum zc_mtunnel_status status, const char *err) {
    if (c_mt->status == status)
        return;
    c_mt->status = status;
    if (c_mt->state_cb) {
        (c_mt->state_cb)(c_mt, c_mt->state_cb_cookie_void, c_mt->state_cb_cookie_int, c_mt->status, err);
    } else {
        ZPN_LOG(AL_DEBUG, "zc_mtunnel_update_state: %s %s", zc_mtunnel_status_string(status), err ? err : "");
    }
}

static struct zpn_mconn_udp_tlv *zc_mconn_udp_tlv_lookup(struct zpn_udp_tlv *udp_tlv, struct sockaddr_storage addr) {
    struct zpn_mconn_udp_tlv *res;
    pthread_mutex_lock(&(udp_tlv->lock));
    res = argo_hash_lookup(udp_tlv->sockaddr, &addr, sizeof(addr), NULL);
    pthread_mutex_unlock(&(udp_tlv->lock));
    return res;
}

struct zpn_tlv *zc_get_tlv(struct zc_state *zc) {
    if (zc->tlv_type == zpn_fohh_tlv) {
        return &(zc->broker_tlv_state.tlv);
    } else {
        return &(zc->broker_zrdt_tlv_state.tlv);
    }
}

static int zpn_client_mtunnel_clean(struct zc_mtunnel *mt) {
    if (mt->ip_proto == IPPROTO_UDP) {
        if (!zpn_mconn_udp_tlv_clean(&(mt->mconn_udp_tlv))) {
            return 0;
        }
    } else {
        if (!zpn_mconn_bufferevent_clean(&(mt->mconn_bufferevent))) {
            return 0;
        }
    }

    if (!zpn_mconn_fohh_tlv_clean(&(mt->mconn_fohh_tlv))) {
        return 0;
    }

    return 1;
}
/* non-static for ut*/
void zpn_client_app_lookup_and_remove_locked(struct zc_state *zc,
                                             struct argo_object *object,
                                             char *domain,
                                             size_t domain_len,
                                             int wildcard,
                                             struct argo_inet *inet,
                                             int is_inet) {
    struct zpn_client_app *app = object->base_structure_void;
    struct zc_application *deleteme;
    int res;

    /*
     * 1. Lookup and delete existing app
     *
     * 2. If new app is not deleted, add it.
     */
    deleteme = NULL;
    if (is_inet) {
        if (zpn_is_ip_address_valid(0, inet, domain) != ZPATH_RESULT_NO_ERROR) {
            ZCLIENT_LOG(AL_WARNING, "Invalid netmask on address <%s>", domain);
        } else {
            if (inet->length == 4) {
                res = zradix_lookup(zc->ipv4_lookup, inet->address, inet->netmask, (void **)&deleteme);
            } else {
                res = zradix_lookup(zc->ipv6_lookup, inet->address, inet->netmask, (void **)&deleteme);
            }
            if (res == ZRADIX_RESULT_NO_ERROR) {
                if (inet->length == 4) {
                    res = zradix_remove(zc->ipv4_lookup, inet->address, inet->netmask, NULL);
                    if (!res)
                        zc->zradix_ipv4_cnt--;
                } else {
                    res = zradix_remove(zc->ipv6_lookup, inet->address, inet->netmask, NULL);
                    if (!res)
                        zc->zradix_ipv6_cnt--;
                }
                if (res != ZRADIX_RESULT_NO_ERROR) {
                    /* Not found on remove */
                    ZCLIENT_LOG(AL_WARNING, "Radix remove application: failed %s (IP)", domain);
                }
            }
        }
    } else {
        /* Always remove existing app, if it is there. Only add back non-deleted entries. */
        domain_len = strlen(domain);
        deleteme = diamond_lookup(zc->domain_lookup, (const uint8_t *)domain, domain_len, wildcard, 0, 0, 0, NULL);
        if (deleteme) {
            // ZCLIENT_LOG(AL_NOTICE, "Removing <%.*s> from diamond", (int) domain_len, domain);
            res = diamond_remove(zc->domain_lookup, (const uint8_t *)domain, domain_len, wildcard, 0, 0, 0, NULL);
            if (res) {
                ZCLIENT_LOG(AL_ERROR, "Could not remove deleted client app for domain <%s>", app->app_domain);
            } else {
            }
        }
    }
    if (deleteme) {
        /* Remove from linked list as well, and free. */
        ZTAILQ_REMOVE(&(zc->apps), deleteme, entry);
        argo_object_release(deleteme->app_object);
        ZPN_FREE(deleteme);
    }
}
static int zpn_client_mtunnel_done(struct zc_mtunnel *mt) {
    if (mt->ip_proto == IPPROTO_UDP) {
        if (zpn_mconn_udp_tlv_done(&mt->mconn_udp_tlv)) {
            if (mt->tlv_type == zpn_fohh_tlv) {
                if (zpn_mconn_done(&mt->mconn_fohh_tlv.mconn)) {
                    return 1;
                }
            } else {
                if (zpn_mconn_done(&mt->mconn_zrdt_tlv.mconn)) {
                    return 1;
                }
            }
        }
    } else {
        if (zpn_mconn_bufferevent_done(&mt->mconn_bufferevent)) {
            if (mt->tlv_type == zpn_fohh_tlv) {
                if (zpn_mconn_done(&mt->mconn_fohh_tlv.mconn)) {
                    return 1;
                }
            } else {
                if (zpn_mconn_done(&mt->mconn_zrdt_tlv.mconn)) {
                    return 1;
                }
            }
        }
    }

    return 0;
}

void zpn_client_mtunnel_internal_display(struct zc_mtunnel *mt) {
    if (zpn_debug_get(ZPN_DEBUG_CLIENT_TIMER_IDX)) {
        ZPN_LOG(AL_DEBUG, "tag_id = %d", mt->tag_id);
        ZPN_LOG(AL_DEBUG, "broker_mconn -----");

        if (mt->tlv_type == zpn_fohh_tlv) {
            zpn_mconn_fohh_tlv_internal_display(&(mt->mconn_fohh_tlv));
        } else {
            zpn_mconn_zrdt_tlv_internal_display(&(mt->mconn_zrdt_tlv));
        }
        ZPN_LOG(AL_DEBUG, "client_mconn -----");
        if (mt->ip_proto == IPPROTO_UDP) {
            zpn_mconn_udp_tlv_internal_display(&(mt->mconn_udp_tlv));
        } else {
            zpn_mconn_bufferevent_internal_display(&(mt->mconn_bufferevent));
        }

        if (mt->connector) {
            ZPN_LOG(AL_DEBUG, "tunnel mconn_s -----");
            zpn_mconn_bufferevent_internal_display(&(((struct zpn_connector_tun *)mt->connector)->mconn_s));

            ZPN_LOG(AL_DEBUG, "tunnel mconn_c -----");
            zpn_mconn_bufferevent_internal_display(&(((struct zpn_connector_tun *)mt->connector)->mconn_c));
        }
    }
}

static void zpn_client_mt_stream_touch(struct zc_mtunnel *mt) {
    if (mt->tlv_type == zpn_zrdt_tlv) {
        zpn_mconn_zrdt_tlv_stream_touch(&(mt->mconn_zrdt_tlv));
    }
}

void zpn_client_check_mtunnel() {
    struct zc_mtunnel *mt;
    struct zc_mtunnel *tmp_mt;
    int active_conn = 0;
    int free_conn = 0;
    int freed = 0;

    for (int i = 0; i < MAX_NUMBER_OF_CLIENTS; i++) {
        if(NULL == zc_st[i]) {
            continue;
        }

        mt = TAILQ_FIRST(&(zc_st[i]->mtunnels));
        while (mt) {
            active_conn++;
            tmp_mt = TAILQ_NEXT(mt, zc_list);

            zpn_client_mtunnel_internal_display(mt);
            zpn_client_mt_stream_touch(mt);

            if (zpn_client_mtunnel_done(mt)) {
                ZPN_LOG(AL_DEBUG, "Mtunnel terminated on both ends, should free it");
                zc_mtunnel_terminate(zc_st[i], mt, 0, NULL);
                active_conn--;
            }
            mt = tmp_mt;
        }

        mt = TAILQ_FIRST(&(zc_st[i]->reaped));
        while (mt) {
            zpn_client_mt_stream_touch(mt);
            free_conn++;
            tmp_mt = TAILQ_NEXT(mt, zc_list);

            if (zpn_client_mtunnel_clean(mt)) {
                TAILQ_REMOVE(&(zc_st[i]->reaped), mt, zc_list);

                zpn_client_mtunnel_log(mt);

                if (mt->double_encrypt) {
                    if (mt->connector)
                        zpn_connector_tun_destroy(mt->connector);
                }
                if (mt->mtunnel_id)
                    ZPN_FREE(mt->mtunnel_id);
                if (mt->application_name)
                    ZPN_FREE(mt->application_name);
                ZPN_FREE(mt);

                freed++;
            } else {
                zpn_client_mtunnel_internal_display(mt);
            }
            mt = tmp_mt;
        }
    }
#if 0
    ZPN_DEBUG_MTUNNEL("Client Health: active_conn = %d, free_conn = %d, freed = %d", active_conn, free_conn, freed);
#else
    (void)active_conn;
    (void)free_conn;
    (void)freed;
#endif
}

static void zc_timer_callback(evutil_socket_t sock, short flags, void *cookie) {
    struct fohh_connection *conn = cookie;
    zpn_client_check_mtunnel();
    extern uint16_t no_fohh_status;
    static int calls = 0;

    if (conn && conn->quiet != no_fohh_status && ++calls > 2) {
        ZPN_LOG(AL_INFO, "switch fohh quiet to %d", no_fohh_status);
        conn->quiet = no_fohh_status;
    }
}

static int zc_timer_init(struct event_base *base, struct fohh_connection *conn) {
    struct timeval tv;
    struct event *ev_timer;

    ev_timer = event_new(base, -1, EV_PERSIST, zc_timer_callback, conn);

    if (!ev_timer) {
        ZPN_LOG(AL_ERROR, "Could not create timer");
        return ZPN_RESULT_ERR;
    }

    tv.tv_sec = 5;
    tv.tv_usec = 0;
    if (event_add(ev_timer, &tv)) {
        ZPN_LOG(AL_ERROR, "Could not activate timer");
        return ZPN_RESULT_ERR;
    }

    return ZPN_RESULT_NO_ERROR;
}

static int zc_send_zia_identity(struct zc_state *zc) {
    struct zpn_tlv *tlv;
    struct zpn_zia_identity zia_id = {0};
    tlv = zc_get_tlv(zc);

    zia_id.si_org_id =  zc->orgid;
    zia_id.si_user_id = zc->o_user_id;
    zia_id.si_device_id =  zc->si_device_id;
    zia_id.si_device_trust = zc->si_device_trust;
    zia_id.zia_cloud = zia_cloud;

    if( zpn_debug_get(ZPN_DEBUG_AUTH_IDX))
        dump_structure(zpn_zia_identity_description, &zia_id, "Sn.", __FUNCTION__, __FILE__, __LINE__);

    return tlv_argo_serialize(tlv, zpn_zia_identity_description, &zia_id, zpn_tlv_conn_incarnation(tlv), fohh_queue_element_type_control);
}
static int32_t zc_next_id(struct zc_state *zc) {
    return __sync_fetch_and_add_4(&(zc->next_id), 1);
}

static int zc_drop_all_tunnels(const struct zc_state *zc) {
    ZPN_LOG(AL_CRITICAL, "Implement me");
    return ZPN_RESULT_NO_ERROR;
}

static int zpn_version_ack_cb(void *argo_cookie_ptr, void *argo_structure_cookie_ptr, struct argo_object *object) {
    (void)argo_cookie_ptr;
    struct zc_state *zc = argo_structure_cookie_ptr;
    struct zpn_version_ack *ack = object->base_structure_void;

    log_msg_time("zpn_version_ack",ack->error?"error":"", 0);

    if (ack->error) {
        ZPN_LOG(AL_ERROR, "Version ack error for %s: %s; closing client.", zc->broker_name, ack->error);
        zc_drop_all_tunnels(zc);
    } else {
        ZPN_DEBUG_AUTH("Version check success");
        zc->version_acked = 1;
    }
    return ZPN_RESULT_NO_ERROR;
}

static int zc_zpn_zia_identity_cb(void *argo_cookie_ptr, void *argo_structure_cookie_ptr, struct argo_object *object) {
    (void)argo_cookie_ptr;
    struct zc_state *zc = argo_structure_cookie_ptr;
    log_msg_time("zpn_zia_identity", "", 0);
    if (zpn_debug_get(ZPN_DEBUG_AUTH_IDX)) {
        char dump[8000];
        if (argo_object_dump(object, dump, sizeof(dump), NULL, 0) == ARGO_RESULT_NO_ERROR) {
            ZPN_LOG(AL_DEBUG, "Rx: %s", dump);
        }
    }
    if (no_zia_identity) {
        ZPN_DEBUG_AUTH("-no_zia_identity is set, not sending zpn_zia_identity");
    } else {
        zc_send_zia_identity(zc);
    }
    return ZPN_RESULT_NO_ERROR;
}

static int zc_send_trusted_networks(struct zc_state *zc) {
    if( !send_trusted_nets ) {
        return ZPN_RESULT_NO_ERROR;
    }
    struct zpn_tlv *tlv = zc_get_tlv(zc);
    ZPN_DEBUG_AUTH("Sending zpn_trusted_networks");
    struct zpn_trusted_networks tn = {0};
    tn.trusted_networks_count = 0;
    tn.trusted_networks = NULL;

    return tlv_argo_serialize(tlv, zpn_trusted_networks_description, &tn, 0, fohh_queue_element_type_mission_critical);
}

static int zc_authenticate_ack_cb(void *argo_cookie_ptr, void *argo_structure_cookie_ptr, struct argo_object *object) {
    struct zc_state *zc = argo_structure_cookie_ptr;
    const struct zpn_client_authenticate_ack *ack = object->base_structure_void;

    log_msg_time("zpn_client_authenticate_ack", ack->error ? "error" : "", 0);

    if (zpn_debug_get(ZPN_DEBUG_AUTH_IDX)) {
        char dump[8000];
        if (argo_object_dump(object, dump, sizeof(dump), NULL, 0) == ARGO_RESULT_NO_ERROR) {
            ZPN_LOG(AL_DEBUG, "Rx: %s", dump);
        }
    }
    log_msg_time("zpn_client_authenticate_ack", ack->error ? "error" : "", 0);

    if (ack->error) {
        ZPN_LOG(AL_ERROR, "Authenticate ack error for %s: %s; closing client.", zc->broker_name, ack->error);
        ZPN_LOG(AL_CRITICAL, "Implement me- Authenticate ack error");
    } else {
        zc->authenticated = 1;
        zc->status = zc_ready;


        if (zc->state_cb) {
            (zc->state_cb)(zc, zc->cookie_void, zc->cookie_int, zc->status, NULL);
        }
    }

    return ZPN_RESULT_NO_ERROR;
}

static int zpn_ec_client_authenticate_ack_cb(void *argo_cookie_ptr,
                                             void *argo_structure_cookie_ptr,
                                             struct argo_object *object) {
    struct zc_state *zc = argo_structure_cookie_ptr;
    const struct zpn_ec_client_authenticate_ack *ack = object->base_structure_void;

    log_msg_time("zpn_ec_client_authenticate_ack", ack->error ? "error" : "", 0);

    char dump[8000];
    if (argo_object_dump(object, dump, sizeof(dump), NULL, 0) == ARGO_RESULT_NO_ERROR) {
        ZPN_LOG(AL_DEBUG, "Rx: %s", dump);
    }

    log_msg_time("zpn_ec_client_authenticate_ack", ack->error ? "error" : "", 0);

    if (ack->error) {
        ZPN_LOG(AL_ERROR, "Authenticate ack error for %s: %s; closing client.", zc->broker_name, ack->error);
        ZPN_LOG(AL_CRITICAL, "Implement me- Authenticate ack error");
    } else {
        zc->authenticated = 1;
        zc->status = zc_ready;
        if (zc->state_cb) {
            (zc->state_cb)(zc, zc->cookie_void, zc->cookie_int, zc->status, NULL);
        }
    }

    return ZPN_RESULT_NO_ERROR;
}

static int zpn_vdi_client_authenticate_ack_cb(void *argo_cookie_ptr,
                                             void *argo_structure_cookie_ptr,
                                             struct argo_object *object)
{
    struct zc_state *zc = argo_structure_cookie_ptr;
    struct zpn_vdi_client_authenticate_ack *ack = object->base_structure_void;

    if (zpn_debug_get(ZPN_DEBUG_CLIENT_IDX)) {
        char dump[8000];
        if (argo_object_dump(object, dump, sizeof(dump), NULL, 0) == ARGO_RESULT_NO_ERROR) {
            ZPN_LOG(AL_DEBUG, "Rx: %s", dump);
        }
    }

    if (ack->error) {
        ZPN_LOG(AL_ERROR, "Authenticate ack error for %s: %s; closing client.", zc->broker_name, ack->error);
        ZPN_LOG(AL_CRITICAL, "XXX Implement Me- Authenticate ack error");
    } else {
        zc->authenticated = 1;
        zc->status = zc_ready;
        if (zc->state_cb) {
            (zc->state_cb)(zc,
                           zc->cookie_void,
                           zc->cookie_int,
                           zc->status,
                           NULL);
        }
    }

    return ZPN_RESULT_NO_ERROR;
}

static struct zc_mtunnel *zc_get_mtunnel_by_tag_id(struct zc_state *zc, int32_t tag_id) {
    struct zc_mtunnel *mt;

    pthread_mutex_lock(&(zc->lock));
    mt = argo_hash_lookup(zc->mtunnels_by_tag, &tag_id, sizeof(tag_id), NULL);
    pthread_mutex_unlock(&(zc->lock));
    return mt;
}

static void send_error_message_back_to_client(struct zc_mtunnel *mt, const char *err) {
    char tmp_str[1000];
    time_t now;
    struct tm tm;
    struct evbuffer *buf = evbuffer_new();
    struct evbuffer *body = evbuffer_new();

    if (!buf || !body) {
        ZPN_LOG(AL_WARNING, "Could not allocate evbuffer.");
        goto EXIT;
    }

    evbuffer_add_printf(body, "<p>Connection Result = %s</p>\n\r", err);

    /* Generate date: */
    now = time(0);
    gmtime_r(&now, &tm);
    strftime(tmp_str, sizeof(tmp_str), "%a, %d %b %Y %H:%M:%S %Z", &tm);

    /* Response Line... */
    if (0 >= evbuffer_add_printf(buf,
                                 "HTTP/%d.%d %d %s\r\n"
                                 "Date: %s\r\n"
                                 "Expires: %s\r\n"
                                 "Connection: close\r\n"
                                 "Cache-Control: max-age=0, no-cache\r\n"
                                 "Content-Type: %s\r\n"
                                 "Content-Length: %ld\r\n"
                                 "\r\n",
                                 1,                      // (int)response->http_version_major,
                                 1,                      // (int)response->http_version_minor,
                                 503,                    // (int)response->status,
                                 "Service Unavailable",  // status_description[response->status],
                                 tmp_str,
                                 tmp_str,
                                 "text/html; charset=ISO-8859-1",
                                 (long)evbuffer_get_length(body))) {
        ZPN_LOG(AL_WARNING, "Could not allocate evbuffer.");
        goto EXIT;
    }

    if (evbuffer_add_buffer(buf, body)) {
        ZPN_LOG(AL_WARNING, "Could not attach evbuffer.");
        goto EXIT;
    }

    // zpn_client_process_rx_data(&(mt->mconn_fohh_tlv.mconn), buf, evbuffer_get_length(buf));

EXIT:
    if (buf)
        evbuffer_free(buf);
    if (body)
        evbuffer_free(body);
}

static int zpn_mtunnel_request_ack_cb(void *argo_cookie_ptr,
                                      void *argo_structure_cookie_ptr,
                                      struct argo_object *object) {
    struct zc_state *zc = argo_structure_cookie_ptr;
    struct zc_mtunnel *mt;
    const struct zpn_mtunnel_request_ack *ack = object->base_structure_void;
    struct zpn_assistant_mtunnel *mtunnel;

    if (zpn_debug_get(ZPN_DEBUG_MTUNNEL_IDX)) {
        DUMP_ARGO(object, NULL, "Rx.");
    }

    mt = zc_get_mtunnel_by_tag_id(zc, ack->tag_id);
    if (!mt) {
        ZPN_LOG(AL_NOTICE, "Tunnel ack received without tunnel tag=%d reason='%s' It timed out?.", ack->tag_id,
                ack->reason ?: "");
        return ZPN_RESULT_NO_ERROR;
    }

    if (mt->mtunnel_id) {
        size_t mtunnel_id_len;
        uint64_t mtunnel_id_hash;
        int res;
        mtunnel_id_len = strlen(mt->mtunnel_id);
        mtunnel_id_hash = CityHash64(mt->mtunnel_id, mtunnel_id_len);

        mtunnel = zc_mtunnel_lookup_and_lock(mt->mtunnel_id, mtunnel_id_hash);
        if (!mtunnel) {
            ZPN_LOG(AL_WARNING, "Mtunnel_id %s: Mtunnel end, but no tunnel!?", mt->mtunnel_id);
            return ZPN_RESULT_NO_ERROR;
        }

        ZPN_LOG(AL_INFO, "storing mtunnel with tag=%d", mtunnel->broker_tag);
        pthread_mutex_lock(&(zc->lock));
        res = argo_hash_store(zc->mtunnels_by_tag, &(ack->tag_id), sizeof(ack->tag_id), 1, mtunnel);
        pthread_mutex_unlock(&(zc->lock));
        if (res) {
            ZPN_LOG(AL_CRITICAL, "%s: hash store failed.", mtunnel->mtunnel_id);

            return ZPN_RESULT_NO_ERROR;
        }
    }

    if (ack->error || (!ack->mtunnel_id) || (!ack->tag_id)) {
        ZPN_LOG(AL_ERROR, "mtunnel error : %s %s %s", mt->application_name, ack->error ? ack->error : "error",
                ack->reason ? ack->reason : "");
        send_error_message_back_to_client(mt, ack->error);

        if( mt->state_cb_cookie_void ) {
            bufferevent_free(mt->state_cb_cookie_void);
            mt->state_cb_cookie_void = NULL;
        }
        zc_mtunnel_terminate(zc, mt, 1, ack->error);
         // need to close the connection to origin client
    } else {
        /* Peer is already created and bound globally (to connection
         * ID)- just need to bind server side to TLV correctly. */
        int res;

        if (zc->tlv_type == zpn_zrdt_tlv) {
            enum zrdt_stream_type type;

            ZPN_LOG(AL_DEBUG, "mtunnel_request_ack, zrdt_tlv");

            res = zpn_mconn_add_local_owner(&(mt->mconn_zrdt_tlv.mconn),
                                            0,
                                            &(zc->broker_zrdt_tlv_state),
                                            &(mt->tag_id),
                                            sizeof(mt->tag_id),
                                            &zpn_mconn_zrdt_tlv_calls);
            if (res) {
                ZPN_LOG(AL_CRITICAL, "Init local owner: %s", zpn_result_string(res));

                zc_mtunnel_terminate(zc, mt, 1, "Internal");
                return res;
            }

            if (mt->ip_proto == IPPROTO_UDP) {
                type = zrdt_stream_unreliable_endpoint;
            } else {
                type = zrdt_stream_reliable_endpoint;
            }

            res = zrdt_stream_create(zpn_mconn_zrdt_tlv_get_conn(&(zc->broker_zrdt_tlv_state)),
                                     &(mt->mconn_zrdt_tlv.stream),
                                     mt->tag_id,
                                     type,
                                     zpn_zrdt_read_cb,
                                     zpn_zrdt_write_cb,
                                     zpn_zrdt_event_cb,
                                     &(zc->broker_zrdt_tlv_state));
            if (res) {
                ZPN_LOG(AL_DEBUG, "Could not stream_create");
                zc_mtunnel_terminate(zc, mt, 1, "stream create error");
                return res;
            }
        } else {
            zpn_mconn_set_fohh_thread_id(
                    &(mt->mconn_fohh_tlv.mconn),
                    fohh_connection_get_thread_id(zpn_mconn_fohh_tlv_get_conn(&(zc->broker_tlv_state))));

            /* Attach to server side fohh_tlv.. (We waited to do this in
             * order to keep from flooding the server with data from the
             * get-go) */
            res = zpn_mconn_add_local_owner(&(mt->mconn_fohh_tlv.mconn),
                                            0,
                                            &(zc->broker_tlv_state),
                                            &(mt->tag_id),
                                            sizeof(mt->tag_id),
                                            &zpn_mconn_fohh_tlv_calls);
            if (res) {
                ZPN_LOG(AL_CRITICAL, "Init local owner: %s", zpn_result_string(res));
                zc_mtunnel_terminate(zc, mt, 1, "local owner error");
                return res;
            }
        }

        /*
         * Bind mconn's together.
         */
        if (mt->double_encrypt) {
            int mconn_thread_id;

            if (mt->ip_proto == IPPROTO_UDP) {
                zpn_mconn_get_fohh_thread_id(&(mt->mconn_udp_tlv.mconn), &mconn_thread_id);
            } else {
                zpn_mconn_get_fohh_thread_id(&(mt->mconn_bufferevent.mconn), &mconn_thread_id);
            }

            mt->connector = (struct zpn_connector *)zpn_connector_tun_create(mt, ssl_ctx_connector, NULL, 1, 0);
            if (!mt->connector) {
                ZPN_LOG(AL_ERROR, "Cannot allocator connector");
                return ZPN_RESULT_NO_MEMORY;
            }

            zpn_mconn_add_global_owner(&(((struct zpn_connector_tun *)mt->connector)->mconn_c.mconn),
                                       0,
                                       mt,
                                       &(mt->tag_id),
                                       sizeof(mt->tag_id),
                                       &client_global_call_set);

            zpn_mconn_add_global_owner(&(((struct zpn_connector_tun *)mt->connector)->mconn_s.mconn),
                                       0,
                                       mt,
                                       &(mt->tag_id),
                                       sizeof(mt->tag_id),
                                       &client_global_call_set);

            res = zpn_connector_tun_connect(
                    mconn_thread_id,
                    (struct zpn_connector_tun *)mt->connector,
                    (mt->tlv_type == zpn_fohh_tlv) ? &(mt->mconn_fohh_tlv.mconn) : &(mt->mconn_zrdt_tlv.mconn),
                    1,
                    0,
                    NULL);
            if (res) {
                ZPN_LOG(AL_CRITICAL, "Server side connect returned: %s", zpn_result_string(res));
                return res;
            }

            ZPN_LOG(AL_DEBUG, "Connected server side");

            res = zpn_connector_tun_connect(
                    mconn_thread_id,
                    (struct zpn_connector_tun *)mt->connector,
                    (mt->ip_proto == IPPROTO_UDP) ? &(mt->mconn_udp_tlv.mconn) : &(mt->mconn_bufferevent.mconn),
                    0,
                    mt->double_encrypt,
                    NULL);
            if (res) {
                ZPN_LOG(AL_CRITICAL, "Client side connect returned: %s", zpn_result_string(res));
                return res;
            }

            ZPN_LOG(AL_DEBUG, "Connected client side");
        } else {
            // not double encrypted
            if (mt->ip_proto == IPPROTO_UDP) {
                if (zc->tlv_type == zpn_zrdt_tlv) {
                    res = zpn_mconn_connect_peer(&(mt->mconn_udp_tlv.mconn), &(mt->mconn_zrdt_tlv.mconn));
                } else {
                    res = zpn_mconn_connect_peer(&(mt->mconn_udp_tlv.mconn), &(mt->mconn_fohh_tlv.mconn));
                }
            } else {
                if (zc->tlv_type == zpn_zrdt_tlv) {
                        res = zpn_mconn_connect_peer(&(mt->mconn_bufferevent.mconn), &(mt->mconn_zrdt_tlv.mconn));
                    } else {
                        res = zpn_mconn_connect_peer(&(mt->mconn_bufferevent.mconn), &(mt->mconn_fohh_tlv.mconn));
                    }
            }
            if (res) {
                ZPN_LOG(AL_ERROR, "Error connecting peers");
                zc_mtunnel_terminate(zc, mt, 1, "peer connection error");
                return res;
            }
        }

        /* Attach self to client... */
        if (mt->ip_proto == IPPROTO_UDP) {
            res = zpn_mconn_add_local_owner(&(mt->mconn_udp_tlv.mconn),
                                            0,
                                            mt->udp_tlv_state,
                                            &(mt->mconn_udp_tlv.peer_sockaddr),
                                            sizeof(struct sockaddr),
                                            &zpn_mconn_udp_tlv_calls);
        } else {
            res = zpn_mconn_add_local_owner(&(mt->mconn_bufferevent.mconn), 0, mt->bev, NULL, 0,
                                            &mconn_bufferevent_calls);
        }

        if (res) {
            ZPN_LOG(AL_ERROR, "Error adding local owner: %s", zpn_result_string(res));
            zc_mtunnel_terminate(zc, mt, 1, "local owner error");
            return res;
        }

        zc_mtunnel_update_state(mt, zc_mtunnel_ready, NULL);
        mt->mtunnel_id = ZPN_STRDUP(ack->mtunnel_id, strlen(ack->mtunnel_id));
    }
    return FOHH_RESULT_NO_ERROR;
}
int zc_assistant_mtunnel_terminate(struct zpn_assistant_mtunnel *a_mt, int terminate_fast, char *err);
static int zpn_mtunnel_end_cb(void *argo_cookie_ptr, void *argo_structure_cookie_ptr, struct argo_object *object) {
    (void)argo_cookie_ptr;
    struct zc_state *zc = argo_structure_cookie_ptr;
    const struct zpn_mtunnel_end *end = object->base_structure_void;

    struct zpn_assistant_mtunnel *mt;

    if (!end->tag_id) {
        ZPN_LOG(AL_WARNING, "Tunnel termination without tag_id");
        return ZPN_RESULT_NO_ERROR;
    }

    if (end->err_code && end->err_code != 5002 && end->err_code != 5027) {
        ZPN_DEBUG_MTUNNEL("Rx. mtunnel_end tag=%d reason=%d '%s' ", end->tag_id, end->err_code, end->error ?: "");
    } else {
        ZPN_DEBUG_MTUNNEL("Rx. mtunnel_end tag=%d reason=%d '%s'", end->tag_id, end->err_code, end->error ?: "");
    }

    pthread_mutex_lock(&(zc->lock));
    mt = argo_hash_lookup(zc->mtunnels_by_tag, &end->tag_id, sizeof(end->tag_id), NULL);
    pthread_mutex_unlock(&(zc->lock));

    if (!mt) {
        ZPN_LOG(AL_NOTICE, "Cannot find the mtunnel for tag_id = %d", end->tag_id);
        return ZPN_RESULT_NO_ERROR;
    }

    if (mt->self_type == 1) {
        struct zc_mtunnel *mtunnel = zc_get_mtunnel_by_tag_id(zc, end->tag_id);
        int terminate_mtunnel = 0;
        if (!mtunnel) {
            ZPN_LOG(AL_NOTICE, "Tunnel termination mtunnel_id = %s, tag_id=%d but does not exist", end->mtunnel_id,
                    end->tag_id);
            return ZPN_RESULT_NO_ERROR;
        }
        if (mtunnel->tlv_type == zpn_fohh_tlv) {
            mtunnel->mconn_fohh_tlv.mconn.fin_rcvd = 1;
            if (mtunnel->mconn_fohh_tlv.mconn.peer) {
                terminate_mtunnel = zpn_mconn_forward_mtunnel_end(mtunnel->mconn_fohh_tlv.mconn.peer, MT_CLOSED_TERMINATED, end->drop_data);
            } else {
                terminate_mtunnel = 1;
            }
        } else {
            mtunnel->mconn_zrdt_tlv.mconn.fin_rcvd = 1;
            if (mtunnel->mconn_zrdt_tlv.mconn.peer) {
                terminate_mtunnel = zpn_mconn_forward_mtunnel_end(mtunnel->mconn_zrdt_tlv.mconn.peer, MT_CLOSED_TERMINATED, end->drop_data);
            } else {
                terminate_mtunnel = 1;
            }
        }

        if (terminate_mtunnel) {
            zc_mtunnel_terminate(zc, mtunnel, 0, end->error);
            /* In this case, the peers were never bound, so the connection
             * close will fail because the full process doesn't get
             * followed. So we free it up here. */
            // if (mtunnel->bev) {
            //     bufferevent_free(mtunnel->bev);
            //     mtunnel->bev = NULL;
            // }
        }

        return ZPN_RESULT_NO_ERROR;
    }

    if (mt->tlv_type == zpn_fohh_tlv) {
        mt->mconn_fohh_tlv_broker.mconn.fin_rcvd = 1;
        if( mt->mconn_fohh_tlv_broker.mconn.peer )
            zpn_mconn_forward_mtunnel_end(mt->mconn_fohh_tlv_broker.mconn.peer, MT_CLOSED_TERMINATED, end->drop_data);
    } else {
        mt->mconn_zrdt_tlv_broker.mconn.fin_rcvd = 1;
        if( mt->mconn_zrdt_tlv_broker.mconn.peer )
            zpn_mconn_forward_mtunnel_end(mt->mconn_zrdt_tlv_broker.mconn.peer, MT_CLOSED_TERMINATED, end->drop_data);
    }

    mt->state = za_reaping;
    zc_mtunnel_locked_state_machine(mt);

#if 0
    zc_assistant_mtunnel_terminate(mt, 1, end->error?"error":NULL);
#endif

    return ZPN_RESULT_NO_ERROR;
}

static int zpn_mtunnel_tag_pause_cb(void *argo_cookie_ptr,
                                    void *argo_structure_cookie_ptr,
                                    struct argo_object *object) {
    (void)argo_cookie_ptr;
    struct zc_state *zc = argo_structure_cookie_ptr;
    struct zpn_mtunnel_tag_pause *req = object->base_structure_void;
    struct zc_mtunnel *mtunnel;


    char dump[8000];
    if (argo_object_dump(object, dump, sizeof(dump), NULL, 0) == ARGO_RESULT_NO_ERROR) {
        ZPN_LOG(AL_DEBUG, "Rx: %s", dump);
    }

    if (!req->tag_id) {
        ZPN_LOG(AL_WARNING, "Tunnel pause without tag_id");
        return ZPN_RESULT_NO_ERROR;
    }

    mtunnel = zc_get_mtunnel_by_tag_id(zc, req->tag_id);

    if (!mtunnel) {
        ZPN_LOG(AL_NOTICE, "Tunnel pause mtunnel does not exist, tag_id = %d", req->tag_id);
        return ZPN_RESULT_NO_ERROR;
    }

    if (mtunnel->self_type == 1) {
        zpn_mconn_to_client_pause(&(mtunnel->mconn_fohh_tlv.mconn));
    } else if (mtunnel->self_type == 2) {
        struct zpn_assistant_mtunnel *mt = (struct zpn_assistant_mtunnel *)mtunnel;
        zpn_mconn_to_client_pause(&(mt->mconn_fohh_tlv_broker.mconn));
    }

    return ZPN_RESULT_NO_ERROR;
}

static int zpn_mtunnel_tag_resume_cb(void *argo_cookie_ptr,
                                     void *argo_structure_cookie_ptr,
                                     struct argo_object *object) {
    struct zc_state *zc = argo_structure_cookie_ptr;
    const struct zpn_mtunnel_tag_resume *req = object->base_structure_void;
    struct zc_mtunnel *mtunnel;


    char dump[8000];
    if (argo_object_dump(object, dump, sizeof(dump), NULL, 0) == ARGO_RESULT_NO_ERROR) {
        ZPN_LOG(AL_DEBUG, "Rx: %s", dump);
    }

    if (!req->tag_id) {
        ZPN_LOG(AL_WARNING, "Tunnel resume without tag_id");
        return ZPN_RESULT_NO_ERROR;
    }

    mtunnel = zc_get_mtunnel_by_tag_id(zc, req->tag_id);

    if (!mtunnel) {
        ZPN_LOG(AL_NOTICE, "Tunnel resume mtunnel does not exist, tag_id = %d", req->tag_id);
        return ZPN_RESULT_NO_ERROR;
    }

    if (mtunnel->self_type == 1) {
        zpn_mconn_to_client_resume(&(mtunnel->mconn_fohh_tlv.mconn));
    } else if (mtunnel->self_type == 2) {
        struct zpn_assistant_mtunnel *mt = (struct zpn_assistant_mtunnel *)mtunnel;
        zpn_mconn_to_client_resume(&(mt->mconn_fohh_tlv_broker.mconn));
    }

    return ZPN_RESULT_NO_ERROR;
}
int zpn_client_search_apps(struct zc_state *zc, const char *domain, int port, int protocol, int *is_double_encrypt)
{
    struct argo_inet inet;
    struct zc_application *found_obj[10];
    int found_count = 10;
    int res;
    int is_inet = 0;

    res = argo_string_to_inet(domain, &inet);
    if ((res == ARGO_RESULT_NO_ERROR) && ((inet.length == 16) || (inet.length == 4))) is_inet = 1;

    if (is_double_encrypt) (*is_double_encrypt) = 0;

    pthread_mutex_lock(&(zc->lock));
    if (is_inet) {
        if (inet.length == 4) {
            res = zradix_search(zc->ipv4_lookup, inet.address, inet.netmask,
                                NULL, NULL, (void **) &found_obj, &found_count);
        } else {
            res = zradix_search(zc->ipv6_lookup, inet.address, inet.netmask,
                                NULL, NULL, (void **) &found_obj, &found_count);
        }
        if (res != ZRADIX_RESULT_NO_ERROR) {
            pthread_mutex_unlock(&(zc->lock));
            return ZPN_RESULT_NOT_FOUND;
        }
    } else {
        found_count = diamond_search(zc->domain_lookup,
                                     (const uint8_t *)domain,
                                     strlen(domain),
                                     (void **)&found_obj,
                                     NULL,
                                     found_count);
        if (!found_count) {
            pthread_mutex_unlock(&(zc->lock));
            return ZPN_RESULT_NOT_FOUND;
        }
    }
    // log the found apps
    for (int i = 0; i < found_count; i++) {
        struct zpn_client_app *app = &found_obj[i]->app;
        ZCLIENT_LOG(AL_DEBUG,
                    "%d. matched app domain='%s' bypass-type=%s bypass=%d sipa=%d udp_port_ranges=%d "
                    "tcp_port_ranges=%d",
                    i + 1,
                    app->app_domain,
                    app->bypass_type ?: "",
                    app->bypass,
                    app->ip_anchored,
                    app->udp_port_ranges_count,
                    app->tcp_port_ranges_count);
    }

    for (int i = 0; i < found_count; i++) {
        struct zpn_client_app *app = &found_obj[i]->app;

        // TODO: what about SIPA apps, ip_anchored ?

        if (protocol == 17) {
            for (int j = 0; j + 1 < app->udp_port_ranges_count; j += 2) {
                if ((port >= app->udp_port_ranges[j]) && (port <= app->udp_port_ranges[j + 1])) {
                    if (is_double_encrypt)
                        (*is_double_encrypt) = app->double_encrypt;
                    pthread_mutex_unlock(&(zc->lock));

                    ZCLIENT_LOG(AL_NOTICE, "udp app found bypass-type=%s bypass=%d  app-domain='%s' domain='%s' port=%d",
                                app->bypass_type ?: "", app->bypass, app->app_domain, domain, port);
                    return ZPN_RESULT_NO_ERROR;
                }
            }
        } else if (protocol == 6) {
            for (int j = 0; j + 1 < app->tcp_port_ranges_count; j += 2) {
                if ((port >= app->tcp_port_ranges[j]) && (port <= app->tcp_port_ranges[j + 1])) {
                    if (is_double_encrypt)
                        (*is_double_encrypt) = app->double_encrypt;
                    pthread_mutex_unlock(&(zc->lock));

                    ZCLIENT_LOG(AL_NOTICE, "tcp app found bypass-type=%s bypass=%d app-domain='%s' domain='%s' port=%d",
                                app->bypass_type ?: "", app->bypass, app->app_domain, domain, port);
                    return ZPN_RESULT_NO_ERROR;
                }
            }
        } else {
            ZCLIENT_LOG(AL_NOTICE,
                        "%d/%d. App unsupported protocol=%d, app-domain='%s' bypass-type=%s bypass=%d sipa=%d", i + 1,
                        found_count, protocol, app->app_domain, app->bypass_type ?: "", app->bypass,
                        app->ip_anchored);
        }
        // does bypass always correlate to "NEVER" ?
        if (app->bypass || !strcmp(app->bypass_type, "NEVER")) {
            // this app is bypassed, as there are no ports
            // ASSUMPTION: the order if the diamond search, longest domains first i.e. FQDNs BEFORE wildcards
            ZCLIENT_LOG(AL_NOTICE, "%d/%d. App is bypassed, app-domain='%s' bypass-type=%s bypass=%d sipa=%d", i + 1,
                        found_count, app->app_domain, app->bypass_type ?: "", app->bypass, app->ip_anchored);
            break;
        }
    }
    pthread_mutex_unlock(&(zc->lock));
    return ZPN_RESULT_NOT_FOUND;
}
static int zpn_client_app_cb(void *argo_cookie_ptr, void *argo_structure_cookie_ptr, struct argo_object *object) {
    struct zc_state *zc = argo_structure_cookie_ptr;
    const struct zpn_client_app *req = (struct zpn_client_app *)object->base_structure_void;
    static int sipa_app_count = 0;

#if 0
    log_msg_time("zpn_client_app",req->app_domain, 0);
#endif

    if (!req->deleted && !req->bypass) {
        char sipa_buf[100];
        sipa_buf[0] = '\0';
        if (req->ip_anchored || req->inspected) {
            sipa_app_count++;
            snprintf(sipa_buf, sizeof(sipa_buf), "sipa-%d=%d|%d", sipa_app_count,  // sipa
                     req->ip_anchored, req->inspected);
        }
        struct zc_application *zapp;
        number_of_apps_recv++;
        /* Store the application */
        ZPN_DEBUG_APPLICATION( "Rx app: %4d. '%-30s' app_gid=%" PRId64 " customer=%" PRId64 " bypass=%d|%s %s",
                number_of_apps_recv,
                req->app_domain, req->app_gid, ZPATH_GID_GET_CUSTOMER_GID(req->app_gid),
                req->bypass,
                req->bypass_type,
                sipa_buf // sipa
             );

        zapp = ZPN_CALLOC(sizeof(struct zc_application));
        if (zapp) {
            if (req->app_domain) {
                int i;

                zapp->app.app_domain = ZPN_STRDUP(req->app_domain, strlen(req->app_domain));
                zapp->app.ports_count = req->ports_count;
                zapp->app.port_ranges_count = req->port_ranges_count;
                zapp->app.tcp_port_ranges_count = req->tcp_port_ranges_count;
                zapp->app.udp_port_ranges_count = req->udp_port_ranges_count;
                if (req->bypass_type){
                    zapp->app.bypass_type = ZPN_STRDUP(req->bypass_type, strlen(req->bypass_type));
                }
                zapp->app.double_encrypt = req->double_encrypt;
                if (req->ports_count) {
                    zapp->app.ingress_ports = ZPN_CALLOC(req->ports_count * sizeof(int32_t));
                    for (i = 0; i < req->ports_count; i++) {
                        zapp->app.ingress_ports[i] = req->ingress_ports[i];
                    }
                }

                if (req->port_ranges_count) {
                    zapp->app.ingress_port_ranges = ZPN_CALLOC(req->port_ranges_count * sizeof(int32_t));
                    for (i = 0; i < req->port_ranges_count; i++) {
                        zapp->app.ingress_port_ranges[i] = req->ingress_port_ranges[i];
                    }
                }

                if (req->tcp_port_ranges_count) {
                    zapp->app.tcp_port_ranges = ZPN_CALLOC(req->tcp_port_ranges_count * sizeof(int32_t));
                    for (i = 0; i < req->tcp_port_ranges_count; i++) {
                        zapp->app.tcp_port_ranges[i] = req->tcp_port_ranges[i];
                    }
                }

                if (req->udp_port_ranges_count) {
                    zapp->app.udp_port_ranges = ZPN_CALLOC(req->udp_port_ranges_count * sizeof(int32_t));
                    for (i = 0; i < req->udp_port_ranges_count; i++) {
                        zapp->app.udp_port_ranges[i] = req->udp_port_ranges[i];
                    }
                }
            }

            ZTAILQ_INSERT_TAIL(&(zc->apps), zapp, entry);
        }

        if (req->ports_count) {
            zc->app_create(req->app_domain, req->ingress_ports[0], 0, req->double_encrypt);
        } else if (req->tcp_port_ranges_count) {
            int i;

            for (i = 0; i < req->tcp_port_ranges_count; i++) {
                zc->app_create(req->app_domain, req->tcp_port_ranges[i], 0, req->double_encrypt);
            }
        }

        if (req->udp_port_ranges_count) {
            zc->app_create(req->app_domain, 0, req->udp_port_ranges[0], req->double_encrypt);
        }
    } else if (req->bypass) {
        ZPN_LOG(AL_ERROR, "Rx app: BYPASSED '%-30s' app_gid=%" PRId64 " customer=%" PRId64 " bypass=%d|%s", req->app_domain,
                req->app_gid, ZPATH_GID_GET_CUSTOMER_GID(req->app_gid), req->bypass, req->bypass_type);
    } else if (req->deleted) {
        ZPN_LOG(AL_ERROR, "Rx app: DELETED '%-30s' app_gid=%" PRId64 " customer=%" PRId64 " bypass=%d|%s",
                req->app_domain, req->app_gid, ZPATH_GID_GET_CUSTOMER_GID(req->app_gid), req->bypass, req->bypass_type);
    }

    return ZPN_RESULT_NO_ERROR;
}

static int zpn_client_app_complete_cb(void *argo_cookie_ptr,
                                      void *argo_structure_cookie_ptr,
                                      struct argo_object *object) {
    struct zpn_client_app_complete *app = object->base_structure_void;

    if (zpn_debug_get(ZPN_DEBUG_APPLICATION_IDX)) {
        DUMP_ARGO(object, NULL, "Rx.");
    }

    log_msg_time("zpn_client_app_complete", "", 0);

    ZPN_LOG(AL_INFO, "recieved total applications=%d %s", number_of_apps_recv, app->error ? app->error : "");
    ZPN_LOG(AL_INFO, "matched and running %d apps", number_of_apps_matched);
    return ZPN_RESULT_NO_ERROR;
}

static int zpn_dns_client_check_cb(void *argo_cookie_ptr, void *argo_structure_cookie_ptr, struct argo_object *object) {
    struct zpn_dns_client_check *dns_check = object->base_structure_void;

    log_msg_time("zpn_dns_client_check", dns_check->error ? "error" : "", 0);

    if (zpn_debug_get(ZPN_DEBUG_BROKER_DNS_IDX)) {
        DUMP_ARGO(object, NULL, "Rx.");
    }

    if (dns_check->error) {
        ZPN_LOG(AL_ERROR, "zpn_dns_client_check error=%s id=%" PRId64 " name=%s type=%s", dns_check->error,
                dns_check->id, dns_check->name, dns_check->type);
    }
    return ZPN_RESULT_NO_ERROR;
}

static int zpn_broker_redirect_cb(void *argo_cookie_ptr, void *argo_structure_cookie_ptr, struct argo_object *object) {

    log_msg_time("zpn_broker_redirect", "", 0);

    DUMP_ARGO(object, NULL, "Rx.");

    log_msg_time("zpn_broker_redirect", "", 0);
    return ZPN_RESULT_NO_ERROR;
}

static int zc_unblock_callback(struct fohh_connection *connection,
                               enum fohh_queue_element_type element_type,
                               void *cookie) {
    struct zc_state *zc = cookie;

    return zpn_fohh_tlv_unblock_cb(&(zc->broker_tlv_state));
}

static int zpn_client_search_domain_complete_cb(void *argo_cookie_ptr,
                                                void *argo_structure_cookie_ptr,
                                                struct argo_object *object) {
    (void)argo_cookie_ptr;
    int i;
    struct zc_state *zc = argo_structure_cookie_ptr;
    struct zpn_client_search_domain_complete *desc = object->base_structure_void;

    log_msg_time("zpn_client_search_domain_complete","", 0);

    char **search_domains = desc->all_domains;
    int search_domains_count = desc->all_domains_count;

    if (zpn_debug_get(ZPN_DEBUG_APPLICATION_IDX)) {
        DUMP_ARGO(object, NULL, "Rx.");
    }

    ZPN_LOG(AL_INFO, "recieved total search domains=%d", search_domains_count);
    log_msg_time("zpn_client_search_domain_complete", "", 0);

    if (zc->dns_search_suffix) {
        for (i = 0; i < zc->dns_search_suffix_count; i++) {
            ZPN_FREE(zc->dns_search_suffix[i]);
        }
        ZPN_FREE(zc->dns_search_suffix);
        zc->dns_search_suffix = NULL;
        zc->dns_search_suffix_count = 0;
    }
    if (search_domains_count) {
        zc->dns_search_suffix = ZPN_CALLOC(sizeof(*(zc->dns_search_suffix)) * search_domains_count);
        for (i = 0; i < search_domains_count; i++) {
            zc->dns_search_suffix[i] = ZPN_STRDUP(search_domains[i], strlen(search_domains[i]));
            ZPN_DEBUG_AUTH("%d. search_domain  %s", i + 1, search_domains[i]);
        }
        zc->dns_search_suffix_count = search_domains_count;
    }

    return ZPN_RESULT_NO_ERROR;
}

static int zpn_client_search_domain_cb(void *argo_cookie_ptr,
                                       void *argo_structure_cookie_ptr,
                                       struct argo_object *object) {
    (void)argo_cookie_ptr;
    int i;
    struct zc_state *zc = argo_structure_cookie_ptr;
    struct zpn_client_search_domain_all *desc = object->base_structure_void;

    char **search_domains = desc->all_domains;
    int search_domains_count = desc->all_domains_count;

    if (zc->dns_search_suffix) {
        for (i = 0; i < zc->dns_search_suffix_count; i++) {
            ZPN_FREE(zc->dns_search_suffix[i]);
        }
        ZPN_FREE(zc->dns_search_suffix);
        zc->dns_search_suffix = NULL;
        zc->dns_search_suffix_count = 0;
    }
    if (search_domains_count) {
        zc->dns_search_suffix = ZPN_CALLOC(sizeof(*(zc->dns_search_suffix)) * search_domains_count);
        for (i = 0; i < search_domains_count; i++) {
            zc->dns_search_suffix[i] = ZPN_STRDUP(search_domains[i], strlen(search_domains[i]));
            ZPN_LOG(AL_DEBUG, "%d. search_domain  %s", i + 1, search_domains[i]);
        }
        zc->dns_search_suffix_count = search_domains_count;
    }

    return ZPN_RESULT_NO_ERROR;
}

static int zpn_client_aggregated_domains_cb(void *argo_cookie_ptr,
                                            void *argo_structure_cookie_ptr,
                                            struct argo_object *object) {

    if (zpn_debug_get(ZPN_DEBUG_AUTH_IDX)) {
        DUMP_ARGO(object, NULL, "Rx.");
    }
    struct zpn_client_aggregated_domains *ad = object->base_structure_void;

    log_msg_time("zpn_client_aggregated_domains", "", 0);

    ZPN_LOG(AL_INFO, "recieved total agregated domains=%d", ad->include_domain_list_cnt);
    for (int i = 0; i < ad->include_domain_list_cnt; i++) {
        ZPN_LOG(AL_INFO, "aggregated domain %d. %s", i + 1, ad->include_domain_list[i]);
    }

    return ZPN_RESULT_NO_ERROR;
}

/* Handy for ignoring specific messages */
static int ignore_object_cb(void *argo_cookie_ptr, void *argo_structure_cookie_ptr, struct argo_object *object) {
    return ZPATH_RESULT_NO_ERROR;
}

static int zpn_trusted_networks_ack_cb(void *argo_cookie_ptr, void *zc_cookie, struct argo_object *object) {
    (void)argo_cookie_ptr;
    (void)zc_cookie;
    if (zpn_debug_get(ZPN_DEBUG_AUTH_IDX)) {
        DUMP_ARGO(object, NULL, "Rx.");
    }
    return ZPATH_RESULT_NO_ERROR;
}

static int zpn_client_connection_upgrade_response_cb(void *argo_cookie_ptr,
                                                     void *zc_cookie,
                                                     struct argo_object *object) {
    (void)argo_cookie_ptr;
    (void)zc_cookie;
    DUMP_ARGO(object, NULL, "Rx.");

    return ZPATH_RESULT_NO_ERROR;
}
/*
 * cb for broker request
 */
static int zpn_broker_request_cb(void *argo_cookie_ptr, void *zc_cookie, struct argo_object *object) {
    (void)argo_cookie_ptr;
    struct zc_state *zc;
    char connection_dbg_str[ZC_CONNECTION_DBG_STR_LEN];

    log_msg_time("zpn_broker_request", "", 0);

    if (zpn_debug_get(ZPN_DEBUG_MTUNNEL_IDX)) {
        DUMP_ARGO(object, NULL, "Rx.");
    }

    zc = zc_cookie;

    snprintf(connection_dbg_str, sizeof(connection_dbg_str), "%s", zc->broker_name);

    zc_broker_request(zc, connection_dbg_str, object->base_structure_void);

    return ZPN_RESULT_NO_ERROR;
}

static int zpn_client_fohh_tlv_window_update_cb(void *argo_cookie_ptr,
                                                void *argo_structure_cookie_ptr,
                                                struct argo_object *object) {
    struct zc_state *zc = argo_structure_cookie_ptr;
    struct zpn_fohh_tlv *fohh_tlv = &zc->broker_tlv_state;
    const struct zpn_fohh_tlv_window_update *req = object->base_structure_void;

    log_msg_time("zpn_fohh_tlv_window_update", "", 0);

    ZPN_DEBUG_MCONN("Rx: tag=%d limit=%" PRId64 " data=%" PRId64, req->tag_id, req->tx_limit, req->rx_data);

    if (!req->tag_id) {
        /* For overall FOHH connection */
        ZPATH_MUTEX_LOCK(&(fohh_tlv->lock), __FILE__, __LINE__);
        if (req->tx_limit > fohh_tlv->tx_limit) {
            fohh_tlv->tx_limit = req->tx_limit;
            fohh_tlv->tx_limit_update_us = epoch_us();
        }
        if (req->tx_limit) {
            fohh_tlv->remote_fc_status = flow_ctrl_enabled;
        } else {
            fohh_tlv->remote_fc_status = flow_ctrl_disabled;
        }
        ZPATH_MUTEX_UNLOCK(&(fohh_tlv->lock), __FILE__, __LINE__);

        zpn_fohh_tlv_unblock_cb(fohh_tlv);
    } else {
        /* For an mconn inside FOHH connection */
        pthread_mutex_lock(&(zc->lock));
        struct zpn_assistant_mtunnel *mtunnel =
                argo_hash_lookup(zc->mtunnels_by_tag, &req->tag_id, sizeof(req->tag_id), NULL);
        pthread_mutex_unlock(&(zc->lock));

        // TODO: fix this complete hack: source client uses zc_tunnel, destination tunnel uses zpn_assistant_mtunnel

        if (!mtunnel) {
            ZPN_LOG(AL_NOTICE, "zpn_fohh_tlv_window_update received for non-existing tunnel tag=%d", req->tag_id);
            return ZPN_RESULT_NO_ERROR;
        }

        if (mtunnel->self_type == 2) {
            mtunnel->mconn_fohh_tlv_broker.remote_fc_status = flow_ctrl_enabled;

            if (mtunnel->mconn_fohh_tlv_broker.tx_limit != req->tx_limit) {
                ZPN_DEBUG_MCONN("tag=%d update limit to %" PRId64, mtunnel->mconn_fohh_tlv_broker.tag_id,
                                req->tx_limit);
            }
            mtunnel->mconn_fohh_tlv_broker.tx_limit = req->tx_limit;
            zpn_client_drain_tx_data(&(mtunnel->mconn_fohh_tlv_broker.mconn));
        } else if (mtunnel->self_type == 1) {
            struct zc_mtunnel *zc_tunnel = (struct zc_mtunnel *)mtunnel;
            zc_tunnel->mconn_fohh_tlv.remote_fc_status = flow_ctrl_enabled;

            if (zc_tunnel->mconn_fohh_tlv.tx_limit != req->tx_limit) {
                ZPN_DEBUG_MCONN("tag=%d update limit to %" PRId64, zc_tunnel->mconn_fohh_tlv.tag_id, req->tx_limit);
            }
            zc_tunnel->mconn_fohh_tlv.tx_limit = req->tx_limit;
            zpn_client_drain_tx_data(&(zc_tunnel->mconn_fohh_tlv.mconn));
        } else {
            ZPN_LOG(AL_ERROR, "Wrong mtunnel type %d", mtunnel->self_type);
        }
    }

    return ZPN_RESULT_NO_ERROR;
}
static int zpn_ia_client_authenticate_ack_cb(void *argo_cookie_ptr,
                                             void *argo_structure_cookie_ptr,
                                             struct argo_object *object)
{
    struct zc_state *zc = argo_structure_cookie_ptr;
    struct zpn_ia_client_authenticate_ack *ack = object->base_structure_void;

    log_msg_time("zpn_ia_client_authenticate_ack", ack->error ? "error" : "", 0);

    if (zpn_debug_get(ZPN_DEBUG_AUTH_IDX)) {
        DUMP_ARGO(object, NULL, "Rx.");
    }

    if (ack->error) {
        ZPN_LOG(AL_ERROR, "Authenticate ack error for %s: %s; closing client.", zc->broker_name, ack->error);
    } else {
        zc->authenticated = 1;
        zc->status = zc_ready;
        if (zc->state_cb) {
            (zc->state_cb)(zc,
                           zc->cookie_void,
                           zc->cookie_int,
                           zc->status,
                           NULL);
        }
    }

    return ZPN_RESULT_NO_ERROR;
}

static int zpn_c3_client_authenticate_ack_cb(void *argo_cookie_ptr,
                                             void *argo_structure_cookie_ptr,
                                             struct argo_object *object) {
    struct zc_state *zc = argo_structure_cookie_ptr;
    struct zpn_machine_tunnel_client_authenticate_ack *ack = object->base_structure_void;

    log_msg_time("zpn_machine_tunnel_client_authenticate_ack", ack->error ? "error" : "", 0);

    if (zpn_debug_get(ZPN_DEBUG_AUTH_IDX)) {
        DUMP_ARGO(object, NULL, "Rx.");
    }

    if (ack->error) {
        ZPN_LOG(AL_ERROR, "Authenticate ack error for %s: %s; closing client.", zc->broker_name, ack->error);
    } else {
        zc->authenticated = 1;
        zc->status = zc_ready;
        if (zc->state_cb) {
            (zc->state_cb)(zc, zc->cookie_void, zc->cookie_int, zc->status, NULL);
        }
    }

    return ZPN_RESULT_NO_ERROR;
}

/*
 * cb for zpn_client_app_registration_notification_description
 */
static int zpn_client_app_notification_cb(void *argo_cookie_ptr, void *zc_cookie, struct argo_object *object) {
    (void)argo_cookie_ptr;
    (void)zc_cookie;
    const struct zpn_client_app_registration_notification *req = object->base_structure_void;

    log_msg_time("zpn_client_app_registration_notification",req->error_code, 0);

    if (zpn_debug_get(ZPN_DEBUG_AUTH_IDX)) {
        DUMP_ARGO(object, NULL, "Rx.");
    }

    log_msg_time("zpn_client_app_registration_notification", req->error_code, 0);

    return ZPN_RESULT_NO_ERROR;
}
static int zpn_client_config_updated_cb(void *argo_cookie_ptr,
                                        void *argo_structure_cookie_ptr,
                                        struct argo_object *object) {
    if (zpn_debug_get(ZPN_DEBUG_APPLICATION_IDX)) {
        DUMP_ARGO(object, NULL, "Rx.");
    }
    return ZPN_RESULT_NO_ERROR;
}
static void zpn_client_app_is_wildcard_inet(struct argo_object *object,
                                            char **domain,
                                            size_t *domain_len,
                                            int *is_wildcard,
                                            int *is_inet,
                                            struct argo_inet *inet) {
    struct zpn_client_app *app = object->base_structure_void;
    int res;

    (*domain) = app->app_domain;

    if (**domain == '.') {
        *is_wildcard = 1;
        *is_inet = 0;
        (*domain)++;
    } else {
        *is_wildcard = 0;
        res = argo_string_to_inet((*domain), inet);
        if ((res == ARGO_RESULT_NO_ERROR) && ((inet->length == 16) || (inet->length == 4))) {
            *is_inet = 1;
        } else {
            *is_inet = 0;
        }
    }
    *domain_len = strlen(*domain);
}
static void zpn_client_app_process(struct zc_state *zc, struct argo_object *app_object, int log) {
    struct zpn_client_app *app = NULL;
    char *domain = NULL;
    size_t domain_len;
    int wildcard = 0;
    int res;
    struct argo_inet inet;
    int is_inet = 0;

    if (app_object) {
        app = app_object->base_structure_void;
        if (!app)
            return;
        domain = app->app_domain;
    } else {
        ZPN_LOG(AL_ERROR, "app_object is null");
        return;
    }

    zpn_client_app_is_wildcard_inet(app_object, &domain, &domain_len, &wildcard, &is_inet, &inet);

    pthread_mutex_lock(&(zc->lock));

    if (0 == zc->app_download_start_us) {
        zc->app_download_start_us = monotime_us();
        ZCLIENT_LOG(AL_DEBUG, "First app recieved at %" PRId64, zc->app_download_start_us);
    }

    zc->number_of_apps_downloaded++;

    /*
     * 1. Lookup and delete existing app
     *
     * 2. If new app is not deleted, add it.
     */
    zpn_client_app_lookup_and_remove_locked(zc, app_object, domain, domain_len, wildcard, &inet, is_inet);

    /* Now create/add a new object (if it wasn't deleted) */

    if (!app->deleted) {
        /* Add new version of app... */
        struct zc_application *addme = ZPN_CALLOC(sizeof(*addme));
        addme->app_object = argo_object_copy(app_object);
        addme->app = *((struct zpn_client_app *)addme->app_object->base_structure_void);
        ZTAILQ_INSERT_TAIL(&(zc->apps), addme, entry);
        if (is_inet) {
            if (inet.length == 4) {
                res = zradix_add(zc->ipv4_lookup, inet.address, inet.netmask, addme);
                if (!res)
                    zc->zradix_ipv4_cnt++;
            } else {
                res = zradix_add(zc->ipv6_lookup, inet.address, inet.netmask, addme);
                if (!res)
                    zc->zradix_ipv6_cnt++;
            }
            if (res != ZRADIX_RESULT_NO_ERROR) {
                ZCLIENT_LOG(AL_ERROR, "Radix add application failed (IP): %s", domain);
                argo_object_release(addme->app_object);
                ZPN_FREE(addme);
            }
        } else {
            res = diamond_add(zc->domain_lookup, (const uint8_t *)domain, domain_len, wildcard, 0, 0, 0, 1, addme);
            if (res) {
                ZCLIENT_LOG(AL_ERROR, "Could not add new app for domain <%s>", app->app_domain);
                argo_object_release(addme->app_object);
                ZPN_FREE(addme);
            } else if (log) {
                ZCLIENT_LOG(AL_NOTICE,
                            "Adding '%s' - '%.*s', app segments=%zu ip_anchored=%d bypassed=%d",
                            app->app_domain,
                            (int)domain_len,
                            domain,
                            app->app_segments_count,
                            app->ip_anchored,
                            app->bypass);
            }
        }

        if (app->bypass || strcmp(app->bypass_type, "NEVER")) {
            zc->number_of_apps_bypassed++;
        }
    } else {
        ZCLIENT_LOG(AL_ERROR, "Could not add new app for domain <%s> %s - deleted", app->app_domain, app->bypass_type);
        zc->number_of_apps_deleted++;
    }
    pthread_mutex_unlock(&(zc->lock));
}
/*
 * App client check gets
 * DNS check + TTL + App info (TCP, UDP & double enc info)
 */
static int zpn_app_client_check_cb(void *argo_cookie_ptr, void *argo_structure_cookie_ptr, struct argo_object *object) {
    struct zc_state *zc = argo_structure_cookie_ptr;
    struct zpn_app_client_check *check_in = object->base_structure_void;
    if (zpn_debug_get(ZPN_DEBUG_APPLICATION_IDX)) {
        DUMP_ARGO(object, NULL, "Rx.");
    }

    struct zpn_client_app new_app;
    memset(&new_app, 0, sizeof(new_app));

    ZPATH_MUTEX_LOCK(&(zc->outstanding_app_check_hash_lock), __FILE__, __LINE__);
    struct outstanding_app_check *out_app = zhash_table_lookup(zc->outstanding_app_check_hash, check_in->name, strlen(check_in->name), NULL);

    if (out_app) {
        if (!check_in->error) {
            new_app.app_domain = check_in->app_domain;
            new_app.bypass_type = check_in->bypass_type;
            new_app.bypass = check_in->bypass;
            new_app.double_encrypt = check_in->double_encrypt;
            new_app.icmp_access_type = check_in->icmp_access_type;

            new_app.ip_anchored = check_in->ip_anchored;

            if (check_in->port_ranges_count > 0) {
                new_app.ingress_port_ranges = check_in->ingress_port_ranges;
                new_app.port_ranges_count = check_in->port_ranges_count;
            }
            if (check_in->tcp_port_ranges_count > 0) {
                new_app.tcp_port_ranges = check_in->tcp_port_ranges;
                new_app.tcp_port_ranges_count = check_in->tcp_port_ranges_count;
            }
            if (check_in->udp_port_ranges_count > 0) {
                new_app.udp_port_ranges = check_in->udp_port_ranges;
                new_app.udp_port_ranges_count = check_in->udp_port_ranges_count;
            }

            /* app scaling for Forwarding Clients */
            if (check_in->app_segments && check_in->app_segments_count > 0) {
                new_app.app_segments =
                        ZPN_CALLOC(sizeof(struct zpn_app_client_check_full_app *) * check_in->app_segments_count);
                new_app.app_segments_count = (int)check_in->app_segments_count;

                // // for (int j = 0; j < check_in->app_segments_count; j++) {
                // //     struct zpn_app_client_check_full_app *new_segment = new_app.app_segments[j]->base_structure_void;
                // //     struct zpn_app_client_check_full_app *req_segment = check_in->app_segments[j]->base_structure_void;
                // //     new_segment->gid = req_segment->gid;
                // }
            }

            struct argo_object *new_object = argo_object_create(zpn_client_app_description, &new_app);

            /* Do diamond work for nice lookups. */
            zpn_client_app_process(zc, new_object, 1);
            argo_object_release(new_object);
        }

        // create mtunnel
        struct zc_mtunnel *mt = zc_mtunnel_create(g_zc,
                                                 out_app->bev,
                                                 out_app->lookup_domain,
                                                 IPPROTO_TCP,
                                                 out_app->port,
                                                 out_app->fohh_thread_id,
                                                 out_app->double_encrypt,
                                                 generic_status_bev,
                                                 out_app->bev,
                                                 0);

        if (!mt) {
            ZCLIENT_LOG(AL_DEBUG, "Could not create mtunnel");

        }

        int res = zhash_table_remove(zc->outstanding_app_check_hash, check_in->name, strlen(check_in->name), NULL);
        if (res) {
            ZCLIENT_LOG(AL_DEBUG, "Unable to remove entry for %s in outstanding app check", check_in->name);
        } else {
            ZPN_FREE(out_app->lookup_domain);
            ZPN_FREE_AND_NULL(out_app);
        }
    }
    ZPATH_MUTEX_UNLOCK(&(zc->outstanding_app_check_hash_lock), __FILE__, __LINE__);

    return ZPN_RESULT_NO_ERROR;
}

static int zpn_client_register_rpc_callbacks(struct zc_state *zc) {
    struct argo_state *argo;
    int res;

    if (zc->tlv_type == zpn_fohh_tlv) {
        argo = fohh_argo_get_rx(zpn_mconn_fohh_tlv_get_conn(&(zc->broker_tlv_state)));
    } else {
        struct zpn_zrdt_argo_state *argo_state;

        argo_state = zrdt_get_msg_codec_state(zc->broker_zrdt_tlv_state.msg_stream);
        argo = argo_state->rx_argo;
    }

    if ((res = argo_register_structure(argo, zpn_machine_tunnel_client_authenticate_ack_description,
                                       zpn_c3_client_authenticate_ack_cb, zc))) {
        ZPN_LOG(AL_ERROR, "Could not register zpn_machine_tunnel_client_authenticate_ack");
        return res;
    }

    /* Register zpn_version_ack */
    if ((res = argo_register_structure(argo, zpn_version_ack_description, zpn_version_ack_cb, zc))) {
        ZPN_LOG(AL_ERROR, "Could not register zc_version_ack for zc_client for %s", zc->broker_name);
        return res;
    }

    /* Register zc_authenticate_ack */
    if ((res = argo_register_structure(argo, zpn_client_authenticate_ack_description, zc_authenticate_ack_cb, zc))) {
        ZPN_LOG(AL_ERROR, "Could not register zc_authenticate_ack for zc_client for %s", zc->broker_name);
        return res;
    }

    if ((res = argo_register_structure(argo, zpn_zia_identity_description, zc_zpn_zia_identity_cb, zc))) {
        ZPN_LOG(AL_ERROR, "Could not register zc_authenticate_ack for zc_client for %s", zc->broker_name);
        return res;
    }


    /* Register zpn_mtunnel_request_ack */
    if ((res = argo_register_structure(argo, zpn_mtunnel_request_ack_description, zpn_mtunnel_request_ack_cb, zc))) {
        ZPN_LOG(AL_ERROR, "Could not register zc_authenticate_ack for zc_client for %s", zc->broker_name);
        return res;
    }

    /* Register zpn_mtunnel_end */
    if ((res = argo_register_structure(argo, zpn_mtunnel_end_description, zpn_mtunnel_end_cb, zc))) {
        ZPN_LOG(AL_ERROR, "Could not register zc_authenticate_ack for zc_client for %s", zc->broker_name);
        return res;
    }

    /* Register zpn_mtunnel_tag_pause_request */
    if ((res = argo_register_structure(argo, zpn_mtunnel_tag_pause_description, zpn_mtunnel_tag_pause_cb, zc))) {
        ZPN_LOG(AL_ERROR, "Could not register zpn_mtunnel_tag_pause for zc_client for %s", zc->broker_name);
        return res;
    }

    /* Register zpn_mtunnel_tag_resume_request */
    if ((res = argo_register_structure(argo, zpn_mtunnel_tag_resume_description, zpn_mtunnel_tag_resume_cb, zc))) {
        ZPN_LOG(AL_ERROR, "Could not register zpn_mtunnel_tag_resume for zc_client for %s", zc->broker_name);
        return res;
    }

    /* Register zpn_client_app */
    if ((res = argo_register_structure(argo, zpn_client_app_description, zpn_client_app_cb, zc))) {
        ZPN_LOG(AL_ERROR, "Could not register zpn_client_app for zc_client for %s", zc->broker_name);
        return res;
    }

    /* Register zpn_client_app_complete */
    if ((res = argo_register_structure(argo, zpn_client_app_complete_description, zpn_client_app_complete_cb, zc))) {
        ZPN_LOG(AL_ERROR, "Could not register zpn_client_complete_app for zc_client for %s", zc->broker_name);
        return res;
    }

    /* Register zpn_client_config_updated */
    if ((res = argo_register_structure(argo, zpn_client_config_updated_description, zpn_client_config_updated_cb, zc))) {
        ZCLIENT_LOG(AL_ERROR, "Could not register zpn_client_config_updated for zc_client for %s", zc->broker_name);
        return res;
    }

    /* Register zpn_dns_client_check */
    if ((res = argo_register_structure(argo, zpn_dns_client_check_description, zpn_dns_client_check_cb, zc))) {
        ZPN_LOG(AL_ERROR, "Could not register zpn_client_complete_app for zc_client for %s", zc->broker_name);
        return res;
    }

    if ((res = argo_register_structure(argo, zpn_broker_redirect_description, zpn_broker_redirect_cb, zc))) {
        ZPN_LOG(AL_ERROR, "Could not register zpn_broker_redirect for zc_client for %s", zc->broker_name);
        return res;
    }

    /* Register zpn_fohh_tlv_window_update */
    if ((res = argo_register_structure(argo, zpn_fohh_tlv_window_update_description,
                                       zpn_client_fohh_tlv_window_update_cb, zc))) {
        ZPN_LOG(AL_ERROR, "Could not register zpn_client_fohh_tlv_window_update");
        return res;
    }

    /* Register zpn_ec_client_authenticate_ack */
    if ((res = argo_register_structure(argo, zpn_ec_client_authenticate_ack_description,
                                       zpn_ec_client_authenticate_ack_cb, zc))) {
        ZPN_LOG(AL_ERROR, "Could not register zpn_client_fohh_tlv_window_update");
        return res;
    }

    /* Register zpn_vdi_client_authenticate_ack */
    if ((res = argo_register_structure(argo, zpn_vdi_client_authenticate_ack_description, zpn_vdi_client_authenticate_ack_cb, zc))) {
        ZPN_LOG(AL_ERROR, "Could not register zpn_client_fohh_tlv_window_update");
        return res;
    }

       /* Register zpn_ia_client_authenticate_ack */
    if ((res = argo_register_structure(argo, zpn_ia_client_authenticate_ack_description, zpn_ia_client_authenticate_ack_cb, zc))) {
        ZPN_LOG(AL_ERROR, "Could not register zpn_ia_client_authenticate_ack");
        return res;
    }

    if ((res = argo_register_structure(argo, zpn_machine_tunnel_client_authenticate_ack_description, zpn_c3_client_authenticate_ack_cb, zc))) {
        ZPN_LOG(AL_ERROR, "Could not register zpn_machine_tunnel_client_authenticate_ack");
        return res;
    }


    if ((res = argo_register_structure(argo, zpn_client_search_domain_description, ignore_object_cb, NULL))) {
        ZPN_LOG(AL_ERROR, "Could not register to ignre zpn_client_search_domain");
        return res;
    }
    if ((res = argo_register_structure(argo, zpn_client_search_domain_complete_description,
                                       zpn_client_search_domain_complete_cb, zc))) {
        ZPN_LOG(AL_ERROR, "Could not register zpn_client_search_domain_complete");
        return res;
    }
    if ((res = argo_register_structure(argo, zpn_client_search_domain_all_description, zpn_client_search_domain_cb,
                                       zc))) {
        ZPN_LOG(AL_ERROR, "Could not register zpn_client_search_domain_all");
        return res;
    }

    if ((res = argo_register_structure(argo, zpn_broker_request_description, zpn_broker_request_cb, zc))) {
        ZPN_LOG(AL_ERROR, "Could not register to zpn_broker_request_description");
        return res;
    }

    if ((res = argo_register_structure(argo, zpn_client_app_registration_notification_description,
                                       zpn_client_app_notification_cb, zc))) {
        ZPN_LOG(AL_ERROR, "Could not register to zpn_client_app_registration_notification");
        return res;
    }

    /* Register zpn_fohh_tlv_window_update */
    if ((res = argo_register_structure(argo, zpn_fohh_tlv_window_update_description, zc_window_update_cb, zc))) {
        ZPN_LOG(AL_ERROR, "Could not register zpn_fohh_tlv_window_update");
        return res;
    }

    /* Register for aggregated domains (TLD + 1) */
    if ((res = argo_register_structure(argo, zpn_client_aggregated_domains_description,
                                       zpn_client_aggregated_domains_cb, zc))) {
        ZPN_LOG(AL_ERROR, "Could not register zpn_client_aggregated_domains for zc_client for %s", zc->broker_name);
        return res;
    }

    if ((res = argo_register_structure(argo, zpn_trusted_networks_ack_description,
                                       zpn_trusted_networks_ack_cb, zc))) {
        ZPN_LOG(AL_ERROR, "Could not register zpn_trusted_networks_ack for zc_client for %s", zc->broker_name);
        return res;
    }

    if ((res = argo_register_structure(argo, zpn_client_connection_upgrade_response_description,
                                       zpn_client_connection_upgrade_response_cb, zc))) {
        ZPN_LOG(AL_ERROR, "Could not register zpn_client_connection_upgrade_response for zc_client for %s",
                zc->broker_name);
        return res;
    }

    /* Register zpn_app_client_check */
    if ((res = argo_register_structure(argo, zpn_app_client_check_description, zpn_app_client_check_cb, zc))) {
        ZCLIENT_LOG(AL_ERROR, "Could not register zpn_app_client_check for zc_client for %s", zc->broker_name);
        return res;
    }

    return ZPN_RESULT_NO_ERROR;
}

static int64_t ASN1_TIME_to_epoch(ASN1_TIME *time) {
    const char *str = (const char *)time->data;
    struct tm tm;

    memset(&tm, 0, sizeof(tm));

#define NEXTDIGIT(str) ((*str++) - '0')

    if (time->type == V_ASN1_UTCTIME) {
        /* YYmmddHHMMSS */
        if (strlen(str) < 12)
            return 0;

        tm.tm_year = NEXTDIGIT(str) * 10;
        tm.tm_year += NEXTDIGIT(str);
        if (tm.tm_year < 50)
            tm.tm_year += 100;

    } else if (time->type == V_ASN1_GENERALIZEDTIME) {
        /* YYYYmmddHHMMSS */
        if (strlen(str) < 14)
            return 0;

        tm.tm_year = NEXTDIGIT(str) * 1000;
        tm.tm_year += NEXTDIGIT(str) * 100;
        tm.tm_year += NEXTDIGIT(str) * 10;
        tm.tm_year += NEXTDIGIT(str);

        tm.tm_year -= 1900;
    }

    tm.tm_mon = NEXTDIGIT(str) * 10;
    tm.tm_mon += NEXTDIGIT(str);
    tm.tm_mon--; /* Month is 0-based */

    tm.tm_mday = NEXTDIGIT(str) * 10;
    tm.tm_mday += NEXTDIGIT(str);

    tm.tm_hour = NEXTDIGIT(str) * 10;
    tm.tm_hour += NEXTDIGIT(str);

    tm.tm_min = NEXTDIGIT(str) * 10;
    tm.tm_min += NEXTDIGIT(str);

    tm.tm_sec = NEXTDIGIT(str) * 10;
    tm.tm_sec += NEXTDIGIT(str);

    return timegm(&tm);
}
static int is_expired_cert(const char *filename) {
    FILE *fp = fopen(filename, "r");
    if (!fp) {
        fprintf(stderr, "Cannot read file %s\n", filename);
        return 1;
    }

    X509 *cert = PEM_read_X509(fp, NULL, NULL, NULL);
    if (!cert) {
        fprintf(stderr, "Unable to parse certificate in file %s\n", filename);
        fclose(fp);
        return 1;
    }

    ASN1_TIME *time = X509_get_notAfter(cert);
    int64_t expiry_time_s = ASN1_TIME_to_epoch(time);
    int64_t now_s = epoch_s();

    if (expiry_time_s < now_s) {
        /* Certificate expired */
        return 1;
    }

    return 0;
}

static int zc_broker_client_callback(struct fohh_connection *connection, enum fohh_connection_state state, void *cookie) {
    struct zc_state *zc = cookie;
    int res;

    ZPN_DEBUG_AUTH( "ZPN client connection callback %s", fohh_connection_state_strings[state]);

    if (is_expired_cert(zc->cert_filename)) {
        zc->num_cbi_reauth_required += 1;
        ZPN_LOG(AL_ERROR, "Certificate Expired!");
        return ZPN_RESULT_ERR;
    }

    if (state == fohh_connection_connected) {
        struct zpn_tlv *tlv;
        struct argo_inet local_private_ip;

        log_msg_time("connected", "", 0);

        res = zpn_fohh_tlv_init(&(zc->broker_tlv_state), zc->broker_fohh, 0);
        if (res) {
            ZPN_LOG(AL_CRITICAL, "Could not init tlv: %s", zpn_result_string(res));
            return res;
        }

        zc->tlv_type = zpn_fohh_tlv;

        zpn_client_register_rpc_callbacks(zc);

        zpn_mconn_fohh_tlv_add_drain_timer(connection, &(zc->broker_tlv_state));

        zc->connected = 1;
        zc->authenticated = 1;
        zc->version_acked = 0;

        connection->quiet = 0;
        tlv = zc_get_tlv(zc);

        log_msg_time("zpn_version", "", 0);

        zpn_send_zpn_version(tlv, zpn_tlv_conn_incarnation(tlv), ZPATH_VERSION_MAJOR, ZPATH_VERSION_MINOR);

        fohh_connection_address(connection, NULL, &local_private_ip);

        if (zc->client_type == zpn_client_type_edge_connector || zc->client_type == zpn_client_type_branch_connector) {
            struct argo_inet local_private_ip;

            fohh_connection_address(connection, NULL, &local_private_ip);

            ZPN_LOG(AL_INFO,"sending zpn_ec_client_authenticate orgid=%" PRId64 " cloud='%s' location=%"PRId64, zc->orgid, zia_cloud, zc->o_location_id);
            log_msg_time("zpn_ec_client_authenticate", "", 0);

            if (zpn_send_zpn_ec_client_authenticate(tlv,
                                                    zpn_tlv_conn_incarnation(tlv),
                                                    1,
                                                    zc->orgid,
                                                    zc->o_location_id,
                                                    zia_cloud,
                                                    (const char **)zc->capabilities,
                                                    zc->capabilities_count,
                                                    &local_private_ip,
                                                    NULL)) {
                ZPN_LOG(AL_CRITICAL, "Failed to send ec/bc authentication request");
                return ZPN_RESULT_ERR;
            }

        } else if (zc->client_type == zpn_client_type_vdi) {

            struct argo_inet local_private_ip;

            fohh_connection_address(connection, NULL, &local_private_ip);

            ZPN_LOG(AL_INFO, "sending zpn_vdi_client_authenticate orgid=%" PRId64 " cloud='%s' ", zc->orgid,
                    zc->cloud_name);
            log_msg_time("zpn_vdi_client_authenticate", "", 0);

            if (zpn_send_zpn_vdi_client_authenticate(tlv,
                                                    zpn_tlv_conn_incarnation(tlv),
                                                    1,
                                                    zc->orgid,
                                                    zc->cloud_name,
                                                    (const char **)zc->capabilities,
                                                    zc->capabilities_count,
                                                    &local_private_ip,
                                                    NULL,
                                                    zc->username,
                                                    zc->last_auth_time)) {
                ZPN_LOG(AL_CRITICAL, "Failed to send vdi authentication request");
                return ZPN_RESULT_ERR;
            }

        } else if ((zc->client_type == zpn_client_type_zapp) || (zc->client_type == zpn_client_type_zapp_partner)) {

            log_msg_time("zpn_client_authenticate", zc->login_name, 0);
            struct zpn_client_authenticate data;
            memset(&data, 0, sizeof(data));
            struct argo_inet local_private_ip;
            fohh_connection_address(connection, NULL, &local_private_ip);


            data.id = 0;

            data.hw_serial_id = zc->hw_id;
            data.cpu_serial_id = zc->cpu_id;
            data.storage_serial_id = zc->storage_id;
            data.login_name = zc->login_name;

            data.capabilities = (const char **)zc->capabilities;
            data.capabilities_count = zc->capabilities_count;

            data.external_device_id = zc->external_device_id;
            data.o_identity_name = zc->o_identity_name;
            data.o_user_id = zc->o_user_id;
            data.o_location_id = zc->o_location_id;
            data.priv_ip = &local_private_ip;
            data.connection_mode = zc->passive;
            data.platform = zc->platform;

            ZPN_DEBUG_AUTH( "sending zpn_client_authenticate login='%s' hw_id='%s'", zc->login_name, zc->hw_id);

            if( zpn_debug_get(ZPN_DEBUG_AUTH_IDX))
                dump_structure(zpn_client_authenticate_description, &data, "Sn.", __FUNCTION__, __FILE__, __LINE__);

            // the assertion might be too big
            data.saml_assertion_xml = zc->saml_assertion;

            if (tlv_argo_serialize(tlv, zpn_client_authenticate_description, &data, zpn_tlv_conn_incarnation(tlv),
                                   fohh_queue_element_type_mission_critical))

            {
                ZPN_LOG(AL_CRITICAL, "Failed to send authentication request");
                return ZPN_RESULT_ERR;
            }

            // send trusted networks
            (void)zc_send_trusted_networks(zc);
        } else if (zc->client_type == zpn_client_type_ip_anchoring) {
            struct argo_inet local_private_ip;

            ZPN_DEBUG_AUTH(
                    "sending 'zpn_ia_client_authenticate' orgid=%" PRId64 " cloud='%s' customer_gid=%" PRId64
                    " loc_id=%" PRId64 " id_name='%s'",
                    zc->orgid, zc->cloud_name, zc->customer_gid, zc->o_location_id, zc->o_identity_name);

            log_msg_time("zpn_ia_client_authenticate", "", 0);

            fohh_connection_address(connection, NULL, &local_private_ip);

            struct zpn_ia_client_authenticate data;
            memset(&data, 0, sizeof(data));
            data.id = 1;
            data.cloud_name = zc->cloud_name;
            data.orgid = zc->orgid;
            data.customer_gid = zc->customer_gid;
            data.private_ip = &local_private_ip;
            data.capabilities = (const char **)zc->capabilities;
            data.capabilities_count = zc->capabilities_count;

            if (zpn_debug_get(ZPN_DEBUG_AUTH_IDX))
                dump_structure(zpn_ia_client_authenticate_description, &data, "Sn.", __FUNCTION__, __FILE__, __LINE__);

            if (tlv_argo_serialize(tlv,
                                   zpn_ia_client_authenticate_description,
                                   &data,
                                   0,
                                   fohh_queue_element_type_mission_critical)) {
                ZPN_LOG(AL_CRITICAL, "Failed to send authentication request");
                return ZPN_RESULT_ERR;
            }
        } else if (zc->client_type == zpn_client_type_machine_tunnel) {
            struct argo_inet local_private_ip;
            log_msg_time("zpn_client_type_machine_tunnel", zc->hw_id, 0);

            ZPN_DEBUG_AUTH(
                    "sending zpn_machine_tunnel_client_authenticate hw_id='%s' cpu_id='%s' storage_id='%s' "
                    "customer_gid=%" PRId64,
                    zc->hw_id, zc->cpu_id, zc->storage_id, zc->customer_gid);

            if (zc->hw_id == NULL) {
                // broker would crash
                ZPN_LOG(AL_ERROR,"ABORT hw_id is NULL");

                return ZPN_RESULT_ERR;
            }
            fohh_connection_address(connection, NULL, &local_private_ip);
            if (zpn_send_zpn_machine_tunnel_client_authenticate(tlv,
                                                                zpn_tlv_conn_incarnation(tlv),
                                                                1,
                                                                zc->hw_id,
                                                                zc->cpu_id,
                                                                zc->storage_id,
                                                                &local_private_ip,
                                                                "host",
                                                                "platform")) {
                ZPN_LOG(AL_CRITICAL, "Failed to send authentication request");
                return ZPN_RESULT_ERR;
            }
        }

        zc->status = zc_authenticate;

        /* Send initial window update to our peer */
        zpn_send_zpn_fohh_window_update(connection,
                                        fohh_connection_incarnation(connection),
                                        0,
                                        flow_control_enabled ? fohh_tlv_window_size : 0,
                                        0);
        zc->broker_tlv_state.last_wnd_update_us = epoch_us();
        zc->broker_tlv_state.remote_tx_limit = fohh_tlv_window_size;

    } else {
        log_msg_time("disconnected",connection->close_reason, 1);
        /* Connection error or disconnected... */
        log_msg_time("disconnected", connection->close_reason, 1);

        zc_drop_all_tunnels(zc);

        zc->connected = 0;
        zc->authenticated = 0;
        zc->version_acked = 0;

        zc->status = zc_not_connected;
    }

    if (zc->state_cb) {
        (zc->state_cb)(zc, zc->cookie_void, zc->cookie_int, zc->status, NULL);
    }

    return FOHH_RESULT_NO_ERROR;
}

static int zpn_client_new_stream_cb(struct zrdt_conn *conn,
                                    struct zrdt_stream *stream,
                                    uint64_t stream_id,
                                    enum zrdt_stream_status status,
                                    void *cookie)

{
    ZPN_LOG(AL_DEBUG, "New Stream callback. stream_id = %ld, status = %s", (long)stream_id,
            zrdt_stream_status_string(status));

    return ZPN_RESULT_NO_ERROR;
}

static void zpn_dtls_client_session_callback(struct zdtls_session *zd_sess,
                                        void *thread_cookie,
                                        enum zdtls_session_status status,
                                        const char *reason,
                                        void *cookie,
                                        int64_t int_cookie,
                                        void *tlv_cookie) {
    struct zc_state *zc = cookie;
    struct fohh_thread *f_thread = (struct fohh_thread *)thread_cookie;
    int thread_id = fohh_thread_get_id(f_thread);
    struct zrdt_conn *z_conn;
    int res;

    ZPN_LOG(AL_DEBUG, "Received dtls session callback, status = %s, description = %s", zdtls_session_status(status),
            zdtls_description(zd_sess));

    if (status == zdtls_session_open) {
        res = zrdt_conn_create(&z_conn,
                               DEFAULT_STREAM_MTU,
                               (zrdt_datagram_tx_f *)zdtls_session_transmit,
                               zd_sess,
                               int_cookie,
                               zpn_client_new_stream_cb,
                               zc,
                               zdtls_session_get_event_base(zd_sess));
        if (res) {
            ZPN_LOG(AL_DEBUG, "Could not create new connection?");
            res = zdtls_session_close(zd_sess);
            if (res) {
                ZPN_LOG(AL_DEBUG, "Could not handle session_close on session failure");
                return;
            }
        } else {
            struct zpn_tlv *tlv;

            ZPN_LOG(AL_DEBUG, "Created zrdt_conn");

            zc->tlv_type = zpn_zrdt_tlv;

            /* zc is not reused, so set tlv_incarnation to 0 */
            zpn_zrdt_tlv_init(&(zc->broker_zrdt_tlv_state), z_conn, 0);

            zrdt_conn_set_thread_id(z_conn, thread_id);

            res = zdtls_session_set_rx_callback(zd_sess,
                                                zpn_zrdt_zdtls_data_callback,
                                                zpn_mconn_zrdt_tlv_get_conn(&(zc->broker_zrdt_tlv_state)),
                                                0);
            if (res) {
                ZPN_LOG(AL_ERROR, "Could not set rx_callback from dtls");
                return;
            }

            res = zrdt_stream_create(zpn_mconn_zrdt_tlv_get_conn(&(zc->broker_zrdt_tlv_state)),
                                     &(zc->broker_zrdt_tlv_state.msg_stream),
                                     -1,
                                     zrdt_stream_reliable_endpoint,
                                     zpn_zrdt_read_cb,
                                     zpn_zrdt_write_cb,
                                     zpn_zrdt_event_cb,
                                     &(zc->broker_zrdt_tlv_state));
            if (res) {
                ZPN_LOG(AL_DEBUG, "Could not stream_create");
                return;
            }

            ZPN_LOG(AL_DEBUG, "Created stream 0");

            /* Initialize Argo state for msg_stream */
            res = zpn_zrdt_init_argo_state(zc->broker_zrdt_tlv_state.msg_stream, argo_serialize_json_no_newline, 1);
            if (res) {
                ZPN_LOG(AL_NOTICE, "Could not initialize argo state for msg_stream");
                return;
            }

            zc->connected = 1;
            zc->version_acked = 0;

            zpn_client_register_rpc_callbacks(zc);

            zpn_send_zpn_version(&(zc->broker_zrdt_tlv_state.tlv), zrdt_conn_incarnation(z_conn), 1, 1);

            tlv = zc_get_tlv(zc);

            if (zc->client_type == zpn_client_type_vdi) {

                struct argo_inet local_private_ip;

                zdtls_connection_address(zd_sess, NULL, &local_private_ip);

                if (zpn_send_zpn_vdi_client_authenticate(tlv, zpn_tlv_conn_incarnation(tlv), 1, 0, NULL, NULL, 0,
                                                        &local_private_ip, NULL, zc->username, zc->last_auth_time)) {
                    ZPN_LOG(AL_CRITICAL, "Failed to send vdi authentication request");
                    return;
                }

            }else {
                if (zpn_send_zpn_client_authenticate(tlv,
                                                     zpn_tlv_conn_incarnation(tlv),
                                                     0,
                                                     zc->saml_assertion,
                                                     zc->hw_id,
                                                     zc->cpu_id,
                                                     zc->storage_id,
                                                     zc->login_name,
                                                     NULL,
                                                     NULL,
                                                     NULL,
                                                     NULL,
                                                     0,
                                                     zpn_client_conn_mode_active)) {
                    ZPN_LOG(AL_CRITICAL, "Failed to send authentication request");
                    return;
                }
            }
        }
    } else if (status == zdtls_session_closing) {
        /* Closing */

    } else {
        /* Handle connection going away */

        if (zc->connected) {
            z_conn = zpn_mconn_zrdt_tlv_get_conn(&(zc->broker_zrdt_tlv_state));
            if (z_conn) {
                ZPN_LOG(AL_DEBUG, "Destroying rdt_conn");
                zpn_zrdt_tlv_destroy(&(zc->broker_zrdt_tlv_state), reason);
            }
        } else {
            ZPN_LOG(AL_DEBUG, "We are never connected, so nothing to be done");
            /* Should we retry? */
        }

        zc->connected = 0;
    }
}

static int zc_broker_resolver_callback(const char *domain_name,
                                       struct argo_inet *ips,
                                       size_t ips_count,
                                       void *callback_void,
                                       int64_t callback_int) {
    struct zc_state *zc = callback_void;
    SSL_CTX *ssl_ctx;
    struct sockaddr_storage broker_address;
    socklen_t addr_len;
    char sni_customer_domain[256];
    int res = ZPN_RESULT_NO_ERROR;
    char buf[128];

    ZPN_LOG(AL_DEBUG, "zc_broker_resolver_callback(), domain_name = %s", domain_name);

    ssl_ctx = zdtls_client_ssl_ctx_create(zc->ca_filename, zc->cert_filename, zc->key_filename, NULL);
    if (!ssl_ctx) {
        ZPN_LOG(AL_ERROR, "Could not create ssl ctx");
        goto EXIT;
    }

    argo_inet_to_sockaddr(ips, (struct sockaddr *)&broker_address, &addr_len, htons(ZPN_CLIENT_BROKER_PORT_UDP));

    fohh_sockaddr_storage_to_str(&broker_address, buf, 128);
    ZPN_LOG(AL_DEBUG, "ZRDT Broker Name = %s, Address = %s\n", zc->broker_name, buf);

    if (zc->sni) {
        snprintf(sni_customer_domain, sizeof(sni_customer_domain), "%s", zc->sni);
    } else {
        snprintf(sni_customer_domain, sizeof(sni_customer_domain), "%s.c2.%s", zc->customer_domain, zc->cloud_name);
    }

    ZPN_LOG(AL_DEBUG, "ZRDT sni name = %s", sni_customer_domain);

    res = ztlv_dtls_connect(fohh_get_thread_event_base(zc->fohh_thread_id),
                            &broker_address,
                            sni_customer_domain,
                            ssl_ctx,
                            zpn_dtls_client_session_callback,
                            zc,
                            1,  // Used to indicate it is a client.
                            fohh_get_thread(zc->fohh_thread_id),
                            1);
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not connect with zdtls.");
        goto EXIT;
    }

    return ZPN_RESULT_NO_ERROR;

EXIT:
    if (ssl_ctx)
        SSL_CTX_free(ssl_ctx);
    return ZPN_RESULT_ERR;
}

int zc_zrdt_create(struct zc_state *zc,
                   const char *customer_domain,
                   const char *cloud_name,
                   const char *sni,
                   int debug) {
    ZPN_LOG(AL_DEBUG, "ca = %s, cert = %s, key = %s", zc->ca_filename, zc->cert_filename, zc->key_filename);

    /* Start to resolve broker name and establish connection to broker */
    fohh_resolve_start(zc->broker_name, FOHH_MIN_DNS_FREQUENCY, NULL, NULL, 0);
    fohh_resolve_start(zc->broker_name, FOHH_MIN_DNS_FREQUENCY, zc_broker_resolver_callback, zc, 0);

    zc_timer_init(fohh_get_thread_event_base(zc->fohh_thread_id), NULL);

    return ZPN_RESULT_NO_ERROR;
}

int zc_fohh_create(struct zc_state *zc,
                   const char *customer_domain,
                   const char *cloud_name,
                   const char *sni,
                   int debug) {
    char sni_customer_domain[256];
    SSL_CTX *ssl_ctx = NULL;
    int res;

    ssl_ctx = fohh_client_ssl_ctx_create(zc->ca_filename, zc->cert_filename, zc->key_filename, NULL);
    if (!ssl_ctx) {
        ZPN_LOG(AL_ERROR, "Could not create SSL context");
        goto EXIT;
    }

    if (sni) {
        snprintf(sni_customer_domain, sizeof(sni_customer_domain), "%s", sni);
    } else {
        snprintf(sni_customer_domain, sizeof(sni_customer_domain), "%s.c2.%s", customer_domain, cloud_name);
    }

    zc->broker_fohh = fohh_client_create(FOHH_WORKER_ZPN_ITASCA_CLIENT,
                                         zc->broker_name,
                                         argo_serialize_json_pretty,
                                         fohh_connection_style_argo_tlv,
                                         0,  // not quiet, send fohh_status_request
                                         zc,
                                         zc_broker_client_callback,
                                         zpn_fohh_tlv_data_callback,
                                         zc_unblock_callback,
                                         NULL,
                                         zc->broker_name,
                                         sni_customer_domain,  // SNI
                                         NULL,
                                         htons(ZPN_CLIENT_BROKER_PORT),
                                         ssl_ctx,
                                         1,
                                         10 * 60);  // 10 mins
    if (!zc->broker_fohh) {
        goto EXIT;
    }
    if (debug)
        fohh_set_debug(zc->broker_fohh, 1);
    fohh_set_sticky(zc->broker_fohh, 1);

    fohh_set_max_backoff(zc->broker_fohh, backoff_max_s);

    res = zpn_fohh_tlv_init(&(zc->broker_tlv_state), zc->broker_fohh, 0);
    if (res) {
        ZPN_LOG(AL_CRITICAL, "Could not init tlv: %s", zpn_result_string(res));
        goto EXIT;
    }

    zc_timer_init(fohh_get_thread_event_base(fohh_connection_get_thread_id(zc->broker_fohh)), zc->broker_fohh);

    if (zpn_mconn_fohh_tlv_init_debug()) {
        ZPN_DEBUG_AUTH("IGNORE:Could not initialize debug command for fohh_tlv");
    }

    return ZPN_RESULT_NO_ERROR;

EXIT:
    if (ssl_ctx)
        SSL_CTX_free(ssl_ctx);
    return ZPN_RESULT_ERR;
}

struct zc_state *zc_state_create(const char *broker_name,
                                 const char *customer_domain,
                                 const char *saml_assertion_xml,
                                 const char *client_certificate_pem_filename,
                                 const char *client_key_pem_filename,
                                 const char *cloud_root_pem_filename,
                                 zc_state_callback_f callback,
                                 void *cookie_void,
                                 int64_t cookie_int,
                                 int debug,
                                 char **capabilities,
                                 int capabilities_count,
                                 int64_t customer_gid,
                                 int64_t orgid,
                                 int64_t o_location_id,
                                 int64_t o_plocation_id,
                                 char *o_identity_name,
                                 int64_t o_user_id,
                                 char* external_device_id,
                                 const char *hw_id,
                                 const char *cpu_id,
                                 const char *storage_id,
                                 const char *login_name,
                                 const char *cloud_name,
                                 const char *platform,
                                 const char *sni,
                                 const char *pub_ip,
                                 const char *client_fqdn,
                                 enum zpn_client_type client_type,
                                 enum zpn_tlv_type tlv_type,
                                 int passive,
                                 const char* username,
                                 int64_t last_user_auth_time) {
    struct zc_state *zc = NULL;
    int res;
    SSL_CTX *ssl_ctx = NULL;
    static int initialized = 0;

    ZPN_LOG(AL_INFO, "zc_state_create for '%s'", zpn_client_type_string(client_type));
    if (client_type == zpn_client_type_edge_connector || client_type == zpn_client_type_vdi) {
        if (!broker_name || !client_certificate_pem_filename || !client_key_pem_filename || !cloud_root_pem_filename) {
            ZPN_LOG(AL_CRITICAL,
                    "ec or vdi: one or more required attributes are missing: !broker_name || "
                    "!client_certificate_pem_filename || !client_key_pem_filename || !cloud_root_pem_filename");
            return NULL;
        }
    } else if (client_type == zpn_client_type_ip_anchoring) {
        if (!broker_name || !client_certificate_pem_filename || !client_key_pem_filename || !cloud_root_pem_filename ||
            !cloud_name || !sni || 0 == customer_gid) {
            ZPN_LOG(AL_CRITICAL,
                    "zpn_client_type_ip_anchoring: one or more required attributes are missing: -broker='%s' "
                    "-cert='%s' -key='%s' root='%s' "
                    "cloud_name='%s' sni='%s' customer_gid='%" PRId64 "'",
                    broker_name,
                    client_certificate_pem_filename,
                    client_key_pem_filename,
                    cloud_root_pem_filename,
                    cloud_name,
                    sni,
                    customer_gid);
            return NULL;
        }
    } else if (client_type == zpn_client_type_branch_connector) {
        if (!broker_name || !client_certificate_pem_filename || !client_key_pem_filename || !cloud_root_pem_filename ||
            !orgid || !cloud_name || !o_location_id) {
            ZPN_LOG(AL_CRITICAL,
                    "zpn_client_type_branch_connector: one or more required attributes are missing: !broker_name || "
                    "!client_certificate_pem_filename || !client_key_pem_filename || !cloud_root_pem_filename || "
                    "!orgid || !cloud_name || !o_location_id");
            return NULL;
        }
    } else if (client_type == zpn_client_type_machine_tunnel) {
        if (!broker_name || !client_certificate_pem_filename || !client_key_pem_filename || !hw_id ||
            !cloud_root_pem_filename) {
            ZPN_LOG(AL_CRITICAL,
                    "zpn_client_type_machine_tunnel: one or more required attributes are missing: !broker_name || "
                    "!client_certificate_pem_filename || !client_key_pem_filename || !hw_id || "
                    "!cloud_root_pem_filename");
            return NULL;
        }
    } else {
        if (!broker_name || !customer_domain || !saml_assertion_xml || !hw_id || !client_certificate_pem_filename ||
            !client_key_pem_filename || !cloud_root_pem_filename) {
            ZPN_LOG(AL_CRITICAL,
                    "one or more required attributes are missing: !broker_name || !customer_domain || "
                    "!saml_assertion_xml || !hw_id || !client_certificate_pem_filename || !client_key_pem_filename || "
                    "!cloud_root_pem_filename");
            return NULL;
        }
    }

    res = zpn_rpc_init();
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not register RPCs");
        return NULL;
    }

    if (__sync_fetch_and_add_4((&initialized), 1) == 0) {
        if (zpn_fohh_worker_state_init(0) != ZPN_RESULT_NO_ERROR) {
            return NULL;
        }
    }

    zc = ZPN_CALLOC(sizeof(*zc));
    if (NULL == zc) {
        return NULL;
    }
    zc->broker_name = ZPN_STRDUP(broker_name, strlen(broker_name));
    if (!zc->broker_name)
        goto FAIL_FREE;
    zc->state_cb = callback;
    zc->cookie_int = cookie_int;
    zc->cookie_void = cookie_void;
    zc->status = zc_not_connected;
    zc->lock = (pthread_mutex_t)PTHREAD_MUTEX_INITIALIZER;
    zc->outstanding_app_check_hash = zhash_table_alloc(&zpn_allocator);
    zc->outstanding_app_check_hash_lock = ZPATH_MUTEX_INIT;
    zc->next_id = 1;

    zc->customer_gid = customer_gid;
    zc->client_type = client_type;

    zc->ca_filename = cloud_root_pem_filename;
    zc->cert_filename = client_certificate_pem_filename;
    zc->key_filename = client_key_pem_filename;
    zc->customer_domain = customer_domain;
    zc->cloud_name = cloud_name;
    zc->sni = sni;
    zc->capabilities_count = capabilities_count;

    zc->orgid = orgid;
    zc->o_location_id = o_location_id;
    zc->o_plocation_id = o_plocation_id;
    zc->o_identity_name = o_identity_name;
    zc->o_user_id = o_user_id;
    zc->external_device_id = external_device_id;
    zc->passive = passive;

    zc->domain_lookup = diamond_create(0, NULL, 10);

    zc->ipv4_lookup = zradix_create();
    zc->ipv6_lookup = zradix_create();

    if (username) {
        zc->username = ZPN_STRDUP(username, strlen(username));
    }
    zc->last_auth_time = last_user_auth_time;

    zc->capabilities = ZPN_CALLOC(zc->capabilities_count * sizeof(char *));
    for (int i = 0; i < zc->capabilities_count; i++) {
        zc->capabilities[i] = ZPN_STRDUP(capabilities[i], strlen(capabilities[i]));
        ZPN_DEBUG_AUTH("capability %d. %s", i + 1, zc->capabilities[i]);
    }

    zc->fohh_thread_id = fohh_next_thread_id();
    zc->client_fqdn = client_fqdn;
    zc->fohh_thread_id = fohh_next_thread_id();

    if (hw_id) {
        zc->hw_id = ZPN_STRDUP(hw_id, strlen(hw_id));
    }

    if (client_type == zpn_client_type_zapp || client_type == zpn_client_type_zapp_partner) {
        zc->client_fqdn = client_fqdn;

        zc->saml_assertion = ZPN_STRDUP(saml_assertion_xml, strlen(saml_assertion_xml));
        if (!zc->saml_assertion)
            goto FAIL_FREE;

        zc->cpu_id = ZPN_STRDUP(cpu_id, strlen(cpu_id));
        zc->storage_id = ZPN_STRDUP(storage_id, strlen(storage_id));
        zc->login_name = ZPN_STRDUP(login_name, strlen(login_name));
        zc->platform = ZPN_STRDUP(platform, strlen(platform));

        if (!zc->hw_id || !zc->cpu_id || !zc->storage_id || !zc->login_name) {
            goto FAIL_FREE;
        }
    }

    res = zpn_zrdt_init_debug();
    if (res) {
        ZPN_DEBUG_AUTH("IGNORE: Could not add zrdt debug command: %s", zpn_result_string(res));
    }

    LIST_INIT(&(zc->udp_tlv_list));

    TAILQ_INIT(&(zc->mtunnels));
    TAILQ_INIT(&(zc->reaped));

    ZTAILQ_INIT(&(zc->apps));

    if (tlv_type == zpn_fohh_tlv) {
        res = zc_fohh_create(zc, customer_domain, cloud_name, sni, debug);
        if (res) {
            goto FAIL_FREE;
        }
    } else {
        res = zc_zrdt_create(zc, customer_domain, cloud_name, sni, debug);
        if (res) {
            goto FAIL_FREE;
        }
    }

    zc_st[zc_st_next++] = zc;

    return zc;

FAIL_FREE:
    if (zc) {
        if (zc->broker_fohh)
            fohh_connection_release(zc->broker_fohh);
        if (zc->broker_name)
            ZPN_FREE(zc->broker_name);
        if (zc->saml_assertion)
            ZPN_FREE(zc->saml_assertion);
        if (zc->cpu_id)
            ZPN_FREE(zc->cpu_id);
        if (zc->storage_id)
            ZPN_FREE(zc->storage_id);
        if (zc->login_name)
            ZPN_FREE(zc->login_name);
        if (ssl_ctx)
            SSL_CTX_free(ssl_ctx);

        if (zc->capabilities_count) {
            for (int i = 0; i < zc->capabilities_count; i++) {
                ZPN_FREE(zc->capabilities[i]);
            }
            ZPN_FREE(zc->capabilities);
        }
        ZPN_FREE(zc->o_dip);
        ZPN_FREE(zc);
    }
    return NULL;
}

int zc_client_destroy(const struct zc_state *client) {
    ZPN_LOG(AL_CRITICAL, "Implement me - zc_client_destroy");
    return ZPN_RESULT_ERR;
}

enum zc_status zc_state_get_status(const struct zc_state *client) {
    if (client)
        return client->status;
    return zc_invalid;
}


static int zc_zpn_mtunnel_request(struct zpn_tlv *tlv,
                                    int64_t conn_incarnation,
                                    int32_t tag_id,
                                    char *app_name,
                                    uint16_t ip_protocol,
                                    int32_t server_port,
                                    int64_t o_user_id,
                                    int64_t o_location_id,
                                    int64_t o_plocation_id,
                                    struct argo_inet *o_sip,
                                    struct argo_inet *o_dip,
                                    int32_t o_sport,
                                    int32_t o_dport,
                                    char *o_identity_name,
                                    struct json_cfg* g_cfg) {
    struct zpn_mtunnel_request data = {};

    data.tag_id = tag_id;
    data.app_name = app_name;
    data.tcp_server_port = server_port;
    data.ip_protocol = ip_protocol;
    data.server_port = server_port;

    data.o_user_id = o_user_id;
    data.o_location_id = o_location_id;
    data.o_plocation_id = o_plocation_id;

    data.o_sip = o_sip;

    data.o_dip = o_dip;
    data.o_sport = o_sport;
    data.o_dport = o_dport;
    data.o_identity_name = o_identity_name;

    add_sipa_test_to_mtunel(&data, g_cfg);

    if (zpn_debug_get(ZPN_DEBUG_MTUNNEL_IDX)) {
        int res = 0;
        if ((res = log_dump_structure(zpn_event_collection, zpn_mtunnel_request_description, &data, "",
                                      zpn_tlv_description(tlv), "c2c", __FUNCTION__, __FILE__, __LINE__))) {
            ZPN_LOG(AL_DEBUG, "Failed to dump struct: %s", zpn_result_string(res));
        }
    }
    int res = tlv_argo_serialize(tlv, zpn_mtunnel_request_description, &data, 0, fohh_queue_element_type_control);
    // cleanup

    ZPN_FREE_AND_NULL(data.server_group_gid);

    return res;
}

/* This basically connects up client connections if they need to
 * be connected... */
static int zc_mtunnel_state_machine(struct zc_mtunnel *mt) {
    struct zc_state *zc = mt->state;
    struct zpn_tlv *tlv;
    int res;

    if (!zc) {
        return ZPN_RESULT_ERR;
    }

    tlv = zc_get_tlv(zc);

    if (zc->status != zc_ready) {
        if (mt->status != zc_mtunnel_init) {
            zc_mtunnel_terminate(zc, mt, 1, "state machine cleanup?");
        }
        return ZPN_RESULT_NO_ERROR;
    }

    switch (mt->status) {
    case zc_mtunnel_init:
            res = zc_zpn_mtunnel_request( tlv, 0,
                                           mt->tag_id,
                                           mt->application_name,
                                           mt->ip_proto,
                                           mt->port_he,
                                           g_client_type != zpn_client_type_zapp ? mt->state->o_user_id:0, //
                                           mt->state->o_location_id,
                                           mt->state->o_plocation_id,
                                           mt->state->o_sip,
                                           mt->state->o_dip,
                                           mt->state->o_sport,
                                           mt->state->o_dport,
                                           mt->state->o_identity_name,
                                           &g_cfg);

        if (res == FOHH_RESULT_WOULD_BLOCK) {
            /* Do nothing- we'll hit this state machine again and retry. */
            ZPN_LOG(AL_NOTICE, "%s: mtunnel request retry: tag_id: %d, app: %s, status: %s, ret: %s", mt->mtunnel_id,
                    mt->tag_id, mt->application_name, zc_mtunnel_status_string(mt->status), zpath_result_string(res));
        } else if (res == FOHH_RESULT_NO_ERROR) {
            /* Wait for response.. */
            zc_mtunnel_update_state(mt, zc_mtunnel_creating, NULL);
        } else {
            /* Some other error, which is really bad... */
            ZPN_LOG(AL_ERROR, "%s: mtunnel request error: tag_id: %d, app: %s, status: %s, ret: %s", mt->mtunnel_id,
                    mt->tag_id, mt->application_name, zc_mtunnel_status_string(mt->status), zpath_result_string(res));
            zc_mtunnel_terminate(zc, mt, 1, "cant send mtunnel request");
            return res;
        }
        break;
    case zc_mtunnel_creating:
    case zc_mtunnel_ready:
        /* Do nothing- just let it flow... */
        break;
    case zc_mtunnel_closed:
        zc_mtunnel_terminate(zc, mt, 0, NULL);
        break;
    default:
        ZPN_LOG(AL_ERROR, "Implement me");
        break;
    }
    return ZPN_RESULT_NO_ERROR;
}

int zc_mtunnel_terminate(struct zc_state *zc, struct zc_mtunnel *c_mt, int terminate_fast, const char *err) {
    int res;

    zc_mtunnel_update_state(c_mt, zc_mtunnel_closed, err);

    if (c_mt->ip_proto == IPPROTO_UDP) {
        res = zpn_mconn_terminate(&(c_mt->mconn_udp_tlv.mconn), terminate_fast, 0, NULL, NULL);
    } else {
        res = zpn_mconn_terminate(&(c_mt->mconn_bufferevent.mconn), terminate_fast, 0, NULL, NULL);
    }

    if (res) {
        ZPN_LOG(AL_ERROR, "za_mtunnel_terminate for mconn_bufferevent returned %s", zpn_result_string(res));
    }

    if (c_mt->double_encrypt && c_mt->connector) {
        res = zpn_mconn_terminate(&(c_mt->mconn_fohh_tlv.mconn), terminate_fast, 0, NULL, NULL);
        if (res) {
            ZPN_LOG(AL_ERROR, "za_mtunnel_terminate for mconn_fohh_tlv returned %s", zpn_result_string(res));
        }
    }

    TAILQ_REMOVE(&(zc->mtunnels), c_mt, zc_list);
    TAILQ_INSERT_HEAD(&(zc->reaped), c_mt, zc_list);

#if 0
    /* FIXME: will implement slow free later */
    if (zpn_mconn_bufferevent_clean(&(c_mt->mconn_bufferevent)) &&
        zpn_mconn_fohh_tlv_clean(&(c_mt->mconn_fohh_tlv))) {

        zpn_client_mtunnel_log(c_mt);

        if (c_mt->mtunnel_id) ZPN_FREE(c_mt->mtunnel_id);
        if (c_mt->application_name) ZPN_FREE(c_mt->application_name);
        ZPN_FREE(c_mt);
    } else {
        ZPN_LOG(AL_ERROR, "Need to implement slow free for client mtunnel");
    }
#endif

    return res;
}

struct zc_mtunnel *zc_mtunnel_create(struct zc_state *zc,
                                     void *owner,  // struct bufferevent *bev,
                                     const char *application_name,
                                     uint16_t ip_proto,
                                     uint16_t port_he,
                                     int fohh_thread_id,
                                     int double_encrypt,
                                     zc_mtunnel_status_callback_f *state_cb,
                                     struct bufferevent *state_cb_cookie_void,
                                     int64_t state_cb_cookie_int) {
    struct zc_mtunnel *mt = NULL;
    struct zpn_udp_tlv *udp_tlv_state = NULL;
    int res;
    uint16_t port = 0;
    char addr_buf[ARGO_INET_ADDRSTRLEN];
    int sock  = 0;

    if (!zc || !application_name || !owner) {
        return NULL;
    }

    if (!zc->connected) {
        return NULL;
    }

    /* Find the udp_tlv_state */
    if (ip_proto == IPPROTO_UDP) {
        LIST_FOREACH(udp_tlv_state, &(zc->udp_tlv_list), udp_tlv_list_entry) {
            if (udp_tlv_state->port_he == port_he) {
                break;
            }
        }

        if (!udp_tlv_state) {
            ZPN_LOG(AL_ERROR, "We are not listening on UDP port %d ???", port_he);
            return NULL;
        }
    }

    mt = ZPN_CALLOC(sizeof(*mt));
    if (mt) {
        mt->self_type = 1;
        ZPN_DEBUG_MTUNNEL("Alloc mtunnel as %p, mconn_s = %p, mconn_c = %p", mt, &(mt->mconn_fohh_tlv),
                          &(mt->mconn_bufferevent));
        mt->tag_id = zc_next_id(zc);
        mt->application_name = ZPN_STRDUP(application_name, strlen(application_name));
        if (!mt->application_name) {
            goto CLEANUP;
        }
        mt->ip_proto = ip_proto;
        mt->port_he = port_he;

        mt->state = zc;
        mt->lock = (pthread_mutex_t)PTHREAD_MUTEX_INITIALIZER;

        mt->state_cb = state_cb;
        mt->state_cb_cookie_void = state_cb_cookie_void;
        mt->state_cb_cookie_int = state_cb_cookie_int;

        if (NULL == zc->o_dip) {
            zc->o_dip = ZPN_CALLOC(sizeof(struct argo_inet));
        }

        if (state_cb_cookie_void) {
            struct sockaddr addr;
            socklen_t len = sizeof(addr);
            struct sockaddr_storage local;
            struct sockaddr_storage remote;
            socklen_t l_len = sizeof(local);
            socklen_t r_len = sizeof(remote);

            sock = bufferevent_getfd(state_cb_cookie_void);
            res = getsockname(sock, (struct sockaddr *)&local, &l_len);
            if (res == 0) {
                if (res == 0) {
                    res = getpeername(sock, (struct sockaddr *)&remote, &r_len);
                    ZPN_DEBUG_MTUNNEL("getpeername returned %d", res);
                    zc->o_sport = sockaddr_get_port_he(&remote);
                }
            }
            if (0 == getsockname(sock, (struct sockaddr *)&addr, &len)) {
                argo_sockaddr_to_inet(&addr, zc->o_dip, NULL);
                        }

        } else {
            // udp
            argo_sockaddr_to_inet((struct sockaddr *)owner, zc->o_dip, &port);
            zc->o_sport = port;
        }

        argo_inet_generate(addr_buf, zc->o_dip);
        ZPN_DEBUG_MTUNNEL("o_dip=%s:%d %d", addr_buf, zc->o_sport, sock);

        /*
         * Init mconn's within mtunnel.
         */
        if (ip_proto == IPPROTO_UDP) {
            res = zpn_mconn_udp_tlv_init(&(mt->mconn_udp_tlv), mt, mconn_bufferevent_udp_c);
            if (res) {
                ZPN_LOG(AL_ERROR, "error initing udp tlv mconn");
                goto CLEANUP;
            }
            zpn_mconn_set_fohh_thread_id(&(mt->mconn_udp_tlv.mconn), fohh_thread_id);
        } else {
            res = zpn_mconn_bufferevent_init(&(mt->mconn_bufferevent), mt, mconn_bufferevent_c);
            if (res) {
                ZPN_LOG(AL_ERROR, "error initing bufferevent mconn");
                goto CLEANUP;
            }
            zpn_mconn_set_fohh_thread_id(&(mt->mconn_bufferevent.mconn), fohh_thread_id);
        }

        if (zc->tlv_type == zpn_zrdt_tlv) {
            res = zpn_mconn_zrdt_tlv_init(&(mt->mconn_zrdt_tlv), mt, mconn_zrdt_tlv_s);
            if (res) {
                ZPN_LOG(AL_ERROR, "error initing fohh_tlv mconn");
                goto CLEANUP;
            }
        } else {
            res = zpn_mconn_fohh_tlv_init(&(mt->mconn_fohh_tlv), mt, mconn_fohh_tlv_s);
            if (res) {
                ZPN_LOG(AL_ERROR, "error initing fohh_tlv mconn");
                goto CLEANUP;
            }
        }

        mt->tlv_type = zc->tlv_type;

        /* Attach self to owner...  (client side ) */
        if (ip_proto == IPPROTO_UDP) {
            res = zpn_mconn_add_global_owner(&(mt->mconn_udp_tlv.mconn), 0, mt, &(mt->tag_id), sizeof(mt->tag_id),
                                             &client_global_call_set);
        } else {
            res = zpn_mconn_add_global_owner(&(mt->mconn_bufferevent.mconn), 0, mt, &(mt->tag_id), sizeof(mt->tag_id),
                                             &client_global_call_set);
        }

        if (res) {
            ZPN_LOG(AL_ERROR, "Error adding global client owner: %s", zpn_result_string(res));
            goto CLEANUP;
        }

        /* Attach self to owner...  (server side ) */
        res = zpn_mconn_add_global_owner(
                (zc->tlv_type == zpn_zrdt_tlv) ? &(mt->mconn_zrdt_tlv.mconn) : &(mt->mconn_fohh_tlv.mconn),
                0,
                mt,
                &(mt->tag_id),
                sizeof(mt->tag_id),
                &server_global_call_set);
        if (res) {
            ZPN_LOG(AL_ERROR, "Error adding global server owner: %s", zpn_result_string(res));
            goto CLEANUP;
        }

        /* Save client side bufferevent so we can attach later */
        if (ip_proto == IPPROTO_UDP) {
            mt->mconn_udp_tlv.peer_sockaddr = *(struct sockaddr_storage *)owner;
            mt->mconn_udp_tlv.local_port = port_he;
            mt->mconn_udp_tlv.peer_port = sockaddr_get_port_he(&(mt->mconn_udp_tlv.peer_sockaddr));

        } else {
            mt->bev = (struct bufferevent *)owner;
        }

        /* Based on some policy, we decide if inner tunnel is required */

        TAILQ_INSERT_HEAD(&(zc->mtunnels), mt, zc_list);

        mt->status = zc_mtunnel_init;
        mt->double_encrypt = double_encrypt;
        mt->udp_tlv_state = udp_tlv_state;

        if (zc_mtunnel_state_machine(mt)) {
            /* Something failed immediately... */
            ZPN_LOG(AL_ERROR, "Error running state machine, immediately: %s", zpn_result_string(res));
            goto CLEANUP;
        }
    }
    return mt;

CLEANUP:
    if (mt) {
        zc_mtunnel_terminate(zc, mt, 1, "some error");
    }
    return NULL;
}

static int global_bind_server_cb(void *mconn_base,
                                 void *mconn_self,
                                 void *global_owner,
                                 void *global_owner_key,
                                 size_t global_owner_key_length,
                                 int64_t *global_owner_incarnation) {
    struct zc_mtunnel *mt = mconn_self;
    struct zc_state *zc = mt->state;
    int res;

    ZPN_DEBUG_MTUNNEL( "Binding mconn with tag=%ld", *((long *)global_owner_key));

    res = argo_hash_store(zc->mtunnels_by_tag, global_owner_key, global_owner_key_length, 1, mt);

    *global_owner_incarnation = mt->incarnation;

    return res;
}

static int global_unbind_server_cb(void *mconn_base,
                                   void *mconn_self,
                                   void *global_owner,
                                   void *global_owner_key,
                                   size_t global_owner_key_length,
                                   int64_t global_owner_incarnation,
                                   int drop_buffered_data,
                                   int dont_propagate,
                                   const char *err) {
    const struct zc_mtunnel *mt = mconn_self;
    struct zc_state *zc = mt->state;
    int res;

    ZPN_DEBUG_MTUNNEL("unbind mtunnel with tag_id=%d", *((int32_t *)global_owner_key));

    res = argo_hash_remove(zc->mtunnels_by_tag, global_owner_key, global_owner_key_length, NULL);
    return res;
}

static int global_bind_client_cb(void *mconn_base,
                                 void *mconn_self,
                                 void *global_owner,
                                 void *global_owner_key,
                                 size_t global_owner_key_length,
                                 int64_t *global_owner_incarnation) {
    struct zc_mtunnel *mt = mconn_self;

#if 0
    if (mt->attached_to_client) {
        ZPN_LOG(AL_ERROR, "Binding bound connection");
    } else {
        //mt->request_id = *((int64_t *) global_owner_key);
    }
    mt->attached_to_client = 1;
#endif

    *global_owner_incarnation = mt->incarnation;

    return ZPN_RESULT_NO_ERROR;
}

static int global_unbind_client_cb(void *mconn_base,
                                   void *mconn_self,
                                   void *global_owner,
                                   void *global_owner_key,
                                   size_t global_owner_key_length,
                                   int64_t global_owner_incarnation,
                                   int drop_buffered_data,
                                   int dont_propagate,
                                   const char *err) {
#if 0
    struct zc_mtunnel *mt = mconn_self;

    if (mt->attached_to_client) {
        //mt->request_id = 0;
    } else {
        ZPN_LOG(AL_ERROR, "Unbinding unbound connection");
    }
    mt->attached_to_client = 0;
#endif

    return ZPN_RESULT_NO_ERROR;
}

static void global_lock_cb(void *mconn_base,
                           void *mconn_self,
                           void *global_owner,
                           void *global_owner_key,
                           size_t global_owner_key_length) {
#if 0
    struct zc_mtunnel *mt = mconn_self;
    struct zc_state *zc = mt->state;

    pthread_mutex_lock(&(zc->lock));
#endif
}

static void global_unlock_cb(void *mconn_base,
                             void *mconn_self,
                             void *global_owner,
                             void *global_owner_key,
                             size_t global_owner_key_length) {
#if 0
    struct zc_mtunnel *mt = mconn_self;
    struct zc_state *zc = mt->state;

    pthread_mutex_unlock(&(zc->lock));
#endif
}

static void global_terminate_cb(void *mconn_base,
                                void *mconn_self,
                                void *global_owner,
                                void *global_owner_key,
                                size_t global_owner_key_length,
                                char *error) {
    struct zc_mtunnel *mt = mconn_self;

    zc_mtunnel_terminate(mt->state, mt, 0, "global terminate");
}

static int global_ip_proto_cb(void *mconn_base,
                              void *mconn_self,
                              void *global_owner,
                              void *global_owner_key,
                              size_t global_owner_key_length) {
    struct zc_mtunnel *mt = mconn_self;

    return mt->ip_proto;
}

static int global_double_encrypt_cb(void *mconn_base,
                                    void *mconn_self,
                                    void *global_owner,
                                    void *global_owner_key,
                                    size_t global_owner_key_length) {
    const struct zc_mtunnel *mt = mconn_self;

    return (int)mt->double_encrypt;
}

static struct zpn_mconn *global_outer_mconn_cb(void *mconn_base,
                                               void *mconn_self,
                                               void *global_owner,
                                               void *global_owner_key,
                                               size_t global_owner_key_length) {
    struct zc_mtunnel *mt = mconn_self;
    struct zpn_mconn *outer_mconn = (struct zpn_mconn *)mconn_base;

    if (mt->double_encrypt && mt->connector) {
        const struct zpn_connector_tun *conn = (struct zpn_connector_tun *)mt->connector;

        if (mconn_base == &conn->mconn_c.mconn) {
            outer_mconn = &(mt->mconn_fohh_tlv.mconn);
        } else if (mconn_base == &conn->mconn_s.mconn) {
            if (mt->ip_proto == IPPROTO_UDP) {
                outer_mconn = &(mt->mconn_udp_tlv.mconn);
            } else {
                outer_mconn = &(mt->mconn_bufferevent.mconn);
            }
        }
    }

    return outer_mconn;
}

static int64_t global_incarnation_cb(void *mconn_base,
                                     void *mconn_self,
                                     void *global_owner,
                                     void *global_owner_key,
                                     size_t global_owner_key_length) {
    const struct zc_mtunnel *mtunnel = mconn_self;

    return mtunnel->incarnation;
}

static int global_validate_incarnation_cb(void *mconn_base,
                                          void *mconn_self,
                                          void *global_owner,
                                          void *global_owner_key,
                                          size_t global_owner_key_length,
                                          int64_t original_incarnation) {
    struct zc_mtunnel *mtunnel = mconn_self;

    if (mtunnel->incarnation == original_incarnation) {
        return 1;
    } else {
        return 0;
    }
}

static char *global_mtunnel_id(void *mconn_base,
                               void *mconn_self,
                               void *global_owner,
                               void *global_owner_key,
                               size_t global_owner_key_length) {
    struct zc_mtunnel *mtunnel = mconn_self;

    return mtunnel->mtunnel_id;
}

/*
 * Locking: assume we have the lock of mtunnel
 */
int zpn_client_mtunnel_log(struct zc_mtunnel *mtunnel) {
    int res = ZPN_RESULT_NO_ERROR;

    mtunnel->log.mtunnel_id = mtunnel->mtunnel_id;

    mtunnel->log.c_rxbytes = mtunnel->mconn_bufferevent.mconn.bytes_to_peer;
    mtunnel->log.c_txbytes = mtunnel->mconn_bufferevent.mconn.bytes_to_client;
    mtunnel->log.a_rxbytes = mtunnel->mconn_fohh_tlv.mconn.bytes_to_peer;
    mtunnel->log.a_txbytes = mtunnel->mconn_fohh_tlv.mconn.bytes_to_client;

    res = zpn_log_structure(zpn_trans_log_description, &(mtunnel->log));

    if (res == ARGO_RESULT_NO_ERROR) {
        ZPN_DEBUG_MTUNNEL( "zpn client mtunnel log success");
    } else {
        ZPN_LOG(AL_ERROR, "zpn client mtunnel log fail");
    }

    return res;
}

int zc_inner_tunnel_ssl_ctx_init(const char *root_pem_filename,
                                 const char *certificate_pem_filename,
                                 const char *key_pem_filename) {
    ssl_ctx_connector =
            zpn_connector_client_ssl_ctx_create(root_pem_filename, certificate_pem_filename, key_pem_filename);
    if (ssl_ctx_connector) {
        return ZPN_RESULT_NO_ERROR;
    } else {
        return ZPN_RESULT_ERR;
    }
}
/*
 * Event callback for zpn_udp_tlv
 */
static void zc_udp_tlv_event_callback(evutil_socket_t fd, short int what, void *pargs) {
    struct zpn_udp_tlv *udp_tlv = pargs;
    struct sockaddr_storage from_addr;
    unsigned int from_addr_len;
    char *data = NULL;
    int bytes = 0;
    char addr_buf[ARGO_INET_ADDRSTRLEN];
    struct evbuffer *evbuf = NULL;
    struct zpn_mconn_udp_tlv *mconn_udp_tlv;
    int res;

    data = ZPN_CALLOC(MAX_UDP_PACKET);
    if (!data)
        return;

    memset((char *)&from_addr, 0, sizeof(struct sockaddr_storage));

    from_addr_len = sizeof(from_addr);

    if ((bytes = recvfrom(udp_tlv->udp_socket, data, MAX_UDP_PACKET, 0, (struct sockaddr *)&from_addr,
                          &from_addr_len)) == -1) {
        goto exit;
    }

    inet_ntop(AF_INET, &(((struct sockaddr_in *)&from_addr)->sin_addr), addr_buf, ARGO_INET_ADDRSTRLEN);

    ZPN_DEBUG_MCONN("zpn_udp_tlv_event_callback(), fd is %d, received %d bytes, buffer is %s, from %s:%d\n", (int)fd,
                    bytes, data, addr_buf, sockaddr_get_port_he(&from_addr));

    mconn_udp_tlv = zc_mconn_udp_tlv_lookup(udp_tlv, from_addr);
    if (!mconn_udp_tlv) {
        ZPN_DEBUG_MCONN("Cannot find mconn_udp_tlv for data, create new one");
        (*udp_tlv->request_cb)(from_addr, udp_tlv->port_he + port_offset_local, udp_tlv->fohh_thread_id);

        mconn_udp_tlv = zc_mconn_udp_tlv_lookup(udp_tlv, from_addr);
    } else {
        ZPN_DEBUG_MCONN("Found mconn_udp_tlv for data, pass along the data");

        mconn_udp_tlv->last_rx_epoch_s = epoch_s();
        mconn_udp_tlv->data_arrived += bytes;

        evbuf = udp_packetize(data, bytes, mconn_udp_tlv->peer_port, mconn_udp_tlv->local_port);
        if (evbuf) {
#if 0 /* UDP framing test: add extra bytes */
            if (1) {
                char extra[10];

                memset(extra, 0, 10);
                evbuffer_add(evbuf, extra, 10);
                bytes += 10;
            }
#endif

            mconn_udp_tlv->callbacks++;
            mconn_udp_tlv->data_to_peer_attemp += bytes;

            res = zpn_client_process_rx_data(&(mconn_udp_tlv->mconn), evbuf, evbuffer_get_length(evbuf), NULL, NULL);
            if (res == ZPN_RESULT_ASYNCHRONOUS) {
                res = ZPN_RESULT_NO_ERROR;
            }
        } else {
            ZPN_DEBUG_MCONN("udp tlv receive, Cannot get packet");
        }
    }

#if 0
    /* XXX FIXME temporarily echo back the data */
    if (sendto(udp_tlv->udp_socket, data, bytes, 0, (struct sockaddr*)&from_addr, from_addr_len) == -1)
    {
        ZPN_DEBUG_MCONN("Cannot send data");
    } else {
        ZPN_DEBUG_MCONN("Sent back %d bytes\n", bytes);
    }
#endif

exit:
    if (data)
        ZPN_FREE(data);
    /* Need to free the evbuf since we are done with it */
    if (evbuf)
        evbuffer_free(evbuf);

    return;
}

/*
 * Here we create and bind a socket to the given address and set up event callbacks
 */
int zc_udp_tlv_listen(struct zpn_udp_tlv *udp_tlv,
                      struct argo_inet *inet,
                      uint16_t port_he,
                      int fohh_thread_id,
                      zpn_udp_tlv_request_cb *request_cb) {
    int sflags;
    struct sockaddr_storage addr;
    struct event_base *base = fohh_get_thread_event_base(fohh_thread_id);
    char addr_buf[ARGO_INET_ADDRSTRLEN];
    socklen_t addr_len;

    memset(&addr, 0, sizeof(addr));

    udp_tlv->inet = *inet;
    udp_tlv->port_he = port_he - port_offset_local;
    udp_tlv->fohh_thread_id = fohh_thread_id;
    udp_tlv->request_cb = request_cb;

    udp_tlv->udp_socket = socket(AF_INET, SOCK_DGRAM, IPPROTO_UDP);
    if (udp_tlv->udp_socket == -1) {
        ZPN_LOG(AL_NOTICE, "Cannot allocate UDP socket - %s", strerror(errno));
        return ZPN_RESULT_ERR;
    }

    sflags = fcntl(udp_tlv->udp_socket, F_GETFL, 0);

    /* Set flags in non-blocking mode */
    if (sflags < 0) {
        ZPN_DEBUG_MCONN("Cannot get socket flag");
        return ZPN_RESULT_ERR;
    }

    if (fcntl(udp_tlv->udp_socket, F_SETFL, sflags | O_NONBLOCK) < 0) {
        ZPN_DEBUG_MCONN("Cannot set socket flag");
        return ZPN_RESULT_ERR;
    }

    argo_inet_generate(addr_buf, inet);
    ZPN_DEBUG_MCONN("zpn_udp_tlv_listen(), listening on %s:%d", addr_buf, port_he);

    argo_inet_to_sockaddr(inet, (struct sockaddr *)&addr, &addr_len, htons(port_he));

    /* bind socket to port */
    if (bind(udp_tlv->udp_socket, (struct sockaddr *)&addr, sizeof(struct sockaddr)) == -1) {
        ZPN_LOG(AL_WARNING, "Cannot bind socket");
        return ZPN_RESULT_ERR;
    }

    udp_tlv->ev = event_new(base, udp_tlv->udp_socket, EV_READ | EV_PERSIST, zc_udp_tlv_event_callback, udp_tlv);
    event_add(udp_tlv->ev, NULL);

    return ZPN_RESULT_NO_ERROR;
}

int zc_udp_server_listen(struct zc_state *zc,
                         struct argo_inet *inet,
                         uint16_t port_he,
                         int fohh_thread_id,
                         zc_udp_request_cb *request_cb) {
    struct zpn_udp_tlv *udp_tlv_state = NULL;

    LIST_FOREACH(udp_tlv_state, &(zc->udp_tlv_list), udp_tlv_list_entry) {
        if (udp_tlv_state->port_he == port_he) {
            /* We are already listening on the port */
            return ZPN_RESULT_NO_ERROR;
        }
    }

    udp_tlv_state = ZPN_CALLOC(sizeof(struct zpn_udp_tlv));

    if (zpn_udp_tlv_init(udp_tlv_state)) {
        ZPN_LOG(AL_CRITICAL, "Could not init UDP tlv");
        ZPN_FREE(udp_tlv_state);
        return ZPN_RESULT_ERR;
    }

    LIST_INSERT_HEAD(&(zc->udp_tlv_list), udp_tlv_state, udp_tlv_list_entry);

    return zc_udp_tlv_listen(udp_tlv_state, inet, port_he, fohh_thread_id, request_cb);
}

static int zc_state_app_dns_check(struct zc_state *zc,
                                  char *domain,
                                  char *type,
                                  int is_app_check)
{
    int res = ZPN_RESULT_NO_ERROR;
    static int64_t app_count = 0;
    static int64_t dns_count = 0;
    struct zpn_tlv *tlv = zc_get_tlv(zc);

    pthread_mutex_lock(&(zc->lock));
    if (zc->status == zc_ready) {
        if (zc->broker_fohh) {
            if (is_app_check) {
                struct zpn_app_client_check check_out;
                memset(&check_out, 0, sizeof(check_out));
                check_out.id = __sync_add_and_fetch_8(&app_count, 1);
                check_out.name = domain;
                check_out.type = type;
                check_out.strict = g_dns_strict;
                res = zpn_send_zpn_app_client_check(tlv,
                                                    0,       // FOHH INCARNATION
                                                    &check_out);

                if (zpn_debug_get(ZPN_DEBUG_APPLICATION_IDX)) {
                    dump_structure(
                            zpn_app_client_check_description, &check_out, "Sn.", __FUNCTION__, __FILE__, __LINE__);
                }
            } else {
                struct zpn_dns_client_check check_out;
                memset(&check_out, 0, sizeof(check_out));
                check_out.id = __sync_add_and_fetch_8(&dns_count, 1);
                check_out.name = domain;
                check_out.type = type;
                check_out.strict = g_dns_strict;

                res = zpn_send_zpn_dns_client_check(tlv,
                                                    0,       // FOHH INCARNATION
                                                    &check_out);

                if (zpn_debug_get(ZPN_DEBUG_APPLICATION_IDX)) {
                    dump_structure(
                            zpn_dns_client_check_description, &check_out, "Sn.", __FUNCTION__, __FILE__, __LINE__);
                }
            }
            if (!res) res = ZPN_RESULT_ASYNCHRONOUS;
        } else {
            res = ZPN_RESULT_ERR;
        }
    } else {
        res = ZPN_RESULT_NOT_READY;
    }
    pthread_mutex_unlock(&(zc->lock));
    return res;
}


int zc_state_dns_check(struct zc_state *zc,
                       char *domain,
                       char *type)
{
    return zc_state_app_dns_check(zc, domain, type, 0);
}

int zc_state_app_check(struct zc_state *zc, char *domain, char *type, uint8_t strict) {
    return zc_state_app_dns_check(zc, domain, type, 1);
}

int zc_state_register_app_create_callback(struct zc_state *zc, zc_app_create_callback *app_create_callback) {
    zc->app_create = app_create_callback;
    return ZPN_RESULT_NO_ERROR;
}

static void accept_callback_generic_tcp(struct evconnlistener *ev_listener,
                                        evutil_socket_t sock,
                                        struct sockaddr *addr,
                                        int len,
                                        void *cookie) {
    const struct zpn_generic_tcp_listener *listener = cookie;
    struct event_base *ev_base = fohh_get_thread_event_base(listener->fohh_thread_id);
    struct bufferevent *bev;

    bev = bufferevent_socket_new(ev_base, sock, BEV_OPT_CLOSE_ON_FREE);
    if (!bev) {
        ZPN_LOG(AL_DEBUG, "Could not create bev\n");
        return;
    }

    if (listener->request_cb_generic_tcp) {
        (*listener->request_cb_generic_tcp)(bev, listener->listening_port, listener->fohh_thread_id);
    } else {
        ZPN_LOG(AL_ERROR, "accept_callback_generic_tcp() - no callback?");
        bufferevent_free(bev);
    }
}

int zc_generic_tcp_listen(struct zc_state *zc,
                          struct argo_inet *inet,
                          uint16_t port_he,
                          int fohh_thread_id,
                          zc_generic_tcp_request_cb *request_cb) {
    struct evconnlistener *ev_listener = NULL;
    struct event_base *ev_base = fohh_get_thread_event_base(fohh_thread_id);
    struct sockaddr_storage addr;
    socklen_t addr_len = sizeof(addr);
    char ipstr[1000];

    int ix = __sync_add_and_fetch_4(&(zc->tcp_listen_count), 1);
    if (ix >= MAX_LISTEN) {
        ZPN_LOG(AL_ERROR, "Too many listeners");
        return ZPN_RESULT_ERR;
    }
    zc->tcp_listen[ix].ev_listener = ev_listener;
    zc->tcp_listen[ix].fohh_thread_id = fohh_thread_id;
    zc->tcp_listen[ix].listening_port = port_he;
    zc->tcp_listen[ix].request_cb_generic_tcp = request_cb;
    zc->tcp_listen[ix].zc = zc;

    argo_inet_to_sockaddr(inet, (struct sockaddr *)&addr, &addr_len, htons(port_he));

    ev_listener = evconnlistener_new_bind(ev_base,
                                          accept_callback_generic_tcp,
                                          &(zc->tcp_listen[ix]),
                                          LEV_OPT_CLOSE_ON_FREE | LEV_OPT_REUSEABLE,
                                          1024,
                                          (struct sockaddr *)&addr,
                                          addr_len);
    if (!ev_listener) {
        ZPN_LOG(AL_ERROR, "generic tcp listener for ip=%s, port=%d : Failed", argo_inet_generate(ipstr, inet), port_he);
        return ZPN_RESULT_ERR;
    }

    ZPN_LOG(AL_DEBUG, "generic tcp listener for ip=%s, port=%d: Success", argo_inet_generate(ipstr, inet), port_he);
    return ZPN_RESULT_NO_ERROR;
}

struct zpn_client_app *zc_is_registered_app(struct zc_state *zc, const char *domain, int16_t port) {
    struct zc_application *zc_app;

    ZTAILQ_FOREACH(zc_app, &(zc->apps), entry) {
        if (zc_app->app.app_domain) {
            if (!strncmp(zc_app->app.app_domain, domain, strlen(domain))) {
                int i;

                if (zc_app->app.ports_count) {
                    for (i = 0; i < (zc_app->app.ports_count / 2); i++) {
                        if ((zc_app->app.ingress_ports[2 * i] <= port) &&
                            ((zc_app->app.ingress_ports[2 * i + 1] >= port))) {
                            return &(zc_app->app);
                        }
                    }
                }

                if (zc_app->app.port_ranges_count) {
                    for (i = 0; i < (zc_app->app.port_ranges_count / 2); i++) {
                        if ((zc_app->app.ingress_port_ranges[2 * i] <= port) &&
                            ((zc_app->app.ingress_port_ranges[2 * i + 1] >= port))) {
                            return &(zc_app->app);
                        }
                    }
                }

                if (zc_app->app.tcp_port_ranges_count) {
                    for (i = 0; i < (zc_app->app.tcp_port_ranges_count / 2); i++) {
                        if ((zc_app->app.tcp_port_ranges[2 * i] <= port) &&
                            ((zc_app->app.tcp_port_ranges[2 * i + 1] >= port))) {
                            return &(zc_app->app);
                        }
                    }
                }

                if (zc_app->app.udp_port_ranges_count) {
                    for (i = 0; i < (zc_app->app.udp_port_ranges_count / 2); i++) {
                        if ((zc_app->app.udp_port_ranges[2 * i] <= port) &&
                            ((zc_app->app.udp_port_ranges[2 * i + 1] >= port))) {
                            return &(zc_app->app);
                        }
                    }
                }
            }
        }
    }

    return NULL;
}

int zpn_client_register_client_broker_app(struct zc_state *client, const char *app_domain) {
    struct zpn_tlv *tlv = zc_get_tlv(client);
    int res = ZPN_RESULT_NO_ERROR;

    if (tlv) {
        log_msg_time("zpn_client_broker_app_registration", app_domain, 0);
        res = zpn_send_zpn_client_broker_app_registration(tlv,
                                                          zpn_tlv_conn_incarnation(tlv),
                                                          //  client->sni,
                                                          app_domain);
    }

    return res;
}
