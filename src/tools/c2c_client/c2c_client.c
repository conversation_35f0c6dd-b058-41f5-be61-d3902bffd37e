/*
 * c2c_client.c. Copyright (C) 2021-2024 Zscaler, Inc. All Rights Reserved.
 */

/*
 * A simple zpn client implementation
 *
 * Listens on a local port, and anything that connects through that
 * port uses a static application.
 *
 *
 *
 */
#include "argo/argo_hash.h"
#include "avl/avl.h"
#include <ctype.h>
#include <errno.h>
#include <fcntl.h>
#include "fohh/fohh.h"
#include "fohh/fohh_http.h"
#include <pthread.h>
#include <signal.h>
#include <stdarg.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <sys/time.h>
#include <unistd.h>
#include "wally/wally.h"
#include "wally/wally_fohh_client.h"
#include "wally/wally_fohh_server.h"
#include "wally/wally_postgres.h"
#include "zpath_lib/zpath_app.h"
#include "zpath_lib/zpath_debug.h"
#include "zpath_lib/zpath_domain_lookup.h"
#include "zpath_lib/zpath_local.h"
#include "zpath_misc/zpath_misc.h"
#include "zpn/zpn_lib.h"
#include "zpn/zpn_rpc.h"
#include "zpath_misc/zpath_version.h"
#include "tools/c2c_client/c2c_client_impl.h"
#include "tools/c2c_client/c2c_data.h"
#include "fohh/fohh_private.h"
#include "tools/c2c_client/zc_app_local.h"
#include "zudp_conn/zudp_conn.h"
#include <sys/stat.h>
#include "argo/argo_private.h"

#define RED "\033[31;3m"
#define GRN "\033[32;3m"
#define YEL "\033[33;3m"
#define BLU "\033[34;3m"
#define PRP "\033[35;3m"
#define LBL "\033[36;3m"
#define RST "\033[0m"

#define MAX_APP_NAME_LEN     256

struct zpn_assistant_state global_assistant;
int number_of_apps_matched = 0;

int config_daemon = 0;

FILE* fp_time_msgs;
int64_t start_time_msgs_s;

char *ca_filename = NULL;
char *cert_filename = NULL;
char *key_filename = NULL;
char *inner_ca_filename = NULL;
char *broker_name = NULL;
char *customer_domain = NULL;
char *saml_filename = NULL;
int do_debug = 0;
int debuglog = 0;
int quiet = 0;
int debug_port = 8700;
int time_msgs = 0;
FILE* fp_time_msgs;
int64_t start_time_msgs_s;
int g_random_app = 0;

struct json_cfg g_cfg ;

// the app id prefix sent as part of zpn_client_register_client_to_client_app
// every zapp would have unique id: <prefix>.<app domain>
char *c2c_app_id_prefix = NULL;
char *c2c_app_id_request = NULL;
char *c2c_ip_request = NULL;

const char *username = NULL;
int64_t user_last_auth_time = 0;

int64_t orgid = 0;
int64_t o_location_id;
int64_t o_plocation_id;
int64_t o_user_id;
char *o_identity_name;
char *external_device_id;

u_int32_t si_device_id;
u_int16_t si_device_trust;
char *zia_cloud;
int no_zia_identity;
int g_passive = 0;

int send_trusted_nets = 1;

char *custom_sni_str = NULL;
int to_edge_connector = 0;
int to_zapp_partner = 0;
int to_browser_isolation = 0;
int to_ip_anchoring = 0;
int to_branch_connector = 0;
int to_machine_tunnel = 0;
static int64_t mtunnels_count = 0;
int to_vdi = 0;

int64_t customer_gid = 0;

long async_count = 1;
long client_count = 1;
int clients_connected = 0;
int clients_launched = 0;
int dont_start_apps = 0;

uint16_t port_offset_local = 0;
uint16_t no_fohh_status = 0;
int no_broker_req_ack = 0;

long thread_count = 1;

char saml_assertion[256 * 1024];

uint64_t zpn_debug_flags = 0;
#define MAX_ZPN_DEBUG_NAMES_COUNT (100)
const char *zpn_debug_names_local[MAX_ZPN_DEBUG_NAMES_COUNT];
size_t zpn_debug_names_count;

enum zpn_client_type g_client_type = zpn_client_type_zapp;

const char *service_ip = "127.0.0.1";
uint16_t app_port = 443;
int sock1 = 0;
int sock2 = 0;
const char *logfile = NULL;
const char *hw_id = NULL;
const char *cpu_id = NULL;
const char *storage_id = NULL;
const char *login_name = NULL;
const char *cloud_name = NULL;
const char *platform = NULL;

const char *config_ip_proxy_ip = "127.0.0.1";
uint16_t config_ip_proxy_port_he = 8699;
int config_ip_proxy = 0;

char *capabilities_matrix[ZPN_CLIENT_CAPABILITY_MAX_COUNT];
int capabilities_count = 0;
int capabilities = 0;

enum zpn_tlv_type broker_tlv_type = zpn_fohh_tlv;
static struct fohh_http_server *http_server;
struct wally_interlock interlock;

struct zc_state* g_zc[MAX_NUMBER_OF_CLIENTS];
char *apps_file = NULL;
const char *fohh_debug_names2[] = FOHH_DEBUG_NAMES;

static int next_state_index = 0;
static pthread_mutex_t next_state_lock ;

// there could be only one client domain !
char *client_to_client_app = NULL;  //"c2c.company.com";

// if 1 send dns request only , dont start mtunnel
int g_dns_only = 0;
int g_dns_strict = 0;
int g_dns_check = 1;

int capability_no_domain_download = 0;
int capability_no_domain_download2 = 0;
int capability_no_ip_download = 0;
int capability_latency_probe = 0;
int capability_mtn = 0;

int parse_args(int argc, char *argv[]);
int usage(const char *argv0);
static int zpn_client_app_create(const char *domain_name, int tcp_port, int udp_port, int double_encrypt);
// **************************************************************
static int client_register_c2c(struct zc_state *client, const char *app) {
    if (NULL == c2c_app_id_prefix || NULL == app) {
        ZPN_DEBUG_AUTH("C2C prefix or app name is not provided, C2C is not regstered");
        return ZPN_RESULT_NOT_FOUND;
    }

    ZPN_LOG(AL_INFO, "register for client_fqdn=%s app=%s", c2c_app_id_prefix, app);

    char buf[500];
    char *s = buf;
    char *e = s + sizeof(buf);

    if (app) {
        // prepand
        sxprintf(s, e, "%s%s", c2c_app_id_prefix, app);
        ZPN_DEBUG_AUTH("register C2C app '%s'", s);

        zpn_client_register_client_broker_app(client, s);
    }

    return ZPN_RESULT_NO_ERROR;
}

/*
 * Callback called as zc_client status changes.
 */
int client_callback(struct zc_state *client,
                    void *cookie_void,
                    int64_t cookie_int,
                    enum zc_status status,
                    const char *error_string) {
    ZPN_DEBUG_AUTH("ZPN client status now '%s'. %s", zc_status_string(status), error_string ? error_string : "");

    if (status != zc_ready) {
        return ZPN_RESULT_NO_ERROR;
    }

    int count = __sync_add_and_fetch_4(&clients_connected, 1);
    int launched = clients_launched;


    char client_fqdn[500];
    char *s = client_fqdn;
    char *e = s + sizeof(client_fqdn);

    // only register C2C if ZCC or machine tunnel
    if (client->client_type == zpn_client_type_zapp || client->client_type == zpn_client_type_machine_tunnel) {
        if (c2c_app_id_prefix && client_to_client_app) {
            sxprintf(s, e, "%s%s", c2c_app_id_prefix, client_to_client_app);
            (void)client_register_c2c(client, client_to_client_app);
        } else {
            ZPN_LOG(AL_INFO,  "c2c_app_id_prefix is not provided, did not register for c2c");
        }
    } else {
        ZPN_LOG(AL_INFO, "client->client_type is not ZCC, did not register for c2c");
    }

    if (count == client_count) {
        wally_interlock_release(&interlock);
    }
    if (launched < client_count) {
        ZPN_LOG(AL_NOTICE, "ZPN client- %ld out of %ld ready, launched = %ld", (long)count, (long)client_count,
                (long)launched);
        __sync_add_and_fetch_4(&clients_launched, 1);
        g_zc[clients_launched - 1] = zc_state_create(broker_name,
                                                     customer_domain,
                                                     saml_assertion,
                                                     cert_filename,
                                                     key_filename,
                                                     ca_filename,
                                                     client_callback,
                                                     NULL,
                                                     1,
                                                     do_debug,
                                                     capabilities_matrix,
                                                     capabilities_count,
                                                     customer_gid,
                                                     orgid,
                                                     o_location_id,
                                                     o_plocation_id,
                                                     o_identity_name,
                                                     o_user_id,
                                                     external_device_id,
                                                     hw_id,
                                                     cpu_id,
                                                     storage_id,
                                                     login_name,
                                                     cloud_name,
                                                     platform,
                                                     custom_sni_str,
                                                     NULL,  // pub_ip not used
                                                     client_fqdn,
                                                     client->client_type,  // it is already determined
                                                     broker_tlv_type,
                                                     g_passive,
                                                     username,
                                                     user_last_auth_time);
        if (g_zc[clients_launched - 1]) {
            if (g_zc[clients_launched - 1]) {
                g_zc[clients_launched - 1]->si_device_id = ++si_device_id;
                g_zc[clients_launched - 1]->si_device_trust = ++si_device_trust;

                g_zc[clients_launched - 1]->mtunnels_by_tag = argo_hash_alloc(7, 1);
                zc_state_register_app_create_callback(g_zc[clients_launched - 1], zpn_client_app_create);
            }
        }
    }
    return ZPN_RESULT_NO_ERROR;
}
// **************************************************************
int generic_status_bev(struct zc_mtunnel *mtunnel,
                       void *cookie_void,
                       int64_t cookie_int,
                       enum zc_mtunnel_status status,
                       const char *error_string) {
    struct bufferevent *bev = cookie_void;
    int sock;

    struct sockaddr_storage local;
    struct sockaddr_storage remote;
    socklen_t l_len = sizeof(local);
    socklen_t r_len = sizeof(remote);
    uint16_t local_port;
    uint16_t remote_port;
    char desc[1000];
    int res;

    if (!bev) {
        return 0;
    }

    sock = bufferevent_getfd(bev);

    res = getsockname(sock, (struct sockaddr *)&local, &l_len);
    if (res == 0) {
        res = getpeername(sock, (struct sockaddr *)&remote, &r_len);
        if (res == 0) {
            char remote_ip_str[ARGO_INET_ADDRSTRLEN];
            char local_ip_str[ARGO_INET_ADDRSTRLEN];

            fohh_sockaddr_storage_to_str(&local, local_ip_str, sizeof(local_ip_str));
            fohh_sockaddr_storage_to_str(&remote, remote_ip_str, sizeof(remote_ip_str));
            local_port = sockaddr_get_port_he(&local);
            remote_port = sockaddr_get_port_he(&remote);

            snprintf(desc, sizeof(desc), "%s:%d->%s->%d status = %s, err = %s", remote_ip_str, remote_port,
                     local_ip_str, local_port, zc_mtunnel_status_string(status), error_string ? error_string : "NULL");
        } else {
            snprintf(desc, sizeof(desc), "mt_status: Could not get address");
        }
    } else {
        snprintf(desc, sizeof(desc), "mt_status: Could not get address");
    }

    if (status == zc_mtunnel_closed) {
        if (error_string) {
            ZPATH_LOG(AL_ERROR, "%s: Closing connection %s", desc, error_string);
        }
#if 0
        bufferevent_free(bev);
#endif  // 0
    } else {
        ZPN_DEBUG_MTUNNEL("%s", desc);
    }

    return 0;
}
int zpn_client_add_outstanding_app_client_check(struct zc_state *zc,
                                                char *domain,
                                                struct bufferevent *bev,
                                                int fohh_thread_id,
                                                int port,
                                                int double_encrypt) {
    int res = ZPN_RESULT_NO_ERROR;
    if (!zc || !domain)
        return ZPN_RESULT_BAD_ARGUMENT;

    ZPATH_MUTEX_LOCK(&(zc->outstanding_app_check_hash_lock), __FILE__, __LINE__);

    struct outstanding_app_check *out_app =
            zhash_table_lookup(zc->outstanding_app_check_hash, domain, strlen(domain), NULL);

    if (!out_app) {
        out_app = ZPN_CALLOC(sizeof(*out_app));
        if (!out_app) {
            res = ZPN_RESULT_NO_MEMORY;
            goto END;
        }

        out_app->lookup_domain = ZPN_STRDUP(domain, strlen(domain));
        out_app->bev = bev;
        out_app->double_encrypt = double_encrypt;
        out_app->fohh_thread_id = fohh_thread_id;
        out_app->port = port;

        res = zhash_table_store(zc->outstanding_app_check_hash, domain, strlen(domain), 0, out_app);
        if (res) {
            ZCLIENT_LOG(AL_ERROR, "Could not store in zc->outstanding_app_check_hash : %s", zpath_result_string(res));
            ZPN_FREE(out_app->lookup_domain);
            ZPN_FREE(out_app);
        }
    }

END:
    ZPATH_MUTEX_UNLOCK(&(zc->outstanding_app_check_hash_lock), __FILE__, __LINE__);
    return res;
}
// **************************************************************
int client_http_cb(struct fohh_http_request *request,
                   const char *matched_host,
                   const char *matched_path,
                   struct bufferevent *bev,
                   size_t parsed_bytes,
                   int fohh_thread_id,
                   void *void_cookie,
                   int64_t int_cookie) {
    struct zc_mtunnel *mt;
    int res;

    if (!request->norm_host) {
        ZPN_DEBUG_CBI("No normalized host part");
        return FOHH_RESULT_ERR;
    }

    int sock = bufferevent_getfd(bev);
    struct sockaddr addr;
    socklen_t len = sizeof(addr);
    int port;
    int i;
    uint16_t port_ne = 80;
    struct argo_inet inet = {0};

    if (getsockname(sock, &addr, &len)) {
        ZCLIENT_LOG(AL_WARNING, "Could not get address from socket");
    } else {
        argo_sockaddr_to_inet(&addr, &inet, &port_ne);
    }
    port = htons(port_ne);
    port = port - port_offset_local;

    /* Find the app */
    ZCLIENT_LOG(AL_DEBUG, "http app request '%s' -> '%s:%d'" , request->hdr_host,  request->norm_host, port);

    for (i = 0; remote_apps[i].app_domain; i++) {
        // not matching wild card apps though
        if (!strncmp(request->norm_host,
                     remote_apps[i].app_domain,
                     strnlen(remote_apps[i].app_domain, MAX_APP_NAME_LEN)) &&
            (remote_apps[i].port == port)) {
            if (zpn_debug_get(ZPN_DEBUG_APPLICATION_IDX)) {
                ZCLIENT_LOG(AL_DEBUG,
                            "matched app='%s' host='%s' created=%d port=%" PRId64,
                            request->norm_host,
                            remote_apps[i].app_domain,
                            remote_apps[i].created,
                            remote_apps[i].port);
            }
            break;
        }
    }

    if (!(remote_apps[i].app_domain)) {
        if (!capability_no_domain_download && !capability_no_ip_download && !capability_no_domain_download2) {
            ZCLIENT_LOG(AL_ERROR,
                        "App '%s:%d' not found, checked %d app(s), App scaling not enabled",
                        request->norm_host,
                        port,
                        i);
            return FOHH_RESULT_NO_ERROR;
        }

        ZCLIENT_LOG(AL_INFO,
                    "App '%s:%d' not found, checked %d app(s), App scaling is enabled %d|%d|%d",
                    request->norm_host,
                    port,
                    i,
                    capability_no_domain_download,
                    capability_no_domain_download2,
                    capability_no_ip_download);
    }

    int is_double_encrypt = 0;
    /*
     * If we are in dev env then
     * check if it is 50k setup, then send an app client check to broker
     */
    res = zpn_client_search_apps(g_zc[0], request->norm_host, port, 6, &is_double_encrypt);
    if (res) {
        int is_inet = 0;
        int res2 = argo_string_to_inet(request->norm_host, &inet);
        if ((res2 == ARGO_RESULT_NO_ERROR) && ((inet.length == 16) || (inet.length == 4))) {
            is_inet = 1;
        }

        if ((is_inet && capability_no_ip_download) ||
            (!is_inet && (capability_no_domain_download || capability_no_domain_download2))) {
            res2 = zpn_client_add_outstanding_app_client_check(
                    g_zc[0], (char *)request->norm_host, bev, fohh_thread_id, port, remote_apps[i].double_encrypt);
            if (res2) {
                ZCLIENT_LOG(AL_DEBUG,
                            "Unable to store app client check for domain %s, error = %s\n",
                            request->norm_host,
                            zpn_result_string(res2));
                return res2;
            }

            char *foo = ZPN_STRDUP(request->norm_host, strlen(request->norm_host));
            char *bar;
            if (is_inet && (inet.length == 16)) {
                bar = ZPN_STRDUP(ZPN_DNS_CHECK_TYPE_AAAA, strlen(ZPN_DNS_CHECK_TYPE_AAAA));
            } else {
                bar = ZPN_STRDUP(ZPN_DNS_CHECK_TYPE_A, strlen(ZPN_DNS_CHECK_TYPE_A));
            }

            /* clientd will always send non strict DNS for now
             * TODO: implement ZCC side behaviour - detect network interface, and send strict DNS on dual stack
             *       strict DNS will be used to probe backend's AF and intelligently use correct AF for ICMP
             */
            res2 = zc_state_app_check(g_zc[0], foo, bar, 0);

            ZPN_FREE(foo);
            ZPN_FREE(bar);
            if (res2 && (res2 != ZPN_RESULT_ASYNCHRONOUS)) {
                ZCLIENT_LOG(AL_DEBUG,
                            "Unable to send app client check for domain %s, error = %s",
                            request->norm_host,
                            zpn_result_string(res2));
                return res2;
            } else if (res2 == ZPN_RESULT_ASYNCHRONOUS) {
                ZCLIENT_LOG(AL_DEBUG, "async req for domain '%s'", request->norm_host);
                return FOHH_RESULT_NO_ERROR;
            }
        } else {
            ZCLIENT_LOG(AL_DEBUG, "Application %s on port %d is not registered\n", request->norm_host, port);
            return ZPN_RESULT_ERR;
        }
    }

    ZCLIENT_LOG(
            AL_DEBUG,
            "Received request for host = %s, %ld bytes, arrived on port %d, sock = %d, double_encrypt = %d, zdx probe "
            "= %d",
            request->norm_host,
            (long)parsed_bytes,
            port,
            sock,
            is_double_encrypt,
            remote_apps[i].zpn_probe_type);

    mt = zc_mtunnel_create(g_zc[0],
                           bev,
                           request->norm_host,
                           IPPROTO_TCP,
                           port,
                           fohh_thread_id,
                           remote_apps[i].double_encrypt,
                           generic_status_bev,
                           bev,
                           0);

    if (!mt) {
        ZCLIENT_LOG(AL_DEBUG, "Could not create mtunnel");
        return FOHH_RESULT_ERR;
    } else {
        return FOHH_RESULT_NO_ERROR;
    }

    return FOHH_RESULT_NO_ERROR;
}

// **************************************************************
int client_udp_cb(struct sockaddr_storage addr, uint16_t listening_port, int fohh_thread_id) {
    const struct zc_mtunnel *mt = NULL;
    int i;

     ZPN_DEBUG_MTUNNEL("Got udp request on port '%d' server='%s'", listening_port, service_ip);


    /* Find the app */
    for (i = 0; remote_apps[i].app_domain; i++) {
        if (remote_apps[i].created && (remote_apps[i].port + port_offset_local == listening_port) &&
            remote_apps[i].ip_proto == IPPROTO_UDP) {
            break;
        }
    }

    if (!remote_apps[i].app_domain) {
        ZPN_LOG(AL_DEBUG, "We are not listening on UDP port %d ???", listening_port);
        return ZPN_RESULT_ERR;
    }

    char buf[500];
    char *s = buf;
    char *e = s + sizeof(buf);

    // c2c or wild card request
    if (remote_apps[i].is_c2c || (c2c_app_id_request && remote_apps[i].app_domain[0] == '.')) {
        if (c2c_ip_request) {
            sxprintf(s, e, "%s", c2c_ip_request);
        } else {
            sxprintf(s, e, "%s%s", c2c_app_id_request ?: "", remote_apps[i].app_domain);
        }
    } else {
        if (remote_apps[i].app_domain[0] == '.') {
            // needs name for wild card
            ZPN_LOG(AL_ERROR, "ERROR: Domain '%s' is wild card and it needs prefix , use -c2c_prefix_request",
                    remote_apps[i].app_domain);
            return ZPN_RESULT_ERR;
        }
        sxprintf(s, e, "%s", remote_apps[i].app_domain);
    }

    ZPN_DEBUG_MTUNNEL("Found UDP app '%s'", s);

    // send dns check
    zc_state_dns_check(g_zc[0], s, ZPN_DNS_CHECK_TYPE_A);

    mt = zc_mtunnel_create(g_zc[0],
                           &addr,
                           s,
                           IPPROTO_UDP,
                           listening_port - port_offset_local,
                           fohh_thread_id,
                           remote_apps[i].double_encrypt,
                           NULL,
                           NULL,
                           0);
    if (!mt) {
        ZPN_LOG(AL_DEBUG, "Could not create mtunnel");
        return FOHH_RESULT_ERR;
    }

    // add the
    pthread_mutex_lock(&(mt->udp_tlv_state->lock));
    argo_hash_store(mt->udp_tlv_state->sockaddr, &addr, sizeof(addr), 1, (void *)&mt->mconn_udp_tlv);
    pthread_mutex_unlock(&(mt->udp_tlv_state->lock));

    return FOHH_RESULT_NO_ERROR;
}

struct zc_state* get_next_state(const char* app, uint16_t port) {
    struct zc_state* zc_out = NULL;
    int index = 0;
    // round robin
    pthread_mutex_lock(&(next_state_lock));

    do {
        if (next_state_index >= MAX_NUMBER_OF_CLIENTS) {
            next_state_index = 0;
        }

        index = next_state_index;
        zc_out = g_zc[next_state_index++];

    } while (zc_out == NULL); // at least one will not be NULL

    int64_t cnt =  __atomic_add_fetch(&mtunnels_count, 1, __ATOMIC_RELAXED);
    ZPN_LOG(AL_INFO, "TCP Request: '%" PRId64 "' app='%s':%d conn_id=%d", cnt, app, port, index);

    pthread_mutex_unlock(&(next_state_lock));

    return zc_out;
}
// **************************************************************
int client_generic_tcp_cb(struct bufferevent *bev, uint16_t listening_port, int fohh_thread_id) {
    const struct zc_mtunnel *mt = NULL;
    int i;

    ZPN_DEBUG_MTUNNEL("Got tcp request on port '%d' server='%s'", listening_port, service_ip);

    /* Find the app */
    if (g_random_app) {
        static int next_app = 0;
        for (i = 0; remote_apps[i].app_domain; i++) {
            if (remote_apps[i].ip_proto == IPPROTO_TCP && i == next_app) {
                next_app++;
                break;
            }
        }

        if (!remote_apps[i].app_domain) {
            // go to top
            next_app = 0;
            i = 0;
        }

    } else {
        // exact app, default behaviour
        for (i = 0; remote_apps[i].app_domain; i++) {
            if (remote_apps[i].created && (remote_apps[i].port + port_offset_local == listening_port) &&
                remote_apps[i].ip_proto == IPPROTO_TCP) {
                break;
            }
        }
    }

    if (!remote_apps[i].app_domain) {
        // didnt find the app
        ZPN_LOG(AL_DEBUG, "We are not listening on TCP port %d ???", listening_port);
        bufferevent_free(bev);
        return ZPN_RESULT_ERR;
    }

    char app_domain[500];
    char *s = app_domain;
    char *e = s + sizeof(app_domain);

    // c2c or wild card request
    if (remote_apps[i].is_c2c || (c2c_app_id_request && remote_apps[i].app_domain[0] == '.')) {
        if (c2c_ip_request) {
            sxprintf(s, e, "%s", c2c_ip_request);
        } else {
            sxprintf(s, e, "%s%s", c2c_app_id_request ?: "", remote_apps[i].app_domain);
        }
    } else {
        if (remote_apps[i].app_domain[0] == '.') {
            // needs name for wild card
            ZPN_LOG(AL_ERROR, "ERROR: Domain '%s' is wild card and it needs prefix , use -c2c_prefix_request",
                    remote_apps[i].app_domain);
            bufferevent_free(bev);
            return ZPN_RESULT_ERR;
        }
        sxprintf(s, e, "%s", remote_apps[i].app_domain);
    }

    ZPN_DEBUG_MTUNNEL("Found TCP app '%s'", s);

    uint16_t port = listening_port - port_offset_local;
    struct zc_state *zc_tcp = get_next_state(app_domain, port);
    // send dns check
    if (g_dns_check) {
        zc_state_dns_check(zc_tcp, s, ZPN_DNS_CHECK_TYPE_A);
    }

    if (!g_dns_only) {
        mt = zc_mtunnel_create(zc_tcp,
                               bev,
                               app_domain,
                               remote_apps[i].ip_proto,
                               port,
                               fohh_thread_id,
                               remote_apps[i].double_encrypt,
                               generic_status_bev,
                               bev,
                               0);
        if (!mt) {
            ZPN_LOG(AL_DEBUG, "Could not create mtunnel");
            bufferevent_free(bev);
            return FOHH_RESULT_ERR;
        }
    } else {
        ZPN_LOG(AL_DEBUG, "Not creating mtunnel, g_dns_only is set");
        bufferevent_free(bev);
    }
    return FOHH_RESULT_NO_ERROR;
}
// **************************************************************
int client_proxy_tcp_cb(struct bufferevent *bev, uint16_t listening_port, int fohh_thread_id) {
    struct sockaddr_storage sa;
    socklen_t slen = sizeof(sa);
    const struct zc_mtunnel *mt = NULL;
    char ip_str[1000];
    struct argo_inet inet;
    uint16_t port_ne;
    uint16_t port_he;
    int sock = bufferevent_getfd(bev);
    int res;

    res = getsockname(sock, (struct sockaddr *)&sa, &slen);
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not getsockname");
        bufferevent_free(bev);
        return ZPN_RESULT_ERR;
    }

    argo_sockaddr_to_inet((struct sockaddr *)&sa, &inet, &port_ne);
    port_he = ntohs(port_ne);

    ZPN_LOG(AL_DEBUG, "Got proxy request on %s:%d", argo_inet_generate(ip_str, &inet), port_he);

    mt = zc_mtunnel_create(g_zc[0], bev, ip_str, IPPROTO_TCP, port_he, fohh_thread_id, 0, generic_status_bev, bev, 0);
    if (!mt) {
        ZPN_LOG(AL_DEBUG, "Could not create mtunnel");
        bufferevent_free(bev);
        return FOHH_RESULT_ERR;
    }

    return FOHH_RESULT_NO_ERROR;
}
static int zpn_client_deregister(struct zpath_debug_state *request_state,
                                 const char **query_values,
                                 int query_value_count,
                                 void *cookie) {
    zpn_client_register_client_broker_app(g_zc[0], NULL);

    return ZPATH_RESULT_NO_ERROR;
}

static int zpn_client_upgrade_connection(struct zpath_debug_state *request_state,
                                 const char **query_values,
                                 int query_value_count,
                                 void *cookie) {
    if (!g_passive) {
        ZDP("c2c_client doesnt have passive connection\n");
    } else {
        ZDP("c2c_client sending zpn_client_connection_upgrade RPC\n");
        if (g_zc[0] && g_zc[0]->broker_fohh) {
            struct zpn_tlv *tlv = zc_get_tlv(g_zc[0]);

            zpn_send_zpn_client_connection_upgrade(tlv, zpn_tlv_conn_incarnation(tlv), 100, g_zc[0]->passive,
                                                   !g_zc[0]->passive);
            g_zc[0]->passive = !g_zc[0]->passive;
        } else {
            ZDP("ERROR: connections are not ready\n");
        }
    }
    return ZPATH_RESULT_NO_ERROR;
}
static int zpn_client_dnscheck(struct zpath_debug_state *request_state,
                               const char **query_values,
                               int query_value_count,
                               void *cookie) {
    int res;
    if (!query_values[0]) {
        ZDP("Missing argument: domain\n");
    } else {
        char *foo = ZPN_STRDUP(query_values[0], strlen(query_values[0]));
        if (!query_values[1])
            query_values[1] = ZPN_DNS_CHECK_TYPE_A;
        char *bar = ZPN_STRDUP(query_values[1], strlen(query_values[1]));
        res = zc_state_dns_check(g_zc[0], foo, bar);
        ZPN_FREE(foo);
        ZPN_FREE(bar);
        ZDP("Client DNS check for %s returned %s\n", query_values[0], zpn_result_string(res));
    }
    return ZPATH_RESULT_NO_ERROR;
}

static int zpn_client_app_port_free(int ip_proto, int port) {
    int port_free = 1;

    for (int i = 0; remote_apps[i].app_domain; i++) {
        if (remote_apps[i].created && (remote_apps[i].ip_proto == ip_proto) && (remote_apps[i].port == port)) {
            port_free = 0;
            break;
        }
    }

    return port_free;
}

static int zpn_client_app_create(const char *domain_name, int tcp_port, int udp_port, int double_encrypt) {
    int should_create = 0;
    int i;

    if (dont_start_apps > 0) {
        return ZPN_RESULT_NO_ERROR;
    }

    /* Find the app */
    for (i = 0; remote_apps[i].app_domain; i++) {
        if (!strncmp(domain_name, remote_apps[i].app_domain, strlen(remote_apps[i].app_domain)) &&
            (remote_apps[i].port == tcp_port || remote_apps[i].port == udp_port)) {
            if (!remote_apps[i].created &&
                (udp_port || port_offset_local /*|| ((tcp_port != 80) && (tcp_port != 443))*/)) {
                should_create = 1;
            }
            break;
        }
    }

    if (!should_create) {
        ZPN_DEBUG_APPLICATION("not matched app:'%s' tcp=%d udp=%d de=%d", domain_name, tcp_port, udp_port, double_encrypt);
        return ZPN_RESULT_NO_ERROR;
    }

    struct argo_inet inet;
    number_of_apps_matched++;

    if (tcp_port) {
        ZPN_LOG(AL_INFO, "%d. Create TCP app for %s, port = %d, double_encrypt = %d", number_of_apps_matched,
                domain_name, tcp_port, double_encrypt);
        remote_apps[i].ip_proto = IPPROTO_TCP;
        remote_apps[i].port = (uint16_t)tcp_port;
    } else if (udp_port) {
        ZPN_LOG(AL_INFO, "%d. Create UDP app for %s, port = %d, double_encrypt = %d", number_of_apps_matched,
                domain_name, udp_port, double_encrypt);
        remote_apps[i].ip_proto = IPPROTO_UDP;
        remote_apps[i].port = (uint16_t)udp_port;
    } else {
        ZPN_LOG(AL_ERROR, "Cannot create app %s because invalid port", domain_name);
        return ZPN_RESULT_NO_ERROR;
    }

    remote_apps[i].double_encrypt = double_encrypt;

    argo_string_to_inet(service_ip, &inet);

    if (zpn_client_app_port_free(remote_apps[i].ip_proto, remote_apps[i].port)) {
        int res;

        /* Now let's listen on the port */
        if (remote_apps[i].ip_proto == IPPROTO_TCP) {
            if (HTTP_SERVER == remote_apps[i].generic_tcp) {
                res = fohh_http_server_listen(http_server, &inet, remote_apps[i].port + port_offset_local, NULL,
                                                NULL, 0, 0);
                if (res) {
                    ZPN_LOG(AL_ERROR, "Server listen failed on port %d", (int)remote_apps[i].port);
                }
            } else {  // TCP_SERVER
                res = zc_generic_tcp_listen(g_zc[0], &inet, remote_apps[i].port + port_offset_local,
                                            fohh_next_thread_id(), client_generic_tcp_cb);
                if (res) {
                    ZPN_LOG(AL_ERROR, "Generic Server listen failed on port %d", (int)remote_apps[i].port + port_offset_local);
                }
            }
        } else if (remote_apps[i].ip_proto == IPPROTO_UDP) {
            res = zc_udp_server_listen(g_zc[0], &inet, remote_apps[i].port + port_offset_local, fohh_next_thread_id(),
                                        client_udp_cb);
            if (res) {
                ZPN_LOG(AL_ERROR, "UDP server listen failed on port %d", (int)remote_apps[i].port);
            }
        } else {
            ZPN_LOG(AL_ERROR, "Cannot create app because unknown protocol");
        }
    } else {
            ZPN_LOG(AL_ERROR, "Port not available protocol=%d port=%"PRId64, remote_apps[i].ip_proto, remote_apps[i].port);
    }

    remote_apps[i].created = 1;


    return ZPN_RESULT_NO_ERROR;
}

void set_zpn_debug_flags() {
    zpn_debug_set_from_bits(0);
    if (!quiet) {
        zpn_debug_set(ZPN_DEBUG_CLIENT_IDX);
        zpn_debug_set(ZPN_DEBUG_APPLICATION_IDX);
        zpn_debug_set(ZPN_DEBUG_ASSISTANT_IDX);
        zpn_debug_set(ZPN_DEBUG_BROKER_ASSISTANT_IDX);
        zpn_debug_set(ZPN_DEBUG_SIGNING_CERT_IDX);
        zpn_debug_set(ZPN_DEBUG_ISSUEDCERT_IDX);
        zpn_debug_set(ZPN_DEBUG_AUTH_IDX);
        zpn_debug_set(ZPN_DEBUG_LOG_IDX);
        zpn_debug_set(ZPN_DEBUG_MTUNNEL_IDX);
    }

    if (zpn_debug_flags) {
        zpn_debug_set_update_from_bits(zpn_debug_flags);
    }

    for (size_t i = 0; i < zpn_debug_names_count; i++) {
        int val = set_debug_flag_by_name(zpn_debug_names_local[i], 1);
        ZPN_LOG(val == -1 ? AL_ERROR : AL_INFO,
                "zpn_debug for '%s' %sset=%d",
                zpn_debug_names_local[i],
                val == -1 ? "not " : "",
                val);
    }

    if (g_cfg.config_json_filename) {
        print_json_cfg(&g_cfg);
    }
}
int zpn_client_create_default_http_apps() {
    struct argo_inet inet;
    int res;

    ZCLIENT_LOG(AL_DEBUG, "Creating default http/https app");

    argo_string_to_inet(service_ip, &inet);

    res = fohh_http_server_listen(http_server,
                                  &inet,
                                  port_offset_local + 80,
                                  NULL,
                                  NULL,
                                  0,
                                  0);
    if (res) {
        ZCLIENT_LOG(AL_ERROR, "Server listen failed on port 80");
        return res;
    }

    res = fohh_http_server_listen(http_server,
                                  &inet,
                                  port_offset_local + 443,
                                  NULL,
                                  NULL,
                                  0,
                                  0);
    if (res) {
        ZCLIENT_LOG(AL_ERROR, "Server listen failed on port 443");
        return res;
    }

    return ZPN_RESULT_NO_ERROR;
}
// **************************************************************
int sub_main(int argc, char *argv[]) {
    int res;
    int64_t t1;
    int64_t t2;
    struct argo_inet inet;
    is_zpn_clientd = 1;

    parse_args(argc, argv);

    if (config_daemon) {
        int fd;
        fprintf(stderr, "Daemonizing\n");
        /* Take off... */
        switch (fork()) {
        case 0:
            break;
        case -1:
            ZPN_LOG(AL_ERROR, "fork failed: %s", strerror(errno));
            return WALLY_RESULT_ERR;
        default:
            /* exit interactive session */
            exit(0);
        }
        if (setsid() == -1) {
            ZPN_LOG(AL_ERROR, "setsid failed: %s", strerror(errno));
            return WALLY_RESULT_ERR;
        }
        if ((fd = open("/dev/null", O_RDWR, 0)) != -1) {
            (void)dup2(fd, STDIN_FILENO);
            (void)dup2(fd, STDOUT_FILENO);
            (void)dup2(fd, STDERR_FILENO);
            if (fd > 2)
                (void)close(fd);
        }
    }

    no_udp_packetize_on_tlv = 1;

    /***************************************************
     *
     * Argo/FOHH library initialization
     *
     */
    struct zpath_simple_app_init_params app_params;
    zpath_simple_app_init_params_default(&app_params);
    app_params.instance_name = "client";
    app_params.role_name = "c2c_client";
    app_params.root_cert_file = ca_filename;
    app_params.cert_chain_file = cert_filename;
    app_params.private_key_file = key_filename;
    app_params.fohh_thread_count = thread_count;
    app_params.log_filename = logfile;
    app_params.debug_port = debug_port;
    app_params.debuglog = debuglog;

    res = zpath_simple_app_init(&app_params);
    if (ZPN_RESULT_NO_ERROR != res) {
        ZPN_LOG(AL_CRITICAL, "cannot create zpath_simple_app_init %s", zpn_result_string(res));
        exit(1);
    }

    /***************************************************
     *
     * Create inner tunnel SSL ctx
     */
    if (inner_ca_filename) {
        res = zc_inner_tunnel_ssl_ctx_init(inner_ca_filename, cert_filename, key_filename);
        if (res) {
            ZPN_LOG(AL_NOTICE, "Cannot create inner tunnel ssl ctx");
            exit(1);
        }
    } else {
        ZPN_LOG(AL_NOTICE, "Missing ca files for inner SSL tunnel");
    }

    zpn_event_collection = zpath_event_collection;
    zdtls_debug = (0);
    zrdt_debug = (0);

    zdtls_init(zpn_event_collection);
    zrdt_init(zpn_event_collection, thread_count);
    zrdt_set_cloud_environment(1);

    set_zpn_debug_flags();

    // read apps
    if (apps_file) {
        if (zc_apps_local_init_with_file(apps_file, &client_to_client_app)) {
            ZPN_LOG(AL_ERROR, "apps_file config is not read\n");
            exit(1);
        }

        if (client_to_client_app) {
            ZPN_LOG(AL_INFO, "'%s' is a c2c app", client_to_client_app);
        }
    }

    /***************************************************
     *
     * ZC_CLIENTD initialization begins...
     *
     */

    if (time_msgs) {
        // create txt file and time messages
        char fname[100] = {0};
        snprintf(fname, sizeof(fname), "./TIMES/%u.txt", getpid());

        if (mkdir("TIMES", 0755) < 0 && errno != EEXIST) {
            ZPN_LOG(AL_ERROR, "cannot create  TIMES: %s", strerror(errno));
        }

        fp_time_msgs = fopen(fname, "w");

        start_time_msgs_s = epoch_us();

        ZPN_LOG(AL_NOTICE, "create TIMES");
    }

    char client_fqdn[500];
    char *s = client_fqdn;
    char *e = s + sizeof(client_fqdn);
    sxprintf(s, e, "%s%s", c2c_app_id_prefix, client_to_client_app);

    next_state_lock = (pthread_mutex_t)PTHREAD_MUTEX_INITIALIZER;

    t1 = epoch_us();
    wally_interlock_lock_1(&interlock);

    // determine client type
    g_client_type = to_edge_connector      ? zpn_client_type_edge_connector
                    : to_machine_tunnel    ? zpn_client_type_machine_tunnel
                    : to_browser_isolation ? zpn_client_type_browser_isolation
                    : to_branch_connector  ? zpn_client_type_branch_connector
                    : to_ip_anchoring      ? zpn_client_type_ip_anchoring
                    : to_vdi               ? zpn_client_type_vdi
                    : to_zapp_partner      ? zpn_client_type_zapp_partner
                                           : zpn_client_type_zapp;

    // primary connection
    //
    __sync_add_and_fetch_4(&clients_launched, 1);
    g_zc[0] = zc_state_create(broker_name,
                         customer_domain,
                         saml_assertion,
                         cert_filename,
                         key_filename,
                         ca_filename,
                         client_callback,
                         NULL,
                         0,
                         do_debug,
                         capabilities_matrix,
                         capabilities_count,
                         customer_gid,
                         orgid,
                         o_location_id,
                         o_plocation_id,
                         o_identity_name,
                         o_user_id,
                         external_device_id,
                         hw_id,
                         cpu_id,
                         storage_id,
                         login_name,
                         cloud_name,
                         platform,
                         custom_sni_str,
                         NULL,  // pub+ip not used
                         client_fqdn,
                         g_client_type,
                         broker_tlv_type,
                         g_passive,
                         username,
                         user_last_auth_time);

    if (!g_zc[0]) {
        ZPN_LOG(AL_ERROR, "Could not create zpn client");
        sleep(1);
        exit(1);
    }
    g_zc[0]->si_device_id = si_device_id;
    g_zc[0]->si_device_trust = si_device_trust;

    g_zc[0]->mtunnels_by_tag = argo_hash_alloc(7, 1);
    zc_state_register_app_create_callback(g_zc[0], zpn_client_app_create);

    res = zudp_conn_table_initialize();
    if (res) {
        ZPN_LOG(AL_ERROR, "Failed to initialize UDP connection table ");
        return res;
    }

    ZPN_DEBUG_AUTH("Waiting for clients to connect...");

    wally_interlock_lock_2(&interlock);
    t2 = epoch_us();
    ZPN_LOG(AL_NOTICE, "Clients connected. %ld clients took %8.3fs or %8.3f clients per second.", (long)client_count,
            (double)((t2 - t1) / 1000000.0f), (double)client_count / ((t2 - t1) / 1000000.0f));

    argo_string_to_inet(service_ip, &inet);

    if (g_cfg.create_http) {
        http_server = fohh_http_server_create_handoff("c2c_http");
        if (!http_server) {
            ZCLIENT_LOG(AL_ERROR, "Cannot create http_server");
            return ZPN_RESULT_ERR;
        }

        res = fohh_http_server_register_handoff(http_server, "*", "*", client_http_cb, NULL, 0);
        if (res) {
            ZCLIENT_LOG(AL_ERROR, "Server register failed");
            return res;
        }

        res = zpn_client_create_default_http_apps();
        if (res) {
            ZCLIENT_LOG(AL_ERROR, "Cannot create default HTTP/HTTPS application");
            return res;
        }
    }

    res = zpath_debug_pre_init(1);
    if (res) {
        fprintf(stderr, "Error: Could not debug pre-init\n");
        exit(1);
    }

    ZPATH_LOCAL_DEBUG_PORT = 9001;

    res = zpath_debug_init("event_log", NULL, ZPATH_LOCAL_DEBUG_PORT);
    if (res) {
        fprintf(stderr, "Could not debug_init: %d", res);
        exit(1);
    }

    if (config_ip_proxy) {
        argo_string_to_inet(config_ip_proxy_ip, &inet);
        res = zc_generic_tcp_listen(g_zc[0], &inet, config_ip_proxy_port_he, fohh_next_thread_id(), client_proxy_tcp_cb);
        if (res) {
            fprintf(stderr, "Could not create ip proxy listener on %s:%d", config_ip_proxy_ip, config_ip_proxy_port_he);
            return res;
        }
    }

    res = zpath_debug_add_write_command("Send DNS check to broker", "/client/dnscheck", zpn_client_dnscheck, NULL, "domain",
                                  "The domain name for which to send the DNS check", "type",
                                  "The domain name for which to send the DNS check", NULL);
    if (res) {
        return res;
    }

    res = zpath_debug_add_write_command("deregister client", "/client/deregister", zpn_client_deregister, NULL, NULL);

    if (res) {
        return res;
    }

    res = zpath_debug_add_admin_command("deregister client", "/client/upgrade", zpn_client_upgrade_connection, NULL, NULL);

    if (res) {
        return res;
    }

    res = zpath_debug_add_flag_ext(zpn_debug_cnxt(),
                                   zpn_debug_catch_cnxt(),
                                   zpn_debug_cnxt_cnt(),
                                   "zpn",
                                   zpn_debug_names);
    if (res) {
        ZPN_LOG(AL_ERROR, "zpath_debug_add_flag failed: %s", zpath_result_string(res));
        return res;
    }

    if (no_fohh_status) {
        fohh_debug = (FOHH_DEBUG_SERIALIZE_BIT);
    }

    res = zpath_debug_add_flag(&zdtls_debug, zdtls_debug_catch_defaults, "zdtls", zdtls_debug_names);

    if (res) {
        ZPN_LOG(AL_ERROR, "zpath_debug_add_flag failed: %s", zpath_result_string(res));
        return res;
    }

    res = zpath_debug_add_flag(&zrdt_debug, zrdt_debug_catch_defaults, "zrdt", zrdt_debug_names);

    if (res) {
        ZPN_LOG(AL_ERROR, "zpath_debug_add_flag failed: %s", zpath_result_string(res));
        return res;
    }

    if (zpath_debug_add_allocator(&zpn_allocator, "zpn")) {
        ZPN_LOG(AL_ERROR, "Could not add zrdt allocator");
        return ZPATH_RESULT_ERR;
    }

    if (zpath_debug_add_allocator(&zrdt_allocator, "zrdt")) {
        ZPN_LOG(AL_ERROR, "Could not add zrdt allocator");
        return ZPATH_RESULT_ERR;
    }

    signal(SIGPIPE, SIG_IGN);

    if (dont_start_apps > 0) {
        ZPN_LOG(AL_INFO, "not starting apps listeners");
    }

    ZPN_LOG(AL_NOTICE, "Initialization complete.");

    while (1)
        sleep(1);
}

void log_msg_time(const char* msg, const char* extra, int reset_timer) {
    if (NULL == fp_time_msgs) {
        return;
    }
    int64_t log_s = 0;

    log_s = epoch_us() - start_time_msgs_s;

    char tmp[100] = {0};
    snprintf(tmp, sizeof(tmp), "%5" PRId64 " ms %s %s\n", log_s / 1000, msg, extra);

    fwrite(tmp, strnlen(tmp, sizeof(tmp)), 1, fp_time_msgs);
    fflush(fp_time_msgs);

    if (reset_timer) {
        start_time_msgs_s = epoch_us();
    }
}

// **************************************************************
int main(int argc, char *argv[]) {
    int ret = sub_main(argc, argv);
    sleep(1);
    return ret;
}
// **************************************************************
int parse_args(int argc, char *argv[]) {
    /* All arguments are pairs, here... */

    g_cfg.lock = (pthread_mutex_t)PTHREAD_MUTEX_INITIALIZER;

    fprintf(stdout, "ARGO: ARGO2_MAX_OBJECT_FIELDS=%d  ARGO_BUF_DEFAULT_SIZE=%d\n", ARGO2_MAX_OBJECT_FIELDS,
            ARGO_BUF_DEFAULT_SIZE);

    // disable heartbeat
    zthread_disable_heartbeat_monitor();

    argo_log_use_printf(3);

    for (int i = 1; i < argc; i++) {
        if (strcmp(argv[i], "-version") == 0) {
            fprintf(stdout, "%s\n", ZPATH_VERSION);
            exit(0);
        } else if (strcmp(argv[i], "-daemon") == 0) {
            config_daemon = 1;
        } else if (strcmp(argv[i], "-debug") == 0) {
            do_debug = 1;
        } else if (strcmp(argv[i], "-debuglog") == 0) {
            debuglog = 1;
        } else if (strcmp(argv[i], "-quiet") == 0) {
            quiet = 1;
        } else if (strcmp(argv[i], "-no_flow_control") == 0) {
            flow_control_enabled = 0;
        } else if (strcmp(argv[i], "-ec") == 0) {
            to_edge_connector = 1;
        } else if (strcmp(argv[i], "-mt") == 0 || strcmp(argv[i], "-c3") == 0) {
            to_machine_tunnel = 1;
        } else if (strcmp(argv[i], "-bi") == 0) {
            to_browser_isolation = 1;
        } else if (strcmp(argv[i], "-vdi") == 0) {
            to_vdi = 1;
        } else if (strcmp(argv[i], "-ia") == 0) {
            to_ip_anchoring = 1;
        } else if (strcmp(argv[i], "-bc") == 0) {
            to_branch_connector = 1;
        } else if (strcmp(argv[i], "-zp") == 0) {
            to_zapp_partner = 1;
        } else if (strcmp(argv[i], "-ipproxy") == 0) {
            config_ip_proxy = 1;
        } else if (strcmp(argv[i], "-dont_start_apps") == 0) {
            dont_start_apps = 1;
        } else if (strcmp(argv[i], "-no_fohh_status") == 0) {
            no_fohh_status = 1;
        } else if (strcmp(argv[i], "-no_br_ack") == 0) {
            no_broker_req_ack = 1;
        } else if (strcmp(argv[i], "-time_msgs") == 0) {
            time_msgs = 1;
        } else if (strcmp(argv[i], "-no_trusted_net") == 0) {
            send_trusted_nets = 0;
        } else if (strcmp(argv[i], "-enable_heartbeat_monitor") == 0) {
            zthread_enable_heartbeat_monitor();
        } else if (strcmp(argv[i], "-no_zia_identity") == 0) {
            no_zia_identity = 1;
        } else if (strcmp(argv[i], "-passive") == 0) {
            g_passive = 1;
        } else if (strcmp(argv[i], "-dns_only") == 0) {
            g_dns_only = 1;
        } else if (strcmp(argv[i], "-dns_strict") == 0) {
            g_dns_strict = 1;
        } else if (strcmp(argv[i], "-no_dns") == 0) {
            g_dns_check = 0;
        } else if (strcmp(argv[i], "-random_app") == 0) {
            g_random_app = 1;
        } else if (strcmp(argv[i], "-http") == 0) {
            g_cfg.create_http = 1;
        } else {
            if ((i + 1) >= argc) {
                fprintf(stderr, "%sargument '%s' is unknown or requires value%s\n", RED, argv[i], RST);
                usage(argv[0]);
            }
            if (strcmp(argv[i], "-ca") == 0) {
                ca_filename = argv[i + 1];
            } else if (strcmp(argv[i], "-config_json") == 0) {

                g_cfg.config_json_filename = argv[i + 1];

                if (parse_json_cfg(&g_cfg)) {
                    return 1;
                }
                if (process_json_cfg(&g_cfg)) {
                    return 1;
                }

            } else if (strcmp(argv[i], "-cert") == 0) {
                cert_filename = argv[i + 1];
            } else if (strcmp(argv[i], "-key") == 0) {
                key_filename = argv[i + 1];
            } else if (strcmp(argv[i], "-tunca") == 0) {
                inner_ca_filename = argv[i + 1];
            } else if (strcmp(argv[i], "-broker") == 0) {
                broker_name = argv[i + 1];
            } else if (strcmp(argv[i], "-customer") == 0) {
                customer_domain = argv[i + 1];
            } else if (strcmp(argv[i], "-ip") == 0) {
                service_ip = argv[i + 1];
            } else if (strcmp(argv[i], "-logfile") == 0) {
                logfile = argv[i + 1];
            } else if (strcmp(argv[i], "-capability") == 0) {
                if (ZPN_RESULT_NO_ERROR == zpn_client_check_capability_validity(&capabilities, argv[i + 1])) {
                    if (capabilities_count < ZPN_CLIENT_CAPABILITY_MAX_COUNT) {
                        capabilities_matrix[capabilities_count++] = argv[i + 1];
                        zpn_clientd_set_capability(argv[i+1]);
                    }
                }
            } else if (strcmp(argv[i], "-saml") == 0) {
                FILE *fp;
                size_t len;
                saml_filename = argv[i + 1];
                fp = fopen(saml_filename, "r");
                if (!fp) {
                    fprintf(stderr, "Cannot read file %s\n", saml_filename);
                    usage(argv[0]);
                }
                len = fread(saml_assertion, 1, sizeof(saml_assertion), fp);
                fclose(fp);
                if (!len) {
                    fprintf(stderr, "Empty file %s\n", saml_filename);
                    usage(argv[0]);
                }
                if (len >= sizeof(saml_assertion)) {
                    fprintf(stderr, "SAML file too big - should be less than %lu\n", sizeof(saml_assertion));
                    usage(argv[0]);
                }
            } else if (strcmp(argv[i], "-port") == 0) {
                app_port = (uint16_t)strtol(argv[i + 1], NULL, 0);
                if (app_port == 0) {
                    fprintf(stderr, "Invalid port %s\n", argv[i + 1]);
                    usage(argv[0]);
                }
            } else if (strcmp(argv[i], "-hw") == 0) {
                hw_id = argv[i + 1];
            } else if (strcmp(argv[i], "-cpu") == 0) {
                cpu_id = argv[i + 1];
            } else if (strcmp(argv[i], "-storage") == 0) {
                storage_id = argv[i + 1];
            } else if (strcmp(argv[i], "-login") == 0) {
                login_name = argv[i + 1];
            } else if (strcmp(argv[i], "-cloud") == 0) {
                cloud_name = argv[i + 1];
            } else if (strcmp(argv[i], "-platform") == 0) {
                platform = argv[i + 1];
            } else if (strcmp(argv[i], "-debugport") == 0) {
                debug_port = atoi(argv[i + 1]);
            } else if (strcmp(argv[i], "-client_count") == 0) {
                client_count = strtol(argv[i + 1], NULL, 0);
                if (client_count <= 0)
                    usage(argv[0]);
            } else if (strcmp(argv[i], "-async") == 0) {
                async_count = strtol(argv[i + 1], NULL, 0);
                if (async_count <= 0)
                    usage(argv[0]);
            } else if (strcmp(argv[i], "-threads") == 0) {
                thread_count = strtol(argv[i + 1], NULL, 0);
                if (thread_count <= 0)
                    usage(argv[0]);
            } else if (strcmp(argv[i], "-sni") == 0) {
                custom_sni_str = argv[i + 1];
                if (thread_count <= 0)
                    usage(argv[0]);
            } else if (strcmp(argv[i], "-broker_tlv") == 0) {
                if (strcmp(argv[i + 1], "zrdt") == 0) {
                    broker_tlv_type = zpn_zrdt_tlv;
                } else if (strcmp(argv[i + 1], "fohh") == 0) {
                    broker_tlv_type = zpn_fohh_tlv;
                } else {
                    fprintf(stderr, "Unknown option for -broker_tlv\n");
                    usage(argv[0]);
                }
            } else if (strcmp(argv[i], "-c2c_prefix") == 0) {
                c2c_app_id_prefix = argv[i + 1];
            } else if (strcmp(argv[i], "-c2c_prefix_request") == 0) {
                c2c_app_id_request = argv[i + 1];
            } else if (strcmp(argv[i], "-c2c_ip_request") == 0) {
                c2c_ip_request = argv[i + 1];
            } else if (strcmp(argv[i], "-port_offset") == 0) {
                port_offset_local = (uint16_t)strtol(argv[i + 1], NULL, 0);

            } else if (strcmp(argv[i], "-apps_file") == 0) {
                apps_file = argv[i + 1];
            } else if (strcmp(argv[i], "-zpn_debug") == 0) {
                zpn_debug_flags = (uint64_t)strtoull(argv[i + 1], NULL, 16);  // HEX
            } else if (strcmp(argv[i], "-d") == 0) {
                if (zpn_debug_names_count < MAX_ZPN_DEBUG_NAMES_COUNT) {
                    zpn_debug_names_local[zpn_debug_names_count++] = argv[i + 1];
                }
            } else if (strcmp(argv[i], "-customer_id") == 0) {
                customer_gid = strtol(argv[i + 1], NULL, 0);
            } else if (strcmp(argv[i], "-orgid") == 0) {
                orgid = strtol(argv[i + 1], NULL, 0);
            } else if (strcmp(argv[i], "-o_location_id") == 0) {
                o_location_id = strtoll(argv[i + 1], NULL, 0);
            } else if (strcmp(argv[i], "-o_plocation_id") == 0) {
                o_plocation_id = strtoll(argv[i + 1], NULL, 0);
            } else if (strcmp(argv[i], "-o_identity_name") == 0) {
                o_identity_name = argv[i + 1];
            } else if (strcmp(argv[i], "-external_device_id") == 0) {
                external_device_id = argv[i + 1];
            } else if (strcmp(argv[i], "-o_user_id") == 0) {
                o_user_id = strtoll(argv[i + 1], NULL, 0);
            } else if (strcmp(argv[i], "-si_device_id") == 0) {
                si_device_id = atoi(argv[i + 1]);
            } else if (strcmp(argv[i], "-si_device_trust") == 0) {
                si_device_trust = atoi(argv[i + 1]);
            } else if (strcmp(argv[i], "-zia_cloud") == 0) {
                zia_cloud = argv[i + 1];
            } else if (strcmp(argv[i], "-printf") == 0) {
                argo_log_use_printf(atoi(argv[i + 1]));
            } else if (strcmp(argv[i], "-username") == 0) {
                username = argv[i + 1];
            } else if (strcmp(argv[i], "-user_last_auth_time") == 0) {
                user_last_auth_time = strtoll(argv[i + 1], NULL, 0);
            } else {
                fprintf(stderr, "%sUnknown argument '%s'%s\n",RED, argv[i], RST);
                usage(argv[0]);
                exit(1);
            }

            fprintf(stdout, "%sProcessed: i:%d, arg-name:%s, arg-value:%s, value-len:%d%s\n", LBL, i, argv[i],
                    argv[i + 1], (int)strlen(argv[i + 1]), RST);


            i++;
        }
    }

    if (g_passive) {
        fprintf(stderr, "-passive, only one connection is allowed\n");
        client_count = 1;
    }

    if (ca_filename == NULL) {
        fprintf(stderr, "Need -ca filename\n");
        usage(argv[0]);
    }

    if (cert_filename == NULL) {
        fprintf(stderr, "Need -cert filename\n");
        usage(argv[0]);
    }

    if (key_filename == NULL) {
        fprintf(stderr, "Need -key filename\n");
        usage(argv[0]);
    }

    if (broker_name == NULL) {
        fprintf(stderr, "Need broker name\n");
        usage(argv[0]);
    }

    if (!to_vdi && !to_edge_connector && !to_ip_anchoring && !to_branch_connector && !to_machine_tunnel) {

        if (customer_domain == NULL) {
            fprintf(stderr, "Need customer domain\n");
            usage(argv[0]);
        }

        if (saml_filename == NULL) {
            fprintf(stderr, "Need SAML assertion filename\n");
            usage(argv[0]);
        }

        if (!hw_id || !cpu_id || !storage_id || !login_name) {
            fprintf(stderr, "Need -hw, cpu_id, storage_id and login_name\n");
            usage(argv[0]);
        }

        if (!cloud_name) {
            fprintf(stderr, "Need cloud_name\n");
            usage(argv[0]);
        }
    }

    if (to_machine_tunnel) {
        if (!hw_id) {
            fprintf(stderr, "Need -hw\n");
            usage(argv[0]);
        }
    }

    if(to_vdi) {
        if(!username){
            fprintf(stderr, "Need username \n");
            usage(argv[0]);
        }
        if(!user_last_auth_time){
            fprintf(stderr, "Need user_last_auth_time \n");
            usage(argv[0]);
        }
    }

    if (to_branch_connector && (!orgid || !cloud_name || !o_location_id)) {
        fprintf(stderr, "Need orgid, cloud name and o_location_id\n");
        usage(argv[1]);
    }

    if (apps_file == NULL && dont_start_apps == 0) {
        fprintf(stderr, "Missing -apps_file or -dont_start_apps\n");
        usage(argv[0]);
    }

    if (c2c_ip_request != NULL) {
        struct argo_inet inet;
        if (ARGO_RESULT_NO_ERROR != argo_string_to_inet(c2c_ip_request, &inet)) {
            fprintf(stderr, "Invalid IP address is given for -c2c_ip_request\n");
            usage(argv[0]);
        }

        if (!inet.length) {
            c2c_ip_request = NULL;
        }
    }
    return 0;
}

// **************************************************************
int usage(const char *argv0) {
    fprintf(stderr, "%s [options]\n", argv0);
    fprintf(stderr,
            "  -config_json CONFIG_JSON_FILE : additional conifguration \n"
            "  -ca FILENAME     : Root certificate for verification\n"
            "  -cert FILENAME   : Client public certificate\n"
            "  -key FILENAME    : Client private key\n"
            "  -broker HOSTNAME : Broker hostname\n"
            "  -customer DOMAIN : Customer domain\n"
            "  -saml FILENAME   : SAML assertion filename\n"
            "  -app NAME        : Application name to connect to\n"
            "  -ip IP           : IP address to bind to. Defaults 127.0.0.1\n"
            "  -dns_only        : DNS requests only, no mtunnels\n"
            "  -dns_strict      : DNS strict flag. Default is not strict\n"
            "  -no_dns          : DNS check is not issued. Default is to send dns check\n"
            "  -port PORT       : Port to bind to. Defaults 80\n"
            "  -port2 PORT      : Other port to bind to. Defaults 443\n"
            "  -logfile NAME    : Send log events to specified file\n"
            "  -debug           : Turn on HEAVY debugging of all TLV traffic\n"
            "  -debuglog        : Specify in order to send debug messages to syslog/stderr\n"
            "  -debugport PORT  : Specify port to run debug interface\n"
            "  -hw              : Hardware ID\n"
            "  -cpu             : CPU ID\n"
            "  -storage         : Storage ID\n"
            "  -login           : Login Name\n"
            "  -cloud           : Cloud Name\n"
            "  -http            : listens on port 80|443 + port_offset\n"
            "  -tunca FILENAME  : Root certificate for verification for inner tunnel\n"
            "  -client_count COUNT : Number of clients to create.\n"
            "  -async COUNT     : Number of clients that can be authenticating simultaneously\n"
            "  -threads COUNT   : Number of worker threads. Default 4\n"
            "  -no_flow_control : Disable flow control on FOHH\n"
            "  -sni             : Custom SNI string\n"
            "  -capability      : Capability values. Provide capabilities as -capability <value1>\n"
            "                     -capability <value2>. Values can be [%s]\n"
            "  -ec              : Specify if we are connecting to Edge Connector\n"
            "  -ia              : Specify if we are connecting to IP Anchoring (SMEs)\n"
            "  -bc              : Specify if we are connecting as Branch Connector\n"
            "  -vdi             : Specify if we are connecting as Multi User VDI Cloud Connector\n"
            "  -platform        : Platform name (windows/linux/mac/ios/android)\n"
            "  -username        : Specify the username for the user connecting from the VDI-CC\n"
            "  -user_last_auth_time : Specify the user_last_auth_timestamp for the user connecting from the VDI-CC\n"
            "  -mt              : Specify if we are connecting as Machine Tunnel\n"
            "  -customer_id     : Specify customer_gid needed for IP Anchoring (SMEs)\n"
            "  -orgid           : Specify orgid (and -cloud) for IP anchoring or EC/BC\n"
            "  -o_location_id   : Specify o_location_id to be used with each mtunnel request\n"
            "  -o_plocation_id  : Specify o_plocation_id to be used with each mtunnel request\n"
            "  -o_identity_name : Specify o_identity_name to be used with each mtunnel request. Optional\n"
            "  -o_user_name     : Specify o_user_id to be used with each mtunnel request. Optional\n"
            "  -zia_cloud       : Specify zia_cloud. Optional\n"
            "  -external_device_id     : Specify external_device_id to be used with zpn_authenticate. Optional\n"
            "  -ipproxy         : Run an IP proxy listening on %s:%d\n"
            "  -printf          : Use printf logging (SLOW)\n"
            "  -c2c_prefix PREFIX : prefix used in client-to-client\n"
            "  -c2c_prefix_request PREFIX : request prefix used in client-to-client\n"
            "  -dont_start_apps : if provided, the apps wont start\n"
            "  -port_offset     : added to local listening app ports, to allow two clients on same machine\n"
            "  -apps_file FILE  : Specify that app config config file, rows of zc_application json \n"
            "  -zpn_debug FLAGS : zpn debug flag as HEX, e.g. MCONN=800, FLOW_CONTROL=1000000\n"
            "  -d NAME          : set zpn debug flag as name. Same as in /debug/zpn, e.g. -d auth\n",
            ZPN_CLIENT_CAPABILITY_ALL_STR,
            config_ip_proxy_ip,
            (int)config_ip_proxy_port_he);
    exit(1);
}

/*
 * Set global capability
 */
void zpn_clientd_set_capability(const char *capability) {
    if (capability) {
        if (strncasecmp(capability,
                        ZPN_CLIENT_CAPABILITY_NO_DOMAIN_DOWNLOAD_STR,
                        sizeof(ZPN_CLIENT_CAPABILITY_NO_DOMAIN_DOWNLOAD_STR) - 1) == 0) {
            capability_no_domain_download = 1;
        } else if (strncasecmp(capability,
                               ZPN_CLIENT_CAPABILITY_NO_DOMAIN_DOWNLOAD_STR,
                               sizeof(ZPN_CLIENT_CAPABILITY_NO_DOMAIN_DOWNLOAD_V2_STR) - 1) == 0) {
            capability_no_domain_download2 = 1;

        } else if (strncasecmp(capability,
                               ZPN_CLIENT_CAPABILITY_NO_IP_DOWNLOAD_STR,
                               sizeof(ZPN_CLIENT_CAPABILITY_NO_IP_DOWNLOAD_STR) - 1) == 0) {
            capability_no_ip_download = 1;
        } else if (strncasecmp(capability,
                               ZPN_CLIENT_CAPABILITY_LATENCY_PROBE_STR,
                               sizeof(ZPN_CLIENT_CAPABILITY_LATENCY_PROBE_STR) - 1) == 0) {
            capability_latency_probe = 1;
        } else if (strncasecmp(capability, ZPN_CLIENT_CAPABILITY_MTN_STR, sizeof(ZPN_CLIENT_CAPABILITY_MTN_STR) - 1) ==
                   0) {
            capability_mtn = 1;
        }
    }
}
