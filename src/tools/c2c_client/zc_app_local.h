
/*
 * zc_app_local.h. Copyright (C) 2021 Zscaler, Inc. All Rights Reserved.
 */
#ifndef __ZC_APP_LOCAL__H
#define __ZC_APP_LOCAL__H

#include "argo/argo.h"

struct zc_application { /* _ARGO: object_definition */
    char *app_domain;   /* _ARGO: string */
    uint16_t ip_proto;  /* _ARGO: integer */
    uint64_t port;      /* _ARGO: integer */
    int double_encrypt; /* _ARGO: integer */
    int created;        /* _ARGO: integer */
    int generic_tcp;    /* _ARGO: integer */
    int is_c2c;         /* _ARGO: integer */
    enum zpn_probe_type zpn_probe_type;
};
#define MAX_REMOTE_APPS 10
extern struct zc_application remote_apps[MAX_REMOTE_APPS + 1];
int zc_apps_local_init_with_file(const char *local_filename, char **c2c_app);

#define TCP_SERVER 1
#define HTTP_SERVER 0

#define C2C_APP 1

#endif /* __ZC_APP_LOCAL__H */
