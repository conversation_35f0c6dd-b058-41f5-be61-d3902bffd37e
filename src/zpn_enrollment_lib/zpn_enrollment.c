/*
 * zpn_enrollment.c. Copyright (C) 2018 Zscaler, Inc. All Rights Reserved.
 *
 * Contract provided by enrollment server is here -
 * https://confluence.corp.zscaler.com/display/ET/Enrollment+workflow+for+ZNF%2C+Private+Brokers+and+Connectors
 *
 * Don't let any of the sensitive info like fingerprint, nonce to be printed, even with AL_DEBUG flag. Reason being
 * when the sensitve info is written to *disk* all bets are off. Also to demonstrate our security focus, we shouldn't
 * print it anywhere.
 */
#define _GNU_SOURCE

#include "zpn_enrollment_lib/zpn_enrollment.h"
#include <openssl/x509v3.h>
#include "zpath_lib/zpa_cloud_config.h"
#include "zpath_lib/zpath_cloud.h"
/*
 * Private instance of state
*/
static struct zpn_enroll_state s_enroll_state;

/* Enrollment debugging */
int s_enroll_debug = 0;


static const char* cloud_cert = NULL;

static const char* get_enrollement_style_name(enum zpn_enrollment_style enroll_style);


static const char* get_enrollement_api_name(enum zpn_enrollment_api_type api_type);

#define ZPN_MAX_STACK_FILE_SIZE     (2*1024*1024)

#define DECRYPT_WITH_EMPTY_DISK_ID       1
#define DECRYPT_WITH_IMDSV1_NON_EMPTY_DISK_ID   2
#define DECRYPT_WITH_IMDSV1_EMPTY_DISK_ID       3

/*
 * Get enrollment endpoint type. Only the existing connectors in the field will use V1 enrollment.
 */
static void get_enrollment_api_endpoint_v1(enum zpn_enrollment_type enroll_type, enum zpn_enrollment_api_type api_type, char *endpoint, int endpoint_len)
{
    char *endpoint_type;

    switch (enroll_type) {
        case ZPN_ENROLLMENT_TYPE_ASSISTANT:
        case ZPN_ENROLLMENT_TYPE_NP:
            endpoint_type = ENROLLMENT_API_ENDPOINT_TYPE_ASSISTANT;
            break;

        default:
            assert(0);
            break;
    }

    switch (api_type) {
        case ENROLLMENT_API_SIGN_IN:
            snprintf(endpoint, endpoint_len, "/shift/api/v1/%s/signin", endpoint_type);
            break;

        case ENROLLMENT_API_FETCH_CSR_DETAILS:
            snprintf(endpoint, endpoint_len,  "/shift/api/v1/admin/%s/onboard/details", endpoint_type);
            break;

        case ENROLLMENT_API_SEND_CSR:
            snprintf(endpoint, endpoint_len, "/shift/api/v1/admin/%s/onboard/csr", endpoint_type);
            break;

        case ENROLLMENT_API_GET_CERTIFICATE:
            snprintf(endpoint, endpoint_len, "/shift/api/v1/admin/%s/onboard/certificate", endpoint_type);
            break;
        default:
            assert(0);
            break;
    }
}

/*
 * Get enrollment endpoint type
 */
static void get_enrollment_api_endpoint_v2(enum zpn_enrollment_type enroll_type, enum zpn_enrollment_api_type api_type, const char *api_hostname, char *endpoint, int endpoint_len)
{
    char *endpoint_type;

    /* Only V2 supports broker and connector, component ::= <assistant|broker|znf> */

    // POST on https://api.<cloud name>/zpn/api/v2/admin/<component>/onboard/details
    // POST on https://api.<cloud name>/zpn/api/v2/admin/<component>/onboard/csr
    // POST on https://api.<cloud name>/zpn/api/v2/admin/<component>/onboard/certificate

    // CNAME: <id>.<asst|pb|znf>.<cloud name>

    switch (enroll_type) {
    case ZPN_ENROLLMENT_TYPE_ASSISTANT:
    case ZPN_ENROLLMENT_TYPE_NP:
        endpoint_type = ENROLLMENT_API_ENDPOINT_TYPE_ASSISTANT;
        break;

    case ZPN_ENROLLMENT_TYPE_PRIVATE_BROKER:
        endpoint_type = ENROLLMENT_API_ENDPOINT_TYPE_PRIVATE_BROKER;
        break;
    case ZPN_ENROLLMENT_TYPE_SITEC:
        endpoint_type = ENROLLMENT_API_ENDPOINT_TYPE_SITEC;
        break;
    default:
        ZPATH_LOG(AL_ERROR, "Invalid enroll_type = %d", enroll_type);
        return;
    }

    switch (api_type) {

    case ENROLLMENT_API_FETCH_CSR_DETAILS:
        snprintf(endpoint, endpoint_len,  "https://%s/zpn/api/v2/admin/%s/onboard/details", api_hostname, endpoint_type);
        break;

    case ENROLLMENT_API_SEND_CSR:
        snprintf(endpoint, endpoint_len,  "https://%s/zpn/api/v2/admin/%s/onboard/csr", api_hostname, endpoint_type);
        break;

    case ENROLLMENT_API_GET_CERTIFICATE:
        snprintf(endpoint, endpoint_len,  "https://%s/zpn/api/v2/admin/%s/onboard/certificate", api_hostname, endpoint_type);
        break;

    case ENROLLMENT_API_GET_ENROLL_DETAILS:
        snprintf(endpoint, endpoint_len,  "https://%s/zpn/api/v2/admin/%s/onboard/enrollment/details", api_hostname, endpoint_type);
        break;

    default:
        snprintf(endpoint, endpoint_len, "%s", "invalid endpoint");
        break;
    }

}

/*
 * Get enrollment endpoint type
 */
static void get_enrollment_api_endpoint_v3(enum zpn_enrollment_type enroll_type,
                                           enum zpn_enrollment_api_type api_type,
                                           const char *api_hostname,
                                           char *endpoint,
                                           int endpoint_len,
                                           int64_t customer_gid)
{
    char *endpoint_type;

    /* V3 supports broker and connector with customer_gid (for rate limit at services side), component ::= <assistant|broker|znf> */

    // POST on https://api.<cloud name>/zpn/api/v3/admin/customers/<customer_gid>/<component>/onboard/details
    // POST on https://api.<cloud name>/zpn/api/v3/admin/customers/<customer_gid>/<component>/onboard/signCSRAndGetCertificate

    switch (enroll_type) {
    case ZPN_ENROLLMENT_TYPE_ASSISTANT:
    case ZPN_ENROLLMENT_TYPE_NP:
        endpoint_type = ENROLLMENT_API_ENDPOINT_TYPE_ASSISTANT;
        break;

    case ZPN_ENROLLMENT_TYPE_PRIVATE_BROKER:
        endpoint_type = ENROLLMENT_API_ENDPOINT_TYPE_PRIVATE_BROKER;
        break;
    case ZPN_ENROLLMENT_TYPE_SITEC:
        endpoint_type = ENROLLMENT_API_ENDPOINT_TYPE_SITEC;
        break;
    default:
        ZPATH_LOG(AL_ERROR, "Invalid enroll_type = %d", enroll_type);
        return;
    }

    switch (api_type) {

    case ENROLLMENT_API_FETCH_CSR_DETAILS:
        snprintf(endpoint, endpoint_len,  "https://%s/zpn/api/v3/admin/customers/%"PRId64"/%s/onboard/details", api_hostname, customer_gid, endpoint_type);
        break;

    case ENROLLMENT_API_SEND_CSR_AND_GET_CERT:
        snprintf(endpoint, endpoint_len,  "https://%s/zpn/api/v3/admin/customers/%"PRId64"/%s/onboard/signCSRAndGetCertificate", api_hostname, customer_gid, endpoint_type);
        break;

    case ENROLLMENT_API_SEND_PUB_KEY:
        snprintf(endpoint, endpoint_len,  "https://%s/zpn/api/v3/admin/customers/%"PRId64"/%s/onboard/np-public-key", api_hostname, customer_gid, endpoint_type);
        break;

    default:
        snprintf(endpoint, endpoint_len, "%s", "invalid endpoint");
        break;
    }

}

/*
 * Get enrollment endpoint type - V4
 */
static void get_enrollment_api_endpoint_v4(enum zpn_enrollment_type enroll_type, enum zpn_enrollment_api_type api_type, const char *api_hostname,
                                           char *endpoint, int endpoint_len, int64_t customer_gid)
{
    char *endpoint_type;

    /* Only V2 supports broker and connector, component ::= <assistant|broker|znf> */

    // POST on https://api.<cloud name>/zpn/api/v4/admin/<component>/onboard/details
    // POST on https://api.<cloud name>/zpn/api/v4/admin/<component>/onboard/csr
    // POST on https://api.<cloud name>/zpn/api/v4/admin/<component>/onboard/certificate

    // CNAME: <id>.<asst|pb|znf>.<cloud name>

    switch (enroll_type) {
    case ZPN_ENROLLMENT_TYPE_ASSISTANT:
    case ZPN_ENROLLMENT_TYPE_NP:
        endpoint_type = ENROLLMENT_API_ENDPOINT_TYPE_ASSISTANT;
        break;

    case ZPN_ENROLLMENT_TYPE_PRIVATE_BROKER:
        endpoint_type = ENROLLMENT_API_ENDPOINT_TYPE_PRIVATE_BROKER;
        break;
    case ZPN_ENROLLMENT_TYPE_SITEC:
        endpoint_type = ENROLLMENT_API_ENDPOINT_TYPE_SITEC;
        break;
    default:
        ZPATH_LOG(AL_ERROR, "Invalid enroll_type = %d", enroll_type);
        return;
    }

    switch (api_type) {

    case ENROLLMENT_API_FETCH_CSR_DETAILS:
        snprintf(endpoint, endpoint_len,  "https://%s/zpn/api/v4/admin/customers/%"PRId64"/%s/onboard/details", api_hostname, customer_gid, endpoint_type);
        break;

    case ENROLLMENT_API_SEND_CSR:
        snprintf(endpoint, endpoint_len,  "https://%s/zpn/api/v4/admin/customers/%"PRId64"/%s/onboard/csr", api_hostname, customer_gid, endpoint_type);
        break;

    case ENROLLMENT_API_GET_CERTIFICATE:
        snprintf(endpoint, endpoint_len,  "https://%s/zpn/api/v4/admin/customers/%"PRId64"/%s/onboard/certificate", api_hostname, customer_gid, endpoint_type);
        break;

    case ENROLLMENT_API_GET_ENROLL_DETAILS:
        snprintf(endpoint, endpoint_len,  "https://%s/zpn/api/v4/admin/customers/%"PRId64"/%s/onboard/enrollment/details", api_hostname, customer_gid, endpoint_type);
        break;

    case ENROLLMENT_API_SEND_PUB_KEY:
        snprintf(endpoint, endpoint_len,  "https://%s/zpn/api/v4/admin/customers/%"PRId64"/%s/onboard/np-public-key", api_hostname, customer_gid, endpoint_type);
        break;

    default:
        snprintf(endpoint, endpoint_len, "%s", "invalid endpoint");
        break;
    }

}

/*
 * Get enrollment endpoint type
 */
static void get_enrollment_api_endpoint(enum zpn_enrollment_type enroll_type, enum zpn_enrollment_style enroll_style, enum zpn_enrollment_api_type api_type,
                                        const char *api_hostname, char *endpoint, int endpoint_len, int64_t customer_gid)
{
    if (enroll_style == ZPN_ENROLLMENT_STYLE_V1) {
        get_enrollment_api_endpoint_v1(enroll_type, api_type, endpoint, endpoint_len);
    } else if (enroll_style == ZPN_ENROLLMENT_STYLE_V2) {
        get_enrollment_api_endpoint_v2(enroll_type, api_type, api_hostname, endpoint, endpoint_len);
    } else if (enroll_style == ZPN_ENROLLMENT_STYLE_V3) {
        get_enrollment_api_endpoint_v3(enroll_type, api_type, api_hostname, endpoint, endpoint_len, customer_gid);
    } else if (enroll_style == ZPN_ENROLLMENT_STYLE_V4) {
        get_enrollment_api_endpoint_v4(enroll_type, api_type, api_hostname, endpoint, endpoint_len, customer_gid);
    }


    ZPATH_LOG(AL_DEBUG, "Using api endpoint: %s, style: %s, Api type: %s, api_hostname: %s\n",
              endpoint, get_enrollement_style_name(enroll_style), get_enrollement_api_name(api_type), api_hostname);
}

/*
 * Get enrollment type name
 */
static const char* zpn_enroll_get_enrollement_name(void)
{
    static char *names[] = { "Unknown", "Connector", "Service edge", "Site Controller", "Sarge", "NP Connector"};

    /* Get our enrollment type */
    enum zpn_enrollment_type enroll_type = s_enroll_state.enroll_type;

    if ((enroll_type >= ZPN_ENROLLMENT_TYPE_ASSISTANT) && (enroll_type <= ZPN_ENROLLMENT_TYPE_NP)) {
        return names[(int)enroll_type];
    }

    assert(0);

    return names[0];
}

/*
 * Get Nonce association type from enrollment details
 */
static int zpn_enroll_get_association_type(enum zpn_enrollment_type enroll_type, char *association_type,
                                           size_t association_len)
{
    switch (enroll_type) {
        case ZPN_ENROLLMENT_TYPE_ASSISTANT:
            snprintf(association_type, association_len, "ASSISTANT_GRP");
            break;
        case ZPN_ENROLLMENT_TYPE_PRIVATE_BROKER:
            snprintf(association_type, association_len, "BROKER_GRP");
            break;
        case ZPN_ENROLLMENT_TYPE_SITEC:
            snprintf(association_type, association_len, "SITE_CONTROLLER_GRP");
            break;
        case ZPN_ENROLLMENT_TYPE_NP:
            snprintf(association_type, association_len, "NP_ASSISTANT_GRP");
            break;
        default:
            ZPATH_LOG(AL_ERROR, "[V4 enrollment] Wrong enrollment type [%s]", zpn_enroll_get_enrollement_name());
            return ZPATH_RESULT_ERR;
    }

    return ZPATH_RESULT_NO_ERROR;
}

/*
 * Sign and b64 encode epoch time in seconds using rsa_key
 */
static int sign_epoch_s(const struct zcrypt_rsa_key *rsa_key, int64_t timestamp, char *signature, size_t *signature_length)
{
    char signme[1000] = { 0 };
    uint8_t signstr[1000];
    unsigned char sha[EVP_MAX_MD_SIZE] = { 0 };
    size_t signme_len;
    size_t out_len;
    EVP_MD_CTX *ctx;
    int res;
    unsigned int shalen;

    sprintf((char *)signme, "%"PRId64, timestamp);

    if (!(ctx = EVP_MD_CTX_create())) {
        ZPATH_LOG(AL_ERROR, "Could not SHA create");
        return ZCRYPT_RESULT_ERR;
    }

    if (1 != EVP_DigestInit_ex(ctx, EVP_sha256(), NULL)) {
        EVP_MD_CTX_destroy(ctx);
        ZPATH_LOG(AL_ERROR, "Could not SHA init");
        return ZCRYPT_RESULT_ERR;
    }

    if (1 != EVP_DigestUpdate(ctx, signme, strlen(signme))) {
        EVP_MD_CTX_destroy(ctx);
        ZPATH_LOG(AL_ERROR, "Could not SHA update");
        return ZPATH_RESULT_ERR;
    }

    if (1 != EVP_DigestFinal_ex(ctx, sha, &shalen)) {
        ZPATH_LOG(AL_ERROR, "Could not SHA final");
        EVP_MD_CTX_destroy(ctx);
        return ZPATH_RESULT_ERR;
    }
    EVP_MD_CTX_destroy(ctx);

    signme_len = sizeof(signstr);
    res = zcrypt_sign(sha,
                      shalen,
                      signstr,
                      &signme_len,
                      rsa_key);
    if (res != ZCRYPT_RESULT_NO_ERROR) {
        ZPATH_LOG(AL_ERROR, "Could not sign");
        return ZPATH_RESULT_ERR;
    }

    out_len = base64_encoded_size(signme_len);
    if (out_len >= (*signature_length) - 1) {
        ZPATH_LOG(AL_ERROR, "Signature too long");
        return ZPATH_RESULT_ERR;
    }

    base64_encode_binary(signature, signstr, signme_len);
    *signature_length = out_len;

    return ZPATH_RESULT_NO_ERROR;
}

/* Get enrollment time epoch and signature */
static int get_enrollment_epoch_s(const struct zcrypt_rsa_key *rsa_key, int64_t *timestamp, char *signature, size_t *signature_length)
{
    *timestamp = epoch_s();

    return sign_epoch_s(rsa_key, *timestamp, signature, signature_length);
}

static int get_response_body_copy_dump(struct evbuffer *body, char *buf, size_t buf_len)
{
    size_t len;
    char tmp_buf[8*BUFSIZ];
    JSON_Value *json;

    if (!body) {
        return ZPATH_RESULT_BAD_ARGUMENT;
    }

    len = evbuffer_get_length(body);
    if (len > (sizeof(tmp_buf) - 1)) {
        ZPATH_LOG(AL_ERROR, "Unexpectedly long response");
        return ZPATH_RESULT_ERR_TOO_LARGE;
    }

    evbuffer_copyout(body, tmp_buf, len);
    tmp_buf[len] = 0;

    json = json_parse_string(tmp_buf);
    if (!json) {
        ZPATH_LOG(AL_ERROR, "Could not parse JSON: <%s>", tmp_buf);
        return ZPATH_RESULT_BAD_DATA;
    }

    if (json_serialize_to_buffer_pretty(json, buf, buf_len) == JSONSuccess) {
        json_value_free(json);
        return ZPATH_RESULT_NO_ERROR;
    }

    json_value_free(json);
    return ZPATH_RESULT_ERR;
}



/*
 * Enroll API init
 */
int zpn_enroll_init(struct zpn_enroll_state **state)
{
    /* Double init is bad ! */
    if (s_enroll_state.magic == ENROLLMENT_API_INIT_MAGIC) {
        ZPN_LOG(AL_ERROR, "Enroll API is already initialized");
        *state = NULL;
        return ZPATH_RESULT_BAD_STATE;
    }

    bzero(&s_enroll_state, sizeof(s_enroll_state));
    s_enroll_state.magic = ENROLLMENT_API_INIT_MAGIC;

    /* Return reference to static library state ! */
    *state = &s_enroll_state;

    return ZPATH_RESULT_NO_ERROR;
}

/*
 * Deprecate this code after around 2025 June - as by that time customers would have
 * upgraded their connectors and fetching disk id will be successful even when
 * devices are not present in /dev/disk/by-uuid/. (This can be confirmed from
 * the stats which show how many customers are using empty disk id).
 */
static EVP_MD_CTX *zpn_decrypt_with_disk_id_vm_id_combo(int decrypt_type)
{
    EVP_MD_CTX *sha_context;
    char data[ZHW_ID_MAX_LEN] = {0};
    size_t data_len = sizeof(data);
    char err[1000] = {0};
    int empty_disk_id = 0;
    int imdsv1 = 0;

    if ((decrypt_type == DECRYPT_WITH_EMPTY_DISK_ID) || (decrypt_type == DECRYPT_WITH_IMDSV1_EMPTY_DISK_ID)){
        empty_disk_id = 1;
    }

    if ((decrypt_type == DECRYPT_WITH_IMDSV1_NON_EMPTY_DISK_ID) || (decrypt_type == DECRYPT_WITH_IMDSV1_EMPTY_DISK_ID)){
        imdsv1 = 1;
    }

    if (!(sha_context=EVP_MD_CTX_create())) {
        ZPN_LOG(AL_ERROR, "zpn_decrypt_with_disk_id_vm_id_combo: Failed to create digest context");
        return NULL;
    }

    if (EVP_DigestInit_ex(sha_context, EVP_sha256(), NULL) != 1) {
        EVP_MD_CTX_destroy(sha_context);
        ZPN_LOG(AL_ERROR,"zpn_decrypt_with_disk_id_vm_id_combo: Failed to use digest type EVP_sha256\n");
        return NULL;
    }

    /* Pull in a piece of compiled in key... DO NOT CHANGE THIS */
#define COMPILED_KEY "FW2PzfYcD5i6HRvLknbPcvLDmnQnUtLCTWM%EzXNeUufg"
    if (EVP_DigestUpdate(sha_context, COMPILED_KEY, strlen(COMPILED_KEY)) != 1) {
        EVP_MD_CTX_destroy(sha_context);
        ZPN_LOG(AL_ERROR,"zpn_decrypt_with_disk_id_vm_id_combo: Failed to hash data using compiled key");
        return NULL;
    }

    /* OSX doesn't like very consistent MAC addresses AT ALL */
    if (strcmp("osx", ZPATH_PLATFORM_NAME)) {
        zhw_id_get_ether(sha_context, NULL);
    }

    if (empty_disk_id == 1) {
        /* update the digest with empty disk id */
        if (EVP_DigestUpdate(sha_context, data, strlen(data)) != 1) {
            EVP_MD_CTX_destroy(sha_context);
            ZPN_LOG(AL_ERROR,"zpn_decrypt_with_disk_id_vm_id_combo: Failed to hash data using empty disk id");
            return NULL;
        }
    } else {
        /* update the digest with disk id */
        if (zhw_id_get_disk(".", data, sizeof(data), err, sizeof(err)) == 0) {
            if (EVP_DigestUpdate(sha_context, data, strlen(data)) != 1) {
                EVP_MD_CTX_destroy(sha_context);
                ZPN_LOG(AL_ERROR,"zpn_decrypt_with_disk_id_vm_id_combo: Failed to hash data using disk id");
                return NULL;
            }
        }
    }


    /* update the digest with vm id */
    if (imdsv1 == 1) {
        if (zvm_id_get(data, &data_len, err, sizeof(err), ZVM_VERSION_IMDSV1) == 0) {
            if (EVP_DigestUpdate(sha_context, data, data_len) != 1) {
                EVP_MD_CTX_destroy(sha_context);
                ZPN_LOG(AL_ERROR,"zpn_decrypt_with_disk_id_vm_id_combo: Failed to hash data using imdsv1");
                return NULL;
            }
        }
    } else {
        if (zvm_id_get(data, &data_len, err, sizeof(err), ZVM_VERSION_IMDSV2) == 0) {
            if (EVP_DigestUpdate(sha_context, data, data_len) != 1) {
                EVP_MD_CTX_destroy(sha_context);
                ZPN_LOG(AL_ERROR,"zpn_decrypt_with_disk_id_vm_id_combo: Failed to hash data using imdsv2");
                return NULL;
            }
        }
    }

    return sha_context;
}

static int
zpn_get_prev_key_from_sha_context(EVP_MD_CTX *prev_sha_context, struct zcrypt_key *prev_key)
{
    struct zhw_id prev_id;
    unsigned int len = sizeof(prev_id.id);
    if (EVP_DigestFinal_ex(prev_sha_context, prev_id.id, &len) != 1) {
        EVP_MD_CTX_destroy(prev_sha_context);
        ZPN_LOG(AL_ERROR,"Failed to retreive the digest value");
        return ZPATH_RESULT_ERR;
    }

    EVP_MD_CTX_destroy(prev_sha_context);

    if (zcrypt_gen_key(prev_key, prev_id.id, sizeof(prev_id.id)) != ZCRYPT_RESULT_NO_ERROR) {
        ZPN_LOG(AL_ERROR, "Cannot get key");
        return ZPATH_RESULT_ERR;
    }
    return ZPATH_RESULT_NO_ERROR;
}

static int
zpn_decrypt_with_bkwd_compatible_hw_id(struct zcrypt_key *prev_key, uint8_t *file_data, size_t file_len, size_t *ret_len)
{

    EVP_MD_CTX *prev_sha_context;
    char *out_buf[INSTANCE_ID_ENCRYPTED_BYTES];
    size_t out_len = sizeof(out_buf);

   /* Decrypt with empty disk id - this will take care of IMDSv2 if it is AWS vm */
    prev_sha_context = zpn_decrypt_with_disk_id_vm_id_combo(DECRYPT_WITH_EMPTY_DISK_ID);
    if (prev_sha_context == NULL) {
        ZPN_LOG(AL_ERROR,"Failed to get the sha context for empty disk id");
    } else if (zpn_get_prev_key_from_sha_context(prev_sha_context, prev_key) == ZPATH_RESULT_NO_ERROR) {
        if (zcrypt_decrypt(prev_key, file_data, file_len, out_buf, &out_len) == ZCRYPT_RESULT_NO_ERROR) {
            ZPN_LOG(AL_NOTICE,"Successfully decrypted data with empty disk id from %s", FILENAME_ID);
            *ret_len = out_len;
            return ZPATH_RESULT_NO_ERROR;
        } else {
            ZPN_LOG(AL_ERROR, "Cannot decrypt data with empty disk id from %s", FILENAME_ID);
        }
    }

    if ((global_vm_type == zvm_vm_type_aws) || (global_vm_type == zvm_vm_type_aws_docker)) {
        /* Decrypt with IMDSv1 and non-empty disk id */
        prev_sha_context = zpn_decrypt_with_disk_id_vm_id_combo(DECRYPT_WITH_IMDSV1_NON_EMPTY_DISK_ID);
        if (prev_sha_context == NULL) {
            ZPN_LOG(AL_ERROR,"Failed to get the sha context for imdsv1 and non-empty disk id");
        } else if (zpn_get_prev_key_from_sha_context(prev_sha_context, prev_key) == ZPATH_RESULT_NO_ERROR) {
            if (zcrypt_decrypt(prev_key, file_data, file_len, out_buf, &out_len) == ZCRYPT_RESULT_NO_ERROR) {
                ZPN_LOG(AL_NOTICE,"Successfully decrypted data with imdsv1 and non-empty disk id from %s", FILENAME_ID);
                *ret_len = out_len;
                hw_id_fails.imdsv2_required = 1;
                return ZPATH_RESULT_NO_ERROR;
            } else {
                ZPN_LOG(AL_ERROR, "Cannot decrypt data with imdsv1 and non-empty disk id from %s", FILENAME_ID);
            }
        }

        /* Decrypt with IMDSv1 and empty disk id */
        prev_sha_context = zpn_decrypt_with_disk_id_vm_id_combo(DECRYPT_WITH_IMDSV1_EMPTY_DISK_ID);
        if (prev_sha_context == NULL) {
            ZPN_LOG(AL_ERROR,"Failed to get the sha context for imdsv1 and empty disk id");
        } else if (zpn_get_prev_key_from_sha_context(prev_sha_context, prev_key) == ZPATH_RESULT_NO_ERROR) {
            if (zcrypt_decrypt(prev_key, file_data, file_len, out_buf, &out_len) == ZCRYPT_RESULT_NO_ERROR) {
                ZPN_LOG(AL_NOTICE,"Successfully decrypted data with imdsv1 and empty disk id from %s", FILENAME_ID);
                *ret_len = out_len;
                hw_id_fails.imdsv2_required = 1;
                return ZPATH_RESULT_NO_ERROR;
            } else {
                ZPN_LOG(AL_ERROR, "Cannot decrypt data with empty disk id from %s", FILENAME_ID);
            }
        }
    }
    return ZPATH_RESULT_ERR;
}

/*
 * Get Instance ID -
 */
int zpn_enroll_get_instance_id(struct zcrypt_key *key, uint8_t *instance_bytes)
{
    FILE *fp;
    uint8_t file_data[20000];
    size_t file_len;
    size_t out_len;

    fp = fopen(FILENAME_ID, "r");
    if (!fp) {
        fp = fopen(FILENAME_ID, "w");
        if (!fp) {
            ZPN_LOG(AL_ERROR, "Cannot write %s - %s\n", FILENAME_ID, strerror(errno));
            return ZPATH_RESULT_ERR;
        }
        if (zcrypt_rand_bytes(instance_bytes, INSTANCE_ID_BYTES) != ZCRYPT_RESULT_NO_ERROR) {
            ZPN_LOG(AL_ERROR, "Cannot generate crypto random bytes\n");
            fclose(fp);
            unlink(FILENAME_ID);
            return ZPATH_RESULT_ERR;
        }

        file_len = sizeof(file_data);
        if (zcrypt_encrypt(key, instance_bytes, INSTANCE_ID_BYTES, file_data, &file_len) != ZCRYPT_RESULT_NO_ERROR) {
            ZPN_LOG(AL_ERROR, "Cannot encrypt data\n");
            fclose(fp);
            unlink(FILENAME_ID);
            return ZPATH_RESULT_ERR;
        }

        if (fwrite(file_data, file_len, 1, fp) != 1) {
            ZPN_LOG(AL_ERROR, "Cannot write to file %s\n", FILENAME_ID);
            fclose(fp);
            unlink(FILENAME_ID);
            return ZPATH_RESULT_ERR;
        }
        fclose(fp);
        return ZPATH_RESULT_NO_ERROR;
    } else {
        file_len = fread(file_data, 1, sizeof(file_data), fp);
        fclose(fp);
        if (file_len != INSTANCE_ID_ENCRYPTED_BYTES) {
            ZPN_LOG(AL_ERROR, "Bad file size for %s", FILENAME_ID);
            return ZPATH_RESULT_ERR;
        }
        char *out_buf[INSTANCE_ID_ENCRYPTED_BYTES];
        out_len = sizeof(out_buf);
        if (zcrypt_decrypt(key, file_data, file_len, out_buf, &out_len) != ZCRYPT_RESULT_NO_ERROR) {
            struct zcrypt_key prev_cfg_hw_key;
            if(zpn_decrypt_with_bkwd_compatible_hw_id(&prev_cfg_hw_key, file_data, file_len, &out_len) != ZCRYPT_RESULT_NO_ERROR) {
                ZPN_LOG(AL_ERROR, "Cannot decrypt data from %s, in backward compatible way", FILENAME_ID);
                return ZPATH_RESULT_ERR;
            } else { /* decrypt with empty disk id is successful */
                ZPN_LOG(AL_NOTICE, "Detected change in hardware id, decrypt with previous hw id is successful");
                /* update the key with prev key so that caller uses this key for further processing */
                memcpy(key, &prev_cfg_hw_key, sizeof(prev_cfg_hw_key));
            }
        }
        if (out_len != INSTANCE_ID_BYTES) {
            ZPN_LOG(AL_ERROR, "Bad decrypted size from %s", FILENAME_ID);
            return ZPATH_RESULT_ERR;
        }
        memcpy(instance_bytes, out_buf, out_len);
        return ZPATH_RESULT_NO_ERROR;
    }
}

/*
 * Extract Key Fields -
 */
int zpn_enroll_extract_key_fields(char *key,
                                  int *shard_id,
                                  char *api_name,
                                  size_t api_name_len,
                                  char *cloud_name,
                                  size_t cloud_name_len,
                                  char **root_cert,
                                  time_t *root_cert_time)
{
    char api_name_copy[API_NAME_BUF_SIZE];
    char *saveptr = NULL;
    char *token = NULL;
    char *pipe1 = NULL;
    char *pipe2 = NULL;
    char *walk;
    size_t api_prefix_len;
    long val;
    int i;

    for (walk = key; (*walk) && (!pipe1 || !pipe2); walk++) {
        if ((*walk) == '|') {
            if (pipe1) {
                pipe2 = walk;
                break;
            } else {
                pipe1 = walk;
            }
        } else {
            if (!pipe1) {
                if (!isdigit(*walk)) {
                    break;
                }
            }
        }
    }
    if (!pipe1 || !pipe2) {
        ZPN_LOG(AL_ERROR, "Provisioning key is not in three sections, || - most likely key is not copied properly, please recopy and restart %s",
                zpn_enroll_get_enrollement_name());
        return ZPATH_RESULT_ERR;
    }
    val = strtol(key, NULL, 10);
    if (!val) {
        ZPN_LOG(AL_ERROR, "Provisioning key does not begin with shard - most likely key is not copied properly, please recopy and restart %s",
                zpn_enroll_get_enrollement_name());
        return ZPATH_RESULT_ERR;
    }
    *shard_id = val;

    if ((pipe2 - pipe1) >= api_name_len) {
        ZPN_LOG(AL_ERROR, "Provisioning key API too long - most likely key is not copied properly, please recopy and restart %s",
                zpn_enroll_get_enrollement_name());
        return ZPATH_RESULT_ERR;
    }
    if ((pipe2 - pipe1) < 3) {
        ZPN_LOG(AL_ERROR, "Provisioning key API too short - most likely key is not copied properly, please recopy and restart %s",
                zpn_enroll_get_enrollement_name());
        return ZPATH_RESULT_ERR;
    }
    for (i = 0; i < (pipe2 - pipe1) - 1; i++) {
        api_name[i] = pipe1[i + 1];
    }
    api_name[i] = 0;
    struct zpath_cloud_config *cloud_config=zpath_get_cloud_config_from_apiname(api_name);
    /*  1. We will try to fetch the cloud_config based on the api_name
        2. If no config is available, then for flex cloud, we get the dev cloud's config
     */
    if (!cloud_config){
        ZPN_LOG(AL_ERROR, "Fetching zpa_cloud config failed for the apiname: %s dev check %d\n", api_name,ZPATH_LOCAL_IS_DEV_ENV);
        if (zpath_is_flex_cloud(api_name)) {
            snprintf(api_name_copy, sizeof(api_name_copy), "%s", api_name);
            token = strtok_r(api_name_copy, ".", &saveptr);
            api_prefix_len = strlen(token) + 1;
            snprintf(s_enroll_state.cfg_key_cloud, sizeof(s_enroll_state.cfg_key_cloud), "%s", api_name + api_prefix_len);
            cloud_config=zpath_get_cloud_config_from_name(ZPA_CLOUD_CONFIG_DEV_CLOUD_NAME);
            if ( !cloud_config ) {
                ZPN_LOG(AL_ERROR, "Cloud Configuration NOT Found ");
                return ZPATH_RESULT_ERR;
            }
            if (cloud_name) {
                snprintf(cloud_name, cloud_name_len, "%s", s_enroll_state.cfg_key_cloud);
            }
            /* If flex cloud or any new cloud is configured using zpath_local table. */
            s_enroll_state.can_drop_fips = cloud_config->zpa_cloud_root_certs[0].cert_can_drop_fips;
            return ZPATH_RESULT_NO_ERROR;
        } else {
            return ZPATH_RESULT_ERR;
        }
    } else {
        snprintf(s_enroll_state.cfg_key_cloud, sizeof(s_enroll_state.cfg_key_cloud), "%s", cloud_config->cloud_name);
    }
    if (root_cert) {
        *root_cert = cloud_config->zpa_cloud_root_certs[0].root_cert;
        s_enroll_state.can_drop_fips = cloud_config->zpa_cloud_root_certs[0].cert_can_drop_fips;
    }
    if (root_cert_time) {
        *root_cert_time = cloud_config->zpa_cloud_root_certs[0].root_cert_time;
    }
    if (cloud_name && cloud_name[0] == '\0' ) {
        snprintf(cloud_name, cloud_name_len, "%s", s_enroll_state.cfg_key_cloud);
    }
    return ZPATH_RESULT_NO_ERROR;
}

typedef void (callback_f)(void);

static void rm_file(void)
{
    unlink(FILENAME_PROVISION_KEY);
}

static int get_provisioning_key_env(char *env, size_t env_len)
{
    char *e = getenv(PROVISIONING_KEY_ENV);
    if (e) {
        size_t len = strlen(e);
        if (len < env_len) {
            snprintf(env, env_len, "%s", e);
            return ZPN_RESULT_NO_ERROR;
        }
    }
    ZPN_LOG(AL_DEBUG, "Could not get provisioning key from environment variable '%s'", PROVISIONING_KEY_ENV);
    return ZPN_RESULT_ERR;
}

static int get_provisioning_key_file(char *env, size_t env_len)
{
    FILE *fp;
    size_t len;

    fp = fopen(FILENAME_PROVISION_KEY, "r");
    if (fp) {
        len = fread(env, 1, env_len - 1, fp);
        fclose(fp);
        if (len) {
            env[len] = 0;
            return ZPN_RESULT_NO_ERROR;
        }
    }
    ZPN_LOG(AL_DEBUG, "Could not get provisioning key from file '%s'", FILENAME_PROVISION_KEY);
    return ZPN_RESULT_ERR;
}

int zpn_get_provision_key_from_plain_text_for_sarge(char *provisioning_key,
                                    size_t provisioning_key_length)
{
    char env[1000];
    size_t len;

    if (get_provisioning_key_env(env, sizeof(env)) == ZPN_RESULT_NO_ERROR) {
        ZPN_LOG(AL_NOTICE, "Retrieved provisioning key from environment.");
    } else if (get_provisioning_key_file(env, sizeof(env)) == ZPN_RESULT_NO_ERROR) {
        ZPN_LOG(AL_NOTICE, "Retrieved provisioning key from file.");
    } else if (zvm_key_get_by_regex(ZVM_REGEX_ASSISTANT_NONCE,
                                    NULL,
                                    NULL,
                                    env,
                                    &len) == ZVM_RESULT_NO_ERROR) {
        ZPN_LOG(AL_NOTICE, "Retrieved provisioning key from VM.");
    } else {
        /* Failed to get provisioning key. */
        ZPN_LOG(AL_ERROR, "Could not get provisioning key");
        return ZPATH_RESULT_ERR;
    }
    len = strlen(env);
    for (int i = 0; i < len; i++) {
        if (env[i] == '\n')
            env[i] = 0;
        if (env[i] == '\r')
            env[i] = 0;
    }

    if (strlen(env) > (provisioning_key_length - 1)) {
        ZPN_LOG(AL_ERROR, "Environment variable provisioning key far too large\n");
        return ZPATH_RESULT_ERR;
    }

    sxprintf(provisioning_key, provisioning_key + provisioning_key_length, "%s", env);
    return ZPATH_RESULT_NO_ERROR;
}

int zpn_enroll_get_provision_key_from_plain_text_for_sarge(char *cloudname,
                                                           int cloudname_sz)
{
    char env[1000];
    int shard;
    char api_name[100];
    int i;
    size_t len;
    char * provisioning_key = NULL;
    int provisioning_key_length = 0;

    if (get_provisioning_key_env(env, sizeof(env)) == ZPN_RESULT_NO_ERROR) {
        ZPN_LOG(AL_NOTICE, "Retrieved provisioning key from environment.");
    } else if (get_provisioning_key_file(env, sizeof(env)) == ZPN_RESULT_NO_ERROR) {
        ZPN_LOG(AL_NOTICE, "Retrieved provisioning key from file.");
    } else if (zvm_key_get_by_regex(ZVM_REGEX_ASSISTANT_NONCE,
                                    NULL,
                                    NULL,
                                    env,
                                    &len) == ZVM_RESULT_NO_ERROR) {
        ZPN_LOG(AL_NOTICE, "Retrieved provisioning key from VM.");
    } else {
        /* Failed to get provisioning key. */
        ZPN_LOG(AL_ERROR, "Could not get provisioning key");
        return ZPATH_RESULT_ERR;
    }
    len = strlen(env);
    for (i = 0; i < len; i++) {
        if (env[i] == '\n')
            env[i] = 0;
        if (env[i] == '\r')
            env[i] = 0;
    }

    if (strlen(env) > (provisioning_key_length - 1)) {
        ZPN_LOG(AL_ERROR, "Environment variable provisioning key far too large\n");
        return ZPATH_RESULT_ERR;
    }
    if (zpn_enroll_extract_key_fields(env, &shard, api_name, sizeof(api_name), cloudname, cloudname_sz, NULL,
                                      NULL) != ZPATH_RESULT_NO_ERROR) {
        ZPN_LOG(AL_ERROR, "Invalid provisioning key: <%.*s...>\n", PROVISION_KEY_LOG_BYTES, env);
        return ZPATH_RESULT_ERR;
    }

    provisioning_key_length = sxprintf(provisioning_key, provisioning_key + provisioning_key_length, "%s", env);
    return ZPATH_RESULT_NO_ERROR;
}

/*
 * Get Provisioning Key - Read OAuth provisioing key from a file
 */
int zpn_enroll_get_oauth_key(char *provisioning_key,
                             size_t provisioning_key_length)
{
    char env[1000];
    FILE *fp;
    size_t len;

    fp = fopen(FILENAME_OAUTH_DETAILS, "r");
    if (!fp) {
        ZPN_LOG(AL_ERROR, "Failed getting OAuth provisioning details %s - %s", FILENAME_OAUTH_DETAILS, strerror(errno));
        return ZPATH_RESULT_ERR;
    }

    len = fread(env, 1, sizeof(env) - 1, fp);
    fclose(fp);
    if (!len) {
        return ZPATH_RESULT_ERR;
    }

    env[len] = 0;

    for (int i = 0; i < len; i++) {
        if (env[i] == '\n') env[i] = 0;
        if (env[i] == '\r') env[i] = 0;
    }

    provisioning_key_length = sxprintf(provisioning_key, provisioning_key + provisioning_key_length, "%s", env);

    return ZPN_RESULT_NO_ERROR;

}

/*
 * Get Provisioning Key -
 */
int zpn_enroll_get_provisioning_key(struct zcrypt_key *key,
                                    char *provisioning_key,
                                    size_t provisioning_key_length)
{
    FILE *fp;
    uint8_t file_data[20000];
    uint8_t out_data[20000];
    char env[1000];
    size_t file_len;
    size_t out_len;
    int i;
    size_t len;
    callback_f *f = NULL;

    fp = fopen(FILENAME_PROVISION_CRYPT, "r");
    if (!fp) {
        len = sizeof(env);
        if (get_provisioning_key_env(env, sizeof(env)) == ZPN_RESULT_NO_ERROR) {
            ZPN_LOG(AL_NOTICE, "Retrieved provisioning key from environment.");
        } else if (get_provisioning_key_file(env, sizeof(env)) == ZPN_RESULT_NO_ERROR) {
            ZPN_LOG(AL_NOTICE, "Retrieved provisioning key from file.");
            f = rm_file;
        } else if (zvm_key_get_by_regex(ZVM_REGEX_ASSISTANT_NONCE,
                                        NULL,
                                        NULL,
                                        env,
                                        &len) == ZVM_RESULT_NO_ERROR) {
            ZPN_LOG(AL_NOTICE, "Retrieved provisioning key from VM.");
        } else {
            /* Failed to get provisioning key. */
            //ZPN_LOG(AL_ERROR, "Could not get provisioning key");
            return ZPATH_RESULT_ERR;
        }

        len = strlen(env);
        for (i = 0; i < len; i++) {
            if (env[i] == '\n') env[i] = 0;
            if (env[i] == '\r') env[i] = 0;
        }

        if (strlen(env) > (provisioning_key_length - 1)) {
            ZPN_LOG(AL_ERROR, "Environment variable provisioning key far too large\n");
            return ZPATH_RESULT_ERR;
        }

        provisioning_key_length = sxprintf(provisioning_key, provisioning_key + provisioning_key_length, "%s", env);

        /* Should have a better verification step for provisioning key. */
        fp = fopen(FILENAME_PROVISION_CRYPT, "w");
        if (!fp) {
            ZPN_LOG(AL_ERROR, "Cannot open %s for writing\n", FILENAME_PROVISION_CRYPT);
            return ZPATH_RESULT_ERR;
        }

        file_len = sizeof(file_data);
        if (zcrypt_encrypt(key, provisioning_key, provisioning_key_length, file_data, &file_len) != ZCRYPT_RESULT_NO_ERROR) {
            ZPN_LOG(AL_ERROR, "Cannot encrypt for %s\n", FILENAME_PROVISION_CRYPT);
            fclose(fp);
            unlink(FILENAME_PROVISION_CRYPT);
            return ZPATH_RESULT_ERR;
        }

        if (fwrite(file_data, file_len, 1, fp) != 1) {
            ZPN_LOG(AL_ERROR, "Cannot write to %s\n", FILENAME_PROVISION_CRYPT);
            fclose(fp);
            unlink(FILENAME_PROVISION_CRYPT);
            return ZPATH_RESULT_ERR;
        }
        fclose(fp);

        /* Execute any cleanup we might need */
        if (f) {
            (f)();
        }
        return ZPATH_RESULT_NO_ERROR;
    } else {
        int i;

        file_len = fread(file_data, 1, sizeof(file_data), fp);
        fclose(fp);
        out_len = sizeof(out_data);
        if (out_len < file_len) {
            ZPN_LOG(AL_ERROR, "File too large: %s\n", FILENAME_PROVISION_CRYPT);
            return ZPATH_RESULT_ERR;
        }
        if (zcrypt_decrypt(key, file_data, file_len, out_data, &out_len) != ZCRYPT_RESULT_NO_ERROR) {
            ZPN_LOG(AL_ERROR, "Cannot decrypt file: %s\n", FILENAME_PROVISION_CRYPT);
            return ZPATH_RESULT_ERR;
        }
        if (out_len >= provisioning_key_length) {
            ZPN_LOG(AL_ERROR, "Decrypted file too large: %s\n", FILENAME_PROVISION_CRYPT);
            return ZPATH_RESULT_ERR;
        }

        for (i = 0; i < out_len; i++) {
            provisioning_key[i] = out_data[i];
        }
        out_data[i] = 0;

        return ZPATH_RESULT_NO_ERROR;
    }
}

/*
 * Get private key
 */
int zpn_enroll_get_private_key(struct zcrypt_key *key, struct zcrypt_rsa_key *rsa_key, char *filename)
{
    char filename_private[1000];
    char filename_public[1000];
    char filename_plain[1000];

    FILE *fp;

    if (!rsa_key) return ZPATH_RESULT_ERR;
    if (!filename) return ZPATH_RESULT_ERR;

    snprintf(filename_private, sizeof(filename_private), "%s_key.pem", filename);
    snprintf(filename_public, sizeof(filename_public), "%s_pub.pem", filename);
    snprintf(filename_plain, sizeof(filename_plain), "%s_key.txt.pem", filename);

    /* Check if keyfiles are there... Don't use the key read routines
     * for this because the key read routines can fail for multiple
     * reasons. */
    fp = fopen(filename_private, "r");
    if (fp) {
        fclose(fp);
        fp = fopen(filename_public, "r");
        if (fp) {
            fclose(fp);
        } else {
            /* Inconsistent state- private key file exists but pub
             * does not. */
            ZPN_LOG(AL_ERROR, "Missing public key file\n");
            return ZPATH_RESULT_ERR;
        }

        /* Read existing keys. */
        if (zcrypt_rsa_key_read_priv(key, rsa_key, filename_private, s_enroll_state.can_drop_fips) != ZCRYPT_RESULT_NO_ERROR) return ZPATH_RESULT_ERR;
        if (zcrypt_rsa_key_read_pub(rsa_key, filename_public) != ZCRYPT_RESULT_NO_ERROR) return ZPATH_RESULT_ERR;
    } else {

        /* Create and write new keys. */
        if (zcrypt_rsa_key_gen_keys(rsa_key) != ZCRYPT_RESULT_NO_ERROR) return ZPATH_RESULT_ERR;
        if (zcrypt_rsa_key_write_priv(key, rsa_key, filename_private) != ZCRYPT_RESULT_NO_ERROR) return ZPATH_RESULT_ERR;
        if (zcrypt_rsa_key_write_pub(rsa_key, filename_public) != ZCRYPT_RESULT_NO_ERROR) return ZPATH_RESULT_ERR;
        if (s_enroll_state.cfg_write_plain) {
            if (zcrypt_rsa_key_write_priv_plaintext(rsa_key, filename_plain) != ZCRYPT_RESULT_NO_ERROR) return ZPATH_RESULT_ERR;
        }
    }

    return ZPATH_RESULT_NO_ERROR;
}


static struct fohh_http_client* get_http_client(char *msg, SSL_CTX *ssl_ctx, const char *api_hostname)
{
    struct fohh_http_client *client;
    enum fohh_http_client_status status;
    struct fohh_ssl_status *ssl_status;
    char remote_host[1000];

    if (s_enroll_state.cfg_api_direct) {
        snprintf(remote_host, sizeof(remote_host), "%s", api_hostname);
    } else {
        switch (s_enroll_state.enroll_type) {
            case ZPN_ENROLLMENT_TYPE_ASSISTANT:
            case ZPN_ENROLLMENT_TYPE_NP:
                snprintf(remote_host, sizeof(remote_host), "co2br.%s", s_enroll_state.cfg_key_cloud);
                break;
            case ZPN_ENROLLMENT_TYPE_PRIVATE_BROKER:
                snprintf(remote_host, sizeof(remote_host), "pb2br.%s", s_enroll_state.cfg_key_cloud);
                break;
            case ZPN_ENROLLMENT_TYPE_SITEC:
                snprintf(remote_host, sizeof(remote_host), "sc2br.%s", s_enroll_state.cfg_key_cloud);
                break;

            default:
                assert(0);
                break;
        }
    }

    ZPATH_LOG(AL_NOTICE, "%s: Connecting to %s via %s.", msg, api_hostname, remote_host);

    client = fohh_http_client_create_synchronous(s_enroll_state.zcdns,
                                                 ssl_ctx,
                                                 api_hostname,
                                                 remote_host,
                                                 fohh_proxy_hostname,
                                                 443,
                                                 fohh_proxy_port,
                                                 10*(1000*1000),
                                                 &status);
    if (!client) {
        ZPATH_LOG(AL_ERROR, "Could not create HTTP context");
        return NULL;
    }

    if (status != fohh_http_client_status_success) {
        switch(status) {
        case fohh_http_client_status_dns_timeout:
            ZPATH_LOG(AL_ERROR, "DNS timed out for %s", api_hostname);
            break;
        case fohh_http_client_status_dns_fail:
            ZPATH_LOG(AL_ERROR, "DNS resolution failed for %s", api_hostname);
            break;
        case fohh_http_client_status_connect_timeout:
            ZPATH_LOG(AL_ERROR, "TCP connection timed out to %s", api_hostname);
            break;
        case fohh_http_client_status_connect_fail:
            ZPATH_LOG(AL_ERROR, "TCP connection failed to %s", api_hostname);
            break;
        case fohh_http_client_status_connect_fail_ssl:
            ssl_status = fohh_http_client_get_ssl_status(client);
            if (ssl_status->err) {
                ZPATH_LOG(AL_ERROR, "TLS Verification Failure. Failed certificate check at depth=%d, where subject=%s, issuer=%s. Error=%s",
                          ssl_status->x509_err_depth,
                          ssl_status->err_subject,
                          ssl_status->err_issuer,
                          ssl_status->x509_err ? ssl_status->x509_err : "Unknown");
            } else {
                ZPATH_LOG(AL_ERROR, "TLS connection failed before certificate verification");
            }
            break;
        case fohh_http_client_status_timeout:
            ZPATH_LOG(AL_ERROR, "Timeout attempting to reach %s", api_hostname);
            break;
        case fohh_http_client_status_failure:
        default:
            ZPATH_LOG(AL_ERROR, "Failure attempting to reach %s", api_hostname);
            break;
        }
        fohh_http_client_destroy_from_another_thread(client);
        return NULL;
    }

    return client;
}

int add_ext(STACK_OF(X509_EXTENSION) *sk, int nid, char *value)
{
    X509_EXTENSION *ex;
    ex = X509V3_EXT_conf_nid(NULL, NULL, nid, value);
    if (!ex) {
        return ZPATH_RESULT_ERR;
    }
    if (!sk_X509_EXTENSION_push(sk, ex)) {
        return ZPATH_RESULT_ERR;
    }

    return ZPATH_RESULT_NO_ERROR;
}

static X509_REQ *
sarge_get_csr(struct fohh_http_client *client,
              char *cloud,
              char *api_hostname,
              struct zcrypt_rsa_key *rsa_key,
              char *hw_fingerprint,
              char *provisioning_key,
              char *text_form,
              size_t text_form_len,
              const char *epoch_str,
              char *signature,
              enum zpn_enrollment_type enroll_type,
              enum zpn_enrollment_style enroll_style,
              int64_t customer_gid,
              const char* token)
{
    struct evbuffer *body = NULL;
    int http_status;
    X509_REQ *req = NULL;
    X509_NAME *name;
    EVP_PKEY *pkey;
    STACK_OF(X509_EXTENSION) *exts = NULL;
    RSA *rsa;
    FILE *fp;
    int res;

    fp = fopen(FILENAME_CSR_NEW, "r");
    if (fp) {
        req = PEM_read_X509_REQ(fp, &req, NULL, NULL);
        fclose(fp);
        if (!req) {
            ZPATH_LOG(AL_ERROR, "CSR file read failed from %s", FILENAME_CSR_NEW);
        }
    } else {
        /* Fetch CSR fields... */
        char body_txt[8192];
        size_t len;
        JSON_Value *json;
        JSON_Object *object;
        const char *organization;
        const char *cname;
        const char *san;
        char san_buf[512];
        char csr_details_api[ENROLLMENT_API_BUF_SIZE];
        char err_buf[BUFSIZ] = "";
        int64_t fetch_start_time_epoch_us;

        fetch_start_time_epoch_us = epoch_us();


        if (enroll_style == ZPN_ENROLLMENT_STYLE_V1) {
            get_enrollment_api_endpoint(enroll_type, enroll_style, ENROLLMENT_API_FETCH_CSR_DETAILS, api_hostname, csr_details_api, sizeof(csr_details_api), customer_gid);
            res = fohh_http_client_fetch_synchronous("Fetch CSR Fields",
                                                     client,
                                                     FOHH_HTTP_METHOD_POST,
                                                     csr_details_api,
                                                     200,          /* Expected HTTP status */
                                                     &http_status, /* Received HTTP status */
                                                     &body,
                                                     signature ? 3 : 2,
                                                     "nonce", provisioning_key,
                                                     "fingerprint", hw_fingerprint,
                                                     "signature", signature);

        } else if (enroll_style == ZPN_ENROLLMENT_STYLE_V4) {
            char association_type[32] = {0};
            switch (enroll_type) {
                case ZPN_ENROLLMENT_TYPE_ASSISTANT:
                    snprintf(association_type, sizeof(association_type), "ASSISTANT_GRP");
                    break;
                case ZPN_ENROLLMENT_TYPE_PRIVATE_BROKER:
                    snprintf(association_type, sizeof(association_type), "BROKER_GRP");
                    break;
                case ZPN_ENROLLMENT_TYPE_SITEC:
                    snprintf(association_type, sizeof(association_type), "SITE_CONTROLLER_GRP");
                    break;
                case ZPN_ENROLLMENT_TYPE_NP:
                    snprintf(association_type, sizeof(association_type), "NP_ASSISTANT_GRP");
                    break;
                default:
                    ZPATH_LOG(AL_ERROR, "[V4 enrollment] Wrong enrollment type [%s]", zpn_enroll_get_enrollement_name());
                    return NULL;
            }
            get_enrollment_api_endpoint(enroll_type, enroll_style, ENROLLMENT_API_FETCH_CSR_DETAILS, api_hostname, csr_details_api, sizeof(csr_details_api), customer_gid);
            res = fohh_http_client_fetch_synchronous_v4("Fetch CSR Fields",
                                                     client,
                                                     FOHH_HTTP_METHOD_POST,
                                                     csr_details_api,
                                                     200,          /* Expected HTTP status */
                                                     &http_status, /* Received HTTP status */
                                                     &body,
                                                     token,
                                                     signature ? 5 : 4,
                                                     "nonce", provisioning_key,
                                                     "fingerprint", hw_fingerprint,
                                                     "timestampInSec", epoch_str,
                                                     "nonceAssociationType", association_type,
                                                     "signature", signature);
        } else {
            /* V2 or V3 enrollment */
            get_enrollment_api_endpoint(enroll_type, enroll_style, ENROLLMENT_API_FETCH_CSR_DETAILS, api_hostname, csr_details_api, sizeof(csr_details_api), customer_gid);
            res = fohh_http_client_fetch_synchronous("Fetch CSR Fields",
                                                     client,
                                                     FOHH_HTTP_METHOD_POST,
                                                     csr_details_api,
                                                     200,          /* Expected HTTP status */
                                                     &http_status, /* Received HTTP status */
                                                     &body,
                                                     signature ? 4 : 3,
                                                     "nonce", provisioning_key,
                                                     "fingerprint", hw_fingerprint,
                                                     "timestampInSec", epoch_str,
                                                     "signature", signature);
        }

        if (res) {
            ZPATH_LOG(AL_ERROR, "Could not fetch CSR fields");
            if (get_response_body_copy_dump(body, err_buf, sizeof(err_buf)) == ZPATH_RESULT_NO_ERROR) {
                ZPATH_LOG(AL_ERROR, "Fetch CSR Response - nonce(<%.30s...>) fingerprint(<%.10s...>)\n%s",
                          provisioning_key, hw_fingerprint, err_buf);
            }
            if (body) {
                evbuffer_free(body);
            }
            return NULL;
        } else {
            if (s_enroll_debug) {
                if (get_response_body_copy_dump(body, err_buf, sizeof(err_buf)) == ZPATH_RESULT_NO_ERROR) {
                    ZPATH_LOG(AL_DEBUG, "Fetch CSR Response in (%"PRId64") usec - \n%s",
                              epoch_us() - fetch_start_time_epoch_us, err_buf);
                }
            }
        }

        len = evbuffer_get_length(body);
        if (len > (sizeof(body_txt) - 1)) {
            ZPATH_LOG(AL_ERROR, "Unexpectedly long response fetching CSR fields");
            evbuffer_free(body);
            return NULL;
        }

        evbuffer_copyout(body, body_txt, len);
        evbuffer_free(body);
        body_txt[len] = 0;

        json = json_parse_string(body_txt);
        if (!json) {
            ZPATH_LOG(AL_ERROR, "Could not parse JSON: <%s>", body_txt);
            return NULL;
        }
        object = json_value_get_object(json);
        if (!object) {
            ZPATH_LOG(AL_ERROR, "JSON was not an object: <%s>", body_txt);
            json_value_free(json);
            return NULL;
        }

        organization = json_object_dotget_string(object, "organization");
        if (!organization) {
            ZPATH_LOG(AL_ERROR, "No 'organization' in JSON object: <%s>", body_txt);
            json_value_free(json);
            return NULL;
        }

#if 0
        state = json_object_dotget_string(object, "state");
        if (!state) {
            ZPATH_LOG(AL_ERROR, "No 'state' in JSON object: <%s>", body_txt);
            json_value_free(json);
            return NULL;
        }
#endif // 0

        cname = json_object_dotget_string(object, "cname");
        if (!cname) {
            ZPATH_LOG(AL_ERROR, "No 'cname' in JSON object: <%s>", body_txt);
            json_value_free(json);
            return NULL;
        }

        ZPATH_LOG(AL_DEBUG, "Creating CSR with org=%s, cn=%s", organization, cname);

        req = X509_REQ_new();
        if (!req) {
            ZPATH_LOG(AL_ERROR, "Could not allocate CSR");
            json_value_free(json);
            X509_REQ_free(req);
            return NULL;
        }

#if 0
        res = X509_REQ_set_version(req, 3);
        if (res != 1) {
            ZPATH_LOG(AL_ERROR, "Could not set CSR version");
            json_value_free(json);
            X509_REQ_free(req);
            return NULL;
        }
#endif // 0

        name = X509_REQ_get_subject_name(req);
        if (!name) {
            ZPATH_LOG(AL_ERROR, "Could not get name");
            json_value_free(json);
            X509_REQ_free(req);
            return NULL;
        }

#if 0
        res = X509_NAME_add_entry_by_txt(name, "C", MBSTRING_ASC, (const unsigned char *)"UK", -1, -1, 0);
        if (res != 1) {
            ZPATH_LOG(AL_ERROR, "Could not set country in CSR");
            json_value_free(json);
            X509_REQ_free(req);
            return NULL;
        }
        res = X509_NAME_add_entry_by_txt(name, "ST", MBSTRING_ASC, (const unsigned char *)state, -1, -1, 0);
        if (res != 1) {
            ZPATH_LOG(AL_ERROR, "Could not set State in CSR");
            json_value_free(json);
            X509_REQ_free(req);
            return NULL;
        }
        res = X509_NAME_add_entry_by_txt(name, "L", MBSTRING_ASC, (const unsigned char *)"elll", -1, -1, 0);
        if (res != 1) {
            ZPATH_LOG(AL_ERROR, "Could not set L in CSR");
            json_value_free(json);
            X509_REQ_free(req);
            return NULL;
        }
#endif // 0
        res = X509_NAME_add_entry_by_txt(name, "O", MBSTRING_ASC, (const unsigned char *)organization, -1, -1, 0);
        if (res != 1) {
            ZPATH_LOG(AL_ERROR, "Could not set Organization(%s) in CSR(%d)", organization, res);
            json_value_free(json);
            X509_REQ_free(req);
            return NULL;
        }

        res = X509_NAME_add_entry_by_txt(name, "CN", MBSTRING_ASC, (const unsigned char *)cname, -1, -1, 0);
        if (res != 1) {
            ZPATH_LOG(AL_ERROR, "Could not set Common Name(%s) in CSR(%d)", cname, res);
            json_value_free(json);
            X509_REQ_free(req);
            return NULL;
        }

        if (enroll_type == ZPN_ENROLLMENT_TYPE_SITEC) {
            san = json_object_dotget_string(object, "san");
            if (!san) {
                ZPATH_LOG(AL_ERROR, "No 'san' in JSON object: <%s>", body_txt);
                json_value_free(json);
                return NULL;
            }
            snprintf(san_buf, sizeof(san_buf), "DNS:%s", san);
            exts = sk_X509_EXTENSION_new_null();
            res = add_ext(exts, NID_subject_alt_name, san_buf);
            if (res) {
                ZPATH_LOG(AL_ERROR, "Could not add SAN ext %s in CSR", san_buf);
                json_value_free(json);
                X509_REQ_free(req);
                return NULL;
            }
            X509_REQ_add_extensions(req, exts);
            sk_X509_EXTENSION_pop_free(exts, X509_EXTENSION_free);
        }
        json_value_free(json);
#if 0
        exts = sk_X509_EXTENSION_new_null();
        add_ext(exts, NID_key_usage, "critical");
        X509_REQ_add_extensions(req, exts);
        sk_X509_EXTENSION_pop_free(exts, X509_EXTENSION_free);
#endif // 0

        rsa = zcrypt_rsa_key_get_rsa(rsa_key);
        if (!rsa) {
            ZPATH_LOG(AL_ERROR, "Could not get RSA key for CSR");
            X509_REQ_free(req);
            return NULL;
        }

        pkey = EVP_PKEY_new();
        res = EVP_PKEY_set1_RSA(pkey, rsa);
        if (res != 1) {
            ZPATH_LOG(AL_ERROR, "Could not set RSA key for CSR");
            X509_REQ_free(req);
            EVP_PKEY_free(pkey);
            return NULL;
        }

        res = X509_REQ_set_pubkey(req, pkey);
        if (res != 1) {
            ZPATH_LOG(AL_ERROR, "Could not set public key for CSR");
            X509_REQ_free(req);
            return NULL;
        }

        res = X509_REQ_sign(req, pkey, EVP_sha256());
        if (res <= 0) {
            ZPATH_LOG(AL_ERROR, "Could not sign CSR: %d", res);
            X509_REQ_free(req);
            return NULL;
        }
        EVP_PKEY_free(pkey);

        /* At this point we should have a CSR... */
        fp = fopen(FILENAME_CSR_NEW, "w");
        if (!fp) {
            ZPATH_LOG(AL_ERROR, "Could not open CSR file for writing");
            X509_REQ_free(req);
            return NULL;
        }

        res = PEM_write_X509_REQ(fp, req);
        fclose(fp);
        if (res != 1) {
            ZPATH_LOG(AL_ERROR, "Could not write CSR");
            X509_REQ_free(req);
            return NULL;
        }

        ZPATH_LOG(AL_DEBUG, "Created CSR");
    }

    {
        int count;

        BIO *certBio = BIO_new(BIO_s_mem());
        if (!certBio) {
            ZPATH_LOG(AL_ERROR, "Could not transform CSR");
            X509_REQ_free(req);
            return NULL;
        }

        if (PEM_write_bio_X509_REQ_NEW(certBio, req) != 1) {
            ZPATH_LOG(AL_ERROR, "Could not read transform CSR");
            BIO_free(certBio);
            X509_REQ_free(req);
            return NULL;
        }

        count = BIO_read(certBio, text_form, text_form_len - 1);
        if (count <= 0) {
            ZPATH_LOG(AL_ERROR, "Could not write transform CSR");
            BIO_free(certBio);
            X509_REQ_free(req);
            return NULL;
        }
        text_form[count] = 0;
        BIO_free(certBio);
    }

    return req;
}

#define MAX_CERTS 10
#define BUF_SIZE 60000

static int extract_certs_from_response_v2(struct evbuffer *body, char *cert[MAX_CERTS], int num_certs, int *certs_count)
{
    char buf[BUF_SIZE];
    char **pems;
    int cert_count = 0;
    ssize_t len;
    int i;

    len = evbuffer_copyout(body, buf, sizeof(buf) - 1);
    if (len <= 0) return ZPATH_RESULT_ERR;

    pems = zcrypt_read_pems_from_buffer(buf, len, &cert_count);
    if (pems) {
        if (cert_count > num_certs) {
            ZPN_LOG(AL_ERROR, "Decoded certs: %d, exceeds size of return certs list: %d", cert_count, num_certs);
            zcrypt_pems_free(pems);
            return ZPATH_RESULT_ERR_TOO_LARGE;
        }
        for (i = 0; i < cert_count; i++) {
            /* Make a copy of certs, caller should free this */
            cert[i] = ZPN_STRDUP(pems[i], strlen(pems[i]));
            if (s_enroll_debug) {
                ZPATH_LOG(AL_DEBUG, "Cert[%d]: %s, Len=%d\n", i, cert[i], (int)strlen(cert[i]));
            }
        }
        zcrypt_pems_free(pems);
        *certs_count = cert_count;
    }

    return ZPATH_RESULT_NO_ERROR;
}

static int extract_certs_from_response_v3(struct evbuffer *body, char *cert[MAX_CERTS], int num_certs, int *certs_count)
{
    char *buf;
    const char *cert_chain;
    char **pems;
    int cert_count = 0;
    ssize_t len;
    size_t cert_chain_len;
    int i;

    buf = ZPN_MALLOC(BUF_SIZE);
    len = evbuffer_copyout(body, buf, BUF_SIZE - 1);
    buf[len] = '\0';
    if (len <= 0) {
        ZPN_LOG(AL_ERROR, "Could not extract certs from V3 API response, len = %ld", len);
        ZPN_FREE(buf);
        return ZPATH_RESULT_ERR;
    }

    JSON_Value* jv = json_parse_string(buf);
    if (json_value_get_type(jv) != JSONObject) {
        ZPATH_LOG(AL_ERROR, "Failed to validate JSON in V3 api response for send CSR and get cert");
        if (jv) {
            json_value_free(jv);
        }
        ZPN_FREE(buf);
        return ZPATH_RESULT_ERR;
    }

    JSON_Object* jo = json_value_get_object(jv);
    if (!jo) {
        ZPATH_LOG(AL_ERROR, "Failed to validate JSON in V3 api response for send CSR and get cert");
        if (jv) {
            json_value_free(jv);
        }
        ZPN_FREE(buf);
        return ZPATH_RESULT_ERR;
    }

    cert_chain = json_object_get_string (jo, "cert_chain");
    if (cert_chain) {
        ZPATH_LOG(AL_DEBUG, "Cert chain received from 'Send CSR and get cert' V3 api response: %s", cert_chain);
    } else {
        ZPATH_LOG(AL_ERROR, "Cert chain is NULL in 'Send CSR and get cert' V3 api response");
        if (jv) json_value_free(jv);
        ZPN_FREE(buf);
        return ZPATH_RESULT_ERR;
    }

    cert_chain_len = strnlen(cert_chain, len);

    pems = zcrypt_read_pems_from_buffer(cert_chain, cert_chain_len, &cert_count);
    if (pems) {
        if (cert_count > num_certs) {
            ZPN_LOG(AL_ERROR, "Decoded certs: %d, exceeds size of return certs list in V3 api response: %d", cert_count, num_certs);
            zcrypt_pems_free(pems);
            if (jv) json_value_free(jv);
            ZPN_FREE(buf);
            return ZPATH_RESULT_ERR_TOO_LARGE;
        }
        for (i = 0; i < cert_count; i++) {
            /* Make a copy of certs, caller should free this */
            cert[i] = ZPN_STRDUP(pems[i], strlen(pems[i]));
            if (s_enroll_debug) {
                ZPATH_LOG(AL_DEBUG, "Cert[%d]: %s, Len=%d\n", i, cert[i], (int)strlen(cert[i]));
            }
        }
        zcrypt_pems_free(pems);
        *certs_count = cert_count;
    }

    if (jv) json_value_free(jv);
    ZPN_FREE(buf);

    return ZPATH_RESULT_NO_ERROR;
}


/*
 * Extract certs from response and write it. This function only supports V2 and V3 style enrollments.
 *
 * For V1 - use write_certificates_v1 as response in V1 comes in completely different form (pretty old format)
 */
static int write_certificates(struct evbuffer *body, int *have_intermediates, enum zpn_enrollment_style enroll_style)
{
    char *cert[MAX_CERTS];
    char str[1000];
    char cert_failure_remediation_str[1000];
    int cert_count = 0;
    int i;
    int res;
    FILE *fp;
    size_t len;

    if (enroll_style == ZPN_ENROLLMENT_STYLE_V3) {
        /* Send CSR and get cert response in V3 API is in JSON form. Extract certs from JSON. */
        res = extract_certs_from_response_v3(body, cert, MAX_CERTS, &cert_count);
        if (res != ZPATH_RESULT_NO_ERROR) {
            ZPATH_LOG(AL_ERROR, "Unable to extract individual certs from certificate response received from V3 API: %s", zpath_result_string(res));
            return ZPATH_RESULT_ERR;
        }
    } else if (enroll_style == ZPN_ENROLLMENT_STYLE_V2 || enroll_style == ZPN_ENROLLMENT_STYLE_V4) {
        /* V2 style */
        res = extract_certs_from_response_v2(body, cert, MAX_CERTS, &cert_count);
        if (res != ZPATH_RESULT_NO_ERROR) {
            ZPATH_LOG(AL_ERROR, "Unable to extract individual certs from certificate response received from V2 API: %s", zpath_result_string(res));
            return ZPATH_RESULT_ERR;
        }
    } else {
        ZPATH_LOG(AL_ERROR, "Invalid enrollment style. Only V2 and V3 are supported.");
        return ZPATH_RESULT_ERR;
    }

    ZPATH_LOG(AL_DEBUG, "Found %d certs", cert_count);

    if (s_enroll_debug) {
        for (i = 0; i < cert_count; i++) {
            ZPATH_LOG(AL_DEBUG, "Cert %d = \n%s", i, cert[i]);
        }
    }

    if (cert_count < 2) {
        ZPATH_LOG(AL_ERROR, "Did not receive enough certificates from API");
        return ZPATH_RESULT_ERR;
    }

    /* First cert is our cert. */
    /* All certs in-between are intermediates. */
    /* Last cert is root. */

    /* Must verify whole chain. */
    res = zcrypt_verify_chain(&(cert[cert_count - 1]), 1,  // Roots
                              NULL, 0,                     // CRLs
                              &(cert[1]), cert_count - 2,  // Intermediates
                              cert[0],
                              zcrypt_cert_purpose_any,
                              str, sizeof(str), cert_failure_remediation_str, sizeof(cert_failure_remediation_str));    // Ours
    if (res != ZCRYPT_RESULT_NO_ERROR) {
        ZPATH_LOG(AL_ERROR, "Certificates retrieved do not successfully verify: %s, cert=\n%s\n", str, cert[0]);
        if (strlen(cert_failure_remediation_str)) {
            ZPATH_LOG(AL_ERROR, "%s", cert_failure_remediation_str);
        }
        return ZPATH_RESULT_ERR;
    }
    ZPATH_LOG(AL_DEBUG, "Certificates retrieved and verified");

    /* Write Root Cert */
    fp = fopen(FILENAME_ROOT_NEW, "w");
    if (!fp) {
        ZPATH_LOG(AL_ERROR, "Cannot open root cert file for writing");
        return ZPATH_RESULT_ERR;
    }
    len = strlen(cert[cert_count - 1]);
    if (fwrite(cert[cert_count - 1], len, 1, fp) != 1) {
        ZPATH_LOG(AL_ERROR, "Cannot newline terminate root cert");
        fclose(fp);
        return ZPATH_RESULT_ERR;
    }
    if (cert[cert_count - 1][len - 1] != '\n') {
        if (fwrite("\n", 1, 1, fp) != 1) {
            ZPATH_LOG(AL_ERROR, "Cannot write root cert");
            fclose(fp);
            return ZPATH_RESULT_ERR;
        }
    }
    fclose(fp);

    /* Write Cert */
    fp = fopen(FILENAME_CERT_NEW, "w");
    if (!fp) {
        ZPATH_LOG(AL_ERROR, "Cannot open cert file for writing");
        return ZPATH_RESULT_ERR;
    }
    len = strlen(cert[0]);
    if (fwrite(cert[0], len, 1, fp) != 1) {
        ZPATH_LOG(AL_ERROR, "Cannot write cert");
        fclose(fp);
        return ZPATH_RESULT_ERR;
    }
    if (cert[0][len - 1] != '\n') {
        if (fwrite("\n", 1, 1, fp) != 1) {
            ZPATH_LOG(AL_ERROR, "Cannot write newline to cert");
            fclose(fp);
            return ZPATH_RESULT_ERR;
        }
    }

    /* Write Chain to end of cert file. */
    if (cert_count >= 3) {
        *have_intermediates = 1;
        for (i = 1; i < cert_count - 1; i++) {
            len = strlen(cert[i]);
            if (fwrite(cert[i], len, 1, fp) != 1) {
                ZPATH_LOG(AL_ERROR, "Cannot write cert to chain");
                fclose(fp);
                return ZPATH_RESULT_ERR;
            }
            if (cert[i][len - 1] != '\n') {
                if (fwrite("\n", 1, 1, fp) != 1) {
                    ZPATH_LOG(AL_ERROR, "Cannot write newline to chain");
                    fclose(fp);
                    return ZPATH_RESULT_ERR;
                }
            }
        }
    }

    fclose(fp);

    /* Write Chain */
    if (cert_count < 3) {
        *have_intermediates = 0;
        unlink(FILENAME_CHAIN_NEW);
    } else {
        *have_intermediates = 1;
        fp = fopen(FILENAME_CHAIN_NEW, "w");
        if (!fp) {
            ZPATH_LOG(AL_ERROR, "Cannot open chain file for writing");
            return ZPATH_RESULT_ERR;
        }
        for (i = 1; i < cert_count - 1; i++) {
            len = strlen(cert[i]);
            if (fwrite(cert[i], len, 1, fp) != 1) {
                ZPATH_LOG(AL_ERROR, "Cannot write cert to chain");
                fclose(fp);
                return ZPATH_RESULT_ERR;
            }
            if (cert[i][len - 1] != '\n') {
                if (fwrite("\n", 1, 1, fp) != 1) {
                    ZPATH_LOG(AL_ERROR, "Cannot write newline to chain");
                    fclose(fp);
                    return ZPATH_RESULT_ERR;
                }
            }
        }
        fclose(fp);
    }

    for (i = 0; i < cert_count; i++) {
        if (cert[i] != NULL) {
            ZPN_FREE(cert[i]);
        }
    }

    return ZPATH_RESULT_NO_ERROR;
}


static int write_certificates_v1(struct evbuffer *body, int *have_intermediates)
{
    #define MAX_CERTS 10
    #define BUF_SIZE 60000
    char buf[BUF_SIZE];
    char norm_buf[BUF_SIZE];
    char *cert[10];
    char str[1000];
    char cert_failure_remediation_str[1000];
    int cert_count = 0;
    ssize_t len;
    char *w;
    char *e;
    char *ow;
    char *oe;
    int i;
    int res;
    FILE *fp;

    len = evbuffer_copyout(body, buf, sizeof(buf) - 1);
    if (len <= 0) return ZPATH_RESULT_ERR;

    w = buf;
    e = w + len;
    ow = norm_buf;
    oe = ow + sizeof(norm_buf) - 1;

    cert[cert_count] = ow;
    cert_count++;

    while ((w < e) && (ow < oe)) {
        if ((*w) == '"') {
            w++;
            continue;
        }
        /* Certs can come back to back without pipes, particularly
         * intermediates */
        if (((w + 3) < e) &&
            (*(w + 0) == '-') &&
            (*(w + 1) == '\\') &&
            (*(w + 2) == 'n') &&
            (*(w + 3) == '-')) {
            *ow = '-';
            ow++;
            *ow = 0;
            ow++;
            *ow = '-';
            if (cert_count >= MAX_CERTS) return ZPATH_RESULT_ERR;
            cert[cert_count] = ow;
            cert_count++;
            w += 4;
            ow++;
            continue;
        }
        if (((*w) == '\\') && ((w + 1) < e) && ((*(w + 1)) == 'n')) {
            *ow = '\n';
            ow++;
            w+=2;
            continue;
        }
        if ((*w) == '|') {
            if ((w + 1) >= e) return ZPATH_RESULT_ERR;
            *ow = 0;
            ow++;
            w++;
            if (cert_count >= MAX_CERTS) return ZPATH_RESULT_ERR;
            cert[cert_count] = ow;
            cert_count++;
            continue;
        }
        *ow = *w;
        ow++;
        w++;
    }
    ZPATH_LOG(AL_DEBUG, "Found %d certs", cert_count);
    for (i = 0; i < cert_count; i++) {
        ZPATH_LOG(AL_DEBUG, "Cert %d = \n%s", i, cert[i]);
    }

    if (cert_count < 2) {
        ZPATH_LOG(AL_ERROR, "Did not receive enough certificates from API");
        return ZPATH_RESULT_ERR;
    }

    /* First cert is our cert. */
    /* All certs in-between are intermediates. */
    /* Last cert is root. */

    /* Must verify whole chain. */
    res = zcrypt_verify_chain(&(cert[cert_count - 1]), 1,  // Roots
                              NULL, 0,                     // CRLs
                              &(cert[1]), cert_count - 2,  // Intermediates
                              cert[0],
                              zcrypt_cert_purpose_any,
                              str, sizeof(str), cert_failure_remediation_str, sizeof(cert_failure_remediation_str));    // Ours
    if (res != ZCRYPT_RESULT_NO_ERROR) {
        ZPATH_LOG(AL_ERROR, "Certificates retrieved do not successfully verify: %s, cert=\n%s\n", str, cert[0]);
        if (strlen(cert_failure_remediation_str)) {
            ZPATH_LOG(AL_ERROR, "%s", cert_failure_remediation_str);
        }
        return ZPATH_RESULT_ERR;
    }
    ZPATH_LOG(AL_DEBUG, "Certificates retrieved and verified");

    /* Write Root Cert */
    fp = fopen(FILENAME_ROOT_NEW, "w");
    if (!fp) {
        ZPATH_LOG(AL_ERROR, "Cannot open root cert file for writing");
        return ZPATH_RESULT_ERR;
    }
    len = strlen(cert[cert_count - 1]);
    if (fwrite(cert[cert_count - 1], len, 1, fp) != 1) {
        ZPATH_LOG(AL_ERROR, "Cannot newline terminate root cert");
        fclose(fp);
        return ZPATH_RESULT_ERR;
    }
    if (cert[cert_count - 1][len - 1] != '\n') {
        if (fwrite("\n", 1, 1, fp) != 1) {
            ZPATH_LOG(AL_ERROR, "Cannot write root cert");
            fclose(fp);
            return ZPATH_RESULT_ERR;
        }
    }
    fclose(fp);

    /* Write Cert */
    fp = fopen(FILENAME_CERT_NEW, "w");
    if (!fp) {
        ZPATH_LOG(AL_ERROR, "Cannot open cert file for writing");
        return ZPATH_RESULT_ERR;
    }
    len = strlen(cert[0]);
    if (fwrite(cert[0], len, 1, fp) != 1) {
        ZPATH_LOG(AL_ERROR, "Cannot write cert");
        fclose(fp);
        return ZPATH_RESULT_ERR;
    }
    if (cert[0][len - 1] != '\n') {
        if (fwrite("\n", 1, 1, fp) != 1) {
            ZPATH_LOG(AL_ERROR, "Cannot write newline to cert");
            fclose(fp);
            return ZPATH_RESULT_ERR;
        }
    }

    /* Write Chain to end of cert file. */
    if (cert_count >= 3) {
        *have_intermediates = 1;
        for (i = 1; i < cert_count - 1; i++) {
            len = strlen(cert[i]);
            if (fwrite(cert[i], len, 1, fp) != 1) {
                ZPATH_LOG(AL_ERROR, "Cannot write cert to chain");
                fclose(fp);
                return ZPATH_RESULT_ERR;
            }
            if (cert[i][len - 1] != '\n') {
                if (fwrite("\n", 1, 1, fp) != 1) {
                    ZPATH_LOG(AL_ERROR, "Cannot write newline to chain");
                    fclose(fp);
                    return ZPATH_RESULT_ERR;
                }
            }
        }
    }

    fclose(fp);

    /* Write Chain */
    if (cert_count < 3) {
        *have_intermediates = 0;
        unlink(FILENAME_CHAIN_NEW);
    } else {
        *have_intermediates = 1;
        fp = fopen(FILENAME_CHAIN_NEW, "w");
        if (!fp) {
            ZPATH_LOG(AL_ERROR, "Cannot open chain file for writing");
            return ZPATH_RESULT_ERR;
        }
        for (i = 1; i < cert_count - 1; i++) {
            len = strlen(cert[i]);
            if (fwrite(cert[i], len, 1, fp) != 1) {
                ZPATH_LOG(AL_ERROR, "Cannot write cert to chain");
                fclose(fp);
                return ZPATH_RESULT_ERR;
            }
            if (cert[i][len - 1] != '\n') {
                if (fwrite("\n", 1, 1, fp) != 1) {
                    ZPATH_LOG(AL_ERROR, "Cannot write newline to chain");
                    fclose(fp);
                    return ZPATH_RESULT_ERR;
                }
            }
        }
        fclose(fp);
    }

    return ZPATH_RESULT_NO_ERROR;
}

/*
 * Sign response
 */
static int sign_response(struct evbuffer *body, char *signature, size_t *signature_length)
{
    uint8_t signme[1000];
    uint8_t signstr[1000];
    uint8_t sha[SHA256_DIGEST_LENGTH];
    size_t len = evbuffer_get_length(body);
    size_t out_len;
    EVP_MD_CTX *ctx;
    int res;

    if (!len) {
        ZPATH_LOG(AL_ERROR, "Expected data for HTTP type 200");
        return ZPATH_RESULT_ERR;
    }
    if (len >= (sizeof(signme) - 1)) {
        ZPATH_LOG(AL_ERROR, "Too large data received from login");
        return ZPATH_RESULT_ERR;
    }

    evbuffer_remove(body, signme, len);

    if (!(ctx = EVP_MD_CTX_create())) {
        ZPATH_LOG(AL_ERROR, "Could not SHA create");
        return ZCRYPT_RESULT_ERR;
    }

    if (1 != EVP_DigestInit_ex(ctx, EVP_sha256(), NULL)) {
        EVP_MD_CTX_destroy(ctx);
        ZPATH_LOG(AL_ERROR, "Could not SHA init");
        return ZCRYPT_RESULT_ERR;
    }

    if (1 != EVP_DigestUpdate(ctx, signme, len)) {
        EVP_MD_CTX_destroy(ctx);
        ZPATH_LOG(AL_ERROR, "Could not SHA update");
        return ZPATH_RESULT_ERR;
    }
    unsigned int shalen = sizeof(sha);
    if (1 != EVP_DigestFinal_ex(ctx, sha, &shalen)) {
        ZPATH_LOG(AL_ERROR, "Could not SHA final");
        EVP_MD_CTX_destroy(ctx);
        return ZPATH_RESULT_ERR;
    }
    EVP_MD_CTX_destroy(ctx);

    if (shalen != sizeof(sha)) {
        ZPATH_LOG(AL_ERROR, "Bad SHA length");
        return ZPATH_RESULT_ERR;
    }

    len = sizeof(signstr);
    res = zcrypt_sign(sha,
                      shalen,
                      signstr,
                      &len,
                      s_enroll_state.cfg_rsa_key);
    if (res != ZCRYPT_RESULT_NO_ERROR) {
        ZPATH_LOG(AL_ERROR, "Could not sign");
        return ZPATH_RESULT_ERR;
    }

    out_len = base64_encoded_size(len);
    if (out_len >= (*signature_length) - 1) {
        ZPATH_LOG(AL_ERROR, "Signature too long");
        return ZPATH_RESULT_ERR;
    }

    base64_encode_binary(signature, signstr, len);
    *signature_length = out_len;

    return ZPATH_RESULT_NO_ERROR;
}

int extract_enrollment_details_from_response(struct evbuffer *body,
                                             char **root_cert,
                                             time_t *root_cert_time,
                                             char *api_hostname,
                                             size_t api_hostname_len,
                                             int64_t *customer_gid,
                                             char *api_version,
                                             size_t api_version_len,
                                             char *offline_domain,
                                             size_t offline_domain_len,
                                             uint64_t *reenroll_period)
{
    struct zcrypt_cert *cert;
    const char* enrollment_hostname = NULL;
    int64_t customer_id = 0;
    const char *customer_id_str = NULL;
    const char *enroll_api_version = NULL;
    const char *enroll_offline_domain = NULL;
    uint64_t enroll_reenroll_period;
    ssize_t len;
    int free_jv_obj = 1;


    size_t buflen = evbuffer_get_length(body);
    char *buf = (char *)ZPN_MALLOC(buflen + 1);

    len = evbuffer_copyout(body, buf, buflen);
    buf[len] = '\0';
    if (len <= 0) {
        ZPATH_LOG(AL_ERROR, "Could not get enrollment details from api response: len = %ld", len);
        ZPN_FREE(buf);
        return ZPATH_RESULT_ERR;
    }

    JSON_Value* jv = json_parse_string(&buf[0]);
    if (json_value_get_type(jv) != JSONObject) {
        ZPATH_LOG(AL_ERROR, "Failed to validate JSON in api response for get enrollment details");
        if (jv) {
            json_value_free(jv);
        }
        ZPN_FREE(buf);
        return ZPATH_RESULT_ERR;
    }

    JSON_Object* jo = json_value_get_object(jv);
    if (!jo) {
        ZPATH_LOG(AL_ERROR, "Failed to validate JSON in api response for get enrollment details");
        if (jv) {
            json_value_free(jv);
        }
        ZPN_FREE(buf);
        return ZPATH_RESULT_ERR;
    }

    if (!*root_cert) {
        cloud_cert = json_object_get_string (jo, "rootCertificate");
        if (cloud_cert) {
            ZPATH_LOG(AL_DEBUG, "Cloud cert received from get enrollment details api response = %s", cloud_cert);
            cert = zcrypt_cert_create_from_pem(cloud_cert);
            if (!cert) {
                ZPATH_LOG(AL_ERROR, "Could not create cert from get enrollment details api response");
                if (jv) json_value_free(jv);
                ZPN_FREE(buf);
                return ZPATH_RESULT_ERR;
            }
            *root_cert_time = zcrypt_cert_valid_from_epoch(cert);
            *root_cert = (char *) cloud_cert;
            free_jv_obj = 0;
            zcrypt_cert_free(cert);
        } else {
            ZPATH_LOG(AL_ERROR, "rootCertificate is NULL in get enrollment details api response");
            *root_cert = NULL;
            if (jv) json_value_free(jv);
            ZPN_FREE(buf);
            return ZPATH_RESULT_ERR;
        }
    }

    enrollment_hostname = json_object_get_string (jo, "enrollmentHostname");
    if (enrollment_hostname) {
        ZPATH_LOG(AL_DEBUG, "Enrollment hostname received from get enrollment details api response = %s", enrollment_hostname);
        snprintf(api_hostname, api_hostname_len, "%s", enrollment_hostname);
    } else {
        ZPATH_LOG(AL_ERROR, "enrollmentHostname is NULL in get enrollment details api response");
        if (jv && free_jv_obj) json_value_free(jv);
        ZPN_FREE(buf);
        return ZPATH_RESULT_ERR;
    }

    enroll_api_version = json_object_get_string (jo, "apiVersion");
    if (enroll_api_version) {
        ZPATH_LOG(AL_DEBUG, "Enroll API version received from get enrollment details api response = %s", enroll_api_version);
        snprintf(api_version, api_version_len, "%s", enroll_api_version);
        if (!strcasecmp(enroll_api_version, "v3")) {
            customer_id_str = json_object_get_string (jo, "customerId");
            customer_id = customer_id_str ? strtoull(customer_id_str, NULL, 10) : 0;
            if (customer_id) {
                ZPATH_LOG(AL_DEBUG, "Customer id received from get enrollment details api response = %ld", (long) customer_id);
                *customer_gid = customer_id;
            } else {
                ZPATH_LOG(AL_ERROR, "API version received as V3 but could not get customer id from get enrollment details api response");
                if (jv && free_jv_obj) json_value_free(jv);
                ZPN_FREE(buf);
                return ZPATH_RESULT_ERR;
            }
        }
    } else {
        ZPATH_LOG(AL_DEBUG, "Could not get API version in get enrollment details api response, using V2 by-default");
    }

    enroll_offline_domain = json_object_get_string (jo, "offlineDomain");
    if (enroll_offline_domain) {
        ZPATH_LOG(AL_DEBUG, "Enroll offline domain received from get enrollment details api response = %s", enroll_offline_domain);
        if (offline_domain) {
            snprintf(offline_domain, offline_domain_len, "%s", enroll_offline_domain);
        }
    } else {
        ZPATH_LOG(AL_INFO, "Could not get offline domain from get enrollment details api response");
    }

    enroll_reenroll_period = json_object_get_number (jo, "reenrollPeriod");
    if (enroll_reenroll_period) {
        ZPATH_LOG(AL_DEBUG, "Reenroll period received from get enrollment details api response = %"PRId64,
                enroll_reenroll_period);
        if (reenroll_period) *reenroll_period = enroll_reenroll_period;
    } else {
        ZPATH_LOG(AL_INFO, "Could not get reenroll period from get enrollment details api response");
    }

    if (jv && free_jv_obj) json_value_free(jv);
    ZPN_FREE(buf);
    return ZPATH_RESULT_NO_ERROR;
}

struct evbuffer * zpn_enroll_get_enrollment_details_raw(const char* cloud_name,
                                      struct zcrypt_rsa_key *rsa_key,
                                      char *hw_fingerprint,
                                      char *provisioning_key,
                                      char *api_hostname,
                                      size_t api_hostname_len,
                                      enum zpn_enrollment_type enroll_type,
                                      enum zpn_enrollment_style enroll_style,
                                      const char *token,
                                      int64_t customer_gid)
{
    struct fohh_http_client *client = NULL;
    struct evbuffer *body = NULL;
    SSL_CTX *ssl_ctx = NULL;
    int http_status;
    char signature_b64[1000];
    char *sig_b64 = NULL;
    char epoch_str[32];
    char get_enroll_details_api[ENROLLMENT_API_BUF_SIZE];
    char err_buf[8*BUFSIZ] = "";
    int res;

    s_enroll_state.enroll_type = enroll_type;
    snprintf(s_enroll_state.cfg_key_cloud, sizeof(s_enroll_state.cfg_key_cloud), "%s", cloud_name);

    /*
     * Get SSL context
     */
    ssl_ctx = fohh_http_client_get_ssl_ctx();
    if (!ssl_ctx) {
        ZPATH_LOG(AL_ERROR, "Cannot get SSL context");
        return NULL;
    }

    /*
     * Create TLS connection to our enrollment web server to get enrollment details.
     */
    client = get_http_client("Get Enrollment Details", ssl_ctx, api_hostname);
    if (!client) {
        ZPATH_LOG(AL_ERROR, "Could not create HTTP client to get enrollment details");
        goto done;
    }

    if (enroll_style == ZPN_ENROLLMENT_STYLE_V2) {
        /* Create timestamp signature */
        size_t signature_length = sizeof(signature_b64);
        int64_t timestamp_s;
        if (get_enrollment_epoch_s(rsa_key, &timestamp_s, signature_b64, &signature_length) == ZPATH_RESULT_NO_ERROR) {
            sig_b64 = signature_b64;
            snprintf(epoch_str, sizeof(epoch_str), "%ld", (long)timestamp_s);
        } else {
            ZPN_LOG(AL_ERROR, "could not generate signature, please delete /opt/zscaler/var, "
                              "recopy the provision key and restart %s", zpn_enroll_get_enrollement_name());
            goto done;
        }

        ZPATH_LOG(AL_DEBUG, "\nGet enrollment details with nonce: <%.*s...>, fingerprint: <%.*s...>, using style: %s, timestamp_s: %s, signature: %s",
                  PROVISION_KEY_LOG_BYTES, provisioning_key,
                  HW_FINGERPRINT_LOG_BYTES, hw_fingerprint,
                  get_enrollement_style_name(ZPN_ENROLLMENT_STYLE_V2),
                  epoch_str, sig_b64);
        get_enrollment_api_endpoint(enroll_type, enroll_style, ENROLLMENT_API_GET_ENROLL_DETAILS, api_hostname, get_enroll_details_api, sizeof(get_enroll_details_api), 0);
        res = fohh_http_client_fetch_synchronous("Get Enrollment Details",
                                                 client,
                                                 FOHH_HTTP_METHOD_POST,
                                                 get_enroll_details_api,
                                                 200,          /* Expected HTTP status */
                                                 &http_status, /* Received HTTP status */
                                                 &body,
                                                 4,
                                                 "nonce", provisioning_key,
                                                 "fingerprint", hw_fingerprint,
                                                 "timestampInSec", epoch_str,
                                                 "signature", sig_b64
                                                );
        if (res) {
            ZPATH_LOG(AL_DEBUG, "Get enrollment details failed.");
            if (get_response_body_copy_dump(body, err_buf, sizeof(err_buf)) == ZPATH_RESULT_NO_ERROR) {
                ZPATH_LOG(AL_DEBUG, "Get Enrollment Details Response - nonce(<%.*s...>) fingerprint(<%.*s...>) signature(%s)\n%s",
                          PROVISION_KEY_LOG_BYTES, provisioning_key,
                          HW_FINGERPRINT_LOG_BYTES, hw_fingerprint,
                          sig_b64, err_buf);
            }
            goto done;
        } else {
            if (s_enroll_debug) {
                if (get_response_body_copy_dump(body, err_buf, sizeof(err_buf)) == ZPATH_RESULT_NO_ERROR) {
                    ZPATH_LOG(AL_DEBUG, "Get Enroll Details Response - \n%s", err_buf);
                }
            }
        }
    } else if (enroll_style == ZPN_ENROLLMENT_STYLE_V4) {
        /* Create timestamp signature */
        size_t signature_length = sizeof(signature_b64);
        int64_t timestamp_s;
        char association_type[32] = {0};

        if (get_enrollment_epoch_s(rsa_key, &timestamp_s, signature_b64, &signature_length) == ZPATH_RESULT_NO_ERROR) {
            sig_b64 = signature_b64;
            snprintf(epoch_str, sizeof(epoch_str), "%ld", (long)timestamp_s);
        } else {
            ZPN_LOG(AL_ERROR, "could not generate signature, please delete /opt/zscaler/var, "
                              "and restart %s", zpn_enroll_get_enrollement_name());
            goto done;
        }

        if (zpn_enroll_get_association_type(enroll_type, association_type, sizeof(association_type)) != ZPATH_RESULT_NO_ERROR) {
            ZPN_LOG(AL_ERROR, "[Enroll V4] Unable to ge the Nonce association type for %s",
                              zpn_enroll_get_enrollement_name());
            goto done;
        }

        ZPATH_LOG(AL_DEBUG, "\nGet enrollment details with nonce: <%.*s...>, fingerprint: <%.*s...>, using style: %s, association_type: %s, timestamp_s: %s, signature: %s",
                  PROVISION_KEY_LOG_BYTES, provisioning_key,
                  HW_FINGERPRINT_LOG_BYTES, hw_fingerprint,
                  get_enrollement_style_name(ZPN_ENROLLMENT_STYLE_V4),
                  association_type, epoch_str, sig_b64);
        get_enrollment_api_endpoint(enroll_type, enroll_style, ENROLLMENT_API_GET_ENROLL_DETAILS, api_hostname, get_enroll_details_api, sizeof(get_enroll_details_api), customer_gid);

        res = fohh_http_client_fetch_synchronous_v4("Get Enrollment Details",
                                                 client,
                                                 FOHH_HTTP_METHOD_POST,
                                                 get_enroll_details_api,
                                                 200,          /* Expected HTTP status */
                                                 &http_status, /* Received HTTP status */
                                                 &body,
                                                 token,
                                                 5,
                                                 "nonce", provisioning_key,
                                                 "fingerprint", hw_fingerprint,
                                                 "timestampInSec", epoch_str,
                                                 "signature", sig_b64,
                                                 "nonceAssociationType", association_type
                                                );
        if (res) {
            ZPATH_LOG(AL_DEBUG, "Get enrollment details failed.");
            if (get_response_body_copy_dump(body, err_buf, sizeof(err_buf)) == ZPATH_RESULT_NO_ERROR) {
                ZPATH_LOG(AL_DEBUG, "Get Enrollment Details Response - nonce(<%.*s...>) fingerprint(<%.*s...>), association_type: (%s), signature(%s)\n%s",
                          PROVISION_KEY_LOG_BYTES, provisioning_key,
                          HW_FINGERPRINT_LOG_BYTES, hw_fingerprint,
                          association_type, sig_b64, err_buf);
            }
            goto done;
        } else {
            if (s_enroll_debug) {
                if (get_response_body_copy_dump(body, err_buf, sizeof(err_buf)) == ZPATH_RESULT_NO_ERROR) {
                    ZPATH_LOG(AL_DEBUG, "Get Enroll Details Response - \n%s", err_buf);
                }
            }
        }
    } else {
        ZPATH_LOG(AL_ERROR, "Get enrollment details is supported over V2 and V4 only, using style: %d - not supported", enroll_style);
        goto done;
    }
    fohh_http_client_destroy_from_another_thread(client);
    SSL_CTX_free(ssl_ctx);
    return body;
done:
    if (client) fohh_http_client_destroy_from_another_thread(client);
    if (ssl_ctx) SSL_CTX_free(ssl_ctx);
    if (body) evbuffer_free(body);
    return NULL;
}


int zpn_enroll_get_enrollment_details(char *api_hostname,
                                      size_t api_hostname_len,
                                      struct evbuffer *body,
                                      char *cloud,
                                      char **root_cert,
                                      time_t *root_cert_time,
                                      int64_t *customer_gid,
                                      char *api_version,
                                      size_t api_version_len,
                                      char *offline_domain,
                                      size_t offline_domain_len,
                                      uint64_t *reenroll_period)
{
    int res;

    if (!body) return ZPATH_RESULT_ERR;

    res = extract_enrollment_details_from_response(body, root_cert, root_cert_time, api_hostname, api_hostname_len, customer_gid, api_version, api_version_len, offline_domain, offline_domain_len, reenroll_period);
    if (res) {
        ZPATH_LOG(AL_ERROR, "extract_enrollment_details_from_response failed");
        goto done;
    }

    if (body) evbuffer_free(body);
    return ZPATH_RESULT_NO_ERROR;

done:
    if (body) evbuffer_free(body);
    return ZPATH_RESULT_ERR;
}

/*
 * Enrollment using V3 APIs
 * Fetch CSR details: https://api.<cloud name>/zpn/api/v3/admin/customers/<customer_gid>/<component>/onboard/details
 * Send CSR and get cert: https://api.<cloud name>/zpn/api/v3/admin/customers/<customer_gid>/<component>/onboard/signCSRAndGetCertificate
 */
int zpn_enroll_v3(struct zcrypt_key *key,
                  struct zcrypt_rsa_key *old_rsa_key,
                  char *hw_fingerprint,
                  char *provisioning_key,
                  int shard,
                  char *api_hostname,
                  enum zpn_enrollment_type enroll_type,
                  enum zpn_enrollment_style enroll_style,
                  char *cloud, int is_re_enroll,
                  int64_t customer_gid)
{
    struct zcrypt_rsa_key *new_rsa_key = NULL;
    struct fohh_http_client *client = NULL;
    struct evbuffer *body = NULL;
    SSL_CTX *ssl_ctx = NULL;
    int http_status;
    char signature_b64[1000];
    char *sig_b64 = NULL;
    char epoch_str[32];
    X509_REQ *csr = NULL;
    char csr_str[10000];
    int have_intermediates;
    char sign_csr_and_get_cert[ENROLLMENT_API_BUF_SIZE];
    char *err_buf = NULL;
    int res;

    /*
     * We're attempting to enroll or re-enroll. We need a new keypair...
     */
    if (!((new_rsa_key = zcrypt_rsa_key_create()))) {
        ZPATH_LOG(AL_ERROR, "Could not create RSA key");
        return ZPATH_RESULT_ERR;
    }

    if (zpn_enroll_get_private_key(key, new_rsa_key, FILENAME_KEY_NEW) != ZPATH_RESULT_NO_ERROR) {
        ZPATH_LOG(AL_ERROR, "Could not generate RSA Keypair");
        return ZPATH_RESULT_ERR;
    }

    /*
     * Get SSL context
     */
    ssl_ctx = fohh_http_client_get_ssl_ctx();
    if (!ssl_ctx) {
        ZPATH_LOG(AL_ERROR, "Cannot get SSL context");
        return ZPATH_RESULT_ERR;
    }


    /*
     * Create TLS connection to our enrollment web server.
     */
    client = get_http_client("Enroll", ssl_ctx, api_hostname);
    if (!client) {
        ZPATH_LOG(AL_ERROR, "Could not create HTTP client");
        goto cleanup_err;
    }

    if (is_re_enroll) {
        /* Create timestamp signature */
        size_t signature_length = sizeof(signature_b64);
        int64_t re_enroll_timestamp;
        if (get_enrollment_epoch_s(old_rsa_key, &re_enroll_timestamp, signature_b64, &signature_length) == ZPATH_RESULT_NO_ERROR) {
            sig_b64 = signature_b64;
            snprintf(epoch_str, sizeof(epoch_str), "%ld", (long)re_enroll_timestamp);
            if (s_enroll_debug) {
                ZPATH_LOG(AL_INFO, "Re-enrolling nonce: <%.*s...>, with re-enroll-timestamp: %ld, signature: %s",
                          PROVISION_KEY_LOG_BYTES, provisioning_key,
                          (long)re_enroll_timestamp, sig_b64);
            }
        } else {
            /* Hard failure because this shouldn't happen unless the environment is manipulated, files deleted.. */
            ZPATH_LOG(AL_ERROR, "could not generate signature for re-enrollment, please delete /opt/zscaler/var, "
                      "recopy the provision key and restart %s", zpn_enroll_get_enrollement_name());
            goto cleanup_err;
        }
    } else {
        snprintf(epoch_str, sizeof(epoch_str), "%ld", (long)epoch_s());
    }

    ZPATH_LOG(AL_DEBUG, "\nEnroll with nonce: <%.*s...>, using style: %s",
              PROVISION_KEY_LOG_BYTES, provisioning_key,
              get_enrollement_style_name(enroll_style));

    /*
     * Get CSR, saved or otherwise. (In both forms)
     */
    csr = sarge_get_csr(client, cloud, api_hostname, new_rsa_key, hw_fingerprint, provisioning_key, csr_str, sizeof(csr_str),
                        epoch_str, sig_b64, enroll_type, enroll_style, customer_gid, NULL);
    if (!csr) {
        goto cleanup_err;
    }

    err_buf = ZPN_MALLOC(8*BUFSIZ);
    get_enrollment_api_endpoint(enroll_type,
                                enroll_style,
                                ENROLLMENT_API_SEND_CSR_AND_GET_CERT,
                                api_hostname,
                                sign_csr_and_get_cert,
                                sizeof(sign_csr_and_get_cert),
                                customer_gid);

    res = fohh_http_client_fetch_synchronous("Send CSR and get cert",
                                             client,
                                             FOHH_HTTP_METHOD_POST,
                                             sign_csr_and_get_cert,
                                             200,          /* Expected HTTP status */
                                             &http_status, /* Received HTTP status */
                                             &body,
                                             sig_b64 ? 5 : 4,
                                             "nonce", provisioning_key,
                                             "fingerprint", hw_fingerprint,
                                             "csr", csr_str,
                                             "timestampInSec", epoch_str,
                                             "signature", sig_b64);
    if (res) {
        ZPATH_LOG(AL_ERROR, "Send CSR and get cert failed.");
        if (get_response_body_copy_dump(body, err_buf, 8*BUFSIZ) == ZPATH_RESULT_NO_ERROR) {
            ZPATH_LOG(AL_ERROR, "Send CSR and get cert response - nonce(<%.*s...>) fingerprint(<%.*s...>)\n%s",
                      PROVISION_KEY_LOG_BYTES, provisioning_key,
                      HW_FINGERPRINT_LOG_BYTES, hw_fingerprint,
                      err_buf);
        }
        goto cleanup_err;
    } else {
        if (s_enroll_debug) {
            if (get_response_body_copy_dump(body, err_buf, 8*BUFSIZ) == ZPATH_RESULT_NO_ERROR) {
                ZPATH_LOG(AL_DEBUG, "Send CSR and get cert response - \n%s", err_buf);
            }
        }
    }

    /*
     * Send CSR and get cert response is in JSON form. Extract certs from JSON.
     * Certificates are returned as a stream of concatnated pems
     * First cert is our cert.
     * All certs in-between are intermediates.
     * Last cert is root.
     */
    res = write_certificates(body, &have_intermediates, enroll_style);
    if (res) {
        goto cleanup_err;
    }

    /* If any of these renamings fail, we can get into an
     * unrecoverable state. i.e. we should really be renaming a
     * directory. */
    if (have_intermediates) {
        rename(FILENAME_CHAIN_NEW, FILENAME_CHAIN);
    } else {
        unlink(FILENAME_CHAIN);
    }
    rename(FILENAME_KEY_NEW_PRIV, FILENAME_KEY_PRIV);
    rename(FILENAME_KEY_NEW_PUB, FILENAME_KEY_PUB);
    rename(FILENAME_CERT_NEW, FILENAME_CERT);
    rename(FILENAME_ROOT_NEW, FILENAME_ROOT);
    unlink(FILENAME_CSR_NEW);
    if (s_enroll_state.cfg_rsa_key) {
        zcrypt_rsa_key_destroy(s_enroll_state.cfg_rsa_key);
    }
    s_enroll_state.cfg_rsa_key = new_rsa_key;

    fohh_http_client_destroy_from_another_thread(client);
    SSL_CTX_free(ssl_ctx);
    if (csr) X509_REQ_free(csr);
    if (err_buf) ZPN_FREE(err_buf);
    if (body) evbuffer_free(body);
    return ZPATH_RESULT_NO_ERROR;

cleanup_err:
    if (new_rsa_key) zcrypt_rsa_key_destroy(new_rsa_key);
    if (client) fohh_http_client_destroy_from_another_thread(client);
    if (ssl_ctx) SSL_CTX_free(ssl_ctx);
    if (err_buf) ZPN_FREE(err_buf);
    if (body) evbuffer_free(body);
    if (csr) X509_REQ_free(csr);
    return ZPATH_RESULT_ERR;
}

int zpn_enroll_is_cert_generation_limit_exceeded(struct evbuffer *body)
{
    char *buf;
    const char *error_id = NULL;
    ssize_t len;
    JSON_Value* jv = NULL;
    int cert_generation_limit_exceeded = 0;

    buf = ZPN_MALLOC(BUF_SIZE);
    len = evbuffer_copyout(body, buf, BUF_SIZE - 1);
    buf[len] = '\0';
    if (len <= 0) {
        ZPATH_LOG(AL_ERROR, "Could not extract data from Send CSR response: len = %ld", len);
        goto done;
    }

    jv = json_parse_string(buf);
    if (json_value_get_type(jv) != JSONObject) {
        ZPATH_LOG(AL_ERROR, "Failed to validate JSON in Send CSR response");
        goto done;
    }

    JSON_Object* jo = json_value_get_object(jv);
    if (!jo) {
        ZPATH_LOG(AL_ERROR, "Failed to validate JSON in Send CSR response");
        goto done;
    }

    error_id = json_object_get_string(jo, "id");
    if (error_id) {
        if (strcmp(error_id, "issued.cert.generation.limit.exceeded") == 0) {
            cert_generation_limit_exceeded = 1;
        }
    } else {
        ZPATH_LOG(AL_ERROR, "Error id is NULL in Send CSR response");
    }

done:
    if (jv) json_value_free(jv);
    ZPN_FREE(buf);
    return cert_generation_limit_exceeded;
}


/*
 * (Re)-Enroll Process V2:
 *
 * Have old key pair (optional)
 *
 * Get new keypair/write them.
 *
 * Login (only for style == v1)
 *
 * Get CSR/Write it.
 *
 * Post CSR
 *
 * Get Cert/Write it.
 *
 * Rename Keypair.
 * Rename Cert.
 * Remove CSR
 */
int zpn_enroll(struct zcrypt_key *key, struct zcrypt_rsa_key *old_rsa_key, char *hw_fingerprint, char *provisioning_key, int shard, char *api_hostname, enum zpn_enrollment_type enroll_type,
                  enum zpn_enrollment_style enroll_style, char *cloud, int is_re_enroll)
{
    struct zcrypt_rsa_key *new_rsa_key = NULL;
    int res = ZPN_RESULT_NO_ERROR;

    struct fohh_http_client *client = NULL;
    struct evbuffer *body = NULL;
    SSL_CTX *ssl_ctx = NULL;
    int http_status;
    char signature_b64[1000];
    char *sig_b64 = NULL;
    size_t len;
    char epoch_str[32];

    X509_REQ *csr;
    char csr_str[10000];

    int have_intermediates;
    char signin_api[ENROLLMENT_API_BUF_SIZE];
    char send_csr_api[ENROLLMENT_API_BUF_SIZE];
    char get_cert_api[ENROLLMENT_API_BUF_SIZE];
    char err_buf[8*BUFSIZ] = "";

    /*
     * We're attempting to enroll or re-enroll. We need a new keypair...
     */
    if (!((new_rsa_key = zcrypt_rsa_key_create()))) {
        ZPATH_LOG(AL_ERROR, "Could not create RSA key");
        return ZPATH_RESULT_ERR;
    }

    if (zpn_enroll_get_private_key(key, new_rsa_key, FILENAME_KEY_NEW) != ZPATH_RESULT_NO_ERROR) {
        ZPATH_LOG(AL_ERROR, "Could not generate RSA Keypair");
        return ZPATH_RESULT_ERR;
    }

    /*
     * Get SSL context
     */
    ssl_ctx = fohh_http_client_get_ssl_ctx();
    if (!ssl_ctx) {
        ZPATH_LOG(AL_ERROR, "Cannot get SSL context");
        return ZPATH_RESULT_ERR;
    }


    /*
     * Create TLS connection to our enrollment web server.
     */
    csr = NULL;
    client = get_http_client("Enroll", ssl_ctx, api_hostname);
    if (!client) {
        ZPATH_LOG(AL_ERROR, "Could not create HTTP client");
        goto cleanup_err;
    }

    /* Login is only needed for V1 */
    if (enroll_style == ZPN_ENROLLMENT_STYLE_V1) {
        ZPATH_LOG(AL_DEBUG, "Enroll with nonce: <%.*s...>, using style: %s",
                  PROVISION_KEY_LOG_BYTES, provisioning_key,
                  get_enrollement_style_name(ZPN_ENROLLMENT_STYLE_V1));

        /*
         * Login.
         */
        get_enrollment_api_endpoint(enroll_type, enroll_style, ENROLLMENT_API_SIGN_IN, api_hostname, signin_api, sizeof(signin_api), 0);
        ZPATH_LOG(AL_DEBUG, "Enrollment Login with nonce = <%.*s...> fingerprint = <%.*s...>",
                  PROVISION_KEY_LOG_BYTES, provisioning_key,
                  HW_FINGERPRINT_LOG_BYTES, hw_fingerprint);
        res = fohh_http_client_fetch_synchronous("Enrollment Login",
                                                 client,
                                                 FOHH_HTTP_METHOD_POST,
                                                 signin_api,
                                                 0,            /* Expected HTTP status */
                                                 &http_status, /* Received HTTP status */
                                                 &body,
                                                 2,
                                                 "nonce", provisioning_key,
                                                 "fingerprint", hw_fingerprint);
        if (res || ((http_status != 200) && (http_status != 204))) {
            ZPATH_LOG(AL_ERROR, "Login request failed - http status(%d) nonce(<%.*s...>) fingerprint(<%.*s...>)",
                      http_status,
                      PROVISION_KEY_LOG_BYTES, provisioning_key,
                      HW_FINGERPRINT_LOG_BYTES, hw_fingerprint);
            goto cleanup_err;
        }

        ZPATH_LOG(AL_DEBUG, "Login to enrollment site complete");
        /*
         * Check if we have any return data we will need to sign. (re-enroll case)
         */
        if (http_status == 200) {
            /* Status 200 == Have some data == MAC that needs to be signed */
            len = sizeof(signature_b64);
            if (sign_response(body, signature_b64, &len)) {
                /*
                 * mgtAPI sends a nonce and wants us to sign and send it back to it as a way to authenticating us.
                 * If this fails the crypto is not working and its a bad system, change OS or hardware of whatever
                 * and get the basics UP.
                 */
                ZPATH_LOG(AL_ERROR, "Could not sign Message Authentication Code - nonce(<%.*s...>) fingerprint(<%.*s...>)",
                          PROVISION_KEY_LOG_BYTES, provisioning_key,
                          HW_FINGERPRINT_LOG_BYTES, hw_fingerprint);
                goto cleanup_err;
            }
            sig_b64 = signature_b64;
            ZPATH_LOG(AL_NOTICE, "This provisioning key & fingerprint combo has already been enrolled. Attempting to re-enroll");
        } else {
            ZPN_LOG(AL_DEBUG, "Initial registration");
        }

        if (body) evbuffer_free(body);
        body = NULL;
    } else if (enroll_style == ZPN_ENROLLMENT_STYLE_V2) {
        if (is_re_enroll) {
            /* Create timestamp signature */
            size_t signature_length = sizeof(signature_b64);
            int64_t re_enroll_timestamp;
            if (get_enrollment_epoch_s(old_rsa_key, &re_enroll_timestamp, signature_b64, &signature_length) == ZPATH_RESULT_NO_ERROR) {
                sig_b64 = signature_b64;
                snprintf(epoch_str, sizeof(epoch_str), "%ld", (long)re_enroll_timestamp);
                if (s_enroll_debug) {
                    ZPATH_LOG(AL_INFO, "Re-enrolling nonce: <%.*s...>, with re-enroll-timestamp: %ld, signature: %s",
                              PROVISION_KEY_LOG_BYTES, provisioning_key,
                              (long)re_enroll_timestamp, sig_b64);
                }
            } else {
                /* Hard failure because this shouldn't happen unless the environment is manipulated, files deleted.. */
                ZPN_LOG(AL_ERROR, "could not generate signature for re-enrollment, please delete /opt/zscaler/var, "
                                  "recopy the provision key and restart %s", zpn_enroll_get_enrollement_name());
                goto cleanup_err;
            }
        } else {
            snprintf(epoch_str, sizeof(epoch_str), "%ld", (long)epoch_s());
        }

        ZPATH_LOG(AL_DEBUG, "\nEnroll with nonce: <%.*s...>, using style: %s",
                  PROVISION_KEY_LOG_BYTES, provisioning_key,
                  get_enrollement_style_name(enroll_style));
    } else {
        ZPATH_LOG(AL_DEBUG, "\nEnroll with nonce: <%.*s...>, using style: %d - not supported",
                  PROVISION_KEY_LOG_BYTES, provisioning_key, enroll_style);
        goto cleanup_err;
    }


    /*
     * Get CSR, saved or otherwise. (In both forms)
     */
    csr = sarge_get_csr(client, cloud, api_hostname, new_rsa_key, hw_fingerprint, provisioning_key, csr_str, sizeof(csr_str),
                        epoch_str, sig_b64, enroll_type, enroll_style, 0, NULL);
    if (!csr) {
        goto cleanup_err;
    }


    /*
     * POST CSR, if necessary. Success is http-204 (i.e NO_CONTENT)
     */
    get_enrollment_api_endpoint(enroll_type, enroll_style, ENROLLMENT_API_SEND_CSR, api_hostname, send_csr_api, sizeof(send_csr_api), 0);
    if (enroll_style == ZPN_ENROLLMENT_STYLE_V1) {
        res = fohh_http_client_fetch_synchronous("Send CSR",
                                                 client,
                                                 FOHH_HTTP_METHOD_POST,
                                                 send_csr_api,
                                                 204,          /* Expected HTTP status */
                                                 &http_status, /* Received HTTP status */
                                                 &body,
                                                 sig_b64 ? 4 : 3,
                                                 "csr", csr_str,
                                                 "nonce", provisioning_key,
                                                 "fingerprint", hw_fingerprint,
                                                 "signature", sig_b64);
    } else if (enroll_style == ZPN_ENROLLMENT_STYLE_V2) {
        res = fohh_http_client_fetch_synchronous("Send CSR",
                                                 client,
                                                 FOHH_HTTP_METHOD_POST,
                                                 send_csr_api,
                                                 204,          /* Expected HTTP status */
                                                 &http_status, /* Received HTTP status */
                                                 &body,
                                                 sig_b64 ? 5 : 4,
                                                 "csr", csr_str,
                                                 "nonce", provisioning_key,
                                                 "fingerprint", hw_fingerprint,
                                                 "timestampInSec", epoch_str,
                                                 "signature", sig_b64);

    }
    if (res) {
        if (get_response_body_copy_dump(body, err_buf, sizeof(err_buf)) == ZPATH_RESULT_NO_ERROR) {
            ZPATH_LOG(AL_ERROR, "Fetch CSR Response - nonce(<%.*s...>) fingerprint(<%.*s...>)\n%s",
                      PROVISION_KEY_LOG_BYTES, provisioning_key,
                      HW_FINGERPRINT_LOG_BYTES, hw_fingerprint,
                      err_buf);
        }
        if (zpn_enroll_is_cert_generation_limit_exceeded(body)) {
            /* Do not make get cert call if cert generation limit is exceeded, stop here. */
            ZPATH_LOG(AL_ERROR, "Send CSR failed, cert generation limit exceeded");
            goto cleanup_err;
        }
        ZPATH_LOG(AL_ERROR, "Send CSR failed, but that may be because we posted it in the past, so it is ok.");
    } else {
        if (s_enroll_debug) {
            if (get_response_body_copy_dump(body, err_buf, sizeof(err_buf)) == ZPATH_RESULT_NO_ERROR) {
                ZPATH_LOG(AL_DEBUG, "Send CSR Response - \n%s", err_buf);
            }
        }
    }
    if (body) evbuffer_free(body);
    body = NULL;

    /*
     * GET Certificate
     */
    get_enrollment_api_endpoint(enroll_type, enroll_style, ENROLLMENT_API_GET_CERTIFICATE, api_hostname, get_cert_api, sizeof(get_cert_api), 0);
    if (enroll_style == ZPN_ENROLLMENT_STYLE_V1) {
        res = fohh_http_client_fetch_synchronous("Get Certificate",
                                                 client,
                                                 FOHH_HTTP_METHOD_POST,
                                                 get_cert_api,
                                                 200,          /* Expected HTTP status */
                                                 &http_status, /* Received HTTP status */
                                                 &body,
                                                 3,
                                                 "nonce", provisioning_key,
                                                 "fingerprint", hw_fingerprint,
                                                 "csr", csr_str
                                                 );
    } else if (enroll_style == ZPN_ENROLLMENT_STYLE_V2) {
        res = fohh_http_client_fetch_synchronous("Get Certificate",
                                                 client,
                                                 FOHH_HTTP_METHOD_POST,
                                                 get_cert_api,
                                                 200,          /* Expected HTTP status */
                                                 &http_status, /* Received HTTP status */
                                                 &body,
                                                 4,
                                                 "nonce", provisioning_key,
                                                 "fingerprint", hw_fingerprint,
                                                 "csr", csr_str,
                                                 "timestampInSec", epoch_str
                                                );
    }
    if (res) {
        ZPATH_LOG(AL_ERROR, "Get Certificate failed.");
        if (get_response_body_copy_dump(body, err_buf, sizeof(err_buf)) == ZPATH_RESULT_NO_ERROR) {
            ZPATH_LOG(AL_ERROR, "Fetch CSR Response - nonce(<%.*s...>) fingerprint(<%.*s...>)\n%s",
                      PROVISION_KEY_LOG_BYTES, provisioning_key,
                      HW_FINGERPRINT_LOG_BYTES, hw_fingerprint,
                      err_buf);
        }
        goto cleanup_err;
    } else {
        if (s_enroll_debug) {
            if (get_response_body_copy_dump(body, err_buf, sizeof(err_buf)) == ZPATH_RESULT_NO_ERROR) {
                ZPATH_LOG(AL_DEBUG, "Get Certificate Response - \n%s", err_buf);
            }
        }
    }

    if (enroll_style == ZPN_ENROLLMENT_STYLE_V2) {
        /* Certificates are returned as a stream of concatnated pems */
        /* First cert is our cert. */
        /* All certs in-between are intermediates. */
        /* Last cert is root. */

        res = write_certificates(body, &have_intermediates, enroll_style);
    } else {

        /* The format of this result is silly- it's plain text, but json
         * escaped, and has pipes in it. And the pipes are delimiters. */
        res = write_certificates_v1(body, &have_intermediates);
    }
    if (res) {
        goto cleanup_err;
    }

    /* If any of these renamings fail, we can get into an
     * unrecoverable state. i.e. we should really be renaming a
     * directory. */
    if (have_intermediates) {
        rename(FILENAME_CHAIN_NEW, FILENAME_CHAIN);
    } else {
        unlink(FILENAME_CHAIN);
    }
    rename(FILENAME_KEY_NEW_PRIV, FILENAME_KEY_PRIV);
    rename(FILENAME_KEY_NEW_PUB, FILENAME_KEY_PUB);
    rename(FILENAME_CERT_NEW, FILENAME_CERT);
    rename(FILENAME_ROOT_NEW, FILENAME_ROOT);
    unlink(FILENAME_CSR_NEW);
    if (s_enroll_state.cfg_rsa_key) {
        zcrypt_rsa_key_destroy(s_enroll_state.cfg_rsa_key);
    }
    s_enroll_state.cfg_rsa_key = new_rsa_key;


    fohh_http_client_destroy_from_another_thread(client);
    SSL_CTX_free(ssl_ctx);
    if (csr) X509_REQ_free(csr);
    if (body) evbuffer_free(body);
    return ZPATH_RESULT_NO_ERROR;

cleanup_err:
    if (new_rsa_key) zcrypt_rsa_key_destroy(new_rsa_key);
    if (client) fohh_http_client_destroy_from_another_thread(client);
    if (ssl_ctx) SSL_CTX_free(ssl_ctx);
    if (body) evbuffer_free(body);
    if (csr) X509_REQ_free(csr);
    return ZPATH_RESULT_ERR;
}

int zpn_enroll_v4(struct zcrypt_key *key, struct zcrypt_rsa_key *old_rsa_key, char *hw_fingerprint, char *provisioning_key,
                  int shard, char *api_hostname, enum zpn_enrollment_type enroll_type,
                  enum zpn_enrollment_style enroll_style, char *cloud, int is_re_enroll, const char* token, int64_t customer_gid)
{
    struct zcrypt_rsa_key *new_rsa_key = NULL;
    int res = ZPN_RESULT_NO_ERROR;

    struct fohh_http_client *client = NULL;
    struct evbuffer *body = NULL;
    SSL_CTX *ssl_ctx = NULL;
    int http_status;
    char signature_b64[1000];
    char *sig_b64 = NULL;
    char epoch_str[32];

    X509_REQ *csr;
    char csr_str[10000];

    int have_intermediates;
    char send_csr_api[ENROLLMENT_API_BUF_SIZE];
    char get_cert_api[ENROLLMENT_API_BUF_SIZE];
    char err_buf[4*BUFSIZ] = "";

    char association_type[32] = {0};

    /*
     * We're attempting to enroll or re-enroll. We need a new keypair...
     */
    if (!((new_rsa_key = zcrypt_rsa_key_create()))) {
        ZPATH_LOG(AL_ERROR, "Could not create RSA key");
        return ZPATH_RESULT_ERR;
    }

    if (zpn_enroll_get_private_key(key, new_rsa_key, FILENAME_KEY_NEW) != ZPATH_RESULT_NO_ERROR) {
        ZPATH_LOG(AL_ERROR, "Could not generate RSA Keypair");
        return ZPATH_RESULT_ERR;
    }

    /*
     * Get SSL context
     */
    ssl_ctx = fohh_http_client_get_ssl_ctx();
    if (!ssl_ctx) {
        ZPATH_LOG(AL_ERROR, "Cannot get SSL context");
        return ZPATH_RESULT_ERR;
    }


    /*
     * Create TLS connection to our enrollment web server.
     */
    csr = NULL;
    client = get_http_client("Enroll", ssl_ctx, api_hostname);
    if (!client) {
        ZPATH_LOG(AL_ERROR, "Could not create HTTP client");
        goto cleanup_err;
    }

    if (is_re_enroll) {
        /* Create timestamp signature */
        size_t signature_length = sizeof(signature_b64);
        int64_t re_enroll_timestamp;
        if (get_enrollment_epoch_s(old_rsa_key, &re_enroll_timestamp, signature_b64, &signature_length) == ZPATH_RESULT_NO_ERROR) {
            sig_b64 = signature_b64;
            snprintf(epoch_str, sizeof(epoch_str), "%ld", (long)re_enroll_timestamp);
            if (s_enroll_debug) {
                ZPATH_LOG(AL_INFO, "Re-enrolling nonce: <%.*s...>, with re-enroll-timestamp: %ld, signature: %s",
                                    PROVISION_KEY_LOG_BYTES, provisioning_key,
                                    (long)re_enroll_timestamp, sig_b64);
            }
        } else {
            /* Hard failure because this shouldn't happen unless the environment is manipulated, files deleted.. */
            ZPN_LOG(AL_ERROR, "could not generate signature for re-enrollment, please delete /opt/zscaler/var, "
                                "recopy the provision key and restart %s", zpn_enroll_get_enrollement_name());
            goto cleanup_err;
        }
    } else {
        snprintf(epoch_str, sizeof(epoch_str), "%ld", (long)epoch_s());
    }

    ZPATH_LOG(AL_DEBUG, "\nEnroll with nonce: <%.*s...>, using style: %s",
              PROVISION_KEY_LOG_BYTES, provisioning_key,
              get_enrollement_style_name(enroll_style));


    /*
     * Get CSR, saved or otherwise. (In both forms)
     */
    csr = sarge_get_csr(client, cloud, api_hostname, new_rsa_key, hw_fingerprint, provisioning_key, csr_str, sizeof(csr_str),
                        epoch_str, sig_b64, enroll_type, enroll_style, customer_gid, token);
    if (!csr) {
        ZPATH_LOG(AL_ERROR, "OAuth enrollment - Failed to get CSR details from enrollment service");
        goto cleanup_err;
    }


    if (zpn_enroll_get_association_type(enroll_type, association_type, sizeof(association_type)) != ZPATH_RESULT_NO_ERROR) {
        ZPN_LOG(AL_ERROR, "[Enroll V4] Unable to ge the Nonce association type for %s",
                          zpn_enroll_get_enrollement_name());
        goto cleanup_err;
    }

    /*
     * POST CSR, if necessary. Success is http-204 (i.e NO_CONTENT)
     */
    get_enrollment_api_endpoint(enroll_type, enroll_style, ENROLLMENT_API_SEND_CSR, api_hostname, send_csr_api, sizeof(send_csr_api), customer_gid);
    ZPATH_LOG(AL_DEBUG, "Calling Send CSR API with - nonce(<%s>) fingerprint(<%s>) csr_str(<%s>) timestampInSec(<%s>) sig_b64(%s) token(<%s>)",
              provisioning_key, hw_fingerprint, csr_str, epoch_str, sig_b64, token);

    res = fohh_http_client_fetch_synchronous_v4("Send CSR",
                                                client,
                                                FOHH_HTTP_METHOD_POST,
                                                send_csr_api,
                                                204,          /* Expected HTTP status */
                                                &http_status, /* Received HTTP status */
                                                &body,
                                                token,
                                                sig_b64 ? 6 : 5,
                                                "csr", csr_str,
                                                "nonce", provisioning_key,
                                                "fingerprint", hw_fingerprint,
                                                "timestampInSec", epoch_str,
                                                "nonceAssociationType", association_type,
                                                "signature", sig_b64);
    if (res) {
        if (get_response_body_copy_dump(body, err_buf, sizeof(err_buf)) == ZPATH_RESULT_NO_ERROR) {
            ZPATH_LOG(AL_ERROR, "Fetch CSR Response - nonce(<%.*s...>) fingerprint(<%.*s...>)\n%s",
                      PROVISION_KEY_LOG_BYTES, provisioning_key,
                      HW_FINGERPRINT_LOG_BYTES, hw_fingerprint,
                      err_buf);
        }
        if (zpn_enroll_is_cert_generation_limit_exceeded(body)) {
            /* Do not make get cert call if cert generation limit is exceeded, stop here. */
            ZPATH_LOG(AL_ERROR, "Send CSR failed, cert generation limit exceeded");
            goto cleanup_err;
        }
        ZPATH_LOG(AL_ERROR, "Send CSR failed, but that may be because we posted it in the past, so it is ok.");
    } else {
        if (s_enroll_debug) {
            if (get_response_body_copy_dump(body, err_buf, sizeof(err_buf)) == ZPATH_RESULT_NO_ERROR) {
                ZPATH_LOG(AL_DEBUG, "Send CSR Response - \n%s", err_buf);
            }
        }
    }
    if (body) evbuffer_free(body);
    body = NULL;

    /*
     * GET Certificate
     * Sign the timestamp using newly generated key - V4 re-enrollment
     * For first time enrollment, we use JWT token for authorization
     */
    if (is_re_enroll) {
        /* Create timestamp signature */
        memset(signature_b64, 0, sizeof(signature_b64));
        size_t signature_length = sizeof(signature_b64);
        int64_t re_enroll_timestamp = 0;
        if (get_enrollment_epoch_s(new_rsa_key, &re_enroll_timestamp, signature_b64, &signature_length) == ZPATH_RESULT_NO_ERROR) {
            sig_b64 = signature_b64;
            snprintf(epoch_str, sizeof(epoch_str), "%ld", (long)re_enroll_timestamp);
            if (s_enroll_debug) {
                ZPATH_LOG(AL_INFO, "Re-enrolling nonce: <%.*s...>, with re-enroll-timestamp: %ld, signature: %s",
                                    PROVISION_KEY_LOG_BYTES, provisioning_key,
                                    (long)re_enroll_timestamp, sig_b64);
            }
        } else {
            /* Hard failure because this shouldn't happen unless the environment is manipulated, files deleted.. */
            ZPN_LOG(AL_ERROR, "could not generate signature for re-enrollment, please delete /opt/zscaler/var, "
                                "and restart %s", zpn_enroll_get_enrollement_name());
            goto cleanup_err;
        }
    }

    get_enrollment_api_endpoint(enroll_type, enroll_style, ENROLLMENT_API_GET_CERTIFICATE, api_hostname, get_cert_api, sizeof(get_cert_api), customer_gid);
    ZPATH_LOG(AL_DEBUG, "Calling Get Cert API with - nonce(<%s>) fingerprint(<%s>) csr_str(<%s>) timestampInSec(<%s>) token(<%s>)",
              provisioning_key, hw_fingerprint, csr_str, epoch_str, token);

    res = fohh_http_client_fetch_synchronous_v4("Get Certificate",
                                                client,
                                                FOHH_HTTP_METHOD_POST,
                                                get_cert_api,
                                                200,          /* Expected HTTP status */
                                                &http_status, /* Received HTTP status */
                                                &body,
                                                token,
                                                sig_b64 ? 6 : 5,
                                                "nonce", provisioning_key,
                                                "fingerprint", hw_fingerprint,
                                                "csr", csr_str,
                                                "timestampInSec", epoch_str,
                                                "nonceAssociationType", association_type,
                                                "signature", sig_b64
                                                );
    if (res) {
        ZPATH_LOG(AL_ERROR, "Get Certificate failed.");
        if (get_response_body_copy_dump(body, err_buf, sizeof(err_buf)) == ZPATH_RESULT_NO_ERROR) {
            ZPATH_LOG(AL_ERROR, "Fetch CSR Response - nonce(<%.*s...>) fingerprint(<%.*s...>)\n%s",
                      PROVISION_KEY_LOG_BYTES, provisioning_key,
                      HW_FINGERPRINT_LOG_BYTES, hw_fingerprint,
                      err_buf);
        }
        goto cleanup_err;
    } else {
        if (s_enroll_debug) {
            if (get_response_body_copy_dump(body, err_buf, sizeof(err_buf)) == ZPATH_RESULT_NO_ERROR) {
                ZPATH_LOG(AL_DEBUG, "Get Certificate Response - \n%s", err_buf);
            }
        }
    }

    /* Certificates are returned as a stream of concatnated pems */
    /* First cert is our cert. */
    /* All certs in-between are intermediates. */
    /* Last cert is root. */

    res = write_certificates(body, &have_intermediates, enroll_style);

    if (res) {
        goto cleanup_err;
    }

    /* If any of these renamings fail, we can get into an
     * unrecoverable state. i.e. we should really be renaming a
     * directory. */
    if (have_intermediates) {
        rename(FILENAME_CHAIN_NEW, FILENAME_CHAIN);
    } else {
        unlink(FILENAME_CHAIN);
    }
    rename(FILENAME_KEY_NEW_PRIV, FILENAME_KEY_PRIV);
    rename(FILENAME_KEY_NEW_PUB, FILENAME_KEY_PUB);
    rename(FILENAME_CERT_NEW, FILENAME_CERT);
    rename(FILENAME_ROOT_NEW, FILENAME_ROOT);
    unlink(FILENAME_CSR_NEW);
    unlink(FILENAME_OAUTH_TOKEN);
    if (s_enroll_state.cfg_rsa_key) {
        zcrypt_rsa_key_destroy(s_enroll_state.cfg_rsa_key);
    }
    s_enroll_state.cfg_rsa_key = new_rsa_key;


    fohh_http_client_destroy_from_another_thread(client);
    SSL_CTX_free(ssl_ctx);
    if (csr) X509_REQ_free(csr);
    if (body) evbuffer_free(body);
    return ZPATH_RESULT_NO_ERROR;

cleanup_err:
    if (new_rsa_key) zcrypt_rsa_key_destroy(new_rsa_key);
    if (client) fohh_http_client_destroy_from_another_thread(client);
    if (ssl_ctx) SSL_CTX_free(ssl_ctx);
    if (body) evbuffer_free(body);
    if (csr) X509_REQ_free(csr);
    return ZPATH_RESULT_ERR;
}



/*
 * Returns NO_ERROR if there is a valid cert to use.
 *
 * This routine will attempt to re-enroll if necessary.
 */
int
zpn_enroll_check(struct zcrypt_key *key, struct zcrypt_rsa_key *rsa_key, char *hw_fingerprint, char *provisioning_key,
                 int shard, char *api_hostname, enum zpn_enrollment_type enroll_type, enum zpn_enrollment_style enroll_style,
                 char *cloud, const char *cwd, int always_re_enroll, int64_t site_reenroll_period, int64_t customer_gid, const char* token)
{
    struct zcrypt_cert *cert;
    int res;

    s_enroll_state.enroll_type = enroll_type;
    s_enroll_state.always_re_enroll = always_re_enroll;

    /********************************************************************************
     * Check if we need new cert
     */
    cert = zcrypt_cert_read(FILENAME_CERT);
    if (cert) {
        int     cert_time_percent_left;
        int64_t now = epoch_s();
        int64_t from = zcrypt_cert_valid_from_epoch(cert);
        int64_t to = zcrypt_cert_valid_to_epoch(cert);
        int     re_enroll;
        zcrypt_cert_free(cert);

        if (now >= to) {
            char *ctime_str = ctime((time_t*)&to);
            /* ctime does a null terminate which we don't like, so trim it */
            ctime_str[strlen(ctime_str) - 1] = '\0';
            ZPATH_LOG(AL_ERROR, "cert.pem expired on %s, delete %s and re-enroll.", ctime_str, cwd);
            return ZPATH_RESULT_ERR;
        }
        /*
        * Check if system time is earlier than the 'notBefore' time.
        * Since customers may have incorrect system time, this ensures the connector
        * is not running out of reasonable times.
        */
        if (now < from) {
            char *ctime_str = ctime((time_t*)&from);
            ctime_str[strlen(ctime_str) - 1] = '\0';
            ZPATH_LOG(AL_ERROR, "Corrective action - cert.pem becomes valid on %s, please check if the system time is correct", ctime_str);
            return ZPATH_RESULT_ERR;
        }

        cert_time_percent_left = (int) (((to - now) * 100 )/(to - from));
        re_enroll = 0;

        if (cert_time_percent_left <= (100 - ZPN_RE_ENROLL_TIME_FACTOR)) {
            re_enroll = 1;
            ZPATH_LOG(AL_NOTICE, "Certificate within (%d) percent of expiration - attempting to re-enroll",
                    100 - ZPN_RE_ENROLL_TIME_FACTOR);
        } else if (site_reenroll_period > 0 && (now > (to - (site_reenroll_period * 24 * 60 * 60)))) {
            re_enroll = 1;
            ZPATH_LOG(AL_NOTICE, "Certificate within (%"PRId64") days of expiration - due to cached site config, attempting to re-enroll",
                                 site_reenroll_period);
        } else if (s_enroll_state.always_re_enroll) {
            re_enroll = 1;
            ZPATH_LOG(AL_NOTICE, "Debug option to 're-enroll always' is set - attempting to re-enroll");
        }

        if (re_enroll) {
            if (enroll_style == ZPN_ENROLLMENT_STYLE_V3) {
                res = zpn_enroll_v3(key, rsa_key, hw_fingerprint, provisioning_key, shard, api_hostname, enroll_type, enroll_style, cloud, re_enroll, customer_gid);
            } else if (enroll_style == ZPN_ENROLLMENT_STYLE_V4) {
                res = zpn_enroll_v4(key, rsa_key, hw_fingerprint, provisioning_key, shard, api_hostname, enroll_type, enroll_style, cloud, re_enroll, token, customer_gid);
            } else {
                res = zpn_enroll(key, rsa_key, hw_fingerprint, provisioning_key, shard, api_hostname, enroll_type, enroll_style, cloud, re_enroll);
            }
            if (res) {
                ZPATH_LOG(AL_NOTICE, "Certificate re-enrollment failed.");
                ZPATH_LOG(AL_WARNING, "Running with a certificate that expires in %ld seconds (%ld days)",
                        (long)(to - now), (long)((to - now)/86400));
                return ZPATH_RESULT_NO_ERROR;
            } else {
                ZPATH_LOG(AL_NOTICE, "%s has been successfully re-enrolled", zpn_enroll_get_enrollement_name());
                return ZPATH_RESULT_NO_ERROR;
            }
        } else {
            ZPATH_LOG(AL_NOTICE, "Have valid certificate.");
            return ZPATH_RESULT_NO_ERROR;
        }
    } else {
        ZPATH_LOG(AL_NOTICE, "No valid certificate. Attempting to enroll");

        if (enroll_style == ZPN_ENROLLMENT_STYLE_V3) {
            res = zpn_enroll_v3(key, rsa_key, hw_fingerprint, provisioning_key, shard, api_hostname, enroll_type, enroll_style, cloud, 0, customer_gid);
        } else if (enroll_style == ZPN_ENROLLMENT_STYLE_V4) {
            res = zpn_enroll_v4(key, rsa_key, hw_fingerprint, provisioning_key, shard, api_hostname, enroll_type, enroll_style, cloud, 0, token, customer_gid);
        } else {
            res = zpn_enroll(key, rsa_key, hw_fingerprint, provisioning_key, shard, api_hostname, enroll_type, enroll_style, cloud, 0);
        }
        if (res) {
            ZPATH_LOG(AL_NOTICE, "Certificate enrollment failed.");
            return ZPATH_RESULT_ERR;
        } else {
            /* Successful enrollment. */
            ZPATH_LOG(AL_NOTICE, "%s has been successfully enrolled", zpn_enroll_get_enrollement_name());
            return ZPATH_RESULT_NO_ERROR;
        }
    }
    return ZPATH_RESULT_ERR;
}

/*
 * Update version
 */
int zpn_enroll_update_version(const char *filename, const char *new_version, char *written_version, size_t written_version_space)
{
    FILE *fp;
    size_t len;

    len = strlen(new_version);
    if (len > (written_version_space - 1)) {
        ZPN_LOG(AL_ERROR, "Version too large: %s", new_version);
        return ZPN_RESULT_ERR;
    }

    fp = fopen(filename, "w");
    if (!fp) {
        ZPN_LOG(AL_ERROR, "Could not open version file %s for writing", filename);
        return ZPN_RESULT_ERR;
    }
    if (fwrite(new_version, len, 1, fp) != 1) {
        fclose(fp);
        unlink(filename);
        ZPN_LOG(AL_ERROR, "Could not write version to %s", filename);
        return ZPN_RESULT_ERR;
    }
    fclose(fp);
    snprintf(written_version, written_version_space, "%s", new_version);
    return ZPN_RESULT_NO_ERROR;
}



/*
 * Get enrollment style name
 */
static const char* get_enrollement_style_name(enum zpn_enrollment_style enroll_style)
{
    static char *names[] = { "Unknown", "V1-Enrollment", "V2-Enrollment", "V3-Enrollment", "V4-Enrollment"};

    if ((enroll_style >= ZPN_ENROLLMENT_STYLE_V1) && (enroll_style <= ZPN_ENROLLMENT_STYLE_V4)) {
        return names[(int)enroll_style];
    }

    return names[0];
}

/*
 * Get enrollment style name
 */
static const char* get_enrollement_api_name(enum zpn_enrollment_api_type api_type)
{
    static char *names[] = { "Unknown", "Sign-In", "Fetch-CSR-Details", "Send-CSR", "Get-Certificate",
                            "Get-Enrollment-Details", "Send-CSR-And-Get-Cert", "Update-Public-Key" };

    if ((api_type >= ENROLLMENT_API_SIGN_IN) && (api_type <= ENROLLMENT_API_SEND_PUB_KEY)) {
        return names[(int)api_type];
    }

    return names[0];
}




/*
 * Get provisioning key env
 */
int zpn_enroll_get_provisioning_key_env(char *env, size_t env_len)
{
    char *e = getenv(PROVISIONING_KEY_ENV);
    if (e) {
        size_t len = strlen(e);
        if (len <= env_len) {
            snprintf(env, env_len, "%s", e);
            return ZPN_RESULT_NO_ERROR;
        }
    }
    ZPN_LOG(AL_DEBUG, "Could not get provisioning key from environment variable '%s'", PROVISIONING_KEY_ENV);
    return ZPN_RESULT_ERR;
}

/*
 * Get provisioning key file
 */
int zpn_enroll_get_provisioning_key_file(char *env, size_t env_len)
{
    FILE *fp;
    size_t len;

    fp = fopen(FILENAME_PROVISION_KEY, "r");
    if (fp) {
        len = fread(env, 1, env_len - 1, fp);
        fclose(fp);
        if (len) {
            env[len] = 0;
            return ZPN_RESULT_NO_ERROR;
        }
    }
    ZPN_LOG(AL_DEBUG, "Could not get provisioning key from file '%s'", FILENAME_PROVISION_KEY);
    return ZPN_RESULT_ERR;
}


/*
 * Upgrade prep -
 */
extern int zpn_enroll_upgrade_prep(zpn_enroll_upgrade_prep_callback *prep_callback, int64_t upgrade_id, void *upgrade_state, int64_t time_delta, int auto_upgrade_disabled)
{
    int ret = ZPATH_RESULT_NO_ERROR;
    const char *name;

    /* Make sure state is correctly setup */
    if ((s_enroll_state.magic != ENROLLMENT_API_INIT_MAGIC) || (upgrade_id == 0) || (upgrade_state == NULL) || (prep_callback == NULL)) {
        ret = ZPATH_RESULT_BAD_ARGUMENT;
    } else {
        s_enroll_state.upgrade_id = upgrade_id;
        s_enroll_state.upgrade_state = upgrade_state;
    }

    if (ret != ZPATH_RESULT_NO_ERROR) {
        goto done;

    }
    switch (s_enroll_state.enroll_type) {
        case ZPN_ENROLLMENT_TYPE_ASSISTANT:
        case ZPN_ENROLLMENT_TYPE_NP:
        case ZPN_ENROLLMENT_TYPE_PRIVATE_BROKER:
        case ZPN_ENROLLMENT_TYPE_SITEC:
            name = zpn_enroll_get_enrollement_name();
            ret = prep_callback(s_enroll_state.upgrade_id, s_enroll_state.upgrade_state, time_delta, auto_upgrade_disabled);
            if ((ret != ZPATH_RESULT_NO_ERROR) &&
                (ret != ZPATH_RESULT_NOT_READY)) {
                ZPN_LOG(AL_ERROR, "Upgrade prep error for: %s, err: %s, upgrade_id: %"PRId64, name, zpath_result_string(ret), upgrade_id);
            }
            break;
        default:
            assert(0);
            break;
    }

done:
    return ret;
}

/*
 * Enroll upload stack
 */
void zpn_enroll_upload_stack(char *upload_host, char *upload_path, char *app_name, char *app_inst, uint64_t customer_id)
{
    char remote_host[1000];
    char date[ARGO_LOG_GEN_TIME_STR_LEN];
    char full_path[1000];
    char filename[128];
    struct fohh_http_client *client;
    struct stat st;
    struct evbuffer *buf = NULL;
    struct evbuffer *result_buf = NULL;
    struct evbuffer *extra_headers = NULL;
    enum fohh_http_client_request_status req_status;
    int http_status;

    char *upload_file;
    FILE *fp;

    SSL_CTX *ssl_ctx = NULL;
    int res;

    char *stack_trace_version;
    char *stack_trace_platform;

    snprintf(filename, sizeof(filename), "%s.stack", app_name);

    if (stat(filename, &st) != 0) {
        /* No stack to upload. That's good. */
        return;
    }
    if (!(S_ISREG(st.st_mode))) {
        /* Stack is not a normal file, skip */
        return;
    }
    if (st.st_size > ZPN_MAX_STACK_FILE_SIZE) {
        /* Stack dump is too big, skip */
        ZPATH_LOG(AL_CRITICAL, "Skipping crash stack trace upload. stack file size: %"PRId64", exceeds max size: %d", st.st_size, ZPN_MAX_STACK_FILE_SIZE);
        return;
    }

    fp = fopen(filename, "r");
    if (!fp) {
        /* Couldn't read it... skip */
        return;
    }

    upload_file = malloc(st.st_size);
    if (!upload_file) {
        fclose(fp);
        return;
    }

    if (fread(upload_file, st.st_size, 1, fp) != 1) {
        fclose(fp);
        free(upload_file);
        return;
    }

    fclose(fp);

    /*
     * We remove the file so that we don't repeatedly upload it.
     */
    unlink(filename);

    stack_trace_platform = zthread_stack_platform(upload_file, st.st_size);
    if (!stack_trace_platform) {
        stack_trace_platform = "unknown.unknown.unknown";
    }

    stack_trace_version = zthread_stack_version(upload_file, st.st_size);
    if (!stack_trace_version) {
        stack_trace_version = "unknown";
    }

    buf = evbuffer_new();
    if (!buf) {
        free(upload_file);
        return;
    }

    if (evbuffer_add(buf, upload_file, st.st_size)) {
        evbuffer_free(buf);
        free(upload_file);
        return;
    }

    res = argo_log_gen_time(epoch_us(), date, sizeof(date), 0, 1);
    if (res) {
        evbuffer_free(buf);
        free(upload_file);
        return;
    }

    snprintf(full_path, sizeof(full_path), "%s%s/%s/%s.%ld.%s.%s.stack",
             upload_path, app_name, stack_trace_version, date, (long)customer_id, app_inst, stack_trace_platform);

    free(upload_file);

    if (s_enroll_state.cfg_api_direct) {
        snprintf(remote_host, sizeof(remote_host), "%s", upload_host);
    } else {
        switch (s_enroll_state.enroll_type) {
            case ZPN_ENROLLMENT_TYPE_ASSISTANT:
            case ZPN_ENROLLMENT_TYPE_NP:
                snprintf(remote_host, sizeof(remote_host), "co2br.%s", s_enroll_state.cfg_key_cloud);
                break;
            case ZPN_ENROLLMENT_TYPE_PRIVATE_BROKER:
                snprintf(remote_host, sizeof(remote_host), "pb2br.%s", s_enroll_state.cfg_key_cloud);
                break;
            case ZPN_ENROLLMENT_TYPE_SITEC:
                snprintf(remote_host, sizeof(remote_host), "sc2br.%s", s_enroll_state.cfg_key_cloud);
                break;

            default:
                assert(0);
                break;
        }
    }

    ssl_ctx = fohh_http_client_get_ssl_ctx();
    if (!ssl_ctx) {
        evbuffer_free(buf);
        return;
    }

    ZPATH_LOG(AL_NOTICE, "Uploading crash stack trace without data: Connecting to %s via %s for the file %s.", upload_host, remote_host, filename);
    client = get_http_client("Stack upload", ssl_ctx, upload_host);

    if (!client) {
        evbuffer_free(buf);
        SSL_CTX_free(ssl_ctx);
        return;
    }

    extra_headers = evbuffer_new();
    if (!extra_headers) {
        evbuffer_free(buf);
        fohh_http_client_destroy_from_another_thread(client);
        SSL_CTX_free(ssl_ctx);
        return;
    }

    if (!evbuffer_add_printf(extra_headers, "x-amz-acl: bucket-owner-full-control\r\n")) {
        evbuffer_free(buf);
        evbuffer_free(extra_headers);
        fohh_http_client_destroy_from_another_thread(client);
        SSL_CTX_free(ssl_ctx);
        return;
    }

    res = fohh_http_client_request_synchronous(client,
                                                10*1000*1000,
                                                FOHH_HTTP_METHOD_PUT,
                                                full_path,
                                                extra_headers,
                                                "application/octet-stream",
                                                buf,
                                                &req_status,
                                                &http_status,
                                                &result_buf);
    if (res) {
        ZPATH_LOG(AL_ERROR, "Could not upload stack trace");
    } else {
        if (req_status == fohh_http_client_request_status_timeout) {
            ZPN_LOG(AL_ERROR, "Stack upload failed: timeout");
        } else if (req_status == fohh_http_client_request_status_failure) {
            ZPN_LOG(AL_ERROR, "Stack upload failed: http protocol failure");
        } else if (req_status != fohh_http_client_request_status_success) {
            ZPN_LOG(AL_ERROR, "Stack upload failed");
        } else {
            ZPN_LOG(AL_NOTICE, "Stack upload complete %d %d", req_status, http_status);
        }
    }

    evbuffer_free(buf);
    evbuffer_free(extra_headers);
    if (result_buf) evbuffer_free(result_buf);
    fohh_http_client_destroy_from_another_thread(client);
    SSL_CTX_free(ssl_ctx);
    return;
}

int zpn_enroll_send_public_key_per_entity_v3(const struct zcrypt_rsa_key *rsa_key,
                                             const char *api_hostname,
                                             enum zpn_enrollment_type enroll_type,
                                             const char *public_key_str,
                                             int64_t public_key_expiry_s,
                                             const char *provisioning_key,
                                             const char *hw_fingerprint,
                                             int64_t customer_gid)
{
    struct fohh_http_client *client = NULL;
    struct evbuffer *body = NULL;
    SSL_CTX *ssl_ctx = NULL;
    int http_status;
    char signature_b64[1000];
    char *sig_b64 = NULL;
    char send_pub_key_api[ENROLLMENT_API_BUF_SIZE];
    char epoch_str[32];
    char err_buf[BUFSIZ] = "";
    char public_key_expiry_s_str[256];

    int res = ZPATH_RESULT_NO_ERROR;

    /*
     * Get SSL context
     */
    ssl_ctx = fohh_http_client_get_ssl_ctx();
    if (!ssl_ctx) {
        ZPATH_LOG(AL_ERROR, "Cannot get SSL context");
        return ZPATH_RESULT_ERR;
    }

    /*
     * Create TLS connection to our enrollment web server to send public key
     */
    client = get_http_client("Update NP Connector public key.", ssl_ctx, api_hostname);
    if (!client) {
        res = ZPATH_RESULT_ERR;
        ZPATH_LOG(AL_ERROR, "Could not create HTTP client to send public key");
        goto done;
    }

    size_t signature_length = sizeof(signature_b64);
    int64_t timestamp_s;
    res = get_enrollment_epoch_s(rsa_key, &timestamp_s, signature_b64, &signature_length);
    if (res == ZPATH_RESULT_NO_ERROR) {
        sig_b64 = signature_b64;
        snprintf(epoch_str, sizeof(epoch_str), "%"PRId64, timestamp_s);
    } else {
        ZPN_LOG(AL_ERROR, "could not generate signature, please delete /opt/zscaler/var, "
                            "recopy the provision key and restart %s", zpn_enroll_get_enrollement_name());
        goto done;
    }

    snprintf(public_key_expiry_s_str, sizeof(public_key_expiry_s_str), "%"PRId64, public_key_expiry_s);

    get_enrollment_api_endpoint(enroll_type, ZPN_ENROLLMENT_STYLE_V3, ENROLLMENT_API_SEND_PUB_KEY, api_hostname, send_pub_key_api, sizeof(send_pub_key_api), customer_gid);
    res = fohh_http_client_fetch_synchronous("Send Public Key",
                                              client,
                                              FOHH_HTTP_METHOD_PUT,
                                              send_pub_key_api,
                                              200,          /* Expected HTTP status */
                                              &http_status, /* Received HTTP status */
                                              &body,
                                              6,
                                              "publicKey", public_key_str,
                                              "publicKeyExpiry", public_key_expiry_s_str,
                                              "nonce", provisioning_key,
                                              "fingerprint", hw_fingerprint,
                                              "timestampInSec", epoch_str,
                                              "signature", sig_b64);
    if (res) {
        ZPATH_LOG(AL_DEBUG, "Send Public Key failed.");
        if (get_response_body_copy_dump(body, err_buf, sizeof(err_buf)) == ZPATH_RESULT_NO_ERROR) {
            ZPATH_LOG(AL_DEBUG, "Send Public Key Response - nonce(<%.*s...>) fingerprint(<%.*s...>) signature(%s)\n publickey %s, publicKeyExpiry %s\n  %s",
                      PROVISION_KEY_LOG_BYTES, provisioning_key,
                      HW_FINGERPRINT_LOG_BYTES, hw_fingerprint,
                      sig_b64, public_key_str, public_key_expiry_s_str, err_buf);
        } else {
            ZPATH_LOG(AL_DEBUG, "Could not get_response_body_copy_dump");
        }
        goto done;
    } else {
        if (s_enroll_debug) {
            if (get_response_body_copy_dump(body, err_buf, sizeof(err_buf)) == ZPATH_RESULT_NO_ERROR) {
                ZPATH_LOG(AL_DEBUG, "Send Public Key Response - \n%s", err_buf);
            }
        }
    }

done:
    if (client) fohh_http_client_destroy_from_another_thread(client);
    if (ssl_ctx) SSL_CTX_free(ssl_ctx);
    if (body) evbuffer_free(body);
    return res;

}

int zpn_enroll_is_enrollment_completed() {
    if ((access(FILENAME_CERT, F_OK) == 0) &&
        (access(FILENAME_KEY_PRIV, F_OK) == 0) &&
        (access(FILENAME_KEY_PUB, F_OK) == 0)) {
        return 1;
    }

    return 0;
}

/*
 * Send public key to wireguard client using OAuth compatible V4 enrollment APIs
 */
int zpn_enroll_send_public_key_per_entity_v4(const struct zcrypt_rsa_key *rsa_key,
                                             const char *api_hostname,
                                             enum zpn_enrollment_type enroll_type,
                                             const char *public_key_str,
                                             int64_t public_key_expiry_s,
                                             const char *provisioning_key,
                                             const char *hw_fingerprint,
                                             int64_t customer_gid,
                                             const char *oauth_token)
{
    struct fohh_http_client *client = NULL;
    struct evbuffer *body = NULL;
    SSL_CTX *ssl_ctx = NULL;
    int http_status;
    char signature_b64[1000];
    char *sig_b64 = NULL;
    char send_pub_key_api[ENROLLMENT_API_BUF_SIZE];
    char epoch_str[32];
    char err_buf[BUFSIZ] = "";
    char public_key_expiry_s_str[256];
    char association_type[32];

    int res = ZPATH_RESULT_NO_ERROR;

    /*
     * Get SSL context
     */
    ssl_ctx = fohh_http_client_get_ssl_ctx();
    if (!ssl_ctx) {
        ZPATH_LOG(AL_ERROR, "Cannot get SSL context");
        return ZPATH_RESULT_ERR;
    }

    /*
     * Create TLS connection to our enrollment web server to send public key
     */
    client = get_http_client("Update NP Connector public key.", ssl_ctx, api_hostname);
    if (!client) {
        res = ZPATH_RESULT_ERR;
        ZPATH_LOG(AL_ERROR, "Could not create HTTP client to send public key");
        goto done;
    }

    size_t signature_length = sizeof(signature_b64);
    int64_t timestamp_s;
    res = get_enrollment_epoch_s(rsa_key, &timestamp_s, signature_b64, &signature_length);
    if (res == ZPATH_RESULT_NO_ERROR) {
        sig_b64 = signature_b64;
        snprintf(epoch_str, sizeof(epoch_str), "%"PRId64, timestamp_s);
    } else {
        ZPN_LOG(AL_ERROR, "Could not generate signature, please delete /opt/zscaler/var. "
                          "OAuth based enrollment is the preffered way of enrollment. "
                          "Alternatively, add the provision key manually and restart %s",
                          zpn_enroll_get_enrollement_name());
        goto done;
    }

    snprintf(public_key_expiry_s_str, sizeof(public_key_expiry_s_str), "%"PRId64, public_key_expiry_s);

    if (zpn_enroll_get_association_type(enroll_type, association_type, sizeof(association_type)) != ZPATH_RESULT_NO_ERROR) {
        ZPN_LOG(AL_ERROR, "[Send Public Key] Unable to ge the Nonce association type for %s",
                          zpn_enroll_get_enrollement_name());
        goto done;
    }

    get_enrollment_api_endpoint(enroll_type, ZPN_ENROLLMENT_STYLE_V4, ENROLLMENT_API_SEND_PUB_KEY,
                                api_hostname, send_pub_key_api, sizeof(send_pub_key_api), customer_gid);
    res = fohh_http_client_fetch_synchronous_v4("Send Public Key",
                                              client,
                                              FOHH_HTTP_METHOD_PUT,
                                              send_pub_key_api,
                                              200,          /* Expected HTTP status */
                                              &http_status, /* Received HTTP status */
                                              &body,
                                              oauth_token,
                                              7,
                                              "publicKey", public_key_str,
                                              "publicKeyExpiry", public_key_expiry_s_str,
                                              "nonce", provisioning_key,
                                              "fingerprint", hw_fingerprint,
                                              "timestampInSec", epoch_str,
                                              "nonceAssociationType", association_type, /* To support directly calling API using JWT - Terraform scripts maybe */
                                              "signature", sig_b64);
    if (res) {
        ZPATH_LOG(AL_DEBUG, "Send Public Key failed.");
        if (get_response_body_copy_dump(body, err_buf, sizeof(err_buf)) == ZPATH_RESULT_NO_ERROR) {
            ZPATH_LOG(AL_DEBUG, "Send Public Key Response - nonce(<%.*s...>) fingerprint(<%.*s...>) "
                                "signature(%s) nonceAssociationType (%s)\n publickey (%s), publicKeyExpiry (%s)\n  %s",
                                PROVISION_KEY_LOG_BYTES, provisioning_key,
                                HW_FINGERPRINT_LOG_BYTES, hw_fingerprint,
                                sig_b64, association_type, public_key_str, public_key_expiry_s_str, err_buf);
        } else {
            ZPATH_LOG(AL_DEBUG, "[Send Public Key] Could not get_response_body_copy_dump");
        }
        goto done;
    } else {
        if (s_enroll_debug) {
            if (get_response_body_copy_dump(body, err_buf, sizeof(err_buf)) == ZPATH_RESULT_NO_ERROR) {
                ZPATH_LOG(AL_DEBUG, "Send Public Key Response - \n%s", err_buf);
            }
        }
    }

done:
    if (client) fohh_http_client_destroy_from_another_thread(client);
    if (ssl_ctx) SSL_CTX_free(ssl_ctx);
    if (body) evbuffer_free(body);
    return res;
}
