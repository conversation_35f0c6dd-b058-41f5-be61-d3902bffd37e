/*
 * wally_test.c. Copyright (C) 2013-2015 Zscaler, Inc. All Rights Reserved.
 *
 * Uses three wally instances, acting as server, middle, and client
 * wally's in a distribution system.
 *
 * The structure used for all these tests is 'wally_test'.
 */


#include <stdio.h>
#include <stdarg.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <pthread.h>
#include <sys/time.h>
#include <ctype.h>
#include <signal.h>

#include "zpath_misc/zpath_misc.h"
#include "zpath_lib/zpath_config_override.h"

#include "fohh/fohh.h"
#include "wally/wally.h"
#include "wally/wally_postgres.h"
#include "wally/wally_sqlt.h"
#include "wally/wally_db.h"
#include "wally/wally_fohh_client.h"
#include "wally/wally_fohh_server.h"
#include "wally/wally_private.h"

#include "zpath_lib/zpath_app.h"
#include "zpath_lib/zpath_debug.h"
#include "zpath_lib/zpath_debug_wally.h"

#include "wally_test/wally_test.h"
#include "wally_test/wally_test_compiled.h"
#include "zpath_lib/zpath_entity.h"
//#include <zpath_entity_compiled.h>
#include "zpath_lib/zpath_instance.h"
//#include <zpath_instance_compiled.h>
#include "zpath_lib/zpath_ip_entity.h"
//#include <zpath_ip_entity_compiled.h>
#include "zpath_lib/zpath_limit.h"
//#include <zpath_limit_compiled.h>
#include "zpath_lib/zpath_policy.h"
//#include <zpath_policy_compiled.h>
#include "zpath_lib/zpath_rule.h"
//#include <zpath_rule_compiled.h>
#include "zpath_lib/zpath_zurldb.h"
//#include <zpath_zurldb_compiled.h>
#include "zpath_lib/zpath_customer_log_config.h"
//#include <zpath_customer_log_config_compiled.h>
#include "zpath_lib/zpath_constellation.h"
//#include <zpath_constellation_compiled.h>
#include "zpath_lib/zpath_debug_wally.h"
#include "zpath_lib/zpath_domain_lookup.h"
//#include <zpath_domain_lookup_compiled.h>
#include "zpath_lib/zpath_domainlist.h"
//#include <zpath_domainlist_compiled.h>
#include "zpath_lib/zpath_customer_notification.h"
//#include <zpath_customer_notification_compiled.h>
#include "zpath_lib/zpath_category.h"
//#include <zpath_category_compiled.h>
#include "zpath_lib/zpath_cloud.h"
//#include <zpath_cloud_compiled.h>
#include "zpath_lib/zpath_ip_location.h"
//#include <zpath_ip_location_compiled.h>
#include "zpath_lib/zpath_table.h"
//#include <zpath_table_compiled.h>
#include "zpath_lib/zpath_tag_entity.h"
//#include <zpath_tag_entity_compiled.h>
#include "zpath_lib/zpath_customer_logo.h"
//#include <zpath_customer_logo_compiled.h>
#include "zpath_lib/zpath_location.h"
//#include <zpath_location_compiled.h>
#include "zpath_lib/zpath_tls.h"
//#include <zpath_tls_compiled.h>
#include "zpath_lib/zpath_customer.h"
//#include <zpath_customer_compiled.h>
#include "zpath_lib/zpath_log_config.h"
//#include <zpath_log_config_compiled.h>
#include "zpath_lib/zpath_log_store.h"
//#include <zpath_log_store_compiled.h>
#include "zpath_lib/zpath_service.h"
//#include <zpath_service_compiled.h>

/* ZPN tables... */
#include "zpn/zpn_app_group_relation.h"
//#include <zpn_app_group_relation_compiled.h>
#include "zpn/zpn_application.h"
//#include <zpn_application_compiled.h>
#include "zpn/zpn_application_group.h"
//#include <zpn_application_group_compiled.h>
#include "zpn/zpn_application_group_application_mapping.h"
//#include <zpn_application_group_application_mapping_compiled.h>
#include "zpn/zpn_application_server.h"
//#include <zpn_application_server_compiled.h>
#include "zpn/zpn_assistant_group.h"
//#include <zpn_assistant_group_compiled.h>
#include "zpn/zpn_assistant_table.h"
//#include <zpn_assistant_table_compiled.h>
#include "zpn/zpn_assistant_version.h"
//#include <zpn_assistant_version_compiled.h>
#include "zpn/zpn_assistantgroup_assistant_relation.h"
//#include <zpn_assistantgroup_assistant_relation_compiled.h>
#include "zpn/zpn_server_group.h"
//#include <zpn_server_group_compiled.h>
#include "zpn/zpn_server_group_assistant_group.h"
//#include <zpn_server_group_assistant_group_compiled.h>
#include "zpn/zpn_servergroup_server_relation.h"
//#include <zpn_servergroup_server_relation_compiled.h>
#include "zpn/zpn_signing_cert.h"
//#include <zpn_signing_cert_compiled.h>
#include "zpn/zpn_issuedcert.h"
//#include <zpn_issuedcert_compiled.h>
#include "zpn/zpn_client_table.h"
//#include <zpn_client_table_compiled.h>
#include "zpn/zpn_idp.h"
//#include <zpn_idp_compiled.h>
#include "zpn/zpn_idp_cert.h"
//#include <zpn_idp_cert_compiled.h>
#include "zpn/zpn_rule.h"
//#include <zpn_rule_compiled.h>
#include "zpn/zpn_rule_condition_operand.h"
#include "zpn/zpn_policy_set.h"
//#include <zpn_policy_set_compiled.h>
//#include <zpn_rule_condition_operand_compiled.h>
#include "zpn/zpn_rule_condition_set.h"
//#include <zpn_rule_condition_set_compiled.h>
#include "zpn/zpn_saml_attrs.h"
//#include <zpn_saml_attrs_compiled.h>
#include "zpn/zpn_broker_load.h"
//#include <zpn_broker_load_compiled.h>
#include "zpn/zpn_shared_customer_domain.h"
//#include <zpn_shared_customer_domain_compiled.h>
#include "zpn/zpn_version.h"
//#include <zpn_version_compiled.h>
#include "zpn/zpn_version_profile.h"
//#include <zpn_version_profile_compiled.h>

#include "zpath_misc/zpath_version.h"
#include "zpath_lib/zpath_config_override_keys.h"

extern int32_t VERSION_MAJOR_MIN;
extern int32_t VERSION_MINOR_MIN;
extern int32_t VERSION_PATCH_MIN;
extern int32_t dont_send_version;
int32_t run_as_wallyd = 0;

struct argo_structure_description *wally_test_description = NULL;

struct wally *wally_server = NULL;
struct wally_origin *wallyd_local_db = NULL;
struct wally_origin *wallyd_remote_db = NULL;
/*
struct wt {
    int skip_for_global_wally;  // Not used for wally_test
    int writable_true_origin;   // Not used for wally_test
    char *table_name;
    int struct_size;
    struct argo_field_description *desc;
    int struct_field_count;
};
*/
static int32_t responses_waiting = 0;

int   config_fully_loaded = 0;
char *config_cert = "../cert/host1_root.crt";
char *config_key  = "../cert/host1_root.crt.key";
char *config_root = "../cert/root.crt";
char *config_remote_host = NULL;
int   config_remote_port_he = 8100;
char *config_local_db = "wally_test";
int   config_export_port_he = 8100;
int   config_debug_port_he = 8000;
char *config_logfile = "log";
int   config_register_count = 0;
int   use_sqlt = 0;
int   do_cleanup = 0;
int   do_cleanup_test = 0;
char *wally_server_name = "wally_server_postgres";

/* Support all the default wallyD tables */
struct wt wts[] = {
    { 1, 0, 0, NULL, WALLY_TEST_HELPER },
#if 0
    { 1, 0, ZPATH_INSTANCE_HELPER },
    { 1, 0, ZPATH_CLOUD_HELPER },
    { 0, 0, ZPATH_IP_ENTITY_HELPER },
    { 0, 0, ZPATH_ENTITY_HELPER },
    { 0, 0, ZPATH_LIMIT_HELPER },
    { 0, 0, ZPATH_POLICY_HELPER },
    { 0, 0, ZPATH_RULE_HELPER },
    { 0, 0, ZPATH_CUSTOMER_LOG_CONFIG_HELPER },
    { 0, 0, ZPATH_CONSTELLATION_HELPER },
    { 0, 0, ZPATH_CONSTELLATION_INSTANCE_HELPER },
    { 0, 0, ZPATH_DOMAIN_LOOKUP_HELPER },
    { 0, 0, ZPATH_DOMAINLIST_HELPER},
    { 0, 0, ZURLDB_ACTIONS_HELPER },
    { 0, 0, ZPATH_CUSTOMER_NOTIFICATION_HELPER },
    { 0, 0, ZPATH_CATEGORY_HELPER },
    { 0, 0, ZPATH_IP_LOCATION_HELPER },
    { 1, 0, ZPATH_TABLE_HELPER },
    { 0, 0, ZPATH_ZURLDB_DOMAIN_HELPER },
    { 0, 0, ZPATH_TAG_ENTITY_HELPER },
    { 0, 0, ZPATH_CUSTOMER_LOGO_HELPER },
    { 0, 0, ZPATH_LOCATION_HELPER },
    { 0, 1, ZPATH_TLS_HELPER },
    { 0, 0, ZPATH_CUSTOMER_HELPER },
    { 0, 0, ZPATH_LOG_CONFIG_HELPER },
    { 0, 0, ZPATH_LOG_STORE_HELPER },
    { 0, 0, ZPATH_SERVICE_HELPER },
    { 0, 0, ZPN_APP_GROUP_RELATION_HELPER },
    { 0, 0, ZPN_APPLICATION_HELPER },
    { 0, 0, ZPN_APPLICATION_GROUP_HELPER },
    { 0, 0, ZPN_APPLICATION_GROUP_APPLICATION_MAPPING_HELPER },
    { 0, 0, ZPN_APP_SERVER_HELPER },
    { 0, 0, ZPN_ASSISTANT_HELPER },
    { 0, 0, ZPN_ASSISTANT_GROUP_HELPER },
    { 0, 0, ZPN_ASSISTANT_VERSION_HELPER },
    { 0, 0, ZPN_ASSISTANTGROUP_ASSISTANT_RELATION_HELPER },
    { 0, 0, ZPN_SERVER_GROUP_HELPER },
    { 0, 0, ZPN_SERVER_GROUP_ASSISTANT_GROUP_HELPER },
    { 0, 0, ZPN_SERVERGROUP_SERVER_RELATION_HELPER },
    { 0, 0, ZPN_SIGNING_CERT_HELPER },
    { 0, 0, ZPN_ISSUEDCERT_HELPER },
    { 0, 0, ZPN_CLIENT_HELPER },
    { 0, 0, ZPN_IDP_HELPER },
    { 0, 0, ZPN_IDP_CERT_HELPER },
    { 0, 0, ZPN_RULE_HELPER },
    { 0, 0, ZPN_RULE_CONDITION_OPERAND_HELPER },
    { 0, 0, ZPN_RULE_CONDITION_SET_HELPER },
    { 0, 0, ZPN_SAML_ATTRS_HELPER },
    { 0, 1, ZPN_BROKER_LOAD_HELPER },
	{ 0, 0, ZPN_POLICY_SET_HELPER },
    { 0, 0, ZPN_SHARED_CUSTOMER_DOMAIN_HELPER },
    { 0, 0, ZPN_VERSION_HELPER },
    { 0, 0, ZPN_VERSION_PROFILE_HELPER },
#endif // 0
};

void usage(const char *argv0, const char *format, ...)
    __attribute__((format(printf, 2, 3)));
void usage(const char *argv0, const char *format, ...)
{
    va_list list;

    fprintf(stdout, "Error: ");
	va_start(list, format);
	vfprintf(stdout, format, list);

    fprintf(stdout, "%s: Usage: \n", argv0);
    fprintf(stdout,
            "   -cert [FILE]       : defaults to ../cert/host1_root.crt\n"
            "   -key [FILE]        : defaults to ../cert/host1_root.crt.key\n"
            "   -root [FILE]       : defaults to ../cert/root.crt\n"
            "   -remotehost [HOST] : Specify remote host as origin DB. Defaults none\n"
            "   -remoteport [PORT] : Specify port to use to connect to remotehost. Defaults 8100\n"
            "   -localdb [NAME]    : Specify the name of the local DB to use. Defaults 'wally_test'\n"
            "                        If set to 'NONE' then no local db is used\n"
            "   -exportport [PORT] : Specify port upon which to export wally service.\n"
            "                        Defaults 8100. Set to 0 to disable (and make this an endpoint)\n"
            "   -debugport [PORT]  : Specify port for debug service. 0 is off. Defaults 8000\n"
            "   -logfile [FILE]    : Specify file name for logging logs. Defaults 'log'\n"
            "                        If set to 'NONE' will disable logging\n"
            "   -register COUNT    : Automatically register COUNT times on wally_test db,\n"
            "                        'ix_num', from 1 -> COUNT inclusive\n"
            "   -fully_loaded      : Run the db fully loaded\n"
            "   -sqlite            : Use SQLite for client or not. (if not will use postgres).\n"
            "                        This flag only apply if remotehost is not NULL.\n"
            "   -cleanup           : Cleanup table\n"
            "   -cleanup_test      : Run table cleanup unit test\n"
            "   -printf            : using printf, otherwise using log file\n"
            "   -vmajor            : min major version\n"
            "   -vminor            : min minor version\n"
            "   -vpatch            : min patch version\n"
            "   -no_version        : don't send version message\n"
            );
    exit(1);
}

static int fetch_callback(void *response_callback_cookie,
                          struct wally_registrant *registrant,
                          struct wally_table *table,
                          int64_t request_id,
                          int row_count)
{
    char *table_name = response_callback_cookie;

    int32_t current_count = __sync_sub_and_fetch_4(&(responses_waiting), 1);
    WALLY_LOG(AL_NOTICE, "Table %s: Fully loaded: Read %d rows. %d tables left", table_name, row_count, (int) current_count);
    return WALLY_RESULT_NO_ERROR;
}

int generic_row_callback(void *cookie,
                         struct wally_registrant *registrant,
                         struct wally_table *table,
                         struct argo_object *previous_row,
                         struct argo_object *row,
                         int64_t request_id)
{
#if 1
    char *msg = cookie;
    char row_dump[1000];
    if (argo_object_dump(row, row_dump, sizeof(row_dump), NULL, 0)) {
        row_dump[0] = 0;
    }
    ZPATH_LOG(AL_DEBUG, "%s: %10ld: Received row %s", msg, (long) request_id, row_dump);
#endif // 0
    return WALLY_RESULT_NO_ERROR;
}

int generic_response_callback(void *cookie,
                              struct wally_registrant *registrant,
                              struct wally_table *table,
                              int64_t request_id,
                              int row_count)
{
    ZPATH_LOG(AL_NOTICE, "%10ld: Request complete, count = %ld",
             (long) request_id, (long) row_count);
    return WALLY_RESULT_NO_ERROR;
}


int sub_main(int argc, char *argv[])
{
    /* Parse arguments. */
    int result;
    void *wp_db;
    struct wally_fohh_client *fohh_client;
    int res;
    int i;
    int use_printf = 0;
    struct table_cleanup_parameter cleanup_param = {0};

    for (i = 1; i < argc; i++) {
        /* Test for all two-word arguments. */
        if (strcmp(argv[i], "-fully_loaded") == 0) {
            config_fully_loaded = 1;
        } else if (strcmp(argv[i], "-sqlite") == 0) {
            use_sqlt = 1;
            wally_server_name = "wally_server_sqlt";
        } else if (strcmp(argv[i], "-printf") == 0) {
            use_printf = 1;
        } else if (strcmp(argv[i], "-cleanup") == 0) {
            do_cleanup = 1;
        } else if(strcmp(argv[i], "-cleanup_test") == 0) {
            do_cleanup_test = 1;
        } else if(strcmp(argv[i], "-no_version") == 0) {
            dont_send_version = 1;
        } else if(strcmp(argv[i], "-is_wallyd") == 0) {
            run_as_wallyd = 1;
        } else {
            if ((i + 1) >= argc) {
                /* There is not a pair of words... */
                usage(argv[0], "Improper argument- may be missing second field: %s\n", argv[i]);
                /* Exits */
            }
            if (strcmp(argv[i], "-cert") == 0) {
                i++;
                config_cert = argv[i];
            } else if (strcmp(argv[i], "-root") == 0) {
                i++;
                config_root = argv[i];
            } else if (strcmp(argv[i], "-key") == 0) {
                i++;
                config_key = argv[i];
            } else if (strcmp(argv[i], "-remotehost") == 0) {
                i++;
                config_remote_host = argv[i];
            } else if (strcmp(argv[i], "-remoteport") == 0) {
                i++;
                config_remote_port_he = atoi(argv[i]);
            } else if (strcmp(argv[i], "-localdb") == 0) {
                i++;
                if (strcmp(argv[i], "NONE") == 0) {
                    config_local_db = NULL;
                } else {
                    config_local_db = argv[i];
                }
            } else if (strcmp(argv[i], "-register") == 0) {
                i++;
                config_register_count = atoi(argv[i]);
            } else if (strcmp(argv[i], "-exportport") == 0) {
                i++;
                config_export_port_he = atoi(argv[i]);
            } else if (strcmp(argv[i], "-debugport") == 0) {
                i++;
                config_debug_port_he = atoi(argv[i]);
            } else if (strcmp(argv[i], "-vmajor") == 0) {
                i++;
                VERSION_MAJOR_MIN = atoi(argv[i]);
            } else if (strcmp(argv[i], "-vminor") == 0) {
                i++;
                VERSION_MINOR_MIN = atoi(argv[i]);
            } else if (strcmp(argv[i], "-vpatch") == 0) {
                i++;
                VERSION_PATCH_MIN = atoi(argv[i]);
            } else if (strcmp(argv[i], "-logfile") == 0) {
                i++;
                if (strcmp(argv[i], "NONE") == 0) {
                    config_logfile = NULL;
                } else {
                    config_logfile = argv[i];
                }
            } else {
                usage(argv[0], "Unrecognized argument: %s\n", argv[i]);
                /* Exits */
            }
        }
    }

    /* for server, never use SQLite */
    if (!config_remote_host) use_sqlt = 0;

    struct zpath_simple_app_init_params app_params;
    zpath_simple_app_init_params_default(&app_params);
    app_params.instance_name = app_params.role_name = "wally_test_" ZPATH_VERSION;
    app_params.root_cert_file = config_root;
    app_params.cert_chain_file = config_cert;
    app_params.private_key_file = config_key;
    app_params.log_filename = config_logfile;
    app_params.debug_port = config_debug_port_he;
    app_params.debuglog = 1;

    result = zpath_simple_app_init(&app_params);
    if (result) {
        fprintf(stderr, "Error: Could not intialize\n");
        return 1;
    }
    if (use_printf) {
        argo_log_use_printf(1);
    } else {
        argo_log_set_max_text_per_line_per_s(1000000);
    }

    wally_debug =
        (!WALLY_DEBUG_RESULT_BIT) |
        (!WALLY_DEBUG_TABLE_BIT) |
        (WALLY_DEBUG_REGISTRATION_BIT) |
        (WALLY_DEBUG_ROW_BIT) |
        (!WALLY_DEBUG_POSTGRES_BIT) |
        (!WALLY_DEBUG_FOHH_CLIENT_ROW_BIT) |
        (!WALLY_DEBUG_POSTGRES_POLL_BIT) |
        (!WALLY_DEBUG_POSTGRES_FC_BIT) |
        (!WALLY_DEBUG_POSTGRES_CONN_BIT) |
        (!WALLY_DEBUG_POSTGRES_EVENT_BIT) |
        (WALLY_DEBUG_ROW_DETAIL_BIT) |
        (!WALLY_DEBUG_WRITE_ROW_BIT) |
        (!WALLY_DEBUG_POSTGRES_WRITE_BIT) |
        0;


    /**************************************************
     * Create server wally + origin(s)
     */
    wally_server = wally_create("wally_server",
                                0,
                                zpath_debug_wally_endpoints_init,
                                NULL,
                                NULL,
                                NULL);
    if (!wally_server) {
        ZPATH_LOG(AL_ERROR, "Could not intialize wally_server");
        return 1;
    }


    if (config_local_db) {
        ZPATH_LOG(AL_DEBUG, "Creating postgres origin: %s", config_local_db);
        if (!use_sqlt) {
            wp_db =
                wally_postgres_create(NULL,                /* Cookie for calling back into wally. */
                                      "localhost",         /* Host to connect to for database.    */
                                      "postgres",          /* User to connect as for database.    */
                                      config_local_db,     /* Database name to use to connect.    */
                                      config_local_db,     /* Name to give the thread running this DB. Chosen this way to be
                                                            * the same across all wallyd's. */
                                      NULL,                /* Password to use for connection. */
                                      4,                   /* Number of connections to use to connect to the DB. If making
                                                            * massive access to postgres, might want to make this larger. Larger
                                                            * values are only really needed for reading- writing is very slow and
                                                            * synchronous.  */
                                  config_remote_host ? 1 : 0,  /* Row writable! */
                                  config_remote_host ? 1 : 0,  /* Whether we can write to slave DB- really table_alterable. */
                                  config_remote_host ? 0 : 1,  /* Is this a true origin */
                                  config_export_port_he ? 0 : 1, /* Is this system the end of a chain (no children) */
                                  config_remote_host ? 0 : 1000000); /* Polling interval. Only poll if this postgres is true origin */
        } else {
            wp_db = wally_sqlt_create(config_local_db, /* dbname */
                                         config_local_db, /* thread_name */
                                         4,    /* nconns */
                                         1,    /* is_row_writable */
                                         0,    /* is_true_origin */
                                         0,    /* is_endpoint */
                                         0);   /* polling_interval_us */
        }

        if (!wp_db) {
            ZPATH_LOG(AL_ERROR, "Could not create postgres for server");
            return 1;
        }
        if (!((wallyd_remote_db = wally_add_origin_1(wally_server,
                                                     wally_server_name,
                                                     wp_db,
                                                     wally_db_register_for_index,
                                                     wally_db_deregister_for_index,
                                                     wally_db_set_cookie,
                                                     wally_db_get_status,
                                                     wally_db_add_table,
                                                     wally_db_set_min_sequence,
                                                     wally_db_dump_state,
                                                     (do_cleanup || do_cleanup_test)? wally_postgres_cleanup : NULL, NULL,
                                                     1)))) {
            ZPATH_LOG(AL_ERROR, "Could not add oring for %s", wally_server_name);
            return 1;
        }
    }

    if (config_remote_host) {
        ZPATH_LOG(AL_DEBUG, "Creating remote origin: %s:%d", config_remote_host, config_remote_port_he);
        fohh_client = wally_fohh_client_create(wally_server,
                                               NULL,
                                               config_remote_host,
                                               NULL,
                                               NULL,
                                               htons(config_remote_port_he),
                                               NULL);
        if (!fohh_client) {
            ZPATH_LOG(AL_ERROR, "Could not create fohh_client");
            return 1;
        }

        if (!((wallyd_local_db = wally_add_origin(wally_server,
                                                  "wally_server_fohh",
                                                  fohh_client,
                                                  wally_fohh_register_for_index,
                                                  wally_fohh_deregister_for_index,
                                                  wally_fohh_set_cookie,
                                                  wally_fohh_get_status,
                                                  wally_fohh_add_table,
                                                  NULL, // set_sequence
                                                  wally_fohh_dump_state,
                                                  0)))) {
            ZPATH_LOG(AL_ERROR, "Could not add fohh_client to middle");
            return 1;
        }
    }

    /* Tell wally_test about all our tables */
    for (i = 0; i < (sizeof(wts) / sizeof(struct wt)); i++) {
        struct argo_structure_description *desc;
        struct wally_table *table;

        desc = argo_register_global_structure(wts[i].table_name,
                                              wts[i].struct_size,
                                              wts[i].desc,
                                              wts[i].struct_field_count);
        if (!desc) {
            WALLY_LOG(AL_ERROR, "Could not register structure %s", wts[i].table_name);
            return WALLY_RESULT_ERR;
        }

        cleanup_param.state = 0;
        cleanup_param.max_cleanup_rows = 5;
        cleanup_param.max_scan_rows = 100;
        cleanup_param.cleanup_interval_us = 70000;
        cleanup_param.row_expire_sec = 30*24*3600;
        cleanup_param.min_seq_auto_update = 0;

        table = wally_table_create_named_db_1(wally_server,
                                              wts[i].writable_true_origin,
                                              desc->type,
                                              desc,
                                              NULL,
                                              NULL,
                                              1,
                                              1,
                                              config_fully_loaded,
                                              NULL,
                                              cleanup_param);
        if (!table) {
            WALLY_LOG(AL_ERROR, "Could not register create table- structure %s",
                       wts[i].table_name);
            return WALLY_RESULT_ERR;
        }

        if (do_cleanup_test) table->cleanup_unit_test = 1;

        if (config_fully_loaded) {
            struct wally_index_column *col;
            col = wally_table_get_index(table, "");
            if (!col) {
                WALLY_LOG(AL_ERROR, "Could not get full table index for %s", wts[i].table_name);
                return WALLY_RESULT_ERR;
            }
            //WALLYD_LOG(AL_DEBUG, "Fully loaded %s: Registering for rows", wts[i].table_name);
            res = wally_table_register_for_row(NULL,
                                               col,
                                               NULL,
                                               0,
                                               0,
                                               0,
                                               0,
                                               0,
                                               0,
                                               fetch_callback,
                                               wts[i].table_name);
            if (res) {
                if (res != WALLY_RESULT_ASYNCHRONOUS) {
                    WALLY_LOG(AL_ERROR, "Could not register for all rows from %s: %s", wts[i].table_name, wally_error_strings[res]);
                    return WALLY_RESULT_ERR;
                } else {
                    int32_t current_count = __sync_add_and_fetch_4(&(responses_waiting), 1);
                    WALLY_LOG(AL_NOTICE, "Waiting for %d tables to be read...", (int) current_count);
                }
                result = WALLY_RESULT_NO_ERROR;
            }
        }
    }

    if (config_export_port_he) {
        ZPATH_LOG(AL_DEBUG, "Exporting wally via port %d", config_export_port_he);
        if (!wally_fohh_server_create(wally_server,
                                      argo_serialize_binary,
                                      fohh_connection_style_argo,
                                      0,
                                      NULL, /* Bind to all IPs */
                                      htons(config_export_port_he),
                                      config_root,
                                      config_cert,
                                      config_key,
                                      1)) {
            ZPATH_LOG(AL_ERROR, "Could not create fohh_server for wally_server");
            return 1;
        }
    }

    res = zpath_debug_wally_add(wally_server, 0);
    if (res) {
        ZPATH_LOG(AL_ERROR, "Could not add wally for debugging");
        return res;
    }

    if (config_register_count) {
        struct argo_structure_description *desc = argo_get_structure_description("wally_test");
        struct wally_table *table;
        struct wally_index_column *column;

        int64_t regs = 0;
        int64_t deregs = 0;

        if (!desc) {
            WALLY_LOG(AL_ERROR, "Could not get wally_test description");
        } else {
            table = wally_table_get(wally_server, "wally_test");
            if (!table) {
                WALLY_LOG(AL_ERROR, "Could not get wally_test table");
            } else {
                column = wally_table_get_index(table, "ix_num");
                if (!column) {
                    WALLY_LOG(AL_ERROR, "Could not get wally_test/ix_num column");
                } else {
                    int *state = WALLY_CALLOC(sizeof(*state) * config_register_count);
                    /* 0 = never registered, 1 = registered, 2 = deregistered */
                    int64_t key;
                    for (i = 0; i < 20; i++) {
                        int j;
                        for (j = 0; j < config_register_count / 10; j++) {
                            key = random() % config_register_count;
                            if (state[key] == 1) {
                                /* Deregister */
                                res = wally_table_deregister_for_row(NULL,
                                                                     column,
                                                                     &key,
                                                                     sizeof(key));
                                state[key] = 2;
                                deregs++;
                            } else {
                                /* Register */
                                res = wally_table_register_for_row(NULL,
                                                                   column,
                                                                   &key,
                                                                   sizeof(key),
                                                                   key,
                                                                   0,
                                                                   0,
                                                                   0,
                                                                   0,
                                                                   NULL,
                                                                   NULL);
                                state[key] = 1;
                                regs++;
                            }
                            if (res && (res != WALLY_RESULT_ASYNCHRONOUS)) {
                                WALLY_LOG(AL_NOTICE, "Received %s from (de)register_for_row", wally_error_strings[res]);
                            }
                        }
                        WALLY_LOG(AL_NOTICE, "Performed %d operations... Sleeping 1s...", (i + 1) * config_register_count / 10);
                        sleep(1);
                    }
                    int total_reg = 0;
                    int total_unreg = 0;
                    for (i = 0; i < config_register_count; i++) {
                        if (state[i] == 1) total_reg++;
                        if (state[i] == 2) total_unreg++;
                    }
                    WALLY_LOG(AL_NOTICE, "Done: Total reg = %d, Total unreg = %d, Total touched = %d, Total untouched = %d, registrations performed = %ld, deregistrations performed = %ld",
                              total_reg, total_unreg, total_reg + total_unreg, config_register_count - (total_reg + total_unreg), (long) regs, (long) deregs);
                }
            }
        }
    }

    if (run_as_wallyd) {
        wally_set_wallyd(wally_server);
        zpath_config_override_monitor_int(WALLY_FEATURE_DROP_INCOMPATIBLE_VERSION,
                                          &wally_server->drop_incompatible_version,
                                          NULL,
                                          DEFAULT_DROP_INCOMPATIBLE_VERSION,
                                          (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                          (int64_t)0);
        //wally_set_drop_incompatible_version_check(wally_server, wally_config_override_check_drop_on_imcompatible_version_enabled);
        zpath_config_override_init(wally_server, 0, 0, config_component_wally);
    }

    ZPATH_LOG(AL_NOTICE, "Initialized");

    while(1) sleep(1);
}

#ifdef SIGUSR2
static void
wally_test_sig_hdl (int signo)
{
    if (SIGUSR2 == signo) {
        printf("received SIGUSR2 - expected only to help address sanitizer\n");
    } else {
        printf("received signal(%d) - UNEXPECTED handle\n", signo);
    }

    exit(1);
}
#endif


int main(int argc, char *argv[])
{
    int res;

#ifdef SIGUSR2
	if (SIG_ERR == signal(SIGUSR2, wally_test_sig_hdl)) {
        printf("Error: Could not intialize - signal hdl failure\n");
        exit(1);
    }
#endif

    res = sub_main(argc, argv);
    sleep(1);
    return res;
}
