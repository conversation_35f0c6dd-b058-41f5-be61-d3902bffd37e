/*
 * zpn_npgatewayd.c. Copyright (C) 2024 Zscaler, Inc. All Rights Reserved.
 *
 * NP Gateway allocation helper
 *
 */

#include "zpn_npgatewayd/zpn_npgateway_alloc.h"
#include "zpn_npgatewayd/zpn_npgateway_logging.h"
#include "zpath_lib/zpath_debug.h"

struct zpath_allocator np_gateway_allocator = ZPATH_ALLOCATOR_INIT("np_gateway");

uint64_t npgwd_debug_log_catch_defaults =
         (NPGWD_DEBUG_REDUNDANCY_IDX) |
         0;

uint64_t npgwd_debug_log = 0;
const char *npgwd_debug_log_names[] = NPGWD_DEBUG_LOG_NAMES;

int zpn_npgateway_debug_init()
{
     int res;

    res = zpath_debug_add_flag(&npgwd_debug_log, npgwd_debug_log_catch_defaults, "npgwd", npgwd_debug_log_names);
    return res;
}
