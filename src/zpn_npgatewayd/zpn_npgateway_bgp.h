/*
 * np_gateway_bgp.h. Copyright (C) 2025 Zscaler Inc, All Rights Reserved
 */

#ifndef _ZPN_NPGATEWAY_BGP_H_
#define _ZPN_NPGATEWAY_BGP_H_


void np_gateway_redundancy_feature_status_toggled(int64_t customer_gid, int enabled);

void zpn_npgateway_bgp_load_config();

void zpn_npgateway_bgp_state_init(struct wally *wally, int64_t customer_gid);

void zpn_npgateway_bgp_state_destroy();

int np_gateway_get_bgp_config_mode();

int np_bgp_gateway_reload_frr_conf(int force_regenerate);

int np_gateway_get_bgp_peer_info_from_neighbor_ip(const char *neighbor_ip, int64_t neighbor_asn,
                                                         int64_t *peer_gid, int *peer_type);

void zpn_npgateway_bgp_set_np_tenant_gateway_gid(int64_t gateway_gid);

int zpn_npgateway_bgp_client_subnet_handler(struct argo_object *previous_row, struct argo_object *row);

void np_gateway_bgp_set_redundancy_feature(int enabled);

void np_gateway_bgp_set_redundant_mode(int enabled);

int zpn_npgateway_bgp_init();

#endif /* _ZPN_NPGATEWAY_BGP_H_ */
