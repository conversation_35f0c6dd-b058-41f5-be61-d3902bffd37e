argo_parse_files(
    INPUT_FILES zpn_npgateway_stats.h zpn_npgateway_common.h zpn_npgateway_creds.h
    OUTPUT_DIR ${CMAKE_CURRENT_BINARY_DIR}
    OUTPUT_FILES_VAR generated_headers
)

add_executable(
    zpn_npgatewayd
    zpn_npgateway_alloc.c
    zpn_npgateway_bgp.c
    zpn_npgateway_common.c
    zpn_npgateway_error.c
    zpn_npgateway_admin_probe.c
    zpn_npgateway_health_server.c
    zpn_npgateway_operator.c
    zpn_npgateway_stats.c
    zpn_npgateway_wally_client.c
    zpn_npgateway_creds.c
    zpn_npgatewayd.c
    zpn_npgateway_config_override_desc.c
    ${generated_headers}
)
target_link_libraries(zpn_npgatewayd PRIVATE npwg_lib zpn zpath_app)

find_program(wg wg REQUIRED)
find_program(wg-quick wg-quick REQUIRED)

add_rpm(
    NAME zpn-npgatewayd
    SPEC rpm/zpn-npgatewayd.spec
    MAKEFILE Makefile.rpm.zpn-npgatewayd
    FILES
        zpn_npgatewayd
        rpm/zpn_npgatewayd.service
        rpm/10-zscaler-user
        rpm/64-zscaler.preset
        rpm/npwg0.conf
        rpm/asan.options
        ${wg}
        ${wg-quick}
        ../rpm/53-maps.conf
        license.txt
)

add_subdirectory(bootstrappers)
