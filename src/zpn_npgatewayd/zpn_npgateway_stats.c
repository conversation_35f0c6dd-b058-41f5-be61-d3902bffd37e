/*
 * zpn_npgateway_stats.c. Copyright (C) 2024 Zscaler, Inc. All Rights Reserved.
 * NP Gateway stats
 */

#include "zpath_misc/zpath_version.h"
#include "argo/argo_log.h"
#include "zpath_misc/zsysinfo.h"
#include "zpn/zpn_lib.h"
#include "zpn/zpn_system.h"
#include "zpath_lib/zpath_system_stats.h"
#include "npwg_lib/npwg_provider.h"

#include "zpn_npgatewayd/zpn_npgateway_logging.h"
#include "zpn_npgatewayd/zpn_npgateway_stats.h"
#include "zpn_npgatewayd/zpn_npgateway_stats_compiled.h"
#include "zpn_npgatewayd/zpn_npgateway_common.h"

static int npgateway_available_cpus = 0;

static struct event_base *ev_base = NULL;
static struct event *ev_timer = NULL;
static uint64_t monitor_ticks = 0;
static pthread_t stats_thread;

/* updated only by the monitor thread */
static uint16_t cpu_util = 0;
static uint64_t cpu_steal = 0;
static uint16_t cpu_steal_perc = 0;
static uint16_t mem_util = 0;
static uint32_t sys_uptime = 0;
static uint64_t mem_total = 0;

static struct zpn_npgateway_stats zpn_npgateway_stats = {0};
static struct argo_structure_description *zpn_npgateway_sys_stats_description = NULL;
static struct argo_structure_description *zpn_npgateway_wireguard_stats_description = NULL;
struct argo_structure_description *zpn_npgateway_common_stats_description = NULL;

static void zpn_npgateway_stats_load_monitor_free()
{
    NPGWD_LOG(AL_DEBUG, "Killing monitor thread");

    if (ev_timer) {
        event_free(ev_timer);
        ev_timer = NULL;
    }

    if (ev_base) {
        event_base_free(ev_base);
        ev_base = NULL;
    }
}

static inline void zpn_npgateway_stats_system_monitor()
{
    zpath_system_monitor_fd();
    zpath_system_monitor_socket();
}

static int zpn_npgateway_wireguard_stats_fill()
{
    ZPATH_RWLOCK_RDLOCK(&(g_npwg_stats_wg.rw_lock), __FILE__, __LINE__);

    struct zpn_npgateway_wireguard_stats *stats = &zpn_npgateway_stats.zpn_npgateway_wireguard_stats;

    stats->this_npgateway_listen_port = g_npwg_stats_wg.this_wireguard_listen_port;

    stats->peers_count_npgateways = g_npwg_stats_wg.peers_count_npgateways;
    stats->peers_count_clients = g_npwg_stats_wg.peers_count_clients;
    stats->peers_count_connectors = g_npwg_stats_wg.peers_count_connectors;
    stats->peers_count_invalid = g_npwg_stats_wg.peers_count_invalid;
    stats->peers_count_total = g_npwg_stats_wg.peers_count_total;
    stats->peers_count_total_max = g_npwg_stats_wg.peers_count_total_max;

    stats->allowed_ips_npgateways_cnt = g_npwg_stats_wg.allowed_ips_npgateways_cnt;
    stats->allowed_ips_clients_cnt = g_npwg_stats_wg.allowed_ips_clients_cnt;
    stats->allowed_ips_connectors_cnt = g_npwg_stats_wg.allowed_ips_connectors_cnt;
    stats->allowed_ips_invalids_cnt = g_npwg_stats_wg.allowed_ips_invalids_cnt;
    stats->allowed_ips_total_cnt = g_npwg_stats_wg.allowed_ips_total_cnt;
    stats->allowed_ips_total_cnt_max = g_npwg_stats_wg.allowed_ips_total_cnt_max;

    stats->rx_npgateways_bytes_per_sec = g_npwg_stats_wg.rx_npgateways_bytes_per_sec;
    stats->tx_npgateways_bytes_per_sec = g_npwg_stats_wg.tx_npgateways_bytes_per_sec;
    stats->rx_clients_bytes_per_sec = g_npwg_stats_wg.rx_clients_bytes_per_sec;
    stats->tx_clients_bytes_per_sec = g_npwg_stats_wg.tx_clients_bytes_per_sec;
    stats->rx_connectors_bytes_per_sec = g_npwg_stats_wg.rx_connectors_bytes_per_sec;
    stats->tx_connectors_bytes_per_sec = g_npwg_stats_wg.tx_connectors_bytes_per_sec;
    stats->rx_invalids_bytes_per_sec = g_npwg_stats_wg.rx_invalids_bytes_per_sec;
    stats->tx_invalids_bytes_per_sec = g_npwg_stats_wg.tx_invalids_bytes_per_sec;
    stats->rx_total_bytes_per_sec = g_npwg_stats_wg.rx_total_bytes_per_sec;
    stats->tx_total_bytes_per_sec = g_npwg_stats_wg.tx_total_bytes_per_sec;

    stats->rx_npgateways_bytes = g_npwg_stats_wg.rx_npgateways_bytes;
    stats->tx_npgateways_bytes = g_npwg_stats_wg.tx_npgateways_bytes;
    stats->rx_clients_bytes = g_npwg_stats_wg.rx_clients_bytes;
    stats->tx_clients_bytes = g_npwg_stats_wg.tx_clients_bytes;
    stats->rx_connectors_bytes = g_npwg_stats_wg.rx_connectors_bytes;
    stats->tx_connectors_bytes = g_npwg_stats_wg.tx_connectors_bytes;
    stats->rx_invalids_bytes = g_npwg_stats_wg.rx_invalids_bytes;
    stats->tx_invalids_bytes = g_npwg_stats_wg.tx_invalids_bytes;
    stats->rx_total_bytes = g_npwg_stats_wg.rx_total_bytes;
    stats->tx_total_bytes = g_npwg_stats_wg.tx_total_bytes;

    stats->handshake_clients_hist_0 = g_npwg_stats_wg.handshake_clients_hist_0;
    stats->handshake_clients_hist_1 = g_npwg_stats_wg.handshake_clients_hist_1;
    stats->handshake_clients_hist_2 = g_npwg_stats_wg.handshake_clients_hist_2;
    stats->handshake_connectors_hist_0 = g_npwg_stats_wg.handshake_connectors_hist_0;
    stats->handshake_connectors_hist_1 = g_npwg_stats_wg.handshake_connectors_hist_1;
    stats->handshake_connectors_hist_2 = g_npwg_stats_wg.handshake_connectors_hist_2;
    stats->handshake_npgateways_hist_0 = g_npwg_stats_wg.handshake_npgateways_hist_0;
    stats->handshake_npgateways_hist_1 = g_npwg_stats_wg.handshake_npgateways_hist_1;
    stats->handshake_npgateways_hist_2 = g_npwg_stats_wg.handshake_npgateways_hist_2;

    stats->persistent_keepalive_off_count = g_npwg_stats_wg.persistent_keepalive_off_count;

    stats->npgateway_stats_monitor_cnt = g_npwg_stats_wg.npgateway_stats_monitor_cnt;
    stats->npgateway_stats_monitor_error_cnt = g_npwg_stats_wg.npgateway_stats_monitor_error_cnt;

    stats->allowed_ips_clients_get_error_cnt = g_npwg_stats_wg.allowed_ips_clients_get_error_cnt;
    stats->allowed_ips_connectors_get_error_cnt = g_npwg_stats_wg.allowed_ips_connectors_get_error_cnt;
    stats->allowed_ips_npgateways_get_error_cnt = g_npwg_stats_wg.allowed_ips_npgateways_get_error_cnt;
    stats->peer_type_get_err_cnt = g_npwg_stats_wg.peer_type_get_err_cnt;
    stats->peer_type_get_err_total_cnt = g_npwg_stats_wg.peer_type_get_err_total_cnt;

    ZPATH_RWLOCK_UNLOCK(&(g_npwg_stats_wg.rw_lock), __FILE__, __LINE__);
    return ZPN_RESULT_NO_ERROR;
}

static int zpn_npgateway_common_stats_fill()
{
    struct zpn_npgateway_common_stats *stats = &zpn_npgateway_stats.zpn_npgateway_common_stats;
    *stats = operate_stats;

    return ZPN_RESULT_NO_ERROR;
}

static void zpn_npgateway_stats_load_get_cpu_util()
{
    struct zpath_system_cpu_stats_util cpu_stats_util = {0};
    struct zpn_npgateway_sys_stats *sys_stats = &zpn_npgateway_stats.zpn_npgateway_sys_stats;

    cpu_stats_util.nonidle = sys_stats->nonidle;
    cpu_stats_util.idle = sys_stats->idle;
    cpu_stats_util.cpu_steal = cpu_steal;

    const int res = zpath_system_get_cpu_stats(&cpu_stats_util);
    if (res) {
        NPGWD_LOG(AL_ERROR, "Could not get system cpu stats, err : %s", zpath_result_string(res));
        return;
    }

    sys_stats->cpu_util = cpu_stats_util.cpu_util;
    sys_stats->nonidle = cpu_stats_util.nonidle;
    sys_stats->idle = cpu_stats_util.idle;
    cpu_steal_perc = cpu_stats_util.cpu_steal_perc;
    cpu_steal = cpu_stats_util.cpu_steal;
}

void zpn_npgateway_monitor_wireguard_log()
{
#ifdef __linux__
    npwg_provider_monitor_wg_log();
#endif
}

static void zpn_npgateway_stats_load_sys_stats()
{
    struct zpn_npgateway_sys_stats *sys_stats = &zpn_npgateway_stats.zpn_npgateway_sys_stats;

    zpn_npgateway_stats_load_get_cpu_util();
    if (zpath_system_get_system_and_process_memory_util_percentage(&sys_stats->system_mem_util, &sys_stats->process_mem_util)) {
        sys_stats->system_mem_util = 0;
        sys_stats->process_mem_util = 0;
        NPGWD_LOG(AL_WARNING, "Could not get the memory usage info");
    }
    zpath_system_get_fd_util(&sys_stats->sys_fd_util, &sys_stats->proc_fd_util);

    NPGWD_LOG(AL_INFO, "Ver=%s:Mem(System|Process)=%d%%|%d%%:CPU(Util)=%d%%:FD(System|Process)=%d%%|%d%%",
               ZPATH_VERSION,
               sys_stats->system_mem_util,
               sys_stats->process_mem_util,
               sys_stats->cpu_util,
               sys_stats->sys_fd_util,
               sys_stats->proc_fd_util);
}

static void zpn_npgateway_stats_load_monitor(evutil_socket_t sock __attribute__((unused)),
                                             short flags __attribute__((unused)),
                                             void *cookie)
{
    struct zthread_info *zthread_arg = cookie;
    uint64_t nonidle = 0, idle = 0;

    zthread_heartbeat(zthread_arg);
    monitor_ticks++;

    if (zsysinfo(&cpu_util, &mem_util, &sys_uptime, &nonidle, &idle, &mem_total) == ZSYSINFO_SUCCESS) {
        NPGWD_LOG(AL_DEBUG, "cpu_util = %d%%, mem_util = %d%%", cpu_util, mem_util);
    } else {
        NPGWD_LOG(AL_WARNING, "Could not get sysinfo");
    }

    zpn_npgateway_stats_system_monitor();
    zpn_npgateway_stats_load_sys_stats();
    zpn_npgateway_monitor_wireguard_log();
}

static int zpn_npgateway_load_dump(struct zpath_debug_state *request_state,
                                   const char **query_values,
                                   int query_value_count, void *cookie)
{
    ZDP("\n");
    if (query_values[0] && !strcasecmp(query_values[0], "json")) {
        ZDP("{\"cpu_util_percentage\": %"PRId16", \"mem_util_percentage\": %"PRId16","
            "\"cpu_nonidle\": %"PRIu64", \"cpu_idle\": %"PRIu64","
            "\"sys_fd_util_percentage\": %d, \"proc_fd_util_percentage\": %"PRIu16","
            "\"system_fd_in_use\": %ld, \"process_fd_in_use\": %ld,"
            "\"num_system_tcpv4_socket_inuse\": %ld, \"num_system_tcpv6_socket_inuse\": %ld,"
            "\"num_system_udpv4_socket_inuse\": %ld, \"num_system_udpv6_socket_inuse\": %ld",
            zpn_npgateway_stats.zpn_npgateway_sys_stats.cpu_util, zpn_npgateway_stats.zpn_npgateway_sys_stats.system_mem_util,
            zpn_npgateway_stats.zpn_npgateway_sys_stats.idle, zpn_npgateway_stats.zpn_npgateway_sys_stats.nonidle,
            zpn_npgateway_stats.zpn_npgateway_sys_stats.sys_fd_util, zpn_npgateway_stats.zpn_npgateway_sys_stats.proc_fd_util,
            (long) zpn_npgateway_stats.zpn_npgateway_fd_stats.system_fd_in_use,
            (long) zpn_npgateway_stats.zpn_npgateway_fd_stats.process_fd_in_use,
            (long) zpn_npgateway_stats.zpn_npgateway_sock_stats.num_system_tcpv4_socket_inuse,
            (long) zpn_npgateway_stats.zpn_npgateway_sock_stats.num_system_tcpv6_socket_inuse,
            (long) zpn_npgateway_stats.zpn_npgateway_sock_stats.num_system_udpv4_socket_inuse,
            (long) zpn_npgateway_stats.zpn_npgateway_sock_stats.num_system_udpv6_socket_inuse);
        ZDP("}\n"); /* terminate the bracket */
    } else {
        ZDP("NP Gateway System Stats: \n");
        ZDP("===================\n");
        ZDP("cpu_util_percentage: %"PRIu16", mem_util_percentage: %"PRIu16"\n",
            zpn_npgateway_stats.zpn_npgateway_sys_stats.cpu_util, zpn_npgateway_stats.zpn_npgateway_sys_stats.system_mem_util);
        ZDP("cpu nonidle: %"PRIu64"%% idle: %"PRIu64"%%\n",
            zpn_npgateway_stats.zpn_npgateway_sys_stats.nonidle, zpn_npgateway_stats.zpn_npgateway_sys_stats.idle);
        ZDP("sys_fd_util_percentage: %d, proc_fd_util_percentage: %d\n",
            zpn_npgateway_stats.zpn_npgateway_sys_stats.sys_fd_util, zpn_npgateway_stats.zpn_npgateway_sys_stats.proc_fd_util);
        ZDP("num_system_tcpv4_socket_inuse: %ld, num_system_tcpv6_socket_inuse: %ld, num_system_udpv4_socket_inuse: %ld, num_system_udpv6_socket_inuse: %ld",
            (long) zpn_npgateway_stats.zpn_npgateway_sock_stats.num_system_tcpv4_socket_inuse,
            (long) zpn_npgateway_stats.zpn_npgateway_sock_stats.num_system_tcpv6_socket_inuse,
            (long) zpn_npgateway_stats.zpn_npgateway_sock_stats.num_system_udpv4_socket_inuse,
            (long) zpn_npgateway_stats.zpn_npgateway_sock_stats.num_system_udpv6_socket_inuse);
        ZDP("\n");
    }
    return ZPN_RESULT_NO_ERROR;
}

static int zpn_npgateway_wg_dump(struct zpath_debug_state *request_state,
                                 const char **query_values,
                                 int query_value_count, void *cookie)
{

    const char *format = ZPN_NPGATEWAY_WIREGUARD_STATS_FORMAT_TEXT;


    if (query_values[0] && !strcasecmp(query_values[0], "json")) {
        format = ZPN_NPGATEWAY_WIREGUARD_STATS_FORMAT_JSON;
    }

    ZPATH_RWLOCK_RDLOCK(&(g_npwg_stats_wg.rw_lock), __FILE__, __LINE__);
    // stats can only be updated after getting above lock
    struct zpn_npgateway_wireguard_stats *stats = &zpn_npgateway_stats.zpn_npgateway_wireguard_stats;

    ZDP(format,
        stats->this_npgateway_listen_port,
        stats->peers_count_total,
        stats->peers_count_total_max,
        stats->peers_count_npgateways,
        stats->peers_count_clients,
        stats->peers_count_connectors,
        stats->allowed_ips_total_cnt,
        stats->allowed_ips_total_cnt_max,
        stats->allowed_ips_npgateways_cnt,
        stats->allowed_ips_clients_cnt,
        stats->allowed_ips_connectors_cnt,
        stats->rx_total_bytes,
        stats->tx_total_bytes,
        stats->rx_npgateways_bytes,
        stats->tx_npgateways_bytes,
        stats->rx_clients_bytes,
        stats->tx_clients_bytes,
        stats->rx_connectors_bytes,
        stats->tx_connectors_bytes,
        stats->rx_total_bytes_per_sec,
        stats->tx_total_bytes_per_sec,
        stats->rx_npgateways_bytes_per_sec,
        stats->tx_npgateways_bytes_per_sec,
        stats->rx_clients_bytes_per_sec,
        stats->tx_clients_bytes_per_sec,
        stats->rx_connectors_bytes_per_sec,
        stats->tx_connectors_bytes_per_sec,
        stats->handshake_clients_hist_0,
        stats->handshake_clients_hist_1,
        stats->handshake_clients_hist_2,
        stats->handshake_connectors_hist_0,
        stats->handshake_connectors_hist_1,
        stats->handshake_connectors_hist_2,
        stats->handshake_npgateways_hist_0,
        stats->handshake_npgateways_hist_1,
        stats->handshake_npgateways_hist_2,
        stats->persistent_keepalive_off_count,
        stats->npgateway_stats_monitor_cnt,
        stats->npgateway_stats_monitor_error_cnt,
        stats->allowed_ips_clients_get_error_cnt,
        stats->allowed_ips_connectors_get_error_cnt,
        stats->allowed_ips_npgateways_get_error_cnt,
        stats->peers_count_invalid,
        stats->allowed_ips_invalids_cnt,
        stats->rx_invalids_bytes,
        stats->tx_invalids_bytes,
        stats->rx_invalids_bytes_per_sec,
        stats->tx_invalids_bytes_per_sec,
        stats->peer_type_get_err_cnt,
        stats->peer_type_get_err_total_cnt);

    ZPATH_RWLOCK_UNLOCK(&(g_npwg_stats_wg.rw_lock), __FILE__, __LINE__);

    return ZPN_RESULT_NO_ERROR;
}

static int zpn_npgateway_common_stats_dump(struct zpath_debug_state *request_state,
                                           const char **query_values,
                                           int query_value_count, void *cookie)
{
    ZDP("\n");
    if (query_values[0] && !strcasecmp(query_values[0], "json")) {
        ZDP("{\"WG MTU\": %"PRId64" ",
            zpn_npgateway_stats.zpn_npgateway_common_stats.mtu);
        ZDP("}\n"); /* terminate the bracket */
    } else {
        ZDP("NP Gateway Common Stats: \n");
        ZDP("===================\n");
        ZDP("WG MTU: %"PRId64" \n", zpn_npgateway_stats.zpn_npgateway_common_stats.mtu);
        ZDP("\n");
    }
    return ZPN_RESULT_NO_ERROR;
}


static void *zpn_npgateway_stats_load_monitor_thread(struct zthread_info *zthread_arg,
                                                     void *cookie __attribute__((unused)))
{
    NPGWD_LOG(AL_DEBUG, "Load monitor thread started running");

    ev_timer = event_new(ev_base,
                         -1,
                         EV_PERSIST,
                         zpn_npgateway_stats_load_monitor,
                         zthread_arg);

    if (!ev_timer) {
        NPGWD_LOG(AL_CRITICAL, "Failed to create npgateway load event");
        return NULL;
    }

    struct timeval tv = { .tv_sec = NPGATEWAY_MONITOR_INTERVAL_S, .tv_usec = 0 };
    if (event_add(ev_timer, &tv) != 0) {
        NPGWD_LOG(AL_CRITICAL, "Failed to initialize npgateway load event timer");
        return NULL;
    }

    zpn_npgateway_stats_load_monitor(-1, 0, NULL);
    zevent_base_dispatch(ev_base);

    return NULL;
}


int zpn_npgateway_stats_load_monitor_init()
{
    ev_base = event_base_new();
    if (!ev_base) {
        return ZPN_RESULT_ERR;
    }

    const int res = zthread_create(&stats_thread,
                                   zpn_npgateway_stats_load_monitor_thread,
                                   NULL,
                                   "zpn_npgateway_stats_load_stats_monitor",
                                   NPWG_STATS_WG_MONITOR_HEARTBEAT_TIMEOUT_S,
                                   NPGATEWAY_MONITOR_THREAD_STACK_SIZE,
                                   MINUTE_TO_US(5),
                                   NULL);

    if (res) {
        zpn_npgateway_stats_load_monitor_free();
        return ZPN_RESULT_ERR;
    }

    return ZPN_RESULT_NO_ERROR;
}


int zpn_npgateway_stats_init()
{
    if ((npgateway_available_cpus = sysconf(_SC_NPROCESSORS_ONLN)) == -1) {
        NPGWD_LOG(AL_ERROR, "failed to read number of available cpus - %s", strerror(errno));
    }

    zpn_npgateway_wireguard_stats_description = argo_register_global_structure(ZPN_NPGATEWAY_WIREGUARD_STATS_HELPER);
    if (!zpn_npgateway_wireguard_stats_description) {
        return ZPN_RESULT_ERR;
    }

    zpn_npgateway_sys_stats_description = argo_register_global_structure(ZPN_NPGATEWAY_SYS_STATS_HELPER);
    if (!zpn_npgateway_sys_stats_description) {
        return ZPN_RESULT_ERR;
    }

    zpn_npgateway_common_stats_description = argo_register_global_structure(ZPN_NPGATEWAY_COMMON_STATS_HELPER);
    if (!zpn_npgateway_common_stats_description) {
        return ZPN_RESULT_ERR;
    }

    /* Initialize fd and socket statistics. */
    struct argo_log_registered_structure *s;

    s = argo_log_register_structure(argo_log_get("statistics_log"),
                                   "zpn_npgateway_sys_stats",
                                    AL_INFO,
                                    MINUTE_TO_US(5), /* 5 mins interval */
                                    zpn_npgateway_sys_stats_description,
                                    &zpn_npgateway_stats.zpn_npgateway_sys_stats,
                                    1,
                                    NULL,
                                    NULL);

    if (!s) {
        NPGWD_LOG(AL_CRITICAL, "Could not register npgateway sys stats- statistics_log not initialized?");
        return ZPN_RESULT_ERR;
    }

    s = argo_log_register_structure(argo_log_get("statistics_log"),
                                    "zpn_npgateway_fd_stats",
                                    AL_INFO,
                                    MINUTE_TO_US(5), /* 5 mins interval */
                                    zpath_system_fd_stats_description,
                                    &zpn_npgateway_stats.zpn_npgateway_fd_stats,
                                    1,
                                    zpn_system_fd_stats_fill,
                                    NULL);
    if (!s) {
        NPGWD_LOG(AL_CRITICAL, "Could not register npgateway fd stats- statistics_log not initialized?");
        return ZPN_RESULT_ERR;
    }

    s = argo_log_register_structure(argo_log_get("statistics_log"),
                                    "zpn_npgateway_sock_stats",
                                    AL_INFO,
                                    MINUTE_TO_US(5), /* 5 mins interval */
                                    zpath_system_sock_stats_description,
                                    &zpn_npgateway_stats.zpn_npgateway_sock_stats,
                                    1,
                                    zpn_system_sock_stats_fill,
                                    NULL);
    if (!s) {
        NPGWD_LOG(AL_CRITICAL, "Could not register npgateway sock stats- statistics_log not initialized?");
        return ZPN_RESULT_ERR;
    }

    s = argo_log_register_structure(argo_log_get("statistics_log"),
                                    "zpn_npgateway_cpu_stats",
                                    AL_INFO,
                                    MINUTE_TO_US(5), /* 5 mins interval */
                                    zpath_system_cpu_stats_description,
                                    &zpn_npgateway_stats.zpn_npgateway_cpu_stats,
                                    1,
                                    zpn_system_cpu_stats_fill,
                                    &npgateway_available_cpus);
    if (!s) {
        NPGWD_LOG(AL_CRITICAL, "Could not register npgateway cpu stats- statistics_log not initialized?");
        return ZPN_RESULT_ERR;
    }

    s = argo_log_register_structure(argo_log_get("statistics_log"),
                                    "zpn_npgateway_disk_stats",
                                    AL_INFO,
                                    MINUTE_TO_US(5), /* 5 mins interval */
                                    zpath_system_disk_stats_description,
                                    &zpn_npgateway_stats.zpn_npgateway_disk_stats,
                                    1,
                                    zpn_system_disk_stats_fill,
                                    NULL);
    if (!s) {
        NPGWD_LOG(AL_CRITICAL, "Could not register npgateway disk stats- statistics_log not initialized?");
        return ZPN_RESULT_ERR;
    }

    s = argo_log_register_structure(argo_log_get("statistics_log"),
                                    "zpn_npgateway_memory_stats",
                                    AL_INFO,
                                    MINUTE_TO_US(5), /* 5 mins interval */
                                    zpath_system_memory_stats_description,
                                    &zpn_npgateway_stats.zpn_npgateway_memory_stats,
                                    1,
                                    zpn_system_memory_stats_fill,
                                    NULL);
    if (!s) {
        NPGWD_LOG(AL_CRITICAL, "Could not register npgateway memory stats- statistics_log not initialized?");
        return ZPN_RESULT_ERR;
    }

    s = argo_log_register_structure(argo_log_get("statistics_log"),
                                    "zpn_npgateway_wireguard_stats",
                                    AL_INFO,
                                    MINUTE_TO_US(5), /* 5 mins interval */
                                    zpn_npgateway_wireguard_stats_description,
                                    &zpn_npgateway_stats.zpn_npgateway_wireguard_stats,
                                    1,
                                    zpn_npgateway_wireguard_stats_fill,
                                    NULL);
    if (!s) {
        NPGWD_LOG(AL_CRITICAL, "Could not register npgateway wireguard stats- statistics_log not initialized?");
        return ZPN_RESULT_ERR;
    }

    s = argo_log_register_structure(argo_log_get("statistics_log"),
                                    "zpn_npgateway_common_stats",
                                    AL_INFO,
                                    MINUTE_TO_US(5), /* 5 mins interval */
                                    zpn_npgateway_common_stats_description,
                                    &zpn_npgateway_stats.zpn_npgateway_common_stats,
                                    1,
                                    zpn_npgateway_common_stats_fill,
                                    NULL);
    if (!s) {
        NPGWD_LOG(AL_CRITICAL, "Could not register npgateway common stats- statistics_log not initialized?");
        return ZPN_RESULT_ERR;
    }

    zpath_system_stats_mutex_init();

    int res = zpath_debug_add_read_command("NP gateway load",
                                           "/npgateway/load",
                                           zpn_npgateway_load_dump,
                                           NULL,
                                           "format",  "<optional> format=json, or plain text otherwise",
                                           NULL);
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not init /zpn/npgateway/load debug command");
        return ZPN_RESULT_ERR;
    }

    res = zpath_debug_add_read_command("NP gateway wireguard stats",
                                       "/npgateway/wg/stats",
                                       zpn_npgateway_wg_dump,
                                       NULL,
                                       "format",  "<optional> format=json, or plain text otherwise",
                                       NULL);
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not init /zpn/npgateway/wg/stats debug command");
        return ZPN_RESULT_ERR;
    }

    res = zpath_debug_add_read_command("NP gateway Common stats",
                                       "/npgateway/common_stats",
                                       zpn_npgateway_common_stats_dump,
                                       NULL,
                                       "format",  "<optional> format=json, or plain text otherwise",
                                       NULL);
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not init /zpn/npgateway/common_stats debug command");
        return ZPN_RESULT_ERR;
    }

    return ZPN_RESULT_NO_ERROR;
}
