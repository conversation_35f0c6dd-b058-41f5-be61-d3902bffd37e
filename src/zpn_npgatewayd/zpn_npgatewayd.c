/*
 * zpn_npgatewayd.c. Copyright (C) 2024-2025 Zscaler, Inc. All Rights Reserved.
 *
 * This is the NP Gateway
 *
 */

#include "argo/argo_log.h"
#include "zpath_lib/sanitizer_config.h"
#include "zpath_lib/zpath_app.h"
#include "zpath_lib/zpath_debug.h"
#include "zpath_lib/zpath_et_zone.h"
#include "zpath_lib/zpath_et_customer_zone.h"
#include "zpath_lib/zpath_et_service_endpoint.h"
#include "zpath_lib/zpath_lib.h"
#include "zpath_lib/zpath_local.h"
#include "zpn/zpn_rpc.h"
#include "zpath_misc/zsysinfo.h"
#include "zpath_misc/zpath_version.h"
#include "npwg_lib/npwg_stats.h"
#include "np_lib/np_frr_utils.h"
#include "np_lib/np_rpc.h"

#include "zpn_npgatewayd/zpn_npgateway_alloc.h"
#include "zpn_npgatewayd/zpn_npgateway_common.h"
#include "zpn_npgatewayd/zpn_npgateway_error.h"
#include "zpn_npgatewayd/zpn_npgateway_health_server.h"
#include "zpn_npgatewayd/zpn_npgateway_logging.h"
#include "zpn_npgatewayd/zpn_npgateway_operator.h"
#include "zpn_npgatewayd/zpn_npgateway_stats.h"
#include "zpn_npgatewayd/zpn_npgateway_wally_client.h"
#include "zpn_npgatewayd/zpn_npgateway_creds.h"
#include "zpn_npgatewayd/zpn_npgateway_config_override_desc.h"
#include "zpn_npgatewayd/zpn_npgateway_admin_probe.h"
#include "zpn_npgatewayd/zpn_npgateway_bgp.h"

static void usage(const char *argv0, const char *format, ...)
{
    va_list list;

    fprintf(stdout, "Error: ");
	va_start(list, format);
	vfprintf(stdout, format, list);

    fprintf(stdout, "%s: Usage:\n", argv0);
    fprintf(stdout,
            "  -instance NUMBER         : Specify which instance ID this daemon will run as (default 0)\n"
            "  -daemon                  : Run as daemon\n"
            "  -health_server_addr ADDR : Override default (%s) health report listen addr,\n"
            "  -health_server_port PORT : Override default (%d) health report port,\n"
            "  -itasca_logs PORT        : Destination port on logging service endpoint (default: 9443)\n"
            "  -listener_port PORT      : Override default (%d) Gateway listener port\n"
            "  -no_health_server        : Turn of health reporting\n"
            "  -no_stats                : Turn of stats and load monitoring\n"
            "  -stack_path PATH         : Write cores to path specified. No trailing slash.\n"
            "  -tenant_gid GID          : Tenant gid\n"
            "  -tenant_gw_gid GID       : Tenant gateway gid\n"
            "  -bootstrap_timeout SEC   : Timeout for bootstrapping (default: 3)\n"
            "  --print-core FILE        : Read FILE and print stack\n"
            "  --print-core-force FILE  : Read FILE and print stack, without checking app name/version\n"
            "  -disable_watchdog        : Disable thread-hang detection\n"
            "  -version                 : Display version and exit\n"
            "  -help                    : Show help\n",
            DEFAULT_HEALTH_SERVER_ADDR,
            DEFAULT_HEALTH_SERVER_PORT,
            DEFAULT_NP_LISTENER_PORT);
    zpath_app_logging_usage_print();
    exit(EXIT_FAILURE);
}
typedef struct daemon_config
{
    int bootstrap_timeout_s;
    char *stack_path;
    int is_daemon;
    int version;
    int instance_id;
    int itasca_logs_port;
    int no_stats;
    int no_health_server;
} daemon_config_t;

static void set_default_daemon_config(daemon_config_t *daemon_config)
{
    daemon_config->bootstrap_timeout_s = 3;
    daemon_config->stack_path = NULL;
    daemon_config->is_daemon = 0;
    daemon_config->version = 0;
    daemon_config->instance_id = 0;
    daemon_config->itasca_logs_port = 9443;
    daemon_config->no_health_server = 0;
    daemon_config->no_stats = 0;
}

static void set_default_gateway_config(np_gateway_config_t *gateway_config)
{
    gateway_config->listener_port = DEFAULT_NP_LISTENER_PORT;
    gateway_config->health_server_address = DEFAULT_HEALTH_SERVER_ADDR;
    gateway_config->health_server_port = DEFAULT_HEALTH_SERVER_PORT;
    gateway_config->gateway_state = NP_TENANT_GATEWAY_STATE_ENROLLED;
}

static void parse_command_line(int argc,
                               char *argv[],
                               daemon_config_t *daemon_config,
                               np_gateway_config_t *gateway_config)
{
    for (int ii = 1, int_val = 0; ii < argc; ++ii) {
        /* Test for all one-word arguments. */
        if (strcmp(argv[ii], "-daemon") == 0) {
            daemon_config->is_daemon = 1;
        } else if (strcmp(argv[ii], "-no_health_server") == 0) {
            daemon_config->no_health_server = 1;
        } else if (strcmp(argv[ii], "-no_stats") == 0) {
            daemon_config->no_stats = 1;
        } else if (strcmp(argv[ii], "-disable_watchdog") == 0) {
            zthread_disable_heartbeat_monitor();
        } else if (strcmp(argv[ii], "-version") == 0) {
            fprintf(stdout, "%s\n", ZPATH_VERSION);
            exit(EXIT_SUCCESS);
        } else if (strcmp(argv[ii], "-help") == 0) {
            usage(argv[0], "\r");
        } else if (strcmp(argv[ii], "-disable_heartbeat_monitor") == 0) {
            zthread_disable_heartbeat_monitor();
        } else {
            /* Test for all two-word arguments. */
            if ((ii + 1) >= argc) {
                /* There is not a pair of words... */
                usage(argv[0], "Improper argument- may be missing second field: %s\n", argv[ii]);
            }
            if (strcmp(argv[ii], "-instance") == 0) {
                daemon_config->instance_id = atoi(argv[++ii]);
            } else if (strcmp(argv[ii], "-datacenter") == 0) {
                gateway_config->data_center = argv[++ii];
            } else if (strcmp(argv[ii], "-bootstrap_timeout") == 0) {
                int_val = atoi(argv[++ii]);
                if (!int_val || int_val > 60) {
                    usage(argv[0], "Invalid bootstrap timeout\n");
                }
                daemon_config->bootstrap_timeout_s = int_val;
            } else if (strcmp(argv[ii], "-health_server_addr") == 0) {
                gateway_config->health_server_address = argv[++ii];
            } else if (strcmp(argv[ii], "-health_server_port") == 0) {
                int_val = atoi(argv[++ii]);
                if (!int_val || int_val > USHRT_MAX) {
                    usage(argv[0], "Invalid health server port\n");
                }
                gateway_config->health_server_port = int_val;
            } else if (strcmp(argv[ii], "-itasca_logs") == 0) {
                int_val = atoi(argv[++ii]);
                if (!int_val || int_val > USHRT_MAX) {
                    usage(argv[0], "Invalid service port\n");
                }
                daemon_config->itasca_logs_port = int_val;
            } else if (strcmp(argv[ii], "-listener_port") == 0) {
                int_val = atoi(argv[++ii]);
                if (!int_val || int_val > USHRT_MAX) {
                    usage(argv[0], "Invalid listener port\n");
                }
                gateway_config->listener_port = int_val;
            } else if (strcmp(argv[ii], "-stack_path") == 0) {
                daemon_config->stack_path = argv[++ii];
            } else if (strcmp(argv[ii], "-tenant_gid") == 0) {
                gateway_config->customer_gid = strtoll(argv[++ii], NULL, 10);
            } else if (strcmp(argv[ii], "-tenant_gw_gid") == 0) {
                gateway_config->gid = strtoll(argv[++ii], NULL, 10);
            } else {
                usage(argv[0], "Unrecognized argument: %s\n", argv[ii]);
            }
        }
    }

    if (!gateway_config->customer_gid) {
        usage(argv[0], "-tenant_gid option is missing\n");
    }
    if (!gateway_config->gid) {
        usage(argv[0], "-tenant_gw_gid option is missing\n");
    }
    if (gateway_config->data_center == NULL) {
        usage(argv[0], "-datacenter option is missing\n");
    }
}

static void npgwd_signal_handler(int signum)
{
    NPGWD_LOG(AL_INFO, "received signal: %d", signum);
    sleep(5);
    exit(1);
}


int main(int argc, char *argv[])
{
    zthread_init(APP_ROLE, ZPATH_VERSION, "unknown", NULL, NULL);
    zthread_do_stack_dump(&argc, argv);
    char instance_name[ARGO_MAX_NAME_LENGTH];

    if (zpath_app_logging_parse_args(&argc, argv)) {
        exit(EXIT_FAILURE);
    }

    daemon_config_t daemon_config = { 0 };
    set_default_daemon_config(&daemon_config);
    np_gateway_config_t gateway_config = { 0 };
    set_default_gateway_config(&gateway_config);
    parse_command_line(argc, argv, &daemon_config, &gateway_config);

    int rc = argo_library_init(512);
    if (rc) {
        fprintf(stderr, "argo_library_init failed: %s\n", argo_result_string(rc));
        return rc;
    }

    struct zpn_npgateway_credentials *creds = NULL;
    for (int i = 0; i < daemon_config.bootstrap_timeout_s; i++) {
        if ((creds = zpn_npgateway_creds_fetch(gateway_config.gid)) != NULL) {
            break;
        }

        fprintf(stderr, "Waiting for the bootstrapper starts credential server...\n");
        sleep(1);
    }

    if (!creds) {
        fprintf(stderr, "Failed to fetch credentials from the bootstrapper in %d seconds\n", daemon_config.bootstrap_timeout_s);
        return NPGWD_RESULT_ERR;
    }
    strncpy(gateway_config.private_key, creds->wireguard_key, NPWG_KEY_LENGTH);

    if (daemon_config.is_daemon) {
        int fd;
        fprintf(stderr, "Daemonizing\n");
        /* Take off... */
        switch (fork()) {
		case 0:
			break;
		case -1:
            fprintf(stderr, "fork failed: %s\n", strerror(errno));
            return NPGWD_RESULT_ERR;
		default:
			/* exit interactive session */
            exit(EXIT_SUCCESS);
        }
        if (setsid() == -1) {
            fprintf(stderr, "setsid failed: %s\n", strerror(errno));
            return NPGWD_RESULT_ERR;
        }
        if ((fd = open("/dev/null", O_RDWR, 0)) != -1) {
            (void)dup2(fd, STDIN_FILENO);
            (void)dup2(fd, STDOUT_FILENO);
            (void)dup2(fd, STDERR_FILENO);
            if (fd > 2)
                (void)close(fd);
        }
    }

    update_itasca_logs_port(daemon_config.itasca_logs_port);

    struct zpath_app_init_params app_params;
    zpath_app_init_params_default(&app_params);
    app_params.instance_id = daemon_config.instance_id;
    app_params.role_name = APP_ROLE;
    app_params.fohh_thread_count = 1;
    app_params.fohh_private_key_pem = creds->cloud_key;

    rc = zpath_app_init(&app_params);
    zpn_npgateway_creds_free(creds);

    if (rc) {
        ZPATH_LOG(AL_ERROR, "zpath_app_init() failed : %s", zpath_result_string(rc));
        return rc;
    }

    NPGWD_LOG(AL_NOTICE, "zpath_app_init() complete");

    zpath_debug_add_allocator(&np_gateway_allocator, "np_gateway");
    snprintf(instance_name, sizeof(instance_name), "%s.%"PRId64".%s.%s",
             zpath_local_global_state.current_config->instance_name,
             gateway_config.customer_gid,
             gateway_config.data_center,
             zpath_local_global_state.combined_config.cloud_name);
    argo_log_update_instance_name(instance_name);

    zthread_init(APP_ROLE,
                 ZPATH_VERSION,
                 ZPATH_LOCAL_FULL_NAME,
                 daemon_config.stack_path,
                 NULL);

    if (!daemon_config.no_health_server) {
        rc = np_gateway_health_server_thread_init(&gateway_config);
        if (rc) {
            NPGWD_LOG(AL_ERROR, "np_gateway_health_server_thread_init() failed : %s",
                      npgwd_result_string(rc));
            goto init_error;
        }
    }

    zpn_event_collection = zpath_event_collection;

    rc = zpn_npgateway_debug_init();
    if (rc) {
        NPGWD_LOG(AL_ERROR, "zpn_npgateway_debug_init() failed : %s",
                      npgwd_result_string(rc));
        goto init_error;
    }

    rc = np_rpc_stats_init();
    if (rc) {
        NPGWD_LOG(AL_CRITICAL, "Could not register NP RPCs : %s", zpath_result_string(rc));
        goto init_error;
    }

    rc = admin_probe_rpc_init();
    if (rc) {
        NPGWD_LOG(AL_ERROR, "admin_probe_rpc_init() failed : %s", npgwd_result_string(rc));
        goto init_error;
    }

    rc = zpn_npgateway_bgp_init();
    if (rc) {
        NPGWD_LOG(AL_ERROR, "zpn_npgateway_bgp_init() failed : %s",
                      npgwd_result_string(rc));
        goto init_error;
    }

    /*
     * The ordering of the following:
     *
     *   np_gateway_operator_event_init(),
     *   np_gateway_wally_init() and
     *   np_gateway_operator_thread_init()
     *
     * is intentional. Please do not change this order.
     *
     * np_gateway_operator_event_init(), sets up the operator event queue.
     * np_gateway_wally_init() queues up the initial set of operator events.
     * Finally, np_gateway_operator_thread_init() starts the thread that
     * consumes these and subsequent events. The operator events arise out of
     * wally row callbacks.
     *
     */
    rc = np_gateway_operator_event_init(&gateway_config);
    if (rc) {
        NPGWD_LOG(AL_ERROR, "np_gateway_operator_event_init() failed : %s",
                  npgwd_result_string(rc));
        goto init_error;
    }

    NPGWD_LOG(AL_NOTICE, "np_gateway_operator_event_init() complete");

    rc = np_gateway_wally_init(&gateway_config);
    if (rc) {
        NPGWD_LOG(AL_ERROR, "np_gateway_wally_init() failed : %s", npgwd_result_string(rc));
        goto init_error;
    }

    NPGWD_LOG(AL_NOTICE, "np_gateway_wally_init() complete");

    rc = zpn_npgateway_config_ovd_desc_register_all();
    if (rc) {
        NPGWD_LOG(AL_ERROR, "ERROR : zpn_npgateway_config_ovd_desc_register_all failed: %s",
                zpath_result_string(rc));
        goto init_error;
    }

    rc = np_gateway_operator_thread_init(&gateway_config);
    if (rc) {
        NPGWD_LOG(AL_ERROR, "np_gateway_operator_thread_init() failed : %s",
                  npgwd_result_string(rc));
        goto init_error;
    }
    NPGWD_LOG(AL_NOTICE, "np_gateway_operator_thread_init() complete");

    if (!daemon_config.no_stats) {
        NPGWD_LOG(AL_NOTICE, "Initializing stats config.... log port:%d", daemon_config.itasca_logs_port);
        rc = zpath_et_zone_init(zpath_global_wally,
                                 zpath_global_slave_db,
                                 zpath_global_remote_db);
        if (rc) {
            NPGWD_LOG(AL_ERROR, "zpath_et_zone_init failed: %s", zpath_result_string(rc));
            goto init_error;
        }

        rc = zpath_et_customer_zone_init(NULL, 0);
        if (rc) {
            NPGWD_LOG(AL_ERROR, "zpath_et_customer_zone_init failed: %s",
                    zpath_result_string(rc));
            goto init_error;
        }

        rc = zpath_et_service_endpoint_init(zpath_global_wally,
                                            zpath_global_slave_db,
                                            zpath_global_remote_db,
                                            daemon_config.itasca_logs_port);
        if (rc) {
            NPGWD_LOG(AL_ERROR, "zpath_et_service_endpoint_init failed: %s", zpath_result_string(rc));
            goto init_error;
        }

        rc = zpn_rpc_init();
        if (rc) {
            NPGWD_LOG(AL_CRITICAL, "Could not register RPCs : %s", zpath_result_string(rc));
            goto init_error;
        }

        rc = zpn_npgateway_stats_init();
        if(rc) {
            NPGWD_LOG(AL_ERROR, "Initializing np gateway stats failed: %s", zpath_result_string(rc));
            goto init_error;
        }

        rc = np_frr_utils_init(zpn_np_instance_type_gateway, gateway_config.gid,
                         gateway_config.customer_gid, np_gateway_get_bgp_peer_info_from_neighbor_ip);
        if (rc) {
            NPGWD_LOG(AL_ERROR, "Initializing np gateway stats failed: %s", zpath_result_string(rc));
            goto init_error;
        }

        rc = zpn_npgateway_stats_load_monitor_init();
        if (rc) {
            NPGWD_LOG(AL_CRITICAL, "Failed to initialize load monitor thread");
            goto init_error;
        }

        rc = npwg_stats_wg_monitor_init(gateway_config.provider);
        if (rc) {
            NPGWD_LOG(AL_ERROR, "ERROR npgatewayd_stats_monitor_thread_init unable to init thread: %s",
                    zpath_result_string(rc));
            goto init_error;
        }

    }

    rc = zpn_npgateway_admin_probe_init(gateway_config.wally, gateway_config.gid, gateway_config.customer_gid, zpn_event_collection, 1);
    if (rc) {
        NPGWD_LOG(AL_ERROR, "zpn_npgateway_admin_probe_init() failed : %s", npgwd_result_string(rc));
        goto init_error;
    }
    NPGWD_LOG(AL_NOTICE, "zpn_npgateway_admin_probe_init() complete");

    __atomic_store_n(&gateway_config.gateway_state,
                     NP_TENANT_GATEWAY_STATE_ONLINE,
                     __ATOMIC_SEQ_CST);

    signal(SIGPIPE, SIG_IGN);
    signal(SIGTERM, &npgwd_signal_handler);
    signal(SIGINT, &npgwd_signal_handler);

    NPGWD_LOG(AL_NOTICE, "Initialization Complete");
    zpath_registration_completed();

    while(1) sleep(1);

    return EXIT_SUCCESS;

init_error:
    sleep(1);
    return rc;
}
