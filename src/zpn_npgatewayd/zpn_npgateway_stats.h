/*
 * zpn_npgateway_stats.h. Copyright (C) 2024 Zscaler Inc. All Rights Reserved.
 */

#ifndef _ZPN_NPGATEWAY_STATS_H_
#define _ZPN_NPGATEWAY_STATS_H_

#include "zpath_lib/zpath_system_stats.h"

extern struct argo_structure_description *zpn_npgateway_common_stats_description;

#include "npwg_lib/npwg_stats.h"

extern struct argo_structure_description *zpn_npgateway_common_stats_description;

struct zpn_npgateway_sys_stats {    /* _ARGO: object_definition */
    int system_mem_util;        /* _ARGO: integer */
    int process_mem_util;       /* _ARGO: integer */
    uint16_t cpu_util;          /* _ARGO: integer */
    uint64_t nonidle;           /* _ARGO: integer */
    uint64_t idle;              /* _ARGO: integer */
    int32_t sys_fd_util;        /* _ARGO: integer */
    int32_t proc_fd_util;       /* _ARGO: integer */
};

// based on struct zpn_npgateway_stats_wg
struct zpn_npgateway_wireguard_stats {                         /* _ARGO: object_definition */

    int64_t this_npgateway_listen_port;                        /* _ARGO: integer */

    int64_t peers_count_total;                                 /* _ARGO: integer */
    int64_t peers_count_total_max;                             /* _ARGO: integer */
    int64_t peers_count_npgateways;                            /* _ARGO: integer */
    int64_t peers_count_clients;                               /* _ARGO: integer */
    int64_t peers_count_connectors;                            /* _ARGO: integer */

    int64_t allowed_ips_total_cnt;                             /* _ARGO: integer */
    int64_t allowed_ips_total_cnt_max;                         /* _ARGO: integer */
    int64_t allowed_ips_npgateways_cnt;                        /* _ARGO: integer */
    int64_t allowed_ips_clients_cnt;                           /* _ARGO: integer */
    int64_t allowed_ips_connectors_cnt;                        /* _ARGO: integer */

    int64_t rx_total_bytes;                                    /* _ARGO: integer */
    int64_t tx_total_bytes;                                    /* _ARGO: integer */
    int64_t rx_npgateways_bytes;                               /* _ARGO: integer */
    int64_t tx_npgateways_bytes;                               /* _ARGO: integer */
    int64_t rx_clients_bytes;                                  /* _ARGO: integer */
    int64_t tx_clients_bytes;                                  /* _ARGO: integer */
    int64_t rx_connectors_bytes;                               /* _ARGO: integer */
    int64_t tx_connectors_bytes;                               /* _ARGO: integer */

    int64_t rx_total_bytes_per_sec;                            /* _ARGO: integer */
    int64_t tx_total_bytes_per_sec;                            /* _ARGO: integer */
    int64_t rx_npgateways_bytes_per_sec;                       /* _ARGO: integer */
    int64_t tx_npgateways_bytes_per_sec;                       /* _ARGO: integer */
    int64_t rx_clients_bytes_per_sec;                          /* _ARGO: integer */
    int64_t tx_clients_bytes_per_sec;                          /* _ARGO: integer */
    int64_t rx_connectors_bytes_per_sec;                       /* _ARGO: integer */
    int64_t tx_connectors_bytes_per_sec;                       /* _ARGO: integer */

    int64_t handshake_clients_hist_0;                          /* _ARGO: integer */
    int64_t handshake_clients_hist_1;                          /* _ARGO: integer */
    int64_t handshake_clients_hist_2;                          /* _ARGO: integer */
    int64_t handshake_connectors_hist_0;                       /* _ARGO: integer */
    int64_t handshake_connectors_hist_1;                       /* _ARGO: integer */
    int64_t handshake_connectors_hist_2;                       /* _ARGO: integer */
    int64_t handshake_npgateways_hist_0;                       /* _ARGO: integer */
    int64_t handshake_npgateways_hist_1;                       /* _ARGO: integer */
    int64_t handshake_npgateways_hist_2;                       /* _ARGO: integer */

    int64_t persistent_keepalive_off_count;                    /* _ARGO: integer */

    int64_t npgateway_stats_monitor_cnt;                       /* _ARGO: integer */
    int64_t npgateway_stats_monitor_error_cnt;                 /* _ARGO: integer */

    int64_t allowed_ips_clients_get_error_cnt;                 /* _ARGO: integer */
    int64_t allowed_ips_connectors_get_error_cnt;              /* _ARGO: integer */
    int64_t allowed_ips_npgateways_get_error_cnt;              /* _ARGO: integer */

    int64_t peers_count_invalid;                               /* _ARGO: integer */
    int64_t allowed_ips_invalids_cnt;                          /* _ARGO: integer */
    int64_t rx_invalids_bytes;                                 /* _ARGO: integer */
    int64_t tx_invalids_bytes;                                 /* _ARGO: integer */
    int64_t rx_invalids_bytes_per_sec;                         /* _ARGO: integer */
    int64_t tx_invalids_bytes_per_sec;                         /* _ARGO: integer */

    int64_t peer_type_get_err_cnt;                             /* _ARGO: integer */
    int64_t peer_type_get_err_total_cnt;                       /* _ARGO: integer */
};
struct zpn_npgateway_common_stats {                            /* _ARGO: object_definition */
    int64_t mtu;                                               /* _ARGO: integer */
    int64_t npc_provider_created;                        /* _ARGO: integer */
    int64_t npc_provider_destroyed;                      /* _ARGO: integer */
    int64_t npc_provider_add_hash;                       /* _ARGO: integer */
    int64_t npc_provider_remove_hash;                    /* _ARGO: integer */
    int64_t npc_provider_remove_hash_err;                /* _ARGO: integer */
    int64_t redundancy_start_wg_fail;                    /* _ARGO: integer */
    int64_t ip_link_delete_err;                          /* _ARGO: integer */
    int64_t ip_link_add_err;                             /* _ARGO: integer */
    int64_t ip_link_up_err;                              /* _ARGO: integer */
    int64_t ip_link_down_err;                            /* _ARGO: integer */
    int64_t link_wg_intf_to_vrf_err;                     /* _ARGO: integer */
    int64_t wg_setconf_err;                              /* _ARGO: integer */
    int64_t npc_set_intf_err;                            /* _ARGO: integer */
    int64_t npc_set_peer_err;                            /* _ARGO: integer */
    int64_t interface_check_and_delete_fail;             /* _ARGO: integer */
};

// following formats are aligned as per above struct zpn_npgateway_wireguard_stats
#define ZPN_NPGATEWAY_WIREGUARD_STATS_FORMAT_JSON                          \
        "{\"this_npgateway_listen_port\": %" PRId64 ","                    \
        " \"peers_count_total\": %" PRId64 ","                             \
        " \"peers_count_total_max\": %" PRId64 ","                         \
        " \"peers_count_npgateways\": %" PRId64 ","                        \
        " \"peers_count_clients\": %" PRId64  ","                          \
        " \"peers_count_connectors\": %" PRId64  ","                       \
        " \"allowed_ips_total_cnt\": %" PRId64  ","                        \
        " \"allowed_ips_total_cnt_max\": %" PRId64  ","                    \
        " \"allowed_ips_npgateways_cnt\": %" PRId64  ","                   \
        " \"allowed_ips_clients_cnt\": %" PRId64  ","                      \
        " \"allowed_ips_connectors_cnt\": %" PRId64  ","                   \
        " \"rx_total_bytes\": %" PRId64  ","                               \
        " \"tx_total_bytes\": %" PRId64  ","                               \
        " \"rx_npgateways_bytes\": %" PRId64  ","                          \
        " \"tx_npgateways_bytes\": %" PRId64  ","                          \
        " \"rx_clients_bytes\": %" PRId64  ","                             \
        " \"tx_clients_bytes\": %" PRId64  ","                             \
        " \"rx_connectors_bytes\": %" PRId64  ","                          \
        " \"tx_connectors_bytes\": %" PRId64  ","                          \
        " \"rx_total_bytes_per_sec\": %" PRId64  ","                       \
        " \"tx_total_bytes_per_sec\": %" PRId64  ","                       \
        " \"rx_npgateways_bytes_per_sec\": %" PRId64  ","                  \
        " \"tx_npgateways_bytes_per_sec\": %" PRId64  ","                  \
        " \"rx_clients_bytes_per_sec\": %" PRId64  ","                     \
        " \"tx_clients_bytes_per_sec\": %" PRId64  ","                     \
        " \"rx_connectors_bytes_per_sec\": %" PRId64  ","                  \
        " \"tx_connectors_bytes_per_sec\": %" PRId64  ","                  \
        " \"handshake_clients_hist_0\": %" PRId64  ","                     \
        " \"handshake_clients_hist_1\": %" PRId64  ","                     \
        " \"handshake_clients_hist_2\": %" PRId64  ","                     \
        " \"handshake_connectors_hist_0\": %" PRId64  ","                  \
        " \"handshake_connectors_hist_1\": %" PRId64  ","                  \
        " \"handshake_connectors_hist_2\": %" PRId64  ","                  \
        " \"handshake_npgateways_hist_0\": %" PRId64  ","                  \
        " \"handshake_npgateways_hist_1\": %" PRId64  ","                  \
        " \"handshake_npgateways_hist_2\": %" PRId64  ","                  \
        " \"persistent_keepalive_off_count\": %" PRId64  ","               \
        " \"npgateway_stats_monitor_cnt\": %" PRId64  ","                  \
        " \"npgateway_stats_monitor_error_cnt\": %" PRId64  ","            \
        " \"allowed_ips_clients_get_error_cnt\": %" PRId64  ","            \
        " \"allowed_ips_connectors_get_error_cnt\": %" PRId64  ","         \
        " \"allowed_ips_npgateways_get_error_cnt\": %" PRId64  ","         \
        " \"peers_count_invalid\": %" PRId64  ","                          \
        " \"allowed_ips_invalids_cnt\": %" PRId64  ","                     \
        " \"rx_invalids_bytes\": %" PRId64  ","                            \
        " \"tx_invalids_bytes\": %" PRId64  ","                            \
        " \"rx_invalids_bytes_per_sec\": %" PRId64  ","                    \
        " \"tx_invalids_bytes_per_sec\": %" PRId64  ","                    \
        " \"peer_type_get_err_cnt\": %" PRId64  ","                        \
        " \"peer_type_get_err_total_cnt\": %" PRId64 "}\n"

#define ZPN_NPGATEWAY_WIREGUARD_STATS_FORMAT_TEXT                        \
        "this_npgateway_listen_port               : %" PRIu64 "\n"       \
        "peers_count_total                        : %" PRId64 "\n"       \
        "peers_count_total_max                    : %" PRId64 "\n"       \
        "peers_count_npgateways                   : %" PRId64 "\n"       \
        "peers_count_clients                      : %" PRId64 "\n"       \
        "peers_count_connectors                   : %" PRId64 "\n"       \
        "allowed_ips_total_cnt                    : %" PRId64 "\n"       \
        "allowed_ips_total_cnt_max                : %" PRId64 "\n"       \
        "allowed_ips_npgateways_cnt               : %" PRId64 "\n"       \
        "allowed_ips_clients_cnt                  : %" PRId64 "\n"       \
        "allowed_ips_connectors_cnt               : %" PRId64 "\n"       \
        "rx_total_bytes                           : %" PRId64 "\n"       \
        "tx_total_bytes                           : %" PRId64 "\n"       \
        "rx_npgateways_bytes                      : %" PRId64 "\n"       \
        "tx_npgateways_bytes                      : %" PRId64 "\n"       \
        "rx_clients_bytes                         : %" PRId64 "\n"       \
        "tx_clients_bytes                         : %" PRId64 "\n"       \
        "rx_connectors_bytes                      : %" PRId64 "\n"       \
        "tx_connectors_bytes                      : %" PRId64 "\n"       \
        "rx_total_bytes_per_sec                   : %" PRId64 "\n"       \
        "tx_total_bytes_per_sec                   : %" PRId64 "\n"       \
        "rx_npgateways_bytes_per_sec              : %" PRId64 "\n"       \
        "tx_npgateways_bytes_per_sec              : %" PRId64 "\n"       \
        "rx_clients_bytes_per_sec                 : %" PRId64 "\n"       \
        "tx_clients_bytes_per_sec                 : %" PRId64 "\n"       \
        "rx_connectors_bytes_per_sec              : %" PRId64 "\n"       \
        "tx_connectors_bytes_per_sec              : %" PRId64 "\n"       \
        "handshake_clients_hist_0                 : %" PRId64 "\n"       \
        "handshake_clients_hist_1                 : %" PRId64 "\n"       \
        "handshake_clients_hist_2                 : %" PRId64 "\n"       \
        "handshake_connectors_hist_0              : %" PRId64 "\n"       \
        "handshake_connectors_hist_1              : %" PRId64 "\n"       \
        "handshake_connectors_hist_2              : %" PRId64 "\n"       \
        "handshake_npgateways_hist_0              : %" PRId64 "\n"       \
        "handshake_npgateways_hist_1              : %" PRId64 "\n"       \
        "handshake_npgateways_hist_2              : %" PRId64 "\n"       \
        "persistent_keepalive_off_count           : %" PRId64 "\n"       \
        "npgateway_stats_monitor_cnt              : %" PRId64 "\n"       \
        "npgateway_stats_monitor_error_cnt        : %" PRId64 "\n"       \
        "allowed_ips_clients_get_error_cnt        : %" PRId64 "\n"       \
        "allowed_ips_connectors_get_error_cnt     : %" PRId64 "\n"       \
        "allowed_ips_npgateways_get_error_cnt     : %" PRId64 "\n"       \
        "peers_count_invalid                      : %" PRId64 "\n"       \
        "allowed_ips_invalids_cnt                 : %" PRId64 "\n"       \
        "rx_invalids_bytes                        : %" PRId64 "\n"       \
        "tx_invalids_bytes                        : %" PRId64 "\n"       \
        "rx_invalids_bytes_per_sec                : %" PRId64 "\n"       \
        "tx_invalids_bytes_per_sec                : %" PRId64 "\n"       \
        "peer_type_get_err_cnt                    : %" PRId64 "\n"       \
        "peer_type_get_err_total_cnt              : %" PRId64 "\n"

struct zpn_npgateway_stats
{
    struct zpn_system_fd_stats   zpn_npgateway_fd_stats;
    struct zpn_system_sock_stats zpn_npgateway_sock_stats;
    struct zpn_system_cpu_stats  zpn_npgateway_cpu_stats;
    struct zpn_system_disk_stats zpn_npgateway_disk_stats;
    struct zpn_system_memory_stats zpn_npgateway_memory_stats;
    struct zpn_npgateway_wireguard_stats zpn_npgateway_wireguard_stats;
    struct zpn_npgateway_sys_stats zpn_npgateway_sys_stats;
    struct zpn_npgateway_common_stats zpn_npgateway_common_stats;
};

int zpn_npgateway_stats_init();
int zpn_npgateway_stats_load_monitor_init();

#endif /* __ZPATH_NPGATEWAYD_STATS_H__ */
