/*
 * zpn_npgateway_admin_probe.h. Copyright (C) 2025 Zscaler Inc, All Rights Reserved
 */

#ifndef _ZPN_NPGATEWAY_ADMIN_PROBE_H_
#define _ZPN_NPGATEWAY_ADMIN_PROBE_H_

#include "wally/wally_private.h"
#include "admin_probe/admin_probe_rpc.h"


int zpn_npgateway_admin_probe_init(struct wally *npgw_wally, int64_t npgw_gid, int64_t customer_gid, struct argo_log_collection *event_log, int is_zpath_config_override_inited);
int zpn_npgateway_admin_probe_tx_task_update(void* status, int is_np_command_probe);

int
zpn_npgateway_admin_probe_stats_fill(void*     cookie,
                                 int       counter,
                                 void*     structure_data);

#endif /* _ZPN_NPGATEWAY_ADMIN_PROBE_H_ */
