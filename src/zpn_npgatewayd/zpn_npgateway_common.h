/*
 * zpn_npgateway_common.h. Copyright (C) 2024 Zscaler, Inc. All Rights Reserved.
 *
 * NP Gateway common functionality.
 *
 */

#ifndef __ZPN_NPGATEWAY_COMMON_H__
#define __ZPN_NPGATEWAY_COMMON_H__

#include "zpn_npgatewayd/zpn_npgateway_error.h"

#include "argo/argo.h"
#include <event.h>
#include "npwg_lib/npwg_provider.h"
#include "np_lib/np_tenant_gateways.h"
#include "wally/wally.h"
#include "zevent/zevent.h"

// NOTE: must change to zpn_npgatewayd after UI / API changes
#define APP_ROLE    "zpn_npgatewayd"

#define DEFAULT_NP_LISTENER_PORT   51820

#define HEARTBEAT_INTERVAL_SECONDS      1
#define DRIVER_UPDATE_INTERVAL_SECONDS  5

#define NPGATEWAY_MONITOR_INTERVAL_S                 (1 * 60)
#define NPGATEWAY_MONITOR_HEARTBEAT_TIMEOUT_S        (1 * 60)
#define NPGATEWAY_MONITOR_THREAD_STACK_SIZE          (16 * 1024 * 1024)
#define NPGATEWAY_MONITOR_THREAD_STATS_INTERVAL_US   MINUTE_TO_US(1)

typedef enum np_connector_state
{
    CONNECTOR_STATE_OFFLINE     = 0,
    CONNECTOR_STATE_ONLINE      = 1,
    CONNECTOR_STATE_COUNT       = 2
} np_connector_state_t;

typedef struct np_gateway_config
{
    // this is the tenant gateway's gid
    int64_t gid;

    int64_t customer_gid;

    // np gateway datacenter
    const char *data_center;

    char private_key[NPWG_KEY_LENGTH+1];

    // cmd-line param
    uint16_t listener_port;

    // must be present in designated folder
    // string cloud_cert;

    // must be present in designated folder
    // string cloud_private_key;

    // read from wally
    // note: set and make use of this
    enum np_tenant_gateway_state gateway_state;

    struct wally *wally;

    struct event_base *operator_event_base;

    // NP Common Library Provider
    npwg_provider provider;

    pthread_t operator_thread;

    // cmd-line param
    uint16_t health_server_port;

    // health check listen address, we may use an extra ENI not bound
    // to an EIP for communication with the deployer alone.
    const char *health_server_address;

    struct event_base *health_server_event_base;
    struct evhttp *health_server;

    pthread_t health_server_thread;

    /*
     * Hash table keep track of wg interfaces towards NPC
     */
    struct zhash_table *npc_hash_by_gid;
    zpath_mutex_t npc_hash_lock;

    int mtu;

    uint8_t redundant_mode_enabled:1,
            redundancy_feature_enabled:1;

} np_gateway_config_t;

typedef struct np_gateway_event_param
{
    np_gateway_config_t *gateway_config;
    struct argo_object *previous_row;
    struct argo_object *row;
} np_gateway_event_param_t;

extern struct zpn_npgateway_common_stats operate_stats;

int allocate_gateway_event_param(np_gateway_event_param_t **event_param,
                                 np_gateway_config_t *gateway_config,
                                 struct argo_object *previous_row,
                                 struct argo_object *row,
                                 const char *caller);

void free_gateway_event_param(np_gateway_event_param_t **event_param);

const char *gateway_health_to_str(const enum np_tenant_gateway_state gateway_state,
                                  size_t *length);

const char *gateway_health_to_str_safe(const np_gateway_config_t *config, size_t *length);

#endif /* __ZPN_NPGATEWAY_COMMON_H__ */
