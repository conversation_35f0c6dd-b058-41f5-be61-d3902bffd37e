/*
 * zpn_npgateway_admin_probe.c. Copyright (C) 2025 Zscaler Inc, All Rights Reserved
 */

#include "zpn_npgatewayd/zpn_npgateway_admin_probe.h"
#include "zpn_npgatewayd/zpn_npgateway_logging.h"
#include "zpn_npgatewayd/zpn_npgateway_error.h"
#include "zpn_npgatewayd/zpn_npgateway_bgp.h"
#include "admin_probe/admin_probe.h"
#include "admin_probe/admin_probe_public.h"
#include "zpn_enrollment_lib/zpn_enrollment.h"
#include "zthread/zthread.h"
#include "admin_probe/admin_probe_stats.h"
#include "np_lib/np_frr_utils.h"
#include "np_lib/np.h"
#include "np_lib/np_bgp.h"

#ifdef __linux__
#include <linux/reboot.h>
#include <sys/syscall.h>
#include <sys/reboot.h>
#include <unistd.h>
#else
#endif

#include "zpath_lib/zpath_debug.h"
#include "zpath_lib/zpath_log_store.h"
#include "zpath_lib/zpath_customer.h"
#include "zpath_lib/zpath_config_override_keys.h"
#include "zpath_lib/zpath_config_override.h"
#include "zpath_lib/zpath_et_service_endpoint.h"

struct argo_log_collection *npgw_admin_probe_collection = NULL;
static int64_t g_customer_gid = 0;

static int do_log_file(struct argo_log_collection *collection,
                       const char *name,
                       const char *path,
                       const char *filename)
{
    char full_file[1000];

    snprintf(full_file, sizeof(full_file), "%s/%s", path, filename);

    struct argo_log_file *log_file;
    log_file = argo_log_file_create(collection,
                                    full_file,
                                    NULL,
                                    1024 * 1024 * 1024,
                                    argo_serialize_binary);
    if (!log_file) {
        return NPGWD_RESULT_ERR;
    }

    if (!argo_log_read(collection, name, 0, 1, argo_log_file_callback, NULL, log_file, NULL, 0)) {
        return NPGWD_RESULT_ERR;
    }
    return NPGWD_RESULT_NO_ERROR;
}

int zpn_npgateway_admin_probe_restart(enum restart_type type)
{
    //TBD
    return NPGWD_RESULT_NO_ERROR;
}


int zpn_npgateway_admin_probe_tx_task_update(void* status, int is_np_command_probe)
{
    struct np_command_probe_status* report = (struct np_command_probe_status*)status;
    int res = NPGWD_RESULT_NO_ERROR;
    if (npgw_admin_probe_collection) {
        argo_log_structure_immediate(npgw_admin_probe_collection,
                                     argo_log_priority_info,
                                     0,
                                     "status_report",
                                     np_command_probe_status_description,
                                     report);
    }

    NPGWD_LOG(AL_NOTICE, "Command Probe %s : np_command_probe_status report from NP Gateway", report->command_uuid? report->command_uuid: "");

    res = zpath_service_endpoint_log_struct(report->customer_gid,
                                            "status_report",
                                            zpath_customer_log_type_admin_probe,
                                            NULL,
                                            NULL,
                                            NULL,
                                            NULL,
                                            np_command_probe_status_description,
                                            report);
    if (res) {
        NP_LOG(AL_ERROR, "Error sending np_command_probe_status, to kafka.. result:%s", zpath_result_string(res));
    }
    return res;
}

int zpn_async_http_upload_cb(struct fohh_http_client *client,
              enum fohh_http_client_request_status status,
              int http_status,
              struct evbuffer *result_body,
              void *void_cookie,
              int64_t int_cookie)
{
    NPGWD_LOG(AL_ERROR, "async CB. request status = %s, Status = %d, Body length = %ld\n",
            fohh_http_client_request_status_str(status),
            http_status,
            result_body ? evbuffer_get_length(result_body) : 0);
    return NPGWD_RESULT_NO_ERROR;
}

int zpn_npgateway_get_remote_host_for_s3_upload(char *remote_host, int size, int *no_proxy)
{
    if (no_proxy) {
        *no_proxy = 1;
    }
    return NPGWD_RESULT_NO_ERROR;

}

int zpn_npgateway_cfg_override_feature_is_npgateway_admin_probe_enabled(enum admin_probe_task_type type)
{
    int64_t config_value = 0;

    if (type < admin_probe_task_type_ip_route || type > admin_probe_task_type_reload_bgp) {
        NPGWD_LOG(AL_ERROR, "Command not supported on NP Gateway - %d", type);
        return 0;
    }
    config_value = zpath_config_override_get_config_int(NETWORK_PRESENCE_ADMIN_PROBE_FEATURE_CMDS_ENABLED,
                                                        &config_value,
                                                        DEFAULT_NETWORK_PRESENCE_ADMIN_PROBE_FEATURE_CMDS_ENABLED,
                                                        (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                        (int64_t)0);
    if (config_value) {
        // Make sure np redundancy feature is enabled to allow commands related to FRR
        if (np_is_redundancy_feature_enabled(g_customer_gid)) {
            config_value = 1;
        } else {
            config_value = 0;
        }
    }

    return config_value?1:0;
}

static int zpn_np_prepare_frr_cmds(struct zpn_frr_cmds_execute_args *frr_arg, char *cmd,
                                         int size_cmd, int *result_to_file) {
    int res = 0;
    int config_mode = -1;
    // Default true
    *result_to_file = 1;

    switch (frr_arg->task_type) {
        case admin_probe_task_type_ip_route:
            res = zpn_np_prepare_show_ip_route_cmd(frr_arg->params->target, cmd, size_cmd);
            break;
        case admin_probe_task_type_ip_interfaces:
            res = zpn_np_prepare_show_ip_interface_cmd(frr_arg->params->interface, cmd, size_cmd);
            break;
        case admin_probe_task_type_ip_bgp:
            res = zpn_np_prepare_show_ip_bgp_cmd(frr_arg->params->target, cmd, size_cmd);
            break;
        case admin_probe_task_type_ip_bgp_neighbors:
            res = zpn_np_prepare_show_ip_bgp_neighbors_cmd(frr_arg->params->target, cmd, size_cmd);
            break;
        case admin_probe_task_type_ip_bgp_summary:
            res = zpn_np_prepare_show_ip_bgp_summary_cmd(cmd, size_cmd);
            break;
        case admin_probe_task_type_ip_clear_bgp:
            res = zpn_np_prepare_clear_ip_bgp_cmd(frr_arg->params->target, cmd, size_cmd);
            // file upload not required for ip clear bgp
            *result_to_file = 0;
            break;
        case admin_probe_task_type_bgp_running_config:
            res = zpn_np_prepare_bgp_running_config_cmd(cmd, size_cmd);
            break;
        case admin_probe_task_type_bgp_config_validate:
            res = zpn_np_prepare_bgp_config_validate_cmd(frr_arg->params->config_type, cmd, size_cmd);
            break;
        case admin_probe_task_type_bgp_config_status_details:
            config_mode = np_gateway_get_bgp_config_mode();
            if (config_mode == -1) {
                NPGWD_LOG(AL_ERROR, "Invalid BGP config mode");
                return NPGWD_RESULT_BAD_STATE;
            }
            res = zpn_np_prepare_bgp_config_validate_cmd(config_mode, cmd, size_cmd);
            break;
        case admin_probe_task_type_bgp_get_logs:
            res = zpn_np_prepare_bgp_get_logs_cmd(frr_arg->params->number, frr_arg->params->since_mins,
                                                     frr_arg->params->until_mins, cmd, size_cmd);
            break;
        case admin_probe_task_type_stop_bgp:
            res = zpn_np_prepare_stop_bgp_cmd(cmd, size_cmd);
            // file upload not required for stop bgp
            *result_to_file = 0;
            break;
        case admin_probe_task_type_start_bgp:
            res = zpn_np_prepare_start_bgp_cmd(cmd, size_cmd);
            // file upload not required for start bgp
            *result_to_file = 0;
            break;
        case admin_probe_task_type_restart_bgp:
            res = zpn_np_prepare_restart_bgp_cmd(cmd, size_cmd);
            // file upload not required for restart bgp
            *result_to_file = 0;
            break;
        case admin_probe_task_type_status_bgp:
            res = zpn_np_prepare_status_bgp_cmd(cmd, size_cmd);
            break;
        default:
            res = NPGWD_RESULT_NOT_FOUND;
            break;
    }
    return res;
}

int zpn_npgateway_admin_probe_frr_cmds_execute(struct zpn_frr_cmds_execute_args *frr_arg, int *cmd_result, char **err_msg) {
    int res = NPGWD_RESULT_NO_ERROR;
    char cmd_to_execute[512] = {0};
    char *out_buf = NULL;
    char *filepath = NULL;
    int is_config_valid = 0;
    int config_mode = -1;

    if (frr_arg == NULL || cmd_result == NULL || err_msg == NULL) {
        NPGWD_LOG(AL_ERROR, "Invalid Input");
        return NPGWD_RESULT_BAD_ARGUMENT;
    }

    filepath = frr_arg->file_path;

    if (frr_arg->task_type == admin_probe_task_type_bgp_failed_config) {
        res = np_frr_util_file_copy(NP_BGP_FRR_ERROR_CONFIG_FILENAME_GENERATED, filepath);
        if (res) {
            NPGWD_LOG(AL_ERROR, "Error copying generated frr config:%s to:%s for command for task_type:%d error:%s",
                                        NP_BGP_FRR_ERROR_CONFIG_FILENAME_GENERATED, filepath,
                                        frr_arg->task_type, zpath_result_string(res));
            if (!np_frr_util_is_file_exist(NP_BGP_FRR_ERROR_CONFIG_FILENAME_GENERATED)) {
                *err_msg = ADMIN_PROBE_ERR_CONFIG_FILE_DOESNOT_EXIST;
            }
            return res;
        }
        *cmd_result = 1;
    } else if (frr_arg->task_type == admin_probe_task_type_bgp_generated_config) {
        res = np_frr_util_file_copy(NP_BGP_FRR_CONFIG_FILENAME_GENERATED, filepath);
        if (res) {
            NPGWD_LOG(AL_ERROR, "Error copying generated frr config:%s to:%s for command task_type:%d error:%s",
                                         NP_BGP_FRR_CONFIG_FILENAME_GENERATED, filepath,
                                        frr_arg->task_type, zpath_result_string(res));

            if (!np_frr_util_is_file_exist(NP_BGP_FRR_CONFIG_FILENAME_GENERATED)) {
                *err_msg = ADMIN_PROBE_ERR_CONFIG_FILE_DOESNOT_EXIST;
            }
            return res;
        }
        *cmd_result = 1;
    } else if (frr_arg->task_type == admin_probe_task_type_reload_bgp) {
        res = np_bgp_gateway_reload_frr_conf(frr_arg->params->is_force_reload);
        if (res) {
            ZPN_LOG(AL_ERROR, "Error reloading frr config for command task_type:%d force_regen:%d error:%s",
                                        frr_arg->task_type, frr_arg->params->is_force_reload, zpath_result_string(res));
            return res;
        }
        *cmd_result = 1;
    } else if (frr_arg->task_type == admin_probe_task_type_bgp_config_status) {

        config_mode = np_gateway_get_bgp_config_mode();
        if (config_mode == -1) {
            NPGWD_LOG(AL_ERROR, "Invalid BGP config mode");
            return NPGWD_RESULT_BAD_STATE;
        }

        res = np_frr_test_config_status((config_mode == np_bgp_config_mode_generated)?
                                NP_BGP_FRR_CONFIG_FILENAME_GENERATED :
                                NP_BGP_FRR_CONFIG_FILENAME_OVERRIDE, &is_config_valid);
        if (res) {
            NPGWD_LOG(AL_ERROR, "Error validating frr config for mode:%d task_type:%d error:%s",
                                         config_mode, frr_arg->task_type, zpath_result_string(res));
            return res;
        }
        *cmd_result = is_config_valid ? 1 : 0;

    } else {
        int write_result_to_file = 0;

        res = zpn_np_prepare_frr_cmds(frr_arg, cmd_to_execute, sizeof(cmd_to_execute), &write_result_to_file);
        if (res) {
            NPGWD_LOG(AL_ERROR, "Failed to prepare command for task_type:%d error:%s", frr_arg->task_type, zpath_result_string(res));
            *err_msg = ADMIN_PROBE_ERR_PREPARING_FRR_COMMAND;
            return res;
        }

        NPGWD_LOG(AL_INFO, "Executing command:%s for task_type:%d", cmd_to_execute, frr_arg->task_type);

        res = np_frr_util_execute_command(cmd_to_execute, &out_buf);
        if (res) {
            NPGWD_LOG(AL_ERROR, "Failed to execute np probe command:%s error:%s", cmd_to_execute, zpath_result_string(res));
            np_frr_release_buf(out_buf);
            *err_msg = ADMIN_PROBE_ERR_COMMAND_EXECUTION;
            return res;
        }

        if (write_result_to_file) {
            if (out_buf) {
                res = np_frr_util_write_buffer_to_file(out_buf, strlen(out_buf), filepath);
                if (res) {
                    NPGWD_LOG(AL_ERROR, "Failed to write frr command:%s output to file:%s error:%s",
                                            cmd_to_execute, filepath, zpath_result_string(res));
                    *err_msg = ADMIN_PROBE_ERR_WRITE_OUTPUT_TO_FILE;
                    np_frr_release_buf(out_buf);
                    return res;
                }
            } else {
                NPGWD_LOG(AL_ERROR, "Command:%s executed, but no output.. Review the command/setup..", cmd_to_execute);
                *err_msg = ADMIN_PROBE_ERR_COMMAND_EXECUTION;
                return NPGWD_RESULT_BAD_STATE;
            }
        }

        // command execution is success
        *cmd_result = 1;
        np_frr_release_buf(out_buf);
    }

    return res;
}

int zpn_npgateway_admin_probe_init(struct wally *npgw_wally, int64_t npgw_gid, int64_t customer_gid, struct argo_log_collection *event_log, int is_zpath_config_override_inited)
{
    int res;

    int is_dev_env = 0;
    int is_np_command_probe = 1;

    npgw_admin_probe_collection = argo_log_create("npgw_admin_probe_log", NULL, NULL); /* 64K entries */
    if (!npgw_admin_probe_collection) {
        NPGWD_LOG(AL_ERROR, "Could not create npgw_admin_probe_log");
        return NPGWD_RESULT_ERR;
    }

    if (do_log_file(npgw_admin_probe_collection, "npgw_admin_probe_log_file", "/zpath/log", "npgw_admin_probe.log")) {
        NPGWD_LOG(AL_ERROR, "Could not create npgw admin probe reader");
    }

    g_customer_gid = customer_gid;

    res = admin_probe_init(npgw_wally,
                           npgw_gid,
                           NULL,
                           customer_gid,
                           admin_probe_app_type_np_gateway,
                           zpn_npgateway_admin_probe_tx_task_update,
                           zpn_npgateway_cfg_override_feature_is_npgateway_admin_probe_enabled,
                           zpn_npgateway_admin_probe_restart,
                           zpn_npgateway_admin_probe_frr_cmds_execute,
                           epoch_s,
                           is_dev_env,
                           is_np_command_probe, // True for NP Gateway
                           zpn_npgateway_get_remote_host_for_s3_upload,
                           NULL,
                           event_log,
                           is_zpath_config_override_inited);
    if (res) {
        NPGWD_LOG(AL_NOTICE, "NP Gateway probe init failed");
        return NPGWD_RESULT_ERR;
    }

    return NPGWD_RESULT_NO_ERROR;
}

int
zpn_npgateway_admin_probe_stats_fill(void*     cookie,
                                 int       counter,
                                 void*     structure_data)
{
    struct admin_probe_stats*    out_data;

    (void)cookie;

    out_data = (struct admin_probe_stats*)structure_data;

    admin_probe_stats_fill(out_data);

    return NPGWD_RESULT_NO_ERROR;
}
