/*
 * zpn_npgateway_logging.h. Copyright (C) 2024 Zscaler, Inc. All Rights Reserved.
 *
 * NP Gateway logging.
 *
 */

#ifndef __ZPN_NPGATEWAY_LOGGING_H__
#define __ZPN_NPGATEWAY_LOGGING_H__

#include "argo/argo.h"
#include "zpath_lib/zpath_app.h"

#define NPGWD_DEBUG_REDUNDANCY_IDX                       (uint64_t)0x00000001

extern uint64_t npgwd_debug_log;

#define NPGWD_DEBUG_LOG_NAMES {              \
        "redundancy",                        \
        NULL                                 \
}

#define NPGWD_LOG(priority, format...) ARGO_LOG(zpath_event_collection, \
                                                priority, "zpn_npgatewayd", ##format)
#define NPGWD_DEBUG(condition, format...) ARGO_DEBUG_LOG(condition, zpath_event_collection, argo_log_priority_debug, "zpn_npgatewayd", ##format)
#define NPGWD_DEBUG_REDUNDANCY(format...) NPGWD_DEBUG(npgwd_debug_log & NPGWD_DEBUG_REDUNDANCY_IDX, ##format)

int zpn_npgateway_debug_init();

#endif /* __ZPN_NPGATEWAY_LOGGING_H__ */
