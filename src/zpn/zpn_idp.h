/*
 * zpn_idp.h. Copyright (C) 2015 Zscaler Inc, All Rights Reserved
 */

#ifndef _ZPN_IDP_H_
#define _ZPN_IDP_H_

#include "argo/argo.h"
#include <openssl/ssl.h>

#include "zpath_misc/zmicro_heap.h"

struct zpn_idp                                 /* _ARGO: object_definition */
{
    /* Standard SQL fields. */
    int64_t sequence;                          /* _ARGO: integer, sequence */
    int32_t modified_time;                     /* _ARGO: integer */
    int32_t creation_time;                     /* _ARGO: integer */
    int64_t modifiedby_userid;                 /* _ARGO: integer */
    int8_t deleted;                            /* _ARGO: integer, deleted */
    int64_t request_id;                        /* _ARGO: integer, nodb, reqid */

    /* Unique ID for idp */
    int64_t id;                                /* _ARGO: integer, key */

    /* Unique ID for idp */
    int64_t gid;                                /* _ARGO: integer */

    char *name;                                /* _ARGO: string */
    char *description;                         /* _ARGO: string */

    int64_t customer_gid;                      /* _ARGO: integer, index */

    char *login_url;                           /* _ARGO: string */
    int64_t certificate_id;                    /* _ARGO: integer */
    int64_t *certificate_ids;                  /* _ARGO: integer */
    int certificate_ids_count;                 /* _ARGO: quiet, integer, count: certificate_ids */
    int64_t *certificate_gids;                 /* _ARGO: integer */
    int certificate_gids_count;                /* _ARGO: quiet, integer, count: certificate_gids */
    int auto_provision_clients;                /* _ARGO: integer */
    int sign_saml_request;                     /* _ARGO: integer */
    char *login_name_attribute;                /* _ARGO: string */
    char *entity_id;                           /* _ARGO: string */
    int redirect_binding;                      /* _ARGO: integer */
    int use_custom_sp_metadata;                /* _ARGO: integer */
    int64_t sp_signing_cert_id;                /* _ARGO: integer */

    char **domain_list;                        /* _ARGO: string */
    int domain_list_count;                     /* _ARGO: integer, quiet, count:domain_list */

    char **sso_type;                           /* _ARGO: string */
    int sso_type_count;                        /* _ARGO: integer, quiet, count:sso_type */

    int scim_enabled;                          /* _ARGO: integer */
    int enable_scim_based_policy;              /* _ARGO: integer */
    int disable_saml_based_policy;             /* _ARGO: integer */
    int reauth_on_user_update;                 /* _ARGO: integer */
    int arbitrary_domains_accepted;            /* _ARGO: integer */

    int oneidentity_enabled;                   /* _ARGO: integer */
    char *iam_idp_id;                          /* _ARGO: string */
    int force_auth;                            /* _ARGO: integer */
    int disabled;                              /* _ARGO: integer */
    unsigned is_user_idp: 1;
};

extern struct argo_structure_description *zpn_idp_description;

/* Get all IDPs for a customer... */
int zpn_idp_get_customer_gid(int64_t customer_gid,
                             struct zpn_idp **idps,
                             size_t *count,
                             wally_response_callback_f callback_f,
                             void *callback_cookie,
                             int64_t callback_id) __attribute__((weak));

int zpn_idp_oneidentity_hosted_validate(int is_oneidentity_auth,
                                        struct zpn_idp *idp);

int zpn_idp_init(struct wally *single_tenant_wally, int64_t tenant_gid, int fully_load, int register_with_zpath_table, wally_row_callback_f *row_cb);

void argo_zpn_idp_init();

int zpn_idp_table_load(int64_t customer_gid,
                       wally_response_callback_f callback_f,
                       void *callback_cookie,
                       int64_t callback_id);

#endif /* _ZPN_IDP_H_ */
