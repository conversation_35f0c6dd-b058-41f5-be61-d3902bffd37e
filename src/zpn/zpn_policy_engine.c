/*
 * zpn_policy_engine.c. Copyright (C) 2019 Zscaler Inc. All Rights Reserved
 */

#include "zpath_misc/zpath_misc.h"
#include "zpath_lib/zpath_lib.h"
#include "zpath_lib/zpath_debug.h"
#include "zpn/zpn_lib.h"
#include "zhash/zhash_table.h"
#include "zevent/zevent.h"
#include "zpath_lib/zpath_config_override_keys.h"
#include "zpath_lib/zpath_config_override.h"
#include <string.h>

#include "zpn/zpn_scope.h"
#include "zpn/zpn_policy_engine.h"
#include "zpn/zpn_policy_engine_private.h"
#include "zpn/zpn_policy_engine_build.h"
#include "zpn/zpn_broker_client_apps_build.h"
#include "zpn/zpn_rule.h"
#include "zpn/zpn_rule_to_pse_group.h"
#include "zpn/zpn_policy_set.h"
#include "zpn/zpn_customer.h"
#include "zpn/zpn_broker_common.h"

/* 10 minutes */
#define POLICY_BUILD_FAILURE_DEFER_US   (MINUTE_TO_US(10))

/* 1s, used for Unit test, if change the value here, you may need to update et-67862.sh respectively*/
#define POLICY_BUILD_FAILURE_DEFER_US_TEST   (1000*1000)

int64_t g_policy_rebuild_backoff_hard_disabled = POLICY_REBUILD_BACKOFF_FEATURE_HARD_DISABLED_DEFAULT;
int64_t g_policy_rebuild_backoff = POLICY_REBUILD_BACKOFF_FEATURE_DEFAULT;
int64_t g_policy_rebuild_backoff_interval_s = POLICY_REBUILD_BACKOFF_INTERVAL_SEC_DEFAULT;
int64_t g_policy_rebuild_backoff_periodic_check_interval_s = POLICY_REBUILD_BACKOFF_PERIODIC_CHECK_INTERVAL_SEC_DEFAULT;
static int zpe_is_policy_rebuild_backoff_supported = 0;

/* All scopes' policies, mapping scope => zpe_policy */
static struct zhash_table *policy_table;

/* Locks changes to policy table. Lookups in policy table are
 * lockless */
static zpath_mutex_t policy_table_lock;

static int zpe_is_policy_table_initialized = 0;

static zpe_policy_change_f *change_callbacks[100];
static int change_callbacks_count = 0;

struct zpath_allocator zpe_allocator = ZPATH_ALLOCATOR_INIT("zpe");

struct zpe_policy_config_change_stats policy_config_change_stats = {0};

static int64_t tickle_defer_us = 1000000;

struct zpe_policy_build_stats policy_build_stats[ZPE_POLICY_TYPE_COUNT][ZTHREAD_MAX_THREADS] = {{{0}}};

struct zpe_policy_rebuild_stats {
    int64_t policy_rebuild_req_received;
    int64_t policy_rebuild_scheduled;
    int64_t policy_rebuild_num_max_backoff_triggered;
};
static struct zpe_policy_rebuild_stats policy_rebuild_stats[ZTHREAD_MAX_THREADS] = {{0}};

/* Checks Policy Rebuild Backoff config override feature. By default, it's disabled. */
static uint8_t zpn_broker_is_policy_rebuild_backoff_feature_enabled(int64_t customer_gid)
{
    int64_t config_value = POLICY_REBUILD_BACKOFF_FEATURE_DEFAULT;

    if (zpe_is_policy_rebuild_backoff_supported && zpath_config_override_is_int_configured(POLICY_REBUILD_BACKOFF_FEATURE, customer_gid)) {
        /* We have a config for customer level which gets more priority */
        config_value = zpath_config_override_get_config_int(POLICY_REBUILD_BACKOFF_FEATURE,
                                                            &config_value,
                                                            POLICY_REBUILD_BACKOFF_FEATURE_DEFAULT,
                                                            customer_gid,
                                                            (int64_t) 0);
    } else {
        config_value = g_policy_rebuild_backoff;
    }

    return config_value ? 1 : 0;
}

static uint8_t zpn_broker_is_policy_rebuild_backoff_enabled(int64_t customer_gid) {

    if (is_unit_test()) {
        return 0;
    }

    if (g_policy_rebuild_backoff_hard_disabled) {
        return 0;
    }

    return zpn_broker_is_policy_rebuild_backoff_feature_enabled(customer_gid);
}

/* Checks Policy Rebuild Backoff Interval config override for given customer */
static int64_t zpn_broker_policy_rebuild_backoff_interval_s(int64_t customer_gid)
{
    int64_t config_value = POLICY_REBUILD_BACKOFF_INTERVAL_SEC_DEFAULT;

    if (zpe_is_policy_rebuild_backoff_supported && zpath_config_override_is_int_configured(POLICY_REBUILD_BACKOFF_INTERVAL_SEC, customer_gid)) {
        /* We have a config for customer level which gets more priority */
        config_value = zpath_config_override_get_config_int(POLICY_REBUILD_BACKOFF_INTERVAL_SEC,
                                                            &config_value,
                                                            POLICY_REBUILD_BACKOFF_INTERVAL_SEC_DEFAULT,
                                                            customer_gid,
                                                            (int64_t) 0);
    } else {
        config_value = g_policy_rebuild_backoff_interval_s;
    }

    return config_value;
}

/* Checks Policy Rebuild Backoff Periodic Check Interval config override for given customer */
static int64_t zpn_broker_policy_rebuild_backoff_periodic_check_interval_s(int64_t customer_gid)
{
    int64_t config_value = POLICY_REBUILD_BACKOFF_PERIODIC_CHECK_INTERVAL_SEC_DEFAULT;

    if (zpe_is_policy_rebuild_backoff_supported && zpath_config_override_is_int_configured(POLICY_REBUILD_BACKOFF_PERIODIC_CHECK_INTERVAL_SEC, customer_gid)) {
        /* We have a config for customer level which gets more priority */
        config_value = zpath_config_override_get_config_int(POLICY_REBUILD_BACKOFF_PERIODIC_CHECK_INTERVAL_SEC,
                                                            &config_value,
                                                            POLICY_REBUILD_BACKOFF_PERIODIC_CHECK_INTERVAL_SEC_DEFAULT,
                                                            customer_gid,
                                                            (int64_t) 0);
    } else {
        config_value = g_policy_rebuild_backoff_periodic_check_interval_s;
    }

    return config_value;
}

/* Forward declarations */
static int build_policy_cb(void *void_cookie, struct wally_registrant *registrant, struct wally_table *table, int64_t int_cookie, int row_count);
static int build_policy_cb_lock_improvement(void *void_cookie, struct wally_registrant *registrant, struct wally_table *table, int64_t int_cookie, int row_count);

static zpath_mutex_t register_lock;

int zpe_policy_set_size(struct zpe_policy_set *set) {
    return set->zpe_rule_count;
}

void zpe_register_for_change(zpe_policy_change_f *callback_f)
{
    ZPATH_MUTEX_LOCK(&register_lock, __FILE__, __LINE__);
    if (change_callbacks_count >= (sizeof(change_callbacks) / sizeof(change_callbacks[0]))) {
        ZPN_LOG(AL_CRITICAL, "Too many callbacks");
        ZPATH_MUTEX_UNLOCK(&register_lock, __FILE__, __LINE__);
        return;
    }
    change_callbacks[change_callbacks_count] = callback_f;
    change_callbacks_count++;
    ZPATH_MUTEX_UNLOCK(&register_lock, __FILE__, __LINE__);
}

static void notify_policy_change(void *cookie1, void *cookie2)
{
    struct zpe_policy *policy = cookie1;
    int i;

    for (i = 0; i < change_callbacks_count; i++) {
        (change_callbacks[i])(policy->scope_gid);
    }
}

static void free_grp_to_apps_callback(void *element, void *cookie)
{
    struct zhash_table *app_set = element;
    zhash_table_free(app_set);
}
void zpe_free_grp_to_apps(struct zhash_table *grp_to_apps)
{
    if (grp_to_apps) {
        zhash_table_free_and_call(grp_to_apps, free_grp_to_apps_callback, NULL);
    }

}

static void policy_build_fail_cb(void *cookie1, void *cookie2 __attribute__((unused)), int64_t int_cookie)
{
    struct zpe_policy *policy= cookie1;
    int zthread_num = zthread_self()->stack.thread_num;
    enum zpe_policy_type policy_type = int_cookie;
    int threshold_us = is_unit_test()? POLICY_BUILD_FAILURE_DEFER_US_TEST:POLICY_BUILD_FAILURE_DEFER_US;

    ZPATH_MUTEX_LOCK(&(policy->lock), __FILE__, __LINE__);
    int build_fail = policy->build_fail[policy_type];
    int64_t build_fail_time_us = policy->build_fail_time_us[policy_type];
    ZPATH_MUTEX_UNLOCK(&(policy->lock), __FILE__, __LINE__);

    if (build_fail) {
        int64_t delta_us = epoch_us() - build_fail_time_us;
        if (delta_us >= threshold_us) {
            policy_build_stats[policy_type][zthread_num].num_total_failures_long++;
            int cnt = __sync_add_and_fetch_4(&(policy->build_fail_count[policy_type]), 1);

            ZPN_LOG(AL_CRITICAL, "failed to build policy for customer %"PRId64", scope %"PRId64" since %"PRId64"(%"PRId64" msec ago), fail_count:%d, policy type: %s",
                                 policy->customer_gid, policy->scope_gid, build_fail_time_us, delta_us, cnt, zpe_policy_type_string(policy_type));
        } else {
            ZPN_LOG(AL_NOTICE, "policy build failure is still not recovered for customer %"PRId64", scope %"PRId64", failed at time %"PRId64", policy type: %s",
                               policy->customer_gid, policy->scope_gid, build_fail_time_us, zpe_policy_type_string(policy_type));
        }
    } else {
        ZPN_LOG(AL_NOTICE, "policy build failure has been recovered for customer %"PRId64", scope %"PRId64", policy type: %s",
                           policy->customer_gid, policy->scope_gid, zpe_policy_type_string(policy_type));
    }
}

/*
 * Initiate policy retrieval. This should only be done in one context
 * at a time.
 *
 * always called with lock.
 */
static void fetch_policies_no_lock_improvement(struct zpe_policy *policy)
{
    int res;
    int i;
    struct zthread_info *self_zthread_info = zthread_self();
    int zthread_num = self_zthread_info->stack.thread_num;

    /* app group => set of applications */
    struct zhash_table *grp_to_apps =  zhash_table_alloc(&zpe_allocator);

    for (i = ZPE_POLICY_TYPE_FIRST; i <= ZPE_POLICY_TYPE_LAST; i++) {
        if ((!policy->policies_built[i]->policy_version) || (policy->policies_built[i]->policy_version != policy->version)) {
            struct zpe_policy_set *new_set = NULL;
            struct zpe_policy_set_meta *new_set_meta = NULL;
            struct zhash_table *new_app_to_rules = NULL;
            struct zpe_mini_policy_set *new_rules_for_all = NULL;
            struct zpe_policy_built *new_policy_built = NULL;
            const int64_t build_started_us = monotime_us();

            if (policy->policies_built[i]->policy_set) {
                ZPATH_LOG(AL_NOTICE, "Rebuilding policy set %s for customer %ld scope %ld 0x%lx ... ",
                                     zpe_policy_type_string(i), (long)policy->customer_gid,
                                     (long)policy->scope_gid, (long)policy->scope_gid);
            } else {
                ZPATH_LOG(AL_NOTICE, "Building first time policy set %s for customer %ld scope %ld 0x%lx ... ",
                                      zpe_policy_type_string(i), (long)policy->customer_gid,
                                      (long)policy->scope_gid, (long)policy->scope_gid);
            }
            res = zpe_generate_set_from_config(policy->scope_gid,
                                               i,
                                               grp_to_apps,
                                               &(policy->async_count),
                                               &new_set,
                                               &new_set_meta,
                                               &new_app_to_rules,
                                               &new_rules_for_all,
                                               build_policy_cb,
                                               policy,
                                               0);
            if (res) {
                if (res != ZPATH_RESULT_ASYNCHRONOUS) {
                    ZPN_LOG(AL_CRITICAL, "%ld %ld: Bad generate result: %s",
                                         (long)policy->customer_gid, (long)policy->scope_gid, zpn_result_string(res));
                    policy_build_stats[i][zthread_num].num_total_failures++;
                    if (policy->build_fail[i] == 0) {
                        policy->build_fail[i] = 1;
                        policy->build_fail_time_us[i] = epoch_us();
                        zevent_big_defer(policy_build_fail_cb, policy, NULL, i, is_unit_test()? POLICY_BUILD_FAILURE_DEFER_US_TEST:POLICY_BUILD_FAILURE_DEFER_US);
                    }
                }
            } else {
                /* No error... */
                if (!new_set) {
                    /* Huh?? */
                    ZPN_LOG(AL_CRITICAL, "%ld %ld: Bad new set", (long)policy->customer_gid, (long)policy->scope_gid);
                } else if (!new_set_meta) {
                    ZPN_LOG(AL_CRITICAL, "%ld %ld: No new set meta... out of memory?",
                                          (long)policy->customer_gid, (long)policy->scope_gid);
                } else if (!new_app_to_rules) {
                    ZPN_LOG(AL_CRITICAL, "%ld %ld: No new_app_to_rules... out of memory?",
                                        (long)policy->customer_gid, (long)policy->scope_gid);
                } else if (!new_rules_for_all) {
                    ZPN_LOG(AL_CRITICAL, "%ld %ld: No new_rules_for_all... out of memory?",
                                         (long)policy->customer_gid, (long)policy->scope_gid);
                } else {
                    new_set_meta->customer_gid = policy->customer_gid;
                    new_set_meta->scope_gid = policy->scope_gid;
                    new_set_meta->policy_type = i;

                    if (policy->policies_built[i]->policy_set) {
                        policy->policies_built[i]->policy_version = policy->version;
                        ZPATH_LOG(AL_NOTICE, "Rebuilding policy set %s for customer %ld scope %ld 0x%lx ... COMPLETE,"
                                  " old_set = %ld, old_set_sequence = %ld, new_set = %ld, new_set_sequence = %ld",
                                  zpe_policy_type_string(i), (long) policy->customer_gid,
                                  (long) policy->scope_gid, (long) policy->scope_gid,
                                  (long) policy->policies_built[i]->policy_meta->gid,
                                  (long) policy->policies_built[i]->policy_meta->sequence,
                                  (long) new_set_meta->gid, (long) new_set_meta->sequence);
                    } else {
                        ZPATH_LOG(AL_NOTICE, "Building first time policy set %s for customer %ld scope %ld 0x%lx ... COMPLETE."
                                  " new_set = %ld, new_set_sequence = %ld",
                                  zpe_policy_type_string(i), (long) policy->customer_gid,
                                  (long) policy->scope_gid, (long) policy->scope_gid,
                                  (long) new_set_meta->gid, (long) new_set_meta->sequence);
                    }

                    if (policy->policies_built[i]->policy_set) {
                        ZPE_FREE_SLOW(policy->policies_built[i]->policy_set);
                    }
                    if (policy->policies_built[i]->policy_meta) {
                        ZPE_FREE_SLOW(policy->policies_built[i]->policy_meta);
                    }
                    zpe_free_app_to_rules_slow(policy->policies_built[i]->app_to_rules);
                    zpe_free_mini_policy_set_slow(policy->policies_built[i]->rules_for_all);
                    ZPE_FREE_SLOW(policy->policies_built[i]);

                    new_policy_built = ZPE_CALLOC(sizeof(*new_policy_built));
                    new_policy_built->policy_version = policy->version;
                    new_policy_built->policy_set = new_set;
                    new_policy_built->policy_meta = new_set_meta;
                    new_policy_built->app_to_rules = new_app_to_rules;
                    new_policy_built->rules_for_all = new_rules_for_all;
                    policy->policies_built[i] = new_policy_built;

                    zevent_defer(notify_policy_change, policy, NULL, 0);
                    if (policy->build_fail[i]) policy->build_fail[i] = 0;

                    if (new_policy_built->policy_set->zpe_rule_count > 15000) {
                        policy_build_stats[i][zthread_num].num_policy_gt_15k++;
                    } else if (new_policy_built->policy_set->zpe_rule_count > 10000) {
                        policy_build_stats[i][zthread_num].num_policy_gt_10k++;
                    } else if (new_policy_built->policy_set->zpe_rule_count > 7000) {
                        policy_build_stats[i][zthread_num].num_policy_gt_7k++;
                    } else if (new_policy_built->policy_set->zpe_rule_count > 5000) {
                        policy_build_stats[i][zthread_num].num_policy_gt_5k++;
                    }
                }
            }
            const int64_t build_time_us = monotime_us() - build_started_us;
            if (build_time_us > SECOND_TO_US(10)) {
                policy_build_stats[i][zthread_num].num_build_time_gt_10s++;
            } else if (build_time_us > SECOND_TO_US(5)) {
                policy_build_stats[i][zthread_num].num_build_time_gt_5s++;
            } else if (build_time_us > SECOND_TO_US(2)) {
                policy_build_stats[i][zthread_num].num_build_time_gt_2s++;
            }
            policy_build_stats[i][zthread_num].num_total_builds++;
            policy_build_stats[i][zthread_num].total_build_time_us += build_time_us;
            if (build_time_us > policy_build_stats[i][zthread_num].max_build_time_us) {
                policy_build_stats[i][zthread_num].max_build_time_us = build_time_us;
            }
        }
    }

    zpe_free_grp_to_apps(grp_to_apps);

    for (i = ZPE_POLICY_TYPE_FIRST; i <= ZPE_POLICY_TYPE_LAST; i++) {
        if ((!policy->policies_built[i]->policy_set)|| (policy->policies_built[i]->policy_version != policy->version)) {
            break;
        }
    }

    if (i > ZPE_POLICY_TYPE_LAST) {
        /* We have a full set of policies */
        if (!policy->policy_ts.current_rebuild_finished_us) {
            policy->policy_ts.current_rebuild_finished_us = epoch_us();
            policy->policy_ts.last_rebuild_finished_us =policy->policy_ts.current_rebuild_finished_us;
            policy->policy_ts.last_rebuild_started_us = policy->policy_ts.current_rebuild_started_us;
            policy->policy_ts.last_rebuild_time_us = policy->policy_ts.last_rebuild_finished_us - policy->policy_ts.last_rebuild_started_us;
            policy->policy_ts.current_rebuild_started_us = 0;
        }
    }
}

/*
 * Initiate policy retrieval. This should only be done in one context
 * at a time.
 *
 */
static void fetch_policies_lock_improvement(struct zpe_policy *policy)
{
    int res;
    int i;
    struct zthread_info *self_zthread_info = zthread_self();
    int zthread_num = self_zthread_info->stack.thread_num;

    ZPATH_MUTEX_LOCK(&(policy->lock), __FILE__, __LINE__);
    if (policy->policy_build_in_progress) {
        policy->policy_rebuild_req_rcved_during_build = 1;
        policy->policy_concurrent_rebuild_req_received++;
        ZPN_LOG(AL_INFO,"%"PRId64": Will dispatch new build request once current build completes, build in progress %d, rebuild req recived during build %d",
                        policy->scope_gid, policy->policy_build_in_progress, policy->policy_rebuild_req_rcved_during_build);
        ZPATH_MUTEX_UNLOCK(&(policy->lock), __FILE__, __LINE__);
        return;
    }
    policy->number_of_build++;
    policy->policy_build_in_progress = 1;
    ZPATH_MUTEX_UNLOCK(&(policy->lock), __FILE__, __LINE__);

    /* app group => set of applications */
    struct zhash_table *grp_to_apps =  zhash_table_alloc(&zpe_allocator);

    for (i = ZPE_POLICY_TYPE_FIRST; i <= ZPE_POLICY_TYPE_LAST; i++) {
        if ((!policy->policies_built[i]->policy_version) || (policy->policies_built[i]->policy_version != policy->version)) {
            struct zpe_policy_set *new_set = NULL;
            struct zpe_policy_set_meta *new_set_meta = NULL;
            struct zhash_table *new_app_to_rules = NULL;
            struct zpe_mini_policy_set *new_rules_for_all = NULL;
            struct zpe_policy_built *new_policy_built = NULL;
            const int64_t build_started_us = monotime_us();

            if (policy->policies_built[i]->policy_set) {
                ZPATH_LOG(AL_NOTICE, "Rebuilding policy set %s for customer %ld scope %ld 0x%lx ... ",
                                     zpe_policy_type_string(i), (long)policy->customer_gid,
                                     (long)policy->scope_gid, (long)policy->scope_gid);
            } else {
                ZPATH_LOG(AL_NOTICE, "Building first time policy set %s for customer %ld scope %ld 0x%lx ... ",
                                      zpe_policy_type_string(i), (long)policy->customer_gid,
                                      (long)policy->scope_gid, (long)policy->scope_gid);
            }
            res = zpe_generate_set_from_config(policy->scope_gid,
                                               i,
                                               grp_to_apps,
                                               &(policy->async_count),
                                               &new_set,
                                               &new_set_meta,
                                               &new_app_to_rules,
                                               &new_rules_for_all,
                                               build_policy_cb_lock_improvement,
                                               policy,
                                               0);
            if (res) {
                if (res != ZPATH_RESULT_ASYNCHRONOUS) {
                    ZPN_LOG(AL_CRITICAL, "%ld %ld: Bad generate result: %s",
                                         (long)policy->customer_gid, (long)policy->scope_gid, zpn_result_string(res));
                    policy_build_stats[i][zthread_num].num_total_failures++;
                    if (policy->build_fail[i] == 0) {
                        policy->build_fail[i] = 1;
                        policy->build_fail_time_us[i] = epoch_us();
                        zevent_big_defer(policy_build_fail_cb, policy, NULL, i, is_unit_test()? POLICY_BUILD_FAILURE_DEFER_US_TEST:POLICY_BUILD_FAILURE_DEFER_US);
                    }
                }
            } else {
                /* No error... */
                if (!new_set) {
                    /* Huh?? */
                    ZPN_LOG(AL_CRITICAL, "%ld %ld: Bad new set", (long)policy->customer_gid, (long)policy->scope_gid);
                } else if (!new_set_meta) {
                    ZPN_LOG(AL_CRITICAL, "%ld %ld: No new set meta... out of memory?",
                                          (long)policy->customer_gid, (long)policy->scope_gid);
                } else if (!new_app_to_rules) {
                    ZPN_LOG(AL_CRITICAL, "%ld %ld: No new_app_to_rules... out of memory?",
                                        (long)policy->customer_gid, (long)policy->scope_gid);
                } else if (!new_rules_for_all) {
                    ZPN_LOG(AL_CRITICAL, "%ld %ld: No new_rules_for_all... out of memory?",
                                         (long)policy->customer_gid, (long)policy->scope_gid);
                } else {
                    new_set_meta->customer_gid = policy->customer_gid;
                    new_set_meta->scope_gid = policy->scope_gid;
                    new_set_meta->policy_type = i;

                    ZPATH_MUTEX_LOCK(&(policy->policy_lock), __FILE__, __LINE__);
                    if (policy->policies_built[i]->policy_set) {
                        policy->policies_built[i]->policy_version = policy->version;
                        ZPATH_LOG(AL_NOTICE, "Rebuilding policy set %s for customer %ld scope %ld 0x%lx ... COMPLETE,"
                                  " old_set = %ld, old_set_sequence = %ld, new_set = %ld, new_set_sequence = %ld",
                                  zpe_policy_type_string(i), (long) policy->customer_gid,
                                  (long) policy->scope_gid, (long) policy->scope_gid,
                                  (long) policy->policies_built[i]->policy_meta->gid,
                                  (long) policy->policies_built[i]->policy_meta->sequence,
                                  (long) new_set_meta->gid, (long) new_set_meta->sequence);
                    } else {
                        ZPATH_LOG(AL_NOTICE, "Building first time policy set %s for customer %ld scope %ld 0x%lx ... COMPLETE."
                                  " new_set = %ld, new_set_sequence = %ld",
                                  zpe_policy_type_string(i), (long) policy->customer_gid,
                                  (long) policy->scope_gid, (long) policy->scope_gid,
                                  (long) new_set_meta->gid, (long) new_set_meta->sequence);
                    }

                    if (policy->policies_built[i]->policy_set) {
                        ZPE_FREE_SLOW(policy->policies_built[i]->policy_set);
                    }
                    if (policy->policies_built[i]->policy_meta) {
                        ZPE_FREE_SLOW(policy->policies_built[i]->policy_meta);
                    }
                    zpe_free_app_to_rules_slow(policy->policies_built[i]->app_to_rules);
                    zpe_free_mini_policy_set_slow(policy->policies_built[i]->rules_for_all);
                    ZPE_FREE_SLOW(policy->policies_built[i]);

                    new_policy_built = ZPE_CALLOC(sizeof(*new_policy_built));
                    new_policy_built->policy_version = policy->version;
                    new_policy_built->policy_set = new_set;
                    new_policy_built->policy_meta = new_set_meta;
                    new_policy_built->app_to_rules = new_app_to_rules;
                    new_policy_built->rules_for_all = new_rules_for_all;
                    policy->policies_built[i] = new_policy_built;

                    ZPATH_MUTEX_UNLOCK(&(policy->policy_lock), __FILE__, __LINE__);

                    zevent_defer(notify_policy_change, policy, NULL, 0);
                    if (policy->build_fail[i]) policy->build_fail[i] = 0;

                    if (new_policy_built->policy_set->zpe_rule_count > 15000) {
                        policy_build_stats[i][zthread_num].num_policy_gt_15k++;
                    } else if (new_policy_built->policy_set->zpe_rule_count > 10000) {
                        policy_build_stats[i][zthread_num].num_policy_gt_10k++;
                    } else if (new_policy_built->policy_set->zpe_rule_count > 7000) {
                        policy_build_stats[i][zthread_num].num_policy_gt_7k++;
                    } else if (new_policy_built->policy_set->zpe_rule_count > 5000) {
                        policy_build_stats[i][zthread_num].num_policy_gt_5k++;
                    }
                }
            }
            const int64_t build_time_us = monotime_us() - build_started_us;
            if (build_time_us > SECOND_TO_US(10)) {
                policy_build_stats[i][zthread_num].num_build_time_gt_10s++;
            } else if (build_time_us > SECOND_TO_US(5)) {
                policy_build_stats[i][zthread_num].num_build_time_gt_5s++;
            } else if (build_time_us > SECOND_TO_US(2)) {
                policy_build_stats[i][zthread_num].num_build_time_gt_2s++;
            }
            policy_build_stats[i][zthread_num].num_total_builds++;
            policy_build_stats[i][zthread_num].total_build_time_us += build_time_us;
            if (build_time_us > policy_build_stats[i][zthread_num].max_build_time_us) {
                policy_build_stats[i][zthread_num].max_build_time_us = build_time_us;
            }
        }
    }

    zpe_free_grp_to_apps(grp_to_apps);

    for (i = ZPE_POLICY_TYPE_FIRST; i <= ZPE_POLICY_TYPE_LAST; i++) {
        if ((!policy->policies_built[i]->policy_set)|| (policy->policies_built[i]->policy_version != policy->version)) {
            break;
        }
    }

    if (i > ZPE_POLICY_TYPE_LAST) {
        ZPATH_MUTEX_LOCK(&(policy->policy_lock), __FILE__, __LINE__);
        /* We have a full set of policies */
        if (!policy->policy_ts.current_rebuild_finished_us) {
            policy->policy_ts.current_rebuild_finished_us = epoch_us();
            policy->policy_ts.last_rebuild_finished_us =policy->policy_ts.current_rebuild_finished_us;
            policy->policy_ts.last_rebuild_started_us = policy->policy_ts.current_rebuild_started_us;
            policy->policy_ts.last_rebuild_time_us = policy->policy_ts.last_rebuild_finished_us - policy->policy_ts.last_rebuild_started_us;
            policy->policy_ts.current_rebuild_started_us = 0;
        }
        ZPATH_MUTEX_UNLOCK(&(policy->policy_lock), __FILE__, __LINE__);
    }

    ZPATH_MUTEX_LOCK(&(policy->lock), __FILE__, __LINE__);
    policy->policy_build_in_progress = 0;
    if (policy->policy_rebuild_req_rcved_during_build) {
        policy->policy_rebuild_req_rcved_during_build = 0;
        ZPN_LOG(AL_INFO,"%"PRId64": version %"PRId64" triggering manual rebuild",
                        policy->scope_gid, policy->version);
        zpe_scope_tickle(policy->scope_gid);
    }
    ZPATH_MUTEX_UNLOCK(&(policy->lock), __FILE__, __LINE__);

}

static void build_policy_cb_deferred(void *cookie1, void *cookie2)
{
    struct zpe_policy *policy= cookie1;
    struct wally_callback_queue *q = NULL;
    int i;

    ZPATH_MUTEX_LOCK(&(policy->policy_lock), __FILE__, __LINE__);

    /* We automatically defer this routine */

    ZPN_DEBUG_ZPE("Build policy CB (deferred), async_count %p %d->%d", &(policy->async_count), policy->async_count, policy->async_count - 1);

    __sync_sub_and_fetch_4(&(policy->async_count), 1);

    if (policy->async_count == 0) {
        /* We build all the policies at once (for latency reasons) */
        fetch_policies_no_lock_improvement(policy);
    }

    /* Check if we have an entry for every policy type, and if there
     * are callbacks registered against it */
    for (i = ZPE_POLICY_TYPE_FIRST; i <= ZPE_POLICY_TYPE_LAST; i++) {
        if (!policy->policies_built[i]->policy_set) break;
    }

    /* A failed policy build can arrive here. In that case we need to
     * let our callbacks all be called back in order to trigger new
     * builds in the future */
    if ((policy->async_count == 0) || (i > ZPE_POLICY_TYPE_LAST)) {
        q = policy->requestors;
        policy->requestors = NULL;
    }

    ZPATH_MUTEX_UNLOCK(&(policy->policy_lock), __FILE__, __LINE__);

    /* If we have full policy set and callbacks to call, call them
     * (now that we are out of our lock) */
    if (q) {
        wally_callback_queue_callback(q);
        wally_callback_queue_destroy(q);
    }
}

static void build_policy_cb_deferred_lock_improvement(void *cookie1, void *cookie2)
{
    struct zpe_policy *policy= cookie1;
    struct wally_callback_queue *q = NULL;
    int i;

    /* We automatically defer this routine */

    ZPN_DEBUG_ZPE("Build policy CB (deferred), async_count %p %d->%d", &(policy->async_count), policy->async_count, policy->async_count - 1);

    __sync_sub_and_fetch_4(&(policy->async_count), 1);

    if (policy->async_count == 0) {
        /* We build all the policies at once (for latency reasons) */
        fetch_policies_lock_improvement(policy);
    }

    /* Check if we have an entry for every policy type, and if there
     * are callbacks registered against it */
    for (i = ZPE_POLICY_TYPE_FIRST; i <= ZPE_POLICY_TYPE_LAST; i++) {
        if (!policy->policies_built[i]->policy_set) break;
    }

    ZPATH_MUTEX_LOCK(&(policy->policy_lock), __FILE__, __LINE__);
    /* A failed policy build can arrive here. In that case we need to
     * let our callbacks all be called back in order to trigger new
     * builds in the future */
    if ((policy->async_count == 0) || (i > ZPE_POLICY_TYPE_LAST)) {
        q = policy->requestors;
        policy->requestors = NULL;
    }

    ZPATH_MUTEX_UNLOCK(&(policy->policy_lock), __FILE__, __LINE__);

    /* If we have full policy set and callbacks to call, call them
     * (now that we are out of our lock) */
    if (q) {
        wally_callback_queue_callback(q);
        wally_callback_queue_destroy(q);
    }
}

/*
 * Callback to resume building policy...
 */
static int build_policy_cb(void *void_cookie, struct wally_registrant *registrant, struct wally_table *table, int64_t int_cookie, int row_count)
{
    struct zpe_policy *policy= void_cookie;
    ZPN_DEBUG_ZPE("Build policy CB (not deferred), async_count %p %d->%d (origin async count = %d)", &(policy->async_count), policy->async_count, policy->async_count - 1, (int) int_cookie);
    zevent_defer(build_policy_cb_deferred, void_cookie, NULL, 0);
    return ZPATH_RESULT_NO_ERROR;
}

/*
 * Callback to resume building policy...
 */
static int build_policy_cb_lock_improvement(void *void_cookie, struct wally_registrant *registrant, struct wally_table *table, int64_t int_cookie, int row_count)
{
    struct zpe_policy *policy= void_cookie;
    ZPN_DEBUG_ZPE("Build policy CB (not deferred), async_count %p %d->%d (origin async count = %d)", &(policy->async_count), policy->async_count, policy->async_count - 1, (int) int_cookie);
    zevent_defer(build_policy_cb_deferred_lock_improvement, void_cookie, NULL, 0);
    return ZPATH_RESULT_NO_ERROR;
}

/*
 * Gets or creates scope policy
 */
static struct zpe_policy *get_or_create_scope_policy(int64_t scope_gid)
{
    struct zpe_policy *policy;
    policy = zhash_table_lookup(policy_table, &scope_gid, sizeof(scope_gid), NULL);
    if (!policy) {
        ZPATH_MUTEX_LOCK(&policy_table_lock, __FILE__, __LINE__);
        policy = zhash_table_lookup(policy_table, &scope_gid, sizeof(scope_gid), NULL);
        if (!policy) {
            int i;
            policy = ZPE_CALLOC(sizeof(*policy));
            policy->scope_gid = scope_gid;
            policy->customer_gid = get_customer_from_scope(scope_gid);
            policy->lock = ZPATH_MUTEX_INIT;
            policy->policy_lock = ZPATH_MUTEX_INIT;
            policy->policy_rebuild_feature_enabled = zpn_broker_is_policy_rebuild_backoff_enabled(policy->customer_gid);
            policy->policy_rebuild_defer_us = zpn_broker_policy_rebuild_backoff_interval_s(policy->customer_gid) * tickle_defer_us;
            policy->policy_rebuild_max_defer_s = zpn_broker_policy_rebuild_backoff_periodic_check_interval_s(policy->customer_gid);
            for (i = 0; i < ZPE_POLICY_TYPE_COUNT; i++) {
                policy->policies_built[i]= ZPE_CALLOC(sizeof(*(policy->policies_built[i])));
            }
            zhash_table_store(policy_table, &scope_gid, sizeof(scope_gid), 0, policy);
        }
        ZPATH_MUTEX_UNLOCK(&(policy_table_lock), __FILE__, __LINE__);
    }
    return policy;
}

static void load_from_wally_cb(void *cookie1, void *cookie2)
{
    struct zpe_policy *policy = cookie1;
    struct wally_table *table = cookie2;
    struct wally_callback_queue *q = NULL;
    int need_tickle = 0;

    ZPATH_MUTEX_LOCK(&(policy->policy_lock), __FILE__, __LINE__);

    if (strcmp(wally_table_name(table), "zpn_policy_set") == 0) policy->policy_set_registration_complete = 1;
    if (strcmp(wally_table_name(table), "zpn_rule") == 0) policy->rule_registration_complete = 1;
    if (strcmp(wally_table_name(table), "zpn_rule_condition_operand") == 0) policy->rule_condition_set_registration_complete = 1;
    if (strcmp(wally_table_name(table), "zpn_rule_condition_set") == 0) policy->rule_condition_operand_registration_complete = 1;

    if (policy->policy_set_registration_complete &&
        policy->rule_registration_complete &&
        policy->rule_condition_set_registration_complete &&
        policy->rule_condition_operand_registration_complete) {
        if (policy->wally_registration_complete) {
            ZPN_LOG(AL_ERROR, "scope %ld: wally registration completed multiple times", (long) policy->scope_gid);
        } else {
            ZPN_LOG(AL_NOTICE, "scope %ld: Policy prefetch complete. Initiating policy build", (long) policy->scope_gid);
        }
        policy->wally_registration_complete = 1;
        q = policy->requestors;
        policy->requestors = NULL;
        need_tickle = 1;
    }

    ZPATH_MUTEX_UNLOCK(&(policy->policy_lock), __FILE__, __LINE__);

    if (q) {
        ZPN_DEBUG_ZPE("scope %ld: Calling deffered wally download callbacks", (long) policy->scope_gid);
        wally_callback_queue_callback(q);
        wally_callback_queue_destroy(q);
    }
    if (need_tickle) zpe_scope_tickle(policy->scope_gid);
}

int load_from_wally(void *response_callback_cookie,
                    struct wally_registrant *registrant,
                    struct wally_table *table,
                    int64_t request_id,
                    int row_count)
{

    zevent_defer(load_from_wally_cb, response_callback_cookie, table, 0);
    return ZPATH_RESULT_NO_ERROR;
}

int zpe_get_policy_no_lock_improvement(struct zpe_policy *policy,
                                       enum zpe_policy_type policy_type,
                                       struct zpe_policy_built **policy_built,
                                       wally_response_callback_f *callback,
                                       void *void_cookie,
                                       int64_t int_cookie,
                                       hold_param_callback_f *hold_callback,
                                       void *param_cookie,
                                       int *being_built)
{
    /* The fast cache lookup case, where policy is already built at
     * least once: */
    if (policy->policies_built[policy_type]->policy_set) {
        /* Fast cache lookup case, but we need to rebuild policy
         * because it has changed */
        if (being_built) *being_built = 1;
        ZPATH_MUTEX_LOCK(&(policy->policy_lock), __FILE__, __LINE__);

        if (!policy->policy_ts.current_rebuild_started_us) {
            policy->policy_ts.current_rebuild_started_us = epoch_us();
            policy->policy_ts.current_rebuild_finished_us = 0;
        }
        if (policy->async_count) {
            /* Rebuild is in progress, we don't need to do our own */
        } else {
            /* Rebuild hasn't started, and we need it! */
            fetch_policies_no_lock_improvement(policy);
            /* If we got updated policy as a result of this build,
                * we will assign it here */
            if (policy_built) *policy_built = policy->policies_built[policy_type];
        }
        ZPATH_MUTEX_UNLOCK(&(policy->policy_lock), __FILE__, __LINE__);
        return ZPATH_RESULT_NO_ERROR;
    }

    /* We don't have a policy set... Gotta get one */
    ZPATH_MUTEX_LOCK(&(policy->policy_lock), __FILE__, __LINE__);

    if (!policy->policy_ts.current_rebuild_started_us) {
        policy->policy_ts.current_rebuild_started_us = epoch_us();
        policy->policy_ts.current_rebuild_finished_us = 0;
    }

    /* If we have an outstanding build request, we will put this
     * request on the queue, and just wait for them all to complete */
    if (policy->requestors) {
        if (hold_callback && param_cookie) (*hold_callback)(param_cookie);
        wally_callback_queue_add(policy->requestors, callback, void_cookie, int_cookie);
        ZPATH_MUTEX_UNLOCK(&(policy->policy_lock), __FILE__, __LINE__);
        if (being_built) *being_built = 1;
        return ZPATH_RESULT_ASYNCHRONOUS;
    }

    /* Get em */
    fetch_policies_no_lock_improvement(policy);

    ZPATH_MUTEX_UNLOCK(&(policy->policy_lock), __FILE__, __LINE__);
    return ZPATH_RESULT_NO_ERROR;
}

int zpe_get_policy_lock_improvement(struct zpe_policy *policy,
                                    enum zpe_policy_type policy_type,
                                    struct zpe_policy_built **policy_built,
                                    wally_response_callback_f *callback,
                                    void *void_cookie,
                                    int64_t int_cookie,
                                    hold_param_callback_f *hold_callback,
                                    void *param_cookie,
                                    int *being_built)
{
    /* The fast cache lookup case, where policy is already built at
     * least once: */
    if (policy->policies_built[policy_type]->policy_set) {
        /* Fast cache lookup case, but we need to rebuild policy
         * because it has changed */
        if (being_built) *being_built = 1;
        ZPATH_MUTEX_LOCK(&(policy->policy_lock), __FILE__, __LINE__);

        if (!policy->policy_ts.current_rebuild_started_us) {
            policy->policy_ts.current_rebuild_started_us = epoch_us();
            policy->policy_ts.current_rebuild_finished_us = 0;
        }
        ZPATH_MUTEX_UNLOCK(&(policy->policy_lock), __FILE__, __LINE__);
        if (policy->async_count) {
            /* Rebuild is in progress, we don't need to do our own */
        } else {
            /* Rebuild hasn't started, and we need it! */
            fetch_policies_lock_improvement(policy);
            /* If we got updated policy as a result of this build,
             * we will assign it here */
            if (policy_built) *policy_built = policy->policies_built[policy_type];
        }
        return ZPATH_RESULT_NO_ERROR;
    }

    /* We don't have a policy set... Gotta get one */
    ZPATH_MUTEX_LOCK(&(policy->policy_lock), __FILE__, __LINE__);

    if (!policy->policy_ts.current_rebuild_started_us) {
        policy->policy_ts.current_rebuild_started_us = epoch_us();
        policy->policy_ts.current_rebuild_finished_us = 0;
    }

    /* If we have an outstanding build request, we will put this
     * request on the queue, and just wait for them all to complete */
    if (policy->requestors) {
        if (hold_callback && param_cookie) (*hold_callback)(param_cookie);
        wally_callback_queue_add(policy->requestors, callback, void_cookie, int_cookie);
        ZPATH_MUTEX_UNLOCK(&(policy->policy_lock), __FILE__, __LINE__);
        if (being_built) *being_built = 1;
        return ZPATH_RESULT_ASYNCHRONOUS;
    }

    ZPATH_MUTEX_UNLOCK(&(policy->policy_lock), __FILE__, __LINE__);

    /* Get em */
    fetch_policies_lock_improvement(policy);
    return ZPATH_RESULT_NO_ERROR;
}

int zpe_get_current_policy_1(int64_t scope_gid,
                             enum zpe_policy_type policy_type,
                             struct zpe_policy_built **policy_built,
                             wally_response_callback_f *callback,
                             void *void_cookie,
                             int64_t int_cookie,
                             hold_param_callback_f *hold_callback,
                             void *param_cookie,
                             int *being_built,
                             struct zpe_policy_built_timestamp *policy_ts)
{
    struct zpe_policy *policy;
    int64_t customer_gid  = get_customer_from_scope(scope_gid);
    int res = ZPATH_RESULT_NO_ERROR;
    int is_rebuild = 0;
    if (being_built) *being_built = 0;

    /* Get or create policy entry. This entry never disappears. */
    policy = get_or_create_scope_policy(scope_gid);

    /* coverity[thread2_checks_field_early : FALSE] */
    if (!policy->wally_registered) {
        ZPATH_MUTEX_LOCK(&(policy->policy_lock), __FILE__, __LINE__);
        if (!policy->wally_registered) {
            ZPN_DEBUG_ZPE("%ld: customer initial registration beginning", (long) customer_gid);
            res = zpn_policy_set_register_customer(customer_gid, load_from_wally, policy, 0);
            if (res != ZPATH_RESULT_ASYNCHRONOUS) {
                ZPN_LOG(AL_CRITICAL, "customer %ld zpn_policy_set_register_customer returned %s", (long) customer_gid, zpath_result_string(res));
            }
            res = zpn_rule_register_customer(customer_gid, load_from_wally, policy, 0);
            if (res != ZPATH_RESULT_ASYNCHRONOUS) {
                ZPN_LOG(AL_CRITICAL, "customer %ld zpn_rule_register_customer returned %s", (long) customer_gid, zpath_result_string(res));
            }
            res = zpn_rule_condition_set_register_customer(customer_gid, load_from_wally, policy, 0);
            if (res != ZPATH_RESULT_ASYNCHRONOUS) {
                ZPN_LOG(AL_CRITICAL, "customer %ld zpn_rule_condition_set_register_customer returned %s", (long) customer_gid, zpath_result_string(res));
            }
            res = zpn_rule_condition_operand_register_customer(customer_gid, load_from_wally, policy, 0);
            if (res != ZPATH_RESULT_ASYNCHRONOUS) {
                ZPN_LOG(AL_CRITICAL, "customer %ld zpn_rule_condition_operand_register_customer returned %s", (long) customer_gid, zpath_result_string(res));
            }
            policy->wally_registered = 1;
        }
        ZPATH_MUTEX_UNLOCK(&(policy->policy_lock), __FILE__, __LINE__);
    }

    if (!policy->wally_registration_complete) {
        ZPATH_MUTEX_LOCK(&(policy->policy_lock), __FILE__, __LINE__);
        if (!policy->wally_registration_complete) {
            ZPN_DEBUG_ZPE("%ld: customer waiting on registration complete", (long)customer_gid);
            if (!policy->requestors) {
                policy->requestors = wally_callback_queue_create();
            }
            if (hold_callback && param_cookie) (*hold_callback)(param_cookie);
            wally_callback_queue_add(policy->requestors, callback, void_cookie, int_cookie);
            ZPATH_MUTEX_UNLOCK(&(policy->policy_lock), __FILE__, __LINE__);
            return ZPATH_RESULT_ASYNCHRONOUS;
        }
        ZPATH_MUTEX_UNLOCK(&(policy->policy_lock), __FILE__, __LINE__);
    }

    if (policy_ts) policy_ts->last_refresh_indication_us = policy->policy_ts.last_refresh_indication_us;


    if ((policy_type < ZPE_POLICY_TYPE_FIRST) || (policy_type > ZPE_POLICY_TYPE_LAST)) {
        ZPN_LOG(AL_CRITICAL, "Bad policy enum: %d, scope 0x%lx", policy_type, (long) scope_gid);
        return ZPATH_RESULT_ERR;
    }

    /* The fast cache lookup case, where policy is already built at
     * least once: */
    if (policy->policies_built[policy_type]->policy_set) {
        is_rebuild = 1;
        if (policy_built) *policy_built = policy->policies_built[policy_type];
        if (!policy->policies_built[policy_type]->policy_meta) {
            ZPN_LOG(AL_WARNING, "%ld has no policy meta", (long) scope_gid);
        }

        if (policy_ts) {
            policy_ts->last_rebuild_started_us = policy->policy_ts.last_rebuild_started_us;
            policy_ts->last_rebuild_finished_us = policy->policy_ts.last_rebuild_finished_us;
            policy_ts->last_rebuild_time_us = policy->policy_ts.last_rebuild_time_us;
        }


        /* Check if we need to rebuild: */
        if (policy->version == policy->policies_built[policy_type]->policy_version) {
            /* No need to rebuild! */
            return ZPATH_RESULT_NO_ERROR;
        }
    }

    uint8_t policy_rebuild_feature_status = zpn_broker_is_policy_rebuild_backoff_enabled(customer_gid);
    policy->policy_rebuild_feature_enabled = policy_rebuild_feature_status;
    /*
     * TODO: Cleanup duplicate code as a part of ET-73139
     */
    if (policy_rebuild_feature_status) {
        res = zpe_get_policy_lock_improvement(policy,
                                              policy_type,
                                              policy_built,
                                              callback,
                                              void_cookie,
                                              int_cookie,
                                              hold_callback,
                                              param_cookie,
                                              being_built);
    } else {
        res = zpe_get_policy_no_lock_improvement(policy,
                                                 policy_type,
                                                 policy_built,
                                                 callback,
                                                 void_cookie,
                                                 int_cookie,
                                                 hold_callback,
                                                 param_cookie,
                                                 being_built);
    }

    if (res == ZPATH_RESULT_ASYNCHRONOUS ||
        (res == ZPATH_RESULT_NO_ERROR && is_rebuild)) {
        return res;
    }

    ZPATH_MUTEX_LOCK(&(policy->policy_lock), __FILE__, __LINE__);
    /* If we now have a config to return (the above can complete
     * synchronously) we just return that */
    if (policy->policies_built[policy_type] && policy->policies_built[policy_type]->policy_set) {
        if (policy_built) *policy_built = policy->policies_built[policy_type];
        ZPATH_MUTEX_UNLOCK(&(policy->policy_lock), __FILE__, __LINE__);
        return ZPATH_RESULT_NO_ERROR;
    }

    if (being_built) *being_built = 1;

    /* If async_count is non-zero, we are waiting for configuration to be complete */
    if (policy->async_count != 0) {
        if (!policy->requestors) {
            policy->requestors = wally_callback_queue_create();
        }
        if (hold_callback && param_cookie) (*hold_callback)(param_cookie);
        wally_callback_queue_add(policy->requestors, callback, void_cookie, int_cookie);
        ZPATH_MUTEX_UNLOCK(&(policy->policy_lock), __FILE__, __LINE__);
        return ZPATH_RESULT_ASYNCHRONOUS;
    } else {
        ZPATH_MUTEX_UNLOCK(&(policy->policy_lock), __FILE__, __LINE__);
        return ZPATH_RESULT_NOT_FOUND;
    }
    return ZPATH_RESULT_ASYNCHRONOUS;
}

int zpe_get_current_policy(int64_t scope_gid,
                           enum zpe_policy_type policy_type,
                           struct zpe_policy_built **policy_built,
                           wally_response_callback_f *callback,
                           void *void_cookie,
                           int64_t int_cookie,
                           hold_param_callback_f *hold_callback,
                           void *param_cookie,
                           int *being_built,
                           struct zpe_policy_built_timestamp *policy_ts)
{
    int res = 0;
    int64_t delta_us = 0;
    int debugging_enabled = zpn_debug_get(ZPN_DEBUG_ZPE_IDX);
    if (debugging_enabled) delta_us = epoch_us();
    res = zpe_get_current_policy_1(scope_gid,
                                   policy_type,
                                   policy_built,
                                   callback,
                                   void_cookie,
                                   int_cookie,
                                   hold_callback,
                                   param_cookie,
                                   being_built,
                                   policy_ts);
    if (debugging_enabled && !res) {
        delta_us = epoch_us() - delta_us;
        ZPN_DEBUG_ZPE("Policy Performance Measurement: get_policy, scope=%ld, type=%d, cost=%ld usec",
                      (long)scope_gid, (int)policy_type, (long)delta_us);
    }
    return res;
}

/*
 * This function is called synchrously without rebuilding a policy
 * The policy is built asynchroursly when zpn_scope_reay or when a policy change happens
 * If the policy is still being built, this function will just use previous policy
 * If no policy is built, return "not found" error with NULL policy set
 * If policy is not configured, return success with empty (not NULL) policy set
 */
int zpe_get_policy_without_rebuild(int64_t scope_gid,
                                   enum zpe_policy_type policy_type,
                                   struct zpe_policy_built **policy_built,
                                   int *being_built,
                                   struct zpe_policy_built_timestamp *policy_ts)
{
    int res = ZPATH_RESULT_NO_ERROR;
    struct zpe_policy *policy;
    if (being_built) *being_built = 0;

    /* Get or create policy entry. This entry never disappears. */
    policy = get_or_create_scope_policy(scope_gid);

    if (policy_ts) policy_ts->last_refresh_indication_us = policy->policy_ts.last_refresh_indication_us;

    if ((policy_type < ZPE_POLICY_TYPE_FIRST) || (policy_type > ZPE_POLICY_TYPE_LAST)) {
        ZPN_LOG(AL_CRITICAL, "Bad policy enum: %d, scope 0x%lx", policy_type, (long) scope_gid);
        return ZPATH_RESULT_ERR;
    }

    /* The fast cache lookup case, where policy is already built at
     * least once: */
    if (policy->policies_built[policy_type]->policy_set) {
        if(policy_built) *policy_built = policy->policies_built[policy_type];
        if (!policy->policies_built[policy_type]->policy_meta) {
            ZPN_LOG(AL_WARNING, "%ld: has no policy meta", (long) scope_gid);
        }

        if (policy_ts) {
            policy_ts->last_rebuild_started_us = policy->policy_ts.last_rebuild_started_us;
            policy_ts->last_rebuild_finished_us = policy->policy_ts.last_rebuild_finished_us;
            policy_ts->last_rebuild_time_us = policy->policy_ts.last_rebuild_time_us;
        }

       if (policy->version != policy->policies_built[policy_type]->policy_version) {
            /* use whatever version it is, the new policy is being or will be built somewhere else */
            ZPN_LOG(AL_WARNING, "%ld: use old version of policy: %ld",
                                (long) scope_gid, (long) policy->policies_built[policy_type]->policy_version);
            if (being_built) *being_built = 1;
        }
    } else {
        ZPN_DEBUG_ZPE("%ld: no policy found, type=%d", (long) scope_gid, (int)policy_type);
        res = ZPATH_RESULT_NOT_FOUND;
    }

    return res;
}

int zpe_get_scope_policy_without_rebuild(int64_t scope_gid,
                                         enum zpe_policy_type policy_type,
                                         struct zpe_policy_built **policy_built_default,
                                         struct zpe_policy_built **policy_built,
                                         struct zpe_policy_built_timestamp *policy_ts)
{
    int res;
    /* get default policy */
    int64_t default_scope_gid = get_customer_from_scope(scope_gid);
    res = zpe_get_policy_without_rebuild(default_scope_gid,
                                         policy_type,
                                         policy_built_default,
                                         NULL,
                                         policy_ts);

    if (res) {
        ZPN_DEBUG_ZPE("scope %ld: policy %s", (long) default_scope_gid, zpe_policy_type_string(res));
        return res;
    }
    if (!is_scope_default(scope_gid)) {
        res = zpe_get_policy_without_rebuild(scope_gid,
                                             policy_type,
                                             policy_built,
                                             NULL,
                                             policy_ts);
    } else {
        *policy_built = *policy_built_default;
    }

    return res;
}

int is_new_policy_available(const struct zpe_policy_built *current_policy_built)
{
    int64_t customer_gid = 0;
    int64_t scope_gid = 0;
    enum zpe_policy_type policy_type = zpe_policy_type_deprecated;
    struct zpe_policy_built *policy_built = NULL;
    int being_built = 0;
    int res = 0;
    int new_policy_available = 0;

    if (!current_policy_built) {
        ZPN_LOG(AL_CRITICAL, "invalid current policy");
        return 0;
    }

    customer_gid = current_policy_built->policy_meta->customer_gid;
    scope_gid = current_policy_built->policy_meta->scope_gid;
    policy_type = current_policy_built->policy_meta->policy_type;

    res = zpe_get_policy_without_rebuild(scope_gid,
                                         policy_type,
                                         &policy_built,
                                         &being_built,
                                         NULL);
    if (res || !policy_built) {
        ZPN_LOG(AL_CRITICAL, "failed to find policy for customer %"PRId64", scope %"PRId64", res=%s", customer_gid, scope_gid, zpath_result_string(res));
        return -1;
    }

    if (being_built) {
        new_policy_available = 1;
    } else if (current_policy_built != policy_built) {
        new_policy_available = 2;
    }

    return new_policy_available;
}

int zpe_pending_or_in_progress_policy_build(int64_t scope_gid)
{
    struct zpe_policy *policy;

    /* Get or create policy entry. This entry never disappears. */
    policy = get_or_create_scope_policy(scope_gid);

    /* coverity[missing_lock: FALSE] */
    if (policy->deferred_update || policy->policy_build_in_progress) {
        return 1;
    }

    return 0;
}
int zpe_dump_policy_state(int64_t scope_gid,
                          char *str,
                          size_t str_len)
{
    struct zpe_policy *policy = get_or_create_scope_policy(scope_gid);

    char *s = str;
    char *e = s + str_len;

    int64_t now_us = epoch_us();

    int i;

    ZPATH_MUTEX_LOCK(&(policy->lock), __FILE__, __LINE__);

    s += sxprintf(s, e, "%ld: version = %ld, requestors: %d, async = %d, deferred = %s\n",
                  (long) scope_gid,
                  (long) policy->version,
                  wally_callback_queue_depth(policy->requestors),
                  policy->async_count,
                  policy->deferred_update ? "yes" : "no");

    if (policy->policy_ts.last_rebuild_started_us) {
        s += sxprintf(s, e, "   Build in progress, started %.3fs ago\n",
                            (double) (now_us - policy->policy_ts.last_rebuild_started_us) / 1000000.0);
    }
    if (policy->policy_ts.last_rebuild_finished_us) {
        s += sxprintf(s, e, "   Build last finished at %ld, %.3fs ago, took %.6fs\n",
                      (long) policy->policy_ts.last_rebuild_finished_us,
                      (double) (now_us - policy->policy_ts.last_rebuild_finished_us) / 1000000.0,
                      (double) policy->policy_ts.last_rebuild_time_us / 1000000.0);
    }

    if (policy->policy_rebuild_feature_enabled) {
        s += sxprintf(s, e, "   Policy rebuild timer created: %"PRId64"s ago \n",
                            epoch_s() - policy->policy_rebuild_timer_created_s);

        s += sxprintf(s, e, "   Policy rebuild request received: %"PRId64" \n",
                            policy->policy_rebuild_req_received);

        s += sxprintf(s, e, "   Number of build: %"PRId64" \n",
                            policy->number_of_build);

        s += sxprintf(s, e, "   Concurrent rebuild req received: %"PRId64" \n",
                            policy->policy_concurrent_rebuild_req_received);

        s += sxprintf(s, e, "   Rebuild timer: %.3fs \n",
                            (double) policy->policy_rebuild_defer_us / 1000000.0);

        s += sxprintf(s, e, "   Max rebuild timer: %"PRId64"s \n",
                            policy->policy_rebuild_max_defer_s);
    } else {
        s += sxprintf(s, e, "   Policy Rebuild feature is disabled\n");
    }

    for (i = 0; i < ZPE_POLICY_TYPE_COUNT; i++) {
        s += sxprintf(s, e, " %35s: version = %8ld  ",
                            zpe_policy_type_string(i), (long)policy->policies_built[i]->policy_version);
        if (policy->policies_built[i]->policy_set) {
            s += sxprintf(s, e, " %5d rules, %6d bytes, set GID=%ld, set sequence=%ld",
                          policy->policies_built[i]->policy_set->zpe_rule_count,
                          policy->policies_built[i]->policy_set->total_bytes,
                          (long) policy->policies_built[i]->policy_meta->gid,
                          (long) policy->policies_built[i]->policy_meta->sequence);
        }
        s += sxprintf(s, e, "\n");
    }

    ZPATH_MUTEX_UNLOCK(&(policy->lock), __FILE__, __LINE__);

    return ZPATH_RESULT_NO_ERROR;
}

int zpe_load_policy(int64_t scope_gid,
                    wally_response_callback_f *callback,
                    void *void_cookie,
                    int64_t int_cookie,
                    hold_param_callback_f *hold_callback,
                    void *param_cookie)
{
    int i;
    int res;
    int good_policy_count = 0;
    int64_t delta_us = 0;
    int debugging_enabled = zpn_debug_get(ZPN_DEBUG_ZPE_IDX);
    if (debugging_enabled) delta_us = epoch_us();

    for (i = ZPE_POLICY_TYPE_FIRST; i <= ZPE_POLICY_TYPE_LAST; i++) {
        res = zpe_get_current_policy(scope_gid,
                                     i,
                                     NULL,
                                     callback,
                                     void_cookie,
                                     int_cookie,
                                     hold_callback,
                                     param_cookie,
                                     NULL,
                                     NULL);
        if (res == ZPN_RESULT_ASYNCHRONOUS) {
            ZPN_DEBUG_ZPE("scope %ld: policy %s is asynchronous", (long) scope_gid, zpe_policy_type_string(i));
            return res;
        }
        /*
         * If all policies success, return no error.
         * If any policy fail other than NOT_FOUND, return the first error other than NOT_FOUND,
         * scope will not be ready and we need to investigate the issue.
         * If only failure is due to NOT_FOUND, return NOT_FOUND, and we will re-do scope_ready()
         */
        if (res) {
            /* In this case we continue processing, but log as noisy an error as we can */
            ZPN_LOG(AL_CRITICAL, "scope %ld failed to load policy type %d (%s), reason: %s", (long) scope_gid, i, zpe_policy_type_string(i),  zpath_result_string(res));
        } else {
            good_policy_count++;
        }
    }
    if (good_policy_count == ZPE_POLICY_TYPE_LAST) {
        ZPN_DEBUG_ZPE("scope %ld zpe loaded all policies", (long) scope_gid);
        struct zpe_policy *policy = get_or_create_scope_policy(scope_gid);
        ZPATH_MUTEX_LOCK(&(policy->lock), __FILE__, __LINE__);
        policy->deferred_update_outstanding = 0;
        ZPATH_MUTEX_UNLOCK(&(policy->lock), __FILE__, __LINE__);
    } else {
        ZPN_LOG(AL_ERROR, "scope %ld zpe loaded %d out of %d policies (policy build errors on the missing)", (long) scope_gid, good_policy_count, ZPE_POLICY_TYPE_LAST);
    }
    if (debugging_enabled && !res) {
        delta_us = epoch_us() - delta_us;
        ZPN_DEBUG_ZPE("Policy Performance Measurement: load all policies, scope=%ld, cost=%ld usec",
                      (long)scope_gid, (long)delta_us);
    }
    return ZPN_RESULT_NO_ERROR;
}


static void defer_tickle(int sock, short flags, void *cookie)
{
    struct zpe_policy  *policy = cookie;
    int do_fetch_policy = 0;

    ZPATH_MUTEX_LOCK(&(policy->lock), __FILE__, __LINE__);
    if (policy->deferred_update) {
        event_free(policy->deferred_update);
        policy->deferred_update = NULL;
        do_fetch_policy = 1;
    }
    ZPATH_MUTEX_UNLOCK(&(policy->lock), __FILE__, __LINE__);

    if (do_fetch_policy) {
        zthread_heartbeat(NULL); /* heartbeat if we are going to spend some time. */
        ZPN_DEBUG_ZPE("%ld: scope deferred tickle expired, building policy", (long) policy->scope_gid);
        zpe_load_policy(policy->scope_gid, NULL, NULL, 0, NULL, NULL);
    } else {
        ZPN_DEBUG_ZPE("%ld: scope deferred tickle expired, but was canceled?", (long) policy->scope_gid);
    }
}

static void dispatch_tickle(struct zevent_base *base, void *void_cookie, int64_t int_cookie)
{
    int64_t scope_gid = int_cookie;
    struct zpe_policy *policy;
    struct timeval tv;
    int64_t     customer_gid = ZPATH_GID_GET_CUSTOMER_GID(scope_gid);
    uint64_t    rebuild_defer_us = tickle_defer_us;
    int64_t     max_rebuild_defer_config_s = zpn_broker_policy_rebuild_backoff_periodic_check_interval_s(customer_gid);
    uint8_t     policy_rebuild_feature_status = zpn_broker_is_policy_rebuild_backoff_enabled(customer_gid);
    int64_t     rebuild_defer_config_us = zpn_broker_policy_rebuild_backoff_interval_s(customer_gid) * tickle_defer_us;
    struct zthread_info *self_zthread_info = zthread_self();
    int zthread_num = self_zthread_info->stack.thread_num;
    int res = 0;

    policy = get_or_create_scope_policy(scope_gid);

    if (!policy_rebuild_feature_status) {
        ZPN_DEBUG_ZPE("%"PRId64": scope deferred policy tickle...(%"PRId64" us)", policy->scope_gid, tickle_defer_us);
    }

    ZPN_DEBUG_ZPE("Policy Rebuild Backoff Status for customer_gid = %"PRId64" is %d, backoff_interval = %"PRId64"us, backoff_periodic_check_interval = %"PRId64"s",
                  customer_gid, policy_rebuild_feature_status, rebuild_defer_config_us,
                  max_rebuild_defer_config_s);

    ZPATH_MUTEX_LOCK(&(policy->lock), __FILE__, __LINE__);
    policy->version++;

    if (policy->wally_registration_complete) {
        policy->policy_rebuild_req_received++;
        policy_rebuild_stats[zthread_num].policy_rebuild_req_received++;
    }

    policy->policy_rebuild_feature_enabled = policy_rebuild_feature_status;
    policy->policy_rebuild_defer_us = rebuild_defer_config_us;
    policy->policy_rebuild_max_defer_s = max_rebuild_defer_config_s;

    if (policy->policy_rebuild_feature_enabled && policy->policy_build_in_progress) {
        policy->policy_rebuild_req_rcved_during_build = 1;
        ZPN_LOG(AL_INFO,"%"PRId64": Will dispatch new build request once current build completes, build in progress %d, rebuild req recived during build %d",
                        policy->scope_gid, policy->policy_build_in_progress, policy->policy_rebuild_req_rcved_during_build);
        ZPATH_MUTEX_UNLOCK(&(policy->lock), __FILE__, __LINE__);
        return;
    }

    if (!policy->deferred_update) {
        if (policy->policy_rebuild_req_received) {
            policy_rebuild_stats[zthread_num].policy_rebuild_scheduled++;
        }
        policy->policy_rebuild_timer_created_s = epoch_s();
        policy->deferred_update = event_new(zevent_get_evbase_for_class(ZEVENT_CLASS_APP_CALC), -1, 0, defer_tickle, policy);
    }

    if (policy->policy_rebuild_feature_enabled) {
        if ((epoch_s() - policy->policy_rebuild_timer_created_s) >= policy->policy_rebuild_max_defer_s) {
            rebuild_defer_us = 0;
            policy_rebuild_stats[zthread_num].policy_rebuild_num_max_backoff_triggered++;
            ZPN_LOG(AL_NOTICE,"%"PRId64": max rebuild backoff %"PRId64" s triggered, scheduling rebuild immediately",
                               policy->scope_gid, policy->policy_rebuild_max_defer_s);
        } else {
            rebuild_defer_us = policy->policy_rebuild_defer_us;
        }
    }

    tv.tv_usec = rebuild_defer_us % 1000000;
    tv.tv_sec = rebuild_defer_us / 1000000;
    res = event_add(policy->deferred_update, &tv);
    if (res) {
        ZPN_LOG(AL_NOTICE,"%"PRId64": version %"PRId64" failed to defer poilcy event timer by %"PRId64" us, res: %d: %s",
                          policy->scope_gid, policy->version, rebuild_defer_us, res, strerror(errno));
    }
    ZPN_DEBUG_ZPE("%"PRId64": version %"PRId64" deferred poilcy build by %"PRId64" us, res: %d",
                   policy->scope_gid, policy->version, rebuild_defer_us, res);
    policy->policy_ts.last_refresh_indication_us = epoch_us();
    policy->deferred_update_outstanding = 1;

    ZPATH_MUTEX_UNLOCK(&(policy->lock), __FILE__, __LINE__);

}

void ut_dispatch_tickle(int64_t scope_gid)
{
    dispatch_tickle(NULL, NULL, scope_gid);
}

/*
 * Increase policy version to pretend a policy is being built
 * This is only used for Unit Testing, do not call for production
 */
void zpe_pretend_scope_tickle(int64_t scope_gid)
{
    struct zpe_policy *policy;
    if (is_unit_test()) {
        policy = get_or_create_scope_policy(scope_gid);
        ZPATH_MUTEX_LOCK(&(policy->lock), __FILE__, __LINE__);
        policy->version++;
        ZPATH_MUTEX_UNLOCK(&(policy->lock), __FILE__, __LINE__);
    }
}

void zpe_pretend_scope_cancel_tickle(int64_t scope_gid)
{
    struct zpe_policy *policy;
    if (is_unit_test()) {
        policy = get_or_create_scope_policy(scope_gid);
        ZPATH_MUTEX_LOCK(&(policy->lock), __FILE__, __LINE__);
        policy->version--;
        ZPATH_MUTEX_UNLOCK(&(policy->lock), __FILE__, __LINE__);
    }
}

static void dispatch_tickle_cancel(struct zevent_base *base, void *void_cookie, int64_t int_cookie)
{
    int64_t scope_gid = int_cookie;
    struct zpe_policy *policy;

    policy = get_or_create_scope_policy(scope_gid);

    ZPATH_MUTEX_LOCK(&(policy->lock), __FILE__, __LINE__);
    if (policy->deferred_update) {
        event_free(policy->deferred_update);
        policy->deferred_update = NULL;
        ZPN_DEBUG_ZPE("%ld: scope deferred policy tickle canceled", (long) policy->scope_gid);
    } else {
        ZPN_DEBUG_ZPE("%ld: scope deferred policy tickle did not exist to cancel", (long) policy->scope_gid);
    }
    policy->deferred_update_outstanding = 0;
    ZPATH_MUTEX_UNLOCK(&(policy->lock), __FILE__, __LINE__);
}


const char *zpe_policy_type_string(enum zpe_policy_type policy_type)
{
    switch (policy_type) {

    case zpe_policy_type_deprecated:
        return "zpe_policy_type_deprecated";
        /*break;*/
    case zpe_policy_type_access:
        return "zpe_policy_type_access";
        /*break;*/
    case zpe_policy_type_reauth:
        return "zpe_policy_type_reauth";
        /*break;*/
    case zpe_policy_type_siem:
        return "zpe_policy_type_siem";
        /*break;*/
    case zpe_policy_type_bypass:
        return "zpe_policy_type_bypass";
        /*break;*/
    case zpe_policy_type_isolate:
        return "zpe_policy_type_isolate";
        /*break;*/
    case zpe_policy_type_inspection:
        return "zpe_policy_type_inspection";
        /*break;*/
    case zpe_policy_type_priv_capabilities:
        return "zpe_policy_type_priv_capabilities";
    case zpe_policy_type_broker_redirect:
        return "zpe_policy_type_broker_redirect";
    case zpe_policy_type_csp_policy:
        return "zpe_policy_type_csp_policy";
        /*break;*/
    case zpe_policy_type_cred_map:
        return "zpe_policy_type_cred_map";
        /*break;*/
    case zpe_policy_type_priv_portal_policy:
        return "zpe_policy_type_priv_portal_policy";
        /*break;*/
    case zpe_policy_type_global_vpn:
        return "zpe_policy_type_global_vpn";
        /*break;*/
    default:
        break;
    }
    return "unknown";
}

const char *zpe_access_action_string(enum zpe_access_action action)
{
    switch (action) {
    case zpe_access_action_none:
        return "zpe_access_action_none";
        /*break;*/
    case zpe_access_action_allow:
        return "zpe_access_action_allow";
        /*break;*/
    case zpe_access_action_deny:
        return "zpe_access_action_deny";
        /*break;*/
    case zpe_access_action_re_auth:
        return "zpe_access_action_re_auth";
        /*break;*/
    case zpe_access_action_log:
        return "zpe_access_action_log";
        /*break;*/
    case zpe_access_action_bypass:
        return "zpe_access_action_bypass";
        /*break;*/
    case zpe_access_action_intercept:
        return "zpe_access_action_intercept";
        /*break;*/
    case zpe_access_action_nodownload:
        return "zpe_access_action_nodownload";
        /*break;*/
    case zpe_access_action_bypass_on_reauth:
        return "zpe_access_action_bypass_on_reauth";
        /*break;*/
    case zpe_access_action_intercept_if_accessible:
        return "zpe_access_action_intercept_if_accessible";
        /*break;*/
    case zpe_access_action_isolate:
        return "zpe_access_action_isolate";
        /*break;*/
    case zpe_access_action_bypass_isolate:
        return "zpe_access_action_bypass_isolate";
        /*break;*/
    case zpe_access_action_approval_required:
        return "zpe_access_action_approval_required";
    case zpe_access_action_check_priv_capabilities:
        return "zpe_access_action_check_priv_capabilities";
    case zpe_access_action_redirect_always:
        return "zpe_access_action_redirect_always";
    case zpe_access_action_redirect_preferred:
        return "zpe_access_action_redirect_preferred";
    case zpe_access_action_redirect_default:
        return "zpe_access_action_redirect_default";
    case zpe_csp_policy_monitor:
        return "zpe_csp_policy_monitor";
    case zpe_csp_policy_do_not_monitor:
        return "zpe_csp_policy_do_not_monitor";
    case zpe_access_action_inject_credentials:
        return "zpe_access_action_inject_credentials";
    case zpe_access_action_check_priv_portal_capabilities:
        return "zpe_access_action_check_priv_portal_capabilities";
    default:
        break;
    }
    return "unknown";
}

const char *zpe_access_approval_status_string(enum zpe_access_approval_status status)
{
    switch(status) {
        case zpe_access_approval_none:
            return "zpe_access_approval_none";
        case zpe_access_approval_active:
            return "zpe_access_approval_active";
        case zpe_access_approval_future:
            return "zpe_access_approval_future";
        case zpe_access_approval_expired:
            return "zpe_access_approval_expired";
        default:
            return "unknown";
    }
}

static int zpe_is_custome_registered(int64_t scope_gid)
{
    struct zpe_policy *policy = get_or_create_scope_policy(scope_gid);
    return policy->wally_registered;
}

void zpe_scope_tickle(int64_t scope_gid)
{
    if (zpe_is_policy_table_initialized && zpe_is_custome_registered(scope_gid)) {
        /* This must be dispatched asynchronously to drop locks... */
        zevent_base_call(zevent_self(), dispatch_tickle, NULL, scope_gid);
    }
}

void zpe_customer_tickle(int64_t customer_gid)
{
    struct zpn_scope *scopes[1000];
    size_t scope_count = 1000;
    struct zpn_scope test_scope = {};
    int i;
    int res;

    /*
     * if policy table is not initialized we should not process further.
     */
    if(!zpe_is_policy_table_initialized) {
        return;
    }

    if (using_scope_test()) {
        scope_count = 1;
        scopes[0] = &test_scope;
        scopes[0]->enabled = 1;
        scopes[0]->gid = get_scope_gid_for_unit_test(customer_gid);
    } else {
        res = zpn_scope_get_by_customer_gid_immediate(customer_gid,
                                                      &scopes[0],
                                                      &scope_count);
        if (res && res != ZPATH_RESULT_NOT_FOUND) {
            ZPN_LOG(AL_CRITICAL, "%ld failed to get scopes, %s", (long)customer_gid, zpn_result_string(res));
            scope_count = 0;
        }
    }

    /*
     * zpn_scope table don't contain any entry for default scope
     * so when customer only have default scope zpn_scope_get_by_customer_gid_immediate
     * will return NOT_FOUND or '0' scope_count due to we need to tickle default scope first.
     * We don't have scope gid for default so we use customer_gid as scope gid for default scope
     */
    if(zpe_is_policy_table_initialized && zhash_table_lookup(policy_table, &customer_gid, sizeof(customer_gid), NULL)) {
        zpe_scope_tickle(customer_gid);
    }

    for (i=0; i<scope_count; i++) {
        /* only update the policy if we have built it before */
        if (scopes[0]->enabled) {
            int64_t scope_gid = scopes[i]->gid;
            if(zhash_table_lookup(policy_table, &scope_gid, sizeof(scope_gid), NULL)){
                zpe_scope_tickle(scope_gid);
            }
        }
    }
}

void zpe_scope_cancel_tickle(int64_t scope_gid)
{
    zevent_base_call(zevent_self(), dispatch_tickle_cancel, NULL, scope_gid);
}

int zpe_is_scope_tickled(int64_t scope_gid)
{
    struct zpe_policy *policy;
    int tickled;

    policy = get_or_create_scope_policy(scope_gid);

    ZPATH_MUTEX_LOCK(&(policy->policy_lock), __FILE__, __LINE__);
    tickled = policy->deferred_update ? 1 : 0;
    ZPATH_MUTEX_UNLOCK(&(policy->policy_lock), __FILE__, __LINE__);
    return tickled;
}

int zpe_is_scope_build_outstanding(int64_t scope_gid)
{
    struct zpe_policy *policy;
    int tickled;

    policy = get_or_create_scope_policy(scope_gid);

    ZPATH_MUTEX_LOCK(&(policy->policy_lock), __FILE__, __LINE__);
    tickled = policy->deferred_update_outstanding;
    ZPATH_MUTEX_UNLOCK(&(policy->policy_lock), __FILE__, __LINE__);
    return tickled;
}

/*
 * This routine will flush a scope's policies. It is used for
 * QA/testing only. It will cancel timers, and clear pending builds.
 */
void zpe_scope_flush(int64_t scope_gid)
{
    struct zpe_policy *policy;
    int i;

    policy = get_or_create_scope_policy(scope_gid);

    ZPATH_MUTEX_LOCK(&(policy->policy_lock), __FILE__, __LINE__);

    for (i = 0; i < ZPE_POLICY_TYPE_COUNT; i++) {
        if (policy->policies_built[i]) {
            if (policy->policies_built[i]->policy_set) {
                ZPE_FREE_SLOW(policy->policies_built[i]->policy_set);
                policy->policies_built[i]->policy_set = NULL;
                zpe_free_app_to_rules_slow(policy->policies_built[i]->app_to_rules);
                policy->policies_built[i]->app_to_rules = NULL;
                zpe_free_mini_policy_set_slow(policy->policies_built[i]->rules_for_all);
                policy->policies_built[i]->rules_for_all = NULL;
            }
            ZPE_FREE_SLOW(policy->policies_built[i]);
            policy->policies_built[i] = ZPE_CALLOC(sizeof(*(policy->policies_built[i])));
        }
    }
    if (policy->deferred_update) {
        event_free(policy->deferred_update);
        policy->deferred_update = NULL;
    }
    policy->deferred_update_outstanding = 0;

    ZPATH_MUTEX_UNLOCK(&(policy->policy_lock), __FILE__, __LINE__);
}

int zpe_init(int policy_rebuild_backoff)
{
    register_lock = ZPATH_MUTEX_INIT;
    policy_table_lock = ZPATH_MUTEX_INIT;
    policy_table = zhash_table_alloc(&zpe_allocator);
    if (!policy_table) return ZPATH_RESULT_NO_MEMORY;
    zpe_is_policy_table_initialized = 1;
    zpe_is_policy_rebuild_backoff_supported = policy_rebuild_backoff;
    return ZPATH_RESULT_NO_ERROR;
}

void zpe_set_deferred_tickle_us(int64_t defer_us)
{
    tickle_defer_us = defer_us;
}


int is_policy_build_failed(int64_t scope_gid, enum zpe_policy_type policy_type, int64_t *fail_count)
{
    struct zpe_policy *policy = get_or_create_scope_policy(scope_gid);
    ZPATH_MUTEX_LOCK(&(policy->lock), __FILE__, __LINE__);
    if (fail_count) *fail_count = policy->build_fail_count[policy_type];
    int build_fail =  policy->build_fail[policy_type];
    ZPATH_MUTEX_UNLOCK(&(policy->lock), __FILE__, __LINE__);
    return build_fail;
}


void get_policy_rebuild_stats(int64_t *num_rebuild_req_received, int64_t *policy_rebuild_scheduled, int64_t *num_max_backoff_triggered)
{
    int total_zthreads = 0;
    zthread_state_get(&total_zthreads);

    for (int i = 0; i < total_zthreads; i++) {
        *num_rebuild_req_received += policy_rebuild_stats[i].policy_rebuild_req_received;
        *policy_rebuild_scheduled += policy_rebuild_stats[i].policy_rebuild_scheduled;
        *num_max_backoff_triggered += policy_rebuild_stats[i].policy_rebuild_num_max_backoff_triggered;
    }
}
