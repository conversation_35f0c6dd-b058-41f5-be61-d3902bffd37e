
/*
 * pbroker_monitor.h. Copyright (C) 2014-2020 Zscaler Inc, All Rights Reserved
 */

#ifndef _PBROKER_MONITOR_H_
#define _PBROKER_MONITOR_H_
#include "fohh/fohh_log.h"
#include "zpn/zpn_private_broker_private.h"

void pbroker_monitor_status_report(void);

/*
 * 1. Re-enroll when we are >= 90% of the cert.pem's valid period. Done in enrollment library.
 * 2. Force-re-enroll when we are >= 99% of the cert.pem's valid period.
 */
#define ZPN_PBROKER_FORCE_RE_ENROLL_TIME_FACTOR               99

int
pbroker_monitor_timer_init(struct event_base *base);

int
pbroker_stats_monitor_init(void);

uint16_t pbroker_get_cpu_util(void);
uint16_t pbroker_get_cpu_steal_perc(void);

int pbroker_get_system_mem_util(void);

int pbroker_get_process_mem_util(void);

void pbroker_log_get_endpoint_data(struct fohh_log_endpoint_cns** cn, struct zpn_private_broker_global_state *gs);

void zpn_private_broker_firedrill_stop(evutil_socket_t sock, int16_t flags, void *cookie);

int zpn_pbroker_firedrill_session_request();

#endif
