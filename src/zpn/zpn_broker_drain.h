/*
 * zpn_broker_drain.h Copyright (C) 2023 Zscaler, Inc. All Rights Reserved.
 */
#ifndef __ZPN_BROKER_DRAIN_H__
#define __ZPN_BROKER_DRAIN_H__

#include "zpn/zpn_lib.h"

#define ZPN_BROKER_MANUAL_DRAIN_TIMEOUT_MIN_S               1
#define ZPN_BROKER_MANUAL_DRAIN_TIMEOUT_MAX_S               86400       //24 hours
#define ZPN_BROKER_MANUAL_DRAIN_TIMEOUT_DEFAULT_S           300         //5 minutes

#define ZPN_BROKER_MANUAL_DRAIN_PC_MIN              1
#define ZPN_BROKER_MANUAL_DRAIN_PC_MAX              100
#define ZPN_BROKER_MANUAL_DRAIN_PC_DEFAULT          100

/*
 * Drain type enum
 */
enum zpn_conn_drain_type
{
    drain_type_none = 0,
    drain_type_manual,
    drain_type_maintenance,
    //drain_type_auto,
    drain_type_total_count  /* should be last; doubles up as drain_types*/
};

enum zpn_conn_drain_subtype
{
    drain_subtype_none,
    drain_subtype_customer_constellation,
    drain_subtype_instance_constellation,
    drain_subtype_customer_partition,
    drain_subtype_instance_partition,
    drain_subtype_maintenance_upgrade,
    drain_subtype_invalid,      /* should be the last */
};

enum zpn_conn_drain_status {
    drain_status_none = 0,
    drain_status_pending,
    drain_status_initiated,
    drain_status_not_possible,
    drain_status_completed,
    drain_status_invalid    /* should be last*/
};

struct zpn_conn_drain_info {
    const char*                     drain_reason;       /* drain reason */
    enum zpn_conn_drain_subtype     drain_subtype;      /* 0 for manual; different subtypes for auto. */
    enum zpn_conn_drain_status      drain_status;       /* status of request */
    int                             drain_enabled;      /* indicates if drain is initiated for this cstate or not */
    int64_t                         expiry_s;           /* indicates time by which this c_state to be evicted */
    int64_t                         max_expiry_s;       /* indicates max possible expiry time..(T0 + drain_timeout_s)*/
    int64_t                         timestamp_s;        /* update whenever a transition happens */
};

/* segregate manual and automatic drain stats */
struct zpn_broker_drain_stats_entry {
    uint64_t    drain_initiated_count;
    uint64_t    drain_pending_count;
    uint64_t    drain_not_possible_count;
    uint64_t    drain_completed_count;
    uint64_t    total_mtunnels_drained;
};

/* Include manual and auto drain together */
struct zpn_broker_drain_stats {                     /* _ARGO: object_definition */
    uint64_t manual_drain_not_possible_count;       /* _ARGO: integer */
    uint64_t manual_drain_completed_count;          /* _ARGO: integer */
    uint64_t manual_total_mtunnels_drained;         /* _ARGO: integer */
    uint64_t maintenance_drain_not_possible_count;  /* _ARGO: integer */
    uint64_t maintenance_drain_completed_count;     /* _ARGO: integer */
    uint64_t maintenance_total_mtunnels_drained;    /* _ARGO: integer */
};

struct zpn_broker_drain_conn_stats {
    int64_t drainable_conn_count;
    int64_t total_conn_count;
};
struct zpn_broker_drain_dry_run_stats {     /* _ARGO: object_definition */
    int64_t drainable_customer_count;       /* _ARGO: integer */
    int64_t total_customer_count;           /* _ARGO: integer */
    int64_t drainable_conn_count;           /* _ARGO: integer */
    int64_t total_conn_count;               /* _ARGO: integer */
    int     drainable_conn_pc;              /* _ARGO: integer */
};
struct zpn_broker_drain_stats_table_entry {
    int64_t customer_gid;
    /* 2 dimensional array indexing client type and then drain type */
    struct zpn_broker_drain_stats_entry stats[zpn_client_type_total_count][drain_type_total_count];
};

enum zpn_conn_drain_subtype zpn_conn_get_drain_subtype(enum zpn_conn_drain_type drain_type);
extern const char *zpn_conn_get_drain_reason_string_for_type(enum zpn_conn_drain_type type);
extern const char *zpn_conn_get_drain_status_string(enum zpn_conn_drain_status drain_status);
extern const char *zpn_conn_get_drain_type_string(enum zpn_conn_drain_type drain_type);
extern const char *zpn_conn_get_drain_subtype_string(enum zpn_conn_drain_subtype drain_subtype);

int zpn_broker_drain_init(void);

void zpn_broker_drain_mark_cstate(struct zpn_broker_client_fohh_state *c_state,
                                  enum zpn_conn_drain_type drain_type,
                                  enum zpn_conn_drain_status drain_status);
void zpn_broker_drain_unmark_cstate(struct zpn_broker_client_fohh_state *c_state,
                                    enum zpn_conn_drain_type drain_type);
void zpn_broker_drain_unmark_cstate_all(struct zpn_broker_client_fohh_state *c_state);
void zpn_broker_drain_info_cleanup(struct zpn_broker_client_fohh_state *c_state,
                                   enum zpn_conn_drain_status final_drain_status);
void zpn_broker_client_drain_monitor(struct zpn_broker_client_fohh_state *c_state);

int zpn_broker_is_drain_scheduled_for_customer(int64_t customer_gid,
                                               enum zpn_conn_drain_type *type_p);
int64_t zpn_broker_drain_get_manual_drain_timer_expiry(int64_t customer_gid);
int64_t zpn_broker_drain_get_manual_drain_max_timer_expiry(int64_t customer_gid);
int zpn_broker_is_drainable_conn(struct zpn_broker_client_fohh_state *c_state);
void zpn_broker_update_passive_stats_customer_gid_entry(struct zpn_broker_client_fohh_state *c_state, int is_increment);

int zpn_broker_get_c_state_drain_expiry_info(const struct zpn_broker_client_fohh_state *c_state,
                                             int64_t *min_expiry_s,
                                             enum zpn_conn_drain_type *initiator_type_p);

void zpn_broker_process_drain_request_all_customers(enum zpn_conn_drain_type drain_type,
                                               enum zpn_conn_drain_subtype drain_subtype,
                                               const char *reason, int ignore_config,
                                               int64_t drain_timeout_s, uint32_t percentage);

void zpn_broker_update_drain_status(struct zpn_broker_client_fohh_state *c_state,
                                    enum zpn_conn_drain_type type,
                                    enum zpn_conn_drain_status status,
                                    const char *file, int line);
void zpn_broker_increment_mtunnel_drained_count(struct zpn_broker_client_fohh_state *c_state, int64_t mtunnel_count);

#endif
