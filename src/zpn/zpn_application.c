/*
 * zpn_application.c. Copyright (C) 2014 Zscaler, Inc. All Rights Reserved.
 */

#include <ctype.h>

#include "argo/argo.h"
#include "argo/argo_hash.h"
#include "diamond/diamond.h"
#include "zpath_lib/zpath_app.h"
#include "zpath_lib/zpath_debug.h"
#include "zradix/zradix.h"
#include "zpath_lib/zpath_cidr_lookup.h"
#include "zpn/zpn_lib.h"
#include "zpn/zpn_application.h"
#include "zpn/zpn_application_compiled.h"
#include "zpn/zpn_application_domain.h"
#include "zpn/zpn_assistantgroup_assistant_relation.h"
#include "zpn/zpn_app_group_relation.h"
#include "zpn/zpn_server_group_assistant_group.h"
#include "zpn/zpn_application_group_application_mapping.h"
#include "zpn/zpn_application_group.h"
#include "zpn/zpn_scope.h"
#include "zpn/zpn_scope_engine.h"
#include "zpath_misc/zpath_dta_checkpoint.h"
#include "zpath_lib/zpath_config_override_keys.h"
#include "zpath_lib/zpath_config_override.h"
#include "zpn/zpn_policy_engine.h"
/* #include "zpn_broker_client_apps.h" */ //Adding this dependency would mean including more libraries to libzpn_"small" making it no more small

int zpn_application_fully_load = 1;
/* Used for unit testing zpn application */
int zpn_application_unit_test = 0;
static int zpn_application_tenant_is_broker = 0;

struct argo_structure_description *zpn_application_description;

struct zpath_allocator zpn_app_allocator = ZPATH_ALLOCATOR_INIT("zpn_app");

struct wally_index_column **zpn_application_index_column = NULL;
struct wally_index_column **zpn_application_customer_gid_column = NULL;

int g_app_debug_log = 0;
static int is_broker_or_exporter_tenant = 0;
int64_t g_app_match_sipa_only_apps_for_sipa_client_hard_disabled_feature_status = 0;

#define INT_TO_IPV4_MASK(x) (0xffffffff << (32 - x))

#define TLD_1_SEND_THREAD_POOL "tld_1_send"
#define DEFAULT_TLD_1_SEND_PUSH_US (50*1000)

#define APP_MULTI_MATCH_TOGGLE_THREAD "app_multi_match_toggle_thread"
#define APP_MULTI_MATCH_TOGGLE_THREAD_POOL "app_multi_match_toggle"

#define PATTERN_MATCH_ERR_CODE_OFFSET 20

#define APP_DOMAIN_PROCESS_CHUNK_SIZE 20
#define APP_DOMAIN_PROCESS_LOG_SEC 1

/* comment out for production, comment in for Unit Testing */
/* #define APP_DOMAIN_PROCESS_UNIT_TEST */

static inline void
zpn_application_app_pattern_match_feature_flag_init(int64_t customer_gid, struct zpn_customer_application_state *state);

static inline void
zpn_application_app_enqueue_dequeue_fix_feature_flag_init(int64_t customer_gid, struct zpn_customer_application_state *state);

static int zpn_app_multi_match_status(struct zpath_debug_state *request_state,
                                      const char **query_values,
                                      int query_value_count,
                                      void *cookie);
static int zpn_app_pattern_match_status(struct zpath_debug_state *request_state,
                                  const char **query_values,
                                  int query_value_count,
                                  void *cookie);
static int zpn_app_match_sipa_only_apps_for_sipa_client_status(struct zpath_debug_state *request_state,
                                                               const char **query_values,
                                                               int query_value_count,
                                                               void *cookie);
enum multi_match_toggle_type {
    mmt_toggle_enabled,  /* customer who is enabled for multi-match, no matter it is configured or using default/global value */
    mmt_toggle_not_configured /* customer who do not a overriding config. (who uses default or global value) */
};

static struct zpn_application_search_stats {                /* _ARGO: * object_definition */
    /* multi-match stats */
    int64_t     mtunnel_selected_app_counts;                /* _ARGO: integer */
    int64_t     mtunnel_multiple_search_counts;             /* _ARGO: integer */
    int64_t     app_download_selected_app_counts;           /* _ARGO: integer */
    int64_t     app_download_multiple_search_counts;        /* _ARGO: integer */
    int64_t     app_scale_selected_app_counts;              /* _ARGO: integer */
    int64_t     app_scale_multiple_search_counts;           /* _ARGO: integer */
    int64_t     exporter_selected_app_counts;               /* _ARGO: integer */
    int64_t     exporter_multiple_search_counts;            /* _ARGO: integer */
    int64_t     pbroker_conn_selected_app_counts;           /* _ARGO: integer */
    int64_t     pbroker_conn_multiple_search_counts;        /* _ARGO: integer */
    int64_t     dns_check_selected_app_counts;              /* _ARGO: integer */
    int64_t     dns_check_multiple_search_counts;           /* _ARGO: integer */

    int64_t     mtunnel_app_not_found;                      /* _ARGO: integer */
    int64_t     mtunnel_application_not_found;              /* _ARGO: integer */
    int64_t     app_download_app_not_found;                 /* _ARGO: integer */
    int64_t     app_download_application_not_found;         /* _ARGO: integer */
    int64_t     app_download_pattern_apps_skipped;          /* _ARGO: integer */
    int64_t     app_download_catch_all_ip_apps_skipped;     /* _ARGO: integer */
    int64_t     app_scale_app_not_found;                    /* _ARGO: integer */
    int64_t     app_scale_application_not_found;            /* _ARGO: integer */
    int64_t     app_scale_dns_query_not_found;              /* _ARGO: integer */
    int64_t     app_scale_dns_query_failed;                 /* _ARGO: integer */
    int64_t     app_scale_fill_app_check_fdqn_failed;       /* _ARGO: integer */
    int64_t     app_scale_fill_app_check_wc_failed;         /* _ARGO: integer */
    int64_t     app_scale_err_dns_check_not_found;          /* _ARGO: integer */
    int64_t     dns_check_app_not_found;                    /* _ARGO: integer */
    int64_t     dns_check_application_not_found;            /* _ARGO: integer */
    int64_t     dns_check_dns_query_not_found;              /* _ARGO: integer */
    int64_t     dns_check_dns_query_failed;                 /* _ARGO: integer */
    int64_t     dns_check_err_not_found;                    /* _ARGO: integer */
    int64_t     exporter_app_not_found;                     /* _ARGO: integer */
    int64_t     exporter_application_not_found;             /* _ARGO: integer */
    int64_t     pbroker_conn_app_not_found;                 /* _ARGO: integer */
    int64_t     pbroker_conn_application_not_found;         /* _ARGO: integer */
    int64_t     pbroker_conn_dns_query_not_found;           /* _ARGO: integer */
    int64_t     pbroker_conn_dns_query_failed;              /* _ARGO: integer */
    int64_t     pbroker_conn_err_dns_check_not_found;       /* _ARGO: integer */

    /* pattern match stats */
    int64_t     diamond_match_counts;                       /* _ARGO: integer */
    int64_t     pattern_match_counts;                       /* _ARGO: integer */
    int64_t     diamond_search_counts;                      /* _ARGO: integer */

    /* application search type agnostic stats */
    int64_t     diamond_domain_not_found;                   /* _ARGO: integer */
    int64_t     ip_lookup_not_found;                        /* _ARGO: integer */
    int64_t     specific_and_matched_app_differ;            /* _ARGO: integer */
    int64_t     diamond_add_failed;                         /* _ARGO: integer */
    int64_t     diamond_remove_failed;                      /* _ARGO: integer */
    int64_t     app_add_pattern_not_supported;              /* _ARGO: integer */
    int64_t     app_remove_pattern_not_supported;           /* _ARGO: integer */
    int64_t     ipv4_add_failed;                            /* _ARGO: integer */
    int64_t     ipv6_add_failed;                            /* _ARGO: integer */
    int64_t     ipv4_remove_failed;                         /* _ARGO: integer */
    int64_t     ipv6_remove_failed;                         /* _ARGO: integer */

    int64_t app_scale_err_strict_dns_check_failed;          /* _ARGO: integer */
    int64_t dns_check_err_strict_dns_check_failed;          /* _ARGO: integer */

    int64_t     multi_match_large_domains;                  /* _ARGO: integer */
    int64_t     multi_match_large_ips;                      /* _ARGO: integer */
    int64_t     multi_match_large_apps;                     /* _ARGO: integer */

    /* overlapping ports stats*/
    int64_t     app_download_app_overlapping_ports;         /* _ARGO: integer */
    int64_t     app_scale_app_overlapping_ports;            /* _ARGO: integer */
} zpn_app_search_stats;

#include "zpn/zpn_application_compiled_c.h"

static struct argo_structure_description *zpn_application_search_stats_description;

typedef void (zpn_app_scaling_updated_callback_f)(const int64_t *config_value, int64_t customer_gid);
static zpn_app_scaling_updated_callback_f *zpn_app_scaling_updated_cb = NULL;

typedef int (tld_1_send_to_all_cb)(int64_t customer_gid,
                                   char** aggregated_domain_list,
                                   int aggregated_domain_count,
                                   int skip_app_scaling_feature_check,
                                   int64_t timer_fired_at_us,
                                   enum zpn_client_type client_type,
                                   enum zpn_platform_type platform_type);

static struct zpn_customer_application_state *state_get_and_lock(int64_t customer_gid, int wrlock);
static int64_t is_application_multi_match_enabled_for_customer(int64_t customer_gid);
static int64_t is_app_scaling_feature_enabled_for_customer(int64_t customer_gid);
static int is_pattern_match_feature_enabled(struct zpn_customer_application_state *state);
static int is_pattern_match_domain_update_enabled(struct zpn_customer_application_state *state);
static int is_domain_valid_to_scope(int64_t scope_gid, const char* domain, int skip_non_sipa_apps, int filter_by_scope);
static int zpn_application_search(int64_t scope_gid, int filter_by_scope,
                                  const char *domain_name, size_t domain_name_len,
                                  char *matched_domain, size_t matched_domain_len,
                                  int *is_wildcard, int skip_non_sipa_apps);
static int64_t is_application_pattern_match_enabled_for_customer(int64_t customer_gid);

static int64_t is_application_enqueue_dequeue_fix_enabled_for_customer(int64_t customer_gid);

static int64_t g_app_pattern_match_hard_disabled = 0;

ZTAILQ_HEAD(zpn_domain_entry_head, zpn_domain_entry);

struct zpn_domain_entry {
    ZTAILQ_ENTRY(zpn_domain_entry) list;
    char *domain;
};

/*
 * This is Queue which contains all the
 * domains which needs to be processed
 * @lock : mutex lock to add/del elem from queue
 * @customer_gid : Customer for whom this queue is created
 * @domain_list : Queue head
 * @hashed_domain_list : hash table for O(1) lookup if element exists in the queue
 * @total_queue_elements : Count of no of elements in the queue
 */
struct zpn_application_domain_queue {
    zpath_mutex_t lock;
    int64_t customer_gid;
    struct zpn_domain_entry_head domain_list; // This is needed to process the queue
    struct zhash_table* hashed_domain_list; // This is needed for O(1) lookup
    int64_t total_queue_elements; // Total number of elements in the queue
    uint8_t on_thread;  // if set, there is already a consumer on the thread, so producer should not start a new thread for consummer.
};

/*
 * These all contain allocated strings with the name that was matched.
 */
typedef void (zpn_app_multi_match_updated_callback_f)(int64_t customer_gid);
struct zpn_customer_application_state {
    struct diamond_state *domain_lookup;
    struct zradix_tree *ipv4_lookup;
    struct zradix_tree *ipv6_lookup;
    unsigned registered                 : 1;
    unsigned registration_completed     : 1;
    unsigned app_scaling_registered     : 1;
    unsigned spare                      : 29;
    int32_t outstanding_registration_count;
    int64_t app_pattern_match_feature_enabled;
    int64_t app_multi_match_feature_enabled;
    int64_t app_match_sipa_only_apps_for_sipa_client;
    int64_t app_enqueue_dequeue_fix_enabled;
    /* Used to track pattern match feature dependency with app scaling feature.
     * This is used only during application search to enforce app scaling.
     */
    int64_t app_scaling_feature_enabled;
    struct wally_callback_queue *wally_callback_queue;
    struct zpn_application_domain_queue *domain_queue;
    int64_t version;
    zpn_app_multi_match_updated_callback_f *multi_match_for_app_scaling_cb;
    zpn_app_multi_match_updated_callback_f *multi_match_for_app_download_cb;
    struct zpath_rwlock customer_app_lock;
};

struct zhash_table *customers_applications;

static struct zpath_rwlock local_app_lock;

struct tld_1_walk_data {
    char ** data;
    size_t max_sz;
    size_t idx;
    int shallow;
};

struct customer_tld_1_state {
    int64_t customer_gid;

    /*
     * tld_1_domain (c_string) => domain_set (set of c_string)
     * e.g "google.com" => set("mail.google.com", "map.goole.com")
     */
    struct zhash_table *domain_table;

    zpath_mutex_t tld_state_lock;

    /*
     * If the timer exists, then we are delaying tld_1_send to clients
     * until the timer fires.
     */
    struct event *timer;

    /* The thread to send tld_1 domains to zapp */
    struct zevent_base *tld_1_send_thread;

    /* send tld_1 to all clients of the customer */
    tld_1_send_to_all_cb *send_to_all_cb;
};

static void zpn_app_multi_match_feature_toggled_cb(const int64_t *config_value, int64_t customer_gid);

/* customer_gid (int64_t) => customer_tld_1 (struct customer_tld_1_state) */
struct zhash_table *tld_1_domains = NULL;
zpath_mutex_t tld_1_domains_lock;
char star[] = {"*"};

/*
 * Enable for the application multi match feature test
 */
int zpn_app_multi_match_flag_test = 0;
void zpn_enable_app_multi_match_feature_test() { zpn_app_multi_match_flag_test = 1; }

static int create_multi_match_toggle_event_base()
{
    static pthread_mutex_t local_multi_match_lock = PTHREAD_MUTEX_INITIALIZER;
    static struct zevent_base *zbase = NULL;
    int res = ZPN_RESULT_NO_ERROR;

    pthread_mutex_lock(&local_multi_match_lock);
    if (!zbase) {
        zbase = zevent_handler_create(APP_MULTI_MATCH_TOGGLE_THREAD, 16*1024*1024, 30);
        if (zbase) {
            zevent_add_to_class(zbase, APP_MULTI_MATCH_TOGGLE_THREAD_POOL);
        } else {
            res = ZPN_RESULT_ERR;
        }
    }
    pthread_mutex_unlock(&local_multi_match_lock);
    return res;
}

void zpn_application_search_stats_counter(enum zpn_app_search_type type, int64_t selected_app_counts)
{
    ZPN_DEBUG_APPLICATION("Search type: %d, selected_app_counts: %"PRId64, type, selected_app_counts);
    switch (type) {
        case zpn_mtunnel_search:
            __sync_add_and_fetch_8(&(zpn_app_search_stats.mtunnel_selected_app_counts), selected_app_counts);
            __sync_add_and_fetch_8(&(zpn_app_search_stats.mtunnel_multiple_search_counts), 1);
            break;
        case zpn_app_download_search:
            __sync_add_and_fetch_8(&(zpn_app_search_stats.app_download_selected_app_counts), selected_app_counts);
            __sync_add_and_fetch_8(&(zpn_app_search_stats.app_download_multiple_search_counts),1);
            break;
        case zpn_app_scale_search:
            __sync_add_and_fetch_8(&(zpn_app_search_stats.app_scale_selected_app_counts), selected_app_counts);
            __sync_add_and_fetch_8(&(zpn_app_search_stats.app_scale_multiple_search_counts), 1);
            break;
        case zpn_exporter_search:
            __sync_add_and_fetch_8(&(zpn_app_search_stats.exporter_selected_app_counts), selected_app_counts);
            __sync_add_and_fetch_8(&(zpn_app_search_stats.exporter_multiple_search_counts), 1);
            break;
        case zpn_dns_check_search:
            __sync_add_and_fetch_8(&(zpn_app_search_stats.dns_check_selected_app_counts), selected_app_counts);
            __sync_add_and_fetch_8(&(zpn_app_search_stats.dns_check_multiple_search_counts), 1);
            break;
        case zpn_pbroker_conn_search:
            __sync_add_and_fetch_8(&(zpn_app_search_stats.pbroker_conn_selected_app_counts), selected_app_counts);
            __sync_add_and_fetch_8(&(zpn_app_search_stats.pbroker_conn_multiple_search_counts), 1);
            break;
        default:
            /* invalid */
            break;
    }
}

static void zpn_diamond_search_stats_counter(int64_t diamond_match_counts, int64_t pattern_match_counts)
{
    __sync_add_and_fetch_8(&(zpn_app_search_stats.diamond_match_counts), diamond_match_counts);
    __sync_add_and_fetch_8(&(zpn_app_search_stats.pattern_match_counts), pattern_match_counts);
    __sync_add_and_fetch_8(&(zpn_app_search_stats.diamond_search_counts), 1);
}

void zpn_application_search_debug_stats_counter(enum zpn_app_search_type search_type, enum zpn_app_search_debug_stats_type stat_type)
{
    ZPN_DEBUG_APPLICATION("Search type: %d, selected_app_counts: %d", search_type, stat_type);
    switch (search_type) {
    case zpn_mtunnel_search:
        if (stat_type == zpn_app_not_found) {
            __sync_add_and_fetch_8(&(zpn_app_search_stats.mtunnel_app_not_found), 1);
        } else if (stat_type == zpn_application_not_found) {
            __sync_add_and_fetch_8(&(zpn_app_search_stats.mtunnel_application_not_found), 1);
        }
        break;
    case zpn_app_download_search:
        if (stat_type == zpn_app_not_found) {
            __sync_add_and_fetch_8(&(zpn_app_search_stats.app_download_app_not_found), 1);
        } else if (stat_type == zpn_application_not_found) {
            __sync_add_and_fetch_8(&(zpn_app_search_stats.app_download_application_not_found), 1);
        } else if (stat_type == zpn_pattern_apps_skipped) {
            __sync_add_and_fetch_8(&(zpn_app_search_stats.app_download_pattern_apps_skipped), 1);
        } else if (stat_type == zpn_catch_all_ip_apps_skipped) {
            __sync_add_and_fetch_8(&(zpn_app_search_stats.app_download_catch_all_ip_apps_skipped), 1);
        } else if (stat_type == zpn_app_overlapping_ports) {
            __sync_add_and_fetch_8(&(zpn_app_search_stats.app_download_app_overlapping_ports), 1);
        }
        break;
    case zpn_app_scale_search:
        switch(stat_type) {
        case zpn_app_not_found:
            __sync_add_and_fetch_8(&(zpn_app_search_stats.app_scale_app_not_found), 1);
            break;
        case zpn_application_not_found:
            __sync_add_and_fetch_8(&(zpn_app_search_stats.app_scale_application_not_found), 1);
            break;
        case zpn_dns_query_failed:
            __sync_add_and_fetch_8(&(zpn_app_search_stats.app_scale_dns_query_failed), 1);
            break;
        case zpn_dns_query_not_found:
            __sync_add_and_fetch_8(&(zpn_app_search_stats.app_scale_dns_query_not_found), 1);
            break;
        case zpn_fill_app_check_fdqn_failed:
            __sync_add_and_fetch_8(&(zpn_app_search_stats.app_scale_fill_app_check_fdqn_failed), 1);
            break;
        case zpn_fill_app_check_wc_failed:
            __sync_add_and_fetch_8(&(zpn_app_search_stats.app_scale_fill_app_check_wc_failed), 1);
            break;
        case zpn_dns_check_err_not_found:
            __sync_add_and_fetch_8(&(zpn_app_search_stats.app_scale_err_dns_check_not_found), 1);
            break;
        case zpn_dns_check_err_strict_failed:
            __sync_add_and_fetch_8(&(zpn_app_search_stats.app_scale_err_strict_dns_check_failed), 1);
            break;
        case zpn_app_overlapping_ports:
            __sync_add_and_fetch_8(&(zpn_app_search_stats.app_scale_app_overlapping_ports), 1);
            break;
        default:
            break;
        }
        break;
    case zpn_dns_check_search:
        switch (stat_type) {
        case zpn_app_not_found:
            __sync_add_and_fetch_8(&(zpn_app_search_stats.dns_check_app_not_found), 1);
            break;
        case zpn_application_not_found:
            __sync_add_and_fetch_8(&(zpn_app_search_stats.dns_check_application_not_found), 1);
            break;
        case zpn_dns_query_failed:
            __sync_add_and_fetch_8(&(zpn_app_search_stats.dns_check_dns_query_failed), 1);
            break;
        case zpn_dns_query_not_found:
            __sync_add_and_fetch_8(&(zpn_app_search_stats.dns_check_dns_query_not_found), 1);
            break;
        case zpn_dns_check_err_not_found:
            __sync_add_and_fetch_8(&(zpn_app_search_stats.dns_check_err_not_found), 1);
            break;
        case zpn_dns_check_err_strict_failed:
            __sync_add_and_fetch_8(&(zpn_app_search_stats.dns_check_err_strict_dns_check_failed), 1);
            break;
        default:
            break;
        }
        break;
    case zpn_exporter_search:
        if (stat_type == zpn_app_not_found) {
            __sync_add_and_fetch_8(&(zpn_app_search_stats.exporter_app_not_found), 1);
        } else if (stat_type == zpn_application_not_found) {
            __sync_add_and_fetch_8(&(zpn_app_search_stats.exporter_application_not_found), 1);
        }
        break;
    case zpn_pbroker_conn_search:
        switch (stat_type) {
        case zpn_app_not_found:
            __sync_add_and_fetch_8(&(zpn_app_search_stats.pbroker_conn_app_not_found), 1);
            break;
        case zpn_application_not_found:
            __sync_add_and_fetch_8(&(zpn_app_search_stats.pbroker_conn_application_not_found), 1);
            break;
        case zpn_dns_query_failed:
            __sync_add_and_fetch_8(&(zpn_app_search_stats.pbroker_conn_dns_query_failed), 1);
            break;
        case zpn_dns_query_not_found:
            __sync_add_and_fetch_8(&(zpn_app_search_stats.pbroker_conn_dns_query_not_found), 1);
            break;
        case zpn_dns_check_err_not_found:
            __sync_add_and_fetch_8(&(zpn_app_search_stats.pbroker_conn_err_dns_check_not_found), 1);
            break;
        default:
            break;
        }
        break;
    case zpn_app_search_none:
        /* Stats not specific to search type */
        switch (stat_type) {
        case zpn_diamond_domain_not_found:
            __sync_add_and_fetch_8(&(zpn_app_search_stats.diamond_domain_not_found), 1);
            break;
        case zpn_ip_lookup_not_found:
            __sync_add_and_fetch_8(&(zpn_app_search_stats.ip_lookup_not_found), 1);
            break;
        case zpn_specific_and_matched_app_differ:
            __sync_add_and_fetch_8(&(zpn_app_search_stats.specific_and_matched_app_differ), 1);
            break;
        case zpn_diamond_add_failed:
            __sync_add_and_fetch_8(&(zpn_app_search_stats.diamond_add_failed), 1);
            break;
        case zpn_diamond_remove_failed:
            __sync_add_and_fetch_8(&(zpn_app_search_stats.diamond_remove_failed), 1);
            break;
        case zpn_app_add_pattern_not_supported:
            __sync_add_and_fetch_8(&(zpn_app_search_stats.app_add_pattern_not_supported), 1);
            break;
        case zpn_app_remove_pattern_not_supported:
            __sync_add_and_fetch_8(&(zpn_app_search_stats.app_remove_pattern_not_supported), 1);
            break;
        case zpn_ipv4_add_failed:
            __sync_add_and_fetch_8(&(zpn_app_search_stats.ipv4_add_failed), 1);
            break;
        case zpn_ipv6_add_failed:
            __sync_add_and_fetch_8(&(zpn_app_search_stats.ipv6_add_failed), 1);
            break;
        case zpn_ipv4_remove_failed:
            __sync_add_and_fetch_8(&(zpn_app_search_stats.ipv4_remove_failed), 1);
            break;
        case zpn_ipv6_remove_failed:
            __sync_add_and_fetch_8(&(zpn_app_search_stats.ipv6_remove_failed), 1);
            break;
        case zpn_multi_match_large_domains_matched:
            __sync_add_and_fetch_8(&(zpn_app_search_stats.multi_match_large_domains), 1);
            break;
        case zpn_multi_match_large_ips_matched:
            __sync_add_and_fetch_8(&(zpn_app_search_stats.multi_match_large_ips), 1);
            break;
        case zpn_multi_match_large_apps:
            __sync_add_and_fetch_8(&(zpn_app_search_stats.multi_match_large_apps), 1);
            break;
        default:
            break;
        }
        break;
    default:
        break;
    }
}

static int64_t g_app_multi_match_hard_disabled = 0;

/*
 * Initialization of
 *  1. hash table for easy lookup for elements in queue
 *  2. queue of domains for easy processing
 */
struct zpn_application_domain_queue *zpn_application_domain_queue_create(int64_t customer_gid)
{
    struct zpn_application_domain_queue *queue = NULL;

    queue = ZPN_APP_CALLOC(sizeof(*queue));
    if (queue) {
        queue->lock = ZPATH_MUTEX_INIT;
        queue->hashed_domain_list = zhash_table_alloc(&zpn_app_allocator);
        if (!queue->hashed_domain_list) {
            ZPATH_MUTEX_DESTROY(&(queue->lock), _FILE_, _LINE_);
            ZPN_APP_FREE(queue);
            return NULL;
        }
        ZTAILQ_INIT(&(queue->domain_list));
        queue->total_queue_elements = 0;
        queue->customer_gid = customer_gid;
    }
    return queue;
}

/* Free domain memory
 * Lock is held before calling this func
 */
static void zpn_application_queue_entry_delete(struct zpn_domain_entry *entry)
{
    if (!entry) return;
    if (entry->domain) ZPN_APP_FREE(entry->domain);
    ZPN_APP_FREE(entry);
}

/*
 * Copy the domain name to keep in queue
 */
static struct zpn_domain_entry *zpn_application_queue_entry_create(char *domain)
{
    struct zpn_domain_entry *entry;

    if (!domain) return NULL;

    entry = ZPN_APP_CALLOC(sizeof(*entry));
    if (!entry) return NULL;

    entry->domain = ZPN_APP_STRDUP(domain, strlen(domain));
    if (!entry->domain) {
        ZPN_APP_FREE(entry);
        return NULL;
    }
    return entry;
}

/*
 * Process the queue elements
 */
static void zpn_application_process_queue_without_base(void *cookie1, void *cookie2)
{
    struct zpn_application_domain_queue *queue = cookie1;
    struct zpn_domain_entry *entry;
    int res;
    int64_t domains_in_queue = 0;
    int process_count = 0;
    int process_count_hb = 0;
    int hb_reset_cnt = 0;
    int64_t start_s = 0;

    if (!queue) {
        ZPN_LOG(AL_ERROR, "Queue is not initialized");
        return;
    }

    ZPN_DEBUG_APPLICATION("Queue elements being processed = %"PRId64, queue->total_queue_elements);

    start_s = monotime_s();
    while (1) {
        int64_t start_us = 0;
        if (zpn_debug_get(ZPN_DEBUG_APPLICATION_IDX)) {
            start_us = epoch_us();
        }
        ZPATH_MUTEX_LOCK(&(queue->lock), __FILE__, __LINE__);
        entry = ZTAILQ_FIRST(&(queue->domain_list));
        if (!entry) {
            queue->on_thread = 0;
            ZPATH_MUTEX_UNLOCK(&(queue->lock), __FILE__, __LINE__);
            __sync_add_and_fetch_8(&(policy_config_change_stats.num_domain_process_empty_queue),1);
            if (hb_reset_cnt > 0) {
                ZPN_LOG(AL_NOTICE, "Application domain processing: empty queue after %d time heartbeat reset for customer: %"PRId64,
                                   hb_reset_cnt, queue->customer_gid);
            }
            break;
        }
        zhash_table_remove(queue->hashed_domain_list, entry->domain, strlen(entry->domain), NULL);
        ZPN_DEBUG_APPLICATION("process one domain update, Domain %s removed to hash for customer: %"PRId64, entry->domain, queue->customer_gid);
        ZTAILQ_REMOVE(&(queue->domain_list), entry, list);
        queue->total_queue_elements--; /* This can be incremented in a producer thread after unlock during this while loop time */
        ZPATH_MUTEX_UNLOCK(&(queue->lock), __FILE__, __LINE__);

        /* Do not set queue->on_thread to 0 until the processing is done, the following two functions must be executed synchronously */
        res = zpn_application_domain_changed(queue->customer_gid, entry->domain);
        if (res) {
            ZPN_LOG(AL_ERROR, "customer %"PRId64" zpn_domain_changed failed for domain %s, res=%s",
                              queue->customer_gid, entry->domain, zpath_result_string(res));
        }
        zpn_application_queue_entry_delete(entry);

        if (zpn_debug_get(ZPN_DEBUG_APPLICATION_IDX)) {
            int64_t delta_us = epoch_us() - start_us;
            ZPN_DEBUG_APPLICATION("process one domain update, customer=%"PRId64", delta_us=%"PRId64, queue->customer_gid, delta_us);
        }

        process_count++;
        __sync_add_and_fetch_8(&(policy_config_change_stats.num_domain_processed), 1);
        if (process_count >= APP_DOMAIN_PROCESS_CHUNK_SIZE) {
            process_count_hb += process_count;
            process_count = 0;
#ifdef APP_DOMAIN_PROCESS_UNIT_TEST
            int64_t now_s = monotime_s() + process_count_hb/APP_DOMAIN_PROCESS_CHUNK_SIZE;
#else
            int64_t now_s = monotime_s();
#endif
            int64_t delta_s = now_s - start_s;
            if (delta_s >= APP_DOMAIN_PROCESS_LOG_SEC) {
                zthread_heartbeat(NULL);
                hb_reset_cnt++;
                __sync_add_and_fetch_8(&(policy_config_change_stats.num_domain_process_reset_heartbeat),1);
                __sync_add_and_fetch_8(&(policy_config_change_stats.num_domain_processed_for_heartbeat),process_count_hb);
                ZPATH_MUTEX_LOCK(&(queue->lock), __FILE__, __LINE__);
                domains_in_queue = queue->total_queue_elements;
                ZPATH_MUTEX_UNLOCK(&(queue->lock), __FILE__, __LINE__);
                ZPN_LOG(AL_WARNING, "Application domain processing: reset heartbeat, customer: %"PRId64", delta_s: %"PRId64
                                    ", domains processed in this chunk: %d, domains still in queue: %"PRId64", heartbeat %d times",
                                    queue->customer_gid, delta_s, process_count_hb, domains_in_queue, hb_reset_cnt);
                start_s = now_s;
                process_count_hb = 0;
            }
        }
    }
}

static inline int is_enqueue_dequeue_fix_feature_enabled(const struct zpn_customer_application_state *state)
{
    int feature_status = 0;
    if (state && state->app_enqueue_dequeue_fix_enabled) {
        feature_status = 1;
    }
    return feature_status;
}

int is_enqueue_dequeue_fix_feature_enabled_for_customer(int64_t customer_gid) {
    const struct zpn_customer_application_state *state = zpn_application_customer_state_get(customer_gid);

    return is_enqueue_dequeue_fix_feature_enabled(state);
}

/*
 * Process the queue elements
 */
static void zpn_application_process_queue(struct zevent_base *base,
                                          void *cookie1,
                                          int64_t cookie2)
{
    return zpn_application_process_queue_without_base(cookie1, 0);
}

/* If the value does not exists in the queue then add it */
int zpn_application_domain_queue_add(struct zpn_application_domain_queue *queue,
                                     char *domain, const struct zpn_customer_application_state *state)
{

    if (!queue || !domain) return ZPATH_RESULT_BAD_ARGUMENT;
    struct zpn_domain_entry * entry = NULL;
    int64_t enq_deq_fix_feature = is_enqueue_dequeue_fix_feature_enabled(state);

    ZPATH_MUTEX_LOCK(&(queue->lock), __FILE__, __LINE__);
    entry = zhash_table_lookup(queue->hashed_domain_list, domain, strlen(domain), NULL);
    if (entry) {
        ZPN_DEBUG_APPLICATION("Domain %s already exist in hash for customer: %"PRId64, domain, queue->customer_gid);
        if (enq_deq_fix_feature) {
            ZTAILQ_REMOVE(&(queue->domain_list), entry, list);
            ZTAILQ_INSERT_TAIL(&(queue->domain_list), entry, list);
        }
        ZPATH_MUTEX_UNLOCK(&(queue->lock), __FILE__, __LINE__);
        return ZPATH_RESULT_NO_ERROR;
    }

    entry = zpn_application_queue_entry_create(domain);
    if (!entry) {
        ZPATH_MUTEX_UNLOCK(&(queue->lock), __FILE__, __LINE__);
        return ZPATH_RESULT_NO_MEMORY;
    }

    ZPN_DEBUG_APPLICATION("Domain %s Added to hash for customer: %"PRId64, domain, queue->customer_gid);
    zhash_table_store(queue->hashed_domain_list, entry->domain, strlen(entry->domain), 1, entry);

    if (enq_deq_fix_feature) {
        ZTAILQ_INSERT_TAIL(&(queue->domain_list), entry, list);
    } else {
        ZTAILQ_INSERT_HEAD(&(queue->domain_list), entry, list);
    }

    queue->total_queue_elements++;
    __sync_add_and_fetch_8(&(policy_config_change_stats.num_domain_in_process_queue), 1);

    /* Create event only if the queue is not empty and is not being processed on any thread */
    if (queue->total_queue_elements > 0 && !queue->on_thread) {
        queue->on_thread = 1;
        /* When broker is starting up, then app calc thread
         * may not be initialized yet, so if this func is called
         * then process on the existing thread
         */
        if (zpath_service_init_complete) {
            if (zevent_base_call(zevent_get_for_class(ZEVENT_CLASS_APP_CALC),
                                 zpn_application_process_queue, queue, 0))
                ZPN_LOG(AL_ERROR, "Could not dispatch  zpn_application_process_queue ");
        } else {
            zevent_defer(zpn_application_process_queue_without_base, queue, NULL, 0);
        }
    }
    ZPATH_MUTEX_UNLOCK(&(queue->lock), __FILE__, __LINE__);

    return ZPATH_RESULT_NO_ERROR;
}

int zpn_application_domain_changed_defer_call(int64_t customer_gid,
                                              char *domain_name)
{
    struct zpn_customer_application_state *state;

    state = zpn_application_customer_state_get(customer_gid);
    if (!state) {
        ZPN_LOG(AL_CRITICAL, "Not able to get state while adding domain: %s for customer %"PRId64"",
                domain_name, customer_gid);
        return ZPATH_RESULT_ERR;
    }

    ZPN_DEBUG_APPLICATION("customer_gid: %"PRId64", domain_name: %s",
                          customer_gid, domain_name);
    int res = zpn_application_domain_queue_add(state->domain_queue, domain_name, state);
    if (res) {
        ZPN_LOG(AL_ERROR, "Failed to add domain %s to queue for customer %ld", domain_name, (long) customer_gid);
    }

    return res;
}

static void create_tld_1_event_base()
{
    static pthread_mutex_t local_tld_1_lock = PTHREAD_MUTEX_INITIALIZER;
    static struct zevent_base *zbase = NULL;

    pthread_mutex_lock(&local_tld_1_lock);
    if (!zbase) {
        zbase = zevent_handler_create("tld_1_send_thread", 16*1024*1024, 30);
        zevent_add_to_class(zbase, TLD_1_SEND_THREAD_POOL);
    }
    pthread_mutex_unlock(&local_tld_1_lock);
}

static int create_tld_1_domains()
{
    int res = ZPN_RESULT_NO_ERROR;
    ZPATH_MUTEX_LOCK(&(tld_1_domains_lock), __FILE__, __LINE__);
    if (!tld_1_domains) {
        tld_1_domains = zhash_table_alloc(&zpn_app_allocator);
        if (!tld_1_domains) {
            ZPN_LOG(AL_CRITICAL, "tld_1_domains alloc Fail");
            res = ZPN_RESULT_NO_MEMORY;
        }
        create_tld_1_event_base();
    }
    ZPATH_MUTEX_UNLOCK(&(tld_1_domains_lock), __FILE__, __LINE__);
    return res;
}

static struct customer_tld_1_state *get_customer_tld_1_domain(int64_t customer_gid)
{
    struct customer_tld_1_state *customer_tld_1;
    if (!tld_1_domains) {
        if (create_tld_1_domains()) return NULL;
    }

    customer_tld_1 = zhash_table_lookup(tld_1_domains, &customer_gid, sizeof(customer_gid), NULL);
    if(!customer_tld_1) {
        ZPATH_MUTEX_LOCK(&(tld_1_domains_lock), __FILE__, __LINE__);
        customer_tld_1 = zhash_table_lookup(tld_1_domains, &customer_gid, sizeof(customer_gid), NULL);
        if (!customer_tld_1) {
            customer_tld_1 = ZPN_APP_CALLOC(sizeof(*customer_tld_1));
            if(!customer_tld_1) {
                ZPN_LOG(AL_CRITICAL, "%ld customer_tld_1 alloc Fail", (long)customer_gid);
                ZPATH_MUTEX_UNLOCK(&(tld_1_domains_lock), __FILE__, __LINE__);
                return NULL;
            }
            customer_tld_1->domain_table = zhash_table_alloc(&zpn_app_allocator);
            if (!customer_tld_1->domain_table) {
                ZPN_APP_FREE(customer_tld_1);
                ZPN_LOG(AL_CRITICAL, "%ld domain_table alloc Fail", (long)customer_gid);
                ZPATH_MUTEX_UNLOCK(&(tld_1_domains_lock), __FILE__, __LINE__);
                return NULL;
            }

            customer_tld_1->customer_gid = customer_gid;
            customer_tld_1->tld_state_lock = ZPATH_MUTEX_INIT;
            customer_tld_1->tld_1_send_thread = zevent_get_for_class(TLD_1_SEND_THREAD_POOL);
            customer_tld_1->send_to_all_cb = NULL;

            if (zhash_table_store(tld_1_domains, &customer_gid, sizeof(customer_gid), 0, customer_tld_1) != 0) {
                zhash_table_free(customer_tld_1->domain_table);
                ZPN_APP_FREE(customer_tld_1);
                ZPN_LOG(AL_CRITICAL, "%ld customer_tld_1 store Fail", (long)customer_gid);
                ZPATH_MUTEX_UNLOCK(&(tld_1_domains_lock), __FILE__, __LINE__);
                return NULL;
            }
        }
        ZPATH_MUTEX_UNLOCK(&(tld_1_domains_lock), __FILE__, __LINE__);
    }
    return customer_tld_1;
}

void tld_1_add_send_callbacks(int64_t customer_gid, void *cookie_cb)
{
    struct customer_tld_1_state *customer_tld_1 = get_customer_tld_1_domain(customer_gid);
    if (customer_tld_1) {
        ZPATH_MUTEX_LOCK(&(customer_tld_1->tld_state_lock), __FILE__, __LINE__);
        customer_tld_1->send_to_all_cb = cookie_cb;
        ZPATH_MUTEX_UNLOCK(&(customer_tld_1->tld_state_lock), __FILE__, __LINE__);
    }
}

static int zpn_application_phrases_in_diamond_cb(void* cookie,
                                                 int wildcard_prefix,
                                                 const char* phrase,
                                                 int phrase_len,
                                                 int is_pattern)
{
    struct zpath_debug_state *request_state = cookie;
    if (is_pattern) {
        ZDP("\t\t%*.*s\n", phrase_len, phrase_len, phrase);
    } else {
        ZDP("\t%s%*.*s\n", wildcard_prefix > 0 ? "*." : "", phrase_len, phrase_len, phrase);
    }
    return ZPN_RESULT_NO_ERROR;
}

static void zpn_application_ip_app_dump(void *object, void* cookie)
{
    struct zpath_debug_state* request_state = cookie;
    char* ip_app_name = object;

    ZDP("\t%s\n", ip_app_name);
}

/*
 * This function will dump all the
 * domains stored in the customers_applications
 * for a particular customer gid
 *
 * How it works:
 *   Given a customer gid, walk through diamond table phrases
 */
static int zpn_application_domains_dump(struct zpath_debug_state* request_state,
                                       const char** query_values,
                                       int query_value_count,
                                       void* cookie)
{
    int res;
    int64_t customer_gid = 0;
    struct zpn_customer_application_state *state;
    struct diamond_state *diamond;
    int pattern_match = 0;

    (void) query_value_count;
    (void) cookie;

    if (query_values[0]) {
        customer_gid = strtoul(query_values[0], NULL, 10);
    } else {
        ZDP("Customer gid is required!\n");
        return ZPN_RESULT_NO_ERROR;
    }

    if (query_values[1]) {
        pattern_match = 1;
    }

    ZPATH_RWLOCK_RDLOCK(&(local_app_lock), __FILE__, __LINE__);
    state = zhash_table_lookup(customers_applications,
                               &customer_gid,
                               sizeof(customer_gid),
                               NULL);
    ZPATH_RWLOCK_UNLOCK(&(local_app_lock), __FILE__, __LINE__);
    if (!state) {
        ZDP("Customer with gid %"PRId64" does not exists!\n", customer_gid);
        return ZPN_RESULT_NO_ERROR;
    }

    ZPATH_RWLOCK_RDLOCK(&(state->customer_app_lock), __FILE__, __LINE__);
    diamond = state->domain_lookup;

    ZDP("FQDN domains in diamond tables are: \n");
    res = diamond_walk_with_pattern(diamond, zpn_application_phrases_in_diamond_cb, request_state, pattern_match);
    if (ZPN_RESULT_NO_ERROR != res) {
        ZDP("Diamond table walk failed - %s\n", zpath_result_string(res));
    }
    ZPATH_RWLOCK_UNLOCK(&(state->customer_app_lock), __FILE__, __LINE__);

    return res;
}

/*
 * This function will dump the application_domain_search
 * output for an application for a particular customer gid
 *
 * How it works:
 *   Given a customer gid, and application domain do a diamond_search
 */
static int
zpn_application_domain_dump(struct zpath_debug_state* request_state,
                                    const char** query_values,
                                    int query_value_count,
                                    void* cookie)
{
    int                                     res;
    int64_t                                 customer_gid = 0;
    int64_t                                 scope_gid = 0;
    const char                             *domain_name = query_values[1];
    size_t                                  domain_name_len;
    int                                     filter_by_scope = 0;
    char                                    matched_domain[MAX_APPLICATION_DOMAIN_LEN*RESULTS_ARRAY_SIZE];
    int                                     is_wildcard = 0;

    (void) query_value_count;
    (void) cookie;

    if (query_values[0]) {
        customer_gid = strtoul(query_values[0], NULL, 10);
    } else {
        ZDP("Customer gid is required!\n");
        return ZPN_RESULT_NO_ERROR;
    }

    if (!domain_name) {
        ZDP("Application Domain is required!\n");
        return ZPN_RESULT_NO_ERROR;
    }

    domain_name_len = strlen(domain_name);
    if (query_values[2]) {
        scope_gid = strtoul(query_values[2], NULL, 10);
        filter_by_scope = 1;

    }
    res = zpn_application_search((scope_gid ? scope_gid : customer_gid), filter_by_scope,
                                 domain_name, domain_name_len, matched_domain, sizeof(matched_domain), &is_wildcard, 0);

    if (res != ZPN_RESULT_NO_ERROR) {
        ZDP("Application Domain Search returned - %s\n", zpath_result_string(res));
    } else {
        ZDP("Application Domain Search returned - Matched domain: %s, Wildcard: %d\n", matched_domain, is_wildcard);
    }

    return ZPN_RESULT_NO_ERROR;
}

static int zpn_application_dump_stats(struct zpath_debug_state *request_state,
                                      const char **query_values __attribute__((unused)),
                                      int query_value_count __attribute__((unused)),
                                      void *cookie __attribute__((unused))) {
    char jsonout[10000];

    if (ARGO_RESULT_NO_ERROR == argo_structure_dump(zpn_application_search_stats_description,
                                                    &zpn_app_search_stats,
                                                    jsonout,
                                                    sizeof(jsonout),
                                                    NULL,
                                                    1)) {
        ZDP("%s\n", jsonout);
    }

    return ZPATH_RESULT_NO_ERROR;
}

static int zpn_application_inclusive_domain_dump(struct zpath_debug_state *request_state,
                                                 const char **query_values,
                                                 int query_value_count __attribute__((unused)),
                                                 void *cookie __attribute__((unused)))
{
    int                                     res = ZPN_RESULT_NO_ERROR;
    int64_t                                 customer_gid = 0;
    int64_t                                 scope_gid = 0;
    size_t                                  domain_name_len;
    size_t                                  domain_results_count = 0;
    char                                   *domain_results[100];
    const char                             *domain_name = query_values[1];

    if (query_values[0]) {
        customer_gid = strtoul(query_values[0], NULL, 10);
    } else {
        ZDP_ZPATH_LOG(AL_ERROR, "Customer gid is required!\n");
        return ZPN_RESULT_NO_ERROR;
    }

    if (!domain_name) {
        ZDP_ZPATH_LOG(AL_ERROR, "Application Domain is required!\n");
        return ZPN_RESULT_NO_ERROR;
    }

    domain_name_len = strlen(domain_name);
    if (domain_name_len > MAX_APPLICATION_DOMAIN_LEN) {
        ZDP_ZPATH_LOG(AL_ERROR, "Invalid input domain: %s\n", domain_name);
        return ZPN_RESULT_NO_ERROR;
    }

    if (query_values[2]) {
        scope_gid = strtoul(query_values[2], NULL, 10);
    }

    if (is_app_multi_match_search(customer_gid, zpn_client_type_zapp)) {
        domain_results_count = sizeof(domain_results) / sizeof(domain_results[0]);
        res = zpn_inclusive_domains_search((scope_gid ? scope_gid : customer_gid),
                                           domain_name,
                                           domain_results,
                                           &domain_results_count,
                                           PATTERN_DOMAIN_SEARCH_ENABLED);
    } else {
        ZDP_ZPATH_LOG(AL_INFO, "Application Multi match is disabled for customer - %"PRId64"\n", customer_gid);
        return ZPN_RESULT_NO_ERROR;
    }

    if (res != ZPATH_RESULT_NO_ERROR) {
        ZDP_ZPATH_LOG(AL_ERROR, "Inclusive domain search failed \n");
        return ZPN_RESULT_NO_ERROR;
    }

    ZDP_ZPATH_LOG(AL_INFO, "Matched domain counts - %ld, matched domains:\n", domain_results_count);
    for (int i = 0; i < domain_results_count; i++) {
        ZDP_ZPATH_LOG(AL_INFO, "\t %s\n", domain_results[i]);
    }

    return ZPN_RESULT_NO_ERROR;
}

/*
 * This function will dump all the IPv4
 * and IPv6 addresses stored in the customers_applications
 * for a particular customer gid
 *
 * How it works:
 *   Given a customer gid, walk through radix for all IPv4 and IPv6 app list
 */
static int zpn_application_ips_dump(struct zpath_debug_state* request_state,
                                    const char** query_values,
                                    int query_value_count,
                                    void* cookie)
{
    int64_t customer_gid = 0;
    struct zpn_customer_application_state *state;
    struct zradix_tree *ipv4_lookup;
    struct zradix_tree *ipv6_lookup;

    (void) query_value_count;
    (void) cookie;

    if (query_values[0]) {
        customer_gid = strtoul(query_values[0], NULL, 10);
    } else {
        ZDP("Customer gid is required!\n");
        return ZPN_RESULT_NO_ERROR;
    }

    ZPATH_RWLOCK_RDLOCK(&(local_app_lock), __FILE__, __LINE__);
    state = zhash_table_lookup(customers_applications,
                               &customer_gid,
                               sizeof(customer_gid),
                               NULL);
    ZPATH_RWLOCK_UNLOCK(&(local_app_lock), __FILE__, __LINE__);
    if (!state) {
        ZDP("Customer with gid %"PRId64" does not exists!\n", customer_gid);
        return ZPN_RESULT_NO_ERROR;
    }

    ZPATH_RWLOCK_RDLOCK(&(state->customer_app_lock), __FILE__, __LINE__);
    ipv4_lookup = state->ipv4_lookup;
    ipv6_lookup = state->ipv6_lookup;

    ZDP("IPv4 and IPv6 addresses are: \n");

    zradix_walk(ipv4_lookup, zpn_application_ip_app_dump, request_state);
    zradix_walk(ipv6_lookup, zpn_application_ip_app_dump, request_state);

    ZPATH_RWLOCK_UNLOCK(&(state->customer_app_lock), __FILE__, __LINE__);

    return ZPN_RESULT_NO_ERROR;
}

static struct zpn_customer_application_state *state_get_and_lock(int64_t customer_gid, int wrlock)
{
    struct zpn_customer_application_state *state;

    if (wrlock) {
        ZPATH_RWLOCK_WRLOCK(&(local_app_lock), __FILE__, __LINE__);
    } else {
        ZPATH_RWLOCK_RDLOCK(&(local_app_lock), __FILE__, __LINE__);
    }

    if (!customers_applications) {
        if (!wrlock) {
            ZPATH_RWLOCK_UNLOCK(&(local_app_lock), __FILE__, __LINE__);
            return state_get_and_lock(customer_gid, 1);
        }
        customers_applications = zhash_table_alloc(&zpn_app_allocator);
        if (!customers_applications) {
            ZPN_LOG(AL_CRITICAL, "Alloc");
            return NULL;
        }
    }

    state = zhash_table_lookup(customers_applications,
                               &customer_gid,
                               sizeof(customer_gid),
                               NULL);
    if (state) {
        return state;
    }

    state = ZPN_APP_CALLOC(sizeof(*state));
    if (state) {
        state->ipv4_lookup = zradix_create();
        if (!state->ipv4_lookup) {
            ZPN_APP_FREE(state);
            ZPN_LOG(AL_CRITICAL, "Failed to alloc radix for ipv4");
            return NULL;
        }
        state->ipv6_lookup = zradix_create();
        if (!state->ipv6_lookup) {
            zradix_free(state->ipv4_lookup);
            ZPN_APP_FREE(state);
            ZPN_LOG(AL_CRITICAL, "Failed to alloc radix for ipv6");
            return NULL;
        }
        state->domain_lookup = diamond_create(0, NULL, 10);
        if (!state->domain_lookup) {
            zradix_free(state->ipv4_lookup);
            zradix_free(state->ipv6_lookup);
            ZPN_APP_FREE(state);
            ZPN_LOG(AL_CRITICAL, "Failed to alloc diamond table");
            return NULL;
        }
        state->domain_queue = zpn_application_domain_queue_create(customer_gid);
        if (!state->domain_queue) {
            zradix_free(state->ipv4_lookup);
            zradix_free(state->ipv6_lookup);
            diamond_free(state->domain_lookup);
            ZPN_APP_FREE(state);
            ZPN_LOG(AL_CRITICAL, "Failed to alloc domain queue");
            return NULL;
        }
    }

    state->customer_app_lock = ZPATH_RWLOCK_INIT;

    zhash_table_store(customers_applications,
                      &customer_gid,
                      sizeof(customer_gid),
                      0,
                      state);

    return state;
}

int zpn_get_app_scaling_status(int64_t customer_gid)
{
    struct zpn_customer_application_state *state = zpn_application_customer_state_get(customer_gid);
    return (state && state->app_scaling_feature_enabled);
}

static struct zpn_customer_application_state *state_get_without_lock(int64_t customer_gid)
{
    struct zpn_customer_application_state *state = NULL;

    if (customers_applications) {
        state = zhash_table_lookup(customers_applications,
                                   &customer_gid,
                                   sizeof(customer_gid),
                                   NULL);
        if (state) {
            return state;
        }
    }

    state = state_get_and_lock(customer_gid, 1);
    ZPATH_RWLOCK_UNLOCK(&(local_app_lock), __FILE__, __LINE__);

    return state;
}

void load_application_state(int64_t customer_gid) {
    state_get_without_lock(customer_gid);
}

static int is_app_multi_match_feature_configured(int64_t customer_gid)
{
    return zpath_config_override_is_int_configured(APPLICATION_MULTI_MATCH_FEATURE, customer_gid);
}

static int is_app_multi_match_feature_enabled_regardless_of_hard_disable(int64_t customer_gid)
{
    struct zpn_customer_application_state *state = state_get_without_lock(customer_gid);
    if (state) {
        return state->app_multi_match_feature_enabled;
    } else {
        ZPN_LOG(AL_CRITICAL, "Failed to get app state, customer=%"PRId64, customer_gid);
        return 0;
    }
}

static void zpn_app_multi_match_toggled_cb(struct zevent_base *base, void *void_cookie, int64_t int_cookie)
{
    int64_t customer_gid = (int64_t)int_cookie;
    struct zpn_customer_application_state *state = state_get_without_lock(customer_gid);
    if (!state) {
        ZPN_LOG(AL_CRITICAL, "MULTI-MATCH, Failed to get state");
        return;
    }

    if (state->multi_match_for_app_scaling_cb) (*state->multi_match_for_app_scaling_cb)(customer_gid);
    if (state->multi_match_for_app_download_cb) (*state->multi_match_for_app_download_cb)(customer_gid);
}

static int zpn_app_multi_match_toggled_walk(void *cookie, void *object, void *key, size_t key_len)
{
    int64_t customer_gid = *((int64_t *)key);
    enum multi_match_toggle_type toggle_type = *((enum multi_match_toggle_type *)cookie);

    ZPN_DEBUG_APPLICATION("MULTI-MATCH: feature flag toggled type=%d, customer=%"PRId64, toggle_type, customer_gid);

    if ((toggle_type == mmt_toggle_enabled && is_app_multi_match_feature_enabled_regardless_of_hard_disable(customer_gid))
            || (toggle_type == mmt_toggle_not_configured && !is_app_multi_match_feature_configured(customer_gid)))  {
        zevent_base_call(zevent_get_for_class(APP_MULTI_MATCH_TOGGLE_THREAD_POOL), zpn_app_multi_match_toggled_cb, NULL, customer_gid);
    }

    return ZPATH_RESULT_NO_ERROR;
}

static inline void zpn_app_multi_match_toggled_all(enum multi_match_toggle_type toggle_type)
{
    int64_t key = 0;

    ZPN_DEBUG_APPLICATION("MULTI-MATCH: feature flag toggle type %d", toggle_type);

    if (zhash_table_get_size(customers_applications)) {
        zhash_table_walk(customers_applications, &key, zpn_app_multi_match_toggled_walk, &toggle_type);
    }
}

static void zpn_app_multi_match_toggled_all_cb(struct zevent_base *base, void *void_cookie, int64_t int_cookie)
{
    zpn_app_multi_match_toggled_all(int_cookie);
}

static void zpn_app_multi_match_feature_toggled_cb(const int64_t *config_value, int64_t customer_gid)
{
    ZPN_DEBUG_APPLICATION("MULTI-MATCH: feature flag toggled value=%"PRId64", customer=%"PRId64, (*config_value), customer_gid);

    if (!is_app_multi_match_feature_hard_disabled()) {
        if (customer_gid > 1) {
            ZPN_LOG(AL_NOTICE, "MULTI-MATCH, feature flag is toggled to %"PRId64", process this customer %"PRId64,
                                (*config_value), customer_gid);
            zevent_base_call(zevent_get_for_class(APP_MULTI_MATCH_TOGGLE_THREAD_POOL), zpn_app_multi_match_toggled_cb, NULL, customer_gid);
        } else {

            /*
             * if global value is 0, and a customer's feature flag is changed from 1 to deleted, this callback may give customer_gid as 0 or 1,
             * we do not know the customer's gid which is deleted. This is a deficiency in config override to be enhanced,
             * for now, just toggle all non-configured customers.
             */

            /* global change, only toggle not configured customers (who use the default value)  */
            /* ET-62154: the toggle of global value trigger the callbback multiple time, we only process it once for all, so need to check the change of value */
            static int64_t value_0 = -100;
            static int64_t value_1 = -100;
            int            value;

            if (customer_gid == 0) {
                value = (value_0 == -100) ? !(*config_value) : value_0;
                value_0 = *config_value;
            } else { /* customer = 1 */
                value = (value_1 == -100) ? !(*config_value) : value_1;
                value_1 = *config_value;
            }

            if (value != *config_value) {
                ZPN_LOG(AL_NOTICE, "MULTI-MATCH, global value of feature flag is toggled to %"PRId64", process non-config customers", (*config_value));
                zevent_base_call(zevent_get_for_class(APP_MULTI_MATCH_TOGGLE_THREAD_POOL), zpn_app_multi_match_toggled_all_cb, NULL, mmt_toggle_not_configured);
            }
        }
    } else {
        /* do nothing since it is hard disabled */
        ZPN_LOG(AL_NOTICE, "MULTI-MATCH is hard disabled");
    }
}

static void zpn_app_multi_match_hard_disable_toggled_cb(const int64_t *config_value, int64_t customer_gid)
{
    ZPN_DEBUG_APPLICATION("MULTI-MATCH: hard disable flag toggled value=%"PRId64, (*config_value));

    /* if hard disable flag is changed, we only need to toggle the enabled customers */
    /* ET-62154: the toggle of global value trigger the callbback multiple time, we only process it once for all, so need to check the change of value */
    static int64_t value = -100;
    if (value == -100) value = !(*config_value);
    if (value != *config_value) {
        ZPN_LOG(AL_NOTICE, "MULTI-MATCH: hard disable flag toggled value=%"PRId64", process enabled customers", (*config_value));
        zevent_base_call(zevent_get_for_class(APP_MULTI_MATCH_TOGGLE_THREAD_POOL), zpn_app_multi_match_toggled_all_cb, NULL, mmt_toggle_enabled);
    }
    value = *config_value;
}


/* Checks if an argo_inet contains data in the 'masked' part of the address */
int zpn_is_ip_address_valid(int64_t customer_gid, struct argo_inet *inet, const char *domain_name)
{
	if(4 == inet->length) {
		struct sockaddr_in target_addr;
		socklen_t target_addr_len;
		argo_inet_to_sockaddr(inet, ((struct sockaddr *)&target_addr), &target_addr_len, 0);
		uint32_t temp = htonl(target_addr.sin_addr.s_addr);
		uint32_t mask = INT_TO_IPV4_MASK(inet->netmask);
		mask = ~mask;
		if((temp & mask) != 0) {
 			return ZPN_RESULT_ERR;
		}
	}
	else {
    	struct in6_addr mask, dst;
    	memset(&mask, 0, sizeof(mask));
    	memset(&dst, 0, sizeof(dst));

    	create_ipv6_netmask(inet->netmask,&mask);
    	int i=0, flag = 0;
    	for(i = 0; i < 16; i++) {
    		mask.s6_addr[i] = ~mask.s6_addr[i];
    	}
    	for(i = 0; i < 16; i++) {
    		dst.s6_addr[i] = inet->address[i] & mask.s6_addr[i];
    	}
    	for(i = 0; i < 16; i++) {
    		if(dst.s6_addr[i] != 0) {
    			flag = 1;
    			break;
    		}
    	}
    	if(flag) {
			ZPN_LOG(AL_WARNING, "INVALID IPV6 [%s] for customer [%ld]", domain_name, (long)customer_gid);
			return ZPN_RESULT_ERR;
		}
	}
	return ZPN_RESULT_NO_ERROR;
}

/* get TLD+1 zones */
static char *get_tld_1_zones(const char *domain_name)
{
    char *p1;
    char *p2;
    int count = 0;
    int len = strlen(domain_name);

    p1 = (char *)&domain_name[0];
    p2 = (char *)&domain_name[len];

    /* for domain "*" or ".", we return "*" */
    if (strcmp(p1, "*") == 0 || strcmp(p1, ".") == 0) {
        return star;
    } else {
        while (p2 > p1 && count < 2) {
            if (*p2 == '.') count++;
            if (count < 2) p2--;
        }
        /* do not include the leading dot */
        return (*p2 == '.') ? p2+1 : p2;
    }
}

static int get_tld_1_walk(void *cookie,
                   void *data,
                   size_t len)
{
    char *tld_1 = (char *)data;
    struct tld_1_walk_data *walk_data = (struct tld_1_walk_data  *)cookie;

    if (tld_1 && walk_data && walk_data->idx < walk_data->max_sz) {
        if (walk_data->shallow) {
            walk_data->data[walk_data->idx++] = tld_1;
        } else {
            walk_data->data[walk_data->idx++] = ZPN_APP_STRDUP(tld_1, strlen(tld_1));
        }
    }

    return ZPATH_RESULT_NO_ERROR;
}

void zpn_free_tld_1(char **tld_1, size_t sz, int shallow)
{
    int i;
    if (tld_1) {
        for (i = 0; i < sz; i++) {
            if (tld_1[i]){
                if (!shallow) ZPN_APP_FREE(tld_1[i]);
                tld_1[i] = NULL;
            }
        }
        ZPN_APP_FREE(tld_1);
        tld_1 = NULL;
    }
}

/* The caller subject to free tld_1 using zpn_free_tld_1() */
/* must be locked with customer_tld_1->tld_state_lock in caller */
static int zpn_get_tld_1(struct customer_tld_1_state *customer_tld_1, char ***tld_1, int *sz, int shallow)
{
    struct tld_1_walk_data walk_data;

    *sz = zhash_table_get_size(customer_tld_1->domain_table);
    if (*sz == 0) {
        *tld_1 = NULL;
        return ZPATH_RESULT_NO_ERROR;
    }

    *tld_1 = ZPN_APP_CALLOC((*sz) * sizeof(**tld_1));
    walk_data.max_sz = *sz;
    walk_data.data = *tld_1;
    walk_data.idx = 0;
    walk_data.shallow = shallow;
    zhash_table_walk_data(customer_tld_1->domain_table, get_tld_1_walk, &walk_data);
    if (*sz != walk_data.idx) {
        ZPN_LOG(AL_WARNING, "customer %ld domain_table walk size mismatch, sz=%ld, idx=%ld",
                            (long)customer_tld_1->customer_gid, (long)(*sz), (long)walk_data.idx);
        *sz = walk_data.idx;
    }

    return ZPATH_RESULT_NO_ERROR;
}

/* The caller subject to free tld_1 using zpn_free_tld_1() with shallow=0*/
int zpn_get_tld_1_by_gid(int64_t customer_gid, char ***tld_1, int *sz)
{
    int res;
    struct customer_tld_1_state *customer_tld_1;

    /* We lock here, must unlock before return */
    customer_tld_1 = get_customer_tld_1_domain(customer_gid);
    if (!customer_tld_1) {
        *tld_1 = NULL;
        *sz = 0;
        return ZPATH_RESULT_ERR;
    }

    ZPATH_MUTEX_LOCK(&(customer_tld_1->tld_state_lock), __FILE__, __LINE__);
    res = zpn_get_tld_1(customer_tld_1, tld_1, sz, 0);
    ZPATH_MUTEX_UNLOCK(&(customer_tld_1->tld_state_lock), __FILE__, __LINE__);
    return res;
}

static void customer_tld_1_timer(int sock, short flags, void *cookie)
{
    struct customer_tld_1_state *customer_tld_1 = cookie;
    char ** tld_1 = NULL;
    int size = 0;
    int res;
    int64_t start_us = epoch_us();

    ZPATH_MUTEX_LOCK(&(customer_tld_1->tld_state_lock), __FILE__, __LINE__);

    if (customer_tld_1->timer) {
        event_free(customer_tld_1->timer);
        customer_tld_1->timer = NULL;
    }

    zpn_get_tld_1(customer_tld_1, &tld_1, &size, 1);

    /* Send TLD_1 domains to all clients of the customer */
    res = (customer_tld_1->send_to_all_cb)(customer_tld_1->customer_gid, tld_1, size, 0, start_us, zpn_client_type_total_count, zpn_platform_type_total_count);

    zpn_free_tld_1(tld_1, size, 1);

    if(res) {
        ZPN_LOG(AL_ERROR, "Customer %ld failed to send tld_1 to all clients", (long)customer_tld_1->customer_gid);
    }

    ZPATH_MUTEX_UNLOCK(&(customer_tld_1->tld_state_lock), __FILE__, __LINE__);
}

/*
 * Must have lock held when called
 */
static void tld_1_refresh_timer(struct customer_tld_1_state *customer_tld_1)
{
    struct timeval tv;

    if (!customer_tld_1->timer) {
        customer_tld_1->timer = event_new(zevent_event_base(customer_tld_1->tld_1_send_thread), -1, 0,
                                                            customer_tld_1_timer, customer_tld_1);
        ZPN_DEBUG_APPLICATION("Customer %ld: TLD_1 timer create", (long) customer_tld_1->customer_gid);
    } else {
        ZPN_DEBUG_APPLICATION("Customer %ld: TLD_1 timer push", (long) customer_tld_1->customer_gid);
    }

    /* This will schedule or reschedule the timer into the future */
    tv.tv_usec = DEFAULT_TLD_1_SEND_PUSH_US % 1000000;
    tv.tv_sec = DEFAULT_TLD_1_SEND_PUSH_US / 1000000;
    event_add(customer_tld_1->timer, &tv);
}


int zpn_customer_tld_1_update(int64_t customer_gid,
                              const char *domain_name,
                              enum zpn_tld_action action)
{
    int res;
    struct argo_inet inet;
    struct customer_tld_1_state *customer_tld_1 = NULL;
    struct zhash_table *domain_set = NULL;
    char *tld_1;
    int is_tld_1_changed = 0;

    res = argo_string_to_inet(domain_name, &inet);
    if ((res == ARGO_RESULT_NO_ERROR) && ((inet.length == 16) || (inet.length == 4))) {
        /* This is an inet not a hostname, do nothing */
        return ZPATH_RESULT_NO_ERROR;
    }

    tld_1 = get_tld_1_zones(domain_name);

    customer_tld_1 = get_customer_tld_1_domain(customer_gid);
    if (!customer_tld_1) return ZPATH_RESULT_ERR;

    ZPATH_MUTEX_LOCK(&(customer_tld_1->tld_state_lock), __FILE__, __LINE__);

    res = ZPATH_RESULT_NO_ERROR;
    /* we stored '\0' character for c_string */
    domain_set = zhash_table_lookup(customer_tld_1->domain_table, tld_1, strlen(tld_1)+1, NULL);
    if (action == zpn_tld_remove){
        if (domain_set) {
            zhash_table_remove(domain_set, domain_name, strlen(domain_name), NULL);
            if (zhash_table_get_size(domain_set) == 0) {
                zhash_table_remove(customer_tld_1->domain_table, tld_1, strlen(tld_1)+1, NULL);
                zhash_table_free(domain_set);
                is_tld_1_changed = 1;
            }
        }
    } else if (action == zpn_tld_add) {
        int is_domain_in_set = 1;
        if (domain_set) {
            if (NULL == zhash_table_lookup(domain_set, domain_name, strlen(domain_name), NULL)) {
                is_domain_in_set = 0;
            }
        } else {
            domain_set = zhash_table_alloc(&zpn_app_allocator);
            if (!domain_set) {
                ZPN_LOG(AL_CRITICAL, "customer %ld failed to alloc domain_set", (long)customer_gid);
                ZPATH_MUTEX_UNLOCK(&(customer_tld_1->tld_state_lock), __FILE__, __LINE__);
                return ZPATH_RESULT_ERR;
            }
            /* include the '\0' for c_string */
            if(zhash_table_store(customer_tld_1->domain_table, tld_1, strlen(tld_1)+1, 0, domain_set) != 0) {
                ZPN_LOG(AL_CRITICAL, "customer %ld failed to store domain_set", (long)customer_gid);
                zhash_table_free(domain_set);
                ZPATH_MUTEX_UNLOCK(&(customer_tld_1->tld_state_lock), __FILE__, __LINE__);
                return ZPATH_RESULT_ERR;
            }
            is_domain_in_set = 0;
            is_tld_1_changed = 1;
        }
        if (!is_domain_in_set) {
            res = zhash_table_store(domain_set, (void *)domain_name, strlen(domain_name), 0, domain_set);
        }
    } else {
        ZPN_LOG(AL_CRITICAL, "customer %ld invalid zpn_tld_action", (long)customer_gid);
        res = ZPATH_RESULT_ERR;
    }

    if (is_tld_1_changed) {
        if (customer_tld_1->send_to_all_cb) {
            tld_1_refresh_timer(customer_tld_1);
        } else {
            ZPN_DEBUG_APPLICATION("send_to_all_cb is not registered, customer %ld may not be ready", (long)customer_tld_1->customer_gid);
        }
    }

    ZPATH_MUTEX_UNLOCK(&(customer_tld_1->tld_state_lock), __FILE__, __LINE__);
    return res;
}

int zpn_customer_application_remove(int64_t customer_gid,
                                    const char *domain_name,
                                    size_t domain_name_len)
{
    int res;
    int is_wildcard = 0;
    struct zpn_customer_application_state *state;
    struct argo_inet inet;
    char *alloc_domain = NULL;
    int is_inet = 0;

    if (domain_name_len && domain_name[0] == '.') {
        is_wildcard = 1;
        domain_name++;
        domain_name_len--;
    } else {
        res = argo_string_to_inet(domain_name, &inet);
        if ((res == ARGO_RESULT_NO_ERROR) && ((inet.length == 16) || (inet.length == 4))) is_inet = 1;
    }

    state = state_get_and_lock(customer_gid, 1);
    ZPATH_RWLOCK_UNLOCK(&(local_app_lock), __FILE__, __LINE__);
    if (!state) {
        return ZPATH_RESULT_ERR;
    }

    ZPATH_RWLOCK_WRLOCK(&(state->customer_app_lock), __FILE__, __LINE__);
    if (is_inet) {
        if (inet.length == 4) {
            res = zradix_lookup(state->ipv4_lookup, inet.address, inet.netmask, (void **)&alloc_domain);
        } else {
            res = zradix_lookup(state->ipv6_lookup, inet.address, inet.netmask, (void **)&alloc_domain);
        }
        if (res == ZRADIX_RESULT_NO_ERROR) {
            if (inet.length == 4) {
            	res = zpn_is_ip_address_valid(customer_gid, &inet, domain_name);
				if(res) {
					ZPN_LOG(AL_WARNING, "INVALID IP ADDR [%s] of APP for Customer [%"PRId64"]...cannot remove...", domain_name, customer_gid);
					ZPATH_RWLOCK_UNLOCK(&(state->customer_app_lock), __FILE__, __LINE__);
					return ZPATH_RESULT_NO_ERROR;
				}
                res = zradix_remove(state->ipv4_lookup, inet.address, inet.netmask, NULL);
            } else {
            	res = zpn_is_ip_address_valid(customer_gid, &inet, domain_name);
				if(res) {
					ZPN_LOG(AL_WARNING, "INVALID IP ADDR [%s] of APP for Customer [%"PRId64"]...cannot remove...", domain_name, customer_gid);
					ZPATH_RWLOCK_UNLOCK(&(state->customer_app_lock), __FILE__, __LINE__);
					return ZPATH_RESULT_NO_ERROR;
				}
                res = zradix_remove(state->ipv6_lookup, inet.address, inet.netmask, NULL);
            }
            if (res != ZRADIX_RESULT_NO_ERROR) {
                /* Not found on remove */
                ZPATH_RWLOCK_UNLOCK(&(state->customer_app_lock), __FILE__, __LINE__);
                ZPN_LOG(AL_WARNING, "Customer %"PRId64", idempotent remove application: failed %s (IP)", customer_gid, domain_name);
                zpn_application_search_debug_stats_counter(zpn_app_search_none, ((inet.length == 4) ? zpn_ipv4_remove_failed : zpn_ipv6_remove_failed));
            } else {
                ZPATH_RWLOCK_UNLOCK(&(state->customer_app_lock), __FILE__, __LINE__);
                ZPN_APP_FREE(alloc_domain);
                ZPN_DEBUG_APPLICATION("Customer %"PRId64", removed application %s (IP)", customer_gid, domain_name);
            }
        } else {
            /* Not found */
            ZPATH_RWLOCK_UNLOCK(&(state->customer_app_lock), __FILE__, __LINE__);
            ZPN_DEBUG_APPLICATION("Customer %"PRId64", idempotent remove application: not there %s (IP)", customer_gid, domain_name);
        }
    } else {
        const char *suffix_str = domain_name;
        int         suffix_len = domain_name_len;
        int         wildcard_prefix = 0;
        int         is_pattern_match = is_pattern_match_str(domain_name, domain_name_len, &wildcard_prefix, &suffix_str, &suffix_len);

        if (is_pattern_match && !is_pattern_match_domain_update_enabled(state)) {
            ZPN_LOG(AL_CRITICAL, "Remove: <%s>, pattern not supported as the feature isn't enabled for customer_gid: %"PRId64" Feature Status(PATTERN)= %d",
                                                            domain_name, customer_gid,
                                                            (int)zpn_application_app_pattern_match_config_value_get(state));
            ZPATH_RWLOCK_UNLOCK(&(state->customer_app_lock), __FILE__, __LINE__);
            return ZPATH_RESULT_NO_ERROR;
        }

        if (suffix_len == -1) {
            ZPN_LOG(AL_ERROR, "Customer: %"PRId64", Error removing <%.*s>, pattern not supported in TLD or string ending without a suffix str", customer_gid, (int)domain_name_len, domain_name);
            ZPATH_RWLOCK_UNLOCK(&(state->customer_app_lock), __FILE__, __LINE__);
            zpn_application_search_debug_stats_counter(zpn_app_search_none, zpn_app_remove_pattern_not_supported);
            return ZPATH_RESULT_ERR;
        }

        if (!is_wildcard) is_wildcard = wildcard_prefix;
        ZPN_DEBUG_APPLICATION("Removing <%.*s>, is_wildcard: %d, is_pattern_match: %d, suffix_str: %.*s", (int)domain_name_len, domain_name, is_wildcard, is_pattern_match, suffix_len, suffix_str);
        if ((alloc_domain = diamond_lookup_with_pattern(state->domain_lookup,
                                                        (is_pattern_match ? (const uint8_t *)suffix_str : (const uint8_t*)domain_name),
                                                        (is_pattern_match ? suffix_len : domain_name_len),
                                                        is_wildcard,
                                                        0,
                                                        0,
                                                        0,
                                                        is_pattern_match, //is_pattern_match
                                                        (is_pattern_match ? (void *)domain_name: NULL)))) {
            if ((res = diamond_remove_with_pattern(state->domain_lookup,
                                            (is_pattern_match ? (const uint8_t *)suffix_str : (const uint8_t*)domain_name),
                                            (is_pattern_match ? suffix_len : domain_name_len),
                                            is_wildcard,
                                            0,
                                            0,
                                            0,
                                            is_pattern_match, //is_pattern_match
                                            (is_pattern_match ? (void *)alloc_domain: NULL)))) {
                ZPATH_RWLOCK_UNLOCK(&(state->customer_app_lock), __FILE__, __LINE__);
                ZPN_LOG(AL_WARNING, "Customer %"PRId64", failed remove application %s%s, res: %d", customer_gid, is_wildcard ? "*.":"", domain_name, res);
                zpn_application_search_debug_stats_counter(zpn_app_search_none, zpn_diamond_remove_failed);
                // Doing below to retain argo errors
                if (res > PATTERN_MATCH_ERR_CODE_OFFSET) {
                    res -= PATTERN_MATCH_ERR_CODE_OFFSET;
                }
            } else {
                ZPATH_RWLOCK_UNLOCK(&(state->customer_app_lock), __FILE__, __LINE__);
                ZPN_APP_FREE(alloc_domain);
                ZPN_DEBUG_APPLICATION("Customer %"PRId64", removed application %s%s", customer_gid, is_wildcard ? "*.":"", domain_name);
            }
        } else {
            ZPATH_RWLOCK_UNLOCK(&(state->customer_app_lock), __FILE__, __LINE__);
            ZPN_DEBUG_APPLICATION("Customer %"PRId64", idempotent remove application: not there %s%s", customer_gid, is_wildcard ? "*.":"", domain_name);
        }
    }

    return ZPATH_RESULT_NO_ERROR;
}

static int is_pattern_match_domain_update_enabled(struct zpn_customer_application_state *state)
{
    int is_pattern_domain_enabled = 1;

    if (is_app_pattern_match_feature_hard_disabled()) {
        // Application pattern match feature is hard disabled Globally.
        return 0;
    }

    if (state && zpn_application_app_pattern_match_config_value_get(state) == APPLICATION_PATTERN_MATCH_FEATURE_DISABLE_FULLY) {
        // Application pattern match feature is fully disabled. Return 0 to not add domains with pattern into diamond.
        is_pattern_domain_enabled = 0;
    }
    return is_pattern_domain_enabled;
}

static int is_pattern_match_feature_enabled(struct zpn_customer_application_state *state)
{
    int feature_status = 0;
    if (state && state->app_scaling_feature_enabled &&
        zpn_application_app_multi_match_feature_enabled_get(state) &&
        is_zpn_application_app_pattern_match_feature_enabled(state)) {
        feature_status = 1;
    }
    return feature_status;
}

int zpn_customer_application_add_or_replace(int64_t customer_gid,
                                            const char *domain_name,
                                            size_t domain_name_len)
{
    struct zpn_customer_application_state *state;
    int res;
    int is_wildcard = 0;
    struct argo_inet inet;
    int is_inet = 0;
    char *alloc_domain = NULL;
    const char *orig_domain = domain_name;
    size_t orig_domain_len = domain_name_len;

    if (domain_name_len && domain_name[0] == '.') {
        is_wildcard = 1;
        domain_name++;
        domain_name_len--;
    } else {
        res = argo_string_to_inet(domain_name, &inet);
        if ((res == ARGO_RESULT_NO_ERROR) && ((inet.length == 16) || (inet.length == 4))) is_inet = 1;
    }

    state = state_get_and_lock(customer_gid, 1);
    ZPATH_RWLOCK_UNLOCK(&(local_app_lock), __FILE__, __LINE__);
    if (!state) {
        return ZPATH_RESULT_ERR;
    }

    ZPATH_RWLOCK_WRLOCK(&(state->customer_app_lock), __FILE__, __LINE__);
    if (is_inet) {
        /* First remove it if it's there... */
        if (inet.length == 4) {
            res = zradix_lookup(state->ipv4_lookup, inet.address, inet.netmask, (void **)&alloc_domain);
        } else {
            res = zradix_lookup(state->ipv6_lookup, inet.address, inet.netmask, (void **)&alloc_domain);
        }
        if (res == ZRADIX_RESULT_NO_ERROR) {
            /* Already there. Done */
            ZPATH_RWLOCK_UNLOCK(&(state->customer_app_lock), __FILE__, __LINE__);
            return ZPATH_RESULT_NO_ERROR;
        }

        alloc_domain = ZPN_APP_STRDUP(orig_domain, orig_domain_len);
        if (!alloc_domain) {
            ZPATH_RWLOCK_UNLOCK(&(state->customer_app_lock), __FILE__, __LINE__);
            return ZPATH_RESULT_NO_MEMORY;
        }

        if (inet.length == 4) {
        	res = zpn_is_ip_address_valid(customer_gid, &inet, domain_name);
			if(res) {
				ZPN_LOG(AL_WARNING, "INVALID IP ADDR [%s] of APP for Customer [%"PRId64"]...cannot add...", domain_name, customer_gid);
				ZPATH_RWLOCK_UNLOCK(&(state->customer_app_lock), __FILE__, __LINE__);
                ZPN_APP_FREE(alloc_domain);
				return ZPATH_RESULT_NO_ERROR;
			}
            res = zradix_add(state->ipv4_lookup, inet.address, inet.netmask, alloc_domain);
        } else {
        	res = zpn_is_ip_address_valid(customer_gid, &inet, domain_name);
        	if(res) {
				ZPN_LOG(AL_WARNING, "INVALID IP ADDR [%s] of APP for Customer [%"PRId64"]...cannot add...", domain_name, customer_gid);
				ZPATH_RWLOCK_UNLOCK(&(state->customer_app_lock), __FILE__, __LINE__);
                ZPN_APP_FREE(alloc_domain);
				return ZPATH_RESULT_NO_ERROR;
			}
            res = zradix_add(state->ipv6_lookup, inet.address, inet.netmask, alloc_domain);
        }
        ZPATH_RWLOCK_UNLOCK(&(state->customer_app_lock), __FILE__, __LINE__);

        if (res == ZRADIX_RESULT_NO_ERROR) {
            ZPN_DEBUG_APPLICATION("Customer %"PRId64", Add application %s (IP)", customer_gid, domain_name);
        } else {
            ZPN_LOG(AL_WARNING, "Customer: %"PRId64", Cannot add %s:%d (IP)", customer_gid, domain_name, res);
            ZPN_APP_FREE(alloc_domain);
            zpn_application_search_debug_stats_counter(zpn_app_search_none, ((inet.length == 4) ? zpn_ipv4_add_failed : zpn_ipv6_add_failed));
            return ZPATH_RESULT_ERR;
        }
    } else {
        const char *suffix_str = domain_name;
        int         suffix_len = domain_name_len;
        int         wildcard_prefix = 0;
        int         is_pattern_match = is_pattern_match_str(domain_name, domain_name_len, &wildcard_prefix, &suffix_str, &suffix_len);

        if (is_pattern_match && !is_pattern_match_domain_update_enabled(state)) {
            ZPN_LOG(AL_CRITICAL, "Add: <%s>, pattern not supported as the feature isn't enabled for customer_gid: %"PRId64" Feature Status(PATTERN)= %d ",
                                                            domain_name, customer_gid,
                                                            (int)zpn_application_app_pattern_match_config_value_get(state));
            ZPATH_RWLOCK_UNLOCK(&(state->customer_app_lock), __FILE__, __LINE__);
            return ZPATH_RESULT_NO_ERROR;
        }

        if (suffix_len == -1) {
            ZPN_LOG(AL_ERROR, "Customer: %"PRId64", Error adding <%.*s>, pattern not supported in TLD or string ending without a suffix str", customer_gid, (int)domain_name_len, domain_name);
            ZPATH_RWLOCK_UNLOCK(&(state->customer_app_lock), __FILE__, __LINE__);
            zpn_application_search_debug_stats_counter(zpn_app_search_none, zpn_app_add_pattern_not_supported);
            return ZPATH_RESULT_ERR;
        }

        if (!is_wildcard) is_wildcard = wildcard_prefix;
        ZPN_DEBUG_APPLICATION("Adding <%.*s>, is_pattern_match: %d, is_wildcard: %d, suffix_str: %.*s", (int)domain_name_len, domain_name, is_pattern_match, is_wildcard, suffix_len, suffix_str);

        if (diamond_lookup_with_pattern(state->domain_lookup,
                                        (is_pattern_match ? (const uint8_t*)suffix_str : (const uint8_t *)domain_name),
                                        (is_pattern_match ? suffix_len : domain_name_len),
                                        is_wildcard,
                                        0,
                                        0,
                                        0,
                                        is_pattern_match, //is_pattern_match
                                        (is_pattern_match ? (void*)domain_name : NULL))) {
            /* Already there. done. */
            ZPATH_RWLOCK_UNLOCK(&(state->customer_app_lock), __FILE__, __LINE__);
            ZPN_DEBUG_APPLICATION("Customer %"PRId64", idempotent add/update application: not there %s%s", customer_gid, is_wildcard ? "*.":"", domain_name);
            return ZPATH_RESULT_NO_ERROR;
        }

        alloc_domain = ZPN_APP_STRDUP(orig_domain, orig_domain_len);
        if (!alloc_domain) {
            ZPATH_RWLOCK_UNLOCK(&(state->customer_app_lock), __FILE__, __LINE__);
            return ZPATH_RESULT_NO_MEMORY;
        }
        if ((res = diamond_add_with_pattern(state->domain_lookup,
                                            (is_pattern_match ? (const uint8_t*)suffix_str : (const uint8_t *)domain_name),
                                            (is_pattern_match ? suffix_len : domain_name_len),
                                            is_wildcard,
                                            0,
                                            0,
                                            0,
                                            1,
                                            is_pattern_match, // is_pattern_match
                                            alloc_domain))) {
            ZPATH_RWLOCK_UNLOCK(&(state->customer_app_lock), __FILE__, __LINE__);
            ZPN_LOG(AL_CRITICAL, "Customer: %"PRId64", Cannot add/update %s%s, res: %d", customer_gid, is_wildcard ? "*.":"", domain_name, res);
            zpn_application_search_debug_stats_counter(zpn_app_search_none, zpn_diamond_add_failed);
            // Doing below to retain argo errors
            if (res > PATTERN_MATCH_ERR_CODE_OFFSET) {
                res -= PATTERN_MATCH_ERR_CODE_OFFSET;
            }
            ZPN_APP_FREE(alloc_domain);
            return res;
        } else {
            ZPATH_RWLOCK_UNLOCK(&(state->customer_app_lock), __FILE__, __LINE__);
            ZPN_DEBUG_APPLICATION("Customer %"PRId64", added application %s%s", customer_gid, is_wildcard ? "*.":"", domain_name);
        }
    }

    return res;
}


static int string_compare(const void *a, const void *b)
{
    const char **aa = (const char **)a;
    const char **bb = (const char **)b;
    return strcmp(*aa, *bb);
}

static int port_range_compare(const void *a, const void *b)
{
    const int *aa = (const int *)a;
    const int *bb = (const int *)b;
    return (*aa) - (*bb);
}

/* Sort port and protocol arrays for TCP and UDP ports
 * Based on TCP or UDP port numbers */
struct zpn_port_map {
    int port;
    int protocol;
};

static int port_map_compare(const void *a, const void *b)
{
	const struct zpn_port_map *aa = (const struct zpn_port_map *)a;
	const struct zpn_port_map *bb = (const struct zpn_port_map *)b;
	return (aa->port - bb->port);
}

static int zpn_application_qsort_port_map_list(struct zpn_application *app)
{
    if (!app)
        return 0;

    int i = 0;
    int j = 0;

    /* Sort TCP and UDP ports and protocols_bitmasks */
    if (app->tcp_port_ranges_count) {
        if (app->tcp_protocols_bitmasks_count < (app->tcp_port_ranges_count / 2)) {
            ZPN_LOG(AL_ERROR,"Error : TCP port count %d is greater than protocol count %d for customer_gid: %"PRId64", "
                "name:%s, domain_name:%s", app->tcp_port_ranges_count/2, app->tcp_protocols_bitmasks_count,
                app->customer_gid, app->name, app->domain_name);
        } else {
            struct zpn_port_map *tcp_list =
                (struct zpn_port_map *)ZPN_APP_MALLOC(sizeof(struct zpn_port_map) * app->tcp_port_ranges_count);
            for(i = 0, j = 0; i < app->tcp_port_ranges_count && j < app->tcp_protocols_bitmasks_count; i+=2,j+=1) {
                tcp_list[i].port = app->tcp_port_ranges[i];
                tcp_list[i].protocol = app->tcp_protocols_bitmasks[j];
                tcp_list[i+1].port = app->tcp_port_ranges[i+1];
            }
            /* Sort the struct port protocol tcp list */
            qsort(tcp_list, (app->tcp_port_ranges_count)/2, 2 * sizeof(tcp_list[0]), port_map_compare);
            for(i = 0, j = 0; i < app->tcp_port_ranges_count && j < app->tcp_protocols_bitmasks_count; i+=2, j+=1) {
                app->tcp_port_ranges[i] = tcp_list[i].port;
                app->tcp_protocols_bitmasks[j] = tcp_list[i].protocol;
                app->tcp_port_ranges[i+1] = tcp_list[i+1].port;
            }
            ZPN_APP_FREE(tcp_list);
        }
    }

    if (app->udp_port_ranges_count) {
        if (app->udp_protocols_bitmasks_count < (app->udp_port_ranges_count / 2)) {
            ZPN_LOG(AL_ERROR,"Error : UDP port count %d is greater than protocol count %d for customer_gid: %"PRId64", "
                "name:%s, domain_name:%s", app->udp_port_ranges_count/2, app->udp_protocols_bitmasks_count,
                app->customer_gid, app->name, app->domain_name);
        } else {
            struct zpn_port_map *udp_list =
                (struct zpn_port_map *)ZPN_APP_MALLOC(sizeof(struct zpn_port_map) * app->udp_port_ranges_count);
            for(i = 0,j = 0; i < app->udp_port_ranges_count && j < app->udp_protocols_bitmasks_count; i+=2, j+=1) {
                udp_list[i].port = app->udp_port_ranges[i];
                udp_list[i].protocol = app->udp_protocols_bitmasks[j];
                udp_list[i+1].port = app->udp_port_ranges[i+1];
            }
            /* Sort the struct port protocol udp list */
            qsort(udp_list, (app->udp_port_ranges_count)/2, 2 * sizeof(udp_list[0]), port_map_compare);
            for(i = 0, j = 0; i < app->udp_port_ranges_count && j < app->udp_protocols_bitmasks_count; i+=2, j+=1) {
                app->udp_port_ranges[i] = udp_list[i].port;
                app->udp_protocols_bitmasks[j] = udp_list[i].protocol;
                app->udp_port_ranges[i+1] = udp_list[i+1].port;
            }
            ZPN_APP_FREE(udp_list);
        }
    }

    return 0;
}

/* Common row fixup code for zpn_applications.
 *
 * This routine:
 *
 * 1. dumps the new row (if enabled)
 * 2. does string -> enum for policy style
 */
void zpn_application_row_fixup(struct argo_object *row)
{
    struct zpn_application *app = row->base_structure_void;
    int i;
    int count = 0;
    struct argo_inet inet;

    if(app->scope_gid == 0) app->scope_gid = app->customer_gid;

    if (zpn_debug_get(ZPN_DEBUG_APPLICATION_IDX)) {
        char *text_dump = NULL;
        if (argo_object_allocate_and_dump(row, &text_dump, NULL, 1) == ARGO_RESULT_NO_ERROR) {
            ZPN_DEBUG_APPLICATION("Row callback: %s", text_dump);
            argo_object_deallocate_dump(text_dump);
        }
    }

    app->m_style = zpath_match_style_string_to_enum(app->match_style);

    /* Protect ourselves if too many ports are configured-
     * passive health only unless 10 or fewer ports are
     * configured */
    if (app->passive_health_enabled == 0) {
        if ((app->udp_port_ranges_count & 1) ||
            (app->tcp_port_ranges_count & 1)) {
            char *text_dump = NULL;
            if (argo_object_allocate_and_dump(row, &text_dump, NULL, 1) == ARGO_RESULT_NO_ERROR) {
                ZPN_LOG(AL_ERROR, "Invalid port counts: %s", text_dump);
                argo_object_deallocate_dump(text_dump);
            }
        }
        for (i = 0; (i + 1) < app->udp_port_ranges_count; i += 2) {
            count += 1 + app->udp_port_ranges[i + 1] - app->udp_port_ranges[i];
        }
        for (i = 0; (i + 1) < app->tcp_port_ranges_count; i += 2) {
            count += 1 + app->tcp_port_ranges[i + 1] - app->tcp_port_ranges[i];
        }
        if (count > ZPN_APPLICATION_MAX_PORTS) {
            ZPN_LOG(AL_WARNING, "Application %ld forced to run passive health because port count(%d) exceeded %d",
                    (long)app->gid, count, ZPN_APPLICATION_MAX_PORTS);
            app->passive_health_enabled = 1;
        }
    }

    if (app->domain_name) {
        zpath_downcase(app->domain_name);
        if ((argo_string_to_inet(app->domain_name, &inet) == ZPN_RESULT_NO_ERROR) && (inet.netmask > 0) && (inet.netmask < 32)) {
            ZPN_DEBUG_APPLICATION("Domain name %s is subnet", app->domain_name);
            app->domains_contains_subnet = 1;
        }
        if (app->domain_name[0] == '.') {
            ZPN_DEBUG_APPLICATION("Domain name %s is wildcard", app->domain_name);
            app->domains_contains_wildcard = 1;
        }
    }

    if (app->domain_names_count) {
        int i;
        char *s, *e;
        s = app->domain_name_debug_str;
        e = s + sizeof(app->domain_name_debug_str);
        for (i = 0; i < app->domain_names_count; i++) {
            zpath_downcase(app->domain_names[i]);
            if (i) {
                s += sxprintf(s, e, ",%s", app->domain_names[i]);
            } else {
                s += sxprintf(s, e, "%s", app->domain_names[i]);
            }
            if ((argo_string_to_inet(app->domain_names[i], &inet) == ZPN_RESULT_NO_ERROR) && (inet.netmask > 0) && (inet.netmask < 32)) {
                ZPN_DEBUG_APPLICATION("Domain name %s is subnet", app->domain_names[i]);
                app->domains_contains_subnet = 1;
            }
            if (app->domain_names[i][0] == '.') {
                ZPN_DEBUG_APPLICATION("Domain name %s is wildcard", app->domain_names[i]);
                app->domains_contains_wildcard = 1;
            }
        }
        /* Quicksort the domains so that we can easily compare rows,
         * etc */
        qsort(app->domain_names, app->domain_names_count, sizeof(app->domain_names[0]), string_compare);
    }

    /* Qsort TCP and UDP port ranges and protocol_bitmask */
    if (app->adp_enabled) {
        zpn_application_qsort_port_map_list(app);
    }
    else {
        if (app->tcp_port_ranges_count) qsort(app->tcp_port_ranges, app->tcp_port_ranges_count / 2, 2 * sizeof(app->tcp_port_ranges[0]), port_range_compare);
        if (app->udp_port_ranges_count) qsort(app->udp_port_ranges, app->udp_port_ranges_count / 2, 2 * sizeof(app->udp_port_ranges[0]), port_range_compare);
    }

    if (app->cname_config) {
        if (strcasecmp(app->cname_config, "noflatten") == 0) {
            app->cname_flatten = 0;
        } else if (strcasecmp(app->cname_config, "flatten") == 0) {
            app->cname_flatten = 1;
        } else {
            app->cname_flatten = 0;
        }
    } else {
        app->cname_flatten = 0;
    }

    if (app->inspect_traffic_with_zia) {
#if 0
        // ZIA inspection 1.0
        app->ip_anchored = 1;
#else
        // ZIA inspection 3.0
        // ip_anchored might of been writen by  the broker in 1.1, so set ip_anchor to 0
        app->ip_anchored = 0;
        ZPN_DEBUG_ZINS_DETAILS("app:%" PRId64 " inspect_traffic_with_zia, update ip_anchored to 0", app->gid);
#endif
    }
}

static void
zpn_application_debug_log_defer_callback(void *cookie1, void *cookie2) {
    g_app_debug_log = 1;
}

static int zpn_application_row_callback_broker_and_exporter(void *cookie,
                                                            struct wally_registrant *registrant,
                                                            struct wally_table *table,
                                                            struct argo_object *previous_row,
                                                            struct argo_object *row,
                                                            int64_t request_id)
{
    struct zpn_application *app = row->base_structure_void;
    int                     i;
    int                     res;
    static int              g_app_debug_enabled = 0;

    if (zpath_service_init_complete) {
        __sync_add_and_fetch(&(policy_config_change_stats.zpn_application), 1);
    }

    if (!g_app_debug_enabled) {
        /* defer enabling debug log in zpn_application_row_callback, app_gid_update
         * and app_domain_update for 30min after broker restart.
         */
        res = zevent_defer(zpn_application_debug_log_defer_callback, NULL, NULL, (30*60*1000*1000));
        if (res != ZPATH_RESULT_NO_ERROR) {
            ZPN_LOG(AL_ERROR, "Could not defer zpn_application_debug_log_defer_callback");
        }
        g_app_debug_enabled = 1;
    }
    // Start printing this log after 30 min from broker uptime
    if (g_app_debug_log) {
        ZPN_LOG(AL_DEBUG, "customer_gid: %"PRId64", scope_gid: %"PRId64", app_gid: %"PRId64", domain_names_count: %d",
                app->customer_gid, app->scope_gid, app->gid, app->domain_names_count);
    }

#ifdef DTA_CHECKPOINT
    if(app->scope_gid && app->scope_gid != app->customer_gid) return WALLY_RESULT_NO_ERROR;
#endif

    /* Broker needs to be informed when application domains change.... */
    if (app->domain_names_count) {
        for (i = 0; i < app->domain_names_count; i++) {
            zpn_application_domain_changed_defer_call(app->customer_gid, app->domain_names[i]);
        }
    } else {
        if (app->domain_name) {
            zpn_application_domain_changed_defer_call(app->customer_gid, app->domain_name);
        }
    }

    zpn_application_version_update(app->customer_gid);

    return WALLY_RESULT_NO_ERROR;
}

static int zpn_application_row_callback_assistant(void *cookie,
                                                  struct wally_registrant *registrant,
                                                  struct wally_table *table,
                                                  struct argo_object *previous_row,
                                                  struct argo_object *row,
                                                  int64_t request_id)
{
    return WALLY_RESULT_NO_ERROR;
}

static inline int
is_app_valid_to_scope(int64_t scope_gid, const struct zpn_application *app) {
    int k;

     /* check if the app is in this scope or in default scope */
    if ((app->scope_gid == scope_gid) || (app->customer_gid == app->scope_gid)) {
        return 1;
    }

    /* check if the app is shared to this scope */
    for (k = 0; k < app->shared_scope_ids_count; k++)
    {
        if (scope_gid == app->shared_scope_ids[k]) {
            return 1;
        }
    }
    for (k = 0; k < app->shared_scope_ids_from_app_group_count; k++)
    {
        if (scope_gid == app->shared_scope_ids_from_app_group[k]) {
            return 1;
        }
    }

    return 0;
}

#ifndef MOCK_ZPN_APPLICATION_SEARCH

/*
 * The domain is valid to the scope only if its app is in the scope,
 * or in the default scope, or is shared to the scope
 */
static int is_domain_valid_to_scope(int64_t scope_gid, const char* domain, int skip_non_sipa_apps, int filter_by_scope)
{
    struct zpn_application_domain *app_domains[10000];
    size_t app_domains_count = 10000;
    int res;
    int64_t customer_gid = ZPATH_GID_GET_CUSTOMER_GID(scope_gid);


    /*
     * When filter_by_scope and skip_non_sipa_apps
     * both are not set no need to look for further
     * domain return the first domain
     */
    if (!filter_by_scope && !skip_non_sipa_apps) {
        return 1;
    }

    res = zpn_application_domain_get_by_domain_immediate(customer_gid, domain, &(app_domains[0]), &app_domains_count);
    if (!res) {
        int j;
        struct zpn_application *app;
        for (j = 0; j < app_domains_count; j++) {
            /* coverity[callee_ptr_arith : FALSE] */
            res = zpn_application_get_by_id_immediate(app_domains[j]->application_gid, &app);
            if(res || !app) { // Check for NULL app is to deal with Coverity
                ZPN_LOG(AL_ERROR, "Failed to fetch application by id %"PRId64" for customer %"PRId64" and domain %s\n",
                                    app_domains[j]->application_gid, customer_gid, domain);
                continue;
            }

            /*
             * Skip non sipa apps
             */
            if (skip_non_sipa_apps && app->ip_anchored != 1) {
                ZPN_LOG(AL_INFO,"Customer %"PRId64" app %"PRId64", name %s, domain %s is not sipa. skipping it",
                                app->customer_gid, app->gid, app->name, domain);
                continue;
            }

            if (!filter_by_scope && skip_non_sipa_apps && app->ip_anchored == 1) {
                return 1;
            }

            if (is_app_valid_to_scope(scope_gid, app)) return 1;
        }
    }

    return 0;
}


static int zpn_application_search(int64_t scope_gid,
                                  int filter_by_scope,
                                  const char *domain_name,
                                  size_t domain_name_len,
                                  char *matched_domain,
                                  size_t matched_domain_len,
                                  int *is_wildcard,
                                  int skip_non_sipa_apps)
{
    struct zpn_customer_application_state *state;
    int res;
    void *results[RESULTS_ARRAY_SIZE];
    int results_wildcard[RESULTS_ARRAY_SIZE];
    size_t count;
    char *res_string = NULL;
    struct argo_inet inet;
    int is_inet = 0;
    int64_t customer_gid = ZPATH_GID_GET_CUSTOMER_GID(scope_gid);
    int64_t start_us = 0;
    int64_t delta_us = 0;
    int64_t pattern_match_counts = 0;

    memset(results, 0, sizeof(void *) * RESULTS_ARRAY_SIZE);
    memset(results_wildcard, 0, sizeof(int) * RESULTS_ARRAY_SIZE);

    /* Convert to INET */
    res = argo_string_to_inet(domain_name, &inet);
    if ((res == ARGO_RESULT_NO_ERROR) && ((inet.length == 16) || (inet.length == 4))) is_inet = 1;
    res = ZPN_RESULT_NOT_FOUND;

    state = state_get_and_lock(customer_gid, 0);
    ZPATH_RWLOCK_UNLOCK(&(local_app_lock), __FILE__, __LINE__);
    if (!state) {
        return ZPATH_RESULT_ERR;
    }

    if (zpn_debug_get(ZPN_DEBUG_APPLICATION_PERF_IDX)) start_us = epoch_us();

    ZPATH_RWLOCK_RDLOCK(&(state->customer_app_lock), __FILE__, __LINE__);
    if (is_inet) {
        int cnt = 1;
        if (inet.length == 4) {
            res = zradix_search(state->ipv4_lookup, inet.address, inet.netmask,
                                NULL, NULL, (void **) &results[0], &cnt);
        } else {
            res = zradix_search(state->ipv6_lookup, inet.address, inet.netmask,
                                NULL, NULL, (void **) &results[0], &cnt);
        }

        if(res == ZRADIX_RESULT_NO_ERROR) {
            results_wildcard[0] = 0;
            count = cnt;
        } else {
            count = 0;
            if (res == ZRADIX_RESULT_NOT_FOUND) {
                ZPN_LOG(AL_INFO, "Could not find IP : %s for scope: %" PRId64 "", domain_name, scope_gid);
            } else {
                ZPN_LOG(AL_ERROR, "Could not find IP : %s for scope: %" PRId64 ", res:%s", domain_name, scope_gid, zpath_result_string(res));
            }
        }
    } else {
        count = diamond_search_with_pattern(state->domain_lookup,
                                            (const uint8_t *)domain_name,
                                            domain_name_len,
                                            &(results[0]),
                                            &(results_wildcard[0]),
                                            RESULTS_ARRAY_SIZE,
                                            &pattern_match_counts,
                                            is_pattern_match_feature_enabled(state));
    }
    ZPATH_RWLOCK_UNLOCK(&(state->customer_app_lock), __FILE__, __LINE__);

    if (count) {
        int i;
        /*
         * if filter_by_scope is set then only return the domain, if it's app is in its only scope,
         * or in default scope, or is shared to its scope
         */
        for (i = 0; i < count; i++) {
            if (is_domain_valid_to_scope(scope_gid, results[i], skip_non_sipa_apps, filter_by_scope)) break;
        }
        if (i < count) {
            res_string = results[i];
            if (is_wildcard) *is_wildcard = results_wildcard[i];
            res = ZPN_RESULT_NO_ERROR;
        } else {
            res = ZPN_RESULT_NOT_FOUND;
            if (is_wildcard) *is_wildcard = 0;
        }
    } else {
        res = ZPN_RESULT_NOT_FOUND;
        if (is_wildcard) *is_wildcard = 0;
    }

    if (matched_domain) {
        if (res_string) {
            snprintf(matched_domain, matched_domain_len, "%s", res_string);
        }
    }

    if (!is_inet) {
        zpn_diamond_search_stats_counter((int64_t)count, pattern_match_counts);
        if (!count) {
            zpn_application_search_debug_stats_counter(zpn_app_search_none, zpn_diamond_domain_not_found);
        }
    } else if (is_inet && !count) {
        zpn_application_search_debug_stats_counter(zpn_app_search_none, zpn_ip_lookup_not_found);
    }

    /* only measure it for diamond_search() */
    if (!is_inet && start_us > 0) {
        delta_us = epoch_us() - start_us;
        ZPN_LOG(AL_NOTICE, "diamond_search performance: zpn_application_search for domain: %s, delta_us=%"PRId64", count=%d", domain_name, delta_us, (int)count);
    }

    return res;
}

int zpn_application_search_by_scope(int64_t scope_gid,
                                    const char *domain_name,
                                    size_t domain_name_len,
                                    char *matched_domain,
                                    size_t matched_domain_len,
                                    int *is_wildcard)
{
    return zpn_application_search(scope_gid, 1, domain_name, domain_name_len, matched_domain, matched_domain_len, is_wildcard, 0);
}

int zpn_application_search_by_customer(int64_t customer_gid,
                                       const char *domain_name,
                                       size_t domain_name_len,
                                       char *matched_domain,
                                       size_t matched_domain_len,
                                       int *is_wildcard)
{
    /* customer_gid is default scope_gid */
    return zpn_application_search(customer_gid, 0, domain_name, domain_name_len, matched_domain, matched_domain_len, is_wildcard, 0);
}

int zpn_application_search_by_customer_with_filter(int64_t customer_gid,
                                       const char *domain_name,
                                       size_t domain_name_len,
                                       char *matched_domain,
                                       size_t matched_domain_len,
                                       int *is_wildcard,
                                       int skip_non_sipa_apps)
{
    /* customer_gid is default scope_gid */
    return zpn_application_search(customer_gid, 0, domain_name, domain_name_len, matched_domain, matched_domain_len, is_wildcard, skip_non_sipa_apps);
}

int zpn_application_search_by_scope_with_filter(int64_t scope_gid,
                                    const char *domain_name,
                                    size_t domain_name_len,
                                    char *matched_domain,
                                    size_t matched_domain_len,
                                    int *is_wildcard,
                                    int skip_non_sipa_apps)
{
    return zpn_application_search(scope_gid, 1, domain_name, domain_name_len, matched_domain, matched_domain_len, is_wildcard, skip_non_sipa_apps);
}

#endif //MOCK_ZPN_APPLICATION_SEARCH

int zpn_application_customer_register_response_callback(void                    *cookie,
                                                        struct wally_registrant *registrant,
                                                        struct wally_table      *table,
                                                        int64_t                 sequence,
                                                        int                     status)
{
    struct wally_callback_queue *q;
    struct zpn_customer_application_state *state = cookie;

    ZPATH_RWLOCK_WRLOCK(&(local_app_lock), __FILE__, __LINE__);

    // change to async count and when outstanding_registration_count == 0 then proceed...
     __sync_sub_and_fetch_4(&(state->outstanding_registration_count), 1);
    ZPN_DEBUG_APPLICATION("zpn_application_customer_register_response_callback, outstanding_registration_count (%d->%d)",
                                    state->outstanding_registration_count + 1, state->outstanding_registration_count);
    if (state->outstanding_registration_count == 0) {
        /* we have completed all wally table register calls */
        state->registration_completed = 1;
        q = state->wally_callback_queue;
        state->wally_callback_queue = NULL;

        ZPATH_RWLOCK_UNLOCK(&(local_app_lock), __FILE__, __LINE__);

        if (q) {
            wally_callback_queue_callback(q);
            wally_callback_queue_destroy(q);
        }
    } else {
        ZPATH_RWLOCK_UNLOCK(&(local_app_lock), __FILE__, __LINE__);
        /* still waiting for the other wally table register to finish */
        return ZPATH_RESULT_NO_ERROR;
    }

    return ZPATH_RESULT_NO_ERROR;
}

static inline void
zpn_application_app_multi_match_feature_flag_init(int64_t customer_gid, struct zpn_customer_application_state *state) {
    static int app_multi_match_global_flag_init_done = 0;

    if (!app_multi_match_global_flag_init_done) {
        zpn_app_multi_match_global_config_ovd_init();
        app_multi_match_global_flag_init_done = 1;
    }

    if (!state) return;
    ZPN_DEBUG_APPLICATION("Application Multi Match feature flag init for customer_gid: %"PRId64, customer_gid);

    state->app_multi_match_feature_enabled = is_application_multi_match_enabled_for_customer(customer_gid);
    zpath_config_override_monitor_int(APPLICATION_MULTI_MATCH_FEATURE,
                                      &(state->app_multi_match_feature_enabled),
                                      (is_broker_or_exporter_tenant ? zpn_app_multi_match_feature_toggled_cb : NULL),
                                      APPLICATION_MULTI_MATCH_FEATURE_DEFAULT,
                                      customer_gid,
                                      (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                      (int64_t)0);
}

static inline void
zpn_application_app_pattern_match_feature_flag_init(int64_t customer_gid, struct zpn_customer_application_state *state) {
    static int app_pattern_match_global_flag_init_done = 0;

    if (!app_pattern_match_global_flag_init_done) {
        zpn_app_pattern_match_global_config_ovd_init();
        app_pattern_match_global_flag_init_done = 1;
    }

    if (!state) return;
    ZPN_DEBUG_APPLICATION("Application Pattern Match feature flag init for customer_gid: %"PRId64, customer_gid);

    state->app_pattern_match_feature_enabled = is_application_pattern_match_enabled_for_customer(customer_gid);
    zpath_config_override_monitor_int(APPLICATION_PATTERN_MATCH_FEATURE,
                                      &(state->app_pattern_match_feature_enabled),
                                      NULL,
                                      APPLICATION_PATTERN_MATCH_FEATURE_DEFAULT,
                                      customer_gid,
                                      (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                      (int64_t)0);
}

static inline void
zpn_application_app_enqueue_dequeue_fix_feature_flag_init(int64_t customer_gid, struct zpn_customer_application_state *state) {
    if (!state) return;

    ZPN_DEBUG_APPLICATION("Application Enqueue Dequeue fix feature flag init for customer_gid: %"PRId64, customer_gid);

    state->app_enqueue_dequeue_fix_enabled = is_unit_test() ? 1: is_application_enqueue_dequeue_fix_enabled_for_customer(customer_gid);
    zpath_config_override_monitor_int(CONFIG_FEATURE_ENQUEUE_DEQUEUE_FIX,
                                      &(state->app_enqueue_dequeue_fix_enabled),
                                      NULL,
                                      CONFIG_FEATURE_ENQUEUE_DEQUEUE_FIX_DEFAULT,
                                      customer_gid,
                                      (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                      (int64_t)0);
}

static inline int
app_scaling_need_to_register(struct zpn_customer_application_state *state)
{
    int need_to_registered = 0;
    if (state) {
        ZPATH_RWLOCK_RDLOCK(&(state->customer_app_lock), __FILE__, __LINE__);
        need_to_registered = !state->app_scaling_registered;
        ZPATH_RWLOCK_UNLOCK(&(state->customer_app_lock), __FILE__, __LINE__);
    }
    return need_to_registered;
}

static inline void
zpn_application_app_scaling_feature_flag_init(int64_t customer_gid, struct zpn_customer_application_state *state) {
    if (!app_scaling_need_to_register(state)) return;

    ZPATH_RWLOCK_WRLOCK(&(state->customer_app_lock), __FILE__, __LINE__);
    if (!state->app_scaling_registered) {
        state->app_scaling_registered = 1;
        ZPATH_RWLOCK_UNLOCK(&(state->customer_app_lock), __FILE__, __LINE__);
        ZPN_DEBUG_APPLICATION("Application App Scaling feature flag init for customer_gid: %"PRId64, customer_gid);
        state->app_scaling_feature_enabled = is_app_scaling_feature_enabled_for_customer(customer_gid);
        zpath_config_override_monitor_int(APP_SCALING_FEATURE,
                                          &(state->app_scaling_feature_enabled),
                                          zpn_app_scaling_updated_cb,
                                          DEFAULT_APP_SCALING_FEATURE,
                                          customer_gid,
                                          (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                          (int64_t)0);
        if (zpn_app_scaling_updated_cb == NULL) {
            ZPN_LOG(AL_WARNING, "customer %"PRId64": zpn_app_scaling_updated_cb is not set", customer_gid);
        }
    } else {
        ZPATH_RWLOCK_UNLOCK(&(state->customer_app_lock), __FILE__, __LINE__);
    }
}

void zpn_register_app_scaling_flag(int64_t customer_gid)
{
    struct zpn_customer_application_state * state = zpn_application_customer_state_get(customer_gid);
    zpn_application_app_scaling_feature_flag_init(customer_gid, state);
}

void zpn_set_app_scaling_update_callback(void *callback) {
    zpn_app_scaling_updated_cb = callback;
}

/* Checks APPLICATION_MATCH_SIPA_ONLY_APPS_FOR_SIPA_CLIENT_ENABLE config override for given customer. By default, it's disabled. */
static inline int
zpn_application_is_app_match_sipa_only_apps_for_sipa_client_enabled_for_customer(int64_t customer_gid)
{
    int64_t config_value = 0;

    config_value = zpath_config_override_get_config_int(APPLICATION_MATCH_SIPA_ONLY_APPS_FOR_SIPA_CLIENT_ENABLE,
                                                        &config_value,
                                                        DEFAULT_APPLICATION_MATCH_SIPA_ONLY_APPS_FOR_SIPA_CLIENT,
                                                        customer_gid,
                                                        (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                        (int64_t)0);
    ZPN_DEBUG_COR("Application Match SIPA Only Apps For SIPA Client feature enable config value for customer_id %"PRId64" is %"PRId64" ",
                  customer_gid, config_value);

    return config_value ? 1 : 0;
}

static inline void
zpn_application_app_match_sipa_only_apps_for_sipa_client_feature_flag_init(int64_t customer_gid, struct zpn_customer_application_state *state) {
    if (!state) return;

    ZPN_DEBUG_APPLICATION("Application Match SIPA only apps for SIPA client feature flag init for customer_gid: %"PRId64, customer_gid);
    state->app_match_sipa_only_apps_for_sipa_client = zpn_application_is_app_match_sipa_only_apps_for_sipa_client_enabled_for_customer(customer_gid);
    zpath_config_override_monitor_int(APPLICATION_MATCH_SIPA_ONLY_APPS_FOR_SIPA_CLIENT_ENABLE,
                                      &(state->app_match_sipa_only_apps_for_sipa_client),
                                      NULL,
                                      DEFAULT_APPLICATION_MATCH_SIPA_ONLY_APPS_FOR_SIPA_CLIENT,
                                      customer_gid,
                                      (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                      (int64_t)0);
}

int zpn_application_match_sipa_only_apps_for_sipa_client_enabled(int64_t customer_gid)
{
    struct zpn_customer_application_state  *state = state_get_without_lock(customer_gid);
    int                                     status = 0;
    int                                     feature_status = 0;
    int                                     hard_disabled_feature_status = 0;

    feature_status = state->app_match_sipa_only_apps_for_sipa_client;
    hard_disabled_feature_status = g_app_match_sipa_only_apps_for_sipa_client_hard_disabled_feature_status;

    ZPN_DEBUG_COR("Application Match SIPA Only Apps For SIPA Client feature flag value for customer_id %"PRId64" is feature_config:%d hard_disable:%d",
                  customer_gid, feature_status, hard_disabled_feature_status);

    status = (!hard_disabled_feature_status && feature_status);

    return status;
}

int zpn_application_customer_register(int64_t                       customer_gid,
                                      wally_response_callback_f     *callback,
                                      void                          *void_cookie,
                                      int64_t                       int_cookie)
{
    return zpn_application_customer_register_with_app_match(customer_gid, callback, void_cookie, int_cookie, 1);
}

int zpn_application_customer_register_with_app_match(int64_t      customer_gid,
                                      wally_response_callback_f     *callback,
                                      void                          *void_cookie,
                                      int64_t                       int_cookie,
                                      int                           is_app_match)
{
    struct zpn_customer_application_state *state;
    int res = ZPATH_RESULT_NO_ERROR;
    int shard_index = ZPATH_SHARD_FROM_GID(customer_gid);
    int registered = 0;
    int async = 0;
    int outstanding_registration_count;

    state = state_get_and_lock(customer_gid, 1);
    if (!state) {
        ZPN_LOG(AL_CRITICAL, "Failed to get state");
        ZPATH_RWLOCK_UNLOCK(&(local_app_lock), __FILE__, __LINE__);
        return ZPATH_RESULT_NO_MEMORY;
    }

    registered = state->registered;
    state->registered = 1;

    if (!registered) {
        // Only for broker and PSE, NOT exporter
        if (is_app_match) {
            zpn_application_app_multi_match_feature_flag_init(customer_gid, state);
            zpn_application_app_scaling_feature_flag_init(customer_gid, state);
            zpn_application_app_pattern_match_feature_flag_init(customer_gid, state);
            zpn_application_app_match_sipa_only_apps_for_sipa_client_feature_flag_init(customer_gid, state);
        }
        // Done for all components including exporter
        zpn_application_app_enqueue_dequeue_fix_feature_flag_init(customer_gid, state);
    }

    if (!state->registration_completed && !registered) {
        if (callback) {
            if (!state->wally_callback_queue) {
		        state->wally_callback_queue = wally_callback_queue_create();
            }
            wally_callback_queue_add(state->wally_callback_queue,
                                     callback,
                                     void_cookie,
                                     int_cookie);
	    }
        async = 1;
    }
    ZPATH_RWLOCK_UNLOCK(&(local_app_lock), __FILE__, __LINE__);

    if (!registered) {
        outstanding_registration_count = __sync_add_and_fetch_4(&(state->outstanding_registration_count), 1);
        res = wally_table_register_for_row(NULL,
                                           zpn_application_customer_gid_column[shard_index],
                                           &customer_gid,
                                           sizeof(customer_gid),
                                           0,
                                           0,
                                           0,
                                           0,
                                           0,
                                           zpn_application_customer_register_response_callback,
                                           state);
        async = (res == WALLY_RESULT_ASYNCHRONOUS);
        if (res == WALLY_RESULT_ASYNCHRONOUS) {
            ZPN_DEBUG_APPLICATION("Registering customer for zpn_application asynchronously - outstanding registration count (%d->%d)",
                                            outstanding_registration_count - 1, outstanding_registration_count);
            res = WALLY_RESULT_NO_ERROR;
        } else {
            outstanding_registration_count = __sync_sub_and_fetch_4(&(state->outstanding_registration_count), 1);
            ZPN_DEBUG_APPLICATION("Customer register for zpn_application returned: %s, outstanding registration count: (%d->%d)",
                            zpath_result_string(res), outstanding_registration_count + 1, outstanding_registration_count);
        }

        if (res == WALLY_RESULT_NO_ERROR) {
            outstanding_registration_count = __sync_add_and_fetch_4(&(state->outstanding_registration_count), 1);
            res = wally_table_register_for_row(NULL,
                                               zpn_application_domain_customer_gid_column[shard_index],
                                               &customer_gid,
                                               sizeof(customer_gid),
                                               0,
                                               0,
                                               0,
                                               0,
                                               0,
                                               zpn_application_customer_register_response_callback,
                                               state);

            if (!async) async = (res == WALLY_RESULT_ASYNCHRONOUS);
            if (res == WALLY_RESULT_ASYNCHRONOUS) {
                ZPN_DEBUG_APPLICATION("Registering customer for zpn_application_domain asynchronously - outstanding registration count (%d->%d)",
                                            outstanding_registration_count - 1, outstanding_registration_count);
                res = WALLY_RESULT_NO_ERROR;
            } else {
                outstanding_registration_count = __sync_sub_and_fetch_4(&(state->outstanding_registration_count), 1);
                ZPN_DEBUG_APPLICATION("Customer register for zpn_application_domain returned: %s, outstanding registration count: (%d->%d)",
                                zpath_result_string(res), outstanding_registration_count + 1, outstanding_registration_count);

            }
        }
        /* We return asynchronous on first response... */
        if (res == WALLY_RESULT_NO_ERROR) res = WALLY_RESULT_ASYNCHRONOUS;
    }
    if (callback && async) {
        return WALLY_RESULT_ASYNCHRONOUS;
    }
    return res;
}

int zpn_application_asst_multi_match_feature_flag_init(int64_t instance_gid)
{
    struct zpn_customer_application_state *state;
    int res = ZPATH_RESULT_NO_ERROR;
    int64_t customer_gid = ZPATH_GID_GET_CUSTOMER_GID(instance_gid);

    state = state_get_and_lock(customer_gid, 1);
    if (!state) {
        ZPN_LOG(AL_CRITICAL, "Failed to get state");
        ZPATH_RWLOCK_UNLOCK(&(local_app_lock), __FILE__, __LINE__);
        return ZPATH_RESULT_NO_MEMORY;
    }

    zpn_application_app_multi_match_feature_flag_init(customer_gid, state);
    ZPATH_RWLOCK_UNLOCK(&(local_app_lock), __FILE__, __LINE__);

    return res;
}

int zpn_application_debug_search_all(struct zpath_debug_state *request_state,
                                     const char **query_values,
                                     int query_value_count,
                                     void *cookie)
{
    const char *scope_str = query_values[0];
    const char *app_str = query_values[1];
    const char *protocol_str = query_values[2];
    const char *port_str = query_values[3];
    const char *assistant_str = query_values[4];

    int64_t scope_gid;
    int64_t customer_gid;
    int64_t assistant_gid = 0;
    int protocol;
    int port;

    int64_t matched_apps[1000];
    size_t matched_apps_count = 1000;
    int64_t matched_app_groups[1000];
    size_t matched_app_groups_count = 1000;
    int wildcard = 0;
	int64_t most_specific_app_gid = 0;

    int res;

    size_t i;

    if (!scope_str || !app_str || !protocol_str || !port_str) {
        ZDP("Require arguments");
        return ZPATH_RESULT_ERR;
    }

    scope_gid = strtoul(scope_str, NULL, 0);
    protocol = strtoul(protocol_str, NULL, 0);
    port = strtoul(port_str, NULL, 0);
    if (assistant_str) {
        assistant_gid = strtoul(assistant_str, NULL, 0);
    }
    customer_gid = ZPATH_GID_GET_CUSTOMER_GID(scope_gid);

    /* We need to make sure the customer is 'alive' first... */
    res = zpn_application_customer_register(customer_gid, NULL, NULL, 0);
    if (res) {
        ZDP("Customer register returned %s\n", zpath_result_string(res));
        return ZPATH_RESULT_NO_ERROR;
    }



    res = zpn_application_search_all(scope_gid,
                                     app_str,
                                     strlen(app_str),
                                     protocol,
                                     port,
                                     assistant_gid,
                                     NULL,
                                     matched_apps,
                                     &matched_apps_count,
                                     matched_app_groups,
                                     &matched_app_groups_count,
                                     &wildcard,
									 &most_specific_app_gid,
                                     NULL,
                                     NULL,
                                     0);

    if (res) {
        ZDP("zpn_application_search_all returned %s\n", zpath_result_string(res));
        return ZPATH_RESULT_NO_ERROR;
    }

    ZDP("Success. Wildcard = %s\n", wildcard ? "Yes" : "No");
    ZDP("   Matched applications = ");
    for (i = 0; i < matched_apps_count; i++) {
        struct zpn_application *app;
        ZDP(" %ld", (long) matched_apps[i]);
        if (zpn_application_get_by_id_immediate(matched_apps[i],&app) == ZPN_RESULT_NO_ERROR) {
            ZDP("(%s), (%s)", app->name, app->match_style);
        }
    }
	if (most_specific_app_gid > 0) {
		ZDP("\n");
		ZDP("   Application <%s>:%d:%d Most specific app_gid: %"PRId64, app_str, protocol, port, most_specific_app_gid);
	}

    ZDP("\n   Matched app groups   = ");
    for (i = 0; i < matched_app_groups_count; i++) {
        struct zpn_application_group *group;
        ZDP(" %ld", (long) matched_app_groups[i]);
        // coverity [callee_ptr_arith]
        if (zpn_application_group_get_by_id(matched_app_groups[i],
                                            &group,
                                            NULL,
                                            NULL,
                                            0) == ZPATH_RESULT_NO_ERROR) {
            ZDP("(%s)", group->name);
        }
    }

    ZDP("\n");
    return ZPATH_RESULT_NO_ERROR;
}

void argo_zpn_application_init()
{
    if(!argo_register_global_structure(ZPN_APPLICATION_HELPER))
        ZPN_LOG(AL_ERROR, "Could not register zpn_application argo object");

}

int zpn_application_init(struct wally *single_tenant_wally, int64_t tenant_id, int tenant_is_broker_or_exporter, int single_tenant_fully_loaded, int register_with_zpath_table, int tenant_is_broker)
{
    zpn_application_tenant_is_broker = tenant_is_broker;
    return zpn_application_init_1(single_tenant_wally, tenant_id, tenant_is_broker_or_exporter, single_tenant_fully_loaded, register_with_zpath_table, 0);
}

int zpn_application_init_1(struct wally *single_tenant_wally, int64_t tenant_id, int tenant_is_broker_or_exporter, int single_tenant_fully_loaded, int register_with_zpath_table, int unit_test)
{
    int res;

    local_app_lock = ZPATH_RWLOCK_INIT;
    tld_1_domains_lock = ZPATH_MUTEX_INIT;

    zpn_application_description = argo_register_global_structure(ZPN_APPLICATION_HELPER);
    if (!zpn_application_description) {
        return ZPN_RESULT_ERR;
    }

    zpn_application_search_stats_description = argo_register_global_structure(ZPN_APPLICATION_SEARCH_STATS_HELPER);
    if (!zpn_application_search_stats_description) {
        ZPN_LOG(AL_ERROR, "Could not register zpn_application_search_stats_description");
        return ZPN_RESULT_ERR;
    }

    if (tenant_is_broker_or_exporter && !is_unit_test()) {
        is_broker_or_exporter_tenant = 1;
        if (!argo_log_register_structure(argo_log_get("statistics_log"),
                                         "application_search_stats",
                                         AL_INFO,
                                         5*60*1000*1000,    /* 5 minute */
                                         zpn_application_search_stats_description,
                                         &zpn_app_search_stats,
                                         0,
                                         NULL,
                                         0)) {
            ZPN_LOG(AL_ERROR, "Could not register zpn_application_search_stats log");
            return ZPN_RESULT_ERR;
        }
    }


    if (single_tenant_wally) {
        struct wally_table *table;
        int shard_index = unit_test ? 0 : ZPATH_SHARD_FROM_GID(tenant_id);
        zpn_application_unit_test = unit_test;
        zpn_application_index_column = ZPN_APP_CALLOC(ZPATH_MAX_SHARDS * sizeof(*zpn_application_index_column));
        zpn_application_customer_gid_column = ZPN_APP_CALLOC(ZPATH_MAX_SHARDS * sizeof(*zpn_application_customer_gid_column));
        if (single_tenant_fully_loaded) {
            /* This is generally used by private brokers */
            res = zpath_app_fully_loaded_customer_table(&table,
                                                        tenant_id,
                                                        single_tenant_wally,
                                                        zpn_application_description,
                                                        tenant_is_broker_or_exporter ? zpn_application_row_callback_broker_and_exporter : zpn_application_row_callback_assistant,
                                                        NULL,
                                                        zpn_application_row_fixup,
                                                        register_with_zpath_table);
            if (res) {
                ZPN_LOG(AL_ERROR, "Could not fully load zpn_application table");
                return res;
            }
        } else {
            /* This is generally used by connectors */
            table = wally_table_create(single_tenant_wally,
                                       1,
                                       zpn_application_description,
                                       tenant_is_broker_or_exporter ? zpn_application_row_callback_broker_and_exporter : zpn_application_row_callback_assistant,
                                       NULL,
                                       1,
                                       1,
                                       zpn_application_row_fixup);
            if (!table) {
                ZPN_LOG(AL_ERROR, "Could not get zpn_application table");
                return ZPN_RESULT_ERR;
            }
        }

        zpn_application_index_column[shard_index] = wally_table_get_index(table, "gid");
        if (!zpn_application_index_column[shard_index]) {
            ZPN_LOG(AL_ERROR, "Could not get column");
            return ZPN_RESULT_ERR;
        }

        zpn_application_customer_gid_column[shard_index] = wally_table_get_index(table, "customer_gid");
        if (!zpn_application_customer_gid_column[shard_index]) {
            ZPN_LOG(AL_ERROR, "Could not get column");
            return ZPN_RESULT_ERR;
        }
    } else {
        res = zpath_app_fully_loaded_sharded_table(zpn_application_description,
                                                    tenant_is_broker_or_exporter ? &zpn_application_row_callback_broker_and_exporter
                                                                                : &zpn_application_row_callback_assistant,
                                                    NULL,
                                                    zpn_application_row_fixup);
        if (res) {
            return res;
        }

        zpn_application_index_column = zpath_app_get_sharded_index("zpn_application", "gid");
        if (!zpn_application_index_column) {
            return ZPN_RESULT_ERR;
        }

        zpn_application_customer_gid_column = zpath_app_get_sharded_index("zpn_application", "customer_gid");
        if (!zpn_application_customer_gid_column) {
            return ZPN_RESULT_ERR;
        }
    }

    if (tenant_is_broker_or_exporter) {
        res = zpath_debug_add_read_command("Perform application lookup, matching all possible matches",
                                      "/zpn/application/search",
                                      zpn_application_debug_search_all,
                                      NULL,
                                      "scope", "Required. Scope GID (use Customer gid for default scope",
                                      "app", "Required. Application name, as domain or IP address",
                                      "protocol", "Required. Protocol: 6 or 17",
                                      "port", "Required. 0 through 65535 inclusive",
                                      "assistant", "Optional. Limit results to apps that could use this assistant",
                                      NULL);
        if (res) {
            return res;
        }
    }

    if (zpn_application_tenant_is_broker) {
        res = zpath_debug_add_safe_read_command("check multi-match feature flags status for a customer",
                                           "/zpn/app/multi_match/status",
                                           zpn_app_multi_match_status,
                                           NULL,
                                           "customer", "Required. customer_gid",
                                           NULL);
        if (res) {
            ZPATH_LOG(AL_ERROR, "Couldn't add curl debug command /zpn/app/multi_match/status");
            return res;
        }

        res = zpath_debug_add_safe_read_command("check application-pattern-match feature flags status for a customer",
                                           "/zpn/app/pattern_match/status",
                                           zpn_app_pattern_match_status,
                                           NULL,
                                           "customer", "Required. customer_gid",
                                           NULL);
        if (res) {
            ZPATH_LOG(AL_ERROR, "Couldn't add curl debug command /zpn/app/pattern_match/status");
            return res;
        }

        res = zpath_debug_add_safe_read_command("check Application Match SIPA Only Apps for SIPA Client feature flags status for a customer",
                                           "/zpn/app/match_sipa_only_for_sipa_client/status",
                                           zpn_app_match_sipa_only_apps_for_sipa_client_status,
                                           NULL,
                                           "customer", "Required. customer_gid",
                                           NULL);
        if (res) {
            ZPATH_LOG(AL_ERROR, "Couldn't add curl debug command /zpn/app/match_sipa_only_for_sipa_client/status");
            return res;
        }
    }

    res = zpath_debug_add_safe_read_command("Dump all FQDN domains in the diamond table.",
                                  "/zpn/application/diamond_dump_domains",
                                  zpn_application_domains_dump,
                                  NULL,
                                  "customer", "Required. Customer GID",
                                  "pattern_match",  "<optional> if omitted, dump without pattern match",
                                  NULL);
    if (res) {
        ZPATH_LOG(AL_ERROR, "Couldn't add curl debug command /zpn/application/domains");
        return res;
    }

    res = zpath_debug_add_read_command("Dump all IPv4 and IPv6 addresses in radix tree for customer applications.",
                                  "/zpn/application/ips",
                                  zpn_application_ips_dump,
                                  NULL,
                                  "customer", "Required. customer GID",
                                  NULL);
    if (res) {
        ZPATH_LOG(AL_ERROR, "Couldn't add curl debug command /zpn/application/ips");
        return res;
    }

    res = zpath_debug_add_safe_read_command("Search the FQDN domain for an application.",
                                  "/zpn/application/diamond_search_domain",
                                  zpn_application_domain_dump,
                                  NULL,
                                  "customer", "Required. Customer GID",
                                  "app", "Required. Application name, as domain",
                                  "scope", "Optional. Scope GID (use Customer gid for default scope)",
                                  NULL);
    if (res) {
        ZPATH_LOG(AL_ERROR, "Couldn't add curl debug command /zpn/application/domain");
        return res;
    }

    res = zpath_debug_add_read_command("dump the stats of broker mtunnel",
                                  "/zpn/application/dump/stats",
                                  zpn_application_dump_stats,
                                  NULL,
                                  NULL);
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not register /zpn/application/dump/stats to debug: %s",
                           zpn_result_string(res));
        return res;
    }

    res = zpath_debug_add_read_command("Search inclusive domains",
                                  "/zpn/application/inclusive_domain/search",
                                  zpn_application_inclusive_domain_dump,
                                  NULL,
                                  "customer", "Required. Customer GID",
                                  "app", "Required. Application name, as domain",
                                  "scope", "Optional. Scope GID (use Customer gid for default scope)",
                                  NULL);
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not register /zpn/application/inclusive_domain/search to debug: %s",
                           zpn_result_string(res));
        return res;
    }

    return ZPN_RESULT_NO_ERROR;
}

int zpn_application_get_by_id(int64_t app_id,
                              struct zpn_application **app,
                              wally_response_callback_f callback_f,
                              void *callback_cookie,
                              int64_t callback_id)
{
    size_t row_count = 1;
    int res;

    int shard_index = ZPATH_SHARD_FROM_GID(app_id);

    res = wally_table_get_rows_fast(zpn_application_index_column[shard_index],
                                    &app_id,
                                    sizeof(app_id),
                                    (void **) app,
                                    &row_count,
                                    1,
                                                    callback_f,
                                                    callback_cookie,
                                                    callback_id);
#ifdef DTA_CHECKPOINT
    ROW_FILTER_DTA_CHECKPOINT(app, row_count, ZPATH_GID_GET_CUSTOMER_GID(app_id));
#endif

    return res;
}


int zpn_application_get_by_customer_gid_immediate(int64_t customer_gid,
                                        struct zpn_application **apps,
                                        size_t *app_count)
{
    int res;

    int shard_index = ZPATH_SHARD_FROM_GID(customer_gid);

    res = wally_table_get_rows_fast(zpn_application_customer_gid_column[shard_index],
                                    &customer_gid,
                                    sizeof(customer_gid),
                                    (void **) apps,
                                    app_count,
                                    0,
                                    NULL,
                                    NULL,
                                    0);
#ifdef DTA_CHECKPOINT
    ROW_FILTER_DTA_CHECKPOINT(apps, *app_count, customer_gid);
#endif
    return res;
}

int zpn_application_get_apps_by_customer_gid(int64_t customer_gid,
                                             int64_t *app_gids,
                                             size_t *app_count,
                                             wally_response_callback_f callback_f,
                                             void *callback_cookie,
                                             int64_t callback_id)
{
    int res;
    int shard_index = ZPATH_SHARD_FROM_GID(customer_gid);
    struct zpn_application *rows[100000];
    size_t row_count = sizeof(rows) / sizeof(rows[0]);
    if (row_count > (*app_count)) {
        row_count = *app_count;
    }
    size_t i;

    res = wally_table_get_rows_fast(zpn_application_customer_gid_column[shard_index],
                                    &customer_gid,
                                    sizeof(customer_gid),
                                    (void **)&(rows[0]),
                                    &row_count,
                                    1,   /* Register on miss! Next time we should have rule... */
                                    callback_f,
                                    callback_cookie,
                                    callback_id);
#ifdef DTA_CHECKPOINT
    ROW_FILTER_DTA_CHECKPOINT(rows, row_count, customer_gid);
#endif

    if (res) {
        *app_count = 0;
        return res;
    }

        for (i = 0; i < row_count; i++) {
            app_gids[i] = rows[i]->gid;
        }
    *app_count = row_count;

    return res;
}

int zpn_application_get_by_id_immediate(int64_t app_id,
                                        struct zpn_application **app)
{
    size_t row_count = 1;
    int res;

    int shard_index = (zpn_application_unit_test) ? 0: ZPATH_SHARD_FROM_GID(app_id);

    // coverity [calee_ptr_arith]
    res = wally_table_get_rows_fast(zpn_application_index_column[shard_index],
                                    &app_id,
                                    sizeof(app_id),
                                    (void **) app,
                                    &row_count,
                                    0,
                                    NULL,
                                    NULL,
                                    0);
#ifdef DTA_CHECKPOINT
    ROW_FILTER_DTA_CHECKPOINT(app, row_count, ZPATH_GID_GET_CUSTOMER_GID(app_id));
#endif
    return res;
}

int zpn_application_translate_port(struct zpn_application *app, int port, uint16_t ip_proto)
{
    int i;

    if (ip_proto == IPPROTO_UDP) {
        for (i = 0; (i + 1) < app->udp_port_ranges_count; i += 2) {
            if ((port >= app->udp_port_ranges[i]) &&
                (port <= app->udp_port_ranges[i + 1])) {
                return port;
            }
        }

        ZPN_LOG(AL_WARNING, "Application %ld: %s: UDP port %d invalid", (long)app->gid, app->domain_name_debug_str, port);
        return 0;
    }

    if (app->tcp_ports_in_count != app->tcp_ports_out_count) {
        ZPN_LOG(AL_WARNING, "%ld: Ports in count != ports out count", (long) app->gid);
        return 0;
    }
    if (app->tcp_port_ranges_count & 1) {
        ZPN_LOG(AL_WARNING, "%ld: Odd port range count", (long)app->gid);
        return ZPN_RESULT_ERR;
    }
    for (i = 0; i < app->tcp_ports_in_count; i++) {
        if (app->tcp_ports_in[i] == port) {
            return app->tcp_ports_out[i];
        }
    }
    for (i = 0; i < app->tcp_port_ranges_count; i += 2) {
        if ((port >= app->tcp_port_ranges[i]) &&
            (port <= app->tcp_port_ranges[i + 1])) {
            return port;
        }
    }

    ZPN_DEBUG_APPLICATION("Application %ld: %s: port %d invalid", (long)app->gid, app->domain_name_debug_str, port);

    return 0;
}

int zpn_application_translate_port_protocol(struct zpn_application *app, int port, uint16_t ip_proto, int *protocol_bitmask)
{
    int i;

	if (!app || !protocol_bitmask)
		return 0;

    if (ip_proto == IPPROTO_UDP) {
        for (i = 0; (i + 1) < app->udp_port_ranges_count; i += 2) {
            if ((port >= app->udp_port_ranges[i]) &&
                (port <= app->udp_port_ranges[i + 1])) {
                /* Get the protocol configured for port */
                if (app->adp_enabled) {
                    if ((app->udp_protocols_bitmasks_count == 0) || (app->udp_protocols_bitmasks_count < (app->udp_port_ranges_count/2))) {
                        ZPN_LOG(AL_ERROR,"Error : Application %ld : %s:UDP port count %d is greater than protocol count %d - invalid index %d",
                                (long)app->gid, app->domain_name_debug_str, app->udp_port_ranges_count/2, app->udp_protocols_bitmasks_count, (i/2));
                    } else {
                        ZPN_DEBUG_APPLICATION("Application %ld: %s: UDP port %d has protocol 0x%x configured",
                                (long)app->gid, app->domain_name_debug_str, port, app->udp_protocols_bitmasks[i/2]);
                        *protocol_bitmask = app->udp_protocols_bitmasks[i/2];
                    }
                }
                return port;
            }
        }

        ZPN_LOG(AL_WARNING, "Application %ld: %s: UDP port %d invalid", (long)app->gid, app->domain_name_debug_str, port);
        return 0;
    }

    if (app->tcp_ports_in_count != app->tcp_ports_out_count) {
        ZPN_LOG(AL_WARNING, "%ld: Ports in count != ports out count", (long) app->gid);
        return 0;
    }
    if (app->tcp_port_ranges_count & 1) {
        ZPN_LOG(AL_WARNING, "%ld: Odd port range count", (long)app->gid);
        return ZPN_RESULT_ERR;
    }
    for (i = 0; i < app->tcp_ports_in_count; i++) {
        if (app->tcp_ports_in[i] == port) {
            return app->tcp_ports_out[i];
        }
    }

    for (i = 0; i < app->tcp_port_ranges_count; i += 2) {
        if ((port >= app->tcp_port_ranges[i]) &&
            (port <= app->tcp_port_ranges[i + 1])) {
            /* Get the protocol configured for port */
            if (app->adp_enabled) {
                if ((app->tcp_protocols_bitmasks_count == 0) || (app->tcp_protocols_bitmasks_count < (app->tcp_port_ranges_count/2))) {
                    ZPN_LOG(AL_ERROR,"Error : Application %ld: %s: TCP port count %d protocol count %d - invalid index %d",
                            (long)app->gid, app->domain_name_debug_str, app->tcp_port_ranges_count, app->tcp_protocols_bitmasks_count, (i/2));
                } else {
                    ZPN_DEBUG_APPLICATION("Application %ld: %s: TCP port %d has protocol 0x%x configured",
                            (long)app->gid, app->domain_name_debug_str, port, app->tcp_protocols_bitmasks[i/2]);
                    *protocol_bitmask = app->tcp_protocols_bitmasks[i/2];
                }
            }
            return port;
        }
    }

    ZPN_DEBUG_APPLICATION("Application %ld: %s: port %d invalid", (long)app->gid, app->domain_name_debug_str, port);
    return 0;
}

int ports_array_contains(int *port_ranges, size_t port_ranges_count, int port)
{
    size_t i;
    for (i = 0; (i + 1) < port_ranges_count; i += 2) {
        if ((port_ranges[i] <= port) && (port_ranges[i + 1] >= port)) {
            return 1;
        }
    }
    return 0;
}

#if 0

/*
 * Inserts an element into an array, maintaining order, and not inserting duplicates
 *
 * DOES NOT VERIFY FINAL SIZE
 */
static void simple_array_insert_ordered(int64_t *array, size_t *array_len, int64_t value)
{
    size_t first = 0;
    size_t last = *array_len - 1;
    size_t middle = 0;

    while (first <= last) {
        middle = (first + last) / 2;
        if (array[middle] < value) {
            first = middle + 1;
            middle = first;
        } else if (array[middle] > value) {
            last = middle - 1;
        } else {
            /* Found */
            return;
        }
    }

    /* middle == insert location. Everything at middle's index and later must be move forward. */
    for (last = array_len; last > middle; last--) {
        array[last] = array[last - 1];
    }
    array[middle] = value;
    (*array_len)++;
    return;
}

#endif // 0

/*
 * Adds an element into an array, without maintaining order, and not inserting duplicates
 *
 * DOES NOT VERIFY FINAL SIZE
 */
void zpn_application_simple_array_insert(int64_t *array, size_t *array_len, int64_t value)
{
    size_t ix = 0;
    size_t len = *array_len;

    for (ix = 0; ix < len; ix++)  {
        if (array[ix] == value) return;
    }
    array[len] = value;
    (*array_len)++;
    return;
}

int is_domain_exclusive(int64_t customer_gid, char *domain)
{
    struct zpn_application_domain *app_domains[10000];
    size_t app_domains_count = 10000;
    int res;
    int is_exclusive = 1;

    /*find any enabled application for the domain to check policy type*/
    res = zpn_application_domain_get_by_domain_immediate(customer_gid, domain, &(app_domains[0]), &app_domains_count);
    if (res) {
        ZPN_LOG(AL_CRITICAL, "Failed to find app_domain for: %"PRId64", %s", customer_gid, domain);
    } else {
        int i;
        for (i = 0; i < app_domains_count; i++) {
            struct zpn_application *app;
            /* coverity[callee_ptr_arith : FALSE] */
            res = zpn_application_get_by_id_immediate(app_domains[i]->application_gid, &app);
            if (res) {
                ZPN_LOG(AL_CRITICAL, "Failed to find app for: %"PRId64", %s, %"PRId64, customer_gid, domain, app_domains[i]->application_gid);
            } else {
                if(app->enabled) {
                    is_exclusive = app->m_style == zpn_match_style_exclusive ? 1: 0;
                    break;
                }
            }
        }
    }
    return is_exclusive;
}

/* return match_count */
int zpn_domain_search_all(int64_t scope_gid,
                          const char *app_name,
                          size_t app_name_len,
                          int return_inclusive_only,      // if set, only return inclusive domains
                          int *is_inclusive,
                          void **domain_lookup_results,   // void *[N]
                          int *domain_lookup_wildcard,    // int domain_lookup_wildcard[N]
                          size_t domain_lookup_count,     // N
                          int search_pattern_domain)
{
    struct zpn_customer_application_state *state;
    struct argo_inet inet;
    size_t i, j;
    int is_inet = 0;
    int64_t customer_gid = ZPATH_GID_GET_CUSTOMER_GID(scope_gid);
    int res;
    int match_count = domain_lookup_count;
    int64_t start_us = 0;
    int64_t delta_us = 0;
    int64_t pattern_match_counts = 0;

    if (!domain_lookup_count) return 0;

    /* Convert to INET */
    res = argo_string_to_inet(app_name, &inet);
    if ((res == ARGO_RESULT_NO_ERROR) && ((inet.length == 16) || (inet.length == 4))) is_inet = 1;

    state = state_get_and_lock(customer_gid, 0);
    ZPATH_RWLOCK_UNLOCK(&(local_app_lock), __FILE__, __LINE__);
    if (!state) {
        return 0;
    }
    if (zpn_debug_get(ZPN_DEBUG_APPLICATION_PERF_IDX)) start_us = epoch_us();

    ZPATH_RWLOCK_RDLOCK(&(state->customer_app_lock), __FILE__, __LINE__);
    if (is_inet) {
        if (inet.length == 4) {
            res = zradix_search(state->ipv4_lookup, inet.address, inet.netmask,
                                NULL, NULL, domain_lookup_results, &match_count);
        } else {
            res = zradix_search(state->ipv6_lookup, inet.address, inet.netmask,
                                NULL, NULL, domain_lookup_results, &match_count);
        }
        if (!res) {
            if (domain_lookup_wildcard) {
                for (i = 0; i < match_count; i++) {
                    domain_lookup_wildcard[i] = 0;
                }
            }
        } else {
            if (res == ZRADIX_RESULT_NOT_FOUND) {
                ZPN_LOG(AL_INFO, "Could not find IP : %s for scope: %" PRId64 "", app_name, scope_gid);
            } else {
                ZPN_LOG(AL_ERROR, "Could not find IP : %s for scope: %" PRId64 ", res:%s", app_name, scope_gid, zpath_result_string(res));
            }
        }
    } else {
        int do_pattern_domain_search = is_pattern_match_feature_enabled(state) && search_pattern_domain;
        match_count = diamond_search_with_pattern(state->domain_lookup,
                                                  (const uint8_t *)app_name,
                                                  app_name_len,
                                                  domain_lookup_results,
                                                  domain_lookup_wildcard,
                                                  domain_lookup_count,
                                                  &pattern_match_counts,
                                                  do_pattern_domain_search);
    }
    ZPATH_RWLOCK_UNLOCK(&(state->customer_app_lock), __FILE__, __LINE__);

    /* only measure it for diamond_search() */
    if (!is_inet && start_us > 0) {
        delta_us = epoch_us() - start_us;
        ZPN_LOG(AL_NOTICE, "diamond_search performance: zpn_domain_search_all for domain: %s, delta_us=%"PRId64", count=%d", app_name, delta_us, match_count);
    }

    if (!is_inet) {
        zpn_diamond_search_stats_counter((int64_t)match_count, pattern_match_counts);
        if (!match_count) {
            zpn_application_search_debug_stats_counter(zpn_app_search_none, zpn_diamond_domain_not_found);
        }
    } else if (is_inet && !match_count) {
        zpn_application_search_debug_stats_counter(zpn_app_search_none, zpn_ip_lookup_not_found);
    }

    j = 0;
    int is_exclusive = 0;
    for (i = 0; i < match_count; i++) {

        /* only keep the domain which app can be accessed by the scope */
        if (is_domain_valid_to_scope(scope_gid, (char*)(domain_lookup_results[i]), 0, 1)) {
            // if domain is exclusive or multi match is not enabled
            is_exclusive = !is_app_multi_match_feature_enabled(customer_gid) || is_domain_exclusive(customer_gid, domain_lookup_results[i]);
            if (i == 0) {
                /* return for the flag to tell if the list of domains is inclusive or not */
                if (is_inclusive) *is_inclusive = !is_exclusive;
                /* if caller only wants inclusive_domains we return nothing if first domain is exclusive */
                if (is_exclusive && return_inclusive_only) break;
            } else {
                /* Stop at the first exclusive match. Matched inclusive domains  are added */
                if (is_exclusive) break;
            }

            domain_lookup_results[j] = domain_lookup_results[i];
            if (domain_lookup_wildcard) domain_lookup_wildcard[j] = (int)(domain_lookup_wildcard[i]);
            j++;
            /* if caller does not ask for "inclusive_domains only", copy the first matched exclusive domain and exit */
            if (is_exclusive) break;
        }
    }
    match_count = j;
    if (match_count >= RESULTS_ARRAY_SIZE) {
        if (is_inet) {
            zpn_application_search_debug_stats_counter(zpn_app_search_none, zpn_multi_match_large_ips_matched);
            ZPN_LOG(AL_WARNING, "%"PRId64": Multi match matched %d ips", customer_gid, match_count);
        } else {
            zpn_application_search_debug_stats_counter(zpn_app_search_none, zpn_multi_match_large_domains_matched);
            ZPN_LOG(AL_WARNING, "%"PRId64": Multi match matched %d domains", customer_gid, match_count);
        }
    }
    return match_count;

}

size_t zpn_get_app_gids_for_all_domains(int64_t customer_gid,
                                        const char *app_name,
                                        void **domain_lookup_results,
                                        size_t domain_lookup_results_count,
                                        int64_t *tmp_app_gids,
                                        size_t tmp_app_gids_count,
                                        int *exact_match_app_gids_count)
{
    int i, j;
    int res;
    size_t app_count = 0;

    if (exact_match_app_gids_count) {
        *exact_match_app_gids_count = 0;
    }

    for (i = 0; i < domain_lookup_results_count; i++) {
        int64_t local_gids[10000];
        size_t local_gids_count = sizeof(local_gids) / sizeof(local_gids[0]);
        res = zpn_application_domain_all(customer_gid, domain_lookup_results[i], &(local_gids[0]), &local_gids_count, 0);
        // Only get the exact match domain/IP app gid count. For exact IP's, app_name should match the IP domain
        if (exact_match_app_gids_count && (*exact_match_app_gids_count == 0) && !strcmp(domain_lookup_results[0], app_name)) {
            *exact_match_app_gids_count = local_gids_count;
        }
        if (res == ZPATH_RESULT_NO_ERROR) {
            /* We have a set of applications... Check if the application includes given ports. */

            /* Just accumulate app_gids for now, since there may
             * often be large scale duplicates- filter them out
             * now rather than inside further loops */
            for (j = 0; j < local_gids_count; j++) {
                if (app_count < tmp_app_gids_count) {
                    zpn_application_simple_array_insert(tmp_app_gids, &app_count, local_gids[j]);
                } else {
                    ZPN_LOG(AL_WARNING, "%ld: %s: Too many app_gids, %ld", (long) customer_gid, app_name, (long) tmp_app_gids_count);
                    break;
                }
            }
        }
    }

    return app_count;
}

int zpn_application_search_all(int64_t scope_gid,                     // Scope
                               const char *app_name,                  // SEARCH: this app
                               size_t app_name_len,                   //              len
                               int ip_protocol,                       //              proto
                               int port,                              //              port
                               int64_t limit_assistant_gid,           //              If set, limit to apps using this connector
                               int *is_inclusive,                     // if the first app is inclusive ?
                               int64_t *app_gids,                     // RETURN: app_gids. In order of most specific match to least specific match.
                               size_t *app_gids_count,                //         number of app_gids + wildcard matched
                               int64_t *app_group_gids,               //         app_group_gids, accumulated from app_gids in order.
                               size_t *app_group_gids_count,          //         number of app_group_gids matched (can be different form app_gids!!)
                               int *app_gid_wildcard,                 //         whether or not the most specific domain matched is a wildcard. (NOT app matched!!)
                               int64_t *most_specific_app_gid,        //         most specific matched app_gid ignoring port and protocol
                               wally_response_callback_f *callback_f, // CALLBACK: if it goes async
                               void *callback_cookie,                 //           cookie void
                               int64_t callback_id)                   //           cookie int
{
    int64_t customer_gid = ZPATH_GID_GET_CUSTOMER_GID(scope_gid);
    int res;

    void *domain_lookup_results[100];
    int domain_lookup_wildcard[100];
    size_t domain_lookup_results_count = sizeof(domain_lookup_results) / sizeof(domain_lookup_results[0]);
    int64_t tmp_app_gids[10000];
    size_t tmp_app_gids_count = 0;
    size_t app_count = 0;
    size_t grp_count = 0;
    size_t app_count_max = *app_gids_count;
    size_t grp_count_max = *app_group_gids_count;
    size_t count;
    size_t i, j, k;
    int64_t limit_assistant_group_gid = 0;
    int64_t specific_domain_app_gid = 0, specific_domain_port_app_gid = 0;
    int exact_match_app_gid_count = 0;

    *app_gids_count = 0;
    *app_group_gids_count = 0;
    if (most_specific_app_gid) *most_specific_app_gid = 0;

    /* Make sure we don't overrun our space... */
    if ((app_count_max) > (sizeof(tmp_app_gids) / sizeof(tmp_app_gids[0]))) {
        ZPN_LOG(AL_ERROR, "Oversize, app_gids_count = %ld", (long) app_count_max);
        app_count_max = sizeof(tmp_app_gids) / sizeof(tmp_app_gids[0]);
    }

    domain_lookup_results_count = zpn_domain_search_all(scope_gid,
                                                        app_name,
                                                        app_name_len,
                                                        0,     // return no matter the domain list is inclusive or exclusive
                                                        is_inclusive,
                                                        &domain_lookup_results[0],
                                                        &domain_lookup_wildcard[0],
                                                        domain_lookup_results_count,
                                                        PATTERN_DOMAIN_SEARCH_ENABLED);

    /* Get the assistant_group, if we were asked to */
    if (limit_assistant_gid) {
        struct zpn_assistantgroup_assistant_relation *relation;
        count = 1;
        res = zpn_assistantgroup_assistant_relation_get_by_assistant(limit_assistant_gid,
                                                                     &relation,
                                                                     &count,
                                                                     callback_f,
                                                                     callback_cookie,
                                                                     callback_id);
        if (res == ZPATH_RESULT_ASYNCHRONOUS) return res;
        if (res) {
            ZPN_LOG(AL_WARNING, "%ld: %s: Could not find assistant group for assistant %ld", (long) customer_gid, app_name, (long) limit_assistant_gid);
            return res;
        } else {
            if (count == 1) {
                limit_assistant_group_gid = relation->assistant_group_id;
            }
        }
    }

    /*
     * we now have all our lookup results. We want to take these and
     * find all applications using them.
     *
     * This fills in tmp_app_gids and tmp_app_gids_count. They need to
     * be filtered (afterwards) before sending back to caller
     */
    if (domain_lookup_results_count == 0) {
        /* This has become common with changes that check destination IP for matches. */
        ZPN_DEBUG_MTUNNEL("%ld: %s: (%ld) No lookup results", (long) customer_gid, app_name, (long) limit_assistant_gid);
    } else {
        if (domain_lookup_wildcard[0] && app_gid_wildcard) {
            *app_gid_wildcard = 1;
        }

        tmp_app_gids_count = zpn_get_app_gids_for_all_domains(customer_gid,
                                                              app_name,
                                                              domain_lookup_results,
                                                              domain_lookup_results_count,
                                                              &tmp_app_gids[0],
                                                              sizeof(tmp_app_gids) / sizeof(tmp_app_gids[0]),
                                                              &exact_match_app_gid_count);
    }

    for (i = 0; i < tmp_app_gids_count; i++) {
        struct zpn_application *app;
        /* coverity[callee_ptr_arith : FALSE] */
        res = zpn_application_get_by_id_immediate(tmp_app_gids[i], &app);
        if (res == ZPATH_RESULT_NO_ERROR) {

            /* If it is disabled, skip... */
            if (app->enabled == 0) continue;
            /* if app is NOT valid to scope, skip... */
            if (!is_app_valid_to_scope(scope_gid, app)) continue;

            // Select the most specific matched app gid ignoring port and protocol only if exact matched domain/IP is present
            if ((specific_domain_app_gid == 0) && exact_match_app_gid_count) {
                specific_domain_app_gid = tmp_app_gids[i];
            }
            /* Check if the app contains port+protocol
             * If there are 2 apps:
             * App1: *.company.com:80
             * App2: www.finance.company.com:443
             *
             * Req is for www.finance.company.com:80
             * We only select App1 for rule evaluation.
             */

            if (((ip_protocol == IPPROTO_UDP) && (ports_array_contains(app->udp_port_ranges, app->udp_port_ranges_count, port))) ||
                ((ip_protocol ==  IPPROTO_TCP) && (ports_array_contains(app->tcp_port_ranges, app->tcp_port_ranges_count, port))) ||
                (((ip_protocol == IPPROTO_ICMP) || (ip_protocol ==  IPPROTO_ICMPV6)) && !port && app->icmp_access_type && ((!strcmp(app->icmp_access_type, "PING") || (!strcmp(app->icmp_access_type, "TRACEROUTE")))))) {
                /* If we have a connector, see if this app includes it... */

                int any_connector_matched = 0;
                if (limit_assistant_group_gid) {
                    /* Get this application's server groups... */
                    struct zpn_app_group_relation *relations[1000];
                    count = sizeof(relations) / sizeof(relations[0]);
                    res = zpn_app_group_relation_get_by_application(app->gid,
                                                                    &(relations[0]),
                                                                    &count,
                                                                    callback_f,
                                                                    callback_cookie,
                                                                    callback_id);
                    if (res == ZPATH_RESULT_ASYNCHRONOUS) return res;
                    if (res) {
                        if (res != ZPATH_RESULT_NOT_FOUND) {
                            ZPN_LOG(AL_WARNING, "%ld: %s: Could not get server group from application %ld, res = %s", (long) customer_gid, app_name, (long) app->gid, zpath_result_string(res));
                        }
                        continue;
                    }
                    for (j = 0; (!any_connector_matched) && (j < count); j++) {
                        /* Get this server group's connector groups... */
                        struct zpn_server_group_assistant_group *rel2[1000];
                        size_t count2 = sizeof(rel2) / sizeof(rel2[0]);
                        res = zpn_server_group_assistant_group_get_by_server_group(relations[j]->group_id,
                                                                                   &(rel2[0]),
                                                                                   &count2,
                                                                                   callback_f,
                                                                                   callback_cookie,
                                                                                   callback_id);
                        if (res == ZPATH_RESULT_ASYNCHRONOUS) return res;
                        if (res) {
                            if (res != ZPATH_RESULT_NOT_FOUND) {
                                ZPN_LOG(AL_WARNING, "%ld: %s: Could not get assistant group from application %ld, server group %ld res = %s", (long) customer_gid, app_name, (long) app->gid, (long)relations[j]->group_id, zpath_result_string(res));
                            }
                            continue;
                        }
                        for (k = 0; k < count2; k++) {
                            if (rel2[k]->assistant_group_id == limit_assistant_group_gid) {
                                any_connector_matched = 1;
                                break;
                            }
                        }
                    }
                } else {
                    any_connector_matched = 1;
                }

                if (any_connector_matched) {
                    /* This application applies- we need it. Now pick up application groups...  */
                    int64_t local_app_group_ids[1000];
                    count = sizeof(local_app_group_ids) / sizeof(local_app_group_ids[0]);
                    res = zpn_application_group_application_mapping_get_group_by_app_id(app->gid,
                                                                                        &(local_app_group_ids[0]),
                                                                                        &count,
                                                                                        callback_f,
                                                                                        callback_cookie,
                                                                                        callback_id);
                    if (res == ZPN_RESULT_ASYNCHRONOUS) return res;
                    if (res) {
                        if (res != ZPN_RESULT_NOT_FOUND) {
                            ZPN_LOG(AL_WARNING, "%ld: %s: Could not get application groups from application %ld, res = %s", (long) customer_gid, app_name, (long) app->gid, zpath_result_string(res));
                        }
                        count = 0;
                    }
                    /*
                     * OKAY, now we have an application that
                     * applies, and application groups that (may)
                     * yet apply. There may not be application
                     * groups- that is okay So it is time to fill
                     * in our response a little more...
                     */
                    if (app_count < app_count_max) zpn_application_simple_array_insert(app_gids, &app_count, app->gid);
                    /* 1. Get app gid's of all exact match domains/IPs and match with app request to see if there is matched domain and port
                     * 2. Save the first exact matched one with matched domain + port
                     */
                    if ((i < exact_match_app_gid_count) && (specific_domain_app_gid != 0) && (specific_domain_port_app_gid == 0)) {
                        specific_domain_port_app_gid = app->gid;
                    }
                    for (k = 0; k < count; k++) {
                        struct zpn_application_group *group;
                        res = zpn_application_group_get_by_id(local_app_group_ids[k],
                                                              &group,
                                                              callback_f,
                                                              callback_cookie,
                                                              callback_id);
                        if (res == ZPN_RESULT_ASYNCHRONOUS) return res;
                        if (res) {
                            ZPN_LOG(AL_WARNING, "%ld: %s: Could not read application_group %ld, res = %s", (long) customer_gid, app_name, (long) local_app_group_ids[k], zpath_result_string(res));
                        } else {
                            if (group->enabled) {
                                if (grp_count < grp_count_max) zpn_application_simple_array_insert(app_group_gids, &grp_count, local_app_group_ids[k]);
                            }
                        }
                    }
                }
            }
        }
    }

    *app_gids_count = app_count;
    *app_group_gids_count = grp_count;

    // Select the most specific application gid
    if (most_specific_app_gid && (*most_specific_app_gid == 0)) {
        /* If wildcard or pattern only matched apps, use the app which matched (domain + port) */
        if (specific_domain_app_gid == 0) {
            *most_specific_app_gid = app_gids[0];
        } else {
            /* 1. if exact matched domain + port matched, choose that app gid
             * 2. else wildcard matches choose first app gid which matched domain alone.
             */
            *most_specific_app_gid = (specific_domain_port_app_gid != 0) ? specific_domain_port_app_gid : specific_domain_app_gid;
        }
    }

    if (app_count >= RESULTS_ARRAY_SIZE) {
        zpn_application_search_debug_stats_counter(zpn_app_search_none, zpn_multi_match_large_apps);
        ZPN_LOG(AL_WARNING, "%"PRId64": Multi match matched %zu apps", customer_gid, app_count);
    }
    if (app_count) return ZPATH_RESULT_NO_ERROR;

    return ZPATH_RESULT_NOT_FOUND;
}

/* used for application download */
int zpn_inclusive_domains_search(int64_t    scope_gid,
                                 const char *app_name,
                                 char       **domains,       /* declare as struct char *domains[N] */
                                 size_t     *domains_count,  /* must input as N, and reurn actual count */
                                 int        search_pattern_domain)
{

    if(!domains_count) return ZPATH_RESULT_ERR;
    *domains_count = zpn_domain_search_all(scope_gid,
                                           app_name,
                                           strnlen(app_name, MAX_APPLICATION_DOMAIN_LEN),
                                           1,  // only return if the domain is inclusive
                                           NULL,
                                           (void **)domains,
                                           NULL,
                                           *domains_count,
                                           search_pattern_domain);

    return ZPATH_RESULT_NO_ERROR;
}

/*
 * Read Config and return application multi-match feature is enabled for a customer.
 */
static int64_t
is_application_multi_match_enabled_for_customer(int64_t customer_gid) {
    int64_t config_value = 0;

    config_value = zpath_config_override_get_config_int(APPLICATION_MULTI_MATCH_FEATURE,
                                                        &config_value,
                                                        APPLICATION_MULTI_MATCH_FEATURE_DEFAULT,
                                                        // list of gids to check
                                                        customer_gid,
                                                        (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                        (int64_t)0);

    ZPN_DEBUG_APPLICATION("Application multi match config value:%s for customer_id: %"PRId64" is: %"PRId64"",
                  APPLICATION_MULTI_MATCH_FEATURE, customer_gid, config_value);

    return config_value ? 1 : 0;
}

/*
 * Read Config and return app scaling feature is enabled for a customer.
 */
static int64_t
is_app_scaling_feature_enabled_for_customer(int64_t customer_gid) {
   int64_t config_value = 0;

    config_value = zpath_config_override_get_config_int(APP_SCALING_FEATURE,
                                                        &config_value,
                                                        DEFAULT_APP_SCALING_FEATURE,
                                                        // list of gids to check
                                                        customer_gid,
                                                        (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                        (int64_t)0);

    ZPN_DEBUG_APPLICATION("App Scaling config value %s for customer_id: %"PRId64" is: %"PRId64"",
                  APPLICATION_MULTI_MATCH_FEATURE, customer_gid, config_value);

    return config_value ? 1 : 0;
}

/*
 * Return if application multi-match feature is enabled for customer
 */
int64_t
zpn_application_app_multi_match_feature_enabled_get(const struct zpn_customer_application_state *state)
{
    if (is_app_multi_match_feature_hard_disabled()) {
        // Application multi match feature is hard disabled Globally
        return 0;
    }

    if (zpn_app_multi_match_flag_test) return 1;
    return state->app_multi_match_feature_enabled;
}

/*
 * Return if application multi-match feature is enabled for customer by customer gid
 */
int64_t
is_app_multi_match_feature_enabled(int64_t customer_gid)
{
    if (is_app_multi_match_feature_hard_disabled()) {
        // Application multi match feature is hard disabled Globally
        return 0;
    }

    return zpn_application_app_multi_match_feature_enabled_get(state_get_without_lock(customer_gid));
}

/*
 * Return TRUE if application multi-match feature is hard disabled globally
 */
int64_t is_app_multi_match_feature_hard_disabled()
{
    return g_app_multi_match_hard_disabled;
}

static int64_t
read_application_multi_match_hard_disabled_config() {
    int64_t config_value = 0;

    config_value = zpath_config_override_get_config_int(APPLICATION_MULTI_MATCH_FEATURE_HARD_DISABLED,
                                                        &config_value,
                                                        APPLICATION_MULTI_MATCH_FEATURE_HARD_DISABLED_DEFAULT,
                                                        // list of gids to check
                                                        (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                        (int64_t)0);

    return config_value ? 1 : 0;
}

void zpn_app_multi_match_global_config_ovd_init()
{
    g_app_multi_match_hard_disabled = read_application_multi_match_hard_disabled_config();

    zpath_config_override_monitor_int(APPLICATION_MULTI_MATCH_FEATURE_HARD_DISABLED,
                                  &g_app_multi_match_hard_disabled,
                                  zpn_app_multi_match_hard_disable_toggled_cb,
                                  APPLICATION_MULTI_MATCH_FEATURE_HARD_DISABLED_DEFAULT,
                                  (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                  (int64_t)0);
}

void zpn_application_version_update(int64_t customer_gid)
{
    struct zpn_customer_application_state *state = state_get_without_lock(customer_gid);
    if (state)  __sync_fetch_and_add_8(&(state->version), 1);
}

struct zpn_customer_application_state *zpn_application_customer_state_get(int64_t customer_gid)
{
    return state_get_without_lock(customer_gid);
}

int64_t zpn_application_version_get(struct zpn_customer_application_state *state)
{
    return state->version;
}

/* Is this app owned by or shared to the scope
 * For valid owned/shared scope return 1 else 0
 */
int is_app_owned_or_shared(struct zpn_application *app, int64_t *scope_gid, char* attr, int64_t machine_gid, unsigned int is_pra)
{
    int i;

    /* This may only happen in debug code, leave it fail open */
    if (!app) return 1;


    /* app scope is disabled or deleted */
    if (!is_valid_scope(app->scope_gid)) return 0;

    /* apps with default scope can be accessed by any users from customized scope */
    if (app->scope_gid == app->customer_gid) return 1;

    /*
     * For PRA sessions, Scope is determined by the Exporter by looking up
     * the scope of the Portal a client is requesting.
     * We do not want the Scope GID to change based on AuthDomain lookup.
     * So, we will let Scope GID to remain unchanged.
     */
    /* if the client changes its reset scope */
    if ((!is_unit_test() || is_dta_test()) && !is_pra) {
        struct zse_scope_data *scope = NULL;
        if (machine_gid) {
            scope = get_scope_by_machine_gid(machine_gid);
        } else {
            scope = get_scope_by_attr(app->customer_gid, attr);
        }
        if (!scope) {
            if(machine_gid) {
                ZPN_DEBUG_SCOPE("customer:%"PRId64" application:%"PRId64" cannot find scope for machine_gid:%"PRId64". Updating current user scope:%"PRId64" to default scope", app->customer_gid, app->gid, machine_gid, *scope_gid);
            } else {
                ZPN_DEBUG_SCOPE("customer:%"PRId64" application:%"PRId64" cannot find scope for attr %s. Updating current user scope:%"PRId64" to default scope", app->customer_gid, app->gid, attr, *scope_gid);
            }

            // Todo: DTA-FIXME later. No need to make a function call. Just assign customer_gid from app->customer_gid
            *scope_gid = get_customer_from_scope(*scope_gid);
        } else if (scope->enabled && scope->scope_gid != *scope_gid) {
            if(machine_gid) {
                ZPN_DEBUG_SCOPE("customer:%"PRId64" application:%"PRId64" Updating customer current scope_gid:%"PRId64" to new scope_gid:%"PRId64" for machine_gid:%"PRId64"", app->customer_gid, app->gid, (*scope_gid), scope->scope_gid, machine_gid);
            } else {
                ZPN_DEBUG_SCOPE("customer:%"PRId64" application:%"PRId64" Updating customer current scope_gid:%"PRId64" to new scope_gid:%"PRId64" for attr %s", app->customer_gid, app->gid, (*scope_gid), scope->scope_gid, attr);
            }
            *scope_gid = scope->scope_gid;
        } else if(!scope->enabled && *scope_gid != get_customer_from_scope(*scope_gid)) {
            if(machine_gid) {
                ZPN_DEBUG_SCOPE("customer:%"PRId64" application:%"PRId64" Scope is disabled for machine_gid:%"PRId64", updating current scope_gid:%"PRId64" to default scope", app->customer_gid, app->gid, machine_gid, (*scope_gid));
            } else {
                ZPN_DEBUG_SCOPE("customer:%"PRId64" application:%"PRId64" Scope is disabled for attr %s, updating current scope_gid:%"PRId64" to default scope", app->customer_gid, app->gid, attr, (*scope_gid));
            }
            // Todo: DTA-FIXME later. No need to make a function call. Just assign customer_gid from app->customer_gid
            *scope_gid = get_customer_from_scope(*scope_gid);
        }
    }

    /* Is user scope valid ? */
    if (!is_valid_scope(*scope_gid)) return 0;

    /* The app is owned by the user scope */
    if(*scope_gid == app->scope_gid) return 1;

    /* shared app */
    for (i = 0; i < app->shared_scope_ids_count; i++) {
        if (*scope_gid == app->shared_scope_ids[i]) return 1;
    }

    /* shared by app group */
    for (i = 0; i < app->shared_scope_ids_from_app_group_count; i++) {
        if (*scope_gid == app->shared_scope_ids_from_app_group[i]) return 1;
    }

    return 0;
}

/*
 * Return TRUE if application pattern-match feature is hard disabled globally
 */
int64_t is_app_pattern_match_feature_hard_disabled()
{
    return g_app_pattern_match_hard_disabled;
}

static int64_t
read_application_pattern_match_hard_disabled_config() {
    int64_t config_value = 0;

    config_value = zpath_config_override_get_config_int(APPLICATION_PATTERN_MATCH_FEATURE_HARD_DISABLED,
                                                        &config_value,
                                                        APPLICATION_PATTERN_MATCH_FEATURE_HARD_DISABLED_DEFAULT,
                                                        // list of gids to check
                                                        (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                        (int64_t)0);

    return config_value ? 1 : 0;
}

void zpn_app_pattern_match_global_config_ovd_init()
{
    g_app_pattern_match_hard_disabled = read_application_pattern_match_hard_disabled_config();

    zpath_config_override_monitor_int(APPLICATION_PATTERN_MATCH_FEATURE_HARD_DISABLED,
                                  &g_app_pattern_match_hard_disabled,
                                  NULL,
                                  APPLICATION_PATTERN_MATCH_FEATURE_HARD_DISABLED_DEFAULT,
                                  (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                  (int64_t)0);
}

/*
 * Read Config and return application pattern-match feature is enabled for a customer.
 */
static int64_t
is_application_pattern_match_enabled_for_customer(int64_t customer_gid) {
    int64_t config_value = 0;

    config_value = zpath_config_override_get_config_int(APPLICATION_PATTERN_MATCH_FEATURE,
                                                        &config_value,
                                                        APPLICATION_PATTERN_MATCH_FEATURE_DEFAULT,
                                                        // list of gids to check
                                                        customer_gid,
                                                        (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                        (int64_t)0);

    ZPN_DEBUG_APPLICATION("Application pattern match config value %s for customer_id: %"PRId64" is: %"PRId64"",
                  APPLICATION_PATTERN_MATCH_FEATURE, customer_gid, config_value);

    return config_value;
}

/*
 * Read Config and return application pattern-match feature is enabled for a customer.
 */
static int64_t
is_application_enqueue_dequeue_fix_enabled_for_customer(int64_t customer_gid) {
    int64_t config_value = 0;

    config_value = zpath_config_override_get_config_int(CONFIG_FEATURE_ENQUEUE_DEQUEUE_FIX,
                                                        &config_value,
                                                        CONFIG_FEATURE_ENQUEUE_DEQUEUE_FIX_DEFAULT,
                                                        // list of gids to check
                                                        customer_gid,
                                                        (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                        (int64_t)0);

    ZPN_DEBUG_APPLICATION("Application enqueue dequeue fix config value %s for customer_id: %"PRId64" is: %"PRId64"",
                  CONFIG_FEATURE_ENQUEUE_DEQUEUE_FIX, customer_gid, config_value);

    return config_value;
}
/*
 * Return if application pattern-match feature is enabled for customer
 */
int64_t
zpn_application_app_pattern_match_config_value_get(const struct zpn_customer_application_state *state)
{
    if (is_app_pattern_match_feature_hard_disabled()) {
        // Application pattern match feature is hard disabled Globally
        return 0;
    }

    //if (zpn_app_pattern_match_flag_test) return 1;
    return state->app_pattern_match_feature_enabled;
}

/*
 * Return true if application pattern-match feature is enabled for customer
 */
int64_t
is_zpn_application_app_pattern_match_feature_enabled(const struct zpn_customer_application_state *state)
{
    int64_t is_feature_enabled = 0;
    int64_t config_value = 0;

    config_value = zpn_application_app_pattern_match_config_value_get(state);

    if (config_value == APPLICATION_PATTERN_MATCH_FEATURE_ENABLE) {
        is_feature_enabled = 1;
    }

    return is_feature_enabled;
}

/*
 * Return if application pattern-match feature is enabled for customer by customer gid
 */
int64_t
is_app_pattern_match_feature_enabled(int64_t customer_gid)
{
    if (is_app_pattern_match_feature_hard_disabled()) {
        // Application pattern match feature is hard disabled Globally
        return 0;
    }

    return is_pattern_match_feature_enabled(state_get_without_lock(customer_gid));
}

int zpn_add_app_multi_match_callback_funcs(int64_t customer_gid, void *app_scaling_cb, void *app_download_cb)
{
    int res = ZPATH_RESULT_NO_ERROR;

    res = create_multi_match_toggle_event_base();
    if (res) {
        ZPN_LOG(AL_ERROR, "MULTI-MATCH, Failed to create thread %s, error: %s", APP_MULTI_MATCH_TOGGLE_THREAD, zpn_result_string(res));
        return res;
    }

    struct zpn_customer_application_state *state = state_get_without_lock(customer_gid);
    if (!state) {
        ZPN_LOG(AL_CRITICAL, "MULTI-MATCH, Failed to get state");
        res = ZPATH_RESULT_ERR;
    } else {
        if (!state->multi_match_for_app_scaling_cb && app_scaling_cb) {
            ZPN_LOG(AL_NOTICE, "MULTI-MATCH,  add multi_match_for_app_scaling_cb for customer %"PRId64, customer_gid);
            state->multi_match_for_app_scaling_cb = app_scaling_cb;
        }
        if (!state->multi_match_for_app_download_cb && app_download_cb)  {
            ZPN_LOG(AL_NOTICE, "MULTI-MATCH,  add multi_match_for_app_download_cb for customer %"PRId64, customer_gid);
            state->multi_match_for_app_download_cb = app_download_cb;
        }
    }
    return res;
}

inline int is_app_multi_match_search(int64_t customer_gid, enum zpn_client_type client_type) {
      return (is_app_multi_match_feature_enabled(customer_gid) && zpn_client_static_config[client_type].multi_match);
}

static int zpn_app_multi_match_status(struct zpath_debug_state *request_state,
                                  const char **query_values,
                                  int query_value_count,
                                  void *cookie)
{
    int64_t customer_gid = 0;
    const char *customer_str = query_values[0];
    struct zpn_customer_application_state *state = NULL;

    if (!customer_str) {
        ZDP("Require argument customer (customer_gid)");
        return ZPATH_RESULT_ERR;
    }

    customer_gid = strtoul(customer_str, NULL, 0);
    if (customer_gid == 0) {
        ZDP("Invalid customer_gid: %"PRId64, customer_gid);
        return ZPATH_RESULT_ERR;
    }

    if (customers_applications) {
        state = zhash_table_lookup(customers_applications,
                                   &customer_gid,
                                   sizeof(customer_gid),
                                   NULL);
    }
    if (!state) {
        ZDP("Customer with gid %"PRId64" does not exists!\n", customer_gid);
        return ZPATH_RESULT_NO_ERROR;
    }

    int64_t is_enabled = is_app_multi_match_feature_enabled(customer_gid);
    int64_t is_hard_disabled = is_app_multi_match_feature_hard_disabled();
    int64_t is_feature_flag_enabled = state->app_multi_match_feature_enabled;
    int64_t is_feature_flag_configued = is_app_multi_match_feature_configured(customer_gid);
    ZDP("Muti-Match is %s for customer %"PRId64" with value: %"PRId64"\n", ((is_enabled == 0)?"disabled":"enabled"), customer_gid, is_enabled);
    ZDP("Muti-Match %s hard disabled for all customers with value: %"PRId64"\n",  is_hard_disabled? "is" : "is not", is_hard_disabled);
    ZDP("Muti-Match feature flag is %s for customer %"PRId64" with value: %"PRId64"\n", ((is_feature_flag_enabled == 0)?"disabled":"enabled"), customer_gid, is_feature_flag_enabled);
    ZDP("Muti-Match feature flag is %s for customer %"PRId64" with value: %"PRId64"\n", ((is_feature_flag_configued == 0)?"not configured":"configured"), customer_gid, is_feature_flag_configued);

    ZDP("\n");
    return ZPATH_RESULT_NO_ERROR;
}

static int zpn_app_pattern_match_status(struct zpath_debug_state *request_state,
                                  const char **query_values,
                                  int query_value_count,
                                  void *cookie)
{
    int64_t customer_gid = 0;
    const char *customer_str = query_values[0];
    struct zpn_customer_application_state *state = NULL;

    if (!customer_str) {
        ZDP("Require argument customer (customer_gid)");
        return ZPATH_RESULT_ERR;
    }

    customer_gid = strtoul(customer_str, NULL, 0);
    if (customer_gid == 0) {
        ZDP("Invalid customer_gid: %"PRId64, customer_gid);
        return ZPATH_RESULT_ERR;
    }

    if (customers_applications) {
        state = zhash_table_lookup(customers_applications,
                                   &customer_gid,
                                   sizeof(customer_gid),
                                   NULL);
    }

    if (!state) {
        ZDP("Customer with gid %"PRId64" does not exist!\n", customer_gid);
        return ZPATH_RESULT_NO_ERROR;
    }

    int64_t is_enabled = is_pattern_match_feature_enabled(state);
    int64_t is_hard_disabled = is_app_pattern_match_feature_hard_disabled();
    int64_t is_feature_flag_enabled = state->app_pattern_match_feature_enabled;
    int64_t is_app_multi_match_enabled = state->app_multi_match_feature_enabled;
    int64_t is_app_multi_match_hard_disabled = is_app_multi_match_feature_hard_disabled();
    int64_t is_app_scaling_enabled = state->app_scaling_feature_enabled;

    ZDP("App Pattern Match %s hard disabled for all customers with value: %"PRId64"\n",  is_hard_disabled? "is" : "is not", is_hard_disabled);
    ZDP("App Muti Match %s hard disabled for all customers with value: %"PRId64"\n",  is_app_multi_match_hard_disabled? "is" : "is not", is_app_multi_match_hard_disabled);

    if (state->app_scaling_registered) {
        ZDP("App Scaling is %s for customer %"PRId64" with value: %"PRId64"\n", ((is_app_scaling_enabled == 0) ? "disabled" : "enabled"), customer_gid, is_app_scaling_enabled);
        if (state->registered) ZDP("App Pattern Match is %s for customer %"PRId64" with value: %"PRId64"\n", ((is_enabled == 0) ? "disabled" : "enabled"), customer_gid, is_enabled);
    } else {
        ZDP("App Scaling feature is not registerd for customer %"PRId64"\n", customer_gid);
    }

    if (state->registered) {
        ZDP("App Pattern Match feature flag is %s for customer %"PRId64" with value: %"PRId64"\n", ((is_feature_flag_enabled == 1) ? "enabled" : ((is_feature_flag_enabled == 0) ? "disabled" : "fully-disabled")), customer_gid, is_feature_flag_enabled);
        ZDP("App Multi Match is %s for customer %"PRId64" with value: %"PRId64"\n", ((is_app_multi_match_enabled == 0) ? "disabled" : "enabled"), customer_gid, is_app_multi_match_enabled);
    } else {
        ZDP("App Multi Match and App Pattern Match are not registerd for customer %"PRId64"\n", customer_gid);
    }

    ZDP("\n");
    return ZPATH_RESULT_NO_ERROR;
}

static int zpn_app_match_sipa_only_apps_for_sipa_client_status(struct zpath_debug_state *request_state,
                                                               const char **query_values,
                                                               int query_value_count,
                                                               void *cookie)
{
    int64_t customer_gid = 0;
    const char *customer_str = query_values[0];

    if (!customer_str) {
        ZDP("Require argument customer (customer_gid)");
        return ZPATH_RESULT_ERR;
    }

    customer_gid = strtoul(customer_str, NULL, 0);
    if (customer_gid == 0) {
        ZDP("Invalid customer_gid: %"PRId64, customer_gid);
        return ZPATH_RESULT_ERR;
    }

    ZDP("Application Match SIPA Only Apps for SIPA Client feature flag is %s\n",
        g_app_match_sipa_only_apps_for_sipa_client_hard_disabled_feature_status ? "hard disabled" : "not hard disabled");
    ZDP("Application Match SIPA Only Apps for SIPA Client feature flag is %s for customer %"PRId64"\n",
        zpn_application_match_sipa_only_apps_for_sipa_client_enabled(customer_gid) == 0 ? "disabled" : "enabled", customer_gid);

    return ZPATH_RESULT_NO_ERROR;
}
