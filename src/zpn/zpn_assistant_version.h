/*
 * zpn_assistant_version_table.h. Copyright (C) 2016 Zscaler Inc, All Rights Reserved
 */

#ifndef _ZPN_ASSISTANT_VERSION_TABLE_H_
#define _ZPN_ASSISTANT_VERSION_TABLE_H_

#include "argo/argo.h"
#include "wally/wally.h"

struct zpn_assistant_version                   /* _ARGO: object_definition */
{
    /* Unique ID for this assistant. */
    int64_t gid;                               /* _ARGO: integer, index, key */

    /* Standard SQL fields. */
    int64_t sequence;                          /* _ARGO: integer, sequence */
    int8_t deleted;                            /* _ARGO: integer, deleted */
    int32_t modified_time;                     /* _ARGO: integer */
    int32_t creation_time;                     /* _ARGO: integer */
    int64_t modifiedby_userid;                 /* _ARGO: integer */
    int64_t request_id;                        /* _ARGO: integer, nodb, reqid */

    int64_t assistant_group_gid;               /* _ARGO: integer */
    /*
     * Set by AUM. OPS also can set it if we are to install a debug image in the field. At that time OPS should set
     * disable_auto_update_expected_version field also.
     */
    int64_t customer_gid;                      /* _ARGO: integer, index */
    char *expected_version;                    /* _ARGO: string */
    char *current_version;                     /* _ARGO: string */
    int64_t system_start_time;                 /* _ARGO: integer */
    int64_t application_start_time;            /* _ARGO: integer */
    int64_t last_connect_time;                 /* _ARGO: integer */
    int64_t last_disconnect_time;              /* _ARGO: integer */
    char *platform;                            /* _ARGO: string */
    int16_t upgrade_status;                    /* _ARGO: integer */
    int16_t ctrl_channel_status;               /* _ARGO: integer */
    int64_t broker_id;                         /* _ARGO: integer */
    int64_t next_restart_time;                 /* _ARGO: integer */
    double latitude;                           /* _ARGO: double */
    double longitude;                          /* _ARGO: double */
    struct argo_inet private_ip;               /* _ARGO: inet */
    struct argo_inet public_ip;                /* _ARGO: inet */
    char *mtunnel_id;                          /* _ARGO: string */
    char *previous_version;                    /* _ARGO: string */
    int64_t last_upgraded_time;                /* _ARGO: integer */
    int lone_warrior;                          /* _ARGO: integer */
    /*
     * Set by OPS when we feel AUM shouldn't care about this connector. Typically connector upgrade profile is the
     * one which controls the connector group, but it is per connector group. This field will give control to disable
     * upgrade of one connector. Could be useful in cases, where we manually set the expected version field for
     * debugging an issue or like.
     */
    int disable_auto_update_expected_version;  /* _ARGO: integer */
    char *expected_sarge_version;              /* _ARGO: string */
    int os_upgrade_enabled;                    /* _ARGO: integer */

    char *expected_frr_version;                /* _ARGO: string */
};

extern struct argo_structure_description *zpn_assistant_version_description;

/* Assistant_wally should only be specified if table access is to be
 * performed from an assistant */
int zpn_assistant_version_init(struct wally *assistant_wally, int64_t assistant_id, int fully_load, int register_with_zpath_table);


int
zpn_assistant_version_get_by_id(int64_t assistant_id,
                                struct zpn_assistant_version **assistant_version,
                                wally_response_callback_f callback_f,
                                void *callback_cookie,
                                int64_t callback_id);

void argo_zpn_assistant_version_init();

#endif /* _ZPN_ASSISTANT_VERSION_TABLE_H_ */
