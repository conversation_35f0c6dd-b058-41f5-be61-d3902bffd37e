/*
 * zpn_balance_redirect.h. Copyright (C) 2022 Zscaler, Inc. All Rights Reserved.
 */

#ifndef _ZPN_BALANCE_REDIRECT_
#define _ZPN_BALANCE_REDIRECT_

#include "zpath_lib/zpath_debug.h"
#include "zpath_lib/zpath_geoip_common.h"
#include "zpn/zpn_lib.h"
#include "zpn/zpn_policy_engine.h"
#include "zpn/zpn_broker_common.h"
#include "zpn/zpn_private_broker_load_table.h"

/*
 * This file contains external APIs to access ZPN's load balancing functionalities.
 *
 * A DC is a set of viable instances with the same lat/lon.
 * Viable instances is a configured forwarding instance matching up the DC load criteria.
 *
 * Out of viable reported instances, three types (buckets) of DCs are prepared.
 *  1. DCs of Privately Accessible Private Brokers. This is specific to clients
 *     wishing to redirect to broker instances, does not apply to other instance types.
 *  2. DCs of Publically Accessible Private Brokers. This is specific to clients
 *     wishing to redirect to broker instances, does not apply to other instance types.
 *  3. DCs of Public instances.
 *
 * At high level, the process of picking best-fit instances is consisted of the following phases:
 *  1. Load all instances that this customer has access to. (zpn_balance_inst_load.c)
 *  2. Apply a set of algorithms to filter out instances. (zpn_balance_inst_filter.c)
 *  3. Group instances into buckets and DCs. (zpn_balance_dc_bucket.c)
 *  4. For each bucket, apply a set of algos to pick the most suitable DCs. (zpn_balance_dc_select.c)
 *  5. For each DC, apply a set of algorithms to pick the most suitable instances. (zpn_balance_inst_select.c)
 *  6. Reconsile the instances picked from the above process across DCs and buckets. (zpn_balance_compact.c)
 *
 * At the end we have between 0 and 6 instances, which will be returned as the best redirect targets.
 */


/*
 * BALANCE_ROLE_*: balance role is an indication of whether or not the
 *    broker sends redirect messages to the client.
 *
 * BALANE_MODE_*: balance mode is an indication of whether or not the
 *    specific broker can be a target of a redirect.
 *
 * BALANCE_ROLE_REDIRECT:
 *    The broker does redirect.
 * BALANCE_ROLE_NO_REDIRECT:
 *    The broker never sends redirect.
 * BALANCE_ROLE_FORWARD:
 *    Only sends redirect on client connect if last redirect was more
 *    than NEXT_BALANCE_INTERVAL_S ago.
 * BALANCE_ROLE_UNCONFIGURED: (Please do not use)
 *    The broker never sends redirect.
 *
 * BALANCE_MODE_UNCONFIGURED:
 *    The broker will not be redirected to. (Please do not use)
 * BALANCE_MODE_MAINTENANCE:
 *    The broker will not be redirected to.
 *    When in maintenance mode, if the broker is also in role_forward
 *    or role_redirect, then the broker will continuously send to all
 *    connected clients redirect messages
 * BALANCE_MODE_BALANCE:
 *    The broker will be redirected to.
 *
 */
#define BALANCE_ROLE_UNCONFIGURED     "UNCONFIGURED"
#define BALANCE_ROLE_REDIRECT         "REDIRECT"
#define BALANCE_ROLE_NO_REDIRECT      "NOREDIRECT"
#define BALANCE_ROLE_FORWARD          "FORWARD"

#define BALANCE_MODE_UNCONFIGURED     "UNCONFIGURED"
#define BALANCE_MODE_MAINTENANCE      "MAINTENANCE"
#define BALANCE_MODE_BALANCE          "BALANCE"

/*
 * ZPN clients should use this macro to decide when to do redirect.
 * No more than 1 redirect per 5 minutes on first connect
 */
#define NEXT_BALANCE_INTERVAL_S       (5*60)
#define BROKER_LOAD_CEILING         (100)
#define MAX_PSE_GROUPS              (100)
#define DC_LOAD_CEILING             (100)

enum zpn_balance_instance_type {
    PRIVATE_BROKER,
    PUBLIC_PRIVATE_BROKER,
    PUBLIC_BROKER,

    /* Add type before this line */
    INVALID_INSTANCE_TYPE
#define NUM_OF_INSTANCE_TYPES (INVALID_INSTANCE_TYPE)
};

enum zpn_balance_redirect_source {
    REDIRECT_SOURCE_BROKER = 0,
    REDIRECT_SOURCE_SITEC
};

/* type - 0: private_mtn, 1: private, 2: public_private_mtn, 3: public_private, 4: public_primary, 5: public_secondary */
enum zpn_redirect_broker_type {
    ZPN_PRIVATE_MTN = 0,
    ZPN_PRIVATE,
    ZPN_PUBLIC_PRIVATE_MTN,
    ZPN_PUBLIC_PRIVATE,
    ZPN_PUBLIC_PRIMARY,
    ZPN_PUBLIC_SECONDARY,
    /* Add type before this line */
    INVALID_REDIRECT_BROKER_TYPE
};

enum geo_fence_state {
    geo_none = 0, /* no filter but limit broker selection to the same country as the client whenever possible */
    geo_include,  /* only select brokers from countries in geofenced_cc_lst */
    geo_exclude,  /* do not select brokers from countries in geofenced_cc_lst */
};

enum lp_inst_type {
    inst_none = 0,
    inst_partitioned,
    inst_unpartioned,
    inst_mixup,
};

/* This structure defines which type of broker should be included in the returned redirected brokers */
// TODO: change to pass the client type instead and turn this into an internal behavior.
struct zpn_balance_redirect_instance_types {
    int type[NUM_OF_INSTANCE_TYPES];
};

struct zpn_broker_redirect_policy_state {
    enum zpe_access_action      redirect_action;
    int64_t                     rule_matched;
    int                         no_redirect_pse_target;
    int                         is_fallback_broker_considered;
    int                         gid_count;
    struct                      zhash_table *policy_result_htable;
    int                         inst_match_found;
};

struct zpn_balance_policy_req{
    /* input */
    int                     is_client_capable;
    int                     is_cc_enabled;
    int                     is_saml_enabled;
    int                     is_scim_enabled;
    int                     is_fallback_broker_enabled;
    struct zhash_table      *general_context_hash;
    struct zhash_table      *saml_hash;
    struct zhash_table      *scim_hash;
    struct zhash_table      *int_hash;

    /* output */
    struct zpn_broker_redirect_policy_state policy_result;
};

struct zpn_balance_feature_flag{
    int         feature_grace_distance;
    int         feature_policy_redirect;
    int         feature_policy_redirect_hard_disabled;
};

/* This structure is input to execution of load balancing functionality (zpn_balance_redirect).
 *
 * Input:
 *  - lat, lon, cc: if not provided, location of this system will be used for the redirect.
 *  - trusted_networks: refer to zpn_broker_client_fohh_state.trusted_networks
 *  - rbt: types of instances that the client wants
 *  - multi_dc_redir_capable: does the client want multi-dc redirect for public brokers?
 *  - zpn_balance_feature_flag: Enable/disable flags to control feature during redirection.
 *
 *  control of debugging
 *  - log_tag: an optional tag that will be logged (in event logs) on every entry that this
 *    module logs. (e.g. identification of a connection).
 *
 * in/out: IMPORTANT: for historical reason, assuming brokers has enough slots. TODO:fix
 *  - brokers - the FQDN/IP of selected instances
 *  - broker_count - number of entries in brokers
 */
struct zpn_balance_redirect_request {
    /* input */
    enum zpn_client_type    client_type;
    int                     multi_dc_redir_capable;
    int64_t                 is_lp_enabled;
    int64_t                 mdc_sdc_mixup_inst;
    int                     alt_cloud_supported;
    int                     ipv6_redirect_enabled;
    int                     ipv6_redirect_capable;
    int                     ipv6_redirect_supported;
    int                     ipv6_exclusive_wanted;
    int                     alt_cloud_aware;
    int                     exclude_self;
    int64_t                 filter_by_site_gid;
    int                     lbb_enabled;        /* lbb enabled for customer */
    int                     lbb_supported;      /* lbb suppported on broker */
    int64_t                 customer_gid;
    int64_t                 partition_gid;
    int64_t                 scope_gid;
    struct                  argo_object *trusted_networks;
    struct argo_inet        *remote_ip;
    double                  remote_lat;
    double                  remote_lon;
    char                    remote_cc[CC_BUF_LEN];
    struct                  zpn_balance_redirect_instance_types *rbt;
    const char              *log_tag;
    int16_t                 do_stats;
    struct zpn_balance_feature_flag flag;
    struct zpn_balance_policy_req *policy_req;
    /* output */
    int                     *broker_count;      /* number of brokers selected */
    char                    **brokers;          /* name of brokers selected */
    int                     *sni_suffix_count;
    char                    **sni_suffix;
    int                     *brokers_type;      /* type of brokers selected, refer to zpn_redirect_broker_type enum */
    struct zpn_instance_info *instance_info;    /* brokers instance info */
    int *instance_info_count;                   /* count for instance_info */
    char                    brk_prefix[10];
};

int is_geofenced(const char *cc);

/* MAIN API TO ACCESS ZPN LOAD BALANCING FUNCTIONALITY */
int zpn_balance_redirect(struct zpn_balance_redirect_request* req);

/**
 * Note: following APIs are helpers for backward compatibility
 * Will become obsolete some day. Preference is for caller to provide the
 * zpn_balance_redirect_request structure so that we don't have to explictly name
 * new paramters in the parameter list.
 */
/*
 * Caller supply redirect message structure
 *
 * The device connected provides its remote_lat, remote_lon,
 * remote_cc. If these are not available, pass 0 and NULL, in which
 * case the location of this system will be used as a basis for the
 * redirect.
 *
 * dbg_msg is any text that wishes to be logged in debug logs. It
 * often contains the remote_ip in text form.
 */
int zpn_broker_balance_redirect(int64_t customer_gid,
                                int64_t scope_gid,
                                struct argo_object *trusted_networks,
                                struct argo_inet *remote_ip,
                                double remote_lat,
                                double remote_lon,
                                char *remote_cc,
                                struct zpn_balance_policy_req *policy_req,
                                int *broker_count,
                                char **brokers,
                                int *brokers_type,
                                struct zpn_instance_info *instance_info,
                                int *instance_info_count,
                                int *sni_suffix_count,
                                char **sni_suffixes,
                                struct zpn_balance_redirect_instance_types *rbt,
                                enum zpn_client_type ctype,
                                int multi_dc,
                                int alt_cloud_aware,
                                int ipv6_redirect_supported,
                                int exclude_self,
                                int64_t filter_by_site_gid,
                                const char *dbg_msg);
/*
 * Check if the given fohh conn needs to be redirected.
 */
int zpn_broker_balance_is_conn_redirect_needed(struct fohh_connection *f_conn,
                                               int force_redirect,
                                               int *balance_timeout/*Accepts NULL*/);
/*
 * Send a redirect message to peer of the given fohh conn.
 */
void zpn_broker_balance_conn_redirect(struct fohh_connection *f_conn,
                                      int64_t customer_id,
                                      int64_t scope_id,
                                      struct argo_inet *peer_ip,
                                      struct site *peer_site,
                                      const char *tunnel_id,
                                      int force_redirect,
                                      const char *reason,
                                      int check_balance_cfg,
                                      int *is_redirect_to_alt_cloud,
                                      struct zpn_balance_redirect_instance_types *rbt,
                                      struct zpn_balance_policy_req *policy_req,
                                      enum zpn_client_type client_type,
                                      int exclude_self);

int zpn_balance_redirect_init(struct wally *wally,
                            struct wally_origin *slave_db,
                            struct wally_origin *remote_db,
                            int64_t current_instance_gid);

/* db/wally notification for broker/pbroker load updates */
extern int zpn_balance_inst_load_update_broker(int64_t gid,
                                        int8_t deleted,
                                        char *name,
                                        uint32_t cpu_util,
                                        uint32_t mem_util,
                                        uint64_t clients,
                                        uint32_t load_skew,
                                        char *role,
                                        char *mode,
                                        int32_t modified_time,
                                        uint16_t status,
                                        uint16_t proc_fd_util);
extern int zpn_balance_inst_load_update_pbroker(int64_t gid,
                                        int8_t deleted,
                                        char **publish_ips,
                                        int publish_ips_count,
                                        uint32_t cpu_util,
                                        uint32_t mem_util,
                                        uint64_t clients,
                                        int32_t modified_time);

/*
 * Return redirect policy feature flag value.
 */
int zpn_balance_get_redirect_policy_feature_status(int64_t customer_gid, int64_t instance_gid, struct zpn_balance_redirect_request *req);

/*
 * Return redirect policy scim criteria feature flag value.
 */
int zpn_balance_get_redirect_policy_scim_status(int64_t customer_gid, int64_t instance_gid);

/*
 * Represent balancing config for the current instance.
 * Data comes from zpn_broker_balance_control table, or
 * config overrides.
 */
#define ZPN_BALANCE_ROLE_MODE_LEN   24
#define ZPN_BALANCE_INST_NAME_LEN   (ZPN_MAX_INSTANCE_NAME_LEN)
struct zpn_balance_instance_info {
    enum Zpn_Instance_Type type;
    int64_t gid;
    int8_t deleted;
    char name[ZPN_BALANCE_INST_NAME_LEN+1];
    char balance_role[ZPN_BALANCE_ROLE_MODE_LEN+1];
    char balance_mode[ZPN_BALANCE_ROLE_MODE_LEN+1];
    double lon;
    double lat;
    enum zpn_broker_status brk_status;
    int8_t flag;     // used by ops/maintenance commands
};

/*
 * zpn_balance_instances_in_dc returns info on all brokers from a DC.
 * DC is determined by lon/lat.
 * Params: brokers and count are of [in/out] type.
 * Returns: error code
 *
 * zpn_balance_instances_in_my_dc is a shortcut returning all brokers
 * from the same DC as the current broker.
 */
int zpn_balance_instances_in_dc(double lon, double lat, struct zpn_balance_instance_info* brokers, int* count);
int zpn_balance_instances_in_my_dc(struct zpn_balance_instance_info* brokers, int* count);


/*
 * Global configuration for the current running instance.
 * (currently only public broker, including rbrokers, has this configuration)
 */
struct zpn_balance_config {
    /****************************************
     * from config overrides
     ****************************************/
    /* If load report time is older than this limit, reduce its load by 50%. */
    int64_t     report_age_reduction_s;
    /* Do not consider a broker that hasn't reported for this long. */
    int64_t     report_age_exclusion_s;
    /* percentage of redirect requests that should be using the multi-dc-redirect logic.
     * if 100, multi-dc-redirect feature is fully enabled;
     * if 0, multi-dc-redirect feature is disabled */
    int64_t     mdc_pub_pct;
    /* multi-dc-redirect: if primary dc load is higer than this limit:
     * choose at least 2 secondary DCs and all 2 brokers are selected from secondary dcs.
     * the _gf value is used if the DC is in a geofenced country*/
    int64_t     mdc_max_pdc_load;
    int64_t     mdc_max_gf_pdc_load;
    /* multi-dc-redirect: if a candidate dc load is higher than this limit,
     * it's not taken as a secondary dc .
     * the _gf value is used if the DC is in a geofenced country*/
    int64_t     mdc_max_sdc_load;
    int64_t     mdc_max_gf_sdc_load;
    /* multi-dc-redirect: max number of secondary DCs
    * range: [2 - unlimited] */
    int64_t     mdc_max_sdc_count;
    /* multi-dc-redirect: max distance (in miles) of a secondary DC from primary
    * range: [100 - unlimited] */
    int64_t     mdc_max_sdc_dist_miles;
    /* multi-dc-redirect: collect enough secondary dcs such that collectively they reach
     * a percentage of the primary dc's capacity.*/
    int64_t     mdc_sdc_cap_target_pct;
    /* If set to 1, never try to redirect user out of geofenced country.
     * Let redirect fail instead. */
    int64_t     geofencing_enforced;

    /***************************************
     * from zpn_broker_balance_control table
     ***************************************/
    int64_t     gid;
    uint32_t    restart_time;
    uint32_t    load_skew;
    /* this applies to private broker redirect now.
     * redirect to public brokers now controlled by config-override. */
    int         single_dc_redir_priv;
    char        instance_name[ZPN_BALANCE_INST_NAME_LEN+1];
    char        balance_role[ZPN_BALANCE_ROLE_MODE_LEN+1];
    char        balance_mode[ZPN_BALANCE_ROLE_MODE_LEN+1];
    /* indicates if balance_role is REDIRECT */
    int         is_role_redirect;

    /***************************************
     * dynamic configuration
     ***************************************/
    // initial and growth sizes for viable broker list.
    // default is hard-coded, can be changed via hidden debug commands
    size_t      inst_list_alloc;
    size_t      inst_list_growth;
};
extern struct zpn_balance_config g_balance_config;

#define ZPN_BROKER_IS_RBROKER() (g_balance_config.is_role_redirect)

int zpn_broker_is_balance_mode_maintainence();

int64_t zpn_pbroker_multidc_config_override(int64_t customer_id);

int64_t zpn_assistant_multidc_config_override(int64_t customer_id);

int64_t zpn_sitec_multidc_config_override(int64_t customer_id);

struct zpn_balance_stats;
void zpn_balance_redirect_get_stats(struct zpn_balance_stats *out_stats);
uint32_t compute_effective_sitec_load(uint32_t mem_util, uint32_t cpu_util, uint32_t fd_util);
int zpn_balance_set_pse_grp_exclusive_for_business_continuity_flag(struct zpath_debug_state *request_state, const char **query_values, int query_value_count, void *object);
int zpn_balance_set_pse_grp_exclusive_for_business_continuity_flag_do(struct zpath_debug_state *request_state, const char **query_values, int query_value_count, void *object, int is_ut);
#endif //_ZPN_BALANCE_REDIRECT_
