
/*
 * zpn_balance_debug.c. Copyright (C) 2022 Zscaler, Inc. All Rights Reserved.
 */

#include "zpn/zbalance/zpn_balance_private.h"
#include "zpn/zpn_scope_ready.h"
#include "zpn/zpn_rule.h"
#include "zpn/zpn_rule_to_pse_group.h"
#include "zpn/zbalance/zpn_balance_redirect.h"
#include "zpn/zpn_broker.h"
#include "zpn/zpn_pbroker_group.h"
#include "zpath_lib/zpath_partition.h"
#include "zpn/zpn_broker_assert.h"

/* format consistently used in debug output */
#define FMT_FLOAT "9.2f"
#define FMT_COORDINATE "+7.2f"
#define FMT_EXCLUSION "%-20s"
#define FMT_DC_NAME "%-30s"

#define DEBUG_ZDP(format...) \
    do { \
        if (request_state)  zpath_debug_cb_printf_response(request_state, ##format); \
        else                fprintf(stderr, ##format); \
    } while (0)

enum instance_type {
    INSTANCE_NONE = 0,
    INSTANCE_PARTITIONED,
    INSTANCE_UNPARTITIONED,
    INSTANCE_TYPE_LAST
};

extern int64_t g_current_profile_version;

/*
 * For showing "exclusion reason code" to UI (i.e. debug output)
 */
static const char *excl_reason_strs[excl_total_count] = {
    [excl_none] =                           "",
    [excl_report_time] =                    "report_time",
    [excl_report_median] =                  "report_median",
    [excl_balance_role] =                   "balance_role",
    [excl_balance_mode] =                   "balance_mode",
    [excl_req_inst_type] =                  "req_inst_type",
    [excl_req_inst_role] =                  "req_inst_role",
    [excl_priv_mtn_mismatch] =              "priv_mtn_mismatch",
    [excl_inst_partition] =                 "excl_inst_partition",
    [excl_part_inst_load] =                 "inst_partition_load",
    [excl_unpart_inst_load] =               "inst_unpartition_load",
    [excl_inst_load] =                      "inst_load",
    [excl_country] =                        "country",
    [excl_geofencing] =                     "geofencing",
    [excl_dcs_per_bucket] =                 "dcs_per_bucket",
    [excl_inst_per_bucket] =                "inst_per_bucket",
    [excl_secondary_dc_count] =             "secondary_dc_count",
    [excl_secondary_dc_dist] =              "secondary_dc_dist",
    [excl_secondary_dc_cap] =               "secondary_dc_cap",
    [excl_primary_dc_load] =                "primary_dc_load",
    [excl_secondary_dc_load] =              "secondary_dc_load",
    [excl_secondary_dc_inst] =              "secondary_dc_inst",
    [excl_selected] =                       "selected",
    [excl_bucket_mtn] =                     "bucket_mtn",
    [excl_distance] =                       "distance",
    [excl_scope_mismatch] =                 "scope_mismatch",
    [excl_scope_fetch] =                    "scope_fetch",
    [excl_scope_disabled] =                 "scope_disabled",
    [excl_policy_mismatch] =                "excl_policy_mismatch",
    [excl_policy_action] =                  "excl_policy_action",
    [excl_state_not_running] =              "state_not_running",
    [excl_alt_cloud] =                      "alt_cloud_set",
    [excl_no_alt_cloud] =                   "alt_cloud_not_set",
    [excl_alt_cloud_invalid] =              "alt_cloud_invalid",
    [excl_self] =                           "exclude_self",
    [excl_site_gid_mismatch] =              "excl_site_gid_mismatch",
    [excl_ipv6_exclusive]    =              "exclude_ipv6_exclusive",
    [excl_inst_exclusive_for_business_continuity] = "exclude_inst_exclusive_for_business_continuity",
};
const char *zpn_balance_excl_str(enum excl_code code) {
    ZPN_BROKER_ASSERT_HARD(code < excl_total_count && excl_reason_strs[code], "Unknown exclusion reason code!");    /* catch missing values in the static array above. */
    return excl_reason_strs[code];
}

/*
 * For showing "selection method code" to UI (i.e. debug output)
 */
static const char *selection_method_strs[zpn_balance_selection_method_count] = {
        [zpn_balance_selection_method_unknown] = "UNKNOWN",
        [zpn_balance_selection_method_metrics] = "METRICS",
        [zpn_balance_selection_method_client_prediction] = "CLIENT_PREDICTION"
};
const char *zpn_balance_selection_method_str(enum zpn_balance_instance_selection_method code) {
    ZPN_BROKER_ASSERT_HARD(code < zpn_balance_selection_method_count && selection_method_strs[code], "Unknown selection method code!");    /* catch missing values in the static array above. */
    return selection_method_strs[code];
}

/*
 * client type and client type input string conversion.
 */
static const char *ct_strings[zpn_client_type_total_count] =  {
    [zpn_client_type_invalid] =             "invalid",
    [zpn_client_type_zapp] =                "zapp",
    [zpn_client_type_slogger] =             "slogger",
    [zpn_client_type_exporter] =            "exporter",
    [zpn_client_type_edge_connector] =      "ec",
    [zpn_client_type_private_broker] =      "pbroker",
    [zpn_client_type_ip_anchoring] =        "sipa",
    [zpn_client_type_browser_isolation] =   "isolation",
    [zpn_client_type_machine_tunnel] =      "machine",
    [zpn_client_type_broker_transit] =      "transit",
    [zpn_client_type_branch_conn_svc] =     "zbcs",
    [zpn_client_type_branch_connector] =    "bc",
    [zpn_client_type_zapp_partner] =        "partner",
    [zpn_client_type_assistant] =           "assistant",
    [zpn_client_type_vdi] =                 "vdi",
    [zpn_client_type_site_controller] =     "sitec",
    [zpn_client_type_eas_ctrl] =            "eas_ctrl",
    [zpn_client_type_eas_data] =            "eas_data",
};
static const char *client_type_str_concat = "zapp,slogger,exporter,ec,pbroker,sipa,isolation,machine,transit,zbcs,bc,partner,assistant,vdi,sitec,eas_ctrl,eas_data";

static __inline__ enum zpn_client_type client_type_from_str(const char* str) {
    if (!str) return zpn_client_type_zapp;  // default
    for (int i = 0; i < zpn_client_type_total_count; i++) {
        if (ct_strings[i] && !strcmp(ct_strings[i], str))
            return (i == zpn_client_type_invalid) ? zpn_client_type_zapp : i;
    }
    return zpn_client_type_zapp;
}

/*
 * bucket type enum to string
 */
static const char *bt_strings[BT_LAST] = {
    [BT_PRIV_MTN]       = "pb_mtn",
    [BT_PRIV]           = "pb",
    [BT_PUBL_PRIV_MTN]  = "pub_pb_mtn",
    [BT_PUBL_PRIV]      = "pub_pb",
    [BT_PUBL]           = "zs"
};

const char* zpn_balance_bucket_type_str(enum dc_bucket_type bt) {
    ZPN_BROKER_ASSERT_HARD(bt < BT_LAST && bt_strings[bt], "DC Bucket type value out of range!");
    return bt_strings[bt];
}

/*
 * For user input of "trusted networks", which is taken as a comma-separated list of trusted network ids
 * and convert it to an argo, as it's the typical format of input from ZCC
 */
#define TN_SEPARATORS ","
struct argo_object *debug_tokenize_trusted_networks(const char* query_str)
{
    struct argo_object *argo = NULL; /* return object */
    int tok_count = 0;
    char *tok, *save, *tok_buf;
    struct zpn_trusted_networks tn;
    memset(&tn, 0, sizeof(tn));

    tok_buf = (query_str && strlen(query_str)) ? ZPN_BALANCE_STRDUP(query_str, strlen(query_str)) : NULL;
    if (!tok_buf) return NULL;

    /* tokenize once to get count, for buffer allocation */
    for (tok = strtok_r(tok_buf, TN_SEPARATORS, &save); tok; tok_count++, tok = strtok_r(NULL, TN_SEPARATORS, &save));

    /* tokenize again to get strings, not efficient but don't care in this case. */
    if (tok_count) {
        tn.id = 1001; /* inconsequential */
        tn.trusted_networks_count = tok_count;
        tn.trusted_networks = ZPN_BALANCE_MALLOC(sizeof(char*) * tn.trusted_networks_count);

        strcpy(tok_buf, query_str); /* tok_buf was modified, copy again */
        tok = strtok_r(tok_buf, TN_SEPARATORS, &save);
        for (; tok_count; tok_count--) {
            tn.trusted_networks[tn.trusted_networks_count - tok_count] = tok; /* individual char* are pointing to tok_buf */
            tok = strtok_r(NULL, TN_SEPARATORS, &save);
        }
        argo = argo_object_create(zpn_trusted_networks_description, &tn);
    }
    if (tok_buf) ZPN_BALANCE_FREE(tok_buf);
    if (tn.trusted_networks) ZPN_BALANCE_FREE(tn.trusted_networks);
    return argo;
}

/*
 * For suporting the ability to print event logs to the output stream of the debug command.
 */
static void zpn_balance_debug_trace(enum argo_log_priority priority, void* cookie, const char *format, ...)
{
    struct zpath_debug_state *request_state = (struct zpath_debug_state *)cookie;
    char tmp_str[8*1024];
    va_list list;
    va_start(list, format);
    vsnprintf(tmp_str, sizeof(tmp_str), format, list);
    va_end(list);
    DEBUG_ZDP("[%10s] %s\n", argo_log_short_priority_string(priority), tmp_str);
}

/*
 * Helper for printing some headers to the output stream before execution
 */
static int debug_print_ip_info(struct argo_inet *remote_ip, struct zpath_debug_state *request_state)
{
    char city[MMINDSTRSIZE] = {0};
    int res = zpath_geoip_lookup_city(remote_ip, city, sizeof(city));
    if (res == ZPATH_RESULT_NO_ERROR)   DEBUG_ZDP("City = %s\n", city);
    else                                DEBUG_ZDP("geoip city lookup failed\n");

    char asorg[MMINDSTRSIZE] = {0}; /* Autonomous System Organization name */
    char isp[MMINDSTRSIZE] = {0};   /* ISP name */
    char org[MMINDSTRSIZE] = {0};   /* Organization name */
    int asnum = -1;                 /* Autonomous System Number for given ip */

    res = zpath_ispip_lookup_orginfo(remote_ip, &asnum, asorg, isp, org);
    if (res == ZPATH_RESULT_NO_ERROR) {
        DEBUG_ZDP("Autonomous System Number = %d\n", asnum);
        DEBUG_ZDP("Autonomous System Organization = %s\n", strlen(asorg) ? asorg : "");
        DEBUG_ZDP("ISP = %s\n", strlen(isp) ? isp : "");
        DEBUG_ZDP("Organization = %s\n", strlen(org) ? org : "");
    } else {
        DEBUG_ZDP("geoip org info lookup failed\n");
    }
    return ZPN_RESULT_NO_ERROR;
}

static __inline__ const char* inst_type_str(enum Zpn_Instance_Type personality) {
    switch (personality) {
    case ZPN_INSTANCE_TYPE_PUBLIC_BROKER: return "ZS";  // Zscaler public infra
    case ZPN_INSTANCE_TYPE_PRIVATE_BROKER: return "PR"; // Customer private instance
    default: return "?!";
    }
}

static void debug_print_viable_inst(int64_t now_s, struct zpath_debug_state *request_state, struct viable_instance *inst, const char* indent_str, int indx) {
    int diff_s = (int)(inst->modified_time_s ? (now_s - inst->modified_time_s) : -1);
    char *client_resolution_type;

    if (inst->clients_hot) {
        client_resolution_type = "HOT";
    } else if (inst->clients == 0) {
        client_resolution_type = "UNAVAILABLE";
    } else if (inst->clients_max && inst->clients_available){
        client_resolution_type = "NORMAL";
    } else if (inst->adjusted_clients_available && !inst->clients_max){
        client_resolution_type = "INFERRED";
    } else {
        client_resolution_type = "UNKNOWN";
    }

    DEBUG_ZDP("%s%1s%4d. [%2s] %20"PRId64", "
                "excl:"FMT_EXCLUSION", "
                "sel_method:%s, "
                "%12s %12s %12s, "
                "rpt:%"PRId64"(%9ds), cpu:%3u, mem:%3u, clients:%" PRId64", clients_max:%" PRId64", clients_available:%" PRId64", adjusted_clients_available[%s]:%"PRId64", fd:%3u skew:%3u, "
                "load:%3u, chance_adj:%+4d, buck_min:%" PRIu64 ", buk_max:%" PRIu64 ", "
                "pub:%7s, ips:%2d, mtn:%2lu, lat:%"FMT_COORDINATE", lon:%"FMT_COORDINATE", grace_dist:%"FMT_COORDINATE", cc:%3s, alt_cloud:%s,"
                "name: %s\n",
            indent_str, inst->is_locally_overriden ? "*" : " ",
            indx, inst_type_str(inst->personality), inst->gid,
            zpn_balance_excl_str(inst->exclusion_reason),
            (inst->method != zpn_balance_selection_method_unknown ? zpn_balance_selection_method_str(inst->method) : "NA"),
            inst->instance_role ? inst->instance_role : "", inst->balance_role ? inst->balance_role : "", inst->balance_mode ? inst->balance_mode : "",
            inst->modified_time_s, diff_s, inst->cpu_pct, inst->mem_pct,
            inst->clients, inst->clients_max, inst->clients_available, client_resolution_type, inst->adjusted_clients_available,
            inst->proc_fd_util_pct, inst->load_skew, inst->effective_load, inst->selection_skew_pct, inst->bucket_min, inst->bucket_max,
            inst->is_public, inst->publish_ips_count, inst->trusted_networks_count, inst->site.lat, inst->site.lon, inst->grace_distance, inst->site.cc,
            inst->alt_cloud ? inst->alt_cloud : "Not configured",
            inst->name ? inst->name : "<no-name>");
}

#define DEFAULT_INST_COUNT      16                  // number of things to print if "all" is not specified on debug command
#define MAX_INST_COUNT          2048                // number of things to print if "all" is specified on debug command
#define FMT_INCOMPLETE_COUNT    "... and %lu more\n"

static void debug_print_inst_loading(struct balance_request_state *state, struct zpath_debug_state *request_state, int do_all)
{
    int i;
    size_t collection_limit = do_all ? (size_t)(-1) : DEFAULT_INST_COUNT;
    DEBUG_ZDP("\nInstance Loading: took %" PRId64 "us\n", state->usec_inst_load);
    DEBUG_ZDP("\n---------------------------------------------------\n");

    DEBUG_ZDP("  Constallations:  %lu\n", state->constellation_gid_count);
    for (i = 0; i < state->constellation_gid_count && i < collection_limit; i++)
        DEBUG_ZDP("  %4d. %"PRId64"\n", i, state->constellation_gids[i]);
    if (i < state->constellation_gid_count)  DEBUG_ZDP(FMT_INCOMPLETE_COUNT, state->constellation_gid_count - i);

    DEBUG_ZDP("  Negative Constellations: %lu\n", state->constellation_gid_excl_count);
    for (i = 0; i < state->constellation_gid_excl_count && i < collection_limit; i++)
        DEBUG_ZDP("  %4d. %"PRId64"\n", i, state->constellation_gids_excl[i]);
    if (i < state->constellation_gid_excl_count)  DEBUG_ZDP(FMT_INCOMPLETE_COUNT, state->constellation_gid_excl_count - i);

    DEBUG_ZDP("  Constellation Instances: %lu\n", state->instance_gid_count);
    for (i = 0; i < state->instance_gid_count && i < collection_limit; i++)
        DEBUG_ZDP("  %4d. %"PRId64"\n", i, state->instance_gids[i]);
    if (i < state->instance_gid_count)  DEBUG_ZDP(FMT_INCOMPLETE_COUNT, state->instance_gid_count - i);

    DEBUG_ZDP("  All Viable Instances: %lu\n", state->viable_instance_count);
    for (i = 0; i < state->viable_instance_count && i < collection_limit; i++)
        DEBUG_ZDP("  %4d. %"PRId64"\n", i, state->viable_instances[i].gid);
    if (i < state->viable_instance_count)  DEBUG_ZDP(FMT_INCOMPLETE_COUNT, state->viable_instance_count - i);
}

static int debug_dump_policy_result_ht(void *cookie, void *counter, void *cookie3, void *cookie4, void *cookie5,
        void *object, void *key, size_t key_len)
{
    struct zpath_debug_state *request_state = cookie;
    struct zpn_private_broker_group *group = NULL;
    int res = 0;
    int i = (*(int *)counter)++;
    int64_t *pse_group_gid = object;
    res = zpn_pbroker_group_get_by_gid(*pse_group_gid, &group, 0, NULL, NULL, 0);
    if (res == ZPN_RESULT_NO_ERROR) {
        /* Print policy matching PSE group's GID along with name */
        DEBUG_ZDP("  %4d. %"PRId64" %2s\n", i, *pse_group_gid, group->name);
    } else {
        /* Print policy matching PSE group's GID */
        DEBUG_ZDP("  %4d. %"PRId64"\n", i, *pse_group_gid);
    }
    return ZPN_RESULT_NO_ERROR;
}

static void debug_print_redirect_policy(struct balance_request_state *state, struct zpath_debug_state *request_state, int do_all)
{
    struct      zpn_rule            *redirect_rule          = NULL;
    size_t      rule_count                                  = 1;
    int         res                                         = 0;
    char        *policy_name                                = NULL;

    DEBUG_ZDP("\nRedirect policy evaluation: took %" PRId64 "us\n", state->usec_policy_execute);
    DEBUG_ZDP("\n---------------------------------------------------\n");

    if (state->policy_state && state->policy_state->rule_matched) {

        res = zpn_rule_get_by_gid(state->policy_state->rule_matched, &redirect_rule, &rule_count);
        if (!res) {
            policy_name = redirect_rule->name;
        }

        DEBUG_ZDP("  Redirect policy matching rule: %" PRId64 " policy_name:%s with action: %s\n",
                state->policy_state->rule_matched, policy_name, zpe_access_action_string(state->policy_state->redirect_action));
        if (state->policy_state->redirect_action == zpe_access_action_redirect_default) {
            return;
        }
        DEBUG_ZDP("  Num of PSE groups matching above rule : %d\n", state->policy_state->gid_count);

        if (state->policy_state->gid_count && state->policy_state->policy_result_htable) {
            int64_t walk_key = 0;
            int counter = 0;
            zhash_table_walk2(state->policy_state->policy_result_htable, &walk_key, debug_dump_policy_result_ht,
                    (void *)request_state, &counter, NULL, NULL, NULL);
        }
    } else {
        DEBUG_ZDP(" Redirect policy action: %s\n", zpe_access_action_string(zpe_access_action_redirect_default));
    }
}

static void debug_print_inst_filtering(struct balance_request_state *state, struct zpath_debug_state *request_state, int do_all)
{
    int i;
    int64_t now_s = epoch_s();
    size_t collection_limit = do_all ? (size_t)(-1) : DEFAULT_INST_COUNT;

    DEBUG_ZDP("\nInstance Filtering: %lu instances, took %" PRId64 "us\n", state->viable_instance_count, state->usec_inst_filter);
    DEBUG_ZDP("\n---------------------------------------------------------\n");

    for (i = 0; i < state->viable_instance_count && i < collection_limit; i++)
        debug_print_viable_inst(now_s, request_state, &state->viable_instances[i], "  ", i);
    if (i < state->viable_instance_count)  DEBUG_ZDP(FMT_INCOMPLETE_COUNT, state->viable_instance_count - i);
}


static void debug_print_instances(struct balance_request_state *state, struct zpath_debug_state *request_state)
{
    int i = 0;
    int64_t now_s = epoch_s();
    char *dc_type;

    for (enum dc_bucket_type bt = 0; bt < BT_LAST; bt++) {
        const struct dc_bucket *bucket = &state->dc_buckets.bucket[bt];

        if (bucket->exclude || !bucket->dc_count) {
             continue;
        }

        DEBUG_ZDP("  [%10s] dcs: %3lu, closest_dist: %"FMT_FLOAT"\n",
                  zpn_balance_bucket_type_str(bt), bucket->dc_count, bucket->dist_best_dc);

        for (i = 0; i < bucket->dc_count; i++) {
            const struct data_center *dc = &bucket->dcs[i];
            int dc_bucket_type = bucket->type;
            struct viable_instance *inst = NULL;
            int indx = 0;

            if (dc->exclude) continue;

            DEBUG_ZDP("\n----------------------------------------\n");
            if (state->do_multi_dc_redir_public && BT_PUBL == dc_bucket_type) {
                dc_type = (bucket->primary_dc == dc) ? "primary" : "secondary";
                DEBUG_ZDP("DC %d: %s\n", i, dc->name);
                DEBUG_ZDP("Type: %s\n", dc_type);
                DEBUG_ZDP("Location: lat: %"FMT_COORDINATE", lon: %"FMT_COORDINATE", cc: %s\n",
                         dc->site.lat, dc->site.lon, dc->site.cc);
                DEBUG_ZDP("Distance: %"FMT_FLOAT"\n", dc->dist);
                DEBUG_ZDP("Capacity: %u, Load: %u\n",
                         dc->capacity_total, dc->effective_load);
            } else {
                DEBUG_ZDP("DC %d: %s\n", i, dc->name);
                DEBUG_ZDP("Location: lat: %"FMT_COORDINATE", lon: %"FMT_COORDINATE", cc: %s\n",
                         dc->site.lat, dc->site.lon, dc->site.cc);
                DEBUG_ZDP("Distance: %"FMT_FLOAT"\n", dc->dist);
                DEBUG_ZDP("Capacity: %u, Load: %u\n",
                         dc->capacity_total, dc->effective_load);
            }
            DEBUG_ZDP("----------------------------------------\n\n");

            if (!LIST_EMPTY(&dc->part_inst_list)) {
                DEBUG_ZDP("  List of partitioned instances:\n");
                LIST_FOREACH(inst, &dc->part_inst_list, entry) {
                    debug_print_viable_inst(now_s, request_state, inst, "        (p)", indx);
                    indx++;
                }
                DEBUG_ZDP("\n");
            }

            if (!LIST_EMPTY(&dc->unpart_inst_list)) {
                DEBUG_ZDP("  List of unpartitioned instances:\n");
                LIST_FOREACH(inst, &dc->unpart_inst_list, entry) {
                    debug_print_viable_inst(now_s, request_state, inst, "        (u)", indx);
                    indx++;
                }
                DEBUG_ZDP("\n");
            }

            if (!LIST_EMPTY(&dc->inst_list)) {
                DEBUG_ZDP("  List of other instances:\n");
                LIST_FOREACH(inst, &dc->inst_list, entry) {
                    debug_print_viable_inst(now_s, request_state, inst, "        (x)", indx);
                    indx++;
                }
                DEBUG_ZDP("\n");
            }
        }
    }
    DEBUG_ZDP("\n");
}

static void debug_print_inst_bucketing(struct balance_request_state *state, struct zpath_debug_state *request_state, int do_all)
{
    int i;

    DEBUG_ZDP("\nDC Bucketing: took %" PRId64 "us\n", state->usec_bucket);
    DEBUG_ZDP("\n-----------------------------------\n");
    DEBUG_ZDP("client_mtns: [");
    for (i = 0; i < state->client_mtn_gids_count; i++)
        DEBUG_ZDP("%4d. %"PRId64", ", i, state->client_mtn_gids[i]);
    DEBUG_ZDP("]\n");
    for (enum dc_bucket_type bt = 0; bt < BT_LAST; bt++)
        DEBUG_ZDP("  [%7s] dcs: %3lu\n", zpn_balance_bucket_type_str(bt), state->dc_buckets.bucket[bt].dc_count);
}

static void debug_print_dc_selection(struct balance_request_state *state, struct zpath_debug_state *request_state, int do_all)
{
    int i;

    DEBUG_ZDP("\nDC Selection: took %" PRId64 "us\n", state->usec_dc_select);
    DEBUG_ZDP("\n-----------------------------------\n");

    for (enum dc_bucket_type bt = 0; bt < BT_LAST; bt++) {
        const struct dc_bucket *bucket = &state->dc_buckets.bucket[bt];
        DEBUG_ZDP("  [%7s] dcs: %3lu, closest_dist: %"FMT_FLOAT", is_grace_dist_applied: %3lu, excl: "FMT_EXCLUSION"\n",
            zpn_balance_bucket_type_str(bt), bucket->dc_count, bucket->dist_best_dc, bucket->apply_grace_dist_btwn_dcs, zpn_balance_excl_str(bucket->exclusion_reason));
        for (i = 0; i < bucket->dc_count; i++) {
            const struct data_center *dc = &bucket->dcs[i];
            DEBUG_ZDP("    %4d. "FMT_DC_NAME" lat: %"FMT_COORDINATE", lon: %"FMT_COORDINATE", cc: %3s, dist: %"FMT_FLOAT", grace_dist: %"FMT_FLOAT", instances: %2u, capacity: %4u, load: %4u, excl: "FMT_EXCLUSION"\n",
                i, dc->name, dc->site.lat, dc->site.lon, dc->site.cc, dc->dist, dc->grace_dist_max, dc->inst_total, dc->capacity_total, dc->effective_load, zpn_balance_excl_str(dc->exclusion_reason));
        }
    }
}

static void debug_print_inst_selection(struct balance_request_state *state, struct zpath_debug_state *request_state, int do_all)
{
    DEBUG_ZDP("\nInstance Selection: took %" PRId64 "us\n", state->usec_inst_select);
    DEBUG_ZDP("\n---------------------------------------------\n");

    debug_print_instances(state,request_state);
}

static void debug_print_dbg_after_redirect(struct balance_request_state *state, struct zpath_debug_state *request_state, int do_all)
{

    switch(state->dbg_stop_after) {
        case DBG_EXEC_PHASE_INST_LOAD: /* 1 */
                debug_print_inst_loading(state, request_state, do_all);
                break;
        case DBG_EXEC_PHASE_REDIRECT_POLICY: /* 2 */
                debug_print_inst_loading(state, request_state, do_all);
                debug_print_redirect_policy(state, request_state, do_all);
                break;
        case DBG_EXEC_PHASE_INST_FILTER: /* 3 */
                debug_print_inst_loading(state, request_state, do_all);
                debug_print_redirect_policy(state, request_state, do_all);
                debug_print_inst_filtering(state, request_state, do_all);
                break;
        case DBG_EXEC_PHASE_DC_BUCKET: /* 4 */
                debug_print_inst_loading(state, request_state, do_all);
                debug_print_redirect_policy(state, request_state, do_all);
                debug_print_inst_filtering(state, request_state, do_all);
                debug_print_inst_bucketing(state, request_state, do_all);
                break;
        case DBG_EXEC_PHASE_DC_SELECT: /* 5 */
                debug_print_inst_loading(state, request_state, do_all);
                debug_print_redirect_policy(state, request_state, do_all);
                debug_print_inst_filtering(state, request_state, do_all);
                debug_print_inst_bucketing(state, request_state, do_all);
                debug_print_dc_selection(state, request_state, do_all);
                break;
        case DBG_EXEC_PHASE_INST_SELECT: /* 6 */
                debug_print_inst_loading(state, request_state, do_all);
                debug_print_redirect_policy(state, request_state, do_all);
                debug_print_inst_filtering(state, request_state, do_all);
                debug_print_inst_bucketing(state, request_state, do_all);
                debug_print_dc_selection(state, request_state, do_all);
                debug_print_inst_selection(state, request_state, do_all);
                break;
        default:
                DEBUG_ZDP("\nIcorrect value for dbg_stop_after option, enter any number from 1-5\n");
                break;
    }
}

static __inline__ void sxprint_inst(char **s, char **e, struct viable_instance *inst, int last_inst, enum instance_type is_part_inst)
{
    /* be a little judicious what we print here, try to reduce auth log size */
    *s += sxprintf(*s, *e, "\"%"PRId64"\": {", inst->gid);
    *s += sxprintf(*s, *e, "\"br\":\"%s\",", inst->balance_role ? inst->balance_role : "");
    *s += sxprintf(*s, *e, "\"bm\":\"%s\",", inst->balance_mode ? inst->balance_mode : "");
    *s += sxprintf(*s, *e, "\"rp\":%"PRId64",", inst->modified_time_s);
    *s += sxprintf(*s, *e, "\"ld\":%u,", inst->effective_load);
    *s += sxprintf(*s, *e, "\"cl\":%"PRIu64",", inst->clients);
    *s += sxprintf(*s, *e, "\"ca\":%"PRId64",", inst->clients_available);
    *s += sxprintf(*s, *e, "\"aca\":%"PRId64",", inst->adjusted_clients_available);
    *s += sxprintf(*s, *e, "\"cm\":%"PRIu64",", inst->clients_max);
    *s += sxprintf(*s, *e, "\"heat\":\"%s\",", inst->clients_hot ? "HOT":"NORMAL");
    *s += sxprintf(*s, *e, "\"p\":\"%s\",", inst->is_public);
    *s += sxprintf(*s, *e, "\"ipc\":%d,", inst->publish_ips_count);
    *s += sxprintf(*s, *e, "\"tn\":%zu,", inst->trusted_networks_count);
    *s += sxprintf(*s, *e, "\"ex\":\"%s\",", zpn_balance_excl_str(inst->exclusion_reason));
    *s += sxprintf(*s, *e, "\"nm\":\"%s\",", inst->name ? inst->name : "");
    *s += sxprintf(*s, *e, "\"alt\":\"%s\",", inst->alt_cloud ? inst->alt_cloud : "");
    *s += sxprintf(*s, *e, "\"me\":\"%s\",", zpn_balance_selection_method_str(inst->method));
    *s += sxprintf(*s, *e, "\"bl\":%"PRIu64",", inst->bucket_min);
    *s += sxprintf(*s, *e, "\"bh\":%"PRIu64",", inst->bucket_max);
    *s += sxprintf(*s, *e, "\"cbl\":%"PRIu64",", inst->client_bucket_min);
    *s += sxprintf(*s, *e, "\"cbh\":%"PRIu64",", inst->client_bucket_max);

    if (INSTANCE_NONE == is_part_inst) {
        *s += sxprintf(*s, *e, "\"it\":\"%s\"", "none");
    } else if(INSTANCE_PARTITIONED == is_part_inst) {
        *s += sxprintf(*s, *e, "\"it\":\"%s\"", "partitioned");
    } else if (INSTANCE_UNPARTITIONED == is_part_inst) {
        *s += sxprintf(*s, *e, "\"it\":\"%s\"", "unpartitioned");
    }
    *s += sxprintf(*s, *e, last_inst ? "}" : "},");
}

/* generate a very condensed version of how the execution of redirect happened.
 * the output of this routine goes into event log for debugging purposes.
 */
void log_execution_condensed(struct balance_request_state *state)
{
#define CONDENSED_LOG_BUF_SIZE (6344)

/* something we can refer later about how this message was written */
#define CONDENSED_LOG_ENCODING_VERSION   (1)
/* We can't print all dcs, so limit on the number of dcs to print.
 * DCs are sorted by dist and there is a good chance the first few are the ones
 * we are interested in anyways.
 */
#define CONDENSED_LOG_MAX_DC_PER_BUCKET  (5)
/* We can't print info of all instances not selected.
 * Just a few samples should be enough
 */
#define CONDENSED_LOG_MAX_INST_PER_DC (4)

    struct zpn_balance_redirect_request *req = state->req;

    char buf[CONDENSED_LOG_BUF_SIZE + 1];
    char *s = buf;
    char *e = s + CONDENSED_LOG_BUF_SIZE;

    s += sxprintf(s, e, "{");

    /* version */
    s += sxprintf(s, e, "\"v\":%d,",CONDENSED_LOG_ENCODING_VERSION);
    /* minimal config */
    s += sxprintf(s, e, "\"sdcpr\":%d,", g_balance_config.single_dc_redir_priv);
    s += sxprintf(s, e, "\"mdcpubpct\":%"PRId64",", g_balance_config.mdc_pub_pct);
    /* result */
    s += sxprintf(s, e, "\"res\":\"%s\",", zpn_result_string(state->res));
    /* timing */
    s += sxprintf(s, e, "\"us\":%"PRId64",", state->usec_stop - state->usec_start);

    /* client */
    s += sxprintf(s, e, "\"clt\":{");
    s += sxprintf(s, e, "\"tp\":\"%s\",", zpn_client_type_string(req->client_type));
    s += sxprintf(s, e, "\"cst\":%"PRId64",", req->customer_gid);
    s += sxprintf(s, e, "\"pg\":%"PRId64",", req->partition_gid);
    s += sxprintf(s, e, "\"tn\":%lu,", state->client_mtn_gids_count);
    s += sxprintf(s, e, "\"la\":%.2f,", req->remote_lat);
    s += sxprintf(s, e, "\"lo\":%.2f,", req->remote_lon);
    s += sxprintf(s, e, "\"cc\":\"%s\",", req->remote_cc);
    s += sxprintf(s, e, "\"pu\":%d,", req->rbt ? req->rbt->type[PUBLIC_BROKER] : 1);
    s += sxprintf(s, e, "\"pr\":%d,", req->rbt ? req->rbt->type[PRIVATE_BROKER] : 1);
    s += sxprintf(s, e, "\"pp\":%d,", req->rbt ? req->rbt->type[PUBLIC_PRIVATE_BROKER] : 1);
    s += sxprintf(s, e, "\"mdc\":%d,", req->multi_dc_redir_capable);
    s += sxprintf(s, e, "\"domdc\":%d,", state->do_multi_dc_redir_public);
    s += sxprintf(s, e, "\"ds_flags\": e=%d c=%d x=%d,", req->ipv6_redirect_enabled, req->ipv6_redirect_capable, req->ipv6_exclusive_wanted);
    s += sxprintf(s, e, "},");


    /* constellations and instances */
    s += sxprintf(s, e, "\"ins\":{");
    s += sxprintf(s, e, "\"c\":%lu,", state->constellation_gid_count);
    s += sxprintf(s, e, "\"nc\":%lu,", state->constellation_gid_excl_count);
    s += sxprintf(s, e, "\"ic\":%lu,", state->instance_gid_count);
    s += sxprintf(s, e, "\"vi\":%lu", state->viable_instance_count);
    s += sxprintf(s, e, "},");

    /* buckets, dcs instances */
    for (enum dc_bucket_type bt = 0; bt < BT_LAST; bt++) {
        /* beginning of bucket */
        const struct dc_bucket *bucket = &state->dc_buckets.bucket[bt];
        s += sxprintf(s, e, "\"%s_%d\":{", zpn_balance_bucket_type_str(bt), bucket->exclusion_reason);

        /* not printing dcs if bucket is excluded */
        if (!bucket->exclude) {
            for (int i = 0; i < bucket->dc_count && i < CONDENSED_LOG_MAX_DC_PER_BUCKET; i++) {
                const struct data_center *dc = &bucket->dcs[i];
                int last_dc = (i == bucket->dc_count - 1 || i == CONDENSED_LOG_MAX_DC_PER_BUCKET - 1) ;

                /* beginning of dc */
                s += sxprintf(s, e, "\"%s\":{", dc->name);
                s += sxprintf(s, e, "\"ds\":%.2f,", dc->dist);
                s += sxprintf(s, e, "\"ic\":%u,", dc->inst_total);
                s += sxprintf(s, e, "\"cp\":%u,", dc->capacity_total);
                s += sxprintf(s, e, "\"ld\":%u,", dc->effective_load);
                s += sxprintf(s, e, "\"ex\":\"%s\"", zpn_balance_excl_str(dc->exclusion_reason));

                /* not printing instance details if dc is excluded */
                if (!dc->exclude) {
                    struct viable_instance *inst = NULL;
                    int inst_count = 0;
                    int last_inst = 0;

                    /* instance list */
                    s += sxprintf(s, e, ",\"i\":{");

                    /* we may not have a whole lot of space, so iterate twich:
                     * first iteration print selected instances
                     * second iteration print as many as allowed */
                    LIST_FOREACH(inst, &dc->inst_list, entry) {
                        if (inst->exclusion_reason != excl_selected) continue;
                        inst_count++;
                        last_inst = inst_count == CONDENSED_LOG_MAX_DC_PER_BUCKET || inst_count == dc->inst_total;
                        sxprint_inst(&s, &e, inst, last_inst, INSTANCE_NONE);
                        if (last_inst) break;
                    }
                    if(req->is_lp_enabled) {
                            LIST_FOREACH(inst, &dc->part_inst_list, entry) {
                                if (inst->exclusion_reason != excl_selected) continue;
                                inst_count++;
                                last_inst = inst_count == CONDENSED_LOG_MAX_DC_PER_BUCKET || inst_count == dc->inst_total;
                                sxprint_inst(&s, &e, inst, last_inst, INSTANCE_PARTITIONED);
                                if (last_inst) break;
                            }
                            LIST_FOREACH(inst, &dc->unpart_inst_list, entry) {
                                if (inst->exclusion_reason != excl_selected) continue;
                                inst_count++;
                                last_inst = inst_count == CONDENSED_LOG_MAX_DC_PER_BUCKET || inst_count == dc->inst_total;
                                sxprint_inst(&s, &e, inst, last_inst, INSTANCE_UNPARTITIONED);
                                if (last_inst) break;
                            }
                    }
                    if (!last_inst) {
                        LIST_FOREACH(inst, &dc->inst_list, entry) {
                            if (inst->exclusion_reason == excl_selected) continue;
                            inst_count++;
                            last_inst = inst_count == CONDENSED_LOG_MAX_DC_PER_BUCKET || inst_count == dc->inst_total;
                            sxprint_inst(&s, &e, inst, last_inst, INSTANCE_NONE);
                            if (last_inst) break;
                        }
                        if(req->is_lp_enabled) {
                            LIST_FOREACH(inst, &dc->part_inst_list, entry) {
                                if (inst->exclusion_reason == excl_selected) continue;
                                inst_count++;
                                last_inst = inst_count == CONDENSED_LOG_MAX_DC_PER_BUCKET || inst_count == dc->inst_total;
                                sxprint_inst(&s, &e, inst, last_inst, INSTANCE_PARTITIONED);
                                if (last_inst) break;
                            }
                            LIST_FOREACH(inst, &dc->unpart_inst_list, entry) {
                                if (inst->exclusion_reason == excl_selected) continue;
                                inst_count++;
                                last_inst = inst_count == CONDENSED_LOG_MAX_DC_PER_BUCKET || inst_count == dc->inst_total;
                                sxprint_inst(&s, &e, inst, last_inst, INSTANCE_UNPARTITIONED);
                                if (last_inst) break;
                            }
                        }
                    }
                    s += sxprintf(s, e, "}"); /* end of instance list */
                }
                s += sxprintf(s, e, last_dc ? "}" : "},"); /* end of dc*/
            }
        }
        s += sxprintf(s, e, bt == BT_LAST - 1 ? "}" : "},"); /* end of bucket */
    }
    s += sxprintf(s, e, "}"); /* end */
    buf[CONDENSED_LOG_BUF_SIZE] = '\0';

    /* log at info level to event log for debugging purposes.
     * this is one of the few entries we generate at info level */
    ZPN_BALANCE_LOG_STATE(AL_INFO, "%s: condensed execution %s", state->req->log_tag, buf);
}

/*
 * Output balance redirect execution state, basically a dump of
 * relevant information in a balance_request_state structures.
 */
int debug_print_balance_state(struct balance_request_state *state, struct zpath_debug_state *request_state, int do_all)
{
   /* Execution timing and params */
    struct zpn_balance_redirect_request *req = state->req;
    DEBUG_ZDP("\nExecution: %s, took %" PRId64 "us\n", zpn_result_string(state->res), state->usec_stop - state->usec_start);
    DEBUG_ZDP("Client Request: client_type: %s, lat: %"FMT_COORDINATE", lon: %"FMT_COORDINATE", cc: %s, customer: %"PRId64", scope: %"PRId64", multi_dc_capable: %d, do_mdc: %d, mdc_sdc_mixup_inst: %"PRId64",  grace(config_ovd): %u\n",
            zpn_client_type_string(req->client_type), req->remote_lat, req->remote_lon, req->remote_cc, req->customer_gid, req->scope_gid, req->multi_dc_redir_capable, state->do_multi_dc_redir_public, req->mdc_sdc_mixup_inst, state->pse_grace_dist_miles);
    DEBUG_ZDP("    requested broker types: pub: %d, pb: %d, pub_pb: %d\n",
            req->rbt ? req->rbt->type[PUBLIC_BROKER] : 1,
            req->rbt ? req->rbt->type[PRIVATE_BROKER] : 1,
            req->rbt ? req->rbt->type[PUBLIC_PRIVATE_BROKER] : 1);
    DEBUG_ZDP("    geofencing %s\n", state->gf_state == geo_include ? "inclusive" : "exclusive");
    DEBUG_ZDP("    grace distance considered for PSE selection: %s\n", state->grace_dist_state ? "yes" : "no");

    if(state->dbg_stop_after) {
        debug_print_dbg_after_redirect(state, request_state, 1);
        goto ll_stop;
    }

    /* Instance Loading */
    debug_print_inst_loading(state, request_state, do_all);

    /* Redirect policy evaluation */
    debug_print_redirect_policy(state, request_state, do_all);

    /* Instance Filtering */
    debug_print_inst_filtering(state, request_state, do_all);

    /* Instance Bucketing */
    debug_print_inst_bucketing(state, request_state, do_all);

    /* DC Selection */
    debug_print_dc_selection(state, request_state, do_all);

    /* Instance Selection (within selected DCs) */
    debug_print_inst_selection(state, request_state, do_all);

    /* Time taken by compaction.*/
    DEBUG_ZDP("\nCompaction: took %" PRId64 "us\n", state->usec_compact);

    /* Compaction and final instance list */
    DEBUG_ZDP("\nFinal Selection: %d brokers\n", *req->broker_count);
    for (int i = 0; i < *req->broker_count; i++) {
        DEBUG_ZDP("   %4d. %s\n", i, req->brokers[i]);
    }

ll_stop:
    return ZPN_RESULT_NO_ERROR;
}

/*
 * Debug function to normalize scim attr value and saml attr name to lower case
 * as similar to broker how it does.
 */
static int debug_normalise_policy_attr(struct zpath_debug_state  *request_state,
                                  const char *key,
                                  enum policy_arg_type arg_type,
                                  char *out_key,
                                  size_t out_key_len)
{
    /* saml - Should be in format of idp_gid|SAML|attr_name|attr_val */
    /* scim - Should be in format of idp_gid|SCIM|attr_gid|attr_val  */

    char    hash_key[2048]                      = {0};
    char   *saveptr                             = NULL;
    char   *token[POLICY_ARG_EXPECTED_TOKENS]   = {0};
    int     i                                   = 0;

    if (key == NULL || out_key == NULL || out_key_len <= 0) {
        // Invalid Parameters
        return ZPN_RESULT_ERR;
    }

    snprintf(hash_key, sizeof(hash_key), "%s", key);

    token[i] = strtok_r(hash_key, POLICY_ARG_DELIMETER, &saveptr);
    while (token[i] != NULL && ++i < POLICY_ARG_EXPECTED_TOKENS) {
        token[i] = strtok_r(NULL, POLICY_ARG_DELIMETER, &saveptr);
    }

    if (i != POLICY_ARG_EXPECTED_TOKENS || strtok_r(NULL, POLICY_ARG_DELIMETER, &saveptr)) {
        DEBUG_ZDP("Policy parameter isn't in expected format:%s\n", key);
        return ZPN_RESULT_ERR;
    } else {
        // Normalise the token to lower case
        if (arg_type == POLICY_ARG_TYPE_SAML) {
            // Attribute name for SAML
            zpath_downcase(token[POLICY_ATTR_NAME_TAG_POSITION]);
        }
        // Attribute value for SAML/SCIM
        zpath_downcase(token[POLICY_ATTR_VALUE_TAG_POSITION]);
    }

    // Copy the normalised key to output buffer
    memset(out_key, 0, out_key_len);
    snprintf(out_key, out_key_len, "%s|%s|%s|%s", token[0], token[1], token[2], token[3]);

    return ZPN_RESULT_NO_ERROR;
}


int debug_run_and_analyze_common_return_state(struct zpath_debug_state  *request_state,
                                        const char                  *client_type,
                                        const char                  *scope_gid,
                                        const char                  *client_ip,
                                        const char                  *client_lat,
                                        const char                  *client_lon,
                                        const char                  *client_cc,
                                        const char                  *client_mtn,
                                        const char                  *client_saml,
                                        const char                  *client_scim,
                                        const char                  *client_scim_group,
                                        int                         do_trace,
                                        int                         do_all,
                                        enum dbg_balance_exec_phase  dbg_stop_after,
                                        int                         auto_flip_geo,
                                        int                         run_n,
                                        int16_t                     do_stats,
                                        int                         alt_cloud_aware,
                                        int                         ipv6_redirect_supported,
                                        int                         exclude_self,
                                        const char                  *site_gid,
                                        struct zpn_balance_redirect_request *req,
                                        struct balance_request_state *state)
{
    int64_t start = epoch_us();

    static const char* log_tag = "debugcmd";
    int res = ZPN_RESULT_NO_ERROR;
    int broker_count = ZPN_CLIENT_MAX_BROKERS;
    char *brokers[ZPN_CLIENT_MAX_BROKERS];
    int brokers_type[ZPN_CLIENT_MAX_BROKERS];
    struct zpn_instance_info broker_info[ZPN_CLIENT_MAX_BROKERS] = {0};
    int broker_info_count = 0;
    int sni_suffix_count = ZPN_CLIENT_MAX_BROKERS;
    char *sni_suffixes[ZPN_CLIENT_MAX_BROKERS];
    struct argo_inet remote_ip;
    struct zpn_balance_redirect_instance_types rbt;
    int feature_grace_distance_enabled = 0;
    int feature_policy_redirect_enabled = 0;
    struct zhash_table *string_table=NULL;
    struct zhash_table *saml_table=NULL;
    struct zhash_table *scim_table=NULL;
    struct zpn_balance_policy_req policy_req = {0};
    int64_t scope_id = scope_gid ? strtoull(scope_gid, NULL, 10) : 0;
    int64_t customer_gid = ZPATH_GID_GET_CUSTOMER_GID(scope_id);
    enum zpn_client_type ctype = client_type_from_str(client_type);
    if (run_n <= 0) run_n = 1;

    if(customer_gid == 0){
        DEBUG_ZDP("Unable to get customer gid from scope_gid(%"PRId64"), using customer_gid=scope_gid\n", scope_id);
        customer_gid = scope_id;
    }

    policy_req.is_client_capable = zpn_client_static_config[ctype].broker_policy_redirect;
    policy_req.is_cc_enabled = zpn_balance_get_redirect_policy_cc_status(customer_gid, ZPN_BROKER_GET_GID());
    policy_req.is_saml_enabled = zpn_balance_get_redirect_policy_saml_status(customer_gid, ZPN_BROKER_GET_GID());
    policy_req.is_scim_enabled = zpn_balance_get_redirect_policy_scim_status(customer_gid, ZPN_BROKER_GET_GID());
    policy_req.is_fallback_broker_enabled = zpn_balance_get_redirect_policy_fallback_broker_status(customer_gid, ZPN_BROKER_GET_GID());

    string_table = zhash_table_alloc(NULL);

    if(!string_table){
        DEBUG_ZDP("Unable to allocate memory for string_table\n");
        goto DONE;
    }

    saml_table = zhash_table_alloc(NULL);

    if(!saml_table){
        DEBUG_ZDP("Unable to allocate memory for saml_table\n");
        goto DONE;
    }

    scim_table = zhash_table_alloc(NULL);

    if(!scim_table){
        DEBUG_ZDP("Unable to allocate memory for scim_table\n");
        goto DONE;
    }

    if(client_cc){
        char tmp_cc[64];
        memset(tmp_cc,'\0',sizeof(tmp_cc));
        snprintf(tmp_cc, sizeof(tmp_cc), "COUNTRY_CODE|%s|true", client_cc);
        tmp_cc[63] = '\0';
        size_t tmp_cc_len = strnlen(tmp_cc, sizeof(tmp_cc));
        res = zhash_table_store(string_table, tmp_cc, tmp_cc_len, 0, tmp_cc);
        if(res){
            DEBUG_ZDP("Unable to store in  string_table\n");
        }
    }

    if(ctype){
        char tmp_cc[64];
        memset(tmp_cc,'\0',sizeof(tmp_cc));
        snprintf(tmp_cc, sizeof(tmp_cc), "CLIENT_TYPE|id|%s", zpn_client_type_string(ctype));
        tmp_cc[63] = '\0';
        size_t tmp_cc_len = strnlen(tmp_cc, sizeof(tmp_cc));
        res = zhash_table_store(string_table, tmp_cc, tmp_cc_len, 0, tmp_cc);
        if(res){
            DEBUG_ZDP("Unable to store in  string_table\n");
        }
    }

    if(client_saml) {
        char tmp_saml[2048] = {0};

        if (!policy_req.is_saml_enabled) {
            DEBUG_ZDP("Saml is not enabled for this customer or broker. Please remove the saml argument and try again\n");
            goto DONE;
        }

        if (debug_normalise_policy_attr(request_state, client_saml, POLICY_ARG_TYPE_SAML, tmp_saml, sizeof(tmp_saml))) {
            DEBUG_ZDP("Unable to normalize the saml parameter:%s\n", client_saml);
            goto DONE;
        }

        size_t tmp_saml_len = strnlen(tmp_saml, sizeof(tmp_saml));
        res = zhash_table_store(saml_table, tmp_saml, tmp_saml_len, 0, tmp_saml);
        if(res){
            DEBUG_ZDP("Unable to store SAML:%s in saml_table\n", client_saml);
        }
    }

    if(client_scim) {
        char tmp_scim[2048] = {0};

        if (!policy_req.is_scim_enabled) {
            DEBUG_ZDP("Scim is not enabled for this customer or broker. Please remove the scim argument and try again\n");
            goto DONE;
        }

        /* Should be in format of idp_gid|SCIM|attr_gid|attr_val */
        if (debug_normalise_policy_attr(request_state, client_scim, POLICY_ARG_TYPE_SCIM, tmp_scim, sizeof(tmp_scim))) {
            DEBUG_ZDP("Unable to normalize the scim parameter:%s\n", client_scim);
            goto DONE;
        }

        size_t tmp_scim_len = strnlen(tmp_scim, sizeof(tmp_scim));
        res = zhash_table_store(scim_table, tmp_scim, tmp_scim_len, 0, tmp_scim);
        if(res){
            DEBUG_ZDP("Unable to store SCIM:%s in scim_table\n", client_scim);
        }
    }

    if(client_scim_group) {
        char tmp_scim[2048] = {0};

        if (!policy_req.is_scim_enabled) {
            DEBUG_ZDP("Scim is not enabled for this customer or broker. Please remove the scim group argument and try again\n");
            goto DONE;
        }

        snprintf(tmp_scim, sizeof(tmp_scim), "%s", client_scim_group);
        tmp_scim[sizeof(tmp_scim) - 1] = '\0';
        size_t tmp_scim_len = strnlen(tmp_scim, sizeof(tmp_scim));
        res = zhash_table_store(scim_table, tmp_scim, tmp_scim_len, 0, tmp_scim);
        if(res){
            DEBUG_ZDP("Unable to store SCIM GROUP:%s in scim_table\n", client_scim_group);
        }
    }

    policy_req.general_context_hash = string_table;
    policy_req.saml_hash = saml_table;
    policy_req.scim_hash = scim_table;

    DEBUG_ZDP("--- Running balance algorithm V2 (%d iterations) ---\n", run_n);
    DEBUG_ZDP("Config for %s - role: %s, mode: %s, single_dc_redir_priv: %d, mdc_pub_pct: %"PRId64"\n",
                g_balance_config.instance_name,
                g_balance_config.balance_role,
                g_balance_config.balance_mode,
                g_balance_config.single_dc_redir_priv,
                g_balance_config.mdc_pub_pct);

    feature_grace_distance_enabled = zpn_balance_get_grace_distance_feature_status(customer_gid, ZPN_BROKER_GET_GID());
    feature_policy_redirect_enabled = zpn_balance_get_redirect_policy_feature_status(customer_gid, ZPN_BROKER_GET_GID(), req);

    //Scope ready is being called to load the policies for redirect policies
    if((scope_gid || customer_gid != 0) && feature_policy_redirect_enabled){
        res = zpn_rule_to_pse_group_register_customer(customer_gid, NULL, NULL, 0);
        if (res) {
            res = ZPN_RESULT_NO_ERROR;
        }

        res = zpn_scope_ready(scope_id, NULL, NULL, 0);
        if (res == ZPN_RESULT_ASYNCHRONOUS) {
            ZDP("Waiting on zpn_scope_ready: %s\n", zpath_result_string(res));
            res = ZPN_RESULT_NO_ERROR;
            goto DONE;
        }
    }

    int cust_alt_cloud_support = (customer_gid ? zpath_instance_is_alt_cloud_enabled_for_customer(customer_gid) : 1);

    int64_t config_val =0;
    /* Construct request */
    req->broker_count = &broker_count,
    req->brokers = brokers;
    req->brokers_type = brokers_type;
    req->instance_info = &broker_info[0];
    req->instance_info_count = &broker_info_count;
    req->client_type = ctype;
    req->customer_gid = customer_gid;
    req->scope_gid = scope_gid ? strtoull(scope_gid, NULL, 10) : customer_gid;
    req->filter_by_site_gid = site_gid ? strtoull(site_gid, NULL, 10) : 0;
    req->trusted_networks = debug_tokenize_trusted_networks(client_mtn);
    req->flag.feature_grace_distance = feature_grace_distance_enabled;
    req->flag.feature_policy_redirect = feature_policy_redirect_enabled;
    req->policy_req = &policy_req;
    req->rbt = &rbt;

    if(req->client_type == zpn_client_type_assistant) {
        config_val = zpn_assistant_multidc_config_override(customer_gid);
        if(config_val) {
            req->multi_dc_redir_capable = zpn_client_static_config[ctype].fohh_redirect_pub_mdc;
            DEBUG_ZDP("Entered the multi dc flag check %d\n",req->multi_dc_redir_capable);
        }
        else {
            req->multi_dc_redir_capable = 0;
        }
    }
    else if(req->client_type == zpn_client_type_private_broker) {
        config_val = zpn_pbroker_multidc_config_override(customer_gid);
        if(config_val) {
            req->multi_dc_redir_capable = zpn_client_static_config[ctype].fohh_redirect_pub_mdc;

        }
        else {
            req->multi_dc_redir_capable = 0;
        }
    }
    else {
        req->multi_dc_redir_capable = zpn_client_static_config[ctype].fohh_redirect_pub_mdc;
    }
    req->log_tag = log_tag;
    req->do_stats = do_stats;
    req->alt_cloud_aware = alt_cloud_aware;
    if (ZPN_BROKER_IS_PUBLIC()) {
        DEBUG_ZDP("Alt-cloud support for broker is %s\n", broker_alt_cloud_support_enabled ? "enabled" : "disabled");
        req->lbb_enabled = zpath_broker_is_lbb_enabled_for_customer(customer_gid);
        req->lbb_supported = zpath_broker_is_lbb_enabled();
        req->is_lp_enabled = ( zpath_get_logical_partition_feature_status() && g_current_profile_version > 0 );
        req->alt_cloud_supported = (broker_alt_cloud_support_enabled && cust_alt_cloud_support);
        rbt.type[PUBLIC_BROKER] = 1;
    } else {
        req->lbb_enabled = 0;
        req->lbb_supported = 0;
        req->alt_cloud_supported = cust_alt_cloud_support;
        req->is_lp_enabled = 0;
        rbt.type[PUBLIC_BROKER] = 0;
    }
    res = zpn_broker_balance_get_ipv6_specific_flags(req, ipv6_redirect_supported, ZPN_BROKER_GET_GID(), "DEBUGGING");
    if (res) goto DONE;
    req->exclude_self = exclude_self;
    req->sni_suffix = sni_suffixes;
    req->sni_suffix_count = &sni_suffix_count;
    rbt.type[PRIVATE_BROKER] = zpn_client_static_config[req->client_type].fohh_redirect_priv_pb;
    rbt.type[PUBLIC_PRIVATE_BROKER] = zpn_client_static_config[req->client_type].fohh_redirect_pub_pb;

    DEBUG_ZDP("Dualstack flags: Enabled:%d, ClientIPv6capable:%d, IPv6-exclusive:%d\n",
              req->ipv6_redirect_enabled, req->ipv6_redirect_capable, req->ipv6_exclusive_wanted);

    if (customer_gid) {
        DEBUG_ZDP("Alt-Cloud support for customer: %"PRId64" is %s\n",
                    customer_gid, (cust_alt_cloud_support ? "enabled" : "disabled"));
    }

    if (exclude_self) {
        DEBUG_ZDP("Excluding current broker from redirect calculation\n");
    }

    DEBUG_ZDP("LBB support for broker is %s\n", req->lbb_supported ? "enabled" : "disabled");
    if (customer_gid) {
        DEBUG_ZDP("LBB support for customer: %"PRId64" is %s\n",
                    customer_gid, (req->lbb_enabled ? "enabled" : "disabled"));
    }

    if (req->is_lp_enabled && req->customer_gid) {
        req->partition_gid = zpath_partition_gid_from_customer_gid(req->customer_gid, NULL);
        DEBUG_ZDP("\nCustomer belongs to partition_gid = %"PRId64", %s\n", req->partition_gid, zpath_partition_gid_to_name(req->partition_gid));
    }

    if (req->trusted_networks) {
        char dump[8000];
        if (argo_object_dump(req->trusted_networks, dump, sizeof(dump), NULL, 1) == ARGO_RESULT_NO_ERROR) {
            DEBUG_ZDP("MTN: %s\n", dump);
        }
    }

    if(client_cc) {
        req->remote_cc[0] = client_cc[0];
        req->remote_cc[1] = client_cc[1];
    }

    /* Fix up client location info */
    if (client_lat && client_lon) {
        req->remote_lon = strtod(client_lon, NULL);
        req->remote_lat = strtod(client_lat, NULL);
    } else if (client_ip && !argo_string_to_inet(client_ip, &remote_ip)) {
        int loc_ret=ZPATH_RESULT_NOT_FOUND;
        char str[ARGO_INET_ADDRSTRLEN];
        if (ZPN_BROKER_IS_PUBLIC()) {
            if (zpn_is_broker_double_precision_geo_feature_pct()) {
                loc_ret = zpath_geoip_lookup_double(&remote_ip, &req->remote_lat, &req->remote_lon, req->remote_cc);
            } else {
                int int_lon=0;
                int int_lat=0;
                loc_ret = zpath_geoip_lookup(&remote_ip, &int_lat, &int_lon, req->remote_cc);
                req->remote_lat = (double)int_lat;
                req->remote_lon = (double)int_lon;
            }
        } else {
            loc_ret = zpath_geoip_lookup_double(&remote_ip, &req->remote_lat, &req->remote_lon, req->remote_cc);
        }
        if (loc_ret != ZPATH_RESULT_NO_ERROR) {
            DEBUG_ZDP("GEO IP Lookup failed for IP = %s \n", argo_inet_generate(str, &remote_ip));
        }
        req->remote_ip = &remote_ip;
        debug_print_ip_info(&remote_ip, request_state);
    }

    if(!client_cc){
        char tmp_cc[64];
        memset(tmp_cc,'\0', sizeof(tmp_cc));
        snprintf(tmp_cc, sizeof(tmp_cc) - 1, "COUNTRY_CODE|%s|true", req->remote_cc);
        size_t tmp_cc_len = strnlen(tmp_cc, sizeof(tmp_cc));
        zhash_table_store(string_table, tmp_cc, tmp_cc_len, 0, tmp_cc);
    }

    zpn_balance_sanitize_remote_location(&req->remote_lat, &req->remote_lon, req->remote_cc);

    for (int iter = 0; iter < run_n; iter++) {
        state->req = req;
        state->trace_f = do_trace ? &zpn_balance_debug_trace : NULL;
        state->trace_f_cookie = request_state;
        state->dbg_stop_after = dbg_stop_after;

        state->gf_state = is_geofenced(req->remote_cc) ? geo_include : geo_exclude;

        /* Run it! */
        res = zpn_balance_redirect_execute(state);
        state->res = res;

        /* if no instances found in the constellation */
        if (res == ZPN_RESULT_NOT_FOUND) {
            ZDP("No brokers in the constellation\n");
            return ZPN_RESULT_NO_ERROR;
        }

        /* if no instances found, flip the geo requirement and run again */
        if (res == ZPN_RESULT_NO_ERROR && (*(req->broker_count) == 0) &&
                state->gf_state == geo_include && !g_balance_config.geofencing_enforced && (auto_flip_geo)) {
            if (iter == 0) {
                debug_print_balance_state(state, request_state, do_all);
            }

            /* Cleanup */
            zpn_balance_redirect_free(state);

            state->gf_state = (state->gf_state == geo_exclude) ? geo_include : geo_exclude;
            DEBUG_ZDP("Re-run with geofencing state %s\n", state->gf_state == geo_include ? "inclusive" : "exclusive");
            DEBUG_ZDP("\n------------------------------------------------\n");

            /* Run it again! */
            res = zpn_balance_redirect_execute(state);
            state->res = res;
        }

        if (iter == 0) { /* Print result only for the first run */
            debug_print_balance_state(state, request_state, do_all);
        } else {
            DEBUG_ZDP("iteration %d: %d brokers [", iter, broker_count);
            for (int c = 0; c < broker_count; c++) DEBUG_ZDP("%s,", req->brokers[c]);
            DEBUG_ZDP("]\n");
        }

        /* Cleanup */
        if (run_n > 1) {
            zpn_balance_redirect_free(state);
        }

        for (int i = 0; i < broker_count; i++)
            ZPN_BALANCE_FREE(brokers[i]); // these strings were copies
    }

    if (run_n > 1) {
        int64_t duration = epoch_us() - start;
        DEBUG_ZDP("Execution time: %"PRId64" us, average %"PRId64" us\n", duration, duration/run_n);
    }

DONE:
    if(string_table)
        zhash_table_free(string_table);
    if(saml_table)
        zhash_table_free(saml_table);
    if(scim_table)
        zhash_table_free(scim_table);

    return res;
}

/*
 * A number of debug commands execute all/part of the balance redirect workflow, ony differ in parameters.
 * Note: use dbg_stop_after to execute the workflow partially.
 */
int debug_run_and_analyze_common(struct zpath_debug_state           *request_state,
                                        const char                  *client_type,
                                        const char                  *scope_gid,
                                        const char                  *client_ip,
                                        const char                  *client_lat,
                                        const char                  *client_lon,
                                        const char                  *client_cc,
                                        const char                  *client_mtn,
                                        const char                  *client_saml,
                                        const char                  *client_scim,
                                        const char                  *client_scim_group,
                                        int                         do_trace,
                                        int                         do_all,
                                        enum dbg_balance_exec_phase  dbg_stop_after,
                                        int                         run_n,
                                        int16_t                     do_stats,
                                        int                         auto_flip_geo,
                                        int                         alt_cloud_aware,
                                        int                         ipv6_redirect_supported,
                                        int                         exclude_self,
                                        const char                  *site_gid)
{
    int res;
    struct zpn_balance_redirect_request req = {0};
    struct balance_request_state state = {0};
    if (run_n <= 0) run_n = 1;

        res = debug_run_and_analyze_common_return_state(request_state,
                                                        client_type,
                                                        scope_gid,
                                                        client_ip,
                                                        client_lat,
                                                        client_lon,
                                                        client_cc,
                                                        client_mtn,
                                                        client_saml,
                                                        client_scim,
                                                        client_scim_group,
                                                        do_trace,
                                                        do_all,
                                                        dbg_stop_after,
                                                        auto_flip_geo,
                                                        run_n,
                                                        do_stats,
                                                        alt_cloud_aware,
                                                        ipv6_redirect_supported,
                                                        exclude_self,
                                                        site_gid,
                                                        &req,
                                                        &state);
    if (run_n == 1) zpn_balance_redirect_free(&state);
    if (req.trusted_networks)
        argo_object_release(req.trusted_networks);

    return res;
}

// Simulate execution of redirecting a client.
static int debug_get_redirect(struct zpath_debug_state *request_state, const char **query_values, int query_value_count, void *object)
{
static int PARAM_CLIENT_TYPE     = 0;
static int PARAM_SCOPE_ID        = 1;
static int PARAM_IP              = 2;
static int PARAM_LON             = 3;
static int PARAM_LAT             = 4;
static int PARAM_CC              = 5;
static int PARAM_MTN             = 6;
static int PARAM_SAML            = 7;
static int PARAM_SCIM            = 8;
static int PARAM_SCIM_GROUP      = 9;
static int PARAM_TRACE           = 10;
static int PARAM_ALL             = 11;
static int PARAM_STATS           = 12;
static int PARAM_ALT_CLOUD_AWARE = 13;
static int PARAM_IPV6_REDIRECT   = 14;
static int PARAM_DBG             = 15;
static int PARAM_EXCLUDE_SELF    = 16;
static int PARAM_SITE_GID        = 17;
static int PARAM_ITER            = 18;

    int dbg_param = 0;

    if(query_values[PARAM_DBG] != NULL) {
        dbg_param = strtoul(query_values[PARAM_DBG], NULL, 10);
    } else {
        dbg_param = 0;
    }

    int iter = query_values[PARAM_ITER] ? atoi(query_values[PARAM_ITER]) : 1;
    int alt_cloud_aware = query_values[PARAM_ALT_CLOUD_AWARE] ? atoi(query_values[PARAM_ALT_CLOUD_AWARE]) : 0;
    int ipv6_redirect_supported = query_values[PARAM_IPV6_REDIRECT] ? atoi(query_values[PARAM_IPV6_REDIRECT]) : 0;
    int exclude_self = query_values[PARAM_EXCLUDE_SELF] ? atoi(query_values[PARAM_EXCLUDE_SELF]) : 0;

    return debug_run_and_analyze_common(request_state,
                                        query_values[PARAM_CLIENT_TYPE],
                                        query_values[PARAM_SCOPE_ID],
                                        query_values[PARAM_IP],
                                        query_values[PARAM_LAT],
                                        query_values[PARAM_LON],
                                        query_values[PARAM_CC],
                                        query_values[PARAM_MTN],
                                        query_values[PARAM_SAML],
                                        query_values[PARAM_SCIM],
                                        query_values[PARAM_SCIM_GROUP],
                                        query_values[PARAM_TRACE] != NULL,
                                        query_values[PARAM_ALL] != NULL,
                                        dbg_param, // run everything
                                        iter,
                                        query_values[PARAM_STATS] ? 1 : 0,
                                        1,
                                        alt_cloud_aware,
                                        ipv6_redirect_supported,
                                        exclude_self,
                                        query_values[PARAM_SITE_GID]);
}


// Debug interface for dumping all available instances for a customer.
static int debug_get_viable_instances(struct zpath_debug_state *request_state,
                                    const char **query_values,
                                    int query_value_count,
                                    void *object)
{
static int PARAM_SCOPE_ID        = 0;
static int PARAM_TRACE           = 1;
static int PARAM_ALL             = 2;
static int PARAM_ALT_CLOUD_AWARE = 3;
static int PARAM_IPV6_REDIRECT   = 4;
int alt_cloud_aware = query_values[PARAM_ALT_CLOUD_AWARE] ? atoi(query_values[PARAM_ALT_CLOUD_AWARE]) : 0;
int ipv6_redirect_supported = query_values[PARAM_IPV6_REDIRECT] ? atoi(query_values[PARAM_IPV6_REDIRECT]) : 0;

    return debug_run_and_analyze_common(request_state,
                                        ct_strings[zpn_client_type_zapp],
                                        query_values[PARAM_SCOPE_ID],
                                        NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL,// ip, lat, lon, cc, mtn, saml, scim, scim group
                                        query_values[PARAM_TRACE] != NULL,
                                        query_values[PARAM_ALL] != NULL,
                                        DBG_EXEC_PHASE_INST_FILTER, 1, 0, 0,
                                        alt_cloud_aware,
                                        ipv6_redirect_supported,
                                        0,
                                        0); // stop after filtering instances
}

// Debug interface for dumping pse groups matching Customer redirect policy.
static int debug_redirect_policy_check(struct zpath_debug_state *request_state,
                             const char **query_values,
                             int query_value_count,
                             void *object)
{
static int PARAM_SCOPE_ID        = 0;
static int PARAM_CLIENT_TYPE     = 1;
static int PARAM_CC              = 2;
static int PARAM_SAML            = 3;
static int PARAM_SCIM            = 4;
static int PARAM_SCIM_GROUP      = 5;
static int PARAM_TRACE           = 6;
static int PARAM_ALL             = 7;
static int PARAM_ALT_CLOUD_AWARE = 8;
static int PARAM_IPV6_REDIRECT   = 9;
int alt_cloud_aware = query_values[PARAM_ALT_CLOUD_AWARE] ? atoi(query_values[PARAM_ALT_CLOUD_AWARE]) : 0;
int ipv6_redirect_supported = query_values[PARAM_IPV6_REDIRECT] ? atoi(query_values[PARAM_IPV6_REDIRECT]) : 0;

    return debug_run_and_analyze_common(request_state,
                                        query_values[PARAM_CLIENT_TYPE]?query_values[PARAM_CLIENT_TYPE]:ct_strings[zpn_client_type_zapp],
                                        query_values[PARAM_SCOPE_ID],
                                        NULL, NULL, NULL, // ip, lat, lon
                                        query_values[PARAM_CC],
                                        NULL,
                                        query_values[PARAM_SAML],
                                        query_values[PARAM_SCIM],
                                        query_values[PARAM_SCIM_GROUP],
                                        query_values[PARAM_TRACE] != NULL,
                                        query_values[PARAM_ALL] != NULL,
                                        DBG_EXEC_PHASE_REDIRECT_POLICY, 1, 0, 0,
                                        alt_cloud_aware,
                                        ipv6_redirect_supported,
                                        0,
                                        0); // stop after instances are dropped into buckets and DCs
}

// Debug interface for dumping dc list for a customer.
static int debug_get_dc_list(struct zpath_debug_state *request_state,
                             const char **query_values,
                             int query_value_count,
                             void *object)
{
static int PARAM_SCOPE_ID        = 0;
static int PARAM_CLIENT_TYPE     = 1;
static int PARAM_CC              = 2;
static int PARAM_MTN             = 3;
static int PARAM_SAML            = 4;
static int PARAM_SCIM            = 5;
static int PARAM_SCIM_GROUP      = 6;
static int PARAM_TRACE           = 7;
static int PARAM_ALL             = 8;
static int PARAM_ALT_CLOUD_AWARE = 9;
static int PARAM_IPV6_REDIRECT   = 10;
int alt_cloud_aware = query_values[PARAM_ALT_CLOUD_AWARE] ? atoi(query_values[PARAM_ALT_CLOUD_AWARE]) : 0;
int ipv6_redirect_supported = query_values[PARAM_IPV6_REDIRECT] ? atoi(query_values[PARAM_IPV6_REDIRECT]) : 0;

    return debug_run_and_analyze_common(request_state,
                                        query_values[PARAM_CLIENT_TYPE]?query_values[PARAM_CLIENT_TYPE]:ct_strings[zpn_client_type_zapp],
                                        query_values[PARAM_SCOPE_ID],
                                        NULL, NULL, NULL,  // ip, lat, lon
                                        query_values[PARAM_CC],
                                        query_values[PARAM_MTN],
                                        query_values[PARAM_SAML],
                                        query_values[PARAM_SCIM],
                                        query_values[PARAM_SCIM_GROUP],
                                        query_values[PARAM_TRACE] != NULL,
                                        query_values[PARAM_ALL] != NULL,
                                        DBG_EXEC_PHASE_DC_BUCKET, 1, 0, 0,
                                        alt_cloud_aware,
                                        ipv6_redirect_supported,
                                        0,
                                        0); // stop after instances are dropped into buckets and DCs
}

// Debug interface for OPS to check and alert on DC load.
static int debug_get_dc_load(struct zpath_debug_state *request_state,
                             const char **query_values,
                             int query_value_count,
                             void *object)
{
    int json = (query_values[0] != NULL);
    int alt_cloud_aware = (query_values[1] != NULL);
    int ipv6_redirect_supported = (query_values[2] != NULL);
    int broker_count = ZPN_CLIENT_MAX_BROKERS;
    char *brokers[ZPN_CLIENT_MAX_BROKERS];
    int sni_suffix_count = ZPN_CLIENT_MAX_BROKERS;
    char *sni_suffixes[ZPN_CLIENT_MAX_BROKERS];

    struct zpn_balance_redirect_instance_types rbt;
    rbt.type[PRIVATE_BROKER] = 0;
    rbt.type[PUBLIC_PRIVATE_BROKER] = 0;
    rbt.type[PUBLIC_BROKER] = 1;

    struct zpn_balance_redirect_request req = (struct zpn_balance_redirect_request) {
                                            .broker_count = &broker_count,
                                            .brokers = brokers,
                                            .client_type = zpn_client_type_zapp,
                                            .remote_ip = NULL,
                                            .remote_lat = 0,
                                            .remote_lon = 0,
                                            .customer_gid = 0,
                                            .scope_gid = 0,
                                            .trusted_networks = NULL,
                                            .rbt = &rbt,
                                            .multi_dc_redir_capable = 1,
                                            .is_lp_enabled = zpath_get_logical_partition_feature_status() && ( g_current_profile_version > 0 ),
                                            .log_tag = NULL,
                                            .sni_suffix = sni_suffixes,
                                            .sni_suffix_count = &sni_suffix_count,
                                            .alt_cloud_supported = broker_alt_cloud_support_enabled,
                                            .alt_cloud_aware = alt_cloud_aware,
                                            .ipv6_redirect_supported = ipv6_redirect_supported,
                                            .exclude_self = 0,
                                            .do_stats = 0 };

    zpn_balance_sanitize_remote_location(&req.remote_lat, &req.remote_lon, req.remote_cc); //load with my own location
    struct balance_request_state state = (struct balance_request_state) {
                                            .req = &req,
                                            .gf_state = geo_exclude,
                                            .dbg_stop_after = DBG_EXEC_PHASE_DC_BUCKET };

    zpn_balance_redirect_execute(&state);

    const struct dc_bucket *bucket = &state.dc_buckets.bucket[BT_PUBL];
    if (json) {
        DEBUG_ZDP("[\n");
    }
    for (int i = 0; i < bucket->dc_count; i++) {
        const struct data_center *dc = &bucket->dcs[i];
        if (json) {
            DEBUG_ZDP("  { \"name\": \"%s\", ", dc->short_name);
            DEBUG_ZDP("\"lat\": %.2f,", dc->site.lat);
            DEBUG_ZDP("\"lon\": %.2f,", dc->site.lon);
            DEBUG_ZDP("\"cc\": \"%s\",", dc->site.cc);
            DEBUG_ZDP("\"instances\": %u,", dc->inst_total);
            DEBUG_ZDP("\"capacity\": %u,", dc->capacity_total);
            DEBUG_ZDP("\"load\": %u }%s\n", dc->effective_load, (i == (bucket->dc_count - 1)) ? "" : ",");
        } else {
            DEBUG_ZDP("%-10s lat: %" FMT_COORDINATE ", lon: %" FMT_COORDINATE ", cc: %3s, instances: %3u, capacity: %4u, load: %4u\n",
                dc->short_name, dc->site.lat, dc->site.lon, dc->site.cc, dc->inst_total, dc->capacity_total, dc->effective_load);
        }
    }
    if (json) {
        DEBUG_ZDP("]\n");
    }
    zpn_balance_redirect_free(&state);

    return ZPN_RESULT_NO_ERROR;
}

/*
 * Debug interfaces to balance configuration.
 */
static int debug_show_config(struct zpath_debug_state *request_state, const char **query_values, int query_value_count, void *object)
{
    struct site my_site = {0};
    get_my_site(&my_site);
    int64_t double_geo_pct;

    if (ZPN_BROKER_IS_PUBLIC()) {
        double_geo_pct = zpn_get_broker_double_precision_geo_feature_pct();
    } else {
        double_geo_pct = 100;
    }
    struct balance_request_configuration config = {};
    zpn_balance_get_instance_selection_retry_configuration(&config);
    zpn_balance_get_client_counts_configuration(&config);

    DEBUG_ZDP("gid:                     %" PRId64 "\n", g_balance_config.gid);
    DEBUG_ZDP("instance_name:           %s\n", g_balance_config.instance_name);
    DEBUG_ZDP("balance_role:            %s\n", g_balance_config.balance_role);
    DEBUG_ZDP("balance_mode:            %s\n", g_balance_config.balance_mode);
    DEBUG_ZDP("double_precision_pct:    %"PRId64"\n", double_geo_pct);
    DEBUG_ZDP("lat:                     %+7.2f\n", my_site.lat);
    DEBUG_ZDP("lon:                     %+7.2f\n", my_site.lon);
    DEBUG_ZDP("cc:                      %s\n", my_site.cc);
    DEBUG_ZDP("single_dc_redir_priv:    %s\n", g_balance_config.single_dc_redir_priv ? "true" : "false");
    DEBUG_ZDP("mdc_pub_pct:             %"PRId64"\n", g_balance_config.mdc_pub_pct);
    DEBUG_ZDP("mdc_max_pdc_load:        %"PRId64"\n", g_balance_config.mdc_max_pdc_load);
    DEBUG_ZDP("mdc_max_gf_pdc_load:     %"PRId64"\n", g_balance_config.mdc_max_gf_pdc_load);
    DEBUG_ZDP("mdc_max_sdc_load:        %"PRId64"\n", g_balance_config.mdc_max_sdc_load);
    DEBUG_ZDP("mdc_max_gf_sdc_load:     %"PRId64"\n", g_balance_config.mdc_max_gf_sdc_load);
    DEBUG_ZDP("mdc_max_sdc_count:       %"PRId64"\n", g_balance_config.mdc_max_sdc_count);
    DEBUG_ZDP("mdc_max_sdc_dist_miles:  %"PRId64"\n", g_balance_config.mdc_max_sdc_dist_miles);
    DEBUG_ZDP("mdc_sdc_cap_target_pct:  %"PRId64"\n", g_balance_config.mdc_sdc_cap_target_pct);
    DEBUG_ZDP("geofencing_enforced:     %"PRId64"\n", g_balance_config.geofencing_enforced);
    DEBUG_ZDP("restart_time:            %ld\n", (long)g_balance_config.restart_time);
    DEBUG_ZDP("load_skew:               %ld\n", (long)g_balance_config.load_skew);
    DEBUG_ZDP("report_age_reduction_s:  %ld\n", (long)g_balance_config.report_age_reduction_s);
    DEBUG_ZDP("report_age_exclusion_s:  %ld\n", (long)g_balance_config.report_age_exclusion_s);
    DEBUG_ZDP("inst_list_alloc:         %ld\n", (long)g_balance_config.inst_list_alloc);
    DEBUG_ZDP("inst_list_growth:        %ld\n", (long)g_balance_config.inst_list_growth);
    DEBUG_ZDP("retries_enabled:         %d\n", config.retries.enabled ? 1 : 0);
    DEBUG_ZDP("retry_max:               %"PRId64"\n", config.retries.max);
    DEBUG_ZDP("retry_threshold          %"PRId64"\n", config.retries.threshold);
    DEBUG_ZDP("clients_hard_disabled    %d\n", config.client_counts.hard_disabled ? 1 : 0);
    DEBUG_ZDP("se_clients_rate          %"PRId64"\n", config.client_counts.se_rate);
    DEBUG_ZDP("pse_clients_rate         %"PRId64"\n", config.client_counts.pse_rate);
    DEBUG_ZDP("client_load_threshold    %"PRId64"\n", config.client_counts.min_load_threshold);
    DEBUG_ZDP("client_count_threshold   %"PRId64"\n", config.client_counts.min_clients_threshold);

    /* Get instance dual_stack state if instance GID is available for a public broker */
    if (ZPN_BROKER_IS_PUBLIC() && g_balance_config.gid) {
        struct zpath_instance *instance = zpath_instance_get_no_locking(g_balance_config.gid);
        int dualstack = instance ? instance->dual_stack : -1;
        DEBUG_ZDP("dualstack:               %d\n", dualstack);
    }

    return ZPN_RESULT_NO_ERROR;
}

static int debug_set_config(struct zpath_debug_state *request_state, const char **query_values, int query_value_count, void *object)
{
    int inst_list_alloc  = query_values[0] ? atoi(query_values[0]) : 0;
    int inst_list_growth = query_values[1] ? atoi(query_values[1]) : 0;
    if (inst_list_alloc < 0 || inst_list_alloc > 10000 || inst_list_growth < 0 || inst_list_growth > 10000)
        return ZPN_RESULT_BAD_ARGUMENT;

    DEBUG_ZDP("Current Config:\n");
    debug_show_config(request_state, query_values, query_value_count, object);

    if (inst_list_alloc)  g_balance_config.inst_list_alloc = (size_t)inst_list_alloc;
    if (inst_list_growth) g_balance_config.inst_list_growth = (size_t)inst_list_growth;

    DEBUG_ZDP("Updated Config:\n");
    debug_show_config(request_state, query_values, query_value_count, object);
    return ZPN_RESULT_NO_ERROR;
}



int zpn_balance_set_pse_grp_exclusive_for_business_continuity_flag_do(struct zpath_debug_state *request_state, const char **query_values, int query_value_count, void *object, int is_ut)
{
    int64_t pse_grp_gid;
    int flag;
    int res;
    struct zpn_private_broker_group *grp;

    if (query_value_count != 2 || !query_values[0] || !query_values[1]) {
        DEBUG_ZDP("Must provide PSE group GID and flag value\n");
        return ZPN_RESULT_NO_ERROR;
    }

    pse_grp_gid = strtoul(query_values[0], NULL, 0);
    if (!pse_grp_gid) {
        DEBUG_ZDP("Must provide a valid PSE group gid\n");
        return ZPN_RESULT_NO_ERROR;
    }

    if (!is_ut) {
        res = zpn_pbroker_group_get_by_gid(pse_grp_gid, &grp, 0, NULL, NULL, 0);
        if (res) {
            DEBUG_ZDP("Must provide a valid PSE group gid\n");
            return ZPN_RESULT_NO_ERROR;
        }
    }

    flag = strtol(query_values[1], NULL, 0);
    if (flag != 0 && flag != 1) {
        DEBUG_ZDP("Must provide a valid value for flag\n");
        return ZPN_RESULT_NO_ERROR;
    }

    res = zpn_pbroker_set_pse_grp_exclusive_for_business_continuity_flag(pse_grp_gid, flag);

    if (flag) {
        if (!res) {
            DEBUG_ZDP("Business Continuity exclusive flag successfully enabled for PSE group gid %"PRId64"\n", pse_grp_gid);
        } else {
            DEBUG_ZDP("Business Continuity exclusive flag couldn't be enabled for PSE group gid %"PRId64"\n", pse_grp_gid);
        }
    } else {
        if (!res) {
            DEBUG_ZDP("Business Continuity exclusive flag successfully disabled for PSE group gid %"PRId64"\n", pse_grp_gid);
        } else if (res == ZHASH_RESULT_NOT_FOUND) {
            DEBUG_ZDP("Business Continuity exclusive flag is not enabled for PSE group gid %"PRId64"\n", pse_grp_gid);
        }
    }

    return ZPN_RESULT_NO_ERROR;
}


int zpn_balance_set_pse_grp_exclusive_for_business_continuity_flag(struct zpath_debug_state *request_state, const char **query_values, int query_value_count, void *object) {
    return zpn_balance_set_pse_grp_exclusive_for_business_continuity_flag_do(request_state, query_values, query_value_count, object, 0);
}

#define PARAM_ALL_MEANS         "Print out all involved IDs and instances (up to 16 if not specified)."
#define PARAM_SCOPE_ID_MEANS    "Scope ID to which the customer belongs. For default scope, use customer_gid."
#define PARAM_TRACE_MEANS       "Print debug log in the output."
#define PARAM_MTN_MEANS         "Comma-separated list of UDIDs (no spaces)."
#define PARAM_DBG_STOP_AFTER_MEANS "Stop redirect logic after certain step, give integer value, options: DBG_EXEC_PHASE_IGNORE_ME = 0\n " \
                                   "                                                                                             DBG_EXEC_PHASE_INST_LOAD = 1  \n" \
                                   "                                                                                              DBG_EXEC_PHASE_REDIRECT_POLICY = 2\n" \
                                   "                                                                                              DBG_EXEC_PHASE_INST_FILTER = 3\n" \
                                   "                                                                                              DBG_EXEC_PHASE_DC_BUCKET = 4\n" \
                                   "                                                                                              DBG_EXEC_PHASE_DC_SELECT = 5 \n" \
                                   "                                                                                              DBG_EXEC_PHASE_INST_SELECT = 6\n"

#define PARAM_STATS_MEANS       "If included, will add stats."
int zpn_broker_balance_debug_init() {
    int res;

    zpath_debug_add_allocator(&zpn_balance_allocator, "zpn_balance");

    res = zpath_debug_add_safe_read_command("Get a list of instances to redirect to for a client",
                "/balance/redirect",
                debug_get_redirect,
                NULL,
                "client_type",        client_type_str_concat,
                "scope_gid",           PARAM_SCOPE_ID_MEANS,
                "remote_ip",          "IP address of the client. Determines client's location if lon/lat parameters are not provided.",
                "lon",                "Longitude of the client. Also provide lat and cc.",
                "lat",                "Latitude of the client. Also provide lon and cc.",
                "cc",                 "Country code of the client. Also provide lon and lat. Will override cc from remote_ip for policies.",
                "trusted_networks",   PARAM_MTN_MEANS,
                "saml",               "saml attribute for the client, supporting 1 saml attribute, should be in formal idp_gid|SAML|attr_name|attr_val",
                "scim",               "scim attribute for the client, supporting 1 scim attribute, should be in formal idp_gid|SCIM|attr_gid|attr_val",
                "scim_group",         "scim_group attribute for the client, supporting 1 scim attribute, should be in formal idp_gid|SCIM_GROUP|gid",
                "trace",              PARAM_TRACE_MEANS,
                "all",                PARAM_ALL_MEANS,
                "stats",              PARAM_STATS_MEANS,
                "alt_cloud_aware",    "alternate cloud aware capability",
                "ipv6_redirect",      "ipv6 redirect capability",
                "dbg_stop_after",     PARAM_DBG_STOP_AFTER_MEANS,
                "exclude_self",       "exclude this broker from redirect calculations",
                "site_gid",           "filter by site gid",
                NULL);
    if (res) return res;

    /* not a safe command, otherwise parameter 'n' can be coalesced with the previous command */
    res = zpath_debug_add_write_command("Get a list of instances to redirect to for a client",
                "/balance/redirect_n",
                debug_get_redirect,
                NULL,
                "client_type",        client_type_str_concat,
                "scope_gid",           PARAM_SCOPE_ID_MEANS,
                "remote_ip",          "IP address of the client. Determines client's location if lon/lat parameters are not provided.",
                "lon",                "Longitude of the client. Also provide lat and cc.",
                "lat",                "Latitude of the client. Also provide lon and cc.",
                "cc",                 "Country code of the client. Also provide lon and lat.",
                "trusted_networks",   PARAM_MTN_MEANS,
                "saml",               "saml attribute for the client, supporting 1 saml attribute, should be in formal idp_gid|SAML|attr_name|attr_val",
                "scim",               "scim attribute for the client, supporting 1 scim attribute, should be in formal idp_gid|SCIM|attr_gid|attr_val",
                "scim_group",         "scim_group attribute for the client, supporting 1 scim attribute, should be in formal idp_gid|SCIM_GROUP|gid",
                "trace",              PARAM_TRACE_MEANS,
                "all",                PARAM_ALL_MEANS,
                "stats",              PARAM_STATS_MEANS,
                "alt_cloud_aware",    "alternate cloud aware capability",
                "ipv6_redirect",      "ipv6 redirect capability",
                "dbg_stop_after",     PARAM_DBG_STOP_AFTER_MEANS,
                "exclude_self",       "exclude this broker from redirect calculations",
                "site_gid",           "filter by site gid",
                "n",                  "Number of times to execute said redirect, used for performance test.",
                NULL);
    if (res) return res;

    res = zpath_debug_add_safe_read_command("Get a list of viable brokers for a customer",
                "/balance/viable_instances",
                debug_get_viable_instances,
                NULL,
                "scope_gid",           PARAM_SCOPE_ID_MEANS,
                "trace",              PARAM_TRACE_MEANS,
                "all",                PARAM_ALL_MEANS,
                "alt_cloud_aware",    "alternate cloud aware capability",
                "ipv6_redirect",      "ipv6 redirect capability",
                NULL);
    if (res) return res;

    res = zpath_debug_add_safe_read_command("Dump a global/customer redirect DC list",
                "/balance/dc_list",
                debug_get_dc_list,
                NULL,
                "scope_gid",           PARAM_SCOPE_ID_MEANS,
                "client_type",        client_type_str_concat,
                "cc",                 "Country code of the client. Will override cc from remote_ip for policies.",
                "trusted_networks",   PARAM_MTN_MEANS,
                "saml",               "saml attribute for the client, supporting 1 saml attribute, should be in formal idp_gid|SAML|attr_name|attr_val",
                "scim",               "scim attribute for the client, supporting 1 scim attribute, should be in formal idp_gid|SCIM|attr_gid|attr_val",
                "scim_group",         "scim_group attribute for the client, supporting 1 scim attribute, should be in formal idp_gid|SCIM_GROUP|gid",
                "trace",              PARAM_TRACE_MEANS,
                "all",                PARAM_ALL_MEANS,
                "alt_cloud_aware",    "alternate cloud aware capability",
                "ipv6_redirect",      "ipv6 redirect capability",
                NULL);
    if (res) return res;

    res = zpath_debug_add_safe_read_command("Get a list of PSE groups matches with Customer's redirect policy",
                "/balance/redirect_policy_check",
                debug_redirect_policy_check,
                NULL,
                "scope_gid",           PARAM_SCOPE_ID_MEANS,
                "client_type",        client_type_str_concat,
                "cc",                 "Country code of the client. Will override cc from remote_ip for policies.",
                "saml",               "saml attribute for the client, supporting 1 saml attribute, should be in formal idp_gid|SAML|attr_name|attr_val",
                "scim",               "scim attribute for the client, supporting 1 scim attribute, should be in formal idp_gid|SCIM|attr_gid|attr_val",
                "scim_group",         "scim_group attribute for the client, supporting 1 scim attribute, should be in formal idp_gid|SCIM_GROUP|gid",
                "trace",              PARAM_TRACE_MEANS,
                "all",                PARAM_ALL_MEANS,
                "alt_cloud_aware",    "alternate cloud aware capability",
                "ipv6_redirect",      "ipv6 redirect capability",
                NULL);
    if (res) return res;

    res = zpath_debug_add_safe_read_command("Compute the effective load of public DCs",
                "/balance/dc_load",
                debug_get_dc_load,
                NULL,
                "json",                "Output JSON format, text otherwise",
                "alt_cloud_aware",     "alternate cloud aware capability",
                "ipv6_redirect ",      "ipv6 redirect capability",
                NULL);
    if (res) return res;

    res = zpath_debug_add_safe_read_command("show current balance configuration",
                "/balance/config",
                debug_show_config,
                NULL,
                NULL);
    if (res) return res;

    res = zpath_debug_add_write_command("set current balance configuration",
                "/balance/config/set",
                debug_set_config,
                NULL,
                "inst_list_alloc",       "viable instance list initial allocation",
                "inst_list_growth",      "viable instance list growth",
                NULL);
    if (res) return res;

    res = zpath_debug_add_write_command("Turn on/off flag to make PSE group exclusive for Business Continuity",
        "/balance/broker/pse_grp_exclusive_for_business_continuity",
        zpn_balance_set_pse_grp_exclusive_for_business_continuity_flag,
        NULL,
        "grp_gid", "PSE group GID",
        "flag", "Enable(1)/Disable(0) PSE groups exclusive for PCC",
        NULL);
    return res;
}
