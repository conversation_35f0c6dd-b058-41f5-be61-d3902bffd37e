/*
 * broker_redirect_policy_tests.cpp. Copyright (C) 2025 Zscaler, Inc. All Rights Reserved
 */

#include <gtest/gtest.h>
#include <gmock/gmock.h>

#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wunused-parameter"
#elif defined __GNUC__
#pragma GCC diagnostic push
#pragma GCC diagnostic ignored "-Wunused-parameter"
#endif // __clang__

#include <cstdint>

extern "C" {
#include "zpn/zbalance/zpn_balance_private.h"
#include "zpn/zpn_rule_to_pse_group.h"
};

#include "test_misc/ZscalerMockBase.h"
#include "zpn/zbalance/gtests/zpn_balance_redirect_policy_tests/mocks/zpn_redirect_policy_mock.h"

using testing::_;
using testing::AllOf;
using testing::DoAll;
using testing::Eq;
using testing::Field;
using testing::InSequence;
using testing::Invoke;
using testing::IsNull;
using testing::Not;
using testing::NotNull;
using testing::Pointee;
using testing::Return;
using testing::SaveArg;
using testing::SaveArgPointee;
using testing::SetArgPointee;
using testing::StrEq;
using testing::WithArg;
using testing::StrictMock;

using namespace std;

class ZpnBrokerRedirectPolicyMockBase
        : public StrictMock<ZpnRedirectPolicyMock> {};

class ZpnBrokerRedirectPolicyTester
        : public ZscalerMockBase<ZpnBrokerRedirectPolicyMockBase>, public ZpnRedirectPolicyMockBase {
public:
    void SetUp() override {
        ZscalerMockBase::SetUp();
        ZpnRedirectPolicyMockBase::SetUp();
        rbt.type[PUBLIC_BROKER] = 1;

        memset(GetState(), 0, sizeof(struct balance_request_state));
        GetState()->req = &req;
        GetState()->gf_state = geo_exclude;
        GetState()->viable_instance_count = 4;
        GetState()->viable_instance_capacity = 1024;
        GetState()->viable_instances = vi;


        //generate dummy viable instances
        random_gid = 289397352052031866Ull;
        for (size_t i = 0; i < GetState()->viable_instance_count; i++) {
            vi[i].gid = random_gid++;
            vi[i].balance_role = role;
            vi[i].balance_mode = mode;
            vi[i].modified_time_s = epoch_s();
            vi[i].personality = ZPN_INSTANCE_TYPE_PRIVATE_BROKER;
            snprintf(vi[i].is_public, sizeof(vi[i].is_public), "%s", "FALSE");
            vi[i].brk_status = RUNNING;
            vi[i].exclude = 0;
            vi[i].exclusion_reason = excl_none;
        }

        //Setup 4 PSEs in India location, 2 PSEs in each group
        for (int i = 0; i < 2; i++) {
            vi[i].site.lat = 12.971598699999999;
            vi[i].site.lon = 77.594562699999997;
            snprintf(vi[i].site.cc, sizeof(vi[i].site.cc), "%s", "IN");
            vi[i].site_gid = 289397352052031509Ull;
            vi[i].pse_grp_gid = 289397352052031774Ull;
            vi[i].pse_grp_exclusive_for_business_continuity = 0;
        }

        for (int i = 2; i < 4; i++) {
            vi[i].site.lat = 12.971598699999999;
            vi[i].site.lon = 77.594562699999997;
            snprintf(vi[i].site.cc, sizeof(vi[i].site.cc), "%s", "IN");
            vi[i].site_gid = 289397352052031509Ull;
            vi[i].pse_grp_gid = 289397352052031930Ull;
            vi[i].pse_grp_exclusive_for_business_continuity = 0;
        }

        rule_to_psegrp[0] = (struct zpn_rule_to_pse_group *) ZPN_CALLOC(sizeof(struct zpn_rule_to_pse_group));
        rule_to_psegrp[1] = (struct zpn_rule_to_pse_group *) ZPN_CALLOC(sizeof(struct zpn_rule_to_pse_group));

    }

    void TearDown() override {
        ZscalerMockBase::TearDown();
        ZpnRedirectPolicyMockBase::TearDown();
        ZPN_FREE(rule_to_psegrp[0]);
        ZPN_FREE(rule_to_psegrp[1]);
    }

    void populate_rules_to_pse_group() {
        rule_to_psegrp[0]->id = 1;
        rule_to_psegrp[0]->customer_gid = 289397352052031488Ull;
        rule_to_psegrp[0]->pse_group_gid = 289397352052031774Ull;

        rule_to_psegrp[1]->id = 2;
        rule_to_psegrp[1]->customer_gid = 289397352052031488Ull;
        rule_to_psegrp[1]->pse_group_gid = 289397352052031930ull;
    }

    void populate_rule_to_pse_group_for_no_match() {
        rule_to_psegrp[0]->id = 1;
        rule_to_psegrp[0]->customer_gid = 289397352052031489Ull;
        rule_to_psegrp[0]->pse_group_gid = 289397352052031775Ull;
    }

    void populate_one_dummy_rule_to_pse_group() {
        rule_to_psegrp[0]->id = 1;
        rule_to_psegrp[0]->customer_gid = 289397352052031488Ull;
        rule_to_psegrp[0]->pse_group_gid = 289397352052031774Ull;
    }

    void set_pse_grp_exclusive_for_business_continuity_for_instances(int first_two, int last_two) {
        for (int i = 0; i < 2; i++) {
            vi[i].pse_grp_exclusive_for_business_continuity = first_two;
        }
        for (int i = 2; i < 4; i++) {
            vi[i].pse_grp_exclusive_for_business_continuity = last_two;
        }
    }

    struct zpn_rule_to_pse_group* GetRuleToPseGrpGid(int i) {
        return rule_to_psegrp[i];
    }

protected:
    struct zpn_rule_to_pse_group *rule_to_psegrp[2];
};

extern "C" {
    int zpn_balance_policy_check_redirect_policy(struct balance_request_state *state,
                                                enum zpe_access_action *matched_action,
                                                int64_t *matched_rule,
                                                struct zpn_rule_to_pse_group **rule_to_pse_group,
                                                size_t *num_pse_group) __attribute__((weak));
    MOCK_ZPN_POLICY_CHECK_REDIRECT_POLICY(ZpnBrokerRedirectPolicyTester);

    int filter_by_scope(struct balance_request_state *state) __attribute__((weak));
    MOCK_FILTER_BY_SCOPE(ZpnBrokerRedirectPolicyTester);
}

ACTION_P(SetRuleToPseGroup, ptr, count) {
    for (int i = 0; i < count; i++) {
        arg0[i] = ptr[i];
        arg0[i] = ptr[i];
    }
}

ACTION(SetRuleToPseGroupNull) {
    arg0[0] = NULL;
}

TEST_F(ZpnBrokerRedirectPolicyTester, RedirectPolicyDefaultBusinessConExcFlagFalseForAllInst) {
    set_pse_grp_exclusive_for_business_continuity_for_instances(0, 0);

    EXPECT_CALL(*get_mock(),
                zpn_balance_policy_check_redirect_policy(_, _, _, _, _))
                .Times(1)
                .WillRepeatedly(DoAll(SetArgPointee<1>(zpe_access_action_redirect_default), SetArgPointee<2>(0), WithArg<3>(SetRuleToPseGroupNull()), SetArgPointee<4>(0), Return(0)));
    EXPECT_CALL(*get_mock(), filter_by_scope(_)).Times(1).WillRepeatedly(DoAll(Return(0)));

    zpn_balance_policy_execute(GetState());
    zpn_balance_inst_filter_execute(GetState());


    //Filter should have happened based on site gid.
    for (size_t i = 0; i < GetState()->viable_instance_count; i++) {
        EXPECT_EQ(GetState()->viable_instances[i].exclude, 0);
        EXPECT_EQ(GetState()->viable_instances[i].exclusion_reason, excl_none);
    }
}

TEST_F(ZpnBrokerRedirectPolicyTester, RedirectPolicyDefaultBusinessConExcFlagTrueForAllInst) {

    set_pse_grp_exclusive_for_business_continuity_for_instances(1, 1);

    EXPECT_CALL(*get_mock(),
                zpn_balance_policy_check_redirect_policy(_, _, _, _, _))
                .Times(1)
                .WillRepeatedly(DoAll(SetArgPointee<1>(zpe_access_action_redirect_default), SetArgPointee<2>(0), WithArg<3>(SetRuleToPseGroupNull()), SetArgPointee<4>(0), Return(0)));
    EXPECT_CALL(*get_mock(), filter_by_scope(_)).Times(1).WillRepeatedly(DoAll(Return(0)));

    zpn_balance_policy_execute(GetState());
    zpn_balance_inst_filter_execute(GetState());

    //Filter should have happened based on site gid and business continuity exclusive flag
    for (size_t i = 0; i < GetState()->viable_instance_count; i++) {
        EXPECT_EQ(GetState()->viable_instances[i].exclude, 1);
        EXPECT_EQ(GetState()->viable_instances[i].exclusion_reason, excl_inst_exclusive_for_business_continuity);
    }
}

TEST_F(ZpnBrokerRedirectPolicyTester, RedirectPolicyDefaultBusinessConExcFlagTrueForAllInst2) {

    // The following init function is called to initialize the hash
    // debug_pse_groups_exclusive_for_business_continuity
    zpn_pbroker_debug_pse_groups_exclusive_for_business_continuity_hash_init();

    const char *query_values1[] = { "289397352052031774Ull", "1"};
    const char *query_values2[] = { "289397352052031930Ull", "1"};
    zpn_balance_set_pse_grp_exclusive_for_business_continuity_flag_do(NULL, query_values1, 2, NULL, 1);
    zpn_balance_set_pse_grp_exclusive_for_business_continuity_flag_do(NULL, query_values2, 2, NULL, 1);
    EXPECT_EQ(zpn_pbroker_pse_grp_exclusive_for_business_continuity_flag_is_set(vi[0].pse_grp_gid), 1);
    EXPECT_EQ(zpn_pbroker_pse_grp_exclusive_for_business_continuity_flag_is_set(vi[2].pse_grp_gid), 1);

    const char *query_values3[] = { "289397352052031774Ull", "0"};
    zpn_balance_set_pse_grp_exclusive_for_business_continuity_flag_do(NULL, query_values3, 2, NULL, 1);
    EXPECT_EQ(zpn_pbroker_pse_grp_exclusive_for_business_continuity_flag_is_set(vi[0].pse_grp_gid), 0);
    EXPECT_EQ(zpn_pbroker_pse_grp_exclusive_for_business_continuity_flag_is_set(vi[2].pse_grp_gid), 1);

    const char *query_values4[] = { "289397352052031930Ull", "0"};
    zpn_balance_set_pse_grp_exclusive_for_business_continuity_flag_do(NULL, query_values4, 2, NULL, 1);
    EXPECT_EQ(zpn_pbroker_pse_grp_exclusive_for_business_continuity_flag_is_set(vi[0].pse_grp_gid), 0);
    EXPECT_EQ(zpn_pbroker_pse_grp_exclusive_for_business_continuity_flag_is_set(vi[2].pse_grp_gid), 0);

    // pass wrong value for flag, flag only accepts 0/1
    query_values1[1] = "11";
    zpn_balance_set_pse_grp_exclusive_for_business_continuity_flag_do(NULL, query_values1, 2, NULL, 1);
    EXPECT_EQ(zpn_pbroker_pse_grp_exclusive_for_business_continuity_flag_is_set(vi[0].pse_grp_gid), 0);

    // pass wrong value for query_value_count as 3
    query_values1[1] = "1";
    zpn_balance_set_pse_grp_exclusive_for_business_continuity_flag_do(NULL, query_values1, 3, NULL, 1);
    EXPECT_EQ(zpn_pbroker_pse_grp_exclusive_for_business_continuity_flag_is_set(vi[0].pse_grp_gid), 0);
    EXPECT_EQ(zpn_pbroker_pse_grp_exclusive_for_business_continuity_flag_is_set(vi[2].pse_grp_gid), 0);

    // Reset the flag for PSE group 289397352052031930Ull which is not set
    zpn_balance_set_pse_grp_exclusive_for_business_continuity_flag_do(NULL, query_values4, 2, NULL, 1);
    EXPECT_EQ(zpn_pbroker_pse_grp_exclusive_for_business_continuity_flag_is_set(vi[2].pse_grp_gid), 0);

    zpn_balance_set_pse_grp_exclusive_for_business_continuity_flag_do(NULL, query_values1, 2, NULL, 1);
    EXPECT_EQ(zpn_pbroker_pse_grp_exclusive_for_business_continuity_flag_is_set(vi[0].pse_grp_gid), 1);
    // Set the flag for PSE group which is already set
    zpn_balance_set_pse_grp_exclusive_for_business_continuity_flag_do(NULL, query_values1, 2, NULL, 1);
    EXPECT_EQ(zpn_pbroker_pse_grp_exclusive_for_business_continuity_flag_is_set(vi[0].pse_grp_gid), 1);
}

TEST_F(ZpnBrokerRedirectPolicyTester, RedirectPolicyDefaultBusinessConExcFlagMix) {
    int found = 0;

    set_pse_grp_exclusive_for_business_continuity_for_instances(1, 0);

    EXPECT_CALL(*get_mock(),
                zpn_balance_policy_check_redirect_policy(_, _, _, _, _))
                .Times(1)
                .WillRepeatedly(DoAll(SetArgPointee<1>(zpe_access_action_redirect_default), SetArgPointee<2>(0), WithArg<3>(SetRuleToPseGroupNull()), SetArgPointee<4>(0), Return(0)));
    EXPECT_CALL(*get_mock(), filter_by_scope(_)).Times(1).WillRepeatedly(DoAll(Return(0)));

    zpn_balance_policy_execute(GetState());
    zpn_balance_inst_filter_execute(GetState());

    //Filter should have happened based on site gid and business continuity exclusive flag
    for (size_t i = 0; i < GetState()->viable_instance_count; i++) {
        if (!GetState()->viable_instances[i].exclude) {
           found++;
           EXPECT_EQ(GetState()->viable_instances[i].exclusion_reason, excl_none);
        } else {
           EXPECT_EQ(GetState()->viable_instances[i].exclusion_reason, excl_inst_exclusive_for_business_continuity);
        }
    }
    EXPECT_EQ(found, 2);
}

// When Redirect Policy(Preferred/Always) is configured and instance(s) matching
// the configured policy is/are found then the business-continuity exlusive flag
// should not have any impact
TEST_F(ZpnBrokerRedirectPolicyTester, RedirectPolicyPreferredBusinessConExcFlagSetForAllInst) {

    set_pse_grp_exclusive_for_business_continuity_for_instances(1, 1);
    populate_rules_to_pse_group();

    EXPECT_CALL(*get_mock(),
                zpn_balance_policy_check_redirect_policy(_, _, _, _, _))
                .Times(1)
                .WillRepeatedly(DoAll(SetArgPointee<1>(zpe_access_action_redirect_preferred),
                                      SetArgPointee<2>(1),
                                      WithArg<3>(SetRuleToPseGroup(rule_to_psegrp, 2)),
                                      SetArgPointee<4>(2), Return(0)));
    EXPECT_CALL(*get_mock(), filter_by_scope(GetState())).Times(1).WillRepeatedly(DoAll(Return(0)));

    zpn_balance_policy_execute(GetState());
    zpn_balance_inst_filter_execute(GetState());

    //Filter should have happened based on policy.
    for (size_t i = 0; i < GetState()->viable_instance_count; i++) {
        EXPECT_EQ(GetState()->viable_instances[i].exclude, 0);
        EXPECT_EQ(GetState()->viable_instances[i].exclusion_reason, excl_none);
    }
}

TEST_F(ZpnBrokerRedirectPolicyTester, RedirectPolicyPreferredNoMatchBusinessConExcFlagSetForAllInst) {

    set_pse_grp_exclusive_for_business_continuity_for_instances(1, 1);
    populate_rule_to_pse_group_for_no_match();

    EXPECT_CALL(*get_mock(),
                zpn_balance_policy_check_redirect_policy(_, _, _, _, _))
                .Times(1)
                .WillRepeatedly(DoAll(SetArgPointee<1>(zpe_access_action_redirect_preferred), SetArgPointee<2>(1), WithArg<3>(SetRuleToPseGroup(rule_to_psegrp, 1)), SetArgPointee<4>(1), Return(0)));
    EXPECT_CALL(*get_mock(), filter_by_scope(GetState())).Times(1).WillRepeatedly(DoAll(Return(0)));

    zpn_balance_policy_execute(GetState());
    zpn_balance_inst_filter_execute(GetState());


    //Filter should have happened based on site_gid as policy match not found.
    //But since pse_grp_exclusive_for_business_continuity flag is set for all instances, none
    //of the instances should be selected, i.e all the instances should be excluded
    // with exclusion reason set as excl_inst_exclusive_for_business_continuity
    for (size_t i = 0; i < GetState()->viable_instance_count; i++) {
        EXPECT_EQ(GetState()->viable_instances[i].exclude, 1);
        EXPECT_EQ(GetState()->viable_instances[i].exclusion_reason, excl_inst_exclusive_for_business_continuity);
    }
}

TEST_F(ZpnBrokerRedirectPolicyTester, RedirectPolicyPreferredNoMatchBusinessConExcFlagSetForSomeInst) {

    int found = 0;
    set_pse_grp_exclusive_for_business_continuity_for_instances(1, 0);
    populate_rule_to_pse_group_for_no_match();

    EXPECT_CALL(*get_mock(),
                zpn_balance_policy_check_redirect_policy(_, _, _, _, _))
                .Times(1)
                .WillRepeatedly(DoAll(SetArgPointee<1>(zpe_access_action_redirect_preferred), SetArgPointee<2>(1), WithArg<3>(SetRuleToPseGroup(rule_to_psegrp, 1)), SetArgPointee<4>(1), Return(0)));
    EXPECT_CALL(*get_mock(), filter_by_scope(GetState())).Times(1).WillRepeatedly(DoAll(Return(0)));

    zpn_balance_policy_execute(GetState());
    zpn_balance_inst_filter_execute(GetState());


    //Filter should have happened based on site_gid as policy match not found.
    //But since pse_grp_exclusive_for_business_continuity flag is set for 2 instances, so other 2
    //instances should be selected
    for (size_t i = 0; i < GetState()->viable_instance_count; i++) {
        if (GetState()->viable_instances[i].exclude) {
            EXPECT_EQ(GetState()->viable_instances[i].exclusion_reason, excl_inst_exclusive_for_business_continuity);
        } else {
            EXPECT_EQ(GetState()->viable_instances[i].exclusion_reason, excl_none);
            found++;
        }
    }
    EXPECT_EQ(found, 2);
}

// When Redirect Policy(Preferred/Always) is configured and instance(s) matching
// the configured policy is/are found then the business-continuity exclusive flag
// should not have any impact
TEST_F(ZpnBrokerRedirectPolicyTester, RedirectPolicyAlwaysBusinessConExcFlagSetForAllInst) {

    set_pse_grp_exclusive_for_business_continuity_for_instances(1, 1);
    populate_rules_to_pse_group();

    EXPECT_CALL(*get_mock(),
                zpn_balance_policy_check_redirect_policy(_, _, _, _, _))
                .Times(1)
                .WillRepeatedly(DoAll(SetArgPointee<1>(zpe_access_action_redirect_always), SetArgPointee<2>(1), WithArg<3>(SetRuleToPseGroup(rule_to_psegrp, 2)), SetArgPointee<4>(2), Return(0)));
    EXPECT_CALL(*get_mock(), filter_by_scope(GetState())).Times(1).WillRepeatedly(DoAll(Return(0)));

    zpn_balance_policy_execute(GetState());
    zpn_balance_inst_filter_execute(GetState());


    for (size_t i = 0; i < GetState()->viable_instance_count; i++) {
        EXPECT_EQ(GetState()->viable_instances[i].exclude, 0);
        EXPECT_EQ(GetState()->viable_instances[i].exclusion_reason, excl_none);
    }
}

TEST_F(ZpnBrokerRedirectPolicyTester, RedirectPolicyAlwaysNoMatchBusinessConExcFlagSetForAllInst) {

    set_pse_grp_exclusive_for_business_continuity_for_instances(1, 1);
    populate_rule_to_pse_group_for_no_match();

    EXPECT_CALL(*get_mock(),
                zpn_balance_policy_check_redirect_policy(_, _, _, _, _))
                .Times(1)
                .WillRepeatedly(DoAll(SetArgPointee<1>(zpe_access_action_redirect_always), SetArgPointee<2>(1), WithArg<3>(SetRuleToPseGroup(rule_to_psegrp, 1)), SetArgPointee<4>(1), Return(0)));
    EXPECT_CALL(*get_mock(), filter_by_scope(GetState())).Times(1).WillRepeatedly(DoAll(Return(0)));

    zpn_balance_policy_execute(GetState());
    zpn_balance_inst_filter_execute(GetState());


    for (size_t i = 0; i < GetState()->viable_instance_count; i++) {
        EXPECT_EQ(GetState()->viable_instances[i].exclude, 1);
        EXPECT_EQ(GetState()->viable_instances[i].exclusion_reason, excl_inst_exclusive_for_business_continuity);
    }
}

TEST_F(ZpnBrokerRedirectPolicyTester, RedirectPolicyAlwaysNoMatchBusinessConExcFlagSetForSomeInst) {
    int found = 0;

    set_pse_grp_exclusive_for_business_continuity_for_instances(1, 0);
    populate_rule_to_pse_group_for_no_match();

    EXPECT_CALL(*get_mock(),
                zpn_balance_policy_check_redirect_policy(_, _, _, _, _))
                .Times(1)
                .WillRepeatedly(DoAll(SetArgPointee<1>(zpe_access_action_redirect_always), SetArgPointee<2>(1), WithArg<3>(SetRuleToPseGroup(rule_to_psegrp, 1)), SetArgPointee<4>(1), Return(0)));
    EXPECT_CALL(*get_mock(), filter_by_scope(GetState())).Times(1).WillRepeatedly(DoAll(Return(0)));

    zpn_balance_policy_execute(GetState());
    zpn_balance_inst_filter_execute(GetState());


    //Filter should have happened based on site_gid as policy match not found.
    //But since pse_grp_exclusive_for_business_continuity flag is set for 2 instances, so other 2
    //instances should be selected
    for (size_t i = 0; i < GetState()->viable_instance_count; i++) {
        if (GetState()->viable_instances[i].exclude) {
            EXPECT_EQ(GetState()->viable_instances[i].exclusion_reason, excl_inst_exclusive_for_business_continuity);
        } else {
            EXPECT_EQ(GetState()->viable_instances[i].exclusion_reason, excl_none);
            found++;
        }
    }
    EXPECT_EQ(found, 2);
}
