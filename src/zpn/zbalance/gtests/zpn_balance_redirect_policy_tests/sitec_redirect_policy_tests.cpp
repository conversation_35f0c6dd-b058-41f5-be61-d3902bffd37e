/*
 * sitec_redirect_policy_tests.cpp. Copyright (C) 2025 Zscaler, Inc. All Rights Reserved
 */


#include <gtest/gtest.h>
#include <gmock/gmock.h>

#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wunused-parameter"
#elif defined __GNUC__
#pragma GCC diagnostic push
#pragma GCC diagnostic ignored "-Wunused-parameter"
#endif // __clang__

#include <cstdint>

extern "C" {
#include "zpn/zbalance/zpn_balance_private.h"
#include "zpn/zpn_rule_to_pse_group.h"
};

#include "test_misc/ZscalerMockBase.h"
#include "zpn/zbalance/gtests/zpn_balance_redirect_policy_tests/mocks/zpn_redirect_policy_mock.h"

using testing::_;
using testing::AllOf;
using testing::DoAll;
using testing::Eq;
using testing::Field;
using testing::InSequence;
using testing::Invoke;
using testing::IsNull;
using testing::Not;
using testing::NotNull;
using testing::Pointee;
using testing::Return;
using testing::SaveArg;
using testing::SaveArgPointee;
using testing::SetArgPointee;
using testing::StrEq;
using testing::WithArg;
using testing::StrictMock;

using namespace std;

class ZpnSitecRedirectPolicyMockBase
        : public StrictMock<ZpnRedirectPolicyMock> {};

class ZpnSitecRedirectPolicyTester
        : public ZscalerMockBase<ZpnSitecRedirectPolicyMockBase>, public ZpnRedirectPolicyMockBase {
public:
    void SetUp() override {
        ZscalerMockBase::SetUp();
        ZpnRedirectPolicyMockBase::SetUp();

        memset(GetState(), 0, sizeof(struct balance_request_state));
        GetState()->req = &req;
        GetState()->gf_state = geo_exclude;
        GetState()->viable_instance_count = 6;
        GetState()->viable_instance_capacity = 1024;
        GetState()->viable_instances = vi;

        //generate dummy viable instances
        random_gid = 289397352052031866Ull;
        for (size_t i = 0; i < GetState()->viable_instance_count; i++) {
            vi[i].gid = random_gid++;
            vi[i].balance_role = role;
            vi[i].balance_mode = mode;
            vi[i].modified_time_s = epoch_s();
            vi[i].personality = ZPN_INSTANCE_TYPE_PRIVATE_BROKER;
            snprintf(vi[i].is_public, sizeof(vi[i].is_public), "%s", "FALSE");
            vi[i].brk_status = RUNNING;
            vi[i].exclude = 0;
            vi[i].exclusion_reason = excl_none;
        }

        //setup 2 pse in India location
        for (int i = 0; i < 2; i++) {
            vi[i].site.lat = 12.971598699999999;
            vi[i].site.lon = 77.594562699999997;
            snprintf(vi[i].site.cc, sizeof(vi[i].site.cc), "%s", "IN");
            vi[i].site_gid = 289397352052031509Ull;
            vi[i].pse_grp_gid = 289397352052031774Ull;
            // The flag pse_grp_exclusive_for_business_continuity should not have any impact
            // when the redirection code is executed by sitec, it should have
            // impact only when the redirection is done by a broker
            vi[i].pse_grp_exclusive_for_business_continuity = rand() % 2;
        }

        //setup 2 pse in US location
        for (int i = 2; i < 4; i++) {
            vi[i].site.lat = 37.338740000000001;
            vi[i].site.lon = -121.88525250000001;
            snprintf(vi[i].site.cc, sizeof(vi[i].site.cc), "%s", "US");
            vi[i].site_gid = 289397352052031972Ull;
            vi[i].pse_grp_gid = 289397352052031930ull;
            vi[i].pse_grp_exclusive_for_business_continuity = rand() % 2;
        }

        //setup 2 pse in india location with no site
        for (int i = 4; i < 6; i++) {
            vi[i].site.lat = 16.506174300000001;
            vi[i].site.lon = 80.648015299999997;
            snprintf(vi[i].site.cc, sizeof(vi[i].site.cc), "%s", "IN");
            vi[i].pse_grp_gid = 289397352052032124Ull;
            snprintf(vi[i].is_public, sizeof(vi[i].is_public), "%s", "TRUE");
            vi[i].pse_grp_exclusive_for_business_continuity = rand() % 2;
        }

        rule_to_psegrp[0] = (struct zpn_rule_to_pse_group *) ZPN_CALLOC(sizeof(struct zpn_rule_to_pse_group));
        rule_to_psegrp[1] = (struct zpn_rule_to_pse_group *) ZPN_CALLOC(sizeof(struct zpn_rule_to_pse_group));

    }

    void TearDown() override {
        ZscalerMockBase::TearDown();
        ZpnRedirectPolicyMockBase::TearDown();
        ZPN_FREE(rule_to_psegrp[0]);
        ZPN_FREE(rule_to_psegrp[1]);
    }

    void populate_two_dummy_rule_to_pse_group() {
        rule_to_psegrp[0]->id = 1;
        rule_to_psegrp[0]->customer_gid = 289397352052031488Ull;
        rule_to_psegrp[0]->pse_group_gid = 289397352052031930ull;

        rule_to_psegrp[1]->id = 2;
        rule_to_psegrp[1]->customer_gid = 289397352052031488Ull;
        rule_to_psegrp[1]->pse_group_gid = 289397352052031931ull;
    }

    void populate_one_dummy_rule_to_pse_group() {
        rule_to_psegrp[0]->id = 2;
        rule_to_psegrp[0]->customer_gid = 289397352052031488Ull;
        rule_to_psegrp[0]->pse_group_gid = 289397352052031931ull;
    }

    struct zpn_rule_to_pse_group* GetRuleToPseGrpGid(int i) {
        return rule_to_psegrp[i];
    }

  protected:
    struct zpn_rule_to_pse_group *rule_to_psegrp[2];
};

extern "C" {
    int zpn_balance_policy_check_redirect_policy(struct balance_request_state *state,
                                                enum zpe_access_action *matched_action,
                                                int64_t *matched_rule,
                                                struct zpn_rule_to_pse_group **rule_to_pse_group,
                                                size_t *num_pse_group) __attribute__((weak));
    MOCK_ZPN_POLICY_CHECK_REDIRECT_POLICY(ZpnSitecRedirectPolicyTester);

    int filter_by_scope(struct balance_request_state *state) __attribute__((weak));
    MOCK_FILTER_BY_SCOPE(ZpnSitecRedirectPolicyTester);
}

ACTION_P(SetRuleToPseGroup, ptr, count) {
    for (int i = 0; i < count; i++) {
        arg0[i] = ptr[i];
        arg0[i] = ptr[i];
    }
}

ACTION(SetRuleToPseGroupNull) {
    arg0[0] = NULL;
}

TEST_F(ZpnSitecRedirectPolicyTester, RedirectPolicyDefault) {
    EXPECT_CALL(*get_mock(),
                zpn_balance_policy_check_redirect_policy(_, _, _, _, _))
                .Times(1)
                .WillRepeatedly(DoAll(SetArgPointee<1>(zpe_access_action_redirect_default), SetArgPointee<2>(0), WithArg<3>(SetRuleToPseGroupNull()), SetArgPointee<4>(0), Return(0)));
    EXPECT_CALL(*get_mock(), filter_by_scope(_)).Times(1).WillRepeatedly(DoAll(Return(0)));

    zpn_balance_policy_execute(GetState());
    zpn_balance_inst_filter_execute(GetState());


    //Filter should have happened based on site gid.
    for (size_t i = 0; i < GetState()->viable_instance_count; i++) {
        if (GetState()->viable_instances[i].site_gid == GetState()->req->filter_by_site_gid) {
            EXPECT_EQ(GetState()->viable_instances[i].exclude, 0);
            EXPECT_EQ(GetState()->viable_instances[i].exclusion_reason, excl_none);
        } else {
            EXPECT_EQ(GetState()->viable_instances[i].exclude, 1);
            EXPECT_EQ(GetState()->viable_instances[i].exclusion_reason, excl_site_gid_mismatch);
        }
    }
}

TEST_F(ZpnSitecRedirectPolicyTester, RedirectPolicyPreferred) {
    int found;

    populate_two_dummy_rule_to_pse_group();

    EXPECT_CALL(*get_mock(),
                zpn_balance_policy_check_redirect_policy(_, _, _, _, _))
                .Times(1)
                .WillRepeatedly(DoAll(SetArgPointee<1>(zpe_access_action_redirect_preferred), SetArgPointee<2>(1), WithArg<3>(SetRuleToPseGroup(rule_to_psegrp, 2)), SetArgPointee<4>(2), Return(0)));
    EXPECT_CALL(*get_mock(), filter_by_scope(GetState())).Times(1).WillRepeatedly(DoAll(Return(0)));

    zpn_balance_policy_execute(GetState());
    zpn_balance_inst_filter_execute(GetState());


    //Filter should have happened based on policy.
    for (size_t i = 0; i < GetState()->viable_instance_count; i++) {
        found = 0;
        for (int j = 0; j < 2; j++) {
            if (GetRuleToPseGrpGid(j)->pse_group_gid == GetState()->viable_instances[i].pse_grp_gid) {
                found = 1;
                break;
            }
        }
        if (found) {
            EXPECT_EQ(GetState()->viable_instances[i].exclude, 0);
            EXPECT_EQ(GetState()->viable_instances[i].exclusion_reason, excl_none);
        } else {
            EXPECT_EQ(GetState()->viable_instances[i].exclude, 1);
            EXPECT_EQ(GetState()->viable_instances[i].exclusion_reason, excl_policy_mismatch);
        }
    }
}

TEST_F(ZpnSitecRedirectPolicyTester, RedirectPolicyPreferredNoMatch) {
    int found;

    populate_one_dummy_rule_to_pse_group();

    EXPECT_CALL(*get_mock(),
                zpn_balance_policy_check_redirect_policy(_, _, _, _, _))
                .Times(1)
                .WillRepeatedly(DoAll(SetArgPointee<1>(zpe_access_action_redirect_preferred), SetArgPointee<2>(1), WithArg<3>(SetRuleToPseGroup(rule_to_psegrp, 1)), SetArgPointee<4>(1), Return(0)));
    EXPECT_CALL(*get_mock(), filter_by_scope(GetState())).Times(1).WillRepeatedly(DoAll(Return(0)));

    zpn_balance_policy_execute(GetState());
    zpn_balance_inst_filter_execute(GetState());


    //Filter should have happened based on site_gid as policy match not found.
    for (size_t i = 0; i < GetState()->viable_instance_count; i++) {
        found = 0;
        for (int j = 0; j < 2; j++) {
            if (GetRuleToPseGrpGid(j)->pse_group_gid == GetState()->viable_instances[i].pse_grp_gid) {
                found = 1;
                break;
            }
        }
        EXPECT_EQ(found, 0);

        if (GetState()->req->filter_by_site_gid == GetState()->viable_instances[i].site_gid) {
            EXPECT_EQ(GetState()->viable_instances[i].exclude, 0);
            EXPECT_EQ(GetState()->viable_instances[i].exclusion_reason, excl_none);
        } else {
            EXPECT_EQ(GetState()->viable_instances[i].exclude, 1);
            EXPECT_EQ(GetState()->viable_instances[i].exclusion_reason, excl_site_gid_mismatch);
        }
    }
}

TEST_F(ZpnSitecRedirectPolicyTester, RedirectPolicyPreferredNoMatchNoFallback) {
    int found;

    populate_one_dummy_rule_to_pse_group();

    GetState()->req->policy_req->is_fallback_broker_enabled = 0;

    EXPECT_CALL(*get_mock(),
                zpn_balance_policy_check_redirect_policy(_, _, _, _, _))
                .Times(1)
                .WillRepeatedly(DoAll(SetArgPointee<1>(zpe_access_action_redirect_preferred), SetArgPointee<2>(1), WithArg<3>(SetRuleToPseGroup(rule_to_psegrp, 1)), SetArgPointee<4>(1), Return(0)));
    EXPECT_CALL(*get_mock(), filter_by_scope(GetState())).Times(1).WillRepeatedly(DoAll(Return(0)));

    zpn_balance_policy_execute(GetState());
    zpn_balance_inst_filter_execute(GetState());

    //Filter should have happened based on site_gid as policy match not found even when fallback broker is 0.
    for (size_t i = 0; i < GetState()->viable_instance_count; i++) {
        found = 0;
        for (int j = 0; j < 2; j++) {
            if (GetRuleToPseGrpGid(j)->pse_group_gid == GetState()->viable_instances[i].pse_grp_gid) {
                found = 1;
                break;
            }
        }
        EXPECT_EQ(found, 0);

        if (GetState()->req->filter_by_site_gid == GetState()->viable_instances[i].site_gid) {
            EXPECT_EQ(GetState()->viable_instances[i].exclude, 0);
            EXPECT_EQ(GetState()->viable_instances[i].exclusion_reason, excl_none);
        } else {
            EXPECT_EQ(GetState()->viable_instances[i].exclude, 1);
            EXPECT_EQ(GetState()->viable_instances[i].exclusion_reason, excl_site_gid_mismatch);
        }
    }
}

TEST_F(ZpnSitecRedirectPolicyTester, RedirectPolicyAlways) {
    int found;

    populate_two_dummy_rule_to_pse_group();

    GetState()->req->policy_req->is_fallback_broker_enabled = 1;

    EXPECT_CALL(*get_mock(),
                zpn_balance_policy_check_redirect_policy(_, _, _, _, _))
                .Times(1)
                .WillRepeatedly(DoAll(SetArgPointee<1>(zpe_access_action_redirect_always), SetArgPointee<2>(1), WithArg<3>(SetRuleToPseGroup(rule_to_psegrp, 2)), SetArgPointee<4>(2), Return(0)));
    EXPECT_CALL(*get_mock(), filter_by_scope(GetState())).Times(1).WillRepeatedly(DoAll(Return(0)));

    zpn_balance_policy_execute(GetState());
    zpn_balance_inst_filter_execute(GetState());


    //Filter should have happened based on policy.
    for (size_t i = 0; i < GetState()->viable_instance_count; i++) {
        found = 0;
        for (int j = 0; j < 2; j++) {
            if (GetRuleToPseGrpGid(j)->pse_group_gid == GetState()->viable_instances[i].pse_grp_gid) {
                found = 1;
                break;
            }
        }
        if (found) {
            EXPECT_EQ(GetState()->viable_instances[i].exclude, 0);
            EXPECT_EQ(GetState()->viable_instances[i].exclusion_reason, excl_none);
        } else {
            EXPECT_EQ(GetState()->viable_instances[i].exclude, 1);
            EXPECT_EQ(GetState()->viable_instances[i].exclusion_reason, excl_policy_mismatch);
        }
    }
}

TEST_F(ZpnSitecRedirectPolicyTester, RedirectPolicyAlwaysNoMatch) {
    int found;

    populate_one_dummy_rule_to_pse_group();

    GetState()->req->policy_req->is_fallback_broker_enabled = 1;

    EXPECT_CALL(*get_mock(),
                zpn_balance_policy_check_redirect_policy(_, _, _, _, _))
                .Times(1)
                .WillRepeatedly(DoAll(SetArgPointee<1>(zpe_access_action_redirect_always), SetArgPointee<2>(1), WithArg<3>(SetRuleToPseGroup(rule_to_psegrp, 1)), SetArgPointee<4>(1), Return(0)));
    EXPECT_CALL(*get_mock(), filter_by_scope(GetState())).Times(1).WillRepeatedly(DoAll(Return(0)));

    zpn_balance_policy_execute(GetState());
    zpn_balance_inst_filter_execute(GetState());

    //Filter should have happened based on site_gid as policy match not found and fallback broker is 1
    for (size_t i = 0; i < GetState()->viable_instance_count; i++) {
        found = 0;
        for (int j = 0; j < 2; j++) {
            if (GetRuleToPseGrpGid(j)->pse_group_gid == GetState()->viable_instances[i].pse_grp_gid) {
                found = 1;
                break;
            }
        }
        EXPECT_EQ(found, 0);

        if (GetState()->req->filter_by_site_gid == GetState()->viable_instances[i].site_gid) {
            EXPECT_EQ(GetState()->viable_instances[i].exclude, 0);
            EXPECT_EQ(GetState()->viable_instances[i].exclusion_reason, excl_none);
        } else {
            EXPECT_EQ(GetState()->viable_instances[i].exclude, 1);
            EXPECT_EQ(GetState()->viable_instances[i].exclusion_reason, excl_site_gid_mismatch);
        }
    }
}

TEST_F(ZpnSitecRedirectPolicyTester, RedirectPolicyAlwaysNoMatchNoFallback) {

    populate_one_dummy_rule_to_pse_group();

    GetState()->req->policy_req->is_fallback_broker_enabled = 0;

    EXPECT_CALL(*get_mock(),
                zpn_balance_policy_check_redirect_policy(_, _, _, _, _))
                .Times(1)
                .WillRepeatedly(DoAll(SetArgPointee<1>(zpe_access_action_redirect_always), SetArgPointee<2>(1), WithArg<3>(SetRuleToPseGroup(rule_to_psegrp, 1)), SetArgPointee<4>(1), Return(0)));
    EXPECT_CALL(*get_mock(), filter_by_scope(GetState())).Times(1).WillRepeatedly(DoAll(Return(0)));

    zpn_balance_policy_execute(GetState());
    zpn_balance_inst_filter_execute(GetState());

    //No broker should be selected as fallback broker is 0
    for (size_t i = 0; i < GetState()->viable_instance_count; i++) {
        EXPECT_EQ(GetState()->viable_instances[i].exclude, 1);
        EXPECT_EQ(GetState()->viable_instances[i].exclusion_reason, excl_policy_mismatch);
    }
}
