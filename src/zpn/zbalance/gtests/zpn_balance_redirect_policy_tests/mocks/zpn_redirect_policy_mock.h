/*
 * sitec_redirect_policy_mock.h. Copyright (C) 2025 Zscaler, Inc. All Rights Reserved
 */

#ifndef _SITEC_REDIRECT_POLICY_MOCK_H_
#define _SITEC_REDIRECT_POLICY_MOCK_H_

#include <gmock/gmock.h>

class ZpnRedirectPolicyMock {
public:
    MOCK_METHOD(int, zpn_balance_policy_check_redirect_policy, (struct balance_request_state *, enum zpe_access_action*, int64_t*, struct zpn_rule_to_pse_group**, size_t*));
    MOCK_METHOD(int, filter_by_scope, (struct balance_request_state *));
};

class ZpnRedirectPolicyMockBase {
public:
    void SetUp() {
        constexpr auto bufsz = 100;
        role = (char *)ZPN_CALLOC(bufsz);
        mode = (char *)ZPN_CALLOC(bufsz);

        snprintf(role, bufsz, "%s", "FORWARD");
        snprintf(mode, bufsz, "%s", "BALANCE");

        rbt.type[PRIVATE_BROKER] = 1;
        rbt.type[PUBLIC_PRIVATE_BROKER] = 1;
        rbt.type[PUBLIC_BROKER] = 0;

        policy_req.is_client_capable = 1;
        policy_req.is_cc_enabled = 1;
        policy_req.is_saml_enabled = 0;
        policy_req.is_scim_enabled = 0;
        policy_req.is_fallback_broker_enabled = 1;
        policy_req.general_context_hash = NULL;
        policy_req.saml_hash = NULL;
        policy_req.scim_hash = NULL;
        policy_req.int_hash = NULL;

        memset(&req, 0, sizeof(struct zpn_balance_redirect_request));
        req.broker_count = &broker_count;
        req.brokers = brokers;
        req.client_type = zpn_client_type_zapp;
        req.remote_ip = NULL;
        req.remote_lat = 37.33;
        req.remote_lon = 121.80;
        req.customer_gid = 289397352052031488Ull;
        req.scope_gid = 289397352052031488Ull;
        req.filter_by_site_gid = 289397352052031509Ull;
        req.trusted_networks = NULL;
        req.rbt = &rbt;
        req.multi_dc_redir_capable = 0;
        req.is_lp_enabled = 0;
        req.log_tag = NULL;
        req.sni_suffix = sni_suffixes;
        req.sni_suffix_count = &sni_suffix_count;
        req.alt_cloud_supported = 0;
        req.alt_cloud_aware = 1;
        req.ipv6_redirect_supported = 0;
        req.exclude_self = 0;
        req.do_stats = 0;
        req.flag.feature_policy_redirect = 1;
        req.policy_req = &policy_req;
    }

    void TearDown() {
        ZPN_FREE(role);
        ZPN_FREE(mode);
    }

    struct balance_request_state* GetState() {
        return &state;
    }

private:
    protected:
    int64_t random_gid;
    struct balance_request_state state;
    int broker_count = ZPN_CLIENT_MAX_BROKERS;
    char *brokers[ZPN_CLIENT_MAX_BROKERS];
    int sni_suffix_count = ZPN_CLIENT_MAX_BROKERS;
    char *sni_suffixes[ZPN_CLIENT_MAX_BROKERS];
    struct zpn_balance_redirect_instance_types rbt;
    struct zpn_balance_redirect_request req;
    struct zpn_balance_policy_req policy_req;
    struct viable_instance vi[1024];
    char *role;
    char *mode;
};

#define MOCK_ZPN_POLICY_CHECK_REDIRECT_POLICY(T)                                                   \
    int zpn_balance_policy_check_redirect_policy(struct balance_request_state *state,              \
                                                 enum zpe_access_action *matched_action,           \
                                                 int64_t *matched_rule,                            \
                                                 struct zpn_rule_to_pse_group **rule_to_pse_group, \
                                                 size_t *num_pse_group) {                          \
        return T::get_mock()->zpn_balance_policy_check_redirect_policy(                            \
                state, matched_action, matched_rule, rule_to_pse_group, num_pse_group);            \
    }

#define MOCK_FILTER_BY_SCOPE(T)                                                   \
    int filter_by_scope(struct balance_request_state *state) {                    \
        return T::get_mock()->filter_by_scope(state);                             \
    }
#endif  // _SITEC_REDIRECT_POLICY_MOCK_H_
