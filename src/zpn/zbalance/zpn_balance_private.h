/*
 * zpn_balance_private.h. Copyright (C) 2022 Zscaler, Inc. All Rights Reserved.
 */

#ifndef _ZPN_BALANCE_PRIVATE_
#define _ZPN_BALANCE_PRIVATE_

#include "zpath_lib/zpath_debug.h"
#include "zpath_lib/zpath_table.h"
#include "zpath_lib/zpath_instance.h"
#include "zpath_lib/zpath_geoip.h"
#include "zpath_lib/zpath_constellation.h"
#include "zpath_lib/zpath_config_override.h"
#include "zpath_lib/zpath_config_override_keys.h"
#include "zhash/zhash_table.h"
#include "zpn/zpn_lib.h"
#include "zpn/zpn_broker_common.h"
#include "zpn/zpn_rpc.h"
#include "zpn/zpn_pbroker_group.h"
#include "zpn/zpn_pbroker_to_group.h"
#include "zpn/zpn_private_broker_table.h"
#include "zpn/zpn_trusted_network.h"
#include "zpn/zpn_privatebrokergroup_trustednetwork_mapping.h"
#include "zpath_lib/zpath_customer.h"
#include "zpn/zpn_broker.h"
#include "zpn/zbalance/zpn_balance_redirect.h"
#include "zpn/zpn_broker_balance_control.h"
#include "zpath_lib/zpath_partition_common.h"
#include "zpn/zpn_broker_partition_stats.h"

/* redirect returns max of:
 * - 3 public brokers
 *      -- if multi-dc redirect, 2 from primary and 1 from secondary
 *      -- otherwise, 2 from primary
 * - 4 private (2 buckets, private and public private, 2 brokers each)
 */
#define MAX_INSTANCES_PER_BUCKET        (3) /* pub, when doing multi-dc redir */
#define MAX_TOTAL_INSTANCES             (ZPN_CLIENT_MAX_BROKERS)

// some hard-coded bounds - doesn't make sense to go lower/higher than these
#define MAX_SECONDARY_DC_DIST_MILES_LO  (CONFIG_FEATURE_BALANCE_PUBLIC_MDC_MAX_SDC_DIST_MILES_MIN)
#define MAX_SECONDARY_DC_DIST_MILES_HI  (CONFIG_FEATURE_BALANCE_PUBLIC_MDC_MAX_SDC_DIST_MILES_MAX)
#define MAX_SECONDARY_DC_COUNT_LO       (CONFIG_FEATURE_BALANCE_PUBLIC_MDC_MAX_SDC_COUNT_MIN)
#define MAX_SECONDARY_DC_COUNT_HI       (CONFIG_FEATURE_BALANCE_PUBLIC_MDC_MAX_SDC_COUNT_MAX)

#define NO_RULE_CONFIGURED              -1

/*
 * Represent balancing config for the current instance.
 * Data comes from zpn_broker_balance_control table, or
 * config overrides.
 */
#define ZPN_BALANCE_ROLE_MODE_LEN   24

/***************************************
 * TODO: merge with redirect stats when
 * logical partitioning becomes available
 ***************************************/
struct zpn_balance_stats {          /* _ARGO: object_definition */
    /* overall results */
    int64_t     success;                /* _ARGO: integer */
    int64_t     failure;                /* _ARGO: integer */

    /* bucket result - bucket is incremented whenever there is
     * an instance of that type returned to client. */
    int64_t     bucket_pvt_mtn;         /* _ARGO: integer */
    int64_t     bucket_pvt;             /* _ARGO: integer */
    int64_t     bucket_pub_pvt_mtn;     /* _ARGO: integer */
    int64_t     bucket_pub_pvt;         /* _ARGO: integer */
    int64_t     bucket_pub;             /* _ARGO: integer */

    /* Execution timing broken down into a number of micro and milli second buckets
     * Note: buckets are in 4x increments except for the "1s plus" catch-all bucket
     * Note: strange naming is to ensure they show up in-order in magellan*/
    int64_t     hist_micro_016;         /* _ARGO: integer */
    int64_t     hist_micro_064;         /* _ARGO: integer */
    int64_t     hist_micro_256;         /* _ARGO: integer */
    int64_t     hist_milli_001;         /* _ARGO: integer */
    int64_t     hist_milli_004;         /* _ARGO: integer */
    int64_t     hist_milli_016;         /* _ARGO: integer */
    int64_t     hist_milli_064;         /* _ARGO: integer */
    int64_t     hist_milli_256;         /* _ARGO: integer */
    int64_t     hist_s_1;               /* _ARGO: integer */
    int64_t     hist_s_1plus;           /* _ARGO: integer */

    /* geofencing related */
    int64_t     gf_success;             /* _ARGO: integer */
    int64_t     gf_failure;             /* _ARGO: integer */

    /* mdc related */
    int64_t     mdc_success;            /* _ARGO: integer */
    int64_t     mdc_failure;            /* _ARGO: integer */
    int64_t     mdc_pdc_0_broker;       /* _ARGO: integer */
    int64_t     mdc_pdc_1_broker;       /* _ARGO: integer */
    int64_t     mdc_sdc_0_broker;       /* _ARGO: integer */
    int64_t     mdc_pub_0_broker;       /* _ARGO: integer */
    /* track some conditions (as exclusion code) that's less than ideal,
     * Note: we are only selectively adding a few. We should add/remove
     * them over time as we find more/less interesting conditions to track.
     * It's unreaslistic to automatically track everything. */
    int64_t     excl_primary_dc_load;   /* _ARGO: integer */
    int64_t     excl_secondary_dc_load; /* _ARGO: integer */

    /* Redirect policy related. Will be incremented only when redirect
     * policy feature flag is enabled. */
    int64_t     policy_hit;                            /* _ARGO: integer */
    int64_t     policy_miss;                           /* _ARGO: integer */
    int64_t     action_default;                        /* _ARGO: integer */
    int64_t     action_preferred;                      /* _ARGO: integer */
    int64_t     action_always;                         /* _ARGO: integer */
    int64_t     redirect_preferred_no_pse_target;      /* _ARGO: integer */
    int64_t     redirect_always_no_pse_target;         /* _ARGO: integer */
    int64_t     fallback_broker_hit;                   /* _ARGO: integer */
    int64_t     grace_dist_hit;                        /* _ARGO: integer */

    /* number of times redirect happened to alt cloud */
    int64_t     redirects_to_alt_cloud; /* _ARGO: integer */
    int64_t     no_brokers_in_alt_cloud; /* _ARGO: integer */

    /* global redirect stats for logical partition */
    int64_t mdc_pdc_0_partition_broker;     /* _ARGO: integer */
    int64_t mdc_pdc_1_partition_broker;     /* _ARGO: integer */
    int64_t mdc_pdc_2_partition_broker;     /* _ARGO: integer */
    int64_t mdc_pdc_0_unpartition_broker;   /* _ARGO: integer */
    int64_t mdc_pdc_1_unpartition_broker;   /* _ARGO: integer */
    int64_t mdc_pdc_2_unpartition_broker;   /* _ARGO: integer */
    int64_t mdc_sdc_0_partition_broker;     /* _ARGO: integer */
    int64_t mdc_sdc_1_partition_broker;     /* _ARGO: integer */
    int64_t mdc_sdc_2_partition_broker;     /* _ARGO: integer */
    int64_t mdc_sdc_0_unpartition_broker;   /* _ARGO: integer */
    int64_t mdc_sdc_1_unpartition_broker;   /* _ARGO: integer */
    int64_t mdc_sdc_2_unpartition_broker;   /* _ARGO: integer */
    int64_t mdc_sdc_0_mixup_broker;         /* _ARGO: integer */
    int64_t mdc_sdc_1_mixup_broker;         /* _ARGO: integer */
    int64_t mdc_sdc_2_mixup_broker;         /* _ARGO: integer */

    /* single DC redirects for partition*/
    int64_t partition_broker_count0;        /* _ARGO: integer */
    int64_t partition_broker_count1;        /* _ARGO: integer */
    int64_t partition_broker_count2;        /* _ARGO: integer */
    int64_t unpartition_broker_count0;      /* _ARGO: integer */
    int64_t unpartition_broker_count1;      /* _ARGO: integer */
    int64_t unpartition_broker_count2;      /* _ARGO: integer */
    int64_t no_target_instance;             /* _ARGO: integer */

    /* redirect policy hard disabled */
    int64_t redirect_policy_skip_due_to_hard_disable;  /* _ARGO: integer */

    /* ET-65356: redirect pb selected with fqdn, v4/v6, v4+v6  */
    int64_t redirect_count_pb_fqdn;         /* _ARGO: integer */
    int64_t redirect_count_pb_v4_or_v6;     /* _ARGO: integer */
    int64_t redirect_count_pb_v4_and_v6;    /* _ARGO: integer */

    /* ET-65485 New redirect metrics - inst_load metrics */
    int64_t all_num_clients_under_threshold_or_unreported;          /* _ARGO: integer */
    int64_t no_reported_clients_count;                              /* _ARGO: integer */
    int64_t no_reported_clients_load;                               /* _ARGO: integer */
    int64_t client_metrics_all_under_threshold;                     /* _ARGO: integer */
    int64_t reported_load_exceeded_100;                             /* _ARGO: integer */
    int64_t successfully_calculated_max_clients;                    /* _ARGO: integer */
    int64_t client_metrics_hot_count;                               /* _ARGO: integer */

    /* ET-65485 - broker select metrics */
    int64_t total_brokers_selected;                       /* _ARGO: integer */
    int64_t instance_select_attempt_total;                /* _ARGO: integer */
    int64_t instance_select_complete_total;               /* _ARGO: integer */
    int64_t partitioned_clients_available_exceeded_max;   /* _ARGO: integer */
    int64_t unpartitioned_clients_available_exceeded_max; /* _ARGO: integer */
    int64_t instance_clients_available_exceeded_max;      /* _ARGO: integer */
    int64_t instance_select_using_clients;                /* _ARGO: integer */
    int64_t instance_select_using_metrics;                /* _ARGO: integer */

    /* ET-65485 - Method Selection Metrics*/
    int64_t first_instance_used_clients;                  /* _ARGO: integer */
    int64_t second_instance_used_clients;                 /* _ARGO: integer */
    int64_t third_instance_used_clients;                  /* _ARGO: integer */
    int64_t fourth_instance_used_clients;                 /* _ARGO: integer */
    int64_t first_instance_used_metrics;                  /* _ARGO: integer */
    int64_t second_instance_used_metrics;                 /* _ARGO: integer */
    int64_t third_instance_used_metrics;                  /* _ARGO: integer */
    int64_t fourth_instance_used_metrics;                 /* _ARGO: integer */

    int64_t no_brokers_selected;                          /* _ARGO: integer */

    /* ET-81403 - IPv6-aware redirection stats */
    int64_t ipv6_misconfig_exclusive_skip_disable;        /* _ARGO: integer */
    int64_t ipv6_misconfig_exclusive_skip_client_cap;     /* _ARGO: integer */
    int64_t ipv6_fqdn_prefixed;                           /* _ARGO: integer */
    int64_t ipv6_capable_ipv4_fqdn;                       /* _ARGO: integer */
    int64_t ipv6_exclusive_all_excluded;                  /* _ARGO: integer */
};

/*
 * Broker reported via zpn_broker_load table.
 */
struct reported_instance {
    int64_t gid;

    /* origin: zpn_balance_load.deleted */
    int8_t deleted;

    /* origin: zpn_balance_load.brk_name
     * would love to see the column renamed to instance_name... */
    char *name;

    /* origin: zpath_instance.role
     * values: zpn_brokerd, and any future components that needs load balancing */
    char *instance_role;

    /* origin: zpn_balance_load.balance_role
     * values: FORWARD/REDIRECT/NOREDIRECT/UNCONFIGURED
     * see #define BALANCE_ROLE_xyz */
    char *balance_role;

    /* origin: zpn_balance_load.balance_mode
     * values: BALANCE/MAINTENANCE/UNCONFIGURED
     * see #define BALANCE_MODE_xyz */
    char *balance_mode;

    /* origin: if data came from zpn_broker_load table, then ZPN_INSTANCE_TYPE_PUBLIC_BROKER
     * if data came from zpn_private_broker_load table, then ZPN_INSTANCE_TYPE_PRIVAT_BROKER
     * TODO: might do something better later */
    enum Zpn_Instance_Type personality;

    /* origin: private broker configuration */
    char **publish_ips;
    int publish_ips_count;
    char is_public[8];

    /* origin: reported in zpn_broker_load/zpn_private_broker_load tables */
    uint32_t cpu_pct;
    uint32_t mem_pct;
    uint64_t clients;
    uint32_t load_skew; // not available (set to 0) to private brokers, configured by OPS for public instances.
    int64_t modified_time_s;

    int is_dualstack; /* If this instance is a dual-stacked instance or not */

    /* for public instances, looked up from zpath_instance table,
     * for private instances, looked up from private broker group configuration */
    struct site site;

    enum zpn_broker_status brk_status;
    /* process fd utilization percentage */
    uint32_t proc_fd_util;
};



/* Enumeration why an instance, DC, or bucket got excluded from consideration
 * for debugging/logging purposes */
enum excl_code {
    excl_none = 0,

    /* instance filtering */
    excl_report_time,           /* last report time was too old */
    excl_report_median,         /* last report time was too old compared to other instances */
    excl_balance_role,          /* instance's balance_role is not a redirect target by configuration */
    excl_balance_mode,          /* instance's balance_mode is not a redirect target by configuration */
    excl_req_inst_type,         /* client doesn't want this type of instance */
    excl_req_inst_role,         /* the role of the instance doesn't meet requirements */
    excl_priv_mtn_mismatch,     /* a private broker is configure with mtn but client's mtn is empty, or matched none. */
    excl_policy_mismatch,       /* private broker instance doesn't match client's redirect policy */
    excl_inst_partition,        /* a broker assigned to a partition that doesn't match that of the customer's partition */
    excl_part_inst_load,        /* inst excluded due to partition load */
    excl_unpart_inst_load,      /* inst excluded due to unpartition load */
    excl_inst_load,             /* inst excluded due to load */
    excl_inst_exclusive_for_business_continuity, /* this instance belongs to a PSE group which is exclusive for Business Continuity */

    /* dc selection */
    excl_country,               /* country code of DC doesn't meet criteria */
    excl_geofencing,            /* not a suitable instance per geofencing rule */
    excl_dcs_per_bucket,        /* reached max number of dcs allowed in a bucket */
    excl_inst_per_bucket,       /* reached max number of instances in a bucket */
    excl_secondary_dc_count,    /* reached max number of dcs allowed to choose the second broker in multi-dc redirect */
    excl_secondary_dc_dist,     /* dc further than allowed to choose the second broker in multi-dc redirect */
    excl_secondary_dc_cap,      /* reached necessary capacity to choose the second broker in multi-dc redirect */
    excl_primary_dc_load,       /* primary dc reached max allowed load so shedding to secondary dcs */
    excl_secondary_dc_load,     /* not selected as secondary dc because the dc reached max allowed load */
    excl_secondary_dc_inst,     /* chosen enough instances from secondary dcs */

    /* instance selection (in a dc) */
    excl_selected,              /* already a final candidate */

    /* compact */
    excl_bucket_mtn,            /* there are instances matching MTN of the client */
    excl_distance,              /* distance from client */
    excl_policy_action,         /* not a suitable bucket based on redirect policy_action result */

    /* scope */
    excl_scope_mismatch,        /* The scope does not match */
    excl_scope_fetch,           /* Unable to fetch scope id from zpn_private_broker table */
    excl_scope_disabled,        /* This scope was disabled */

    /* load balancing */
    excl_state_not_running,     /* The broker is not running, could be in starting or terminating */
                                /* exclude instances other than running state */
    /* alt-cloud */
    excl_alt_cloud,             /* alt_cloud configured for instance */
    excl_no_alt_cloud,          /* no alt_cloud configured for instance */
    excl_alt_cloud_invalid,     /* alt_cloud for the instance is not in zpath_cloud table */

    /* excluding self */
    excl_self,                  /* excluding the current broker */

    excl_site_gid_mismatch,     /*exclude as private broker not part of the site*/

    excl_ipv6_exclusive,        /* IPv6 exclusive was requested, but instance is not dual-stacked */

    /* MUST BE LAST! */
    excl_total_count
};
const char *zpn_balance_excl_str(enum excl_code code);

enum zpn_balance_instance_selection_method {
    zpn_balance_selection_method_unknown=0,
    zpn_balance_selection_method_metrics,
    zpn_balance_selection_method_client_prediction,

    /* MUST BE LAST! */
    zpn_balance_selection_method_count
};

enum zpn_balance_ipv6_selection_method {
    zpn_balance_ipv6_misconfig_exclusive_skip_disable,
    zpn_balance_ipv6_misconfig_exclusive_skip_client_capability,
    zpn_balance_ipv6_fqdn_prefixed,
    zpn_balance_ipv4_fqdn_only,
    zpn_balance_ipv6_all_excluded,
    /* MUST BE LAST! */
    zpn_balance_ipv6_selection_method_count
};
const char *zpn_balance_selection_method_str(enum zpn_balance_instance_selection_method code);

/*
 * Redirect target candidate.
 * Basically a snapshot of reported_instance, with additional fields.
 */
struct viable_instance {
    /*************************************
     * replicated from reported_instance
     *************************************/
    int64_t gid;
    char *name;
    char *instance_role;
    char *balance_role;
    char *balance_mode;
    enum Zpn_Instance_Type personality;
    char **publish_ips;
    int publish_ips_count;
    char is_public[8];
    uint32_t cpu_pct;
    uint32_t mem_pct;
    uint64_t clients;
    uint32_t load_skew;
    int64_t modified_time_s;
    struct site site;
    int64_t site_gid;

    int is_public_private; /* computed - is_public is not always trust worthy */
    int is_locally_overriden; /* if some of the load data, geo is overriden locally */
    int is_dualstack;         /* if this instance is a dual-stacked instance */

    /*************************************
     * dynamic fields for a specific run
     *************************************/
    /* = 0: chance to be picked is purely by load. No adjustment.
     * > 0: more likely to be selected.
     *     e.g. 50: 50% chance to be selected.
     *     e.g. 200: twice more likely to be selected.
     * < 0: less likely to be selected.
     *     e.g. -50: twice unlikely to be selected.
     *     e.g. < -100: will not be selected.
     */
    uint32_t effective_load; // computed from cpu_pct, mem_pct, load_skew, proc_fd_util
    uint64_t clients_available; // computed from their client count and mem_pct
    //As we finalize and process the total clients this will be used to hold that data
    //we keep it separate so that when recalculation occurs we will still have the original values on hand
    uint64_t adjusted_clients_available;
    uint64_t clients_max;
    int clients_hot;
    int32_t selection_skew_pct;
    uint64_t bucket_min;
    uint64_t bucket_max;
    uint64_t client_bucket_min;
    uint64_t client_bucket_max;

    /* only available to private brokers, looked up from pb config */
    /* TODO: doesn't make sense for publish_ips to be in reported_instance
     * and trusted_networks in viable_instance - should happen in one place. */
    int64_t *trusted_networks_gids;
    size_t trusted_networks_count;
    int64_t pse_grp_gid;    /* PSE group gid for redirect policy lookup */
    /* Should be set to TRUE if the PSE grp to which this instance belongs
     * is exlusive for pcc */
    int pse_grp_exclusive_for_business_continuity;
    double grace_distance;  /* PSE grace distance to prefer it over public brokers */
    double pse_inst_dist;   /* PSE distance (in miles) from client */

    /* once excluded, this instance will no longer
     * be considered a redirect target. */
    int exclude;
    enum excl_code exclusion_reason;
    char *alt_cloud;
    char alt_instance_name[ZPN_MAX_INSTANCE_FULL_NAME_LEN + 1];

    enum zpn_balance_instance_selection_method method;

    /* part of a data_center */
    LIST_ENTRY(viable_instance) entry;

    enum zpn_broker_status brk_status;
    uint32_t proc_fd_util_pct;
};

/* many times the circumference of the earth
 * not using -1 to avoid unsigned weirdness */
#define INFINITE_DIST (500000.0)
#define DC_NAME_LEN (24)
#define DC_SHORT_NAME_LEN (7)
/*
 * List of Redirect target candidate belonging to the same site.
 */
struct data_center {
    struct site site;
    char name[DC_NAME_LEN+1];
    char short_name[DC_SHORT_NAME_LEN+1];
    uint32_t inst_total;        /* total instance count */
    uint32_t inst_part_total;   /* total partitioned instance count */
    uint32_t inst_unpart_total; /* total unpartitioned instance count */
    uint32_t capacity_total;    /* sum of all instances' capacity in the DC, before any instance is selected */
    uint32_t capacity_partitioned;    /* total resource capacity of all instances belonging to the customer's partition */
    uint32_t capacity_unpartitioned; /* total resource capacity of all instances that are not partitioned */
    uint32_t capacity_remaining; /* sum of all available instances' capacity in the DC, this changes as instances get selected */
    uint32_t client_capacity_partitioned; /* total client capacity of all instances belonging to the customer's partition */
    uint32_t client_capacity_unpartitioned; /* total client capacity of all instances that are not partitioned */
    uint32_t client_capacity_remaining; /* total client capacity in the DC, this changes as instances get selected */
    uint32_t skip_partitioned;
    uint32_t effective_load;    /* effective load of the DC is the average instances' effective load */
    double dist;                /* distance (in miles ha!) from client */
    double grace_dist_max;      /* DC grace distance (in miles). Applicable only for Private DCs */
    int same_cc;                /* same cc from client? */
    int exclude;                /* DC exclusion implies instance exclusion */
    enum excl_code exclusion_reason;

    LIST_HEAD(viable_instance_head, viable_instance) inst_list;
    LIST_HEAD(viable_instance_head_part, viable_instance) part_inst_list;
    LIST_HEAD(viable_instance_head_unpart, viable_instance) unpart_inst_list;
};


/*
 * DCs are organized into the following types, aka "buckets".
 */
enum dc_bucket_type {
    BT_PRIV_MTN = 0,
    BT_PRIV,
    BT_PUBL_PRIV_MTN,
    BT_PUBL_PRIV,
    BT_PUBL,
    BT_LAST         /* MUST BE LAST */
};
const char* zpn_balance_bucket_type_str(enum dc_bucket_type bt);

struct dc_bucket {
    enum dc_bucket_type type;
    size_t max_selected_inst;
    size_t max_selected_dc;
    size_t max_selected_inst_per_dc;
    struct data_center *dcs;
    size_t dc_count;            /* valid dcs */
    size_t capacity;            /* allocated */
    double dist_best_dc;        /* shortest dist from client of any DC in this bucket */
    size_t apply_grace_dist_btwn_dcs;   /* is grace_distance applied between DCs. Applicable only for PRIV bucket */
    int exclude;                /* bucket exclusion implies DC exclusion */
    enum excl_code exclusion_reason;
    struct viable_instance *verdict[MAX_INSTANCES_PER_BUCKET];  /* selected instances from this bucket */
    size_t verdict_count;
    /* for book-keeping and stats only */
    size_t verdict_count_mdc_pdc;
    size_t verdict_count_mdc_sdc;

    /*
     * primary DC and secondary DCs, only for public multi-DC redirect for now
     * up to 2 instances are chosen from primary.
     * 1 instance is chosen among secondaries, or 2 if primary dc yielded no brokers.
     */
    struct data_center *primary_dc;
    struct data_center *secondary_dc[MAX_SECONDARY_DC_COUNT_HI];
    size_t secondary_dc_count;
};

struct dc_buckets {
    struct dc_bucket bucket[BT_LAST];
};

enum dbg_balance_exec_phase {
    DBG_EXEC_PHASE_IGNORE_ME = 0,
    DBG_EXEC_PHASE_INST_LOAD,
    DBG_EXEC_PHASE_REDIRECT_POLICY,
    DBG_EXEC_PHASE_INST_FILTER,
    DBG_EXEC_PHASE_DC_BUCKET,
    DBG_EXEC_PHASE_DC_SELECT,
    DBG_EXEC_PHASE_INST_SELECT,
    DBG_EXEC_PHASE_COMPACT,
};

/*
 * A function prototype for tracing balance redirect logic. Used in debug commands.
 */
typedef void (zpn_balance_trace_fn)(enum argo_log_priority priority, void* cookie, const char *format, ...);

/*
 * A function prototype to indicate change in broker balance control for this broker.
 */
typedef void (zpn_broker_balance_control_notify_fn)();

void zpn_balance_set_notify_cb(zpn_broker_balance_control_notify_fn *cb);
/*
 * Internal states pertinent to handling a single balance redirect request.
 * Contains original request passed to zpn_balance_redirect as well as
 * internal states as the request passes through various stages/tasks.
 *
 * Generally, this is the input to all algorithms.
 * Algorithms implement visitor patter, generating and modifying internal states.
 *
 * balance_request_configuration
 * Encapsulates the config at the time of request setup
 * This ensures that during the lifetime of the redirect request configuration changes cannot occur
 * Resulting in a consistent behavior even when config changes are occurring
 */
struct balance_request_configuration {
    struct {
        int64_t enabled;
        int64_t max;
        int64_t threshold;
    } retries;

    struct {
        int64_t hard_disabled;
        int64_t se_rate;
        int64_t se_enabled;
        int64_t pse_rate;
        int64_t pse_enabled;
        int64_t min_load_threshold;
        int64_t min_clients_threshold;

    } client_counts;
};

struct balance_request_state {
    struct zpn_balance_redirect_request *req;

    /* for tracing to debug interface */
    zpn_balance_trace_fn* trace_f;
    void* trace_f_cookie;
    enum dbg_balance_exec_phase dbg_stop_after; /* debugger wants to run to a certain step only */

    /* dynamic config coming from config_ovd */
    uint32_t pse_grace_dist_miles;

    /* PSE grace_distance state for current execution */
    int grace_dist_state;
    int is_pse_group_grace_dist_configured;

    /* geofencing state for current execution */
    int gf_state;

    /* computed state */
    int do_multi_dc_redir_public;

    /* timing and err of execution */
    int res;
    int64_t usec_start;
    int64_t usec_inst_load;
    int64_t usec_policy_execute;
    int64_t usec_inst_filter;
    int64_t usec_bucket;
    int64_t usec_dc_select;
    int64_t usec_inst_select;
    int64_t usec_compact;
    int64_t usec_stop;

    /* Redirect policy evaluation state for current execution */
    struct zpn_broker_redirect_policy_state *policy_state;

    /* roll-up of exclusion code encountered */
    int16_t exclusions[excl_total_count];

    /* - instance fetching -
     * Note: if constellation_gid_count > 0, public instances not included in instance_gids
     * are NOT considered since they are not part of customer's constellations.
     */
    int64_t constellation_gids[MAX_TOPLEVEL_CONSTELLATIONS_PER_CUSTOMER];
    size_t constellation_gid_count;
    int64_t constellation_gids_excl[MAX_TOPLEVEL_CONSTELLATIONS_PER_CUSTOMER];
    size_t constellation_gid_excl_count;
    int64_t instance_gids[ZPN_MAX_BROKERS];
    size_t instance_gid_count;

    /* built by instance fetching, modified by instance filtering */
    struct viable_instance *viable_instances;
    size_t viable_instance_count;
    size_t viable_instance_capacity;

    /* bucketing */
    int64_t *client_mtn_gids;
    size_t client_mtn_gids_count;
    struct dc_buckets dc_buckets;

    /* final verdict after compression */
    char *instance_name[MAX_TOTAL_INSTANCES]; //FQDN or IP
    char *sni_suffixes[MAX_TOTAL_INSTANCES];
    int instance_type[MAX_TOTAL_INSTANCES];         //used in IPv4/IPv6 touchup
    int instance_is_dualstack[MAX_TOTAL_INSTANCES]; //used in IPv4/IPv6 touchup
    size_t instance_count;

    int exclude_alt_cloud_instances;
    int use_client_counts;
    struct balance_request_configuration configuration;
};

int zpn_balance_config_init(struct wally *wally,
                            struct wally_origin *slave_db,
                            struct wally_origin *remote_db,
                            int64_t current_instance_gid);

int zpn_balance_cfg_broker_control_row_cb(void *cookie,
                        struct wally_registrant *registrant,
                        struct wally_table *table,
                        struct argo_object *previous_row,
                        struct argo_object *row,
                        int64_t request_id);
void get_my_site(struct site *my_site);

int zpn_broker_balance_debug_init();

int debug_run_and_analyze_common_return_state(struct zpath_debug_state *request_state,
                                        const char                  *client_type,
                                        const char                  *scope_gid,
                                        const char                  *client_ip,
                                        const char                  *client_lat,
                                        const char                  *client_lon,
                                        const char                  *client_cc,
                                        const char                  *client_mtn,
                                        const char                  *client_saml,
                                        const char                  *client_scim,
                                        const char                  *client_scim_group,
                                        int                         do_trace,
                                        int                         do_all,
                                        enum dbg_balance_exec_phase  dbg_stop_after,
                                        int                         auto_flip_geo,
                                        int                         run_n,
                                        int16_t                     do_stats,
                                        int                         alt_cloud_aware,
                                        int                         ipv6_redirect_supported,
                                        int                         exclude_self,
                                        const char                  *site_gid,
                                        struct zpn_balance_redirect_request *req,
                                        struct balance_request_state *state);

int debug_run_and_analyze_common(struct zpath_debug_state    *request_state,
                                const char                  *client_type,
                                const char                  *scope_gid,
                                const char                  *client_ip,
                                const char                  *client_lat,
                                const char                  *client_lon,
                                const char                  *client_cc,
                                const char                  *client_mtn,
                                const char                  *client_saml,
                                const char                  *client_scim,
                                const char                  *client_scim_group,
                                int                         do_trace,
                                int                         do_all,
                                enum dbg_balance_exec_phase dbg_stop_after,
                                int                         iter,
                                int16_t                     do_stats,
                                int                         auto_flip_geo,
                                int                         alt_cloud_aware,
                                int                         ipv6_redirect_supported,
                                int                         exclude_self,
                                const char                  *site_gid);
/* only for debugging purposes, prints out the whole execution state*/
int debug_print_balance_state(struct balance_request_state *state,
                              struct zpath_debug_state *request_state,
                              int do_all);
void log_execution_condensed(struct balance_request_state *state);
struct argo_object *debug_tokenize_trusted_networks(const char* query_str);

/* Update broker load - need to be invoked externally for all instances
 * meant to be redirect targets. */
int zpn_balance_inst_load_update_broker(int64_t gid,
                                        int8_t deleted,
                                        char *name,
                                        uint32_t cpu_util,
                                        uint32_t mem_util,
                                        uint64_t clients,
                                        uint32_t load_skew,
                                        char *role,
                                        char *mode,
                                        int32_t modified_time,
                                        uint16_t status,
                                        uint16_t proc_fd_util);
int zpn_balance_inst_load_update_pbroker(int64_t gid,
                                        int8_t deleted,
                                        char **publish_ips,
                                        int publish_ips_count,
                                        uint32_t cpu_util,
                                        uint32_t mem_util,
                                        uint64_t clients,
                                        int32_t modified_time);

int zpn_balance_inst_load_init();

int zpn_balance_inst_load_execute(struct balance_request_state* state);
void zpn_balance_inst_load_free(struct balance_request_state* state);

int zpn_balance_policy_execute(struct balance_request_state* state);
int zpn_balance_policy_free(struct balance_request_state* state);

int zpn_balance_inst_filter_execute(struct balance_request_state* state);
void zpn_balance_inst_filter_free(struct balance_request_state* state);

int zpn_balance_dc_bucket_execute(struct balance_request_state* state);
void zpn_balance_dc_bucket_free(struct balance_request_state* state);

int zpn_balance_dc_select_execute(struct balance_request_state* state);
void zpn_balance_dc_select_free(struct balance_request_state* state);

int zpn_balance_compact_execute(struct balance_request_state* state);
void zpn_balance_compact_free(struct balance_request_state* state);

int zpn_balance_inst_select_execute(struct balance_request_state* state);
void zpn_balance_inst_select_free(struct balance_request_state* state);

/* Return grace_distance feature flag value*/
int zpn_balance_get_grace_distance_feature_status(int64_t customer_gid, int64_t instance_gid);

/* Return IPv6 PSE redirect feature flag value*/
int zpn_balance_get_pse_ipv6_redirect_feature_status(int64_t customer_gid, int64_t instance_gid);

/* Return if IPv6 redirect feature is disabled*/
int zpn_balance_get_ipv6_redirect_feature_enabled_status(int64_t customer_gid, int64_t instance_gid);

/* Return IPv6 exclusive redirect feature flag value*/
int zpn_balance_get_ipv6_exclusive_redirect_feature_status(int64_t customer_gid);

/* Return string with will act as a prefix to a broker's FQDN */
char *zpn_balance_get_dualstack_brk_fqdn_prefix(int64_t customer_gid, int64_t instance_gid);

/* Return redirect policy sub-module criteria feature flags*/
int zpn_balance_get_redirect_policy_saml_status(int64_t customer_gid, int64_t instance_gid);
int zpn_balance_get_redirect_policy_cc_status(int64_t customer_gid, int64_t instance_gid);
int zpn_balance_get_redirect_policy_fallback_broker_status(int64_t customer_gid, int64_t instance_gid);

int zpn_broker_balance_get_ipv6_specific_flags(struct zpn_balance_redirect_request *req, int ipv6_redirect_supported, int64_t instance_gid, const char* dbg_msg);


void compute_dc_capacity(struct data_center *dc,
                         int64_t is_lp_enabled,
                         enum dc_bucket_type bucket_type,
                         struct balance_request_configuration *configuration);

uint32_t compute_dc_effective_load(struct data_center *dc, enum lp_inst_type type);

uint32_t zpn_balance_compute_instance_effective_load(uint32_t cpu_pct, uint32_t mem_pct,
                                                     uint32_t proc_fd_util_pct, uint32_t load_skew);
/* Important: if param state_out is provided:
 * - the caller wishes to receive the execution state for inspection.
 * - the caller must call zpn_balance_redirect_free() on the received state,
 *   or memory will be leaked!
 */
int zpn_balance_redirect_internal(struct zpn_balance_redirect_request* req,
                                  zpn_balance_trace_fn* trace_f,
                                  void* trace_f_cookie,
                                  struct balance_request_state **state_out);
int zpn_balance_redirect_execute(struct balance_request_state* state);
void zpn_balance_redirect_free(struct balance_request_state* state);

/* If no remote geo info provided, fill in with data of the current instance
 * Note: all parameters are in/out!
 */
void zpn_balance_sanitize_remote_location(double *lat, double *lon, char *cc);

/* stats */
int zpn_balance_stats_init();
/* analyze the result of an execution and generate/add to stats */
void zpn_balance_redirect_collect_stats(struct balance_request_state* state);

void zpn_balance_update_pb_redirect_stats_fqdn();
void zpn_balance_update_pb_redirect_stats_v4_or_v6();
void zpn_balance_update_pb_redirect_stats_v4_and_v6();

/* ET-65485 instance load client num stats updaters */
typedef void(zpn_balance_client_load_stat_inc_f)();
void zpn_balance_client_load_update_all_num_clients_under_threshold_or_unreported();
void zpn_balance_client_load_update_client_metrics_all_under_threshold();
void zpn_balance_client_load_update_no_reported_clients_load();
void zpn_balance_client_load_update_no_reported_clients_count();
void zpn_balance_client_load_update_reported_load_exceeded_100();
void zpn_balance_client_load_update_successfully_calculated_max_clients();
void zpn_balance_client_load_update_clients_hot_count();

/* E-65485 broker selection stats */
void zpn_balance_client_load_update_total_brokers_selected();
void zpn_balance_client_load_update_instance_select_attempt_total();
void zpn_balance_client_load_update_instance_select_complete_total();
void zpn_balance_client_load_update_partitioned_clients_available_exceeded_max();
void zpn_balance_client_load_update_unpartitioned_clients_available_exceeded_max();
void zpn_balance_client_load_update_instance_clients_available_exceeded_max();
void zpn_balance_client_load_update_instance_select_using_clients();
void zpn_balance_client_load_update_instance_select_using_metrics();
void zpn_balance_load_update_selection_stats(enum zpn_balance_instance_selection_method method, int selection_round);

void zpn_balance_client_load_update_no_brokers_selected();

/* ET-81403 - IPv6-aware redirection stats */
void zpn_balance_ipv6_stats(int selection_round);

void zpn_balance_get_current_configuration(struct balance_request_configuration *config);
void zpn_balance_get_instance_selection_retry_configuration(struct balance_request_configuration *configuration);
void zpn_balance_get_client_counts_configuration(struct balance_request_configuration *configuration);
int zpn_balance_is_client_estimation_enabled_for_instance(struct balance_request_configuration *configuration, enum Zpn_Instance_Type personality);
int zpn_balance_is_client_estimation_enabled_for_bucket(struct balance_request_configuration *configuration, enum dc_bucket_type bucket_type);


#define BALANCE_INSTANCE_TYPE_BROKER  "zpn_brokerd"
#define BALANCE_INSTANCE_TYPE_ACAS    "zpn_acas"

#define EMPTY_STR ""

#define POLICY_ARG_EXPECTED_TOKENS      4
#define POLICY_ARG_DELIMETER            "|"
#define POLICY_ATTR_VALUE_TAG_POSITION   3
#define POLICY_ATTR_NAME_TAG_POSITION    2

enum policy_arg_type {
    POLICY_ARG_TYPE_SAML = 0,
    POLICY_ARG_TYPE_SCIM
};



extern struct zpath_allocator zpn_balance_allocator;
#define ZPN_BALANCE_MALLOC(x) zpath_malloc(&zpn_balance_allocator, x, __LINE__, __FILE__)
#define ZPN_BALANCE_FREE(x) zpath_free(x, __LINE__, __FILE__)
#define ZPN_BALANCE_FREE_SLOW(x) zpath_free_slow(x, __LINE__, __FILE__)
#define ZPN_BALANCE_CALLOC(x) zpath_calloc(&zpn_balance_allocator, x, __LINE__, __FILE__)
#define ZPN_BALANCE_REALLOC(x, y) zpath_realloc(&zpn_balance_allocator, x, y, __LINE__, __FILE__)
#define ZPN_BALANCE_STRDUP(x, y) zpath_strdup(&zpn_balance_allocator, x, y, __LINE__, __FILE__)

#define ZPN_BLANCE_DUMP_ROW(tbl, row) \
    if (zpn_debug_get(ZPN_DEBUG_BALANCE_IDX)) { \
        char dump[8000];\
        if (argo_object_dump(row, dump, sizeof(dump), NULL, 1) == ARGO_RESULT_NO_ERROR) {\
            ZPN_DEBUG_BALANCE("Row callback on table %s: %s", tbl, dump);\
        }\
    }\

/* Customized logging functions, with the ability to invoke an optional callback function */
#define ZPN_DEBUG_BALANCE_C(format...) \
{ \
    if (trace_f) trace_f(AL_DEBUG, trace_f_cookie, ##format); \
    ZPN_DEBUG_LOG(zpn_debug_get(ZPN_DEBUG_BALANCE_IDX), ##format); \
}

#define ZPN_BALANCE_LOG_C(priority, format...) \
{ \
    if (trace_f) trace_f(priority, trace_f_cookie, ##format); \
    ZPN_LOG(priority, ##format); \
}

/* Logging helpers for balance_request_state, with the ability to invoke an optional callback function */
#define ZPN_DEBUG_BALANCE_STATE(format...) \
{ \
    if (state->trace_f) state->trace_f(AL_DEBUG, state->trace_f_cookie, ##format); \
    ZPN_DEBUG_LOG(zpn_debug_get(ZPN_DEBUG_BALANCE_IDX), ##format); \
}

#define ZPN_BALANCE_LOG_STATE(priority, format...) \
{ \
    if (state->trace_f) state->trace_f(priority, state->trace_f_cookie, ##format); \
    ZPN_LOG(priority, ##format); \
}

#define max(a, b) (((a) > (b)) ? (a) : (b))
#define min(a, b) (((a) < (b)) ? (a) : (b))

#endif // _ZPN_BALANCE_PRIVATE_
