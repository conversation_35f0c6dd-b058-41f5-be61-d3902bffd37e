/*
 * zpn_balance_inst_filter.c. Copyright (C) 2022 Zscaler, Inc. All Rights Reserved.
 */

#include "zpn/zbalance/zpn_balance_private.h"
#include "zpn/zpn_scope.h"
#include "zpath_lib/zpath_local.h"
#include "zpn/zpn_broker_assert.h"

#ifdef ZBALANCE_TESTING
#include "test_misc/testing_macros.h"
#else
#include "test_misc/production_macros.h"
#endif
int filter_by_scope(struct balance_request_state* state) __attribute__((weak));

/*
 * Algorithm Description:
 * Exclude any instance that has not reported since "report_age_exclusion_s" seconds
 * prior to the latest known report timestamps among all viable instances.
 */
static int filter_by_report_time_exclusion(struct balance_request_state* state)
{
    int64_t exclusion_time_s = 0; /* no exclusion by default */
    int64_t latest_load_report_time_s = 0;
    int i;

    /* find most recent time any instance reported its load */
    for (i = 0; i < state->viable_instance_count; i++) {
        if (state->viable_instances[i].modified_time_s > latest_load_report_time_s) {
            latest_load_report_time_s = state->viable_instances[i].modified_time_s;
        }
    }
    if (latest_load_report_time_s) {
        /* anything reported later than this will be considered dead */
        exclusion_time_s = latest_load_report_time_s - g_balance_config.report_age_exclusion_s;
    }
    ZPN_DEBUG_BALANCE_STATE("%s latest_load_report_time_s = %"PRId64", exclusion_time_s = %"PRId64". "
                        "Instances reported before exclusion_time_s will not be considered.",
                        state->req->log_tag, latest_load_report_time_s, exclusion_time_s);

    /* exclude everything considered dead */
    for (i = 0; i < state->viable_instance_count; i++) {
        if (!state->viable_instances[i].exclude && state->viable_instances[i].modified_time_s < exclusion_time_s) {
            state->viable_instances[i].exclude = 1;
            state->viable_instances[i].exclusion_reason = excl_report_time;
        }
    }
    return ZPN_RESULT_NO_ERROR;
}


/*
 * Algorithm Description:
 * Reduce the chance of instances with report age older than X seconds by 50%.
 * X is configured with CONFIG_LOAD_STALE_AGE_S config override.
 */
static int filter_by_report_time_staleness(struct balance_request_state *state)
{
    int64_t now_s = epoch_s();
    int64_t stale_report_s = now_s - g_balance_config.report_age_reduction_s;

    for (int i = 0; i < state->viable_instance_count; i++) {
        if (!state->viable_instances[i].exclude && state->viable_instances[i].modified_time_s < stale_report_s) {
        ZPN_DEBUG_BALANCE_STATE("%s %s reported at %"PRId64", before %"PRId64" and will have its chance of getting picked reduced.",
                            state->req->log_tag,
                            state->viable_instances[i].name? state->viable_instances[i].name : "<no-name>",
                            state->viable_instances[i].modified_time_s,
                            stale_report_s);
            state->viable_instances[i].selection_skew_pct = -50;
        }
    }
    return ZPN_RESULT_NO_ERROR;
}

/*
 * Algorithm Description:
 * If the client is alt cloud aware and it is in geofenced countries
 *  - exclude all the brokers which do not have alt domain configured
 * if the client is alt cloud aware and it is not in geofenced countries
 *  - ecclude all the brokers which have alt domain configured.
 * if the client is not alt cloud aware
 *  - exclude all the brokers which have alt domain configured.
 */
static int filter_by_alt_cloud(struct balance_request_state *state)
{
    int exclude_alt_cloud_instances = 1;
    char *alt_cloud;

    if (state->req->alt_cloud_supported && state->req->alt_cloud_aware && state->gf_state == geo_include) {
        exclude_alt_cloud_instances = 0;
    }

    state->exclude_alt_cloud_instances = exclude_alt_cloud_instances;

    for (int i = 0; i < state->viable_instance_count; i++) {
        if (state->viable_instances[i].exclude) continue;

        alt_cloud = state->viable_instances[i].alt_cloud;
        if (!zpn_broker_is_alt_cloud_valid(alt_cloud)) {
            ZPN_DEBUG_BALANCE_STATE("%s %s excluded - alt_cloud = %s invalid",
                                state->req->log_tag,
                                state->viable_instances[i].name ? state->viable_instances[i].name : "<no-name>",
                                alt_cloud);
            state->viable_instances[i].exclude = 1;
            state->viable_instances[i].exclusion_reason = excl_alt_cloud_invalid;
            continue;
        }


        if (exclude_alt_cloud_instances) {
            if (alt_cloud) {
                ZPN_DEBUG_BALANCE_STATE("%s %s excluded - alt_cloud = %s configured",
                                state->req->log_tag,
                                state->viable_instances[i].name ? state->viable_instances[i].name : "<no-name>",
                                alt_cloud);
                state->viable_instances[i].exclude = 1;
                state->viable_instances[i].exclusion_reason = excl_alt_cloud;
            }
        } else {
            if (!alt_cloud) {
                ZPN_DEBUG_BALANCE_STATE("%s %s excluded - alt_cloud not configured",
                                state->req->log_tag,
                                state->viable_instances[i].name ? state->viable_instances[i].name : "<no-name>");
                state->viable_instances[i].exclude = 1;
                state->viable_instances[i].exclusion_reason = excl_no_alt_cloud;
            }
        }
    }
    return ZPN_RESULT_NO_ERROR;
}

static int filter_by_exclude_self(struct balance_request_state *state)
{
    /* if exclude_self is not configured, need not proceed */
    if (!state->req->exclude_self) {
        return ZPN_RESULT_NO_ERROR;
    }

    for (int i = 0; i < state->viable_instance_count; i++) {
        if (state->viable_instances[i].exclude)
            continue; /* someone else got to it first ! */

        if (!state->viable_instances[i].name)
            continue;

        /* look for self broker name and exclude */
        if (0 == strcmp(state->viable_instances[i].name, ZPATH_LOCAL_FULL_NAME)) {
            state->viable_instances[i].exclude = 1;
            state->viable_instances[i].exclusion_reason = excl_self;
            break;
        }
    }
    return ZPN_RESULT_NO_ERROR;
}

static __inline__ int count_viable_instances_not_excluded(const struct balance_request_state* state)
{
    int count = 0;
    for (int i = 0; i < state->viable_instance_count; i++) {
        if (!state->viable_instances[i].exclude) count++;
    }
    return count;
}

/* Outcome:
 * 1. excluded instances sink to the bottom
 * 2. then reverse order by modified_time_s (lateste first)
 */
static int compare_modified_time(const void *a, const void *b)
{
    const struct viable_instance *inst_a = a;
    const struct viable_instance *inst_b = b;
    int res = (inst_a->exclude ? 1 : 0) - (inst_b->exclude ? 1 : 0);
    if (!res) res = (int)(inst_b->modified_time_s - inst_a->modified_time_s);
    return res;
}

/*
 * Algorithm Description:
 * Exclude any instance that has not reported since "GRACE_PERIOD_FROM_MEDIAN_S" seconds
 * prior to the median of report timestamps among all viable instances.
 */
static int filter_by_report_time_median(struct balance_request_state* state)
{
#define GRACE_PERIOD_FROM_MEDIAN_S 180

    int count;
    int median_idx;
    int64_t exclusion_time_s; /* no exclusion by default */

    ZPN_BROKER_ASSERT_HARD(state->viable_instances != NULL, "Viable Instances is NULL!"); /* otherwise should've errored out before reaching here */

    qsort(state->viable_instances,
          state->viable_instance_count,
          sizeof(struct viable_instance),
          compare_modified_time);

    count = count_viable_instances_not_excluded(state);
    if (!count) return ZPN_RESULT_NO_ERROR;

    /* Compute the median report time minus 3 min any instance reported earlier will be excluded. */
    median_idx = (count + 1) / 2 - 1;

    exclusion_time_s = state->viable_instances[median_idx].modified_time_s - GRACE_PERIOD_FROM_MEDIAN_S;
    ZPN_DEBUG_BALANCE_STATE("%s report time median = %"PRId64", exclude instances reported prior to %"PRId64"",
                        state->req->log_tag, state->viable_instances[median_idx].modified_time_s, exclusion_time_s);

    /* exclude everything considered too old */
    for (int i = median_idx + 1; i < count; i++) {
        if (!state->viable_instances[i].exclude && state->viable_instances[i].modified_time_s < exclusion_time_s) {
            state->viable_instances[i].exclude = 1;
            state->viable_instances[i].exclusion_reason = excl_report_median;
        }
    }
    return ZPN_RESULT_NO_ERROR;
}

/*
 * Algorithm Description:
 * Include brokers in running state
 * Exclude any instance that is not in running state
 * zpn_broker_load table status field indicateds this value
 * 0 - running/started
 */
static int filter_by_instance_status(struct balance_request_state* state)
{
    for (int i = 0; i < state->viable_instance_count; i++) {
        if (state->viable_instances[i].exclude) continue; /* someone else got to it first ! */

        if(state->viable_instances[i].brk_status != RUNNING) {
            ZPN_DEBUG_BALANCE_STATE("%s %s excluded - brk_status = %d(%s)",
                    state->req->log_tag,
                    state->viable_instances[i].name ? state->viable_instances[i].name : "<no-name>",
                    state->viable_instances[i].brk_status,
                    zpn_broker_get_status_string(state->viable_instances[i].brk_status));
            state->viable_instances[i].exclude = 1;
            state->viable_instances[i].exclusion_reason = excl_state_not_running;
            continue;
        }
    }
    return ZPN_RESULT_NO_ERROR;
}

/*
 * Algorithm Description:
 * Exclude redirect brokers as a redirect target.
 * Exclude any instance that is not configured with balance mode "BALANCE_MODE_BALANCE".
 * BALANCE_MODE_BALANCE indicates the instance's intention to be a redirect target.
 */
static int filter_by_instance_balance_role_mode(struct balance_request_state* state, int req_public_broker)
{
    for (int i = 0; i < state->viable_instance_count; i++) {
        struct zpn_broker_balance_control *bc=NULL;
        /* If configuration is updated to change the broker mode to MAINTENANCE and recent,
         * only then we give preference to control table, otherwise, data from instance table is preferred.*/
        if ((req_public_broker && zpn_broker_balance_control_get_by_id_immediate(state->viable_instances[i].gid, &bc) == ZPN_RESULT_NO_ERROR ) &&
            (bc != NULL && bc->balance_mode != NULL && bc->balance_role != NULL) &&
            !strcmp(bc->balance_mode, BALANCE_MODE_MAINTENANCE) &&
            (bc->modified_time > state->viable_instances[i].modified_time_s)) {
            ZPN_DEBUG_BALANCE_STATE("%s %s Prefer balance_role and balance_mode from control table - control table time:%"PRId32", instance_table time:%"PRId64"",
                            state->req->log_tag,
                            state->viable_instances[i].name ? state->viable_instances[i].name : "<no-name>",
                            bc->modified_time, state->viable_instances[i].modified_time_s);
            ZPN_BALANCE_FREE(state->viable_instances[i].balance_role);
            ZPN_BALANCE_FREE(state->viable_instances[i].balance_mode);
            state->viable_instances[i].balance_role  = ZPN_BALANCE_STRDUP(bc->balance_role, strlen(bc->balance_role));
            state->viable_instances[i].balance_mode  = ZPN_BALANCE_STRDUP(bc->balance_mode, strlen(bc->balance_mode));
        }
        if (state->viable_instances[i].exclude) continue; /* someone else got to it first ! */

        /* if balance_role is configured to be REDIRECT, i.e. this is a redirect broker,
         * eliminate as a redir target. */
        if (state->viable_instances[i].balance_role && !strcmp(state->viable_instances[i].balance_role, BALANCE_ROLE_REDIRECT)) {
            ZPN_DEBUG_BALANCE_STATE("%s %s excluded - balance_role = %s",
                                state->req->log_tag,
                                state->viable_instances[i].name ? state->viable_instances[i].name : "<no-name>",
                                state->viable_instances[i].balance_role ? state->viable_instances[i].balance_role : EMPTY_STR);
            state->viable_instances[i].exclude = 1;
            state->viable_instances[i].exclusion_reason = excl_balance_role;
            continue;
        }
        /* if balance_role is not configured, or configured to be anything other than BALANCE,
         * e.g. in maintenance, eliminate as a redir target. */
        if (!state->viable_instances[i].balance_mode || strcmp(state->viable_instances[i].balance_mode, BALANCE_MODE_BALANCE)) {
            ZPN_DEBUG_BALANCE_STATE("%s %s excluded - balance_mode = %s",
                                state->req->log_tag,
                                state->viable_instances[i].name ? state->viable_instances[i].name : "<no-name>",
                                state->viable_instances[i].balance_mode ? state->viable_instances[i].balance_mode : EMPTY_STR);
            state->viable_instances[i].exclude = 1;
            state->viable_instances[i].exclusion_reason = excl_balance_mode;
            continue;
        }

    }
    return ZPN_RESULT_NO_ERROR;
}

/*
 * Algorithm Description:
 * Certain client types can be rebalanced to only certain types of instances.
 * Current use case:
 * - zbcs client can only speak to acas, not brokers.
 * - other client can only speak to brokers, not acas.
 */
struct zpn_client_type_instance_role_mapping {
    enum zpn_client_type ct;
    char *instance_role;
    int nullable; /* accept if instance doesn't have a role configured */
};
static struct zpn_client_type_instance_role_mapping ct_to_role[] = {
    { zpn_client_type_branch_conn_svc,   BALANCE_INSTANCE_TYPE_ACAS,        0 },
    { 0,                                 BALANCE_INSTANCE_TYPE_BROKER,      1 } // this last one is default!!!
};
static __inline__ struct zpn_client_type_instance_role_mapping *get_ct_to_role(enum zpn_client_type ct) {
    struct zpn_client_type_instance_role_mapping *res = NULL;
    for (int i = 0; i < sizeof(ct_to_role)/sizeof(struct zpn_client_type_instance_role_mapping); i++) {
        res = &ct_to_role[i];
        if (res->ct == ct) { return res; }
    }
    return res; // last one is the default.
}

static int filter_by_client_type_and_instance_role(struct balance_request_state* state)
{
    struct zpn_client_type_instance_role_mapping *cfg = get_ct_to_role(state->req->client_type);
    ZPN_BROKER_ASSERT_HARD(cfg && cfg->instance_role, "Instance role in NULL!");
    for (int i = 0; i < state->viable_instance_count; i++) {
        if (state->viable_instances[i].exclude) continue; /* someone else got to it first ! */
        if (!state->viable_instances[i].instance_role) {
            if (cfg->nullable) continue;
            state->viable_instances[i].exclude = 1;
            state->viable_instances[i].exclusion_reason = excl_req_inst_role;
        } else if (strcmp(state->viable_instances[i].instance_role, cfg->instance_role)) {
            state->viable_instances[i].exclude = 1;
            state->viable_instances[i].exclusion_reason = excl_req_inst_role;
        }
        if (state->viable_instances[i].exclude){
            ZPN_DEBUG_BALANCE_STATE("%s %s excluded - instance_role is %s and client type is %s",
                                    state->req->log_tag,
                                    state->viable_instances[i].name ? state->viable_instances[i].name : "<no-name>",
                                    state->viable_instances[i].instance_role ? state->viable_instances[i].instance_role : "<null>",
                                    zpn_client_type_string(state->req->client_type));
        }
    }
    return ZPN_RESULT_NO_ERROR;
}

STATIC_INLINE int filter_by_client_ipv6_strict_preference(struct balance_request_state* state)
{
    int ipv6_exclusive_wanted = state->req->ipv6_exclusive_wanted;
    int excl_count = 0;
    int public_brokers = 0;

    if (ipv6_exclusive_wanted) {
        /* Filter out all PUBLIC instances which are NOT dual-stacked since customer wants ONLY IPv6 as transport */
        for (int i = 0; i < state->viable_instance_count; i++) {

            if (state->viable_instances[i].exclude) continue; /* someone else got to it first */

            if (state->viable_instances[i].personality != ZPN_INSTANCE_TYPE_PUBLIC_BROKER) {
                continue; //IPv6-exclusive constraint applies only to public brokers
            } else {
                public_brokers++; //count number of public brokers we have
            }

            if (state->viable_instances[i].is_dualstack == 0) {
                state->viable_instances[i].exclude = 1;
                state->viable_instances[i].exclusion_reason = excl_ipv6_exclusive;
                ZPN_DEBUG_BALANCE_STATE("%s %s excluded - instance is not dualstacked when request is for IPv6 exclusive",
                                        state->req->log_tag,
                                        state->viable_instances[i].name ? state->viable_instances[i].name : "<no-name>");
                excl_count++;
            }
        }

        ZPN_DEBUG_BALANCE_STATE("%s excluded %d non-dualstacked instances for IPv6 exclusive", state->req->log_tag, excl_count);

        /* If all viable public brokers have been excluded...lets holler about that fact */
        if (public_brokers && (public_brokers == excl_count)) {
            zpn_balance_ipv6_stats(zpn_balance_ipv6_all_excluded);
            ZPN_LOG(AL_ERROR, "%s all %d public brokers instances have been excluded due to IPv6 exclusive filtering", state->req->log_tag, public_brokers);
        }
    }

    return ZPN_RESULT_NO_ERROR;
}

static int pub_ip_published(char **publish_ips, int publish_ips_count) {
    for (int i = 0; i < publish_ips_count; i++) {
        struct argo_inet inet;
        if ((argo_string_to_inet(publish_ips[i], &inet) == ARGO_RESULT_NO_ERROR)
              && (inet.length == 4 || inet.length == 16)
              && !argo_inet_is_private(&inet))
            return 1;
    }
    return 0;
}

static int is_redirect_policy_get_pse_based_on_site(struct balance_request_state* state)
{
    int res = 1;

    if (state->policy_state) {
        switch (state->policy_state->redirect_action) {
            case zpe_access_action_redirect_preferred:
                res = (state->policy_state->inst_match_found) ? 0 : 1;
                break;
            case zpe_access_action_redirect_always:
                res = (state->policy_state->inst_match_found) ? 0 : 1;
                if (res) {
                    res = (state->req->policy_req->is_fallback_broker_enabled) ? 1 : 0;
                }
                break;
            case zpe_access_action_redirect_default:
            default:
                res  = 1;
                break;
        }
    }

    return res;
}
/*
 * Algorithm Description:
 * Exclude Private broker instances from redirect target which aren't matching customer configured redirect policy.
 *
 */
static int filter_by_redirect_policy(struct balance_request_state* state) {
    int res = ZPN_RESULT_NO_ERROR;
    int match_found = 0;
    int include_fallback_broker = 1;

    if (!state->policy_state || state->policy_state->redirect_action == zpe_access_action_redirect_default) {
        // Policy result isn't available or redirect action is default
        return ZPN_RESULT_NO_ERROR;
    }

    if (!state->policy_state->policy_result_htable) {
        // Bad hash table result
        ZPN_BALANCE_LOG_STATE(AL_ERROR, "%s Error filtering by redirect policy. NULL result\n", state->req->log_tag);
        return ZPN_RESULT_NO_ERROR;
    }

    if (state->policy_state->redirect_action == zpe_access_action_redirect_always) {
        include_fallback_broker = (state->req->policy_req->is_fallback_broker_enabled) ? 1 : 0;
    }

    for (int i = 0; i < state->viable_instance_count; ++i) {
        struct viable_instance *inst = &state->viable_instances[i];
        if (inst->exclude) continue; // someone got to it first

        if (inst->personality == ZPN_INSTANCE_TYPE_PRIVATE_BROKER) {
            if (zhash_table_lookup(state->policy_state->policy_result_htable, &inst->pse_grp_gid, sizeof(inst->pse_grp_gid), NULL)) {
                match_found = 1;
                state->policy_state->inst_match_found = 1;
                break;
            }
        }
    }

    for (int i = 0; i < state->viable_instance_count; ++i) {
        struct viable_instance *inst = &state->viable_instances[i];
        if (inst->exclude) continue; // someone got to it first

        if (inst->personality == ZPN_INSTANCE_TYPE_PRIVATE_BROKER) {
            // Exclude private instances which aren't matching client redirect policy result instances
            if (!zhash_table_lookup(state->policy_state->policy_result_htable, &inst->pse_grp_gid, sizeof(inst->pse_grp_gid), NULL)) {
                if (!match_found && state->req->filter_by_site_gid && include_fallback_broker) {
                        /* when redirect action is either preferred or always and there is no match then
                         * do not mark the inst as exclude at this point as we need to filter by site
                         * to get PSEs from the site.
                         */
                        continue;
                }
                inst->exclude = 1;
                inst->exclusion_reason = excl_policy_mismatch;
                ZPN_DEBUG_BALANCE_STATE("%s %"PRId64" excluded - caller doesn't want policy non-matching private brokers",
                                        state->req->log_tag,
                                        inst->gid);
            }
        }
    }
    return res;
}

/*
 * Description:
 * Caller decides (actually it's the zpn_client_capability mapping maintained by zpn_lib.c)
 * whether it wants public, private or both instance types as redirect targets.
 * Filter out those instances that doesn't match client's requirements */
/* TODO: might want to use zpn_client_capability directly instead of having callers do it. */
static int filter_by_requested_instance_type(struct balance_request_state* state)
{
    struct zpn_balance_redirect_instance_types *rbt = state->req->rbt;

    for (int i = 0; i < state->viable_instance_count; i++) {
        struct viable_instance *inst = &state->viable_instances[i];
        if (inst->exclude) continue; /* someone else got to it first ! */

        switch (inst->personality) {
        case ZPN_INSTANCE_TYPE_PRIVATE_BROKER :
            /* a private broker is made public if:
            * 1. it's declared public, or
            * 2. it has "publish ips" that are not in the private ip blocks
            * Note that we are marking the instance here, which will be used later during "bucketing" */
            inst->is_public_private = (!strcmp(inst->is_public, "TRUE")) ||
                                        ((!strcmp(inst->is_public, "DEAFULT")) &&
                                            pub_ip_published(inst->publish_ips, inst->publish_ips_count));

            /* caller doesn't want to include this type of private broker */
            if (rbt && !rbt->type[inst->is_public_private ? PUBLIC_PRIVATE_BROKER : PRIVATE_BROKER]) {
                inst->exclude = 1;
                inst->exclusion_reason = excl_req_inst_type;
                ZPN_DEBUG_BALANCE_STATE("%s %"PRId64" excluded - caller doesn't want %s private brokers",
                                        state->req->log_tag,
                                        inst->gid,
                                        inst->is_public_private ? "public" : EMPTY_STR);
            }
            break;
        case ZPN_INSTANCE_TYPE_PUBLIC_BROKER:
        default: /* there could be more types in future */
            /* if caller doesn't want to include this type of broker, exclude it */
            if (rbt && !rbt->type[PUBLIC_BROKER]) {
                inst->exclude = 1;
                inst->exclusion_reason = excl_req_inst_type;
                ZPN_DEBUG_BALANCE_STATE("%s %"PRId64" excluded - caller doesn't want public brokers", state->req->log_tag, inst->gid);
            }
            break;
        }
    }
    return ZPN_RESULT_NO_ERROR;
}

static int
get_scope_by_id(struct balance_request_state *state, int64_t pbroker_gid, int64_t *scope_gid)
{
    struct zpn_private_broker *pbroker = NULL;

    int ret = zpn_private_broker_get_by_id_immediate(pbroker_gid, &pbroker);
    if (ZPATH_RESULT_NO_ERROR != ret) {
        ZPN_DEBUG_BALANCE_STATE("%s: Couldn't find pbroker table by pbroker_gid %"PRId64", wally returned (%s)\n", state->req->log_tag, pbroker_gid, zpath_result_string(ret));
        *scope_gid = 0;
        return ret;
    }

    *scope_gid = pbroker->scope_gid;
    return ZPN_RESULT_NO_ERROR;
}

/*
 * Function to filter out instances from 'viable instances' list based on the scope_gid.
 *
 * Requirement:
 *   1. ZApp user belonging to default scope should only be redirected to default scope PSE
 *   2. ZApp user belonging to specific scope(x) should only be redirected to default scope PSE or specific scope(x) PSE
 *
 * The logic is as follows:
 *   1. pBroker belongs to default scope. ==> Allowed (not filtered).
 *   2. pBroker belongs to specific scope, but (dta or state->req->scope is disabled) ==> Disable/Filter
 *   3. pBroker belongs to specific scope and dta is enabled and scope is enabled for both.
 *
 * Exemptions:
 *   1. We do not check if the client scope is enabled/disable here. It was already checked before we reach this point,
 *      and if the status changed mid-flight, no issue, since the selected instance will reject the connection later
 *      anyways
 */
int filter_by_scope(struct balance_request_state* state)
{
    int result;
    int is_inst_scope_enabled;
    int is_dta_enabled;


    for (int i = 0; i < state->viable_instance_count; i++) {
        struct viable_instance *inst = &state->viable_instances[i];
        int64_t inst_scope_gid = 0;

        is_inst_scope_enabled = 0;
        is_dta_enabled = 0;

        if((inst->personality == ZPN_INSTANCE_TYPE_PRIVATE_BROKER) && !inst->exclude) {

            /* Get the scope gid for the instance */
            result = get_scope_by_id(state, inst->gid, &inst_scope_gid);
            if(result != ZPN_RESULT_NO_ERROR) {
                inst->exclude = 1;
                inst->exclusion_reason = excl_scope_fetch;
                ZPN_BALANCE_LOG_STATE(AL_ERROR, "%s Fetching scope for instance %"PRId64" failed with errorCode(%d)\n",
                                    state->req->log_tag, inst->gid, result);
                continue;
            }

            /* If instance belongs to 'default' scope, do not filter */
            if(is_scope_default(inst_scope_gid)) {
                continue;
            }

            is_dta_enabled = zpn_get_delegated_admin_status(ZPATH_GID_GET_CUSTOMER_GID(inst->gid));
            if(!is_dta_enabled) {
                /* At this point dta is disabled, and instance does not belong to default scope, so filter it */
                inst->exclude = 1;
                inst->exclusion_reason = excl_scope_disabled;
                ZPN_BALANCE_LOG_STATE(AL_DEBUG, "%s DTA feature disabled and pbroker does not belong to default scope, excluding instance %"PRId64"\n",
                                            state->req->log_tag, inst->gid);
                continue;
            }

            /* Check the state of scope for the instance */
            result = zpn_is_scope_enabled(inst_scope_gid, &is_inst_scope_enabled);
            if(result != ZPN_RESULT_NO_ERROR) {
                ZPN_BALANCE_LOG_STATE(AL_ERROR, "%s Fetching scope status for scope %"PRId64" failed with errorCode(%d)\n",
                                    state->req->log_tag, inst_scope_gid, result);
                inst->exclude = 1;
                inst->exclusion_reason = excl_scope_fetch;
                continue;
            }

            /* If the instance scope is disabled, we filter out the instance */
            if(!is_inst_scope_enabled) {
                ZPN_BALANCE_LOG_STATE(AL_DEBUG, "%s Scope is disabled for scope %"PRId64"\n",
                                    state->req->log_tag, inst_scope_gid);
                inst->exclude = 1;
                inst->exclusion_reason = excl_scope_disabled;
                continue;
            }

            /* Check if client and instance belong to same scope */
            if(inst_scope_gid != state->req->scope_gid) {
                ZPN_BALANCE_LOG_STATE(AL_DEBUG, "%s Instance scope %"PRId64" and client scope %"PRId64" do not match, filtering the instance\n",
                                    state->req->log_tag, inst_scope_gid, state->req->scope_gid);
                inst->exclude = 1;
                inst->exclusion_reason = excl_scope_mismatch;
                continue;
            }
        }
    }

    /* We want to continue filtering */
    return ZPN_RESULT_NO_ERROR;
}

int filter_instance_by_site(struct balance_request_state* state)
{
    for (int i = 0; i < state->viable_instance_count; i++) {
        struct viable_instance *inst = &state->viable_instances[i];

        if (inst->personality != ZPN_INSTANCE_TYPE_PRIVATE_BROKER) {
            continue;
        }

        if(inst->site_gid != state->req->filter_by_site_gid) {
            ZPN_BALANCE_LOG_STATE(AL_DEBUG, "%s Instance site_gid %"PRId64" and requested site_gid %"PRId64" do not match, filtering the instance\n", state->req->log_tag, inst->site_gid, state->req->filter_by_site_gid);
            inst->exclude = 1;
            inst->exclusion_reason = excl_site_gid_mismatch;
            continue;
        }
    }

    return ZPN_RESULT_NO_ERROR;
}

static int filter_by_pse_group_exclusive_for_business_continuity_flag(struct balance_request_state* state)
{
    // If instance match was already found during redirect policy filter
    // evaluation then just return as the policy filter takes precedence
    // over the PSE group's business continuity exclusive flag
    if (state->policy_state && state->policy_state->inst_match_found) {
        return ZPN_RESULT_NO_ERROR;
    }

    for (int i = 0; i < state->viable_instance_count; ++i) {
        struct viable_instance *inst = &state->viable_instances[i];
        if (inst->exclude) continue; // continue as it is already excluded

        if (inst->personality == ZPN_INSTANCE_TYPE_PRIVATE_BROKER) {
            if (inst->pse_grp_exclusive_for_business_continuity) {
                ZPN_DEBUG_BALANCE_STATE("%"PRId64" excluded - the PSE group(gid %"PRId64") for the instance is marked exclusive for PCC",
                                        inst->gid, inst->pse_grp_gid);
                inst->exclude = 1;
                inst->exclusion_reason = excl_inst_exclusive_for_business_continuity;
            }
        }
    }
    return ZPN_RESULT_NO_ERROR;
}

int zpn_balance_inst_filter_execute(struct balance_request_state* state)
{
    int res;
    int req_public_broker = 1;
    if (!count_viable_instances_not_excluded(state))
        return ZPN_RESULT_NO_ERROR;

    if (state->req->rbt != NULL && state->req->rbt->type[PUBLIC_BROKER] == 0) {
        req_public_broker = 0;
    }

    res = filter_by_exclude_self(state);
    if (res)    return res;

    if (req_public_broker) {
        res = filter_by_redirect_policy(state);
        if (res) return res;

        res = filter_by_pse_group_exclusive_for_business_continuity_flag(state);
        if (res) return res;
    }

    res = filter_by_requested_instance_type(state);
    if (res) return res;

    res = filter_by_scope(state);
    if(res) return res;

    res = filter_by_client_type_and_instance_role(state);
    if (res) return res;

    res = filter_by_client_ipv6_strict_preference(state);
    if (res) return res;

    res = filter_by_instance_balance_role_mode(state, req_public_broker);
    if (res) return res;

    res = filter_by_instance_status(state);
    if (res) return res;

    res = filter_by_report_time_exclusion(state);
    if (res) return res;

    res = filter_by_report_time_median(state);
    if (res) return res;

    res = filter_by_report_time_staleness(state);
    if (res) return res;

    res = filter_by_alt_cloud(state);
    if (res) return res;

    if (!req_public_broker) {
        res = filter_by_redirect_policy(state);
        if (res) return res;
    }

    if (state->req->filter_by_site_gid && is_redirect_policy_get_pse_based_on_site(state)) {
        res = filter_instance_by_site(state);
        if (res) return res;
    }
    return ZPN_RESULT_NO_ERROR;
}

void zpn_balance_inst_filter_free(struct balance_request_state* state)
{
    // no-op
}
