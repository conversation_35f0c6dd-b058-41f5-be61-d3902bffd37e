
/*
 * zpn_balance_inst_load.c. Copyright (C) 2022 Zscaler, Inc. All Rights Reserved.
 */
#include <string.h>
#include "zpn/zbalance/zpn_balance_private.h"
#include "zpn/zpn_broker_assert.h"
#include "zpn/zpn_pbroker_group.h"

#ifdef Z<PERSON>NCE_TESTING
#include "test_misc/testing_macros.h"
#else
#include "test_misc/production_macros.h"
#endif

int64_t g_mem_load_pct_lo_wm = BROKER_MEM_LOAD_PCT_LO_WM_DFLT;
int64_t g_mem_load_pct_hi_wm = BROKER_MEM_LOAD_PCT_HI_WM_DFLT;

int64_t g_proc_fd_util_load_pct_hi_wm = BROKER_PROC_FD_UTIL_LOAD_PCT_HI_WM_DFLT;

#define MAX_PBGROUP_TO_MTN_MAPPING_COUNT 1000

#define ZDP(format...) zpath_debug_cb_printf_response(request_state, ##format)

// Table (gid => reported_instance) of reported brokers.
static struct {
    zpath_rwlock_t lock;
    struct zhash_table *table;
    int initialized;
} global_state;

#define GLOBAL_INITIALIZED (global_state.initialized)
#define GLOBAL_RD_LOCK  ZPATH_RWLOCK_RDLOCK(&global_state.lock, __FILE__, __LINE__)
#define GLOBAL_WR_LOCK  ZPATH_RWLOCK_WRLOCK(&global_state.lock, __FILE__, __LINE__)
#define GLOBAL_UNLOCK   ZPATH_RWLOCK_UNLOCK(&global_state.lock, __FILE__, __LINE__)

// Local override of instance attributes (via debug commands)
#define INSTANCE_LOCAL_OVERRIDE_TAG_LEN (127)
#define INSTANCE_LOCAL_OVERRIDE_ROLE_MODE_LEN (31)
#define INSTANCE_LOCAL_OVERRIDE_ALT_CLOUD_LEN    ZPN_MAX_ALT_CLOUD_NAME_LEN

struct instance_local_override {
    int64_t gid;
    struct site site;
    uint32_t cpu_pct;
    uint32_t mem_pct;
    uint32_t proc_fd_util_pct;
    int use_client_count;
    int is_dualstack;
    uint64_t client_count;
    char tag[INSTANCE_LOCAL_OVERRIDE_TAG_LEN + 1];
    char role[INSTANCE_LOCAL_OVERRIDE_ROLE_MODE_LEN + 1];
    char mode[INSTANCE_LOCAL_OVERRIDE_ROLE_MODE_LEN + 1];
    char alt_cloud[INSTANCE_LOCAL_OVERRIDE_ALT_CLOUD_LEN + 1];
};
static struct {
    zpath_rwlock_t lock;
    struct zhash_table *table; // holds instance_local_override
    int initialized;
} g_local_override;

static void copy_local_override_to_viable_inst(struct viable_instance *viable);
static int debug_instance_show(struct zpath_debug_state *request_state, const char **query_values, int query_value_count, void *object);
static int debug_instance_local_show(struct zpath_debug_state *request_state, const char **query_values, int query_value_count, void *object);
static int debug_instance_local_generate(struct zpath_debug_state *request_state, const char **query_values, int query_value_count, void *object);
static int debug_instance_local_add(struct zpath_debug_state *request_state, const char **query_values, int query_value_count, void *object);
static int debug_instance_local_remove(struct zpath_debug_state *request_state, const char **query_values, int query_value_count, void *object);
static int debug_instance_local_clear(struct zpath_debug_state *request_state, const char **query_values, int query_value_count, void *object);
static int zpn_balance_inst_local_add(int64_t gid, uint32_t cpu_util, uint32_t mem_util, uint32_t proc_fd_util, double lat, double lon, const char *cc, const char* tag, const char* role, const char* mode, const char *alt_cloud, int use_client_count, uint64_t client_count, int is_dualstack);
static int zpn_balance_inst_local_remove(int64_t gid);
static int zpn_balance_inst_local_clear();

#define LOCAL_OVERRIDE_INITIALIZED (g_local_override.initialized)
#define LOCAL_OVERRIDE_RD_LOCK  ZPATH_RWLOCK_RDLOCK(&g_local_override.lock, __FILE__, __LINE__)
#define LOCAL_OVERRIDE_WR_LOCK  ZPATH_RWLOCK_WRLOCK(&g_local_override.lock, __FILE__, __LINE__)
#define LOCAL_OVERRIDE_UNLOCK   ZPATH_RWLOCK_UNLOCK(&g_local_override.lock, __FILE__, __LINE__)

int zpn_balance_inst_load_init()
{
    ZPN_STANDARD_COMPONENT_ONETIME_INIT_CHECK;

    /* in case some test code didn't initialize zpath_instance,default to ZPATH_GLOBAL_CONFIG_OVERRIDE_GID (i.e. 1) */
    int64_t inst_gid = ZPATH_INSTANCE_GID ? ZPATH_INSTANCE_GID : ZPATH_GLOBAL_CONFIG_OVERRIDE_GID;
    int64_t cust_gid = ZPATH_GID_GET_CUSTOMER_GID(inst_gid);

    global_state.lock = ZPATH_RWLOCK_INIT;

    /* This init function is called only once when the broker starts and the mutex itself
     * is getting initialized in this function. So we don't really need to lock the
     * mutex here. Jira-id for the coverity issue: ET-85468
     */
    /* coverity[missing_lock: FALSE] */
    global_state.table = zhash_table_alloc(&zpn_balance_allocator);

    g_local_override.lock = ZPATH_RWLOCK_INIT;
    g_local_override.table = zhash_table_alloc(&zpn_balance_allocator);
    g_local_override.initialized = 1;

    zpath_config_override_monitor_int(CONFIG_FEATURE_BROKER_MEM_LOAD_PCT_LO_WM,
                                &g_mem_load_pct_lo_wm,
                                NULL,
                                (int64_t)BROKER_MEM_LOAD_PCT_LO_WM_DFLT,
                                (int64_t)inst_gid,
                                (int64_t)cust_gid,
                                (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                (int64_t)0);

    zpath_config_override_monitor_int(CONFIG_FEATURE_BROKER_MEM_LOAD_PCT_HI_WM,
                                &g_mem_load_pct_hi_wm,
                                NULL,
                                (int64_t)BROKER_MEM_LOAD_PCT_HI_WM_DFLT,
                                (int64_t)inst_gid,
                                (int64_t)cust_gid,
                                (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                (int64_t)0);

    /* overrides for max fd util percentage */
    zpath_config_override_monitor_int(CONFIG_FEATURE_BROKER_PROC_FD_UTIL_LOAD_PCT_HI_WM,
                                &g_proc_fd_util_load_pct_hi_wm,
                                NULL,
                                (int64_t)BROKER_PROC_FD_UTIL_LOAD_PCT_HI_WM_DFLT,
                                (int64_t)inst_gid,
                                (int64_t)cust_gid,
                                (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                (int64_t)0);

    global_state.initialized = 1;

    (void)zpath_debug_add_safe_read_command("show reported instances",
                "/balance/instance",
                debug_instance_show,
                NULL,
                "gid",                "Instance GID, optional",
                "customer_gid",       "Customer GID, optional",
                "name",               "Name of a public broker (can be partial, but case sensitive), optional",
                NULL);

    (void)zpath_debug_add_safe_read_command("show locally modified instance's parameters",
                "/balance/instance/local",
                debug_instance_local_show,
                NULL,
                "gid",                "Instance GID. Optional. List all if unspecified.",
                NULL);

    (void)zpath_debug_add_write_command("show locally modified instance's parameters",
                "/balance/instance/local/generate",
                debug_instance_local_generate,
                NULL,
                "dcs",                "Number of dcs, required, range [1, 100]",
                NULL);

    (void)zpath_debug_add_write_command("locally modify instance's parameters",
                "/balance/instance/local/add",
                debug_instance_local_add,
                NULL,
                "gid",                "Instance GID, required",
                "cpu",                "CPU percentage, required, range [0, 100]",
                "mem",                "Memory percentage, required, range [0, 100]",
                "lat",                "latitude, optional, range [-90.00, 90.00]",
                "lon",                "longitude, optional, range [-180.00, 180.00]",
                "cc",                 "country code, optional",
                "tag",                "something you can remember about this entry, optional",
                "role",               "balance role, optional",
                "mode",               "balance mode, optional",
                "alt_cloud",          "alternate cloud name",
                "fd",                 "proc fd_util percentage[0,100]",
                "use_clients",        "1 to enable client override, 0 to disable",
                "clients",            "the number of clients on that SE",
                "dualstack",          "1 to indicate this is dual-stacked instance",
                NULL);

    (void)zpath_debug_add_write_command("remove locally modified instance",
                "/balance/instance/local/remove",
                debug_instance_local_remove,
                NULL,
                "gid",                "Instance GID, required",
                NULL);

    (void)zpath_debug_add_write_command("remove all locally modified instances",
                "/balance/instance/local/clear",
                debug_instance_local_clear,
                NULL,
                NULL);

    return ZPN_RESULT_NO_ERROR;
}

static __inline__ void str_compare_and_dup(char** dest, char* src) {
    if (!src) {
        ZPN_BALANCE_FREE(*dest); // does null check internally
        *dest = NULL;
        return;
    }
    if (*dest && !strcmp(*dest, src)) { // equal - nothing to do
        return;
    }
    ZPN_BALANCE_FREE(*dest);
    *dest = ZPN_BALANCE_STRDUP(src, strlen(src));
}

/************************************************************
 * A public broker updating it's row in zpn_broker_load table
 */
int zpn_balance_inst_load_update_broker(int64_t gid,
                                        int8_t deleted,
                                        char *name,
                                        uint32_t cpu_util,
                                        uint32_t mem_util,
                                        uint64_t clients,
                                        uint32_t load_skew,
                                        char *balance_role,
                                        char *balance_mode,
                                        int32_t modified_time,
                                        uint16_t status,
                                        uint16_t proc_fd_util)
{
    int res = ZPN_RESULT_NO_ERROR;
    int new_insert = 0;

    ZPN_DEBUG_BALANCE("Update load: gid = %" PRId64 ", name = %s, cpu = %u, mem = %u, skew = %u, "
                      "balance_role = %s, balance_mode = %s status = %s fd = %u",
                      gid, name, cpu_util, mem_util, load_skew,
                      balance_role ? balance_role : BALANCE_ROLE_UNCONFIGURED,
                      balance_mode ? balance_mode : BALANCE_MODE_UNCONFIGURED,
                      zpn_broker_get_status_string(status),
                      proc_fd_util);
    if (!name) {
        ZPN_LOG(AL_ERROR, "No name; gid = %ld", (long) gid);
        return ZPN_RESULT_BAD_ARGUMENT;
    }

    GLOBAL_WR_LOCK;

    struct zpath_instance *instance = zpath_instance_get_no_locking(gid);
    if (!instance) {
        // Remove the broker if it's not found in the zpath_instance.
        ZPN_LOG(AL_WARNING, "Failed to get zpath_instance for gid = %" PRId64, gid);
        res = ZPN_RESULT_NOT_FOUND;
        goto done;
    }

    if (!instance->run || !instance->active) {
        /* Instance is not running/active... Should remove it and free
         * from table if it is there... */
        /* FIXME: Implement Me */
        ZPN_LOG(AL_WARNING, "%ld: Instance not running or active", (long) gid);
    }

    struct reported_instance *brk = zhash_table_lookup(global_state.table, &gid, sizeof(gid), NULL);
    if (!brk) {
        brk = ZPN_BALANCE_CALLOC(sizeof(struct reported_instance));
        /* Store at the end so that we have all our data correct before storing */
        /* TBH it's not necessary since we are doing it under lock */
        new_insert = 1;
    }

    brk->deleted = deleted;

    str_compare_and_dup(&brk->name, name);
    str_compare_and_dup(&brk->instance_role, instance->role);
    str_compare_and_dup(&brk->balance_role, balance_role);
    str_compare_and_dup(&brk->balance_mode, balance_mode);

    snprintf(brk->is_public, sizeof(brk->is_public), "%s", "TRUE");
    brk->personality = ZPN_INSTANCE_TYPE_PUBLIC_BROKER;

    brk->cpu_pct = cpu_util;
    brk->mem_pct = mem_util;
    brk->clients = clients;
    brk->load_skew = load_skew;
    brk->modified_time_s = modified_time;
    brk->brk_status = status;
    brk->proc_fd_util = proc_fd_util;

    brk->site.lat = instance->latitude;
    brk->site.lon = instance->longitude;
    brk->gid = gid;
    char *cc = instance->country_code ? instance->country_code : "";
    snprintf(brk->site.cc, sizeof(brk->site.cc), "%s", cc);

    brk->is_dualstack = instance->dual_stack;

    if (new_insert) {
        zhash_table_store(global_state.table, &gid, sizeof(gid), 0, brk);
    }
done:
    GLOBAL_UNLOCK;
    return res;
}



/***********************************************************************************************
 * Update private broker table.
 */
int zpn_balance_inst_load_update_pbroker(int64_t gid,
                                    int8_t deleted,
                                    char **publish_ips,
                                    int publish_ips_count,
                                    uint32_t cpu_util,
                                    uint32_t mem_util,
                                    uint64_t clients,
                                    int32_t modified_time)
{
    int res = ZPN_RESULT_NO_ERROR;
    int new_insert = 0;
    struct zpn_private_broker *pb = NULL;

    ZPN_DEBUG_BALANCE("Update load: gid = %" PRId64 ", cpu = %u, mem = %u, role = %s",
                       gid, cpu_util, mem_util, publish_ips_count ? BALANCE_ROLE_FORWARD : BALANCE_ROLE_UNCONFIGURED);

    GLOBAL_WR_LOCK;

    /* Filter out deleted Private broker instance. No need to add them in inst_table.
     * Note: Calling the "no_lock" version because this function is a wally row callback -
     * inadvertently locking wally can lead to undefined behavior (ET-36558).
     * Note: This also depends on private broker shard tables are (fully) loaded outside
     * as we are no doing registration here.
     */
    res = zpn_private_broker_get_by_id_no_lock(gid, &pb);
    if (res == ZPN_RESULT_NOT_FOUND) {
        ZPN_DEBUG_BALANCE("Private broker is not available, gid = %" PRId64, gid);
        goto done;
    }

    struct reported_instance *pbrk = zhash_table_lookup(global_state.table, &gid, sizeof(gid), NULL);
    if (!pbrk) {
        pbrk = ZPN_BALANCE_CALLOC(sizeof(*pbrk));
        pbrk->personality = ZPN_INSTANCE_TYPE_PRIVATE_BROKER;
        pbrk->gid = gid;

        new_insert = 1;
    }

    pbrk->deleted = deleted;

    if (pbrk->publish_ips_count != publish_ips_count) {
        if (pbrk->publish_ips_count) {
            for (int i = 0; i < pbrk->publish_ips_count; i++) {
                ZPN_BALANCE_FREE(pbrk->publish_ips[i]);
            }
            ZPN_BALANCE_FREE(pbrk->publish_ips);
            pbrk->publish_ips = NULL;
            pbrk->publish_ips_count = 0;
        }
        if (publish_ips_count) {
            pbrk->publish_ips= ZPN_BALANCE_CALLOC(sizeof(pbrk->publish_ips[0]) * publish_ips_count);
            for (int i = 0; i < publish_ips_count; i++) {
                pbrk->publish_ips[i] = ZPN_BALANCE_STRDUP(publish_ips[i], strlen(publish_ips[i]));
            }
            pbrk->publish_ips_count = publish_ips_count;
        }
    } else {
        for (int i = 0; i < publish_ips_count; i++) {
            if (strcmp(pbrk->publish_ips[i], publish_ips[i]) != 0) {
                ZPN_BALANCE_FREE(pbrk->publish_ips[i]);
                pbrk->publish_ips[i] = ZPN_BALANCE_STRDUP(publish_ips[i], strlen(publish_ips[i]));
            }
        }
    }

    pbrk->cpu_pct = cpu_util;
    pbrk->mem_pct = mem_util;
    pbrk->load_skew = 0; // not set, only public instances have this value, configured by OPS.
    pbrk->modified_time_s = modified_time;
    pbrk->clients = clients;

    if (pbrk->publish_ips_count) {
        pbrk->balance_role = BALANCE_ROLE_FORWARD;
        pbrk->balance_mode = BALANCE_MODE_BALANCE;
    } else {
        pbrk->balance_role = BALANCE_ROLE_UNCONFIGURED;
        pbrk->balance_mode = BALANCE_MODE_UNCONFIGURED;
    }

    if(new_insert) {
        zhash_table_store(global_state.table, &gid, sizeof(gid), 0, pbrk);
    }
done:
    GLOBAL_UNLOCK;

    return ZPN_RESULT_NO_ERROR;
}

static void grow_viable_instances_if_needed(struct balance_request_state* state)
{
    if (!state->viable_instances) {
        state->viable_instance_capacity = g_balance_config.inst_list_alloc;
        state->viable_instances = ZPN_BALANCE_CALLOC(sizeof(struct viable_instance) * state->viable_instance_capacity);
        state->viable_instance_count = 0;
    } else if (state->viable_instance_count == state->viable_instance_capacity) {
        state->viable_instance_capacity += g_balance_config.inst_list_growth;
        state->viable_instances = ZPN_BALANCE_REALLOC(state->viable_instances, sizeof(struct viable_instance) * state->viable_instance_capacity);
        /*
         * ET-44903: zero out the new memory for a clear-realloc behavior.
         * Code elsewhere assumes the viable instance structure is initially cleared and only sets non-null values.
         */
        memset(&state->viable_instances[state->viable_instance_count], 0,
                sizeof(struct viable_instance) * (state->viable_instance_capacity - state->viable_instance_count));
        ZPN_DEBUG_BALANCE_STATE("%s reallocate of viable instance list %zu --> %zu",
            state->req->log_tag, state->viable_instance_count, state->viable_instance_capacity);
    }
}


/* When computing effective proc_fd_util load:
 *  -- If proc_fd_util load is lower than low-watermark, treat proc_fd_util load as 0.
 *  -- If proc_fd_util load is higher than high-watermark, treat proc_fd_util load as 100.
 *  -- If proc_fd_util load is lesser, calculate as a percentage of high watermark.
 */
 static uint32_t compute_effective_proc_fd_util_load(uint32_t reported_proc_fd_util)
{
    uint32_t proc_fd_util_hi_wm = (uint32_t)g_proc_fd_util_load_pct_hi_wm;

    if ((proc_fd_util_hi_wm < BROKER_PROC_FD_UTIL_LOAD_PCT_HI_WM_MIN) ||
        (proc_fd_util_hi_wm > BROKER_PROC_FD_UTIL_LOAD_PCT_HI_WM_MAX)) {
        proc_fd_util_hi_wm = BROKER_PROC_FD_UTIL_LOAD_PCT_HI_WM_DFLT;
        ZPN_LOG(AL_WARNING, "Incorrect proc_fd_util load configuration; hi_wm = %u; using default", proc_fd_util_hi_wm);
    }

    if (reported_proc_fd_util >= proc_fd_util_hi_wm) {
        return 100;
    }

    /* return as a percentage of high watermark */
    return ((reported_proc_fd_util * 100 / proc_fd_util_hi_wm));
}

/* When computing effective memory load:
 *  -- If memory load is lower than low-watermark, treat memory load as 0.
 *  -- If memory load is higher than high-water, treat memory load as 100.
 *  -- If memory load is in-between, extrapolate between 0-100.
 */
 static uint32_t compute_effective_mem_load(uint32_t reported_memory)
{
    uint32_t mem_lo_wm = (uint32_t)g_mem_load_pct_lo_wm;
    uint32_t mem_hi_wm = (uint32_t)g_mem_load_pct_hi_wm;
    if (mem_lo_wm > BROKER_MEM_LOAD_PCT_LO_WM_MAX || mem_hi_wm > 100 || mem_lo_wm >= mem_hi_wm) {
        /* revert to default */
        ZPN_LOG(AL_WARNING, "Incorrect memory load configuration lo_wm = %u, hi_wm = %u.", mem_lo_wm, mem_hi_wm);
        mem_lo_wm = BROKER_MEM_LOAD_PCT_LO_WM_DFLT;
        mem_hi_wm = BROKER_MEM_LOAD_PCT_HI_WM_DFLT;
    }
    if (reported_memory <= mem_lo_wm) return 0;
    if (reported_memory >= mem_hi_wm) return 100;
    return (((reported_memory - mem_lo_wm) * 100) / (mem_hi_wm - mem_lo_wm));
}

 /*
  * Returns a prediction of how many more clients we believe we can accept on this broker
  */
STATIC_INLINE uint64_t zpn_balance_inst_load_get_client_memory_usage_load(uint64_t num_clients, uint64_t reported_load, struct balance_request_configuration *cfg) {
    if (!cfg) {
        ZPN_LOG(AL_CRITICAL, "No CFG provided for client load estimation... cannot safely proceed so giving up as if disabled");
        return 0;
    }

    // if the configuration gets broken somehow we should avoid div by 0 issues... guarantee we are
    // at least 1 even in disastrous circumstances - this shouldn't happen but I would rather be safe
    int64_t load_threshold = max(1, cfg->client_counts.min_load_threshold);

    if (num_clients == 0) {
        // no clients means we have no 'max' clients we could reasonably predict so we give up
        zpn_balance_client_load_update_no_reported_clients_count();
        return 0;
    } else if (reported_load == 0) {
        // Reported load as 0 would result in a div by 0 and offers no real metrics data so we give up
        zpn_balance_client_load_update_no_reported_clients_load();
        return 0;
    } else if (num_clients < cfg->client_counts.min_clients_threshold && reported_load < load_threshold) {
        //if both metrics are under threshold we will give up
        zpn_balance_client_load_update_client_metrics_all_under_threshold();
        return 0;
    }

    if (reported_load >= 100) {
        ZPN_LOG(AL_NOTICE, "Received a load of %"PRId64" exceeding 100", reported_load);
        zpn_balance_client_load_update_reported_load_exceeded_100();
        return num_clients; //If we are at or over 100, then necessarily the max clients is the current num clients!
    }

    zpn_balance_client_load_update_successfully_calculated_max_clients();
    /*
     * calculate the number of clients that the broker has capacity for
     * using the % memory used and the number of clients on the broker
     * This covers weird cases in heterogeneous data centers where broker load may
     * look equal (or even better) but not actually be better.
     * Imagine the case where you have 2 brokers
     * Broker A has 9000 Clients at 90% mem utilization
     * Broker B has 5 clients at 80% mem utilization
     * Naively Broker B has fewer clients and its mem utilization is lower, so we should prefer it
     * However, if we calculate their client capacity we see:
     * Broker A has room for 1000 more clients
     * Broker B has room for 1 more client
     *
     * Formula derivation:
     * 1. get the number of clients/memory unit (in our case % of memory used
     *    ⎟ current clients  ⎟    clients
     *    ⎜──────────────────⎟ = ─────────
     *    ⎣current memory use⎦   1% memory
     *
     * 2. Calculate max clients if we were to assume 100% load
     *    Note: we could lower this to something like 90/80/etc but since this
     *          is a relative comparison looking ultimately at the magnitudes of the values
     *          this number works as well as any other number. If we wanted to change max clients
     *          by som threshold that could be done (say set 1 broker to only use 90% of max clients)
     *          by replacing 100 with that brokers load. Currently, based on the simulations I don't think
     *          that gives us very much extra use.
     *    100 ⋅ clients
     *    ───────────── = max clients
     *      1% memory
     * 3. Finally, the caller can figure out the clients available
     *    max clients - current clients = clients available
     *
     * 4. We can do that as one step as outlined below
     *
     * Notes: obviously reported_load must always be >0 - this is guaranteed by our threshold check having a min of 0
     *        Also we need the reported_load < 100 so that we don't accidentally roll over the calculations
     *        100
     *     ───────────── > 1 or else we will roll over the unsigned value
     *     reported_load
     */
    return 100 * num_clients / reported_load;
 }

/*
 * zpn_balance_compute_instance_effective_load
 *  Given memory, cpu, fd and skew parameters, compute instance effective load
 */
uint32_t zpn_balance_compute_instance_effective_load(uint32_t cpu_pct, uint32_t mem_pct,
                                                     uint32_t proc_fd_util_pct, uint32_t load_skew)
{
    uint32_t mem_pct_new = compute_effective_mem_load(mem_pct);
    uint32_t proc_fd_util_pct_new = compute_effective_proc_fd_util_load(proc_fd_util_pct);

    /* find max of the three factors */
    uint32_t effective_load;
    effective_load = max(max(mem_pct_new, proc_fd_util_pct_new), cpu_pct);

    if (load_skew) {
        effective_load = effective_load * load_skew / 100;
    }
    effective_load = effective_load > BROKER_LOAD_CEILING ? BROKER_LOAD_CEILING : effective_load;
    return effective_load;
}

static void compute_effective_load(struct viable_instance *viable, struct balance_request_configuration *cfg)
{
    viable->effective_load = zpn_balance_compute_instance_effective_load(viable->cpu_pct, viable->mem_pct, viable->proc_fd_util_pct, viable->load_skew);

    if (zpn_balance_is_client_estimation_enabled_for_instance(cfg, viable->personality)) {
        viable->clients_available = 0;
        viable->adjusted_clients_available = 0;
        uint32_t effective_client_load = max(viable->mem_pct, viable->effective_load);
        viable->clients_max = zpn_balance_inst_load_get_client_memory_usage_load(viable->clients,
                                                                                 // Take the largest metric - either effective_load or the raw reported mem
                                                                                 // this enables other metrics to potentially influence the number of clients
                                                                                 // if they are peaked. But also prevents us from thinking there is 0 load when things are low
                                                                                 // This gives the best possibility of trying to calculate client rates
                                                                                 effective_client_load,
                                                                                 cfg);
        if (viable->clients < cfg->client_counts.min_clients_threshold &&
            effective_client_load > cfg->client_counts.min_load_threshold) {
            // We are in an outlier mode on this 'viable instance' where
            // We are using more than our memory threshold but our client rate is very low
            // We will mark this so that later steps can choose to special case this behavior.
            // The result in running in such a state is our max client/client capacity will be abnormally small
            // This is *correct* because a run away broker should be de-prioritized
            viable->clients_hot = 1;
            zpn_balance_client_load_update_clients_hot_count();
        }
        if (viable->clients_max && viable->clients_max >= viable->clients) {
            viable->clients_available = viable->clients_max - viable->clients;
        }
    } else {
        viable->clients_max = 0;
        viable->clients_available = 0;
        viable->adjusted_clients_available = 0;
    }
}

static void copy_reported_inst_to_viable_inst(struct viable_instance *viable, const struct reported_instance *reported, struct balance_request_configuration *cfg) {
    viable->gid = reported->gid;
    if (reported->name)           viable->name          = ZPN_BALANCE_STRDUP(reported->name, strlen(reported->name));
    if (reported->instance_role)  viable->instance_role = ZPN_BALANCE_STRDUP(reported->instance_role, strlen(reported->instance_role));
    if (reported->balance_role)   viable->balance_role  = ZPN_BALANCE_STRDUP(reported->balance_role, strlen(reported->balance_role));
    if (reported->balance_mode)   viable->balance_mode  = ZPN_BALANCE_STRDUP(reported->balance_mode, strlen(reported->balance_mode));
    viable->publish_ips_count = reported->publish_ips_count;
    if (reported->publish_ips_count)
        viable->publish_ips = ZPN_BALANCE_CALLOC(sizeof(char *) * reported->publish_ips_count);

    for (int i = 0; i < reported->publish_ips_count; i++)
        viable->publish_ips[i] = ZPN_BALANCE_STRDUP(reported->publish_ips[i], strlen(reported->publish_ips[i]));

    viable->cpu_pct = reported->cpu_pct;
    viable->mem_pct = reported->mem_pct;
    viable->clients = reported->clients;
    viable->load_skew = reported->load_skew;
    viable->personality = reported->personality;
    viable->modified_time_s = reported->modified_time_s;
    viable->brk_status = reported->brk_status;
    viable->proc_fd_util_pct = reported->proc_fd_util;
    viable->is_dualstack = reported->is_dualstack;

    copy_local_override_to_viable_inst(viable);

    compute_effective_load(viable, cfg);
}

/* checking whether a private broker's should be considered an initial "viable instance" for a given client request:
 * - the broker should belong to the customer in question
 * - the broker should be part of a pb group
 * - that pb group is enabled
 * - that pb itself is enabled
 *
 * If everything checks out:
 * - ZPN_RESULT_NO_ERROR is returned
 * - pb group info is returned (output param)
 */
static int validate_reported_pbroker(struct balance_request_state* state,
                                     struct reported_instance *reported,
                                     struct zpn_private_broker_group **grp_out)
{
    int res = ZPN_RESULT_NO_ERROR;
    struct zpn_private_broker_to_group *pb_to_grp;
    struct zpn_private_broker_group *grp;
    struct zpn_private_broker *pb = NULL;

    if (state->req->customer_gid != ZPATH_GID_GET_CUSTOMER_GID(reported->gid))
        return ZPN_RESULT_ERR;

    res = zpn_pbroker_to_group_get_by_private_broker_gid(reported->gid, &pb_to_grp, 0, NULL, NULL, 0);
    if (res) {
        ZPN_BALANCE_LOG_STATE(AL_ERROR, "%s Could not get private broker to group for gid = %"PRId64": %s",
                                    state->req->log_tag, reported->gid, zpn_result_string(res));
        return ZPN_RESULT_ERR;
    }
    res = zpn_pbroker_group_get_by_gid(pb_to_grp->private_broker_group_gid, &grp, 0, NULL, NULL, 0);
    if (res) {
        ZPN_BALANCE_LOG_STATE(AL_ERROR, "%s Could not get private broker %"PRId64" group info from group gid = %"PRId64": %s",
                                    state->req->log_tag, reported->gid, pb_to_grp->private_broker_group_gid, zpn_result_string(res));
        return ZPN_RESULT_ERR;
    }
    if (!grp->enabled) {
        ZPN_DEBUG_BALANCE_STATE("%s Private broker %"PRId64") group %"PRId64" is not enabled",
                                    state->req->log_tag, reported->gid, pb_to_grp->private_broker_group_gid);
        return ZPN_RESULT_ERR;
    }

    res = zpn_private_broker_get_by_id(reported->gid, &pb, NULL, NULL, 0);
    /* in case of async but resulting a value - operate on stale info (better than making a random yes decision) */
    if ((res == WALLY_RESULT_NO_ERROR || res == WALLY_RESULT_ASYNCHRONOUS) && pb && !pb->enabled) {
        ZPN_DEBUG_BALANCE_STATE("%s Private broker is disabled, gid = %"PRId64", lookup res = %s",
                                    state->req->log_tag, reported->gid, zpath_result_string(res));
        return ZPN_RESULT_ERR;
    }

    if (grp_out) {
        *grp_out = grp;
    }
    return ZPN_RESULT_NO_ERROR;
}

static int get_viable_instances_tbl_walk_cb(void *cookie, void *object, void *key, size_t key_len)
{
    int res;
    struct balance_request_state* state = cookie;
    struct zpn_private_broker_group *grp_info;
    struct reported_instance *reported = object;
    struct viable_instance *viable = NULL;
    char *alt_cloud = NULL;
    int is_pse_grp_grace_dist_configured = 0;

    grow_viable_instances_if_needed(state);
    viable = &state->viable_instances[state->viable_instance_count];

    switch (reported->personality) {
    case ZPN_INSTANCE_TYPE_PUBLIC_BROKER:
        /* if constellation_gid_count > 0, public instances not included in instance_gids are NOT considered
            * since they are not in customer's constellations.*/
        if (state->constellation_gid_count) {
            goto done;
        }
        snprintf(viable->is_public, sizeof(viable->is_public), "%s", reported->is_public);
        viable->site = reported->site;
        alt_cloud = zpath_instance_get_alt_cloud(reported->gid);
        if (alt_cloud && alt_cloud[0]) {
            viable->alt_cloud = ZPN_BALANCE_STRDUP(alt_cloud, strlen(alt_cloud));
        }
        break;

    case ZPN_INSTANCE_TYPE_PRIVATE_BROKER:
        res = validate_reported_pbroker(state, reported, &grp_info);
        if (res) goto done; // not suitable

        /* copy out some private broker specific stuff
         * - is_pubic is a pb group level config.
         * - geo info is a pb group level config.
         * - trusted network is a pb group level config.
         */
        snprintf(viable->is_public, sizeof(viable->is_public), "%s", grp_info->is_public ? grp_info->is_public : "DEFAULT");

        viable->site.lat = grp_info->latitude;
        viable->site.lon = grp_info->longitude;
        viable->site_gid = grp_info->site_gid;
        char *cc = grp_info->country_code ? grp_info->country_code : "";
        snprintf(viable->site.cc, sizeof(viable->site.cc), "%s", cc);

        viable->pse_grp_gid = grp_info->gid;
        // The following function is used to look if the business-continuity
        // exclusive flag is set from the debug command, if it is present then
        // set the business-continuity exclusive flag to TRUE
        if (zpn_pbroker_pse_grp_exclusive_for_business_continuity_flag_is_set(viable->pse_grp_gid)) {
            viable->pse_grp_exclusive_for_business_continuity = 1;
        } else {
            viable->pse_grp_exclusive_for_business_continuity = grp_info->exclusive_for_business_continuity;
        }
        if (state->req->flag.feature_grace_distance && grp_info->grace_distance_enabled) {
            /* Grace distance feature is enabled*/
            viable->grace_distance = grp_info->calc_grace_distance_value_miles ? grp_info->calc_grace_distance_value_miles : 0;
            if (viable->grace_distance) {
                /* Grace distance feature is enabled and also configured by the customer */
                is_pse_grp_grace_dist_configured = 1;
            }
        } else {
            viable->grace_distance = 0;
        }
        viable->pse_inst_dist = 0;

        if (grp_info->alt_cloud && grp_info->alt_cloud[0]) {
            viable->alt_cloud = ZPN_BALANCE_STRDUP(grp_info->alt_cloud, strlen(grp_info->alt_cloud));
        }

        struct zpn_privatebrokergroup_trustednetwork_mapping *mapping[MAX_PBGROUP_TO_MTN_MAPPING_COUNT];
        size_t mapping_count = MAX_PBGROUP_TO_MTN_MAPPING_COUNT;
        res = zpn_privatebrokergroup_trustednetwork_mapping_get_by_private_brokergroup_gid(grp_info->gid,
                                                                                        mapping, &mapping_count, 0, NULL, NULL, 0);
        if (res != ZPATH_RESULT_NO_ERROR) {
            ZPN_DEBUG_BALANCE_STATE("%s Could not get private broker group to trusted networks map for gid = %"PRId64": %s",
                                        state->req->log_tag, grp_info->gid, zpn_result_string(res));
            mapping_count = 0;
        }
        viable->trusted_networks_count = mapping_count;
        if (mapping_count) {
            viable->trusted_networks_gids = ZPN_BALANCE_CALLOC(sizeof(int64_t) * mapping_count);
        }
        for (size_t i = 0; i < mapping_count; i++) {
            viable->trusted_networks_gids[i] = mapping[i]->trusted_network_gid;
        }
        break;

    default:
        ZPN_BROKER_ASSERT_HARD(0, "Unknown reported->personality!"); // remind us when new component types are introduced...
        break;
    }
    copy_reported_inst_to_viable_inst(viable, reported, &(state->configuration));
    if (!state->is_pse_group_grace_dist_configured) {
        state->is_pse_group_grace_dist_configured = is_pse_grp_grace_dist_configured;
    }
    state->viable_instance_count++;

 done:
    return ZPN_RESULT_NO_ERROR;
}

/* Put all eligible brokers into the list of possible_brokers. */
// Gets the set of brokers constrained by instance gids ready to be redirected to.
// If gids are empty, all brokers are considered.
// The array of brokers is allocated inside as one chunk of memory.
static int get_viable_instances(struct balance_request_state* state)
{
    ZPN_BROKER_ASSERT_HARD(!state->viable_instances, "Viable instances is NULL!");
    char *alt_cloud = NULL;

    GLOBAL_RD_LOCK;

    //Grab setting here before we iterate so that we have a consistent view
    //Not a view that switches during config override change
    zpn_balance_get_current_configuration(&(state->configuration));

    /* Note: these are all public instances, built from customer's constellation config. */
    if (state->instance_gid_count) {
        for (size_t i = 0; i < state->instance_gid_count; ++i) {
            struct reported_instance *reported = zhash_table_lookup(global_state.table,
                                                            &state->instance_gids[i],
                                                            sizeof(state->instance_gids[i]),
                                                            NULL);
            if (!reported) {
                ZPN_DEBUG_BALANCE("%s Failed to find reported broker load for gid=%" PRId64,
                                  state->req->log_tag,
                                  state->instance_gids[i]);
                continue;
            }

            grow_viable_instances_if_needed(state);

            struct viable_instance *viable = &state->viable_instances[state->viable_instance_count];
            snprintf(viable->is_public, sizeof(viable->is_public), "%s", reported->is_public);
            viable->site = reported->site;
            alt_cloud = zpath_instance_get_alt_cloud(reported->gid);
            if (alt_cloud && alt_cloud[0]) {
                viable->alt_cloud = ZPN_BALANCE_STRDUP(alt_cloud, strlen(alt_cloud));
            }
            copy_reported_inst_to_viable_inst(viable, reported, &(state->configuration));
            state->viable_instance_count++;
        }
    }

    int64_t walk_key = 0;
    zhash_table_walk(global_state.table, &walk_key, get_viable_instances_tbl_walk_cb, state);

    GLOBAL_UNLOCK;
    return ZPN_RESULT_NO_ERROR;
}

typedef int (inst_table_entry_match_f)(void *filter, struct reported_instance *brk);

struct inst_table_dc_filter_criteria {
    double lon;
    double lat;
};

struct inst_table_filter_cookie {
    inst_table_entry_match_f *match_f;
    void *criteria;

    struct zpn_balance_instance_info *brokers;
    size_t broker_count; /* input size of brokers */
    size_t result_count; /* how many found so far */
};

static int inst_table_filter_walk_callback(void *cookie, void *object, void *key, size_t key_len) {
    struct inst_table_filter_cookie *c = cookie;
    struct reported_instance *brk = object;

    if (c->match_f(c->criteria, brk)) {
        if (c->result_count == c->broker_count) { /* overflow */
            return ZPATH_RESULT_INCOMPLETE;
        }

        /* matched, copy data out */
        c->brokers[c->result_count].gid = brk->gid;
        c->brokers[c->result_count].deleted = brk->deleted;
        c->brokers[c->result_count].type = brk->personality;
        c->brokers[c->result_count].lon = brk->site.lon;
        c->brokers[c->result_count].lat = brk->site.lat;
        c->brokers[c->result_count].brk_status = brk->brk_status;

        c->brokers[c->result_count].name[0] = '\0';
        c->brokers[c->result_count].balance_role[0] = '\0';
        c->brokers[c->result_count].balance_mode[0] = '\0';
        if (brk->name) snprintf(c->brokers[c->result_count].name, sizeof(c->brokers[c->result_count].name), "%s", brk->name);
        if (brk->balance_role) snprintf(c->brokers[c->result_count].balance_role, sizeof(c->brokers[c->result_count].balance_role), "%s", brk->balance_role);
        if (brk->balance_mode) snprintf(c->brokers[c->result_count].balance_mode, sizeof(c->brokers[c->result_count].balance_mode), "%s", brk->balance_mode);

        c->result_count++;
    }
    return ZPATH_RESULT_NO_ERROR;
}

static int inst_table_filter(inst_table_entry_match_f* match_f,
                            void* criteria,
                            struct zpn_balance_instance_info* brokers,
                            int* count)
{
    int res = ZPN_RESULT_NO_ERROR;

    GLOBAL_RD_LOCK;

    int64_t walk_key = 0;
    struct inst_table_filter_cookie cookie = { match_f, criteria, brokers, *count, 0 };
    res = zhash_table_walk(global_state.table, &walk_key, inst_table_filter_walk_callback, &cookie);

    GLOBAL_UNLOCK;

    *count = cookie.result_count;
    return res;
}

static int inst_table_entry_dc_match_f(void *criteria, struct reported_instance *brk) {
    struct inst_table_dc_filter_criteria *c = criteria;
    return c->lat == brk->site.lat && c->lon == brk->site.lon && brk->deleted == 0;
}

int zpn_balance_instances_in_dc(double lon, double lat, struct zpn_balance_instance_info* brokers, int* count)
{
    struct inst_table_dc_filter_criteria criteria = { lon, lat };
    return inst_table_filter(&inst_table_entry_dc_match_f, &criteria, brokers, count);
}

int zpn_balance_instances_in_my_dc(struct zpn_balance_instance_info* brokers, int* count)
{
    struct site my_site;
    get_my_site(&my_site);
    return zpn_balance_instances_in_dc(my_site.lon, my_site.lat, brokers, count);
}


int zpn_balance_inst_load_execute(struct balance_request_state* state)
{
    if (!GLOBAL_INITIALIZED) return ZPN_RESULT_BAD_STATE;

    const struct zpn_balance_redirect_request *req = state->req;
    int64_t fetch_customer_gid;
    struct zpath_customer_to_constellation *result[MAX_TOPLEVEL_CONSTELLATIONS_PER_CUSTOMER];
    struct zpath_customer_to_constellation *result_exclude[MAX_TOPLEVEL_CONSTELLATIONS_PER_CUSTOMER];
    size_t count = MAX_TOPLEVEL_CONSTELLATIONS_PER_CUSTOMER;
    size_t count_exclude = MAX_TOPLEVEL_CONSTELLATIONS_PER_CUSTOMER;
    int res;

    fetch_customer_gid = req->customer_gid;
    state->instance_gid_count = ZPN_MAX_BROKERS;

    //no public brokers requested. Ignore constellation
    if (req->rbt != NULL && req->rbt->type[PUBLIC_BROKER] == 0) {
        state->instance_gid_count = 0;
        res = get_viable_instances(state);
        if (res) {
            ZPN_BALANCE_LOG_STATE(AL_ERROR, "%s Failed to get the list of viable brokers for customer_gid = %" PRId64 ", instance_gid_count = %zu: %s",
                                        req->log_tag, req->customer_gid, state->instance_gid_count, zpn_error_description_string(res));
        } else {
            ZPN_BALANCE_LOG_STATE(AL_INFO, "%s customer %" PRId64 " has %zu viable instances",
                                        req->log_tag, req->customer_gid, state->viable_instance_count);
        }
        return res;
    }

    res = zpath_customer_to_constellation_get(fetch_customer_gid,
                                                  &result[0],
                                                  &count,
                                                  &result_exclude[0],
                                                  &count_exclude);
    if (res != ZPATH_RESULT_NO_ERROR) {
        ZPN_BALANCE_LOG_STATE(AL_ERROR, "%s Failed to get constellations for customer_gid = %" PRId64 ": %s",
                                    req->log_tag, req->customer_gid, zpath_result_string(res));
        return ZPN_RESULT_ERR;
    }

    if (!count) {
        /* No constellations for the customer. Check the root customer, and use theirs as default */
        fetch_customer_gid = ZPATH_GID_ROOT_CUSTOMER;
        count = MAX_TOPLEVEL_CONSTELLATIONS_PER_CUSTOMER;
        res = zpath_customer_to_constellation_get(fetch_customer_gid,
                                                  &result[0],
                                                  &count,
                                                  &result_exclude[0],
                                                  &count_exclude);
        if (res != ZPATH_RESULT_NO_ERROR) {
            ZPN_BALANCE_LOG_STATE(AL_ERROR, "%s Failed to get default constellations for root customer_gid = %" PRId64 ": %s",
                                        req->log_tag, fetch_customer_gid, zpath_result_string(res));
            return ZPN_RESULT_ERR;
        }
    }

    if (count) {
        int i;
        state->constellation_gid_count = count;
        state->constellation_gid_excl_count = count_exclude;
        for (i = 0; i < count; i++) {
            state->constellation_gids[i] = result[i]->constellation_gid;
        }
        for (i = 0; i < count_exclude; i++) {
            state->constellation_gids_excl[i] = result_exclude[i]->constellation_gid;
        }
        res = zpath_constellation_get_instance_gids(fetch_customer_gid,
                                                        state->instance_gids,
                                                        &state->instance_gid_count);
        if (res) {
            ZPN_BALANCE_LOG_STATE(AL_ERROR, "%s Failed to get constellation instance gids for customer_gid = %" PRId64 "(%"PRId64"): %s",
                                        req->log_tag, req->customer_gid, fetch_customer_gid, zpath_result_string(res));
            return ZPN_RESULT_ERR;
        }
    } else {
        state->instance_gid_count = 0;
    }

    res = get_viable_instances(state);
    if (res) {
        ZPN_BALANCE_LOG_STATE(AL_ERROR, "%s Failed to get the list of viable brokers for customer_gid = %" PRId64 ", instance_gid_count = %zu: %s",
                                    req->log_tag, req->customer_gid, state->instance_gid_count, zpn_error_description_string(res));
    } else {
        ZPN_BALANCE_LOG_STATE(AL_INFO, "%s customer %" PRId64 " has %zu constellations, %zu negative, %zu instance gids determined by constellations, with %zu viable instances",
                                    req->log_tag, req->customer_gid, count, count_exclude, state->instance_gid_count, state->viable_instance_count);
    }
    return res;
}


void zpn_balance_inst_load_free(struct balance_request_state* state)
{
    for (int i = 0; i < state->viable_instance_count; i++) {
        if (state->viable_instances[i].name) ZPN_BALANCE_FREE(state->viable_instances[i].name);
        if (state->viable_instances[i].alt_cloud) ZPN_BALANCE_FREE(state->viable_instances[i].alt_cloud);
        if (state->viable_instances[i].instance_role) ZPN_BALANCE_FREE(state->viable_instances[i].instance_role);
        if (state->viable_instances[i].balance_role) ZPN_BALANCE_FREE(state->viable_instances[i].balance_role);
        if (state->viable_instances[i].balance_mode) ZPN_BALANCE_FREE(state->viable_instances[i].balance_mode);
        for (int j = 0; j < state->viable_instances[i].publish_ips_count; j++) {
            ZPN_BALANCE_FREE(state->viable_instances[i].publish_ips[j]);
        }

        if (state->viable_instances[i].publish_ips_count)
            ZPN_BALANCE_FREE(state->viable_instances[i].publish_ips);

        if (state->viable_instances[i].trusted_networks_count) {
            ZPN_BALANCE_FREE(state->viable_instances[i].trusted_networks_gids);
            state->viable_instances[i].trusted_networks_gids =  NULL;
            state->viable_instances[i].trusted_networks_count = 0;
        }
    }
    ZPN_BALANCE_FREE(state->viable_instances);
    state->viable_instances = NULL;
    state->viable_instance_capacity = state->viable_instance_count = 0;
}

static void copy_local_override_to_viable_inst(struct viable_instance *viable)
{
    if (!LOCAL_OVERRIDE_INITIALIZED) return;

    LOCAL_OVERRIDE_RD_LOCK;

    struct instance_local_override *item = zhash_table_lookup(g_local_override.table, &viable->gid, sizeof(viable->gid), NULL);
    if (item) {
        /* always copied because cpu/memory load could be 0 */
        viable->cpu_pct = item->cpu_pct;
        viable->mem_pct = item->mem_pct;
        viable->proc_fd_util_pct = item->proc_fd_util_pct;
        viable->is_dualstack = item->is_dualstack;
        /* lat/lon/cc are copied only if they are set */
        if (item->site.lat) viable->site.lat = item->site.lat;
        if (item->site.lon) viable->site.lon = item->site.lon;
        if (item->site.cc[0] && item->site.cc[1]) {
            viable->site.cc[0] = item->site.cc[0];
            viable->site.cc[1] = item->site.cc[1];
            viable->site.cc[2] = '\0';
        }
        viable->modified_time_s =  epoch_s(); // local override is always considered up-to-date.
        if (item->role[0]) {
            if (viable->balance_role) ZPN_BALANCE_FREE(viable->balance_role);
            viable->balance_role  = ZPN_BALANCE_STRDUP(item->role, strlen(item->role));
        }
        if (item->mode[0]) {
            if (viable->balance_mode) ZPN_BALANCE_FREE(viable->balance_mode);
            viable->balance_mode  = ZPN_BALANCE_STRDUP(item->mode, strlen(item->mode));
        }
        if (item->alt_cloud[0]) {
            if (viable->alt_cloud) ZPN_BALANCE_FREE(viable->alt_cloud);
            viable->alt_cloud  = ZPN_BALANCE_STRDUP(item->alt_cloud, strlen(item->alt_cloud));
        }

        if (item->use_client_count) {
            viable->clients = item->client_count;
        }
        viable->is_locally_overriden = 1;
    }

    LOCAL_OVERRIDE_UNLOCK;
}

static int debug_instance_walk_cb(void *cookie1, void *cookie2, void *cookie3, void *cookie4, void *cookie5,
                                        void *object, void *key, size_t key_len)
{
    struct zpath_debug_state *request_state = (struct zpath_debug_state *)cookie1;
    int64_t gid = *((int64_t*)cookie2);
    int64_t c_gid = *((int64_t*)cookie3);
    char* name = (char*)cookie4;
    int64_t now = *((int64_t*)cookie5);
    struct reported_instance *inst = (struct reported_instance *)object;
    if ((!gid && !c_gid && !name) || inst->gid == gid || ZPATH_GID_GET_CUSTOMER_GID(inst->gid) == c_gid ||
        (name != NULL && inst->name != NULL && strstr(inst->name, name)))
    {
        int diff_s = (int)(inst->modified_time_s ? (now - inst->modified_time_s) : -1);
        char *alt_cloud = zpath_instance_get_alt_cloud(inst->gid);
        ZDP("gid=%"PRId64", deleted=%d, type=%s, cpu=%3d, mem=%3d, clients=%" PRId64 ", lat=%+7.2f, lon=%+7.2f, cc=%2s, alt_cloud=%s, rpt=%10"PRId64"(%9ds), dualstack=%d, name=%s\n",
            inst->gid, inst->deleted, inst->personality == ZPN_INSTANCE_TYPE_PUBLIC_BROKER ? "ZS" : "PR",
            inst->cpu_pct, inst->mem_pct, inst->clients, inst->site.lat, inst->site.lon, inst->site.cc,
            ((alt_cloud) ? alt_cloud : "N/A"), inst->modified_time_s, diff_s, inst->is_dualstack, inst->name ? inst->name : "N/A");
    }
    return ZPN_RESULT_NO_ERROR;
}

static int debug_instance_show(struct zpath_debug_state *request_state, const char **query_values, int query_value_count, void *object)
{
    int res = ZPN_RESULT_NO_ERROR;
    int64_t gid = 0;
    int64_t c_gid = 0;
    int64_t walk_key = 0;
    int64_t now = epoch_s();

    if (query_values[0]) { gid = strtoull(query_values[0], NULL, 10); }
    if (query_values[1]) { c_gid = strtoull(query_values[1], NULL, 10); }

    GLOBAL_RD_LOCK;
    res = zhash_table_walk2(global_state.table, &walk_key, debug_instance_walk_cb, request_state, &gid, &c_gid, (char*)query_values[2], &now);
    GLOBAL_UNLOCK;

    ZDP("show instance for gid %"PRId64", customer %"PRId64", name %s result %s\n",
        gid, c_gid, (query_values[2] ? query_values[2] : "n/a"), zpn_result_string(res));
    return ZPN_RESULT_NO_ERROR;
}

static int debug_instance_local_show_walk_cb(void *cookie1, void *cookie2, void *cookie3, void *cookie4, void *cookie5,
                                        void *object, void *key, size_t key_len)
{
    struct zpath_debug_state *request_state = (struct zpath_debug_state *)cookie1;
    int64_t gid = *((int64_t*)cookie2);
    uint32_t *index = (uint32_t*)cookie3;
    uint32_t *count = (uint32_t*)cookie4;

    struct instance_local_override *item = (struct instance_local_override *)object;
    if (!gid || gid == item->gid) {
        ZDP("[%3u] gid=%"PRId64", cpu=%3u, mem=%3u, fd=%3u, cli[%s]=%"PRIu64", lat=%+7.2f, lon=%+7.2f, cc=%2s, role=%12s, mode=%12s, tag=%s, dualstack=%d\n",
            *count, item->gid, item->cpu_pct, item->mem_pct, item->proc_fd_util_pct, (item->use_client_count ? "ON":"OFF"), item->client_count, item->site.lat, item->site.lon, item->site.cc, item->role, item->mode, item->tag, item->is_dualstack);
        (*count)++;
    }
    (*index)++;
    return ZPN_RESULT_NO_ERROR;
}

static int debug_instance_local_show(struct zpath_debug_state *request_state, const char **query_values, int query_value_count, void *object)
{
    if (!LOCAL_OVERRIDE_INITIALIZED) { ZDP("local cache not initialized\n"); return ZPN_RESULT_BAD_ARGUMENT; }

    int res = ZPN_RESULT_NO_ERROR;
    int64_t gid = 0;
    int64_t walk_key = 0;
    uint32_t index = 0;
    uint32_t count = 0;

    if (query_values[0]) { gid = strtoull(query_values[0], NULL, 10); }

    LOCAL_OVERRIDE_RD_LOCK;
    res = zhash_table_walk2(g_local_override.table, &walk_key, debug_instance_local_show_walk_cb, request_state, &gid, &index, &count, NULL);
    LOCAL_OVERRIDE_UNLOCK;

    ZDP("%u results returned for gid %"PRId64". Error: %s\n", count, gid, zpn_result_string(res));
    return ZPN_RESULT_NO_ERROR;
}

static int debug_instance_local_generate_walk_cb(void *cookie1, void *cookie2, void *cookie3, void *cookie4, void *cookie5,
                                        void *object, void *key, size_t key_len)
{
    struct reported_instance *item = (struct reported_instance *)object;
    // only do it for for public brokers.
    if (item->personality != ZPN_INSTANCE_TYPE_PUBLIC_BROKER) return ZPN_RESULT_NO_ERROR;

    struct zpath_debug_state *request_state = (struct zpath_debug_state *)cookie1;
    uint32_t dcs = *((uint32_t*)cookie2);
    uint32_t inst_per_dc = *((uint32_t*)cookie3);
    uint32_t *current_dc_indx = (uint32_t*)cookie4;
    uint32_t *current_inst_indx = (uint32_t*)cookie5;

    double lat = (-90.0) + (180.0 * (*current_dc_indx + 1) / dcs);
    double lon = (-180.0) + (360.0 * (*current_dc_indx + 1) / dcs);
    if (lat == 0.0) lat = 1.0;
    if (lon == 0.0) lon = 1.0;
    int mem = random() % 100;
    int cpu = random() % 100;
    int fd = random() % 100;

    ZDP("curl \"http://localhost:8000/balance/instance/local/add?gid=%"PRId64
        "&cpu=%d&mem=%d&fd=%d&lat=%.2f&lon=%.2f&cc=%s&role=FORWARD&mode=BALANCE&tag=%s\"\n",
            item->gid, cpu, mem, fd, lat, lon, "US", item->name);

    (*current_inst_indx)++;
    if ((*current_inst_indx) == inst_per_dc) {
        (*current_inst_indx) = 0;
        (*current_dc_indx)++;
    }

    return ZPN_RESULT_NO_ERROR;
}

static int debug_instance_local_generate(struct zpath_debug_state *request_state, const char **query_values, int query_value_count, void *object)
{
    if (!LOCAL_OVERRIDE_INITIALIZED) { return ZPN_RESULT_BAD_ARGUMENT; }
    if (!query_values[0]) { return ZPN_RESULT_BAD_ARGUMENT; }

    int res = ZPN_RESULT_NO_ERROR;
    uint32_t dcs = 0;
    uint32_t inst_per_dc = 0;
    uint32_t current_dc_indx = 0;
    uint32_t current_inst_indx = 0;
    int64_t walk_key = 0;

    dcs = (uint32_t)atoi(query_values[0]);
    if (dcs == 0 || dcs > 100) { return ZPN_RESULT_BAD_ARGUMENT; }

    GLOBAL_RD_LOCK;
    uint32_t total_insts = (uint32_t)zhash_table_get_size(global_state.table);
    inst_per_dc = (total_insts / dcs + 1);
    if (inst_per_dc == 0) inst_per_dc = 1;
    res = zhash_table_walk2(global_state.table, &walk_key, debug_instance_local_generate_walk_cb, request_state, &dcs, &inst_per_dc, &current_dc_indx, &current_inst_indx);
    GLOBAL_UNLOCK;

    ZDP("generate local instance params for %d result %s\n", dcs, zpn_result_string(res));
    return ZPN_RESULT_NO_ERROR;
}

static int zpn_balance_inst_local_add(int64_t gid, uint32_t cpu_util, uint32_t mem_util, uint32_t proc_fd_util,
                                      double lat, double lon, const char *cc, const char* tag, const char* role, const char* mode, const char *alt_cloud,
                                      int use_client_count, uint64_t client_count, int is_dualstack)
{
    int res = ZPN_RESULT_NO_ERROR;
    if (!LOCAL_OVERRIDE_INITIALIZED) return ZPN_RESULT_BAD_STATE;

    LOCAL_OVERRIDE_WR_LOCK;

    struct instance_local_override *item = zhash_table_lookup(g_local_override.table, &gid, sizeof(gid), NULL);
    if (!item) {
        item = ZPN_BALANCE_CALLOC(sizeof(struct instance_local_override));
        res = zhash_table_store(g_local_override.table, &gid, sizeof(gid), 0, item);
    }
    item->gid = gid;
    item->cpu_pct = cpu_util;
    item->mem_pct = mem_util;
    item->proc_fd_util_pct = proc_fd_util;
    item->site.lat = lat;
    item->site.lon = lon;
    item->use_client_count = use_client_count;
    item->client_count = client_count;
    if (is_dualstack >= 0) item->is_dualstack = is_dualstack;
    if (cc && cc[0] && cc[1]) {
        item->site.cc[0] = toupper(cc[0]);
        item->site.cc[1] = toupper(cc[1]);
    }
    if (tag) snprintf(item->tag, INSTANCE_LOCAL_OVERRIDE_TAG_LEN, "%s", tag);
    if (role) snprintf(item->role, INSTANCE_LOCAL_OVERRIDE_ROLE_MODE_LEN, "%s", role);
    if (mode) snprintf(item->mode, INSTANCE_LOCAL_OVERRIDE_ROLE_MODE_LEN, "%s", mode);
    if (alt_cloud) snprintf(item->alt_cloud, INSTANCE_LOCAL_OVERRIDE_ALT_CLOUD_LEN + 1, "%s", alt_cloud);

    LOCAL_OVERRIDE_UNLOCK;

    return res;
}

static int debug_instance_local_add(struct zpath_debug_state *request_state, const char **query_values, int query_value_count, void *object)
{
    int64_t     gid = 0;
    uint32_t    cpu = 0;
    uint32_t    mem = 0;
    double      lat = 0;
    double      lon = 0;
    int         res = 0;
    uint32_t    fd_util = 0;
    int set_client_count = 0;
    uint64_t client_count = 0;
    int is_dualstack = -1;

    // gid, mem, cpu are required
    if (!query_values[0]) { return ZPN_RESULT_BAD_ARGUMENT; }
    gid = strtoull(query_values[0], NULL, 10);

    if (!query_values[1]) { return ZPN_RESULT_BAD_ARGUMENT; }
    cpu = atoi(query_values[1]);
    if (cpu > 100) { return ZPN_RESULT_BAD_ARGUMENT; }

    if (!query_values[2]) { return ZPN_RESULT_BAD_ARGUMENT; }
    mem = atoi(query_values[2]);
    if (mem > 100) { return ZPN_RESULT_BAD_ARGUMENT; }

    // lat/lon are used only if they are not 0 (strictly speaking 0/0 is valid but reserved for "not set" here)
    if (query_values[3]) { lat = strtod(query_values[3], NULL); if ( lat > 90.0 || lat < -90.0) { return ZPN_RESULT_BAD_ARGUMENT; } }
    if (query_values[4]) { lon = strtod(query_values[4], NULL); if ( lon > 180.0 || lon < -180.0) { return ZPN_RESULT_BAD_ARGUMENT; } }

    if (query_values[10]) {
        fd_util = atoi(query_values[10]);
        if (fd_util > 100) { return ZPN_RESULT_BAD_ARGUMENT; }
    }

    if(query_values[11]) {
        set_client_count = strtoul(query_values[11], NULL, 0) != 0;
    }

    if (query_values[12]) {
        client_count = strtoull(query_values[12], NULL, 0);
    }

    if (query_values[13]) {
        is_dualstack = atoi(query_values[13]);
    }

    res = zpn_balance_inst_local_add(gid, cpu, mem, fd_util, lat, lon, query_values[5], query_values[6], query_values[7], query_values[8], query_values[9], set_client_count, client_count, is_dualstack);
    ZDP("adding %"PRId64" result %s\n", gid, zpn_result_string(res));
    return ZPN_RESULT_NO_ERROR;
}

static int zpn_balance_inst_local_remove(int64_t gid)
{
    int res = ZPN_RESULT_NO_ERROR;

    if (!LOCAL_OVERRIDE_INITIALIZED) return ZPN_RESULT_BAD_STATE;

    LOCAL_OVERRIDE_WR_LOCK;
    struct instance_local_override *item = zhash_table_lookup(g_local_override.table, &gid, sizeof(gid), NULL);
    if (item) {
        res = zhash_table_remove(g_local_override.table, &gid, sizeof(gid), NULL);
        ZPN_BALANCE_FREE(item);
    } else {
        res = ZPN_RESULT_NOT_FOUND;
    }
    LOCAL_OVERRIDE_UNLOCK;
    return res;
}

static int debug_instance_local_remove(struct zpath_debug_state *request_state, const char **query_values, int query_value_count, void *object)
{
    if (!query_values[0]) { ZDP("gid is required\n"); return ZPN_RESULT_BAD_ARGUMENT; }
    int64_t gid = strtoull(query_values[0], NULL, 10);
    int res = zpn_balance_inst_local_remove(gid);
    ZDP("removing %"PRId64" result %s\n", gid, zpn_result_string(res));
    return ZPN_RESULT_NO_ERROR;
}

static __inline__ void free_instance_local_override_f(void *element, void *cookie) { ZPN_BALANCE_FREE(element); }

static int zpn_balance_inst_local_clear()
{
    if (!LOCAL_OVERRIDE_INITIALIZED) return ZPN_RESULT_BAD_STATE;

    LOCAL_OVERRIDE_WR_LOCK;
    zhash_table_flush_and_call(g_local_override.table, free_instance_local_override_f, NULL);
    LOCAL_OVERRIDE_UNLOCK;
    return ZPN_RESULT_NO_ERROR;
}

static int debug_instance_local_clear(struct zpath_debug_state *request_state, const char **query_values, int query_value_count, void *object)
{
    int res = zpn_balance_inst_local_clear();
    ZDP("res: %s\n", zpn_result_string(res));
    return ZPN_RESULT_NO_ERROR;
}

void zpn_balance_get_instance_selection_retry_configuration(struct balance_request_configuration *configuration){
    configuration->retries.enabled = 0;
    configuration->retries.max = 0;
    configuration->retries.threshold = 0;

    int64_t hard_disabled = CONFIG_FEATURE_BROKER_BALANCE_INSTANCE_SELECTION_HARD_DISABLE_RETRIES_DEFAULT;
    zpath_config_override_get_config_int( CONFIG_FEATURE_BROKER_BALANCE_INSTANCE_SELECTION_HARD_DISABLE_RETRIES,
                                          &(hard_disabled),
                                          CONFIG_FEATURE_BROKER_BALANCE_INSTANCE_SELECTION_HARD_DISABLE_RETRIES_DEFAULT,
                                          (int64_t) ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                          (int64_t) 0);

    if (hard_disabled) {
        // Hard disabled do nothing further.
        return;
    }

    configuration->retries.enabled = CONFIG_FEATURE_BROKER_BALANCE_INSTANCE_SELECTION_USE_RETRIES_DEFAULT;
    zpath_config_override_get_config_int( CONFIG_FEATURE_BROKER_BALANCE_INSTANCE_SELECTION_USE_RETRIES,
                                         &(configuration->retries.enabled),
                                         CONFIG_FEATURE_BROKER_BALANCE_INSTANCE_SELECTION_USE_RETRIES_DEFAULT,
                                         ZPATH_INSTANCE_GID,
                                         (int64_t) ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                         (int64_t) 0);

    if (!configuration->retries.enabled) {
        // Individually or globally not enabled so give up
        return;
    }


    configuration->retries.max = CONFIG_FEATURE_BROKER_BALANCE_INSTANCE_SELECTION_RETRIES_LIMIT_DEFAULT;
    zpath_config_override_get_config_int(CONFIG_FEATURE_BROKER_BALANCE_INSTANCE_SELECTION_RETRIES_LIMIT,
                                          &(configuration->retries.max),
                                          CONFIG_FEATURE_BROKER_BALANCE_INSTANCE_SELECTION_RETRIES_LIMIT_DEFAULT,
                                          ZPATH_INSTANCE_GID,
                                          (int64_t) ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                          (int64_t) 0);

    configuration->retries.threshold = CONFIG_FEATURE_BROKER_BALANCE_INSTANCE_SELECTION_RETRY_MIN_BUCKET_THRESHOLD_DEFAULT;
    zpath_config_override_get_config_int(CONFIG_FEATURE_BROKER_BALANCE_INSTANCE_SELECTION_RETRY_MIN_BUCKET_THRESHOLD,
                                         &(configuration->retries.threshold),
                                         CONFIG_FEATURE_BROKER_BALANCE_INSTANCE_SELECTION_RETRY_MIN_BUCKET_THRESHOLD_DEFAULT,
                                         ZPATH_INSTANCE_GID,
                                         (int64_t) ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                         (int64_t) 0);
}

void zpn_balance_get_client_counts_configuration(struct balance_request_configuration *configuration){
    configuration->client_counts.hard_disabled = CONFIG_FEATURE_BROKER_BALANCE_CLIENT_LOAD_HARD_DISABLED_DEFAULT;
    zpath_config_override_get_config_int(CONFIG_FEATURE_BROKER_BALANCE_CLIENT_LOAD_HARD_DISABLED,
                                         &(configuration->client_counts.hard_disabled),
                                         CONFIG_FEATURE_BROKER_BALANCE_CLIENT_LOAD_HARD_DISABLED_DEFAULT,
                                         (int64_t) ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                         (int64_t) 0);

    // hard disable needs to stop looking things up in the config override
    // and just give up
    if (configuration->client_counts.hard_disabled) {
        configuration->client_counts.se_rate = 0;
        configuration->client_counts.se_enabled= 0;
        configuration->client_counts.pse_rate = 0;
        configuration->client_counts.pse_enabled= 0;
        configuration->client_counts.min_clients_threshold = 0;
        configuration->client_counts.min_load_threshold = 0;
        return;
    }

    configuration->client_counts.se_rate = CONFIG_FEATURE_BROKER_BALANCE_CLIENT_LOAD_SE_ENABLED_RATE_DEFAULT;
    /*
     * This override is to enable 'use clients' feature for public_broker redirect targets
     * In PCC, we wont have public brokers as targets; so we need not get the value for PCC case.
     */
    if (g_broker_common_cfg && ZPN_BROKER_IS_PUBLIC()) {
        zpath_config_override_get_config_int(CONFIG_FEATURE_BROKER_BALANCE_CLIENT_LOAD_SE_ENABLED_RATE,
                                             &(configuration->client_counts.se_rate),
                                             CONFIG_FEATURE_BROKER_BALANCE_CLIENT_LOAD_SE_ENABLED_RATE_DEFAULT,
                                             ZPATH_INSTANCE_GID,
                                             (int64_t) ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                             (int64_t) 0);
    }

    configuration->client_counts.pse_rate = CONFIG_FEATURE_BROKER_BALANCE_CLIENT_LOAD_PSE_ENABLED_RATE_DEFAULT;
    zpath_config_override_get_config_int(CONFIG_FEATURE_BROKER_BALANCE_CLIENT_LOAD_PSE_ENABLED_RATE,
                                         &(configuration->client_counts.pse_rate),
                                         CONFIG_FEATURE_BROKER_BALANCE_CLIENT_LOAD_PSE_ENABLED_RATE_DEFAULT,
                                         ZPATH_INSTANCE_GID,
                                         (int64_t) ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                         (int64_t) 0);

    uint32_t rand = ((uint32_t)random()) % CONFIG_FEATURE_BROKER_BALANCE_CLIENT_LOAD_ENABLED_RATE_MAX;

    configuration->client_counts.se_enabled = rand < configuration->client_counts.se_rate;
    configuration->client_counts.pse_enabled = rand < configuration->client_counts.pse_rate;

    configuration->client_counts.min_load_threshold = CONFIG_FEATURE_BROKER_BALANCE_CLIENT_LOAD_MIN_LOAD_PERCENT_THRESHOLD_DEFAULT;
    zpath_config_override_get_config_int(CONFIG_FEATURE_BROKER_BALANCE_CLIENT_LOAD_MIN_LOAD_PERCENT_THRESHOLD,
                                         &(configuration->client_counts.min_load_threshold),
                                         CONFIG_FEATURE_BROKER_BALANCE_CLIENT_LOAD_MIN_LOAD_PERCENT_THRESHOLD_DEFAULT,
                                         ZPATH_INSTANCE_GID,
                                         (int64_t) ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                         (int64_t) 0);

    configuration->client_counts.min_clients_threshold = CONFIG_FEATURE_BROKER_BALANCE_CLIENT_LOAD_MIN_CLIENT_COUNT_THRESHOLD_DEFAULT;
    zpath_config_override_get_config_int(CONFIG_FEATURE_BROKER_BALANCE_CLIENT_LOAD_MIN_CLIENT_COUNT_THRESHOLD,
                                         &(configuration->client_counts.min_clients_threshold),
                                         CONFIG_FEATURE_BROKER_BALANCE_CLIENT_LOAD_MIN_CLIENT_COUNT_THRESHOLD_DEFAULT,
                                         ZPATH_INSTANCE_GID,
                                         (int64_t) ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                         (int64_t) 0);

}

int zpn_balance_is_client_estimation_enabled_for_instance(struct balance_request_configuration *configuration,
                                                          enum Zpn_Instance_Type personality) {
    if (!configuration) {
        return 0;
    }

    switch(personality) {
        case ZPN_INSTANCE_TYPE_PUBLIC_BROKER:
            return configuration->client_counts.se_enabled != 0;
        case ZPN_INSTANCE_TYPE_PRIVATE_BROKER:
            return configuration->client_counts.pse_enabled != 0;
        default:
            return 0;
    }
}

int zpn_balance_is_client_estimation_enabled_for_bucket(struct balance_request_configuration *configuration,
                                                        enum dc_bucket_type bucket_type) {
    if (!configuration) {
        return 0;
    }

    switch (bucket_type) {
        case BT_PRIV_MTN:
                    __attribute__((fallthrough));
        case BT_PRIV:
                    __attribute__((fallthrough));
        case BT_PUBL_PRIV_MTN:
                    __attribute__((fallthrough));
        case BT_PUBL_PRIV:
            return configuration->client_counts.pse_enabled != 0;
        case BT_PUBL:
            return configuration->client_counts.se_enabled != 0;
        default:
            return 0;
    }
}

void zpn_balance_get_current_configuration(struct balance_request_configuration *config) {
    zpn_balance_get_instance_selection_retry_configuration(config);
    zpn_balance_get_client_counts_configuration(config);
}

uint32_t compute_effective_sitec_load(uint32_t mem_util, uint32_t cpu_util, uint32_t fd_util)
{
    uint32_t mem_pct_new = compute_effective_mem_load(mem_util);
    uint32_t proc_fd_util_pct_new = compute_effective_proc_fd_util_load(fd_util);

    /* find max of the three factors */
    uint32_t effective_load;
    effective_load = (mem_pct_new > proc_fd_util_pct_new) ? mem_pct_new : proc_fd_util_pct_new;
    effective_load = (effective_load > cpu_util) ? effective_load : cpu_util;

    effective_load = effective_load > BROKER_LOAD_CEILING ? BROKER_LOAD_CEILING : effective_load;
    return effective_load;
}
