/*
 * zpath_siem_rx.h. Copyright (C) 2017 Zscaler Inc. All Rights Reserved
 */

#ifndef __ZPN_SIEM_RX_H__
#define __ZPN_SIEM_RX_H__
extern struct zhash_table *zpn_siem_debug_stats_table;
extern zpath_mutex_t zpn_siem_debug_stats_table_lock;

enum debug_log_error_type {
    mtunnel_not_allocated = 0,
    ack_failure,
    no_timer_event,
    siem_gid_not_found,
    no_zpn_fohh_client_slogger,
    siem_serialize_failed,
    failed_object_copy,
    output_init_failed,
    rs_siem_gid_changed,
    connection_going_away,
    siem_out_failed,
    zfcs_mt_consume_blocked,
    zfcs_mt_consume_failed,
    zpn_siem_debug_log_error_type_max /* Should always be last */
};

enum rx_state_destroy_type {
    destroy_generic = 0,
    destroy_timer,
    destroy_fohh,
    destroy_zfcs,
    destroy_mt,
    rx_state_destroy_type_max        /* Should always be last */
};
struct zpn_siem_debug_stats {
    uint64_t siem_gid;
    uint64_t customer_id;
    uint64_t is_active;
    uint64_t active_rs_conn_count;
    uint64_t destroyed_rs_conn_count;
    uint64_t log_received_count;
    uint64_t log_transfered_count;
    uint64_t log_dropped_count;
    uint64_t zpn_rs_destroy_reason_type_count[rx_state_destroy_type_max];
    uint64_t zpn_siem_error_type_count[zpn_siem_debug_log_error_type_max];
    zpath_mutex_t siem_stats_lock;
};

struct zpn_siem_debug_stats_log {                      /* _ARGO: object_definition */
    uint64_t mtunnel_not_allocated;                    /* _ARGO: integer */
    uint64_t ack_failure;                              /* _ARGO: integer */
    uint64_t no_timer_event;                           /* _ARGO: integer */
    uint64_t siem_gid_not_found_count;                 /* _ARGO: integer */
    uint64_t no_zpn_fohh_client_slogger;               /* _ARGO: integer */
    uint64_t siem_serialize_failed;                    /* _ARGO: integer */
    uint64_t failed_object_copy;                       /* _ARGO: integer */
    uint64_t output_init_failed;                       /* _ARGO: integer */
    uint64_t rs_siem_gid_changed;                      /* _ARGO: integer */
    uint64_t connection_going_away;                    /* _ARGO: integer */
    uint64_t siem_out_failed;                          /* _ARGO: integer */
    uint64_t zfcs_mt_consume_blocked;                  /* _ARGO: integer */
    uint64_t zfcs_mt_consume_failed;                   /* _ARGO: integer */
};

struct zpn_siem_conn_details_log {                                     /* _ARGO: object_definition */
    uint64_t total_incoming_rs_conn_count;                             /* _ARGO: integer */
    uint64_t total_active_rs_conn_count;                               /* _ARGO: integer */
    uint64_t total_destroyed_rs_conn_count;                            /* _ARGO: integer */
    uint64_t total_log_received_count;                                 /* _ARGO: integer */
    uint64_t total_log_transfered_count;                               /* _ARGO: integer */
    uint64_t total_log_dropped_count;                                  /* _ARGO: integer */
    uint64_t total_broker_redirection_count;                           /* _ARGO: integer */
    uint64_t total_rs_destroy_reason_type_generic;                     /* _ARGO: integer */
    uint64_t total_rs_destroy_reason_type_timer;                       /* _ARGO: integer */
    uint64_t total_rs_destroy_reason_type_fohh;                        /* _ARGO: integer */
    uint64_t total_rs_destroy_reason_type_zfcs;                        /* _ARGO: integer */
    uint64_t total_rs_destroy_reason_type_mt;                          /* _ARGO: integer */
};
/*
 * Calls for handling connections from log generators (brokers,
 * etc). This is used by slogger to process connections.
 */

/*
 * Listen and process siem traffic. (This is slogger functionality)
 *
 * Accepts traffic to the specified sni_str.
 */
int zpn_siem_rx_listen(struct fohh_generic_server *sni_server, char *sni_str, char *sni_str_json);
struct zpn_siem_debug_stats *zpn_siem_get_global_debug_stats();
uint64_t zpn_siem_get_global_broker_redirection_count();
uint64_t* zpn_siem_get_incoming_client_connection_count();
#endif /* __ZPN_SIEM_RX_H__ */
