/*
 * zpn_broker_natural.c. Copyright (C) 2024 Zscaler Inc. All Rights Reserved.
 *
 * This file contains all the rate limiting towards natural from broker related functionality.
 */
#include "zpn/zpn_lib.h"
#include "zpn/zpn_broker_private.h"
#include "zpath_lib/zpath_config_override.h"
#include "zpath_lib/zpath_config_override_keys.h"
#include "zpn/zpn_private_broker_private.h"
#include "zpath_lib/zpath_customer.h"
#include "zpn/zpn_broker_natural.h"

#define ZPN_NATURAL_LOG_THROTTLE_DATA_TBL_KEY_SIZE           512
#define ZPN_NATURAL_LOG_THROTTLE_FLUSH_COUNT_INC             1
#define ZPN_NATURAL_LOG_THROTTLE_ERR_TBL_CLEANUP_COUNT       60
#define ZPN_NATURAL_LOG_THROTTLE_ERR_CODES_FILLUP_SKIP_COUNT 12

struct natural_transaction_throttle_logs{
    char *prev_err;
    char *user_id;
    uint8_t is_flush_needed;
    int64_t count;
    int64_t customer_id;
    int64_t trans_log_capture_start_time;
    struct argo_object *stored_transaction_log;
};

static struct zhash_table* zpn_natural_log_throttle_err_codes_tbl = NULL;
static struct zhash_table* zpn_natural_log_throttle_stats_tbl = NULL;
zpath_mutex_t zpn_natural_log_throttle_err_codes_tbl_lock;
zpath_mutex_t zpn_natural_log_throttle_stats_tbl_lock;
// We can throttle max of 256 error codes
uint16_t zpn_natural_log_throttle_err_codes_count = 0;

extern struct argo_structure_description*  natural_transaction_throttle_logs_stats_description;

int64_t natural_log_throttle_threshold_time = NATURAL_TRANS_LOG_RATE_LIMIT_DEFAULT_TIME;
int64_t natural_log_throttle_enable = NATURAL_TRANS_LOG_RATE_LIMITTING_DEFAULT;

int zpn_natural_log_error_codes_throttle_init(void){
    int res = ZPN_RESULT_NO_ERROR;
    if (!zpn_natural_log_throttle_err_codes_tbl) {
        zpn_natural_log_throttle_err_codes_tbl = zhash_table_alloc(&zpn_allocator);
        zpn_natural_log_throttle_err_codes_tbl_lock = ZPATH_MUTEX_INIT;
        if (!zpn_natural_log_throttle_err_codes_tbl) {
            ZPN_LOG(AL_ERROR, "Failed to allocate memory for natural's log throttling error code table");
            return ZPN_RESULT_NO_MEMORY;
        }
    }
    return res;
}

int zpn_natural_log_error_codes_stats_init(void){
    int res = ZPN_RESULT_NO_ERROR;
    if (!zpn_natural_log_throttle_stats_tbl) {
        zpn_natural_log_throttle_stats_tbl = zhash_table_alloc(&zpn_allocator);
        zpn_natural_log_throttle_stats_tbl_lock = ZPATH_MUTEX_INIT;
        if (!zpn_natural_log_throttle_stats_tbl) {
            ZPN_LOG(AL_ERROR, "Failed to allocate memory for natural's log throttled stats table");
            return ZPN_RESULT_NO_MEMORY;
        }
    }
    return res;
}

static void natural_log_throttle_err_code_data_tbl_free_callback(void *object, void *cookie)
{
    struct natural_transaction_throttle_logs *free_logs = (struct natural_transaction_throttle_logs *)object;
    if(free_logs != NULL){
        if(free_logs->prev_err != NULL){
            ZPN_FREE(free_logs->prev_err);
            free_logs->prev_err = NULL;
        }
        if(free_logs->user_id != NULL){
            ZPN_FREE(free_logs->user_id);
            free_logs->user_id = NULL;
        }
        if(free_logs->stored_transaction_log != NULL){
            argo_object_release(free_logs->stored_transaction_log);
            free_logs->stored_transaction_log = NULL;
        }
        ZPN_FREE(free_logs);
    }
}

void zpn_c_state_natural_log_lock(struct zpn_natural_log_throttle_data *natural_log)
{
    if(natural_log){
        ZPATH_MUTEX_LOCK(&(natural_log->lock), __FILE__, __LINE__);
    } else{
        ZPN_LOG(AL_WARNING, "No natural_log for locking in zpn_c_state_natural_log_lock");
    }
}

void zpn_c_state_natural_log_unlock(struct zpn_natural_log_throttle_data *natural_log)
{
    if(natural_log){
        ZPATH_MUTEX_UNLOCK(&(natural_log->lock), __FILE__, __LINE__);
    } else{
        ZPN_LOG(AL_WARNING, "No natural_log for unlocking in zpn_c_state_natural_log_unlock");
    }
}

void zpn_natural_log_throttle_print_debug_stats(struct natural_transaction_throttle_logs_stats* debug_stats,
                                                struct zpath_debug_state *request_state)
{
    int64_t total_skip_count;
    int64_t log_flushed_count;
    int64_t last_time_log_flushed;

    ZPN_ATOMIC_LOAD(&(debug_stats->total_skip_count),total_skip_count);
    ZPN_ATOMIC_LOAD(&(debug_stats->log_flushed_count),log_flushed_count);
    ZPN_ATOMIC_LOAD(&(debug_stats->last_time_log_flushed),last_time_log_flushed);

    ZDP("Debug stats:\n\
    \tError:\t%s\n\
    \tTotal_skip_count:\t%"PRId64"\n\
    \tLog_flushed_count:\t%"PRId64"\n\
    \tLast_Time_Log_Flushed:\t%"PRId64"\n",
    debug_stats->error_code,
    total_skip_count,
    log_flushed_count,
    last_time_log_flushed);
}

static int log_throttle_debug_stats_table_walk(void *cookie, void *object, void* key, size_t key_len)
{
    struct natural_transaction_throttle_logs_stats *debug_stats =
                                    (struct natural_transaction_throttle_logs_stats*) object;
    struct zpath_debug_state *request_state = (struct zpath_debug_state *) cookie;
    zpn_natural_log_throttle_print_debug_stats(debug_stats, request_state);
    return 0;
}

int zpn_broker_natural_log_throttle_stats_dump(struct zpath_debug_state *request_state,
                                          const char **query_values,
                                          int query_value_count,
                                          void *cookie)
{
    int64_t walk_key = 0;
    ZPATH_MUTEX_LOCK(&(zpn_natural_log_throttle_stats_tbl_lock), __FILE__, __LINE__);
    while ( zhash_table_walk(zpn_natural_log_throttle_stats_tbl,
                             &walk_key,
                             log_throttle_debug_stats_table_walk,
                             request_state) == ZHASH_RESULT_INCOMPLETE ) {}
    ZDP("Total number of errors being throttled in this broker instance: %d \n", zpn_natural_log_throttle_err_codes_count);
    ZPATH_MUTEX_UNLOCK(&(zpn_natural_log_throttle_stats_tbl_lock), __FILE__, __LINE__);
    return ZPATH_RESULT_NO_ERROR;
}
/*
   We can throttle max of 256 error_codes in this implementation.
   From the error_code - index maping in global err_codes_tbl, take the index value.
   Marking index of error_set as 1 which signifies the enabling of particular err_code.
*/
int zpn_natural_log_throttle_add_error_codes(char *config_err_code, int *index)
{
    if(zpn_natural_log_throttle_err_codes_count > (ZPN_NATURAL_LOG_THROTTLE_MAX_ERROR_COUNT - 1)){
        //logged by the caller
        return ZPN_RESULT_ERR;
    }else{
        int *err_code_index = ZPN_CALLOC(sizeof(*err_code_index));
        *err_code_index = zpn_natural_log_throttle_err_codes_count;
        *index = *err_code_index;
        zhash_table_store( zpn_natural_log_throttle_err_codes_tbl,
                           config_err_code,
                           strlen(config_err_code),
                           0,
                           err_code_index);
        __sync_add_and_fetch(&(zpn_natural_log_throttle_err_codes_count), 1);
        return ZPN_RESULT_NO_ERROR;
    }
}

void zpn_natural_log_throttle_time_callback(const int64_t *config_value, int64_t impacted_gid)
{
    natural_log_throttle_threshold_time = *config_value;
    return;
}

void zpn_natural_log_throttle_callback(const int64_t *config_value, int64_t impacted_gid)
{
    natural_log_throttle_enable = *config_value;
    return;
}

void zpn_natural_send_trans_log(int64_t customer_id, struct zpn_trans_log *obj)
{
    int res = zpath_customer_log_struct(customer_id,
                                        zpath_customer_log_type_zpn_transaction,
                                        "transaction",
                                        NULL,
                                        NULL,
                                        NULL,
                                        NULL,
                                        zpn_trans_log_description,
                                        obj);

    if (res) {
        ZPN_LOG(AL_WARNING, "Failed: To send transaction log to natural for customer_id: %"PRId64"",customer_id);
    }
}

void zpn_natural_throttle_logs_flush(struct natural_transaction_throttle_logs *logs, char *reason)
{
    struct zpn_trans_log *stored_obj = logs->stored_transaction_log->base_structure_void;
    if(stored_obj != NULL){
        if(logs->count){
            logs->count--;
        }
        stored_obj->skipped_error_count_natural = logs->count;
        snprintf(stored_obj->natural_throttle_log_flush_reason,
                 sizeof(stored_obj->natural_throttle_log_flush_reason),
                 "%s", reason);
        zpn_natural_log_throttle_update_stats(logs->count,
                                              ZPN_NATURAL_LOG_THROTTLE_FLUSH_COUNT_INC,
                                              logs->prev_err,
                                              epoch_s());
        zpn_natural_send_trans_log(logs->customer_id,stored_obj);
        logs->count = 0;
        logs->is_flush_needed = 0;
        logs->trans_log_capture_start_time = epoch_s();
    }
}

int log_throttle_cstate_data_tbl_flush_walk(void *cookie, void *object, void* key, size_t key_len)
{
    struct natural_transaction_throttle_logs *logs = (struct natural_transaction_throttle_logs *)object;
    char *reason = (char *)cookie;
    if(logs->is_flush_needed == 1){
        zpn_natural_throttle_logs_flush(logs, reason);
    }
    return 0;
}
/*
   Check if a particular error_code is empty or not.
   We are not checking the validity of an error_code because we do not want to restrict the list of supported error codes.
*/
int is_error_code_empty(const char *err_code)
{
  while (*err_code != '\0') {
    if (!isspace((unsigned char)*err_code))
        return ZPN_RESULT_NO_ERROR;
    err_code++;
  }
  return ZPN_RESULT_ERR;
}

void zpn_natural_log_throttle_err_set_update(uint8_t err_set[], char* config_err_code)
{
    int *err_code_index = (int*)zhash_table_lookup(zpn_natural_log_throttle_err_codes_tbl,
                                                    config_err_code,
                                                    strlen(config_err_code),
                                                    NULL);
    if(err_code_index == NULL){
        int index = -1;
        int res = zpn_natural_log_throttle_add_error_codes(config_err_code, &index);
        if(res == ZPN_RESULT_ERR){
            ZPN_LOG(AL_ERROR, "Cannot add error:%s.Max number of errors that can be throttled has exceeded the limit of 256.", config_err_code);
        }
        if(index != -1){
            err_set[index] = 1;
        }
    }else{
        err_set[*err_code_index] = 1;
    }
}

void log_throttle_cstate_data_tbl_walk(struct zpn_broker_client_fohh_state *c_state, char* reason)
{
    int64_t walk_key = 0;
    if (c_state->natural_log.err_code_data_tbl) {
        while (zhash_table_walk(c_state->natural_log.err_code_data_tbl,
                                &walk_key,
                                log_throttle_cstate_data_tbl_flush_walk,
                                reason) == ZHASH_RESULT_INCOMPLETE) {}
    }
}

void zpn_natural_log_free_data_tbl(struct zpn_broker_client_fohh_state *c_state, char* reason)
{
    log_throttle_cstate_data_tbl_walk(c_state, reason);
    if (c_state->natural_log.err_code_data_tbl) {
        zhash_table_free_and_call(c_state->natural_log.err_code_data_tbl, natural_log_throttle_err_code_data_tbl_free_callback, NULL);
        c_state->natural_log.err_code_data_tbl = NULL;
    }
}

int zpn_natural_log_throttle_err_tbl_cleanup_walk(void *cookie, void *object, void* key, size_t key_len)
{
    struct natural_transaction_throttle_logs *logs = (struct natural_transaction_throttle_logs *)object;
    char *reason = (char *)cookie;
    int64_t natural_log_throttle_time_diff = epoch_s() - logs->trans_log_capture_start_time;
    if(logs->is_flush_needed == 1 && natural_log_throttle_time_diff >= natural_log_throttle_threshold_time){
        zpn_natural_throttle_logs_flush(logs, reason);
    }
    return 0;
}

void zpn_natural_log_throttle_err_tbl_cleanup(struct zpn_broker_client_fohh_state *c_state)
{
    int64_t walk_key = 0;
    if (c_state->natural_log.err_code_data_tbl) {
        while (zhash_table_walk(c_state->natural_log.err_code_data_tbl,
                                &walk_key,
                                zpn_natural_log_throttle_err_tbl_cleanup_walk,
                                "throttling_idle_timeout_exceeded") == ZHASH_RESULT_INCOMPLETE) {}
    }
}

void zpn_natural_cleanup_log_throttle_err_tbl(struct zpn_broker_client_fohh_state *c_state)
{
    if(c_state!=NULL && c_state->customer_gid){
        zpn_c_state_natural_log_lock(&(c_state->natural_log));
        if(c_state->natural_log.err_data_tbl_clean_counter %
                ZPN_NATURAL_LOG_THROTTLE_ERR_TBL_CLEANUP_COUNT == 0){
            c_state->natural_log.err_data_tbl_clean_counter = 0;
            c_state->natural_log.err_data_tbl_clean_counter++;
            zpn_natural_log_throttle_err_tbl_cleanup(c_state);
        }else{
            c_state->natural_log.err_data_tbl_clean_counter++;
        }
        zpn_c_state_natural_log_unlock(&(c_state->natural_log));
    }
}

void zpn_natural_log_throttle_err_tbl_update(struct zpn_broker_client_fohh_state *c_state)
{
    int64_t customer_gid = c_state->customer_gid;
    if(!natural_log_throttle_enable){
        if(c_state->natural_log.err_code_data_tbl != NULL){
            log_throttle_cstate_data_tbl_walk(c_state, "log_throttle_disabled");
        }
        return;
    }else{
        if(c_state->natural_log.err_code_data_tbl == NULL){
            c_state->natural_log.err_code_data_tbl = zhash_table_alloc(&zpn_allocator);
        }
        char *config_value = NULL;
        config_value = zpath_config_override_get_config_str(NATURAL_TRANS_LOG_RATE_LIMIT_ERRORS,
                                                        &config_value,
                                                        "",
                                                        customer_gid,
                                                        ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                        (int64_t) 0);
        if(config_value == NULL){
            return;
        }
        int config_value_length = strlen(config_value);
        if(config_value_length > 0){
            uint8_t temp_err_set[ZPN_NATURAL_LOG_THROTTLE_MAX_ERROR_COUNT];
            memset(temp_err_set, 0 , sizeof(temp_err_set));
            char *copied_config_value = ZPN_STRDUP(config_value, config_value_length);
            char delimeter[] = ",";
            char *strtok_ptr = NULL;
            char *config_err_code = strtok_r(copied_config_value, delimeter, &strtok_ptr);
            ZPATH_MUTEX_LOCK(&(zpn_natural_log_throttle_err_codes_tbl_lock), __FILE__, __LINE__);
            while(config_err_code != NULL){
                if(is_error_code_empty(config_err_code) != ZPN_RESULT_ERR){
                    zpn_natural_log_throttle_err_set_update(temp_err_set, config_err_code);
                    config_err_code = strtok_r(NULL, delimeter, &strtok_ptr);
                }
            }
            ZPATH_MUTEX_UNLOCK(&(zpn_natural_log_throttle_err_codes_tbl_lock), __FILE__, __LINE__);
            for(int i=0; i < ZPN_NATURAL_LOG_THROTTLE_MAX_ERROR_COUNT; i++){
                c_state->natural_log.error_set[i] = temp_err_set[i];
            }
            ZPN_FREE(copied_config_value);
        }else{
            char *def_err = BRK_MT_SETUP_FAIL_SAML_EXPIRED;
            char def_err_code[64] = {'\0'};
            snprintf(def_err_code, sizeof(def_err_code), "%s", def_err);
            memset(c_state->natural_log.error_set, 0, sizeof(c_state->natural_log.error_set));
            ZPATH_MUTEX_LOCK(&(zpn_natural_log_throttle_err_codes_tbl_lock), __FILE__, __LINE__);
            zpn_natural_log_throttle_err_set_update(c_state->natural_log.error_set, def_err_code);
            ZPATH_MUTEX_UNLOCK(&(zpn_natural_log_throttle_err_codes_tbl_lock), __FILE__, __LINE__);
        }
        return;
    }
}

void zpn_natural_update_log_throttle_err_tbl(struct zpn_broker_client_fohh_state *c_state)
{
    if(c_state!=NULL && c_state->customer_gid){
        zpn_c_state_natural_log_lock(&(c_state->natural_log));
        if(c_state->natural_log.throttling_config_check_count %
            ZPN_NATURAL_LOG_THROTTLE_ERR_CODES_FILLUP_SKIP_COUNT == 0){
                c_state->natural_log.throttling_config_check_count = 0;
                c_state->natural_log.throttling_config_check_count++;
                zpn_natural_log_throttle_err_tbl_update(c_state);
        }else{
                c_state->natural_log.throttling_config_check_count++;
        }
        zpn_c_state_natural_log_unlock(&(c_state->natural_log));
    }
}

int natural_throttle_log_stats_copy_callback(void* cookie,
                                             int counter,
                                             void* structure_data)
{
    struct natural_transaction_throttle_logs_stats* out_data = NULL;
    struct natural_transaction_throttle_logs_stats *log_throttle_stats = NULL;
    int res = 0;
    ZPATH_MUTEX_LOCK(&(zpn_natural_log_throttle_stats_tbl_lock), __FILE__, __LINE__);
    char* key =(char*)cookie;
    log_throttle_stats = zhash_table_lookup(zpn_natural_log_throttle_stats_tbl,
                                            key,
                                            strlen(key),
                                            NULL);
    if(log_throttle_stats!=NULL){
        out_data = (struct natural_transaction_throttle_logs_stats *)structure_data;
        out_data->log_flushed_count = log_throttle_stats->log_flushed_count;
        out_data->total_skip_count = log_throttle_stats->total_skip_count;
        out_data->last_time_log_flushed = log_throttle_stats->last_time_log_flushed;
    }
    ZPATH_MUTEX_UNLOCK(&(zpn_natural_log_throttle_stats_tbl_lock), __FILE__, __LINE__);
    return res;
}

void zpn_natural_log_throttle_update_stats(int64_t count,
                                           int64_t flush_count,
                                           char* err,
                                           int64_t last_time_log_flushed)
{
    if(err == NULL){
        return;
    }
    char *stats_cookie = NULL;
    char key[128]={'\0'};
    snprintf(key, sizeof(key), "%s", err);
    int flag = 0;
    ZPATH_MUTEX_LOCK(&(zpn_natural_log_throttle_stats_tbl_lock), __FILE__, __LINE__);
    struct natural_transaction_throttle_logs_stats *log_throttle_stats = NULL;
    log_throttle_stats = zhash_table_lookup(zpn_natural_log_throttle_stats_tbl,
                                            err,
                                            strlen(err),
                                            NULL);
    if(log_throttle_stats == NULL){
        flag = 1;
        struct natural_transaction_throttle_logs_stats *stats = ZPN_CALLOC(sizeof(*stats));
        stats->total_skip_count = count;
        stats->log_flushed_count = flush_count;
        stats->last_time_log_flushed = last_time_log_flushed;
        stats->error_code = ZPN_STRDUP(err,strlen(err));
        stats_cookie = stats->error_code;
        zhash_table_store(zpn_natural_log_throttle_stats_tbl, key, strlen(key), 0, stats);
    }else{
        log_throttle_stats->total_skip_count += count;
        log_throttle_stats->log_flushed_count += flush_count;
        log_throttle_stats->last_time_log_flushed = last_time_log_flushed;
    }
    ZPATH_MUTEX_UNLOCK(&(zpn_natural_log_throttle_stats_tbl_lock), __FILE__, __LINE__);
    if(flag==1){
        struct natural_transaction_throttle_logs_stats *zi_stats = ZPN_CALLOC(sizeof(*zi_stats));
        char obj_name[256]={'\0'};
        snprintf(obj_name, sizeof(obj_name), "%s_throttle_stats", key);
        argo_log_register_structure(argo_log_get("statistics_log"),
                                    obj_name,
                                    AL_INFO,
                                    5*60*1000*1000, /* 5 mins interval */
                                    natural_transaction_throttle_logs_stats_description,
                                    zi_stats,
                                    1,
                                    natural_throttle_log_stats_copy_callback,
                                    stats_cookie);
    }
}

void natural_transaction_throttle_logs_fillup(struct natural_transaction_throttle_logs *log,
                                              struct zpn_broker_mtunnel *mtunnel)
{
    log->count = 0;

    if(log->prev_err != NULL){
        ZPN_FREE(log->prev_err);
        log->prev_err = NULL;
    }
    log->prev_err = ZPN_STRDUP(mtunnel->err, strlen(mtunnel->err));

    if(log->user_id != NULL){
        ZPN_FREE(log->user_id);
        log->user_id = NULL;
    }
    log->user_id = ZPN_STRDUP(mtunnel->user_id, strlen(mtunnel->user_id));

    if(log->stored_transaction_log != NULL){
        argo_object_release(log->stored_transaction_log);
        log->stored_transaction_log = NULL;
    }
    log->stored_transaction_log = argo_object_create(zpn_trans_log_description,&(mtunnel->log));

    log->customer_id = mtunnel->customer_id;
    log->is_flush_needed = 0;
    log->trans_log_capture_start_time = epoch_s();
}

int zpn_natural_log_throttle_check(struct zpn_broker_mtunnel *mtunnel, struct zpn_broker_client_fohh_state *c_state)
{
    if(c_state == NULL || mtunnel == NULL){
        return ZPN_NATURAL_SEND_TRANS_LOG;
    }
    char *err = mtunnel->err;
    int64_t customer_gid = mtunnel->customer_id;
    if(err != NULL && customer_gid && natural_log_throttle_enable)
    {
        ZPATH_MUTEX_LOCK(&(zpn_natural_log_throttle_err_codes_tbl_lock), __FILE__, __LINE__);
        int *err_code_index = zhash_table_lookup(zpn_natural_log_throttle_err_codes_tbl,
                                                 err,
                                                 strlen(err),
                                                 NULL);
        ZPATH_MUTEX_UNLOCK(&(zpn_natural_log_throttle_err_codes_tbl_lock), __FILE__, __LINE__);
        int value = ZPN_NATURAL_SEND_TRANS_LOG;
        zpn_c_state_natural_log_lock(&(c_state->natural_log));
        if(err_code_index != NULL && c_state->natural_log.error_set[*err_code_index] == 1)
        {
            char key[ZPN_NATURAL_LOG_THROTTLE_DATA_TBL_KEY_SIZE] = {'\0'};
            snprintf(key, sizeof(key), "%"PRId64"_%s_%"PRId32"_%u",
                     mtunnel->application_id,
                     mtunnel->log.domain,
                     mtunnel->log.s_port,
                     mtunnel->log.ip_proto);
            struct natural_transaction_throttle_logs *log = NULL;
            if(c_state->natural_log.err_code_data_tbl != NULL){
                log = zhash_table_lookup(c_state->natural_log.err_code_data_tbl,
                                         key,
                                         strlen(key),
                                         NULL);
            }else{
                ZPN_LOG(AL_NOTICE, "Throttling is waiting for c_state data table allocation.");
                value = ZPN_NATURAL_SEND_TRANS_LOG;
                goto end;
            }
            if(log == NULL){
                struct natural_transaction_throttle_logs *logs = ZPN_CALLOC(sizeof(*logs));
                natural_transaction_throttle_logs_fillup(logs,mtunnel);
                zhash_table_store(c_state->natural_log.err_code_data_tbl, key, strlen(key), 0, logs);
                value = ZPN_NATURAL_SEND_TRANS_LOG;
            }else{
                if(strcmp(log->prev_err, err) == 0){
                    log->count++;
                    log->is_flush_needed = 1;
                    argo_object_release(log->stored_transaction_log);
                    log->stored_transaction_log = argo_object_create(zpn_trans_log_description,
                                                                     &(mtunnel->log));
                    if(log->stored_transaction_log == NULL) {
                        value = ZPN_NATURAL_SEND_TRANS_LOG;
                        goto end;
                    }
                    int64_t trans_log_capture_curr_time = epoch_s();
                    int64_t trans_log_capture_time_diff = trans_log_capture_curr_time - log->trans_log_capture_start_time;
                    if(trans_log_capture_time_diff >= natural_log_throttle_threshold_time){
                        zpn_natural_throttle_logs_flush(log, "log_throttle_threshold_time_exceeded");
                    }
                    value = ZPN_NATURAL_DO_NOT_SEND_TRANS_LOG;
                }else{
                    if(log->is_flush_needed == 1){
                        zpn_natural_throttle_logs_flush(log, "log_throttle_err_code_changed");
                    }
                    natural_transaction_throttle_logs_fillup(log,mtunnel);
                    value = ZPN_NATURAL_SEND_TRANS_LOG;
                }
            }
        }
    end:
        zpn_c_state_natural_log_unlock(&(c_state->natural_log));
        return value;
    }
    return ZPN_NATURAL_SEND_TRANS_LOG;
}
