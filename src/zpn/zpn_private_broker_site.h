/*
 * zpn_private_broker_site.h. Copyright (C) 2024 Zscaler Inc, All Rights Reserved
 *
 */

#ifndef _ZPN_PRIVATE_BROKER_SITE_H_
#define _ZPN_PRIVATE_BROKER_SITE_H_

#include "zpn/zpn_private_broker_private.h"
#include "zpn/zpn_ddil_config.h"
#include "zpn/zpn_sitec_group.h"
#include "zpn/zpn_sitec_table.h"
#include "zpn/zpn_firedrill_site.h"

struct fohh_connection;
struct zpn_site_controller;
struct zpn_private_broker_load;


#define DEFINE_SITE_FLIP_TRIGGER(X) \
    X(SITE_FLIP_BY_INIT_DONE, "PSE is ready"), \
    X(SITE_FLIP_BY_SITE_AVAILABILITY, "site availability"), \
    X(SITE_FLIP_BY_COMMAND, "required by command"), \
    X(SITE_FLIP_BY_SITE_GID_ZERO, "PSE is detached from site"), \
    X(SITE_FLIP_BY_SITE_GID_CHANGED, "PS<PERSON> is attached to a new site"), \
    X(SITE_FLIP_BY_SITE_CONFIG_CHANGED, "ddil config is changed"), \
    X(SITE_FLIP_BY_FIREDRILL_CONFIG_CHANGED, "firedrill config is changed"), \

typedef enum site_flip_trigger_s {
#undef X
#define X(a, b)     a
    DEFINE_SITE_FLIP_TRIGGER(X)
} site_flip_trigger_t;

int zpn_private_broker_site_notify_cfg_site_gid(int64_t site_gid);
int zpn_private_broker_site_notify_cfg_site_config(int valid, int64_t reenroll_period, int sitec_preferred);
int zpn_private_broker_site_notify_cfg_ddil_config(struct zpn_ddil_config *ddil_config);
int zpn_private_broker_site_notify_cfg_sitec_config(struct zpn_site_controller *sitec);
int zpn_private_broker_site_notify_cfg_sitec_group_config(struct zpn_site_controller_group *sitec_group);
int zpn_private_broker_site_notify_pbctl_to_broker_status(int connected);
int zpn_private_broker_site_notify_pbctl_to_sitec_status(int connected);

void zpn_private_broker_site_xmit_pbinfo(struct zpn_private_broker_load *load, struct zpn_pbroker_status_report *pb_report);

int zpn_private_broker_site_register_fohh(struct fohh_connection* connection, const char* label,
        const char *remote_host_name, const char *sni_service_name, const char * sni_suffix,
        int stick_to_sitec, int current_to_sitec);
int zpn_private_broker_site_deregister_fohh(struct fohh_connection* connection);

int zpn_private_broker_site_init();
int zpn_private_broker_site_init_tasks(struct event_base *base);
int zpn_private_broker_site_start();

int zpn_private_broker_site_is_sitec_eligible(const char* offline_domain);
int zpn_private_broker_site_is_sitec_preferred();
int zpn_private_broker_site_sitec_is_reachable();
int zpn_private_broker_site_broker_is_reachable();
//void zpn_pbroker_update_firedrill_config_to_site_config_file(struct zpn_firedrill_config *firedrill_cfg);

int zpn_private_broker_handle_config_change(
        int site_is_active,                     // 0: inactive, 1: active, -1: ignore
        int sitec_preferred,                    // 0: prefer to broker, 1: prefer to sitec, -1: ignore
        const char* offline_domain,             // NULL or "": turn off offline mode, INVALID_OFFLINE_DOMAIN: ignore
        int64_t reenroll_period,                // -1: ignore
        int allow_c2site,                       // 0: disallow c2site, 1: allow c2site, -1: ignore
        int is_switchtime_enabled,              // -1: ignore
        int64_t max_allowed_downtime_s,         // -1: ignore
        int64_t max_allowed_switchtime_s,       // -1: ignore
        site_flip_trigger_t trigger,
        struct zpn_firedrill_config *firedrill_cfg);

#endif // _ZPN_PRIVATE_BROKER_SITE_H_
