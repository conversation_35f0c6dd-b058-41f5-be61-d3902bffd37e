/*
 * zpn_fohh_client_exporter.h. Copyright (C) 2017 Zscaler, Inc. All Rights Reserved.
 */

#ifndef _ZPN_FOHH_CLIENT_EXPORTER_H_
#define _ZPN_FOHH_CLIENT_EXPORTER_H_

#include <event2/event.h>
#include <sys/queue.h>
#include "zevent/zevent.h"
#include "zpath_misc/zpath_misc.h"
#include "zpn/zpn_rpc.h"
#include "zpn/zpn_connector_tun.h"
#include "zpn/zpn_fohh_client.h"

#define EXPORTER_USE_ZEVENT 1
#define EXPORTER_MAX_DBG_STR 1000

#define MT_GPROFILE_CLEANUP(mt, zfce, dbg_str, destroy_code)                                  \
    do {                                                                                      \
        ZPN_LOG(AL_ERROR, "%s: Cannot allocate memory for gprofiles config for mt", dbg_str); \
        ZPATH_MUTEX_LOCK(&(zfce->lock), __FILE__, __LINE__);                                  \
        EXPORTER_ZFCE_STATS_FIELD_INC(zfce->mt_destroy_count, 1);                             \
        ZPATH_MUTEX_UNLOCK(&(zfce->lock), __FILE__, __LINE__);                                \
        zpn_fohh_client_exporter_mt_destroy(mt, destroy_code);                                \
    } while (0)

struct zpn_fohh_client_exporter;

struct zfce_pending_proxy_req {
    TAILQ_ENTRY(zfce_pending_proxy_req)    queue_entry;
    struct zpn_fohh_client_exporter_proxy_req  *preq;
};
TAILQ_HEAD(zfce_pending_proxy_req_head, zfce_pending_proxy_req);

struct zpn_fohh_client_exporter_proxy_req {
    TAILQ_ENTRY(zpn_fohh_client_exporter_proxy_req)  queue_entry;
    char *dbg_str;
    unsigned char *proxy_buffer;
    int len;
    int conn_id;
    int port;
    int opcode;
    const char *auth_cookie;
    const char *assertion;
    int64_t customer_id;
};

struct zfce_pending_mt_req {
    TAILQ_ENTRY(zfce_pending_mt_req)    queue_entry;
    struct zpn_fohh_client_exporter_mt  *mt;
};

TAILQ_HEAD(zfce_pending_mt_req_head, zfce_pending_mt_req);

struct zfce_app_query {
    TAILQ_ENTRY(zfce_app_query)         queue_entry;
    int                                 query_id;
    char                                *host_name;
    int                                 proto;
    uint16_t                            port;
    int                                 tls;
    int64_t                             publish_gid;
    int                                 sent;
};

TAILQ_HEAD(zfce_app_query_head, zfce_app_query);

typedef int
(zfce_app_query_ack_callback_f)(int64_t query_id,
                                int64_t customer_id,
                                char *assertion_key,
                                char *host_name,
                                int proto,
                                uint16_t port,
                                int tls,
                                int64_t publish_gid,
                                const char *result,
                                const char *reason);

/******************************************************************************************
 *  Each instance of zpn_fohh_client_exporter is used by one user. These are analogous to
 *  an instance of a zapp. Currently I am using login name to identify an exporter, but it
 *  is easy to change to other identifier as login name is just a string.
 *
 *  Caller should not hold reference to this struture, just need to use it temporarily as a handle
 *  to get access to mtunnels.
 *
 *  The exporter holds two lists of mtunnels, one active, one idle. Active mtunnels are those
 *  currently used by the parser. Idle mtunnels are not used by the parser
 *
 *  An mtunnel will be terminated and freed if it sits on idle list for more than 10 seconds.
 *
 *  Currnetly it is designed to self destruct if no mtunnels is assocaited with it for 5 minuetes
 */

TAILQ_HEAD(zpn_fohh_client_exporter_mt_head_tailq, zpn_fohh_client_exporter_mt);

enum zpn_server_type {
  zpn_server_type_invalid=0,
  zpn_server_type_broker,
  zpn_server_type_exporter,
  zpn_server_type_total_count
};
struct zpn_fohh_client_exporter {

    int32_t destroy_count;

    zpath_mutex_t lock;

    struct zpn_fohh_client *zfc;
    enum zpn_server_type remote_server_type;

    enum zfc_status status;

    struct argo_hash_table *mtunnel_by_id;

    SSL_CTX *ssl_ctx_connector;

    // TODO: Following is unused, we can remove in next commit
    //SSL_CTX *ssl_ctx_connector_no_verify;
    int64_t ssl_ctx_ref_count;

    struct argo_hash_table *busy_mt;
    struct argo_hash_table *idle_mt;
    int32_t num_busy_mt;
    int32_t num_idle_mt;

    struct zpn_fohh_client_exporter_mt_head_tailq   idle_mt_list;

    struct zfce_pending_mt_req_head    pending_mt_reqs;
    struct zfce_pending_proxy_req_head pending_proxy_reqs;

    struct zfce_app_query_head         app_query_list;
    struct argo_hash_table             *query_by_id;

    /* Timer to monitor tcp connection */
    struct event *monitor_ev;

    int64_t idle_start_us;

    zfce_app_query_ack_callback_f      *app_query_ack_cb;


    /* Stats for debugging */

    /* Total mtunnels created */
    int64_t mt_create_count;

    /* Total mtunnels destroyed */
    int64_t mt_destroy_count;

    /* We'll hold the initial batch of requests until
     * redirect message is received, or timed out. */
    int32_t initial_redir_received;
    int32_t ignore_next_connection_drop;
    struct event *redir_timeout_ev;
};

/******************************************************************************************
 *  This will initialize the fohh_client_exporter module and will block until a connection
 *  to a the broker is successfully created or rejected (or timed out?).
 */
int zpn_fohh_client_exporter_init(int ot_mode);

struct zpn_fohh_client_exporter *zpn_fohh_client_exporter_get(const char *login_name);
struct zpn_fohh_client_exporter *zpn_fohh_client_proxy_exporter_get(const char *login_name);

struct zpn_fohh_client_exporter *zpn_fohh_client_exporter_create(const char *broker_name,
								 enum zpn_server_type remote_server_type,
                                                                 const char *cloud_root_pem_filename,
                                                                 const char *client_certificate_pem_filename,
                                                                 const char *client_key_pem_filename,
                                                                 const char *cloud_name,
                                                                 int64_t customer_id,
                                                                 const char *assertion_key,
                                                                 const char *assertion,
                                                                 struct argo_inet *public_ip,
                                                                 struct argo_inet *private_ip,
                                                                 int64_t pra_scope_gid,
                                                                 int is_pra_third_party_login,
                                                                 zfce_app_query_ack_callback_f *app_query_ack_cb);

int zpn_fohh_client_no_server_cert_verify(struct zpn_fohh_client_exporter *zfce);

int zpn_fohh_client_app_query(struct zpn_fohh_client_exporter *zfce,
                              int64_t query_id,
                              const char *hostname,
                              int proto,
                              uint16_t port,
                              int tls,
                              int64_t publish_gid);

/******************************************************************************************
 * zpn_fohh_client_exporter_mt
 *
 * When Exporter parser gets a request, it will request an mtunnel from Fohh_Client_Exporter
 * to send the request, and wait for the response. When the parser is done with the mtunnel,
 * it will release the mtunnel back to Fohh_Client_Exporter.
 *
 * Parser will never try to terminate an mtunnel, it will just release it. The Fohh_Client_Exporter
 * will eventually terminate and free it.
 *
 * While the Parse holds the mtunnel and waits for the response, the mtunnel can be termianted
 * due to remote FIN or FOHH connection to Broker going away. The mtunnel itself will be put in
 * free queue and its status (and incarnation) will change. Parser will be notified through
 * status callback or simply notice the incarnation (or status change). When this happens,
 *     - If the Parser hasn't sent any response back yet, it should release the mtunnel,
 *       request a new one and resend the request.  The Parser is responsible for how long
 *       to hold the request and how many retries to make before responding to the Browser with 404.
 *     - If the Parser has already sent partial response back, it should release the mtunnel,
 *       and termiante the connection to Browser immediately.
 *
 */

struct zpn_fohh_client_exporter_mt;

enum zpn_fohh_client_exporter_mt_status {
    zfce_mt_connecting,
    zfce_mt_connected,
    zfce_mt_remote_disconnect,
    zfce_mt_connect_error,
    zfce_mt_release_request,
    zfce_mt_request_error,
    zfce_mt_invalid
};

struct zpn_mconn_fohh_client_exporter {
    struct zpn_mconn mconn;

    int rx_paused;
    int tx_paused;

    int64_t rx_bytes;
    int64_t tx_bytes;
};

typedef int
(zfce_mt_status_callback_f)(struct zpn_fohh_client_exporter_mt *mt,
                            void *cookie_void,
                            int64_t cookie_int,
                            enum zpn_fohh_client_exporter_mt_status status,
                            const char *reason);

typedef int
(zfce_mt_consume_callback_f)(struct zpn_fohh_client_exporter_mt *mt,
                             struct evbuffer *buf,
                             size_t len,
                             void *cookie_void,
                             int64_t cookie_int);

typedef int
(zfce_mt_unblock_callback_f)(struct zpn_fohh_client_exporter_mt *mt,
                             void *cookie_void,
                             int64_t cookie_int);

typedef struct zpn_http_trans_log *
(zfce_mt_trans_log_callback_f)(void *cookie_void,
                               int64_t cookie_int);

#define EXPORTER_MAX_HOSTNAME_LEN             256

struct zfce_mt_key {
    char host_name[EXPORTER_MAX_HOSTNAME_LEN];
    int proto;
    uint16_t port;
    int tls;
    int verify_cert;
    int32_t exporter_conn_id;
};


struct zpn_fohh_client_exporter_mt {
    char dbg_str[EXPORTER_MAX_DBG_STR];

    zpath_mutex_t lock;
    int64_t incarnation;

    struct zpn_mconn_fohh_tlv              broker_mconn;
    struct zpn_mconn_fohh_client_exporter  parser_mconn;
    char *mtunnel_id;

    char *err;

    struct zpn_fohh_client_exporter *state;
    int32_t tag_id;

    struct zfce_mt_key  key;
    char *ja3hash;

    struct zpn_connector *connector;

    TAILQ_ENTRY(zpn_fohh_client_exporter_mt)  queue_entry;

    enum zpn_fohh_client_exporter_mt_status status;
    zfce_mt_status_callback_f *status_cb;
    zfce_mt_consume_callback_f *consume_cb;
    zfce_mt_unblock_callback_f *unblock_cb;
    zfce_mt_trans_log_callback_f *trans_log_cb;
    void *cookie_void;
    int64_t cookie_int;
    int64_t publish_gid;

    int busy;
    int64_t idle_start_us;
    int64_t free_start_us;

    int deleting;
    int request_released;     /* We cannot call exporter request anymore */
    int request_done;         /* Exporter tells us request is done with mt */
    int release_scheduled;

    /* Back-end verifications */
    unsigned verify_cert:1;
    unsigned verify_host:1;
    unsigned is_guac_mt:1;
    unsigned is_mt_upgraded:1;

    int32_t exporter_conn_id;

    char *console_user;

    enum zpn_console_credential_type console_cred_type;

    int64_t capabilities_policy_id;
    char    *file_transfer_list;

    int64_t cred_policy_id;
    char *guac_error_string;
    char *console_conn_type;
    int32_t is_pra_session;
    char *session_recording;
    char *pra_conn_id;
    int32_t is_guac_proxy_conn;
    char *shared_users_list;
    char *shared_mode;
    int  event_type;
    char *user_email;
    const struct zpn_managed_chrome_payload *gposture;
    struct argo_object *gposture_object;
    char *stepup_auth_level_id;
    char *credential_id;
    char *credential_pool_id;
    struct zpn_managed_browser_profiles *gprofiles;
};

const char *zfce_mt_status_string(enum zpn_fohh_client_exporter_mt_status status);
const char *console_credential_type_string(enum zpn_console_credential_type type);

struct zpn_fohh_client_exporter_mt *zpn_fohh_client_export_mt_get(const char *dbg_str,
                                                                  struct zpn_fohh_client_exporter *zfce,
                                                                  char *host_name,
                                                                  int proto,
                                                                  uint16_t port,
                                                                  int tls,
                                                                  zfce_mt_status_callback_f *status_cb,
                                                                  zfce_mt_consume_callback_f *consume_cb,
                                                                  zfce_mt_unblock_callback_f *unblock_cb,
                                                                  zfce_mt_trans_log_callback_f *trans_log_cb,
                                                                  void *cookie_void,
                                                                  int64_t cookie_int,
                                                                  int64_t publish_gid,
                                                                  int verify_cert,
                                                                  int32_t exporter_conn_id,
                                                                  int no_mt_reuse,
                                                                  int is_mt_upgraded,
                                                                  int is_guac_mt,
                                                                  int is_guac_proxy_conn,
                                                                  struct argo_object **gposture_object,
                                                                  struct zpn_managed_browser_profiles **gprofiles);

int zpn_fohh_client_export_mt_release(struct zpn_fohh_client_exporter_mt *mt, int http_transaction_error);

int zfce_mt_consume(struct zpn_fohh_client_exporter_mt *mt, struct evbuffer *buf, size_t len, int64_t incarnation);
int zfce_mt_unblock(struct zpn_fohh_client_exporter_mt *mt);

void zpn_fohh_client_exporter_send_exporter_log_data(struct zpn_fohh_client_exporter_mt *mt);

/* Global Exporter usage stats */
struct exporter_usage_stats {                           /* _ARGO: object_definition */

    /* Connector stats */
    int64_t exporter_connnector_create;                 /* _ARGO: integer */
    int64_t exporter_connnector_destroy;                /* _ARGO: integer */
    int64_t exporter_connnector_create_error;           /* _ARGO: integer */
    int64_t exporter_connnector_connect_error;          /* _ARGO: integer */

    /* SSL_CTX stats */
    int64_t exporter_ssl_ctx_create;                    /* _ARGO: integer */
    int64_t exporter_ssl_ctx_create_error;              /* _ARGO: integer */
    int64_t exporter_ssl_ctx_destroy;                   /* _ARGO: integer */

    /* Mtunnel create/destroy stats */
    int64_t exporter_mt;                                /* _ARGO: integer */
    int64_t exporter_mt_create;                         /* _ARGO: integer */
    int64_t exporter_mt_destroy;                        /* _ARGO: integer */

    /* Mtunnel connector slow free counters */
    int64_t exporter_mt_connector_sched_slow_free;      /* Scheduled(added) to slow free queue */   /* _ARGO: integer */
    int64_t exporter_mt_connector_skip_slow_free_queue; /* Skiped processing slow free entries */   /* _ARGO: integer */

    /* ZFCE connection stats */
    int64_t exporter_zfce_conn;                         /* _ARGO: integer */
    int64_t exporter_zfce_conn_create;                  /* _ARGO: integer */
    int64_t exporter_zfce_conn_create_error;            /* _ARGO: integer */
    int64_t exporter_zfce_conn_destroy;                 /* _ARGO: integer */
    int64_t exporter_zfce_conn_force_destroy;           /* _ARGO: integer */
    /* When this gets increased, ssl_ctx_connector is NULL in zpn_mtunnel_request_ack_cb.
     * Most likely zfce got destroyed before zpn_mtunnel_request_ack_cb happens */
    int64_t exporter_zfce_conn_destroy_before_cb;       /* _ARGO: integer */

    /* Fatal resource errors - Will trigger asserts */
    int64_t exporter_fatal_connector_socket_error;      /* _ARGO: integer */

    /* Google posture stats */
    int64_t exporter_posture_to_caa_redirect;           /* _ARGO: integer */
    int64_t exporter_posture_failed_gkey_from_caa;      /* _ARGO: integer */
    int64_t exporter_posture_not_found_in_object_store; /* _ARGO: integer */
    int64_t exporter_posture_failure_in_parsing;        /* _ARGO: integer */

    /* Step-up auth stats */
    int64_t exporter_redirect_to_authsp_levelid;        /* _ARGO: integer */
};


extern struct exporter_usage_stats g_exporter_usage;
extern int g_exporter_disable_hard_assert;
extern int g_exporter_fatal_errors_threshold;

/* Usage stats helper macros */
#define EXPORTER_GLOBAL_USAGE_STATS_FIELD_INC(field) __sync_add_and_fetch_8(&g_exporter_usage.field, 1)
#define EXPORTER_GLOBAL_USAGE_STATS_FIELD_DEC(field) __sync_sub_and_fetch_8(&g_exporter_usage.field, 1)
#define EXPORTER_GLOBAL_USAGE_STATS_FIELD_GET(field) (g_exporter_usage.field)
#define EXPORTER_GLOBAL_USAGE_STATS_FIELD_RESET(field) __sync_lock_release_8(&g_exporter_usage.field)

void zpn_fohh_client_exporter_send_exporter_proxy_data(struct zpn_fohh_client_exporter *zfce,
                                                       struct evbuffer *proxy_buffer, size_t len);
struct zpn_fohh_client_exporter_proxy_req *zpn_fohh_client_exporter_proxy_req_get(struct zpn_fohh_client_exporter *zfce,
                                                                                  unsigned char *proxy_buffer, int len,
                                                                                  int conn_id, int port, int op_code,
										                                          const char *auth_cookie,
										                                          const char *assertion,
										                                          int64_t customer_id);
void zpn_fohh_client_proxy_exporter_remove_by_id(struct zpn_fohh_client_exporter *exporter, const char *auth_token);
void zpn_exporter_pra_guac_proxy_data(struct zpn_fohh_client_exporter_mt *mt);
int is_remote_fin_propogation_enabled (int64_t customer_gid);
#endif /* _ZPN_FOHH_CLIENT_EXPORTER_H_ */
