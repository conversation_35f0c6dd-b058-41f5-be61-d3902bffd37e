/*
 * zpn_site.h. Copyright (C) 2024 Zscaler Inc, All Rights Reserved
 */

#ifndef _ZPN_SITE_H_
#define _ZPN_SITE_H_

#include "argo/argo.h"

#define ZPN_MAX_SITE 100

struct zpn_site {                              /* _ARGO: object_definition */
    /* Standard SQL fields. */
    int64_t sequence;                          /* _ARGO: integer, sequence */
    int32_t modified_time;                     /* _ARGO: integer */
    int32_t creation_time;                     /* _ARGO: integer */
    int64_t modifiedby_userid;                 /* _ARGO: integer */
    int64_t deleted;                           /* _ARGO: integer, deleted */

    /* Real fields */
    int64_t gid;                               /* _ARGO: integer, index, key */
    char *name;                                /* _ARGO: string */
    int8_t enabled;                            /* _ARGO: integer */
    char *description;                         /* _ARGO: string */
    int64_t customer_gid;                      /* _ARGO: integer, index */
    int64_t scope_gid;                         /* _ARGO: integer, index */
    int64_t reenroll_period;                   /* _ARGO: integer */
    char *offline_domain;                      /* _ARGO: string */
    int64_t *siem_gids;                        /* _ARGO: integer */
    int siem_gid_count;                        /* _ARGO: quiet, integer, count: siem_gids */
    int8_t firedrill_enabled;                  /* _ARGO: integer */
    int8_t sitec_preferred;                    /* _ARGO: integer */
};

extern struct argo_structure_description *zpn_site_description;

int zpn_site_init(struct wally *single_tenant_wally,
                  int64_t single_tenant_gid,
                  wally_row_callback_f *row_cb,
                  int single_tenant_fully_loaded,
                  int register_with_zpath_table);

int zpn_site_get_by_gid(int64_t gid,
                        struct zpn_site **site,
                        int register_on_miss,
                        wally_response_callback_f callback_f,
                        void *callback_cookie,
                        int64_t callback_id);

int zpn_site_get_by_customer_gid(int64_t customer_gid,
                                 struct zpn_site **site,
                                 size_t *row_count,
                                 wally_response_callback_f callback_f,
                                 void *callback_cookie,
                                 int64_t callback_id);
#endif /* _ZPN_SITE_H_ */
