/*
 * zpn_broker.h. Copyright (C) 2014 Zscaler, Inc. All Rights Reserved.
 */

#ifndef _ZPN_BROKER_H_
#define _ZPN_BROKER_H_

#include "zcdns/zcdns.h"
#include "ztimer/ztimer_wheel.h"
#include "fohh/fohh.h"

#include "zpn/zpn_rpc.h"
#include "zpn/zpn_broker_dispatch_pool.h"

struct zpn_broker;
struct zpath_debug_state;

extern int64_t broker_instance_id;
extern enum zpn_broker_status broker_status;
extern struct zpn_broker_termination_context g_broker_termination_ctx;

extern struct zcdns *broker_zcdns;
extern struct fohh_generic_server *sni_server;

extern const char * zpn_broker_get_status_string(uint16_t);
extern void zpn_broker_set_broker_status(enum zpn_broker_status status);
extern enum zpn_broker_status zpn_broker_get_broker_status(void);

enum zpn_firedrill_status {
    FIREDRILL_DISABLED = 0,
    FIREDRILL_ENABLED,
};

#define BROKER_CRYPTO_STORE_KEY_ID "ZPNCustomerCrypto"
#define BROKER_CERT_GEN_KEY_ID "ZPNCustomerCertGen"
#define KEY_BUFF_SIZE 32*1024
#define FILE_SIZE 1024
#define VERSION_LEN 32

extern int64_t ops_customer_gid;

extern int is_static_wally_enabled;

/*
 * The real transaction logs goes here.
 */
extern struct argo_log_collection *zpn_transaction_collection;
/*
 * All the RPC leading to the creation of a transaction goes here. There are the messages that will help create a
 * transaction. Most of the problems that we currently have is around broker, dispatcher & connector, so capturing
 * only those RPC at the moment.
 */
extern struct argo_log_collection *zpn_meta_transaction_collection;
extern struct argo_log_collection *zpn_health_collection;
extern struct argo_log_collection *zpn_auth_collection;
extern struct argo_log_collection *zpn_ast_auth_collection;
extern struct argo_log_collection *zpn_pb_auth_collection;
extern struct argo_log_collection *zpn_sc_auth_collection;
extern struct argo_log_collection *zpn_dns_collection;
extern struct argo_log_collection *zpn_auth_state_collection;
extern struct argo_log_collection *admin_probe_collection;
// ZIA IpSec and ZVPN Health for ODC/EAS feature
extern struct argo_log_collection *zia_health_collection;
extern struct argo_log_collection *zpn_np_collection;


extern struct ztimer_wheel *timer_wheel;

extern int64_t broker_alt_cloud_support_enabled;

struct zpn_broker_customer_counter_hash {
    zpath_rwlock_t lock;
    struct zhash_table *table;
};

struct zpn_broker_customer_counter_entry {
    uint64_t count;
    int64_t customer_gid;
};

#define ZPN_BROKER_RD_LOCK(lock)    ZPATH_RWLOCK_RDLOCK(&lock, __FILE__, __LINE__)
#define ZPN_BROKER_WR_LOCK(lock)    ZPATH_RWLOCK_WRLOCK(&lock, __FILE__, __LINE__)
#define ZPN_BROKER_UNLOCK(lock)     ZPATH_RWLOCK_UNLOCK(&lock, __FILE__, __LINE__)

extern struct zpn_svcp_stats zpn_svcp_chk_stats;
extern struct zpn_stepup_auth_stats zpn_stepup_chk_stats;

struct zpn_common_broker_cfg;

struct waf_cert_domain {
    struct fohh_connection *f_conn;
    int64_t retry_count;
    int64_t cert_id;
    char enc_pvt_key[KEY_BUFF_SIZE];
};

struct zpn_file_key {
    struct fohh_connection *f_conn;
    int fetch_retry_count;
    char enc_or_dec_pvt_key[KEY_BUFF_SIZE];
    char filename[FILE_SIZE];
    int is_update;
    char version[VERSION_LEN];
    char enc_or_dec_pvt_key2[KEY_BUFF_SIZE];
};

/* To Store the auto generated request context when request sent
 * to auto cert gen service. Later used to map libcrypto response or
 * failure handling */
struct waf_cert_gen_req {
    char        cn[256];
    int64_t     app_gid;
    int64_t     cust_gid;
    int         status;
    const char  *status_msg;
    struct fohh_connection *f_conn;
    int         retry_count;
};

/*
  Adaptive Load monitor configuration
  Stores the cpu, memory and proc_fd spike threshold to be monitored
*/
struct zpn_broker_adaptive_load_config {
    int64_t adaptive_load_feature_flag;     /* _ARGO: integer */
    int64_t cpu_spike_threshold;            /* _ARGO: integer */
    int64_t mem_spike_threshold;            /* _ARGO: integer */
    int64_t proc_fd_spike_threshold;        /* _ARGO: integer */
    int64_t report_interval;                /* _ARGO: integer */
};

/*
  Adaptive Load monitor runtime values
  Stores the runtime value of cpu, memory and proc_fd utilization and last report time
*/
struct zpn_broker_adaptive_load_data {
    int16_t cpu_util;                       /* _ARGO: integer */
    int16_t mem_util;                       /* _ARGO: integer */
    uint16_t proc_fd_util;                  /* _ARGO: integer */
    int64_t last_report_time;               /* _ARGO: integer */
};

extern struct zpn_broker_zpm_stats g_zpn_broker_zpm_conn_stats;

/*
 * Create a broker. Yes, this does about everything.
 */
struct zpn_broker *zpn_broker_init(int thread_count,
                                   struct zcdns *zcdns,
                                   struct zpn_common_broker_cfg *broker_cfg,
                                   const char *logfile_path,
                                   uint8_t dsp_channel_count,
                                   int use_itasca_logging_port_he);

struct zpn_broker *zpn_broker_init_standard(int thread_count,
                                   struct zcdns *zcdns,
                                   struct zpn_common_broker_cfg *broker_cfg,
                                   const char *logfile_path,
                                   uint8_t dsp_channel_count,
                                   int use_itasca_logging_port_he);
int zpn_broker_init_deferred();

/*
 * Used by pbroker to tell broker its control connection
 *
 * If too many of these configurations are needed, then we will have
 * to move private broker to be generically accessible by broker.
 */
void broker_set_pbroker_control(struct fohh_connection *f_conn);

/*
 * Call to generate a broker load message. This exists so that a
 * private broker can report load the moment it is connected to the
 * cloud and healthy. (Rather than waiting...)
 */
struct zpn_private_broker_load;
typedef void zpn_broker_xmit_pbinfo_callback(struct zpn_private_broker_load *load, struct zpn_pbroker_status_report *pb_report);
int zpn_broker_load_monitor(void);
int zpn_broker_private_broker_load_monitor(zpn_broker_xmit_pbinfo_callback *pbload_callback);
int zpn_broker_adhoc_load_monitor(void);
void zpn_broker_load_notify_status_change(enum zpn_broker_status);

void zpn_broker_exit_handler(enum zpath_termination_code tc, int sig, int self_thread_num);
void zpn_broker_heartbeat_exit_cb(int thread_number);
int zpn_broker_lib_exit_cb(enum zpath_termination_code tc, void *cookie, int self_thread_num);

struct fohh_generic_server *zpn_broker_get_sni_server();

extern int broker_autotune;
void zpn_broker_set_autotune(int bool_value);

void zpn_broker_zapp_idle_tunnel_burst_value_monitor();
int zpn_broker_zapp_get_idle_tunnel_burst_value();
int zpn_broker_is_dtls_enabled_on_broker();
int zpn_broker_is_dtls_enabled_for_customer(int64_t customer_gid);
int zpn_broker_is_dtls_enabled_for_client(int64_t client_gid, int64_t customer_gid);
int zpn_broker_is_dtls_enabled_for_app(int64_t app_gid, int64_t app_grp_gid, int64_t client_gid, int64_t customer_gid);
int zpn_broker_is_dns_assistant_check_unicast_enabled();
int zpn_broker_is_dtls_client_enabled_on_broker(int64_t customer_gid);
int zpn_broker_is_quickack_enabled_for_customer(int64_t customer_gid);
int zpn_broker_is_quickack_read_enabled_for_customer(int64_t customer_gid);
int zpn_broker_is_low_write_watermark_enabled_for_customer(int64_t customer_gid);
int zpn_broker_is_asst_zistats_upload_disabled_for_customer(int64_t customer_gid);
int zpn_broker_is_quickack_enabled_for_assistant(int64_t assistant_id, int64_t assistant_group_id, int64_t customer_gid);
int zpn_broker_is_quickack_read_enabled_for_assistant(int64_t assistant_id, int64_t assistant_group_id, int64_t customer_gid);
void zpn_broker_get_app_buffer_parameters();
int zpn_broker_is_app_buffer_tune_enabled_for_broker();
int zpn_broker_is_dev_environment();
int64_t zpn_broker_config_get_fohh_window();
int64_t zpn_broker_config_get_fohh_mconn_window();
int64_t zpn_broker_config_get_fohh_watermark();
int64_t zpn_broker_config_get_fohh_mconn_watermark();
int64_t zpn_broker_get_dns_check_response_delay_us(int64_t assistant_gid, int64_t assistant_group_id, int64_t customer_gid);

int64_t zpn_broker_is_waf_ptag_enabled(int64_t broker_gid, int64_t cst_id);
int64_t zpn_broker_is_waf_enabled(int64_t broker_gid, int64_t cst_id);
int zpn_broker_is_asst_databroker_resilience_enabled(int64_t customer_gid);

int zpn_broker_is_pbroker_zistats_upload_disabled_for_customer(int64_t customer_gid);
int zpn_broker_is_sitec_zistats_upload_disabled_for_customer(int64_t customer_gid);

struct zpn_broker_client_fohh_state *zpn_broker_find_connected_client_by_cname(const char *cname, int64_t* incarnation, int *thread_num);

int zpn_broker_update_c2c_app_registration_to_dispatcher(struct zpn_broker_dispatcher *dispatcher, struct fohh_connection *f_conn);

void zpn_broker_soft_assert_argo_reg_post_listen(int on);

#if 0
int zpn_broker_log_to_customer(struct argo_structure_description *description, void *structure_data, int64_t customer_gid);
#endif // 0

void zpn_broker_redirect_clients(int ops_force_restart, const char* reason);
void zpn_broker_redirect_clients_slow_drain(const char* reason);

int zpn_broker_pb_send_leg_report_to_combiner(char *mtunnel_id, uint64_t mtunnel_id_hash, int64_t incarnation, void *object, void *cached_tlv);

int zpn_broker_check_atleast_one_client_exists(int64_t customer_gid);

void dr_register_argo_objects();
int zpn_broker_dtls_mtu(int64_t assistant_id);

extern int64_t broker_mtr_flow_control_disabled;

int zpn_broker_socket_listen_client_init(void);
int zpn_broker_assistant_listen_socket(void);
int zpn_broker_client_listen_socket(int run_deferred);

void zpn_broker_verify_alt_cloud_info_callback(struct fohh_connection *f_conn,
                                              const char **apabilities,
                                              int capabilities_count,
                                              char *current_alt_cloud);
void zpn_broker_alt_cloud_sni_update(char *old_alt_cloud, char *new_alt_cloud);
void zpn_broker_get_cloud_name(char *cloud_name, size_t len);
char * zpn_broker_get_configured_alt_cloud(void);
char * zpn_broker_get_default_cloud_name(void);
char *zpn_broker_get_instance_full_name();
int zpn_broker_is_alt_cloud_configured(void);

/* customer list helper functions */
struct zpn_broker_customer_counter_hash* zpn_broker_customer_list_alloc(void);
void zpn_broker_customer_list_populate(struct zpn_broker_customer_counter_hash* customer_list);
void zpn_broker_customer_list_display(struct zpn_broker_customer_counter_hash* customer_list, void *cookie);
void zpn_broker_customer_list_free(struct zpn_broker_customer_counter_hash* customer_list);

/* SVCP init function */
int zpn_broker_svcp_init();

int zpn_broker_add_proxy_cb(void *argo_cookie_ptr,
                            void *argo_structure_cookie_ptr,
                            struct argo_object *object);
int zpn_broker_delete_proxy_cb(void *argo_cookie_ptr,
                            void *argo_structure_cookie_ptr,
                            struct argo_object *object);

void zpn_broker_zpm_incr_connect_count();
void zpn_broker_zpm_incr_disconnect_count();
void zpn_broker_zpm_incr_connect_attempt_count();
void zpn_broker_zpm_connect_attempt_count_reset();

int zpn_broker_mission_critical_cb(void *argo_cookie_ptr,
                                    void *argo_structure_cookie_ptr,
                                    struct argo_object *object);

enum zpn_broker_boot_stage {
    ZPN_BROKER_INITIAL_SETUP_STAGE_0,          // Stage 0: Command line parsing, early init
    ZPN_BROKER_APP_INIT_STAGE_1,               // Stage 1: Zpath app initialization
    ZPN_BROKER_ZTHREAD_INIT_STAGE_2,           // Stage 2: ZCDNS and thread init
    ZPN_BROKER_DB_INIT_STAGE_3,                // Stage 3: Database and tables init
    ZPN_BROKER_PRE_INIT_FEATURES_STAGE_4,      // Stage 4: Pre-broker initialization features
    ZPN_BROKER_CORE_INIT_STAGE_5,              // Stage 5: Core broker initialization
    ZPN_BROKER_POST_INIT_FEATURES_STAGE_6,     // Stage 6: Post-broker initialization features
    ZPN_BROKER_NETWORK_INIT_STAGE_7,           // Stage 7: Network and listen socket init
    ZPN_BROKER_BOOT_STAGES_MAX                 // Keep this last for array bounds
};

enum zpn_broker_stage_state {
    STAGE_START,
    STAGE_END
};

struct zpn_broker_bootup_stats_ctx {
    char *name;
    int64_t start_time;
    int64_t end_time;
};

extern struct zpn_broker_bootup_stats_ctx zpn_broker_global_bootup_stats[];
void zpn_broker_update_bootup_stage_stats(enum zpn_broker_boot_stage stage,
                                          enum zpn_broker_stage_state state);
int zpn_broker_dump_bootup_stats(struct zpath_debug_state *request_state,
                                 const char **query_values, int query_value_count,
                                 void *cookie);

int64_t zpn_fohh_flow_control_enhancement_enabled(int64_t customer_gid);
int zpn_pbroker_firedrill_start(int64_t firedrill_interval);
uint32_t zpn_broker_batched_mconn_window_updates_enabled(int64_t assistant_gid, int64_t assistant_group_id, int64_t customer_gid);
uint32_t zpn_broker_syn_app_rtt_enabled(int64_t assistant_gid, int64_t assistant_group_id, int64_t customer_gid);
uint32_t zpn_broker_pipeline_latency_trace_enabled(int64_t assistant_gid, int64_t assistant_group_id, int64_t customer_gid);
uint32_t zpn_broker_client_syn_app_rtt_enabled(int64_t customer_gid);
uint32_t zpn_broker_client_pipeline_latency_trace_enabled(int64_t customer_gid);
#endif /* _ZPN_BROKER_H_ */
