/*
 * zpn_private_broker_site_config.h. Copyright (C) 2024 Zscaler Inc, All Rights Reserved
 *
 */

#ifndef _ZPN_PRIVATE_BROKER_SITE_CONFIG_H_
#define _ZPN_PRIVATE_BROKER_SITE_CONFIG_H_

#include <stdint.h>
#include <stdio.h>

#include "zpn/zpn_lib.h"
#include "zpn/zpn_firedrill_site.h"

typedef struct zpn_pbroker_site_config_s {
    int  site_is_active;
    int  sitec_preferred;
    char offline_domain[ZPN_MAX_DOMAIN_NAME_LEN + 1];
    uint64_t reenroll_period;
    uint64_t max_allowed_downtime_s;
    struct zpn_firedrill_config firedrill_cfg;
} zpn_pbroker_site_config_t;

int zpn_private_broker_site_config_load(zpn_pbroker_site_config_t *config);
int zpn_private_broker_site_config_save(const zpn_pbroker_site_config_t *config);

#endif // _ZPN_PRIVATE_BROKER_SITE_H_
