/*
 * zpn_siem_tx.c. Copyright (C) 2017 Zscaler Inc. All Rights Reserved
 */

#include "argo/argo.h"
#include <sys/queue.h>
#include "fohh/fohh_private.h"
#include "zpath_misc/zpath_misc.h"
#include "zhash/zhash_table.h"
#include "zpath_lib/zpath_lib.h"
#include "zpath_lib/zpath_local.h"
#include "zpath_lib/zpath_debug.h"

#include "zpn/zpn_lib.h"
#include "zpn/zpn_siem.h"
#include "zpn/zpn_siem_tx.h"
#include "zpn/zpn_siem_rpc.h"
#include "zpath_lib/zpath_instance.h"
#include "zpath_lib/zpath_assert.h"
#include "zpn/zpn_broker_common.h"
#include "zpath_lib/zpath_config_override.h"
#include "zpath_lib/zpath_config_override_keys.h"
#include "zpath_lib/zpath_config_override_desc.h"


//Log watched stats every 5 minutes
#define STATS_FREQUENCY (60*5)
struct customer_siem_context;

struct tx_key {
    int64_t gid;
    /* Redundant field, since this gid is unique SIEM gid */
    //int64_t sequence;
};

struct tx_state {
    char *name;
    enum zpn_siem_type type; //log type serviced by this SIEM tx context
    struct tx_key key;
    struct fohh_connection *f_conn;

    int64_t connects;
    int64_t connect_us;

    struct zpn_siem_element_head objects;
    int64_t queue_count;
    int64_t queue_size;
    int64_t max_queue_count;
    int64_t max_queue_size;

    int64_t next_id_to_use;
    int64_t last_id_acked;
    int64_t max_id_transmitted;
    int limited_retransmit;

    /* The following will only have a value if the queue is blocked on
     * transmit */
    struct zpn_siem_element *next_to_transmit;

    struct zpath_mutex lock;

    int64_t objects_received;                /* total objs received via logging API */
    int64_t objects_filtered;                /* total objs filtered as per config */
    int64_t objects_transmitted;             /* total objs sent via FOHH */
    int64_t objects_retransmitted;           /* total objs re-sent via FOHH */
    int64_t objects_drained;                 /* total objs discarded due to SIEM disablement */
    int64_t objects_overruns;                /* total objs  dropped from queue due to queue constraints */
    int64_t objects_overruns_bytes;          /* total bytes dropped from queue due to queue constraints */
    int64_t objects_nomemory;                /* total objs alloc failures */
    int64_t false_overruns;                  /* total objs for which acks came in after obj deletion */
    int64_t ack_count;                       /* total ack count received from slogger */
    int64_t ack_bytes;                       /* total ack bytes received from slogger */

    int64_t blocked_at;                      /* time this context last got blocked; reset to 0 upon (re)connect */
    int64_t blocked_count;                   /* total object blocked since last transmit failure because of any reason */
    int64_t last_ack_seen_s;                 /* Last time when an ack was seen on this tx_context  */
    int     in_qfreeze_state;                /* If we are unable to get an ack for log transmits for a prolonged interval */
    int64_t num_qfreeze;                     /* Count of how many time we have frozen this TX queue state */
    struct customer_siem_context *c_context; /* Back pointer to customer context to whom this SIEM belongs */
    TAILQ_ENTRY(tx_state) customer_entry;
    TAILQ_ENTRY(tx_state) root_freeze_q_entry;
};
TAILQ_HEAD(tx_state_head, tx_state);

/* Per customer context for quick info */
struct customer_siem_context {
    struct zpath_mutex   lock;
    int64_t              customer_id;
    int                  tx_count[zpn_siem_type_max];
    int                  qfreeze_count[zpn_siem_type_max];
    int64_t              tx_overruns;
    int64_t              invalid_siem_gid;
    int64_t              fohh_qfreeze_duration_s;
    int64_t              fohh_qfreeze_max_queue_size;
    struct tx_state_head tx_states[zpn_siem_type_max];
};

/* Global 'root' level context for overall info */
struct siem_context {
    unsigned             siem_logging_disabled:1;
    unsigned             init_done:1;
    struct zhash_table   *customers;
    struct zpath_mutex   context_lock;
    int64_t              total_customers;
    int64_t              total_siem;
    int64_t              invalid_siem_gid;
    int64_t              missing_ts_context;
    int                  qfreeze_count[zpn_siem_type_max];
    struct tx_state_head qfreeze[zpn_siem_type_max];
    struct argo_log_registered_structure *r_stats;
};

struct local_args {
    int64_t gid;
    char *s;
    char *e;
    int special_processing;
    struct customer_siem_context **context_list;
    int64_t num_customers;
    int64_t counter;
};

struct customer_siem_stats {                  /* _ARGO: object_definition */
    int64_t stats_siem_objs_received;         /* _ARGO: integer */
    int64_t stats_siem_objs_unfiltered;       /* _ARGO: integer */
    int64_t stats_siem_overruns;              /* _ARGO: integer */
    int64_t stats_siem_overruns_bytes;        /* _ARGO: integer */
    int64_t stats_siem_acks_received;         /* _ARGO: integer */
    int64_t stats_siem_acks_bytes;            /* _ARGO: integer */
    int64_t stats_siem_types_mask;            /* _ARGO: integer */
    int64_t stats_siem_customers_count;       /* _ARGO: integer */
    int64_t stats_siem_count;                 /* _ARGO: integer */
    int64_t *stats_siem_qfreeze_gids;         /* _ARGO: integer */
    int64_t stats_siem_qfreeze_gids_c;        /* _ARGO: integer, quiet, count: stats_siem_qfreeze_gids */
    int64_t stats_siem_qfreeze_count;         /* _ARGO: integer */
};
#include "zpn/zpn_siem_tx_compiled_c.h"

static struct zhash_table *queues = NULL;
static struct zpath_mutex queues_lock;

static struct zhash_table *watch_list = NULL;
static struct zpath_mutex watch_list_lock;

static struct siem_context global_siem_context;

const static char *siem_type_to_str[] = ZPN_SIEM_TYPE_STR_LIST;
struct argo_structure_description *customer_siem_stats_description;
static int aggregate_siem_counters(void *callback_cookie, int counter, void *structure_data);

#define COMPARE_FIELD_VALUES(x,y,z) \
    if (x->z != y->z) return (x->z > y->z) ? -1 : 1

static void qfreeze_op_for_tx_state(struct tx_state *ts, int add)
{
    ZPATH_MUTEX_LOCK(&global_siem_context.context_lock, __FILE__, __LINE__);

    if (add) {
        TAILQ_INSERT_TAIL(&(global_siem_context.qfreeze[ts->type]), ts, root_freeze_q_entry);
        global_siem_context.qfreeze_count[ts->type]++;
        __sync_fetch_and_add_8(&ts->c_context->qfreeze_count[ts->type], 1);
    } else {
        TAILQ_REMOVE(&(global_siem_context.qfreeze[ts->type]), ts, root_freeze_q_entry);
        global_siem_context.qfreeze_count[ts->type]--;
        __sync_fetch_and_sub_8(&ts->c_context->qfreeze_count[ts->type], 1);
    }

    ZPATH_MUTEX_UNLOCK(&global_siem_context.context_lock, __FILE__, __LINE__);
}

static void element_remove(struct tx_state *ts, struct zpn_siem_element *element)
{
    /* Remove element. Remember to update next_to_transmit if it
     * is the one being removed */
    ZPN_DEBUG_SIEM_ALL("%s: Removing element %p", ts->name, element);
    if (ts->next_to_transmit == element) {
        ts->next_to_transmit = TAILQ_NEXT(element, list);
    }
    TAILQ_REMOVE(&(ts->objects), element, list);
    ts->queue_count--;
    ts->queue_size -= element->object->total_size;
    argo_object_release(element->object);
    SIEM_FREE(element);
}

/*
 * Note: Updates ts->next_to_transmit
 */
static int transmit(struct tx_state *ts, struct zpn_siem_element *element)
{
    int res;
    res = fohh_argo_serialize(ts->f_conn, zpn_siem_element_description, element, 0, fohh_queue_element_type_control);
    if (res) {
        ts->next_to_transmit = element;
        if (res == FOHH_RESULT_WOULD_BLOCK) {
            ZPN_DEBUG_SIEM("%s: Blocked at sequence %"PRId64, ts->name, element->id);
            if (ts->blocked_at == 0) {
                ts->blocked_at = epoch_us();
                ts->blocked_count = 1;
            } else {
                ts->blocked_count++;
            }
        } else {
            /* Disconnected? */
            ZPN_LOG(AL_ERROR, "%s: Error enqueueing object %"PRId64, ts->name, element->id);
        }
    } else {
        if (element->id > ts->max_id_transmitted) {
            ts->max_id_transmitted = element->id;
            ZPN_DEBUG_SIEM_ALL("%s: Transmit sequence %"PRId64" (first time)", ts->name, element->id);
        } else {
            ts->objects_retransmitted++;
            ZPN_DEBUG_SIEM_ALL("%s: Transmit sequence %"PRId64" (retransmit)", ts->name, element->id);
        }
        ts->objects_transmitted++;
        if (ts->next_to_transmit == element) {
            ts->next_to_transmit = TAILQ_NEXT(element, list);
        }
    }
    return res;
}

static int ack_callback(void *argo_cookie_ptr,
                        void *argo_structure_cookie_ptr,
                        struct argo_object *object)
{
    struct tx_state *ts = argo_structure_cookie_ptr;
    struct zpn_siem_element_ack *ack = object->base_structure_void;
    struct zpn_siem_element *element;
    static int once = 0;
    int dequeue_from_qfreeze = 0;
    int res = ZPATH_RESULT_NO_ERROR;
    int64_t time_now = epoch_s();

    ZPATH_MUTEX_LOCK(&(ts->lock), __FILE__, __LINE__);
    ZPN_DEBUG_SIEM_ALL("%s: Received ACK id = %"PRId64", last_id_acked = %"PRId64", next_id_to_use = %"PRId64, ts->name, ack->id, ts->last_id_acked, ts->next_id_to_use);

    if (ack->id <= ts->last_id_acked) {
        ZPN_LOG(AL_ERROR, "%s: Received repeat ack? Last id acked = %"PRId64", received ack id = %"PRId64, ts->name, ts->last_id_acked, ack->id);
    } else {
        ts->last_id_acked = ack->id;
    }

    /* we received an ack...turn off qfreeze_state if set */
    if (ts->in_qfreeze_state) {
        ts->in_qfreeze_state = 0;
        ts->max_queue_size = ZPN_SIEM_TX_Q_MAX_BYTES; //unfreeze the TX queue if we have recived an ack on this connection
        dequeue_from_qfreeze = 1;
        ZPN_LOG(AL_NOTICE, "[%s|%s|(%s)]: Restored connection towards SIEM server: ack received after %"PRId64"s (qfreeze interval is %"PRId64"s)",
                fohh_description(ts->f_conn), fohh_state(ts->f_conn), ts->name, time_now - ts->last_ack_seen_s, ts->c_context->fohh_qfreeze_duration_s);
    }

    ts->last_ack_seen_s = time_now;

    while ((element = TAILQ_FIRST(&(ts->objects)))) {
        ZPN_DEBUG_SIEM_ALL("%s: Checking element %"PRId64": %p", ts->name, element->id, element);
        if (element->id > ack->id) {
            if (!once)
            {
                ZPN_LOG(AL_ERROR, "%s: Received ack for frame (%"PRId64") already dropped from memory", ts->name, element->id);
                ts->false_overruns++;
            }
            break;
        }
        once = 1;
        ZPN_DEBUG_SIEM_ALL("%s: Remove sequence %"PRId64" because we received ack %"PRId64, ts->name, element->id, ack->id);
        ts->ack_count++;
        ts->ack_bytes += zpn_siem_element_obj_size(element);
        element_remove(ts, element);
    }

    /* We were in limited retransmit mode, since we got an ack (re)transmit queued objects*/
    if (ts->limited_retransmit) {
        ZPN_DEBUG_SIEM("%s: ack received from %s, now sending all objects", ts->name, fohh_peer_cn(ts->f_conn));
        ts->limited_retransmit = 0;

        element = TAILQ_FIRST(&(ts->objects));
        while (element) {
            res = transmit(ts, element); // Does accounting
            if (res) {
                if (res == FOHH_RESULT_WOULD_BLOCK) {
                    ZPN_DEBUG_SIEM("%s: Blocked", ts->name);
                    res = ZPATH_RESULT_NO_ERROR;;
                } else {
                    /* Disconnected? */
                    ZPN_LOG(AL_ERROR, "%s: Error enqueueing object", ts->name);
                }
                break;
            }
            element = TAILQ_NEXT(element, list);
        }
    }

    ZPATH_MUTEX_UNLOCK(&(ts->lock), __FILE__, __LINE__);

    if (dequeue_from_qfreeze) {
        qfreeze_op_for_tx_state(ts, 0);
    }

    return res;
}

static int connection_callback(struct fohh_connection *connection,
                               enum fohh_connection_state state,
                               void *cookie)
{
    struct tx_state *ts = cookie;
    struct zpn_siem_element *element;
    int res = ZPATH_RESULT_NO_ERROR;;

    ZPATH_MUTEX_LOCK(&(ts->lock), __FILE__, __LINE__);

    if (state == fohh_connection_connected) {
        char new_name[1024];
        char *tmp = NULL;
        int r;
        struct zpn_siem *siem;

        r = zpn_siem_get(ts->key.gid, &siem);
        if (r != ZPATH_RESULT_NO_ERROR) {
            ZPN_LOG(AL_ERROR, "Could not get SIEM for SIEM id %"PRId64, ts->key.gid);
            ZPATH_MUTEX_UNLOCK(&(ts->lock), __FILE__, __LINE__);
            return r;
        } else {
            snprintf(new_name, sizeof(new_name), "%s.%"PRId64":%s", siem->name, ts->key.gid, fohh_description(connection));
            tmp = ts->name;
            ts->name = SIEM_STRDUP(new_name, strlen(new_name));
            if (!ts->name) {
                ts->name = tmp;
            } else {
                if (tmp) {
                    SIEM_FREE(tmp);
                }
            }
        }

        ZPN_DEBUG_SIEM("%s: Connection callback, new state = %s",
                       ts->name,
                       fohh_connection_state_strings[state]);

        ts->connects++;
        ts->connect_us = epoch_us();

        if ((res = argo_register_structure(fohh_argo_get_rx(connection),
                                           zpn_siem_element_ack_description,
                                           ack_callback,
                                           ts))) {
            ZPN_LOG(AL_ERROR, "%s: Could not register for acknowledgement", ts->name);
            ZPATH_MUTEX_UNLOCK(&(ts->lock), __FILE__, __LINE__);
            return res;
        }

        /*
         * On the first connect event to the slogger transmit all the queued objects. For proper accounting,
         * we remember to what ID we had transmitted to.
         * Subsequent reconnects could be because of fohh connection going down when the slogger's mtunnel to
         * SIEM endpoint is flapping. This could be because unstable SIEMs. In such cases we want to avoid
         * unnecessary retransmissions. Send one object and wait for the ack before retransmitting all the
         * queued objects
         */
        element = TAILQ_FIRST(&(ts->objects));
        if (!element && ts->last_id_acked) {
            ZPN_LOG(AL_NOTICE, "%s: Nothing to retransmit on reconnect", ts->name);
            ZPATH_MUTEX_UNLOCK(&(ts->lock), __FILE__, __LINE__);
            return ZPATH_RESULT_NO_ERROR;
        }

        if (ts->connects > 1) {
            ts->limited_retransmit = 1;
            ZPN_DEBUG_SIEM("%s: Entering limited retransmit mode for %s", ts->name, fohh_peer_cn(ts->f_conn));
        }

        while (element) {
            res = transmit(ts, element); // Does accounting
            if (res) {
                if (res == FOHH_RESULT_WOULD_BLOCK) {
                    ZPN_DEBUG_SIEM("%s: Blocked", ts->name);
                    res = ZPATH_RESULT_NO_ERROR;;
                } else {
                    /* Disconnected? */
                    ZPN_LOG(AL_ERROR, "%s: Error enqueueing object", ts->name);
                }
                break;
            }
            if (ts->limited_retransmit) {
                break;
            }
            element = TAILQ_NEXT(element, list);
        }
    } else {
        ZPN_DEBUG_SIEM("%s: Connection callback, new state = %s",
                       ts->name,
                       fohh_connection_state_strings[state]);

        ts->next_to_transmit = TAILQ_FIRST(&(ts->objects));
    }

    ZPATH_MUTEX_UNLOCK(&(ts->lock), __FILE__, __LINE__);
    return ZPATH_RESULT_NO_ERROR;
}

static int unblock_callback(struct fohh_connection *connection,
                            enum fohh_queue_element_type element_type,
                            void *cookie)
{
    struct tx_state *ts = cookie;
    int res;

    ZPATH_MUTEX_LOCK(&(ts->lock), __FILE__, __LINE__);

    if (ts->blocked_at) {
        int64_t now = epoch_us();
        int64_t blocked_for = now - ts->blocked_at;
        ZPN_LOG(AL_INFO, "%s: Unblocked after %"PRId64" us and %"PRId64" attempts to send", ts->name, blocked_for, ts->blocked_count);
        ts->blocked_at = 0;
        ts->blocked_count = 0;
    }

    while (ts->next_to_transmit) {
        res = transmit(ts, ts->next_to_transmit);
        if (res) {
            break;
        }
    }
    ZPATH_MUTEX_UNLOCK(&(ts->lock), __FILE__, __LINE__);
    return FOHH_RESULT_NO_ERROR;
}

static struct tx_state *tx_state_get(int64_t gid, int64_t sequence __attribute__ ((unused)), struct zpn_siem *siem)
{
    struct tx_state *ts = NULL;
    struct tx_key key;
    struct customer_siem_context *c_context = NULL;
    int64_t customer_gid = 0;
    int res = ZPATH_RESULT_NO_ERROR;
    int created = 0;

    key.gid = gid;
    //key.sequence = sequence;

    ts = zhash_table_lookup(queues, &key, sizeof(key), NULL);
    if (!ts) {
        ZPATH_MUTEX_LOCK(&queues_lock, __FILE__, __LINE__);
        ts = zhash_table_lookup(queues, &key, sizeof(key), NULL);
        if (!ts) {
            ts = SIEM_CALLOC(sizeof(*ts));
            if (ts) {
                char domain[255];
                char name[255];
                snprintf(domain, sizeof(domain), "slogger.%s", ZPATH_LOCAL_CLOUD_NAME);
                snprintf(name, sizeof(name), "%s.%"PRId64, siem->name, gid);
                ts->key = key;
                TAILQ_INIT(&(ts->objects));
                ts->max_queue_count = ZPN_SIEM_TX_Q_MAX_OBJECTS;
                ts->max_queue_size = ZPN_SIEM_TX_Q_MAX_BYTES;
                ts->next_id_to_use = 1;
                ts->lock = ZPATH_MUTEX_INIT;
                ts->name = SIEM_STRDUP(name, strlen(name));
                ts->type = siem->siem_type;
                if (!ts->name) {
                    ZPN_LOG(AL_ERROR, "Memory");
                    SIEM_FREE(ts);
                    ts = NULL;
                    ZPATH_MUTEX_UNLOCK(&queues_lock, __FILE__, __LINE__);
                    return ts;
                } else {
                    /*
                     * Set stats name as NULL to prevent any fohh stats being generated for this client. We are
                     * disabling the stats as we don't see any use for it now
                     */
                    ts->f_conn = fohh_client_create_2(FOHH_WORKER_ZPN_SLOGGER,
                                                      NULL,
                                                      argo_serialize_binary,
                                                      fohh_connection_style_argo,
                                                      0,
                                                      ts,
                                                      connection_callback,
                                                      NULL,
                                                      unblock_callback,
                                                      NULL,
                                                      domain,
                                                      domain,
                                                      NULL,
                                                      htons(SIEM_PORT),
                                                      NULL,
                                                      1,
                                                      15,
                                                      1);
                    if (!ts->f_conn) {
                        ZPN_LOG(AL_ERROR, "Could not allocate siem %s:%"PRId64":%"PRId64, siem->name, gid, siem->sequence);
                        SIEM_FREE(ts->name);
                        SIEM_FREE(ts);
                        ts = NULL;
                        ZPATH_MUTEX_UNLOCK(&queues_lock, __FILE__, __LINE__);
                        return ts;
                    } else {
                        ZPN_DEBUG_SIEM("%s: SIEM created", siem->name);
                        fohh_set_sticky(ts->f_conn, 1);
                        //fohh_set_debug(ts->f_conn, 1);
                    }
                }
                /* Add to customer context (create it if non-existent) */
                customer_gid = ZPATH_GID_GET_CUSTOMER_GID(gid);

                ZPATH_MUTEX_LOCK(&global_siem_context.context_lock, __FILE__, __LINE__);
                c_context = zhash_table_lookup(global_siem_context.customers, &customer_gid, sizeof(customer_gid), NULL);
                if (!c_context) {
                    c_context = SIEM_CALLOC(sizeof(*c_context));
                    if (c_context) {
                        res = zhash_table_store(global_siem_context.customers, &customer_gid, sizeof(customer_gid), 0, c_context);
                        if (res == ZPN_RESULT_NO_ERROR) {
                            global_siem_context.total_customers++;
                            /* Created new c_context...init all log type tx queues */
                            for (int i = zpn_siem_type_invalid; i < zpn_siem_type_max; i++) {
                                TAILQ_INIT(&(c_context->tx_states[i]));
                            }
                            created = 1;
                        }
                    }
                }
                ZPATH_MUTEX_UNLOCK(&global_siem_context.context_lock, __FILE__, __LINE__);

                if (!c_context || res != ZPN_RESULT_NO_ERROR) {
                    /* Failure */
                    ZPN_LOG(AL_ERROR, "SIEM TX customer context %s fail", c_context ? "hash store" : "memory alloc");
                    if (c_context) SIEM_FREE(c_context);
                    SIEM_FREE(ts->name);
                    SIEM_FREE(ts);
                    ts = NULL;
                    ZPATH_MUTEX_UNLOCK(&queues_lock, __FILE__, __LINE__);
                    return ts;
                }

                res = zhash_table_store(queues, &(ts->key), sizeof(ts->key), 0, ts);
                if (res) {
                    ZPN_LOG(AL_ERROR, "Could not store siem %s:%"PRId64":%"PRId64, siem->name, gid, siem->sequence);
                    /* No need to remove customer_context as it can still track other SIEMs of that customer */
                    SIEM_FREE(ts->name);
                    SIEM_FREE(ts);
                    ts = NULL;
                } else {
                    /* Assign and store customer context for per customer SIEMs tracking */
                    ts->c_context = c_context;
                    __sync_fetch_and_add_8(&global_siem_context.total_siem, 1);
                    ZPATH_MUTEX_LOCK(&c_context->lock, __FILE__, __LINE__);
                    TAILQ_INSERT_TAIL(&(c_context->tx_states[siem->siem_type]), ts, customer_entry);
                    c_context->tx_count[siem->siem_type]++;
                    if (created) { /* First time init */
                        c_context->customer_id = customer_gid;
                        c_context->fohh_qfreeze_duration_s = BROKER_SIEM_QUEUE_SHRINKAGE_WAIT_TIME_INTERVAL_MIN;
                        c_context->fohh_qfreeze_max_queue_size = ZPN_SIEM_TX_FOHH_QFREEZE_Q_SIZE;
                    }
                    ZPATH_MUTEX_UNLOCK(&c_context->lock, __FILE__, __LINE__);
                }
            }
        }
        ZPATH_MUTEX_UNLOCK(&queues_lock, __FILE__, __LINE__);
    }
    return ts;
}

/*
 * Checks if the specified object should be logged to the specified
 * SIEM. (This is the filtering mechanism)
 *
 * returns ZPATH_RESULT_NO_ERROR if the object is to be logged. Otherwise it should not.
 */
int zpn_siem_check_filter(struct argo_object *object, struct zpn_siem *siem)
{
    char *search_field;
    char *object_type = argo_object_get_type(object);
    char *value = NULL;
    int res;
    int i;

    if (strcmp(siem->source_log_type, object_type) != 0) {
        /* FIXME: clean this up in the future
         * API uses zpn_ast_comprehensive_stats and itasca uses zpn_assistant_comprehensive_stats */
        if (strcmp(siem->source_log_type, "zpn_ast_comprehensive_stats") == 0
                            && strcmp(object_type, "zpn_assistant_comprehensive_stats") == 0) {
            /* this case should be considered as a matched case */
            return ZPATH_RESULT_NO_ERROR;
        }

        /* This isn't an error- we do the filtering on log type here,
         * not in the policy table. But we return an error to indicate
         * that the object is not to be logged. */
        ZPN_DEBUG_SIEM("%s: SIEM type %s is not same as object type %s", siem->name, siem->source_log_type, object_type);
        return ZPATH_RESULT_ERR;
    }
    if (strcmp(object_type, "zpn_trans_log") == 0) {
        search_field = "reason";
    } else if (strcmp(object_type, "zpn_auth_log") == 0) {
        search_field = "status";
    } else if (strcmp(object_type, "zpn_ast_auth_log") == 0) {
        search_field = "status";
    } else if (strcmp(object_type, "zpn_http_trans_log") == 0) {
        search_field = NULL;
    } else if (strcmp(object_type, "zpn_auth_state_log") == 0) {
        search_field = "status";
    } else if (strcmp(object_type, "zpn_ast_comprehensive_stats") == 0) {
        search_field = NULL;
    } else if (strcmp(object_type, "zpn_sys_auth_log") == 0) {
        search_field = "status";
    } else if (strcmp(object_type, "zpn_sitec_auth_log") == 0) {
        search_field = "status";
    } else if (strcmp(object_type,"zpn_waf_http_exchanges_log") == 0) {
        search_field = NULL;
    } else if (strcmp(object_type,"zpn_waf_http_api_exchanges_log") == 0) {
        search_field = NULL;
    } else if (strcmp(object_type,"zpn_app_inspection_log") == 0) {
        search_field = NULL;
    } else if (strcmp(object_type,"zpn_krb_inspection_log") == 0) {
        search_field = NULL;
    } else if (strcmp(object_type,"zpn_ldap_inspection_log") == 0) {
        search_field = NULL;
    } else if (strcmp(object_type,"zpn_smb_inspection_log") == 0) {
        search_field = NULL;
    } else if (strcmp(object_type,"zpn_pbroker_comprehensive_stats") == 0) {
        search_field = NULL;
    } else if (strcmp(object_type,"zpn_sitec_comprehensive_stats") == 0) {
        search_field = NULL;
    } else {
        ZPN_LOG(AL_ERROR, "Do not recognize log type %s", object_type);
        return ZPATH_RESULT_ERR;
    }

    if (search_field) {
        res = argo_object_read_string_by_column_name(object, search_field, &value);
        if (res) {
            ZPN_LOG(AL_ERROR, "%s: SIEM Did not find field value for field %s whithin %s", siem->name, search_field, object_type);
            return ZPATH_RESULT_ERR;
        }

        if (value) {
            for (i = 0; i < siem->filter_count; i++) {
                if (strcmp(value, siem->filter[i]) == 0) {
                    ZPN_DEBUG_SIEM_ALL("%s: SIEM Filtered %s object based on filter value %s", siem->name, object_type, search_field);
                    return ZPN_RESULT_SIEM_FILTERED;
                }
            }
        } else {
            ZPN_DEBUG_SIEM_ALL("%s: Reason field has no value", siem->name);
        }
    }
    return ZPATH_RESULT_NO_ERROR;
}

size_t zpn_siem_get_slogger_info_for_one( char* slogger_name_buf,
                                        size_t buf_size,
                                        int64_t siem_id,
                                        struct zpn_siem *siem,
                                        char * log_type, int last_entry)
{
    size_t buf_used = 0;
    struct tx_state *ts;
    char delim = ',';
    if(last_entry) {
        delim = ' ';
    }

    if ( siem && siem->enabled && !strcmp(siem->source_log_type,log_type)) {
        ts = tx_state_get(siem_id, siem->sequence, siem);
        if ( !ts ) {
			ZPN_DEBUG_SIEM( "Could not get TX state for SIEM id %"PRId64, siem_id);
            buf_used = snprintf(slogger_name_buf + strlen(slogger_name_buf), buf_size , "%"PRId64":Unavailable,",siem_id);
            return buf_used;
        }
        if ( !(ts->f_conn) ){
            ZPN_DEBUG_SIEM( "Could not get TX state for SIEM id %"PRId64, siem_id);
            buf_used = snprintf(slogger_name_buf + strlen(slogger_name_buf), buf_size , "%"PRId64":Unavailable,",siem_id);
            return buf_used;
        }
        if ( ts->f_conn->have_peer_common_name ) {
            buf_used = snprintf(slogger_name_buf + strlen(slogger_name_buf), buf_size , "%"PRId64":%s%c",siem_id,ts->f_conn->peer_common_name,delim );
        } else {
            buf_used = snprintf(slogger_name_buf + strlen(slogger_name_buf), buf_size, "%"PRId64":%s%c",siem_id,ts->f_conn->remote_address_str,delim );
        }

    }
    return buf_used;
}

void zpn_siem_get_slogger_info(char* slogger_name_buf,
                                size_t buf_size,
                                int64_t* siem_ids,
                                size_t siem_ids_count,
                                char *log_type)
{
    int res = 0;
    size_t total_buf_used = 0;
    size_t buf_used = 0;
    struct zpn_siem *siem;

    if (disable_siem_logging_global) {
        return;
    }
    for (int i = 0; i < siem_ids_count; i++) {
        res = zpn_siem_get(siem_ids[i], &siem);
        if (res != ZPATH_RESULT_NO_ERROR) {
            ZPN_LOG(AL_ERROR, "Could not get SIEM for SIEM id %"PRId64, siem_ids[i]);
            continue;
        }
        buf_used = zpn_siem_get_slogger_info_for_one(slogger_name_buf,( buf_size - total_buf_used ),siem_ids[i],siem, log_type, (i == siem_ids_count - 1));
        if ( buf_used > ( buf_size - total_buf_used)) {
            ZPN_LOG(AL_CRITICAL,"Buffer size not enough for siem %s for customer %"PRId64, siem->name , siem->customer_gid );
            return;
        } else {
            total_buf_used += buf_used ;
        }

    }
    return;
}

static int64_t zpn_get_slogger_queue_shrinkage_wait_time(int64_t customer_gid)
{
    int64_t config_value = BROKER_SIEM_QUEUE_SHRINKAGE_WAIT_TIME_INTERVAL_MIN; /* Disabled if not broker */
    if (ZPN_INSTANCE_IS_BROKER() && ZPN_BROKER_IS_PUBLIC()) {
        int64_t root_customer_gid = ZPATH_GID_GET_CUSTOMER_GID(zpath_instance_global_state.current_config->gid);
        config_value = zpath_config_override_get_config_int(BROKER_FEATURE_SIEM_QUEUE_SHRINKAGE,
                                                          &config_value,
                                                          BROKER_SIEM_QUEUE_SHRINKAGE_WAIT_TIME_INTERVAL_DEFAULT,
                                                          customer_gid,
                                                          zpath_instance_global_state.current_config->gid,
                                                          root_customer_gid,
                                                          (int64_t) ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                          (int64_t) 0 );
    }

    return config_value;
}

int zpn_siem_log(int64_t gid, struct argo_object *object)
{
    struct zpn_siem *siem;
    struct tx_state *ts;
    struct zpn_siem_element *element;
    int res = ZPATH_RESULT_NO_ERROR;
    int overruns = 0;
    int check_for_qfreeze = 0;
    int enqueue_on_qfreeze = 0;
    int64_t overruns_bytes = 0;

    // from -disable_siem_logging broker command line option
    if (disable_siem_logging_global) {
        global_siem_context.siem_logging_disabled = 1;
        return res;
    }

    /* Get SIEM */
    res = zpn_siem_get(gid, &siem);
    if (res != ZPATH_RESULT_NO_ERROR) {
        if (global_siem_context.init_done) {
            int64_t customer_gid = ZPATH_GID_GET_CUSTOMER_GID(gid);
            struct customer_siem_context *c_context = zhash_table_lookup(global_siem_context.customers, &customer_gid, sizeof(customer_gid), NULL);
            if (c_context) __sync_fetch_and_add_8(&c_context->invalid_siem_gid, 1);
        }
        __sync_fetch_and_add_8(&global_siem_context.invalid_siem_gid, 1);
        ZPN_LOG(AL_ERROR, "Could not get SIEM for SIEM id %"PRId64, gid);
        return res;
    }

    // If siem is DISABLED or siem logging is disabled
    if(!siem->enabled || disable_siem_logging_debug) {
        ZPN_DEBUG_SIEM("SIEM [%"PRId64":%s] is disabled", siem->gid, siem->name);
        /* Get Transmit state. This initializes the transmit state fully */
        int elem_count = 0;
		ts = tx_state_get(gid, siem->sequence, siem);
		if (!ts) {
			ZPN_LOG(AL_ERROR, "Could not get TX state for SIEM id %"PRId64, gid);
            __sync_fetch_and_add_8(&global_siem_context.missing_ts_context, 1);
			return ZPATH_RESULT_NO_ERROR;
		}
		ZPATH_MUTEX_LOCK(&(ts->lock), __FILE__, __LINE__);
        ts->objects_received++;
		while (!TAILQ_EMPTY(&ts->objects)) {
			element = TAILQ_FIRST(&ts->objects);
			element_remove(ts, element);
			elem_count ++;
		}
		ZPN_DEBUG_SIEM("Draining the queues...[%d] elements removed", elem_count);
        ts->objects_drained += elem_count;
		ZPATH_MUTEX_UNLOCK(&(ts->lock), __FILE__, __LINE__);
		return ZPATH_RESULT_NO_ERROR;
    }

    /* Filter log */
    res = zpn_siem_check_filter(object, siem);
    if (res) {
        if (res == ZPN_RESULT_SIEM_FILTERED) {
            struct tx_key key = { .gid = siem->gid };
            struct tx_state *ts = zhash_table_lookup(queues, &key, sizeof(key), NULL);
            if (ts) __sync_fetch_and_add_8(&ts->objects_filtered, 1); //accounting
        }
        return ZPATH_RESULT_NO_ERROR;
    }

    /* Get Transmit state. This initializes the transmit state fully */
    ts = tx_state_get(gid, siem->sequence, siem);
    if (!ts) {
        ZPN_LOG(AL_ERROR, "Could not get TX state for SIEM id %"PRId64, gid);
        __sync_fetch_and_add_8(&global_siem_context.missing_ts_context, 1);
        return res;
    }

    __sync_fetch_and_add_8(&ts->objects_received, 1); //accounting

    /*
     * Enqueue:
     * 1. Ensure there is room.
     * 2. Enqueue.
     */
    /* Remove oldest until we have enough space. (or the queue is empty) */
    ZPATH_MUTEX_LOCK(&(ts->lock), __FILE__, __LINE__);
    while (((element = TAILQ_FIRST(&ts->objects))) &&
           ((ts->queue_count >= ts->max_queue_count) ||
            ((ts->queue_size + object->total_size) > ts->max_queue_size))) {
        ZPN_DEBUG_SIEM("%s: Overrun, removing element %"PRId64, ts->name, element->id);
        overruns++;
        overruns_bytes += zpn_siem_element_obj_size(element);
        element_remove(ts, element);
    }
    if (overruns) {
        ts->objects_overruns += overruns;
        ts->objects_overruns_bytes += overruns_bytes;
        ZPN_DEBUG_SIEM("%s: Removed %d elements due to overrun", ts->name, overruns);
    }

    /* Allocate and enqueue new element */
    element = SIEM_CALLOC(sizeof(*element));
    if (!element) {
        ts->objects_nomemory++;
        ZPATH_MUTEX_UNLOCK(&(ts->lock), __FILE__, __LINE__);
        ZPN_LOG(AL_ERROR, "alloc");
        return ZPATH_RESULT_NO_MEMORY;
    }
    element->object = object;
    argo_object_hold(element->object);
    element->id = ts->next_id_to_use;
    ts->next_id_to_use++;
    element->siem_gid = gid;
    TAILQ_INSERT_TAIL(&(ts->objects), element, list);

    ts->queue_count++;
    ts->queue_size += element->object->total_size;

    /* If we aren't tx blocked we can send immediately */
    ZPN_DEBUG_SIEM_ALL("%s: Log element: %"PRId64": %p", ts->name, element->id, element);
    if (!ts->next_to_transmit) {
        ZPN_DEBUG_SIEM_ALL("%s: Transmit immediately %"PRId64": %p", ts->name, element->id, element);
        res = transmit(ts, element);
        if (res) {
            check_for_qfreeze = 1;
        }
    } else {
        check_for_qfreeze = 1;
    }

    if (check_for_qfreeze && ts->in_qfreeze_state == 0) {
        /* Accounting if transmission fails due to FOHH not connected (qfreeze) state */
        int64_t value = zpn_get_slogger_queue_shrinkage_wait_time(ZPATH_GID_GET_CUSTOMER_GID(ts->c_context->customer_id));
        if (value) {
            ts->c_context->fohh_qfreeze_duration_s = value;

            int64_t time_since_last_ack_s = epoch_s() - ts->last_ack_seen_s;

            /* Do we have something waiting on the transmit queue AND failed to see an ack within qfreeze interval? */
            if (ts->queue_count && time_since_last_ack_s > ts->c_context->fohh_qfreeze_duration_s) {
                /* Set qfreeze state queue size and other quick info identifiers */
                ts->in_qfreeze_state = 1;
                ts->max_queue_size = ts->c_context->fohh_qfreeze_max_queue_size;
                ts->num_qfreeze++;
                enqueue_on_qfreeze = 1;

                ZPN_LOG(AL_NOTICE, "[%s|%s|(%s)]: Throttling connection towards SIEM server: Q size is %"PRId64
                        " bytes, configured qfreeze is %"PRId64"seconds, last ack seen was %"PRId64" seconds ago",
                        fohh_description(ts->f_conn), fohh_state(ts->f_conn), ts->name,
                        ts->queue_size, ts->c_context->fohh_qfreeze_duration_s, time_since_last_ack_s);
            }
        } else {
            /* Set to 0, which is to be interpreted as disable shrinking/freezing of TX queues
             * Note: This should not affect an already frozen Q because:
             * 1) Logs are not reaching SIEM anyway, so its just wastage to use more mem to hold logs
             * 2) We enqueue on the global frozen queue when in_q_freeze_state is 0, and dequeue when non-zero;
             *    Trying to dequeue here can cause illegal queue op due to thread race condition ( i.e., dequeue
             *    on a non-enqueued node or enqueue on an already enqueued node)
             *    So a frozen Q MUST and WILL wait for an ack before unfreezing itself
             *
             * No-op
             */
        }
    }

    ZPATH_MUTEX_UNLOCK(&(ts->lock), __FILE__, __LINE__);

    if (enqueue_on_qfreeze) {
        qfreeze_op_for_tx_state(ts, 1);
    }

    return ZPATH_RESULT_NO_ERROR;
}

static int siem_cmptr(const void *tx_a, const void *tx_b)
{
    const struct tx_state *a = *((const struct tx_state **)tx_a);
    const struct tx_state *b = *((const struct tx_state **)tx_b);

    if (a == b) return 0;
    if (a && !b) return -1;
    if (!a && b) return 1;

    COMPARE_FIELD_VALUES(a, b, num_qfreeze);
    COMPARE_FIELD_VALUES(a, b, objects_overruns_bytes);
    COMPARE_FIELD_VALUES(a, b, last_ack_seen_s);
    COMPARE_FIELD_VALUES(a, b, objects_received);

    return 0; /* Equivalent */
}

static const char *debug_str = "LogType,MaxQ,MaxB,Elements,Bytes,MaxIDtransmitted,LastAckID,TotTx,TotalAck,TotReTx,TotOverrun,"
                               "Totdrained,TotFalseOverrun,TotRecv,TotFiltered,TotNoMemory,Connects,Blocks,LastConnect,LastDisconnect,BlockedSince,QFreezeWaitTime,LastAck,IsQFrozen,SIEMName,Desc\n";

static size_t debug_out(char *s, char *e, const struct tx_state *ts)
{
    const char *orig = s;
    int64_t time_now = epoch_s();
    s += sxprintf(s, e, "%s,%"PRId64",%"PRId64",%"PRId64",%"PRId64",%"PRId64",%"PRId64",%" PRId64",%"PRId64",%"PRId64",%"PRId64
                           ",%"PRId64",%"PRId64",%"PRId64",%"PRId64",%"PRId64",%"PRId64",%"PRId64",-%fs,-%"PRId64",%"PRId64",%"PRId64"s,%"PRId64"s,%s,%s,%s\n",
                           siem_type_to_str[ts->type], ts->max_queue_count, ts->max_queue_size, ts->queue_count, ts->queue_size, ts->max_id_transmitted, ts->last_id_acked,
                           ts->objects_transmitted, ts->ack_count, ts->objects_retransmitted, ts->objects_overruns, ts->objects_drained, ts->false_overruns, ts->objects_received,
                           ts->objects_filtered, ts->objects_nomemory, ts->connects, ts->blocked_count, ((double)(time_now * 1000000) - ts->connect_us) / 1000000.0,
                           fohh_conn_get_current_disconnect_duration_s(ts->f_conn), !ts->blocked_at ? -1 : time_now - ts->blocked_at, ts->c_context->fohh_qfreeze_duration_s,
                           (!ts->objects_received || !ts->last_ack_seen_s) ? -1 : time_now - ts->last_ack_seen_s, ts->in_qfreeze_state ? "Y" : "N", ts->name ? ts->name : "NoName", fohh_state(ts->f_conn));
    return s - orig;
}

static int walk_f(void *cookie, void *object, void *key, size_t key_len)
{
    struct tx_state *ts = object;
    struct local_args *args = cookie;

    /* If no space, skip. (just faster..) */
    if (args->s == args->e) return 0;

    /* If not the GID we're looking for, skip. */
    if (args->gid && (args->gid != ts->key.gid)) return 0;

    args->s += debug_out(args->s, args->e, ts);

    return 0;
}

static int walk_cust(void *cookie, void *object, void *key, size_t key_len)
{
    const struct customer_siem_context *s = object;
    struct local_args *args = cookie;
    args->s += sxprintf(args->s, args->e, "%"PRId64"|%"PRId64"|%"PRId64"|%"PRId64":\n",
                                            s->customer_id, s->fohh_qfreeze_duration_s, s->fohh_qfreeze_max_queue_size, s->invalid_siem_gid);
    for (int i = zpn_siem_type_invalid + 1; i < zpn_siem_type_max; i++) {
        if (s->tx_count[i])
            args->s += sxprintf(args->s, args->e, "\t[%s|%d|%d]\n",
                                        siem_type_to_str[i], s->tx_count[i], s->qfreeze_count[i]);
    }
    return 0;
}

int zpn_siem_debug(int64_t gid, char *out_buf, size_t out_buf_size, int option)
{
    char *s, *e;
    struct local_args args;
    int64_t walk_key = 0;
    void *entry = NULL;
    struct tx_key key;
    struct tx_state *ts = NULL;
    struct customer_siem_context *c_context = NULL;
    struct customer_siem_stats siem_stats = {0};

    s = out_buf;
    e = s + out_buf_size;

    key.gid = gid;

    memset(&args, 0, sizeof(struct local_args));
    args.s = s;
    args.e = e;
    args.gid = gid;

    if (!queues || !global_siem_context.init_done) {
        snprintf(out_buf, out_buf_size, "Queues not initialized\n");
        return ZPATH_RESULT_NO_ERROR;
    }

    /* Specific query */
    if (option == 1) { //Summary of all customers in this system
        if (!global_siem_context.customers) return ZPATH_RESULT_NO_ERROR;
        int qfreeze = 0;
        for (int i = zpn_siem_type_invalid + 1; i < zpn_siem_type_max; i++) qfreeze += global_siem_context.qfreeze_count[i];
        s += sxprintf(s, e, "Customers = %"PRId64"\nTotal SIEM contexts = %"PRId64"\nSIEM contexts frozen = %d\n",
                             global_siem_context.total_customers, global_siem_context.total_siem, qfreeze);
        s += sxprintf(s, e, "CustomerID|qfreeze_time_duration_s|qfreeze_queue_size_bytes|Frozen_SIEMs_noticed ==> [Logtype|NumSIEMsConfigured|NumSIEMsFrozen]\n");
        args.s = s;
        ZPATH_MUTEX_LOCK(&global_siem_context.context_lock, __FILE__, __LINE__);
        zhash_table_walk(global_siem_context.customers, &walk_key, walk_cust, &args);
        ZPATH_MUTEX_UNLOCK(&global_siem_context.context_lock, __FILE__, __LINE__);
    } else if (option == 2) { //JSON stats dump
        args.special_processing = 1;
        aggregate_siem_counters(&args, 0, &siem_stats);
    } else if (option == 3) { //Dump only SIEMs in qfreeze state
        int qfreeze_count = 0;
        int j = 0;
        struct tx_state **states_arr = NULL;
        ZPATH_MUTEX_LOCK(&global_siem_context.context_lock, __FILE__, __LINE__);
        for (int i = zpn_siem_type_invalid + 1; i < zpn_siem_type_max; i++) {
            qfreeze_count += global_siem_context.qfreeze_count[i];
        }
        if (qfreeze_count) {
            states_arr = ZPN_CALLOC(qfreeze_count * sizeof(struct tx_state *));
            if (!states_arr) {
                ZPATH_MUTEX_UNLOCK(&global_siem_context.context_lock, __FILE__, __LINE__);
            } else {
                for (int i = zpn_siem_type_invalid + 1; i < zpn_siem_type_max && j < qfreeze_count; i++) {
                    TAILQ_FOREACH(ts, &(global_siem_context.qfreeze[i]), root_freeze_q_entry) {
                        states_arr[j++] = ts;
                    }
                }
                ZPATH_MUTEX_UNLOCK(&global_siem_context.context_lock, __FILE__, __LINE__);

                qsort(states_arr, qfreeze_count, sizeof(struct tx_state *), siem_cmptr);
                for (j = 0; j < qfreeze_count; j++) {
                    args.gid = states_arr[j]->key.gid;
                    walk_f(&args, states_arr[j], NULL, 0);
                }
                ZPN_FREE(states_arr);
            }
        } else {
            ZPATH_MUTEX_UNLOCK(&global_siem_context.context_lock, __FILE__, __LINE__);
        }
    } else {
        /* Generic query */
        if (gid == 0) {
            /* Walk all tx contexts.. */
            s += sxprintf(s, e, "%s", debug_str);
            args.s = s;
            ZPATH_MUTEX_LOCK(&(queues_lock), __FILE__, __LINE__);
            zhash_table_walk(queues, &walk_key, walk_f, &args);
            ZPATH_MUTEX_UNLOCK(&(queues_lock), __FILE__, __LINE__);
        } else if ((entry = zhash_table_lookup(queues, &key, sizeof(struct tx_key), NULL)) != NULL) {
            /* Dump all tx context for particular SIEM GID */
            s += sxprintf(s, e, "%s", debug_str);
            args.s = s;
            walk_f(&args, entry, NULL, 0);
        } else if ((entry = zhash_table_lookup(global_siem_context.customers, &gid, sizeof(gid), NULL)) != NULL) {
            /* Customer id based: Walk cusotmer context and dump all SIEMs related to it..*/
            c_context = entry;
            s += sxprintf(s, e, "%s", debug_str);
            s += sxprintf(s, e, "Customer data for %"PRId64":\n", gid);
            args.s = s;
            ZPATH_MUTEX_LOCK(&c_context->lock, __FILE__, __LINE__);
            for (int i = zpn_siem_type_invalid + 1; i < zpn_siem_type_max; i++) {
                TAILQ_FOREACH(ts, &(c_context->tx_states[i]), customer_entry) {
                    args.gid = ts->key.gid;
                    walk_f(&args, ts, NULL, 0);
                }
            }
            ZPATH_MUTEX_UNLOCK(&c_context->lock, __FILE__, __LINE__);
        } else {
            /* Not found */
            snprintf(out_buf, out_buf_size, "GID does not correspond to known customer context or SIEM\n");
            return ZPATH_RESULT_NO_ERROR;
        }
    }

    if (args.s == s) {
        s += sxprintf(s, e, "SIEM not found\n");
    }

    return ZPATH_RESULT_NO_ERROR;
}


static int walk_watched_f(void *cookie, void *object, void *key, size_t key_len)
{
    const struct tx_state *ts = object;
    struct local_args *args = cookie;

    /* If no space, skip. (just faster..) */
    if (args->s == args->e) return ZPATH_RESULT_NO_ERROR;

    // Only log stats of watched siems
    if (args->special_processing || zhash_table_lookup(watch_list, &(ts->key.gid), sizeof(ts->key.gid), NULL)) {
        args->s += debug_out(args->s, args->e, ts);
        args->gid++;
    }
    return ZPATH_RESULT_NO_ERROR;
}

static int get_customer_context_f(void *cookie, void *object, void *key, size_t key_len)
{
    struct local_args *args = cookie;

    if (object && args && args->context_list && args->counter < args->num_customers)
        args->context_list[args->counter++] = object;

    return ZPATH_RESULT_NO_ERROR;
}

static int aggregate_siem_counters(void *callback_cookie, int counter, void *structure_data)
{
    struct customer_siem_stats *siem_stats = structure_data;
    struct local_args l_args, *args = callback_cookie;
    int64_t walk_key = 0;
    int64_t num_customers = 0;
    struct customer_siem_context **c = NULL;
    struct tx_state *ts = NULL;
    /* Aggregate counters */
    int64_t siem_type_bitmask = 0;
    int64_t objs_recvd = 0;
    int64_t objs_filter = 0;
    int64_t siem_count = 0;
    int64_t qfreeze_count = 0;
    int64_t overrun = 0;
    int64_t overrun_bytes = 0;
    int64_t ack_count = 0;
    int64_t ack_bytes = 0;
    int64_t *gids = NULL;

    if (!siem_stats || !global_siem_context.init_done) return ZPATH_RESULT_NO_ERROR;

    /* If cookie was not provided, we use local stack struct...*/
    if (!args) {
        args = &l_args;
        memset(args, 0, sizeof(struct local_args));
    }


    ZPATH_MUTEX_LOCK(&global_siem_context.context_lock, __FILE__, __LINE__);
    num_customers = global_siem_context.total_customers;
    c = ZPN_CALLOC(num_customers * sizeof(struct customer_siem_context *));
    if (!c) {
        ZPATH_MUTEX_UNLOCK(&global_siem_context.context_lock, __FILE__, __LINE__);
        return ZPATH_RESULT_NO_ERROR;
    }

    args->num_customers = num_customers;
    args->context_list = c;

    zhash_table_walk(global_siem_context.customers, &walk_key, get_customer_context_f, args);

    for (int j = zpn_siem_type_invalid + 1; j < zpn_siem_type_max; j++) {
        qfreeze_count += global_siem_context.qfreeze_count[j];
    }
    if (qfreeze_count) {
        /* Get SIEM gids only if requested from curl command */
        if (args->special_processing) {
            gids = ZPN_CALLOC(qfreeze_count * sizeof(int64_t));
            struct tx_state **states_arr = ZPN_CALLOC(qfreeze_count * sizeof(struct tx_state *));
            if (!gids || !states_arr) {
                /* WHATTT?? Fine, we will not process list of GIDs */
                if (gids) ZPN_FREE(gids);
                if (states_arr) ZPN_FREE(states_arr);
            } else {
                walk_key = 0; //recycle && reuse to save planet..
                for (int j = zpn_siem_type_invalid + 1; j < zpn_siem_type_max && walk_key < qfreeze_count; j++) {
                    TAILQ_FOREACH(ts, &(global_siem_context.qfreeze[j]), root_freeze_q_entry) {
                        states_arr[walk_key++] = ts;
                    }
                }
                qsort(states_arr, qfreeze_count, sizeof(struct tx_state *), siem_cmptr);
                for (int j = 0; j < qfreeze_count; j++) gids[j] = states_arr[j]->key.gid;
                ZPN_FREE(states_arr);
            }
        }
    }
    siem_count = global_siem_context.total_siem;
    ZPATH_MUTEX_UNLOCK(&global_siem_context.context_lock, __FILE__, __LINE__);

    for (int i = 0; i < num_customers; i++) {
        struct customer_siem_context *c_context = c[i];
        ZPATH_MUTEX_LOCK(&c_context->lock, __FILE__, __LINE__);
        for (int j = zpn_siem_type_invalid + 1; j < zpn_siem_type_max; j++) {
            if (c_context->tx_count[j]) {
                siem_type_bitmask |= (1 << j); //to infer type of SIEM that exists in this system
                TAILQ_FOREACH(ts, &(c_context->tx_states[j]), customer_entry) {
                    objs_recvd    += __sync_fetch_and_add_8(&ts->objects_received, 0);
                    objs_filter   += __sync_fetch_and_add_8(&ts->objects_filtered, 0);
                    overrun       += __sync_fetch_and_add_8(&ts->objects_overruns, 0);
                    overrun_bytes += __sync_fetch_and_add_8(&ts->objects_overruns_bytes, 0);
                    ack_count     += __sync_fetch_and_add_8(&ts->ack_count, 0);
                    ack_bytes     += __sync_fetch_and_add_8(&ts->ack_bytes, 0);
                }
            }
        }
        ZPATH_MUTEX_UNLOCK(&c_context->lock, __FILE__, __LINE__);
    }

    ZPN_FREE(c);
    args->context_list = NULL;

    if (siem_stats->stats_siem_qfreeze_gids) {
        ZPN_FREE(siem_stats->stats_siem_qfreeze_gids);
    }
    siem_stats->stats_siem_objs_received            = objs_recvd;
    siem_stats->stats_siem_objs_unfiltered          = objs_recvd - objs_filter;
    siem_stats->stats_siem_overruns                 = overrun;
    siem_stats->stats_siem_overruns_bytes           = overrun_bytes;
    siem_stats->stats_siem_types_mask               = siem_type_bitmask;
    siem_stats->stats_siem_count                    = siem_count;
    siem_stats->stats_siem_customers_count          = num_customers;
    siem_stats->stats_siem_qfreeze_count            = qfreeze_count;
    siem_stats->stats_siem_qfreeze_gids_c           = gids ? qfreeze_count : 0;
    siem_stats->stats_siem_qfreeze_gids             = gids; //NULL or otherwise
    siem_stats->stats_siem_acks_bytes               = ack_bytes;
    siem_stats->stats_siem_acks_received            = ack_count;

    /* Print into event_log if requested */
    if (args->special_processing) {
        struct argo_object *obj = argo_object_create(customer_siem_stats_description, siem_stats);
        if (!obj) return ARGO_RESULT_NO_MEMORY;
        size_t written = 0;
        argo_object_dump(obj, args->s, args->e - args->s, &written, 1);
        argo_object_release(obj);
        args->s += written;
    }

    return ZPATH_RESULT_NO_ERROR;
}

static void zpn_broker_siem_log_dump_frozen_tx(evutil_socket_t sock, int16_t flags, void *object)
{
    size_t size = (2*1024*1024);
    struct local_args args;
    struct tx_state *ts = NULL;
    char *buf_start = NULL;

    memset(&args, 0, sizeof(struct local_args));
    args.special_processing = 1;
    args.s = buf_start = SIEM_CALLOC(size);

    if (args.s) {
        args.e = args.s + size;
        if (global_siem_context.init_done) {
            ZPATH_MUTEX_LOCK(&global_siem_context.context_lock, __FILE__, __LINE__);
            for (int i = zpn_siem_type_invalid + 1; i < zpn_siem_type_max; i++) {
                if (global_siem_context.qfreeze_count[i]) {
                    TAILQ_FOREACH(ts, &(global_siem_context.qfreeze[i]), root_freeze_q_entry) {
                        walk_watched_f(&args, ts, NULL, 0);
                    }
                }
            }
            ZPATH_MUTEX_UNLOCK(&global_siem_context.context_lock, __FILE__, __LINE__);
        }

        if (args.gid)
            ZPN_LOG(AL_INFO, "Frozen SIEM contexts = %"PRId64"\n%s%s", args.gid, debug_str, buf_start);

        SIEM_FREE(buf_start);
    }
}

static void zpn_broker_siem_tx_log_watched_stats(evutil_socket_t sock, int16_t flags, void *object)
{
    int64_t walk_key = 0;
    size_t size = (2*1024*1024);
    struct local_args args;
    char *buf_start = NULL;

    memset(&args, 0, sizeof(struct local_args));
    args.s = buf_start = SIEM_CALLOC(size);

    if (args.s) {
        args.e = args.s + size;
        if (queues && watch_list) {
            ZPATH_MUTEX_LOCK(&(queues_lock), __FILE__, __LINE__);
            /* Walk them all! */
            zhash_table_walk(queues, &walk_key, walk_watched_f, &args);
            ZPATH_MUTEX_UNLOCK(&(queues_lock), __FILE__, __LINE__);
        }

        if (args.gid)
            ZPN_LOG(AL_INFO, "Monitored SIEM contexts = %"PRId64"\n%s%s", args.gid, debug_str, buf_start);

        SIEM_FREE(buf_start);
    }
}

int zpn_siem_tx_state_init(struct event_base *event_base) {
    struct timeval tv;
    struct event *ev;
    struct customer_siem_stats *siem_stats = NULL;

    queues_lock = ZPATH_MUTEX_INIT;
    watch_list_lock = ZPATH_MUTEX_INIT;
    global_siem_context.context_lock = ZPATH_MUTEX_INIT;

    ZPATH_MUTEX_LOCK(&queues_lock, __FILE__, __LINE__);
    if (!queues) {
        queues = zhash_table_alloc(&siem_allocator);
        if (!queues) {
            ZPATH_MUTEX_UNLOCK(&queues_lock, __FILE__, __LINE__);
            return ZPATH_RESULT_NO_MEMORY;
        }
    }
    ZPATH_MUTEX_UNLOCK(&queues_lock, __FILE__, __LINE__);

    ZPATH_MUTEX_LOCK(&global_siem_context.context_lock, __FILE__, __LINE__);
    if (!global_siem_context.init_done) {
        struct zhash_table *t = zhash_table_alloc(&siem_allocator);
        ZPATH_ASSERT_HARD(t ? 1 : 0, NULL, 0, "zpn_siem_tx", "Global customer context table alloc fail");
        global_siem_context.customers = t;
        for (int i = zpn_siem_type_invalid; i < zpn_siem_type_max; i++) {
            TAILQ_INIT(&(global_siem_context.qfreeze[i]));
        }
        global_siem_context.init_done = 1;
    }
    ZPATH_MUTEX_UNLOCK(&global_siem_context.context_lock, __FILE__, __LINE__);

    ZPATH_MUTEX_LOCK(&watch_list_lock , __FILE__, __LINE__);
    if (!watch_list) {
        watch_list = zhash_table_alloc(&siem_allocator);
        if (!watch_list) {
            ZPATH_MUTEX_UNLOCK(&watch_list_lock , __FILE__, __LINE__);
            return ZPATH_RESULT_NO_MEMORY;
        }
    }
    ZPATH_MUTEX_UNLOCK(&watch_list_lock , __FILE__, __LINE__);

    customer_siem_stats_description = argo_register_global_structure(CUSTOMER_SIEM_STATS_HELPER);

    if (event_base) {
        ev = event_new(event_base, -1, EV_PERSIST, zpn_broker_siem_tx_log_watched_stats, NULL);
        if (!ev) {
            ZPN_LOG(AL_ERROR, "Could not create siem tx stats event...");
            return ZPATH_RESULT_ERR;
        }

        tv.tv_usec = 0;
        tv.tv_sec = STATS_FREQUENCY;
        if (event_add(ev, &tv)) {
            ZPN_LOG(AL_ERROR, "Could not add timer to the siem tx stats event handler");
            return ZPATH_RESULT_ERR;
        }

        ev = event_new(event_base, -1, EV_PERSIST, zpn_broker_siem_log_dump_frozen_tx, NULL);
        if (!ev) {
            ZPN_LOG(AL_ERROR, "Could not create siem tx frozen dump event...");
            return ZPATH_RESULT_ERR;
        }

        tv.tv_usec = 0;
        tv.tv_sec = (60*60); //Dump into event logs once every 60 mins
        if (event_add(ev, &tv)) {
            ZPN_LOG(AL_ERROR, "Could not add timer to the siem tx frozen dump event handler");
            return ZPATH_RESULT_ERR;
        }
    }

    if ((siem_stats = ZPN_CALLOC(sizeof(*siem_stats))) == NULL) return ZPATH_RESULT_NO_MEMORY;

    global_siem_context.r_stats = argo_log_register_structure(argo_log_get("statistics_log"),
                                                              "siem_tx_context_stats",/* stats name */
                                                              AL_INFO,
                                                              MINUTE_TO_US(15), /* 15 mins interval */
                                                              customer_siem_stats_description,
                                                              siem_stats,         /* structure data */
                                                              0,                   /* log immediate */
                                                              aggregate_siem_counters,  /* callback */
                                                              NULL);             /* callback_cookie */

    return ZPATH_RESULT_NO_ERROR;
}

int zpn_siem_debug_watch(int64_t gid, int setting) {
    ZPATH_MUTEX_LOCK(&watch_list_lock, __FILE__, __LINE__);
    int *found_gid = zhash_table_lookup(watch_list, &gid, sizeof(gid), NULL);

    if (found_gid && !setting) { // enabled and asked to disable
        zhash_table_remove(watch_list, &gid, sizeof(gid), found_gid);
    } else if (!found_gid && setting) {  // disabled and asked to enable
        zhash_table_store(watch_list, &gid, sizeof(gid), 0, &gid);
    }

    ZPATH_MUTEX_UNLOCK(&watch_list_lock, __FILE__, __LINE__);
    return ZPATH_RESULT_NO_ERROR;
}

int zpn_siem_is_operational(int64_t siem_id)
{
    int res = 1;
    struct tx_key key = {.gid = siem_id};

    const struct tx_state *ts = zhash_table_lookup(queues, &key, sizeof(key), NULL);
    if (ts && ts->in_qfreeze_state) res = 0;

    return res;
}
