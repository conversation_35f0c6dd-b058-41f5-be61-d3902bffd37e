/*
 * zpn_customer.c. Copyright (C) 2019 Zscaler Inc. All Rights Reserved
 */

#include "zpath_lib/zpath_debug.h"
#include "zpn/zpn_lib.h"
#include <limits.h>

#include "zpn/zpn_posture_profile.h"
#include "zpn/zpn_trusted_network.h"
#include "zpn/zpn_private_broker_load_table.h"
#include "zpn/zpn_pbroker_group.h"
#include "zpn/zpn_private_broker_table.h"
#include "zpn/zpn_pbroker_to_group.h"
#include "zpn/zpn_workload_tag_group.h"
#include "zpn/zpn_scim_group.h"
#include "zpn/zpn_broker_common.h"

#include "zpn/zpn_customer.h"
#include "zpn/zpn_scope_ready.h"
#include "zpn/zpn_scim_attr_header.h"
#include "zpn/zpn_privatebrokergroup_trustednetwork_mapping.h"
#include "zpn/zpn_customer_config.h"
#include "zpn/zpn_c2c_client_registration.h"
#include "zpn/zpn_broker_ipars.h"
#include "zpn/zpn_broker_client.h"
#include "zpn/zpn_application.h"
#include "zpn/zpn_rule_to_pse_group.h"
#include "zpn/zpn_step_up_auth_level.h"
#include "zpn/zpn_rule_to_step_up_auth_level_mapping.h"
#include "zpn/zpn_application_group_application_mapping.h"
#include "zpn/zpn_broker_client_apps_db.h"
#include "zpn/zpn_scope_engine.h"
#include "zpn/zpn_idp_cert.h"
#include "zpn/zpn_idp.h"
#include "zpn/zpn_svcp_profile.h"
#include "np_lib/np_client_subnets.h"
#include "np_lib/np_lan_subnets.h"
#include "np_lib/np_dns_ns_records.h"
#include "np_lib/np_tenant_gateways.h"

#include "zpn/zpn_managed_chrome_extension.h"
#include "zpn/zpn_managed_browser_profile.h"

#include "zpn_zdx/zpn_zdx_webprobe_rate_limit.h"

struct zpn_customer_ready_state_stats zpn_customer_ready_stats = {0};
struct zpn_customer_ready_state {
    int64_t customer_gid;
    zpath_mutex_t state_lock;
    struct wally_callback_queue *cb_queue;
    int async;
    unsigned ready:1;
};

int zpn_customer_ready_internal_locked(struct zpn_customer_ready_state *customer_ready_state);

/* customer_gid (int64_t) => zpn_customer_ready_state (struct zpn_customer_ready_state) */
struct zhash_table *zpn_customer_ready_table = NULL;
static pthread_mutex_t customer_ready_table_lock = (pthread_mutex_t)PTHREAD_MUTEX_INITIALIZER;

static int create_zpn_customer_ready_table()
{
    int res = ZPN_RESULT_NO_ERROR;
    pthread_mutex_lock(&customer_ready_table_lock);
    zpn_customer_ready_table = zhash_table_alloc(&zpn_allocator);
    pthread_mutex_unlock(&customer_ready_table_lock);
    return res;
}

static struct zpn_customer_ready_state *get_zpn_customer_ready_state(int64_t customer_gid)
{
    /* ET-87947 : once the customer data is added, mostly during broker init time, its never deleted.
     * Hence a customer data lookup without a lock is harmless.
     */
    /* coverity[missing_lock : Intentional] */
    struct zpn_customer_ready_state *customer_ready_state = zhash_table_lookup(zpn_customer_ready_table,
                                                                               &customer_gid,
                                                                               sizeof(customer_gid),
                                                                               NULL);
    if(!customer_ready_state) {
        pthread_mutex_lock(&customer_ready_table_lock);
        customer_ready_state = zhash_table_lookup(zpn_customer_ready_table, &customer_gid, sizeof(customer_gid), NULL);
        if (!customer_ready_state) {
            customer_ready_state = ZPN_CALLOC(sizeof(*customer_ready_state));
            customer_ready_state->customer_gid = customer_gid;
            customer_ready_state->state_lock = ZPATH_MUTEX_INIT;
            customer_ready_state->async = 0;
            customer_ready_state->ready = 0;
            customer_ready_state->cb_queue = wally_callback_queue_create();
            zhash_table_store(zpn_customer_ready_table, &customer_gid, sizeof(customer_gid), 0, customer_ready_state);
        }
        pthread_mutex_unlock(&customer_ready_table_lock);
    }
    return customer_ready_state;
}

int zpn_customer_ready_short(int64_t customer_gid,
                             wally_response_callback_f callback_f,
                             void *callback_cookie,
                             int64_t callback_id) {
    int res;

    load_application_state(customer_gid);

    // Fetch attr headers before policy loading because it is used by policy loading
    res = zpn_scim_attr_header_load_by_customer_gid(customer_gid, callback_f, callback_cookie, callback_id);
    if (res) {
        ZPN_DEBUG_SIGNING_CERT("%ld: Customer scim attr headers fetch not complete: %s",
                               (long) customer_gid,
                               zpath_result_string(res));
    } else {
        ZPN_DEBUG_SIGNING_CERT("%ld: Customer scim attr headers load fetch complete", (long) customer_gid);
    }

    return res;
}

static int zpn_customer_ready_callback(void *cookie,
                                       struct wally_registrant *registrant,
                                       struct wally_table *table,
                                       int64_t int_cookie,
                                       int row_count)
{
    struct zpn_customer_ready_state *customer_ready_state = cookie;
    ZPATH_MUTEX_LOCK(&(customer_ready_state->state_lock), __FILE__, __LINE__);
    customer_ready_state->async--;
    zpn_customer_ready_internal_locked(customer_ready_state);

    if(customer_ready_state->ready) {
        zpn_customer_ready_stats.cb_queue_dequeue_count++;
        zpn_customer_ready_stats.cb_queue_size--;
        ZPATH_MUTEX_UNLOCK(&(customer_ready_state->state_lock), __FILE__, __LINE__);
        wally_callback_queue_callback(customer_ready_state->cb_queue);
        return WALLY_RESULT_NO_ERROR;
    }
    ZPATH_MUTEX_UNLOCK(&(customer_ready_state->state_lock), __FILE__, __LINE__);
    return WALLY_RESULT_NO_ERROR;
}

/* Make sure apps and apps->groups and app_groups are loaded before load policy */
int zpn_customer_app_ready(struct zpn_customer_ready_state *customer_ready_state)
{
    /* get apps by customer */
    int64_t customer_gid = customer_ready_state->customer_gid;
    int ret = ZPN_RESULT_NO_ERROR;
    int res;
    int i;
    int j;
    int64_t app_gids[100000];
    size_t app_count = sizeof(app_gids) / sizeof(app_gids[0]);
    int64_t app_group_ids[1000];
    size_t grp_count;

    /* get all apps for this customer, this will register zpn_application */
    res = zpn_application_get_apps_by_customer_gid(customer_gid, &(app_gids[0]), &app_count,
                                                   zpn_customer_ready_callback, customer_ready_state, 0);

    if (res) {
        if (res == ZPN_RESULT_NOT_FOUND) {
            res = ZPN_RESULT_NO_ERROR;
            ZPN_DEBUG_SIGNING_CERT("%ld: Customer zpn_application_get_apps_by_customer_gid not found", (long) customer_gid);
        } else {
            if (res == ZPN_RESULT_ASYNCHRONOUS) customer_ready_state->async++;
            /* This needs to finish *before* app_to_groups_mapping load */
            ZPN_DEBUG_SIGNING_CERT("%ld: Customer zpn_application_get_apps_by_customer_gid not complete: %s",
                                   (long)customer_gid, zpath_result_string(res));
        }
        if (!ret) ret = res;
    } else {
        ZPN_DEBUG_SIGNING_CERT("%ld: Customer zpn_application_get_apps_by_customer_gid complete, count=%d",
                               (long)customer_gid, (int)app_count);
        /* get app_groups by each app_gid, this will register zpn_application_group_application_mapping */
        for (i = 0; i < app_count; i++) {
            grp_count = sizeof(app_group_ids) / sizeof(app_group_ids[0]);
            res = zpn_application_group_application_mapping_get_group_by_app_id(app_gids[i],
                                                                                &(app_group_ids[0]),
                                                                                &grp_count,
                                                                                zpn_customer_ready_callback,
                                                                                customer_ready_state,
                                                                                0);
            if (res) {
                if (res == ZPN_RESULT_NOT_FOUND) {
                    res = ZPN_RESULT_NO_ERROR;
                    ZPN_DEBUG_SIGNING_CERT("%ld, %ld: Customer zpn_application_group_application_mapping_get_group_by_app_id not found",
                                           (long)customer_gid, (long)app_gids[i]);
                } else {
                    if (res == ZPN_RESULT_ASYNCHRONOUS) customer_ready_state->async++;
                    ZPN_DEBUG_SIGNING_CERT("%ld, %ld: Customer zpn_application_group_application_mapping_get_group_by_app_id not complete: %s",
                                           (long)customer_gid, (long)app_gids[i], zpath_result_string(res));
                }
                if (!ret) ret = res;
            } else {
                ZPN_DEBUG_SIGNING_CERT("%ld, %ld: Customer zpn_application_group_application_mapping_get_group_by_app_id complete, count=%d",
                                       (long)customer_gid, (long)app_gids[i], (int)grp_count);

                /* get app_groups by app_group_gid, this will register zpn_app_group */
                for (j = 0; j < grp_count; j++) {
                    struct zpn_application_group *app_group;
                    res = zpn_application_group_get_by_id(app_group_ids[j],
                                                          &app_group,
                                                          zpn_customer_ready_callback,
                                                          customer_ready_state,
                                                          0);
                    if (res) {
                        if (res == ZPN_RESULT_NOT_FOUND) {
                            res = ZPN_RESULT_NO_ERROR;
                            ZPN_DEBUG_SIGNING_CERT("%ld, %ld: Customer zpn_broker_client_apps_group_register not found",
                                                   (long)customer_gid, (long)app_group_ids[j]);
                        } else {
                            if (res == ZPN_RESULT_ASYNCHRONOUS) customer_ready_state->async++;
                            ZPN_DEBUG_SIGNING_CERT("%ld, %ld: Customer zpn_broker_client_apps_group_register not complete: %s",
                                                   (long)customer_gid, (long)app_group_ids[j], zpath_result_string(res));
                            if (!ret) ret = res;
                        }
                    } else {
                        ZPN_DEBUG_SIGNING_CERT("%ld, %ld: Customer zpn_broker_client_apps_group_register complete",
                                               (long)customer_gid, (long)app_group_ids[j]);
                    }
                }
            }
        }
    }

    return ret;
}

/* These data load needs to be complete before load rest data in zpn_customer_ready_internal_locked */
int zpn_customer_ready_preload(struct zpn_customer_ready_state *customer_ready_state)
{
    int ret = ZPN_RESULT_NO_ERROR;
    int res;
    int64_t customer_gid = customer_ready_state->customer_gid;

    // Fetch attr headers before policy loading because it is used by policy loading
    res = zpn_scim_attr_header_load_by_customer_gid(customer_gid, zpn_customer_ready_callback, customer_ready_state, 0);
    if (res) {
        if (res == ZPN_RESULT_ASYNCHRONOUS) customer_ready_state->async++;
        //This needs to finish *before* policy load
        ZPN_DEBUG_SIGNING_CERT("%ld: Customer scim attr headers fetch not complete: %s", (long) customer_gid, zpath_result_string(res));
        if (!ret) ret = res;
    } else {
        ZPN_DEBUG_SIGNING_CERT("%ld: Customer scim attr headers load fetch complete", (long) customer_gid);
    }

    res = zpn_customer_app_ready(customer_ready_state);
    if (!ret) ret = res;

    return ret;
}

/* must be called with zpn_customer_ready_state->lock */
int zpn_customer_ready_internal_locked(struct zpn_customer_ready_state *customer_ready_state)
{
    int ret = ZPN_RESULT_NO_ERROR;
    int res;
    int64_t customer_gid = customer_ready_state->customer_gid;

    if (customer_ready_state->async) return ZPN_RESULT_ASYNCHRONOUS;

    /* These needs to finish *before* policy load, but it is OK to load it asynchrously here
     * since the policy load is moved to scope state */
    res = zpn_customer_ready_preload(customer_ready_state);
    if (!ret) ret = res;

    res = zpn_customer_config_load(customer_gid, zpn_customer_ready_callback, customer_ready_state, 0);
    if (res) {
        if (res == ZPN_RESULT_NOT_FOUND) {
            /* This is fine... */
            res = ZPN_RESULT_NO_ERROR;
        } else {
            if (res == ZPN_RESULT_ASYNCHRONOUS) customer_ready_state->async++;
            ZPN_DEBUG_SIGNING_CERT("%ld: zpn_customer_config_load not complete: %s", (long) customer_gid, zpath_result_string(res));
            if (!ret) ret = res;
        }
    } else {
        ZPN_DEBUG_SIGNING_CERT("%ld: zpn_customer_config_load complete", (long) customer_gid);
    }

    res = zpn_idp_cert_table_load(customer_gid, zpn_customer_ready_callback, customer_ready_state, 0);
    if (res) {
        if (res == ZPN_RESULT_ASYNCHRONOUS) customer_ready_state->async++;
        ZPN_DEBUG_SIGNING_CERT("%" PRId64 ": Customer zpn_idp_cert fetch not complete: %s", customer_gid, zpath_result_string(res));
        if (!ret) ret = res;
    } else {
        ZPN_DEBUG_SIGNING_CERT("%" PRId64 ": Customer zpn_idp_cert fetch complete", customer_gid);
    }

    res = zpn_idp_table_load(customer_gid, zpn_customer_ready_callback, customer_ready_state, 0);
    if (res) {
        if (res == ZPN_RESULT_ASYNCHRONOUS) customer_ready_state->async++;
        ZPN_DEBUG_SIGNING_CERT("%" PRId64 ": Customer zpn_idp fetch not complete: %s", customer_gid, zpath_result_string(res));
        if (!ret) ret = res;
    } else {
        ZPN_DEBUG_SIGNING_CERT("%" PRId64 ": Customer zpn_idp fetch complete", customer_gid);
    }

    res = zpn_posture_profile_load(customer_gid, zpn_customer_ready_callback, customer_ready_state, 0);
    if (res) {
        if (res == ZPN_RESULT_ASYNCHRONOUS) customer_ready_state->async++;
        ZPN_DEBUG_SIGNING_CERT("%ld: Customer posture profile fetch not complete: %s", (long) customer_gid, zpath_result_string(res));
        if (!ret) ret = res;
    } else {
        ZPN_DEBUG_SIGNING_CERT("%ld: Customer posture profile fetch complete", (long) customer_gid);
    }

    res = zpn_svcp_load(customer_gid, zpn_customer_ready_callback, customer_ready_state, 0);
    if (res) {
        if (res == ZPN_RESULT_ASYNCHRONOUS) customer_ready_state->async++;
        ZPN_DEBUG_SIGNING_CERT("%ld: Customer zpn_svcp fetch not complete: %s", (long) customer_gid, zpath_result_string(res));
        if (!ret) ret = res;
    } else {
        ZPN_DEBUG_SIGNING_CERT("%ld: Customer zpn_svcp fetch complete", (long) customer_gid);
    }

    res = zpn_trusted_network_load(customer_gid, zpn_customer_ready_callback, customer_ready_state, 0);
    if (res) {
        if (res == ZPN_RESULT_ASYNCHRONOUS) customer_ready_state->async++;
        ZPN_DEBUG_SIGNING_CERT("%ld: Customer trusted network fetch not complete: %s", (long) customer_gid, zpath_result_string(res));
        if (!ret) ret = res;
    } else {
        ZPN_DEBUG_SIGNING_CERT("%ld: Customer trusted network fetch complete", (long) customer_gid);
    }

    res = zpn_private_broker_load_initialize(customer_gid, zpn_customer_ready_callback, customer_ready_state, 0);
    if (res) {
        if (res == ZPN_RESULT_ASYNCHRONOUS) customer_ready_state->async++;
        ZPN_DEBUG_SIGNING_CERT("%ld: Customer private broker load fetch not complete: %s", (long) customer_gid, zpath_result_string(res));
        if (!ret) ret = res;
    } else {
        ZPN_DEBUG_SIGNING_CERT("%ld: Customer private broker load fetch complete", (long) customer_gid);
    }

    res = zpn_pbroker_group_load(customer_gid, zpn_customer_ready_callback, customer_ready_state, 0);
    if (res) {
        if (res == ZPN_RESULT_ASYNCHRONOUS) customer_ready_state->async++;
        ZPN_DEBUG_SIGNING_CERT("%ld: Customer pbroker group load fetch not complete: %s", (long) customer_gid, zpath_result_string(res));
        if (!ret) ret = res;
    } else {
        ZPN_DEBUG_SIGNING_CERT("%ld: Customer pbroker group load fetch complete", (long) customer_gid);
    }

    res = zpn_private_broker_to_group_load(customer_gid, zpn_customer_ready_callback, customer_ready_state, 0);
    if (res) {
        if (res == ZPN_RESULT_ASYNCHRONOUS) customer_ready_state->async++;
        ZPN_DEBUG_SIGNING_CERT("%ld: Customer pbroker to group load fetch not complete: %s", (long) customer_gid, zpath_result_string(res));
        if (!ret) ret = res;
    } else {
        ZPN_DEBUG_SIGNING_CERT("%ld: Customer pbroker to group load fetch complete", (long) customer_gid);
    }

    if(!ZPN_BROKER_IS_PRIVATE()) {
        res = zpn_rule_to_pse_group_register_customer(customer_gid, NULL, NULL, 0);
        if (res != ZPN_RESULT_ASYNCHRONOUS) {
            ZPN_LOG(AL_CRITICAL, "customer %ld zpn_rule_to_pse_group returned %s", (long) customer_gid, zpath_result_string(res));
        }
    }

    res = zpn_step_up_auth_level_table_load(customer_gid, zpn_customer_ready_callback, customer_ready_state, 0);
    if (res) {
        if (res == ZPN_RESULT_ASYNCHRONOUS) customer_ready_state->async++;
        ZPN_DEBUG_SIGNING_CERT("%ld: Customer zpn_step_up_auth_level_table load fetch not complete: %s", (long) customer_gid, zpath_result_string(res));
        if (!ret) ret = res;
    } else {
        ZPN_DEBUG_SIGNING_CERT("%ld: Customer zpn_step_up_auth_level_table load fetch complete", (long) customer_gid);
    }

    res = zpn_rule_to_step_up_auth_level_mapping_table_load(customer_gid, zpn_customer_ready_callback, customer_ready_state, 0);
    if (res) {
        if (res == ZPN_RESULT_ASYNCHRONOUS) customer_ready_state->async++;
        ZPN_DEBUG_SIGNING_CERT("%ld: Customer zpn_rule_to_step_up_auth_level_mapping_table fetch not complete: %s", (long) customer_gid, zpath_result_string(res));
        if (!ret) ret = res;
    } else {
        ZPN_DEBUG_SIGNING_CERT("%ld: Customer zpn_rule_to_step_up_auth_level_mapping_table load fetch complete", (long) customer_gid);
    }

    res = zpn_privatebrokergroup_trustednetwork_mapping_table_load(customer_gid, zpn_customer_ready_callback, customer_ready_state, 0);
    if (res) {
        if (res == ZPN_RESULT_ASYNCHRONOUS) customer_ready_state->async++;
        ZPN_DEBUG_SIGNING_CERT("%ld: Customer pbgroup trustednetwork mapping load fetch not complete: %s", (long) customer_gid, zpath_result_string(res));
        if (!ret) ret = res;
    } else {
        ZPN_DEBUG_SIGNING_CERT("%ld: Customer pbgroup trustednetwork mapping load fetch complete", (long) customer_gid);
    }

    res = zpn_c2c_client_load(customer_gid, zpn_customer_ready_callback, customer_ready_state, 0);
    if (res) {
        if (res == ZPN_RESULT_ASYNCHRONOUS) customer_ready_state->async++;
        ZPN_DEBUG_SIGNING_CERT("%ld: Customer zpn_c2c_client_registration load fetch not complete: %s", (long) customer_gid, zpath_result_string(res));
        if (!ret) ret = res;
    } else {
        ZPN_DEBUG_SIGNING_CERT("%ld: Customer zpn_c2c_client_registration load fetch complete", (long) customer_gid);
    }

    res = zpn_c2c_ip_range_load(customer_gid, zpn_customer_ready_callback, customer_ready_state, 0);
    if (res) {
        if (res == ZPN_RESULT_ASYNCHRONOUS) customer_ready_state->async++;
        ZPN_DEBUG_SIGNING_CERT("%" PRId64 ": Customer zpn_c2c_ip_range load fetch not complete: %s", customer_gid, zpath_result_string(res));
        if (!ret) ret = res;
    } else {
        ZPN_DEBUG_SIGNING_CERT("%" PRId64 ": Customer zpn_c2c_ip_range load fetch complete", customer_gid);
    }

    res = zpn_workload_tag_grp_load(customer_gid, zpn_customer_ready_callback, customer_ready_state, 0);
    if (res) {
        if (res == ZPN_RESULT_ASYNCHRONOUS) customer_ready_state->async++;
        ZPN_DEBUG_SIGNING_CERT("%" PRId64 ": Customer zpn_workload_tag_group load fetch not complete: %s", customer_gid, zpath_result_string(res));
        if (res && res != ZPN_RESULT_ASYNCHRONOUS) {
            zpn_workload_tag_grp_stats_cust_load_status(0);
        }
        if (!ret) ret = res;
    } else {
        ZPN_DEBUG_SIGNING_CERT("%" PRId64 ": Customer zpn_workload_tag_group load fetch complete", customer_gid);
    }

    res = zse_load_scope(customer_gid, zpn_customer_ready_callback, customer_ready_state, 0);
    if (res) {
        if (res == ZPN_RESULT_ASYNCHRONOUS) customer_ready_state->async++;
        ZPN_DEBUG_SIGNING_CERT("%ld: Customer scope table load fetch not complete: %s", (long) customer_gid, zpath_result_string(res));
        if (!ret) ret = res;
    } else {
        ZPN_DEBUG_SIGNING_CERT("%ld: Customer scope table load fetch complete", (long) customer_gid);
    }

    if (!ZPN_IS_SITEC()) {
        tld_1_add_send_callbacks(customer_gid, zpn_broker_client_send_aggregated_domain_list_for_customer_gid);
    }

    res = zpn_zdx_webprobe_rate_limit_expect_customer(customer_gid);
    if (res ) {
        ZPN_DEBUG_SIGNING_CERT("%"PRId64": customer could not setup webprobe rate limiting tracking", customer_gid);
        if (!ret)  ret = res;
    } else {
        ZPN_DEBUG_SIGNING_CERT("%"PRId64": customer webprobe rate limiting tracking ready", customer_gid);
    }

    /* NP PSE support not ready */
    if (ZPN_BROKER_IS_PUBLIC()) {
        res = np_client_subnets_load(customer_gid, zpn_customer_ready_callback, customer_ready_state, 0);
        if (res) {
            if (res == ZPN_RESULT_ASYNCHRONOUS) customer_ready_state->async++;
            ZPN_DEBUG_NP("%"PRId64": Customer np client subnets fetch not complete: %s", customer_gid, zpath_result_string(res));
            if (!ret) ret = res;
        } else {
            ZPN_DEBUG_NP("%"PRId64": Customer np client subnets fetch complete", customer_gid);
        }

        res = np_lan_subnets_load(customer_gid, zpn_customer_ready_callback, customer_ready_state, 0);
        if (res) {
            if (res == ZPN_RESULT_ASYNCHRONOUS) customer_ready_state->async++;
            ZPN_DEBUG_NP("%"PRId64": Customer np lan subnets fetch not complete: %s", customer_gid, zpath_result_string(res));
            if (!ret) ret = res;
        } else {
            ZPN_DEBUG_NP("%"PRId64": Customer np lan subnets fetch complete", customer_gid);
        }

        res = np_dns_ns_records_load(customer_gid, zpn_customer_ready_callback, customer_ready_state, 0);
        if (res) {
            if (res == ZPN_RESULT_ASYNCHRONOUS) customer_ready_state->async++;
            ZPN_DEBUG_NP("%"PRId64": Customer np dns ns records fetch not complete: %s", customer_gid, zpath_result_string(res));
            if (!ret) ret = res;
        } else {
            ZPN_DEBUG_NP("%"PRId64": Customer np dns ns records fetch complete", customer_gid);
        }

        res = np_tenant_gateways_load(customer_gid, zpn_customer_ready_callback, customer_ready_state, 0);
        if (res) {
            if (res == ZPN_RESULT_ASYNCHRONOUS) customer_ready_state->async++;
            ZPN_DEBUG_NP("%"PRId64": Customer np tenant gateways fetch not complete: %s", customer_gid, zpath_result_string(res));
            if (!ret) ret = res;
        } else {
            ZPN_DEBUG_NP("%"PRId64": Customer np tenant gateways fetch complete", customer_gid);
        }

        res = zpn_scim_group_load(customer_gid, zpn_customer_ready_callback, customer_ready_state, 0);
        if (res) {
            if (res == ZPN_RESULT_ASYNCHRONOUS) customer_ready_state->async++;
            ZPN_DEBUG_NP("%"PRId64": Customer scim group table load fetch not complete: %s", customer_gid, zpath_result_string(res));
            if (!ret) ret = res;
        } else {
            ZPN_DEBUG_NP("%"PRId64": Customer scim group table load fetch complete", customer_gid);
        }
    }

    if (!customer_ready_state->async && !ret) {
        customer_ready_state->ready = 1;
        ZPN_LOG(AL_NOTICE, "%" PRId64 " customer is ready", customer_ready_state->customer_gid);
    }

    return ret;
}

int zpn_customer_ready_no_policy(int64_t customer_gid,
                                 wally_response_callback_f callback_f,
                                 void *callback_cookie,
                                 int64_t callback_id)
{
    int res;
    struct zpn_customer_ready_state *customer_ready_state = get_zpn_customer_ready_state(customer_gid);

    ZPATH_MUTEX_LOCK(&(customer_ready_state->state_lock), __FILE__, __LINE__);


    if (!customer_ready_state->ready) {
        zpn_customer_ready_internal_locked(customer_ready_state);
        if (customer_ready_state->ready) {
            zpn_customer_ready_stats.cb_queue_dequeue_count++;
            zpn_customer_ready_stats.cb_queue_size--;
            ZPATH_MUTEX_UNLOCK(&(customer_ready_state->state_lock), __FILE__, __LINE__);
            wally_callback_queue_callback(customer_ready_state->cb_queue);
            return ZPN_RESULT_NO_ERROR;
        }

        res = wally_callback_queue_add(customer_ready_state->cb_queue,
                                       callback_f,
                                       callback_cookie,
                                       callback_id);
        zpn_customer_ready_stats.cb_queue_enqueue_count++;
        zpn_customer_ready_stats.cb_queue_size++;
        ZPATH_MUTEX_UNLOCK(&(customer_ready_state->state_lock), __FILE__, __LINE__);
        if (res) {
            return res;
        } else {
            return ZPATH_RESULT_ASYNCHRONOUS;
        }
    }
    ZPATH_MUTEX_UNLOCK(&(customer_ready_state->state_lock), __FILE__, __LINE__);
    return ZPATH_RESULT_NO_ERROR;
}

int zpn_customer_ready(int64_t customer_gid,
                       wally_response_callback_f callback_f,
                       void *callback_cookie,
                       int64_t callback_id)
{
    int res = ZPN_RESULT_NO_ERROR;

    res = zpn_customer_ready_no_policy(customer_gid,
                                       callback_f,
                                       callback_cookie,
                                       callback_id);
    if (res) {
        ZPN_DEBUG_SIGNING_CERT("%ld: Customer fetch without default scope not complete: %s",
                               (long) customer_gid,
                               zpath_result_string(res));
        return res;
    } else {
        ZPN_DEBUG_SIGNING_CERT("%ld: Customer fetch without default scope complete", (long) customer_gid);
    }

    res = zpn_default_scope_ready(customer_gid,
                                  callback_f,
                                  callback_cookie,
                                  callback_id);
    if (res) {
        ZPN_DEBUG_SIGNING_CERT("%ld: Default scope fetch not complete: %s",
                                (long) customer_gid,
                                zpath_result_string(res));
    } else {
        ZPN_DEBUG_SIGNING_CERT("%ld: Default scope fetch complete", (long) customer_gid);
    }

    return res;
}

static int zpn_customer_ready_debug(struct zpath_debug_state *request_state,
                                    const char **query_values,
                                    int query_value_count,
                                    void *cookie)
{
    int64_t customer_gid = 0;
    int res;
    if (zpn_parse_str_to_uint(query_values[0], NULL, &customer_gid))
    {
        ZDP("Invalid input string or too long to fit in unsigned int");
        return ZPATH_RESULT_ERR;
    }
    res = zpn_customer_ready(customer_gid, NULL, NULL, 0);
    ZDP("zpn_customer_ready for customer %ld returned %s\n", (long) customer_gid, zpn_result_string(res));

    return ZPATH_RESULT_NO_ERROR;
}

uint8_t zpn_parse_str_to_uint(const char *inp_tok,
                          char **out_tok,
                          int64_t *parsed_number)
{
    char * next_tok = NULL;
    uint64_t num = 0;

    if(inp_tok && inp_tok[0] != '-')
    {
        num = strtoul(inp_tok, &next_tok, 0);
        if (*next_tok != 0)
        {
            if (out_tok)
                *out_tok = next_tok;
            return ZPATH_RESULT_BAD_DATA;
        }
        if (num == ULONG_MAX)
        {
            return ZPATH_RESULT_ERR_TOO_LARGE;
        }
        *parsed_number = num;
        return ZPATH_RESULT_NO_ERROR;
    }

    return ZPATH_RESULT_BAD_ARGUMENT;
}

int zpn_customer_init()
{
    int res;

    res = zpath_debug_add_write_command("Check if all customers' state has been loaded from config",
                                  "/customer/ready",
                                  zpn_customer_ready_debug,
                                  NULL,
                                  "customer", "Customer GID",
                                  NULL);
    if (res) return res;

    res = create_zpn_customer_ready_table();
    if (res) return res;

    res = zpn_scope_ready_init();

    return res;
}
