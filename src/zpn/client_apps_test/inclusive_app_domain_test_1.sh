#!../zpn_policy_engine_test
#
# File : src/zpn/client_apps_test/inclusive_app_domain_test_1.sh
#
# Inclusive domain app download test
#
# These do not really use any policy as of yet.
#

ENABLE_APP_MULTI_MATCH_FEATURE_FLAG

#compare old and new policy evaluation methods
POLICY_EVALUATION_COMPARE

# use hard code scope gid 73197005911883776 and customer gid 73197005911883776
USE_SCOPE_INIT

#
# Start up an (empty) client for customer 73197005911883776
# This will trigger fetching config for customer 73197005911883776.
#
WALLY_POLICY
GET 73197005911883776 access ZPATH_RESULT_ASYNCHRONOUS
WALLY_REG_VERIFY -table zpn_rule -register YES -column customer_gid -key 73197005911883776 -id id1
WALLY_RESPOND -id id1 -rows 0 -table zpn_rule
WAITFOR WALLY_REG_VERIFY -table zpn_rule_condition_set -register YES -column customer_gid -key 73197005911883776 -id id1
WALLY_RESPOND -id id1 -rows 0 -table zpn_rule_condition_set
WAITFOR WALLY_REG_VERIFY -table zpn_rule_condition_operand -register YES -column customer_gid -key 73197005911883776 -id id1
WALLY_RESPOND -id id1 -rows 0 -table zpn_rule_condition_operand
# A deferred policy build occurs here which will trigger this registration.
WAITFOR WALLY_REG_VERIFY -table zpn_policy_set -register YES -column customer_gid -key 73197005911883776 -id id1
WALLY_RESPOND -id id1 -rows 0 -table zpn_policy_set

WAITFOR GET 73197005911883776 access ZPATH_RESULT_NO_ERROR

#
# Init zpath_table table. The delay is to let the multithreaded
# initialization complete.
#73197005911883776
ZPATH_TABLE
WAITFOR WALLY_REG_VERIFY -table zpath_table -register YES -id id1
# If you want, you could WALLY_INJECT initial zpath_table rows here.
WALLY_RESPOND -id id1 -rows 0 -table zpath_table

# initilize zpn_application and zpn_application_domain for customer 1234
ZPN_APPLICATION_DOMAIN -customer 73197005911883776

# Initial registration. This triggers a bunch of wally (zapp)
CLIENT_APP start -client_type 1 -user vbhatt -customer 73197005911883776 -scope 73197005911883776

#client type Cloud Connector (CC)
CLIENT_APP start -segment -client_type 4 -user CC_USER -customer 73197005911883776 -scope 73197005911883776

# Verify wally asked for customer's apps.
WAITFOR WALLY_REG_VERIFY -table zpn_application -register YES -column customer_gid -key 73197005911883776 -id id1
WAITFOR WALLY_REG_VERIFY -table zpn_application_domain -register YES -column customer_gid -key 73197005911883776 -id id1a

# Send response complete to wally, since we have no rows
WALLY_RESPOND -id id1 -rows 0 -table zpn_application
WALLY_RESPOND -id id1a -rows 0 -table zpn_application_domain

# Check that it is complete.
WAITFOR CLIENT_APP complete -user vbhatt
WAITFOR CLIENT_APP complete -user CC_USER

# Events between response and app_complete are on different threads,
# so wait a millisacond, here.
DELAY 10000

# Be sure no accidental registration came in again...
WALLY_REG_VERIFY -table zpn_application -register NONE


# Case: 1
# _______________________________________________________________________________________________________________
# |App seg	|	domains 								|	port 				|	style			|	action  |
# +---------+-------------------------------------------+-----------------------+-------------------+-----------+
# |5 		|	server1.login.us.corp.company.com 		|	135,137,42			|	inclusive		|	access  |
# |6 		|	server2.login.us.corp.company.com 		|	135,80,443			|	exclusive		|	access  |
# |7 		|	.login.us.corp.company.com 				|	52,1512,636			|	inclusive		|	access  |
# |8 		|	.us.corp.company.com 					|	3389,42,135 		|	inclusive		|	access  |
# |9 		|	.corp.company.com 						|	80,3268,53			|	inclusive		|	access  |
# |10 		|	.company.com 							|	1024-65535,443 		|	inclusive		|	access  |
# ---------------------------------------------------------------------------------------------------------------
#
#

WALLY_INJECT {"zpn_application":{"gid":5, "customer_gid":73197005911883776, "tcp_port_ranges":[135,135,137,137,42,42], "domain_names":["server1.login.us.corp.company.com"], "bypass_type":"NEVER", "enabled":1, "match_style": "inclusive"}}

WALLY_INJECT {"zpn_application":{"gid":6, "customer_gid":73197005911883776, "tcp_port_ranges":[135,135,80,80,443,443], "domain_names":["server2.login.us.corp.company.com"], "bypass_type":"NEVER", "enabled":1, "match_style": "exclusive"}}

WALLY_INJECT {"zpn_application":{"gid":7, "customer_gid":73197005911883776, "tcp_port_ranges":[52,52,1512,1512,636,636], "domain_names":[".login.us.corp.company.com"], "bypass_type":"NEVER", "enabled":1, "match_style": "inclusive"}}
WALLY_INJECT {"zpn_application":{"gid":8, "customer_gid":73197005911883776, "tcp_port_ranges":[3389,3389,42,42,135,135], "domain_names":[".us.corp.company.com"], "bypass_type":"NEVER", "enabled":1, "match_style": "inclusive"}}
WALLY_INJECT {"zpn_application":{"gid":9, "customer_gid":73197005911883776, "tcp_port_ranges":[80,80,3268,3268,53,53], "domain_names":[".corp.company.com"], "bypass_type":"NEVER", "enabled":1, "match_style": "inclusive"}}
WALLY_INJECT {"zpn_application":{"gid":10, "customer_gid":73197005911883776, "tcp_port_ranges":[1024,65535,443,443], "domain_names":[".company.com"], "bypass_type":"NEVER", "enabled":1, "match_style": "inclusive"}}


WALLY_INJECT {"zpn_application_domain":{"id":30, "customer_gid":73197005911883776, "application_gid":5, "domain_name":"server1.login.us.corp.company.com", "deleted":0}}
WALLY_INJECT {"zpn_application_domain":{"id":31, "customer_gid":73197005911883776, "application_gid":6, "domain_name":"server2.login.us.corp.company.com", "deleted":0}}
WALLY_INJECT {"zpn_application_domain":{"id":32, "customer_gid":73197005911883776, "application_gid":7, "domain_name":".login.us.corp.company.com", "deleted":0}}
WALLY_INJECT {"zpn_application_domain":{"id":33, "customer_gid":73197005911883776, "application_gid":8, "domain_name":".us.corp.company.com", "deleted":0}}
WALLY_INJECT {"zpn_application_domain":{"id":34, "customer_gid":73197005911883776, "application_gid":9, "domain_name":".corp.company.com", "deleted":0}}
WALLY_INJECT {"zpn_application_domain":{"id":35, "customer_gid":73197005911883776, "application_gid":10, "domain_name":".company.com", "deleted":0}}


WALLY_REG_VERIFY -table zpn_application_group_application_mapping -register YES -column application_id -key 5 -id id2
WALLY_REG_VERIFY -table zpn_application_group_application_mapping -register YES -column application_id -key 6 -id id3
WALLY_REG_VERIFY -table zpn_application_group_application_mapping -register YES -column application_id -key 7 -id id4
WALLY_REG_VERIFY -table zpn_application_group_application_mapping -register YES -column application_id -key 8 -id id5
WALLY_REG_VERIFY -table zpn_application_group_application_mapping -register YES -column application_id -key 9 -id id6
WALLY_REG_VERIFY -table zpn_application_group_application_mapping -register YES -column application_id -key 10 -id id7


WALLY_INJECT {"zpn_application_group_application_mapping":{"id":100, "application_id":5, "application_group_id":4, "customer_gid":73197005911883776}}
WALLY_INJECT {"zpn_application_group_application_mapping":{"id":101, "application_id":6, "application_group_id":4, "customer_gid":73197005911883776}}
WALLY_INJECT {"zpn_application_group_application_mapping":{"id":102, "application_id":7, "application_group_id":4, "customer_gid":73197005911883776}}
WALLY_INJECT {"zpn_application_group_application_mapping":{"id":103, "application_id":8, "application_group_id":4, "customer_gid":73197005911883776}}
WALLY_INJECT {"zpn_application_group_application_mapping":{"id":104, "application_id":9, "application_group_id":4, "customer_gid":73197005911883776}}
WALLY_INJECT {"zpn_application_group_application_mapping":{"id":105, "application_id":10, "application_group_id":4, "customer_gid":73197005911883776}}

WALLY_RESPOND -id id2 -rows 1 -table zpn_application_group_application_mapping
WALLY_RESPOND -id id3 -rows 1 -table zpn_application_group_application_mapping
WALLY_RESPOND -id id4 -rows 1 -table zpn_application_group_application_mapping
WALLY_RESPOND -id id5 -rows 1 -table zpn_application_group_application_mapping
WALLY_RESPOND -id id6 -rows 1 -table zpn_application_group_application_mapping
WALLY_RESPOND -id id7 -rows 1 -table zpn_application_group_application_mapping

WALLY_REG_VERIFY -table zpn_application_group -register YES -column gid -key 4 -id id3
WALLY_INJECT {"zpn_application_group":{"gid":4, "customer_gid":73197005911883776, "enabled":1}}
WALLY_RESPOND -id id3 -rows 1 -table zpn_application_group

ZPN_TLD_1_UPDATE -customer 73197005911883776 -domain server1.login.us.corp.company.com
ZPN_TLD_1_UPDATE -customer 73197005911883776 -domain server2.login.us.corp.company.com
ZPN_TLD_1_UPDATE -customer 73197005911883776 -domain .login.us.corp.company.com
ZPN_TLD_1_UPDATE -customer 73197005911883776 -domain .us.corp.company.com
ZPN_TLD_1_UPDATE -customer 73197005911883776 -domain .corp.company.com
ZPN_TLD_1_UPDATE -customer 73197005911883776 -domain .company.com
DELAY 20000


#
# server1.login.us.corp.company.com: {"zpn_client_app":{"app_domain":"server1.login.us.corp.company.com",
# "ingress_port_ranges":[42, 42, 52, 53, 80, 80, 135, 135, 137, 137, 443, 443, 636, 636, 1024, 65535],"tcp_port_ranges":[42, 42, 52, 53, 80, 80, 135, 135, 137, 137, 443, 443, 636, 636, 1024, 65535],"deleted":0,"bypass":0,"icmp_access_type":"NONE","double_encrypt":0,"bypass_type":"NEVER"}}
#

WAITFOR CLIENT_APP check -user vbhatt -domain server1.login.us.corp.company.com -proto tcp -port 135 -action never
WAITFOR CLIENT_APP check -user vbhatt -domain server1.login.us.corp.company.com -proto tcp -port 52 -action never
WAITFOR CLIENT_APP check -user vbhatt -domain server1.login.us.corp.company.com -proto tcp -port 80 -action never
WAITFOR CLIENT_APP check -user vbhatt -domain server1.login.us.corp.company.com -proto tcp -port 443 -action never
WAITFOR CLIENT_APP check -user vbhatt -domain server1.login.us.corp.company.com -proto tcp -port 3389 -action never

#
# server2.login.us.corp.company.com: {"zpn_client_app":{"app_domain":"server2.login.us.corp.company.com",
# "ingress_port_ranges":[80, 80, 135, 135, 443, 443],"tcp_port_ranges":[80, 80, 135, 135, 443, 443],"deleted":0,"bypass":0,"icmp_access_type":"NONE","double_encrypt":0,"bypass_type":"NEVER"}}
#
WAITFOR CLIENT_APP check -user vbhatt -domain server2.login.us.corp.company.com -proto tcp -port 135 -action never
WAITFOR CLIENT_APP check -user vbhatt -domain server2.login.us.corp.company.com -proto tcp -port 52 -action not_found
WAITFOR CLIENT_APP check -user vbhatt -domain server2.login.us.corp.company.com -proto tcp -port 80 -action never
WAITFOR CLIENT_APP check -user vbhatt -domain server2.login.us.corp.company.com -proto tcp -port 443 -action never
WAITFOR CLIENT_APP check -user vbhatt -domain server2.login.us.corp.company.com -proto tcp -port 42 -action not_found
WAITFOR CLIENT_APP check -user vbhatt -domain server2.login.us.corp.company.com -proto tcp -port 53 -action not_found
WAITFOR CLIENT_APP check -user vbhatt -domain server2.login.us.corp.company.com -proto tcp -port 3389 -action not_found
WAITFOR CLIENT_APP check -user vbhatt -domain server2.login.us.corp.company.com -proto tcp -port 1024 -action not_found

#
# .login.us.corp.company.com: {"zpn_client_app":{"app_domain":".login.us.corp.company.com",
# "ingress_port_ranges":[42, 42, 52, 53, 80, 80, 135, 135, 443, 443, 636, 636, 1024, 65535],"tcp_port_ranges":[42, 42, 52, 53, 80, 80, 135, 135, 443, 443, 636, 636, 1024, 65535],"deleted":0,"bypass":0,"icmp_access_type":"NONE","double_encrypt":0,"bypass_type":"NEVER"}}
#
WAITFOR CLIENT_APP check -user vbhatt -domain .login.us.corp.company.com -proto tcp -port 137 -action not_found
WAITFOR CLIENT_APP check -user vbhatt -domain .login.us.corp.company.com -proto tcp -port 52 -action never
WAITFOR CLIENT_APP check -user vbhatt -domain .login.us.corp.company.com -proto tcp -port 80 -action never
WAITFOR CLIENT_APP check -user vbhatt -domain .login.us.corp.company.com -proto tcp -port 42 -action never
WAITFOR CLIENT_APP check -user vbhatt -domain .login.us.corp.company.com -proto tcp -port 2048 -action never

#
# .us.corp.company.com: {"zpn_client_app":{"app_domain":".us.corp.company.com",
# "ingress_port_ranges":[42, 42, 53, 53, 80, 80, 135, 135, 443, 443, 1024, 65535],"tcp_port_ranges":[42, 42, 53, 53, 80, 80, 135, 135, 443, 443, 1024, 65535],"deleted":0,"bypass":0,"icmp_access_type":"NONE","double_encrypt":0,"bypass_type":"NEVER"}}
#
WAITFOR CLIENT_APP check -user vbhatt -domain .us.corp.company.com -proto tcp -port 52 -action not_found
WAITFOR CLIENT_APP check -user vbhatt -domain .us.corp.company.com -proto tcp -port 80 -action never
WAITFOR CLIENT_APP check -user vbhatt -domain .us.corp.company.com -proto tcp -port 443 -action never
WAITFOR CLIENT_APP check -user vbhatt -domain .us.corp.company.com -proto tcp -port 42 -action never


#
# .corp.company.com: {"zpn_client_app":{"app_domain":".corp.company.com",
# "ingress_port_ranges":[53, 53, 80, 80, 443, 443, 1024, 65535],"tcp_port_ranges":[53, 53, 80, 80, 443, 443, 1024, 65535],"deleted":0,"bypass":0,"icmp_access_type":"NONE","double_encrypt":0,"bypass_type":"NEVER"}}
#
WAITFOR CLIENT_APP check -user vbhatt -domain .corp.company.com -proto tcp -port 52 -action not_found
WAITFOR CLIENT_APP check -user vbhatt -domain .corp.company.com -proto tcp -port 80 -action never
WAITFOR CLIENT_APP check -user vbhatt -domain .corp.company.com -proto tcp -port 443 -action never
WAITFOR CLIENT_APP check -user vbhatt -domain .corp.company.com -proto tcp -port 42 -action not_found

#
# .company.com: {"zpn_client_app":{"app_domain":".company.com",
# "ingress_port_ranges":[443, 443, 1024, 65535],"tcp_port_ranges":[443, 443, 1024, 65535],"deleted":0,"bypass":0,"icmp_access_type":"NONE","double_encrypt":0,"bypass_type":"NEVER"}}
#
WAITFOR CLIENT_APP check -user vbhatt -domain .company.com -proto tcp -port 52 -action not_found
WAITFOR CLIENT_APP check -user vbhatt -domain .company.com -proto tcp -port 80 -action not_found
WAITFOR CLIENT_APP check -user vbhatt -domain .company.com -proto tcp -port 443 -action never
WAITFOR CLIENT_APP check -user vbhatt -domain .company.com -proto tcp -port 42 -action not_found


## Segment based app download validation for Case 1

#
# 5:server1.login.us.corp.company.com: {"zpn_client_app":{"app_domain":"server1.login.us.corp.company.com",
# "ingress_port_ranges":[42, 42, 135, 135, 137, 137],"tcp_port_ranges":[42, 42, 135, 135, 137, 137],"deleted":0,"bypass":0,"icmp_access_type":"NONE","double_encrypt":0,"bypass_type":"NEVER","app_gid":5,"match_style":2}}
#
WAITFOR CLIENT_APP check -user CC_USER -domain server1.login.us.corp.company.com -app_gid 5 -proto tcp -port 42 -action never -match_style 2
WAITFOR CLIENT_APP check -user CC_USER -domain server1.login.us.corp.company.com -app_gid 5 -proto tcp -port 52 -action not_found -match_style 2

#
# 6:server2.login.us.corp.company.com: {"zpn_client_app":{"app_domain":"server2.login.us.corp.company.com",
# "ingress_port_ranges":[80, 80, 135, 135, 443, 443],"tcp_port_ranges":[80, 80, 135, 135, 443, 443],"deleted":0,"bypass":0,"icmp_access_type":"NONE","double_encrypt":0,"bypass_type":"NEVER","app_gid":6,"match_style":1}}
#
WAITFOR CLIENT_APP check -user CC_USER -domain server2.login.us.corp.company.com -app_gid 6 -proto tcp -port 80 -action never -match_style 1

#
# 7:.login.us.corp.company.com: {"zpn_client_app":{"app_domain":".login.us.corp.company.com",
# "ingress_port_ranges":[52, 52, 636, 636, 1512, 1512],"tcp_port_ranges":[52, 52, 636, 636, 1512, 1512],"deleted":0,"bypass":0,"icmp_access_type":"NONE","double_encrypt":0,"bypass_type":"NEVER","app_gid":7,"match_style":2}}
#
WAITFOR CLIENT_APP check -user CC_USER -domain .login.us.corp.company.com -app_gid 7 -proto tcp -port 52 -action never -match_style 2

#
# 8:.us.corp.company.com: {"zpn_client_app":{"app_domain":".us.corp.company.com",
# "ingress_port_ranges":[42, 42, 135, 135, 3389, 3389],"tcp_port_ranges":[42, 42, 135, 135, 3389, 3389],"deleted":0,"bypass":0,"icmp_access_type":"NONE","double_encrypt":0,"bypass_type":"NEVER","app_gid":8,"match_style":2}}
#
WAITFOR CLIENT_APP check -user CC_USER -domain .us.corp.company.com -app_gid 8 -proto tcp -port 42 -action never -match_style 2

#
# 9:.corp.company.com: {"zpn_client_app":{"app_domain":".corp.company.com",
# "ingress_port_ranges":[53, 53, 80, 80, 3268, 3268],"tcp_port_ranges":[53, 53, 80, 80, 3268, 3268],"deleted":0,"bypass":0,"icmp_access_type":"NONE","double_encrypt":0,"bypass_type":"NEVER","app_gid":9,"match_style":2}}
#
WAITFOR CLIENT_APP check -user CC_USER -domain .corp.company.com -app_gid 9 -proto tcp -port 80 -action never -match_style 2


#
# 10:.company.com: {"zpn_client_app":{"app_domain":".company.com",
# "ingress_port_ranges":[443, 443, 1024, 65535],"tcp_port_ranges":[443, 443, 1024, 65535],"deleted":0,"bypass":0,"icmp_access_type":"NONE","double_encrypt":0,"bypass_type":"NEVER","app_gid":10,"match_style":2}}
#
WAITFOR CLIENT_APP check -user CC_USER -domain .company.com -app_gid 10 -proto tcp -port 443 -action never -match_style 2

# Enabled below commands for debug porpose
# client_app dump -user vbhatt
# client_app dump -user CC_USER

# Case: 2
#
# _______________________________________________________________________________________________________________
# |App seg	|	domains 								|	port 				|	style			|	action  |
# +---------+-------------------------------------------+-----------------------+-------------------+-----------+
# |5 		|	server1.login.us.corp.company.com 		|	135,137,42			|	inclusive		|	access  |
# |6 		|	server2.login.us.corp.company.com 		|	135,80,443			|	exclusive		|	access  |
# |7 		|	.login.us.corp.company.com 				|	52,1512,636			|	inclusive		|	access  |
# |8 		|	.us.corp.company.com 					|	3389,42,135 		|	exclusive		|	access  |
# |9 		|	.corp.company.com 						|	80,3268,53			|	inclusive		|	access  |
# |10 		|	.company.com 							|	1024-65535,443 		|	inclusive		|	access  |
# ---------------------------------------------------------------------------------------------------------------
#
#

WALLY_INJECT {"zpn_application":{"gid":8, "customer_gid":73197005911883776, "tcp_port_ranges":[3389,3389,42,42,135,135], "domain_names":[".us.corp.company.com"], "bypass_type":"NEVER", "enabled":1, "match_style": "exclusive"}}
DELAY 2000

#
# server1.login.us.corp.company.com: {"zpn_client_app":{"app_domain":"server1.login.us.corp.company.com",
# "ingress_port_ranges":[42, 42, 52, 52, 135, 135, 137, 137, 636, 636, 1512, 1512],"tcp_port_ranges":[42, 42, 52, 52, 135, 135, 137, 137, 636, 636, 1512, 1512],"deleted":0,"bypass":0,"icmp_access_type":"NONE","double_encrypt":0,"bypass_type":"NEVER"}}
#
WAITFOR CLIENT_APP check -user vbhatt -domain server1.login.us.corp.company.com -proto tcp -port 135 -action never
WAITFOR CLIENT_APP check -user vbhatt -domain server1.login.us.corp.company.com -proto tcp -port 52 -action never
WAITFOR CLIENT_APP check -user vbhatt -domain server1.login.us.corp.company.com -proto tcp -port 1512 -action never
WAITFOR CLIENT_APP check -user vbhatt -domain server1.login.us.corp.company.com -proto tcp -port 80 -action not_found
WAITFOR CLIENT_APP check -user vbhatt -domain server1.login.us.corp.company.com -proto tcp -port 53 -action not_found
WAITFOR CLIENT_APP check -user vbhatt -domain server1.login.us.corp.company.com -proto tcp -port 42 -action never
WAITFOR CLIENT_APP check -user vbhatt -domain server1.login.us.corp.company.com -proto tcp -port 443 -action not_found
WAITFOR CLIENT_APP check -user vbhatt -domain server1.login.us.corp.company.com -proto tcp -port 3389 -action not_found


#
# .login.us.corp.company.com: {"zpn_client_app":{"app_domain":".login.us.corp.company.com",
# "ingress_port_ranges":[52, 52, 636, 636, 1512, 1512],"tcp_port_ranges":[52, 52, 636, 636, 1512, 1512],"deleted":0,"bypass":0,"icmp_access_type":"NONE","double_encrypt":0,"bypass_type":"NEVER"}}
#
WAITFOR CLIENT_APP check -user vbhatt -domain .login.us.corp.company.com -proto tcp -port 135 -action not_found
WAITFOR CLIENT_APP check -user vbhatt -domain .login.us.corp.company.com -proto tcp -port 52 -action never
WAITFOR CLIENT_APP check -user vbhatt -domain .login.us.corp.company.com -proto tcp -port 1512 -action never
WAITFOR CLIENT_APP check -user vbhatt -domain .login.us.corp.company.com -proto tcp -port 80 -action not_found
WAITFOR CLIENT_APP check -user vbhatt -domain .login.us.corp.company.com -proto tcp -port 53 -action not_found
WAITFOR CLIENT_APP check -user vbhatt -domain .login.us.corp.company.com -proto tcp -port 42 -action not_found
WAITFOR CLIENT_APP check -user vbhatt -domain .login.us.corp.company.com -proto tcp -port 443 -action not_found
WAITFOR CLIENT_APP check -user vbhatt -domain .login.us.corp.company.com -proto tcp -port 3389 -action not_found


#
# .us.corp.company.com: {"zpn_client_app":{"app_domain":".us.corp.company.com",
# "ingress_port_ranges":[42, 42, 135, 135, 3389, 3389],"tcp_port_ranges":[42, 42, 135, 135, 3389, 3389],"deleted":0,"bypass":0,"icmp_access_type":"NONE","double_encrypt":0,"bypass_type":"NEVER"}}
#
WAITFOR CLIENT_APP check -user vbhatt -domain .us.corp.company.com -proto tcp -port 135 -action never
WAITFOR CLIENT_APP check -user vbhatt -domain .us.corp.company.com -proto tcp -port 52 -action not_found
WAITFOR CLIENT_APP check -user vbhatt -domain .us.corp.company.com -proto tcp -port 1512 -action not_found
WAITFOR CLIENT_APP check -user vbhatt -domain .us.corp.company.com -proto tcp -port 80 -action not_found
WAITFOR CLIENT_APP check -user vbhatt -domain .us.corp.company.com -proto tcp -port 53 -action not_found
WAITFOR CLIENT_APP check -user vbhatt -domain .us.corp.company.com -proto tcp -port 42 -action never
WAITFOR CLIENT_APP check -user vbhatt -domain .us.corp.company.com -proto tcp -port 443 -action not_found
WAITFOR CLIENT_APP check -user vbhatt -domain .us.corp.company.com -proto tcp -port 3389 -action never

#
# .corp.company.com: {"zpn_client_app":{"app_domain":".corp.company.com",
# "ingress_port_ranges":[53, 53, 80, 80, 443, 443, 1024, 65535],"tcp_port_ranges":[53, 53, 80, 80, 443, 443, 1024, 65535],"deleted":0,"bypass":0,"icmp_access_type":"NONE","double_encrypt":0,"bypass_type":"NEVER"}}
#
WAITFOR CLIENT_APP check -user vbhatt -domain .corp.company.com -proto tcp -port 135 -action not_found
WAITFOR CLIENT_APP check -user vbhatt -domain .corp.company.com -proto tcp -port 52 -action not_found
WAITFOR CLIENT_APP check -user vbhatt -domain .corp.company.com -proto tcp -port 1512 -action never
WAITFOR CLIENT_APP check -user vbhatt -domain .corp.company.com -proto tcp -port 80 -action never
WAITFOR CLIENT_APP check -user vbhatt -domain .corp.company.com -proto tcp -port 53 -action never
WAITFOR CLIENT_APP check -user vbhatt -domain .corp.company.com -proto tcp -port 42 -action not_found
WAITFOR CLIENT_APP check -user vbhatt -domain .corp.company.com -proto tcp -port 443 -action never
WAITFOR CLIENT_APP check -user vbhatt -domain .corp.company.com -proto tcp -port 3389 -action never

#
# .company.com: {"zpn_client_app":{"app_domain":".company.com",
# "ingress_port_ranges":[443, 443, 1024, 65535],"tcp_port_ranges":[443, 443, 1024, 65535],"deleted":0,"bypass":0,"icmp_access_type":"NONE","double_encrypt":0,"bypass_type":"NEVER"}}
#
WAITFOR CLIENT_APP check -user vbhatt -domain .company.com -proto tcp -port 135 -action not_found
WAITFOR CLIENT_APP check -user vbhatt -domain .company.com -proto tcp -port 52 -action not_found
WAITFOR CLIENT_APP check -user vbhatt -domain .company.com -proto tcp -port 1512 -action never
WAITFOR CLIENT_APP check -user vbhatt -domain .company.com -proto tcp -port 80 -action not_found
WAITFOR CLIENT_APP check -user vbhatt -domain .company.com -proto tcp -port 53 -action not_found
WAITFOR CLIENT_APP check -user vbhatt -domain .company.com -proto tcp -port 42 -action not_found
WAITFOR CLIENT_APP check -user vbhatt -domain .company.com -proto tcp -port 443 -action never
WAITFOR CLIENT_APP check -user vbhatt -domain .company.com -proto tcp -port 3389 -action never


## Segment based app download validation for Case 2
#
# 5:server1.login.us.corp.company.com: {"zpn_client_app":{"app_domain":"server1.login.us.corp.company.com",
# "ingress_port_ranges":[42, 42, 135, 135, 137, 137],"tcp_port_ranges":[42, 42, 135, 135, 137, 137],"deleted":0,"bypass":0,"icmp_access_type":"NONE","double_encrypt":0,"bypass_type":"NEVER","app_gid":5,"match_style":2}}
#
WAITFOR CLIENT_APP check -user CC_USER -domain server1.login.us.corp.company.com -app_gid 5 -proto tcp -port 42 -action never -match_style 2
WAITFOR CLIENT_APP check -user CC_USER -domain server1.login.us.corp.company.com -app_gid 5 -proto tcp -port 52 -action not_found -match_style 2

#
# 6:server2.login.us.corp.company.com: {"zpn_client_app":{"app_domain":"server2.login.us.corp.company.com",
# "ingress_port_ranges":[80, 80, 135, 135, 443, 443],"tcp_port_ranges":[80, 80, 135, 135, 443, 443],"deleted":0,"bypass":0,"icmp_access_type":"NONE","double_encrypt":0,"bypass_type":"NEVER","app_gid":6,"match_style":1}}
#
WAITFOR CLIENT_APP check -user CC_USER -domain server2.login.us.corp.company.com -app_gid 6 -proto tcp -port 80 -action never -match_style 1

#
# 7:.login.us.corp.company.com: {"zpn_client_app":{"app_domain":".login.us.corp.company.com",
# "ingress_port_ranges":[52, 52, 636, 636, 1512, 1512],"tcp_port_ranges":[52, 52, 636, 636, 1512, 1512],"deleted":0,"bypass":0,"icmp_access_type":"NONE","double_encrypt":0,"bypass_type":"NEVER","app_gid":7,"match_style":2}}
#
WAITFOR CLIENT_APP check -user CC_USER -domain .login.us.corp.company.com -app_gid 7 -proto tcp -port 52 -action never -match_style 2

#
# 8:.us.corp.company.com: {"zpn_client_app":{"app_domain":".us.corp.company.com",
# "ingress_port_ranges":[42, 42, 135, 135, 3389, 3389],"tcp_port_ranges":[42, 42, 135, 135, 3389, 3389],"deleted":0,"bypass":0,"icmp_access_type":"NONE","double_encrypt":0,"bypass_type":"NEVER","app_gid":8,"match_style":1}}
#
WAITFOR CLIENT_APP check -user CC_USER -domain .us.corp.company.com -app_gid 8 -proto tcp -port 42 -action never -match_style 1

#
# 9:.corp.company.com: {"zpn_client_app":{"app_domain":".corp.company.com",
# "ingress_port_ranges":[53, 53, 80, 80, 3268, 3268],"tcp_port_ranges":[53, 53, 80, 80, 3268, 3268],"deleted":0,"bypass":0,"icmp_access_type":"NONE","double_encrypt":0,"bypass_type":"NEVER","app_gid":9,"match_style":2}}
#
WAITFOR CLIENT_APP check -user CC_USER -domain .corp.company.com -app_gid 9 -proto tcp -port 80 -action never -match_style 2


#
# 10:.company.com: {"zpn_client_app":{"app_domain":".company.com",
# "ingress_port_ranges":[443, 443, 1024, 65535],"tcp_port_ranges":[443, 443, 1024, 65535],"deleted":0,"bypass":0,"icmp_access_type":"NONE","double_encrypt":0,"bypass_type":"NEVER","app_gid":10,"match_style":2}}
#
WAITFOR CLIENT_APP check -user CC_USER -domain .company.com -app_gid 10 -proto tcp -port 443 -action never -match_style 2

# Enabled below commands for debug porpose
# client_app dump -user vbhatt
# client_app dump -user CC_USER


# Case: 3
#
# _______________________________________________________________________________________________________________
# |App seg	|	domains 								|	port 				|	style			|	action  |
# +---------+-------------------------------------------+-----------------------+-------------------+-----------+
# |5 		|	server1.login.us.corp.company.com 		|	135,137,42			|	inclusive		|	access  |
# |6 		|	server2.login.us.corp.company.com 		|	135,80,443			|	exclusive		|	access  |
# |7 		|	.login.us.corp.company.com 				|	52,1512,636			|	inclusive		|	access  |
# |8 		|	.us.corp.company.com 					|	3389,42,135 		|	inclusive		|	access  |
# |9 		|	.corp.company.com 						|	80,3268,53			|	inclusive		|	Bypass  |
# |10 		|	.company.com 							|	1024-65535,443 		|	inclusive		|	access  |
# ---------------------------------------------------------------------------------------------------------------
#
#


WALLY_INJECT {"zpn_application":{"gid":8, "customer_gid":73197005911883776, "tcp_port_ranges":[3389,3389,42,42,135,135], "domain_names":[".us.corp.company.com"], "bypass_type":"NEVER", "enabled":1, "match_style": "inclusive"}}
WALLY_INJECT {"zpn_application":{"gid":9, "customer_gid":73197005911883776, "tcp_port_ranges":[80,80,3268,3268,53,53], "domain_names":[".corp.company.com"], "bypass_type":"ALWAYS", "enabled":1, "match_style": "inclusive"}}
DELAY 2000



#
# server1.login.us.corp.company.com: {"zpn_client_app":{"app_domain":"server1.login.us.corp.company.com",
# "ingress_port_ranges":[42, 42, 52, 52, 135, 135, 137, 137, 636, 636, 1512, 1512, 3389, 3389],"tcp_port_ranges":[42, 42, 52, 52, 135, 135, 137, 137, 636, 636, 1512, 1512, 3389, 3389],"deleted":0,"bypass":0,"icmp_access_type":"NONE","double_encrypt":0,"bypass_type":"NEVER"}}
#
WAITFOR CLIENT_APP check -user vbhatt -domain server1.login.us.corp.company.com -proto tcp -port 135 -action never
WAITFOR CLIENT_APP check -user vbhatt -domain server1.login.us.corp.company.com -proto tcp -port 52 -action never
WAITFOR CLIENT_APP check -user vbhatt -domain server1.login.us.corp.company.com -proto tcp -port 1512 -action never
WAITFOR CLIENT_APP check -user vbhatt -domain server1.login.us.corp.company.com -proto tcp -port 80 -action not_found
WAITFOR CLIENT_APP check -user vbhatt -domain server1.login.us.corp.company.com -proto tcp -port 53 -action not_found
WAITFOR CLIENT_APP check -user vbhatt -domain server1.login.us.corp.company.com -proto tcp -port 42 -action never
WAITFOR CLIENT_APP check -user vbhatt -domain server1.login.us.corp.company.com -proto tcp -port 443 -action not_found
WAITFOR CLIENT_APP check -user vbhatt -domain server1.login.us.corp.company.com -proto tcp -port 3389 -action never
WAITFOR CLIENT_APP check -user vbhatt -domain server1.login.us.corp.company.com -proto tcp -port 8080 -action not_found


#
# .login.us.corp.company.com: {"zpn_client_app":{"app_domain":".login.us.corp.company.com",
# "ingress_port_ranges":[42, 42, 52, 52, 135, 135, 636, 636, 1512, 1512, 3389, 3389],"tcp_port_ranges":[42, 42, 52, 52, 135, 135, 636, 636, 1512, 1512, 3389, 3389],"deleted":0,"bypass":0,"icmp_access_type":"NONE","double_encrypt":0,"bypass_type":"NEVER"}}
#
WAITFOR CLIENT_APP check -user vbhatt -domain .login.us.corp.company.com -proto tcp -port 135 -action never
WAITFOR CLIENT_APP check -user vbhatt -domain .login.us.corp.company.com -proto tcp -port 52 -action never
WAITFOR CLIENT_APP check -user vbhatt -domain .login.us.corp.company.com -proto tcp -port 1512 -action never
WAITFOR CLIENT_APP check -user vbhatt -domain .login.us.corp.company.com -proto tcp -port 80 -action not_found
WAITFOR CLIENT_APP check -user vbhatt -domain .login.us.corp.company.com -proto tcp -port 53 -action not_found
WAITFOR CLIENT_APP check -user vbhatt -domain .login.us.corp.company.com -proto tcp -port 42 -action never
WAITFOR CLIENT_APP check -user vbhatt -domain .login.us.corp.company.com -proto tcp -port 443 -action not_found
WAITFOR CLIENT_APP check -user vbhatt -domain .login.us.corp.company.com -proto tcp -port 3389 -action never
WAITFOR CLIENT_APP check -user vbhatt -domain .login.us.corp.company.com -proto tcp -port 8080 -action not_found


#
# .us.corp.company.com: {"zpn_client_app":{"app_domain":".us.corp.company.com",
# "ingress_port_ranges":[42, 42, 135, 135, 3389, 3389],"tcp_port_ranges":[42, 42, 135, 135, 3389, 3389],"deleted":0,"bypass":0,"icmp_access_type":"NONE","double_encrypt":0,"bypass_type":"NEVER"}}
#
WAITFOR CLIENT_APP check -user vbhatt -domain .us.corp.company.com -proto tcp -port 135 -action never
WAITFOR CLIENT_APP check -user vbhatt -domain .us.corp.company.com -proto tcp -port 52 -action not_found
WAITFOR CLIENT_APP check -user vbhatt -domain .us.corp.company.com -proto tcp -port 1512 -action not_found
WAITFOR CLIENT_APP check -user vbhatt -domain .us.corp.company.com -proto tcp -port 80 -action not_found
WAITFOR CLIENT_APP check -user vbhatt -domain .us.corp.company.com -proto tcp -port 53 -action not_found
WAITFOR CLIENT_APP check -user vbhatt -domain .us.corp.company.com -proto tcp -port 42 -action never
WAITFOR CLIENT_APP check -user vbhatt -domain .us.corp.company.com -proto tcp -port 443 -action not_found
WAITFOR CLIENT_APP check -user vbhatt -domain .us.corp.company.com -proto tcp -port 3389 -action never


#
# .corp.company.com: {"zpn_client_app":{"app_domain":".corp.company.com","deleted":0,"bypass":1,"icmp_access_type":"NONE","double_encrypt":0,"bypass_type":"ALWAYS"}}
#
WAITFOR CLIENT_APP check -user vbhatt -domain .corp.company.com -bypass 1 -proto tcp -port 80 -action bypass


#
# .company.com: {"zpn_client_app":{"app_domain":".company.com",
# "ingress_port_ranges":[443, 443, 1024, 65535],"tcp_port_ranges":[443, 443, 1024, 65535],"deleted":0,"bypass":0,"icmp_access_type":"NONE","double_encrypt":0,"bypass_type":"NEVER"}}
#
WAITFOR CLIENT_APP check -user vbhatt -domain .company.com -proto tcp -port 135 -action not_found
WAITFOR CLIENT_APP check -user vbhatt -domain .company.com -proto tcp -port 52 -action not_found
WAITFOR CLIENT_APP check -user vbhatt -domain .company.com -proto tcp -port 1512 -action never
WAITFOR CLIENT_APP check -user vbhatt -domain .company.com -proto tcp -port 80 -action not_found
WAITFOR CLIENT_APP check -user vbhatt -domain .company.com -proto tcp -port 53 -action not_found
WAITFOR CLIENT_APP check -user vbhatt -domain .company.com -proto tcp -port 42 -action not_found
WAITFOR CLIENT_APP check -user vbhatt -domain .company.com -proto tcp -port 443 -action never
WAITFOR CLIENT_APP check -user vbhatt -domain .company.com -proto tcp -port 8080 -action never

# client_app dump -user vbhatt


# Case: 4
#
# ___________________________________________________________________________________________________________________________________
# |App seg	|	domains 								|	port 				|	style			|	action  |   us_in_dr_mode   |
# +---------+-------------------------------------------+-----------------------+-------------------+-----------+-------------------+
# |5 		|	server1.login.us.corp.company.com 		|	22,137,42			|	inclusive		|	access  |         1         |
# |6 		|	server2.login.us.corp.company.com 		|	135,80,443			|	exclusive		|	access  |         1         |
# |7 		|	.login.us.corp.company.com 				|	52,1512,636			|	inclusive		|	access  |         0         |
# |8 		|	.us.corp.company.com 					|	3389,42,135 		|	inclusive		|	access  |         1         |
# |9 		|	.corp.company.com 						|	80,3268,53			|	inclusive		|	access  |         0         |
# |10 		|	.company.com 							|	1024-65535,443 		|	inclusive		|	access  |         1         |
# -----------------------------------------------------------------------------------------------------------------------------------
#
#

# This will activate DR mode for APP downloading
ACTIVATE_DR_MODE

WALLY_INJECT {"zpn_application":{"gid":5, "customer_gid":73197005911883776, "tcp_port_ranges":[22,22,137,137,42,42], "domain_names":["server1.login.us.corp.company.com"], "bypass_type":"NEVER", "enabled":1, "match_style": "inclusive", "use_in_dr_mode":"1"}}

WALLY_INJECT {"zpn_application":{"gid":6, "customer_gid":73197005911883776, "tcp_port_ranges":[135,135,80,80,443,443], "domain_names":["server2.login.us.corp.company.com"], "bypass_type":"NEVER", "enabled":1, "match_style": "exclusive", "use_in_dr_mode":"1"}}

WALLY_INJECT {"zpn_application":{"gid":7, "customer_gid":73197005911883776, "tcp_port_ranges":[52,52,1512,1512,636,636], "domain_names":[".login.us.corp.company.com"], "bypass_type":"NEVER", "enabled":1, "match_style": "inclusive", "use_in_dr_mode":"0"}}
WALLY_INJECT {"zpn_application":{"gid":8, "customer_gid":73197005911883776, "tcp_port_ranges":[3389,3389,42,42,135,135], "domain_names":[".us.corp.company.com"], "bypass_type":"NEVER", "enabled":1, "match_style": "inclusive", "use_in_dr_mode":"1"}}
WALLY_INJECT {"zpn_application":{"gid":9, "customer_gid":73197005911883776, "tcp_port_ranges":[80,80,3268,3268,53,53], "domain_names":[".corp.company.com"], "bypass_type":"NEVER", "enabled":1, "match_style": "inclusive", "use_in_dr_mode":"0"}}
WALLY_INJECT {"zpn_application":{"gid":10, "customer_gid":73197005911883776, "tcp_port_ranges":[1024,65535,443,443], "domain_names":[".company.com"], "bypass_type":"NEVER", "enabled":1, "match_style": "inclusive", "use_in_dr_mode":"1"}}
DELAY 2000


#
# server1.login.us.corp.company.com: {"zpn_client_app":{"app_domain":"server1.login.us.corp.company.com",
# "ingress_port_ranges":[22, 22, 42, 42, 135, 135, 137, 137, 443, 443, 1024, 65535],"tcp_port_ranges":[22, 22, 42, 42, 135, 135, 137, 137, 443, 443, 1024, 65535],"deleted":0,"bypass":0,"icmp_access_type":"NONE","double_encrypt":0,"bypass_type":"NEVER"}}
#
WAITFOR CLIENT_APP check -user vbhatt -domain server1.login.us.corp.company.com -proto tcp -port 135 -action never
WAITFOR CLIENT_APP check -user vbhatt -domain server1.login.us.corp.company.com -proto tcp -port 22 -action never
WAITFOR CLIENT_APP check -user vbhatt -domain server1.login.us.corp.company.com -proto tcp -port 52 -action not_found
WAITFOR CLIENT_APP check -user vbhatt -domain server1.login.us.corp.company.com -proto tcp -port 80 -action not_found
WAITFOR CLIENT_APP check -user vbhatt -domain server1.login.us.corp.company.com -proto tcp -port 53 -action not_found
WAITFOR CLIENT_APP check -user vbhatt -domain server1.login.us.corp.company.com -proto tcp -port 42 -action never
WAITFOR CLIENT_APP check -user vbhatt -domain server1.login.us.corp.company.com -proto tcp -port 443 -action never
WAITFOR CLIENT_APP check -user vbhatt -domain server1.login.us.corp.company.com -proto tcp -port 8080 -action never


#
# .login.us.corp.company.com: {"zpn_client_app":{"app_domain":".login.us.corp.company.com",
# "ingress_port_ranges":[42, 42, 135, 135, 443, 443, 1024, 65535],"tcp_port_ranges":[42, 42, 135, 135, 443, 443, 1024, 65535],"deleted":0,"bypass":0,"icmp_access_type":"NONE","double_encrypt":0,"bypass_type":"NEVER"}}
#
WAITFOR CLIENT_APP check -user vbhatt -domain .login.us.corp.company.com -proto tcp -port 135 -action never
WAITFOR CLIENT_APP check -user vbhatt -domain .login.us.corp.company.com -proto tcp -port 52 -action not_found
WAITFOR CLIENT_APP check -user vbhatt -domain .login.us.corp.company.com -proto tcp -port 80 -action not_found
WAITFOR CLIENT_APP check -user vbhatt -domain .login.us.corp.company.com -proto tcp -port 53 -action not_found
WAITFOR CLIENT_APP check -user vbhatt -domain .login.us.corp.company.com -proto tcp -port 42 -action never
WAITFOR CLIENT_APP check -user vbhatt -domain .login.us.corp.company.com -proto tcp -port 443 -action never
WAITFOR CLIENT_APP check -user vbhatt -domain .login.us.corp.company.com -proto tcp -port 3389 -action never

#
# .us.corp.company.com: {"zpn_client_app":{"app_domain":".us.corp.company.com",
# "ingress_port_ranges":[42, 42, 135, 135, 443, 443, 1024, 65535],"tcp_port_ranges":[42, 42, 135, 135, 443, 443, 1024, 65535],"deleted":0,"bypass":0,"icmp_access_type":"NONE","double_encrypt":0,"bypass_type":"NEVER"}}
#
WAITFOR CLIENT_APP check -user vbhatt -domain .us.corp.company.com -proto tcp -port 135 -action never
WAITFOR CLIENT_APP check -user vbhatt -domain .us.corp.company.com -proto tcp -port 52 -action not_found
WAITFOR CLIENT_APP check -user vbhatt -domain .us.corp.company.com -proto tcp -port 80 -action not_found
WAITFOR CLIENT_APP check -user vbhatt -domain .us.corp.company.com -proto tcp -port 53 -action not_found
WAITFOR CLIENT_APP check -user vbhatt -domain .us.corp.company.com -proto tcp -port 42 -action never
WAITFOR CLIENT_APP check -user vbhatt -domain .us.corp.company.com -proto tcp -port 443 -action never
WAITFOR CLIENT_APP check -user vbhatt -domain .us.corp.company.com -proto tcp -port 3389 -action never


#
# .corp.company.com: {"zpn_client_app":{"app_domain":".corp.company.com",
# "ingress_port_ranges":[443, 443, 1024, 65535],"tcp_port_ranges":[443, 443, 1024, 65535],"deleted":0,"bypass":0,"icmp_access_type":"NONE","double_encrypt":0,"bypass_type":"NEVER"}}
#
WAITFOR CLIENT_APP check -user vbhatt -domain .company.com -proto tcp -port 135 -action not_found
WAITFOR CLIENT_APP check -user vbhatt -domain .company.com -proto tcp -port 52 -action not_found
WAITFOR CLIENT_APP check -user vbhatt -domain .company.com -proto tcp -port 1512 -action never
WAITFOR CLIENT_APP check -user vbhatt -domain .company.com -proto tcp -port 80 -action not_found
WAITFOR CLIENT_APP check -user vbhatt -domain .company.com -proto tcp -port 53 -action not_found
WAITFOR CLIENT_APP check -user vbhatt -domain .company.com -proto tcp -port 42 -action not_found
WAITFOR CLIENT_APP check -user vbhatt -domain .company.com -proto tcp -port 443 -action never
WAITFOR CLIENT_APP check -user vbhatt -domain .company.com -proto tcp -port 8080 -action never


#
# .company.com: {"zpn_client_app":{"app_domain":".company.com",
# "ingress_port_ranges":[443, 443, 1024, 65535],"tcp_port_ranges":[443, 443, 1024, 65535],"deleted":0,"bypass":0,"icmp_access_type":"NONE","double_encrypt":0,"bypass_type":"NEVER"}}
#
WAITFOR CLIENT_APP check -user vbhatt -domain .company.com -proto tcp -port 135 -action not_found
WAITFOR CLIENT_APP check -user vbhatt -domain .company.com -proto tcp -port 52 -action not_found
WAITFOR CLIENT_APP check -user vbhatt -domain .company.com -proto tcp -port 1512 -action never
WAITFOR CLIENT_APP check -user vbhatt -domain .company.com -proto tcp -port 80 -action not_found
WAITFOR CLIENT_APP check -user vbhatt -domain .company.com -proto tcp -port 53 -action not_found
WAITFOR CLIENT_APP check -user vbhatt -domain .company.com -proto tcp -port 42 -action not_found
WAITFOR CLIENT_APP check -user vbhatt -domain .company.com -proto tcp -port 443 -action never
WAITFOR CLIENT_APP check -user vbhatt -domain .company.com -proto tcp -port 8080 -action never


# Enabled below command for debug porpose
# client_app dump -user vbhatt
