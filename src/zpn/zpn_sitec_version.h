/*
 * zpn_sitec_version.h. Copyright (C) 2024 Zscaler Inc. All Rights Reserved
 */

#ifndef __ZPN_SITEC_VERSION_H__
#define __ZPN_SITEC_VERSION_H__

#include "argo/argo.h"
#include "wally/wally.h"

struct zpn_site_controller_version {           /* _ARGO: object_definition */
    /* Standard SQL fields. */
    int64_t sequence;                          /* _ARGO: integer, sequence */
    int32_t modified_time;                     /* _ARGO: integer */
    int32_t creation_time;                     /* _ARGO: integer */
    int64_t modifiedby_userid;                 /* _ARGO: integer */
    int64_t deleted;                           /* _ARGO: integer, deleted */

    /* The unique index for this private broker */
    int64_t gid;                               /* _ARGO: integer, index, key */
    int64_t site_controller_group_gid;         /* _ARGO: integer */
    int64_t customer_gid;                      /* _ARGO: integer, index */

    /* Fields from site controller */
    char *current_version;                     /* _ARGO: string */
    int64_t system_start_time;                 /* _ARGO: integer */
    int64_t application_start_time;            /* _ARGO: integer */
    int64_t last_connect_time;                 /* _ARGO: integer */
    int64_t last_disconnect_time;              /* _ARGO: integer */
    char *platform;                            /* _ARGO: string */
    int ctrl_channel_status;                   /* _ARGO: integer */
    int64_t broker_id;                         /* _ARGO: integer */
    struct argo_inet *private_ip;              /* _ARGO: inet */
    struct argo_inet *public_ip;               /* _ARGO: inet */
    char *tunnel_id;                           /* _ARGO: string */

    /* Fields from upgrade manager */
    char *expected_version;                    /* _ARGO: string */
    int upgrade_status;                        /* _ARGO: integer */
    int64_t next_restart_time;                 /* _ARGO: integer */
    char *restart_instructions;                /* _ARGO: string */
    char *previous_version;                    /* _ARGO: string */
    int64_t last_upgraded_time;                /* _ARGO: integer */
    int lone_warrior;                          /* _ARGO: integer */
    char *expected_sarge_version;              /* _ARGO: string */
    int os_upgrade_enabled;                    /* _ARGO: integer */
    int64_t master_last_sync_time;           /* _ARGO: integer */
    int64_t shard_last_sync_time;            /* _ARGO: integer */
    int64_t userdb_last_sync_time;           /* _ARGO: integer */
};

int zpn_sitec_version_init(struct wally *single_tenant_wally,
                           int64_t single_tenant_gid,
                           int single_tenant_fully_loaded,
                           int register_with_zpath_table);


int zpn_sitec_version_get_by_id(int64_t sitec_gid,
                                struct zpn_site_controller_version **sitec_version,
                                wally_response_callback_f callback_f,
                                void *callback_cookie,
                                int64_t callback_id) __attribute__((weak));
#endif /* __ZPN_SITEC_VERSION_H__ */
