#ifndef _ZPN_PRIVATE_BROKER_PRIVATE_H_
#define _ZPN_PRIVATE_BROKER_PRIVATE_H_

#include "zpn/zpn_private_broker_version.h"
#include "zcdns/zcdns_libevent.h"
#include "zpn/zpn_sub_module_upgrade.h"
#include "zpn_enrollment_lib/zpn_enrollment.h" // TODO: Fix this dependency hell.
#include "zhw/zhw_id.h"
#include "zcrypt/zcrypt.h"
#include "zcrypt/zcrypt_meta.h"
#include "zpn/zpn_system.h"
#include "zpn/zpn_rpc.h"
#include "zpn/zpn_lib.h"
#include "zpn/zpn_version_control/zpn_version_control_utils.h"
#include "zpath_lib/zpath_upgrade_utils.h"

/* Private broker defines */
#define PRIVATE_BROKER_PROV_KEY_LEN             1024
#define PRIVATE_BROKER_KEY_API_LEN              100
#define PRIVATE_BROKER_INSTANCE_NAME_LEN        ZPN_MAX_INSTANCE_NAME_LEN
#define PRIVATE_BROKER_CLOUD_NAME_LEN           ZPN_MAX_CLOUD_NAME_LEN
#define PRIVATE_BROKER_CN_LEN                   256
#define PRIVATE_BROKER_INVALID_ID               0
#define PRIVATE_BROKER_GLOBAL_MAGIC             (0XBAFF1ED) // Baffled
#define PRIVATE_BROKER_ROLE_NAME                "Private Broker"
#define PRIVATE_BROKER_ROLE_NAME_LEN            32
#define PRIVATE_BROKER_SNI_DOMAIN_LEN           ZPN_MAX_DOMAIN_NAME_LEN
#define PRIVATE_BROKER_ENROLL_VERSION           2
#define PRIVATE_BROKER_INSTANCE_FULL_NAME       (ZPN_MAX_INSTANCE_NAME_LEN + 1 + ZPN_MAX_CLOUD_NAME_LEN)

#define PBROKER_RESPONSE_MAX_BUFFER_DATA        (64*1024)

#define PBROKER_DEBUG_BYTES ZPATH_DEBUG_BYTES

#define PBROKER_DEFAULT_FOHH_THREADS		4
#define SERVICE_EDGE_LOG_NAME   "Service Edge"
#define PRIVATE_BROKER_ARGO_LOG_NAME       "zpn_lib"

#define ZPN_PBROKER_FORCE_RE_ENROLL_TIME_FACTOR               99

#define PBROKER_CONTROL_TX_STATS_PROC_SMAPS_TIMEPERIOD_SEC    ((int64_t)(24ll * 60ll * 60ll))

#define PBROKER_COMMAND_LINE_ARG_LEN        1024

extern int is_pbroker_dev_environment;

#define DUMP_CONFIG_WALLY_STATE_FILE        "wally_config_dump"
#define DUMP_CONFIG_WALLY_OVD_STATE_FILE    "wally_config_ovd_dump"

//#define PRIVATE_BROKER_ATOMICS_API_C11_STDATOMIC

#ifdef PRIVATE_BROKER_ATOMICS_API_C11_STDATOMIC
#include <stdatomic.h>
#endif

/*
 * Connected broker
 */
struct zpn_connected_broker {
    int64_t broker_gid_from_config;     /* Broker gid */
    struct fohh_connection *f_conn;     /* Pbroker -> Broker conn */
    int64_t f_conn_incarnation;         /* f_conn_incarnation */
    int log_upload;                     /* Pbroker -> Broker log upload state */
    uint64_t debug_flag;                /* Debug logs flag for broker */
    int stats_upload;                   /* PBroker -> Broker stats upload state */
};

#ifdef PRIVATE_BROKER_ATOMICS_API_C11_STDATOMIC

  /*
   * Use the standard C11 Atomics API
   */

  #define ZPN_ATOMIC_UINT64  atomic_uint_least64_t
  #define ZPN_ATOMIC_INT64   atomic_int_least64_t

  #define ZPN_ATOMIC_UINT32  atomic_uint_least64_t
  #define ZPN_ATOMIC_INT32   atomic_int_least32_t

  #define ZPN_ATOMIC_LOAD_EXPLICIT(ptr,var,memorder) \
    do { var = atomic_load_explicit(&ptr, memorder); } while(0)

  #define ZPN_ATOMIC_LOAD(ptr,var) \
    do { var = atomic_load(ptr); } while(0)

  #define ZPN_ATOMIC_STORE_EXPLICIT(ptr,var,memorder) \
    do { atomic_store_explicit(&ptr, var, memorder); } while(0)

  #define ZPN_ATOMIC_STORE(ptr,var) \
    do { atomic_store(ptr, var); } while(0)

  #define ZPN_ATOMIC_FETCH_ADD8(ptr,val) atomic_fetch_add((atomic_uint_least64_t *)(ptr), val)
  #define ZPN_ATOMIC_FETCH_SUB8(ptr,val) atomic_fetch_sub((atomic_uint_least64_t *)(ptr), val)

  #define ZPN_ATOMIC_FETCH_ADD4(ptr,val) atomic_fetch_add((atomic_uint_least32_t *)(ptr), val)
  #define ZPN_ATOMIC_FETCH_SUB4(ptr,val) atomic_fetch_sub((atomic_uint_least32_t *)(ptr), val)

#else

  /*
   * Use gcc builtin atomic API. These have been deprecated in favor of standard C11 Atomics API
   * Their internal implementation uses the standard C11 Atomics API
   */

  #define ZPN_ATOMIC_UINT64  volatile uint64_t
  #define ZPN_ATOMIC_INT64   volatile int64_t

  #define ZPN_ATOMIC_UINT32  volatile uint32_t
  #define ZPN_ATOMIC_INT32   volatile int32_t

  #define ZPN_ATOMIC_LOAD_EXPLICIT(ptr,var,memorder) ZPN_ATOMIC_LOAD(ptr,var)
  #define ZPN_ATOMIC_LOAD(ptr,var) \
    do { var = *(ptr); } while(0)

  #define ZPN_ATOMIC_STORE_EXPLICIT(ptr,var,memorder) ZPN_ATOMIC_STORE(ptr,var)
  #define ZPN_ATOMIC_STORE(ptr,var) \
    do { *(ptr) = var; } while(0)

  #define ZPN_ATOMIC_FETCH_ADD8(ptr,val) __sync_add_and_fetch_8(ptr, val)
  #define ZPN_ATOMIC_FETCH_SUB8(ptr,val) __sync_sub_and_fetch_8(ptr, val)

  #define ZPN_ATOMIC_FETCH_ADD4(ptr,val) __sync_add_and_fetch_4(ptr, val)
  #define ZPN_ATOMIC_FETCH_SUB4(ptr,val) __sync_sub_and_fetch_4(ptr, val)

#endif

struct zpn_private_broker_client_connection_stats {

    /* Aggregate client statistics */
    ZPN_ATOMIC_UINT64 total_client_connections_created;
    ZPN_ATOMIC_UINT64 total_client_connections_freed;
    ZPN_ATOMIC_UINT64 total_active_client_connections_count;
    ZPN_ATOMIC_UINT64 total_client_connections_init_failed;
    ZPN_ATOMIC_UINT64 approx_peak_active_client_connections_count;

    /* ZCC statistics */
    ZPN_ATOMIC_UINT64 zcc_connections_created;
    ZPN_ATOMIC_UINT64 zcc_connections_freed;
    ZPN_ATOMIC_UINT64 zcc_active_connections_count;
    ZPN_ATOMIC_UINT64 zcc_connections_init_failed;

    /* Branch Connector statistics */
    ZPN_ATOMIC_UINT64 branch_connector_connections_created;
    ZPN_ATOMIC_UINT64 branch_connector_connections_freed;
    ZPN_ATOMIC_UINT64 branch_connector_active_connections_count;
    ZPN_ATOMIC_UINT64 branch_connector_connections_init_failed;

    /* Edge Connector statistics */
    ZPN_ATOMIC_UINT64 edge_connector_connections_created;
    ZPN_ATOMIC_UINT64 edge_connector_connections_freed;
    ZPN_ATOMIC_UINT64 edge_connector_active_connections_count;
    ZPN_ATOMIC_UINT64 edge_connector_connections_init_failed;

    /* VDI statistics */
    ZPN_ATOMIC_UINT64 vdi_connections_created;
    ZPN_ATOMIC_UINT64 vdi_connections_freed;
    ZPN_ATOMIC_UINT64 vdi_active_connections_count;
    ZPN_ATOMIC_UINT64 vdi_connections_init_failed;

    /* Machine Tunnel statistics */
    ZPN_ATOMIC_UINT64 machine_tunnel_connections_created;
    ZPN_ATOMIC_UINT64 machine_tunnel_connections_freed;
    ZPN_ATOMIC_UINT64 machine_tunnel_active_connections_count;
    ZPN_ATOMIC_UINT64 machine_tunnel_connections_init_failed;

    /* Zapp Partner statistics */
    ZPN_ATOMIC_UINT64 zapp_partner_connections_created;
    ZPN_ATOMIC_UINT64 zapp_partner_connections_freed;
    ZPN_ATOMIC_UINT64 zapp_partner_active_connections_count;
    ZPN_ATOMIC_UINT64 zapp_partner_connections_init_failed;

    /*
     * 'Other' client type statistics. We can then only implement
     * those client types for which we want the specific statistics
     */

    ZPN_ATOMIC_UINT64 other_client_connections_created;
    ZPN_ATOMIC_UINT64 other_client_connections_freed;
    ZPN_ATOMIC_UINT64 other_client_active_connections_count;
    ZPN_ATOMIC_UINT64 other_client_connections_init_failed;
};

struct zpn_private_broker_mtunnel_stats {

    /* Aggregate pbroker mtunnels statistics */
    ZPN_ATOMIC_UINT64 total_mtunnels_created;
    ZPN_ATOMIC_UINT64 total_mtunnels_freed;
    ZPN_ATOMIC_UINT64 total_mtunnels_reaped_in;
    ZPN_ATOMIC_UINT64 total_mtunnels_reaped_out;
    ZPN_ATOMIC_UINT64 total_mtunnels_active;
    ZPN_ATOMIC_UINT64 total_mtunnels_init_failed;
    ZPN_ATOMIC_UINT64 total_mtunnels_c2c_regex_bypass;
    ZPN_ATOMIC_UINT64 approx_mtunnels_peak_active;

    /* ZCC mtunnel statistics */
    ZPN_ATOMIC_UINT64 zcc_mtunnels_created;
    ZPN_ATOMIC_UINT64 zcc_mtunnels_freed;
    ZPN_ATOMIC_UINT64 zcc_mtunnels_reaped_in;
    ZPN_ATOMIC_UINT64 zcc_mtunnels_reaped_out;
    ZPN_ATOMIC_UINT64 zcc_mtunnels_active;
    ZPN_ATOMIC_UINT64 zcc_mtunnels_init_failed;
    ZPN_ATOMIC_UINT64 zcc_mtunnels_c2c_regex_bypass;

    /* Branch Connector mtunnel statistics */
    ZPN_ATOMIC_UINT64 bc_mtunnels_created;
    ZPN_ATOMIC_UINT64 bc_mtunnels_freed;
    ZPN_ATOMIC_UINT64 bc_mtunnels_reaped_in;
    ZPN_ATOMIC_UINT64 bc_mtunnels_reaped_out;
    ZPN_ATOMIC_UINT64 bc_mtunnels_active;
    ZPN_ATOMIC_UINT64 bc_mtunnels_init_failed;
    ZPN_ATOMIC_UINT64 bc_mtunnels_c2c_regex_bypass;

    /* Edge Connector mtunnel statistics */
    ZPN_ATOMIC_UINT64 ec_mtunnels_created;
    ZPN_ATOMIC_UINT64 ec_mtunnels_freed;
    ZPN_ATOMIC_UINT64 ec_mtunnels_reaped_in;
    ZPN_ATOMIC_UINT64 ec_mtunnels_reaped_out;
    ZPN_ATOMIC_UINT64 ec_mtunnels_active;
    ZPN_ATOMIC_UINT64 ec_mtunnels_init_failed;
    ZPN_ATOMIC_UINT64 ec_mtunnels_c2c_regex_bypass;

    /* VDI mtunnel statistics */
    ZPN_ATOMIC_UINT64 vdi_mtunnels_created;
    ZPN_ATOMIC_UINT64 vdi_mtunnels_freed;
    ZPN_ATOMIC_UINT64 vdi_mtunnels_reaped_in;
    ZPN_ATOMIC_UINT64 vdi_mtunnels_reaped_out;
    ZPN_ATOMIC_UINT64 vdi_mtunnels_active;
    ZPN_ATOMIC_UINT64 vdi_mtunnels_init_failed;
    ZPN_ATOMIC_UINT64 vdi_mtunnels_c2c_regex_bypass;

    /* Machine Tunnel mtunnel statistics */
    ZPN_ATOMIC_UINT64 mt_mtunnels_created;
    ZPN_ATOMIC_UINT64 mt_mtunnels_freed;
    ZPN_ATOMIC_UINT64 mt_mtunnels_reaped_in;
    ZPN_ATOMIC_UINT64 mt_mtunnels_reaped_out;
    ZPN_ATOMIC_UINT64 mt_mtunnels_active;
    ZPN_ATOMIC_UINT64 mt_mtunnels_init_failed;
    ZPN_ATOMIC_UINT64 mt_mtunnels_c2c_regex_bypass;

    /* Zapp pertner mtunnel statistics */
    ZPN_ATOMIC_UINT64 zp_mtunnels_created;
    ZPN_ATOMIC_UINT64 zp_mtunnels_freed;
    ZPN_ATOMIC_UINT64 zp_mtunnels_reaped_in;
    ZPN_ATOMIC_UINT64 zp_mtunnels_reaped_out;
    ZPN_ATOMIC_UINT64 zp_mtunnels_active;
    ZPN_ATOMIC_UINT64 zp_mtunnels_init_failed;
    ZPN_ATOMIC_UINT64 zp_mtunnels_c2c_regex_bypass;

    /*
     * 'Other' client type mtunnel statistics. We can then only implement
     * those client types for which we want the specific mtunnel statistics
     */

    ZPN_ATOMIC_UINT64 other_mtunnels_created;
    ZPN_ATOMIC_UINT64 other_mtunnels_freed;
    ZPN_ATOMIC_UINT64 other_mtunnels_reaped_in;
    ZPN_ATOMIC_UINT64 other_mtunnels_reaped_out;
    ZPN_ATOMIC_UINT64 other_mtunnels_active;
    ZPN_ATOMIC_UINT64 other_mtunnels_init_failed;
    ZPN_ATOMIC_UINT64 other_mtunnels_c2c_regex_bypass;

    /*
    * Misc c2c stat computed during mtunnel handling
    */
    ZPN_ATOMIC_UINT64 c2c_regex_eval_avg_time_us;
};

/*
 * This struct will hold all PSE dispatcher stats
 */
struct zpn_private_broker_dispatcher_stats {
    ZPN_ATOMIC_UINT64 total_cache_dispatch_hit_count;
    ZPN_ATOMIC_UINT64 total_cache_dispatch_miss_count;
    ZPN_ATOMIC_UINT64 total_max_dispatch_exceeded_count;
    ZPN_ATOMIC_UINT64 total_cloud_dispatch_count;
    ZPN_ATOMIC_UINT64 total_cloud_dispatch_fail_count;
    ZPN_ATOMIC_UINT64 local_dispatcher_state_eval_avg_time_us;
    ZPN_ATOMIC_UINT64 local_dispatcher_state_eval_max_time_us;
};
/*
 * This struct will provide an abstraction for the client statistics
 * for a specific type of client. This will allow us to then record
 * the statistics without having to call a function, go through a
 * switch statement to then increment the stat.
 *
 * Holding or copying a pointer to an atomic variable does not make
 * any operations on the pointer atomic automatically.
 *
 * Ref: https://stackoverflow.com/questions/42082219/declaring-atomic-pointers-vs-pointers-to-atomics
 */

struct generic_client_connections_stats {
    ZPN_ATOMIC_UINT64 *client_connections_created;
    ZPN_ATOMIC_UINT64 *client_connections_freed;
    ZPN_ATOMIC_UINT64 *active_client_connections_count;
    ZPN_ATOMIC_UINT64 *client_connections_init_failed;

    ZPN_ATOMIC_UINT64 *total_client_connections_created;
    ZPN_ATOMIC_UINT64 *total_client_connections_freed;
    ZPN_ATOMIC_UINT64 *total_active_client_connections_count;
    ZPN_ATOMIC_UINT64 *total_client_connections_init_failed;
    ZPN_ATOMIC_UINT64 *approx_peak_active_client_connections_count;

};

struct generic_mtunnel_stats {
    ZPN_ATOMIC_UINT64 *client_mtunnels_created;
    ZPN_ATOMIC_UINT64 *client_mtunnels_freed;
    ZPN_ATOMIC_UINT64 *client_mtunnels_reaped_in;
    ZPN_ATOMIC_UINT64 *client_mtunnels_reaped_out;
    ZPN_ATOMIC_UINT64 *client_mtunnels_active;
    ZPN_ATOMIC_UINT64 *client_mtunnels_init_failed;
    ZPN_ATOMIC_UINT64 *client_mtunnels_c2c_regex_bypass;

    ZPN_ATOMIC_UINT64 *total_mtunnels_created;
    ZPN_ATOMIC_UINT64 *total_mtunnels_freed;
    ZPN_ATOMIC_UINT64 *total_mtunnels_reaped_in;
    ZPN_ATOMIC_UINT64 *total_mtunnels_reaped_out;
    ZPN_ATOMIC_UINT64 *total_mtunnels_active;
    ZPN_ATOMIC_UINT64 *total_mtunnels_init_failed;
    ZPN_ATOMIC_UINT64 *total_mtunnels_c2c_regex_bypass;
    ZPN_ATOMIC_UINT64 *approx_mtunnels_peak_active;
};

/*
 * Private broker state - place for common runtime state
 */
struct zpn_private_broker_state {
    int64_t next_restart_time;
    int64_t time_delta_us;
    int     drmode_enabled;
    char    dr_running_mode[DR_RUNNING_MODE_STATUS_LEN];

    /* Local wally states */
    struct wally *wally;
    struct wally_origin *remote_db;
    struct wally_origin *slave_db;
    struct wally_fohh_client *wally_fohh_client_handle;
    struct wally_test_origin *wally_test_origin;
    struct fohh_connection *cfg_fohh_client_handle;

    struct wally *wally_ovd;
    struct wally_test_origin *wally_ovd_test_origin;
    struct wally_origin *remote_db_ovd;
    struct wally_origin *slave_db_ovd;
    struct wally_fohh_client *wally_fohh_client_handle_ovd;
    struct fohh_connection *ovd_fohh_client_handle;

    struct wally *static_wally;
    struct wally_test_origin *static_wally_test_origin;
    struct wally_origin *static_remote_db;
    struct wally_origin *static_slave_db;
    struct wally_fohh_client *static_wally_fohh_client_handle;
    struct fohh_connection *rcfg_fohh_client_handle;

    /* Private broker tables */
    struct zpn_private_broker_version_table *private_broker_version;
    struct zpn_private_broker_table *private_broker_table;

    /* FOHH connection for control channel */
    struct fohh_connection *broker_control;

    /* FOHH connection for broker mission control channel */
    struct fohh_connection *broker_mc;

    /* FOHH connection for sitec control channel */
    struct fohh_connection *sitec_control;

    /* FOHH connection for stats channel */
    struct fohh_connection *broker_stats;

    /* FOHH connection for transaction log channel */
    struct fohh_connection *transaction_log_channel;

    /* FOHH connection for auth log channel */
    struct fohh_connection *auth_log_channel;

    /* FOHH connection for ast auth log channel */
    struct fohh_connection *ast_auth_log_channel;

    /* FOHH connection for event log channel */
    struct fohh_connection *event_log_channel;

    /* FOHH connection for dns log channel */
    struct fohh_connection *dns_log_channel;

    int64_t num_comprehensive_stats_upload;
    int64_t num_comprehensive_stats_upload_fails;

    /* Private broker's connected broker */
    struct zpn_connected_broker *connected_broker;

    /* Private broker's client stats */
    struct zpn_private_broker_client_connection_stats client_connection_stats;
    struct zpn_private_broker_mtunnel_stats mtunnel_stats;

    /* Private broker's dispatcher stats*/
    struct zpn_private_broker_dispatcher_stats dispatcher_stats;
};

void init_mtunnel_stats_ptrs(struct generic_mtunnel_stats *generic_stats, enum zpn_client_type client_type);
void init_client_connection_stats_ptrs(struct generic_client_connections_stats *generic_stats, enum zpn_client_type client_type);

enum zpn_private_broker_pause_reason {
    zpn_private_broker_pause_reason_invalid,
    zpn_private_broker_pause_reason_restart_process,
    zpn_private_broker_pause_reason_restart_system,
    zpn_private_broker_pause_reason_certificate_expiry,
    zpn_private_broker_pause_reason_upgrade_inprogress
};

/*
 * Private broker global state - Global states needed by private broker
 * Note: All strings should have their allocated storage, other code may refer to this storage safely as it stays
 * in scope for the lifetime of the program
 */
struct zpn_private_broker_global_state {
    int magic;
    pthread_mutex_t lock;
    pthread_mutex_t dr_handler_lock;
    char role_name[PRIVATE_BROKER_ROLE_NAME_LEN];
    struct zpn_enroll_state *enroll_state;
    char *dom;
    char *root_cert;
    time_t root_cert_time;
    char cn[PRIVATE_BROKER_CN_LEN];
    char cfg_name[PRIVATE_BROKER_CN_LEN];
    int is_paused;
    enum zpn_private_broker_pause_reason pause_reason;
    int64_t pause_mode_enter_time_cloud_s;
    int64_t pb_restart_time_cloud_s;
    int64_t customer_id;
    int64_t private_broker_id;
    int64_t private_broker_scope_id;
    struct zpn_private_broker_version *private_broker_version;
    struct zpn_private_broker_state *private_broker_state;
    struct zcdns *zcdns;
    uint8_t cfg_instance_bytes[INSTANCE_ID_BYTES];
    uint8_t cfg_fingerprint_bytes[INSTANCE_ID_BYTES];
    char cfg_fingerprint_str[(((INSTANCE_ID_BYTES + 2)/ 3)* 4)+ 1];
    char cfg_provisioning_key[PRIVATE_BROKER_PROV_KEY_LEN];
    int cfg_key_shard;
    char cfg_key_api[PRIVATE_BROKER_KEY_API_LEN];

    zpath_rwlock_t site_config_lock;
    int sitec_preferred;
    int64_t site_gid;
    char offline_domain[ZPN_MAX_DOMAIN_NAME_LEN + 1];
    uint64_t site_reenroll_period;
    int64_t max_allowed_downtime_s;
    int64_t max_allowed_switchtime_s;
    int8_t is_switchtime_enabled;
    int allow_c2site;
    int site_is_active;

    struct zhw_id cfg_hw_id;
    struct zcrypt_key cfg_hw_key;
    EVP_PKEY *cfg_pkey;
    struct zthread_info *zthread;
    int enroll_version;
    int debug_flag;
    SSL_CTX *self_to_broker_ssl_ctx;
    SSL_CTX *self_to_private_ssl_ctx;
    SSL_CTX *self_to_broker_dtls_ctx;
    int64_t pb_start_time_s;
    int64_t pb_start_time_m_s;
    enum zcrypt_metadata_develop_mode develop_certs_mode;
    int64_t cloud_time_delta_us;
    int auto_upgrade_disabled;
    uint32_t host_uptime_s;

    int64_t cert_validity_start_time_cloud_s;
    int64_t cert_validity_end_time_cloud_s;
    int64_t cert_force_re_enroll_time_cloud_s;

    int number_of_hw_id_changed;
    int64_t hw_id_changed_time_us[ZHW_TOTAL_HW_USED];
    struct event *pbroker_monitor_timer;
    struct event *pbroker_firedrill_timer;
    struct event *pbroker_mc_timer;
    struct event *pbroker_malloc_trim_timer;
    uint32_t configured_cpus;
    uint32_t available_cpus;
    uint32_t fohh_threads;
    int swap_config;

    /* Interval in seconds for receiving fohh_status_request messages from broker for various connections */
    int64_t stats_status_interval;
    int64_t ctl_status_interval;
    int64_t pblog_txn_status_interval;
    int64_t pblog_auth_status_interval;
    int64_t pblog_ast_auth_status_interval;
    int64_t pblog_event_status_interval;
    int64_t pblog_dns_status_interval;
    int64_t cfg_status_interval;
    int64_t rcfg_status_interval;
    int64_t ovd_status_interval;
    int64_t userdb_status_interval;

    /* Alt cloud config feature flag state */
    int64_t alt_cloud_feature_state;
    int     alt_cloud_feature_state_initialised; // To make sure, we interpret the feature flag properly

    /* Flag to signal the pbroker_monitor_timer_cb to restart the PSE, because config changed */
    int alt_cloud_feature_state_changed;

    /* Flag to signal the pbroker_monitor_timer_cb to restart the PSE */
    int alt_cloud_name_changed;
    int rdir_alt_cloud_name_changed;

    struct event *recovery_timer;

    /*
     * This is the default cloud name, viz. prod.zpath.net
     * In the absence of any alt_cloud, this will be used as both redir and listener alt cloud names.
     */
    char cfg_key_cloud[PRIVATE_BROKER_CLOUD_NAME_LEN + 1];

    /* This is the cloud name which will be used to set up the listeners which will then service the connections from clients */
    char listener_alt_cloud_name[PRIVATE_BROKER_CLOUD_NAME_LEN + 1];

    /* This is the cloud name which will be used to connect to the broker (pb2br, pb2lbr, pb2slbr) */
    char redir_alt_cloud_name[PRIVATE_BROKER_CLOUD_NAME_LEN + 1];

    /* This would be pbroker-gid.[<cloud_name> | <alt_cloud_name>] */
    char instance_full_name[PRIVATE_BROKER_INSTANCE_FULL_NAME + 1];

    zpath_mutex_t redirect_handler_lock;

    /* System Stats */
    struct zpn_private_broker_system_stats sys_stats;

    const char *sarge_version;

    struct zpn_private_broker_dr_stats dr_stats;
    int cgroup_version;
    int is_container_env;

    uint8_t imds_disabled;
    uint8_t imdsv2_required;
    uint8_t disk_id_fail;

    /* private broker fproxy */
    int64_t fproxy_enabled;                                                     /* _ARGO: integer */

    struct fohh_generic_server *proxy_server;
    pthread_mutex_t proxy_server_lock;
    int proxy_server_initialized;

    /*zpn client version check*/
    struct zpn_version_control_data version_data;
    struct zpath_cloud_config *cloud_config;
    int firedrill_status;
    int64_t firedrill_interval;
    int64_t firedrill_starttime;
    int64_t firedrill_leftover_s;
    int64_t os_upgrade_feature_flag;
    int64_t sarge_upgrade_feature_flag;
    int64_t sarge_backup_version_feature_flag;
    int64_t full_os_upgrade_feature_flag;
    uint64_t last_os_upgrade_time;
    uint64_t last_sarge_upgrade_time;
    char *platform_version;
    struct zpath_upgrade_stats upgrade_stats;

    /* Oauth based enrollment configs */
    int oauth_enroll;
};


extern uint64_t pbroker_debug;
extern uint64_t pbroker_debug_catch_defaults;

#define PBROKER_DEBUG_CONFIG_BIT                 (uint64_t)0x00000001
#define PBROKER_DEBUG_CONN_BIT                   (uint64_t)0x00000002
#define PBROKER_DEBUG_CERT_BIT                   (uint64_t)0x00000004
#define PBROKER_DEBUG_DATA_XFER_BIT              (uint64_t)0x00000008
#define PBROKER_DEBUG_OBJECT_BIT                 (uint64_t)0x00000010
#define PBROKER_DEBUG_AUTH_BIT                   (uint64_t)0x00000020
#define PBROKER_DEBUG_RAW_BIT                    (uint64_t)0x00000040
#define PBROKER_DEBUG_STATS_BIT                  (uint64_t)0x00000080
#define PBROKER_DEBUG_STATS_TX_BIT               (uint64_t)0x00000100
#define PBROKER_DEBUG_REDIRECT_BIT               (uint64_t)0x00000200
#define PBROKER_DEBUG_SITE_BIT                   (uint64_t)0x00000400

/* The following is used to set up more debugging... Add to this list
 * as you add debugging bits. Make sure they stay in sync. */
#define PBROKER_DEBUG_NAMES {                   \
            "config",                           \
            "conn",                             \
            "cert",                             \
            "data",                             \
            "object",                           \
            "auth",                             \
            "raw",                              \
            "stats",                            \
            "stats_tx",                         \
            "redirect",                         \
            "site",                             \
            NULL                                \
            }


#define PBROKER_LOG(priority, format...) if (1) ARGO_LOG(zpath_event_collection, priority, "pbroker", ##format)
#define PBROKER_DEBUG_LOG(condition, format...) if (1) ARGO_DEBUG_LOG(condition, zpath_event_collection, argo_log_priority_debug, "pbroker", ##format)

#define PBROKER_DEBUG_CONFIG(format...) PBROKER_DEBUG_LOG(pbroker_debug & PBROKER_DEBUG_CONFIG_BIT, ##format)
#define PBROKER_DEBUG_CONN(format...) PBROKER_DEBUG_LOG(pbroker_debug & PBROKER_DEBUG_CONN_BIT, ##format)
#define PBROKER_DEBUG_CERT(format...) PBROKER_DEBUG_LOG(pbroker_debug & PBROKER_DEBUG_CERT_BIT, ##format)
#define PBROKER_DEBUG_DATA_XFER(format...) PBROKER_DEBUG_LOG(pbroker_debug & PBROKER_DEBUG_DATA_XFER_BIT, ##format)
#define PBROKER_DEBUG_OBJECT(format...) PBROKER_DEBUG_LOG(pbroker_debug & PBROKER_DEBUG_OBJECT_BIT, ##format)
#define PBROKER_DEBUG_AUTH(format...) PBROKER_DEBUG_LOG(pbroker_debug & PBROKER_DEBUG_AUTH_BIT, ##format)
#define PBROKER_DEBUG_RAW(format...) PBROKER_DEBUG_LOG(pbroker_debug & PBROKER_DEBUG_RAW_BIT, ##format)
#define PBROKER_DEBUG_STATS(format...) PBROKER_DEBUG_LOG(pbroker_debug & PBROKER_DEBUG_STATS_BIT, ##format)
#define PBROKER_DEBUG_STATS_TX(format...) PBROKER_DEBUG_LOG(pbroker_debug & PBROKER_DEBUG_STATS_TX_BIT, ##format)
#define PBROKER_DEBUG_REDIRECT(format...) PBROKER_DEBUG_LOG(pbroker_debug & PBROKER_DEBUG_REDIRECT_BIT, ##format)
#define PBROKER_DEBUG_SITE(format...) PBROKER_DEBUG_LOG(pbroker_debug & PBROKER_DEBUG_SITE_BIT, ##format)

extern struct zpath_allocator pbroker_allocator;
#define PBROKER_MALLOC(x) zpath_malloc(&pbroker_allocator, x, __LINE__, __FILE__)
#define PBROKER_FREE(x) zpath_free(x, __LINE__, __FILE__)
#define PBROKER_FREE_SLOW(x) zpath_free_slow(x, __LINE__, __FILE__)
#define PBROKER_CALLOC(x) zpath_calloc(&pbroker_allocator, x, __LINE__, __FILE__)
#define PBROKER_STRDUP(x, y) zpath_strdup(&pbroker_allocator, x, y, __LINE__, __FILE__)

#endif
