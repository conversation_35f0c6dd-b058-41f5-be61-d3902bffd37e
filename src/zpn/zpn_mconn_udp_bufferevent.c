/*
 * zpn_mconn_udp.c. Copyright (C) 2015 Zscaler Inc. All Rights Reserved.
 */

#include <event2/event.h>
#include <event2/bufferevent_ssl.h>
#include "fohh/fohh.h"
#include <netinet/udp.h>
#include <fcntl.h>
#include <errno.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>

#include "zpn/zpn_mconn_udp_bufferevent.h"
#include "zudp_conn/zudp_conn.h"
#include "zpn/zpn_lib.h"

void mconn_udp_bufferevent_log (int priority,
                                const char *format_str,
                                va_list list)
{
    char dump[2000];
    vsnprintf(dump, sizeof(dump), format_str, list);
    if (priority < argo_log_priority_notice) {
        ZPN_LOG(priority, "%s", dump);
    } else {
        ZPN_LOG(AL_DEBUG, "%s", dump);
    }
}
struct evbuffer *udp_packetize(char *buf, ssize_t len, uint16_t src, uint16_t dst)
{
    struct evbuffer *evbuf = NULL;

    //ZPN_LOG(AL_DEBUG, "udp_packetize(), len = %d, sport = %d, dport = %d", len, src, dst);

    evbuf = evbuffer_new();
    if (evbuf) {
		evbuffer_set_dont_dump(evbuf);
        struct udphdr header;

#if (defined(__APPLE__) && defined(__MACH__)) || defined(__FreeBSD__)
        header.uh_sport = htons(src);
        header.uh_dport = htons(dst);
        header.uh_ulen = htons(len + sizeof(struct udphdr));
        header.uh_sum = 0xcafe;
#else
        header.source = htons(src);
        header.dest= htons(dst);
        header.len = htons(len + sizeof(struct udphdr));
        header.check = 0xcafe;
#endif

#if 0   /* UDP framing test: add extra bytes */
        {
            int new_len = 0;
#if (defined(__APPLE__) && defined(__MACH__)) || defined(__FreeBSD__)
            header.uh_ulen = new_len;
#else
            header.len = new_len;
#endif
        }
#endif

        evbuffer_add(evbuf, (char *)&header, sizeof(struct udphdr));
        if (len) {
            evbuffer_add(evbuf, buf, (size_t)len);
        }
    } else {
        ZPN_LOG(AL_WARNING, "Cannot allocate evbuffer during udp socket receive");
    }

    return evbuf;
}

struct evbuffer *udp_extract_packet(struct evbuffer *evbuf)
{
    unsigned char *mem;
    struct evbuffer *pktbuf = NULL;

    mem = evbuffer_pullup(evbuf, sizeof(struct udphdr));
    if (mem) {
        struct udphdr *header = (struct udphdr *)mem;
#if (defined(__APPLE__) && defined(__MACH__)) || defined(__FreeBSD__)
        size_t payload_len = ntohs(header->uh_ulen);
#else
        size_t payload_len = ntohs(header->len);
#endif

        //ZPN_LOG(AL_DEBUG, "Header: 0x%08lx, buffer length = %ld, udp header (payload length) = %d",
        //        *(unsigned long *)&mem[0],
        //        (long) evbuffer_get_length(evbuf),
        //        payload_len);

        if (evbuffer_get_length(evbuf) >= payload_len) {
            pktbuf = evbuffer_new();
            if (pktbuf) {
				evbuffer_set_dont_dump(pktbuf);
                /* Copy the whole packet over */
                evbuffer_remove_buffer(evbuf, pktbuf, payload_len);
                /* Remove the header */
                evbuffer_drain(pktbuf, sizeof(struct udphdr));
            } else {
                ZPN_LOG(AL_NOTICE, "Cannot allocate evbuffer to extract packet");
            }
        }
    }

    return pktbuf;
}

static void mconn_udp_drop_peer_packet(struct zpn_mconn_udp_bufferevent *mconn_udp)
{
    size_t dropped_len = 0;

    /* !!! We should not come in here if mtunnel is double encrypted !!! */

    if (mconn_udp->mconn.peer && zpn_mconn_get_transmit_buffer(mconn_udp->mconn.peer)) {
        struct evbuffer *peer_tx_buf = zpn_mconn_get_transmit_buffer(mconn_udp->mconn.peer);

        while (evbuffer_get_length(peer_tx_buf) > ZPN_MCONN_MAX_CLIENT_TX_DATA) {
            struct evbuffer *pkt = NULL;

            pkt = udp_extract_packet(peer_tx_buf);
            if (!pkt) {
                ZPN_DEBUG_MCONN("Cannot extract packet from peer transmit buffer");
                break;
            }

            dropped_len += evbuffer_get_length(pkt) + sizeof(struct udphdr);
            evbuffer_free(pkt);
        }
    }

    if (dropped_len) {
        ZPN_DEBUG_MCONN("Dropped %d bytes of UDP data due to flow control", (int)dropped_len);
        mconn_udp->dropped_bytes += dropped_len;
    }
}

static void mconn_udp_bufferevent_read_cb_deferred(void *cookie1, void *cookie2)
{
    struct zpn_mconn_udp_bufferevent *mconn_udp;
    struct zpn_mconn *mconn;
    ssize_t n_byte = 0;
    char buffer[MAX_UDP_PACKET];
    struct evbuffer *evbuf;
    int res = ZPN_RESULT_NO_ERROR;
    int double_encrypt = 0;
    struct zudp_sock *udp_sock;
    struct zudp_conn *udp_conn;
    struct zudp_conn_4tuple conn_4tuple;
    struct sockaddr_storage addr;
    socklen_t addr_len;
    char tmpbuf[1024];

    udp_sock = cookie1;

    addr_len = sizeof(addr);
    memset(&addr, 0, addr_len);
    if ((n_byte = recvfrom(udp_sock->sock, buffer, MAX_UDP_PACKET, 0,
                                (struct sockaddr *)&addr, &addr_len)) < 0) {
        return;
    }
    memcpy(&(conn_4tuple.src_addr), &(udp_sock->src_addr),
                                            sizeof(conn_4tuple.src_addr));
    memcpy(&(conn_4tuple.dst_addr), &addr, sizeof(conn_4tuple.dst_addr));

    zudp_sockaddr_storage_with_port_to_str(&(conn_4tuple.src_addr), &(conn_4tuple.dst_addr), tmpbuf, sizeof(tmpbuf) - 1 );
    if (!(udp_conn = zudp_conn_table_lookup_udp_conn(&conn_4tuple))) {
        ZPN_LOG(AL_ERROR, "Lookup fail %s", tmpbuf);
        return;
    }

    mconn_udp = udp_conn->context;
    if (mconn_udp == NULL) {
        ZPN_LOG(AL_ERROR, "The mconn is old and the connection is reused %s\n", tmpbuf);
        return;
    }
    ZPN_DEBUG_MCONN("Read cb %s context %p", tmpbuf, mconn_udp);
    mconn = &(mconn_udp->mconn);
    if (mconn->global_owner) {
        (mconn->global_owner_calls->lock)(mconn,
                                          mconn->self,
                                          mconn->global_owner,
                                          mconn->global_owner_key,
                                          mconn->global_owner_key_length);
    } else {
        return;
    }

    mconn_udp->last_rx_epoch_s = epoch_s();
    mconn_udp->rx_bytes += n_byte;

    //ZPN_LOG(AL_DEBUG, "mconn_udp_bufferevent_read_cb, fd is %d, received %d bytes, buffer is %s\n", (int)fd, n_byte, buffer);

    /* Drop the packet if we are rx paused */
    if (mconn_udp->rx_paused) {
        mconn_udp->dropped_bytes += n_byte;
        goto exit;
    }

    /* Drop the packet if it is too big to be transfered over fohh */
    if (n_byte > (ZPN_MCONN_MTU - 100)) {
        mconn_udp->dropped_bytes += n_byte;
        goto exit;
    }

    struct sockaddr_storage *src_addr = zudp_conn_get_srcaddr_from_connection(mconn_udp->udp_conn);
    if (!src_addr) {
        ZPN_LOG(AL_ERROR, "Source address invalid context %p", mconn_udp);
        goto exit;
    }
    evbuf = udp_packetize(buffer, n_byte, sockaddr_get_port_he(&mconn_udp->addr), sockaddr_get_port_he(src_addr));
    if (!evbuf) {
        ZPN_LOG(AL_WARNING, "Cannot allocate evbuffer during udp socket receive");
        goto exit;
    }

    //ZPN_LOG(AL_DEBUG, "mconn_udp_bufferevent_read_cb, formed evbuffer udp packet, len = %d", (int)evbuffer_get_length(evbuf));

    if (evbuffer_get_length(evbuf)) {
        res = zpn_client_process_rx_data(&(mconn_udp->mconn), evbuf, evbuffer_get_length(evbuf), NULL, NULL);
        if (res) {
            ZPN_DEBUG_MCONN("Process_rx data returned %s", zpn_result_string(res));
        }
    }

    /* Need to free the evbuf since we are done with it */
    evbuffer_free(evbuf);

    double_encrypt = (mconn->global_owner_calls->double_encrypt)(mconn,
                                                                 mconn->self,
                                                                 mconn->global_owner,
                                                                 mconn->global_owner_key,
                                                                 mconn->global_owner_key_length);

    /* Check to see if we need to drop any packet due to overflow */
    if (!double_encrypt) {
        mconn_udp_drop_peer_packet(mconn_udp);
    }

exit:
    if (mconn->global_owner) {
        (mconn->global_owner_calls->unlock)(mconn,
                                            mconn->self,
                                            mconn->global_owner,
                                            mconn->global_owner_key,
                                            mconn->global_owner_key_length);
    }
}

static void mconn_udp_bufferevent_read_cb(evutil_socket_t fd, short int what, void *pargs)
{
    zevent_defer(mconn_udp_bufferevent_read_cb_deferred, pargs, NULL, 0);
}

int zpn_mconn_udp_bufferevent_init(struct zpn_mconn_udp_bufferevent *mconn_udp, void *mconn_self, enum zpn_mconn_type type)
{
    mconn_udp->last_rx_epoch_s = epoch_s();
    mconn_udp->last_tx_epoch_s = epoch_s();
    return zpn_mconn_init(&(mconn_udp->mconn), mconn_self, type);
}

static int zpn_mconn_udp_bufferevent_bind_cb(void *mconn_base,
                                        void *mconn_self,
                                        void *owner,
                                        void *owner_key,
                                        size_t owner_key_length,
                                        int64_t *owner_incarnation)
{
    struct zpn_mconn_udp_bufferevent *mconn_udp = mconn_base;
    struct event_base *base;
    char tmpbuf[1024];

    mconn_udp->tx_queue = evbuffer_new();
    if (!mconn_udp->tx_queue) {
        ZPN_LOG(AL_ERROR, "Cannot allocate tx_queue for UDP mconn bufferevent");
        return ZPN_RESULT_NO_MEMORY;
    }
    evbuffer_set_dont_dump(mconn_udp->tx_queue);

    mconn_udp->addr = *(struct sockaddr_storage *)owner;
    base = fohh_get_thread_event_base(mconn_udp->mconn.fohh_thread_id);

    struct sockaddr *tmp_addr = (struct sockaddr *)&(mconn_udp->addr);
    int af = tmp_addr->sa_family;
    int traffic_type = ZUDP_CONN_TRAFFIC_MCONN;
    mconn_udp->udp_conn = zudp_conn_table_create_udp_conn(
            mconn_udp->addr, mconn_udp, NULL, base, mconn_udp_bufferevent_read_cb, &(mconn_udp->udp_socket_errno), mconn_udp_bufferevent_log, af, traffic_type);
    if (mconn_udp->udp_conn == NULL) {
        char ip_addr[INET6_ADDRSTRLEN];
        struct sockaddr *tmp_addr = (struct sockaddr *)&mconn_udp->addr;
        int af = tmp_addr->sa_family;
        void *src = (af == AF_INET) ?
                        (void *)&((struct sockaddr_in *)tmp_addr)->sin_addr :
                        (void *)&((struct sockaddr_in6 *)tmp_addr)->sin6_addr;
        ZPN_LOG(AL_ERROR, "Cannot allocate udp_conn for ip %s port %d - %s",
                inet_ntop(af, src, ip_addr, sizeof(ip_addr)) ? ip_addr : "(null)",
                (af== AF_INET)? ntohs(((struct sockaddr_in *)tmp_addr)->sin_port): ntohs(((struct sockaddr_in6 *)tmp_addr)->sin6_port),
                strerror(errno));
        return ZPN_RESULT_NO_MEMORY;
    }
    zudp_sockaddr_storage_with_port_to_str(&(((mconn_udp->udp_conn)->conn_4tuple).src_addr),  &(((mconn_udp->udp_conn)->conn_4tuple).dst_addr), tmpbuf, sizeof(tmpbuf) - 1 );
    ZPN_DEBUG_MCONN("Create conn mconn %s %p", tmpbuf, mconn_udp);

    return ZPN_RESULT_NO_ERROR;
}

static int zpn_mconn_udp_bufferevent_unbind_cb(void *mconn_base,
                                          void *mconn_self,
                                          void *owner,
                                          void *owner_key,
                                          size_t owner_key_length,
                                          int64_t owner_incarnation,
                                          int drop_buffered_data,
                                          int dont_propagate,
                                          const char *err)
{
    struct zpn_mconn_udp_bufferevent *mconn_udp = mconn_base;
    char tmpbuf[1024];

    zudp_sockaddr_storage_with_port_to_str(&(((mconn_udp->udp_conn)->conn_4tuple).src_addr),  &(((mconn_udp->udp_conn)->conn_4tuple).dst_addr),
                                           tmpbuf, sizeof(tmpbuf) - 1 );
    ZPN_DEBUG_MCONN("destroy cb %s mconn udp %p", tmpbuf, mconn_udp);
    zudp_conn_table_destroy_udp_conn(mconn_udp->udp_conn);
    mconn_udp->udp_conn = NULL;

    if (mconn_udp->tx_queue) {
        evbuffer_free(mconn_udp->tx_queue);
        mconn_udp->tx_queue = NULL;
    }

    return ZPN_RESULT_NO_ERROR;
}

static void zpn_mconn_udp_bufferevent_lock_cb(void *mconn_base,
                                         void *mconn_self,
                                         void *owner,
                                         void *owner_key,
                                         size_t owner_key_length)
{
    //ZPN_LOG(AL_DEBUG, "zpn_mconn_udp_bufferevent_lock_cb()");
    return;
}


static void zpn_mconn_udp_bufferevent_unlock_cb(void *mconn_base,
                                           void *mconn_self,
                                           void *owner,
                                           void *owner_key,
                                           size_t owner_key_length)
{
    //ZPN_LOG(AL_DEBUG, "zpn_mconn_udp_bufferevent_unlock_cb()");
    return;
}

static int zpn_mconn_udp_bufferevent_transmit_cb(void *mconn_base,
                                            void *mconn_self,
                                            void *owner,
                                            void *owner_key,
                                            size_t owner_key_length,
                                            int64_t owner_incarnation,
                                            int fohh_thread_id,
                                            struct evbuffer *buf,
                                            size_t buf_len)
{
    struct zpn_mconn_udp_bufferevent *mconn_udp = mconn_base;
    int res = ZPN_RESULT_NO_ERROR;

   ZPN_DEBUG_MCONN("Send data. buf_len = %ld", (long) buf_len);

    mconn_udp->last_tx_epoch_s = epoch_s();

    /* XXX Implement Me- Backoff on large buffering. */
    if (mconn_udp->tx_paused) {
        ZPN_DEBUG_MCONN("zpn_mconn_udp_bufferevent tx paused");
        return ZPN_RESULT_WOULD_BLOCK;
    }

    if (mconn_udp->udp_conn) {

        struct evbuffer *pktbuf = NULL;
        int enq_len = 0;
        int dropped_len = 0;

        while (evbuffer_get_length(buf) &&
               ((pktbuf = udp_extract_packet(buf)) != NULL)) {

            int len = (int)evbuffer_get_length(pktbuf);
            unsigned char *pkt = NULL;
            unsigned char foo = 0;

            ZPN_DEBUG_MCONN("Sending packet of %d bytes", (int)evbuffer_get_length(pktbuf));

            if (len) {
                pkt = evbuffer_pullup(pktbuf, evbuffer_get_length(pktbuf));
            } else {
                pkt = &foo;
            }
            if (pkt) {
                size_t pktlen = 0;
                int sendto_errno = 0;
                pktlen = evbuffer_get_length(pktbuf);
                if (zudp_conn_table_sendto_udp_conn(mconn_udp->udp_conn,
                                                    pkt, pktlen, &sendto_errno) <= 0) {
                    dropped_len += (len + sizeof(struct udphdr));
                    /* Log error only when errno != 0 */
                    if (sendto_errno) {
                        ZPN_LOG(AL_WARNING, "bev tx failed - %s, Dropping the packet dropped len = %d", strerror(sendto_errno), dropped_len);
                    }

                    /* Number of bytes transmitted by sendto is zero, means udp socket */
                    /* send buffer is full. so drop the packet */
                    evbuffer_free(pktbuf);
                    continue;
                } else {
                    //ZPN_LOG(AL_DEBUG, "zpn_mconn_udp_bufferevent transmitted");
                }
            } else {
                ZPN_LOG(AL_WARNING, "zpn_mconn_udp_bufferevent cannot pullup pktbuf");
            }

            mconn_udp->mconn.bytes_to_client += (len + sizeof(struct udphdr));
            mconn_udp->tx_bytes += (len + sizeof(struct udphdr));
            enq_len += (len + sizeof(struct udphdr));

            evbuffer_free(pktbuf);
        }

        if (((enq_len > 0) || (dropped_len > 0)) && mconn_udp->mconn.peer) {
            ZPN_DEBUG_MCONN("window update, enq_len = %d, dropped_len = %d", enq_len, dropped_len);
            zpn_mconn_client_window_update(mconn_udp->mconn.peer, 0, (enq_len + dropped_len), 1);
        } else if(((enq_len > 0) || (dropped_len > 0)) && !mconn_udp->mconn.peer) {
            ZPN_DEBUG_MCONN("%p UDP dequeue tx_buffer but not able to update peer enq_len %d, dropped_len %d", mconn_udp, enq_len, dropped_len);
        }

    }

    return res;
}

static int zpn_mconn_udp_bufferevent_pause_cb(void *mconn_base,
                                         void *mconn_self,
                                         void *owner,
                                         void *owner_key,
                                         size_t owner_key_length,
                                         int64_t owner_incarnation,
                                         int fohh_thread_id)
{
    struct zpn_mconn_udp_bufferevent *mconn_udp = mconn_base;
    struct zpn_mconn *mconn = &(mconn_udp->mconn);
    int double_encrypt = 0;

    if (mconn->global_owner_calls) {
        double_encrypt = (mconn->global_owner_calls->double_encrypt)(mconn,
                                                                     mconn->self,
                                                                     mconn->global_owner,
                                                                     mconn->global_owner_key,
                                                                     mconn->global_owner_key_length);
    }

    /*
     * We pause UDP stream only when it is double encrypted, in which case we drop packet as it arrives
     * For non double encrypted, we just drop head of line packet instead of newly arrived packet
     */
    if (double_encrypt) {
        mconn_udp->rx_paused = 1;
    }

    return ZPN_RESULT_NO_ERROR;
}

static int zpn_mconn_udp_bufferevent_resume_cb(void *mconn_base,
                                          void *mconn_self,
                                          void *owner,
                                          void *owner_key,
                                          size_t owner_key_length,
                                          int64_t owner_incarnation,
                                          int fohh_thread_id)
{
    struct zpn_mconn_udp_bufferevent *mconn_udp = mconn_base;
    struct zpn_mconn *mconn = &(mconn_udp->mconn);
    int double_encrypt = 0;

    if (mconn->global_owner_calls) {
        double_encrypt = (mconn->global_owner_calls->double_encrypt)(mconn,
                                                                     mconn->self,
                                                                     mconn->global_owner,
                                                                     mconn->global_owner_key,
                                                                     mconn->global_owner_key_length);
    }

    /*
     * We pause UDP stream only when it is double encrypted, in which case we drop packet as it arrives
     * For non double encrypted, we just drop head of line packet instead of newly arrived packet
     */
    if (double_encrypt) {
        mconn_udp->rx_paused = 0;
    }

    return ZPN_RESULT_NO_ERROR;
}

int zpn_mconn_udp_bufferevent_forward_tunnel_end_cb(void *mconn_base,
                                               void *mconn_self,
                                               void *owner,
                                               void *owner_key,
                                               size_t owner_key_length,
                                               int64_t owner_incarnation,
                                               const char *err,
                                               int32_t drop_data)
{
    struct zpn_mconn_udp_bufferevent *mconn_udp = mconn_base;

    //ZPN_LOG(AL_DEBUG, "zpn_mconn_udp_bufferevent_forward_tunnel_end_cb()");

    mconn_udp->mconn.fin_rcvd = 1;
    mconn_udp->mconn.fin_sent = 1;

    /* Reflect the tunnel end if not already */
    if (!mconn_udp->mconn.fin_refl) {
        zpn_mconn_forward_mtunnel_end(mconn_udp->mconn.peer, MT_CLOSED_TERMINATED, drop_data);
        mconn_udp->mconn.fin_refl = 1;
    }

    return ZPN_RESULT_NO_ERROR;
}

/*
 * This is usually called when our peer has sent out some data and we want to
 * see if we need to update the tx_limit at the remote end of fohh connection
 */
void zpn_mconn_udp_bufferevent_window_update_cb(void *mconn_base,
                                                void *mconn_self,
                                                void *owner,
                                                void *owner_key,
                                                size_t owner_key_length,
                                                int64_t owner_incarnation,
                                                int fohh_thread_id,
                                                int tx_len,
                                                int batch_win_upd)
{
    return;
}

void zpn_mconn_udp_bufferevent_stats_update_cb(void *mconn_base,
                                               void *mconn_self,
                                               void *owner,
                                               void *owner_key,
                                               size_t owner_key_length,
                                               int64_t owner_incarnation,
                                               int fohh_thread_id,
                                               enum zpn_mconn_stats stats_name)
{
    return;
}

static int zpn_mconn_udp_bufferevent_disable_read_cb(void *mconn_base,
                                         void *mconn_self,
                                         void *owner,
                                         void *owner_key,
                                         size_t owner_key_length,
                                         int64_t owner_incarnation,
                                         int fohh_thread_id)
{
    return ZPN_RESULT_NO_ERROR;
}
static int zpn_mconn_udp_bufferevent_enable_read_cb(void *mconn_base,
                                         void *mconn_self,
                                         void *owner,
                                         void *owner_key,
                                         size_t owner_key_length,
                                         int64_t owner_incarnation,
                                         int fohh_thread_id)
{
    return ZPN_RESULT_NO_ERROR;
}

/*
 * Callbacks exposed to mconn
 */
const struct zpn_mconn_local_owner_calls mconn_udp_bufferevent_calls = {
    zpn_mconn_udp_bufferevent_bind_cb,
    zpn_mconn_udp_bufferevent_unbind_cb,
    zpn_mconn_udp_bufferevent_lock_cb,
    zpn_mconn_udp_bufferevent_unlock_cb,
    zpn_mconn_udp_bufferevent_transmit_cb,
    zpn_mconn_udp_bufferevent_pause_cb,
    zpn_mconn_udp_bufferevent_resume_cb,
    zpn_mconn_udp_bufferevent_forward_tunnel_end_cb,
    zpn_mconn_udp_bufferevent_window_update_cb,
    zpn_mconn_udp_bufferevent_stats_update_cb,
    zpn_mconn_udp_bufferevent_disable_read_cb,
    zpn_mconn_udp_bufferevent_enable_read_cb
};

int zpn_mconn_udp_bufferevent_done(struct zpn_mconn_udp_bufferevent *mconn_udp)
{
    int64_t current_epoch_s = epoch_s();
    int64_t udp_timeout_s;
    int32_t udp_port = sockaddr_get_port_he(&(mconn_udp->addr));
    if ((udp_port == 88) ||     // Kerberos
        (udp_port == 123) ||    // NTP
        (udp_port == 161) ||    // SNMP
        (udp_port == 389)) {    // LDAP
        /* Use fast timeout; one default */
        udp_timeout_s = mconn_udp->mconn.udp_fast_timeout_s ? mconn_udp->mconn.udp_fast_timeout_s : UDP_FAST_TIMEOUT_S;
    } else {
        /* Use slow timeout; one default */
        udp_timeout_s = mconn_udp->mconn.udp_timeout_s ? mconn_udp->mconn.udp_timeout_s : UDP_TIMEOUT_S;
    }
    if (((current_epoch_s - mconn_udp->last_rx_epoch_s) > udp_timeout_s) &&
        ((current_epoch_s - mconn_udp->last_tx_epoch_s) > udp_timeout_s)) {
        ZPN_DEBUG_MCONN("mconn udp bufferevent done timeout %p: udp_port: %d; udp_timeout_s: %"PRId64,
                        mconn_udp, udp_port, udp_timeout_s);
        if (mconn_udp->mconn.peer) {
            zpn_mconn_forward_mtunnel_end(mconn_udp->mconn.peer, MT_CLOSED_TERMINATED, 1);
        }

        mconn_udp->mconn.fin_rcvd = 1;
        mconn_udp->mconn.fin_sent = 1;

        return 1;
    }

    return 0;
}

int zpn_mconn_udp_bufferevent_clean(struct zpn_mconn_udp_bufferevent *mconn_udp)
{
    if (mconn_udp->udp_conn || mconn_udp->tx_queue) {
        ZPN_DEBUG_MCONN("mconn udp bufferevent not clean, udp_conn = %p, tx_queue = %p", mconn_udp->udp_conn, mconn_udp->tx_queue);
        return 0;
    }

    if (!zpn_mconn_clean(&(mconn_udp->mconn))) {
        ZPN_DEBUG_MCONN("mconn udp bufferevent not clean");
        return 0;
    }

    ZPN_DEBUG_MCONN("mconn udp bufferevent clean");

    return 1;
}

void zpn_mconn_udp_bufferevent_internal_display(struct zpn_mconn_udp_bufferevent *mconn_udp)
{
    if (! mconn_udp) {
        ZPN_DEBUG_MCONN("mconn_udp is NULL, returning !");
        return;
    }

    if (! mconn_udp->tx_queue) {
        ZPN_DEBUG_MCONN("output buffer mconn_udp->tx_queue is NULL, returning !");
        return;
    }

    ZPN_DEBUG_MCONN("output buffer len = %d", (int)evbuffer_get_length(mconn_udp->tx_queue));
    ZPN_DEBUG_MCONN("rx_paused = %d, tx_paused = %d", mconn_udp->rx_paused, mconn_udp->tx_paused);
    ZPN_DEBUG_MCONN("rx_bytes = %ld, tx_bytes = %ld, dropped_bytes = %ld",
                    (long)mconn_udp->rx_bytes, (long)mconn_udp->tx_bytes, (long)mconn_udp->dropped_bytes);

    zpn_mconn_internal_display(&(mconn_udp->mconn));
}
