add_library(
    zpn_broker_client
    STATIC
    ../../zpn_broker_client.c
    ../../zpn_broker_client_apps.c
    ../../zpn_broker_client_apps_build.c
    ../../zpn_broker.c
    ../../zpn_aae_profile_conclusion.c
    ../../zpn_app_group_relation.c
    ../../zpn_application_server.c
    ../../zpn_application_domain.c
    ../../zpn_application_group_application_mapping.c
    ../../zpn_application_group.c
    ../../zpn_application.c
    ../../zpn_assistant_group.c
    ../../zpn_assistant_table.c
    ../../zpn_assistant_version.c
    ../../zpn_assistantgroup_assistant_relation.c
    ../../zpn_c2c_client_registration.c
    ../../zpn_client_table.c
    ../../zpn_customer.c
    ../../zpn_idp_cert.c
    ../../zpn_idp.c
    ../../zpn_issuedcert.c
    ../../zpn_location_group_to_location.c
    ../../zpn_machine_group.c
    ../../zpn_machine_table.c
    ../../zpn_customer_config.c
    ../../zpn_machine_to_group.c
    ../../zpn_policy_set.c
    ../../zpn_posture_profile.c
    ../../zpn_pbroker_group.c
    ../../zpn_private_broker_table.c
    ../../zpn_private_broker_load_table.c
    ../../zpn_pbroker_to_group.c
    ../../zpn_private_broker_version.c
    ../../zpn_privatebrokergroup_trustednetwork_mapping.c
    ../../zpn_rule_condition_operand.c
    ../../zpn_rule_condition_set.c
    ../../zpn_rule.c
    ../../zpn_rule_to_assistant_group.c
    ../../zpn_rule_to_server_group.c
    ../../zpn_saml_attrs.c
    ../../zpn_scim_attr_header.c
    ../../zpn_scim_group.c
    ../../zpn_scim_user_attribute.c
    ../../zpn_scim_user_group.c
    ../../zpn_server_group_assistant_group.c
    ../../zpn_server_group.c
    ../../zpn_servergroup_server_relation.c
    ../../zpn_shared_customer_domain.c
    ../../zpn_signing_cert.c
    ../../zpn_trusted_network.c
    ../../zpn_user_risk.c
    ../../zpn_workload_tag_group.c
    ../../zpn_znf_group.c
    ../../zpn_znf.c
    ../../zpn_znf_to_group.c
    ../../zpn_broker_transit.c
    ../../zpn_rule_to_location.c
    ../../zpn_scim_user.c
    ../../zpn_broker_ipars.c
    ../../zpn_rpc.c
    ../../zpn_rule_to_location_group.c
    ../../zpn_fohh_client_exporter.c
    ${generated_headers}
)
target_link_libraries(
    zpn_broker_client
    PUBLIC argo zpath_lib wally zcrypto_lib zpn_eas zpath_app
)
create_testing_library(LIBRARY zpn_broker_client)

set(test_files zpn_broker_client_post_verify_region_dynamic_client_check_cb_tests.cpp)

set(tests ${test_files})
list(TRANSFORM tests REPLACE ".cpp" "")

add_simple_apps(
    SOURCES ${test_files}
    DEPS GTest::gtest_main GTest::gmock zpn_broker_client_testing zpn_region_restriction_testing
)
add_simple_tests(${tests})

add_simple_apps(
    SOURCES zpn_broker_client_send_aggregated_domain_per_thread_tests.cpp
    DEPS GTest::gtest_main GTest::gmock zpn_broker_client_testing
)
add_simple_tests(zpn_broker_client_send_aggregated_domain_per_thread_tests)

add_subdirectory(zpn_broker_client_should_disconnect_restricted_user_tests)
