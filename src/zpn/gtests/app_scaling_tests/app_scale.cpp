/*
 * app_scale.cpp Copyright (C) 2025 Zscaler, Inc. All Rights Reserved
 */
#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include "zpath_misc/zpath_version.h"

#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wunused-parameter"
#pragma clang diagnostic ignored "-Wwritable-strings"
#elif defined __GNUC__
#pragma GCC diagnostic push
#pragma GCC diagnostic ignored "-Wunused-parameter"
#pragma GCC diagnostic ignored "-Wwrite-strings"
#endif  // __clang__

using testing::Eq;

extern "C" {
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <netinet/in.h>
#include <pthread.h>
#include <syslog.h>
#include <signal.h>
#include "fohh/fohh.h"
#include "fohh/fohh_log.h"
#include "fohh/fohh_private.h"
#include "argo/argo_log.h"
#include "zpn/zpn_lib.h"
#include "zevent/zevent.h"
#include "zhash/zhash_table.h"
#include "base64/base64.h"
#include <openssl/rand.h>
#include "zpn/zpn_broker_client.h"
#include "zpn/zpn_eas/zpn_eas.h"

#include "zpath_lib/zpath_cloud.h"
#include "zpath_lib/zpath_customer.h"
#include "zpath_lib/zpath_local.h"
#include "zpath_misc/zpath_misc.h"
#include "zpn/zpn_policy_engine.h"
#include "zpath_lib/zpath_config_override_desc.h"
#include "zpath_lib/zpath_config_override_keys.h"

int zpn_broker_dispatch_init_for_unit_tests();
void zpn_policy_enable_app_scaling_feature_test();

#ifdef SIPA_APP_SCALE_CODE_ENABLED
int zpn_sipa_app_scaling_enabled(int64_t customer_gid);
int zpn_sipa_app_scaling_enabled_status_for_customer(int64_t customer_gid);
#endif

struct customer_state *get_customer_state(int64_t customer_gid, void *cookie);
struct argo_object *zpn_broker_client_apps_copy_full_app_data(struct zpn_application *src);

extern int zpn_policy_app_scaling_flag_test;
}

extern struct wally_index_column **zpn_application_index_column;
extern struct zpn_fohh_worker_state *zpn_fohh_workers;
struct zpn_common_broker_cfg g_broker_cfg;
unsigned f_debug = 0;

class TestEnvironment : public ::testing::Environment {
  public:
    // Initialise the timestamp.
    virtual void SetUp() {
        int res = argo_library_init(512);
        ASSERT_TRUE(ZPN_RESULT_NO_ERROR == res);

        // prints to stdout/err
        argo_log_use_printf(f_debug ? 3 : 1);

        struct zpath_simple_app_init_params app_params;
        zpath_simple_app_init_params_default(&app_params);
        app_params.instance_name = app_params.role_name = "appscale";
        app_params.debug_port = 8010;

        res = zpath_simple_app_init(&app_params);

        ASSERT_TRUE(ZPN_RESULT_NO_ERROR == res);
        // create global context
        mock_create_global_config();

        zpn_rpc_init();

        zpath_customer_init(NULL, 0, 0, 0);

        g_broker_cfg.instance_type = ZPN_INSTANCE_TYPE_PUBLIC_BROKER;

        g_broker_common_cfg = &g_broker_cfg;

        zpe_init(1);
        zpn_broker_drain_init();

        for (size_t i = 0; i < ZPN_BROKER_BUCKETS; i++) {
            struct zpn_broker_bucket *bucket = &(broker.buckets[i]);
            pthread_mutexattr_t mattr;

            pthread_mutexattr_init(&mattr);
            pthread_mutexattr_settype(&mattr, PTHREAD_MUTEX_RECURSIVE);
            pthread_mutex_init(&(bucket->lock), &mattr);

            bucket->reaped_start_us = 0;
            bucket->reaped_max_count = 0;
            bucket->reaped_max_null_count = 0;

            bucket->mtunnel_by_id = argo_hash_alloc(7, 1);
            ASSERT_TRUE(bucket->mtunnel_by_id);

            bucket->f_conn_by_tunnel_id = argo_hash_alloc(7, 1);
            ASSERT_TRUE(bucket->f_conn_by_tunnel_id);

            TAILQ_INIT(&(bucket->mtunnel_list));
            TAILQ_INIT(&(bucket->reaped_list));
        }

        res = zpn_broker_dispatch_init_for_unit_tests();
        ASSERT_EQ(res, ZPN_RESULT_NO_ERROR);

        zpn_policy_enable_app_scaling_feature_test();

        zpn_application_index_column =
                (struct wally_index_column **)ZPN_APP_CALLOC(ZPATH_MAX_SHARDS * sizeof(*zpn_application_index_column));
        zpn_fohh_workers =
                (struct zpn_fohh_worker_state *)ZPN_CALLOC(fohh_thread_count() * sizeof(struct zpn_fohh_worker_state));

        zpn_transaction_log_init_throttling_error_code();

        // register overrides desc
        res = zpath_config_override_desc_init(config_component_broker);
        ASSERT_EQ(res, ZPN_RESULT_NO_ERROR);

        res = register_app_scaling_config_ovd_descriptions();
        ASSERT_EQ(res, ZPN_RESULT_NO_ERROR);

        res = zpn_broker_client_apps_init(NULL, 0, 200000, 0);
        ASSERT_EQ(res, ZPN_RESULT_NO_ERROR);

        if (f_debug) {
            ZPN_LOG(AL_NOTICE, "Debug log is enabled");
            zpn_debug_set(ZPN_DEBUG_COR_IDX);
            zpn_debug_set(ZPN_DEBUG_CLIENT_IDX);
        }
    }

  private:
    static void mock_create_global_config() {
        static struct zpath_cloud cloud = {};
        static char *ip_anchor_clouds[] = {"zscaler.net", "zsdemo.net"};
        cloud.ip_anchor_clouds = ip_anchor_clouds;
        cloud.ip_anchor_clouds_count = sizeof(ip_anchor_clouds) / sizeof(char *);
        zpath_cloud_global_state.current_config = &cloud;

        static struct zpath_instance current_config;
        memset(&current_config, 0, sizeof(struct zpath_instance));
        current_config.latitude = 38.00;
        current_config.longitude = -123.00;
        // set lat/long for broker location
        zpath_instance_global_state.current_config = &current_config;
    }
};

class APPSCALE_SUITE : public testing::Test {
  public:
    void SetUp() override {
        int customer_gid = 123456;
        c_state = zpn_broker_c_state_alloc();
        ASSERT_TRUE(c_state);

        c_state->client_thread = zevent_self();
        zpn_broker_client_state_init(c_state, zpn_client_type_zapp, zpn_tunnel_auth_zapp, zpn_fohh_tlv);
        c_state->customer_gid = customer_gid;
        memset(&fohh_tlv, 0, sizeof(struct zpn_fohh_tlv));

        fohh_tlv.tlv.conn.f_conn = (struct fohh_connection *)ZPN_CALLOC(sizeof(*fohh_tlv.tlv.conn.f_conn));
        ASSERT_TRUE(fohh_tlv.tlv.conn.f_conn);

        int res = zpn_fohh_tlv_init(&(c_state->tlv_state), fohh_tlv.tlv.conn.f_conn, c_state->incarnation);
        ASSERT_TRUE(!res);
        fohh_connection_set_dynamic_cookie(fohh_tlv.tlv.conn.f_conn, c_state);
        snprintf(
                fohh_tlv.tlv.conn.f_conn->peer_common_name, sizeof(fohh_tlv.tlv.conn.f_conn->peer_common_name), "TEST");

        // needed for registering messages
        fohh_tlv.tlv.conn.f_conn->argo.rx_argo = argo_alloc(
                NULL,
                NULL,
                NULL,  // fohh_default_argo_callback,
                fohh_tlv.tlv.conn.f_conn,
                ((fohh_tlv.tlv.conn.f_conn->style == fohh_connection_style_argo_tlv) | fohh_tlv.tlv.conn.f_conn->quiet)
                        ? 0
                        : 1024 * 1024 * 1024,
                fohh_tlv.tlv.conn.f_conn->argo.encoding,
                1000,
                0,
                fohh_tlv.tlv.conn.f_conn->accept_binary_argo);
        client = (struct zpn_client_app_state *) ZPN_CALLOC(sizeof(*client));
        client->customer_gid = customer_gid;
        customer = (struct customer_state *) ZPN_CALLOC(sizeof(*customer));
        customer->customer_gid = customer_gid;
        client->customer = customer;
    }
    void TearDown() override {
        ZPN_FREE_AND_NULL(customer);
        ZPN_FREE_AND_NULL(client);
        ZPN_FREE_AND_NULL(c_state);
        ZPN_FREE_AND_NULL(fohh_tlv.tlv.conn.f_conn);
    }

    struct zpn_broker_client_fohh_state *c_state;
    struct zpn_client_app_state *client;
    struct customer_state *customer;
    struct zpn_fohh_tlv fohh_tlv;
};

void parse_args(int argc, char *argv[]) {
    for (int i = 1; i < argc; i++) {
        if (strcmp(argv[i], "-version") == 0) {
            fprintf(stdout, "%s\n", ZPATH_VERSION);
            exit(0);
        } else if (strcmp(argv[i], "-d") == 0) {
            f_debug = 1;
        } else {
            // here are two part arguments
            if ((i + 1) >= argc) {
                fprintf(stderr, "argument '%s' is unknown or requires value\n", argv[i]);
            }
        }
    }
}

/*
 * run environment once
 */
int main(int argc, char *argv[]) {
    ::testing::InitGoogleTest(&argc, argv);

    parse_args(argc, argv);

    // gtest takes ownership of the TestEnvironment ptr - we don't delete it.
    ::testing::AddGlobalTestEnvironment(new TestEnvironment);

    // YOLO
    ::testing::FLAGS_gtest_repeat = 1;

    return RUN_ALL_TESTS();
}

/*
 * Verify c_state is initialize Test T1
 */
TEST_F(APPSCALE_SUITE, T1_Verify_cstate_initialization_passed) {
    EXPECT_EQ(c_state ? 1 : 0, 1);
}

/*
 * Get appscaling config value
 * T2: Android, android:enabled, expected 1
 * T3: Windows, appscale:enabled, expected 1
 * T4: BC, appscale:enabled, expected 0
 * T5: Exporter, appscale:enabled, expected 0
 */
TEST_F(APPSCALE_SUITE, T2_Verify_android_client_app_scale_config_val_POSITIVE_CASE) {
    client->client_type = zpn_client_type_zapp;
    client->platform_type = zpn_platform_type_android;
    client->customer->android_app_scaling_feature_status = 1;

    int res = zpn_broker_get_app_scaling_flag_for_specific_client(client, NULL);
    EXPECT_EQ(res, 1);
}

TEST_F(APPSCALE_SUITE, T3_Verify_windows_client_app_scale_config_val_POSITIVE_CASE) {
    client->client_type = zpn_client_type_zapp;
    client->platform_type = zpn_platform_type_windows;
    int res = zpn_broker_get_app_scaling_flag_for_specific_client(client, NULL);
    EXPECT_EQ(res, 1);
}

TEST_F(APPSCALE_SUITE, T4_Verify_bc_client_app_scale_config_val_NEGATIVE_CASE) {
    client->client_type = zpn_client_type_branch_connector;

    int res = zpn_broker_get_app_scaling_flag_for_specific_client(client, NULL);
    EXPECT_EQ(res, 0);
}

TEST_F(APPSCALE_SUITE, T5_Verify_exporter_client_app_scale_config_val_NEGATIVE_CASE) {
    client->client_type = zpn_client_type_exporter;

    int res = zpn_broker_get_app_scaling_flag_for_specific_client(client, NULL);
    EXPECT_EQ(res, 0);
}

/*
 * SIPA releated
 */

#ifdef SIPA_APP_SCALE_CODE_ENABLED
TEST_F(APPSCALE_SUITE, sipa_app_scale_config) {
    client->client_type = zpn_client_type_ip_anchoring;

    int res = zpn_broker_get_app_scaling_flag_for_specific_client(client, NULL);
    EXPECT_EQ(res, 1);
}
TEST_F(APPSCALE_SUITE, sipa_enabled_default) {
    client->client_type = zpn_client_type_ip_anchoring;
    int res = zpn_sipa_app_scaling_enabled(1);
    EXPECT_EQ(res, DEFAULT_SIPA_APP_SCALING_FEATURE);
}

TEST_F(APPSCALE_SUITE, sipa_sitec_disabled) {
    client->client_type = zpn_client_type_ip_anchoring;

    g_broker_cfg.instance_type = ZPN_INSTANCE_TYPE_SITEC;
    int res = zpn_sipa_app_scaling_enabled(1);
    EXPECT_EQ(res, 0);

    g_broker_cfg.instance_type = ZPN_INSTANCE_TYPE_PUBLIC_BROKER;
}

TEST_F(APPSCALE_SUITE, sipa_status_default) {
    client->client_type = zpn_client_type_ip_anchoring;
    zpn_policy_app_scaling_flag_test = 0;

    int res = zpn_sipa_app_scaling_enabled_status_for_customer(1);
    EXPECT_EQ(res, 0);

    zpn_policy_app_scaling_flag_test = 1;
}
#endif
/*
 * Full app segements tests
 */

TEST_F(APPSCALE_SUITE, full_segments) {
    struct zpn_application src = {};
    struct argo_object *obj = zpn_broker_client_apps_copy_full_app_data(&src);

    ASSERT_TRUE(obj != NULL);

    if (obj) {
        argo_object_release(obj);
    }
}
TEST_F(APPSCALE_SUITE, full_segments_null) {
    struct argo_object *obj = zpn_broker_client_apps_copy_full_app_data(NULL);

    ASSERT_TRUE(obj == NULL);

    if (obj) {
        argo_object_release(obj);
    }
}
