/*
 * zpn_mconn_icmp_pkt_handler_tests. Copyright (C) 2025 Zscaler, Inc. All Rights Reserved
 */

#include <gtest/gtest.h>
#include <netinet/ip.h>

#include "test_headers/zpn_mconn_icmp_util_tests.h"

using ::testing::Test;

//--------------------------------------------------------------//
//                    NULL Data Tests                           //
//--------------------------------------------------------------//

/*
 * These tests fail for now as we don't properly handle null pointers
 * we should decide if we *should* and then either delete or re-enable these tests
 */

TEST(DISABLED_ZpnMconnIcmpPktHandlerTestsNullTests, NullMconnNullBuffer) {
    // Setup Expectations
    int expected_result = ZPN_MCONN_ICMP_PKT_INTERNAL_ERR;

    // Run Test
    auto actual_result = zpn_mconn_icmp_pkt_handler(nullptr, nullptr);

    // Test Assertions
    ASSERT_EQ(actual_result, expected_result);
}

TEST(DISABLED_ZpnMconnIcmpPktHandlerTestsNullTests, NullBuffer) {
    // Setup Expectations
    int expected_result = ZPN_MCONN_ICMP_PKT_INTERNAL_ERR;

    // Setup Test Data
    struct zpn_mconn mconn{};

    // Run Test
    auto actual_result = zpn_mconn_icmp_pkt_handler(&mconn, nullptr);

    // Test Assertions
    ASSERT_EQ(actual_result, expected_result);
}

TEST(DISABLED_ZpnMconnIcmpPktHandlerTestsNullTests, NullMconn) {
    // Setup Expectations
    int expected_result = ZPN_MCONN_ICMP_PKT_INTERNAL_ERR;

    // Setup Test Data
    std::shared_ptr<struct evbuffer> managed_buf(evbuffer_new(), evbuffer_free);

    // Run Test
    auto actual_result = zpn_mconn_icmp_pkt_handler(nullptr, managed_buf.get());

    // Test Assertions
    ASSERT_EQ(actual_result, expected_result);
}

//--------------------------------------------------------------//
//                   Valid Data Tests                           //
//--------------------------------------------------------------//

class ZpnMconnIcmpPktHandlerTestsMockTester : public ::Test {
  public:
    void SetUp() override {
        _evbuf = std::shared_ptr<struct evbuffer>(evbuffer_new(), evbuffer_free);
    }

    void write_up_to_ip_header() {
        evbuffer_add(_evbuf.get(), &ip_header, sizeof(ip_header));
    }

    struct evbuffer *get_buf() {
        return _evbuf.get();
    }

    struct zpn_mconn *get_mconn() {
        return &_mconn;
    }

    struct ip ip_header{}; // allow for the test to manipulate the iph at will

  private:
    struct zpn_mconn _mconn{};
    std::shared_ptr<struct evbuffer> _evbuf;
};

TEST_F(ZpnMconnIcmpPktHandlerTestsMockTester, EmptyBufferReturnsZeroLenErr) {
    // Setup Expectations
    int expected_result = ZPN_MCONN_ICMP_PKT_ZERO_LEN_DROP;

    // Run Test
    auto actual_result = zpn_mconn_icmp_pkt_handler(get_mconn(), get_buf());

    // Test Assertions
    ASSERT_EQ(actual_result, expected_result);
}

TEST_F(ZpnMconnIcmpPktHandlerTestsMockTester, BufferWithInvalidIPHeaderReturnsMalformed) {
    // Setup Expectations
    int expected_result = ZPN_MCONN_ICMP_PKT_MALFORMED_DROP;

    // Setup Test Data
    ip_header.ip_hl = 10;  // ip_hl is 10-words

    // setup the data buffer
    write_up_to_ip_header();

    // Run Test
    auto actual_result = zpn_mconn_icmp_pkt_handler(get_mconn(), get_buf());

    // Test Assertions
    ASSERT_EQ(actual_result, expected_result);
}

TEST_F(ZpnMconnIcmpPktHandlerTestsMockTester, DropsFragments_FragmentLastPacket) {
    // Setup Expectations
    int expected_result = ZPN_MCONN_ICMP_PKT_FRAG_DROP;

    // Setup Test Data
    ip_header.ip_hl = sizeof(ip_header) >> 2;           // We are only writing the header right now so this is fine
    ip_header.ip_off |= htons(100 & IP_OFFMASK);  // Add some fragment location (13bits) masked for clarity of the operation

    // setup the data buffer
    write_up_to_ip_header();

    // Run Test
    auto actual_result = zpn_mconn_icmp_pkt_handler(get_mconn(), get_buf());

    // Test Assertions
    ASSERT_EQ(actual_result, expected_result);
}

TEST_F(ZpnMconnIcmpPktHandlerTestsMockTester, DropsFragments_IntermediateFragment) {
    // Setup Expectations
    int expected_result = ZPN_MCONN_ICMP_PKT_FRAG_DROP;

    // Setup Test Data
    ip_header.ip_hl = sizeof(ip_header) >> 2;           // We are only writing the header right now so this is fine
    ip_header.ip_off |= htons(100 & IP_OFFMASK);  // Add some fragment location (13bits) masked for clarity of the operation
    ip_header.ip_off |= htons(IP_MF);             // Add the more fragments flag

    // setup the data buffer
    write_up_to_ip_header();

    // Run Test
    auto actual_result = zpn_mconn_icmp_pkt_handler(get_mconn(), get_buf());

    // Test Assertions
    ASSERT_EQ(actual_result, expected_result);
}

TEST_F(ZpnMconnIcmpPktHandlerTestsMockTester, TCPPacketReturnsMalformed) {
    // Setup Expectations
    int expected_result = ZPN_MCONN_ICMP_PKT_MALFORMED_DROP;

    // Setup Test Data
    ip_header.ip_hl = sizeof(ip_header) >> 2;  // We are only writing the header right now so this is fine
    ip_header.ip_off = 0;
    ip_header.ip_p = IPPROTO_TCP;

    // setup the data buffer
    write_up_to_ip_header();

    // Run Test
    auto actual_result = zpn_mconn_icmp_pkt_handler(get_mconn(), get_buf());

    // Test Assertions
    ASSERT_EQ(actual_result, expected_result);
}

TEST_F(ZpnMconnIcmpPktHandlerTestsMockTester, UDPPacketReturnsMalformed) {
    // Setup Expectations
    int expected_result = ZPN_MCONN_ICMP_PKT_MALFORMED_DROP;

    // Setup Test Data
    ip_header.ip_hl = sizeof(ip_header) >> 2;  // We are only writing the header right now so this is fine
    ip_header.ip_off = 0;
    ip_header.ip_p = IPPROTO_TCP;

    // setup the data buffer
    write_up_to_ip_header();

    // Run Test
    auto actual_result = zpn_mconn_icmp_pkt_handler(get_mconn(), get_buf());

    // Test Assertions
    ASSERT_EQ(actual_result, expected_result);
}
