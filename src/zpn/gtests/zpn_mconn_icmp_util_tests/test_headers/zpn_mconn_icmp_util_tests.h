/*
 * zpn_mconn_icmp_util_tests.h. Copyright (C) 2025 Zscaler, Inc. All Rights Reserved
 */

#ifndef ITASCA_ZPN_MCONN_ICMP_UTIL_TESTS_H
#define ITASCA_ZPN_MCONN_ICMP_UTIL_TESTS_H

#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wunused-parameter"
#pragma clang diagnostic ignored "-Wwritable-strings"
#elif defined __GNUC__
#pragma GCC diagnostic push
#pragma GCC diagnostic ignored "-Wunused-parameter"
#pragma GCC diagnostic ignored "-Wwrite-strings"
#endif  // __clang__

extern "C" {
#include "zpn/zpn_mconn_icmp_util.h"
};

#ifdef __clang__
#pragma clang diagnostic pop
#elif defined __GNUC__
#pragma GCC diagnostic pop
#endif  // __clang__

// Exposing testing prototypes
extern "C" {};

#endif  // ITASCA_ZPN_MCONN_ICMP_UTIL_TESTS_H
