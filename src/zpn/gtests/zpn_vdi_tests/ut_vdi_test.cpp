//
// zpn_.cpp. Copyright (C) 2024 Zscaler, Inc. All Rights Reserved
//
#include <gtest/gtest.h>
#include <gmock/gmock.h>

#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wunused-parameter"
#pragma clang diagnostic ignored "-Wwritable-strings"
#elif defined __GNUC__
#pragma GCC diagnostic push
#pragma GCC diagnostic ignored "-Wunused-parameter"
#pragma GCC diagnostic ignored "-Wwrite-strings"
#endif  // __clang__

using testing::Eq;

extern "C" {
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <netinet/in.h>
#include <pthread.h>
#include <syslog.h>
#include <signal.h>
#include "fohh/fohh.h"
#include "fohh/fohh_log.h"
#include "fohh/fohh_private.h"
#include "argo/argo_log.h"
#include "zpn/zpn_lib.h"
#include "zpath_misc/zpath_version.h"
#include "zevent/zevent.h"
#include "zhash/zhash_table.h"
#include "base64/base64.h"
#include <openssl/rand.h>
#include "zpn/zpn_broker_client.h"

#include "zpath_lib/zpath_cloud.h"
#include "zpath_lib/zpath_customer.h"
#include "zpath_lib/zpath_local.h"
#include "zpath_misc/zpath_misc.h"
#include "zpn/zpn_policy_engine.h"
#include "zpath_lib/zpath_config_override_keys.h"
#include "zpn/zpn_scope_engine.h"
#include "zhash/zhash_table.h"
#include "zpn/zpn_vdi/zpn_vdi.h"
#include "zpn/zpn_pb_client.h"
}

// APIs
extern "C" {
int zpn_update_hops_vdi(struct zpn_broker_client_fohh_state *c_state,
                        struct zpn_vdi_client_authenticate *downstream_auth);
void zpn_broker_get_vdi_auth_stats(struct zpn_broker_vdi_authentication_stats *stats);
}
unsigned f_debug = 0;

class TestEnvironment : public ::testing::Environment {
  public:
    // Initialise the timestamp.
    virtual void SetUp() {
        int res = argo_library_init(512);
        ASSERT_TRUE(ZPN_RESULT_NO_ERROR == res);

        // prints to stdout/err
        argo_log_use_printf(f_debug ? 3 : 1);

        struct zpath_simple_app_init_params app_params;
        zpath_simple_app_init_params_default(&app_params);
        app_params.instance_name = app_params.role_name = "eas";
        app_params.debug_port = 8010;

        res = zpath_simple_app_init(&app_params);

        ASSERT_TRUE(ZPN_RESULT_NO_ERROR == res);
        // create global context
        // mock_create_global_config();

        zpn_rpc_init();

        zpn_fohh_workers =
                (struct zpn_fohh_worker_state *)ZPN_CALLOC(fohh_thread_count() * sizeof(struct zpn_fohh_worker_state));

        if (f_debug) {
            ZPN_LOG(AL_NOTICE, "Debug log is enabled");
            zpn_debug_set(ZPN_DEBUG_AUTH_IDX);
        }
    }
};

void parse_args(int argc, char *argv[]) {
    for (int i = 1; i < argc; i++) {
        if (strcmp(argv[i], "-version") == 0) {
            fprintf(stdout, "%s\n", ZPATH_VERSION);
            exit(0);
        } else if (strcmp(argv[i], "-d") == 0) {
            f_debug = 1;
        } else {
            // here are two part arguments
            if ((i + 1) >= argc) {
                fprintf(stderr, "argument '%s' is unknown or requires value\n", argv[i]);
            }
        }
    }
}

/*
 * run environment once
 */
int main(int argc, char *argv[]) {
    ::testing::InitGoogleTest(&argc, argv);

    parse_args(argc, argv);

    // gtest takes ownership of the TestEnvironment ptr - we don't delete it.
    ::testing::AddGlobalTestEnvironment(new TestEnvironment);

    // YOLO
    ::testing::FLAGS_gtest_repeat = 1;

    return RUN_ALL_TESTS();
}

class VDI_SUITE : public testing::Test {
  public:
    void SetUp() override {
        c_state = zpn_broker_c_state_alloc();
        ASSERT_TRUE(c_state);

        c_state->client_thread = zevent_self();
        zpn_broker_client_state_init(c_state, zpn_client_type_eas_data, zpn_tunnel_auth_znf, zpn_fohh_tlv);
        c_state->customer_gid = 1;
        memset(&fohh_tlv, 0, sizeof(struct zpn_fohh_tlv));

        fohh_tlv.tlv.conn.f_conn = (struct fohh_connection *)ZPN_CALLOC(sizeof(*fohh_tlv.tlv.conn.f_conn));
        ASSERT_TRUE(fohh_tlv.tlv.conn.f_conn);

        int res = zpn_fohh_tlv_init(&(c_state->tlv_state), fohh_tlv.tlv.conn.f_conn, c_state->incarnation);
        ASSERT_TRUE(!res);
        fohh_connection_set_dynamic_cookie(fohh_tlv.tlv.conn.f_conn, c_state);
        snprintf(
                fohh_tlv.tlv.conn.f_conn->peer_common_name, sizeof(fohh_tlv.tlv.conn.f_conn->peer_common_name), "TEST");

        // needed for registering messages
        fohh_tlv.tlv.conn.f_conn->argo.rx_argo = argo_alloc(
                NULL,
                NULL,
                NULL,  // fohh_default_argo_callback,
                fohh_tlv.tlv.conn.f_conn,
                ((fohh_tlv.tlv.conn.f_conn->style == fohh_connection_style_argo_tlv) | fohh_tlv.tlv.conn.f_conn->quiet)
                        ? 0
                        : 1024 * 1024 * 1024,
                fohh_tlv.tlv.conn.f_conn->argo.encoding,
                1000,
                0,
                fohh_tlv.tlv.conn.f_conn->accept_binary_argo);
    }
    void TearDown() override {
        ZPN_FREE(c_state);
        ZPN_FREE(fohh_tlv.tlv.conn.f_conn);
    }

    struct zpn_broker_client_fohh_state *c_state;
    struct zpn_fohh_tlv fohh_tlv;
};

TEST_F(VDI_SUITE, zpn_update_hops_vdi) {
    struct zpn_vdi_client_authenticate auth = {};
    int res = zpn_update_hops_vdi(c_state, &auth);
    ASSERT_EQ(res, ZPN_RESULT_NO_ERROR);
}

TEST_F(VDI_SUITE, zpn_broker_get_vdi_auth_stats) {
    struct zpn_broker_vdi_authentication_stats stats = {};
    zpn_broker_get_vdi_auth_stats(&stats);
    // no assert, runing the func for coverage
}

TEST_F(VDI_SUITE, pb_client_vdi_authenticate_ack_cb) {
    struct zpn_pb_client pb_client = {};
    struct fohh_connection fcon = {};
    struct zpn_vdi_client_authenticate_ack ack = {};

    struct argo_object *object = argo_object_create(zpn_vdi_client_authenticate_ack_description, &ack);
    if (!object) {
        GTEST_FATAL_FAILURE_("zpn_zia_instance_info_description");
    }

    fcon.dynamic_cookie = c_state;
    pb_client.inbound_client_connection = &fcon;
    pb_client.inbound_client_connection_tlv_type = zpn_fohh_tlv;

    int res = pb_client_vdi_authenticate_ack_cb(NULL, &pb_client, object);
    ASSERT_EQ(res, ZPN_RESULT_NO_ERROR);

    argo_object_release(object);
}

TEST_F(VDI_SUITE, pb_client_vdi_authenticate_ack_c_error) {
    struct zpn_pb_client pb_client = {};
    struct fohh_connection fcon = {};
    struct zpn_vdi_client_authenticate_ack ack = {};
    ack.error = "TEST_ERROR";

    struct argo_object *object = argo_object_create(zpn_vdi_client_authenticate_ack_description, &ack);
    if (!object) {
        GTEST_FATAL_FAILURE_("zpn_zia_instance_info_description");
    }

    fcon.dynamic_cookie = c_state;
    pb_client.inbound_client_connection = &fcon;
    pb_client.inbound_client_connection_tlv_type = zpn_fohh_tlv;


    int res = pb_client_vdi_authenticate_ack_cb(NULL, &pb_client, object);
    ASSERT_EQ(res, ZPATH_RESULT_ERR);

    argo_object_release(object);
}
TEST_F(VDI_SUITE, pb_client_vdi_authenticate_ack_c_incarnation) {
    struct zpn_pb_client pb_client = {};
    struct fohh_connection fcon = {};
    struct zpn_vdi_client_authenticate_ack ack = {};
    ack.error = "TEST_ERROR";

    struct argo_object *object = argo_object_create(zpn_vdi_client_authenticate_ack_description, &ack);
    if (!object) {
        GTEST_FATAL_FAILURE_("zpn_zia_instance_info_description");
    }

    fcon.dynamic_cookie = c_state;
    pb_client.inbound_client_connection = &fcon;
    pb_client.inbound_client_connection_tlv_type = zpn_fohh_tlv;
    pb_client.inbound_client_connection_incarnation = 2; // mismatch

    int res = pb_client_vdi_authenticate_ack_cb(NULL, &pb_client, object);
    ASSERT_EQ(res, ZPN_RESULT_BAD_STATE);

    argo_object_release(object);
}
