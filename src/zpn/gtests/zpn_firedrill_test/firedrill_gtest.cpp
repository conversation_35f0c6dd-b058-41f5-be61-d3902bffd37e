//
// src/zpn/gtests/zpn_firedrill_test/firedrill_gtest.cpp. Copyright (C) 2025 Zscaler, Inc. All Rights Reserved
//
#include <gtest/gtest.h>
#include <gmock/gmock.h>

 #include "test_misc/ZscalerMockBase.h"

#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wunused-parameter"
#pragma clang diagnostic ignored "-Wwritable-strings"
#elif defined __GNUC__
#pragma GCC diagnostic push
#pragma GCC diagnostic ignored "-Wunused-parameter"
#pragma GCC diagnostic ignored "-Wwrite-strings"
#endif  // __clang__


extern "C" {
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <netinet/in.h>
#include <pthread.h>
#include <syslog.h>
#include <signal.h>
#include <zpn/zpn_firedrill_site.h>
#include <wally/wally.h>
 #include "zpn/zpn_lib.h"
}


using testing::_;
using testing::AllOf;
using testing::DoAll;
using testing::Eq;
using testing::Field;
using testing::InSequence;
using testing::Invoke;
using testing::IsNull;
using testing::Not;
using testing::NotNull;
using testing::Pointee;
using testing::Return;
using testing::SaveArg;
using testing::SaveArgPointee;
using testing::SetArgPointee;
using testing::StrEq;
using testing::WithArg;
using testing::StrictMock;
using testing::ReturnPointee;

using namespace std;

int gtest_firedrill_config_fetch_callback(void *response_callback_cookie,
                                        struct wally_registrant *registrant,
                                        struct wally_table *table,
                                        int64_t request_id,
                                        int row_count)
{
    return ZPN_RESULT_NO_ERROR;
}

#define MOCK_ZPN_FIREDRILL_SITE_BY_SITE_GID(T)                                                      \
            int zpn_firedrill_site_by_site_gid(int64_t site_gid,                                    \
                                                int64_t customer_gid,                               \
                                                wally_response_callback_f callback_f,               \
                                                struct zpn_firedrill_site **firedrill_instance) {   \
            return T::get_mock()->zpn_firedrill_site_by_site_gid(site_gid, customer_gid, callback_f, firedrill_instance); \
}

#define MOCK_ZPN_FIREDRILL_SITE_BY_CUSTOMER_GID(T)                                              \
            int zpn_firedrill_site_get_by_customer_gid(int64_t customer_gid,                    \
                                                struct zpn_firedrill_site **firedrill_site,     \
                                                size_t *row_count,                              \
                                                wally_response_callback_f callback_f,           \
                                                void *callback_cookie,                          \
                                                int64_t callback_id) {                          \
            return T::get_mock()->zpn_firedrill_site_get_by_customer_gid(customer_gid, firedrill_site, row_count, callback_f, callback_cookie, callback_id); \
}


class FiredrillSiteRespMock {
    public:
        MOCK_METHOD(int, zpn_firedrill_site_get_by_customer_gid, (int64_t customer_gid,
                                                                    struct zpn_firedrill_site **firedrill_site,
                                                                    size_t *row_count,
                                                                    wally_response_callback_f callback_f,
                                                                    void *callback_cookie,
                                                                    int64_t callback_id));
        MOCK_METHOD(int, zpn_firedrill_site_by_site_gid, (int64_t site_gid,
                                                          int64_t customer_gid,
                                                          wally_response_callback_f *callback_f,
                                                          struct zpn_firedrill_site **firedrill_instance));
};

class FiredrillSiteRespMockBase : public testing::StrictMock<FiredrillSiteRespMock> {};

class FIREDRILL_SUITE :  public ZscalerMockBase<FiredrillSiteRespMockBase> {
    protected:
    int64_t site_gid;
    int64_t customer_gid;
    public:
    void SetUp() override {
        ZscalerMockBase::SetUp();
        site_gid = 289397352052032122;
        customer_gid = 289397352052031488;
    }

    void TearDown() override {
        ZscalerMockBase::TearDown();
    }
};


ACTION_P(SetFiredrillSitebySiteGidResp, ptr) {
    *arg0=ptr;
}

ACTION_P(SetFiredrillSitebyCustomerGidResp, ptr) {
    *arg0=ptr;
}


extern "C" {
    MOCK_ZPN_FIREDRILL_SITE_BY_CUSTOMER_GID(FIREDRILL_SUITE);
    MOCK_ZPN_FIREDRILL_SITE_BY_SITE_GID(FIREDRILL_SUITE);
}


TEST_F(FIREDRILL_SUITE, FiredrillSiteBySiteGid ) {

    int res = ZPN_RESULT_ERR;

    struct zpn_firedrill_site *firedrill_test_obj = new struct zpn_firedrill_site;
    firedrill_test_obj->firedrill_interval = 20;
    firedrill_test_obj->firedrill_interval_unit = (char*)(new string("SECONDS"))->c_str();

    EXPECT_CALL(*get_mock(),
                zpn_firedrill_site_by_site_gid)
                .Times(1)
                .WillOnce(DoAll(WithArg<3>(SetFiredrillSitebySiteGidResp(firedrill_test_obj)), testing::Return(0)));

    res = zpn_firedrill_site_by_site_gid(site_gid,
                                        customer_gid,
                                        gtest_firedrill_config_fetch_callback,
                                        &firedrill_test_obj);
    EXPECT_EQ(res, ZPN_RESULT_NO_ERROR);
    EXPECT_EQ(firedrill_test_obj->firedrill_interval, 20);
    EXPECT_STREQ(firedrill_test_obj->firedrill_interval_unit,"SECONDS");
}

TEST_F(FIREDRILL_SUITE, FiredrillSiteByCustomerGid ) {

    int res = ZPN_RESULT_ERR;
    size_t *row_count = NULL;
    void *callback_cookie = NULL;
    int64_t callback_id = 0;

    struct zpn_firedrill_site *firedrill_test_obj = new struct zpn_firedrill_site;
    firedrill_test_obj->firedrill_interval = 20;
    firedrill_test_obj->firedrill_interval_unit = (char*)(new string("SECONDS"))->c_str();

    EXPECT_CALL(*get_mock(),
                zpn_firedrill_site_get_by_customer_gid)
                .Times(1)
                .WillOnce(DoAll(WithArg<1>(SetFiredrillSitebyCustomerGidResp(firedrill_test_obj)), testing::Return(0)));

    res = zpn_firedrill_site_get_by_customer_gid(customer_gid,
                                        &firedrill_test_obj,
                                        row_count,
                                        gtest_firedrill_config_fetch_callback,
                                        callback_cookie,
                                        callback_id);
    EXPECT_EQ(res, ZPN_RESULT_NO_ERROR);
    EXPECT_EQ(firedrill_test_obj->firedrill_interval, 20);
    EXPECT_STREQ(firedrill_test_obj->firedrill_interval_unit,"SECONDS");

}

TEST(firedrill_gtest, FiredrillRowFixUp ) {

    struct zpn_firedrill_site *firedrill_test_obj = new struct zpn_firedrill_site;

    firedrill_test_obj->firedrill_interval = 30;
    firedrill_test_obj->firedrill_interval_unit = (char*)(new string("MINUTES"))->c_str();
    firedrill_test_obj->firedrill_interval_s = 1800;

    struct argo_object *argo_test_obj = new struct argo_object;
    argo_test_obj->base_structure_void = new struct zpn_firedrill_site;
    argo_test_obj->base_structure_void = firedrill_test_obj;

    zpn_firedrill_site_row_fixup(argo_test_obj);

    EXPECT_EQ(firedrill_test_obj->firedrill_interval_s, 1800);
}

TEST(firedrill_gtest, FiredrillRowCallback ) {

    int res = ZPN_RESULT_ERR;
    res = zpn_firedrill_site_row_callback(NULL,
                                          NULL,
                                          NULL,
                                          NULL,
                                          NULL,
                                          0);
    EXPECT_EQ(res, ZPN_RESULT_NO_ERROR);
}

int main(int argc, char **argv) {
    ::testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}
