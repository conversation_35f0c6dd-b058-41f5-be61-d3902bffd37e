#ifndef AUTH_LOG_TEST_EXTERNAL_IMPORTS_H
#define AUTH_LOG_TEST_EXTERNAL_IMPORTS_H

#include "fohh/fohh_private.h"
#include "zpn/zpn_broker_client.h"
#include "zpn/zpn_broker_common.h"
#include "zpn/zpn_broker_client_user_risk.h"
#include "zpn/zpn_broker_client_scim.h"
#include "zpn/zpn_broker_private.h"
#include "zpn/zpn_rpc.h"
#include "zpn/zpn_userdb_common.h"
#include "zpn/zpn_user_risk.h"
#include "zpath_misc/zpath_version.h"
#include "zpath_lib/zpath_customer.h"
#include "zpath_lib/zpath_cloud.h"
#include "zpn/zpn_broker_pbroker.h"
#include "zpn/zpn_broker_assistant.h"
#include "zpn/zpn_broker_sitec.h"
#include "zpn/zpn_lib.h"

#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wunused-parameter"
#pragma clang diagnostic ignored "-Wwritable-strings"
#elif defined __GNUC__
#pragma GCC diagnostic push
#pragma GCC diagnostic ignored "-Wunused-parameter"
#pragma GCC diagnostic ignored "-Wwrite-strings"
#endif  // __clang__

void zpn_broker_pbroker_redirect_pbrokers(int do_it, const char* reason);
void zpn_broker_assistant_redirect_assistants(int do_it, const char* reason);

enum A2PBAuth {
    AUTH_NOT_SEEN = 0,
    AUTH_UNSUCCESSFUL = 1,
    AUTH_SUCCESSFUL = 2
};

struct connected_assistant {
    int64_t assistant_gid_from_config;
    struct fohh_connection *f_conn;
    int64_t f_conn_incarnation;

    /* Timer for authentication reports. */
    struct event *timer;
    int first_auth_sent;
    int last_np_state_seen;

    char *version;
    char *sarge_version;

    /* New fields for QBR */
    char *public_cloud;
    char *private_cloud;
    char *region_id;

    int version_major;
    int version_minor;

    unsigned tcp_info_ready:1;
    unsigned status_report_ready:1;

    struct zpn_ast_auth_log auth_log;

    enum connector_type connector_type;
    struct {
        int64_t     delta_mtunnel_count;
    } delta_from_prev_auth_log;
    int64_t g_ast_grp;

    int log_upload;
    uint64_t debug_flag;

    int stats_upload;

    struct argo_inet a_ip;
    char a_cc[CC_STR_LEN + 1];             /* Need to NULL terminate */
    double a_lat;
    double a_lon;

    char *dft_rt_intf;
    char *platform;
    char *platform_detail;
    char *runtime_os;
    char *platform_arch;
    char *platform_version;
    char *frr_version;

    /* 128 bit (16 bytes, 24 bytes base64) random ID for this TLS
     * connection, in base64 */
    uint8_t tunnel_id_bin[ZPN_TUNNEL_ID_BYTES];
    char tunnel_id[ZPN_TUNNEL_ID_BYTES_TEXT + 1];

    int64_t customer_gid;
    int64_t scope_gid;

    LIST_ENTRY(connected_assistant) list_entry;
    int in_list;

    int disabled;
    struct zpn_broker_assistant_capability  capability;
    int32_t udp4_port_util;
    int32_t udp6_port_util;
    int32_t tcp4_port_util;
    int32_t tcp6_port_util;
    int32_t sys_fd_util;
    int32_t proc_fd_util;
    char dr_running_mode[DR_RUNNING_MODE_STATUS_LEN];
    enum A2PBAuth dr_auth_status;
    enum A2PBAuth scope_auth_status;
    uint64_t tx_auth_report_failed;
    uint64_t tx_auth_report_success;
    uint64_t rx_tcp_info_report;
    uint64_t rx_environment_report;
    uint64_t rx_health_report;
    uint64_t rx_app_route_registration;
    uint64_t rx_broker_request_ack;
    uint64_t rx_dns_assistant_check;
    uint64_t rx_log_stats_upload;
    uint64_t rx_log_control;
    uint64_t rx_stats_control;
    uint64_t rx_status_report;
    uint64_t rx_state;
    uint64_t rx_active_connection;
    uint64_t rx_waf_cert_prv_key_req;
    uint64_t rx_waf_cert_gen_req;
    uint64_t active_connection_switched_in;
    uint64_t active_connection_switched_out;
    uint64_t active_control_connection_currently:1;
    uint64_t monitor_count;

    int is_redirect_sent;
};

#endif // TEST_EXTERNAL_IMPORTS_H
