/*
 * zpn_broker_utils.h. Copyright (C) 2025 Zscaler, Inc. All Rights Reserved
 */

extern "C" {
    #include "zpn/zpn_broker_private.h"
    #include "argo/argo_hash.h"
}

/* Create the global broker buckets, which is generally created during broker initialization */
void ZpnBrokerBucketsInit() {
    for (int i = 0; i < ZPN_BROKER_BUCKETS; i++) {
        struct zpn_broker_bucket *bucket = &(broker.buckets[i]);

        bucket->reaped_start_us = 0;
        bucket->reaped_max_count = 0;
        bucket->reaped_max_null_count = 0;

        bucket->mtunnel_by_id = argo_hash_alloc(7, 1);
        if (!bucket->mtunnel_by_id) {
            fprintf(stderr, "Could not allocate hash table");
            return;
        }
        bucket->f_conn_by_tunnel_id = argo_hash_alloc(7, 1);
        if (!bucket->f_conn_by_tunnel_id) {
            fprintf(stderr, "Could not allocate hash table");
            return;
        }
        TAILQ_INIT(&(bucket->mtunnel_list));
        TAILQ_INIT(&(bucket->reaped_list));
    }
}
