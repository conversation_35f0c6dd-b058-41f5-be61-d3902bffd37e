#ifndef ZPN_AUTH_LOG_TEST_WEAK_HEADERS_H
#define ZPN_AUTH_LOG_TEST_WEAK_HEADERS_H

struct connected_assistant;

int64_t zpath_wrapper_config_override_get_config_int(const char* config_key,
                                                      int64_t* config_value,
                                                      int64_t config_default_value,
                                                      int64_t global_gid,
                                                      int64_t root_customer_gid,
                                                      int64_t default_global_gid,
                                                      int64_t default_customer_gid)  __attribute__((weak));

void zpath_wrapper_zpn_broker_assistant_control_destroy_with_asst_lock(struct connected_assistant *asst)  __attribute__((weak));
void zpath_wrapper_zpn_broker_remove_tunnel_info_by_sc_gid(int64_t sc_gid)  __attribute__((weak));
void zpath_wrapper_zpn_broker_pbroker_control_conn_redirect(struct fohh_connection *f_conn)  __attribute__((weak));
void zpath_wrapper_zpn_broker_sitec_control_conn_redirect(struct fohh_connection *f_conn)  __attribute__((weak));
void zpath_wrapper_zpn_free(void* ptr, int line, const char* file)  __attribute__((weak));

#endif // ZPN_AUTH_LOG_TEST_WEAK_HEADERS_H
