/*
 * mock_zpn_auth_log_mocks.h. Copyright (C) 2025 Zscaler, Inc. All Rights Reserved
 */

#ifndef MOCK_ZPN_AUTH_LOG_MOCKS_H
#define MOCK_ZPN_AUTH_LOG_MOCKS_H

#include <gmock/gmock.h>
#include "zpath_lib/zpath_config_override.h"
#include "fohh/fohh.h"

class mockZpnApi_zpn_config_override_get_config_int{
public:
    MOCK_METHOD(int64_t, zpath_wrapper_config_override_get_config_int, (const char* config_key,
                                                                         int64_t* config_value,
                                                                         int64_t config_default_value,
                                                                         int64_t global_gid,
                                                                         int64_t root_customer_gid,
                                                                         int64_t default_global_gid,
                                                                         int64_t default_customer_gid));

};

class mockZpnApi_zpn_broker_pbroker_conn_callback{
public:
    MOCK_METHOD(int, zpath_wrapper_zpn_broker_pbroker_conn_callback, (struct fohh_connection *connection,
                                                                   enum fohh_connection_state state,
                                                                   void *cookie));
};

class mockZpnApi_zpn_broker_pbroker_control_conn_monitor_cb{
public:
    MOCK_METHOD(void, zpath_wrapper_zpn_broker_pbroker_control_conn_monitor_cb, (evutil_socket_t sock, short flags, void *cookie));
};

class mockZpnApi_zpn_broker_pbroker_control_conn_redirect{
public:
    MOCK_METHOD(void, zpath_wrapper_zpn_broker_pbroker_control_conn_redirect, (struct fohh_connection *f_conn));
};

class mockZpnApi_zpn_broker_assistant_control_destroy_with_asst_lock{
public:
    MOCK_METHOD(void, zpath_wrapper_zpn_broker_assistant_control_destroy_with_asst_lock, (struct connected_assistant *asst));
};

class mockZpnApi_zpn_broker_remove_tunnel_info_by_sc_gid{
public:
    MOCK_METHOD(void, zpath_wrapper_zpn_broker_remove_tunnel_info_by_sc_gid, (int64_t sc_gid));
};

class mockZpnApi_zpn_broker_sitec_control_conn_redirect{
public:
    MOCK_METHOD(void, zpath_wrapper_zpn_broker_sitec_control_conn_redirect, (struct fohh_connection *f_conn));
};

class mockZpnApi_zpn_free{
public:
    MOCK_METHOD(void, zpath_wrapper_zpn_free, (void* ptr, int line, const char* file));
};

#define MOCK_ZPN_FEATURE_FLAG_CONFIG_OVERRIDE_GET_CONFIG_INT(T)    \
    int64_t zpath_wrapper_config_override_get_config_int(const char* config_key, \
                                                          int64_t* config_value,    \
                                                          int64_t config_default_value, \
                                                          int64_t global_gid,   \
                                                          int64_t root_customer_gid,    \
                                                          int64_t default_global_gid,   \
                                                          int64_t default_customer_gid) {   \
        return T::get_mock()->zpath_wrapper_config_override_get_config_int(  \
            config_key, \
            config_value,   \
            config_default_value,   \
            global_gid,   \
            root_customer_gid,  \
            default_global_gid, \
            default_customer_gid);  \
    }

#define MOCK_ZPN_BROKER_PBROKER_CONN_CALLBACK(T)    \
    int zpath_wrapper_zpn_broker_pbroker_conn_callback(struct fohh_connection *connection, \
                                                    enum fohh_connection_state state, \
                                                    void *cookie) {  \
        return T::get_mock()->zpath_wrapper_zpn_broker_pbroker_conn_callback(connection, state, cookie);    \
    }

#define MOCK_ZPN_BROKER_PBROKER_CONTROL_CONN_MONITOR_CALLBACK(T)    \
    void zpath_wrapper_zpn_broker_pbroker_control_conn_monitor_cb(evutil_socket_t sock, short flags, void *cookie) {  \
        return T::get_mock()->zpath_wrapper_zpn_broker_pbroker_control_conn_monitor_cb(sock,flags,cookie);    \
    }

#define MOCK_ZPN_BROKER_PBROKER_CONTROL_CONN_REDIRECT(T)    \
   void zpath_wrapper_zpn_broker_pbroker_control_conn_redirect(struct fohh_connection *f_conn) { \
        return T::get_mock()->zpath_wrapper_zpn_broker_pbroker_control_conn_redirect(f_conn);    \
   }

#define MOCK_ZPN_BROKER_ASSISTANT_COTROL_DESTORY_WITH_ASST_LOCK(T)    \
   void zpath_wrapper_zpn_broker_assistant_control_destroy_with_asst_lock(struct connected_assistant *asst) { \
        return T::get_mock()->zpath_wrapper_zpn_broker_assistant_control_destroy_with_asst_lock(asst);    \
   }

#define MOCK_ZPN_BROKER_REMOVE_TUNNEL_INFO_BY_SC_GID(T)    \
   void zpath_wrapper_zpn_broker_remove_tunnel_info_by_sc_gid(int64_t sc_gid) { \
        return T::get_mock()->zpath_wrapper_zpn_broker_remove_tunnel_info_by_sc_gid(sc_gid);    \
   }

#define MOCK_ZPN_BROKER_SITEC_CONTROL_CONN_REDIRECT(T)    \
   void zpath_wrapper_zpn_broker_sitec_control_conn_redirect(struct fohh_connection *f_conn) { \
        return T::get_mock()->zpath_wrapper_zpn_broker_sitec_control_conn_redirect(f_conn);    \
   }

#define MOCK_ZPN_FREE(T)    \
   void zpath_wrapper_zpn_free(void *ptr, int line, const char* file) { \
        return T::get_mock()->zpath_wrapper_zpn_free(ptr, line, file);    \
   }

#endif //MOCK_ZPN_AUTH_LOG_MOCKS_H
