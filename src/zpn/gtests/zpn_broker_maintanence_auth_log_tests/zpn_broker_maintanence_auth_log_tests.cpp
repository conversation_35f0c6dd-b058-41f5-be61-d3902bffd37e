/*
 * zpn_broker_maintanence_auth_log_tests.cpp Copyright (C) 2025 Zscaler, Inc. All Rights Reserved
 */

#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include "test_misc/ZscalerMockBase.h"

extern "C" {
    #include "test_headers/mock_zpn_auth_log_mocks.h"
    #include "test_headers/external_imports.h"
}

using ::testing::Return;
using ::testing::StrEq;
using ::testing::StrictMock;
using ::testing::_;

/**** Test environment setup recipe starts ***********************/

class TestEnvironment : public ::testing::Environment {
  public:
    virtual void SetUp() {
        size_t size = 512;
        int res = argo_library_init(size);
        ASSERT_TRUE(ZPN_RESULT_NO_ERROR == res);

        //create global context
        mock_create_global_config();

        zpn_rpc_init();
        zpath_customer_init(NULL, 0, 0, 0);

        struct fohh_generic_server *sni_server = zpath_debug_lookup_fohh_generic_server("broker");
        zpn_broker_pbroker_listen(sni_server);
    }

    virtual void TearDown() {
    }

  private:
    static void mock_create_global_config() {
        static struct zpath_cloud cloud = {};
        static char *ip_anchor_clouds[] = {"zscaler.net", "zsdemo.net"};
        cloud.ip_anchor_clouds = ip_anchor_clouds;
        cloud.ip_anchor_clouds_count = sizeof(ip_anchor_clouds) / sizeof(char *);
        zpath_cloud_global_state.current_config = &cloud;

        // set lat/long for broker location
        static struct zpath_instance current_config;
        memset(&current_config, 0, sizeof(struct zpath_instance));
        current_config.latitude = 38.00;
        current_config.longitude = -123.00;

        zpath_instance_global_state.current_config = &current_config;
    }
};

int main(int argc, char *argv[]) {
    ::testing::InitGoogleTest(&argc, argv);
    // gtest takes ownership of the TestEnvironment ptr - we don't delete it.
    ::testing::AddGlobalTestEnvironment(new TestEnvironment);
    return RUN_ALL_TESTS();
}
/******* Test environment setup recipe ends ***********************/

/******* Mock Class Recipe Starts *********************************/
class ZpnClientAuthLogMockBase
        : public mockZpnApi_zpn_free,
          public mockZpnApi_zpn_broker_pbroker_control_conn_redirect,
          public mockZpnApi_zpn_broker_assistant_control_destroy_with_asst_lock,
          public mockZpnApi_zpn_broker_remove_tunnel_info_by_sc_gid,
          public mockZpnApi_zpn_broker_sitec_control_conn_redirect,
          public mockZpnApi_zpn_config_override_get_config_int {
};

class ZpnClientAuthLogMockTester
        : public ZscalerMockBase<ZpnClientAuthLogMockBase> {
  public:
    /* Setup method called before every test suite*/
    void SetUp() override {
        ZscalerMockBase::SetUp();

        c_state = zpn_broker_c_state_alloc();
        ASSERT_TRUE(c_state);

        c_state->client_thread = zevent_self();
        //c_state->customer_gid = 1;
        zpn_broker_client_state_init(c_state, zpn_client_type_zapp, zpn_tunnel_auth_zapp, zpn_fohh_tlv);

        memset(&fohh_tlv, 0, sizeof(struct zpn_fohh_tlv));
        fohh_tlv.tlv.conn.f_conn = (struct fohh_connection *)ZPN_CALLOC(sizeof(*fohh_tlv.tlv.conn.f_conn));
        ASSERT_TRUE(fohh_tlv.tlv.conn.f_conn);

        int res = zpn_fohh_tlv_init(&(c_state->tlv_state), fohh_tlv.tlv.conn.f_conn, c_state->incarnation);
        ASSERT_TRUE(!res);

        fohh_connection_set_dynamic_cookie(fohh_tlv.tlv.conn.f_conn, c_state);

        g_broker_common_cfg = &(this->g_broker_cfg);
        g_broker_common_cfg->instance_type  = ZPN_INSTANCE_TYPE_PUBLIC_BROKER;
    }
    /* Teardown method called after every test suite*/
    void TearDown() override {
        ZscalerMockBase::TearDown();
        ZPN_FREE(c_state);
        ZPN_FREE(fohh_tlv.tlv.conn.f_conn);
        g_broker_common_cfg = nullptr;
    }

    struct zpn_broker_client_fohh_state *c_state;
    struct zpn_fohh_tlv fohh_tlv;
    struct zpn_common_broker_cfg g_broker_cfg = {};
};

extern "C" {
    #include "zpath_lib/zpath_config_override.h"
    #include "zpath_lib/zpath_config_override_keys.h"
    MOCK_ZPN_FEATURE_FLAG_CONFIG_OVERRIDE_GET_CONFIG_INT(ZpnClientAuthLogMockTester);
    MOCK_ZPN_BROKER_PBROKER_CONTROL_CONN_REDIRECT(ZpnClientAuthLogMockTester);
    MOCK_ZPN_BROKER_ASSISTANT_COTROL_DESTORY_WITH_ASST_LOCK(ZpnClientAuthLogMockTester);
    MOCK_ZPN_BROKER_REMOVE_TUNNEL_INFO_BY_SC_GID(ZpnClientAuthLogMockTester);
    MOCK_ZPN_BROKER_SITEC_CONTROL_CONN_REDIRECT(ZpnClientAuthLogMockTester);
    MOCK_ZPN_FREE(ZpnClientAuthLogMockTester);
    int zpath_wrapper_zpn_broker_pbroker_conn_callback(struct fohh_connection *connection,
                                                    enum fohh_connection_state state,
                                                    void *cookie);
    void zpath_wrapper_zpn_broker_pbroker_control_conn_monitor_cb(evutil_socket_t sock, short flags, void *cookie);
    void zpath_wrapper_zpn_broker_assistant_control_conn_monitor_cb(evutil_socket_t sock, short flags, void *cookie);
    int  zpath_wrapper_zpn_broker_assistant_control_conn_callback(struct fohh_connection *connection,
                                                          enum fohh_connection_state state,
                                                          void *cookie);
    void zpath_wrapper_zpn_broker_sitec_ctrl_conn_monitor_cb(evutil_socket_t sock, short flags, void *cookie);
    int zpath_wrapper_zpn_broker_sitec_ctrl_conn_callback(struct fohh_connection *connection,
                                                  enum fohh_connection_state state,
                                                  void *cookie);
}
/******* Mock Class Recipe Ends ********************************************/

/******** Test Suites ******************************************************/

/*
 * Validate if zpn_is_broker_auth_log_redirect_status_feature_enabled() API is working fine
 */
TEST_F(ZpnClientAuthLogMockTester, test_feature_flag_config_override) {
    int expected_feature_flag = 0;

    // Public Broker: Validate feature is disabled when feature flag is off
    g_broker_common_cfg->instance_type  = ZPN_INSTANCE_TYPE_PUBLIC_BROKER;

    expected_feature_flag = 0;
    EXPECT_CALL(*get_mock(), zpath_wrapper_config_override_get_config_int(_,_,_,_,_,_,_)).WillOnce(::testing::Return(0));
    ASSERT_EQ(expected_feature_flag, zpn_is_broker_auth_log_redirect_status_feature_enabled());

    // Public Broker: Validate feature is enabled when feature flag is on
    expected_feature_flag = 1;
    EXPECT_CALL(*get_mock(), zpath_wrapper_config_override_get_config_int(_,_,_,_,_,_,_)).WillOnce(::testing::Return(1));
    ASSERT_EQ(expected_feature_flag, zpn_is_broker_auth_log_redirect_status_feature_enabled());

    // Private Broker: Validate feature is disabled even if the feature is enabled globally
    g_broker_common_cfg->instance_type  = ZPN_INSTANCE_TYPE_PRIVATE_BROKER;
    expected_feature_flag = 0;
    ASSERT_EQ(expected_feature_flag, zpn_is_broker_auth_log_redirect_status_feature_enabled());
}

/*
 *  PSE control connection: Validate if close_reason is set to FOHH_CLOSE_REASON_BROKER_REDIRECT
 *  when broker maintanence triggrs redirect with BRK_REDIRECT_REASON_BROKER_RESTART as the redirect reason
 */
TEST_F(ZpnClientAuthLogMockTester, test_close_reason_field_in_pse_control_conn_auth_log_for_broker_restart){
    char *expected_auth_close_reason = FOHH_CLOSE_REASON_BROKER_REDIRECT;
    evutil_socket_t sock = 1;
    short flags = 1;

    // Setup the control connection
    fohh_connection broker_pse_control_conn;
    zpn_broker_pbroker_fohh_state *pb_state = (struct zpn_broker_pbroker_fohh_state *)ZPN_CALLOC(sizeof(*pb_state));
    fohh_connection_set_dynamic_cookie(&broker_pse_control_conn, pb_state);

    // Trigger maintainence redirect from broker
    EXPECT_CALL(*get_mock(), zpath_wrapper_config_override_get_config_int(_,_,_,_,_,_,_)).Times(2).WillRepeatedly(::testing::Return(1));
    EXPECT_CALL(*get_mock(), zpath_wrapper_zpn_broker_pbroker_control_conn_redirect(_)).WillRepeatedly(::testing::Invoke([](fohh_connection *broker_pse_control_conn) {
            return;
        }));
    zpn_broker_pbroker_redirect_pbrokers(1, BRK_REDIRECT_REASON_BROKER_RESTART);

    // Execute redirect in the control connection monitor callback
    pb_state->monitor_count = ZPN_TUNNEL_MONITOR_REDIRECT_INTERVAL_COUNT-1;
    zpath_wrapper_zpn_broker_pbroker_control_conn_monitor_cb(sock, flags, &broker_pse_control_conn);

    //Trigger auth log on DISCONNECT event
    EXPECT_CALL(*get_mock(), zpath_wrapper_zpn_free(_,_,_)).WillRepeatedly(::testing::Invoke([](void *ptr, int line, const char* file) {
            return;
        }));
    zpath_wrapper_zpn_broker_pbroker_conn_callback(&broker_pse_control_conn, fohh_connection_state::fohh_connection_disconnected, &broker_pse_control_conn);

    // Validate that the auth log has expected close_reason field
    ASSERT_STREQ(expected_auth_close_reason, pb_state->log.close_reason);

    ZPN_FREE(pb_state);
}

/*
 *  PSE control connection: Validate if close_reason is set to FOHH_CLOSE_REASON_BROKER_REDIRECT
 *  when broker maintanence triggrs redirect with BRK_REDIRECT_REASON_BROKER_MAINTENANCE as the redirect reason
 */
TEST_F(ZpnClientAuthLogMockTester, test_close_reason_field_in_pse_control_conn_auth_log_for_broker_maintanence){
    char *expected_auth_close_reason = FOHH_CLOSE_REASON_BROKER_REDIRECT;
    evutil_socket_t sock = 1;
    short flags = 1;

    // Setup the control connection
    fohh_connection broker_pse_control_conn;
    zpn_broker_pbroker_fohh_state *pb_state = (struct zpn_broker_pbroker_fohh_state *)ZPN_CALLOC(sizeof(*pb_state));
    fohh_connection_set_dynamic_cookie(&broker_pse_control_conn, pb_state);

    // Trigger maintainence redirect from broker
    EXPECT_CALL(*get_mock(), zpath_wrapper_config_override_get_config_int(_,_,_,_,_,_,_)).Times(2).WillRepeatedly(::testing::Return(1));
    EXPECT_CALL(*get_mock(), zpath_wrapper_zpn_broker_pbroker_control_conn_redirect(_)).WillRepeatedly(::testing::Invoke([](fohh_connection *broker_pse_control_conn) {
            return;
        }));
    zpn_broker_pbroker_redirect_pbrokers(1, BRK_REDIRECT_REASON_BROKER_MAINTENANCE);

    // Execute redirect in the control connection monitor callback
    pb_state->monitor_count = ZPN_TUNNEL_MONITOR_REDIRECT_INTERVAL_COUNT-1;
    zpath_wrapper_zpn_broker_pbroker_control_conn_monitor_cb(sock, flags, &broker_pse_control_conn);

    // Trigger auth log on DISCONNECT event
    EXPECT_CALL(*get_mock(), zpath_wrapper_zpn_free(_,_,_)).WillRepeatedly(::testing::Invoke([](void *ptr, int line, const char* file) {
            return;
        }));
    zpath_wrapper_zpn_broker_pbroker_conn_callback(&broker_pse_control_conn, fohh_connection_state::fohh_connection_disconnected, &broker_pse_control_conn);

    // Validate that the auth log has expected close_reason field
    ASSERT_STREQ(expected_auth_close_reason, pb_state->log.close_reason);

    ZPN_FREE(pb_state);
}

/*
 *  PSE control connection: Validate if close_reason is NOT set to FOHH_CLOSE_REASON_BROKER_REDIRECT
 *  when broker maintanence triggrs redirect with BRK_REDIRECT_REASON_NETWORK_CHANGE as the redirect reason
 */
TEST_F(ZpnClientAuthLogMockTester, test_close_reason_field_in_pse_control_conn_auth_log_for_network_change){
    char *expected_auth_close_reason = FOHH_CLOSE_REASON_BROKER_REDIRECT;
    evutil_socket_t sock = 1;
    short flags = 1;

    // Setup the control connection
    fohh_connection broker_pse_control_conn;
    zpn_broker_pbroker_fohh_state *pb_state = (struct zpn_broker_pbroker_fohh_state *)ZPN_CALLOC(sizeof(*pb_state));
    fohh_connection_set_dynamic_cookie(&broker_pse_control_conn, pb_state);

    // Trigger maintainence redirect from broker
    EXPECT_CALL(*get_mock(), zpath_wrapper_config_override_get_config_int(_,_,_,_,_,_,_)).Times(2).WillRepeatedly(::testing::Return(1));
    EXPECT_CALL(*get_mock(), zpath_wrapper_zpn_broker_pbroker_control_conn_redirect(_)).WillRepeatedly(::testing::Invoke([](fohh_connection *broker_pse_control_conn) {
            return;
        }));
    zpn_broker_pbroker_redirect_pbrokers(1, BRK_REDIRECT_REASON_NETWORK_CHANGE);

    // Execute redirect in the control connection monitor callback
    pb_state->monitor_count = ZPN_TUNNEL_MONITOR_REDIRECT_INTERVAL_COUNT-1;
    zpath_wrapper_zpn_broker_pbroker_control_conn_monitor_cb(sock, flags, &broker_pse_control_conn);

    // Trigger auth log on DISCONNECT event
    EXPECT_CALL(*get_mock(), zpath_wrapper_zpn_free(_,_,_)).WillRepeatedly(::testing::Invoke([](void *ptr, int line, const char* file) {
            return;
        }));
    zpath_wrapper_zpn_broker_pbroker_conn_callback(&broker_pse_control_conn, fohh_connection_state::fohh_connection_disconnected, &broker_pse_control_conn);

    // Validate that the auth log has expected close_reason field
    ASSERT_STRNE(expected_auth_close_reason, pb_state->log.close_reason);

    ZPN_FREE(pb_state);
}

/*
 *  AppC control connection: Validate if close_reason is set to FOHH_CLOSE_REASON_BROKER_REDIRECT
 *  when broker maintanence triggrs redirect with BRK_REDIRECT_REASON_BROKER_RESTART as the redirect reason
 */
TEST_F(ZpnClientAuthLogMockTester, test_close_reason_field_in_appc_control_conn_auth_log_for_broker_restart){
    char *expected_auth_close_reason = FOHH_CLOSE_REASON_BROKER_REDIRECT;
    evutil_socket_t sock = 1;
    short flags = 1;

    // Setup the control connection
    fohh_connection control_conn;
    connected_assistant *asst = (connected_assistant*)ZPN_CALLOC(sizeof(*asst));
    fohh_connection_set_dynamic_cookie(&control_conn, asst);

    // Trigger maintainence redirect from broker
    EXPECT_CALL(*get_mock(), zpath_wrapper_config_override_get_config_int(_,_,_,_,_,_,_)).Times(2).WillRepeatedly(::testing::Return(1));
    EXPECT_CALL(*get_mock(), zpath_wrapper_zpn_broker_assistant_control_destroy_with_asst_lock(_)).WillRepeatedly(::testing::Invoke([](connected_assistant *asst) {
            return;
        }));
    zpn_broker_assistant_redirect_assistants(1, BRK_REDIRECT_REASON_BROKER_RESTART);

    // Execute redirect in the control connection monitor callback
    asst->monitor_count = ZPN_TUNNEL_MONITOR_REDIRECT_INTERVAL_COUNT-1;
    zpath_wrapper_zpn_broker_assistant_control_conn_monitor_cb(sock, flags, asst);

    // Trigger auth log on DISCONNECT event
    zpath_wrapper_zpn_broker_assistant_control_conn_callback(&control_conn, fohh_connection_state::fohh_connection_disconnected, &control_conn);

    // Validate that the auth log has expected close_reason field
    ASSERT_STREQ(expected_auth_close_reason, asst->auth_log.close_reason);

    ZPN_FREE(asst);
}

/*
 *  AppC control connection: Validate if close_reason is set to FOHH_CLOSE_REASON_BROKER_REDIRECT
 *  when broker maintanence triggrs redirect with BRK_REDIRECT_REASON_BROKER_MAINTENANCE as the redirect reason
 */
TEST_F(ZpnClientAuthLogMockTester, test_close_reason_field_in_appc_control_conn_auth_log_for_broker_maintanence){
    char *expected_auth_close_reason = FOHH_CLOSE_REASON_BROKER_REDIRECT;
    evutil_socket_t sock = 1;
    short flags = 1;

    // Setup the control connection
    fohh_connection control_conn;
    connected_assistant *asst = (connected_assistant*)ZPN_CALLOC(sizeof(*asst));
    fohh_connection_set_dynamic_cookie(&control_conn, asst);

    // Trigger maintainence redirect from broker
    EXPECT_CALL(*get_mock(), zpath_wrapper_config_override_get_config_int(_,_,_,_,_,_,_)).Times(2).WillRepeatedly(::testing::Return(1));
    EXPECT_CALL(*get_mock(), zpath_wrapper_zpn_broker_assistant_control_destroy_with_asst_lock(_)).WillRepeatedly(::testing::Invoke([](connected_assistant *asst) {
            return;
        }));
    zpn_broker_assistant_redirect_assistants(1, BRK_REDIRECT_REASON_BROKER_MAINTENANCE);

    // Execute redirect in the control connection monitor callback
    asst->monitor_count = ZPN_TUNNEL_MONITOR_REDIRECT_INTERVAL_COUNT-1;
    zpath_wrapper_zpn_broker_assistant_control_conn_monitor_cb(sock, flags, asst);

    // Trigger auth log on DISCONNECT event
    zpath_wrapper_zpn_broker_assistant_control_conn_callback(&control_conn, fohh_connection_state::fohh_connection_disconnected, &control_conn);

    // Validate that the auth log has expected close_reason field
    ASSERT_STREQ(expected_auth_close_reason, asst->auth_log.close_reason);

    ZPN_FREE(asst);
}

/*
 *  AppC control connection: Validate if close_reason is NOT set to FOHH_CLOSE_REASON_BROKER_REDIRECT
 *  when broker maintanence triggrs redirect with BRK_REDIRECT_REASON_NETWORK_CHANGE as the redirect reason
 */
TEST_F(ZpnClientAuthLogMockTester, test_close_reason_field_in_appc_control_conn_auth_log_for_broker_for_network_change){
    char *expected_auth_close_reason = FOHH_CLOSE_REASON_BROKER_REDIRECT;
    evutil_socket_t sock = 1;
    short flags = 1;

    // Setup the control connection
    fohh_connection control_conn;
    connected_assistant *asst = (connected_assistant*)ZPN_CALLOC(sizeof(*asst));
    fohh_connection_set_dynamic_cookie(&control_conn, asst);

    // Trigger maintainence redirect from broker
    EXPECT_CALL(*get_mock(), zpath_wrapper_config_override_get_config_int(_,_,_,_,_,_,_)).Times(2).WillRepeatedly(::testing::Return(1));
    EXPECT_CALL(*get_mock(), zpath_wrapper_zpn_broker_assistant_control_destroy_with_asst_lock(_)).WillRepeatedly(::testing::Invoke([](connected_assistant *asst) {
            return;
        }));
    zpn_broker_assistant_redirect_assistants(1, BRK_REDIRECT_REASON_NETWORK_CHANGE);

    // Execute redirect in the control connection monitor callback
    asst->monitor_count = ZPN_TUNNEL_MONITOR_REDIRECT_INTERVAL_COUNT-1;
    zpath_wrapper_zpn_broker_assistant_control_conn_monitor_cb(sock, flags, asst);

    // Trigger auth log on DISCONNECT event
    zpath_wrapper_zpn_broker_assistant_control_conn_callback(&control_conn, fohh_connection_state::fohh_connection_disconnected, &control_conn);

    // Validate that the auth log has expected close_reason field
    ASSERT_STRNE(expected_auth_close_reason, asst->auth_log.close_reason);

    ZPN_FREE(asst);
}

/*
 *  SiteC/PCC control connection: Validate if close_reason is set to FOHH_CLOSE_REASON_BROKER_REDIRECT
 *  when broker maintanence triggrs redirect with BRK_REDIRECT_REASON_BROKER_RESTART as the redirect reason
 */
TEST_F(ZpnClientAuthLogMockTester, test_close_reason_field_in_sitec_control_conn_auth_log_for_broker_restart){
    char *expected_auth_close_reason = FOHH_CLOSE_REASON_BROKER_REDIRECT;
    evutil_socket_t sock = 1;
    short flags = 1;

    // Setup the control connection
    fohh_connection control_conn;
    zpn_broker_sitec_fohh_state *sitec_state = (zpn_broker_sitec_fohh_state *)ZPN_CALLOC(sizeof(*sitec_state));
    fohh_connection_set_dynamic_cookie(&control_conn, sitec_state);

    // Trigger maintainence redirect from broker
    EXPECT_CALL(*get_mock(), zpath_wrapper_config_override_get_config_int(_,_,_,_,_,_,_)).Times(2).WillRepeatedly(::testing::Return(1));
    EXPECT_CALL(*get_mock(), zpath_wrapper_zpn_broker_sitec_control_conn_redirect(_)).WillRepeatedly(::testing::Invoke([](fohh_connection *control_conn) {
            return;
        }));
    EXPECT_CALL(*get_mock(), zpath_wrapper_zpn_broker_remove_tunnel_info_by_sc_gid(_)).WillRepeatedly(::testing::Invoke([](int64_t sc_gid) {
             return;
         }));
    zpn_broker_sitec_set_redirect_sitec_connections(1, BRK_REDIRECT_REASON_BROKER_RESTART);

    // Execute redirect in the control connection monitor callback
    sitec_state->monitor_count = ZPN_TUNNEL_MONITOR_REDIRECT_INTERVAL_COUNT-1;
    zpath_wrapper_zpn_broker_sitec_ctrl_conn_monitor_cb(sock, flags, &control_conn);

    // Trigger auth log on DISCONNECT event
    EXPECT_CALL(*get_mock(), zpath_wrapper_zpn_free(_,_,_)).WillRepeatedly(::testing::Invoke([](void *ptr, int line, const char* file) {
            return;
        }));
    zpath_wrapper_zpn_broker_sitec_ctrl_conn_callback(&control_conn, fohh_connection_state::fohh_connection_disconnected, &control_conn);

    // Validate that the auth log has expected close_reason field
    ASSERT_STREQ(expected_auth_close_reason, sitec_state->log.close_reason);

    ZPN_FREE(sitec_state);
}

/*
 *  SiteC/PCC control connection: Validate if close_reason is set to FOHH_CLOSE_REASON_BROKER_REDIRECT
 *  when broker maintanence triggrs redirect with BRK_REDIRECT_REASON_BROKER_MAINTENANCE as the redirect reason
 */
TEST_F(ZpnClientAuthLogMockTester, test_close_reason_field_in_sitec_control_conn_auth_log_for_broker_maintanence){
    char *expected_auth_close_reason = FOHH_CLOSE_REASON_BROKER_REDIRECT;
    evutil_socket_t sock = 1;
    short flags = 1;

    // Setup the control connection
    fohh_connection control_conn;
    zpn_broker_sitec_fohh_state *sitec_state = (zpn_broker_sitec_fohh_state *)ZPN_CALLOC(sizeof(*sitec_state));
    fohh_connection_set_dynamic_cookie(&control_conn, sitec_state);

    // Trigger maintainence redirect from broker
    EXPECT_CALL(*get_mock(), zpath_wrapper_config_override_get_config_int(_,_,_,_,_,_,_)).Times(2).WillRepeatedly(::testing::Return(1));
    EXPECT_CALL(*get_mock(), zpath_wrapper_zpn_broker_sitec_control_conn_redirect(_)).WillRepeatedly(::testing::Invoke([](fohh_connection *control_conn) {
            return;
        }));
    EXPECT_CALL(*get_mock(), zpath_wrapper_zpn_broker_remove_tunnel_info_by_sc_gid(_)).WillRepeatedly(::testing::Invoke([](int64_t sc_gid) {
             return;
         }));
    zpn_broker_sitec_set_redirect_sitec_connections(1, BRK_REDIRECT_REASON_BROKER_MAINTENANCE);

    // Execute redirect in the control connection monitor callback
    sitec_state->monitor_count = ZPN_TUNNEL_MONITOR_REDIRECT_INTERVAL_COUNT-1;
    zpath_wrapper_zpn_broker_sitec_ctrl_conn_monitor_cb(sock, flags, &control_conn);

    // Trigger auth log on DISCONNECT event
    EXPECT_CALL(*get_mock(), zpath_wrapper_zpn_free(_,_,_)).WillRepeatedly(::testing::Invoke([](void *ptr, int line, const char* file) {
            return;
        }));
    zpath_wrapper_zpn_broker_sitec_ctrl_conn_callback(&control_conn, fohh_connection_state::fohh_connection_disconnected, &control_conn);

    // Validate that the auth log has expected close_reason field
    ASSERT_STREQ(expected_auth_close_reason, sitec_state->log.close_reason);

    ZPN_FREE(sitec_state);
}

/*
 *  SiteC/PCC control connection: Validate if close_reason is NOT set to FOHH_CLOSE_REASON_BROKER_REDIRECT
 *  when broker maintanence triggrs redirect with BRK_REDIRECT_REASON_NETWORK_CHANGE as the redirect reason
 */
TEST_F(ZpnClientAuthLogMockTester, test_close_reason_field_in_sitec_control_conn_auth_log_for_network_change){
    char *expected_auth_close_reason = FOHH_CLOSE_REASON_BROKER_REDIRECT;
    evutil_socket_t sock = 1;
    short flags = 1;

    // Setup the control connection
    fohh_connection control_conn;
    zpn_broker_sitec_fohh_state *sitec_state = (zpn_broker_sitec_fohh_state *)ZPN_CALLOC(sizeof(*sitec_state));
    fohh_connection_set_dynamic_cookie(&control_conn, sitec_state);

    // Trigger maintainence redirect from broker
    EXPECT_CALL(*get_mock(), zpath_wrapper_config_override_get_config_int(_,_,_,_,_,_,_)).Times(2).WillRepeatedly(::testing::Return(1));
    EXPECT_CALL(*get_mock(), zpath_wrapper_zpn_broker_sitec_control_conn_redirect(_)).WillRepeatedly(::testing::Invoke([](fohh_connection *control_conn) {
            return;
        }));
    EXPECT_CALL(*get_mock(), zpath_wrapper_zpn_broker_remove_tunnel_info_by_sc_gid(_)).WillRepeatedly(::testing::Invoke([](int64_t sc_gid) {
             return;
         }));
    zpn_broker_sitec_set_redirect_sitec_connections(1, BRK_REDIRECT_REASON_NETWORK_CHANGE);

    // Execute redirect in the control connection monitor callback
    sitec_state->monitor_count = ZPN_TUNNEL_MONITOR_REDIRECT_INTERVAL_COUNT-1;
    zpath_wrapper_zpn_broker_sitec_ctrl_conn_monitor_cb(sock, flags, &control_conn);

    // Trigger auth log on DISCONNECT event
    EXPECT_CALL(*get_mock(), zpath_wrapper_zpn_free(_,_,_)).WillRepeatedly(::testing::Invoke([](void *ptr, int line, const char* file) {
            return;
        }));
    zpath_wrapper_zpn_broker_sitec_ctrl_conn_callback(&control_conn, fohh_connection_state::fohh_connection_disconnected, &control_conn);

    // Validate that the auth log has expected close_reason field
    ASSERT_STRNE(expected_auth_close_reason, sitec_state->log.close_reason);

    ZPN_FREE(sitec_state);
}
