/*
 * zpn_mconn_tlv.h. Copyright (C) 2014 Zscaler Inc. All rights reserved.
 */

#ifndef _ZPN_MCONN_TLV_H_
#define _ZPN_MCONN_TLV_H_

#include <event2/event.h>

#include <sys/queue.h>
#include "ztlv/zpn_tlv.h"
#include "fohh/fohh_private.h"
#include "zpn/zpn_lib.h"
#include "zpn/zpn_mconn.h"
#include "fohh/fohh_private.h"

enum flow_ctrl_status {
    flow_ctrl_none = 0,
    flow_ctrl_disabled,
    flow_ctrl_enabled
};

enum latency_inspection_stage {
    latency_inspection_stage_rx_0 = 0,
    latency_inspection_stage_tx_0,
    latency_inspection_stage_tx_1,
    latency_inspection_stage_tx_2,
    latency_inspection_stage_tx_3,
    latency_inspection_stage_last = latency_inspection_stage_tx_3,
    latency_inspection_stage_num
};

#define STATES_SEEN_MAX 64
#define STATES_SEEN_MAX_BIT (STATES_SEEN_MAX - 1)
enum states_seen {
    states_seen_0 = 0,
    states_seen_1,
    states_seen_2,
    states_seen_last = states_seen_2
};

extern int fohh_tlv_window_size;
extern int fohh_tlv_mconn_window_size;

TAILQ_HEAD(zpn_mconn_fohh_tlv_head, zpn_mconn_fohh_tlv);

#define OLD_SCHEME       0

/*
 * Structure for tracking tags and their associated mconns for an FOHH
 * TLV connection.
 *
 * Never access directly, please.
 */
struct zpn_fohh_tlv {
    struct zpn_tlv tlv;

    /* Lock for accessing all data within this structure */
    zpath_mutex_t lock;

    /* The TLV FOHH connection this structure is managing. These two are moved inside zpn_tlv */
    //struct fohh_connection *f_conn;
    //int64_t f_conn_incarnation;

    /* Indexed by tag. The key is a copied reference */
    struct argo_hash_table *tags;

#if OLD_SCHEME
    /* List of mconn's associated with this TLV */
    struct zpn_mconn_fohh_tlv_head mconn_list;
    int32_t list_size;

    /* zpn_mconn to drain next */
    struct zpn_mconn_fohh_tlv *next_mconn;
#else
    /* List of busy mconn's associated with this TLV */
    struct zpn_mconn_fohh_tlv_head busy_mconn_list;
    int32_t busy_list_size;

    /* List of idle mconn's associated with this TLV */
    struct zpn_mconn_fohh_tlv_head idle_mconn_list;
    int32_t idle_list_size;
    int f_conn_down;
#endif

    /* Timer to drain mconn associated with this fohh connection */
    struct event *drain_ev;

    /* Timer to monitor tcp connection */
    struct event *monitor_ev;

    /* Timer to monitor tcp connection */
    struct event *expire_ev;

    /* Timer to tune tcp connection */
    struct event *tune_ev;

    /*
     * 1: Pause/ressume the whole fohh_tlv connection
     * 0: Pause/reesume eachh mconn
     */
    int tunnel_pr;

    int64_t rx_data;
    int64_t tx_data;
    int64_t tx_data_drop;

    int64_t rx_data_us;                             /* last time we sent out data */
    int64_t tx_data_us;                             /* last time we received data */
    int64_t enq_data_us;                            /* last time we got data from peer */

    int64_t tx_limit;                               /* Amount of data we can sent according to peer receiver */
    int64_t tx_limit_update_us;                     /* The lastest time tx_limit was actually changed */
    int64_t remote_rx_data;                         /* Ammount of data remote received */
    int64_t remote_rx_data_change_us;
    enum flow_ctrl_status  remote_fc_status;
    int64_t last_wnd_update_us;                     /* Last time we sent window update message to remote */
    int64_t last_batch_wnd_update_us;               /* Last time we sent batch window update message to remote */
    int64_t remote_tx_limit;                        /* The tx_limit we sent to remote for which we are receiver */
    int64_t peer_tx_data;                           /* Amount of sent by peers of mconns of this fohh conn */
    int64_t rx_udp_data_dropped_frame_error;        /* Amount of rx udp data dropped due to framing error fohh conn */
    int64_t rx_udp_data_dropped_tx_buf_full;        /* Amount of rx udp data dropped due to full tx buf fohh conn */
    int64_t rx_icmp_error_data_dropped;             /* Amount of rx icmp error case data dropped */
    int64_t rx_icmp6_error_data_dropped;            /* Amount of rx icmp6 error case data dropped */
    int64_t fohh_data_write_blocked;                /* Number of blocked time writing on to this connection*/
    int64_t fohh_fc_blocked;                        /* Number of blocked time due to no credit to send*/
    int64_t fohh_data_write_fohh_blocked;           /* Number of blocked time writing on to this connection*/
    int64_t fohh_data_write_evbuf_blocked;          /* Number of blocked time writing on to this connection*/

    int64_t enq_bytes;
    int64_t deq_bytes;

    int64_t fc_blocked_timestamp;
    int64_t fc_blocked_timestamp_initial;

    int32_t unblock_thread_call_count;

    int32_t max_tag_id;
    int64_t fohh_tlv_last_seen_kick_flow_us;
    int64_t continuous_kick_flow_control_seen;
    int kick_flow_control_reset;                    /* flag indicate this fohh is not healthy due to flow control over flow and better to be reset*/
    int64_t max_fohh_fc_blocked_time;
    int64_t tot_fohh_fc_blocked_time;
    int64_t fohh_fc_blocked_time;                  /* Blocked time in us*/
    int64_t window_update_delta_time;
    int64_t max_window_update_delta_time;
    int64_t fohh_connection_disable_read_cnt;                           /* number of fohh connection disable reads */
    int64_t fohh_connection_enable_read_cnt;                            /* number of fohh connection disable reads */
    int64_t fohh_connection_disable_read_client_tx_buff_high_cnt;       /* number of fohh connection disable reads */
    int64_t fohh_connection_enable_read_client_tx_buff_high_cnt;        /* number of fohh connection enable reads */
    int64_t fohh_connection_disable_read_client_tx_buff_high_total_us;  /* fohh connection disable reads total time us from all mt */
    int64_t last_filled_buf;
    int64_t fohh_win_update_expired;
    int64_t fohh_win_update_low_water_mark;
    int64_t fohh_win_update_available_buf_above_threshold;
    uint64_t hop_latency;
    uint64_t max_hop_latency;
    uint64_t hop_latency_hist[ZPN_FOHH_HOP_LATENCY_HISTOGRAM_MAX];
    uint64_t pipeline_latency_max; // max of last stage below
    uint64_t pipeline_latency[latency_inspection_stage_num];
    uint64_t pipeline_latency_hist[ZPN_FOHH_PIPELINE_LATENCY_HISTOGRAM_MAX]; // base stage -> last stage
    struct zpn_mconn *pipeline_latency_mconn;
    uint64_t states_seen_bitmap;
};

#define FOHH_TIMER_CONN_MONITOR_S     (60)
#define FOHH_TLV_BLOCKED_RESET_US     (5*60*1000000)

/*
 * mconns can be independently linked to/from fohh_tlv's using
 * standard mconn routines.
 *
 * Never access directly, please.
 */
struct zpn_mconn_fohh_tlv {
    /* Embedded mconn. */
    struct zpn_mconn mconn;	 	 /* This has to be the first member */

    /* If zero: Not attached to fohh connection. Else connected. */
    int32_t tag_id;

    /* List of mconn's within a single fohh_connection. */
    TAILQ_ENTRY(zpn_mconn_fohh_tlv) mconn_list_entry;

    /* Indicate whether we are on busy queue or idle queue */
    int busy;

    /*a single mconn can disable the entire fohh_tlv read socket, but need to remember to enable read socket back */
    int disabled_read_socket;

    int64_t data_arrived;
    int32_t callbacks;
    int32_t buffer_under;
    int32_t buffer_over;
    int64_t data_to_peer_attemp;

    int remote_paused;
    int batch_window_update_needed; // pre-determined batch window update needed
    int64_t pause_sent_us;

    int64_t tx_data;                                /* Amount of data we have sent so far */
    int64_t tx_limit;                               /* Amount of data we can sent according to peer receiver */
    int64_t remote_rx_data;                         /* Amount of data remote received */
    int64_t remote_rx_data_change_us;
    enum flow_ctrl_status  remote_fc_status;
    int64_t last_wnd_tx_update_us;                  /* Last time we sent window update message to remote */
    int64_t last_wnd_rx_update_us;                  /* Last time we received window update message to remote */
    int64_t remote_tx_limit;                        /* The tx_limit we sent to remote for which we are receiver */
    int64_t prev_batch_remote_tx_limit;             /* The previous tx_limit we sent to remote in a batch for which we are receiver */

    int64_t bytes_from_peer;

    int fc_blocked;                                 /* Flow control blocked because of lack of credit */
    int64_t last_unblock_cb_process_us;             /* Timestamp on last processed time at zpn_fohh_tlv_unblock_cb */
    int64_t disable_read_client_tx_buff_high_time_start_us;
};



/*
 * Initialize a structure for managing an f_conn.
 *
 * Common use is for the fohh_tlv state to be statically allocated-
 * thus we only do initialization, not allocation.
 */
int zpn_fohh_tlv_init(struct zpn_fohh_tlv *fohh_tlv,
                      struct fohh_connection *f_conn,
                      int64_t tlv_incarnation);

void zpn_fohh_tlv_clear_state(struct zpn_fohh_tlv *fohh_tlv);
void zpn_fohh_tlv_set_conn(struct zpn_fohh_tlv *fohh_tlv, struct fohh_connection *f_conn);
struct fohh_connection *zpn_mconn_fohh_tlv_get_conn(struct zpn_fohh_tlv *fohh_tlv);
//int64_t zpn_mconn_fohh_tlv_get_incarnation(struct zpn_fohh_tlv *fohh_tlv);

/*
 * Get global owner data from fohh_tlv + tag.
 * (i.e. mtunnel from connection+tag)
 */
void *zpn_fohh_tlv_get_global_owner(struct zpn_fohh_tlv *fohh_tlv,
                                    int32_t tag_id);

void *zpn_fohh_tlv_get_local_owner(struct zpn_fohh_tlv *fohh_tlv,
                                   int32_t tag_id);

/*
 * Destroy/dealloc all elements of a structure that was managing an
 * fohh_tlv. Does not free fohh_tlv.
 */
int zpn_fohh_tlv_destroy(struct zpn_fohh_tlv *fohh_tlv, const char *reason);

/*
 * Update transmit limit for fohh_tlv
 */
void zpn_fohh_tlv_set_tx_limit(struct zpn_fohh_tlv *fohh_tlv, int64_t tx_limit);

/*
 * Init fohh_tlv mconn.
 */
int zpn_mconn_fohh_tlv_init(struct zpn_mconn_fohh_tlv *mconn_fohh_tlv,
                            void *mconn_self,
                            enum zpn_mconn_type type);


/*
 * TLV data callback for the fohh_connection...
 */
int zpn_fohh_tlv_data_callback(struct fohh_connection *connection,
                               void *cookie,
                               int32_t tag,
                               int32_t data_length,
                               struct evbuffer *data);


extern const struct zpn_mconn_local_owner_calls zpn_mconn_fohh_tlv_calls;

/* Call to unblock the zpn_fohh_tlv */
int zpn_fohh_tlv_unblock_cb(struct zpn_fohh_tlv *fohh_tlv);

int zpn_mconn_fohh_tlv_clean(struct zpn_mconn_fohh_tlv *mconn_fohh_tlv);

void zpn_mconn_fohh_tlv_internal_display(struct zpn_mconn_fohh_tlv *mconn_fohh_tlv);

void zpn_mconn_fohh_tlv_add_drain_timer(struct fohh_connection *connection, struct zpn_fohh_tlv *fohh_tlv);

void zpn_mconn_fohh_tlv_add_monitor_timer(struct fohh_connection *connection,
                                          struct zpn_fohh_tlv *fohh_tlv,
                                          event_callback_fn monitor_timer_cb,
                                          int64_t secs,
                                          int64_t usecs);

void zpn_mconn_fohh_tlv_remove_monitor_timer(struct zpn_fohh_tlv *fohh_tlv);

void zpn_mconn_fohh_tlv_add_expire_timer(struct fohh_connection *connection,
                                         struct zpn_fohh_tlv *fohh_tlv,
                                         int exipire_time,
                                         event_callback_fn expire_timer_cb);

void zpn_mconn_fohh_tlv_add_tune_timer(struct fohh_connection *connection,
                                       struct zpn_fohh_tlv *fohh_tlv,
                                       event_callback_fn tune_timer_cb,
                                       int64_t secs,
                                       int64_t usecs);

void zpn_mconn_fohh_tlv_activate_drain_timer(struct zpn_fohh_tlv *fohh_tlv);

/*
 * Update transmit limit for mconn_fohh_tlv
 */
void zpn_mconn_fohh_tlv_set_tx_limit(struct zpn_mconn_fohh_tlv *mconn_fohh_tlv, int64_t tx_limit);

/*
 * Dump flow control information of fohh_tlv
 */

void zpn_mconn_fohh_tlv_fc_monitor(struct zpn_fohh_tlv *fohh_tlv, int log_status);


/*
 * Add debug commands to set window size for fohh connection and its mconns
 */
int zpn_mconn_fohh_tlv_init_debug(void);

int64_t zpn_mconn_fohh_tlv_get_fohh_window_size();
int64_t zpn_mconn_fohh_tlv_get_fohh_mconn_window_size();

void zpn_mconn_fohh_tlv_app_buffer_tune_enable(int enhanced, int fohh_window, int fohh_mconn_window, int64_t fohh_watermark, int64_t fohh_mconn_watermark);
int zpn_mconn_fohh_tlv_set_disable_fix_to_enable_socket_read_unbind_flag(int enable);

static inline char *zpn_fohh_tlv_pipeline_latency_info(struct zpn_fohh_tlv *fohh_tlv, char *s, char *e)
{
  assert(fohh_tlv);
  assert(ZPN_FOHH_PIPELINE_LATENCY_HISTOGRAM_MAX - 1 == 10);
  s += sxprintf(s, e," \"pipe_latency_0\":\"%lu\", \"pipe_latency_1\":\"%lu\", \"pipe_latency_2\":\"%lu\", \"pipe_latency_3\":\"%lu\", "
                     "\"pipe_latency_4\":\"%lu\", \"pipe_latency_max\":\"%lu\", \"50us\":\"%lu\", \"100us\":\"%lu\", \"250us\":\"%lu\", "
                     "\"500us\":\"%lu\", \"1ms\":\"%lu\", \"5ms\":\"%lu\", \"10ms\":\"%lu\", \"25ms\":\"%lu\", \"50ms\":\"%lu\", "
                     "\"100ms\":\"%lu\", \">100ms\":\"%lu\"",
                (unsigned long) fohh_tlv->pipeline_latency[0], (unsigned long) fohh_tlv->pipeline_latency[1],
                (unsigned long) fohh_tlv->pipeline_latency[2], (unsigned long) fohh_tlv->pipeline_latency[3],
                (unsigned long) fohh_tlv->pipeline_latency[4], (unsigned long) fohh_tlv->pipeline_latency_max,
                (unsigned long) fohh_tlv->pipeline_latency_hist[0], (unsigned long) fohh_tlv->pipeline_latency_hist[1],
                (unsigned long) fohh_tlv->pipeline_latency_hist[2], (unsigned long) fohh_tlv->pipeline_latency_hist[3],
                (unsigned long) fohh_tlv->pipeline_latency_hist[4], (unsigned long) fohh_tlv->pipeline_latency_hist[5],
                (unsigned long) fohh_tlv->pipeline_latency_hist[6], (unsigned long) fohh_tlv->pipeline_latency_hist[7],
                (unsigned long) fohh_tlv->pipeline_latency_hist[8], (unsigned long) fohh_tlv->pipeline_latency_hist[9],
                (unsigned long) fohh_tlv->pipeline_latency_hist[10]);
  return s;
}

// Owner FOHH thread lock must be held so only busy list entry appending can happen not list entry removal or traversal
static inline char *zpn_mconn_fohh_tlv_mconns_info(struct zpn_fohh_tlv *fohh_tlv, char *s, char *e)
{
    int done_one = 0;
    void *first = NULL;
    struct zpn_mconn_fohh_tlv *mconn_fohh_tlv = NULL;
    struct zpn_mconn *mconn = NULL;
    assert(fohh_tlv);
    ZPATH_MUTEX_LOCK(&(fohh_tlv->lock), __FILE__, __LINE__);
    int total_mconns = fohh_tlv->busy_list_size + fohh_tlv->idle_list_size;
    ZPATH_MUTEX_UNLOCK(&(fohh_tlv->lock), __FILE__, __LINE__);
    int count = 0;
    while (count < total_mconns) { // extra level of protection to ensure loop termination
      ZPATH_MUTEX_LOCK(&(fohh_tlv->lock), __FILE__, __LINE__); // see lock comments above
      mconn_fohh_tlv = TAILQ_FIRST(&(fohh_tlv->busy_mconn_list));
      if (!mconn_fohh_tlv) {
        ZPATH_MUTEX_UNLOCK(&(fohh_tlv->lock), __FILE__, __LINE__);
        break;
      }
      if (mconn_fohh_tlv == first) {
        ZPATH_MUTEX_UNLOCK(&(fohh_tlv->lock), __FILE__, __LINE__);
        break;
      }
      if (!done_one) {
		done_one = 1;
		first = mconn_fohh_tlv;
      }
      ZPATH_MUTEX_UNLOCK(&(fohh_tlv->lock), __FILE__, __LINE__);
      mconn = &(mconn_fohh_tlv->mconn);
      if (!mconn->global_owner) {
        break;
      }
      if (mconn->global_owner_calls) {
        (mconn->global_owner_calls->lock)(mconn,
                                          mconn->self,
                                          mconn->global_owner,
                                          mconn->global_owner_key,
                                          mconn->global_owner_key_length);
        size_t rlen = mconn->client_rx_data ? evbuffer_get_length(mconn->client_rx_data) : 0;
        size_t tlen = mconn->transmit_buffer && mconn->transmit_buffer->tx_buf ? evbuffer_get_length(mconn->transmit_buffer->tx_buf) : 0;
        s += sxprintf(s, e," \"mconn_tag_id\":\"%d\", \"rx_buf\":\"%zu\", \"tx_buf\":\"%zu\"", mconn_fohh_tlv->tag_id, rlen, tlen);
        ZPATH_MUTEX_LOCK(&(fohh_tlv->lock), __FILE__, __LINE__);
        TAILQ_REMOVE(&(fohh_tlv->busy_mconn_list), mconn_fohh_tlv, mconn_list_entry);
        TAILQ_INSERT_TAIL(&(fohh_tlv->busy_mconn_list), mconn_fohh_tlv, mconn_list_entry);
        ZPATH_MUTEX_UNLOCK(&(fohh_tlv->lock), __FILE__, __LINE__);
        (mconn->global_owner_calls->unlock)(mconn,
                                            mconn->self,
                                            mconn->global_owner,
                                            mconn->global_owner_key,
                                            mconn->global_owner_key_length);
      }
      count++;
    } // while
    s += sxprintf(s, e," \"total_mconns\":\"%d\"", total_mconns);
    return s;
}

static inline char *zpn_fohh_tlv_hop_latency_info(struct zpn_fohh_tlv *fohh_tlv, char *s, char *e)
{
  assert(fohh_tlv);
  s += sxprintf(s, e," \"hop_latency\":\"%lu\", \"max_hop_latency\":\"%lu\", \"10ms\":\"%lu\", \"20ms\":\"%lu\", \"30ms\":\"%lu\", "
                     "\"50ms\":\"%lu\", \"75ms\":\"%lu\", \"100ms\":\"%lu\", \"250ms\":\"%lu\", \"500ms\":\"%lu\", "
                     "\"1000ms\":\"%lu\", \">1000ms\":\"%lu\"",
                (unsigned long) fohh_tlv->hop_latency, (unsigned long) fohh_tlv->max_hop_latency,
                (unsigned long) fohh_tlv->hop_latency_hist[0], (unsigned long) fohh_tlv->hop_latency_hist[1],
                (unsigned long) fohh_tlv->hop_latency_hist[2], (unsigned long) fohh_tlv->hop_latency_hist[3],
                (unsigned long) fohh_tlv->hop_latency_hist[4], (unsigned long) fohh_tlv->hop_latency_hist[5],
                (unsigned long) fohh_tlv->hop_latency_hist[6], (unsigned long) fohh_tlv->hop_latency_hist[7],
                (unsigned long) fohh_tlv->hop_latency_hist[8], (unsigned long) fohh_tlv->hop_latency_hist[9]);
  return s;
}

static inline uint64_t zpn_fohh_tlv_hop_latency_hist(struct zpn_fohh_tlv *fohh_tlv, int bucket)
{
  assert(fohh_tlv);
  assert(bucket < ZPN_FOHH_HOP_LATENCY_HISTOGRAM_MAX);
  return  fohh_tlv->hop_latency_hist[bucket];
}

// called by thread owner
static inline void zpn_mconn_fohh_tlv_hop_latency_count(struct zpn_fohh_tlv *fohh_tlv, int64_t hop_latency)
{
  assert(fohh_tlv);
  if (hop_latency < ZPN_FOHH_HOP_LATENCY_BKT_0) {
    fohh_tlv->hop_latency_hist[0]++;
  } else if (hop_latency < ZPN_FOHH_HOP_LATENCY_BKT_1) {
    fohh_tlv->hop_latency_hist[1]++;
  } else if (hop_latency < ZPN_FOHH_HOP_LATENCY_BKT_2) {
    fohh_tlv->hop_latency_hist[2]++;
  } else if (hop_latency < ZPN_FOHH_HOP_LATENCY_BKT_3) {
    fohh_tlv->hop_latency_hist[3]++;
  } else if (hop_latency < ZPN_FOHH_HOP_LATENCY_BKT_4) {
    fohh_tlv->hop_latency_hist[4]++;
  } else if (hop_latency < ZPN_FOHH_HOP_LATENCY_BKT_5) {
    fohh_tlv->hop_latency_hist[5]++;
  } else if (hop_latency < ZPN_FOHH_HOP_LATENCY_BKT_6) {
    fohh_tlv->hop_latency_hist[6]++;
  } else if (hop_latency < ZPN_FOHH_HOP_LATENCY_BKT_7) {
    fohh_tlv->hop_latency_hist[7]++;
  } else if (hop_latency < ZPN_FOHH_HOP_LATENCY_BKT_8) {
    fohh_tlv->hop_latency_hist[8]++;
  } else {
    fohh_tlv->hop_latency_hist[9]++;
  }
}

static inline char *zpn_mconn_fohh_tlv_drop_stats(struct zpn_fohh_tlv *fohh_tlv, char *s, char *e)
{
  assert(fohh_tlv);
  // extra spacing around PRId64 is needed due to "error: invalid suffix on literal; C++11 requires a space between literal and identifier"
  s += sxprintf(s, e," \"udp_drop_bad_frame\":\"%" PRId64 "\", \"udp_drop_tx_buf_full\":\"%" PRId64 "\", \"icmp_drop\":\"%" PRId64 "\", \"icmp6_drop\":\"%" PRId64 "\"",
                fohh_tlv->rx_udp_data_dropped_frame_error, fohh_tlv->rx_udp_data_dropped_tx_buf_full, fohh_tlv->rx_icmp_error_data_dropped,
                fohh_tlv->rx_icmp6_error_data_dropped);
  return s;
}

static inline void zpn_mconn_fohh_tlv_set_states_seen(struct zpn_fohh_tlv *fohh_tlv, enum states_seen state)
{
  fohh_tlv->states_seen_bitmap |= 1 << state;
}

static inline uint64_t zpn_mconn_fohh_tlv_get_states_seen(struct zpn_fohh_tlv *fohh_tlv)
{
  return fohh_tlv->states_seen_bitmap;
}
#endif /* _ZPN_MCONN_FOHH_TLV_H_ */
