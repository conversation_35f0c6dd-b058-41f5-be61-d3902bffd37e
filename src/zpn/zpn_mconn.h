/*
 * zpn_mconn.h. Copyright (C) 2014 Zscaler Inc. All Rights Reserved.
 */

#ifndef _ZPN_MCONN_H_
#define _ZPN_MCONN_H_

#include <stdint.h>
#include <sys/queue.h>
#include <event2/buffer.h>
#include "fohh/fohh.h"
#include "zpn/zpn_mconn_udp_util.h"
#include "zpn/zpn_mconn_transmit_buffer.h"

#define ZPN_MCONN_MTU                FOHH_TLV_MTU_DATA
#define ZPN_MCONN_MTU_CTRL           FOHH_TLV_MTU_CTRL
#define ZPN_MCONN_TIMEOUT_BRK        30000000           /* 30 seconds (in usec) */


/*
 * ET-7577: We reduce the timeout for mtunnels on assistant, so that when it is timed out,
 * the corresponding mtunnel is still available on broker
 */
#define ZPN_MCONN_TIMEOUT_AST    20000000           /* 20 seconds (in usec) */

//#define ZPN_MCONN_MAX_CLIENT_TX_DATA       (10*1024*1024)	    /* We buffer max 10 MB */
#define ZPN_MCONN_MAX_CLIENT_TX_DATA       (64*1024)	            /* We buffer max 64 KB */

/* Set to 5 min. If we are blocked by remote this long, something wrong with the mconn, and should just kill it */
#define ZPN_MCONN_PAUSE_TIMEOUT  (1000000*300)
//#define ZPN_MCONN_PAUSE_TIMEOUT  1000000

#define ZPN_MCONN_TRACK_ARRAY_SIZE    10
#define ZPN_MCONN_TRACK_RX_DIFF_RX_DATA_HISTOGRAM_MAX 5
#define ZPN_MCONN_TRACK_RX_DIFF_RX_DATA_50000_US 50000
#define ZPN_MCONN_TRACK_RX_DIFF_RX_DATA_200000_US 200000
#define ZPN_MCONN_TRACK_RX_DIFF_RX_DATA_700000_US 700000
#define ZPN_MCONN_TRACK_RX_DIFF_RX_DATA_1500000_US 1500000

#define ZPN_MCONN_TRACK_TX_PEER_RX_DATA_HISTOGRAM_MAX 5
#define ZPN_MCONN_TRACK_TX_PEER_RX_DATA_20000_US 20000
#define ZPN_MCONN_TRACK_TX_PEER_RX_DATA_50000_US 50000
#define ZPN_MCONN_TRACK_TX_PEER_RX_DATA_100000_US 100000
#define ZPN_MCONN_TRACK_TX_PEER_RX_DATA_500000_US 500000

#define ZPN_MCONN_TRACK_QLEN_HISTOGRAM_MAX 4
// RX limit is calibrated based on TX
#define ZPN_MCONN_RX_QLEN_LOW (ZPN_MCONN_MAX_CLIENT_TX_DATA / 4)
#define ZPN_MCONN_RX_QLEN_MID (ZPN_MCONN_MAX_CLIENT_TX_DATA / 2)
#define ZPN_MCONN_RX_QLEN_HI  (ZPN_MCONN_MAX_CLIENT_TX_DATA)

#define ZPN_MCONN_TX_QLEN_LOW (ZPN_MCONN_MAX_CLIENT_TX_DATA / 2)
#define ZPN_MCONN_TX_QLEN_MID (ZPN_MCONN_MAX_CLIENT_TX_DATA)
#define ZPN_MCONN_TX_QLEN_HI  (ZPN_MCONN_MAX_CLIENT_TX_DATA * 5)

enum zpn_mconn_stats {
    window_update_no_peer_discard_data,
    window_update_no_peer_after_dequeue_successfully,
    window_update_no_peer_drop_because_fin_sent,
    mconn_terminate_no_peer,
    mconn_terminate_check_data_buffer_no_peer,
    mconn_not_clean_due_to_peer_still_data_buffered,
    window_update_sucesss_discard_data,
    max_zpn_mconn_stats,
};

enum zpn_mconn_drop_stats {
    drop_stats_none = 0,
    drop_stats_udp_frame_error,
    drop_stats_udp_tx_full,
    drop_stats_icmp_error,
    drop_stats_icmp6_error
};

/*
 * Internals of zpn_mconn code.
 *
 * An mconn is one 'half' of a zpn tunnel. It is a single stream. The
 * 'midpoint' of a tunnel is the connection between two
 * zpn_mconns. The 'ends' of the tunnel are the two 'clients' or
 * 'local_owners' of each mconn.
 *
 * A zpn_mconn is intended to be embedded within another data
 * structure:
 *
 * struct zpn_mconn_bufferevent {
 *     struct zpn_mconn mconn;
 *     struct bufferevent *bev;
 * };
 *
 * zpn_mconn's are designed to be associated with three different things:
 *
 * Optional: Global Owner. This is some owner structure that deals
 *   with the association of two mconn's as a whole. I think it will
 *   be used in basically all cases, but I'm not totally sure yet.
 *
 * Required: Local Owner. Often referred to as the 'client' of this
 *   mconn. This is the 'local' owner of an mconn. This is something
 *   like a socket, or a structure for associating an FOHH TLV
 *   connection with many mconn's.
 *
 * Required: Peer. This is the mconn that is a peer to this one, for
 *   transmitting/receiving data.
 *
 * The locking is fairly strict- read the various comments to see how
 * it is done.
 *
 * When an mconn is associated with an owner, either global or local,
 * there are a set of callbacks that the mconn must be able to make to
 * that owner in order to keep state consistent, and to be able to
 * perform its duties.
 *
 * The owner_key, owner_key_length, and owner_incarnation are used to
 * allow correct access within the owner in the event that there are a
 * significant number of mconns associated with a single owner.
 *
 */


/*
 * Owner Bind/Unbind callbacks.
 *
 * The owner lock will be held when these are called.
 *
 * Note that 'bind' sets owner_incarnation.
 *
 * These must return ZPN_RESULT_NO_ERROR on success. Any other result
 * indicates complete failure.
 *
 * mconn_base: The address of the mconn structure.
 *
 * mconn_self: The address of the overall structure to which the mconn
 * belongs. Often something like a struct mtunnel. But NOT the address
 * of an mconn_fohh_tlv, which is the same as mconn_base.
 *
 * owner: The thing to attach to. An FOHH structure, or an mtunnel
 * collection, etc.
 *
 * owner_key: A relatively reliable reference for the owner's
 * key. Usually stored in mconn_self somewhere.
 *
 * owner_incarnation: For protecting against erroneous owner re-use.
 *
 * dont_propagate: Indication that the callback should not propagate
 * the unbinding. This is done in the case where unbinding is coming
 * from the remote side in the first place...
 *
 * err is an error that may optionally exist, and may optionally be
 * propagated by the callback.
 */
typedef int (zpn_mconn_owner_bind_cb)(void *mconn_base,
                                      void *mconn_self,
                                      void *owner,
                                      void *owner_key,
                                      size_t owner_key_length,
                                      int64_t *owner_incarnation);
typedef int (zpn_mconn_owner_unbind_cb)(void *mconn_base,
                                        void *mconn_self,
                                        void *owner,
                                        void *owner_key,
                                        size_t owner_key_length,
                                        int64_t owner_incarnation,
                                        int drop_buffered_data,
                                        int dont_propagate,
                                        const char *err);


/*
 * Owner Lock/Unlock callbacks.
 */
typedef void (zpn_mconn_owner_lock_cb)(void *mconn_base,
                                       void *mconn_self,
                                       void *owner,
                                       void *owner_key,
                                       size_t owner_key_length);
typedef void (zpn_mconn_owner_unlock_cb)(void *mconn_base,
                                         void *mconn_self,
                                         void *owner,
                                         void *owner_key,
                                         size_t owner_key_length);

typedef int (zpn_mconn_owner_try_lock_cb)(void *mconn_base,
                                          void *mconn_self,
                                          void *owner,
                                          void *owner_key,
                                          size_t owner_key_length);

/*
 * Owner IP protocol callbacks.
 */
typedef int (zpn_mconn_owner_ip_proto)(void *mconn_base,
                                       void *mconn_self,
                                       void *owner,
                                       void *owner_key,
                                       size_t owner_key_length);

/*
 * Owner double encrypt callbacks.
 */
typedef int (zpn_mconn_owner_double_encrypt)(void *mconn_base,
                                             void *mconn_self,
                                             void *owner,
                                             void *owner_key,
                                             size_t owner_key_length);


/*
 * Owner outer mconn callbacks.
 */
typedef struct zpn_mconn *(zpn_mconn_owner_outer_mconn)(void *mconn_base,
                                                        void *mconn_self,
                                                        void *owner,
                                                        void *owner_key,
                                                        size_t owner_key_length);

/*
 * Owner Transmit callback. Can be called in the context of an
 * arbitrary thread.
 *
 * Data should be pulled out of buf and transmitted.
 *
 * Should return:
 *
 * ZPN_RESULT_NO_ERROR: Success- data transmitted or queued.
 *
 * ZPN_RESULT_WOULD_BLOCK: Cannot queue data without blocking. (Will
 *   cause us to receive an unblock call in the future)
 *
 * Mconn internal lock is held when this is called.
 */
typedef int (zpn_mconn_transmit_cb)(void *mconn_base,
                                    void *mconn_self,
                                    void *owner,
                                    void *owner_key,
                                    size_t owner_key_length,
                                    int64_t owner_incarnation,
                                    int fohh_thread_id,
                                    struct evbuffer *buf,
                                    size_t buf_len);


/*
 * Owner pause callback. Can be called in the context of an arbitrary
 * thread.
 *
 * Can return WOULD_BLOCK if the pause indication itself cannot
 * currently be sent.
 *
 * Mconn internal lock is held when this is called.
 */
typedef int (zpn_mconn_pause_cb)(void *mconn_base,
                                 void *mconn_self,
                                 void *owner,
                                 void *owner_key,
                                 size_t owner_key_length,
                                 int64_t owner_incarnation,
                                 int fohh_thread_id);


/*
 * Owner resume callback. Can be called in the context of an arbitrary
 * thread.
 *
 * Can return WOULD_BLOCK if the transmission of the pause indication
 * cannot currently be sent
 *
 * Mconn internal lock is held when this is called.
 */
typedef int (zpn_mconn_resume_cb)(void *mconn_base,
                                  void *mconn_self,
                                  void *owner,
                                  void *owner_key,
                                  size_t owner_key_length,
                                  int64_t owner_incarnation,
                                  int fohh_thread_id);

/*
 * Owner disable_read callback. Can be called in the context of an arbitrary
 * thread.
 *
 * Can return WOULD_BLOCK if the pause indication itself cannot
 * currently be sent.
 *
 * Mconn internal lock is held when this is called.
 */
typedef int (zpn_mconn_disable_read_cb)(void *mconn_base,
                                        void *mconn_self,
                                        void *owner,
                                        void *owner_key,
                                        size_t owner_key_length,
                                        int64_t owner_incarnation,
                                        int fohh_thread_id);


/*
 * Owner enable_read callback. Can be called in the context of an arbitrary
 * thread.
 *
 * Can return WOULD_BLOCK if the transmission of the pause indication
 * cannot currently be sent
 *
 * Mconn internal lock is held when this is called.
 */
typedef int (zpn_mconn_enable_read_cb)(void *mconn_base,
                                       void *mconn_self,
                                       void *owner,
                                       void *owner_key,
                                       size_t owner_key_length,
                                       int64_t owner_incarnation,
                                       int fohh_thread_id);

/*
 * Owner terminate callback. Can be called in the context of an arbitrary
 * thread.
 *
 * This is only for global owner, and is intended to terminate
 * the mtunnel to which this mconn belongs
 *
 * Mconn internal lock is held when this is called.
 */
typedef void (zpn_mconn_terminate_cb)(void *mconn_base,
                                      void *mconn_self,
                                      void *owner,
                                      void *owner_key,
                                      size_t owner_key_length,
                                      char *error);

typedef int (zpn_mconn_forward_tunnel_end_cb)(void *mconn_base,
                                              void *mconn_self,
                                              void *owner,
                                              void *owner_key,
                                              size_t owner_key_length,
                                              int64_t owner_incarnation,
                                              const char *err,
                                              int32_t drop_data);

/*
 * Owner Window Update callback. Can be called in the context of an
 * arbitrary thread.
 *
 * Mconn internal lock is held when this is called.
 */
typedef void (zpn_mconn_window_update_cb)(void *mconn_base,
                                          void *mconn_self,
                                          void *owner,
                                          void *owner_key,
                                          size_t owner_key_length,
                                          int64_t owner_incarnation,
                                          int fohh_thread_id,
                                          int tx_len,
                                          int batch_win_upd);

typedef void (zpn_mconn_stats_update_cb)(void *mconn_base,
                                         void *mconn_self,
                                         void *owner,
                                         void *owner_key,
                                         size_t owner_key_length,
                                         int64_t owner_incarnation,
                                         int fohh_thread_id,
                                         enum zpn_mconn_stats stats_name);

typedef int64_t (zpn_mconn_owner_incarnation_cb)(void *mconn_base,
                                                 void *mconn_self,
                                                 void *global_owner,
                                                 void *global_owner_key,
                                                 size_t global_owner_key_length);

typedef int (zpn_mconn_owner_validate_incarnation_cb)(void *mconn_base,
                                                      void *mconn_self,
                                                      void *global_owner,
                                                      void *global_owner_key,
                                                      size_t global_owner_key_length,
                                                      int64_t original_incarnation);

/*
 * Owner mtunnel ID callbacks.
 */
typedef char *(zpn_mconn_owner_id)(void *mconn_base,
                                   void *mconn_self,
                                   void *owner,
                                   void *owner_key,
                                   size_t owner_key_length);

/*
 * Owner get peer callbacks.
 */
typedef struct zpn_mconn *(zpn_mconn_owner_get_peer)(void *mconn_base,
                                                     void *mconn_self,
                                                     void *owner,
                                                     void *owner_key,
                                                     size_t owner_key_length);

typedef int64_t (zpn_mconn_owner_customer_gid)(void *mconn_base,
                                               void *mconn_self,
                                               void *owner,
                                               void *owner_key,
                                               size_t owner_key_length);

/*
 * Callbacks for global owner.
 */
struct zpn_mconn_global_owner_calls {
    zpn_mconn_owner_bind_cb *bind;
    zpn_mconn_owner_unbind_cb *unbind;
    zpn_mconn_owner_lock_cb *lock;
    zpn_mconn_owner_unlock_cb *unlock;
    zpn_mconn_terminate_cb *terminate;
    zpn_mconn_owner_ip_proto *ip_proto;
    zpn_mconn_owner_double_encrypt *double_encrypt;
    zpn_mconn_owner_outer_mconn *outer_mconn;
    zpn_mconn_owner_incarnation_cb *incarnation;
    zpn_mconn_owner_validate_incarnation_cb *validate_incarnation;
    zpn_mconn_owner_id *id;
    zpn_mconn_owner_get_peer *get_peer;
    zpn_mconn_owner_customer_gid *get_customer_gid;
};

/*
 * Callbacks for local owner.
 */
struct zpn_mconn_local_owner_calls {
    zpn_mconn_owner_bind_cb *bind;
    zpn_mconn_owner_unbind_cb *unbind;
    zpn_mconn_owner_lock_cb *lock;
    zpn_mconn_owner_unlock_cb *unlock;
    zpn_mconn_transmit_cb *transmit;
    zpn_mconn_pause_cb *pause;
    zpn_mconn_resume_cb *resume;
    zpn_mconn_forward_tunnel_end_cb *forward_tunnel_end;
    zpn_mconn_window_update_cb *window_update;
    zpn_mconn_stats_update_cb *stats_update;
    zpn_mconn_disable_read_cb *disable_read;
    zpn_mconn_enable_read_cb *enable_read;
};


/*
 * mconn's are destroyed only slowly so that stale (very short term)
 * pointers are not invalid.
 *
 * Mconns are freed on a per-thread free queue, managed by
 * zpn_mconn.c. Mconns are assumed to have zero allocated state once
 * they have been unbound from their peer and local/global
 * state. Mconns are freed by freeing 'self', which is the structure
 * containing the mconn. (Yes, this means you cannot put two mconn's
 * in the same structure- they have to be pointers to two mconns...)
 *
 * This is handled by a simple incarnation check on the asynchronous
 * callbacks.
 *
 * Incarnation is incremented for a zpn_mconn the instant it is freed.
 *
 * zpn_mconn can only be freed by the thread it belongs to.
 */
enum zpn_mconn_type {
    mconn_unknown_type,
    mconn_bufferevent_s,
    mconn_bufferevent_c,
    mconn_bufferevent_udp_s,
    mconn_bufferevent_udp_c,
    mconn_bufferevent_tun_s,
    mconn_bufferevent_tun_c,
    mconn_icmp_s,
    mconn_icmp_c,
    mconn_fohh_tlv_s,
    mconn_fohh_tlv_c,
    mconn_zrdt_tlv_s,
    mconn_zrdt_tlv_c,
    mconn_buffereventpair_s,
    mconn_buffereventpair_c,
};

typedef void (zpn_pipeline_setup_f)(struct zpn_mconn*, void* cookie);

struct zpn_mconn {
    /* Incarnation, for incarnation checks... */
    int64_t incarnation;

    /* Type of mconn */
    enum zpn_mconn_type type;

    /* Reference to the structure of which we are a member-
     * zpn_mconn's are often embedded in a more descriptive
     * structure. */
    void *self;

    /* Our peer, if we're connected to them... This lock is ONLY for
     * this peer state and paused data */
    struct zpn_mconn *peer;
    int64_t peer_incarnation;
    /* Data we have buffered that cannot be sent yet, because the our
     * client indicated that it would block. (or our client isn't set
     * up yet) When empty, there is no evbuffer. Synchronized using
     * peer_lock. */
    struct zpn_mconn_transmit_buffer* transmit_buffer;

    zpn_pipeline_setup_f* setup_pipeline_cb;
    void* pipeline_cookie;

    /* Data we have buffered that cannot be sent to peer yet. */
    struct evbuffer *client_rx_data;

    /* If we have framed data, then we need to track partial data
     * going into client transmit buffer. */
    struct udp_packet_state udp_state;

    struct mconn_icmp_state {
        void *cookie;
        struct in_addr a_ip;
        struct in_addr s_ip;
        uint16_t id;
        uint16_t seq;
    } icmp_state;

    /* Our global owner- not required, but often there. Access to the
     * owner fields are locked by configuration within the owner
     * itself. Existence of global_owner is indication of validity. */
    void *global_owner;
    void *global_owner_key;
    size_t global_owner_key_length;
    int64_t global_owner_incarnation;
    const struct zpn_mconn_global_owner_calls *global_owner_calls;

    /* Our local owner- required. Access to the local owner fields is
     * by configuration within the owner itself. Existence of
     * local_owner is indication of validity. */
    void *local_owner;
    void *local_owner_key;
    size_t local_owner_key_length;
    int64_t local_owner_incarnation;
    const struct zpn_mconn_local_owner_calls *local_owner_calls;
    /* It is possible we're draining data to our client, and we need
     * to disconnect the client once that data has been sent. If that
     * is the case, this is set. */
    int client_needs_to_disconnect_local_owner;

    int client_needs_to_forward;

    /* Thread ID for this mconn. This is the thread ID which is
     * responsible for all data RX/TX on this mconn. It is usually the
     * thread_id associated with the owner. zpn_mconn makes all
     * tx/pause/unpause calls occur on this thread_id. */
    int fohh_thread_id;

    /* Whether or our client is currently kept from sending us more
     * data. (Doesn't guarantee no more data will arrive from the
     * client, as sometimes pausing is quite asynchronous)
     * (i.e. client RX is off) */
    int client_paused;

    /* It is possible that we could not send an indication to the
     * client that they need to be paused, unpaused, or
     * disconnected. In those cases, we set an indication so that the
     * timer thread can perform those actions as necessary. */
    int client_needs_to_pause;
    int client_needs_to_resume;

    int to_client_paused;	/* client won't accept data from us if set */
    int64_t pause_start_us; /* time when pause started */

    int32_t to_client_paused_count;	                /* number of pause sent */
    int32_t from_client_paused_count;               /* number of pause received */
    int32_t to_client_resume_count;                 /* number of resume sent */
    int32_t from_client_resume_count;               /* number of resume received */
    int32_t to_client_pause_timed_out_count;        /* number of timed out pauses */

    /* to client pause time stats */
    int64_t to_client_pause_time_max_us;            /* maximum pause time in us */
    int64_t to_client_pause_time_max_epoch_us;      /* epoch time when max pause time happened */
    int64_t to_client_pause_time_total_us;          /* cumulative pause time in us */
    /* from client pause time stats */
    int64_t from_client_pause_time_max_us;          /* maximum pause time in us */
    int64_t from_client_pause_time_max_epoch_us;    /* epoch time when max pause time happened */
    int64_t from_client_pause_time_total_us;        /* cumulative pause time in us */

    int64_t bytes_to_client;
    int64_t bytes_to_peer;
    int64_t bytes_to_peer_attempt;
    int64_t bytes_from_peer;
    int64_t bytes_dropped_udp;
    int64_t packets_malformed_udp;
    int64_t icmp_time_exceeded_drops;
    int64_t icmp_pkt_frag_drops;
    int64_t icmp_malformed_pkt_drops;
    int64_t icmp_zero_len_pkt_drops;
    int64_t icmp_internal_err_drops;
    int64_t icmp_access_err_drops;
    int64_t icmp_timeout_failure_drops;
    int64_t icmp_rate_limit_exceeded_err_drops;
    int64_t icmp_tx_packets;
    int64_t icmp_rx_packets;
    int64_t icmpv6_time_exceeded_drops;
    int64_t icmpv6_malformed_pkt_drops;
    int64_t icmpv6_internal_err_drops;
    int64_t icmpv6_access_err_drops;
    int64_t icmpv6_timeout_failure_drops;
    int64_t icmpv6_rate_limit_exceeded_err_drops;
    int64_t icmpv6_tx_packets;
    int64_t icmpv6_rx_packets;

    int64_t rx_data_us_b;		/* First time we got data */
    int64_t tx_data_us_b;		/* Fisrt time we sent data */
    int64_t rx_data_us;		        /* Last time we got data */
    int64_t tx_data_us;		        /* Last time we sent data */
    int64_t from_peer_data_us_b;        /* First time we got data from peer */
    int64_t from_peer_data_us;          /* Last time we got data from peer */
    int64_t rx_data_us_tx_cnxt;     /* rx_data_us in TX thread */

    int fin_rcvd;
    int fin_sent;
    int64_t fin_rcvd_us;
    int64_t fin_sent_us;
    int fin_sent_drop_data;             /* 1 if we already sent out FIN with drop_data = 1 */
    int fin_refl;
    int64_t config_fin_expire_us;       /* configuration for fin expire time in us, zero otherwise */

    int drop_tx;                        /* set to drop_data of received mtunnel_end message */

    int track;
    int tx_rx_diff[ZPN_MCONN_TRACK_ARRAY_SIZE];
    int tx_rx_total;
    int tx_rx_index;

    int rx_tx_diff[ZPN_MCONN_TRACK_ARRAY_SIZE];
    int rx_tx_total;
    int rx_tx_index;

    int dropped_untransmitted_bytes;

    unsigned drop_udp_framed_data:1;
    unsigned is_zapp_client:1;
    unsigned is_zapp_partner_client:1;
    unsigned icmp_access_type:2;

    int rx_fin_pending;
    int terminated;
    unsigned is_machine_tunnel_client:1;

    //Special forwarding instructions for inspected tunnels
    int hold_forward_mtunnel_end;
    int terminate_at_connector;

    unsigned is_mconn_track_perf_stats_enabled:1;  /* is track performance enabled */
    /* histogram: number of packets on ingress
     * with inter packet delays falling into buckets of: 20ms, 50ms, 100ms, 500ms, 500ms plus */
    int rx_diff_rx_data_hist[ZPN_MCONN_TRACK_RX_DIFF_RX_DATA_HISTOGRAM_MAX];
    int64_t rx_diff_rx_data_us;     /* time stamp starting measurements of rx_diff_rx_data */

    int tx_peer_rx_data_max_us;     /* max time from ingress (rx) mconn to egress (tx) mconn */
    int64_t tx_peer_rx_data_max_epoch_us;     /* epoch of max time from ingress (rx) mconn to egress (tx) mconn */
    int tx_peer_rx_data_max_cnt;    /* number of packets received from ingress (rx) mconn to egress (tx) mconn during tx_peer_rx_data_max_us  */
    int tx_peer_rx_data_cnt;        /* current number of packets received from ingress (rx) mconn to egress (tx) mconn during curr tx_peer_rx_data_us */
    /* histogram : number of packets from ingress mconn to egress mconn
     * with delays falling into buckets of: 20ms, 50ms, 100ms, 500ms, 500ms plus */
    int tx_peer_rx_data_hist[ZPN_MCONN_TRACK_TX_PEER_RX_DATA_HISTOGRAM_MAX];
    int64_t tx_peer_rx_data_us;     /* time stamp starting measurements of tx_peer_rx_data_max_us */
    int64_t tx_peer_rx_data_bytes_to_peer_attempt;   /* bytes_to_peer_attempt at tx_peer_rx_data_us */

    int tx_data_unblock_max_us;     /* max time in us taken to schedule packet waiting at egress mconn to be sent */
    int tx_data_unblock_max_cnt;    /* number of attempts to schedule packets at egress mconn during tx_data_unblock_max_us */
    int64_t tx_data_unblock_tot_us; /* total time in us taken to schedule packets waiting at egress mconn to be sent */
    int tx_data_unblock_cnt;        /* curr number of attempts to schedule packets at egress mconn */
    int64_t tx_data_unblock_us;     /* time stamp starting measurements for curr tx_data_unblock_max_us */

    int tx_peer_rx_data_max_unblock_cnt_curr;           /* total number of attempts to schedule packets at egress mconn during tx_peer_rx_data_max_us */
    int tx_peer_rx_data_max_unblock_cnt;     /* max from tx_peer_rx_data_max_unblock_cnt_curr */

    unsigned fohh_connection_disable_read_config_flag:1;  /* is bufferevent_disable config enabled */
    uint64_t client_rx_data_len[ZPN_MCONN_TRACK_QLEN_HISTOGRAM_MAX];
    uint64_t tx_buf_len[ZPN_MCONN_TRACK_QLEN_HISTOGRAM_MAX];

    /* disable read client outter mconn flag */
    int disable_read_client_flag;
    /* disable read client count stats per mtunnel*/
    int32_t enable_read_client_count;
    int32_t disable_read_client_count;

    /* disable read client time stats per mtunnel*/
    int64_t disable_read_client_time_max_us;            /* maximum disabled read time in us */
    int64_t disable_read_client_time_max_epoch_us;      /* epoch time when max disabled read time happened */
    int64_t disable_read_client_time_total_us;          /* cumulative disabled read time in us */

    /*  disable read client config parameters */
    int64_t disable_read_client_tx_buff_high;
    int64_t disable_read_client_tx_buff_low;
    int64_t disable_read_client_tx_buff_high_allow_time_max_us;

    /* disable read client starting time stamp per mtunnel*/
    int64_t disable_read_client_tx_buff_high_allow_time_start_us;

    /* disable read client started flag per mtunnel */
    int disable_read_client_tx_buff_high_allow_time_max_flag;

    unsigned allocator_libevent_out_queue_is_enabled:1;  /* is allocator libevent out queue cfg override enabled */
    int64_t udp_timeout_s;
    int64_t udp_fast_timeout_s;
};


/*
 * Initialize an mconn. Mconn is assumed to be cleared to 0 before
 * call.
 *
 * self is very often identical to mconn... (mconn is usually the
 * first structure within some parent structure, as a poor-man's
 * object-orientedness)
 */
int zpn_mconn_init(struct zpn_mconn *mconn, void *self, enum zpn_mconn_type type);


/*
 * Add a global owner to this mconn.
 *
 * LOCKING: If is_owner_lock_held is set, then the owner's lock is
 *   considered held. Otherwise, the mconn will grab the owner's lock
 *   via CB. (which is available because of calls)
 */
int zpn_mconn_add_global_owner(struct zpn_mconn *mconn,
                               int is_owner_lock_held,
                               void *global_owner,
                               void *global_owner_key,
                               size_t global_owner_key_length,
                               const struct zpn_mconn_global_owner_calls *calls);

/*
 * Add a local owner to this mconn.
 *
 * LOCKING: If is_owner_lock_held is set, then the owner's lock is
 *   considered held. Otherwise, the mconn will grab the owner's lock
 *   via CB. (which is available because of calls)
 */
int zpn_mconn_add_local_owner(struct zpn_mconn *mconn,
                              int is_owner_lock_held,
                              void *local_owner,
                              void *local_owner_key,
                              size_t local_owner_key_length,
                              const struct zpn_mconn_local_owner_calls *calls);

int zpn_mconn_remove_local_owner(struct zpn_mconn *mconn,
                                 int is_owner_lock_held,
                                 int drop_buffered_data,
                                 int dont_propagate_unbind,
                                 const char *err);

/*
 * Associate two mconn's.
 *
 * LOCKING: None required. (Should probably happen only at
 *   initialization time...)
 */
int zpn_mconn_connect_peer(struct zpn_mconn *mconn, struct zpn_mconn *peer_mcon);

/*
 * This routine is simply a combination of remove_local_owner,
 * remove_global_owner, and disconnect_peer.
 *
 * if drop_buffered_data is set, then buffered data will be dropped anywhere.
 *
 * if mconn_initiated is set, then the mconn specified will not have
 * an end-message sent. (since it probably received one...)
 *
 * reason, if set, is a reason that may (optionally) be sent as a part of delinking...
 *
 * uncounted_dropped_bytes -
 * this number indicates if mconn dequeued some buffered data (this buffered data is coming from peer, and mconn has to dequeue to its client)
 * and mconn is not able to update peer's connection stats on these dequeued bytes.
 * peer connection needs update because it needs such acknowledgement from mconn so it knows mconn has dequeued the bytes successfully,
 * so that peer can continue the flow control window
 */
int zpn_mconn_terminate(struct zpn_mconn *mconn,
                        int drop_buffered_data,
                        int mconn_initiated,
                        const char *reason,
                        int64_t *uncounted_dropped_bytes);

/*
 * Unblock a client- indicate to anyone sending data to this client
 * that the client can take more data again.
 *
 * If the transmit, resume, or pause callbacks return
 * ZPN_RESULT_WOULD_BLOCK, then at some point later this call must be
 * made letting the system know to continue data transmission.
 */
int zpn_mconn_unblock_client(struct zpn_mconn *mconn);


/*
 * Get the local owner/incarnation of an mconn.
 *
 * incarnation is passed out.
 */
void *zpn_mconn_get_local_owner(struct zpn_mconn *mconn, int64_t *incarnation);

/*
 * This routine handles reception of data received by the client.
 * (That presumably should be sent to a peer...)
 *
 * Returns:
 *
 * ZPN_RESULT_SUCCESS: The data has been transmitted. (In socket
 *   buffers, whatever)
 *
 * ZPN_RESULT_ASYNCHRONOUS: The data has been buffered, but the
 *   transmitter better stop transmitting to us, lest there be crazy
 *   buffer bloat.
 *
 * ZPN_RESULT_*: Some error has occurred, and processing more data
 *   will probably never be successful.
 *
 * LOCKING: None required.
 */
int zpn_client_process_rx_data(struct zpn_mconn *mconn, struct evbuffer *buf, size_t buf_len,
                               int64_t *dropped_bytes, enum zpn_mconn_drop_stats *drop_stats_type);

int zpn_mconn_send_data_to_client(struct zpn_mconn *mconn, struct evbuffer *buf, size_t buf_len, int need_to_lock, int64_t *droplen);

/*
 * This routine allow client to drain data buffered on mconn->transmit_buffer. It basically
 * calls zpn_mconn_send_data_to_client().
 *
 * Returns: whatever the return value from zpn_mconn_send_data_to_client() call
 */
int zpn_client_drain_tx_data(struct zpn_mconn *mconn);

/*
 * This function checks to make sure zpn_mconn object is clean for reuse. If OK to clean, make sure all the held
 * objects are also freed
 */
int zpn_mconn_clean(struct zpn_mconn *mconn);

void zpn_mconn_flush_data(struct zpn_mconn *mconn);

/*
 * Pause data arriving from the client.
 *
 * LOCKING: None required.
 */
int zpn_mconn_pause_client(struct zpn_mconn *mconn, int needs_to_lock);

/*
 * Resume data arriving from the client.
 *
 * This routine should propagate ASYNCHRONOUS if it receives such on
 * attempted retransmission.
 *
 * LOCKING: None required.
 */
int zpn_mconn_resume_client(struct zpn_mconn *mconn, int needs_to_lock);

/*
 * Pause data arriving from the client.
 *
 * LOCKING: None required.
 */
int zpn_mconn_disable_read_client(struct zpn_mconn *mconn, int needs_to_lock);

/*
 * Resume data arriving from the client.
 *
 * This routine should propagate ASYNCHRONOUS if it receives such on
 * attempted retransmission.
 *
 * LOCKING: None required.
 */
int zpn_mconn_enable_read_client(struct zpn_mconn *mconn, int needs_to_lock);

/*
 * Update window size for client.
 *
 * This routine should propagate ASYNCHRONOUS if it receives such on
 * attempted retransmission.
 *
 * LOCKING: None required.
 */
int zpn_mconn_client_window_update(struct zpn_mconn *mconn, int needs_to_lock, int total_xmt, int batch_win_upd);

/*
 * This function sets the fohh_thread_id of an mconn
 */
void zpn_mconn_set_fohh_thread_id(struct zpn_mconn *mconn, int fohh_thread_id);
int zpn_mconn_get_fohh_thread_id(struct zpn_mconn *mconn, int *fohh_thread_id);

struct terminate_cookie {
    char *mtunnel_id;
    char *error;
};

void zpn_mconn_internal_display(struct zpn_mconn *mconn);

void zpn_mconn_discard_client_data(struct zpn_mconn *mconn, int64_t *uncounted_dropped_bytes);

void zpn_mconn_to_client_pause(struct zpn_mconn *mconn);
void zpn_mconn_to_client_resume(struct zpn_mconn *mconn);

int zpn_mconn_forward_mtunnel_end(struct zpn_mconn *mconn, const char *err, int32_t drop_data);

int zpn_mconn_done(struct zpn_mconn *mconn);
int zpn_mconn_fin_expired(struct zpn_mconn *mconn);

int zpn_mconn_track(struct zpn_mconn *mconn);
void zpn_mconn_track_perf_ingress(struct zpn_mconn *mconn);
void zpn_mconn_track_perf_egress(struct zpn_mconn *mconn);

char *zpn_mconn_type_str(enum zpn_mconn_type type);

void zpn_mconn_setup_pipeline(struct zpn_mconn *mconn, char* pipeline_name);
void zpn_mconn_stats_update(struct zpn_mconn *mconn, enum zpn_mconn_stats stats_name);
struct zpn_mconn *global_get_peer_no_op(void *mconn_base,
                                        void *mconn_self,
                                        void *global_owner,
                                        void *global_owner_key,
                                        size_t global_owner_key_length);

int64_t global_get_customer_gid_no_op(void *mconn_base,
                                      void *mconn_self,
                                      void *global_owner,
                                      void *global_owner_key,
                                      size_t global_owner_key_length);

static inline void zpn_mconn_client_rx_data_len_stats(struct zpn_mconn *mconn, uint64_t qlen)
{
  assert(mconn);
  if (qlen < ZPN_MCONN_RX_QLEN_LOW) {
    mconn->client_rx_data_len[0]++;
  } else if (qlen < ZPN_MCONN_RX_QLEN_MID) {
    mconn->client_rx_data_len[1]++;
  } else if (qlen < ZPN_MCONN_RX_QLEN_HI) {
    mconn->client_rx_data_len[2]++;
  } else {
    mconn->client_rx_data_len[3]++;
  }
}

static inline void zpn_mconn_tx_buf_len_stats(struct zpn_mconn *mconn, uint64_t qlen)
{
  assert(mconn);
  if (qlen < ZPN_MCONN_TX_QLEN_LOW) {
    mconn->tx_buf_len[0]++;
  } else if (qlen < ZPN_MCONN_TX_QLEN_MID) {
    mconn->tx_buf_len[1]++;
  } else if (qlen < ZPN_MCONN_TX_QLEN_HI) {
    mconn->tx_buf_len[2]++;
  } else {
    mconn->tx_buf_len[3]++;
  }
}
#endif /* _ZPN_MCONN_H_ */
