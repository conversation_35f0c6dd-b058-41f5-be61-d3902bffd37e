/*
 * zpn_mconn_icmp_util.c. Copyright (C) 2020 Zscaler Inc. All Rights Reserved.
 */

#include <sys/types.h>    /* XXX temporary hack to get u_ types */
#include <arpa/inet.h>
#include <netinet/in.h>
#include <netinet/ip.h>
#include <netinet/ip_icmp.h>
#include <netinet/ip6.h>
#include <netinet/icmp6.h>

#include "zpn/zpn_rpc.h"
#include "ztlv/zpn_tlv.h"
#include "zpn/zpn_mconn.h"
#include "fohh/fohh_private.h"
#include "zpn/zpn_mconn_icmp_util.h"
#include "zpn/zpn_icmp_rate_limit.h"
#include "zhealth/zhealth_route_lookup.h"

#ifndef IPV6_VERSION
#define IPV6_VERSION 0x60
#endif

#ifndef IPV6_VERSION_MASK
#define IPV6_VERSION_MASK 0xf0
#endif

#define MIN_IPV6_MTU 1280

static inline unsigned short csum(void* vdata, size_t length) {
 // Cast the data pointer to one that can be indexed.
 char* data=(char*)vdata;
 size_t i;

 // Initialise the accumulator.
 uint32_t acc=0xffff;

 // Handle complete 16-bit blocks.
 for (i=0;i+1<length;i+=2) {
     uint16_t word;
     memcpy(&word,data+i,2);
     acc+=ntohs(word);
     if (acc>0xffff) {
         acc-=0xffff;
     }
 }

 // Handle any partial block at the end of the data.
 if (length&1) {
     uint16_t word=0;
     memcpy(&word,data+length-1,1);
     acc+=ntohs(word);
     if (acc>0xffff) {
         acc-=0xffff;
     }
 }

 // Return the checksum in network byte order.
 return htons(~acc);
}

struct evbuffer *zpn_mconn_icmp_packetize(uint8_t *icmp_buf, size_t icmp_buf_len,
                                          int icmp_type, int icmp_code, uint16_t req_id,
                                          uint16_t req_seq, struct in_addr ip_src, uint16_t ip_id)
{
    struct ip *iph = (struct ip *)(icmp_buf);
    int iph_len = iph->ip_hl << 2;
    int ip_tlen = ntohs(iph->ip_len);

    if (icmp_type == ICMP_ECHOREPLY) {
        iph->ip_dst = ip_src;

        struct icmp *icmph = (struct icmp *)(icmp_buf + iph_len);
        icmph->icmp_hun.ih_idseq.icd_id = req_id;
        icmph->icmp_hun.ih_idseq.icd_seq = req_seq;
        icmph->icmp_cksum = 0;
        icmph->icmp_cksum = csum((unsigned short *)icmph,
                                 (ip_tlen - iph_len));

        iph->ip_sum = 0;
        iph->ip_sum = csum((unsigned short *)iph, iph_len);
    } else if ((icmp_type == ICMP_TIMXCEED) || (icmp_type == ICMP_UNREACH)) {
        iph->ip_dst = ip_src;

        struct ip *iph_inner = (struct ip *) (icmp_buf + iph_len + ICMP_HDR_LEN);
        int iph_inner_len = iph_inner->ip_hl << 2;

        struct icmp *icmph_inner = (struct icmp *)(icmp_buf + iph_len +
                                                   ICMP_HDR_LEN + iph_inner_len);
        icmph_inner->icmp_hun.ih_idseq.icd_id = req_id;
        icmph_inner->icmp_hun.ih_idseq.icd_seq = req_seq;

        icmph_inner->icmp_cksum = 0;
        icmph_inner->icmp_cksum = csum((unsigned short *)icmph_inner,
                                       ((ip_tlen - iph_len -
                                         ICMP_HDR_LEN - iph_inner_len)));

        iph_inner->ip_src = ip_src;
        iph_inner->ip_id = ip_id;

        iph_inner->ip_sum = 0;
        iph_inner->ip_sum = csum((unsigned short *)iph_inner, (iph_inner_len));

        struct icmp *icmph = (struct icmp *)(icmp_buf + iph_len);
        icmph->icmp_cksum = 0;
        icmph->icmp_cksum = csum((unsigned short *)icmph, ((ip_tlen - iph_len)));

        iph->ip_sum = 0;
        iph->ip_sum = csum((unsigned short *)iph, (iph_len));
    }

    struct evbuffer *icmp_pkt_buf = evbuffer_new();
    if (!icmp_pkt_buf) {
        return NULL;
    }

    evbuffer_add(icmp_pkt_buf, icmp_buf, ip_tlen);

    return icmp_pkt_buf;
}

struct evbuffer *zpn_mconn_icmpv6_packetize(uint8_t *icmp_buf, size_t icmp_buf_len,
                                            int icmp_type, int icmp_code, uint16_t req_id,
                                            uint16_t req_seq, struct in6_addr ip6_src)
{
    struct ip6_hdr *ip6h = (struct ip6_hdr *)(icmp_buf);
    int iph_len = sizeof(struct ip6_hdr); // IP header
    int ip_tlen = ntohs(ip6h->ip6_plen) + iph_len; // Packet total length
    int icmp_tlen = ip_tlen - iph_len;

    if (icmp_type == ICMP6_ECHO_REPLY) {
        ip6h->ip6_dst = ip6_src;
        struct icmp6_hdr *icmpv6_hdr = (struct icmp6_hdr *)(icmp_buf + iph_len);
        icmpv6_hdr->icmp6_id = req_id;
        icmpv6_hdr->icmp6_seq = req_seq;
        icmpv6_hdr->icmp6_cksum = csum((unsigned short *)icmpv6_hdr, icmp_tlen);
    } else if ((icmp_type == ICMP6_TIME_EXCEEDED) ||
               (icmp_type == ICMP6_PACKET_TOO_BIG) ||
               (icmp_type == ICMP6_DST_UNREACH) ||
               (icmp_type == ICMP6_PARAM_PROB)) {
        ip6h->ip6_dst = ip6_src;
        struct ip6_hdr *iph_inner = (struct ip6_hdr *)(icmp_buf + iph_len + ICMP_HDR_LEN);
        int iph_inner_len = iph_len;
        struct icmp6_hdr *icmph_inner = (struct icmp6_hdr *)(icmp_buf + iph_len + ICMP_HDR_LEN + iph_inner_len);
        icmph_inner->icmp6_id = req_id;
        icmph_inner->icmp6_seq = req_seq;
        icmph_inner->icmp6_cksum = csum((unsigned short *) icmph_inner, (ip_tlen - iph_len - ICMP_HDR_LEN - iph_inner_len));

        iph_inner->ip6_src = ip6_src;
        iph_inner->ip6_flow = 0;
        iph_inner->ip6_vfc = (iph_inner->ip6_vfc & ~IPV6_VERSION_MASK) | (IPV6_VERSION & IPV6_VERSION_MASK);

        struct icmp6_hdr *icmpv6_hdr = (struct icmp6_hdr *)(icmp_buf + iph_len);
        icmpv6_hdr->icmp6_cksum = csum((unsigned short *)icmpv6_hdr, icmp_tlen);
    }

    struct evbuffer *icmp_pkt_buf = evbuffer_new();
    if (!icmp_pkt_buf) {
        return NULL;
    }

    evbuffer_add(icmp_pkt_buf, icmp_buf, ip_tlen);

    return icmp_pkt_buf;
}

static int zpn_mconn_icmp_build_time_exceed_pkt(struct zpn_mconn *mconn, struct evbuffer *icmp_buf, uint8_t *icmp_linear_buf)
{
    struct evbuffer *tmp_icmp_pkt_buf = evbuffer_new();
    if (!tmp_icmp_pkt_buf) {
        return ZPN_MCONN_ICMP_PKT_INTERNAL_ERR;
    }

    /* Source IP Header */
    struct ip *iph = (struct ip *)(icmp_linear_buf);
    int iph_len = iph->ip_hl << 2;

    uint8_t *tmp_pkt_buf = ZPN_CALLOC(sizeof(struct ip) + ICMP_HDR_LEN + iph_len + 8);
    if (!tmp_pkt_buf) {
        evbuffer_free(tmp_icmp_pkt_buf);
        return ZPN_MCONN_ICMP_PKT_INTERNAL_ERR;
    }

    /* Temp IP Header */
    struct ip *tmp_iph = (struct ip *)tmp_pkt_buf;;

    tmp_iph->ip_hl  = 5;
    tmp_iph->ip_v   = IPVERSION;
    tmp_iph->ip_id  = ((long)icmp_linear_buf) & 0xFFFF;
    tmp_iph->ip_ttl = 255;
    tmp_iph->ip_p   = IPPROTO_ICMP;

    // TODO: Need to handle more on ZRDT & fconn validity
    struct zpn_tlv *tlv = zpn_mconn_get_local_owner(mconn, 0);
    if (tlv && tlv->type == zpn_fohh_tlv) {
        struct fohh_connection *fconn = tlv->conn.f_conn;
        if (!fconn) {
            evbuffer_free(tmp_icmp_pkt_buf);
            ZPN_FREE(tmp_pkt_buf);
            return ZPN_MCONN_ICMP_PKT_NO_ERR;
        }

        if(fconn->local_address.ss_family == AF_INET) {
            tmp_iph->ip_src = ((struct sockaddr_in *)(&fconn->local_address))->sin_addr;
        } else if(fconn->local_address.ss_family == AF_INET6) {
            /* Get the IPv4 src address through which we can send back the icmp time exceeded packet */
            struct sockaddr_storage src_sockaddr_ss;
            struct sockaddr_storage dst_sockaddr_ss;
            struct sockaddr_storage gw_sockaddr_ss;

            ((struct sockaddr_in *)&dst_sockaddr_ss)->sin_family = AF_INET;
            ((struct sockaddr_in *)&dst_sockaddr_ss)->sin_addr = iph->ip_src;

            char *interface = zhealth_os_route_get(&dst_sockaddr_ss, &src_sockaddr_ss, &gw_sockaddr_ss);
            if(interface == NULL) {
                tmp_iph->ip_src = ((struct sockaddr_in *)(&fconn->local_address))->sin_addr;
            } else {
                tmp_iph->ip_src = ((struct sockaddr_in *)(&src_sockaddr_ss))->sin_addr;
            }
        } else {
            ZPN_FREE(tmp_pkt_buf);
            return ZPN_MCONN_ICMP_PKT_NO_ERR;
        }
    }

    tmp_iph->ip_dst = iph->ip_src;
    tmp_iph->ip_len = htons(sizeof(struct ip) + ICMP_HDR_LEN + iph_len + 8 /* 64-bit of data as per RFC - https://tools.ietf.org/html/rfc792*/);
    tmp_iph->ip_sum = csum((unsigned short *)tmp_iph, (sizeof(struct ip)));

    struct icmp *icmph = (struct icmp *)(tmp_pkt_buf + sizeof(struct ip));

    icmph->icmp_type = ICMP_TIMXCEED;
    icmph->icmp_code = ICMP_TIMXCEED_INTRANS;

    memcpy(icmph->icmp_data, icmp_linear_buf, (iph_len + 8));
    icmph->icmp_cksum = csum((unsigned short *)icmph, ((ICMP_HDR_LEN + iph_len + 8)));

    evbuffer_add(tmp_icmp_pkt_buf, tmp_pkt_buf, (sizeof(struct ip) + ICMP_HDR_LEN + iph_len + 8));

    evbuffer_drain(icmp_buf, evbuffer_get_length(icmp_buf));
    evbuffer_remove_buffer(tmp_icmp_pkt_buf, icmp_buf, evbuffer_get_length(tmp_icmp_pkt_buf));

    evbuffer_free(tmp_icmp_pkt_buf);
    ZPN_FREE(tmp_pkt_buf);

    return ZPN_MCONN_ICMP_PKT_NO_ERR;
}

/*
 * [Outer headers with source and destination][Original inner header]
 * [IPv6 header][ICMPv6 header][IPv6 header][ICMPv6 header]
 */
static int zpn_mconn_icmpv6_build_time_exceed_pkt(struct zpn_mconn *mconn,
                                                  struct evbuffer *icmpv6_buf,
                                                  uint8_t *icmpv6_linear_buf,
                                                  int icmpv6_buf_len)
{
    struct evbuffer *tmp_icmpv6_pkt_buf = evbuffer_new();
    if (!tmp_icmpv6_pkt_buf) {
        return ZPN_MCONN_ICMP_PKT_INTERNAL_ERR;
    }

    struct ip6_hdr *ip6h = (struct ip6_hdr *)(icmpv6_buf);
    int iph_len = sizeof(struct ip6_hdr);

    /*
     * As per rfc4443 (https://datatracker.ietf.org/doc/html/rfc4443#section-3.3),
     * the payload of "Time Exceeded Message" ICMPv6 packet is:
     *   "As much of invoking packet as possible without the ICMPv6 packet
     *    exceeding the minimum IPv6 MTU."
     * The minimum IPv6 MTU is 1280 bytes. So final packet should look like:
     *    Outer IPv6 header + ICMPv6 header + invoking packet
     * such that total leght is below or equal to 1280 bytes.
     */

    int total_packet_len = iph_len + ICMP_HDR_LEN + icmpv6_buf_len;
    int icmpv6_payload_len = total_packet_len < MIN_IPV6_MTU ? icmpv6_buf_len : (icmpv6_buf_len - (total_packet_len - MIN_IPV6_MTU));

    /* Inner packet and outer header sizes */
    int whole_pkt_len = iph_len + ICMP_HDR_LEN + icmpv6_payload_len;

    uint8_t *tmp_pkt_buf = ZPN_CALLOC(whole_pkt_len);
    if (!tmp_pkt_buf) {
        return ZPN_MCONN_ICMP_PKT_INTERNAL_ERR;
    }

    /* Outer IP header */
    struct ip6_hdr *outer_ipv6_hdr = (struct ip6_hdr *)tmp_pkt_buf;
    outer_ipv6_hdr->ip6_flow = 0;
    outer_ipv6_hdr->ip6_vfc = (ip6h->ip6_vfc & ~IPV6_VERSION_MASK) | (IPV6_VERSION & IPV6_VERSION_MASK);
    outer_ipv6_hdr->ip6_plen = htons(whole_pkt_len - iph_len);
    outer_ipv6_hdr->ip6_nxt = IPPROTO_ICMPV6;
    outer_ipv6_hdr->ip6_hops = 255;

    struct zpn_tlv *tlv = zpn_mconn_get_local_owner(mconn, 0);
    if (tlv && tlv->type == zpn_fohh_tlv) {
        struct fohh_connection *fconn = tlv->conn.f_conn;
        if (!fconn) {
            ZPN_FREE(tmp_pkt_buf);
            return ZPN_MCONN_ICMP_PKT_NO_ERR;
        }

        if(fconn->local_address.ss_family == AF_INET6) {
            outer_ipv6_hdr->ip6_src = ((struct sockaddr_in6 *)(&fconn->local_address))->sin6_addr;
        } else if(fconn->local_address.ss_family == AF_INET) {
            /* Get the IPv6 src address through which we can send back the icmpv6 time exceeded packet */
            struct sockaddr_storage src_sockaddr_ss;
            struct sockaddr_storage dst_sockaddr_ss;
            struct sockaddr_storage gw_sockaddr_ss;

            ((struct sockaddr_in6 *)&dst_sockaddr_ss)->sin6_family = AF_INET6;
            ((struct sockaddr_in6 *)&dst_sockaddr_ss)->sin6_addr = ip6h->ip6_src;

            char *interface = zhealth_os_route_get(&dst_sockaddr_ss, &src_sockaddr_ss, &gw_sockaddr_ss);
            if(interface == NULL) {
                outer_ipv6_hdr->ip6_src = ((struct sockaddr_in6 *)(&fconn->local_address))->sin6_addr;
            } else {
                outer_ipv6_hdr->ip6_src = ((struct sockaddr_in6 *)(&src_sockaddr_ss))->sin6_addr;
            }
        } else {
            ZPN_FREE(tmp_pkt_buf);
            return ZPN_MCONN_ICMP_PKT_NO_ERR;
        }
    }

    outer_ipv6_hdr->ip6_dst = ip6h->ip6_src;
    struct icmp6_hdr *icmpv6_hdr = (struct icmp6_hdr *)(tmp_pkt_buf + iph_len);
    icmpv6_hdr->icmp6_type = ICMP6_TIME_EXCEEDED;
    icmpv6_hdr->icmp6_code = ICMP6_TIME_EXCEED_TRANSIT;

    char* payload = (char*)(tmp_pkt_buf + iph_len + ICMP_HDR_LEN);
    memcpy(payload, icmpv6_linear_buf, icmpv6_payload_len);
    icmpv6_hdr->icmp6_cksum = csum((unsigned short *)icmpv6_hdr, (whole_pkt_len - iph_len));

    evbuffer_add(tmp_icmpv6_pkt_buf, tmp_pkt_buf, whole_pkt_len);

    evbuffer_drain(icmpv6_buf, evbuffer_get_length(icmpv6_buf));
    evbuffer_remove_buffer(tmp_icmpv6_pkt_buf, icmpv6_buf, evbuffer_get_length(tmp_icmpv6_pkt_buf));

    evbuffer_free(tmp_icmpv6_pkt_buf);
    ZPN_FREE(tmp_pkt_buf);

    return ZPN_MCONN_ICMP_PKT_NO_ERR;
}

int zpn_mconn_icmp_pkt_handler(struct zpn_mconn *mconn, struct evbuffer *icmp_buf)
{
    int icmp_buf_len = evbuffer_get_length(icmp_buf);

    if (icmp_buf_len <= 0) {
        return ZPN_MCONN_ICMP_PKT_ZERO_LEN_DROP;
    }

    uint8_t *icmp_linear_buf = evbuffer_pullup(icmp_buf, icmp_buf_len);
    if (!icmp_linear_buf) {
        return ZPN_MCONN_ICMP_PKT_INTERNAL_ERR;
    }

    struct ip *iph = (struct ip *) (icmp_linear_buf);
    int iph_len = iph->ip_hl << 2;

    if (icmp_buf_len < iph_len) {
        return ZPN_MCONN_ICMP_PKT_MALFORMED_DROP;
    }

    /* Dropping ICMP frags */
    if (((iph->ip_off)& htons(IP_OFFMASK)) ||
        ((iph->ip_off) & htons(IP_MF))) {
        return ZPN_MCONN_ICMP_PKT_FRAG_DROP;
    }

    if (iph->ip_p != IPPROTO_ICMP) {
        return ZPN_MCONN_ICMP_PKT_MALFORMED_DROP;
    }

    struct icmp *icmph = (struct icmp *)(icmp_linear_buf + iph_len);
    uint8_t icmp_type = icmph->icmp_type;
    uint8_t icmp_code = icmph->icmp_code;

    if (icmp_type == ICMP_ECHO) {
        ZPN_DEBUG_MCONN_ICMP("DEBUG ICMP: %s: ip_src 0x%x; ip_dst 0x%x; icmp ID %u; icmp seq# %u; now (redundant) %"PRId64,
                             __func__, ntohl(iph->ip_src.s_addr), ntohl(iph->ip_dst.s_addr),
                             ntohs(icmph->icmp_hun.ih_idseq.icd_id), ntohs(icmph->icmp_hun.ih_idseq.icd_seq), epoch_us());
        mconn->icmp_state.s_ip = iph->ip_src;
        mconn->icmp_state.a_ip = iph->ip_dst;
        mconn->icmp_state.id = icmph->icmp_hun.ih_idseq.icd_id;
        mconn->icmp_state.seq = icmph->icmp_hun.ih_idseq.icd_seq;

        if (mconn->type == mconn_fohh_tlv_c && mconn->peer && mconn->peer->type == mconn_fohh_tlv_s) { /*broker context*/
            if (zpn_icmp_rate_limit_handler(mconn->icmp_state.cookie)) {
                return ZPN_MCONN_ICMP_PKT_RATE_LIMIT_EXCEEDED_ERR;
            }
        }
    } else if (icmp_type == ICMP_ECHOREPLY) {
       ZPN_DEBUG_MCONN_ICMP("DEBUG ICMP: %s: ip_src 0x%x; ip_dst 0x%x; icmp ID %u; icmp seq# %u; now (redundant) %"PRId64,
                            __func__, ntohl(iph->ip_src.s_addr), ntohl(iph->ip_dst.s_addr),
                            ntohs(icmph->icmp_hun.ih_idseq.icd_id), ntohs(icmph->icmp_hun.ih_idseq.icd_seq), epoch_us());
       mconn->icmp_state.s_ip = iph->ip_src;
       mconn->icmp_state.a_ip = iph->ip_dst;
    }

    if (iph->ip_ttl == 1) {
        if (icmp_type == ICMP_ECHO) {
            /* when reaching here, it is handling a icmp request data
             * so the mconn reaching here is always the client mconn as the request comes from zcc towards app server
             * zcc --> fohh/zrdt ——> (mconn_c process request)user boker (s)----> fohh_c/zrdt_c ---> (mconn_c process request)connector -----> icmp_s ---> app server
             *
             * and we check if ttl > 1 so we can proceed with forwarding the icmp echo request
             */
            int res = zpn_mconn_icmp_build_time_exceed_pkt(mconn, icmp_buf, icmp_linear_buf);
            if (res == ZPN_MCONN_ICMP_PKT_NO_ERR) {
                if (!mconn->peer) return ZPN_MCONN_ICMP_PKT_INTERNAL_ERR;
                if (((mconn->type == mconn_fohh_tlv_c && mconn->peer->type == mconn_icmp_s) ||
                     (mconn->type == mconn_zrdt_tlv_c && mconn->peer->type == mconn_icmp_s) ||
                     (mconn->type == mconn_fohh_tlv_c && mconn->peer->type == mconn_fohh_tlv_s) ||
                     (mconn->type == mconn_fohh_tlv_c && mconn->peer->type == mconn_zrdt_tlv_s) ||
                     (mconn->type == mconn_zrdt_tlv_c && mconn->peer->type == mconn_fohh_tlv_s) ||
                     (mconn->type == mconn_zrdt_tlv_c && mconn->peer->type == mconn_zrdt_tlv_s)) &&
                    ((mconn->icmp_access_type == ZPN_MCONN_ICMP_ACCESS_TYPE_PING_TRACEROUTING) ||
                    (mconn->icmp_access_type == ZPN_MCONN_ICMP_ACCESS_TYPE_PING))) {
                    return ZPN_MCONN_ICMP_PKT_FORWARD_TERROR_TO_CLIENT;
                }
                return ZPN_MCONN_ICMP_PKT_ACCESS_ERR;
            }
            return res;
        } else {
            return ZPN_MCONN_ICMP_PKT_TIME_EXCEEDED_DROP;
        }
    }

    iph->ip_ttl--;
    iph->ip_sum = 0;
    iph->ip_sum = csum((unsigned short *)iph, (iph_len));

    if (icmp_type == ICMP_ECHO) {
        return ZPN_MCONN_ICMP_PKT_FORWARD_TO_PEER;
    } else if (icmp_type == ICMP_ECHOREPLY) {
#if 0
        mconn->icmp_state.s_ip = iph->ip_src;
        mconn->icmp_state.a_ip = iph->ip_dst;
#endif
        return ZPN_MCONN_ICMP_PKT_FORWARD_REPLY_TO_CLIENT;
    } else if (icmp_type == ICMP_TIMXCEED && icmp_code == ICMP_TIMXCEED_INTRANS) {
        if (!mconn->peer) return ZPN_MCONN_ICMP_PKT_INTERNAL_ERR;
        /*
         * when reaching here, we are handling a icmp response packet.
         * so the mconn reaching here is always the server mconn as the request comes from app server towards zcc server
         * zcc <-- fohh/zrdt <—— user boker ((mconn_s process response)) <---- fohh_c/zrdt_c <--- connector(mconn_s process response) <--- icmp_s <--- app server
         *
         * and we validate if its the desired setup so we can forward the zcc back to zcc
         */
        if (((mconn->type == mconn_icmp_s && mconn->peer->type == mconn_fohh_tlv_c) ||
             (mconn->type == mconn_icmp_s && mconn->peer->type == mconn_zrdt_tlv_c) ||
             (mconn->type == mconn_fohh_tlv_s && mconn->peer->type == mconn_fohh_tlv_c) ||
             (mconn->type == mconn_fohh_tlv_s && mconn->peer->type == mconn_zrdt_tlv_c) ||
             (mconn->type == mconn_zrdt_tlv_s && mconn->peer->type == mconn_fohh_tlv_c) ||
             (mconn->type == mconn_zrdt_tlv_s && mconn->peer->type == mconn_zrdt_tlv_c)) &&
            ((mconn->icmp_access_type == ZPN_MCONN_ICMP_ACCESS_TYPE_PING_TRACEROUTING) ||
             (mconn->icmp_access_type == ZPN_MCONN_ICMP_ACCESS_TYPE_PING))) {
            return ZPN_MCONN_ICMP_PKT_FORWARD_TERROR_TO_CLIENT;
        }
        return ZPN_MCONN_ICMP_PKT_ACCESS_ERR;
    } else if (icmp_type == ICMP_TIMXCEED && icmp_code == ICMP_TIMXCEED_REASS) {
        return ZPN_MCONN_ICMP_PKT_FORWARD_TERROR_TO_CLIENT;
    } else if (icmp_type == ICMP_UNREACH) {
        if (icmp_code == ICMP_UNREACH_NEEDFRAG) {
            if (!mconn->peer) return ZPN_MCONN_ICMP_PKT_INTERNAL_ERR;
            /*
             * see comments above or ICMP_TIMXCEED packet
             */
            if (((mconn->type == mconn_icmp_s && mconn->peer->type == mconn_fohh_tlv_c) ||
                 (mconn->type == mconn_icmp_s && mconn->peer->type == mconn_zrdt_tlv_c) ||
                 (mconn->type == mconn_fohh_tlv_s && mconn->peer->type == mconn_fohh_tlv_c) ||
                 (mconn->type == mconn_fohh_tlv_s && mconn->peer->type == mconn_zrdt_tlv_c) ||
                 (mconn->type == mconn_zrdt_tlv_s && mconn->peer->type == mconn_fohh_tlv_c) ||
                 (mconn->type == mconn_zrdt_tlv_s && mconn->peer->type == mconn_zrdt_tlv_c)) &&
                ((mconn->icmp_access_type == ZPN_MCONN_ICMP_ACCESS_TYPE_PING_TRACEROUTING) ||
                 (mconn->icmp_access_type == ZPN_MCONN_ICMP_ACCESS_TYPE_PING))) {
                return ZPN_MCONN_ICMP_PKT_FORWARD_DERROR_TO_CLIENT;
            }
            return ZPN_MCONN_ICMP_PKT_ACCESS_ERR;
        } else if (icmp_code == ICMP_UNREACH_NET || icmp_code == ICMP_UNREACH_HOST) {
            return ZPN_MCONN_ICMP_PKT_FORWARD_DERROR_TO_CLIENT;
        }
    }

    return ZPN_MCONN_ICMP_PKT_MALFORMED_DROP;
}

/*
 * For now using the same error code as ICMPv4,
 * Check if we need separate error codes for ICMPv6
 */
int zpn_mconn_icmpv6_pkt_handler(struct zpn_mconn *mconn, struct evbuffer *icmpv6_buf)
{
    int icmpv6_buf_len = evbuffer_get_length(icmpv6_buf);

    if (icmpv6_buf_len <= 0) {
        return ZPN_MCONN_ICMP_PKT_ZERO_LEN_DROP;
    }

    uint8_t *icmpv6_linear_buf = evbuffer_pullup(icmpv6_buf, icmpv6_buf_len);
    if (!icmpv6_linear_buf) {
        return ZPN_MCONN_ICMP_PKT_INTERNAL_ERR;
    }

    struct ip6_hdr *ip6h = (struct ip6_hdr *)(icmpv6_linear_buf);
    int iph_len = sizeof(struct ip6_hdr);
    int iph_icmph_len = iph_len + sizeof(struct icmp6_hdr);

    if ((icmpv6_buf_len < iph_icmph_len) ||
        (ip6h->ip6_nxt != IPPROTO_ICMPV6)) {
        return ZPN_MCONN_ICMP_PKT_MALFORMED_DROP;
    }

    struct icmp6_hdr *icmpv6_hdr = (struct icmp6_hdr *)(icmpv6_linear_buf + iph_len);
    uint8_t icmp_type = icmpv6_hdr->icmp6_type;
    uint8_t icmp_code = icmpv6_hdr->icmp6_code;

    /* If ICMP request is coming to the broker then check for rate limiting */
    if (icmp_type == ICMP6_ECHO_REQUEST) {
        /* If the request is coming from the ZCC -> Broker, then rate limit the request */
        if (mconn->type == mconn_fohh_tlv_c && mconn->peer && mconn->peer->type == mconn_fohh_tlv_s) {
            if (zpn_icmp_rate_limit_handler(mconn->icmp_state.cookie)) {
                return ZPN_MCONN_ICMP_PKT_RATE_LIMIT_EXCEEDED_ERR;
            }
        }
    }

    /* if TTL for Echo request is 1, then reconstruct the time exceeded message */
    if (ip6h->ip6_hops == 1) {
        if (icmp_type == ICMP6_ECHO_REQUEST) {
            int res = zpn_mconn_icmpv6_build_time_exceed_pkt(mconn, icmpv6_buf, icmpv6_linear_buf, icmpv6_buf_len);
            if (res == ZPN_MCONN_ICMP_PKT_NO_ERR) {
                if (!mconn->peer) return ZPN_MCONN_ICMP_PKT_INTERNAL_ERR;
                if (((mconn->type == mconn_fohh_tlv_c && mconn->peer->type == mconn_icmp_s) ||
                     (mconn->type == mconn_zrdt_tlv_c && mconn->peer->type == mconn_icmp_s) ||
                     (mconn->type == mconn_fohh_tlv_c && mconn->peer->type == mconn_fohh_tlv_s) ||
                     (mconn->type == mconn_fohh_tlv_c && mconn->peer->type == mconn_zrdt_tlv_s) ||
                     (mconn->type == mconn_zrdt_tlv_c && mconn->peer->type == mconn_fohh_tlv_s) ||
                     (mconn->type == mconn_zrdt_tlv_c && mconn->peer->type == mconn_zrdt_tlv_s)) &&
                    ((mconn->icmp_access_type == ZPN_MCONN_ICMP_ACCESS_TYPE_PING) ||
                     (mconn->icmp_access_type == ZPN_MCONN_ICMP_ACCESS_TYPE_PING_TRACEROUTING))) {
                    return ZPN_MCONN_ICMP_PKT_FORWARD_TERROR_TO_CLIENT;
                }
                return ZPN_MCONN_ICMP_PKT_ACCESS_ERR;
            }
            return res;
        } else {
            return ZPN_MCONN_ICMP_PKT_TIME_EXCEEDED_DROP;
        }
    }

    ip6h->ip6_hops--;

    if (icmp_type == ICMP6_ECHO_REQUEST) {
        return ZPN_MCONN_ICMP_PKT_FORWARD_TO_PEER;
    } else if (icmp_type == ICMP6_ECHO_REPLY) {
        return ZPN_MCONN_ICMP_PKT_FORWARD_REPLY_TO_CLIENT;
    } else if (icmp_type == ICMP6_TIME_EXCEEDED && icmp_code == ICMP6_TIME_EXCEED_TRANSIT) {
        if (!mconn->peer) return ZPN_MCONN_ICMP_PKT_INTERNAL_ERR;
        /* This is icmp response coming from App Server towards ZCC server */
        if (((mconn->type == mconn_icmp_s && mconn->peer->type == mconn_fohh_tlv_c) ||
             (mconn->type == mconn_icmp_s && mconn->peer->type == mconn_zrdt_tlv_c) ||
             (mconn->type == mconn_fohh_tlv_s && mconn->peer->type == mconn_fohh_tlv_c) ||
             (mconn->type == mconn_fohh_tlv_s && mconn->peer->type == mconn_zrdt_tlv_c) ||
             (mconn->type == mconn_zrdt_tlv_s && mconn->peer->type == mconn_fohh_tlv_c) ||
             (mconn->type == mconn_zrdt_tlv_s && mconn->peer->type == mconn_zrdt_tlv_c)) &&
             ((mconn->icmp_access_type == ZPN_MCONN_ICMP_ACCESS_TYPE_PING) ||
             (mconn->icmp_access_type == ZPN_MCONN_ICMP_ACCESS_TYPE_PING_TRACEROUTING))) {
            return ZPN_MCONN_ICMP_PKT_FORWARD_TERROR_TO_CLIENT;
        }
        return ZPN_MCONN_ICMP_PKT_ACCESS_ERR;
    } else if (icmp_type == ICMP6_TIME_EXCEEDED && icmp_code == ICMP6_TIME_EXCEED_REASSEMBLY) {
        return ZPN_MCONN_ICMP_PKT_FORWARD_TERROR_TO_CLIENT;
    } else if (icmp_type == ICMP6_DST_UNREACH &&
              ((icmp_code == ICMP6_DST_UNREACH_NOROUTE) ||
               (icmp_code == ICMP6_DST_UNREACH_ADMIN) ||
               (icmp_code == ICMP6_DST_UNREACH_BEYONDSCOPE) ||
               (icmp_code == ICMP6_DST_UNREACH_ADDR) ||
               (icmp_code == ICMP6_DST_UNREACH_NOPORT))) {
        return ZPN_MCONN_ICMP_PKT_FORWARD_DERROR_TO_CLIENT;
    } else if (icmp_type == ICMP6_PACKET_TOO_BIG) {
        if (!mconn->peer) return ZPN_MCONN_ICMP_PKT_INTERNAL_ERR;
        if (((mconn->type == mconn_icmp_s && mconn->peer->type == mconn_fohh_tlv_c) ||
             (mconn->type == mconn_icmp_s && mconn->peer->type == mconn_zrdt_tlv_c) ||
             (mconn->type == mconn_fohh_tlv_s && mconn->peer->type == mconn_fohh_tlv_c) ||
             (mconn->type == mconn_fohh_tlv_s && mconn->peer->type == mconn_zrdt_tlv_c) ||
             (mconn->type == mconn_zrdt_tlv_s && mconn->peer->type == mconn_fohh_tlv_c) ||
             (mconn->type == mconn_zrdt_tlv_s && mconn->peer->type == mconn_zrdt_tlv_c)) &&
             ((mconn->icmp_access_type == ZPN_MCONN_ICMP_ACCESS_TYPE_PING) ||
             (mconn->icmp_access_type == ZPN_MCONN_ICMP_ACCESS_TYPE_PING_TRACEROUTING))) {
            return ZPN_MCONN_ICMP_PKT_FORWARD_DERROR_TO_CLIENT;
        }
        return ZPN_MCONN_ICMP_PKT_ACCESS_ERR;
    }

    return ZPN_MCONN_ICMP_PKT_MALFORMED_DROP;
}
