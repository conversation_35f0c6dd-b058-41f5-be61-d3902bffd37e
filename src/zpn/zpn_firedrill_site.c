/*
 * zpn_firedrill_site.c. Copyright (C) 2024 Zscaler Inc. All Rights Reserved.
 */

#include "wally/wally.h"
#include "zpath_lib/zpath_app.h"

#include "zpath_lib/zpath_debug.h"

#include "zpn/zpn_lib.h"

#include "zpn/zpn_firedrill_site.h"
#include "zpn/zpn_firedrill_site_compiled.h"

struct argo_structure_description *zpn_firedrill_site_description = NULL;

static struct wally_index_column **zpn_firedrill_site_customer_gid_column = NULL;
static struct wally_index_column **zpn_firedrill_site_site_gid_column = NULL;

void zpn_firedrill_site_row_fixup(struct argo_object *row)
{
    struct zpn_firedrill_site *firedrill_site = row->base_structure_void;

    struct {
        const char* unit;
        int64_t factor;
    } units[] = {
        {"SECONDS", 1}, {"MINUTES", 60}, {"HOURS", 60 * 60},
    };

#define FIREDILL_UNIT_DEFAULT_UNIT 60
    /* default unit is MINUTES if the unit is unknown */
    int64_t factor = FIREDILL_UNIT_DEFAULT_UNIT;
    if (firedrill_site->firedrill_interval) {
        for (int i = 0; i < sizeof(units) / sizeof(units[0]); ++i) {
            if (strncmp(firedrill_site->firedrill_interval_unit, units[i].unit, strlen(units[i].unit)) == 0) {
                factor = units[i].factor;
                break;
            }
        }
    }
    firedrill_site->firedrill_interval_s = firedrill_site->firedrill_interval * factor;

    if (zpn_debug_get(ZPN_DEBUG_SITE_CONTROLLER_IDX)) {
        char dump[8000];
        if (argo_object_dump(row, dump, sizeof(dump), NULL, 0) == ARGO_RESULT_NO_ERROR) {
            ZPN_DEBUG_SITEC("Row callback: %s", dump);
        }
    }
}

int zpn_firedrill_site_row_callback(void *cookie,
                                       struct wally_registrant *registrant,
                                       struct wally_table *table,
                                       struct argo_object *previous_row,
                                       struct argo_object *row,
                                       int64_t request_id)
{
    if (zpn_debug_get(ZPN_DEBUG_SITE_CONTROLLER_IDX)) {
        char dump[8000];
        if (argo_object_dump(row, dump, sizeof(dump), NULL, 0) == ARGO_RESULT_NO_ERROR) {
            ZPN_DEBUG_SITEC("Row callback: %s", dump);
        }
    }

    return WALLY_RESULT_NO_ERROR;
}

int zpn_firedrill_site_init(struct wally *single_tenant_wally,
                            int64_t single_tenant_gid,
                            wally_row_callback_f *row_cb,
                            int single_tenant_fully_loaded,
                            int register_with_zpath_table)
{
    int res;

    zpn_firedrill_site_description = argo_register_global_structure(ZPN_FIREDRILL_SITE_HELPER);
    if (!zpn_firedrill_site_description) {
        ZPN_LOG(AL_ERROR, "No description: zpn_firedrill_site_init");
        return ZPN_RESULT_ERR;
    }

    if (!row_cb) {
        row_cb = zpn_firedrill_site_row_callback;
    }
    if (single_tenant_wally) {
        struct wally_table *table;
        int shard_index = ZPATH_SHARD_FROM_GID(single_tenant_gid);

        if (single_tenant_fully_loaded) {
            /* This is generally used by site controller */
            res = zpath_app_fully_loaded_customer_table(&table,
                                                        ZPATH_GID_GET_CUSTOMER_GID(single_tenant_gid),
                                                        single_tenant_wally,
                                                        zpn_firedrill_site_description,
                                                        row_cb,
                                                        NULL,
                                                        zpn_firedrill_site_row_fixup,
                                                        register_with_zpath_table);
            if (res) {
                ZPN_LOG(AL_ERROR, "Could not get zpn_site table fully loaded");
                return ZPN_RESULT_ERR;
            }
        } else {
            table = wally_table_create(single_tenant_wally,
                                       1,
                                       zpn_firedrill_site_description,
                                       row_cb,
                                       NULL,
                                       1,
                                       1,
                                       zpn_firedrill_site_row_fixup);
        }

        if (!table) {
            ZPN_LOG(AL_ERROR, "Could not get table");
            return ZPN_RESULT_ERR;
        }

        zpn_firedrill_site_customer_gid_column = ZPN_CALLOC(ZPATH_MAX_SHARDS * sizeof(*zpn_firedrill_site_customer_gid_column));

        zpn_firedrill_site_customer_gid_column[shard_index] = wally_table_get_index(table, "customer_gid");
        if (!zpn_firedrill_site_customer_gid_column[shard_index]) {
            ZPN_LOG(AL_ERROR, "Could not get zpn_firedrill_site_customer_gid_column");
            return ZPN_RESULT_ERR;
        }

        zpn_firedrill_site_site_gid_column = ZPN_CALLOC(ZPATH_MAX_SHARDS * sizeof(*zpn_firedrill_site_site_gid_column));

        zpn_firedrill_site_site_gid_column[shard_index] = wally_table_get_index(table, "site_gid");
        if (!zpn_firedrill_site_site_gid_column[shard_index]) {
            ZPN_LOG(AL_ERROR, "Could not get zpn_firedrill_site_site_gid_column");
            return ZPN_RESULT_ERR;
        }

    } else {
        res = zpath_app_fully_loaded_sharded_table(zpn_firedrill_site_description,
                                                   row_cb,
                                                   NULL,
                                                   zpn_firedrill_site_row_fixup);
        if (res) {
            return res;
        }

        zpn_firedrill_site_customer_gid_column = zpath_app_get_sharded_index("zpn_firedrill_site", "customer_gid");
        if (!zpn_firedrill_site_customer_gid_column) {
            ZPN_LOG(AL_ERROR, "Could not get zpn_firedrill_site_customer_gid_column");
            return ZPN_RESULT_ERR;
        }
        zpn_firedrill_site_site_gid_column = zpath_app_get_sharded_index("zpn_firedrill_site", "site_gid");
        if (!zpn_firedrill_site_site_gid_column) {
             ZPATH_LOG(AL_ERROR, "couldn't get index column for site gid");
             return ZPATH_RESULT_ERR;
        }
    }

    return ZPN_RESULT_NO_ERROR;
}

int zpn_firedrill_site_get_by_customer_gid(int64_t customer_gid,
                                        struct zpn_firedrill_site **firedrill_site,
                                        size_t *row_count,
                                        wally_response_callback_f callback_f,
                                        void *callback_cookie,
                                        int64_t callback_id)
{
    int res;
    int shard_index = ZPATH_SHARD_FROM_GID(customer_gid);

    res = wally_table_get_rows_fast(zpn_firedrill_site_customer_gid_column[shard_index],
                                    &customer_gid,
                                    sizeof(customer_gid),
                                    (void **) firedrill_site,
                                    row_count,
                                    1,
                                    callback_f,
                                    callback_cookie,
                                    callback_id);
    return res;
}

int zpn_firedrill_site_by_site_gid(int64_t site_gid, int64_t customer_gid, wally_response_callback_f *callback_f,
                                    struct zpn_firedrill_site **firedrill_instance)
{
    int res;
    size_t rows = 1;
    int shard_index = ZPATH_SHARD_FROM_GID(customer_gid);

    res = wally_table_get_rows_fast(zpn_firedrill_site_site_gid_column[shard_index],
        &site_gid,
        sizeof(site_gid),
        (void **)firedrill_instance,
        &rows,
        1,
        callback_f,
        (void *)firedrill_instance,
        0);
    return res;
}
