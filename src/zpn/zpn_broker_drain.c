/*
 * zpn_broker_drain.c. Copyright (C) 2023 Zscaler, Inc. All Rights Reserved.
 */

#include "zpath_lib/zpath_lib.h"
#include "fohh/fohh_private.h"
#include "fohh/fohh.h"
#include "zpath_lib/zpath_debug.h"
#include "zpath_lib/zpath_partition_common.h"
#include "zpn/zpn_broker_client.h"
#include "zpn/zbalance/zpn_balance_redirect.h"
#include "zpath_lib/zpath_constellation.h"
#include "zpn/zpn_broker_drain.h"
#include "zhash/zhash_table.h"
#include "zpn/zpn_broker_assert.h"
#include "zpath_lib/zpath_config_override.h"
#include "zpath_lib/zpath_config_override_keys.h"
#include "zpath_lib/zpath_customer.h"
#include "zpath_lib/zpath_domain_lookup.h"
#include "zpn/zpn_broker_drain_compiled.h"
#include "zpn/zpn_broker_maintenance.h"

struct argo_structure_description *zpn_broker_drain_stats_description;
struct zpn_broker_drain_stats drain_stats_all = {0};

static struct argo_structure_description *zpn_broker_drain_dry_run_stats_desc;

/* global struct for holding stats with client-level info */
static struct zpn_broker_drain_stats_entry g_drain_stats[zpn_client_type_total_count][drain_type_total_count] = {{{0}}};;

const char *zpn_conn_drain_reason_subtype_strings[] = {
    [drain_subtype_none] = "",
    [drain_subtype_customer_constellation] =  BRK_REDIRECT_REASON_CUSTOMER_CONSTELLATION_CHANGE,
    [drain_subtype_instance_constellation] =   BRK_REDIRECT_REASON_INSTANCE_CONSTELLATION_CHANGE,
    [drain_subtype_customer_partition] = BRK_REDIRECT_REASON_CUSTOMER_PARTITION_CHANGE,
    [drain_subtype_instance_partition] = BRK_REDIRECT_REASON_INSTANCE_PARTITION_CHANGE,
    [drain_subtype_invalid] = BRK_REDIRECT_REASON_OTHER,
};

/* Given type; retrieve the drain reason string */
const char *zpn_conn_get_drain_reason_string_for_type(enum zpn_conn_drain_type type)
{
    if (drain_type_manual == type) {
        return BRK_REDIRECT_REASON_MANUAL_DRAIN_TRIGGER;
    } else if (drain_type_maintenance == type) {
        return BRK_REDIRECT_REASON_BROKER_MAINTENANCE;
    }

    /* all other cases */
    return BRK_REDIRECT_REASON_OTHER;
}

/* based on reason code, set the drain subtype */
enum zpn_conn_drain_subtype
zpn_conn_get_drain_subtype(enum zpn_conn_drain_type drain_type)
{
    switch (drain_type) {
        case drain_type_manual: {
            return drain_subtype_none;
        }
        case drain_type_maintenance: {
            return drain_subtype_maintenance_upgrade;
        }
        default: {
            break;
        }
    }
    return drain_subtype_none;
}

/* Displaying drain_status string */
const char *zpn_conn_drain_status_strings[] = {
    [drain_status_none] = "none",
    [drain_status_initiated] = "initiated",
    [drain_status_pending] = "pending",
    [drain_status_not_possible] = "not_possible",
    [drain_status_completed] = "completed",
    [drain_status_invalid] = "",
};

const char *zpn_conn_get_drain_status_string(enum zpn_conn_drain_status drain_status)
{
    if (drain_status > drain_status_invalid || !zpn_conn_drain_status_strings[drain_status])
        return "";
    return zpn_conn_drain_status_strings[drain_status];
}

/* Displaying drain_type string */
const char *zpn_conn_drain_type_strings[] = {
    [drain_type_none] = "",
    [drain_type_manual] = "manual_drain",
    [drain_type_maintenance] = "maintenance_drain",
    //[drain_type_auto] = "automated_drain",
    [drain_type_total_count] = "invalid",
};

const char *zpn_conn_get_drain_type_string(enum zpn_conn_drain_type drain_type)
{
    if (drain_type > drain_type_total_count || !zpn_conn_drain_type_strings[drain_type])
        return "";
    return zpn_conn_drain_type_strings[drain_type];
}

int zpn_conn_is_valid_drain_type(enum zpn_conn_drain_type drain_type)
{
    if ((drain_type > drain_type_none) && (drain_type < drain_type_total_count))
        return 1;
    else
        return 0;
}

/* Displaying drain subtype string */
const char *zpn_conn_drain_subtype_strings[] = {
    [drain_subtype_none] = "none",
    [drain_subtype_customer_constellation] = "customer_constellation",
    [drain_subtype_instance_constellation] = "instance_constellation",
    [drain_subtype_customer_partition] = "customer_partition",
    [drain_subtype_instance_partition] = "instance_partition",
    [drain_subtype_invalid] = "invalid",
};

const char *zpn_conn_get_drain_subtype_string(enum zpn_conn_drain_subtype drain_subtype)
{
    if (drain_subtype > drain_subtype_invalid || !zpn_conn_drain_subtype_strings[drain_subtype])
        return "";
    return zpn_conn_drain_subtype_strings[drain_subtype];
}

/* Iteration contexts */
struct zpn_broker_drain_conn_ctr_entry {
    int is_draining_required;
    int64_t drainable_conn_count;
    int64_t total_conn_count;
    int64_t customer_gid;
};

struct zpn_broker_drain_request {
    int64_t customer_gid;
    int64_t cookie_gid;     /* partition_gid/constellation_gid as the case maybe */
    enum zpn_conn_drain_type drain_type;
    enum zpn_conn_drain_subtype drain_subtype;
    const char *reason;
    int ignore_config;
    int64_t drain_timeout_s;
    uint32_t percentage;
};

/***** stats *****/
struct zpn_broker_drain_stats_table {
    zpath_rwlock_t lock;
    struct zhash_table *customer_stats_hash;
};
static struct zpn_broker_drain_stats_table drain_stats_table;

/**** manual drain information ****/

struct zpn_broker_manual_drain_entry {
    int64_t         customer_gid;
    int64_t         timestamp_s;    /* timestamp at which the entry was created */
    int64_t         drain_timeout_s;
    uint32_t        percentage;     /* drain % of connections */
    struct event*   manual_drain_timer;
};
struct zpn_broker_manual_drain_table {
    zpath_rwlock_t lock;
    struct zhash_table *manual_drain_hash;
};
static struct zpn_broker_manual_drain_table manual_drain_outstanding;


/* Forward declarations */
static int zpn_broker_is_draining_required(int64_t customer_gid);
static void
zpn_broker_drain_mark_cstates_per_customer_on_thread(int64_t curr_fohh_thread, int64_t customer_gid,
                                                     struct zpn_broker_connected_customer_clients* gid_client_list,
                                                     const struct zpn_broker_drain_request *drain_req);

/****************************************************
 * Stats updation functions
 ***************************************************/

int zpn_broker_drain_stats_fill_all(void* cookie, int counter, void* structure_data)
{
    struct zpn_broker_drain_stats *temp_stats;
    temp_stats = (struct zpn_broker_drain_stats *)structure_data;
    enum zpn_conn_drain_type type = drain_type_manual;

    /* memset first */
    memset(temp_stats, 0, sizeof(struct zpn_broker_drain_stats));

    /* accumulating across all client types */
    for (int i = 1; i < zpn_client_type_total_count; i++) {
        type = drain_type_manual;
        temp_stats->manual_drain_not_possible_count += g_drain_stats[i][type].drain_not_possible_count;
        temp_stats->manual_drain_completed_count += g_drain_stats[i][type].drain_completed_count;
        temp_stats->manual_total_mtunnels_drained += g_drain_stats[i][type].total_mtunnels_drained;

        type = drain_type_maintenance;
        temp_stats->maintenance_drain_not_possible_count += g_drain_stats[i][type].drain_not_possible_count;
        temp_stats->maintenance_drain_completed_count += g_drain_stats[i][type].drain_completed_count;
        temp_stats->maintenance_total_mtunnels_drained += g_drain_stats[i][type].total_mtunnels_drained;
    }

    return ZPN_RESULT_NO_ERROR;
}

static void zpn_broker_drain_stats_incr_decr(struct zpn_broker_drain_stats_table_entry *entry,
                                             enum zpn_client_type client_type,
                                             enum zpn_conn_drain_type drain_type,
                                             enum zpn_conn_drain_status drain_status,
                                             int increment)
{
    if (!entry)
        return;

    /* update both customer stats and global stats as well. */
    switch (drain_status) {
        case drain_status_none: {
            break;
        }
        case drain_status_pending: {
            entry->stats[client_type][drain_type].drain_pending_count += (increment != 0)  ? 1 : -1;
            g_drain_stats[client_type][drain_type].drain_pending_count += (increment != 0)  ? 1 : -1;
            break;
        }
        case drain_status_initiated: {
            entry->stats[client_type][drain_type].drain_initiated_count += (increment != 0) ? 1 : -1;
            g_drain_stats[client_type][drain_type].drain_initiated_count += (increment != 0) ? 1 : -1;
            break;
        }
        case drain_status_not_possible: {
            entry->stats[client_type][drain_type].drain_not_possible_count += (increment != 0) ? 1 : -1;
            g_drain_stats[client_type][drain_type].drain_not_possible_count += (increment != 0) ? 1 : -1;
            break;
        }
        case drain_status_completed: {
            entry->stats[client_type][drain_type].drain_completed_count += (increment != 0) ? 1 : -1;
            g_drain_stats[client_type][drain_type].drain_completed_count += (increment != 0) ? 1 : -1;
            break;
        }
        default: {
            break;
        }
    }
}

/*
 * zpn_broker_drain_stats_update_internal
 *  decrement for old state and increment for new one.
 */
static void zpn_broker_drain_stats_update_internal(struct zpn_broker_drain_stats_table_entry *entry,
                                                   struct zpn_broker_client_fohh_state *c_state,
                                                   enum zpn_conn_drain_type drain_type,
                                                   enum zpn_conn_drain_status drain_status)
{
    zpn_broker_drain_stats_incr_decr(entry, c_state->client_type, drain_type,
                                    c_state->drain_info[drain_type].drain_status, 0);

    /* update new state and increment count */
    c_state->drain_info[drain_type].drain_status = drain_status;

    zpn_broker_drain_stats_incr_decr(entry, c_state->client_type, drain_type, drain_status, 1);
}

/*
 * zpn_broker_update_drain_status_locked
 *  Update the drain status and the counters as well for each state
 *  Assume; lock to drain stats table is already held
 */
void zpn_broker_update_drain_status_locked(struct zpn_broker_client_fohh_state *c_state,
                                    enum zpn_conn_drain_type type,
                                    enum zpn_conn_drain_status status)
{
    if (!c_state) {
        return;
    }

    /* if we got an invalid drain_type, ignore */
    if (!zpn_conn_is_valid_drain_type(type)) {
        return;
    }

    /* validate invalid drain_status */
    if (status >= drain_status_invalid) {
        return;
    }

    /* validate client type */
    if (!zpn_client_type_is_valid(c_state->client_type)) {
        return;
    }

    /* update timestamp */
    c_state->drain_info[type].timestamp_s = epoch_s();

    struct zpn_broker_drain_stats_table_entry *entry = NULL;
    int64_t customer_gid = c_state->customer_gid;

    /* Lookup for an entry for customer_gid */
    entry = zhash_table_lookup(drain_stats_table.customer_stats_hash, &customer_gid, sizeof(customer_gid), NULL);

    if (!entry) {
        /* creating a new stats entry for this customer. */
        entry = ZPN_CALLOC(sizeof(struct zpn_broker_drain_stats_table_entry));
        if (entry) {
            zhash_table_store(drain_stats_table.customer_stats_hash, &customer_gid,
                                sizeof(customer_gid), 0, entry);
        } else {
            return;
        }
    }

    zpn_broker_drain_stats_update_internal(entry, c_state, type, status);

    /*
     * drain status extra handling
     *  'Initiated':      this c_state drain will be owned by this drain_type, used at completed state;
     *                    update drain_initiator; and cleanup stats for other drain types
     *  'Completed':    identify mtunnels being drained ; and tag against the drain initiator.
     */
    if (drain_status_initiated == status) {
        c_state->drain_initiator = type;
        for (enum zpn_conn_drain_type i = drain_type_none + 1;
                (i != type) && (type < drain_type_total_count); type++) {
            zpn_broker_drain_stats_update_internal(entry, c_state, type, drain_status_none);
        }
    } else if (drain_status_completed == status) {
        entry->stats[c_state->client_type][type].total_mtunnels_drained += c_state_get_mtunnel_count(c_state);
    }
}

/*
 * zpn_broker_update_drain_status
 *  Update the drain status and the counters as well for each state
 */
void zpn_broker_update_drain_status(struct zpn_broker_client_fohh_state *c_state,
                                    enum zpn_conn_drain_type type,
                                    enum zpn_conn_drain_status status,
                                    const char *file, int line)
{
    ZPATH_RWLOCK_WRLOCK(&(drain_stats_table.lock), file, line);
    zpn_broker_update_drain_status_locked(c_state, type, status);
    ZPATH_RWLOCK_UNLOCK(&(drain_stats_table.lock), file, line);
}

/****************************************************
 * Manual drain outstanding information
 ***************************************************/
/*
 * zpn_broker_is_manual_drain_scheduled
 *  Checks if manual drain is scheduled for this customer,
 *  useful when new conn come when drain is scheduled
 */
int zpn_broker_is_manual_drain_scheduled(int64_t customer_gid)
{
    int res = 0;
    ZPN_BROKER_RD_LOCK(manual_drain_outstanding.lock);
    if (zhash_table_lookup(manual_drain_outstanding.manual_drain_hash, &customer_gid, sizeof(customer_gid), NULL)) {
        res = 1;
    }
    ZPN_BROKER_UNLOCK(manual_drain_outstanding.lock);
    return res;
}

int64_t zpn_broker_drain_get_manual_drain_timer_expiry(int64_t customer_gid)
{
    struct zpn_broker_manual_drain_entry *entry = NULL;
    int64_t expiry_s = 0;
    ZPN_BROKER_RD_LOCK(manual_drain_outstanding.lock);
    entry = zhash_table_lookup(manual_drain_outstanding.manual_drain_hash, &customer_gid, sizeof(customer_gid), NULL);
    if (entry) {
        expiry_s = entry->timestamp_s + (rand() % entry->drain_timeout_s);  /* randomize the expiry time */
    }
    ZPN_BROKER_UNLOCK(manual_drain_outstanding.lock);
    return expiry_s;
}

/*
 * zpn_broker_drain_get_manual_drain_max_timer_expiry
 *  The max duration till which the drain is active
 */
int64_t zpn_broker_drain_get_manual_drain_max_timer_expiry(int64_t customer_gid)
{
    struct zpn_broker_manual_drain_entry *entry = NULL;
    int64_t expiry_s = 0;
    ZPN_BROKER_RD_LOCK(manual_drain_outstanding.lock);
    entry = zhash_table_lookup(manual_drain_outstanding.manual_drain_hash, &customer_gid, sizeof(customer_gid), NULL);
    if (entry) {
        expiry_s = entry->timestamp_s + entry->drain_timeout_s;
    }
    ZPN_BROKER_UNLOCK(manual_drain_outstanding.lock);
    return expiry_s;
}


static void zpn_broker_remove_from_manual_drain_outstanding(int64_t customer_gid)
{
    ZPN_BROKER_WR_LOCK(manual_drain_outstanding.lock);

    struct zpn_broker_manual_drain_entry *entry = NULL;
    entry = zhash_table_lookup(manual_drain_outstanding.manual_drain_hash, &customer_gid, sizeof(customer_gid), NULL);
    if (entry) {
        ZPN_DEBUG_BROKER_DRAIN_CONN("drain_conn: removing manual drain entry; customer; %"PRId64" entry: %p",
                                        entry->customer_gid, entry);
        zhash_table_remove(manual_drain_outstanding.manual_drain_hash, &customer_gid, sizeof(customer_gid), entry);
    }
    ZPN_BROKER_UNLOCK(manual_drain_outstanding.lock);

    if (entry) {
        /* free the entry */
        if (entry->manual_drain_timer) {
            event_free(entry->manual_drain_timer);
            entry->manual_drain_timer = NULL;
        }
        ZPN_FREE(entry);
        entry = NULL;
    }
}

static void zpn_broker_update_manual_drain_timer_callback(evutil_socket_t sock, short flags, void *cookie)
{
    const struct zpn_broker_manual_drain_entry *entry = cookie;
    if (!cookie)
        return;

    /* remove entry from hashtable */
    zpn_broker_remove_from_manual_drain_outstanding(entry->customer_gid);
}

/*
 * zpn_broker_drain_create_manual_entry
 *  Allocate manual drain entry and initiate timer as well.
 */
struct zpn_broker_manual_drain_entry*
zpn_broker_drain_create_manual_entry(int64_t customer_gid, int64_t drain_timeout_s,
                                     uint32_t percentage, int *res)
{
    struct zpn_broker_manual_drain_entry *entry;
    struct timeval tv;

    entry = ZPN_CALLOC(sizeof(struct zpn_broker_manual_drain_entry));
    if (!entry) {
        ZPN_LOG(AL_CRITICAL, "drain_conn: manual_drain_outstanding malloc failed");
        if (res) { *res = ZPN_RESULT_NO_MEMORY;  }
        return NULL;
    }

    /* populate the entry and add to hash table */
    entry->customer_gid = customer_gid;
    entry->drain_timeout_s = drain_timeout_s;
    entry->timestamp_s = epoch_s();
    entry->percentage = percentage;
    entry->manual_drain_timer = event_new(broker_timer_base,
                                            -1,
                                            0,
                                            zpn_broker_update_manual_drain_timer_callback,
                                            entry);
    if (!entry->manual_drain_timer) {
        ZPN_LOG(AL_ERROR, "drain_conn: Could not create manual drain timer");
        ZPN_FREE(entry);
        if (res) { *res = ZPN_RESULT_ERR; }
        return NULL;
    }

    tv.tv_sec = entry->drain_timeout_s;
    tv.tv_usec = 0;
    if (event_add(entry->manual_drain_timer, &tv)) {
        ZPN_LOG(AL_ERROR, "drain_conn: Could not activate manual drain timer");
        event_free(entry->manual_drain_timer);
        ZPN_FREE(entry);
        if (res) { *res = ZPN_RESULT_ERR; }
        return NULL;
    }
    return entry;
}

/****************************************************
 * Drain core Logic
 ***************************************************/

/*
 * zpn_broker_drain_is_ops_forced_redirect
 *  checks if its an OPS forced operation; currently only maintenance drain
 */
static int zpn_broker_drain_is_ops_forced_redirect(enum zpn_conn_drain_type type)
{
    if (drain_type_maintenance == type) {
        return 1;
    }
    return 0;
}

/*
 * zpn_broker_drain_get_drainable_conn_count
 *  gets the number of customer connections that can be drained, by iterating over all threads and get stats
 */
struct zpn_broker_drain_conn_stats
zpn_broker_drain_get_drainable_conn_count(int64_t customer_gid)
{
    /* iterate over cgid hash and display contents */
    struct zpn_broker_cgid_to_cstate_per_thread *cgid_cstate = &(broker.cgid_to_cstate_per_thread);
    struct zpn_broker_connected_customer_clients* gid_client_list;
    struct zpn_broker_connected_clients *connected_clients = &(broker.clients);
    struct zpn_broker_drain_conn_stats stats;
    memset(&stats, 0, sizeof(struct zpn_broker_drain_conn_stats));

    int max_fohh_thread = fohh_thread_count();

    pthread_mutex_lock(&(connected_clients->lock));
    for (int curr_fohh_thread = 0; curr_fohh_thread < max_fohh_thread; curr_fohh_thread++) {
        gid_client_list = zhash_table_lookup(cgid_cstate->customer_gid[curr_fohh_thread], &customer_gid, sizeof(customer_gid), NULL);
        if (gid_client_list == NULL) {
            continue;
        }
        /* accumulate across all threads */
        stats.drainable_conn_count += gid_client_list->drainable_conn_count;

        /* total conn. count by accumulating over all client types */
        int64_t total_count = 0;
        for (int i = 1; i < zpn_client_type_total_count; i++) {
            total_count += __sync_fetch_and_add_8(&gid_client_list->all_client_type_stats[i], 0);
        }
        stats.total_conn_count += total_count;
    }
    pthread_mutex_unlock(&(connected_clients->lock));

    ZPN_DEBUG_BROKER_DRAIN_CONN("drain_conn: Customer_gid: %"PRId64"  Connection: drainable / total:  %5"PRId64" / %5"PRId64" ",
                                customer_gid, stats.drainable_conn_count, stats.total_conn_count);

    return stats;
}

/*
 * zpn_broker_update_passive_stats_customer_gid_entry
 *  If conn. is passive, increment the passive stats in customer_gid client list.
 *  Also, a passive conn. is not drainable, so decrement the drainable count,
 */
void zpn_broker_update_passive_stats_customer_gid_entry(struct zpn_broker_client_fohh_state *c_state, int is_increment)
{
    if (!c_state) {
        ZPN_LOG(AL_ERROR, "NULL c_state : Error updating passive c_state to "
                          "hash table zpn_broker_cgid_to_cstate_per_thread");
        return;
    }
    int thread_id = c_state->conn_thread_id;
    if (thread_id < 0 || thread_id >= FOHH_MAX_THREADS) {
        ZPN_LOG(AL_CRITICAL, "drain_conn: c_state fohh thread is out-of-range in incr passive stats");
        return;
    }

    struct zpn_broker_connected_customer_clients* gid_client_list;
    struct zpn_broker_cgid_to_cstate_per_thread *cgid_cstate = &(broker.cgid_to_cstate_per_thread);

    gid_client_list = zhash_table_lookup(cgid_cstate->customer_gid[thread_id],
                                         &(c_state->customer_gid),
                                         sizeof(c_state->customer_gid), NULL);
    if (NULL == gid_client_list) {
        ZPN_LOG(AL_ERROR, "Could not retrieve gid client list from  cgid_cstate hash at thread: %d for customer %"PRId64" ",
                            thread_id, c_state->customer_gid);
        return;
    }

    gid_client_list->all_client_type_passive_stats[c_state->client_type] += (is_increment != 0) ? 1 : -1;

    /* decrement drainable count, if it went to passive, else increment. */
    if (zpn_broker_is_drainable_conn(c_state)) {
        gid_client_list->drainable_conn_count += (is_increment != 0) ? -1 : 1;
    }
}

/*
 * zpn_broker_is_drainable_conn
 *  Checks if a conn. adheres to drainable category,then update stats; Used in dry_run statistics.
 *  NOTE: passive; active and switching is dealt with separately
 */
int zpn_broker_is_drainable_conn(struct zpn_broker_client_fohh_state *c_state)
{
    if (!c_state) {
        return 0;
    }
    /* client type does not support drain */
    if (!zpn_client_static_config[c_state->client_type].drain_conn_support) {
        return 0;
    }

    return 1;
}

/*
 * zpn_broker_is_drain_scheduled_for_customer
 *  used when we have a new conn created while a drain has been scheduled for the customer.
 *  Pass back the drain type as well.
 */
int zpn_broker_is_drain_scheduled_for_customer(int64_t customer_gid,
                                               enum zpn_conn_drain_type *type_p)
{
    if (!type_p)
        return 0;

    /* check if manual drain is scheduled */
    if (zpn_broker_is_manual_drain_scheduled(customer_gid)) {
        *type_p = drain_type_manual;
        return 1;
    }

    /* checks if maintenance has initiated a redirect */
    if (zpn_broker_is_maintenance_redirects_initiated()) {
        *type_p = drain_type_maintenance;
        return 1;
    }

    /* default */
    *type_p = drain_type_none;
    return 0;
}

/*
 * zpn_broker_get_c_state_drain_expiry_info
 *  gets drain expiry info of the c_state;
 *  returns 0 if drain has not been enabled by any drain type
 *  returns 1 if drain is enabled; get the initiator type with earliest expiry, and its expiry_s
 */
int zpn_broker_get_c_state_drain_expiry_info(const struct zpn_broker_client_fohh_state *c_state,
                                             int64_t *min_expiry_s,
                                             enum zpn_conn_drain_type *initiator_type_p)
{
    if (!c_state || !min_expiry_s || !initiator_type_p) {
        return 0;
    }

    int drain_enabled = 0;
    *min_expiry_s = 0;
    *initiator_type_p = drain_type_none;

    for (enum zpn_conn_drain_type type = drain_type_none + 1;
                                  type < drain_type_total_count; type++) {
        if (!c_state->drain_info[type].drain_enabled) {
            continue;
        }

        if ((0 == *min_expiry_s) ||
            (c_state->drain_info[type].expiry_s < *min_expiry_s)) {
            drain_enabled = 1;
            *min_expiry_s = c_state->drain_info[type].expiry_s;
            *initiator_type_p = type;
        }
    }
    return drain_enabled;
}

/*
 * zpn_broker_get_drain_initiator
 *  If drain was initiated in multiple ways, associate it with the drain that happens first
 */
enum zpn_conn_drain_type zpn_broker_get_drain_initiator(const struct zpn_broker_client_fohh_state *c_state)
{
    /* currently we only have manual drain; check for it */
    if (0 == c_state->drain_info[drain_type_manual].drain_enabled) {
        return drain_type_none;
    }
    return drain_type_manual;
}

/*
 * zpn_broker_client_drain_monitor
 *  Checks the c_state drain status and sets up a force redirect if needed.
 *  Called from client conn. monitor.
 */
void zpn_broker_client_drain_monitor(struct zpn_broker_client_fohh_state *c_state)
{
    if (!c_state)
        return;

    enum zpn_conn_drain_type initiator_type = drain_type_none;
    enum zpn_conn_drain_subtype initiator_subtype = drain_subtype_none;
    int64_t drain_expiry_s = 0;

    /* if connection is not marked for drain, bail out */
    int drain_enabled = zpn_broker_get_c_state_drain_expiry_info(c_state, &drain_expiry_s, &initiator_type);
    if (!drain_enabled) {
        return;
    }

    if (!drain_expiry_s || !zpn_conn_is_valid_drain_type(initiator_type)) {
        ZPN_LOG(AL_ERROR, "%s: drain_conn: Invalid drain initiator data identified; "
                    "(expiry: %"PRId64" drain_type: %u) return w/o redirecting",
                    c_state->tunnel_id, drain_expiry_s, initiator_type);
        return;
    }

    /* we try to ensure drain is completed before the time expires */
    int64_t delta = epoch_s() - c_state->drain_info[initiator_type].timestamp_s;
    int64_t time_now_s = epoch_s();
    if ((time_now_s + ZPN_TUNNEL_MONITOR_INTERVAL_S) > drain_expiry_s) {
        /* If its more than twice the monitor interval since expiry; mark as drain not possible */
        if (time_now_s > drain_expiry_s)  {
            int64_t time_since_expiry = time_now_s - drain_expiry_s;
            if (time_since_expiry > (2 * ZPN_TUNNEL_MONITOR_INTERVAL_S)) {
                /* state is already marked not possible; wait till max expiry before cleaning up */
                if (drain_status_not_possible == c_state->drain_info[initiator_type].drain_status) {
                    if (time_now_s > c_state->drain_info[initiator_type].max_expiry_s) {
                        zpn_broker_drain_info_cleanup(c_state, drain_status_not_possible);
                        ZPN_LOG(AL_NOTICE, "%s: drain_conn: %s/%s set drain status to none from monitor(drain_not_possible) state: %s",
                                    c_state->tunnel_id,
                                    zpn_conn_get_drain_type_string(initiator_type), zpn_conn_get_drain_subtype_string(initiator_subtype),
                                    zpn_tlv_description(c_state_get_tlv(c_state)));
                    }
                } else {
                    zpn_broker_update_drain_status(c_state, initiator_type, drain_status_not_possible, __FILE__, __LINE__);
                    ZPN_LOG(AL_NOTICE, "%s: drain_conn: %s/%s: set drain status to not_possible from monitor with reason: %s (delta: %"PRId64") c_state: %s",
                                                c_state->tunnel_id,
                                                zpn_conn_get_drain_type_string(initiator_type),
                                                zpn_conn_get_drain_subtype_string(initiator_subtype),
                                                c_state->drain_info[initiator_type].drain_reason, delta,
                                                zpn_tlv_description(c_state_get_tlv(c_state)));
                }
                /* return either ways */
                return;
            }
        }

        /* send the redirect message */
        initiator_subtype = c_state->drain_info[initiator_type].drain_subtype;
        zpn_broker_update_drain_status(c_state, initiator_type, drain_status_initiated, __FILE__, __LINE__);
        zpn_broker_client_redirect_from_another_thread(c_state->tunnel_id,
                                                    c_state->conn_thread_id,
                                                    zpn_broker_drain_is_ops_forced_redirect(initiator_type),
                                                    1,
                                                    c_state->drain_info[initiator_type].drain_reason);
        ZPN_LOG(AL_NOTICE, "%s: drain_conn: %s/%s: set drain status to initiated from monitor with reason: %s (delta: %"PRId64") c_state: %s",
                                    c_state->tunnel_id,
                                    zpn_conn_get_drain_type_string(initiator_type),
                                    zpn_conn_get_drain_subtype_string(initiator_subtype),
                                    c_state->drain_info[initiator_type].drain_reason, delta,
                                    zpn_tlv_description(c_state_get_tlv(c_state)));

    }
}

void zpn_broker_drain_info_cleanup(struct zpn_broker_client_fohh_state *c_state,
                                   enum zpn_conn_drain_status final_drain_status)
{
    if (!c_state)
        return;

    /* cleaning up drain info */
    for (enum zpn_conn_drain_type type = drain_type_none + 1; type < drain_type_total_count; type++)
    {
        /* for drain status completed/not_possible;  we need to find who initated and associate stats there */
        if ((drain_status_completed == final_drain_status) ||
            (drain_status_not_possible == final_drain_status)) {
            if (c_state->drain_initiator == type) {
                int64_t delta = epoch_s() - c_state->drain_info[type].timestamp_s;
                zpn_broker_update_drain_status(c_state, type, final_drain_status, __FILE__, __LINE__);
                ZPN_LOG(AL_NOTICE, "%s: drain_conn: %s/%s: set drain status to %s (delta: %"PRId64") for c_state: %s",
                                    c_state->tunnel_id,
                                    zpn_conn_get_drain_type_string(type),
                                    zpn_conn_get_drain_subtype_string(c_state->drain_info[type].drain_subtype),
                                    zpn_conn_get_drain_status_string(final_drain_status), delta,
                                    zpn_tlv_description(c_state_get_tlv(c_state)));
            }
        } else {
            zpn_broker_update_drain_status(c_state, type, final_drain_status, __FILE__, __LINE__);
        }

        c_state->drain_info[type].drain_enabled = 0;
        c_state->drain_info[type].expiry_s = 0;
        c_state->drain_info[type].max_expiry_s = 0;
        c_state->drain_info[type].drain_subtype = drain_subtype_none;
    }
    c_state->drain_initiator = drain_type_none;
    return;
}

/*
 * zpn_broker_process_drain_request_internal
 *  Process redirect for all conn of a given customer_gid on a given thread
 */
static void
zpn_broker_process_drain_request_internal(struct fohh_thread *f_thread,
                                          int64_t curr_fohh_thread,
                                          const struct zpn_broker_drain_request *drain_req)
{
    int thread_id = f_thread->fohh_thread_id;
    const char *thread_name = f_thread->zthread->stack.thread_name;

    if (!drain_req) {
        ZPN_LOG(AL_ERROR, "drain_conn: unable to get drain_req in process drain req on thread!! return");
        return;
    }

    struct zpn_broker_cgid_to_cstate_per_thread *cgid_cstate = &(broker.cgid_to_cstate_per_thread);
    struct zpn_broker_connected_customer_clients* gid_client_list;
    int64_t customer_gid = drain_req->customer_gid;
    enum zpn_conn_drain_type drain_type = drain_req->drain_type;

    gid_client_list = zhash_table_lookup(cgid_cstate->customer_gid[thread_id], &customer_gid, sizeof(customer_gid), NULL);

    if (gid_client_list == NULL) {
        return;
    }

    ZPN_DEBUG_BROKER_DRAIN_CONN("drain_conn: In process_drain_req ; Thread: %s[%d] customer: %"PRId64" type: %s ",
                                thread_name, thread_id, customer_gid, zpn_conn_get_drain_type_string(drain_type));

    zpn_broker_drain_mark_cstates_per_customer_on_thread(curr_fohh_thread, customer_gid, gid_client_list, drain_req);
}

static void zpn_broker_process_drain_request_on_thread(struct fohh_thread *f_thread,
                                                        void *cookie,
                                                        int64_t curr_fohh_thread)
{
    struct zpn_broker_drain_request *drain_ctx = cookie;
    zpn_broker_process_drain_request_internal(f_thread, curr_fohh_thread, drain_ctx);
    ZPN_FREE(drain_ctx);
}

static struct zpn_broker_drain_request*
zpn_broker_create_drain_context_entry(int64_t customer_gid,
                                      int64_t cookie_gid,
                                      enum zpn_conn_drain_type drain_type,
                                      enum zpn_conn_drain_subtype drain_subtype,
                                      const char *reason,
                                      int ignore_config,
                                      int64_t drain_timeout_s,
                                      uint32_t percentage)
{
    struct zpn_broker_drain_request *elem = NULL;
    elem = ZPN_CALLOC(sizeof(struct zpn_broker_drain_request));
    if (!elem) {
        ZPN_LOG(AL_CRITICAL, "drain_conn: malloc failed in drain request create");
        return NULL;
    }
    elem->customer_gid = customer_gid;
    elem->cookie_gid = cookie_gid;
    elem->drain_type = drain_type;
    elem->drain_subtype = drain_subtype;
    elem->reason = reason;
    elem->ignore_config = ignore_config;
    elem->drain_timeout_s = drain_timeout_s;
    elem->percentage = percentage;

    return elem;
}

/*
 * zpn_broker_process_drain_request
 *  Once a drain request is initiated; it will be enqueued.
 *  This routine is called on dequeueing that request -> process it and remove the request as well
 */
static void zpn_broker_process_drain_request(int64_t customer_gid, int64_t cookie_gid,
                                            enum zpn_conn_drain_type drain_type,
                                            enum zpn_conn_drain_subtype drain_subtype,
                                            const char *reason,
                                            int64_t drain_timeout_s, uint32_t percentage)
{
    int max_fohh_thread = fohh_thread_count();
    struct zpn_broker_drain_request *drain_ctx;

    for (int curr_fohh_thread = 0; curr_fohh_thread < max_fohh_thread; curr_fohh_thread++) {
        /* create a drain req context for each thread ; free on exec. */
        drain_ctx = zpn_broker_create_drain_context_entry(customer_gid, cookie_gid, drain_type, drain_subtype,
                                                reason, 1, drain_timeout_s, percentage);
        if (!drain_ctx) {   /* memory issue; exit */
            break;
        }
        int res = fohh_thread_call(curr_fohh_thread,
                                   zpn_broker_process_drain_request_on_thread,
                                   drain_ctx, curr_fohh_thread);
        if (res) {
            ZPN_LOG(AL_ERROR, "drain_conn: Failed to thread call drain_request_on_thread"
                                " on thread: %d with reason: %s", curr_fohh_thread, zpn_result_string(res));
        }
    }
}

/*** Drain process for all customers  ****/

static int zpn_broker_process_drain_customer_fn(void *cookie1, void *cookie2, void *cookie3,
                                                void *cookie4, void *cookie5,
                                                void *object, void *key, size_t key_len)
{
    if (!key || !object || !cookie1 || !cookie2) {
        ZPN_LOG(AL_ERROR, "key/object/cookie is NULL in drain_customer_fn; return");
        return 0;
    }

    const struct zpn_broker_drain_request *ctx = cookie1;
    int64_t curr_fohh_thread = *(int64_t *)cookie2;
    int64_t customer_gid = *(int64_t *)key;
    struct zpn_broker_connected_customer_clients* gid_client_list = (struct zpn_broker_connected_customer_clients*)object;

    zpn_broker_drain_mark_cstates_per_customer_on_thread(curr_fohh_thread, customer_gid, gid_client_list, ctx);
    return 0;
}

static void
zpn_broker_process_drain_request_all_customers_on_thread(struct fohh_thread *f_thread,
                                                        void *cookie,
                                                        int64_t curr_fohh_thread)
{
    int thread_id = f_thread->fohh_thread_id;
    const char *thread_name = f_thread->zthread->stack.thread_name;
    struct zpn_broker_drain_request *drain_ctx = cookie;

    const struct zpn_broker_cgid_to_cstate_per_thread *cgid_cstate = &(broker.cgid_to_cstate_per_thread);

    if (!cookie) {
        ZPN_LOG(AL_ERROR, "drain_conn: unable to get cookie in drain_request_all_customers on thread!! return");
        return;
    }

    ZPN_DEBUG_BROKER_DRAIN_CONN("drain_conn: In initiating drain_req all customers on fohh_thread: %s[%d] type: %s ",
                                thread_name, thread_id, zpn_conn_get_drain_type_string(drain_ctx->drain_type));

    /* walk through all elements in the cgid_state hash and initiate drain */
    int64_t key = 0;
    zhash_table_walk2(cgid_cstate->customer_gid[curr_fohh_thread],  &key,
                      zpn_broker_process_drain_customer_fn, cookie, &curr_fohh_thread,
                      NULL, NULL, NULL);
    ZPN_FREE(drain_ctx);
}

void
zpn_broker_process_drain_request_all_customers(enum zpn_conn_drain_type drain_type,
                                               enum zpn_conn_drain_subtype drain_subtype,
                                               const char *reason, int ignore_config,
                                               int64_t drain_timeout_s, uint32_t percentage)
{
    int max_fohh_thread = fohh_thread_count();
    struct zpn_broker_drain_request *drain_ctx;

    for (int curr_fohh_thread = 0; curr_fohh_thread < max_fohh_thread; curr_fohh_thread++) {
        /* create a drain req context for each thread ; free on exec. */
        drain_ctx = zpn_broker_create_drain_context_entry(0, 0, drain_type, drain_subtype,
                    reason, ignore_config, drain_timeout_s, percentage);
        if (!drain_ctx) {   /* memory issue; exit */
            break;
        }
        int res = fohh_thread_call(curr_fohh_thread,
                                   zpn_broker_process_drain_request_all_customers_on_thread,
                                   drain_ctx, curr_fohh_thread);
        if (res) {
            ZPN_LOG(AL_ERROR, "drain_conn: Failed to thread call drain_request_all_customers_on_thread"
                                " on thread: %d with reason: %s", curr_fohh_thread, zpn_result_string(res));
        }
    }
}

/****************************************************
 * Drain infra ; c_state handling
 ***************************************************/

void zpn_broker_drain_mark_cstate(struct zpn_broker_client_fohh_state *c_state,
                                  enum zpn_conn_drain_type drain_type,
                                  enum zpn_conn_drain_status drain_status)
{
    if (!c_state)
        return;

    if (!zpn_client_static_config[c_state->client_type].drain_conn_support) {
        return;
    }

    int64_t delta = epoch_s() - c_state->drain_info[drain_type].timestamp_s;
    c_state->drain_info[drain_type].drain_enabled = 1;
    c_state->drain_info[drain_type].drain_subtype = zpn_conn_get_drain_subtype(drain_type);
    c_state->drain_info[drain_type].drain_reason = zpn_conn_get_drain_reason_string_for_type(drain_type);
    zpn_broker_update_drain_status(c_state, drain_type, drain_status, __FILE__, __LINE__);
    c_state->drain_info[drain_type].expiry_s = zpn_broker_drain_get_manual_drain_timer_expiry(c_state->customer_gid);
    c_state->drain_info[drain_type].max_expiry_s = zpn_broker_drain_get_manual_drain_max_timer_expiry(c_state->customer_gid);

    /* log it. */
    ZPN_LOG(AL_NOTICE, "%s: drain_conn: %s/%s: set drain status to %s via marking with reason: %s (delta: %"PRId64") for c_state: %s",
                                c_state->tunnel_id,
                                zpn_conn_get_drain_type_string(drain_type),
                                zpn_conn_get_drain_subtype_string(c_state->drain_info[drain_type].drain_subtype),
                                zpn_conn_get_drain_status_string(drain_status),
                                c_state->drain_info[drain_type].drain_reason, delta,
                                zpn_tlv_description(c_state_get_tlv(c_state)));
}

void zpn_broker_drain_unmark_cstate(struct zpn_broker_client_fohh_state *c_state,
                                    enum zpn_conn_drain_type drain_type)
{
    if (!zpn_client_static_config[c_state->client_type].drain_conn_support) {
        return;
    }

    if (c_state->drain_info[drain_type].drain_enabled) {
        ZPN_DEBUG_BROKER_DRAIN_CONN("drain_conn: unmark cstate by drain: %s:  status: %d description: %s",
                    zpn_conn_get_drain_type_string(drain_type),
                    c_state->drain_info[drain_type].drain_status, zpn_tlv_description(c_state_get_tlv(c_state)));
    }

    zpn_broker_update_drain_status(c_state, drain_type, drain_status_none, __FILE__, __LINE__);
    c_state->drain_info[drain_type].drain_enabled = 0;
    c_state->drain_info[drain_type].expiry_s = 0;
    c_state->drain_info[drain_type].max_expiry_s = 0;
    c_state->drain_info[drain_type].drain_subtype = drain_subtype_none;
    if (c_state->drain_initiator == drain_type) {
        c_state->drain_initiator = drain_type_none;
    }
}
/*
 * zpn_broker_drain_unmark_cstate_all
 *  clear cstate for drain from all drain types (manual , auto.)
 */
void zpn_broker_drain_unmark_cstate_all(struct zpn_broker_client_fohh_state *c_state)
{
    for (enum zpn_conn_drain_type type = drain_type_none + 1; type < drain_type_total_count; type++) {
        zpn_broker_drain_unmark_cstate(c_state, type);
    }
}

static inline void zpn_broker_drain_unmark_customer_cstates_internal(struct fohh_thread *thread,
                                                              int64_t customer_gid,
                                                              enum zpn_conn_drain_type drain_type)
{
    int thread_id = thread->fohh_thread_id;
    const char *thread_name = thread->zthread->stack.thread_name;

    struct zpn_broker_connected_customer_clients* gid_client_list;
    struct zpn_broker_client_fohh_state *c_state;
    struct zpn_broker_cgid_to_cstate_per_thread *cgid_cstate = &(broker.cgid_to_cstate_per_thread);

    gid_client_list = zhash_table_lookup(cgid_cstate->customer_gid[thread_id], &customer_gid, sizeof(customer_gid), NULL);

    if (!gid_client_list) {
        return;
    }

    ZPN_DEBUG_BROKER_DRAIN_CONN("drain_conn: Initiate unmark for for customer gid: %"PRId64" on thread: %s[%d]",
                             customer_gid, thread_name, thread_id);
    LIST_FOREACH(c_state, &(gid_client_list->customer_client_list), customer_gid_entry) {
        zpn_broker_drain_unmark_cstate(c_state, drain_type);
    }
}

static void zpn_broker_drain_unmark_customer_cstates_on_thread(struct fohh_thread *f_thread,
                                                               void *cookie,
                                                               int64_t current_fohh_thread)
{
    struct zpn_broker_drain_request *ctx = cookie;
    if (!ctx) {
        ZPN_LOG(AL_ERROR, "drain_conn: unable to get ctx in unmark customer cstates on thread!! return");
        return;
    }
    zpn_broker_drain_unmark_customer_cstates_internal(f_thread, ctx->customer_gid, ctx->drain_type);
    ZPN_FREE(ctx);
}

static void zpn_broker_drain_unmark_customer_cstates(int64_t customer_gid, enum zpn_conn_drain_type drain_type)
{
    int max_fohh_thread = fohh_thread_count();
    struct zpn_broker_drain_request *ctx;

    /* Iterate all threads and identify connections that need to be unmarked */
    for (int curr_fohh_thread = 0; curr_fohh_thread < max_fohh_thread; curr_fohh_thread++) {
        /* create a drain req context for each thread ; free on exec. */
        ctx = zpn_broker_create_drain_context_entry(customer_gid, 0, drain_type, drain_subtype_none, NULL, 0, 0, 0);
        if (!ctx) {   /* memory issue; exit */
            break;
        }

        int res = fohh_thread_call(curr_fohh_thread,
                                   zpn_broker_drain_unmark_customer_cstates_on_thread,
                                   ctx, curr_fohh_thread);
        if (res) {
            ZPN_LOG(AL_ERROR, "drain_conn: Failed to thread call drain_unmark_customer_cstates"
                                " on thread: %d with reason: %s", curr_fohh_thread, zpn_result_string(res));
        }
    }
}

static void zpn_broker_drain_unmark_all_cstates(enum zpn_conn_drain_type drain_type)
{
    /* run through connected clients list */
    struct zpn_broker_connected_clients* connected_clients;
    struct zpn_broker_client_fohh_state* c_state;

    connected_clients = &(broker.clients);

    pthread_mutex_lock(&(connected_clients->lock));

    LIST_FOREACH(c_state, &(connected_clients->client_list), list_entry) {
        zpn_broker_drain_unmark_cstate(c_state, drain_type);
    }
    pthread_mutex_unlock(&(connected_clients->lock));
}

/*
 * zpn_broker_drain_should_mark_cstate
 *  Given the % drain; decide if this conn. should be drained or not.
 */
int zpn_broker_drain_should_mark_cstate(uint32_t percentage)
{
    if (percentage <= 0) {
        return 0;
    } else if (percentage == 100) {
        return 1;
    }
    /* decide based on random var. */
    int decider = rand() % 100;
    return (decider <= percentage) ? 1 : 0;
}

int zpn_broker_drain_check_cstate_drain_needed(const struct zpn_broker_client_fohh_state *c_state,
                                               enum zpn_conn_drain_type drain_type)
{
    if (drain_type_manual == drain_type) {
        if (!zpn_client_static_config[c_state->client_type].drain_conn_support) {
            return 0;
        }
        /* Do not initiate drain on passive connections */
        if (zpn_client_conn_mode_passive == c_state->curr_connection_mode) {
            return 0;
        }
    } else if (drain_type_maintenance == drain_type) {
        return 1;   /* no conditions; drain 'em all */
    } else {    /* unsupported drain type; lets not drain */
        return 0;
    }

    return 1;
}

/*
 * zpn_broker_drain_mark_cstates_per_customer_on_thread
 *  for a customer; mark cstates based on drain type; and percentage
 */
static void
zpn_broker_drain_mark_cstates_per_customer_on_thread(int64_t curr_fohh_thread,
                                                     int64_t customer_gid,
                                                     struct zpn_broker_connected_customer_clients* gid_client_list,
                                                     const struct zpn_broker_drain_request *drain_req)
{
    if (!gid_client_list || !drain_req)
        return;

    struct zpn_broker_client_fohh_state *c_state;

    /* get the args */
    enum zpn_conn_drain_type drain_type = drain_req->drain_type;
    enum zpn_conn_drain_subtype drain_subtype = drain_req->drain_subtype;
    const char *reason = drain_req->reason;
    int64_t drain_timeout_s = drain_req->drain_timeout_s;
    uint32_t percentage = drain_req->percentage;

    /* if ignore_config not set; check the config, and bail out if drain not needed */
    if (!drain_req->ignore_config) {
        if (!zpn_broker_is_draining_required(customer_gid)) {
            ZPN_DEBUG_BROKER_DRAIN_CONN("drain_conn: Not initiating drain for type: %s ; customer: %"PRId64" on fohh_thread: %"PRId64" ; as target is valid",
                    zpn_conn_get_drain_type_string(drain_type), customer_gid, curr_fohh_thread);
            return;
        }
    }

    LIST_FOREACH(c_state, &(gid_client_list->customer_client_list), customer_gid_entry) {

        /* need not proceed if drain is already in progress */
        if (c_state->drain_info[drain_type].drain_enabled) {
            ZPN_LOG(AL_NOTICE, "%s: drain_conn: drain already in progress for type: %s for customer: %"PRId64"..NOT proceeding",
                                c_state->tunnel_id, zpn_conn_get_drain_type_string(drain_type), customer_gid);
            continue;
        }

        /* checks for conditions per drain type to see if drain needed */
        if (!zpn_broker_drain_check_cstate_drain_needed(c_state, drain_type)) {
            continue;
        }

        /* drain by percentage logic */
        if (!zpn_broker_drain_should_mark_cstate(percentage)) {
            continue;
        }

        /* actual marking */
        c_state->drain_info[drain_type].drain_enabled = 1;
        c_state->drain_info[drain_type].drain_subtype = drain_subtype;
        c_state->drain_info[drain_type].drain_reason = reason;

        zpn_broker_update_drain_status(c_state, drain_type, drain_status_pending,  __FILE__, __LINE__);

        c_state->drain_info[drain_type].expiry_s = epoch_s() + (rand() % drain_timeout_s);
        c_state->drain_info[drain_type].max_expiry_s = epoch_s() + drain_timeout_s;

        int64_t pending_delta = c_state->drain_info[drain_type].expiry_s - epoch_s();

        ZPN_LOG(AL_NOTICE, "%s: drain_conn: %s/%s: set drain status to pending for c_state: %s; expires in %"PRId64" sec",
                c_state->tunnel_id,
                zpn_conn_get_drain_type_string(drain_type),
                zpn_conn_get_drain_subtype_string(drain_subtype),
                zpn_tlv_description(c_state_get_tlv(c_state)),
                (pending_delta >= 0) ? pending_delta : 0);
    }
}

/****************************************************
 * Drain debug command handling
 ***************************************************/

/* Dry Run routines */
void zpn_broker_drain_customer_dry_run(struct zpath_debug_state *request_state, int64_t customer_gid)
{
    struct zpath_customer *customer = NULL;
    int res = zpath_customer_get_immediate(customer_gid, &customer);
    if (res) {
        ZPN_LOG(AL_ERROR, "drain_conn: Failed to get customer name for customer_gid:%"PRId64" error %s\n",
                                customer_gid, zpath_result_string(res));
    }
    int is_draining_required = zpn_broker_is_draining_required(customer_gid);
    struct zpn_broker_drain_conn_stats conn_stats = zpn_broker_drain_get_drainable_conn_count(customer_gid);

    ZDP("gid: %-20"PRId64" need_drain: %d   connections: [drainable/total]:  %"PRId64"/%"PRId64"   name: %s\n",
            customer_gid, is_draining_required,
            is_draining_required ? conn_stats.drainable_conn_count : 0,
            conn_stats.total_conn_count,
            customer ? customer->name : "");
}


static int conn_counter_display_entry(void *cookie1, void *cookie2, void *cookie3,
                                      void *cookie4, void *cookie5,
                                      void *object, void *key, size_t key_len)
{
    /* ensure we have all valid params */
    /* NOTE: request_state can be NULL.. which means we dont display anything here. */
    if (!key || !object || !cookie2) {
        ZPN_LOG(AL_ERROR, "drain_conn: key/cookie/object is NULL in conn counter display, return");
        return 0;
    }

    int64_t customer_gid = *(int64_t *)key;
    struct zpn_broker_drain_conn_ctr_entry *counter = object;
    struct zpath_debug_state *request_state = cookie1;
    struct zpn_broker_drain_dry_run_stats *stats = cookie2;

    if (request_state) {
        struct zpath_customer *customer = NULL;
        int res = zpath_customer_get_immediate(customer_gid, &customer);
        if (res) {
            ZPN_LOG(AL_ERROR, "drain_conn: Failed to get customer name for customer_gid:%"PRId64" error %s\n",
                    customer_gid, zpath_result_string(res));
        }

        ZDP("gid: %-20"PRId64" need_drain: %d   connections: [drainable/total]:  %"PRId64"/%"PRId64"   name: %s\n",
                customer_gid, counter->is_draining_required,
                counter->is_draining_required ? counter->drainable_conn_count : 0,
                counter->total_conn_count, customer ? customer->name : "");
    }

    stats->total_customer_count += 1;
    stats->total_conn_count += counter->total_conn_count;
    /* update drainable stats only if draining is required */
    if (counter->is_draining_required) {
        stats->drainable_customer_count += 1;
        stats->drainable_conn_count += counter->drainable_conn_count;
    }

    return 0;
}

static int dry_run_all_customers_accumulate_fn(void *cookie1, void *cookie2, void *cookie3,
                                             void *cookie4, void *cookie5,
                                             void *object, void *key, size_t key_len)
{
    /* ensure we have all valid params */
    if (!key || !object || !cookie1) {
        ZPN_LOG(AL_ERROR, "drain_conn: key/cookies/object is NULL in accumulate fn, return");
        return 0;
    }

    int64_t customer_gid = *(int64_t *)key;
    struct zpn_broker_customer_counter_hash *conn_counter = cookie1;
    struct zpn_broker_connected_customer_clients* gid_client_list = (struct zpn_broker_connected_customer_clients*)object;

    /* Lookup the entry and check if draining is required or not */
    ZPN_BROKER_WR_LOCK(conn_counter->lock);
    struct zpn_broker_drain_conn_ctr_entry *counter = NULL;
    counter = zhash_table_lookup(conn_counter->table, &customer_gid, sizeof(customer_gid), NULL);
    if (!counter) {
        counter = ZPN_CALLOC(sizeof(struct zpn_broker_drain_conn_ctr_entry));
        if (!counter) {
            ZPN_LOG(AL_CRITICAL, "drain_conn: malloc failed in counter entry alloc");
            goto cleanup_case;
        }
        counter->customer_gid = customer_gid;
        counter->total_conn_count = counter->drainable_conn_count = 0;

        counter->is_draining_required = zpn_broker_is_draining_required(customer_gid);
        zhash_table_store(conn_counter->table, &customer_gid, sizeof(customer_gid), 0, counter);
    }

    if (counter->is_draining_required) {
        counter->drainable_conn_count += gid_client_list->drainable_conn_count;
    }

    /* total conn. count by accumulating over all client types */
    int64_t total_count = 0;
    for (int i = 1; i < zpn_client_type_total_count; i++) {
        total_count += __sync_fetch_and_add_8(&gid_client_list->all_client_type_stats[i], 0);
    }
    counter->total_conn_count += total_count;

cleanup_case:
    ZPN_BROKER_UNLOCK(conn_counter->lock);
    return 0;
}

struct zpn_broker_drain_dry_run_stats
zpn_broker_drain_all_customers_dry_run(struct zpath_debug_state *request_state)
{
    struct zpn_broker_connected_clients *connected_clients = &(broker.clients);
    struct zpn_broker_cgid_to_cstate_per_thread *cgid_cstate = &(broker.cgid_to_cstate_per_thread);
    struct zpn_broker_drain_dry_run_stats stats;
    memset(&stats, 0, sizeof(struct zpn_broker_drain_dry_run_stats));

    struct zpn_broker_customer_counter_hash *conn_counter = NULL;
    conn_counter = zpn_broker_customer_list_alloc();
    if (!conn_counter) {
        return stats;
    }

    int max_fohh_thread = fohh_thread_count();

    pthread_mutex_lock(&(connected_clients->lock));
    for (int curr_fohh_thread = 0; curr_fohh_thread < max_fohh_thread; curr_fohh_thread++) {
        /* walk through all elements in the cgid_state hash and accumulate */
        int64_t key = 0;
        zhash_table_walk2(cgid_cstate->customer_gid[curr_fohh_thread], &key,
                          dry_run_all_customers_accumulate_fn, conn_counter, NULL,
                          NULL, NULL, NULL);
    }
    pthread_mutex_unlock(&(connected_clients->lock));

    /* display the contents for dry_run */
    int64_t key = 0;

    ZPN_BROKER_RD_LOCK(conn_counter->lock);
    zhash_table_walk2(conn_counter->table, &key,
                        conn_counter_display_entry, request_state, &stats, NULL, NULL, NULL);
    stats.drainable_conn_pc = stats.total_conn_count ? (int)(100 * stats.drainable_conn_count / stats.total_conn_count) : 0;
    ZPN_BROKER_UNLOCK(conn_counter->lock);
    if (request_state) {
        ZDP("--------------- SUMMARY ---------------\n");
        ZDP("%40s: %10"PRId64" \n", "Total customers", stats.total_customer_count);
        ZDP("%40s: %10"PRId64" \n", "Total drainable customers", stats.drainable_customer_count);
        ZDP("%40s: %10"PRId64" \n", "Total connection count", stats.total_conn_count);
        ZDP("%40s: %10"PRId64" \n", "Total drainable connection count", stats.drainable_conn_count);
        ZDP("%40s: %10d\n", "Drainable connection count pc", stats.drainable_conn_pc);
    }

    /* free hashtable */
    zpn_broker_customer_list_free(conn_counter);

    return stats;
}

/*
 * zpn_broker_drain_connections_manual
 *  run over all connections for customer and mark for drain
 */
static void zpn_broker_drain_connections_manual(int64_t customer_gid, int64_t drain_timeout_s, uint32_t percentage)
{
    ZPN_DEBUG_BROKER_DRAIN_CONN("drain_conn: Initiated manual drain for customer %"PRId64" with "
                                "timeout: %"PRId64" sec. for %u%% connections",
                                customer_gid, drain_timeout_s, percentage);
    zpn_broker_process_drain_request(customer_gid, 0, drain_type_manual, drain_subtype_none,
                            BRK_REDIRECT_REASON_MANUAL_DRAIN_TRIGGER, drain_timeout_s, percentage);
}

/****************************************************
 * Drain decision making functions
 ***************************************************/

static int zpn_broker_is_drain_by_constellation_required(int64_t customer_gid)
{
    int64_t *const_instance_gids;
    size_t const_instance_gid_count = ZPN_MAX_BROKERS;
    int64_t instance_gid = broker_instance_id;
    int res;
    int is_drain_action_required = 0;

    /* 1. First check if the customer has any constellations, if no constellations, drain NOT needed */
    size_t constellation_count = 0;
    size_t constellation_exclude_count = 0;
    res = zpath_constellation_get_customer_constellation_count(customer_gid, &constellation_count, &constellation_exclude_count);
    if (res != ZPATH_RESULT_NO_ERROR) {
        ZPN_LOG(AL_ERROR, "drain_conn: Failed to get constellation count for customer_gid = %" PRId64 ": %s",
                                customer_gid, zpath_result_string(res));
        return 0;    /* returns drain NOT needed. */
    }

    if (!constellation_count) {
        ZPN_DEBUG_BROKER_DRAIN_CONN("drain_conn: customer_gid: %"PRId64" has no constellations...Drain: NO", customer_gid);
        return 0;
    }

    /* 2. If customer has no instances from all constellations;  draining IS needed */
    /* malloc, as it exceeds max array use on heap - cov. */
    const_instance_gids = ZPN_CALLOC(ZPN_MAX_BROKERS * sizeof(int64_t));
    if (!const_instance_gids) {
        ZPN_LOG(AL_CRITICAL, "drain_conn: malloc failed in const_instance_gids creation");
        return 0;    /* returns drain NOT needed. */
    }

    res = zpath_constellation_get_instance_gids(customer_gid, &const_instance_gids[0], &const_instance_gid_count);
    if (res != ZPATH_RESULT_NO_ERROR) {
        ZPN_LOG(AL_ERROR, "drain_conn: Failed to get constellation instances for customer_gid = %" PRId64 ": %s",
                                customer_gid, zpath_result_string(res));
        goto cleanup;
    }

    /* If this instance is not part of customer constellations, drain */
    int is_instance_in_constellation = 0;
    for (size_t i = 0; i < const_instance_gid_count; i++) {
        if (const_instance_gids[i] == instance_gid) {
            is_instance_in_constellation = 1;
            break;
        }
    }
    if (!is_instance_in_constellation) {
        ZPN_DEBUG_BROKER_DRAIN_CONN("drain_conn: Instance not present in constellation for "
                                    "customer_gid: %"PRId64" .. Drain: YES", customer_gid);
        is_drain_action_required = 1;
        goto cleanup;
    }

    ZPN_DEBUG_BROKER_DRAIN_CONN("drain_conn: customer_gid: %"PRId64" ..Drain: NO by constellation", customer_gid);

cleanup:
    ZPN_FREE(const_instance_gids);
    return is_drain_action_required;
}

static int zpn_broker_is_drain_by_partition_required(int64_t customer_gid)
{
    int is_drain_action_required = 0;
    int64_t partition_gids[ZPATH_MAX_PARTITIONS];
    size_t count = ZPATH_MAX_PARTITIONS;
    int64_t instance_gid = broker_instance_id;
    int res;

    if (zpath_partition_is_partition_profile_active()) {
        /* Identify partition for customer */
        int64_t customer_partition_gid = zpath_partition_gid_from_customer_gid(customer_gid, NULL);
        if (!customer_partition_gid) {
            ZPN_LOG(AL_ERROR, "drain_conn: unable to find partition for customer %"PRId64" ..Drain: NO", customer_gid);
            goto cleanup;
        }

        /* check if instance in partition */
        int is_instance_in_partition = 0;
        res = zpath_partition_get_instance_partitions(instance_gid, partition_gids, &count);
        if (res) {
            ZPN_LOG(AL_ERROR, "drain_conn: Failed to get partitions for instance %"PRId64" ..Drain: NO", instance_gid);
            goto cleanup;
        }

        if (count == 0) { /* This is added as part of ET-89006 to avoid drain for broker in unpartitioned dc */
            ZPN_DEBUG_BROKER_DRAIN_CONN("drain_conn: No partitions found for instance %"PRId64" ..Drain: NO", instance_gid);
            goto cleanup;
        }

        for (size_t i = 0; i < count; i++) {
            if (partition_gids[i] == customer_partition_gid) {
                is_instance_in_partition = 1;
                break;
            }
        }
        if (!is_instance_in_partition) {
            ZPN_DEBUG_BROKER_DRAIN_CONN("drain_conn: Instance not present in partition: %"PRId64" for "
                                        "customer_gid: %"PRId64" .. Drain: YES", customer_partition_gid, customer_gid);

            is_drain_action_required = 1;
            goto cleanup;
        }
        ZPN_DEBUG_BROKER_DRAIN_CONN("drain_conn: customer_gid: %"PRId64" ..Drain: NO by partition", customer_gid);

    } else {
        ZPN_DEBUG_BROKER_DRAIN_CONN("drain_conn: customer_gid: %"PRId64" ..Drain: NO by partition"
                                    "(no partition profile present)", customer_gid);
    }
cleanup:
    return is_drain_action_required;
}

static int zpn_broker_is_draining_required(int64_t customer_gid)
{
    if (zpn_broker_is_drain_by_constellation_required(customer_gid)) {
        return 1;
    }

    /* If draining not required due to constellations, check if partitions demand a drain */
    if (zpn_broker_is_drain_by_partition_required(customer_gid)) {
        return 1;
    }
    return 0;
}

/****************************************************
 * Debug/display commands
 ***************************************************/

void zpn_broker_display_global_drain_stats(struct zpath_debug_state *request_state)
{
    for (enum zpn_conn_drain_type type = drain_type_none + 1; type < drain_type_total_count; type++) {
        ZDP("%-20s: ", zpn_conn_get_drain_type_string(type));

        struct zpn_broker_drain_stats_entry temp_total = {0};
        /* accumulate over all client types */
        for (int i = 1; i < zpn_client_type_total_count; i++) {
            temp_total.drain_pending_count += g_drain_stats[i][type].drain_pending_count;
            temp_total.drain_initiated_count += g_drain_stats[i][type].drain_initiated_count;
            temp_total.drain_not_possible_count += g_drain_stats[i][type].drain_not_possible_count;
            temp_total.drain_completed_count += g_drain_stats[i][type].drain_completed_count;
            temp_total.total_mtunnels_drained += g_drain_stats[i][type].total_mtunnels_drained;
        }

        ZDP("\t drain_pending: %"PRIu64", drain_initiated: %"PRIu64", drain_not_possible: %"PRIu64", "
              "drain_completed: %"PRIu64", mtunnels drained: %"PRIu64" \n",
                temp_total.drain_pending_count, temp_total.drain_initiated_count,
                temp_total.drain_not_possible_count, temp_total.drain_completed_count,
                temp_total.total_mtunnels_drained);
    }
    ZDP("\n");
}

static inline void zpn_broker_display_drain_stats_element(struct zpath_debug_state *request_state,
                                                          int64_t customer_gid,
                                                          const struct zpn_broker_drain_stats_table_entry *entry)
{
    struct zpath_customer *customer = NULL;
    int res = zpath_customer_get_immediate(customer_gid, &customer);
    if (res) {
         ZPN_LOG(AL_ERROR, "drain_conn: Failed to get customer name for customer_gid:%"PRId64" error %s\n",
                customer_gid, zpath_result_string(res));
    }

    ZDP("%-20"PRId64"(%s) :\n", customer_gid, customer ? customer->name : "");

    for (enum zpn_conn_drain_type type = drain_type_none + 1; type < drain_type_total_count; type++) {
        ZDP("\t%-20s: ", zpn_conn_get_drain_type_string(type));

        struct zpn_broker_drain_stats_entry temp_total = {0};
        /* accumulate over all client types */
        for (int i = 1; i < zpn_client_type_total_count; i++) {
            temp_total.drain_pending_count += entry->stats[i][type].drain_pending_count;
            temp_total.drain_initiated_count += entry->stats[i][type].drain_initiated_count;
            temp_total.drain_not_possible_count += entry->stats[i][type].drain_not_possible_count;
            temp_total.drain_completed_count += entry->stats[i][type].drain_completed_count;
            temp_total.total_mtunnels_drained += entry->stats[i][type].total_mtunnels_drained;
        }

        ZDP("\t drain_pending: %"PRIu64", drain_initiated: %"PRIu64", drain_not_possible: %"PRIu64", "
              "drain_completed: %"PRIu64", mtunnels drained: %"PRIu64" \n",
                temp_total.drain_pending_count, temp_total.drain_initiated_count,
                temp_total.drain_not_possible_count, temp_total.drain_completed_count,
                temp_total.total_mtunnels_drained);
    }
    ZDP("\n");
}

static int drain_customer_stats_hash_walk_fn(void *cookie, void *object, void *key, size_t key_len)
{
    if (!key || !object || !cookie) {
        ZPN_LOG(AL_ERROR, "drain_conn: incorrect args in stats_walk_fn ");
        return 0;
    }

    const struct zpn_broker_drain_stats_table_entry *entry = object;
    struct zpath_debug_state *request_state = cookie;
    int64_t customer_gid = *(int64_t *)key;
    zpn_broker_display_drain_stats_element(request_state, customer_gid, entry);
    return 0;
}

static int zpn_broker_drain_customer_stats_dump(struct zpath_debug_state *request_state,
                                                const char **query_values,
                                                int query_value_count,
                                                void *cookie)
{
    int64_t customer_gid = 0;

    if (query_values[0]) {
        char *end_ptr;
        customer_gid = strtoul(query_values[0], &end_ptr, 0);
        if (*end_ptr != '\0') { /* couldn't completely convert to number */
            customer_gid = zpath_domain_lookup_get_id(query_values[0], strlen(query_values[0]));
            /* unable to find a valid customer gid, error out */
            if (!customer_gid) {
                ZDP("Bad customer ID or customer domain\n");
                return ZPN_RESULT_NO_ERROR;
            }
        }
        ZDP("Customer_gid for given gid/domain str: %s is  %"PRId64"\n", query_values[0], customer_gid);
    }

    ZPN_BROKER_RD_LOCK(drain_stats_table.lock);

    if (customer_gid) {
        const struct zpn_broker_drain_stats_table_entry *entry;
        entry = zhash_table_lookup(drain_stats_table.customer_stats_hash, &customer_gid, sizeof(customer_gid), NULL);
        if (!entry) {
            ZDP("No drain stats information available for customer: %s\n", query_values[0]);
        } else {
            zpn_broker_display_drain_stats_element(request_state, customer_gid, entry);
        }
    } else {    /* all entries */
        ZDP("----Global drain Stats table----\n");
        zpn_broker_display_global_drain_stats(request_state);

        ZDP("----Customer Stats Hashtable contains %"PRIu64" entries----\n", zhash_table_get_size(drain_stats_table.customer_stats_hash));
        int64_t key = 0;
        zhash_table_walk(drain_stats_table.customer_stats_hash, &key, drain_customer_stats_hash_walk_fn, request_state);
    }

    ZPN_BROKER_UNLOCK(drain_stats_table.lock);

    return ZPN_RESULT_NO_ERROR;
}

static inline void zpn_broker_display_manual_drain_entry(struct zpath_debug_state *request_state,
                                                         const struct zpn_broker_manual_drain_entry *entry)
{
    struct zpath_customer *customer = NULL;
    int res = zpath_customer_get_immediate(entry->customer_gid, &customer);
    if (res) {
         ZPN_LOG(AL_ERROR, "drain_conn: Failed to get customer name for customer_gid:%"PRId64" error %s\n",
                        entry->customer_gid, zpath_result_string(res));
    }

    ZDP("gid: %-20"PRId64" pc: %-5u%% timeout(sec.): %-10"PRId64" time_remaining(sec.): %-10"PRId64" name: %s\n",
            entry->customer_gid, entry->percentage, entry->drain_timeout_s,
            (entry->drain_timeout_s + entry->timestamp_s - epoch_s()),
            customer ? customer->name : "");
}

static int manual_drain_outstanding_hash_walk_fn(void *cookie, void *object, void *key, size_t key_len)
{
    if (!key || !object || !cookie) {
        ZPN_LOG(AL_ERROR, "drain_conn: incorrect args in manual_drain_ walkfn ");
        return 0;
    }

    const struct zpn_broker_manual_drain_entry *entry = object;
    struct zpath_debug_state *request_state = cookie;
    /* skip the global entry(gid:0); as its already taken care */
    if (entry->customer_gid) {
        zpn_broker_display_manual_drain_entry(request_state, entry);
    }
    return 0;
}

static int zpn_broker_drain_customer_outstanding_dump(struct zpath_debug_state *request_state,
                                                const char **query_values,
                                                int query_value_count,
                                                void *cookie)
{
    int64_t customer_gid = 0;

    if (query_values[0]) {
        char *end_ptr;
        customer_gid = strtoul(query_values[0], &end_ptr, 0);
        if (*end_ptr != '\0') { /* couldn't completely convert to number */
            customer_gid = zpath_domain_lookup_get_id(query_values[0], strlen(query_values[0]));
            /* unable to find a valid customer gid, error out */
            if (!customer_gid) {
                ZDP("Bad customer ID or customer domain\n");
                return ZPN_RESULT_NO_ERROR;
            }
        }
        ZDP("Customer_gid for given gid/domain str: %s is  %"PRId64"\n", query_values[0], customer_gid);
    }

    ZPN_BROKER_RD_LOCK(manual_drain_outstanding.lock);
    if (customer_gid) {
        const struct zpn_broker_manual_drain_entry *entry;
        entry = zhash_table_lookup(manual_drain_outstanding.manual_drain_hash, &customer_gid, sizeof(customer_gid), NULL);
        if (!entry) {
            ZDP("No Manual drain request posted for customer: %s to show\n", query_values[0]);
        } else {
            zpn_broker_display_manual_drain_entry(request_state, entry);
        }
    } else {    /* all entries */
        ZDP("----Outstanding manual drain entry count: %"PRIu64" ----\n", zhash_table_get_size(manual_drain_outstanding.manual_drain_hash));

        /* display all customer entry separately, first */
        const struct zpn_broker_manual_drain_entry *global_entry;
        global_entry = zhash_table_lookup(manual_drain_outstanding.manual_drain_hash, &customer_gid, sizeof(customer_gid), NULL);
        if (global_entry) {
            ZDP("All Customers:  pc: %-5u%% timeout(sec.): %-10"PRId64" time_remaining(sec.): %-10"PRId64"\n",
                    global_entry->percentage, global_entry->drain_timeout_s,
                    (global_entry->drain_timeout_s + global_entry->timestamp_s - epoch_s()));
        }

        int64_t key = 0;
        zhash_table_walk(manual_drain_outstanding.manual_drain_hash, &key, manual_drain_outstanding_hash_walk_fn, request_state);
    }

    ZPN_BROKER_UNLOCK(manual_drain_outstanding.lock);
    return ZPN_RESULT_NO_ERROR;
}

static inline void zpn_broker_clear_manual_drain_entry(struct zpath_debug_state *request_state,
                                                       struct zpn_broker_manual_drain_entry *entry)
{
    if (entry->customer_gid) {
        ZDP("Clearing manual drain entry for customer: %"PRId64" \n", entry->customer_gid);
    } else {
        ZDP("Clearing manual drain entry for All customers \n");
    }

    int res;
    res = zhash_table_remove(manual_drain_outstanding.manual_drain_hash, &(entry->customer_gid),
                        sizeof(entry->customer_gid), entry);
    if (res) {
        ZPN_LOG(AL_ERROR, "drain_conn: error removing entry for customer_gid: %"PRId64" from manual hash ", entry->customer_gid);
        ZDP("Failed to clear manual drain entry for customer: %"PRId64"\n", entry->customer_gid);
        return;
    }

    /* free the entry */
    if (entry->manual_drain_timer) {
        event_free(entry->manual_drain_timer);
        entry->manual_drain_timer = NULL;
    }
    ZPN_FREE(entry);
    entry = NULL;
}

static int manual_drain_clear_hash_walk_fn(void *cookie, void *object, void *key, size_t key_len)
{
    if (!key || !object || !cookie) {
        ZPN_LOG(AL_ERROR, "drain_conn: incorrect args in manual_drain_clear walkfn ");
        return 0;
    }

    struct zpn_broker_manual_drain_entry *entry = object;
    struct zpath_debug_state *request_state = cookie;

    zpn_broker_clear_manual_drain_entry(request_state, entry);
    return 0;
}

static int zpn_broker_drain_customer_clear(struct zpath_debug_state *request_state,
                                           const char **query_values,
                                           int query_value_count,
                                            void *cookie)
{
    int64_t customer_gid = 0;

    if (query_values[0]) {
        char *end_ptr;
        customer_gid = strtoul(query_values[0], &end_ptr, 0);
        if (*end_ptr != '\0') { /* couldn't completely convert to number */
            customer_gid = zpath_domain_lookup_get_id(query_values[0], strlen(query_values[0]));
            /* unable to find a valid customer gid, error out */
            if (!customer_gid) {
                ZDP("Bad customer ID or customer domain\n");
                return ZPN_RESULT_NO_ERROR;
            }
        }
        ZDP("Customer_gid for given gid/domain str: %s is  %"PRId64"\n", query_values[0], customer_gid);
    }

    if (customer_gid) {
        /* unmark cstates for the customer */
        zpn_broker_drain_unmark_customer_cstates(customer_gid, drain_type_manual);

        struct zpn_broker_manual_drain_entry *entry;
        ZPN_BROKER_WR_LOCK(manual_drain_outstanding.lock);
        entry = zhash_table_lookup(manual_drain_outstanding.manual_drain_hash, &customer_gid, sizeof(customer_gid), NULL);
        if (entry) {
            zpn_broker_clear_manual_drain_entry(request_state, entry);
        } else {
            ZDP("No Manual drain request posted for customer: %s to clear\n", query_values[0]);
        }
        ZPN_BROKER_UNLOCK(manual_drain_outstanding.lock);

    } else {
        /* unmark ALL cstates; if marked */
        zpn_broker_drain_unmark_all_cstates(drain_type_manual);

        ZDP("----Outstanding manual drain entry count: %"PRIu64" ----\n", zhash_table_get_size(manual_drain_outstanding.manual_drain_hash));
        int64_t key = 0;
        ZPN_BROKER_WR_LOCK(manual_drain_outstanding.lock);
        zhash_table_walk(manual_drain_outstanding.manual_drain_hash, &key,
                            manual_drain_clear_hash_walk_fn, request_state);
        ZPN_BROKER_UNLOCK(manual_drain_outstanding.lock);
    }
    return ZPN_RESULT_NO_ERROR;
}


/*
 * zpn_broker_drain_manual_one_customer
 *  Initiating manual drain for given customer after necessary validations.
 */
int zpn_broker_drain_manual_one_customer(struct zpath_debug_state *request_state,
                                         int64_t customer_gid, int ignore_config,
                                         int64_t drain_timeout_s, uint32_t percentage)
{
    int res;
    int is_draining_required = 0;
    is_draining_required = (ignore_config != 0) ? 1 : zpn_broker_is_draining_required(customer_gid);

    /* exit, if draining is not necessary */
    if (!is_draining_required) {
        ZDP("----- Drain is not required for Customer: %"PRId64" -----\n", customer_gid);
        return ZPN_RESULT_NO_ERROR;
    }

    struct zpn_broker_drain_conn_stats conn_stats = zpn_broker_drain_get_drainable_conn_count(customer_gid);
    if (0 == conn_stats.drainable_conn_count) {
        ZDP("----- Drain is required for Customer: %"PRId64"; but not being initiated(no connections to drain); Total: %5"PRId64"-----\n",
                        customer_gid, conn_stats.total_conn_count);
        return ZPN_RESULT_NO_ERROR;
    }

    /*
     * check if we have any drain request enqueued for the same customer or global(0)
     * if yes, return, else create
     */
    struct zpn_broker_manual_drain_entry *entry = NULL;

    ZPN_BROKER_RD_LOCK(manual_drain_outstanding.lock);
    entry = zhash_table_lookup(manual_drain_outstanding.manual_drain_hash,
                                 &customer_gid, sizeof(customer_gid), NULL);
    if (entry) {
        ZDP("Manual drain request already queued for customer: %"PRId64" pc(%u%%); expiring in %"PRId64" sec.\n",
                    customer_gid, entry->percentage, entry->drain_timeout_s - (epoch_s() - entry->timestamp_s));
        ZPN_BROKER_UNLOCK(manual_drain_outstanding.lock);
        return ZPN_RESULT_NO_ERROR;
    }

    int64_t global_customer_gid = 0;
    struct zpn_broker_manual_drain_entry *global_entry = NULL;
    global_entry = zhash_table_lookup(manual_drain_outstanding.manual_drain_hash,
                                 &global_customer_gid, sizeof(global_customer_gid), NULL);
    if (global_entry) {
        ZDP("Manual drain request already queued for all customers (pc: %u%%); expiring in %"PRId64" sec.\n",
                    global_entry->percentage, global_entry->drain_timeout_s - (epoch_s() - global_entry->timestamp_s));
        ZPN_BROKER_UNLOCK(manual_drain_outstanding.lock);
        return ZPN_RESULT_NO_ERROR;
    }
    ZPN_BROKER_UNLOCK(manual_drain_outstanding.lock);

    /* create a new entry in hashtable for the customer */
    ZPN_BROKER_WR_LOCK(manual_drain_outstanding.lock);

    res = ZPN_RESULT_NO_ERROR;
    entry = zpn_broker_drain_create_manual_entry(customer_gid, drain_timeout_s, percentage, &res);
    if (!entry) {
        ZPN_BROKER_UNLOCK(manual_drain_outstanding.lock);
        return res;
    }
    zhash_table_store(manual_drain_outstanding.manual_drain_hash, &customer_gid,
                        sizeof(customer_gid), 0, entry);
    ZPN_BROKER_UNLOCK(manual_drain_outstanding.lock);

    /* run over connections and mark them up */
    ZDP("-----Initiating %s Drain for Customer_gid: %"PRId64" (Timeout: %"PRId64" sec.) (Pc: %u%%)-----\n",
            ignore_config ? "Force" : "", customer_gid, drain_timeout_s, percentage);
    zpn_broker_drain_connections_manual(customer_gid, drain_timeout_s, percentage);

    return ZPN_RESULT_NO_ERROR;
}

/*
 * zpn_broker_drain_manual_all_customers
 *  Initiating manual drain for all customers after necessary validations.
 */
int zpn_broker_drain_manual_all_customers(struct zpath_debug_state *request_state,
                                          int ignore_config, int64_t drain_timeout_s, uint32_t percentage)
{
    struct zpn_broker_manual_drain_entry *entry = NULL;
    struct zpn_broker_manual_drain_entry *global_entry = NULL;
    int64_t global_customer_gid = 0;
    int res;

    ZPN_BROKER_RD_LOCK(manual_drain_outstanding.lock);

    /* Check if manual drain is initiated for all customers already; then don't proceed */
    global_entry = zhash_table_lookup(manual_drain_outstanding.manual_drain_hash,
                                 &global_customer_gid, sizeof(global_customer_gid), NULL);
    if (global_entry) {
        ZDP("Manual drain request already queued for all customers (pc: %u%%); expiring in %"PRId64" sec.\n",
                    global_entry->percentage, global_entry->drain_timeout_s - (epoch_s() - global_entry->timestamp_s));
        ZPN_BROKER_UNLOCK(manual_drain_outstanding.lock);
        return ZPN_RESULT_NO_ERROR;
    }

    /* check if we have manual drains outstanding on any customer ; then don't proceed */
    uint64_t manual_drain_count = zhash_table_get_size(manual_drain_outstanding.manual_drain_hash);
    if (manual_drain_count) {
        ZDP("Manual drain request already queued for %"PRIu64" other customers; cannot initiate drain all.\n", manual_drain_count);
        ZPN_BROKER_UNLOCK(manual_drain_outstanding.lock);
        return ZPN_RESULT_NO_ERROR;
    }

    ZPN_BROKER_UNLOCK(manual_drain_outstanding.lock);

    /* create a new entry in hashtable for 'all' customer */
    ZPN_BROKER_WR_LOCK(manual_drain_outstanding.lock);
    res = ZPN_RESULT_NO_ERROR;
    entry = zpn_broker_drain_create_manual_entry(global_customer_gid, drain_timeout_s, percentage, &res);
    if (!entry) {
        ZPN_BROKER_UNLOCK(manual_drain_outstanding.lock);
        return res;
    }
    zhash_table_store(manual_drain_outstanding.manual_drain_hash, &global_customer_gid,
                        sizeof(global_customer_gid), 0, entry);
    ZPN_BROKER_UNLOCK(manual_drain_outstanding.lock);

    /* run over connections and mark them up */
    ZDP("-----Initiating %s Drain for all customers (Timeout: %"PRId64" sec.) (Pc: %u%%)-----\n",
            ignore_config ? "Force" : "", drain_timeout_s, percentage);
    zpn_broker_process_drain_request_all_customers(drain_type_manual, drain_subtype_none,
                        BRK_REDIRECT_REASON_MANUAL_DRAIN_TRIGGER,
                        ignore_config, drain_timeout_s, percentage);
    return res;
}

/*
 * zpn_broker_drain_customer_command
 *  debug command handler to drain connections of a customer
 */
static int zpn_broker_drain_customer_command(struct zpath_debug_state *request_state,
                                             const char **query_values,
                                             int query_value_count,
                                             void *cookie)
{
    /* Initialize to default values */
    int ignore_config = 0;
    int64_t customer_gid = 0;
    int64_t drain_timeout_s = ZPN_BROKER_MANUAL_DRAIN_TIMEOUT_DEFAULT_S;
    int tmp_pc = ZPN_BROKER_MANUAL_DRAIN_PC_DEFAULT;
    uint32_t percentage = ZPN_BROKER_MANUAL_DRAIN_PC_DEFAULT;
    int res;

    /* validation: we need either customer or percentage to be present.; safeguard against unintentional drain of all  */
    if (!query_values[0] && !query_values[3]) {
        ZDP("Invalid command; Provide atleast one of customer or percentage \n");
        return ZPN_RESULT_NO_ERROR;
    }

    /* Extract the customer value; this value is optional now; absence means all customers. */
    if (query_values[0]) {
        char *end_ptr;
        customer_gid = strtoul(query_values[0], &end_ptr, 0);
        if (*end_ptr != '\0') { /* couldn't completely convert to number */
            customer_gid = zpath_domain_lookup_get_id(query_values[0], strlen(query_values[0]));
        }
        if (!customer_gid) {
            ZDP("Bad customer ID or customer domain not provided\n");
            return ZPN_RESULT_NO_ERROR;
        }
    }

    /* force the drain or not */
    ignore_config = query_values[1] ? 1 : 0;

    /* timeout */
    if (query_values[2]) {
        drain_timeout_s = strtoul(query_values[2], NULL, 0);
        if (drain_timeout_s < ZPN_BROKER_MANUAL_DRAIN_TIMEOUT_MIN_S ||
            drain_timeout_s > ZPN_BROKER_MANUAL_DRAIN_TIMEOUT_MAX_S) {
            ZDP("Invalid drain timeout value provided.. expected Range: [1, 86400] Default: 300s.\n");
            return ZPN_RESULT_NO_ERROR;
        }
    }

    /* percentage */
    if (query_values[3]) {
        tmp_pc = atoi(query_values[3]);
        if (tmp_pc < ZPN_BROKER_MANUAL_DRAIN_PC_MIN ||
            tmp_pc > ZPN_BROKER_MANUAL_DRAIN_PC_MAX) {
            ZDP("Invalid percentage value provided.. expected Range: [1, 100] Default: 100\n");
            return ZPN_RESULT_NO_ERROR;
        }
        /* now that the value is in range, we can safely cast it */
        percentage = (uint32_t)tmp_pc;
    }

    /* Drain cannot be done on a noredirect/unconfigured mode broker */
    if ((!strcmp(g_balance_config.balance_role, BALANCE_ROLE_NO_REDIRECT) ||
         !strcmp(g_balance_config.balance_role, BALANCE_ROLE_UNCONFIGURED))) {
        ZDP("Balance Role is %s; drain is not supported!\n", g_balance_config.balance_role);
        return ZPN_RESULT_NO_ERROR;
    }

    /* show the input values */
    ZDP("Customer_gid is  %"PRId64" ignore_config: %d drain_timeout_s: %"PRId64" sec; percentage: %d \n",
                customer_gid, ignore_config, drain_timeout_s, percentage);

    if (customer_gid) { /* a specific customer */
        res = zpn_broker_drain_manual_one_customer(request_state, customer_gid, ignore_config, drain_timeout_s, percentage);
    } else {    /* all customers */
        res = zpn_broker_drain_manual_all_customers(request_state, ignore_config, drain_timeout_s, percentage);
    }
    return res;
}

/*
 * zpn_broker_drain_customer_dry_run_command
 *  debug command handler to dry run drain connections
 */
static int zpn_broker_drain_customer_dry_run_command(struct zpath_debug_state *request_state,
                                                     const char **query_values,
                                                     int query_value_count,
                                                     void *cookie)
{
    int64_t customer_gid = 0;

    /* Extract the options */
    if (query_values[0]) {
        char *end_ptr;
        customer_gid = strtoul(query_values[0], &end_ptr, 0);
        if (*end_ptr != '\0') { /* couldn't completely convert to number */
            customer_gid = zpath_domain_lookup_get_id(query_values[0], strlen(query_values[0]));
            /* unable to find a valid customer gid, error out */
            if (!customer_gid) {
                ZDP("Bad customer ID or customer domain\n");
                return ZPN_RESULT_NO_ERROR;
            }
        }
        ZDP("Customer_gid for given gid/domain str: %s is  %"PRId64"\n", query_values[0], customer_gid);
    }

    /* Drain cannot be done on a noredirect/unconfigured mode broker */
    if ((!strcmp(g_balance_config.balance_role, BALANCE_ROLE_NO_REDIRECT) ||
         !strcmp(g_balance_config.balance_role, BALANCE_ROLE_UNCONFIGURED))) {
        ZDP("Balance Role is %s; drain is not supported!\n", g_balance_config.balance_role);
        return ZPN_RESULT_NO_ERROR;
    }

    if (customer_gid) {
        zpn_broker_drain_customer_dry_run(request_state, customer_gid);
    } else {  /* all customers */
        zpn_broker_drain_all_customers_dry_run(request_state);
    }

    return ZPN_RESULT_NO_ERROR;
}

/*
 * zpn_broker_drain_customer_dry_run_summary_command
 *  A summary of drain dry run; this is to be run across all connections on broker
 *  Optionally, a json format will also be provided.
 */
static int
zpn_broker_drain_customer_dry_run_summary_command(struct zpath_debug_state *request_state,
                                                 const char **query_values,
                                                 int query_value_count,
                                                 void *cookie)
{
    char out_buf[4096];

    struct zpn_broker_drain_dry_run_stats stats;
    memset(&stats, 0, sizeof(struct zpn_broker_drain_dry_run_stats));

    /* Drain cannot be done on a noredirect/unconfigured mode broker */
    if ((!strcmp(g_balance_config.balance_role, BALANCE_ROLE_NO_REDIRECT) ||
         !strcmp(g_balance_config.balance_role, BALANCE_ROLE_UNCONFIGURED))) {
        memset(&stats, 0, sizeof(struct zpn_broker_drain_dry_run_stats));
    } else {
        stats = zpn_broker_drain_all_customers_dry_run(NULL);
    }

    if (query_values[0] && !strcasecmp(query_values[0], "json")) {
        int res = argo_structure_dump(zpn_broker_drain_dry_run_stats_desc,
                                        &stats,
                                        out_buf, sizeof(out_buf),
                                        NULL, 1);
        if (res != ARGO_RESULT_NO_ERROR) {
            snprintf(out_buf, strlen(out_buf), "{Output: \"error generating dry_run summary\"}");
        }
        ZDP("%s\n", out_buf);
    } else {
        ZDP("------- Drain dry run summary ---------------\n");
        ZDP("%40s: %10"PRId64" \n", "Total customers", stats.total_customer_count);
        ZDP("%40s: %10"PRId64" \n", "Total drainable customers", stats.drainable_customer_count);
        ZDP("%40s: %10"PRId64" \n", "Total connection count", stats.total_conn_count);
        ZDP("%40s: %10"PRId64" \n", "Total drainable connection count", stats.drainable_conn_count);
        ZDP("%40s: %10d\n", "Drainable connection count pc", stats.drainable_conn_pc);
    }
    return ZPN_RESULT_NO_ERROR;
}

/****************************************************
 * Initialization routines
 ***************************************************/

/*
 * zpn_broker_drain_init
 *  Initialize broker drain infra; which includes the linked list, hashtable and timers
 */
int zpn_broker_drain_init(void)
{
    /* stats init */
    drain_stats_table.lock = ZPATH_RWLOCK_INIT;
    drain_stats_table.customer_stats_hash = zhash_table_alloc(&zhash_table_allocator);

    /* manual drain information */
    manual_drain_outstanding.lock = ZPATH_RWLOCK_INIT;
    manual_drain_outstanding.manual_drain_hash = zhash_table_alloc(&zhash_table_allocator);

    zpn_broker_drain_dry_run_stats_desc = argo_register_global_structure(ZPN_BROKER_DRAIN_DRY_RUN_STATS_HELPER);
    if (!zpn_broker_drain_dry_run_stats_desc) {
        ZPN_LOG(AL_ERROR, "drain_conn: Cannot argo_register_global_structure zpn_broker_drain_dry_run_stats_desc");
        return ZPN_RESULT_ERR;
    }

    /* add debug commands */
    int res;

    res = zpath_debug_add_admin_command("Drain connections for customer gid",
                                        "/zpn/broker/client/drain",
                                        zpn_broker_drain_customer_command,
                                        NULL,
                                        "customer", "<optional> GID/domain_name of the customer for drain",
                                        "ignore_config",    "<optional> forcefully remove connections; regardless of config",
                                        "drain_timeout_s",  "<optional> max seconds for mTunnels to drain. Acceptable: [1, 86400], default: 300",
                                        "percentage", "<optional> percentage of connections to drain. Acceptable: [1, 100], default: 100",
                                        NULL);
    if (res)    return res;

    res = zpath_debug_add_read_command("dry_run drain connections logic",
                                        "/zpn/broker/client/drain/dry_run",
                                        zpn_broker_drain_customer_dry_run_command,
                                        NULL,
                                        "customer", "<optional>, GID/domain_name of the customer for drain",
                                        NULL);
    if (res)    return res;

    res = zpath_debug_add_read_command("dry_run drain connections summary",
                                        "/zpn/broker/client/drain/dry_run/summary",
                                        zpn_broker_drain_customer_dry_run_summary_command,
                                        NULL,
                                        "format",  "<optional> format=json, or plain text otherwise",
                                        NULL);
    if (res)    return res;

    res = zpath_debug_add_read_command("Dump drain information for customer gid",
                                        "/zpn/broker/client/drain/stats",
                                        zpn_broker_drain_customer_stats_dump,
                                        NULL,
                                        "customer", "<optional>, GID/domain_name of the customer",
                                        NULL);
    if (res)    return res;

    res = zpath_debug_add_read_command("Dump drain information for customer gid",
                                        "/zpn/broker/client/drain/dump",
                                        zpn_broker_drain_customer_outstanding_dump,
                                        NULL,
                                        "customer", "<optional>, GID/domain_name of the customer",
                                        NULL);
    if (res)    return res;

    res = zpath_debug_add_admin_command("Clear manual drain scheduled for customer gid",
                                        "/zpn/broker/client/drain/clear",
                                        zpn_broker_drain_customer_clear,
                                        NULL,
                                        "customer", "<optional>, GID/domain_name of the customer",
                                        NULL);
    if (res)    return res;

    /* argo registration for stats */
    zpn_broker_drain_stats_description = argo_register_global_structure(ZPN_BROKER_DRAIN_STATS_HELPER);
    if (!zpn_broker_drain_stats_description) {
        ZPN_LOG(AL_ERROR, "drain_conn: Could not register zpn_broker_drain_stats_description");
        return ZPN_RESULT_ERR;
    }

    argo_log_register_structure(argo_log_get("statistics_log"),
                                "zpn_client_type_all",
                                AL_INFO,
                                60*1000*1000, /* 1 min interval */
                                zpn_broker_drain_stats_description,
                                &drain_stats_all,
                                1,
                                zpn_broker_drain_stats_fill_all,
                                NULL);

    return ZPN_RESULT_NO_ERROR;
}
