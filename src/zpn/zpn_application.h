/*
 * zpn_application.h. Copyright (C) 2014 Zscaler Inc. All Rights Reserved
 */

/*
 * Definitions pertaining to ZPN applications.
 */

#ifndef _ZPN_APPLICATION_H_
#define _ZPN_APPLICATION_H_

#include "wally/wally.h"
#include "zpath_lib/zpath_match_style.h"
#include "zpath_lib/zpath_lib.h"

#define ZPN_APPLICATION_MAX_PORTS 10
#define ZPN_APPLICATION_DOMAIN_DEBUG_STR_LEN 200

#if 0

/*
 * ZIA Inspection 1.0:
 * Generic Macro for using the inspection flag correctly - along with the sipa flag
 * This macro will take in client type, configuration, and output the correct mutually exclusive flags
 * which can be used by the caller for communicating with clients
 *
 * Currently, there are 2 ways we inspect traffic, however multiple clients may end up inspecting traffic
 * As such we should be able to figure out if the client supports inspection and set its flag
 * Since inspected apps are also marked as 'SIPA' to force ZCC forwarding to SME we will also
 * need to toggle that flag based on inspection setting and client type support.
 * ip anchoring and inspection flags are mutually exclusive so if one is on, the other must be off
 * They can both be off.
 *
 * This is macro'd because the logic needs to work on both unsigned and signed flags and this is
 * the easiest way without duplicating logic
 */
#define ZPN_APPLICATION_SET_INSPECTION_FLAGS(client_type, configured_sipa, configured_inspected, sipa_flag, inspected_flag) \
do {                                                                                                                        \
    if (zpn_client_static_config[ (client_type) ].zia_inspection) {                                                \
        if ((inspected_flag)) break;                                                                                        \
        if ((configured_inspected)) {                                                                                       \
            (sipa_flag) = 0;                                                                                                \
            (inspected_flag) = 1;                                                                                           \
        } else if ((configured_sipa)) {                                                                                     \
            (sipa_flag) = 1;                                                                                                \
        }                                                                                                                   \
    } else {                                                                                                                \
        (inspected_flag) = 0;                                                                                               \
        if ((configured_sipa)) {                                                                                            \
            (sipa_flag) = 1;                                                                                                \
        }                                                                                                                   \
    }                                                                                                                       \
}while(0);
#else
/*
 * ZIA Inspection 3.0
 * Apps marked for inspection, are sent to SME ( on SIPA connection).
 * The ip anchoring flag is not set, unlike in ZIA 1.0
 */
#define ZPN_APPLICATION_SET_INSPECTION_FLAGS(configured_sipa, configured_inspected, sipa_flag, inspected_flag) \
    do {                                                                                                       \
        if ((configured_sipa)) {                                                                               \
            (sipa_flag) = 1;                                                                                   \
        }                                                                                                      \
        if ((configured_inspected)) {                                                                          \
            (inspected_flag) = 1;                                                                              \
        }                                                                                                      \
    } while (0);
#endif
#define RESULTS_ARRAY_SIZE 10

#define PATTERN_DOMAIN_SEARCH_ENABLED       1
#define PATTERN_DOMAIN_SEARCH_DISABLED      0

enum zpn_client_type;

extern struct zpath_allocator zpn_app_allocator;
#define ZPN_APP_MALLOC(x)       zpath_malloc(&zpn_app_allocator, x, __LINE__, __FILE__)
#define ZPN_APP_FREE(x)         zpath_free(x, __LINE__, __FILE__)
#define ZPN_APP_FREE_SLOW(x)    zpath_free_slow(x, __LINE__, __FILE__)
#define ZPN_APP_CALLOC(x)       zpath_calloc(&zpn_app_allocator, x, __LINE__, __FILE__)
#define ZPN_APP_STRDUP(x, y)    zpath_strdup(&zpn_app_allocator, x, y, __LINE__, __FILE__)
#define ZPN_APP_REALLOC(x, y)   zpath_realloc(&zpn_app_allocator, x, y, __LINE__, __FILE__)

enum zpn_tld_action {
    zpn_tld_add = 0,
    zpn_tld_remove
};

struct zpn_application {                       /* _ARGO: object_definition */
    /* Standard SQL fields. */
    int64_t sequence;                          /* _ARGO: integer, sequence */
    int32_t modified_time;                     /* _ARGO: integer */
    int32_t creation_time;                     /* _ARGO: integer */
    int64_t modifiedby_userid;                 /* _ARGO: integer */
    int8_t deleted;                            /* _ARGO: integer, deleted */
    int64_t request_id;                        /* _ARGO: integer, nodb, reqid */

    /* The unique index for this application. */
    int64_t gid;                               /* _ARGO: integer, index, key */

    /* The customer to which this application belongs. */
    int64_t customer_gid;                      /* _ARGO: integer, index */

    /* Domain name reference for the application. This is how an
     * application is really identified/indexed.
     *
     * NOTE: domain_name EXISTS FOR BACKWARDS COMPATIBILITY ONLY. NO
     * NEW CODE SHOULD RELY ON THIS VALUE. Use domain_names instead */
    char *domain_name;                         /* _ARGO: string, index */

    /* The name of the application; friendly */
    char *name;                                /* _ARGO: string */

    /* Whether or not this application is currently enabled. */
    int8_t enabled;                            /* _ARGO: integer */
    int8_t bypass;                             /* _ARGO: integer */
    char *bypass_type;                         /* _ARGO: string */
    /* passive_health_enabled is TRUE, when the Health Reporting in Application Segments config is "On Access" */
    int8_t passive_health_enabled;             /* _ARGO: integer */

    /* Description of the application. */
    char *description;                         /* _ARGO: string */

    /* What TCP ports comprise this application. tcp_ports represent
     * ports on client side, tcp_ports_out represent ports server
     * side., on the application-server side. These should be 1:1 with
     * each other, and represent port translation. */
    int *tcp_ports_in;                         /* _ARGO: integer */
    int tcp_ports_in_count;                    /* _ARGO: quiet, integer, count: tcp_ports_in */
    int *tcp_ports_out;                        /* _ARGO: integer */
    int tcp_ports_out_count;                   /* _ARGO: quiet, integer, count: tcp_ports_out */

    int *tcp_port_ranges;                      /* _ARGO: integer */
    int tcp_port_ranges_count;                 /* _ARGO: quiet, integer, count: tcp_port_ranges */
    int *tcp_protocols_bitmasks;               /* _ARGO: integer */
    int tcp_protocols_bitmasks_count;          /* _ARGO: quiet, integer, count: tcp_protocols_bitmasks */

    /* Idle timeout, in seconds */
    int default_idle_timeout;                  /* _ARGO: integer */

    /* Max age, min seconds. */
    int default_max_age;                       /* _ARGO: integer */

    char **domain_names;                       /* _ARGO: string */
    int domain_names_count;                    /* _ARGO: quiet, integer, count: domain_names */

    /* Should there be configuration for logging information? */

    int8_t disable_inheritance;                /* _ARGO: integer */
    int8_t double_encrypt;                     /* _ARGO: integer */
    uint8_t connector_close_to_app;            /* _ARGO: integer */

    int *udp_port_ranges;                      /* _ARGO: integer */
    int udp_port_ranges_count;                 /* _ARGO: quiet, integer, count: udp_port_ranges */
    int *udp_protocols_bitmasks;               /* _ARGO: integer */
    int udp_protocols_bitmasks_count;          /* _ARGO: quiet, integer, count: udp_protocols_bitmasks */

    char *health_check_type;                   /* _ARGO: string */

    // icmp_access_type - "PING_TRACEROUTING", "PING", "NONE"
    char *icmp_access_type;                    /* _ARGO: string */

    char *cname_config;                        /* _ARGO: string */

    int fqdn_dns_check;                      /* _ARGO: integer */

    char *config_space;                        /* _ARGO: string */

    char *policy_style;                        /* _ARGO: string */

    int ip_anchored;                           /* _ARGO: integer */
    int inspect_traffic_with_zia;              /* _ARGO: integer */

    int bypass_on_reauth;                      /* _ARGO: integer */

    int64_t scope_gid;                               /* _ARGO: integer, index */
    int64_t *shared_scope_ids;                       /* _ARGO: integer */
    int shared_scope_ids_count;                      /* _ARGO: quiet, integer, count: shared_scope_ids */
    int64_t *shared_scope_ids_from_app_group;        /* _ARGO: integer */
    int shared_scope_ids_from_app_group_count;       /* _ARGO: quiet, integer, count: shared_scope_ids_from_app_group */

    /* match_style -> m_style on row RX */
    enum zpn_match_style m_style;

    char domain_name_debug_str[ZPN_APPLICATION_DOMAIN_DEBUG_STR_LEN];

    char **log_features;                       /* _ARGO: string */
    int log_features_count;                    /* _ARGO: quiet, integer, count: log_features */
    int8_t tcp_keepalive;                      /* _ARGO: integer */


    /* App is enabled for use in DR mode */
    int use_in_dr_mode;                        /* _ARGO: integer */

    char *match_style;                         /* _ARGO: string */

    unsigned domains_contains_subnet:1;
    unsigned domains_contains_wildcard:1;
    unsigned cname_flatten:1;

    int adp_enabled;                           /* _ARGO: integer */
    int auto_app_protect_enabled;              /* _ARGO: integer */
    int api_protection_enabled;                /* _ARGO: integer */
    int extranet_enabled;                      /* _ARGO: integer */
    // partner_id
    int64_t zpn_er_id;                         /* _ARGO: integer */

};

enum zpn_app_search_type {
    zpn_app_search_none = 0,
    zpn_mtunnel_search,
    zpn_app_download_search,
    zpn_app_scale_search,
    zpn_dns_check_search,
    zpn_exporter_search,
    zpn_pbroker_conn_search
};

enum zpn_app_search_debug_stats_type {
    zpn_app_not_found = 0,      //app_gid returned 0
    zpn_application_not_found,  // app_gid present, but zpn_application table does not have data for the app_gid
    zpn_dns_query_failed,       // broker dns query request returned failure
    zpn_dns_query_not_found,    // broker dns query returned result not found
    zpn_dns_check_err_not_found, // broker dns response not found, sending DNS check error to requester
    zpn_dns_check_err_strict_failed, // broker dns query response failed due to strict checking for A or AAAA
    zpn_fill_app_check_fdqn_failed, //app scaling fqdn fill app check failed
    zpn_fill_app_check_wc_failed, //app scaling wildcard app fill app check failed
    zpn_diamond_domain_not_found, // app search diamond does not contain domain
    zpn_ip_lookup_not_found, // ip address not present in radix memory
    zpn_specific_and_matched_app_differ, //most specific app gid chosen different from the matched app based on domain and port
    zpn_diamond_add_failed, // application add failed
    zpn_diamond_remove_failed, // Application remove failed
    zpn_app_add_pattern_not_supported,  // Adding unsupported pattern
    zpn_app_remove_pattern_not_supported,  // Removing unsupported pattern
    zpn_ipv4_add_failed, // Adding ipv4 address failed
    zpn_ipv6_add_failed, // Adding ipv6 address failed
    zpn_ipv4_remove_failed, // Removing ipv4 address failed
    zpn_ipv6_remove_failed, // Removing ipv6 address failed
    zpn_pattern_apps_skipped, // Skip app download for pattern apps
    zpn_multi_match_large_domains_matched, // # domains matched >= 10
    zpn_multi_match_large_ips_matched, // # ip's matched >= 10
    zpn_multi_match_large_apps, // Multi match matched >= 10 domain
    zpn_catch_all_ip_apps_skipped, // Skip app download for catch all ip apps
    zpn_app_overlapping_ports // Skip app download for catch all ip apps
};

extern struct argo_structure_description *zpn_application_description;

extern struct wally_index_column **zpn_application_customer_gid_column;

/* Reference to the state of a customer's applications. Can be used to
 * quickly get application state version */
struct zpn_customer_application_state;

extern int g_app_debug_log;
/*
 * Initialize ZPN application apis...
 *
 * Must be called after zpath_app_init
 *
 * set single_tenant_wally + tenant_id only if single tenant.
 * set tenant_is_broker for supported components, for config override monitor
 */
int zpn_application_init(struct wally *single_tenant_wally, int64_t tenant_id, int tenant_is_broker_or_exporter, int single_tenant_fully_loaded, int register_with_zpath_table, int tenant_is_broker);

int zpn_application_init_1(struct wally *single_tenant_wally, int64_t tenant_id, int tenant_is_broker_or_exporter, int single_tenant_fully_loaded, int register_with_zpath_table, int unit_test);

/*
 * Get an appliation give an application ID.
 *
 * Verifies that the retrieved application ID belongs to the same
 * customer as the customer ID. (Duh)
 *
 * app is returned only on ZPN_RESULT_NO_ERROR response.
 */
int zpn_application_get_by_id(int64_t app_id,
                              struct zpn_application **app,
                              wally_response_callback_f callback_f,
                              void *callback_cookie,
                              int64_t callback_id);

/*
 * Get all applications for a given customer. This only returns what
 * is in memory.
 */
int zpn_application_get_by_customer_gid_immediate(int64_t customer_gid,
                                        struct zpn_application **apps,
                                        size_t *app_count);

int zpn_application_get_apps_by_customer_gid(int64_t customer_gid,
                                             int64_t *app_gids,
                                             size_t *app_count,
                                             wally_response_callback_f callback_f,
                                             void *callback_cookie,
                                             int64_t callback_id);

/*
 * Get an appliation give an application ID.
 *
 * Returns in-memory result, or failure.
 *
 * app is returned only on ZPN_RESULT_NO_ERROR response.
 */
int zpn_application_get_by_id_immediate(int64_t app_id,
                                        struct zpn_application **app);

/*
 * Check if an application matching the given domain name (or IP
 * address!) exists.
 *
 * This system presumes that that something has already caused the
 * system to fetch the applications being searched for. (This is
 * always the case for the broker). Otherwise a
 * zpn_application_register_customer might be needed.
 *
 * If the application exists, the actual application name that was
 * matched is returned in matched_domain, which must be large enough
 * for the result.
 *
 * Also lets you know if the application was a wildcard match or
 * not... (is_wildcard is optional) (In this case, domain_name and
 * matched_domain will not match...)
 *
 * matched_domain may be NULL if the caller does not need that info
 *
 * Returns ZPN_RESULT_NO_ERROR if there is a match
 * Returns ZPN_RESULT_NOT_FOUND if there is not a match
 *
 * NOTE: Some functionality that used to be provided by this routine
 * has been moved to zpn_application_domain
 */
int zpn_application_search_by_customer(int64_t customer_gid,
                                       const char *domain_name,
                                       size_t domain_name_len,
                                       char *matched_domain,
                                       size_t matched_domain_len,
                                       int *is_wildcard);
int zpn_application_search_by_scope(int64_t scope_gid,
                                    const char *domain_name,
                                    size_t domain_name_len,
                                    char *matched_domain,
                                    size_t matched_domain_len,
                                    int *is_wildcard);

int zpn_application_search_by_customer_with_filter(int64_t customer_gid,
                                       const char *domain_name,
                                       size_t domain_name_len,
                                       char *matched_domain,
                                       size_t matched_domain_len,
                                       int *is_wildcard,
                                       int skip_non_sipa_apps);
int zpn_application_search_by_scope_with_filter(int64_t scope_gid,
                                    const char *domain_name,
                                    size_t domain_name_len,
                                    char *matched_domain,
                                    size_t matched_domain_len,
                                    int *is_wildcard,
                                    int skip_non_sipa_apps);

/*
 * Get all the applications and application groups matching the given
 * domain name (or IP address!) + port + protocol
 *
 * zpn_application_register_customer() should have been called
 * sometime in the past
 *
 * NOTE: Some functionality that used to be provided by this routine
 * has been moved to zpn_application_domain
 *
 * The process here is:
 *
 * 1. Get all domains (or IPs) matching the requested domain_name
 *    (which if an IP must be text form)
 *
 * 2. For each domain, get all applications matching that domain.
 *
 * 3. For each application matching the domain, check if the ports match.
 *
 * 4. If there is a match, check for an application_group for the application.
 *
 * 5. Fill in one result of app_id + app_grp_id + is_wildcard
 *
 * 6. If at least one match, return async, no_error, else not_found.
 *
 * Returns ZPN_RESULT_ASYNCHRONOUS if we needed something from wally
 * Returns ZPN_RESULT_NO_ERROR if there is a match
 * Returns ZPN_RESULT_NOT_FOUND if there is not a match
 *
 */
int zpn_application_search_all(int64_t scope_gid,                     // Scope
                               const char *app_name,                  // SEARCH: this app
                               size_t app_name_len,                   //              len
                               int ip_protocol,                       //              proto
                               int port,                              //              port
                               int64_t limit_assistant_gid,           //              If set, limit to apps using this connector
                               int *is_inclusive,                     // if the first app is inclusive ?
                               int64_t *app_gids,                     // RETURN: app_gids. In order of most specific match to least specific match.
                               size_t *app_gids_count,                //         number of app_gids + wildcard matched
                               int64_t *app_group_gids,               //         app_group_gids, accumulated from app_gids in order.
                               size_t *app_group_gids_count,          //         number of app_group_gids matched (can be different form app_gids!!)
                               int *app_gid_wildcard,                 //         whether or not the most specific domain matched is a wildcard. (NOT app matched!!)
                               int64_t *most_specific_app_gid,        //         most specific matched app_gid ignoring port and protocol
                               wally_response_callback_f *callback_f, // CALLBACK: if it goes async
                               void *callback_cookie,                 //           cookie void
                               int64_t callback_id);                  //           cookie int

int zpn_inclusive_domains_search(int64_t    scope_gid,
                                 const char *app_name,
                                 char       **domains,       /* declare as struct char *domains[N] */
                                 size_t     *domains_count, /* must input as N, and reurn actual count */
                                 int        search_pattern_domain);
/* return match_count */
int zpn_domain_search_all(int64_t scope_gid,
                          const char *app_name,
                          size_t app_name_len,
                          int return_inclusive_only,      // if set, only return inclusive domains
                          int *is_inclusive,
                          void **domain_lookup_results,   // void *[N]
                          int *domain_lookup_wildcard,    // int domain_lookup_wildcard[N]
                          size_t domain_lookup_count,    // N
                          int search_pattern_domain);

void zpn_enable_app_multi_match_feature_test();
size_t zpn_get_app_gids_for_all_domains(int64_t customer_gid,
                                        const char *app_name,
                                        void **domain_lookup_results,
                                        size_t domain_lookup_results_count,
                                        int64_t *tmp_app_gids,
                                        size_t tmp_app_gids_count,
                                        int *exact_match_app_gids_count);

/*
 * Make sure we get the applications for a specific customer. This is
 * generally asynchronous, and it doesn't matter. Often happens when a
 * client connects to a broker...
 */
int zpn_application_customer_register(int64_t                       customer_gid,
                                      wally_response_callback_f     *callback,
                                      void                          *void_cookie,
                                      int64_t                       int_cookie);
/*
 * Wrapper for zpn_application_customer_register with application_match flag to
 * prevent feature toggle callback for exporter.
 * We are supporting app_match only with broker components.
 */
int zpn_application_customer_register_with_app_match(int64_t      customer_gid,
                                      wally_response_callback_f     *callback,
                                      void                          *void_cookie,
                                      int64_t                       int_cookie,
                                      int                           is_app_match); // pass 1 if we need to have application_match feature
/*
 * Get port translation for an application. Returns 0 if port invalid.
 */
int zpn_application_translate_port(struct zpn_application *app, int port, uint16_t ip_proto);
int zpn_application_translate_port_protocol(struct zpn_application *app, int port, uint16_t ip_proto, int *protocol_bitmask);
int64_t zpn_application_app_multi_match_feature_enabled_get(const struct zpn_customer_application_state *state);
int64_t is_app_multi_match_feature_enabled(int64_t customer_gid);

void zpn_app_multi_match_global_config_ovd_init();
int64_t is_app_multi_match_feature_hard_disabled();

/*
 * Manage the tree of domains for application lookup.
 */
int zpn_customer_application_add_or_replace(int64_t customer_gid,
                                            const char *domain_name,
                                            size_t domain_name_len);
int zpn_customer_application_remove(int64_t customer_gid,
                                    const char *domain_name,
                                    size_t domain_name_len);

/*
 * Manage TLD+1 domains
 */
int zpn_customer_tld_1_update(int64_t customer_gid,
                              const char *domain_name,
                              enum zpn_tld_action action);
/* The caller subject to free tld_1 using zpn_free_tld_1() with shallow=0*/
int zpn_get_tld_1_by_gid(int64_t customer_gid, char ***z_tld_1, int *sz);
void zpn_free_tld_1(char **tld_1, size_t sz, int shallow);

/*
 * Adds an element into an array, without maintaining order, and not inserting duplicates
 *
 * DOES NOT VERIFY FINAL SIZE
 *
 * Simple utility, exported so its not duplicated.
 */
void zpn_application_simple_array_insert(int64_t *array, size_t *array_len, int64_t value);

/* Fixup fields in zpn_application rows. Should be called if you ever
 * write code that received a row_callback from zpn_application */
void zpn_application_row_fixup(struct argo_object *row);

int64_t zpn_application_app_pattern_match_config_value_get(const struct zpn_customer_application_state *state);
int64_t is_app_pattern_match_feature_enabled(int64_t customer_gid);
int64_t is_zpn_application_app_pattern_match_feature_enabled(const struct zpn_customer_application_state *state);

void zpn_app_pattern_match_global_config_ovd_init();
int64_t is_app_pattern_match_feature_hard_disabled();

void zpn_application_version_update(int64_t customer_gid);
struct zpn_customer_application_state *zpn_application_customer_state_get(int64_t customer_gid);
int64_t zpn_application_version_get(struct zpn_customer_application_state *state);

/* Checks if an argo_inet contains data in the 'masked' part of the address */
int zpn_is_ip_address_valid(int64_t customer_gid, struct argo_inet *inet, const char *domain_name);

void tld_1_add_send_callbacks(int64_t customer_gid, void *cookie_cb);

void load_application_state(int64_t customer_gid);

struct zpn_application_domain_queue *zpn_application_domain_queue_create(int64_t customer_gid);
void zpn_application_domain_queue_destroy(struct zpn_application_domain_queue *queue);

int zpn_application_domain_queue_add(struct zpn_application_domain_queue *queue,
                                     char *domain, const struct zpn_customer_application_state *state);

int zpn_application_domain_changed_defer_call(int64_t customer_gid,
                                              char *domain_name);

/* Is this app owned by or shared to the scope */
int is_app_owned_or_shared(struct zpn_application *app, int64_t *scope_gid, char* attr, int64_t machine_gid, unsigned int is_ot);


void argo_zpn_application_init();

static inline int zpn_application_is_invalid_domain_names(struct zpn_application *app) {
      return (!app || !app->domain_names_count || !app->domain_names || !app->domain_names[0]);
}

int ports_array_contains(int *port_ranges, size_t port_ranges_count, int port);

void zpn_application_search_stats_counter(enum zpn_app_search_type type, int64_t selected_app_counts);
void zpn_application_search_debug_stats_counter(enum zpn_app_search_type search_type, enum zpn_app_search_debug_stats_type stat_type);

int zpn_add_app_multi_match_callback_funcs(int64_t customer_gid, void *app_scaling_cb, void *app_download_cb);
int is_app_multi_match_search(int64_t customer_gid, enum zpn_client_type client_type);

int zpn_application_asst_multi_match_feature_flag_init(int64_t instance_gid);
int zpn_application_match_sipa_only_apps_for_sipa_client_enabled(int64_t customer_gid);

void zpn_register_app_scaling_flag(int64_t customer_gid);
void zpn_set_app_scaling_update_callback(void *callback);
int zpn_get_app_scaling_status(int64_t customer_gid);

int is_enqueue_dequeue_fix_feature_enabled_for_customer(int64_t customer_gid);

int is_domain_exclusive(int64_t customer_gid, char *domain);
#endif /* _ZPN_APPLICATION_H_ */
