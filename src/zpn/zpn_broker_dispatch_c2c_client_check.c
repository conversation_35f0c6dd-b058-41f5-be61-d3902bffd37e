/*
 * zpn_broker_dispatch_c2c_client_check.c. Copyright (C) 2024 Zscaler Inc. All Rights Reserved
 */

#include <event2/event.h>
#include "zhash/zhash_table.h"
#include "zpath_misc/zpath_misc.h"
#include "zpath_lib/zpath_local.h"
#include "zpath_lib/zpath_instance.h"
#include "zpath_lib/zpath_debug.h"

#include "zpn/zpn_lib.h"
#include "zpn/zpn_application_domain.h"
#include "zpn/zpn_broker.h"
#include "zpn/zpn_broker_client.h"
#include "zpn/zpn_broker_dispatch.h"
#include "zpn/zpn_application.h"
#include "zpn/zpn_broker_dns.h"
#include "zpn/zpn_broker_dispatch_c2c_client_check.h"
#include "zpn/zpn_broker_common.h"
#include "zpn/zpn_broker_pbroker.h"
#include "zpn_cache/zpn_fifo_cache.h"
#include "zpn/zpn_broker_mtunnel.h"
#include "zpn/zpn_broker_dispatch.h"
#include "zpn/zpn_c2c_client_registration.h"
#include "zpath_lib/zpath_config_override.h"
#include "zpath_lib/zpath_config_override_keys.h"
#include "zpn/zpn_private_broker_util.h"
#include "zpn/zpn_customer_resiliency_settings.h"

#include "zpn/zpn_broker_dispatch_c2c_client_check_compiled.h"

static struct event_base    *g_broker_c2c_event_base    = NULL;
static int    g_initialized                             = 0;
static int64_t is_c2c_ldbypass_phase2_hard_disabled = C2C_BYPASS_LOCAL_DISPATCH_PHASE2_HARD_DISABLED_DEFAULT;
static int64_t is_c2c_ldbypass_phase2_enabled = C2C_BYPASS_LOCAL_DISPATCH_PHASE2_DEFAULT;
static int64_t is_c2c_ldbypass_local_route_enabled = C2C_BYPASS_LOCAL_DISPATCH_PHASE2_PSE_TIMEOUT_ROUTE_LOCALLY_DEFAULT;
static int64_t c2c_ldbypass_cache_bucket_count = 0;
static int64_t c2c_ldbypass_cache_chains_per_bucket = 0;
static int64_t c2c_ldbypass_cache_chains_config_change = 0;

/*
 * Hash table of all customers.
 */
static struct zhash_table *g_broker_cache_customers = NULL;
static struct zpath_mutex g_broker_cache_customer_lock;

struct zpn_broker_cache_customer {
    int64_t customer_gid;
    struct zpath_mutex lock;
};


static struct argo_structure_description *zpn_c2c_fqdn_bypass_cache_stats_description;

static struct zpn_fifo_cache *g_c2c_fqdn_check_cache = NULL;


struct zpn_broker_c2c_check_cache_time {
    int64_t     set_time_sum_us;
    int64_t     set_time_max_us;
    int64_t     get_time_sum_us;
    int64_t     get_time_max_us;
    int64_t     del_time_sum_us;
    int64_t     del_time_max_us;
    int64_t     disp_check_time_sum_us;
    int64_t     disp_check_time_max_us;
};

static struct zpn_broker_c2c_check_cache_time c2c_check_cache_time[ZTHREAD_MAX_THREADS] = {{0}};
static zpath_rwlock_t c2c_check_cache_time_lock = {0};

struct zpn_c2c_fqdn_bypass_cache_stats zpn_c2c_bypass_cache_stats = {0};
static struct zpn_c2c_fqdn_bypass_cache_stats exported_zpn_c2c_bypass_cache_stats = {0};

struct zpn_broker_c2c_request_queue {
    ZLIST_ENTRY(zpn_broker_c2c_request_queue) list;

    // Requestor ID. Mtunnel ID at PSE. Tunnel ID at Public Broker.
    char *req_id;
    // Mtunnel incarnation ID. Applicable only at PSE.
    int64_t mtunnel_incarnation;
};

ZLIST_HEAD(zpn_broker_c2c_request_queue_head, zpn_broker_c2c_request_queue);

struct zpn_broker_c2c_fqdn_check_cache {
    int64_t customer_gid;
    char *c2c_fqdn;
    uint8_t is_c2c_fqdn;

    uint8_t state;
    uint8_t deletion_in_progress;
    struct event *timer;

    /* Timestamp at which this entry was created, and epoch at which
     * timer expires */
    int64_t created_s;
    int64_t timer_fires_s;

    int64_t last_check_transmit_s;
    int64_t last_dispatcher_id;
    int64_t last_rpc_check_sent_us;

    /* Lock per cache entry to synchronize timer event, request queue and delete request */
    zpath_mutex_t lock;
    struct zpn_broker_c2c_request_queue_head list;
};

static int zpn_broker_c2c_cache_get_hash(const char *key, uint64_t *out_key_hash, int *out_bucket_id);
static int zpn_broker_c2c_entry_cache_set_timer(struct zpn_broker_c2c_fqdn_check_cache *entry, int64_t timeout_s);
static void zpn_broker_c2c_cache_entry_delete(struct zpn_broker_c2c_fqdn_check_cache *entry);
static int zpn_broker_c2c_check_send_all(struct zpn_broker_c2c_fqdn_check_cache *entry, const char *error);
static int get_c2c_check_expiry_interval_s(int64_t customer_gid);
static int get_c2c_check_rpc_timeout_interval_s(int64_t customer_gid);
static int64_t get_cache_expiry_interval_s(int64_t customer_gid, struct zpn_broker_dispatcher_c2c_app_check *check);
static void zpn_broker_c2c_check_cache_set_time_stats_update(int64_t start_time_us);
static void zpn_broker_c2c_check_cache_get_time_stats_update(int64_t start_time_us);
static void zpn_broker_c2c_check_cache_del_time_stats_update(int64_t start_time_us);
static void zpn_broker_c2c_check_cache_disp_check_time_stats_update(int64_t start_time_us);
static void zpn_broker_c2c_cache_entry_free(struct zpn_broker_c2c_fqdn_check_cache *entry);


int zpn_broker_c2c_fqdn_check_response_from_dispatcher(struct zpn_broker_dispatcher_c2c_app_check *check)
{
    int res;

    struct zpn_fifo_cache *cache_bucket             = NULL;
    struct zpn_broker_c2c_fqdn_check_cache *entry   = NULL;
    uint64_t cache_key_hash = 0;
    int bucket_id = 0;
    int dropped_old_entry = 0;
    void *data = NULL;
    char key[ZPN_C2C_FQDN_CHECK_KEY_LEN_MAX] = {0};
    int64_t expiry_s = 0;
    const char *resp_error = NULL;
    enum zpn_fifo_cache_drop_reason drop_reason = 0;

    if (!check->app_fqdn) {
        ZPN_LOG(AL_ERROR, "Received remote C2C Check response without a FQDN");
        __sync_fetch_and_add_8(&(zpn_c2c_bypass_cache_stats.disp_check_req_rx_drop_count), 1);
        return ZPN_RESULT_NO_ERROR;
    }

    if (!g_initialized) {
        ZPN_LOG(AL_ERROR, "Received remote C2C Check response when broker is initializing: Customer = %ld, fqdn = %s", (long) check->customer_gid, check->app_fqdn);
        __sync_fetch_and_add_8(&(zpn_c2c_bypass_cache_stats.disp_check_req_rx_drop_count), 1);
        return ZPN_RESULT_NO_ERROR;
    }

    snprintf(key, sizeof(key), "%"PRId64"$%s", check->customer_gid, check->app_fqdn);

    res = zpn_broker_c2c_cache_get_hash(key, &cache_key_hash, &bucket_id);
    if (res) {
        ZPN_LOG(AL_NOTICE, "Could not hash and bucket_id for cache_key: %s ",key);
        return ZPN_RESULT_NOT_FOUND;
    }

    cache_bucket = &g_c2c_fqdn_check_cache[bucket_id];

    //start_time_us = monotime_us();
    res = zpn_fifo_cache_get(cache_bucket, key, &data, &dropped_old_entry, &drop_reason);
    if (dropped_old_entry) {
        ZPN_LOG(AL_NOTICE, "zpn_broker_c2c_fqdn_check_response_from_dispatcher: Dropped old entry:%s drop_reason:%d", check->app_fqdn, drop_reason);
        __sync_fetch_and_sub_8(&(zpn_c2c_bypass_cache_stats.cache_in_use_entries), 1);
        __sync_fetch_and_add_8(&(zpn_c2c_bypass_cache_stats.eviction_during_get), 1);
    }

    if (res || !data) {
        ZPN_LOG(AL_NOTICE, "Received remote C2C Check response that we are not tracking: Customer = %ld, fqdn = %s key:%s bucket:%d key_hash:%ld res:%s",
         (long) check->customer_gid, check->app_fqdn, key, bucket_id, (long)cache_key_hash, zpn_result_string(res));
        __sync_fetch_and_add_8(&(zpn_c2c_bypass_cache_stats.disp_check_req_rx_drop_count), 1);
        return ZPN_RESULT_NO_ERROR;
    }

    entry = (struct zpn_broker_c2c_fqdn_check_cache *)data;

    ZPATH_MUTEX_LOCK(&(entry->lock), __FILE__, __LINE__);

    if (entry->c2c_fqdn == NULL || entry->deletion_in_progress) {
        ZPN_LOG(AL_NOTICE, "Received remote C2C Check response for an expired entry: Customer = %ld, fqdn = %s key:%s bucket:%d key_hash:%ld res:%s",
         (long) check->customer_gid, check->app_fqdn, key, bucket_id, (long)cache_key_hash, zpn_result_string(res));
        __sync_fetch_and_add_8(&(zpn_c2c_bypass_cache_stats.disp_check_req_rx_drop_count), 1);
        ZPATH_MUTEX_UNLOCK(&(entry->lock), __FILE__, __LINE__);
        return ZPN_RESULT_NO_ERROR;
    }

    __sync_fetch_and_add_8(&(zpn_c2c_bypass_cache_stats.disp_check_req_rx_count), 1);

    if (check->result == 1) {
        __sync_fetch_and_add_8(&(zpn_c2c_bypass_cache_stats.total_c2c_fqdn_resp), 1);
    } else if (check->result == 0) {
        __sync_fetch_and_add_8(&(zpn_c2c_bypass_cache_stats.total_app_fqdn_resp), 1);
    }

    ZPN_DEBUG_BROKER_C2C_FQDN_CHECK("Received remote C2C Check response for Customer = %ld, fqdn = %s RPC time: %ld us key:%s bucket:%d key_hash:%"PRIu64" error:%s",
           (long) check->customer_gid, check->app_fqdn, (long)(monotime_us() - entry->last_rpc_check_sent_us),  key, bucket_id, cache_key_hash, check->error ? check->error : "None");

    zpn_broker_c2c_check_cache_disp_check_time_stats_update(entry->last_rpc_check_sent_us);

    if (check->error){
        entry->state = ZPN_BROKER_C2C_CACHE_ENTRY_STATE_INVALID;
        expiry_s = ZPN_BROKER_C2C_ERROR_CACHE_TIME_S;
        resp_error = check->error;
    } else {
        entry->state = ZPN_BROKER_C2C_CACHE_ENTRY_STATE_VALID;
        expiry_s = get_cache_expiry_interval_s(check->customer_gid, check);
    }
    ZPATH_MUTEX_UNLOCK(&(entry->lock), __FILE__, __LINE__);

    /* Response is valid. Reset timer */
    res = zpn_broker_c2c_entry_cache_set_timer(entry, expiry_s);
    if (res) {
        ZPN_LOG(AL_ERROR, "Error setting timer remote C2C Check DNS, customer = %ld, fqdn = %s", (long)entry->customer_gid, entry->c2c_fqdn);
    }

    entry->is_c2c_fqdn = (uint8_t)check->result;

    // Update expiry interval in Hash table
    zpn_fifo_cache_update_expiry(cache_bucket, key, resp_error ? epoch_us() : epoch_us() + SECOND_TO_US(expiry_s));

    ZPN_DEBUG_BROKER_C2C_FQDN_CHECK("Remote C2C Check Entry: caching %s for customer %ld expiry_s:%"PRId64"", check->app_fqdn,
                                        (long) entry->customer_gid, expiry_s);

    res = zpn_broker_c2c_check_send_all(entry, resp_error);
    if (res) {
        ZPN_LOG(AL_ERROR, "Encountered error %s when attempting to send remote C2C check response for customer %ld, fqdn %s", zpn_result_string(res), (long) entry->customer_gid, entry->c2c_fqdn);
    }

    return ZPN_RESULT_NO_ERROR;
}

static void zpn_broker_c2c_check_cache_set_time_stats_update(int64_t start_time_us)
{
    const struct zthread_info *self_zthread_info;
    int64_t set_time_us;
    int zthread_num;

    set_time_us = monotime_us() - start_time_us;
    self_zthread_info = zthread_self();
    zthread_num = self_zthread_info->stack.thread_num;
    ZPATH_RWLOCK_RDLOCK(&c2c_check_cache_time_lock, __FILE__, __LINE__);
    c2c_check_cache_time[zthread_num].set_time_sum_us += set_time_us;
    if (c2c_check_cache_time[zthread_num].set_time_max_us < set_time_us) {
        c2c_check_cache_time[zthread_num].set_time_max_us = set_time_us;
    }
    ZPATH_RWLOCK_UNLOCK(&c2c_check_cache_time_lock, __FILE__, __LINE__);
}

static void zpn_broker_c2c_check_cache_get_time_stats_update(int64_t start_time_us)
{
    struct zthread_info *self_zthread_info;
    int64_t get_time_us;
    int zthread_num;

    get_time_us = monotime_us() - start_time_us;
    self_zthread_info = zthread_self();
    zthread_num = self_zthread_info->stack.thread_num;
    ZPATH_RWLOCK_RDLOCK(&c2c_check_cache_time_lock, __FILE__, __LINE__);
    c2c_check_cache_time[zthread_num].get_time_sum_us += get_time_us;
    if (c2c_check_cache_time[zthread_num].get_time_max_us < get_time_us) {
        c2c_check_cache_time[zthread_num].get_time_max_us = get_time_us;
    }
    ZPATH_RWLOCK_UNLOCK(&c2c_check_cache_time_lock, __FILE__, __LINE__);
}

static void zpn_broker_c2c_check_cache_disp_check_time_stats_update(int64_t start_time_us)
{
    struct zthread_info *self_zthread_info;
    int64_t disp_check_time_us;
    int zthread_num;

    disp_check_time_us = monotime_us() - start_time_us;
    self_zthread_info = zthread_self();
    zthread_num = self_zthread_info->stack.thread_num;
    ZPATH_RWLOCK_RDLOCK(&c2c_check_cache_time_lock, __FILE__, __LINE__);
    c2c_check_cache_time[zthread_num].disp_check_time_sum_us += disp_check_time_us;
    if (c2c_check_cache_time[zthread_num].disp_check_time_max_us < disp_check_time_us) {
        c2c_check_cache_time[zthread_num].disp_check_time_max_us = disp_check_time_us;
    }
    ZPATH_RWLOCK_UNLOCK(&c2c_check_cache_time_lock, __FILE__, __LINE__);
}

static void zpn_broker_c2c_check_cache_del_time_stats_update(int64_t start_time_us)
{
    struct zthread_info *self_zthread_info;
    int64_t del_time_us;
    int zthread_num;

    del_time_us = monotime_us() - start_time_us;
    self_zthread_info = zthread_self();
    zthread_num = self_zthread_info->stack.thread_num;
    ZPATH_RWLOCK_RDLOCK(&c2c_check_cache_time_lock, __FILE__, __LINE__);
    c2c_check_cache_time[zthread_num].del_time_sum_us += del_time_us;
    if (c2c_check_cache_time[zthread_num].del_time_max_us < del_time_us) {
        c2c_check_cache_time[zthread_num].del_time_max_us = del_time_us;
    }
    ZPATH_RWLOCK_UNLOCK(&c2c_check_cache_time_lock, __FILE__, __LINE__);
}

static int zpn_broker_c2c_check_send_to_pbroker(struct zpn_broker_c2c_fqdn_check_cache *entry,
                                                struct zpn_broker_c2c_request_queue *q,
                                                const char *error) {
    struct zpn_broker_dispatcher_c2c_app_check zpdac = {0};
    struct fohh_connection *f_conn;
    int64_t f_conn_incarnation;
    int res = zpn_broker_pbctl_tunnel_fohh_lookup(&f_conn, &f_conn_incarnation, q->req_id, strlen(q->req_id));

    if (res) {
        ZPN_LOG(AL_ERROR, "Remote C2C Check: Could not find f_conn: customer %ld, fqdn %s, is_c2c:%s error:%s",
                         (long) entry->customer_gid,
                         entry->c2c_fqdn,
                         entry->is_c2c_fqdn ? "YES" : "NO",
                         error ? error : "None");
        return res;
    }

    zpdac.customer_gid = entry->customer_gid;
    zpdac.result = entry->is_c2c_fqdn;
    zpdac.app_fqdn = entry->c2c_fqdn;
    zpdac.expire_s = entry->timer_fires_s;
    zpdac.error = error;

    ZPN_DEBUG_BROKER_C2C_FQDN_CHECK("%s: DNS Entry: customer %"PRId64", domain %s: Sending response to %s cache remaining time = %"PRId64"s",
                                   q->req_id,
                                   entry->customer_gid,
                                   entry->c2c_fqdn,
                                   fohh_peer_cn(f_conn),
                                   zpdac.expire_s);

    res = zpn_send_dispatch_fqdn_c2c_check_struct(f_conn, f_conn_incarnation, &zpdac);
    if (res) {
        ZPN_LOG(AL_WARNING, "%s: Could not c2c domain for %s to %s: %s", q->req_id, entry->c2c_fqdn, fohh_description(f_conn), zpn_result_string(res));
    }

    return res;
}
static int zpn_broker_c2c_check_send_all(struct zpn_broker_c2c_fqdn_check_cache *entry, const char *error)
{
    struct zpn_broker_c2c_request_queue *q = NULL;
    int ret = ZPN_RESULT_NO_ERROR;
    int is_c2c_local_route_disable = 0;

    ZPN_DEBUG_BROKER_C2C_FQDN_CHECK("Remote C2C Check: customer %ld, fqdn %s, is_c2c:%s error:%s. Sending response to all waiters...",
                         (long) entry->customer_gid,
                         entry->c2c_fqdn,
                         entry->is_c2c_fqdn ? "YES" : "NO",
                         error ? error : "None");

    ZPATH_MUTEX_LOCK(&(entry->lock), __FILE__, __LINE__);
    while ((q = ZLIST_FIRST(&(entry->list)))) {

        ZLIST_REMOVE(q, list);

        if (ZPN_BROKER_IS_PRIVATE()) {
            ZPN_DEBUG_BROKER_C2C_FQDN_CHECK("Remote C2C Check: customer %ld, fqdn %s, is_c2c:%s Sending response to mtunnel:%s ...",
                        (long) entry->customer_gid,
                        entry->c2c_fqdn,
                        entry->is_c2c_fqdn ? "YES" : "NO",
                        q->req_id);

            if (error && !is_c2c_ldbypass_local_route_config_enabled(entry->c2c_fqdn)) {
                is_c2c_local_route_disable = 1;
            }

            zpn_broker_c2c_fqdn_check_resume_mtunnel(q->req_id, entry->is_c2c_fqdn, is_c2c_local_route_disable, q->mtunnel_incarnation);
        } else {
            ZPN_DEBUG_BROKER_C2C_FQDN_CHECK("Remote C2C Check: customer %ld, fqdn %s, is_c2c:%s Sending response to tunnel:%s ...",
                        (long) entry->customer_gid,
                        entry->c2c_fqdn,
                        entry->is_c2c_fqdn ? "YES" : "NO",
                        q->req_id);
            ret = zpn_broker_c2c_check_send_to_pbroker(entry, q, error);
        }

        if (q->req_id) {
            ZPN_CACHE_FREE(q->req_id);
            q->req_id = NULL;
        }
        q->mtunnel_incarnation = 0;

        ZPN_CACHE_FREE(q);
    }
    ZPATH_MUTEX_UNLOCK(&(entry->lock), __FILE__, __LINE__);

    return ret;
}

static void zpn_broker_c2c_cache_entry_delete(struct zpn_broker_c2c_fqdn_check_cache *entry)
{
    struct zpn_broker_c2c_request_queue *q = NULL;
    int res                                         = 0;
    struct zpn_fifo_cache *cache_bucket             = NULL;
    uint64_t cache_key_hash = 0;
    int bucket_id = 0;
    char cache_key[ZPN_C2C_FQDN_CHECK_KEY_LEN_MAX] = {0};
    int64_t start_time_us = 0;

    if (entry == NULL || entry->c2c_fqdn == NULL) {
        return;
    }

    // Ignore cache delete request if entry is already deleted or deletion in progress.
    ZPATH_MUTEX_LOCK(&(entry->lock), __FILE__, __LINE__);

    if (entry->deletion_in_progress) {
        ZPATH_MUTEX_UNLOCK(&(entry->lock), __FILE__, __LINE__);
        return;
    }
    // Deletion in-progress. Do not allow any other threads to delete this entry again.
    entry->deletion_in_progress = 1;

    ZPATH_MUTEX_UNLOCK(&(entry->lock), __FILE__, __LINE__);

    start_time_us = monotime_us();
    // Remove hash entry in fifo cache
    snprintf(cache_key, sizeof(cache_key) - 1, "%"PRId64"$%s", entry->customer_gid, entry->c2c_fqdn);

    res = zpn_broker_c2c_cache_get_hash(cache_key, &cache_key_hash, &bucket_id);
    if (res == ZPN_RESULT_NO_ERROR) {
        cache_bucket = &g_c2c_fqdn_check_cache[bucket_id];
        res = zpn_fifo_cache_remove(cache_bucket, cache_key);
        if (res) {
            ZPN_DEBUG_BROKER_C2C_FQDN_CHECK("Could not remove cache entry for fqdn:%s key:%s bucket:%d error:%s. It might have been removed already due to cache size full",
                                                 entry->c2c_fqdn, cache_key, bucket_id, zpn_result_string(res));
        } else {
            __sync_fetch_and_sub_8(&(zpn_c2c_bypass_cache_stats.cache_in_use_entries), 1);
            zpn_broker_c2c_check_cache_del_time_stats_update(start_time_us);
        }
        __sync_fetch_and_add_8(&(zpn_c2c_bypass_cache_stats.cache_deleted), 1);
    }

    ZPATH_MUTEX_LOCK(&(entry->lock), __FILE__, __LINE__);
    if (entry->timer) {
        event_free(entry->timer);
        entry->timer = NULL;
    }

    while ((q = ZLIST_FIRST(&(entry->list)))) {
        ZLIST_REMOVE(q, list);
        if (q->req_id) {
            ZPN_CACHE_FREE(q->req_id);
            q->req_id = NULL;
        }
        q->mtunnel_incarnation = 0;
        ZPN_CACHE_FREE(q);
    }
    ZPATH_MUTEX_UNLOCK(&(entry->lock), __FILE__, __LINE__);

    zpn_broker_c2c_cache_entry_free(entry);
}

static int zpn_broker_c2c_cache_get_hash(const char *cache_key, uint64_t *out_key_hash, int *out_bucket_id) {

    size_t cache_key_len;
    uint64_t cache_key_hash = 0;
    int bucket_id = 0;

    cache_key_len = strnlen(cache_key, ZPN_C2C_FQDN_CHECK_KEY_LEN_MAX);
    if (cache_key_len + 1 >= ZPN_C2C_FQDN_CHECK_KEY_LEN_MAX) {
        return ZPN_RESULT_NOT_FOUND;
    }

    cache_key_hash = CityHash64(cache_key, cache_key_len);
    bucket_id = ZPN_C2C_CHECK_CACHE_KEY_HASH_TO_BUCKET(cache_key_hash);

    // Make sure bucket id is within the range
    if (bucket_id < 0 || bucket_id >= c2c_ldbypass_cache_bucket_count) {
        ZPN_LOG(AL_WARNING, "Invalid Bucket_id:%d as it doesn't in valid range from 0 to :%"PRId64" for key:%s",
                                 bucket_id, c2c_ldbypass_cache_bucket_count, cache_key);
        return ZPN_RESULT_ERR;
    }

    *out_key_hash = cache_key_hash;
    *out_bucket_id = bucket_id;
    return ZPN_RESULT_NO_ERROR;

}

static void zpn_broker_c2c_cache_entry_free(struct zpn_broker_c2c_fqdn_check_cache *entry) {

    // Cleanup all resources initialized as part of zpn_broker_c2c_cache_entry_create
   if (entry) {
       if (entry->c2c_fqdn) {
           ZPN_CACHE_FREE(entry->c2c_fqdn);
           entry->c2c_fqdn = NULL;
       }

       ZPATH_MUTEX_DESTROY(&(entry->lock), _FILE_, _LINE_);

       // Slow free helps to prevent possible race conditions if multiple threads attempt to delete the cache at same time.
       ZPN_CACHE_FREE_SLOW(entry);
   }
}

static struct zpn_broker_c2c_fqdn_check_cache *zpn_broker_c2c_cache_entry_create(const char *fqdn, int64_t cust_gid) {
    struct zpn_broker_c2c_fqdn_check_cache *entry = NULL;

    entry = ZPN_CACHE_CALLOC(sizeof(*entry));
    if (!entry) {
        return NULL;
    }

    entry->c2c_fqdn = ZPN_CACHE_STRDUP(fqdn, strlen(fqdn));
    entry->customer_gid = cust_gid;
    entry->created_s = epoch_s();
    entry->state = ZPN_BROKER_C2C_CACHE_ENTRY_STATE_INIT;
    entry->deletion_in_progress = 0;
    entry->lock = ZPATH_MUTEX_INIT;

    return entry;
}

static int zpn_broker_c2c_cache_entry_send_request(struct zpn_broker_c2c_fqdn_check_cache *entry, enum zpn_err_brk_req last_disp_error, int req_id)
{
    int res = ZPN_RESULT_NO_ERROR;
    struct zpn_broker_dispatcher_c2c_app_check check_out = {0};

    ZPN_DEBUG_BROKER_C2C_FQDN_CHECK("Remote C2C Check Entry: customer %ld, fqdn %s, Sending request",
                         (long) entry->customer_gid,
                         entry->c2c_fqdn);

    entry->last_check_transmit_s = epoch_s();
    entry->last_rpc_check_sent_us = monotime_us();

    check_out.customer_gid = entry->customer_gid;
    check_out.app_fqdn = entry->c2c_fqdn;
    check_out.id = req_id;

    if (ZPN_BROKER_IS_PRIVATE()) {
        // For refresh request from PSE, Let Public broker know the PSEs cache expiry time.
        if (req_id == zpn_request_type_refresh) {
            check_out.expire_s = entry->timer_fires_s;
        }
        res = zpn_broker_dispatch_fwd_fqdn_c2c_check_req(&check_out);
        if (res) {
            ZPN_LOG(AL_WARNING, "Error forwarding remote C2C check for fqdn %s error:%s", entry->c2c_fqdn, zpn_result_string(res));
            // Forwarding error is tolerable.
        }
    } else {
        res = zpn_broker_dispatch_send_c2c_app_check(&check_out, last_disp_error, &(entry->last_dispatcher_id));
        if (res) {
            ZPN_LOG(AL_ERROR, "Error:%s sending remote C2C check for fqdn:%s customer:%"PRId64" to dispatcher:%s",
                            zpn_result_string(res), check_out.app_fqdn, check_out.customer_gid, (last_disp_error ? "SECONDARY" : "PRIMARY"));
        }
    }
    if (res == ZPN_RESULT_NO_ERROR) {
        __sync_fetch_and_add_8(&(zpn_c2c_bypass_cache_stats.disp_check_req_tx_count), 1);
    } else {
        __sync_fetch_and_add_8(&(zpn_c2c_bypass_cache_stats.disp_check_req_tx_fail_count), 1);
    }

    return res;
}

static void zpn_broker_c2c_check_expire_fifo_cache(struct zpn_broker_c2c_fqdn_check_cache *entry)
{
    int res = 0;
    struct zpn_fifo_cache *cache_bucket             = NULL;
    uint64_t cache_key_hash = 0;
    int bucket_id = 0;
    char key[ZPN_C2C_FQDN_CHECK_KEY_LEN_MAX] = {0};

    if (entry == NULL) {
        return;
    }

    snprintf(key, sizeof(key), "%"PRId64"$%s", entry->customer_gid, entry->c2c_fqdn);

    res = zpn_broker_c2c_cache_get_hash(key, &cache_key_hash, &bucket_id);
    if (res) {
        return;
    }

    cache_bucket = &g_c2c_fqdn_check_cache[bucket_id];

    // Update the expiry as current time to expire the cache in FIFO table.
    zpn_fifo_cache_update_expiry(cache_bucket, key, epoch_us());
    return;
}

static void zpn_broker_c2c_check_resend_to_secondary_dispatcher(struct zpn_broker_c2c_fqdn_check_cache *entry) {

    ZPN_DEBUG_BROKER_C2C_FQDN_CHECK("%s: %ld: Sending re-dispatch request", entry->c2c_fqdn, (long) entry->customer_gid);

    if (zpn_broker_c2c_entry_cache_set_timer(entry, get_c2c_check_rpc_timeout_interval_s(entry->customer_gid))) {
        ZPN_LOG(AL_ERROR, "OUTSTANDING: Customer %ld: could not set timer for remote C2C Check request for %s",
                (long) entry->customer_gid,
                entry->c2c_fqdn);
    }

    entry->state = ZPN_BROKER_C2C_CACHE_ENTRY_STATE_RETRY_OUTSTANDING;

    if (zpn_broker_c2c_cache_entry_send_request(entry, zpn_err_brk_req_timeout_redispatch_to_diff_dc, zpn_request_type_normal)) {
        ZPN_LOG(AL_ERROR, "OUTSTANDING: Customer %ld: could not re send remote C2C Check request for %s",
                (long) entry->customer_gid,
                entry->c2c_fqdn);
                /* This can mean dispatchers unavailable, etc. We send response back now*/
                zpn_broker_c2c_check_send_all(entry, ZPN_ERR_C2C_CHECK_DSP_TIMEOUT);
                zpn_broker_c2c_check_expire_fifo_cache(entry);
                zpn_broker_c2c_entry_cache_set_timer(entry, ZPN_BROKER_C2C_ERROR_CACHE_TIME_S);
                entry->state = ZPN_BROKER_C2C_CACHE_ENTRY_STATE_INVALID;
    }
}

static void zpn_broker_c2c_check_cache_timeout(evutil_socket_t sock, short flags, void *cookie)
{
    struct zpn_broker_c2c_fqdn_check_cache *entry = cookie;

    if (entry == NULL || entry->c2c_fqdn == NULL) {
        // Invalid cookie
        return;
    }

    /* For all these cases except outstanding, we remove the entry. */
    switch (entry->state) {
    case ZPN_BROKER_C2C_CACHE_ENTRY_STATE_OUTSTANDING:
    case ZPN_BROKER_C2C_CACHE_ENTRY_STATE_RETRY_OUTSTANDING:
        ZPN_DEBUG_BROKER_C2C_FQDN_CHECK("%s: %ld: Outstanding remote C2C check request timed out", entry->c2c_fqdn, (long)entry->customer_gid);
        if (ZPN_BROKER_IS_PUBLIC() && entry->state == ZPN_BROKER_C2C_CACHE_ENTRY_STATE_OUTSTANDING) {
            //Response not received for the first request, we should send a lookup to secondary dispatcher
            __sync_fetch_and_add_8(&(zpn_c2c_bypass_cache_stats.init_disp_check_timeout_count), 1);
            zpn_broker_c2c_check_resend_to_secondary_dispatcher(entry);
        } else {
            //It may be a private broker or public broker did not get response even after second request, send timeout response now
            if (entry->state == ZPN_BROKER_C2C_CACHE_ENTRY_STATE_RETRY_OUTSTANDING) {
                __sync_fetch_and_add_8(&(zpn_c2c_bypass_cache_stats.init_fallback_disp_timeout_count), 1);
            } else {
                __sync_fetch_and_add_8(&(zpn_c2c_bypass_cache_stats.init_disp_check_timeout_count), 1);
            }

            zpn_broker_c2c_check_send_all(entry, ZPN_ERR_C2C_CHECK_DSP_TIMEOUT);
            zpn_broker_c2c_check_expire_fifo_cache(entry);
            zpn_broker_c2c_entry_cache_set_timer(entry, ZPN_BROKER_C2C_ERROR_CACHE_TIME_S);

            entry->state = ZPN_BROKER_C2C_CACHE_ENTRY_STATE_INVALID;
        }
        break;
    case ZPN_BROKER_C2C_CACHE_ENTRY_STATE_VALID:
    case ZPN_BROKER_C2C_CACHE_ENTRY_STATE_INVALID:
    case ZPN_BROKER_C2C_CACHE_ENTRY_STATE_INIT:
        ZPN_DEBUG_BROKER_C2C_FQDN_CHECK("%s: %ld: Outstanding remote C2C Check entry expired", entry->c2c_fqdn, (long) entry->customer_gid);
        zpn_broker_c2c_cache_entry_delete(entry);
        break;
    default:
        ZPN_DEBUG_BROKER_C2C_FQDN_CHECK("%s: %ld: Unknown Cache state:%d", entry->c2c_fqdn, (long) entry->customer_gid, entry->state);
        zpn_broker_c2c_cache_entry_delete(entry);
    }
}


static int zpn_broker_c2c_entry_cache_set_timer(struct zpn_broker_c2c_fqdn_check_cache *entry,
                                                int64_t timeout_s)
{
    struct timeval tv = {0};

    tv.tv_sec = timeout_s;
    tv.tv_usec = 0;

    if (entry == NULL) {
        return ZPN_RESULT_BAD_ARGUMENT;
    }

    ZPN_DEBUG_BROKER_C2C_FQDN_CHECK("Remote C2C Check Entry: fqdn %s: Setting timer for %ld seconds from now",
                                    entry->c2c_fqdn,
                                    (long) timeout_s);

    ZPATH_MUTEX_LOCK(&(entry->lock), __FILE__, __LINE__);

    if (entry->c2c_fqdn == NULL || entry->deletion_in_progress) {
        //Ignore the timer request as cache is deleted
        ZPATH_MUTEX_UNLOCK(&(entry->lock), __FILE__, __LINE__);
        return ZPN_RESULT_EXPIRED;
    }

    if (!entry->timer) {
        entry->timer = event_new(g_broker_c2c_event_base, -1, 0, zpn_broker_c2c_check_cache_timeout, entry);
    }
    if (!entry->timer) {
        ZPN_LOG(AL_ERROR, "Could not create timer for fqdn:%s", entry->c2c_fqdn);
        ZPATH_MUTEX_UNLOCK(&(entry->lock), __FILE__, __LINE__);
        return ZPN_RESULT_NO_MEMORY;
    }

    if (entry->timer) {
        // Delete old event if any before adding new one. Never block if the event's callback is running in another thread.
        event_del_noblock(entry->timer);

        if (event_add(entry->timer, &tv)) {
            ZPN_LOG(AL_ERROR, "Could not add event timer for fqdn:%s", entry->c2c_fqdn);
        } else {
            entry->timer_fires_s = epoch_s() + timeout_s;
        }
    }
    ZPATH_MUTEX_UNLOCK(&(entry->lock), __FILE__, __LINE__);

    return ZPN_RESULT_NO_ERROR;
}

static int zpn_broker_c2c_cache_entry_enqueue_request(struct zpn_broker_c2c_fqdn_check_cache *entry,
                                                const char *tunnel_id, const char *mtunnel_id, int64_t mtunnel_incarnation)
{
    struct zpn_broker_c2c_request_queue *q = NULL;

    /* A NULL request doesn't lodge a callback */
    if ((tunnel_id == NULL) && (mtunnel_id == NULL)) {
        ZPN_LOG(AL_ERROR, "NULL input request id for fqdn:%s", entry->c2c_fqdn);
        return ZPN_RESULT_ERR;
    }

    q = ZPN_CACHE_CALLOC(sizeof(*q));
    if (!q) return ZPN_RESULT_NO_MEMORY;

    if (ZPN_BROKER_IS_PRIVATE() && mtunnel_id) {
        q->req_id = ZPN_CACHE_STRDUP(mtunnel_id, strlen(mtunnel_id));
        q->mtunnel_incarnation = mtunnel_incarnation;
    } else if (ZPN_BROKER_IS_PUBLIC() && tunnel_id){
        q->req_id = ZPN_CACHE_STRDUP(tunnel_id, strlen(tunnel_id));
    } else {
        ZPN_CACHE_FREE(q);
        return ZPN_RESULT_NO_ERROR;
    }

    ZPATH_MUTEX_LOCK(&(entry->lock), __FILE__, __LINE__);
    ZLIST_INSERT_HEAD(&(entry->list), q, list);
    ZPATH_MUTEX_UNLOCK(&(entry->lock), __FILE__, __LINE__);

    return ZPN_RESULT_NO_ERROR;
}

struct zpn_broker_cache_customer *zpn_broker_fqdn_c2c_get_customer(int64_t customer_gid) {
    struct zpn_broker_cache_customer *customer = NULL;
    int res = 0;

    ZPATH_MUTEX_LOCK(&g_broker_cache_customer_lock, __FILE__, __LINE__);
    /* Get/Allocate customer state */
    customer = zhash_table_lookup(g_broker_cache_customers, &customer_gid, sizeof(customer_gid), NULL);
    if (!customer) {
        ZPN_DEBUG_BROKER_C2C_FQDN_CHECK("C2C fqdn check Query: allocating customer %ld",
                                (long) customer_gid);
        customer = ZPN_CALLOC(sizeof(*customer));
        if (!customer) {
            ZPN_LOG(AL_ERROR, "Failed to allocate memory for customer state. gid:%"PRId64"", customer_gid);
            ZPATH_MUTEX_UNLOCK(&g_broker_cache_customer_lock, __FILE__, __LINE__);
            return NULL;
        }
        customer->lock = ZPATH_MUTEX_INIT;
        customer->customer_gid = customer_gid;

        res = zhash_table_store(g_broker_cache_customers, &customer_gid, sizeof(customer_gid), 0, customer);
        if (res) {
            ZPN_FREE(customer);
            ZPN_LOG(AL_ERROR, "Error storing customer state into hash table for:%"PRId64"", customer_gid);
            ZPATH_MUTEX_UNLOCK(&g_broker_cache_customer_lock, __FILE__, __LINE__);
            return NULL;
        }
    }
    ZPATH_MUTEX_UNLOCK(&g_broker_cache_customer_lock, __FILE__, __LINE__);
    return customer;
}

int zpn_broker_fqdn_c2c_refresh_query(int req_id,
                                        int64_t remaining_ttl_s,
                                        struct zpn_fifo_cache *cache_bucket,
                                        const char *key,
                                        struct zpn_broker_c2c_fqdn_check_cache *entry) {
    int res = 0;
    int64_t extended_expiry_time_s = 0;

    if (zpn_broker_c2c_cache_entry_send_request(entry, zpn_err_brk_req_no_error, req_id)) {
        return ZPN_RESULT_NOT_FOUND;
    }

    //Extend the expiry time(timer cb) for refresh requests
    extended_expiry_time_s = remaining_ttl_s + get_c2c_check_rpc_timeout_interval_s(entry->customer_gid);
    res = zpn_broker_c2c_entry_cache_set_timer(entry, extended_expiry_time_s);
    if (res) {
        ZPN_LOG(AL_ERROR, "Error setting timer remote C2C Check DNS, customer = %ld, fqdn = %s",
                                    (long)entry->customer_gid, entry->c2c_fqdn);
        return res;
    }

    // Extend the expiry time of a cache in FIFO table
    zpn_fifo_cache_update_expiry(cache_bucket, key, epoch_us() + SECOND_TO_US(extended_expiry_time_s));

    __sync_fetch_and_add_8(&(zpn_c2c_bypass_cache_stats.cache_refresh_req_count), 1);

    return res;
}

int
zpn_broker_fqdn_c2c_check_query( int64_t customer_gid,
                                 const char *tunnel_id,
                                 const char *mtunnel_id,
                                 const char *fqdn,
                                 size_t fqdn_len,
                                 int *result,
                                 int64_t *cache_entry_expires_at,
                                 int req_id,
                                 int64_t mtunnel_incarnation)
{
    int res                                         = 0;
    struct zpn_fifo_cache *cache_bucket             = NULL;
    struct zpn_broker_c2c_fqdn_check_cache *entry   = NULL;
    uint64_t cache_key_hash = 0;
    int bucket_id = 0;
    int dropped_old_entry = 0;
    void *data = NULL;
    char cache_key[ZPN_C2C_FQDN_CHECK_KEY_LEN_MAX] = {0};
    int64_t total_cache_ttl_s, ttl_elapsed_s       = 0;
    int64_t remaining_ttl_s                        = 0;
    int64_t start_time_us                          = 0;
    enum zpn_fifo_cache_drop_reason drop_reason    = 0;
    struct zpn_broker_cache_customer *customer     = NULL;

    ZPN_DEBUG_BROKER_C2C_FQDN_CHECK("Remote C2C Check Query: customer %ld, tunnel %s, mtunnel %s, fqdn %s",
                         (long) customer_gid,
                         tunnel_id ? tunnel_id : "NULL",
                         mtunnel_id ? mtunnel_id : "NULL",
                         fqdn);

    if (!g_initialized) {
        ZPN_LOG(AL_NOTICE, "Received remote Query while broker is still initializing: customer %ld, tunnel %s, mtunnel %s, fqdn %s",
                         (long) customer_gid,
                         tunnel_id ? tunnel_id : "NULL",
                         mtunnel_id ? mtunnel_id : "NULL",
                         fqdn);
        return ZPN_RESULT_NOT_FOUND;
    }
    start_time_us = monotime_us();

    snprintf(cache_key, sizeof(cache_key) - 1, "%"PRId64"$%s", customer_gid, fqdn);

    res = zpn_broker_c2c_cache_get_hash(cache_key, &cache_key_hash, &bucket_id);
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not hash and bucket_id for cache_key: %s ",cache_key);
        return ZPN_RESULT_NOT_FOUND;
    }

    cache_bucket = &g_c2c_fqdn_check_cache[bucket_id];

    customer = zpn_broker_fqdn_c2c_get_customer(customer_gid);
    if (customer == NULL) {
        ZPN_LOG(AL_ERROR, "Could not get c2c check customer state for:%"PRId64"", customer_gid);
        return ZPN_RESULT_NO_MEMORY;
    }

    ZPATH_MUTEX_LOCK(&(customer->lock), __FILE__, __LINE__);

    res = zpn_fifo_cache_get(cache_bucket, cache_key, &data, &dropped_old_entry, &drop_reason);
    if (dropped_old_entry) {
        ZPN_DEBUG_BROKER_C2C_FQDN_CHECK("zpn_broker_fqdn_c2c_check_query:Dropped old entry:%s drop_reason:%d customer_gid: %ld", fqdn, drop_reason, (long)customer_gid);
        __sync_fetch_and_add_8(&(zpn_c2c_bypass_cache_stats.eviction_during_get), 1);
        __sync_fetch_and_sub_8(&(zpn_c2c_bypass_cache_stats.cache_in_use_entries), 1);
    }

    if (res || !data) {
        int64_t expire_us = 0;
        int64_t cache_expire_s = 0;

        /* It can happen very often that we don't find cache here. */
        ZPN_DEBUG_BROKER_C2C_FQDN_CHECK("Could not get cache for fqdn: %s : %s",
                                 fqdn, zpn_result_string(res));

        ZPN_DEBUG_BROKER_C2C_FQDN_CHECK("Creating a new cache entry for fqdn: %s customer_gid: %ld",
                            fqdn, (long)customer_gid);
        __sync_fetch_and_add_8(&(zpn_c2c_bypass_cache_stats.cache_misses), 1);

        // Create a new cache entry
        data = zpn_broker_c2c_cache_entry_create(fqdn, customer_gid);
        if (data == NULL) {
            ZPATH_MUTEX_UNLOCK(&(customer->lock), __FILE__, __LINE__);
            return ZPN_RESULT_NO_MEMORY;
        }

        // Expiry interval to timeout incomplete cache entries if we don't receive the check response from Public Broker/Dispatcher
        cache_expire_s = get_c2c_check_rpc_timeout_interval_s(customer_gid);
        if (ZPN_BROKER_IS_PUBLIC()) {
            // Expiry interval will be double for broker as it has to query both Primary and Secondary dispatcher before marking it as INACTIVE
            cache_expire_s = cache_expire_s * 2;
        }

        expire_us = epoch_us() + SECOND_TO_US(cache_expire_s);

        //reset
        dropped_old_entry = 0;
        res = zpn_fifo_cache_add(cache_bucket, cache_key, data, sizeof(*(struct zpn_broker_c2c_fqdn_check_cache *)data), 1, expire_us, &dropped_old_entry, &drop_reason);
        if (dropped_old_entry) {
            __sync_fetch_and_sub_8(&(zpn_c2c_bypass_cache_stats.cache_in_use_entries), 1);
            __sync_fetch_and_add_8(&(zpn_c2c_bypass_cache_stats.eviction_during_set), 1);
            if (drop_reason == zpn_fifo_cache_drop_reason_size_full) {
                // Old entry was deleted due to cache size full.
                __sync_fetch_and_add_8(&(zpn_c2c_bypass_cache_stats.cache_replacement_count), 1);
            }
            ZPN_DEBUG_BROKER_C2C_FQDN_CHECK("zpn_broker_fqdn_c2c_check_query:Dropped old entry:%s drop_reason:%d customer_gid: %ld during set", fqdn, drop_reason, (long)customer_gid);
        }

        if (res) {
            ZPN_LOG(AL_ERROR, "Could not add cache entry for fqdn: %s : %s",
                            fqdn, zpn_result_string(res));
            zpn_broker_c2c_cache_entry_free(data);
            ZPATH_MUTEX_UNLOCK(&(customer->lock), __FILE__, __LINE__);
            return res;
        } else {
            ZPN_DEBUG_BROKER_C2C_FQDN_CHECK("Remote C2C check cache entry added for fqdn: %s key:%s bucket:%d key_hash:%"PRIu64"",
                            fqdn, cache_key, bucket_id, cache_key_hash);
            __sync_fetch_and_add_8(&(zpn_c2c_bypass_cache_stats.cache_in_use_entries), 1);
            zpn_broker_c2c_check_cache_set_time_stats_update(start_time_us);
        }
        entry = (struct zpn_broker_c2c_fqdn_check_cache *)data;
    } else {
        //cache hit
        entry = (struct zpn_broker_c2c_fqdn_check_cache *)data;

        ZPN_DEBUG_BROKER_C2C_FQDN_CHECK("Remote C2C Check request Cache hit for Customer = %ld, fqdn = %s remaining TTL = %ld s Cache State:%" PRId8 " key:%s bucket:%d key_hash:%"PRIu64"",
                (long) customer_gid, fqdn, (long)(entry->timer_fires_s - epoch_s() ) ,entry->state, cache_key, bucket_id, cache_key_hash);
        __sync_fetch_and_add_8(&(zpn_c2c_bypass_cache_stats.cache_hits), 1);
        zpn_broker_c2c_check_cache_get_time_stats_update(start_time_us);
    }
    ZPATH_MUTEX_UNLOCK(&(customer->lock), __FILE__, __LINE__);

    switch (entry->state) {
    default:
    case ZPN_BROKER_C2C_CACHE_ENTRY_STATE_INIT:
        /* Create a timer for RPC check timeout value, send request, queue entry, return async */
        if (zpn_broker_c2c_entry_cache_set_timer(entry, get_c2c_check_rpc_timeout_interval_s(customer_gid))) {
            ZPN_LOG(AL_ERROR, "%s: INIT: Mtunnel id %s: Customer %ld: could not set timer for remote C2C check request for %s",
                    tunnel_id ? tunnel_id : "NULL",
                    mtunnel_id ? mtunnel_id : "NULL",
                    (long) customer_gid,
                    fqdn);
        }

        if (zpn_broker_c2c_cache_entry_send_request(entry, zpn_err_brk_req_no_error, req_id)) {
            ZPN_LOG(AL_ERROR, "%s: INIT: Mtunnel id %s: Customer %ld: could not send remote C2C check request for %s",
                    tunnel_id ? tunnel_id : "NULL",
                    mtunnel_id ? mtunnel_id : "NULL",
                    (long) customer_gid,
                    fqdn);
            /* This can mean, brokers or dispatchers unavailable, etc. We'll nuke it right out. */
            zpn_broker_c2c_cache_entry_delete(entry);
            return ZPN_RESULT_NOT_FOUND;
        }
        entry->state = ZPN_BROKER_C2C_CACHE_ENTRY_STATE_OUTSTANDING;

        /* FALL THROUGH */
    case ZPN_BROKER_C2C_CACHE_ENTRY_STATE_OUTSTANDING:
    case ZPN_BROKER_C2C_CACHE_ENTRY_STATE_RETRY_OUTSTANDING:
        /* Queue entry, return async  */
        if (zpn_broker_c2c_cache_entry_enqueue_request(entry, tunnel_id, mtunnel_id, mtunnel_incarnation)) {
            ZPN_LOG(AL_ERROR, "Could not enqueue request for fqdn:%s mtunnel:%s tunnel:%s",
                                 fqdn, mtunnel_id ? mtunnel_id : "NULL", tunnel_id ? tunnel_id : "NULL");
        }

        if (cache_entry_expires_at) {
            *cache_entry_expires_at = entry->timer_fires_s;
        }
        return ZPN_RESULT_ASYNCHRONOUS;
    case ZPN_BROKER_C2C_CACHE_ENTRY_STATE_VALID:

        total_cache_ttl_s = entry->timer_fires_s - entry->last_check_transmit_s;
        ttl_elapsed_s = epoch_s() - entry->last_check_transmit_s;
        remaining_ttl_s = total_cache_ttl_s - ttl_elapsed_s;

        // this is a refresh request from PSE, we should send request to dispatcher if broker expiry is less or same as PSE.
        if  (req_id == zpn_request_type_refresh && (entry->timer_fires_s <= *cache_entry_expires_at) && remaining_ttl_s > 1) {
            ZPN_DEBUG_BROKER_C2C_FQDN_CHECK("C2C Refresh Query: customer %ld, tunnel %s, mtunnel %s, fqdn %s, remaining TTL = %ld s, Refreshing from dispatcher",
                                 (long) customer_gid,
                                 tunnel_id ? tunnel_id : "NULL",
                                 mtunnel_id ? mtunnel_id : "NULL",
                                 fqdn,
                                 (long)remaining_ttl_s);

            if (zpn_broker_fqdn_c2c_refresh_query(req_id, remaining_ttl_s, cache_bucket, cache_key, entry)) {
                ZPN_LOG(AL_ERROR, "%s: INIT: Mtunnel id %s: Customer %ld: could not send remote C2C check request for %s",
                        tunnel_id ? tunnel_id : "NULL",
                        mtunnel_id ? mtunnel_id : "NULL",
                        (long) customer_gid,
                        fqdn);
                return ZPN_RESULT_NOT_FOUND;
            }
            /* Queue entry, return async  */
            if (zpn_broker_c2c_cache_entry_enqueue_request(entry, tunnel_id, mtunnel_id, mtunnel_incarnation)) {
                ZPN_LOG(AL_ERROR, "Could not enqueue request");
            }
            return ZPN_RESULT_ASYNCHRONOUS;
        }

        /* Return success. Maybe re-send request, depending on
         * time. If resent request, reset timer to short
         * outstanding */

        *result = entry->is_c2c_fqdn;

        // Initiate a refresh request only if remaining ttl_s is greater than 1 sec and less than half of total cache expiry time.
        if (ttl_elapsed_s > (total_cache_ttl_s / 2) && remaining_ttl_s > 1) {
            /* Refresh request. Timer gets reset if we hear a response */
            ZPN_DEBUG_BROKER_C2C_FQDN_CHECK("Remote C2C check  Query: customer %ld, tunnel %s, mtunnel %s, fqdn %s, remaining TTL = %ld s, Refreshing from dispatcher",
                                 (long) customer_gid,
                                 tunnel_id ? tunnel_id : "NULL",
                                 mtunnel_id ? mtunnel_id : "NULL",
                                 fqdn,
                                 (long)remaining_ttl_s);

            if (zpn_broker_fqdn_c2c_refresh_query(zpn_request_type_refresh, remaining_ttl_s, cache_bucket, cache_key, entry)) {
                ZPN_LOG(AL_ERROR, "%s: INIT: Mtunnel id %s: Customer %ld: could not send remote C2C check request for %s",
                        tunnel_id ? tunnel_id : "NULL",
                        mtunnel_id ? mtunnel_id : "NULL",
                        (long) customer_gid,
                        fqdn);
            }
        }

        if (cache_entry_expires_at) {
            *cache_entry_expires_at = entry->timer_fires_s;
        }
        return ZPN_RESULT_NO_ERROR;
    case ZPN_BROKER_C2C_CACHE_ENTRY_STATE_INVALID:
        /* Return failure */
        return ZPN_RESULT_NOT_FOUND;
    }

    return ZPN_RESULT_NO_ERROR;
}

void *zpn_c2c_fqdn_bypass_init_cache_stats_description()
{
    if (zpn_c2c_fqdn_bypass_cache_stats_description == NULL) {
        zpn_c2c_fqdn_bypass_cache_stats_description = argo_register_global_structure(ZPN_C2C_FQDN_BYPASS_CACHE_STATS_HELPER);

        if (!zpn_c2c_fqdn_bypass_cache_stats_description) {
            ZPN_LOG(AL_ERROR, "Could not register zpn c2c fqdn cache stats description");
            return NULL;
        }
    }
    return zpn_c2c_fqdn_bypass_cache_stats_description;
}

static int zpn_c2c_check_cache_time_fill(struct zpn_c2c_fqdn_bypass_cache_stats *stats_now) {

    int64_t set_time_sum = 0;
    int64_t set_count_delta = 0;
    int64_t set_time_max = 0;
    int64_t get_time_sum = 0;
    int64_t get_count_delta = 0;
    int64_t get_time_max = 0;
    int64_t del_time_sum = 0;
    int64_t del_count_delta = 0;
    int64_t del_time_max = 0;
    int64_t disp_check_time_sum = 0;
    int64_t disp_check_count_delta = 0;
    int64_t disp_check_time_max = 0;
    int total_zthreads = 0;

    zthread_state_get(&total_zthreads);

    ZPATH_RWLOCK_RDLOCK(&c2c_check_cache_time_lock, __FILE__, __LINE__);
    for (int i = 0; i < total_zthreads; i++) {
        set_time_sum += c2c_check_cache_time[i].set_time_sum_us;
        get_time_sum += c2c_check_cache_time[i].get_time_sum_us;
        del_time_sum += c2c_check_cache_time[i].del_time_sum_us;
        disp_check_time_sum += c2c_check_cache_time[i].disp_check_time_sum_us;

        if (set_time_max < c2c_check_cache_time[i].set_time_max_us) {
            set_time_max = c2c_check_cache_time[i].set_time_max_us;
        }
        if (get_time_max < c2c_check_cache_time[i].get_time_max_us) {
            get_time_max = c2c_check_cache_time[i].get_time_max_us;
        }
        if (del_time_max < c2c_check_cache_time[i].del_time_max_us) {
            del_time_max = c2c_check_cache_time[i].del_time_max_us;
        }
        if (disp_check_time_max < c2c_check_cache_time[i].disp_check_time_max_us) {
            disp_check_time_max = c2c_check_cache_time[i].disp_check_time_max_us;
        }
    }
    ZPATH_RWLOCK_UNLOCK(&c2c_check_cache_time_lock, __FILE__, __LINE__);

    set_count_delta = stats_now->cache_misses - exported_zpn_c2c_bypass_cache_stats.cache_misses;
    get_count_delta = stats_now->cache_hits - exported_zpn_c2c_bypass_cache_stats.cache_hits;
    del_count_delta = stats_now->cache_deleted - exported_zpn_c2c_bypass_cache_stats.cache_deleted;
    disp_check_count_delta = stats_now->disp_check_req_rx_count - exported_zpn_c2c_bypass_cache_stats.disp_check_req_rx_count;

    stats_now->avg_cache_add_time_us = (set_count_delta > 0) ? (set_time_sum / set_count_delta) : 0;
    stats_now->max_cache_add_time_us = set_time_max;

    stats_now->avg_cache_get_time_us = (get_count_delta > 0) ? (get_time_sum / get_count_delta) : 0;
    stats_now->max_cache_get_time_us = get_time_max;

    stats_now->avg_cache_del_time_us = (del_count_delta > 0) ? (del_time_sum / del_count_delta) : 0;
    stats_now->max_cache_del_time_us = del_time_max;

    stats_now->avg_disp_init_check_time_us = (disp_check_count_delta > 0) ? (disp_check_time_sum / disp_check_count_delta) : 0;
    stats_now->max_disp_check_time_us = disp_check_time_max;

    ZPATH_RWLOCK_WRLOCK(&c2c_check_cache_time_lock, __FILE__, __LINE__);
    memset(&c2c_check_cache_time[0], 0, sizeof(c2c_check_cache_time));
    ZPATH_RWLOCK_UNLOCK(&c2c_check_cache_time_lock, __FILE__, __LINE__);

    memcpy(&exported_zpn_c2c_bypass_cache_stats, stats_now, sizeof(exported_zpn_c2c_bypass_cache_stats));
    return ZPN_RESULT_NO_ERROR;
}

static void zpn_c2c_fqdn_bypass_cache_stats_build_basic_stats(struct zpn_c2c_fqdn_bypass_cache_stats *stats) {
    stats->total_cache_request = stats->cache_hits + stats->cache_misses;
    stats->cache_max_entries = (c2c_ldbypass_cache_bucket_count * c2c_ldbypass_cache_chains_per_bucket);

    if (stats->cache_hits) {
        stats->cache_hit_ratio = 100 * ((double)stats->cache_hits / (double)stats->total_cache_request);
    }
    if (stats->cache_in_use_entries && stats->cache_max_entries) {
        stats->cache_util_factor = 100 * ((double)stats->cache_in_use_entries / (double)stats->cache_max_entries);
    }
}

static int
zpn_c2c_fqdn_bypass_cache_stats_fill(void *cookie __attribute__((unused)),
                                     int counter __attribute__((unused)),
                                     void *structure_data)
{
    struct zpn_c2c_fqdn_bypass_cache_stats *stats = structure_data;

    zpn_c2c_fqdn_bypass_cache_stats_build_basic_stats(stats);
    zpn_c2c_check_cache_time_fill(stats);
    return ZPN_RESULT_NO_ERROR;
}

int get_data_from_fqdn(uint64_t customer_gid, const char *fqdn, void **data)
{
    char cache_key[ZPN_C2C_FQDN_CHECK_KEY_LEN_MAX] = {0};
    struct zpn_fifo_cache *cache_bucket             = NULL;
    uint64_t cache_key_hash = 0;
    int bucket_id = 0;
    int dropped_old_entry = 0;
    enum zpn_fifo_cache_drop_reason drop_reason = 0;
    int res = 0;

    snprintf(cache_key, sizeof(cache_key) - 1, "%"PRId64"$%s", customer_gid, fqdn);

    res = zpn_broker_c2c_cache_get_hash(cache_key, &cache_key_hash, &bucket_id);
    if (res) {
        return ZPN_RESULT_NOT_FOUND;
    }

    cache_bucket = &g_c2c_fqdn_check_cache[bucket_id];

    res = zpn_fifo_cache_get(cache_bucket, cache_key, data, &dropped_old_entry, &drop_reason);
    if (dropped_old_entry) {
        __sync_fetch_and_sub_8(&(zpn_c2c_bypass_cache_stats.cache_in_use_entries), 1);
        __sync_fetch_and_add_8(&(zpn_c2c_bypass_cache_stats.eviction_during_get), 1);
        ZPN_LOG(AL_NOTICE, "get_data_from_fqdn : Dropped old entry:%s drop_reason:%d cust_gid:%ld", fqdn, drop_reason, (long)customer_gid);
    }

    return res;
}

void convertTimeToDatetime(uint64_t timeValue, char* datetime, int len)
{
    time_t epochTime = (time_t)timeValue; // Convert to seconds
    struct tm *timeinfo;
    timeinfo = localtime(&epochTime);

    strftime(datetime, len, "%m/%d/%Y %H:%M:%S", timeinfo);
}

int zpn_broker_c2c_fqdn_cache_dump(struct zpath_debug_state *request_state,
                                   const char **query_values,
                                   int query_value_count,
                                   void *cookie)
{
    int res = ZPN_RESULT_NO_ERROR;
    uint64_t c_gid = 0;
    const char *fqdn = NULL;
    int start = 1, count = 500;
    void *data = NULL;
    struct zpn_broker_c2c_fqdn_check_cache *entry = NULL;
    struct zpn_fifo_cache *cache_bucket = NULL;
    struct zpn_fifo_cache_element *element;
    struct zpn_fifo_cache_element *tmp_element;
    int element_num = 0, i = 0;
    char datetime[DATE_TIME_LEN] = {0},datetime2[DATE_TIME_LEN] = {0};

    if (query_values[0]) {
        c_gid = strtoul(query_values[0], NULL, 0);
    }

    if (query_values[1]) {
        fqdn = query_values[1];
    }

    if(query_values[2]) {
        start = atoi(query_values[2]);
        if (start < 1 || start > (c2c_ldbypass_cache_bucket_count*c2c_ldbypass_cache_chains_per_bucket)) {
            ZDP("Start should be in range 1-%"PRId64"\n", (c2c_ldbypass_cache_bucket_count*c2c_ldbypass_cache_chains_per_bucket));
            return res;
        }
    }

    if(query_values[3]) {
        count = atoi(query_values[3]);
        if (count < 1 || count > 1000) {
            ZDP("Count should be in range 1-1000\n");
            return res;
        }
    }

    if (ZPN_BROKER_IS_PRIVATE()) {
        uint64_t customer_gid = ZPATH_GID_GET_CUSTOMER_GID(g_broker_common_cfg->private_broker.broker_id);
        if(!c_gid) {
            c_gid = customer_gid;
        } else if (c_gid != customer_gid) {
            ZDP("Incorrect Customer gid for this pse\n");
            return res;
        }
    } else if (!c_gid) {
        ZDP("Please specify customer gid\n");
        return res;
    }

    if (fqdn) {
        res = get_data_from_fqdn(c_gid, fqdn, &data);
        if (!res && data) {
            entry = (struct zpn_broker_c2c_fqdn_check_cache *)data;
            convertTimeToDatetime(entry->created_s,datetime,DATE_TIME_LEN);
            convertTimeToDatetime(entry->timer_fires_s,datetime2,DATE_TIME_LEN);
            ZDP(DUMP_FMT, entry->c2c_fqdn, entry->is_c2c_fqdn?"True":"False", datetime, entry->created_s, datetime2, entry->timer_fires_s);
        } else if (res == ZPN_CACHE_RESULT_NOT_FOUND) {
            ZDP("Fqdn does not exist\n");
        }
    } else {
        for (i = 0; i < c2c_ldbypass_cache_bucket_count; i++) {
            cache_bucket = &g_c2c_fqdn_check_cache[i];
            ZPATH_MUTEX_LOCK(&(cache_bucket->lock), __FILE__, __LINE__);
            ZTAILQ_FOREACH_SAFE(element, &(cache_bucket->list), list, tmp_element) {
                entry = (struct zpn_broker_c2c_fqdn_check_cache *)element->data;
                if(entry->customer_gid == c_gid) {
                    element_num++;
                    if(element_num >= start && element_num < (start + count)) {
                        convertTimeToDatetime(entry->created_s,datetime,DATE_TIME_LEN);
                        convertTimeToDatetime(entry->timer_fires_s,datetime2,DATE_TIME_LEN);
                        ZDP(DUMP_FMT, entry->c2c_fqdn, entry->is_c2c_fqdn?"True":"False", datetime, entry->created_s, datetime2,  entry->timer_fires_s);
                    } else if (element_num > (start + count)) {
                        ZPATH_MUTEX_UNLOCK(&(cache_bucket->lock), __FILE__, __LINE__);
                        goto DONE;
                    }
                }
            }
            ZPATH_MUTEX_UNLOCK(&(cache_bucket->lock), __FILE__, __LINE__);
        }
    }

DONE:
    return ZPN_RESULT_NO_ERROR;
}

int zpn_broker_c2c_fqdn_cache_expire(struct zpath_debug_state *request_state,
                                     const char **query_values,
                                     int query_value_count,
                                     void *cookie)
{
    int res = ZPN_RESULT_NO_ERROR;
    uint64_t c_gid = 0;
    const char *fqdn = NULL;
    struct zpn_broker_c2c_fqdn_check_cache *entry = NULL;
    struct zpn_fifo_cache *cache_bucket = NULL;
    struct zpn_fifo_cache_element *element;
    struct zpn_fifo_cache_element *tmp_element;
    int i = 0;
    int64_t now_s = epoch_s();
    char cache_key[ZPN_C2C_FQDN_CHECK_KEY_LEN_MAX] = {0};
    uint64_t cache_key_hash = 0;
    int bucket_id = 0;

    if (query_values[0]) {
        c_gid = strtoul(query_values[0], NULL, 0);
    }

    if (query_values[1]) {
        fqdn = query_values[1];
    }

    if (ZPN_BROKER_IS_PRIVATE()) {
        uint64_t customer_gid = ZPATH_GID_GET_CUSTOMER_GID(g_broker_common_cfg->private_broker.broker_id);
        if(!c_gid) {
            c_gid = customer_gid;
        } else if (c_gid != customer_gid) {
            ZDP("Incorrect Customer gid for this pse\n");
            return res;
        }
    } else if (!c_gid) {
        ZDP("Please specify customer gid\n");
        return res;
    }

    if (fqdn) {
        snprintf(cache_key, sizeof(cache_key) - 1, "%"PRId64"$%s", c_gid, fqdn);
        res = zpn_broker_c2c_cache_get_hash(cache_key, &cache_key_hash, &bucket_id);
        if (res) {
            return ZPN_RESULT_NOT_FOUND;
        }
        cache_bucket = &g_c2c_fqdn_check_cache[bucket_id];
        /* Perform lookup without taking lock. */
        element = zhash_table_lookup(cache_bucket->map,
                                     cache_key,
                                     strnlen(cache_key, ZPN_FIFO_CACHE_KEY_LEN_MAX),
                                     NULL);
        if (element && element->data) {
            ZPATH_MUTEX_LOCK(&(cache_bucket->lock), __FILE__, __LINE__);
            if (element->deletion_in_progress == 0 && element->expire_at_us > now_s) {
                entry = (struct zpn_broker_c2c_fqdn_check_cache *)element->data;
                element->expire_at_us = now_s;
                entry->timer_fires_s = now_s;
                ZDP("Fqdn expiry successful\n");
             } else {
                 ZDP("Fqdn does not exist\n");
             }
             ZPATH_MUTEX_UNLOCK(&(cache_bucket->lock), __FILE__, __LINE__);
        } else if (res == ZPN_CACHE_RESULT_NOT_FOUND) {
            ZDP("Fqdn does not exist\n");
        }
    } else {
        for (i = 0; i < c2c_ldbypass_cache_bucket_count; i++) {
            cache_bucket = &g_c2c_fqdn_check_cache[i];
            ZPATH_MUTEX_LOCK(&(cache_bucket->lock), __FILE__, __LINE__);
            ZTAILQ_FOREACH_SAFE(element, &(cache_bucket->list), list, tmp_element) {
                entry = (struct zpn_broker_c2c_fqdn_check_cache *)element->data;
                if(entry->customer_gid == c_gid && element->expire_at_us > now_s) {
                    element->expire_at_us = now_s;
                    entry->timer_fires_s = now_s;
                }
            }
            ZPATH_MUTEX_UNLOCK(&(cache_bucket->lock), __FILE__, __LINE__);
        }
    }

    return ZPN_RESULT_NO_ERROR;
}

int zpn_broker_c2c_fqdn_cache_stats_dump(struct zpath_debug_state *request_state,
                                    const char **query_values,
                                    int query_value_count,
                                    void *cookie)
{
    char jsonout[10000] = {0};

    zpn_c2c_fqdn_bypass_cache_stats_build_basic_stats(&zpn_c2c_bypass_cache_stats);

    if (ARGO_RESULT_NO_ERROR == argo_structure_dump(zpn_c2c_fqdn_bypass_cache_stats_description, &zpn_c2c_bypass_cache_stats, jsonout,
                                                    sizeof(jsonout), NULL, 1)){
        ZDP("Remote C2C check cache stats: %s\n", jsonout);
    }

    return ZPATH_RESULT_NO_ERROR;
}

int zpn_broker_c2c_fqdn_cache_reset_stats(struct zpath_debug_state *request_state,
                                          const char **query_values,
                                          int query_value_count,
                                          void *cookie)
{
    int64_t         cache_max_entries = 0;
    int16_t         cache_util_factor = 0;
    int64_t         cache_in_use_entries = 0;

    ZDP("Resetting remote C2C check cache stats\n");
    ZPN_LOG(AL_INFO, "Resetting remote C2C check cache stats..");

    // Take a backup of stats related to actual cache usage which shouldn't be reset to 0
    cache_max_entries = zpn_c2c_bypass_cache_stats.cache_max_entries;
    cache_in_use_entries = zpn_c2c_bypass_cache_stats.cache_in_use_entries;
    cache_util_factor = zpn_c2c_bypass_cache_stats.cache_util_factor;


    memset(&exported_zpn_c2c_bypass_cache_stats, 0, sizeof(exported_zpn_c2c_bypass_cache_stats));
    memset(&zpn_c2c_bypass_cache_stats, 0, sizeof(zpn_c2c_bypass_cache_stats));

    // Restore the stats related to actual cache usage.
    zpn_c2c_bypass_cache_stats.cache_max_entries = cache_max_entries;
    zpn_c2c_bypass_cache_stats.cache_in_use_entries = cache_in_use_entries;
    zpn_c2c_bypass_cache_stats.cache_util_factor = cache_util_factor;

    return ZPATH_RESULT_NO_ERROR;
}

int zpn_broker_c2c_fqdn_cache_refresh(struct zpath_debug_state *request_state,
                                      const char **query_values,
                                      int query_value_count,
                                      void *cookie)
{
    int res = ZPN_RESULT_NO_ERROR;
    uint64_t c_gid = 0;
    const char *fqdn = NULL;
    int start = 1, count = 100;
    void *data = NULL;
    struct zpn_broker_c2c_fqdn_check_cache *entry = NULL;
    struct zpn_fifo_cache *cache_bucket = NULL;
    struct zpn_fifo_cache_element *element;
    struct zpn_fifo_cache_element *tmp_element;
    int element_num = 0, i = 0, result = 0;

    if (query_values[0]) {
        c_gid = strtoul(query_values[0], NULL, 0);
    }

    if (query_values[1]) {
        fqdn = query_values[1];
    }

    if(query_values[2]) {
        start = atoi(query_values[2]);
        if (start < 1 || start > (c2c_ldbypass_cache_bucket_count*c2c_ldbypass_cache_chains_per_bucket)) {
            ZDP("Start should be in range 1-%"PRId64"\n", (c2c_ldbypass_cache_bucket_count*c2c_ldbypass_cache_chains_per_bucket));
            return res;
        }
    }

    if(query_values[3]) {
        count = atoi(query_values[3]);
        if (count < 1 || count > 500) {
            ZDP("Count should be in range 1-500\n");
            return res;
        }
    }

    if (ZPN_BROKER_IS_PRIVATE()) {
        uint64_t customer_gid = ZPATH_GID_GET_CUSTOMER_GID(g_broker_common_cfg->private_broker.broker_id);
        if(!c_gid) {
            c_gid = customer_gid;
        } else if (c_gid != customer_gid) {
            ZDP("Incorrect Customer gid for this pse\n");
            return res;
        }
    } else if (!c_gid) {
        ZDP("Please specify customer gid\n");
        return res;
    }

    if (fqdn) {
        res = get_data_from_fqdn(c_gid, fqdn, &data);
        if (!res) {
            entry = (struct zpn_broker_c2c_fqdn_check_cache *)data;
            if (zpn_broker_fqdn_c2c_check_query(c_gid, NULL, NULL, fqdn, strnlen(fqdn, ZPN_C2C_FQDN_LEN_MAX), &result, &(entry->timer_fires_s), zpn_request_type_refresh, 0) != ZPN_RESULT_ASYNCHRONOUS) {
                 ZDP("Unable to send refresh request to dispatcher\n");
            } else {
                 ZDP("Sent refresh request to dispatcher\n");
            }
        } else if (res == ZPN_CACHE_RESULT_NOT_FOUND) {
            ZDP("Fqdn does not exist\n");
        }
    } else {
        for (i = 0; i < c2c_ldbypass_cache_bucket_count; i++) {
            cache_bucket = &g_c2c_fqdn_check_cache[i];
            ZPATH_MUTEX_LOCK(&(cache_bucket->lock), __FILE__, __LINE__);
            ZTAILQ_FOREACH_SAFE(element, &(cache_bucket->list), list, tmp_element) {
                entry = (struct zpn_broker_c2c_fqdn_check_cache *)element->data;
                if(entry->customer_gid == c_gid) {
                    element_num++;
                    if(element_num >= start && element_num < (start + count)) {
                        if (zpn_broker_fqdn_c2c_check_query(c_gid, NULL, NULL, entry->c2c_fqdn, strnlen(entry->c2c_fqdn, ZPN_C2C_FQDN_LEN_MAX), &result, &(entry->timer_fires_s), zpn_request_type_refresh, 0) != ZPN_RESULT_ASYNCHRONOUS) {
                             ZDP("Unable to send refresh request to dispatcher for %s\n",entry->c2c_fqdn);
                        } else {
                             ZDP("Sent refresh request to dispatcher for %s\n",entry->c2c_fqdn);
                        }
                    } else if (element_num > (start + count)) {
                        ZPATH_MUTEX_UNLOCK(&(cache_bucket->lock), __FILE__, __LINE__);
                        goto DONE;
                    }
                }
            }
            ZPATH_MUTEX_UNLOCK(&(cache_bucket->lock), __FILE__, __LINE__);
        }
    }
DONE:
    return ZPN_RESULT_NO_ERROR;
}

int zpn_broker_c2c_fqdn_cache_flush(struct zpath_debug_state *request_state,
                                    const char **query_values,
                                    int query_value_count,
                                    void *cookie)
{
    int res = ZPN_RESULT_NO_ERROR;
    uint64_t c_gid = 0;
    const char *fqdn = NULL;
    struct zpn_broker_c2c_fqdn_check_cache *entry = NULL;
    struct zpn_fifo_cache *cache_bucket = NULL;
    struct zpn_fifo_cache_element *element;
    struct zpn_fifo_cache_element *tmp_element;
    void *data = NULL;
    int i=0;

    if (query_values[0]) {
        c_gid = strtoul(query_values[0], NULL, 0);
    }

    if (query_values[1]) {
        fqdn = query_values[1];
    }

    if (ZPN_BROKER_IS_PRIVATE()) {
        uint64_t customer_gid = ZPATH_GID_GET_CUSTOMER_GID(g_broker_common_cfg->private_broker.broker_id);
        if(!c_gid) {
            c_gid = customer_gid;
        } else if (c_gid != customer_gid) {
            ZDP("Incorrect Customer gid for this pse\n");
            return res;
        }
    } else if (!c_gid) {
        ZDP("Please specify customer gid\n");
        return res;
    }

    if (fqdn) {
        res = get_data_from_fqdn(c_gid, fqdn, &data);
        if (!res) {
            entry = (struct zpn_broker_c2c_fqdn_check_cache *)data;
        } else if (res == ZPN_CACHE_RESULT_NOT_FOUND) {
            ZDP("Fqdn does not exist\n");
             return ZPN_RESULT_NO_ERROR;
        }
        ZDP("Removing fqdn :%s\n", entry->c2c_fqdn);
        zpn_broker_c2c_cache_entry_delete(entry);
    } else {
        for (i = 0; i < c2c_ldbypass_cache_bucket_count; i++) {
            cache_bucket = &g_c2c_fqdn_check_cache[i];
            ZTAILQ_FOREACH_SAFE(element, &(cache_bucket->list), list, tmp_element) {
                entry = (struct zpn_broker_c2c_fqdn_check_cache *)element->data;
                if(entry->customer_gid == c_gid) {
                    ZDP("Removing fqdn :%s\n", entry->c2c_fqdn);
                    zpn_broker_c2c_cache_entry_delete(entry);
                }
            }
        }
    }

    return ZPN_RESULT_NO_ERROR;
}

int zpn_broker_debug_c2c_fqdn_cache_query(struct zpath_debug_state *request_state,
                                    const char **query_values,
                                    int query_value_count,
                                    void *cookie)
{
    int res = ZPN_RESULT_NO_ERROR;
    uint64_t c_gid = 0;
    const char *fqdn = NULL;
    int result;
    int64_t expire_s;
    char datetime[DATE_TIME_LEN] = {0};

    if (query_values[0]) {
        c_gid = strtoul(query_values[0], NULL, 0);
    }

    if (query_values[1]) {
        fqdn = query_values[1];
    } else {
        ZDP("Please specify fqdn\n");
        return ZPATH_RESULT_NO_ERROR;
    }

    if (ZPN_BROKER_IS_PRIVATE()) {
        uint64_t customer_gid = ZPATH_GID_GET_CUSTOMER_GID(g_broker_common_cfg->private_broker.broker_id);
        if(!c_gid) {
            c_gid = customer_gid;
        } else if (c_gid != customer_gid) {
            ZDP("Incorrect Customer gid for this pse\n");
            return res;
        }
    } else if (!c_gid) {
        ZDP("Please specify customer gid\n");
        return res;
    }

    res = zpn_broker_fqdn_c2c_check_query(c_gid,
                                          NULL,
                                          NULL,
                                          fqdn,
                                          strnlen(fqdn, ZPN_C2C_FQDN_LEN_MAX),
                                          &result,
                                          &expire_s,
                                          zpn_request_type_query,
                                          0);
    if (res == ZPN_RESULT_ASYNCHRONOUS) {
        ZDP("Async response, please check logs for result\n");
    } else if (res == ZPN_RESULT_NO_ERROR) {
        convertTimeToDatetime(expire_s,datetime,DATE_TIME_LEN);
        ZDP("Result: is_c2c:%s expiry: %s (%"PRId64")\n", result?"True":"False", datetime, expire_s);
    } else {
        ZDP("Error sending DNS check for fqdn:%s\n", fqdn);
    }

    return ZPATH_RESULT_NO_ERROR;
}

#define LDBYPASS_PHASE2_PSE_CONFIG_FLAG_STATUS_FORMAT_JSON                                                          \
        "{\"config.feature.c2c.local_dispatch_bypass.phase2.hard_disabled\": %" PRId64 ","                          \
        " \"config.feature.c2c.local_dispatch_bypass.phase2.enable\": %" PRId64 ","                                 \
        " \"config.feature.c2c.pse.local_dispatch_bypass.promote\": %" PRId64 ","                                   \
        " \"config.feature.c2c.local_dispatch_bypass.phase2.pse.cache_buckets\": %" PRId64 ","                      \
        " \"config.feature.c2c.local_dispatch_bypass.phase2.pse.cache_chains\": %" PRId64 ","                       \
        " \"config.feature.c2c.local_dispatch_bypass.phase2.pse.expiry_s\": %" PRId64 ","                           \
        " \"config.feature.c2c.local_dispatch_bypass.phase2.pse.check_timeout_s\": %" PRId64 ","                    \
        " \"config.feature.c2c.local_dispatch_bypass.phase2.pse.timeout_route_locally.enable\": %" PRId64 "}\n"

#define LDBYPASS_PHASE2_BROKER_CONFIG_FLAG_STATUS_FORMAT_JSON                                                       \
        "{\"config.feature.c2c.local_dispatch_bypass.phase2.hard_disabled\": %" PRId64 ","                          \
        " \"config.feature.c2c.local_dispatch_bypass.phase2.enable\": %" PRId64 ","                                 \
        " \"config.feature.c2c.local_dispatch_bypass.phase2.pse.timeout_route_locally.enable\": %" PRId64 ","       \
        " \"config.feature.c2c.local_dispatch_bypass.phase2.broker.cache_buckets\": %" PRId64 ","                   \
        " \"config.feature.c2c.local_dispatch_bypass.phase2.broker.cache_chains\": %" PRId64 ","                    \
        " \"config.feature.c2c.local_dispatch_bypass.phase2.broker.expiry_s\": %" PRId64 ","                        \
        " \"config.feature.c2c.local_dispatch_bypass.phase2.broker.check_timeout_s\": %" PRId64 "}\n"

#define LDBYPASS_PHASE2_PSE_CONFIG_FLAG_STATUS_FORMAT_TEXT                                                         \
        "config.feature.c2c.local_dispatch_bypass.phase2.hard_disabled                         : %" PRId64 "\n"    \
        "config.feature.c2c.local_dispatch_bypass.phase2.enable                                : %" PRId64 "\n"    \
        "config.feature.c2c.pse.local_dispatch_bypass.promote                                  : %" PRId64 "\n"    \
        "config.feature.c2c.local_dispatch_bypass.phase2.pse.cache_buckets                     : %" PRId64 "\n"    \
        "config.feature.c2c.local_dispatch_bypass.phase2.pse.cache_chains                      : %" PRId64 "\n"    \
        "config.feature.c2c.local_dispatch_bypass.phase2.pse.expiry_s                          : %" PRId64 "\n"    \
        "config.feature.c2c.local_dispatch_bypass.phase2.pse.check_timeout_s                   : %" PRId64 "\n"    \
        "config.feature.c2c.local_dispatch_bypass.phase2.pse.timeout_route_locally.enable      : %" PRId64 "\n"    \

#define LDBYPASS_PHASE2_BROKER_CONFIG_FLAG_STATUS_FORMAT_TEXT                                                      \
        "config.feature.c2c.local_dispatch_bypass.phase2.hard_disabled                         : %" PRId64 "\n"    \
        "config.feature.c2c.local_dispatch_bypass.phase2.enable                                : %" PRId64 "\n"    \
        "config.feature.c2c.local_dispatch_bypass.phase2.pse.timeout_route_locally.enable      : %" PRId64 "\n"    \
        "config.feature.c2c.local_dispatch_bypass.phase2.broker.cache_buckets                  : %" PRId64 "\n"    \
        "config.feature.c2c.local_dispatch_bypass.phase2.broker.cache_chains                   : %" PRId64 "\n"    \
        "config.feature.c2c.local_dispatch_bypass.phase2.broker.expiry_s                       : %" PRId64 "\n"    \
        "config.feature.c2c.local_dispatch_bypass.phase2.broker.check_timeout_s                : %" PRId64 "\n"    \

int c2c_ldbypass_phase2_config_flag_status(struct zpath_debug_state *request_state,
                                           const char **query_values,
                                           int query_value_count,
                                           void *cookie)
{
    int res = ZPN_RESULT_NO_ERROR;
    int64_t c_gid = 0;

    if (query_values[0]) {
        c_gid = strtoul(query_values[0], NULL, 0);
    }

    if (ZPN_BROKER_IS_PRIVATE()) {
        uint64_t customer_gid = ZPATH_GID_GET_CUSTOMER_GID(g_broker_common_cfg->private_broker.broker_id);
        if(!c_gid) {
            c_gid = customer_gid;
        } else if (c_gid != customer_gid) {
            ZDP("Incorrect Customer gid for this pse\n");
            return res;
        }
    } else if (!c_gid) {
        ZDP("Please specify customer gid\n");
        return res;
    }

    ZDP("Dumping config flag status of c2c ldbypass phase-2\n");
    if (ZPN_BROKER_IS_PRIVATE()) {

        const char *format = LDBYPASS_PHASE2_PSE_CONFIG_FLAG_STATUS_FORMAT_TEXT;
        if (query_values[1] && !strcasecmp(query_values[1], "json")) {
            format = LDBYPASS_PHASE2_PSE_CONFIG_FLAG_STATUS_FORMAT_JSON;
        }

        int64_t feature_enabled_on_pse = is_c2c_ldbypass_phase2_enabled;
        int64_t pse_expiry_s = c2c_get_bypass_local_dispatch_phase2_pse_expiry_s(c_gid);
        int64_t pse_rpc_timeout_s = c2c_get_bypass_local_dispatch_phase2_pse_rpc_timeout_s(c_gid);
        int64_t ld_bypass_1_feature_status = c2c_is_bypass_local_dispatch_by_regex_enabled_for_customer(c_gid);

        ZDP(format,
            is_c2c_ldbypass_phase2_hard_disabled,
            feature_enabled_on_pse,
            ld_bypass_1_feature_status,
            c2c_ldbypass_cache_bucket_count,
            c2c_ldbypass_cache_chains_per_bucket,
            pse_expiry_s,
            pse_rpc_timeout_s,
            is_c2c_ldbypass_local_route_enabled);
    } else {

        const char *format = LDBYPASS_PHASE2_BROKER_CONFIG_FLAG_STATUS_FORMAT_TEXT;
        if (query_values[1] && !strcasecmp(query_values[1], "json")) {
            format = LDBYPASS_PHASE2_BROKER_CONFIG_FLAG_STATUS_FORMAT_JSON;
        }

        int64_t feature_enabled_on_broker = c2c_is_bypass_local_dispatch_phase2_enabled_for_customer(c_gid);
        int64_t broker_expiry_s = c2c_get_bypass_local_dispatch_phase2_broker_expiry_s();
        int64_t broker_rpc_timeout_s = c2c_get_bypass_local_dispatch_phase2_broker_rpc_timeout_s(c_gid);

        ZDP(format,
            is_c2c_ldbypass_phase2_hard_disabled,
            feature_enabled_on_broker,
            is_c2c_ldbypass_local_route_enabled,
            c2c_ldbypass_cache_bucket_count,
            c2c_ldbypass_cache_chains_per_bucket,
            broker_expiry_s,
            broker_rpc_timeout_s);
    }

    return ZPATH_RESULT_NO_ERROR;
}

int zpn_broker_c2c_fqdn_cache_debug_cmds_init(void) {
    int res;

    res = zpath_debug_add_read_command("Dump either single fqdn or specified count number of cache entries for specified customer",
                                  "/c2c_fqdn/cache/dump",
                                  zpn_broker_c2c_fqdn_cache_dump,
                                  NULL,
                                  "customer", "Customer GID",
                                  "fqdn", "Optional.",
                                  "start", "Optional. The cache entry start index. Default 1",
                                  "count", "Optional. The cache entry count. Default 500, max 1000",
                                  NULL);
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not add /c2c_fqdn/cache/dump debug command: %s", zpn_result_string(res));
        return res;
    }

    res = zpath_debug_add_write_command("Expire all cache entries or specified fqdn cache entry for specified customer",
                                  "/c2c_fqdn/cache/expire",
                                  zpn_broker_c2c_fqdn_cache_expire,
                                  NULL,
                                  "customer", "Customer GID",
                                  "fqdn", "Optional.",
                                  NULL);
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not add /c2c_fqdn/cache/expire debug command: %s", zpn_result_string(res));
        return res;
    }

    res = zpath_debug_add_read_command("Dump cache stats for all customers",
                                  "/c2c_fqdn/cache/stats",
                                  zpn_broker_c2c_fqdn_cache_stats_dump,
                                  NULL,
                                  NULL);
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not add /c2c_fqdn/cache/stats debug command: %s", zpn_result_string(res));
        return res;
    }

    res = zpath_debug_add_write_command("Reset cache usage stats for all customers",
                                  "/c2c_fqdn/cache/reset_stats",
                                  zpn_broker_c2c_fqdn_cache_reset_stats,
                                  NULL,
                                  NULL);
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not add /c2c_fqdn/cache/reset_stats debug command: %s", zpn_result_string(res));
        return res;
    }

    res = zpath_debug_add_write_command("Refresh Expire all cache entries or specified fqdn cache entry for specified customer",
                                  "/c2c_fqdn/cache/refresh",
                                  zpn_broker_c2c_fqdn_cache_refresh,
                                  NULL,
                                  "customer", "Customer GID",
                                  "fqdn", "Optional.",
                                  "start", "Optional. The cache entry start index. Default 1",
                                  "count", "Optional. The cache entry count. Default 100, max 500",
                                  NULL);
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not add /c2c_fqdn/cache/refresh debug command: %s", zpn_result_string(res));
        return res;
    }

    res = zpath_debug_add_write_command("Flush all cache entries or specified fqdn cache entry for specified customer",
                                  "/c2c_fqdn/cache/flush",
                                  zpn_broker_c2c_fqdn_cache_flush,
                                  NULL,
                                  "customer", "Customer GID",
                                  "fqdn", "Optional.",
                                  NULL);
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not add /c2c_fqdn/cache/flush debug command: %s", zpn_result_string(res));
        return res;
    }

    res = zpath_debug_add_write_command("Query Global Dispatcher for specified fqdn for specified customer",
                                  "/c2c_fqdn/cache/query",
                                  zpn_broker_debug_c2c_fqdn_cache_query,
                                  NULL,
                                  "customer", "Customer GID",
                                  "fqdn", "Required.",
                                  NULL);
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not add /c2c_fqdn/cache/query debug command: %s", zpn_result_string(res));
        return res;
    }

    res = zpath_debug_add_read_command("Check C2C LD Bypass 2.0 config flag status broker or PSE",
                                  "/c2c_fqdn/ldbypass_phase2_config_flag/status",
                                  c2c_ldbypass_phase2_config_flag_status,
                                  NULL,
                                  "customer_gid", "Customer id",
                                  "format", "<optional> format=json, or plain text otherwise",
                                  NULL);
    if (res) {
        ZPN_LOG(AL_ERROR, "Failed to add debug command /c2c/ldbypass_phase2_config_flag/status");
        return res;
    }

    return res;
}

/*
 * Cache chains can be increased dynamically at runtime,
 * But decreasing cache chains will only take effect after a service restart.
*/
static void zpn_broker_c2c_check_cache_chains_update_cb(const int64_t *config_value, int64_t gid) {
    int i = 0;
    int res = 0;

    ZPN_LOG(AL_INFO, "Validating config change of remote C2C check cache Chains_per_bucket. Current Value:%"PRId64" Requested Value:%"PRId64" for gid:%"PRId64"",
                                                                        c2c_ldbypass_cache_bucket_count, *config_value, gid);

    if (*config_value <= c2c_ldbypass_cache_chains_per_bucket) {
        ZPN_LOG(AL_WARNING, "Discarding invalid config change of remote C2C check cache Chains_per_bucket. Current value:%"PRId64" Requested value:%"PRId64"",
                                                                    c2c_ldbypass_cache_chains_per_bucket, *config_value);
        return;
    }

    c2c_ldbypass_cache_chains_per_bucket = *config_value;

    ZPN_LOG(AL_INFO, "Updating remote remote C2C check cache size bucket_count:%"PRId64" and Chains_per_bucket:%"PRId64" for gid:%"PRId64"",
                                                                        c2c_ldbypass_cache_bucket_count, *config_value, gid);

    for (i = 0; i < c2c_ldbypass_cache_bucket_count; i++) {
        res = zpn_fifo_cache_update_size(&g_c2c_fqdn_check_cache[i], c2c_ldbypass_cache_chains_per_bucket);
        if (res) {
            ZPN_LOG(AL_ERROR, "Could not resize cache chain for bucket_id %d: %s", i, zpn_result_string(res));
            return;
        }
    }

    return;
}

static int64_t get_cache_expiry_interval_s(int64_t customer_gid, struct zpn_broker_dispatcher_c2c_app_check *check) {
    int64_t expire_s = get_c2c_check_expiry_interval_s(customer_gid);

    if (check && check->expire_s) {
        int64_t now, ttl = 0;

        now = epoch_s();
        ttl = check->expire_s - now;

        ZPN_DEBUG_BROKER_C2C_FQDN_CHECK("Expiry received from dispatcher/broker : %" PRId64 " and from config ovd: %" PRId64, ttl, expire_s);
    }
    return expire_s;
}

int is_c2c_ldbypass_local_route_config_enabled(const char *fqdn) {

    if (is_c2c_ldbypass_local_route_enabled == 0) {
        // Local route is disabled through config override. But Prefer local routing if Cloud is unreachable.
        if (zpn_pse_control_connection_is_cloud_unreachable()) {
            ZPN_DEBUG_BROKER_C2C_FQDN_CHECK("Preferring local route for FQDN:%s as cloud is unreachable", fqdn);
            return 1;
        }
    }

    ZPN_DEBUG_BROKER_C2C_FQDN_CHECK("Config  %s val : %" PRId64,C2C_BYPASS_LOCAL_DISPATCH_PHASE2_PSE_TIMEOUT_ROUTE_LOCALLY,is_c2c_ldbypass_local_route_enabled);
    return is_c2c_ldbypass_local_route_enabled;
}

static int get_c2c_check_rpc_timeout_interval_s(int64_t customer_gid) {
    int cache_rpc_timeout_s = 0;

    if (ZPN_BROKER_IS_PRIVATE()) {
        cache_rpc_timeout_s = c2c_get_bypass_local_dispatch_phase2_pse_rpc_timeout_s(customer_gid);
    } else {
        cache_rpc_timeout_s = c2c_get_bypass_local_dispatch_phase2_broker_rpc_timeout_s(customer_gid);
    }
    return cache_rpc_timeout_s;
}

static int get_c2c_check_expiry_interval_s(int64_t customer_gid) {
    int cache_check_expiry_s = 0;

    if (ZPN_BROKER_IS_PRIVATE()) {
        cache_check_expiry_s = c2c_get_bypass_local_dispatch_phase2_pse_expiry_s(customer_gid);
    } else {
        cache_check_expiry_s = c2c_get_bypass_local_dispatch_phase2_broker_expiry_s();
    }
    return cache_check_expiry_s;
}

int isPowerOfTwo(int64_t x)
{
    return (x & (x - 1)) == 0;
}

int is_c2c_ldbypass_feature_enabled(int64_t customer_gid) {
    if (is_c2c_ldbypass_phase2_hard_disabled) {
        // Feature is hard disabled. Return 0.
        ZPN_DEBUG_BROKER_C2C_FQDN_CHECK("C2C LD Bypass 2.0 feature is hard disabled for all customers");
        return 0;
    }

    if (ZPN_BROKER_IS_PRIVATE()) {
        if (!is_c2c_ldbypass_phase2_enabled) {
            ZPN_DEBUG_BROKER_C2C_FQDN_CHECK("C2C LD Bypass 2.0 feature is disabled on pse for customer :%"PRId64,customer_gid);
        } else {
            ZPN_DEBUG_BROKER_C2C_FQDN_CHECK("C2C LD Bypass 2.0 feature is enabled on pse for customer :%"PRId64,customer_gid);
        }
        return is_c2c_ldbypass_phase2_enabled;
    }

    return c2c_is_bypass_local_dispatch_phase2_enabled_for_customer(customer_gid);
}

int zpn_broker_dispatch_c2c_client_check_init(struct event_base *ev_base) {
    int res = 0;
    int i = 0;

    if (g_initialized) return ZPN_RESULT_NO_ERROR;

    is_c2c_ldbypass_phase2_hard_disabled = c2c_is_bypass_local_dispatch_phase2_hard_disabled();
    zpath_config_override_monitor_int(C2C_BYPASS_LOCAL_DISPATCH_PHASE2_HARD_DISABLED,
                                      &(is_c2c_ldbypass_phase2_hard_disabled),
                                      NULL,
                                      C2C_BYPASS_LOCAL_DISPATCH_PHASE2_HARD_DISABLED_DEFAULT,
                                      (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                      (int64_t)0);

    c2c_check_cache_time_lock = ZPATH_RWLOCK_INIT;
    g_broker_cache_customers = zhash_table_alloc(&zpn_cache_allocator);
    g_broker_cache_customer_lock = ZPATH_MUTEX_INIT;

    if (ZPN_BROKER_IS_PRIVATE()) {
        struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();
        c2c_ldbypass_cache_bucket_count = C2C_BYPASS_LOCAL_DISPATCH_PHASE2_PSE_CACHE_BUCKETS_DEFAULT;
        c2c_ldbypass_cache_chains_per_bucket = C2C_BYPASS_LOCAL_DISPATCH_PHASE2_PSE_CACHE_CHAINS_DEFAULT;

        is_c2c_ldbypass_phase2_enabled = c2c_is_bypass_local_dispatch_phase2_enabled_for_customer(gs->customer_id);
        zpath_config_override_monitor_int(C2C_BYPASS_LOCAL_DISPATCH_PHASE2,
                                        &is_c2c_ldbypass_phase2_enabled,
                                        NULL,
                                        C2C_BYPASS_LOCAL_DISPATCH_PHASE2_DEFAULT,
                                        gs->private_broker_id,
                                        gs->customer_id,
                                        (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                        (int64_t)0);

        is_c2c_ldbypass_local_route_enabled = c2c_get_bypass_local_dispatch_phase2_pse_timeout_route_locally(gs->customer_id);
        zpath_config_override_monitor_int(C2C_BYPASS_LOCAL_DISPATCH_PHASE2_PSE_TIMEOUT_ROUTE_LOCALLY,
                                    &is_c2c_ldbypass_local_route_enabled,
                                    NULL,
                                    C2C_BYPASS_LOCAL_DISPATCH_PHASE2_PSE_TIMEOUT_ROUTE_LOCALLY_DEFAULT,
                                    gs->private_broker_id,
                                    gs->customer_id,
                                    (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                    (int64_t)0);

        c2c_ldbypass_cache_bucket_count = c2c_get_bypass_local_dispatch_phase2_pse_cache_buckets(gs->customer_id);
        if (!isPowerOfTwo(c2c_ldbypass_cache_bucket_count)) {
            ZPN_LOG(AL_ERROR, "PSE cache bucket_count:%"PRId64" isn't power of 2. Using default config",
                    c2c_ldbypass_cache_bucket_count);
            c2c_ldbypass_cache_bucket_count = C2C_BYPASS_LOCAL_DISPATCH_PHASE2_PSE_CACHE_BUCKETS_DEFAULT;
        }
        c2c_ldbypass_cache_chains_per_bucket = c2c_get_bypass_local_dispatch_phase2_pse_cache_chains(gs->customer_id);
    } else {
        c2c_ldbypass_cache_bucket_count = C2C_BYPASS_LOCAL_DISPATCH_PHASE2_BROKER_CACHE_BUCKETS_DEFAULT;
        c2c_ldbypass_cache_chains_per_bucket = C2C_BYPASS_LOCAL_DISPATCH_PHASE2_BROKER_CACHE_CHAINS_DEFAULT;

        c2c_ldbypass_cache_bucket_count = c2c_get_bypass_local_dispatch_phase2_broker_cache_buckets();
        if (!isPowerOfTwo(c2c_ldbypass_cache_bucket_count)) {
            ZPN_LOG(AL_ERROR, "Broker cache bucket_count:%"PRId64" isn't power of 2. Using default config",
                    c2c_ldbypass_cache_bucket_count);
            c2c_ldbypass_cache_bucket_count = C2C_BYPASS_LOCAL_DISPATCH_PHASE2_BROKER_CACHE_BUCKETS_DEFAULT;
        }
        c2c_ldbypass_cache_chains_per_bucket = c2c_get_bypass_local_dispatch_phase2_broker_cache_chains();
    }

    g_broker_c2c_event_base = ev_base;

    ZPN_LOG(AL_INFO, "Initializing remote C2C check cache with bucket_count:%"PRId64" and Chains_per_bucket:%"PRId64"",
                                                                        c2c_ldbypass_cache_bucket_count, c2c_ldbypass_cache_chains_per_bucket);

    if (c2c_ldbypass_cache_bucket_count) {
        g_c2c_fqdn_check_cache = ZPN_CACHE_CALLOC(c2c_ldbypass_cache_bucket_count * sizeof(*g_c2c_fqdn_check_cache));
        if (g_c2c_fqdn_check_cache == NULL) {
            ZPN_LOG(AL_ERROR, "Could not init cache for bucket_id %d: %s", i, zpn_result_string(res));
            return ZPN_RESULT_NO_MEMORY;
        }
    }

    for (i = 0; i < c2c_ldbypass_cache_bucket_count; i++) {
        res = zpn_fifo_cache_init(&g_c2c_fqdn_check_cache[i], c2c_ldbypass_cache_chains_per_bucket);
        if (res) {
            ZPN_LOG(AL_ERROR, "Could not init cache for bucket_id %d: %s", i, zpn_result_string(res));
            return res;
        }
    }

    zpn_c2c_fqdn_bypass_cache_stats_description = zpn_c2c_fqdn_bypass_init_cache_stats_description();

    if (!zpn_c2c_fqdn_bypass_cache_stats_description) {
        return ZPATH_RESULT_ERR;
    }

    if (!argo_log_register_structure(argo_log_get("statistics_log"),
                                 "zpn_c2c_fqdn_bypass_cache_stats",
                                 AL_INFO,
                                 ZPN_C2C_BYPASS_CACHE_STATS_INTERVAL_US,
                                 zpn_c2c_fqdn_bypass_cache_stats_description,
                                 &zpn_c2c_bypass_cache_stats,
                                 1,
                                 zpn_c2c_fqdn_bypass_cache_stats_fill,
                                 NULL)) {
        ZPN_LOG(AL_ERROR, "Could not register zpn_c2c_fqdn_bypass_cache_stats");
        return ZPN_RESULT_ERR;
    }

    c2c_ldbypass_cache_chains_config_change = c2c_ldbypass_cache_chains_per_bucket;

    if (ZPN_BROKER_IS_PRIVATE()) {
        struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();

        zpath_config_override_monitor_int(C2C_BYPASS_LOCAL_DISPATCH_PHASE2_PSE_CACHE_CHAINS,
                            &c2c_ldbypass_cache_chains_config_change,
                            zpn_broker_c2c_check_cache_chains_update_cb,
                            C2C_BYPASS_LOCAL_DISPATCH_PHASE2_PSE_CACHE_CHAINS_DEFAULT,
                            gs->private_broker_id,
                            gs->customer_id,
                            (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                            (int64_t)0);

    } else {
        zpath_config_override_monitor_int(C2C_BYPASS_LOCAL_DISPATCH_PHASE2_BROKER_CACHE_CHAINS,
                            &c2c_ldbypass_cache_chains_config_change,
                            zpn_broker_c2c_check_cache_chains_update_cb,
                            C2C_BYPASS_LOCAL_DISPATCH_PHASE2_BROKER_CACHE_CHAINS_DEFAULT,
                            zpath_instance_global_state.current_config->gid,
                            (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                            (int64_t)0);
    }

     /* Init c2c fqdn cache debug commands */
    res = zpn_broker_c2c_fqdn_cache_debug_cmds_init();
    if (res) {
        ZDX_LOG(AL_ERROR, "Unable to init c2c fqdn cache debug commands: %s", zpath_result_string(res));
        return res;
    }

    g_initialized = 1;

    return ZPN_RESULT_NO_ERROR;

}
