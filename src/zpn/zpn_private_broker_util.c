#include "zpn/zpn_private_broker_private.h"
#include "zpn/zpn_private_broker_site.h"
#include "zpn/zpn_private_broker_util.h"
#include "zpn/zpn_private_broker_private.h"
#include "zpn/zpn_fohh_worker.h"
#include "zpn/zpn_rpc.h"
#include "zpath_lib/zpath_system.h"
#include "zpath_misc/zpath_misc.h"
#include "zpn/zpn_broker_common.h"
#include "zpn/zpn_private_broker_table.h"
#include "zpn/zpn_pbroker_group.h"
#include "zpn_dr/zpn_dr_lib.h"
#include "zpn/zpn_private_broker_site_config.h"

#include "zpath_lib/zpath_debug.h"
#include "zpath_lib/zpath_debug_wally.h"
#include "zpath_lib/zpath_config_override.h"
#include <string.h>
#include "argo/argo.h"
#include "wally/wally_sqlt.h"
#include "wally/wally_db.h"
#include "zsdtls/zdtls.h"
#include "zpath_lib/zpath_cloud.h"
#include "zpath_lib/zpath_config_override_keys.h"
#include <pthread.h>
#include "zpn/zpn_dispatcher.h"
#include "fohh/fohh_log.h"
#include "zpath_lib/zpa_cloud_config.h"
#include "zpath_misc/zpath_version.h"
#include "zpn/zpn_broker_private.h"
#include "zpn/zpn_pb_client.h"
#include "zpn/zpn_fohh_client.h"
#include "zpn/zpn_broker.h"

#define PBROKER_STATE_DAY_STR_LEN 21
#define PBROKER_STATE_HOUR_MIN_SEC_STR_LEN 5
#define PBROKER_ALT_DOMAIN_FILE         "alt_domain.cfg"
#define PBROKER_MAX_ALT_DOMAIN_STR      1000
#define PBROKER_LISTENER_ALT_CLOUD_STR  "PBROKER_LISTENER_ALT_CLOUD"
#define PBROKER_RDIR_ALT_CLOUD_STR      "PBROKER_RDIR_ALT_CLOUD"
#define PBROKER_FIREDRILL_SWITCH_VALUE 0xDEADBEEF

extern struct argo_structure_description *zpn_broker_mission_critical_description_resp;
struct zpath_allocator pbroker_allocator = ZPATH_ALLOCATOR_INIT("pbroker");

/* Global runtime OS information for private broker during startup */
char g_pbroker_runtime_os[FOHH_MAX_NAMELEN] = {0};

uint64_t pbroker_debug =
    //(PBROKER_DEBUG_CONFIG_BIT) |
    //(PBROKER_DEBUG_CONN_BIT) |
    //(PBROKER_DEBUG_CERT_BIT) |
    //(PBROKER_DEBUG_DATA_XFER_BIT) |
    //(PBROKER_DEBUG_OBJECT_BIT) |
    //(PBROKER_DEBUG_AUTH_BIT) |
    0;

uint64_t pbroker_debug_catch_defaults =
    //(PBROKER_DEBUG_CONFIG_BIT) |
    //(PBROKER_DEBUG_CONN_BIT) |
    //(PBROKER_DEBUG_CERT_BIT) |
    //(PBROKER_DEBUG_DATA_XFER_BIT) |
    //(PBROKER_DEBUG_OBJECT_BIT) |
    //(PBROKER_DEBUG_AUTH_BIT) |
    0;



char pb_sni_customer_domain_ctl[PRIVATE_BROKER_SNI_DOMAIN_LEN];
char pb_sni_customer_domain_cfg[PRIVATE_BROKER_SNI_DOMAIN_LEN];
char pb_sni_customer_domain_rcfg[PRIVATE_BROKER_SNI_DOMAIN_LEN];
char pb_sni_customer_domain_ovd[PRIVATE_BROKER_SNI_DOMAIN_LEN];
char pb_sni_customer_domain_log[PRIVATE_BROKER_SNI_DOMAIN_LEN];
char pb_sni_customer_domain_stats[PRIVATE_BROKER_SNI_DOMAIN_LEN];
char pb_sni_customer_domain_userdb_tmpl[PRIVATE_BROKER_SNI_DOMAIN_LEN];
char pb_sni_customer_domain_mc[PRIVATE_BROKER_SNI_DOMAIN_LEN];


uint32_t expire_epoch;

static int64_t local_private_broker_id = 0;

static int64_t        pb_client_conn_monitor_enabled    = DEFAULT_ZPN_PB_CLIENT_CONNECTION_MONITOR;
static int64_t        pb_client_conn_monitor_interval_s = DEFAULT_ZPN_PB_CLIENT_CONNECTION_MONITOR_INTERVAL;

extern char *geoip_db_file;
extern char *geoip_isp_db_file;
int sub_module_upgrade_failed;
extern int file_clean_downloaded[FILE_CODE_MAX];
extern int key_retry_flag[FILE_CODE_MAX];
extern int new_version_downloaded[FILE_CODE_MAX];
extern struct zpn_firedrill_stats g_pb_firedrill_stats_obj;

char zpath_geoip_filename[FILE_PATH_LEN];
char zpath_ispip_filename[FILE_PATH_LEN];

enum pb_b_conn_change_state {
    PB_TO_B_CONN_DISABLE = 0,
    PB_TO_B_CONN_ENABLE,
};

struct zpath_allocator * zpn_private_broker_get_allocator()
{
    return &pbroker_allocator;
}

/*
 * The instance of private broker global state
 */
static struct zpn_private_broker_global_state g_private_broker_global_state;

/*
 * Private broker global state clear
 */
int zpn_private_broker_global_state_clear(void)
{
    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();

    if (gs->magic == PRIVATE_BROKER_GLOBAL_MAGIC) {
        fprintf(stdout,"ERROR: Private broker global state already initilized\n");
        fflush(stdout);
        sleep(1);
        exit(1);
    }

    /* Fields ordered by declaration order in struct */
    gs->lock = (pthread_mutex_t)PTHREAD_MUTEX_INITIALIZER;
    gs->site_config_lock = ZPATH_RWLOCK_INIT;
    {
        pthread_mutex_lock(&gs->lock);
        gs->dr_handler_lock = (pthread_mutex_t)PTHREAD_MUTEX_INITIALIZER;
        gs->magic = PRIVATE_BROKER_GLOBAL_MAGIC;
        bzero(gs->role_name, sizeof(gs->role_name));
        gs->enroll_state = NULL;
        gs->root_cert = NULL;
        bzero(gs->cn, sizeof(gs->cn));
        bzero(gs->cfg_name, sizeof(gs->cfg_name));
        gs->is_paused = 0;
        gs->pause_reason = zpn_private_broker_pause_reason_invalid;
        gs->customer_id = PRIVATE_BROKER_INVALID_ID;
        gs->private_broker_id = PRIVATE_BROKER_INVALID_ID;
        gs->private_broker_version = NULL;
        gs->zcdns = NULL;
        gs->private_broker_state = NULL;
        bzero(gs->cfg_instance_bytes, sizeof(gs->cfg_instance_bytes));
        bzero(gs->cfg_fingerprint_bytes, sizeof(gs->cfg_fingerprint_bytes));
        bzero(gs->cfg_provisioning_key, sizeof(gs->cfg_provisioning_key));
        gs->cfg_key_shard = PRIVATE_BROKER_INVALID_ID;
        bzero(gs->cfg_key_api, sizeof(gs->cfg_key_api));
        bzero(gs->cfg_key_cloud, sizeof(gs->cfg_key_cloud));

        bzero(&gs->cfg_hw_id, sizeof(gs->cfg_hw_id));
        bzero(&gs->cfg_hw_key, sizeof(gs->cfg_hw_key));

        gs->cfg_pkey = NULL;
        gs->zthread = NULL;
        gs->self_to_private_ssl_ctx = NULL;
        gs->self_to_broker_ssl_ctx = NULL;
        gs->self_to_broker_dtls_ctx = NULL;
        gs->pb_start_time_s = epoch_s();

        gs->pb_start_time_m_s = 0;
        struct timespec ts = {0, 0};
        if (clock_gettime(CLOCK_MONOTONIC, &ts) == 0) {
            gs->pb_start_time_m_s = ts.tv_sec;
        }

        gs->develop_certs_mode = zcrypt_metadata_develop_mode_unknown;
        gs->auto_upgrade_disabled = 0;

        gs->cert_validity_start_time_cloud_s = 0;
        gs->cert_validity_end_time_cloud_s = 0;
        gs->cert_force_re_enroll_time_cloud_s = 0;
        if ((gs->configured_cpus = sysconf(_SC_NPROCESSORS_CONF)) == -1) {
            ZPN_LOG(AL_NOTICE, "failed to read number of configured cpus - %s", strerror(errno));
        }

        if ((gs->available_cpus = sysconf(_SC_NPROCESSORS_ONLN)) == -1) {
            ZPN_LOG(AL_NOTICE, "failed to read number of available cpus - %s", strerror(errno));
        }

        gs->pbroker_monitor_timer = NULL;
        gs->number_of_hw_id_changed = 0;
        bzero(gs->hw_id_changed_time_us, sizeof(gs->hw_id_changed_time_us));

        gs->sarge_version = NULL;

        memset(&gs->dr_stats, 0, sizeof(gs->dr_stats));

        memset(gs->instance_full_name, 0, sizeof(gs->instance_full_name));

        gs->cgroup_version = ZPN_SYSTEM_USE_DEFAULT;

        /* Alt cloud related entries */
        gs->alt_cloud_feature_state_changed = 0;
        gs->alt_cloud_name_changed = 0;
        gs->rdir_alt_cloud_name_changed = 0;
        gs->alt_cloud_feature_state = CONFIG_FEATURE_ALT_CLOUD_DISABLED;
        gs->alt_cloud_feature_state_initialised = 0;
        memset(gs->instance_full_name, 0, sizeof(gs->instance_full_name));
        memset(gs->listener_alt_cloud_name, 0, sizeof(gs->listener_alt_cloud_name));
        memset(gs->redir_alt_cloud_name, 0, sizeof(gs->redir_alt_cloud_name));
        gs->redirect_handler_lock = ZPATH_MUTEX_INIT;

        gs->recovery_timer = NULL;

        pthread_mutex_unlock(&gs->lock);
    }


    return ZPATH_RESULT_NO_ERROR;
}

int zpn_private_broker_get_dr_stats(int64_t *dr_activation_on_cnt, int64_t *dr_activation_test_cnt,
                                    int64_t *dr_activation_off_cnt, int64_t *dr_activation_err_cnt,
                                    int64_t *dr_activation_req_cnt, int64_t *dr_activation_resp_cnt,
                                    int64_t *dr_activation_no_resp_cnt, int64_t *dr_activation_err_resp_cnt,
                                    int64_t *dr_auto_cfg_dump_cnt, int64_t *dr_auto_cfg_dump_fail_cnt,
                                    int64_t *dr_config_snapshot_dump_cnt, int64_t *dr_config_snapshot_dump_fail_cnt,
                                    int64_t *dr_config_snapshot_current_count) {

    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();
    struct zpn_private_broker_dr_stats *stats = &gs->dr_stats;

    if (!dr_auto_cfg_dump_cnt || !dr_auto_cfg_dump_fail_cnt ||
        !dr_activation_on_cnt ||!dr_activation_off_cnt ||
        !dr_activation_req_cnt || !dr_activation_resp_cnt ||
        !dr_activation_no_resp_cnt || !dr_activation_err_resp_cnt ||
        !dr_activation_test_cnt || !dr_activation_err_cnt ||
        !dr_config_snapshot_dump_cnt || !dr_config_snapshot_dump_fail_cnt) {
        ZPN_LOG(AL_ERROR, "zpn_system_pbroker_get_dr_stats - Invalid null arg");
        return ZPN_RESULT_BAD_ARGUMENT;
    }

    *dr_auto_cfg_dump_cnt = stats->dr_config_auto_dump_count;
    *dr_auto_cfg_dump_fail_cnt = stats->dr_config_dump_fail_count;
    *dr_activation_on_cnt = stats->dr_activate_on_count;
    *dr_activation_test_cnt = stats->dr_activate_test_count;
    *dr_activation_off_cnt = stats->dr_activate_off_count;
    *dr_activation_err_cnt = stats->dr_activate_err_count;
    *dr_activation_req_cnt = stats->dr_activate_req_count;
    *dr_activation_resp_cnt = stats->dr_activate_req_count;
    *dr_activation_no_resp_cnt = stats->dr_activate_no_resp_count;
    *dr_activation_err_resp_cnt = stats->dr_activate_err_resp_count;
    *dr_config_snapshot_dump_cnt = stats->dr_config_snapshot_counter;
    *dr_config_snapshot_dump_fail_cnt = stats->dr_config_snapshot_failed_counter;
    *dr_config_snapshot_current_count = stats->dr_config_snapshot_current_count;

    return ZPN_RESULT_NO_ERROR;
}



/*
 *
 * Descrption:
 * The function returns pbroker monotonic uptime in a human readable form in the input buf
 * and in the format xxDxxHxxMxxS which represents uptime xx Days, xx Hour, xx Mins xx Seconds.
 * The function is a modified version of the fohh_get_uptime_str function.
 *
 * note:
 * max length of buf is 30 taking consideration of the worst case scenario '9223372036854775807D23H59M59S'
 * max length of day_str is 21 taking consideration of the worst case scenario '9223372036854775807D:'
 * max length of hour_str is 5 taking consideration of the worst case scenario '23H:'
 * max length of min_str is 5 taking consideration of the worst case scenario '59M:'
 * max length of sec_str is 4 taking consideration of the worst case scenario '59S'
 *
 * example:
 * 2           secs : 2S
 * 86          secs : 1M:26S
 * 3608        secs : 1H:0M:8S
 * 3668        secs : 1H:1M:8S
 * 86400       secs : 1D:0H:0M:0S
 * 86400245250 secs : 1000002D:20H:7M:30S
 *
 */
char* pbroker_state_get_uptime_str(char *buf, int buf_len)
{
    int64_t day;
    int64_t hour;
    int64_t min;
    int64_t sec;
    char    day_str[PBROKER_STATE_DAY_STR_LEN];
    char    hour_str[PBROKER_STATE_HOUR_MIN_SEC_STR_LEN];
    char    min_str[PBROKER_STATE_HOUR_MIN_SEC_STR_LEN];
    char    sec_str[PBROKER_STATE_HOUR_MIN_SEC_STR_LEN];

    struct timespec ts;

    if (clock_gettime(CLOCK_MONOTONIC, &ts) != 0) {
        ZPN_LOG(AL_NOTICE, "clock_gettime() failed with errno %d: %s", errno, strerror(errno));
        return "-1";
    }

    sec = ts.tv_sec - g_private_broker_global_state.pb_start_time_m_s;

    day = sec / (24 * 60 * 60);
    sec %= (24 * 60 * 60);

    hour = sec / (60 * 60);
    sec %= (60 * 60);

    min = sec / 60;
    sec %= 60;

    snprintf_nowarn(day_str, sizeof(day_str), "%"PRId64"D:", day);
    snprintf_nowarn(hour_str, sizeof(hour_str), "%"PRId64"H:", hour);
    snprintf_nowarn(min_str, sizeof(min_str), "%"PRId64"M:", min);
    snprintf_nowarn(sec_str, sizeof(sec_str), "%"PRId64"S", sec);

    snprintf(buf, buf_len, "%s%s%s%s",  (day==0)? "": day_str,
                                            (hour==0 && day==0)? "": hour_str,
                                            (day==0 && hour==0 && min==0)? "": min_str,
                                            (day==0 && hour==0 && min==0 && sec==0)? "": sec_str);

    return buf;
}

/*
 * Get private broker global state
 */
struct zpn_private_broker_global_state* zpn_get_private_broker_global_state(void)
{
    /* Return pointer to singleton private broker global state */
    return &g_private_broker_global_state;
}

int zpn_private_broker_state_get_configured_cpus()
{
    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();
    return gs->configured_cpus;
}

int zpn_private_broker_state_get_available_cpus()
{
    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();
    return gs->available_cpus;
}

int64_t zpn_dispatcher_get_avg_eval_time_us()
{
    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();
    ZPN_ATOMIC_STORE(&gs->private_broker_state->dispatcher_stats.local_dispatcher_state_eval_avg_time_us, zpn_dispatcher_state_get_avg_eval_time_us());
    return gs->private_broker_state->dispatcher_stats.local_dispatcher_state_eval_avg_time_us;
}

int64_t zpn_dispatcher_get_max_eval_time_us()
{
    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();
    ZPN_ATOMIC_STORE(&gs->private_broker_state->dispatcher_stats.local_dispatcher_state_eval_max_time_us, zpn_dispatcher_state_get_max_eval_time_us());
    return gs->private_broker_state->dispatcher_stats.local_dispatcher_state_eval_max_time_us;
}

void zpn_private_broker_state_set_fohh_threads(int threads)
{
    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();
    gs->fohh_threads = threads;
}

uint32_t zpn_private_broker_state_get_fohh_threads()
{
    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();
    return gs->fohh_threads;
}

int zpn_private_broker_state_get_system_mem_usage()
{
    const struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();
	const struct zpn_private_broker_system_stats *sys_stats = &(gs->sys_stats);
	return sys_stats->system_mem_util;
}

int zpn_private_broker_state_get_process_mem_usage()
{
    const struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();
	const struct zpn_private_broker_system_stats *sys_stats = &(gs->sys_stats);
	return sys_stats->process_mem_util;
}

void zpn_private_broker_set_site_gid(int64_t site_gid)
{
    zpn_get_private_broker_global_state()->site_gid = site_gid;
}

void zpn_private_broker_set_site_gid_with_lock(int64_t site_gid)
{
    PBROKER_SITE_CONFIG_WRLOCK();
    zpn_get_private_broker_global_state()->site_gid = site_gid;
    PBROKER_SITE_CONFIG_UNLOCK();
}

int64_t zpn_private_broker_get_site_gid()
{
    return zpn_get_private_broker_global_state()->site_gid;
}

int64_t zpn_private_broker_get_site_gid_with_lock()
{
    int64_t site_gid;

    PBROKER_SITE_CONFIG_RDLOCK();
    site_gid = zpn_get_private_broker_global_state()->site_gid;
    PBROKER_SITE_CONFIG_UNLOCK();

    return site_gid;
}

void zpn_private_broker_set_sitec_preferred(int sitec_preferred)
{
    zpn_get_private_broker_global_state()->sitec_preferred = sitec_preferred;
}

void zpn_private_broker_set_sitec_preferred_with_lock(int sitec_preferred)
{
    PBROKER_SITE_CONFIG_WRLOCK();
    zpn_get_private_broker_global_state()->sitec_preferred = sitec_preferred;
    PBROKER_SITE_CONFIG_UNLOCK();
}

int zpn_private_broker_get_sitec_preferred()
{
    return zpn_get_private_broker_global_state()->sitec_preferred;
}

int zpn_private_broker_get_sitec_preferred_with_lock()
{
    uint64_t sitec_preferred;

    PBROKER_SITE_CONFIG_RDLOCK();
    sitec_preferred = zpn_get_private_broker_global_state()->sitec_preferred;
    PBROKER_SITE_CONFIG_UNLOCK();

    return sitec_preferred;
}

int zpn_private_broker_set_offline_domain(const char *offline_domain)
{
    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();

    if (strlen(offline_domain) >= sizeof(gs->offline_domain)) {
        return ZPN_RESULT_ERR;
    }

    strcpy(gs->offline_domain, offline_domain);

    return ZPN_RESULT_NO_ERROR;
}

int zpn_private_broker_set_offline_domain_with_lock(const char *offline_domain)
{
    int res = ZPN_RESULT_NO_ERROR;

    if (offline_domain) {
        PBROKER_SITE_CONFIG_WRLOCK();
        res = zpn_private_broker_set_offline_domain(offline_domain);
        PBROKER_SITE_CONFIG_UNLOCK();
    }

    return res;
}

const char* zpn_private_broker_get_offline_domain()
{
    return zpn_get_private_broker_global_state()->offline_domain;
}

int zpn_private_broker_get_offline_domain_with_lock(char *offline_domain, size_t len)
{
    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();
    int res = ZPN_RESULT_NO_ERROR;

    if (offline_domain) {
        PBROKER_SITE_CONFIG_RDLOCK();
        snprintf(offline_domain, len, "%s", gs->offline_domain);
        PBROKER_SITE_CONFIG_UNLOCK();
    }

    return res;
}

int zpn_private_broker_has_offline_domain()
{
    return zpn_get_private_broker_global_state()->offline_domain[0];
}

void zpn_private_broker_set_reenroll_period(uint64_t reenroll_period)
{
    zpn_get_private_broker_global_state()->site_reenroll_period = reenroll_period;
}

void zpn_private_broker_set_reenroll_period_with_lock(uint64_t reenroll_period)
{
    PBROKER_SITE_CONFIG_WRLOCK();
    zpn_get_private_broker_global_state()->site_reenroll_period = reenroll_period;
    PBROKER_SITE_CONFIG_UNLOCK();
}

uint64_t zpn_private_broker_get_reenroll_period()
{
    return zpn_get_private_broker_global_state()->site_reenroll_period;
}

uint64_t zpn_private_broker_get_reenroll_period_with_lock()
{
    uint64_t reenroll_period;

    PBROKER_SITE_CONFIG_RDLOCK();
    reenroll_period = zpn_get_private_broker_global_state()->site_reenroll_period;
    PBROKER_SITE_CONFIG_UNLOCK();

    return reenroll_period;
}

void zpn_private_broker_set_allow_c2site(int allow_c2site)
{
    zpn_get_private_broker_global_state()->allow_c2site = allow_c2site;
}

void zpn_private_broker_set_allow_c2site_with_lock(int allow_c2site)
{
    PBROKER_SITE_CONFIG_WRLOCK();
    zpn_get_private_broker_global_state()->allow_c2site = allow_c2site;
    PBROKER_SITE_CONFIG_UNLOCK();
}

int zpn_private_broker_get_allow_c2site()
{
    return zpn_get_private_broker_global_state()->allow_c2site;
}

int zpn_private_broker_get_allow_c2site_with_lock()
{
    uint64_t allow_c2site;

    PBROKER_SITE_CONFIG_RDLOCK();
    allow_c2site = zpn_get_private_broker_global_state()->allow_c2site;
    PBROKER_SITE_CONFIG_UNLOCK();

    return allow_c2site;
}

void zpn_private_broker_set_max_allowed_downtime_s(int64_t max_allowed_downtime_s)
{
    zpn_get_private_broker_global_state()->max_allowed_downtime_s = max_allowed_downtime_s;
}

void zpn_private_broker_set_max_allowed_downtime_s_with_lock(int64_t max_allowed_downtime_s)
{
    PBROKER_SITE_CONFIG_WRLOCK();
    zpn_get_private_broker_global_state()->max_allowed_downtime_s = max_allowed_downtime_s;
    PBROKER_SITE_CONFIG_UNLOCK();
}

int64_t zpn_private_broker_get_max_allowed_downtime_s()
{
    return zpn_get_private_broker_global_state()->max_allowed_downtime_s;
}

int64_t zpn_private_broker_get_max_allowed_downtime_s_with_lock()
{
    int64_t max_allowed_downtime_s;

    PBROKER_SITE_CONFIG_RDLOCK();
    max_allowed_downtime_s = zpn_get_private_broker_global_state()->max_allowed_downtime_s;
    PBROKER_SITE_CONFIG_UNLOCK();

    return max_allowed_downtime_s;
}

void zpn_private_broker_set_is_switchtime_enabled(int enabled)
{
    zpn_get_private_broker_global_state()->is_switchtime_enabled = enabled;
}

void zpn_private_broker_set_is_switchtime_enabled_with_lock(int enabled)
{
    PBROKER_SITE_CONFIG_WRLOCK();
    zpn_get_private_broker_global_state()->is_switchtime_enabled = enabled;
    PBROKER_SITE_CONFIG_UNLOCK();
}

int zpn_private_broker_get_is_switchtime_enabled()
{
    return zpn_get_private_broker_global_state()->is_switchtime_enabled;
}

int zpn_private_broker_get_is_switchtime_enabled_with_lock()
{
    uint64_t enabled;

    PBROKER_SITE_CONFIG_RDLOCK();
    enabled = zpn_get_private_broker_global_state()->is_switchtime_enabled;
    PBROKER_SITE_CONFIG_UNLOCK();

    return enabled;
}

void zpn_private_broker_set_max_allowed_switchtime_s(int64_t max_allowed_switchtime_s)
{
    zpn_get_private_broker_global_state()->max_allowed_switchtime_s = max_allowed_switchtime_s;
}

void zpn_private_broker_set_max_allowed_switchtime_s_with_lock(int64_t max_allowed_switchtime_s)
{
    PBROKER_SITE_CONFIG_WRLOCK();
    zpn_get_private_broker_global_state()->max_allowed_switchtime_s = max_allowed_switchtime_s;
    PBROKER_SITE_CONFIG_UNLOCK();
}

int64_t zpn_private_broker_get_max_allowed_switchtime_s()
{
    return zpn_get_private_broker_global_state()->max_allowed_switchtime_s;
}

int64_t zpn_private_broker_get_max_allowed_switchtime_s_with_lock()
{
    int64_t max_allowed_switchtime_s;

    PBROKER_SITE_CONFIG_RDLOCK();
    max_allowed_switchtime_s = zpn_get_private_broker_global_state()->max_allowed_switchtime_s;
    PBROKER_SITE_CONFIG_UNLOCK();

    return max_allowed_switchtime_s;
}

void zpn_private_broker_set_site_is_active(int site_is_active)
{
    zpn_get_private_broker_global_state()->site_is_active = site_is_active;
}

void zpn_private_broker_set_site_is_active_with_lock(int site_is_active)
{
    PBROKER_SITE_CONFIG_WRLOCK();
    zpn_get_private_broker_global_state()->site_is_active = site_is_active;
    PBROKER_SITE_CONFIG_UNLOCK();
}

int zpn_private_broker_get_site_is_active()
{
    return zpn_get_private_broker_global_state()->site_is_active;
}

int zpn_private_broker_get_site_is_active_with_lock()
{
    uint64_t site_is_active;

    PBROKER_SITE_CONFIG_RDLOCK();
    site_is_active = zpn_get_private_broker_global_state()->site_is_active;
    PBROKER_SITE_CONFIG_UNLOCK();

    return site_is_active;
}

void zpn_private_broker_state_set_container_env()
{
    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();
    gs->is_container_env = 1;
}

uint32_t zpn_private_broker_state_is_container_env()
{
    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();
    return gs->is_container_env;
}

/* Get cloud time delta */
int64_t zpn_private_broker_get_cloud_time_delta_us(void)
{
    return g_private_broker_global_state.cloud_time_delta_us;
}

/* Set cloud time delta */
void zpn_private_broker_set_cloud_time_delta_us(int64_t cloud_time_delta)
{
    /* Set argo logs time adjustment */
    argo_set_adj_time(cloud_time_delta);

    g_private_broker_global_state.cloud_time_delta_us = cloud_time_delta;
}

/* Get cloud adjusted epoch time in micro seconds */
int64_t __inline__ zpn_private_broker_cloud_adjusted_epoch_us(void)
{
    return (epoch_us() + g_private_broker_global_state.cloud_time_delta_us);
}

/* Get cloud adjusted epoch time in micro seconds */
int64_t __inline__ zpn_private_broker_cloud_adjusted_epoch_s(void)
{
    return (epoch_s() + g_private_broker_global_state.cloud_time_delta_us / 1000000L);
}

int64_t zpn_cloud_adjusted_epoch_us(void)
{
    if (ZPN_BROKER_IS_PRIVATE()) {
        return zpn_private_broker_cloud_adjusted_epoch_us();
    }

    return epoch_us();
}

int64_t zpn_cloud_adjusted_epoch_s(void)
{
    if (ZPN_BROKER_IS_PRIVATE()) {
        return zpn_private_broker_cloud_adjusted_epoch_s();
    }

    return epoch_s();
}

static const char *
hex_print_bytes(uint8_t *byte_array, int len)
{
    int i;
    static char buf[1024] = ""; // Local static buf !
    char *s = &buf[0];
    char *e = s + sizeof(buf);

    bzero(buf, sizeof(buf));

    s += sxprintf(s, e, "%s", "[");
    for (i = 0; i < len; i++) {
        s += sxprintf(s, e, "\\%x", byte_array[i]);
    }
    s += sxprintf(s, e, "%s", "]");

    return &buf[0];
}

static const char*
hex_print_char_bytes(char *byte_array, int len)
{
    int i;
    static char buf[1024] = ""; // Local static buf !
    char *s = &buf[0];
    char *e = s + sizeof(buf);

    bzero(buf, sizeof(buf));

    s += sxprintf(s, e, "%s", "[");
    for (i = 0; i < len; i++) {
        s += sxprintf(s, e, "\%c", byte_array[i]);
    }
    s += sxprintf(s, e, "%s", "]");

    return &buf[0];
}

void zpn_private_broker_set_firedrill_state(int64_t firedrill_status)
{
    zpn_get_private_broker_global_state()->firedrill_status = firedrill_status;
}

int zpn_private_broker_get_firedrill_state()
{
    return zpn_get_private_broker_global_state()->firedrill_status;
}

void zpn_private_broker_set_firedrill_interval(int64_t firedrill_interval)
{
    zpn_get_private_broker_global_state()->firedrill_interval = firedrill_interval;
}

int64_t zpn_private_broker_get_firedrill_interval()
{
    return zpn_get_private_broker_global_state()->firedrill_interval;
}
void zpn_private_broker_set_firedrill_starttime(int64_t firedrill_starttime)
{
    zpn_get_private_broker_global_state()->firedrill_starttime = firedrill_starttime;
}

int64_t zpn_private_broker_get_firedrill_starttime()
{
    return zpn_get_private_broker_global_state()->firedrill_starttime;
}

/* Priavte broker global satte dump */
void zpn_private_broker_global_state_dump(void)
{
    if (0) {
        struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();

        if (gs->magic != PRIVATE_BROKER_GLOBAL_MAGIC) {
            printf("Private broker global state not initialized\n");
            return;
        }

        /* Code is disabled, but if it is enabled it will be protected with lock when accessing */
        {
            pthread_mutex_lock(&gs->lock);
            printf("Role name: %s\n", gs->role_name);
            printf("Root cert: %s\n", gs->root_cert ? gs->root_cert : "null");
            printf("CN: %s\n", gs->cn);
            printf("Customer ID: %"PRId64"\n", gs->customer_id);
            printf("Private Broker ID: %"PRId64"\n", gs->private_broker_id);
            if (gs->private_broker_version) {
                printf("Private Broker Version: Current: %s, Expected: %s\n", gs->private_broker_version->current_version, gs->private_broker_version->expected_version);
            } else {
                printf("Private Broker Version is null\n");
            }
            if (gs->zcdns) {
                printf("Zcdns initialized\n");
            } else {
                printf("Zcdns is null\n");
            }
            printf("Instance bytes: %s\n", hex_print_bytes(gs->cfg_instance_bytes, (int)sizeof(gs->cfg_instance_bytes)));
            printf("Fingerprint bytes: %s\n", hex_print_bytes(gs->cfg_fingerprint_bytes, (int)sizeof(gs->cfg_fingerprint_bytes)));
            printf("Provisioning key: %s\n", hex_print_char_bytes(gs->cfg_provisioning_key, (int)sizeof(gs->cfg_provisioning_key)));
            printf("Key shard: %d\n", gs->cfg_key_shard);
            printf("Key api: %s\n", gs->cfg_key_api);
            printf("Key cloud: %s\n", gs->cfg_key_cloud);
            printf("HardwareID: %s\n", hex_print_bytes(gs->cfg_hw_id.id, (int)sizeof(gs->cfg_hw_id.id)));
            printf("Hardware Key: %s\n", hex_print_bytes(gs->cfg_hw_key.data, (int)sizeof(gs->cfg_hw_key.data)));
#if OPENSSL_VERSION_NUMBER < 0x10100000L
            // 1.0.x
            // I'm too lazy to get the modern version of this.
            printf("Private key type : %d set\n", EVP_PKEY_type(gs->cfg_pkey->type));
#endif
            if (gs->zthread) {
                printf("Zthread main thread is valid\n");
            } else {
                printf("Zthread main thread is null\n");
            }
            pthread_mutex_unlock(&gs->lock);
        }
    }
}

int
zpn_private_broker_conn_sni_init(void)
{
    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();

    /* Create Broker: control, data, config connections to public broker */
    /* V2 or V3 Style SNI: <pbroker_id>.<pbcfg|pbctl|pbdata>.<cloud_name> */
    if (gs->enroll_version == 2 || gs->enroll_version == 3 || gs->enroll_version == 4) {
        // Warns with -fsanitize=address -O2.

        char *cloud_name = pbroker_get_redir_cloud_name();

        snprintf_nowarn(pb_sni_customer_domain_cfg, sizeof(pb_sni_customer_domain_cfg), "%ld.pbcfg.%s", (long) gs->private_broker_id, cloud_name);
        snprintf_nowarn(pb_sni_customer_domain_rcfg, sizeof(pb_sni_customer_domain_rcfg), "%ld.pbrcfg.%s", (long) gs->private_broker_id, cloud_name);
        snprintf_nowarn(pb_sni_customer_domain_ctl, sizeof(pb_sni_customer_domain_ctl), "%ld.pbctl.%s", (long) gs->private_broker_id, cloud_name);
        snprintf_nowarn(pb_sni_customer_domain_log, sizeof(pb_sni_customer_domain_log), "%ld.pblog.%s", (long) gs->private_broker_id, cloud_name);
        snprintf_nowarn(pb_sni_customer_domain_ovd, sizeof(pb_sni_customer_domain_ovd), "%ld.pbovd.%s", (long) gs->private_broker_id, cloud_name);
        snprintf_nowarn(pb_sni_customer_domain_stats, sizeof(pb_sni_customer_domain_stats), "%ld.pbstats.%s", (long) gs->private_broker_id, cloud_name);
        snprintf_nowarn(pb_sni_customer_domain_userdb_tmpl, sizeof(pb_sni_customer_domain_userdb_tmpl), "%ld.%%ld.%%ld.pbuserdb.%s", (long) gs->private_broker_id, cloud_name);
    } else {
        ZPN_LOG(AL_ERROR, "Unsupported private broker enroll version: %d", gs->enroll_version);
        return ZPATH_RESULT_BAD_STATE;
    }

    return ZPN_RESULT_NO_ERROR;

}

void
zpn_private_broker_client_connection_monitor_cfg_ovd_init(int64_t     private_broker_id,
                                                          int64_t     private_broker_group_id,
                                                          int64_t     customer_id)
{
    zpath_config_override_monitor_int(ZPN_PB_CLIENT_CONNECTION_MONITOR,
                                      &pb_client_conn_monitor_enabled,
                                      NULL,
                                      DEFAULT_ZPN_PB_CLIENT_CONNECTION_MONITOR,
                                      (int64_t) private_broker_id,
                                      (int64_t) private_broker_group_id,
                                      (int64_t) customer_id,
                                      (int64_t) ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                      (int64_t) 0);

    zpath_config_override_monitor_int(ZPN_PB_CLIENT_CONNECTION_MONITOR_INTERVAL,
                                      &pb_client_conn_monitor_interval_s,
                                      NULL,
                                      DEFAULT_ZPN_PB_CLIENT_CONNECTION_MONITOR_INTERVAL,
                                      (int64_t) private_broker_id,
                                      (int64_t) private_broker_group_id,
                                      (int64_t) customer_id,
                                      (int64_t) ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                      (int64_t) 0);
}

int64_t
is_zpn_private_broker_client_connection_monitor_enabled()
{
    return pb_client_conn_monitor_enabled;
}

int64_t
zpn_private_broker_get_client_connection_monitor_interval()
{
    return pb_client_conn_monitor_interval_s;
}

void zpn_private_broker_send_fohh_info_with_alt_cloud_capability(struct fohh_connection *connection)
{
    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();

    /* Send fohh connection info to peer */
    const char *pb_client_capabilities[] = {ZPN_CLIENT_CAPABILITY_ALT_CLOUD_AWARE_STR};
    struct fohh_info fohh_cookie;
    fohh_cookie.capabilities_count = 1;
    fohh_cookie.current_alt_cloud = gs->listener_alt_cloud_name;
    fohh_cookie.capabilities = pb_client_capabilities;

    fohh_send_info_with_capability(connection, &fohh_cookie);
}


void handle_alt_cloud_change(struct zpn_broker_redirect *req)
{
    int fresult = 0;
    char *alt_cloud = pbroker_get_redir_cloud_name();
    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();

    /*
     * alt_cloud pointer will never be NULL here. It will always point to valid
     * memory location. So we only check for req->alt_cloud being NULL
     */

    if(req->alt_cloud && (req->alt_cloud[0] != '\0')) {
        /* We recieved an alcloud value from broker_redirect. Now, compare with what we have */
        if(strncmp(alt_cloud, req->alt_cloud, PRIVATE_BROKER_CLOUD_NAME_LEN)) {

            /*
             * Update the alt_cloud with the new alt_cloud name.
             */
            snprintf_nowarn(gs->redir_alt_cloud_name, sizeof(gs->redir_alt_cloud_name), "%s", req->alt_cloud);

            ZPN_LOG(AL_INFO, "Alt cloud has changed from %s to %s", alt_cloud, req->alt_cloud);
            fresult = pbroker_store_alt_cloud_to_file(gs->listener_alt_cloud_name, gs->redir_alt_cloud_name);
            if(fresult) {
                ZPN_LOG(AL_ERROR, "Failed to update redir alt_cloud name:%s to local cache file. Error: %d", req->alt_cloud, fresult);
            }
            gs->rdir_alt_cloud_name_changed = 1;
        }
    } else {
        if(strncmp(alt_cloud, gs->cfg_key_cloud, PRIVATE_BROKER_CLOUD_NAME_LEN)) {
            /* We are in alt_cloud, so now switch to default alt_cloud */
            snprintf_nowarn(gs->redir_alt_cloud_name, sizeof(gs->redir_alt_cloud_name), "%s", gs->cfg_key_cloud);

            ZPN_LOG(AL_INFO, "Alt cloud has changed from %s to %s", gs->redir_alt_cloud_name, gs->cfg_key_cloud);
            fresult = pbroker_store_alt_cloud_to_file(gs->listener_alt_cloud_name, NULL);
            if(fresult) {
                ZPN_LOG(AL_ERROR, "Failed to clear redir alt_cloud name:%s from local cache file. Error: %d", gs->cfg_key_cloud, fresult);
            }
            gs->rdir_alt_cloud_name_changed = 1;
        }
    }
}

/*
 * The alt_cloud update we recieve here only affects the PSE to Broker connections. It does
 * not affect those connections, where PSE is acting as the server and recieves connections
 * from clients (like zcc and connector). Those changes will be triggered by updates to
 * zpn_private_broker_group
 */
int
zpn_private_broker_handle_redirect_request(void* argo_cookie_ptr, void* argo_structure_cookie_ptr, struct argo_object* object)
{
    struct fohh_connection *conn = argo_structure_cookie_ptr;
    struct zpn_broker_redirect *req = object->base_structure_void;
    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();
    char dump[5000];

    (void) argo_cookie_ptr;

    ZPN_LOG(AL_INFO, "Received zpn_broker_redirect for connection:%s", fohh_description(conn));

    /* Dump the broker redirect message */
    if (argo_object_dump(object, dump, sizeof(dump), NULL, 0) == ARGO_RESULT_NO_ERROR) {
        ZPN_LOG(AL_INFO, "Rx: %s", dump);
    }

    ZPATH_MUTEX_LOCK(&(gs->redirect_handler_lock), __FILE__, __LINE__);
    handle_alt_cloud_change(req);
    ZPATH_MUTEX_UNLOCK(&(gs->redirect_handler_lock), __FILE__, __LINE__);

    if (!gs->rdir_alt_cloud_name_changed && req->broker_count) {
        int result = fohh_redirect(conn, (const char **)req->brokers, req->broker_count, req->timestamp_s, req->sni_suffixes);
        if(result != FOHH_RESULT_NO_ERROR) {
            ZPN_LOG(AL_INFO, "fohh_redirect failed for connection:%s error:%s", fohh_description(conn), fohh_result_string(result));
        }
    } else if(!req->broker_count) {
        ZPN_LOG(AL_INFO, "broker redirect message returned 0 broker candidate for connection:%s", fohh_description(conn));
    }

    return ZPN_RESULT_NO_ERROR;
}

static int zpn_pbroker_version_ack_cb(void *argo_cookie_ptr __attribute__((unused)),
                                      void *argo_structure_cookie_ptr,
                                      struct argo_object *object)
{
    struct fohh_connection *f_conn = argo_structure_cookie_ptr;
    const struct zpn_version_ack *ack = object->base_structure_void;
    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();

    gs->version_data.zpn_version_check_end_us = epoch_us();
    char dump[8000];
    if (argo_object_dump(object, dump, sizeof(dump), NULL, 0) == ARGO_RESULT_NO_ERROR) {
        ZPN_LOG(AL_NOTICE, "%s: Rx: version_ack: %s",fohh_peer_cn(f_conn),dump);
    }

    ZPN_LOG(AL_NOTICE, "Received version_ack from broker,took %" PRId64 " us : error = %s",
           (gs->version_data.zpn_version_check_end_us - gs->version_data.zpn_version_check_start_us),
           ack->error ? : "NONE");

    if (ack->error) {
        ZPN_LOG(AL_ERROR, "%s: pbroker running incompatible version. version_ack from broker : error = %s", fohh_description(f_conn), ack->error);
        gs->version_data.client_version = zpn_client_version_invalid;
    } else {
        gs->version_data.client_version = zpn_client_version_valid;
    }
    return ZPN_RESULT_NO_ERROR;
}

static int
zpn_private_broker_cfg_conn_cb(struct fohh_connection*    connection,
                               enum fohh_connection_state state,
                               void*                      cookie)
{
    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();
    struct zpn_tlv tlv;
    tlv.type = zpn_fohh_tlv;
    tlv.conn.f_conn = connection;
    tlv.conn_incarnation = fohh_connection_incarnation(connection);
    tlv.tlv_incarnation = 0;
    int res = 0;

    if (state == fohh_connection_connected) {
        ZPN_LOG(AL_NOTICE, "%s - Config channel connected successfully", fohh_description(connection));

        /* Register zpn_version_ack */
        if ((res = argo_register_structure(fohh_argo_get_rx(connection), zpn_version_ack_description, zpn_pbroker_version_ack_cb, connection))) {
            ZPN_LOG(AL_ERROR, "Could not register zpn_pbroker_version_ack_cb for pbroker config connection %s", fohh_description(connection));
            return res;
        }

        gs->version_data.zpn_version_check_start_us = epoch_us();
        zpn_send_zpn_version(&tlv, zpn_tlv_conn_incarnation(&tlv), ZPATH_VERSION_MAJOR, ZPATH_VERSION_MINOR);

        res = argo_register_structure(fohh_argo_get_rx(connection),
                                                zpn_broker_redirect_description,
                                                zpn_private_broker_handle_redirect_request,
                                                connection);
        if (res) {
            ZPN_LOG(AL_ERROR, "Could not register broker_redirect for config connection %s",
                    fohh_description(connection));
            return res;
        }

        zpn_private_broker_send_fohh_info_with_alt_cloud_capability(connection);
    }

    return ZPN_RESULT_NO_ERROR;
}

int zpn_private_broker_userdb_conn_cb(struct fohh_connection *connection,
                                      enum fohh_connection_state state,
                                      void *cookie)
{
    if (state == fohh_connection_connected) {
        const int res = argo_register_structure(fohh_argo_get_rx(connection),
                                                zpn_broker_redirect_description,
                                                zpn_private_broker_handle_redirect_request,
                                                connection);
        if (res) {
            ZPN_LOG(AL_ERROR, "Could not register broker_redirect for config connection %s",
                    fohh_description(connection));
            return res;
        }

        zpn_private_broker_send_fohh_info_with_alt_cloud_capability(connection);
    }

    return ZPN_RESULT_NO_ERROR;
}



static int
zpn_private_broker_rcfg_conn_cb(struct fohh_connection*    connection,
                                enum fohh_connection_state state,
                                void*                      cookie)
{
    if (state == fohh_connection_connected) {
        const int res = argo_register_structure(fohh_argo_get_rx(connection),
                                                zpn_broker_redirect_description,
                                                zpn_private_broker_handle_redirect_request,
                                                connection);
        if (res) {
            ZPN_LOG(AL_ERROR, "Could not register broker_redirect for config connection %s",
                    fohh_description(connection));
            return res;
        }
    }

    return ZPN_RESULT_NO_ERROR;
}

void fill_dr_running_mode_for_pb(char *dr_running_mode) {
    FILE *fp;
    char file_content[DR_RUNNING_MODE_STATUS_LEN] = {0};
    fp = fopen(ZPN_DR_MODE_FLAG, "r");
    if (fp == NULL) {
        ZPN_LOG(AL_ERROR, "Failed to open marker file while reading DR mode");
        return;
    }
    if (fgets(file_content, DR_RUNNING_MODE_STATUS_LEN, fp) == NULL) {
        ZPN_LOG(AL_ERROR, "Could not read DR running mode for pbroker");
    }
    if (!strcmp(file_content, DNS_DR_ACTIVATION_ON_STR) ||
        !strcmp(file_content, DNS_DR_ACTIVATION_OFF_STR) ||
        !strcmp(file_content, DNS_DR_ACTIVATION_TEST_STR)) {
        strcpy(dr_running_mode, file_content);
    } else {
        ZPN_LOG(AL_ERROR, "Unidentified DR running mode %s in the marker file", file_content);
    }
    fclose(fp);
}

/* Private broker wally init */
struct zpn_private_broker_state*
zpn_private_broker_wally_init(int64_t     private_broker_id,
                              int         shard_index,
                              const char *cloud_name,
                              const char *broker,
                              const char *private_broker_name,
                              const char *cert_name,
                              int         auto_upgrade_disabled,
                              int         stats_log_to_disk,
                              int         use_sqlt, int dr_mode_enabled,
                              zpn_dr_wally_init_f *zpn_dr_wally_init,
                              const char *default_cloud_name)
{
    char hostname[256];
    struct wally_fohh_client *wally_fohh_client_handle = NULL;
    void *db_handle;
    struct wally_origin *slave_db;
    char db_name[ARGO_MAX_NAME_LENGTH];
    char str[ARGO_MAX_NAME_LENGTH];
    int is_endpoint = 0;

    char offline_hostname[266];
    char offline_sni_name[PRIVATE_BROKER_SNI_DOMAIN_LEN];
    const char* offline_domain = zpn_private_broker_get_offline_domain();
    int to_sitec = zpn_private_broker_site_is_sitec_eligible(offline_domain);

    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();
    struct zpn_private_broker_state *private_broker_state = gs->private_broker_state;

    //ZPN_LOG(AL_DEBUG, "Initializing Private Broker");

    if (!cloud_name) return NULL;
    if (!private_broker_id) return NULL;

    local_private_broker_id = private_broker_id;

    /*
     * simple_app_init have already init-ed role & build. We know the instance name only now and so let
     * us pass on that info to argo_log module.
     */
    if (argo_log_init(private_broker_name, private_broker_id, NULL, NULL, 0)) {
        ZPN_LOG(AL_ERROR, "Could not init argo library");
        goto err_end;
    }

    if(!dr_mode_enabled) {
        is_endpoint = 1;
    }


    /* Create wally for private broker config state */
    //ZPN_LOG(AL_DEBUG, "Creating Private Broker Wally...");
    private_broker_state->wally = wally_create("Private_Broker_Wally",
                                                is_endpoint,
                                                zpath_debug_wally_endpoints_init,
                                                NULL,
                                                NULL,
                                                NULL);
    if (!private_broker_state->wally) {
        ZPN_LOG(AL_ERROR, "Could not create zpa private broker wally");
        goto err_end;
    }

    if (use_sqlt) {
        snprintf(db_name, sizeof(db_name), "%s.%d", ZPATH_LOCAL_SLAVE_DB_NAME, shard_index);
        ZPATH_LOG(AL_NOTICE, "Attaching to local DB (zpath_global_slave) as %s.", db_name);
        db_handle = wally_sqlt_create(db_name, /* db_name */
                                      "local_sqlt", /* thread_name */
                                      4,    /* nconns */
                                      1,    /* is_row_writable */
                                      0,    /* is_true_origin */
                                      wally_is_endpoint(private_broker_state->wally),
                                      0);   /* polling_interval_us */
        if (!db_handle) {
            ZPATH_LOG(AL_ERROR, "wally_sqlt_create failed for database %s", db_name);
            goto err_end;
        }

        snprintf_nowarn(str, sizeof(str), "sqlt:%s", db_name);
        slave_db = wally_add_origin(private_broker_state->wally,
                                    str,
                                    db_handle,
                                    wally_db_register_for_index,
                                    wally_db_deregister_for_index,
                                    wally_db_set_cookie,
                                    wally_db_get_status,
                                    NULL,
                                    wally_db_set_min_sequence,
                                    wally_db_dump_state,
                                    1);
        if (!slave_db) {
            ZPATH_LOG(AL_ERROR, "wally_add_origin failed");
            goto err_end;
        }
        private_broker_state->slave_db = slave_db;
    }

    if (broker) {
        snprintf(hostname, sizeof(hostname), "%s", broker);
    } else {
        snprintf(hostname, sizeof(hostname), "%s.%s", ZPN_PRIVATE_BROKER_TO_PUBLIC_BROKER_DNS_NAME, cloud_name);
    }

    if (snprintf(offline_hostname, sizeof(offline_hostname),
            "%s.%s", ZPN_PRIVATE_BROKER_TO_SITE_CONTROLLER_DNS_NAME, offline_domain) >= sizeof(offline_hostname)) {
        ZPN_LOG(AL_ERROR, "String is truncated when creating offline_hostname");
    }
    if (snprintf(offline_sni_name, sizeof(offline_sni_name),
            "%ld.pbcfg.%s", (long) private_broker_id, offline_domain) >= sizeof(offline_sni_name)) {
        ZPN_LOG(AL_ERROR, "String is truncated when creating offline_sni_name");
    }

    char* remote_host_name = to_sitec ? offline_hostname : hostname;
    char* sni_service_name = to_sitec ? offline_sni_name : pb_sni_customer_domain_cfg;
    const char* sni_suffix = to_sitec ? offline_domain : cloud_name;
    uint16_t service_port = to_sitec ? ZPN_PRIVATE_BROKER_TO_SITE_CONTROLLER_PORT : ZPN_PRIVATE_BROKER_TO_PUBLIC_BROKER_PORT;
    SSL_CTX* ssl_ctx = to_sitec ? gs->self_to_private_ssl_ctx : gs->self_to_broker_ssl_ctx;

    if(!dr_mode_enabled) {
        wally_fohh_client_handle = wally_fohh_client_create_using_ctx(private_broker_state->wally,
                NULL,
                remote_host_name,
                sni_service_name,
                (char*)sni_suffix,
                htons(service_port),
                zpn_private_broker_cfg_conn_cb,
                ssl_ctx);
        if (!wally_fohh_client_handle) {
            ZPN_LOG(AL_ERROR, "Could not create fohh origin");
            goto err_end;
        }

        private_broker_state->cfg_fohh_client_handle = wally_fohh_client_get_f_conn(wally_fohh_client_handle);

        zpn_private_broker_site_register_fohh(private_broker_state->cfg_fohh_client_handle, "pbcfg",
                hostname, pb_sni_customer_domain_cfg, cloud_name, 0, to_sitec);

        /* Make the config connection sticky */
        fohh_set_sticky(wally_fohh_client_get_f_conn(wally_fohh_client_handle), 1);

        fohh_connection_monitor_sanity(private_broker_state->cfg_fohh_client_handle,
                                    zpn_private_broker_fohh_connection_sanity_callback_with_lock,
                                    ZPN_PRIVATE_BROKER_ALT_CLOUD_CONN_MAX_INIT_TIME_S);
        /*
         * Set status interval address, which has the interval in seconds for sending fohh_status_request messages by connected broker.
         * Default status interval is 1 second for sending fohh_status_request messages.
         */
        fohh_set_status_interval(private_broker_state->cfg_fohh_client_handle, &(gs->cfg_status_interval));

        fohh_connection_set_default_sni(private_broker_state->cfg_fohh_client_handle,
                                        (char *)default_cloud_name);

        private_broker_state->remote_db = wally_add_origin(private_broker_state->wally,
                remote_host_name,
                wally_fohh_client_handle,
                wally_fohh_register_for_index,
                wally_fohh_deregister_for_index,
                wally_fohh_set_cookie,
                wally_fohh_get_status,
                wally_fohh_add_table,
                NULL,
                wally_fohh_dump_state,
                1);
        if (!private_broker_state->remote_db) {
            ZPN_LOG(AL_ERROR, "Could not add remote origin");
            goto err_end;
        }
    } else {

        /*
         * This will create a local dr_wally test origin for private broker config state.
         * Data/rows for this test origin will be statically loaded based on the config json file into appropriate tables.
         * Data/rows for Private_Broker_Wally will be served from dr_wally test origin based on registration request.
         */

        (zpn_dr_wally_init)( "dr_wally",
                           private_broker_state->wally,
                           &private_broker_state->wally_test_origin,
                           &private_broker_state->remote_db);
        private_broker_state->drmode_enabled = 1;
        fill_dr_running_mode_for_pb(private_broker_state->dr_running_mode);
    }

    zpath_debug_wally_add(private_broker_state->wally, 0);

    if(!dr_mode_enabled) {
        private_broker_state->wally_fohh_client_handle = wally_fohh_client_handle;
    }

    /* Init zpn_pbroker_client_connection_stats */
    ZPN_ATOMIC_STORE(&private_broker_state->client_connection_stats.total_active_client_connections_count, 0);
    ZPN_ATOMIC_STORE(&private_broker_state->client_connection_stats.total_client_connections_init_failed, 0);
    ZPN_ATOMIC_STORE(&private_broker_state->client_connection_stats.total_client_connections_created, 0);
    ZPN_ATOMIC_STORE(&private_broker_state->client_connection_stats.total_client_connections_freed, 0);
    ZPN_ATOMIC_STORE(&private_broker_state->client_connection_stats.approx_peak_active_client_connections_count, 0);

    ZPN_ATOMIC_STORE(&private_broker_state->client_connection_stats.zcc_connections_created, 0);
    ZPN_ATOMIC_STORE(&private_broker_state->client_connection_stats.zcc_connections_freed, 0);
    ZPN_ATOMIC_STORE(&private_broker_state->client_connection_stats.zcc_active_connections_count, 0);
    ZPN_ATOMIC_STORE(&private_broker_state->client_connection_stats.zcc_connections_init_failed, 0);

    ZPN_ATOMIC_STORE(&private_broker_state->client_connection_stats.branch_connector_connections_created, 0);
    ZPN_ATOMIC_STORE(&private_broker_state->client_connection_stats.branch_connector_connections_freed, 0);
    ZPN_ATOMIC_STORE(&private_broker_state->client_connection_stats.branch_connector_active_connections_count, 0);
    ZPN_ATOMIC_STORE(&private_broker_state->client_connection_stats.branch_connector_connections_init_failed, 0);

    ZPN_ATOMIC_STORE(&private_broker_state->client_connection_stats.edge_connector_connections_created, 0);
    ZPN_ATOMIC_STORE(&private_broker_state->client_connection_stats.edge_connector_connections_freed, 0);
    ZPN_ATOMIC_STORE(&private_broker_state->client_connection_stats.edge_connector_active_connections_count, 0);
    ZPN_ATOMIC_STORE(&private_broker_state->client_connection_stats.edge_connector_connections_init_failed, 0);

    ZPN_ATOMIC_STORE(&private_broker_state->client_connection_stats.vdi_active_connections_count, 0);
    ZPN_ATOMIC_STORE(&private_broker_state->client_connection_stats.vdi_connections_freed, 0);
    ZPN_ATOMIC_STORE(&private_broker_state->client_connection_stats.vdi_connections_created, 0);
    ZPN_ATOMIC_STORE(&private_broker_state->client_connection_stats.vdi_connections_init_failed, 0);

    ZPN_ATOMIC_STORE(&private_broker_state->client_connection_stats.machine_tunnel_connections_created, 0);
    ZPN_ATOMIC_STORE(&private_broker_state->client_connection_stats.machine_tunnel_connections_freed, 0);
    ZPN_ATOMIC_STORE(&private_broker_state->client_connection_stats.machine_tunnel_active_connections_count, 0);
    ZPN_ATOMIC_STORE(&private_broker_state->client_connection_stats.machine_tunnel_connections_init_failed, 0);

    ZPN_ATOMIC_STORE(&private_broker_state->client_connection_stats.other_client_connections_created, 0);
    ZPN_ATOMIC_STORE(&private_broker_state->client_connection_stats.other_client_connections_freed, 0);
    ZPN_ATOMIC_STORE(&private_broker_state->client_connection_stats.other_client_active_connections_count, 0);
    ZPN_ATOMIC_STORE(&private_broker_state->client_connection_stats.other_client_connections_init_failed, 0);

    /* Init zpn_private_broker_mtunnel_stats */

    struct zpn_private_broker_mtunnel_stats *mtstats = &private_broker_state->mtunnel_stats;

    ZPN_ATOMIC_STORE(&mtstats->total_mtunnels_created, 0);
    ZPN_ATOMIC_STORE(&mtstats->total_mtunnels_freed, 0);
    ZPN_ATOMIC_STORE(&mtstats->total_mtunnels_reaped_in, 0);
    ZPN_ATOMIC_STORE(&mtstats->total_mtunnels_reaped_out, 0);
    ZPN_ATOMIC_STORE(&mtstats->total_mtunnels_active, 0);
    ZPN_ATOMIC_STORE(&mtstats->total_mtunnels_init_failed, 0);
    ZPN_ATOMIC_STORE(&mtstats->approx_mtunnels_peak_active, 0);
    ZPN_ATOMIC_STORE(&mtstats->total_mtunnels_c2c_regex_bypass, 0);

    ZPN_ATOMIC_STORE(&mtstats->zcc_mtunnels_created, 0);
    ZPN_ATOMIC_STORE(&mtstats->zcc_mtunnels_freed, 0);
    ZPN_ATOMIC_STORE(&mtstats->zcc_mtunnels_reaped_in, 0);
    ZPN_ATOMIC_STORE(&mtstats->zcc_mtunnels_reaped_out, 0);
    ZPN_ATOMIC_STORE(&mtstats->zcc_mtunnels_active, 0);
    ZPN_ATOMIC_STORE(&mtstats->zcc_mtunnels_init_failed, 0);
    ZPN_ATOMIC_STORE(&mtstats->zcc_mtunnels_c2c_regex_bypass, 0);

    ZPN_ATOMIC_STORE(&mtstats->ec_mtunnels_created, 0);
    ZPN_ATOMIC_STORE(&mtstats->ec_mtunnels_freed, 0);
    ZPN_ATOMIC_STORE(&mtstats->ec_mtunnels_reaped_in, 0);
    ZPN_ATOMIC_STORE(&mtstats->ec_mtunnels_reaped_out, 0);
    ZPN_ATOMIC_STORE(&mtstats->ec_mtunnels_active, 0);
    ZPN_ATOMIC_STORE(&mtstats->ec_mtunnels_init_failed, 0);
    ZPN_ATOMIC_STORE(&mtstats->ec_mtunnels_c2c_regex_bypass, 0);

    ZPN_ATOMIC_STORE(&mtstats->vdi_mtunnels_created, 0);
    ZPN_ATOMIC_STORE(&mtstats->vdi_mtunnels_freed, 0);
    ZPN_ATOMIC_STORE(&mtstats->vdi_mtunnels_reaped_in, 0);
    ZPN_ATOMIC_STORE(&mtstats->vdi_mtunnels_reaped_out, 0);
    ZPN_ATOMIC_STORE(&mtstats->vdi_mtunnels_active, 0);
    ZPN_ATOMIC_STORE(&mtstats->vdi_mtunnels_init_failed, 0);
    ZPN_ATOMIC_STORE(&mtstats->vdi_mtunnels_c2c_regex_bypass, 0);

    ZPN_ATOMIC_STORE(&mtstats->bc_mtunnels_created, 0);
    ZPN_ATOMIC_STORE(&mtstats->bc_mtunnels_freed, 0);
    ZPN_ATOMIC_STORE(&mtstats->bc_mtunnels_reaped_in, 0);
    ZPN_ATOMIC_STORE(&mtstats->bc_mtunnels_reaped_out, 0);
    ZPN_ATOMIC_STORE(&mtstats->bc_mtunnels_active, 0);
    ZPN_ATOMIC_STORE(&mtstats->bc_mtunnels_init_failed, 0);
    ZPN_ATOMIC_STORE(&mtstats->bc_mtunnels_c2c_regex_bypass, 0);

    ZPN_ATOMIC_STORE(&mtstats->mt_mtunnels_created, 0);
    ZPN_ATOMIC_STORE(&mtstats->mt_mtunnels_freed, 0);
    ZPN_ATOMIC_STORE(&mtstats->mt_mtunnels_reaped_in, 0);
    ZPN_ATOMIC_STORE(&mtstats->mt_mtunnels_reaped_out, 0);
    ZPN_ATOMIC_STORE(&mtstats->mt_mtunnels_active, 0);
    ZPN_ATOMIC_STORE(&mtstats->mt_mtunnels_init_failed, 0);
    ZPN_ATOMIC_STORE(&mtstats->mt_mtunnels_c2c_regex_bypass, 0);

    ZPN_ATOMIC_STORE(&mtstats->other_mtunnels_created, 0);
    ZPN_ATOMIC_STORE(&mtstats->other_mtunnels_freed, 0);
    ZPN_ATOMIC_STORE(&mtstats->other_mtunnels_reaped_in, 0);
    ZPN_ATOMIC_STORE(&mtstats->other_mtunnels_reaped_out, 0);
    ZPN_ATOMIC_STORE(&mtstats->other_mtunnels_active, 0);
    ZPN_ATOMIC_STORE(&mtstats->other_mtunnels_init_failed, 0);
    ZPN_ATOMIC_STORE(&mtstats->other_mtunnels_c2c_regex_bypass, 0);

    /* Init zpn_private_broker_dispatcher_stats */
    struct zpn_private_broker_dispatcher_stats *dispatch_stats = &private_broker_state->dispatcher_stats;
    ZPN_ATOMIC_STORE(&dispatch_stats->total_cache_dispatch_hit_count, 0);
    ZPN_ATOMIC_STORE(&dispatch_stats->total_cache_dispatch_miss_count, 0);
    ZPN_ATOMIC_STORE(&dispatch_stats->total_max_dispatch_exceeded_count, 0);
    ZPN_ATOMIC_STORE(&dispatch_stats->total_cloud_dispatch_count, 0);
    ZPN_ATOMIC_STORE(&dispatch_stats->total_cloud_dispatch_fail_count, 0);
    ZPN_ATOMIC_STORE(&dispatch_stats->local_dispatcher_state_eval_avg_time_us, 0);
    ZPN_ATOMIC_STORE(&dispatch_stats->local_dispatcher_state_eval_max_time_us, 0);

    return private_broker_state;

err_end:
    return NULL;
}

/* Private broker static wally init */
int
zpn_private_broker_static_wally_init(struct zpn_private_broker_state* private_broker_state,
                                     int         shard_index,
                                     const char *cloud_name,
                                     const char *broker,
                                     int         use_sqlt,
                                     int dr_mode_enabled,
                                     const char *default_cloud_name)
{
   /* No DR for Static Wally to keep it simple!! for now */
    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();
    char hostname[256];
    struct wally_fohh_client *wally_fohh_client_handle = NULL;
    void *db_handle;
    struct wally_origin *slave_db;
    char db_name[ARGO_MAX_NAME_LENGTH];
    char str[ARGO_MAX_NAME_LENGTH];
    int is_endpoint = 0;

    char offline_hostname[266];
    char offline_sni_name[PRIVATE_BROKER_SNI_DOMAIN_LEN];
    const char* offline_domain = zpn_private_broker_get_offline_domain();
    int to_sitec = zpn_private_broker_site_is_sitec_eligible(offline_domain);

    //ZPN_LOG(AL_DEBUG, "Initializing Private Broker");

    if (!private_broker_state) return ZPN_RESULT_ERR;
    if (!cloud_name) return ZPN_RESULT_ERR;

    if(!dr_mode_enabled) {
        is_endpoint = 1;
    }


    /* Create wally for private broker config state */
    //ZPN_LOG(AL_DEBUG, "Creating Private Broker Wally...");
    private_broker_state->static_wally = wally_create("Private_Broker_Static_Wally",
                                                      is_endpoint,
                                                      zpath_debug_wally_endpoints_init,
                                                      NULL,
                                                      NULL,
                                                      NULL);
    if (!private_broker_state->static_wally) {
        ZPN_LOG(AL_ERROR, "Could not create zpa private broker static wally");
        goto err_end;
    }

    if (use_sqlt) {
        snprintf(db_name, sizeof(db_name), "%s.%d", ZPATH_LOCAL_SLAVE_DB_NAME, shard_index);
        ZPATH_LOG(AL_NOTICE, "Attaching to local DB (zpath_global_slave) as %s.", db_name);
        db_handle = wally_sqlt_create(db_name, /* db_name */
                                      "local_sqlt", /* thread_name */
                                      4,    /* nconns */
                                      1,    /* is_row_writable */
                                      0,    /* is_true_origin */
                                      wally_is_endpoint(private_broker_state->static_wally),
                                      0);   /* polling_interval_us */
        if (!db_handle) {
            ZPATH_LOG(AL_ERROR, "wally_sqlt_create failed for database %s", db_name);
            goto err_end;
        }

        snprintf_nowarn(str, sizeof(str), "sqlt:%s", db_name);
        slave_db = wally_add_origin(private_broker_state->static_wally,
                                    str,
                                    db_handle,
                                    wally_db_register_for_index,
                                    wally_db_deregister_for_index,
                                    wally_db_set_cookie,
                                    wally_db_get_status,
                                    NULL,
                                    wally_db_set_min_sequence,
                                    wally_db_dump_state,
                                    1);
        if (!slave_db) {
            ZPATH_LOG(AL_ERROR, "wally_add_origin failed");
            goto err_end;
        }
        private_broker_state->static_slave_db = slave_db;
    }

    if (broker) {
        snprintf(hostname, sizeof(hostname), "%s", broker);
    } else {
        snprintf(hostname, sizeof(hostname), "%s.%s", ZPN_PRIVATE_BROKER_TO_PUBLIC_BROKER_DNS_NAME, cloud_name);
    }

    if (snprintf(offline_hostname, sizeof(offline_hostname),
            "%s.%s", ZPN_PRIVATE_BROKER_TO_SITE_CONTROLLER_DNS_NAME, offline_domain) >= sizeof(offline_hostname)) {
        ZPN_LOG(AL_ERROR, "String is truncated when creating offline_hostname");
    }
    if (snprintf(offline_sni_name, sizeof(offline_sni_name),
            "%ld.pbrcfg.%s", (long) gs->private_broker_id, offline_domain) >= sizeof(offline_sni_name)) {
        ZPN_LOG(AL_ERROR, "String is truncated when creating offline_sni_name");
    }

    char* remote_host_name = to_sitec ? offline_hostname : hostname;
    char* sni_service_name = to_sitec ? offline_sni_name : pb_sni_customer_domain_rcfg;
    const char* sni_suffix = to_sitec ? offline_domain : cloud_name;
    uint16_t service_port = to_sitec ? ZPN_PRIVATE_BROKER_TO_SITE_CONTROLLER_PORT : ZPN_PRIVATE_BROKER_TO_PUBLIC_BROKER_PORT;
    SSL_CTX* ssl_ctx = to_sitec ? gs->self_to_private_ssl_ctx : gs->self_to_broker_ssl_ctx;

    if(!dr_mode_enabled) {
        wally_fohh_client_handle = wally_fohh_client_create_using_ctx(private_broker_state->static_wally,
                NULL,
                remote_host_name,
                sni_service_name,
                (char *)sni_suffix,
                htons(service_port),
                zpn_private_broker_rcfg_conn_cb,
                ssl_ctx);
        if (!wally_fohh_client_handle) {
            ZPN_LOG(AL_ERROR, "Could not create fohh origin");
            goto err_end;
        }

        private_broker_state->rcfg_fohh_client_handle = wally_fohh_client_get_f_conn(wally_fohh_client_handle);

        zpn_private_broker_site_register_fohh(private_broker_state->rcfg_fohh_client_handle, "pbrcfg",
                hostname, pb_sni_customer_domain_rcfg, cloud_name, 0, to_sitec);

        /* Make the config connection sticky */
        fohh_set_sticky(wally_fohh_client_get_f_conn(wally_fohh_client_handle), 1);

        fohh_connection_monitor_sanity(private_broker_state->rcfg_fohh_client_handle,
                                    zpn_private_broker_fohh_connection_sanity_callback_with_lock,
                                    ZPN_PRIVATE_BROKER_ALT_CLOUD_CONN_MAX_INIT_TIME_S);
        /*
         * Set status interval address, which has the interval in seconds for sending fohh_status_request messages by connected broker.
         * Default status interval is 1 second for sending fohh_status_request messages.
         */
        fohh_set_status_interval(private_broker_state->rcfg_fohh_client_handle, &(gs->rcfg_status_interval));

        fohh_connection_set_default_sni(private_broker_state->rcfg_fohh_client_handle,
                                        (char *)default_cloud_name);

        private_broker_state->static_remote_db = wally_add_origin(private_broker_state->static_wally,
                remote_host_name,
                wally_fohh_client_handle,
                wally_fohh_register_for_index,
                wally_fohh_deregister_for_index,
                wally_fohh_set_cookie,
                wally_fohh_get_status,
                wally_fohh_add_table,
                NULL,
                wally_fohh_dump_state,
                1);
        if (!private_broker_state->static_remote_db) {
            ZPN_LOG(AL_ERROR, "Could not add remote origin");
            goto err_end;
        }
    }

    zpath_debug_wally_add(private_broker_state->static_wally, 0);

    if(!dr_mode_enabled) {
        private_broker_state->static_wally_fohh_client_handle = wally_fohh_client_handle;
    }
    return ZPN_RESULT_NO_ERROR;

err_end:
    ZPN_FREE(private_broker_state);
    return ZPN_RESULT_ERR;
}


#define MAX_CONFIGS 100

/* Private broker wally override init */
int
zpn_private_broker_wally_override_init(struct zpn_private_broker_state* private_broker_state,
                                       const char *cloud_name,
                                       const char *broker,
                                       int64_t customer_gid,
                                       int use_sqlt,
                                       int drmode,
                                       zpn_dr_wally_init_f *zpn_dr_wally_init,
                                       const char *default_cloud_name)
{
    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();
    char hostname[256];
    struct wally_fohh_client *wally_fohh_client_handle = NULL;
    void *db_handle;
    struct wally_origin *slave_db;
    char db_name[ARGO_MAX_NAME_LENGTH];
    char str[ARGO_MAX_NAME_LENGTH];
    int end_point = 0;

    char offline_hostname[266];
    char offline_sni_name[PRIVATE_BROKER_SNI_DOMAIN_LEN];
    const char* offline_domain = zpn_private_broker_get_offline_domain();
    int to_sitec = zpn_private_broker_site_is_sitec_eligible(offline_domain);

    if (!private_broker_state) return ZPN_RESULT_ERR;
    if (!cloud_name) return ZPN_RESULT_ERR;

    if(!drmode)
        end_point = 1;

    private_broker_state->wally_ovd = wally_create("Private_Broker_Wally_Override",
                                                end_point,
                                                zpath_debug_wally_endpoints_init,
                                                NULL,
                                                NULL,
                                                NULL);
    if (!private_broker_state->wally_ovd) {
        ZPN_LOG(AL_ERROR, "Could not create zpa private broker wally override");
        return ZPN_RESULT_ERR;
    }

    if (use_sqlt) {
        snprintf(db_name, sizeof(db_name), "%s", ZPATH_LOCAL_SLAVE_DB_NAME);
        ZPATH_LOG(AL_NOTICE, "Attaching to local DB (zpath_global_slave) as %s.", db_name);
        db_handle = wally_sqlt_create(db_name, /* db_name */
                                      "local_sqlt", /* thread_name */
                                      4,    /* nconns */
                                      1,    /* is_row_writable */
                                      0,    /* is_true_origin */
                                      wally_is_endpoint(private_broker_state->wally),
                                      0);   /* polling_interval_us */
        if (!db_handle) {
            ZPATH_LOG(AL_ERROR, "wally_sqlt_create failed for database %s", db_name);
            return ZPN_RESULT_ERR;
        }

        snprintf_nowarn(str, sizeof(str), "sqlt:%s", db_name);
        slave_db = wally_add_origin(private_broker_state->wally_ovd,
                                    str,
                                    db_handle,
                                    wally_db_register_for_index,
                                    wally_db_deregister_for_index,
                                    wally_db_set_cookie,
                                    wally_db_get_status,
                                    NULL,
                                    wally_db_set_min_sequence,
                                    wally_db_dump_state,
                                    1);
        if (!slave_db) {
            ZPATH_LOG(AL_ERROR, "wally_add_origin failed");
            return ZPN_RESULT_ERR;
        }
        private_broker_state->slave_db_ovd = slave_db;
    }

    if (broker) {
        snprintf(hostname, sizeof(hostname), "%s", broker);
    } else {
        snprintf(hostname, sizeof(hostname), "%s.%s", ZPN_PRIVATE_BROKER_TO_PUBLIC_BROKER_DNS_NAME, cloud_name);
    }

    if (snprintf(offline_hostname, sizeof(offline_hostname),
            "%s.%s", ZPN_PRIVATE_BROKER_TO_SITE_CONTROLLER_DNS_NAME, offline_domain) >= sizeof(offline_hostname)) {
        ZPN_LOG(AL_ERROR, "String is truncated when creating offline_hostname");
    }
    if (snprintf(offline_sni_name, sizeof(offline_sni_name),
            "%ld.pbovd.%s", (long) gs->private_broker_id, offline_domain) >= sizeof(offline_sni_name)) {
        ZPN_LOG(AL_ERROR, "String is truncated when creating offline_sni_name");
    }

    char* remote_host_name = to_sitec ? offline_hostname : hostname;
    char* sni_service_name = to_sitec ? offline_sni_name : pb_sni_customer_domain_ovd;
    const char* sni_suffix = to_sitec ? offline_domain : cloud_name;
    uint16_t service_port = to_sitec ? ZPN_PRIVATE_BROKER_TO_SITE_CONTROLLER_PORT : ZPN_PRIVATE_BROKER_TO_PUBLIC_BROKER_PORT;
    SSL_CTX* ssl_ctx = to_sitec ? gs->self_to_private_ssl_ctx : gs->self_to_broker_ssl_ctx;

    if(!drmode) {
        wally_fohh_client_handle = wally_fohh_client_create_using_ctx(private_broker_state->wally_ovd,
                NULL,
                remote_host_name,
                sni_service_name,
                (char *)sni_suffix,
                htons(service_port),
                zpn_private_broker_cfg_conn_cb,
                ssl_ctx);
        if (!wally_fohh_client_handle) {
            ZPN_LOG(AL_ERROR, "Could not create fohh origin for overide");
            return ZPN_RESULT_ERR;
        }

        private_broker_state->remote_db_ovd = wally_add_origin(private_broker_state->wally_ovd,
                                                           remote_host_name,
                                                           wally_fohh_client_handle,
                                                           wally_fohh_register_for_index,
                                                           wally_fohh_deregister_for_index,
                                                           wally_fohh_set_cookie,
                                                           wally_fohh_get_status,
                                                           wally_fohh_add_table,
                                                           NULL,
                                                           wally_fohh_dump_state,
                                                           1);
        if (!private_broker_state->remote_db_ovd) {
            ZPN_LOG(AL_ERROR, "Could not add remote origin");
            return ZPN_RESULT_ERR;
        }
        private_broker_state->wally_fohh_client_handle_ovd = wally_fohh_client_handle;
        private_broker_state->ovd_fohh_client_handle = wally_fohh_client_get_f_conn(wally_fohh_client_handle);

        zpn_private_broker_site_register_fohh(private_broker_state->ovd_fohh_client_handle, "pbovd",
                hostname, pb_sni_customer_domain_ovd, cloud_name, 0, to_sitec);

        fohh_connection_monitor_sanity(private_broker_state->ovd_fohh_client_handle,
                                    zpn_private_broker_fohh_connection_sanity_callback_with_lock,
                                    ZPN_PRIVATE_BROKER_ALT_CLOUD_CONN_MAX_INIT_TIME_S);
        /*
         * Set status interval address, which has the interval in seconds for sending fohh_status_request messages by connected broker.
         * Default status interval is 1 second for sending fohh_status_request messages.
         */
        fohh_set_status_interval(private_broker_state->ovd_fohh_client_handle, &(gs->ovd_status_interval));

        fohh_connection_set_default_sni(private_broker_state->ovd_fohh_client_handle,
                                        (char *)default_cloud_name);
    } else {

        /*
         * This will create a local dr_ovd_wally test origin for private broker config ovd state.
         * Data/rows for this test origin will be statically loaded based on the config ovd json file into appropriate tables.
         * Data/rows for Private_Broker_Wally_Override will be served from dr_ovd_wally test origin based on registration request.
         */

        (zpn_dr_wally_init)( "dr_ovd_wally",
                           private_broker_state->wally_ovd,
                           &private_broker_state->wally_ovd_test_origin,
                           &private_broker_state->remote_db_ovd);
    }

    zpath_debug_wally_add(private_broker_state->wally_ovd, 0);

    return ZPN_RESULT_NO_ERROR;
}

char*
pb_get_override_server_cn()
{
    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();
    if (!gs || !gs->private_broker_state || !gs->private_broker_state->wally_fohh_client_handle_ovd) {
        return "";
    }

    struct fohh_connection *connection = wally_fohh_client_get_f_conn(gs->private_broker_state->wally_fohh_client_handle_ovd);
    if (!connection) {
        return "";
    }
    return fohh_peer_cn(connection);
}

/* Init private ssl context, call this early during startup */
int zpn_private_broker_init_self_to_private_ssl_ctx(char *f_root, char *f_cert, char *f_key_pem)
{

    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();

    if (!gs) {
        ZPN_LOG(AL_CRITICAL, "No global state for init ssl ctx");
        return ZPN_RESULT_BAD_STATE;
    }

    if ( !f_root || !f_cert || !f_key_pem) {
        ZPN_LOG(AL_CRITICAL, "Invalid null file name for ssl ctx file arg params");
        return ZPN_RESULT_BAD_ARGUMENT;
    }

    if (!gs->cfg_pkey) {
        ZPN_LOG(AL_CRITICAL, "Invalid null pkey for init ssl ctx");
        return ZPN_RESULT_BAD_STATE;
    }

    /* Init the regular TLS ctx */
    {
        pthread_mutex_lock(&gs->lock);
        gs->self_to_private_ssl_ctx = fohh_client_ssl_ctx_create(f_root,            // peer_trusted_root_cert_pem_filename,
                                                                 f_cert,            // client_cert_pem_filename,
                                                                 f_key_pem,         // client_key_pem_filename,
                                                                 gs->cfg_pkey);     // EVP_PKEY

        if (!gs->self_to_private_ssl_ctx) {
            ZPN_LOG(AL_CRITICAL, "Unable to create private ssl_ctx");
            pthread_mutex_unlock(&gs->lock);
            return ZPN_RESULT_BAD_STATE;
        }
        pthread_mutex_unlock(&gs->lock);
    }

    return ZPN_RESULT_NO_ERROR;
}

/* Init global ssl context, call this early during startup */
int zpn_private_broker_init_self_to_broker_ssl_ctx(char *f_cloud, char *f_cert, char *f_key_pem)
{

    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();

    if (!gs) {
        ZPN_LOG(AL_CRITICAL, "No global state for init ssl ctx");
        return ZPN_RESULT_BAD_STATE;
    }

    if ( !f_cloud || !f_cert || !f_key_pem) {
        ZPN_LOG(AL_CRITICAL, "Invalid null file name for ssl ctx file arg params");
        return ZPN_RESULT_BAD_ARGUMENT;
    }

    if (!gs->cfg_pkey) {
        ZPN_LOG(AL_CRITICAL, "Invalid null pkey for init ssl ctx");
        return ZPN_RESULT_BAD_STATE;
    }

    /* Init the regular TLS ctx */
    {
        pthread_mutex_lock(&gs->lock);
        gs->self_to_broker_ssl_ctx = fohh_client_ssl_ctx_create(f_cloud,           // peer_trusted_root_cert_pem_filename,
                                                                f_cert,            // client_cert_pem_filename,
                                                                f_key_pem,         // client_key_pem_filename,
                                                                gs->cfg_pkey);     // EVP_PKEY

        if (!gs->self_to_broker_ssl_ctx) {
            ZPN_LOG(AL_CRITICAL, "Unable to create global ssl_ctx");
            pthread_mutex_unlock(&gs->lock);
            return ZPN_RESULT_BAD_STATE;
        }
        pthread_mutex_unlock(&gs->lock);
    }

    /* Init the DTLS ctx */
    {
        pthread_mutex_lock(&gs->lock);
        gs->self_to_broker_dtls_ctx = zdtls_client_ssl_ctx_create(f_cloud,         // peer_trusted_root_cert_pem_filename,
                                                                  f_cert,          // client_cert_pem_filename,
                                                                  f_key_pem,       // client_key_pem_filename,
                                                                  gs->cfg_pkey);   // EVP_PKEY
        if (!gs->self_to_broker_dtls_ctx) {
            ZPN_LOG(AL_CRITICAL, "Unable to create global dtls_ctx");
            pthread_mutex_unlock(&gs->lock);
            return ZPN_RESULT_BAD_STATE;
        }
        pthread_mutex_unlock(&gs->lock);
    }

    return ZPN_RESULT_NO_ERROR;
}

/* Get global ssl ctx, should not need lock as it is never changed after initially being allocated */
SSL_CTX * zpn_private_broker_get_self_to_broker_ssl_ctx(void)
{
    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();

    if ( !gs || !gs->self_to_broker_ssl_ctx) {
        ZPN_LOG(AL_CRITICAL, "No global state for get ssl ctx");
        return NULL;
    }

    return gs->self_to_broker_ssl_ctx;
}

int pbroker_get_num_hw_id_changed(void)
{
    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();

    return gs->number_of_hw_id_changed;
}

int64_t* pbroker_get_hw_id_changed_time_us(void)
{
    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();

    return gs->hw_id_changed_time_us;
}

/*
 * 1 if the broker_control is connected, 0 otherwise
 */
int pbroker_broker_control_is_connected(void)
{

    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();

    if (!gs->private_broker_state->broker_control) {
        return 0;
    }

    if (fohh_connection_connected == fohh_get_state(gs->private_broker_state->broker_control)) {
        return 1;
    }

    return 0;
}

char *pbroker_get_name_by_id(const int64_t *private_broker_id)
{
    struct  zpn_private_broker *pbroker     = NULL;
    int     res                             = 0;
    char    *pbroker_name                   = NULL;

    if(!private_broker_id) {
        ZPN_LOG(AL_ERROR, "Invalid Private broker id");
        return NULL;
    }
    res = zpn_private_broker_get_by_id( *private_broker_id, &pbroker, NULL, NULL, 0);
    if(ZPATH_RESULT_NO_ERROR != res) {
        ZPN_LOG(AL_ERROR, "Could not get private broker for id = %ld: %s",
                (long) *private_broker_id, zpn_result_string(res));
    }
    else {
        // return the name of private broker
        if(pbroker && pbroker->name)
            pbroker_name = pbroker->name;
    }
    return pbroker_name;
}

char* pbroker_get_configured_name(void)
{
    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();

    return gs->cfg_name;
}

void pbroker_set_hw_id_changed_and_log_time_us(int64_t id_changed_time_us, int num_of_hw_id_changed)
{
    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();

    gs->number_of_hw_id_changed = num_of_hw_id_changed;
    gs->hw_id_changed_time_us[num_of_hw_id_changed - 1] = id_changed_time_us;
}

void
pbroker_init_cert_validity_counters(const char *cert_file_name)
{
    struct zcrypt_cert *cert;
    int64_t one_percent_time;
    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();

    cert = zcrypt_cert_read(cert_file_name);
    if (!cert) {
        ZPN_LOG(AL_ERROR, "Could not read cert file %s", cert_file_name);
        return;
    }
    gs->cert_validity_start_time_cloud_s = zcrypt_cert_valid_from_epoch(cert);
    gs->cert_validity_end_time_cloud_s = zcrypt_cert_valid_to_epoch(cert);
    zcrypt_cert_free(cert);

    one_percent_time = (gs->cert_validity_end_time_cloud_s - gs->cert_validity_start_time_cloud_s) / 100;
    gs->cert_force_re_enroll_time_cloud_s = gs->cert_validity_end_time_cloud_s -
        (one_percent_time * (100 - ZPN_PBROKER_FORCE_RE_ENROLL_TIME_FACTOR));
}

void set_is_pbroker_dev_environment(void)
{
    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();
    is_pbroker_dev_environment = zpath_is_cloud_dev_env(gs->cfg_key_cloud);
    ZPN_LOG(AL_INFO, "%s debug environment is %d", SERVICE_EDGE_LOG_NAME, is_pbroker_dev_environment);
}

int private_broker_is_dev_environment(void)
{
    return is_pbroker_dev_environment;
}

/* Get global ssl ctx, should not need lock as it is never changed after initially being allocated */
SSL_CTX * zpn_private_broker_get_self_to_broker_dtls_ctx(void)
{
    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();

    if ( !gs || !gs->self_to_broker_dtls_ctx) {
        ZPN_LOG(AL_CRITICAL, "No global state for get dtls ctx");
        return NULL;
    }

    return gs->self_to_broker_dtls_ctx;
}

int zdtls_enabled_on_pbroker(int64_t pbroker_gid, int64_t pbroker_grp_gid)
{
    int64_t config_value = zpath_config_override_get_config_int(DTLS_FEATURE,
                                                                &config_value,
                                                                DEFAULT_PBROKER_DTLS,
                                                                pbroker_gid,
                                                                pbroker_grp_gid,
                                                                ZPATH_GID_GET_CUSTOMER_GID(pbroker_gid),
                                                                (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                                (int64_t)0);

    ZPN_DEBUG_COR("DTLS config value for pbroker_gid %ld pbroker_grp_gid %ld is %ld", (long)pbroker_gid, (long)pbroker_grp_gid, (long)config_value);

    return config_value?1:0;
}

char *get_pbroker_stats_conn_sni()
{
    return pb_sni_customer_domain_stats;
}

/* Verify version logger */
static void log_verify_version_f(int priority, char *log_buf)
{
    char buf[1000];

    snprintf(buf, sizeof(buf), "FILE_FETCH:%s", log_buf);
    ZPATH_LOG(priority, "%s", buf);
}

int is_pbroker_dr_flag_exist() {
    int res = 0;
    if (access(ZPN_DR_MODE_FLAG, F_OK ) == 0 ) {
        // DR Mode flag exist
        res = 1;
    }
    return res;
}

int is_pbroker_running_drmode() {
    int ret = 0;
    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();

    if (gs && gs->private_broker_state && gs->private_broker_state->drmode_enabled) {
        ret = 1;
    }

    return ret;
}

int is_pbroker_state_enabled() {
    int ret = 0;
    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();
    struct zpn_private_broker *pbroker = NULL;

    /* Check if pbroker is enabled */
    ret = zpn_private_broker_get_by_id(gs->private_broker_id, &pbroker, NULL, NULL, 0);
    if (pbroker) {
        if (pbroker->enabled) {
            ret = 1;
        }
    } else {
        ZPN_LOG(AL_CRITICAL, "Unable to fetch config for pbroker gid:%"PRId64" result:%s",
                                                 gs->private_broker_id, zpn_result_string(ret));
    }
    return ret;
}

int is_pbroker_group_marked_to_support_dr() {
    int ret = 0;
    int is_dr_configured = 0;

    struct zpn_private_broker_group *group;

    if (ZPN_BROKER_IS_PRIVATE() && is_pbroker_state_enabled()) {
        ret = zpn_pbroker_group_get_by_gid(ZPN_BROKER_GET_GROUP_GID(), &group, 0, NULL, NULL, 0);
        if (ret) {
            ZPN_LOG(AL_ERROR,"Could not get private_broker_group detail");
        } else {
            is_dr_configured = group->use_in_dr_mode;
        }
    }
    return is_dr_configured;
}

int get_rollback_cnt(){
    int val;
    int ret = 0;
    FILE * fp = fopen(ROLLBACK_FILE, "r");

    if(!fp){
        if(errno != ENOENT){
            ZPN_LOG(AL_ERROR,"Could ot open rollback file: error:%s",strerror(errno));
        }
        return 0;
    }

    if(fscanf(fp, "%d", &val))
        ret = val;
    fclose(fp);
    fp = NULL;
    return ret;
}

int write_to_file_and_update(){
    int val;
    FILE * fp = fopen(ROLLBACK_FILE, "r");

    if (!fp) {
        //rollback file does not exist
        fp = fopen(ROLLBACK_FILE, "w");
        if (!fp){
            ZPATH_LOG(AL_ERROR, "Could create file:%s. err:%s", ROLLBACK_FILE, strerror(errno));
            return ZPATH_RESULT_ERR;
        }
        fprintf(fp, "%d", 1);
        fclose(fp);
        ZPATH_LOG(AL_NOTICE, "Write to file successful. File:%s", ROLLBACK_FILE);
        return ZPATH_RESULT_NO_ERROR;
    }

    //If file is present, increment the counter
    if(fscanf(fp, "%d", &val))
        val++;
    fclose(fp);
    fp = NULL;

    //reopen for write
    fp = fopen(ROLLBACK_FILE, "w");
    if(!fp){
        ZPATH_LOG(AL_ERROR, "Could not open file for write. File:%s. err:%s", ROLLBACK_FILE, strerror(errno));
        return ZPATH_RESULT_ERR;
    }
    fprintf(fp, "%d", val);
    fclose(fp);
    ZPATH_LOG(AL_NOTICE, "Write to file successful. File:%s. val:%d", ROLLBACK_FILE, val);
    return ZPATH_RESULT_NO_ERROR;
}

int geoip_replace_and_trigger_reload(char *filename, struct zpn_file_fetch_key *key, struct zcrypt_key local_key){
    int res = 0,ret = 0;
    char *geoip_data = NULL, *geoip_fallback_data = NULL;
    size_t geoip_size = 0,geoip_fallback_size = 0;
    char metafile_name[MAX_FF_STR_LEN+5] = {0}; //5 for _meta
    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();
    int develop_mode = 1,geo_sanity = 1;
    char file_version[FILE_PATH_LEN] = {0};
    char old_version[MAX_VERSION_LEN] = {0};
    int key2_used = 0;

    if(read_version(filename, old_version)){
        goto err;
    }

    if(key->version){
        write_version(filename, key->version);
    }else if(!zpath_is_geoip_running()){
        write_version(filename, "default");
    }

   if(gs->develop_certs_mode == zcrypt_metadata_develop_mode_disabled){
       develop_mode = 0;
    }

    snprintf(metafile_name, MAX_FF_STR_LEN+5, "%s_%s", filename,"meta");
    if(key->enc_or_dec_pvt_key && (key->enc_or_dec_pvt_key[0]!='\0')){
        geoip_data = decrypt_file(key->enc_or_dec_pvt_key, filename, &geoip_size, 1);
    }
KEY2_DECRYPT:
    if(!geoip_data && key->enc_or_dec_pvt_key2 && (key->enc_or_dec_pvt_key2[0]!='\0')){
        key2_used = 1;
        ZPN_LOG(AL_NOTICE,"Trying decryption with backup key for: %s",filename);
        geoip_data = decrypt_file(key->enc_or_dec_pvt_key2, filename, &geoip_size, 1);
    }

    if(!geoip_data){
        ZPN_LOG(AL_ERROR,"Unable to decrypt file:%s. Cannot load the new geoip database",filename);
        key_retry_flag[MMDB_GEOIP] = 1; //retry getting the key
        if(key->version && !strcmp(key->version,"default")){
            ZPATH_LOG(AL_ERROR, " FILE_FETCH: unable to decrypt default file.. Rolling back service-edge-child to previous version!");
            if(!sub_module_upgrade_failed){
                write_to_file_and_update();
            }
            sub_module_upgrade_failed = 1;
            key_retry_flag[MMDB_GEOIP] = 0;
        }
        ret = ZPATH_RESULT_NO_ERROR;
        goto err;
    }

    key_retry_flag[MMDB_GEOIP] = 0;//We have the key now
    ZPN_LOG(AL_INFO,"Successfuly decrypted file:%s.",filename);

    if(zcrypt_metadata_verify_version_in_memory(geoip_data, geoip_size, metafile_name, key->version, NULL, 0, log_verify_version_f, develop_mode, 0) != ZCRYPT_RESULT_NO_ERROR){
        ZPATH_LOG(AL_ERROR, " FILE_FETCH: could not verify metadata info for the file");
        if(!key2_used && key->enc_or_dec_pvt_key2 && (key->enc_or_dec_pvt_key2[0]!='\0')){
            ZPN_LOG(AL_INFO,"Retrying decryption");
            ZPN_FF_FREE(geoip_data);
            geoip_data = NULL;
            goto KEY2_DECRYPT;
        }
        if(key->version && !strcmp(key->version,"default")){
            ZPATH_LOG(AL_ERROR, " FILE_FETCH: meta file validation failed for default file.. Rolling back service-edge-child to previous version!");
            if(!sub_module_upgrade_failed){
                write_to_file_and_update();
            }
            sub_module_upgrade_failed = 1;
        }else{
            //some issue with file, we should retry downloading the file in some time
            key_retry_flag[MMDB_GEOIP] = 0; //no need to set this, key will automatically be retried with file
            write_version(filename, old_version);
            file_clean_downloaded[MMDB_GEOIP] = 0;
        }
        goto err;
    }

    ZPATH_LOG(AL_NOTICE," FILE_FETCH: successfully verified metadata for file:%s",filename);
    new_version_downloaded[MMDB_GEOIP] = 1;

    if(file_exists(FILENAME_GEOIP) && file_exists(FILENAME_GEOIP_META) && zpath_is_geoip_running()  && !zpath_is_geoip_fallback_running()){
        //delete the old fallback file
        unlink(FILENAME_GEOIP_FALLBACK);

        //current running geoip file becomes the fallback file
        res = rename(FILENAME_GEOIP, FILENAME_GEOIP_FALLBACK);
        if(res){
            ZPATH_LOG(AL_ERROR, "FILE_FETCH: Could not rename %s to %s", FILENAME_GEOIP, FILENAME_GEOIP_FALLBACK);
            ret = ZPATH_RESULT_ERR;
            goto err;
        }
        res = rename(FILENAME_GEOIP_META, FILENAME_GEOIP_FALLBACK_META);
        if(res){
            ZPATH_LOG(AL_ERROR, "FILE_FETCH: Could not rename %s to %s", FILENAME_GEOIP_META, FILENAME_GEOIP_FALLBACK_META);
            ret = ZPATH_RESULT_ERR;
            goto err;
        }
    }

    //Get the key and decrypt fallback file if it exists - to be used in the reload/init function
    if(file_exists(FILENAME_GEOIP_FALLBACK)){
        geoip_fallback_data = decrypt_file((char*)local_key.data, FILENAME_GEOIP_FALLBACK, &geoip_fallback_size, 0);
        if(!geoip_fallback_data){
            ZPN_LOG(AL_ERROR,"Unable to decrypt file:%s",FILENAME_GEOIP_FALLBACK);
        }
    }

    //create the new db file by encrypting it with local key
    if(encrypt_to_file((char*)local_key.data, geoip_data, geoip_size, FILENAME_GEOIP, 0)){
        ZPN_LOG(AL_ERROR,"Unable to encrypt file and create:%s",FILENAME_GEOIP);
        ret = ZPN_RESULT_ERR;
    }
    res = rename(metafile_name, FILENAME_GEOIP_META);
    if(res){
        ZPATH_LOG(AL_ERROR, "FILE_FETCH: Could not rename %s to %s", metafile_name, FILENAME_GEOIP_META);
        unlink(metafile_name);
    }

    unlink(filename);

    if(!zpath_is_geoip_running()){
        snprintf(zpath_geoip_filename, sizeof(zpath_geoip_filename), "%s", FILENAME_GEOIP);
        geoip_db_file = zpath_geoip_filename;
        res = zpath_geoip_init_from_memory(geoip_db_file, geoip_data, geoip_size, geoip_fallback_data, geoip_fallback_size);
        if (res) {
            ZPN_LOG(AL_ERROR, "zpath_geoip_init failed: %s",
                    zpath_result_string(res));
            ret = ZPATH_RESULT_ERR;
            if(key->version && !strcmp(key->version,"default")){
                ZPATH_LOG(AL_ERROR, " FILE_FETCH: error in initialising default geoip file.. Rolling back service-edge-child to previous version!");
                if(!sub_module_upgrade_failed){
                    write_to_file_and_update();
                }
                sub_module_upgrade_failed = 1;
            }
            goto err;
        }
    }else{
        res = zpath_trigger_geoip_reload(NULL,geoip_data, geoip_size, geoip_fallback_data, geoip_fallback_size);
        if(res){
            ZPN_LOG(AL_ERROR, "zpath_trigger_geoip_reload failed: %s",
                    zpath_result_string(res));
            ret = ZPATH_RESULT_ERR;
            goto err;
        }
        if(!zpath_is_geoip_running()){
            ZPN_LOG(AL_ERROR, "No geoip file loaded, retrying download of default file");
            geoip_db_file = NULL;
            goto err;
        }
    }

    //sanity check
    res = zpath_geoip_sanity_verify();
    if(res){
        ZPN_LOG(AL_ERROR,"GeoIP-City db failed sanity check\n");
        geoip_db_file = NULL;
        geo_sanity = 0;
        if(key->version && !strcmp(key->version,"default")){
            ZPATH_LOG(AL_ERROR, " FILE_FETCH: error in initialising default geoip file.. Rolling back service-edge-child to previous version!");
            if(!sub_module_upgrade_failed){
                write_to_file_and_update();
            }
            sub_module_upgrade_failed = 1;
        }
    }

    //check if new file failed sanity check, if so, move to the fallback file
    if(!geoip_db_file && geoip_fallback_data && !zpath_is_geoip_fallback_running())
    {
        snprintf(zpath_geoip_filename, sizeof(zpath_geoip_filename), "%s", FILENAME_GEOIP_FALLBACK);
        geoip_db_file = zpath_geoip_filename;
        ZPN_LOG(LOG_INFO,"Loading GeoIP-City database from fallback file...\n");
        res = zpath_trigger_geoip_reload(NULL, geoip_fallback_data, geoip_fallback_size, NULL, 0);
        if(!res){
            res = zpath_geoip_sanity_verify();
            if(!res){
                geo_sanity = 1;
                ZPN_LOG(LOG_INFO,"Successfully loaded GeoIP-City database from fallback file..\n");
            }else{
                geo_sanity = 0;
                ZPN_LOG(AL_ERROR,"GeoIP-City db failed sanity check\n");
                geoip_db_file = NULL;
                goto err;
            }
        }else{
            ZPN_LOG(AL_ERROR, "GeoIP-City databse init failed from fallback file: %s",
                zpath_result_string(res));
            geo_sanity = 0;
            geoip_db_file = NULL;
            goto err;
        }
    }else if(!geoip_db_file){
        res = ZPATH_RESULT_ERR;
        goto err;
    }

    zcrypt_get_metadata_version(FILENAME_GEOIP_META, file_version, FILE_PATH_LEN, log_verify_version_f, 0);
    write_version(filename, file_version);
    ZPN_LOG(LOG_INFO,"Successfully loaded GeoIP-City database..\n");

    if(geo_sanity && zpath_is_geoip_fallback_running()){
        zcrypt_get_metadata_version(FILENAME_GEOIP_FALLBACK_META, file_version, FILE_PATH_LEN, log_verify_version_f, 0);
        write_version(filename, file_version);
        if(geoip_data){
            ZPN_FF_FREE(geoip_data);
            geoip_data = NULL;
        }
        ZPN_LOG(LOG_INFO,"Running GeoIP-City version:%s", file_version);
    }else if(geoip_fallback_data){
        ZPN_FF_FREE(geoip_fallback_data);
        geoip_fallback_data = NULL;
    }

    if(geo_sanity && !zpath_is_geoip_fallback_running())
        ZPN_LOG(LOG_INFO,"Running GeoIP-City version:%s", file_version);
    //update the auth log
    zpn_send_zpn_tcp_info_report(gs->private_broker_state->broker_control,
                    fohh_connection_incarnation(gs->private_broker_state->broker_control),
                        gs->private_broker_state->broker_control,
                        zvm_vm_type_to_str_concise(zvm_type_get()), g_pbroker_runtime_os);

    return ret;
err:
    if(!key_retry_flag[MMDB_GEOIP] && file_exists(FILENAME_GEOIP_ENC))
        unlink(FILENAME_GEOIP_ENC);
    if(!key_retry_flag[MMDB_GEOIP] && file_exists(metafile_name))
        unlink(metafile_name);
    if(geoip_data){
        ZPN_FF_FREE(geoip_data);
        geoip_data = NULL;
    }
    if(geoip_fallback_data){
        ZPN_FF_FREE(geoip_fallback_data);
        geoip_fallback_data = NULL;
    }
    return ret;
}

int ispip_replace_and_trigger_reload(char *filename, struct zpn_file_fetch_key *key, struct zcrypt_key local_key){
    int res = 0,ret = 0;
    char *ispip_data = NULL, *ispip_fallback_data = NULL;
    size_t ispip_size = 0,ispip_fallback_size = 0;
    char metafile_name[MAX_FF_STR_LEN+5] = {0}; //5 for _meta
    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();
    int develop_mode = 1;
    char file_version[FILE_PATH_LEN] = {0};
    int isp_sanity = 1;
    char old_version[MAX_VERSION_LEN] = {0};
    int key2_used = 0;

    if(read_version(filename, old_version)){
        goto err;
    }

    if(key->version){
        write_version(filename, key->version);
    }else if(!zpath_is_ispip_running()){
        write_version(filename, "default");
    }

    if(gs->develop_certs_mode == zcrypt_metadata_develop_mode_disabled){
       develop_mode = 0;
    }

    snprintf(metafile_name, MAX_FF_STR_LEN+5, "%s_%s", filename,"meta");

    if(key->enc_or_dec_pvt_key && (key->enc_or_dec_pvt_key[0] != '\0')){
        ispip_data = decrypt_file(key->enc_or_dec_pvt_key, filename, &ispip_size, 1);
    }

KEY2_ISP_DECRYPT:
    if(!ispip_data && key->enc_or_dec_pvt_key2 && (key->enc_or_dec_pvt_key2[0] != '\0')){
        key2_used = 1;
        ZPN_LOG(AL_NOTICE,"Trying decryption with backup key for: %s",filename);
        ispip_data = decrypt_file(key->enc_or_dec_pvt_key2, filename, &ispip_size, 1);
    }

    if(!ispip_data){
        ZPN_LOG(AL_ERROR,"Unable to decrypt file:%s. Cannot load the new ispip database",filename);
        key_retry_flag[MMDB_ISP] = 1; //retry getting the key
        ret = ZPATH_RESULT_NO_ERROR;
        if(key->version && !strcmp(key->version,"default")){
            ZPATH_LOG(AL_ERROR, " FILE_FETCH: unable to decrypt default file.. Rolling back service-edge-child to previous version!");
            if(!sub_module_upgrade_failed){
                write_to_file_and_update();
            }
            sub_module_upgrade_failed = 1;
            key_retry_flag[MMDB_ISP] = 0;
        }
        goto err;
    }


    key_retry_flag[MMDB_ISP] = 0;//We have the key now
    ZPN_LOG(AL_INFO,"Successfuly decrypted file:%s.",filename);

    if(zcrypt_metadata_verify_version_in_memory(ispip_data, ispip_size, metafile_name, key->version, NULL, 0, log_verify_version_f, develop_mode, 0) != ZCRYPT_RESULT_NO_ERROR){
        ZPATH_LOG(AL_ERROR, " FILE_FETCH: could not verify metadata info for the file");
        if(!key2_used && key->enc_or_dec_pvt_key2 && (key->enc_or_dec_pvt_key2[0] != '\0')){
            ZPN_LOG(AL_INFO,"Retrying decryption");
            ZPN_FF_FREE(ispip_data);
            ispip_data = NULL;
            goto KEY2_ISP_DECRYPT;
        }
        if(key->version && !strcmp(key->version,"default")){
            ZPATH_LOG(AL_ERROR, " FILE_FETCH: meta file validation failed for default isp file.. Rolling back service-edge-child to previous version!");
            if(!sub_module_upgrade_failed){
                write_to_file_and_update();
            }
            sub_module_upgrade_failed = 1;
        }else{
            //some issue with file, we should retry downloading the file in some time
            key_retry_flag[MMDB_ISP] = 0;
            write_version(filename, old_version);
            file_clean_downloaded[MMDB_ISP] = 0;
        }
        goto err;
    }

    ZPATH_LOG(AL_NOTICE," FILE_FETCH: successfully verified metadata for file:%s",filename);
    new_version_downloaded[MMDB_ISP] = 1;

    if(file_exists(FILENAME_ISP) && zpath_is_ispip_running() && !zpath_is_ispip_fallback_running()){
        //delete the old fallback file
        unlink(FILENAME_ISP_FALLBACK);

        //current running ispip file becomes the fallback file
        res = rename(FILENAME_ISP, FILENAME_ISP_FALLBACK);
        if(res){
            ZPATH_LOG(AL_ERROR, "FILE_FETCH: Could not rename %s to %s", FILENAME_ISP, FILENAME_ISP_FALLBACK);
            ret = ZPATH_RESULT_ERR;
            goto err;
        }
        res = rename(FILENAME_ISP_META, FILENAME_ISP_FALLBACK_META);
        if(res){
            ZPATH_LOG(AL_ERROR, "FILE_FETCH: Could not rename %s to %s", FILENAME_ISP_META, FILENAME_ISP_FALLBACK_META);
            ret = ZPATH_RESULT_ERR;
            goto err;
        }
    }

    //Get the key and decrypt fallback file if it exists - to be used in the reload/init function
    if(file_exists(FILENAME_ISP_FALLBACK)){
        ispip_fallback_data = decrypt_file((char*)local_key.data, FILENAME_ISP_FALLBACK, &ispip_fallback_size, 0);
        if(!ispip_fallback_data){
            ZPN_LOG(AL_ERROR,"Unable to decrypt file:%s",FILENAME_ISP_FALLBACK);
        }
    }

    //create the new db file by encrypting it with local key
    if(encrypt_to_file((char*)local_key.data, ispip_data, ispip_size, FILENAME_ISP, 0)){
        ZPN_LOG(AL_ERROR,"Unable to encrypt file and create:%s",FILENAME_ISP);
        ret = ZPN_RESULT_ERR;
    }
    res = rename(metafile_name, FILENAME_ISP_META);
    if(res){
        ZPATH_LOG(AL_ERROR, "FILE_FETCH: Could not rename %s to %s", metafile_name, FILENAME_ISP_META);
        unlink(metafile_name);
    }

    unlink(filename);

    if(!zpath_is_ispip_running()){
        snprintf(zpath_ispip_filename, sizeof(zpath_ispip_filename), "%s", FILENAME_ISP);
        geoip_isp_db_file = zpath_ispip_filename;
        res = zpath_ispip_init_from_memory(geoip_isp_db_file, ispip_data, ispip_size, ispip_fallback_data, ispip_fallback_size);
        if (res) {
            ZPN_LOG(AL_ERROR, "zpath_ispip_init failed: %s",
                    zpath_result_string(res));
            if(key->version && !strcmp(key->version,"default")){
                ZPATH_LOG(AL_ERROR, " FILE_FETCH: error in initialising default isp file.. Rolling back service-edge-child to previous version!");
                if(!sub_module_upgrade_failed){
                    write_to_file_and_update();
                }
                sub_module_upgrade_failed = 1;
            }
            ret = ZPATH_RESULT_ERR;
            goto err;
        }
    }else{
        res = zpath_trigger_ispip_reload(NULL,ispip_data, ispip_size, ispip_fallback_data, ispip_fallback_size);
        if(res){
            ZPN_LOG(AL_ERROR, "zpath_trigger_ispip_reload failed: %s",
                    zpath_result_string(res));
            ret = ZPATH_RESULT_ERR;
            unlink(FILENAME_ISP_META);
            goto err;
        }
        if(!zpath_is_ispip_running()){
            ZPN_LOG(AL_ERROR, "No geo ISP file loaded, retrying download of default file");
            geoip_isp_db_file = NULL;
            goto err;
        }
    }

    //sanity check
    res = zpath_ispip_sanity_verify();
    if(res){
        ZPN_LOG(AL_ERROR,"ISP db failed sanity check\n");
        geoip_isp_db_file = NULL;
        isp_sanity = 0;
        if(key->version && !strcmp(key->version,"default")){
            ZPATH_LOG(AL_ERROR, " FILE_FETCH: error in initialising default isp file.. Rolling back service-edge-child to previous version!");
            if(!sub_module_upgrade_failed){
                write_to_file_and_update();
            }
            sub_module_upgrade_failed = 1;
        }
    }

    //check if new file failed sanity check, if so, move to the fallback file
    if(!geoip_isp_db_file && ispip_fallback_data && !zpath_is_ispip_fallback_running())
    {
        snprintf(zpath_ispip_filename, sizeof(zpath_ispip_filename), "%s", FILENAME_ISP_FALLBACK);
        geoip_isp_db_file = zpath_ispip_filename;
        ZPN_LOG(LOG_INFO,"Loading GeoIP-ISP database from fallback file...\n");
        res = zpath_trigger_ispip_reload(NULL,ispip_fallback_data, ispip_fallback_size, NULL, 0);
        if(!res){
            res = zpath_ispip_sanity_verify();
            if(!res){
                isp_sanity = 1;
                ZPN_LOG(LOG_INFO,"Successfully loaded GeoIP-ISP database from fallback file..\n");
            }else{
                isp_sanity = 0;
                ZPN_LOG(AL_ERROR,"ISP db failed sanity check\n");
                geoip_isp_db_file = NULL;
                goto err;
            }
        }else{
            isp_sanity = 0;
            ZPN_LOG(AL_ERROR, "ISP databse init failed from fallback file: %s",
                zpath_result_string(res));
            geoip_isp_db_file = NULL;
            goto err;
        }
    }else if(!geoip_isp_db_file){
        res = ZPATH_RESULT_ERR;
        goto err;
    }

    ZPN_LOG(LOG_INFO,"Successfully loaded GeoIP-ISP database..\n");
    zcrypt_get_metadata_version(FILENAME_ISP_META, file_version, FILE_PATH_LEN, log_verify_version_f, 0);
    write_version(filename, file_version);

    if(isp_sanity && zpath_is_ispip_fallback_running()){
        zcrypt_get_metadata_version(FILENAME_ISP_FALLBACK_META, file_version, FILE_PATH_LEN, log_verify_version_f, 0);
        write_version(filename, file_version);
        ZPN_LOG(LOG_INFO,"Running GeoIP-ISP version:%s", file_version);
        if(ispip_data){
            ZPN_FF_FREE(ispip_data);
            ispip_data = NULL;
        }
    }else if(ispip_fallback_data){
        ZPN_FF_FREE(ispip_fallback_data);
        ispip_fallback_data = NULL;
    }

    if(isp_sanity && !zpath_is_ispip_fallback_running())
        ZPN_LOG(LOG_INFO,"Running GeoIP-ISP version:%s", file_version);
    //update the auth log
    zpn_send_zpn_tcp_info_report(gs->private_broker_state->broker_control,
                    fohh_connection_incarnation(gs->private_broker_state->broker_control),
                        gs->private_broker_state->broker_control,
                        zvm_vm_type_to_str_concise(zvm_type_get()), g_pbroker_runtime_os);

    return ret;
err:
    if(!key_retry_flag[MMDB_ISP] && file_exists(FILENAME_ISP_ENC))
        unlink(FILENAME_ISP_ENC);
    if(!key_retry_flag[MMDB_ISP] && file_exists(metafile_name))
        unlink(metafile_name);
    if(ispip_data){
        ZPN_FF_FREE(ispip_data);
        ispip_data = NULL;
    }
    if(ispip_fallback_data){
        ZPN_FF_FREE(ispip_fallback_data);
        ispip_fallback_data = NULL;
    }
    return ret;
}

void
pbroker_set_swap_config(uint64_t bytes)
{
    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();
    gs->swap_config = (bytes != 0)? 1: 0;
}

int
pbroker_get_swap_config()
{
    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();
    if (!gs) {
        return 0;
    } else {
        return gs->swap_config;
    }
}

int pbroker_store_alt_cloud_to_file(char *listener_alt_cloud, char *rdir_alt_cloud)
{
    FILE *fp = NULL;
    char alt_domain_str[PBROKER_MAX_ALT_DOMAIN_STR] = {0};
    int len = 0;
    int res = 0;
    int ret = ZPATH_RESULT_NO_ERROR;
    int redir_write_successful = 0;
    int listener_write_successful = 0;

    if(!listener_alt_cloud && !rdir_alt_cloud) {
        ZPN_LOG(AL_INFO, "Cleaning up the alt cloud data from the %s file", PBROKER_ALT_DOMAIN_FILE);
    }

    fp = fopen(PBROKER_ALT_DOMAIN_FILE, "w");
    if (fp == NULL) {
        ZPATH_LOG(AL_ERROR, "Failed to open file %s", strerror(errno));
        ret = ZPATH_RESULT_ERR;
        goto out;
    }

    if(listener_alt_cloud && strnlen(listener_alt_cloud, PRIVATE_BROKER_CLOUD_NAME_LEN)) {
        len = snprintf(alt_domain_str, PBROKER_MAX_ALT_DOMAIN_STR, "%s:%s\n", PBROKER_LISTENER_ALT_CLOUD_STR, listener_alt_cloud);

        res = fwrite(alt_domain_str, 1 , len, fp);
        if(res != len){
            ZPATH_LOG(AL_NOTICE,"Error while writing listener alt cloud name in file:%s. err:%s",
                                PBROKER_ALT_DOMAIN_FILE,
                                strerror(errno));
            fclose(fp);
            ret = ZPATH_RESULT_ERR;
            goto out;
        } else {
            listener_write_successful = 1;
        }
    }

    if(rdir_alt_cloud && strnlen(rdir_alt_cloud, PRIVATE_BROKER_CLOUD_NAME_LEN)) {
        memset(alt_domain_str, 0, PBROKER_MAX_ALT_DOMAIN_STR);
        len = snprintf(alt_domain_str, PBROKER_MAX_ALT_DOMAIN_STR, "%s:%s\n", PBROKER_RDIR_ALT_CLOUD_STR, rdir_alt_cloud);

        res = fwrite(alt_domain_str, 1 , len, fp);
        if(res != len){
            ZPATH_LOG(AL_NOTICE,"Error while writing redirect alt cloud name in file:%s. err:%s",
                                PBROKER_ALT_DOMAIN_FILE,
                                strerror(errno));
            fclose(fp);
            ret = ZPATH_RESULT_ERR;
            goto out;
        } else {
            redir_write_successful = 1;
        }
    }

    if(redir_write_successful || listener_write_successful) {
        char msg[1024];
        int index = 0;
        memset(msg, 0, sizeof(msg));

        int count = snprintf(msg, sizeof(msg), "Successfully stored alt cloud info in cache. ");
        index += count;

        if(redir_write_successful) {
            count = snprintf(msg + index, sizeof(msg) - index, "redir:%s ", (rdir_alt_cloud && rdir_alt_cloud[0]) ? rdir_alt_cloud : "NULL");
            index += count;
        }

        if(listener_write_successful) {
            count = snprintf(msg + index, sizeof(msg) - index, "listener:%s ", (listener_alt_cloud && listener_alt_cloud[0]) ? listener_alt_cloud : "NULL");
        }

        ZPATH_LOG(AL_INFO, "%s", msg);
    }

    fclose(fp);

out:
    return ret;
}

int pbroker_read_alt_cloud_from_file(char *listener_alt_cloud, char *rdir_alt_cloud)
{
    FILE *fp = NULL;
    char *line = NULL;
    char alt_domain_str[PBROKER_MAX_ALT_DOMAIN_STR] = {0};
    int ret = ZPATH_RESULT_NO_ERROR;


    if(!listener_alt_cloud) {
        ZPN_LOG(AL_ERROR, "NULL listener alt_cloud argument passed to read cloud info from the file file");
        ret = ZPATH_RESULT_BAD_ARGUMENT;
        goto out;
    }

    if(!rdir_alt_cloud) {
        ZPN_LOG(AL_ERROR, "NULL redirect alt_cloud argument passed to read cloud info from the file file");
        ret = ZPATH_RESULT_BAD_ARGUMENT;
        goto out;
    }

    if(file_exists(PBROKER_ALT_DOMAIN_FILE) == 0) {
        ZPATH_LOG(AL_ERROR, "Error while getting file info, file: %s, error: %s", PBROKER_ALT_DOMAIN_FILE, strerror(errno));
        ret = ZPATH_RESULT_ERR;
        goto out;
    }

    fp = fopen(PBROKER_ALT_DOMAIN_FILE, "r");
    if (fp == NULL) {
        ZPATH_LOG(AL_ERROR, "Failed to open file %s", strerror(errno));
        ret = ZPATH_RESULT_ERR;
        goto out;
    }

    while ((line = fgets(alt_domain_str, sizeof(alt_domain_str), fp))) {
        if(strstr(line, PBROKER_LISTENER_ALT_CLOUD_STR)) {
            sscanf(line, PBROKER_LISTENER_ALT_CLOUD_STR":%100s",listener_alt_cloud);
        } else if(strstr(line, PBROKER_RDIR_ALT_CLOUD_STR)) {
            sscanf(line, PBROKER_RDIR_ALT_CLOUD_STR":%100s",rdir_alt_cloud);
        }
    }

    ZPATH_LOG(AL_INFO, "Successfully read alt cloud. redir:%s listener:%s",
                    (listener_alt_cloud && listener_alt_cloud[0]) ? listener_alt_cloud : "NULL",
                    (rdir_alt_cloud && rdir_alt_cloud[0]) ? rdir_alt_cloud : "NULL");
    fclose(fp);

out:
    return ret;
}

/* Retrieve the cloud-name to use to connect to broker/rbroker */
char* pbroker_get_redir_cloud_name(void) {
    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();

    if(gs->redir_alt_cloud_name[0] != '\0') {
        return gs->redir_alt_cloud_name;
    } else {
        return gs->cfg_key_cloud;
    }
}

/* Retrieve the cloud-name to use to start the listeners */
char* pbroker_get_listener_cloud(void) {
    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();

    if(gs->listener_alt_cloud_name[0] != '\0') {
        return gs->listener_alt_cloud_name;
    } else {
        return gs->cfg_key_cloud;
    }
}

void zpn_private_broker_alt_cloud_config_override_monitor_cb(const int64_t *config_value, int64_t impacted_gid)
{
    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();
    int64_t new_value = (uint64_t)*config_value;
    int fresult = 0;

    ZPN_LOG(AL_NOTICE, "alt_cloud config changed. New value:%"PRId64" impacted_gid:%"PRId64"", new_value, impacted_gid);

    // We dont check the value alt_cloud_feature_state_initialised in this function, because we definitely know it would be 1

    /* Change any non-zero value to 1. */
    if (gs->alt_cloud_feature_state) {
        gs->alt_cloud_feature_state = CONFIG_FEATURE_ALT_CLOUD_ENABLED;
    }

    /* Update the alt_cloud cache file */
    if (gs->alt_cloud_feature_state == CONFIG_FEATURE_ALT_CLOUD_DISABLED) {
        if(strnlen(gs->listener_alt_cloud_name, sizeof(gs->listener_alt_cloud_name)) || strnlen(gs->redir_alt_cloud_name, sizeof(gs->redir_alt_cloud_name))) {
            gs->alt_cloud_feature_state_changed = 1;
        }
        fresult = pbroker_store_alt_cloud_to_file(NULL, NULL);
        if (fresult) {
            ZPN_LOG(AL_ERROR, "Failed to write alt_cloud to local cache file. Error: %d", fresult);
        }
    } else {
        /* enabling the feature */
        struct zpn_private_broker_group *group = NULL;
        int res = zpn_pbroker_group_get_by_gid(g_broker_common_cfg->private_broker.pb_group_id, &group, 1, NULL, NULL, 0);
        if(res) {
            ZPN_LOG(AL_ERROR, "For pbroker %"PRId64" group info for %"PRId64", error : %s", g_broker_common_cfg->private_broker.pb_group_id, ZPN_BROKER_GET_GID(), zpn_result_string(res));
        } else {
            if(group->alt_cloud) {
                gs->alt_cloud_feature_state_changed = 1;
                snprintf_nowarn(gs->listener_alt_cloud_name, sizeof(gs->listener_alt_cloud_name), "%s", group->alt_cloud);
            }
            fresult = pbroker_store_alt_cloud_to_file(gs->listener_alt_cloud_name, gs->redir_alt_cloud_name);
            if (fresult) {
                ZPN_LOG(AL_ERROR, "Failed to write alt_cloud to local cache file. Error: %d", fresult);
            }
        }
    }
}

/*
 * PSE config override register
 */
int zpn_pbroker_pse_cfg_override_register(void) {
    int res = ZPN_RESULT_NO_ERROR;

    static struct zpath_config_override_desc pse_cfg_descriptions[] = {
        {
            .key                = ARGO_MEM_THRESHOLD_PERCENTAGE_PSE,
            .desc               = "Argo logging memory use percent threshold for PSE",
            .details            = "Argo logging memory use percent threshold for PSE, value is percent and must be between 0, 100\n"
            "Order of check: component id, component group id, customer gid, global\n"
            "default: 95 (Default is 95 % )",
            .val_type           = config_type_int,
            .component_types    = config_component_pbroker,
            .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_inst_grp | config_target_gid_type_cust |  config_target_gid_type_global,
            .int_range_lo       = ARGO_MEM_THRESHOLD_PERCENTAGE_PSE_MIN,
            .int_range_hi       = ARGO_MEM_THRESHOLD_PERCENTAGE_PSE_MAX,
            .int_default        = ARGO_MEM_THRESHOLD_PERCENTAGE_PSE_DEFAULT,
            .feature_group      = FEATURE_GROUP_PRIVATE_SERVICE_EDGE_ARGO,
            .value_traits       = config_value_traits_normal
        },

        {
            .key                = CONFIG_FEATURE_PSE_MIN_MEMORY_KB,
            .desc               = "Minimum memory required in KB to allow a PSE to run",
            .details            = "Minimum memory required in KB to allow a PSE to run, "
            "value is in KB must be between 262144 (i.e. 256  MB) to 33554432 (i.e. 32 GB) expressed in units of KB\n"
            "Order of check: component id, component group id, customer gid, global\n"
            "default: 1572864 KB (i.e. Default is 1572864 KB or 1536*1024 KB )",
            .val_type           = config_type_int,
            .component_types    = config_component_pbroker,
            .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_inst_grp | config_target_gid_type_cust |  config_target_gid_type_global,
            .int_range_lo       = CONFIG_FEATURE_PSE_MIN_MEMORY_KB_MIN,
            .int_range_hi       = CONFIG_FEATURE_PSE_MIN_MEMORY_KB_MAX,
            .int_default        = CONFIG_FEATURE_PSE_MIN_MEMORY_KB_DEFAULT,
            .feature_group      = FEATURE_GROUP_PRIVATE_SERVICE_EDGE_MEMORY,
            .value_traits       = config_value_traits_normal
        },

        {
            .key                = CONFIG_FEATURE_PSE_LIBEVENT_PRIORITY,
            .desc               = "Controls wether pse will use libevent or zevent",
            .details            = "Decides wether the pse will use libevent or zevent, by default it will use zevent (0)\n"
                                  "values - 1:enabled 0:disabled but dont restart pse -1: disabled and restart pse",
            .val_type           = config_type_int,
            .component_types    = config_component_pbroker,
            .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_cust |  config_target_gid_type_global,
            .int_range_lo       = CONFIG_FEATURE_PSE_LIBEVENT_PRIORITY_MIN,
            .int_range_hi       = CONFIG_FEATURE_PSE_LIBEVENT_PRIORITY_MAX,
            .int_default        = CONFIG_FEATURE_PSE_LIBEVENT_PRIORITY_DEFAULT,
            .feature_group      = FEATURE_GROUP_PRIVATE_SERVICE_EDGE_LIBEVENT,
            .value_traits       = config_value_traits_normal
        },

        {
            .key                = CONFIG_FEATURE_PSE_CUSTOM_APP_THREAD,
            .desc               = "Controls wether pse will use custom number of app threads or not",
            .details            = "Decides wether the pse will use custom number of app threads or not. Default 10 app threads (config:0)\n"
                                  "values - 1:enabled 0:disabled but dont restart pse -1: disabled and restart pse",
            .val_type           = config_type_int,
            .component_types    = config_component_pbroker,
            .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_cust |  config_target_gid_type_global,
            .int_range_lo       = CONFIG_FEATURE_PSE_CUSTOM_APP_THREAD_MIN,
            .int_range_hi       = CONFIG_FEATURE_PSE_CUSTOM_APP_THREAD_MAX,
            .int_default        = CONFIG_FEATURE_PSE_CUSTOM_APP_THREAD_DEFAULT,
            .feature_group      = FEATURE_GROUP_PRIVATE_SERVICE_EDGE_APP_THREAD,
            .value_traits       = config_value_traits_normal
        },

        {
            .key                = ZPN_PB_CLIENT_NO_CREATE,
            .desc               = "Control PSE PB Client used for pbza two hop connections to enable/disable them, by default pbza connections are created to Public Broker",
            .details            = "Control PSE PB Client used for pbza two hop connections to Public Broker, 0 = disable no-create, 1 = enable no-create\n"
            "Order of check: component id, component group id, customer gid, global\n"
            "default: 0 (i.e. pbza no-create is disabled, pbza connection will be creeated, turning this to 1 is a normally a developer use/testing capablity)",
            .val_type           = config_type_int,
            .component_types    = config_component_pbroker,
            .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_inst_grp | config_target_gid_type_cust |  config_target_gid_type_global,
            .int_range_lo       = ZPN_PB_CLIENT_NO_CREATE_MIN,
            .int_range_hi       = ZPN_PB_CLIENT_NO_CREATE_MAX,
            .int_default        = ZPN_PB_CLIENT_NO_CREATE_DEFAULT,
            .feature_group      = FEATURE_GROUP_PRIVATE_SERVICE_EDGE_TWO_HOP,
            .value_traits       = config_value_traits_feature_enablement
        },

        {
            .key                = ZPN_GEOIP_MMDB_DOWNLOAD_DISABLE,
            .desc               = "Control PSE MMDB download disable",
            .details            = "Control PSE MMDB download disable, 0 = allow mmdb download, 1 = disable mmdb download\n"
            "Order of check: component id, component group id, customer gid, global\n"
            "default: 0 (i.e. PSE mmdb download is enabled)",
            .val_type           = config_type_int,
            .component_types    = config_component_pbroker,
            .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_inst_grp | config_target_gid_type_cust |  config_target_gid_type_global,
            .int_range_lo       = DEFAULT_ZPN_GEOIP_MMDB_DOWNLOAD_MIN,
            .int_range_hi       = DEFAULT_ZPN_GEOIP_MMDB_DOWNLOAD_MAX,
            .int_default        = DEFAULT_ZPN_GEOIP_MMDB_DOWNLOAD_DISABLE,
            .feature_group      = FEATURE_GROUP_COUNTRY_CODE_POLICY,
            .value_traits       = config_value_traits_feature_enablement
        },

        {
            .key                = PBROKER_CONFIG_OVERRIDE_STATS_COMPREHENSIVE,
            .desc               = "Control PSE comprehensive stats enable/disable, used by PSE dashboard and Magellan",
            .details            = "Control PSE comprehensive stats enable/disable, 0 = pse comprehensive stats disabled, 1 = pse comprehensive stats enabled\n"
            "Order of check: component id, component group id, customer gid, global\n"
            "default: 1 (i.e. PSE comprehensive stats enabled)",
            .val_type           = config_type_int,
            .component_types    = config_component_pbroker,
            .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_inst_grp | config_target_gid_type_cust |  config_target_gid_type_global,
            .int_range_lo       = PBROKER_CONFIG_OVERRIDE_STATS_COMPREHENSIVE_MIN,
            .int_range_hi       = PBROKER_CONFIG_OVERRIDE_STATS_COMPREHENSIVE_MAX,
            .int_default        = PBROKER_CONFIG_OVERRIDE_STATS_COMPREHENSIVE_DEFAULT,
            .feature_group      = FEATURE_GROUP_PRIVATE_SERVICE_EDGE_STATS,
            .value_traits       = config_value_traits_feature_enablement
        },

        /*  Admin Probe section */
        {
            .key                = PBROKER_ADMIN_PROBE_FEATURE_ALL,
            .desc               = "Control PSE admin probe enable/disable",
            .details            = "Control PSE admin probe enable/disable, 0 = disable, 1 = enable\n"
            "Order of check: global\n"
            "default: 1 (i.e. PSE admin probe enabled by default)",
            .val_type           = config_type_int,
            .component_types    = config_component_pbroker,
            .target_gid_types   = config_target_gid_type_global,
            .int_range_lo       = DEFAULT_PBROKER_ADMIN_PROBE_ALL_MIN,
            .int_range_hi       = DEFAULT_PBROKER_ADMIN_PROBE_ALL_MAX,
            .int_default        = DEFAULT_PBROKER_ADMIN_PROBE_ALL_ENABLED,
            .feature_group      = FEATURE_GROUP_PRIVATE_SERVICE_EDGE_COMMAND_PROBE,
            .value_traits       = config_value_traits_feature_enablement
        },
        {
            .key                = PBROKER_ADMIN_PROBE_FEATURE_RESTART_PROCESS,
            .desc               = "Control PSE admin probe restart process enable/disable",
            .details            = "Control PSE admin probe restart process enable/disable, 0 = disable, 1 = enable\n"
            "Order of check: global\n"
            "default: 0 (i.e. PSE admin probe restart process disabled by default)",
            .val_type           = config_type_int,
            .component_types    = config_component_pbroker,
            .target_gid_types   = config_target_gid_type_global,
            .int_range_lo       = DEFAULT_PBROKER_ADMIN_PROBE_RESTART_PROCESS_MIN,
            .int_range_hi       = DEFAULT_PBROKER_ADMIN_PROBE_RESTART_PROCESS_MAX,
            .int_default        = DEFAULT_PBROKER_ADMIN_PROBE_RESTART_PROCESS_ENABLED,
            .feature_group      = FEATURE_GROUP_PRIVATE_SERVICE_EDGE_COMMAND_PROBE,
            .value_traits       = config_value_traits_feature_enablement
        },
        {
            .key                = PBROKER_ADMIN_PROBE_FEATURE_RESTART_SYSTEM,
            .desc               = "Control PSE admin probe restart system enable/disable",
            .details            = "Control PSE admin probe restart system enable/disable, 0 = disable, 1 = enable\n"
            "Order of check: global\n"
            "default: 0 (i.e. PSE admin probe restart system disabled by default)",
            .val_type           = config_type_int,
            .component_types    = config_component_pbroker,
            .target_gid_types   = config_target_gid_type_global,
            .int_range_lo       = DEFAULT_PBROKER_ADMIN_PROBE_RESTART_SYSTEM_MIN,
            .int_range_hi       = DEFAULT_PBROKER_ADMIN_PROBE_RESTART_SYSTEM_MAX,
            .int_default        = DEFAULT_PBROKER_ADMIN_PROBE_RESTART_SYSTEM_ENABLED,
            .feature_group      = FEATURE_GROUP_PRIVATE_SERVICE_EDGE_COMMAND_PROBE,
            .value_traits       = config_value_traits_feature_enablement
        },

        {
            .key                = PBROKER_ADMIN_PROBE_FEATURE_DNS,
            .desc               = "Control PSE admin probe dns enable/disable",
            .details            = "Control PSE admin probe dns enable/disable, 0 = disable, 1 = enable\n"
            "Order of check: global\n"
            "default: 0 (i.e. PSE admin probe dns, disabled by default)",
            .val_type           = config_type_int,
            .component_types    = config_component_pbroker,
            .target_gid_types   = config_target_gid_type_global,
            .int_range_lo       = DEFAULT_PBROKER_ADMIN_PROBE_DNS_MIN,
            .int_range_hi       = DEFAULT_PBROKER_ADMIN_PROBE_DNS_MAX,
            .int_default        = DEFAULT_PBROKER_ADMIN_PROBE_DNS_ENABLED,
            .feature_group      = FEATURE_GROUP_PRIVATE_SERVICE_EDGE_COMMAND_PROBE,
            .value_traits       = config_value_traits_feature_enablement
        },
        {
            .key                = PBROKER_ADMIN_PROBE_FEATURE_ICMP,
            .desc               = "Control PSE admin probe icmp enable/disable",
            .details            = "Control PSE admin probe icmp enable/disable, 0 = disable, 1 = enable\n"
            "Order of check: global\n"
            "default: 0 (i.e. PSE admin probe icmp, disabled by default)",
            .val_type           = config_type_int,
            .component_types    = config_component_pbroker,
            .target_gid_types   = config_target_gid_type_global,
            .int_range_lo       = DEFAULT_PBROKER_ADMIN_PROBE_ICMP_MIN,
            .int_range_hi       = DEFAULT_PBROKER_ADMIN_PROBE_ICMP_MAX,
            .int_default        = DEFAULT_PBROKER_ADMIN_PROBE_ICMP_ENABLED,
            .feature_group      = FEATURE_GROUP_PRIVATE_SERVICE_EDGE_COMMAND_PROBE,
            .value_traits       = config_value_traits_feature_enablement
        },

        {
            .key                = PBROKER_ADMIN_PROBE_FEATURE_TCP,
            .desc               = "Control PSE admin probe tcp enable/disable",
            .details            = "Control PSE admin probe tcp enable/disable, 0 = disable, 1 = enable\n"
            "Order of check: global\n"
            "default: 0 (i.e. PSE admin probe tcp, disabled by default)",
            .val_type           = config_type_int,
            .component_types    = config_component_pbroker,
            .target_gid_types   = config_target_gid_type_global,
            .int_range_lo       = DEFAULT_PBROKER_ADMIN_PROBE_TCP_MIN,
            .int_range_hi       = DEFAULT_PBROKER_ADMIN_PROBE_TCP_MAX,
            .int_default        = DEFAULT_PBROKER_ADMIN_PROBE_TCP_ENABLED,
            .feature_group      = FEATURE_GROUP_PRIVATE_SERVICE_EDGE_COMMAND_PROBE,
            .value_traits       = config_value_traits_feature_enablement
        },

        {
            .key                = PBROKER_ADMIN_PROBE_FEATURE_TCPDUMP,
            .desc               = "Control PSE admin probe tcpdump enable/disable",
            .details            = "Control PSE admin probe tcpdump enable/disable, 0 = disable, 1 = enable\n"
            "Order of check: global\n"
            "default: 0 (i.e. PSE admin probe tcpdump, disabled by default)",
            .val_type           = config_type_int,
            .component_types    = config_component_pbroker,
            .target_gid_types   = config_target_gid_type_global,
            .int_range_lo       = DEFAULT_PBROKER_ADMIN_PROBE_TCPDUMP_MIN,
            .int_range_hi       = DEFAULT_PBROKER_ADMIN_PROBE_TCPDUMP_MAX,
            .int_default        = DEFAULT_PBROKER_ADMIN_PROBE_TCPDUMP_ENABLED,
            .feature_group      = FEATURE_GROUP_PRIVATE_SERVICE_EDGE_COMMAND_PROBE,
            .value_traits       = config_value_traits_feature_enablement
        },

        /* PSE Resiliency */
        {
            .key                = ZPN_PSE_RESILIENCY_ENABLED,
            .desc               = "Control pse resiliency mode enable/disable",
            .details            = "Control pse resiliency mode enable/disable, 0 = disable, 1 = enable\n"
            "Order of check: component id, component group id, customer gid, global\n"
            "default: 1 (i.e. pse resiliency, enabled by default)",
            .val_type           = config_type_int,
            .component_types    = config_component_pbroker,
            .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_inst_grp | config_target_gid_type_cust |  config_target_gid_type_global,
            .int_range_lo       = ZPN_PSE_RESILIENCY_ENABLED_MIN,
            .int_range_hi       = ZPN_PSE_RESILIENCY_ENABLED_MAX,
            .int_default        = ZPN_PSE_RESILIENCY_ENABLED_DEFAULT,
            .feature_group      = FEATURE_GROUP_PSE_RESILIENCY,
            .value_traits       = config_value_traits_feature_enablement
        },

        {
            .key                = ZPN_PSE_RESILIENCY_CLOUD_DOWN_INTERVAL_S,
            .desc               = "Control pse resiliency mode cloud down detect interval in seconds",
            .details            = "Control pse resiliency mode cloud down detect interval in seconds, between 30 seconds to 3600 seconds\n"
            "Order of check: component id, component group id, customer gid, global\n"
            "default: 60 (i.e. pse resiliency mode cloud down detect interval is 60 seconds by default)",
            .val_type           = config_type_int,
            .component_types    = config_component_pbroker,
            .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_inst_grp | config_target_gid_type_cust |  config_target_gid_type_global,
            .int_range_lo       = ZPN_PSE_RESILIENCY_CLOUD_DOWN_INTERVAL_S_MIN,
            .int_range_hi       = ZPN_PSE_RESILIENCY_CLOUD_DOWN_INTERVAL_S_MAX,
            .int_default        = ZPN_PSE_RESILIENCY_CLOUD_DOWN_INTERVAL_S_DEFAULT,
            .feature_group      = FEATURE_GROUP_PSE_RESILIENCY,
            .value_traits       = config_value_traits_normal
        },
        /* Client connection moinitor */
        {
            .key                = ZPN_PB_CLIENT_CONNECTION_MONITOR,
            .desc               = "Control pse client connection monitor, 0 = disable, 1 = enable, this is an optional debug feature",
            .details            = "Control pse client connection monitor, 0 = disable, 1 = enable\n"
            "Order of check: component id, component group id, customer gid, global\n"
            "default: 0 (i.e. pse client connection monitor is disabled by default)",
            .val_type           = config_type_int,
            .component_types    = config_component_pbroker,
            .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_inst_grp | config_target_gid_type_cust |  config_target_gid_type_global,
            .int_range_lo       = ZPN_PB_CLIENT_CONNECTION_MONITOR_MIN,
            .int_range_hi       = ZPN_PB_CLIENT_CONNECTION_MONITOR_MAX,
            .int_default        = DEFAULT_ZPN_PB_CLIENT_CONNECTION_MONITOR,
            .feature_group      = FEATURE_GROUP_PRIVATE_SERVICE_EDGE_CLIENT_MONITOR,
            .value_traits       = config_value_traits_feature_enablement
        },

        {
            .key                = ZPN_PB_CLIENT_CONNECTION_MONITOR_INTERVAL,
            .desc               = "Control pse client connection monitor interval, interval is configured in seconds, this is used only if monitor is enabled",
            .details            = "Control pse client connection monitor interval, interval is configured in seconds, value between 60 to 3600 seconds\n"
            "Order of check: component id, component group id, customer gid, global\n"
            "default: 240 (i.e. pse client connection monitor default interval is 240 seconds)",
            .val_type           = config_type_int,
            .component_types    = config_component_pbroker,
            .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_inst_grp | config_target_gid_type_cust |  config_target_gid_type_global,
            .int_range_lo       = ZPN_PB_CLIENT_CONNECTION_MONITOR_INTERVAL_MIN,
            .int_range_hi       = ZPN_PB_CLIENT_CONNECTION_MONITOR_INTERVAL_MAX,
            .int_default        = DEFAULT_ZPN_PB_CLIENT_CONNECTION_MONITOR_INTERVAL,
            .feature_group      = FEATURE_GROUP_PRIVATE_SERVICE_EDGE_CLIENT_MONITOR,
            .value_traits       = config_value_traits_normal
        },

        {
            .key                = ZPN_PSE_REGISTRATION_WALLY_ENABLED,
            .desc               = "If enabled, PSE will have static/registration wally connection, otherwise it get static tables from shard wally connection",
            .details            = "If enabled, PSE will have static/registration wally connection, otherwise it get static tables from shard wally connection. (by default it is disabled.)\n"
                                  "The target_gid_types from most specific to most general are: instance, customer, and global. Instance group is not available",
            .val_type           = config_type_int,
            .component_types    = config_component_pbroker,
            .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_cust |  config_target_gid_type_global,
            .int_range_lo       = ZPN_PSE_REGISTRATION_WALLY_ENABLED_MIN,
            .int_range_hi       = ZPN_PSE_REGISTRATION_WALLY_ENABLED_MAX,
            .int_default        = ZPN_PSE_REGISTRATION_WALLY_ENABLED_DEFAULT,
            .feature_group      = FEATURE_GROUP_PRIVATE_SERVICE_EDGE_STATIC_WALLY,
            .value_traits       = config_value_traits_feature_enablement
        },
        {
            .key                = PSE_CONFIG_OVERRIDE_ENABLE_WALLY_TABLE_STATS_UPLOAD,
            .desc               = "Control the upload of wally_table stats from Pse, Stats will be used by Magellan",
            .details            = "enable/disable the upload of wally_table stats from Pse\n"
                                  "Order of check: component id, component group id, customer gid, global\n"
                                  "default: 1 (i.e. enabled)",
            .val_type           = config_type_int,
            .component_types    = config_component_pbroker,
            .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_inst_grp | config_target_gid_type_cust |  config_target_gid_type_global,
            .int_range_lo       = PSE_CONFIG_OVERRIDE_ENABLE_WALLY_TABLE_STATS_UPLOAD_MIN,
            .int_range_hi       = PSE_CONFIG_OVERRIDE_ENABLE_WALLY_TABLE_STATS_UPLOAD_MAX,
            .int_default        = PSE_CONFIG_OVERRIDE_ENABLE_WALLY_TABLE_STATS_UPLOAD_DEFAULT,
            .feature_group      = FEATURE_GROUP_PRIVATE_SERVICE_EDGE_STATS,
            .value_traits       = config_value_traits_feature_enablement
        }

    };

    /* Register all descriptions */
    int len = sizeof(pse_cfg_descriptions) / sizeof(pse_cfg_descriptions[0]);
    for (int i = 0; i < len; i++) {
        res = zpath_config_override_desc_register(&pse_cfg_descriptions[i]);
        if (res != ZPN_RESULT_NO_ERROR) {
            ZPN_LOG(AL_ERROR, "Unable to register pse_cfg_descriptions[%d] for key: %s, err: %s", i, pse_cfg_descriptions[i].key, zpn_result_string(res));
            goto done;
        }
    }

    res = ZPN_DR_RESULT_NO_ERROR;

done:

    return res;
}

int zpn_private_broker_fohh_connection_sanity_callback_with_lock(struct fohh_connection* connection)
{
    /* Sanity check 1: switch connection between siteC or default broker if required */
    fohh_connection_site_state_check(connection,
            zpn_private_broker_get_site_is_active_with_lock(),
            zpn_private_broker_site_sitec_is_reachable(),
            zpn_private_broker_site_broker_is_reachable(),
            zpn_private_broker_site_is_sitec_preferred(),
            (zpn_private_broker_get_firedrill_state() ? PBROKER_FIREDRILL_SWITCH_VALUE : zpn_private_broker_get_max_allowed_downtime_s_with_lock()),
            NULL);

    /* Sanity check 2: further down we are only interested in unconnected connection */
    if (fohh_get_state(connection) == fohh_connection_connected) {
        return ZPATH_RESULT_NO_ERROR;
    }

    int ret = ZPATH_RESULT_NO_ERROR;

    /* Sonar complains: "Remove this conditional structure or edit its code blocks so that they're not all the same"
     * So here defines a variable to return NO_ERROR. Remove this comment if you want to add new code here.
     */

    return ret;
}

int zpn_private_broker_firedrill_timer_activate(int64_t firedrill_interval)
{
    struct timeval tv = {0};
    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();
    struct zpn_firedrill_config firedrill_cfg = {0};

    ZPN_LOG(AL_ERROR, "firedrill triggered for interval: %"PRId64"", firedrill_interval);
    /* activate the timer */
    tv.tv_sec = firedrill_interval;
    tv.tv_usec = 0;
    if (event_add(gs->pbroker_firedrill_timer, &tv)) {
        ZPN_LOG(AL_ERROR, "Could not activate status timer for firedrill");
        return ZPN_RESULT_ERR;
    }

    /* update the firedrill state to write to site_config.cfg */
    firedrill_cfg.status = ZPN_PRIVATE_BROKER_FIREDRILL_ENABLED;
    firedrill_cfg.interval_s = firedrill_interval;
    firedrill_cfg.start_time = epoch_s();

    zpn_pbroker_update_firedrill_config_to_site_config_file(&firedrill_cfg);

    /* update firedrill stats */
    __sync_add_and_fetch(&g_pb_firedrill_stats_obj.firedrill_triggered_count, 1);

    ZPN_LOG(AL_ERROR, "firedrill triggered stats g_pb_firedrill_stats_obj.firedrill_triggered_count: %d", g_pb_firedrill_stats_obj.firedrill_triggered_count);
    return ZPN_RESULT_NO_ERROR;
}

void zpn_private_broker_firedrill_state_change_pb_to_b_conn(struct zpn_broker_client_fohh_state *cst_state,
                                                            int state)
{
    if(cst_state->public_broker_client->outbound_pb_to_b_client->conn) {
        if(state) {
            fohh_connection_enable_async(cst_state->public_broker_client->outbound_pb_to_b_client->conn,
                fohh_connection_incarnation(cst_state->public_broker_client->outbound_pb_to_b_client->conn));
        } else {
            fohh_connection_disable_async(cst_state->public_broker_client->outbound_pb_to_b_client->conn,
                fohh_connection_incarnation(cst_state->public_broker_client->outbound_pb_to_b_client->conn), FOHH_CLOSE_REASON_PRIVATE_BROKER_FIREDRILL);
        }
    }
}

void zpn_private_broker_pb_to_b_conns_change_state(enum pb_b_conn_change_state state)
{
    /* run through connected clients list */
    struct zpn_broker_connected_clients* connected_clients;
    struct zpn_broker_client_fohh_state* cst_state;

    connected_clients = &(broker.clients);
    pthread_mutex_lock(&(connected_clients->lock));
    LIST_FOREACH(cst_state, &(connected_clients->client_list), list_entry) {
        zpn_private_broker_firedrill_state_change_pb_to_b_conn(cst_state, state);
    }
    pthread_mutex_unlock(&(connected_clients->lock));
}

void zpn_private_broker_switch_to_cloud()
{
    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();
    // enable ctrl conn with broker
    if (gs->firedrill_status && gs->private_broker_state->broker_control) {
        fohh_connection_enable_async(gs->private_broker_state->broker_control,
                                      fohh_connection_incarnation(gs->private_broker_state->broker_control));
    }
    /* iterate over the cstates and check if pbza or pbmt (public_broker_client) is disabled, enable it */
    zpn_private_broker_pb_to_b_conns_change_state(PB_TO_B_CONN_ENABLE);
}

void zpn_private_broker_switch_to_pcc()
{
    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();

    /* disable ctrl conn with broker */
    if (gs->firedrill_status && gs->private_broker_state->broker_control) {
        ZPN_LOG(AL_ERROR, "firedrill DISABLE PRIVATE BROKER CONTROL CHANNEL");
        fohh_connection_disable_async(gs->private_broker_state->broker_control,
                                      fohh_connection_incarnation(gs->private_broker_state->broker_control), FOHH_CLOSE_REASON_PRIVATE_BROKER_FIREDRILL);
    }
    /* ToDo: disable pbza and pbmt conns */
    /* iterate over the cstates and check if pbza or pbmt (public_broker_client) is not null and disable it */
    zpn_private_broker_pb_to_b_conns_change_state(PB_TO_B_CONN_DISABLE);
}

int zpn_private_broker_mission_critical_request_cb(void* argo_cookie_ptr,
                                                    void* argo_structure_cookie_ptr,
                                                    struct argo_object* object)
{
    struct zpn_broker_mission_critical_resp *mc_resp = object->base_structure_void;
    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();

    ZPN_LOG(AL_NOTICE,"zpn_private_broker_mission_critical_request_cb firedrill entered");

    /* disconnect the amc connection */
    if (gs->private_broker_state->broker_mc) {
        fohh_connection_delete(gs->private_broker_state->broker_mc, FOHH_CLOSE_REASON_MISSION_ACCOMPLISHED);
    }

    /* check the firedrill status */
    if(mc_resp->firedrill_status) {

        ZPN_LOG(AL_NOTICE,"firedrill enabled, trigger firedrill again...");
        gs->firedrill_interval = mc_resp->firedrill_interval;

        /*status change will be done in start call */
        if(zpn_pbroker_firedrill_start(mc_resp->firedrill_interval)) {
            return ZPN_RESULT_ERR;
        }
        __sync_add_and_fetch(&g_pb_firedrill_stats_obj.firedrill_transit_count, 1);

    } else {
        struct zpn_firedrill_config firedrill_cfg = {0};

        ZPN_LOG(AL_NOTICE,"zpn_pbroker_mission_critical_request_cb firedrill disabled, connecting back to cloud");

        zpn_private_broker_switch_to_cloud();

        gs->firedrill_status = ZPN_PRIVATE_BROKER_FIREDRILL_DISABLED;

        //update the firedrill state to write to site_config.cfg
        firedrill_cfg.interval_s = zpn_private_broker_get_firedrill_interval();
        firedrill_cfg.start_time = zpn_private_broker_get_firedrill_starttime();
        firedrill_cfg.status = ZPN_PRIVATE_BROKER_FIREDRILL_DISABLED;

        zpn_pbroker_update_firedrill_config_to_site_config_file(&firedrill_cfg);

        /* increment disconnect count */
        __sync_add_and_fetch(&g_pb_firedrill_stats_obj.firedrill_completed_count, 1);
    }
    return ZPN_RESULT_NO_ERROR;
}

static int zpn_private_broker_mission_critical_unblock_callback(struct fohh_connection *connection,
                                                          enum fohh_queue_element_type element_type,
                                                          void *cookie)
{
    ZPN_LOG(AL_CRITICAL, "Implement me");
    return FOHH_RESULT_NO_ERROR;
}

void zpn_pbroker_send_mission_critical_req(void *cookie1, void *cookie2)
{
    struct zpn_broker_mission_critical mc_data = {0};
    struct fohh_connection *f_conn = cookie1;
    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();

    ZPN_LOG(AL_ERROR, "zpn_pbroker_send_mission_critical_req entered");

    mc_data.site_gid = gs->site_gid;
    if(zpn_broker_mission_critical_req(f_conn, 0, &mc_data)) {
        ZPN_LOG(AL_ERROR, "failed to send mission critical request");
        return;
    }
    ZPN_LOG(AL_ERROR, "firedrill pbroker sent mission critical message request successfully");
}

static int zpn_private_broker_mission_critical_conn_cb(struct fohh_connection *connection,
                                                                enum fohh_connection_state state,
                                                                void *cookie)
{
    int res = 0;
    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();

    /* As of now, this channel will be used for firedrill data fetching */
    ZPN_LOG(AL_NOTICE, "firedrill %ld: %s: pbroker mission critical connection entered", (long)fohh_peer_get_id(connection), fohh_description(connection));

    if (state == fohh_connection_connected) {
        ZPN_LOG(AL_NOTICE, "firedrill %ld: %s: pbroker mission critical connection connected", (long)fohh_peer_get_id(connection), fohh_description(connection));

        /* mc conn successful, del the timer */
        if(gs->pbroker_mc_timer) {
            event_del(gs->pbroker_mc_timer);
        }

        /* Connected, register callbacks */
        struct argo_state *argo = fohh_argo_get_rx(connection);

        if ((res = argo_register_structure(argo, zpn_broker_mission_critical_description_resp, zpn_private_broker_mission_critical_request_cb, connection))) {
            ZPN_LOG(AL_ERROR, "Could not register zpn_sitec_status_report for private broker for %s", fohh_description(connection));
            return res;
        }
        zevent_defer(zpn_pbroker_send_mission_critical_req, connection, NULL, 0);

    } else{
        /* Connection probably went away... */
        const char *reason = fohh_close_reason(connection);
        ZPN_LOG(AL_NOTICE, "%s: pbroker amc connection DOWN due to %s", fohh_description(connection), reason);

    }
    return res;
}

void zpn_private_broker_mc_timer_cb(evutil_socket_t sock, int16_t flags, void *cookie)
{
    /* we are hitting the timer means, pbmc is not successful, enter firedrill mode */
    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();

    ZPN_LOG(AL_INFO, "firedrill, pbmc connection failed, entering firedrill mode again for interval %"PRId64,gs->firedrill_interval);

    /* disconnect the pbmc connection */
    if (gs->private_broker_state->broker_mc) {
        fohh_connection_delete_async(gs->private_broker_state->broker_mc, gs->private_broker_state->broker_mc->incarnation, FOHH_CLOSE_REASON_MISSION_CRITICAL_CONN_FAILED);
    }

    if(gs->pbroker_mc_timer) {
        event_del(gs->pbroker_mc_timer);
    }
    __sync_add_and_fetch(&g_pb_firedrill_stats_obj.firedrill_transit_count, 1);

    /* trigger the firedrill again */
    if(gs->firedrill_interval && zpn_pbroker_firedrill_start(gs->firedrill_interval)) {
        ZPN_LOG(AL_INFO, "firedrill, mc conn failed, retriggering firedrill failed for interval %"PRId64,gs->firedrill_interval);
        return;
    }
}

void zpn_pbroker_mission_critical_conn_create(struct fohh_thread *thread, void *cookie, int64_t int_cookie)
{
    char broker_name[256];
    struct timeval tv = {0};

    const char *cloud_name = pbroker_get_redir_cloud_name();
    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();

    /* activate the timer for mc conn failure */
    tv.tv_sec = 60;
    tv.tv_usec = 0;
    if (event_add(gs->pbroker_mc_timer, &tv)) {
        ZPN_LOG(AL_ERROR, "Could not add status timer for pbmc conn failure check");
        return;
    }

    if (snprintf(pb_sni_customer_domain_mc, sizeof(pb_sni_customer_domain_mc), "%ld.pbmc.%s", (long) gs->private_broker_id, cloud_name) >= sizeof(pb_sni_customer_domain_mc)) {
        ZPN_LOG(AL_ERROR, "String is truncated when creating sni_name");
    }

    snprintf(broker_name, sizeof(broker_name), "any.pb2br.%s", cloud_name);

    gs->private_broker_state->broker_mc = fohh_client_create(FOHH_WORKER_ZPN_MC,
                                                                NULL,
                                                                argo_serialize_binary,
                                                                fohh_connection_style_argo,
                                                                0,
                                                                cookie,
                                                                zpn_private_broker_mission_critical_conn_cb,
                                                                NULL,
                                                                zpn_private_broker_mission_critical_unblock_callback,
                                                                NULL,
                                                                broker_name,
                                                                pb_sni_customer_domain_mc,
                                                                NULL,
                                                                htons(ZPN_PRIVATE_BROKER_TO_SITE_CONTROLLER_PORT),
                                                                gs->self_to_broker_ssl_ctx,
                                                                1,
                                                                ZPN_PBROKER_BROKER_RX_TIMEOUT_S);

    if (!gs->private_broker_state->broker_mc) {
        ZPN_LOG(AL_ERROR, "Failed to create mission critical connection to %s", broker_name);
        return;
    }

    ZPN_LOG(AL_NOTICE, "Successfully created mission critical connection to %s", broker_name);
}

void update_global_state_from_site_config(zpn_pbroker_site_config_t *site_config)
{
    zpn_private_broker_set_site_is_active(site_config->site_is_active);
    zpn_private_broker_set_sitec_preferred(site_config->sitec_preferred);
    zpn_private_broker_set_offline_domain(site_config->offline_domain);
    zpn_private_broker_set_reenroll_period(site_config->reenroll_period);
    zpn_private_broker_set_max_allowed_downtime_s(site_config->max_allowed_downtime_s);
    zpn_private_broker_set_firedrill_state(site_config->firedrill_cfg.status);
    zpn_private_broker_set_firedrill_interval(site_config->firedrill_cfg.interval_s);
    zpn_private_broker_set_firedrill_starttime(site_config->firedrill_cfg.start_time);
}

void zpn_pbroker_update_firedrill_config_to_site_config_file(struct zpn_firedrill_config *firedrill_cfg)
{
    /*
        read from the config file and compare it with the current session params
        site_config - values from the cfg file, updated to global structure
        firedrill_cfg - values from the current session
    */

    zpn_pbroker_site_config_t site_config = {};

    zpn_private_broker_site_config_load(&site_config);
    update_global_state_from_site_config(&site_config);

    zpn_private_broker_handle_config_change(
                zpn_private_broker_get_site_is_active(),
                zpn_private_broker_get_sitec_preferred(),
                zpn_private_broker_get_offline_domain(),
                zpn_private_broker_get_reenroll_period(),
                zpn_private_broker_get_allow_c2site(),
                zpn_private_broker_get_is_switchtime_enabled(),
                zpn_private_broker_get_max_allowed_downtime_s(),
                zpn_private_broker_get_max_allowed_switchtime_s(),
                SITE_FLIP_BY_FIREDRILL_CONFIG_CHANGED,
                firedrill_cfg);

    ZPN_LOG(AL_INFO,"firedrill config file update completed");
}
