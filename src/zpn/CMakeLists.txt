add_subdirectory(zins_inspection)
add_subdirectory(zbalance)
add_subdirectory(zpn_eas)
add_subdirectory(zpn_version_control)
add_subdirectory(zpn_vdi)

argo_parse_files(
    INPUT_FILES
        dsp_stats.h
        zpath_user.h
        zpn_aae.h
        zpn_aae_profile_conclusion.h
        zpn_aae_profile_definition.h
        zpn_app_group_relation.h
        zpn_app_protection_csp_profile.h
        zpn_application.c
        zpn_application.h
        zpn_application_def.h
        zpn_application_domain.h
        zpn_application_group.h
        zpn_application_group_application_mapping.h
        zpn_application_server.h
        zpn_approval.h
        zpn_approval_mapping.h
        zpn_assistant_config_override.h
        zpn_assistant_group.h
        zpn_assistant_table.h
        zpn_assistant_version.h
        zpn_assistantgroup_assistant_relation.h
        zpn_auto_server_test.c
        zpn_branch_connector.h
        zpn_broker.c
        zpn_broker_assistant.c
        zpn_broker_assistant_stats.c
        zpn_broker_balance_control.h
        zpn_broker_client.c
        zpn_broker_client.h
        zpn_broker_client_apps.c
        zpn_broker_client_neg_path_cache.c
        zpn_broker_client_path_cache.c
        zpn_broker_client_path_cache_cfg.c
        zpn_broker_client_path_cache_mock.c
        zpn_broker_client_private.h
        zpn_broker_client_scim.h
        zpn_broker_client_user_risk.h
        zpn_broker_dispatch.c
        zpn_broker_dispatch_c2c_client_check.h
        zpn_broker_dispatch_pool.h
        zpn_broker_drain.c
        zpn_broker_drain.h
        zpn_broker_ipars.h
        zpn_broker_load.h
        zpn_broker_maintenance.h
        zpn_broker_mtunnel_stats.h
        zpn_broker_natural.h
        zpn_broker_np.c
        zpn_broker_np.h
        zpn_broker_np_client.h
        zpn_broker_partition_stats.c
        zpn_broker_partition_stats.h
        zpn_broker_pbroker.c
        zpn_broker_pbroker_stats.c
        zpn_broker_proxy.c
        zpn_broker_sitec.c
        zpn_broker_stepup_auth.h
        zpn_broker_sys_stats.h
        zpn_c2c_client_registration.h
        zpn_c2c_ip_ranges.h
        zpn_cbi_mapping.h
        zpn_cbi_profile.h
        zpn_client_less.h
        zpn_client_table.h
        zpn_client_tracker.h
        zpn_customer.h
        zpn_customer_config.h
        zpn_customer_resiliency_settings.c
        zpn_customer_resiliency_settings.h
        zpn_customer_version_profile.h
        zpn_cxo_user.h
        zpn_cxo_user_token.h
        zpn_ddil_config.h
        zpn_firedrill_site.h
        zpn_event_notification.h
        zpn_exporter_client.h
        zpn_exporter_mtunnel.h
        zpn_extranet_resource.h
        zpn_file_fetch.c
        zpn_fohh_client_exporter.h
        zpn_fohh_worker.h
        zpn_idp.h
        zpn_idp_cert.h
        zpn_inspection_application.h
        zpn_ipars_rpc.h
        zpn_issuedcert.h
        zpn_location.h
        zpn_location_group.h
        zpn_location_group_to_location.h
        zpn_machine_group.h
        zpn_machine_table.h
        zpn_machine_to_group.h
        zpn_managed_browser_profile.h
        zpn_managed_chrome_extension.h
        zpn_mconn_fohh_tlv.c
        zpn_mconn_icmp.c
        zpn_mconn_icmp_common.c
        zpn_mconn_icmpv6.c
        zpn_nanolog_association.h
        zpn_pbroker_data_connection_stats.c
        zpn_pbroker_group.c
        zpn_pbroker_group.h
        zpn_pbroker_monitor.c
        zpn_pbroker_to_group.c
        zpn_pbroker_to_group.h
        zpn_pbrokergroup_pbroker_relation.h
        zpn_policy_engine.h
        zpn_policy_engine_build.h
        zpn_policy_set.h
        zpn_posture_profile.h
        zpn_private_broker_load_table.h
        zpn_private_broker_proxy.c
        zpn_private_broker_table.h
        zpn_private_broker_version.h
        zpn_privatebrokergroup_trustednetwork_mapping.h
        zpn_privileged_capabilities.h
        zpn_privileged_portal_rule.h
        zpn_public_cert.h
        zpn_ratelimit_policy.h
        zpn_restricted_entity.h
        zpn_rpc.h
        zpn_rule.h
        zpn_rule_condition_operand.h
        zpn_rule_condition_set.h
        zpn_rule_to_assistant_group.h
        zpn_rule_to_location.h
        zpn_rule_to_location_group.h
        zpn_rule_to_pse_group.h
        zpn_rule_to_server_group.h
        zpn_rule_to_step_up_auth_level_mapping.h
        zpn_saml_attrs.h
        zpn_save_point.h
        zpn_scim_attr_header.h
        zpn_scim_group.h
        zpn_scim_user.h
        zpn_scim_user_attribute.h
        zpn_scim_user_group.h
        zpn_scope.h
        zpn_scope_ready.h
        zpn_scope_engine.h
        zpn_server_group.h
        zpn_server_group_assistant_group.h
        zpn_server_group_to_location.h
        zpn_server_group_to_location_group.h
        zpn_servergroup_server_relation.h
        zpn_session_aggregate_store.h
        zpn_session_postures.h
        zpn_shared_customer_domain.h
        zpn_siem.h
        zpn_siem_rpc.h
        zpn_siem_rx.h
        zpn_siem_serialize_test.h
        zpn_siem_tx.c
        zpn_signing_cert.h
        zpn_site.h
        zpn_sitec_group.h
        zpn_sitec_table.h
        zpn_sitec_to_group.h
        zpn_sitec_version.h
        zpn_step_up_auth_level.h
        zpn_sub_module_upgrade.h
        zpn_svcp_profile.h
        zpn_system.h
        zpn_trusted_network.h
        zpn_user_risk.h
        zpn_user_to_event_notification.h
        zpn_userdb_common.h
        zpn_version.h
        zpn_version_profile.h
        zpn_workload_tag_group.c
        zpn_workload_tag_group.h
        zpn_znf.h
        zpn_znf_group.h
        zpn_znf_to_group.h
        et_blocked_country.h
        et_exempt_customer.h
        et_exempt_sni.h
        zpn_region_restriction.c
        zpn_client_setting.h
    OUTPUT_DIR ${CMAKE_CURRENT_BINARY_DIR}
    OUTPUT_FILES_VAR generated_headers
)

add_library(
    zpn
    STATIC
    assistant_log.c
    dsp_debug.c
    dsp_queue.c
    dsp_state.c
    dsp_stats.c
    zpn_aae.c
    zpn_aae_profile_conclusion.c
    zpn_app_group_relation.c
    zpn_app_protection_csp_profile.c
    zpn_application.c
    zpn_application_domain.c
    zpn_application_group.c
    zpn_application_group_application_mapping.c
    zpn_application_server.c
    zpn_approval.c
    zpn_approval_mapping.c
    zpn_assistant_group.c
    zpn_assistant_table.c
    zpn_assistant_version.c
    zpn_assistantgroup_assistant_relation.c
    zpn_branch_connector.c
    zpn_broker.c
    zpn_broker_assistant.c
    zpn_broker_assistant_stats.c
    zpn_broker_balance_control.c
    zpn_broker_client.c
    zpn_broker_client_apps.c
    zpn_broker_client_apps_build.c
    zpn_broker_client_apps_db.c
    zpn_broker_client_apps_debug.c
    zpn_broker_client_apps_merge.c
    zpn_broker_client_neg_path_cache.c
    zpn_broker_client_path_cache.c
    zpn_broker_client_path_cache_cfg.c
    zpn_broker_client_path_cache_mock.c
    zpn_broker_client_scim.c
    zpn_broker_client_svcp.c
    zpn_broker_client_user_risk.c
    zpn_broker_common.c
    zpn_broker_config_override_desc.c
    zpn_broker_cryptosvc.c
    zpn_broker_debug.c
    zpn_broker_dispatch.c
    zpn_broker_dispatch_c2c_client_check.c
    zpn_broker_dispatch_pool.c
    zpn_broker_dns.c
    zpn_broker_drain.c
    zpn_broker_ipars.c
    zpn_broker_ipars_request_queue.c
    zpn_broker_load.c
    zpn_broker_maintenance.c
    zpn_broker_mtunnel.c
    zpn_broker_mtunnel_stats.c
    zpn_broker_natural.c
    zpn_broker_np.c
    zpn_broker_np_client.c
    zpn_broker_partition_stats.c
    zpn_broker_pbroker.c
    zpn_broker_pbroker_stats.c
    zpn_broker_pm.c
    zpn_broker_policy.c
    zpn_broker_proxy.c
    zpn_broker_siem.c
    zpn_broker_sitec.c
    zpn_broker_stepup_auth.c
    zpn_broker_transit.c
    zpn_bufferevent_server.c
    zpn_c2c_client_registration.c
    zpn_cbi_mapping.c
    zpn_cbi_profile.c
    zpn_client.c
    zpn_client_dns_suffix_cache.c
    zpn_client_less.c
    zpn_client_table.c
    zpn_client_tracker.c
    zpn_connector.c
    zpn_connector_ssl_helpers.c
    zpn_connector_tun.c
    zpn_customer.c
    zpn_customer_config.c
    zpn_customer_resiliency_settings.c
    zpn_ddil_config.c
    zpn_firedrill_site.c
    zpn_debug.c
    zpn_dispatcher.c
    zpn_dispatcher_cfg_override_desc.c
    zpn_dta_overrides.c
    zpn_exporter_client.c
    zpn_exporter_mtunnel.c
    zpn_file_fetch.c
    zpn_fohh_client.c
    zpn_fohh_client_exporter.c
    zpn_fohh_client_pbroker.c
    zpn_fohh_client_slogger.c
    zpn_fohh_worker.c
    zpn_icmp_debug_interface.c
    zpn_icmp_rate_limit.c
    zpn_idp.c
    zpn_idp_cert.c
    zpn_inspection_application.c
    zpn_ipars_rpc.c
    zpn_issuedcert.c
    zpn_jit_approval_policy.c
    zpn_lib.c
    zpn_location.c
    zpn_location_group_to_location.c
    zpn_machine_group.c
    zpn_machine_table.c
    zpn_machine_to_group.c
    zpn_managed_browser_profile.c
    zpn_managed_chrome_extension.c
    zpn_mconn.c
    zpn_mconn_bufferevent.c
    zpn_mconn_buffereventpair.c
    zpn_mconn_fohh_tlv.c
    zpn_mconn_icmp.c
    zpn_mconn_icmp_common.c
    zpn_mconn_icmp_tlv.c
    zpn_mconn_icmp_util.c
    zpn_mconn_icmpv6.c
    zpn_mconn_transmit_buffer.c
    zpn_mconn_udp_bufferevent.c
    zpn_mconn_udp_tlv.c
    zpn_mconn_udp_util.c
    zpn_mconn_zrdt_tlv.c
    zpn_mtunnel.c
    zpn_pb_client.c
    zpn_pbroker_broker.c
    zpn_pbroker_broker_control.c
    zpn_pbroker_data_connection_stats.c
    zpn_pbroker_group.c
    zpn_pbroker_monitor.c
    zpn_pbroker_sitec_control.c
    zpn_pbroker_to_group.c
    zpn_pbrokergroup_pbroker_relation.c
    zpn_policy_engine.c
    zpn_policy_engine_build.c
    zpn_policy_engine_debug.c
    zpn_policy_engine_evaluate.c
    zpn_policy_overrides.c
    zpn_policy_set.c
    zpn_posture_profile.c
    zpn_private_broker_load_table.c
    zpn_private_broker_proxy.c
    zpn_private_broker_site.c
    zpn_private_broker_site_config.c
    zpn_private_broker_table.c
    zpn_private_broker_util.c
    zpn_private_broker_version.c
    zpn_privatebrokergroup_trustednetwork_mapping.c
    zpn_privileged_capabilities.c
    zpn_privileged_portal_rule.c
    zpn_public_cert.c
    zpn_rate_limit.c
    zpn_rpc.c
    zpn_rule.c
    zpn_rule_condition_operand.c
    zpn_rule_condition_set.c
    zpn_rule_to_assistant_group.c
    zpn_rule_to_location.c
    zpn_rule_to_location_group.c
    zpn_rule_to_pse_group.c
    zpn_rule_to_server_group.c
    zpn_rule_to_step_up_auth_level_mapping.c
    zpn_saml_attrs.c
    zpn_scim_attr_header.c
    zpn_scim_group.c
    zpn_scim_user.c
    zpn_scim_user_attribute.c
    zpn_scim_user_group.c
    zpn_scope.c
    zpn_scope_engine.c
    zpn_scope_ready.c
    zpn_server_group.c
    zpn_server_group_assistant_group.c
    zpn_servergroup_server_relation.c
    zpn_session_aggregate_store.c
    zpn_session_postures.c
    zpn_session_trusted_networks.c
    zpn_shared_customer_domain.c
    zpn_siem.c
    zpn_siem_rx.c
    zpn_siem_serialize.c
    zpn_siem_tx.c
    zpn_signing_cert.c
    zpn_site.c
    zpn_sitec_group.c
    zpn_sitec_log.c
    zpn_sitec_table.c
    zpn_sitec_to_group.c
    zpn_sitec_version.c
    zpn_step_up_auth_level.c
    zpn_sub_module_upgrade.c
    zpn_svcp_profile.c
    zpn_system.c
    zpn_trusted_network.c
    zpn_user_risk.c
    zpn_userdb_common.c
    zpn_workload_tag_group.c
    zpn_znf.c
    zpn_znf_group.c
    zpn_znf_to_group.c
    zpn_zrdt.c
    zpn_zrdt_client.c
    zpn_zrdt_debug.c
    zpn_region_restriction.c
    zpn_region_restriction_logging.c
    et_blocked_country.c
    et_exempt_customer.c
    et_exempt_sni.c
    zpn_region_restriction_private.c
    zpn_client_setting.c
    ${generated_headers}
)

target_link_libraries(
    zpn
    PUBLIC
        LibCcronexpr
        ICU::i18n
        ICU::uc
        ICU::data
        admin_probe
        lookup_lib
        np_lib
        object_store
        zbalance
        zcrypto_lib
        zhealth
        zins_inspection
        zpn_cache
        zpn_dr
        zpn_eas
        zpn_event
        zpn_ot
        zpn_pcap
        zpn_pdp
        zpn_version_control
        zpn_waf
        zpn_zdx
        zpn_zdx_webprobe
        zsaml
        ztimer
        ztlv
        zpath_lib
        zpn_vdi
)

add_simple_apps(
    SOURCES
        file_fetch_test.c
        merge_test.c
        zhealth_ping_test.c
        zpn_auto_server_test.c
        zpn_broker_client_path_cache_test_scale.c
        zpn_broker_dispatch_pool_test.c
        zpn_broker_transit_test.c
        zpn_bufferevent_server_test.c
        zpn_c2c_registration_test.c
        zpn_client_dns_suffix_cache_test.c
        zpn_crypt_tool.c
        zpn_echo.c
        zpn_echo2.c
        zpn_fohh_client_exporter_test.c
        zpn_fohh_client_pbroker_test.c
        zpn_fohh_client_slogger_test.c
        zpn_fohh_client_test.c
        zpn_fohh_json_test.c
        zpn_fohh_machine_tunnel_client_test.c
        zpn_icmp_mtr_test.c
        zpn_icmp_ping_test.c
        zpn_icmp_rate_limit_unit_test.c
        zpn_mconn_test.c
        zpn_mconn_transmit_buffer_test.c
        zpn_mconn_udp_util_test.c
        zpn_object_store_serializable_test.c
        zpn_siem_serialize_test.c
        zpn_siem_test.c
        zpn_tcp_mtr_test.c
        zpn_tcp_ping_test.c
        zpn_test.c
        zpn_test_app_client.c
        zpn_test_app_server.c
        zpn_thread_test.c
        zpn_zdx_cache_test.c
        zpn_zdx_http_test.c
        zpn_zdx_probes_test_client.c
    DEPS zpn zhealth LibPCAP object_store zpath_app
)

add_dependencies(zpn_fohh_json_test gen-dev-certs)

add_simple_apps(SOURCES zpn_zdx_probes_test_server.c DEPS zpn_zdx_combiner zpn zpath_app)

add_simple_apps(
    SOURCES
        zpn_dispatcher_test.cpp
        dsp_queue_test.cpp
        zpn_hostname_validation_test.cpp
        zpn_brk_dsp_circuit_breaker_test.cpp
    DEPS zpn GTest::gtest_main GTest::gmock zpath_app
)

add_executable(
    zpn_policy_engine_test
    zbalance/zpn_balance_redirect.c
    zpn_broker_client_apps_db_test.c
    zpn_policy_engine_static.c
    zpn_policy_engine_test.c
)

target_compile_definitions(zpn_policy_engine_test PRIVATE _UNIT_TEST)
target_link_libraries(zpn_policy_engine_test PRIVATE zpn zpath_app)
add_dependencies(zpn_policy_engine_test gen-headers-exporter)

add_simple_tests(
    dsp_queue_test
    file_fetch_test
    merge_test
    zpn_broker_dispatch_pool_test
    zpn_c2c_registration_test
    zpn_dispatcher_test
    zpn_hostname_validation_test
    zpn_object_store_serializable_test
    zpn_siem_serialize_test
    zpn_brk_dsp_circuit_breaker_test
)

add_test_with_args(BINARY zpn_policy_engine_test ARGS policy_engine_test/new_linearize_test.sh)
add_test_with_args(BINARY zpn_policy_engine_test ARGS policy_engine_test/test.sh)
add_test_with_args(BINARY zpn_policy_engine_test ARGS policy_engine_test/test2.sh)
add_test_with_args(BINARY zpn_policy_engine_test ARGS policy_engine_test/load_policy.sh)
add_test_with_args(BINARY zpn_policy_engine_test ARGS policy_engine_test/et-32842.sh)
add_test_with_args(BINARY zpn_policy_engine_test ARGS policy_engine_test/et-35488.sh)
add_test_with_args(BINARY zpn_policy_engine_test ARGS policy_engine_test/firewall_style_policy_test.sh)
add_test_with_args(BINARY zpn_policy_engine_test ARGS policy_engine_test/csp.sh)
add_test_with_args(BINARY zpn_policy_engine_test ARGS policy_engine_test/csp_user_portal.sh)
add_test_with_args(BINARY zpn_policy_engine_test ARGS policy_engine_test/et-65185.sh)
add_test_with_args(BINARY zpn_policy_engine_test ARGS policy_engine_test/et-69132.sh)
# add_test_with_args(BINARY zpn_policy_engine_test ARGS policy_engine_test/et-41688.sh)
# add_test_with_args(BINARY zpn_policy_engine_test ARGS policy_engine_test/et-44793.sh)
add_test_with_args(BINARY zpn_policy_engine_test ARGS client_apps_test/inclusive_app_domain_test_1.sh)
add_test_with_args(BINARY zpn_policy_engine_test ARGS client_apps_test/inclusive_app_domain_test_2.sh)
add_test_with_args(BINARY zpn_policy_engine_test ARGS policy_engine_test/cred_map_tests.sh)
add_test_with_args(BINARY zpn_policy_engine_test ARGS policy_engine_test/et-67861-saml.sh)
add_test_with_args(BINARY zpn_policy_engine_test ARGS policy_engine_test/et-67861-scim.sh)
add_test_with_args(BINARY zpn_policy_engine_test ARGS policy_engine_test/et-79354-aae.sh)
add_test_with_args(BINARY zpn_policy_engine_test ARGS policy_engine_test/np_access_policy.sh)
add_test_with_args(BINARY zpn_policy_engine_test ARGS policy_engine_test/et-88326.sh)
add_test_with_args(BINARY zpn_policy_engine_test ARGS client_apps_test/basic.sh)
add_test_with_args(BINARY zpn_policy_engine_test ARGS client_apps_test/segment.sh)
add_test_with_args(BINARY zpn_policy_engine_test ARGS client_apps_test/app_domain_tests.sh)
add_test_with_args(BINARY zpn_policy_engine_test ARGS client_apps_test/et-16359_et-16844.sh)
add_test_with_args(BINARY zpn_policy_engine_test ARGS client_apps_test/et-16827.sh)
add_test_with_args(BINARY zpn_policy_engine_test ARGS client_apps_test/et-16919.sh)
add_test_with_args(BINARY zpn_policy_engine_test ARGS client_apps_test/et-17920.sh)
add_test_with_args(BINARY zpn_policy_engine_test ARGS policy_engine_test/et-67862.sh)
add_test_with_args(BINARY zpn_policy_engine_test ARGS client_apps_test/et-20267.sh)
# add_test_with_args(BINARY zpn_policy_engine_test ARGS client_apps_test/et-25587.sh)
add_test_with_args(BINARY zpn_policy_engine_test ARGS client_apps_test/et-29472.sh)
add_test_with_args(BINARY zpn_policy_engine_test ARGS client_apps_test/et-29473.sh)
add_test_with_args(BINARY zpn_policy_engine_test ARGS client_apps_test/et-35689.sh)
add_test_with_args(BINARY zpn_policy_engine_test ARGS client_apps_test/ES-709/ES-709.sh)
# add_test_with_args(BINARY zpn_policy_engine_test ARGS client_apps_test/et-39991-dta.sh)
# add_test_with_args(BINARY zpn_policy_engine_test ARGS client_apps_test/et-40635.sh)
# add_test_with_args(BINARY zpn_policy_engine_test ARGS client_apps_test/et-40635-1.sh)
# add_test_with_args(BINARY zpn_policy_engine_test ARGS client_apps_test/et-40280-dtachk.sh)
add_test_with_args(BINARY zpn_policy_engine_test ARGS client_apps_test/jit-approval-tests.sh)
add_test_with_args(BINARY zpn_policy_engine_test ARGS client_apps_test/embedded_app_download_restrict.sh)
add_test_with_args(BINARY zpn_policy_engine_test ARGS constellation_tests/et-24544.sh)
# Failure TBD: ET-79637 - add_test_with_args(BINARY zpn_policy_engine_test ARGS constellation_tests/et-23388.sh)
# Failure TBD: ET-79637 - add_test_with_args(BINARY zpn_policy_engine_test ARGS zbalance/tests/public.sh)
add_test_with_args(BINARY zpn_policy_engine_test ARGS policy_engine_test/test_redir.sh)
add_test_with_args(BINARY zpn_policy_engine_test ARGS zbalance/tests/private.sh)
add_test_with_args(BINARY zpn_policy_engine_test ARGS zbalance/tests/grace_distance.sh)
# Failure TBD: ET-79637 - add_test_with_args(BINARY zpn_policy_engine_test ARGS zbalance/tests/geofencing.sh)
add_test_with_args(BINARY zpn_policy_engine_test ARGS zbalance/tests/mtn.sh)
add_test_with_args(BINARY zpn_policy_engine_test ARGS zbalance/tests/acas.sh)
add_test_with_args(BINARY zpn_policy_engine_test ARGS zbalance/tests/mem_normalization.sh)
add_test_with_args(BINARY zpn_policy_engine_test ARGS zbalance/tests/partition_profile.sh)
#add_test_with_args(BINARY zpn_policy_engine_test ARGS zbalance/tests/partition_redirect.sh)
# Failure TBD: ET-79637 - add_test_with_args(BINARY zpn_policy_engine_test ARGS zbalance/tests/et-66985.sh)

#TODO re-add these tests once working
#        zpn_zdx_probes_test_client
#    zpn_zdx_probes_test_server)

# since zpn is really unwieldy - lets make a small library for region_restriction so
# so that we can unit test it cleanly
add_library(
    zpn_region_restriction
    STATIC
    zpn_region_restriction.c
    zpn_region_restriction_logging.c
    et_blocked_country.c
    et_exempt_customer.c
    et_exempt_sni.c
    zpn_region_restriction_private.c
    ${generated_headers}
)

add_library(zpn_firedrill_site STATIC zpn_firedrill_site.c ${generated_headers})
target_link_libraries(zpn_region_restriction PUBLIC argo zpath_lib wally)
create_testing_library(LIBRARY zpn_region_restriction)

target_link_libraries(zpn_firedrill_site PUBLIC argo zpath_lib wally)
create_testing_library(LIBRARY zpn_firedrill_site)

create_testing_library(LIBRARY zpn)

# Non Lib specific stuff
add_subdirectory(gtests)
