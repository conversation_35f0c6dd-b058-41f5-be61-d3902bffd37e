/*
 * zpn_lib.c. Copyright (C) 2014 Zscaler Inc. All Rights Reserved
 */

#include "zpath_lib/zpath_lib.h"
#include "zpath_lib/zpath_app.h"
#include "zpn/zpn_lib.h"
#include "zpn/zpn_rpc.h"
#include "zpath_lib/zpath_local.h"
#include <openssl/err.h>
#include "zpath_lib/zpath_cloud.h"

int flow_control_enabled = 1;
int no_udp_packetize_on_tlv = 0;
int zpn_app_request_atleast_one = 1;
int broker_app_calc_threads_count = BROKER_APP_CALC_THREADS_DEFAULT;

struct argo_log_collection *zpn_event_collection = NULL;
struct zpath_allocator zpn_allocator = ZPATH_ALLOCATOR_INIT("zpn");

/*
 * NOTE: Broker rewrites zpn debug on startup
 *
 * For new features we turn on the debug flags by default and slowly turn
 * it off once we get the confidence. So if you find few flags turned ON, its
 * ok.
 */
uint64_t zpn_debug_ext[ZPN_DEBUG_ARRAY_SIZE_64] = {0};
uint64_t zpn_debug_catch_defaults_ext[ZPN_DEBUG_ARRAY_SIZE_64] = {0};

const char *zpn_debug_names[] = ZPN_DEBUG_NAMES;

const char *zpn_result_strings[] = {
    [ZPN_RESULT_NO_ERROR] = "ZPN_RESULT_NO_ERROR",
    [ZPN_RESULT_ERR] = "ZPN_RESULT_ERR",
    [ZPN_RESULT_NOT_FOUND] = "ZPN_RESULT_NOT_FOUND",
    [ZPN_RESULT_NO_MEMORY] = "ZPN_RESULT_NO_MEMORY",
    [ZPN_RESULT_CANT_WRITE] = "ZPN_RESULT_CANT_WRITE",
    [ZPN_RESULT_ERR_TOO_LARGE] = "ZPN_RESULT_ERR_TOO_LARGE",
    [ZPN_RESULT_BAD_ARGUMENT] = "ZPN_RESULT_BAD_ARGUMENT",
    [ZPN_RESULT_INSUFFICIENT_DATA] = "ZPN_RESULT_INSUFFICIENT_DATA",
    [ZPN_RESULT_NOT_IMPLEMENTED] = "ZPN_RESULT_NOT_IMPLEMENTED",
    [ZPN_RESULT_BAD_DATA] = "ZPN_RESULT_BAD_DATA",
    [ZPN_RESULT_WOULD_BLOCK] = "ZPN_RESULT_WOULD_BLOCK",
    [ZPN_RESULT_BAD_STATE] = "ZPN_RESULT_BAD_STATE",
    [ZPN_RESULT_INCOMPLETE] = "ZPN_RESULT_INCOMPLETE",
    [ZPN_RESULT_ASYNCHRONOUS] = "ZPN_RESULT_ASYNCHRONOUS",
    [ZPN_RESULT_EXCESS_DYN_FIELDS] = "ZPN_RESULT_EXCESS_DYN_FIELDS",
    [ZPN_RESULT_NOT_READY] = "ZPN_RESULT_NOT_READY",
    [ZPN_RESULT_EXPIRED] = "ZPN_RESULT_EXPIRED",
    [ZPN_RESULT_ACCESS_DENIED] = "ZPN_RESULT_ACCESS_DENIED",
    [ZPN_RESULT_AL_AUTH_REQUIRED] = "ZPN_RESULT_AL_AUTH_REQUIRED",
    [ZPN_RESULT_AL_AUTH_EXPIRED] = "ZPN_RESULT_AL_AUTH_EXPIRED",
    [ZPN_RESULT_CUR_MCONN_BLOCK_AND_NO_IMPACT_ON_SHARED_RESOURCE] = "ZPN_RESULT_CUR_MCONN_BLOCK_AND_NO_IMPACT_ON_SHARED_RESOURCE"
};

struct zpn_client_capability zpn_client_static_config[zpn_client_type_total_count] =
    {
     //                                                                           ENABLED                       SIEM_RULES                    SEGMENTS                      USE_CN_AS_UID                 REDIR_PRIV_PB                 APP_ADD_STAR                  SCIM_COMPATIBLE               FOHH_AUTOTUNE                 TARGET_APP_GID                DRAIN_CONN                    WORKLOAD_TAG_GRP            APPSCALE_V2
     //                                      NAME                                           USES_SAML                     GETS_APPS                     FWD_TO_PUB                    TELEMETRY                     REDIR_PUB_PB                  LOCATION_POLICY               AC_LOOPING                    IGN_CACHE                     ZIA_INSPECTION                MULTI_MATCH                   GETS_NP_APPS      |         NEEDS_FULL_APP_DATA_ON_APP_CHECK
     //                                                                                               GLOBAL_RULES                  GETS_IPS                      CAN_SRV_GRP                   FOHH_REDIR                    MULT_DC                       HAS_ZNF_DATA_FOR_POLICY       TUNNEL_PR                     HAS_USR_RISK                  POLICY_REDIRECT               PTAG                         APPSCALE |         |
     [zpn_client_type_invalid] =           {"zpn_client_type_invalid",            0,        0,        0,        0,        0,        0,        0,        0,        0,        0,        0,        0,        0,        0,        0,        0,        0,        0,        0,        0,        0,        0,        0,        0,        0,        0,        0,        0,        0,        0,        0,        0,       0,       0,        0},  // zpn_client_type_invalid
     [zpn_client_type_zapp] =              {"zpn_client_type_zapp",               1,        1,        1,        0,        1,        0,        0,        1,        0,        0,        0,        1,        1,        1,        1,        1,        0,        0,        1,        0,        1,        1,        0,        1,        0,        1,        1,        1,        1,        1,        0,        1,       1,       1,        0},  // zpn_client_type_zapp
     [zpn_client_type_slogger] =           {"zpn_client_type_slogger",            1,        0,        0,        1,        0,        0,        0,        0,        0,        1,        0,        1,        0,        0,        0,        0,        0,        0,        0,        0,        0,        0,        1,        0,        1,        0,        0,        1,        0,        0,        0,        0,       0,       0,        0},  // zpn_client_type_slogger
     [zpn_client_type_exporter] =          {"zpn_client_type_exporter",           1,        1,        1,        0,        0,        0,        0,        0,        0,        0,        0,        1,        0,        0,        0,        0,        0,        0,        1,        0,        0,        0,        0,        1,        0,        0,        0,        1,        0,        1,        0,        0,       0,       0,        0},  // zpn_client_type_exporter
     [zpn_client_type_edge_connector] =    {"zpn_client_type_edge_connector",     1,        0,        1,        0,        1,        1,        1,        1,        1,        1,        1,        1,        1,        1,        0,        0,        1,        1,        0,        1,        0,        0,        0,        0,        0,        1,        1,        1,        1,        0,        1,        0,       0,       1,        1},  // zpn_client_type_edge_connector
     [zpn_client_type_private_broker] =    {"zpn_client_type_private_broker",     1,        0,        0,        0,        0,        0,        0,        0,        0,        0,        0,        1,        0,        0,        1,        0,        0,        0,        0,        0,        0,        0,        0,        0,        0,        0,        0,        1,        1,        0,        0,        0,       0,       0,        0},  // zpn_client_type_private_broker
     [zpn_client_type_ip_anchoring] =      {"zpn_client_type_ip_anchoring",       1,        0,        1,        0,        1,        1,        1,        0,        1,        1,        1,        1,        0,        0,        0,        0,        1,        0,        0,        0,        0,        0,        0,        0,        1,        0,        0,        1,        0,        1,        0,        0,       0,       0,        0},  // zpn_client_type_ip_anchoring
     [zpn_client_type_browser_isolation] = {"zpn_client_type_browser_isolation",  1,        1,        1,        0,        1,        0,        0,        0,        0,        0,        0,        1,        0,        0,        0,        0,        0,        0,        1,        0,        0,        0,        0,        1,        0,        0,        0,        1,        0,        1,        0,        0,       0,       0,        0},  // zpn_client_type_browser_isolation
     [zpn_client_type_exporter_noauth] =   {"zpn_client_type_exporter_noauth",    1,        0,        0,        0,        0,        0,        0,        0,        0,        1,        0,        1,        0,        0,        0,        0,        0,        0,        0,        0,        0,        0,        0,        0,        0,        0,        0,        1,        0,        1,        0,        0,       0,       0,        0},  // zpn_client_type_exporter_noauth
     [zpn_client_type_machine_tunnel] =    {"zpn_client_type_machine_tunnel",     1,        0,        1,        0,        1,        0,        0,        1,        0,        0,        0,        1,        1,        1,        1,        1,        0,        0,        0,        0,        1,        1,        0,        0,        0,        0,        1,        1,        1,        1,        0,        0,       1,       1,        0},  // zpn_client_type_machine_tunnel
     [zpn_client_type_broker_transit] =    {"zpn_client_type_broker_transit",     1,        0,        0,        0,        0,        0,        0,        0,        0,        0,        0,        0,        0,        0,        0,        0,        0,        0,        0,        0,        0,        0,        0,        0,        0,        0,        0,        0,        0,        0,        0,        0,       0,       0,        0},  // zpn_client_type_broker_transit
     [zpn_client_type_branch_conn_svc] =   {"zpn_client_type_branch_conn_svc",    1,        0,        1,        0,        0,        0,        0,        0,        0,        1,        0,        1,        0,        0,        0,        0,        0,        0,        0,        0,        0,        0,        0,        0,        0,        0,        0,        1,        0,        0,        0,        0,       0,       0,        0},  // zpn_client_type_branch_conn_svc
     [zpn_client_type_zapp_partner] =      {"zpn_client_type_zapp_partner",       1,        1,        1,        0,        1,        0,        0,        1,        0,        0,        0,        1,        1,        1,        1,        1,        0,        0,        1,        0,        1,        1,        0,        1,        0,        0,        1,        1,        1,        0,        0,        0,       1,       1,        0},  // zpn_client_type_zapp_partner
     [zpn_client_type_branch_connector] =  {"zpn_client_type_branch_connector",   1,        0,        1,        0,        1,        1,        1,        1,        1,        1,        1,        1,        1,        1,        0,        0,        1,        0,        0,        1,        0,        0,        0,        0,        0,        1,        1,        1,        0,        1,        0,        0,       0,       0,        0},  // zpn_client_type_branch_connector
     [zpn_client_type_zia_inspection] =    {"zpn_client_type_zia_inspection",     1,        0,        1,        0,        1,        1,        1,        0,        1,        1,        1,        0,        0,        0,        0,        0,        1,        0,        0,        0,        0,        0,        0,        0,        0,        0,        0,        0,        0,        1,        0,        0,       0,       0,        0},  // zpn_client_type_zia_inspection
     [zpn_client_type_vdi] =               {"zpn_client_type_vdi",                1,        0,        1,        0,        1,        1,        1,        1,        1,        1,        1,        1,        1,        1,        0,        0,        1,        1,        1,        1,        0,        0,        0,        0,        0,        1,        1,        1,        1,        0,        0,        0,       0,       1,        1},  // zpn_client_type_vdi
     [zpn_client_type_zia_proxy] =         {"zpn_client_type_zia_proxy",          0,        0,        0,        0,        0,        0,        0,        0,        0,        0,        0,        0,        0,        0,        0,        0,        0,        0,        0,        0,        0,        0,        0,        0,        0,        0,        0,        0,        0,        0,        0,        0,       0,       0,        0},  // zpn_client_type_zia_proxy
     [zpn_client_type_assistant] =         {"zpn_client_type_assistant",          1,        0,        0,        0,        0,        0,        0,        0,        0,        0,        0,        0,        0,        0,        1,        0,        0,        0,        0,        0,        0,        0,        0,        0,        0,        0,        0,        0,        0,        0,        0,        0,       0,       0,        0},  // zpn_client_type_assistant
     [zpn_client_type_site_controller] =   {"zpn_client_type_site_controller",    1,        0,        0,        0,        0,        0,        0,        0,        0,        0,        0,        1,        0,        0,        1,        0,        0,        0,        0,        0,        0,        0,        0,        0,        0,        0,        0,        1,        0,        0,        0,        0,       0,       0,        0},  // zpn_client_type_site_controller
     [zpn_client_type_eas_ctrl] =          {"zpn_client_type_eas_ctrl",           1,        0,        1,        0,        0,        0,        0,        0,        1,        1,        1,        1,        0,        0,        0,        0,        0,        0,        0,        0,        0,        0,        0,        0,        0,        0,        0,        0,         0,       0,        0,        0,       0,       0,        0},  // zpn_client_type_eas_ctrl
     [zpn_client_type_eas_data] =          {"zpn_client_type_eas_data",           1,        0,        1,        0,        0,        0,        0,        0,        1,        1,        1,        0,        0,        0,        0,        0,        0,        0,        0,        0,        0,        0,        0,        0,        0,        0,        0,        0,         0,       0,        0,        0,       0,       0,        0},  // zpn_client_type_eas_data
    };

struct zpn_tunnel_capability zpn_tunnel_static_config[zpn_tunnel_auth_total_count] =
    {
     //                                      NAME                              ENABLED  GETS_APPS   TUNNEL_PR   FOHH_AUTOTUNE
     [zpn_tunnel_auth_invalid] =           {"zpn_tunnel_auth_invalid",               0,         0,          0,              0},
     [zpn_tunnel_auth_cloud] =             {"zpn_tunnel_auth_cloud",                 1,         0,          0,              0},
     [zpn_tunnel_auth_zapp] =              {"zpn_tunnel_auth_zapp",                  1,         1,          1,              1},
     [zpn_tunnel_auth_connector] =         {"zpn_tunnel_auth_connector",             1,         0,          0,              0},
     [zpn_tunnel_auth_znf] =               {"zpn_tunnel_auth_znf",                   1,         1,          0,              0},
     [zpn_tunnel_auth_private_broker] =    {"zpn_tunnel_auth_private_broker",        1,         0,          0,              0},
     [zpn_tunnel_auth_site_controller] =   {"zpn_tunnel_auth_site_controller",       1,         0,          0,              0},
     [zpn_tunnel_auth_machine_tunnel] =    {"zpn_tunnel_auth_machine_tunnel",        1,         1,          1,              1},
     [zpn_tunnel_auth_browser_isolation] = {"zpn_tunnel_auth_browser_isolation",     1,         1,          0,              0},
     [zpn_tunnel_auth_zapp_partner] =      {"zpn_tunnel_auth_zapp_partner",          1,         1,          1,              1},
     [zpn_tunnel_auth_branch_connector] =  {"zpn_tunnel_auth_branch_connector",      1,         1,          0,              0},
    };

const char *zmt_string[ZMT_COUNT] = {
    [zmt_name] = ZMT_NAME_STR,
    [zmt_dns_srv] = ZMT_DNS_SRV_STR,
    [zmt_ip] = ZMT_IP_STR,
    [zmt_use_tls] = ZMT_USE_TLS_STR,
    [zmt_guac] = ZMT_GUAC_STR
};

const char *invalid_res_str = "INVALID_RESULT";

// returns array position of bit for 64 bit integer
uint16_t zpn_bit_to_array_pos(uint64_t bitmap)
{
   assert(bitmap);
   uint16_t position = 0;
   while(bitmap){
	 if (bitmap & 1ULL)
	   goto done;
     position++;
     bitmap = bitmap >> 1ULL;
   }
done:
   return position;
}

// convert legacy to present design
void zpn_debug_set_from_bits(uint64_t bitmap)
{
#define BITS_IN_UINT64 64
   for (uint16_t i = 0; i < BITS_IN_UINT64; i++) {
     bitmap & 1ULL ? zpn_debug_set(i) : zpn_debug_reset(i);
     bitmap = bitmap >> 1ULL;
   }
}

// convert legacy to present design
void zpn_debug_set_update_from_bits(uint64_t bitmap)
{
#define BITS_IN_UINT64 64
   for (uint16_t i = 0; i < BITS_IN_UINT64; i++) {
	 if (bitmap & 1ULL)
       zpn_debug_set(i);
     bitmap = bitmap >> 1ULL;
   }
}

// convert legacy to present design; this is slow bcoz of
// zpn_bit_to_array_pos(), but once conversion to indices
// are done, we don't need this function
uint8_t zpn_debug_get_from_bit(uint64_t bit)
{
   return(zpn_debug_get(zpn_bit_to_array_pos(bit)));
}

//////////////////////////////////////////////////////////

const char *zpn_result_string(int result)
{
    if (result >= (sizeof(zpn_result_strings) / sizeof (const char *))) return invalid_res_str;
    if (result < 0) return invalid_res_str;
    if (zpn_result_strings[result] == NULL) return invalid_res_str;
    return zpn_result_strings[result];
}

const char *zpn_client_type_string(enum zpn_client_type type)
{
    return zpn_client_static_config[type].name;
}

enum zpn_client_type zpn_client_type_from_str(const char* str) {
    if (!str) return zpn_client_type_invalid;
    for (int i = 0; i < zpn_client_type_total_count; i++) {
        if (!strcmp(zpn_client_static_config[i].name, str))
            return i;
    }
    return zpn_client_type_invalid;
}

const char *zpn_auth_type_string(enum zpn_tunnel_auth auth_type)
{
    return zpn_tunnel_static_config[auth_type].name;
}
/*
   validates client_type and returns a const pointer, not needed to be freed. c2c uses it
*/
const char *zpn_client_type_string_from_string(const char* str) {
    if (!str) return NULL;
    for (int i = 0; i < zpn_client_type_total_count; i++) {
        if (!strcmp(zpn_client_static_config[i].name, str))
            return zpn_client_static_config[i].name;
    }
    return NULL;
}

int zpn_client_type_is_valid(enum zpn_client_type type)
{
    if ((type > zpn_client_type_invalid) && (type < zpn_client_type_total_count)) {
        return 1;
    } else {
        return 0;
    }
}

/*
 * Internally we use zmt_ prefix. But zapp don't use the prefix. Hence two tables are compared against.
 * Default is zmt_name (if the input is invalid)
 */
enum zpn_mtunnel_type zpn_app_type_from_str(const char *app_type)
{
    static int                      log_throttler = 0;
#define ZPN_APP_TYPE_FROM_STR_MAX_ZAPP_TYPES 4
    int i;
    const struct {
        char*                   app_type_str;
        enum zpn_mtunnel_type   app_type_value;
    } zapp_mapping[ZPN_APP_TYPE_FROM_STR_MAX_ZAPP_TYPES] =
    {
            {"ip", zmt_ip},
            {"dns-srv", zmt_dns_srv},
            {"name", zmt_name},
            {"zmt_guac", zmt_guac},
    };

    if (!app_type) {
        goto done_fail;
    }
    for (i = 0; i < ZMT_COUNT; i++) {
        if (strcmp(app_type, zmt_string[i]) == 0) {
            return i;
        }
    }
    for (i = 0; i < ZPN_APP_TYPE_FROM_STR_MAX_ZAPP_TYPES; i++) {
        if (strcmp(app_type, zapp_mapping[i].app_type_str) == 0) {
            return zapp_mapping[i].app_type_value;
        }
    }

done_fail:
    if (0 == (log_throttler % 1000)) {
        ZPN_LOG(AL_ERROR, "Couldn't find app type from string - %s", app_type ? app_type : "");
    }
    log_throttler++;
    return zmt_name;
}


const char* zpn_app_type_to_str(enum zpn_mtunnel_type type)
{
    return zmt_string[type];
}

int zpn_log_structure(struct argo_structure_description *description, void *structure_data)
{
    if (zpn_debug_get(ZPN_DEBUG_LOG_IDX)) {
        return argo_log_structure_immediate(zpn_event_collection,
                                            AL_DEBUG,
                                            0,
                                            "zpn_structure_log",
                                            description,
                                            structure_data);
    } else {
        return ZPN_RESULT_NO_ERROR;
    }
}

int zpn_init(struct argo_log_collection *event_log)
{
    zpn_event_collection = event_log;
    zpn_debug_set(ZPN_DEBUG_SIEM_IDX);
    zpn_debug_set(ZPN_DEBUG_SIEM_ALL_IDX);
    zpn_debug_set(ZPN_DEBUG_PRIVATE_BROKER_IDX);
    zpn_debug_set(ZPN_DEBUG_MTN_IDX);
    zpn_debug_set(ZPN_DEBUG_CLIENT_IDX);

    zpn_debug_catch_defaults_set(ZPN_DEBUG_CLIENT_IDX);
    zpn_debug_catch_defaults_set(ZPN_DEBUG_BROKER_IDX);
    zpn_debug_catch_defaults_set(ZPN_DEBUG_APPLICATION_IDX);
    zpn_debug_catch_defaults_set(ZPN_DEBUG_RULE_IDX);
    zpn_debug_catch_defaults_set(ZPN_DEBUG_DISPATCHER_IDX);
    zpn_debug_catch_defaults_set(ZPN_DEBUG_LEARN_IDX);
    zpn_debug_catch_defaults_set(ZPN_DEBUG_APPLICATION_SERVER_IDX);
    zpn_debug_catch_defaults_set(ZPN_DEBUG_ASSISTANT_IDX);
    zpn_debug_catch_defaults_set(ZPN_DEBUG_BROKER_ASSISTANT_IDX);
    zpn_debug_catch_defaults_set(ZPN_DEBUG_SIGNING_CERT_IDX);
    zpn_debug_catch_defaults_set(ZPN_DEBUG_ISSUEDCERT_IDX);
    zpn_debug_catch_defaults_set(ZPN_DEBUG_MCONN_IDX);
    zpn_debug_catch_defaults_set(ZPN_DEBUG_CLIENT_TABLE_IDX);
    zpn_debug_catch_defaults_set(ZPN_DEBUG_IDP_IDX);
    zpn_debug_catch_defaults_set(ZPN_DEBUG_TIMER_IDX);
    zpn_debug_catch_defaults_set(ZPN_DEBUG_CLIENT_TIMER_IDX);
    zpn_debug_catch_defaults_set(ZPN_DEBUG_AUTH_IDX);
    zpn_debug_catch_defaults_set(ZPN_DEBUG_APPLICATION_GROUP_IDX);
    zpn_debug_catch_defaults_set(ZPN_DEBUG_HEALTH_REPORT_IDX);
    zpn_debug_catch_defaults_set(ZPN_DEBUG_LOG_IDX);
    zpn_debug_catch_defaults_set(ZPN_DEBUG_MTUNNEL_IDX);
    zpn_debug_catch_defaults_set(ZPN_DEBUG_BROKER_LOAD_IDX);
    zpn_debug_catch_defaults_set(ZPN_DEBUG_CONFIG_IDX);
    zpn_debug_catch_defaults_set(ZPN_DEBUG_BROKER_DNS_IDX);
    zpn_debug_catch_defaults_set(ZPN_DEBUG_FLOW_CONTROL_IDX);
    zpn_debug_catch_defaults_set(ZPN_DEBUG_BALANCE_IDX);
    zpn_debug_catch_defaults_set(ZPN_DEBUG_SIEM_IDX);
    zpn_debug_catch_defaults_set(ZPN_DEBUG_SIEM_ALL_IDX);
    zpn_debug_catch_defaults_set(ZPN_DEBUG_EXPORTER_IDX);
    zpn_debug_catch_defaults_set(ZPN_DEBUG_SAML_SIG_FAIL_IDX);
    zpn_debug_catch_defaults_set(ZPN_DEBUG_PRIVATE_BROKER_IDX);
    zpn_debug_catch_defaults_set(ZPN_DEBUG_PRIVATE_BROKER_LOAD_IDX);
    zpn_debug_catch_defaults_set(ZPN_DEBUG_STARTUP_IDX);
    zpn_debug_catch_defaults_set(ZPN_DEBUG_ZPE_IDX);
    zpn_debug_catch_defaults_set(ZPN_DEBUG_MTN_IDX);
    zpn_debug_catch_defaults_set(ZPN_DEBUG_PBC_IDX);
    zpn_debug_catch_defaults_set(ZPN_DEBUG_PATH_CACHE_IDX);
    zpn_debug_catch_defaults_set(ZPN_DEBUG_MACHINE_IDX);
    zpn_debug_catch_defaults_set(ZPN_DEBUG_COR_IDX);
    zpn_debug_catch_defaults_set(ZPN_DEBUG_MEM_IDX);
    zpn_debug_catch_defaults_set(ZPN_DEBUG_C2C_IDX);
    zpn_debug_catch_defaults_set(ZPN_DEBUG_CBI_IDX);
    zpn_debug_catch_defaults_set(ZPN_DEBUG_PCAP_IDX);
    zpn_debug_catch_defaults_set(ZPN_DEBUG_RATE_LIMIT_IDX);
    zpn_debug_catch_defaults_set(ZPN_DEBUG_IPARS_IDX);
    zpn_debug_catch_defaults_set(ZPN_DEBUG_USER_RISK_IDX);
    zpn_debug_catch_defaults_set(ZPN_DEBUG_APPLICATION_PERF_IDX);
    zpn_debug_catch_defaults_set(ZPN_DEBUG_NEG_PATH_CACHE_IDX);
    zpn_debug_catch_defaults_set(ZPN_DEBUG_VER_CONTROL_IDX);
    zpn_debug_catch_defaults_set(ZPN_DEBUG_WORKLOAD_TAG_GRP_IDX);
    zpn_debug_catch_defaults_set(ZPN_DEBUG_BROKER_C2C_FQDN_CHECK_IDX);
    zpn_debug_catch_defaults_set(ZPN_DEBUG_IDP_VERBOSE_IDX);
    zpn_debug_catch_defaults_set(ZPN_DEBUG_PBROKER_STATS_IDX);
    zpn_debug_catch_defaults_set(ZPN_DEBUG_MCONN_ICMP_IDX);
    zpn_debug_catch_defaults_set(ZPN_DEBUG_MCONN_LATENCY_IDX);
    zpn_debug_catch_defaults_set(ZPN_DEBUG_MCONN_WINDOW_IDX);
    return zpn_rpc_init();
}

char *zpn_error_description_string(enum zc_error_description error)
{
    char *p;

    switch (error)
    {
    case zpn_error_saml_assertion_expired:
        p = BRK_MT_SETUP_FAIL_SAML_EXPIRED;
        break;
    case zpn_error_reauth_expired:
        p = BRK_MT_SETUP_FAIL_LAST_AUTH_EXPIRED;
        break;
    case zpn_error_unknown:
    default:
        p = BRK_ERR_UNKNOWN;
        break;
    }

    return p;
}

/* Check if capability string is valid
 * values needs to be mtn, aca, prior_info, app_domains,
 * no_ip_download, no_domain_download or c2c
 *
 * @capability_str (i) : capability provided by client
 *
 * return ZPN_RESULT_NO_ERROR : valid
 *        ZPN_RESULT_ERR : invalid
 */
int zpn_client_check_capability_validity(int *capabilities, const char *capability_str)
{
    if (!capabilities || !capability_str) {
        return ZPN_RESULT_ERR;
    }

    const size_t capability_str_len = strlen(capability_str);
    for (int capability = zpn_client_capability_begin; capability < zpn_client_capability_end; capability++) {
        if (strncmp(capability_str, zpn_client_capability_to_string(capability), capability_str_len) == 0) {
            if (*capabilities & BIT_FLAG_U32(capability)) {
                ZPN_LOG(AL_DEBUG, "Duplicate : %s, already provided this capability", capability_str);
                return ZPN_RESULT_ERR;
            }

            *capabilities |= BIT_FLAG_U32(capability);
            return ZPN_RESULT_NO_ERROR;
        }
    }

    ZPN_LOG(AL_NOTICE, "Invalid capability : %s, allowed values are %s", capability_str, ZPN_CLIENT_CAPABILITY_ALL_STR);
    return ZPN_RESULT_ERR;
}

char *
zpn_tx_path_decision_get_str(uint64_t     flags,
                             char*        buffer,
                             int          buffer_len)
{
    size_t     iter;
    char*   start;
    char*   end;
    size_t     mapper_tbl_len;
    const char *zpn_tx_path_decision_str[] = {
            "AH",          // ZPN_TX_PATH_DECISION_AST_HEALTH_TX
            "ANH",         // ZPN_TX_PATH_DECISION_AST_NO_HEALTH_TX
            "AD",          // ZPN_TX_PATH_DECISION_AST_DSP_DECISION
            "ADO",         // ZPN_TX_PATH_DECISION_AST_DSP_DECISION_OVERRIDDEN
            "AS",          // ZPN_TX_PATH_DECISION_AST_STICKY_CACHE
            "AN",          // ZPN_TX_PATH_DECISION_AST_NEW_SEARCH
            "APD",         // ZPN_TX_PATH_DECISION_AST_PREFER_DISPATCHER
            "APL",         // ZPN_TX_PATH_DECISION_AST_PREFER_LOCAL
            "ADBR",     // ZPN_TX_PATH_DECISION_AST_DATABROKER_RESILIENCE
            "AFIXME2",     // ZPN_TX_PATH_DECISION_AST_FOR_FUTURE_GROWTH2
            "AFIXME3",     // ZPN_TX_PATH_DECISION_AST_FOR_FUTURE_GROWTH3
        "AFIXME4",     // ZPN_TX_PATH_DECISION_AST_FOR_FUTURE_GROWTH4
            "AFIXME5",     // ZPN_TX_PATH_DECISION_AST_FOR_FUTURE_GROWTH5
            "AFIXME6",     // ZPN_TX_PATH_DECISION_AST_FOR_FUTURE_GROWTH6
            "AFIXME7",     // ZPN_TX_PATH_DECISION_AST_FOR_FUTURE_GROWTH7
            "AFIXME8",     // ZPN_TX_PATH_DECISION_AST_FOR_FUTURE_GROWTH8
            "BH",          // ZPN_TX_PATH_DECISION_BRK_CACHE_HIT
            "BM",          // ZPN_TX_PATH_DECISION_BRK_CACHE_MISS
            "BA",          // ZPN_TX_PATH_DECISION_BRK_TX_TO_AST
            "BAF",         // ZPN_TX_PATH_DECISION_BRK_TX_TO_AST_FAIL
            "BD",          // ZPN_TX_PATH_DECISION_BRK_TX_TO_DSP
            "BDS",         // ZPN_TX_PATH_DECISION_BRK_TX_TO_DSP_FAIL_SEND_FAILED
            "BDR",         // ZPN_TX_PATH_DECISION_BRK_TX_TO_DSP_FAIL_TOO_MANY_RETRY
            "BDD",         // ZPN_TX_PATH_DECISION_BRK_TX_TO_DSP_IN_DIFF_DC
            "BNH",         // ZPN_TX_PATH_DECISION_BRK_NEG_CACHE_HIT
            "BDTR",        // ZPN_TX_PATH_DECISION_BRK_TX_TO_DSP_TIMEOUT_RETRY
     [50] = "DDE",         // ZPN_TX_PATH_DECISION_DSP_EXTRANET_APP_ROUTE
            "DCNE",        // ZPN_TX_PATH_DECISION_DSP_EXTRANET_ROUTE_CACHE_HIT
            "DCE",         // ZPN_TX_PATH_DECISION_DSP_EXTRANET_ROUTE_NO_CACHE
            "DCL",         // ZPN_TX_PATH_DECISION_DSP_LOAD_BALANCE_CACHE_SESSION
            "DDLA",        // ZPN_TX_PATH_DECISION_DSP_LOAD_BALANCE_ACTIVE
            "DDLP",        // ZPN_TX_PATH_DECISION_DSP_LOAD_BALANCE_PASSIVE
            "DDC2CBRM",     // ZPN_TX_PATH_DECISION_DSP_DISPATCH_C2C_LOCAL_BYPASS_REGEX_MATCH
            "DDC2C",       // ZPN_TX_PATH_DECISION_DSP_DISPATCH_C2C
            "DDH",         // ZPN_TX_PATH_DECISION_DSP_DISPATCH_HEALTH
            "DDNH",        // ZPN_TX_PATH_DECISION_DSP_DISPATCH_NO_HEALTH
            "DCN",         // ZPN_TX_PATH_DECISION_DSP_CACHE_NONE
            "DCAP",        // ZPN_TX_PATH_DECISION_DSP_CACHE_AST_PREF
            "DCS",         // ZPN_TX_PATH_DECISION_DSP_CACHE_SESSION

    };

    if (!buffer) {
        return "";
    }

    if (0 == flags) {
        return "";
    }

    buffer[0] = '\0';
    start = buffer;
    end = buffer + buffer_len;
    mapper_tbl_len = sizeof(zpn_tx_path_decision_str)/sizeof(zpn_tx_path_decision_str[0]);
    for (iter = 0; ((iter < (sizeof(flags) * 8)) && (iter < mapper_tbl_len)); iter++) {
        if (0 == (flags & (1ull << iter))) {
            continue;
        }

        if (start != buffer) {
            start += sxprintf(start, end, "|");
        }
        start += sxprintf(start, end, "%s", zpn_tx_path_decision_str[iter]);
    }

    return buffer;
}

static const char *waf_insp_status_str[] = {
    [zpn_trans_waf_no_policy_or_insp_disabled]      = "no_policy_or_insp_disabled",    // WAF inspection no policy or inspection disabled for app/port in segment
    [zpn_trans_waf_disabled_insp_policy_for_app]    = "disabled_insp_policy_for_app",  // WAF disabled inspection policy for application
    [zpn_trans_waf_feature_disabled]                = "feature_disabled",              // WAF inspection application feature disabled
    [zpn_trans_waf_no_domain_match]                 = "no_domain_match",               // WAF inspection application no domain match

    [zpn_trans_waf_internal_err]                    = "waf_internal_err",              // WAF inspection application fail internal error
    [zpn_trans_waf_resource_unavail]                = "waf_resource_unavail",          // WAF inspection application resource unavailable
    [zpn_trans_waf_ssl_to_server_fail]              = "waf_ssl_to_server_fail",        // WAF inspection application SSL connection to server failed
    [zpn_trans_waf_ssl_ctx_cryptosvc_error]         = "ssl_ctx_cryptosvc_error",       // WAF inspection SSL ctx error or cryptosvc error
    [zpn_trans_waf_ssl_ctx_certgen_error]           = "ssl_ctx_certgen_svc_error",     // WAF inspection SSL ctx error due to cert-gen service failure
    [zpn_trans_waf_resource_arrival_timeout]        = "waf_resource_pending",          // WAF resources build up did not complete in time
    [zpn_trans_waf_status_unassigned]               = "waf_status_unassigned",         // WAF inspection uncovered use case

    [zpn_trans_waf_http]                            = "waf_http",                      // WAF inspection application http
    [zpn_trans_waf_double_enc_http]                 = "waf_double_enc_http",           // WAF inspection application http double encryption
    [zpn_trans_waf_https]                           = "waf_https",                     // WAF inspection application https
    [zpn_trans_waf_double_enc_https]                = "waf_double_enc_https",          // WAF inspection application https double encryption

    [zpn_trans_waf_krb]                             = "insp_krb",                      // Inspection application krb
    [zpn_trans_waf_double_enc_krb]                  = "insp_double_enc_krb",           // Inspection application krb double encryption
    [zpn_trans_waf_ldap]                            = "insp_ldap",                     // Inspection application ldap
    [zpn_trans_waf_double_enc_ldap]                 = "insp_double_enc_ldap",          // Inspection application ldap double encryption
    [zpn_trans_waf_smb]                             = "insp_smb",                      // Inspection application smb
    [zpn_trans_waf_double_enc_smb]                  = "insp_double_enc_smb",           // Inspection application smb double encryption

    [zpn_trans_waf_auto]                            = "insp_auto",                     // Inspection application auto inspection of traffic
    [zpn_trans_waf_double_enc_auto]                 = "insp_double_enc_auto",          // Inspection application auto inspection double encryption
    [zpn_trans_waf_auto_tls]                        = "insp_auto_tls",                 // Inspection application TLS traffic
    [zpn_trans_waf_double_enc_auto_tls]             = "insp_double_enc_auto_tls",      // Inspection application TLS traffic double encryption

    [zpn_trans_waf_ptag]                            = "insp_ptag",                     // Inspection application ptag of traffic
    [zpn_trans_waf_double_enc_ptag]                 = "insp_double_enc_ptag",          // Inspection application ptag double encryption
};
const static size_t zpn_trans_waf_max_status_code = sizeof(waf_insp_status_str) / sizeof(char *);

/* ******************************************************* */
/* do not set this to non-zero for for production
 * it is used for scope unit testing only
 */
int unit_test_scope = 0;
void unit_test_scope_init() { unit_test_scope = 1; }
int unit_test_gloabl = 0;
int dta_unit_test = 0;
int dta_test_skip_default_rule = 0;
/*********************************************************/
void unit_test_init(){unit_test_gloabl = 1;}
int is_unit_test(){return unit_test_gloabl;}
int using_scope_test() { return unit_test_scope;}
void dta_test_init(){dta_unit_test = 1;}
int is_dta_test(){return dta_unit_test;}
void dta_skip_default_rule_init(){dta_test_skip_default_rule = 1;};
int is_dta_skip_default_rule() { return dta_test_skip_default_rule;};
int64_t get_customer_gid_for_unit_test(int64_t scope_gid)
{
    return scope_gid;
}
int64_t get_scope_gid_for_unit_test(int64_t customer_gid)
{
    return customer_gid;
}

int64_t get_customer_from_scope(int64_t scope_gid)
{
    if (using_scope_test()) {
        return get_customer_gid_for_unit_test(scope_gid);
    } else {
        return ZPATH_GID_GET_CUSTOMER_GID(scope_gid);
    }
}

int is_scope_default(int64_t scope_gid) {
    return get_customer_from_scope(scope_gid) == scope_gid;
}



void zpn_tx_insp_status_get_str(uint8_t insp_val, uint64_t ssl_err, char *buffer)
{
    if (!buffer) {
        ZPN_LOG(AL_ERROR, "Input buffer for inspection error description is NULL");
        return;
    }
    if (zpn_trans_waf_ssl_to_server_fail == insp_val) {
        char *ssl_err_bgn_txt = "Connect  ";
        snprintf(buffer, ZPN_TX_INSP_STATUS_STR_LEN, "%s", ssl_err_bgn_txt);
        /* err-code, str-ptr, str-len-avail */
        ERR_error_string_n(ssl_err, buffer + sizeof(ssl_err_bgn_txt),
                           (ZPN_TX_INSP_STATUS_STR_LEN - (sizeof(ssl_err_bgn_txt) + 1)));
    } else if (zpn_trans_waf_max_status_code <= insp_val) {
        ZPN_LOG(AL_ERROR, "Invalid inspection code value - %d", insp_val);
        snprintf(buffer, ZPN_TX_INSP_STATUS_STR_LEN, "Provided WAF Error code is out of range");
    } else {
        snprintf(buffer, ZPN_TX_INSP_STATUS_STR_LEN, "%s", waf_insp_status_str[insp_val]);
    }
}

int zpn_broker_is_alt_cloud_valid(char *alt_cloud)
{
    int alt_cloud_match = 0;
    int i;

    if (alt_cloud == NULL || (alt_cloud && !alt_cloud[0])) {
        return 1;
    }

    //check if there is a cloud match
    for (i = 0; i < ZPATH_CLOUD_ALT_CLOUDS_COUNT; i++) {
        if (strcmp(alt_cloud, ZPATH_CLOUD_ALT_CLOUDS(i)) == 0) {
            alt_cloud_match = 1;
            break;
        }
    }

    return alt_cloud_match;
}

void zpn_broker_app_calc_threads_count_update(int count) {
    broker_app_calc_threads_count = count;
}

int zpn_broker_app_calc_threads_count_get() {
    return broker_app_calc_threads_count;
}

int zpn_pse_app_calc_threads_count_get_and_update() {
    int num_cores;
    num_cores = sysconf(_SC_NPROCESSORS_ONLN);
    if ((-1 == num_cores) || (0 == num_cores)) {
        ZPN_LOG(AL_ERROR, "Failed to get number of available cpus - %s\n", strerror(errno));
        broker_app_calc_threads_count = PSE_DEFAULT_APP_THREADS;
    } else if (num_cores/2 > PSE_DEFAULT_APP_THREADS) {
       broker_app_calc_threads_count = PSE_DEFAULT_APP_THREADS;
    } else if (num_cores/2 < PSE_MIN_APP_THREADS) {
        broker_app_calc_threads_count = PSE_MIN_APP_THREADS;
    } else {
        broker_app_calc_threads_count = num_cores/2;
    }

    return broker_app_calc_threads_count;
}

int zpn_lib_is_app_scale_legacy_zcc(enum zpn_platform_type platform) {
    return (platform == zpn_platform_type_windows || platform == zpn_platform_type_mac);
}

int zpn_lib_is_app_scale_zcc_clients(enum zpn_client_type client_type) {
    return (client_type == zpn_client_type_zapp || client_type == zpn_client_type_zapp_partner ||
            client_type == zpn_client_type_machine_tunnel);
}
int set_debug_flag_by_name(const char *debug_flag_name, int8_t val) {
    for (uint16_t count = 0; zpn_debug_names[count]; count++) {
        if (!strncmp(debug_flag_name, zpn_debug_names[count], strlen(zpn_debug_names[count]))) {
            if (val) {
                zpn_debug_set(count);
            } else {
                zpn_debug_reset(count);
            }
            return zpn_debug_get(count);
        }
    }

    return -1;
}

/*
 * Generates random number within given range.
 *
 * Lightweight and multithreading safe, but not for cryptographically secure usecase.
 */
uint64_t zpn_xorshift64_rand(uint64_t min, uint64_t max)
{
    static uint64_t seeds[ZTHREAD_MAX_THREADS] = {0};
    struct zthread_info *self_zthread_info;
    int zthread_num;
    uint64_t *state;

    self_zthread_info = zthread_self();
    zthread_num = self_zthread_info->stack.thread_num;

    if (seeds[zthread_num] == 0) {
        seeds[zthread_num] = epoch_us() + zthread_num; // Unique seed for each thread
    }

    /* XOR Shift Algorithm to generate random. */
    state = &seeds[zthread_num];
    *state ^= *state << 13;
    *state ^= *state >> 7;
    *state ^= *state << 17;

    /* Set it within range. */
    return min + (*state % (max - min + 1));
}
