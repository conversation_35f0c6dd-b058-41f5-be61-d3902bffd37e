/*
 * zpn_private_broker_load.c. Copyright (C) 2019 Zscaler, Inc. All Rights Reserved.
 *
 * Private Broker load table serves two purpose mainly
 * a. For the private broker to record its load in the DB (in that sense it is the cousin of 'broker load' table.
 * b. For the assistant to know the list of private IPs. This info is configured by the administrator.
 *
 * Data is pulled by,
 *  a. Public broker to serve to the private brokers
 *  b. Private broker to b.1) update its own state and b.2) serve the config to the connectors.
 *  c. Connectors.
 * Data is pulled only on-demand (on a wally query). This is because the data can get very large in a public broker
 * context and we don't want to hold entries of all the private broker deployments which are not even going to reach it.
 *
 * Data is written by,
 *  a. Admin (via management API)
 *  b. PBroker - only the one table row which is associated with itself.
 * Note that no one field is written by both the parties.
 *
 */
#include "argo/argo.h"
#include "argo/argo_hash.h"
#include "zpath_lib/zpath_app.h"
#include "zpath_lib/zpath_debug.h"
#include "zpath_lib/zpath_instance.h"
#include "wally/wally.h"
#include "zpn/zpn_lib.h"
#include "zpn/zpn_private_broker_load_table.h"
#include "zpn/zpn_private_broker_load_table_compiled.h"

struct customer_load_state {
    int done;
    zpath_mutex_t lock;
    struct wally_callback_queue *queue;
};

static zpath_mutex_t hash_lock;
static struct zhash_table *customer_table = NULL;
zpn_private_broker_load_callback_f *g_load_cb = NULL;

static struct wally_index_column **zpn_private_broker_load_gid_column;
static struct wally_index_column **zpn_private_broker_load_customer_gid_column;
struct argo_structure_description *zpn_private_broker_load_description = NULL;


static struct wally_table *private_broker_load_tables[ZPATH_MAX_SHARDS] = {0};

static int
zpn_private_broker_load_dump_by_pbroker_gid(struct zpath_debug_state*   request_state,
                                            const char**                query_values,
                                            int                         query_value_count,
                                            void*                       cookie);
static int
zpn_private_broker_load_dump_by_customer_gid(struct zpath_debug_state*   request_state,
                                             const char**                query_values,
                                             int                         query_value_count,
                                             void*                       cookie);

static int
zpn_private_broker_load_row_callback(void*                       cookie,
                                     struct wally_registrant*    registrant,
                                     struct wally_table*         table,
                                     struct argo_object*         previous_row,
                                     struct argo_object*         row,
                                     int64_t                     request_id)
{
    struct zpn_private_broker_load *load = row->base_structure_void;
    if (zpn_debug_get(ZPN_DEBUG_PRIVATE_BROKER_LOAD_IDX)) {
        char dump[8000];
        if (argo_object_dump(row, dump, sizeof(dump), NULL, 0) == ARGO_RESULT_NO_ERROR) {
            ZPN_DEBUG_PRIVATE_BROKER_LOAD("Row callback: %s", dump);
        }
    }

    /* zpn_pbroker_balance_update_load */
    if (g_load_cb) g_load_cb(load->gid, load->deleted, load->publish, load->publish_count,
                             load->cpu_util, load->mem_util, load->clients, load->modified_time);
    return ZPN_RESULT_NO_ERROR;
}


static void
zpn_private_broker_load_table_post_init()
{
    int result;

    result = zpath_debug_add_read_command("Dump table entry of a private broker given its gid.",
                                     "/zpn/cfg/private_broker_load/dump_by_pbroker_gid",
                                     zpn_private_broker_load_dump_by_pbroker_gid,
                                     NULL,
                                     "pbroker_gid", "GID of the Pbroker",
                                     NULL);
    if (result) {
        ZPN_LOG(AL_ERROR, "couldn't add /zpn/cfg/private_broker_load/dump_by_pbroker_gid");
    }

    result = zpath_debug_add_read_command("Dump table entry of a private broker given the customer gid.",
                                     "/zpn/cfg/private_broker_load/dump_by_customer_gid",
                                     zpn_private_broker_load_dump_by_customer_gid,
                                     NULL,
                                     "customer_gid", "GID of the customer",
                                     NULL);
    if (result) {
        ZPN_LOG(AL_ERROR, "couldn't add /zpn/cfg/private_broker_load/dump_by_customer_gid");
    }
}


/*
 * Get the load table configuration given its pbroker gid
 *
 * Caller, remember to free the memory after the usage, till then wally holds a reference for you.
 */
int
zpn_private_broker_load_get_by_pbroker_gid(int64_t                            pbroker_gid,
                                           struct zpn_private_broker_load**   pbroker_load)
{
    int                         res;
    size_t                      row_count;
    struct wally_index_column*  column;
    int                         register_on_miss;

    int shard_index = ZPATH_SHARD_FROM_GID(pbroker_gid);
    column = zpn_private_broker_load_gid_column[shard_index];

    row_count = 1;
    res = wally_table_get_rows_fast(column, &pbroker_gid, sizeof(pbroker_gid), (void **)pbroker_load, &row_count,
                                    register_on_miss = 0, NULL, NULL, 0);
    return res;
}


/*
 * Dump the table entry corresponding to a given private broker.
 * (eg) curl '127.0.0.1:8001/zpn/cfg/private_broker_load/dump_by_pbroker_gid?pbroker_gid=1'
 *
 * Expect to get ZPATH_RESULT_ASYNCHRONOUS, if you are requesting the entry for the first time since the process is UP.
 * If this happen, please retry in a second, and you should see the entry.
 */
static int
zpn_private_broker_load_dump_by_pbroker_gid(struct zpath_debug_state*   request_state,
                                            const char**                query_values,
                                            int                         query_value_count,
                                            void*                       cookie)
{
    struct zpn_private_broker_load*     pbroker_load;
    uint64_t                            pbroker_gid;
    int                                 ret;
    char                                jsonout[10000];

    if (!query_values[0]) {
        ZDP("Require 'pbroker_gid=gid'\n");
        return ZPATH_RESULT_NO_ERROR;
    }

    pbroker_gid = strtoull(query_values[0], NULL, 0);
    ret = zpn_private_broker_load_get_by_pbroker_gid(pbroker_gid, &pbroker_load);
    if (ZPATH_RESULT_NO_ERROR != ret) {
        ZDP("couldn't find load table by pbroker_gid, wally returned (%s)\n", zpath_result_string(ret));
        return ZPATH_RESULT_NO_ERROR;
    }

    if (ARGO_RESULT_NO_ERROR == argo_structure_dump(zpn_private_broker_load_description,
                                                    pbroker_load, jsonout, sizeof(jsonout), NULL, 1)) {
        ZDP("%s\n", jsonout);
    }

    return ZPATH_RESULT_NO_ERROR;
}


/*
 * Get the load table configuration given its customer gid
 *
 * Caller, remember to free the memory after the usage, till then wally holds a reference for you.
 */
int
zpn_private_broker_load_get_by_customer_gid(int64_t                            customer_gid,
                                            struct zpn_private_broker_load**   pbroker_load,
                                            const size_t*                      pbroker_load_size,
                                            void*                              callback_cookie,
                                            int64_t                            callback_id,
                                            wally_response_callback_f          callback_f)
{
    int                         res;
    struct wally_index_column*  column;
    int                         register_on_miss;

    int shard_index = ZPATH_SHARD_FROM_GID(customer_gid);
    column = zpn_private_broker_load_customer_gid_column[shard_index];

    res = wally_table_get_rows_fast(column, &customer_gid, sizeof(customer_gid), (void **)pbroker_load,
                                    (size_t *)pbroker_load_size, register_on_miss = 1, NULL, NULL, 0);
    return res;
}


/*
 * Dump the table entry corresponding to a given private broker.
 * (eg) curl '127.0.0.1:8001/zpn/cfg/private_broker_load/dump_by_pbroker_gid?pbroker_gid=1'
 *
 * Expect to get ZPATH_RESULT_ASYNCHRONOUS, if you are requesting the entry for the first time since the process is UP.
 * If this happen, please retry in a second, and you should see the entry.
 */
static int
zpn_private_broker_load_dump_by_customer_gid(struct zpath_debug_state*   request_state,
                                             const char**                query_values,
                                             int                         query_value_count,
                                             void*                       cookie)
{
    struct zpn_private_broker_load*     pbroker_load[ZPN_PRIVATE_BROKER_LOAD_TABLE_MAX_ENTRIES_PER_CUSTOMER];
    uint64_t                            customer_gid;
    int                                 ret;
    char                                jsonout[10000];
    size_t                              number_of_entries;
    size_t                              iter_entries;


    if (!query_values[0]) {
        ZDP("Require 'customer_gid=gid'\n");
        return ZPATH_RESULT_NO_ERROR;
    }

    customer_gid = strtoull(query_values[0], NULL, 0);
    number_of_entries = ZPN_PRIVATE_BROKER_LOAD_TABLE_MAX_ENTRIES_PER_CUSTOMER;
    ret = zpn_private_broker_load_get_by_customer_gid(customer_gid, pbroker_load, &number_of_entries, NULL, 0, NULL);
    if (ZPATH_RESULT_NO_ERROR != ret) {
        ZDP("couldn't find load table by customer_gid, wally returned (%s)\n", zpath_result_string(ret));
        return ZPATH_RESULT_NO_ERROR;
    }

    for (iter_entries = 0; iter_entries < number_of_entries; iter_entries++) {
        if (ARGO_RESULT_NO_ERROR == argo_structure_dump(zpn_private_broker_load_description,
                                                        pbroker_load[iter_entries], jsonout, sizeof(jsonout), NULL, 1)) {
            ZDP("%s\n", jsonout);
        }
    }
    if (number_of_entries) {
        ZDP("%zu matching entries in zpn_private_load_table\n", number_of_entries);
    }

    return ZPATH_RESULT_NO_ERROR;
}


int
zpn_private_broker_load_write(int64_t gid,
                              uint16_t cpu_util,
                              uint16_t mem_util,
                              uint32_t active_mtun,
                              uint32_t clients,
                              uint32_t bytes_xfer,
                              char **publish,
                              size_t publish_count)
{
    struct zpn_private_broker_load load;
    struct argo_object *obj;
    int res = ZPN_RESULT_NO_ERROR;
    int shard_id = ZPATH_SHARD_FROM_GID(gid);

    if (!gid) return ZPN_RESULT_BAD_ARGUMENT;
    if (!shard_id) return ZPN_RESULT_BAD_ARGUMENT;
    if (!private_broker_load_tables[shard_id]) {
        ZPATH_LOG(AL_ERROR, "Bad shard %d from gid %ld", shard_id, (long) gid);
        return ZPN_RESULT_ERR;
    }

    memset(&load, 0, sizeof(load));

    load.customer_gid = ZPATH_GID_GET_CUSTOMER_GID(gid);
    load.gid = gid;
    load.cpu_util = cpu_util;
    load.mem_util = mem_util;
    load.active_mtun = active_mtun;
    load.clients = clients;
    load.bytes_xfer = bytes_xfer;
    load.publish = publish;
    load.publish_count = publish_count;
    load.modified_time = epoch_s();

    obj = argo_object_create(zpn_private_broker_load_description, &load);
    if (!obj) {
        ZPN_LOG(AL_ERROR, "Could not create zpn_broker_load object to write");
        return ZPN_RESULT_NO_MEMORY;
    }

    res = wally_table_write_origin_row(private_broker_load_tables[shard_id], obj);
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not write table row: %s", zpath_result_string(res));
    }
    argo_object_release(obj);

    return res;
}

void argo_zpn_private_broker_load_init()
{
    if(!argo_register_global_structure(ZPN_PRIVATE_BROKER_LOAD_HELPER))
        ZPN_LOG(AL_ERROR, "Could not register zpn_private_broker_load argo object");

}

int argo_register_zpn_private_broker_load_desc() {
    zpn_private_broker_load_description = argo_register_global_structure(ZPN_PRIVATE_BROKER_LOAD_HELPER);
    if (!zpn_private_broker_load_description) return ZPN_RESULT_ERR;
    return ZPN_RESULT_NO_ERROR;
}

int zpn_private_broker_load_init(struct wally *lone_wally, int64_t system_gid, int fully_load, int register_with_zpath_table, zpn_private_broker_load_callback_f *load_cb)
{
    return zpn_private_broker_load_init_internal(lone_wally, system_gid, fully_load, register_with_zpath_table, load_cb, 0);
}

int zpn_private_broker_load_init_internal(struct wally *lone_wally,
                                          int64_t system_gid,
                                          int fully_load,
                                          int register_with_zpath_table,
                                          zpn_private_broker_load_callback_f *load_cb,
                                          int install_cb_for_lone_wally)
{
    int res;
    int64_t customer_gid;

    customer_table = zhash_table_alloc(&zpn_allocator);
    hash_lock = ZPATH_MUTEX_INIT;

    customer_gid = ZPATH_GID_GET_CUSTOMER_GID(system_gid);

    if (!zpn_private_broker_load_description) {
        argo_register_zpn_private_broker_load_desc();
    }

    if (lone_wally) {
        struct wally_table *table;
        int shard_index = ZPATH_SHARD_FROM_GID(system_gid);

        if (install_cb_for_lone_wally) g_load_cb = load_cb;

        zpn_private_broker_load_customer_gid_column = ZPN_CALLOC(ZPATH_MAX_SHARDS * sizeof(*zpn_private_broker_load_customer_gid_column));
        zpn_private_broker_load_gid_column = ZPN_CALLOC(ZPATH_MAX_SHARDS * sizeof(*zpn_private_broker_load_gid_column));

        if (fully_load) {
            res = zpath_app_fully_loaded_customer_table(&table,
                                                        customer_gid,
                                                        lone_wally,
                                                        zpn_private_broker_load_description,
                                                        zpn_private_broker_load_row_callback,
                                                        NULL,
                                                        NULL,
                                                        register_with_zpath_table);
            if (res) {
                ZPN_LOG(AL_ERROR, "Could not fully load zpn_private_broker_load table on single tenant wally");
                return ZPN_RESULT_ERR;
            }
        } else {
            /* NOT WRITABLE!!! */
            table = wally_table_create(lone_wally,
                                       0,
                                       zpn_private_broker_load_description,
                                       zpn_private_broker_load_row_callback,
                                       NULL,
                                       1,
                                       0,
                                       NULL);
            if (!table) {
                ZPN_LOG(AL_ERROR, "Could not get zpn_private_broker_load table on single tenant wally");
                return ZPN_RESULT_ERR;
            }
        }

        zpn_private_broker_load_customer_gid_column[shard_index] = wally_table_get_index(table, "customer_gid");
        if (!zpn_private_broker_load_customer_gid_column[shard_index]) {
            ZPN_LOG(AL_ERROR, "Could not get column");
            return ZPN_RESULT_ERR;
        }
        zpn_private_broker_load_gid_column[shard_index] = wally_table_get_index(table, "gid");
        if (!zpn_private_broker_load_gid_column[shard_index]) {
            ZPN_LOG(AL_ERROR, "Could not get column");
            return ZPN_RESULT_ERR;
        }
        private_broker_load_tables[shard_index] = table;
    } else {
        g_load_cb = load_cb;
        /* WRITABLE!!!! */
        res = zpath_app_add_writable_sharded_table(zpn_private_broker_load_description,
                                                   zpn_private_broker_load_row_callback,
                                                   NULL,
                                                   0,
                                                   NULL);
        if (res) {
            return res;
        }

        zpn_private_broker_load_customer_gid_column = zpath_app_get_sharded_index("zpn_private_broker_load", "customer_gid");
        if (!zpn_private_broker_load_customer_gid_column) {
            return ZPN_RESULT_ERR;
        }

        zpn_private_broker_load_gid_column = zpath_app_get_sharded_index("zpn_private_broker_load", "gid");
        if (!zpn_private_broker_load_gid_column) {
            return ZPN_RESULT_ERR;
        }

        int i;

        for (i = 1; i < ZPATH_MAX_SHARDS; i++) {
            if (zpath_shard_wally[i]) {
                private_broker_load_tables[i] = wally_table_get(zpath_shard_wally[i], "zpn_private_broker_load");
                if (!private_broker_load_tables[i]) {
                    ZPATH_LOG(AL_CRITICAL, "Could not init shard %d", i);
                    return ZPATH_RESULT_ERR;
                }
            }
        }
    }

    zpn_private_broker_load_table_post_init();

    return ZPN_RESULT_NO_ERROR;
}

int zpn_private_broker_load_permanently_register_customer_gid(int64_t customer_gid,
                                                              wally_row_callback_f *callback,
                                                              void *callback_cookie)
{
    int shard_index = ZPATH_SHARD_FROM_GID(customer_gid);
    struct wally_registrant *registrant;
    char debug_str[1000];
    int res;

    if (!private_broker_load_tables[shard_index]) {
        ZPN_LOG(AL_ERROR, "No shard for %ld", (long)customer_gid);
        return ZPATH_RESULT_BAD_ARGUMENT;
    }

    snprintf(debug_str, sizeof(debug_str), "permanent_pbroker_load_%ld", (long) customer_gid);
    registrant = wally_table_create_registrant(private_broker_load_tables[shard_index],
                                               callback,
                                               callback_cookie,
                                               debug_str);
    if (!registrant) {
        ZPN_LOG(AL_ERROR, "could not create registrant for %ld", (long) customer_gid);
        return ZPATH_RESULT_ERR;
    }

    res = wally_table_register_for_row(registrant,
                                       zpn_private_broker_load_customer_gid_column[shard_index],
                                       &customer_gid,
                                       sizeof(customer_gid),
                                       0, // int64_t request_id,
                                       0, // int64_t request_sequence,
                                       0, // int request_atleast_one,
                                       0, // int just_callback,
                                       0, // int unique_registration,
                                       NULL, // wally_response_callback_f *response_callback,
                                       NULL); // void *response_callback_cookie);
    if (res) {
        if (res != ZPN_RESULT_ASYNCHRONOUS) {
            ZPN_LOG(AL_ERROR, "Could not register %ld", (long) customer_gid);
            return ZPATH_RESULT_ERR;
        }
    }
    return ZPATH_RESULT_NO_ERROR;
}

static void zpn_private_broker_load_response_callback_deferred(void *cookie1, void *cookie2)
{
    struct customer_load_state *load_state = cookie1;
    ZPATH_MUTEX_LOCK(&(load_state->lock), __FILE__, __LINE__);
    if (!load_state->done) {
        wally_callback_queue_callback(load_state->queue);
        load_state->done = 1;
    }
    ZPATH_MUTEX_UNLOCK(&(load_state->lock), __FILE__, __LINE__);
}


static int zpn_private_broker_load_response_callback(void *response_callback_cookie,
                                                     struct wally_registrant *registrant,
                                                     struct wally_table *table,
                                                     int64_t request_id,
                                                     int row_count)
{
    zevent_defer(zpn_private_broker_load_response_callback_deferred, response_callback_cookie, NULL, 0);
    return ZPATH_RESULT_NO_ERROR;
}


int zpn_private_broker_load_initialize(int64_t customer_gid,
                                       wally_response_callback_f callback_f,
                                       void *callback_cookie,
                                       int64_t callback_id)
{
    /* This code is optimized to not need locks for the common case */
    struct customer_load_state *load_state;
    int res;

    if (!customer_gid) {
        ZPN_LOG(AL_ERROR, "customer_gid = 0");
        return ZPN_RESULT_ERR;
    }

    load_state = zhash_table_lookup(customer_table, &customer_gid, sizeof(customer_gid), NULL);
    if (!load_state) {
        ZPATH_MUTEX_LOCK(&hash_lock, __FILE__, __LINE__);
        load_state = zhash_table_lookup(customer_table, &customer_gid, sizeof(customer_gid), NULL);
        if (!load_state) {
            load_state = ZPN_CALLOC(sizeof(*load_state));
            load_state->lock = ZPATH_MUTEX_INIT;
            zhash_table_store(customer_table, &customer_gid, sizeof(customer_gid), 0, load_state);
        }
        ZPATH_MUTEX_UNLOCK(&hash_lock, __FILE__, __LINE__);
    }
    if (load_state->done) return ZPATH_RESULT_NO_ERROR;

    ZPATH_MUTEX_LOCK(&(load_state->lock), __FILE__, __LINE__);
    if (load_state->done) {
        /* Might have been set while we wait for lock... */
        ZPATH_MUTEX_UNLOCK(&(load_state->lock), __FILE__, __LINE__);
        return ZPATH_RESULT_NO_ERROR;
    }
    if (!load_state->queue) {
        /* Need to register */
        /* NOTE: register_for_row can call our callback SYNCHRONOUSLY
         * if wally already has state for this connection. We are not
         * handling that in this case because it should be impossible
         * for something else to have read this state before this
         * routine, and this routine only reads it once. If this use
         * model were to change, this code would have to be
         * improved. */

        int shard_id = ZPATH_GID_GET_SHARD(customer_gid);
        load_state->queue = wally_callback_queue_create();
        res = wally_table_register_for_row(NULL,
                                           zpn_private_broker_load_customer_gid_column[shard_id],
                                           &customer_gid,
                                           sizeof(customer_gid),
                                           0, // int64_t request_id,
                                           0, // int64_t request_sequence,
                                           zpn_app_request_atleast_one, // int request_atleast_one,
                                           0, // int just_callback,
                                           0, // int unique_registration,
                                           zpn_private_broker_load_response_callback,
                                           load_state);
        /* Result is ALWAYS asynchronous, even if the callback will
         * come synchronously... Should fix that some day. */
        if (res != ZPN_RESULT_ASYNCHRONOUS) {
            ZPN_LOG(AL_ERROR, "Received response = %s", zpn_result_string(res));
        }
    }
    wally_callback_queue_add(load_state->queue, callback_f, callback_cookie, callback_id);

    ZPATH_MUTEX_UNLOCK(&(load_state->lock), __FILE__, __LINE__);

    return ZPN_RESULT_ASYNCHRONOUS;
}
