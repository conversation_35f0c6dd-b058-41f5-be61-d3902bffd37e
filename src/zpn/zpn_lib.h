/*
 * zpn_lib.h. Copyright (C) 2014 Zscaler Inc. All Rights Reserved
 */

#ifndef _ZPN_LIB_H_
#define _ZPN_LIB_H_

#include <assert.h>

#include "argo/argo_log.h"

#include "zpath_misc/zpath_misc.h"
#include "zpath_lib/zpath_lib.h"

/* Max limits for cloud, alt-cloud, instance, SNI */
/* NOTE: these are max allowed length; when creating buffers ; provision for extra '\0' */
#define ZPN_MAX_CLOUD_NAME_LEN              127
#define ZPN_MAX_ALT_CLOUD_NAME_LEN          127
#define ZPN_MAX_INSTANCE_NAME_LEN           99
#define ZPN_MAX_SNI_NAME_LEN                511
#define ZPN_MAX_DOMAIN_NAME_LEN             253         /* As per RFC 1035 */
#define ZPN_MAX_INSTANCE_FULL_NAME_LEN      255         /* Name including @@instance@ */

enum zpe_policy_eval_type {
    zpe_eval_old = 0,
    zpe_eval_new = 1,
    zpe_eval_both = 2
};

#define MAX_ATTR_SIZE     256

/* please don't modify the pool names below as changing pool names affects operational distribution of pools.*/
#define FOHH_WORKER_ZPN_SCCTL            "control"
#define FOHH_WORKER_ZPN_SCCFG             FOHH_WORKER_WALLY
#define FOHH_WORKER_ZPN_SCSCFG            FOHH_WORKER_WALLY
#define FOHH_WORKER_ZPN_SCOVD             FOHH_WORKER_WALLY
#define FOHH_WORKER_ZPN_SCUSERDB          FOHH_WORKER_WALLY
#define FOHH_WORKER_ZPN_SCSTATS           "fohh_log"
#define FOHH_WORKER_ZPN_PBCTL             "control"
#define FOHH_WORKER_ZPN_PBCFG             FOHH_WORKER_WALLY
#define FOHH_WORKER_ZPN_PBSCFG            FOHH_WORKER_WALLY
#define FOHH_WORKER_ZPN_PBOVD             FOHH_WORKER_WALLY
#define FOHH_WORKER_ZPN_PBUSERDB          FOHH_WORKER_WALLY
#define FOHH_WORKER_ZPN_PBSTATS           "fohh_log"
#define FOHH_WORKER_ZPN_ACTL              "control"
#define FOHH_WORKER_ZPN_ADATA             "data"
#define FOHH_WORKER_ZPN_ACFG              FOHH_WORKER_WALLY
#define FOHH_WORKER_ZPN_AOVD              FOHH_WORKER_WALLY
#define FOHH_WORKER_ZPN_ASTATS            "fohh_log"
#define FOHH_WORKER_ZPN_DISPATCHER_POOL   "dpool_"
#define FOHH_WORKER_ZPN_PROXY             "proxy"
#define FOHH_WORKER_ZPN_ZAPP              "data"
#define FOHH_WORKER_ZPN_ZAPP_PARTNER      "data"
#define FOHH_WORKER_ZPN_MACHINE           "data"
#define FOHH_WORKER_ZPN_SLOGGER           "fohh_log"
#define FOHH_WORKER_ZPN_EXPORTER_CLIENT   "data"
#define FOHH_WORKER_ZPN_EXPORTER_HTTP     "data"
#define FOHH_WORKER_ZPN_EDGE_CONNECTOR    "data"
#define FOHH_WORKER_ZPN_BRANCH_CONNECTOR  "data"
#define FOHH_WORKER_ZPN_BROWSER_ISOLATION "data"
#define FOHH_WORKER_ZPN_IP_ANCHOR         "data"
#define FOHH_WORKER_ZPN_PBDATA            "data"
#define FOHH_WORKER_ZPN_ITASCA_CLIENT     "data"
#define FOHH_WORKER_ZPN_ASERVER           "data"
#define FOHH_WORKER_ZPN_DATA              "data"
#define FOHH_WORKER_ZPN_DTLS              "dtls"
#define FOHH_WORKER_BROKER_TRANSIT        "data"
#define FOHH_WORKER_ZPN_IPARS             "ipars"
#define FOHH_WORKER_ZINS                  "zins"
#define FOHH_WORKER_ZPN_MC                "mcritical"


/* For maintenance work that isn't really tx/rx data. Some places put
 * this work on random worker threads */
#define FOHH_WORKER_ZPN_MAINTENANCE     "maintenance"
#define DR_RUNNING_MODE_STATUS_LEN      32

/* The TCP port for connections from client to broker. */
#define ZPN_CLIENT_BROKER_PORT 443
#define ZPN_CLIENT_BROKER_PORT_2
#define ZPN_DISPATCHER_PORT 8700
#define ZPN_DISPATCHER_PORT_TLV 8701
#define ZPN_DISPATCHER_PORT_TLV_BINARY 8702
#define ZPN_ASSISTANT_BROKER_PORT 443
#define ZPN_ASSISTANT_BROKER_PORT_PROXY_PROTOCOL 8443
#define ZPN_ASSISTANT_BROKER_PORT_OLD 2385
#define ZPN_ASSISTANT_BROKER_DATA_PORT_OLD 2386
#define ZPN_ASSISTANT_BROKER_CONFIG_PORT_OLD 2387
#define ZPN_PRIVATE_BROKER_TO_PUBLIC_BROKER_PORT 443
#define ZPN_PRIVATE_BROKER_TO_SITE_CONTROLLER_PORT 443
#define ZPN_TRANSIT_BROKER_PORT 443
#define ZPN_ASSISTANT_SITEC_PORT 443

#define ZPN_PRIVATE_BROKER_TO_PUBLIC_CTRL_BROKER_DNS_NAME       "any.pb2br"
#define ZPN_PRIVATE_BROKER_TO_PUBLIC_BROKER_DNS_NAME            "pb2br"
#define ZPN_PRIVATE_BROKER_TO_PUBLIC_LOGGING_BROKER_DNS_NAME    "pb2lbr"
#define ZPN_PRIVATE_BROKER_TO_PUBLIC_STATS_BROKER_DNS_NAME      "pb2slbr"
#define ZPN_PRIVATE_BROKER_TO_SITE_CONTROLLER_DNS_NAME          "pb2bcp"

#define ZPN_ASSISTANT_BROKER_RX_TIMEOUT_S (15)
#define ZPN_ASSISTANT_SITEC_RX_TIMEOUT_S (15)
#define ZPN_PBROKER_BROKER_RX_TIMEOUT_S (15)
#define ZPN_TRANSIT_BROKER_RX_TIMEOUT_S (15)
#define ZPN_SITEC_BROKER_RX_TIMEOUT_S (15)

/* The UDP port for connections from client to broker */
#define ZPN_CLIENT_BROKER_PORT_UDP            443
#define ZPN_CONNECTOR_BROKER_DATA_PORT_UDP    443

#define ZPN_CUSTOMER_READY_DEFER_US (50000)

/* VERY DANGEROUS: */
//#define ZPN_XXX_DISABLE_SAML_EXPIRY_XXX

//#define ZPN_DEFAULT_SAML_EXPIRE_TIME (12 * 60 * 60)  /* 12 hours */
#define ZPN_DEFAULT_SAML_EXPIRE_TIME (7 * 24 * 60 * 60)  /* one week */
//#define ZPN_DEFAULT_SAML_EXPIRE_TIME (0xFFFFFFFF)      /* Temporarily change to infinite */

#define ZPN_MTUNNEL_ID_BYTES (30)
#define ZPN_TUNNEL_ID_BYTES (15)

/* The number of threads used to calculate application policy for
 * download to clients */
#define BROKER_APP_CALC_THREADS_DEFAULT 24

/*
 * Define the number of app threads for pse
 */
#define PSE_DEFAULT_APP_THREADS 24
#define PSE_MIN_APP_THREADS 2

/* Following does not include NULL character.. */
#define ZPN_MTUNNEL_ID_BYTES_TEXT ((4 * ((ZPN_MTUNNEL_ID_BYTES + 2) / 3)) + 1)
/* The following is to make sure there's some reasonable chance that
 * an mtunnel ID is valid- that's all it is used for. This allows for
 * periodic changes in mtunnel format. */
#define ZPN_MTUNNEL_ID_BYTES_TEXT_MIN 32
#define ZPN_TUNNEL_ID_BYTES_TEXT (4 * ((ZPN_TUNNEL_ID_BYTES + 2) / 3))

#define ZPN_MAX_USER_LEN 64

#define ZPN_MAX_SEARCH_DOMAINS 100

#define ZPN_MAX_CLIENT_TYPE_LEN 256

/* 30 minute service age */
#define ZPN_MAX_SERVICE_AGE_S (30*60)

/* Threshold beyond which we will generate event logs. Note this is as
 * reported by TCP, so is network latency */
#define CONTROL_CONN_HIGH_RTT_LOG_THRESHOLD_MS 450

/* 2 minute DNS interval. */
#define ZPN_DNS_QUERY_INTERVAL_S 120

/* 4 minute tunnel log update */
#define ZPN_TUNNEL_LOG_INTERVAL_S 240
#define ZPN_TUNNEL_DOMAIN_HASH_INTERVAL_S 60
#define ZPN_TUNNEL_MONITOR_INTERVAL_S 10
#define ZPN_TUNNEL_AUTH_STATE_INTERVAL_S (24 * 60 * 60)
#define ZPN_TUNNEL_DDIL_DOWNTIME_S (10 * 60)
#define ZPN_TUNNEL_DDIL_INTERVAL_ON_IDLE_S (1 * 60)
#define ZPN_TUNNEL_DDIL_INTERVAL_ON_BUSY_S (30 * 60)
#define ZPN_TUNNEL_REDIRECT_CHECK_INTERVAL_S 60
#define ZPN_ZIA_IPSEC_TUNNEL_CHECK_INTERVAL_S 300
#define ZPN_TUNNEL_MONITOR_MALLOC_TRIM_CHECK_INTERVAL_S (60 * 60)
#define ZPN_TUNNEL_MONITOR_REDIRECT_INTERVAL_COUNT (ZPN_TUNNEL_REDIRECT_CHECK_INTERVAL_S / ZPN_TUNNEL_MONITOR_INTERVAL_S)
#define ZPN_TUNNEL_MONITOR_LOG_INTERVAL_COUNT (ZPN_TUNNEL_LOG_INTERVAL_S / ZPN_TUNNEL_MONITOR_INTERVAL_S)
#define ZPN_TUNNEL_DOMAIN_HASH_INTERVAL_COUNT (ZPN_TUNNEL_DOMAIN_HASH_INTERVAL_S / ZPN_TUNNEL_MONITOR_INTERVAL_S)
#define ZPN_TUNNEL_AUTH_STATE_INTERVAL_COUNT (ZPN_TUNNEL_AUTH_STATE_INTERVAL_S / ZPN_TUNNEL_MONITOR_INTERVAL_S)
#define ZPN_TUNNEL_DDIL_DOWNTIME_COUNT (ZPN_TUNNEL_DDIL_DOWNTIME_S / ZPN_TUNNEL_MONITOR_INTERVAL_S)
#define ZPN_TUNNEL_DDIL_INTERVAL_ON_IDLE_COUNT (ZPN_TUNNEL_DDIL_INTERVAL_ON_IDLE_S / ZPN_TUNNEL_MONITOR_INTERVAL_S)
#define ZPN_TUNNEL_DDIL_INTERVAL_ON_BUSY_COUNT (ZPN_TUNNEL_DDIL_INTERVAL_ON_BUSY_S / ZPN_TUNNEL_MONITOR_INTERVAL_S)
#define ZPN_ZIA_IPSEC_TUNNEL_INTERVAL_COUNT (ZPN_ZIA_IPSEC_TUNNEL_CHECK_INTERVAL_S / ZPN_TUNNEL_MONITOR_INTERVAL_S)
#define ZPN_TUNNEL_MONITOR_MALLOC_TRIM_HOUR_INTERVAL_COUNT (ZPN_TUNNEL_MONITOR_MALLOC_TRIM_CHECK_INTERVAL_S / ZPN_TUNNEL_MONITOR_INTERVAL_S )
#define ZPN_TUNNEL_MONITOR_MALLOC_TRIM_DAY_INTERVAL_COUNT (ZPN_TUNNEL_AUTH_STATE_INTERVAL_S / ZPN_TUNNEL_MONITOR_INTERVAL_S )

/* ZPN_MAX_BROKERS defines how many brokers (max) combined are
 * considered for load balancing. (i.e. most that can come from
 * constellations, etc) */
#define ZPN_MAX_BROKERS   15000
#define ZPN_MAX_REDIRECT_BROKERS    4    /* Should match ZPN_CLIENT_MAX_BROKERS in zpn_rpc.h */
#define ZPN_MAX_HEARTBEAT_TIMEOUT   20   /* Maximum heartbeat timeout for broker/private broker/sitec*/

/* The maximum number of application GIDs that can be recorded for a
 * single transaction */
#define ZPN_MAX_APP_GIDS 1000

#define ZPN_MTUNNEL_DISPATCH_MAX  9
#define ZPN_MTUNNEL_DISPATCH_INTERVAL_US 100000

#define MCONN_FIN_EXPIRED_ENABLE                 1
#define FIN_EXPIRE_US     (5*60*1000000)             /* 5 minuetes in us */

/*
 * UDP connection timeout. The normal tieout is 60s for UDP
 * generically. The fast UDP timeout is generally used for UDP
 * 'connections' that are very short lived, such as LDAP
 */
#define UDP_TIMEOUT_S          (60)
#define UDP_FAST_TIMEOUT_S     (2)
#define MAX_UDP_PACKET    (65536)

#define ICMP_TIMEOUT_S         (60)
#define MAX_ICMP_PACKET        (60*1024)
#define ICMP_ACCESS_TYPE_LEN   (100)
#define BYPASS_TYPE_LEN (100)
#define MAX_DOMAIN_LEN (512)
#define MAX_DOMAIN_LEN_SIZE (MAX_DOMAIN_LEN +1)
#define MAX_DOMAIN_PREFIX_LEN (256)
/*
 * Maximum size of ports array. Each port range takes two elements, so
 * this allows for 1K different port/port ranges to be specified per
 * domain
 */
#define MAX_PORTS_ARRAY (2048)

extern struct zpath_allocator zpn_allocator;
#define ZPN_MALLOC(x) zpath_malloc(&zpn_allocator, x, __LINE__, __FILE__)
#define ZPN_FREE(x) zpath_free(x, __LINE__, __FILE__)
#define ZPN_FREE_AND_NULL(x) do { zpath_free(x, __LINE__, __FILE__); x = NULL; } while(0)
#define ZPN_FREE_SLOW(x) zpath_free_slow(x, __LINE__, __FILE__)
#define ZPN_CALLOC(x) zpath_calloc(&zpn_allocator, x, __LINE__, __FILE__)
#define ZPN_STRDUP(x, y) zpath_strdup(&zpn_allocator, x, y, __LINE__, __FILE__)
#define ZPN_REALLOC(x, y) zpath_realloc(&zpn_allocator, x, y, __LINE__, __FILE__)

#define ZPN_RESULT_NO_ERROR          ZPATH_RESULT_NO_ERROR          /* AKA success */
#define ZPN_RESULT_ERR               ZPATH_RESULT_ERR               /* Generic error, when none other are appropriate */
#define ZPN_RESULT_NOT_FOUND         ZPATH_RESULT_NOT_FOUND         /* Could not find what was requested. */
#define ZPN_RESULT_NO_MEMORY         ZPATH_RESULT_NO_MEMORY         /* Could not allocate memory */
#define ZPN_RESULT_CANT_WRITE        ZPATH_RESULT_CANT_WRITE        /* Failure to write (output callback failed?) */
#define ZPN_RESULT_ERR_TOO_LARGE     ZPATH_RESULT_ERR_TOO_LARGE     /* Requested data doesn't fit in space provided */
#define ZPN_RESULT_BAD_ARGUMENT      ZPATH_RESULT_BAD_ARGUMENT      /* Asked for something wrong. */
#define ZPN_RESULT_INSUFFICIENT_DATA ZPATH_RESULT_INSUFFICIENT_DATA /* Was not provided enough data to perform operation */
#define ZPN_RESULT_NOT_IMPLEMENTED   ZPATH_RESULT_NOT_IMPLEMENTED   /* Yes, for features that are not yet implemented. */
#define ZPN_RESULT_BAD_DATA          ZPATH_RESULT_BAD_DATA          /* Tried to parse data, but format seemed wrong. */
#define ZPN_RESULT_WOULD_BLOCK       ZPATH_RESULT_WOULD_BLOCK       /* Attempting operation would result in blocking. Bad, naughty blocking. */
#define ZPN_RESULT_BAD_STATE         ZPATH_RESULT_BAD_STATE         /* Encountered bad internal state while attempting operation */
#define ZPN_RESULT_INCOMPLETE        ZPATH_RESULT_INCOMPLETE
#define ZPN_RESULT_ASYNCHRONOUS      ZPATH_RESULT_ASYNCHRONOUS      /* Asynchronous means the request will still complete, but at a later time, with a
                                                                     * callback. As opposed to would_block, which didn't do the job, nor will
                                                                     * it. */
#define ZPN_RESULT_EXCESS_DYN_FIELDS ZPATH_RESULT_EXCESS_DYN_FIELDS /* The RPC has too many dynamic fields */
#define ZPN_RESULT_NOT_READY         ZPATH_RESULT_NOT_READY         /* Requested DB is not ready. */
#define ZPN_RESULT_VALIDATION_ERR    ZPATH_RESULT_VALIDATION_ERR    /* Indicates non-critical yet major failure during validation of some state/args/params at runtime */
#define ZPN_RESULT_EXPIRED           (ZPATH_RESULT_MAX + 1)         /* Expired */
#define ZPN_RESULT_ACCESS_DENIED     (ZPATH_RESULT_MAX + 2)         /* Access Denied */
#define ZPN_RESULT_SIEM_FILTERED     (ZPATH_RESULT_MAX + 3)         /* SIEM logs filtered */
#define ZPN_RESULT_AL_AUTH_REQUIRED  (ZPATH_RESULT_MAX + 4)         /* Step up to higher auth level is required */
#define ZPN_RESULT_AL_AUTH_EXPIRED   (ZPATH_RESULT_MAX + 5)         /* Step up auth level expired, reauth */
#define ZPN_RESULT_CUR_MCONN_BLOCK_AND_NO_IMPACT_ON_SHARED_RESOURCE   (ZPATH_RESULT_MAX + 6)         /* Datapath MCONN is blocked, and this block has no impact on shared resouce  */

extern int zpn_app_request_atleast_one;

extern struct argo_log_collection *zpn_event_collection;


#define ZPN_DEBUG_CLIENT_IDX                (0)
#define ZPN_DEBUG_BROKER_IDX                (1)
#define ZPN_DEBUG_APPLICATION_IDX           (2)
#define ZPN_DEBUG_RULE_IDX                  (3)
#define ZPN_DEBUG_DISPATCHER_IDX            (4)
#define ZPN_DEBUG_LEARN_IDX                 (5)
#define ZPN_DEBUG_APPLICATION_SERVER_IDX    (6)
#define ZPN_DEBUG_ASSISTANT_IDX             (7)
#define ZPN_DEBUG_BROKER_ASSISTANT_IDX      (8)
#define ZPN_DEBUG_SIGNING_CERT_IDX          (9)
#define ZPN_DEBUG_ISSUEDCERT_IDX            (10)
#define ZPN_DEBUG_MCONN_IDX                 (11)
#define ZPN_DEBUG_CLIENT_TABLE_IDX          (12)
#define ZPN_DEBUG_IDP_IDX                   (13)
#define ZPN_DEBUG_TIMER_IDX                 (14)
#define ZPN_DEBUG_CLIENT_TIMER_IDX          (15)
#define ZPN_DEBUG_AUTH_IDX                  (16)
#define ZPN_DEBUG_APPLICATION_GROUP_IDX     (17)
#define ZPN_DEBUG_HEALTH_REPORT_IDX         (18)
#define ZPN_DEBUG_LOG_IDX                   (19)
#define ZPN_DEBUG_MTUNNEL_IDX               (20)
#define ZPN_DEBUG_BROKER_LOAD_IDX           (21)
#define ZPN_DEBUG_CONFIG_IDX                (22)
#define ZPN_DEBUG_BROKER_DNS_IDX            (23)
#define ZPN_DEBUG_FLOW_CONTROL_IDX          (24)
#define ZPN_DEBUG_BALANCE_IDX               (25)
#define ZPN_DEBUG_SIEM_IDX                  (26)
#define ZPN_DEBUG_SIEM_ALL_IDX              (27)
#define ZPN_DEBUG_EXPORTER_IDX              (28)
#define ZPN_DEBUG_SAML_SIG_FAIL_IDX         (29)
#define ZPN_DEBUG_PRIVATE_BROKER_IDX        (30)
#define ZPN_DEBUG_PRIVATE_BROKER_LOAD_IDX   (31)
#define ZPN_DEBUG_STARTUP_IDX               (32)
#define ZPN_DEBUG_ZPE_IDX                   (33)
/* MTN because MTN is associated with policy download */
#define ZPN_DEBUG_MTN_IDX                   (34)
#define ZPN_DEBUG_PBC_IDX                   (35)
#define ZPN_DEBUG_PATH_CACHE_IDX            (36)
#define ZPN_DEBUG_MACHINE_IDX               (37)
#define ZPN_DEBUG_COR_IDX                   (38)
#define ZPN_DEBUG_MEM_IDX                   (39)
#define ZPN_DEBUG_RPC_IDX                   (40)

#define ZPN_DEBUG_PCAP_IDX                  (41)

/* c2c and transit broker connections  */
#define ZPN_DEBUG_C2C_IDX                   (42)
#define ZPN_DEBUG_CBI_IDX                   (43)

#define ZPN_DEBUG_CUSTOMER_CONFIG_IDX       (44)

/* To prevent jenkins build policy engine tests from heavy logging */
#ifdef ZPE_DETAIL_DISABLE
#define ZPN_DEBUG_ZPE_DETAIL_IDX            0ULL
#else
#define ZPN_DEBUG_ZPE_DETAIL_IDX            (45)
#endif

#define ZPN_DEBUG_RATE_LIMIT_IDX            (46)
#define ZPN_DEBUG_JIT_APPROVAL_IDX          (47)
#define ZPN_DEBUG_SCOPE_IDX                 (48)

//Webprobe inspection bits
#define ZPN_DEBUG_WEBPROBE_HTTP_INSPECT_IDX (49)

#define ZPN_DEBUG_PRIV_CAPABILITIES_IDX     (50)
#define ZPN_DEBUG_PRA_CRED_MAP_IDX          (51)
#define ZPN_DEBUG_FC_MCONN_IDX              (52)
#define ZPN_DEBUG_IPARS_IDX                 (53)

#define ZPN_DEBUG_USER_RISK_IDX             (54)
#define ZPN_DEBUG_BROKER_DRAIN_CONN_IDX     (55)
#define ZPN_DEBUG_APPLICATION_PERF_IDX      (56)

// ZIA inspection
#define ZPN_DEBUG_ZINS_IDX                  (57)
// EAS - SME ZVPN
#define ZPN_DEBUG_EAS_IDX                   (58)

//SVCP
#define ZPN_DEBUG_SVCP_IDX                  (59)
#define ZPN_DEBUG_SVCP_DETAIL_IDX           (60)
#define ZPN_DEBUG_NEG_PATH_CACHE_IDX        (61)

//site controller bits
#define ZPN_DEBUG_SITE_CONTROLLER_IDX       (62)

//Version Control
#define ZPN_DEBUG_VER_CONTROL_IDX           (63)

#define ZPN_DEBUG_WORKLOAD_TAG_GRP_IDX      (64)
#define ZPN_DEBUG_BROKER_C2C_FQDN_CHECK_IDX (65)
#define ZPN_DEBUG_IDP_VERBOSE_IDX           (66)
#define ZPN_DEBUG_PBROKER_STATS_IDX         (67)
// Network Presence
#define ZPN_DEBUG_NP_IDX                    (68)
#define ZPN_DEBUG_BNC_IDX                   (69)
#define ZPN_DEBUG_ZINS_DETAIL_IDX           (70)
#define ZPN_DEBUG_PROXY_IDX                 (71)
#define ZPN_DEBUG_AAE_IDX                   (72)
#define ZPN_DEBUG_MCONN_ICMP_IDX            (73)
#define ZPN_DEBUG_NP_VERBOSE_IDX            (74)
#define ZPN_DEBUG_MCONN_LATENCY_IDX         (75)
#define ZPN_DEBUG_MCONN_WINDOW_IDX          (76)
#define ZPN_DEBUG_NUM_IDX                    77 // increment as new entries are added

/* The following is used to set up more debugging... Add to this list
 * as you add debugging bits. Make sure they stay in sync. */
#define ZPN_DEBUG_NAMES {                       \
            "client",                           \
            "broker",                           \
            "application",                      \
            "rule",                             \
            "dispatcher",                       \
            "learn",                            \
            "application_server",               \
            "assistant",                        \
            "broker_assistant",                 \
            "signing_cert",                     \
            "issuedcert",                       \
            "mconn",                            \
            "client_table",                     \
            "idp",                              \
            "timer",                            \
            "client_timer",                     \
            "auth",                             \
            "application_group",                \
            "health",                           \
            "log",                              \
            "mtunnel",                          \
            "broker_load",                      \
            "config",                           \
            "broker_dns",                       \
            "flow_control",                     \
            "balance",                          \
            "siem",                             \
            "siem_all",                         \
            "exporter",                         \
            "saml_sig_fail",                    \
            "private_broker",                   \
            "private_broker_load",              \
            "startup",                          \
            "zpe",                              \
            "mtn",                              \
            "pbc",                              \
            "path_cache",                       \
            "machine",                          \
            "cor",                              \
            "mem",                              \
            "rpc",                              \
            "pcap",                             \
            "c2c",                              \
            "cbi",                              \
            "customer_config",                  \
            "zpe_detail",                       \
            "rate_limit",                       \
            "jit_approval",                     \
            "scope",                            \
            "webprobe_http_inspect",            \
            "priv_capabilities",                \
            "pra_cred_map",                     \
            "fc_mconn",                         \
            "ipars",                            \
            "user_risk",                        \
            "broker_drain_conn",                \
            "application_perf",                 \
            "zins",                             \
            "eas",                              \
            "svcp",                             \
            "svcp_details",                     \
            "neg_path_cache",                   \
            "sitec",                            \
            "ver_control",                      \
            "workload_tag_grp",                 \
            "c2c_check_cache",                  \
            "idp_verbose",                      \
            "private_broker_stats",             \
            "np",                               \
            "bnc",                              \
            "zins_details",                     \
            "proxy",                            \
            "aae",                              \
            "mconn_icmp",                       \
            "np_verbose",                       \
            "mconn_latency",                    \
            "mconn_window",                     \
            NULL                                \
            }

extern const char *zpn_debug_names[];

#define ZPN_LOG(priority, format...) if (1) ARGO_LOG(zpn_event_collection, priority, "zpn_lib", ##format)
#define ZPN_LOG_2(priority, name, format...) if (1) ARGO_LOG(zpn_event_collection, priority, name, ##format)
#define ZPN_DEBUG_LOG(condition, format...) if (1) ARGO_DEBUG_LOG((condition), zpn_event_collection, argo_log_priority_debug, "zpn_lib", ##format)
// allws to control the name of log entry, so it can be  filtered by
#define ZPN_DEBUG_LOG2(condition, name, format...) if (1) ARGO_DEBUG_LOG((condition), zpn_event_collection, argo_log_priority_debug, name, ##format)
#define ZPN_DEBUG_CLIENT(format...) ZPN_DEBUG_LOG(zpn_debug_get(ZPN_DEBUG_CLIENT_IDX), ##format)
#define ZPN_DEBUG_BROKER(format...) ZPN_DEBUG_LOG(zpn_debug_get(ZPN_DEBUG_BROKER_IDX), ##format)
#define ZPN_DEBUG_APPLICATION(format...) ZPN_DEBUG_LOG(zpn_debug_get(ZPN_DEBUG_APPLICATION_IDX), ##format)
#define ZPN_DEBUG_RULE(format...) ZPN_DEBUG_LOG(zpn_debug_get(ZPN_DEBUG_RULE_IDX), ##format)
#define ZPN_DEBUG_DISPATCHER(format...) ZPN_DEBUG_LOG(zpn_debug_get(ZPN_DEBUG_DISPATCHER_IDX), ##format)
#define ZPN_DEBUG_LEARN(format...) ZPN_DEBUG_LOG(zpn_debug_get(ZPN_DEBUG_LEARN_IDX), ##format)
#define ZPN_DEBUG_APPLICATION_SERVER(format...) ZPN_DEBUG_LOG(zpn_debug_get(ZPN_DEBUG_APPLICATION_SERVER_IDX), ##format)
#define ZPN_DEBUG_ASSISTANT(format...) ZPN_DEBUG_LOG(zpn_debug_get(ZPN_DEBUG_ASSISTANT_IDX), ##format)
#define ZPN_DEBUG_BROKER_ASSISTANT(format...) ZPN_DEBUG_LOG(zpn_debug_get(ZPN_DEBUG_BROKER_ASSISTANT_IDX), ##format)
#define ZPN_DEBUG_SIGNING_CERT(format...) ZPN_DEBUG_LOG(zpn_debug_get(ZPN_DEBUG_SIGNING_CERT_IDX), ##format)
#define ZPN_DEBUG_ISSUEDCERT(format...) ZPN_DEBUG_LOG(zpn_debug_get(ZPN_DEBUG_ISSUEDCERT_IDX), ##format)
#define ZPN_DEBUG_MCONN(format...) ZPN_DEBUG_LOG(zpn_debug_get(ZPN_DEBUG_MCONN_IDX), ##format)
#define ZPN_DEBUG_MCONN_WINDOW(format...) ZPN_DEBUG_LOG(zpn_debug_get(ZPN_DEBUG_MCONN_WINDOW_IDX), ##format)
#define ZPN_DEBUG_MCONN_ICMP(format...) ZPN_DEBUG_LOG(zpn_debug_get(ZPN_DEBUG_MCONN_ICMP_IDX), ##format)
#define ZPN_DEBUG_MCONN_LATENCY(format...) ZPN_DEBUG_LOG(zpn_debug_get(ZPN_DEBUG_MCONN_LATENCY_IDX), ##format)
#define ZPN_DEBUG_CLIENT_TABLE(format...) ZPN_DEBUG_LOG(zpn_debug_get(ZPN_DEBUG_CLIENT_TABLE_IDX), ##format)
#define ZPN_DEBUG_IDP(format...) ZPN_DEBUG_LOG(zpn_debug_get(ZPN_DEBUG_IDP_IDX), ##format)
#define ZPN_DEBUG_TIMER(format...) ZPN_DEBUG_LOG(zpn_debug_get(ZPN_DEBUG_TIMER_IDX), ##format)
#define ZPN_DEBUG_CLIENT_TIMER(format...) ZPN_DEBUG_LOG(zpn_debug_get(ZPN_DEBUG_CLIENT_TIMER_IDX), ##format)
#define ZPN_DEBUG_AUTH(format...) ZPN_DEBUG_LOG(zpn_debug_get(ZPN_DEBUG_AUTH_IDX), ##format)
#define ZPN_DEBUG_APPLICATION_GROUP(format...) ZPN_DEBUG_LOG(zpn_debug_get(ZPN_DEBUG_APPLICATION_GROUP_IDX), ##format)
#define ZPN_DEBUG_HEALTH(format...) ZPN_DEBUG_LOG(zpn_debug_get(ZPN_DEBUG_HEALTH_REPORT_IDX), ##format)
#define ZPN_DEBUG_LOGGING(format...) ZPN_DEBUG_LOG(zpn_debug_get(ZPN_DEBUG_LOG_IDX), ##format)
#define ZPN_DEBUG_MTUNNEL(format...) ZPN_DEBUG_LOG(zpn_debug_get(ZPN_DEBUG_MTUNNEL_IDX), ##format)
#define ZPN_DEBUG_BROKER_LOAD(format...) ZPN_DEBUG_LOG(zpn_debug_get(ZPN_DEBUG_BROKER_LOAD_IDX), ##format)
#define ZPN_DEBUG_CONFIG(format...) ZPN_DEBUG_LOG(zpn_debug_get(ZPN_DEBUG_CONFIG_IDX), ##format)
#define ZPN_DEBUG_BROKER_DNS(format...) ZPN_DEBUG_LOG(zpn_debug_get(ZPN_DEBUG_BROKER_DNS_IDX), ##format)
#define ZPN_DEBUG_FLOW_CONTROL(format...) ZPN_DEBUG_LOG(zpn_debug_get(ZPN_DEBUG_BROKER_FLOW_CONTROL_IDX), ##format)
#define ZPN_DEBUG_BALANCE(format...) ZPN_DEBUG_LOG(zpn_debug_get(ZPN_DEBUG_BALANCE_IDX), ##format)
#define ZPN_DEBUG_SIEM(format...) ZPN_DEBUG_LOG(zpn_debug_get(ZPN_DEBUG_SIEM_IDX), ##format)
#define ZPN_DEBUG_SIEM_ALL(format...) ZPN_DEBUG_LOG(zpn_debug_get(ZPN_DEBUG_SIEM_ALL_IDX), ##format)
#define ZPN_DEBUG_EXPORTER(format...) ZPN_DEBUG_LOG(zpn_debug_get(ZPN_DEBUG_EXPORTER_IDX), ##format)
#define ZPN_DEBUG_SAML_SIG_FAIL(format...) ZPN_DEBUG_LOG(zpn_debug_get(ZPN_DEBUG_SAML_SIG_FAIL_IDX), ##format)
#define ZPN_DEBUG_PRIVATE_BROKER(format...) ZPN_DEBUG_LOG(zpn_debug_get(ZPN_DEBUG_PRIVATE_BROKER_IDX), ##format)
#define ZPN_DEBUG_PBROKER(format...) ZPN_DEBUG_LOG(zpn_debug_get(ZPN_DEBUG_PRIVATE_BROKER_IDX), ##format)
#define ZPN_DEBUG_PRIVATE_BROKER_LOAD(format...) ZPN_DEBUG_LOG(zpn_debug_get(ZPN_DEBUG_PRIVATE_BROKER_LOAD_IDX), ##format)
#define ZPN_DEBUG_SITEC(format...) ZPN_DEBUG_LOG(zpn_debug_get(ZPN_DEBUG_SITE_CONTROLLER_IDX), ##format)
#define ZPN_DEBUG_STARTUP(format...) ZPN_DEBUG_LOG(zpn_debug_get(ZPN_DEBUG_STARTUP_IDX), ##format)
#define ZPN_DEBUG_ZPE(format...) ZPN_DEBUG_LOG(zpn_debug_get(ZPN_DEBUG_ZPE_IDX), ##format)
#define ZPN_DEBUG_MTN(format...) ZPN_DEBUG_LOG(zpn_debug_get(ZPN_DEBUG_MTN_IDX), ##format)
#define ZPN_DEBUG_PBC(format...) ZPN_DEBUG_LOG(zpn_debug_get(ZPN_DEBUG_PBC_IDX), ##format)
#define ZPN_DEBUG_PATH_CACHE(format...) ZPN_DEBUG_LOG(zpn_debug_get(ZPN_DEBUG_PATH_CACHE_IDX), ##format)
#define ZPN_DEBUG_MACHINE(format...) ZPN_DEBUG_LOG(zpn_debug_get(ZPN_DEBUG_MACHINE_IDX), ##format)
#define ZPN_DEBUG_COR(format...) ZPN_DEBUG_LOG(zpn_debug_get(ZPN_DEBUG_COR_IDX), ##format)
#define ZPN_DEBUG_RPC(format...) ZPN_DEBUG_LOG(zpn_debug_get(ZPN_DEBUG_RPC_IDX), ##format)
#define ZPN_DEBUG_PCAP(format...) ZPN_DEBUG_LOG(zpn_debug_get(ZPN_DEBUG_PCAP_IDX), ##format)
#define ZPN_DEBUG_C2C(format...) ZPN_DEBUG_LOG(zpn_debug_get(ZPN_DEBUG_C2C_IDX), ##format)
#define ZPN_DEBUG_CBI(format...) ZPN_DEBUG_LOG(zpn_debug_get(ZPN_DEBUG_CBI_IDX), ##format)
#define ZPN_DEBUG_CUSTOMER_CONFIG(format...) ZPN_DEBUG_LOG(zpn_debug_get(ZPN_DEBUG_CUSTOMER_CONFIG_IDX), ##format)
#define ZPN_DEBUG_ZPE_DETAIL(format...) ZPN_DEBUG_LOG(zpn_debug_get(ZPN_DEBUG_ZPE_DETAIL_IDX), ##format)
#define ZPN_DEBUG_RATE_LIMIT(format...) ZPN_DEBUG_LOG(zpn_debug_get(ZPN_DEBUG_RATE_LIMIT_IDX), ##format)
#define ZPN_DEBUG_JIT_APPROVAL(format...) ZPN_DEBUG_LOG(zpn_debug_get(ZPN_DEBUG_JIT_APPROVAL_IDX), ##format)
#define ZPN_DEBUG_WEBPROBE_HTTP_INSPECT(format...) ZPN_DEBUG_LOG(zpn_debug_get(ZPN_DEBUG_WEBPROBE_HTTP_INSPECT_IDX), ##format)
#define ZPN_DEBUG_SCOPE(format...) ZPN_DEBUG_LOG(zpn_debug_get(ZPN_DEBUG_SCOPE_IDX), ##format)
#define ZPN_DEBUG_PRIV_CAPABILITIES(format...) ZPN_DEBUG_LOG(zpn_debug_get(ZPN_DEBUG_PRIV_CAPABILITIES_IDX), ##format)
#define ZPN_DEBUG_PRA_CRED_MAP(format...) ZPN_DEBUG_LOG(zpn_debug_get(ZPN_DEBUG_PRA_CRED_MAP_IDX), ##format)
#define ZPN_DEBUG_FC_MCONN(format...) ZPN_DEBUG_LOG(zpn_debug_get(ZPN_DEBUG_FC_MCONN_IDX), ##format)
#define IS_ZPN_DEBUG_IPARS()       (zpn_debug_get(ZPN_DEBUG_IPARS_IDX))

#define ZPN_DEBUG_IPARS(format...) ZPN_DEBUG_LOG(IS_ZPN_DEBUG_IPARS(), ##format)
#define ZPN_DEBUG_USER_RISK(format...) ZPN_DEBUG_LOG(zpn_debug_get(ZPN_DEBUG_USER_RISK_IDX), ##format)
#define ZPN_DEBUG_BROKER_DRAIN_CONN(format...) ZPN_DEBUG_LOG(zpn_debug_get(ZPN_DEBUG_BROKER_DRAIN_CONN_IDX), ##format)
#define ZPN_DEBUG_NEG_PATH_CACHE(format...) ZPN_DEBUG_LOG(zpn_debug_get(ZPN_DEBUG_NEG_PATH_CACHE_IDX), ##format)
#define ZPN_DEBUG_ZINS(format...) ZPN_DEBUG_LOG2(zpn_debug_get(ZPN_DEBUG_ZINS_IDX), "zins", ##format)
#define ZPN_ERROR_ZINS(format...) if (1) ARGO_DEBUG_LOG(zpn_debug_get(ZPN_DEBUG_ZINS_IDX), zpn_event_collection, argo_log_priority_error, "zins", ##format)
#define ZPN_DEBUG_ZINS_DETAILS(format...) ZPN_DEBUG_LOG2(zpn_debug_get(ZPN_DEBUG_ZINS_DETAIL_IDX), "zinsd", ##format)


#define ZPN_LOG_ZINS(priority, format...) if (1) ARGO_LOG(zpn_event_collection, priority, "zins", ##format)
#define ZPN_DEBUG_EAS(format...) ZPN_DEBUG_LOG2(zpn_debug_get(ZPN_DEBUG_EAS_IDX),"eas", ##format)
#define ZPN_ERROR_EAS(format...) if (1) ARGO_LOG(zpn_event_collection, argo_log_priority_error, "eas", ##format)

#define ZPN_DEBUG_SVCP(format...) ZPN_DEBUG_LOG(zpn_debug_get(ZPN_DEBUG_SVCP_IDX), ##format)
#define ZPN_DEBUG_SVCP_DETAIL(format...) ZPN_DEBUG_LOG(zpn_debug_get(ZPN_DEBUG_SVCP_DETAIL_IDX), ##format)
#define ZPN_DEBUG_VER_CONTROL(format...) ZPN_DEBUG_LOG(zpn_debug_get(ZPN_DEBUG_VER_CONTROL_IDX), ##format)

#define ZCLIENT_LOG(priority, format...) if (1) ARGO_LOG(zpn_event_collection, priority, "clientd", ##format)

#define ZPN_DEBUG_WORKLOAD_TAG_GRP(format...) ZPN_DEBUG_LOG(zpn_debug_get(ZPN_DEBUG_WORKLOAD_TAG_GRP_IDX), ##format)
#define ZPN_DEBUG_BROKER_C2C_FQDN_CHECK(format...) ZPN_DEBUG_LOG(zpn_debug_get(ZPN_DEBUG_BROKER_C2C_FQDN_CHECK_IDX), ##format)
#define ZPN_DEBUG_IDP_VERBOSE(format...) ZPN_DEBUG_LOG(zpn_debug_get(ZPN_DEBUG_IDP_VERBOSE_IDX), ##format)
#define ZPN_DEBUG_PBROKER_STATS(format...) ZPN_DEBUG_LOG(zpn_debug_get(ZPN_DEBUG_PBROKER_STATS_IDX), ##format)
#define IS_ZPN_DEBUG_NP()           (zpn_debug_get(ZPN_DEBUG_NP_IDX))
#define ZPN_DEBUG_NP(format...)     ZPN_DEBUG_LOG(IS_ZPN_DEBUG_NP(), ##format)
#define IS_ZPN_DEBUG_NP_VERBOSE()   (zpn_debug_get(ZPN_DEBUG_NP_VERBOSE_IDX))
#define ZPN_DEBUG_NP_VERBOSE(format...) ZPN_DEBUG_LOG(IS_ZPN_DEBUG_NP_VERBOSE(), ##format)
#define ZPN_DEBUG_BNC(format...)   ZPN_DEBUG_LOG(zpn_debug_get(ZPN_DEBUG_BNC_IDX),   ##format)
#define IS_ZPN_DEBUG_PROXY()        (zpn_debug_get(ZPN_DEBUG_PROXY_IDX))
#define ZPN_DEBUG_PROXY(format...) ZPN_DEBUG_LOG(zpn_debug_get(ZPN_DEBUG_PROXY_IDX), ##format)
#define ZPN_DEBUG_AAE(format...) ZPN_DEBUG_LOG(zpn_debug_get(ZPN_DEBUG_AAE_IDX), ##format)

#define ZPN_ASSERT(x)          assert(x)

/********************************************************************************
 * Error or response codes
 *
 * - BRK_MT_TERMINATED:                               Broker terminated the mtunnel which has received
 *                                                    termination from both client and assistant
 * - BRK_MT_TERMINATED_IDLE_TIMEOUT:                  Broker terminated the mtunnel due to idle timeout
 * - BRK_MT_SETUP_TIMEOUT:                            Broker mtunnel setup has timed out
 * - BRK_MT_SETUP_FAIL_USER_NOT_FOUND:                Broker failed to setup mtunnel due to
 *                                                    user not found
 * - BRK_MT_SETUP_FAIL_SAML_EXPIRED:                  Broker failed to setup mtunnel due to
 *                                                    SAML assertion expired
 * - BRK_MT_SETUP_FAIL_REJECTED_BY_POLICY:            Broker failed to setup mtunnel because
 *                                                    it is not allowed by the policy
 * - BRK_MT_SETUP_FAIL_REJECTED_BY_POLICY_APPROVAL    Broker failed to setup mtunnel because
 *                                                    approval is required and there is no active Just-In-Time approval
 * - BRK_MT_SETUP_FAIL_NO_POLICY_FOUND                Broker failed to setup mtunnel because
 *                                                    no policy is found
 * - BRK_MT_SETUP_FAIL_CANNOT_SEND_TO_DISPATCHER      Broker failed to setup mtunnel because
 *                                                    it cannot send broker_request to dispatcher
 * - BRK_MT_SETUP_FAIL_MEMORY                         Broker failed to setup mtunnel because
 *                                                    it was out of memory.
 * - BRK_MT_SETUP_FAIL_PROBE                          Could not perform policy because learning failed
 * - BRK_MT_SETUP_FAIL_NO_PROBE_DATA                  Could not perform policy because we don't have probe data
 * - BRK_MT_SETUP_FAIL_CANNOT_PROMOTE                 Could not promote connection to public broker
 * - BRK_MT_SETUP_FAIL_DUPLICATE_TAG_ID               Broker failed to setup mtunnel because
 *                                                    another mtunnel with the same tag id has
 *                                                    already existed
 * - BRK_MT_SETUP_FAIL_DUPLICATE_TAG_ID               Broker failed to setup mtunnel because
 *                                                    another mtunnel with the same tag id has
 *                                                    already existed
 * - BRK_MT_SETUP_FAIL_REPEATED_DISPATCH              Broker exceeded dispatch retry count
 * - INVALID_DOMAIN                                   Java dispatcher uses it for discovery timeout or if health based
 *                                                    app is missing health. Also note
 *                                                    that if any of the connectors replied saying it can't resolve
 *                                                    the DNS, then GUI will show as NO_CONNECTOR_AVAILABLE.
 * - BRK_MT_SETUP_FAIL_BIND_TO_CLIENT_LOCAL_OWNER     Broker failed to setup mtunnel because
 *                                                    it cannot bind the mtunnel to client side
 *                                                    connection
 * - BRK_MT_SETUP_FAIL_BIND_TO_AST_LOCAL_OWNER        Broker failed to setup mtunnel because
 *                                                    it cannot bind the mtunnel to assistant side
 *                                                    connection
 * - BRK_MT_SETUP_FAIL_BIND_RECV_IN_BAD_STATE         Broker received an unexpected bind request
 *                                                    from assistant
 * - BRK_MT_SETUP_FAIL_CANNOT_SEND_MT_COMPLETE        Broker failed to setup mtunnel because
 *                                                    it cannot send tunnel complete message
 * - BRK_MT_SETUP_FAIL_TO_CREATE_STREAM               Broker failed to create rdt stream
 * - BRK_MT_SETUP_FAIL_SCIM_INACTIVE                  Broker failed to setup mtunnel because
 *                                                    user is deactivated or not synced via SCIM
 * - BRK_MT_SETUP_FAIL_RATE_LIMIT_EXCEEDED            Broker rejected the mtunnel request because it exceeded
 *                                                    the set rate limit.
 * - BRK_MT_SETUP_FAIL_RATE_LIMIT_NUM_APP_EXCEEDED    Broker rejected the mtunnel request because it exceeded
 *                                                    the set rate limit for different domains.
 * - BRK_MT_SETUP_FAIL_RATE_LIMIT_LOOP_DETECTED       Broker rejected the mtunnel request because it detected
 *                                                    a potential mtunnel creation looping issue.
 * - BRK_MT_REJECT_MAX_WTAG_EXCEEDED                  Broker rejected the mtunnel request because client sent
 *                                                    workload tag groups exceeded the max allowed.
 * - BRK_MT_CLOSED_FROM_CLIENT                        Broker received tunnel end message from client
 * - BRK_MT_CLOSED_FROM_ASSISTANT                     Broker received tunnel end message from assistant
 * - BRK_MT_AUTH_SAML_FAILURE                         Broker failed to authenticate SAML assertion
 * - BRK_MT_AUTH_NO_SAML_ASSERTION_IN_MSG             Broker failed to authenticate SAML assertion because
 *                                                    there is no assertion in SAML message
 * - BRK_MT_AUTH_TWO_SAML_ASSERTION_IN_MSG            Broker failed to authenticate SAML assertion because
 *                                                    there is two assertions in SAML message
 * - BRK_MT_AUTH_SAML_ASSERTION_TOO_LARGE             Broker failed to authenticate SAML assertion because
 *                                                    the SAML message is too large
 * - BRK_MT_AUTH_SAML_DECODE_FAIL                     Broker failed to authenticate SAML assertion because
 *                                                    it failed to decode the SAML message
 * - BRK_MT_AUTH_SAML_FINGER_PRINT_FAIL               Broker failed to authenticate SAML assertion because
 *                                                    it failed to verify the assertion's fingerprint
 * - BRK_MT_AUTH_SAML_NO_USER_ID                      Broker failed to authenticate SAML assertion because
 *                                                    there is no user id in SAML message
 * - BRK_MT_AUTH_SAML_CANNOT_ADD_ATTR_TO_HEAP         Broker failed to authenticate SAML assertion because
 *                                                    running out of heap memory space
 * - BRK_MT_AUTH_SAML_CANNOT_ADD_ATTR_TO_HASH         Broker failed to authenticate SAML assertion because
 *                                                    it cannot add SAML attributes to hash
 * - BRK_MT_AUTH_ALREADY_FAILED                       Authentication is being attempted again in the same
 *                                                    tunnel after authentication already failed once.
 * - AST_MT_TERMINATED:                               Assistant terminated the mtunnel which has received
 *                                                    termination from both server and broker
 * - AST_MT_SETUP_TIMEOUT                             Assistant mtunnel setup has timed out
 * - AST_MT_SETUP_TIMEOUT_CANNOT_CONN_TO_SERVER       Assistant mtunnel setup has timed out due to failure
 *                                                    to connect to server
 * - AST_MT_SETUP_TIMEOUT_CANNOT_CONN_TO_BROKER       Assistant mtunnel setup has timed out due to failure
 *                                                    to connect to broker
 * - AST_MT_SETUP_TIMEOUT_NO_ACK_TO_BIND              Assistant mtunnel setup has timed out due to failure
 *                                                    to failure to receive bind ack from broker
 * - AST_MT_SETUP_ERR_OPEN_SERVER_CONN                Assistant cannot set up mtunnel because it cannot
 *                                                    open connection to server
 * - AST_MT_SETUP_ERR_OPEN_SERVER_TIMEOUT             Assistant cannot set up mtunnel because connect to
 *                                                    server timed out
 * - AST_MT_SETUP_ERR_OPEN_SERVER_ERROR               Assistant cannot set up mtunnel because of error
 *                                                    connecting to server
 * - AST_MT_SETUP_ERR_OPEN_SERVER_CLOSE               Assistant cannot set up mtunnel because connection
 *                                                    closed by server
 * - AST_MT_SETUP_ERR_OPEN_BROKER_CONN                Assistant cannot set up mtunnel because it cannot
 *                                                    connect to the broker
 * - AST_MT_SETUP_ERR_BROKER_BIND_FAIL                Assistant cannot set up mtunnel because it cannot
 *                                                    bind to the connection to the broker
 * - AST_MT_SETUP_ERR_DUP_MT_ID                       Assistant cannot set up mtunnel because the mtunnel
 *                                                    with the same id has already existed
 * - AST_MT_SETUP_ERR_HASH_TBL_FULL                   Assistant cannot set up mtunnel because the mtunnel
 *                                                    hash table is full
 * - AST_MT_SETUP_ERR_INIT_FOHH_MCONN                 Assistant cannot set up mtunnel because it failed
 *                                                    to initialize the broker end of the mtunnel
 * - AST_MT_SETUP_ERR_INIT_BE_MCONN                   Assistant cannot set up mtunnel because it failed
 *                                                    to initialize the server end of the mtunnel
 * - AST_MT_SETUP_ERR_CONN_PEER                       Assistant cannot set up mtunnel because it failed
 *                                                    to connect the broker end and server end of the
 *                                                    mtunnel together
 * - AST_MT_SETUP_ERR_BIND_GLOBAL_OWNER               Assistant cannot set up mtunnel because it failed
 *                                                    to bind the mtunnel to its global owner
 * - AST_MT_SETUP_ERR_BIND_TO_AST_LOCAL_OWNER         Assistant cannot set up mtunnel because it failed
 *                                                    to bind the mtunnel to its local owner
 * - AST_MT_SETUP_ERR_BIND_ACK                        Assistant cannot set up mtunnel because it received
 *                                                    bind ack with error from broker
 * - AST_MT_SETUP_ERR_BRK_HASH_TBL_FULL               Assistant cannot set up mtunnel because the broker
 *                                                    mtunnel hash table is full
 * - AST_MT_SETUP_ERR_APP_NOT_FOUND                   Assistant cannot set up mtunnel because it cannot
 *                                                    find the app
 * - MT_CLOSED_TLS_CONN_GONE                          Mtunnel is terminated because its associated fohh
 *                                                    connection is gone.
 * - MT_CLOSED_TLS_CONN_GONE_AST_CLOSED               Mtunnel is terminated because its associated fohh
 *                                                    connection is gone because of assistant closing it.
 * - MT_CLOSED_TLS_CONN_GONE_CLIENT_CLOSED            Mtunnel is terminated because its associated fohh
 *                                                    connection is gone because of client/zapp/user/exporter/.. closing
 *                                                    it.
 * - MT_CLOSED_INTERNAL_ERROR                         Mtunnel is terminated because of internal error
 * - MT_CLOSED_FRAMING_ERROR                          Mtunnel is terminated because of framing error
 * - MT_CLOSED_TERMINATED                             Mtunnel is terminated due to termination received
 *                                                    from either client or server
 * - EXPTR_FCONN_GONE                                 Exporter f_conn to broker is gone
 * - EXPTR_FCONN_AUTH_FAIL                            Exporter f_conn to broker authtentication failed
 * - EXPTR_MT_TLS_SETUP_FAIL_VERSION_MISMATCH         Exporter tls mtunnel setup failure due to version mismatch
 * - EXPTR_MT_TLS_SETUP_FAIL_NOT_TRUSTED_CA           Exporter tls mtunnel setup failure due to untrusted CA
 * - EXPTR_MT_TLS_SETUP_FAIL_CERT_CHAIN_ISSUE         Exporter tls mtunnel setup failure due to chain issue
 * - EXPTR_MT_TLS_SETUP_FAIL_PEER                     Exporter tls mtunnel setup failure due to peer error
 * - EXPTR_MT_TLS_SETUP_FAIL                          Exporter tls mtunnel setup failure
 */


/* GENERIC ERRORS */
#define ZPN_ERR_BAD_REQUEST                               "ZPN_ERR_BAD_REQUEST"
#define ZPN_ERR_INVALID_APPLICATION                       "ZPN_ERR_APPLICATION_INVALID"
#define ZPN_ERR_APPLICATION_UNAVAILABLE                   "ZPN_ERR_APPLICATION_UNAVAILABLE"
#define ZPN_ERR_NOT_FOUND                                 "ZPN_ERR_NOT_FOUND"
#define ZPN_ERR_STALE_CONNECTION                          "ZPN_ERR_STALE_CONNECTION"


/* AUTHENTICATION ERRORS */
#define ZPN_ERR_AUTH_SAML_EXPIRED                         "ZPN_ERR_AUTH_SAML_EXPIRED"
#define ZPN_ERR_AUTH_EXPIRED                              "ZPN_ERR_AUTH_EXPIRED"
#define ZPN_ERR_CERT_EXPIRED                              "ZPN_ERR_CERT_EXPIRED"
#define ZPN_ERR_AUTH_TIMEOUT                              "ZPN_ERR_AUTH_TIMEOUT"
#define ZPN_ERR_AUTH_NOT_COMPLETE                         "ZPN_ERR_AUTH_NOT_COMPLETE"
#define ZPN_ERR_CUSTOMER_DISABLED                         "ZPN_ERR_CUSTOMER_DISABLED"
#define ZPN_ERR_AUTH_CUSTOMER_FAIL                        "ZPN_ERR_AUTH_CUSTOMER_FAIL"
#define ZPN_ERR_AUTH_CUSTOMER_MISSING                     "ZPN_ERR_AUTH_CUSTOMER_MISSING"
#define ZPN_ERR_AUTH_APP_FAIL                             "ZPN_ERR_AUTH_APP_FAIL"
#define ZPN_ERR_AUTH_APP_DOWNLOAD_FAIL                    "ZPN_ERR_AUTH_APP_DOWNLOAD_FAIL"
#define ZPN_ERR_AUTH_SERVICE_DISABLED                     "ZPN_ERR_AUTH_SERVICE_DISABLED"
#define ZPN_ERR_SCIM_INACTIVE                             "ZPN_ERR_SCIM_INACTIVE"
#define ZPN_ERR_SCIM_MOVED                                "ZPN_ERR_SCIM_MOVED"
#define ZPN_ERR_RISK_USER_MOVED                           "ZPN_ERR_RISK_USER_MOVED"
#define ZPN_ERR_PARTNER_LOGIN_DISABLED                    "ZPN_ERR_PARTNER_LOGIN_DISABLED"
#define ZPN_ERR_SCOPE_FETCH_FAILED                        "ZPN_ERR_SCOPE_FETCH_FAILED"
#define ZPN_ERR_SCOPE_DISABLED                            "ZPN_ERR_SCOPE_DISABLED"
#define ZPN_ERR_SCOPE_MISMATCH                            "ZPN_ERR_SCOPE_MISMATCH"
#define ZPN_ERR_SITE_MISMATCH                             "ZPN_ERR_SITE_MISMATCH"
#define ZPN_ERR_SITEC_SERVICE_DISABLED                    "ZPN_ERR_SITEC_SERVICE_DISABLED"
#define ZPN_ERR_SITEC_GROUP_DISABLED_OR_DELETED           "ZPN_ERR_SITEC_GROUP_DISABLED_OR_DELETED"

#define ZPN_ERR_REGION_RESTRICTED                         "ZPN_ERR_REGION_RESTRICTED"

/* DNS */
#define ZPN_ERR_DNS_CHECK_NOT_FOUND                       "ZPN_ERR_DNS_CHECK_NOT_FOUND"
#define ZPN_ERR_DNS_CHECK_NO_ASSISTANT                    "ZPN_ERR_DNS_CHECK_NO_ASSISTANT"
#define ZPN_ERR_DNS_CHECK_DSP_TIMEOUT                     "ZPN_ERR_DNS_CHECK_DSP_TIMEOUT"
#define ZPN_ERR_DNS_CHECK_MULTIPLE_CLIENT_REGISTRATIONS_FOR_DOMAIN  "ZPN_ERR_DNS_CHECK_MULTIPLE_CLIENT_REGISTRATIONS_FOR_DOMAIN"
#define ZPN_DNS_CHECK_TYPE_A                              "A"
#define ZPN_DNS_CHECK_TYPE_AAAA                           "AAAA"
#define ZPN_DNS_CHECK_TYPE_SRV                            "SRV"
#define ZPN_DNS_CHECK_TYPE_TXT                            "TXT"
#define ZPN_DNS_CHECK_TYPE_BUF_SIZE                       5
#define ZPN_DNS_REJECT_HARD_DISABLED_EXTRANET             "ZPN_DNS_REJECT_HARD_DISABLED_EXTRANET"
#define ZPN_DNS_REJECT_EXTRANET_NOT_ENABLED               "ZPN_DNS_REJECT_EXTRANET_NOT_ENABLED"

/* Health Report Reasons */
#define ZPN_HEALTH_READY                                  "ZPN_HEALTH_READY"
#define ZPN_HEALTH_AGEDOUT                                "ZPN_HEALTH_AGEDOUT"
#define ZPN_HEALTH_UNREACHABLE                            "ZPN_HEALTH_UNREACHABLE"
#define ZPN_HEALTH_UNCONFIGURED                           "ZPN_HEALTH_UNCONFIGURED"

/* Assistant App health check types */
#define AST_APP_HEALTH_CHECK_TYPE_NONE                    "NONE"
#define AST_APP_HEALTH_CHECK_TYPE_DEFAULT                 "DEFAULT"


/* Broker request types and errors */
#define ZPN_BRK_REQ_TYPE_PEEK                             "PEEK"
#define ZPN_BRK_REQ_TYPE_USE_STICKY                       "USE_STICKY"
#define ZPN_ERR_BRK_REQ_NO_CONNECTOR_AVAILABLE            "NO_CONNECTOR_AVAILABLE"
#define ZPN_ERR_BRK_REQ_APP_NOT_REACHABLE                 "APP_NOT_REACHABLE"
#define ZPN_ERR_BRK_REQ_APP_IN_LEARN_MODE                 "APP_IN_LEARN_MODE"
#define ZPN_ERR_BRK_REQ_APP_RETRY                         "APP_RETRY"
#define ZPN_ERR_BRK_REQ_STICKY_CHANGED                    "STICKY_CHANGED"
#define ZPN_ERR_BRK_REQ_MULTIPLE_CLIENT_REGISTRATIONS_FOR_DOMAIN "MULTIPLE_CLIENT_REGISTRATIONS_FOR_DOMAIN"
#define ZPN_ERR_BRK_REQ_INVALID_DOMAIN                     "INVALID_DOMAIN"
#define ZPN_ERR_BRK_REQ_TIMEOUT                            "TIMEOUT"
#define ZPN_ERR_BRK_REQ_NONE                               "NONE"

/* App route info types */
#define ZPN_APP_ROUTE_INFO_APP_TYPE_C2C                   "c2c"

/* App route discovery types */
#define ZPN_APP_ROUTE_DISC_TYPE_NEW                       "NEW"
#define ZPN_APP_ROUTE_DISC_TYPE_RENEW                     "RENEW"
#define ZPN_APP_ROUTE_DISC_TYPE_CONN_REGISTERED           "CONN_REGISTERED"

/* App route registration health report types */
#define ZPN_APP_ROUTE_REG_HEALTH_REPORT_CONTINUOUS        "CONTINUOUS"
#define ZPN_APP_ROUTE_REG_HEALTH_REPORT_ON_ACCESS         "ON-ACCESS"
#define ZPN_APP_ROUTE_REG_HEALTH_REPORT_NONE              "NONE"

/* Broker - Assistant Connection Type (Old, but still used ) */
#define ZPN_ASSISTANT_BROKER_CONTROL                      "ZPN_ASSISTANT_BROKER_CONTROL"
#define ZPN_ASSISTANT_PBROKER_CONTROL                     "ZPN_ASSISTANT_PBROKER_CONTROL"
#define ZPN_ASSISTANT_BROKER_DATA                         "ZPN_ASSISTANT_BROKER_DATA"
#define ZPN_ASSISTANT_BROKER_CONFIG                       "ZPN_ASSISTANT_BROKER_CONFIG"
#define ZPN_ASSISTANT_BROKER_LOG                          "ZPN_ASSISTANT_BROKER_LOG"
#define ZPN_ASSISTANT_BROKER_LOG_INSPECTION               "ZPN_ASSISTANT_BROKER_LOG_INSPECTION"
#define ZPN_ASSISTANT_BROKER_STATS                        "ZPN_ASSISTANT_BROKER_STATS"

#define ZPN_ASSISTANT_SITEC_CONTROL                      "ZPN_ASSISTANT_SITEC_CONTROL"
#define ZPN_ASSISTANT_SITEC_CONFIG                       "ZPN_ASSISTANT_SITEC_CONFIG"
#define ZPN_ASSISTANT_SITEC_LOG                          "ZPN_ASSISTANT_SITEC_LOG"
#define ZPN_ASSISTANT_SITEC_LOG_INSPECTION               "ZPN_ASSISTANT_SITEC_LOG_INSPECTION"
#define ZPN_ASSISTANT_SITEC_STATS                        "ZPN_ASSISTANT_SITEC_STATS"

/* Broker - PCC Connection Type */
#define ZPN_SITEC_BROKER_CONTROL                         "ZPN_SITEC_BROKER_CONTROL"
#define ZPN_SITEC_BROKER_LOG                             "ZPN_SITEC_BROKER_LOG"

/* ZPN Tunnel Types */
#define ZPN_TUNNEL_CONTROL                                "ZPN_TUNNEL_CONTROL"
#define ZPN_TUNNEL_DATA                                   "ZPN_TUNNEL_DATA"
#define ZPN_TUNNEL_CONFIG                                 "ZPN_TUNNEL_CONFIG"
#define ZPN_TUNNEL_LOG                                    "ZPN_TUNNEL_LOG"

#define ZPN_TUNNEL_SITEC_CONTROL                          "ZPN_TUNNEL_SITEC_CONTROL"
#define ZPN_TUNNEL_SITEC_LOG                              "ZPN_TUNNEL_SITEC_LOG"

/* ZPN Tunnel Statuses */
#define ZPN_STATUS_CONNECTED                              "ZPN_STATUS_CONNECTED"
#define ZPN_STATUS_AUTHENTICATED                          "ZPN_STATUS_AUTHENTICATED"
#define ZPN_STATUS_AUTH_FAILED                            "ZPN_STATUS_AUTH_FAILED"
#define ZPN_STATUS_DISCONNECTED                           "ZPN_STATUS_DISCONNECTED"
#define ZPN_STATUS_UNCONFIGURED                           "ZPN_STATUS_UNCONFIGURED"

/* broker upgrade request status
   ToDo - add them to et_string.sql with a error code
*/
#define BRK_CONN_UPGRADE_REQUEST_FORBIDDEN                 "BRK_CONN_UPGRADE_REQUEST_FORBIDDEN"
#define BRK_CONN_UPGRADE_REQUEST_FAILED                    "BRK_CONN_UPGRADE_REQUEST_FAILED"

#define BRK_MT_TERMINATED                                 "BRK_MT_TERMINATED"
#define BRK_MT_TERMINATED_IDLE_TIMEOUT                    "BRK_MT_TERMINATED_IDLE_TIMEOUT"
#define BRK_MT_TERMINATED_APPROVAL_TIMEOUT                "BRK_MT_TERMINATED_APPROVAL_TIMEOUT"
#define BRK_MT_TERMINATED_PSE_BROKER_CHANGED              "BRK_MT_TERMINATED_PSE_BROKER_CHANGED"
#define BRK_MT_SETUP_TIMEOUT                              "BRK_MT_SETUP_TIMEOUT"
#define BRK_MT_SETUP_FAIL_APP_NOT_FOUND                   "BRK_MT_SETUP_FAIL_APP_NOT_FOUND"
#define BRK_MT_SETUP_FAIL_USER_NOT_FOUND                  "BRK_MT_SETUP_FAIL_USER_NOT_FOUND"
#define BRK_MT_SETUP_FAIL_CUSTOMER_NOT_FOUND              "BRK_MT_SETUP_FAIL_CUSTOMER_NOT_FOUND"
#define BRK_MT_SETUP_FAIL_SAML_EXPIRED                    "BRK_MT_SETUP_FAIL_SAML_EXPIRED"
#define BRK_MT_SETUP_FAIL_REJECTED_BY_POLICY              "BRK_MT_SETUP_FAIL_REJECTED_BY_POLICY"
#define BRK_MT_SETUP_FAIL_REAUTH_WITH_AL                  "BRK_MT_SETUP_FAIL_REAUTH_WITH_AL"
#define BRK_MT_SETUP_FAIL_REJECTED_BY_POLICY_APPROVAL     "BRK_MT_SETUP_FAIL_REJECTED_BY_POLICY_APPROVAL"
#define BRK_MT_SETUP_FAIL_ACCESS_DENIED                   "BRK_MT_SETUP_FAIL_ACCESS_DENIED"
#define BRK_MT_SETUP_FAIL_WEBPROBE_RATE_LIMITED           "BRK_MT_SETUP_FAIL_WEBPROBE_RATE_LIMITED"
#define BRK_MT_SETUP_FAIL_WEBPROBE_HTTPS_DISABLED         "BRK_MT_SETUP_FAIL_WEBPROBE_HTTPS_DISABLED"
#define BRK_MT_SETUP_FAIL_LAST_AUTH_EXPIRED               "BRK_MT_SETUP_FAIL_LAST_AUTH_EXPIRED"
// changing BRK_MT_SETUP_FAIL_REAUTH_EXPIRED tp BRK_MT_SETUP_FAIL_SAML_EXPIRED for backward compatibility
#define BRK_MT_SETUP_FAIL_REAUTH_EXPIRED                  "BRK_MT_SETUP_FAIL_SAML_EXPIRED"
#define BRK_MT_SETUP_FAIL_REAUTH_EXPIRED_WITH_AL          "BRK_MT_SETUP_FAIL_REAUTH_EXPIRED_WITH_AL"
#define BRK_MT_SETUP_FAIL_NO_POLICY_FOUND                 "BRK_MT_SETUP_FAIL_NO_POLICY_FOUND"
#define BRK_MT_SETUP_FAIL_CANNOT_SEND_TO_DISPATCHER       "BRK_MT_SETUP_FAIL_CANNOT_SEND_TO_DISPATCHER"
#define BRK_MT_SETUP_FAIL_MEMORY                          "BRK_MT_SETUP_FAIL_MEMORY"
#define BRK_MT_SETUP_FAIL_PROBE                           "BRK_MT_SETUP_FAIL_PROBE"
#define BRK_MT_SETUP_FAIL_NO_PROBE_DATA                   "BRK_MT_SETUP_FAIL_NO_PROBE_DATA"
#define BRK_MT_SETUP_FAIL_CANNOT_PROMOTE                  "BRK_MT_SETUP_FAIL_CANNOT_PROMOTE"
#define BRK_MT_SETUP_FAIL_NO_ASSISTANT_GROUPS             "BRK_MT_SETUP_FAIL_NO_ASSISTANT_GROUPS"
#define BRK_MT_SETUP_FAIL_CONNECTOR_GROUPS_MISSING        "BRK_MT_SETUP_FAIL_CONNECTOR_GROUPS_MISSING"
#define BRK_MT_SETUP_FAIL_REPEATED_DISPATCH               "BRK_MT_SETUP_FAIL_REPEATED_DISPATCH"
#define INVALID_DOMAIN                                    "INVALID_DOMAIN"
#define BRK_MT_SETUP_FAIL_ALLOCATION                      "BRK_MT_SETUP_FAIL_ALLOCATION"
#define BRK_MT_SETUP_FAIL_APP_NULL                        "BRK_MT_SETUP_FAIL_APP_NULL"
#define BRK_MT_SETUP_FAIL_IVNALID_SRV_PORT                "BRK_MT_SETUP_FAIL_IVNALID_SRV_PORT"
#define BRK_MT_SETUP_FAIL_INCOMPLETE_AUTH                 "BRK_MT_SETUP_FAIL_INCOMPLETE_AUTH"
#define BRK_MT_SETUP_FAIL_DUPLICATE_TAG_ID                "BRK_MT_SETUP_FAIL_DUPLICATE_TAG_ID"
#define BRK_MT_SETUP_FAIL_BIND_TO_CLIENT_LOCAL_OWNER      "BRK_MT_SETUP_FAIL_BIND_TO_CLIENT_LOCAL_OWNER"
#define BRK_MT_SETUP_FAIL_BIND_TO_AST_LOCAL_OWNER         "BRK_MT_SETUP_FAIL_BIND_TO_AST_LOCAL_OWNER"
#define BRK_MT_SETUP_FAIL_BIND_RECV_IN_BAD_STATE          "BRK_MT_SETUP_FAIL_BIND_RECV_IN_BAD_STATE"
#define BRK_MT_SETUP_FAIL_CANNOT_SEND_MT_COMPLETE         "BRK_MT_SETUP_FAIL_CANNOT_SEND_MT_COMPLETE"
#define BRK_MT_SETUP_FAIL_TO_CREATE_STREAM                "BRK_MT_SETUP_FAIL_TO_CREATE_STREAM"
#define BRK_MT_SETUP_FAIL_INVALID_TLV                     "BRK_MT_SETUP_FAIL_INVALID_TLV"
#define BRK_MT_CLOSED_FROM_CLIENT                         "BRK_MT_CLOSED_FROM_CLIENT"
#define BRK_MT_SETUP_FAIL_SCIM_INACTIVE                   "BRK_MT_SETUP_FAIL_SCIM_INACTIVE"
#define BRK_MT_SETUP_FAIL_CTRL_BRK_CANNOT_FIND_CONNECTOR  "BRK_MT_SETUP_FAIL_CTRL_BRK_CANNOT_FIND_CONNECTOR"
#define BRK_MT_SETUP_FAIL_RATE_LIMIT_EXCEEDED             "BRK_MT_SETUP_FAIL_RATE_LIMIT_EXCEEDED"
#define BRK_MT_SETUP_FAIL_RATE_LIMIT_NUM_APP_EXCEEDED     "BRK_MT_SETUP_FAIL_RATE_LIMIT_NUM_APP_EXCEEDED"
#define BRK_MT_SETUP_FAIL_RATE_LIMIT_LOOP_DETECTED        "BRK_MT_SETUP_FAIL_RATE_LIMIT_LOOP_DETECTED"
#define BRK_MT_TERMINATED_BRK_SWITCHED                    "BRK_MT_TERMINATED_BRK_SWITCHED"
#define BRK_MT_SETUP_BIND_REJECTED_BY_IP_POLICY           "BRK_MT_SETUP_BIND_REJECTED_BY_IP_POLICY"
#define BRK_MT_SETUP_FAIL_BIND_MTUNNEL_NOT_FOUND          "BRK_MT_SETUP_FAIL_BIND_MTUNNEL_NOT_FOUND"
#define BRK_MT_SETUP_FAIL_BIND_MTUNNEL_MISSING_PARAMS     "BRK_MT_SETUP_FAIL_BIND_MTUNNEL_MISSING_PARAMS"
#define BRK_MT_REJECT_MAX_WTAG_EXCEEDED                   "BRK_MT_REJECT_MAX_WTAG_EXCEEDED"
#define BRK_MT_REJECT_NOT_FORWARDING_BROKER               "BRK_MT_REJECT_NOT_FORWARDING_BROKER"

/*
 * Assistant will close the tunnel only when application server closes it (TCP FIN or RST)
 */
#define BRK_MT_CLOSED_FROM_ASSISTANT                      "BRK_MT_CLOSED_FROM_ASSISTANT"
#define BRK_MT_RESET_FROM_SERVER                          "BRK_MT_RESET_FROM_SERVER"
#define BRK_MT_AUTH_SAML_FAILURE                          "BRK_MT_AUTH_SAML_FAILURE"
#define BRK_MT_AUTH_NO_SAML_ASSERTION_IN_MSG              "BRK_MT_AUTH_NO_SAML_ASSERTION_IN_MSG"
#define BRK_MT_AUTH_TWO_SAML_ASSERTION_IN_MSG             "BRK_MT_AUTH_TWO_SAML_ASSERTION_IN_MSG"
#define BRK_MT_AUTH_SAML_ASSERTION_TOO_LARGE              "BRK_MT_AUTH_SAML_ASSERTION_TOO_LARGE"
#define BRK_MT_AUTH_SAML_DECODE_FAIL                      "BRK_MT_AUTH_SAML_DECODE_FAIL"
#define BRK_MT_AUTH_SAML_FINGER_PRINT_FAIL                "BRK_MT_AUTH_SAML_FINGER_PRINT_FAIL"
#define BRK_MT_AUTH_SAML_NO_USER_ID                       "BRK_MT_AUTH_SAML_NO_USER_ID"
#define BRK_MT_AUTH_SAML_CANNOT_ADD_ATTR_TO_HEAP          "BRK_MT_AUTH_SAML_CANNOT_ADD_ATTR_TO_HEAP"
#define BRK_MT_AUTH_SAML_CANNOT_ADD_ATTR_TO_HASH          "BRK_MT_AUTH_SAML_CANNOT_ADD_ATTR_TO_HASH"
#define BRK_MT_AUTH_ALREADY_FAILED                        "BRK_MT_AUTH_ALREADY_FAILED"
#define BRK_ERR_UNKNOWN                                   "BRK_ERR_UNKNOWN"
#define BRK_MT_REJECT_HARD_DISABLED_EXTRANET              "BRK_MT_REJECT_HARD_DISABLED_EXTRANET"
#define BRK_MT_REJECT_EXTRANET_NOT_ENABLED                "BRK_MT_REJECT_EXTRANET_NOT_ENABLED"
/*
 * Broker Redirect reason code
 */
#define BRK_REDIRECT_REASON_REDIRECT_BROKER               "BRK_REDIRECT_REASON_REDIRECT_BROKER"       /* client hitting a redirect broker */
#define BRK_REDIRECT_REASON_REDIRECT_SITEC                "BRK_REDIRECT_REASON_REDIRECT_SITEC"       /* client hitting a site controller */
#define BRK_REDIRECT_REASON_BROKER_RESTART                "BRK_REDIRECT_REASON_BROKER_RESTART"        /* broker is getting restarted (e.g. upgrade) */
#define BRK_REDIRECT_REASON_BROKER_MAINTENANCE            "BRK_REDIRECT_REASON_BROKER_MAINTENANCE"    /* broker is put under maintenance mode */
#define BRK_REDIRECT_REASON_BALANCE_LOAD                  "BRK_REDIRECT_REASON_BALANCE_LOAD"          /* redirect due to broker load */
#define BRK_REDIRECT_REASON_BALANCE_TIMEOUT               "BRK_REDIRECT_REASON_BALANCE_TIMEOUT"       /* client was redirected some time back */
#define BRK_REDIRECT_REASON_NETWORK_CHANGE                "BRK_REDIRECT_REASON_NETWORK_CHANGE"        /* client was redirected some time back */
#define BRK_REDIRECT_ALT_CLOUD_CHANGE                     "BRK_REDIRECT_ALT_CLOUD_CHANGE"        /* client was redirected due to change in alt cloud */
#define BRK_REDIRECT_REASON_MANUAL_DRAIN_TRIGGER          "BRK_REDIRECT_REASON_MANUAL_DRAIN_TRIGGER"     /* Drain triggered via debug command */
#define BRK_REDIRECT_REASON_CUSTOMER_PARTITION_CHANGE     "BRK_REDIRECT_REASON_CUSTOMER_PARTITION_CHANGE"        /* client was redirected as customer to partition mapping changed */
#define BRK_REDIRECT_REASON_INSTANCE_PARTITION_CHANGE     "BRK_REDIRECT_REASON_INSTANCE_PARTITION_CHANGE"        /* client was redirected as instance to partition mapping changed */
#define BRK_REDIRECT_REASON_INSTANCE_CONSTELLATION_CHANGE "BRK_REDIRECT_REASON_INSTANCE_CONSTELLATION_CHANGE"        /* client was redirected due to constellation configuration change */
#define BRK_REDIRECT_REASON_CUSTOMER_CONSTELLATION_CHANGE "BRK_REDIRECT_REASON_CUSTOMER_CONSTELLATION_CHANGE"   /* client was redirected duw to customer's constellation config change */
#define BRK_REDIRECT_REASON_OTHER                         "BRK_REDIRECT_REASON_OTHER"                 /* catch-all */

#define DSP_MT_SETUP_FAIL_CANNOT_SEND_TO_BROKER           "DSP_MT_SETUP_FAIL_CANNOT_SEND_TO_BROKER"
#define DSP_MT_SETUP_FAIL_DISCOVERY_TIMEOUT               "DSP_MT_SETUP_FAIL_DISCOVERY_TIMEOUT"
#define DSP_MT_SETUP_FAIL_MISSING_HEALTH                  "DSP_MT_SETUP_FAIL_MISSING_HEALTH"

#define AST_MT_MEMORY_ALLOCATION_FAILURE                  "AST_MT_MEMORY_ALLOCATION_FAILURE"
#define AST_MT_TERMINATED                                 "AST_MT_TERMINATED"
#define AST_MT_SETUP_TIMEOUT                              "AST_MT_SETUP_TIMEOUT"
#define AST_MT_SETUP_TIMEOUT_CANNOT_CONN_TO_SERVER        "AST_MT_SETUP_TIMEOUT_CANNOT_CONN_TO_SERVER"
#define AST_MT_SETUP_TIMEOUT_CANNOT_CONN_TO_BROKER        "AST_MT_SETUP_TIMEOUT_CANNOT_CONN_TO_BROKER"
#define AST_MT_SETUP_TIMEOUT_CANNOT_CONN_TO_CONTROL_BROKER       "AST_MT_SETUP_TIMEOUT_CANNOT_CONN_TO_CONTROL_BROKER"
#define AST_MT_SETUP_TIMEOUT_NO_ACK_TO_BIND               "AST_MT_SETUP_TIMEOUT_NO_ACK_TO_BIND"
#define AST_MT_SETUP_ERR_OPEN_SERVER_CONN                 "AST_MT_SETUP_ERR_OPEN_SERVER_CONN"
#define AST_MT_SETUP_ERR_NO_PROCESS_FD                    "AST_MT_SETUP_ERR_NO_PROCESS_FD"
#define AST_MT_SETUP_ERR_NO_SYSTEM_FD                     "AST_MT_SETUP_ERR_NO_SYSTEM_FD"
#define AST_MT_SETUP_ERR_NO_EPHEMERAL_PORT                "AST_MT_SETUP_ERR_NO_EPHEMERAL_PORT"
#define AST_MT_SETUP_ERR_OPEN_BROKER_CONN                 "AST_MT_SETUP_ERR_OPEN_BROKER_CONN"
#define AST_MT_SETUP_ERR_OPEN_SERVER_TIMEOUT              "AST_MT_SETUP_ERR_OPEN_SERVER_TIMEOUT"
#define AST_MT_SETUP_ERR_OPEN_SERVER_ERROR                "AST_MT_SETUP_ERR_OPEN_SERVER_ERROR"
#define AST_MT_SETUP_ERR_OPEN_SERVER_CLOSE                "AST_MT_SETUP_ERR_OPEN_SERVER_CLOSE"
#define AST_MT_SETUP_ERR_SSL_ERROR                        "AST_MT_SETUP_ERR_SSL_ERROR"
#define AST_MT_SETUP_ERR_BROKER_BIND_FAIL                 "AST_MT_SETUP_ERR_BROKER_BIND_FAIL"
#define AST_MT_SETUP_ERR_DUP_MT_ID                        "AST_MT_SETUP_ERR_DUP_MT_ID"
#define AST_MT_SETUP_ERR_BRK_REQ_TIMEOUT_RETRY_DUP        "AST_MT_SETUP_ERR_BRK_REQ_TIMEOUT_RETRY_DUP"
#define AST_MT_SETUP_ERR_HASH_TBL_FULL                    "AST_MT_SETUP_ERR_HASH_TBL_FULL"
#define AST_MT_SETUP_ERR_INIT_FOHH_MCONN                  "AST_MT_SETUP_ERR_INIT_FOHH_MCONN"
#define AST_MT_SETUP_ERR_INIT_BE_MCONN                    "AST_MT_SETUP_ERR_INIT_BE_MCONN"
#define AST_MT_SETUP_ERR_CONN_PEER                        "AST_MT_SETUP_ERR_CONN_PEER"
#define AST_MT_SETUP_ERR_BIND_GLOBAL_OWNER                "AST_MT_SETUP_ERR_BIND_GLOBAL_OWNER"
#define AST_MT_SETUP_ERR_BIND_TO_AST_LOCAL_OWNER          "AST_MT_SETUP_ERR_BIND_TO_AST_LOCAL_OWNER"
#define AST_MT_SETUP_ERR_BIND_ACK                         "AST_MT_SETUP_ERR_BIND_ACK"
#define AST_MT_SETUP_ERR_BRK_HASH_TBL_FULL                "AST_MT_SETUP_ERR_BRK_HASH_TBL_FULL"
#define AST_MT_SETUP_ERR_APP_NOT_FOUND                    "AST_MT_SETUP_ERR_APP_NOT_FOUND"
#define AST_MT_SETUP_DELAYED_REQUEST                      "AST_MT_SETUP_DELAYED_REQUEST"
#define AST_MT_SETUP_INVALID_REQUEST                      "AST_MT_SETUP_INVALID_REQUEST"
#define AST_MT_SETUP_ERR_EXCEED_LIMIT                     "AST_MT_SETUP_ERR_EXCEED_LIMIT"
#define AST_MT_SETUP_ERR_NO_MTUNNEL_ID                    "AST_MT_SETUP_ERR_NO_MTUNNEL_ID"
#define AST_MT_SETUP_ERR_SYSTEM_MTUNNEL_LIMIT_REACHED     "AST_MT_SETUP_ERR_SYSTEM_MTUNNEL_LIMIT_REACHED"
#define AST_MT_SETUP_ERR_MAX_SESSIONS_REACHED             "AST_MT_SETUP_ERR_MAX_SESSIONS_REACHED"
#define AST_MT_SETUP_ERR_CPU_LIMIT_REACHED                "AST_MT_SETUP_ERR_CPU_LIMIT_REACHED"
#define AST_MT_SETUP_ERR_MEM_LIMIT_REACHED                "AST_MT_SETUP_ERR_MEM_LIMIT_REACHED"
#define AST_MT_SETUP_ERR_PRA_UNAVAILABLE                  "AST_MT_SETUP_ERR_PRA_UNAVAILABLE"
#define AST_MT_SETUP_ERR_AST_IN_PAUSE_STATE_FOR_UPGRADE   "AST_MT_SETUP_ERR_AST_IN_PAUSE_STATE_FOR_UPGRADE"
/*
 * NO_DNS for no-health based application. Note that the DNS request can fail when the SEND fails (think socket/fd
 * exhaustion) or a genuine DNS response failure.
 */
#define AST_MT_SETUP_ERR_NO_DNS_TO_SERVER                 "AST_MT_SETUP_ERR_NO_DNS_TO_SERVER"
#define AST_MT_SETUP_ERR_AST_CFG_DISABLED                 "AST_MT_SETUP_ERR_AST_CFG_DISABLED"

#define AST_MT_SETUP_ERR_IPV6_WEBPROBE_HTTPS_DISABLED    "AST_MT_SETUP_ERR_IPV6_WEBPROBE_HTTPS_DISABLED"
#define AST_MT_SETUP_ERR_IPV6_WEBPROBE_HTTP_DISABLED     "AST_MT_SETUP_ERR_IPV6_WEBPROBE_HTTP_DISABLED"

#define AST_MT_CONN_TO_SERVER_STUCK                       "AST_MT_CONN_TO_SERVER_STUCK"

#define MT_CLOSED_TLS_CONN_GONE                           "MT_CLOSED_TLS_CONN_GONE"
#define MT_CLOSED_TLS_CONN_GONE_AST_CLOSED                "MT_CLOSED_TLS_CONN_GONE_AST_CLOSED"
#define MT_CLOSED_TLS_CONN_GONE_CLIENT_CLOSED             "MT_CLOSED_TLS_CONN_GONE_CLIENT_CLOSED"
#define MT_CLOSED_DTLS_CONN_GONE                          "MT_CLOSED_DTLS_CONN_GONE"
#define MT_CLOSED_DTLS_CONN_GONE_AST_CLOSED               "MT_CLOSED_DTLS_CONN_GONE_AST_CLOSED"
#define MT_CLOSED_DTLS_CONN_GONE_CLIENT_CLOSED            "MT_CLOSED_DTLS_CONN_GONE_CLIENT_CLOSED"
#define MT_CLOSED_TLS_CONN_GONE_SCIM_USER_DISABLE         "MT_CLOSED_TLS_CONN_GONE_SCIM_USER_DISABLE"
#define MT_CLOSED_INTERNAL_ERROR                          "MT_CLOSED_INTERNAL_ERROR"
#define MT_CLOSED_FRAMING_ERROR                           "MT_CLOSED_FRAMING_ERROR"
#define MT_CLOSED_TERMINATED                              "MT_CLOSED_TERMINATED"
#define MT_CLOSED_TLS_CONN_GONE_MANUAL_DRAIN              "MT_CLOSED_TLS_CONN_GONE_MANUAL_DRAIN"
#define MT_CLOSED_TLS_CONN_GONE_UPGRADE                   "MT_CLOSED_TLS_CONN_GONE_UPGRADE"

#define EXPTR_FCONN_GONE                                  "EXPTR_FCONN_GONE"
#define EXPTR_FCONN_AUTH_FAIL                             "EXPTR_FCONN_AUTH_FAIL"
#define EXPTR_MT_TLS_SETUP_FAIL_VERSION_MISMATCH          "EXPTR_MT_TLS_SETUP_FAIL_VERSION_MISMATCH"
#define EXPTR_MT_TLS_SETUP_FAIL_NOT_TRUSTED_CA            "EXPTR_MT_TLS_SETUP_FAIL_NOT_TRUSTED_CA"
#define EXPTR_MT_TLS_SETUP_FAIL_CERT_CHAIN_ISSUE          "EXPTR_MT_TLS_SETUP_FAIL_CERT_CHAIN_ISSUE"
#define EXPTR_MT_TLS_SETUP_FAIL_PEER                      "EXPTR_MT_TLS_SETUP_FAIL_PEER"
#define EXPTR_MT_TLS_SETUP_FAIL                           "EXPTR_MT_TLS_SETUP_FAIL"
#define EXPTR_MT_TERMINATED_TIMEOUT                       "EXPTR_MT_TERMINATED_TIMEOUT"

#define PBROKER_FCONN_GONE                                "PBROKER_FCONN_GONE"
#define PBROKER_FCONN_AUTH_FAIL                           "PBROKER_FCONN_AUTH_FAIL"
#define PBROKER_MT_TLS_SETUP_FAIL                         "PBROKER_MT_TLS_SETUP_FAIL"
#define PBROKER_DR_STATE_MISMATCH                         "PBROKER_DR_STATE_MISMATCH"

#define SIEM_CONN_CLOSE                                   "SIEM_CONN_CLOSE"
#define SIEM_CONN_CLOSE_REDIRECT                          "SIEM_CONN_CLOSE_REDIRECT"
#define ZPN_BRK_ASST_CONN_CLOSE                           "ZPN_BRK_ASST_CONN_CLOSE"

/* WAF MTUNNEL FAILURES */
#define AST_MT_INSPECTION_APPLICATION_FAILURE       "AST_MT_FETCH_INSPECTION_APPLICATION_FAIL"

#define DTLS_NOT_ENABLED                                  "DTLS_NOT_ENABLED"
#define OPEN_OR_ACTIVE_CONNECTION                         "OPEN_OR_ACTIVE_CONNECTION"

/* EXPORTER LOG FAILURES */
#define BRK_MT_NOT_FOUND                                  "BRK_MT_NOT_FOUND"
#define BRK_MT_NO_MORE_LOGS                               "BRK_MT_NO_MORE_LOGS"

/* ZVPN / EAS Errors*/
#define BRK_MT_BROKER_REQUEST_ZVPN_NOT_FOUND                "BRK_MT_BROKER_REQUEST_ZVPN_NOT_FOUND"
#define BRK_MT_BROKER_REQUEST_FAIL_SEND_TO_ZVPN             "BRK_MT_BROKER_REQUEST_FAIL_SEND_TO_ZVPN"
#define BRK_MT_ZVPN_ERROR                                   "BRK_MT_ZVPN_ERROR"

/* Common names for slogger */
#define ZPN_SLOGGER_NAMEID                    "ZPA LSS Client"
#define ZPN_EDGE_CONNECTOR_NAMEID             "ZPA Edge Connector"
#define ZPN_IP_ANCHORING_NAMEID               "ZPA IP Anchoring"
#define ZPN_EXPORTER_NOAUTH_NAMEID            "ZPA BA Unauthenticated"
#define ZPN_MACHINE_NAMEID                    "ZPA MACHINE"


enum zpn_err_brk_req {
    zpn_err_brk_req_no_error = 0,
    zpn_err_brk_req_no_connector_available,
    zpn_err_brk_req_app_not_reachable,
    zpn_err_brk_req_app_in_learn_mode,
    zpn_err_brk_req_app_retry,
    zpn_err_brk_req_sticky_changed,
    zpn_err_brk_req_multiple_client_registrations_for_domain,
    zpn_err_brk_req_invalid_domain,
    zpn_err_brk_req_timeout_redispatch_to_diff_dc,
    zpn_err_brk_req_circuit_breaker_state_open
};

enum zc_error_description {
    zpn_error_saml_assertion_expired,
    zpn_error_reauth_expired,
    zpn_error_unknown
};

/*
 *
 * NOTE: for forward/backward compatibility reasons,
 * 1. None of the fields can be deleted EVER
 * 2. None of the fields should be re-ordered EVER
 * 3. _FUTURE_GROWTH fields can be re-used to fit your purpose.
 *
 *
 * Path selection decisions are today done in Dispatcher & connector. In the future user's broker will also start
 * making some decision. These flags will catch whatever decision is done at each layer. Note that each component can
 * make multiple small decisions to come up with the bigger decision for the transaction - so expect multiple fields
 * to be set.
 *
 * ZPN_TX_PATH_DECISION_BRK_TX_TO_DSP is independent of ZPN_TX_PATH_DECISION_BRK_CACHE_HIT, because we can have cases,
 * where broker found a cache, but not able to send it to the connector directly. In that case, broker makes another
 * decision to TX the request to the dispatcher directly.
 */
#define ZPN_TX_PATH_DECISION_AST_HEALTH_TX                      (uint64_t)0x0001
#define ZPN_TX_PATH_DECISION_AST_NO_HEALTH_TX                   (uint64_t)0x0002
#define ZPN_TX_PATH_DECISION_AST_DSP_DECISION                   (uint64_t)0x0004
#define ZPN_TX_PATH_DECISION_AST_DSP_DECISION_OVERRIDDEN        (uint64_t)0x0008
#define ZPN_TX_PATH_DECISION_AST_STICKY_CACHE                   (uint64_t)0x0010
#define ZPN_TX_PATH_DECISION_AST_NEW_SEARCH                     (uint64_t)0x0020
#define ZPN_TX_PATH_DECISION_AST_PREFER_DISPATCHER              (uint64_t)0x0040
#define ZPN_TX_PATH_DECISION_AST_PREFER_LOCAL                   (uint64_t)0x0080
#define ZPN_TX_PATH_DECISION_AST_DATABROKER_RESILIENCE          (uint64_t)0x0100
#define ZPN_TX_PATH_DECISION_AST_FOR_FUTURE_GROWTH2             (uint64_t)0x0200
#define ZPN_TX_PATH_DECISION_AST_FOR_FUTURE_GROWTH3             (uint64_t)0x0400
#define ZPN_TX_PATH_DECISION_AST_FOR_FUTURE_GROWTH4             (uint64_t)0x0800
#define ZPN_TX_PATH_DECISION_AST_FOR_FUTURE_GROWTH5             (uint64_t)0x1000
#define ZPN_TX_PATH_DECISION_AST_FOR_FUTURE_GROWTH6             (uint64_t)0x2000
#define ZPN_TX_PATH_DECISION_AST_FOR_FUTURE_GROWTH7             (uint64_t)0x4000
#define ZPN_TX_PATH_DECISION_AST_FOR_FUTURE_GROWTH8             (uint64_t)0x8000
#define ZPN_TX_PATH_DECISION_BRK_CACHE_HIT                      (uint64_t)0x10000
#define ZPN_TX_PATH_DECISION_BRK_CACHE_MISS                     (uint64_t)0x20000
#define ZPN_TX_PATH_DECISION_BRK_TX_TO_AST                      (uint64_t)0x40000
#define ZPN_TX_PATH_DECISION_BRK_TX_TO_AST_FAIL                 (uint64_t)0x80000
#define ZPN_TX_PATH_DECISION_BRK_TX_TO_DSP                      (uint64_t)0x100000
#define ZPN_TX_PATH_DECISION_BRK_TX_TO_DSP_FAIL_SEND_FAILED     (uint64_t)0x200000
#define ZPN_TX_PATH_DECISION_BRK_TX_TO_DSP_FAIL_TOO_MANY_RETRY  (uint64_t)0x400000
#define ZPN_TX_PATH_DECISION_BRK_TX_TO_DSP_IN_DIFF_DC           (uint64_t)0x800000
#define ZPN_TX_PATH_DECISION_BRK_NEG_CACHE_HIT                  (uint64_t)0x1000000
#define ZPN_TX_PATH_DECISION_BRK_TX_TO_DSP_TIMEOUT_RETRY        (uint64_t)0x2000000
/* dispatcher can start from the 64th bit and grow inwards */
// Do not use MSB, it overflows argo differential decoder.
#define ZPN_TX_PATH_DECISION_DSP_EXTRANET_APP_ROUTE             (uint64_t)0x0004000000000000
#define ZPN_TX_PATH_DECISION_DSP_EXTRANET_ROUTE_CACHE_HIT       (uint64_t)0x0008000000000000
#define ZPN_TX_PATH_DECISION_DSP_EXTRANET_ROUTE_NO_CACHE        (uint64_t)0x0010000000000000
#define ZPN_TX_PATH_DECISION_DSP_LOAD_BALANCE_CACHE_SESSION     (uint64_t)0x0020000000000000
#define ZPN_TX_PATH_DECISION_DSP_LOAD_BALANCE_ACTIVE            (uint64_t)0x0040000000000000
#define ZPN_TX_PATH_DECISION_DSP_LOAD_BALANCE_PASSIVE           (uint64_t)0x0080000000000000
#define ZPN_TX_PATH_DECISION_DSP_DISPATCH_C2C_LOCAL_BYPASS_REGEX_MATCH (uint64_t)0x0100000000000000
#define ZPN_TX_PATH_DECISION_DSP_DISPATCH_C2C                   (uint64_t)0x0200000000000000
#define ZPN_TX_PATH_DECISION_DSP_DISPATCH_HEALTH                (uint64_t)0x0400000000000000
#define ZPN_TX_PATH_DECISION_DSP_DISPATCH_NO_HEALTH             (uint64_t)0x0800000000000000
#define ZPN_TX_PATH_DECISION_DSP_CACHE_NONE                     (uint64_t)0x1000000000000000
#define ZPN_TX_PATH_DECISION_DSP_CACHE_AST_PREF                 (uint64_t)0x2000000000000000
#define ZPN_TX_PATH_DECISION_DSP_CACHE_SESSION                  (uint64_t)0x4000000000000000
#define ZPN_TX_PATH_DECISION_STR_LEN                            128
#define ZPN_TX_INSP_STATUS_STR_LEN                              128

/* Warning: Do not change the order of fields in this structure as it
 * is initialized statically in zpn_lib.c */
struct zpn_client_capability {
    char *name;
    int enabled;
    int uses_saml;
    int global_rules;
    int siem_rules;
    int gets_apps;
    int gets_ips;      /* Whether or not the client gets connector IP tuple in mtunnel req ack */
    int segments;      /* Whether or not the client gets applications
                        * as segments when downloaded. (If not set,
                        * client gets aggregated domains) */
    int fwd_to_pub;    /* Whether or not this client type is forwarded to public broker */
    int can_srv_grp;   /* Whether or not this client type is allowed to specify server groups in mtunnel requests */
    int cn_username;   /* Whether this client uses its peer CN as its username when making requests if not username field is used*/

    int telemetry;     /* Whether we will pay attention (log, do policy on) telemetry sent by this client */

    int fohh_redirect; /* Whether or not the specified client does fohh redirect */
    int fohh_redirect_priv_pb; /* if fohh_redirect is true, can we redir to private broker? */
    int fohh_redirect_pub_pb;  /* if fohh_redirect is true, can we redir to public private broker? */
    int fohh_redirect_pub_mdc;  /* if fohh_redirect is true, can we redir to public brokers from multiple dcs? */

    int app_add_star;  /* Whether to replace . with .* in client app domains */
    int location_policy; /* Whether to lookup and add locations for policy processing for such client */
    int uses_znf_policy_data; /* Whether there is znf data to fetch during policy evaluation */
    int scim_compatible; /* Whether the client can be configured with scim for policy application */
    int ac_looping; /* Whether the client can trigger mtunnel creation loops (App Connector behind a Cloud Connector is an example setup) */
    int tunnel_pr;     /* Whether to pause/resume the whole fohh_tlv connection. */
    int fohh_autotune; /* Whether to autotune fohh connection. */
    int ignore_cache;  /* Whether to ignore cache. Currently 1 means bypass both session and connector caches. Not applicable to connector's sticky caches. */
    int has_user_risk;     /* User risk required idp_gid_username, only Zapp, Zapp partner, exporter and browser isolation connections has that */
    int can_target_app_gid;   /* Whether or not this client type is allowed to specify application gid in mtunnel requests */

    int zia_inspection; /* 1. if this client type mtunnel is allowed to do zia inspection. 0 = mtunnel continues without zia inspection */
    int broker_policy_redirect; /* If broker redirect is allowed its 1, otherwise its 0, this mirrors values for fohh_redirect_priv_pb and fohh_redirect_pub_pb columns for the given client type */
    int drain_conn_support; /* Can the customer specific connections be redirected to other instance/constellation/partition */
    int multi_match; /* whether or not this client type support multiple application match style(firewall style policy) */
    int ptag; /* whether or not this client type support protocol tagging feature */
    int workload_tag_grp; /* If workload tag feature is enabled for this client */
    int np; /* whether or not this client supports NP */
    int appscale; /* Legacy Clients which support app scaling feature with capability no_domain_download like ZCC Windows and MAC*/
    int appscale_v2; /* New Clients which support app scaling feature with capability no_domain_download_v2 like ZCC android, IOS, BC, CC, SIPA */
    int needs_full_app_data_on_app_check; /* Does the client require augmented app check with full app segment data during app check */
};

#define ZPN_PLATFORM_MAC            "mac"
#define ZPN_PLATFORM_ANDROID        "android"
#define ZPN_PLATFORM_IOS            "ios"
#define ZPN_PLATFORM_WINDOWS        "windows"
#define ZPN_PLATFORM_LINUX          "linux"
#define ZPN_PLATFORM_INVALID        "invalid"

enum zpn_platform_type {
    zpn_platform_type_invalid = 0,
    zpn_platform_type_windows,
    zpn_platform_type_linux,
    zpn_platform_type_ios,
    zpn_platform_type_android,
    zpn_platform_type_mac,
    zpn_platform_type_total_count
};

#define ZPN_PLATFORM_TYPE_STR(__platform__) \
    (__platform__ == zpn_platform_type_windows) ? ZPN_PLATFORM_WINDOWS : \
    (__platform__ == zpn_platform_type_linux) ? ZPN_PLATFORM_LINUX : \
    (__platform__ == zpn_platform_type_ios) ? ZPN_PLATFORM_IOS : \
    (__platform__ == zpn_platform_type_android) ? ZPN_PLATFORM_ANDROID : \
    (__platform__ == zpn_platform_type_mac) ? ZPN_PLATFORM_MAC : ZPN_PLATFORM_INVALID

/*
 * Client_type always refers to the system originating the initial
 * mtunnel request. For example, there is no pbroker client_type,
 * since pbroker is never a client.
 */
enum zpn_client_type {
    zpn_client_type_invalid = 0,
    zpn_client_type_zapp, /* ZCC,client connector */
    zpn_client_type_slogger,
    zpn_client_type_exporter,
    zpn_client_type_edge_connector, /* cloud connector */
    zpn_client_type_private_broker, /* For CONTROL/CONFIG tunnels ONLY */
    zpn_client_type_ip_anchoring,   /* SME,SIPA */
    zpn_client_type_browser_isolation,
    zpn_client_type_exporter_noauth,
    zpn_client_type_machine_tunnel,
    zpn_client_type_broker_transit,  /* b2b transit, part c2c */
    zpn_client_type_branch_conn_svc, /* ZIA Branch Connector (as a service) */
    zpn_client_type_branch_connector,
    zpn_client_type_zapp_partner,   /* ZCC partner tunnel */
    zpn_client_type_zia_inspection, /* ZIA inspection tunnel */
    zpn_client_type_vdi,            /* users from Multi user VDI */
    zpn_client_type_zia_proxy,      /* CBI Proxy*/
    zpn_client_type_assistant,
    zpn_client_type_site_controller, /* site_controller connections to broker*/
    zpn_client_type_eas_ctrl,    /* ZIA/ZVPN EAS control connection */
    zpn_client_type_eas_data,    /* ZIA/ZVPN EAS data connection */
    zpn_client_type_total_count, /* MUST BE LAST. Not used for any
                                  * real client Update
                                  * zpn_client_static_config when
                                  * making changes to this table
                                  */

};

/*
 * Appscaling - Utility structure
 * Structure used to populate all the platform
 * and clients for which operation is intended
 */
struct zpn_lib_appscale_client_cookie {
    int platforms[zpn_platform_type_total_count];
    int clients[zpn_client_type_total_count];
};

/*
 * Types of probes we can get
 */
enum zpn_probe_type {
    zpn_probe_type_default=0,
    zpn_probe_type_zdx_mtr,
    zpn_probe_type_zdx_web_probe_deprecated,
    zpn_probe_type_zdx_web_probe,
    zpn_probe_type_zdx_web_probe_https
};

struct zpn_tunnel_capability {
    char *name;
    int enabled;
    int gets_apps;
    int tunnel_pr;     /* Whether to pause/resume the whole fohh_tlv connection. */
    int fohh_autotune; /* Whether to autotune fohh connection. */
};

/*
 * tunnel_auth refers to the mechanism by which a TLS connection was
 * cryptographically authenticated
 */
enum zpn_tunnel_auth {
    zpn_tunnel_auth_invalid=0,

    /* zpn_tunnel_auth_cloud: Authentication of a zscaler device to
     * another zscaler device. This authentication uses trusted cloud
     * cert (i.e. dev.zpath.net.crt) to verify the peer has been
     * properly signed. */
    zpn_tunnel_auth_cloud,

    /* zpn_tunnel_auth_zapp: Authentication of a ZAPP endpoint with a
     * broker (private or public). ZAPP trusts both cloud root cert
     * and ZAPP customer's root cert. This allows ZAPP to communicate
     * with both public and private brokers. Actual ZAPP certificate
     * is verified by checking it against certs in zpn_client
     * table. */
    zpn_tunnel_auth_zapp,

    /* zpn_tunnel_auth_connector: Authentication of a connector with a
     * broker (private or public). This is pretty similar to ZAPP
     * authentication, except the certificate is verified within the
     * zpn_issuedcert table. */
    zpn_tunnel_auth_connector,

    /* zpn_tunnel_auth_znf: Authentication exceptionally similar to
     * zpn_tunnel_auth_connector. The only difference is that
     * zpn_issuedcert indicates (to the broker) that the peer is an
     * edge connector (znf) instead of a connector.  */
    zpn_tunnel_auth_znf,

    /* zpn_tunnel_auth_private: Authentication exceptionally similar
     * to zpn_tunnel_auth_connector. The only difference is that
     * zpn_issuedcert indicates (to the broker) that the peer is a
     * private broker instead of a connector.  */
    zpn_tunnel_auth_private_broker,

    zpn_tunnel_auth_machine_tunnel,

    /* zpn_tunnel_auth_browser_isolation. Authentication of browser
     * isolation container to public broker */
    zpn_tunnel_auth_browser_isolation,

    zpn_tunnel_auth_branch_connector,
    /* zpn_tunnel_auth_zapp_partner: Authentication of a ZAPP partner
     * endpoint with a broker (private or public). This is pretty similar
     * to ZAPP except one extra step to check that it's marked as partner in
     * zpn_client table. ZAPP partner trusts both cloud root cert and ZAPP
     * customer's root cert. This allows ZAPP partner to communicate with
     * both public and private brokers (exactly same as ZAPP). Actual
     * certificate is verified by checking it against certs in zpn_client
     * table. */
    zpn_tunnel_auth_zapp_partner,

    /* zpn_tunnel_auth_private: Authentication exceptionally similar
     * to zpn_tunnel_auth_connector. The only difference is that
     * zpn_issuedcert indicates (to the broker) that the peer is a
     * private broker instead of a connector.  */
    zpn_tunnel_auth_site_controller,

    zpn_tunnel_auth_total_count, /* MUST BE LAST. Not used for any
                                  * real client Update
                                  * zpn_client_static_config when
                                  * making changes to this table
                                  */
};

extern struct zpn_client_capability zpn_client_static_config[zpn_client_type_total_count];
extern struct zpn_tunnel_capability zpn_tunnel_static_config[zpn_tunnel_auth_total_count];

enum zpn_mtunnel_type {
    zmt_name = 0,
    zmt_dns_srv = 1,
    zmt_ip = 2,
    zmt_use_tls = 3,
    zmt_guac = 4
};

/* insp_status types*/
enum zpn_trans_waf_insp_status_type {
    zpn_trans_waf_no_policy_or_insp_disabled,
    zpn_trans_waf_disabled_insp_policy_for_app,
    zpn_trans_waf_feature_disabled,
    zpn_trans_waf_no_domain_match,

    zpn_trans_waf_internal_err,
    zpn_trans_waf_resource_unavail,
    zpn_trans_waf_ssl_to_server_fail,
    zpn_trans_waf_ssl_ctx_cryptosvc_error,
    zpn_trans_waf_ssl_ctx_certgen_error,
    zpn_trans_waf_resource_arrival_timeout,
    zpn_trans_waf_status_unassigned,

    zpn_trans_waf_http,
    zpn_trans_waf_double_enc_http,
    zpn_trans_waf_https,
    zpn_trans_waf_double_enc_https,

    zpn_trans_waf_krb,
    zpn_trans_waf_double_enc_krb,
    zpn_trans_waf_ldap,
    zpn_trans_waf_double_enc_ldap,
    zpn_trans_waf_smb,
    zpn_trans_waf_double_enc_smb,

    zpn_trans_waf_auto,
    zpn_trans_waf_double_enc_auto,
    zpn_trans_waf_auto_tls,
    zpn_trans_waf_double_enc_auto_tls,

    zpn_trans_waf_ptag,
    zpn_trans_waf_double_enc_ptag,
    /* added here instead of 1st to avoid interop bugs */
    /* note if error codes added here - increase waf_insp_status_str arr in zpn_lib.c required */
};

#define ZPN_IS_WAF_INSPECTION_PERFORMED(__insp_status__)                                                        \
    ((zpn_trans_waf_http == __insp_status__) || (zpn_trans_waf_double_enc_http == __insp_status__) ||           \
     (zpn_trans_waf_https == __insp_status__) || (zpn_trans_waf_double_enc_https == __insp_status__) ||         \
     (zpn_trans_waf_krb == __insp_status__) || (zpn_trans_waf_double_enc_krb == __insp_status__) ||             \
     (zpn_trans_waf_ldap == __insp_status__) || (zpn_trans_waf_double_enc_ldap == __insp_status__) ||           \
     (zpn_trans_waf_smb == __insp_status__) || (zpn_trans_waf_double_enc_smb == __insp_status__) ||             \
     (zpn_trans_waf_auto == __insp_status__) || (zpn_trans_waf_double_enc_auto == __insp_status__) ||           \
     (zpn_trans_waf_auto_tls == __insp_status__) || (zpn_trans_waf_double_enc_auto_tls == __insp_status__) ||   \
     (zpn_trans_waf_ptag == __insp_status__) || (zpn_trans_waf_double_enc_ptag == __insp_status__) ||           \
     (zpn_trans_waf_ssl_to_server_fail == __insp_status__))                                                     \
            ? 1                                                                                                 \
            : 0

#define ZPN_IS_WAF_INSPECTION_DISABLED(__insp_status__)                 \
    ((zpn_trans_waf_no_policy_or_insp_disabled == __insp_status__) ||   \
     (zpn_trans_waf_disabled_insp_policy_for_app == __insp_status__) || \
     (zpn_trans_waf_feature_disabled == __insp_status__))               \
            ? 1                                                         \
            : 0

enum zpn_console_credential_type {
    zpn_console_credential_type_username_password=0,
    zpn_console_credential_type_ssh_key,
    zpn_console_credential_type_password,
    zpn_console_credential_type_unknown
};

enum zpn_console_login_type {
    zpn_console_login_type_password=0,
    zpn_console_login_type_passphrase,
    zpn_console_login_type_private_key,
};

#define ZMT_COUNT 5
#define ZMT_NAME_STR "zmt_name"
#define ZMT_DNS_SRV_STR "zmt_dns_srv"
#define ZMT_IP_STR "zmt_ip"
#define ZMT_USE_TLS_STR "zmt_use_tls"
#define ZMT_GUAC_STR "zmt_guac"

#define ZPN_LIB_ZMT_STRING_MAX_LEN  16
extern const char *zmt_string[ZMT_COUNT];
enum zpn_mtunnel_type zpn_app_type_from_str(const char *app_type);
const char* zpn_app_type_to_str(enum zpn_mtunnel_type type);

extern int zpn_init(struct argo_log_collection *event_log);
extern const char *zpn_result_string(int result);

extern const char *zpn_client_type_string(enum zpn_client_type type);
extern enum zpn_client_type zpn_client_type_from_str(const char* str);
extern const char *zpn_client_type_string_from_string(const char* str);
extern int zpn_client_type_is_valid(enum zpn_client_type type);

extern const char *zpn_auth_type_string(enum zpn_tunnel_auth auth_type);

extern int zpn_log_structure(struct argo_structure_description *description, void *structure_data);

extern char *zpn_error_description_string(enum zc_error_description error);

int zpn_client_check_capability_validity(int *capabilities, const char *capability_str);

extern int flow_control_enabled;

extern int no_udp_packetize_on_tlv;

char *
zpn_tx_path_decision_get_str(uint64_t flags, char* buffer, int buffer_len);

/* zpn_tx_insp_status_get_str err_code is of type zpn_trans_waf_insp_status_type */
void
zpn_tx_insp_status_get_str(uint8_t err_code, uint64_t ssl_err, char* buffer_out_err_str);

int zpn_broker_is_alt_cloud_valid(char *alt_cloud);

/*
 * List of capabilities a client can send
 * these are integers values
 *
 * To send multiple capabilities like mtn, prior_info and no_ip_download use
 * ZPN_CLIENT_CAPABILITY_MTN | ZPN_CLIENT_CAPABILITY_PRIOR_INFO | ZPN_CLIENT_CAPABILITY_NO_IP_DOWNLOAD
 *
 * To check if client has send no_ip_download, check using
 * (capability & ZPN_CLIENT_CAPABILITY_NO_IP_DOWNLOAD)
 *
 */
enum {
    zpn_client_capability_begin = 0,
    zpn_client_capability_mtn = zpn_client_capability_begin,
    zpn_client_capability_aca,
    zpn_client_capability_prior_info,
    zpn_client_capability_app_domains,
    zpn_client_capability_no_ip_download,
    zpn_client_capability_no_domain_download,
    zpn_client_capability_c2c,
    zpn_client_capability_alt_cloud_aware,
    zpn_client_capability_latency_probe,
    zpn_client_capability_svcp,
    zpn_client_capability_zia_inspection,
    zpn_client_capability_ipv6_redirect,
    zpn_client_capability_client_state,
    zpn_client_capability_network_presence,
    zpn_client_capability_stepup_auth,
    zpn_client_capability_no_domain_download_v2,
    zpn_client_capability_end
};

enum connector_type {
    app_connector = 0,
    np_connector = 1,
    invalid_connector_type = 2,
};

enum np_connector_status {
    offline = 0,
    online = 1
};

#define ZPN_CLIENT_CAPABILITY_MAX_COUNT     zpn_client_capability_end
#define ZPN_CLIENT_CAPABILITY_ALL           (0xFFFFFFFFUL >> (32 - ZPN_CLIENT_CAPABILITY_MAX_COUNT))

#define ZPN_CLIENT_CAPABILITY_MTN_STR                           "mtn"
#define ZPN_CLIENT_CAPABILITY_ACA_STR                           "aca"
#define ZPN_CLIENT_CAPABILITY_PRIOR_INFO_STR                    "prior_info"
#define ZPN_CLIENT_CAPABILITY_APP_DOMAINS_STR                   "app_domains"
#define ZPN_CLIENT_CAPABILITY_NO_IP_DOWNLOAD_STR                "no_ip_download"
#define ZPN_CLIENT_CAPABILITY_NO_DOMAIN_DOWNLOAD_STR            "no_domain_download"
#define ZPN_CLIENT_CAPABILITY_C2C_STR                           "c2c"
#define ZPN_CLIENT_CAPABILITY_ALT_CLOUD_AWARE_STR               "ALT_CLOUD_AWARE"
#define ZPN_CLIENT_CAPABILITY_LATENCY_PROBE_STR                 "latency_probe"
#define ZPN_CLIENT_CAPABILITY_SVCP_STR                          "svcp"
#define ZPN_CLIENT_CAPABILITY_ZIA_INSPECTION_STR                "ZIA_INSPECTION"
#define ZPN_CLIENT_CAPABILITY_IPV6_REDIRECT_STR                 "IPV6_REDIRECT"
#define ZPN_CLIENT_CAPABILITY_CLIENT_STATE_STR                  "CLIENT_STATE"
#define ZPN_CLIENT_CAPABILITY_NETWORK_PRESENCE_STR              "np"
#define ZPN_CLIENT_CAPABILITY_STEPUP_AUTH_STR                   "STEP_UP_AUTH"
#define ZPN_CLIENT_CAPABILITY_NO_DOMAIN_DOWNLOAD_V2_STR         "no_domain_download_v2"

#define ZPN_CLIENT_CAPABILITY_ALL_STR   (ZPN_CLIENT_CAPABILITY_MTN_STR "," \
                                         ZPN_CLIENT_CAPABILITY_ACA_STR "," \
                                         ZPN_CLIENT_CAPABILITY_PRIOR_INFO_STR "," \
                                         ZPN_CLIENT_CAPABILITY_APP_DOMAINS_STR "," \
                                         ZPN_CLIENT_CAPABILITY_NO_IP_DOWNLOAD_STR "," \
                                         ZPN_CLIENT_CAPABILITY_NO_DOMAIN_DOWNLOAD_STR "," \
                                         ZPN_CLIENT_CAPABILITY_C2C_STR "," \
                                         ZPN_CLIENT_CAPABILITY_ALT_CLOUD_AWARE_STR "," \
                                         ZPN_CLIENT_CAPABILITY_LATENCY_PROBE_STR "," \
                                         ZPN_CLIENT_CAPABILITY_SVCP_STR "," \
                                         ZPN_CLIENT_CAPABILITY_ZIA_INSPECTION_STR "," \
                                         ZPN_CLIENT_CAPABILITY_IPV6_REDIRECT_STR "," \
                                         ZPN_CLIENT_CAPABILITY_CLIENT_STATE_STR "," \
                                         ZPN_CLIENT_CAPABILITY_NETWORK_PRESENCE_STR "," \
                                         ZPN_CLIENT_CAPABILITY_STEPUP_AUTH_STR "," \
                                         ZPN_CLIENT_CAPABILITY_NO_DOMAIN_DOWNLOAD_V2_STR)

static inline const char *zpn_client_capability_to_string(int n)
{
    if (n < zpn_client_capability_begin || n >= zpn_client_capability_end) {
        return "";
    }

    static const char *str[] = {
        ZPN_CLIENT_CAPABILITY_MTN_STR,
        ZPN_CLIENT_CAPABILITY_ACA_STR,
        ZPN_CLIENT_CAPABILITY_PRIOR_INFO_STR,
        ZPN_CLIENT_CAPABILITY_APP_DOMAINS_STR,
        ZPN_CLIENT_CAPABILITY_NO_IP_DOWNLOAD_STR,
        ZPN_CLIENT_CAPABILITY_NO_DOMAIN_DOWNLOAD_STR,
        ZPN_CLIENT_CAPABILITY_C2C_STR,
        ZPN_CLIENT_CAPABILITY_ALT_CLOUD_AWARE_STR,
        ZPN_CLIENT_CAPABILITY_LATENCY_PROBE_STR,
        ZPN_CLIENT_CAPABILITY_SVCP_STR,
        ZPN_CLIENT_CAPABILITY_ZIA_INSPECTION_STR,
        ZPN_CLIENT_CAPABILITY_IPV6_REDIRECT_STR,
        ZPN_CLIENT_CAPABILITY_CLIENT_STATE_STR,
        ZPN_CLIENT_CAPABILITY_NETWORK_PRESENCE_STR,
        ZPN_CLIENT_CAPABILITY_STEPUP_AUTH_STR,
        ZPN_CLIENT_CAPABILITY_NO_DOMAIN_DOWNLOAD_V2_STR
    };

    return str[n];
};

/*
 * Always allow 2 more extra space for new capabilities to grow. So that a old itasca component can copy the
 * capabilities coming from a newer component.
 */
#define ZPN_ASST_MAX_NUMBER_OF_CAPABILITIES 4
#define     ZPN_ASST_CAPABILITY_STR_ASC "ASC"
#define     ZPN_ASST_CAPABILITY_STR_APU "APU"

#define TCP_QUICKACK_FEATURE_MAXLEN 64


#define ZPN_DEBUG_ARRAY_SIZE_64 8
extern uint64_t zpn_debug_ext[];
extern uint64_t zpn_debug_catch_defaults_ext[];

void unit_test_init();
int is_unit_test();
void unit_test_scope_init();
int using_scope_test();
void dta_test_init();
int is_dta_test();
void dta_skip_default_rule_init();
int is_dta_skip_default_rule();
int64_t get_customer_gid_for_unit_test(int64_t scope_gid);
int64_t get_scope_gid_for_unit_test(int64_t customer_gid);
int64_t get_customer_from_scope(int64_t scope_gid);
int is_scope_default(int64_t scope_gid);

static __inline__ void *zpn_debug_cnxt(void)
{
   return zpn_debug_ext;
}

static __inline__ void *zpn_debug_catch_cnxt(void)
{
   return zpn_debug_catch_defaults_ext;
}

static __inline__ uint8_t zpn_debug_cnxt_cnt(void)
{
   return ZPN_DEBUG_NUM_IDX;
}

static __inline__ uint8_t zpn_debug_common_with_ctx(const void *ctx,
                                                    uint16_t pos, uint16_t *arr_idx)
{
   assert(ctx && pos < ZPN_DEBUG_NUM_IDX);
   *arr_idx = pos / 64;
   assert(*arr_idx < ZPN_DEBUG_ARRAY_SIZE_64);
   uint8_t bit_idx = pos % 64;
   return bit_idx;
}

static __inline__ uint8_t zpn_debug_get_with_ctx(const void *ctx, uint16_t pos)
{
   uint16_t arr_idx;
   uint8_t bit_idx = zpn_debug_common_with_ctx(ctx, pos, &arr_idx);
   /* coverity[INTEGER_OVERFLOW:FALSE] */
   return (((uint64_t *)(ctx))[arr_idx] >> bit_idx) & 1;
}

static __inline__ uint8_t zpn_debug_get(uint16_t pos)
{
   return zpn_debug_get_with_ctx(zpn_debug_ext, pos);
}

static __inline__ uint8_t zpn_debug_catch_defaults_get_with_ctx(const void *ctx, uint16_t pos)
{
   return zpn_debug_get_with_ctx(ctx, pos);
}

static __inline__ void zpn_debug_set_with_ctx(void *ctx, uint16_t pos)
{
   uint16_t arr_idx;
   uint8_t bit_idx = zpn_debug_common_with_ctx(ctx, pos, &arr_idx);
   /* coverity[INTEGER_OVERFLOW:FALSE] */
   ((uint64_t *)(ctx))[arr_idx] |= (1ULL << bit_idx);
}

static __inline__ void zpn_debug_set(uint16_t pos)
{
   zpn_debug_set_with_ctx(zpn_debug_ext, pos);
}

static __inline__ void zpn_debug_catch_defaults_set(uint16_t pos)
{
   zpn_debug_set_with_ctx(zpn_debug_catch_defaults_ext, pos);
}

static __inline__ void zpn_debug_reset_with_ctx(void *ctx, uint16_t pos)
{
   uint16_t arr_idx;
   uint8_t bit_idx = zpn_debug_common_with_ctx(ctx, pos, &arr_idx);
   /* coverity[INTEGER_OVERFLOW:FALSE] */
   ((uint64_t *)(ctx))[arr_idx] &= ~(1ULL << bit_idx);
}

static __inline__ void zpn_debug_reset(uint16_t pos)
{
   zpn_debug_reset_with_ctx(zpn_debug_ext, pos);
}

static __inline__ void zpn_debug_catch_defaults_reset(uint16_t pos)
{
   zpn_debug_reset_with_ctx(zpn_debug_catch_defaults_ext, pos);
}

/****  App Scaling functions start ****/

struct app_scale_hard_disable_feature_flag_clients {
    int64_t broker_hard_disabled;
    int64_t pse_hard_disabled;
};

/*
 * Utility function to check if platform is legacy platform
 * for appscaling was first supported
 * i.e ZCC Windows and MAC
 *
 * For MAC we don't have platform type in code prior to 2025 hence
 * use negation for android, ios and linux
 */
int zpn_lib_is_app_scale_legacy_zcc(enum zpn_platform_type platform);

/*
 * Utility function - Different flavours of ZCC for which appscale is supported
 */
int zpn_lib_is_app_scale_zcc_clients(enum zpn_client_type client_type);

/****  App Scaling functions end ****/

int set_debug_flag_by_name(const char *debug_flag_name, int8_t val);

void zpn_debug_set_from_bits(uint64_t bitmap);
void zpn_debug_set_update_from_bits(uint64_t bitmap);
uint8_t zpn_debug_get_from_bit(uint64_t bit);

/*
 * List of states a client can send
 * these are integers values
 *
 * To check if client has send dr_on, check using
 * (state & ZPN_CLIENT_STATE_DR_ON)
 *
 */
#define ZPN_CLIENT_STATE_NONE       0
#define ZPN_CLIENT_STATE_DR_ON      (1<<0)
#define ZPN_CLIENT_STATE_DR_OFF     (1<<1)
#define ZPN_CLIENT_STATE_DR_TEST    (1<<2)
#define ZPN_CLIENT_STATE_DDIL_ON    (1<<3)
#define ZPN_CLIENT_STATE_DDIL_OFF   (1<<4)
#define ZPN_CLIENT_STATE_DDIL_TEST  (1<<5)
#define ZPN_CLIENT_STATE_ALL        \
                (ZPN_CLIENT_STATE_DR_ON | ZPN_CLIENT_STATE_DR_OFF | ZPN_CLIENT_STATE_DR_TEST \
                 | ZPN_CLIENT_STATE_DDIL_ON | ZPN_CLIENT_STATE_DDIL_OFF | ZPN_CLIENT_STATE_DDIL_TEST)

#define ZPN_CLIENT_STATE_MAX_COUNT      6
#define ZPN_CLIENT_STATE_DR_ON_STR      "dr_on"
#define ZPN_CLIENT_STATE_DR_OFF_STR     "dr_off"
#define ZPN_CLIENT_STATE_DR_TEST_STR    "dr_test"
#define ZPN_CLIENT_STATE_DDIL_ON_STR    "ddil_on"
#define ZPN_CLIENT_STATE_DDIL_OFF_STR   "ddil_off"
#define ZPN_CLIENT_STATE_DDIL_TEST_STR  "ddil_test"
#define ZPN_CLIENT_STATE_ALL_STR \
                ZPN_CLIENT_STATE_DR_ON_STR"," \
                ZPN_CLIENT_STATE_DR_OFF_STR"," \
                ZPN_CLIENT_STATE_DR_TEST_STR"," \
                ZPN_CLIENT_STATE_DDIL_ON_STR"," \
                ZPN_CLIENT_STATE_DDIL_OFF_STR"," \
                ZPN_CLIENT_STATE_DDIL_TEST_STR

/* DNS cache related macros */
#define MAX_DNS_CACHE_KEY_LEN           512
#define MAX_DNS_QUERY_TYPE_LEN          32

void zpn_broker_app_calc_threads_count_update(int count);
int zpn_broker_app_calc_threads_count_get();
int zpn_pse_app_calc_threads_count_get_and_update();

/*
 * Generates random number within given range.
 *
 * Lightweight and multithreading safe, but not for cryptographically secure usecase.
 */
uint64_t zpn_xorshift64_rand(uint64_t min, uint64_t max);

#endif /* _ZPN_LIB_H_ */
