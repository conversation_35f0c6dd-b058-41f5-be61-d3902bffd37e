/*
 * zpn_pb_client.c. Copyright (C) 2019 Zscaler Inc. All Rights Reserved.
 */

#include "fohh/fohh.h"
#include "zpn/zpn_lib.h"

#include "zpn/zpn_broker_private.h"
#include "zpn/zpn_fohh_client.h"
#include "zpn/zpn_zrdt_client.h"
#include "zpn/zpn_pb_client.h"
#include "zpn/zpn_broker_private.h"
#include "zpn/zpn_private_broker_site.h"
#include "zpn/zpn_private_broker_util.h"
#include "zpn/zpn_broker_dispatch.h"

#include "zpn/zpn_rpc.h"
#include "zpn/zpn_broker_mtunnel.h"

#include "zpn/zpn_broker_client.h"
#include "zpn/zpn_broker.h"
#include "zpn/zpn_broker_assert.h"
#include "zpn_zdx/zpn_zdx_http.h"
#include "zpn/zpn_assistantgroup_assistant_relation.h"
#include "zpn/zpn_broker_transit.h"
#include "zpn/zpn_c2c_client_registration.h"
#include "zpn/zpn_broker_ipars.h"
#include "zpn/zpn_broker_ipars_request_queue.h"
#include "zpn/zpn_ipars_rpc.h"
#include "zpn/zpn_pbroker_data_connection_stats.h"
#include "zpath_lib/zpath_config_override_keys.h"
#include "zpn/zpn_vdi/zpn_vdi.h"

static void zpn_pb_forward_client_to_client_app_registration(struct zevent_base *base, void *void_cookie, int64_t int_cookie);
int64_t g_config_pb_client_no_create = ZPN_PB_CLIENT_NO_CREATE_DEFAULT;
int64_t g_pb_client_close_fini_count = 0;
int64_t g_pb_zrdt_client_close_count = 0;
int64_t g_pb_client_create_count = 0;
int64_t g_pb_client_no_create_count = 0;
int64_t g_pb_client_close_count = 0;


int64_t g_pb_client_connect_count = 0;
int64_t g_pb_client_disconnect_count = 0;
int64_t g_pb_client_start_auth_count = 0;
int64_t g_pb_client_auth_complete_count = 0;

int64_t pb_client_client_connection_incarnation(struct zpn_pb_client *pb_client)
{
    if (pb_client->inbound_client_connection_tlv_type == zpn_fohh_tlv) {
        struct fohh_connection *f_conn = pb_client->inbound_client_connection;
        return fohh_connection_incarnation(f_conn);
    } else if (pb_client->inbound_client_connection_tlv_type == zpn_zrdt_tlv) {
        struct zrdt_conn *z_conn = pb_client->inbound_client_connection_zrdt;
        return zrdt_conn_incarnation(z_conn);
    } else {
        ZPN_LOG(AL_NOTICE, "Unknown pb_client client connection type");
        return 0;
    }
}

char *pb_client_client_connection_description(struct zpn_pb_client *pb_client)
{
    if (pb_client->inbound_client_connection_tlv_type == zpn_fohh_tlv) {
        struct fohh_connection *f_conn = pb_client->inbound_client_connection;
        return fohh_description(f_conn);
    } else if (pb_client->inbound_client_connection_tlv_type == zpn_zrdt_tlv) {
        struct zrdt_conn *z_conn = pb_client->inbound_client_connection_zrdt;
        return zdtls_description(zrdt_conn_get_datagram_tx_cookie(z_conn));
    } else {
        ZPN_LOG(AL_NOTICE, "Unknown pb_client client connection type");
        return "Unknown client connection type";
    }
}

struct zpn_broker_client_fohh_state *pb_client_to_c_state(struct zpn_pb_client *pb_client)
{
    if (pb_client->inbound_client_connection_tlv_type == zpn_fohh_tlv) {
        struct fohh_connection *f_conn = pb_client->inbound_client_connection;
        return fohh_connection_get_dynamic_cookie(f_conn);
    } else if (pb_client->inbound_client_connection_tlv_type == zpn_zrdt_tlv) {
        struct zrdt_conn *z_conn = pb_client->inbound_client_connection_zrdt;
        return zrdt_conn_get_dynamic_cookie(z_conn);
    } else {
        ZPN_LOG(AL_NOTICE, "Unknown pb_client client connection type");
        return NULL;
    }
}

struct zpn_tlv *pb_client_to_c_state_tlv(struct zpn_pb_client *pb_client)
{
    struct zpn_broker_client_fohh_state *c_state = pb_client_to_c_state(pb_client);
    struct zpn_tlv *tlv = NULL;

    if (c_state) {
        tlv = c_state_get_tlv(c_state);
    }

    return tlv;
}

char *pb_client_broker_conn_description(struct zpn_pb_client *pb_client)
{
    if (pb_client->outbound_tlv_type == zpn_fohh_tlv) {
        struct zpn_fohh_client *fohh_client = pb_client->outbound_pb_to_b_client;

        if (fohh_client) {
            struct fohh_connection *broker_f_conn = zpn_fohh_client_get_f_conn(fohh_client);
            return fohh_description(broker_f_conn);
        } else {
            ZPN_LOG(AL_DEBUG, "Null fohh_client for pb_client: %p", pb_client);
        }
    } else if (pb_client->outbound_tlv_type == zpn_zrdt_tlv) {
        struct zpn_zrdt_client *zrdt_client = pb_client->outbound_pb_to_b_client_zrdt;

        if (zrdt_client) {
            struct zrdt_conn *broker_z_conn = zpn_zrdt_client_get_z_conn(zrdt_client);
            return zdtls_description(zrdt_conn_get_datagram_tx_cookie(broker_z_conn));
        } else {
            ZPN_LOG(AL_DEBUG, "Null zrdt_client for pb_client: %p", pb_client);
        }
    }
    return NULL;
}

int64_t pb_client_broker_conn_incarnation(struct zpn_pb_client *pb_client)
{
    if (pb_client->outbound_tlv_type == zpn_fohh_tlv) {
        struct zpn_fohh_client *fohh_client = pb_client->outbound_pb_to_b_client;

        if (fohh_client) {
            struct fohh_connection *broker_f_conn = zpn_fohh_client_get_f_conn(fohh_client);
            return fohh_connection_incarnation(broker_f_conn);
        } else {
            ZPN_LOG(AL_DEBUG, "Null fohh_client for pb_client: %p", pb_client);
        }
    } else if (pb_client->outbound_tlv_type == zpn_zrdt_tlv) {
        struct zpn_zrdt_client *zrdt_client = pb_client->outbound_pb_to_b_client_zrdt;

        if (zrdt_client) {
            struct zrdt_conn *broker_z_conn = zpn_zrdt_client_get_z_conn(zrdt_client);
            return zrdt_conn_incarnation(broker_z_conn);
        } else {
            ZPN_LOG(AL_DEBUG, "Null zrdt_client for pb_client: %p", pb_client);
        }
    } else {
        ZPN_LOG(AL_NOTICE, "Unknown pb_client client connection type");
    }

    return 0;
}

/* Return 1 if either fohh or zrdt to broker is still connected, otherwise return 0 */
int pb_client_broker_conn_is_connected(struct zpn_pb_client *pb_client)
{
    if (!pb_client) {
        return 0;
    }

    /* Already marked for deletion, consider disconnected */
    if (pb_client->deletion_in_progress == 1) {
        return 0;
    }

    if (pb_client->outbound_tlv_type == zpn_fohh_tlv) {
        struct zpn_fohh_client *fohh_client = pb_client->outbound_pb_to_b_client;

        if (fohh_client) {
            struct fohh_connection *broker_f_conn = zpn_fohh_client_get_f_conn(fohh_client);
            return (fohh_get_state(broker_f_conn) == fohh_connection_connected);
        } else {
            ZPN_LOG(AL_DEBUG, "Null fohh_client for pb_client: %p", pb_client);
        }
    } else if (pb_client->outbound_tlv_type == zpn_zrdt_tlv) {
        struct zpn_zrdt_client *zrdt_client = pb_client->outbound_pb_to_b_client_zrdt;

        if (zrdt_client) {
            return zpn_zrdt_client_connected(zrdt_client);
        } else {
            ZPN_LOG(AL_DEBUG, "Null zrdt_client for pb_client: %p", pb_client);
        }
    } else {
        ZPN_LOG(AL_NOTICE, "Unknown pb_client client connection type");
    }

    return 0;
}

static int pb_client_authenticate_ack_cb(void *argo_cookie_ptr,
                                         void *argo_structure_cookie_ptr,
                                         struct argo_object *object)
{
    struct zpn_client_authenticate_ack *ack;
    struct zpn_pb_client *pb_client = argo_structure_cookie_ptr;
    struct zpn_broker_client_fohh_state *c_state;

    if (pb_client_client_connection_incarnation(pb_client) != pb_client->inbound_client_connection_incarnation) {
        ZPN_LOG(AL_NOTICE, "Client connection gone, closing.");
        return ZPATH_RESULT_ERR;
    }

    ack = object->base_structure_void;
    if (ack->error) {
        ZPN_LOG(AL_ERROR, "Could not authenticate with public broker: %s", ack->error);
        return ZPATH_RESULT_ERR;
    }

    ZPN_DEBUG_PBC("Client, Received authenticate, broker = %s, tunnel = %s", ack->current_broker, ack->tunnel_id);
    pb_client->authenticated = 1;
    snprintf(pb_client->broker_tunnel_id, sizeof(pb_client->broker_tunnel_id), "%s", ack->tunnel_id);

    ZPN_ATOMIC_FETCH_ADD8(&g_pb_client_auth_complete_count, 1);

    c_state = pb_client_to_c_state(pb_client);
    if (c_state) {
        ZPN_LOG(AL_NOTICE, "Client[%s] downstream connection %s,%s with downstream_tunnel_id: %s [%s] connected to upstream public broker %s via upstream_tunnel_id: %s [%s]",
                 (c_state->user_id != NULL) ? c_state->user_id : "unknown",
                 pb_client_client_connection_description(pb_client),
                 zpn_client_type_string(c_state->client_type),
                 c_state->tunnel_id,
                 (pb_client->inbound_client_connection_tlv_type == zpn_fohh_tlv)? "tls" : "dtls",
                 pb_client_broker_conn_description(pb_client),
                 ack->tunnel_id,
                 (pb_client->outbound_tlv_type == zpn_fohh_tlv)? "tls" : "dtls");
    }

    return ZPATH_RESULT_NO_ERROR;
}

static int pb_machine_tunnel_client_authenticate_ack_cb(void *argo_cookie_ptr,
                                                        void *argo_structure_cookie_ptr,
                                                        struct argo_object *object)
{
    struct zpn_machine_tunnel_client_authenticate_ack *ack;
    struct zpn_pb_client *pb_client = argo_structure_cookie_ptr;
    struct zpn_broker_client_fohh_state *c_state;

    if (pb_client_client_connection_incarnation(pb_client) != pb_client->inbound_client_connection_incarnation) {
        ZPN_LOG(AL_NOTICE, "Machine Tunnel Client connection gone, closing.");
        return ZPATH_RESULT_ERR;
    }

    ack = object->base_structure_void;
    if (ack->error) {
        ZPN_LOG(AL_ERROR, "Machine tunnel client could not authenticate with public broker: %s", ack->error);
        return ZPATH_RESULT_ERR;
    }

    ZPN_DEBUG_PBC("Machine tunnel client, Received authenticate, broker = %s, tunnel = %s", ack->current_broker, ack->tunnel_id);
    pb_client->authenticated = 1;
    snprintf(pb_client->broker_tunnel_id, sizeof(pb_client->broker_tunnel_id), "%s", ack->tunnel_id);

    c_state = pb_client_to_c_state(pb_client);
    if (c_state) {
        ZPN_LOG(AL_NOTICE, "Client[%s] downstream connection %s,%s with downstream_tunnel_id: %s [%s] connected to upstream public broker %s via upstream_tunnel_id: %s [%s]",
                (c_state->user_id != NULL) ? c_state->user_id : "unknown",
                 pb_client_client_connection_description(pb_client),
                 zpn_client_type_string(c_state->client_type),
                 c_state->tunnel_id,
                 (pb_client->inbound_client_connection_tlv_type == zpn_fohh_tlv)? "tls" : "dtls",
                 pb_client_broker_conn_description(pb_client),
                 ack->tunnel_id,
                 (pb_client->outbound_tlv_type == zpn_fohh_tlv)? "tls" : "dtls");
    }

    return ZPATH_RESULT_NO_ERROR;
}

/* Returning error will close the connection. We will kill the
 * connection in the close callback. This is just informational. No
 * need to propagate. */
static int pb_client_ec_authenticate_ack_cb(void *argo_cookie_ptr,
                                            void *argo_structure_cookie_ptr,
                                            struct argo_object *object)
{
    struct zpn_ec_client_authenticate_ack *ack;
    struct zpn_pb_client *pb_client = argo_structure_cookie_ptr;
    struct zpn_broker_client_fohh_state *c_state;

    if (pb_client_client_connection_incarnation(pb_client) != pb_client->inbound_client_connection_incarnation) {
        ZPN_LOG(AL_NOTICE, "Client connection gone, closing.");
        return ZPATH_RESULT_ERR;
    }

    ack = object->base_structure_void;
    if (ack->error) {
        ZPN_LOG(AL_ERROR, "Could not authenticate with public broker...");
        return ZPATH_RESULT_ERR;
    }

    ZPN_DEBUG_PBC("Edge/Branch connector, Received authenticate, broker = %s, tunnel = %s", ack->current_broker, ack->tunnel_id);
    pb_client->authenticated = 1;
    snprintf(pb_client->broker_tunnel_id, sizeof(pb_client->broker_tunnel_id), "%s", ack->tunnel_id);

    c_state = pb_client_to_c_state(pb_client);
    if (c_state) {
        ZPN_LOG(AL_NOTICE, "Client[%s] downstream connection %s,%s with downstream_tunnel_id: %s [%s] connected to upstream public broker %s via upstream_tunnel_id: %s [%s]",
                (c_state->user_id != NULL) ? c_state->user_id : "unknown",
                 pb_client_client_connection_description(pb_client),
                 zpn_client_type_string(c_state->client_type),
                 c_state->tunnel_id,
                 (pb_client->inbound_client_connection_tlv_type == zpn_fohh_tlv)? "tls" : "dtls",
                 pb_client_broker_conn_description(pb_client),
                 ack->tunnel_id,
                 (pb_client->outbound_tlv_type == zpn_fohh_tlv)? "tls" : "dtls");
    }

    return ZPATH_RESULT_NO_ERROR;
}

static int pb_mtunnel_request_ack_cb(void *argo_cookie_ptr,
                                    void *argo_structure_cookie_ptr,
                                    struct argo_object *object)
{
    /* Need to get a handle on the mtunnel represented by this ack,
     * and get the inbound mconn data (from client side) so that we
     * can forward the proper request on to the client */
    struct zpn_mtunnel_request_ack *ack = object->base_structure_void;
    struct zpn_pb_client *pb_client = argo_structure_cookie_ptr;
    struct zpn_broker_mtunnel *mtunnel = NULL;
    struct zpn_tlv *tlv = NULL;
    int res;
    int64_t reauth_s = 0;

    if (pb_client_client_connection_incarnation(pb_client) != pb_client->inbound_client_connection_incarnation) {
        ZPN_DEBUG_MTUNNEL("Received mtunnel_request_ack, but pb client connection has changed");
        return ZPATH_RESULT_NO_ERROR;
    }

    tlv = pb_client_to_c_state_tlv(pb_client);

    if (pb_client->outbound_tlv_type == zpn_fohh_tlv) {
        struct zpn_fohh_client *fohh_client = pb_client->outbound_pb_to_b_client;

        mtunnel = zpn_fohh_tlv_get_global_owner(&(fohh_client->fohh_tlv_state), ack->tag_id);
    } else if (pb_client->outbound_tlv_type == zpn_zrdt_tlv) {
        struct zpn_zrdt_client *zrdt_client = pb_client->outbound_pb_to_b_client_zrdt;

        if (zrdt_client) {
            mtunnel = zpn_zrdt_tlv_get_global_owner(&(zrdt_client->zrdt_tlv_state), ack->tag_id);
        } else {
            ZPN_DEBUG_MTUNNEL("Received mtunnel_request_ack, but ZRDT from pb to b is gone");
        }
    }
    if (!mtunnel) {
        ZPN_DEBUG_MTUNNEL("%s: Broker request ack received for mtunnel tag_id = %d that isn't found", zpn_tlv_description(tlv), (int)ack->tag_id);
        return ZPN_RESULT_NO_ERROR;
    }
    if (mtunnel->state >= zbms_reaping) {
        /* It is already being freed, so return */
        return ZPN_RESULT_NO_ERROR;
    }

    if (zpn_debug_get(ZPN_DEBUG_MTUNNEL_IDX)) {
        char out[2000];
        if (argo_object_dump(object, out, sizeof(out), NULL, 0) == ARGO_RESULT_NO_ERROR) {
            ZPN_DEBUG_MTUNNEL("%s: Rx: %s", pb_client_broker_conn_description(pb_client), out);
        } else {
            ZPN_DEBUG_MTUNNEL("%s: tag %d Rx from broker mtunnel request_ack: %s, mtunnel=%s, forwarding to client on tag %d",
                              mtunnel->mtunnel_id,
                              (int)ack->tag_id,
                              ack->error ? ack->error : "<null>",
                              ack->mtunnel_id,
                              (int)mtunnel->log.c_tag);
        }
    }

    reauth_s = mtunnel->reauth_timeout;
    res = zpn_send_zpn_mtunnel_request_ack(tlv,
                                           pb_client->inbound_client_connection_incarnation,
                                           reauth_s,
                                           mtunnel->log.c_tag,
                                           0,
                                           0,
                                           NULL,
                                           NULL,
                                           mtunnel->mtunnel_id,
                                           ack->error,
                                           ack->reason,
                                           NULL);
    if (res) {
        ZPN_LOG(AL_ERROR, "%s: sending request_ack returned %s", mtunnel->mtunnel_id, zpn_result_string(res));
    }else{
        ZPN_DEBUG_MTUNNEL("zpn_send_zpn_mtunnel_request_ack: sending reauth timeout:%"PRIu64" for mtunnel:%s", reauth_s,  mtunnel->mtunnel_id);
    }
    return ZPN_RESULT_NO_ERROR;
}

static int pb_mtunnel_end_cb(void *argo_cookie_ptr,
                             void *argo_structure_cookie_ptr,
                             struct argo_object *object)
{
    /* Need to get a handle on the mtunnel represented by this ack,
     * and get the inbound mconn data (from client side) so that we
     * can forward the proper request on to the client */
    struct zpn_mtunnel_end *req = object->base_structure_void;
    struct zpn_pb_client *pb_client = argo_structure_cookie_ptr;
    struct zpn_tlv *tlv = NULL;
    struct zpn_broker_mtunnel *mtunnel = NULL;
    size_t mtunnel_id_len;
    uint64_t mtunnel_id_hash;
    struct zpn_mconn *client_mconn;
    struct zpn_mconn *assistant_mconn;
    int bucket_id = -1;

    if (pb_client_client_connection_incarnation(pb_client) != pb_client->inbound_client_connection_incarnation) {
        ZPN_LOG(AL_NOTICE, "Client connection gone, closing.");
        return ZPATH_RESULT_ERR;
    }

    if (!req->mtunnel_id && !req->tag_id) {
        ZPN_LOG(AL_ERROR, "Expecting mtunnel_id or tag_id in mtunnel_end request");
        return ZPN_RESULT_ERR;
    }

    tlv = pb_client_to_c_state_tlv(pb_client);

    if (req->mtunnel_id) {
        mtunnel_id_len = strlen(req->mtunnel_id);
        mtunnel_id_hash = CityHash64(req->mtunnel_id, mtunnel_id_len);
        mtunnel = mtunnel_lookup_and_bucket_lock(req->mtunnel_id, mtunnel_id_hash, &bucket_id);
        if (!mtunnel) {
            ZPN_LOG(AL_NOTICE, "%s: Mtunnel_end received for mtunnel that isn't found", req->mtunnel_id);
            return ZPN_RESULT_NO_ERROR;
        }
    } else {
        if (pb_client->outbound_tlv_type == zpn_fohh_tlv) {
            struct zpn_fohh_client *fohh_client = pb_client->outbound_pb_to_b_client;
            mtunnel = zpn_fohh_tlv_get_global_owner(&(fohh_client->fohh_tlv_state), req->tag_id);
        } else if (pb_client->outbound_tlv_type == zpn_zrdt_tlv) {
            struct zpn_zrdt_client *zrdt_client = pb_client->outbound_pb_to_b_client_zrdt;

            if (zrdt_client) {
                mtunnel = zpn_zrdt_tlv_get_global_owner(&(zrdt_client->zrdt_tlv_state), req->tag_id);
            } else {
                ZPN_DEBUG_MTUNNEL("Received mtunnel_end, but ZRDT from pb to b is gone");
            }
        }
        if (!mtunnel) {
            ZPN_DEBUG_MTUNNEL("%s: Broker request req received for mtunnel tag_id = %d that isn't found", zpn_tlv_description(tlv), (int)req->tag_id);
            return ZPN_RESULT_NO_ERROR;
        }
        if (mtunnel->state >= zbms_reaping) {
            /* It is already being freed, so return */
            return ZPN_RESULT_NO_ERROR;
        }
        mtunnel_bucket_lock(mtunnel, &bucket_id);
    }
    if (zpn_debug_get(ZPN_DEBUG_MTUNNEL_IDX)) {
        char out[2000];
        if (argo_object_dump(object, out, sizeof(out), NULL, 0) == ARGO_RESULT_NO_ERROR) {
            ZPN_DEBUG_MTUNNEL("%s: Rx: %s", pb_client_broker_conn_description(pb_client), out);
        } else {
            ZPN_DEBUG_MTUNNEL("%s: tag %d (mtunnel %s) Rx from broker mtunnel end (%s), forwarding to client on tag %d, zins_insp_res=%d",
                              mtunnel->mtunnel_id,
                              (int)mtunnel->log.a_tag,
                              req->mtunnel_id,
                              req->error ? req->error : "<null>",
                              (int)mtunnel->log.c_tag,
                              req->zia_inspection_res);
        }
    }

    mtunnel_lock(mtunnel);

    client_mconn = zpn_broker_mtunnel_client_mconn(mtunnel);
    assistant_mconn = zpn_broker_mtunnel_assistant_mconn(mtunnel);

    if (mtunnel->zpn_broker_mtunnel_type == zbmt_transit_c2c) {
        // this tunnel is created as part of c2c recv txn
        // the mtunnel_end should be forwarded to asisstant , not client
        client_mconn->fin_rcvd = 1;
        if (!client_mconn->fin_rcvd_us)
            client_mconn->fin_rcvd_us = epoch_us();
        if (req->drop_data) {
            client_mconn->drop_tx = req->drop_data;
        }

        zpn_mconn_forward_mtunnel_end(assistant_mconn, req->error, req->drop_data);
    } else {
        assistant_mconn->fin_rcvd = 1;
        if (!assistant_mconn->fin_rcvd_us)
            assistant_mconn->fin_rcvd_us = epoch_us();
        if (req->drop_data) {
            assistant_mconn->drop_tx = req->drop_data;
        }

        zpn_mconn_forward_mtunnel_end(client_mconn, req->error, req->drop_data);
    }
    mtunnel_unlock(mtunnel);
    mtunnel_bucket_unlock(bucket_id);

    return ZPN_RESULT_NO_ERROR;
}

/*
* recv txh log data from parent and it updates the mtunnel log.
* this broker mtunnel log is TERM
*/
static int zpn_c2c_pbroker_zpn_mtunnel_stats_cb(void *argo_cookie_ptr,
                                                  void *argo_structure_cookie_ptr,
                                                  struct argo_object *object) {
    struct zpn_pb_client *pb_client = argo_structure_cookie_ptr;
    struct zpn_mtunnel_stats *msg = object->base_structure_void;
    struct zpn_tlv *tlv = pb_client_to_c_state_tlv(pb_client);
    struct zpn_broker_mtunnel *mtunnel = NULL;

    if (zpn_debug_get(ZPN_DEBUG_C2C_IDX)) {
        char dump[5000];
        if (argo_object_dump(object, dump, sizeof(dump), NULL, 0) == ARGO_RESULT_NO_ERROR) {
            ZPN_LOG(AL_DEBUG, "Rx: %s", dump);
        }
    }

    // 1. find the tunnel
    if (pb_client_client_connection_incarnation(pb_client) != pb_client->inbound_client_connection_incarnation) {
        ZPN_LOG(AL_NOTICE, "Client connection gone, closing.");
        return ZPATH_RESULT_ERR;
    }

    if (pb_client->outbound_tlv_type == zpn_fohh_tlv) {
        struct zpn_fohh_client *fohh_client = pb_client->outbound_pb_to_b_client;

        mtunnel = zpn_fohh_tlv_get_global_owner(&(fohh_client->fohh_tlv_state), msg->tag_id);
    } else if (pb_client->outbound_tlv_type == zpn_zrdt_tlv) {
        struct zpn_zrdt_client *zrdt_client = pb_client->outbound_pb_to_b_client_zrdt;

        if (zrdt_client) {
            mtunnel = zpn_zrdt_tlv_get_global_owner(&(zrdt_client->zrdt_tlv_state), msg->tag_id);
        } else {
            ZPN_DEBUG_MTUNNEL("Received zpn_mtunnel_stats, but ZRDT from pb to b is gone");
        }
    }
    if (!mtunnel) {
        ZPN_DEBUG_MTUNNEL("%s: zpn_mtunnel_stats received for mtunnel tag_id = %d that isn't found",
                          zpn_tlv_description(tlv), (int)msg->tag_id);
        return ZPN_RESULT_NO_ERROR;
    }

    mtunnel_lock(mtunnel);
    if (mtunnel->state >= zbms_reaping) {
        /* It is already being freed, so return */

    } else {
        if (msg->error_code && strcmp(msg->error_code, c2c_result_string(C2C_TRANSIT_MTUNNEL_NOT_FOUND, C2C_SUCCESS_FULL)) == 0) {
            /*
             * This mtunnel is not present on origin broker, close this transit mtunnel without logging in txn log.
             *
             * When the mtunnel got closed on origin broker - it mush have logged it. So we've log for this txn.
             *
             * Scenario: For timeout re-dispatch to diff DC feature - say the app access was successful because of
             * dsp A and mtunnel got closed. Now brk req came on term broker because of dsp B and it sends transit
             * req to origin broker but by the time mtunnel is already destroyed, so origin broker returns
             * C2C_TRANSIT_MTUNNEL_NOT_FOUND error. We don't need to generate txn log for this, else customer might
             * get confused after seeing error txn log for successful app access.
             */
            mtunnel->no_more_logs = 1;
        }
        zpn_broker_mtunnel_log_transit_update(mtunnel, msg, zpn_trans_hop_type_term);
        if (msg->error_code &&
            (strcmp(msg->error_code, c2c_result_string(C2C_TRANSIT_INCORRECT_BROKER, C2C_SUCCESS_FULL)) == 0 ||
            strcmp(msg->error_code, c2c_result_string(C2C_TRANSIT_TLV_ERROR, C2C_SUCCESS_FULL)) == 0 ||
            strcmp(msg->error_code, c2c_result_string(C2C_TRANSIT_MTUNNEL_NOT_FOUND, C2C_SUCCESS_FULL)) == 0 ||
            strcmp(msg->error_code, c2c_result_string(C2C_TRANSIT_OWNER_ERROR, C2C_SUCCESS_FULL)) == 0)) {
            mtunnel_locked_destroy(mtunnel, 1, 0, 0, BRK_MT_SETUP_FAIL_CANNOT_SEND_MT_COMPLETE);
            mtunnel_locked_state_machine(mtunnel);
            mtunnel_unlock(mtunnel);
            return ZPN_RESULT_NO_ERROR;
        }
        zpn_broker_mtunnel_log(mtunnel);
    }

    mtunnel_unlock(mtunnel);

    return ZPN_RESULT_NO_ERROR;
}

/*
* update PB after notification from parent Broker
*/
static int zpn_pbroker_app_registration_notification_cb(void *argo_cookie_ptr,
                                                        void *argo_structure_cookie_ptr,
                                                        struct argo_object *object) {
    // the  ntofication message from parent broker
    // client is already recieved one

    return ZPN_RESULT_NO_ERROR;
}

static int zpn_pbroker_broker_data_conn_redirect(void *argo_cookie_ptr,
                                            void *argo_structure_cookie_ptr,
                                            struct argo_object *object)
{
    struct zpn_pb_client *pb_client = argo_structure_cookie_ptr;
    struct fohh_connection *outbound_f_conn = zpn_fohh_client_get_f_conn(pb_client->outbound_pb_to_b_client);

    if(pb_client_broker_conn_is_connected(pb_client)) {
        /* zpn_private_broker_handle_redirect_request requires the second argument to be a fohh_connection* pointer */
        zpn_private_broker_handle_redirect_request(argo_cookie_ptr, outbound_f_conn, object);
    }

    return ZPN_RESULT_NO_ERROR;
}

/* This behaves much like a connection callback... */
int
zpn_pb_client_status_cb(struct zpn_fohh_client *zfc,
                        void *cookie_void,
                        int64_t cookie_int,
                        enum zfc_status status,
                        const char *error_string)
{
    int res;
    struct zpn_pb_client *pb_client = cookie_void;
    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();

    if (!pb_client->inbound_client_connection && !pb_client->inbound_client_connection_zrdt) {
        /* Got removed... */
        ZPN_LOG(AL_ERROR, "No client side connection");
        return ZPATH_RESULT_ERR;
    }

    if (pb_client_client_connection_incarnation(pb_client) != cookie_int) {
        ZPN_LOG(AL_NOTICE, "%s: Incarnation mismatch- closing stale connection", pb_client_client_connection_description(pb_client));
        return ZPATH_RESULT_ERR;
    }

    struct zpn_broker_client_fohh_state *c_state = pb_client_to_c_state(pb_client);
    if (!c_state) {
        ZPN_LOG(AL_ERROR, "%s: No c_state", pb_client_client_connection_description(pb_client));
        return ZPATH_RESULT_ERR;
    }

    ZPN_LOG(AL_INFO, "%s: pb_client: %p, tunnel_id: %s, status: %s, instance: %ld",
            __func__, pb_client, c_state->tunnel_id, zfc_status_string(status), (long)cookie_int);

    if (status == zfc_ready) {
        /* Register all our callbacks for authentication, tunnels, etc */
        struct fohh_connection *f_conn_to_broker = zpn_fohh_client_get_f_conn(zfc);
        int fohh_thread_id = 0;

        if (f_conn_to_broker) {
            /* We will support directing data connections also */
            zpn_private_broker_send_fohh_info_with_alt_cloud_capability(f_conn_to_broker);

            struct argo_state *argo;
            argo = fohh_argo_get_rx(f_conn_to_broker);

            fohh_set_rx_data_timeout_s(f_conn_to_broker, ZPN_PB_CLIENT_RX_TIMEOUT_S);

            if ((c_state->client_type == zpn_client_type_zapp) || (c_state->client_type == zpn_client_type_zapp_partner)) {
                if ((res = argo_register_structure(argo, zpn_client_authenticate_ack_description, pb_client_authenticate_ack_cb, pb_client))) {
                    ZPN_LOG(AL_ERROR, "Could not register zc_authenticate_ack for zc_client for %s", ZFC_DBG(zfc));
                    return res;
                }
            } else if (c_state->client_type == zpn_client_type_machine_tunnel) {
                if ((res = argo_register_structure(argo, zpn_machine_tunnel_client_authenticate_ack_description, pb_machine_tunnel_client_authenticate_ack_cb, pb_client))) {
                    ZPN_LOG(AL_ERROR, "Could not register zc_authenticate_ack for zc_client for %s", ZFC_DBG(zfc));
                    return res;
                }
            } else if (c_state->client_type == zpn_client_type_edge_connector || c_state->client_type == zpn_client_type_branch_connector) {
                if ((res = argo_register_structure(argo, zpn_ec_client_authenticate_ack_description, pb_client_ec_authenticate_ack_cb, pb_client))) {
                    ZPN_LOG(AL_ERROR, "Could not register zpn_ec_client_authenticate_ack for zc_client for %s", ZFC_DBG(zfc));
                    return res;
                }
            }  else if(c_state->client_type == zpn_client_type_vdi) {
                if ((res = argo_register_structure(argo, zpn_vdi_client_authenticate_ack_description, pb_client_vdi_authenticate_ack_cb, pb_client))) {
                    ZPN_LOG(AL_ERROR, "Could not register zpn_vdi_client_authenticate_ack for zc_client for %s", ZFC_DBG(zfc));
                    return res;
                }
            }

            if ((res = argo_register_structure(argo, zpn_mtunnel_end_description, pb_mtunnel_end_cb, pb_client))) {
                ZPN_LOG(AL_ERROR, "Could not register zpn_mtunnel_end for broker connection");
                return res;
            }
            if ((res = argo_register_structure(argo, zpn_mtunnel_request_ack_description, pb_mtunnel_request_ack_cb, pb_client))) {
                ZPN_LOG(AL_ERROR, "Could not register zc_mtunnel_request_ack for zc_client for %s", ZFC_DBG(zfc));
                return res;
            }
            if ((res = argo_register_structure(argo, zpn_broker_redirect_description, zpn_pbroker_broker_data_conn_redirect, pb_client))) {
            	ZPN_LOG(AL_ERROR, "Could not register broker_redirect for pbroker connection %s", ZFC_DBG(zfc));
             	return res;
            }

            if ((res = argo_register_structure(argo,
                                               zpn_client_app_registration_notification_description,
                                               zpn_pbroker_app_registration_notification_cb,
                                               pb_client))) {
                ZPN_LOG(AL_ERROR, "Could not register broker_redirect for pbroker connection %s", ZFC_DBG(zfc));
                return res;
            }

            if ((res = argo_register_structure(argo, zpn_broker_request_description, zpn_c2c_pbroker_broker_request_fohh_cb,
                                               zfc))) {
                ZPN_LOG(AL_ERROR, "Could not register zpn_broker_request for connection %s", ZFC_DBG(zfc));
                return res;
            }

            if ((res = argo_register_structure(argo, zpn_mtunnel_stats_description, zpn_c2c_pbroker_zpn_mtunnel_stats_cb,pb_client))) {
                ZPN_LOG(AL_ERROR, "Could not register zpn_mtunnel_stats for connection %s", ZFC_DBG(zfc));
                return res;
            }

            /* Register zpn_broker_request_ack */
            if ((res = argo_register_structure(argo, zpn_broker_request_ack_description,
                                               bt_broker_request_ack_cb_from_c2c_conn, c_state))) {
                ZPN_LOG(AL_ERROR, "Could not register zpn_broker_request_ack for c2c connection %s", ZFC_DBG(zfc));
                return res;
            }

            if ((res = argo_register_structure(argo, zpn_ipars_reservation_response_description,
                                               zpn_pbroker_ipars_reservation_response_cb, pb_client))) {
                ZPN_LOG(AL_ERROR, "Could not register zpn_ipars_reservation_response for connection %s", ZFC_DBG(zfc));
                return res;
            }

            if ((res = argo_register_structure(argo, zpn_ipars_refresh_request_description,
                                               zpn_pbroker_ipars_refresh_request_cb, pb_client))) {
                ZPN_LOG(AL_ERROR, "Could not register zpn_ipars_refresh_request for connection %s", ZFC_DBG(zfc));
                return res;
            }

            if ((res = argo_register_structure(argo, zpn_ipars_change_notification_description,
                                               zpn_pbroker_ipars_change_notification_cb, pb_client))) {
                ZPN_LOG(AL_ERROR, "Could not register zpn_ipars_change_notification for connection %s", ZFC_DBG(zfc));
                return res;
            }

            /* Check the client inbound_f_conn state */
            res = zpn_tlv_sanity_check(c_state_get_tlv(c_state));
            if (res) {
                // Client connection went away while its outbound_broker data connection in-progress.
                ZPN_LOG(AL_ERROR, "%s: Could not call back to client, pb_client: %p, tunnel_id: %s, instance: %ld",
                        __func__, pb_client, c_state->tunnel_id, (long) cookie_int);
                return res;
            }

            fohh_thread_id = c_state_get_fohh_thread_id(c_state);

            /* Tell the client to send all saved auth messages (All
             * successive ones are mirrored anyway). Yes this is
             * called on the inbound_f_conn. */
            res = zevent_base_call(fohh_thread_id_zevent_base(fohh_thread_id), zpn_broker_pb_to_b_start_auth, pb_client, cookie_int);
            if (res) {
                /* OUCH */
                ZPN_LOG(AL_ERROR, "%s: Could not call back to client, pb_client: %p, tunnel_id: %s, instance: %ld",
                        __func__, pb_client, c_state->tunnel_id, (long) cookie_int);
                return res;
            }

            pb_client->connected_us = epoch_us();
            strncpy(pb_client->peer_cn, fohh_peer_cn(f_conn_to_broker), strlen(fohh_peer_cn(f_conn_to_broker)));

            res = zevent_base_call(fohh_thread_id_zevent_base(fohh_thread_id),
                                   zpn_pb_forward_client_to_client_app_registration, pb_client, cookie_int);
            if (res) {
                ZPN_LOG(AL_ERROR, "zpn_pb_forward_client_to_client_app_registration failed %s", zpn_result_string(res));
            }

            if (ZPN_BROKER_IS_PRIVATE()) {
                zpn_pbroker_data_connection_stats_update_brk_state_hash(pb_client, 1, 0);
            }

            if (!zpn_is_dr_mode_active() && !c_state->state_ddil_on) {
                zpn_broker_ipars_send_initial_reservation_request(c_state);
            }

            ZPN_ATOMIC_FETCH_ADD8(&g_pb_client_connect_count, 1);
        } else {
            ZPN_LOG(AL_ERROR, "%s: No connection?", c_state->tunnel_id);
        }
        // conn status to disable if in firedrill mode for pbza and pbmt
        // should this be done only for pbza, pbmt and pbzap sni's ?
        if(ZPN_BROKER_IS_PRIVATE() &&
            gs &&
            (gs->firedrill_status == ZPN_PRIVATE_BROKER_FIREDRILL_ENABLED) &&
            ((c_state->client_type == zpn_client_type_machine_tunnel) ||
            (c_state->client_type == zpn_client_type_zapp) ||
            (c_state->client_type == zpn_client_type_zapp_partner))) {
            ZPN_LOG(AL_ERROR, "%s: pse in firedrill, disable the connection", c_state->tunnel_id);
            fohh_connection_disable_async(pb_client->outbound_pb_to_b_client->conn,
                fohh_connection_incarnation(pb_client->outbound_pb_to_b_client->conn), FOHH_CLOSE_REASON_PRIVATE_BROKER_FIREDRILL);
        }
    } else {
        if (pb_client->connected_us && ZPN_BROKER_IS_PRIVATE()) {
            zpn_broker_ipars_request_cancel_by_c_state(c_state);
            zpn_pbroker_data_connection_stats_update_brk_state_hash(pb_client, 0, 0);
        }
        pb_client->connected_us = 0;
        pb_client->disconnected_us = epoch_us();
        pb_client->authenticated = 0;
        pb_client->broker_tunnel_id[0] = 0;

        ZPN_LOG(AL_INFO, "%s: pb_client: %p, tunnel_id: %s, status: %s - Broker upstream connection state cleared",
                __func__, pb_client, c_state->tunnel_id, zfc_status_string(status));

        ZPN_ATOMIC_FETCH_ADD8(&g_pb_client_disconnect_count, 1);
    }

    if(error_string) {
        ZPN_DEBUG_PBC("%s: Status callback, status = %s, error_string = %s", c_state->tunnel_id, zfc_status_string(status), error_string);
    } else {
        ZPN_DEBUG_PBC("%s: Status callback, status = %s", c_state->tunnel_id, zfc_status_string(status));
    }
    return ZPATH_RESULT_NO_ERROR;
}

int
zpn_pb_client_zrdt_status_cb(struct zpn_zrdt_client *zdc,
                             void *cookie_void,
                             int64_t cookie_int,
                             enum zdc_status status,
                             const char *error_string)
{
    struct zpn_pb_client *pb_client = cookie_void;
    struct zpn_tlv *c_state_tlv = NULL;

    if (!pb_client->inbound_client_connection && !pb_client->inbound_client_connection_zrdt) {
        /* Got removed... */
        ZPN_LOG(AL_ERROR, "No client side connection");
        return ZPATH_RESULT_ERR;
    }

    if (pb_client_client_connection_incarnation(pb_client) != cookie_int) {
        ZPN_LOG(AL_NOTICE, "%s: Incarnation mismatch- closing stale connection", pb_client_client_connection_description(pb_client));
        return ZPATH_RESULT_ERR;
    }

    struct zpn_broker_client_fohh_state *c_state = pb_client_to_c_state(pb_client);
    if (!c_state) {
        ZPN_LOG(AL_ERROR, "%s: No c_state", pb_client_client_connection_description(pb_client));
        return ZPATH_RESULT_ERR;
    }

    c_state_tlv = c_state_get_tlv(c_state);

    if (status == zdc_ready) {
        ZPN_LOG(AL_DEBUG, "%s: PB client is connected to Broker through DTLS on behalf of %s", zpn_zrdt_client_description(zdc), c_state_tlv ? zpn_tlv_description(c_state_tlv) : "Unknown");

        struct zpn_zrdt_argo_state *argo_state;
        struct argo_state *argo;
        int res;
        struct zpn_tlv *tlv = &(pb_client->outbound_pb_to_b_client_zrdt->zrdt_tlv_state.tlv);

        pb_client->connected_us = epoch_us();
        strncpy(pb_client->peer_cn, zpn_tlv_peer_cn(tlv), strlen(zpn_tlv_peer_cn(tlv)));

        argo_state = zrdt_get_msg_codec_state(zdc->zrdt_tlv_state.msg_stream);
        argo = argo_state->rx_argo;

        if ((c_state->client_type == zpn_client_type_zapp) || (c_state->client_type == zpn_client_type_zapp_partner)) {
            if ((res = argo_register_structure(argo, zpn_client_authenticate_ack_description, pb_client_authenticate_ack_cb, pb_client))) {
                ZPN_LOG(AL_ERROR, "Could not register zc_authenticate_ack for zdc_client for %s", ZDC_DBG(zdc));
                return res;
            }
        } else if (c_state->client_type == zpn_client_type_machine_tunnel) {
            if ((res = argo_register_structure(argo, zpn_client_authenticate_ack_description, pb_machine_tunnel_client_authenticate_ack_cb, pb_client))) {
                ZPN_LOG(AL_ERROR, "Could not register zc_authenticate_ack for zdc_client for %s", ZDC_DBG(zdc));
                return res;
            }
        } else if (c_state->client_type == zpn_client_type_edge_connector || c_state->client_type == zpn_client_type_branch_connector ) {
            if ((res = argo_register_structure(argo, zpn_ec_client_authenticate_ack_description, pb_client_ec_authenticate_ack_cb, pb_client))) {
                ZPN_LOG(AL_ERROR, "Could not register zpn_ec_client_authenticate_ack for zdc_client for %s", ZDC_DBG(zdc));
                return res;
            }
        } else if (c_state->client_type == zpn_client_type_vdi) {
            if ((res = argo_register_structure(argo, zpn_vdi_client_authenticate_ack_description, pb_client_vdi_authenticate_ack_cb, pb_client))) {
                ZPN_LOG(AL_ERROR, "Could not register zpn_vdi_client_authenticate_ack for zdc_client for %s", ZDC_DBG(zdc));
                return res;
            }
        }

        if ((res = argo_register_structure(argo, zpn_mtunnel_end_description, pb_mtunnel_end_cb, pb_client))) {
            ZPN_LOG(AL_ERROR, "Could not register zpn_mtunnel_end for broker connection");
            return res;
        }
        if ((res = argo_register_structure(argo, zpn_mtunnel_request_ack_description, pb_mtunnel_request_ack_cb, pb_client))) {
            ZPN_LOG(AL_ERROR, "Could not register zc_mtunnel_request_ack for zdc_client for %s", ZDC_DBG(zdc));
            return res;
        }

        if ((res = argo_register_structure(argo,
                                           zpn_client_app_registration_notification_description,
                                           zpn_pbroker_app_registration_notification_cb,
                                           pb_client))) {
            ZPN_LOG(AL_ERROR, "Could not register broker_redirect for pbroker connection %s", ZFC_DBG(zdc));
            return res;
        }

        if ((res = argo_register_structure(argo, zpn_broker_request_description, zpn_c2c_pbroker_broker_request_zrdt_cb,
                                           zdc))) {
            ZPN_LOG(AL_ERROR, "Could not register zpn_broker_request for connection %s", ZFC_DBG(zdc));
            return res;
        }

        if ((res = argo_register_structure(argo, zpn_mtunnel_stats_description,
                                           zpn_c2c_pbroker_zpn_mtunnel_stats_cb, pb_client))) {
            ZPN_LOG(AL_ERROR, "Could not register zpn_mtunnel_stats for connection %s", ZFC_DBG(zdc));
            return res;
        }

        /* Register zpn_broker_request_ack */
        if ((res = argo_register_structure(argo, zpn_broker_request_ack_description,
                                           bt_broker_request_ack_cb_from_c2c_conn, c_state))) {
            ZPN_LOG(AL_ERROR, "Could not register zpn_broker_request_ack for c2c connection %s", ZFC_DBG(zdc));
            return res;
        }

        /* Tell the client to send all saved auth messages (All successive ones are mirrored anyway) */
        res = zevent_base_call(fohh_thread_id_zevent_base(c_state_get_fohh_thread_id(c_state)), zpn_broker_pb_to_b_start_auth, pb_client, cookie_int);
        if (res) {
            /* OUCH */
            ZPN_LOG(AL_ERROR, "Could not call back to client.");
            return res;
        }

        res = zevent_base_call(fohh_thread_id_zevent_base(c_state_get_fohh_thread_id(c_state)),
                               zpn_pb_forward_client_to_client_app_registration, pb_client, cookie_int);
        if (res) {
            ZPN_LOG(AL_ERROR, "zpn_pb_forward_client_to_client_app_registration failed %s", zpn_result_string(res));
        }
        if (ZPN_BROKER_IS_PRIVATE()) {
            zpn_pbroker_data_connection_stats_update_brk_state_hash(pb_client, 1, 0);
        }

    } else {
        if (pb_client->connected_us) {
            ZPN_LOG(AL_DEBUG, "%s: PB client is disconnected to Broker through DTLS on behalf of %s", zpn_zrdt_client_description(zdc), c_state_tlv ? zpn_tlv_description(c_state_tlv) : "Unknown");
            if (ZPN_BROKER_IS_PRIVATE()) {
                zpn_pbroker_data_connection_stats_update_brk_state_hash(pb_client, 0, 1);
            }
        }
        pb_client->connected_us = 0;
        pb_client->disconnected_us = epoch_us();
        pb_client->broker_tunnel_id[0] = 0;
        pb_client->authenticated = 0;
    }

    if(error_string) {
        ZPN_DEBUG_PBC("%s: Status callback, status = %s, error_string = %s", c_state->tunnel_id, zdc_status_string(status), error_string);
    } else {
        ZPN_DEBUG_PBC("%s: Status callback, status = %s", c_state->tunnel_id, zdc_status_string(status));
    }

    return ZPN_RESULT_NO_ERROR;
}


struct zpn_pb_client *zpn_pb_client_create(struct zpn_broker_client_fohh_state *c_state, enum zpn_tlv_type tlv_type, char *peer_cn)
{
    struct zpn_pb_client *pb_client;
    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();
    SSL_CTX *self_to_broker_ssl_ctx = zpn_private_broker_get_self_to_broker_ssl_ctx();
    SSL_CTX *self_to_broker_dtls_ctx = zpn_private_broker_get_self_to_broker_dtls_ctx();
    char domain[256] = {0};
    char sni[256] = {0};

    if ( g_config_pb_client_no_create ) {
        ZPN_ATOMIC_FETCH_ADD8(&g_pb_client_no_create_count,1);
        return NULL;
    }

    if (!c_state) {
        ZPN_LOG(AL_ERROR, "No c_state");
        return NULL;
    }

    if (!c_state->auth_complete) {
        ZPN_LOG(AL_ERROR, "%s: Not authenticated", c_state->tunnel_id);
        return NULL;
    }

    if (is_pbroker_running_drmode()) {
        // PSE running in DR mode. Do not try connecting broker.
        return NULL;
    }

    if ((tlv_type == zpn_fohh_tlv) && c_state->public_broker_client) {
        ZPN_LOG(AL_ERROR, "%s: Already have public broker client", c_state->tunnel_id);
        return NULL;
    }

    if ((tlv_type == zpn_zrdt_tlv) && c_state->public_broker_client_zrdt) {
        ZPN_LOG(AL_ERROR, "%s: Already have public broker client for zrdt", c_state->tunnel_id);
        return NULL;
    }

    if (!self_to_broker_ssl_ctx) {
        ZPN_LOG(AL_ERROR, "%s: Global self to broker ssl_ctx is null", c_state->tunnel_id);
        return NULL;
    }

    ZPN_DEBUG_PBC("%s: Creating pb_client", c_state->tunnel_id);

    pb_client = ZPN_CALLOC(sizeof(*pb_client));
    if(!pb_client) {
       ZPN_LOG(AL_ERROR, "Unable to alloc pb_client associated with tunnel %s", c_state->tunnel_id);
       return NULL;
    }

    ZPN_LOG(AL_INFO, "%s: Created pb_client: %p, tunnel_id: %s", __func__, pb_client, c_state->tunnel_id);

    pb_client->inbound_client_connection_tlv_type = c_state->tlv_type;

    if (c_state->tlv_type == zpn_fohh_tlv) {
        struct fohh_connection *inbound_f_conn = zpn_mconn_fohh_tlv_get_conn(&(c_state->tlv_state));

        pb_client->inbound_client_connection = inbound_f_conn;
        pb_client->inbound_client_connection_incarnation = fohh_connection_incarnation(inbound_f_conn);
        pb_client->inbound_client_connection_zrdt = NULL;
    } else {
        /* ZRDT conn is created only after dtls session is established */
        struct zrdt_conn *inbound_z_conn = zpn_mconn_zrdt_tlv_get_conn(&(c_state->zrdt_tlv_state));

        pb_client->inbound_client_connection = NULL;
        pb_client->inbound_client_connection_zrdt = inbound_z_conn;
        pb_client->inbound_client_connection_incarnation = zrdt_conn_incarnation(inbound_z_conn);
    }

    snprintf(domain, sizeof(domain), "pb2br.%s", pbroker_get_redir_cloud_name());

    if (c_state->client_type == zpn_client_type_edge_connector) {
        snprintf(sni, sizeof(sni), "%ld.pbec.%s", (long) c_state->customer_gid, pbroker_get_redir_cloud_name());
    } else if (c_state->client_type == zpn_client_type_branch_connector) {
        snprintf(sni, sizeof(sni), "%ld.pbbc.%s", (long) c_state->customer_gid, pbroker_get_redir_cloud_name());
    } else if (c_state->client_type == zpn_client_type_vdi) {
        if(c_state->auth_type == zpn_tunnel_auth_znf) {
            snprintf(sni, sizeof(sni), "%ld.pb_ec_vdi.%s", (long) c_state->customer_gid, pbroker_get_redir_cloud_name());
        } else if (c_state->auth_type == zpn_tunnel_auth_branch_connector) {
            snprintf(sni, sizeof(sni), "%ld.pb_bc_vdi.%s", (long) c_state->customer_gid, pbroker_get_redir_cloud_name());
        }
    } else if (c_state->client_type == zpn_client_type_zapp) {
        snprintf(sni, sizeof(sni), "%ld.pbza.%s", (long) c_state->customer_gid, pbroker_get_redir_cloud_name());
    } else if (c_state->client_type == zpn_client_type_zapp_partner) {
        snprintf(sni, sizeof(sni), "%ld.pbzap.%s", (long) c_state->customer_gid, pbroker_get_redir_cloud_name());
    } else if (c_state->client_type == zpn_client_type_machine_tunnel) {
        snprintf(sni, sizeof(sni), "%ld.pbmt.%s", (long) c_state->customer_gid, pbroker_get_redir_cloud_name());
    } else {
        ZPN_LOG(AL_ERROR, "%s: Bad client type for creating connection to broker: %d", c_state->tunnel_id, c_state->client_type);
        goto fail_free;
    }

    pb_client->outbound_tlv_type = tlv_type;
    if (tlv_type == zpn_fohh_tlv) {
        pb_client->outbound_pb_to_b_client = zpn_fohh_client_simple_init(peer_cn ? peer_cn : domain,
                                                            sni,
                                                            pbroker_get_redir_cloud_name(),
                                                            self_to_broker_ssl_ctx,
                                                            zpn_pb_client_status_cb,
                                                            pb_client,
                                                            pb_client->inbound_client_connection_incarnation);
        if (!pb_client->outbound_pb_to_b_client) {
            ZPN_LOG(AL_ERROR, "%s: Could not create pb_to_b client", c_state->tunnel_id);
            goto fail_free;
        }

        fohh_connection_set_default_sni(pb_client->outbound_pb_to_b_client->conn, gs->cfg_key_cloud);
    } else {
        pb_client->outbound_pb_to_b_client_zrdt = zpn_zrdt_client_simple_init(peer_cn ? peer_cn : domain,
                                                                 sni,
                                                                 self_to_broker_dtls_ctx,
                                                                 zpn_pb_client_zrdt_status_cb,
                                                                 pb_client,
                                                                 pb_client->inbound_client_connection_incarnation,
                                                                 c_state->client_type);
        if (!pb_client->outbound_pb_to_b_client_zrdt) {
            ZPN_LOG(AL_ERROR, "%s: Could not create pb_to_b client for zrdt", c_state->tunnel_id);
            goto fail_free;
        }
    }

    ZPN_ATOMIC_FETCH_ADD8(&g_pb_client_create_count,1);
    return pb_client;

 fail_free:
    if (pb_client) {
        ZPN_FREE(pb_client);
    }
    return NULL;
}

int zpn_pb_connected(struct zpn_pb_client *pb_client)
{
    if (pb_client == NULL) {
        return 0;
    }

    if (pb_client->outbound_tlv_type == zpn_fohh_tlv) {
        struct zpn_fohh_client *fohh_client = pb_client->outbound_pb_to_b_client;

        if (fohh_client && (fohh_client->status == zfc_ready)) {
            ZPN_DEBUG_PBC("%s: pb_client: %p, status: %s is connected", __func__, pb_client, zfc_status_string(fohh_client->status));

            return 1;
        } else {
            ZPN_DEBUG_PBC("%s: pb_client: %p is not connected, status: %s", __func__,
                    pb_client, (fohh_client? zfc_status_string(fohh_client->status): "None"));
        }
    } else {
        struct zpn_zrdt_client *zrdt_client = pb_client->outbound_pb_to_b_client_zrdt;

        if (pb_client->authenticated &&
            zrdt_client &&
            (zrdt_client->status == zdc_ready) &&
            zpn_zrdt_client_connected(zrdt_client)) {
            return 1;
        } else {
            if (zrdt_client) {
                ZPN_DEBUG_PBC("pb_client is not ready, client->authenticated = %d, zrdt_client->status = %s, zrdt connection is %s",
                              pb_client->authenticated,
                              (zrdt_client->status == zdc_ready) ? "ready":"not ready",
                              zpn_zrdt_client_connected(zrdt_client) ? "connected" : "not connected");
            } else {
                ZPN_DEBUG_PBC("pb_client is not ready, client->authenticated = %d", pb_client->authenticated);
            }
        }
    }

    return 0;
}

static int32_t zpn_pb_next_tag_id(struct zpn_pb_client *pb_client)
{
    if (pb_client->outbound_tlv_type == zpn_fohh_tlv) {
        struct zpn_fohh_client *fohh_client = pb_client->outbound_pb_to_b_client;

        if (fohh_client) {
            return zpn_fohh_client_next_tag_id(fohh_client);
        }
    } else {
        struct zpn_zrdt_client *zrdt_client = pb_client->outbound_pb_to_b_client_zrdt;

        if (zrdt_client) {
            return zpn_zrdt_client_next_tag_id(zrdt_client);
        }
    }

    return 0;
}

struct zpn_tlv *zpn_pb_get_tlv(struct zpn_pb_client *pb_client)
{
    if (pb_client->outbound_tlv_type == zpn_fohh_tlv) {
        struct zpn_fohh_client *fohh_client = pb_client->outbound_pb_to_b_client;

        if (fohh_client) {
            return &(fohh_client->fohh_tlv_state.tlv);
        } else {
            if (!zpn_is_dr_mode_active()) {
                ZPN_LOG(AL_NOTICE, "No pb_to_b_client");
            }
        }
    } else {
        struct zpn_zrdt_client *zrdt_client = pb_client->outbound_pb_to_b_client_zrdt;

        if (zrdt_client) {
            return &(zrdt_client->zrdt_tlv_state.tlv);
        } else {
            if (!zpn_is_dr_mode_active()) {
                ZPN_LOG(AL_NOTICE, "No pb_to_b_client_zrdt");
            }
        }
    }

    return NULL;
}

static void zpn_zdx_fill_mtunnel_pb_pipeline_cookie(struct zpn_broker_mtunnel *mtunnel, struct zpn_tlv *tlv) {
    uint16_t downstream_port_ne;

    if (mtunnel->zpn_probe_type == zpn_probe_type_zdx_mtr) {
        if (tlv->type == zpn_fohh_tlv) {
            struct zpn_mconn* assistant_tlv_mconn = &(mtunnel->assistant_tlv.mconn);
            struct zpn_zdx_cookie *zdx_cookie = assistant_tlv_mconn->pipeline_cookie;

            fohh_connection_address_and_port(tlv->conn.f_conn,
                                             &(zdx_cookie->probe_info.downstream_ip), &downstream_port_ne,
                                             NULL, NULL);
            zdx_cookie->probe_info.downstream_port = ntohs(downstream_port_ne);
            zdx_cookie->probe_info.downstream_system_type = zpn_zdx_system_type_broker;
            zdx_cookie->probe_info.argo_structure_cookie_ptr = tlv;
            zdx_cookie->probe_info.conn_incarnation = zpn_tlv_incarnation(tlv);

            /* Set tag_id if not set - Likely use this from log because mtunnel->assistant_tlv is not used for pb */
            if (!zdx_cookie->probe_info.tag_id) {
                zdx_cookie->probe_info.tag_id = mtunnel->log.a_tag;
            }
        } else {
            struct zpn_mconn* server_zrdt_tlv_mconn = &(mtunnel->assistant_tlv_zrdt.mconn);
            struct zpn_zdx_cookie *zdx_zrdt_cookie = server_zrdt_tlv_mconn->pipeline_cookie;

            zdtls_connection_address_and_port(zrdt_conn_get_datagram_tx_cookie(tlv->conn.z_conn),
                                              &(zdx_zrdt_cookie->probe_info.downstream_ip), &downstream_port_ne,
                                              NULL, NULL);

            zdx_zrdt_cookie->probe_info.downstream_port = ntohs(downstream_port_ne);
            zdx_zrdt_cookie->probe_info.downstream_system_type = zpn_zdx_system_type_broker;
            zdx_zrdt_cookie->probe_info.argo_structure_cookie_ptr = tlv;
            zdx_zrdt_cookie->probe_info.conn_incarnation = zpn_tlv_incarnation(tlv);

            /* Set tag_id if not set - Likely use this from log because mtunnel->assistant_tlv is not used for pb */
            if (!zdx_zrdt_cookie->probe_info.tag_id) {
                zdx_zrdt_cookie->probe_info.tag_id = mtunnel->log.a_tag;
            }
        }
    }
}

struct zpn_pb_client *zpn_pb_client_get_pb_client_from_mtunnel(struct zpn_broker_mtunnel *mtunnel) {
    struct zpn_pb_client *pb_client = NULL;
    struct zpn_broker_client_fohh_state *c_state;

    c_state = zpn_broker_mtunnel_client_c_state(mtunnel);
    if (!c_state) {
        ZPN_LOG(AL_ERROR, "%s: No c_state", mtunnel->mtunnel_id);
        return NULL;
    }

    /* Find the pb_client to use
     * This decision is done on the per mtunnel basis.
     * Here we prefer zrdt/dtls connection to public broker, but we can
     * change to fohh connection per next mtunnel request
     */
    if (zpn_broker_is_dtls_enabled_on_broker() &&
        mtunnel->allow_all_xport &&
        c_state->public_broker_client_zrdt && zpn_pb_connected(c_state->public_broker_client_zrdt)) {
        pb_client = c_state->public_broker_client_zrdt;
        ZPN_DEBUG_PBC("%s: Pick ZRDT connection for mtunnel, %s", mtunnel->mtunnel_id, zpn_tlv_description(&(pb_client->outbound_pb_to_b_client_zrdt->zrdt_tlv_state.tlv)));
    } else {
        pb_client = c_state->public_broker_client;
        ZPN_DEBUG_PBC("%s: Pick FOHH connection from mtunnel, %s", mtunnel->mtunnel_id, zpn_tlv_description(&(pb_client->outbound_pb_to_b_client->fohh_tlv_state.tlv)));
    }

    if (!pb_client) {
        ZPN_LOG(AL_ERROR, "%s: No pb_client", mtunnel->mtunnel_id);
        return NULL;
    }

    return pb_client;
}

/*
 * Promote mtunnel to public broker..
 */
int zpn_pb_mtunnel_promote_locked(struct zpn_broker_mtunnel *mtunnel, int bucket_id)
{
    struct zpn_pb_client *pb_client = NULL;
    //struct zrdt_conn *broker_z_conn = NULL;
    struct zpn_broker_client_fohh_state *c_state;
    enum zrdt_stream_type atype = zrdt_stream_buffer;
    enum zrdt_stream_type ctype = zrdt_stream_buffer;
    struct zpn_mconn *client_mconn;
    struct zpn_mconn *assistant_mconn;
    struct zpn_tlv *broker_tlv;
    int res = ZPN_RESULT_NO_ERROR;

    c_state = zpn_broker_mtunnel_client_c_state(mtunnel);
    if (!c_state) {
        ZPN_LOG(AL_ERROR, "%s: No c_state", mtunnel->mtunnel_id);
        return ZPN_RESULT_ERR;
    }

    /* Find the pb_client to use
     * This decision is done on the per mtunnel basis.
     * Here we prefer zrdt/dtls connection to public broker, but we can
     * change to fohh connection per next mtunnel request. :q
     */
    if (zpn_broker_is_dtls_enabled_on_broker() &&
        mtunnel->allow_all_xport &&
        c_state->public_broker_client_zrdt && zpn_pb_connected(c_state->public_broker_client_zrdt)) {
        pb_client = c_state->public_broker_client_zrdt;
        ZPN_DEBUG_PBC("%s: Pick ZRDT connection to promote, %s", mtunnel->mtunnel_id, zpn_tlv_description(&(pb_client->outbound_pb_to_b_client_zrdt->zrdt_tlv_state.tlv)));
    } else if (zpn_pb_connected(c_state->public_broker_client)) {
        pb_client = c_state->public_broker_client;
        ZPN_DEBUG_PBC("%s: Pick FOHH connection to promote, %s", mtunnel->mtunnel_id, zpn_tlv_description(&(pb_client->outbound_pb_to_b_client->fohh_tlv_state.tlv)));
    }

    if (!pb_client) {
        ZPN_LOG(AL_ERROR, "%s: No pb_client to promote the mtunnel", mtunnel->mtunnel_id);
        return ZPN_RESULT_ERR;
    }

    c_state->default_pb_client_tlv_type = pb_client->outbound_tlv_type;

    mtunnel->assistant_tlv_type = pb_client->outbound_tlv_type;

    broker_tlv = zpn_pb_get_tlv(pb_client);
    if (!broker_tlv) {
        ZPN_LOG(AL_ERROR, "%s: No TLV", mtunnel->mtunnel_id);
        return ZPN_RESULT_ERR;
    }

    if (zpn_tlv_sanity_check(broker_tlv) != ZPN_RESULT_NO_ERROR) {
        ZPN_LOG(AL_CRITICAL, "tlv no connection?");
        return ZPN_RESULT_ERR;
    }


    /* BEFORE we connect the mconns we will send the mtunnel
     * request. This ensures in the common case that the mtunnel
     * request arrives before any data. In DTLS this isn't quite as
     * important, but for TLS connections it is required. */
    struct zpn_mtunnel_request_int req = {0};
    req.tag_id = zpn_pb_next_tag_id(pb_client);
    req.app_name = mtunnel->req_app_name;
    req.app_type = mtunnel->log.app_type;
    req.server_port = mtunnel->log.c_service_port;
    req.double_encrypt = mtunnel->double_encrypt;
    req.ip_protocol = mtunnel->ip_protocol;
    req.zpn_probe_type = mtunnel->zpn_probe_type;
    req.o_dwgs = mtunnel->o_dwgs;
    req.o_dwgs_count = mtunnel->o_dwgs_count;

    req.o_user_id = mtunnel->log.o_user_id;
    req.o_location_id = mtunnel->log.o_location_id;
    if (mtunnel->log.o_sip.length) {
        req.o_sip = &(mtunnel->log.o_sip);
    }
    if (mtunnel->log.o_dip.length) {
        req.o_dip = &(mtunnel->log.o_dip);
    }
    req.o_sport = mtunnel->log.o_sport;
    req.o_dport = mtunnel->log.o_dport;

    if( mtunnel->log.o_identity_name ) {
        req.o_identity_name = mtunnel->log.o_identity_name;
    }

    // zia inspection 3.0
    req.o_location_id = mtunnel->o_location_id;
    req.o_user_id = mtunnel->log.o_user_id;
    req.o_identity_name = mtunnel->log.o_identity_name;

    /* Update our own hop just before forwarding, we are origin */
    res = zpn_broker_client_update_self_hop_log_from_self_mtunnel_log(mtunnel, zpn_trans_hop_type_origin);
    if (res) {
        ZPN_LOG(AL_ERROR, "%s: Unable to set current mtunnel hop to local log", mtunnel->mtunnel_id);
    }

    /* Set our orignal mtunnel hop state outgoing mtunnel request to public broker */
    zpn_broker_client_attach_self_hop_log_to_next_hop_mtunnel_request(mtunnel, &req);

    client_mconn = zpn_broker_mtunnel_client_mconn(mtunnel);
    assistant_mconn = zpn_broker_mtunnel_assistant_mconn(mtunnel);

    ZPN_DEBUG_PBC("%s: Promoting to public broker, tlv = %p, mconn = %p, tag = %d", mtunnel->mtunnel_id, broker_tlv, &(mtunnel->assistant_tlv.mconn), (int)req.tag_id);

    res = tlv_argo_serialize(broker_tlv,
                             zpn_mtunnel_request_int_description,
                             &req,
                             0,
                             fohh_queue_element_type_control);
    if (res) {
        mtunnel->log.fwd_to_next_hop = 0;
        ZPN_LOG(AL_ERROR, "%s: Could not send mtunnel request to broker: %s", mtunnel->mtunnel_id, zpn_result_string(res));
        return res;
    }

    mtunnel->log.a_tag = req.tag_id;

    /* Now that we have sent the request, we will immediately attach
     * the mconns for tx/rx. This uses the 'assistant' mconn, which is
     * really just the mconn representing the other side of the
     * connection requested by the client. Most of this code is
     * inspired by the assistant bind code. */
    if (pb_client->outbound_tlv_type == zpn_zrdt_tlv) {
        mtunnel->assistant_tlv_type = zpn_zrdt_tlv;

        if (mtunnel->zpn_probe_type == zpn_probe_type_default && mtunnel->client_tlv_type == zpn_zrdt_tlv) {
            /* Client side has RDT */
            atype = zrdt_stream_transit;
            ctype = zrdt_stream_transit;
        } else {
            atype = zrdt_stream_reliable_endpoint;
            ctype = zrdt_stream_reliable_endpoint;
        }

        res = zrdt_stream_create(zpn_mconn_zrdt_tlv_get_conn(&(pb_client->outbound_pb_to_b_client_zrdt->zrdt_tlv_state)),
                                 &(mtunnel->assistant_tlv_zrdt.stream),
                                 mtunnel->log.a_tag,
                                 atype,
                                 zpn_zrdt_read_cb,
                                 zpn_zrdt_write_cb,
                                 zpn_zrdt_event_cb,
                                 &(pb_client->outbound_pb_to_b_client_zrdt->zrdt_tlv_state));
        if (res) {
            ZPN_LOG(AL_DEBUG, "%s: Could not create stream", mtunnel->mtunnel_id);
            mtunnel_locked_destroy(mtunnel, 1, 0, 0, BRK_MT_SETUP_FAIL_TO_CREATE_STREAM);
            mtunnel_locked_state_machine(mtunnel);
            mtunnel_unlock(mtunnel);
            mtunnel_bucket_unlock(bucket_id);
            return ZPN_RESULT_NO_ERROR;
        } else {
            mtunnel->pse_to_broker_incarnation = pb_client_broker_conn_incarnation(pb_client);
            ZPN_DEBUG_MTUNNEL("%s: Set PSE -> Broker zrdt conn incarnation: %ld for pb_client: %p",
                              mtunnel->mtunnel_id, (long)mtunnel->pse_to_broker_incarnation, pb_client);
        }

        ZPN_DEBUG_MTUNNEL("%s: Created stream a_tag=%d %s", mtunnel->mtunnel_id, mtunnel->log.a_tag, zrdt_stream_type_string(atype));
    } else {
        mtunnel->assistant_tlv_type = zpn_fohh_tlv;
        mtunnel->assistant_f_conn = pb_client->inbound_client_connection;
        mtunnel->pse_to_broker_incarnation = pb_client_broker_conn_incarnation(pb_client);
        ZPN_DEBUG_MTUNNEL("%s: Set PSE -> Broker tls conn incarnation: %ld for pb_client: %p",
                          mtunnel->mtunnel_id, (long)mtunnel->pse_to_broker_incarnation, pb_client);

        if (mtunnel->client_tlv_zrdt.stream) {
            /* Client side has RDT */
            if (mtunnel->ip_protocol == IPPROTO_UDP || mtunnel->ip_protocol == IPPROTO_ICMP) {
                ctype = zrdt_stream_unreliable_endpoint;
            } else {
                ctype = zrdt_stream_reliable_endpoint;
            }
        }
    }

    if (mtunnel->client_tlv_zrdt.stream && (ctype != zrdt_stream_buffer) &&
        zrdt_stream_get_type(mtunnel->client_tlv_zrdt.stream) == zrdt_stream_buffer) {
        (void) zrdt_stream_set_type(mtunnel->client_tlv_zrdt.stream, ctype);
    }

    //fill pb cookie here
    zpn_zdx_fill_mtunnel_pb_pipeline_cookie(mtunnel, broker_tlv);

    zpn_mconn_connect_peer(client_mconn, assistant_mconn);

    zpn_mconn_set_fohh_thread_id(assistant_mconn, zpn_tlv_get_thread_id(broker_tlv));
    res = zpn_mconn_add_local_owner(assistant_mconn,
                                    0,
                                    broker_tlv,
                                    &(mtunnel->log.a_tag), // Must last as long as the mconn!! (Don't use the stack...)
                                    sizeof(mtunnel->log.a_tag),
                                    (broker_tlv->type == zpn_fohh_tlv) ? &zpn_mconn_fohh_tlv_calls : &zpn_mconn_zrdt_tlv_calls);
    if (res) {
        ZPN_LOG(AL_ERROR, "%s: local owner bind failed: %s", mtunnel->mtunnel_id, zpn_result_string(res));
        return res;
    }
    /* Update flow control flag */
    if (broker_tlv->type == zpn_fohh_tlv) {
        struct zpn_fohh_client *fohh_client = pb_client->outbound_pb_to_b_client;
        mtunnel->assistant_tlv.remote_fc_status = fohh_client->fohh_tlv_state.remote_fc_status;
    }

    mtunnel->assistant_id = zpn_tlv_peer_get_id(broker_tlv);
    //mtunnel->application_server_id = req->g_aps;

    ZPN_DEBUG_MTUNNEL("%s: Promoted to broker, Tag %d, %s", mtunnel->mtunnel_id, (int) mtunnel->log.a_tag, zpn_tlv_description(broker_tlv));

    if (mtunnel->assistant_id) {
        struct zpn_assistantgroup_assistant_relation *ag_relation = NULL;
        size_t count = 1;

        zpn_assistantgroup_assistant_relation_get_by_assistant(mtunnel->assistant_id,
                                                               &ag_relation,
                                                               &count,
                                                               NULL,
                                                               NULL,
                                                               0);
        if (ag_relation) {
            mtunnel->assistant_group_id = ag_relation->assistant_group_id;
        }
    }

    mtunnel->state = zbms_complete;
    mtunnel->log.tunnel_us = epoch_us() - mtunnel->log.startrx_us;

    zpn_broker_mtunnel_log(mtunnel);

    /* Mark it 'active' for the NEXT log. First log will be "open" */
    mtunnel->log.action = "active";

    mtunnel_locked_state_machine(mtunnel);


    return res;
}

void zpn_pb_client_mirror(struct zpn_pb_client *pb_client, struct argo_object *object)
{
    struct zpn_tlv *broker_tlv;
    int res;

    if (!pb_client) return;

    broker_tlv = zpn_pb_get_tlv(pb_client);
    if (!broker_tlv) return;

    if (!zpn_tlv_connected(broker_tlv))
        return;

    res = tlv_argo_serialize_object(broker_tlv,
                                    object,
                                    0,
                                    fohh_queue_element_type_mission_critical);
    if (res) {
        ZPN_BROKER_ASSERT_SOFT(0, "zpn_pb_client_mirror: could not serialize object: %s", zpn_result_string(res));
    }
}


static void zpn_pb_client_close_on_thread(struct zevent_base *base, void *void_cookie, int64_t int_cookie)
{
    struct zpn_pb_client *pb_client = void_cookie;

    if (pb_client->outbound_tlv_type == zpn_fohh_tlv) {
        zpn_fohh_client_fini(pb_client->outbound_pb_to_b_client);
        ZPN_ATOMIC_FETCH_ADD8(&g_pb_client_close_fini_count,1);
        pb_client->outbound_pb_to_b_client = NULL;
    } else if (pb_client->outbound_tlv_type == zpn_zrdt_tlv) {
        ZPN_LOG(AL_DEBUG, "%s: PB close connection to broker because client disconnect", zpn_zrdt_client_description(pb_client->outbound_pb_to_b_client_zrdt));
        zpn_zrdt_client_close(pb_client->outbound_pb_to_b_client_zrdt);
        ZPN_ATOMIC_FETCH_ADD8(&g_pb_zrdt_client_close_count,1);
        pb_client->outbound_pb_to_b_client_zrdt = NULL;
    }

    ZPN_FREE_SLOW(pb_client);
}

int zpn_pb_client_close(struct zpn_pb_client *pb_client)
{
    struct zevent_base *zbase = NULL;
    int res = ZPN_RESULT_NO_ERROR;

    if (!pb_client) return ZPN_RESULT_BAD_ARGUMENT;

    if (ZPN_BROKER_IS_PRIVATE()) {
        zpn_pbroker_data_connection_stats_update_brk_state_hash(pb_client, 0, 1);
    }

    if (pb_client->outbound_tlv_type == zpn_fohh_tlv) {
        struct zpn_fohh_client *fohh_client;
        struct fohh_connection *broker_f_conn;

        fohh_client = pb_client->outbound_pb_to_b_client;
        if (!fohh_client) {
            ZPN_LOG(AL_ERROR, "No fohh_client");
            return ZPN_RESULT_BAD_ARGUMENT;
        }

        broker_f_conn = zpn_fohh_client_get_f_conn(fohh_client);
        if (!broker_f_conn) {
            ZPN_LOG(AL_ERROR, "No broker_f_conn");
            return ZPN_RESULT_BAD_ARGUMENT;
        }

        zbase = fohh_connection_zevent_base(broker_f_conn);
    } else if (pb_client->outbound_tlv_type == zpn_zrdt_tlv) {
        struct zpn_zrdt_client *zrdt_client;

        zrdt_client = pb_client->outbound_pb_to_b_client_zrdt;
        if (!zrdt_client) {
            ZPN_LOG(AL_ERROR, "No zrdt_client");
            return ZPN_RESULT_BAD_ARGUMENT;
        }

        zbase = zrdt_client->zbase;
    }
    ZPN_ATOMIC_FETCH_ADD8(&g_pb_client_close_count,1);
    if (zbase) {
        res = zevent_base_call(zbase, zpn_pb_client_close_on_thread, pb_client, 0);
        if (res) {
            ZPN_LOG(AL_ERROR, "Uh oh");
        }
    } else {
        ZPN_LOG(AL_ERROR, "No zbase");
    }
    return res;
}

static void zpn_pb_forward_client_to_client_app_registration(struct zevent_base *base, void *void_cookie, int64_t int_cookie) {

    struct zpn_pb_client *pb_client = void_cookie;

    if (!pb_client) {
        return;
    }

    if (pb_client_client_connection_incarnation(pb_client) != int_cookie) {
        /* Conn went away while async. That's fine */
        return ;
    }

    struct zpn_broker_client_fohh_state *cs = pb_client_to_c_state(pb_client);

    if (!cs) {
        ZPN_LOG(AL_ERROR, "No client state" );
        return;
    }

    if (cs->client_fqdn == NULL) {
        // fqdn is empty, do not forward
        return;
    }

    if (!c2c_is_enabled_on_broker() || !c2c_is_enabled_for_customer(cs->customer_gid)) {
        // c2c feature is disabled
        ZPN_DEBUG_C2C("%s: Stop sending zpn_broker_dispatcher_app_registration as C2C feature is disabled: tunnel_id=%s",
                      cs->client_fqdn, cs->tunnel_id);
        return;
    }

    const char *fqdn[MAX_CLIENT_FQDN] = {0};
    fqdn[0] = cs->client_fqdn;

    struct zpn_broker_dispatcher_app_registration req;
    req.customer_gid = cs->customer_gid;
    req.client_cname = cs->cname;
    req.client_fqdn = fqdn;
    req.fqdn_count = 1;
    req.connect_us = cs->connect_us;
    req.alive = cs->fqdn_registered == FQDN_REGISTERED ? 1 : 0;
    req.g_brk = ZPN_BROKER_GET_GID();
    req.machine_id = cs->hash_hardware_id;

    const int res = zpn_pb_forward_app_registration(pb_client, &req);
    if (res) {
        ZPN_LOG(AL_ERROR, "zpn_pb_forward_app_registration failed %s %s", zpn_result_string(res) , pb_client_client_connection_description(pb_client));
    }
}
/*
 * forwards registration to public broker
 */
int zpn_pb_forward_app_registration(struct zpn_pb_client *pb_client,
                                    struct zpn_broker_dispatcher_app_registration *req) {
    struct zpn_broker_client_fohh_state *cs = pb_client_to_c_state(pb_client);
    struct zpn_tlv *broker_tlv = NULL;

    if (!cs) {
        ZPN_LOG(AL_ERROR, "No client state" );
        return ZPN_RESULT_ERR;
    }

    broker_tlv = zpn_pb_get_tlv(pb_client);
    if (!broker_tlv) {
        ZPN_LOG(AL_ERROR, "%s: No TLV", cs->cname);
        return ZPN_RESULT_ERR;
    }

    if (zpn_tlv_sanity_check(broker_tlv) != ZPN_RESULT_NO_ERROR) {
        ZPN_LOG(AL_CRITICAL, "tlv no connection?");
        return ZPN_RESULT_ERR;
    }

    struct argo_object *obj = argo_object_create(zpn_broker_dispatcher_app_registration_description, req);
    if (!obj) {
        ZPN_LOG(AL_ERROR, "Could not create zpn_client_broker_app_registration argo object");
        return ZPN_RESULT_NO_MEMORY;
    }

    char dump[2048];
    if (argo_object_dump(obj, dump, sizeof(dump), NULL, 0) != ARGO_RESULT_NO_ERROR) {
        dump[0] = '\0';
    }

    const int res = tlv_argo_serialize_object(broker_tlv, obj, 0, fohh_queue_element_type_control);
    if (res) {
        ZPN_LOG(AL_ERROR,
                "%s: Could not send zpn_client_broker_app_registration to broker: %s: %s",
                cs->cname, zpn_result_string(res), dump);
    } else {
        ZPN_DEBUG_C2C("%s: Sent zpn_client_broker_app_registration to broker through %s: %s",
                      cs->cname, zpn_tlv_type_str(broker_tlv->type), dump);
    }

    argo_object_release(obj);

    return res;
}

struct fohh_connection *zpn_pb_client_get_f_conn(struct zpn_pb_client *pb_client)
{
    if (!pb_client) {
        ZPN_LOG(AL_ERROR, "Could not get f_conn, pb_client is NULL");
        return NULL;
    }
    if (!pb_client->outbound_pb_to_b_client) {
        ZPN_LOG(AL_ERROR, "Could not get f_conn, pb_client->outbound_pb_to_b_client is NULL, broker_tunnel_id: %s, "
                "peer_cn: %s, connected_us: %"PRId64", disconnected_us: %"PRId64", deletion_in_progress: %"PRIu32"",
                pb_client->broker_tunnel_id, pb_client->peer_cn, pb_client->connected_us, pb_client->disconnected_us,
                pb_client->deletion_in_progress);
        return NULL;
    }

    return zpn_fohh_client_get_f_conn(pb_client->outbound_pb_to_b_client);
}

struct zrdt_conn *zpn_pb_client_get_z_conn(struct zpn_pb_client *pb_client)
{
    if (!pb_client) {
        ZPN_LOG(AL_ERROR, "Could not get z_conn, pb_client is NULL");
        return NULL;
    }
    if (!pb_client->outbound_pb_to_b_client_zrdt) {
        ZPN_LOG(AL_ERROR, "Could not get z_conn, pb_client->outbound_pb_to_b_client_zrdt is NULL, broker_tunnel_id: %s, "
                "peer_cn: %s, connected_us: %"PRId64", disconnected_us: %"PRId64", deletion_in_progress: %"PRIu32"",
                pb_client->broker_tunnel_id, pb_client->peer_cn, pb_client->connected_us, pb_client->disconnected_us,
                pb_client->deletion_in_progress);
        return NULL;
    }

    return zpn_zrdt_client_get_z_conn(pb_client->outbound_pb_to_b_client_zrdt);
}
