/*
 * zpn_broker_np_client.c. Copyright (C) 2024 Zscaler, Inc. All Rights Reserved
 */
#include <arpa/inet.h>
#include "zpath_lib/zpath_lib.h"
#include "zpn/zpn_broker_np.h"
#include "zpn/zpn_broker_np_client.h"
#include "zpn/zpn_broker_np_client_compiled.h"
#include "zpn/zpn_broker_client.h"
#include "zpath_lib/zpath_config_override_keys.h"
#include "zpath_lib/zpath_config_override.h"
#include "np_lib/np.h"
#include "np_lib/np_lan_subnets.h"
#include "np_lib/np_client_subnets.h"
#include "np_lib/np_dns_ns_records.h"

struct zpath_allocator zpn_broker_np_client_allocator = ZPATH_ALLOCATOR_INIT("zpn_broker_np_client");

static struct zhash_table *customers;
static zpath_rwlock_t customers_lock;

static int64_t np_recalc_push_us = DEFAULT_NP_RECALC_PUSH_US;

TAILQ_HEAD(zpn_client_np_app_state_head, zpn_client_np_app_state);

struct np_customer_state {
    /*
     * Lock on adding/removing clients, and on callbacks for updating
     * state
     */
    zpath_mutex_t lock;

    int64_t customer_gid;

    /*
     * If the timer exists, then we are delaying updates to clients
     * until the timer fires.
     */
    struct event *timer;

    /* The thread on which we do things. (customer functionality is
     * often called from wally/etc. But our timer lives here.. */
    struct zevent_base *calc_thread;

    /* Accumulated count of num of clients enabled with NP
     * app download for this customer
     */
    int64_t current_np_app_download_clients;
    int64_t total_np_app_download_clients;

    /* Used to accumulate subnets/domains that have changed since last distribution */
    struct zhash_table *client_subnets_accumulate;
    struct zhash_table *lan_subnets_accumulate;
    struct zhash_table *dns_ns_records_accumulate; /* only for debugging purposes for now */

    /*
     * All the np clients associated with this customer
     */
    struct zpn_client_np_app_state_head np_clients;

    /* Config overrides */
    int64_t config_keep_alive_timeout_s;
    int64_t config_long_term_expiry_hrs;

    /* Feature flag monitoring */
    uint16_t feature_status_incarnation;
};

struct zpn_client_np_app_state {
    char debug_str[64];
    char app_download_status_str[128];

    /* References to client: */
    int64_t customer_gid;

    /* Reference for the owner (client connection), and each
     * asynchronous callback outstanding. */
    int32_t reference_count;

    int conn_thread_id;
    int64_t c_state_incarnation;

    char tunnel_id[ZPN_TUNNEL_ID_BYTES_TEXT + 1];

    /* More client info: */
    struct zevent_base *client_fohh_thread;

    /* Reference to customer state. Customer never disappears :) */
    struct np_customer_state *customer;

    /* The thread on which we calculate stuff */
    struct zevent_base *calc_thread;

    /* Our entry in the customer client list */
    TAILQ_ENTRY(zpn_client_np_app_state) list;

    struct zhash_table *client_subnets_sent;
    struct zhash_table *lan_subnets_sent;
    zpath_mutex_t client_subnets_sent_lock;
    zpath_mutex_t lan_subnets_sent_lock;

    struct zpn_broker_np_client_app_stats stats;

    uint16_t lan_subnets_initial_download_complete:1,
             client_subnets_initial_download_complete:1,
             domains_initial_download_complete:1,
             is_first_subnet_enqueued:1,
             is_first_client_subnet_enqueued:1,
             is_first_lan_subnet_enqueued:1,
             is_first_domain_enqueued:1,
             is_last_client_subnet_enqueued:1,
             is_last_lan_subnet_enqueued:1,
             is_last_domain_enqueued:1,
             is_cstate_done:1,
             deleted:1,
             spare:4;

    zpn_client_tracker_t *tracker;
};

struct timer_parameter {
    struct np_customer_state *customer;
    int64_t start_time_mono_s;
    uint8_t download_client_subnets:1,
            download_lan_subnets:1,
            download_dns_ns_records:1,
            spare:5;
};

/*
 * Structure filled with app_gids for clients to process...
 */
struct subnet_gids {
    int reference_count;
    int64_t *gids;
    size_t gids_count;
    size_t gids_used;   // Count of entries used.
};

/* this data goes to the stats.log */
static struct zpn_broker_np_client_app_sent_stats zpn_broker_np_client_app_sent_stats[FOHH_MAX_THREADS] = {{0}};

static struct argo_structure_description *zpn_broker_np_client_app_stats_desc = NULL;
static struct argo_structure_description *zpn_broker_np_client_app_customer_log_desc = NULL;
static struct argo_structure_description *zpn_broker_np_client_app_sent_stats_desc = NULL;

static void zpn_broker_np_client_download_client_subnets(struct zpn_client_np_app_state *client, struct subnet_gids *subnet_gids, int64_t is_new_client);
static void zpn_broker_np_client_download_lan_subnets(struct zpn_client_np_app_state *client, struct subnet_gids *subnet_gids, int64_t is_new_client);
static void zpn_broker_np_client_download_dns_ns_records(struct zpn_client_np_app_state *client, int64_t is_new_client);

static void refresh_timer(struct np_customer_state *customer,
                          uint8_t download_client_subnets,
                          uint8_t download_lan_subnets,
                          uint8_t download_dns_ns_records);

/* Returns time in GMT, caller must ensure str is not null */
static char *epoch_to_time_str(int64_t epoch, char *str, int str_len)
{
    if (epoch <= 0) {
        snprintf(str, str_len, "N/A");
    } else {
        argo_log_gen_time(epoch, str, str_len, 0, 1);
    }
    return str;
}

/* Log to np.log
 * This will be triggered from customer timer with 1s suppression, and it will be per customer log
 * so the log volume should be under control.
 * Must be called with customer lock held */
static int zpn_broker_np_client_apps_customer_log(const char *log_name,
                                                  struct np_customer_state *customer,
                                                  uint8_t download_client_subnets,
                                                  uint8_t download_lan_subnets,
                                                  uint8_t download_dns_ns_records)
{
    struct zpn_broker_np_client_app_customer_log log = {0};

    if (!customer) return ZPN_RESULT_ERR;

    log.log_date = epoch_us();
    log.g_cst = customer->customer_gid;
    log.current_np_app_download_clients = customer->current_np_app_download_clients;
    log.total_np_app_download_clients = customer->total_np_app_download_clients;

    if (download_client_subnets && customer->client_subnets_accumulate) {
        log.accumulate_client_subnets_count = zhash_table_get_size(customer->client_subnets_accumulate);
    }
    if (download_lan_subnets && customer->lan_subnets_accumulate) {
        log.accumulate_lan_subnets_count = zhash_table_get_size(customer->lan_subnets_accumulate);
    }
    if (download_dns_ns_records && customer->dns_ns_records_accumulate) {
        log.accumulate_dns_ns_records_count = zhash_table_get_size(customer->dns_ns_records_accumulate);
    }

    return zpn_broker_np_log(log_name, zpn_broker_np_client_app_customer_log_desc, &log);
}

static int64_t zpn_broker_np_init_keep_alive_timeout_s(int64_t customer_gid)
{
    int64_t config_value = NETWORK_PRESENCE_BROKER_KEEP_ALIVE_TIMEOUT_SECONDS_DEFAULT;

    return zpath_config_override_get_config_int(NETWORK_PRESENCE_BROKER_KEEP_ALIVE_TIMEOUT_SECONDS,
                                                &config_value,
                                                NETWORK_PRESENCE_BROKER_KEEP_ALIVE_TIMEOUT_SECONDS_DEFAULT,
                                                customer_gid,
                                                (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                (int64_t)0);
}

static int64_t zpn_broker_np_init_long_term_expiry_hrs(int64_t customer_gid)
{
    int64_t config_value = NETWORK_PRESENCE_LONG_TERM_EXPIRY_HOURS_DEFAULT;

    return zpath_config_override_get_config_int(NETWORK_PRESENCE_LONG_TERM_EXPIRY_HOURS,
                                                &config_value,
                                                NETWORK_PRESENCE_LONG_TERM_EXPIRY_HOURS_DEFAULT,
                                                customer_gid,
                                                (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                (int64_t)0);
}

static struct np_customer_state *get_np_customer_state(int64_t customer_gid)
{
    struct np_customer_state *customer;

    if (customer_gid == 0) {
        ZPN_LOG(AL_ERROR, "Customer_gid == 0 when getting customer state");
        return NULL;
    }

    ZPATH_RWLOCK_RDLOCK(&customers_lock, __FILE__, __LINE__);
    customer = zhash_table_lookup(customers, &customer_gid, sizeof(customer_gid), NULL);
    ZPATH_RWLOCK_UNLOCK(&customers_lock, __FILE__, __LINE__);
    if (!customer) {
        ZPATH_RWLOCK_WRLOCK(&customers_lock, __FILE__, __LINE__);
        /* Repeat lookup because it could have been added before we got lock */
        customer = zhash_table_lookup(customers, &customer_gid, sizeof(customer_gid), NULL);
        if (!customer) {
            if ((customer = ZPN_BNC_CALLOC(sizeof(*customer))) == NULL) {
                ZPN_LOG(AL_ERROR, "No memory when creating customer state");
                ZPATH_RWLOCK_UNLOCK(&customers_lock, __FILE__, __LINE__);
                return NULL;
            }

            customer->lock = ZPATH_MUTEX_INIT;
            customer->customer_gid = customer_gid;
            TAILQ_INIT(&(customer->np_clients));
            customer->client_subnets_accumulate = zhash_table_alloc(&zpn_broker_np_client_allocator);
            customer->lan_subnets_accumulate = zhash_table_alloc(&zpn_broker_np_client_allocator);
            customer->dns_ns_records_accumulate = zhash_table_alloc(&zpn_broker_np_client_allocator);
            customer->calc_thread = zevent_get_for_class(ZEVENT_CLASS_APP_CALC);
            customer->config_keep_alive_timeout_s = zpn_broker_np_init_keep_alive_timeout_s(customer_gid);
            customer->config_long_term_expiry_hrs = zpn_broker_np_init_long_term_expiry_hrs(customer_gid);
            customer->feature_status_incarnation = 0;
            zhash_table_store(customers, &customer_gid, sizeof(customer_gid), 0, customer);

            zpath_config_override_monitor_int(NETWORK_PRESENCE_BROKER_KEEP_ALIVE_TIMEOUT_SECONDS,
                                              &customer->config_keep_alive_timeout_s,
                                              NULL,
                                              NETWORK_PRESENCE_BROKER_KEEP_ALIVE_TIMEOUT_SECONDS_DEFAULT,
                                              customer_gid,
                                              (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                              (int64_t)0);

            zpath_config_override_monitor_int(NETWORK_PRESENCE_LONG_TERM_EXPIRY_HOURS,
                                              &customer->config_long_term_expiry_hrs,
                                              NULL,
                                              NETWORK_PRESENCE_LONG_TERM_EXPIRY_HOURS_DEFAULT,
                                              customer_gid,
                                              (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                              (int64_t)0);

            ZPN_DEBUG_BNC("Customer:%"PRId64" State initialized successfully", customer_gid);
        }
        ZPATH_RWLOCK_UNLOCK(&customers_lock, __FILE__, __LINE__);
    }
    return customer;
}

static int32_t np_client_hold(struct zpn_client_np_app_state *client)
{
    if (!client) {
        ZPN_LOG(AL_CRITICAL, "Attempting to hold NULL client, this should never happen");
        return 0;
    }
    int32_t count = __sync_add_and_fetch_4(&(client->reference_count), 1);
    if (count == 1) {
        ZPN_LOG(AL_CRITICAL, "Incremented NP client reference count to 1, this should never happen");
    }
    return count;
}
/* Must be called before making any thread call
 * Must be paired with CLIENT_RELEASE_THREAD_CALL() when failed to make thread call & when thread call finishes */
#define CLIENT_HOLD_THREAD_CALL(client) np_client_hold(client)
/* hold when adding to customer list */
#define CLIENT_HOLD_CUSTOMER_LIST(client) np_client_hold(client)
/* hold when attaching to cstate */
#define CLIENT_HOLD_CSTATE(client) np_client_hold(client)


static void free_np_client_subnets(void *element, void *cookie)
{
    struct argo_object *object = element;
    struct zpn_client_np_app_state *client = cookie;
    struct np_client_subnets *subnet = object->base_structure_void;
    ZPN_DEBUG_BNC("%s: Releasing client subnet: %"PRId64", customer gid: %"PRId64"", client->debug_str, subnet->gid, subnet->customer_gid);
    argo_object_release(object);
    return;
}

static void free_np_lan_subnets(void *element, void *cookie)
{
    struct argo_object *object = element;
    struct zpn_client_np_app_state *client = cookie;
    struct np_lan_subnets *subnet = object->base_structure_void;
    ZPN_DEBUG_BNC("%s: Releasing lan subnet: %"PRId64", customer gid: %"PRId64"", client->debug_str, subnet->gid, subnet->customer_gid);
    argo_object_release(object);
    return;
}

static void np_client_cleanup(struct zpn_client_np_app_state *client, int free_sent_apps)
{
    ZPN_DEBUG_BNC("%s: NP client Freed", client->debug_str);

    if (free_sent_apps) {
        zhash_table_free_and_call(client->client_subnets_sent, free_np_client_subnets, client);
        zhash_table_free_and_call(client->lan_subnets_sent, free_np_lan_subnets, client);
    }

    ZPATH_MUTEX_DESTROY(&(client->client_subnets_sent_lock), _FILE_, _LINE_);
    ZPATH_MUTEX_DESTROY(&(client->lan_subnets_sent_lock), _FILE_, _LINE_);
    ZPN_BNC_FREE(client);
}

static int32_t np_client_release(struct zpn_client_np_app_state **client)
{
    if (!(*client)) {
        ZPN_LOG(AL_CRITICAL, "Attempting to release NULL client, this should never happen");
        return 0;
    }
    int32_t count = __sync_sub_and_fetch_4(&((*client)->reference_count), 1);

    /* If this suppose to be the last client to be released, the non-zero count may indicate a memory leak */
    /* i.e. we have more np_client_hold() than np_client_release() */
    if (!count) {
        np_client_cleanup(*client, 1/* free cache */);
        *client = NULL;
    }
    return count;
}
/* release when thread call finishes */
#define CLIENT_RELEASE_THREAD_CALL(client) np_client_release(&client)
/* release from customer np_clients list */
#define CLIENT_RELEASE_CUSTOMER_LIST(client) np_client_release(&client)
/* detach from c_state np_app_state reference pointer */
#define CLIENT_RELEASE_CSTATE(client) np_client_release(&client)


/*
 * Removes client from callback queues, etc and marks it deleted to
 * short-circuit in-progress evaluations and callbacks
 */
static void np_client_delete_cb(struct zevent_base  *base __attribute__((unused)),
                                void                *void_cookie,
                                int64_t             int_cookie __attribute__((unused)))
{
    struct zpn_client_np_app_state *client = void_cookie;
    struct np_customer_state *customer = client->customer;

    ZPATH_MUTEX_LOCK(&(customer->lock), __FILE__, __LINE__);

    __sync_sub_and_fetch_8(&(client->customer->current_np_app_download_clients), 1);
    ZPN_DEBUG_BNC("%s removing from customer np client list", client->debug_str);
    TAILQ_REMOVE(&(customer->np_clients), client, list);
    client->deleted = 1;
    ZPATH_MUTEX_UNLOCK(&(customer->lock), __FILE__, __LINE__);

    CLIENT_RELEASE_CUSTOMER_LIST(client);

    CLIENT_RELEASE_THREAD_CALL(client);
}

static int gid_hash_walk(void *cookie,
                         void *object __attribute__((unused)),
                         void *key,
                         size_t key_len)
{
    struct subnet_gids *subnet_gids = cookie;

    if (key_len != sizeof(int64_t)) {
        ZPN_LOG(AL_ERROR, "Invalid size");
        return ZPATH_RESULT_ERR;
    }
    if (subnet_gids->gids_used >= subnet_gids->gids_count) {
        /* avoid overflow */
        ZPN_LOG(AL_ERROR, "Too many subnet_gids %ld vs %ld", (long)subnet_gids->gids_used, (long)subnet_gids->gids_count);
        return ZPATH_RESULT_ERR;
    }
    subnet_gids->gids[subnet_gids->gids_used] = *((int64_t *)key);
    subnet_gids->gids_used++;

    return ZPATH_RESULT_NO_ERROR;
}

/* This function will set reference counter to 1 */
static struct subnet_gids *subnet_gids_from_hash(struct zhash_table *table)
{
    struct subnet_gids *subnet_gids = NULL;
    int64_t key = 0;
    int res;

    ZPN_DEBUG_BNC("Generating gids from hash");

    subnet_gids = ZPN_BNC_CALLOC(sizeof(*subnet_gids));
    subnet_gids->reference_count = 1;
    if (table) {
        subnet_gids->gids_count = zhash_table_get_size(table);
        subnet_gids->gids = ZPN_BNC_CALLOC(sizeof(*(subnet_gids->gids)) * subnet_gids->gids_count);

        res = zhash_table_walk(table,
                               &key,
                               gid_hash_walk,
                               subnet_gids);
        if (res) {
            ZPN_LOG(AL_ERROR, "Too many subnet_gids %ld vs %ld", (long)subnet_gids->gids_used, (long)subnet_gids->gids_count);
            /* We will still process as many as we could... */
        }
    }
    return subnet_gids;
}

static int32_t subnet_gids_hold(struct subnet_gids *subnet_gids)
{
    if (!subnet_gids) {
        ZPN_LOG(AL_CRITICAL, "Attempted to hold a null subnet_gid");
        return 0;
    }

    int32_t count = __sync_add_and_fetch_4(&(subnet_gids->reference_count), 1);
    if (count == 1) {
        ZPN_LOG(AL_CRITICAL, "Incremented subnet_gids reference count to 1");
    }
    return count;
}

static void subnet_gids_release(struct subnet_gids *subnet_gids)
{
    if (!subnet_gids) {
        return;
    }

    int ref = __sync_sub_and_fetch_4(&(subnet_gids->reference_count), 1);
    if (ref == 0) {
        if (subnet_gids->gids) {
            ZPN_BNC_FREE(subnet_gids->gids);
        }
        ZPN_BNC_FREE(subnet_gids);
    }
}

/*
 * Get all the customer's client subnet gids
 */
static struct subnet_gids *generate_all_client_subnet_gids(struct np_customer_state *customer)
{
    /* Generate all app_ids direct from wally. */
    struct np_client_subnets *subnets[MAX_NP_CLIENT_APPS];
    size_t subnets_count = sizeof(subnets) / sizeof(subnets[0]);
    struct subnet_gids *subnet_gids = NULL;
    int res;
    res = np_client_subnets_get_by_customer_gid_immediate(customer->customer_gid,
                                                          &(subnets[0]),
                                                          &subnets_count);
    if (res) {
        if (res != ZPN_RESULT_NOT_FOUND) {
            ZPN_LOG(AL_ERROR, "Error fetching client subnets for customer %"PRId64": %s", customer->customer_gid, zpn_result_string(res));
        } else {
            ZPN_DEBUG_BNC("client subnets fetch for customer %"PRId64": got no record", customer->customer_gid);
        }
    } else {
        size_t i;
        subnet_gids = ZPN_BNC_CALLOC(sizeof(*subnet_gids));
        subnet_gids->gids = ZPN_BNC_CALLOC(sizeof(*(subnet_gids->gids)) * subnets_count);
        subnet_gids->reference_count = 1;
        for (i = 0; i < subnets_count; i++) {
            subnet_gids->gids[i] = subnets[i]->gid;
        }
        subnet_gids->gids_count = subnets_count;
        subnet_gids->gids_used = subnets_count;
    }
    return subnet_gids;
}

/*
 * Get all the customer's lan subnet gids
 */
static struct subnet_gids *generate_all_lan_subnet_gids(struct np_customer_state *customer)
{
    /* Generate all app_ids direct from wally. */
    struct np_lan_subnets *subnets[MAX_NP_CLIENT_APPS];
    size_t subnets_count = sizeof(subnets) / sizeof(subnets[0]);
    struct subnet_gids *subnet_gids = NULL;
    int res;
    res = np_lan_subnets_get_by_customer_gid_immediate(customer->customer_gid,
                                                       &(subnets[0]),
                                                       &subnets_count);
    if (res) {
        if (res != ZPN_RESULT_NOT_FOUND) {
            ZPN_LOG(AL_ERROR, "Error fetching lan subnets for customer %"PRId64": %s", customer->customer_gid, zpn_result_string(res));
        } else {
            ZPN_DEBUG_BNC("lan subnets fetch for customer %"PRId64": got no record", customer->customer_gid);
        }
    } else {
        size_t i;
        subnet_gids = ZPN_BNC_CALLOC(sizeof(*subnet_gids));
        subnet_gids->gids = ZPN_BNC_CALLOC(sizeof(*(subnet_gids->gids)) * subnets_count);
        subnet_gids->reference_count = 1;
        for (i = 0; i < subnets_count; i++) {
            subnet_gids->gids[i] = subnets[i]->gid;
        }
        subnet_gids->gids_count = subnets_count;
        subnet_gids->gids_used = subnets_count;
    }
    return subnet_gids;
}

static void client_subnet_gid_update(struct np_customer_state *customer, int64_t gid)
{
    ZPATH_MUTEX_LOCK(&(customer->lock), __FILE__, __LINE__);

    /* config changed, schedule timer to push updates to all clients */
    refresh_timer(customer,
                  1/*download client subnets*/,
                  0/*download lan subnets*/,
                  0/*download dns ns records*/);

    /* Remember that this subnet has been touched. */
    if (!zhash_table_lookup(customer->client_subnets_accumulate, &gid, sizeof(gid), NULL)) {
        zhash_table_store(customer->client_subnets_accumulate, &gid, sizeof(gid), 0, customer);
    }

    ZPATH_MUTEX_UNLOCK(&(customer->lock), __FILE__, __LINE__);
}

static void lan_subnet_gid_update(struct np_customer_state *customer, int64_t gid)
{
    ZPATH_MUTEX_LOCK(&(customer->lock), __FILE__, __LINE__);

    /* config changed, schedule timer to push updates to all clients */
    refresh_timer(customer,
                  0/*download client subnets*/,
                  1/*download lan subnets*/,
                  0/*download dns ns records*/);

    /* Remember that this subnet has been touched. */
    if (!zhash_table_lookup(customer->lan_subnets_accumulate, &gid, sizeof(gid), NULL)) {
        zhash_table_store(customer->lan_subnets_accumulate, &gid, sizeof(gid), 0, customer);
    }

    ZPATH_MUTEX_UNLOCK(&(customer->lock), __FILE__, __LINE__);
}

/*
 * Let all clients of this customer about all the subnets that have been accumulated
 *
 * ALWAYS called with customer->lock held.
 */
static void inform_all_np_clients(struct np_customer_state *customer,
                                  uint8_t download_client_subnets,
                                  uint8_t download_lan_subnets,
                                  uint8_t download_dns_ns_records)
{
    struct zpn_client_np_app_state *client;
    struct subnet_gids *client_subnet_gids = NULL;
    struct subnet_gids *lan_subnet_gids = NULL;

    if (download_client_subnets) {
        client_subnet_gids = subnet_gids_from_hash(customer->client_subnets_accumulate);
    }
    if (download_lan_subnets) {
        lan_subnet_gids = subnet_gids_from_hash(customer->lan_subnets_accumulate);
    }

    zpn_broker_np_client_apps_customer_log("np_app_download",
                                           customer,
                                           download_client_subnets,
                                           download_lan_subnets,
                                           download_dns_ns_records);

    TAILQ_FOREACH(client, &(customer->np_clients), list) {

        ZPN_DEBUG_BNC("Customer %"PRId64": NP subnet update inform client %s, download: client subnets (%s), lan subnets: (%s), dns ns records: (%s)",
                        customer->customer_gid, client->debug_str,
                        download_client_subnets ? "yes" : "no",
                        download_lan_subnets ? "yes" : "no",
                        download_dns_ns_records ? "yes" : "no");

        if (download_client_subnets) {
            subnet_gids_hold(client_subnet_gids);
            zpn_broker_np_client_download_client_subnets(client, client_subnet_gids, 0);
        }
        if (download_lan_subnets) {
            subnet_gids_hold(lan_subnet_gids);
            zpn_broker_np_client_download_lan_subnets(client, lan_subnet_gids, 0);
        }
        if (download_dns_ns_records) {
            zpn_broker_np_client_download_dns_ns_records(client, 0);
        }
    }

    if (download_client_subnets) {
        subnet_gids_release(client_subnet_gids);
    }
    if (download_lan_subnets) {
        subnet_gids_release(lan_subnet_gids);
    }

    if (customer->client_subnets_accumulate) zhash_table_free(customer->client_subnets_accumulate);
    customer->client_subnets_accumulate = zhash_table_alloc(&zpn_broker_np_client_allocator);

    if (customer->lan_subnets_accumulate) zhash_table_free(customer->lan_subnets_accumulate);
    customer->lan_subnets_accumulate = zhash_table_alloc(&zpn_broker_np_client_allocator);

    if (customer->dns_ns_records_accumulate) zhash_table_free(customer->dns_ns_records_accumulate);
    customer->dns_ns_records_accumulate = zhash_table_alloc(&zpn_broker_np_client_allocator);
}

static void np_client_subnets_row_callback_deferred(void *void_cookie1, void *void_cookie2)
{
    struct argo_object *row = void_cookie1;
    struct argo_object *previous_row = void_cookie2;
    struct np_customer_state *customer = NULL;
    struct np_client_subnets *subnet = row->base_structure_void;
    int64_t customer_gid = subnet->customer_gid;

    if (!np_is_feature_enabled(customer_gid)) {
        ZPN_LOG(AL_INFO, "Ignoring client subnet row callback as feature state is disabled for customer %"PRId64"", customer_gid);
        goto done;
    }

    customer = get_np_customer_state(customer_gid);
    if (!customer) {
        ZPN_LOG(AL_ERROR, "np_client_subnet_row_callback: Could not get customer state for customer gid: %"PRId64"", customer_gid);
        goto done;
    }

    if (previous_row) {
        ZPN_DEBUG_BNC("Customer %"PRId64": Client subnet row arrived, gid = %"PRId64", processing old row", customer_gid, subnet->gid);
        struct np_client_subnets *old_subnet = previous_row->base_structure_void;
        client_subnet_gid_update(customer, old_subnet->gid);
    }
    client_subnet_gid_update(customer, subnet->gid);

done:
    if (previous_row) argo_object_release(previous_row);
    argo_object_release(row);
}

static int zpn_bnc_np_client_subnets_row_callback(void *response_callback_cookie __attribute__((unused)),
                                                  struct wally_registrant *registrant __attribute__((unused)),
                                                  struct wally_table *table __attribute__((unused)),
                                                  struct argo_object *previous_row,
                                                  struct argo_object *row,
                                                  int64_t request_id __attribute__((unused)))
{
    if (zpn_debug_get(ZPN_DEBUG_BNC_IDX)) {
        char dump[8000];
        if (argo_object_dump(row, dump, sizeof(dump), NULL, 0) == ARGO_RESULT_NO_ERROR) {
            ZPN_DEBUG_BNC("np_client_subnets row callback: %s", dump);
        }
    }

    argo_object_hold(row);
    if (previous_row) argo_object_hold(previous_row);

    if (zevent_defer(np_client_subnets_row_callback_deferred, row, previous_row, 0) != 0) {
        ZPN_LOG(AL_ERROR, "zpn_bnc_np_client_subnets_row_callback: failed to schedule deferred call!");
        argo_object_release(row);
        if (previous_row) argo_object_release(previous_row);
    }

    return ZPN_RESULT_NO_ERROR;
}

static void np_lan_subnets_row_callback_deferred(void *void_cookie1, void *void_cookie2)
{
    struct argo_object *row = void_cookie1;
    struct argo_object *previous_row = void_cookie2;
    struct np_customer_state *customer = NULL;
    struct np_lan_subnets *subnet = row->base_structure_void;
    int64_t customer_gid = subnet->customer_gid;

    if (!np_is_feature_enabled(customer_gid)) {
        ZPN_LOG(AL_INFO, "Ignoring lan subnet row callback as feature state is disabled for customer %"PRId64"", customer_gid);
        goto done;
    }

    customer = get_np_customer_state(customer_gid);
    if (!customer) {
        ZPN_LOG(AL_ERROR, "np_lan_subnet_row_callback: Could not get customer state for customer gid: %"PRId64"", customer_gid);
        goto done;
    }

    if (previous_row) {
        ZPN_DEBUG_BNC("Customer %"PRId64": LAN subnet row arrived, gid = %"PRId64", processing old row", customer_gid, subnet->gid);
        struct np_lan_subnets *old_subnet = previous_row->base_structure_void;
        lan_subnet_gid_update(customer, old_subnet->gid);
    }
    lan_subnet_gid_update(customer, subnet->gid);

done:
    if (previous_row) argo_object_release(previous_row);
    argo_object_release(row);
}

static int zpn_bnc_np_lan_subnets_row_callback(void *response_callback_cookie __attribute__((unused)),
                                               struct wally_registrant *registrant __attribute__((unused)),
                                               struct wally_table *table __attribute__((unused)),
                                               struct argo_object *previous_row,
                                               struct argo_object *row,
                                               int64_t request_id __attribute__((unused)))
{
    if (zpn_debug_get(ZPN_DEBUG_BNC_IDX)) {
        char dump[8000];
        if (argo_object_dump(row, dump, sizeof(dump), NULL, 0) == ARGO_RESULT_NO_ERROR) {
            ZPN_DEBUG_BNC("np_lan_subnets row callback: %s", dump);
        }
    }

    argo_object_hold(row);
    if (previous_row) argo_object_hold(previous_row);

    if (zevent_defer(np_lan_subnets_row_callback_deferred, row, previous_row, 0) != 0) {
        ZPN_LOG(AL_ERROR, "zpn_bnc_np_lan_subnets_row_callback: failed to schedule deferred call!");
        argo_object_release(row);
        if (previous_row) argo_object_release(previous_row);
    }

    return ZPN_RESULT_NO_ERROR;
}

static void np_dns_ns_records_row_callback_deferred(void *void_cookie1, void *void_cookie2)
{
    struct argo_object *row = void_cookie1;
    struct np_customer_state *customer = NULL;
    struct np_dns_ns_records *record = row->base_structure_void;
    int64_t customer_gid = record->customer_gid;

    if (!np_is_feature_enabled(customer_gid)) {
        ZPN_LOG(AL_INFO, "Ignoring dns ns record row callback as feature state is disabled for customer %"PRId64"", customer_gid);
        goto done;
    }

    customer = get_np_customer_state(customer_gid);
    if (!customer) {
        ZPN_LOG(AL_ERROR, "np_dns_ns_records_row_callback: Could not get customer state for customer gid: %"PRId64"", customer_gid);
        goto done;
    }

    ZPATH_MUTEX_LOCK(&(customer->lock), __FILE__, __LINE__);
    /* config changed, schedule timer to push updates to all clients */
    refresh_timer(customer,
                  0/*download client subnets*/,
                  0/*download lan subnets*/,
                  1/*download dns ns records*/);

    /* Remember that this record has been touched. */
    if (!zhash_table_lookup(customer->dns_ns_records_accumulate, &record->gid, sizeof(record->gid), NULL)) {
        zhash_table_store(customer->dns_ns_records_accumulate, &record->gid, sizeof(record->gid), 0, customer);
    }
    ZPATH_MUTEX_UNLOCK(&(customer->lock), __FILE__, __LINE__);

done:
    argo_object_release(row);
}

static int zpn_bnc_np_dns_ns_records_row_callback(void *response_callback_cookie __attribute__((unused)),
                                                  struct wally_registrant *registrant __attribute__((unused)),
                                                  struct wally_table *table __attribute__((unused)),
                                                  struct argo_object *previous_row,
                                                  struct argo_object *row,
                                                  int64_t request_id __attribute__((unused)))
{
    if (zpn_debug_get(ZPN_DEBUG_BNC_IDX)) {
        char dump[8000];
        if (argo_object_dump(row, dump, sizeof(dump), NULL, 0) == ARGO_RESULT_NO_ERROR) {
            ZPN_DEBUG_BNC("np_dns_ns_records row callback: %s", dump);
        }
    }
    argo_object_hold(row);
    if (zevent_defer(np_dns_ns_records_row_callback_deferred, row, NULL, 0) != 0) {
        ZPN_LOG(AL_ERROR, "zpn_bnc_np_dns_ns_records_row_callback: failed to schedule deferred call!");
        argo_object_release(row);
    }
    return ZPN_RESULT_NO_ERROR;
}

static void customer_timer(int sock __attribute__((unused)), short flags __attribute__((unused)), void *cookie)
{
    struct timer_parameter *timer_param = cookie;
    struct np_customer_state *customer = timer_param->customer;
    uint8_t download_client_subnets = timer_param->download_client_subnets;
    uint8_t download_lan_subnets = timer_param->download_lan_subnets;
    uint8_t download_dns_ns_records = timer_param->download_dns_ns_records;

    ZPATH_MUTEX_LOCK(&(customer->lock), __FILE__, __LINE__);

    ZPN_DEBUG_BNC("Customer %"PRId64": NP App timer fired, download: client subnet (%s) lan subnet (%s) dns records (%s)",
                    customer->customer_gid,
                    download_client_subnets ? "yes" : "no",
                    download_lan_subnets ? "yes" : "no",
                    download_dns_ns_records ? "yes" : "no");

    ZPN_BNC_FREE(timer_param);
    if (customer->timer) {
        event_free(customer->timer);
        customer->timer = NULL;
    }

    inform_all_np_clients(customer, download_client_subnets, download_lan_subnets, download_dns_ns_records);

    ZPATH_MUTEX_UNLOCK(&(customer->lock), __FILE__, __LINE__);
}

/*
 * Must have customer lock held when called
 */
static void refresh_timer(struct np_customer_state *customer,
                          uint8_t download_client_subnets,
                          uint8_t download_lan_subnets,
                          uint8_t download_dns_ns_records)
{
    struct timeval tv;
    struct timer_parameter *timer_param;
    int64_t delayed_s = 0;

    if (!customer->timer) {
        timer_param = ZPN_BNC_CALLOC(sizeof(*timer_param));
        timer_param->customer = customer;
        timer_param->download_client_subnets = download_client_subnets;
        timer_param->download_lan_subnets = download_lan_subnets;
        timer_param->download_dns_ns_records = download_dns_ns_records;
        timer_param->start_time_mono_s = monotime_s();
        customer->timer = event_new(zevent_event_base(customer->calc_thread), -1, 0, customer_timer, timer_param);
        ZPN_DEBUG_BNC("Customer %"PRId64": App timer create", customer->customer_gid);
    } else {
        /* Update timer parameters */
        timer_param = (struct timer_parameter *)event_get_callback_arg(customer->timer);
        timer_param->download_client_subnets |= download_client_subnets;
        timer_param->download_lan_subnets |= download_lan_subnets;
        timer_param->download_dns_ns_records |= download_dns_ns_records;
        ZPN_DEBUG_BNC("Customer %"PRId64": App timer push", customer->customer_gid);
    }

    delayed_s = monotime_s() - timer_param->start_time_mono_s;
    if (delayed_s >= MAX_TIMER_DELAY_S) {
        /* We already delayed 60 seconds on it, fire immediately to avoid client starvation */
        tv.tv_usec = 0;
        tv.tv_sec = 0;
        if (event_add(customer->timer, &tv) != 0) {
            ZPN_LOG(AL_ERROR, "Customer %"PRId64": Failed to schedule instant timer push", customer->customer_gid);
        } else {
            ZPN_LOG(AL_NOTICE, "Customer %"PRId64": already delayed np app download for more than: %"PRId64" seconds, forcing instant download",
                        customer->customer_gid, delayed_s);
        }
    } else {
        /* This will schedule or reschedule the timer into the future */
        tv.tv_usec = np_recalc_push_us % 1000000;
        tv.tv_sec = np_recalc_push_us / 1000000;
        if (event_add(customer->timer, &tv) != 0) {
            ZPN_LOG(AL_ERROR, "Customer %"PRId64": Failed to schedule instant timer push", customer->customer_gid);
        }
    }
}

static struct zpn_client_np_app_state*
zpn_broker_client_create_np_app_state(struct zpn_broker_client_fohh_state *c_state)
{
    struct zpn_client_np_app_state *client;

    client = ZPN_BNC_CALLOC(sizeof(*client));

    /* initialize reference counter to 1, we need to offset it when app state init is done */
    client->reference_count = 1;

    client->client_subnets_sent_lock = ZPATH_MUTEX_INIT;
    client->lan_subnets_sent_lock = ZPATH_MUTEX_INIT;
    snprintf(client->debug_str, sizeof(client->debug_str), "%s:%"PRId64"", c_state->tunnel_id, c_state->customer_gid);

    client->tracker = &(c_state->tracker);
    client->tracker->tag = client->debug_str;

    client->customer_gid = c_state->customer_gid;
    client->conn_thread_id = c_state->conn_thread_id;

    snprintf(client->tunnel_id, sizeof(client->tunnel_id), "%s", c_state->tunnel_id);
    client->c_state_incarnation = c_state->incarnation;

    client->client_fohh_thread = zevent_self();
    if (!client->client_fohh_thread) {
        ZPN_LOG(AL_CRITICAL, "%s: Cannot get thread: client thread", client->debug_str);
        np_client_cleanup(client, 0/* free cache */);
        return NULL;
    }
    client->calc_thread = zevent_get_for_class(ZEVENT_CLASS_APP_CALC);
    if (!client->calc_thread) {
        ZPN_LOG(AL_CRITICAL, "%s: Cannot get thread: calc thread", client->debug_str);
        np_client_cleanup(client, 0/* free cache */);
        return NULL;
    }

    client->customer = get_np_customer_state(client->customer_gid);
    if (!client->customer) {
        ZPN_LOG(AL_CRITICAL, "%s: Cannot get customer", client->debug_str);
        np_client_cleanup(client, 0/* free cache */);
        return NULL;
    }

    /* hold it before c_state references it */
    CLIENT_HOLD_CSTATE(client);
    c_state->np_app_state = client;
    c_state->np_app_stats = &(client->stats);
    ZPN_DEBUG_BNC("%s: NP app fetch started", client->debug_str);
    snprintf(client->app_download_status_str, sizeof(client->app_download_status_str), "NP_APP_DOWNLOAD: state initialized");

    ZPATH_MUTEX_LOCK(&(client->client_subnets_sent_lock), __FILE__, __LINE__);
    client->client_subnets_sent = zhash_table_alloc(&zpn_broker_np_client_allocator);
    ZPATH_MUTEX_UNLOCK(&(client->client_subnets_sent_lock), __FILE__, __LINE__);

    ZPATH_MUTEX_LOCK(&(client->lan_subnets_sent_lock), __FILE__, __LINE__);
    client->lan_subnets_sent = zhash_table_alloc(&zpn_broker_np_client_allocator);
    ZPATH_MUTEX_UNLOCK(&(client->lan_subnets_sent_lock), __FILE__, __LINE__);

    /* Attach self to customer, so we get updates. Then have the
     * customer process me... */
    ZPATH_MUTEX_LOCK(&(client->customer->lock), __FILE__, __LINE__);
    __sync_add_and_fetch_8(&(client->customer->current_np_app_download_clients), 1);
    __sync_add_and_fetch_8(&(client->customer->total_np_app_download_clients), 1);
    /* hold it before attaching to customer client list */
    CLIENT_HOLD_CUSTOMER_LIST(client);
    TAILQ_INSERT_TAIL(&(client->customer->np_clients), client, list);
    ZPATH_MUTEX_UNLOCK(&(client->customer->lock), __FILE__, __LINE__);

    return client;

}

/* Send one client subnet to the client */
static void subnet_tx_cb(struct zevent_base *base __attribute__((unused)),
                         void               *void_cookie,
                         int64_t            int_cookie,
                         void               *extra_cookie1,
                         void               *extra_cookie2 __attribute__((unused)),
                         void               *extra_cookie3 __attribute__((unused)),
                         int64_t            extra_int_cookie)
{
    struct zpn_client_np_app_state *client = void_cookie;
    struct argo_object *object_to_transmit = extra_cookie1;
    struct zpn_broker_client_fohh_state *c_state = NULL;
    int is_client_subnet = int_cookie ? 1 : 0;
    int is_new_client = extra_int_cookie ? 1 : 0;
    static int client_subnet_sent_complete = 0;
    static int lan_subnet_sent_complete = 0;
    char* tunnel_id = NULL;
    struct zpn_tlv *tlv;
    int res;

    if (!client || client->deleted) {
        ZPN_LOG(AL_ERROR, "subnet_tx_cb without client state");
        goto done;
    }

    tunnel_id = client->tunnel_id;
    if (zpn_broker_client_tunnel_c_state_lookup(&c_state, NULL, tunnel_id, strnlen(tunnel_id, sizeof(client->tunnel_id) - 1))) {
        ZPN_LOG(AL_ERROR, "Cannot look for c_state with tunnel id = %s in the lookup hash table, customer gid: %"PRId64"",
                            tunnel_id, client->customer_gid);
        goto done;
    }

    tlv = c_state_get_tlv(c_state);
    if (zpn_tlv_sanity_check(tlv) != ZPN_RESULT_NO_ERROR) {
        ZPN_LOG(AL_ERROR, "%s TLV no connection when sending %s subnet, customer gid: %"PRId64"",
                            c_state->tunnel_id, is_client_subnet ? "client" : "lan", c_state->customer_gid);
        goto done;
    }

    res = zpn_send_np_client_app(tlv, 0, object_to_transmit);
    if (res) {
        ZPN_LOG(AL_WARNING, "%s: overrun? Error sending %s subnet to customer: %"PRId64" - %s",
                            c_state->tunnel_id,
                            is_client_subnet ? "client" : "lan",
                            c_state->customer_gid,
                            zpn_result_string(res));
    }

    if (is_new_client) {
        if (is_client_subnet) {
            client->stats.client_subnet_sent_cnt_out++;
            if (!client->is_cstate_done) zpn_broker_np_client_app_sent_stats[client->conn_thread_id].client_subnet_sent_cnt_out++;
            if (client->is_last_client_subnet_enqueued &&
                    (client->stats.client_subnet_sent_cnt_in == client->stats.client_subnet_sent_cnt_out)) {
                zpn_client_tracker_end(client->tracker, client_track_np_client_subnet, ZPN_RESULT_NO_ERROR);
                client_subnet_sent_complete = 1;
            }
        } else {
            client->stats.lan_subnet_sent_cnt_out++;
            if (!client->is_cstate_done) zpn_broker_np_client_app_sent_stats[client->conn_thread_id].lan_subnet_sent_cnt_out++;
            if (client->is_last_lan_subnet_enqueued &&
                    (client->stats.lan_subnet_sent_cnt_in == client->stats.lan_subnet_sent_cnt_out)) {
                zpn_client_tracker_end(client->tracker, client_track_np_lan_subnet, ZPN_RESULT_NO_ERROR);
                lan_subnet_sent_complete = 1;
            }
        }
        if (client_subnet_sent_complete && lan_subnet_sent_complete) {
            zpn_client_tracker_end(client->tracker, client_track_np_subnet, ZPN_RESULT_NO_ERROR);
        }
    }
    if (!client->stats.first_subnet_sent_us) client->stats.first_subnet_sent_us = epoch_us();
    client->stats.latest_subnet_sent_us = epoch_us();
done:
    if (client) CLIENT_RELEASE_THREAD_CALL(client);
    if (object_to_transmit) argo_object_release(object_to_transmit);
}

/* Send one client subnet to the client */
static void domain_tx_cb(struct zevent_base *base __attribute__((unused)),
                         void               *void_cookie,
                         int64_t            int_cookie,
                         void               *extra_cookie1,
                         void               *extra_cookie2 __attribute__((unused)),
                         void               *extra_cookie3 __attribute__((unused)),
                         int64_t            extra_int_cookie __attribute__((unused)))
{
    struct zpn_client_np_app_state *client = void_cookie;
    struct argo_object *object_to_transmit = extra_cookie1;
    struct zpn_broker_client_fohh_state *c_state = NULL;
    int is_new_client = int_cookie ? 1 : 0;
    char* tunnel_id = NULL;
    struct zpn_tlv *tlv;
    int res;

    if (!client || client->deleted) {
        ZPN_LOG(AL_ERROR, "domain_tx_cb without client state");
        goto done;
    }

    tunnel_id = client->tunnel_id;
    if (zpn_broker_client_tunnel_c_state_lookup(&c_state, NULL, tunnel_id, strnlen(tunnel_id, sizeof(client->tunnel_id) - 1))) {
        ZPN_LOG(AL_ERROR, "Cannot look for c_state with tunnel id = %s in the lookup hash table, customer gid: %"PRId64"",
                            tunnel_id, client->customer_gid);
        goto done;
    }

    tlv = c_state_get_tlv(c_state);
    if (zpn_tlv_sanity_check(tlv) != ZPN_RESULT_NO_ERROR) {
        ZPN_LOG(AL_ERROR, "%s TLV no connection when sending domain, customer gid: %"PRId64"",
                            c_state->tunnel_id, c_state->customer_gid);
        goto done;
    }

    res = zpn_send_np_app_domain(tlv, 0, object_to_transmit);
    if (res) {
        ZPN_LOG(AL_WARNING, "%s: overrun? Error sending NP app domain to customer: %"PRId64" - %s",
                            c_state->tunnel_id,
                            c_state->customer_gid,
                            zpn_result_string(res));
    }

    if (is_new_client) {
        client->stats.domain_sent_cnt_out++;
        if (!client->is_cstate_done) zpn_broker_np_client_app_sent_stats[client->conn_thread_id].domain_sent_cnt_out++;
        if (client->is_last_domain_enqueued &&
                (client->stats.domain_sent_cnt_in == client->stats.domain_sent_cnt_out)) {
            zpn_client_tracker_end(client->tracker, client_track_np_domain, ZPN_RESULT_NO_ERROR);
        }
    }
    if (!client->stats.first_domain_sent_us) client->stats.first_domain_sent_us = epoch_us();
    client->stats.latest_domain_sent_us = epoch_us();
done:
    if (client) CLIENT_RELEASE_THREAD_CALL(client);
    if (object_to_transmit) argo_object_release(object_to_transmit);
}

/* Must be called with client_subnets_sent_lock
 * Client subnet update will end up with row modification */
static void zpn_broker_np_client_send_client_subnet(struct zpn_client_np_app_state *client,
                                                    int64_t client_subnet_gid,
                                                    int64_t is_new_client,
                                                    int is_last_subnet)
{
    struct argo_object *old_subnet_object = NULL;
    /* old_subnet: what we have sent to the client before */
    struct np_client_subnets *old_subnet;
    /* new_subnet: latest subnet config from wally */
    struct np_client_subnets *new_subnet;
    int res = ZPN_RESULT_NO_ERROR;
    int added = 0;
    int deleted = 0;

    if (!client || client->deleted) {
        ZPN_LOG(AL_ERROR, "zpn_broker_np_client_send_client_subnet without client state");
        return;
    }

    if (!client_subnet_gid) {
        ZPN_LOG(AL_ERROR, "zpn_broker_np_client_send_client_subnet received subnet gid as 0, customer: %"PRId64"", client->customer_gid);
        return;
    }

    // coverity[callee_ptr_arith]
    res = np_client_subnets_get_by_id_immediate(client_subnet_gid, &new_subnet);
    if (res) new_subnet = NULL;

    if (new_subnet && (new_subnet->subnet == NULL)) {
        ZPN_DEBUG_BNC("Received new client subnet (gid %"PRId64", customer gid %"PRId64") but subnet is NULL, ignoring",
                            new_subnet->gid, new_subnet->customer_gid);
        return;
    }

    /* Get the object we last sent to client. */
    old_subnet_object = zhash_table_lookup(client->client_subnets_sent, &client_subnet_gid, sizeof(client_subnet_gid), NULL);
    if (old_subnet_object) {
        old_subnet = old_subnet_object->base_structure_void;
    } else {
        old_subnet = NULL;
    }

    if (!new_subnet && old_subnet) {
        /* Subnet has been deleted, remove it from hash table and inform client of deletion */
        deleted = 1;
    } else if (new_subnet && !old_subnet) {
        /* A new subnet has been added, add it to hash table and inform client of addition */
        added = 1;
    } else if (new_subnet && old_subnet) {
        /* new row arrived on subnet we have sent before, check if anything changed */
        if (old_subnet->subnet && new_subnet->subnet && !argo_inet_is_same(old_subnet->subnet, new_subnet->subnet)) {
            /* subnet has been modified */
            added = 1;
            deleted = 1;
        } else {
            /* nothing to do */
            ZPN_DEBUG_BNC("Received new client subnet (gid %"PRId64", customer gid %"PRId64") but subnet did not change, skipping",
                            new_subnet->gid, new_subnet->customer_gid);
        }
    } else {
        /* nothing to do */
        ZPN_DEBUG_BNC("No new & old client subnet");
    }

    if (deleted) {
        char subnet_str[ARGO_INET_ADDRSTRLEN] = {0};
        struct argo_object *object_to_send = NULL;

        /* send the app to client */
        struct zpn_np_client_app data;
        memset(&data, 0, sizeof(data));
        data.app_subnet = old_subnet->subnet;
        data.deleted = 1;
        data.app_type = "CLIENT";

        object_to_send = argo_object_create(zpn_np_client_app_description, &data);
        if (!object_to_send) {
            ZPN_LOG(AL_ERROR, "Could not create object for client subnet %"PRId64" customer: %"PRId64"", old_subnet->gid, client->customer_gid);
            return;
        }

        CLIENT_HOLD_THREAD_CALL(client);
        if (0 != zevent_base_big_call(client->client_fohh_thread, subnet_tx_cb, client, 1/*is client subnet*/, object_to_send, NULL, NULL, 0)) {
            ZPN_LOG(AL_ERROR, "Thread call failed for sending client subnet gid %"PRId64" customer: %"PRId64"", old_subnet->gid, client->customer_gid);
            argo_object_release(object_to_send);
            CLIENT_RELEASE_THREAD_CALL(client);
            return;
        } else {
            ZPN_DEBUG_BNC("Received a deleted client subnet (gid %"PRId64", subnet: %s, customer gid %"PRId64"), informing client",
                            old_subnet->gid, argo_inet_generate(subnet_str, old_subnet->subnet), old_subnet->customer_gid);
        }

        zhash_table_remove(client->client_subnets_sent, &(old_subnet->gid), sizeof(old_subnet->gid), old_subnet_object);
        argo_object_release(old_subnet_object);
    }

    if (added) {
        char subnet_str[ARGO_INET_ADDRSTRLEN] = {0};
        struct argo_object *object_to_send = NULL;

        if (new_subnet->customer_gid != client->customer_gid) {
            ZPN_LOG(AL_ERROR, "Cannot send client subnet %"PRId64" to customer: %"PRId64" as subnet belongs to a different customer %"PRId64"",
                                new_subnet->gid, client->customer_gid, new_subnet->customer_gid);
            return;
        }

        /* send the app to client */
        struct zpn_np_client_app data;
        memset(&data, 0, sizeof(data));
        data.app_subnet = new_subnet->subnet;
        data.deleted = 0;
        data.app_type = "CLIENT";

        object_to_send = argo_object_create(zpn_np_client_app_description, &data);
        if (!object_to_send) {
            ZPN_LOG(AL_ERROR, "Could not create object for client subnet %"PRId64" customer: %"PRId64"", new_subnet->gid, client->customer_gid);
            return;
        }

        if (is_new_client) {
            if (!client->is_first_subnet_enqueued) {
                client->is_first_subnet_enqueued = 1;
                zpn_client_tracker_start(client->tracker, client_track_np_subnet);
            }
            if (!client->is_first_client_subnet_enqueued) {
                client->is_first_client_subnet_enqueued = 1;
                zpn_client_tracker_start(client->tracker, client_track_np_client_subnet);
            }
            if (!client->is_last_client_subnet_enqueued) {
                client->is_last_client_subnet_enqueued = is_last_subnet ? 1 : 0;
            }
            client->stats.client_subnet_sent_cnt_in++;
            if (!client->is_cstate_done) zpn_broker_np_client_app_sent_stats[client->conn_thread_id].client_subnet_sent_cnt_in++;
        }

        CLIENT_HOLD_THREAD_CALL(client);
        if (0 != zevent_base_big_call(client->client_fohh_thread, subnet_tx_cb, client, 1/*is client subnet*/, object_to_send, NULL, NULL, is_new_client)) {
            ZPN_LOG(AL_ERROR, "Thread call failed for sending client subnet gid %"PRId64" customer: %"PRId64"", new_subnet->gid, client->customer_gid);
            argo_object_release(object_to_send);
            CLIENT_RELEASE_THREAD_CALL(client);
            return;
        } else {
            ZPN_DEBUG_BNC("Received a new client subnet (gid %"PRId64", subnet: %s, customer gid %"PRId64"), informing client",
                            new_subnet->gid, argo_inet_generate(subnet_str, new_subnet->subnet), new_subnet->customer_gid);
        }

        struct argo_object *object = argo_object_create(np_client_subnets_description, new_subnet);
        zhash_table_store(client->client_subnets_sent, &(new_subnet->gid), sizeof(new_subnet->gid), 0, object);
    }
}


static void zpn_broker_np_client_send_lan_subnet_each(struct zpn_client_np_app_state *client,
                                                      int delete,
                                                      const struct argo_inet *subnet,
                                                      int64_t subnet_gid,
                                                      int is_new_client)
{
    char subnet_str[ARGO_INET_ADDRSTRLEN] = {0};
    argo_inet_generate(subnet_str, subnet);
    struct argo_object *object_to_send = NULL;

    /* send the app to client */
    struct zpn_np_client_app data = {
        .app_subnet = subnet,
        .deleted    = delete ? 1 : 0,
        .app_type   = "LAN"
    };

    object_to_send = argo_object_create(zpn_np_client_app_description, &data);
    if (!object_to_send) {
        ZPN_LOG(AL_ERROR, "Could not create object for lan subnet %"PRId64" customer: %"PRId64"", subnet_gid, client->customer_gid);
        return;
    }

    CLIENT_HOLD_THREAD_CALL(client);
    if (0 != zevent_base_big_call(client->client_fohh_thread, subnet_tx_cb, client, 0/*is client subnet*/, object_to_send, NULL, NULL, delete?0:is_new_client)) {
        ZPN_LOG(AL_ERROR, "Thread call failed for sending lan subnet gid %"PRId64" customer: %"PRId64"", subnet_gid, client->customer_gid);
        argo_object_release(object_to_send);
        CLIENT_RELEASE_THREAD_CALL(client);
        return;
    } else {
        ZPN_DEBUG_BNC("Received a %s lan subnet (gid %"PRId64", subnet: %s, customer gid %"PRId64"), informing client",
                      delete?"deleted":"new", subnet_gid, subnet_str, client->customer_gid);
    }

    return;
}

int64_t zpn_broker_np_client_multilan_enabled(int64_t customer_gid)
{
    int64_t value;

    value = zpath_config_override_get_config_int(CONFIG_FEATURE_NP_MULTI_LAN_SUBNET_ENABLE,
                                                &value,
                                                DEFAULT_CONFIG_FEATURE_NP_MULTI_LAN_SUBNET_ENABLE,
                                                zpath_instance_global_state.current_config->gid,
                                                customer_gid,
                                                (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                (int64_t)0);
    return value;
}

/* Must be called with lan_subnets_sent_lock
 * LAN subnet can never be modified, it will only be deleted and re-created */
static void zpn_broker_np_client_send_lan_subnet(struct zpn_client_np_app_state *client,
                                                 int64_t lan_subnet_gid,
                                                 int64_t is_new_client,
                                                 int is_last_subnet)
{
    struct argo_object *old_subnet_object = NULL;
    /* old_subnet: what we have sent to the client before */
    struct np_lan_subnets *old_subnet;
    /* new_subnet: latest subnet config from wally */
    struct np_lan_subnets *new_subnet;
    int res = ZPN_RESULT_NO_ERROR;
    int added = 0;
    int deleted = 0;

    struct argo_inet *ips_to_add_holder[NETWORK_SEGMENT_MAX_LAN_SUBNETS];
    struct argo_inet *ips_to_delete_holder[NETWORK_SEGMENT_MAX_LAN_SUBNETS];

    struct argo_inet **ips_to_delete = NULL;
    struct argo_inet **ips_to_add = NULL;
    int ips_to_add_count = 0;
    int ips_to_delete_count = 0;
    int64_t customer_gid = 0;

    int64_t g_broker_multi_lan_feature_enable = DEFAULT_CONFIG_FEATURE_NP_MULTI_LAN_SUBNET_ENABLE;

    if (!client || client->deleted) {
        ZPN_LOG(AL_ERROR, "zpn_broker_np_client_send_lan_subnet without client state");
        return;
    }

    if (!lan_subnet_gid) {
        ZPN_LOG(AL_ERROR, "zpn_broker_np_client_send_lan_subnet received subnet gid as 0, customer: %"PRId64"", client->customer_gid);
        return;
    }

    // coverity[callee_ptr_arith]
    res = np_lan_subnets_get_by_id_immediate(lan_subnet_gid, &new_subnet);
    if (res) new_subnet = NULL;

    /* Get the object we last sent to client. */
    old_subnet_object = zhash_table_lookup(client->lan_subnets_sent, &lan_subnet_gid, sizeof(lan_subnet_gid), NULL);
    if (old_subnet_object) {
        old_subnet = old_subnet_object->base_structure_void;
    } else {
        old_subnet = NULL;
    }

    if (new_subnet) {
        customer_gid = new_subnet->customer_gid;
    } else if (old_subnet) {
        customer_gid = old_subnet->customer_gid;
    }

    g_broker_multi_lan_feature_enable = zpn_broker_np_client_multilan_enabled(customer_gid);

    /*
     * the feature is disabled by default, once the feature is enabled,
     * upon each np_lan_subnets row callback, broker will check np_lan_subnets.lan_subnets column.added
     *
     * For migration, CP team will run a one time script to move existing data from np_lan_subnets.subnet to np_lan_subnets.lan_subnets.
     *
     * Scenario:
     * np_lan_subnets.subent = *******/24
     *
     * t1: feature disabled, broker is running and advertised *******/24 to all connected ZCC.
     * t2: one time script migrating np_lan_subnets.subnet to np_lan_subnets.lan_subnets .
     * t3: row callback triggers on backend, still consuming np_lan_subents.subnet, read no delta.
     * t4: feature enable
     * t5: nothing change.
     *
     * after above, if admin change np_lan_subnets.lan_subnets from *******/24 to *******/24, *******/24,
     * a row callback will trigger on broker, broker find the delta between old_subnets (which it recieved at t3) and new_subnets, find the delta of *******/24
     */
    if (g_broker_multi_lan_feature_enable) {
        if (new_subnet) {
            if ((new_subnet->lan_subnets == NULL || new_subnet->lan_subnets_count <= 0) ) {
                ZPN_DEBUG_BNC("Received new lan subnet (gid %" PRId64 ", customer gid %" PRId64 ") "
                                "np_lan_subnets.lan_subnets: %s, np_lan_subnets.lan_subnets_count: %d",
                                new_subnet->gid, new_subnet->customer_gid,
                                new_subnet->lan_subnets ? "not null" : "null", new_subnet->lan_subnets_count);
                return;
            }

            for (int i = 0; i < new_subnet->lan_subnets_count; i++) {
                /* bad data from db, should ignore */
                if (new_subnet->lan_subnets[i] == NULL) {
                    ZPN_LOG(AL_WARNING, "New LAN subnet received (gid: %" PRId64
                                        ", customer gid: %" PRId64 ", index: %d) has an uninitialized "
                                        "entry in np_lan_subnets.lan_subnets[%d]. Ignoring.",
                                        new_subnet->gid, new_subnet->customer_gid, i, i);
                    return;
                }
            }
        }
    } else {
        if (new_subnet && (new_subnet->subnet == NULL)) {
            ZPN_DEBUG_BNC("Received new lan subnet (gid %"PRId64", customer gid %"PRId64") but np_lan_subnets.subnet is NULL, ignoring",
                            new_subnet->gid, new_subnet->customer_gid);
            return;
        }
    }

    if (g_broker_multi_lan_feature_enable) {
        if (!new_subnet && old_subnet) {
            ips_to_delete = old_subnet->lan_subnets;
            ips_to_delete_count = old_subnet->lan_subnets_count;
            deleted = 1;
        } else if (new_subnet && !old_subnet) {
            ips_to_add = new_subnet->lan_subnets;
            ips_to_add_count = new_subnet->lan_subnets_count;
            added = 1;
        } else if (new_subnet && old_subnet) {
            res = np_lan_subnet_find_lan_subnets_delta_hash_table((const struct argo_inet **)new_subnet->lan_subnets, new_subnet->lan_subnets_count,
                                                                  (const struct argo_inet **)old_subnet->lan_subnets, old_subnet->lan_subnets_count,
                                                                  ips_to_add_holder, sizeof(ips_to_add_holder), &ips_to_add_count,
                                                                  ips_to_delete_holder, sizeof(ips_to_delete_holder), &ips_to_delete_count,
                                                                  old_subnet->gid, new_subnet->gid);
            if (res) {
                ZPN_LOG(AL_ERROR, "np_lan_subnet_find_lan_subnets_delta_hash_table find delta error %d, customer: %"PRId64"", res, client->customer_gid);
                return;
            }
            if (ips_to_add_count) {
                added = 1;
                ips_to_add = ips_to_add_holder;
            }

            if (ips_to_delete_count) {
                deleted = 1;
                ips_to_delete = ips_to_delete_holder;
            }

            ZPN_DEBUG_BNC("Received modifed subnet added_count: %d, deleted_count %d (gid %"PRId64", customer gid %"PRId64")",
                           ips_to_add_count, ips_to_delete_count, new_subnet->gid, new_subnet->customer_gid);
        }
    } else {
        if (!new_subnet && old_subnet) {
            /* Subnet has been deleted, remove it from hash table and inform client of deletion */
            deleted = 1;
        } else if (new_subnet && !old_subnet) {
            /* A new subnet has been added, add it to hash table and inform client of addition */
            added = 1;
        } else if (new_subnet && old_subnet) {
            /* new row arrived on subnet we have sent before, check if anything changed */
            if (!argo_inet_is_same(old_subnet->subnet, new_subnet->subnet)) {
                /* subnet has been modified */
                added = 1;
                deleted = 1;
            } else {
                /* nothing to do */
                ZPN_DEBUG_BNC("Received new lan subnet (gid %"PRId64", customer gid %"PRId64") but subnet did not change, skipping",
                                new_subnet->gid, new_subnet->customer_gid);
            }
        } else {
            /* nothing to do */
            ZPN_DEBUG_BNC("No new & old lan subnet");
        }
    }

    if (deleted) {
        if (g_broker_multi_lan_feature_enable) {
            for (int i = 0; i < ips_to_delete_count; i++) {
                struct argo_inet *subnet = ips_to_delete[i];
                zpn_broker_np_client_send_lan_subnet_each(client, 1 /* delete */, subnet, old_subnet->gid, 0);
            }
        } else {
            zpn_broker_np_client_send_lan_subnet_each(client, 1 /* delete */, old_subnet->subnet, old_subnet->gid, 0);
        }

        zhash_table_remove(client->lan_subnets_sent, &(old_subnet->gid), sizeof(old_subnet->gid), old_subnet_object);
        argo_object_release(old_subnet_object);
    }

    if (added) {

        if (new_subnet->customer_gid != client->customer_gid) {
            ZPN_LOG(AL_ERROR, "Cannot send lan subnet %"PRId64" to customer: %"PRId64" as subnet belongs to a different customer %"PRId64"",
                                new_subnet->gid, client->customer_gid, new_subnet->customer_gid);
            return;
        }

        if (g_broker_multi_lan_feature_enable) {
            for (int i = 0; i < ips_to_add_count; i++) {
                struct argo_inet *subnet = ips_to_add[i];
                zpn_broker_np_client_send_lan_subnet_each(client, 0 /* add */, subnet, new_subnet->gid, is_new_client);
            }
        } else {
            zpn_broker_np_client_send_lan_subnet_each(client, 0 /* add */, new_subnet->subnet, new_subnet->gid, is_new_client);
        }

        if (is_new_client) {
            if (!client->is_first_subnet_enqueued) {
                client->is_first_subnet_enqueued = 1;
                zpn_client_tracker_start(client->tracker, client_track_np_subnet);
            }
            if (!client->is_first_lan_subnet_enqueued) {
                client->is_first_lan_subnet_enqueued = 1;
                zpn_client_tracker_start(client->tracker, client_track_np_lan_subnet);
            }
            if (!client->is_last_lan_subnet_enqueued) {
                client->is_last_lan_subnet_enqueued = is_last_subnet ? 1 : 0;
            }

            if (g_broker_multi_lan_feature_enable) {
                client->stats.lan_subnet_sent_cnt_in += ips_to_add_count;
                if (!client->is_cstate_done) (zpn_broker_np_client_app_sent_stats[client->conn_thread_id].lan_subnet_sent_cnt_in)+=ips_to_add_count;
            } else {
                client->stats.lan_subnet_sent_cnt_in++;
                if (!client->is_cstate_done) zpn_broker_np_client_app_sent_stats[client->conn_thread_id].lan_subnet_sent_cnt_in++;
            }
        }

        struct argo_object *object = argo_object_create(np_lan_subnets_description, new_subnet);
        zhash_table_store(client->lan_subnets_sent, &(new_subnet->gid), sizeof(new_subnet->gid), 0, object);
    }
}

static void zpn_broker_np_client_send_dns_ns_record(struct zpn_client_np_app_state *client,
                                                    struct np_dns_ns_records *record,
                                                    int64_t is_new_client,
                                                    int is_last_record)
{
    struct argo_object *object_to_send = NULL;
    struct zpn_np_app_domain data;

    if (!client || client->deleted || !record) {
        ZPN_LOG(AL_ERROR, "Cannot send NP DNS record, client(%s), record(%s)",
                            (client == NULL) ? "not null" : "null",
                            (record == NULL) ? "not null" : "null");
        return;
    }

    for (int i = 0; i < record->fqdn_count; i++) {
        if (!record->fqdn[i]) continue;

        /* send the app to client, one domain at a time */
        memset(&data, 0, sizeof(data));
        data.domain = record->fqdn[i];
        data.nameservers = record->nameserver_ip;
        data.nameservers_count = record->nameserver_ip_count;
        data.deleted = 0;

        object_to_send = argo_object_create(zpn_np_app_domain_description, &data);
        if (!object_to_send) {
            ZPN_LOG(AL_ERROR, "Could not create object for NP DNS NS record gid %"PRId64" customer: %"PRId64"",
                                record->gid, client->customer_gid);
            return;
        }

        if (is_new_client) {
            if (!client->is_first_domain_enqueued) {
                client->is_first_domain_enqueued = 1;
                zpn_client_tracker_start(client->tracker, client_track_np_domain);
            }
            if (!client->is_last_domain_enqueued) {
                client->is_last_domain_enqueued = is_last_record ? 1 : 0;
            }
            client->stats.domain_sent_cnt_in++;
            if (!client->is_cstate_done) zpn_broker_np_client_app_sent_stats[client->conn_thread_id].domain_sent_cnt_in++;
        }

        CLIENT_HOLD_THREAD_CALL(client);
        if (0 != zevent_base_big_call(client->client_fohh_thread, domain_tx_cb, client, is_new_client, object_to_send, NULL, NULL, 0)) {
            ZPN_LOG(AL_ERROR, "Thread call failed for sending NP domain app - NS record gid %"PRId64" customer: %"PRId64"",
                            record->gid, client->customer_gid);
            argo_object_release(object_to_send);
            CLIENT_RELEASE_THREAD_CALL(client);
            return;
        } else {
            /* Success! object and client will be released in domain_tx_cb */
        }
    }
}

static void np_client_app_complete_cb(struct zevent_base    *base __attribute__((unused)),
                                      void                  *void_cookie,
                                      int64_t               int_cookie __attribute__((unused)))
{
    struct zpn_client_np_app_state *client = void_cookie;
    struct zpn_broker_client_fohh_state *c_state = NULL;
    char *tunnel_id = NULL;
    struct zpn_tlv *tlv;
    int res;

    if (!client || client->deleted) {
        ZPN_LOG(AL_ERROR, "np_client_app_complete_cb without client state");
        return;
    }

    tunnel_id = client->tunnel_id;
    if (zpn_broker_client_tunnel_c_state_lookup(&c_state, NULL, tunnel_id, strnlen(tunnel_id, sizeof(client->tunnel_id) - 1))) {
        ZPN_LOG(AL_ERROR, "Cannot look for c_state with tunnel id = %s in the lookup hash table, customer gid: %"PRId64"",
                            tunnel_id, client->customer_gid);
        goto done;
    }

    tlv = c_state_get_tlv(c_state);
    if (zpn_tlv_sanity_check(tlv) != ZPN_RESULT_NO_ERROR) {
        ZPN_LOG(AL_ERROR, "%s TLV no connection when sending np_client_app_complete, customer gid: %"PRId64"",
                            c_state->tunnel_id, c_state->customer_gid);
        goto done;
    }

    res = zpn_send_np_client_app_complete(tlv, 0,  NULL);
    if (res) {
        ZPN_LOG(AL_WARNING, "%s: overrun? Error sending np_client_app_complete to customer: %"PRId64" - %s",
                            c_state->tunnel_id,
                            c_state->customer_gid,
                            zpn_result_string(res));
    }

    zpn_client_tracker_end(client->tracker, client_track_np_subnet_complete, ZPN_RESULT_NO_ERROR);
    if (client->domains_initial_download_complete) {
        /* we have finished all np app download (subnets + domains) */
        zpn_client_tracker_end(client->tracker, client_track_np, ZPN_RESULT_NO_ERROR);
        client->stats.app_complete_us = epoch_us();
        snprintf(client->app_download_status_str, sizeof(client->app_download_status_str),
                "NP_APP_DOWNLOAD: initial download completed");
    }
done:
    CLIENT_RELEASE_THREAD_CALL(client);
}

static void client_subnet_download_cb(struct zevent_base *base __attribute__((unused)),
                                      void               *void_cookie,
                                      int64_t            int_cookie,
                                      void               *extra_cookie1,
                                      void               *extra_cookie2 __attribute__((unused)),
                                      void               *extra_cookie3 __attribute__((unused)),
                                      int64_t            extra_int_cookie __attribute__((unused)))
{
    struct zpn_client_np_app_state *client = void_cookie;
    struct subnet_gids *subnet_gids = extra_cookie1;
    int64_t is_new_client = int_cookie;

    if (!client || client->deleted) {
        ZPN_LOG(AL_ERROR, "client_subnet_download_cb without client state");
        return;
    }

    if (subnet_gids) {
        /* We have client subnets to download, send them */
        ZPATH_MUTEX_LOCK(&(client->client_subnets_sent_lock), __FILE__, __LINE__);
        for (int i = 0; i < subnet_gids->gids_used; i++) {
            zpn_broker_np_client_send_client_subnet(client, subnet_gids->gids[i], is_new_client, (i == subnet_gids->gids_used-1));
        }
        ZPATH_MUTEX_UNLOCK(&(client->client_subnets_sent_lock), __FILE__, __LINE__);
    }

    /* Send np_client_app_complete if we finish downloading all subnets */
    if (!client->client_subnets_initial_download_complete) {
        client->client_subnets_initial_download_complete = 1;
        client->stats.client_subnet_complete_us = epoch_us();
        snprintf(client->app_download_status_str, sizeof(client->app_download_status_str),
                        "NP_APP_DOWNLOAD: initial client subnet download completed");
        if (client->client_subnets_initial_download_complete && client->lan_subnets_initial_download_complete ) {
            /* we have finished initial subnet download, send complete message */
            zpn_client_tracker_start(client->tracker, client_track_np_subnet_complete);
            snprintf(client->app_download_status_str, sizeof(client->app_download_status_str),
                        "NP_APP_DOWNLOAD: initial subnet download completed");
            CLIENT_HOLD_THREAD_CALL(client);
            if (0 != zevent_base_call(client->client_fohh_thread, np_client_app_complete_cb, client, client->c_state_incarnation)) {
                ZPN_LOG(AL_ERROR, "%s Failed to thread call, cannot send np_client_app_complete to client, customer %"PRId64"",
                                    client->tunnel_id, client->customer_gid);
                CLIENT_RELEASE_THREAD_CALL(client);
            }
        }
    }

    zthread_heartbeat(NULL);
    if (subnet_gids) subnet_gids_release(subnet_gids);
    CLIENT_RELEASE_THREAD_CALL(client);
    return;
}

static void zpn_broker_np_client_download_client_subnets(struct zpn_client_np_app_state *client,
                                                         struct subnet_gids *subnet_gids,
                                                         int64_t is_new_client)
{
    if (!client || client->deleted) {
        ZPN_LOG(AL_ERROR, "Cannot send NP client subnets, client(%s)", (client == NULL) ? "null" : "not null");
        return;
    }
    client->stats.client_subnet_start_us = epoch_us();
    snprintf(client->app_download_status_str, sizeof(client->app_download_status_str),
                "NP_APP_DOWNLOAD: Started client subnets download to %s client", is_new_client ? "new" : "old");
    CLIENT_HOLD_THREAD_CALL(client);
    if (0 != zevent_base_big_call(client->calc_thread, client_subnet_download_cb, client, is_new_client, subnet_gids, NULL, NULL, 0)) {
        ZPN_LOG(AL_ERROR, "%s Failed to thread call - unable to download client subnets, customer %"PRId64"",
                            client->tunnel_id, client->customer_gid);
        if (subnet_gids) subnet_gids_release(subnet_gids);
        CLIENT_RELEASE_THREAD_CALL(client);
    }
}

static void lan_subnet_download_cb(struct zevent_base *base __attribute__((unused)),
                                   void               *void_cookie,
                                   int64_t            int_cookie,
                                   void               *extra_cookie1,
                                   void               *extra_cookie2 __attribute__((unused)),
                                   void               *extra_cookie3 __attribute__((unused)),
                                   int64_t            extra_int_cookie __attribute__((unused)))
{
    struct zpn_client_np_app_state *client = void_cookie;
    struct subnet_gids *subnet_gids = extra_cookie1;
    int64_t is_new_client = int_cookie;

    if (!client || client->deleted) {
        ZPN_LOG(AL_ERROR, "lan_subnet_download_cb without client state");
        return;
    }

    if (subnet_gids) {
        /* We have lan subnets to download, send them */
        ZPATH_MUTEX_LOCK(&(client->lan_subnets_sent_lock), __FILE__, __LINE__);
        for (int i = 0; i < subnet_gids->gids_used; i++) {
            zpn_broker_np_client_send_lan_subnet(client, subnet_gids->gids[i], is_new_client, (i == subnet_gids->gids_used-1));
        }
        ZPATH_MUTEX_UNLOCK(&(client->lan_subnets_sent_lock), __FILE__, __LINE__);
    }

    /* Send np_client_app_complete if we finish downloading all subnets */
    if (!client->lan_subnets_initial_download_complete) {
        client->lan_subnets_initial_download_complete = 1;
        client->stats.lan_subnet_complete_us = epoch_us();
        snprintf(client->app_download_status_str, sizeof(client->app_download_status_str),
                        "NP_APP_DOWNLOAD: initial lan subnet download completed");
        if (client->client_subnets_initial_download_complete && client->lan_subnets_initial_download_complete ) {
            /* we have finished initial subnet download, send complete message */
            zpn_client_tracker_start(client->tracker, client_track_np_subnet_complete);
            snprintf(client->app_download_status_str, sizeof(client->app_download_status_str),
                        "NP_APP_DOWNLOAD: initial subnet download completed");
            CLIENT_HOLD_THREAD_CALL(client);
            if (0 != zevent_base_call(client->client_fohh_thread, np_client_app_complete_cb, client, client->c_state_incarnation)) {
                ZPN_LOG(AL_ERROR, "%s Failed to thread call, cannot send np_client_app_complete to client, customer %"PRId64"",
                                    client->tunnel_id, client->customer_gid);
                CLIENT_RELEASE_THREAD_CALL(client);
            }
        }
    }

    zthread_heartbeat(NULL);
    if (subnet_gids) subnet_gids_release(subnet_gids);
    CLIENT_RELEASE_THREAD_CALL(client);
    return;
}

static void zpn_broker_np_client_download_lan_subnets(struct zpn_client_np_app_state *client,
                                                      struct subnet_gids *subnet_gids,
                                                      int64_t is_new_client)
{
    if (!client || client->deleted) {
        ZPN_LOG(AL_ERROR, "Cannot send NP lan subnets, client(%s)",
                            (client == NULL) ? "null" : "not null");
        return;
    }
    client->stats.lan_subnet_start_us = epoch_us();
    snprintf(client->app_download_status_str, sizeof(client->app_download_status_str),
                "NP_APP_DOWNLOAD: Started lan subnets download to %s client", is_new_client ? "new" : "old");
    CLIENT_HOLD_THREAD_CALL(client);
    if (0 != zevent_base_big_call(client->calc_thread, lan_subnet_download_cb, client, is_new_client, subnet_gids, NULL, NULL, 0)) {
        ZPN_LOG(AL_ERROR, "%s Failed to thread call - unable to download lan subnets, customer %"PRId64"",
                            client->tunnel_id, client->customer_gid);
        if (subnet_gids) subnet_gids_release(subnet_gids);
        CLIENT_RELEASE_THREAD_CALL(client);
    }
}

static void np_app_domain_complete_cb(struct zevent_base    *base __attribute__((unused)),
                                      void                  *void_cookie,
                                      int64_t               int_cookie __attribute__((unused)))
{
    struct zpn_client_np_app_state *client = void_cookie;
    struct zpn_broker_client_fohh_state *c_state = NULL;
    char *tunnel_id = NULL;
    struct zpn_tlv *tlv;
    int res;

    if (!client || client->deleted) {
        ZPN_LOG(AL_ERROR, "np_app_domain_complete_cb without client state");
        return;
    }

    tunnel_id = client->tunnel_id;
    if (zpn_broker_client_tunnel_c_state_lookup(&c_state, NULL, tunnel_id, strnlen(tunnel_id, sizeof(client->tunnel_id) - 1))) {
        ZPN_LOG(AL_ERROR, "Cannot look for c_state with tunnel id = %s in the lookup hash table, customer gid: %"PRId64"",
                            tunnel_id, client->customer_gid);
        goto done;
    }

    tlv = c_state_get_tlv(c_state);
    if (zpn_tlv_sanity_check(tlv) != ZPN_RESULT_NO_ERROR) {
        ZPN_LOG(AL_ERROR, "%s TLV no connection when sending np_app_domain_complete, customer gid: %"PRId64"",
                            c_state->tunnel_id, c_state->customer_gid);
        goto done;
    }

    res = zpn_send_np_app_domain_complete(tlv, 0,  NULL);
    if (res) {
        ZPN_LOG(AL_WARNING, "%s: overrun? Error sending np_app_domain_complete to customer: %"PRId64" - %s",
                            c_state->tunnel_id,
                            c_state->customer_gid,
                            zpn_result_string(res));
    }

    zpn_client_tracker_end(client->tracker, client_track_np_domain_complete, ZPN_RESULT_NO_ERROR);
    if (client->client_subnets_initial_download_complete && client->lan_subnets_initial_download_complete) {
        /* we have finished all np app download (subnets + domains) */
        zpn_client_tracker_end(client->tracker, client_track_np, ZPN_RESULT_NO_ERROR);
        client->stats.app_complete_us = epoch_us();
        snprintf(client->app_download_status_str, sizeof(client->app_download_status_str),
                "NP_APP_DOWNLOAD: initial download completed");
    }
done:
    CLIENT_RELEASE_THREAD_CALL(client);
}

static void dns_ns_record_download_cb(struct zevent_base *base __attribute__((unused)),
                                      void               *void_cookie,
                                      int64_t            int_cookie,
                                      void               *extra_cookie1 __attribute__((unused)),
                                      void               *extra_cookie2 __attribute__((unused)),
                                      void               *extra_cookie3 __attribute__((unused)),
                                      int64_t            extra_int_cookie __attribute__((unused)))
{
    struct zpn_client_np_app_state *client = void_cookie;
    struct np_dns_ns_records *records[MAX_NP_CLIENT_APPS];
    size_t records_count = MAX_NP_CLIENT_APPS;
    int64_t customer_gid;
    int is_new_client = int_cookie ? 1 : 0;
    int res = ZPN_RESULT_NO_ERROR;

    if (!client || client->deleted) {
        ZPN_LOG(AL_ERROR, "dns_ns_record_download_cb without client state");
        return;
    }

    customer_gid = client->customer_gid;

    snprintf(client->app_download_status_str, sizeof(client->app_download_status_str),
                "NP_APP_DOWNLOAD: Collecting all DNS records for sending to %s client", is_new_client ? "new" : "old");

    /* get all NP DNS NS records for this customer */
    res = np_dns_ns_records_get_by_customer_gid_immediate(customer_gid, records, &records_count);
    if (res) {
        if (res != ZPN_RESULT_NOT_FOUND) {
            ZPN_LOG(AL_ERROR, "Error fetching DNS NS records for customer %"PRId64": %s", customer_gid, zpn_result_string(res));
            CLIENT_RELEASE_THREAD_CALL(client);
            return;
        } else {
            ZPN_DEBUG_BNC("DNS NS records fetch for customer %"PRId64": got no record", customer_gid);
        }
    } else {
        /* success */
        ZPN_DEBUG_BNC("%"PRId64": Customer np_dns_ns_records_get_by_customer_gid complete, count=%d",
                            customer_gid, (int)records_count);
    }

    /* Send domains to client */
    for (int i = 0; i < records_count; i++) {
        zpn_broker_np_client_send_dns_ns_record(client, records[i], is_new_client, (i == records_count - 1));
    }
    snprintf(client->app_download_status_str, sizeof(client->app_download_status_str),
                "NP_APP_DOWNLOAD: DNS NS records pushed update to %s client", is_new_client ? "new" : "old");
    /* Send np_app_domain_complete everytime when we finish downloading */
    zpn_client_tracker_start(client->tracker, client_track_np_domain_complete);
    client->stats.domain_complete_us = epoch_us();
    CLIENT_HOLD_THREAD_CALL(client);
    if (0 != zevent_base_call(client->client_fohh_thread, np_app_domain_complete_cb, client, client->c_state_incarnation)) {
        ZPN_LOG(AL_ERROR, "%s Failed to thread call, cannot send np_app_domain_complete to client, customer %"PRId64"",
                            client->tunnel_id, client->customer_gid);
        CLIENT_RELEASE_THREAD_CALL(client);
    }
    CLIENT_RELEASE_THREAD_CALL(client);
    zthread_heartbeat(NULL);
    return;
}

static void zpn_broker_np_client_download_dns_ns_records(struct zpn_client_np_app_state *client, int64_t is_new_client)
{
    if (!client || client->deleted) {
        ZPN_LOG(AL_ERROR, "zpn_broker_np_client_download_dns_ns_records without client state");
        return;
    }
    client->stats.domain_start_us = epoch_us();
    snprintf(client->app_download_status_str, sizeof(client->app_download_status_str),
                "NP_APP_DOWNLOAD: Started DNS records download to %s client", is_new_client ? "new" : "old");
    CLIENT_HOLD_THREAD_CALL(client);
    if (0 != zevent_base_big_call(client->calc_thread, dns_ns_record_download_cb, client, is_new_client, NULL, NULL, NULL, 0)) {
        ZPN_LOG(AL_ERROR, "%s Failed to thread call - unable to download dns ns records, customer %"PRId64"",
                            client->tunnel_id, client->customer_gid);
        CLIENT_RELEASE_THREAD_CALL(client);
    }
}

static void cstate_np_app_stats_dump(struct zpath_debug_state             *request_state,
                                     struct zpn_broker_client_fohh_state  *c_state)
{
    struct zpn_client_np_app_state *client = NULL;
    char str[255] = "";

    client = c_state->np_app_state;
    if (!client || client->deleted) {
        ZDP("client NP app state is not initialized!\n");
        return;
    }

    /* This is called in a different thread for debug, we want to hold client until it is done */
    CLIENT_HOLD_THREAD_CALL(client);
    ZDP("\t{\n");
    ZDP("\t\tdebug str:\t%s\n", client->app_download_status_str);
    ZDP("\t\tapp_start_us:\t%s\n", epoch_to_time_str(client->stats.app_start_us, str, sizeof(str)));
    ZDP("\t\tapp_complete_us:\t%s\n", epoch_to_time_str(client->stats.app_complete_us, str, sizeof(str)));
    ZDP("\t\tclient_subnet_start_us:\t%s\n", epoch_to_time_str(client->stats.client_subnet_start_us, str, sizeof(str)));
    ZDP("\t\tclient_subnet_complete_us:\t%s\n", epoch_to_time_str(client->stats.client_subnet_complete_us, str, sizeof(str)));
    ZDP("\t\tlan_subnet_start_us:\t%s\n", epoch_to_time_str(client->stats.lan_subnet_start_us, str, sizeof(str)));
    ZDP("\t\tlan_subnet_complete_us:\t%s\n", epoch_to_time_str(client->stats.lan_subnet_complete_us, str, sizeof(str)));
    ZDP("\t\tdomain_start_us:\t%s\n", epoch_to_time_str(client->stats.domain_start_us, str, sizeof(str)));
    ZDP("\t\tdomain_complete_us:\t%s\n", epoch_to_time_str(client->stats.domain_complete_us, str, sizeof(str)));
    ZDP("\t\tclient_subnet_sent_cnt_in:\t%"PRId64"\n", client->stats.client_subnet_sent_cnt_in);
    ZDP("\t\tclient_subnet_sent_cnt_out:\t%"PRId64"\n", client->stats.client_subnet_sent_cnt_out);
    ZDP("\t\tlan_subnet_sent_cnt_in:\t%"PRId64"\n", client->stats.lan_subnet_sent_cnt_in);
    ZDP("\t\tlan_subnet_sent_cnt_out:\t%"PRId64"\n", client->stats.lan_subnet_sent_cnt_out);
    ZDP("\t\tdomain_sent_cnt_in:\t%"PRId64"\n", client->stats.domain_sent_cnt_in);
    ZDP("\t\tdomain_sent_cnt_out:\t%"PRId64"\n", client->stats.domain_sent_cnt_out);
    ZDP("\t\tfirst_subnet_sent_us:\t%s\n", epoch_to_time_str(client->stats.first_subnet_sent_us, str, sizeof(str)));
    ZDP("\t\tlatest_subnet_sent_us:\t%s\n", epoch_to_time_str(client->stats.latest_subnet_sent_us, str, sizeof(str)));
    ZDP("\t\tfirst_domain_sent_us:\t%s\n", epoch_to_time_str(client->stats.first_domain_sent_us, str, sizeof(str)));
    ZDP("\t\tlatest_domain_sent_us:\t%s\n", epoch_to_time_str(client->stats.latest_domain_sent_us, str, sizeof(str)));
    ZDP("\t}\n");
    CLIENT_RELEASE_THREAD_CALL(client);
}

static int
zbnc_dump_customer_np_client_stats(struct zpath_debug_state*    request_state,
                                   const char**                 query_values,
                                   int                          query_value_count __attribute__((unused)),
                                   void*                        cookie __attribute__((unused)))
{
    struct zpn_broker_client_fohh_state* c_state = NULL;
    struct np_customer_state *customer = NULL;
    struct zpn_client_np_app_state *client;
    int64_t customer_gid = 0;
    int64_t c_state_incarnation = 0;
    int res = ZPN_RESULT_NO_ERROR;

    if (!query_values[0]) {
        ZDP("customer id is required!\nUsage curl localhost:8000/zpn/broker/np/client_apps/dump?customer_gid=<id>\n");
        return ZPN_RESULT_NO_ERROR;
    } else {
        customer_gid = strtol(query_values[0], NULL, 0);
    }

    if (customer_gid <= 0) {
        ZDP("invalid customer_gid!\n");
        return ZPN_RESULT_NO_ERROR;
    }

    ZPATH_RWLOCK_RDLOCK(&customers_lock, __FILE__, __LINE__);
    customer = zhash_table_lookup(customers, &customer_gid, sizeof(customer_gid), NULL);
    ZPATH_RWLOCK_UNLOCK(&customers_lock, __FILE__, __LINE__);
    if (!customer) {
        ZDP("NP customer state %"PRId64" not yet initialized, no client connection from this customer yet\n", customer_gid);
        return ZPN_RESULT_NO_ERROR;
    }

    ZPATH_MUTEX_LOCK(&(customer->lock), __FILE__, __LINE__);
    TAILQ_FOREACH(client, &(customer->np_clients), list) {
        ZDP("{\n");
        if (!client || client->deleted) {
            ZDP("\tFound an already deleted NP client, this should never happen\n}\n");
            continue;
        }

        CLIENT_HOLD_THREAD_CALL(client);
        ZDP("\tNP client's tunnel ID: %s, debug str: %s, app download status %s, ref count: %d\n",
                        client->tunnel_id, client->debug_str, client->app_download_status_str, client->reference_count);
        // coverity[overrun-buffer-val] - False positive
        res = zpn_broker_client_tunnel_c_state_lookup(&c_state, &c_state_incarnation,
                                    client->tunnel_id, strnlen(client->tunnel_id, ZPN_TUNNEL_ID_BYTES_TEXT + 1));
        if (res || !c_state) {
            ZDP("\tNo client connection exists with tunnel id '%s'\n}\n", client->tunnel_id);
            CLIENT_RELEASE_THREAD_CALL(client);
            continue;
        }

        if (c_state_incarnation != c_state->incarnation ) {
            ZDP("\tc_state incarnation mismatch for tunnel id %s, the connection may be closed\n", query_values[0]);
        } else {
            cstate_np_app_stats_dump(request_state, c_state);
        }
        CLIENT_RELEASE_THREAD_CALL(client);
        ZDP("}\n");
    }
    ZPATH_MUTEX_UNLOCK(&(customer->lock), __FILE__, __LINE__);

    return ZPATH_RESULT_NO_ERROR;
}

static int zpn_broker_np_client_app_sent_stats_fill(void *cookie, int counter, void *structure_data)
{
    struct zpn_broker_np_client_app_sent_stats *stats = structure_data;
    int max_fohh_thread = fohh_thread_count();
    int res;

    res = argo_structure_array_add(zpn_broker_np_client_app_sent_stats_desc, &zpn_broker_np_client_app_sent_stats[0],
                                   max_fohh_thread, stats, sizeof(struct zpn_broker_np_client_app_sent_stats));
    if (res) {
        ZPN_LOG(AL_ERROR, "unable to add zpn broker np client app sent stats");
    }

    return ZPN_RESULT_NO_ERROR;
}

static int zpn_broker_np_client_debug_init()
{
    struct zpn_broker_np_client_app_sent_stats *app_sent_stats;
    int res;

    zpn_broker_np_client_app_stats_desc = argo_register_global_structure(ZPN_BROKER_NP_CLIENT_APP_STATS_HELPER);
    if (!zpn_broker_np_client_app_stats_desc) {
        ZPN_LOG(AL_ERROR, "could not register zpn_broker_np_client_app_stats description");
        return ZPN_RESULT_ERR;
    }

    zpn_broker_np_client_app_customer_log_desc = argo_register_global_structure(ZPN_BROKER_NP_CLIENT_APP_CUSTOMER_LOG_HELPER);
    if (!zpn_broker_np_client_app_customer_log_desc) {
        ZPN_LOG(AL_ERROR, "could not register zpn_broker_np_client_app_customer_log description");
        return ZPN_RESULT_ERR;
    }

    zpn_broker_np_client_app_sent_stats_desc = argo_register_global_structure(ZPN_BROKER_NP_CLIENT_APP_SENT_STATS_HELPER);
    if (!zpn_broker_np_client_app_sent_stats_desc) {
        ZPN_LOG(AL_ERROR, "could not register zpn_broker_np_client_app_sent_stats description");
        return ZPN_RESULT_ERR;
    }

    res = zpath_debug_add_read_command("Dump NP app download stats for all NP clients per customer",
                                  "/zpn/broker/np/client_apps/dump",
                                  zbnc_dump_customer_np_client_stats,
                                  NULL,
                                  "customer_gid", "Required, customer's ID",
                                  NULL);
    if (res) {
        ZPN_LOG(AL_ERROR, "could not init zbnc_dump_customer_np_client_stats");
        return res;
    }

    /* allocate stats buffer for the aggregated stats, this is a one time operation */
    app_sent_stats = ZPN_BNC_CALLOC(sizeof(*app_sent_stats));
    if (!argo_log_register_structure(argo_log_get("statistics_log"),
                                     "zpn_broker_np_client_app_sent_stats",
                                     AL_INFO,
                                     60*1000*1000,    /* 1 minute */
                                     zpn_broker_np_client_app_sent_stats_desc,
                                     app_sent_stats,
                                     1,
                                     zpn_broker_np_client_app_sent_stats_fill,
                                     NULL)) {
        ZPN_LOG(AL_CRITICAL, "Could not register broker np app sent stats - statistics_log not initialized?");
        return ZPN_RESULT_ERR;
    }

    return ZPN_RESULT_NO_ERROR;
}

void zpn_broker_np_client_cstate_dump(struct zpath_debug_state             *request_state,
                                      struct zpn_broker_client_fohh_state  *c_state,
                                      void                                 *cookie)
{
    struct zpn_broker_np_dump_clients_params *params = cookie;
    char ip_str[ARGO_INET_ADDRSTRLEN] = "";
    char str[255] = "";

    if (!c_state) {
        ZDP("no c_state!");
        return;
    }

    if ((params->long_term_gateway_gid > 0) && (params->long_term_gateway_gid != c_state->np.long_term_gateway_gid)) {
        return;
    }
    if ((params->long_term_subnet_gid > 0) && (params->long_term_subnet_gid != c_state->np.long_term_subnet_gid)) {
        return;
    }
    argo_inet_generate(ip_str, &(c_state->np.long_term_ip));
    if ((params->long_term_ip_str != NULL) && (strcmp(params->long_term_ip_str, ip_str) != 0)) {
        return;
    }

    ZDP("{\n");
    ZDP("\tNP client stats for tunnel ID: %s, customer: %"PRId64"\n", c_state->tunnel_id, c_state->customer_gid);
    ZDP("\tlong_term_gateway_gid:\t%"PRId64"\n", c_state->np.long_term_gateway_gid);
    ZDP("\tlong_term_subnet_gid:\t%"PRId64"\n", c_state->np.long_term_subnet_gid);
    ZDP("\tlong_term_ip:\t%s\n", ip_str);
    ZDP("\tlong_term_promoted_us:\t%s (%"PRId64")\n", epoch_to_time_str(c_state->np.long_term_promoted_us, str, sizeof(str)), c_state->np.long_term_promoted_us);
    ZDP("\tipars_conn_incarnation:\t%"PRId64"\n", c_state->np.ipars_conn_incarnation);
    ZDP("\tkeep_alive_timeout_s:\t%d\n", c_state->np.keep_alive_timeout_s);
    ZDP("\tkeep_alive_expires_at:\t%s (%"PRId64")\n", epoch_to_time_str(c_state->np.keep_alive_expires_at, str, sizeof(str)), c_state->np.keep_alive_expires_at);
    ZDP("\trequest_id_for_renew:\t%"PRId64"\n", c_state->np.request_id_for_renew);
    cstate_np_app_stats_dump(request_state, c_state);
    ZDP("}\n");
}


int zpn_broker_client_np_apps_done(struct zpn_broker_client_fohh_state *c_state)
{
    struct zpn_client_np_app_state *client = c_state->np_app_state;

    ZPN_DEBUG_BNC("%s: Done requested", client->debug_str);

    client->is_cstate_done = 1;

    CLIENT_HOLD_THREAD_CALL(client);
    zevent_base_call(client->calc_thread, np_client_delete_cb, client, 0);

    c_state->np_app_state = NULL;
    c_state->np_app_stats = NULL;

    /* decrease reference_count because initial state has ref count 1, now we decrease it to 0 so that it is freed */
    CLIENT_RELEASE_CSTATE(client);

    return ZPATH_RESULT_NO_ERROR;
}

int zpn_broker_np_client_np_apps_start(struct zpn_broker_client_fohh_state *c_state)
{
    if (!np_is_feature_enabled(c_state->customer_gid)) {
        ZPN_LOG(AL_INFO, "Skipping np client init as feature state is disabled for customer %"PRId64"", c_state->customer_gid);
        return ZPN_RESULT_NO_ERROR;
    }

    struct zpn_client_np_app_state *client = zpn_broker_client_create_np_app_state(c_state);
    struct subnet_gids *client_subnet_gids = NULL;
    struct subnet_gids *lan_subnet_gids = NULL;

    if (!client) return ZPATH_RESULT_ERR;

    zpn_client_tracker_start(client->tracker, client_track_np);
    client->stats.app_start_us = epoch_us();

    client_subnet_gids = generate_all_client_subnet_gids(client->customer);
    zpn_broker_np_client_download_client_subnets(client, client_subnet_gids, 1/*is new client*/);

    lan_subnet_gids = generate_all_lan_subnet_gids(client->customer);
    zpn_broker_np_client_download_lan_subnets(client, lan_subnet_gids, 1/*is new client*/);

    zpn_broker_np_client_download_dns_ns_records(client, 1/*is new client*/);

    /* We have hold the client when creating app state, hence releasing it */
    np_client_release(&client);

    return ZPATH_RESULT_NO_ERROR;
}

int zpn_broker_np_client_register_for_apps(struct zpn_broker_client_fohh_state *c_state)
{
    int res = ZPN_RESULT_NO_ERROR;

    if (!c_state) {
        ZPN_LOG(AL_ERROR, "No c_state when registering NP app");
        return ZPN_RESULT_ERR;
    }

    if (!np_is_feature_enabled(c_state->customer_gid)) {
        ZPN_LOG(AL_INFO, "Skipping NP client app registration as feature state is disabled for customer %"PRId64"", c_state->customer_gid);
        return ZPN_RESULT_NO_ERROR;
    }

    if (c_state->np_app_state) {
        // Normal
    } else {
        res = zpn_broker_np_client_np_apps_start(c_state);
        if (res) {
            ZPN_LOG(AL_ERROR, "%s Failed to start NP app download, customer_gid: %"PRId64"",
                                c_state->tunnel_id, c_state->customer_gid);
        } else {
            ZPN_LOG(AL_NOTICE, "%s Started NP app download, customer_gid: %"PRId64"",
                                c_state->tunnel_id, c_state->customer_gid);
        }
    }

    return res;
}

int16_t zpn_broker_np_get_keep_alive_timeout_s(int64_t customer_gid)
{
    const struct np_customer_state *customer = get_np_customer_state(customer_gid);
    const int64_t value = customer ?
                          customer->config_keep_alive_timeout_s :
                          zpn_broker_np_init_keep_alive_timeout_s(customer_gid);
    return (int16_t)value;
}

int16_t zpn_broker_np_get_long_term_expiry_hrs(int64_t customer_gid)
{
    const struct np_customer_state *customer = get_np_customer_state(customer_gid);
    const int64_t value = customer ?
                          customer->config_long_term_expiry_hrs :
                          zpn_broker_np_init_long_term_expiry_hrs(customer_gid);
    return (int16_t)value;
}

int16_t zpn_broker_np_get_feature_status_incarnation(int64_t customer_gid)
{
    const struct np_customer_state *customer = get_np_customer_state(customer_gid);
    return customer ? customer->feature_status_incarnation : 0;
}

static int zpn_broker_np_inc_feature_status_incarnation_each(void *cookie __attribute__((unused)),
                                                             void *object,
                                                             void *key __attribute__((unused)),
                                                             size_t key_len __attribute__((unused)))
{
    struct np_customer_state *customer = object;
    if (customer) {
        customer->feature_status_incarnation++;
    }
    return ZPATH_RESULT_NO_ERROR;
}

void zpn_broker_np_inc_feature_status_incarnation(int64_t customer_gid)
{
    if (customer_gid) {
        struct np_customer_state *customer = get_np_customer_state(customer_gid);
        if (customer) {
            customer->feature_status_incarnation++;
        }
    } else {
       zhash_table_walk(customers, NULL, zpn_broker_np_inc_feature_status_incarnation_each, NULL);
    }
}

int zpn_broker_np_client_init()
{
    int res;

    res = zpath_debug_add_allocator(&zpn_broker_np_client_allocator, "zpn_bnc");
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not add zpn_bnc allocator");
        return res;
    }

    customers = zhash_table_alloc(&zpn_broker_np_client_allocator);
    customers_lock = ZPATH_RWLOCK_INIT;

    res = zpn_broker_np_client_debug_init();
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not init broker np client debug module");
        return res;
    }

    res = np_client_subnets_table_init(NULL, 0, zpn_bnc_np_client_subnets_row_callback, NULL, 0);
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not init np_client_subnets table");
        return res;
    }

    res = np_lan_subnets_table_init(NULL, 0, zpn_bnc_np_lan_subnets_row_callback, NULL, 0);
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not init np_lan_subnets table");
        return res;
    }

    res = np_dns_ns_records_table_init(NULL, 0, zpn_bnc_np_dns_ns_records_row_callback, 0);
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not init np_client_subnets table");
        return res;
    }

    return ZPN_RESULT_NO_ERROR;
}
