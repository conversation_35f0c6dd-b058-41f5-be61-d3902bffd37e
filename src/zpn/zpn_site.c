/*
 * zpn_site.c. Copyright (C) 2024 Zscaler Inc. All Rights Reserved.
 */

#include "wally/wally.h"
#include "zpath_lib/zpath_app.h"

#include "zpath_lib/zpath_debug.h"

#include "zpn/zpn_lib.h"

#include "zpn/zpn_site.h"
#include "zpn/zpn_site_compiled.h"


struct argo_structure_description *zpn_site_description = NULL;

static struct wally_index_column **zpn_site_gid_column = NULL;
static struct wally_index_column **zpn_site_customer_gid_column = NULL;
static void zpn_site_row_fixup(struct argo_object *row)
{
    struct zpn_site *site = row->base_structure_void;
    if(site->scope_gid == 0) site->scope_gid = site->customer_gid;

    if (zpn_debug_get(ZPN_DEBUG_SITE_CONTROLLER_IDX)) {
        char dump[8000];
        if (argo_object_dump(row, dump, sizeof(dump), NULL, 0) == ARGO_RESULT_NO_ERROR) {
            ZPN_DEBUG_SITEC("Row callback: %s", dump);
        }
    }
}

static int zpn_site_row_callback(void *cookie,
                                 struct wally_registrant *registrant,
                                 struct wally_table *table,
                                 struct argo_object *previous_row,
                                 struct argo_object *row,
                                 int64_t request_id)
{
    if (zpn_debug_get(ZPN_DEBUG_SITE_CONTROLLER_IDX)) {
        char dump[8000];
        if (argo_object_dump(row, dump, sizeof(dump), NULL, 0) == ARGO_RESULT_NO_ERROR) {
            ZPN_DEBUG_SITEC("Row callback: %s", dump);
        }
    }

    return WALLY_RESULT_NO_ERROR;
}

int zpn_site_init(struct wally *single_tenant_wally,
                  int64_t single_tenant_gid,
                  wally_row_callback_f *row_cb,
                  int single_tenant_fully_loaded,
                  int register_with_zpath_table)
{
    int res;

    zpn_site_description = argo_register_global_structure(ZPN_SITE_HELPER);
    if (!zpn_site_description) {
        ZPN_LOG(AL_ERROR, "No description: zpn_site_init");
        return ZPN_RESULT_ERR;
    }

    if (!row_cb) {
        row_cb = zpn_site_row_callback;
    }
    if (single_tenant_wally) {
        struct wally_table *table;
        int shard_index = ZPATH_SHARD_FROM_GID(single_tenant_gid);

        if (single_tenant_fully_loaded) {
            /* This is generally used by site controller */
            res = zpath_app_fully_loaded_customer_table(&table,
                                                        ZPATH_GID_GET_CUSTOMER_GID(single_tenant_gid),
                                                        single_tenant_wally,
                                                        zpn_site_description,
                                                        row_cb,
                                                        NULL,
                                                        zpn_site_row_fixup,
                                                        register_with_zpath_table);
            if (res) {
                ZPN_LOG(AL_ERROR, "Could not get zpn_site table fully loaded");
                return ZPN_RESULT_ERR;
            }
        } else {
            /* This is generally used by connectors */
            table = wally_table_create(single_tenant_wally,
                                       1,
                                       zpn_site_description,
                                       row_cb,
                                       NULL,
                                       1,
                                       1,
                                       zpn_site_row_fixup);
        }

        if (!table) {
            ZPN_LOG(AL_ERROR, "Could not get table");
            return ZPN_RESULT_ERR;
        }

        zpn_site_gid_column = ZPN_CALLOC(ZPATH_MAX_SHARDS * sizeof(*zpn_site_gid_column));
        zpn_site_customer_gid_column = ZPN_CALLOC(ZPATH_MAX_SHARDS * sizeof(*zpn_site_customer_gid_column));

        zpn_site_gid_column[shard_index] = wally_table_get_index(table, "gid");
        if (!zpn_site_gid_column[shard_index]) {
            ZPN_LOG(AL_ERROR, "Could not get column");
            return ZPN_RESULT_ERR;
        }

        zpn_site_customer_gid_column[shard_index] = wally_table_get_index(table, "customer_gid");
        if (!zpn_site_customer_gid_column[shard_index]) {
            ZPN_LOG(AL_ERROR, "Could not get column");
            return ZPN_RESULT_ERR;
        }
    } else {
        res = zpath_app_fully_loaded_sharded_table(zpn_site_description,
                                                   row_cb,
                                                   NULL,
                                                   zpn_site_row_fixup);
        if (res) {
            return res;
        }

        zpn_site_gid_column = zpath_app_get_sharded_index("zpn_site", "gid");
        if (!zpn_site_gid_column) {
            ZPN_LOG(AL_ERROR, "Could not get zpn_site_gid_column");
            return ZPN_RESULT_ERR;
        }

        zpn_site_customer_gid_column = zpath_app_get_sharded_index("zpn_site", "customer_gid");
        if (!zpn_site_customer_gid_column) {
            ZPN_LOG(AL_ERROR, "Could not get zpn_site_customer_gid_column");
            return ZPN_RESULT_ERR;
        }
    }

    return ZPN_RESULT_NO_ERROR;
}


int zpn_site_get_by_gid(int64_t gid,
                        struct zpn_site **site,
				        int register_on_miss,
                        wally_response_callback_f callback_f,
                        void *callback_cookie,
                        int64_t callback_id)
{
    int res;
    size_t row_count = 1;
    int shard_index = ZPATH_SHARD_FROM_GID(gid);

    res = wally_table_get_rows_fast(zpn_site_gid_column[shard_index],
                                    &gid,
                                    sizeof(gid),
                                    (void **) site,
                                    &row_count,
                                    register_on_miss,
                                    callback_f,
                                    callback_cookie,
                                    callback_id);
    return res;
}

int zpn_site_get_by_customer_gid(int64_t customer_gid,
                                 struct zpn_site **site,
                                 size_t *row_count,
                                 wally_response_callback_f callback_f,
                                 void *callback_cookie,
                                 int64_t callback_id)
{
    int res;
    int shard_index = ZPATH_SHARD_FROM_GID(customer_gid);

    res = wally_table_get_rows_fast(zpn_site_customer_gid_column[shard_index],
                                    &customer_gid,
                                    sizeof(customer_gid),
                                    (void **) site,
                                    row_count,
                                    1,
                                    callback_f,
                                    callback_cookie,
                                    callback_id);
    return res;
}
