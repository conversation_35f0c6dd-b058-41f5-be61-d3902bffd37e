/*
 * zpn_broker.c. Copyright (C) 2014 Zscaler, Inc. All Rights Reserved.
 */

#include "zthread/zthread.h"
#include "argo/argo_hash.h"

#include "zcrypto_lib/zcrypto_lib.h"
#include "zpath_lib/zpath_local.h"
#include "zpath_lib/zpa_cloud_config.h"
#include "zpath_lib/zpath_instance.h"
#include "zpath_lib/zpath_cloud.h"
#include "zpath_lib/zpath_customer.h"
#include "zpath_lib/zpath_log_store.h"
#include "zpath_lib/zpath_app.h"
#include "zpath_lib/zpath_debug.h"
#include "zpath_lib/zpath_app_debug.h"
#include "zpath_lib/zpath_domain_lookup.h"
#include "zpath_lib/zpath_config_override.h"
#include "zpath_lib/zpath_config_override_desc.h"
#include "zpath_lib/zpath_system.h"
#include "zpn_event/zpn_event.h"

#include "ztimer/ztimer_wheel.h"

#include "fohh/fohh_log.h"

#include <event2/event.h>
#include <event2/listener.h>
#include <event2/bufferevent.h>
#include <event2/thread.h>
#include <event2/buffer.h>

#include "zpath_lib/zpath_instance_group.h"
#include "zpath_lib/zpath_partition.h"
#include "zpath_lib/zpath_instance_group_partition.h"
#include "zpath_lib/zpath_instance_partition_override.h"
#include "zpath_lib/zpath_customer_partition_override.h"
#include "zpath_lib/zpath_partition_common.h"
#include "zpath_lib/zpath_system_stats.h"

#include <sys/queue.h>
#ifdef __linux__
#include <malloc.h>
#endif
#include "zsaml/zsaml.h"
#include "zcdns/zcdns.h"
#include "wally/wally_postgres.h"

#include "zpn_pcap/zpn_pcap_group.h"

#include "zradix/zradix.h"
#include "fohh/fohh.h"
#include "np_lib/np.h"
#include "zpath_lib/zpath_et_userdb.h"
#include "zpath_lib/zpath_et_customer_userdb.h"
#include "fohh/http_parser.h"
#include "zpath_lib/zpath_assert.h"

#include "zpn/zpn_lib.h"
#include "zpn/zpn_rpc.h"

#include "zpn/zpn_policy_engine.h"
#include "zpn/zpn_policy_engine_debug.h"
#include "zpn/zpn_broker.h"
#include "zpn/zpn_broker_private.h"

#include "zpn/zpn_broker_assistant.h"
#include "zpn/zpn_broker_pbroker.h"
#include "zpn/zpn_broker_client.h"

#include "zpn/zpn_broker_dispatch.h"
#include "zpn/zpn_broker_mtunnel.h"

#include "zpn/zpn_assistant_table.h"
#include "zpn/zpn_private_broker_table.h"
#include "zpn/zpn_assistant_version.h"
#include "zpn/zpn_sub_module_upgrade.h"
#include "zpn/zpn_rule_to_pse_group.h"
#include "zpn/zpn_step_up_auth_level.h"
#include "zpn/zpn_rule_to_step_up_auth_level_mapping.h"
#include "zpn/zpn_svcp_profile.h"
#include "zpn/zpn_assistant_group.h"
#include "zpn/zpn_assistantgroup_assistant_relation.h"
#include "zpn/zpn_server_group.h"
#include "zpn/zpn_server_group_assistant_group.h"
#include "zpn/zpn_app_group_relation.h"
#include "zpn/zpn_servergroup_server_relation.h"
#include "zpn/zpn_application.h"
#include "zpn/zpn_application_server.h"
#include "zpn/zpn_application_group.h"
#include "zpn/zpn_application_group_application_mapping.h"
#include "zpn/zpn_application_domain.h"
#include "zpn/zpn_broker_load.h"
#include "zpn/zbalance/zpn_balance_redirect.h"
#include "zpn/zpn_broker_partition_stats.h"
#include "zpn/zpn_broker_client_apps.h"
#include "zpn/zpn_broker_client_apps_debug.h"
#include "zpn/zpn_client_tracker.h"
#include "zpn/zpn_client_less.h"
#include "zpn/zpn_broker_stepup_auth.h"

#include "zpn/zpn_znf.h"
#include "zpn/zpn_znf_group.h"
#include "zpn/zpn_znf_to_group.h"
#include "zpn/zpn_branch_connector.h"
#include "zpn/zpn_location.h"
#include "zpn/zpn_workload_tag_group.h"
#include "zpn/zpn_c2c_client_registration.h"

#include "zpn/zpn_signing_cert.h"
#include "zpn/zpn_issuedcert.h"
#include "zpn/zpn_client_table.h"
#include "zpn/zpn_idp.h"
#include "zpn/zpn_idp_cert.h"

#include "zpn/zpn_policy_set.h"
#include "zpn/zpn_rule.h"
#include "zpn/zpn_rule_condition_operand.h"
#include "zpn/zpn_rule_condition_set.h"
#include "zpn/zpn_rule_to_assistant_group.h"
#include "zpn/zpn_rule_to_server_group.h"
#include "zpn/zpn_rule_to_location_group.h"
#include "zpn/zpn_rule_to_location.h"
#include "zpn/zpn_location_group_to_location.h"
#include "zpn/zpn_saml_attrs.h"
#include "zpn/zpn_scim_attr_header.h"
#include "zpn/zpn_customer_application_group.h"
#include "zpn/zpn_approval.h"
#include "zpn/zpn_approval_mapping.h"
#include "zpn/zpn_broker_proxy.h"
#include "zpn/zpn_shared_customer_domain.h"
#include "zpath_misc/zsysinfo.h"
#include "zpn/zpn_fohh_worker.h"
#include "zpn/zpn_broker_dns.h"
#include "zpn/zpn_siem.h"
#include "zpn/zpn_siem_tx.h"
#include "zpn/zpn_broker_siem.h"
#include "zpn/zpn_broker_common.h"
#include "zpn/zpn_pbroker_monitor.h"
#include "zpn/zpn_private_broker_load_table.h"
#include "zpn/zpn_private_broker_version.h"
#include "zpn/zpn_sub_module_upgrade.h"
#include "zpn/zpn_posture_profile.h"
#include "zpn/zpn_trusted_network.h"
#include "zpn/zpn_customer.h"
#include "zpath_lib/zpath_et_zone.h"
#include "zpath_lib/zpath_et_customer_zone.h"
#include "zpath_lib/zpath_et_service_endpoint.h"
#include "zpath_lib/et_geoip_override.h"
#include "zpath_lib/zpath_partition_profile.h"

#include "zpn/zpn_posture_profile.h"
#include "zpn/zpn_trusted_network.h"
#include "zpn/zpn_customer.h"
#include "zpn/zpn_session_postures.h"
#include "zpn/zpn_session_aggregate_store.h"
#include "zpn/zpn_pbroker_group.h"
#include "zpn/zpn_pbroker_to_group.h"
#include "zpn/zpn_broker_assert.h"
#include "zpn/zpn_private_broker_site.h"
#include "zpn/zpn_private_broker_util.h"
#include "zpn/zpn_privatebrokergroup_trustednetwork_mapping.h"
#include "zpn/zpn_pbrokergroup_pbroker_relation.h"
#include "zpn/zpn_broker_client_path_cache.h"
#include "zpn/zpn_broker_client_path_cache_cfg.h"
#include "zpn/zpn_zrdt_debug.h"
#include "zpn/zpn_rate_limit.h"
#include "zpath_lib/zpath_config_override.h"
#include "zpn/zpn_machine_group.h"
#include "zpn/zpn_machine_table.h"
#include "zpn/zpn_machine_to_group.h"
#include "zpn/zpn_scim_group.h"
#include "zpn/zpn_scim_user.h"
#include "zpn/zpn_scim_user_attribute.h"
#include "zpn/zpn_scim_user_group.h"
#include "zpn/zpn_user_risk.h"
#include "zpn/zpn_icmp_rate_limit.h"
#include "zpn/zpn_icmp_debug_interface.h"
#include "zpn/zpn_aae.h"
#include "zpn/zpn_aae_profile_conclusion.h"

#include "zpn/zpn_zrdt_client.h"
#include "zpn/zpn_debug.h"
#include "zpn/zpn_cbi_mapping.h"
#include "zpn/zpn_cbi_profile.h"
#include "zpn_inspection/zpn_transform_stats.h"
#include "zpn_inspection/zpn_pipeline_stats.h"
#include "zpn/zpn_broker_transit.h"
#include "zpath_lib/et_translate_wally.h"
#include "zpn/zpn_customer_config.h"
#include "zpn/zpn_broker_client_private.h"
#include "zpn/zpn_broker_client.h"

#include "zpn/zpn_broker_maintenance.h"
#include "zpn/zpn_pb_client.h"

#include "zpn/zpn_broker_cryptosvc.h"
#include "zpn/zpn_fohh_client.h"
#include "zpath_lib/zpath_config_override_keys.h"

#include "zpath_lib/zpath_partition_common.h"
#include "zpath_misc/zpath_dta_checkpoint.h"
#include "zpn/zpn_file_fetch.h"
#include "zpn/zpn_broker_ipars.h"
#include "np_lib/np.h"
#include "zpn/zpn_broker_np.h"
#include "zpn/zpn_ipars.h"
#include "zpn/zpn_customer_resiliency_settings.h"
#include "zpn/zpn_mconn_fohh_tlv.h"
#include "zpn/zbalance/zpn_balance_private.h"
#include "zpn/zpn_broker_client_neg_path_cache.h"
#include "zpath_lib/zpath_config_override_desc.h"
#include "zpn/zpn_policy_overrides.h"
#include "zpn/zpn_broker_config_override_desc.h"
#include "zpn/zpn_ddil_config.h"
#include "zpn/zpn_site.h"
#include "zpn/zpn_sitec_table.h"
#include "zpn/zpn_sitec_group.h"
#include "zpn/zpn_sitec_to_group.h"
#include "zpn/zpn_sitec_version.h"
#include "zpn/zpn_broker_sitec.h"
#include "zpath_lib/zpath_service.h"
#include "zpn/zpn_broker_natural.h"
#include "zpn/zins_inspection/zins_inspection.h"
#include "zpn/zpn_eas/zpn_eas.h"
#include "zpn/zpn_broker_dispatch_c2c_client_check.h"
#include "zpn/zpn_version_control/zpn_version_control.h"
#include "zpn/zpn_broker_pm.h"
#include "zpn/zpn_vdi/zpn_vdi.h"
#include "zpn/zpn_firedrill_site.h"

#include "zpn/zpn_managed_chrome_extension.h"
#include "zpn/zpn_managed_browser_profile.h"

#ifdef ZPN_TESTING
#include "test_misc/testing_macros.h"
#include "zpn/gtests/zpn_broker_maintanence_auth_log_tests/test_headers/zpn_broker_maintanence_auth_log_tests_weak_headers.h"
#else
#include "test_misc/production_macros.h"
#endif // ZPN_TESTING

/* stats app_thread queue */
struct app_thread_stats {                              /* _ARGO: object_definition */
    int64_t app_thread_high_priority_enqueue_count;    /* _ARGO: integer */
    int64_t app_thread_high_priority_dequeue_count;    /* _ARGO: integer */
    int64_t app_thread_low_priority_enqueue_count;     /* _ARGO: integer */
    int64_t app_thread_low_priority_dequeue_count;     /* _ARGO: integer */
    int64_t app_thread_low_priority_q_wait_time;            /* _ARGO: integer */
    int64_t app_thread_high_priority_q_wait_time;           /* _ARGO: integer */
    int64_t app_thread_high_priority_q_wait_time_gt_2s;     /* _ARGO: integer */
    int64_t app_thread_high_priority_q_wait_time_gt_5s;     /* _ARGO: integer */
    int64_t app_thread_high_priority_q_wait_time_gt_10s;    /* _ARGO: integer */
};

/*
* Stats related to broker zpm conn stats
*/
struct zpn_broker_zpm_stats {         /* _ARGO: object_definition */
    int64_t connect_count;            /* _ARGO: integer */
    int64_t disconnect_count;         /* _ARGO: integer */
    int64_t connect_attempt_count;    /* _ARGO: integer */
};

/* Broker's health monitor module stats */
struct zpn_broker_zhm_stats {                   /* _ARGO: object_definition */
    int64_t all;                                /* _ARGO: integer */
    int64_t lookups;                            /* _ARGO: integer */
    int64_t ok;                                 /* _ARGO: integer */
    int64_t fail;                               /* _ARGO: integer */
    int64_t msg_id;                             /* _ARGO: integer */
    int64_t zpm_connects;                       /* _ARGO: integer */
};

/* Broker's ZPM module abort and kill signal stats */
struct zpn_broker_zpm_abrt_kill_stats {         /* _ARGO: object_definition */
    int64_t sig_abrt;                           /* _ARGO: integer */
    int64_t sig_kill;                           /* _ARGO: integer */
    int64_t msg_id;                             /* _ARGO: integer */
};

struct zpn_broker_bootup_stats_ctx zpn_broker_global_bootup_stats[] = {
    {"Initial Setup", 0, 0},                        // stage 0
    {"Zpath App Init", 0, 0},                       // stage 1
    {"Zthread Init", 0, 0},                         // stage 2
    {"Database Tables Init", 0, 0},                 // stage 3
    {"Broker Pre-Init Features", 0, 0},             // stage 4
    {"Broker Core Init", 0, 0},                     // stage 5
    {"Broker Post-Init Features", 0, 0},            // stage 6
    {"Network Listen Init", 0, 0}                   // stage 7
};

int zpn_broker_dump_bootup_stats(struct zpath_debug_state *request_state,
                                 const char **query_values, int query_value_count,
                                 void *cookie);

int zpn_pbroker_firedrill_get_config(struct zpn_firedrill_site **firedrill_config);
int zpn_pbroker_firedrill_start(int64_t firedrill_interval);


static struct argo_structure_description *zpn_broker_zhm_stats_description;
static struct zpn_broker_zhm_stats zhm_stats;
struct zpn_broker_zpm_stats g_zpn_broker_zpm_conn_stats = {0};

static struct argo_structure_description *zpn_broker_zpm_abrt_kill_stats_description;
static struct zpn_broker_zpm_abrt_kill_stats zpm_abrt_kill_stats;

#include "zpn/zpn_broker_compiled_c.h"
#include "zpn/zpn_scope.h"
#include "zpn/zpn_scope_engine.h"

#define DIAGNOSTICS_CMD_THRESHOLD 80
#define UPTIME_INFO_MAX 200
#define PRA_JIT_CATEGORY "ZPA Privileged Remote Access Just In Time Privileged Approvals"
#define PRA_CATEGORY  "ZPA Privileged Remote Access"
#define NATURAL_RATE_LIMIT_STR_VALID_CB(f)\
         .str_valid_cb = (f),             \
         .str_valid_cb_name= (#f)

int natural_log_throttle_err_str_validation(const char *value_str){
    return 1;
}

int64_t natural_trans_log_limiting_global_time =  NATURAL_TRANS_LOG_RATE_LIMIT_DEFAULT_TIME;
int64_t natural_trans_log_limiting_hard_disable_flag = NATURAL_TRANS_LOG_RATE_LIMITTING_DEFAULT;
int64_t g_step_up_auth_hard_disable_flag = ZPN_BROKER_STEP_UP_AUTH_FEATURE_HARD_DISABLED_DEFAULT;
int64_t *app_thread_num = NULL;
int64_t g_app_thread_hb_override_time = APP_THREAD_HEARTBEAT_OVERRIDE_DEFAULT;

struct zpn_broker broker;
int64_t broker_instance_id = 0;
int64_t ops_customer_gid;
int64_t use_custom_app_thread;

int is_static_wally_enabled = ZPN_PSE_REGISTRATION_WALLY_ENABLED_DEFAULT;

/* Broker status */
enum zpn_broker_status broker_status = INVALID;

/* broker termination context */
struct zpn_broker_termination_context g_broker_termination_ctx = {zpath_tc_invalid, NULL, -1};
int g_broker_load_update_blocking = 0;

/* will be set from termination thread ; once we identify broker status is TERMINATING */
int g_broker_termination_ready = 0;

/* we are really TERMINATING, notify term. thread that we are going down */
int g_broker_termination_notify = 0;

struct zcdns *broker_zcdns = NULL;
int broker_autotune = 0;

struct fohh_generic_server *sni_server = NULL;

static struct zthread_info *tickle_me = NULL;
static struct event_base *zbh_base = NULL;
struct event_base *broker_timer_base = NULL;

/* Global variables used for adaptive load monitoring
*/
static struct event *zbl_adaptive_timer = NULL;
static struct zpn_broker_adaptive_load_config zbal_config_obj;
static struct zpn_broker_adaptive_load_data zbal_data_obj;

struct event_base *zpn_broker_log_stats_event_base;

struct ztimer_wheel *timer_wheel = NULL;

struct argo_log_collection *zpn_transaction_collection = NULL;
struct argo_log_collection *zpn_meta_transaction_collection = NULL;
struct argo_log_collection *zpn_health_collection = NULL;
struct argo_log_collection *zpn_auth_collection = NULL;
struct argo_log_collection *zpn_ast_auth_collection = NULL;
struct argo_log_collection *zpn_pb_auth_collection = NULL;
struct argo_log_collection *zpn_sc_auth_collection = NULL;
struct argo_log_collection *zpn_dns_collection = NULL;
struct argo_log_collection *zpn_ast_waf_collection = NULL;
struct argo_log_collection *zpn_ast_waf_api_collection = NULL;
struct argo_log_collection *zpn_ast_app_inspection_collection = NULL;
struct argo_log_collection *zpn_ast_krb_inspection_collection = NULL;
struct argo_log_collection *zpn_ast_ldap_inspection_collection = NULL;
struct argo_log_collection *zpn_ast_smb_inspection_collection = NULL;
struct argo_log_collection *zpn_ast_ptag_collection = NULL;
struct argo_log_collection *zpn_auth_state_collection = NULL;
struct argo_log_collection *admin_probe_collection = NULL;
struct argo_log_collection *zia_health_collection = NULL;
struct argo_log_collection *zpn_np_collection = NULL;

struct argo_log_collection *app_thread_stats_log = NULL;

char *geoip_db_file = NULL;
char *geoip_isp_db_file = NULL;

struct zpn_fohh_worker_stats    broker_worker_stats;
struct zpn_fohh_worker_client_stats broker_client_stats;

int g_soft_assert_argo_reg_post_listen = 0;

struct zpn_common_broker_cfg    *g_broker_common_cfg = NULL;

struct fohh_connection *pbroker_control_connection;

struct zradix_tree *dont_advertise_ipv4;
struct zradix_tree *dont_advertise_ipv6;

static int is_ip_filtered(struct argo_inet *inet);

static int zpn_broker_c2c_api(struct zpath_debug_state *request_state,
                              const char **query_values,
                              int query_value_count,
                              void *cookie);

static void setup_fohh_conn_setup_timeout();
static void setup_broker_client_auth_complete_timeout();
static void setup_broker_client_ip_anchor_rx_data_timeout();
static void setup_broker_disable_flow_control_mtr();
static void setup_malloc_fastbin();
static void zpn_broker_update_load_stats();
static void setup_dont_dump();

int64_t fohh_conn_timeout_override;
int64_t broker_client_auth_timeout_override;
int64_t broker_client_ip_anchor_rx_data_timeout_override;

int64_t broker_alt_cloud_support_enabled = CONFIG_FEATURE_ALT_CLOUD_DISABLED;
int64_t g_txt_support = 0;
int64_t g_pse_ipv6_alt_authsp_feature_status = CONFIG_FEATURE_PSE_IPV6_ALT_AUTHSP_DEFAULT;

extern int64_t g_policy_fqdn_to_srv_ip_hard_disabled_feature_status;
extern int64_t g_policy_workload_tag_grp_hard_disabled;
extern int64_t g_app_match_sipa_only_apps_for_sipa_client_hard_disabled_feature_status;
extern int64_t g_app_scaling_bypass_improvement_hard_disabled_feature_status;
extern int64_t g_extranet_hard_disabled;
extern int64_t g_aae_profile_hard_disabled;
extern int64_t g_policy_re_eval_on_scim_update_hard_disabled;
extern int64_t g_policy_re_eval_on_posture_change_hard_disabled;

/* App scaling hard disable flags */
extern struct app_scale_hard_disable_feature_flag_clients g_appscale_hard_disable_clients[zpn_client_type_total_count];
int64_t g_ptag_global_disable;
int64_t g_waf_global_disable;

extern int64_t g_pse_user_risk_feature_enabled;
extern int64_t g_app_download_keep_alive;
extern struct zpn_broker_client_latency_probe_stats zpn_lat_chk_stats;
struct zpn_svcp_stats zpn_svcp_chk_stats = {0};
struct zpn_stepup_auth_stats zpn_stepup_chk_stats = {0};
extern int64_t g_app_zia_inspection_disable;
extern int64_t g_lp_init_status;
extern int64_t g_current_profile_version;
extern int64_t g_active_profile_version;
extern int64_t g_auto_cert_gen_global_disable;

static struct zpath_config_override_desc zpn_broker_waf_config_override_desc[] = {
    {
        .key                = WAF_FEATURE,
        .desc               = "Enable or disable WAF feature.",
        .details            = "0: disable (WAF is disabled)\n"
                              "1: enable (WAF is enabled)\n"
                              "default: 0 (i.e. WAF is disabled)",
        .val_type           = config_type_int,
        .component_types    = config_component_broker | config_component_pbroker | config_component_appc,
        .target_gid_types   = config_target_gid_type_inst |  config_target_gid_type_global,
        .int_range_lo       = 0,
        .int_range_hi       = 1,
        .int_default        = WAF_ENABLED,
        .value_traits       = config_value_traits_feature_enablement,
        .feature_group      = FEATURE_GROUP_APP_PROTECTION
    },
    {
        .key                = WAF_FEATURE_GLOBAL_DISABLE,
        .desc               = "Enable or disable WAF feature globally.",
        .details            = "0: enable (WAF is enabled)\n"
                              "1: disable (WAF is disabled)\n"
                              "default: 0 (i.e. WAF is enabled globally)",
        .val_type           = config_type_int,
        .component_types    = config_component_broker | config_component_pbroker | config_component_appc,
        .target_gid_types   = config_target_gid_type_global,
        .int_range_lo       = 0,
        .int_range_hi       = 1,
        .int_default        = WAF_GLOBAL_DISABLE,
        .value_traits       = config_value_traits_systemwide,
        .feature_group      = FEATURE_GROUP_APP_PROTECTION,
    },
    {
        .key                = AD_PROTECTION_FEATURE,
        .desc               = "Enable or disable AD protection feature",
        .details            = "0: disable \n"
                              "1: enable \n"
                              "default: 0 ",
        .val_type           = config_type_int,
        .component_types    = config_component_broker | config_component_pbroker | config_component_appc,
        .target_gid_types   = config_target_gid_type_inst |  config_target_gid_type_global,
        .int_range_lo       = 0,
        .int_range_hi       = 1,
        .int_default        = AD_PROTECTION_ENABLED,
        .value_traits       = config_value_traits_feature_enablement,
        .feature_group      = FEATURE_GROUP_APP_PROTECTION
    },
    {
        .key                = AD_PROTECTION_FEATURE_GLOBAL_DISABLE,
        .desc               = "Enable or disable AD protection feature globally",
        .details            = "0: enable \n"
                              "1: disable \n"
                              "default: 0 ",
        .val_type           = config_type_int,
        .component_types    = config_component_broker | config_component_pbroker | config_component_appc,
        .target_gid_types   = config_target_gid_type_inst |  config_target_gid_type_global,
        .int_range_lo       = 0,
        .int_range_hi       = 1,
        .int_default        = AD_PROTECTION_GLOBAL_DISABLE,
        .value_traits       = config_value_traits_systemwide,
        .feature_group      = FEATURE_GROUP_APP_PROTECTION
    },
    {
        .key                = AUTO_APP_PROTECTION_FEATURE,
        .desc               = "Enable/Disable Auto APP Protection",
        .details            = "0: Disable Auto APP Protection \n"
                              "1: Enable Auto APP Protection \n"
                              "default: 0 ",
        .val_type           = config_type_int,
        .component_types    = config_component_broker | config_component_pbroker | config_component_appc,
        .target_gid_types   = config_target_gid_type_inst |  config_target_gid_type_global,
        .int_range_lo       = 0,
        .int_range_hi       = 1,
        .int_default        = AUTO_APP_PROTECTION_ENABLED,
        .value_traits       = config_value_traits_feature_enablement,
        .feature_group      = FEATURE_GROUP_APP_PROTECTION
    },
    {
        .key                = AUTO_APP_PROTECTION_FEATURE_GLOBAL_DISABLE,
        .desc               = "Global level disable Auto APP Protection",
        .details            = "if Zscaler global customer and tenant have Auto APPprotection enabled = Enable, else Disable \n"
                              "1: Global Disable Auto APP Protection \n"
                              "0: Global Enable Auto APP Protection \n"
                              "default: 0 ",
        .val_type           = config_type_int,
        .component_types    = config_component_broker | config_component_pbroker | config_component_appc,
        .target_gid_types   = config_target_gid_type_global,
        .int_range_lo       = 0,
        .int_range_hi       = 1,
        .int_default        = AUTO_APP_PROTECTION_GLOBAL_DISABLE,
        .value_traits       = config_value_traits_systemwide,
        .feature_group      = FEATURE_GROUP_APP_PROTECTION
    },
    {
        .key                = AUTO_APP_CERT_GEN_FEATURE_GLOBAL_DISABLE,
        .desc               = "Global level disable Auto Certificate Generation",
        .details            = "if Zscaler global customer have Auto Certificate Generation enabled = Enable, else Disable \n"
                              "1: Global Disable Auto Certificate Generation \n"
                              "0: Global Enable Auto Certificate Generation \n"
                              "default: 0 ",
        .val_type           = config_type_int,
        .component_types    = config_component_broker | config_component_pbroker | config_component_appc,
        .target_gid_types   = config_target_gid_type_global,
        .int_range_lo       = 0,
        .int_range_hi       = 1,
        .int_default        = AUTO_APP_CERT_GEN_GLOBAL_DISABLE,
        .value_traits       = config_value_traits_systemwide,
        .feature_group      = FEATURE_GROUP_APP_PROTECTION
    },
    {
        .key                = WAF_PROTOCOL_TAGGING_FEATURE,
        .desc               = "Enable/Disable WAF Protocol Tagging",
        .details            = "0: Disable WAF Protocol Tagging \n"
                              "1: Enable WAF Protocol Tagging \n"
                              "default: 0 ",
        .val_type           = config_type_int,
        .component_types    = config_component_broker | config_component_pbroker | config_component_appc,
        .target_gid_types   = config_target_gid_type_inst |  config_target_gid_type_global,
        .int_range_lo       = 0,
        .int_range_hi       = 1,
        .int_default        = WAF_PROTOCOL_TAGGING_ENABLED,
        .value_traits       = config_value_traits_feature_enablement,
        .feature_group      = FEATURE_GROUP_APP_PROTECTION
    },
    {
        .key                = WAF_PROTOCOL_TAGGING_FEATURE_GLOBAL_DISABLE,
        .desc               = "Global level disable WAF Protocol Tagging",
        .details            = "if Zscaler global customer and tenant have WAF Protocol Tagging enabled = Enable, else Disable \n"
                              "1: Global Disable WAF Protocol Tagging \n"
                              "0: Global Enable WAF Protocol Tagging \n"
                              "default: 0 ",
        .val_type           = config_type_int,
        .component_types    = config_component_broker | config_component_pbroker | config_component_appc,
        .target_gid_types   = config_target_gid_type_global,
        .int_range_lo       = 0,
        .int_range_hi       = 1,
        .int_default        = WAF_PROTOCOL_TAGGING_GLOBAL_DISABLE,
        .value_traits       = config_value_traits_systemwide,
        .feature_group      = FEATURE_GROUP_APP_PROTECTION
    },
};

static struct zpath_config_override_desc zpn_broker_pra_config_override_desc[] = {
    {
        .key = JIT_FEATURE_GLOBAL_DISABLE,
        .desc = "Global config override to disable PRA JIT Priv Approvals",
        .details =  "0: PRA JIT is enabled\n"
            "1: PRA JIT is disabled\n"
            "Order of check - Public: global\n"
            "default: 0 - PRA JIT is enabled",
        .val_type = config_type_int,
        .component_types = config_component_exporter | config_component_ot_exporter | config_component_ot_broker | config_component_broker |config_component_pbroker,
        .target_gid_types = config_target_gid_type_global,
        .int_range_lo = 0,
        .int_range_hi = 1,
        .int_default = DEFAULT_JIT_FEATURE_GLOBAL_DISABLE,
        .feature_group      = FEATURE_GROUP_PRIVILEGED_REMOTE_ACCESS_ESSENTIALS,
        .value_traits       = config_value_traits_systemwide,
    },
    {
        .key                = PRA_FEATURE_GLOBAL_DISABLE,
        .desc               = "Kill switch for PRA suite of features",
        .details            = "0: PRA is enabled\n"
            "1: PRA is disabled\n"
            "Order of check - Public: global\n"
            "default: 0 - PRA is enabled",
        .val_type           = config_type_int,
        .component_types    = config_component_exporter | config_component_ot_exporter | config_component_ot_broker | config_component_broker |config_component_pbroker,
        .target_gid_types   = config_target_gid_type_global,
        .int_range_lo       = 0,
        .int_range_hi       = 1,
        .int_default        = DEFAULT_PRA_FEATURE_GLOBAL_DISABLE,
        .feature_group      = FEATURE_GROUP_PRIVILEGED_REMOTE_ACCESS_ESSENTIALS,
        .value_traits       = config_value_traits_systemwide,
    },
    {
        .key                = PRA_FEATURE,
        .desc               = "PRA feature config disablement per customer",
        .details            = "turn off feature: disabled\n"
                              "turn on feature: enabled\n"
                              "default: disabled",
        .val_type           = config_type_str,
        .component_types    = config_component_exporter | config_component_ot_exporter | config_component_ot_broker | config_component_broker |config_component_pbroker,
        .target_gid_types   = config_target_gid_type_cust,
        .str_default        = DEFAULT_PRA_FEATURE,
        .feature_group      = FEATURE_GROUP_PRIVILEGED_REMOTE_ACCESS_ESSENTIALS,
        .value_traits       = config_value_traits_feature_enablement,
    },
    {
        .key                = JIT_APPROVAL_BASED_ACCESS,
        .desc               = "Enable/disable Privileged Approvals JIT",
        .details            = "turn off feature: disabled\n"
                              "turn on feature: enabled\n"
                              "default: disabled",
        .val_type           = config_type_str,
        .component_types    = config_component_exporter | config_component_ot_exporter  | config_component_ot_broker | config_component_broker |config_component_pbroker,
        .target_gid_types   = config_target_gid_type_cust | config_target_gid_type_global,
        .str_default        = DEFAULT_JIT_APPROVAL_BASED_ACCESS,
        .feature_group      = FEATURE_GROUP_PRIVILEGED_REMOTE_ACCESS_ESSENTIALS,
        .value_traits       = config_value_traits_feature_enablement,
    },
};

static struct zpath_config_override_desc zpn_broker_app_buffer_tune_config_override_desc[] = {
    {
        .key                = ZPN_BROKER_APP_BUFFER_TUNE_FEATURE,
        .desc               = "enable or disable app buffer tune feature such that we can tune up our FOHH window update window size",
        .details            = "0: App buffer tune feature is disabled\n"
                              "1: App buffer tune feature is enabled for customer (if set by customer gid) or global (if set for All)\n"
                              "Order of check: inst id, zsroot, global\n"
                              "default: 0 (i.e. app buffer tune feature is disabled by default on public broker only)",
        .val_type           = config_type_int,
        .component_types    = config_component_broker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_zsroot | config_target_gid_type_global,
        .int_range_lo       = LOW_ZPN_BROKER_APP_BUFFER_TUNE_FEATURE_VALUE,
        .int_range_hi       = HIGH_ZPN_BROKER_APP_BUFFER_TUNE_FEATURE_VALUE,
        .int_default        = ZPN_BROKER_APP_BUFFER_TUNE_FEATURE_DEFAULT_VALUE,
        .feature_group      = FEATURE_GROUP_BROKER_APP_BUFFER,
        .value_traits       = config_value_traits_feature_enablement,
    },
    {
        .key                = ZPN_BROKER_APP_BUFFER_CONFIG,
        .desc               = "tune FOHH window size to the desired value",
        .details            = "Order of check: inst id, zsroot, global\n"
                              "default: 0 (i.e. 0 is the default value for the app buffer size)",
        .val_type           = config_type_int,
        .component_types    = config_component_broker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_zsroot | config_target_gid_type_global,
        .int_range_lo       = LOW_ZPN_BROKER_APP_BUFFER_CONFIG_VALUE,
        .int_range_hi       = HIGH_ZPN_BROKER_APP_BUFFER_CONFIG_VALUE,
        .int_default        = ZPN_BROKER_APP_BUFFER_CONFIG_DEFAULT_VALUE,
        .feature_group      = FEATURE_GROUP_BROKER_APP_BUFFER,
        .value_traits       = config_value_traits_normal,
    },
    {
        .key                = ZPN_BROKER_APP_BUFFER_MCONN_CONFIG,
        .desc               = "tune FOHH mconn window size to the desired value",
        .details            = "Order of check: inst id, zsroot, global\n"
                              "default: 0 (i.e. 0 is the default value for the mconn app buffer size)",
        .val_type           = config_type_int,
        .component_types    = config_component_broker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_zsroot | config_target_gid_type_global,
        .int_range_lo       = LOW_ZPN_BROKER_APP_BUFFER_MCONN_CONFIG_VALUE,
        .int_range_hi       = HIGH_ZPN_BROKER_APP_BUFFER_MCONN_CONFIG_VALUE,
        .int_default        = ZPN_BROKER_APP_BUFFER_MCONN_CONFIG_DEFAULT_VALUE,
        .feature_group      = FEATURE_GROUP_BROKER_APP_BUFFER,
        .value_traits       = config_value_traits_normal,

    },
    {
        .key                = ZPN_BROKER_APP_BUFFER_WATERMARK,
        .desc               = "tune FOHH window update low-watermark to the desired value, once the low-watermark is reached, a window update will trigger",
        .details            = "Order of check: inst id, zsroot, global\n"
                              "default: 0 (i.e. 0 is the default watermark value for the app buffer)",
        .val_type           = config_type_int,
        .component_types    = config_component_broker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_zsroot | config_target_gid_type_global,
        .int_range_lo       = LOW_ZPN_BROKER_APP_BUFFER_WATERMARK_VALUE,
        .int_range_hi       = HIGH_ZPN_BROKER_APP_BUFFER_WATERMARK_VALUE,
        .int_default        = ZPN_BROKER_APP_BUFFER_WATERMARK_DEFAULT_VALUE,
        .feature_group      = FEATURE_GROUP_BROKER_APP_BUFFER,
        .value_traits       = config_value_traits_normal,
    },
    {
        .key                = ZPN_BROKER_APP_BUFFER_MCONN_WATERMARK,
        .desc               = "tune FOHH mconn window update low-watermark to the desired value, once the low-watermark is reached, a window update will trigger",
        .details            = "Order of check: inst id, zsroot, global\n"
                              "default: 0 (i.e. 0 is the default watermark value for the mconn app buffer)",
        .val_type           = config_type_int,
        .component_types    = config_component_broker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_zsroot | config_target_gid_type_global,
        .int_range_lo       = LOW_ZPN_BROKER_APP_BUFFER_MCONN_WATERMARK_VALUE,
        .int_range_hi       = HIGH_ZPN_BROKER_APP_BUFFER_MCONN_WATERMARK_VALUE,
        .int_default        = ZPN_BROKER_APP_BUFFER_MCONN_WATERMARK_DEFAULT_VALUE,
        .feature_group      = FEATURE_GROUP_BROKER_APP_BUFFER,
        .value_traits       = config_value_traits_normal,
    }

};

static struct zpath_config_override_desc zpn_pse_app_buffer_tune_config_override_desc[] = {
      {
        .key                = ZPN_PSE_APP_BUFFER_TUNE_FEATURE,
        .desc               = "enable or disable app buffer tune feature such that we can tune up our FOHH window update window size",
        .details            = "0: App buffer tune feature is disabled\n"
                              "1: App buffer tune feature is enabled for customer (if set by customer gid) or global (if set for All)\n"
                              "Order of check: component id, component group id, customer gid, global\n"
                              "default: 0 (i.e. app buffer tune feature is disabled by default on PSE)",
        .val_type           = config_type_int,
        .component_types    = config_component_pbroker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_inst_grp | config_target_gid_type_cust | config_target_gid_type_global,
        .int_range_lo       = LOW_ZPN_PSE_APP_BUFFER_TUNE_FEATURE_VALUE,
        .int_range_hi       = HIGH_ZPN_PSE_APP_BUFFER_TUNE_FEATURE_VALUE,
        .int_default        = ZPN_PSE_APP_BUFFER_TUNE_FEATURE_DEFAULT_VALUE,
        .feature_group      = FEATURE_GROUP_PRIVATE_SERVICE_EDGE_APP_BUFFER_TUNE,
        .value_traits       = config_value_traits_feature_enablement,
    },
    {
        .key                = ZPN_PSE_APP_BUFFER_CONFIG,
        .desc               = "tune FOHH window size to the desired value",
        .details            = "Order of check: component id, component group id, customer gid, global\n"
                              "default: 0 (i.e. 0 is the default value for the app buffer size)",
        .val_type           = config_type_int,
        .component_types    = config_component_pbroker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_inst_grp | config_target_gid_type_cust | config_target_gid_type_global,
        .int_range_lo       = LOW_ZPN_PSE_APP_BUFFER_CONFIG_VALUE,
        .int_range_hi       = HIGH_ZPN_PSE_APP_BUFFER_CONFIG_VALUE,
        .int_default        = ZPN_PSE_APP_BUFFER_CONFIG_DEFAULT_VALUE,
        .feature_group      = FEATURE_GROUP_PRIVATE_SERVICE_EDGE_APP_BUFFER_TUNE,
        .value_traits       = config_value_traits_normal,
    },
    {
        .key                = ZPN_PSE_APP_BUFFER_MCONN_CONFIG,
        .desc               = "tune FOHH mconn window size to the desired value",
        .details            = "Order of check: component id, component group id, customer gid, global\n"
                              "default: 0 (i.e. 0 is the default value for the mconn app buffer size)",
        .val_type           = config_type_int,
        .component_types    = config_component_pbroker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_inst_grp | config_target_gid_type_cust | config_target_gid_type_global,
        .int_range_lo       = LOW_ZPN_PSE_APP_BUFFER_MCONN_CONFIG_VALUE,
        .int_range_hi       = HIGH_ZPN_PSE_APP_BUFFER_MCONN_CONFIG_VALUE,
        .int_default        = ZPN_PSE_APP_BUFFER_MCONN_CONFIG_DEFAULT_VALUE,
        .feature_group      = FEATURE_GROUP_PRIVATE_SERVICE_EDGE_APP_BUFFER_TUNE,
        .value_traits       = config_value_traits_normal,
    },
    {
        .key                = ZPN_PSE_APP_BUFFER_WATERMARK,
        .desc               = "tune FOHH window update low-watermark to the desired value, once the low-watermark is reached, a window update will trigger",
        .details            = "Order of check: component id, component group id, customer gid, global\n"
                              "default: 0 (i.e. 0 is the default watermark value for the app buffer watermark)",
        .val_type           = config_type_int,
        .component_types    = config_component_pbroker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_inst_grp | config_target_gid_type_cust | config_target_gid_type_global,
        .int_range_lo       = LOW_ZPN_PSE_APP_BUFFER_WATERMARK_VALUE,
        .int_range_hi       = HIGH_ZPN_PSE_APP_BUFFER_WATERMARK_VALUE,
        .int_default        = ZPN_PSE_APP_BUFFER_WATERMARK_DEFAULT_VALUE,
        .feature_group      = FEATURE_GROUP_PRIVATE_SERVICE_EDGE_APP_BUFFER_TUNE,
        .value_traits       = config_value_traits_normal,
    },
    {
        .key                = ZPN_PSE_APP_BUFFER_MCONN_WATERMARK,
        .desc               = "tune FOHH mconn window update low-watermark to the desired value, once the low-watermark is reached, a window update will trigger",
        .details            = "Order of check: component id, component group id, customer gid, global\n"
                              "default: 0 (i.e. 0 is the default watermark value for the mconn app buffer watermark)",
        .val_type           = config_type_int,
        .component_types    = config_component_pbroker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_inst_grp | config_target_gid_type_cust | config_target_gid_type_global,
        .int_range_lo       = LOW_ZPN_PSE_APP_BUFFER_MCONN_WATERMARK_VALUE,
        .int_range_hi       = HIGH_ZPN_PSE_APP_BUFFER_MCONN_WATERMARK_VALUE,
        .int_default        = ZPN_PSE_APP_BUFFER_MCONN_WATERMARK_DEFAULT_VALUE,
        .feature_group      = FEATURE_GROUP_PRIVATE_SERVICE_EDGE_APP_BUFFER_TUNE,
        .value_traits       = config_value_traits_normal,
    }
};

static struct zpath_config_override_desc zia_override_desc = {
        .key               = ZPN_BROKER_ZIA_CLOUD_PREFIX,
        .desc              = "provides prefix for ZIA cloud domain, used by broker to verify on auth from CC/BC/SIPA/etc. "
                             "Prefix must not have trailing '.', broker will add it. "
                             "The prefix is used if the original zia_cloud is not found. See ET-81763.",
        .details           = "default: NULL (i.e. there is no prefix)",
        .val_type          = config_type_str,
        .component_types   = config_component_broker | config_component_pbroker,
        .target_gid_types  = config_target_gid_type_cust | config_target_gid_type_global,
        .int_range_lo      = 0,
        .int_range_hi      = 0,
        .int_default       = 0,
        .str_default       = ZPN_BROKER_ZIA_CLOUD_PREFIX_DEFAULT,
        .feature_group     = FEATURE_GROUP_BROKER,
        .value_traits      = config_value_traits_normal,
};


extern int64_t g_app_zia_inspection_idle_timeout_s;

/*
 * config override global variable to disable flow control for mtr probes
 */
int64_t broker_mtr_flow_control_disabled;

static struct zpath_config_override_desc partner_login_override_desc = {
        .key                = PARTNER_LOGIN_FEATURE,
        .desc               = "Config override flag to enable/disable partner login feature",
        .details            = "1: Enable partner login\n"
                              "0: Disable partner login\n"
                              "Order of check: Customer gid, global\n"
                              "default: 1",
        .val_type           = config_type_int,
        .component_types    = config_component_broker | config_component_pbroker,
        .target_gid_types   = config_target_gid_type_cust | config_target_gid_type_global,
        .int_range_lo       = MIN_PARTNER_LOGIN_FEATURE,
        .int_range_hi       = MAX_PARTNER_LOGIN_FEATURE,
        .int_default        = DEFAULT_PARTNER_LOGIN_FEATURE,
        .feature_group      = FEATURE_GROUP_PARTNER_LOGIN,
        .value_traits       = config_value_traits_feature_enablement,
};

static struct zpath_config_override_desc zpn_zdx_https_webprobe_config_override_desc_broker_pbroker_common[] = {
    {
        .key                = ZPN_ZDX_CONFIG_HTTPS_WEBPROBE_CACHE_INTERCEPT_HARD_DISABLED,
        .desc               = "when enabled stops ALL ZDX https webprobe requests from being fulfilled across the cloud",
        .details            = "Not Set: ZDX Webprobe is not influenced by this flag\n"
                              "0: ZDX Https Webprobe is not influenced by this flag\n"
                              "1: ZDX Https Webprobe is disabled across the cloud\n"
                              "Order of check: global\n"
                              "default: 0 (i.e. ZDX Webprobe is not hard disabled by default)",
        .val_type           = config_type_int,
        .component_types    = config_component_broker | config_component_pbroker,
        .target_gid_types   = config_target_gid_type_global,
        .int_range_lo       = 0,
        .int_range_hi       = 1,
        .int_default        = ZPN_ZDX_CONFIG_HTTPS_WEBPROBE_CACHE_INTERCEPT_HARD_DISABLED_DEFAULT,
        .value_traits       = config_value_traits_systemwide,
        .feature_group      = FEATURE_GROUP_ZDX_HTTPS_WEBPROBE,
    }
};

static struct zpath_config_override_desc zpn_zdx_https_webprobe_config_override_desc_broker[] = {
    {
        .key                = ZPN_ZDX_CONFIG_BROKER_HTTPS_WEBPROBE_CACHE_INTERCEPT_ENABLED,
        .desc               = "when enabled allows ZDX Https Webprobe to pass through public brokers, overridden by hard disable flag",
        .details            = "0: ZDX ZDX Https Webprobe is not enabled on public brokers\n"
                              "1: ZDX Https webprobe is enabled\n"
                              "Order of check: customer gid, global\n"
                              "default: 0 (i.e. ZDX Https Webprobe is not enabled by default)",
        .val_type           = config_type_int,
        .component_types    = config_component_broker,
        .target_gid_types   = config_target_gid_type_cust | config_target_gid_type_global,
        .int_range_lo       = 0,
        .int_range_hi       = 1,
        .int_default        = ZPN_ZDX_CONFIG_BROKER_HTTPS_WEBPROBE_CACHE_INTERCEPT_ENABLED_DEFAULT,
        .feature_group      = FEATURE_GROUP_ZDX_HTTPS_WEBPROBE,
        .value_traits       = config_value_traits_feature_enablement,
    }
};

static struct zpath_config_override_desc zpn_zdx_https_webprobe_config_override_desc_pbroker[] = {
    {
        .key                = ZPN_ZDX_CONFIG_PBROKER_HTTPS_WEBPROBE_CACHE_INTERCEPT_ENABLED,
        .desc               = "when enabled allows ZDX Https Webprobe to pass through private brokers, overridden by hard disable flag",
        .details            = "0: ZDX Https Webprobe is not enabled on private brokers\n"
                              "1: ZDX Https webprobe is enabled\n"
                              "Order of check: customer gid, global\n"
                              "default: 0 (i.e. ZDX Https Webprobe is not enabled by default)",
        .val_type           = config_type_int,
        .component_types    = config_component_pbroker,
        .target_gid_types   = config_target_gid_type_cust | config_target_gid_type_global,
        .int_range_lo       = 0,
        .int_range_hi       = 1,
        .int_default        = ZPN_ZDX_CONFIG_PBROKER_HTTPS_WEBPROBE_CACHE_INTERCEPT_ENABLED_DEFAULT,
        .feature_group      = FEATURE_GROUP_ZDX_HTTPS_WEBPROBE,
        .value_traits       = config_value_traits_feature_enablement,
    }
};

static struct zpath_config_override_desc trans_log_natural_log_throttling[] = {
    {
        .key                = NATURAL_TRANS_LOG_RATE_LIMIT_ERRORS,
        .desc               = "Config override flag to add comma separated list of Error codes for a specified customer",
        .details            = "default: NULL",
        .val_type           = config_type_str,
        .component_types    = config_component_broker,
        .target_gid_types   = config_target_gid_type_global | config_target_gid_type_cust,
        .int_range_lo       = 0,
        .int_range_hi       = 0,
        .int_default        = 0,
        .str_default        = NATURAL_TRANS_LOG_RATE_LIMIT_DEFAULT_ERRORS, NATURAL_RATE_LIMIT_STR_VALID_CB(natural_log_throttle_err_str_validation),
        .feature_group      = FEATURE_GROUP_NATURAL_RATE_LIMITING,
        .value_traits       = config_value_traits_normal,
    },
    {
        .key                = NATURAL_TRANS_LOG_RATE_LIMIT_TIME,
        .desc               = "Config override flag to add threshold throttling global time for all customers and errors",
        .details            = "default: 300",
        .val_type           = config_type_int,
        .component_types    = config_component_broker,
        .target_gid_types   = config_target_gid_type_global,
        .int_range_lo       = MIN_NATURAL_TRANS_LOG_RATE_LIMIT_TIME,
        .int_range_hi       = MAX_NATURAL_TRANS_LOG_RATE_LIMIT_TIME,
        .int_default        = NATURAL_TRANS_LOG_RATE_LIMIT_DEFAULT_TIME,
        .feature_group      = FEATURE_GROUP_NATURAL_RATE_LIMITING,
        .value_traits       = config_value_traits_normal,
    },
    {
        .key                = NATURAL_TRANS_LOG_RATE_LIMITTING,
        .desc               = "Config override flag to enable/disable throttling for all customers",
        .details            = "default: disabled",
        .val_type           = config_type_int,
        .component_types    = config_component_broker,
        .target_gid_types   = config_target_gid_type_global,
        .int_range_lo       = NATURAL_TRANS_LOG_RATE_LIMITTING_DISABLED,
        .int_range_hi       = NATURAL_TRANS_LOG_RATE_LIMITTING_ENABLED,
        .int_default        = NATURAL_TRANS_LOG_RATE_LIMITTING_DEFAULT,
        .feature_group      = FEATURE_GROUP_NATURAL_RATE_LIMITING,
        .value_traits       = config_value_traits_feature_enablement,
    }
};

static struct zpath_config_override_desc zpn_broker_fohh_mconn_track_perf_stats_level_config_override_desc[] = {
        {
                .key                = ZPN_BROKER_FOHH_MCONN_TRACK_PERF_STATS_LEVEL,
                .desc               = "when enabled allows adding into transaction logs fohh mconn track performance statistics",
                .details            = "0: no extra statistics is added into transaction logs on public brokers\n"
                           "1: adds level 1 fohh mconn tx_peer_rx_data_hist track performance statistics into transaction\n"
                           "2: adds level 2 fohh mconn rx_diff_rx_data_hist track performance statistics into transaction\n"
                           "3: adds level 3 fohh mconn tx_peer_rx_data_max  track performance statistics into transaction\n"
                           "default: 0 - no extra statistics is added into transaction logs",
                .val_type           = config_type_int,
                .component_types    = config_component_broker,
                .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_cust | config_target_gid_type_zsroot | config_target_gid_type_global ,
                .int_range_lo       = LOW_ZPN_BROKER_FOHH_MCONN_TRACK_PERF_STATS_LEVEL,
                .int_range_hi       = HIGH_ZPN_BROKER_FOHH_MCONN_TRACK_PERF_STATS_LEVEL,
                .int_default        = ZPN_BROKER_FOHH_MCONN_TRACK_PERF_STATS_LEVEL_DEFAULT_VALUE,
                .value_traits       = config_value_traits_normal,
                .feature_group      = FEATURE_GROUP_MCONN_TRACK_PERF_STATS
        }
};

static struct zpath_config_override_desc zpn_pse_fohh_mconn_track_perf_stats_level_config_override_desc[] = {
        {
                .key                = ZPN_PSE_FOHH_MCONN_TRACK_PERF_STATS_LEVEL,
                .desc               = "when enabled allows adding into transaction logs fohh mconn track performance statistics",
                .details            = "0: no extra statistics is added into transaction logs on private brokers\n"
                           "1: adds level 1 fohh mconn tx_peer_rx_data_hist track performance statistics into transaction\n"
                           "2: adds level 2 fohh mconn rx_diff_rx_data_hist track performance statistics into transaction\n"
                           "3: adds level 3 fohh mconn tx_peer_rx_data_max  track performance statistics into transaction\n"
                           "default: 0 - no extra statistics is added into transaction logs",
                .val_type           = config_type_int,
                .component_types    = config_component_pbroker,
                .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_cust | config_target_gid_type_global,
                .int_range_lo       = LOW_ZPN_PSE_FOHH_MCONN_TRACK_PERF_STATS_LEVEL,
                .int_range_hi       = HIGH_ZPN_PSE_FOHH_MCONN_TRACK_PERF_STATS_LEVEL,
                .int_default        = ZPN_PSE_FOHH_MCONN_TRACK_PERF_STATS_LEVEL_DEFAULT_VALUE,
                .value_traits       = config_value_traits_normal,
                .feature_group      = FEATURE_GROUP_MCONN_TRACK_PERF_STATS
        }
};

void broker_set_pbroker_control(struct fohh_connection *f_conn)
{
    pbroker_control_connection = f_conn;
}

static int zpn_private_broker_set_group_cfg(int64_t private_broker_gid);

void zpn_broker_set_autotune(int bool_value)
{
    if (bool_value) {
        ZPN_LOG(AL_NOTICE, "Broker running with autotune for clients");
    } else {
        ZPN_LOG(AL_NOTICE, "Broker running without autotune for clients");
    }
    broker_autotune = bool_value;
}

static void broker_structure_registration_after_barrier_set_cb(const char *structure_type,
                                                                int is_dictionary,
                                                                size_t structure_size,
                                                                const struct argo_field_description *description,
                                                                size_t description_count) {
    /* ET-31797 argo can encounter unknown types and registers a dummy without description.
     * Service endpoints also register global structures on-demand.
     * So use flag g_soft_assert_argo_reg_post_listen to control when we want to soft-assert.
     */
    ZPN_BROKER_ASSERT_SOFT(0, "argo structure registration [%s] should not happen after listening for clients!", structure_type);
}

int zpn_broker_cert_lifetime(struct zpath_debug_state*  request_state,
                          const char** query_values,
                          int query_value_count,
                          void* cookie)
{
    int json_format = query_value_count > 0 && query_values && query_values[0] && strcmp(query_values[0], "0");

    const char *cert_file_name = ZPATH_LOCAL_PUBLIC_CERTIFICATE_FILE;
    int64_t elapsed_lifetime, remaining_lifetime = 0;

    char output[500] = {};
    int error = 0;

    if (!cert_file_name) {
        strncpy(output, "The file name of certificate is NULL", sizeof(output) - 1);
        error = 1;
        goto output;
    }

    if (zcrypto_lib_get_cert_lifetime(cert_file_name, &elapsed_lifetime, &remaining_lifetime) != ZPATH_RESULT_NO_ERROR) {
        snprintf(output, sizeof(output), "Failed to get lifetime from the certificate %s", cert_file_name);
        error = 1;
        goto output;
    }

    if (remaining_lifetime >= 0) {
        snprintf(output, sizeof(output), "The remaining lifetime is: %"PRId64" seconds", remaining_lifetime);
    } else {
        snprintf(output, sizeof(output), "The certificate is expired at: %"PRId64" seconds ago", -remaining_lifetime);
    }

output:
    if (json_format) {
        ZDP("{"
                "\"expired\": %d, "
                "\"lifetime\": %"PRId64", "
                "\"description\": \"%s\""
            "}\n",
            remaining_lifetime >= 0 ? 0 : 1,
            remaining_lifetime >= 0 ? remaining_lifetime : 0,
            output
           );
    } else {
        ZDP("%s\n", output);
    }

    return error ? ZPN_RESULT_ERR : ZPN_RESULT_NO_ERROR;
}

static void* zpn_broker_on_invalid_self_cert_thread(struct zthread_info *zthread_arg, void *cookie)
{
    const char* cert_file_name = (char*) cookie;

    ZPN_LOG(AL_DEBUG, "Start to handle self cert expired event, cert name %s, broker status %s(%d)",
            cert_file_name, zpn_broker_get_status_string(broker_status), broker_status);

    while (RUNNING != broker_status) {
        ZPN_LOG(AL_DEBUG, "Waiting for broker to be in RUNNING status");

        zthread_heartbeat(NULL);
        sleep(5);

        if (MAINTENANCE == broker_status || TERMINATING == broker_status) {
            return NULL;
        }
    }

    ZPN_LOG(AL_DEBUG, "Enter maintenance mode because self cert is invalid, cert name %s", cert_file_name);

    zpn_broker_maintenance_config config;

    zpn_broker_get_maintenance_default_config(&config);
    config.initial_delay_s = 5;
    config.exit = 0;
    config.close_sockets = 1; //This ensures that listening sockets are closed so that NLB could detect us as unhealthy

    while (zpn_broker_maintenance_force_start(&config) != ZPN_RESULT_NO_ERROR) {
        ZPN_LOG(AL_ERROR, "Failed to start maintenance functions, will retry after 45 seconds ..");
        zthread_heartbeat(NULL);
        sleep(45);
    }

    while (!zpn_broker_maintenance_done()) {
        ZPN_LOG(AL_DEBUG, "Running maintenance functions, waiting for them to be finished ..");
        zthread_heartbeat(NULL);
        sleep(10);
    }

    ZPN_LOG(AL_DEBUG, "Maintenance functions finished, broker in MAINTENANCE mode; start to monitor certificate");

    int64_t elapsed_lifetime, remaining_lifetime;

    while (1) {
        // check the cert until it is valid
        if (zcrypto_lib_get_cert_lifetime(cert_file_name, &elapsed_lifetime, &remaining_lifetime) != ZPATH_RESULT_NO_ERROR) {
            ZPN_LOG(AL_ERROR, "Failed to get lifetime from the certificate %s", cert_file_name);
        } else if (elapsed_lifetime >= 0 && remaining_lifetime > 0) {
            break;
        } else {
            ZPN_LOG(AL_ERROR, "Waiting for a valid certificate, current name=%s, elapsed=%"PRId64", remaining=%"PRId64,
                    cert_file_name, elapsed_lifetime, remaining_lifetime);
        }

        zthread_heartbeat(NULL);
        sleep(45);
    }

    ZPN_LOG(AL_INFO, "The certificate become valid, will reboot");

    sleep(3);   // give the log a chance to flush

    /* This will update load table and does the exit as well. */
    zpn_broker_exit_handler(zpath_tc_exit, 0, zthread_get_self_thread_id());

    exit(0);
}

static void zpn_broker_on_invalid_self_cert(const char* cert_file_name)
{
    pthread_t thread;
    int res = zthread_create(&thread,
            zpn_broker_on_invalid_self_cert_thread,
            (char*)cert_file_name,
            "self_cert_monitor",
            60,
            16*1024*1024,
            0,
            NULL);

    if (res) {
        ZPN_LOG(AL_ERROR, "Could not initialize self thread");
    }
}

static int64_t zpn_broker_check_self_cert(const char* cert_file_name)
{
    static const uint32_t SECONDS_PER_DAY = 60 * 60 * 24;

    static const int64_t CHECK_AFTER_ONE_MINUTE = 60;
    static const int64_t CHECK_AFTER_ONE_HOUR = 60 * 60;
    static const int64_t CHECK_AFTER_ONE_DAY = SECONDS_PER_DAY;

    int64_t elapsed_lifetime, remaining_lifetime;

    if (zcrypto_lib_get_cert_lifetime(cert_file_name, &elapsed_lifetime, &remaining_lifetime) != ZPATH_RESULT_NO_ERROR) {
        ZPN_LOG(AL_ERROR, "Failed to get lifetime from the certificate %s", cert_file_name);
        return CHECK_AFTER_ONE_MINUTE;
    }

    if (elapsed_lifetime < 0) {
        ZPN_LOG(AL_ERROR, "The certificate %s is too new to use, lifetime %"PRId64, cert_file_name, elapsed_lifetime);
        zpn_broker_on_invalid_self_cert(cert_file_name);
        return 0;
    }

    if (remaining_lifetime <= 0) {
        ZPN_LOG(AL_CRITICAL, "The certificate %s is expired before %"PRId64" seconds", cert_file_name, -remaining_lifetime);
        zpn_broker_on_invalid_self_cert(cert_file_name);
        return 0;
    }

    int prio = AL_INFO;
    if (remaining_lifetime < SECONDS_PER_DAY * 1) {
        prio = AL_CRITICAL;
    } else if (remaining_lifetime < SECONDS_PER_DAY * 3) {
        prio = AL_ERROR;
    } else if (remaining_lifetime < SECONDS_PER_DAY * 10) {
        prio = AL_WARNING;
    }

    ZPN_LOG(prio, "The certificate %s is to be expired in %"PRId64" days and %"PRId64" seconds",
            cert_file_name, remaining_lifetime / SECONDS_PER_DAY, remaining_lifetime % SECONDS_PER_DAY);

    if (remaining_lifetime < CHECK_AFTER_ONE_MINUTE) {
        return remaining_lifetime;
    } else if (remaining_lifetime < CHECK_AFTER_ONE_HOUR) {
        return CHECK_AFTER_ONE_MINUTE;
    } else if (remaining_lifetime < CHECK_AFTER_ONE_DAY) {
        return CHECK_AFTER_ONE_HOUR;
    } else {
        return CHECK_AFTER_ONE_DAY;
    }
}

static void zpn_broker_monitor_self_cert_periodically(evutil_socket_t sock, short flags, void *cookie)
{
    struct event *ev = cookie;

    int64_t checkpoint = zpn_broker_check_self_cert(ZPATH_LOCAL_PUBLIC_CERTIFICATE_FILE);

    if (checkpoint > 0) {
        struct timeval tv;

        tv.tv_sec = checkpoint;
        tv.tv_usec = 0;

        if (evtimer_add(ev, &tv) < 0) {
            ZPN_LOG(AL_ERROR, "Can't set timer value to monitor certificate's expiration date");
            evtimer_del(ev);
        }
    }
}

int zpn_broker_self_cert_monitor_init(struct event_base *base, int runtime_monitor)
{
    const char *cert_file_name = ZPATH_LOCAL_PUBLIC_CERTIFICATE_FILE;

    if (!cert_file_name) {
        ZPN_LOG(AL_ERROR, "The file name of certificate is NULL");
        return ZPN_RESULT_ERR;
    }

    int64_t checkpoint = zpn_broker_check_self_cert(cert_file_name);
    if (checkpoint == 0) {
        return ZPN_RESULT_NO_ERROR;
    }

    struct event *ev = evtimer_new(base, zpn_broker_monitor_self_cert_periodically, event_self_cbarg());

    if (!ev) {
        ZPN_LOG(AL_ERROR, "Can't create timer to monitor certificate's expiration date");
        return ZPN_RESULT_ERR;
    }

    struct timeval tv;
    tv.tv_sec = checkpoint;
    tv.tv_usec = 0;

    if (evtimer_add(ev, &tv) < 0) {
        ZPN_LOG(AL_ERROR, "Can't set timer value to monitor certificate's expiration date");
        evtimer_del(ev);
        return ZPN_RESULT_ERR;
    }

    return ZPN_RESULT_NO_ERROR;
}

static void zbh_timer_callback(evutil_socket_t sock, short flags, void *cookie) {
    if (tickle_me) zthread_heartbeat(tickle_me);

    zpn_broker_check_mtunnel();
}

// non-static for unit tests
int zbh_base_init(struct event_base *base) {
    struct timeval tv;
    struct event *ev_timer;

    zbh_base = base;

    ev_timer = event_new(base,
                         -1,
                         EV_PERSIST,
                         zbh_timer_callback,
                         NULL);
    if (!ev_timer) {
        ZPN_LOG(AL_ERROR, "Could not create timer");
        return ZPN_RESULT_ERR;
    }

    tv.tv_sec = MTUNNEL_HOUSEKEEPING_INTERVAL_US / 1000000l;
    tv.tv_usec = MTUNNEL_HOUSEKEEPING_INTERVAL_US % 1000000l;
    if (event_add(ev_timer, &tv)) {
        ZPN_LOG(AL_ERROR, "Could not activate timer");
        return ZPN_RESULT_ERR;
    }

    return ZPN_RESULT_NO_ERROR;
}

struct prev_pbroker_intf_stats {
    int64_t intf_rb;
    int64_t intf_rp;
    int64_t intf_re;
    int64_t intf_rd;
    int64_t intf_tb;
    int64_t intf_tp;
    int64_t intf_te;
    int64_t intf_td;
} prev_pbroker_intf_stats;

struct prev_pbroker_intf_stats prev_pbroker_intf_stats = { 0 };



static void zpn_pbroker_status_report_get_if_stats(struct zinterfaces interfaces[MAX_INTERFACES],
                                            int interface_count,
                                            struct zpn_pbroker_status_report *data)
{

    int i;
    int64_t rb = 0, rp = 0, re = 0, rd = 0, tb = 0, tp = 0, te = 0, td = 0;

    for (i = 0; i < interface_count; i++) {
        struct sockaddr_in *addr_in = (struct sockaddr_in *)&(interfaces[i].addr);

        rb += interfaces[i].rb;
        rp += interfaces[i].rp;
        re += interfaces[i].re;
        rd += interfaces[i].rd;
        tb += interfaces[i].tb;
        tp += interfaces[i].tp;
        te += interfaces[i].te;
        td += interfaces[i].td;

        ZPN_DEBUG_PBROKER("  - %s: %s, rb=%ld, rp=%ld, re=%ld, rd=%ld, tb=%ld, tp=%ld, te=%ld, td=%ld",
                            interfaces[i].name, inet_ntoa(addr_in->sin_addr),
                            (long)interfaces[i].rb, (long)interfaces[i].rp, (long)interfaces[i].re, (long)interfaces[i].rd,
                            (long)interfaces[i].tb, (long)interfaces[i].tp, (long)interfaces[i].te, (long)interfaces[i].td);
    }

    /* Interafce level counts */
    data->intf_count = interface_count;
    data->intf_rb = rb;
    data->intf_rp = rp;
    data->intf_re = re;
    data->intf_rd = rd;
    data->intf_tb = tb;
    data->intf_tp = tp;
    data->intf_te = te;
    data->intf_td = td;

    /* Delta wrt previous */
    data->delta_intf_rb = rb - prev_pbroker_intf_stats.intf_rb;
    data->delta_intf_rp = rp - prev_pbroker_intf_stats.intf_rp;
    data->delta_intf_re = re - prev_pbroker_intf_stats.intf_re;
    data->delta_intf_rd = rd - prev_pbroker_intf_stats.intf_rd;
    data->delta_intf_tb = tb - prev_pbroker_intf_stats.intf_tb;
    data->delta_intf_tp = tp - prev_pbroker_intf_stats.intf_tp;
    data->delta_intf_te = te - prev_pbroker_intf_stats.intf_te;
    data->delta_intf_td = td - prev_pbroker_intf_stats.intf_td;

    /* Interface totals: receive + ttransmit */
    data->total_intf_b = rb + tb;
    data->total_intf_p = rp + tp;
    data->total_intf_e = re + te;
    data->total_intf_d = rd + td;

    /* Deltas wrt prev i.e. current - prev */
    data->delta_total_intf_b = rb + tb - (prev_pbroker_intf_stats.intf_rb + prev_pbroker_intf_stats.intf_tb);
    data->delta_total_intf_p = rp + tp - (prev_pbroker_intf_stats.intf_rp + prev_pbroker_intf_stats.intf_tp);
    data->delta_total_intf_e = re + te - (prev_pbroker_intf_stats.intf_re + prev_pbroker_intf_stats.intf_te);
    data->delta_total_intf_d = rd + td - (prev_pbroker_intf_stats.intf_rd + prev_pbroker_intf_stats.intf_td);

    /* Update prev tp be current for next iteration */
    prev_pbroker_intf_stats.intf_rb = rb;
    prev_pbroker_intf_stats.intf_rp = rp;
    prev_pbroker_intf_stats.intf_re = re;
    prev_pbroker_intf_stats.intf_rd = rd;
    prev_pbroker_intf_stats.intf_tb = tb;
    prev_pbroker_intf_stats.intf_tp = tp;
    prev_pbroker_intf_stats.intf_te = te;
}

/* updated only by the monitor thread */
struct zpn_broker_sys_stats g_broker_sys_stats;
struct zpn_broker_load_stats g_broker_load_stats;
static uint32_t sys_uptime = 0;
static int32_t tcp4_port_util = 0;
static int32_t tcp6_port_util = 0;
static int64_t system_fd_in_use = 0;
static int64_t process_fd_in_use = 0;
static int64_t num_system_tcpv4_socket_inuse = 0;
static int64_t num_system_udpv4_socket_inuse = 0;
static int64_t num_system_tcpv6_socket_inuse = 0;
static int64_t num_system_udpv6_socket_inuse = 0;
static char uptime_info[UPTIME_INFO_MAX];

void zpn_broker_set_broker_status(enum zpn_broker_status status)
{
    broker_status = status;
}

enum zpn_broker_status zpn_broker_get_broker_status(void)
{
    return broker_status;
}

/*
 * zpn_broker_load_notify_status_change
 *  When broker status is changing to state other than Terminating, use this func.
 *  We only need to update the table at this point
 */
void zpn_broker_load_notify_status_change(enum zpn_broker_status status)
{
    if(!ZPN_BROKER_IS_PUBLIC())
        return;

    /* Do not update if state is TERMINATING already */
    if (MAINTENANCE == broker_status || TERMINATING == broker_status)
        return;

    ZPN_LOG(AL_NOTICE, "BrokerReadiness:  Status change:  %s ==> %s",
                        zpn_broker_get_status_string(broker_status), zpn_broker_get_status_string(status));
    broker_status = status;
    zpn_broker_adhoc_load_monitor();
}

/*
 * zpn_broker_update_load_and_terminate
 *   updates the broker load table; and go ahead with termination
 */
static inline void zpn_broker_update_load_and_terminate(void)
{
    /* Initializing interlock */
    zpath_interlock_init(&exit_lock);

    /* handle the case where we exit even before broker init is done */
    if (!g_broker_common_cfg)
        goto regular_exit;

    /* Apply only for public broker */
    if (!ZPN_BROKER_IS_PUBLIC())
        goto regular_exit;

    /* goto normal exit if status is still INVALID */
    if (INVALID == broker_status) {
        goto regular_exit;
    }

    int res = ZPN_RESULT_NO_ERROR;
    res = zpn_broker_adhoc_load_monitor();
    if (res) {
        fprintf(stdout, "Could not write TERMINATING state to broker load table; exit\n");
        goto regular_exit;
    }


    /* Wait on interlock, once we get signal, go ahead and call actual exit handler */
    fprintf(stdout, "Updated TERMINATING status to load table, waiting for signal\n");
    g_broker_load_update_blocking = 1;
    zpath_interlock_wait(&exit_lock);

regular_exit:

    /* we are done with the exit_lock, release the mutex; Applies for pass and fail cases both */
    zpath_interlock_unlock(&exit_lock);

    /* Pass the args to the actual exit fns */
    zpn_broker_final_termination();
}


/**** All termination handlers ***/

/* Exit handler for heartbeat failures */
void zpn_broker_heartbeat_exit_cb(int thread_number)
{
    /* update broker_status to terminating; to ensure term thread comes into action */
    g_broker_termination_notify = 1;
    zpn_broker_set_broker_status(TERMINATING);

    fprintf(stdout, "Invoking heartbeat_exceeded callback for thread: %d(%s)\n",
                        thread_number, zthread_get_thread_name(thread_number));

    /* Return if already in exit path */
    if (g_broker_termination_ctx.tc != zpath_tc_invalid) {
        fprintf(stdout, "Broker already in exit path with term. code: %u\n", g_broker_termination_ctx.tc);
        return;
    }

    /* Disable heartbeat from now on */
    zthread_disable_heartbeat_monitor();

    /* Form the args and set context */
    int *thread_num_ptr =  ZPN_MALLOC(sizeof(int));
    *thread_num_ptr = thread_number;
    g_broker_termination_ctx.tc = zpath_tc_heartbeat_exceeded;
    g_broker_termination_ctx.cookie = thread_num_ptr;
    g_broker_termination_ctx.self_thread_num = thread_number;

    /* Invoke the handler; call the actual exit handler immediately on fail case */
    zpn_broker_update_load_and_terminate();
}

void zpn_broker_exit_handler(enum zpath_termination_code tc, int sig, int self_thread_num)
{
    /* update broker_status to terminating; to ensure term thread comes into action */
    g_broker_termination_notify = 1;
    zpn_broker_set_broker_status(TERMINATING);

    fprintf(stdout, "Invoking exit handler with signal: %d, code: %s on thread: %d(%s)\n",
            sig, zpath_get_termination_code_string(tc),
            self_thread_num, zthread_get_thread_name(self_thread_num));

    /* Return if already in exit path */
    if (g_broker_termination_ctx.tc != zpath_tc_invalid) {
        fprintf(stdout, "Broker already in exit path with term. code: %u\n", g_broker_termination_ctx.tc);
        return;
    }

    /* Disable heartbeat from now on */
    zthread_disable_heartbeat_monitor();

    /* Form the args and set context */
    int *sigptr =  ZPN_MALLOC(sizeof(int));
    *sigptr = sig;
    g_broker_termination_ctx.tc = tc;
    g_broker_termination_ctx.cookie = sigptr;
    g_broker_termination_ctx.self_thread_num = self_thread_num;

    /* Invoke the handler; call the actual exit handler immediately on fail case */
    zpn_broker_update_load_and_terminate();
}

/* Exit handler for calls from zpath lib */
int zpn_broker_lib_exit_cb(enum zpath_termination_code tc, void *cookie, int self_thread_num)
{
    /* update broker_status to terminating; to ensure term thread comes into action */
    g_broker_termination_notify = 1;
    zpn_broker_set_broker_status(TERMINATING);

    fprintf(stdout, "Invoking lib_exit handler with code: %s on thread: %d(%s)\n",
            zpath_get_termination_code_string(tc),
            self_thread_num, zthread_get_thread_name(self_thread_num));

    /* Return if already in exit path */
    if (g_broker_termination_ctx.tc != zpath_tc_invalid) {
        fprintf(stdout, "Broker already in exit path with term. code: %u\n", g_broker_termination_ctx.tc);
        return ZPN_RESULT_NO_ERROR;
    }

    /* Disable heartbeat from now on */
    zthread_disable_heartbeat_monitor();

    /* Form the args and set context */
    void *arg = cookie;
    if (zpath_tc_lib_assert == tc  && cookie) {
        char *assert_buf_copy;
        size_t buf_len  = strlen((char *)cookie);
        assert_buf_copy = ZPN_MALLOC(buf_len + 1);
        memcpy(assert_buf_copy, cookie, buf_len);
        assert_buf_copy[buf_len] = '\0';
        arg = assert_buf_copy;
    }
    g_broker_termination_ctx.tc = tc;
    g_broker_termination_ctx.cookie = arg;
    g_broker_termination_ctx.self_thread_num = self_thread_num;

    /* Invoke the handler; call the actual exit handler immediately on fail case */
    zpn_broker_update_load_and_terminate();
    return ZPN_RESULT_NO_ERROR;
}

/* zpn_broker_update_load_stats - Updates the system resource values for g_broker_load_stats
*  The CPU, Memory, Process and FD utilization are populated here.
*  Some of these values are stored g_broker_sys_stats as well
*  Goal of this function is to update all the resource values used by wally load
*  update.
*/
static void zpn_broker_update_load_stats()
{
    uint64_t mem_total = 0;
    if (zsysinfo(&g_broker_sys_stats.s_cpu_util, &g_broker_sys_stats.s_mem_util, &sys_uptime,
                 &g_broker_sys_stats.s_nonidle, &g_broker_sys_stats.s_idle, &mem_total) != ZSYSINFO_SUCCESS) {
        ZPN_DEBUG_BROKER_LOAD("cpu_util = %d%%, mem_util = %d%%"PRIu64,
                              g_broker_sys_stats.s_cpu_util,
                              g_broker_sys_stats.s_mem_util);
        zpn_broker_load_set_mem_total(mem_total);
    } else {
        ZPN_DEBUG_BROKER_LOAD("Cannot pull sysinfo");
    }
    g_broker_load_stats.mem_util = g_broker_sys_stats.s_mem_util;
    g_broker_load_stats.cpu_util = g_broker_sys_stats.s_cpu_util;
    zpn_broker_load_decorate(&g_broker_load_stats.cpu_util);

    if (zpath_system_get_system_and_process_memory_util_percentage(&g_broker_sys_stats.system_mem_util, &g_broker_sys_stats.process_mem_util) != ZPN_RESULT_NO_ERROR) {
        ZPN_DEBUG_BROKER_LOAD("Can not get memory utilization data");
    }

    if (zpath_system_get_fd_util(&g_broker_sys_stats.s_sys_fd_util, &g_broker_sys_stats.s_proc_fd_util) != ZPN_RESULT_NO_ERROR) {
        ZPN_DEBUG_BROKER_LOAD("Can not get fd utilization data");
    }
    g_broker_load_stats.balance_mode = zpn_broker_maintenance_running() ? BALANCE_MODE_MAINTENANCE : g_balance_config.balance_mode;
    g_broker_load_stats.balance_role = g_balance_config.balance_role;
    g_broker_load_stats.load_skew = g_balance_config.load_skew;
    g_broker_load_stats.status = (uint16_t) zpn_broker_get_broker_status();

    g_broker_load_stats.proc_fd_util = (uint16_t) g_broker_sys_stats.s_proc_fd_util;
    g_broker_load_stats.load = zpn_balance_compute_instance_effective_load(g_broker_load_stats.cpu_util,
                g_broker_load_stats.mem_util, g_broker_load_stats.proc_fd_util, g_broker_load_stats.load_skew);
}

/* zpn_broker_update_sys_stats - Update the remaining g_broker_sys_stats system resource parameters
*/
void zpn_broker_update_sys_stats()
{
    struct zpn_fohh_worker_stats worker_stats;
    struct zpn_fohh_worker_client_stats client_stats = {};
    g_broker_sys_stats.balance_mode = zpn_broker_maintenance_running() ? BALANCE_MODE_MAINTENANCE : g_balance_config.balance_mode;
    g_broker_sys_stats.balance_role = g_balance_config.balance_role;

    if(sys_uptime && !zpn_process_system_uptime(sys_uptime, uptime_info, UPTIME_INFO_MAX)) {
        ZPN_DEBUG_BROKER_LOAD("sys_uptime = %s", (char *)uptime_info);
    } else {
         ZPN_DEBUG_BROKER_LOAD("Cannot process sys uptime info");
    }

    /* Right now, though we are having active_mtunnel in load statistics
     * But, we are not using active_mtunnel in load calculation algorithm
     * This is used only for statistics usage, so updating it only in LOAD_MONITOR interval
    */
    zpn_fohh_worker_current_stats(&worker_stats);
    g_broker_load_stats.active_mtun = worker_stats.num_mtunnel_create - worker_stats.num_mtunnel_destroy;
    g_broker_load_stats.bytes_xfer = (worker_stats.num_bytes_xmt_fohh + worker_stats.num_bytes_xmt_zrdt - broker_worker_stats.num_bytes_xmt_fohh - broker_worker_stats.num_bytes_xmt_zrdt)/ZPN_LOAD_MONITOR_TIMER_SEC;
    g_broker_sys_stats.s_active_mtun = (uint32_t) g_broker_load_stats.active_mtun;

    zpn_fohh_worker_current_stats(&worker_stats);
    broker_worker_stats = worker_stats;

    zpn_fohh_worker_client_current_stats(&client_stats);
    broker_client_stats = client_stats;
    g_broker_load_stats.num_clients = client_stats.num_client;

    if (zpath_system_get_fd_in_use(&system_fd_in_use, &process_fd_in_use) == ZPN_RESULT_NO_ERROR) {
        ZPN_DEBUG_BROKER_LOAD("system_fd_in_use = %ld, process_fd_in_use = %ld", (long) system_fd_in_use, (long) process_fd_in_use);
    } else {
        ZPN_DEBUG_BROKER_LOAD("Can not get fd in use data");
    }

    if (zpath_system_get_udp_socket_util(&g_broker_sys_stats.s_udp4_port_util, &g_broker_sys_stats.s_udp6_port_util) != ZPN_RESULT_NO_ERROR) {
        ZPN_DEBUG_BROKER_LOAD("Can not get udp port utilization data");
    }

    if (zpath_system_get_tcp_socket_util(&tcp4_port_util, &tcp6_port_util) == ZPN_RESULT_NO_ERROR) {
        ZPN_DEBUG_BROKER_LOAD("tcp4_port_util = %d%%, tcp6_port_util = %d%%", tcp4_port_util, tcp6_port_util);
    } else {
        ZPN_DEBUG_BROKER_LOAD("Can not get tcp port utilization data");
    }

    if (zpath_system_get_tcp_socket_inuse(&num_system_tcpv4_socket_inuse, &num_system_tcpv6_socket_inuse) == ZPN_RESULT_NO_ERROR) {
        ZPN_DEBUG_BROKER_LOAD("num_system_tcpv4_socket_inuse = %ld, num_system_tcpv6_socket_inuse = %ld", (long) num_system_tcpv4_socket_inuse, (long) num_system_tcpv6_socket_inuse);
    } else {
        ZPN_DEBUG_BROKER_LOAD("Can not get tcp socket in use data");
    }

    if (zpath_system_get_udp_socket_inuse(&num_system_udpv4_socket_inuse, &num_system_udpv6_socket_inuse) == ZPN_RESULT_NO_ERROR) {
        ZPN_DEBUG_BROKER_LOAD("num_system_udpv4_socket_inuse = %ld, num_system_udpv6_socket_inuse = %ld", (long) num_system_udpv4_socket_inuse, (long) num_system_udpv6_socket_inuse);
    } else {
        ZPN_DEBUG_BROKER_LOAD("Can not get udp socket in use data");
    }
}

int zpn_broker_private_broker_load_monitor(zpn_broker_xmit_pbinfo_callback *pbinfo_callback) {
    int res = 0;
    if (g_broker_common_cfg->instance_type == ZPN_INSTANCE_TYPE_PRIVATE_BROKER) {
        /* Send private broker load via RPC to public broker. */
        struct zpn_private_broker_load pbl;
        struct zpn_pbroker_status_report pb_report;
        struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();
        struct zpn_private_broker_system_stats *sys_stats = &gs->sys_stats;
        struct zroute default_route;
        struct sockaddr_storage resolver[ZCDNS_MAX_RESOLVERS];
        socklen_t resolver_len[ZCDNS_MAX_RESOLVERS];
        int resolver_count = 0;
        struct zpn_private_broker *pb;
        struct zinterfaces ifs[MAX_INTERFACES];
        int ifs_count = 0;
        char publish_strs_data[MAX_INTERFACES][256];
        char *publish_strs_ptr[MAX_INTERFACES];
        char **publish_strs = NULL;
        size_t publish_strs_count = 0;
        int i;

        /* Get cpu, mem, port util, fd util from monitor */
        g_broker_load_stats.cpu_util = sys_stats->cpu_util;
        g_broker_load_stats.mem_util = (int16_t)sys_stats->system_mem_util;
        g_broker_sys_stats.s_udp4_port_util = sys_stats->udp4_port_util;
        g_broker_sys_stats.s_udp6_port_util = sys_stats->udp6_port_util;
        tcp4_port_util = sys_stats->tcp4_port_util;
        tcp6_port_util = sys_stats->tcp6_port_util;
        g_broker_sys_stats.s_sys_fd_util = sys_stats->sys_fd_util;
        g_broker_load_stats.proc_fd_util = (uint16_t) sys_stats->proc_fd_util;

        /* Fill in_use data to print when zpn/broker/load debug command is invoked. */
        system_fd_in_use = sys_stats->system_fd_in_use;
        process_fd_in_use = sys_stats->process_fd_in_use;
        num_system_tcpv4_socket_inuse = sys_stats->num_system_tcpv4_socket_inuse;
        num_system_tcpv6_socket_inuse = sys_stats->num_system_tcpv6_socket_inuse;
        num_system_udpv4_socket_inuse = sys_stats->num_system_udpv4_socket_inuse;
        num_system_udpv6_socket_inuse = sys_stats->num_system_udpv6_socket_inuse;

        if (pbroker_control_connection) {

            memset(&pbl, 0, sizeof(pbl));
            memset(&pb_report, 0, sizeof(pb_report));
            pbl.gid = broker_instance_id;
            pbl.customer_gid = ZPATH_GID_GET_CUSTOMER_GID(g_broker_common_cfg->private_broker.broker_id);
            pbl.cpu_util = g_broker_load_stats.cpu_util;
            pbl.mem_util = g_broker_load_stats.mem_util;
            pbl.active_mtun = (uint32_t) g_broker_load_stats.active_mtun;
            pbl.clients = g_broker_load_stats.num_clients; //this column was always present, but we never set it until ET-65845
            pbl.bytes_xfer = g_broker_load_stats.bytes_xfer;

            res = zpn_private_broker_get_by_id_wait(g_broker_common_cfg->private_broker.broker_id, &pb);
            if (res) {
                ZPN_LOG(AL_CRITICAL, "Could not get private broker");
                return ZPN_RESULT_ERR;
            }

            int need_all_ipv4 = 0;
            int need_all_ipv6 = 0;
            if (pb->publish_count) {
                publish_strs = pb->publish;
                publish_strs_count = pb->publish_count;
            } else if (pb->listen_ips_count) {
                for (i = 0; i < pb->listen_ips_count; i++) {
                    if (argo_inet_is_ipv4_any(&(pb->listen_ips[i]))) {
                        need_all_ipv4 = 1;
                    } else if (argo_inet_is_ipv6_any(&(pb->listen_ips[i]))) {
                        need_all_ipv6 = pb->publish_ipv6 ? 1 : 0;
                    } else {
                        argo_inet_generate(&(publish_strs_data[publish_strs_count][0]), &(pb->listen_ips[i]));
                        publish_strs_ptr[publish_strs_count] = &(publish_strs_data[publish_strs_count][0]);
                        publish_strs_count++;
                    }
                }
                publish_strs = &(publish_strs_ptr[0]);
            } else {
                need_all_ipv4 = 1;
                need_all_ipv6 = pb->publish_ipv6 ? 1 : 0;
                publish_strs = &(publish_strs_ptr[0]);
            }

            /* Get default route */
            if (get_default_route(&default_route) == ZSYSINFO_SUCCESS) {
                struct sockaddr_in *gw_addr_in = (struct sockaddr_in *)&(default_route.gw);
                struct argo_inet inet;
                uint16_t port_ne;

                argo_sockaddr_to_inet((struct sockaddr *)&default_route.gw, &inet, &port_ne);

                ZPN_DEBUG_PBROKER("Default route: interface = %s, gateway = %s", default_route.intf_name, inet_ntoa(gw_addr_in->sin_addr));
                pb_report.dft_rt_intf = default_route.intf_name;
                pb_report.dft_rt_gw = inet;
            }

            /* Get resolver */
            if (gs->zcdns) {
                zcdns_get_resolvers(gs->zcdns, resolver, resolver_len, &resolver_count);

                for (i = 0; i < resolver_count; i++) {
                    char str[ARGO_INET_ADDRSTRLEN];
                    struct sockaddr_storage *ss = &resolver[i];
                    struct argo_inet inet;
                    uint16_t port_ne;

                    argo_sockaddr_to_inet((struct sockaddr *)ss, &inet, &port_ne);

                    ZPN_DEBUG_PBROKER("Resolver %d: addr = %s, port = %d", i + 1, argo_inet_generate(str, &inet), ntohs(port_ne));
                    if (i == 0) {
                        pb_report.resolver = inet;
                    }
                }
            }

            if (need_all_ipv4 || need_all_ipv6) {

                /* Get IPs from interfaces, since we are listening on IN_ADDR_ANY */
                res = get_interfaces(&(ifs[0]), &ifs_count);
                if (res == ZSYSINFO_SUCCESS) {

                    /* Get interface stats */
                    zpn_pbroker_status_report_get_if_stats(ifs, ifs_count, &pb_report);

                    for (i = 0; i < ifs_count; i++) {
                        struct argo_inet inet;
                        argo_sockaddr_to_inet((struct sockaddr *)&(ifs[i].addr), &inet, NULL);
                        argo_inet_generate(&(publish_strs_data[publish_strs_count][0]), &inet);

                        if (is_ip_filtered(&inet)) continue;
                        if ((inet.length == 4) && (!need_all_ipv4)) continue;
                        if ((inet.length == 16) && (!need_all_ipv6)) continue;
                        if (pb->listen_ips_count) {
                            /* If it is already in the listen list, skip it */
                            int j;
                            for (j = 0; j < pb->listen_ips_count; j++) {
                                if (memcmp(&inet, &(pb->listen_ips[j]), sizeof(inet)) == 0) break;
                            }
                            if (j != pb->listen_ips_count) continue; /* Skip! */
                        }
                        argo_inet_generate(&(publish_strs_data[publish_strs_count][0]), &inet);
                        publish_strs_ptr[publish_strs_count] = &(publish_strs_data[publish_strs_count][0]);
                        publish_strs_count++;
                    }
                }
                publish_strs = &(publish_strs_ptr[0]);
            }

            pbl.publish = publish_strs;
            pbl.publish_count = publish_strs_count;

            res = fohh_argo_serialize(pbroker_control_connection,
                                    zpn_private_broker_load_description,
                                    &pbl,
                                    0,
                                    fohh_queue_element_type_mission_critical);
            if (res) {
                ZPN_LOG(AL_ERROR, "Could not send load: %s", zpn_result_string(res));
            } else {
                char dump[1000];
                argo_structure_dump(zpn_private_broker_load_description, &pbl, dump, sizeof(dump), NULL, 0);
                ZPN_DEBUG_STARTUP("Sending private_broker_load: %s", dump);
            }

            pb_report.g_pbrk = broker_instance_id;
            pb_report.cpu_util = g_broker_load_stats.cpu_util;
            pb_report.mem_util = g_broker_load_stats.mem_util;
            pb_report.number_of_clients = g_broker_load_stats.num_clients;

            if (0 == zpath_system_get_uptime_s(&gs->host_uptime_s)) {
                pb_report.sys_uptime_s = epoch_s() - gs->host_uptime_s;
            } else {
                ZPN_LOG(AL_ERROR, "Could not get the system uptime info");
                pb_report.sys_uptime_s = 0;
            }
            pb_report.pb_uptime_s = gs->pb_start_time_s;

            pb_report.udp4_port_util = g_broker_sys_stats.s_udp4_port_util;
            pb_report.udp6_port_util = g_broker_sys_stats.s_udp6_port_util;

            pb_report.sys_fd_util = g_broker_sys_stats.s_sys_fd_util;
            pb_report.proc_fd_util = g_broker_load_stats.proc_fd_util;

            snprintf(pb_report.ovd_broker_cn, sizeof(pb_report.ovd_broker_cn), "%s", pb_get_override_server_cn());
            struct fohh_log_endpoint_cns* uniq_cns;
            fohh_log_get_uniq_endpoint_cns(&uniq_cns);
            if (uniq_cns && uniq_cns->num_cns) {
                pb_report.log_brokers_uniq = uniq_cns->cns;
                pb_report.log_brokers_uniq_count = uniq_cns->num_cns;
            } else {
                pb_report.log_brokers_uniq = NULL;
                pb_report.log_brokers_uniq_count = 0;
            }

            struct fohh_log_endpoint_cns* cns_data;
            pbroker_log_get_endpoint_data(&cns_data, gs);
            if (cns_data && cns_data->num_cns) {
                pb_report.all_log_brokers_data = cns_data->cns;
                pb_report.all_log_brokers_data_count = cns_data->num_cns;
            } else {
                pb_report.all_log_brokers_data = NULL;
                pb_report.all_log_brokers_data_count = 0;
            }

            pb_report.last_os_upgrade_time = gs->last_os_upgrade_time;
            pb_report.last_sarge_upgrade_time = gs->last_sarge_upgrade_time;
            pb_report.platform_version = gs->platform_version;

            /* Send status report via RPC */
            res = fohh_argo_serialize(pbroker_control_connection,
                                      zpn_pbroker_status_report_description,
                                      &pb_report,
                                      0,
                                      fohh_queue_element_type_control);
            if (res) {
                ZPN_LOG(AL_ERROR, "Could not send pbroker status report: %s", zpn_result_string(res));
            } else {
                char dump[2000];
                argo_structure_dump(zpn_pbroker_status_report_description, &pb_report, dump, sizeof(dump), NULL, 0);
                ZPN_DEBUG_STARTUP("Sending zpn_pbroker_status_report: %s", dump);
            }

            /* Send pbinfo via RPC */
            if (pbinfo_callback) {
                pbinfo_callback(&pbl, &pb_report);
            }

            if (uniq_cns) {
                fohh_free_uniq_cns(uniq_cns);
                uniq_cns = NULL;
            }

            if(cns_data) {
                fohh_free_uniq_cns(cns_data);
                cns_data = NULL;
            }
        } else {
            if (!is_pbroker_running_drmode()) {
                ZPN_LOG(AL_ERROR, "No control connection");
            }
        }
    }
    return res;
}

int zpn_broker_adhoc_load_monitor() {
    int res = ZPN_RESULT_NO_ERROR;
    zpn_broker_update_load_stats();
    res = zpn_broker_load_write(&g_broker_load_stats);
    if (res != ZPN_RESULT_NO_ERROR) {
        ZPN_LOG(AL_WARNING, "Cannot write to zpn_broker_load table: %s", zpn_result_string(res));
    }
    return res;
}

/* zpn_broker_update_load_table - Send the latest load information to wally
*/
static int zpn_broker_update_load_table() {
    int res=0;
    res = zpn_broker_load_write(&g_broker_load_stats);

    if (res != ZPN_RESULT_NO_ERROR) {
        /* If the load write to zpn_broker_load fails, then it will be updated next time */
        ZPN_LOG(AL_WARNING, "Load Monitor: Cannot write to zpn_broker_load table: %s", zpn_result_string(res));
    } else {
        g_broker_load_stats.report_count++;
    }
    return res;
}

/* zpn_broker_load_monitor -
*  The load monitor call back function invoked by the timer.
*  LOAD Monitor timer callback.
*/
int zpn_broker_load_monitor(void)
{
    int res = 0;
    zpn_broker_update_sys_stats();
    if(ZPN_BROKER_IS_PUBLIC()) {
        /* We are having this adaptive load monitor under feature flag
         * Becasue we want to measure our system behavior and performance
         * If this generates more load reports per minute
         * We will plan to remove this feature flag later based on our observation
         */
        if (!zbal_config_obj.adaptive_load_feature_flag) {
            zpn_broker_update_load_stats();
            res = zpn_broker_update_load_table();
        }
        zpn_broker_write_load_sys_stats(&g_broker_sys_stats, &g_broker_load_stats);
        g_broker_load_stats.report_count=0;
    } else {
        zpn_broker_update_load_stats();
        res = zpn_broker_private_broker_load_monitor(zpn_private_broker_site_xmit_pbinfo);
    }
    return res;
}

/* zpn_broker_adaptive_load_monitor
*  Monitors the load of the system periodically
*  Send a load update based on zbal_config_obj.report_interval
*/
static void zpn_broker_adaptive_load_monitor(void)
{
    zpn_broker_update_load_stats();
    /* Initiate load_table_write Only if it meets following conditions
    *  Last report time is greater than ZPN_LOAD_MONITOR_TIMER_SEC
    *   OR
    *  1. Last load update was sent zbal_config_obj.report_interval seconds ago
    *  2. The system metrics crossed the threshold
    */
   int64_t now_s = epoch_s();
    if ( ((now_s-zbal_data_obj.last_report_time) >= ZPN_LOAD_MONITOR_TIMER_SEC ) ||
        ( ((now_s-zbal_data_obj.last_report_time) >= zbal_config_obj.report_interval) &&
        ((abs(zbal_data_obj.cpu_util-g_broker_load_stats.cpu_util) >=zbal_config_obj.cpu_spike_threshold) ||
        (abs(zbal_data_obj.mem_util-g_broker_load_stats.mem_util) >=zbal_config_obj.mem_spike_threshold) ||
        (abs(zbal_data_obj.proc_fd_util-g_broker_load_stats.proc_fd_util) >=zbal_config_obj.proc_fd_spike_threshold))) )
    {
        if (zpn_broker_update_load_table()  == ZPN_RESULT_NO_ERROR) {
            /* Rebase the adaptive load object values */
            zbal_data_obj.last_report_time=now_s;
            zbal_data_obj.cpu_util=g_broker_load_stats.cpu_util;
            zbal_data_obj.mem_util=g_broker_load_stats.mem_util;
            zbal_data_obj.proc_fd_util=g_broker_load_stats.proc_fd_util;
        }
    }
}

static int zpn_broker_debug_load(struct zpath_debug_state *request_state,
                                const char **query_values,
                                int query_value_count,
                                void *cookie)
{
    ZDP("\n");
    if (query_values[0] && !strcasecmp(query_values[0], "json")) {
        ZDP("{\"cpu_util_percentage\": %"PRId16", \"mem_util_percentage\": %"PRId16", \"num_clients\": %"PRIu64 ", \"sys_uptime\": \"%s\", \"active_mtun\": %"PRId64", \"bytes_xfer\": %"PRId64", \"udp4_port_util_percentage\": %d, \"udp6_port_util_percentage\": %d,"
            "\"tcp4_port_util_percentage\": %d, \"tcp6_port_util_percentage\": %d, \"sys_fd_util_percentage\": %d, \"proc_fd_util_percentage\": %"PRIu16", \"system_fd_in_use\": %ld, \"process_fd_in_use\": %ld,"
            "\"num_system_tcpv4_socket_inuse\": %ld, \"num_system_tcpv6_socket_inuse\": %ld, \"num_system_udpv4_socket_inuse\": %ld, \"num_system_udpv6_socket_inuse\": %ld, "
            "\"broker_role\": \"%s\", \"broker_mode\": \"%s\", \"load_skew\": %d",
            g_broker_load_stats.cpu_util, g_broker_load_stats.mem_util, g_broker_load_stats.num_clients, uptime_info, g_broker_load_stats.active_mtun, g_broker_load_stats.bytes_xfer, g_broker_sys_stats.s_udp4_port_util, g_broker_sys_stats.s_udp6_port_util, tcp4_port_util, tcp6_port_util, g_broker_sys_stats.s_sys_fd_util, g_broker_load_stats.proc_fd_util,
            (long) system_fd_in_use, (long) process_fd_in_use, (long) num_system_tcpv4_socket_inuse, (long) num_system_tcpv6_socket_inuse,
            (long) num_system_udpv4_socket_inuse, (long) num_system_udpv6_socket_inuse,  g_balance_config.balance_role,
            zpn_broker_maintenance_running() ? BALANCE_MODE_MAINTENANCE : g_balance_config.balance_mode,
            g_balance_config.load_skew);

        if (ZPN_BROKER_IS_PUBLIC()) {
            ZDP(", \"broker_status\": \"%s\"", zpn_broker_get_status_string(broker_status));
            ZDP(", \"dns_txt_support\": %d", (int)g_txt_support);
            ZDP(", \"Total_mem\": %"PRIu64"", zpn_broker_load_get_mem_total());
            ZDP(", \"Available_cpus\": %"PRIu32"", zpn_broker_load_get_available_cpus());
            ZDP(", \"Base_host_serial_id\": \"%s\"", zpn_broker_load_get_base_host_serial_id());
        }
        ZDP("}\n"); /* terminate the bracket */
    } else {
        ZDP("broker_status: %s\n", zpn_broker_get_status_string(broker_status));
        ZDP("broker role: %s \t broker mode: %s \t load skew: %d\n", g_balance_config.balance_role,
        zpn_broker_maintenance_running() ? BALANCE_MODE_MAINTENANCE : g_balance_config.balance_mode,
        g_balance_config.load_skew);
        ZDP("num_system_tcpv4_socket_inuse: %ld, num_system_tcpv6_socket_inuse: %ld, num_system_udpv4_socket_inuse: %ld, num_system_udpv6_socket_inuse: %ld\n",
            (long)num_system_tcpv4_socket_inuse, (long)num_system_tcpv6_socket_inuse, (long)num_system_udpv4_socket_inuse, (long)num_system_udpv6_socket_inuse);
        ZDP("system_fd_in_use: %ld, process_fd_in_use: %ld\n", (long)system_fd_in_use, (long)process_fd_in_use);
        ZDP("dns_txt_support: %d\n", (int)g_txt_support);
        ZDP("------------------------------------------------------------\n");
        ZDP("Broker System Stats: \n");
        ZDP("------------------------------------------------------------\n");
        ZDP("cpu_util_percentage: %"PRIu16", mem_util_percentage: %"PRIu16", sys_uptime: %s, active_mtun: %"PRIu32", bytes_xfer: %"PRIu32"\n",g_broker_sys_stats.s_cpu_util, g_broker_sys_stats.s_mem_util, uptime_info, g_broker_sys_stats.s_active_mtun, g_broker_sys_stats.s_bytes_xfer);
        ZDP("cpu nonidle: %"PRIu64"%% idle: %"PRIu64"%%\n",g_broker_sys_stats.s_nonidle,g_broker_sys_stats.s_idle);
        ZDP("udp4_port_util_percentage: %d, udp6_port_util_percentage: %d, tcp4_port_util_percentage: %d, tcp6_port_util_percentage: %d\n", g_broker_sys_stats.s_udp4_port_util, g_broker_sys_stats.s_udp6_port_util, tcp4_port_util, tcp6_port_util);
        ZDP("sys_fd_util_percentage: %d, proc_fd_util_percentage: %d\n", g_broker_sys_stats.s_sys_fd_util, g_broker_sys_stats.s_proc_fd_util);

        ZDP("------------------------------------------------------------\n");
        ZDP("Broker Load Stats: \n");
        ZDP("------------------------------------------------------------\n");
        ZDP("cpu_util_percentage: %"PRId16", mem_util_percentage: %"PRId16", proc_fd_util_percentage:  %"PRId16"\n",
                    g_broker_load_stats.cpu_util,
                    g_broker_load_stats.mem_util,
                    g_broker_load_stats.proc_fd_util);
        ZDP("num_clients: %"PRIu64", active_mtun: %"PRId64", bytes_xfer: %"PRId64"\n",
                    g_broker_load_stats.num_clients,
                    g_broker_load_stats.active_mtun,
                    g_broker_load_stats.bytes_xfer);
        ZDP("Load_report_count: %"PRIu16" \n", g_broker_load_stats.report_count);
        ZDP("Load_percentage: %"PRIu16" \n", g_broker_load_stats.load);
        if (ZPN_BROKER_IS_PUBLIC()) {
            ZDP("Total_mem: %"PRIu64" KB, Available_cpus: %"PRIu32" Base_host_serial_id: %s\n",
                zpn_broker_load_get_mem_total(), zpn_broker_load_get_available_cpus(),
                zpn_broker_load_get_base_host_serial_id());
        }

        if ((zbal_config_obj.adaptive_load_feature_flag) &&
           (ZPN_BROKER_IS_PUBLIC())) {
            int64_t last_report=epoch_s()-zbal_data_obj.last_report_time;
            ZDP("------------------------------------------------------------\n");
            ZDP("adaptive_load_monitor:\n");
            ZDP("------------------------------------------------------------\n");
            ZDP("config: report_interval:  %3"PRId64", cpu_spike_threshold:  %3"PRId64", mem_spike_threshold:  %3"PRId64", proc_fd_spike_threshold:  %3"PRId64"\n",zbal_config_obj.report_interval,zbal_config_obj.cpu_spike_threshold,zbal_config_obj.mem_spike_threshold,zbal_config_obj.proc_fd_spike_threshold);
            ZDP("data: cpu_util_percentage:  %3"PRId16", mem_util_percentage: %3"PRId16", proc_fd_util_percentage:  %3"PRIu16", Last Report Time:  %"PRId64" seconds ago \n",zbal_data_obj.cpu_util, zbal_data_obj.mem_util,zbal_data_obj.proc_fd_util, last_report);
        }
    }
    ZDP("\n");
    return ZPN_RESULT_NO_ERROR;
}

int zpn_broker_zpm_conn_disconn_stats(struct zpath_debug_state *request_state,
                                        const char **query_values,
                                        int query_value_count,
                                        void *cookie)
{
    if (!g_broker_zpm_conn_config_monitor_flag) {
        ZDP("broker zpm monitoring feature is disabled!!\n");
        return ZPATH_RESULT_NO_ERROR;
    }
    ZDP("Broker zpm connect/disconnect stats:");
    ZDP("\nConnected: %"PRId64,g_zpn_broker_zpm_conn_stats.connect_count);
    ZDP("\nDisconnected: %"PRId64,g_zpn_broker_zpm_conn_stats.disconnect_count);
    ZDP("\nConnect Attempts (before a successful connect): %"PRId64,g_zpn_broker_zpm_conn_stats.connect_attempt_count);
    ZDP("\n");
    return ZPATH_RESULT_NO_ERROR;
}

int zpn_broker_zpm_conn_status(struct zpath_debug_state *request_state,
                                        const char **query_values,
                                        int query_value_count,
                                        void *cookie)
{
    ZDP("broker zpm monitoring feature is %s\n",
            (g_broker_zpm_conn_config_monitor_flag != 0) ? "enabled" : "disabled");
    ZDP("broker to zpm connection is %s\n",
            (g_zpn_broker_zpm_conn_status != 0) ? "enabled" : "disabled");

    return ZPATH_RESULT_NO_ERROR;
}

int zpn_broker_zthread_termination_stuck_zpm_bails_out(struct zpath_debug_state *request_state,
                                        const char **query_values,
                                        int query_value_count,
                                        void *cookie)
{
    if (!g_broker_zpm_conn_config_monitor_flag) {
        ZDP("broker zpm monitoring feature is disabled!!\n");
        return ZPATH_RESULT_NO_ERROR;
    }

    g_zthread_termination_stuck_zpm_bails_out = 1;
    ZDP("g_zthread_termination_stuck_zpm_bails_out set to 1\n");
    sleep(100500);
    return ZPATH_RESULT_NO_ERROR;
}

int zpn_broker_zthread_termination_deadlock_zpm_bails_out(struct zpath_debug_state *request_state,
                                        const char **query_values,
                                        int query_value_count,
                                        void *cookie)
{
    if (!g_broker_zpm_conn_config_monitor_flag) {
        ZDP("broker zpm monitoring feature is disabled!!\n");
        return ZPATH_RESULT_NO_ERROR;
    }

    g_zthread_termination_deadlock_zpm_bails_out = 1;
    ZDP("g_zthread_termination_deadlock_zpm_bails_out set to 1\n");
    sleep(100500);
    return ZPATH_RESULT_NO_ERROR;
}

int zpn_broker_zthread_stuck_term_thread_bails_out(struct zpath_debug_state *request_state,
                                        const char **query_values,
                                        int query_value_count,
                                        void *cookie)
{
    if (!g_broker_zpm_conn_config_monitor_flag) {
        ZDP("broker zpm monitoring feature is disabled!!\n");
        return ZPATH_RESULT_NO_ERROR;
    }

    g_zthread_stuck_termination_bails_out = 1;
    ZDP("broker-zpm: g_zthread_stuck_termination_bails_out set to 1\n");
    sleep(100500);
    return ZPATH_RESULT_NO_ERROR;
}

/*
 * Initialize zpm related commands and stats log
 * Commands show the right information only if feature is enabled.
 */
static int zpn_broker_zpm_cmd_init(void)
{
    int res = ZPN_RESULT_NO_ERROR;

    /* Register broker zpm conn stats */
    struct argo_structure_description *zpm_conn_stats_description;
    zpm_conn_stats_description = argo_register_global_structure(ZPN_BROKER_ZPM_STATS_HELPER);

    if (!argo_log_register_structure(argo_log_get("statistics_log"),
                        "zpm_conn_stats",
                        AL_INFO,
                        5*60*1000*1000, /* 5 mins interval */
                        zpm_conn_stats_description,
                        &g_zpn_broker_zpm_conn_stats,
                        1,
                        NULL,
                        NULL))  {
        ZPN_LOG(AL_CRITICAL, "Could not register broker zpm conn stats");
        return ZPN_RESULT_ERR;
    }

    /* Register broker zpm conn debug commands */
    /* add debug cmd for enablement status */
    res = zpath_debug_add_read_command("Broker ZPM connection status",
                                  "/zpn/broker/zpm_conn_status",
                                  zpn_broker_zpm_conn_status,
                                  NULL,
                                  NULL);
    if (res) {
        ZPN_LOG(AL_ERROR, "broker-zpm: Could not init /zpn/broker/zpm_conn_status debug command, res = %s", zpn_result_string(res));
        return res;
    }

    /* add debug cmd for tracking the connects and disconnects */
    res = zpath_debug_add_read_command("Broker ZPM connect/disconnect status",
                                  "/zpn/broker/zpm_conn_stats",
                                  zpn_broker_zpm_conn_disconn_stats,
                                  NULL,
                                  NULL);
    if (res) {
        ZPN_LOG(AL_ERROR, "broker-zpm: Could not init /zpn/broker/zpm_conn_stats debug command, res = %s", zpn_result_string(res));
        return res;
    }

    if(zpath_is_dev_environment()) {
        /* scenario 1 - hb missed, zthread and termination thread stuck, zpm bails broker out */
        res = zpath_debug_add_admin_command("broker-zpm: HB miss, zthread termination thread stuck, zpm bails out",
                                    "/zpn/broker/zthread_term_stuck_zpm_bails_out",
                                    zpn_broker_zthread_termination_stuck_zpm_bails_out,
                                     NULL,
                                     NULL);
        if (res) {
            ZPN_LOG(AL_ERROR, "broker-zpm: Could not init /zpn/broker/zthread_term_stuck_zpm_bail_out debug command, res = %s", zpn_result_string(res));
            return res;
        }
        /* scenario 2 - hb missed, zthread and termination deadlock, zpm bails broker out */
        res = zpath_debug_add_admin_command("broker-zpm: HB miss, zthread termination thread deadlock, zpm bails out",
                                    "/zpn/broker/zthread_term_deadlock_zpm_bails_out",
                                    zpn_broker_zthread_termination_deadlock_zpm_bails_out,
                                    NULL,
                                    NULL);
        if (res) {
            ZPN_LOG(AL_ERROR, "broker-zpm: Could not init /zpn/broker/zthread_term_deadlock_zpm_bails_out debug command, res = %s", zpn_result_string(res));
            return res;
        }

        /* scenario 3 - hb missed, zthread stuck termination bails broker out ( zpm not in picture ) */
        res = zpath_debug_add_admin_command("broker-zpm: HB miss, zthread stuck, termination thread bails out",
                                    "/zpn/broker/zthread_stuck_term_thread_bails_out",
                                    zpn_broker_zthread_stuck_term_thread_bails_out,
                                    NULL,
                                    NULL);
        if (res) {
            ZPN_LOG(AL_ERROR, "broker-zpm: Could not init /zpn/broker/zthread_stuck_term_thread_bails_out debug command, res = %s", zpn_result_string(res));
            return res;
        }
    }
    return res;
}

static int zpn_broker_zpm_conn_init(struct event_base *base)
{
    int res = 0;

    res = zpn_broker_connect_to_process_monitor(base);
    if(0 != res) {
        ZPN_LOG(AL_ERROR,"broker-zpm: broker conn to process monitor failed %s",zpn_result_string(res));
        return res;
    }

    return ZPN_RESULT_NO_ERROR;
}

static int zpn_broker_lp_status(struct zpath_debug_state *request_state,
                                        const char **query_values,
                                        int query_value_count,
                                        void *cookie) {
    ZDP("\n");
    if (query_values[0] && !strcasecmp(query_values[0], "json")) {
        ZDP("{\n\"Logical Partitioning feature\": \"%s\",\n\"Logical Partitioning initialisation\": \"%s\",\n"
        "\"Logical Partitioning current profile version\": %"PRId64",\n"
        "\"Logical Partitioning active profile version\": %"PRId64",\n"
        "\"Logical Partitioning profile activation\": \"%s\"\n}\n",
        zpath_get_logical_partition_feature_status()?"enabled":"disabled",
        (-1 == g_active_profile_version)?"successful":((g_lp_init_status)?"failed":"successful"),
        g_current_profile_version,
        g_active_profile_version, g_lp_init_status ? "no" : "yes");
    } else {
        ZDP("Logical Partitioning feature: %s\n", zpath_get_logical_partition_feature_status()?"enabled":"disabled");
        if(-1 == g_active_profile_version) {
            ZDP("Logical Partitioning initialisation: successful for this broker\n");
        } else {
            ZDP("Logical Partitioning initialisation: %s for this broker\n", (g_lp_init_status)?"failed":"successful");
        }
        ZDP("Logical Partitioning current profile version: %"PRId64"\n", g_current_profile_version);
        ZDP("Logical Partitioning active profile version: %"PRId64"\n", g_active_profile_version);
        ZDP("Logical Partitioning profile activation: %s \n", g_lp_init_status ? "no" : "yes");
    }
    ZDP("\n");
    return ZPN_RESULT_NO_ERROR;
}

static int  zpn_broker_zhm_upload_stats(struct zpath_debug_state *request_state,
                                       const char **query_values,
                                       int query_value_count,
                                       void *cookie)
{
    if (ZPN_BROKER_IS_PUBLIC() && cookie) {
        if (query_value_count != 6) return ZPN_RESULT_BAD_ARGUMENT;
        struct zpn_broker_zhm_stats *stats = cookie;
        for (int i = 0; i < query_value_count; i++) {
            if (query_values[i]) {
                const int64_t val = strtol(query_values[i], NULL, 10);
                if (i == 0) {
                    stats->all = val;
                } else if (i == 1) {
                    stats->lookups = val;
                } else if (i == 2) {
                    stats->ok = val;
                } else if (i == 3) {
                    stats->fail = val;
                } else if (i == 4) {
                    stats->msg_id = val;
                } else if (i == 5) {
                    stats->zpm_connects = val;
                }
            } else {
                return ZPN_RESULT_BAD_ARGUMENT;
            }
        }
    }

    return ZPN_RESULT_NO_ERROR;
}

static int  zpn_broker_zpm_upload_abrt_kill_stats(struct zpath_debug_state *request_state,
                                                  const char **query_values,
                                                  int query_value_count,
                                                  void *cookie)
{
    if (ZPN_BROKER_IS_PUBLIC() && cookie) {
        if (query_value_count != 3) return ZPN_RESULT_BAD_ARGUMENT;
        struct zpn_broker_zpm_abrt_kill_stats *stats = cookie;
        for (int i = 0; i < query_value_count; i++) {
            if (query_values[i]) {
                const int64_t val = strtol(query_values[i], NULL, 10);
                if (i == 0) {
                    stats->sig_abrt = val;
                } else if (i == 1) {
                    stats->sig_kill = val;
                } else if (i == 2) {
                    stats->msg_id = val;
                }
            } else {
                return ZPN_RESULT_BAD_ARGUMENT;
            }
        }
    }

    return ZPN_RESULT_NO_ERROR;
}

static int zpn_broker_print_alt_cloud(struct zpath_debug_state *request_state,
                                        const char **query_values,
                                        int query_value_count,
                                        void *cookie)
{
    char alt_cloud[ZPN_MAX_ALT_CLOUD_NAME_LEN + 1];
    int feature_enabled;
    char *broker_type;

    if (ZPN_BROKER_IS_PUBLIC()) {
        feature_enabled = broker_alt_cloud_support_enabled;
        broker_type = "public";
    } else {
        feature_enabled = (*g_broker_common_cfg->private_broker.alt_cloud_feature_state_initialised &&
                           *g_broker_common_cfg->private_broker.alt_cloud_feature_state);
        broker_type = "private";
    }

    ZDP("Alt cloud feature is %s for this %s broker\n",
        (feature_enabled) ? "enabled" : "disabled", broker_type);

    if (zpn_broker_get_configured_alt_cloud()) {
        ZDP("Alt cloud configured for this %s broker: %s\n",
                    broker_type, zpn_broker_get_configured_alt_cloud());
    } else {
        ZDP("Alt cloud is not configured for this %s broker\n", broker_type);
    }

    zpn_broker_get_cloud_name(alt_cloud, sizeof(alt_cloud));
    ZDP("%s broker is listening to cloud %s\n", broker_type, alt_cloud);

    ZDP("%s instance full name is: %s\n", broker_type, zpn_broker_get_instance_full_name());
    return ZPN_RESULT_NO_ERROR;
}

static int zpn_broker_diag_load(struct zpath_debug_state *request_state,
                                void *cookie, int verbose)
{
    ZDP("\n");
    if (verbose) {
        ZDP("cpu_util_percentage: %"PRIu16", mem_util_percentage: %"PRIu16", sys_uptime: %s, active_mtun: %"PRId64", bytes_xfer: %"PRId64"\n",g_broker_sys_stats.s_cpu_util, g_broker_sys_stats.s_mem_util, uptime_info, g_broker_load_stats.active_mtun, g_broker_load_stats.bytes_xfer);
        ZDP("num_system_tcpv4_socket_inuse: %ld, num_system_tcpv6_socket_inuse: %ld, num_system_udpv4_socket_inuse: %ld, num_system_udpv6_socket_inuse: %ld\n",
            (long)num_system_tcpv4_socket_inuse, (long)num_system_tcpv6_socket_inuse, (long)num_system_udpv4_socket_inuse, (long)num_system_udpv6_socket_inuse);
        ZDP("udp4_port_util_percentage: %d, udp6_port_util_percentage: %d, tcp4_port_util_percentage: %d, tcp6_port_util_percentage:%d \n", g_broker_sys_stats.s_udp4_port_util, g_broker_sys_stats.s_udp6_port_util, tcp4_port_util, tcp6_port_util);
        ZDP("system_fd_in_use: %ld, process_fd_in_use: %ld \n", (long)system_fd_in_use, (long)process_fd_in_use);
        ZDP("sys_fd_util_percentage: %d, proc_fd_util_percentage: %d\n", g_broker_sys_stats.s_sys_fd_util, g_broker_sys_stats.s_proc_fd_util);
    }
    if (g_broker_sys_stats.s_cpu_util > DIAGNOSTICS_CMD_THRESHOLD) {
        ZDP("cpu_util_percentage %"PRIu16" crossed threshold percentage %d\n", g_broker_sys_stats.s_cpu_util, DIAGNOSTICS_CMD_THRESHOLD);
    } else if(!verbose) {
        ZDP("cpu_util_percentage %"PRIu16" under threshold percentage %d\n", g_broker_sys_stats.s_cpu_util, DIAGNOSTICS_CMD_THRESHOLD);
    }
    if (g_broker_sys_stats.s_mem_util > DIAGNOSTICS_CMD_THRESHOLD) {
        ZDP("mem_util_percentage %"PRIu16" crossed threshold percentage %d\n", g_broker_sys_stats.s_mem_util, DIAGNOSTICS_CMD_THRESHOLD);
    } else if(!verbose) {
        ZDP("mem_util_percentage %"PRIu16" under threshold percentage %d\n", g_broker_sys_stats.s_mem_util, DIAGNOSTICS_CMD_THRESHOLD);
    }
    if(g_broker_sys_stats.s_sys_fd_util > DIAGNOSTICS_CMD_THRESHOLD) {
        ZDP("sys_fd_util_percentage %d crossed threshold percentage %d\n", g_broker_sys_stats.s_sys_fd_util, DIAGNOSTICS_CMD_THRESHOLD);
    } else if(!verbose) {
        ZDP("sys_fd_util_percentage %d under threshold percentage %d\n", g_broker_sys_stats.s_sys_fd_util, DIAGNOSTICS_CMD_THRESHOLD);
    }
    if(g_broker_sys_stats.s_proc_fd_util > DIAGNOSTICS_CMD_THRESHOLD) {
        ZDP("proc_fd_util_percentage %d crossed threshold percentage %d\n", g_broker_sys_stats.s_proc_fd_util, DIAGNOSTICS_CMD_THRESHOLD);
    } else if(!verbose) {
        ZDP("proc_fd_util_percentage %d under threshold percentage %d\n", g_broker_sys_stats.s_proc_fd_util, DIAGNOSTICS_CMD_THRESHOLD);
    }
    if(g_broker_sys_stats.s_udp4_port_util > DIAGNOSTICS_CMD_THRESHOLD) {
        ZDP("udp4_port_util_percentage %d crossed threshold percentage %d\n", g_broker_sys_stats.s_udp4_port_util, DIAGNOSTICS_CMD_THRESHOLD);
    } else if(!verbose) {
        ZDP("udp4_port_util_percentage %d under threshold percentage %d\n", g_broker_sys_stats.s_udp4_port_util, DIAGNOSTICS_CMD_THRESHOLD);
    }
    if(g_broker_sys_stats.s_udp6_port_util > DIAGNOSTICS_CMD_THRESHOLD) {
        ZDP("udp6_port_util_percentage %d crossed threshold percentage %d\n", g_broker_sys_stats.s_udp6_port_util, DIAGNOSTICS_CMD_THRESHOLD);
    } else if(!verbose) {
        ZDP("udp6_port_util_percentage %d under threshold percentage %d\n", g_broker_sys_stats.s_udp6_port_util, DIAGNOSTICS_CMD_THRESHOLD);
    }
    if(tcp4_port_util > DIAGNOSTICS_CMD_THRESHOLD) {
        ZDP("tcp4_port_util_percentage %d crossed threshold percentage %d\n", tcp4_port_util, DIAGNOSTICS_CMD_THRESHOLD);
    } else if(!verbose) {
        ZDP("tcp4_port_util_percentage %d under threshold percentage %d\n", tcp4_port_util, DIAGNOSTICS_CMD_THRESHOLD);
    }
    if(tcp6_port_util > DIAGNOSTICS_CMD_THRESHOLD) {
        ZDP("tcp6_port_util_percentage %d crossed threshold percentage %d\n", tcp6_port_util, DIAGNOSTICS_CMD_THRESHOLD);
    } else if(!verbose) {
        ZDP("tcp6_port_util_percentage %d under threshold percentage %d\n", tcp6_port_util, DIAGNOSTICS_CMD_THRESHOLD);
    }
    ZDP("\n");
    return ZPN_RESULT_NO_ERROR;
}

static void zpn_broker_maintainence_redirect_internal(int ops_force_redirect, const char* reason)
{
    struct zpn_broker_connected_clients *connected_clients = &(broker.clients);
    struct zpn_broker_client_fohh_state *client;

    ZPN_DEBUG_BALANCE("In maintainence mode, need to send redirect to all connected clients");

    pthread_mutex_lock(&(connected_clients->lock));

    LIST_FOREACH(client, &(connected_clients->client_list), list_entry) {
        zpn_broker_client_redirect_from_another_thread(client->tunnel_id, c_state_get_fohh_thread_id(client), ops_force_redirect, 1, reason);
    }

    pthread_mutex_unlock(&(connected_clients->lock));
}

/* This one triggers redirect based on balance_control table */
static void zpn_broker_maintainence_redirect()
{
    if (strncmp(g_balance_config.balance_mode, BALANCE_MODE_MAINTENANCE, strlen(BALANCE_MODE_MAINTENANCE))) {
        return;
    }
    zpn_broker_maintainence_redirect_internal(0, BRK_REDIRECT_REASON_BROKER_MAINTENANCE);
}

/* This one is triggered by graceful upgrade process */
void zpn_broker_redirect_clients(int ops_force_redirect, const char* reason)
{
    zpn_broker_maintainence_redirect_internal(ops_force_redirect, reason);
}

/*
 * zpn_broker_redirect_clients_slow_drain
 *  This func. enables redirecting clients gracefully over specified drain period.
 *  used in maintenance code; drain over a period of maintenance_config.drain_timeout_s
 */
void zpn_broker_redirect_clients_slow_drain(const char* reason)
{
    ZPN_LOG(AL_NOTICE, "In maintainence mode, slow redirect to all connected clients; reason: %s", reason);

    zpn_broker_process_drain_request_all_customers(drain_type_maintenance, drain_subtype_maintenance_upgrade,
                reason, 1, zpn_broker_get_maintenance_drain_timeout(), 100);

}

static void zbl_timer_callback(evutil_socket_t sock, short flags, void *cookie)
{
    if (tickle_me) zthread_heartbeat(tickle_me);

   zpn_broker_load_monitor();

    if ((!strncmp(g_balance_config.balance_role, BALANCE_ROLE_REDIRECT, strlen(BALANCE_ROLE_REDIRECT)) ||
         !strncmp(g_balance_config.balance_role, BALANCE_ROLE_FORWARD, strlen(BALANCE_ROLE_FORWARD)))) {
        zpn_broker_maintainence_redirect();
    }
}

static void zbl_adaptive_timer_callback(evutil_socket_t sock, short flags, void *cookie)
{
    if (tickle_me) zthread_heartbeat(tickle_me);

    zpn_broker_adaptive_load_monitor();
}

static int zbl_base_init(struct event_base *base)
{
    struct timeval tv;
    struct event *ev_timer;

    /* Make timer fo load reporting that runs on normal interval. */
    ev_timer = event_new(base,
                         -1,
                         EV_PERSIST,
                         zbl_timer_callback,
                         NULL);
    if (!ev_timer) {
        ZPN_LOG(AL_ERROR, "Could not create timer");
        return ZPN_RESULT_ERR;
    }

    tv.tv_sec = ZPN_LOAD_MONITOR_TIMER_SEC;
    tv.tv_usec = 0;
    if (event_add(ev_timer, &tv)) {
        ZPN_LOG(AL_ERROR, "Could not activate timer");
        return ZPN_RESULT_ERR;
    }

    memset(&broker_worker_stats, 0, sizeof(struct zpn_fohh_worker_stats));
    memset(&broker_client_stats, 0, sizeof(broker_client_stats));

    /* Make load run once very quickly... */


    return ZPN_RESULT_NO_ERROR;
}

static void zpn_broker_zpm_config_flag_monitor_cb(const int64_t *config_value, int64_t impacted_gid)
{
    ZPN_LOG(AL_ERROR,"broker-zpm: broker-zpm connection configuration status is %s",g_broker_zpm_conn_config_monitor_flag?"enabled":"disabled");
    if((!g_zpn_broker_zpm_conn_status) &&  g_broker_zpm_conn_config_monitor_flag) {
        ZPN_LOG(AL_ERROR,"broker-zpm: broker-zpm config flag enabled");
        zpn_broker_zpm_conn_init(broker_timer_base);
    } else {
        ZPN_LOG(AL_ERROR,"broker-zpm: broker-zpm config flag disabled");
        zpn_broker_zpm_close_conn();
    }
}


/* Check if the broker-zpm conn is enabled for this instance */
int zpn_broker_zpm_conn_config_status(void)
{
    int64_t value;
    int64_t root_customer_gid = ZPATH_GID_GET_CUSTOMER_GID(zpath_instance_global_state.current_config->gid);

    value = zpath_config_override_get_config_int(CONFIG_FEATURE_BROKER_ZPM_CONNECTION_CONFIG,
                                                &value,
                                                DEFAULT_CONFIG_FEATURE_BROKER_ZPM_CONNECTION_CONFIG,
                                                zpath_instance_global_state.current_config->gid,
                                                root_customer_gid,
                                                (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                (int64_t)0);

    zpath_config_override_monitor_int(CONFIG_FEATURE_BROKER_ZPM_CONNECTION_CONFIG,
                                        &g_broker_zpm_conn_config_monitor_flag,
                                        zpn_broker_zpm_config_flag_monitor_cb,
                                        DEFAULT_CONFIG_FEATURE_BROKER_ZPM_CONNECTION_CONFIG,
                                        zpath_instance_global_state.current_config->gid,
                                        root_customer_gid,
                                        (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                        (int64_t)0);

    g_broker_zpm_conn_config_monitor_flag = value;
    ZPN_LOG(AL_NOTICE, "broker-zpm: broker-zpm enablement for broker gid %"PRId64" is %"PRId64"",
                                                zpath_instance_global_state.current_config->gid,
                                                value);
    return value;
}

/* zbl_adaptive_obj_init - Initialize the adaptive load object values
*/
static void zbl_adaptive_obj_init() {
    zpn_broker_update_load_stats();
    zbal_data_obj.cpu_util=g_broker_load_stats.cpu_util;
    zbal_data_obj.mem_util=g_broker_load_stats.mem_util;
    zbal_data_obj.proc_fd_util=g_broker_load_stats.proc_fd_util;
    zbal_data_obj.last_report_time=epoch_s();
}

static int zbl_adaptive_base_start_timer()
{
    struct timeval tv;
    /* Make timer fo adaptive load reporting that runs on regular interval. */
    zbl_adaptive_timer = event_new(broker_timer_base,
                         -1,
                         EV_PERSIST,
                         zbl_adaptive_timer_callback,
                         NULL);
    if (!zbl_adaptive_timer) {
        ZPN_LOG(AL_ERROR, "Could not create adaptive load timer");
        return ZPN_RESULT_ERR;
    }

    tv.tv_sec = ZPN_ADAPTIVE_LOAD_MONITOR_TIMER_SEC;
    tv.tv_usec = 0;
    if (event_add(zbl_adaptive_timer, &tv)) {
        ZPN_LOG(AL_ERROR, "Could not activate adaptive load timer");
        event_free(zbl_adaptive_timer);
        zbl_adaptive_timer=NULL;
        return ZPN_RESULT_ERR;
    }
    zbl_adaptive_obj_init();
    return ZPN_RESULT_NO_ERROR;
}

static void zbl_adaptive_base_stop_timer()
{
    event_free(zbl_adaptive_timer);
    zbl_adaptive_timer=NULL;
}

/* zbl_adaptive_base_manage - performs operation on feature toggle
*  feature on: starts adaptive load timer
*  feature off: stops the adaptive load timer
*/
void zbl_adaptive_base_manage() {
    int res=ZPN_RESULT_NO_ERROR;
    if (zbal_config_obj.adaptive_load_feature_flag) {
        if (!zbl_adaptive_timer) {
            res = zbl_adaptive_base_start_timer();
            if (!res) {
                ZPN_LOG(AL_NOTICE, "Adaptive Load Monitor: Feature Enabled");
            }
        }
    } else {
        if (zbl_adaptive_timer) {
            zbl_adaptive_base_stop_timer();
            ZPN_LOG(AL_NOTICE, "Adaptive Load Monitor: Feature Disabled");
        }
    }

}

static int zpn_broker_app_thread_heartbeat_time_override_get_value()
{
    int64_t config_value = APP_THREAD_HEARTBEAT_OVERRIDE_DEFAULT;

    if (ZPN_BROKER_IS_PUBLIC()) {
        config_value = zpath_config_override_get_config_int(CONFIG_FEATURE_APP_THREAD_HEARTBEAT_OVERRIDE_BROKER,
                                                            &config_value,
                                                            APP_THREAD_HEARTBEAT_OVERRIDE_DEFAULT,
                                                            zpath_instance_global_state.current_config->gid,
                                                            (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                            (int64_t)0);
    } else {
        int64_t customer_gid = ZPATH_GID_GET_CUSTOMER_GID(g_broker_common_cfg->private_broker.broker_id);
        config_value = zpath_config_override_get_config_int(CONFIG_FEATURE_APP_THREAD_HEARTBEAT_OVERRIDE_PSE,
                                                            &config_value,
                                                            APP_THREAD_HEARTBEAT_OVERRIDE_DEFAULT,
                                                            (int64_t)g_broker_common_cfg->private_broker.broker_id,
                                                            customer_gid,
                                                            (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                            (int64_t)0);
    }

    ZPN_DEBUG_BROKER("Broker App Thread Heartbeat Time Override config value is %"PRId64, config_value);
    return config_value;
}

void zpn_broker_app_thread_heartbeat_time_monitor_callback(const int64_t *config_value, int64_t impacted_gid)
{
    int app_threads_count = zpn_broker_app_calc_threads_count_get();
    ZPN_LOG(AL_NOTICE, "app_thread: app_threads_count: %d, app_thread_heartbeat_time value: %"PRId64, app_threads_count, g_app_thread_hb_override_time);

    // Resert hb for all app threads
    for (int i = 0; i < app_threads_count; i++) {
        ZPN_LOG(AL_NOTICE, "app_thread: Reset app_thread_heartbeat_time for app thread_%d to value: %"PRId64, i, g_app_thread_hb_override_time);
        zthread_set_heartbeat_override_for_thread(app_thread_num[i], g_app_thread_hb_override_time);
    }
}

static void zpn_broker_app_thread_heartbeat_time_override_init() {

    if (ZPN_BROKER_IS_PUBLIC()) {
        zpath_config_override_monitor_int(CONFIG_FEATURE_APP_THREAD_HEARTBEAT_OVERRIDE_BROKER,
                                          &g_app_thread_hb_override_time,
                                          zpn_broker_app_thread_heartbeat_time_monitor_callback,
                                          APP_THREAD_HEARTBEAT_OVERRIDE_DEFAULT,
                                          zpath_instance_global_state.current_config->gid,
                                          (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                          (int64_t)0);
    } else {
        int64_t customer_gid = ZPATH_GID_GET_CUSTOMER_GID(g_broker_common_cfg->private_broker.broker_id);
        zpath_config_override_monitor_int(CONFIG_FEATURE_APP_THREAD_HEARTBEAT_OVERRIDE_PSE,
                                          &g_app_thread_hb_override_time,
                                          zpn_broker_app_thread_heartbeat_time_monitor_callback,
                                          APP_THREAD_HEARTBEAT_OVERRIDE_DEFAULT,
                                          g_broker_common_cfg->private_broker.broker_id,
                                          customer_gid,
                                          (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                          (int64_t)0);
    }
}

static void zpn_broker_adaptive_load_init_overrides() {
    /* in case some test code didn't initialize zpath_instance -
     * default to ZPATH_GLOBAL_CONFIG_OVERRIDE_GID */
    int64_t inst_gid = ZPATH_INSTANCE_GID ? ZPATH_INSTANCE_GID : ZPATH_GLOBAL_CONFIG_OVERRIDE_GID;

    zpath_config_override_monitor_int(CONFIG_FEATURE_BROKER_ADAPTIVE_LOAD_MONITOR,
                                    &zbal_config_obj.adaptive_load_feature_flag,
                                    zbl_adaptive_base_manage,
                                    DEFAULT_CONFIG_FEATURE_BROKER_ADAPTIVE_LOAD_MONITOR,
                                    inst_gid,
                                    (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                    (int64_t)0);

    zpath_config_override_monitor_int(CONFIG_FEATURE_BROKER_ADAPTIVE_LOAD_UPDATE_INTERVAL,
                                    &zbal_config_obj.report_interval,
                                    NULL,
                                    DEFAULT_CONFIG_FEATURE_BROKER_ADAPTIVE_LOAD_UPDATE_INTERVAL,
                                    inst_gid,
                                    (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                    (int64_t)0);

    zpath_config_override_monitor_int(CONFIG_FEATURE_BROKER_ADAPTIVE_LOAD_CPU_SPIKE_THRESHOLD,
                                    &zbal_config_obj.cpu_spike_threshold,
                                    NULL,
                                    DEFAULT_CONFIG_FEATURE_BROKER_ADAPTIVE_LOAD_CPU_SPIKE_THRESHOLD,
                                    inst_gid,
                                    (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                    (int64_t)0);

    zpath_config_override_monitor_int(CONFIG_FEATURE_BROKER_ADAPTIVE_LOAD_MEMORY_SPIKE_THRESHOLD,
                                    &zbal_config_obj.mem_spike_threshold,
                                    NULL,
                                    DEFAULT_CONFIG_FEATURE_BROKER_ADAPTIVE_LOAD_MEMORY_SPIKE_THRESHOLD,
                                    inst_gid,
                                    (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                    (int64_t)0);

    zpath_config_override_monitor_int(CONFIG_FEATURE_BROKER_ADAPTIVE_LOAD_PROC_FD_SPIKE_THRESHOLD,
                                    &zbal_config_obj.proc_fd_spike_threshold,
                                    NULL,
                                    DEFAULT_CONFIG_FEATURE_BROKER_ADAPTIVE_LOAD_PROC_FD_SPIKE_THRESHOLD,
                                    inst_gid,
                                    (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                    (int64_t)0);
}

void *zbh_thread(struct zthread_info *zthread_arg, void *cookie)
{
    struct event_base *base = NULL;
    int res;
    struct zpath_interlock *lock = (struct zpath_interlock *)cookie;

    tickle_me = zthread_arg;

    base = event_base_new();
    if (!base) {
        ZPN_LOG(AL_ERROR, "Could not create event_base: zbh_health");
        goto fail;
    }

    res = zbh_base_init(base);
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not initialize event_base, zbh_health thread");
        goto fail;
    }

    broker_timer_base = zbh_base;

    res = zbl_base_init(base);
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not initialize event_base, zbl_monitor thread");
        goto fail;
    }

    if(ZPN_BROKER_IS_PUBLIC()) {

        zpn_broker_adaptive_load_init_overrides();

        /* initialize commands for broker-zpm interaction */
        res = zpn_broker_zpm_cmd_init();
        if (res) {
            goto fail;
        }

        /* broker-zpm connection */
        if(g_zpn_broker_zpm_boot_arg_flag && zpn_broker_zpm_conn_config_status()) {
            /* initialise the broker to zpm connection */
            res = zpn_broker_zpm_conn_init(base);
            if(res){
                ZPN_LOG(AL_ERROR, "broker-zpm: Error in zpn_broker_zpm_conn_init res = %s",zpn_result_string(res));
                goto fail;
            }
        } else {
            ZPN_LOG(AL_ERROR, "broker-zpm: broker-zpm disabled through boot arg or config");
        }
    }

    res = zpn_broker_self_cert_monitor_init(base, 1);
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not initialize broker_cert_monitor");
        goto fail;
    }

    res = zpn_broker_dns_init(base);
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not initialize broker_dns");
        goto fail;
    }

    res = zpn_broker_dispatch_c2c_client_check_init(base);
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not initialize zpn_broker_dispatch_client_check");
        goto fail;
    }

    res = zpn_rate_limit_init(base);
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not initialize zpn_rate_limit");
        goto fail;
    }

    res = zpn_pcap_group_init(base);
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not initialize zpn_pcap_group_init");
        goto fail;
    }

    /* Every 10 ms, max timer a bit less than 2s */
    timer_wheel = ztimer_wheel_create(base, &zpn_allocator, 200, 10*1000);
    if (!timer_wheel) {
        ZPN_LOG(AL_ERROR, "Could not create timer wheel");
        goto fail;
    }

    zpath_interlock_signal(lock);
    zevent_base_dispatch(base);

    ZPN_LOG(AL_ERROR, "Not reachable, zpn broker health monitor thread");
 fail:
    /* Should watchdog... */
    while(1) {
        sleep(1);
    }
    return NULL;
}

static void zpn_broker_log_stats_once_per_second(evutil_socket_t sock, int16_t flags, void *cookie)
{
    struct zthread_info *zthread_arg = cookie;

    zthread_heartbeat(zthread_arg);

    /* Not much to do yet */
    return;
}

static void *zpn_broker_log_stats_thread(struct zthread_info *zthread_arg, void *cookie)
{
    struct timeval tv;
    struct event *ev;

    ev = event_new(zpn_broker_log_stats_event_base, -1, EV_PERSIST, zpn_broker_log_stats_once_per_second, zthread_arg);
    if (!ev) {
        ZPN_LOG(AL_CRITICAL, "Could not make event");
        goto FAIL;
    }

    /* every so often... */
    tv.tv_usec = 0;
    tv.tv_sec = 1;
    if (event_add(ev, &tv)) {
        ZPN_LOG(AL_CRITICAL, "Could not event_add");
        goto FAIL;
    }

    zevent_base_dispatch(zpn_broker_log_stats_event_base);

    ZPN_LOG(AL_CRITICAL, "Not reachable");
 FAIL:
    /* Should watchdog... */
    while(1) {
        sleep(1);
    }
    return NULL;
}

int zpn_broker_health_init(struct event_base *base)
{
    if (base) {
        return zbh_base_init(base);
    } else {
        pthread_t thread;
        int res;
        struct zpath_interlock lock;

        zpath_interlock_init(&lock);
        res = zthread_create(&thread,
                             zbh_thread,
                             &lock,
                             "zpn_broker_monitor",
                             60,
                             16*1024*1024,
                             60*1000*1000,
                             NULL);
        if (res) return ZPN_RESULT_ERR;

        zpath_interlock_wait(&lock);
        return ZPN_RESULT_NO_ERROR;
    }
}

/*
 * zpn_broker_termination_thread
 *  We remain in a while loop; each iteration check if broker has gone to TERMINATING
 *  If so, exit in the next iteration.
 */
static void *zpn_broker_termination_thread(struct zthread_info *zthread_arg, void *cookie)
{
    /* set the thread as non-killable on termination */
    zthread_set_dont_kill_on_terminate_flag(zthread_arg->stack.thread_num);

    if(zpath_is_dev_environment() &&
        (g_zthread_termination_stuck_zpm_bails_out ||
         g_zthread_termination_deadlock_zpm_bails_out)) {
        fprintf(stdout, "broker-zpm: zpn_broker_termination_thread stuck scenario");
        while(1);
    }

    while (1) {
        zthread_heartbeat(zthread_arg);
        sleep(ZPN_TERMINATION_THREAD_SLEEP_SEC);

        if (g_broker_termination_ready) {
            /* Final point before we exit; unconditionally do the exit call. */
            zpn_broker_final_termination();
        } else {
            /* we have been notified; lets get ready to terminate in next iteration */
            if (g_broker_termination_notify) {
                g_broker_termination_ready = 1;
            }
        }
    }
    return NULL;
}

/*
 * zpn_broker_termination_handler_thread_init
 *  creates a thread exclusively for events during broker termination
 */
int zpn_broker_termination_handler_thread_init(void)
{
    int result;
    pthread_t thread;

    result = zthread_create(&thread,
                            zpn_broker_termination_thread,
                            NULL,
                            "zpn_broker_termination_thread",
                            INT_MAX,    /* keep hb timeout max to avoid this thread being monitored */
                            16*1024*1024,
                            60*1000*1000,
                            NULL);
    if (result) {
        ZPN_LOG(AL_ERROR, "BrokerReadiness: Could not create zpn_broker_termination_thread");
        return result;
    }

    return ZPN_RESULT_NO_ERROR;
}

/****************************************************
 * Identify unique customers list
 ***************************************************/

static int zpn_broker_find_customers_walk_fn(void *cookie, void *object, void *key, size_t key_len)
{
    if (!key || !cookie) {
        ZPN_LOG(AL_ERROR, "key/cookie is NULL in customer_list get hash, return");
        return 0;
    }
    int64_t customer_gid = *(int64_t *)key;
    struct zpn_broker_customer_counter_hash *customer_list = cookie;

    ZPN_BROKER_WR_LOCK(customer_list->lock);

    struct zpn_broker_customer_counter_entry *counter = NULL;
    counter = zhash_table_lookup(customer_list->table, &customer_gid, sizeof(customer_gid), NULL);
    if (!counter) {

        counter = ZPN_CALLOC(sizeof(struct zpn_broker_customer_counter_entry));
        if (!counter) {
            ZPN_LOG(AL_CRITICAL, "malloc failed in customer list hash entry creation");
            ZPN_BROKER_UNLOCK(customer_list->lock);
            return 0;
        }
        counter->customer_gid = customer_gid;
        counter->count = 0;
        zhash_table_store(customer_list->table, &customer_gid, sizeof(customer_gid), 0, counter);
    }

    counter->count++;
    ZPN_BROKER_UNLOCK(customer_list->lock);

    return 0;
}

static void zpn_broker_find_customers_free_fn(void *element, void *cookie)
{
    ZPN_FREE(element);
}

static int zpn_broker_display_customer_walk_fn(void *cookie, void *object, void *key, size_t key_len)
{
    if (!key || !object || !cookie) {
        ZPN_LOG(AL_ERROR, "key/object/cookie is NULL in display customer walk_fn, return");
        return 0;
    }

    struct zpath_debug_state *request_state = cookie;
    int64_t customer_gid = *(int64_t *)key;
    struct zpn_broker_customer_counter_entry *counter = object;

    struct zpath_customer *customer = NULL;
    int res = zpath_customer_get_immediate(customer_gid, &customer);
    if (res) {
         ZPN_LOG(AL_ERROR, "Failed to get customer name for customer_gid:%"PRId64" error %s\n",
                customer_gid, zpath_result_string(res));
    }

    if (request_state) {
        ZDP("%-20"PRId64"(%s) \n", customer_gid, customer ? customer->name : "");
    } else {
        ZPN_LOG(AL_NOTICE, "%-20"PRId64"(%s) exists on %"PRIu64" thread(s)",
                customer_gid, customer ? customer->name : "", counter ? counter->count : 0);
    }
    return ZPN_RESULT_NO_ERROR;
}

struct zpn_broker_customer_counter_hash* zpn_broker_customer_list_alloc(void)
{
    /* allocate hashtable and free up after use */
    struct zpn_broker_customer_counter_hash *customer_list = NULL;

    customer_list = ZPN_CALLOC(sizeof(struct zpn_broker_customer_counter_hash));
    if (!customer_list) {
        ZPN_LOG(AL_CRITICAL, "customer_list hash malloc failed");
        return NULL;
    }
    customer_list->lock = ZPATH_RWLOCK_INIT;
    customer_list->table = zhash_table_alloc(&zhash_table_allocator);

    return customer_list;
}

void zpn_broker_customer_list_populate(struct zpn_broker_customer_counter_hash* customer_list)
{
    struct zpn_broker_cgid_to_cstate_per_thread *cgid_cstate = &(broker.cgid_to_cstate_per_thread);
    int max_fohh_thread = fohh_thread_count();
    struct zpn_broker_connected_clients *connected_clients = &(broker.clients);

    pthread_mutex_lock(&(connected_clients->lock));

    /* Just iterate and get the data, not scheduling it */
    for (int curr_fohh_thread = 0; curr_fohh_thread < max_fohh_thread; curr_fohh_thread++) {

        int64_t key = 0;
        zhash_table_walk(cgid_cstate->customer_gid[curr_fohh_thread], &key,
                         zpn_broker_find_customers_walk_fn, customer_list);
    }
    pthread_mutex_unlock(&(connected_clients->lock));
}

void zpn_broker_customer_list_display(struct zpn_broker_customer_counter_hash* customer_list, void *cookie)
{
    if (!customer_list)
        return;

    int64_t key = 0;
    ZPN_BROKER_RD_LOCK(customer_list->lock);
    zhash_table_walk(customer_list->table, &key,
                    zpn_broker_display_customer_walk_fn, cookie);
    ZPN_BROKER_UNLOCK(customer_list->lock);
}

void zpn_broker_customer_list_free(struct zpn_broker_customer_counter_hash* customer_list)
{
    if (!customer_list)
        return;

    ZPN_BROKER_WR_LOCK(customer_list->lock);
    zhash_table_free_and_call(customer_list->table, zpn_broker_find_customers_free_fn, NULL);
    ZPN_BROKER_UNLOCK(customer_list->lock);

    ZPN_FREE(customer_list);
}


/*
 * Dump all the active cstates for a customer gid
 *
 * Usage curl "127.0.0.1:8000/zpn/broker/client/customer_cstates?customerGID=<customer-GID/domain-name>"
 *
 */
static int zpn_broker_customer_cstates_dump(struct zpath_debug_state* request_state,
                                            const char **query_values,
                                            int query_value_count,
                                            void *cookie)
{
    int64_t gid;
    int curr_fohh_thread;
    int max_fohh_thread;
    struct zpn_broker_cgid_to_cstate_per_thread *cgid_cstate = &(broker.cgid_to_cstate_per_thread);
    struct zpn_broker_connected_customer_clients* gid_client_list;
    struct zpn_broker_client_fohh_state *c_state;
    struct zpn_broker_connected_clients *connected_clients = &(broker.clients);

    (void) query_value_count;
    (void) cookie;

    if (query_values[0]) {
        /* This is the case when code has a bug for specific client
         * not setting the customer gid so show those client connections
         */
        if (strcmp(query_values[0], "0") == 0) {
            gid = 0;
            ZDP("NOTICE: Customer gid can not be 0 if a client exists with "
                 "0 gid, means it customer gid is not initialized in connection. "
                 "Do raise a bug for fixing that client component\n");
            goto SHOW;
        }

        char *end_ptr;
        gid = strtoul(query_values[0], &end_ptr, 0);
        if (*end_ptr != '\0') { /* couldn't completely convert to number */
            gid = zpath_domain_lookup_get_id(query_values[0], strlen(query_values[0]));
        }
    } else {
        ZDP("Customer GID or domain name is required!\n");
        ZDP("Usage : curl 127.0.0.1:8000/zpn/broker/client/customer_cstates?customer=<customer-GID/domain-name>\n");
        goto DONE;
    }

    if (!gid) {
       ZDP("Bad customer ID or customer domain not provided\n");
       goto DONE;
    }

    ZDP("Customer_gid for given gid/domain str: %s is  %"PRId64"\n", query_values[0], gid);

SHOW:
    /* Lock connected clients and c_state */
    pthread_mutex_lock(&(connected_clients->lock));

    max_fohh_thread = fohh_thread_count();
    for (curr_fohh_thread = 0; curr_fohh_thread < max_fohh_thread; curr_fohh_thread++) {
        ZDP("\nThread view - %d\n", curr_fohh_thread);
        gid_client_list = zhash_table_lookup(cgid_cstate->customer_gid[curr_fohh_thread], &gid, sizeof(gid), NULL);
        if (gid_client_list == NULL) {
            ZDP("Customer %s cstate does not exists at thread %d\n", query_values[0], curr_fohh_thread);
            continue;
        }

        LIST_FOREACH(c_state, &(gid_client_list->customer_client_list), customer_gid_entry) {
            ZDP("User %s :Client type %s connected with tunnel id %s\n",
                        c_state->user_id,
                        zpn_client_type_string(c_state->client_type),
                        c_state->tunnel_id);

            int64_t curr_time = epoch_s();
            for (enum zpn_conn_drain_type type = drain_type_none + 1; type < drain_type_total_count; type++){
                if (!c_state->drain_info[type].drain_enabled)
                    continue;

                ZDP("\tdrain type: %s, subtype:%s",
                        zpn_conn_get_drain_type_string(type),
                        zpn_conn_get_drain_subtype_string(c_state->drain_info[type].drain_subtype));
                if (c_state->drain_info[type].expiry_s) {
                    if (c_state->drain_info[type].expiry_s >= curr_time) {
                        ZDP(", expires in %"PRId64" sec. ", (c_state->drain_info[type].expiry_s - curr_time));
                    } else {
                        ZDP(", expired %"PRId64"  sec. ago.", (curr_time - c_state->drain_info[type].expiry_s));
                    }
                }
                ZDP("\n");
            }
        }
    }

    pthread_mutex_unlock(&(connected_clients->lock));

DONE:
    return ZPN_RESULT_NO_ERROR;
}

/*
 * Display client-type statistics for all the active cstates for a customer gid
 *
 * Usage curl "127.0.0.1:8000/zpn/broker/client/customer_cstates/stats?customerGID=<customer-GID/domain-name>"
 *
 */
static int zpn_broker_customer_cstates_stats_dump(struct zpath_debug_state* request_state,
                                                  const char **query_values,
                                                  int query_value_count,
                                                  void *cookie)
{
    int64_t customer_gid = 0;

    if (query_values[0]) {
        char *end_ptr;
        customer_gid = strtoul(query_values[0], &end_ptr, 0);
        if (*end_ptr != '\0') { /* couldn't completely convert to number */
            customer_gid = zpath_domain_lookup_get_id(query_values[0], strlen(query_values[0]));
        }
    } else {
        ZDP("Customer GID or domain name is required!\n");
        ZDP("Usage : curl 127.0.0.1:8000/zpn/broker/client/customer_cstates/stats?customer=<customer-GID/domain-name\n");
        return ZPN_RESULT_NO_ERROR;
    }
    if (!customer_gid) {
        ZDP("Bad customer ID or customer domain not provided\n");
        return ZPN_RESULT_NO_ERROR;
    }

    ZDP("Customer_gid for given gid/domain str: %s is  %"PRId64"\n", query_values[0], customer_gid);
    struct zpn_broker_connected_customer_clients* gid_client_list;
    struct zpn_broker_cgid_to_cstate_per_thread *cgid_cstate = &(broker.cgid_to_cstate_per_thread);
    struct zpn_broker_connected_clients *connected_clients = &(broker.clients);

    /* Lock connected clients and c_state */
    pthread_mutex_lock(&(connected_clients->lock));

    int max_fohh_thread = fohh_thread_count();
    int64_t total_stats[zpn_client_type_total_count] = {0};
    int64_t total_passive_stats[zpn_client_type_total_count] = {0};

    for (int curr_fohh_thread = 0; curr_fohh_thread < max_fohh_thread; curr_fohh_thread++) {

        gid_client_list = zhash_table_lookup(cgid_cstate->customer_gid[curr_fohh_thread],
                                        &customer_gid,
                                        sizeof(customer_gid), NULL);
        if (gid_client_list == NULL) {
            continue;
        }

        /* accumulate across all threads */
        for (int i = 1; i < zpn_client_type_total_count; i++) {
            total_stats[i] += __sync_fetch_and_add_8(&gid_client_list->all_client_type_stats[i], 0);
            total_passive_stats[i] += __sync_fetch_and_add_8(&gid_client_list->all_client_type_passive_stats[i], 0);
        }
    }
    pthread_mutex_unlock(&(connected_clients->lock));

    for (int i = 1; i < zpn_client_type_total_count; i++) {
        ZDP("%36s: Total = %8"PRId64"    Active = %8"PRId64"    Passive = %8"PRId64" \n",
                    zpn_client_type_string(i), total_stats[i],
                    total_stats[i] - total_passive_stats[i], total_passive_stats[i]);
    }

    return ZPN_RESULT_NO_ERROR;
}

/*
 * Display list of customers having connections on this instance
 *  Usage curl "127.0.0.1:8000/zpn/broker/client/customer_list
 */
static int zpn_broker_customer_list_command(struct zpath_debug_state* request_state,
                                            const char **query_values,
                                            int query_value_count,
                                            void *cookie)
{
    struct zpn_broker_customer_counter_hash *cust_list_hash = NULL;

    cust_list_hash = zpn_broker_customer_list_alloc();
    if (!cust_list_hash) {
        return ZPN_RESULT_NO_MEMORY;
    }

    zpn_broker_customer_list_populate(cust_list_hash);
    zpn_broker_customer_list_display(cust_list_hash, request_state);
    zpn_broker_customer_list_free(cust_list_hash);

    return ZPN_RESULT_NO_ERROR;
}

/*
 * 1. Do a curl 127.0.0.1:9000/assistant/debug_dump in connector to get the input required for this command.
 * 2. Now in broker do something like this,
 * curl '127.0.0.1:8000/zpn/broker/learn?g_ast=217246660303022926&g_app=217246660303022315&g_app_grp=217246660302996096&g_srv_grp=217246660302995953&g_ast_grp=217246660303020198&s_port=80&domain=money.cnn.com&app_type=ip&disc_type=NEW&disc_msg_time=123&lifetime_s=120'
 */
static int zpn_broker_send_assistant_learn_frame(struct zpath_debug_state *request_state,
                                            const char **query_values,
                                            int query_value_count,
                                            void *cookie)
{
    struct zpn_app_route_discovery disc;
    char* domain = NULL;
    char* disc_type = NULL;
    char* app_type = NULL;

    if (!query_values[0] ||
        !query_values[1] ||
        !query_values[2] ||
        !query_values[3] ||
        !query_values[4] ||
        !query_values[5] ||
        !query_values[6] ||
        !query_values[7] ||
        !query_values[8] ||
        !query_values[9] ||
        !query_values[10])
    {
        ZDP("Missing argument: one or more of: g_ast, g_app, g_app_grp, g_ast_grp, g_srv_grp, s_port, domain, " "app_type, disc_type, disc_msg_time, lifetime_s\n");
    } else {
        disc.g_ast = strtol(query_values[0], NULL, 10);
        disc.g_app = strtol(query_values[1], NULL, 10);
        disc.g_app_grp = strtol(query_values[2], NULL, 10);
        disc.g_ast_grp = strtol(query_values[3], NULL, 10);
        disc.g_srv_grp = strtol(query_values[4], NULL, 10);
        disc.s_port = strtol(query_values[5], NULL, 10);
        domain = ZPN_MALLOC(strlen(query_values[6] + 1));
        strcpy(domain, query_values[6]);
        disc.domain = domain;
        app_type = ZPN_MALLOC(strlen(query_values[7] + 1));
        strcpy(app_type, query_values[7]);
        disc.app_type = app_type;
        if (query_values[8]) {
            disc_type = ZPN_MALLOC(strlen(query_values[8] + 1));
            strcpy(disc_type, query_values[8]);
        }
        disc.disc_type = disc_type;
        if (query_values[9]) {
            disc.disc_msg_time = strtol(query_values[9], NULL, 10);
        } else {
            disc.disc_msg_time = 0;
        }
        if (query_values[10]) {
            disc.lifetime = strtol(query_values[10], NULL, 10);
        } else {
            disc.lifetime = 0;
        }

        /* don't care fields */
        disc.g_brk = 1111;
        disc.g_cst = 1111;
        disc.g_dsp = 1111;
        /* assuming TCP as the protocol */
        disc.ip_protocol = 6;

        ZDP("Going to send a learn frame to assistant '%s'",query_values[0]);
        //
        // Setting thread id to 0. Only used for stats.
        //
        zpn_broker_dispatch_send_and_log_learn_frame(0, &disc);
    }
    if (domain) {
        ZPN_FREE(domain);
    }
    if (app_type) {
        ZPN_FREE(app_type);
    }
    if (disc_type) {
        ZPN_FREE(disc_type);
    }
    return ZPATH_RESULT_NO_ERROR;
}

static int zpn_dump_siem(struct zpath_debug_state *request_state,
                         const char **query_values,
                         int query_value_count,
                         void *cookie)
{
    int64_t gid = 0;
    int option = 0;
    size_t buf_size = (2*1024*1024);
    char *buf = ZPN_CALLOC(buf_size);
    if (buf) {
        if (query_values[0]) {
            gid = strtoul(query_values[0], NULL, 0);
        } else if (query_values[1]) {
            gid = strtoul(query_values[1], NULL, 0);
        } else if (query_values[2]) {
            option = 1;
        } else if (query_values[3]) {
            option = 2;
        } else if (query_values[4]) {
            option = 3;
        }

        zpn_siem_debug(gid, buf, buf_size, option);

        ZDP("%s", buf);
        ZPN_FREE(buf);
    }
    return ZPN_RESULT_NO_ERROR;
}

static int zpn_watch_siem(struct zpath_debug_state *request_state,
                          const char **query_values,
                          int query_value_count,
                          void *cookie)
{
    int64_t gid = 0;
    int setting = 0;

    if (query_value_count != 2 || !query_values[0] || !query_values[1]) {
        ZDP("Must provide GID of siem to watch and its watch value");
        return ZPN_RESULT_NO_ERROR;
    }

    gid = strtoul(query_values[0], NULL, 0);
    setting = strtol(query_values[1], NULL, 0);

    if (!gid) {
        ZDP("Must Provide a valid GID");
    }

    int res;
    res = zpn_siem_debug_watch(gid, setting);
    if (!res) {
        ZDP("%ld watch succesfully set to %i\n", (long)gid, setting);
    } else {
        ZDP("Unable to set watch %ld to %i: %s\n", (long)gid, setting, zpn_result_string(res));
    }
    return ZPN_RESULT_NO_ERROR;
}


static int is_ip_filtered(struct argo_inet *inet)
{
    int res;
    int res_count = 1;

    if (inet->length == 4) {
        res = zradix_search(dont_advertise_ipv4, &(inet->address[0]), inet->netmask, NULL, NULL, NULL, &res_count);
    } else {
        res = zradix_search(dont_advertise_ipv6, &(inet->address[0]), inet->netmask, NULL, NULL, NULL, &res_count);
    }
    if (res == ZRADIX_RESULT_NO_ERROR) {
        /* FOUND hit in tree, thus it is filtered */
        return 1;
    } else {
        return 0;
    }
}

static int add_ip_filter(const char *str)
{
    struct argo_inet inet;
    int res;
    static int arbitrary = 1;

    if ((res = argo_string_to_inet(str, &inet))) {
        return res;
    }
    if (inet.length == 4) {
        res = zradix_add(dont_advertise_ipv4, &(inet.address[0]), inet.netmask, &arbitrary);
    } else {
        res = zradix_add(dont_advertise_ipv6, &(inet.address[0]), inet.netmask, &arbitrary);
    }
    if (res) {
        ZPN_LOG(AL_ERROR, "Error adding %s: %s", str, zradix_result_string(res));
        return ZPN_RESULT_ERR;
    }
    return ZPATH_RESULT_NO_ERROR;
}

static int setup_ip_advertise_filters(void)
{
    dont_advertise_ipv4 = zradix_create();
    dont_advertise_ipv6 = zradix_create();

    /* These come from
     * https://en.wikipedia.org/wiki/Reserved_IP_addresses, with some
     * interpretation. The set that we don't want to advertise,
     * basically */
    if (add_ip_filter("0.0.0.0/32")) return ZPATH_RESULT_ERR;     // Unspecified (IN_ADDR_ANY)
    if (add_ip_filter("0.0.0.0/8")) return ZPATH_RESULT_ERR;      // Current network
    if (add_ip_filter("**********/10")) return ZPATH_RESULT_ERR;  // Carrier grade NAT
    if (add_ip_filter("*********/8")) return ZPATH_RESULT_ERR;    // Loopback
    if (add_ip_filter("***********/16")) return ZPATH_RESULT_ERR; // link-local
    if (add_ip_filter("*********/24")) return ZPATH_RESULT_ERR;   // IETF protocol assignments?
    if (add_ip_filter("*********/24")) return ZPATH_RESULT_ERR;   // Documentation
    if (add_ip_filter("***********/24")) return ZPATH_RESULT_ERR; // Reserved
    if (add_ip_filter("**********/15")) return ZPATH_RESULT_ERR;  // Reserved for benchmark testing.
    if (add_ip_filter("************/24")) return ZPATH_RESULT_ERR;// Documentation
    if (add_ip_filter("***********/24")) return ZPATH_RESULT_ERR; // Documentation
    if (add_ip_filter("*********/4")) return ZPATH_RESULT_ERR;    // IP multiast
    //if (add_ip_filter("240.0.0.0/4")) return ZPATH_RESULT_ERR;  // Reserved for future use. Going to allow this for now.
    if (add_ip_filter("***************")) return ZPATH_RESULT_ERR;// limited broadcast
    if (add_ip_filter("::/128")) return ZPATH_RESULT_ERR;         // Unspecified (IN_ADDR_ANY)
    if (add_ip_filter("::1/128")) return ZPATH_RESULT_ERR;        // Loopback
    if (add_ip_filter("fe80::/10")) return ZPATH_RESULT_ERR;      // Link local
    if (add_ip_filter("ff00::/8")) return ZPATH_RESULT_ERR;       // Multicast
    return ZPATH_RESULT_NO_ERROR;
}

int do_log_file(struct argo_log_collection *collection,
                       const char *name,
                       const char *path,
                       const char *filename)
{
    char full_file[1000];

    snprintf(full_file, sizeof(full_file), "%s/%s", path, filename);

    struct argo_log_file *log_file;
    log_file = argo_log_file_create(collection,
                                    full_file,
                                    NULL,
                                    1024 * 1024 * 1024,
                                    argo_serialize_binary);
    if (!log_file) {
        return ZPN_RESULT_ERR;
    }

    if (!argo_log_read(collection, name, 0, 1, argo_log_file_callback, NULL, log_file, NULL, 0)) {
        return ZPN_RESULT_ERR;
    }
    return ZPN_RESULT_NO_ERROR;
}

static int validate_listen_ip(struct argo_inet *ip)
{
    int res = ZPN_RESULT_ERR;
    char ip_str[ARGO_INET_ADDRSTRLEN];

    if (!ip) {
        ZPN_LOG(AL_CRITICAL, "Null ip");
        return res;
    }

    argo_inet_generate(ip_str, ip);

    if ((ip->length == 4) && ((ip->netmask == 32) || (strcmp(ip_str, "0.0.0.0") == 0))) {
        /* Valid IPv4 address or INADDR_ANY */
        res = ZPN_RESULT_NO_ERROR;
    } else if ((ip->length == 16) && ((ip->netmask == 128) || (strcmp(ip_str, "::") == 0))) {
        /* Valid IPv6 address or INADDR_ANY */
        res = ZPN_RESULT_NO_ERROR;
    } else {
        ZPN_LOG(AL_ERROR, "Invalid listen address: %s", ip_str);
    }

    return res;
}

static int zpn_private_broker_row_callback(void *cookie,
                                           struct wally_registrant *registrant,
                                           struct wally_table *table,
                                           struct argo_object *previous_row,
                                           struct argo_object *row,
                                           int64_t request_id)
{

#ifdef DTA_CHECKPOINT
    struct zpn_private_broker *pb = row->base_structure_void;
    if(pb->scope_gid && pb->scope_gid != pb->customer_gid) return WALLY_RESULT_NO_ERROR;
#endif
    if (zpn_debug_get(ZPN_DEBUG_PRIVATE_BROKER_IDX)) {
        char dump[8000];
        if (argo_object_dump(row, dump, sizeof(dump), NULL, 0) == ARGO_RESULT_NO_ERROR) {
            ZPN_DEBUG_PBROKER("Row callback: %s", dump);
        }
    }

    struct zpn_private_broker *pbrk = row->base_structure_void;
    if (ZPN_BROKER_IS_PRIVATE() && pbrk->gid == g_broker_common_cfg->private_broker.broker_id) {
        if (pbrk->deleted)  {
            ZPN_LOG(AL_ERROR, "Service Edge has been deleted, shutting down...");
            sleep(1);
            exit(0);
        }

        g_broker_common_cfg->private_broker.enabled = pbrk->enabled;
    }

    return ZPN_RESULT_NO_ERROR;
}

static void zpn_pbroker_group_verify_alt_cloud_name_change(char *pb_alt_cloud)
{
    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();
    int fresult = 0;

    if(!pb_alt_cloud && strnlen(gs->listener_alt_cloud_name, PRIVATE_BROKER_CLOUD_NAME_LEN)) {
        ZPN_LOG(AL_INFO, "Private broker %"PRId64", group %"PRId64" alt cloud changed %s -> %s ",
                          ZPN_BROKER_GET_GID(),
                          ZPN_BROKER_GET_GROUP_GID(),
                          gs->listener_alt_cloud_name,
                          gs->cfg_key_cloud);
        memset(gs->listener_alt_cloud_name, 0, PRIVATE_BROKER_CLOUD_NAME_LEN);
        fresult = pbroker_store_alt_cloud_to_file(NULL, gs->redir_alt_cloud_name);
        if (fresult) {
            ZPN_LOG(AL_ERROR, "Failed to write alt_cloud to local cache file. Error: %s", zpn_result_string(fresult));
        }
        gs->alt_cloud_name_changed = 1;
    }

    if(pb_alt_cloud && strncmp(gs->listener_alt_cloud_name, pb_alt_cloud, PRIVATE_BROKER_CLOUD_NAME_LEN)) {
        ZPN_LOG(AL_INFO, "Private broker %"PRId64", group %"PRId64" alt cloud changed %s -> %s ",
                          ZPN_BROKER_GET_GID(),
                          ZPN_BROKER_GET_GROUP_GID(),
                          strnlen(gs->listener_alt_cloud_name, PRIVATE_BROKER_CLOUD_NAME_LEN) ? gs->listener_alt_cloud_name : gs->cfg_key_cloud,
                          pb_alt_cloud);
        snprintf(gs->listener_alt_cloud_name, PRIVATE_BROKER_CLOUD_NAME_LEN, "%s", pb_alt_cloud);
        fresult = pbroker_store_alt_cloud_to_file(gs->listener_alt_cloud_name, gs->redir_alt_cloud_name);
        if (fresult) {
            ZPN_LOG(AL_ERROR, "Failed to write alt_cloud to local cache file. Error: %s", zpn_result_string(fresult));
        }
        gs->alt_cloud_name_changed = 1;
    }

}

static void zpn_private_broker_group_set_dynamic_cfg(struct zpn_private_broker_group *group)
{
    /* Set dynamic PSE group config i.e latitude, longitude and Country code */
    if (g_broker_common_cfg && group &&
     g_broker_common_cfg->instance_type == ZPN_INSTANCE_TYPE_PRIVATE_BROKER &&
     g_broker_common_cfg->private_broker.pb_group_id == group->gid) {

        if (g_broker_common_cfg->private_broker.latitude != group->latitude) {
            ZPN_LOG(AL_NOTICE, "Private broker group config latitude update from:%f to:%f",
                    g_broker_common_cfg->private_broker.latitude, group->latitude);
            g_broker_common_cfg->private_broker.latitude = group->latitude;
        }

        if (g_broker_common_cfg->private_broker.longitude != group->longitude) {
            ZPN_LOG(AL_NOTICE, "Private broker group config longitude update from:%f to:%f",
                    g_broker_common_cfg->private_broker.longitude, group->longitude);
            g_broker_common_cfg->private_broker.longitude = group->longitude;
        }

        if (group->country_code) {
            if (strncmp(g_broker_common_cfg->private_broker.cc, group->country_code, strlen(group->country_code)) != 0) {
                ZPN_LOG(AL_NOTICE, "Private broker group config country_code update from:%s to:%s",
                        g_broker_common_cfg->private_broker.cc, group->country_code);
                snprintf(g_broker_common_cfg->private_broker.cc, sizeof(g_broker_common_cfg->private_broker.cc), "%s", group->country_code);
            }
        } else {
            g_broker_common_cfg->private_broker.cc[0] = 0;
        }
    }

    return;
}

static int zpn_private_broker_group_row_callback(void *cookie,
                                                 struct wally_registrant *registrant,
                                                 struct wally_table *table,
                                                 struct argo_object *previous_row,
                                                 struct argo_object *row,
                                                 int64_t request_id)
{

    struct zpn_private_broker_group *pbg = row->base_structure_void;
#ifdef DTA_CHECKPOINT
    if(pbg->scope_gid && pbg->scope_gid != pbg->customer_gid) return WALLY_RESULT_NO_ERROR;
#endif

    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();
    if (gs->alt_cloud_feature_state_initialised &&
        gs->alt_cloud_feature_state &&
        ZPN_BROKER_GET_GROUP_GID() &&
        ZPN_BROKER_GET_GROUP_GID() == pbg->gid) {
            zpn_pbroker_group_verify_alt_cloud_name_change(pbg->alt_cloud);
    }

    if (g_broker_common_cfg && g_broker_common_cfg->instance_type == ZPN_INSTANCE_TYPE_PRIVATE_BROKER &&
            g_broker_common_cfg->private_broker.pb_group_id == pbg->gid) {
        zpn_private_broker_group_set_dynamic_cfg(pbg);
        if (!zpn_is_dr_mode_active()) {
            zpn_private_broker_site_notify_cfg_site_gid(pbg->site_gid);
        }
    }

    if (zpn_debug_get(ZPN_DEBUG_PRIVATE_BROKER_IDX)) {
        char dump[8000];
        if (argo_object_dump(row, dump, sizeof(dump), NULL, 0) == ARGO_RESULT_NO_ERROR) {
            ZPN_DEBUG_PRIVATE_BROKER("Row callback: %s", dump);
        }
    }

    return ZPN_RESULT_NO_ERROR;
}

static int zpn_broker_ddil_config_row_callback(void *cookie,
                                               struct wally_registrant *registrant,
                                               struct wally_table *table,
                                               struct argo_object *previous_row,
                                               struct argo_object *row,
                                               int64_t request_id)
{
    struct zpn_ddil_config *ddil_config = row->base_structure_void;
#ifdef DTA_CHECKPOINT
    if(site->scope_gid && site->scope_gid != site->customer_gid) return WALLY_RESULT_NO_ERROR;
#endif

    if (ZPN_BROKER_IS_PRIVATE()) {
        zpn_private_broker_site_notify_cfg_ddil_config(ddil_config);
    }

    zpn_idp_cert_set_refresh(ddil_config->customer_gid);

    if (zpn_debug_get(ZPN_DEBUG_PRIVATE_BROKER_IDX)) {
        char *text_dump = NULL;
        if (argo_object_allocate_and_dump(row, &text_dump, NULL, 1) == ARGO_RESULT_NO_ERROR) {
            ZPN_DEBUG_PRIVATE_BROKER("Row callback: %s", text_dump);
            argo_object_deallocate_dump(text_dump);
        }
    }

    return ZPN_RESULT_NO_ERROR;
}

static int zpn_private_broker_firedrill_row_callback(void *cookie,
                                                    struct wally_registrant *registrant,
                                                    struct wally_table *table,
                                                    struct argo_object *previous_row,
                                                    struct argo_object *row,
                                                    int64_t request_id)
{
    if (zpn_debug_get(ZPN_DEBUG_PRIVATE_BROKER_IDX)) {
        char dump[8000];
        if (argo_object_dump(row, dump, sizeof(dump), NULL, 0) == ARGO_RESULT_NO_ERROR) {
            ZPN_DEBUG_SITEC("Row callback: %s", dump);
        }
    }
    return ZPN_RESULT_NO_ERROR;
}


static int zpn_site_row_callback(void *cookie,
                                 struct wally_registrant *registrant,
                                 struct wally_table *table,
                                 struct argo_object *previous_row,
                                 struct argo_object *row,
                                 int64_t request_id)
{
    struct zpn_site *site = row->base_structure_void;
    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();

#ifdef DTA_CHECKPOINT
    if(site->scope_gid && site->scope_gid != site->customer_gid) return WALLY_RESULT_NO_ERROR;
#endif

    if (ZPN_BROKER_IS_PRIVATE() && site->gid == zpn_private_broker_get_site_gid_with_lock()) {
        zpn_private_broker_site_notify_cfg_site_config(!site->deleted && site->enabled, site->reenroll_period, site->sitec_preferred);
    }

    if (zpn_debug_get(ZPN_DEBUG_PRIVATE_BROKER_IDX)) {
        char dump[8000];
        if (argo_object_dump(row, dump, sizeof(dump), NULL, 0) == ARGO_RESULT_NO_ERROR) {
            ZPN_DEBUG_PRIVATE_BROKER("Row callback: %s", dump);
        }
    }

    if (ZPN_BROKER_IS_PRIVATE() && site->firedrill_enabled && gs->firedrill_status == ZPN_PRIVATE_BROKER_FIREDRILL_DISABLED ) {
        ZPN_LOG(AL_INFO, "firedrill triggered for customer: %"PRId64"", site->customer_gid);
        zpn_pbroker_firedrill_session_request();
    }
    return ZPN_RESULT_NO_ERROR;
}

static int zpn_site_controller_row_callback(void *cookie,
                                            struct wally_registrant *registrant,
                                            struct wally_table *table,
                                            struct argo_object *previous_row,
                                            struct argo_object *row,
                                            int64_t request_id)
{
    struct zpn_site_controller *sitec = row->base_structure_void;
#ifdef DTA_CHECKPOINT
    if(sitec->scope_gid && sitec->scope_gid != sitec->customer_gid) return WALLY_RESULT_NO_ERROR;
#endif

    if (ZPN_BROKER_IS_PRIVATE()) {
        zpn_private_broker_site_notify_cfg_sitec_config(sitec);
    }

    if (zpn_debug_get(ZPN_DEBUG_PRIVATE_BROKER_IDX)) {
        char dump[8000];
        if (argo_object_dump(row, dump, sizeof(dump), NULL, 0) == ARGO_RESULT_NO_ERROR) {
            ZPN_DEBUG_PRIVATE_BROKER("Row callback: %s", dump);
        }
    }

    return ZPN_RESULT_NO_ERROR;
}

static int zpn_sitec_group_row_callback(void *cookie,
                                        struct wally_registrant *registrant,
                                        struct wally_table *table,
                                        struct argo_object *previous_row,
                                        struct argo_object *row,
                                        int64_t request_id)
{
    struct zpn_site_controller_group *group = row->base_structure_void;
#ifdef DTA_CHECKPOINT
    if(group->scope_gid && group->scope_gid != group->customer_gid) return WALLY_RESULT_NO_ERROR;
#endif

    if (ZPN_BROKER_IS_PRIVATE()) {
        zpn_private_broker_site_notify_cfg_sitec_group_config(group);
    }

    if (zpn_debug_get(ZPN_DEBUG_PRIVATE_BROKER_IDX)) {
        char dump[8000];
        if (argo_object_dump(row, dump, sizeof(dump), NULL, 0) == ARGO_RESULT_NO_ERROR) {
            ZPN_DEBUG_PRIVATE_BROKER("Row callback: %s", dump);
        }
    }

    return ZPN_RESULT_NO_ERROR;
}


static int
zpn_broker_client_dump_one(struct zpath_debug_state* request_state,
                           const char **query_values,
                           int query_value_count,
                           void *cookie)

{
    struct zpn_broker_connected_clients* connected_clients;
    struct zpn_broker_client_fohh_state* cstate;
    const char*                          tunnel_id;

    if (!query_values[0]) {
        ZDP("tunnel ID of the user is required\n");
        goto done;
    }

    tunnel_id = query_values[0];

    connected_clients = &(broker.clients);
    pthread_mutex_lock(&(connected_clients->lock));
    LIST_FOREACH(cstate, &(connected_clients->client_list), list_entry) {
        if (0 == strncmp(tunnel_id, cstate->tunnel_id, strlen(cstate->tunnel_id))) {
            zpn_broker_client_path_cache_dump(cstate, request_state);
        }
    }
    pthread_mutex_unlock(&(connected_clients->lock));

done:
    return ZPN_RESULT_NO_ERROR;
}


static int
zpn_broker_client_swittchcloud_status(struct zpath_debug_state* request_state,
                                      const char **query_values,
                                      int query_value_count,
                                      void *cookie)
{
    struct zpn_broker_connected_clients* connected_clients;
    struct zpn_broker_client_fohh_state* c_state;
    const char*                          tunnel_id;

    int state_ddil_on = 0;
    int64_t monitor_count = 0;
    int num_mtunnels = 0;

    if (!ZPN_BROKER_IS_PRIVATE()) {
        ZDP("this command is valid for private service edge only\n");
        goto done;
    }

    if (!query_values[0]) {
        ZDP("tunnel ID of the user is required\n");
        goto done;
    }

    tunnel_id = query_values[0];

    connected_clients = &(broker.clients);
    pthread_mutex_lock(&(connected_clients->lock));
    LIST_FOREACH(c_state, &(connected_clients->client_list), list_entry) {
        if (0 == strncmp(tunnel_id, c_state->tunnel_id, strlen(c_state->tunnel_id))) {
            state_ddil_on = c_state->state_ddil_on;
            monitor_count = c_state->monitor_count;
            num_mtunnels = c_state_get_mtunnel_count(c_state);
            break;
        }
    }
    pthread_mutex_unlock(&(connected_clients->lock));

    if (!c_state) {
        ZDP("tunnel ID of the user is not found: %s\n", tunnel_id);
        goto done;
    }

    if (!state_ddil_on) {
        ZDP("ddil_on is 0 for this tunnel\n");
        goto done;
    }

    int64_t remain = zpn_broker_client_get_switch_to_cloud_time(monitor_count, num_mtunnels);
    ZDP("switchtime %s, ddil_on %d, monitor_count %"PRId64", mtunnels %d\n",
            zpn_private_broker_get_is_switchtime_enabled() ? "enabled" : "disabled",
            state_ddil_on, monitor_count, num_mtunnels);
    if (remain >= 0) {
        ZDP("send switch_to_cloud message after %"PRId64" seconds\n", remain);
    } else {
        ZDP("won't send switch_to_cloud message\n");
    }

done:
    return ZPN_RESULT_NO_ERROR;
}

static int
zpn_broker_client_test_swittchcloud_status(struct zpath_debug_state* request_state,
                                           const char **query_values,
                                           int query_value_count,
                                           void *cookie)
{
    if (!query_values[0]) {
        ZDP("Require argument: the elapsed time after client is connected\n");
        goto done;
    }
    if (!query_values[1]) {
        ZDP("Require argument: the number of mtunnels\n");
        goto done;
    }

    int64_t monitor_count = atoi(query_values[0]) / ZPN_TUNNEL_MONITOR_INTERVAL_S;
    if (monitor_count < 0) {
        ZDP("The elapsed time should not be negative\n");
        goto done;
    }
    int num_mtunnels = atoi(query_values[1]);

    int64_t remain = zpn_broker_client_get_switch_to_cloud_time(monitor_count, num_mtunnels);
    ZDP("switchtime %"PRId64" (%s), current tick %"PRId64", mtunnels %d\n",
            zpn_private_broker_get_max_allowed_switchtime_s_with_lock(),
            zpn_private_broker_get_is_switchtime_enabled() ? "enabled" : "disabled",
            monitor_count, num_mtunnels);
    if (remain >= 0) {
        ZDP("send switch_to_cloud message after %"PRId64" seconds\n", remain);
    } else {
        ZDP("won't send switch_to_cloud message\n");
    }

done:
    return ZPN_RESULT_NO_ERROR;
}

static int
zpn_broker_client_dump_all(struct zpath_debug_state* request_state,
                           const char **query_values,
                           int query_value_count,
                           void *cookie)
{
    struct zpn_broker_connected_clients*    connected_clients;
    struct zpn_broker_client_fohh_state*    cstate;

    connected_clients = &(broker.clients);
    pthread_mutex_lock(&(connected_clients->lock));
    LIST_FOREACH(cstate, &(connected_clients->client_list), list_entry) {
        zpn_broker_client_path_cache_dump(cstate, request_state);
    }
    pthread_mutex_unlock(&(connected_clients->lock));

    return ZPN_RESULT_NO_ERROR;
}

void dr_register_argo_objects()
{
    // Filter tables - Config connection
    argo_zpath_customer_init();
    argo_zpn_app_group_relation_init();
    argo_zpn_app_server_init();
    argo_zpn_application_init();
    argo_zpn_application_domain_init();
    argo_zpn_application_group_init();
    argo_zpn_application_group_application_mapping_init();
    argo_zpn_assistant_init();
    argo_zpn_assistant_group_init();
    argo_zpn_assistant_version_init();
    argo_zpn_assistantgroup_assistant_relation_init();
    argo_zpn_customer_config_init();
    argo_zpn_c2c_client_registration_init();
    argo_zpn_idp_init();
    argo_zpn_idp_cert_init();
    //argo_zpn_inspection_application_init();
    argo_zpn_policy_set_init();
    argo_zpn_posture_profile_db_init();
    argo_zpn_private_broker_init();
    argo_zpn_private_broker_load_init();
    argo_zpn_private_broker_version_init();
    argo_zpn_privatebrokergroup_trustednetwork_mapping_init();
    argo_zpn_rule_init();
    argo_zpn_saml_attrs_init();
    argo_zpn_scim_attr_header_init();
    argo_zpn_server_group_init();
    argo_zpn_server_group_assistant_group_init();
    argo_zpn_servergroup_server_relation_init();
    argo_zpn_shared_customer_domain_init();
    argo_zpn_signing_cert_init();
    argo_zpn_trusted_network_init();
    argo_zpn_znf_init();
    argo_zpn_znf_to_group_init();
    argo_zpn_znf_group_init();
    //argo_zpn_command_probe_init();

    // Allow tables - Config connection
    argo_zpn_rule_condition_operand_init();
    argo_zpn_rule_condition_set_init();
    argo_zpn_rule_to_server_group_init();
    argo_zpn_rule_to_assistant_group_init();
    argo_zpn_client_init();
    argo_zpn_issuedcert_init();
    argo_zpn_private_broker_group_init();
    argo_zpn_private_broker_to_group_init();
    argo_zpn_machine_init();
    argo_zpn_machine_group_init();
    argo_zpn_machine_to_group_init();

    // Filter tables - Config override connection
    argo_zpath_config_override_init();
    argo_et_customer_userdb_init();
    argo_et_customer_zone_init();

    // Allow tables - Config override connection
    argo_et_userdb_init();
    argo_et_translate_init();
    argo_et_translate_code_init();

    // Allow tables - Userdb connection
    argo_zpn_scim_user_init();
    argo_zpn_scim_user_attribute_init();
    argo_zpn_scim_user_group_init();
    argo_zpn_scim_group_init();
    argo_zpn_user_risk_init();
    argo_zpn_aae_profile_conclusion_init();

    // Workload tags are synced from ZIA to ZPA
    argo_zpn_workload_tag_group_init();

    // Extranet
    argo_zpn_location_group_to_location_init();
    argo_zpn_rule_to_location_group_init();
    argo_zpn_rule_to_location_init();
}

static int zpn_broker_proxy_sni_list_cb(struct zpath_debug_state *request_state,
                                        const char **query_values,
                                        int query_value_count,
                                        void *cookie)
{
    int zpn_broker_proxy_sni_cnt;
    char **zpn_broker_proxy_sni_list;
    char **zpn_broker_proxy_sni_resolve_domains;
    int *zpn_broker_proxy_sni_wildcard_prefix;
    int *zpn_broker_proxy_sni_use_ssl;

    zpn_broker_proxy_get_snis(&zpn_broker_proxy_sni_list,
                              &zpn_broker_proxy_sni_wildcard_prefix,
                              &zpn_broker_proxy_sni_resolve_domains,
                              &zpn_broker_proxy_sni_use_ssl,
                              &zpn_broker_proxy_sni_cnt);

    ZDP("broker_proxy_sni_count = %d\n", zpn_broker_proxy_sni_cnt);
    for (int i = 0; i < zpn_broker_proxy_sni_cnt; i++) {
        ZDP("- SNI = %s, resolve_domain = %s, wildcard_prefix = %d, use_ssl = %d\n",
            zpn_broker_proxy_sni_list[i], zpn_broker_proxy_sni_resolve_domains[i], zpn_broker_proxy_sni_wildcard_prefix[i], zpn_broker_proxy_sni_use_ssl[i]);
    }

    return ZPN_RESULT_NO_ERROR;
}

int fill_app_thread_stats(void *cookie, int counter, void *structure_data)
{
    struct zevent_base *z_base = cookie;
    struct app_thread_stats *stats = structure_data;

    stats->app_thread_high_priority_enqueue_count = z_base->high_priority_enqueue_count;
    stats->app_thread_high_priority_dequeue_count = z_base->high_priority_dequeue_count;
    stats->app_thread_low_priority_enqueue_count = z_base->low_priority_enqueue_count;
    stats->app_thread_low_priority_dequeue_count = z_base->low_priority_dequeue_count;
    stats->app_thread_low_priority_q_wait_time = z_base->low_priority_queue_wait_time;
    stats->app_thread_high_priority_q_wait_time = z_base->high_priority_queue_wait_time;
    stats->app_thread_high_priority_q_wait_time_gt_2s = z_base->high_priority_queue_wait_time_gt_2s;
    stats->app_thread_high_priority_q_wait_time_gt_5s = z_base->high_priority_queue_wait_time_gt_5s;
    stats->app_thread_high_priority_q_wait_time_gt_10s = z_base->high_priority_queue_wait_time_gt_10s;

    return ARGO_RESULT_NO_ERROR;
}

static int zpn_broker_add_known_sni_proxy_domains(struct zcdns *zcdns, struct fohh_generic_server *server)
{
    int i;
    int res;

    ZPN_LOG(AL_NOTICE, "alt_cloud: adding SNI proxy domains");
    if (!zcdns) {
        ZPN_LOG(AL_ERROR, "alt_cloud: zcdns is NULL while adding sni proxy domains, return!!");
        return ZPN_RESULT_BAD_ARGUMENT;
    }

    /* Proxy api endpoint for flex cloud or any new cloud configured in zpath_local table. */
    if (ZPATH_LOCAL_MGMTAPI_FQDN) {
        res = zpn_broker_proxy_add(zcdns, sni_server, ZPATH_LOCAL_MGMTAPI_FQDN, 0, NULL, 0);
        if (res) {
            ZPN_LOG(AL_ERROR, "Could not register sni proxy server for %s", ZPATH_LOCAL_MGMTAPI_FQDN);
            return res;
        }
    }

    struct zpath_cloud_config *cloud_config=zpath_get_cloud_config_from_name(ZPATH_LOCAL_CLOUD_NAME);

    if(!cloud_config) {
        ZPN_LOG(AL_ERROR, "NO Proxy SNIs to be added for Cloud %s", ZPATH_LOCAL_CLOUD_NAME);
        return ZPN_RESULT_BAD_ARGUMENT;
    }

    if(cloud_config->sarge) {
        res = zpn_broker_proxy_add(zcdns, sni_server, cloud_config->sarge->dist_hostname, 0, NULL, 0);
        if (res) {
            ZPN_LOG(AL_ERROR, "Could not register sni proxy server for %s", cloud_config->sarge->dist_hostname);
            return res;
        }
    }

    struct zpn_apimap *zpn_apis=cloud_config->zpn_apis;
    size_t zpn_apis_count=cloud_config->zpn_apis_count;
    /* Allow each of the api endpoints to be proxied via broker */
    for (i = 0; i < zpn_apis_count; i++) {
        res = zpn_broker_proxy_add(zcdns, sni_server, zpn_apis[i].api, 0, NULL, 0);
        if (res) {
            ZPN_LOG(AL_ERROR, "Could not register sni proxy server for %s", zpn_apis[i].api);
            return res;
        }
    }

    struct zpath_misc_domain_map *zpath_misc_domains = cloud_config->zpath_misc;
    size_t zpath_misc_domains_count = cloud_config->zpath_misc_count;

    for (i = 0; i < zpath_misc_domains_count; i++) {
        res = zpn_broker_proxy_add(zcdns,
                                    sni_server,
                                    zpath_misc_domains[i].domain,
                                    0,
                                    zpath_misc_domains[i].resolve_domain,
                                    0);
        if (res) {
            ZPN_LOG(AL_ERROR, "Could not register sni proxy server for %s", zpath_misc_domains[i].domain);
            return res;
        }
    }

    struct zpn_samlsp_map *zpn_samlsps=cloud_config->zpn_samlsps;
    size_t zpn_samlsps_count=cloud_config->zpn_samlsp_count;

    for (i = 0; i < zpn_samlsps_count; i++) {
        res = zpn_broker_proxy_add(zcdns, sni_server, zpn_samlsps[i].samlsp, 0, zpn_samlsps[i].proxy, 0);
        if (res) {
            ZPN_LOG(AL_ERROR, "Could not register sni proxy server for %s", zpn_samlsps[i].samlsp);
            return res;
        }
    }

    struct zpn_authsp_map *zpn_authsps=cloud_config->zpn_authsps;
    size_t zpn_authsps_count=cloud_config->zpn_authsp_count;

    for (i = 0; i < zpn_authsps_count; i++) {
        res = zpn_broker_proxy_add(zcdns, sni_server, zpn_authsps[i].authsp, 0, zpn_authsps[i].proxy, 0);
        if (res) {
            ZPN_LOG(AL_ERROR, "Could not register sni proxy server for %s", zpn_authsps[i].authsp);
            return res;
        }
    }

    return ZPN_RESULT_NO_ERROR;
}

char *zpn_broker_get_instance_full_name()
{
    if(ZPN_BROKER_IS_PUBLIC()) {
        return zpath_instance_global_state.instance_full_name;
    } else if (ZPN_BROKER_IS_PRIVATE()) {
        if(*g_broker_common_cfg->private_broker.alt_cloud_feature_state_initialised &&
           *g_broker_common_cfg->private_broker.alt_cloud_feature_state) {
            return ZPATH_LOCAL_FULL_NAME;
        }

        struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();
        return gs->instance_full_name;
    } else {
        return ZPATH_LOCAL_FULL_NAME;
    }
}

int zpn_broker_is_alt_cloud_configured(void)
{
    if (ZPN_BROKER_IS_PUBLIC()) {
        if (broker_alt_cloud_support_enabled &&
            zpath_instance_global_state.current_config->alt_cloud) {
            return 1;
        }
    } else {
        if(*g_broker_common_cfg->private_broker.alt_cloud_feature_state_initialised &&
           *g_broker_common_cfg->private_broker.alt_cloud_feature_state &&
           strnlen(g_broker_common_cfg->private_broker.alt_cloud_name, PRIVATE_BROKER_CLOUD_NAME_LEN)) {
            return 1;
        }
    }
    return 0;
}

/*
 * zpn_broker_get_cloud_name
 *  Get cloud name depending on whether alt-cloud is enabled or not.
 *  PS: pre-allocated buffer being passed in
 */
void zpn_broker_get_cloud_name(char *cloud_name, size_t len)
{
    if (ZPN_BROKER_IS_PUBLIC()) {
        if (broker_alt_cloud_support_enabled &&
            zpath_instance_global_state.current_config->alt_cloud) {
            snprintf(cloud_name, len, "%s", zpath_instance_global_state.current_config->alt_cloud);
        } else {
            snprintf(cloud_name, len, "%s", ZPATH_LOCAL_CLOUD_NAME);
        }
    } else {
        if(*g_broker_common_cfg->private_broker.alt_cloud_feature_state_initialised &&
           *g_broker_common_cfg->private_broker.alt_cloud_feature_state &&
           strnlen(g_broker_common_cfg->private_broker.alt_cloud_name, PRIVATE_BROKER_CLOUD_NAME_LEN)) {
            snprintf_nowarn(cloud_name, len, "%s", g_broker_common_cfg->private_broker.alt_cloud_name);
        } else {
            snprintf_nowarn(cloud_name, len, "%s", ZPATH_LOCAL_CLOUD_NAME);
        }
    }
}

/*
 * zpn_broker_get_configured_alt_cloud
 *  returns configured alt-cloud if configured, else return NULL
 */
char * zpn_broker_get_configured_alt_cloud(void)
{
    if (ZPN_BROKER_IS_PUBLIC()) {
        return zpath_instance_global_state.current_config->alt_cloud;
    } else {
        return g_broker_common_cfg->private_broker.alt_cloud_name;
    }
}

char * zpn_broker_get_default_cloud_name(void)
{
    if (ZPN_BROKER_IS_PUBLIC()) {
        return ZPATH_LOCAL_CLOUD_NAME;
    } else {
        return g_broker_common_cfg->private_broker.cloud_name;
    }
}

/*
 * zpn_broker_alt_cloud_sni_update_internal
 * Update connection snis for all broker, connector , PSE connections when alt-cloud changes
 */
static void zpn_broker_alt_cloud_sni_update_internal(char *old_alt_cloud, char *new_alt_cloud)
{
    ZPN_LOG(AL_NOTICE, "alt_cloud: alt_cloud sni update called.. old_alt_cloud: %s  new_alt_cloud: %s",
                    old_alt_cloud ? old_alt_cloud : "<None>",
                    new_alt_cloud ? new_alt_cloud : "<None>");

    /* nothing to be done if alt-cloud has not changed */
    if (!old_alt_cloud && !new_alt_cloud)
        return;

    if (old_alt_cloud && new_alt_cloud && !strcmp(old_alt_cloud, new_alt_cloud)) {
        return;
    }

    /* Broker client connections */
    zpn_broker_client_update_connection_snis(old_alt_cloud, new_alt_cloud);

    /* connnector connections */
    zpn_broker_assistant_update_connection_snis(old_alt_cloud, new_alt_cloud);

    /* PSE connections - only for public broker, see zpn_broker_pbroker_listen  */
    if (g_broker_common_cfg->instance_type == ZPN_INSTANCE_TYPE_PUBLIC_BROKER) {
        zpn_broker_pbroker_update_connection_snis(old_alt_cloud, new_alt_cloud);
    }

    /* SNI proxy */
    struct fohh_generic_server *sni_server = zpath_debug_lookup_fohh_generic_server("broker");
    if (!sni_server) {
        ZPN_LOG(AL_ERROR, "Error: could not retrieve sni_server for sni proxy updation!");
        return;
    }
    if (!old_alt_cloud && new_alt_cloud) {
        /* alt-cloud is newly being added, delete all sni proxy */
        zpn_broker_clear_all_sni_proxy_domains(sni_server);
    } else if (old_alt_cloud && !new_alt_cloud) {
        /* alt-cloud is being removed, add sni proxy */
        zpn_broker_restore_all_sni_proxy_domains(broker_zcdns, sni_server);
    }
}

void zpn_broker_alt_cloud_sni_update(char *old_alt_cloud, char *new_alt_cloud)
{
    if (CONFIG_FEATURE_ALT_CLOUD_DISABLED == broker_alt_cloud_support_enabled) {
        ZPN_LOG(AL_NOTICE, "alt_cloud: feature disabled; ignoring instance table update!");
        return;
    }
    zpn_broker_alt_cloud_sni_update_internal(old_alt_cloud, new_alt_cloud);
}

static void zpn_broker_alt_cloud_monitor_callback(const int64_t *config_value, int64_t impacted_gid)
{
    /* Change any non-zero value to 1. */
    if (broker_alt_cloud_support_enabled) {
        broker_alt_cloud_support_enabled = CONFIG_FEATURE_ALT_CLOUD_ENABLED;
    }
    ZPN_LOG(AL_NOTICE, "alt_cloud: alt_cloud_global value: %ld",(long)broker_alt_cloud_support_enabled);

    /* Update full instance name based on feature flag and alt-cloud name */
    zpath_instance_update_full_instance_name(broker_alt_cloud_support_enabled);

    /* remove alt_cloud */
    if (broker_alt_cloud_support_enabled == CONFIG_FEATURE_ALT_CLOUD_DISABLED) {
        zpn_broker_alt_cloud_sni_update_internal(zpath_instance_global_state.current_config->alt_cloud, NULL);
    } else { /* enabling the feature, go from default to alt_cloud */
        zpn_broker_alt_cloud_sni_update_internal(NULL, zpath_instance_global_state.current_config->alt_cloud);
    }
}

/*
 * zpn_broker_alt_cloud_support_init
 *  Initializing alt_cloud support infra and overrides
 */
void zpn_broker_alt_cloud_support_init(void)
{
    broker_alt_cloud_support_enabled = CONFIG_FEATURE_ALT_CLOUD_DISABLED;

    /* retrieve the current value and setup monitoring */
    if (ZPN_BROKER_IS_PUBLIC()) {
        int64_t root_customer_gid = ZPATH_GID_GET_CUSTOMER_GID(zpath_instance_global_state.current_config->gid);

        broker_alt_cloud_support_enabled = zpath_config_override_get_config_int(CONFIG_FEATURE_ALT_CLOUD,
                                                            &broker_alt_cloud_support_enabled,
                                                            CONFIG_FEATURE_ALT_CLOUD_DEFAULT_VALUE,
                                                            zpath_instance_global_state.current_config->gid,
                                                            root_customer_gid,
                                                            (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                            (int64_t)0);
        ZPN_LOG(AL_NOTICE, "alt_cloud config for broker gid %ld root customer gid %ld is  %ld",
                            (long)zpath_instance_global_state.current_config->gid,
                            (long)root_customer_gid,
                            (long)broker_alt_cloud_support_enabled);

        zpath_config_override_monitor_int(CONFIG_FEATURE_ALT_CLOUD,
                                          &broker_alt_cloud_support_enabled,
                                          zpn_broker_alt_cloud_monitor_callback,
                                          CONFIG_FEATURE_ALT_CLOUD_DEFAULT_VALUE,
                                          zpath_instance_global_state.current_config->gid,
                                          root_customer_gid,
                                          (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                          (int64_t)0);
    }
}

void zpn_pbroker_alt_cloud_support_init(void)
{

    /* Instance type is Private Service Edge */
    int64_t customer_gid = g_broker_common_cfg->private_broker.customer_id;
    int64_t *alt_cloud_feature_state = g_broker_common_cfg->private_broker.alt_cloud_feature_state;
    int *alt_cloud_feature_state_initialised = g_broker_common_cfg->private_broker.alt_cloud_feature_state_initialised;

    *alt_cloud_feature_state = zpath_config_override_get_config_int(CONFIG_FEATURE_ALT_CLOUD,
                                                                    alt_cloud_feature_state,
                                                                    CONFIG_FEATURE_ALT_CLOUD_DEFAULT_VALUE,
                                                                    g_broker_common_cfg->private_broker.broker_id,
                                                                    g_broker_common_cfg->private_broker.pb_group_id,
                                                                    customer_gid,
                                                                    (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                                    (int64_t)0);

    ZPN_LOG(AL_NOTICE, "Customer:%"PRId64" pbroker gid:%"PRId64" pbroker group gid:%"PRId64" alt_cloud config val:%"PRId64"",
                        customer_gid,
                        g_broker_common_cfg->private_broker.broker_id,
                        g_broker_common_cfg->private_broker.pb_group_id,
                        *alt_cloud_feature_state);

    *alt_cloud_feature_state_initialised = 1;

    zpath_config_override_monitor_int(CONFIG_FEATURE_ALT_CLOUD,
                                      alt_cloud_feature_state,
                                      zpn_private_broker_alt_cloud_config_override_monitor_cb,
                                      CONFIG_FEATURE_ALT_CLOUD_DEFAULT_VALUE,
                                      g_broker_common_cfg->private_broker.broker_id,
                                      g_broker_common_cfg->private_broker.pb_group_id,
                                      customer_gid,
                                      (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                      (int64_t)0);
}

/* Checks FQDN_TO_SRV_IP hard disable config override for given customer. By default, it's disabled. */
static int zpn_broker_is_policy_fqdn_to_srv_ip_hard_disabled()
{
    int64_t config_value = 0;

    config_value = zpath_config_override_get_config_int(POLICY_FQDN_TO_SERV_IP_HARD_DISABLE,
                                                        &config_value,
                                                        DEFAULT_POLICY_FQDN_TO_SERV_IP_HARD_DISABLE,
                                                        (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                        (int64_t)0);
    ZPN_DEBUG_COR("FQDN_TO_SRV_IP feature hard disable config value is %"PRId64, config_value);

    return config_value ? 1 : 0;
}

static void zpn_broker_policy_workload_tag_grp_hard_disabled_init() {
    g_policy_workload_tag_grp_hard_disabled = zpn_broker_is_workload_tag_grp_hard_disabled();
    zpath_config_override_monitor_int(CONFIG_FEATURE_WORKLOAD_TAG_GRP_HARD_DISABLED,
                                      &g_policy_workload_tag_grp_hard_disabled,
                                      NULL,
                                      CONFIG_FEATURE_WORKLOAD_TAG_GRP_HARD_DISABLED_DEFAULT,
                                      (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                      (int64_t)0);
}

static void zpn_broker_policy_fqdn_to_srv_ip_hard_disabled_feature_init() {
    g_policy_fqdn_to_srv_ip_hard_disabled_feature_status = zpn_broker_is_policy_fqdn_to_srv_ip_hard_disabled();
    zpath_config_override_monitor_int(POLICY_FQDN_TO_SERV_IP_HARD_DISABLE,
                                      &g_policy_fqdn_to_srv_ip_hard_disabled_feature_status,
                                      NULL,
                                      DEFAULT_POLICY_FQDN_TO_SERV_IP_HARD_DISABLE,
                                      (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                      (int64_t)0);
}

/* Checks Match SIPA only app segments for SIPA client hard disable config override for a given customer. By default, it's disabled. */
static int zpn_broker_is_app_match_sipa_only_apps_for_sipa_client_hard_disabled()
{
    int64_t config_value = 0;

    config_value = zpath_config_override_get_config_int(APPLICATION_MATCH_SIPA_ONLY_APPS_FOR_SIPA_CLIENT_HARD_DISABLED,
                                                        &config_value,
                                                        DEFAULT_APPLICATION_MATCH_SIPA_ONLY_APPS_FOR_SIPA_CLIENT_HARD_DISABLED,
                                                        (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                        (int64_t)0);
    ZPN_DEBUG_COR("Application Match SIPA Only Apps For SIPA Client feature hard disable config value is %"PRId64, config_value);

    return config_value ? 1 : 0;
}

static void zpn_broker_app_match_sipa_only_for_sipa_client_hard_disabled_feature_init() {
    g_app_match_sipa_only_apps_for_sipa_client_hard_disabled_feature_status = zpn_broker_is_app_match_sipa_only_apps_for_sipa_client_hard_disabled();
    zpath_config_override_monitor_int(APPLICATION_MATCH_SIPA_ONLY_APPS_FOR_SIPA_CLIENT_HARD_DISABLED,
                                      &g_app_match_sipa_only_apps_for_sipa_client_hard_disabled_feature_status,
                                      NULL,
                                      DEFAULT_APPLICATION_MATCH_SIPA_ONLY_APPS_FOR_SIPA_CLIENT_HARD_DISABLED,
                                      (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                      (int64_t)0);
}

/* Checks app_scaling bypass improvement hard disable config override for a given customer. By default, it's disabled. */
static int zpn_broker_is_app_scaling_bypass_improvement_hard_disabled()
{
    int64_t config_value = 0;

    config_value = zpath_config_override_get_config_int(APP_SCALING_BYPASS_IMPROVEMENT_HARD_DISABLED,
                                                        &config_value,
                                                        DEFAULT_APP_SCALING_BYPASS_IMPROVEMENT_HARD_DISABLED,
                                                        (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                        (int64_t)0);
    ZPN_DEBUG_COR("App scaling bypass improvement feature hard disable config value is %"PRId64, config_value);

    return config_value ? 1 : 0;
}

static void zpn_broker_app_scaling_bypass_improvement_hard_disabled_feature_init() {
    g_app_scaling_bypass_improvement_hard_disabled_feature_status = zpn_broker_is_app_scaling_bypass_improvement_hard_disabled();
    zpath_config_override_monitor_int(APP_SCALING_BYPASS_IMPROVEMENT_HARD_DISABLED,
                                      &g_app_scaling_bypass_improvement_hard_disabled_feature_status,
                                      NULL,
                                      DEFAULT_APP_SCALING_BYPASS_IMPROVEMENT_HARD_DISABLED,
                                      (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                      (int64_t)0);
}

static void zpn_broker_extranet_hard_disable_init()
{
    g_extranet_hard_disabled = zpn_broker_is_extranet_hard_disabled();
    zpath_config_override_monitor_int(CONFIG_FEATURE_EXTRANET_HARD_DISABLED,
                                      &g_extranet_hard_disabled,
                                      NULL,
                                      CONFIG_FEATURE_EXTRANET_HARD_DISABLED_DEFAULT,
                                      (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                      (int64_t)0);
}

static void zpn_broker_aae_hard_disable_init()
{
    g_aae_profile_hard_disabled = CONFIG_FEATURE_AAE_PROFILE_HARD_DISABLED_DEFAULT;
    zpath_config_override_monitor_int(CONFIG_FEATURE_AAE_PROFILE_HARD_DISABLED,
                                      &g_aae_profile_hard_disabled,
                                      NULL,
                                      CONFIG_FEATURE_AAE_PROFILE_HARD_DISABLED_DEFAULT,
                                      (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                      (int64_t)0);
}

static void zpn_app_scaling_hard_disable_init()
{
    if (ZPN_BROKER_IS_PUBLIC()) {
        g_appscale_hard_disable_clients[zpn_client_type_edge_connector].broker_hard_disabled = DEFAULT_BROKER_CC_APP_SCALING_HARD_DISABLE_FEATURE;
        g_appscale_hard_disable_clients[zpn_client_type_vdi].broker_hard_disabled = DEFAULT_BROKER_VDI_APP_SCALING_HARD_DISABLE_FEATURE;
        zpath_config_override_monitor_int(BROKER_CC_APP_SCALING_HARD_DISABLE_FEATURE,
                                          &g_appscale_hard_disable_clients[zpn_client_type_edge_connector].broker_hard_disabled,
                                          zpn_app_scaling_cc_hard_disable_feature_flag_changed_cb,
                                          DEFAULT_BROKER_CC_APP_SCALING_HARD_DISABLE_FEATURE,
                                          (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                          (int64_t)0);
        zpath_config_override_monitor_int(BROKER_VDI_APP_SCALING_HARD_DISABLE_FEATURE,
                                          &g_appscale_hard_disable_clients[zpn_client_type_vdi].broker_hard_disabled,
                                          zpn_app_scaling_vdi_hard_disable_feature_flag_changed_cb,
                                          DEFAULT_BROKER_VDI_APP_SCALING_HARD_DISABLE_FEATURE,
                                          (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                          (int64_t)0);
    }

    if (ZPN_BROKER_IS_PRIVATE()) {
        g_appscale_hard_disable_clients[zpn_client_type_edge_connector].pse_hard_disabled = DEFAULT_PSE_CC_APP_SCALING_HARD_DISABLE_FEATURE;
        g_appscale_hard_disable_clients[zpn_client_type_vdi].pse_hard_disabled = DEFAULT_PSE_VDI_APP_SCALING_HARD_DISABLE_FEATURE;
        zpath_config_override_monitor_int(PSE_CC_APP_SCALING_HARD_DISABLE_FEATURE,
                                          &g_appscale_hard_disable_clients[zpn_client_type_edge_connector].pse_hard_disabled,
                                          zpn_app_scaling_cc_hard_disable_feature_flag_changed_cb,
                                          DEFAULT_PSE_CC_APP_SCALING_HARD_DISABLE_FEATURE,
                                          (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                          (int64_t)0);
        zpath_config_override_monitor_int(PSE_VDI_APP_SCALING_HARD_DISABLE_FEATURE,
                                          &g_appscale_hard_disable_clients[zpn_client_type_vdi].pse_hard_disabled,
                                          zpn_app_scaling_vdi_hard_disable_feature_flag_changed_cb,
                                          DEFAULT_PSE_VDI_APP_SCALING_HARD_DISABLE_FEATURE,
                                          (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                          (int64_t)0);
    }
}

static void zpn_broker_policy_re_eval_on_scim_update_hard_disable_init()
{
    g_policy_re_eval_on_scim_update_hard_disabled = CONFIG_FEATURE_POLICY_RE_EVAL_ON_SCIM_UPDATE_HARD_DISABLED_DEFAULT;
    zpath_config_override_monitor_int(CONFIG_FEATURE_POLICY_RE_EVAL_ON_SCIM_UPDATE_HARD_DISABLED,
                                      &g_policy_re_eval_on_scim_update_hard_disabled,
                                      NULL,
                                      CONFIG_FEATURE_POLICY_RE_EVAL_ON_SCIM_UPDATE_HARD_DISABLED_DEFAULT,
                                      (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                      (int64_t)0);
}

static void zpn_broker_policy_re_eval_on_posture_change_hard_disable_init()
{
    g_policy_re_eval_on_posture_change_hard_disabled = CONFIG_FEATURE_POLICY_RE_EVAL_ON_POSTURE_CHANGE_HARD_DISABLED_DEFAULT;
    zpath_config_override_monitor_int(CONFIG_FEATURE_POLICY_RE_EVAL_ON_POSTURE_CHANGE_HARD_DISABLED,
                                      &g_policy_re_eval_on_posture_change_hard_disabled,
                                      NULL,
                                      CONFIG_FEATURE_POLICY_RE_EVAL_ON_POSTURE_CHANGE_HARD_DISABLED_DEFAULT,
                                      (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                      (int64_t)0);
}

/* Initialize broker hard disabled config overrides flag */
static inline void zpn_broker_hard_disabled_feature_flag_init()
{
    /* Initialize fqdn to server ip hard disabled config overrides */
    zpn_broker_policy_fqdn_to_srv_ip_hard_disabled_feature_init();
    /* Initialize Match SIPA only app segments for SIPA client hard disabled config overrides */
    zpn_broker_app_match_sipa_only_for_sipa_client_hard_disabled_feature_init();
    /* Initialize App scaling bypass improvement hard disabled config overrides */
    zpn_broker_app_scaling_bypass_improvement_hard_disabled_feature_init();
    /* Initialize Adaptive Access Engine Policy hard disabled config overrides */
    zpn_broker_aae_hard_disable_init();
    /* Initialize Policy Re_eval on SCIM Update hard disabled config overrides */
    zpn_broker_policy_re_eval_on_scim_update_hard_disable_init();
    /* Initialize Policy re-eval on posture change hard disabled config overrides */
    zpn_broker_policy_re_eval_on_posture_change_hard_disable_init();

    /* Initialize app scaling feature hard disabled config overrides */
    zpn_app_scaling_hard_disable_init();
}

void zpn_broker_balance_control_notify_cb()
{
    if (zpn_broker_is_balance_mode_maintainence()) {
        if (strcmp(g_balance_config.balance_role, BALANCE_ROLE_NO_REDIRECT)
                    && strcmp(g_balance_config.balance_role, BALANCE_ROLE_UNCONFIGURED)) {
            zpn_broker_assistant_redirect_assistants(1, BRK_REDIRECT_REASON_BROKER_MAINTENANCE);
            zpn_broker_pbroker_redirect_pbrokers(1, BRK_REDIRECT_REASON_BROKER_MAINTENANCE);
        } else {
            zpn_broker_assistant_redirect_assistants(0, NULL);
            zpn_broker_pbroker_redirect_pbrokers(0, NULL);
        }
    } else {
        zpn_broker_assistant_redirect_assistants(0, NULL);
        zpn_broker_pbroker_redirect_pbrokers(0, NULL);
    }
}

int zpn_register_waf_desc()
{
    int res = ZPN_RESULT_NO_ERROR;
    int len;

    if (g_broker_common_cfg->instance_type == ZPN_INSTANCE_TYPE_PUBLIC_BROKER
            || g_broker_common_cfg->instance_type == ZPN_INSTANCE_TYPE_PRIVATE_BROKER) {
        len = sizeof(zpn_broker_waf_config_override_desc) / sizeof(struct zpath_config_override_desc);
        for (int i = 0; i < len; i++) {
            res = zpath_config_override_desc_register(&zpn_broker_waf_config_override_desc[i]);
            if (res) {
                ZPN_LOG(AL_ERROR, "Error registering for waf config overrides for broker");
                return res;
            }
        }
    }

    /* Starting monitoring for below config overrides on broker */
    zpath_config_override_monitor_int(AUTO_APP_CERT_GEN_FEATURE_GLOBAL_DISABLE,
                                      &g_auto_cert_gen_global_disable,
                                      NULL,
                                      AUTO_APP_CERT_GEN_GLOBAL_DISABLE,
                                      (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                      (int64_t)0);

    zpath_config_override_monitor_int(WAF_PROTOCOL_TAGGING_FEATURE_GLOBAL_DISABLE,
                                      &g_ptag_global_disable,
                                      NULL,
                                      WAF_PROTOCOL_TAGGING_GLOBAL_DISABLE,
                                      (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                      (int64_t)0);

    zpath_config_override_monitor_int(WAF_FEATURE_GLOBAL_DISABLE,
                                      &g_waf_global_disable,
                                      NULL,
                                      WAF_GLOBAL_DISABLE,
                                      (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                      (int64_t)0);

    return res;
}

int zpn_register_pra_desc()
{
    int res = ZPN_RESULT_NO_ERROR;
    int len;

    if ((g_broker_common_cfg->instance_type == ZPN_INSTANCE_TYPE_PUBLIC_BROKER) ||
        (g_broker_common_cfg->instance_type == ZPN_INSTANCE_TYPE_PRIVATE_BROKER)) {
        len = sizeof(zpn_broker_pra_config_override_desc) / sizeof(struct zpath_config_override_desc);
        for (int i = 0; i < len; i++) {
            res = zpath_config_override_desc_register(&zpn_broker_pra_config_override_desc[i]);
            if (res) {
                ZPN_LOG(AL_ERROR, "Error registering for pra config overrides for broker");
                return res;
            }
        }
    }

    return res;
}

int zpn_register_natural_log_throttle()
{
    int res = ZPN_RESULT_NO_ERROR;
    int len;
    len = sizeof(trans_log_natural_log_throttling) / sizeof(struct zpath_config_override_desc);
    for (int i = 0; i < len; i++) {
        res = zpath_config_override_desc_register(&trans_log_natural_log_throttling[i]);
        if (res) {
            ZPN_LOG(AL_ERROR, "Error registering for trans log config overrides for broker");
            return res;
        }
    }
    return res;
}

int
zpn_register_app_tune_desc()
{
        int res = ZPN_RESULT_NO_ERROR;
        int len;

        if (g_broker_common_cfg->instance_type == ZPN_INSTANCE_TYPE_PUBLIC_BROKER) {
            len = sizeof(zpn_broker_app_buffer_tune_config_override_desc) / sizeof(struct zpath_config_override_desc);
            for (int i = 0; i < len; i++) {
                res = zpath_config_override_desc_register(&zpn_broker_app_buffer_tune_config_override_desc[i]);
            }
        } else {
            len = (sizeof(zpn_pse_app_buffer_tune_config_override_desc) / sizeof(struct zpath_config_override_desc));
            for (int i = 0; i < len; i++) {
                res = zpath_config_override_desc_register(&zpn_pse_app_buffer_tune_config_override_desc[i]);
            }
        }

        return res;
}

int zpn_zdx_https_register_desc()
{
    int res = ZPN_RESULT_NO_ERROR;
    int len;

    if ((g_broker_common_cfg->instance_type == ZPN_INSTANCE_TYPE_PUBLIC_BROKER) ||
        (g_broker_common_cfg->instance_type == ZPN_INSTANCE_TYPE_PRIVATE_BROKER)) {
        len = sizeof(zpn_zdx_https_webprobe_config_override_desc_broker_pbroker_common) / sizeof(struct zpath_config_override_desc);
        for (int i = 0; i < len; i++) {
            res = zpath_config_override_desc_register(&zpn_zdx_https_webprobe_config_override_desc_broker_pbroker_common[i]);
        }
    }

    if (g_broker_common_cfg->instance_type == ZPN_INSTANCE_TYPE_PUBLIC_BROKER) {
        len = sizeof(zpn_zdx_https_webprobe_config_override_desc_broker) / sizeof(struct zpath_config_override_desc);
        for (int i = 0; i < len; i++) {
            res = zpath_config_override_desc_register(&zpn_zdx_https_webprobe_config_override_desc_broker[i]);
        }
    } else if (g_broker_common_cfg->instance_type == ZPN_INSTANCE_TYPE_PRIVATE_BROKER) {
        len = sizeof(zpn_zdx_https_webprobe_config_override_desc_pbroker) / sizeof(struct zpath_config_override_desc);
        for (int i = 0; i < len; i++) {
            res = zpath_config_override_desc_register(&zpn_zdx_https_webprobe_config_override_desc_pbroker[i]);
        }
    } else {
        ZDX_LOG(AL_ERROR, "Unable to register zpn_zdx_https_webprobe config overrides: Invalid system type");
    }

    return res;
}

int zpn_broker_fohh_mconn_track_perf_level_register_desc()
{
    int res = ZPN_RESULT_NO_ERROR;
    int len;

    if (g_broker_common_cfg->instance_type == ZPN_INSTANCE_TYPE_PUBLIC_BROKER) {
        len = sizeof(zpn_broker_fohh_mconn_track_perf_stats_level_config_override_desc) / sizeof(struct zpath_config_override_desc);
        for (int i = 0; i < len; i++) {
            res = zpath_config_override_desc_register(&zpn_broker_fohh_mconn_track_perf_stats_level_config_override_desc[i]);
        }
    } else if (g_broker_common_cfg->instance_type == ZPN_INSTANCE_TYPE_PRIVATE_BROKER) {
        len = sizeof(zpn_pse_fohh_mconn_track_perf_stats_level_config_override_desc) / sizeof(struct zpath_config_override_desc);
        for (int i = 0; i < len; i++) {
            res = zpath_config_override_desc_register(&zpn_pse_fohh_mconn_track_perf_stats_level_config_override_desc[i]);
        }
    } else {
        ZPN_LOG(AL_ERROR, "Unable to register fohh mconn track_perf config overrides: Invalid system type");
    }

    return res;
}

int zpn_broker_get_offline_idp_cert(struct zpn_idp_cert* cert,
                                    char **odomain,
                                    char **entity_id,
                                    int64_t customer_gid,
                                    wally_response_callback_f *callback_f,
                                    void *callback_cookie,
                                    int64_t callback_id)
{
    struct zpn_ddil_config *ddil_config;
    size_t row_count = 1;
    struct zcrypt_cert *zcert;
    int res;

    res = zpn_ddil_config_get_by_customer_gid(customer_gid, &ddil_config, &row_count, callback_f, callback_cookie, callback_id);
    if (res) {
        if (res != ZPN_RESULT_NOT_FOUND) {
            ZPN_LOG(AL_ERROR, "unable to fetch ddil config error:%s", zpn_result_string(res));
        }
        return res;
    }

    if (!ddil_config->use_existing_idp) {
        if (ddil_config->idp_cert == NULL) {
            ZPN_LOG(AL_ERROR, "idp cert is null in offline config");
            return ZPN_RESULT_ERR;
        }

        zcert = zcrypt_cert_create_from_pem(ddil_config->idp_cert);
        if (!zcert) {
            ZPN_LOG(AL_ERROR, "unsble to create zcert for offline idp cert");
            return ZPN_RESULT_ERR;
        }

        cert->valid_to = zcrypt_cert_valid_to_epoch(zcert);
        cert->valid_from = zcrypt_cert_valid_from_epoch(zcert);
        cert->certificate = ddil_config->idp_cert;

        *entity_id = ddil_config->idp_entity_id;

        zcrypt_cert_free(zcert);
    }

    *odomain = ddil_config->offline_domain;

    return ZPN_RESULT_NO_ERROR;
}

static int zpn_broker_get_customer_userdb_row_default(int64_t customer_gid, int64_t userdb_id)
{
    int res = ZPN_RESULT_NO_ERROR;

    if (ZPN_BROKER_IS_PRIVATE()) {
        res = zevent_base_big_call(zpath_et_wally_userdb_get_base(),
                                   zpath_et_userdb_add,
                                   NULL,
                                   userdb_id,
                                   NULL, NULL, NULL,
                                   1);
        if (res) {
            ZPN_LOG(AL_CRITICAL, "Doing base event call to  make userdb connection failed... something is very wrong res:%s customer_gid:%"PRId64"", zpath_result_string(res), customer_gid);
        }

    }
    return res;
}

/* For PSE - config override monitor for alt authsp feature */
static void zpn_broker_ipv6_alt_authsp_monitor_callback(const int64_t *config_value, int64_t impacted_gid)
{
    struct zpn_private_broker_global_state *gs;
    static int initialized = 0;
    int enabled;
    int res = ZPN_RESULT_NO_ERROR;

    enabled = *config_value ? 1 : 0;
    ZPN_LOG(AL_NOTICE, "IPv6 Alternative authsp feature state changed to %s", enabled ? "enabled" : "disabled");
    if (!enabled) return;
    if (zpn_is_dr_mode_active()) {
        ZPN_LOG(AL_NOTICE, "IPv6 Alternative authsp feature is not supported in DR mode");
        return;
    }
    if (initialized) {
        ZPN_LOG(AL_NOTICE, "zpath_cloud table is already initialized");
        return;
    }
    /* Initializing zpath_cloud table */
    gs = zpn_get_private_broker_global_state();
    ZPN_LOG(AL_NOTICE, "Initializing zpath_cloud...");
    res = zpath_cloud_init(gs->private_broker_state->wally_ovd,
                           gs->private_broker_state->slave_db_ovd,
                           gs->private_broker_state->remote_db_ovd,
                           0,
                           0/*use zpath_table*/);
    if (res) {
        ZPN_LOG(AL_ERROR, "zpath_cloud init failed");
    } else {
        initialized = 1;
    }

    return;
}

int zpn_broker_get_app_thread_heartbeat_value(struct zpath_debug_state *request_state,
                             const char **query_values,
                             int query_value_count,
                             void *object)
{
    int app_threads_count = zpn_broker_app_calc_threads_count_get();

    for (int i = 0; i < app_threads_count; i++) {
        int thread_num = (int)app_thread_num[i];
        if (!zthread_is_valid_thread_number(thread_num)){
            ZDP("Invalid zthread num\n");
            return ZPATH_RESULT_BAD_ARGUMENT;
        }
        ZDP("Max Heartbeat Delta for thread_num: %d, thread_name: %s, is %"PRId64"\n", thread_num, zthread_get_thread_name(thread_num), zthread_get_heartbeat_override_for_thread(thread_num));
    }
    return ZPATH_RESULT_NO_ERROR;
}

/* use_itasca_logginge_port_he: Set to non-zero to override HTTP POST
 * logging with itasca logging */
static struct zpn_broker *zpn_broker_init_internal(int thread_count,
                                                   struct zcdns *zcdns,
                                                   struct zpn_common_broker_cfg *broker_cfg,
                                                   const char *logfile_path,
                                                   uint8_t dsp_channel_count,
                                                   int run_deferred,
                                                   int use_itasca_logging_port_he)
{
    struct zpath_instance *instance = NULL;
    int i;
    int res;
    static int initialized = 0;
    struct wally *private_broker_wally = NULL; /* Null for public broker */
    struct wally *private_broker_gwally = NULL; /* Null for public broker */
    struct wally *private_broker_static_wally = NULL; /* NULL for public broker */
    int64_t private_broker_gid = 0; /* 0 for public broker */
    int cfg_api_implemented = 0;
    int32_t log_connection_with_bad_sni;
    int curr_fohh_thread;
    int app_threads_count;

    struct zpn_private_broker_global_state*     gs;

    if (!broker_cfg) {
        ZPN_LOG(AL_ERROR, "NULL common broker cfg");
        return NULL;
    }

    fohh_tx_filter_history_add("zpn_client_app");
    fohh_tx_filter_history_add("zpn_client_app_complete");

    res = setup_ip_advertise_filters();
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not set up advertise filters");
        return NULL;
    }

    /* Set global config pointer now */
    g_broker_common_cfg = broker_cfg;
    if (g_broker_common_cfg->instance_type == ZPN_INSTANCE_TYPE_PUBLIC_BROKER) {
        ZPN_LOG(AL_NOTICE, "Initializing Broker");
        cfg_api_implemented = 1;
    } else if (g_broker_common_cfg->instance_type == ZPN_INSTANCE_TYPE_PRIVATE_BROKER) {
        ZPN_LOG(AL_NOTICE, "Initializing Service Edge with gid(%"PRId64")", g_broker_common_cfg->private_broker.broker_id);

        res = zpn_pbroker_data_conn_call_init_cb(g_broker_common_cfg);
        if (res) {
            ZPN_LOG(AL_ERROR, "Error zpn_pbroker_data_conns_init");
            return NULL;
        }

        zpath_debug_add_allocator(&pbroker_allocator, "pbroker");
    } else {
        return NULL;
    }

    /* spawn up termination handler thread; used in public brokers only */
    if (g_broker_common_cfg->instance_type == ZPN_INSTANCE_TYPE_PUBLIC_BROKER) {
        ZPN_LOG(AL_NOTICE, "Initializing Broker termination handler thread");
        res = zpn_broker_termination_handler_thread_init();
        if (res) {
            ZPN_LOG(AL_ERROR, "Could not initialize broker termination handler thread: %s", zpn_result_string(res));
            return NULL;
        }
    }

    zpn_transaction_collection = argo_log_create("zpn_transaction_log", NULL, NULL); /* 64K entries */
    zpn_health_collection = argo_log_create("zpn_health_log", NULL, NULL); /* 64K entries */
    zpn_auth_collection = argo_log_create("zpn_auth_log", NULL, NULL); /* 64K entries */
    zpn_ast_auth_collection = argo_log_create("zpn_ast_auth_log", NULL, NULL); /* 64K entries */
    zpn_pb_auth_collection = argo_log_create("zpn_pb_auth_log", NULL, NULL); /* 64K entries */
    zpn_sc_auth_collection = argo_log_create("zpn_sitec_auth_log", NULL, NULL); /* 64K entries */
    zpn_dns_collection = argo_log_create("zpn_dns_log", NULL, NULL); /* 64K entries */
    zpn_ast_waf_collection = argo_log_create(ZPN_WAF_LOG, NULL, NULL); /* 64K entries */
    zpn_ast_ptag_collection = argo_log_create(ZPN_PTAG_LOG, NULL, NULL); /* 64K entries */
    zpn_ast_waf_api_collection = argo_log_create(ZPN_API_LOG, NULL, NULL); /* 64K entries */
    zpn_auth_state_collection = argo_log_create("zpn_auth_state_log", NULL, NULL); /* 64K entries */
    zia_health_collection = argo_log_create("zpn_zia_health", NULL, NULL); /* 64K entries */
    zpn_ast_app_inspection_collection = argo_log_create(ZPN_APP_INSPECTION_LOG, NULL, NULL); /* 64K entries */
    zpn_ast_krb_inspection_collection = argo_log_create(ZPN_KRB_INSPECTION_LOG, NULL, NULL); /* 64K entries */
    zpn_ast_ldap_inspection_collection = argo_log_create(ZPN_LDAP_INSPECTION_LOG, NULL, NULL); /* 64K entries */
    zpn_ast_smb_inspection_collection = argo_log_create(ZPN_SMB_INSPECTION_LOG, NULL, NULL); /* 64K entries */

    if (!zpn_transaction_collection ||
        !zpn_health_collection ||
        !zpn_auth_collection ||
        !zpn_ast_auth_collection ||
        !zpn_pb_auth_collection ||
        !zpn_sc_auth_collection ||
        !zpn_dns_collection ||
        !zpn_ast_waf_collection ||
        !zpn_ast_waf_api_collection ||
        !zpn_ast_app_inspection_collection ||
        !zpn_ast_krb_inspection_collection ||
        !zpn_ast_ldap_inspection_collection ||
        !zpn_ast_smb_inspection_collection ||
        !zpn_ast_ptag_collection ||
        !zpn_auth_state_collection ||
        !zia_health_collection) {
        ZPN_LOG(AL_ERROR, "Could not create transaction logs");
        return NULL;
    }

    if (g_broker_common_cfg->instance_type == ZPN_INSTANCE_TYPE_PUBLIC_BROKER) {
        admin_probe_collection = argo_log_create("admin_probe_log", NULL, NULL); /* 64K entries */
        if (!admin_probe_collection) {
            ZPN_LOG(AL_ERROR, "Could not create transaadmin_probe_loglogs");
            return NULL;
        }
    }

    if (g_broker_common_cfg->instance_type == ZPN_INSTANCE_TYPE_PUBLIC_BROKER) {
        zpn_meta_transaction_collection = argo_log_create("zpn_meta_transaction_log", NULL, NULL); /* 64K entries */
        if (!zpn_meta_transaction_collection) {
            ZPN_LOG(AL_ERROR, "Could not create meta transaction logs");
            return NULL;
        }
    }

    if (g_broker_common_cfg->instance_type == ZPN_INSTANCE_TYPE_PUBLIC_BROKER) {
        zpn_np_collection = argo_log_create("zpn_np_log", NULL, NULL); /* 64K entries */
        if (!zpn_np_collection) {
            ZPN_LOG(AL_ERROR, "Could not create np logs");
            return NULL;
        }
    }

    res = zse_init(zpn_is_dr_mode_active());
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not init zse");
        return NULL;
    }
    zpath_debug_add_allocator(&zse_allocator, "zse");

    res = zpe_init(1);
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not init zpe");
        return NULL;
    }
    zpath_debug_add_allocator(&zpe_allocator, "zpe");

    if (__sync_fetch_and_add_4((&initialized), 1) == 0) {
        if (zpn_fohh_worker_state_init(zpath_service_broker) != ZPN_RESULT_NO_ERROR) {
            return NULL;
        }
    }

    if (logfile_path) {
        if (do_log_file(zpn_transaction_collection, "zpn_transaction_log_file", logfile_path, "transaction.log")) {
            ZPN_LOG(AL_ERROR, "Could not create transaction reader");
        }
        if (do_log_file(zpn_auth_collection, "zpn_auth_log_file", logfile_path, "auth.log")) {
            ZPN_LOG(AL_ERROR, "Could not create auth_log reader");
        }
        if (do_log_file(zpn_ast_auth_collection, "zpn_ast_auth_log_file", logfile_path, "ast_auth.log")) {
            ZPN_LOG(AL_ERROR, "Could not create ast_auth_log reader");
        }
        if (do_log_file(zpn_pb_auth_collection, "zpn_pb_auth_log_file", logfile_path, "pb_auth.log")) {
            ZPN_LOG(AL_ERROR, "Could not create pb_auth_log reader");
        }
        if (do_log_file(zpn_sc_auth_collection, "zpn_sc_auth_log_file", logfile_path, "sc_auth.log")) {
            ZPN_LOG(AL_ERROR, "Could not create sc_auth_log reader");
        }
        if (do_log_file(zpn_health_collection, "zpn_health_log_file", logfile_path, "health.log")) {
            ZPN_LOG(AL_ERROR, "Could not create health_log reader");
        }
        if (do_log_file(zia_health_collection, "zpn_zia_health", logfile_path, "zia_health.log")) {
            ZPN_LOG(AL_ERROR, "Could not create zia_health_collection reader");
        }
        if (do_log_file(zpn_dns_collection, "zpn_dns_log_file", logfile_path, "dns.log")) {
            ZPN_LOG(AL_ERROR, "Could not create dns_log reader");
        }
        if (do_log_file(zpn_ast_waf_collection, ZPN_WAF_LOG, logfile_path, "waf.log")) {
            ZPN_LOG(AL_ERROR, "Could not create waf_log reader");
        }
        if (do_log_file(zpn_ast_waf_api_collection, ZPN_API_LOG, logfile_path, "api.log")) {
            ZPN_LOG(AL_ERROR, "Could not create api_log reader");
        }
        if (do_log_file(zpn_ast_app_inspection_collection, ZPN_APP_INSPECTION_LOG, logfile_path, "app_inspection.log")) {
            ZPN_LOG(AL_ERROR, "Could not create app_inspection_log reader");
        }
        if (do_log_file(zpn_ast_krb_inspection_collection, ZPN_KRB_INSPECTION_LOG, logfile_path, "krb_inspection.log")) {
            ZPN_LOG(AL_ERROR, "Could not create krb_inspection_log reader");
        }
        if (do_log_file(zpn_ast_ldap_inspection_collection, ZPN_LDAP_INSPECTION_LOG, logfile_path, "ldap_inspection.log")) {
            ZPN_LOG(AL_ERROR, "Could not create ldap_inspection_log reader");
        }
        if (do_log_file(zpn_ast_smb_inspection_collection, ZPN_SMB_INSPECTION_LOG, logfile_path, "smb_inspection.log")) {
            ZPN_LOG(AL_ERROR, "Could not create smb_inspection_log reader");
        }
        if (do_log_file(zpn_ast_ptag_collection, ZPN_PTAG_LOG, logfile_path, "ptag.log")) {
            ZPN_LOG(AL_ERROR, "Could not create ptag_log reader");
        }
        if (do_log_file(zpn_auth_state_collection, "zpn_auth_state_log_file", logfile_path, "auth_state.log")) {
            ZPN_LOG(AL_ERROR, "Could not create auth_state_log reader");
        }
        if (g_broker_common_cfg->instance_type == ZPN_INSTANCE_TYPE_PUBLIC_BROKER) {
            if (do_log_file(zpn_meta_transaction_collection, "zpn_meta_transaction_log_file", logfile_path, "meta_transaction.log")) {
                ZPN_LOG(AL_ERROR, "Could not create meta transaction reader");
            }
            if (do_log_file(admin_probe_collection, "admin_probe_log_file", logfile_path, "admin_probe.log")) {
                ZPN_LOG(AL_ERROR, "Could not create admin probe reader");
            }
            if (do_log_file(zpn_np_collection, "zpn_np_log_file", logfile_path, "np.log")) {
                ZPN_LOG(AL_ERROR, "Could not create np_log reader");
            }
        }
    }

    if (cfg_api_implemented) {
        ZPN_LOG(AL_NOTICE, "Initializing siem");
        res = zpn_siem_init();
        if (res) {
            ZPN_LOG(AL_ERROR, "Could not initialize siem");
            return NULL;
        }

        ZPN_LOG(AL_NOTICE, "Initializing siem complete");

        ZPN_LOG(AL_NOTICE, "Initializing zpn_broker_siem_init");


        zpn_broker_log_stats_event_base = event_base_new();

        pthread_t siem_stats_thread;
        res = zthread_create(&siem_stats_thread,
                                zpn_broker_log_stats_thread,
                                NULL,
                                "zpn_broker_log_stats_thread",
                                60,
                                16*1024*1024,
                                60*1000*1000,
                                NULL);
        if (res) {
            ZPN_LOG(AL_ERROR, "Could not initialize broker log stats thread");
            return NULL;
        }

        res = zpn_broker_siem_init(zpn_broker_log_stats_event_base);
        if (res) {
            ZPN_LOG(AL_ERROR, "Could not initialize broker siem");
            return NULL;
        }
        ZPN_LOG(AL_NOTICE, "Initializing zpn_broker_siem_init complete");
    }

    srand(time(NULL)); // Seed the random number generator with current time

    res = zsaml_init();
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not init zsaml");
        return NULL;
    }

    if (zpath_debug_add_allocator(&zsaml_allocator, "zsaml")) {
        ZPN_LOG(AL_ERROR, "Could not add zsaml allocator");
        return NULL;
    }


    // FIXME: Ramesh/Kanti/Mansoor: This code has to be refactored to exist once and only once
    // It is currently duplicated in the private broker init.
    //
    if (g_broker_common_cfg->instance_type == ZPN_INSTANCE_TYPE_PUBLIC_BROKER) {
        res = zpn_rpc_init();
        if (res) {
            ZPN_LOG(AL_ERROR, "Could not register RPCs");
            return NULL;
        }
    }

    memset(&broker, 0, sizeof(broker));

    broker.thread_count = thread_count;

    for (i = 0; i < ZPN_BROKER_BUCKETS; i++) {
        struct zpn_broker_bucket *bucket = &(broker.buckets[i]);
        pthread_mutexattr_t mattr;

        pthread_mutexattr_init(&mattr);
        pthread_mutexattr_settype(&mattr, PTHREAD_MUTEX_RECURSIVE);
        pthread_mutex_init(&(bucket->lock), &mattr);

        bucket->reaped_start_us = 0;
        bucket->reaped_max_count = 0;
        bucket->reaped_max_null_count = 0;

        bucket->mtunnel_by_id = argo_hash_alloc(7, 1);
        if (!bucket->mtunnel_by_id) {
            ZPN_LOG(AL_CRITICAL, "Could not allocate hash table");
            return NULL;
        }
        bucket->f_conn_by_tunnel_id = argo_hash_alloc(7, 1);
        if (!bucket->f_conn_by_tunnel_id) {
            ZPN_LOG(AL_CRITICAL, "Could not allocate hash table");
            return NULL;
        }
        TAILQ_INIT(&(bucket->mtunnel_list));
        TAILQ_INIT(&(bucket->reaped_list));
    }

    for (curr_fohh_thread = 0; curr_fohh_thread < FOHH_MAX_THREADS; curr_fohh_thread++) {
        broker.cgid_to_cstate_per_thread.customer_gid[curr_fohh_thread] = zhash_table_alloc(&zpn_allocator);
        if (!broker.cgid_to_cstate_per_thread.customer_gid[curr_fohh_thread]) {
            ZPN_LOG(AL_CRITICAL, "No memory for customer gid hash table thread %d", curr_fohh_thread);
            return NULL;
        }
    }

    broker.clients.lock = (pthread_mutex_t)PTHREAD_MUTEX_INITIALIZER;
    broker.clients.tun_ids = argo_hash_alloc(7, 1);
    if (!broker.clients.tun_ids) {
        ZPN_LOG(AL_CRITICAL, "No memory for connected client hash table");
        return NULL;
    }

    broker.clients.cnames = argo_hash_alloc(7, 1);
    if (!broker.clients.cnames) {
        ZPN_LOG(AL_CRITICAL, "No memory for connected client hash table (cnames)");
        return NULL;
    }

    broker.clients.client_fqdns = argo_hash_alloc(7, 1);
    if (!broker.clients.client_fqdns) {
        ZPN_LOG(AL_CRITICAL, "No memory for client_fqdns hash table (client_fqdns)");
        return NULL;
    }

    broker.clients.client_fqdns_per_customer = argo_hash_alloc(7, 1);
    if (!broker.clients.client_fqdns_per_customer) {
        ZPN_LOG(AL_CRITICAL, "No memory for client_fqdns_per_customer hash table (client_fqdns_per_customer)");
        return NULL;
    }

    broker.clients.zvpn_ctrl_lock = (pthread_mutex_t)PTHREAD_MUTEX_INITIALIZER;
    broker.clients.zvpn_ctrl_conns =  argo_hash_alloc(7, 1);
    if (!broker.clients.zvpn_ctrl_conns) {
        ZPN_LOG(AL_CRITICAL, "No memory for zvpn_ctrl_conns hash table");
        return NULL;
    }

    broker.clients.zvpn_data_lock = (pthread_mutex_t)PTHREAD_MUTEX_INITIALIZER;
    broker.clients.zvpn_data_conns =  argo_hash_alloc(7, 1);
    if (!broker.clients.zvpn_data_conns) {
        ZPN_LOG(AL_CRITICAL, "No memory for zvpn_data_conns hash table");
        return NULL;
    }

    broker.clients.ip_ranges = zhash_table_alloc(&zpn_ipars_allocator);
    if (!broker.clients.ip_ranges) {
        ZPN_LOG(AL_CRITICAL, "No memory for c2c_ip_ranges hash table");
        return NULL;
    }

    broker.clients.np_subnets = zhash_table_alloc(&np_allocator);
    if (!broker.clients.np_subnets) {
        ZPN_LOG(AL_CRITICAL, "No memory for np_subnets hash table");
        return NULL;
    }

    /* Initialize broker mtunnel free queue */
    zpn_broker_mtunnel_init();
    zpn_broker_mtunnel_free_q_init();

    zpn_broker_c_state_free_q_init();
    zpn_broker_a_state_free_q_init();
    zpn_broker_a_state_active_q_init();
    if (zpn_transform_stats_init() != ZPN_RESULT_NO_ERROR) {
        ZPN_LOG(AL_CRITICAL, "Failed to init transform stats");
        return NULL;
    }

    if (zpn_pipeline_stats_init() != ZPN_RESULT_NO_ERROR) {
        ZPN_LOG(AL_CRITICAL, "Failed to init pipeline stats");
        return NULL;
    }

    if (g_broker_common_cfg->instance_type == ZPN_INSTANCE_TYPE_PRIVATE_BROKER) {
        /* Extract private broker values, these will be null for public broker */
        private_broker_wally = g_broker_common_cfg->private_broker.wally;
        private_broker_gwally = g_broker_common_cfg->private_broker.gwally;
        private_broker_gid = g_broker_common_cfg->private_broker.broker_id;

        ZPN_BROKER_ASSERT_HARD(private_broker_wally != NULL, "Broker Init: Private broker wally is NULL!");
        ZPN_BROKER_ASSERT_HARD(private_broker_gwally != NULL, "Broker Init: Private broker gwally is NULL!");
        ZPN_BROKER_ASSERT_HARD(private_broker_gid != 0, "Broker Init: Private broker gid is 0!");
    }

    ZPN_DEBUG_STARTUP("Initializing zpath_config_override...");

    res = zpath_config_override_init(private_broker_gwally,
                                     ZPATH_GID_GET_CUSTOMER_GID(private_broker_gid), 0,
                                     (g_broker_common_cfg->instance_type == ZPN_INSTANCE_TYPE_PUBLIC_BROKER
                                      ? config_component_broker
                                      : config_component_pbroker));
    if (res) {
        ZPN_LOG(AL_ERROR, "zpath_config_override failed: %s", zpath_result_string(res));
        return NULL;
    } else {
        /* Enable partition based config override lookups as well.. */
        zpath_config_override_init_partition_support(zpath_is_logical_partition_enabled, zpath_partition_get_instance_partitions, zpath_partition_gid_from_customer_gid);
    }

    if (g_broker_common_cfg->instance_type == ZPN_INSTANCE_TYPE_PRIVATE_BROKER) {
        int64_t is_enabled = 0;
        gs = zpn_get_private_broker_global_state();
        is_static_wally_enabled = (int)zpath_config_override_get_config_int(ZPN_PSE_REGISTRATION_WALLY_ENABLED,
                                                                            &is_enabled,
                                                                            ZPN_PSE_REGISTRATION_WALLY_ENABLED_DEFAULT,
                                                                            (int64_t)g_broker_common_cfg->private_broker.broker_id,
                                                                            (int64_t)g_broker_common_cfg->private_broker.customer_id,
                                                                            (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                                            (int64_t)0);

        if (is_static_wally_enabled) {
            res = zpn_private_broker_static_wally_init(gs->private_broker_state,
                                                       gs->cfg_key_shard,
                                                       pbroker_get_redir_cloud_name(),
                                                       NULL,
                                                       g_broker_common_cfg->private_broker.use_sqlt,
                                                       zpn_is_dr_mode_active(),
                                                       gs->cfg_key_cloud);

            if (res) {
                ZPN_LOG(AL_CRITICAL, "Unable to initilize private broker state for static wally for customer %"PRId64", on %"PRId64", res=%s",
                                     g_broker_common_cfg->private_broker.customer_id, g_broker_common_cfg->private_broker.broker_id, zpn_result_string(res));
                return NULL;
            }

            ZPN_LOG(AL_NOTICE, "private broker state for static wally is initialized for customer %"PRId64", on %"PRId64,
                               g_broker_common_cfg->private_broker.customer_id, g_broker_common_cfg->private_broker.broker_id);
        } else {
            ZPN_LOG(AL_NOTICE, "private broker static wally is disabled for customer %"PRId64", on %"PRId64,
                               g_broker_common_cfg->private_broker.customer_id, g_broker_common_cfg->private_broker.broker_id);
        }
        g_broker_common_cfg->private_broker.static_wally = gs->private_broker_state->static_wally;
        private_broker_static_wally = g_broker_common_cfg->private_broker.static_wally;
        if (is_static_wally_enabled) ZPN_BROKER_ASSERT_HARD(private_broker_static_wally != NULL, "Broker Init: Private broker static_wally is NULL!");
    }

    zpn_register_config_override_policy_override_desciptions(g_broker_common_cfg->instance_type);

    res = zpn_broker_config_override_desc_register_all(g_broker_common_cfg->instance_type);
    if(res){
        ZPN_LOG(AL_ERROR, "Error in zpn_broker_config_override_desc_register_all res = %s",zpn_result_string(res));
        return NULL;
    }

    register_app_scaling_config_ovd_descriptions();

    if (ZPN_BROKER_IS_PUBLIC()) {
        /* GID = 1 = global default */
        zpath_config_override_monitor_int(CONFIG_FOHH_LOG_MAX_IN_FLIGHT,
                                          &fohh_log_max_logs_in_flight,
                                          NULL,
                                          FOHH_LOG_MAX_LOGS_IN_FLIGHT_DEFAULT,
                                          (int64_t)ZPATH_INSTANCE_GID,
                                          (int64_t)ZPATH_GID_GET_CUSTOMER_GID(ZPATH_INSTANCE_GID),
                                          (int64_t)1,
                                          (int64_t)0);
    }

    /* Initialize alt-cloud support and overrides */
    zpn_broker_alt_cloud_support_init();
    /* Initialize broker hard disabled config overrides */
    zpn_broker_hard_disabled_feature_flag_init();

    if(ZPN_BROKER_IS_PUBLIC()) {
        fohh_conn_burst_value = zpn_broker_zapp_get_idle_tunnel_burst_value();
        /* setup monitor to track the dynamic value of burst counter */
        zpn_broker_zapp_idle_tunnel_burst_value_monitor();
        res = zpn_broker_dispatch_stats_init();
        if (res) {
            ZPN_LOG(AL_ERROR, "zpn_broker_dispatch_stats_init failed: %s", zpath_result_string(res));
            return NULL;
        }
    }

    res = zpn_cache_init(zpn_broker_is_dev_environment());
    if (res) {
        ZPN_LOG(AL_ERROR, "zpn_cache init failed: %s", zpath_result_string(res));
        return NULL;
    }
    /*
     * Init dispatcher pools before customer, as there is a dependency
     * on dispatcher pool from customer (custom pools)
     */
    int init_pool = ZPN_BROKER_IS_PRIVATE() ? 0 : 1;
    int init_local_disp = ZPN_BROKER_IS_PRIVATE() ? 1 : 0;
    res = zpn_broker_dispatch_init(init_pool, init_local_disp, dsp_channel_count, 0);
    if (res) {
        ZPN_LOG(AL_CRITICAL, "Could not initialize dispatching code");
        return NULL;
    }

    if (ZPN_BROKER_IS_PUBLIC()) {
        res = zpn_broker_client_neg_path_cache_init();
        if (res) {
            ZPN_LOG(AL_ERROR, "Could not init negative path cache on broker: %s", zpath_result_string(res));
            return NULL;
        }
    }

    if (g_broker_common_cfg->instance_type == ZPN_INSTANCE_TYPE_PUBLIC_BROKER) {
        instance = zpath_instance_global_state.current_config;

        if (!instance) {
            ZPN_LOG(AL_CRITICAL, "No instance configuration for broker_init");
            return NULL;
        }

        g_broker_common_cfg->public_broker.broker_id = broker_instance_id = instance->gid;

        if (instance->ips_count == 0) {
            ZPN_LOG(AL_WARNING, "No IP's configured for broker");
        }

        ZPN_DEBUG_STARTUP("Initializing domain_lookup...");
        res = zpath_domain_lookup_init(zpath_global_wally,
                                       zpath_global_slave_db,
                                       zpath_global_remote_db);
        if (res) {
            ZPN_LOG(AL_ERROR, "zpath_domain_lookup_init failed: %s",
                    zpath_result_string(res));
            return NULL;
        }

        ZPN_DEBUG_STARTUP("Initializing et_zone...");
        res = zpath_et_zone_init(zpath_global_wally,
                                 zpath_global_slave_db,
                                 zpath_global_remote_db);
        if (res) {
            ZPN_LOG(AL_ERROR, "zpath_et_zone_init failed: %s",
                    zpath_result_string(res));
            return NULL;
        }

        ZPN_DEBUG_STARTUP("Initializing et_customer_zone...");
        res = zpath_et_customer_zone_init(NULL, 0);
        if (res) {
            ZPN_LOG(AL_ERROR, "zpath_et_customer_zone_init failed: %s",
                    zpath_result_string(res));
            return NULL;
        }

        ZPN_DEBUG_STARTUP("Initializing et_service_endpoint...");
        res = zpath_et_service_endpoint_init(zpath_global_wally,
                                             zpath_global_slave_db,
                                             zpath_global_remote_db,
                                             use_itasca_logging_port_he);
        if (res) {
            ZPN_LOG(AL_ERROR, "zpath_et_service_endpoint_init failed: %s",
                    zpath_result_string(res));
            return NULL;
        }

        if (use_itasca_logging_port_he) {

            res = zpath_debug_add_write_command("Send duplicate transaction logs",
                                          "/broker/log/natural/repeats",
                                          zpath_debug_update_natural_logs_repeat_count,
                                          NULL,
                                          "count", "The number of times (non-zero +ve) to transmit a log to C-PG",
                                          NULL);
            if (res) {
                ZPN_LOG(AL_ERROR, "Failed to add debug command /broker/log/natural/repeats");
                return NULL;
            }
        }

        ZPN_DEBUG_STARTUP("Initializing zpn_customer_config...");
        res = zpn_customer_config_init(NULL, 0, 0 /* no fully load */, 0);
        if (res) {
            ZPN_LOG(AL_ERROR, "zpn_customer_config_init failed: %s",
                    zpath_result_string(res));
            return NULL;
        }

    } else if (g_broker_common_cfg->instance_type == ZPN_INSTANCE_TYPE_PRIVATE_BROKER) {
        /* zpath_app_init will initialize a lot of tables for us... We
         * need to do the equivalent initialization work here for the
         * private broker (only). */
        ZPN_DEBUG_STARTUP("Initializing zpath_customer table");
        res = zpath_customer_init(private_broker_wally, private_broker_gid, 0 /* no fully load */, 0);
        if (res != ZPATH_RESULT_NO_ERROR) {
            ZPN_LOG(AL_ERROR, "Could not initialize zpath_customer table");
            return NULL;
        }

        ZPN_DEBUG_STARTUP("Initializing zpath_domain_lookup");
        res = zpath_domain_lookup_init_single_tenant(private_broker_gid);
        if (res != ZPATH_RESULT_NO_ERROR) {
            ZPN_LOG(AL_ERROR, "Could not initialize zpath_domain_lookup");
            return NULL;
        }

        ZPN_DEBUG_STARTUP("Initializing et_customer_zone...");
        res = zpath_et_customer_zone_init(private_broker_gwally, ZPATH_GID_GET_CUSTOMER_GID(private_broker_gid));
        if (res) {
            ZPN_LOG(AL_ERROR, "zpath_et_customer_zone_init failed: %s", zpath_result_string(res));
            return NULL;
        }

        ZPN_DEBUG_STARTUP("Initializing zpn_customer_config...");
        res = zpn_customer_config_init(private_broker_wally, ZPATH_GID_GET_CUSTOMER_GID(private_broker_gid), 0 /* no fully load */, 0);
        if (res) {
            ZPN_LOG(AL_ERROR, "zpn_customer_config_init failed: %s", zpath_result_string(res));
            return NULL;
        }
    }

    ZPN_DEBUG_STARTUP("Initializing zpn_client_tracker...");
    res = zpn_client_tracker_init();
    if (res) {
        ZPN_LOG(AL_ERROR, "zpn_client_tracker failed: %s", zpath_result_string(res));
        return NULL;
    }

    res = zpn_bc_init(private_broker_wally, private_broker_gid, 0 /* no fully load */, 0, 1);
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not init zpn_bc_init");
        return NULL;
    }

    res = zpn_location_init(private_broker_wally, private_broker_gid, 0 /* no fully load */, 0);
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not init zpn_location_init");
        return NULL;
    }

    res = zpn_workload_tag_group_init(private_broker_wally, private_broker_gid, 0, 0, 1);
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not init zpn_workload_tag_group_init");
        return NULL;
    }
    zpn_broker_policy_workload_tag_grp_hard_disabled_init();

    ZPN_DEBUG_STARTUP("Initializing et_translate");
    res = et_translate_init_wally(private_broker_gwally);
    if (res != ZPATH_RESULT_NO_ERROR) {
        ZPN_LOG(AL_ERROR, "et_translate_init_wally failed: %s", zpath_result_string(res));
        return NULL;
    }

    ZPN_DEBUG_STARTUP("Initializing zpn_application table");
    res = zpn_application_init(private_broker_wally, ZPATH_GID_GET_CUSTOMER_GID(private_broker_gid),
                               1 /* We are a broker */,
                               1 /* This works for both private and public broker */,
                               0 /* Register with zpath_table */,
                               1 /* support pattern match */);
    if (res != ZPATH_RESULT_NO_ERROR) {
        ZPN_LOG(AL_ERROR, "Could not initialize zpn_application table");
        return NULL;
    }

    if (zpath_debug_add_allocator(&zpn_app_allocator, "zpn_app")) {
        ZPN_LOG(AL_ERROR, "Could not add zpn_app allocator");
        return NULL;
    }

    ZPN_DEBUG_STARTUP("Initializing zpn_application_domain table");
    res = zpn_application_domain_init(private_broker_wally, private_broker_gid, 0);
    if (res != ZPATH_RESULT_NO_ERROR) {
        ZPN_LOG(AL_ERROR, "Could not initialize zpn_application_domain table");
        return NULL;
    }

    if (geoip_db_file) {
        if (!private_broker_wally) {
            res = zpath_geoip_init(geoip_db_file);
            if (res) {
                ZPN_LOG(AL_ERROR, "zpath_geoip_init failed: %s",
                        zpath_result_string(res));
                return NULL;
            }
        }
    }

    if (geoip_isp_db_file) {
        if (private_broker_wally) {
            ZPATH_LOG(AL_CRITICAL, "Implement me: geoip isp DB");
        } else {
            res = zpath_ispip_init(geoip_isp_db_file);
            if (res) {
                ZPN_LOG(AL_ERROR, "zpath_ispip_init failed: %s",
                        zpath_result_string(res));
            }
        }
    }

    if (private_broker_wally) {
        /* We are trying to make it so private broker does not need
         * geoip- it will rely on public wally for that function */
        //ZPATH_LOG(AL_CRITICAL, "Implement me: geoip override");
    } else {
        res = et_geoip_override_init(zpath_global_wally, zpath_global_slave_db,
                                     zpath_global_remote_db);
        if (res) {
            ZPN_LOG(AL_ERROR, "et_geoip_override_init failed: %s",
                    zpath_result_string(res));
            return NULL;
        }
    }

    app_thread_stats_log = argo_log_get("statistics_log");

    /* Create a couple threads for processing client download policy */
    if (private_broker_wally) {
        if (use_custom_app_thread) {
            app_threads_count = zpn_pse_app_calc_threads_count_get_and_update();
        } else {
            app_threads_count = PSE_DEFAULT_APP_THREADS;
        }
        ZPN_LOG(AL_NOTICE, "PSE starting with %d app threads", app_threads_count);
    } else {
        app_threads_count = zpn_broker_app_calc_threads_count_get();
        ZPN_LOG(AL_NOTICE, "Broker starting with %d app threads", app_threads_count);
    }

    app_threads_count = zpn_broker_app_calc_threads_count_get();
    app_thread_num = (int64_t*)ZPN_CALLOC(app_threads_count * sizeof(*app_thread_num));
    if (app_thread_num == NULL) {
        ZPN_LOG(AL_CRITICAL, "App thread num allocation failure");
        return NULL;
    }

    g_app_thread_hb_override_time = zpn_broker_app_thread_heartbeat_time_override_get_value();
    for (i = 0; i < app_threads_count; i++) {
        struct zevent_base *z_base;
        char str[100];
        snprintf(str, sizeof(str), "app_thread_%d", i);
        z_base = zevent_handler_create(str, 16*1024*1024, g_app_thread_hb_override_time);
        if (!z_base) {
            fprintf(stdout,"ERROR: Could not create zevent...\n");
            fflush(stdout);
            sleep(1);
            zpn_broker_exit_handler(zpath_tc_exit, 1, zthread_get_self_thread_id());
        }
        zevent_add_to_class(z_base, ZEVENT_CLASS_APP_CALC);

        struct app_thread_stats *z_stats = ZPN_CALLOC(sizeof(*z_stats));
        struct argo_structure_description *app_thread_stats_description
                 = argo_register_global_structure(APP_THREAD_STATS_HELPER);
        char name[250];
        snprintf(name, sizeof(name), "%s_stats", str);
        argo_log_register_structure(app_thread_stats_log,
                                    name,
                                    AL_INFO,
                                    60*1000*1000,    /* 1 minute */
                                    app_thread_stats_description,
                                    z_stats,
                                    0,
                                    fill_app_thread_stats,
                                    z_base);
        app_thread_num[i] = z_base->thread_num;
    }
    zpn_broker_app_thread_heartbeat_time_override_init();

    res = zpn_server_group_init(private_broker_wally, private_broker_gid, 0 /* no fully load */, 0);
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not init zpn_server_group table");
        return NULL;
    }

    res = zpn_server_group_assistant_group_init(private_broker_wally, private_broker_gid, 0 /* no fully load */, 0);
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not init zpn_server_group_assistant_group table");
        return NULL;
    }

    res = zpn_assistant_group_init(private_broker_wally, private_broker_gid, 0 /* no fully load */, 0);
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not init zpn_assistant_group table");
        return NULL;
    }

    res = zpn_assistantgroup_assistant_relation_init(private_broker_wally, private_broker_gid, 0 /* no fully load */, 0);
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not init zpn_assistantgroup_asssistant_relation table");
        return NULL;
    }

    res = zpn_assistant_table_init(private_broker_wally, private_broker_gid, 0 /* no fully load */, 0);
    if (res) {
        ZPN_LOG(AL_CRITICAL, "Could not init assistant table");
        return NULL;
    }

    res = zpn_cbi_mapping_table_init(private_broker_wally, private_broker_gid, 0 /* no fully load */, 0);
    if (res) {
        ZPN_LOG(AL_CRITICAL, "Could not init zpn_cbi_mapping table");
        return NULL;
    }

    res = zpn_cbi_profile_table_init(private_broker_wally, private_broker_gid, 0 /* no fully load */, 0);
    if (res) {
        ZPN_LOG(AL_CRITICAL, "Could not init zpn_cbi_profile table");
        return NULL;
    }

    res = zpn_private_broker_table_init(private_broker_wally, private_broker_gid, 0 /* no fully load */, 0, zpn_private_broker_row_callback);
    if (res) {
        ZPN_LOG(AL_CRITICAL, "Could not init private broker table");
        return NULL;
    }

    res = zpn_idp_cert_init(private_broker_wally, private_broker_gid, ZPN_BROKER_IS_PRIVATE() ? 1 : 0, 0);
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not init zpn_idp_cert table");
        return NULL;
    }

    res = zpn_idp_init(private_broker_wally, private_broker_gid, ZPN_BROKER_IS_PRIVATE() ? 1 : 0, 0, NULL);
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not init zpn_idp table");
        return NULL;
    }

    if( ZPN_BROKER_IS_PUBLIC() ||
        (ZPN_BROKER_IS_PRIVATE() && !zpn_is_dr_mode_active())) {
        res = zpn_ddil_config_init(private_broker_wally, private_broker_gid, zpn_broker_ddil_config_row_callback, ZPN_BROKER_IS_PRIVATE() ? 1 : 0, 0);
        if (res) {
            ZPN_LOG(AL_CRITICAL, "Could not init zpn_ddil_config_init table");
            return NULL;
        }

        zpn_idp_cert_set_offline_cert_fn(zpn_broker_get_offline_idp_cert);

        res = zpn_firedrill_site_init(private_broker_wally, private_broker_gid, zpn_private_broker_firedrill_row_callback, 0, 1);
        if (res) {
            ZPN_LOG(AL_CRITICAL, "Could not init zpn_site_firedrill table");
            return NULL;
        }

        res = zpn_site_init(private_broker_wally, private_broker_gid, zpn_site_row_callback, ZPN_BROKER_IS_PRIVATE() ? 1 : 0, 0);
        if (res) {
            ZPN_LOG(AL_CRITICAL, "Could not init zpn_site table");
            return NULL;
        }

        res = zpn_sitec_to_group_table_init(private_broker_wally, private_broker_gid, NULL, ZPN_BROKER_IS_PRIVATE() ? 1 : 0, 0);
        if (res) {
            ZPN_LOG(AL_CRITICAL, "Could not init zpn_site_controller_to_group table");
            return NULL;
        }

        res = zpn_sitec_group_init(private_broker_wally, private_broker_gid, zpn_sitec_group_row_callback, ZPN_BROKER_IS_PRIVATE() ? 1 : 0, 0);
        if (res) {
            ZPN_LOG(AL_CRITICAL, "Could not init zpn_site_controller_group table");
            return NULL;
        }

        res = zpn_sitec_table_init(private_broker_wally, private_broker_gid, zpn_site_controller_row_callback, ZPN_BROKER_IS_PRIVATE() ? 1 : 0, 0);
        if (res) {
            ZPN_LOG(AL_CRITICAL, "Could not init zpn_site_controller table");
            return NULL;
        }

        res = zpn_sitec_version_init(private_broker_wally, private_broker_gid, 0, 0);
        if (res) {
            ZPN_LOG(AL_CRITICAL, "Could not init zpn_site_controller_version table");
            return NULL;
        }
    }

    res = zpn_pbroker_group_init(private_broker_wally, private_broker_gid, zpn_private_broker_group_row_callback, 1/* Fully loaded */, 0);
    if (res) {
        ZPN_LOG(AL_CRITICAL, "Could not init private broker group table");
        return NULL;
    }

    res = zpn_pbroker_to_group_table_init(private_broker_wally, private_broker_gid, NULL, 1/* Fully loaded */, 0);
    if (res) {
        ZPN_LOG(AL_CRITICAL, "Could not init private broker group mapping table");
        return NULL;
    }

    res = zpn_privatebrokergroup_trustednetwork_mapping_table_init(private_broker_wally, private_broker_gid, 0);
    if (res) {
        ZPN_LOG(AL_CRITICAL, "Could not init private broker group trusted network mapping table");
        return NULL;
    }

    res = zpn_machine_table_init(private_broker_wally, private_broker_gid, 0 /* no fully load */, 0);
    if (res) {
        ZPN_LOG(AL_CRITICAL, "Could not init machine table");
        return NULL;
    }

    res = zpn_machine_group_table_init(private_broker_wally, private_broker_gid, 0 /* no fully load */, 0);
    if (res) {
        ZPN_LOG(AL_CRITICAL, "Could not init machine group table");
        return NULL;
    }

    res = zpn_machine_to_group_table_init(private_broker_wally, private_broker_gid, 0 /* no fully load */, 0);
    if (res) {
        ZPN_LOG(AL_CRITICAL, "Could not init machine group mapping table");
        return NULL;
    }

    if (ZPN_BROKER_IS_PUBLIC()) {
        zpath_service_init();
    }

    /* Make sure we have read our config at least once before
     * continuing initialization */
    if (private_broker_gid) {
        struct zpn_private_broker *pb = NULL;
        ZPN_DEBUG_STARTUP("Reading private broker config...");
        do {
            res = zpn_private_broker_get_by_id_wait(private_broker_gid, &pb);
            if (res) {
                if (res != ZPN_RESULT_NOT_FOUND) {
                    ZPN_LOG(AL_NOTICE, "...waiting for private broker config... (%s)", zpn_result_string(res));
                } else {
                    ZPN_LOG(AL_NOTICE, "...waiting for private broker config...");
                }
                sleep(1);
            }
        } while (res != ZPN_RESULT_NO_ERROR);
        ZPN_DEBUG_STARTUP("Reading private broker config... complete");
        /* Set group related config */
        do {
            res = zpn_private_broker_set_group_cfg(private_broker_gid);
            if (res) {
                ZPN_DEBUG_STARTUP("waiting to set private broker group cfg %s", zpn_result_string(res));
                sleep(1);
            }
        } while (res != ZPN_RESULT_NO_ERROR);

        /* Populate the scope gid field in the global wally state */
        g_broker_common_cfg->private_broker.scope_id = pb->scope_gid;
        gs = zpn_get_private_broker_global_state();
        gs->private_broker_scope_id = pb->scope_gid;
        zpn_pbroker_alt_cloud_support_init();

        struct zpn_private_broker_group *group;
        res = zpn_pbroker_group_get_by_gid(g_broker_common_cfg->private_broker.pb_group_id,
                                            &group,
                                            1,
                                            NULL,
                                            NULL,
                                            0);
        if (res) {
            ZPN_LOG(AL_CRITICAL, "For pb: %"PRId64"and group: %"PRId64" failed to fetch private broker group error: %s",
                                    private_broker_gid,
                                    g_broker_common_cfg->private_broker.pb_group_id,
                                    zpn_result_string(res));
        } else {
            if(gs->alt_cloud_feature_state_initialised && gs->alt_cloud_feature_state) {
                zpn_pbroker_group_verify_alt_cloud_name_change(group->alt_cloud);
            }

            if (!zpn_is_dr_mode_active()) {
                zpn_private_broker_site_notify_cfg_site_gid(group->site_gid);
            }
        }
    }

    res = zpn_assistant_version_init(private_broker_wally, private_broker_gid, 0 /* no fully load */, 0);
    if (res) {
        ZPN_LOG(AL_CRITICAL, "Could not init assistant_version table");
        return NULL;
    }

    res = zpn_app_group_relation_init(private_broker_wally, private_broker_gid, 0 /* no fully load */, 0);
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not init zpn_app_group_relation table");
        return NULL;
    }

    res = zpn_servergroup_server_relation_init(private_broker_wally, private_broker_gid, 0 /* no fully load */, 0);
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not init zpn_servergroup_server_relation table");
        return NULL;
    }

    res = zpn_application_server_init(private_broker_wally, private_broker_gid, 0 /* no fully load */, 0);
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not init zpn_app_server table");
        return NULL;
    }

    zpn_broker_cryptosvc_mutex_init();

    res = zpn_private_broker_version_init(private_broker_wally, private_broker_gid, 0 /* no fully load */, 0);
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not init zpn_private_broker_version_init table");
        return NULL;
    }

    res = zpn_sub_module_upgrade_init(private_broker_wally, private_broker_gid, 0 /* no fully load */, 0);
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not init zpn_sub_module_upgrade table");
        return NULL;
    }

    if(!ZPN_BROKER_IS_PRIVATE()) {
        res = zpn_rule_to_pse_group_init(private_broker_wally, private_broker_gid, 0 /* no fully load */, 0);
        if (res) {
            ZPN_LOG(AL_ERROR, "Could not init zpn_rule_to_pse_group_init table. Error:%s", zpath_result_string(res));
            return NULL;
       }
    }

    res = zpn_svcp_profile_init(private_broker_wally, private_broker_gid, 0 /* no fully load */, 0);
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not init zpn_svcp_profile table. Error:%s", zpath_result_string(res));
        return NULL;
    }

    res = zpn_rule_to_step_up_auth_level_mapping_init(private_broker_wally, private_broker_gid, 0 /* no fully load */, 0);
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not init zpn_rule_to_step_up_auth_level_mapping_init table. Error:%s", zpath_result_string(res));
        return NULL;
    }

    res = zpn_step_up_auth_level_init(private_broker_wally, private_broker_gid, 0 /* no fully load */, 0);
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not init zpn_step_up_auth_level_init table. Error:%s", zpath_result_string(res));
        return NULL;
    }

    res = zpn_customer_resiliency_settings_init(private_broker_wally, ZPATH_GID_GET_CUSTOMER_GID(private_broker_gid), 0 /* no fully load */, 0);
    if (res) {
            ZPN_LOG(AL_ERROR,"Could not init private broker resiliency settings: %s",
                                zpn_result_string(res));
            return NULL;
    }

    if (private_broker_wally) {
        //res = zpn_signing_cert_init(private_broker_wally, private_broker_gid, NULL);
        res = zpn_signing_cert_init(private_broker_wally, private_broker_gid, broker_cfg->private_broker.pkey_mem, ZPN_BROKER_IS_PRIVATE() ? 1 : 0, 0);
        if (res) {
            ZPN_LOG(AL_ERROR, "Could not init zpn_signing_cert table");
            return NULL;
        }

    } else {
        res = zpn_signing_cert_init(private_broker_wally, private_broker_gid, NULL, ZPN_BROKER_IS_PRIVATE() ? 1 : 0, 0);
        if (res) {
            ZPN_LOG(AL_ERROR, "Could not init zpn_signing_cert table");
            return NULL;
        }
    }

    res = zpn_issuedcert_init(private_broker_wally, private_broker_gid, 0 /* no fully load */, 0);
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not init zpn_issuedcert table");
        return NULL;
    }

    /*
     * init znf tables
     */
    res = zpn_znf_init(private_broker_wally, private_broker_gid, 0 /* no fully load */, 0);
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not init zpn_znf table") ;
        return NULL;
    }

    res = zpn_znf_group_init(private_broker_wally, private_broker_gid, 0 /* no fully load */, 0);
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not init zpn_znf_group table");
        return NULL;
    }

    res = zpn_znf_to_group_init(private_broker_wally, private_broker_gid, 0 /* no fully load */, 0);
    if (res) {
       ZPN_LOG(AL_ERROR, "Could not init zpn_znf_to_group table") ;
       return NULL;
    }

    if (!private_broker_wally) {
        zpn_balance_set_notify_cb(zpn_broker_balance_control_notify_cb);
        /* This should happen before zpn_broker_load_init and zpn_private_broker_load_init !!*/
        res = zpn_balance_redirect_init(zpath_global_wally,
                                   zpath_global_slave_db,
                                   zpath_global_remote_db,
                                   broker_instance_id);
        if (res) {
            ZPN_LOG(AL_ERROR, "Could not perform zpn_balance_redirect_init");
            return NULL;
        }
    }

    res = zpn_private_broker_load_init(private_broker_wally, private_broker_gid, 0 /* no fully load */, 0, zpn_balance_inst_load_update_pbroker);
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not init zpn_private_broker_load table");
        return NULL;
    }

    /* Register vdi client auth stats structure */
    argo_log_register_structure(argo_log_get("statistics_log"),
                        "zpn_vdi_client_auth_stats",
                        AL_INFO,
                        1*60*1000*1000, /* 1 mins interval */
                        zpn_broker_vdi_auth_stats_description,
                        &zpn_vdi_auth_stats,
                        1,
                        NULL,
                        NULL);

    res = zpath_debug_add_read_command("VDI client stats",
                                "/zpn/broker/client/vdi_stats",
                                zpn_broker_client_vdi_debug_stats,
                                NULL,
                                NULL);
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not init /zpn/broker/client/vdi_stats debug command");
        return NULL;
    }

    if (!private_broker_wally) {

        res = zpn_broker_load_init(zpath_global_wally,
                                   zpath_global_slave_db,
                                   zpath_global_remote_db);
        if (res) {
            ZPN_LOG(AL_ERROR, "Could not init zpn_broker_load table");
            return NULL;
        }

        res = zpn_public_broker_stats_init();
        if (res) {
            ZPN_LOG(AL_ERROR, "Could not init public broker stats");
            return NULL;
        }
        /* Register latency stats and debug command */
        argo_log_register_structure(argo_log_get("statistics_log"),
                            "zpn_broker_latency_probe_stats",
                            AL_INFO,
                            1*60*1000*1000, /* 1 mins interval */
                            zpn_broker_client_latency_probe_stats_description,
                            &zpn_lat_chk_stats,
                            1,
                            NULL,
                            NULL);
        res = zpath_debug_add_read_command("Broker latency stats",
                                    "/zpn/broker/latency_stats",
                                    zpn_broker_latency_stats,
                                    NULL,
                                    NULL);
        if (res) {
            ZPN_LOG(AL_ERROR, "Could not init /zpn/broker/latency_stats debug command");
            return NULL;
        }

        /* Register SVCP stats and debug command */
        argo_log_register_structure(argo_log_get("statistics_log"),
                            "zpn_svcp_stats",
                            AL_INFO,
                            5*60*1000*1000, /* 5 mins interval */
                            zpn_svcp_stats_description,
                            &zpn_svcp_chk_stats,
                            1,
                            NULL,
                            NULL);

        /* Register Stepup auth stats and debug command */
        argo_log_register_structure(argo_log_get("statistics_log"),
                            "zpn_stepup_auth_stats",
                            AL_INFO,
                            5*60*1000*1000, /* 5 mins interval */
                            zpn_stepup_auth_stats_description,
                            &zpn_stepup_chk_stats,
                            1,
                            NULL,
                            NULL);
    } else {
        ZPN_DEBUG_STARTUP("Implement (if necessary) broker load table loading. We may not want this information going into private brokers...");
    }

    /* Curl command for cert status */
    res = zpath_debug_add_read_command("Lifetime of broker's certificate",
                                  "/zpn/broker/cert_lifetime",
                                  zpn_broker_cert_lifetime,
                                  NULL,
                                  "json", "Output in JSON",
                                  NULL);
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not init /zpn/broker/cert_lifetime debug command");
        return NULL;
    }

    /*Curl command for svcp stats*/
    res = zpath_debug_add_read_command("Dump SVCP stats",
                                  "/zpn/broker/svcp_stats",
                                  zpn_broker_client_svcp_stats,
                                  NULL,
                                  NULL);
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not init /zpn/broker/svcp_stats debug command");
        return NULL;
    }

    /*Curl command for stepup auth stats*/
    res = zpath_debug_add_read_command("Dump Stepup Auth stats",
                                  "/zpn/broker/stepup_stats",
                                  zpn_broker_client_stepup_auth_dump_stats,
                                  NULL,
                                  NULL);
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not init /zpn/broker/stepup_stats debug command");
        return NULL;
    }

    zpn_register_app_tune_desc();
    zpn_broker_get_app_buffer_parameters();

    res = zpath_debug_add_read_command("Broker load",
                                  "/zpn/broker/load",
                                  zpn_broker_debug_load,
                                  NULL,
                                  "format",  "<optional> format=json, or plain text otherwise",
                                  NULL);
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not init /zpn/broker/load debug command");
        return NULL;
    }

    res = zpath_debug_add_read_command("Broker alt cloud",
                                  "/zpn/broker/alt_cloud",
                                  zpn_broker_print_alt_cloud,
                                  NULL,
                                  NULL);
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not init /zpn/broker/alt_cloud debug command");
        return NULL;
    }

    if(ZPN_BROKER_IS_PUBLIC()) {
        res = zpath_debug_add_read_command("Broker logical partitioning feature status",
                                  "/zpn/broker/lp_status",
                                  zpn_broker_lp_status,
                                  NULL,
                                  "format",  "<optional> format=json, or plain text otherwise",
                                  NULL);
        if (res) {
            ZPN_LOG(AL_ERROR, "Could not init /zpn/broker/lp_status debug command");
            return NULL;
        }

        zpn_broker_zhm_stats_description = argo_register_global_structure(ZPN_BROKER_ZHM_STATS_HELPER);
        if (!zpn_broker_zhm_stats_description) {
            ZPN_LOG(AL_ERROR, "Cannot register ZHM stats description");
            return NULL;
        }

        if (!argo_log_register_structure(argo_log_get("statistics_log"),
                                         "zpn_broker_zhm_stats",
                                         AL_INFO,
                                         15*1000*1000, /*15 sec*/
                                         zpn_broker_zhm_stats_description,
                                         &zhm_stats,
                                         1,
                                         NULL,
                                         NULL)) {
            ZPN_LOG(AL_ERROR, "Could not setup ZHM periodic stats");
            return NULL;
        }

        res = zpath_debug_add_read_command("Send ZHM stats via broker statslog",
                                      "/zpn/broker/zhm_upload_stats",
                                      zpn_broker_zhm_upload_stats,
                                      &zhm_stats,
                                      "all", "Total number of queries received by ZHM",
                                      "lookups", "Total number of lookups done by ZHM",
                                      "ok", "Total number of queries with success result",
                                      "fail", "Total number of queries with failure result",
                                      "msg_id", "Sequence id of this message",
                                      "zpm_connects", "Number of connections successfully made to ZPM",
                                      NULL);
        if (res) {
            ZPN_LOG(AL_ERROR, "Could not init /zpn/broker/zhm_upload_stats debug command");
            return NULL;
        }

        zpn_broker_zpm_abrt_kill_stats_description = argo_register_global_structure(ZPN_BROKER_ZPM_ABRT_KILL_STATS_HELPER);
        if (!zpn_broker_zpm_abrt_kill_stats_description) {
            ZPN_LOG(AL_ERROR, "Cannot register ZPM abort and kill stats description");
            return NULL;
        }

        if (!argo_log_register_structure(argo_log_get("statistics_log"),
                                         "zpn_broker_zpm_abrt_kill_stats",
                                         AL_INFO,
                                         900*1000*1000, /* 15 minutes */
                                         zpn_broker_zpm_abrt_kill_stats_description,
                                         &zpm_abrt_kill_stats,
                                         1,
                                         NULL,
                                         NULL)) {
            ZPN_LOG(AL_ERROR, "Could not setup ZPM periodic abort and kill stats");
            return NULL;
        }

        res = zpath_debug_add_read_command("Send ZPM stats via broker statslog",
                                      "/zpn/broker/zpm_upload_stats",
                                      zpn_broker_zpm_upload_abrt_kill_stats,
                                      &zpm_abrt_kill_stats,
                                      "sig_abrt", "Total number of sig_abrt signal sent by ZPM",
                                      "sig_kill", "Total number of sig_kill signal sent by ZPM",
                                      "msg_id", "Sequence id of this message",
                                      NULL);
        if (res) {
            ZPN_LOG(AL_ERROR, "Could not init /zpn/broker/zpm_upload_stats debug command");
            return NULL;
        }
    }

    res = zpath_debug_add_diagnostic_cb("Broker load",
                                      "/zpn/broker/load",
                                      zpn_broker_diag_load,
                                      NULL);
    if (res) {
            ZPN_LOG(AL_ERROR, "Could not register broker load diagnositc callback");
            return NULL;
    }

    res = zpn_scope_init(private_broker_wally, private_broker_gid, 0, 0);
    if(res) {
        ZPN_LOG(AL_CRITICAL, "Could not init zpn_scope table");
        return NULL;
    }


    /* Static Wally Feature */
    if (is_static_wally_enabled) {
        res = zpn_client_table_init_static(private_broker_static_wally, private_broker_gid, 1 /*Static Wally*/, 0 /* no fully load */, 0);
        if (res) {
            ZPN_LOG(AL_ERROR, "Could not init zpn_client table for static wally");
            return NULL;
        }
    } else {
        res = zpn_client_table_init_static(private_broker_wally, private_broker_gid, 0, 0, 0);
        if (res) {
            ZPN_LOG(AL_ERROR, "Could not init zpn_client table for regular  wally");
            return NULL;
        }
    }

    res = zpn_shared_customer_domain_init(private_broker_wally, private_broker_gid, 0 /* no fully load */, 0);
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not init zpn_shared_customer_domain table");
        return NULL;
    }

    res = zpn_application_group_application_mapping_init(private_broker_wally, private_broker_gid, 1, 1, 0);
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not init zpn_application_group_application_mapping table");
        return NULL;
    }

    res = zpn_application_group_init(private_broker_wally, private_broker_gid, 0 /* no fully load */, 0);
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not init zpn_application_group table");
        return NULL;
    }

#if 0
    res = zpn_broker_load_init(NULL, 0);
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not init zpn_broker_load table");
        return NULL;
    }
#endif

    res = zpn_posture_profile_init(private_broker_wally, private_broker_gid, 0 /* no fully load */, 0);
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not init zpn_posture_profile table");
        return NULL;
    }

    res = zpn_trusted_network_init(private_broker_wally, private_broker_gid, 0 /* no fully load */, 0);
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not init zpn_trusted_network table");
        return NULL;
    }

    ZPN_LOG(AL_NOTICE, "Initializing zpn_c2c_client_registration...");
    res = zpn_c2c_client_registration_init(private_broker_wally, private_broker_gid, 0 /* no fully load */, 0, NULL);
    if (res) {
        ZPN_LOG(AL_CRITICAL, "Could not init zpn_c2c_client_registration_init table");
        return NULL;
    }

    /*
     * Spawn health monitoring thread...
     */
    res = zpn_broker_health_init(NULL);
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not initialize broker health monitoring: %s", zpn_result_string(res));
        return NULL;
    }

    if(ZPN_BROKER_IS_PUBLIC()) {
        /* Initializing Version Control */
        res = zpn_version_control_init(broker_timer_base,
                                    zpath_global_wally,
                                    zpath_global_slave_db,
                                    zpath_global_remote_db);
        if (res) {
            ZPN_LOG(AL_ERROR, "Could not perform zpn_version_control_init - %s",zpn_result_string(res));
            return NULL;
        }
    }
    /*
     * Spawn private broker site monitoring thread...
     */
    if (ZPN_BROKER_IS_PRIVATE()) {
        res = zpn_private_broker_site_init();
        if (res) {
            ZPN_LOG(AL_ERROR, "Could not initialize private broker site monitoring: %s", zpn_result_string(res));
            return NULL;
        }
    }

    /* Update broker status once */
    zpn_broker_load_notify_status_change(INITIALIZING);

    /*
     * Initialize the aggregate store framework
     */
    res = zpn_session_aggregate_store_init();
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not initialize aggregate store framework: %s", zpn_result_string(res));
        return NULL;
    }

    /*
     * Initialize the session store
     */
    res = zpn_session_postures_init();
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not initialize posture store: %s", zpn_result_string(res));
        return NULL;
    }

    /*
     * Initialize SCIM Data Tables
     * ORDER IS IMPORTANT!
     * 1. Init each table inside the userdb that we need an index for
     * 2. Init the et_userdb as the row callbacks require all the index information from 1 to function
     */
    res = zpn_scim_user_attribute_init();
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not initialize scim_user_attribute table: %s", zpn_result_string(res));
        return NULL;
    }

    res = zpn_scim_group_init();
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not initialize scim_group table: %s", zpn_result_string(res));
        return NULL;
    }

    res = zpn_scim_user_group_init();
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not initialize scim_user_group table: %s", zpn_result_string(res));
        return NULL;
    }

    res = zpn_scim_user_init();
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not initialize scim_user table: %s", zpn_result_string(res));
        return NULL;
    }

    res = zpn_user_risk_init();
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not initialize user_risk table: %s", zpn_result_string(res));
        return NULL;
    }

    res = zpn_aae_init();
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not initialize aae tables : %s", zpn_result_string(res));
        return NULL;
    }

    ZPN_LOG(AL_NOTICE, "Initializing et_userdb...");
    if (ZPN_BROKER_IS_PRIVATE())
        res = zpath_et_userdb_init(private_broker_gwally, 0, ZPATH_GID_GET_CUSTOMER_GID(private_broker_gid));
    else
        res = zpath_et_userdb_init(zpath_global_wally, 1, 0);

    if (res) {
        ZPN_LOG(AL_ERROR, "zpath_et_userdb_init failed: %s", zpath_result_string(res));
        return NULL;
    }

    ZPN_LOG(AL_NOTICE, "Initializing zpath_et_customer_userdb...");
    res = zpath_et_customer_userdb_init(private_broker_gwally,
                                        ZPATH_GID_GET_CUSTOMER_GID(private_broker_gid),
                                        zpn_broker_get_customer_userdb_row_default);
    if (res) {
        ZPN_LOG(AL_ERROR, "Initializing zpath_et_customer_userdb failed: %s", zpath_result_string(res));
        return NULL;
    }

    /* Initialize policy related tables */
    res = zpn_policy_set_init(private_broker_wally, private_broker_gid, 0 /* no fully load */, 0);
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not initialize zpn policy set table: %s", zpn_result_string(res));
        return NULL;
    }

    res = zpn_rule_init(private_broker_wally, private_broker_gid, 0 /* no fully load */, 0);
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not initialize zpn rule table: %s", zpn_result_string(res));
        return NULL;
    }

    res = zpn_rule_condition_set_init(private_broker_wally, private_broker_gid, 0 /* no fully load */, 0);
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not initialize zpn condition set table: %s", zpn_result_string(res));
        return NULL;
    }

    res = zpn_rule_condition_operand_init(private_broker_wally, private_broker_gid, 0 /* no fully load */, 0);
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not initialize zpn condition operand table: %s", zpn_result_string(res));
        return NULL;
    }

    res = zpn_rule_to_server_group_init(private_broker_wally, private_broker_gid, 0 /* no fully load */, 0);
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not initialize zpn_rule_to_server_group table: %s", zpn_result_string(res));
        return NULL;
    }

    res = zpn_rule_to_assistant_group_init(private_broker_wally, private_broker_gid, 0 /* no fully load */, 0);
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not initialize zpn_rule_to_assistant_group table: %s", zpn_result_string(res));
        return NULL;
    }

    res = zpn_rule_to_location_init(private_broker_wally, private_broker_gid);
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not initialize zpn_rule_to_location table: %s", zpn_result_string(res));
        return NULL;
    }

    res = zpn_rule_to_location_group_init(private_broker_wally, private_broker_gid);
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not initialize zpn_rule_to_location_group table: %s", zpn_result_string(res));
        return NULL;
    }

    res = zpn_location_group_to_location_init(private_broker_wally, private_broker_gid);
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not initialize zpn_location_group_to_location table: %s", zpn_result_string(res));
        return NULL;
    }

    res = zpn_saml_attrs_init(private_broker_wally, private_broker_gid, 0 /* no fully load */, 0);
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not initialize zpn saml attr table: %s", zpn_result_string(res));
        return NULL;
    }

    res = zpn_scim_attr_header_init(private_broker_wally, private_broker_gid, 0);
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not initialize zpn scim attr table: %s", zpn_result_string(res));
        return NULL;
    }

    res = zpn_broker_client_apps_init(private_broker_wally, private_broker_gid, DEFAULT_RECALC_PUSH_US, zpn_is_dr_mode_active());
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not init zpn_broker_client_apps");
        return NULL;
    }

    if (zpath_debug_add_allocator(&zpn_broker_client_allocator, "zpn_bca")) {
        ZPN_LOG(AL_ERROR, "Could not add zpn_bca allocator");
        return NULL;
    }

    if (zpath_debug_add_allocator(&zpn_broker_client_app_cache_allocator, "zpn_client_app_cache")) {
        ZPN_LOG(AL_ERROR, "Could not add zpn_client_app_cache allocator");
        return NULL;
    }

    if (ZPN_BROKER_IS_PRIVATE()){
        if (zpath_debug_add_allocator(&zpn_file_fetch_allocator, "zpn_file_fetch")) {
            ZPN_LOG(AL_ERROR, "Could not add zpn_bca allocator");
            return NULL;
        }
    }

    res = zpn_approval_init(private_broker_wally, private_broker_gid, 0 /* no fully load */, 0);
    if (res) {
      ZPN_LOG(AL_ERROR, "Could not init zpn_approval table: %s", zpn_result_string(res));
        return NULL;
    }


    res = zpn_approval_mapping_init(private_broker_wally, private_broker_gid, 0 /* no fully load */, 0);
    if (res) {
      ZPN_LOG(AL_ERROR, "Could not init zpn_approval_mapping table: %s", zpn_result_string(res));
        return NULL;
    }

    if (!zpn_is_dr_mode_active()) {
        ZPN_LOG(AL_NOTICE, "Initializing IPARS client...");
        res = zpn_broker_ipars_client_init();
        if (res) {
            ZPN_LOG(AL_CRITICAL, "Could not init zpn ipars client");
            return NULL;
        }
    }

    res = zpath_debug_add_allocator(&zpn_ipars_allocator, "zpn_ipars");
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not add zpn_ipars allocator: %s", zpn_result_string(res));
        return NULL;
    }

    ZPN_LOG(AL_NOTICE, "Initializing zpn_c2c_ip_ranges");
    res = zpn_c2c_ip_ranges_init(private_broker_wally, private_broker_gid, 0 /* no fully load */, 0);
    if (res) {
        ZPN_LOG(AL_CRITICAL, "Could not init zpn_c2c_ip_ranges table");
        return NULL;
    }

    /* PSE doesn't support NP as of datapath-39 */
    if (ZPN_BROKER_IS_PUBLIC()) {
        ZPN_LOG(AL_NOTICE, "Initializing network presence...");
        res = np_init(zpath_instance_global_state.current_config->gid);
        if (res) {
            ZPN_LOG(AL_CRITICAL, "Could not init network presence");
            return NULL;
        }
        res = zpn_broker_np_init();
        if (res) {
            ZPN_LOG(AL_CRITICAL, "Could not init network presence broker");
            return NULL;
        }
    }

    if (ZPN_BROKER_IS_PUBLIC()) {
        ZPN_LOG(AL_NOTICE, "Initializing zia inspection");
        res = zins_init_inspection(Z_FALSE, Z_TRUE);
        if (res) {
            ZPN_LOG(AL_CRITICAL, "Could not init zia inspection");
            return NULL;
        }

        ZPN_LOG(AL_NOTICE, "Initializing extranets");
        res = eas_init();
        if (res) {
            ZPN_LOG(AL_CRITICAL, "Could not init extranets");
            return NULL;
        }


    }
    zpn_broker_extranet_hard_disable_init();

    if (!private_broker_gid) {
        res = zpn_client_less_init(NULL, NULL);
        if (res) {
            ZPN_LOG(AL_ERROR, "Could not init zpn_client_less");
            return NULL;
        }
    }

    if(res) {
        ZPN_LOG(AL_ERROR, "Could not add the debug interface /debug/zpn/broker/policy_cache: %s", zpn_result_string(res));
        // do NOT return NULL in case of error ...
    }

    zpn_icmp_rate_limit_init();

    if (zpn_icmp_debug_interface_init()) {
        ZPN_LOG(AL_ERROR, "icmp debug interface init failed !");
        return NULL;
    }

    res = zpath_debug_add_read_command("Dump SIEM debugging state",
                                  "/siem/dump",
                                  zpn_dump_siem,
                                  NULL,
                                  "gid", "Optional. The SIEM GID to dump",
                                  "cid", "Optional. The customer GID for context dump",
                                  "summary", "Optional. Summarizes SIEM TX contexts",
                                  "stats", "Optional: Dump stats JSON object",
                                  "frozen", "Optional: Dump all TX states which are in frozen state currently",
                                  NULL);

    res = zpath_debug_add_write_command("Watch SIEM debugging state",
                                  "/siem/watch",
                                  zpn_watch_siem,
                                  NULL,
                                  "gid", "The SIEM GID to toggle watching in logs",
                                  "setting", "Set the SIEM GID watch to 0 or 1",
                                  NULL);


    if(res) {
        ZPN_LOG(AL_ERROR, "Could not add the debug interface /debug/zpn/broker/policy_cache: %s", zpn_result_string(res));
        // do NOT return NULL in case of error ...
    }

    if (zpn_mconn_fohh_tlv_init_debug()) {
        ZPN_LOG(AL_ERROR, "Could not initialize debug command for fohh_tlv");
    }

    setup_fohh_conn_setup_timeout();
    setup_broker_client_auth_complete_timeout();
    setup_broker_client_ip_anchor_rx_data_timeout();
    setup_broker_disable_flow_control_mtr();
    setup_malloc_fastbin();
    setup_dont_dump();

    if(ZPN_BROKER_IS_PUBLIC()) {
        /* Update full instance name based on feature flag and alt-cloud name */
        zpath_instance_update_full_instance_name(broker_alt_cloud_support_enabled);

        /* update the instance name for argo logging */
        //argo_log_update_instance_name(zpath_instance_global_state.instance_full_name);
    }

    res = zpn_customer_init(); /* must happen before listening for clients */
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not zpn_customer_init()");
        return NULL;
    }

    /* Listening for data should only occur after all our other
     * systems have been initialized. (Arriving connections make use
     * of that stuff we have initialized) */

    /*
     * Create an SNI dispatching server.
     * Turn off the logging for a connection hitting this SNI server with bad SNI in pbroker case. In pbroker case it
     * will be noisy as there will be multiple pbroker with the same IP address in the pbroker load table and only
     * one will be reachable to the connector. So the connector will try that IP with all the possible SNI to try its
     * luck.
     */
    log_connection_with_bad_sni = (ZPN_INSTANCE_TYPE_PUBLIC_BROKER == g_broker_common_cfg->instance_type) ? 1 : 0;
    sni_server = fohh_generic_server_create(1, log_connection_with_bad_sni);
    if (!sni_server) {
        ZPN_LOG(AL_ERROR, "Could not create generic SNI dispatching server");
        return NULL;
    }

    res = zpath_debug_add_fohh_generic_server(sni_server, "broker");
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not add generic_server sni debugger: %s", zpn_result_string(res));
        return NULL;
    }

    res = zpn_broker_proxy_init();
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not zpn_broker_proxy_init: %s", zpn_result_string(res));
        return NULL;
    }

    /* Should always setup dtls */
    {
        int first_thread;
        int last_thread;

        ZPN_LOG(AL_INFO, "zrdt/dtls is enabled for broker");
        broker.mux = zdtls_mux_create();
        if (!broker.mux) {
            ZPN_LOG(AL_ERROR, "Cannot create mux for DTLS connections");
            return NULL;
        }

        res = fohh_worker_pool_exists(FOHH_WORKER_ZPN_DTLS, &first_thread, &last_thread);
        if (res != ZPN_RESULT_NO_ERROR) {
            first_thread = 0;
            last_thread = thread_count - 1;
        }

        ZPN_LOG(AL_DEBUG, "DTLS threads: [%d - %d]", first_thread, last_thread);

        for (i = first_thread; i <= last_thread; i++) {
            zdtls_mux_add_to_ev_base(broker.mux, fohh_get_thread_event_base(i), fohh_get_thread(i));
        }

        zrdt_set_thread_range(first_thread, last_thread);
    }

    res = zpn_broker_client_path_cache_global_init(0);
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not init path cache global");
        return NULL;
    }

    res = zpn_broker_client_path_cache_cfg_init(0);
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not init path cache cfg");
        return NULL;
    }

    res = zpn_broker_assistant_init(g_broker_common_cfg->instance_type);
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not init assistant component: %s", zpn_result_string(res));
        return NULL;
    }

    if (g_broker_common_cfg->instance_type == ZPN_INSTANCE_TYPE_PUBLIC_BROKER) {

        res = zpn_broker_pbroker_init();
        if (res) {
            ZPN_LOG(AL_ERROR, "Could not init pbroker component: %s", zpn_result_string(res));
            return NULL;
        }

        res = zpn_broker_pbroker_listen(sni_server);
        if (res) {
            ZPN_LOG(AL_ERROR, "Could not listen for private brokers: %s", zpn_result_string(res));
            return NULL;
        }

        res = zpn_broker_sitec_init();
        if (res) {
            ZPN_LOG(AL_ERROR, "Could not init sitec component: %s", zpn_result_string(res));
            return NULL;
        }

        res = zpn_broker_sitec_listen(sni_server);
        if (res) {
            ZPN_LOG(AL_ERROR, "Could not listen for site controller: %s", zpn_result_string(res));
            return NULL;
        }
    }

    /* SNI proxy domains initialization */
    res = zpn_broker_add_known_sni_proxy_domains(zcdns, sni_server);
    if (res) {
        ZPN_LOG(AL_ERROR, "alt_cloud: Known Proxy domain registration did not complete successfully!");
        return NULL;
    }

    if (g_broker_common_cfg->instance_type == ZPN_INSTANCE_TYPE_PUBLIC_BROKER) {
        struct zpath_proxy_sni_args args;
        args.sni_server = sni_server;
        args.zcdns = zcdns;
        res = zpath_broker_proxy_sni_init(zpath_global_wally,
                                        zpath_global_slave_db,
                                        zpath_global_remote_db,
                                        zpn_broker_proxy_update,
                                        &args);
        if (res) {
            ZPN_LOG(AL_ERROR, "Could not zpn_broker_proxy_sni_init()");
            return NULL;
        }

        zpath_broker_proxy_authenticated_sni_init(&args, zpn_broker_proxy_auth_sni_feature_toggle);

        res = zpath_debug_add_read_command("List all active sni proxy in broker",
                                "/zpn/broker/sni_proxy",
                                zpn_broker_proxy_sni_list_cb,
                                NULL,
                                NULL);
        if (res) {
            ZPATH_LOG(AL_ERROR, "Failed to add /zpn/broker/sni_proxy debug command");
            return NULL;
        }
    }

    res = zpn_broker_svcp_init();
    if (res) {
        return NULL;
    }

    res = zpn_broker_client_init_debug();
    if (res) {
        return NULL;
    }

    if (g_soft_assert_argo_reg_post_listen) {
        /*
        * There should not be any more argo structure registration past this point -
        * we are starting to listen for clients.
        */
        argo_structure_set_registration_barrier(1, broker_structure_registration_after_barrier_set_cb);
    }

    /*
    * Moved fohh_generic_server_listen part to zpn_broker_socket_listen_client_init() which is called once
    * all the other components of broker are done with their init. This will prevent ZCC to connect to broker as
    * 443/8443 ports are not attached to any IPs while broker init is in progress hence avoiding any race condition to occur.
    */

    if (zpn_debug_add_allocators()) {
        ZPN_LOG(AL_ERROR, "Could not add zpn allocators");
        return NULL;
    }

    res = zpn_transaction_log_init_throttling();
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not init zpn_transaction_log_init_throttling_error_code");
        return NULL;
    }
    if(ZPN_BROKER_IS_PUBLIC()){
        res = zpn_natural_log_error_codes_throttle_init();
        if(res){
            ZPN_LOG(AL_ERROR, "Could not init natural_log_throttling_data_table");
            return NULL;
        }
        res = zpn_natural_log_error_codes_stats_init();
        if(res){
            ZPN_LOG(AL_ERROR, "Could not init zpn_natural_log_throttle_stats_tbl");
            return NULL;
        }
    }
    // init broker2broker transit support
    bt_init_broker_transit();

    /* We assume assistant common name is gid+domain */
    res = zpath_debug_add_write_command("Turn on/off assistant log upload.",
                                  "/broker/assistant/upload_log",
                                  zpn_broker_assistant_log_upload_toggle,
                                  NULL,
                                  "cn", "The common name of the assistant",
                                  NULL);
    if (res) {
        return NULL;
    }

    res = zpath_debug_add_write_command("Turn on/off assistant stats upload.",
                                  "/broker/assistant/upload_stats",
                                  zpn_broker_assistant_stats_upload_toggle,
                                  NULL,
                                  "cn", "The common name of the assistant",
                                  NULL);
    if (res) {
        return NULL;
    }

    if (g_broker_common_cfg->instance_type == ZPN_INSTANCE_TYPE_PUBLIC_BROKER) {
        res = zpath_debug_add_admin_command("Restart connector.",
                                    "/broker/assistant/restart",
                                    zpn_broker_assistant_restart,
                                    NULL,
                                    "cn", "The common name of the assistant",
                                    NULL);
        if (res) {
            return NULL;
        }
    }

    res = zpath_debug_add_write_command("Turn on/off assistant debug flag.",
                                  "/broker/assistant/debug_flag",
                                  zpn_broker_assistant_debug_flag,
                                  NULL,
                                  "cn", "The common name of the assistant",
                                  "flag", "The name of the debug flag",
                                  NULL);
    if (res) {
        return NULL;
    }

    res = zpath_debug_add_write_command("Specify the app to track.",
                                  "/tune/zpn/trackapp",
                                  zpn_broker_tune_track_application,
                                  NULL,
                                  "app_gid", "The gid of application to track",
                                  NULL);
    if (res) {
        return NULL;
    }

    res = zpath_debug_add_write_command("Specify the user to track.",
                                  "/tune/zpn/trackusr",
                                  zpn_broker_tune_track_usr,
                                  NULL,
                                  "usr_gid", "The gid of user whose application to track",
                                  NULL);
    if (res) {
        return NULL;
    }

    res = zpath_debug_add_read_command("Specify the user to track.",
                                  "/tune/zpn/show",
                                  zpn_broker_tune_show,
                                  NULL,
                                  NULL);
    if (res) {
        return NULL;
    }

    res = zpath_debug_add_admin_command("Send the assistant a learn frame",
                                  "/zpn/broker/learn",
                                  zpn_broker_send_assistant_learn_frame,
                                  NULL,
                                  "g_ast", "Global assistant id",
                                  "g_app", "Global application ID",
                                  "g_app_grp", "Global application group ID",
                                  "g_ast_grp", "Global assistant group ID",
                                  "g_srv_grp", "Global server group ID",
                                  "s_port", "Service port",
                                  "domain", "Domain",
                                  "app_type", "App type",
                                  "disc_type", "Discovery message type - NEW/RENEW/CONN_REGISTERED",
                                  "disc_msg_time", "Fake time the dispatcher sent this message",
                                  "lifetime_s", "lifetime for which health report to be sent by connector",
                                  NULL);

    if (res) {
        return NULL;
    }

    res = zpath_debug_add_write_command("Send the assistant a dns check request",
                                  "/zpn/broker/assistant/dns_check",
                                  zpn_broker_assistant_send_dns_check_dbg,
                                  NULL,
                                  "g_ast", "Global assistant id",
                                  "g_app", "Global application ID",
                                  "name", "Domain name",
                                  "type", "A or AAAA or SRV",
                                  "g_dsp", "Global dispatcher id",
                                  NULL);

    if (res) {
        return NULL;
    }

    if (g_broker_common_cfg->instance_type == ZPN_INSTANCE_TYPE_PUBLIC_BROKER) {
        res = zpath_debug_add_write_command("Turn on/off private broker debug flag.",
                                      "/broker/pbroker/debug_flag",
                                      zpn_broker_pbroker_debug_flag,
                                      NULL,
                                      "cn", "The common name of the private broker (format: <pb_gid>.pb.<CLOUD>)",
                                      "flag", "The name of the debug flag",
                                      NULL);
        if (res) {
            return NULL;
        }

        res = zpath_debug_add_write_command("Turn on/off site controller debug flag.",
                                      "/broker/sitec/debug_flag",
                                      zpn_broker_sitec_debug_flag,
                                      NULL,
                                      "cn", "The common name of the sitec (format: <sc_gid>.sc.<CLOUD>)",
                                      "flag", "The name of the debug flag",
                                      NULL);
        if (res) {
            return NULL;
        }
    }

    res = zpath_debug_add_write_command("Add an error code for transaction log throttling",
                                "/zpn/transaction_log/throttling_error/add",
                                zpn_transaction_log_add_throttling_error,
                                NULL,
                                "error", "The error string to throttle. For ex: BRK_MT_SETUP_FAIL_SAML_EXPIRED",
                                NULL);

    if (res) {
        return NULL;
    }

    res = zpath_debug_add_write_command("Remove an error code for transaction log throttling",
                                  "/zpn/transaction_log/throttling_error/remove",
                                  zpn_transaction_log_remove_throttling_error,
                                  NULL,
                                  "error", "The error string to throttle. For ex: BRK_MT_SETUP_FAIL_SAML_EXPIRED",
                                  NULL);

    if (res) {
        return NULL;
    }

    res = zpath_debug_add_read_command("Show error codes for transaction log throttling",
                                  "/zpn/transaction_log/throttling_error/show",
                                  zpn_transaction_log_show_throttling_error,
                                  NULL,
                                  NULL);
    if (res) {
        return NULL;
    }

    res = zpath_debug_add_write_command("Turn on/off transaction log throttling.",
                                "/zpn/transaction_log/throttling/toggle",
                                zpn_transaction_log_throttling,
                                NULL,
                                NULL);
    if (res) {
        return NULL;
    }

    res = zpath_debug_add_read_command("Show the status of switch to cloud for one client",
                                  "/zpn/broker/client/switchtocloud",
                                  zpn_broker_client_swittchcloud_status,
                                  NULL,
                                  "tunnel_id", "Tunnel ID of the user",
                                  NULL);
    if (res) {
        return NULL;
    }

    res = zpath_debug_add_write_command("Show the status of switch to cloud for one client",
                                  "/zpn/broker/client/test_switchtocloud",
                                  zpn_broker_client_test_swittchcloud_status,
                                  NULL,
                                  "time", "The elapsed time after client is connected, in second",
                                  "mtunnel", "Number of mtunnels",
                                  NULL);
    if (res) {
        return NULL;
    }

    res = zpath_debug_add_write_command("Dump c_state info of all the clients",
                                  "/zpn/broker/client/cstate/dump_all",
                                  zpn_broker_client_dump_all,
                                  NULL,
                                  NULL);
    if (res) {
        return NULL;
    }

    res = zpath_debug_add_read_command("Dump c_state info of one client",
                                  "/zpn/broker/client/cstate/dump_one",
                                  zpn_broker_client_dump_one,
                                  NULL,
                                  "tunnel_id", "Tunnel ID of the user",
                                  NULL);
    if (res) {
        return NULL;
    }

    res = zpath_debug_add_write_command("Dump broker mtunnel internal states",
                                  "/zpn/broker/mtunnels/dump",
                                  zpn_broker_mtunnels_dump,
                                  NULL,
                                  "tunnel_id", "Tunnel ID of the user",
                                  "all", "all mtunnels(optional)",
                                  NULL);
    if (res) {
        return NULL;
    }

    res = zpath_debug_add_write_command("Dump broker mtunnel user_risk states",
                                  "/zpn/broker/client/user_risk/mtunnels/dump",
                                  zpn_broker_user_risk_mtunnels_dump,
                                  NULL,
                                  "tunnel_id", "Tunnel ID of the user",
                                  NULL);
    if (res) {
        return NULL;
    }

    res = zpath_debug_add_read_command("Dump all cstates for a customer gid",
                                  "/zpn/broker/client/customer_cstates",
                                  zpn_broker_customer_cstates_dump,
                                  NULL,
                                  "customer", "Required. Domain name or GID of the customer",
                                  NULL);
    if (res) {
        return NULL;
    }

    res = zpath_debug_add_read_command("Dump cstate statistics for a customer gid",
                                  "/zpn/broker/client/customer_cstates/stats",
                                  zpn_broker_customer_cstates_stats_dump,
                                  NULL,
                                  "customer", "Required. Domain name or GID of the customer",
                                  NULL);
    if (res) {
        return NULL;
    }

    res = zpath_debug_add_read_command("Display list of customer gid with connections on the instance",
                                  "/zpn/broker/client/customer_list",
                                  zpn_broker_customer_list_command,
                                  NULL,
                                  NULL);
    if (res) {
        return NULL;
    }

    res = zpath_debug_add_safe_read_command("Dump broker c2c registrations",
                                       "/zpn/broker/c2c/registrations",
                                       zpn_broker_c2c_registrations_dump,
                                       NULL,
                                       "id", "Required. fqdn or customer id according to type",
                                       "type", "Required. fqdn or customer",
                                       NULL);
    if (res) {
        return NULL;
    }

    if (!zpn_is_dr_mode_active()) {
        res = zpath_debug_add_safe_read_command("Dump broker c2c IP reservations",
                                           "/zpn/broker/c2c/ips",
                                           zpn_broker_ipars_dump_reserved_ips,
                                           NULL,
                                           "customer", "Optional. Customer GID.",
                                           "ip_range", "Optional. IP range GID.",
                                           NULL);
        if (res) {
            return NULL;
        }
    }

    res = zpath_debug_add_safe_read_command("Dump broker c2c cnames for an fqdn",
                                       "/zpn/broker/c2c/cnames",
                                       zpn_broker_c2c_cnames_dump,
                                       NULL,
                                       "fqdn", "Required. c2c fqdn",
                                       NULL);
    if (res) {
        return NULL;
    }

    res = zpath_debug_add_write_command("Dump broker active c2c mtunnels",
                                       "/zpn/broker/c2c/mtunnels/dump",
                                       zpn_broker_c2c_mtunnels_dump,
                                       NULL,
                                       NULL);
    if (res) {
        return NULL;
    }

    res = zpath_debug_add_safe_read_command("Dump ZVPN's control connections",
                                       "/zpn/broker/eas/ctrl/dump",
                                       eas_broker_zvpn_ctrl_dump,
                                       NULL,
                                       "zia_id", "Optional.",
                                       "zia_cloud", "Optional.",
                                       NULL);
    if (res) {
        return NULL;
    }

    res = zpath_debug_add_admin_command("Close ZVPN's control connections",
                                       "/zpn/broker/eas/ctrl/close",
                                       eas_broker_zvpn_ctrl_close,
                                       NULL,
                                       "zia_id", "",
                                       "zia_cloud", "",
                                       NULL);
    if (res) {
        return NULL;
    }

    res = zpath_debug_add_safe_read_command("Dump ZVPN's data connections",
                                       "/zpn/broker/eas/data/dump",
                                       eas_broker_zvpn_data_dump,
                                       NULL,
                                       "zia_id", "Optional.",
                                       "zia_cloud", "Optional.",
                                       NULL);
    if (res) {
        return NULL;
    }

    res = zpath_debug_add_safe_read_command("Dump broker active eas tunnel entries",
                                       "/zpn/broker/eas/entries/dump",
                                       eas_broker_zvpn_entries_dump,
                                       NULL,
                                       "zia_id", "Optional.",
                                       "zia_cloud", "Optional.",
                                       NULL);
    if (res) {
        return NULL;
    }

    if (zpn_broker_is_dev_environment()) {
        zpath_debug_add_write_command("add mock fqdn entry, auto increment fqdn and cnames",
                                "/zpn/broker/c2c/fqdn_mock",
                                zpn_broker_c2c_fqdn_mock_entry,
                                NULL,
                                "fqdn", "Required. mock fqdn,not validated,string",
                                "customer_id", "Required. number ",
                                "cname", "Required. string ",
                                NULL);

        zpath_debug_add_write_command("trigger c2c api",
                                "/zpn/broker/c2c/api",
                                zpn_broker_c2c_api,
                                NULL,
                                "id","api id, 1-update c2c registration, 2-pb_client close zrdt, 3-flips C2C_TEST_NO_TRANSIT_REQ_ACK",
                                "p1", "id 1 - c2c registration with dispatcher, 2 - close pbza connection to broker ",
                                "p2", "string id=2 - cname",
                                NULL);

        zpath_debug_add_write_command("Tweak c2c IP reserved_us in c_state",
                                "/zpn/broker/c2c/ip_reserved_at",
                                zpn_broker_ipars_tweak_reserved_us,
                                NULL,
                                "tunnel_id", "Required. Tunnel ID to look for.",
                                "reserved_sec", "Required. New epoch time in seconds",
                                NULL);

        zpath_debug_add_write_command("Send ZVPN Location Registration from Ctrl Brk to Dispatcher",
                                "/zpn/broker/eas/ctrlbrk2dsp/send_loc_reg",
                                debug_zpn_eas_broker_send_loc_reg_to_dsp,
                                NULL,
                                "msg_id", "Required, Location Registration Msg ID",
                                "zpa_customer_gid", "Required, ZPA Customer GID",
                                "zia_cloud_name", "Required, ZIA Cloud",
                                "zia_org_id", "Required, ZIA org id",
                                "zia_partner_id", "Required, ZIA partner id",
                                "zia_location_id", "Required, ZIA location id",
                                "zia_instance_id", "Required, ZIA instance id",
                                "zia_tunnel_id", "Required, ZIA tunnel id",
                                "tunnel_status", "Required, ZIA Tunnel Status",
                                "expires", "Required, Location Expiration in Seconds",
                                "tunnel_score", "Required, ZIA Tunnel Score",
                                "instance_score", "Required, ZIA Instance Score",
                                "zia_inst_latitude", "Required, ZIA Instance Latitude",
                                "zia_inst_longtitude", "Required, ZIA Instance Longitude",
                                NULL);

        zpath_debug_add_write_command("Send DNS assistant request from Ctrl Brk to ZVPN",
                                "/zpn/broker/eas/ctrlbrk2zvpn/send_dns_check",
                                eas_broker_send_dns_frm_ctlbrk,
                                NULL,
                                "zvpninstid", "Required. ZVPN instance id",
                                "ziacloud", "Required. ZIA cloud",
                                "name", "Required. domain name",
                                "type", "Required. type of request A, AAAA, SRV, TXT",
                                "ziaorgid", "Required. ZIA orgid",
                                "ziatunnelid", "Required. ZIA tunnel id",
                                "ziapartnerid", "Required. ZIA partner id",
                                "zialocid", "Required. ZIA location id",
                                "g_app", "Required. ZPA App gid",
                                "g_cst", "Required. ZPA Customer gid",
                                "g_dsp", "Optional. Dispatcher info",
                                "dsp_us", "Optional. Dispatcher time taken to send the request",
                                NULL);

        if (!zpn_is_dr_mode_active()) {
            zpath_debug_add_write_command("Trigger zpn_ipars_refresh_request internally (Dev/QA only)",
                                    "/zpn/broker/c2c/refresh_ip",
                                    zpn_broker_ipars_refresh_ip,
                                    NULL,
                                    "customer", "Required. Customer GID.",
                                    "ip_range", "Required. IP range GID.",
                                    "begin", "Optional. IP address a range begins with.",
                                    "end", "Optional. IP address a range ends with.",
                                    NULL);
        }
    }
    if(ZPN_BROKER_IS_PUBLIC()){
        res = zpath_debug_add_read_command("Dump throttled error counts at broker's end ",
                                    "/zpn/broker/natural_log_throttle/dump",
                                    zpn_broker_natural_log_throttle_stats_dump,
                                    NULL,
                                    NULL);
        if (res) {
            ZPN_LOG(AL_ERROR, "Could not add debug command: /zpn/broker/natural_log_throttle/dump");
            return NULL;
        }
    }
    res = zpath_debug_add_read_command("Print App Thread Heartbeat value for app threads",
                                "/zpn/broker/app_thread/heartbeat/value",
                                zpn_broker_get_app_thread_heartbeat_value,
                                NULL,
                                NULL);
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not add debug command: /zpn/broker/app_thread/heartbeat/value");
        return NULL;
    }

    res = zpn_policy_engine_debug_init();
    if (res) {
        return NULL;
    }

    res = zpn_broker_client_apps_debug_init();
    if (res) {
        return NULL;
    }

    res = zpn_application_domain_init_debug();
    if (res) {
        return NULL;
    }

    res = zpn_zrdt_init_debug();
    if (res) {
        return NULL;
    }

    zpn_zrdt_client_module_init();

    res = zpath_config_override_desc_register(&partner_login_override_desc);
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not register override description for %s: %s",
                partner_login_override_desc.key, zpn_result_string(res));
        return NULL;
    }

    res = zpath_config_override_desc_register(&zia_override_desc);
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not register override description for %s: %s", zia_override_desc.key,
                zpn_result_string(res));
        return NULL;
    }

    zpath_config_override_monitor_int(ZIA_INSPECTION_DISABLE,
                                      &g_app_zia_inspection_disable,
                                      NULL,
                                      (int64_t)ZIA_INSPECTION_DISABLE_DEFAULT,
                                      (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                      (int64_t)0);

    zpath_config_override_monitor_int(POLICY_REBUILD_BACKOFF_FEATURE_HARD_DISABLED,
                                      &g_policy_rebuild_backoff_hard_disabled,
                                      NULL,
                                      (int64_t)POLICY_REBUILD_BACKOFF_FEATURE_HARD_DISABLED_DEFAULT,
                                      (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                      (int64_t)0);

    zpath_config_override_monitor_int(ZPN_BROKER_STEP_UP_AUTH_FEATURE_HARD_DISABLED,
                                      &g_step_up_auth_hard_disable_flag,
                                      NULL,
                                      (int64_t)ZPN_BROKER_STEP_UP_AUTH_FEATURE_HARD_DISABLED_DEFAULT,
                                      (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                      (int64_t)0);

    zpn_user_risk_hard_disabled_monitor_init();

    if (g_broker_common_cfg->instance_type == ZPN_INSTANCE_TYPE_PUBLIC_BROKER) {
        zpn_register_natural_log_throttle();
        res = zpn_broker_dispatch_override_init();
        if (res) {
            ZPN_LOG(AL_ERROR, "Could not register broker-dispatch override description: %s",
                    zpn_result_string(res));
            return NULL;
        }

        int64_t root_customer_gid = ZPATH_GID_GET_CUSTOMER_GID(zpath_instance_global_state.current_config->gid);
        zpath_config_override_monitor_int(POLICY_EVAL_TYPE_FEATURE,
                                          &g_policy_eval_type,
                                          NULL,
                                          (int64_t)DEFAULT_POLICY_EVAL_TYPE,
                                          zpath_instance_global_state.current_config->gid,
                                          root_customer_gid,
                                          (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                          (int64_t)0);
        zpath_config_override_monitor_int(ZTHREAD_ON_CRASH_DO_BROKER_CONFIG_OVERRIDE_KEY,
                                          &zthread_on_crash_do_feature,
                                          zthread_on_crash_do_config_override_monitor_callback,
                                          (int64_t)ZTHREAD_ON_CRASH_DO_BROKER_CONFIG_OVERRIDE_DEFAULT,
                                          zpath_instance_global_state.current_config->gid,
                                          root_customer_gid,
                                          (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                          (int64_t)0);
        zpath_config_override_monitor_int(APP_DOWNLOAD_KEEP_ALIVE_FEATURE,
                                          &g_app_download_keep_alive,
                                          NULL,
                                          (int64_t)DEFAULT_APP_DOWNLOAD_KEEP_ALIVE_FEATURE,
                                          zpath_instance_global_state.current_config->gid,
                                          root_customer_gid,
                                          (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                          (int64_t)0);
        zpath_config_override_monitor_int(ZIA_INSPECTION_IDLE_TIMEOUT_S,
                                          &g_app_zia_inspection_idle_timeout_s,
                                          NULL, // connections will pickup  new timeout on creation
                                          (int64_t)ZIA_INSPECTION_IDLE_TIMEOUT_S_DEFAULT,
                                          (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                          (int64_t)0);
         //monitoring global enable/disable flag
        zpath_config_override_monitor_int(NATURAL_TRANS_LOG_RATE_LIMITTING,
                                          &(natural_trans_log_limiting_hard_disable_flag),
                                          zpn_natural_log_throttle_callback,
                                          (int64_t)NATURAL_TRANS_LOG_RATE_LIMITTING_DEFAULT,
                                          root_customer_gid,
                                          (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                          (int64_t)0);
        zpath_config_override_monitor_int(NATURAL_TRANS_LOG_RATE_LIMIT_TIME,
                                          &(natural_trans_log_limiting_global_time),
                                          zpn_natural_log_throttle_time_callback,
                                          (int64_t)NATURAL_TRANS_LOG_RATE_LIMIT_DEFAULT_TIME,
                                          root_customer_gid,
                                          (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                          (int64_t)0);
        zpath_config_override_monitor_int(POLICY_REBUILD_BACKOFF_FEATURE,
                                          &g_policy_rebuild_backoff,
                                          NULL,
                                          (int64_t)POLICY_REBUILD_BACKOFF_FEATURE_DEFAULT,
                                          zpath_instance_global_state.current_config->gid,
                                          (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                          (int64_t)0);
        zpath_config_override_monitor_int(POLICY_REBUILD_BACKOFF_INTERVAL_SEC,
                                          &g_policy_rebuild_backoff_interval_s,
                                          NULL,
                                          (int64_t)POLICY_REBUILD_BACKOFF_INTERVAL_SEC_DEFAULT,
                                          zpath_instance_global_state.current_config->gid,
                                          (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                          (int64_t)0);
        zpath_config_override_monitor_int(POLICY_REBUILD_BACKOFF_PERIODIC_CHECK_INTERVAL_SEC,
                                          &g_policy_rebuild_backoff_periodic_check_interval_s,
                                          NULL,
                                          (int64_t)POLICY_REBUILD_BACKOFF_PERIODIC_CHECK_INTERVAL_SEC_DEFAULT,
                                          zpath_instance_global_state.current_config->gid,
                                          (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                          (int64_t)0);
        zpath_config_override_monitor_int(BROKER_GLOBAL_USER_RISK_FEATURE_ENABLED,
                                          &(g_broker_user_risk_feature_enabled),
                                          NULL,
                                          (int64_t)DEFAULT_BROKER_GLOBAL_USER_RISK_FEATURE,
                                          (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                          (int64_t)0);
    } else if (g_broker_common_cfg->instance_type == ZPN_INSTANCE_TYPE_PRIVATE_BROKER) {
        int64_t customer_gid = ZPATH_GID_GET_CUSTOMER_GID(g_broker_common_cfg->private_broker.broker_id);
        zpath_config_override_monitor_int(POLICY_EVAL_TYPE_FEATURE,
                                          &g_policy_eval_type,
                                          NULL,
                                          (int64_t)DEFAULT_POLICY_EVAL_TYPE,
                                          g_broker_common_cfg->private_broker.broker_id,
                                          g_broker_common_cfg->private_broker.pb_group_id,
                                          customer_gid,
                                          (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                          (int64_t)0);
        zpath_config_override_monitor_int(APP_DOWNLOAD_KEEP_ALIVE_FEATURE,
                                          &g_app_download_keep_alive,
                                          NULL,
                                          (int64_t)DEFAULT_APP_DOWNLOAD_KEEP_ALIVE_FEATURE,
                                          g_broker_common_cfg->private_broker.broker_id,
                                          g_broker_common_cfg->private_broker.pb_group_id,
                                          customer_gid,
                                          (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                          (int64_t)0);
        zpath_config_override_monitor_int(DNS_TXT_QUERY_SUPPORT_FEATURE,
                                          &g_txt_support,
                                          NULL,
                                          (int64_t)DEFAULT_DNS_TXT_QUERY_SUPPORT_FEATURE,
                                          g_broker_common_cfg->private_broker.broker_id,
                                          g_broker_common_cfg->private_broker.pb_group_id,
                                          customer_gid,
                                          (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                          (int64_t)0);

        zpath_config_override_monitor_int(PSE_USER_RISK_FEATURE_ENABLED,
                                          &(g_pse_user_risk_feature_enabled),
                                          NULL,
                                          DEFAULT_PSE_USER_RISK_FEATURE,
                                          customer_gid,
                                          (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                          (int64_t)0);
        zpath_config_override_monitor_int(POLICY_REBUILD_BACKOFF_FEATURE,
                                          &g_policy_rebuild_backoff,
                                          NULL,
                                          (int64_t)POLICY_REBUILD_BACKOFF_FEATURE_DEFAULT,
                                          g_broker_common_cfg->private_broker.broker_id,
                                          g_broker_common_cfg->private_broker.pb_group_id,
                                          (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                          (int64_t)0);
        zpath_config_override_monitor_int(POLICY_REBUILD_BACKOFF_INTERVAL_SEC,
                                          &g_policy_rebuild_backoff_interval_s,
                                          NULL,
                                          (int64_t)POLICY_REBUILD_BACKOFF_INTERVAL_SEC_DEFAULT,
                                          g_broker_common_cfg->private_broker.broker_id,
                                          g_broker_common_cfg->private_broker.pb_group_id,
                                          (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                          (int64_t)0);
        zpath_config_override_monitor_int(POLICY_REBUILD_BACKOFF_PERIODIC_CHECK_INTERVAL_SEC,
                                          &g_policy_rebuild_backoff_periodic_check_interval_s,
                                          NULL,
                                          (int64_t)POLICY_REBUILD_BACKOFF_PERIODIC_CHECK_INTERVAL_SEC_DEFAULT,
                                          g_broker_common_cfg->private_broker.broker_id,
                                          g_broker_common_cfg->private_broker.pb_group_id,
                                          (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                          (int64_t)0);
        zpath_config_override_monitor_int(CONFIG_FEATURE_PSE_IPV6_ALT_AUTHSP,
                                          &g_pse_ipv6_alt_authsp_feature_status,
                                          zpn_broker_ipv6_alt_authsp_monitor_callback,
                                          (int64_t)CONFIG_FEATURE_PSE_IPV6_ALT_AUTHSP_DEFAULT,
                                          g_broker_common_cfg->private_broker.broker_id,
                                          g_broker_common_cfg->private_broker.pb_group_id,
                                          customer_gid,
                                          (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                          (int64_t)0);
    }

    zpath_app_reached_milestone(zpath_app_milestone_component_initialized);

    zpn_register_waf_desc();

    zpn_register_pra_desc();

    zpn_zdx_https_register_desc();

    zpn_broker_fohh_mconn_track_perf_level_register_desc();

    return &broker;
}

struct zpn_broker *zpn_broker_init(int thread_count,
                                   struct zcdns *zcdns,
                                   struct zpn_common_broker_cfg *broker_cfg,
                                   const char *logfile_path,
                                   uint8_t dsp_channel_count,
                                   int use_itasca_logging_port_he) {
    return zpn_broker_init_internal(thread_count, zcdns, broker_cfg, logfile_path, dsp_channel_count, 1, use_itasca_logging_port_he);
}

struct zpn_broker *zpn_broker_init_standard(int thread_count,
                                   struct zcdns *zcdns,
                                   struct zpn_common_broker_cfg *broker_cfg,
                                   const char *logfile_path,
                                   uint8_t dsp_channel_count,
                                   int use_itasca_logging_port_he) {
    return zpn_broker_init_internal(thread_count, zcdns, broker_cfg, logfile_path, dsp_channel_count, 0, use_itasca_logging_port_he);
}

int zpn_broker_init_deferred() {
    return zpn_broker_client_listen_deferred(sni_server);
}

struct fohh_generic_server *zpn_broker_get_sni_server()
{
    return sni_server;
}

static int zpn_private_broker_set_group_cfg(int64_t private_broker_gid)
{
    int res;
    struct zpn_private_broker_to_group *pbroker_to_group;
    struct zpn_private_broker_group *group;


    /* Get mapping row from pbroker */
    res = zpn_pbroker_to_group_get_by_private_broker_gid(private_broker_gid,
                                                         &pbroker_to_group,
							 1,
                                                         NULL,
                                                         NULL,
                                                         0);
    if (res) {
        if (res != ZPN_RESULT_ASYNCHRONOUS) {
            ZPN_LOG(AL_ERROR, "Unable to get group mapping for private broker gid: %lu: %s", (long)private_broker_gid, zpn_result_string(res));
        } else {
            ZPN_DEBUG_STARTUP("Waiting for private broker group associations configuration...");
        }
        return res;
    }

    /* Get pbroker group from mapping */
    res = zpn_pbroker_group_get_by_gid(pbroker_to_group->private_broker_group_gid, &group, 1, NULL, NULL, 0);
    if (res) {
        if (res != ZPN_RESULT_ASYNCHRONOUS) {
            ZPN_LOG(AL_ERROR, "Unable to get group mapping for private broker greoup gid: %lu", (long)pbroker_to_group->private_broker_group_gid);
        } else {
            ZPN_DEBUG_STARTUP("Waiting for private broker group configuration...");
        }
        return res;
    }

    g_broker_common_cfg->private_broker.pb_group_id = pbroker_to_group->private_broker_group_gid;
    g_broker_common_cfg->private_broker.site_id = group->site_gid;

    /* Set latitude, longitude, Country code from group config */
    zpn_private_broker_group_set_dynamic_cfg(group);

    return ZPN_RESULT_NO_ERROR;
}

/*
 * This function groups together c_state based on customer gid
 * c_state is attached to list customer_gid_entry based on the customer gid
 * this func needs to be called in conjunction with add_client
 *
 * As zdtls customer gid is determined later than add_client so this
 * code cannot be part of add_client but called separately based on
 * when the customer gid is available
 *
 * @c_state : connection between broker and client
 *
 */
void zpn_broker_add_cstate_to_customer_gid_entry(struct zpn_broker_client_fohh_state *c_state)
{
    int thread_id;
    struct zpn_broker_connected_customer_clients* gid_client_list;

    struct zpn_broker_cgid_to_cstate_per_thread *cgid_cstate = &(broker.cgid_to_cstate_per_thread);

    if (!c_state) {
        ZPN_LOG(AL_ERROR, "NULL c_state : Error storing c_state to "
                          "hash table zpn_broker_cgid_to_cstate_per_thread");
        return;
    }

    if (0 == c_state->customer_gid) {
        ZPN_LOG(AL_DEBUG, "%s: Customer gid is not populated for client %s "
                "in zpn_broker_add_cstate_to_customer_gid_entry",
                c_state->tunnel_id, zpn_client_type_string(c_state->client_type));
        /* having zero in customer gid is an incomplete entry; we will populate them later based on callbacks */
        c_state->customer_gid_entry_incomplete = 1;
        return;
    }

    /* Store the customer_gid -> cstate
     * mapping in the hash table for cstate thread
     */
    thread_id = c_state_get_fohh_thread_id(c_state);
    ZPN_BROKER_ASSERT_HARD((thread_id >= 0 && thread_id < FOHH_MAX_THREADS),
                            "c_state fohh thread is out-of-range");
    if (thread_id >= 0 && thread_id < FOHH_MAX_THREADS) {
        gid_client_list = zhash_table_lookup(cgid_cstate->customer_gid[thread_id],
                                      &(c_state->customer_gid),
                                      sizeof(c_state->customer_gid), NULL);
        if (gid_client_list == NULL) {
            gid_client_list = ZPN_CALLOC(sizeof(struct zpn_broker_connected_customer_clients));

            gid_client_list->customer_gid = c_state->customer_gid;

            LIST_INIT(&(gid_client_list->customer_client_list));

            int res = zhash_table_store(cgid_cstate->customer_gid[thread_id],
                                        &(c_state->customer_gid),
                                        sizeof(c_state->customer_gid),
                                        0,
                                        gid_client_list);
            if (res != ZPN_RESULT_NO_ERROR) {
                ZPN_LOG(AL_ERROR, "%s: Error storing gid %"PRId64" in hash table "
                        "zpn_broker_cgid_to_cstate_per_thread, thread_id %d. "
                        "Error is: %s", c_state->tunnel_id, c_state->customer_gid,
                        thread_id, zpn_result_string(res));

                ZPN_BROKER_ASSERT_SOFT(0, "%s: Error storing gid %"PRId64" in hash "
                         "table zpn_broker_cgid_to_cstate_per_thread, "
                         "thread_id %d. Error is: %s",
                         c_state->tunnel_id, c_state->customer_gid,
                         thread_id, zpn_result_string(res));
                return;
            }
        }
        LIST_INSERT_HEAD(&(gid_client_list->customer_client_list), c_state, customer_gid_entry);
        ZPN_LOG(AL_NOTICE, "%s: c_state for the customer id %"PRId64" added to client list for thread %d",
                            c_state->tunnel_id, c_state->customer_gid, thread_id);

        /* update stats based on client type */
        gid_client_list->all_client_type_stats[c_state->client_type]++;

        /* increment drainable count, if its a drainable one. */
        if (zpn_broker_is_drainable_conn(c_state)) {
            gid_client_list->drainable_conn_count++;
        }
    }
}

void zpn_broker_client_connected_customer_pagination_state_cleanup(struct zpn_broker_client_connected_customer_pagination_state *ps) {
    if (!ps) {
        return;
    }

    if (ps->cookie && ps->cleanup) {
        ps->cleanup(ps->cookie);
    }
    memset(ps, 0, sizeof(*ps));
}

/*
 * zpn_broker_remove_cstate_from_customer_gid_entry
 *  This func. removes the cstate from customer_gid_entry, and cleans up the hashtable , if needed
 *  Note that, we pass in customer_gid in cases the customer_gid has changed; and we need to remove the entry from old locn.
 */
void zpn_broker_remove_cstate_from_customer_gid_entry(struct zpn_broker_client_fohh_state *c_state, int64_t customer_gid)
{
    if (!c_state) {
        ZPN_LOG(AL_ERROR, "NULL c_state : Error deleting c_state from hash table zpn_broker_cgid_to_cstate_per_thread");
        return;
    }

    if (c_state->customer_gid_entry_incomplete != 0) {
        ZPN_LOG(AL_ERROR, "%s: customer_gid_entry is incomplete for c_state of type: %s , return!",
                          c_state->tunnel_id, zpn_client_type_string(c_state->client_type));
        return;
    }

    if (0 == c_state->customer_gid) {
        ZPN_LOG(AL_ERROR, "%s: Error deleting c_state from hash table for client type: %s: customer_gid is 0, return!",
                          c_state->tunnel_id, zpn_client_type_string(c_state->client_type));
        return;
    }

    /* update head of customer_gid list  and counters */
    struct zpn_broker_cgid_to_cstate_per_thread *cgid_cstate = &(broker.cgid_to_cstate_per_thread);
    struct zpn_broker_connected_customer_clients* gid_client_list;

    int thread_id = c_state->conn_thread_id;
    if (thread_id < 0 || thread_id >= FOHH_MAX_THREADS) {
        ZPN_LOG(AL_CRITICAL, "%s: drain_conn: c_state fohh thread is out-of-range in remove cstate from customer gid entry",
                              c_state->tunnel_id);
        return;
    }

    gid_client_list = zhash_table_lookup(cgid_cstate->customer_gid[thread_id],
                                         &customer_gid,
                                         sizeof(customer_gid), NULL);
    if (NULL == gid_client_list) {
        ZPN_LOG(AL_ERROR, "%s: Error deleting c_state from cgid_cstate hash at thread: %d for "
                            "customer %"PRId64" : NULL client list",
                            c_state->tunnel_id, thread_id, customer_gid);
        return;
    }

    /*
     * dis-associate the c_state from customer_gid_entry pagination states
     * if one of our next to process states is the current c_state then
     * the new next to process is whatever is next in the list.
     * If it is the end of the list, then that's fine it just means the updater can be done
     */

    if (gid_client_list->config_update_state.next_to_process == c_state) {
        gid_client_list->config_update_state.next_to_process = LIST_NEXT(c_state, customer_gid_entry);
    }
    if (gid_client_list->aggregate_domains_update_state.next_to_process == c_state) {
        gid_client_list->aggregate_domains_update_state.next_to_process = LIST_NEXT(c_state, customer_gid_entry);
    }

    LIST_REMOVE(c_state, customer_gid_entry);
    ZPN_LOG(AL_NOTICE, "%s: Removed c_state from the list for customer id %"PRId64"", c_state->tunnel_id, c_state->customer_gid);

    gid_client_list->all_client_type_stats[c_state->client_type]--;

    /* if conn. is passive, update the passive stats */
    if (zpn_client_conn_mode_passive == c_state->curr_connection_mode) {
        gid_client_list->all_client_type_passive_stats[c_state->client_type]--;
    } else {    /* drainable stats decr. only for active */
        if (zpn_broker_is_drainable_conn(c_state)) {
            gid_client_list->drainable_conn_count--;
        }
    }

    if (!LIST_FIRST(&(gid_client_list->customer_client_list))) {   /* last list entry removed, free it */
        ZPN_LOG(AL_NOTICE, "%s: Removing last entry in gid_client_list for customer: %"PRId64" thread: %d",
                            c_state->tunnel_id, customer_gid, thread_id);
        int res = zhash_table_remove(cgid_cstate->customer_gid[thread_id],
                            &customer_gid, sizeof(customer_gid), gid_client_list);
        if (res != ZPATH_RESULT_NO_ERROR) {
            ZPN_LOG(AL_NOTICE,
                    "%s: customer list in not in hash for customer id %"PRId64": for client thread %d and thread %d",
                    c_state->tunnel_id,
                    customer_gid,
                    c_state->conn_thread_id,
                    thread_id);
        }

        // clean up all our state for safety
        zpn_broker_client_connected_customer_pagination_state_cleanup(&(gid_client_list->config_update_state));
        zpn_broker_client_connected_customer_pagination_state_cleanup(&(gid_client_list->aggregate_domains_update_state));

        ZPN_FREE(gid_client_list);
    }
}

/*
 * zpn_broker_update_cstate_customer_gid_entry
 *  Placing the cstate in the right hashtable in cgid_cstate->customer_gid
 */
void zpn_broker_update_cstate_customer_gid_entry(struct zpn_broker_client_fohh_state *c_state,
                                                 int64_t prev_customer_gid)
{
    if (!c_state)
        return;

    /* customer_gid is same implies entry was added correct apriori */
    if (prev_customer_gid == c_state->customer_gid)
        return;

    /* disassociate from old gid. */
    if (prev_customer_gid) {
        zpn_broker_remove_cstate_from_customer_gid_entry(c_state, prev_customer_gid);
    }

    /* associate with new gid */
    zpn_broker_add_cstate_to_customer_gid_entry(c_state);

    /* customer gid entry is now complete */
    c_state->customer_gid_entry_incomplete = 0;
}

void zpn_broker_add_client(struct zpn_broker_client_fohh_state *c_state)
{
    int res;
    struct zpn_broker_connected_clients *connected_clients = &(broker.clients);

    pthread_mutex_lock(&(connected_clients->lock));

    if (!argo_hash_lookup(connected_clients->tun_ids, c_state->tunnel_id_bin, ZPN_TUNNEL_ID_BYTES, NULL)) {
        res = argo_hash_store(connected_clients->tun_ids, c_state->tunnel_id_bin, ZPN_TUNNEL_ID_BYTES, 0, c_state);

        if (res == ZPN_RESULT_NO_ERROR) {
            if (c_state->cname) {
                int ret;
                ret = argo_hash_store(connected_clients->cnames, c_state->cname, strlen(c_state->cname), 0, c_state);
                if (ret) {
                    ZPN_LOG(AL_ERROR, "Could not store in cnames table: %s", zpn_result_string(ret));
                } else {
                    ZPN_DEBUG_CLIENT("add cname=%s auth=%d %s", c_state->cname, c_state->auth_complete, c_state->tunnel_id);
                }
            }
        }

        if (res == ZPN_RESULT_NO_ERROR) {
            LIST_INSERT_HEAD(&(connected_clients->client_list), c_state, list_entry);

        } else {
            ZPN_LOG(AL_ERROR, "Could not store: %s", zpn_result_string(res));
        }
    }

    pthread_mutex_unlock(&(connected_clients->lock));
}

void zpn_broker_remove_client(struct zpn_broker_client_fohh_state *zc)
{
    int res;
    struct zpn_broker_connected_clients *connected_clients = &(broker.clients);

    pthread_mutex_lock(&(connected_clients->lock));

    if (argo_hash_lookup(connected_clients->tun_ids, zc->tunnel_id_bin, ZPN_TUNNEL_ID_BYTES, NULL)) {

        res = argo_hash_remove(connected_clients->tun_ids, zc->tunnel_id_bin, ZPN_TUNNEL_ID_BYTES, zc);
        if (res == ZPN_RESULT_NO_ERROR) {
            LIST_REMOVE(zc, list_entry);
        } else {
            ZPN_LOG(AL_ERROR, "Could not find client to remove: %s", zpn_result_string(res));
        }

        if (zc->cname) {
            // we need to remove all entries assosiated with this connection
            if (zc->endpoint_cname) {
                res = argo_hash_remove(connected_clients->cnames, zc->endpoint_cname, strlen(zc->endpoint_cname), zc);
                if (!res) {
                    ZPN_DEBUG_C2C("removed cname=%s", zc->endpoint_cname);
                }
            }

            res = argo_hash_remove(connected_clients->cnames, zc->cname, strlen(zc->cname), zc);
            if (!res) {
                ZPN_DEBUG_C2C("removed cname=%s", zc->cname);
            }
        }
    }

    /* Remove the cstate from the customer_gid list */
    zpn_broker_remove_cstate_from_customer_gid_entry(zc, zc->customer_gid);

    /* Remove C2C-by-IP registration and notify dispatchers and the client */
    zpn_broker_ipars_cleanup_c2c_ip_locked(zc);

    /* Remove NP registration */
    zpn_broker_np_ipars_release_and_cleanup_np_ip(zc, 0, 1);

    /* Remove C2C registration and notify dispatchers and the client */
    c2c_remove_registration_locked(zc);

    pthread_mutex_unlock(&(connected_clients->lock));

    if (zc->client_type == zpn_client_type_eas_ctrl && zc->zvpn_registered) {
        (void)eas_broker_remove_ctrl_zvpn(zc);
    }
}

/*
 * find connected clinet by cname
 * there could be multiple clients, the selection is done by:
 *   1. DTLS over TLS
 *   2. newer over older
 */
struct zpn_broker_client_fohh_state *zpn_broker_find_connected_client_by_cname(const char *cname, int64_t* incarnation,int *thread_id) {
#define MAX_NUMBER_OF_DUPLICATED_CNAME 20

    struct zpn_broker_connected_clients *connected_clients = &(broker.clients);
    struct zpn_broker_client_fohh_state *zc[MAX_NUMBER_OF_DUPLICATED_CNAME] = {0};
    struct zpn_broker_client_fohh_state *best_zc = NULL;
    int counter = 0;
    int overflow = 0;

    if (!cname) {
        return NULL;
    }

    pthread_mutex_lock(&(connected_clients->lock));
    counter = argo_hash_lookup_multiple(connected_clients->cnames, cname, strlen(cname),(void **) &zc[0],
                                        MAX_NUMBER_OF_DUPLICATED_CNAME, &overflow);

    ZPN_DEBUG_C2C("c2c lookup cname=%s counter=%d ", cname, counter);

    if (overflow) {
        ZPN_LOG(AL_ERROR, "argo_hash_lookup_multiple found more matches that it can report back. counter=%d cname=%s",
                counter, cname);
        // work with what we got, ignore the rest
    }

    if (0 == counter) {
        pthread_mutex_unlock(&(connected_clients->lock));
        return NULL;
    }

    // choose the best
    for (int i = 0; i < counter; i++) {

        if (NULL == best_zc || // pick first one if not set
            (best_zc->tlv_type != zc[i]->tlv_type && zc[i]->tlv_type == zpn_zrdt_tlv) ||  // prefer DTLS
            (best_zc->tlv_type == zc[i]->tlv_type &&
             best_zc->start_epoch_us < zc[i]->start_epoch_us)) {  // check more resent if tlv are the same
            // zc[i] is better
            best_zc = zc[i];
            ZPN_DEBUG_C2C("select type=%s epoch=%" PRId64, zpn_tlv_type_str(zc[i]->tlv_type), zc[i]->start_epoch_us);
        }
    }

    if (best_zc) {
        *incarnation = best_zc->incarnation;

        if (best_zc->tlv_type == zpn_fohh_tlv) {
            struct fohh_connection *conn = zpn_mconn_fohh_tlv_get_conn(&(best_zc->tlv_state));
            if (conn) {
                *thread_id = fohh_connection_get_thread_id(conn);
            } else {
                *thread_id = 0;
            }
        } else {
            *thread_id = best_zc->zrdt_tlv_state.fohh_thread_id;
        }
    }

    pthread_mutex_unlock(&(connected_clients->lock));
    return best_zc;  // could be NULL
}

int64_t zpn_broker_is_waf_enabled(int64_t broker_gid, int64_t cst_id)
{
    // Check if the feature is disabled globally
    if (g_waf_global_disable) {
        // WAF has been disabled globally
        //
        return (int64_t)0;
    }

    // Feature is not disabled globally. Check if it has been enabled for this broker
    // or for this customer.
    //
    int64_t is_waf_enabled = WAF_ENABLED;

    is_waf_enabled = zpath_config_override_get_config_int(WAF_FEATURE,
                                                          &is_waf_enabled,
                                                          WAF_ENABLED,
                                                          broker_gid,
                                                          cst_id,
                                                          (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                          (int64_t)0);
    return is_waf_enabled;
}


int64_t zpn_broker_is_waf_ptag_enabled(int64_t broker_gid, int64_t cst_id)
{
    /* Check if the feature is disabled globally */
    if (g_ptag_global_disable) {
        // WAF Protocol Tagging has been disabled globally
        return (int64_t)0;
    }

    /* Feature is not disabled globally. Check if it has been enabled for this broker
     * or for this customer. */
    int64_t is_waf_ptag_enabled = WAF_PROTOCOL_TAGGING_ENABLED;

    is_waf_ptag_enabled =    zpath_config_override_get_config_int(WAF_PROTOCOL_TAGGING_FEATURE,
                                                                              &is_waf_ptag_enabled,
                                                                              WAF_PROTOCOL_TAGGING_ENABLED,
                                                                              broker_gid,
                                                                              cst_id,
                                                                              (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                                              (int64_t)0);

    return is_waf_ptag_enabled;
}

int zpn_broker_is_dtls_enabled_on_broker()
{
    int64_t config_value = 0;

    if (g_broker_common_cfg->instance_type == ZPN_INSTANCE_TYPE_PUBLIC_BROKER) {
        int64_t root_customer_gid = ZPATH_GID_GET_CUSTOMER_GID(zpath_instance_global_state.current_config->gid);
        config_value = zpath_config_override_get_config_int(DTLS_FEATURE,
                                                            &config_value,
                                                            DEFAULT_BROKER_DTLS,
                                                            zpath_instance_global_state.current_config->gid,
                                                            root_customer_gid,
                                                            (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                            (int64_t)0);
        ZPN_DEBUG_COR("DTLS config value for broker gid %ld root customer gid %ld is %ld",
                      (long)zpath_instance_global_state.current_config->gid,
                      (long)root_customer_gid,
                      (long)config_value);
    } else {
        int64_t customer_gid = ZPATH_GID_GET_CUSTOMER_GID(g_broker_common_cfg->private_broker.broker_id);
        config_value = zpath_config_override_get_config_int(DTLS_FEATURE,
                                                            &config_value,
                                                            DEFAULT_BROKER_DTLS,
                                                            g_broker_common_cfg->private_broker.broker_id,
                                                            g_broker_common_cfg->private_broker.pb_group_id,
                                                            customer_gid,
                                                            (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                            (int64_t)0);
        ZPN_DEBUG_COR("DTLS config value for broker gid %ld brk grp gid %ld customer gid %ld is %ld",
                       (long)g_broker_common_cfg->private_broker.broker_id,
                       (long)g_broker_common_cfg->private_broker.pb_group_id,
                       (long)customer_gid,
                       (long)config_value);
    }


    return config_value?1:0;
}

void zpn_broker_get_app_buffer_parameters() {
    int enhanced = 0;
    int fohh_window = 0;
    int fohh_mconn_window = 0;
    int64_t fohh_watermark = 0;
    int64_t fohh_mconn_watermark = 0;
    if (zpn_broker_is_app_buffer_tune_enabled_for_broker()) {
        fohh_window = zpn_broker_config_get_fohh_window();
        fohh_mconn_window = zpn_broker_config_get_fohh_mconn_window();
        fohh_watermark = zpn_broker_config_get_fohh_watermark();
        fohh_mconn_watermark = zpn_broker_config_get_fohh_mconn_watermark();
        enhanced = 1;
        zpn_mconn_fohh_tlv_app_buffer_tune_enable(enhanced, fohh_window, fohh_mconn_window, fohh_watermark,
                                                  fohh_mconn_watermark);
    } else {
        zpn_mconn_fohh_tlv_app_buffer_tune_enable(enhanced, fohh_window, fohh_mconn_window, fohh_watermark,
                                                  fohh_mconn_watermark);
    }
}

int zpn_broker_is_app_buffer_tune_enabled_for_broker()
{
    int64_t config_value = 0;

    if (g_broker_common_cfg->instance_type == ZPN_INSTANCE_TYPE_PUBLIC_BROKER) {
        int64_t root_customer_gid = ZPATH_GID_GET_CUSTOMER_GID(zpath_instance_global_state.current_config->gid);
        config_value = zpath_config_override_get_config_int(ZPN_BROKER_APP_BUFFER_TUNE_FEATURE,
                                                            &config_value,
                                                            ZPN_BROKER_APP_BUFFER_TUNE_FEATURE_DEFAULT_VALUE,
                                                            zpath_instance_global_state.current_config->gid,
                                                            root_customer_gid,
                                                            (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                            (int64_t)0);
        ZPN_DEBUG_COR("App buffer tune feature config for %ld root customer gid %ld is %ld",
                      (long)zpath_instance_global_state.current_config->gid,
                      (long)root_customer_gid,
                      (long)config_value);
    } else {
        int64_t customer_gid = ZPATH_GID_GET_CUSTOMER_GID(g_broker_common_cfg->private_broker.broker_id);
        config_value = zpath_config_override_get_config_int(ZPN_PSE_APP_BUFFER_TUNE_FEATURE,
                                                            &config_value,
                                                            ZPN_PSE_APP_BUFFER_TUNE_FEATURE_DEFAULT_VALUE,
                                                            g_broker_common_cfg->private_broker.broker_id,
                                                            g_broker_common_cfg->private_broker.pb_group_id,
                                                            customer_gid,
                                                            (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                            (int64_t)0);
        ZPN_DEBUG_COR("App buffer tune feature config for %ld brk grp gid %ld customer gid %ld is %ld",
                       (long)g_broker_common_cfg->private_broker.broker_id,
                       (long)g_broker_common_cfg->private_broker.pb_group_id,
                       (long)customer_gid,
                       (long)config_value);
    }

    return config_value?1:0;
}


int64_t zpn_broker_config_get_fohh_window()
{
   int64_t config_value = 0;

    if (g_broker_common_cfg->instance_type == ZPN_INSTANCE_TYPE_PUBLIC_BROKER) {
        int64_t root_customer_gid = ZPATH_GID_GET_CUSTOMER_GID(zpath_instance_global_state.current_config->gid);
        config_value = zpath_config_override_get_config_int(ZPN_BROKER_APP_BUFFER_CONFIG,
                                                            &config_value,
                                                            ZPN_BROKER_APP_BUFFER_CONFIG_DEFAULT_VALUE,
                                                            zpath_instance_global_state.current_config->gid,
                                                            root_customer_gid,
                                                            (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                            (int64_t)0);
        if(config_value <= 0) {
            ZPN_DEBUG_COR("Fohh window cannot be <= 0, so setting it to default");
        }
        ZPN_DEBUG_COR("Fohh window for %ld root customer gid %ld is %ld",
                      (long)zpath_instance_global_state.current_config->gid,
                      (long)root_customer_gid,
                      (long)config_value);
    } else {
        int64_t customer_gid = ZPATH_GID_GET_CUSTOMER_GID(g_broker_common_cfg->private_broker.broker_id);
        config_value = zpath_config_override_get_config_int(ZPN_PSE_APP_BUFFER_CONFIG,
                                                            &config_value,
                                                            ZPN_PSE_APP_BUFFER_CONFIG_DEFAULT_VALUE,
                                                            g_broker_common_cfg->private_broker.broker_id,
                                                            g_broker_common_cfg->private_broker.pb_group_id,
                                                            customer_gid,
                                                            (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                            (int64_t)0);
        if(config_value <= 0) {
            ZPN_DEBUG_COR("Fohh window cannot be <= 0, so setting it to default");
        }
        ZPN_DEBUG_COR("App buffer tune feature config for %ld brk grp gid %ld customer gid %ld is %ld",
                       (long)g_broker_common_cfg->private_broker.broker_id,
                       (long)g_broker_common_cfg->private_broker.pb_group_id,
                       (long)customer_gid,
                       (long)config_value);
    }
    return config_value;
}

int64_t zpn_broker_config_get_fohh_mconn_window()
{
  int64_t config_value = 0;

    if (g_broker_common_cfg->instance_type == ZPN_INSTANCE_TYPE_PUBLIC_BROKER) {
        int64_t root_customer_gid = ZPATH_GID_GET_CUSTOMER_GID(zpath_instance_global_state.current_config->gid);
        config_value = zpath_config_override_get_config_int(ZPN_BROKER_APP_BUFFER_MCONN_CONFIG,
                                                            &config_value,
                                                            ZPN_BROKER_APP_BUFFER_MCONN_CONFIG_DEFAULT_VALUE,
                                                            zpath_instance_global_state.current_config->gid,
                                                            root_customer_gid,
                                                            (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                            (int64_t)0);
        if(config_value <= 0) {
            ZPN_DEBUG_COR("Fohh mconn window cannot be <= 0, so setting it to default");
        }
        ZPN_DEBUG_COR("Fohh window for %ld root customer gid %ld is %ld",
                      (long)zpath_instance_global_state.current_config->gid,
                      (long)root_customer_gid,
                      (long)config_value);
    } else {
        int64_t customer_gid = ZPATH_GID_GET_CUSTOMER_GID(g_broker_common_cfg->private_broker.broker_id);
        config_value = zpath_config_override_get_config_int(ZPN_PSE_APP_BUFFER_MCONN_CONFIG,
                                                            &config_value,
                                                            ZPN_PSE_APP_BUFFER_MCONN_CONFIG_DEFAULT_VALUE,
                                                            g_broker_common_cfg->private_broker.broker_id,
                                                            g_broker_common_cfg->private_broker.pb_group_id,
                                                            customer_gid,
                                                            (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                            (int64_t)0);
        if(config_value <= 0) {
            ZPN_DEBUG_COR("Fohh mconn window cannot be <= 0, so setting it to default");
        }
        ZPN_DEBUG_COR("App buffer tune feature config for %ld brk grp gid %ld customer gid %ld is %ld",
                       (long)g_broker_common_cfg->private_broker.broker_id,
                       (long)g_broker_common_cfg->private_broker.pb_group_id,
                       (long)customer_gid,
                       (long)config_value);
    }
    return config_value;
}

int64_t zpn_broker_config_get_fohh_watermark()
{
  int64_t config_value = 0;

    if (g_broker_common_cfg->instance_type == ZPN_INSTANCE_TYPE_PUBLIC_BROKER) {
        int64_t root_customer_gid = ZPATH_GID_GET_CUSTOMER_GID(zpath_instance_global_state.current_config->gid);
        config_value = zpath_config_override_get_config_int(ZPN_BROKER_APP_BUFFER_WATERMARK,
                                                            &config_value,
                                                            ZPN_BROKER_APP_BUFFER_WATERMARK_DEFAULT_VALUE,
                                                            zpath_instance_global_state.current_config->gid,
                                                            root_customer_gid,
                                                            (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                            (int64_t)0);
        ZPN_DEBUG_COR("Fohh window for %ld root customer gid %ld is %ld",
                      (long)zpath_instance_global_state.current_config->gid,
                      (long)root_customer_gid,
                      (long)config_value);
    } else {
        int64_t customer_gid = ZPATH_GID_GET_CUSTOMER_GID(g_broker_common_cfg->private_broker.broker_id);
        config_value = zpath_config_override_get_config_int(ZPN_PSE_APP_BUFFER_WATERMARK,
                                                            &config_value,
                                                            ZPN_PSE_APP_BUFFER_WATERMARK_DEFAULT_VALUE,
                                                            g_broker_common_cfg->private_broker.broker_id,
                                                            g_broker_common_cfg->private_broker.pb_group_id,
                                                            customer_gid,
                                                            (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                            (int64_t)0);
        ZPN_DEBUG_COR("App buffer tune feature config for %ld brk grp gid %ld customer gid %ld is %ld",
                       (long)g_broker_common_cfg->private_broker.broker_id,
                       (long)g_broker_common_cfg->private_broker.pb_group_id,
                       (long)customer_gid,
                       (long)config_value);
    }
    return config_value;
}

int64_t zpn_broker_config_get_fohh_mconn_watermark()
{
  int64_t config_value = 0;

    if (g_broker_common_cfg->instance_type == ZPN_INSTANCE_TYPE_PUBLIC_BROKER) {
        int64_t root_customer_gid = ZPATH_GID_GET_CUSTOMER_GID(zpath_instance_global_state.current_config->gid);
        config_value = zpath_config_override_get_config_int(ZPN_BROKER_APP_BUFFER_MCONN_WATERMARK,
                                                            &config_value,
                                                            ZPN_BROKER_APP_BUFFER_MCONN_WATERMARK_DEFAULT_VALUE,
                                                            zpath_instance_global_state.current_config->gid,
                                                            root_customer_gid,
                                                            (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                            (int64_t)0);
        ZPN_DEBUG_COR("Fohh window for %ld root customer gid %ld is %ld",
                      (long)zpath_instance_global_state.current_config->gid,
                      (long)root_customer_gid,
                      (long)config_value);
    } else {
        int64_t customer_gid = ZPATH_GID_GET_CUSTOMER_GID(g_broker_common_cfg->private_broker.broker_id);
        config_value = zpath_config_override_get_config_int(ZPN_PSE_APP_BUFFER_MCONN_WATERMARK,
                                                            &config_value,
                                                            ZPN_PSE_APP_BUFFER_MCONN_WATERMARK_DEFAULT_VALUE,
                                                            g_broker_common_cfg->private_broker.broker_id,
                                                            g_broker_common_cfg->private_broker.pb_group_id,
                                                            customer_gid,
                                                            (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                            (int64_t)0);
        ZPN_DEBUG_COR("App buffer tune feature config for %ld brk grp gid %ld customer gid %ld is %ld",
                       (long)g_broker_common_cfg->private_broker.broker_id,
                       (long)g_broker_common_cfg->private_broker.pb_group_id,
                       (long)customer_gid,
                       (long)config_value);
    }
    return config_value;
}


int zpn_broker_zapp_get_idle_tunnel_burst_value()
{
    int64_t config_value = DEFAULT_CONFIG_FEATURE_BROKER_ZAPP_IDLE_TUNNEL_BURST_COUNT;
    int64_t root_customer_gid = ZPATH_GID_GET_CUSTOMER_GID(zpath_instance_global_state.current_config->gid);

    config_value = zpath_config_override_get_config_int(CONFIG_FEATURE_BROKER_ZAPP_IDLE_TUNNEL_BURST_COUNT,
                                                        &config_value,
                                                        DEFAULT_CONFIG_FEATURE_BROKER_ZAPP_IDLE_TUNNEL_BURST_COUNT,
                                                        root_customer_gid,
                                                        (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                        (int64_t)0);

    ZPN_DEBUG_BROKER("Broker Zapp idle tunnel burst config value is %"PRId64, config_value);
    return config_value;
}

void zpn_broker_zapp_idle_tunnel_burst_value_monitor()
{
    int64_t root_customer_gid = ZPATH_GID_GET_CUSTOMER_GID(zpath_instance_global_state.current_config->gid);

    zpath_config_override_monitor_int(CONFIG_FEATURE_BROKER_ZAPP_IDLE_TUNNEL_BURST_COUNT,
                                    &fohh_conn_burst_value,
                                    NULL,
                                    DEFAULT_CONFIG_FEATURE_BROKER_ZAPP_IDLE_TUNNEL_BURST_COUNT,
                                    root_customer_gid,
                                    (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                    (int64_t)0);

    ZPN_DEBUG_BROKER("Broker Zapp idle tunnel burst config value is %"PRId64,fohh_conn_burst_value);
}

int zpn_broker_is_quickack_enabled_for_customer (int64_t customer_gid)
{
    int64_t config_value = 0;

    config_value = zpath_config_override_get_config_int(QUICKACK_CONFIG_CLIENT,
                                                        &config_value,
                                                        DEFAULT_BROKER_QUICKACK,
                                                        customer_gid,
                                                        (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                        (int64_t)0);

    ZPN_DEBUG_COR("Socket quickack config value for customer_id %ld is %ld", (long)customer_gid, (long)config_value);

    return config_value ? 1 : 0;
}

int zpn_broker_is_quickack_read_enabled_for_customer (int64_t customer_gid)
{
    int64_t config_value = 0;

    config_value = zpath_config_override_get_config_int(QUICKACK_READ_CONFIG_CLIENT,
                                                        &config_value,
                                                        DEFAULT_BROKER_QUICKACK_READ,
                                                        customer_gid,
                                                        (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                        (int64_t)0);

    ZPN_DEBUG_COR("Socket quickack read config value for customer_id %ld is %ld", (long)customer_gid,
            (long)config_value);

    return config_value?1:0;
}

int zpn_broker_is_low_write_watermark_enabled_for_customer (int64_t customer_gid)
{
    int64_t config_value = 0;

    if (ZPN_BROKER_IS_PUBLIC()) {
        config_value = zpath_config_override_get_config_int(LIBEVENT_LOW_WRITE_WATERMARK_BROKER,
                                                            &config_value,
                                                            LIBEVENT_LOW_WRITE_WATERMARK_DEFAULT,
                                                            customer_gid,
                                                            (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                            (int64_t)0);
    } else {
        config_value = zpath_config_override_get_config_int(LIBEVENT_LOW_WRITE_WATERMARK_PSE,
                                                            &config_value,
                                                            LIBEVENT_LOW_WRITE_WATERMARK_DEFAULT,
                                                            customer_gid,
                                                            (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                            (int64_t)0);
    }
    ZPN_DEBUG_COR("LibEvent Low Write Watermark config value for customer_id %ld is %ld", (long)customer_gid,
            (long)config_value);

    return config_value?1:0;
}


int zpn_broker_is_dns_assistant_check_unicast_enabled() {
    int64_t config_value = 0;

    if (g_broker_common_cfg->instance_type == ZPN_INSTANCE_TYPE_PUBLIC_BROKER) {
        int64_t root_customer_gid = ZPATH_GID_GET_CUSTOMER_GID(zpath_instance_global_state.current_config->gid);
        config_value = zpath_config_override_get_config_int(BROKER_DNS_ASSISTANT_CHECK_UNICAST_FEATURE,
                                                            &config_value,
                                                            DEFAULT_BROKER_DNS_ASSISTANT_CHECK_UNICAST_FEATURE,
                                                            zpath_instance_global_state.current_config->gid,
                                                            root_customer_gid,
                                                            (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                            (int64_t)0);
        ZPN_DEBUG_COR("zpn_dns_assistant_check optimization config value for broker gid %ld root customer gid %ld is %ld",
                         (long)zpath_instance_global_state.current_config->gid,
                         (long)root_customer_gid,
                         (long)config_value);
    }

    return config_value?1:0;
}


int zpn_broker_is_quickack_enabled_for_assistant (int64_t assistant_gid, int64_t assistant_group_id, int64_t customer_gid)
{
    int64_t config_value = 0;

    config_value = zpath_config_override_get_config_int(QUICKACK_CONFIG_ASSISTANT,
                                                        &config_value,
                                                        DEFAULT_BROKER_QUICKACK,
                                                        assistant_gid,
                                                        assistant_group_id,
                                                        customer_gid,
                                                        (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                        (int64_t)0);

    ZPN_DEBUG_COR("Socket quickack config value for assistant_gid = %ld for assistant_group_id = %ld for customer_id %ld is %ld", (long)assistant_gid, (long)assistant_group_id, (long)customer_gid, (long)config_value);

    return config_value ? 1 : 0;
}

int zpn_broker_is_quickack_read_enabled_for_assistant (int64_t assistant_gid, int64_t assistant_group_id, int64_t customer_gid)
{
    int64_t config_value = 0;

    config_value = zpath_config_override_get_config_int(QUICKACK_READ_CONFIG_ASSISTANT,
                                                        &config_value,
                                                        DEFAULT_BROKER_QUICKACK_READ,
                                                        assistant_gid,
                                                        assistant_group_id,
                                                        customer_gid,
                                                        (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                        (int64_t)0);

    ZPN_DEBUG_COR("Socket quickack read config value for assistant_gid = %ld for assistant_group_id = %ld for customer_id %ld is %ld", (long)assistant_gid, (long)assistant_group_id, (long)customer_gid,
            (long)config_value);

    return config_value?1:0;
}

int64_t zpn_broker_get_dns_check_response_delay_us(int64_t assistant_gid, int64_t assistant_group_id, int64_t customer_gid)
{
    int64_t config_value = 0;

    config_value = zpath_config_override_get_config_int(BROKER_FEATURE_DNS_CHECK_ERROR_DELAY_US,
                                                        &config_value,
                                                        DEFAULT_BROKER_FEATURE_DNS_CHECK_ERROR_DELAY_US,
                                                        assistant_gid,
                                                        assistant_group_id,
                                                        customer_gid,
                                                        (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                        (int64_t)0);

    ZPN_DEBUG_COR("dns check delay_us on error config value for assistant_gid = %ld for assistant_group_id = %ld for customer_id %ld is %ld", (long)assistant_gid, (long)assistant_group_id, (long)customer_gid, (long)config_value);

    return config_value;
}

int zpn_broker_is_dtls_enabled_for_customer(int64_t customer_gid)
{
    int64_t config_value = 0;

    config_value = zpath_config_override_get_config_int(DTLS_FEATURE,
                                                        &config_value,
                                                        DEFAULT_BROKER_DTLS,
                                                        customer_gid,
                                                        (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                        (int64_t)0);

    ZPN_DEBUG_COR("DTLS config value for customer_id %ld is %ld",
                  (long)customer_gid,
                  (long)config_value);

    return config_value?1:0;

}

int zpn_broker_is_dtls_enabled_for_client(int64_t client_gid, int64_t customer_gid)
{
    int64_t config_value = 0;

    config_value = zpath_config_override_get_config_int(DTLS_FEATURE,
                                                        &config_value,
                                                        DEFAULT_BROKER_DTLS,
                                                        client_gid,
                                                        customer_gid,
                                                        (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                        (int64_t)0);

    ZPN_DEBUG_COR("DTLS config value for client cert_id %ld customer_id %ld is %ld",
                  (long)client_gid,
                  (long)customer_gid,
                  (long)config_value);

    return config_value?1:0;

}

int zpn_broker_is_dtls_client_enabled_on_broker(int64_t customer_gid)
{
    int64_t config_value = 0;

    if (g_broker_common_cfg->instance_type == ZPN_INSTANCE_TYPE_PUBLIC_BROKER) {
        config_value = zpath_config_override_get_config_int(DTLS_FEATURE_CLIENT,
                                                            &config_value,
                                                            DEFAULT_BROKER_DTLS_CLIENT,
                                                            zpath_instance_global_state.current_config->gid,
                                                            customer_gid,
                                                            (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                            (int64_t)0);
        ZPN_DEBUG_COR("DTLS-CLIENT config value for broker gid %ld customer gid %ld is %ld",
                      (long)zpath_instance_global_state.current_config->gid,
                      (long)customer_gid,
                      (long)config_value);
    } else {
        int64_t pb_customer_gid = ZPATH_GID_GET_CUSTOMER_GID(g_broker_common_cfg->private_broker.broker_id);
        config_value = zpath_config_override_get_config_int(DTLS_FEATURE_CLIENT,
                                                            &config_value,
                                                            DEFAULT_PBROKER_DTLS_CLIENT,
                                                            g_broker_common_cfg->private_broker.broker_id,
                                                            g_broker_common_cfg->private_broker.pb_group_id,
                                                            pb_customer_gid,
                                                            (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                            (int64_t)0);
        ZPN_DEBUG_COR("DTLS-CLIENT config value for pbroker gid %ld brk grp gid %ld customer gid %ld is %ld",
                       (long)g_broker_common_cfg->private_broker.broker_id,
                       (long)g_broker_common_cfg->private_broker.pb_group_id,
                       (long)customer_gid,
                       (long)config_value);
    }

    return config_value?1:0;
}

int zpn_broker_is_dtls_enabled_for_app(int64_t app_gid, int64_t app_grp_gid, int64_t client_gid, int64_t customer_gid)
{
    int64_t config_value = 0;

    if (g_broker_common_cfg->instance_type == ZPN_INSTANCE_TYPE_PUBLIC_BROKER) {
        int64_t root_customer_gid = ZPATH_GID_GET_CUSTOMER_GID(zpath_instance_global_state.current_config->gid);
        if (client_gid) {
            config_value = zpath_config_override_get_config_int(DTLS_FEATURE,
                                                                &config_value,
                                                                DEFAULT_BROKER_DTLS,
                                                                app_gid,
                                                                app_grp_gid,
                                                                client_gid,
                                                                customer_gid,
                                                                zpath_instance_global_state.current_config->gid,
                                                                root_customer_gid,
                                                                (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                                (int64_t)0);
            ZPN_DEBUG_COR("DTLS config value for app_gid %ld app_grp_gid %ld client cert_id %ld customer_id %ld broker id %ld is %ld",
                          (long)app_gid,
                          (long)app_grp_gid,
                          (long)client_gid,
                          (long)customer_gid,
                          (long)zpath_instance_global_state.current_config->gid,
                          (long)config_value);
        } else {
            config_value = zpath_config_override_get_config_int(DTLS_FEATURE,
                                                                &config_value,
                                                                DEFAULT_BROKER_DTLS,
                                                                app_gid,
                                                                app_grp_gid,
                                                                customer_gid,
                                                                zpath_instance_global_state.current_config->gid,
                                                                root_customer_gid,
                                                                (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                                (int64_t)0);
            ZPN_DEBUG_COR("DTLS config value for app_gid %ld app_grp_gid %ld customer_id %ld broker id %ld is %ld",
                          (long)app_gid,
                          (long)app_grp_gid,
                          (long)customer_gid,
                          (long)zpath_instance_global_state.current_config->gid,
                          (long)config_value);
        }
    } else {
        if (client_gid) {
            config_value = zpath_config_override_get_config_int(DTLS_FEATURE,
                                                                &config_value,
                                                                DEFAULT_BROKER_DTLS,
                                                                app_gid,
                                                                app_grp_gid,
                                                                client_gid,
                                                                customer_gid,
                                                                g_broker_common_cfg->private_broker.broker_id,
                                                                (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                                (int64_t)0);
            ZPN_DEBUG_COR("DTLS config value for app_gid %ld app_grp_gid %ld client cert_id %ld customer_id %ld private broker id %ld is %ld",
                          (long)app_gid,
                          (long)app_grp_gid,
                          (long)client_gid,
                          (long)customer_gid,
                          (long)g_broker_common_cfg->private_broker.broker_id,
                          (long)config_value);
        } else {
            config_value = zpath_config_override_get_config_int(DTLS_FEATURE,
                                                                &config_value,
                                                                DEFAULT_BROKER_DTLS,
                                                                app_gid,
                                                                app_grp_gid,
                                                                customer_gid,
                                                                g_broker_common_cfg->private_broker.broker_id,
                                                                (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                                (int64_t)0);
            ZPN_DEBUG_COR("DTLS config value for app_gid %ld app_grp_gid %ld customer_id %ld private broker id %ld is %ld",
                          (long)app_gid,
                          (long)app_grp_gid,
                          (long)customer_gid,
                          (long)g_broker_common_cfg->private_broker.broker_id,
                          (long)config_value);
        }
    }

    return config_value?1:0;

}

void broker_set_malloc_fastbin(uint32_t value) {
    // restrict config to only 2 values since others are untested
    if (value != BROKER_FEATURE_MALLOC_FASTBIN_DISABLED && value != BROKER_FEATURE_MALLOC_FASTBIN_ENABLED) {
        ZPN_LOG(AL_WARNING, "Bad config value for malloc fastbin with enabled value: %u", value);
        return;
    }
#ifdef __linux__
    if(mallopt(M_MXFAST, value) != 1) {
      ZPN_LOG(AL_ERROR, "malloc fastbin with enabled value: %u failed", value);
    } else {
      ZPN_LOG(AL_INFO, "malloc fastbin with enabled value: %u success", value);
    }
#endif
}

static void malloc_fastbin_monitor_callback(const int64_t *config_value, int64_t impacted_gid)
{
    broker_set_malloc_fastbin((uint32_t)(uint64_t)*config_value);
}

static void setup_malloc_fastbin() {
    static int64_t malloc_fastbin_value = BROKER_FEATURE_MALLOC_FASTBIN_DEFAULT;
    if (ZPN_BROKER_IS_PUBLIC()) {
        zpath_config_override_monitor_int(BROKER_FEATURE_MALLOC_FASTBIN,
                                          &malloc_fastbin_value,
                                          malloc_fastbin_monitor_callback,
                                          BROKER_FEATURE_MALLOC_FASTBIN_DEFAULT,
                                          zpath_instance_global_state.current_config->gid,
                                          (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                          (int64_t)0);
    } else {
        int64_t customer_gid = ZPATH_GID_GET_CUSTOMER_GID(g_broker_common_cfg->private_broker.broker_id);
        zpath_config_override_monitor_int(PBROKER_FEATURE_MALLOC_FASTBIN,
                                          &malloc_fastbin_value,
                                          malloc_fastbin_monitor_callback,
                                          BROKER_FEATURE_MALLOC_FASTBIN_DEFAULT,
                                          g_broker_common_cfg->private_broker.broker_id,
                                          g_broker_common_cfg->private_broker.pb_group_id,
                                          customer_gid,
                                          (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                          (int64_t)0);
    }
}

int64_t zpn_fohh_flow_control_enhancement_enabled(int64_t customer_gid)
{
    int64_t value;
    value = zpath_config_override_get_config_int(FOHH_FLOW_CONTROL_ENHANCEMENTS,
                                                 &value,
                                                 DEFAULT_FOHH_FLOW_CONTROL_ENHANCEMENTS,
                                                 customer_gid,
                                                 (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                 (int64_t)0);
    return value;
}

static void dont_dump_monitor_callback(const int64_t *config_value, int64_t impacted_gid)
{
    zpath_dont_dump_enable_config_override((uint64_t)*config_value);
}

static void setup_dont_dump() {
    static int64_t dont_dump_value = FEATURE_DONT_DUMP_DEFAULT;
    if (ZPN_BROKER_IS_PUBLIC()) {
        zpath_config_override_monitor_int(BROKER_FEATURE_DONT_DUMP,
                                          &dont_dump_value,
                                          dont_dump_monitor_callback,
                                          FEATURE_DONT_DUMP_DEFAULT,
                                          zpath_instance_global_state.current_config->gid,
                                          (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                          (int64_t)0);
    }
}

uint32_t zpn_broker_batched_mconn_window_updates_enabled(int64_t assistant_gid, int64_t assistant_group_id, int64_t customer_gid)
{
  int64_t batched_mconn_window_updates = zpath_config_override_get_config_int(
    ZPN_BROKER_IS_PUBLIC() ? CONFIG_FEATURE_BROKER_MCONN_BATCH_WINDOW_UPDATES : CONFIG_FEATURE_PSE_MCONN_BATCH_WINDOW_UPDATES,
    &batched_mconn_window_updates,
    ZPN_BROKER_IS_PUBLIC() ? CONFIG_FEATURE_BROKER_MCONN_BATCH_WINDOW_UPDATES_DEFAULT : CONFIG_FEATURE_PSE_MCONN_BATCH_WINDOW_UPDATES_DEFAULT,
    assistant_gid,
    assistant_group_id,
    customer_gid,
    (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
    (int64_t)0);
   ZPN_DEBUG_COR("Batched window updates config for agid=%" PRId64" aggid=%" PRId64" cgid=%" PRId64" is %" PRId64,
                 assistant_gid, assistant_group_id, customer_gid, batched_mconn_window_updates);
   return batched_mconn_window_updates != 0;
}

// Note: Both ends of connection should be enabled for more accurate results
// also using the assistant instance/gid since scope is smallest
uint32_t zpn_broker_syn_app_rtt_enabled(int64_t assistant_gid, int64_t assistant_group_id, int64_t customer_gid)
{
  int64_t syn_app_rtt = zpath_config_override_get_config_int(
    ZPN_BROKER_IS_PUBLIC() ? CONFIG_FEATURE_BROKER_SYN_APP_RTT : CONFIG_FEATURE_PSE_SYN_APP_RTT,
    &syn_app_rtt,
    ZPN_BROKER_IS_PUBLIC() ? CONFIG_FEATURE_BROKER_SYN_APP_RTT_DEFAULT : CONFIG_FEATURE_PSE_SYN_APP_RTT_DEFAULT,
    assistant_gid,
    assistant_group_id,
    customer_gid,
    ZPN_BROKER_IS_PUBLIC() ? zpath_instance_global_state.current_config->gid : g_broker_common_cfg->private_broker.broker_id,
    (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
    (int64_t)0);
  ZPN_DEBUG_COR("Synchronous app rtt config for agid=%" PRId64" aggid=%" PRId64" cgid=%" PRId64" gid=%" PRId64" is %" PRId64,
                assistant_gid, assistant_group_id, customer_gid,
                ZPN_BROKER_IS_PUBLIC() ? zpath_instance_global_state.current_config->gid : g_broker_common_cfg->private_broker.broker_id,
                syn_app_rtt);
  return syn_app_rtt != 0;
}

// also handles assistant gid and assistant group gid in case that canary deployment is opted for
uint32_t zpn_broker_pipeline_latency_trace_enabled(int64_t assistant_gid, int64_t assistant_group_id, int64_t customer_gid)
{
  int64_t pipeline_latency_trace = zpath_config_override_get_config_int(
    ZPN_BROKER_IS_PUBLIC() ? CONFIG_FEATURE_BROKER_FOHH_PIPELINE_LATENCY_TRACE : CONFIG_FEATURE_PSE_FOHH_PIPELINE_LATENCY_TRACE,
    &pipeline_latency_trace,
    ZPN_BROKER_IS_PUBLIC() ? CONFIG_FEATURE_BROKER_FOHH_PIPELINE_LATENCY_TRACE_DEFAULT : CONFIG_FEATURE_PSE_FOHH_PIPELINE_LATENCY_TRACE_DEFAULT,
    assistant_gid,
    assistant_group_id,
    customer_gid,
    ZPN_BROKER_IS_PUBLIC() ? zpath_instance_global_state.current_config->gid : g_broker_common_cfg->private_broker.broker_id,
    (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
    (int64_t)0);
  ZPN_DEBUG_COR("Pipeline latency trace config for agid=%" PRId64" aggid=%" PRId64" cgid=%" PRId64" gid=%" PRId64" is %" PRId64,
                assistant_gid, assistant_group_id, customer_gid,
                ZPN_BROKER_IS_PUBLIC() ? zpath_instance_global_state.current_config->gid : g_broker_common_cfg->private_broker.broker_id,
                pipeline_latency_trace);
  return pipeline_latency_trace != 0;
}

// Note: Both ends of connection should be enabled for more accurate results
uint32_t zpn_broker_client_syn_app_rtt_enabled(int64_t customer_gid)
{
  int64_t syn_app_rtt = zpath_config_override_get_config_int(
    ZPN_BROKER_IS_PUBLIC() ? CONFIG_FEATURE_BROKER_CLIENT_SYN_APP_RTT : CONFIG_FEATURE_PSE_CLIENT_SYN_APP_RTT,
    &syn_app_rtt,
    ZPN_BROKER_IS_PUBLIC() ? CONFIG_FEATURE_BROKER_SYN_APP_RTT_DEFAULT : CONFIG_FEATURE_PSE_SYN_APP_RTT_DEFAULT,
    customer_gid,
    ZPN_BROKER_IS_PUBLIC() ? zpath_instance_global_state.current_config->gid : g_broker_common_cfg->private_broker.broker_id,
    (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
    (int64_t)0);
  ZPN_DEBUG_COR("Synchronous app rtt config for cgid=%" PRId64" gid=%" PRId64" is %" PRId64,
                customer_gid,
                ZPN_BROKER_IS_PUBLIC() ? zpath_instance_global_state.current_config->gid : g_broker_common_cfg->private_broker.broker_id,
                syn_app_rtt);
  return syn_app_rtt != 0;
}

uint32_t zpn_broker_client_pipeline_latency_trace_enabled(int64_t customer_gid)
{
  int64_t pipeline_latency_trace = zpath_config_override_get_config_int(
    ZPN_BROKER_IS_PUBLIC() ? CONFIG_FEATURE_BROKER_CLIENT_FOHH_PIPELINE_LATENCY_TRACE : CONFIG_FEATURE_PSE_CLIENT_FOHH_PIPELINE_LATENCY_TRACE,
    &pipeline_latency_trace,
    ZPN_BROKER_IS_PUBLIC() ? CONFIG_FEATURE_BROKER_FOHH_PIPELINE_LATENCY_TRACE_DEFAULT : CONFIG_FEATURE_PSE_FOHH_PIPELINE_LATENCY_TRACE_DEFAULT,
    customer_gid,
    ZPN_BROKER_IS_PUBLIC() ? zpath_instance_global_state.current_config->gid : g_broker_common_cfg->private_broker.broker_id,
    (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
    (int64_t)0);
  ZPN_DEBUG_COR("Pipeline latency trace config for cgid=%" PRId64" gid=%" PRId64" is %" PRId64,
                customer_gid,
                ZPN_BROKER_IS_PUBLIC() ? zpath_instance_global_state.current_config->gid : g_broker_common_cfg->private_broker.broker_id,
                pipeline_latency_trace);
  return pipeline_latency_trace != 0;
}

static void fohh_conn_setup_timemout_monitor_callback(const int64_t *config_value, int64_t impacted_gid)
{
    fohh_set_conn_setup_timeout((uint64_t)*config_value);
}

static void setup_fohh_conn_setup_timeout() {
    // First do a one shot read and then setup the monitor
    if (ZPN_BROKER_IS_PUBLIC()) {
        int64_t root_customer_gid = ZPATH_GID_GET_CUSTOMER_GID(zpath_instance_global_state.current_config->gid);

        fohh_conn_timeout_override = zpath_config_override_get_config_int(FOHH_CONNECTION_SETUP_TIMEOUT,
                                                                          &fohh_conn_timeout_override,
                                                                          FOHH_CONNECTION_SETUP_TIMEOUT_SEC,
                                                                          zpath_instance_global_state.current_config->gid,
                                                                          root_customer_gid,
                                                                          (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                                          (int64_t)0);
        fohh_set_conn_setup_timeout((uint64_t)fohh_conn_timeout_override);

        zpath_config_override_monitor_int(FOHH_CONNECTION_SETUP_TIMEOUT,
                                          &fohh_conn_timeout_override,
                                          fohh_conn_setup_timemout_monitor_callback,
                                          FOHH_CONNECTION_SETUP_TIMEOUT_SEC,
                                          zpath_instance_global_state.current_config->gid,
                                          root_customer_gid,
                                          (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                          (int64_t)0);
    } else {
        int64_t customer_gid = ZPATH_GID_GET_CUSTOMER_GID(g_broker_common_cfg->private_broker.broker_id);

        fohh_conn_timeout_override = zpath_config_override_get_config_int(FOHH_CONNECTION_SETUP_TIMEOUT,
                                                                          &fohh_conn_timeout_override,
                                                                          FOHH_CONNECTION_SETUP_TIMEOUT_SEC,
                                                                          g_broker_common_cfg->private_broker.broker_id,
                                                                          g_broker_common_cfg->private_broker.pb_group_id,
                                                                          customer_gid,
                                                                          (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                                          (int64_t)0);
        fohh_set_conn_setup_timeout((uint64_t)fohh_conn_timeout_override);

        zpath_config_override_monitor_int(FOHH_CONNECTION_SETUP_TIMEOUT,
                                          &fohh_conn_timeout_override,
                                          fohh_conn_setup_timemout_monitor_callback,
                                          FOHH_CONNECTION_SETUP_TIMEOUT_SEC,
                                          g_broker_common_cfg->private_broker.broker_id,
                                          g_broker_common_cfg->private_broker.pb_group_id,
                                          customer_gid,
                                          (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                          (int64_t)0);
    }
}

static void zpn_broker_auth_timeout_monitor_callback(const int64_t *config_value, int64_t impacted_gid)
{
    zpn_broker_client_set_auth_timeout((uint64_t)*config_value);
}

static void setup_broker_client_auth_complete_timeout() {

    // First do a one shot read and then setup the monitor
    if (ZPN_BROKER_IS_PUBLIC()) {
        int64_t root_customer_gid = ZPATH_GID_GET_CUSTOMER_GID(zpath_instance_global_state.current_config->gid);

        broker_client_auth_timeout_override = zpath_config_override_get_config_int(BROKER_CLIENT_AUTH_TIMEOUT,
                                                                                   &broker_client_auth_timeout_override,
                                                                                   BROKER_CLIENT_AUTH_TIMEOUT_SEC,
                                                                                   zpath_instance_global_state.current_config->gid,
                                                                                   root_customer_gid,
                                                                                   (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                                                   (int64_t)0);

        zpn_broker_client_set_auth_timeout((uint64_t)broker_client_auth_timeout_override);

        zpath_config_override_monitor_int(BROKER_CLIENT_AUTH_TIMEOUT,
                                          &broker_client_auth_timeout_override,
                                          zpn_broker_auth_timeout_monitor_callback,
                                          BROKER_CLIENT_AUTH_TIMEOUT_SEC,
                                          zpath_instance_global_state.current_config->gid,
                                          root_customer_gid,
                                          (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                          (int64_t)0);
    } else {
        int64_t customer_gid = ZPATH_GID_GET_CUSTOMER_GID(g_broker_common_cfg->private_broker.broker_id);

        broker_client_auth_timeout_override = zpath_config_override_get_config_int(BROKER_CLIENT_AUTH_TIMEOUT,
                                                                                   &broker_client_auth_timeout_override,
                                                                                   BROKER_CLIENT_AUTH_TIMEOUT_SEC,
                                                                                   g_broker_common_cfg->private_broker.broker_id,
                                                                                   g_broker_common_cfg->private_broker.pb_group_id,
                                                                                   customer_gid,
                                                                                   (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                                                   (int64_t)0);

        zpn_broker_client_set_auth_timeout((uint64_t)broker_client_auth_timeout_override);

        zpath_config_override_monitor_int(BROKER_CLIENT_AUTH_TIMEOUT,
                                          &broker_client_auth_timeout_override,
                                          zpn_broker_auth_timeout_monitor_callback,
                                          BROKER_CLIENT_AUTH_TIMEOUT_SEC,
                                          g_broker_common_cfg->private_broker.broker_id,
                                          g_broker_common_cfg->private_broker.pb_group_id,
                                          customer_gid,
                                          (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                          (int64_t)0);
    }
}

static void setup_broker_client_ip_anchor_rx_data_timeout() {

    // First do a one shot read and then setup the monitor
    if (ZPN_BROKER_IS_PUBLIC()) {
        int64_t root_customer_gid = ZPATH_GID_GET_CUSTOMER_GID(zpath_instance_global_state.current_config->gid);

        broker_client_ip_anchor_rx_data_timeout_override = zpath_config_override_get_config_int(BROKER_CLIENT_IP_ANCHOR_RX_DATA_TIMEOUT,
                                                                                                &broker_client_ip_anchor_rx_data_timeout_override,
                                                                                                BROKER_CLIENT_IP_ANCHOR_RX_DATA_TIMEOUT_SEC,
                                                                                                zpath_instance_global_state.current_config->gid,
                                                                                                root_customer_gid,
                                                                                                (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                                                                (int64_t)0);

        zpn_broker_client_ip_anchor_rx_data_timeout_set((uint64_t)broker_client_ip_anchor_rx_data_timeout_override);

    } else {
        int64_t customer_gid = ZPATH_GID_GET_CUSTOMER_GID(g_broker_common_cfg->private_broker.broker_id);

        broker_client_ip_anchor_rx_data_timeout_override = zpath_config_override_get_config_int(BROKER_CLIENT_IP_ANCHOR_RX_DATA_TIMEOUT,
                                                                                                &broker_client_ip_anchor_rx_data_timeout_override,
                                                                                                BROKER_CLIENT_IP_ANCHOR_RX_DATA_TIMEOUT_SEC,
                                                                                                g_broker_common_cfg->private_broker.broker_id,
                                                                                                g_broker_common_cfg->private_broker.pb_group_id,
                                                                                                customer_gid,
                                                                                                (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                                                                (int64_t)0);

        zpn_broker_client_ip_anchor_rx_data_timeout_set((uint64_t)broker_client_ip_anchor_rx_data_timeout_override);

    }
}

static void setup_broker_disable_flow_control_mtr() {

    if (ZPN_BROKER_IS_PUBLIC()) {
        int64_t root_customer_gid = ZPATH_GID_GET_CUSTOMER_GID(zpath_instance_global_state.current_config->gid);
        zpath_config_override_monitor_int(BROKER_DISABLE_FC_MTR,
                                          &broker_mtr_flow_control_disabled,
                                          NULL,
                                          DEFAULT_BROKER_DISABLE_FC_MTR,
                                          zpath_instance_global_state.current_config->gid,
                                          root_customer_gid,
                                          (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                          (int64_t)0);
    } else {
        int64_t customer_gid = ZPATH_GID_GET_CUSTOMER_GID(g_broker_common_cfg->private_broker.broker_id);
        zpath_config_override_monitor_int(BROKER_DISABLE_FC_MTR,
                                          &broker_mtr_flow_control_disabled,
                                          NULL,
                                          DEFAULT_BROKER_DISABLE_FC_MTR,
                                          g_broker_common_cfg->private_broker.broker_id,
                                          g_broker_common_cfg->private_broker.pb_group_id,
                                          customer_gid,
                                          (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                          (int64_t)0);
    }
}

int64_t zpn_broker_config_get_fohh_mconn_track_perf_stats_level(int64_t customer_id)
{
    int64_t config_value = 0;

    if (g_broker_common_cfg->instance_type == ZPN_INSTANCE_TYPE_PUBLIC_BROKER) {
        int64_t root_customer_gid = ZPATH_GID_GET_CUSTOMER_GID(zpath_instance_global_state.current_config->gid);
        config_value = zpath_config_override_get_config_int(ZPN_BROKER_FOHH_MCONN_TRACK_PERF_STATS_LEVEL,
                                                            &config_value,
                                                            ZPN_BROKER_FOHH_MCONN_TRACK_PERF_STATS_LEVEL_DEFAULT_VALUE,
                                                            zpath_instance_global_state.current_config->gid,
                                                            customer_id,
                                                            root_customer_gid,
                                                            (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                            (int64_t)0);
        ZPN_DEBUG_MTUNNEL("Fohh mconn track perf stats level feature config for customer %ld, gid %ld, root gid %ld is %ld",
                          (long)customer_id,
                          (long)zpath_instance_global_state.current_config->gid,
                          (long)root_customer_gid,
                          (long)config_value);
    } else {
        int64_t customer_gid = ZPATH_GID_GET_CUSTOMER_GID(g_broker_common_cfg->private_broker.broker_id);
        config_value = zpath_config_override_get_config_int(ZPN_PSE_FOHH_MCONN_TRACK_PERF_STATS_LEVEL,
                                                            &config_value,
                                                            ZPN_PSE_FOHH_MCONN_TRACK_PERF_STATS_LEVEL_DEFAULT_VALUE,
                                                            g_broker_common_cfg->private_broker.broker_id,
                                                            g_broker_common_cfg->private_broker.pb_group_id,
                                                            customer_gid,
                                                            (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                            (int64_t)0);
        ZPN_DEBUG_MTUNNEL("Fohh mconn track perf stats level feature config for %ld brk grp gid %ld customer gid %ld is %ld",
                          (long)g_broker_common_cfg->private_broker.broker_id,
                          (long)g_broker_common_cfg->private_broker.pb_group_id,
                          (long)customer_gid,
                          (long)config_value);
    }
    return config_value;
}

int zpn_broker_update_c2c_app_registration_to_dispatcher(struct zpn_broker_dispatcher *dispatcher, struct fohh_connection *f_conn)
{
    struct zpn_broker_connected_clients *connected_clients = &(broker.clients);
    struct zpn_broker_client_fohh_state *client;
    int res = ZPN_RESULT_NO_ERROR;

    ZPN_DEBUG_DISPATCHER("C2C sending all fqdn/ip registration to a channel for %s", dispatcher->domain_name);

    pthread_mutex_lock(&(connected_clients->lock));

    LIST_FOREACH(client, &(connected_clients->client_list), list_entry) {
        if (!client->fqdn_registered && !client->c2c_ip.reserved_ip.length) {
            continue;
        }

        if (!zpn_broker_dispatcher_pool_name_match(client->customer_gid, dispatcher->pool_name)) {
            continue;
        }

        zpn_broker_dispatch_send_c2c_registration_to_one_dispatcher(client, f_conn, dispatcher->instance_id);
    }

    pthread_mutex_unlock(&(connected_clients->lock));
    return res;
}

void zpn_broker_soft_assert_argo_reg_post_listen(int on) {
    g_soft_assert_argo_reg_post_listen = on;
}

static void fcon_close_on_thread(struct zevent_base *base, void *void_cookie, int64_t int_cookie) {
    struct fohh_connection *broker_f_conn = void_cookie;

    fohh_connection_disconnect(broker_f_conn, "manual trigger");
}
/*
 * dev debug command "/zpn/broker/c2c/api"
 *
 */
static int zpn_broker_c2c_api(struct zpath_debug_state *request_state,
                              const char **query_values,
                              int query_value_count,
                              void *cookie) {
    int64_t customer_gid;
    long app_id;

    if (!zpn_broker_is_dev_environment()) {
        ZDP("This features is not supported in production\n");
        return ZPATH_RESULT_NO_ERROR;
    }

    if (!query_values[0]) {
        ZDP("Require api id \n");
        // in dev environmetn will print all of them if fqdn is not specified
        return ZPATH_RESULT_NO_ERROR;
    }

    app_id = strtol(query_values[0], NULL, 0);

    if (1 == app_id) {
        // simulate registration on new dispatcher connection

        if (!query_values[1]) {
            ZDP("Require customer_id\n");

            return ZPATH_RESULT_NO_ERROR;
        }

        customer_gid = strtoll(query_values[1], NULL, 0);

        if (customer_gid == 0) {
            ZDP("customer_gid is invalid\n");
            return ZPN_RESULT_NO_ERROR;
        }

        struct zpn_broker_dispatcher *dispatcher = zpn_brk_dsp_circuit_breaker_get_best_dsp(customer_gid, zpn_err_brk_req_no_error, 0, NULL);

        if (NULL == dispatcher) {
            ZDP("no best dispatcher for customer_id=%" PRId64 "\n", customer_gid);
            return ZPN_RESULT_NO_ERROR;
        }

        struct fohh_connection *connection = zpn_broker_dispatcher_channel_select(dispatcher);

        if (NULL == connection) {
            ZDP("no connection for best dispatcher for customer_id=%" PRId64 "\n", customer_gid);
            return ZPN_RESULT_NO_ERROR;
        }

        ZDP("updating c2c registrations for dispatcher=%s customer_id=%" PRId64 "\n", dispatcher->domain_name,
            customer_gid);

        zpn_broker_update_c2c_app_registration_to_dispatcher(dispatcher, connection);

    } else if (2 == app_id) {
        // close pb_client's zrdt connection to broker
        if (ZPN_BROKER_IS_PRIVATE()) {
            if (!query_values[1]) {
                ZDP("Require connection cname required\n");
                return ZPATH_RESULT_NO_ERROR;
            }
            ZDP("cname=%s\n", query_values[1]);

            int fohh_control = query_values[2] ? strtoll(query_values[2], NULL, 0) : -1;
            struct zpn_broker_client_fohh_state *c_state = NULL;
            int64_t c_state_incarnation;
            int thread_num;
            c_state = zpn_broker_find_connected_client_by_cname(query_values[1], &c_state_incarnation, &thread_num);
            if (NULL == c_state) {
                ZDP("c_state not found\n");

                return ZPATH_RESULT_NO_ERROR;
            }

            if (c_state->public_broker_client) {
                ZDP("     PB client %s - %s\n", zpn_tlv_type_str(c_state->public_broker_client->outbound_tlv_type),
                    c_state->public_broker_client->peer_cn);

                if (2 == fohh_control) {
                    zpn_pb_client_close(c_state->public_broker_client);
                    c_state->public_broker_client = NULL;
                } else if (1 == fohh_control) {
                    if (c_state->public_broker_client->outbound_pb_to_b_client) {
                        struct zevent_base *zbase = NULL;
                        struct fohh_connection *broker_f_conn =
                                zpn_fohh_client_get_f_conn(c_state->public_broker_client->outbound_pb_to_b_client);
                        if (broker_f_conn) {
                            zbase = fohh_connection_zevent_base(broker_f_conn);
                            if (zbase) {
                                zevent_base_call(zbase, fcon_close_on_thread, broker_f_conn, 0);
                            }
                        }
                    }
                }
            }
            if (c_state->public_broker_client_zrdt) {
                ZDP("     PB client %s - %s\n", zpn_tlv_type_str(c_state->public_broker_client_zrdt->outbound_tlv_type),
                    c_state->public_broker_client_zrdt->peer_cn);

                zpn_pb_client_close(c_state->public_broker_client_zrdt);
                c_state->public_broker_client_zrdt = NULL;
            }

        } else {
            ZDP("Not a private broker\n");
        }
    } else if (3 == app_id) {
        // ignore transit_request_ack to cause tunnel timeout
        global_c2c_debug_flags ^= C2C_TEST_NO_TRANSIT_REQ_ACK;

        ZDP("C2C_TEST_NO_TRANSIT_REQ_ACK is %u\n", global_c2c_debug_flags);

    } else {
        ZDP("Invalid id. Valid ids:  1-update c2c registration, 2-pb_client close zrdt, 3-flips C2C_TEST_NO_TRANSIT_REQ_ACK\n");
    }



    return ZPN_RESULT_NO_ERROR;
}

int zpn_broker_dump_bootup_stats(struct zpath_debug_state *request_state,
                                 const char **query_values,
                                 int query_value_count,
                                 void *cookie)
{
    char tmp_buf[2048];
    char *s = tmp_buf;
    char *e = s + sizeof(tmp_buf);
    float total_ms = 0.0;

    const int stage_width = 35;
    const int status_width = 20;
    const int duration_width = 20;
    const int total_width = 80;
    const char *separator = "--------------------------------------------------------------------------------";

    s += snprintf(s, e - s, "\nBroker Bootup Statistics:\n");
    s += snprintf(s, e - s, "%.*s\n", total_width, separator);

    s += snprintf(s, e - s, "%-*s%-*s%*s\n",
                 stage_width, "Stage",
                 status_width, "Status",
                 duration_width, "Duration");
    s += snprintf(s, e - s, "%.*s\n", total_width, separator);

    for (int i = 1; i < ZPN_BROKER_BOOT_STAGES_MAX; i++) {
        const char* status;
        float duration_ms = 0.0;

        if (zpn_broker_global_bootup_stats[i].start_time == 0) {
            status = "Not Started";
        } else if (zpn_broker_global_bootup_stats[i].end_time == 0) {
            status = "In Progress";
            duration_ms = (epoch_us() - zpn_broker_global_bootup_stats[i].start_time) / 1000.0;
        } else {
            status = "Completed";
            duration_ms = (zpn_broker_global_bootup_stats[i].end_time - zpn_broker_global_bootup_stats[i].start_time) / 1000.0;
            total_ms += duration_ms;
        }

        s += snprintf(s, e - s, "%-*s%-*s%*.3f ms\n",
                     stage_width, zpn_broker_global_bootup_stats[i].name,
                     status_width, status,
                     duration_width - 3, duration_ms);
    }

    s += snprintf(s, e - s, "%.*s\n", total_width, separator);

    if (zpn_broker_global_bootup_stats[ZPN_BROKER_NETWORK_INIT_STAGE_7].end_time > 0) {
        s += snprintf(s, e - s, "%-*s%-*s%*.3f ms (%.3f seconds)\n\n",
                     stage_width, "Total Time:",
                     status_width, "",
                     duration_width - 3, total_ms,
                     total_ms/1000.0);
    }

    ZDP("%s", tmp_buf);
    return 0;
}

void zpn_broker_update_bootup_stage_stats(enum zpn_broker_boot_stage stage, enum zpn_broker_stage_state state)
{
    if (stage >= ZPN_BROKER_BOOT_STAGES_MAX) {
        return;
    }

    if (state == STAGE_START) {
        zpn_broker_global_bootup_stats[stage].start_time = epoch_us();
    } else {
        zpn_broker_global_bootup_stats[stage].end_time = epoch_us();
    }
}

/* Send leg report from Broker/PB to Combiner (Connector) */
int zpn_broker_pb_send_leg_report_to_combiner(char *mtunnel_id, uint64_t mtunnel_id_hash, int64_t incarnation, void *object, void *cached_tlv)
{
    int res = ZDX_RESULT_NO_ERROR;
    struct zpn_broker_mtunnel *mtunnel;
    int bucket_id = -1;

    if (!mtunnel_id || !object) {
        ZPN_LOG(AL_ERROR, "Invalid null mtunnel_id or object");
        return ZPN_RESULT_BAD_ARGUMENT;
    }

    mtunnel = mtunnel_lookup_and_bucket_lock(mtunnel_id, mtunnel_id_hash, &bucket_id);

    if (mtunnel) {

        struct zpn_tlv *tlv = NULL;

        if (mtunnel->promoted_to_public) {

            /* Connection now via public broker */

            struct zpn_pb_client *pb_client = zpn_pb_client_get_pb_client_from_mtunnel(mtunnel);

            if (!pb_client) {
                ZDX_LOG(AL_ERROR, "Received expired callback... no pb_client associated with mtunnel %s", mtunnel_id);
                mtunnel_bucket_unlock(bucket_id);
                return ZDX_RESULT_BAD_STATE;
            }

            tlv = zpn_pb_get_tlv(pb_client);

            ZPN_LOG(AL_INFO, "Connection via public broker for mtunnel_id: %s", mtunnel_id);
        } else {
            tlv = zpn_broker_mtunnel_assistant_tlv(mtunnel);

            ZPN_LOG(AL_DEBUG, "Connection direct via connector for mtunnel_id: %s", mtunnel_id);
        }

        if (!tlv) {
            ZPN_LOG(AL_ERROR, "Cannot find %s tlv for mtunnel_id: %s",
                    mtunnel->promoted_to_public ? "broker" : "assistant",
                    mtunnel_id);
            mtunnel_bucket_unlock(bucket_id);
            return ZDX_RESULT_BAD_STATE;
        }

        if ((void *)tlv != cached_tlv) {
            ZPN_LOG(AL_ERROR, "Cached tlv: %p and current tlv: %p do not match for mtunnel_id: %s",
                    tlv,
                    cached_tlv,
                    mtunnel_id);
            mtunnel_bucket_unlock(bucket_id);
            return ZDX_RESULT_BAD_STATE;
        }

        if (zpn_tlv_incarnation_validation(tlv, incarnation) != ZPN_RESULT_NO_ERROR) {
            ZDX_LOG(AL_ERROR,
                    "Received expired callback... ignoring tlv=%p expected_incarnation=%"PRId64" actual_incarnation=%"PRId64"",
                    tlv,
                    incarnation,
                    zpn_tlv_incarnation(tlv));
            mtunnel_bucket_unlock(bucket_id);
            return ZDX_RESULT_BAD_STATE;
        }

        ZPN_LOG(AL_INFO, "COMBINER: sending leg: %s", zpn_tlv_description(tlv));

        /* Send to remote combiner */
        res = tlv_argo_serialize(tlv, zpn_zdx_probe_legs_info_description, object, 0, fohh_queue_element_type_control);
        mtunnel_bucket_unlock(bucket_id);

        if (res) {
            ZDX_LOG(AL_ERROR, "Unable to send to remote combiner, res: %s",
                    zpath_result_string(res));
            return res;
        }

    } else {
        ZPN_LOG(AL_ERROR, "%s : No mtunnel found for mtunnel_id", mtunnel_id);
    }

    return res;
}

int zpn_broker_is_asst_zistats_upload_disabled_for_customer(int64_t customer_gid)
{
    int64_t config_value = 0;

    config_value = zpath_config_override_get_config_int(ASST_ZISTATS_UPLOAD_CONFIG,
                                                        &config_value,
                                                        DEFAULT_ASST_ZISTATS_UPLOAD_DISABLED,
                                                        customer_gid,
                                                        (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                        (int64_t)0);

    return config_value?1:0;
}

int zpn_broker_is_pbroker_zistats_upload_disabled_for_customer(int64_t customer_gid)
{
    int64_t config_value = 0;

    config_value = zpath_config_override_get_config_int(PBROKER_ZISTATS_UPLOAD_CONFIG,
                                                        &config_value,
                                                        DEFAULT_PBROKER_ZISTATS_UPLOAD_DISABLED,
                                                        customer_gid,
                                                        (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                        (int64_t)0);

    return config_value?1:0;
}

int zpn_broker_is_sitec_zistats_upload_disabled_for_customer(int64_t customer_gid)
{
    int64_t config_value = 0;

    config_value = zpath_config_override_get_config_int(SITEC_ZISTATS_UPLOAD_CONFIG,
                                                        &config_value,
                                                        DEFAULT_SITEC_ZISTATS_UPLOAD_DISABLED,
                                                        customer_gid,
                                                        (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                        (int64_t)0);

    return config_value?1:0;
}

int zpn_broker_check_atleast_one_client_exists(int64_t customer_gid)
{
    int max_fohh_thread;
    int count = 0;
    struct zpn_broker_cgid_to_cstate_per_thread *cgid_cstate = &(broker.cgid_to_cstate_per_thread);
    struct zpn_broker_connected_customer_clients* gid_client_list;
    struct zpn_broker_client_fohh_state *c_state;
    struct zpn_broker_connected_clients *connected_clients = &(broker.clients);

    pthread_mutex_lock(&(connected_clients->lock));

    max_fohh_thread = fohh_thread_count();
    for (int curr_fohh_thread = 0; curr_fohh_thread < max_fohh_thread; curr_fohh_thread++) {
        gid_client_list = zhash_table_lookup(cgid_cstate->customer_gid[curr_fohh_thread], &customer_gid, sizeof(customer_gid), NULL);
        if (gid_client_list == NULL) {
            continue;
        }

        LIST_FOREACH(c_state, &(gid_client_list->customer_client_list), customer_gid_entry) {
            count++;
            break;
        }
        if (count > 0) {
            break;
        }
    }

    pthread_mutex_unlock(&(connected_clients->lock));
    return count;
}

int zpn_broker_dtls_mtu(int64_t target_id) {
    struct zpn_assistantgroup_assistant_relation *ag_relation;
    int64_t mtu = 0;
    size_t count = 1;
    int res;

    if(ZPN_BROKER_IS_PRIVATE()) {
        int64_t customer_gid = ZPATH_GID_GET_CUSTOMER_GID(g_broker_common_cfg->private_broker.broker_id);
        mtu = zpath_config_override_get_config_int(DTLS_FEATURE_MTU,
                                                   &mtu,
                                                   DEFAULT_STREAM_MTU,
                                                   g_broker_common_cfg->private_broker.broker_id,
                                                   g_broker_common_cfg->private_broker.pb_group_id,
                                                   customer_gid,
                                                   (int64_t)0);
        if (mtu != DEFAULT_STREAM_MTU) {
            ZPN_DEBUG_COR("MTU config for DTLS(zrdt) private broker connections (pbroker gid %ld) is %ld", (long)target_id, (long)mtu);
        }
    } else {
        res = zpn_assistantgroup_assistant_relation_get_by_assistant(target_id,
                                                                     &ag_relation,
                                                                     &count,
                                                                     NULL,
                                                                     NULL,
                                                                     0);
        /*
        * for the dtls session mtu the order of overrides is
        * - connector id
        * - connector group id
        * - customer id
        * We don't use the root customer id or ZPATH_GLOBAL_CONFIG_OVERRIDE_GID because there is no reason to
        * apply the MTU override globally across all customers.
        *
        * target_id/assistant_id to assistant group id should have been fetched during the authentication. If its not
        * readily available for some reason use just the assistant id
        */
        if (res) {
            ZPN_DEBUG_COR("Getting assistant group id failed for assistant %ld", (long)target_id);

            mtu = zpath_config_override_get_config_int(DTLS_FEATURE_MTU,
                                                       &mtu,
                                                       DEFAULT_STREAM_MTU,
                                                       target_id,
                                                       ZPATH_GID_GET_CUSTOMER_GID(target_id),
                                                       (int64_t)0);
        } else {
            mtu = zpath_config_override_get_config_int(DTLS_FEATURE_MTU,
                                                       &mtu,
                                                       DEFAULT_STREAM_MTU,
                                                       target_id,
                                                       ag_relation->assistant_group_id,
                                                       ZPATH_GID_GET_CUSTOMER_GID(target_id),
                                                       (int64_t)0);
        }

        if (mtu != DEFAULT_STREAM_MTU) {
            ZPN_DEBUG_COR("MTU config for DTLS(zrdt) assistant connections (assistant gid %ld) is %ld", (long)target_id, (long)mtu);
        }
    }

    return mtu;
}

int zpn_broker_socket_listen_client_init(void) {
    struct zpath_instance *instance = NULL;
    int64_t private_broker_gid = 0; /* 0 for public broker */
    int i;
    int res;


    if (g_broker_common_cfg->instance_type == ZPN_INSTANCE_TYPE_PUBLIC_BROKER) {
        /*
         * Attach all the IPs that we must...
         *
         * Port 443 clean.
         * Port 8443 on proxy protocol.
         */
         instance = zpath_instance_global_state.current_config;
         if (!instance) {
            ZPN_LOG(AL_CRITICAL, "No instance configuration for broker_init");
            return ZPN_RESULT_ERR;
        }

        for (i = 0; i < instance->ips_count; i++) {
            char str[ARGO_INET_ADDRSTRLEN];

            struct sockaddr_storage addr;
            socklen_t addr_len;

            argo_inet_to_sockaddr(&(instance->ips[i]), (struct sockaddr *)&addr, &addr_len, htons(ZPN_ASSISTANT_BROKER_PORT));
            res = fohh_generic_server_listen(sni_server, (struct sockaddr *)&addr, addr_len, 0);
            if (res) {
                ZPN_LOG(AL_ERROR, "Could not listen for clients, port %d, no proxy protocol, IP %s, reason = %s",
                        ZPN_ASSISTANT_BROKER_PORT, argo_inet_generate(str, &(instance->ips[i])), zpn_result_string(res));
                /* Don't error out- minor misconfigurations can happen- they should catch these logs */
            } else {
                ZPN_LOG(AL_NOTICE, "Listening for clients, port %d, no proxy protocol, IP %s",
                        ZPN_ASSISTANT_BROKER_PORT, argo_inet_generate(str, &(instance->ips[i])));
            }

            argo_inet_to_sockaddr(&(instance->ips[i]), (struct sockaddr *)&addr, &addr_len, htons(ZPN_ASSISTANT_BROKER_PORT_PROXY_PROTOCOL));
            res = fohh_generic_server_listen(sni_server, (struct sockaddr *)&addr, addr_len, 1);
            if (res) {
                ZPN_LOG(AL_ERROR, "Could not listen for clients, port %d, proxy protocol, IP %s, reason = %s",
                        ZPN_ASSISTANT_BROKER_PORT_PROXY_PROTOCOL, argo_inet_generate(str, &(instance->ips[i])), zpn_result_string(res));
                /* Don't error out- minor misconfigurations can happen- they should catch these logs */
            } else {
                ZPN_LOG(AL_NOTICE, "Listening for clients, port %d, proxy protocol, IP %s",
                        ZPN_ASSISTANT_BROKER_PORT_PROXY_PROTOCOL, argo_inet_generate(str, &(instance->ips[i])));
            }
        }
        /* Let zthread layer know about the listening socket FDs so that they can be speedily closed in signal handlers */
        int fd_list[SOCK_FD_ARRAY_MAX] = {-1, -1, -1, -1, -1, -1, -1, -1, -1, -1};
        if (fohh_generic_server_obtain_sockfd(sni_server, fd_list, sizeof(fd_list)/sizeof(fd_list[0])) == ZPN_RESULT_NO_ERROR) {
            zthread_set_socket_fds(fd_list, sizeof(fd_list)/sizeof(fd_list[0]));
            ZPN_LOG(AL_NOTICE, "Informed zthread about socket FDs on SNI server");
        } else {
            ZPN_LOG(AL_NOTICE, "Failed to inform zthread about socket FDs on SNI server");
        }
    } else if (g_broker_common_cfg->instance_type == ZPN_INSTANCE_TYPE_PRIVATE_BROKER) {
        /* Extract private broker values, these will be null for public broker */
        private_broker_gid = g_broker_common_cfg->private_broker.broker_id;
        ZPN_BROKER_ASSERT_HARD(private_broker_gid != 0, "Private broker gid is 0!");

        struct zpn_private_broker *pb;
        struct argo_inet localhost[2];
        struct argo_inet *listen_ips;
        int listen_ips_count = 0;
        int successful_listen_ips_count;
        int j;
        int listen_all_ips = 0;

        res = zpn_private_broker_get_by_id_wait(private_broker_gid, &pb);
        if (res) {
            ZPN_LOG(AL_ERROR, "Could not get private broker");
            return ZPN_RESULT_ERR;
        }

        if (pb->listen_ips_count) {
            listen_ips = pb->listen_ips;
            listen_ips_count = pb->listen_ips_count;

            g_broker_common_cfg->private_broker.listen_ips_count = listen_ips_count;
            for (j = 0; j < listen_ips_count; j++) {
                char str[ARGO_INET_ADDRSTRLEN] = {0};
                struct argo_inet l_ip = pb->listen_ips[j];
                /* Validate each listen ips, fallback to listening on all ips if any bad ip config detected */
                if (validate_listen_ip(&l_ip) != ZPN_RESULT_NO_ERROR) {
                    listen_all_ips = 1;
                    ZPN_LOG(AL_CRITICAL, "Invalid listen address:%s, fallback to listening on all ips", argo_inet_generate(str, &l_ip));
                    break;
                }
            }
        } else {
            /* Listen on all ips */
            listen_all_ips = 1;
        }

        if (listen_all_ips) {
            listen_ips = localhost;
            listen_ips_count = 2;
            argo_string_to_inet("0.0.0.0", &(localhost[0]));
            argo_string_to_inet("::", &(localhost[1]));
            ZPN_LOG(AL_INFO, "Listening on all ips, port:%d", ZPN_CLIENT_BROKER_PORT);
        }

        successful_listen_ips_count = 0;
        size_t bytes_written = 0;
        size_t buf_size = (ARGO_INET_ADDRSTRLEN * listen_ips_count) + listen_ips_count;
        char *bad_listen_ips = ZPN_EVENT_CALLOC((unsigned int)buf_size);
        if (!bad_listen_ips) {
            ZPN_LOG(AL_ERROR, "calloc failed in creation of bad_listen_ips");
        }

        for (i = 0; i < listen_ips_count; i++) {

            char str[ARGO_INET_ADDRSTRLEN];
            struct argo_inet *listen_ip = &(listen_ips[i]);

            struct sockaddr_storage addr;
            socklen_t addr_len;

            argo_inet_to_sockaddr(listen_ip, (struct sockaddr *)&addr, &addr_len, htons(ZPN_CLIENT_BROKER_PORT));
            res = fohh_generic_server_listen(sni_server,(struct sockaddr *)&addr, addr_len, 0);
            if (res) {
                char *list_ip = argo_inet_generate(str, listen_ip);
                if (bad_listen_ips) {
                    bytes_written += snprintf(bad_listen_ips + bytes_written, (buf_size - bytes_written), "%s ", list_ip);
                }

                ZPN_LOG(AL_ERROR, "Could not listen for clients, port %d, IP %s, reason = %s",
                        ZPN_CLIENT_BROKER_PORT, argo_inet_generate(str, listen_ip), zpn_result_string(res));
            } else {
                successful_listen_ips_count++;
                ZPN_LOG(AL_NOTICE, "Listen for clients, port %d, IP %s",
                        ZPN_CLIENT_BROKER_PORT, argo_inet_generate(str, listen_ip));
            }
        }
        if (0 == successful_listen_ips_count) {
            struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();
            res = zpn_event_system_listen_ip_bind_failure_notify(bad_listen_ips,
                                                                 buf_size,
                                                                 zpn_event_system_pb,
                                                                 private_broker_gid,
                                                                 g_broker_common_cfg->private_broker.pb_group_id,
                                                                 ZPATH_GID_GET_CUSTOMER_GID(private_broker_gid),
                                                                 gs->private_broker_scope_id == gs->customer_id ? 0 : gs->private_broker_scope_id);
            if (res) {
                ZPN_EVENT_LOG(AL_ERROR, "Failed to notify listen ip bind failure event: %s", zpn_result_string(res));
            } else {
                sleep(1);
            }
            if (bad_listen_ips) {
                ZPN_EVENT_FREE(bad_listen_ips);
            }
            ZPN_BROKER_ASSERT_HARD(0,
                                   "Couldn't listen on any ports, may be bad listen IP config?");
        }

        if (bad_listen_ips) {
            ZPN_EVENT_FREE(bad_listen_ips);
        }
    }
    return ZPN_RESULT_NO_ERROR;

}

int zpn_broker_assistant_listen_socket(void) {
    int res = zpn_broker_assistant_listen(sni_server, g_broker_common_cfg->instance_type);
    return res;
}

int zpn_broker_client_listen_socket(int run_deferred) {
    int res;
    /* Let everything register on our server before we really start listening. */
    if (run_deferred) {
        res = zpn_broker_client_listen(sni_server);
    } else {
        res = zpn_broker_client_listen_standard(sni_server);
    }
    return res;
}

void zpn_broker_verify_alt_cloud_info_callback(struct fohh_connection *f_conn,
                                              const char **capabilities,
                                              int capabilities_count,
                                              char *current_alt_cloud)
{
    int alt_cloud_aware = 0;

    for (int i = 0; i < capabilities_count; i++) {
        if (strcmp(capabilities[i], ZPN_CLIENT_CAPABILITY_ALT_CLOUD_AWARE_STR) == 0) {
            alt_cloud_aware = 1;
            break;
        }
    }

    if (!alt_cloud_aware) {
        ZPN_LOG(AL_DEBUG, "connection %s is not alt cloud aware", fohh_description(f_conn));
        return;
    }

    fohh_connection_set_alt_cloud_aware(f_conn, alt_cloud_aware);
    ZPN_LOG(AL_DEBUG, "connection %s is alt cloud aware", fohh_description(f_conn));

    if (ZPN_BROKER_IS_PUBLIC()) {
        if (!zpn_broker_is_alt_cloud_valid(current_alt_cloud)) {
            fohh_connection_set_redirect_for_alt_cloud_change(f_conn, 1);
        }
    }

    return;
}

void zpn_broker_zpm_incr_disconnect_count()
{
    __sync_fetch_and_add_8(&g_zpn_broker_zpm_conn_stats.disconnect_count, 1);
}

void zpn_broker_zpm_incr_connect_count()
{
    __sync_fetch_and_add_8(&g_zpn_broker_zpm_conn_stats.connect_count, 1);
}

void zpn_broker_zpm_incr_connect_attempt_count()
{
    __sync_fetch_and_add_8(&g_zpn_broker_zpm_conn_stats.connect_attempt_count, 1);
}

void zpn_broker_zpm_connect_attempt_count_reset()
{
    g_zpn_broker_zpm_conn_stats.connect_attempt_count = 0;
}

STATIC_INLINE int64_t zpath_wrapper_config_override_get_config_int(const char* config_key,
                                                                    int64_t* config_value,
                                                                    int64_t config_default_value,
                                                                    int64_t global_gid,
                                                                    int64_t root_customer_gid,
                                                                    int64_t default_global_gid,
                                                                    int64_t default_customer_gid) {
    return zpath_config_override_get_config_int(config_key, config_value, config_default_value, global_gid, root_customer_gid, default_global_gid, default_customer_gid);
}

/* BROKER_FEATURE_AUTH_LOG_REDIRECTION_STATUS is the feature flag for supporting redirect reason in the auth log close_reason.
 * This method checks if the feature is enabled.
 */
int64_t zpn_is_broker_auth_log_redirect_status_feature_enabled()
{
    int64_t config_value = 0;

    /* Get the value of feature config override, if its not hard disabled: by default 0.*/
    if (ZPN_BROKER_IS_PUBLIC()) {
        int64_t root_customer_gid = ZPATH_GID_GET_CUSTOMER_GID(zpath_instance_global_state.current_config->gid);
        config_value = zpath_wrapper_config_override_get_config_int(BROKER_FEATURE_AUTH_LOG_REDIRECTION_STATUS,
                                                                        &config_value,
                                                                        DEFAULT_BROKER_FEATURE_AUTH_LOG_REDIRECTION_STATUS,
                                                                        zpath_instance_global_state.current_config->gid,
                                                                        root_customer_gid,
                                                                        (int64_t) ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                                        (int64_t) 0 );
    }
    ZPN_LOG(AL_DEBUG, "broker_auth_log_redirect_status_feature config override is set to %"PRId64" ", config_value);

    return config_value;
}

int64_t zpn_is_redirect_reason_maintanence(const char* redirect_reason)
{
    int is_maintanence = 0;

    if(redirect_reason != NULL &&
       (!strcmp(redirect_reason, BRK_REDIRECT_REASON_BROKER_RESTART) || !strcmp(redirect_reason, BRK_REDIRECT_REASON_BROKER_MAINTENANCE))) {
        is_maintanence = 1;
        ZPN_LOG(AL_DEBUG, "Redirect reason is %s", redirect_reason);
    }

    return is_maintanence;
}

int zpn_broker_send_mission_critical_resp(struct fohh_connection *f_conn, enum zpn_firedrill_status firedrill_status, int64_t firedrill_interval)
{
    struct zpn_broker_mission_critical_resp mc_resp = {0};

    ZPN_LOG(AL_NOTICE, "firedrill: sending mission critical rpc response");

    mc_resp.firedrill_interval = firedrill_interval;
    mc_resp.firedrill_status = firedrill_status;

    /* send the response rpc now */
    if(zpn_broker_mission_critical_ack(f_conn, 0, &mc_resp)) {
        ZPN_LOG(AL_ERROR, "firedrill: sending mission critical rpc response failed");
        return ZPATH_RESULT_ERR;
    }
    return ZPATH_RESULT_NO_ERROR;
}

int zpn_broker_mission_critical_cb(void *argo_cookie_ptr,
                                    void *argo_structure_cookie_ptr,
                                    struct argo_object *object)
{
    int res = 0;
    struct zpn_site *site = NULL;
    struct zpn_broker_mission_critical *mc_msg = object->base_structure_void;
    struct fohh_connection *f_conn = argo_structure_cookie_ptr;

    ZPN_LOG(AL_NOTICE, "firedrill zpn_broker_mission_critical_cb entered");

    if(!mc_msg->site_gid) {
        ZPN_LOG(AL_ERROR, "site gid is NULL");
        return ZPN_RESULT_ERR;
    }

    /* query the zpn_site table with site_gid */
    if (zpn_site_get_by_gid(mc_msg->site_gid, &site, 1, NULL, NULL, 0)) {
        ZPN_LOG(AL_ERROR, "Could not get site for site_gid = %"PRId64"", mc_msg->site_gid);
        return ZPN_RESULT_ERR;
    }

    if(!site) {
        ZPN_LOG(AL_ERROR, "query for site info for site_gid %"PRId64" failed", mc_msg->site_gid);
        return ZPN_RESULT_ERR;
    }

    ZPN_LOG(AL_ERROR, "firedrill %s for site_gid = %"PRId64"", ((site->firedrill_enabled)?"enabled":"disabled"), mc_msg->site_gid);

    if(site && site->firedrill_enabled) {

        /* fetch the interval from the firedrill table */
        struct zpn_firedrill_site  *firedrill_config = NULL;
        int64_t customer_gid = ZPATH_GID_GET_CUSTOMER_GID(fohh_peer_get_id(f_conn));

        res = zpn_firedrill_site_by_site_gid(mc_msg->site_gid, customer_gid, NULL, &firedrill_config);
        if(res) {
            ZPN_LOG(AL_ERROR, "firedrill config fetch failed for site_gid = %"PRId64" res: %s",mc_msg->site_gid, zpn_result_string(res));
            return res;
        }

        if(!firedrill_config) {
            ZPN_LOG(AL_ERROR, "firedrill_config is async %"PRId64" failed", mc_msg->site_gid);
            return ZPN_RESULT_ERR;
        }

        res = zpn_broker_send_mission_critical_resp(f_conn, FIREDRILL_ENABLED, firedrill_config->firedrill_interval_s);
        if(res) {
            ZPN_LOG(AL_ERROR, "firedrill enabled: mission critical rpc response for site_gid %"PRId64" failed", mc_msg->site_gid);
            return ZPN_RESULT_ERR;
        }
        return ZPN_RESULT_NO_ERROR;
    } else {
        res = zpn_broker_send_mission_critical_resp(f_conn, FIREDRILL_DISABLED, 0);
        if(res) {
            ZPN_LOG(AL_ERROR, "firedrill disabled: mission critical rpc response for site_gid %"PRId64" failed", mc_msg->site_gid);
            return ZPN_RESULT_ERR;
        }
    }
    ZPN_LOG(AL_ERROR, "firedrill mission critical rpc response successful");
    return ZPN_RESULT_NO_ERROR;
}
