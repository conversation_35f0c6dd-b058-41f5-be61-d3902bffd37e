/*
 * zpn_broker_sitec.c. Copyright (C) 2024 Zscaler Inc. All Rights Reserved
 */

#include "zpn/zpn_broker_sitec.h"

#include "base64/base64.h"
#include "fohh/fohh.h"
#include "fohh/fohh_private.h"
#include <openssl/rand.h>
#include "wally/wally.h"
#include "wally/wally_column_nullify.h"
#include "wally/wally_filter_table.h"
#include "wally/wally_fohh_server.h"
#include "wally/wally_fohh_rpc_proxy.h"
#include "zhash/zhash_table.h"
#include "zpath_lib/zpath_app.h"
#include "zpath_lib/zpath_cloud.h"
#include "zpath_lib/zpath_config_override.h"
#include "zpath_lib/zpath_config_override_keys.h"
#include "zpath_lib/zpath_customer.h"
#include "zpath_lib/zpath_debug.h"
#include "zpath_lib/zpath_domain_lookup.h"
#include "zpath_lib/zpath_instance.h"
#include "zpath_lib/zpath_local.h"
#include "zpath_lib/zpath_log_store.h"
#include "zpath_lib/zpath_feature_flag_keys.h"
#include "admin_probe/admin_probe_rpc.h"

#include "fohh/fohh_log.h"
#include "zpath_lib/zpath_et_wally_userdb.h"
#include "zpn/zpn_application.h"
#include "zpn/zpn_application_domain.h"
#include "zpn/zpn_broker.h"
#include "zpn/zpn_broker_assert.h"
#include "zpn/zbalance/zpn_balance_redirect.h"
#include "zpn/zpn_broker_client.h"
#include "zpn/zpn_broker_dispatch.h"
#include "zpn/zpn_broker_dns.h"
#include "zpn/zpn_broker_mtunnel.h"
#include "zpn/zpn_broker_private.h"
#include "zpn/zpn_broker_siem.h"
#include "zpn/zpn_issuedcert.h"
#include "zpn/zpn_lib.h"
#include "zpn/zpn_sitec_group.h"
#include "zpn/zpn_sitec_to_group.h"
#include "zpn/zpn_sitec_table.h"
#include "zpn/zpn_rpc.h"
#include "zpn/zpn_signing_cert.h"
#include "zpn/zpn_broker_cryptosvc.h"
#include "zpn/zpn_broker_pbroker.h"
#include "zpn/zpn_broker_assistant.h"
#include "zpn_waf/zpn_waf_log.h"
#include "zpn_waf/zpn_waf_log_compiled.h"
#include "zpn_waf/zpn_app_inspection_log.h"
#include "zpn_waf/zpn_app_inspection_log_compiled.h"
#include "zpn/zpn_broker_assistant_stats.h"
#include "zpn/zpn_broker_pbroker_stats.h"
#include "zpn/zpn_assistantgroup_assistant_relation.h"
#include "zpn/zpn_pbroker_to_group.h"
#include "zpn_event/zpn_event.h"
#include "zpn_event/zpn_event_data.h"
#include "zpn_event/zpn_event_stats.h"
#include "zpath_lib/zpath_et_service_endpoint.h"

#ifdef ZPN_TESTING
#include "test_misc/testing_macros.h"
#include "zpn/gtests/zpn_broker_maintanence_auth_log_tests/test_headers/zpn_broker_maintanence_auth_log_tests_weak_headers.h"

static void zpn_broker_sitec_ctrl_conn_monitor_cb(evutil_socket_t sock, short flags, void *cookie);
static int zpn_broker_sitec_ctrl_conn_callback(struct fohh_connection *connection,
                                               enum fohh_connection_state state,
                                               void *cookie);

void zpath_wrapper_zpn_broker_sitec_ctrl_conn_monitor_cb(evutil_socket_t sock, short flags, void *cookie) {
    zpn_broker_sitec_ctrl_conn_monitor_cb(sock, flags, cookie);
}
int zpath_wrapper_zpn_broker_sitec_ctrl_conn_callback(struct fohh_connection *connection,
                                                      enum fohh_connection_state state,
                                                      void *cookie) {
    return zpn_broker_sitec_ctrl_conn_callback(connection, state, cookie);
}
#else
#include "test_misc/production_macros.h"
#endif // ZPN_TESTING

STATIC_INLINE void zpath_wrapper_zpn_free(void* ptr, int line, const char* file) {
    zpath_free(ptr, line, file);
}

extern char *geoip_db_file;
extern struct argo_structure_description *zpn_broker_mission_critical_description;

struct scgid_to_ctrl_tunnel_info {
    char tunnel_id[ZPN_TUNNEL_ID_BYTES_TEXT + 1];
    int64_t sc_gid;
    uint64_t debug_flag;
    struct fohh_connection *f_conn;
    int64_t f_conn_incarnation;
};

struct scgid_to_ctrl_tunnel {
    zpath_mutex_t lock;
    struct zhash_table *table;
};

enum sitec_config_conn_type {
    sitec_config,
    sitec_static_config,
    sitec_override,
    sitec_userdb
};

struct connected_sitec_config_fohh_state {
    const char *type;
    struct fohh_connection *f_conn;

    struct event *timer;

    /* 128 bit (16 bytes, 24 bytes base64) random ID for this TLS
     * connection, in base64 */
    uint8_t tunnel_id_bin[ZPN_TUNNEL_ID_BYTES];
    char tunnel_id[ZPN_TUNNEL_ID_BYTES_TEXT + 1];
    uint64_t monitor_count;
};

struct sitec_log_info {
    struct fohh_connection *f_conn;
    int64_t f_conn_incarnation;

    /* 128 bit (16 bytes, 24 bytes base64) random ID for this TLS
     * connection, in base64 */
    uint8_t tunnel_id_bin[ZPN_TUNNEL_ID_BYTES];
    char tunnel_id[ZPN_TUNNEL_ID_BYTES_TEXT + 1];
    uint64_t monitor_count;
};

struct sitec_stats_proxy_info {
    struct fohh_connection *f_conn;
    int64_t f_conn_incarnation;

    struct event *timer;

    /* 128 bit (16 bytes, 24 bytes base64) random ID for this TLS
     * connection, in base64 */
    uint8_t tunnel_id_bin[ZPN_TUNNEL_ID_BYTES];
    char tunnel_id[ZPN_TUNNEL_ID_BYTES_TEXT + 1];
    uint64_t monitor_count;

    int64_t customer_gid;
    uint64_t rx_stats_upload;
};

LIST_HEAD(sitec_stats_info_head, sitec_stats_info);
struct sitec_stats_info {
    int64_t sitec_gid_from_config;
    struct fohh_connection *f_conn;
    int64_t f_conn_incarnation;

    int64_t g_sitec_grp;

    /* 128 bit (16 bytes, 24 bytes base64) random ID for this TLS
     * connection, in base64 */
    uint8_t tunnel_id_bin[ZPN_TUNNEL_ID_BYTES];
    char tunnel_id[ZPN_TUNNEL_ID_BYTES_TEXT + 1];

    int64_t customer_gid;
    struct event *timer;

    LIST_ENTRY(sitec_stats_info) list_entry;
    int in_list;

    uint64_t rx_stats_upload;
    uint64_t monitor_count;
};

struct stats_connected_sitecs {
    struct zpath_mutex lock;
    struct sitec_stats_info_head sitec_list;
};

static struct stats_connected_sitecs sitecs_stats;

static int zpn_broker_sitec_stats_conn_callback(struct fohh_connection *connection,
                                                enum fohh_connection_state state,
                                                void *cookie);
static int zpn_broker_sitec_stats_unblock_callback(struct fohh_connection *connection,
                                                   enum fohh_queue_element_type element_type,
                                                   void *cookie);
static void zpn_broker_sitec_stats_conn_info_callback(struct fohh_connection *f_conn, void *cookie);
static int zpn_broker_sitec_stats_conn_redirect(struct fohh_connection *f_conn);

#define MAX_ZPN_BROKER_SITEC_SNIS 32
#define ZPN_GLOBAL_INSPECTION_CONFIG_GID 1

int zpn_broker_sitec_snis_count = 0;
char* zpn_broker_sitec_snis[MAX_ZPN_BROKER_SITEC_SNIS];
int zpn_broker_sitec_sni_flags[MAX_ZPN_BROKER_SITEC_SNIS];

static int disconnect_sitec = 0;
static const char* disconnect_sitec_reason = NULL;
static int redirect_sitec = 0;
static const char* redirect_sitec_reason = NULL;
static struct scgid_to_ctrl_tunnel sc_gid_to_tunnel;
static struct wally_fohh_server *wally_server_global;

int zpn_broker_sc_gid_to_ctrl_tunnel(int64_t sc_gid,
                                     char *ret_tunnel_id,
                                     size_t ret_tunnel_id_len)
{
    int res = ZPN_RESULT_NOT_FOUND;
    struct scgid_to_ctrl_tunnel_info *info = NULL;

    ZPATH_MUTEX_LOCK(&sc_gid_to_tunnel.lock, __FILE__, __LINE__);
    info = zhash_table_lookup(sc_gid_to_tunnel.table,
                              &sc_gid,
                              sizeof(sc_gid),
                              NULL);
    if (info) {
        snprintf(ret_tunnel_id, ret_tunnel_id_len, "%s", info->tunnel_id);
        res = ZPN_RESULT_NO_ERROR;
    }
    ZPATH_MUTEX_UNLOCK(&sc_gid_to_tunnel.lock, __FILE__, __LINE__);

    return res;
}

int zpn_broker_sc_gid_to_connection(int64_t sc_gid,
                                    struct fohh_connection **conn,
                                    int64_t *conn_incarnation)
{
    int res = ZPN_RESULT_NOT_FOUND;
    struct scgid_to_ctrl_tunnel_info *info = NULL;

    ZPATH_MUTEX_LOCK(&sc_gid_to_tunnel.lock, __FILE__, __LINE__);
    info = zhash_table_lookup(sc_gid_to_tunnel.table,
                              &sc_gid,
                              sizeof(sc_gid),
                              NULL);
    if (info) {
        *conn = info->f_conn;
        *conn_incarnation = info->f_conn_incarnation;
        res = ZPN_RESULT_NO_ERROR;
    }
    ZPATH_MUTEX_UNLOCK(&sc_gid_to_tunnel.lock, __FILE__, __LINE__);

    return res;
}

int zpn_broker_store_tunnel_info_by_sc_gid(int64_t sc_gid,
                                           char *tunnel_id,
                                           struct fohh_connection *f_conn)
{
    struct scgid_to_ctrl_tunnel_info *info = ZPN_CALLOC(sizeof(*info));

    if (!info) {
        ZPN_LOG(AL_CRITICAL, "Memory alloc failed");
        return ZPN_RESULT_NO_MEMORY;
    }

    info->f_conn = f_conn;
    info->f_conn_incarnation = fohh_connection_incarnation(f_conn);
    info->sc_gid = sc_gid;
    snprintf(info->tunnel_id, sizeof(info->tunnel_id), "%s", tunnel_id);

    ZPATH_MUTEX_LOCK(&sc_gid_to_tunnel.lock, __FILE__, __LINE__);
    zhash_table_store(sc_gid_to_tunnel.table, &sc_gid, sizeof(sc_gid), 0, info);
    ZPATH_MUTEX_UNLOCK(&sc_gid_to_tunnel.lock, __FILE__, __LINE__);

    return ZPN_RESULT_NO_ERROR;
}

void zpn_broker_remove_tunnel_info_by_sc_gid(int64_t sc_gid)
{
    struct scgid_to_ctrl_tunnel_info *info;
    ZPATH_MUTEX_LOCK(&sc_gid_to_tunnel.lock, __FILE__, __LINE__);
    info = zhash_table_lookup(sc_gid_to_tunnel.table,
                              &sc_gid,
                              sizeof(sc_gid),
                              NULL);
    if (info) {
        zhash_table_remove(sc_gid_to_tunnel.table,
                           &sc_gid,
                           sizeof(sc_gid),
                           NULL);
        ZPN_FREE(info);
    }
    ZPATH_MUTEX_UNLOCK(&sc_gid_to_tunnel.lock, __FILE__, __LINE__);
}

STATIC_INLINE void zpath_wrapper_zpn_broker_remove_tunnel_info_by_sc_gid(int64_t sc_gid)
{
    zpn_broker_remove_tunnel_info_by_sc_gid(sc_gid);
}

static int zpn_broker_get_debug_flags_by_sc_gid(int64_t sc_gid,
                                                uint64_t *debug_flag)
{
    int res = ZPN_RESULT_NOT_FOUND;
    struct scgid_to_ctrl_tunnel_info *info = NULL;

    if (!debug_flag) {
        ZPN_LOG(AL_ERROR, "Invalid null debug_flag argument");
        return ZPN_RESULT_BAD_ARGUMENT;
    }

    ZPATH_MUTEX_LOCK(&sc_gid_to_tunnel.lock, __FILE__, __LINE__);
    info = zhash_table_lookup(sc_gid_to_tunnel.table,
                              &sc_gid,
                              sizeof(sc_gid),
                              NULL);
    if (info) {
        *debug_flag = info->debug_flag;
        res = ZPN_RESULT_NO_ERROR;
    }
    ZPATH_MUTEX_UNLOCK(&sc_gid_to_tunnel.lock, __FILE__, __LINE__);

    return res;
}

static int zpn_broker_set_debug_flags_by_sc_gid(int64_t sc_gid,
                                                uint64_t debug_flag)
{
    int res = ZPN_RESULT_NOT_FOUND;
    struct scgid_to_ctrl_tunnel_info *info = NULL;

    ZPATH_MUTEX_LOCK(&sc_gid_to_tunnel.lock, __FILE__, __LINE__);
    info = zhash_table_lookup(sc_gid_to_tunnel.table,
                              &sc_gid,
                              sizeof(sc_gid),
                              NULL);
    if (info) {
        info->debug_flag = debug_flag;
        res = ZPN_RESULT_NO_ERROR;
    }
    ZPATH_MUTEX_UNLOCK(&sc_gid_to_tunnel.lock, __FILE__, __LINE__);

    return res;
}

/* Set sitec debug flags from broker */
int zpn_broker_sitec_debug_flag(struct zpath_debug_state *request_state,
                                  const char **query_values,
                                  int query_value_count,
                                  void *cookie)
{
    int64_t sitec_gid = 0;
    uint64_t debug_flag = 0;
    struct fohh_connection *f_conn = NULL;
    struct zpn_site_controller *sitec = NULL;
    const char *w;
    int res;
    int64_t f_conn_incarnation;

    if (!query_values[0]) {
        return ZPN_RESULT_ERR;
    }

    /* Parse first integer of CN */
    for (w = query_values[0]; *w; w++) {
        if (isdigit(*w)) {
            sitec_gid = strtoll(w, NULL, 0);
            break;
        }
    }

    ZPATH_LOG(AL_DEBUG, "sitec name = %s, id = %"PRId64"",
              query_values[0], sitec_gid);

    /* Fetch pbroker connection */
    res = zpn_broker_sc_gid_to_connection(sitec_gid, &f_conn, &f_conn_incarnation);
    if (res) {
        ZPN_LOG(AL_ERROR, "Cannot find sc_gid:%"PRId64" connection, may be disconnected", sitec_gid);
        zpath_debug_cb_printf_response(request_state, "Cannot find sitec %s, it may not be connected to this broker\n", query_values[0]);
        return ZPN_RESULT_NO_ERROR;
    }

    res = zpn_sitec_get_by_id(sitec_gid, &sitec, NULL, NULL, 0);
    if (res) {
        if (res != ZPN_RESULT_ASYNCHRONOUS) {
            /* Some error. Yuck. */
            ZPN_LOG(AL_ERROR, "%s: Could not get sitec for gid = %"PRId64": %s",
                    fohh_description(f_conn), sitec_gid, zpn_result_string(res));
        }
        return ZPN_RESULT_NO_ERROR;
    }

    /* Dont operate if disabled */
    if (!sitec->enabled) {
        ZPN_LOG(AL_NOTICE, "%s: sitec %"PRId64" is not enabled. Not continuing with setting debug_flag", fohh_description(f_conn), sitec_gid);
        zpath_debug_cb_printf_response(request_state, "Cannot send commad to sitec %s, it is disabled\n", query_values[0]);
        return ZPN_RESULT_NO_ERROR;
    }

    /* Fetch debug flag */
    res = zpn_broker_get_debug_flags_by_sc_gid(sitec_gid, &debug_flag);
    if (res) {
        ZPN_LOG(AL_ERROR, "Unable to get debug_flag for sitec_gid: %"PRId64"", sitec_gid);
        return res;
    }

    {
        int count;

        if (!query_values[1]) {
            zpath_debug_cb_printf_response(request_state, "DEBUG, %s = %"PRIx64"\n", query_values[0], debug_flag);
            for (count = 0; zpn_debug_names[count]; count++) {
                zpath_debug_cb_printf_response(request_state,
                                               "%s : %s\n",
                                               (debug_flag & (1 << count)) ? "ON" : "OFF",
                                               zpn_debug_names[count]);
            }
        } else {
            for (count = 0; zpn_debug_names[count]; count++) {
                if (!strncmp(query_values[1], zpn_debug_names[count], strlen(zpn_debug_names[count]))) {
                    zpath_debug_cb_printf_response(request_state,
                                                   "Debug flag %s was %s, setting it to %s\n",
                                                   query_values[1],
                                                   (debug_flag & (1 << count)) ? "ON" : "OFF",
                                                   (debug_flag & (1 << count)) ? "OFF" : "ON");

                    if (debug_flag & (1 << count)) {
                        debug_flag &= ~(1 << count);
                    } else {
                        debug_flag |= (1 << count);
                    }

                    /* Update sitec */
                    zpn_send_sitec_log_control(f_conn,
                                               fohh_connection_incarnation(f_conn),
                                               sitec_gid,
                                               ZPN_SCCTL_LOG_CONTROL_OPCODE_SET_LOGGING_FLAG,
                                               ZPN_SCCTL_LOG_CONTROL_OPCODE_SET_LOG_UPLOAD_STATE_DEFAULT,
                                               debug_flag);
                    break;
                }
            }

            /* Update debug flag */
            zpn_broker_set_debug_flags_by_sc_gid(sitec_gid, debug_flag);
            if (res) {
                ZPN_LOG(AL_ERROR, "Unable to get debug_flag for sitec_gid: %"PRId64"", sitec_gid);
                return res;
            }

            if (!zpn_debug_names[count]) {
                /* Flag name is wrong */
                zpath_debug_cb_printf_response(request_state, "Cannot find flag %s\n", query_values[1]);
            }
        }
    }

    return ZPN_RESULT_NO_ERROR;
}

static void zpn_broker_sitec_add_sni(char* sni_str, int wildcard) {
    ZPN_ASSERT(zpn_broker_sitec_snis_count < MAX_ZPN_BROKER_SITEC_SNIS);
    zpn_broker_sitec_snis[zpn_broker_sitec_snis_count] = ZPN_STRDUP(sni_str, strlen(sni_str));
    zpn_broker_sitec_sni_flags[zpn_broker_sitec_snis_count] = wildcard;
    zpn_broker_sitec_snis_count++;
}

void zpn_broker_sitec_update_sni(char* old_sni_str, char *new_sni_str, int wildcard) {
    ZPN_ASSERT(zpn_broker_sitec_snis_count < MAX_ZPN_BROKER_SITEC_SNIS);

    for (int count = 0; count < zpn_broker_sitec_snis_count; count++) {
        if (0 == strcmp(zpn_broker_sitec_snis[count], old_sni_str)) {
            ZPN_FREE(zpn_broker_sitec_snis[count]);
            zpn_broker_sitec_snis[count] = ZPN_STRDUP(new_sni_str, strlen(new_sni_str));
            zpn_broker_sitec_sni_flags[count] = wildcard;
            return;
        }
    }
    ZPN_LOG(AL_ERROR, "Could not update broker_sitec sni: %s to %s", old_sni_str, new_sni_str);
}

void zpn_broker_sitec_get_snis(char*** snis_out, int** flags_out, int* count_out)
{
    if (snis_out) {
        *snis_out = zpn_broker_sitec_snis;
    }
    if (flags_out) {
        *flags_out = zpn_broker_sitec_sni_flags;
    }
    if (count_out) {
        *count_out = zpn_broker_sitec_snis_count;
    }
}

void zpn_broker_sitec_set_redirect_sitec_connections(int do_it, const char* reason)
{
    redirect_sitec = do_it;
    redirect_sitec_reason = reason;
}

int zpn_broker_sitec_get_redirect_sitec_flag()
{
    return redirect_sitec;
}

const char *zpn_broker_sitec_get_redirect_sitec_reason()
{
    return redirect_sitec_reason;
}

void zpn_broker_sitec_set_disconnect_sitec_connections(int do_it, const char* reason)
{
    disconnect_sitec = do_it;
    disconnect_sitec_reason = reason;
}

int zpn_broker_sitec_get_disconnect_sitec_flag()
{
    return disconnect_sitec;
}

const char *zpn_broker_sitec_get_disconnect_sitec_reason()
{
    return disconnect_sitec_reason;
}

int zpn_broker_sitec_peer_geoip_lookup(struct fohh_connection *f_conn,
                                         struct argo_inet *peer_ip,
                                         struct site *peer_site)
{
    if (!f_conn || !peer_ip || !peer_site) {
        return ZPN_RESULT_BAD_ARGUMENT;
    }

    memset(peer_ip, 0, sizeof(*peer_ip));
    memset(peer_site, 0, sizeof(*peer_site));

    const int64_t peer_gid = fohh_peer_get_id(f_conn);
    struct zpn_site_controller_to_group *grp_relation;
    size_t count = 1;
    int res = zpn_sitec_to_group_get_by_sitec_gid(peer_gid, &grp_relation, &count, 0, NULL, NULL, 0);
    if (res) {
        ZPN_LOG(AL_ERROR, "%s: Could not get sitec to group for gid = %"PRId64": %s",
                fohh_description(f_conn), peer_gid, zpn_result_string(res));
        return res;
    }

    struct zpn_site_controller_group *grp;
    count = 1;
    res = zpn_sitec_group_get_by_gid(grp_relation->site_controller_group_gid,
                                     &grp,
                                     &count,
                                     0,
                                     NULL,
                                     NULL,
                                     0);
    if (res) {
        ZPN_LOG(AL_ERROR, "%s: Could not get sitec group info for gid = %"PRId64": %s",
                fohh_description(f_conn), grp_relation->site_controller_group_gid, zpn_result_string(res));
        return res;
    }

    fohh_connection_address(f_conn, peer_ip, NULL);

    peer_site->lat = grp->latitude;
    peer_site->lon = grp->longitude;
    if (grp->country_code) {
        snprintf(peer_site->cc, sizeof(peer_site->cc), "%s", grp->country_code);
    }

    return ZPN_RESULT_NO_ERROR;
}

static int zpn_broker_sitec_control_conn_redirect(struct fohh_connection *f_conn)
{
    if (!zpn_broker_balance_is_conn_redirect_needed(f_conn, redirect_sitec, NULL)) {
        return ZPN_RESULT_NO_ERROR;
    }

    const struct zpn_broker_sitec_fohh_state *sc_state = fohh_connection_get_dynamic_cookie(f_conn);
    ZPN_DEBUG_BALANCE("Redirecting control conn for peer with tunnel %s: %s",
                      sc_state->tunnel_id, fohh_description(f_conn));

    struct argo_inet peer_ip;
    struct site peer_site;
    int is_redirect_to_alt_cloud = 0;
    int res = zpn_broker_sitec_peer_geoip_lookup(f_conn, &peer_ip, &peer_site);
    if (res) {
        return res;
    }

    zpn_broker_balance_conn_redirect(f_conn,
                                     ZPATH_GID_GET_CUSTOMER_GID(fohh_peer_get_id(f_conn)),
                                     ZPATH_GID_GET_CUSTOMER_GID(fohh_peer_get_id(f_conn)),
                                     &peer_ip, &peer_site,
                                     sc_state->tunnel_id,
                                     redirect_sitec,
                                     redirect_sitec_reason,
                                     0,
                                     &is_redirect_to_alt_cloud,
                                     NULL,
                                     NULL,
                                     zpn_client_type_site_controller,
                                     0);

    if (is_redirect_to_alt_cloud) {
        __sync_fetch_and_add_8(&(zpn_fohh_workers[f_conn->fohh_thread_id].sitec_stats.num_sc_ctrl_alt_cloud_redirects), 1);
    }
    return ZPN_RESULT_NO_ERROR;
}

STATIC_INLINE void zpath_wrapper_zpn_broker_sitec_control_conn_redirect(struct fohh_connection *f_conn)
{
    zpn_broker_sitec_control_conn_redirect(f_conn);
}

static int zpn_broker_sitec_config_conn_redirect(struct fohh_connection *f_conn)
{
    if (!zpn_broker_balance_is_conn_redirect_needed(f_conn, redirect_sitec, NULL)) {
        return ZPN_RESULT_NO_ERROR;
    }

    struct connected_sitec_config_fohh_state *sc_state = fohh_connection_get_dynamic_cookie(f_conn);
    ZPN_DEBUG_BALANCE("Redirecting %s conn for peer with tunnel %s: %s",
                      sc_state->type, sc_state->tunnel_id, fohh_description(f_conn));

    struct argo_inet peer_ip;
    struct site peer_site;
    int is_redirect_to_alt_cloud = 0;
    const int res = zpn_broker_sitec_peer_geoip_lookup(f_conn, &peer_ip, &peer_site);
    if (res) {
        return res;
    }

    zpn_broker_balance_conn_redirect(f_conn,
                                     ZPATH_GID_GET_CUSTOMER_GID(fohh_peer_get_id(f_conn)),
                                     ZPATH_GID_GET_CUSTOMER_GID(fohh_peer_get_id(f_conn)),
                                     &peer_ip, &peer_site,
                                     sc_state->tunnel_id,
                                     redirect_sitec,
                                     redirect_sitec_reason,
                                     0,
                                     &is_redirect_to_alt_cloud,
                                     NULL,
                                     NULL,
                                     zpn_client_type_site_controller,
                                     0);

    if (is_redirect_to_alt_cloud) {
        if (strcmp(sc_state->type, "config") == 0) {
            __sync_fetch_and_add_8(&(zpn_fohh_workers[f_conn->fohh_thread_id].sitec_stats.num_sc_cfg_alt_cloud_redirects), 1);
        } else if (strcmp(sc_state->type, "static_config") == 0) {
            __sync_fetch_and_add_8(&(zpn_fohh_workers[f_conn->fohh_thread_id].sitec_stats.num_sc_rcfg_alt_cloud_redirects), 1);
        } else if (strcmp(sc_state->type, "override") == 0) {
            __sync_fetch_and_add_8(&(zpn_fohh_workers[f_conn->fohh_thread_id].sitec_stats.num_sc_ovd_alt_cloud_redirects), 1);
        }
    }
    return ZPN_RESULT_NO_ERROR;
}

static void zpn_broker_sitec_log_conn_redirect(struct fohh_connection *f_conn, void *cookie)
{
    if (!zpn_broker_balance_is_conn_redirect_needed(f_conn, redirect_sitec, NULL)) {
        return;
    }

    struct sitec_log_info *sc_info = (struct sitec_log_info *)cookie;
    ZPN_LOG(AL_DEBUG, "%s: Redirecting for peer with tunnel %s", fohh_description(f_conn), sc_info->tunnel_id);

    if (fohh_connection_incarnation(f_conn) != sc_info->f_conn_incarnation) {
        return;
    }

    struct argo_inet peer_ip;
    struct site peer_site;
    int is_redirect_to_alt_cloud = 0;
    int res = zpn_broker_sitec_peer_geoip_lookup(f_conn, &peer_ip, &peer_site);
    if (res) {
        return;
    }

    zpn_broker_balance_conn_redirect(f_conn,
                                     ZPATH_GID_GET_CUSTOMER_GID(fohh_peer_get_id(f_conn)),
                                     ZPATH_GID_GET_CUSTOMER_GID(fohh_peer_get_id(f_conn)),
                                     &peer_ip, &peer_site,
                                     sc_info->tunnel_id,
                                     redirect_sitec,
                                     redirect_sitec_reason,
                                     0,
                                     &is_redirect_to_alt_cloud,
                                     NULL,
                                     NULL,
                                     zpn_client_type_site_controller,
                                     0);

    if (is_redirect_to_alt_cloud) {
        __sync_fetch_and_add_8(&(zpn_fohh_workers[f_conn->fohh_thread_id].sitec_stats.num_sc_log_alt_cloud_redirects), 1);
    }
}

static void zpn_broker_sitec_log_stats_cb(struct fohh_connection *f_conn, enum fohh_connection_state state)
{
    if (state == fohh_connection_connected) {
        zpn_fohh_worker_sitec_connect_log(f_conn->fohh_thread_id);
    } else {
        zpn_fohh_worker_sitec_disconnect_log(f_conn->fohh_thread_id);
    }
}

static void zpn_broker_sitec_pblog_stats_cb(struct fohh_connection *f_conn, enum fohh_connection_state state)
{
    if (state == fohh_connection_connected) {
        zpn_fohh_worker_sitec_connect_pblog(f_conn->fohh_thread_id);
    } else {
        zpn_fohh_worker_sitec_disconnect_pblog(f_conn->fohh_thread_id);
    }
}

static void zpn_broker_sitec_alog_stats_cb(struct fohh_connection *f_conn, enum fohh_connection_state state)
{
    if (state == fohh_connection_connected) {
        zpn_fohh_worker_sitec_connect_alog(f_conn->fohh_thread_id);
    } else {
        zpn_fohh_worker_sitec_disconnect_alog(f_conn->fohh_thread_id);
    }
}

static void zpn_broker_sitec_conn_info_callback(struct fohh_connection *f_conn, void *cookie)
{
    zpn_broker_sitec_control_conn_redirect(f_conn);
}

static void zpn_broker_sitec_cfg_conn_info_callback(struct fohh_connection *f_conn, void *cookie)
{
    zpn_broker_sitec_config_conn_redirect(f_conn);
}

static int zpn_broker_sitec_process_callback(void *response_callback_cookie,
                                               struct wally_registrant *registrant,
                                               struct wally_table *table,
                                               int64_t request_id,
                                               int row_count)
{
    struct fohh_connection *f_conn = response_callback_cookie;
    if (fohh_connection_incarnation(f_conn) != request_id) {
        __sync_fetch_and_add_8(&(zpn_fohh_workers[f_conn->fohh_thread_id].sitec_stats.num_sitec_fohh_incarnation_mismatch), 1);
        return WALLY_RESULT_NO_ERROR;
    }

    fohh_connection_process(f_conn, request_id);
    return WALLY_RESULT_NO_ERROR;
}

static int zpn_broker_sitec_ctx_callback_common(void *conn_cookie,
                                                const char *sni,
                                                const char *sni_suffix,
                                                SSL_CTX **ssl_ctx,
                                                enum zpn_tlv_type tlv_type,
                                                const char *conn_description)
{
    char sub_sni[256];
    size_t sni_suffix_len;
    size_t sni_len;
    int res;
    const char *use_sni = NULL;
    int64_t customer_id = 0;
    int64_t sitec_id;
    struct zpn_site_controller *sitec = NULL;
    struct zpath_customer *customer = NULL;
    int is_dtls = (tlv_type == zpn_fohh_tlv) ? 0 : 1;
    wally_response_callback_f *callback_f = (tlv_type == zpn_fohh_tlv) ? zpn_broker_sitec_process_callback : NULL;
    void *callback_cookie = (tlv_type == zpn_fohh_tlv) ? conn_cookie : NULL;
    int64_t cookie_int = (tlv_type == zpn_fohh_tlv) ? fohh_connection_incarnation(conn_cookie) : 0;

    if (isdigit(sni[0])) {
        /*
         * V2 style connector SNI: <sitec_id>.<broker_gid><scctl|sccfg>.<cloud name>
         *
         * Note: Modern customer provision is domain as
         * CUSTOMER_GID.zpa-customer.com. Thus we check if the GID
         * passed is a customer_GID
         */
        sitec_id = strtoll(sni, NULL, 0);
        if ((sitec_id < 0x0100000000000000ll) ||
            sitec_id == ZPATH_GID_GET_CUSTOMER_GID(sitec_id)) {
            /* Assume it is V1 style enrollment if the ID isn't big enough to include shard. */
            sitec_id = 0;
            ZPN_DEBUG_SITEC("%s - sni: %s, sni_suffix: %s - V1 style sitec enrollment", conn_description, sni, sni_suffix ? sni_suffix : "NULL");
        } else {
            ZPN_DEBUG_SITEC("%s - sni: %s, sni_suffix: %s, sitec_id: %"PRId64" - V2 style sitec enrollment",
                                conn_description, sni, sni_suffix ? sni_suffix : "NULL", sitec_id);

            res = zpn_sitec_get_by_id(sitec_id,
                                      &sitec,
                                      callback_f,
                                      callback_cookie,
                                      cookie_int);
            if (res) {
                if (res == ZPN_RESULT_ASYNCHRONOUS) {
                    ZPN_DEBUG_SITEC("Asynchronous sitec fetch for gid = %"PRId64"", sitec_id);
                } else {
                    ZPN_LOG(AL_WARNING, "Error fetching sitec %"PRId64": %s", sitec_id, zpn_result_string(res));
                }
                return res;
            } else {
                customer_id = sitec->customer_gid;
                ZPN_DEBUG_SITEC("%s: sitec %"PRId64" fetched, cert_id: %"PRId64", customer_gid: %"PRId64"",
                                    conn_description, sitec_id, sitec->cert_id, customer_id);

                res = zpath_customer_get(customer_id, &customer, callback_f, callback_cookie, cookie_int);
                if (res) {
                    if (res == ZPN_RESULT_ASYNCHRONOUS) {
                        ZPN_DEBUG_SITEC("Asynchronous customer fetch for gid = %"PRId64"", customer_id);
                    } else {
                        ZPN_LOG(AL_WARNING, "Error fetching customer %"PRId64": %s", customer_id, zpn_result_string(res));
                    }
                    return res;
                }
                use_sni = customer->domain_name;
                ZPN_DEBUG_SITEC("Customer domain: %s, customer_gid: %"PRId64"", use_sni, customer_id);
            }
        }
    } else {
        ZPN_DEBUG_SITEC("%s - sni: %s, sni_suffix: %s - V1 style  sitec enrollmentco", conn_description, sni, sni_suffix ? sni_suffix : "NULL");
    }

    if (!sni_suffix) {
        use_sni = sni;
    } else if (!use_sni) {
        /* V1 style connector SNI: <domain>.<sccfg|scctl>.<cloud namse> */
        sni_suffix_len = strlen(sni_suffix) + 1;
        sni_len = strlen(sni);

        if (sni_len <= sni_suffix_len) {
            ZPN_LOG(AL_ERROR, "Expecting SNI longer than root. Sni = %s", sni);
            return ZPN_RESULT_ERR;
        }

        snprintf(sub_sni, sizeof(sub_sni), "%.*s", (int)(sni_len - sni_suffix_len), sni);
        use_sni = sub_sni;
    }

    res = zpn_signing_cert_get_verify_ctx(ssl_ctx,
                                          &customer_id,
                                          use_sni,
                                          0,
                                          callback_f,
                                          conn_cookie,
                                          cookie_int,
                                          is_dtls);
    if (res) {
        if (res == ZPN_RESULT_ASYNCHRONOUS) {
            ZPN_DEBUG_SITEC("Asynchronous context request for sni = %s", sni);
        } else {
            ZPN_LOG(AL_WARNING, "Error context request for sni = %s: %s", sni, zpn_result_string(res));
        }
    } else {
        ZPN_DEBUG_SITEC("%s: SNI = %s has context", conn_description, sni);

        if (tlv_type == zpn_zrdt_tlv) {
            if (!zpn_broker_is_dtls_enabled_on_broker()) {
                /* DTLS not enabled for this customer */
                ZPN_LOG(AL_INFO, "SSL ctx fetch failed because dtls is not enabled");
                *ssl_ctx = NULL;
                res = ZPN_RESULT_ERR;
            }
        }
    }
    return res;
}

int64_t zpn_broker_sitec_total_sitec_connections_to_redirect(const struct zpn_fohh_worker_sitec_stats* sitec_stats)
{
    return sitec_stats->num_sitec_cfg
         + sitec_stats->num_sitec_rcfg
         + sitec_stats->num_sitec_ovd
         + sitec_stats->num_sitec_userdb
         + sitec_stats->num_sitec_stats
         + sitec_stats->num_sitec_ctrl
         + sitec_stats->num_sitec_log
         + sitec_stats->num_sitec_pblog
         + sitec_stats->num_sitec_alog
         + sitec_stats->num_sitec_astats
         + sitec_stats->num_sitec_pbstats;

}
int zpn_broker_sitec_ctx_callback(struct fohh_connection *f_conn,
                                  const char *sni,
                                  const char *sni_suffix,
                                  SSL_CTX **ssl_ctx)
{
    return zpn_broker_sitec_ctx_callback_common(f_conn, sni, sni_suffix, ssl_ctx, zpn_fohh_tlv, fohh_description(f_conn));
}

static int zpn_broker_sitec_verify_callback_common(struct zpn_tlv *tlv, int verify_scope)
{
    int res;
    int64_t sitec_gid_derived_from_cert;
    int64_t sitec_gid_fetched_from_wally;
    char *cn = zpn_tlv_peer_cn(tlv);
    X509 *cert;
    struct zpn_site_controller *sitec;
    int ip_acl_iter;
    struct argo_inet sitec_addr;
    int ip_acl_validation_failed;
    char    *cn_iter;
    wally_response_callback_f *callback_f = (tlv->type == zpn_fohh_tlv) ? zpn_broker_sitec_process_callback : NULL;
    void *callback_cookie = (tlv->type == zpn_fohh_tlv) ? (void *)(tlv->conn.f_conn) : NULL;
    int64_t request_id = (tlv->type == zpn_fohh_tlv) ? fohh_connection_incarnation(tlv->conn.f_conn) : zrdt_conn_incarnation(tlv->conn.z_conn);

    if (!cn) {
        ZPN_LOG(AL_WARNING, "No peer CN");
        return ZPN_RESULT_ERR;
    }

    sitec_gid_derived_from_cert = 0;
    for (cn_iter = &(cn[0]); *cn_iter; cn_iter++) {
        if (isdigit(*cn_iter)) {
            sitec_gid_derived_from_cert = strtoll(cn_iter, &cn_iter, 0);
            break;
        }
    }
    if (sitec_gid_derived_from_cert == 0) {
        ZPN_LOG(AL_WARNING, "Could not derive sitec gid from cname(%s)", cn);
        return ZPN_RESULT_ERR;
    }

    cert = zpn_tlv_peer_cert(tlv);

    if (!cert) {
        ZPN_LOG(AL_WARNING, "No peer cert??");
        return ZPN_RESULT_ERR;
    }

    res = zpn_issuedcert_verify(ZPATH_GID_GET_CUSTOMER_GID(sitec_gid_derived_from_cert),
                                cert,
                                cn,
                                "SITE_CONTROLLER",
                                &sitec_gid_fetched_from_wally,
                                callback_f,
                                callback_cookie,
                                request_id);
    X509_free(cert);

    if (res) {
        if (res == ZPN_RESULT_ASYNCHRONOUS) {
            ZPN_DEBUG_SITEC("Asynchronous verification request for CN = %s", cn);
        } else {
            ZPN_LOG(AL_WARNING, "Error verifying request for CN = %s, = %s", cn, zpn_result_string(res));
        }
        return res;

    } else {
        if (sitec_gid_fetched_from_wally != sitec_gid_derived_from_cert) {
            ZPN_LOG(AL_WARNING, "Error verifying request for CN = %s, gid mismatch(%"PRId64" vs %"PRId64")", cn, sitec_gid_derived_from_cert, sitec_gid_fetched_from_wally);
            return ZPN_RESULT_ERR;
        }
        ZPN_DEBUG_SITEC("Verified cert for CN = %s", cn);
    }
    zpn_tlv_peer_set_id(tlv, sitec_gid_derived_from_cert);

    res = zpn_sitec_get_by_id(sitec_gid_derived_from_cert,
                              &sitec,
                              callback_f,
                              callback_cookie,
                              request_id);
    if (res) {
        if (res == ZPN_RESULT_ASYNCHRONOUS) {
            ZPN_DEBUG_SITEC("Asynchronous sitec fetch for gid = %"PRId64"", sitec_gid_derived_from_cert);
        } else {
            ZPN_LOG(AL_WARNING, "Error fetching sitec %"PRId64": %s", sitec_gid_derived_from_cert,
                    zpn_result_string(res));
        }
        return res;
    } else {
        ZPN_DEBUG_SITEC("%s: sitec cert verified, sitec_id %"PRId64" fetched.", cn, sitec_gid_derived_from_cert);
    }

    zpn_tlv_address(tlv, &sitec_addr, NULL);

    struct zpn_site_controller_to_group *sc_to_group;
    size_t count = 1;
    res = zpn_sitec_to_group_get_by_sitec_gid(sitec_gid_derived_from_cert,
                                              &sc_to_group,
                                              &count,
                                              1,
                                              callback_f,
                                              callback_cookie,
                                              request_id);
    if (res) {
        if (res == ZPN_RESULT_ASYNCHRONOUS) {
            ZPN_DEBUG_SITEC("Asynchronous zpn_site_controller_to_group fetch for gid = %"PRId64"",sitec_gid_derived_from_cert);
        } else {
            ZPN_LOG(AL_WARNING, "Error fetching zpn_site_controller_to_group: %"PRId64": %s", sitec_gid_derived_from_cert, zpn_result_string(res));
            /* We don't error here because the relation may simply be changing, etc */
            res = ZPN_RESULT_NO_ERROR;
        }
        return res;
    } else {
        struct zpn_site_controller_group *group;
        size_t g_count = 1;
        res = zpn_sitec_group_get_by_gid(sc_to_group->site_controller_group_gid,
                                         &group,
                                         &g_count,
                                         1,
                                         callback_f,
                                         callback_cookie,
                                         request_id);
        if (res) {
            if (res == ZPN_RESULT_ASYNCHRONOUS) {
                ZPN_DEBUG_SITEC("Asynchronous sitec_group fetch for gid = %"PRId64"", group->gid);
            } else {
                ZPN_LOG(AL_WARNING, "Error fetching sitec_group: %"PRId64": %s", group->gid, zpn_result_string(res));
                res = ZPN_RESULT_NO_ERROR;
            }
        } else {
            /*
             * IP ACL is configured at site controller level.
             * So either customer do not configure them
             * or will have to configure IP ACL's for all the sc's in the group
             */
            if (0 == sitec->ip_acl_count) {
                ip_acl_validation_failed = 0;
            } else {
                ip_acl_validation_failed = 1;
            }
            ip_acl_iter = 0;
            while (ip_acl_iter != sitec->ip_acl_count) {
                if (argo_inet_is_contained(&sitec->ip_acl[ip_acl_iter], &sitec_addr)) {
                    ip_acl_validation_failed = 0;
                    break;
                }
                ip_acl_iter++;
            }

            if (ip_acl_validation_failed) {
                char sitec_addr_str[ARGO_INET_ADDRSTRLEN];
                ZPN_LOG(AL_ERROR, "sc's %"PRId64" IP(%s) didn't match ACL "
                        "defined in sitec Group %"PRId64" - "
                        "terminating the connector",
                        sitec_gid_derived_from_cert,
                        argo_inet_generate(sitec_addr_str, &sitec_addr),
                        sc_to_group->site_controller_group_gid);
                return ZPN_RESULT_ACCESS_DENIED;
            }
        }
    }

    return res;
}

int zpn_broker_sitec_verify_callback(struct fohh_connection *f_conn)
{
    struct zpn_tlv tlv;

    zpn_tlv_init(&tlv, zpn_fohh_tlv, f_conn, 0);
    return zpn_broker_sitec_verify_callback_common(&tlv, 0);
}

static int zpn_is_sitec_authlog_lss_feature_enabled(int64_t customer_gid)
{
    char    *config_val         = NULL;
    int64_t root_customer_gid   = 0;
    int     ret                 = 0;

    root_customer_gid = ZPATH_GID_GET_CUSTOMER_GID(zpath_instance_global_state.current_config->gid);
    config_val = zpath_config_override_get_config_str(SITEC_FEATURE_AUTH_LOG_LSS,
                                                      &config_val,
                                                      DEFAULT_SITEC_FEATURE_AUTH_LOG_LSS,
                                                      customer_gid,
                                                      root_customer_gid,
                                                      (int64_t) ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                      (int64_t) 0 );

    if (strcmp(config_val, DEFAULT_SITEC_FEATURE_AUTH_LOG_LSS) == 0) {
        // Feature flag is not enabled. return false
        ret = 0;
    } else {
        // Feature flag is enabled. return true
        ret = 1;
    }

    return ret;
}

static void zpn_broker_sitec_log_conn_monitor_cb(evutil_socket_t sock, short flags, void *cookie)
{
    int res;
    int64_t customer_id = 0;
    struct zpath_customer *customer = NULL;
    struct fohh_connection *f_conn = cookie;

    /* Drop connection when broker is shutting down. */
    if (disconnect_sitec) {
        const char *reason = disconnect_sitec_reason ? : FOHH_CLOSE_REASON_UPGRADE;
        ZPN_LOG(AL_INFO, "%s: Disconnect sitec log connection due to %s",
                fohh_description(f_conn), reason);
        fohh_connection_delete(f_conn, reason);
        return;
    }

    /* Drop connection when customer is disabled */
    customer_id = ZPATH_GID_GET_CUSTOMER_GID(fohh_peer_get_id(f_conn));
    if (customer_id != 0) {
        res = zpath_customer_get(customer_id,
                                 &customer,
                                 NULL,
                                 NULL,
                                 0);

        if (res == ZPN_RESULT_NOT_FOUND || (res == ZPN_RESULT_NO_ERROR && customer->disable_traffic == 1)) {
            ZPN_LOG(AL_NOTICE, "%s: Disconnecting sitec(%"PRId64") as customer(%"PRId64") is not found or is disabled",
                            fohh_description(f_conn), fohh_peer_get_id(f_conn), customer_id);
            /* Kill the connection */
            fohh_connection_delete(f_conn, ZPN_ERR_CUSTOMER_DISABLED);
            return;
        }
    }

    if (redirect_sitec) {
        ZPN_LOG(AL_INFO, "%s: Redirect sitec log connection due to %s",
                fohh_description(f_conn),
                redirect_sitec_reason ? : FOHH_CLOSE_REASON_UPGRADE);
        zpn_broker_sitec_log_conn_redirect(f_conn, log_rx_get_app_info(f_conn));
    }
}

static int zpn_broker_sitec_update_sc_lat_lon(struct zpn_sitec_auth_log *log, struct fohh_connection *f_conn)
{
    struct zpn_site_controller_group *group;
    int res;
    size_t count = 1;

    res = zpn_sitec_group_get_by_gid(log->grp_gid,
                                         &group,
                                         &count,
                                         0,
                                         NULL,
                                         NULL,
                                         0);
    if (res == ZPN_RESULT_ASYNCHRONOUS) {
        return ZPN_RESULT_NO_ERROR;
    }
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not get sitec group info for gid = %"PRId64": %s",
                            log->grp_gid, zpn_result_string(res));
        return ZPN_RESULT_ERR;
    } else {
        log->lat = group->latitude;
        log->lon = group->longitude;
        if (group->country_code) {
            if (log->cc && strcmp(log->cc, group->country_code)) {
                ZPN_FREE(log->cc);
                log->cc = NULL;
            }

            if (NULL == log->cc) {
                log->cc = ZPN_STRDUP(group->country_code, strlen(group->country_code));
            }
        }
    }

    if (geoip_db_file && (log->lat == 0) && (log->lon == 0)) {
            fohh_connection_address(f_conn, &(log->pub_ip), NULL);
            char str[ARGO_INET_ADDRSTRLEN];
            int lat, lon;
            char cc[CC_STR_LEN + 1];

            if (zpath_geoip_lookup(&log->pub_ip, &lat, &lon, &cc[0]) == ZPATH_RESULT_NO_ERROR) {
                ZPN_DEBUG_SITEC("sitec IP = %s, lat = %d, lon = %d, cc = %s",
                                    argo_inet_generate(str, &(log->pub_ip)), lat, lon, cc);
                log->lat = lat;
                log->lon = lon;
                cc[CC_STR_LEN] = '\0';
                if(log->cc && strcmp(log->cc, cc)){
                    ZPN_FREE(log->cc);
                    log->cc = NULL;
                }

                if(log->cc == NULL){
                    log->cc = ZPN_STRDUP(cc, strlen(cc));
                }
            }
    }

    return ZPN_RESULT_NO_ERROR;
}

static void zpn_broker_sitec_auth_log(struct fohh_connection *f_conn,
                                      struct zpn_sitec_auth_log *log,
                                      enum fohh_connection_state state)
{
    struct fohh_tcp_info info;

    log->log_date = epoch_us();
    log->g_cst = ZPATH_GID_GET_CUSTOMER_GID(log->gid);
    log->g_brk = broker_instance_id;
    log->client_type = zpn_client_type_string(zpn_client_type_site_controller);
    log->auth_type = zpn_auth_type_string(zpn_tunnel_auth_site_controller);

    if (!log->pub_ip.length) {
        uint16_t pub_port_ne;
        fohh_connection_address_and_port(f_conn, &(log->pub_ip), &pub_port_ne, NULL, NULL);
        log->pub_port = ntohs(pub_port_ne);
    }

    memset(&info, 0, sizeof(info));
    if (fohh_connection_get_tcp_info(f_conn, &info) == FOHH_RESULT_NO_ERROR) {
        log->tcpi_snd_mss = info.tcpi_snd_mss;
        log->tcpi_rcv_mss = info.tcpi_rcv_mss;
        log->tcpi_rtt = info.tcpi_rtt;
        log->tcpi_rttvar = info.tcpi_rttvar;
        log->tcpi_snd_cwnd = info.tcpi_snd_cwnd;
        log->tcpi_advmss = info.tcpi_advmss;
        log->tcpi_reordering = info.tcpi_reordering;
        log->tcpi_rcv_rtt = info.tcpi_rcv_rtt;
        log->tcpi_rcv_space = info.tcpi_rcv_space;
        log->tcpi_total_retrans = info.tcpi_total_retrans;
        log->tcpi_thru_put = info.tcpi_thru_put;
        log->tcpi_unacked = info.tcpi_unacked;
        log->tcpi_sacked = info.tcpi_sacked;
        log->tcpi_lost = info.tcpi_lost;
        log->tcpi_fackets = info.tcpi_fackets;

        log->tcpi_last_data_sent = info.tcpi_last_data_sent;
        log->tcpi_last_ack_sent = info.tcpi_last_ack_sent;
        log->tcpi_last_data_recv = info.tcpi_last_data_recv;
        log->tcpi_last_ack_recv = info.tcpi_last_ack_recv;
        log->tcpi_bytes_acked = info.tcpi_bytes_acked;
        log->tcpi_bytes_received = info.tcpi_bytes_received;
        log->tcpi_segs_out = info.tcpi_segs_out;
        log->tcpi_segs_in = info.tcpi_segs_in;

        if ((info.tcpi_rtt / 1000) > CONTROL_CONN_HIGH_RTT_LOG_THRESHOLD_MS) {
            ZPN_LOG(AL_NOTICE, "%s: sitec connection high RTT: %d ms, exceeded RTT threshold: %d ms", fohh_description(f_conn), info.tcpi_rtt / 1000, CONTROL_CONN_HIGH_RTT_LOG_THRESHOLD_MS);
        }
    }

    fohh_connection_get_stats(f_conn,
                              &(log->transmit_bytes),
                              &(log->receive_bytes),
                              &(log->transmit_objects),
                              &(log->receive_objects),
                              &(log->transmit_raw_tlv),
                              &(log->receive_raw_tlv));
    /* Skip quiet app connections */
    if (!fohh_connection_is_quiet(f_conn)) {
        log->app_rtt_us = fohh_connection_get_max_app_rtt(f_conn);
        if (fohh_histogram_get_counts_array(f_conn, FOHH_HISTOGRAM_APP_RTT, log->app_rtt, FOHH_HISTOGRAM_MAX_BUCKETS) == FOHH_RESULT_NO_ERROR) {
            log->app_rtt_count = FOHH_HISTOGRAM_MAX_BUCKETS;
        } else {
            ZPN_LOG(AL_DEBUG, "%s: Unable to get app rtt histogram", fohh_description(f_conn));
        }
    }

    /* FIXME: Do we need to send max value for all TCP_INFO metrics? */
    log->tcp_rtt_us = fohh_connection_get_max_tcp_rtt(f_conn);
    if (fohh_histogram_get_counts_array(f_conn, FOHH_HISTOGRAM_TCP_RTT, log->tcp_rtt, FOHH_HISTOGRAM_MAX_BUCKETS) == FOHH_RESULT_NO_ERROR) {
        log->tcp_rtt_count = FOHH_HISTOGRAM_MAX_BUCKETS;
    } else {
        ZPN_LOG(AL_DEBUG, "%s: Unable to get tcp rtt histogram", fohh_description(f_conn));
    }

    if (fohh_histogram_get_counts_array(f_conn, FOHH_HISTOGRAM_TCP_WIN, log->tcp_congestion_win, FOHH_HISTOGRAM_MAX_BUCKETS) == FOHH_RESULT_NO_ERROR) {
        log->tcp_congestion_win_count = FOHH_HISTOGRAM_MAX_BUCKETS;
    } else {
        ZPN_LOG(AL_DEBUG, "%s: Unable to get tcp congestion win histogram", fohh_description(f_conn));
    }

    if (state == fohh_connection_connected) {
        if (!log->auth_us) log->auth_us = epoch_us();
        log->status = ZPN_STATUS_AUTHENTICATED;
    } else {
        log->deauth_us = epoch_us();
        log->status = ZPN_STATUS_DISCONNECTED;
    }

    log->tlv_type = zpn_tlv_type_str(zpn_fohh_tlv);
    zpn_broker_sitec_update_sc_lat_lon(log, f_conn);

    /* Log to local for debug purpose if enabled */
    if (zpn_sc_auth_collection) {
        argo_log_structure_immediate(zpn_sc_auth_collection,
                                     argo_log_priority_info,
                                     0,
                                     "sc_auth_log",
                                     zpn_sitec_auth_log_description,
                                     log);
    }

    if(zpn_is_sitec_authlog_lss_feature_enabled(log->g_cst)) {
        zpn_broker_siem_sc_auth_log(log);
    }

    zpath_customer_log_struct(log->g_cst,
                              zpath_customer_log_type_zpn_sc_auth,
                              "sc_auth",
                              NULL,
                              NULL,
                              NULL,
                              NULL,
                              zpn_sitec_auth_log_description,
                              log);
}


static void sc_auth_log(struct fohh_connection *f_conn, enum fohh_connection_state state)
{
    struct zpn_broker_sitec_fohh_state *sc_state = fohh_connection_get_dynamic_cookie(f_conn);
    struct zpn_sitec_auth_log *log = &(sc_state->log);

    if (sc_state->status_report_received == 0) {
        return;
    }

    log->tunnel_type = ZPN_SITEC_BROKER_CONTROL;
    zpn_broker_sitec_auth_log(f_conn, log, state);
    sc_state->first_auth_log_sent = 1;
}

static void zpn_broker_sitec_ctrl_conn_monitor_cb(evutil_socket_t sock, short flags, void *cookie)
{
    int res;
    int64_t customer_id = 0;
    struct zpath_customer *customer = NULL;
    struct fohh_connection *f_conn = cookie;
    struct zpn_broker_sitec_fohh_state *sc_state = fohh_connection_get_dynamic_cookie(f_conn);
    if (!sc_state) {
        ZPN_LOG(AL_NOTICE, "%s: sitec control conn monitor cb has no cookie", fohh_description(f_conn));
        return;
    }
    sc_state->monitor_count++;

    /* Drop connection when broker is shutting down. */
    if (disconnect_sitec) {
        const char *reason = disconnect_sitec_reason ? : FOHH_CLOSE_REASON_UPGRADE;
        ZPN_LOG(AL_INFO, "%s: Disconnect sitec control connection due to %s",
                fohh_description(f_conn), reason);
        fohh_connection_delete(f_conn, reason);
        return;
    }

    /* Drop connection when customer is disabled */
    customer_id = ZPATH_GID_GET_CUSTOMER_GID(fohh_peer_get_id(f_conn));
    if (customer_id != 0) {
        res = zpath_customer_get(customer_id,
                                 &customer,
                                 NULL,
                                 NULL,
                                 0);

        if (res == ZPN_RESULT_NOT_FOUND || (res == ZPN_RESULT_NO_ERROR && customer->disable_traffic == 1)) {
            ZPN_LOG(AL_NOTICE, "%s: Disconnecting sitec(%"PRId64") as customer(%"PRId64") is not found or is disabled",
                            fohh_description(f_conn), fohh_peer_get_id(f_conn), customer_id);
            /* Kill the connection */
            fohh_connection_delete(f_conn, ZPN_ERR_CUSTOMER_DISABLED);
            return;
        }
    }

    if (redirect_sitec &&
        ((sc_state->monitor_count % ZPN_TUNNEL_MONITOR_REDIRECT_INTERVAL_COUNT) == 0
         || strcmp(redirect_sitec_reason, BRK_REDIRECT_REASON_BROKER_RESTART) == 0)) {
        ZPN_LOG(AL_INFO, "%s: Redirect sitec control connection due to %s",
                fohh_description(f_conn),
                redirect_sitec_reason ? : FOHH_CLOSE_REASON_UPGRADE);
        zpath_wrapper_zpn_broker_sitec_control_conn_redirect(f_conn);

        if (zpn_is_broker_auth_log_redirect_status_feature_enabled() && zpn_is_redirect_reason_maintanence(redirect_sitec_reason)) {
            sc_state->is_redirect_sent = 1;
        }
    }

    if ((sc_state->monitor_count % ZPN_TUNNEL_MONITOR_LOG_INTERVAL_COUNT) == 0) {
        sc_auth_log(f_conn, fohh_get_state(f_conn));
    }
}

static void cleanup_sc_auth_log_brokers_unique(struct zpn_sitec_auth_log *log) {
    if (log->log_brokers_uniq) {
        for (int i = 0; i < log->log_brokers_uniq_count; ++i) {
            ZPN_FREE(log->log_brokers_uniq[i]);
        }
        ZPN_FREE(log->log_brokers_uniq);
        log->log_brokers_uniq = NULL;
        log->log_brokers_uniq_count = 0;
    }
}

static void cleanup_sc_auth_log_all_log_brokers_data(struct zpn_sitec_auth_log *log) {
    if (log->all_log_brokers_data) {
        for (int i = 0; i < log->all_log_brokers_data_count; ++i) {
            ZPN_FREE(log->all_log_brokers_data[i]);
            log->all_log_brokers_data[i] = NULL;
        }
        ZPN_FREE(log->all_log_brokers_data);
        log->all_log_brokers_data = NULL;
        log->all_log_brokers_data_count = 0;
    }

}

static int zpn_broker_sitec_status_report_cb(void *argo_cookie_ptr,
                                             void *argo_structure_cookie_ptr,
                                             struct argo_object *object)
{
    struct zpn_sitec_status_report *sc_report = object->base_structure_void;
    struct fohh_connection *f_conn = argo_structure_cookie_ptr;
    struct zpn_broker_sitec_fohh_state *sc_state;
    struct zpn_sitec_auth_log *log;

    if (zpn_debug_get(ZPN_DEBUG_SITE_CONTROLLER_IDX)) {
        char buf[2000];
        if (argo_object_dump(object, buf, sizeof(buf), NULL, 0) == ARGO_RESULT_NO_ERROR) {
            ZPN_DEBUG_SITEC("%s: %"PRId64": %s", fohh_description(f_conn), fohh_peer_get_id(f_conn), buf);
        }
    }

    sc_state = fohh_connection_get_dynamic_cookie(f_conn);
    if (!sc_state) {
        ZPN_LOG(AL_NOTICE, "Failed to retrieve sc state from control connection");
        return ZPN_RESULT_NO_ERROR;
    }
    log = &(sc_state->log);
    log->cpu_util = sc_report->cpu_util;
    log->mem_util =  sc_report->mem_util;
    log->udp4_port_util = sc_report->udp4_port_util;
    log->udp6_port_util = sc_report->udp6_port_util;
    log->sys_fd_util = sc_report->sys_fd_util;
    log->proc_fd_util = sc_report->proc_fd_util;

    log->last_os_upgrade_time = sc_report->last_os_upgrade_time;
    log->last_sarge_upgrade_time = sc_report->last_sarge_upgrade_time;

    log->vm_start_s = sc_report->sys_uptime_s;
    log->app_start_s = sc_report->sc_uptime_s;

    if (log->dft_rt_intf) {
        ZPN_FREE(log->dft_rt_intf);
        log->dft_rt_intf = NULL;
    }
    if (log->platform_version) {
        ZPN_FREE(log->platform_version);
        log->platform_version = NULL;
    }

    if (sc_report->dft_rt_intf) {
        log->dft_rt_intf = ZPN_STRDUP(sc_report->dft_rt_intf, strlen(sc_report->dft_rt_intf));
    }
    if (sc_report->platform_version) {
        log->platform_version = ZPN_STRDUP(sc_report->platform_version, strlen(sc_report->platform_version));
    }

    log->dft_rt_gw = sc_report->dft_rt_gw;
    log->resolver = sc_report->resolver;

    log->intf_count = sc_report->intf_count;
    log->intf_rb = sc_report->intf_rb;
    log->intf_rp = sc_report->intf_rp;
    log->intf_re = sc_report->intf_re;
    log->intf_rd = sc_report->intf_rd;
    log->intf_tb = sc_report->intf_tb;
    log->intf_tp = sc_report->intf_tp;
    log->intf_te = sc_report->intf_te;
    log->intf_td = sc_report->intf_td;

    log->delta_intf_rb = sc_report->delta_intf_rb;
    log->delta_intf_rp = sc_report->delta_intf_rp;
    log->delta_intf_re = sc_report->delta_intf_re;
    log->delta_intf_rd = sc_report->delta_intf_rd;
    log->delta_intf_tb = sc_report->delta_intf_tb;
    log->delta_intf_tp = sc_report->delta_intf_tp;
    log->delta_intf_te = sc_report->delta_intf_te;
    log->delta_intf_td = sc_report->delta_intf_td;

    log->total_intf_b = sc_report->total_intf_b;
    log->total_intf_p = sc_report->total_intf_p;
    log->total_intf_e = sc_report->total_intf_e;
    log->total_intf_d = sc_report->total_intf_d;

    log->delta_total_intf_b = sc_report->delta_total_intf_b;
    log->delta_total_intf_p = sc_report->delta_total_intf_p;
    log->delta_total_intf_e = sc_report->delta_total_intf_e;
    log->delta_total_intf_d = sc_report->delta_total_intf_d;
    zpn_broker_sitec_update_sc_lat_lon(log, f_conn);

    snprintf(log->ovd_broker_cn, sizeof(log->ovd_broker_cn), "%s", sc_report->ovd_broker_cn);

    cleanup_sc_auth_log_brokers_unique(log);
    if (sc_report->log_brokers_uniq_count) {
        log->log_brokers_uniq = ZPN_CALLOC(sizeof(char*)* sc_report->log_brokers_uniq_count);
        int ii = 0;
        for (ii = 0; ii < sc_report->log_brokers_uniq_count; ii++) {
            log->log_brokers_uniq[ii] = ZPN_STRDUP(sc_report->log_brokers_uniq[ii], strlen(sc_report->log_brokers_uniq[ii]));
        }
        log->log_brokers_uniq_count = sc_report->log_brokers_uniq_count;
    }

    cleanup_sc_auth_log_all_log_brokers_data(log);
    if (sc_report->all_log_brokers_data_count) {
        log->all_log_brokers_data = ZPN_CALLOC(sizeof(char*)* sc_report->all_log_brokers_data_count);
        int ii = 0;
        for (ii = 0; ii < sc_report->all_log_brokers_data_count; ii++) {
            log->all_log_brokers_data[ii] = ZPN_STRDUP(sc_report->all_log_brokers_data[ii], strlen(sc_report->all_log_brokers_data[ii]));
        }
        log->all_log_brokers_data_count = sc_report->all_log_brokers_data_count;
    }

    if (log->status_conn_info) {
        ZPN_FREE(log->status_conn_info);
        log->status_conn_info = NULL;
    }
    if (sc_report->status_conn_info != NULL && sc_report->status_conn_info[0]) {
        log->status_conn_info = ZPN_STRDUP(sc_report->status_conn_info, strlen(sc_report->status_conn_info));
    }
    log->master_last_sync_time = sc_report->master_last_sync_time;
    log->shard_last_sync_time = sc_report->shard_last_sync_time;
    log->userdb_last_sync_time = sc_report->userdb_last_sync_time;
    sc_state->status_report_received = 1;
    if (sc_state->first_auth_log_sent == 0) {
        sc_auth_log(f_conn, fohh_get_state(f_conn));
    }
    return ZPN_RESULT_NO_ERROR;
}

static int zpn_broker_sitec_environment_report_cb(void *argo_cookie_ptr,
                                                  void *argo_structure_cookie_ptr,
                                                  struct argo_object *object)
{
    struct zpn_sitec_environment_report *report = object->base_structure_void;
    if (!report->sarge_version)
        goto done;

    struct fohh_connection *f_conn = argo_structure_cookie_ptr;
    struct zpn_broker_sitec_fohh_state *sc_state = fohh_connection_get_dynamic_cookie(f_conn);
    sc_state->log_sarge_version = ZPN_STRDUP(report->sarge_version, strlen(report->sarge_version));
    sc_state->log.sarge_version = sc_state->log_sarge_version;

done:
    return ZPN_RESULT_NO_ERROR;
}

static int zpn_broker_sitec_tcp_info_report_cb(void *argo_cookie_ptr,
                                               void *argo_structure_cookie_ptr,
                                               struct argo_object *object)
{
    struct fohh_connection *f_conn = argo_structure_cookie_ptr;
    struct zpn_broker_sitec_fohh_state *sc_state = fohh_connection_get_dynamic_cookie(f_conn);
    struct zpn_tcp_info_report *req = object->base_structure_void;

    sc_state->log.priv_ip = req->priv_ip;
    if (sc_state->log_version) {
        ZPN_FREE(sc_state->log_version);
        sc_state->log_version = NULL;
    }

    sc_state->log_version = ZPN_STRDUP(req->version, strlen(req->version));
    sc_state->log.version = sc_state->log_version;

    if (sc_state->log.platform) {
        ZPN_FREE(sc_state->log.platform);
        sc_state->log.platform = NULL;
    }

    if (sc_state->log.platform_detail) {
        ZPN_FREE(sc_state->log.platform_detail);
        sc_state->log.platform_detail = NULL;
    }

    if (sc_state->log.runtime_os) {
        ZPN_FREE(sc_state->log.runtime_os);
        sc_state->log.runtime_os = NULL;
    }

    if(sc_state->log.geoip_version) {
        ZPN_FREE(sc_state->log.geoip_version);
        sc_state->log.geoip_version = NULL;
    }

    if(sc_state->log.isp_version) {
        ZPN_FREE(sc_state->log.isp_version);
        sc_state->log.isp_version = NULL;
    }

    char *platform = req->platform ? req->platform : "UNKNOWN";
    sc_state->log.platform = ZPN_STRDUP(platform, strlen(platform));

    char *geoip_version = req->geoip_version ? req->geoip_version : "\0";
    char *isp_version = req->isp_version ? req->isp_version : "\0";
    sc_state->log.geoip_version = ZPN_STRDUP(geoip_version, strlen(geoip_version));
    sc_state->log.isp_version = ZPN_STRDUP(isp_version, strlen(isp_version));

    const char *platform_detail = req->platform_detail ? req->platform_detail : "UNKNOWN";
    sc_state->log.platform_detail = ZPN_STRDUP(platform_detail, strlen(platform_detail));

    const char *runtime_os = req->runtime_os ? req->runtime_os : "Unknown";
    sc_state->log.runtime_os = ZPN_STRDUP(runtime_os, strnlen(runtime_os, FOHH_MAX_NAMELEN));

    ZPN_DEBUG_SITEC("sitec gid %"PRId64", Platform = %s and Platform Detail = %s, runtime OS = %s",
                              sc_state->log.gid, platform, platform_detail, runtime_os);

    if (!sc_state->received_tcp_info) {
        sc_state->received_tcp_info = 1;
        if (sc_state->log.grp_gid) {
            sc_auth_log(f_conn, fohh_get_state(f_conn));
        }
    }
    return ZPN_RESULT_NO_ERROR;
}

static int zpn_broker_sitec_admin_probe_status_report_cb(void* argo_cookie_ptr,
                                                         void* argo_structure_cookie_ptr,
                                                         struct argo_object* object)
{
    const struct fohh_connection *f_conn = argo_structure_cookie_ptr;
    struct zpn_command_probe_status *report = object->base_structure_void;

    if (!report || !f_conn) {
        ZPN_LOG(AL_ERROR, "Command Probe : argo callback argument is not valid");
        return ZPN_RESULT_NO_ERROR;
    }

    if (zpn_debug_get(ZPN_DEBUG_SITE_CONTROLLER_IDX)) {
        char buf[8000];
        if (argo_object_dump(object, buf, sizeof(buf), NULL, 0) == ARGO_RESULT_NO_ERROR) {
            ZPN_LOG(AL_DEBUG, "sitec Command Probe : Rx: %s", buf);
        }
    }

    if (admin_probe_collection) {
        argo_log_structure_immediate(admin_probe_collection,
                                     argo_log_priority_info,
                                     0,
                                     "sitec_status_report",
                                     zpn_command_probe_status_description,
                                     report);
    }


    ZPN_LOG(AL_NOTICE, "Command Probe %s : zpn_command_probe_status report from sitec", report->command_uuid? report->command_uuid: "");

    return zpath_customer_log_struct(report->customer_gid,
                                     zpath_customer_log_type_admin_probe,
                                     "command_probe",
                                     NULL,
                                     NULL,
                                     NULL,
                                     NULL,
                                     zpn_command_probe_status_description,
                                     report);
}

static int zpn_broker_sitec_decrypt_key_req_cb(void* argo_cookie_ptr,
                                               void* argo_structure_cookie_ptr,
                                               struct argo_object *object)
{
    struct zpn_decrypt_key_request *req = (struct zpn_decrypt_key_request *)object->base_structure_void;
    zpn_broker_cryptosvc_decrypt_key(argo_structure_cookie_ptr, req);

    return FOHH_RESULT_NO_ERROR;
}

static int zpn_broker_sitec_file_key_decrypt_cb(void* argo_cookie_ptr,
                                                void* argo_structure_cookie_ptr,
                                                struct argo_object *object)
{
    struct zpn_file_fetch_key *key_t = object->base_structure_void;

    struct zpn_file_key *key = ZPN_CALLOC(sizeof(struct zpn_file_key));
    if (NULL == key) {
        ZPN_LOG(AL_ERROR, "Broker failed mem alloc for pvt key decryption for file: %s", key_t->filename);
        return ZPN_RESULT_ERR;
    }

    key->f_conn = argo_structure_cookie_ptr;
    key->fetch_retry_count = 0;
    key->is_update = key_t->is_update;
    snprintf(key->enc_or_dec_pvt_key, KEY_BUFF_SIZE, "%s", key_t->enc_or_dec_pvt_key);
    snprintf(key->enc_or_dec_pvt_key2, KEY_BUFF_SIZE, "%s", key_t->enc_or_dec_pvt_key2);
    snprintf(key->filename, FILE_SIZE, "%s", key_t->filename);
    snprintf(key->version, VERSION_LEN, "%s", key_t->version);
    if(zpn_broker_pbroker_cryptosvc_get_decrypted_key(key)){
        ZPN_LOG(AL_DEBUG, "zpn_broker_pbroker_key_decrypt_cb: decrypt failed!.");
    }

    return FOHH_RESULT_NO_ERROR;
}

static int zpn_broker_sitec_mission_critical_conn_callback(struct fohh_connection *connection,
                                                        enum fohh_connection_state state,
                                                        void *cookie)
{
    int res = 0;
    /* As of now, this channel will be used for firedrill data fetching */
    ZPN_LOG(AL_NOTICE, "%ld: %s: scmc mission critical connection entered", (long)fohh_peer_get_id(connection), fohh_description(connection));

    if (state == fohh_connection_connected) {

        ZPN_LOG(AL_NOTICE, "%ld: %s: scmc mission critical connected", (long)fohh_peer_get_id(connection), fohh_description(connection));

        /* Connected, register callbacks */
        struct argo_state *argo = fohh_argo_get_rx(connection);

        if ((res = argo_register_structure(argo, zpn_broker_mission_critical_description, zpn_broker_mission_critical_cb, connection))) {
            ZPN_LOG(AL_ERROR, "Could not register zpn_sitec_status_report for private broker for %s", fohh_description(connection));
            return res;
        }

    } else{
        /* disconnect and update stats, what else ? */
        const char *reason = fohh_close_reason(connection);
        ZPN_LOG(AL_NOTICE, "%s: sitec scmc connection DOWN due to %s", fohh_description(connection), reason);
    }
    return res;
}

static int zpn_broker_sitec_ctrl_conn_callback(struct fohh_connection *connection,
                                               enum fohh_connection_state state,
                                               void *cookie)
{
    int64_t sc_gid;
    int64_t customer_gid = 0;
    struct zpath_customer *customer = NULL;
    struct zpn_broker_sitec_fohh_state *sc_state;
    int res;
    struct zpn_site_controller *sitec;
    struct zpn_site_controller_to_group *sitec_to_grp;

    if (state == fohh_connection_connected) {
        ZPN_LOG(AL_NOTICE, "%"PRId64": %s: SC control connection connected", fohh_peer_get_id(connection), fohh_description(connection));

        sc_state = fohh_connection_get_dynamic_cookie(connection);
        if (sc_state) {
            ZPN_LOG(AL_ERROR, "%s: Pre-existing dynamic cookie on connected", fohh_description(connection));
            return ZPN_RESULT_ERR;
        }

        sc_gid = fohh_peer_get_id(connection);

        /* Drop connection when customer is disabled */
        customer_gid = ZPATH_GID_GET_CUSTOMER_GID(sc_gid);
        if (customer_gid != 0) {
            res = zpath_customer_get(customer_gid,
                                     &customer,
                                     NULL,
                                     NULL,
                                     0);

            if (res == ZPN_RESULT_NOT_FOUND || (res == ZPN_RESULT_NO_ERROR && customer->disable_traffic == 1)) {
                ZPN_DEBUG_SITEC("%s: Could not accept connection with sc(%"PRId64"), customer(%"PRId64") is not found or is disabled",
                            fohh_description(connection), sc_gid, customer_gid);
                return ZPN_RESULT_ERR;
            }
        }

        sc_state = ZPN_CALLOC(sizeof(*sc_state));
        fohh_connection_set_dynamic_cookie(connection, sc_state);

        /* Generate an ID for this connection */
        res = RAND_bytes(&(sc_state->tunnel_id_bin[0]), ZPN_TUNNEL_ID_BYTES);
        if (1 != res) {
            ZPN_BROKER_ASSERT_HARD(0, "Crypto Random Generator not seeded!");
            ZPN_FREE(sc_state);
            return res;
        }
        base64_encode_binary(sc_state->tunnel_id, sc_state->tunnel_id_bin, ZPN_TUNNEL_ID_BYTES);
        sc_state->log.tunnel_id = sc_state->tunnel_id;
        sc_state->log.gid = sc_gid;
        sc_state->log.g_cst = ZPATH_GID_GET_CUSTOMER_GID(sc_state->log.gid);

        res = zpn_sitec_get_by_id(fohh_peer_get_id(connection),
                                  &sitec,
                                  NULL,
                                  NULL,
                                  0);
        if (res) {
            ZPN_LOG(AL_WARNING, "Error fetching sitec %"PRId64": %s", fohh_peer_get_id(connection), zpn_result_string(res));
        } else {
            sc_state->log.g_microtenant = is_scope_default(sitec->scope_gid) ? 0 : sitec->scope_gid;
        }

        size_t row_count = 1;
        res = zpn_sitec_to_group_get_by_sitec_gid(sc_state->log.gid,
                                                  &sitec_to_grp,
                                                  &row_count,
                                                  1,
                                                  NULL,
                                                  NULL,
                                                  0);
        if (res == ZPN_RESULT_NO_ERROR) {
            sc_state->log.grp_gid = sitec_to_grp->site_controller_group_gid;
            ZPN_LOG(AL_NOTICE, "%s: %"PRId64": Retrieved sitec group as %"PRId64"", fohh_description(connection), sc_state->log.gid, sc_state->log.grp_gid);

            /* If we have both reveived our first TCP info update as well
            * as figured out the pb group (we are here aren't we??) then
            * send pb auth log */
            if (sc_state->received_tcp_info) {
                sc_auth_log(connection, fohh_get_state(connection));
            }
        }

        /* Set up monitor timer. Note: First auth log is generated when we get tcp_info_report. */
        sc_state->timer = event_new(zevent_event_base(zevent_self()),
                                    -1,
                                    EV_PERSIST,
                                    zpn_broker_sitec_ctrl_conn_monitor_cb,
                                    connection);

        if (!sc_state->timer) {
            ZPN_LOG(AL_CRITICAL, "Memory Allocation failed");
            ZPN_FREE(sc_state);
            return FOHH_RESULT_NO_MEMORY;
        }

        struct timeval tv;
        tv.tv_sec = ZPN_TUNNEL_MONITOR_INTERVAL_S;
        tv.tv_usec = 0;
        if (event_add(sc_state->timer, &tv)) {
            ZPN_LOG(AL_CRITICAL, "Could not add sitec timer");
            event_free(sc_state->timer);
            ZPN_FREE(sc_state);
            return FOHH_RESULT_NO_MEMORY;
        }

        zpn_broker_store_tunnel_info_by_sc_gid(sc_state->log.gid,
                                                sc_state->tunnel_id,
                                                connection);
        ZPN_LOG(AL_DEBUG, "%s: Connected to sitec with gid: %"PRId64" with cn %s",
                fohh_description(connection), sc_state->log.gid, fohh_peer_cn(connection));

        /* Connected, register callbacks */
        struct argo_state *argo = fohh_argo_get_rx(connection);

        if ((res = argo_register_structure(argo, zpn_sitec_status_report_description, zpn_broker_sitec_status_report_cb, connection))) {
            ZPN_LOG(AL_ERROR, "Could not register zpn_sitec_status_report for private broker for %s", fohh_description(connection));
            return res;
        }
        if ((res = argo_register_structure(argo, zpn_sitec_environment_report_description, zpn_broker_sitec_environment_report_cb, connection))) {
            ZPN_LOG(AL_ERROR, "Could not register zpn_sitec_environment_report for private broker for %s", fohh_description(connection));
            return res;
        }
        if ((res = argo_register_structure(argo, zpn_tcp_info_report_description, zpn_broker_sitec_tcp_info_report_cb, connection))) {
            ZPN_LOG(AL_ERROR, "Could not register zpn_tcp_info_report for connection %s", fohh_description(connection));
            return res;
        }
        if ((res = argo_register_structure(argo, zpn_command_probe_status_description, zpn_broker_sitec_admin_probe_status_report_cb, connection))) {
            ZPN_LOG(AL_ERROR, "Could not register zpn_command_probe_status_description for connection %s", fohh_description(connection));
            return res;
        }

        if ((res = argo_register_structure(argo, zpn_file_fetch_key_description, zpn_broker_sitec_file_key_decrypt_cb, connection))) {
            ZPN_LOG(AL_ERROR, "Could not register zpn_file_fetch_key for connection %s", fohh_description(connection));
            return res;
        }

        if ((res = argo_register_structure(argo, zpn_decrypt_key_request_description, zpn_broker_sitec_decrypt_key_req_cb, connection))) {
            ZPN_LOG(AL_ERROR, "Could not register zpn_decrypt_key_request for connection %s", fohh_description(connection));
            return res;
        }

        zpn_fohh_worker_sitec_connect_ctrl(fohh_connection_get_thread_id(connection));
    } else {
        const char *reason = fohh_close_reason(connection);
        ZPN_LOG(AL_NOTICE, "%"PRId64": %s: sitec control connection disconnected: %s", fohh_peer_get_id(connection), fohh_description(connection), reason);

        if (connection->state == fohh_connection_connected) {
            zpn_fohh_worker_sitec_disconnect_ctrl(fohh_connection_get_thread_id(connection));
        }

        sc_state = fohh_connection_get_dynamic_cookie(connection);
        if (sc_state) {
            if(zpn_is_broker_auth_log_redirect_status_feature_enabled() && sc_state->is_redirect_sent) {
                ZPN_LOG(AL_DEBUG, "%s: Overriding close_reason to %s", fohh_description(connection), FOHH_CLOSE_REASON_BROKER_REDIRECT);
                sc_state->log.close_reason = FOHH_CLOSE_REASON_BROKER_REDIRECT;
                sc_state->is_redirect_sent = 0;
            } else {
                sc_state->log.close_reason = reason;
            }

            sc_auth_log(connection, fohh_connection_disconnected);

            fohh_connection_set_dynamic_cookie(connection, NULL);
            if (sc_state->log_version) {
                ZPN_FREE(sc_state->log_version);
                sc_state->log_version = NULL;
            }
            if (sc_state->log_sarge_version) {
                ZPN_FREE(sc_state->log_sarge_version);
                sc_state->log_sarge_version = NULL;
            }
            if (sc_state->log.platform) {
                ZPN_FREE(sc_state->log.platform);
                sc_state->log.platform = NULL;
            }
            if (sc_state->log.platform_detail) {
                ZPN_FREE(sc_state->log.platform_detail);
                sc_state->log.platform_detail = NULL;
            }
            if (sc_state->log.runtime_os) {
                ZPN_FREE(sc_state->log.runtime_os);
                sc_state->log.runtime_os = NULL;
            }

            if(sc_state->log.geoip_version) {
                ZPN_FREE(sc_state->log.geoip_version);
                sc_state->log.geoip_version = NULL;
            }

            if(sc_state->log.isp_version) {
                ZPN_FREE(sc_state->log.isp_version);
                sc_state->log.isp_version = NULL;
            }

            if (sc_state->timer) {
                event_del(sc_state->timer);
                event_free(sc_state->timer);
                sc_state->timer = NULL;
            }

            if (sc_state->log.cc) {
                ZPN_FREE(sc_state->log.cc);
                sc_state->log.cc = NULL;
            }

            if (sc_state->log.dft_rt_intf) {
                ZPN_FREE(sc_state->log.dft_rt_intf);
                sc_state->log.dft_rt_intf = NULL;
            }

            if (sc_state->log.platform_version) {
                ZPN_FREE(sc_state->log.platform_version);
                sc_state->log.platform_version = NULL;
            }

            if (sc_state->log.status_conn_info) {
                ZPN_FREE(sc_state->log.status_conn_info);
                sc_state->log.status_conn_info = NULL;
            }

            cleanup_sc_auth_log_brokers_unique(&sc_state->log);
            cleanup_sc_auth_log_all_log_brokers_data(&sc_state->log);

            zpath_wrapper_zpn_broker_remove_tunnel_info_by_sc_gid(sc_state->log.gid);

            zpath_wrapper_zpn_free(sc_state, __LINE__, __FILE__);
        }
    }

    return FOHH_RESULT_NO_ERROR;
}

static int zpn_broker_sitec_ctrl_conn_unblock_callback(struct fohh_connection *connection,
                                                       enum fohh_queue_element_type element_type,
                                                       void *cookie)
{
    return FOHH_RESULT_NO_ERROR;
}

static void zpn_broker_sitec_config_conn_monitor_cb(evutil_socket_t sock, short flags, void *cookie)
{
    int res;
    int64_t customer_id = 0;
    struct zpath_customer *customer = NULL;
    struct connected_sitec_config_fohh_state *sc_state = cookie;
    if (!sc_state) {
        ZPN_LOG(AL_NOTICE, "sitec config conn monitor cb has no cookie");
        return;
    }

    sc_state->monitor_count++;

    struct fohh_connection *f_conn = sc_state->f_conn;

    /* Drop connection when broker is shutting down. */
    if (disconnect_sitec) {
        const char *reason = disconnect_sitec_reason ? : FOHH_CLOSE_REASON_UPGRADE;
        ZPN_LOG(AL_INFO, "%s: Disconnect sitec %s connection due to %s",
                fohh_description(f_conn), sc_state->type, reason);
        fohh_connection_delete(f_conn, reason);
        return;
    }

    /* Drop connection when customer is disabled */
    customer_id = ZPATH_GID_GET_CUSTOMER_GID(fohh_peer_get_id(f_conn));
    if (customer_id != 0) {
        res = zpath_customer_get(customer_id,
                                 &customer,
                                 NULL,
                                 NULL,
                                 0);

        if (res == ZPN_RESULT_NOT_FOUND ||
            (res == ZPN_RESULT_NO_ERROR && customer->disable_traffic == 1)) {
            ZPN_LOG(AL_NOTICE, "%s: Disconnecting sitec(%"PRId64") as customer(%"PRId64") is not found or is disabled", fohh_description(f_conn), fohh_peer_get_id(f_conn), customer_id);
            fohh_connection_delete(f_conn, ZPN_ERR_CUSTOMER_DISABLED);
            return;
        }
    }

    if (redirect_sitec &&
        ((sc_state->monitor_count % ZPN_TUNNEL_MONITOR_REDIRECT_INTERVAL_COUNT) == 0
         || strcmp(redirect_sitec_reason, BRK_REDIRECT_REASON_BROKER_RESTART) == 0)) {
        ZPN_LOG(AL_INFO, "%s: Redirect sitec %s connection due to %s",
                fohh_description(f_conn),
                sc_state->type,
                redirect_sitec_reason ? : FOHH_CLOSE_REASON_UPGRADE);
        zpn_broker_sitec_config_conn_redirect(sc_state->f_conn);
    }
}

static int zpn_broker_sitec_create_config_conn_cookie(struct fohh_connection *f_conn,
                                                      enum sitec_config_conn_type type)
{
    static const char *conn_type_text[] = {
        "config",
        "static_config",
        "override",
        "userdb",
    };

    struct connected_sitec_config_fohh_state *sc_state = ZPN_CALLOC(sizeof(*sc_state));

    /* Generate an ID for this client */
    const int res = RAND_bytes(&(sc_state->tunnel_id_bin[0]), ZPN_TUNNEL_ID_BYTES);
    if (1 != res) {
        ZPN_BROKER_ASSERT_HARD(0, "Crypto Random Generator not seeded!");
        ZPN_FREE(sc_state);
        return FOHH_RESULT_ERR;
    }
    base64_encode_binary(sc_state->tunnel_id, sc_state->tunnel_id_bin, ZPN_TUNNEL_ID_BYTES);

    sc_state->timer = event_new(zevent_event_base(zevent_self()),
                                -1,
                                EV_PERSIST,
                                zpn_broker_sitec_config_conn_monitor_cb,
                                sc_state);
    if (!sc_state->timer) {
        ZPN_LOG(AL_CRITICAL, "Memory allocation failed");
        ZPN_FREE(sc_state);
        return FOHH_RESULT_NO_MEMORY;
    }

    struct timeval tv;
    tv.tv_sec = ZPN_TUNNEL_MONITOR_INTERVAL_S;
    tv.tv_usec = 0;
    if (event_add(sc_state->timer, &tv)) {
        ZPN_LOG(AL_CRITICAL, "Could not add sitec config timer");
        event_free(sc_state->timer);
        ZPN_FREE(sc_state);
        return FOHH_RESULT_NO_MEMORY;
    }

    sc_state->type = conn_type_text[type];
    sc_state->f_conn = f_conn;

    fohh_connection_set_dynamic_cookie(f_conn, sc_state);
    return FOHH_RESULT_NO_ERROR;
}

static void zpn_broker_sitec_destroy_config_conn_cookie(struct fohh_connection *f_conn)
{
    struct connected_sitec_config_fohh_state *sc_state = fohh_connection_get_dynamic_cookie(f_conn);
    if (sc_state) {
        if (sc_state->timer) {
            event_free(sc_state->timer);
        }
        fohh_connection_set_dynamic_cookie(f_conn, NULL);
        ZPN_FREE(sc_state);
    }
}

static int zpn_broker_sitec_config_override_conn_callback(struct fohh_connection *connection,
                                                          enum fohh_connection_state state,
                                                          void *cookie)
{
    ZPN_LOG(AL_DEBUG, "%s: sitec config override conn callback. Setting up filter tables", fohh_description(connection));

    static char *filter_tables[] = {
            "zpath_config_override",
            "et_customer_userdb",
            "et_customer_zone",
            "zpn_inspection_config_data",
            "zpn_inspection_zsdefined_control",
            "et_geoip_override"
    };
    static int filter_tables_count = sizeof(filter_tables) / sizeof(filter_tables[0]);

    static char *allow_tables[] = {
            "et_userdb",
            "et_translate",
            "et_translate_code",
            "zpath_table",
            "zpath_cloud"
    };
    static int allow_tables_count = sizeof(allow_tables) / sizeof(allow_tables[0]);

    static struct wally_column_nullify_config nullify_cfg[] = {
            {"et_userdb", "name"},
            {"et_userdb", "description"},
            {"et_userdb", "userdb_url"},
            {"et_userdb", "userdb_username"},
            {"et_userdb", "userdb_password"},
            {"zpath_cloud", "ip_anchor_clouds"},
            {"zpath_cloud", "secret"},
            {"zpath_cloud", "file_key"},
            {"zpath_cloud", "file_key2"},
    };
    static int nullify_cfg_count = sizeof(nullify_cfg) / sizeof(nullify_cfg[0]);

    if (state == fohh_connection_connected) {
        int res;
        struct zpath_customer *customer = NULL;
        /* Allocate state for this connection, so we can track its stuff... */
        int64_t gid = fohh_peer_get_id(connection);
        int64_t customer_gid = ZPATH_GID_MASK_CUSTOMER & gid;
        int64_t global_gid = ZPATH_GLOBAL_CONFIG_OVERRIDE_GID;
        int64_t global_insp_gid = ZPN_GLOBAL_INSPECTION_CONFIG_GID;

        /* Drop connection when customer is disabled */
        if (customer_gid != 0) {
            res = zpath_customer_get(customer_gid,
                                     &customer,
                                     NULL,
                                     NULL,
                                     0);

            if (res == ZPN_RESULT_NOT_FOUND ||
                (res == ZPN_RESULT_NO_ERROR && customer->disable_traffic == 1)) {
                ZPN_DEBUG_SITEC("%s: Could not accept connection with sitec(%"PRId64"), customer(%"PRId64") is not found or is disabled", fohh_description(connection), gid, customer_gid);
                return FOHH_RESULT_ERR;
            }
        }

        struct filter_entry entries[] = {
                    /* TABLE NAME                   COL NAME           KEY              KEYSIZE  */
                {"et_customer_userdb",              "customer_gid", &customer_gid,    sizeof(int64_t)},
                {"et_customer_zone",                "customer_gid", &customer_gid,    sizeof(int64_t)},
                {"zpath_config_override",           "customer_gid", &customer_gid,    sizeof(int64_t)},
                {"zpath_config_override",           "customer_gid", &global_gid,      sizeof(int64_t)},
                {"zpn_inspection_config_data",      "customer_gid", &customer_gid,    sizeof(int64_t)},
                {"zpn_inspection_config_data",      "customer_gid", &global_insp_gid, sizeof(int64_t)},
                {"zpn_inspection_zsdefined_control","customer_gid", &customer_gid,    sizeof(int64_t)},
                {"zpn_inspection_zsdefined_control","customer_gid", &global_insp_gid, sizeof(int64_t)},
                {"et_geoip_override",               "customer_gid", &customer_gid,    sizeof(int64_t)}
        };
        struct zhash_table* table_filter = NULL;

        zpn_fohh_worker_sitec_connect_override(fohh_connection_get_thread_id(connection));

        if (add_multiple_filter_keys(entries, sizeof(entries)/sizeof(struct filter_entry), &table_filter) != WALLY_RESULT_NO_ERROR) {
            ZPN_LOG(AL_ERROR, "Failed to add filter table entries");
            return FOHH_RESULT_ERR;
        }

        struct zhash_table *nullify_state = wally_column_nullify_build_state(nullify_cfg, nullify_cfg_count);
        if (!nullify_state) {
            ZPN_LOG(AL_ERROR, "Could not initialize wally column nullify state");
            deallocate_table_filter(&table_filter);
            return FOHH_RESULT_ERR;
        }

        if (!wally_fohh_create_fohh_from_client(wally_server_global,
                                                connection,
                                                allow_tables,
                                                allow_tables_count,
                                                filter_tables,
                                                filter_tables_count,
                                                ZPATH_GID_MASK_CUSTOMER,
                                                ZPATH_GID_MASK_CUSTOMER & gid,
                                                global_gid,
                                                table_filter,
                                                nullify_state,
                                                zpn_broker_sitec_config_override_conn_callback)) {
            ZPN_LOG(AL_ERROR, "Could not hand off FOHH to wally for sitec override: %s",
                    fohh_description(connection));
            return FOHH_RESULT_ERR;
        }

        res = zpn_broker_sitec_create_config_conn_cookie(connection, sitec_override);
        if (res) {
            return res;
        }

        ZPN_LOG(AL_NOTICE, "%s: sitec override connection UP", fohh_description(connection));

    } else {
        /* Connection probably went away... */
        if (connection->state == fohh_connection_connected) {
            /* We only count report connection going down when previous state is CONNECTED */
            ZPN_LOG(AL_NOTICE, "%s: sitec override connection DOWN", fohh_description(connection));

            zpn_broker_sitec_destroy_config_conn_cookie(connection);
            zpn_fohh_worker_sitec_disconnect_override(fohh_connection_get_thread_id(connection));
        }
    }

    return FOHH_RESULT_NO_ERROR;
}

static int zpn_broker_sitec_config_override_unblock_callback(struct fohh_connection *connection,
                                                             enum fohh_queue_element_type element_type,
                                                             void *cookie)
{
    return FOHH_RESULT_NO_ERROR;
}

static int zpn_broker_sitec_config_conn_callback(struct fohh_connection *connection,
                                                 enum fohh_connection_state state,
                                                 void *cookie)
{
    ZPN_LOG(AL_DEBUG, "%s: sitec Config conn callback. Setting up filter tables",
            fohh_description(connection));
    static char *allow_tables[] =
        {
         "zpn_rule_condition_operand",
         "zpn_rule_condition_set",
         "zpn_rule_to_server_group",
         "zpn_rule_to_assistant_group",
         "zpn_client",
         "zpn_issuedcert",
         "zpn_private_broker_group",
         "zpn_private_broker_to_group",
         "zpn_machine",
         "zpn_machine_group",
         "zpn_machine_to_group",
         "zpn_inspection_application",
         "zpn_approval",
         "zpn_approval_mapping",
         "zpn_location",
         "zpath_table"
        };
    static int allow_tables_count = sizeof(allow_tables) / sizeof(allow_tables[0]);

    static char *filter_tables[] =
        {
         "zpath_customer",
         "zpn_app_group_relation",
         "zpn_app_server",
         "zpn_application",
         "zpn_application_domain",
         "zpn_application_group",
         "zpn_application_group_application_mapping",
         "zpn_assistant",
         "zpn_assistant_group",
         "zpn_assistant_version",
         "zpn_assistantgroup_assistant_relation",
         "zpn_customer_config",
         "zpn_client_setting",
         "zpn_c2c_client_registration",
         "zpn_c2c_ip_ranges",
         "zpn_idp",
         "zpn_idp_cert",
         "zpn_inspection_application",
         "zpn_approval",
         "zpn_approval_mapping",
         "zpn_policy_set",
         "zpn_posture_profile_db",
         "zpn_private_broker",
         "zpn_private_broker_load",
         "zpn_private_broker_version",
         "zpn_sub_module_upgrade",
         "zpn_customer_resiliency_settings",
         "zpn_privatebrokergroup_trustednetwork_mapping",
         "zpn_rule",
         "zpn_saml_attrs",
         "zpn_scim_attr_header",
         "zpn_server_group",
         "zpn_server_group_assistant_group",
         "zpn_servergroup_server_relation",
         "zpn_shared_customer_domain",
         "zpn_signing_cert",
         "zpn_trusted_network",
         "zpn_znf",
         "zpn_znf_to_group",
         "zpn_znf_group",
         "zpn_command_probe",
         "zpn_scope",
         "zpn_branch_connector",
         "zpn_branch_connector_to_group",
         "zpn_branch_connector_group",
         "zpn_svcp_profile",
         "zpn_cbi_mapping",
         "zpn_cbi_profile",
         "zpn_ddil_config",
         "zpn_site",
         "zpn_site_controller",
         "zpn_site_controller_group",
         "zpn_site_controller_to_group",
         "zpn_site_controller_version",
         "zpn_rule_to_pse_group",
         "zpn_inspection_profile_to_control",
         "zpn_inspection_profile",
         "zpn_inspection_prof_to_zsdefined_ctrl",
         "zpn_public_cert",
         "zpn_siem",
         "zpath_category",
         "zpath_rule",
         "zpath_service",
         "zpn_workload_tag_group",
         "zpn_step_up_auth_level",
         "zpn_rule_to_step_up_auth_level_mapping",
         "zpn_firedrill_site"
        };
    static int filter_tables_count = sizeof(filter_tables) / sizeof(filter_tables[0]);

    if (state == fohh_connection_connected) {
        int res;
        struct zpath_customer *customer = NULL;
        /* Allocate state for this connection, so we can track its stuff... */
        int64_t gid = fohh_peer_get_id(connection);
        int64_t customer_gid = ZPATH_GID_MASK_CUSTOMER & gid;
        int shard = ZPATH_SHARD_FROM_GID(gid);
        char hostname[ARGO_MAX_NAME_LENGTH];

        zpn_fohh_worker_sitec_connect_config(fohh_connection_get_thread_id(connection));

        if (!gid || !shard || (shard > ZPATH_CLOUD_SHARD_COUNT)) {
            ZPN_LOG(AL_ERROR, "Broker received sitec config connection: %s, sitec = %"PRId64", shard = %d (Out of range)", fohh_description(connection), gid, shard);
            return FOHH_RESULT_ERR;
        }

        /* Drop connection when customer is disabled */
        if (customer_gid != 0) {
            res = zpath_customer_get(customer_gid,
                                     &customer,
                                     NULL,
                                     NULL,
                                     0);

            if (res == ZPN_RESULT_NOT_FOUND ||
                (res == ZPN_RESULT_NO_ERROR && customer->disable_traffic == 1)) {
                ZPN_DEBUG_SITEC("%s: Could not accept connection with sitec(%"PRId64"), customer(%"PRId64") is not found or is disabled", fohh_description(connection), gid, customer_gid);
                return FOHH_RESULT_ERR;
            }
        }

        struct filter_entry entries[] = {
            /* TABLE NAME                     COL NAME        KEY             KEYSIZE  */
            {"zpn_rule_condition_operand",    "customer_gid", &customer_gid, sizeof(int64_t)},
            {"zpn_rule_condition_set",        "customer_gid", &customer_gid, sizeof(int64_t)},
            {"zpn_client",                    "customer_gid", &customer_gid, sizeof(int64_t)},
            {"zpn_issuedcert",                "customer_gid", &customer_gid, sizeof(int64_t)},
            {"zpn_private_broker_to_group",   "private_broker_gid", &gid, sizeof(int64_t)},
            {"zpn_private_broker_to_group",   "customer_gid",  &customer_gid, sizeof(int64_t)},
            {"zpn_private_broker_group",      "customer_gid",  &customer_gid, sizeof(int64_t)},
            {"zpn_inspection_application",    "customer_gid",  &customer_gid, sizeof(int64_t)},
            {"zpn_approval",                  "customer_gid",  &customer_gid, sizeof(int64_t)},
            {"zpn_approval_mapping",          "customer_gid",  &customer_gid, sizeof(int64_t)},
            {"zpn_location",                  "customer_gid",  &customer_gid, sizeof(int64_t)}
        };
        struct zhash_table* table_filter = NULL;

        if (add_multiple_filter_keys(entries, sizeof(entries)/sizeof(struct filter_entry), &table_filter) != WALLY_RESULT_NO_ERROR) {
            ZPN_LOG(AL_ERROR, "Failed to add filter table entries");
            return FOHH_RESULT_ERR;
        }

        res = zpn_broker_sitec_create_config_conn_cookie(connection, sitec_config);
        if (res) {
            ZPN_LOG(AL_ERROR, "Failed to create config connection cookie");
            return res;
        }

        snprintf(hostname, sizeof(hostname), "s%d-wally.%s", shard, ZPATH_LOCAL_CLOUD_NAME);

        if (!wally_fohh_rpc_proxy_create(fohh_connection_get_dynamic_cookie(connection),
                                         zpath_shard_wally[shard],
                                         hostname,
                                         htons(ZPATH_LOCAL_FOHH_PORT),
                                         connection,
                                         allow_tables,
                                         allow_tables_count,
                                         filter_tables,
                                         filter_tables_count,
                                         ZPATH_GID_MASK_CUSTOMER,
                                         ZPATH_GID_MASK_CUSTOMER & gid,
                                         0,
                                         table_filter,
                                         NULL,
                                         zpn_broker_sitec_config_conn_callback)) {
            ZPN_LOG(AL_ERROR, "Could not hand off FOHH to wally for sitec config: %s",
                    fohh_description(connection));
            return FOHH_RESULT_ERR;
        }

        ZPN_LOG(AL_NOTICE, "%s: sitec config connection UP", fohh_description(connection));
    } else {
        /* Connection probably went away... */
        if (connection->state == fohh_connection_connected) {
            /* We only count report connection going down when previous state is CONNECTED */
            ZPN_LOG(AL_NOTICE, "%s: sitec config connection DOWN", fohh_description(connection));

            zpn_broker_sitec_destroy_config_conn_cookie(connection);
            zpn_fohh_worker_sitec_disconnect_config(fohh_connection_get_thread_id(connection));
        }
    }

    return FOHH_RESULT_NO_ERROR;
}

static int zpn_broker_sitec_config_unblock_callback(struct fohh_connection *connection,
                                                    enum fohh_queue_element_type element_type,
                                                    void *cookie)
{
    return FOHH_RESULT_NO_ERROR;
}

int zpn_broker_sitec_userdb_sni_get_identifier(const char *sni, int64_t *zone, int64_t *userdb)
{
    char *walk = NULL;
    int64_t cst_gid = strtoll(sni, &walk, 10);
    if (cst_gid == 0 || *walk != '.')
        return ZPN_RESULT_BAD_ARGUMENT;

    walk++;
    int64_t my_zone = strtoll(walk, &walk, 10);
    if (!my_zone || *walk != '.')
        return ZPN_RESULT_BAD_ARGUMENT;

    walk++;
    int64_t my_userdb = strtoll(walk, &walk, 10);
    if (!my_userdb)
        return ZPN_RESULT_BAD_ARGUMENT;

    *zone = my_zone;
    *userdb = my_userdb;

    return ZPN_RESULT_NO_ERROR;
}

static int zpn_broker_sitec_userdb_conn_callback(struct fohh_connection *connection,
                                                 enum fohh_connection_state state,
                                                 void *cookie)
{
    ZPN_LOG(AL_DEBUG, "%s: sitec Userdb conn callback. Setting up filter tables", fohh_description(connection));

    static char *allow_tables[] = {
            "zpn_scim_user",
            "zpn_scim_user_attribute",
            "zpn_scim_user_group",
            "zpn_scim_group",
            "zpn_user_risk",
            "zpath_table",
            "zpn_aae_profile_conclusion"
    };
    static int allow_tables_count = sizeof(allow_tables) / sizeof(allow_tables[0]);

    if (state == fohh_connection_connected) {
        int res;
        int64_t zone;
        int64_t userdb;
        struct zpath_customer *customer = NULL;
        int64_t gid = fohh_peer_get_id(connection);
        int64_t customer_gid = ZPATH_GID_MASK_CUSTOMER & gid;
        char hostname[ARGO_MAX_NAME_LENGTH];

        if (zpn_broker_sitec_userdb_sni_get_identifier(connection->sni_name, &zone, &userdb)) {
            ZPN_LOG(AL_ERROR, "Failed to parse zone/userdb in pbuserdb sni (sni=%s)", connection->sni_name);
            return FOHH_RESULT_ERR;
        }

        /* Drop connection when customer is disabled */
        if (customer_gid != 0) {
            res = zpath_customer_get(customer_gid,
                                     &customer,
                                     NULL,
                                     NULL,
                                     0);

            if (res == ZPN_RESULT_NOT_FOUND || (res == ZPN_RESULT_NO_ERROR && customer->disable_traffic == 1)) {
                ZPN_DEBUG_SITEC("%s: Could not accept connection with sitec(%"PRId64"), customer(%"PRId64") is not found or is disabled",
                            fohh_description(connection), fohh_peer_get_id(connection), customer_gid);
                return ZPN_RESULT_ERR;
            }
        }

        struct filter_entry entries[] = {
                /*  TABLE NAME                 COL NAME         KEY           KEYSIZE  */
                {"zpn_scim_user",           "customer_gid", &customer_gid, sizeof(int64_t)},
                {"zpn_scim_user_attribute", "customer_gid", &customer_gid, sizeof(int64_t)},
                {"zpn_scim_user_group",     "customer_gid", &customer_gid, sizeof(int64_t)},
                {"zpn_scim_group",          "customer_gid", &customer_gid, sizeof(int64_t)},
                {"zpn_user_risk",           "customer_gid", &customer_gid, sizeof(int64_t)},
                {"zpn_aae_profile_conclusion",  "customer_gid", &customer_gid, sizeof(int64_t)}
        };
        struct zhash_table* table_filter = NULL;

        if (add_multiple_filter_keys(entries, sizeof(entries)/sizeof(struct filter_entry), &table_filter) != WALLY_RESULT_NO_ERROR) {
            ZPN_LOG(AL_ERROR, "Failed to add filter table entries");
            return FOHH_RESULT_ERR;
        }

        snprintf(hostname, sizeof(hostname), "cz%dd%d-wally.%s", (int) zone, (int) userdb, ZPATH_LOCAL_CLOUD_NAME);
        if (!wally_fohh_rpc_proxy_create(fohh_connection_get_dynamic_cookie(connection),
                                         zpath_et_wally_userdb_get_userdb_wally(zone, userdb),
                                         hostname,
                                         htons(ZPATH_LOCAL_FOHH_PORT),
                                         connection,
                                         allow_tables,
                                         allow_tables_count,
                                         NULL,
                                         0,
                                         ZPATH_GID_MASK_CUSTOMER,
                                         customer_gid,
                                         0,
                                         table_filter,
                                         NULL,
                                         zpn_broker_sitec_userdb_conn_callback)) {
            ZPN_LOG(AL_ERROR, "Could not hand off FOHH to wally for sitec userdb: %s",
                    fohh_description(connection));
            return FOHH_RESULT_ERR;
        }

        res = zpn_broker_sitec_create_config_conn_cookie(connection, sitec_userdb);
        if (res) {
            return res;
        }

        /* Previous changes were because of ET-44125, we don't need that now, current changes are part of ET-89307 */
        zpn_fohh_worker_sitec_connect_userdb(fohh_connection_get_thread_id(connection));
        ZPN_LOG(AL_NOTICE, "%s: sitec userdb connection UP", fohh_description(connection));

    } else {
        /* Connection probably went away... */
        if (connection->state == fohh_connection_connected) {
            /* We only count report connection going down when previous state is CONNECTED */
            ZPN_LOG(AL_NOTICE, "%s: sitec userdb connection DOWN", fohh_description(connection));

            fohh_connection_set_dynamic_cookie(connection, cookie);
            zpn_broker_sitec_destroy_config_conn_cookie(connection);
            zpn_fohh_worker_sitec_disconnect_userdb(fohh_connection_get_thread_id(connection));
        }
        return FOHH_RESULT_ERR;
    }

    return FOHH_RESULT_NO_ERROR;
}

static int zpn_broker_sitec_userdb_unblock_callback(struct fohh_connection *connection,
                                                    enum fohh_queue_element_type element_type,
                                                    void *cookie)
{
    ZPN_LOG(AL_CRITICAL, "Implement me");
    return FOHH_RESULT_NO_ERROR;
}

static int zpn_broker_sitec_static_config_conn_callback(struct fohh_connection *connection,
                                                        enum fohh_connection_state state,
                                                        void *cookie)
{
    ZPN_LOG(AL_DEBUG, "%s: sitec Static config conn callback. Setting up filter tables",
            fohh_description(connection));

    static char *allow_static_tables[] =
        {
         "zpn_client",
         "zpath_table"
        };
    static int allow_static_tables_count = sizeof(allow_static_tables) / sizeof(allow_static_tables[0]);

    static char **filter_static_tables = NULL;
    static int filter_static_tables_count = 0;

    if (state == fohh_connection_connected) {
        int res;
        struct zpath_customer *customer = NULL;
        /* Allocate state for this connection, so we can track its stuff... */
        int64_t gid = fohh_peer_get_id(connection);
        int64_t customer_gid = ZPATH_GID_MASK_CUSTOMER & gid;
        int shard = ZPATH_SHARD_FROM_GID(gid);
        char hostname[ARGO_MAX_NAME_LENGTH];

        zpn_fohh_worker_sitec_connect_static_config(fohh_connection_get_thread_id(connection));

        if (!gid || !shard || (shard > ZPATH_CLOUD_SHARD_COUNT)) {
            ZPN_LOG(AL_ERROR, "Broker received sitec static config connection: %s, sitec = %"PRId64", shard = %d (Out of range)", fohh_description(connection), gid, shard);
            return FOHH_RESULT_ERR;
        }

        /* Drop connection when customer is disabled */
        if (customer_gid != 0) {
            res = zpath_customer_get(customer_gid,
                                     &customer,
                                     NULL,
                                     NULL,
                                     0);

            if (res == ZPN_RESULT_NOT_FOUND ||
                (res == ZPN_RESULT_NO_ERROR && customer->disable_traffic == 1)) {
                ZPN_DEBUG_SITEC("%s: Could not accept connection with sitec(%"PRId64"), customer(%"PRId64") is not found or is disabled", fohh_description(connection), gid, customer_gid);
                return FOHH_RESULT_ERR;
            }
        }

        struct filter_entry static_entries[] =
        {
            /* TABLE NAME                     COL NAME        KEY             KEYSIZE  */
            {"zpn_client",                    "customer_gid", &customer_gid, sizeof(int64_t)}
        };
        struct zhash_table* static_table_filter = NULL;

        if (add_multiple_filter_keys(static_entries, sizeof(static_entries)/sizeof(struct filter_entry), &static_table_filter) != WALLY_RESULT_NO_ERROR) {
            ZPN_LOG(AL_ERROR, "Failed to add filter static table entries");
            return FOHH_RESULT_ERR;
        }

        res = zpn_broker_sitec_create_config_conn_cookie(connection, sitec_static_config);
        if (res) {
            ZPN_LOG(AL_ERROR, "Failed to create static config connection cokkie");
            return res;
        }

        snprintf(hostname, sizeof(hostname), "r%d-wally.%s", shard, ZPATH_LOCAL_CLOUD_NAME);

        if (!wally_fohh_rpc_proxy_create(fohh_connection_get_dynamic_cookie(connection),
                                         zpath_shard_wally_static[shard],
                                         hostname,
                                         htons(ZPATH_LOCAL_FOHH_PORT),
                                         connection,
                                         allow_static_tables,
                                         allow_static_tables_count,
                                         filter_static_tables,
                                         filter_static_tables_count,
                                         ZPATH_GID_MASK_CUSTOMER,
                                         ZPATH_GID_MASK_CUSTOMER & gid,
                                         0,
                                         static_table_filter,
                                         NULL,
                                         zpn_broker_sitec_static_config_conn_callback)) {
            ZPN_LOG(AL_ERROR, "Could not hand off FOHH to wally for sitec static tables config: %s", fohh_description(connection));
            return FOHH_RESULT_ERR;
        }

        ZPN_LOG(AL_NOTICE, "%s: sitec static config connection UP", fohh_description(connection));

    } else {
        /* Connection probably went away... */
        if (connection->state == fohh_connection_connected) {
            /* We only count report connection going down when previous state is CONNECTED */
            ZPN_LOG(AL_NOTICE, "%s: sitec static config connection DOWN", fohh_description(connection));
            fohh_connection_set_dynamic_cookie(connection, cookie);
            zpn_broker_sitec_destroy_config_conn_cookie(connection);
            zpn_fohh_worker_sitec_disconnect_static_config(fohh_connection_get_thread_id(connection));
        }
    }

    return FOHH_RESULT_NO_ERROR;
}

static int zpn_broker_sitec_static_config_unblock_callback(struct fohh_connection *connection,
                                                             enum fohh_queue_element_type element_type,
                                                             void *cookie)
{
    ZPN_LOG(AL_CRITICAL, "Implement me");
    return FOHH_RESULT_NO_ERROR;
}

static void* zpn_broker_sitec_app_info_callback(struct fohh_connection *f_conn)
{
    int64_t sc_gid;
    struct sitec_log_info *sc_info;
    int res;

    sc_gid = fohh_peer_get_id(f_conn);
    if (!sc_gid) return NULL;

    sc_info = ZPN_CALLOC(sizeof(*sc_info));

    /* Generate an ID for this client */
    res = RAND_bytes(&(sc_info->tunnel_id_bin[0]), ZPN_TUNNEL_ID_BYTES);
    if (1 != res) {
        ZPN_BROKER_ASSERT_HARD(0, "Crypto Random Generator not seeded!");
        ZPN_FREE(sc_info);
        return NULL;
    }
    base64_encode_binary(sc_info->tunnel_id, sc_info->tunnel_id_bin, ZPN_TUNNEL_ID_BYTES);
    sc_info->f_conn = f_conn;
    sc_info->f_conn_incarnation = fohh_connection_incarnation(f_conn);

    return sc_info;
}

int zpn_broker_sitec_init()
{
    int res;
    res = zpn_broker_sitec_stats_init();
    if (res) {
        ZPN_LOG(AL_ERROR, "Error occurred while sitec stats init, error: %s", zpath_result_string(res));
        return res;
    }

    return ZPN_RESULT_NO_ERROR;
}

static int customer_log_collection_callback(struct customer_log_collection_info* cust_log_collection_info, void* data)
{
    if (!cust_log_collection_info) {
        return ZPN_RESULT_ERR;
    }

    int res;
    res = zpath_customer_log_struct(ZPATH_GID_GET_CUSTOMER_GID(cust_log_collection_info->peer_gid),
                                    cust_log_collection_info->customer_log_type,
                                    cust_log_collection_info->log_type,
                                    NULL,
                                    NULL,
                                    NULL,
                                    NULL,
                                    cust_log_collection_info->log_description,
                                    data);
    if (res) {
        ZPN_LOG(AL_WARNING, "Failed to log %s into infra: %s", cust_log_collection_info->log_type, zpn_result_string(res));
        return res;
    }

    return ZPN_RESULT_NO_ERROR;
}

int zpn_sc_pb_auth_log_siem_callback(struct customer_log_collection_info* cust_log_collection_info,
                                     void* data)
{
    if (!cust_log_collection_info) {
        return ZPN_RESULT_ERR;
    }

    zpn_broker_siem_pb_auth_log(data, 0);

    return ZPN_RESULT_NO_ERROR;
}

struct zhash_table *zpn_broker_sitec_create_log_collection_map()
{
    struct customer_log_collection_info *event_log;
    struct customer_log_collection_info *sc_asst_auth_log;
    struct customer_log_collection_info *sc_pb_auth_log;
    struct customer_log_collection_info *customer_auth_log;

    struct zhash_table* log_collection_map = zhash_table_alloc(&zpn_allocator);
    if (!log_collection_map) {
        ZPN_LOG(AL_ERROR, "Out of memory");
        return NULL;
    }

    event_log = create_customer_log_collection(NULL,
                                               0,
                                               NULL,
                                               zpn_event_collection,
                                               0,
                                               NULL,
                                               NULL,
                                               NULL,
                                               0);

    char* zpn_event_collection_name = argo_log_get_name(zpn_event_collection);
    if (zhash_table_store(log_collection_map,
                           zpn_event_collection_name,
                           strlen(zpn_event_collection_name),
                           1,
                           event_log)) {
        ZPN_LOG(AL_ERROR, "Failed to setup event log rx");
    }

    sc_asst_auth_log = create_customer_log_collection("asst_auth",
                                                      (int)zpath_customer_log_type_zpn_ast_auth,
                                                      zpn_ast_auth_log_description,
                                                      zpn_auth_collection,
                                                      1,
                                                      customer_log_collection_callback,
                                                      zpn_ast_auth_log_siem_callback,
                                                      zpn_ast_auth_log_siem_data_fill_callback,
                                                      1);

    char* sc_ast_auth_collection_name = "zpn_sc_asst_auth_log";
    if (zhash_table_store(log_collection_map, sc_ast_auth_collection_name, strlen(sc_ast_auth_collection_name), 1, sc_asst_auth_log)) {
        ZPN_LOG(AL_ERROR, "Failed to setup sc asst auth log rx");
    }

    sc_pb_auth_log = create_customer_log_collection("sys_auth",
                                                    (int)zpath_customer_log_type_zpn_sys_auth,
                                                    zpn_sys_auth_log_description,
                                                    zpn_pb_auth_collection,
                                                    1,
                                                    customer_log_collection_callback,
                                                    zpn_sc_pb_auth_log_siem_callback,
                                                    NULL,
                                                    1);

    char* sc_pb_auth_collection_name = "zpn_sc_pb_auth_log";
    if (zhash_table_store(log_collection_map, sc_pb_auth_collection_name, strlen(sc_pb_auth_collection_name), 1, sc_pb_auth_log)) {
        ZPN_LOG(AL_ERROR, "Failed to setup sc pb auth log rx");
    }

    customer_auth_log = create_customer_log_collection("client_auth",
                                                       (int)zpath_customer_log_type_zpn_auth,
                                                       zpn_auth_log_description,
                                                       zpn_auth_collection,
                                                       1,
                                                       customer_log_collection_callback,
                                                       zpn_auth_log_siem_callback,
                                                       zpn_auth_log_siem_data_fill_callback,
                                                       1);

    char* auth_collection_name = argo_log_get_name(zpn_auth_collection);
    if (zhash_table_store(log_collection_map,
                          auth_collection_name,
                          strlen(auth_collection_name),
                          1,
                          customer_auth_log)) {
        ZPN_LOG(AL_ERROR, "Failed to setup auth log rx");
    }

    return log_collection_map;
}

int zpn_broker_sitec_astats_log_upload_cb(void *argo_cookie_ptr,
                                          void *argo_structure_cookie_ptr,
                                          struct argo_object *object)
{
    struct fohh_connection *f_conn = argo_structure_cookie_ptr;
    struct sitec_stats_proxy_info *stats_info;
    struct argo_log *log = object->base_structure_void;
    int64_t asst_id = log->l_inst_gid;
    int64_t ast_grp = 0;

    stats_info = fohh_connection_get_dynamic_cookie(f_conn);
    if (!stats_info) {
        ZPN_LOG(AL_ERROR, "Could not send stats to customer kafka pipeline because dynamic cookie is NULL");
        return zpn_broker_assistant_stats_upload(f_conn, object, 0, NULL, 0, 0, 1, 0);
    }

    /* Fetch geoIP and gid from assistant group. Note- these were
        * prefetched during authentication, so should be
        * available directly */
    struct zpn_assistantgroup_assistant_relation *ag_relation;
    size_t count = 1;
    int res;
    res = zpn_assistantgroup_assistant_relation_get_by_assistant(asst_id,
                                                                 &ag_relation,
                                                                 &count,
                                                                 NULL,
                                                                 NULL,
                                                                 0);
    if (res) {
        ZPN_LOG(AL_WARNING, "%s: Error fetching assistantgroup_assistant_relation. Assistant ID = %"PRId64": %s",
                fohh_peer_cn(f_conn), asst_id, zpn_result_string(res));
    } else {
        ast_grp = ag_relation->assistant_group_id;
    }

    stats_info->rx_stats_upload++;
    return zpn_broker_assistant_stats_upload(f_conn,
                                             object,
                                             stats_info->customer_gid,
                                             stats_info->tunnel_id,
                                             asst_id,
                                             ast_grp,
                                             1,
                                             0);
}

static void zpn_broker_sitec_stats_proxy_conn_monitor_cb(evutil_socket_t sock, short flags, void *cookie)
{
    int res;
    int64_t customer_id = 0;
    struct zpath_customer *customer = NULL;
    struct sitec_stats_proxy_info *sc_state = cookie;
    if (!sc_state) {
        ZPN_LOG(AL_NOTICE, "sitec config conn monitor cb has no cookie");
        return;
    }

    sc_state->monitor_count++;

    struct fohh_connection *f_conn = sc_state->f_conn;

    /* Drop connection when broker is shutting down. */
    if (disconnect_sitec) {
        const char *reason = disconnect_sitec_reason ? : FOHH_CLOSE_REASON_UPGRADE;
        ZPN_LOG(AL_INFO, "%s: Disconnect sitec stats connection due to %s",
                fohh_description(f_conn), reason);
        fohh_connection_delete(f_conn, reason);
        return;
    }

    /* Drop connection when customer is disabled */
    customer_id = ZPATH_GID_GET_CUSTOMER_GID(fohh_peer_get_id(f_conn));
    if (customer_id != 0) {
        res = zpath_customer_get(customer_id,
                                 &customer,
                                 NULL,
                                 NULL,
                                 0);

        if (res == ZPN_RESULT_NOT_FOUND ||
            (res == ZPN_RESULT_NO_ERROR && customer->disable_traffic == 1)) {
            ZPN_LOG(AL_NOTICE, "%s: Disconnecting sitec(%"PRId64") as customer(%"PRId64") is not found or is disabled", fohh_description(f_conn), fohh_peer_get_id(f_conn), customer_id);
            fohh_connection_delete(f_conn, ZPN_ERR_CUSTOMER_DISABLED);
            return;
        }
    }

    if (redirect_sitec &&
        ((sc_state->monitor_count % ZPN_TUNNEL_MONITOR_REDIRECT_INTERVAL_COUNT) == 0
         || strcmp(redirect_sitec_reason, BRK_REDIRECT_REASON_BROKER_RESTART) == 0)) {
        ZPN_LOG(AL_INFO, "%s: Redirect sitec stats connection due to %s",
                fohh_description(f_conn),
                redirect_sitec_reason ? : FOHH_CLOSE_REASON_UPGRADE);
        zpn_broker_sitec_config_conn_redirect(sc_state->f_conn);
    }
}

int zpn_broker_sitec_astats_conn_callback(struct fohh_connection *connection,
                                          enum fohh_connection_state state,
                                          void *cookie)
{
    struct argo_state *argo;
    struct sitec_stats_proxy_info *stats_info;

    if (state == fohh_connection_connected) {
        int res;
        int64_t sitec_id = 0;
        int64_t customer_gid = 0;
        struct zpath_customer *customer = NULL;

        sitec_id = fohh_peer_get_id(connection);
        if (!sitec_id) return FOHH_RESULT_ERR;

        ZPN_LOG(AL_INFO, "%s: Broker received sitec astats connection, sitec ID = %"PRId64,
                            fohh_description(connection), sitec_id);

        fohh_connection_set_dynamic_cookie(connection, NULL);

        /* Drop connection when customer is disabled */
        customer_gid = ZPATH_GID_GET_CUSTOMER_GID(sitec_id);
        if (customer_gid != 0) {
            res = zpath_customer_get(customer_gid,
                                     &customer,
                                     NULL,
                                     NULL,
                                     0);

            if (res == ZPN_RESULT_NOT_FOUND || (res == ZPN_RESULT_NO_ERROR && customer->disable_traffic == 1)) {
                ZPN_DEBUG_SITEC("%s: Could not accept connection with sitec(%"PRId64"), customer(%"PRId64") is not found or is disabled",
                            fohh_description(connection), sitec_id, customer_gid);
                return FOHH_RESULT_ERR;
            }
        }

        argo = fohh_argo_get_rx(connection);

        /* Register assistant log upload. Connector's stats come as log */
        if ((res = argo_register_structure(argo, global_argo_log_desc, zpn_broker_sitec_astats_log_upload_cb, connection))) {
            ZPN_LOG(AL_ERROR, "Could not register sitec assistant log upload for connection %s", fohh_description(connection));
            return res;
        }

        struct timeval tv;
        stats_info = ZPN_CALLOC(sizeof(*stats_info));
        if (!stats_info) {
            ZPN_LOG(AL_ERROR, "Memory");
            return FOHH_RESULT_NO_MEMORY;
        }

        stats_info->customer_gid = customer_gid;
        /* Generate an ID for this client */
        res = RAND_bytes(&(stats_info->tunnel_id_bin[0]), ZPN_TUNNEL_ID_BYTES);
        if (1 != res) {
            ZPN_BROKER_ASSERT_HARD(0, "Crypto Random Generator not seeded!");
            ZPN_FREE(stats_info);
            return res;
        }
        base64_encode_binary(stats_info->tunnel_id, stats_info->tunnel_id_bin, ZPN_TUNNEL_ID_BYTES);
        stats_info->f_conn = connection;
        stats_info->f_conn_incarnation = fohh_connection_incarnation(connection);
        fohh_connection_set_dynamic_cookie(connection, stats_info);

        stats_info->timer = event_new(fohh_get_thread_event_base(fohh_connection_get_thread_id(connection)),
                                      -1,
                                      EV_PERSIST,
                                      zpn_broker_sitec_stats_proxy_conn_monitor_cb,
                                      stats_info);
        if (!stats_info->timer) {
            ZPN_LOG(AL_CRITICAL, "Memory");
            ZPN_FREE(stats_info);
            return FOHH_RESULT_NO_MEMORY;
        }

        tv.tv_sec = ZPN_TUNNEL_MONITOR_INTERVAL_S;;
        tv.tv_usec = 0;
        if (event_add(stats_info->timer, &tv)) {
            ZPN_LOG(AL_CRITICAL, "Could not add sitec assistant timer");
            event_free(stats_info->timer);
            ZPN_FREE(stats_info);
            return FOHH_RESULT_NO_MEMORY;
        }

        ZPN_LOG(AL_NOTICE, "%s: broker sitec  astats connection UP, sitec ID = %"PRId64", Customer GID = %"PRId64,
                fohh_description(connection), sitec_id, stats_info->customer_gid);

        zpn_fohh_worker_sitec_connect_astats(fohh_connection_get_thread_id(connection));
    } else {
        /* Connection probably went away... */
        const char *reason = fohh_close_reason(connection);
        ZPN_LOG(AL_NOTICE, "%s: broker sitec astats connection DOWN due to %s",
                fohh_description(connection), reason);

        if (connection->state == fohh_connection_connected) {
            zpn_fohh_worker_sitec_disconnect_astats(fohh_connection_get_thread_id(connection));
        }

        stats_info = fohh_connection_get_dynamic_cookie(connection);
        if (stats_info) {
            if (stats_info->timer) {
                event_free(stats_info->timer);
                stats_info->timer = NULL;
            }
            ZPN_FREE(stats_info);
            fohh_connection_set_dynamic_cookie(connection, NULL);
        }
    }

    return FOHH_RESULT_NO_ERROR;
}

int zpn_broker_sitec_astats_unblock_callback(struct fohh_connection *connection,
                                            enum fohh_queue_element_type element_type,
                                            void *cookie)
{
    return FOHH_RESULT_NO_ERROR;
}

int zpn_broker_sitec_pbstats_log_upload_cb(void *argo_cookie_ptr,
                                          void *argo_structure_cookie_ptr,
                                          struct argo_object *object)
{
    struct fohh_connection *f_conn = argo_structure_cookie_ptr;
    struct sitec_stats_proxy_info *stats_info;
    struct argo_log *log = object->base_structure_void;
    int64_t pb_id = log->l_inst_gid;
    int64_t pb_grp_id = 0;
    int res;

    stats_info = fohh_connection_get_dynamic_cookie(f_conn);
    if (!stats_info) {
        ZPN_LOG(AL_ERROR, "Could not send stats to customer kafka pipeline because dynamic cookie is NULL");
        return zpn_broker_pbroker_stats_upload(f_conn, object, 0, NULL, 0, 0, 1);
    }

    // Get pb group gid for the pbroker
    struct zpn_private_broker_to_group *pb_grp = NULL;

    res = zpn_pbroker_to_group_get_by_private_broker_gid(pb_id, &pb_grp, 0, NULL, NULL, 0);
    if (pb_grp) {
        pb_grp_id = pb_grp->private_broker_group_gid;
    } else {
        ZPN_LOG(AL_NOTICE, "Cannot find the pbroker with Private broker ID = %"PRId64" %s", pb_id, zpn_result_string(res));
    }

    stats_info->rx_stats_upload++;
    return zpn_broker_pbroker_stats_upload(f_conn,
                                           object,
                                           stats_info->customer_gid,
                                           stats_info->tunnel_id,
                                           pb_id,
                                           pb_grp_id,
                                           1);
}

int zpn_broker_sitec_pbstats_conn_callback(struct fohh_connection *connection,
                                           enum fohh_connection_state state,
                                           void *cookie)
{
    struct argo_state *argo;
    struct sitec_stats_proxy_info *stats_info;

    if (state == fohh_connection_connected) {
        int res;
        int64_t sitec_id = 0;
        int64_t customer_gid = 0;
        struct zpath_customer *customer = NULL;

        sitec_id = fohh_peer_get_id(connection);
        if (!sitec_id) return FOHH_RESULT_ERR;

        ZPN_LOG(AL_INFO, "%s: Broker received sitec pbstats connection, sitec ID = %"PRId64,
                            fohh_description(connection), sitec_id);

        fohh_connection_set_dynamic_cookie(connection, NULL);

        /* Drop connection when customer is disabled */
        customer_gid = ZPATH_GID_GET_CUSTOMER_GID(sitec_id);
        if (customer_gid != 0) {
            res = zpath_customer_get(customer_gid,
                                     &customer,
                                     NULL,
                                     NULL,
                                     0);

            if (res == ZPN_RESULT_NOT_FOUND || (res == ZPN_RESULT_NO_ERROR && customer->disable_traffic == 1)) {
                ZPN_DEBUG_SITEC("%s: Could not accept connection with sitec(%"PRId64"), customer(%"PRId64") is not found or is disabled",
                            fohh_description(connection), sitec_id, customer_gid);
                return FOHH_RESULT_ERR;
            }
        }

        argo = fohh_argo_get_rx(connection);

        /* Register assistant log upload. Connector's stats come as log */
        if ((res = argo_register_structure(argo, global_argo_log_desc, zpn_broker_sitec_pbstats_log_upload_cb, connection))) {
            ZPN_LOG(AL_ERROR, "Could not register sitec assistant log upload for connection %s", fohh_description(connection));
            return res;
        }

        struct timeval tv;
        stats_info = ZPN_CALLOC(sizeof(*stats_info));
        if (!stats_info) {
            ZPN_LOG(AL_ERROR, "Memory");
            return FOHH_RESULT_NO_MEMORY;
        }

        stats_info->customer_gid = customer_gid;
        /* Generate an ID for this client */
        res = RAND_bytes(&(stats_info->tunnel_id_bin[0]), ZPN_TUNNEL_ID_BYTES);
        if (1 != res) {
            ZPN_BROKER_ASSERT_HARD(0, "Crypto Random Generator not seeded!");
            ZPN_FREE(stats_info);
            return res;
        }
        base64_encode_binary(stats_info->tunnel_id, stats_info->tunnel_id_bin, ZPN_TUNNEL_ID_BYTES);
        stats_info->f_conn = connection;
        stats_info->f_conn_incarnation = fohh_connection_incarnation(connection);
        fohh_connection_set_dynamic_cookie(connection, stats_info);

        stats_info->timer = event_new(fohh_get_thread_event_base(fohh_connection_get_thread_id(connection)),
                                      -1,
                                      EV_PERSIST,
                                      zpn_broker_sitec_stats_proxy_conn_monitor_cb,
                                      stats_info);
        if (!stats_info->timer) {
            ZPN_LOG(AL_CRITICAL, "Memory");
            ZPN_FREE(stats_info);
            return FOHH_RESULT_NO_MEMORY;
        }

        tv.tv_sec = ZPN_TUNNEL_MONITOR_INTERVAL_S;
        tv.tv_usec = 0;
        if (event_add(stats_info->timer, &tv)) {
            ZPN_LOG(AL_CRITICAL, "Could not add sitec assistant timer");
            event_free(stats_info->timer);
            ZPN_FREE(stats_info);
            return FOHH_RESULT_NO_MEMORY;
        }

        ZPN_LOG(AL_NOTICE, "%s: broker sitec pbstats connection UP, sitec ID = %"PRId64", Customer GID = %"PRId64,
                fohh_description(connection), sitec_id, stats_info->customer_gid);

        zpn_fohh_worker_sitec_connect_pbstats(fohh_connection_get_thread_id(connection));
    } else {
        /* Connection probably went away... */
        const char *reason = fohh_close_reason(connection);
        ZPN_LOG(AL_NOTICE, "%s: broker sitec pbstats connection DOWN due to %s",
                fohh_description(connection), reason);

        if (connection->state == fohh_connection_connected) {
            zpn_fohh_worker_sitec_disconnect_pbstats(fohh_connection_get_thread_id(connection));
        }

        stats_info = fohh_connection_get_dynamic_cookie(connection);
        if (stats_info) {
            if (stats_info->timer) {
                event_free(stats_info->timer);
                stats_info->timer = NULL;
            }
            ZPN_FREE(stats_info);
            fohh_connection_set_dynamic_cookie(connection, NULL);
        }
    }

    return FOHH_RESULT_NO_ERROR;
}

int zpn_broker_sitec_pbstats_unblock_callback(struct fohh_connection *connection,
                                              enum fohh_queue_element_type element_type,
                                              void *cookie)
{
    return FOHH_RESULT_NO_ERROR;
}

struct zhash_table *zpn_broker_sitec_create_pblog_collection_map()
{
    struct customer_log_collection_info *customer_trans_log;
    struct customer_log_collection_info *customer_auth_log;
    struct customer_log_collection_info *customer_ast_auth_log;
    struct customer_log_collection_info *event_log;
    struct customer_log_collection_info *customer_dns_log;

    struct zhash_table* log_collection_map = zhash_table_alloc(&zpn_allocator);
    if (!log_collection_map) {
        ZPN_LOG(AL_ERROR, "Out of memory");
        return NULL;
    }

    customer_trans_log = create_customer_log_collection("transaction",
                                                        (int)zpath_customer_log_type_zpn_transaction,
                                                        zpn_trans_log_description,
                                                        zpn_transaction_collection,
                                                        1,
                                                        customer_log_collection_callback,
                                                        zpn_trans_log_siem_callback,
                                                        zpn_trans_log_siem_data_fill_callback,
                                                        1);

    char* transaction_collection_name = argo_log_get_name(zpn_transaction_collection);
    if (zhash_table_store(log_collection_map,
                          transaction_collection_name,
                          strlen(transaction_collection_name),
                          1,
                          customer_trans_log)) {
        ZPN_LOG(AL_ERROR, "Failed to setup transaction log rx");
    }

    customer_auth_log = create_customer_log_collection("client_auth",
                                                       (int)zpath_customer_log_type_zpn_auth,
                                                       zpn_auth_log_description,
                                                       zpn_auth_collection,
                                                       1,
                                                       customer_log_collection_callback,
                                                       zpn_auth_log_siem_callback,
                                                       zpn_auth_log_siem_data_fill_callback,
                                                       1);

    char* auth_collection_name = "zpn_pb_client_auth_log";
    if (zhash_table_store(log_collection_map,
                          auth_collection_name,
                          strlen(auth_collection_name),
                          1,
                          customer_auth_log)) {
        ZPN_LOG(AL_ERROR, "Failed to setup auth log rx");
    }

    customer_ast_auth_log = create_customer_log_collection("asst_auth",
                                                           (int)zpath_customer_log_type_zpn_ast_auth,
                                                           zpn_ast_auth_log_description,
                                                           zpn_auth_collection,
                                                           1,
                                                           customer_log_collection_callback,zpn_ast_auth_log_siem_callback,zpn_ast_auth_log_siem_data_fill_callback,
                                                           1);

    char* ast_auth_collection_name = argo_log_get_name(zpn_ast_auth_collection);
    if (zhash_table_store(log_collection_map,
                          ast_auth_collection_name,
                          strlen(ast_auth_collection_name),
                          1,
                          customer_ast_auth_log)) {
        ZPN_LOG(AL_ERROR, "Failed to setup auth log rx");
    }

    event_log = create_customer_log_collection(NULL,
                                               0,
                                               NULL,
                                               zpn_event_collection,
                                               0,
                                               NULL,
                                               NULL,
                                               NULL,
                                               0);

    char* zpn_event_collection_name = "pb_event_log";
    if (zhash_table_store(log_collection_map,
                           zpn_event_collection_name,
                           strlen(zpn_event_collection_name),
                           1,
                           event_log)) {
        ZPN_LOG(AL_ERROR, "Failed to setup event log rx");
    }

    customer_dns_log = create_customer_log_collection(NULL,
                                                      0,
                                                      NULL,
                                                      zpn_dns_collection,
                                                      0,
                                                      NULL,
                                                      NULL,
                                                      NULL,
                                                      0);

    char* dns_collection_name = argo_log_get_name(zpn_dns_collection);
    if (zhash_table_store(log_collection_map,
                          dns_collection_name,
                          strlen(dns_collection_name),
                          1,
                          customer_dns_log)) {
        ZPN_LOG(AL_ERROR, "Failed to setup dns log rx");
    }

    return log_collection_map;
}

struct zhash_table *zpn_broker_sitec_create_alog_collection_map()
{
    struct zhash_table* log_collection_map = zhash_table_alloc(&zpn_allocator);
    if (!log_collection_map) {
        ZPN_LOG(AL_ERROR, "Out of memory");
        return NULL;
    }

    struct customer_log_collection_info *event_log;
    ZPN_BROKER_ASSERT_SOFT((NULL != zpn_event_collection),
                            "event collection is not yet initialized");
    event_log = create_customer_log_collection(NULL,0, NULL, zpn_event_collection, 0, NULL, NULL, NULL, 0);
    if (!event_log) {
        ZPN_LOG(AL_ERROR, "Out of memory, when setting up log receiver for sitec alog");
        zhash_table_free(log_collection_map);
        return NULL;
    }

    char* zpn_event_collection_name = "asst_event_log";
    if (zhash_table_store(log_collection_map,
                          zpn_event_collection_name,
                          strlen(zpn_event_collection_name),
                          1,
                          event_log)) {
        ZPN_LOG(AL_ERROR, "Failed to setup event log receiver for sitec alog");
    }

    if (zpn_ast_waf_http_exchanges_log_description == NULL) {
        zpn_ast_waf_http_exchanges_log_description = argo_register_global_structure(ZPN_WAF_HTTP_EXCHANGES_LOG_HELPER);
        if (!zpn_ast_waf_http_exchanges_log_description) {
            ZPN_LOG(AL_ERROR, "Failed to set up WAF http log description");
            zhash_table_free(log_collection_map);
            return NULL;
        }
    }

    struct customer_log_collection_info *waf_log;
    waf_log = create_customer_log_collection(NULL,
                                             zpath_customer_log_type_zpn_http_inspection,
                                             zpn_ast_waf_http_exchanges_log_description,
                                             zpn_ast_waf_collection,
                                             1, // transmit to log infra
                                             customer_log_collection_callback,
                                             zpn_inspection_log_siem_callback,
                                             NULL,
                                             1);
    if (!waf_log) {
        ZPN_LOG(AL_ERROR, "Out of memory, when setting up WAF http log receiver for sitec assistant");
        zhash_table_free(log_collection_map);
        return NULL;
    }

    /* add waf log collection to log collection hash-map */
    char* zpn_waf_log_name = argo_log_get_name(zpn_ast_waf_collection);
    if (zhash_table_store(log_collection_map, zpn_waf_log_name, strlen(zpn_waf_log_name),
                            1, waf_log)) {
        ZPN_LOG(AL_ERROR, "broker sitec Failed to setup event WAF http log receiver for WAF sitec assistant");
    }

    /* create app inspection log collection */
    if (zpn_ast_app_inspection_log_description == NULL) {
        zpn_ast_app_inspection_log_description = argo_register_global_structure(ZPN_APP_INSPECTION_LOG_HELPER);
        if (!zpn_ast_app_inspection_log_description) {
            ZPN_LOG(AL_ERROR, "broker sitec Failed to set up app inspection log description");
            zhash_table_free(log_collection_map);
            return NULL;
        }
    }

    struct customer_log_collection_info *app_inspection_log;
    app_inspection_log = create_customer_log_collection(NULL,
                                             zpath_customer_log_type_zpn_app_inspection,
                                             zpn_ast_app_inspection_log_description,
                                             zpn_ast_app_inspection_collection,
                                             1, // transmit to log infra
                                             customer_log_collection_callback,
                                             zpn_app_inspection_log_siem_callback,
                                             NULL,
                                             1);
    if (!app_inspection_log) {
        ZPN_LOG(AL_ERROR, "Out of memory, when setting up app inspection log receiver for sitec assistant");
        zhash_table_free(log_collection_map);
        return NULL;
    }

    /* add app inspection log collection to log collection hash-map */
    char* zpn_app_inspection_log_name = argo_log_get_name(zpn_ast_app_inspection_collection);
    size_t zpn_app_inspection_log_name_len = (zpn_app_inspection_log_name && zpn_app_inspection_log_name[0])?strlen(zpn_app_inspection_log_name):0;
    if (zhash_table_store(log_collection_map, zpn_app_inspection_log_name, zpn_app_inspection_log_name_len, 1, app_inspection_log)) {
        ZPN_LOG(AL_ERROR, "Failed to setup event app inspection log receiver for sitec assistant");
    }

    /* create krb inspection log collection */
    if (zpn_ast_krb_inspection_log_description == NULL) {
        zpn_ast_krb_inspection_log_description = argo_register_global_structure(ZPN_KRB_INSPECTION_LOG_HELPER);
        if (!zpn_ast_krb_inspection_log_description) {
            ZPN_LOG(AL_ERROR, "broker sitec Failed to set up krb inspection log description");
            zhash_table_free(log_collection_map);
            return NULL;
        }
    }

    struct customer_log_collection_info *krb_inspection_log;
    krb_inspection_log = create_customer_log_collection(NULL,
                                             zpath_customer_log_type_zpn_krb_inspection,
                                             zpn_ast_krb_inspection_log_description,
                                             zpn_ast_krb_inspection_collection,
                                             1, // transmit to log infra
                                             customer_log_collection_callback,
                                             zpn_krb_inspection_log_siem_callback,
                                             NULL,
                                             1);
    if (!krb_inspection_log) {
        ZPN_LOG(AL_ERROR, "Out of memory, when setting up krb inspection log receiver for sitec assistant");
        zhash_table_free(log_collection_map);
        return NULL;
    }

    /* add krb inspection log collection to log collection hash-map */
    char* zpn_krb_inspection_log_name = argo_log_get_name(zpn_ast_krb_inspection_collection);
    size_t zpn_krb_inspection_log_name_len = (zpn_krb_inspection_log_name && zpn_krb_inspection_log_name[0])?strlen(zpn_krb_inspection_log_name):0;
    if (zhash_table_store(log_collection_map, zpn_krb_inspection_log_name, zpn_krb_inspection_log_name_len, 1, krb_inspection_log)) {
        ZPN_LOG(AL_ERROR, "Failed to setup event krb inspection log receiver for sitec assistant");
    }

    /* create ldap inspection log collection */
    if (zpn_ast_ldap_inspection_log_description == NULL) {
        zpn_ast_ldap_inspection_log_description = argo_register_global_structure(ZPN_LDAP_INSPECTION_LOG_HELPER);
        if (!zpn_ast_ldap_inspection_log_description) {
            ZPN_LOG(AL_ERROR, "broker sitec Failed to set up ldap inspection log description");
            zhash_table_free(log_collection_map);
            return NULL;
        }
    }

    struct customer_log_collection_info *ldap_inspection_log;
    ldap_inspection_log = create_customer_log_collection(NULL,
                                             zpath_customer_log_type_zpn_ldap_inspection,
                                             zpn_ast_ldap_inspection_log_description,
                                             zpn_ast_ldap_inspection_collection,
                                             1, // transmit to log infra
                                             customer_log_collection_callback,
                                             zpn_ldap_inspection_log_siem_callback,
                                             NULL,
                                             1);
    if (!ldap_inspection_log) {
        ZPN_LOG(AL_ERROR, "Out of memory, when setting up ldap inspection log receiver for sitec assistant");
        zhash_table_free(log_collection_map);
        return NULL;
    }

    /* add ldap inspection log collection to log collection hash-map */
    char* zpn_ldap_inspection_log_name = argo_log_get_name(zpn_ast_ldap_inspection_collection);
    size_t zpn_ldap_inspection_log_name_len = (zpn_ldap_inspection_log_name && zpn_ldap_inspection_log_name[0])?strlen(zpn_ldap_inspection_log_name):0;
    if (zhash_table_store(log_collection_map, zpn_ldap_inspection_log_name, zpn_ldap_inspection_log_name_len, 1, ldap_inspection_log)) {
        ZPN_LOG(AL_ERROR, "Failed to setup event ldap inspection log receiver for sitec assistant");
    }

    /* create smb log collection */
    if (zpn_ast_smb_inspection_log_description == NULL) {
        zpn_ast_smb_inspection_log_description = argo_register_global_structure(ZPN_SMB_INSPECTION_LOG_HELPER);
        if (!zpn_ast_smb_inspection_log_description) {
            ZPN_LOG(AL_ERROR, "broker sitec Failed to set up smb inspection log description");
            zhash_table_free(log_collection_map);
            return NULL;
        }
    }

    struct customer_log_collection_info *smb_inspection_log;
    smb_inspection_log = create_customer_log_collection(NULL,
                                             zpath_customer_log_type_zpn_smb_inspection,
                                             zpn_ast_smb_inspection_log_description,
                                             zpn_ast_smb_inspection_collection,
                                             1, // transmit to log infra
                                             customer_log_collection_callback,
                                             zpn_smb_inspection_log_siem_callback,
                                             NULL,
                                             1);
    if (!smb_inspection_log) {
        ZPN_LOG(AL_ERROR, "Out of memory, when setting up smb log receiver for sitec assistant");
        zhash_table_free(log_collection_map);
        return NULL;
    }

    /* add smb log collection to log collection hash-map */
    char* zpn_smb_inspection_log_name = argo_log_get_name(zpn_ast_smb_inspection_collection);
    size_t zpn_smb_inspection_log_name_len = (zpn_smb_inspection_log_name && zpn_smb_inspection_log_name[0])?strlen(zpn_smb_inspection_log_name):0;
    if (zhash_table_store(log_collection_map, zpn_smb_inspection_log_name, zpn_smb_inspection_log_name_len, 1, smb_inspection_log)) {
        ZPN_LOG(AL_ERROR, "Failed to setup event smb inspection log receiver for sitec assistant");
    }

    /* create http log collection */
    if (zpn_ast_waf_http_exchanges_api_log_description == NULL) {
        zpn_ast_waf_http_exchanges_api_log_description = argo_register_global_structure(ZPN_WAF_HTTP_EXCHANGES_API_LOG_HELPER);
        if (!zpn_ast_waf_http_exchanges_api_log_description) {
            ZPN_LOG(AL_ERROR, "broker sitec Failed to set up http api log description");
            zhash_table_free(log_collection_map);
            return NULL;
        }
    }

    struct customer_log_collection_info *api_log;
    api_log = create_customer_log_collection(NULL,
                                             zpath_customer_log_type_zpn_http_api_inspection,
                                             zpn_ast_waf_http_exchanges_api_log_description,
                                             zpn_ast_waf_api_collection,
                                             1, // transmit to log infra
                                             customer_log_collection_callback,
                                             zpn_inspection_api_log_siem_callback,
                                             NULL,
                                             1);
    if (!api_log) {
        ZPN_LOG(AL_ERROR, "Out of memory, when setting up api log receiver for sitec assistant");
        zhash_table_free(log_collection_map);
        return NULL;
    }

    /* add api log collection to log collection hash-map */
    char* zpn_api_log_name = argo_log_get_name(zpn_ast_waf_api_collection);
    size_t zpn_api_log_name_len = (zpn_api_log_name && zpn_api_log_name[0])?strlen(zpn_api_log_name):0;
    if (zhash_table_store(log_collection_map, zpn_api_log_name, zpn_api_log_name_len, 1, api_log)) {
        ZPN_LOG(AL_ERROR, "Failed to setup event api inspection log receiver for sitec assistant");
    }

    if (zpn_ast_ptag_log_description == NULL) {
        zpn_ast_ptag_log_description = argo_register_global_structure(ZPN_PTAG_LOG_HELPER);
        if (!zpn_ast_ptag_log_description) {
            ZPN_LOG(AL_ERROR, "broker sitec Failed to set up ptag log description");
            zhash_table_free(log_collection_map);
            return NULL;
        }
    }

    struct customer_log_collection_info *ptag_log;
    ptag_log = create_customer_log_collection(NULL,
                                              zpath_customer_log_type_zpn_ptag,
                                              zpn_ast_ptag_log_description,
                                              zpn_ast_ptag_collection,
                                              1, // transmit to log infra
                                              customer_log_collection_callback,
                                              NULL,
                                              NULL,
                                              1);
    if (!ptag_log) {
        ZPN_LOG(AL_ERROR, "Out of memory, when setting up ptag log receiver for sitec assistant");
        zhash_table_free(log_collection_map);
        return NULL;
    }

    /* add ptag log collection to log collection hash-map */
    char* zpn_ptag_log_name = argo_log_get_name(zpn_ast_ptag_collection);
    size_t zpn_ptag_log_name_len = (zpn_ptag_log_name && zpn_ptag_log_name[0])?strlen(zpn_ptag_log_name):0;
    if (zhash_table_store(log_collection_map, zpn_ptag_log_name, zpn_ptag_log_name_len, 1, ptag_log)) {
        ZPN_LOG(AL_ERROR, "Failed to setup event ptag log receiver for sitec assistant");
    }

    return log_collection_map;
}

int zpn_broker_sitec_listen(struct fohh_generic_server *sni_server)
{
    struct fohh_connection *f_conn;
    static int initialized = 0;
    char sni_str[ZPN_MAX_SNI_NAME_LEN + 1];
    int res;

    if (!initialized) {

        sc_gid_to_tunnel.lock = ZPATH_MUTEX_INIT;
        sc_gid_to_tunnel.table = zhash_table_alloc(&zpn_allocator);
        if (!sc_gid_to_tunnel.table) {
            ZPN_LOG(AL_ERROR, "Couldn't allocate sc_gid_to_tunnel table");
            return ZPN_RESULT_NO_MEMORY;
        }

        /* Create wally servers for non-static tables */
        wally_server_global = wally_fohh_server_create(zpath_global_wally,
                                                       argo_serialize_binary,
                                                       fohh_connection_style_argo,
                                                       1,
                                                       NULL,
                                                       0,
                                                       NULL,
                                                       NULL,
                                                       NULL,
                                                       1);
        if (!wally_server_global) {
            ZPN_LOG(AL_ERROR, "Could not initialize wally global server");
            return ZPN_RESULT_ERR;
        }

        initialized = 1;
    }

    if (!sni_server) return ZPN_RESULT_ERR;

    /* create cloud_name after checking alt-cloud name */
    char cloud_name[ZPN_MAX_CLOUD_NAME_LEN + 1];
    zpn_broker_get_cloud_name(cloud_name, sizeof(cloud_name));

    f_conn = fohh_server_create(0, //int quiet,
                                argo_serialize_binary, // enum argo_serialize_mode encoding,
                                fohh_connection_style_argo, // enum fohh_connection_style style,
                                NULL, // void *cookie,
                                zpn_broker_sitec_ctrl_conn_callback,
                                NULL,
                                zpn_broker_sitec_ctrl_conn_unblock_callback,
                                NULL,
                                NULL,
                                0,
                                NULL, // char *root_cert_file_name,
                                NULL, // char *my_cert_file_name,
                                NULL, // char *my_cert_key_file_name,
                                1, // int require pvt broker_cert,
                                1, // int use_ssl);
                                zpn_broker_sitec_ctx_callback,     // ssl ctx callback
                                zpn_broker_sitec_verify_callback, // verify callback
                                zpn_broker_client_post_verify_region_check_cb, // post verify callback
                                1, // Allow binary argo.
                                ZPN_SITEC_BROKER_RX_TIMEOUT_S); // timeout
    if (!f_conn) {
        return ZPN_RESULT_ERR;
    }

    snprintf(sni_str, sizeof(sni_str), "scctl.%s", cloud_name);
    res = fohh_generic_server_register(sni_server,
                                       f_conn,
                                       sni_str,
                                       1,
                                       FOHH_WORKER_ZPN_SCCTL);
    if (res) {
        return res;
    }
    zpn_broker_sitec_add_sni(sni_str, 1);

    fohh_set_info_callback(f_conn, zpn_broker_sitec_conn_info_callback, zpn_broker_verify_alt_cloud_info_callback);

    f_conn = fohh_server_create(0, //int quiet,
                                argo_serialize_binary, // enum argo_serialize_mode encoding,
                                fohh_connection_style_argo, // enum fohh_connection_style style,
                                NULL, // void *cookie,
                                zpn_broker_sitec_config_conn_callback,
                                NULL,
                                zpn_broker_sitec_config_unblock_callback,
                                NULL,
                                NULL,
                                0,
                                NULL, // char *root_cert_file_name,
                                NULL, // char *my_cert_file_name,
                                NULL, // char *my_cert_key_file_name,
                                1, // int require pvt broker cert,
                                1, // int use_ssl);
                                zpn_broker_sitec_ctx_callback, // ssl ctx callback
                                zpn_broker_sitec_verify_callback, // verify callback
                                zpn_broker_client_post_verify_region_check_cb, // post verify callback
                                1, // Allow binary argo.
                                ZPN_SITEC_BROKER_RX_TIMEOUT_S); // timeout
    if (!f_conn) {
        return ZPN_RESULT_ERR;
    }

    snprintf(sni_str, sizeof(sni_str), "sccfg.%s", cloud_name);
    res = fohh_generic_server_register(sni_server,
                                       f_conn,
                                       sni_str,
                                       1,
                                       FOHH_WORKER_ZPN_SCCFG);
    if (res) {
        return res;
    }
    zpn_broker_sitec_add_sni(sni_str, 1);

    fohh_set_info_callback(f_conn, zpn_broker_sitec_cfg_conn_info_callback, zpn_broker_verify_alt_cloud_info_callback);

    if (is_static_wally_enabled) {
        f_conn = fohh_server_create(0, //int quiet,
                                    argo_serialize_binary, // enum argo_serialize_mode encoding,
                                    fohh_connection_style_argo, // enum fohh_connection_style style,
                                    NULL, // void *cookie,
                                    zpn_broker_sitec_static_config_conn_callback,
                                    NULL,
                                    zpn_broker_sitec_static_config_unblock_callback,
                                    NULL,
                                    NULL,
                                    0,
                                    NULL, // char *root_cert_file_name,
                                    NULL, // char *my_cert_file_name,
                                    NULL, // char *my_cert_key_file_name,
                                    1, // int require pvt broker cert,
                                    1, // int use_ssl);
                                    zpn_broker_sitec_ctx_callback, // ssl ctx callback
                                    zpn_broker_sitec_verify_callback, // verify callback
                                    zpn_broker_client_post_verify_region_check_cb,
                                    1, // Allow binary argo.
                                    ZPN_SITEC_BROKER_RX_TIMEOUT_S); // timeout
        if (!f_conn) {
            return ZPN_RESULT_ERR;
        }

        snprintf(sni_str, sizeof(sni_str), "scrcfg.%s", ZPATH_LOCAL_CLOUD_NAME);
        res = fohh_generic_server_register(sni_server,
                                           f_conn,
                                           sni_str,
                                           1,
                                           FOHH_WORKER_ZPN_SCSCFG);
        if (res) {
            return res;
        }
        zpn_broker_sitec_add_sni(sni_str, 1);

        fohh_set_info_callback(f_conn, zpn_broker_sitec_cfg_conn_info_callback, zpn_broker_verify_alt_cloud_info_callback);
    }

    f_conn = fohh_server_create(0, //int quiet,
                                argo_serialize_binary, // enum argo_serialize_mode encoding,
                                fohh_connection_style_argo, // enum fohh_connection_style style,
                                NULL, // void *cookie,
                                zpn_broker_sitec_config_override_conn_callback,
                                NULL,
                                zpn_broker_sitec_config_override_unblock_callback,
                                NULL,
                                NULL,
                                0,
                                NULL, // char *root_cert_file_name,
                                NULL, // char *my_cert_file_name,
                                NULL, // char *my_cert_key_file_name,
                                1, // int require pvt broker cert,
                                1, // int use_ssl);
                                zpn_broker_sitec_ctx_callback, // ssl ctx callback
                                zpn_broker_sitec_verify_callback, // verify callback
                                zpn_broker_client_post_verify_region_check_cb,
                                1, // Allow binary argo.
                                ZPN_SITEC_BROKER_RX_TIMEOUT_S); // timeout
    if (!f_conn) {
        return ZPN_RESULT_ERR;
    }

    snprintf(sni_str, sizeof(sni_str), "scovd.%s", cloud_name);
    res = fohh_generic_server_register(sni_server,
                                       f_conn,
                                       sni_str,
                                       1,
                                       FOHH_WORKER_ZPN_SCOVD);
    if (res) {
        return res;
    }
    zpn_broker_sitec_add_sni(sni_str, 1);

    fohh_set_info_callback(f_conn, zpn_broker_sitec_cfg_conn_info_callback, zpn_broker_verify_alt_cloud_info_callback);

    f_conn = fohh_server_create(0,                           // int quiet,
                                argo_serialize_binary,       // enum argo_serialize_mode encoding,
                                fohh_connection_style_argo,  // enum fohh_connection_style style,
                                NULL,                        // void *cookie,
                                zpn_broker_sitec_userdb_conn_callback,
                                NULL,
                                zpn_broker_sitec_userdb_unblock_callback,
                                NULL,
                                NULL,
                                0,
                                NULL,                                // char *root_cert_file_name,
                                NULL,                                // char *my_cert_file_name,
                                NULL,                                // char *my_cert_key_file_name,
                                1,                                   // int require pvt broker cert,
                                1,                                   // int use_ssl);
                                zpn_broker_sitec_ctx_callback,       // ssl ctx callback
                                zpn_broker_sitec_verify_callback,    // verify callback
                                zpn_broker_client_post_verify_region_check_cb, // post verify callback
                                1,                                   // Allow binary argo.
                                ZPN_SITEC_BROKER_RX_TIMEOUT_S);    // timeout
    if (!f_conn) {
        return ZPN_RESULT_ERR;
    }

    snprintf(sni_str, sizeof(sni_str), "scuserdb.%s", cloud_name);
    res = fohh_generic_server_register(sni_server, f_conn, sni_str, 1, FOHH_WORKER_ZPN_SCUSERDB);
    if (res) {
        return res;
    }
    zpn_broker_sitec_add_sni(sni_str, 1);

    fohh_set_info_callback(f_conn, zpn_broker_sitec_cfg_conn_info_callback, zpn_broker_verify_alt_cloud_info_callback);

    f_conn = fohh_server_create(0, //int quiet,
                                argo_serialize_binary, // enum argo_serialize_mode encoding,
                                fohh_connection_style_argo, // enum fohh_connection_style style,
                                NULL, // void *cookie,
                                zpn_broker_sitec_astats_conn_callback,
                                NULL,
                                zpn_broker_sitec_astats_unblock_callback,
                                NULL,
                                NULL,
                                0,
                                NULL, // char *root_cert_file_name,
                                NULL, // char *my_cert_file_name,
                                NULL, // char *my_cert_key_file_name,
                                1, // int require_assistant_cert,
                                1, // int use_ssl);
                                zpn_broker_sitec_ctx_callback, // ssl ctx callback
                                zpn_broker_sitec_verify_callback, // verify callback
                                zpn_broker_client_post_verify_region_check_cb, // post verify callback
                                1, // Allow binary argo.
                                ZPN_SITEC_BROKER_RX_TIMEOUT_S); // timeout
    if (!f_conn) {
        return ZPN_RESULT_ERR;
    }

    fohh_set_info_callback(f_conn, zpn_broker_sitec_cfg_conn_info_callback, zpn_broker_verify_alt_cloud_info_callback);

    snprintf(sni_str, sizeof(sni_str), "scastats.%s", cloud_name);

    ZPN_DEBUG_STARTUP("Register SNI Server: *.%s", sni_str);
    res = fohh_generic_server_register(sni_server,
                                        f_conn,
                                        sni_str,
                                        1,
                                        FOHH_WORKER_ZPN_SCSTATS);
    if (res) {
        return res;
    }
    zpn_broker_sitec_add_sni(sni_str, 1);

    f_conn = fohh_server_create(0, //int quiet,
                                argo_serialize_binary, // enum argo_serialize_mode encoding,
                                fohh_connection_style_argo, // enum fohh_connection_style style,
                                NULL, // void *cookie,
                                zpn_broker_sitec_pbstats_conn_callback,
                                NULL,
                                zpn_broker_sitec_pbstats_unblock_callback,
                                NULL,
                                NULL,
                                0,
                                NULL, // char *root_cert_file_name,
                                NULL, // char *my_cert_file_name,
                                NULL, // char *my_cert_key_file_name,
                                1, // int require_assistant_cert,
                                1, // int use_ssl);
                                zpn_broker_sitec_ctx_callback, // ssl ctx callback
                                zpn_broker_sitec_verify_callback, // verify callback
                                zpn_broker_client_post_verify_region_check_cb, // post verify callback
                                1, // Allow binary argo.
                                ZPN_SITEC_BROKER_RX_TIMEOUT_S); // timeout
    if (!f_conn) {
        return ZPN_RESULT_ERR;
    }

    fohh_set_info_callback(f_conn, zpn_broker_sitec_cfg_conn_info_callback, zpn_broker_verify_alt_cloud_info_callback);

    snprintf(sni_str, sizeof(sni_str), "scpbstats.%s", cloud_name);

    ZPN_DEBUG_STARTUP("Register SNI Server: *.%s", sni_str);
    res = fohh_generic_server_register(sni_server,
                                        f_conn,
                                        sni_str,
                                        1,
                                        FOHH_WORKER_ZPN_SCSTATS);
    if (res) {
        return res;
    }
    zpn_broker_sitec_add_sni(sni_str, 1);

    snprintf(sni_str, sizeof(sni_str), "scpblog.%s", cloud_name);

    res = fohh_log_receive(sni_server,                                      //fohh_generic_server
                           sni_str,                                         //domain
                           1,                                               //wildcard_prefix
                           zpn_broker_sitec_ctx_callback,                   //ssl ctx oallback
                           zpn_broker_sitec_verify_callback,                //verify callback
                           fohh_log_conn_info_callback,                     //info callback
                           zpn_broker_verify_alt_cloud_info_callback,       // verify info callback
                           zpn_broker_client_post_verify_region_check_cb,   // post verify callback
                           zpn_broker_sitec_app_info_callback,              // app info callback
                           zpn_broker_sitec_log_conn_redirect,              //log conn redirect callback
                           zpn_broker_sitec_pblog_stats_cb,                 //log stats callback
                           zpn_broker_sitec_log_conn_monitor_cb,            //log_tunnel_timer_callback
                           NULL,                                            //log_timer_callback
                           zpn_broker_sitec_create_pblog_collection_map(),  //log collection map
                           NULL,
                           1);
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not set up sitec pblog receiver: %s", zpn_result_string(res));
        return res;
    }

    zpn_broker_sitec_add_sni(sni_str, 1);
    ZPN_LOG(AL_NOTICE, "Done setting up pblog receiver");

    snprintf(sni_str, sizeof(sni_str), "scalog.%s", cloud_name);

    res = fohh_log_receive(sni_server,                                      //fohh_generic_server
                           sni_str,                                         //domain
                           1,                                               //wildcard_prefix
                           zpn_broker_sitec_ctx_callback,                   //ssl ctx oallback
                           zpn_broker_sitec_verify_callback,                //verify callback
                           fohh_log_conn_info_callback,                     //info callback
                           zpn_broker_verify_alt_cloud_info_callback,       // verify info callback
                           zpn_broker_client_post_verify_region_check_cb, // post verify callback
                           zpn_broker_sitec_app_info_callback,              // app info callback
                           zpn_broker_sitec_log_conn_redirect,              //log conn redirect callback
                           zpn_broker_sitec_alog_stats_cb,                  //log stats callback
                           zpn_broker_sitec_log_conn_monitor_cb,            //log_tunnel_timer_callback
                           NULL,                                            //log_timer_callback
                           zpn_broker_sitec_create_alog_collection_map(),   //log collection map
                           NULL,
                           1);
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not set up sitec alog receiver: %s", zpn_result_string(res));
        return res;
    }

    zpn_broker_sitec_add_sni(sni_str, 1);
    ZPN_LOG(AL_NOTICE, "Done setting up sitec alog receiver");

    snprintf(sni_str, sizeof(sni_str), "sclog.%s", cloud_name);

    res = fohh_log_receive(sni_server,                                      //fohh_generic_server
                           sni_str,                                         //domain
                           1,                                               //wildcard_prefix
                           zpn_broker_sitec_ctx_callback,                   //ssl ctx oallback
                           zpn_broker_sitec_verify_callback,                //verify callback
                           fohh_log_conn_info_callback,                     //info callback
                           zpn_broker_verify_alt_cloud_info_callback,       // verify info callback
                           zpn_broker_client_post_verify_region_check_cb,   // post verify callback
                           zpn_broker_sitec_app_info_callback,              // app info callback
                           zpn_broker_sitec_log_conn_redirect,              //log conn redirect callback
                           zpn_broker_sitec_log_stats_cb,                   //log stats callback
                           zpn_broker_sitec_log_conn_monitor_cb,            //log_tunnel_timer_callback
                           NULL,                                            //log_timer_callback
                           zpn_broker_sitec_create_log_collection_map(),    //log collection map
                           NULL,
                           0);
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not set up sitec log receiver: %s", zpn_result_string(res));
        return res;
    }

    zpn_broker_sitec_add_sni(sni_str, 1);
    ZPN_LOG(AL_NOTICE, "Done setting up sitec log receiver");

    f_conn = fohh_server_create(0,                                     //int quiet,
                                argo_serialize_binary,                 // enum argo_serialize_mode encoding,
                                fohh_connection_style_argo,            // enum fohh_connection_style style,
                                NULL,
                                zpn_broker_sitec_stats_conn_callback,
                                NULL,
                                zpn_broker_sitec_stats_unblock_callback,
                                NULL,
                                NULL,
                                0,
                                NULL,                                   // char *root_cert_file_name,
                                NULL,                                   // char *my_cert_file_name,
                                NULL,                                   // char *my_cert_key_file_name,
                                1,                                      // int require_pbroker_cert,
                                1,                                      // int use_ssl);
                                zpn_broker_sitec_ctx_callback,          // ssl ctx callback
                                zpn_broker_sitec_verify_callback,       // verify callback
                                zpn_broker_client_post_verify_region_check_cb, // post verify callback
                                1,                                      // Allow binary argo.
                                ZPN_SITEC_BROKER_RX_TIMEOUT_S);         // timeout
    if (!f_conn) {
        return ZPN_RESULT_ERR;
    }

    snprintf(sni_str, sizeof(sni_str), "scstats.%s", cloud_name);
    res = fohh_generic_server_register(sni_server, f_conn, sni_str, 1, FOHH_WORKER_ZPN_SCSTATS);
    if (res) {
        return res;
    }

    zpn_broker_sitec_add_sni(sni_str, 1);
    fohh_set_info_callback(f_conn, zpn_broker_sitec_stats_conn_info_callback, zpn_broker_verify_alt_cloud_info_callback);


    /* scmc start */
     f_conn = fohh_server_create(0, //int quiet,
                                argo_serialize_binary, // enum argo_serialize_mode encoding,
                                fohh_connection_style_argo, // enum fohh_connection_style style,
                                NULL, // void *cookie,
                                zpn_broker_sitec_mission_critical_conn_callback,
                                NULL,
                                zpn_broker_sitec_ctrl_conn_unblock_callback,
                                NULL,
                                NULL,
                                0,
                                NULL, // char *root_cert_file_name,
                                NULL, // char *my_cert_file_name,
                                NULL, // char *my_cert_key_file_name,
                                1, // int require pvt broker_cert,
                                1, // int use_ssl);
                                zpn_broker_sitec_ctx_callback,     // ssl ctx callback
                                zpn_broker_sitec_verify_callback, // verify callback
                                zpn_broker_client_post_verify_region_check_cb, // post verify callback
                                1, // Allow binary argo.
                                ZPN_SITEC_BROKER_RX_TIMEOUT_S); // timeout
    if (!f_conn) {
        return ZPN_RESULT_ERR;
    }

    snprintf(sni_str, sizeof(sni_str), "scmc.%s", cloud_name);
    res = fohh_generic_server_register(sni_server,
                                       f_conn,
                                       sni_str,
                                       1,
                                       FOHH_WORKER_ZPN_MC);
    if (res) {
        return res;
    }
    zpn_broker_sitec_add_sni(sni_str, 1);

    fohh_set_info_callback(f_conn, zpn_broker_sitec_conn_info_callback, zpn_broker_verify_alt_cloud_info_callback);

    /*  scmc ends */

    return ZPN_RESULT_NO_ERROR;
}

static void zpn_broker_sitec_stats_destroy(struct sitec_stats_info *sitec)
{
    struct fohh_connection *f_conn = sitec->f_conn;

    if (f_conn) {
        ZPN_LOG(AL_NOTICE, "%s: Site controller stats connection DOWN, sitec ID = %"PRId64,
                fohh_description(f_conn),
                sitec->sitec_gid_from_config);
    } else {
        ZPN_LOG(AL_NOTICE, "Stale site controller stats connection DOWN, sitec ID = %"PRId64,
                sitec->sitec_gid_from_config);
    }

    if (sitec->timer) {
        event_del(sitec->timer);
        event_free(sitec->timer);
    }

    /* Remove pbroker from connected proker_list */
    ZPATH_MUTEX_LOCK(&(sitecs_stats.lock), __FILE__, __LINE__);
    if (sitec->in_list) {
        LIST_REMOVE(sitec, list_entry);
        sitec->in_list = 0;
    }
    ZPATH_MUTEX_UNLOCK(&(sitecs_stats.lock), __FILE__, __LINE__);

    ZPN_FREE(sitec);
}

/* This is called every ZPN_TUNNEL_MONITOR_INTERVAL_S seconds */
static void zpn_broker_sitec_stats_conn_monitor_cb(evutil_socket_t sock, short flags, void *cookie)
{
    int res;
    struct zpath_customer *customer = NULL;
    struct fohh_connection *f_conn = cookie;
    struct sitec_stats_info *sitec = fohh_connection_get_dynamic_cookie(f_conn);
    if (!sitec) {
        ZPN_LOG(AL_NOTICE, "%s: Site controller stats conn monitor cb has no cookie", fohh_description(f_conn));
        return;
    }

    sitec->monitor_count++;

    /* Drop connection when broker is shutting down. */
    if (zpn_broker_sitec_get_disconnect_sitec_flag()) {
        const char *disconnect_reason = zpn_broker_sitec_get_disconnect_sitec_reason();
        ZPN_LOG(AL_INFO, "%s: Disconnect sitec stats connection due to %s",
                fohh_description(f_conn), disconnect_reason ? : FOHH_CLOSE_REASON_UPGRADE);
        fohh_connection_delete(f_conn, disconnect_reason);
        return;
    }

    /* Drop connection when customer is disabled */
    if (sitec->customer_gid != 0) {
        res = zpath_customer_get(sitec->customer_gid,
                                 &customer,
                                 NULL,
                                 NULL,
                                 0);

        if (res == ZPN_RESULT_NOT_FOUND || (res == ZPN_RESULT_NO_ERROR && customer->disable_traffic == 1)) {
            ZPN_LOG(AL_NOTICE, "%s: Disconnecting sitec(%"PRId64") as customer(%"PRId64") is not found or is disabled",
                            fohh_description(f_conn), fohh_peer_get_id(f_conn), sitec->customer_gid);
            /* Kill the connection */
            fohh_connection_delete(f_conn, ZPN_ERR_CUSTOMER_DISABLED);
            return;
        }
    }

    if (zpn_broker_sitec_get_redirect_sitec_flag() &&
        ((sitec->monitor_count % ZPN_TUNNEL_MONITOR_REDIRECT_INTERVAL_COUNT) == 0
         || strcmp(redirect_sitec_reason, BRK_REDIRECT_REASON_BROKER_RESTART) == 0)) {
        ZPN_LOG(AL_INFO, "%s: Redirect sitec stats connection due to %s",
                fohh_description(f_conn), redirect_sitec_reason ? : FOHH_CLOSE_REASON_UPGRADE);
        zpn_broker_sitec_stats_conn_redirect(sitec->f_conn);
    }
}

static int zpn_broker_sitec_stats_upload_to_zi_endpoint(struct argo_object *object, int64_t customer_gid)
{
    struct argo_object *obj_copy;

    if (zpn_debug_get(ZPN_DEBUG_SITE_CONTROLLER_IDX)) {
        char dump[8000];
        if (argo_object_dump(object, dump, sizeof(dump), NULL, 0) == ARGO_RESULT_NO_ERROR) {
            ZPN_LOG(AL_DEBUG, "Rx: %s", dump);
        }
    }

    if (zpn_broker_is_sitec_zistats_upload_disabled_for_customer(customer_gid)) {
        ZPN_DEBUG_PRIVATE_BROKER("Received stats from site controller but zistats upload is disabled for customer %ld", (long)customer_gid);
        return ZPN_RESULT_NO_ERROR;
    }

    obj_copy = argo_object_copy(object);
    argo_log_log_object_immediate(zpath_stats_collection, obj_copy);
    argo_object_release(obj_copy);

    return ZPN_RESULT_NO_ERROR;
}

static int zpn_broker_sitec_stats_upload_to_ci_endpoint(struct argo_object    *object,
                                                        int64_t               customer_gid,
                                                        char                  *tunnel_id,
                                                        int64_t               sitec_gid,
                                                        int64_t               sitec_grp_gid)
{
    int res;
    if (!customer_gid) {
        ZPN_LOG(AL_ERROR, "Could not send stats to customer kafka pipeline because customer_id was not configured");
        return ZPN_RESULT_ERR;
    }

    struct argo_log *log = object->base_structure_void;
    if (!log->l_obj) {
        ZPN_LOG(AL_ERROR, "Malformed log from instance:%s - missing internal l_obj", log->l_inst);
        return ZPN_RESULT_ERR;
    }

    res = zpn_broker_sitec_stats_upload_to_zi_endpoint(object, customer_gid);
    if (res) {
        ZPN_LOG(AL_ERROR, "Error occurred while uploading stats to zi endpoint for customer id: %ld, error: %s", (long) customer_gid, zpath_result_string(res));
    }

    enum zpath_customer_log_type log_type;
    if (0 == strncmp("sitec_stats_comprehensive", log->l_name, sizeof("sitec_stats_comprehensive"))) {
        log_type = zpath_customer_log_type_sc_comprehensive_stats;
        /* Stream comprehensive stats log to LSS */
        zpn_broker_siem_sitec_comprehensive_stats(log->l_obj->base_structure_void,
                                                  customer_gid,
                                                  tunnel_id,
                                                  sitec_gid,
                                                  sitec_grp_gid);
    } else {
        log_type = zpath_customer_log_type_ci_stats;
    }

    /* We're sending comprehensive stats to a separate kafka topic */
    return zpath_service_endpoint_log_struct_config(customer_gid,
                                                    log->l_name,
                                                    log_type,
                                                    argo_get_object_description(log->l_obj),
                                                    log->l_inst,
                                                    sitec_gid,
                                                    log->l_role,
                                                    NULL,
                                                    NULL,
                                                    NULL,
                                                    NULL,
                                                    log->l_obj->base_structure_void); // internal calls creates the log object and captures this data and manages it
}

int zpn_broker_sitec_stats_send_event_log_to_kafka(struct argo_object *object,
                                                   int64_t customer_gid,
                                                   int64_t sitec_gid)
{
    struct argo_log *log = object->base_structure_void;
    struct zpn_event_stats *zpn_event_stats_data;
    int res;

    if (customer_gid == 0) {
        ZPN_LOG(AL_ERROR, "Could not send zpn_event log to  kafka pipeline because customer gid found NULL");
        return ZPN_RESULT_ERR;
    }

    if (!log->l_obj) {
        ZPN_LOG(AL_ERROR, "Malformed log from instance:%s - missing internal l_obj", log->l_inst);
        return ZPN_RESULT_ERR;
    }

    res = zpath_service_endpoint_log_struct_config(customer_gid,
                                                   log->l_name,
                                                   zpath_customer_log_type_zpn_event,
                                                   argo_get_object_description(log->l_obj),
                                                   log->l_inst,
                                                   sitec_gid,
                                                   log->l_role,
                                                   log->l_prio,
                                                   log->l_disp,
                                                   log->l_sys,
                                                   log->l_ctg,
                                                   log->l_obj->base_structure_void);
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not forward zpn_event log to producer sent by %s: %s", log->l_inst, zpn_result_string(res));
        return res;
    }

    zpn_event_stats_data = zpn_event_get_stats_data_obj();
    __sync_add_and_fetch_8(&(zpn_event_stats_data->num_event_forwarded), 1);
    __sync_add_and_fetch_8(&(zpn_event_stats_data->num_event_total), 1);

    return ZPN_RESULT_NO_ERROR;
}


int zpn_broker_sitec_stats_upload(struct fohh_connection *f_conn,
                                  struct argo_object     *object,
                                  int64_t                customer_gid,
                                  char                   *tunnel_id,
                                  int64_t                sitec_gid,
                                  int64_t                sitec_grp_gid)
{
    struct argo_log *log = object->base_structure_void;
    int redirect_to_ci_endpoint;

    redirect_to_ci_endpoint = 0;
    if (0 == strncmp("sitec_stats_", log->l_name, strlen("sitec_stats_"))) {
        if (!customer_gid) {
            ZPN_LOG(AL_ERROR, "Could not send stats to customer kafka pipeline because customer gid found NULL, tunnel ID: %s", tunnel_id);
            return zpn_broker_sitec_stats_upload_to_zi_endpoint(object, customer_gid);
        }

        if ((0 == strncmp("sitec_stats_comprehensive", log->l_name, sizeof("sitec_stats_comprehensive"))) ||
           (0 == strncmp("sitec_stats_system_memory", log->l_name, sizeof("sitec_stats_system_memory"))) ||
           (0 == strncmp("sitec_stats_siem_log", log->l_name, sizeof("sitec_stats_siem_log")))) {
            redirect_to_ci_endpoint = 1;
        } else {
            redirect_to_ci_endpoint = 0;
        }

        if (redirect_to_ci_endpoint) {
            return zpn_broker_sitec_stats_upload_to_ci_endpoint(object,
                                                                customer_gid,
                                                                tunnel_id,
                                                                sitec_gid,
                                                                sitec_grp_gid);
        } else {
            return zpn_broker_sitec_stats_upload_to_zi_endpoint(object, customer_gid);
        }
    }

    /*
     * PSE sends zpn_event log over stats connection. Identify it and forward it to producer
     * on a separate kafka topic zpn_event.
     */
    if (0 == strncmp("zpn_event_log", log->l_otyp, sizeof("zpn_event_log"))) {
        return zpn_broker_sitec_stats_send_event_log_to_kafka(object,
                                                              customer_gid,
                                                              sitec_gid);
    }

    /* Receive zpn_event stats and send it to zi endpoint. */
    if (0 == strncmp("zpn_event_stats", log->l_name, sizeof("zpn_event_stats"))) {
        return zpn_broker_sitec_stats_upload_to_zi_endpoint(object, customer_gid);
    }

    return ZPN_RESULT_ERR;
}

int zpn_broker_sitec_stats_log_upload_cb(void *argo_cookie_ptr,
                                         void *argo_structure_cookie_ptr,
                                         struct argo_object *object)
{
    struct fohh_connection *f_conn = argo_structure_cookie_ptr;
    struct sitec_stats_info *sitec;

    sitec = fohh_connection_get_dynamic_cookie(f_conn);
    if (!sitec) {
        ZPN_LOG(AL_ERROR, "Could not send stats to customer kafka pipeline because dynamic cookie is NULL for connection %s", fohh_description(f_conn));
        return zpn_broker_sitec_stats_upload(f_conn, object, 0, NULL, 0, 0);
    }
    sitec->rx_stats_upload++;
    return zpn_broker_sitec_stats_upload(f_conn,
                                         object,
                                         sitec->customer_gid,
                                         sitec->tunnel_id,
                                         sitec->sitec_gid_from_config,
                                         sitec->g_sitec_grp);
}

static int zpn_broker_sitec_stats_conn_redirect(struct fohh_connection *f_conn)
{
    if (!zpn_broker_balance_is_conn_redirect_needed(f_conn, redirect_sitec, NULL)) {
        return ZPN_RESULT_NO_ERROR;
    }

    struct sitec_stats_info *sitec = fohh_connection_get_dynamic_cookie(f_conn);
    ZPN_DEBUG_BALANCE("%s: Redirecting for peer with tunnel %s", fohh_description(f_conn), sitec->tunnel_id);

    struct argo_inet peer_ip;
    struct site peer_site;
    int is_redirect_to_alt_cloud = 0;

    const int res = zpn_broker_sitec_peer_geoip_lookup(f_conn, &peer_ip, &peer_site);
    if (res) {
        return res;
    }

    const int64_t peer_gid = fohh_peer_get_id(f_conn);
    zpn_broker_balance_conn_redirect(f_conn,
                                     ZPATH_GID_GET_CUSTOMER_GID(peer_gid),
                                     ZPATH_GID_GET_CUSTOMER_GID(peer_gid),
                                     &peer_ip, &peer_site,
                                     sitec->tunnel_id,
                                     redirect_sitec,
                                     redirect_sitec_reason,
                                     0,
                                     &is_redirect_to_alt_cloud,
                                     NULL,
                                     NULL,
                                     zpn_client_type_site_controller,
                                     0);

    if (is_redirect_to_alt_cloud) {
        __sync_fetch_and_add_8(&(zpn_fohh_workers[f_conn->fohh_thread_id].sitec_stats.num_sc_stats_alt_cloud_redirects), 1);
    }

    return ZPN_RESULT_NO_ERROR;
}

static void zpn_broker_sitec_stats_conn_info_callback(struct fohh_connection *f_conn, void *cookie)
{
    zpn_broker_sitec_stats_conn_redirect(f_conn);
}

static int zpn_broker_sitec_stats_conn_callback(struct fohh_connection *connection,
                                                enum fohh_connection_state state,
                                                void *cookie)
{
    struct argo_state *argo;
    struct sitec_stats_info *sitec;
    int64_t sitec_id;
    int res;
    int64_t customer_gid;
    struct zpath_customer *customer = NULL;

    if (state == fohh_connection_connected) {
        ZPN_LOG(AL_NOTICE, "%s: Site controller stats connection is UP", fohh_description(connection));

        zpn_fohh_worker_sitec_connect_stats(fohh_connection_get_thread_id(connection));

        sitec_id = fohh_peer_get_id(connection);
        if (!sitec_id) {
            ZPN_LOG(AL_ERROR, "Site controller ID not found for connection %s", fohh_description(connection));
            return FOHH_RESULT_ERR;
        }

        ZPN_DEBUG_SITEC("%s: Broker received pb stats connection, Site controller ID = %"PRId64,
                            fohh_description(connection), sitec_id);

        fohh_connection_set_dynamic_cookie(connection, NULL);
        argo = fohh_argo_get_rx(connection);

        /* Drop connection when customer is disabled */
        customer_gid = ZPATH_GID_GET_CUSTOMER_GID(sitec_id);
        if (customer_gid != 0) {
            res = zpath_customer_get(customer_gid,
                                     &customer,
                                     NULL,
                                     NULL,
                                     0);

            if (res == ZPN_RESULT_NOT_FOUND || (res == ZPN_RESULT_NO_ERROR && customer->disable_traffic == 1)) {
                ZPN_DEBUG_SITEC("%s: Could not accept connection with sitec(%"PRId64"), customer(%"PRId64") is not found or is disabled",
                            fohh_description(connection), sitec_id, customer_gid);
                return FOHH_RESULT_ERR;
            }
        }

        /* Register site controller log upload. Site controller's stats come as log */
        if ((res = argo_register_structure(argo, global_argo_log_desc, zpn_broker_sitec_stats_log_upload_cb, connection))) {
            ZPN_LOG(AL_ERROR, "Could not register site controller log upload for connection %s", fohh_description(connection));
            return res;
        }

        sitec = ZPN_CALLOC(sizeof(*sitec));

        /* Generate an ID for this client */
        res = RAND_bytes(&(sitec->tunnel_id_bin[0]), ZPN_TUNNEL_ID_BYTES);
        if (1 != res) {
            ZPN_BROKER_ASSERT_HARD(0, "Crypto Random Generator not seeded!");
            ZPN_FREE(sitec);
            return res;
        }
        base64_encode_binary(sitec->tunnel_id, sitec->tunnel_id_bin, ZPN_TUNNEL_ID_BYTES);
        sitec->in_list = 0;

        sitec->sitec_gid_from_config = sitec_id;
        sitec->f_conn = connection;
        sitec->f_conn_incarnation = fohh_connection_incarnation(connection);

        sitec->timer = event_new(zevent_event_base(zevent_self()),
                                    -1,
                                    EV_PERSIST,
                                    zpn_broker_sitec_stats_conn_monitor_cb,
                                    connection);

        if (!sitec->timer) {
            ZPN_LOG(AL_CRITICAL, "Memory");
            ZPN_FREE(sitec);
            return FOHH_RESULT_NO_MEMORY;
        }

        struct timeval tv;
        tv.tv_sec = ZPN_TUNNEL_MONITOR_INTERVAL_S;
        tv.tv_usec = 0;
        if (event_add(sitec->timer, &tv)) {
            ZPN_LOG(AL_CRITICAL, "Could not add sitec timer");
            event_free(sitec->timer);
            ZPN_FREE(sitec);
            return FOHH_RESULT_NO_MEMORY;
        }

        fohh_connection_set_dynamic_cookie(connection, sitec);

        {
            // Get customer_gid & sitec group gid for the sitec
            struct zpn_site_controller_to_group *sitec_grp = NULL;
            size_t row_count = 1;
            res = zpn_sitec_to_group_get_by_sitec_gid(sitec_id,
                                                  &sitec_grp,
                                                  &row_count,
                                                  1,
                                                  NULL,
                                                  NULL,
                                                  0);
            if (sitec_grp) {
                // We will always succeed here since this has been called during verification before connection is up
                sitec->customer_gid = sitec_grp->customer_gid;
                sitec->g_sitec_grp = sitec_grp->site_controller_group_gid;
            } else {
                ZPN_LOG(AL_NOTICE, "Cannot find the site controller with ID = %"PRId64, sitec_id);
            }
        }

        /* Add pbroker to connected pbroker_list */
        ZPATH_MUTEX_LOCK(&(sitecs_stats.lock), __FILE__, __LINE__);
        LIST_INSERT_HEAD(&(sitecs_stats.sitec_list), sitec, list_entry);
        sitec->in_list = 1;
        ZPATH_MUTEX_UNLOCK(&(sitecs_stats.lock), __FILE__, __LINE__);

        ZPN_LOG(AL_NOTICE, "%s: Site controller stats connection UP, sitec ID = %"PRId64,
                fohh_description(connection), sitec_id);
    } else {
        if (connection->state == fohh_connection_connected) {
        	/* Connection probably went away... */
        	const char *reason = fohh_close_reason(connection);
        	ZPN_LOG(AL_NOTICE, "%s: site controller stats connection DOWN: %s", fohh_description(connection), reason);

        	sitec = fohh_connection_get_dynamic_cookie(connection);
        	if (sitec) {
            	zpn_broker_sitec_stats_destroy(sitec);
            	fohh_connection_set_dynamic_cookie(connection, NULL);
            	zpn_fohh_worker_sitec_disconnect_stats(fohh_connection_get_thread_id(connection));
            }
        }
    }
    return FOHH_RESULT_NO_ERROR;
}

static int zpn_broker_sitec_stats_unblock_callback(struct fohh_connection *connection,
                                                   enum fohh_queue_element_type element_type,
                                                   void *cookie)
{
    return FOHH_RESULT_NO_ERROR;
}

int zpn_broker_sitec_stats_info(struct zpath_debug_state *request_state,
                                const char **query_values,
                                int query_value_count,
                                void *cookie)
{
    struct sitec_stats_info* sitec;
    struct sitec_stats_info* tmp;
    uint64_t total_stats_connections = 0;
    uint64_t total_rx_stats_upload = 0;

    ZPATH_MUTEX_LOCK(&(sitecs_stats.lock), __FILE__, __LINE__);

    LIST_FOREACH_SAFE(sitec, &(sitecs_stats.sitec_list), list_entry, tmp) {
        total_stats_connections++;
        total_rx_stats_upload += sitec->rx_stats_upload;
    }

    ZPATH_MUTEX_UNLOCK(&(sitecs_stats.lock), __FILE__, __LINE__);

    ZDP("total number of stats connections: %"PRId64"\n", total_stats_connections);
    ZDP("total received stats upload: %"PRId64"\n", total_rx_stats_upload);

    return ZPATH_RESULT_NO_ERROR;
}

int zpn_broker_sitec_stats_dump_all(struct zpath_debug_state *request_state,
                                    const char **query_values,
                                    int query_value_count,
                                    void *cookie)
{
    struct sitec_stats_info* sitec;
    struct sitec_stats_info* tmp;

    ZDP(" -- stats connections view -- \n");
    ZPATH_MUTEX_LOCK(&(sitecs_stats.lock), __FILE__, __LINE__);

    LIST_FOREACH_SAFE(sitec, &(sitecs_stats.sitec_list), list_entry, tmp) {
        ZDP("sitec_gid_from_config                  : %"PRId64"\n", sitec->sitec_gid_from_config);
        ZDP("g_sitec_grp                            : %"PRId64"\n", sitec->g_sitec_grp);
        ZDP("sitec_customer_gid                     : %"PRIu64"\n", (uint64_t)ZPATH_GID_GET_CUSTOMER_GID(sitec->sitec_gid_from_config));
        ZDP("f_conn                                 : %s\n", fohh_description(sitec->f_conn));
        ZDP("rx_log_stats_upload                    : %"PRId64"\n", sitec->rx_stats_upload);

        ZDP("\n");
    }

    ZPATH_MUTEX_UNLOCK(&(sitecs_stats.lock), __FILE__, __LINE__);

    return ZPATH_RESULT_NO_ERROR;
}

int zpn_broker_sitec_stats_init()
{
    int res;
    res = zpath_debug_add_read_command("Dump all the log(stats_log) connections to site controllers",
                                  "/broker/sitec/stats/info",
                                  zpn_broker_sitec_stats_info,
                                  NULL,
                                  NULL);
    if (res) {
        ZPN_LOG(AL_ERROR, "Error occurred while adding a debug command /broker/sitec/stats/info, error: %s", zpath_result_string(res));
        return res;
    }

    res = zpath_debug_add_read_command("Dump all the log(stats_log) connections to site controllers",
                                  "/broker/sitec/stats/dump",
                                  zpn_broker_sitec_stats_dump_all,
                                  NULL,
                                  NULL);
    if (res) {
        ZPN_LOG(AL_ERROR, "Error occurred while adding a debug command /broker/sitec/stats/dump, error: %s", zpath_result_string(res));
        return res;
    }

    sitecs_stats.lock = ZPATH_MUTEX_INIT;

    return ZPN_RESULT_NO_ERROR;
}
