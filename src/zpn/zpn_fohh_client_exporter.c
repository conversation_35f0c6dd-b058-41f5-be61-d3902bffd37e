/*
 * zpn_fohh_client_exporter.c. Copyright (C) 2017 Zscaler, Inc. All Rights Reserved.
 */

#include <sys/queue.h>
#include "argo/argo_hash.h"

#include "zpath_lib/zpath_lib.h"
#include "zpath_lib/zpath_local.h"
#include "zpath_lib/zpath_debug.h"
#include "zpath_lib/zpath_instance.h"
#include "zpath_lib/zpath_config_override.h"
#include "zpath_lib/zpath_config_override_keys.h"

#include "zpn/zpn_lib.h"
#include "zpn/zpn_mconn.h"
#include "zpn/zpn_fohh_client_exporter.h"
#include "zpn/zpn_fohh_client_exporter_compiled.h"
#include "zpn/zpn_exporter_mtunnel.h"
#include "zpn/zpn_exporter_client.h"

static int initialized = 0;
struct zpn_fohh_client_exporter;
struct zpn_mconn_global_owner_calls zfce_mt_global_call_set_broker;
struct zpn_mconn_global_owner_calls zfce_mt_global_call_set_parser;
static struct event *mt_timer = NULL;
static struct event *mt_connector_free_timer = NULL;

/* *********************************************************************************************
 * Global Resources protected by local_lock
 */
static zpath_mutex_t local_lock;
static struct argo_hash_table *exporter_by_id = NULL;

struct zpn_fohh_client_exporter_mt_queue_stats {
    int64_t allocations;
    int64_t free_queue_count;
    int64_t free_queue_with_connector_count;
    int64_t reap_queue_count;
};

struct zpn_fohh_client_exporter_mt_free_queue {
    zpath_mutex_t lock;
    struct zpn_fohh_client_exporter_mt_head_tailq reap_mt_list;

    /* Free list */
    struct zpn_fohh_client_exporter_mt_head_tailq mt_list;

    /* Intermediate free list with mt->connector when mt->connector this is freed, mt moves into  mt_list above*/
    struct zpn_fohh_client_exporter_mt_head_tailq mt_list_with_connector;

    struct zpn_fohh_client_exporter_mt_queue_stats stats;
};

static struct zpn_fohh_client_exporter_mt_free_queue free_q;

static void zpn_fohh_client_exporter_destroy(struct zpn_fohh_client_exporter *zfce);
static void zpn_fohh_client_exporter_mt_free_q_init();
static struct zpn_fohh_client_exporter_mt *zpn_fohh_client_exporter_mt_allocate(void);
static void zpn_fohh_client_exporter_mt_reap(struct zpn_fohh_client_exporter_mt *mt);

static int zpn_fohh_client_exporter_send_all_app_queries(struct zpn_fohh_client_exporter *zfce);

const struct zpn_mconn_local_owner_calls mconn_fohh_client_exporter_calls;

static int zpn_fohh_client_exporter_mt_destroy(struct zpn_fohh_client_exporter_mt *mt, char *err);

static void zpn_fohh_client_exporter_mt_release_request(struct zpn_fohh_client_exporter_mt *mt);
#if EXPORTER_USE_ZEVENT
static void zpn_fohh_client_exporter_mt_release_request_thread_call(struct zevent_base *zbase, void *cookie, int64_t int_cookie);
#else
static void zpn_fohh_client_exporter_mt_release_request_thread_call(struct fohh_thread *thread, void *cookie, int64_t int_cookie);
#endif
static int zpn_fohh_client_exporter_schedule_release_request(struct zpn_fohh_client_exporter_mt *mt);
static int zpn_fohh_client_exporter_mt_busy_to_idle_list(struct zpn_fohh_client_exporter_mt *mt, int zfce_locked);
static int zpn_fohh_client_exporter_mt_remove_from_busy_list_and_destroy(struct zpn_fohh_client_exporter_mt *mt, int zfce_locked);

static void zpn_fohh_client_socket_errors_monitor(void);

#define ZFCE_MONITOR_TIMER_S        10
#define ZFCE_MT_IDLE_TIMEOUT_US     (9*ZFCE_MONITOR_TIMER_S*1000000)  /* Idle mt timeout in 90 seconds */
#define ZFCE_IDLE_TIMEOUT_US        (60*5*1000000)                    /* Idle exporter timeout in 5 minutes */
#define ZFCE_IDLE_TIMEOUT_GRACE_US  (60*1*1000000)                    /* Idle exporter grace timeout in 1 minute */

/* ZFCE stats helper macros */
#define EXPORTER_ZFCE_STATS_FIELD_INC(field, increment) __sync_add_and_fetch_8(&(field), increment)
#define EXPORTER_ZFCE_STATS_FIELD_DEC(field, decrement) __sync_sub_and_fetch_8(&(field), decrement)
#define EXPORTER_ZFCE_STATS_FIELD_GET(field) (field)
#define EXPORTER_ZFCE_STATS_FIELD_RESET(field) __sync_lock_release_8(&(field))


/* Exporter usage stats global state */
struct exporter_usage_stats g_exporter_usage = { 0 };

/* Hard assert enabled by default */
int g_exporter_disable_hard_assert = 0;

/* Fatal errors threshold */
int g_exporter_fatal_errors_threshold = 0;

/***************************************************************************************************
 * Exporter support for broker redirect:
 * Feature flag: "config.feature.exporter.redirect_mode"
 * Values:
 *   EXPORTER_REDIRECT_MODE_DISABLED:    redirects not supported.
 *   EXPORTER_REDIRECT_MODE_FORCE:       only respond to forced redirects (e.g. broker shutting down).
 *   EXPORTER_REDIRECT_MODE_FULL:        support initial redirect and any subsequent redirects, regardless of redirect's attribute.
 */
#define EXPORTER_REDIRECT_MODE_DISABLED     0
#define EXPORTER_REDIRECT_MODE_FORCE        1
#define EXPORTER_REDIRECT_MODE_FULL         2
int64_t g_exporter_redirect_mode = EXPORTER_REDIRECT_MODE_DISABLED;
int64_t g_exporter_redirect_mode_sanitized = EXPORTER_REDIRECT_MODE_DEFAULT;

/* How long to hold initial request while we wait for redirect.
 * Only applicable for EXPORTER_REDIRECT_MODE_FULL mode. */
#define EXPORTER_REDIRECT_TIMEOUT_S     2
#define EXPORTER_REDIRECT_TIMEOUT_US    0


/*************************************************************************************************
 *
 *  zfce global exporter stuff
 *
 *************************************************************************************************/
static void zpn_fohh_client_exporter_monitor_cb(evutil_socket_t sock, short flags, void *cookie)
{
    struct zpn_fohh_client_exporter *zfce = cookie;
    struct zpn_fohh_client_exporter_mt *mt;
    struct zpn_fohh_client_exporter_mt *next_mt;
    struct zpn_fohh_client_exporter_mt_head_tailq head;
    char lookup_id[1024] =  {0};
    int64_t cur_time_us;
    int destroy_exporter = 0;
    int64_t cur_mt_destroy_count = 0;

    if (zfce == NULL || zfce->zfc == NULL)
        return;

    ZPN_LOG(AL_DEBUG, "%s for user %.*s: num_busy_mt = %d, num_idle_mt = %d, num_alloc_mt = %ld, num_reap_mt = %ld, num_free_mt = %ld, num_mt_free_with_conn = %ld",
            fohh_description(zfce->zfc->conn), ZPATH_DEBUG_BYTES, zfce->zfc->auth_token, zfce->num_busy_mt, zfce->num_idle_mt,
            (long)free_q.stats.allocations, (long)free_q.stats.reap_queue_count, (long)free_q.stats.free_queue_count, (long)free_q.stats.free_queue_with_connector_count);

    TAILQ_INIT(&head);

    cur_time_us = epoch_us();

    ZPATH_MUTEX_LOCK(&(zfce->lock), __FILE__, __LINE__);

    if (zfce->num_idle_mt > 0) {

        next_mt = TAILQ_FIRST(&(zfce->idle_mt_list));

        while (next_mt) {
            struct zpn_fohh_client_exporter_mt *mt = next_mt;

            next_mt = TAILQ_NEXT(next_mt, queue_entry);

            if (zpn_debug_get(ZPN_DEBUG_MCONN_IDX)) {
                ZPN_LOG(AL_DEBUG, "%s:%s: idle mtunnel host = %s, proto = %d, port = %d, tls = %d",
                        mt->dbg_str, mt->mtunnel_id, mt->key.host_name, mt->key.proto, mt->key.port, mt->key.tls);
            }

            if (cur_time_us - mt->idle_start_us > (ZFCE_MT_IDLE_TIMEOUT_US)) {
                ZPN_DEBUG_EXPORTER("%s:%s: export mt expired, remove, cur_time_us = %ld, idle_start_us = %ld",
                                   mt->dbg_str,
                                   mt->mtunnel_id, (long)cur_time_us, (long)mt->idle_start_us);
                argo_hash_remove(zfce->idle_mt,
                                 &mt->key,
                                 sizeof(struct zfce_mt_key),
                                 mt);
                TAILQ_REMOVE(&(zfce->idle_mt_list), mt, queue_entry);
                zfce->num_idle_mt--;
                TAILQ_INSERT_TAIL(&(head), mt, queue_entry);
            } else if ((mt->status == zfce_mt_remote_disconnect) ||
                       (mt->status == zfce_mt_connect_error) ||
                       (mt->status == zfce_mt_request_error)) {
                ZPN_DEBUG_EXPORTER("%s:%s: export mt is in %s state, should destroy",
                                   mt->dbg_str,
                                   mt->mtunnel_id, zfce_mt_status_string(mt->status));
                argo_hash_remove(zfce->idle_mt,
                                 &mt->key,
                                 sizeof(struct zfce_mt_key),
                                 mt);
                TAILQ_REMOVE(&(zfce->idle_mt_list), mt, queue_entry);
                zfce->num_idle_mt--;
                TAILQ_INSERT_TAIL(&(head), mt, queue_entry);
            }
        }
    }

    if (!zfce->num_busy_mt && !zfce->num_idle_mt && !zfce->idle_start_us) {
        zfce->idle_start_us = cur_time_us;
        ZPN_DEBUG_EXPORTER("No mt is allocated, start exporter: %s at %ld", fohh_description(zfce->zfc->conn), (long) zfce->idle_start_us);
    }
    ZPATH_MUTEX_UNLOCK(&(zfce->lock), __FILE__, __LINE__);

    while ((mt = TAILQ_FIRST(&(head)))) {
        TAILQ_REMOVE(&(head), mt, queue_entry);
        zpn_fohh_client_exporter_mt_destroy(mt, MT_CLOSED_TERMINATED);
        cur_mt_destroy_count++;
    }

    ZPATH_MUTEX_LOCK(&(zfce->lock), __FILE__, __LINE__);

    // Increment the number of mtunnels destroyed above
    EXPORTER_ZFCE_STATS_FIELD_INC(zfce->mt_destroy_count, cur_mt_destroy_count);
    if (zfce->remote_server_type == zpn_server_type_broker && !zfce->zfc->pra_scope_gid) {
        snprintf(lookup_id, 1024, "%s", zfce->zfc->auth_token);
    } else if (zfce->remote_server_type == zpn_server_type_broker && zfce->zfc->pra_scope_gid) {
        snprintf(lookup_id, 1024, "%s:%"PRId64"", zfce->zfc->auth_token, zfce->zfc->pra_scope_gid);
    } else {
        snprintf(lookup_id, 1024, "%s:%s", zfce->zfc->auth_token, zfce->zfc->broker_dns_name);
    }

    if (zfce->idle_start_us) {
        ZPN_DEBUG_EXPORTER("Exporter %s for user %.*s has been idle for %ld seconds, ssl_ctx_ref_count: %ld",
                           fohh_description(zfce->zfc->conn),
                           ZPATH_DEBUG_BYTES, zfce->zfc->auth_token,
                           (long)((cur_time_us - zfce->idle_start_us) / 1000000), (long)zfce->ssl_ctx_ref_count);

        if ((cur_time_us - zfce->idle_start_us) > ZFCE_IDLE_TIMEOUT_US) {

            char *conn_desc = (zfce->zfc != NULL) ? fohh_description(zfce->zfc->conn) : "unknown";

            ZPATH_MUTEX_LOCK(&local_lock, __FILE__, __LINE__);
            if (zfce->ssl_ctx_ref_count <= 0) {
                ZPN_LOG(AL_INFO, "%s: Exporter has been idle for more than %ld seconds, zfce: %p destroy allowed, ssl_ctx_ref_count: %ld, conn mt created count: %ld, conn mt destroyed count: %ld",
                        conn_desc, (long)(ZFCE_IDLE_TIMEOUT_US / 1000000), zfce, (long)zfce->ssl_ctx_ref_count, (long)zfce->mt_create_count, (long)zfce->mt_destroy_count);

                /*** Normal destroy ****/
                argo_hash_remove(exporter_by_id, lookup_id, strnlen(lookup_id, 1024), zfce);
                destroy_exporter = 1;
            } else {
                /* Safety valve: avoid holding up indefinitely, if ref_count did not drop for (ZFCE_IDLE_TIMEOUT_US + ZFCE_IDLE_TIMEOUT_GRACE_US) time, go ahead destroy to avoid buildup */
                if ((cur_time_us - zfce->idle_start_us) > (ZFCE_IDLE_TIMEOUT_US + ZFCE_IDLE_TIMEOUT_GRACE_US)) {
                    ZPN_LOG(AL_WARNING, "%s: Exporter has been idle for more than %ld seconds, grace period expired, zfce: %p allowing destroy with non-zero ssl_ctx_ref_count: %ld, conn mt created count: %ld, conn mt destroyed count: %ld",
                            conn_desc, (long)(ZFCE_IDLE_TIMEOUT_US / 1000000), zfce, (long)zfce->ssl_ctx_ref_count, (long)zfce->mt_create_count, (long)zfce->mt_destroy_count);

                    /*** Force destroy ****/
                    argo_hash_remove(exporter_by_id, lookup_id, strnlen(lookup_id, 1024), zfce);
                    destroy_exporter = 1;

                    EXPORTER_GLOBAL_USAGE_STATS_FIELD_INC(exporter_zfce_conn_force_destroy);
                    EXPORTER_GLOBAL_USAGE_STATS_FIELD_DEC(exporter_zfce_conn);
                } else {
                    ZPN_LOG(AL_INFO, "%s: Exporter has been idle for more than %ld seconds, zfce: %p waiting for ssl_ctx_ref_count: %ld to drop to zero before destroy, conn mt created count: %ld, conn mt destroyed count: %ld",
                            conn_desc, (long)(ZFCE_IDLE_TIMEOUT_US / 1000000), zfce, (long)zfce->ssl_ctx_ref_count, (long)zfce->mt_create_count, (long)zfce->mt_destroy_count);
                }
            }
            ZPATH_MUTEX_UNLOCK(&local_lock, __FILE__, __LINE__);
        }
    }
    ZPATH_MUTEX_UNLOCK(&(zfce->lock), __FILE__, __LINE__);

    if (destroy_exporter) {
        zpn_fohh_client_exporter_destroy(zfce);
    }
}

static void zpn_fohh_client_exporter_add_monitor_timer(struct fohh_connection *connection,
                                                       struct zpn_fohh_client_exporter *zfce,
                                                       event_callback_fn monitor_timer_cb,
                                                       int64_t secs,
                                                       int64_t usecs)
{
    int thread_num = fohh_connection_get_thread_id(connection);
    struct event_base *base = fohh_get_thread_event_base(thread_num);
    struct timeval tv;

    /* Check to see if the connection was reused */
    if (zfce->monitor_ev) {
        event_free(zfce->monitor_ev);
        zfce->monitor_ev = NULL;
    }

    /* We assume the fohh connection is established already and is assigned a thread */
    zfce->monitor_ev = event_new(base,
                                 -1,
                                 EV_PERSIST,
                                 monitor_timer_cb,
                                 zfce);

    tv.tv_sec = secs;
    tv.tv_usec = usecs;
    if (event_add(zfce->monitor_ev, &tv)) {
        ZPN_LOG(AL_ERROR, "Could not activate monitor timer");
        event_free(zfce->monitor_ev);
        zfce->monitor_ev = NULL;
    }
}

static struct zpn_fohh_client_exporter_mt *get_mtunnel_by_tag_id(struct zpn_fohh_client_exporter *zfce, int32_t tag_id)
{
    struct zpn_fohh_client_exporter_mt *mt;

    ZPATH_MUTEX_LOCK(&(zfce->lock), __FILE__, __LINE__);
    mt = argo_hash_lookup(zfce->mtunnel_by_id,
                          &tag_id,
                          sizeof(tag_id),
                          NULL);
    ZPATH_MUTEX_UNLOCK(&(zfce->lock), __FILE__, __LINE__);
    return mt;
}

static int zpn_client_app_cb(void *argo_cookie_ptr,
                             void *argo_structure_cookie_ptr,
                             struct argo_object *object)
{
    struct zpn_client_app *req = (struct zpn_client_app *)object->base_structure_void;

    if (zpn_debug_get(ZPN_DEBUG_APPLICATION_IDX)) {
        char dump[8000];
        if (argo_object_dump(object, dump, sizeof(dump), NULL, 0) == ARGO_RESULT_NO_ERROR) {
            ZPN_LOG(AL_DEBUG, "Rx: %s", dump);
        }
    }

    if (!req->deleted && !req->bypass) {
        ZPN_LOG(AL_DEBUG, "Application: %s", req->app_domain);
    }

    return ZPN_RESULT_NO_ERROR;
}

static int zpn_client_app_complete_cb(void *argo_cookie_ptr,
                                      void *argo_structure_cookie_ptr,
                                      struct argo_object *object)
{
    struct zpn_fohh_client_exporter *zfce = argo_structure_cookie_ptr;

    if (zpn_debug_get(ZPN_DEBUG_APPLICATION_IDX)) {
        char dump[8000];
        if (argo_object_dump(object, dump, sizeof(dump), NULL, 0) == ARGO_RESULT_NO_ERROR) {
            ZPN_LOG(AL_DEBUG, "%s: Rx: %s", ZFC_DBG(zfce->zfc), dump);
        }
    }

    ZPN_LOG(AL_DEBUG, "%s: Got all applications", ZFC_DBG(zfce->zfc));
    return ZPN_RESULT_NO_ERROR;
}

/* Receive ack error if user is not allowed to join */
static int zpn_exporter_data_ack_cb(void *argo_cookie_ptr __attribute__((unused)),
                                        void *argo_structure_cookie_ptr,
                                        struct argo_object *object)
{
    struct zpn_fohh_client_exporter *zfce = argo_structure_cookie_ptr;
    struct zpn_exporter_data_ack *ack = object->base_structure_void;
    struct zpn_fohh_client_exporter_mt *mt;

    if (!ack->tag_id) {
        ZPN_LOG(AL_NOTICE, "SESS_PROC Exporter data ack received without tag id");
        return ZPN_RESULT_NO_ERROR;
    }

    mt = get_mtunnel_by_tag_id(zfce, ack->tag_id);
    if (!mt) {
        ZPN_LOG(AL_NOTICE, "SESS_PROC Exporter data ack received without tunnel. It timed out? tag_id = %d", ack->tag_id);
        return ZPN_RESULT_NO_ERROR;
    }

    if (ack->cmd_type) {
        ZPN_LOG(AL_NOTICE, "SESS_PROC mtunnel_id = %s Exporter data request ack error : %s, cmd %d, data %"PRId64"",
            mt->mtunnel_id, ack->error ? ack->error : "Empty", ack->cmd_type, ack->cmd_data);
        mt_cb_on_ack_update(mt->mtunnel_id, ack->error, ack->cmd_type, ack->cmd_data);
    }

    return ZPN_RESULT_NO_ERROR;
}


static int zpn_exporter_log_data_ack_cb(void *argo_cookie_ptr,
                                        void *argo_structure_cookie_ptr,
                                        struct argo_object *object)
{
    struct zpn_fohh_client_exporter *zfce = argo_structure_cookie_ptr;
    struct zpn_exporter_log_data_ack *ack = object->base_structure_void;
    struct zpn_fohh_client_exporter_mt *mt;

    if (!ack->tag_id) {
        ZPN_LOG(AL_NOTICE, "Exporter log data ack received without tag id");
        return ZPN_RESULT_NO_ERROR;
    }

    mt = get_mtunnel_by_tag_id(zfce, ack->tag_id);
    if (!mt) {
        ZPN_LOG(AL_NOTICE, "Exporter log data ack received without tunnel. It timed out? tag_id = %d", ack->tag_id);
        return ZPN_RESULT_NO_ERROR;
    }
    if (ack->error) {
        ZPN_LOG(AL_NOTICE, "mtunnel_id = %s Exporter log data request ack error : %s", mt->mtunnel_id, ack->error);
    }

    return ZPN_RESULT_NO_ERROR;
}

void update_ack_error(struct zpn_mtunnel_request_ack *ack, struct zpn_fohh_client_exporter_mt *mt){
    char err_str[1024];

    ZPN_LOG(AL_NOTICE, "%s: mtunnel_id = %s  Tunnel request ack error %s : %s", mt->dbg_str, mt->mtunnel_id ? mt->mtunnel_id : "<unknown>", ack->error ? ack->error : "error", ack->reason);

    if (ack->reason) {
        snprintf(err_str, sizeof(err_str), "%s,%s", ack->error, ack->reason);
    } else {
        snprintf(err_str, sizeof(err_str), "%s", ack->error);
    }
    if (mt->err) ZPN_FREE(mt->err);
    mt->err = ZPN_STRDUP(err_str, strlen(err_str));
    if (ack->stepup_auth_level_id) {
        if (mt->stepup_auth_level_id) ZPN_FREE(mt->stepup_auth_level_id);
        mt->stepup_auth_level_id = ZPN_STRDUP(ack->stepup_auth_level_id, strlen(ack->stepup_auth_level_id));
    }
}

static int zpn_mtunnel_request_ack_cb(void *argo_cookie_ptr,
                                      void *argo_structure_cookie_ptr,
                                      struct argo_object *object)
{
    struct zpn_fohh_client_exporter *zfce = argo_structure_cookie_ptr;
    struct zpn_mtunnel_request_ack *ack = object->base_structure_void;
    struct zpn_fohh_client_exporter_mt *mt;
    int res = ZPN_RESULT_NO_ERROR;
    int64_t fatal_errors = 0;

    if (!ack->tag_id) {
        ZPN_LOG(AL_NOTICE, "Mtunnel request ack received without tag id");
        return ZPN_RESULT_NO_ERROR;
    }

    mt = get_mtunnel_by_tag_id(zfce, ack->tag_id);
    if (!mt) {
        ZPN_LOG(AL_NOTICE, "Mtunnel request ack received without tunnel. It timed out? tag_id = %d", ack->tag_id);
        return ZPN_RESULT_NO_ERROR;
    }

    if (zpn_debug_get(ZPN_DEBUG_MTUNNEL_IDX)) {
        char dump[8000];
        if (argo_object_dump(object, dump, sizeof(dump), NULL, 1) == ARGO_RESULT_NO_ERROR) {
            ZPN_LOG(AL_DEBUG, "%s: Rx: %s", mt->dbg_str, dump);
        }
    }

    ZPATH_MUTEX_LOCK(&(mt->lock), __FILE__, __LINE__);

    if (!ack->mtunnel_id) {
        ZPN_LOG(AL_NOTICE, "%s: Tunnel request ack without mtunnel_id", mt->dbg_str);
        mt->status = zfce_mt_connect_error;
        if (ack->error) {
            update_ack_error(ack, mt);
        }
        goto exit;
    }

    mt->mtunnel_id = ZPN_STRDUP(ack->mtunnel_id, strlen(ack->mtunnel_id));

    if (ack->error) {
        mt->status = zfce_mt_connect_error;
        update_ack_error(ack, mt);
        goto exit;
    }

    res = zpn_fohh_client_attach_mconn(zfce->zfc, &(mt->broker_mconn), &mt->tag_id, sizeof(int32_t));
    if (res) {
        ZPN_LOG(AL_ERROR, "%s:%s: Cannot attach mconn to zpn_fohh_client", mt->dbg_str, mt->mtunnel_id);
        mt->status = zfce_mt_connect_error;
        goto exit;
    }

    if (mt->key.tls) {
        int mconn_thread_id = fohh_connection_get_thread_id(zfce->zfc->conn);

        if (!zfce->ssl_ctx_connector) {
            ZPN_LOG(AL_NOTICE, "Null ssl_ctx_connector");
            mt->status = zfce_mt_connect_error;
            EXPORTER_GLOBAL_USAGE_STATS_FIELD_INC(exporter_zfce_conn_destroy_before_cb);
            goto exit;
        }

        if (!mt->is_guac_proxy_conn) {
            mt->connector = (struct zpn_connector *)zpn_connector_tun_create(mt, zfce->ssl_ctx_connector, &(zfce->ssl_ctx_ref_count),  mt->verify_cert, mt->verify_host);
            ZPN_DEBUG_MTUNNEL("%s:%s: Created TLS state. Verify cert = %d, Verify host = %d, connector: %p",
                          mt->dbg_str, mt->mtunnel_id, mt->verify_cert, mt->verify_host, mt->connector);

            if (!mt->connector) {
                ZPN_LOG(AL_ERROR, "%s:%s: Cannot create tlv connector", mt->dbg_str, mt->mtunnel_id);
                mt->status = zfce_mt_connect_error;
                EXPORTER_GLOBAL_USAGE_STATS_FIELD_INC(exporter_connnector_create_error);

                /* Record this as fatal error - Reason is socketpair() failed or memory allocation failed, both are really bad !*/
                EXPORTER_GLOBAL_USAGE_STATS_FIELD_INC(exporter_fatal_connector_socket_error);
                goto exit;
            } else {
                // Created connector instance
                EXPORTER_GLOBAL_USAGE_STATS_FIELD_INC(exporter_connnector_create);
            }

            res = zpn_mconn_add_global_owner(&(((struct zpn_connector_tun *)mt->connector)->mconn_c.mconn),
                                             1,
                                             mt,
                                             &(mt->tag_id),
                                             sizeof(mt->tag_id),
                                             &zfce_mt_global_call_set_parser);

            res = zpn_mconn_add_global_owner(&(((struct zpn_connector_tun *)mt->connector)->mconn_s.mconn),
                                             1,
                                             mt,
                                             &(mt->tag_id),
                                             sizeof(mt->tag_id),
                                             &zfce_mt_global_call_set_broker);

            res  = zpn_connector_tun_connect(mconn_thread_id,
                                             (struct zpn_connector_tun *)mt->connector,
                                             &(mt->broker_mconn.mconn),
                                             1,
                                             0,
                                             mt->key.host_name);
            if (res) {
                EXPORTER_GLOBAL_USAGE_STATS_FIELD_INC(exporter_connnector_connect_error);
                ZPN_LOG(AL_CRITICAL, "%s: mtunnel_id = %s  Server side connect returned: %s", mt->dbg_str, mt->mtunnel_id, zpn_result_string(res));
                mt->status = zfce_mt_connect_error;
                goto exit;
            }

            ZPN_DEBUG_SIEM_ALL("%s: mtunnel_id = %s Connected server side", mt->dbg_str, mt->mtunnel_id);

            res  = zpn_connector_tun_connect(mconn_thread_id,
                                             (struct zpn_connector_tun *)mt->connector,
                                             &(mt->parser_mconn.mconn),
                                             0,
                                             mt->key.tls,
                                             mt->key.host_name);
            if (res) {
                EXPORTER_GLOBAL_USAGE_STATS_FIELD_INC(exporter_connnector_connect_error);
                ZPN_LOG(AL_CRITICAL, "%s: mtunnel_id = %s  Client side connect returned: %s", mt->dbg_str, mt->mtunnel_id, zpn_result_string(res));
                mt->status = zfce_mt_connect_error;
                goto exit;
            }
        }

        ZPN_DEBUG_SIEM_ALL("%s: mtunnel_id = %s  Connected client side", mt->dbg_str, mt->mtunnel_id);
    } else {
        res = zpn_mconn_connect_peer(&(mt->parser_mconn.mconn), &(mt->broker_mconn.mconn));
        if (res) {
            EXPORTER_GLOBAL_USAGE_STATS_FIELD_INC(exporter_connnector_connect_error);
            ZPN_LOG(AL_ERROR, "%s:%s: Cannot connect peers", mt->dbg_str, mt->mtunnel_id);
            mt->status = zfce_mt_connect_error;
            goto exit;
        }
    }

    zpn_mconn_set_fohh_thread_id(&(mt->parser_mconn.mconn), fohh_connection_get_thread_id(zfce->zfc->conn));

    res = zpn_mconn_add_local_owner(&(mt->parser_mconn.mconn),
                                    0,
                                    mt,
                                    NULL,
                                    0,
                                    &mconn_fohh_client_exporter_calls);
    if (res) {
        ZPN_LOG(AL_ERROR, "%s:%s: Cannot add local owner for exporter: %s", mt->dbg_str, mt->mtunnel_id, zpath_result_string(res));
        EXPORTER_GLOBAL_USAGE_STATS_FIELD_INC(exporter_connnector_connect_error);
        mt->status = zfce_mt_connect_error;
        goto exit;
    }

    ZPN_DEBUG_MTUNNEL("%s:%s: Attached mconn to zpn_fohh_client", mt->dbg_str, mt->mtunnel_id);
    mt->status = zfce_mt_connected;

exit:

    ZPATH_MUTEX_UNLOCK(&(mt->lock), __FILE__, __LINE__);

    if (mt->status_cb && mt->cookie_void && !mt->request_released) {
        if (mt->status == zfce_mt_connect_error) {
            zpn_fohh_client_exporter_mt_release_request(mt);
        } else {
            (*mt->status_cb)(mt, mt->cookie_void, mt->cookie_int, mt->status, mt->err);
        }
    }

    /* Hard reset allowed ? */
    if (g_exporter_disable_hard_assert == 0) {
        fatal_errors = EXPORTER_GLOBAL_USAGE_STATS_FIELD_GET(exporter_fatal_connector_socket_error);

        /* Did we exceed fatal errors threshold ? */
        if (fatal_errors > g_exporter_fatal_errors_threshold) {
            ZPN_LOG(AL_CRITICAL, "Critical socket error, aborting exporter with hard assert: fatal_errors count : %ld", (long)fatal_errors);
            sleep(1);
            zthread_assert_text("Critical socket error, aborting exporter\n");
        }
    }

    return ZPN_RESULT_NO_ERROR;
}

static int zpn_mtunnel_end_cb(void *argo_cookie_ptr,
                              void *argo_structure_cookie_ptr,
                              struct argo_object *object)
{
    struct zpn_fohh_client_exporter *zfce = argo_structure_cookie_ptr;
    struct zpn_mtunnel_end *req = object->base_structure_void;
    const char *id = req->mtunnel_id ? req->mtunnel_id : "NULL";
    struct zpn_fohh_client_exporter_mt *mt = NULL;
    int release_request = 0;

    ZPN_DEBUG_MCONN("%s: %s: Received tunnel end for tag_id = %d", ZFC_DBG(zfce->zfc), id, req->tag_id);

    if (!req->tag_id) {
        ZPN_LOG(AL_ERROR, "%s: Expecting tag_id in mtunnel_end request", id);
        return ZPN_RESULT_NO_ERROR;
    }

    mt = get_mtunnel_by_tag_id(zfce, req->tag_id);
    if (!mt) {
        ZPN_LOG(AL_NOTICE, "%s, Cannot find the mtunnel for tag_id = %d", id, req->tag_id);
        return ZPN_RESULT_NO_ERROR;
    }

    if (zpn_debug_get(ZPN_DEBUG_MTUNNEL_IDX)) {
        char dump[8000];
        if (argo_object_dump(object, dump, sizeof(dump), NULL, 1) == ARGO_RESULT_NO_ERROR) {
            ZPN_LOG(AL_DEBUG, "%s: Rx: %s", ZFC_DBG(zfce->zfc), dump);
        }
    }

    ZPATH_MUTEX_LOCK(&(mt->lock), __FILE__, __LINE__);

    mt->broker_mconn.mconn.fin_rcvd = 1;
    if (!mt->broker_mconn.mconn.fin_rcvd_us) mt->broker_mconn.mconn.fin_rcvd_us = epoch_us();

    if (req->drop_data) {
        mt->broker_mconn.mconn.drop_tx = req->drop_data;
    }

    if (req->error) {
        if (mt->err) ZPN_FREE(mt->err);
        mt->err = ZPN_STRDUP(req->error, strlen(req->error));
    }

    if (mt->mtunnel_id) {
        mt->status = zfce_mt_remote_disconnect;
    } else {
        /*
         * This is the case where we get mtunnel_end instead of mtunnel_request_ack when
         * setting up mtunnel
         */
        mt->status = zfce_mt_connect_error;
    }

    if (mt->broker_mconn.mconn.peer) {
        zpn_mconn_forward_mtunnel_end(&mt->parser_mconn.mconn, MT_CLOSED_TERMINATED, req->drop_data);
    } else {
        /* if mtunnel_end is returned before mtunnel is setup, call exporter callback directly */
        release_request = 1;
    }

    ZPATH_MUTEX_UNLOCK(&(mt->lock), __FILE__, __LINE__);

    if (release_request) {
        zpn_fohh_client_exporter_mt_release_request(mt);
    }

    return ZPN_RESULT_NO_ERROR;
}

static void zfce_mt_timer_callback(evutil_socket_t sock, short flags, void *cookie)
{
    struct zpn_fohh_client_exporter_mt *mt = NULL;
    struct zpn_fohh_client_exporter_mt *tmp_mt = NULL;

    /* Reap clean mt */
    ZPATH_MUTEX_LOCK(&(free_q.lock), __FILE__, __LINE__);

    for (mt = TAILQ_FIRST(&(free_q.reap_mt_list)); mt != NULL; mt = tmp_mt) {
        tmp_mt = TAILQ_NEXT(mt, queue_entry);

        /* If mt is clean, move from reap list to free list */
        if (zpn_mconn_fohh_tlv_clean(&mt->broker_mconn))  {
            TAILQ_REMOVE(&(free_q.reap_mt_list), mt, queue_entry);
            free_q.stats.reap_queue_count--;

            mt->free_start_us = epoch_us();
            if (mt->connector) {
                /* if mt->connector present, put mt into intermediate free list to slow free mt->connector */
                TAILQ_INSERT_TAIL(&(free_q.mt_list_with_connector), mt, queue_entry);
                free_q.stats.free_queue_with_connector_count++;

                /* Schedule slow free */
                EXPORTER_GLOBAL_USAGE_STATS_FIELD_INC(exporter_mt_connector_sched_slow_free);
            } else {
                TAILQ_INSERT_TAIL(&(free_q.mt_list), mt, queue_entry);
                free_q.stats.free_queue_count++;
            }
        }
    }

    ZPATH_MUTEX_UNLOCK(&(free_q.lock), __FILE__, __LINE__);
}

#define MT_CONNECTOR_FREE_DELAY_US (5*1000*1000)   // 5 seconds delay

static void zfce_mt_connector_free_timer_callback(evutil_socket_t sock, short flags, void *cookie)
{
    struct zpn_fohh_client_exporter_mt *mt = NULL;
    struct zpn_fohh_client_exporter_mt *tmp_mt = NULL;
    int64_t now = epoch_us();

    /* Reap clean mt */
    ZPATH_MUTEX_LOCK(&(free_q.lock), __FILE__, __LINE__);

    for (mt = TAILQ_FIRST(&(free_q.mt_list_with_connector)); mt != NULL; mt = tmp_mt) {
        tmp_mt = TAILQ_NEXT(mt, queue_entry);

        /* Free only after MT_CONNECTOR_FREE_DELAY_US elapsed */
        if (mt->connector && ((now - mt->free_start_us) > MT_CONNECTOR_FREE_DELAY_US)) {
            zpn_connector_tun_destroy(mt->connector);
            ZPN_FREE(mt->connector);
            mt->connector = NULL;

            /* Record destruction */
            EXPORTER_GLOBAL_USAGE_STATS_FIELD_INC(exporter_connnector_destroy);

            /* Move entry from intermediate mt_list_with_connector to free mt_list */
            TAILQ_REMOVE(&(free_q.mt_list_with_connector), mt, queue_entry);
            free_q.stats.free_queue_with_connector_count--;
            TAILQ_INSERT_TAIL(&(free_q.mt_list), mt, queue_entry);
            free_q.stats.free_queue_count++;
        } else {
            EXPORTER_GLOBAL_USAGE_STATS_FIELD_INC(exporter_mt_connector_skip_slow_free_queue);

            /* Stop processing as following entries in queue mt_list_with_connector as these should be later than current entry */
            break;
        }
    }

    ZPATH_MUTEX_UNLOCK(&(free_q.lock), __FILE__, __LINE__);

    /* Run socket errors monitor */
    zpn_fohh_client_socket_errors_monitor();
}

#define MT_CONNECTOR_FD_ERROR_THRESHOLD 1
#define MT_CONNECTOR_ERROR_ACCUMULATE_THRESHOLD 12 // One minute of sustained error accumulation

static void zpn_fohh_client_socket_errors_monitor(void)
{
    static int64_t prev_fd_resource_errors = 0; /* Remember last release errors count */
    static int64_t hits_accumulate = 0;  /* Remember running total for hits */

    int64_t cur_fd_resource_errors = 0;
    int64_t errors_change = 0;
    int64_t fatal_errors = 0;

    /* Get exporter wide socket resource errors */
    cur_fd_resource_errors = fohh_get_socket_resource_errors_count();

    /* Check for growth */
    errors_change = cur_fd_resource_errors - prev_fd_resource_errors;

    if (errors_change >= MT_CONNECTOR_FD_ERROR_THRESHOLD) {

        /* Score a hit on accumulating errors */
        hits_accumulate++;

        ZPN_LOG(AL_WARNING, "FD resource errors: growth: change: %ld in interval, current errors: %ld, previous errors: %ld, hits accumulate: %ld",
                (long)errors_change, (long)cur_fd_resource_errors, (long)prev_fd_resource_errors, (long)hits_accumulate);
    } else {
        /* Score a miss on accumulating errors: back off */
        if (hits_accumulate >= 1) {
            hits_accumulate--;

            ZPN_LOG(AL_INFO, "FD resource errors: backoff: change: %ld in interval, current errors: %ld, previous errors: %ld, hits accumulate: %ld",
                    (long)errors_change, (long)cur_fd_resource_errors, (long)prev_fd_resource_errors, (long)hits_accumulate);
        } else {

            /* Errors not increasing, assume things are normal */
            hits_accumulate = 0;
        }
    }

    /* Should we pull the plug ? */
    if (hits_accumulate > MT_CONNECTOR_ERROR_ACCUMULATE_THRESHOLD) {
        ZPN_LOG(AL_CRITICAL, "FD resource errors: threshold crossed: change: %ld in interval, current errors: %ld, previous errors: %ld, hits accumulate: %ld",
                (long)errors_change, (long)cur_fd_resource_errors, (long)prev_fd_resource_errors, (long)hits_accumulate);

        fatal_errors = EXPORTER_GLOBAL_USAGE_STATS_FIELD_INC(exporter_fatal_connector_socket_error);

        /* Hard reset allowed ? */
        if (g_exporter_disable_hard_assert == 0) {

            /* Did we exceed fatal errors threshold ? */
            if (fatal_errors > g_exporter_fatal_errors_threshold) {
                ZPN_LOG(AL_CRITICAL, "Critical socket error, aborting exporter with hard assert: fatal_errors count : %ld", (long)fatal_errors);
                sleep(1);
                zthread_assert_text("Critical socket error, aborting exporter\n");
            }
        }
    }

    prev_fd_resource_errors = cur_fd_resource_errors;
}

/* Sanitize config override in case of invalid value */
static void sanitize_config_override_cb(const int64_t *target_address, int64_t impacted_gid) {
    if (target_address == &g_exporter_redirect_mode) {
        if (g_exporter_redirect_mode < 0 || g_exporter_redirect_mode > EXPORTER_REDIRECT_MODE_FULL) {
            g_exporter_redirect_mode_sanitized = EXPORTER_REDIRECT_MODE_DEFAULT;
            ZPN_LOG(AL_ERROR, EXPORTER_REDIRECT_FEATURE_OVERRIDE": invalid value %"PRId64" received, use default value %"PRId64,
                                g_exporter_redirect_mode, g_exporter_redirect_mode_sanitized);
        } else {
            ZPN_LOG(AL_INFO, EXPORTER_REDIRECT_FEATURE_OVERRIDE": updated from %"PRId64" to %"PRId64,
                                g_exporter_redirect_mode_sanitized, g_exporter_redirect_mode);
            g_exporter_redirect_mode_sanitized = g_exporter_redirect_mode;
        }
    }
}

static void zpn_fohh_client_exporter_init_ot_mode_overrides() {
    /*
     * ot_mode will have different overrides. for instance,
     * no redirect will be allowed in this mode
     */
}

static void zpn_fohh_client_exporter_init_overrides() {
    /* in case some test code didn't initialize zpath_instance -
     * default to ZPATH_GLOBAL_CONFIG_OVERRIDE_GID */
    g_exporter_redirect_mode = EXPORTER_REDIRECT_MODE_DEFAULT;
    int64_t inst_gid = ZPATH_INSTANCE_GID ? ZPATH_INSTANCE_GID : ZPATH_GLOBAL_CONFIG_OVERRIDE_GID;
    int64_t cust_gid = ZPATH_GID_GET_CUSTOMER_GID(inst_gid);

    zpath_config_override_monitor_int(EXPORTER_REDIRECT_FEATURE_OVERRIDE,
                                    &g_exporter_redirect_mode,
                                    sanitize_config_override_cb,
                                    (int64_t)EXPORTER_REDIRECT_MODE_DEFAULT,
                                    (int64_t)inst_gid,
                                    (int64_t)cust_gid,
                                    (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                    (int64_t)0);
}

int zpn_fohh_client_exporter_init(int ot_mode)
{
    if (!initialized) {
        struct event_base *base = fohh_get_thread_event_base(fohh_worker_pool_get_thread_id(FOHH_WORKER_ZPN_MAINTENANCE));

        local_lock = ZPATH_MUTEX_INIT;

        zpn_fohh_client_exporter_mt_free_q_init();

        exporter_by_id = argo_hash_alloc(8, 1);
        if (!exporter_by_id) {
            ZPN_LOG(AL_ERROR, "Cannot allocate exporter_by_id hash in init");
            return ZPN_RESULT_NO_MEMORY;
        }

        mt_timer = event_new(base, -1, EV_PERSIST, zfce_mt_timer_callback, NULL);
        if (mt_timer) {
            struct timeval tv;

            tv.tv_sec = 5;
            tv.tv_usec = 0;
            if (event_add(mt_timer, &tv)) {
                ZPN_LOG(AL_ERROR, "Exporter cannot activate mt timer");
            }
        } else {
            ZPN_LOG(AL_ERROR, "Exporter cannot create mt timer");
        }

        mt_connector_free_timer = event_new(base, -1, EV_PERSIST, zfce_mt_connector_free_timer_callback, NULL);
        if (mt_connector_free_timer) {
            struct timeval tv;

            tv.tv_sec = 5;
            tv.tv_usec = 0;
            if (event_add(mt_connector_free_timer, &tv)) {
                ZPN_LOG(AL_ERROR, "Exporter cannot activate mt connector free timer");
            }
        } else {
            ZPN_LOG(AL_ERROR, "Exporter cannot create mt connector free timer");
        }

        if (zpath_debug_add_allocator(&zpn_allocator, "zpn")) {
            ZPN_LOG(AL_ERROR, "Could not add zpn allocator");
        }

        if (zpn_mconn_fohh_tlv_init_debug()) {
            ZPN_LOG(AL_ERROR, "Could not initialize debug command for fohh_tlv");
        }

        if (ot_mode) {
            zpn_fohh_client_exporter_init_ot_mode_overrides();
        } else {
            zpn_fohh_client_exporter_init_overrides();
        }

        struct argo_log_registered_structure *s;
        struct argo_structure_description *desc;

        desc = argo_register_global_structure(EXPORTER_USAGE_STATS_HELPER);

        // Log once per minute...
        s = argo_log_register_structure(argo_log_get("statistics_log"),
                                       "exporter_usage_stats",
                                       AL_INFO,
                                       60*1000*1000,
                                       desc,
                                       &g_exporter_usage,
                                       1,
                                       NULL,
                                       NULL);
        if (!s) {
            ZPN_LOG(AL_CRITICAL, "Could not register exporter usage stats- statistics_log not initialized?");
            return ZPN_RESULT_ERR;
        }

        initialized = 1;
    }

    return ZPN_RESULT_NO_ERROR;
}

static void zpn_fohh_client_exporter_send_all_mt_requests(struct zpn_fohh_client_exporter *zfce)
{
    ZPATH_MUTEX_LOCK(&(zfce->lock), __FILE__, __LINE__);
    if (zfce->status == zfc_ready) {
        struct zfce_pending_mt_req *pmr = NULL;

        while ((pmr = TAILQ_FIRST(&(zfce->pending_mt_reqs)))) {
            int res = ZPN_RESULT_NO_ERROR;
            struct zpn_fohh_client_exporter_mt *mt = NULL;
            char *app_type = NULL; /* default value */


            TAILQ_REMOVE(&(zfce->pending_mt_reqs), pmr, queue_entry);
            mt = pmr->mt;
            if (!mt) {
                ZPN_FREE(pmr);
                continue;
            }

            char *mtunnel_id = mt->mtunnel_id ? mt->mtunnel_id : "<unknown>";

            if (mt->is_guac_mt) {
                /* For Guacamole mtunnel, app type is ZMT_GUAC_STR */
                app_type = ZMT_GUAC_STR;
            }

            ZPN_LOG(AL_NOTICE, "%s:%s: mtunnel_id = %s status = %s Sending mtunnel request for %s:%d:%d with tag_id = %d.",
                    mt->dbg_str,
                    ZFC_DBG(zfce->zfc),
                    mtunnel_id,
                    zfce_mt_status_string(mt->status),
                    mt->key.host_name,
                    mt->key.proto,
                    mt->key.port,
                    mt->tag_id);

            res = zpn_send_zpn_mtunnel_request(&(zfce->zfc->fohh_tlv_state.tlv),
                                               fohh_connection_incarnation(zfce->zfc->conn),   /* fohh conn incarnation */
                                               mt->tag_id,                                     /* Tag id */
                                               mt->key.host_name,                              /* application name */
                                               mt->key.proto,                                  /* ip_proto */
                                               mt->key.port,                                   /* port_he */
                                               0,                                              /* double_encrypt */
                                               0,                                              /* zpn_probe_type */
                                               mt->publish_gid,
                                               app_type,
                                               0,
                                               0,
                                               mt->gposture,
                                               mt->gprofiles);
            if (res) {
                ZPN_LOG(AL_NOTICE, "%s: mtunnel_id = %s   Failed to send out mtunnel request", mt->dbg_str, mtunnel_id);
            }

            ZPN_FREE(pmr);
        }
    }
    ZPATH_MUTEX_UNLOCK(&(zfce->lock), __FILE__, __LINE__);
}


/* In wally, check if there is a global feature flag set */
int is_diags_logs_rpc_globally_disabled()
{

    int64_t config_val = zpath_config_override_get_config_int(DIAGS_LOG_RPC_GLOBAL_DISABLE,
                                                              &config_val,
                                                              DEFAULT_DIAGS_LOG_RPC_GLOBAL_DISABLE,
                                                              (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                              (int64_t)0); // Argument list terminator
    return config_val ? 1 : 0;
}

/* Check if diags logs rpc is disabled for this gid */
int is_diags_logs_rpc_disabled_for_customer(int64_t customer_gid)
{
    int64_t config_val = zpath_config_override_get_config_int( DIAGS_LOG_RPC,
                                                               &config_val,
                                                               DEFAULT_DIAGS_LOG_RPC,
                                                               customer_gid,
                                                               (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                               (int64_t) 0 );

    return config_val ? 0 : 1;
}

int is_diags_log_rpc_disabled(int64_t customer_gid)
{
    /* Global disable takes precedence */
    if (is_diags_logs_rpc_globally_disabled()) return 1;

    /* If global disable is not set then check if diags log rpc is disabled
     * on a specific customer gid
     */
    return is_diags_logs_rpc_disabled_for_customer(customer_gid);
}


void zpn_fohh_client_exporter_send_exporter_log_data(struct zpn_fohh_client_exporter_mt *mt)
{
    struct zpn_fohh_client_exporter *zfce = NULL;
    int diags_log_rpc_disabled = 0;

    if (mt == NULL) {
        return;
    }

    zfce = mt->state;
    if (zfce == NULL || zfce->zfc == NULL) {
        return;
    }

    diags_log_rpc_disabled = is_diags_log_rpc_disabled(zfce->zfc->customer_id);

    if (diags_log_rpc_disabled) {
        ZPN_LOG(AL_DEBUG, "Diagnostics logs RPC is disabled");
        return;
    }

    if (zfce && zfce->zfc && zfce->zfc->conn) {
        ZPATH_MUTEX_LOCK(&(zfce->lock), __FILE__, __LINE__);
        if (zfce->status == zfc_ready) {
            char * mtunnel_id = mt->mtunnel_id ? mt->mtunnel_id : "<unknown>";
            int res = ZPN_RESULT_NO_ERROR;
            ZPN_LOG(AL_NOTICE, "%s:%s: mtunnel_id = %s Sending mtunnel exporter log data for %s:%d with tag_id = %d console conn type = %s.",
                    mt->dbg_str,
                    ZFC_DBG(zfce->zfc),
                    mtunnel_id,
                    mt->console_user,
                    mt->console_cred_type,
                    mt->tag_id,
		    mt->console_conn_type);
            if (mt->shared_users_list) {
                ZPN_LOG(AL_INFO, "Sending shared-session user list %s",mt->shared_users_list);
            }

            res = zpn_send_exporter_log_data(&(zfce->zfc->fohh_tlv_state.tlv),
                                             mt->tag_id,                                     /* Tag id */
                                             mt->console_user,                               /* PRA console user */
                                             mt->console_cred_type,                          /* PRA console cred type */
                                             mt->capabilities_policy_id,                     /* PRA capability policy id */
                                             mt->file_transfer_list,                         /* PRA file transfer details */
                                             mt->cred_policy_id,                             /* Credential map policy ID */
                                             mt->guac_error_string,                          /* PRA guac error string */
                                             mt->console_conn_type,                          /* PRA console conn type */
                                             mt->is_pra_session,                             /* PRA session */
                                             mt->session_recording,                          /* PRA session recording flag */
                                             mt->pra_conn_id,                                /* PRA connection ID */
                                             mt->shared_users_list,                          /* PRA shared user-list */
                                             mt->shared_mode,                                /* PRA shared user monitor mode */
                                             mt->user_email,                                 /* PRA shared user */
                                             mt->event_type,                                 /* PRA shared user event */
                                             mt->credential_id,                              /* PRA credential Id */
                                             mt->credential_pool_id);                        /* PRA credential Pool Id */

            if (res) {
                ZPN_LOG(AL_NOTICE, "%s: mtunnel_id = %s Failed to send out mtunnel exporter log data", mtunnel_id, mt->dbg_str);
            }
        } else {
            ZPN_LOG(AL_ERROR, "zfce status is not ready for sending exporter log data");
        }
        ZPATH_MUTEX_UNLOCK(&(zfce->lock), __FILE__, __LINE__);
    }
}

#if EXPORTER_USE_ZEVENT
static void zpn_fohh_client_exporter_try_send_mt_requests(struct zevent_base *zbase, void *cookie, int64_t int_cookie)
#else
static void zpn_fohh_client_exporter_try_send_mt_requests(struct fohh_thread *thread, void *cookie, int64_t int_cookie)
#endif
{
    struct zpn_fohh_client_exporter *zfce = cookie;

    if (zfce->status == zfc_ready) {
        zpn_fohh_client_exporter_send_all_mt_requests(zfce);
    }
}

static int zpn_fonn_client_exporter_schedule_mtunnel_req(struct zpn_fohh_client_exporter *zfce, struct zpn_fohh_client_exporter_mt *mt)
{
    struct zfce_pending_mt_req *pmr;
    int res;
    char *mtunnel_id = mt->mtunnel_id ? mt->mtunnel_id : "<unknown>";

    pmr = ZPN_CALLOC(sizeof(struct zfce_pending_mt_req));
    if (!pmr) {
        ZPN_LOG(AL_NOTICE, "%s: Cannot allocate pending mt req", mt->dbg_str);
        return ZPN_RESULT_NO_MEMORY;
    }

    pmr->mt = mt;

    ZPATH_MUTEX_LOCK(&(zfce->lock), __FILE__, __LINE__);
    TAILQ_INSERT_TAIL(&(zfce->pending_mt_reqs), pmr, queue_entry);
    ZPATH_MUTEX_UNLOCK(&(zfce->lock), __FILE__, __LINE__);

    ZPN_DEBUG_MTUNNEL("%s: mtunnel_id = %s   Scheduled a mt_req", mt->dbg_str, mtunnel_id);

    if (zfce->zfc && zfce->zfc->conn) {
#if EXPORTER_USE_ZEVENT
        res = fohh_thread_call_zevent(fohh_connection_get_thread_id(zfce->zfc->conn),
                                      zpn_fohh_client_exporter_try_send_mt_requests,
                                      zfce,
                                      fohh_connection_incarnation(zfce->zfc->conn));
#else
        res = fohh_thread_call(fohh_connection_get_thread_id(zfce->zfc->conn),
                               zpn_fohh_client_exporter_try_send_mt_requests,
                               zfce,
                               fohh_connection_incarnation(zfce->zfc->conn));
#endif
        if (res) {
            ZPN_LOG(AL_CRITICAL, "%s: Cannot make fohh_thread_call for zpn_fohh_client_exporter_status_reporting!", mt->dbg_str);
        }
    } else {
        ZPN_DEBUG_MTUNNEL("%s: No zfc or zfv conn for scheduling a mt_req", mt->dbg_str);
    }

    return ZPN_RESULT_NO_ERROR;
}

static struct zpn_fohh_client_exporter *zpn_fohh_client_exporter_get_by_id(const char *auth_token)
{
    struct zpn_fohh_client_exporter *exporter = NULL;

    ZPATH_MUTEX_LOCK(&local_lock, __FILE__, __LINE__);
    exporter = argo_hash_lookup(exporter_by_id, auth_token, strlen(auth_token), NULL);
    ZPATH_MUTEX_UNLOCK(&local_lock, __FILE__, __LINE__);
    ZPN_LOG(AL_INFO, "SESS_PROC returning exporter from hash table %p customer_id = %.*s", exporter, ZPATH_DEBUG_BYTES, auth_token);
    return exporter;
}

static int zpn_fohh_client_exporter_store(struct zpn_fohh_client_exporter *exporter, const char *auth_token)
{
    int res = ZPN_RESULT_NO_ERROR;

    ZPATH_MUTEX_LOCK(&local_lock, __FILE__, __LINE__);

    if (!exporter_by_id) {
        exporter_by_id = argo_hash_alloc(8, 1);
        if (!exporter_by_id) {
            ZPN_LOG(AL_ERROR, "Cannot create exporter hash table");
            ZPATH_MUTEX_UNLOCK(&local_lock, __FILE__, __LINE__);
            return ZPN_RESULT_NO_MEMORY;
        }
    }

    res = argo_hash_store(exporter_by_id, auth_token, strlen(auth_token), 0, exporter);
    if (res) {
        ZPN_LOG(AL_ERROR, "Cannot store exporter to hash table, customer_id = %.*s", ZPATH_DEBUG_BYTES, auth_token);
    }

    ZPATH_MUTEX_UNLOCK(&local_lock, __FILE__, __LINE__);
    return res;
}

/* MUST HOLD LOCK WHEN CALLED */
static int zpn_fohh_client_exporter_schedule_mt_terminate(void *cookie, void *object)
{
    struct zpn_fohh_client_exporter *zfce = cookie;
    struct zpn_fohh_client_exporter_mt *mt = object;
    int res;
    char *mtunnel_id = mt->mtunnel_id ? mt->mtunnel_id : "<unknown>";

    ZPN_DEBUG_EXPORTER("%s: mtunnel_id = %s   zpn_fohh_client_exporter_schedule_mt_terminate()", mt->dbg_str, mtunnel_id);

    ZPATH_MUTEX_LOCK(&(mt->lock), __FILE__, __LINE__);
    mt->status = zfce_mt_remote_disconnect;
    if (mt->err) ZPN_FREE(mt->err);
    if (zfce && zfce->status == zfc_auth_fail) {
        mt->err = ZPN_STRDUP(EXPTR_FCONN_AUTH_FAIL, strlen(EXPTR_FCONN_AUTH_FAIL));
    } else {
        mt->err = ZPN_STRDUP(EXPTR_FCONN_GONE, strlen(EXPTR_FCONN_GONE));
    }

    if (zfce && zfce->zfc && zfce->zfc->conn && mt->status_cb && mt->cookie_void && !mt->request_released) {
        if (!mt->release_scheduled) {

#if EXPORTER_USE_ZEVENT
            res = fohh_thread_call_zevent(fohh_connection_get_thread_id(zfce->zfc->conn),
                                          zpn_fohh_client_exporter_mt_release_request_thread_call,
                                          mt,
                                          mt->incarnation);
#else
            res = fohh_thread_call(fohh_connection_get_thread_id(zfce->zfc->conn),
                                   zpn_fohh_client_exporter_mt_release_request_thread_call,
                                   mt,
                                   mt->incarnation);
#endif
            if (res) {
                ZPN_LOG(AL_CRITICAL, "%s: Cannot make fohh_thread_call for zpn_fohh_client_exporter_mt_release_request_thread_call()!", mt->dbg_str);
            } else {
                mt->release_scheduled = 1;
            }
        }
    }
    ZPATH_MUTEX_UNLOCK(&(mt->lock), __FILE__, __LINE__);

    return ZPN_RESULT_NO_ERROR;
}

static int zpn_clientless_app_query_ack_cb(void *argo_cookie_ptr,
                                           void *argo_structure_cookie_ptr,
                                           struct argo_object *object)
{
    struct zpn_fohh_client_exporter *zfce = argo_structure_cookie_ptr;
    struct zpn_clientless_app_query_ack *query_ack = object->base_structure_void;
    struct zfce_app_query *query = NULL;

    if (zpn_debug_get(ZPN_DEBUG_APPLICATION_IDX)) {
        char dump[8000];
        if (argo_object_dump(object, dump, sizeof(dump), NULL, 1) == ARGO_RESULT_NO_ERROR) {
            ZPN_LOG(AL_DEBUG, "%s: Rx: %s", ZFC_DBG(zfce->zfc), dump);
        }
    }

    ZPATH_MUTEX_LOCK(&(zfce->lock), __FILE__, __LINE__);

    query = argo_hash_lookup(zfce->query_by_id,
                             &(query_ack->query_id),
                             sizeof(query_ack->query_id),
                             NULL);
    if (query) {
        argo_hash_remove(zfce->query_by_id,
                         &(query->query_id),
                         sizeof(query->query_id),
                         NULL);
        TAILQ_REMOVE(&zfce->app_query_list, query, queue_entry);
    }

    ZPATH_MUTEX_UNLOCK(&(zfce->lock), __FILE__, __LINE__);

    if (query) {
        if (zfce->app_query_ack_cb) {
            (*zfce->app_query_ack_cb)(query->query_id,
                                      zfce->zfc->customer_id,
                                      zfce->zfc->auth_token,
                                      query->host_name,
                                      query->proto,
                                      query->port,
                                      query->tls,
                                      query->publish_gid,
                                      query_ack->result,
                                      query_ack->reason);
        }
        if (query->host_name) ZPN_FREE(query->host_name);
        ZPN_FREE(query);
    }

    return ZPN_RESULT_NO_ERROR;
}

static void zfce_terminate_mtunnels(struct zpn_fohh_client_exporter *zfce)
{
    /* need to terminate all the mtunnels */
    if(zfce){
        ZPATH_MUTEX_LOCK(&(zfce->lock), __FILE__, __LINE__);
        if(zfce->mtunnel_by_id != NULL){
            int64_t walk_key = 0;
            argo_hash_walk(zfce->mtunnel_by_id,
                           &walk_key,
                           zpn_fohh_client_exporter_schedule_mt_terminate,
                           zfce);
        }
        ZPATH_MUTEX_UNLOCK(&(zfce->lock), __FILE__, __LINE__);
    }
}

static void resume_from_failed_redirect(struct zpn_fohh_client_exporter *zfce)
{
    /* current logic applies to the first redir */
    if (zfce->initial_redir_received) {
        return;
    }

    /* cancel timeout */
    if (zfce->redir_timeout_ev) {
        event_free(zfce->redir_timeout_ev);
        zfce->redir_timeout_ev = NULL;
    }

    /* Preventing first page load interruption due to redirect is best-effort.
     * Not trying again */
    zfce->initial_redir_received = 1;
    zfce->ignore_next_connection_drop = 0;

    switch (zfce->status) {
    case zfc_ready:
        /* resume buffered mtunnel requests using the current broker. */
        zpn_fohh_client_exporter_send_all_mt_requests(zfce);
        zpn_fohh_client_exporter_send_all_app_queries(zfce);
        break;
    case zfc_not_connected:
        zfce_terminate_mtunnels(zfce);
        break;
    default:
        break;
    }
}

static int zpn_fohh_client_exporter_redirect(void *argo_cookie_ptr,
                                               void *argo_structure_cookie_ptr,
                                               struct argo_object *object)
{
    struct zpn_fohh_client_exporter *zfce = argo_structure_cookie_ptr;
    struct fohh_connection *f_conn = zfce->zfc->conn;
    struct zpn_broker_redirect *req = object->base_structure_void;
    int res;

    if (zpn_debug_get(ZPN_DEBUG_EXPORTER_IDX)) {
        char dump[5000];
        if (argo_object_dump(object, dump, sizeof(dump), NULL, 0) == ARGO_RESULT_NO_ERROR) {
            ZPN_LOG(AL_DEBUG, "%s: Rx: %s", ZFC_DBG(zfce->zfc), dump);
        }
    }

    if (!req->broker_count) {
        ZPN_LOG(AL_WARNING, "%s: broker redirect message returned no broker", ZFC_DBG(zfce->zfc));
        resume_from_failed_redirect(zfce);
        return ZPN_RESULT_NO_ERROR;
    }

    switch (g_exporter_redirect_mode_sanitized) {
        case EXPORTER_REDIRECT_MODE_DISABLED:
        {
            /* no redirect */
            ZPN_LOG(AL_INFO, "%s: ignoring redirect message - redirect is disabled", ZFC_DBG(zfce->zfc));
            resume_from_failed_redirect(zfce);
            return ZPN_RESULT_NO_ERROR;
        }
        case EXPORTER_REDIRECT_MODE_FORCE:
        {
            if (!req->force_redirect) {
                ZPN_LOG(AL_INFO, "%s: ignoring redirect message - redirect is optional", ZFC_DBG(zfce->zfc));
                resume_from_failed_redirect(zfce);
                return ZPN_RESULT_NO_ERROR;
            }
            /* else do redirect */
            break;
        }
        case EXPORTER_REDIRECT_MODE_FULL:
        {
            /* do redirect */
            break;
        }
        default:
        {
            /* should not happen with sanitized config */
            ZPN_LOG(AL_ERROR, "Invalid g_exporter_redirect_mode_sanitized = %"PRId64,
                            g_exporter_redirect_mode_sanitized);
            resume_from_failed_redirect(zfce);
            return ZPN_RESULT_NO_ERROR;
        }
    }

    res = fohh_redirect(f_conn, (const char **)req->brokers, req->broker_count, req->timestamp_s, NULL);

    if (res) {
        ZPN_LOG(AL_WARNING, "%s: fohh_redirect failed with %s", ZFC_DBG(zfce->zfc), zpn_error_description_string(res));
        resume_from_failed_redirect(zfce);
        return ZPN_RESULT_NO_ERROR;
    }

    ZPN_DEBUG_EXPORTER("%s is redirected", ZFC_DBG(zfce->zfc));

    /* cancel timeout */
    if (zfce->redir_timeout_ev) {
        event_free(zfce->redir_timeout_ev);
        zfce->redir_timeout_ev = NULL;
    }

    if (!zfce->initial_redir_received) {
        /* fohh_redirect will cause the current connection to be dropped.
        * We will hold outstanding mtunnels when we received that event.
        * Note this works only for the first redirect. */
        zfce->ignore_next_connection_drop = 1;
        zfce->initial_redir_received = 1;
    }
    return ZPN_RESULT_NO_ERROR;
}

static void zpn_fohh_client_exporter_redir_timeout(evutil_socket_t sock, short flags, void *cookie)
{
    struct zpn_fohh_client_exporter *zfce = cookie;

    ZPN_LOG(AL_WARNING, "%s: initial broker redirect message timed out!", ZFC_DBG(zfce->zfc));

    resume_from_failed_redirect(zfce);
}

static int zfce_status_callback(struct zpn_fohh_client *zfc,
                                void *cookie_void,
                                int64_t cookie_int,
                                enum zfc_status status,
                                const char *error_string)
{
    struct zpn_fohh_client_exporter *zfce = (struct zpn_fohh_client_exporter *)cookie_void;
    struct argo_state *argo;
    int res;

    if (!zfc->conn) {
        ZPN_LOG(AL_ERROR, "%s: Connection status: %s with no conn present !", ZFC_DBG(zfc), zfc_status_string(status));
        return ZPN_RESULT_NO_ERROR;
    }

    ZPN_DEBUG_EXPORTER("%s: ZPN Fohh client exporter status now %s. %s", ZFC_DBG(zfc), zfc_status_string(status), error_string ? error_string : "No Error");

    zfce->status = status;

    if (status == zfc_authenticate) {
        argo = fohh_argo_get_rx(zfc->conn);

        /* Register zpn_client_app */
        if ((res = argo_register_structure(argo, zpn_client_app_description, zpn_client_app_cb, zfce))) {
            ZPN_LOG(AL_ERROR, "Could not register zpn_client_app for zc_client for %s", ZFC_DBG(zfc));
            return res;
        }

        /* Register zpn_client_app_complete */
        if ((res = argo_register_structure(argo, zpn_client_app_complete_description, zpn_client_app_complete_cb, zfce))) {
            ZPN_LOG(AL_ERROR, "Could not register zpn_client_complete_app for zc_client for %s", ZFC_DBG(zfc));
            return res;
        }

        /* Register zpn_mtunnel_request_ack */
        if ((res = argo_register_structure(argo, zpn_mtunnel_request_ack_description, zpn_mtunnel_request_ack_cb, zfce))) {
            ZPN_LOG(AL_ERROR, "Could not register zc_authenticate_ack for zc_client for %s", ZFC_DBG(zfc));
            return res;
        }

        /* Register zpn_mtunnel_end */
        if ((res = argo_register_structure(argo, zpn_mtunnel_end_description, zpn_mtunnel_end_cb, zfce))) {
            ZPN_LOG(AL_ERROR, "Could not register zc_authenticate_ack for zc_client for %s", ZFC_DBG(zfc));
            return res;
        }

        /* Register zpn_clientless_app_query_ack */
        if ((res = argo_register_structure(argo, zpn_clientless_app_query_ack_description, zpn_clientless_app_query_ack_cb, zfce))) {
            ZPN_LOG(AL_ERROR, "Could not register zpn_clientless_app_query_ack_cb for zc_client for %s", ZFC_DBG(zfc));
            return res;
        }

        /* Register zpn_exporter_log_data_ack */
        if ((res = argo_register_structure(argo, zpn_exporter_log_data_ack_description, zpn_exporter_log_data_ack_cb, zfce))) {
            ZPN_LOG(AL_ERROR, "Could not register zpn_exporter_log_data_ack for zc_client for %s", ZFC_DBG(zfc));
            return res;
        }


        /* Register zpn_exporter_data_ack_description message */
        if ((res = argo_register_structure(argo, zpn_exporter_data_ack_description, zpn_exporter_data_ack_cb, zfce))) {
            ZPN_LOG(AL_ERROR, "Could not register zpn_exporter_data_ack for zpn_client connection %s", ZFC_DBG(zfc));
            return res;
        }


    } else if (status == zfc_ready) {
        if (g_exporter_redirect_mode_sanitized != EXPORTER_REDIRECT_MODE_DISABLED) {
            /* Register zpn_broker_redirect */
            argo = fohh_argo_get_rx(zfc->conn);
            if ((res = argo_register_structure(argo, zpn_broker_redirect_description, zpn_fohh_client_exporter_redirect, zfce))) {
                ZPN_LOG(AL_ERROR, "%s: register zpn_broker_redirect_description failed with %s", ZFC_DBG(zfc), zpn_error_description_string(res));
                return res;
            }
        }

        if (g_exporter_redirect_mode_sanitized == EXPORTER_REDIRECT_MODE_FULL && !zfce->initial_redir_received) {
            /* Start initial redirect sequence by sending fohh_info and wait for response, or timeout.
            * While this is going on, mtunnel requests are buffered.
            * Note that this works only for initial connection. Once there are on-going requests,
            * we have to drop all the mTunnels on additional redirects (e.g. broker is going away).
            */
            if ((res = fohh_send_info(zfc->conn))) {
                ZPN_LOG(AL_ERROR, "%s: fohh_send_info failed with %s", ZFC_DBG(zfc), zpn_error_description_string(res));
                return res;
            }

            /* start a timer for the initial redirect sequence */
            if (!zfce->redir_timeout_ev) {
                int thread_num = fohh_connection_get_thread_id(zfc->conn);
                struct event_base *base = fohh_get_thread_event_base(thread_num);
                struct timeval tv;
                zfce->redir_timeout_ev = event_new(base,
                                            -1,
                                            0,  /* one-time event */
                                            zpn_fohh_client_exporter_redir_timeout,
                                            zfce);

                tv.tv_sec = EXPORTER_REDIRECT_TIMEOUT_S;
                tv.tv_usec = EXPORTER_REDIRECT_TIMEOUT_US;
                if (event_add(zfce->redir_timeout_ev, &tv)) {
                    ZPN_LOG(AL_ERROR, "%s: Could not activate redirect timeout", ZFC_DBG(zfc));
                    event_free(zfce->redir_timeout_ev);
                    zfce->redir_timeout_ev = NULL;
                }
            }
            ZPN_DEBUG_EXPORTER("%s: zfc_ready, waiting for redir message from broker...", ZFC_DBG(zfc));
        } else {
              zpn_fohh_client_exporter_send_all_mt_requests(zfce);
              zpn_fohh_client_exporter_send_all_app_queries(zfce);
        }
        zpn_fohh_client_exporter_add_monitor_timer(zfce->zfc->conn,
                                                   zfce,
                                                   zpn_fohh_client_exporter_monitor_cb,
                                                   ZFCE_MONITOR_TIMER_S,
                                                   0);
    } else if ((status == zfc_not_connected) || (status == zfc_auth_fail)) {

        if (status == zfc_not_connected && zfce->ignore_next_connection_drop) {
            /* Ignore once due to initial reconnect.
             * We'll get another one if the reconnect does not succeed. */
            ZPN_DEBUG_EXPORTER("%s: zfc_not_connected ignored once while waiting for redir message from broker...", ZFC_DBG(zfc));

            zfce->ignore_next_connection_drop = 0;
            return ZPN_RESULT_NO_ERROR;
        }

        zfce_terminate_mtunnels(zfce);

    } else {
    }

    return ZPN_RESULT_NO_ERROR;
}

struct zpn_fohh_client_exporter *zpn_fohh_client_exporter_get(const char *auth_token)
{
    struct zpn_fohh_client_exporter *zfce = NULL;

    zfce = zpn_fohh_client_exporter_get_by_id(auth_token);
    return zfce;
}

#if 0
static int verify_callback(int preverify_ok, X509_STORE_CTX *ctx)
{
    return 1;
}

int zpn_fohh_client_no_server_cert_verify(struct zpn_fohh_client_exporter *zfce)
{
    if (zfce->ssl_ctx_connector) {
        SSL_CTX_set_verify(zfce->ssl_ctx_connector, SSL_VERIFY_PEER, verify_callback);
    }
    return ZPN_RESULT_NO_ERROR;
}
#endif

struct zpn_fohh_client_exporter *zpn_fohh_client_exporter_create(const char *broker_name,
                                                                 enum zpn_server_type remote_server_type,
                                                                 const char *cloud_root_pem_filename,
                                                                 const char *client_certificate_pem_filename,
                                                                 const char *client_key_pem_filename,
                                                                 const char *cloud_name,
                                                                 int64_t customer_id,
                                                                 const char *assertion_key,
                                                                 const char *assertion,
                                                                 struct argo_inet *public_ip,
                                                                 struct argo_inet *private_ip,
                                                                 int64_t pra_scope_gid,
                                                                 int is_pra_third_party_login,
                                                                 zfce_app_query_ack_callback_f *app_query_ack_cb)
{
    struct zpn_fohh_client_exporter *zfce = NULL;
    int res = ZPN_RESULT_NO_ERROR;
    enum zpn_client_type client_type;
    char lookup_id[1024] = {0};

    if (!broker_name ||
        !client_certificate_pem_filename ||
        !client_key_pem_filename ||
        !cloud_root_pem_filename) return NULL;

    if (remote_server_type == zpn_server_type_broker && !pra_scope_gid) {
        snprintf(lookup_id, 1024, "%s", assertion_key);
    } else if (remote_server_type == zpn_server_type_broker && pra_scope_gid) {
        snprintf(lookup_id, 1024, "%s:%"PRId64"", assertion_key, pra_scope_gid);
    } else {
        snprintf(lookup_id, 1024, "%s:%s", assertion_key, broker_name);
    }

    zfce = zpn_fohh_client_exporter_get_by_id(lookup_id);

    if (zfce) {
        ZPN_DEBUG_EXPORTER("Have exporter , zfce = %p, assertion_key = %s", zfce, assertion_key);
        return zfce;
    }

    ZPN_DEBUG_EXPORTER("%s: Allocate new exporter for %s", broker_name, assertion_key);

    zfce = ZPN_CALLOC(sizeof(struct zpn_fohh_client_exporter));
    if (zfce) {
        zfce->lock = ZPATH_MUTEX_INIT;
        ZPATH_MUTEX_LOCK(&(zfce->lock), __FILE__, __LINE__);
        zfce->remote_server_type = remote_server_type;
        if (!assertion) {
            // FIXME: Assume no auth exporter client, we need to do this better
            client_type = zpn_client_type_exporter_noauth;
        } else {
            client_type = zpn_client_type_exporter;
        }

        zfce->zfc = zpn_fohh_client_init(client_type,
                                         zpn_tunnel_auth_cloud,
                                         NULL,
                                         NULL,
                                         broker_name,
                                         NULL,
                                         assertion,
                                         cloud_root_pem_filename,
                                         client_certificate_pem_filename,
                                         client_key_pem_filename,
                                         NULL,
                                         zfce_status_callback,
                                         zfce,
                                         0,
                                         NULL,
                                         NULL,
                                         NULL,
                                         NULL,
                                         NULL,
                                         NULL,
                                         cloud_name,
                                         customer_id,
                                         assertion_key,
                                         public_ip,
                                         private_ip,
                                         pra_scope_gid,
                                         is_pra_third_party_login);
        if (!zfce->zfc) {
            ZPN_LOG(AL_ERROR, "Cannot create zfc");
            goto fail_free;
        }

        zfce->status = zfc_init;

        zfce->mtunnel_by_id = argo_hash_alloc(7, 1);
        if (!zfce->mtunnel_by_id) {
            ZPN_LOG(AL_ERROR, "Cannot create mtunnel_by_id hash");
            goto fail_free;
        }

        TAILQ_INIT(&(zfce->pending_mt_reqs));
        TAILQ_INIT(&(zfce->pending_proxy_reqs));

        zfce->busy_mt = argo_hash_alloc(7, 1);
        if (!zfce->busy_mt) {
            ZPN_LOG(AL_ERROR, "Cannot create busy_mt hash");
            goto fail_free;
        }

        zfce->idle_mt = argo_hash_alloc(7, 1);
        if (!zfce->idle_mt) {
            ZPN_LOG(AL_ERROR, "Cannot create idle_mt hash");
            goto fail_free;
        }

        TAILQ_INIT(&(zfce->idle_mt_list));

        zfce->query_by_id = argo_hash_alloc(7, 1);
        if (!zfce->query_by_id) {
            ZPN_LOG(AL_ERROR, "Cannot create query id hash");
            goto fail_free;
        }
        TAILQ_INIT(&(zfce->app_query_list));

        zfce->app_query_ack_cb = app_query_ack_cb;
        res = zpn_fohh_client_exporter_store(zfce, (char *)lookup_id);

        if (res) {
            ZPN_LOG(AL_ERROR, "Cannot store zfce");
            goto fail_free;
        }

        zfce->ssl_ctx_connector = fohh_web_client_ssl_ctx_create();
        if (!zfce->ssl_ctx_connector) {
            EXPORTER_GLOBAL_USAGE_STATS_FIELD_INC(exporter_ssl_ctx_create_error);
            ZPN_LOG(AL_ERROR, "%p: Cannot create SSL ctx", zfce);
            goto fail_free;
        } else {
            EXPORTER_GLOBAL_USAGE_STATS_FIELD_INC(exporter_ssl_ctx_create);
        }

// TODO: Following is unused, we can remove in next commit
//      zfce->ssl_ctx_connector_no_verify =  fohh_web_client_ssl_ctx_create_noverify();
//      if (!zfce->ssl_ctx_connector_no_verify) {
//          ZPN_LOG(AL_ERROR, "Cannot create no verify SSL ctx");
//          goto fail_free;
//      }

        EXPORTER_GLOBAL_USAGE_STATS_FIELD_INC(exporter_zfce_conn_create);
        EXPORTER_GLOBAL_USAGE_STATS_FIELD_INC(exporter_zfce_conn);
        ZPATH_MUTEX_UNLOCK(&(zfce->lock), __FILE__, __LINE__);
        return zfce;
    }

fail_free:
    if (zfce) {
        if (zfce->zfc) {
            zpn_fohh_client_fini(zfce->zfc);
            zfce->zfc = NULL;
        }
        if (zfce->mtunnel_by_id) {
            argo_hash_free(zfce->mtunnel_by_id);
            zfce->mtunnel_by_id = NULL;
        }
        if (zfce->busy_mt) {
            argo_hash_free(zfce->busy_mt);
            zfce->busy_mt =  NULL;
        }
        if (zfce->idle_mt) {
            argo_hash_free(zfce->idle_mt);
            zfce->idle_mt = NULL;
        }
        if (zfce->query_by_id) {
            argo_hash_free(zfce->query_by_id);
            zfce->query_by_id = NULL;
        }
        if (zfce->ssl_ctx_connector) {
            SSL_CTX_free(zfce->ssl_ctx_connector);
            zfce->ssl_ctx_connector = NULL;

            EXPORTER_GLOBAL_USAGE_STATS_FIELD_INC(exporter_ssl_ctx_destroy);
        }
        ZPATH_MUTEX_UNLOCK(&(zfce->lock), __FILE__, __LINE__);
// TODO: Following is unused, we can remove in next commit
//      if (zfce->ssl_ctx_connector_no_verify) {
//          SSL_CTX_free(zfce->ssl_ctx_connector_no_verify);
//          zfce->ssl_ctx_connector_no_verify = NULL;
//      }
        ZPN_FREE(zfce);
    }

    EXPORTER_GLOBAL_USAGE_STATS_FIELD_INC(exporter_zfce_conn_create_error);

    return NULL;
}

static void zpn_fohh_client_exporter_destroy(struct zpn_fohh_client_exporter *zfce)
{
    int32_t destroy_count;

    if(!zfce || !zfce->zfc || !zfce->zfc->conn) {
        ZPN_LOG(AL_ERROR, "fohh client exporter cleanup: NULL argument");
        return;
    }

    ZPATH_MUTEX_LOCK(&(zfce->lock), __FILE__, __LINE__);

    ZPN_DEBUG_EXPORTER("%s: zpn_fohh_client_exporter_destroy()", fohh_description(zfce->zfc->conn));

    destroy_count = __sync_add_and_fetch_4(&(zfce->destroy_count), 1);
    if (destroy_count > 1) {

        destroy_count = __sync_sub_and_fetch_4(&(zfce->destroy_count), 1);
        ZPN_LOG(AL_WARNING, "%s: zpn_fohh_client_exporter_destroy already active, destroy_count: %d, skipping destroy",
                fohh_description(zfce->zfc->conn), destroy_count);
        ZPATH_MUTEX_UNLOCK(&(zfce->lock), __FILE__, __LINE__);
        return;
    } else {
        ZPN_LOG(AL_INFO, "%s: zpn_fohh_client_exporter_destroy, destroy_count: %d, allowing destroy",
                fohh_description(zfce->zfc->conn), destroy_count);
    }

    /* Destroy any pending app queries */
    ZPN_LOG(AL_ERROR, "%s: Implement me: destroy pending app queries", fohh_description(zfce->zfc->conn));

    /* When we come here, there should be no mtunnels left */
    if (zfce->zfc) {
        zpn_fohh_client_fini(zfce->zfc);
        zfce->zfc = NULL;
    }
    if (zfce->mtunnel_by_id) {
        argo_hash_free(zfce->mtunnel_by_id);
        zfce->mtunnel_by_id = NULL;
    }
    if (zfce->busy_mt) {
        argo_hash_free(zfce->busy_mt);
        zfce->busy_mt = NULL;
    }
    if (zfce->idle_mt) {
        argo_hash_free(zfce->idle_mt);
        zfce->idle_mt = NULL;
    }
    if (zfce->query_by_id) {
        argo_hash_free(zfce->query_by_id);
        zfce->query_by_id = NULL;
    }

    if (zfce->ssl_ctx_connector) {
        ZPN_LOG(AL_DEBUG, "freeing ssl context: %p, ssl_ctx_ref_count: %ld", zfce->ssl_ctx_connector, (long)zfce->ssl_ctx_ref_count);
        SSL_CTX_free(zfce->ssl_ctx_connector);
        zfce->ssl_ctx_connector = NULL;

        EXPORTER_GLOBAL_USAGE_STATS_FIELD_INC(exporter_ssl_ctx_destroy);
    }

// TODO: Following is unused, may be removed in next commit
//  if (zfce->ssl_ctx_connector_no_verify) {
//      SSL_CTX_free(zfce->ssl_ctx_connector_no_verify);
//      zfce->ssl_ctx_connector_no_verify = NULL;
//  }
    if (zfce->monitor_ev) {
        event_free(zfce->monitor_ev);
        zfce->monitor_ev = NULL;
    }

    if (zfce->redir_timeout_ev) {
        event_free(zfce->redir_timeout_ev);
        zfce->redir_timeout_ev = NULL;
    }

    ZPATH_MUTEX_UNLOCK(&(zfce->lock), __FILE__, __LINE__);

    ZPN_FREE(zfce);

    EXPORTER_GLOBAL_USAGE_STATS_FIELD_INC(exporter_zfce_conn_destroy);
    EXPORTER_GLOBAL_USAGE_STATS_FIELD_DEC(exporter_zfce_conn);
}

static int zpn_fohh_client_exporter_send_all_app_queries(struct zpn_fohh_client_exporter *zfce)
{
    struct zfce_app_query *query = NULL;
    struct zpn_clientless_app_query app_query;

    ZPATH_MUTEX_LOCK(&(zfce->lock), __FILE__, __LINE__);

    TAILQ_FOREACH(query, &(zfce->app_query_list), queue_entry) {
        if (query->sent) {
            break;
        }

        memset(&app_query, 0, sizeof(struct zpn_clientless_app_query));
        app_query.query_id = query->query_id;
        app_query.app_name = query->host_name;
        app_query.tcp_server_port = query->port;
        app_query.ip_protocol = query->proto;
        app_query.server_port = query->port;
        app_query.publish_gid = query->publish_gid;

        zpn_send_zpn_clientless_app_query(zfce->zfc->conn,
                                          fohh_connection_incarnation(zfce->zfc->conn),
                                          &app_query);
        query->sent = 1;
    }

    ZPATH_MUTEX_UNLOCK(&(zfce->lock), __FILE__, __LINE__);

    return ZPN_RESULT_NO_ERROR;
}

#if EXPORTER_USE_ZEVENT
static void zpn_fohh_client_exporter_try_send_app_query(struct zevent_base *zbase, void *cookie, int64_t int_cookie)
#else
static void zpn_fohh_client_exporter_try_send_app_query(struct fohh_thread *thread, void *cookie, int64_t int_cookie)
#endif
{
    struct zpn_fohh_client_exporter *zfce = cookie;

    if (zfce->status == zfc_ready) {
        zpn_fohh_client_exporter_send_all_app_queries(zfce);
    }
}

int zpn_fohh_client_app_query(struct zpn_fohh_client_exporter *zfce,
                              int64_t query_id,
                              const char *hostname,
                              int proto,
                              uint16_t port,
                              int tls,
                              int64_t publish_gid)
{
    struct zfce_app_query *query = NULL;
    int res;

    ZPN_DEBUG_EXPORTER("zpn_fohh_client_app_query()");

    query = ZPN_CALLOC(sizeof(struct zfce_app_query));
    if (!query) {
        ZPN_LOG(AL_NOTICE, "Cannot allocate memory for app query");
        return ZPN_RESULT_NO_MEMORY;
    }

    query->query_id = query_id;
    query->host_name = ZPN_STRDUP(hostname, strlen(hostname));
    if (!query->host_name) {
        ZPN_LOG(AL_NOTICE, "Cannot allocate memory for app query hostname");
        ZPN_FREE(query);
        return ZPN_RESULT_NO_MEMORY;
    }
    query->proto = proto;
    query->port = port;
    query->tls = tls;
    query->publish_gid = publish_gid;

    ZPATH_MUTEX_LOCK(&(zfce->lock), __FILE__, __LINE__);

    res = argo_hash_store(zfce->query_by_id,
                          &query->query_id,
                          sizeof(query->query_id),
                          0,
                          query);
    if (res) {
        ZPN_LOG(AL_NOTICE, "Cannot store app query to hash");
        ZPN_FREE(query->host_name);
        ZPN_FREE(query);
        ZPATH_MUTEX_UNLOCK(&(zfce->lock), __FILE__, __LINE__);
        return res;
    }

    TAILQ_INSERT_HEAD(&(zfce->app_query_list), query, queue_entry);

    ZPATH_MUTEX_UNLOCK(&(zfce->lock), __FILE__, __LINE__);

    if (zfce->zfc && zfce->zfc->conn) {
#if EXPORTER_USE_ZEVENT
        res = fohh_thread_call_zevent(fohh_connection_get_thread_id(zfce->zfc->conn),
                                      zpn_fohh_client_exporter_try_send_app_query,
                                      zfce,
                                      fohh_connection_incarnation(zfce->zfc->conn));
#else
        res = fohh_thread_call(fohh_connection_get_thread_id(zfce->zfc->conn),
                               zpn_fohh_client_exporter_try_send_app_query,
                               zfce,
                               fohh_connection_incarnation(zfce->zfc->conn));
#endif
        if (res) {
            ZPN_LOG(AL_CRITICAL, "Cannot make fohh_thread_call for zpn_fohh_client_exporter_try_send_app_query!");
        }
    } else {
        ZPN_DEBUG_EXPORTER("No zfc or zfv conn for scheduling a app query");
    }

    return ZPN_RESULT_NO_ERROR;
}

/*************************************************************************************************
 *
 *  zfce_mt stuff
 *
 *************************************************************************************************/

const char *zfce_mt_status_string(enum zpn_fohh_client_exporter_mt_status status)
{
    switch (status) {
    case zfce_mt_connecting:
        return "zfce_mt_connecting";
    case zfce_mt_connected:
        return "zfce_mt_connected";
    case zfce_mt_remote_disconnect:
        return "zfce_mt_remote_disconnect";
    case zfce_mt_connect_error:
        return "zfce_mt_connect_error";
    case zfce_mt_release_request:
        return "zfce_mt_release_request";
    case zfce_mt_request_error:
        return "zfce_mt_request_error";
    default:
        return "ERROR";
    }
}

/*************************************************************************************************
 *  zfce_mt local owner
 */
int zpn_mconn_fohh_client_exporter_init(struct zpn_mconn_fohh_client_exporter *mconn_parser, void *mconn_self)
{
    return zpn_mconn_init(&(mconn_parser->mconn), mconn_self, mconn_fohh_tlv_c);
}

static int zpn_mconn_fohh_client_exporter_receive(struct zpn_mconn_fohh_client_exporter *mconn_parser,
                                                 struct evbuffer *buf,
                                                 size_t len)
{
    struct zpn_mconn *mconn = &(mconn_parser->mconn);
    int res = ZPN_RESULT_NO_ERROR;

    if (mconn->global_owner) {
        (mconn->global_owner_calls->lock)(mconn,
                                          mconn->self,
                                          mconn->global_owner,
                                          mconn->global_owner_key,
                                          mconn->global_owner_key_length);
    } else {
        return res;
    }

    if (mconn_parser->rx_paused) {
        res = ZPN_RESULT_WOULD_BLOCK;
        goto exit;
    }

    if (len) {
        res = zpn_client_process_rx_data(mconn, buf, evbuffer_get_length(buf), NULL, NULL);
        if (res) {
            ZPN_DEBUG_MCONN("Process_rx data returned %s", zpn_result_string(res));
        }
    }

exit:
    if (mconn->global_owner) {
        (mconn->global_owner_calls->unlock)(mconn,
                                            mconn->self,
                                            mconn->global_owner,
                                            mconn->global_owner_key,
                                            mconn->global_owner_key_length);
    }

    return res;
}

static int zpn_mconn_fohh_client_exporter_bind_cb(void *mconn_base,
                                                  void *mconn_self,
                                                  void *owner,
                                                  void *owner_key,
                                                  size_t owner_key_length,
                                                  int64_t *owner_incarnation)
{
    return ZPN_RESULT_NO_ERROR;
}

static int zpn_mconn_fohh_client_exporter_unbind_cb(void *mconn_base,
                                                    void *mconn_self,
                                                    void *owner,
                                                    void *owner_key,
                                                    size_t owner_key_length,
                                                    int64_t owner_incarnation,
                                                    int drop_buffered_data,
                                                    int dont_propagate,
                                                    const char *err)
{
    return ZPN_RESULT_NO_ERROR;
}

static void zpn_mconn_fohh_client_exporter_lock_cb(void *mconn_base,
                                                   void *mconn_self,
                                                   void *owner,
                                                   void *owner_key,
                                                   size_t owner_key_length)
{
}


static void zpn_mconn_fohh_client_exporter_unlock_cb(void *mconn_base,
                                                     void *mconn_self,
                                                     void *owner,
                                                     void *owner_key,
                                                     size_t owner_key_length)
{
}

static int zpn_mconn_fohh_client_exporter_transmit_cb(void *mconn_base,
                                                      void *mconn_self,
                                                      void *owner,
                                                      void *owner_key,
                                                      size_t owner_key_length,
                                                      int64_t owner_incarnation,
                                                      int fohh_thread_id,
                                                      struct evbuffer *buf,
                                                      size_t buf_len)
{
    struct zpn_mconn_fohh_client_exporter *mconn_parser = mconn_base;
    struct zpn_mconn *mconn = &(mconn_parser->mconn);
    struct zpn_fohh_client_exporter_mt *mt = mconn->global_owner;
    int res = ZPN_RESULT_NO_ERROR;
    int need_lock = 0;

    ZPN_DEBUG_MCONN("%s: Send data. Len = %ld, buf_len = %ld", mt ? mt->dbg_str : "no mt", (long) evbuffer_get_length(buf), (long) buf_len);

    if (mconn->global_owner) {
        if (need_lock && mconn->global_owner_calls) {
            (mconn->global_owner_calls->lock)(mconn,
                                              mconn->self,
                                              mconn->global_owner,
                                              mconn->global_owner_key,
                                              mconn->global_owner_key_length);
        }
    } else {
        return res;
    }

    if (!mt->busy) {
        ZPN_DEBUG_MCONN("%s: Nobody is expecting data from us", mt ? mt->dbg_str : "no mt");
        goto exit;
    }

    if (mconn_parser->tx_paused) {
        ZPN_DEBUG_MCONN("%s: tx_paused?", mt ? mt->dbg_str : "no mt");
        goto exit;
    }

    if (buf && buf_len) {
        mt = mconn->global_owner;

        if (mt && mt->consume_cb && mt->cookie_void && !mt->request_released) {
            size_t pre_len, post_len;
            int enq_len;

            pre_len = evbuffer_get_length(buf);
            res = (*mt->consume_cb)(mt, buf, buf_len, mt->cookie_void, mt->cookie_int);
            post_len = evbuffer_get_length(buf);

            enq_len = pre_len - post_len;
            mconn->bytes_to_client += enq_len;
            if (enq_len && mconn->peer) {
                zpn_mconn_client_window_update(mconn->peer, 0, enq_len, 0);
            }

            if (mconn->client_needs_to_forward && post_len == 0) {
                struct zpn_fohh_client_exporter *zfce = ((struct zpn_fohh_client_exporter_mt *)mt)->state;
                if (zfce && is_remote_fin_propogation_enabled(zfce->zfc->customer_id)){
                    mconn->client_needs_to_forward = 0;
                    zpn_mconn_forward_mtunnel_end(mconn, MT_CLOSED_TERMINATED, 0);
                    ZPN_DEBUG_MCONN("%s: fin propagate to parser mconn", mt->dbg_str);
                }
            }
        } else {
            ZPN_DEBUG_MCONN("No mt or consume_cb or cookie_void, released_cookie?, mt = %p, consume_cb = %p, cookie_void = %p, released_cookie = %d",
                            mt, mt->consume_cb, mt->cookie_void, mt->request_released);
        }
    }

exit:
    if (need_lock && mconn->global_owner_calls) {
        (mconn->global_owner_calls->unlock)(mconn,
                                            mconn->self,
                                            mconn->global_owner,
                                            mconn->global_owner_key,
                                            mconn->global_owner_key_length);
    }

    return res;
}

static int zpn_mconn_fohh_client_exporter_pause_cb(void *mconn_base,
                                                   void *mconn_self,
                                                   void *owner,
                                                   void *owner_key,
                                                   size_t owner_key_length,
                                                   int64_t owner_incarnation,
                                                   int fohh_thread_id)
{
    struct zpn_mconn_fohh_client_exporter *mconn_parser = mconn_base;
    struct zpn_mconn *mconn = &(mconn_parser->mconn);
    struct zpn_fohh_client_exporter_mt *mt = mconn->global_owner;

    if (mt->busy) {
        mconn_parser->rx_paused = 1;
    }

    return ZPN_RESULT_NO_ERROR;
}

#if EXPORTER_USE_ZEVENT
static void zpn_fohh_client_exporter_resume_parser(struct zevent_base *zbase, void *cookie, int64_t int_cookie)
#else
static void zpn_fohh_client_exporter_resume_parser(struct fohh_thread *thread, void *cookie, int64_t int_cookie)
#endif
{
    struct zpn_fohh_client_exporter_mt *mt = cookie;

    if (mt && mt->unblock_cb && mt->cookie_void && !mt->request_released) {
        (*mt->unblock_cb)(mt, mt->cookie_void, mt->cookie_int);
    }
}

static int zpn_mconn_fohh_client_exporter_resume_cb(void *mconn_base,
                                                    void *mconn_self,
                                                    void *owner,
                                                    void *owner_key,
                                                    size_t owner_key_length,
                                                    int64_t owner_incarnation,
                                                    int fohh_thread_id)
{
    struct zpn_mconn_fohh_client_exporter *mconn_parser = mconn_base;
    struct zpn_mconn *mconn = &(mconn_parser->mconn);
    struct zpn_fohh_client_exporter_mt *mt = mconn->global_owner;
    int res;

    mconn_parser->rx_paused = 0;

    if (mt) {
        struct zpn_fohh_client_exporter *zfce = ((struct zpn_fohh_client_exporter_mt *)mt)->state;

        /* Don't do anything if we are not being used by the Parser */
        if (!mt->busy) {
            return ZPN_RESULT_NO_ERROR;
        }

        if (zfce && zfce->zfc && zfce->zfc->conn) {
#if EXPORTER_USE_ZEVENT
            res = fohh_thread_call_zevent(fohh_connection_get_thread_id(zfce->zfc->conn),
                                          zpn_fohh_client_exporter_resume_parser,
                                          mt,
                                          0);
#else
            res = fohh_thread_call(fohh_connection_get_thread_id(zfce->zfc->conn),
                                   zpn_fohh_client_exporter_resume_parser,
                                   mt,
                                   0);
#endif
            if (res) {
                ZPN_LOG(AL_CRITICAL, "Cannot make fohh_thread_call for zpn_fohh_client_exporter_resume_parser!");
            }
        }
    }

    return ZPN_RESULT_NO_ERROR;
}

static int zpn_mconn_fohh_client_exporter_disable_read_cb(void *mconn_base,
                                                   void *mconn_self,
                                                   void *owner,
                                                   void *owner_key,
                                                   size_t owner_key_length,
                                                   int64_t owner_incarnation,
                                                   int fohh_thread_id)
{
    return ZPN_RESULT_NO_ERROR;
}

static int zpn_mconn_fohh_client_exporter_enable_read_cb(void *mconn_base,
                                                    void *mconn_self,
                                                    void *owner,
                                                    void *owner_key,
                                                    size_t owner_key_length,
                                                    int64_t owner_incarnation,
                                                    int fohh_thread_id)
{
    return ZPN_RESULT_NO_ERROR;
}

int is_remote_fin_propogation_enabled (int64_t customer_gid)
{

   int64_t config_value = 0;
   zpath_config_override_get_config_int (CONFIG_FEATURE_EXPORTER_PROPAGATE_FIN_FROM_REMOTE,
                                         &config_value,
                                         DEFAULT_CONFIG_FEATURE_PROPAGATE_FIN_FROM_REMOTE,
                                         customer_gid,
                                         ZPATH_INSTANCE_GID,
                                         (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                         0);

   return config_value ? 1 : 0;
}

int zpn_mconn_fohh_client_exporter_forward_tunnel_end_cb(void *mconn_base,
                                                         void *mconn_self,
                                                         void *owner,
                                                         void *owner_key,
                                                         size_t owner_key_length,
                                                         int64_t owner_incarnation,
                                                         const char *err,
                                                         int32_t drop_data)
{
    struct zpn_mconn_fohh_client_exporter *mconn_parser = mconn_base;
    struct zpn_mconn *mconn = &(mconn_parser->mconn);
    struct zpn_fohh_client_exporter_mt *mt = mconn->global_owner;

    /*
     * There are two code paths to get here
     * 1. Through mtunnel received from f_conn connected to broker. In this case
     *    mt->status already set properly
     * 2. Through connector error for TLS connections. This is usually indicate
     *    TLS handshake failure. In this case, mt->status is still zfce_mt_connected,
     *    we need to set it properly here
     */

    if (drop_data || mt->is_guac_mt) {
        int need_deleted = 0;
        struct zpn_connector_tun *conn = (struct zpn_connector_tun *) mt->connector;
        struct zpn_mconn_bufferevent *peer_b = (struct zpn_mconn_bufferevent *)mconn->peer;
        char err_str[1024];

        /* Get any SSL related error string */
        err_str[0] = 0;

        if (conn && conn->ssl_status.err) {
            ZPN_DEBUG_MTUNNEL("%s:%s: Mtunnel TLS certificate failure was %s, where Subject=%s, Issuer=%s", mt->dbg_str, mt->mtunnel_id,
                    conn->ssl_status.x509_err, conn->ssl_status.err_subject, conn->ssl_status.err_issuer);

            switch (conn->ssl_status.err) {
                /* CA trust issues */
                case X509_V_ERR_CERT_UNTRUSTED: //fallthrough
                case X509_V_ERR_DEPTH_ZERO_SELF_SIGNED_CERT: //fallthrough
                case X509_V_ERR_INVALID_CA:
                    sxprintf(err_str, err_str + sizeof(err_str), "%s, Certificate Error: %s, Subject=%s, Issuer=%s",
                            EXPTR_MT_TLS_SETUP_FAIL_NOT_TRUSTED_CA, conn->ssl_status.x509_err, conn->ssl_status.err_subject, conn->ssl_status.err_issuer);
                    break;
                /* cert chaining issues */
                case X509_V_ERR_SELF_SIGNED_CERT_IN_CHAIN: //falthrough
                case X509_V_ERR_CERT_CHAIN_TOO_LONG:
                    sxprintf(err_str, err_str + sizeof(err_str), "%s, Certificate Error: %s, Subject=%s, Issuer=%s",
                            EXPTR_MT_TLS_SETUP_FAIL_CERT_CHAIN_ISSUE, conn->ssl_status.x509_err, conn->ssl_status.err_subject, conn->ssl_status.err_issuer);
                /* other issues */
                default:
                    sxprintf(err_str, err_str + sizeof(err_str), "%s, Certificate Error: %s, Subject=%s, Issuer=%s",
                            EXPTR_MT_TLS_SETUP_FAIL, conn->ssl_status.x509_err, conn->ssl_status.err_subject, conn->ssl_status.err_issuer);
                    break;
            }
        } else if (peer_b && peer_b->ssl_erk) {
            char dbg_buf[256];

            dbg_buf[0] = 0;
            ERR_error_string_n(peer_b->ssl_erk, dbg_buf, sizeof(dbg_buf));
            switch (peer_b->ssl_erk) {
                /* SSL Version Error */
                case SSL_R_WRONG_VERSION_NUMBER:
                    sxprintf(err_str, err_str + sizeof(err_str), "%s,%s", EXPTR_MT_TLS_SETUP_FAIL_VERSION_MISMATCH, dbg_buf);
                    break;
                /* other issues */
                default:
                    if (mt->err && (strstr(mt->err, "BRK_MT_TERMINATED_APPROVAL_TIMEOUT") != NULL)) {
                        sxprintf(err_str, err_str + sizeof(err_str), "%s,%s", BRK_MT_TERMINATED_APPROVAL_TIMEOUT, dbg_buf);
                    } else if (mt->err && (strstr(mt->err, "TIMEOUT") != NULL)) {
                        sxprintf(err_str, err_str + sizeof(err_str), "%s,%s", EXPTR_MT_TERMINATED_TIMEOUT, dbg_buf);
                    } else {
                        sxprintf(err_str, err_str + sizeof(err_str), "%s,%s", EXPTR_MT_TLS_SETUP_FAIL_PEER, dbg_buf);
                    }
                    break;
            }
        }

        if (strlen(err_str)) {
            ZPN_DEBUG_MTUNNEL("%s:%s: Mtunnel setup failure due to TLS setup with remote server failed, err = %s", mt->dbg_str, mt->mtunnel_id, err);
            mt->status = zfce_mt_connect_error;
            if (mt->err) ZPN_FREE(mt->err);
            mt->err = ZPN_STRDUP(err_str, strlen(err_str));
        }

        /* Force destroy, so call status_cb */
        mt->deleting = 1;
        if (!mt->request_released) {
            mt->request_released = 1;
            need_deleted = 1;
        }

        if (need_deleted && mt->status_cb && mt->cookie_void) {
            ZPN_DEBUG_MTUNNEL("%s:%s: calling status callback to report remote disconnect", mt->dbg_str, mt->mtunnel_id);
            (*mt->status_cb)(mt, mt->cookie_void, mt->cookie_int, mt->status, mt->err);
        }
    } else {
        /*
         * Normal disconnect, Http response messages with response type 'TE: chunked' or no content-Length
           will send the FIN to indicate the EOF. needs to prapogate fin.
         */
        struct zpn_fohh_client_exporter *zfce = ((struct zpn_fohh_client_exporter_mt *)mt)->state;
        if (is_remote_fin_propogation_enabled(zfce->zfc->customer_id) && mt->status_cb && mt->cookie_void) {
            (*mt->status_cb)(mt, mt->cookie_void, mt->cookie_int, mt->status, "MT_CLOSED_TERMINATED");
        }
    }

    return ZPN_RESULT_NO_ERROR;
}

void zpn_mconn_fohh_client_exporter_window_update_cb(void *mconn_base,
                                                     void *mconn_self,
                                                     void *owner,
                                                     void *owner_key,
                                                     size_t owner_key_length,
                                                     int64_t owner_incarnation,
                                                     int fohh_thread_id,
                                                     int tx_len,
                                                     int batch_win_upd)
{
    struct zpn_mconn_fohh_client_exporter *mconn_parser = mconn_base;
    struct zpn_mconn *mconn = &(mconn_parser->mconn);
    struct zpn_fohh_client_exporter_mt *mt = mconn->global_owner;
    int res;

    if (mt) {
        struct zpn_fohh_client_exporter *zfce = ((struct zpn_fohh_client_exporter_mt *)mt)->state;

        /* Don't do anything if we are not being used by the Parser */
        if (!mt->busy) {
            return;
        }

        if (zfce && zfce->zfc && zfce->zfc->conn) {
#if EXPORTER_USE_ZEVENT
            res = fohh_thread_call_zevent(fohh_connection_get_thread_id(zfce->zfc->conn),
                                          zpn_fohh_client_exporter_resume_parser,
                                          mt,
                                          0);
#else
            res = fohh_thread_call(fohh_connection_get_thread_id(zfce->zfc->conn),
                                   zpn_fohh_client_exporter_resume_parser,
                                   mt,
                                   0);
#endif
            if (res) {
                ZPN_LOG(AL_CRITICAL, "Cannot make fohh_thread_call for zpn_fohh_client_exporter_resume_parser!");
            }
        }
    }

    return;
}

void zpn_mconn_fohh_client_exporter_stats_update_cb(void *mconn_base,
                                                    void *mconn_self,
                                                    void *owner,
                                                    void *owner_key,
                                                    size_t owner_key_length,
                                                    int64_t owner_incarnation,
                                                    int fohh_thread_id,
                                                    enum zpn_mconn_stats stats_name)
{
    return;
}

const struct zpn_mconn_local_owner_calls mconn_fohh_client_exporter_calls = {
    zpn_mconn_fohh_client_exporter_bind_cb,
    zpn_mconn_fohh_client_exporter_unbind_cb,
    zpn_mconn_fohh_client_exporter_lock_cb,
    zpn_mconn_fohh_client_exporter_unlock_cb,
    zpn_mconn_fohh_client_exporter_transmit_cb,
    zpn_mconn_fohh_client_exporter_pause_cb,
    zpn_mconn_fohh_client_exporter_resume_cb,
    zpn_mconn_fohh_client_exporter_forward_tunnel_end_cb,
    zpn_mconn_fohh_client_exporter_window_update_cb,
    zpn_mconn_fohh_client_exporter_stats_update_cb,
    zpn_mconn_fohh_client_exporter_disable_read_cb,
    zpn_mconn_fohh_client_exporter_enable_read_cb
};

/*************************************************************************************************
 *  zfce_mt global owner
 */

static int global_bind_broker_cb(void *mconn_base,
                                 void *mconn_self,
                                 void *global_owner,
                                 void *global_owner_key,
                                 size_t global_owner_key_length,
                                 int64_t *global_owner_incarnation)
{
    struct zpn_fohh_client_exporter_mt *mt = mconn_self;
    struct zpn_fohh_client_exporter *zfce = ((struct zpn_fohh_client_exporter_mt *)mt)->state;
    int res;

    ZPATH_MUTEX_LOCK(&(zfce->lock), __FILE__, __LINE__);
    res = argo_hash_store(zfce->mtunnel_by_id,
                          global_owner_key,
                          global_owner_key_length,
                          0,
                          mt);
    ZPATH_MUTEX_UNLOCK(&(zfce->lock), __FILE__, __LINE__);

    *global_owner_incarnation = mt->incarnation;

    return res;
}

static int global_unbind_broker_cb(void *mconn_base,
                                   void *mconn_self,
                                   void *global_owner,
                                   void *global_owner_key,
                                   size_t global_owner_key_length,
                                   int64_t global_owner_incarnation,
                                   int drop_buffered_data,
                                   int dont_propagate,
                                   const char *err)
{
    struct zpn_fohh_client_exporter_mt *mt = mconn_self;
    struct zpn_fohh_client_exporter *zfce = ((struct zpn_fohh_client_exporter_mt *)mt)->state;
    int res;

    ZPATH_MUTEX_LOCK(&(zfce->lock), __FILE__, __LINE__);
    res = argo_hash_remove(zfce->mtunnel_by_id,
                           global_owner_key,
                           global_owner_key_length,
                           NULL);
    ZPATH_MUTEX_UNLOCK(&(zfce->lock), __FILE__, __LINE__);

    return res;
}

static int global_bind_parser_cb(void *mconn_base,
                                 void *mconn_self,
                                 void *global_owner,
                                 void *global_owner_key,
                                 size_t global_owner_key_length,
                                 int64_t *global_owner_incarnation)
{
    struct zpn_fohh_client_exporter_mt *mt = mconn_self;

    *global_owner_incarnation = mt->incarnation;
    return ZPN_RESULT_NO_ERROR;
}

static int global_unbind_parser_cb(void *mconn_base,
                                   void *mconn_self,
                                   void *global_owner,
                                   void *global_owner_key,
                                   size_t global_owner_key_length,
                                   int64_t global_owner_incarnation,
                                   int drop_buffered_data,
                                   int dont_propagate,
                                   const char *err)
{
    return ZPN_RESULT_NO_ERROR;
}

static void global_lock_cb(void *mconn_base,
                           void *mconn_self,
                           void *global_owner,
                           void *global_owner_key,
                           size_t global_owner_key_length)
{
    struct zpn_fohh_client_exporter_mt *mt = mconn_self;

    ZPATH_MUTEX_LOCK(&(mt->lock), __FILE__, __LINE__);

    return;
}

static void global_unlock_cb(void *mconn_base,
                             void *mconn_self,
                             void *global_owner,
                             void *global_owner_key,
                             size_t global_owner_key_length)
{
    struct zpn_fohh_client_exporter_mt *mt = mconn_self;

    ZPATH_MUTEX_UNLOCK(&(mt->lock), __FILE__, __LINE__);

    return;
}

static void global_terminate_cb(void *mconn_base,
                                void *mconn_self,
                                void *global_owner,
                                void *global_owner_key,
                                size_t global_owner_key_length,
                                char *error)
{
}

static int global_ip_proto_cb(void *mconn_base,
                               void *mconn_self,
                               void *global_owner,
                               void *global_owner_key,
                               size_t global_owner_key_length)
{
    struct zpn_fohh_client_exporter_mt *mt = mconn_self;

    return mt->key.proto;
}

static int global_double_encrypt_cb(void *mconn_base,
                                    void *mconn_self,
                                    void *global_owner,
                                    void *global_owner_key,
                                    size_t global_owner_key_length)
{
    return 0;
}

static struct zpn_mconn *global_outer_mconn_cb(void *mconn_base,
                                               void *mconn_self,
                                               void *global_owner,
                                               void *global_owner_key,
                                               size_t global_owner_key_length)
{
    struct zpn_fohh_client_exporter_mt *mt = mconn_self;
    struct zpn_mconn *outer_mconn = (struct zpn_mconn *)mconn_base;

    if (mt->key.tls && mt->connector) {
        struct zpn_connector_tun *conn = (struct zpn_connector_tun *)mt->connector;

        if (mconn_base == &conn->mconn_c.mconn) {
            outer_mconn = &(mt->broker_mconn.mconn);
        } else if (mconn_base == &conn->mconn_s.mconn) {
            outer_mconn = &(mt->parser_mconn.mconn);
        }
    }

    return (struct zpn_mconn *)outer_mconn;
}

static int64_t global_incarnation_cb(void *mconn_base,
                                     void *mconn_self,
                                     void *global_owner,
                                     void *global_owner_key,
                                     size_t global_owner_key_length)
{
    struct zpn_fohh_client_exporter_mt *mt = mconn_self;

    return mt->incarnation;
}

static int global_validate_incarnation_cb(void *mconn_base,
                                          void *mconn_self,
                                          void *global_owner,
                                          void *global_owner_key,
                                          size_t global_owner_key_length,
                                          int64_t original_incarnation)
{
    struct zpn_fohh_client_exporter_mt *mt = mconn_self;

    if (mt->incarnation == original_incarnation) {
        return 1;
    } else {
        return 0;
    }
}

static char *global_mtunnel_id_cb(void *mconn_base,
                                          void *mconn_self,
                                          void *global_owner,
                                          void *global_owner_key,
                                          size_t global_owner_key_length)
{
    struct zpn_fohh_client_exporter_mt *mt = mconn_self;

    return mt->mtunnel_id;
}

struct zpn_mconn_global_owner_calls zfce_mt_global_call_set_broker = {
    global_bind_broker_cb,
    global_unbind_broker_cb,
    global_lock_cb,
    global_unlock_cb,
    global_terminate_cb,
    global_ip_proto_cb,
    global_double_encrypt_cb,
    global_outer_mconn_cb,
    global_incarnation_cb,
    global_validate_incarnation_cb,
    global_mtunnel_id_cb,
    global_get_peer_no_op,
    global_get_customer_gid_no_op
};

struct zpn_mconn_global_owner_calls zfce_mt_global_call_set_parser = {
    global_bind_parser_cb,
    global_unbind_parser_cb,
    global_lock_cb,
    global_unlock_cb,
    global_terminate_cb,
    global_ip_proto_cb,
    global_double_encrypt_cb,
    global_outer_mconn_cb,
    global_incarnation_cb,
    global_validate_incarnation_cb,
    global_mtunnel_id_cb,
    global_get_peer_no_op,
    global_get_customer_gid_no_op
};

/*************************************************************************************************
 *  zfce_mt API
 */

static void zpn_fohh_client_export_mt_set_key(struct zfce_mt_key  *key,
                                              char *host_name,
                                              int proto,
                                              uint16_t port,
                                              int tls,
                                              int verify_cert,
                                              int32_t exporter_conn_id)
{
    memset(key, 0, sizeof(struct zfce_mt_key));

    snprintf(key->host_name, sizeof(key->host_name), "%s", host_name);
    key->proto = proto;
    key->port = port;
    key->tls = tls;
    key->verify_cert = verify_cert;
    key->exporter_conn_id = exporter_conn_id;
}

static int zpn_fohh_client_exporter_mt_destroy(struct zpn_fohh_client_exporter_mt *mt, char *err)
{
    int res = ZPN_RESULT_NO_ERROR;
    char *mtunnel_id = mt->mtunnel_id ? mt->mtunnel_id : "<unkbown>";

    ZPATH_MUTEX_LOCK(&(mt->lock), __FILE__, __LINE__);

    ZPN_DEBUG_MTUNNEL("%s: mtunnel_id = %s  Terminating exporter mt", mt->dbg_str, mtunnel_id);

    res = zpn_mconn_terminate(&(mt->broker_mconn.mconn), 1, 0, err, NULL);
    if (res) {
        ZPN_LOG(AL_ERROR, "%s: mtunnel_id = %s  Terminating exporter broker mconn returned %s", mt->dbg_str, mtunnel_id, zpn_result_string(res));
    }

    if (mt->key.tls && mt->connector) {
        res = zpn_mconn_terminate(&(mt->parser_mconn.mconn), 1, 0, err, NULL);
        if (res) {
            ZPN_LOG(AL_ERROR, "%s: mtunnel_id = %s  Terminating exporter parser mconn returned %s", mt->dbg_str, mtunnel_id, zpn_result_string(res));
        }
    }

    if (mt->mtunnel_id) {
        ZPN_FREE(mt->mtunnel_id);
        mt->mtunnel_id = NULL;
    }
    if (mt->err) {
        ZPN_FREE(mt->err);
        mt->err = NULL;
    }

    if (mt->console_user) {
        ZPN_FREE(mt->console_user);
        mt->console_user = NULL;
    }

    if (mt->file_transfer_list) {
        ZPN_FREE(mt->file_transfer_list);
        mt->file_transfer_list = NULL;
    }
    if (mt->guac_error_string) {
        ZPN_FREE(mt->guac_error_string);
        mt->guac_error_string = NULL;
    }

    if (mt->console_conn_type){
        ZPN_FREE(mt->console_conn_type);
        mt->console_conn_type = NULL;
    }
    if (mt->session_recording) {
        ZPN_FREE(mt->session_recording);
        mt->session_recording = NULL;
    }
    if (mt->pra_conn_id){
        ZPN_FREE(mt->pra_conn_id);
        mt->pra_conn_id = NULL;
    }
    if (mt->credential_id) {
        ZPN_FREE(mt->credential_id);
        mt->credential_id = NULL;
    }
    if (mt->credential_pool_id) {
        ZPN_FREE(mt->credential_pool_id);
        mt->credential_pool_id = NULL;
    }
    if (mt->shared_users_list) {
        ZPN_FREE(mt->shared_users_list);
        mt->shared_users_list = NULL;
    }
    if (mt->shared_mode) {
        ZPN_FREE(mt->shared_mode);
        mt->shared_mode = NULL;
    }
    if (mt->user_email) {
        ZPN_FREE(mt->user_email);
        mt->user_email = NULL;
    }

    if (mt->gposture_object) {
        argo_object_release(mt->gposture_object);
        mt->gposture_object = NULL;
    }

    if (mt->stepup_auth_level_id) {
        ZPN_FREE_AND_NULL(mt->stepup_auth_level_id);
    }

    if (mt->gposture) {
        mt->gposture = NULL;
    }

    if (mt->gprofiles) {
        if (mt->gprofiles->gprofile_gids) {
            ZPN_FREE(mt->gprofiles->gprofile_gids);
            mt->gprofiles->gprofile_gids = NULL;
        }
        ZPN_FREE(mt->gprofiles);
        mt->gprofiles = NULL;
    }

    // Count mtunnels destroyed
    EXPORTER_GLOBAL_USAGE_STATS_FIELD_INC(exporter_mt_destroy);
    EXPORTER_GLOBAL_USAGE_STATS_FIELD_DEC(exporter_mt);

    ZPATH_MUTEX_UNLOCK(&(mt->lock), __FILE__, __LINE__);

    zpn_fohh_client_exporter_mt_reap(mt);

    return res;
}

struct zpn_fohh_client_exporter_mt *zpn_fohh_client_export_mt_get(const char *dbg_str,
                                                                  struct zpn_fohh_client_exporter *zfce,
                                                                  char *host_name,
                                                                  int proto,
                                                                  uint16_t port,
                                                                  int tls,
                                                                  zfce_mt_status_callback_f *status_cb,
                                                                  zfce_mt_consume_callback_f *consume_cb,
                                                                  zfce_mt_unblock_callback_f *unblock_cb,
                                                                  zfce_mt_trans_log_callback_f *trans_log_cb,
                                                                  void *cookie_void,
                                                                  int64_t cookie_int,
                                                                  int64_t publish_gid,
                                                                  int verify_cert,
                                                                  int32_t exporter_conn_id,
                                                                  int no_mt_reuse,
                                                                  int is_mt_upgraded,
                                                                  int is_guac_mt,
                                                                  int is_guac_proxy_conn,
                                                                  struct argo_object **gposture_object,
                                                                  struct zpn_managed_browser_profiles **gprofiles)
{
    struct zpn_fohh_client_exporter_mt *mt = NULL;
    struct zfce_mt_key  key;
    struct zfce_mt_key  key1;
    int res;

    ZPN_DEBUG_EXPORTER("zpn_fohh_client_export_mt_get(): %s, hostname=%s, proto=%d, port=%d, tls=%d, verify_cert=%d, exporter_conn_id=%d",
                       dbg_str, host_name, proto, port, tls, verify_cert, exporter_conn_id);

    if (!host_name) {
        ZPN_LOG(AL_INFO, "%s: Trying to get broker, but doesnot supply host_name", dbg_str);
        return NULL;
    }

    if (strlen(host_name) > (EXPORTER_MAX_HOSTNAME_LEN - 1)) {
        ZPN_LOG(AL_INFO, "%s: Too big domain name = %s", dbg_str, host_name);
        return NULL;
    }

    zpn_fohh_client_export_mt_set_key(&key, host_name, proto, port, tls, verify_cert, exporter_conn_id);

    ZPATH_MUTEX_LOCK(&(zfce->lock), __FILE__, __LINE__);

    /* This fixes ET-92513, we are in race between zfce destroy & zfce access to get mt. */
    if (__sync_fetch_and_or(&zfce->destroy_count, 0) > 0) {
        ZPN_LOG(AL_ERROR, "zfce connection destroy is already initiated, hostname=%s, exporter_conn_id=%d", host_name, exporter_conn_id);
        ZPATH_MUTEX_UNLOCK(&(zfce->lock), __FILE__, __LINE__);
        return NULL;
    }

    /* We become busy, reset the idle start time */
    zfce->idle_start_us = 0;

    mt = argo_hash_lookup(zfce->idle_mt, &key, sizeof(struct zfce_mt_key), NULL);

    if (mt && is_mt_upgraded) {
        ZPATH_MUTEX_LOCK(&(mt->lock), __FILE__, __LINE__);
        ZPN_DEBUG_EXPORTER("%s: Mtunnel upgraded, mtunnel_id = %s", dbg_str, mt->mtunnel_id);
        mt->is_mt_upgraded = 1;
        ZPATH_MUTEX_UNLOCK(&(mt->lock), __FILE__, __LINE__);
    }

    if (mt && (mt->status == zfce_mt_connected)) {
        /* Found an existing mtunnel in idle queue */
        argo_hash_remove(zfce->idle_mt,
                         &mt->key,
                         sizeof(struct zfce_mt_key),
                         mt);

        TAILQ_REMOVE(&zfce->idle_mt_list, mt, queue_entry);

        zfce->num_idle_mt--;

        res = argo_hash_store(zfce->busy_mt,
                              &mt->key,
                              sizeof(struct zfce_mt_key),
                              1,
                              mt);
        if (res == ARGO_RESULT_NO_MEMORY) {
            ZPN_LOG(AL_ERROR, "%s: Cannot store mtunnel in busy hash?", dbg_str);
            EXPORTER_ZFCE_STATS_FIELD_INC(zfce->mt_destroy_count, 1);
            ZPATH_MUTEX_UNLOCK(&(zfce->lock), __FILE__, __LINE__);
            zpn_fohh_client_exporter_mt_destroy(mt, MT_CLOSED_INTERNAL_ERROR);
            return NULL;
        }

        zfce->num_busy_mt++;

        ZPATH_MUTEX_UNLOCK(&(zfce->lock), __FILE__, __LINE__);

        mt->status_cb = status_cb;
        mt->consume_cb = consume_cb;
        mt->unblock_cb = unblock_cb;
        mt->trans_log_cb = trans_log_cb;
        mt->cookie_void = cookie_void;
        mt->cookie_int = cookie_int;
        mt->publish_gid = publish_gid;
        mt->busy = 1;
        mt->idle_start_us = 0;

        ZPATH_MUTEX_LOCK(&(mt->lock), __FILE__, __LINE__);
        if (gposture_object && *gposture_object) {
            mt->gposture_object = argo_object_copy(*gposture_object);
            mt->gposture = mt->gposture_object->base_structure_void;
        }

        if (gprofiles && *gprofiles) {
            if ((*gprofiles)->gprofile_gid_count) {
                mt->gprofiles = (struct zpn_managed_browser_profiles *)ZPN_CALLOC(sizeof(struct zpn_managed_browser_profiles));
                if (mt->gprofiles == NULL) {
                    ZPATH_MUTEX_UNLOCK(&(mt->lock), __FILE__, __LINE__);
                    MT_GPROFILE_CLEANUP(mt, zfce, dbg_str, MT_CLOSED_INTERNAL_ERROR);
                    return NULL;
                }
                mt->gprofiles->gprofile_gid_count = (*gprofiles)->gprofile_gid_count;
                mt->gprofiles->managed_browser_payload_version =  (*gprofiles)->managed_browser_payload_version;
                mt->gprofiles->gprofile_gids = (int64_t *)ZPN_CALLOC(sizeof(int64_t) * mt->gprofiles->gprofile_gid_count);
                if (mt->gprofiles->gprofile_gids == NULL) {
                    ZPATH_MUTEX_UNLOCK(&(mt->lock), __FILE__, __LINE__);
                    MT_GPROFILE_CLEANUP(mt, zfce, dbg_str, MT_CLOSED_INTERNAL_ERROR);
                    return NULL;
                }
                memcpy(mt->gprofiles->gprofile_gids, (*gprofiles)->gprofile_gids, sizeof(int64_t) * mt->gprofiles->gprofile_gid_count);
            }
        }

        snprintf(mt->dbg_str, sizeof(mt->dbg_str), "%s", dbg_str);
        ZPATH_MUTEX_UNLOCK(&(mt->lock), __FILE__, __LINE__);
        mt->is_guac_mt = is_guac_mt;
        mt->is_guac_proxy_conn = is_guac_proxy_conn;

        ZPN_DEBUG_EXPORTER("%s: %s: Got mtunnel from idle queue, tag_id = %d", dbg_str, mt->mtunnel_id ? mt->mtunnel_id : "<unknown mtunnel id>", mt->tag_id);

        return mt;
    }

    /* We cannot find mt with the correct exporter_conn_id,
     * try to find any non-nailed up idle mt to the app,
     * unless configuration disallows reusing of non-nailed connections.
     */
    if (exporter_conn_id && !no_mt_reuse) {
        zpn_fohh_client_export_mt_set_key(&key1, host_name, proto, port, tls, verify_cert, 0);
        mt = argo_hash_lookup(zfce->idle_mt, &key1, sizeof(struct zfce_mt_key), NULL);
        if (mt && (mt->status == zfce_mt_connected)) {

            /* Found an existing mtunnel in idle queue, but it is not nailed up */
            argo_hash_remove(zfce->idle_mt,
                             &mt->key,
                             sizeof(struct zfce_mt_key),
                             mt);

            TAILQ_REMOVE(&zfce->idle_mt_list, mt, queue_entry);

            zfce->num_idle_mt--;

            /* Change it to new key */
            mt->key = key;

            res = argo_hash_store(zfce->busy_mt,
                                  &mt->key,
                                  sizeof(struct zfce_mt_key),
                                  1,
                                  mt);
            if (res == ARGO_RESULT_NO_MEMORY) {
                ZPN_LOG(AL_ERROR, "%s: Cannot store mtunnel in busy hash?", dbg_str);
                EXPORTER_ZFCE_STATS_FIELD_INC(zfce->mt_destroy_count, 1);
                ZPATH_MUTEX_UNLOCK(&(zfce->lock), __FILE__, __LINE__);
                zpn_fohh_client_exporter_mt_destroy(mt, MT_CLOSED_INTERNAL_ERROR);
                return NULL;
            }

            zfce->num_busy_mt++;

            ZPATH_MUTEX_UNLOCK(&(zfce->lock), __FILE__, __LINE__);

            mt->status_cb = status_cb;
            mt->consume_cb = consume_cb;
            mt->unblock_cb = unblock_cb;
            mt->trans_log_cb = trans_log_cb;
            mt->cookie_void = cookie_void;
            mt->cookie_int = cookie_int;
            mt->publish_gid = publish_gid;
            mt->busy = 1;
            mt->idle_start_us = 0;

            ZPATH_MUTEX_LOCK(&(mt->lock), __FILE__, __LINE__);
            if (gposture_object && *gposture_object) {
                argo_object_hold(*gposture_object);
                mt->gposture_object = *gposture_object;
                mt->gposture = (*gposture_object)->base_structure_void;
            }

            if (gprofiles && *gprofiles) {
                if ((*gprofiles)->gprofile_gid_count) {
                    mt->gprofiles = (struct zpn_managed_browser_profiles *)ZPN_CALLOC(sizeof(struct zpn_managed_browser_profiles));
                    if (mt->gprofiles == NULL) {
                        ZPATH_MUTEX_UNLOCK(&(mt->lock), __FILE__, __LINE__);
                        MT_GPROFILE_CLEANUP(mt, zfce, dbg_str, MT_CLOSED_INTERNAL_ERROR);
                        return NULL;
                    }
                    mt->gprofiles->gprofile_gid_count = (*gprofiles)->gprofile_gid_count;
                    mt->gprofiles->managed_browser_payload_version =  (*gprofiles)->managed_browser_payload_version;
                    mt->gprofiles->gprofile_gids = (int64_t *)ZPN_CALLOC(sizeof(int64_t) * mt->gprofiles->gprofile_gid_count);
                    if (mt->gprofiles->gprofile_gids == NULL) {
                        ZPATH_MUTEX_UNLOCK(&(mt->lock), __FILE__, __LINE__);
                        MT_GPROFILE_CLEANUP(mt, zfce, dbg_str, MT_CLOSED_INTERNAL_ERROR);
                        return NULL;
                    }
                    memcpy(mt->gprofiles->gprofile_gids, (*gprofiles)->gprofile_gids, sizeof(int64_t) * mt->gprofiles->gprofile_gid_count);
                }
            }

            snprintf(mt->dbg_str, sizeof(mt->dbg_str), "%s", dbg_str);
            ZPATH_MUTEX_UNLOCK(&(mt->lock), __FILE__, __LINE__);
            mt->exporter_conn_id = exporter_conn_id;
            mt->is_guac_mt = is_guac_mt;

            ZPN_DEBUG_EXPORTER("%s: %s: Got mtunnel from idle queue, tag_id = %d", dbg_str, mt->mtunnel_id ? mt->mtunnel_id : "<unknown mtunnel id>", mt->tag_id);

            return mt;
        }
    }

    ZPATH_MUTEX_UNLOCK(&(zfce->lock), __FILE__, __LINE__);

    /* We cannot find in idle queue, so need to allocate new ones */

    ZPN_DEBUG_EXPORTER("%s: Cannot get mtunnel from idle queue, need to create one", dbg_str);
    mt = zpn_fohh_client_exporter_mt_allocate();

    if (mt) {
        int32_t tag_id;

        ZPN_DEBUG_EXPORTER("%s: Initialize created mt", dbg_str);

        mt->lock = ZPATH_MUTEX_INIT;

        zpn_fohh_client_export_mt_set_key(&mt->key, host_name, proto, port, tls, verify_cert, exporter_conn_id);

        tag_id = zpn_fohh_client_next_tag_id(zfce->zfc);
        mt->tag_id = tag_id;
        mt->state = zfce;
        mt->status = zfce_mt_connecting;
        mt->status_cb = status_cb;
        mt->consume_cb = consume_cb;
        mt->unblock_cb = unblock_cb;
        mt->trans_log_cb = trans_log_cb;
        mt->cookie_void = cookie_void;
        mt->cookie_int = cookie_int;
        mt->publish_gid = publish_gid;
        mt->verify_cert = verify_cert ? 1 : 0;
        mt->verify_host = mt->verify_cert;
        mt->busy = 1;
        mt->idle_start_us = 0;

        ZPATH_MUTEX_LOCK(&(mt->lock), __FILE__, __LINE__);
        if (gposture_object && *gposture_object) {
            argo_object_hold(*gposture_object);
            mt->gposture_object = *gposture_object;
            mt->gposture = (*gposture_object)->base_structure_void;
        }

        if (gprofiles && *gprofiles) {
            if ((*gprofiles)->gprofile_gid_count) {
                mt->gprofiles = (struct zpn_managed_browser_profiles *)ZPN_CALLOC(sizeof(struct zpn_managed_browser_profiles));
                if (mt->gprofiles == NULL) {
                    ZPATH_MUTEX_UNLOCK(&(mt->lock), __FILE__, __LINE__);
                    MT_GPROFILE_CLEANUP(mt, zfce, dbg_str, MT_CLOSED_INTERNAL_ERROR);
                    return NULL;
                }
                mt->gprofiles->gprofile_gid_count = (*gprofiles)->gprofile_gid_count;
                mt->gprofiles->managed_browser_payload_version =  (*gprofiles)->managed_browser_payload_version;
                mt->gprofiles->gprofile_gids = (int64_t *)ZPN_CALLOC(sizeof(int64_t) * mt->gprofiles->gprofile_gid_count);
                if (mt->gprofiles->gprofile_gids == NULL) {
                    ZPATH_MUTEX_UNLOCK(&(mt->lock), __FILE__, __LINE__);
                    MT_GPROFILE_CLEANUP(mt, zfce, dbg_str, MT_CLOSED_INTERNAL_ERROR);
                    return NULL;
                }
                memcpy(mt->gprofiles->gprofile_gids, (*gprofiles)->gprofile_gids, sizeof(int64_t) * mt->gprofiles->gprofile_gid_count);
            }
        }

        snprintf(mt->dbg_str, sizeof(mt->dbg_str), "%s", dbg_str);
        ZPATH_MUTEX_UNLOCK(&(mt->lock), __FILE__, __LINE__);
        mt->exporter_conn_id = exporter_conn_id;
        mt->is_guac_mt = is_guac_mt;

        ZPATH_MUTEX_LOCK(&(zfce->lock), __FILE__, __LINE__);

        EXPORTER_GLOBAL_USAGE_STATS_FIELD_INC(exporter_mt_create);
        EXPORTER_GLOBAL_USAGE_STATS_FIELD_INC(exporter_mt);
        EXPORTER_ZFCE_STATS_FIELD_INC(mt->state->mt_create_count, 1);

        res = argo_hash_store(zfce->busy_mt,
                              &mt->key,
                              sizeof(struct zfce_mt_key),
                              1,
                              mt);
        if (res == ARGO_RESULT_NO_MEMORY) {
            ZPN_LOG(AL_ERROR, "%s: Cannot store mtunnel in busy hash?", dbg_str);
            ZPATH_MUTEX_UNLOCK(&(zfce->lock), __FILE__, __LINE__);
            if (gposture_object && *gposture_object)
                argo_object_release(*gposture_object);
            zpn_fohh_client_exporter_mt_reap(mt);
            return NULL;
        }

        zfce->num_busy_mt++;

        ZPATH_MUTEX_UNLOCK(&(zfce->lock), __FILE__, __LINE__);

        ZPN_DEBUG_EXPORTER("%s: Adding global owners", dbg_str);

        zpn_mconn_fohh_tlv_init(&(mt->broker_mconn), mt, mconn_fohh_tlv_c);
        res = zpn_mconn_add_global_owner(&(mt->broker_mconn.mconn),
                                         0,
                                         mt,
                                         &(mt->tag_id),
                                         sizeof(tag_id),
                                         &zfce_mt_global_call_set_broker);
        if (res) {
            ZPN_LOG(AL_ERROR, "%s: Failed to add global owner", dbg_str);
        }

        zpn_mconn_fohh_client_exporter_init(&(mt->parser_mconn), mt);
        res = zpn_mconn_add_global_owner(&(mt->parser_mconn.mconn),
                                         0,
                                         mt,
                                         &(mt->tag_id),
                                         sizeof(tag_id),
                                         &zfce_mt_global_call_set_parser);
        if (res) {
            ZPN_LOG(AL_ERROR, "%s: Failed to add global owner", dbg_str);
        }

        res = zpn_fonn_client_exporter_schedule_mtunnel_req(zfce, mt);

        /* Race zfce->zfc dbg_str with ack */
        ZPATH_MUTEX_LOCK(&(zfce->lock), __FILE__, __LINE__);
        ZPN_DEBUG_MTUNNEL("%s: %s: create mt = %p, tag_id = %d", dbg_str, ZFC_DBG(zfce->zfc), mt, mt->tag_id);
        ZPATH_MUTEX_UNLOCK(&(zfce->lock), __FILE__, __LINE__);

        return mt;
    }

    ZPN_LOG(AL_ERROR, "%s: Cannot allocate new mtunnel", dbg_str);

    return NULL;
}

static int zpn_fohh_client_exporter_mt_busy_to_idle_list(struct zpn_fohh_client_exporter_mt *mt, int zfce_locked)
{
    struct zpn_fohh_client_exporter *zfce = mt->state;
    int res;

    ZPATH_MUTEX_LOCK(&(mt->lock), __FILE__, __LINE__);

    ZPN_DEBUG_EXPORTER("%s: %s: zpn_fohh_client_exporter_mt_busy_to_idle_list()", mt->dbg_str, mt->mtunnel_id);

    mt->is_guac_mt = 0;
    mt->cookie_void = NULL;
    mt->cookie_int = 0;
    mt->idle_start_us = epoch_us();
    mt->deleting = 0;
    mt->request_released = 0;
    mt->request_done = 0;
    mt->release_scheduled = 0;
    mt->publish_gid = 0;
    mt->busy = 0;
    if (mt->console_user) {
        ZPN_FREE(mt->console_user);
        mt->console_user = NULL;
    }
    if (mt->file_transfer_list) {
        ZPN_FREE(mt->file_transfer_list);
        mt->file_transfer_list = NULL;
    }
    if (mt->guac_error_string) {
        ZPN_FREE(mt->guac_error_string);
        mt->guac_error_string = NULL;
    }
    if (mt->console_conn_type){
        ZPN_FREE(mt->console_conn_type);
        mt->console_conn_type = NULL;
    }
    if (mt->session_recording) {
        ZPN_FREE(mt->session_recording);
        mt->session_recording = NULL;
    }
    if (mt->pra_conn_id){
        ZPN_FREE(mt->pra_conn_id);
        mt->pra_conn_id = NULL;
    }
    if (mt->shared_users_list) {
        ZPN_FREE(mt->shared_users_list);
        mt->shared_users_list = NULL;
    }
    if (mt->shared_mode) {
        ZPN_FREE(mt->shared_mode);
        mt->shared_mode = NULL;
    }
    if (mt->user_email) {
        ZPN_FREE(mt->user_email);
        mt->user_email = NULL;
    }

    ZPATH_MUTEX_UNLOCK(&(mt->lock), __FILE__, __LINE__);

    /* Move from busy list to idle list */

    if (!zfce_locked) ZPATH_MUTEX_LOCK(&(zfce->lock), __FILE__, __LINE__);

    TAILQ_INSERT_TAIL(&zfce->idle_mt_list, mt, queue_entry);

    res = argo_hash_remove(zfce->busy_mt,
                           &mt->key,
                           sizeof(struct zfce_mt_key),
                           mt);
    if (res == ARGO_RESULT_NOT_FOUND) {
        ZPN_LOG(AL_ERROR, "Releasing mtunnel not in busy hash?");
        if (!zfce_locked) ZPATH_MUTEX_UNLOCK(&(zfce->lock), __FILE__, __LINE__);
        return res;
    }

    zfce->num_busy_mt--;

    res = argo_hash_store(zfce->idle_mt,
                          &mt->key,
                          sizeof(struct zfce_mt_key),
                          1,
                          mt);
    if (res == ARGO_RESULT_NO_MEMORY) {
        ZPN_LOG(AL_ERROR, "Cannot store mtunnel in idle hash?");
        EXPORTER_ZFCE_STATS_FIELD_INC(zfce->mt_destroy_count, 1);
        if (!zfce_locked) ZPATH_MUTEX_UNLOCK(&(zfce->lock), __FILE__, __LINE__);
        zpn_fohh_client_exporter_mt_destroy(mt, MT_CLOSED_INTERNAL_ERROR);
        return res;
    }

    zfce->num_idle_mt++;

    if (!zfce_locked) ZPATH_MUTEX_UNLOCK(&(zfce->lock), __FILE__, __LINE__);

    return ZPATH_RESULT_NO_ERROR;
}


static int zpn_fohh_client_exporter_mt_remove_from_busy_list_and_destroy(struct zpn_fohh_client_exporter_mt *mt, int zfce_locked) {
    struct zpn_fohh_client_exporter *zfce = mt->state;
    int res;

    ZPATH_MUTEX_LOCK(&(mt->lock), __FILE__, __LINE__);

    ZPN_DEBUG_EXPORTER("%s: %s: zpn_fohh_client_exporter_mt_remove_from_busy_list_and_destroy()", mt->dbg_str, mt->mtunnel_id);

    mt->is_guac_mt = 0;
    mt->cookie_void = NULL;
    mt->cookie_int = 0;
    mt->idle_start_us = epoch_us();
    mt->deleting = 0;
    mt->request_released = 0;
    mt->request_done = 0;
    mt->release_scheduled = 0;
    mt->publish_gid = 0;
    mt->busy = 0;

    if (mt->console_user) {
        ZPN_FREE(mt->console_user);
        mt->console_user = NULL;
    }
    ZPATH_MUTEX_UNLOCK(&(mt->lock), __FILE__, __LINE__);

    /* Move from busy list and destroy */

    if (!zfce_locked) ZPATH_MUTEX_LOCK(&(zfce->lock), __FILE__, __LINE__);

    res = argo_hash_remove(zfce->busy_mt,
                           &mt->key,
                           sizeof(struct zfce_mt_key),
                           mt);
    if (res == ARGO_RESULT_NOT_FOUND) {
        ZPN_LOG(AL_ERROR, "Releasing mtunnel not in busy hash?");
        if (!zfce_locked) ZPATH_MUTEX_UNLOCK(&(zfce->lock), __FILE__, __LINE__);
        return res;
    }

    zfce->num_busy_mt--;

    if (!zfce_locked) ZPATH_MUTEX_UNLOCK(&(zfce->lock), __FILE__, __LINE__);

    EXPORTER_ZFCE_STATS_FIELD_INC(zfce->mt_destroy_count, 1);
    zpn_fohh_client_exporter_mt_destroy(mt, MT_CLOSED_TERMINATED);

    return ZPATH_RESULT_NO_ERROR;
}

static void zpn_fohh_client_exporter_mt_release_request(struct zpn_fohh_client_exporter_mt *mt)
{
    int move_to_idle = 0;
    int mt_upgraded = 0;
    char *mtunnel_id = mt->mtunnel_id ? mt->mtunnel_id : "<unknown>";

    ZPATH_MUTEX_LOCK(&(mt->lock), __FILE__, __LINE__);

    ZPN_DEBUG_EXPORTER("%s: zpn_fohh_client_exporter_mt_release_request()", mt->dbg_str);

    if (mt->status_cb && mt->cookie_void && !mt->request_released) {
        mt->request_released = 1;
        if (mt->request_done) {
            if (mt->is_mt_upgraded) {
                ZPN_DEBUG_EXPORTER("%s: mtunnel_id = %s  Mtunnel upgraded, cannot reuse so not moving to idle queue", mt->dbg_str, mtunnel_id);
                mt_upgraded = 1;
            } else {
                ZPN_DEBUG_EXPORTER("%s: mtunnel_id = %s  Move mtunnel to idle queue", mt->dbg_str, mtunnel_id);
                move_to_idle = 1;
            }
        }
        if (mt->status == zfce_mt_connected) {
            ZPN_DEBUG_EXPORTER("%s: mtunnel_id = %s  Close mtunnel", mt->dbg_str, mtunnel_id);
            (*mt->status_cb)(mt, mt->cookie_void, mt->cookie_int, zfce_mt_release_request, mt->err);
        } else {
            ZPN_DEBUG_EXPORTER("%s: mtunnel_id = %s  mtunnel is closed, sending status = %d", mt->dbg_str, mtunnel_id, mt->status);
            (*mt->status_cb)(mt, mt->cookie_void, mt->cookie_int, mt->status, mt->err);
        }
    } else {
        ZPN_DEBUG_EXPORTER("%s: mtunnel_id = %s  not need to release request: status_cb = %p, cookie_void = %p, released = %d", mt->dbg_str, mtunnel_id, mt->status_cb, mt->cookie_void, mt->request_released);
    }
    ZPATH_MUTEX_UNLOCK(&(mt->lock), __FILE__, __LINE__);

    if (mt_upgraded) {
        zpn_fohh_client_exporter_mt_remove_from_busy_list_and_destroy(mt, 0);
    } else if (move_to_idle) {
        zpn_fohh_client_exporter_mt_busy_to_idle_list(mt, 0);
    }
}

#if EXPORTER_USE_ZEVENT
static void zpn_fohh_client_exporter_mt_release_request_thread_call(struct zevent_base *zbase, void *cookie, int64_t int_cookie)
#else
static void zpn_fohh_client_exporter_mt_release_request_thread_call(struct fohh_thread *thread, void *cookie, int64_t int_cookie)
#endif
{
    struct zpn_fohh_client_exporter_mt *mt = cookie;

    zpn_fohh_client_exporter_mt_release_request(mt);
}

/* Assume we have the lock of mt when calling this */
static int zpn_fohh_client_exporter_schedule_release_request(struct zpn_fohh_client_exporter_mt *mt)
{
    struct zpn_fohh_client_exporter *zfce = mt->state;
    int res;

    if (zfce && zfce->zfc && zfce->zfc->conn && mt->status_cb && mt->cookie_void && !mt->request_released) {
        if (!mt->release_scheduled) {
#if EXPORTER_USE_ZEVENT
            res = fohh_thread_call_zevent(fohh_connection_get_thread_id(zfce->zfc->conn),
                                          zpn_fohh_client_exporter_mt_release_request_thread_call,
                                          mt,
                                          mt->incarnation);
#else
            res = fohh_thread_call(fohh_connection_get_thread_id(zfce->zfc->conn),
                                   zpn_fohh_client_exporter_mt_release_request_thread_call,
                                   mt,
                                   mt->incarnation);
#endif
            if (res) {
                ZPN_LOG(AL_CRITICAL, "%s: Cannot make fohh_thread_call for zpn_fohh_client_exporter_schedule_release_request_thread_call()!", mt->dbg_str);
            } else {
                mt->release_scheduled = 1;
            }
        }
    }

    return ZPN_RESULT_NO_ERROR;
}

int zpn_fohh_client_export_mt_release(struct zpn_fohh_client_exporter_mt *mt, int http_transaction_error)
{
    int move_to_idle = 0;
    int mt_upgraded = 0;

    if (!mt) {
        return ZPN_RESULT_NO_ERROR;
    }

    char *mtunnel_id = mt->mtunnel_id ? mt->mtunnel_id : "<unknown>";

    ZPATH_MUTEX_LOCK(&(mt->lock), __FILE__, __LINE__);

    ZPN_DEBUG_EXPORTER("%s: mtunnel_id = %s zpn_fohh_client_export_mt_release(), http_transaction_error = %d", mt->dbg_str, mtunnel_id, http_transaction_error);
    mt->request_done = 1;
    if (http_transaction_error) {
        mt->status = zfce_mt_request_error;
    }
    if (!mt->request_released) {
        zpn_fohh_client_exporter_schedule_release_request(mt);
    } else {
        if (mt->is_mt_upgraded) {
            mt_upgraded = 1;
        } else {
            move_to_idle = 1;
        }
    }
    ZPATH_MUTEX_UNLOCK(&(mt->lock), __FILE__, __LINE__);

    if (mt_upgraded) {
        zpn_fohh_client_exporter_mt_remove_from_busy_list_and_destroy(mt, 0);
    } else if (move_to_idle) {
        zpn_fohh_client_exporter_mt_busy_to_idle_list(mt, 0);
    }

    return ZPN_RESULT_NO_ERROR;
}

int zfce_mt_consume(struct zpn_fohh_client_exporter_mt *mt, struct evbuffer *buf, size_t len, int64_t incarnation)
{
    int res = ZPN_RESULT_NO_ERROR;

    if (buf && mt) {
        res = zpn_mconn_fohh_client_exporter_receive(&(mt->parser_mconn), buf, len);
    }

    return res;
}

#if EXPORTER_USE_ZEVENT
static void zpn_fohh_client_exporter_mt_async_transmit(struct zevent_base *zbase, void *cookie, int64_t int_cookie)
#else
static void zpn_fohh_client_exporter_mt_async_transmit(struct fohh_thread *thread, void *cookie, int64_t int_cookie)
#endif
{
    struct zpn_fohh_client_exporter_mt *mt = cookie;
    char *mtunnel_id = mt->mtunnel_id ? mt->mtunnel_id : "<unknown>";

    ZPATH_MUTEX_LOCK(&(mt->lock), __FILE__, __LINE__);

    ZPN_DEBUG_EXPORTER("%s: mtunnel_id = %s  zpn_fohh_client_exporter_mt_async_transmit()", mt->dbg_str, mtunnel_id);

    zpn_client_drain_tx_data(&(mt->parser_mconn.mconn));
    ZPATH_MUTEX_UNLOCK(&(mt->lock), __FILE__, __LINE__);
}

static int zpn_fohh_client_exporter_schedule_mtunnel_transmit(struct zpn_fohh_client_exporter_mt *mt)
{
    struct zpn_fohh_client_exporter *zfce = mt->state;
    int res;
    char *mtunnel_id = mt->mtunnel_id ? mt->mtunnel_id : "<unknown>";

    if (zfce && zfce->zfc && zfce->zfc->conn && mt->status_cb && mt->cookie_void && !mt->request_released) {
#if EXPORTER_USE_ZEVENT
        res = fohh_thread_call_zevent(fohh_connection_get_thread_id(zfce->zfc->conn),
                                      zpn_fohh_client_exporter_mt_async_transmit,
                                      mt,
                                      mt->incarnation);
#else
        res = fohh_thread_call(fohh_connection_get_thread_id(zfce->zfc->conn),
                               zpn_fohh_client_exporter_mt_async_transmit,
                               mt,
                               mt->incarnation);
#endif
        if (res) {
            ZPN_LOG(AL_CRITICAL, "%s: mtunnel_id = %s  Cannot make fohh_thread_call for zpn_fohh_client_exporter_mt_async_transmit()!", mt->dbg_str, mtunnel_id);
        }
    }

    return ZPN_RESULT_NO_ERROR;
}

int zfce_mt_unblock(struct zpn_fohh_client_exporter_mt *mt)
{
    /* Global owner can accept data again */
    if (mt) {
        zpn_fohh_client_exporter_schedule_mtunnel_transmit(mt);
    }
    return ZPN_RESULT_NO_ERROR;
}

/*************************************************************************************************
 *  zfce_mt Object Allocation/Free
 */
static void zpn_fohh_client_exporter_mt_free_q_init()
{
    memset(&free_q, 0, sizeof(free_q));
    free_q.lock = ZPATH_MUTEX_INIT;
    TAILQ_INIT(&(free_q.mt_list));
    TAILQ_INIT(&(free_q.mt_list_with_connector));
    TAILQ_INIT(&(free_q.reap_mt_list));
}

static void zpn_fohh_client_exporter_mt_reap(struct zpn_fohh_client_exporter_mt *mt)
{
    ZPATH_MUTEX_LOCK(&(free_q.lock), __FILE__, __LINE__);
    TAILQ_INSERT_TAIL(&(free_q.reap_mt_list), mt, queue_entry);
    free_q.stats.reap_queue_count++;
    ZPATH_MUTEX_UNLOCK(&(free_q.lock), __FILE__, __LINE__);
}

static struct zpn_fohh_client_exporter_mt *zpn_fohh_client_exporter_mt_allocate(void)
{
    struct zpn_fohh_client_exporter_mt *mt = NULL;
    int64_t incarnation = 0;

    ZPATH_MUTEX_LOCK(&(free_q.lock), __FILE__, __LINE__);

    if ((mt = TAILQ_FIRST(&(free_q.mt_list)))) {
        TAILQ_REMOVE(&(free_q.mt_list), mt, queue_entry);
        free_q.stats.free_queue_count--;
        incarnation = mt->incarnation;
        incarnation++;
        //if (mt->connector) {
            //zpn_connector_destroy(mt->connector);
            //ZPN_FREE(mt->connector);
        //}
        memset(mt, 0, sizeof(struct zpn_fohh_client_exporter_mt));
        mt->incarnation = incarnation;
    } else {
        mt = (struct zpn_fohh_client_exporter_mt *)ZPN_CALLOC(sizeof(struct zpn_fohh_client_exporter_mt));
        if (mt) {
            free_q.stats.allocations++;
            memset(mt, 0, sizeof(struct zpn_fohh_client_exporter_mt));
            mt->lock = ZPATH_MUTEX_INIT;
            mt->incarnation = 1;
        }
    }

    ZPATH_MUTEX_UNLOCK(&(free_q.lock), __FILE__, __LINE__);

    return mt;
}

const char *console_credential_type_string(enum zpn_console_credential_type cred_type)
{
    switch (cred_type) {
    case zpn_console_credential_type_username_password:
        return "Username-Password";
    case zpn_console_credential_type_ssh_key:
        return "SSH Key";
    case zpn_console_credential_type_password:
        return "Password";
    default:
        return "n/a";
    }
}

void zpn_exporter_pra_guac_proxy_data(struct zpn_fohh_client_exporter_mt *mt)
{
    struct zpn_fohh_client_exporter *zfce = NULL;

    if (mt == NULL) {
        return;
    }

    zfce = mt->state;
    if (zfce == NULL || zfce->zfc == NULL) {
        return;
    }

    if (zfce && zfce->zfc && zfce->zfc->conn) {
        ZPATH_MUTEX_LOCK(&(zfce->lock), __FILE__, __LINE__);
        if (zfce->status == zfc_ready) {
            char * mtunnel_id = mt->mtunnel_id ? mt->mtunnel_id : "<unknown>";
            int res = ZPN_RESULT_NO_ERROR;
            res = zpn_send_exporter_guac_proxy_data(&(zfce->zfc->fohh_tlv_state.tlv),
                                                    mt->tag_id,
                                                    mt->pra_conn_id,
                                                    mt->console_user,
                                                    mt->event_type,
                                                    mt->user_email,
                                                    mt->capabilities_policy_id);
            if (res) {
                ZPN_LOG(AL_NOTICE, "%s: mtunnel_id = %s Failed to send out mtunnel exporter guac proxy data", mtunnel_id, mt->dbg_str);
            }
        } else {
            ZPN_LOG(AL_ERROR, "zfce status is not ready for sending exporter guac proxy data");
        }
        ZPATH_MUTEX_UNLOCK(&(zfce->lock), __FILE__, __LINE__);
    }
}
