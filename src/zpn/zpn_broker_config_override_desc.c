/*
 * zpn_broker_config_override_desc.c . Copyright (C) 2023-2025 Zscaler Inc. All Rights Reserved.
 *
 * zpn broker configuration override description registration.
 */

#include "zpn/zpn_broker.h"
#include "zpn/zpn_broker_common.h"
#include "zrdt/zrdt.h"
#include "zpath_lib/zpath_config_override_desc.h"
#include "zpath_lib/zpath_config_override_keys.h"
#include "zpath_lib/zpath_feature_flag_keys.h"
#include "zpn/zpn_broker_config_override_desc.h"
#include "fohh/fohh_log.h"

static struct zpath_config_override_desc zpn_broker_config_override_flags_descriptions_common[] = {
    /* Legacy common config overrides used by Broker and PSE; use separate PSE and SE sections going forward */
    {
        .key                = BROKER_FEATURE_DNS_CHECK_ERROR_DELAY_US,
        .desc               = "Dns check route optimisation",
        .details            = "This config enables broker to optimize the zpn_dns_assistant check route \n"
                              "The zpn_dns_assistant_check is sent to the dispatcher that requested it instead of the entire pool.\n"
                              "Order of check: component id, component grp id, customer id, global\n"
                              "Default: 50*1000",
        .val_type           = config_type_int,
        .component_types    = config_component_broker | config_component_pbroker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_inst_grp | config_target_gid_type_cust | config_target_gid_type_global,
        .int_range_lo       = BROKER_FEATURE_DNS_CHECK_ERROR_DELAY_US_MIN_US,
        .int_range_hi       = BROKER_FEATURE_DNS_CHECK_ERROR_DELAY_US_MAX_US,
        .int_default        = DEFAULT_BROKER_FEATURE_DNS_CHECK_ERROR_DELAY_US,
        .feature_group      = FEATURE_GROUP_BROKER_DNS,
        .value_traits       = config_value_traits_normal,
    },
    {
        .key                = ZPN_BROKER_STEP_UP_AUTH_FEATURE_ENABLED,
        .desc               = "Feature flag to enable or disable the support of Broker Step Up Auth",
        .details            = "1: feature is enabled.\n"
                              "0: feature is disabled.\n"
                              "Order of check: customer id, global\n"
                              "Default: 0, feature is disabled",
        .val_type           = config_type_int,
        .component_types    = config_component_broker | config_component_pbroker,
        .target_gid_types   = config_target_gid_type_cust | config_target_gid_type_global,
        .int_range_lo       = ZPN_BROKER_STEP_UP_AUTH_FEATURE_MIN,
        .int_range_hi       = ZPN_BROKER_STEP_UP_AUTH_FEATURE_MAX,
        .int_default        = ZPN_BROKER_STEP_UP_AUTH_FEATURE_DEFAULT,
        .feature_group      = FEATURE_GROUP_STEPUP_AUTH,
        .value_traits       = config_value_traits_feature_enablement,
    },
    {
        .key                = ZPN_BROKER_STEP_UP_AUTH_FEATURE_HARD_DISABLED,
        .desc               = "Feature flag to hard disable the support of Broker Step Up Auth globally",
        .details            = "feature flag to hard disable the step-up auth globally\n"
                              "Order of check: global\n"
                              "Default: 0, feature is not hard disabled",
        .val_type           = config_type_int,
        .component_types    = config_component_broker | config_component_pbroker,
        .target_gid_types   = config_target_gid_type_global,
        .int_range_lo       = ZPN_BROKER_STEP_UP_AUTH_FEATURE_HARD_DISABLED_MIN,
        .int_range_hi       = ZPN_BROKER_STEP_UP_AUTH_FEATURE_HARD_DISABLED_MAX,
        .int_default        = ZPN_BROKER_STEP_UP_AUTH_FEATURE_HARD_DISABLED_DEFAULT,
        .feature_group      = FEATURE_GROUP_STEPUP_AUTH,
        .value_traits       = config_value_traits_systemwide,
    },
    {
        .key                = ZPN_DISPATCHER_HOLD_TIME,
        .desc               = "Dispatcher alive time",
        .details            = "Do not use this config in production\n"
                              "15 minutes to wait for a dispatcher to be fully 'alive'",
        .val_type           = config_type_int,
        .component_types    = config_component_broker | config_component_pbroker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_global,
        .int_range_lo       = ZPN_DISPATCHER_HOLD_TIME_MIN_SECS,
        .int_range_hi       = ZPN_DISPATCHER_HOLD_TIME_MAX_SECS,
        .int_default        = DEFAULT_ZPN_DISPATCHER_HOLD_TIME,
        .feature_group      = FEATURE_GROUP_BROKER_DISPATCHER_GENERIC,
        .value_traits       = config_value_traits_normal,
    },
    {
        .key                = DTLS_CLIENT_MTU,
        .desc               = DTLS_CLIENT_MTU_DESC,
        .details            = "Order of check: customer gid\n"
                              "default: 1482 bytes (DEFAULT_STREAM_MTU)",
        .val_type           = config_type_int,
        .component_types    = config_component_broker | config_component_pbroker,
        .target_gid_types   = config_target_gid_type_cust,
        .int_range_lo       = LOW_DTLS_MTU,
        .int_range_hi       = HIGH_DTLS_MTU,
        .int_default        = DEFAULT_STREAM_MTU,
        .feature_group      = FEATURE_GROUP_DTLS,
        .value_traits       = config_value_traits_normal,
    },
    {
        .key                = FEATURE_VDI_ENABLED,
        .desc               = "when enabled allows vdi client type connections",
        .details            = "0: vdi client type is not enabled\n"
                              "1: vdi client type is enabled\n"
                              "Order of check: customer gid, global\n"
                              "default: 0",
        .val_type           = config_type_int,
        .component_types    = config_component_broker | config_component_pbroker,
        .target_gid_types   = config_target_gid_type_cust | config_target_gid_type_global,
        .int_range_lo       = 0,
        .int_range_hi       = 1,
        .int_default        = DEFAULT_FEATURE_VDI_ENABLED,
        .feature_group      = FEATURE_GROUP_VDI_ENABLE,
        .value_traits       = config_value_traits_feature_enablement,
    },
    {
        .key                = FEATURE_VDI_HARD_DISABLED,
        .desc               = "when enabled disables vdi client type connections",
        .details            = "0: vdi client type hard disablement is off\n"
                              "1: vdi client type hard disablement is on\n"
                              "Order of check: global\n"
                              "default: 0",
        .val_type           = config_type_int,
        .component_types    = config_component_broker | config_component_pbroker,
        .target_gid_types   = config_target_gid_type_global,
        .int_range_lo       = 0,
        .int_range_hi       = 1,
        .int_default        = DEFAULT_FEATURE_VDI_HARD_DISABLED,
        .feature_group      = FEATURE_GROUP_VDI_ENABLE,
        .value_traits       = config_value_traits_systemwide,
    },
    {
        .key                = BROKER_VDI_CLIENT_STATUS_TIMEOUT_SEC,
        .desc               = "VDI client timeout",
        .details            = "Configure the timeout for vdi client \n"
                                "This disconnects the client if no activity beyond the configured time \n"
                                "Order of check: component id, root customer id, global\n"
                                "Default: 43200 secs",
        .val_type           = config_type_int,
        .component_types    = config_component_broker | config_component_pbroker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_zsroot | config_target_gid_type_global | config_target_gid_type_inst_grp,
        .int_range_lo       = BROKER_VDI_CLIENT_STATUS_TIMEOUT_SEC_MIN,
        .int_range_hi       = BROKER_VDI_CLIENT_STATUS_TIMEOUT_SEC_MAX,
        .int_default        = BROKER_VDI_CLIENT_STATUS_TIMEOUT_DEFAULT_SEC,
        .feature_group      = FEATURE_GROUP_VDI_ENABLE,
        .value_traits       = config_value_traits_normal,
    },
    {
        .key                = CONFIG_FEATURE_EXTRANET_HARD_DISABLED,
        .desc               = "When enabled, disables the extranet feature",
        .details            = "0: extranet feature hard disablement is off\n"
                              "1: extranet feature hard disablement is on\n"
                              "Order of check: global\n"
                              "default: 0",
        .val_type           = config_type_int,
        .component_types    = config_component_broker | config_component_pbroker,
        .target_gid_types   = config_target_gid_type_global,
        .int_range_lo       = CONFIG_FEATURE_EXTRANET_HD_MIN,
        .int_range_hi       = CONFIG_FEATURE_EXTRANET_HD_MAX,
        .int_default        = CONFIG_FEATURE_EXTRANET_HARD_DISABLED_DEFAULT,
        .feature_group      = FEATURE_GROUP_EXTRANET,
        .value_traits       = config_value_traits_systemwide,
    },
    {
        .key                = CONFIG_FEATURE_AAE_PROFILE_FEATURE,
        .desc               = "When enabled allow adapative access policy evaluation",
        .details            = "0: aae feature is disabled\n"
                              "1: aae feature is enabled\n"
                              "Order of check: customer_gid, global\n"
                              "default: 0",
        .val_type           = config_type_int,
        .component_types    = config_component_broker | config_component_pbroker,
        .target_gid_types   = config_target_gid_type_cust | config_target_gid_type_global,
        .int_range_lo       = CONFIG_FEATURE_AAE_PROFILE_FEATURE_VAL_MIN,
        .int_range_hi       = CONFIG_FEATURE_AAE_PROFILE_FEATURE_VAL_MAX,
        .int_default        = CONFIG_FEATURE_AAE_PROFILE_FEATURE_DEFAULT,
        .feature_group      = FEATURE_GROUP_AAE,
        .value_traits       = config_value_traits_feature_enablement,
    },
    {
        .key                = CONFIG_FEATURE_AAE_PROFILE_HARD_DISABLED,
        .desc               = "When enabled, disables the aae feature globally",
        .details            = "0: aae feature hard disablement is off\n"
                              "1: aae feature hard disablement is on\n"
                              "Order of check: global\n"
                              "default: 0",
        .val_type           = config_type_int,
        .component_types    = config_component_broker | config_component_pbroker,
        .target_gid_types   = config_target_gid_type_global,
        .int_range_lo       = CONFIG_FEATURE_AAE_PROFILE_HARD_DISABLED_VAL_MIN,
        .int_range_hi       = CONFIG_FEATURE_AAE_PROFILE_HARD_DISABLED_VAL_MAX,
        .int_default        = CONFIG_FEATURE_AAE_PROFILE_HARD_DISABLED_DEFAULT,
        .feature_group      = FEATURE_GROUP_AAE,
        .value_traits       = config_value_traits_systemwide,
    },
    {
        .key                = CONFIG_FOHH_LOG_MAX_IN_FLIGHT,
        .desc               = "FOHH logging max logs in flight or unacknowledged",
        .details            = "FOHH logging max logs in flight or unacknowledged\n"
                                "Order of check: component id, root customer gid, global\n"
                                "default: 2000 ( Default is 2000 logs may be in flight and unacknowledged )",
        .val_type           = config_type_int,
        .component_types    = config_component_broker | config_component_pbroker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_zsroot |  config_target_gid_type_global,
        .int_range_lo       = FOHH_LOG_MAX_LOGS_IN_FLIGHT_LOW,
        .int_range_hi       = FOHH_LOG_MAX_LOGS_IN_FLIGHT_HIGH,
        .int_default        = FOHH_LOG_MAX_LOGS_IN_FLIGHT_DEFAULT,
        .feature_group      = FEATURE_GROUP_FOHH_CONFIG,
        .value_traits       = config_value_traits_normal,
    },
    {
        .key                = CONFIG_FEATURE_PATH_CACHE_KEY_AST_GRP,
        .desc               = "Changes the lookup key type for broker client path cache",
        .details            = "0: Default key type\n"
                              "1: Key will include Assistant Group if received in mtunnel request\n"
                              "Order of Check: Customer gid, global\n",
        .val_type           = config_type_int,
        .component_types    = config_component_broker | config_component_pbroker,
        .target_gid_types   = config_target_gid_type_cust | config_target_gid_type_global,
        .int_range_lo       = MIN_CONFIG_FEATURE_PATH_CACHE_KEY_AST_GRP,
        .int_range_hi       = MAX_CONFIG_FEATURE_PATH_CACHE_KEY_AST_GRP,
        .int_default        = DEFAULT_CONFIG_FEATURE_PATH_CACHE_KEY_AST_GRP,
        .feature_group      = FEATURE_GROUP_BROKER_PATH_CACHE,
        .value_traits       = config_value_traits_feature_enablement,
    },
    {
        .key                = CONFIG_FEATURE_POLICY_RE_EVAL_ON_SCIM_UPDATE,
        .desc               = "When enabled reevaluate policies on SCIM update",
        .details            = "0: policy re-eval on SCIM update feature is disabled\n"
                              "1: policy re-eval on SCIM update feature is enabled\n"
                              "Order of check: customer_gid, global\n"
                              "default: 0",
        .val_type           = config_type_int,
        .component_types    = config_component_broker | config_component_pbroker,
        .target_gid_types   = config_target_gid_type_cust | config_target_gid_type_global,
        .int_range_lo       = CONFIG_FEATURE_POLICY_RE_EVAL_ON_SCIM_UPDATE_VAL_MIN,
        .int_range_hi       = CONFIG_FEATURE_POLICY_RE_EVAL_ON_SCIM_UPDATE_VAL_MAX,
        .int_default        = CONFIG_FEATURE_POLICY_RE_EVAL_ON_SCIM_UPDATE_DEFAULT,
        .feature_group      = FEATURE_GROUP_POLICY_RE_EVAL_ON_SCIM_UPDATE,
        .value_traits       = config_value_traits_feature_enablement,
    },
    {
        .key                = CONFIG_FEATURE_POLICY_RE_EVAL_ON_SCIM_UPDATE_HARD_DISABLED,
        .desc               = "When enabled, hard disables the policy re-eval on SCIM update feature globally",
        .details            = "0: policy re-eval on SCIM update feature hard disablement is off\n"
                              "1: policy re-eval on SCIM update feature hard disablement is on\n"
                              "Order of check: global\n"
                              "default: 0",
        .val_type           = config_type_int,
        .component_types    = config_component_broker | config_component_pbroker,
        .target_gid_types   = config_target_gid_type_global,
        .int_range_lo       = CONFIG_FEATURE_POLICY_RE_EVAL_ON_SCIM_UPDATE_HARD_DISABLED_VAL_MIN,
        .int_range_hi       = CONFIG_FEATURE_POLICY_RE_EVAL_ON_SCIM_UPDATE_HARD_DISABLED_VAL_MAX,
        .int_default        = CONFIG_FEATURE_POLICY_RE_EVAL_ON_SCIM_UPDATE_HARD_DISABLED_DEFAULT,
        .feature_group      = FEATURE_GROUP_POLICY_RE_EVAL_ON_SCIM_UPDATE,
        .value_traits       = config_value_traits_systemwide,
    },
};

static struct zpath_config_override_desc zpn_broker_config_override_flags_descriptions_private_broker[] = {
    /* Config overrides used by PSE */
    {
        .key                = CONFIG_FEATURE_ALT_CLOUD,
        .desc               = CONFIG_FEATURE_ALT_CLOUD_DESC,
        .details            = "1: feature is enabled.\n"
                              "0: feature is disabled.\n"
                              "Order of check: component id, component group id, customer gid, global id\n"
                              "default: 1, feature is enabled",
        .val_type           = config_type_int,
        .component_types    = config_component_pbroker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_inst_grp | config_target_gid_type_cust |  config_target_gid_type_global,
        .int_range_lo       = CONFIG_FEATURE_ALT_CLOUD_DISABLED,
        .int_range_hi       = CONFIG_FEATURE_ALT_CLOUD_ENABLED,
        .int_default        = CONFIG_FEATURE_ALT_CLOUD_DEFAULT_VALUE,
        .feature_group      = FEATURE_GROUP_ALT_CLOUD,
        .value_traits       = config_value_traits_feature_enablement,
    },

    {
        .key                = DNS_TXT_QUERY_SUPPORT_FEATURE,
        .desc               = DNS_TXT_QUERY_SUPPORT_FEATURE_DESC,
        .details            = "1: feature is enabled.\n"
                              "0: feature is disabled.\n"
                              "Order of check: component id, component group id, customer gid, global id\n"
                              "default: 1, feature is enabled",
        .val_type           = config_type_int,
        .component_types    = config_component_pbroker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_inst_grp | config_target_gid_type_cust |  config_target_gid_type_global,
        .int_range_lo       = DNS_TXT_QUERY_SUPPORT_FEATURE_MIN,
        .int_range_hi       = DNS_TXT_QUERY_SUPPORT_FEATURE_MAX,
        .int_default        = DEFAULT_DNS_TXT_QUERY_SUPPORT_FEATURE,
        .feature_group      = FEATURE_GROUP_BROKER_DNS,
        .value_traits       = config_value_traits_feature_enablement,
    },

    {
        .key                = BROKER_SEND_AST_AUTH_REPORT_EVERY_1_MIN,
        .desc               = "When enabled, send zpn_ast_auth_report every 1 min instead of the default 4 min",
        .details            = "1: feature is enabled.\n"
                              "0: feature is disabled.\n"
                              "Order of check: component id, component group id, customer gid, global id\n"
                              "default: 1 (Send auth report every 1 minute)",
        .val_type           = config_type_int,
        .component_types    = config_component_pbroker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_inst_grp | config_target_gid_type_cust |  config_target_gid_type_global,
        .int_range_lo       = BROKER_SEND_AST_AUTH_REPORT_EVERY_1_MIN_LOW,
        .int_range_hi       = BROKER_SEND_AST_AUTH_REPORT_EVERY_1_MIN_HIGH,
        .int_default        = BROKER_SEND_AST_AUTH_REPORT_EVERY_1_MIN_DEFAULT,
        .feature_group      = FEATURE_GROUP_BROKER_DISPATCHER_GENERIC,
        .value_traits       = config_value_traits_feature_enablement,
    },

    {
        .key                = ZIA_INSPECTION_DISABLE,
        .desc               = ZIA_INSPECTION_DISABLE_DESC,
        .details            = "1: feature is disabled.\n"
                              "0: feature is enabled.\n"
                              "Order of check: component id, component group id, customer gid, global id\n"
                              "default: 0, feature is enabled",
        .val_type           = config_type_int,
        .component_types    = config_component_pbroker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_inst_grp | config_target_gid_type_cust |  config_target_gid_type_global,
        .int_range_lo       = ZIA_INSPECTION_DISABLE_MIN,
        .int_range_hi       = ZIA_INSPECTION_DISABLE_MAX,
        .int_default        = ZIA_INSPECTION_DISABLE_DEFAULT,
        .feature_group      = FEATURE_GROUP_ZIA_INSPECTION,
        .value_traits       = config_value_traits_feature_enablement,
    },

    {
        .key                = DTLS_FEATURE,
        .desc               = DTLS_FEATURE_DESC,
        .details            = "1: feature is enabled.\n"
                              "0: feature is disabled.\n"
                              "Order of check: component id, component group id, customer gid, global id\n"
                              "default: 0, feature is disabled",
        .val_type           = config_type_int,
        .component_types    = config_component_pbroker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_inst_grp | config_target_gid_type_cust |  config_target_gid_type_global,
        .int_range_lo       = DTLS_FEATURE_MIN,
        .int_range_hi       = DTLS_FEATURE_MAX,
        .int_default        = DEFAULT_BROKER_DTLS,
        .feature_group      = FEATURE_GROUP_DTLS,
        .value_traits       = config_value_traits_feature_enablement,
    },
    {
        .key                = DTLS_FEATURE_CLIENT,
        .desc               = DTLS_FEATURE_CLIENT_DESC,
        .details            = "1: feature is enabled.\n"
                              "0: feature is disabled.\n"
                              "Order of check: component id, component group id, customer gid, global id\n"
                              "default: 0, feature is disabled",
        .val_type           = config_type_int,
        .component_types    = config_component_pbroker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_inst_grp | config_target_gid_type_cust |  config_target_gid_type_global,
        .int_range_lo       = DTLS_FEATURE_CLIENT_MIN,
        .int_range_hi       = DTLS_FEATURE_CLIENT_MAX,
        .int_default        = DEFAULT_PBROKER_DTLS_CLIENT,
        .feature_group      = FEATURE_GROUP_DTLS,
        .value_traits       = config_value_traits_feature_enablement,
    },

    {
        .key                = FOHH_CONNECTION_SETUP_TIMEOUT,
        .desc               = FOHH_CONNECTION_SETUP_TIMEOUT_DESC,
        .details            = "Control timeout config in seconds to delete incomplete fohh connections, between 10 seconds to 60 seconds\n"
                              "Order of check: component id, component group id, customer gid, global id\n"
                              "default: 10 (i.e. fohh connection that doesn't progress to connected state within 10 secs will be deleted by default)",
        .val_type           = config_type_int,
        .component_types    = config_component_pbroker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_inst_grp | config_target_gid_type_cust |  config_target_gid_type_global,
        .int_range_lo       = FOHH_CONNECTION_SETUP_TIMEOUT_SEC_MIN,
        .int_range_hi       = FOHH_CONNECTION_SETUP_TIMEOUT_SEC_MAX,
        .int_default        = FOHH_CONNECTION_SETUP_TIMEOUT_SEC,
        .feature_group      = FEATURE_GROUP_FOHH,
        .value_traits       = config_value_traits_normal,
    },

    {
        .key                = BROKER_CLIENT_AUTH_TIMEOUT,
        .desc               = BROKER_CLIENT_AUTH_TIMEOUT_DESC,
        .details            = "Control timeout config in seconds to delete unauthenticated client connections, between 20 seconds to 120 seconds\n"
                              "Order of check: component id, component group id, customer gid, global id\n"
                              "default: 20 (i.e. client connection that doesn't complete authentication within 20 secs will be deleted by default)",
        .val_type           = config_type_int,
        .component_types    = config_component_pbroker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_inst_grp | config_target_gid_type_cust |  config_target_gid_type_global,
        .int_range_lo       = BROKER_CLIENT_AUTH_TIMEOUT_MIN_SEC,
        .int_range_hi       = BROKER_CLIENT_AUTH_TIMEOUT_MAX_SEC,
        .int_default        = BROKER_CLIENT_AUTH_TIMEOUT_SEC,
        .feature_group      = FEATURE_GROUP_BROKER_SESSION_AUTHENTICATION,
        .value_traits       = config_value_traits_normal,
    },

    {
        .key                = BROKER_CLIENT_IP_ANCHOR_RX_DATA_TIMEOUT,
        .desc               = BROKER_CLIENT_IP_ANCHOR_RX_DATA_TIMEOUT_DESC,
        .details            = "Control timeout config in seconds to delete idle client ip anchor connections, between 600 seconds to 86400 seconds\n"
                              "Order of check: component id, component group id, customer gid, global id\n"
                              "default: 43200 (i.e. client ip anchor connection which stay idle for 43200 secs will be deleted by default)",
        .val_type           = config_type_int,
        .component_types    = config_component_pbroker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_inst_grp | config_target_gid_type_cust |  config_target_gid_type_global,
        .int_range_lo       = BROKER_CLIENT_IP_ANCHOR_RX_DATA_TIMEOUT_SEC_MIN,
        .int_range_hi       = BROKER_CLIENT_IP_ANCHOR_RX_DATA_TIMEOUT_SEC_MAX,
        .int_default        = BROKER_CLIENT_IP_ANCHOR_RX_DATA_TIMEOUT_SEC,
        .feature_group      = FEATURE_GROUP_BROKER_CLIENT,
        .value_traits       = config_value_traits_normal,
    },
    {
        .key                = BROKER_DISABLE_FC_MTR,
        .desc               = BROKER_DISABLE_FC_MTR_DESC,
        .details            = "0: Flow Control for ZDX MTR is enabled.\n"
                              "1: Flow Control for ZDX MTR is disabled.\n"
                              "Order of check: component id, component group id, customer gid, global id\n"
                              "default: 1, (i.e Flow control for ZDX MTR is disabled)",
        .val_type           = config_type_int,
        .component_types    = config_component_pbroker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_inst_grp | config_target_gid_type_cust |  config_target_gid_type_global,
        .int_range_lo       = BROKER_DISABLE_FC_MTR_MIN,
        .int_range_hi       = BROKER_DISABLE_FC_MTR_MAX,
        .int_default        = DEFAULT_BROKER_DISABLE_FC_MTR,
        .feature_group      = FEATURE_GROUP_ZDX_BROKER_DISABLE_FC_MTR,
        .value_traits       = config_value_traits_feature_enablement,
    },

    {
        .key                = DTLS_FEATURE_MTU,
        .desc               = DTLS_FEATURE_MTU_DESC,
        .details            = "Order of check: component id, component group id, customer gid\n"
                              "default: 1482 bytes (DEFAULT_STREAM_MTU)",
        .val_type           = config_type_int,
        .component_types    = config_component_pbroker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_inst_grp | config_target_gid_type_cust,
        .int_range_lo       = LOW_DTLS_MTU,
        .int_range_hi       = HIGH_DTLS_MTU,
        .int_default        = DEFAULT_STREAM_MTU,
        .feature_group      = FEATURE_GROUP_DTLS,
        .value_traits       = config_value_traits_normal,

    },

    {
        .key                = RATE_LIMIT_STATUS_FEATURE,
        .desc               = RATE_LIMIT_STATUS_FEATURE_DESC,
        .details            = "1: feature is enabled.\n"
                              "0: feature is disabled.\n"
                              "Order of check: component id, component group id, customer gid, global id\n"
                              "default: 1, feature is enabled",
        .val_type           = config_type_int,
        .component_types    = config_component_pbroker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_inst_grp | config_target_gid_type_cust |  config_target_gid_type_global,
        .int_range_lo       = RATE_LIMIT_STATUS_MIN,
        .int_range_hi       = RATE_LIMIT_STATUS_MAX,
        .int_default        = DEFAULT_RATE_LIMIT_STATUS,
        .feature_group      = FEATURE_GROUP_BROKER_RATE_LIMITING,
        .value_traits       = config_value_traits_feature_enablement,
    },

    {
        .key                = CC_RATE_LIMIT_STATUS_FEATURE,
        .desc               = RATE_LIMIT_STATUS_FEATURE_DESC,
        .details            = "Applies only to BC/CC clients.\n"
                              "1: feature is enabled.\n"
                              "0: feature is disabled.\n"
                              "Order of check: component id, component group id, customer gid, global id\n"
                              "default: 1, feature is enabled",
        .val_type           = config_type_int,
        .component_types    = config_component_pbroker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_inst_grp | config_target_gid_type_cust |  config_target_gid_type_global,
        .int_range_lo       = RATE_LIMIT_STATUS_MIN,
        .int_range_hi       = RATE_LIMIT_STATUS_MAX,
        .int_default        = DEFAULT_RATE_LIMIT_STATUS,
        .feature_group      = FEATURE_GROUP_BROKER_RATE_LIMITING,
        .value_traits       = config_value_traits_feature_enablement,
    },

    {
        .key                = RATE_LIMIT_MAX_MT_REQ_RATE,
        .desc               = RATE_LIMIT_STATUS_FEATURE_DESC,
        .details            = "Number of mtunnel requests allowed per second, on the same application, for each client.\n"
                              "Application: domain+port+proto\n"
                              "Order of check: component id, component group id, customer gid, global id\n"
                              "default: 100 mt/s",
        .val_type           = config_type_int,
        .component_types    = config_component_pbroker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_inst_grp | config_target_gid_type_cust |  config_target_gid_type_global,
        .int_range_lo       = MAX_MT_REQ_RATE_MIN_VAL,
        .int_range_hi       = MAX_MT_REQ_RATE_MAX_VAL,
        .int_default        = DEFAULT_MAX_MT_REQ_RATE,
        .feature_group      = FEATURE_GROUP_BROKER_RATE_LIMITING,
        .value_traits       = config_value_traits_normal,
    },

    {
        .key                = RATE_LIMIT_MT_REQ_BUCKET_SIZE,
        .desc               = RATE_LIMIT_STATUS_FEATURE_DESC,
        .details            = "Additional buffer for handling bursts of mtunnel requests on same application, should be set >=10x rate.\n"
                              "Order of check: component id, component group id, customer gid, global id\n"
                              "default: 1000 mtunnel requests",
        .val_type           = config_type_int,
        .component_types    = config_component_pbroker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_inst_grp | config_target_gid_type_cust |  config_target_gid_type_global,
        .int_range_lo       = RATE_LIMIT_MT_REQ_BUCKET_SIZE_MIN_VAL,
        .int_range_hi       = RATE_LIMIT_MT_REQ_BUCKET_SIZE_MAX_VAL,
        .int_default        = DEFAULT_MT_REQ_BUCKET_SIZE,
        .feature_group      = FEATURE_GROUP_BROKER_RATE_LIMITING,
        .value_traits       = config_value_traits_normal,
    },

    {
        .key                = RATE_LIMIT_CC_MAX_MT_REQ_RATE,
        .desc               = RATE_LIMIT_STATUS_FEATURE_DESC,
        .details            = "Applies only to BC/CC clients.\n"
                              "Number of mtunnel requests allowed per second, on the same application, for each CC/BC client.\n"
                              "Application: domain+port+proto\n"
                              "Order of check: component id, component group id, customer gid, global id\n"
                              "default: 200 mt/s",
        .val_type           = config_type_int,
        .component_types    = config_component_pbroker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_inst_grp | config_target_gid_type_cust |  config_target_gid_type_global,
        .int_range_lo       = CC_MAX_MT_REQ_RATE_MIN_VAL,
        .int_range_hi       = CC_MAX_MT_REQ_RATE_MAX_VAL,
        .int_default        = DEFAULT_CC_MAX_MT_REQ_RATE,
        .feature_group      = FEATURE_GROUP_BROKER_RATE_LIMITING,
        .value_traits       = config_value_traits_normal,
    },

    {
        .key                = RATE_LIMIT_CC_MT_REQ_BUCKET_SIZE,
        .desc               = RATE_LIMIT_STATUS_FEATURE_DESC,
        .details            = "Applies only to BC/CC clients.\n"
                              "Additional buffer for handling bursts of mtunnel requests on same application, should be set >=10x rate.\n"
                              "Order of check: component id, component group id, customer gid, global id\n"
                              "default: 2000 mtunnel requests",
        .val_type           = config_type_int,
        .component_types    = config_component_pbroker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_inst_grp | config_target_gid_type_cust |  config_target_gid_type_global,
        .int_range_lo       = CC_MT_REQ_BUCKET_SIZE_MIN_VAL,
        .int_range_hi       = CC_MT_REQ_BUCKET_SIZE_MAX_VAL,
        .int_default        = DEFAULT_CC_MT_REQ_BUCKET_SIZE,
        .feature_group      = FEATURE_GROUP_BROKER_RATE_LIMITING,
        .value_traits       = config_value_traits_normal,
    },

    {
        .key                = RATE_LIMIT_MAX_NEW_APP_RATE,
        .desc               = RATE_LIMIT_STATUS_FEATURE_DESC,
        .details            = "Number of mtunnel requests allowed per second, on different applications, for each client.\n"
                              "Application: domain+port+proto\n"
                              "Order of check: component id, component group id, customer gid, global id\n"
                              "default: 100 mt/s",
        .val_type           = config_type_int,
        .component_types    = config_component_pbroker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_inst_grp | config_target_gid_type_cust |  config_target_gid_type_global,
        .int_range_lo       = MAX_NEW_APP_RATE_MIN_VAL,
        .int_range_hi       = MAX_NEW_APP_RATE_MAX_VAL,
        .int_default        = DEFAULT_MAX_NEW_APP_RATE,
        .feature_group      = FEATURE_GROUP_BROKER_RATE_LIMITING,
        .value_traits       = config_value_traits_normal,
    },

    {
        .key                = RATE_LIMIT_NEW_APP_BUCKET_SIZE,
        .desc               = RATE_LIMIT_STATUS_FEATURE_DESC,
        .details            = "Additional buffer for handling bursts of mtunnel requests on different applications, should be set >=10x rate.\n"
                              "Order of check: component id, component group id, customer gid, global id\n"
                              "default: 1000 mtunnel requests",
        .val_type           = config_type_int,
        .component_types    = config_component_pbroker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_inst_grp | config_target_gid_type_cust |  config_target_gid_type_global,
        .int_range_lo       = NEW_APP_BUCKET_SIZE_MIN_VAL,
        .int_range_hi       = NEW_APP_BUCKET_SIZE_MAX_VAL,
        .int_default        = DEFAULT_NEW_APP_BUCKET_SIZE,
        .feature_group      = FEATURE_GROUP_BROKER_RATE_LIMITING,
        .value_traits       = config_value_traits_normal,
    },

    {
        .key                = BROKER_CLIENT_PATH_CACHE_MAX_SIZE_PER_DOMAIN,
        .desc               = BROKER_CLIENT_PATH_CACHE_MAX_SIZE_PER_DOMAIN_DESC,
        .details            = "Applies only to BC/CC/SIPA clients and not ZCC.\n"
                              "Order of check: component id, component group id, customer gid, global id\n"
                              "default: 10 (For eg. if 10, hash (user_id %10) is the hash key)",
        .val_type           = config_type_int,
        .component_types    = config_component_pbroker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_inst_grp | config_target_gid_type_cust |  config_target_gid_type_global,
        .int_range_lo       = BROKER_CLIENT_PATH_CACHE_MAX_SIZE_PER_DOMAIN_MIN,
        .int_range_hi       = BROKER_CLIENT_PATH_CACHE_MAX_SIZE_PER_DOMAIN_MAX,
        .int_default        = DEFAULT_BROKER_CLIENT_PATH_CACHE_MAX_SIZE_PER_DOMAIN,
        .feature_group      = FEATURE_GROUP_BROKER_DISPATCHER_GENERIC,
        .value_traits       = config_value_traits_normal,
    },

    {
        .key                = BROKER_FEATURE_AUTH_STATE_LOG,
        .desc               = BROKER_FEATURE_AUTH_STATE_LOG_DESC,
        .details            = "Auth state log is to detect and log the changes in client's dynamic attr i.e saml,scim,trusted n/w etc.\n"
                              "1: feature is enabled.\n"
                              "0: feature is disabled.\n"
                              "Order of check: component id, component group id, customer gid, global id\n"
                              "default: 1, feature is enabled",
        .val_type           = config_type_int,
        .component_types    = config_component_pbroker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_inst_grp | config_target_gid_type_cust |  config_target_gid_type_global,
        .int_range_lo       = BROKER_FEATURE_AUTH_STATE_LOG_MIN,
        .int_range_hi       = BROKER_FEATURE_AUTH_STATE_LOG_MAX,
        .int_default        = DEFAULT_BROKER_FEATURE_AUTH_STATE_LOG,
        .feature_group      = FEATURE_GROUP_BROKER_CLIENT,
        .value_traits       = config_value_traits_feature_enablement,
    },

    {
        .key                = BROKER_FEATURE_DNS_STRICT_CHECK,
        .desc               = BROKER_FEATURE_DNS_STRICT_CHECK_DESC,
        .details            = "Broker strict DNS check will query AppConnector for an exact DNS type match of A or AAAA\n"
                              "1: feature is enabled.\n"
                              "0: feature is disabled.\n"
                              "Order of check: component id, component group id, customer gid, global id\n"
                              "default: 1, feature is enabled",
        .val_type           = config_type_int,
        .component_types    = config_component_pbroker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_inst_grp | config_target_gid_type_cust |  config_target_gid_type_global,
        .int_range_lo       = BROKER_FEATURE_DNS_STRICT_CHECK_MIN,
        .int_range_hi       = BROKER_FEATURE_DNS_STRICT_CHECK_MAX,
        .int_default        = DEFAULT_BROKER_FEATURE_DNS_STRICT_CHECK,
        .feature_group      = FEATURE_GROUP_BROKER_DNS_STRICT_CHECK,
        .value_traits       = config_value_traits_feature_enablement,
    },

    {
        .key                = QUICKACK_CONFIG_ASSISTANT,
        .desc               = "Enable quickack mode if set or disable quickack mode if cleared on app connector side for data connection between app connector and broker",
        .details            = "0: Quickack on assistant is disabled\n"
                              "1: Quickack on assistant is enabled\n"
                              "Order of check: component id, component group id, customer gid, global\n"
                              "default: 0 (i.e. Quickack on assistant is disabled by default)",
        .val_type           = config_type_int,
        .component_types    = config_component_pbroker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_inst_grp | config_target_gid_type_cust | config_target_gid_type_global,
        .int_range_lo       = 0,
        .int_range_hi       = 1,
        .int_default        = DEFAULT_ASST_QUICKACK,
        .feature_group      = FEATURE_GROUP_QUICKACK,
        .value_traits       = config_value_traits_feature_enablement,
    },
    {
        .key                = QUICKACK_READ_CONFIG_ASSISTANT,
        .desc               = "Enable quickack mode if set or disable quickack mode if cleared on app connector side for data connection between app connector and broker",
        .details            = "0: Quickack read on assistant is disabled\n"
                              "1: Quickack read on assistant is enabled\n"
                              "Order of check: component id, component group id, customer gid, global\n"
                              "default: 0 (i.e. Quickack read on assistant is disabled by default)",
        .val_type           = config_type_int,
        .component_types    = config_component_pbroker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_inst_grp | config_target_gid_type_cust | config_target_gid_type_global,
        .int_range_lo       = 0,
        .int_range_hi       = 1,
        .int_default        = DEFAULT_ASST_QUICKACK_READ,
        .feature_group      = FEATURE_GROUP_QUICKACK,
        .value_traits       = config_value_traits_feature_enablement,

    },
    {
        .key                = LIBEVENT_LOW_WRITE_WATERMARK_PSE,
        .desc               = "Enable Low Write Watermark on PSE",
        .details            = "0: LibEvent Low Write Watermark on PSE is disabled\n"
                              "1: LibEvent Low Write Watermark on PSE is enabled\n"
                              "Order of check: customer gid, global\n"
                              "default: 0 (i.e. LibEvent Low Write Watermark on PSE is disabled by default)",
        .val_type           = config_type_int,
        .component_types    = config_component_pbroker,
        .target_gid_types   = config_target_gid_type_cust | config_target_gid_type_global,
        .int_range_lo       = LIBEVENT_LOW_WRITE_WATERMARK_MIN,
        .int_range_hi       = LIBEVENT_LOW_WRITE_WATERMARK_MAX,
        .int_default        = LIBEVENT_LOW_WRITE_WATERMARK_DEFAULT,
        .feature_group      = FEATURE_GROUP_LIBEVENT_LOW_WRITE_WATERMARK,
        .value_traits       = config_value_traits_feature_enablement,

    },
    {
        .key                = BROKER_CLIENT_STATUS_TIMEOUT_SEC,
        .desc               = "Broker clients idle timeout for connections with fohh_status_xxx heartbeat",
        .details            = "Order of check - Private: broker id, broker group id, customer id, global\n"
                              "Default: 43200 sec (12 hr)",
        .val_type           = config_type_int,
        .component_types    = config_component_pbroker,
        .target_gid_types   = config_target_gid_type_inst |
                              config_target_gid_type_inst_grp | config_target_gid_type_cust |
                              config_target_gid_type_global,
        .int_range_lo       = BROKER_CLIENT_STATUS_TIMEOUT_SEC_MIN, // 5 min
        .int_range_hi       = BROKER_CLIENT_STATUS_TIMEOUT_SEC_MAX, // 24 hrs
        .int_default        = BROKER_CLIENT_STATUS_TIMEOUT_DEFAULT_SEC,
        .feature_group      = FEATURE_GROUP_FOHH,
        .value_traits       = config_value_traits_normal,
    },
    {
        .key                = BROKER_CLIENT_ENABLE_CLEANUP_STALE,
        .desc               = "Enable or Disable cleanup stale ZCC connections",
        .details            = "0: Disable\n"
                              "1: Enable\n"
                              "DEFAULT: 0\n",
        .val_type           = config_type_int,
        .component_types    = config_component_pbroker,
        .target_gid_types   = config_target_gid_type_inst |
                              config_target_gid_type_inst_grp | config_target_gid_type_cust |
                              config_target_gid_type_global,
        .int_range_lo       = BROKER_CLIENT_ENABLE_CLEANUP_STALE_VAL_MIN,
        .int_range_hi       = BROKER_CLIENT_ENABLE_CLEANUP_STALE_VAL_MAX,
        .int_default        = DEFAULT_BROKER_CLIENT_ENABLE_CLEANUP_STALE,
        .feature_group      = FEATURE_GROUP_BROKER_CLIENT,
        .value_traits       = config_value_traits_feature_enablement,
    },
    {
        .key                = QUICKACK_CONFIG_CLIENT,
        .desc               = "Enable/disable quickack for a customer.",
        .details            = "This config enables the socket setting for quickack in accept/listen and connect of the socket"
                                "to force the delayed acknowledgements to stop.\n"
                                "0: Quickack is disabled    \n1: Quickack is enabled \n"
                                "Order of check: customer gid, global\n"
                                "default: 0, feature is disabled.",
        .val_type           = config_type_int,
        .component_types    = config_component_pbroker,
        .target_gid_types   = config_target_gid_type_cust | config_target_gid_type_global,
        .int_range_lo       = 0,
        .int_range_hi       = 1,
        .int_default        = DEFAULT_BROKER_QUICKACK,
        .feature_group      = FEATURE_GROUP_QUICKACK,
        .value_traits       = config_value_traits_feature_enablement,
    },
    {
        .key                = QUICKACK_READ_CONFIG_CLIENT,
        .desc               = "Enable/disable quickack at read callback for a customer.",
        .details            = "This config enables the socket setting for quickack at "
                                "read callback to force the delayed acknowledgements to stop.\n"
                                "0: Quickack is disabled    1: Quickack is enabled \n"
                                "Order of check: customer gid, global\n"
                                "default: 0, feature is disabled.",
        .val_type           = config_type_int,
        .component_types    = config_component_pbroker,
        .target_gid_types   = config_target_gid_type_cust | config_target_gid_type_global,
        .int_range_lo       = 0,
        .int_range_hi       = 1,
        .int_default        = DEFAULT_BROKER_QUICKACK_READ,
        .feature_group      = FEATURE_GROUP_QUICKACK,
        .value_traits       = config_value_traits_feature_enablement,
    },
    {
        .key                = ZPN_PSE_ENABLE_HOSTNAME_VALIDATION,
        .desc               = "Enable hostname validation",
        .details            = "Enable hostname validation coming from client auth\n"
                              "1: feature is enabled.\n"
                              "0: feature is disabled.\n"
                              "Order of check: component id, component group id, customer gid, global id\n"
                              "default: 0, feature is disabled",
        .val_type           = config_type_int,
        .component_types    = config_component_pbroker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_inst_grp |
                              config_target_gid_type_cust | config_target_gid_type_global,
        .int_range_lo       = ZPN_PSE_ENABLE_HOSTNAME_VALIDATION_MIN,
        .int_range_hi       = ZPN_PSE_ENABLE_HOSTNAME_VALIDATION_MAX,
        .int_default        = ZPN_DEFAULT_PSE_ENABLE_HOSTNAME_VALIDATION,
        .feature_group      = FEATURE_GROUP_PRIVATE_SERVICE_EDGE_HOSTNAME_VALIDATION,
        .value_traits       = config_value_traits_feature_enablement,
    },
    {
        .key                = CONFIG_FEATURE_PSE_SEND_PENDING_MTUNNEL_END,
        .desc               = CONFIG_FEATURE_PSE_SEND_PENDING_MTUNNEL_END_DESC,
        .details            = "0: Do not send pending mtunnel_end to assistant\n"
                              "1: Send pending mtunnel_end to assistant\n"
                              "Order of check: broker gid, broker group gid, customer gid, global\n"
                              "default: 0 (Disabled)",
        .val_type           = config_type_int,
        .component_types    = config_component_pbroker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_inst_grp |
                              config_target_gid_type_cust | config_target_gid_type_global,
        .int_range_lo       = MIN_CONFIG_FEATURE_PSE_SEND_PENDING_MTUNNEL_END,
        .int_range_hi       = MAX_CONFIG_FEATURE_PSE_SEND_PENDING_MTUNNEL_END,
        .int_default        = DEFAULT_CONFIG_FEATURE_PSE_SEND_PENDING_MTUNNEL_END,
        .feature_group      = FEATURE_GROUP_PRIVATE_SERVICE_EDGE,
        .value_traits       = config_value_traits_feature_enablement,
    },
    {
        .key                = PBROKER_FEATURE_MALLOC_FASTBIN,
        .desc               = CONFIG_FEATURE_BROKER_MALLOC_FASTBIN_DESC,
        .details            = "Malloc Fastbin usage can be controlled by Mallopt; \n"
                              "by default, it is enabled and set to max chunk size of 128; \n"
                              "Setting the Malloc Fastbin flag to 0, disables fastbin usage; \n"
                              "we restrict to 128 or 0.",
        .val_type           = config_type_int,
        .component_types    = config_component_pbroker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_global,
        .int_range_lo       = BROKER_FEATURE_MALLOC_FASTBIN_DISABLED,
        .int_range_hi       = BROKER_FEATURE_MALLOC_FASTBIN_ENABLED,
        .int_default        = BROKER_FEATURE_MALLOC_FASTBIN_DEFAULT,
        .feature_group      = FEATURE_GROUP_BROKER,
        .value_traits       = config_value_traits_normal,
    },
    {
        .key                = CONFIG_FEATURE_PBROKER_FPROXY,
        .feature_group      = FEATURE_GROUP_AUTH_SNI,
        .desc               = "Feature Knob for pbroker forward proxy",
        .details            = "Enable/Disable localhost forward proxy on pbroker, default port 8510\n"
                                      "default: 0 (disabled)",
        .val_type           = config_type_int,
        .value_traits       = config_value_traits_feature_enablement,
        .component_types    = config_component_pbroker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_global,
        .int_range_lo       = 0,
        .int_range_hi       = 1,
        .int_default        = CONFIG_FEATURE_PBROKER_FPROXY_DEFAULT_STATUS
    },
    {
        .key                = CONFIG_FOHH_STATUS_INTVL_CTL,
        .feature_group      = FEATURE_GROUP_FOHH_INTERVAL,
        .desc               = "status interval for sending fohh_status_request messages on ctl connection",
        .details            = "Order of check: customer gid\n"
                                "default: 1 (i.e. default status interval is 1 second for sending fohh_status_request messages)",
        .val_type           = config_type_int,
        .value_traits       = config_value_traits_normal,
        .component_types    = config_component_pbroker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_cust,
        .int_range_lo       = LOW_STATUS_INTVL,
        .int_range_hi       = HIGH_STATUS_INTVL,
        .int_default        = STATUS_INTVL_DEFAULT
    },
    {
        .key                = CONFIG_FOHH_STATUS_INTVL_STATS,
        .feature_group      = FEATURE_GROUP_FOHH_INTERVAL,
        .desc               = "status interval for sending fohh_status_request messages on stats connection",
        .details            = "Order of check: customer gid\n"
                                "default: 1 (i.e. default status interval is 1 second for sending fohh_status_request messages)",
        .val_type           = config_type_int,
        .value_traits       = config_value_traits_normal,
        .component_types    = config_component_pbroker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_cust,
        .int_range_lo       = LOW_STATUS_INTVL,
        .int_range_hi       = HIGH_STATUS_INTVL,
        .int_default        = STATUS_INTVL_DEFAULT
    },
    {
        .key                = CONFIG_FOHH_STATUS_INTVL_OVD,
        .feature_group      = FEATURE_GROUP_FOHH_INTERVAL,
        .desc               = "status interval for sending fohh_status_request messages on ovd connection",
        .details            = "Order of check: customer gid\n"
                                "default: 1 (i.e. default status interval is 1 second for sending fohh_status_request messages)",
        .val_type           = config_type_int,
        .value_traits       = config_value_traits_normal,
        .component_types    = config_component_pbroker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_cust,
        .int_range_lo       = LOW_STATUS_INTVL,
        .int_range_hi       = HIGH_STATUS_INTVL,
        .int_default        = STATUS_INTVL_DEFAULT
    },
    {
        .key                = CONFIG_FOHH_STATUS_INTVL_CFG,
        .feature_group      = FEATURE_GROUP_FOHH_INTERVAL,
        .desc               = "status interval for sending fohh_status_request messages on config connection",
        .details            = "Order of check: customer gid\n"
                                "default: 1 (i.e. default status interval is 1 second for sending fohh_status_request messages)",
        .val_type           = config_type_int,
        .value_traits       = config_value_traits_normal,
        .component_types    = config_component_pbroker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_cust,
        .int_range_lo       = LOW_STATUS_INTVL,
        .int_range_hi       = HIGH_STATUS_INTVL,
        .int_default        = STATUS_INTVL_DEFAULT
    },
    {
        .key                = CONFIG_FOHH_STATUS_INTVL_LOG,
        .feature_group      = FEATURE_GROUP_FOHH_INTERVAL,
        .desc               = "status interval for sending fohh_status_request messages on Log connection",
        .details            = "Order of check: customer gid\n"
                                "default: 1 (i.e. default status interval is 1 second for sending fohh_status_request messages)",
        .val_type           = config_type_int,
        .value_traits       = config_value_traits_normal,
        .component_types    = config_component_pbroker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_cust,
        .int_range_lo       = LOW_STATUS_INTVL,
        .int_range_hi       = HIGH_STATUS_INTVL,
        .int_default        = STATUS_INTVL_DEFAULT
    },
    {
        .key                = CONFIG_FOHH_STATUS_INTVL_USERDB,
        .feature_group      = FEATURE_GROUP_FOHH_INTERVAL,
        .desc               = "status interval for sending fohh_status_request messages on userdb connection",
        .details            = "Order of check: customer gid\n"
                                "default: 1 (i.e. default status interval is 1 second for sending fohh_status_request messages)",
        .val_type           = config_type_int,
        .value_traits       = config_value_traits_normal,
        .component_types    = config_component_pbroker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_cust,
        .int_range_lo       = LOW_STATUS_INTVL,
        .int_range_hi       = HIGH_STATUS_INTVL,
        .int_default        = STATUS_INTVL_DEFAULT
    },
    {
        .key                = CONFIG_FOHH_STATUS_INTVL_RCFG,
        .feature_group      = FEATURE_GROUP_FOHH_INTERVAL,
        .desc               = "status interval for sending fohh_status_request messages on rcfg connection",
        .details            = "Order of check: customer gid\n"
                                "default: 1 (i.e. default status interval is 1 second for sending fohh_status_request messages)",
        .val_type           = config_type_int,
        .value_traits       = config_value_traits_normal,
        .component_types    = config_component_pbroker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_cust,
        .int_range_lo       = LOW_STATUS_INTVL,
        .int_range_hi       = HIGH_STATUS_INTVL,
        .int_default        = STATUS_INTVL_DEFAULT
    },
    {
        .key                = CONFIG_FEATURE_EXTRANET_PSE_ENABLED,
        .desc               = "Feature Flag for extranet on private brokers for a specific customer",
        .details            = "0: extranet feature customer+private brokers is off\n"
                              "1: extranet feature customer+private brokers is on\n"
                              "Order of check: hard disabled, customer level, global\n"
                              "default: 0",
        .val_type           = config_type_int,
        .component_types    = config_component_pbroker,
        .target_gid_types   = config_target_gid_type_cust | config_target_gid_type_global,
        .int_range_lo       = CONFIG_FEATURE_EXTRANET_PSE_ENABLED_MIN,
        .int_range_hi       = CONFIG_FEATURE_EXTRANET_PSE_ENABLED_MAX,
        .int_default        = CONFIG_FEATURE_EXTRANET_PSE_ENABLED_DEFAULT,
        .feature_group      = FEATURE_GROUP_EXTRANET,
        .value_traits       = config_value_traits_feature_enablement,
    },
    {
        .key                = ZPN_PSE_FOHH_CONNECTION_DISABLE_READ_CONFIG_FLAG,
        .desc               = "Feature flag to configure read disable feature on private brokers",
        .details            = "1: fohh connection read disable feature is enabled\n"
                              "   which triggers read disable (block) entire fohh connection socket towards zcc client\n"
                              "   if mtunnel tx buff towards appc reaches high water mark and stays above low water for allowed maximum time\n"
                              "0: fohh connection read disable (block) feature is disabled\n"
                              "   which triggers read disable (block) entire fohh connection socket towards zcc client\n"
                              "   if one of mtunnels triggers pause message towards zcc client\n"
                              "default: 0",
        .val_type           = config_type_int,
        .component_types    = config_component_pbroker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_cust | config_target_gid_type_global,
        .int_range_lo       = LOW_ZPN_PSE_FOHH_CONNECTION_DISABLE_READ_CONFIG_FLAG,
        .int_range_hi       = HIGH_ZPN_PSE_FOHH_CONNECTION_DISABLE_READ_CONFIG_FLAG,
        .int_default        = ZPN_PSE_FOHH_CONNECTION_DISABLE_READ_CONFIG_FLAG_DEFAULT_VALUE,
        .value_traits       = config_value_traits_feature_enablement,
        .feature_group      = FEATURE_GROUP_FOHH
    },
    {
        .key                = ZPN_PSE_FOHH_CONNECTION_DISABLE_READ_CLIENT_TX_BUFF_HIGH,
        .desc               = "read disable feature - tx buff high water mark config in bytes",
        .details            = "read disable (block) entire fohh connection socket towards zcc client is triggered\n"
                              "if mtunnel tx buff towards appc reaches high water mark and stays above low water for allowed maximum time",
        .val_type           = config_type_int,
        .component_types    = config_component_pbroker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_cust | config_target_gid_type_global,
        .int_range_lo       = LOW_ZPN_PSE_FOHH_CONNECTION_DISABLE_READ_CLIENT_TX_BUFF_HIGH,
        .int_range_hi       = HIGH_ZPN_PSE_FOHH_CONNECTION_DISABLE_READ_CLIENT_TX_BUFF_HIGH,
        .int_default        = ZPN_PSE_FOHH_CONNECTION_DISABLE_READ_CLIENT_TX_BUFF_HIGH_DEFAULT,
        .value_traits       = config_value_traits_normal,
        .feature_group      = FEATURE_GROUP_FOHH
    },
    {
        .key                = ZPN_PSE_FOHH_CONNECTION_DISABLE_READ_CLIENT_TX_BUFF_LOW,
        .desc               = "read disable feature - tx buff low water mark config in bytes",
        .details            = "if tx buff towards appc reaches high water mark and stays above low water for allowed maximum time\n"
                              "read disable (block) socket towards zcc client is triggered",
        .val_type           = config_type_int,
        .component_types    = config_component_pbroker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_cust | config_target_gid_type_global,
        .int_range_lo       = LOW_ZPN_PSE_FOHH_CONNECTION_DISABLE_READ_CLIENT_TX_BUFF_LOW,
        .int_range_hi       = HIGH_ZPN_PSE_FOHH_CONNECTION_DISABLE_READ_CLIENT_TX_BUFF_LOW,
        .int_default        = ZPN_PSE_FOHH_CONNECTION_DISABLE_READ_CLIENT_TX_BUFF_LOW_DEFAULT,
        .value_traits       = config_value_traits_normal,
        .feature_group      = FEATURE_GROUP_FOHH
    },
    {
        .key                = ZPN_PSE_FOHH_CONNECTION_DISABLE_READ_CLIENT_TX_BUFF_HIGH_ALLOW_TIME_MAX_US,
        .desc               = "read disable feature - allowed maximum time config in us",
        .details            = "if tx buff towards appc reaches high water mark and stays above low water for allowed maximum time\n"
                              "read disable (block) socket towards zcc client is triggered",
        .val_type           = config_type_int,
        .component_types    = config_component_pbroker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_cust | config_target_gid_type_global,
        .int_range_lo       = LOW_ZPN_PSE_FOHH_CONNECTION_DISABLE_READ_CLIENT_TX_BUFF_HIGH_ALLOW_TIME_MAX_US,
        .int_range_hi       = HIGH_ZPN_PSE_FOHH_CONNECTION_DISABLE_READ_CLIENT_TX_BUFF_HIGH_ALLOW_TIME_MAX_US,
        .int_default        = ZPN_PSE_FOHH_CONNECTION_DISABLE_READ_CLIENT_TX_BUFF_HIGH_ALLOW_TIME_MAX_US_DEFAULT,
        .value_traits       = config_value_traits_normal,
        .feature_group      = FEATURE_GROUP_FOHH
    },
    {
        .key                = CONFIG_FEATURE_PSE_QBR_INSIGHTS_FEATURE,
        .feature_group      = FEATURE_GROUP_QBR,
        .desc               = "When enabled, new fields for QBR report will be made available in the transaction logs",
        .details            = "0: feature is disabled\n"
                              "1: feature is enabled\n"
                              "default: 0",
        .val_type           = config_type_int,
        .value_traits       = config_value_traits_feature_enablement,
        .component_types    = config_component_pbroker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_inst_grp | config_target_gid_type_cust | config_target_gid_type_global,
        .int_range_lo       = CONFIG_FEATURE_QBR_INSIGHTS_FEATURE_VAL_MIN,
        .int_range_hi       = CONFIG_FEATURE_QBR_INSIGHTS_FEATURE_VAL_MAX,
        .int_default        = CONFIG_FEATURE_QBR_INSIGHTS_FEATURE_DEFAULT
    },
    {
        .key                = CONFIG_FEATURE_PSE_IPV6_ALT_AUTHSP,
        .feature_group      = FEATURE_GROUP_PRIVATE_SERVICE_EDGE,
        .desc               = "When enabled, PSE will download zpath_cloud table and use it for saml audience validation",
        .details            = "0: feature is disabled\n"
                              "1: feature is enabled\n"
                              "default: 0",
        .val_type           = config_type_int,
        .value_traits       = config_value_traits_feature_enablement,
        .component_types    = config_component_pbroker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_inst_grp | config_target_gid_type_cust | config_target_gid_type_global,
        .int_range_lo       = CONFIG_FEATURE_PSE_IPV6_ALT_AUTHSP_DISABLED,
        .int_range_hi       = CONFIG_FEATURE_PSE_IPV6_ALT_AUTHSP_ENABLED,
        .int_default        = CONFIG_FEATURE_PSE_IPV6_ALT_AUTHSP_DEFAULT
    },
    {
        .key                = CONFIG_FEATURE_EXTRANET_DNS_NEG_CACHE_TIME_S,
        .desc               = "Extranet specific negative DNS cache timeout",
        .details            = "Configure the timeout for negative DNS cache entry for an extranet app.\n"
                              "Order of check: component id, component group id, customer id, global\n"
                              "default: 120 seconds",
        .val_type           = config_type_int,
        .component_types    = config_component_pbroker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_inst_grp | config_target_gid_type_cust | config_target_gid_type_global,
        .int_range_lo       = CONFIG_FEATURE_EXTRANET_DNS_NEG_CACHE_TIME_S_MIN,
        .int_range_hi       = CONFIG_FEATURE_EXTRANET_DNS_NEG_CACHE_TIME_S_MAX,
        .int_default        = CONFIG_FEATURE_EXTRANET_DNS_NEG_CACHE_TIME_S_DEFAULT,
        .feature_group      = FEATURE_GROUP_EXTRANET,
        .value_traits       = config_value_traits_normal,
    },
    {
        .key                = FOHH_FLOW_CONTROL_ENHANCEMENTS,
        .feature_group      = FEATURE_GROUP_FOHH_FLOW_CONTROL_ENHANCEMENTS,
        .desc               = "Enable Fohh flow control enhancements",
        .details            = "Enable Fohh flow control enhancements\n"
                              "default: 0 (behaviour disabled by default)",
        .val_type           = config_type_int,
        .value_traits       = config_value_traits_feature_enablement,
        .component_types    = config_component_pbroker,
        .target_gid_types   = config_target_gid_type_cust | config_target_gid_type_global,
        .int_range_lo       = FOHH_FLOW_CONTROL_ENHANCEMENTS_MIN,
        .int_range_hi       = FOHH_FLOW_CONTROL_ENHANCEMENTS_MAX,
        .int_default        = DEFAULT_FOHH_FLOW_CONTROL_ENHANCEMENTS,
    },
    {
        .key                = CONFIG_FEATURE_PSE_MCONN_BATCH_WINDOW_UPDATES,
        .feature_group      = FEATURE_GROUP_MCONN_WINDOW_UPDATES,
        .desc               = "When enabled, Mconn window updates are batched ",
        .details            = "0: feature is disabled\n"
                              "1: feature is enabled\n"
                              "default: 0",
        .val_type           = config_type_int,
        .value_traits       = config_value_traits_feature_enablement,
        .component_types    = config_component_pbroker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_inst_grp | config_target_gid_type_cust | config_target_gid_type_global,
        .int_range_lo       = CONFIG_FEATURE_PSE_MCONN_BATCH_WINDOW_UPDATES_MIN,
        .int_range_hi       = CONFIG_FEATURE_PSE_MCONN_BATCH_WINDOW_UPDATES_MAX,
        .int_default        = CONFIG_FEATURE_PSE_MCONN_BATCH_WINDOW_UPDATES_DEFAULT
    },
    {
        .key                = CONFIG_FEATURE_PSE_SYN_APP_RTT,
        .feature_group      = FEATURE_GROUP_APP_RTT,
        .desc               = "When enabled, APP RTT uses synchronous status messages ",
        .details            = "0: feature is disabled\n"
                              "1: feature is enabled\n"
                              "default: 0",
        .val_type           = config_type_int,
        .value_traits       = config_value_traits_feature_enablement,
        .component_types    = config_component_pbroker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_inst_grp | config_target_gid_type_cust | config_target_gid_type_global,
        .int_range_lo       = CONFIG_FEATURE_PSE_SYN_APP_RTT_MIN,
        .int_range_hi       = CONFIG_FEATURE_PSE_SYN_APP_RTT_MAX,
        .int_default        = CONFIG_FEATURE_PSE_SYN_APP_RTT_DEFAULT
    },
    {
        .key                = CONFIG_FEATURE_PSE_FOHH_PIPELINE_LATENCY_TRACE,
        .feature_group      = FEATURE_GROUP_PIPELINE_LATENCY_TRACE,
        .desc               = "When enabled, Fohh pipeline latency tracing is active ",
        .details            = "0: feature is disabled\n"
                              "1: feature is enabled\n"
                              "default: 0",
        .val_type           = config_type_int,
        .value_traits       = config_value_traits_feature_enablement,
        .component_types    = config_component_pbroker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_inst_grp | config_target_gid_type_cust | config_target_gid_type_global,
        .int_range_lo       = CONFIG_FEATURE_PSE_FOHH_PIPELINE_LATENCY_TRACE_MIN,
        .int_range_hi       = CONFIG_FEATURE_PSE_FOHH_PIPELINE_LATENCY_TRACE_MAX,
        .int_default        = CONFIG_FEATURE_PSE_FOHH_PIPELINE_LATENCY_TRACE_DEFAULT
    },
    {
        .key                = CONFIG_FEATURE_PSE_CLIENT_SYN_APP_RTT,
        .feature_group      = FEATURE_GROUP_APP_RTT,
        .desc               = "When enabled, APP RTT uses synchronous status messages ",
        .details            = "0: feature is disabled\n"
                              "1: feature is enabled\n"
                              "default: 0",
        .val_type           = config_type_int,
        .value_traits       = config_value_traits_feature_enablement,
        .component_types    = config_component_pbroker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_cust | config_target_gid_type_global,
        .int_range_lo       = CONFIG_FEATURE_PSE_SYN_APP_RTT_MIN,
        .int_range_hi       = CONFIG_FEATURE_PSE_SYN_APP_RTT_MAX,
        .int_default        = CONFIG_FEATURE_PSE_SYN_APP_RTT_DEFAULT
    },
    {
        .key                = CONFIG_FEATURE_PSE_CLIENT_FOHH_PIPELINE_LATENCY_TRACE,
        .feature_group      = FEATURE_GROUP_PIPELINE_LATENCY_TRACE,
        .desc               = "When enabled, Fohh pipeline latency tracing is active ",
        .details            = "0: feature is disabled\n"
                              "1: feature is enabled\n"
                              "default: 0",
        .val_type           = config_type_int,
        .value_traits       = config_value_traits_feature_enablement,
        .component_types    = config_component_pbroker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_cust | config_target_gid_type_global,
        .int_range_lo       = CONFIG_FEATURE_PSE_FOHH_PIPELINE_LATENCY_TRACE_MIN,
        .int_range_hi       = CONFIG_FEATURE_PSE_FOHH_PIPELINE_LATENCY_TRACE_MAX,
        .int_default        = CONFIG_FEATURE_PSE_FOHH_PIPELINE_LATENCY_TRACE_DEFAULT
    },
    {
        .key                = ZPN_PSE_MTUNNEL_FIN_EXPIRE_TIME_S,
        .feature_group      = FEATURE_GROUP_BROKER_MTUNNEL,
        .desc               = "Configure fin expire timeout in seconds",
        .details            = "After receive fin, mtunnel will expire after specified time if it is still alive\n"
                              "default: 5min ",
        .val_type           = config_type_int,
        .value_traits       = config_value_traits_normal,
        .component_types    = config_component_pbroker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_cust | config_target_gid_type_global,
        .int_range_lo       = LOW_ZPN_PSE_MTUNNEL_FIN_EXPIRE_TIME_S,
        .int_range_hi       = HIGH_ZPN_PSE_MTUNNEL_FIN_EXPIRE_TIME_S,
        .int_default        = ZPN_PSE_MTUNNEL_FIN_EXPIRE_TIME_S_DEFAULT
    },
    {
        .key                = PBROKER_SARGE_UPGRADE_ENABLE,
        .feature_group      = FEATURE_GROUP_SARGE,
        .desc               = "Enable automatic upgrade of Private Broker sarge",
        .details            = "1: feature is enabled\n"
                              "0: feature is disabled \n"
                              "default: 0",
        .val_type           = config_type_int,
        .value_traits       = config_value_traits_feature_enablement,
        .component_types    = config_component_pbroker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_inst_grp | config_target_gid_type_cust | config_target_gid_type_global,
        .int_range_lo       = PBROKER_SARGE_UPGRADE_ENABLE_MIN,
        .int_range_hi       = PBROKER_SARGE_UPGRADE_ENABLE_MAX,
        .int_default        = DEFAULT_PBROKER_SARGE_UPGRADE_ENABLE
    },
    {
        .key                = AUTOMATIC_OS_UPGRADE_ENABLE,
        .feature_group      = FEATURE_GROUP_SARGE,
        .desc               = "Enable automatic upgrade of OS",
        .details            = "1: feature is enabled\n"
                              "0: feature is disabled \n"
                              "default: 0",
        .val_type           = config_type_int,
        .value_traits       = config_value_traits_feature_enablement,
        .component_types    = config_component_pbroker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_inst_grp | config_target_gid_type_cust | config_target_gid_type_global,
        .int_range_lo       = AUTOMATIC_OS_UPGRADE_ENABLE_MIN,
        .int_range_hi       = AUTOMATIC_OS_UPGRADE_ENABLE_MAX,
        .int_default        = DEFAULT_AUTOMATIC_OS_UPGRADE_ENABLE
    },
    {
        .key                = AUTOMATIC_FULL_OS_UPGRADE_ENABLE,
        .feature_group      = FEATURE_GROUP_SARGE,
        .desc               = "Enable automatic upgrade of entire OS",
        .details            = "1: feature is enabled\n"
                              "0: feature is disabled \n"
                              "default: 0",
        .val_type           = config_type_int,
        .value_traits       = config_value_traits_feature_enablement,
        .component_types    = config_component_pbroker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_inst_grp | config_target_gid_type_cust | config_target_gid_type_global,
        .int_range_lo       = AUTOMATIC_FULL_OS_UPGRADE_MIN,
        .int_range_hi       = AUTOMATIC_FULL_OS_UPGRADE_MAX,
        .int_default        = DEFAULT_AUTOMATIC_FULL_OS_UPGRADE_ENABLE
    },
    {
        .key                = SARGE_BACKUP_VERSION_ENABLE,
        .feature_group      = FEATURE_GROUP_SARGE,
        .desc               = "Enable/disable backup sarge version feature for sarge",
        .details            = "1: feature is enabled\n"
                              "0: feature is disabled \n"
                              "default: 0",
        .val_type           = config_type_int,
        .value_traits       = config_value_traits_feature_enablement,
        .component_types    = config_component_pbroker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_inst_grp | config_target_gid_type_cust | config_target_gid_type_global,
        .int_range_lo       = SARGE_BACKUP_VERSION_ENABLE_MIN,
        .int_range_hi       = SARGE_BACKUP_VERSION_ENABLE_MAX,
        .int_default        = DEFAULT_SARGE_BACKUP_VERSION_ENABLE
    },
    {
        .key                = CONFIG_FEATURE_APP_THREAD_HEARTBEAT_OVERRIDE_PSE,
        .feature_group      = FEATURE_GROUP_APP_THREAD_HEARTBEAT_OVERRIDE,
        .desc               = "When enabled, Reset App Thread Heartbeat for PSE to new value in seconds",
        .details            = "default: 20s, Value range is 20-300s",
        .val_type           = config_type_int,
        .value_traits       = config_value_traits_normal,
        .component_types    = config_component_pbroker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_cust | config_target_gid_type_global,
        .int_range_lo       = APP_THREAD_HEARTBEAT_OVERRIDE_MIN,
        .int_range_hi       = APP_THREAD_HEARTBEAT_OVERRIDE_MAX,
        .int_default        = APP_THREAD_HEARTBEAT_OVERRIDE_DEFAULT
    },
    {
        .key                = PSE_OAUTH_ENROLL_DISABLE,
        .feature_group      = FEATURE_GROUP_SARGE,
        .desc               = "Enable enrollment via OAuth service",
        .details            = "1: OAuth Enrollment disabled\n"
                              "0: OAuth Enrollment enabled \n"
                              "default: 0 (Oauth Enrollment Enabled)",
        .val_type           = config_type_int,
        .value_traits       = config_value_traits_feature_enablement,
        .component_types    = config_component_pbroker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_inst_grp | config_target_gid_type_cust | config_target_gid_type_global,
        .int_range_lo       = PSE_OAUTH_ENROLL_DISABLE_MIN,
        .int_range_hi       = PSE_OAUTH_ENROLL_DISABLE_MAX,
        .int_default        = DEFAULT_PSE_OAUTH_ENROLL_DISABLE
    }
};

static struct zpath_config_override_desc zpn_broker_config_override_flags_descriptions_public_broker[] = {
    /* Config overrides used by Broker */
    {
        .key                = CONFIG_FEATURE_ALT_CLOUD,
        .desc               = CONFIG_FEATURE_ALT_CLOUD_DESC,
        .details            = "1: feature is enabled.\n"
                              "0: feature is disabled.\n"
                              "Order of check: component id, root customer gid, global id\n"
                              "default: 1, feature is enabled",
        .val_type           = config_type_int,
        .component_types    = config_component_broker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_zsroot |  config_target_gid_type_global,
        .int_range_lo       = CONFIG_FEATURE_ALT_CLOUD_DISABLED,
        .int_range_hi       = CONFIG_FEATURE_ALT_CLOUD_ENABLED,
        .int_default        = CONFIG_FEATURE_ALT_CLOUD_DEFAULT_VALUE,
        .feature_group      = FEATURE_GROUP_ALT_CLOUD,
        .value_traits       = config_value_traits_feature_enablement,
    },
    {
        .key                = BROKER_FEATURE_ZVPN_DNS_CHECK_ERROR_DELAY_US,
        .desc               = "Dns check route optimisation for ZVPN",
        .details            = "This config enables broker to optimize the zpn_dns_assistant check route for ZVPN \n"
                              "The zpn_dns_assistant_check is sent to the dispatcher that requested it instead of the entire pool.\n"
                              "Order of check: customer id, global\n"
                              "Default: 50*1000",
        .val_type           = config_type_int,
        .component_types    = config_component_broker , // no PSE
        .target_gid_types   = config_target_gid_type_cust | config_target_gid_type_global,
        .int_range_lo       = 0, // can be 0, to disable the delay
        .int_range_hi       = BROKER_FEATURE_DNS_CHECK_ERROR_DELAY_US_MAX_US, // same as assistant
        .int_default        = DEFAULT_BROKER_FEATURE_DNS_CHECK_ERROR_DELAY_US, // same as assistant
        .feature_group      = FEATURE_GROUP_EXTRANET,
        .value_traits       = config_value_traits_normal,
    },

    {
        .key                = DNS_TXT_QUERY_SUPPORT_FEATURE,
        .desc               = DNS_TXT_QUERY_SUPPORT_FEATURE_DESC,
        .details            = "1: feature is enabled.\n"
                              "0: feature is disabled.\n"
                              "Order of check: customer gid, global id\n"
                              "default: 1, feature is enabled",
        .val_type           = config_type_int,
        .component_types    = config_component_broker,
        .target_gid_types   = config_target_gid_type_cust |  config_target_gid_type_global,
        .int_range_lo       = DNS_TXT_QUERY_SUPPORT_FEATURE_MIN,
        .int_range_hi       = DNS_TXT_QUERY_SUPPORT_FEATURE_MAX,
        .int_default        = DEFAULT_DNS_TXT_QUERY_SUPPORT_FEATURE,
        .feature_group      = FEATURE_GROUP_BROKER_DNS,
        .value_traits       = config_value_traits_feature_enablement,
    },

    {
        .key                = BROKER_SEND_AST_AUTH_REPORT_EVERY_1_MIN,
        .desc               = "When enabled, send zpn_ast_auth_report every 1 min instead of the default 4 min",
        .details            = "0: Disabled\n"
                              "1: Enabled\n"
                              "Order of check: component id, root customer gid, global id\n"
                              "default: 1 (Send auth report every 1 minute)",
        .val_type           = config_type_int,
        .component_types    = config_component_broker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_zsroot |  config_target_gid_type_global,
        .int_range_lo       = BROKER_SEND_AST_AUTH_REPORT_EVERY_1_MIN_LOW,
        .int_range_hi       = BROKER_SEND_AST_AUTH_REPORT_EVERY_1_MIN_HIGH,
        .int_default        = BROKER_SEND_AST_AUTH_REPORT_EVERY_1_MIN_DEFAULT,
        .feature_group      = FEATURE_GROUP_BROKER_DISPATCHER_GENERIC,
        .value_traits       = config_value_traits_feature_enablement,
    },

    {
        .key                = DTLS_FEATURE,
        .desc               = DTLS_FEATURE_DESC,
        .details            = "1: feature is enabled.\n"
                              "0: feature is disabled.\n"
                              "Order of check: component id, customer gid, root customer gid, global id\n"
                              "default: 0, feature is disabled",
        .val_type           = config_type_int,
        .component_types    = config_component_broker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_cust |
                              config_target_gid_type_zsroot |  config_target_gid_type_global,
        .int_range_lo       = DTLS_FEATURE_MIN,
        .int_range_hi       = DTLS_FEATURE_MAX,
        .int_default        = DEFAULT_BROKER_DTLS,
        .feature_group      = FEATURE_GROUP_DTLS,
        .value_traits       = config_value_traits_feature_enablement,
    },
    {
        .key                = DTLS_FEATURE_CLIENT,
        .desc               = DTLS_FEATURE_CLIENT_DESC,
        .details            = "1: feature is enabled.\n"
                              "0: feature is disabled.\n"
                              "Order of check: component id, customer gid, global id\n"
                              "default: 0, feature is disabled",
        .val_type           = config_type_int,
        .component_types    = config_component_broker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_cust |  config_target_gid_type_global,
        .int_range_lo       = DTLS_FEATURE_CLIENT_MIN,
        .int_range_hi       = DTLS_FEATURE_CLIENT_MAX,
        .int_default        = DEFAULT_PBROKER_DTLS_CLIENT,
        .feature_group      = FEATURE_GROUP_DTLS,
        .value_traits       = config_value_traits_feature_enablement,
    },

    {
        .key                = FOHH_CONNECTION_SETUP_TIMEOUT,
        .desc               = FOHH_CONNECTION_SETUP_TIMEOUT_DESC,
        .details            = "Control timeout config in seconds to delete incomplete fohh connections, between 10 seconds to 60 seconds\n"
                              "Order of check: component id, root customer gid, global id\n"
                              "default: 10 (i.e. fohh connection that doesn't progress to connected state within 10 secs will be deleted by default)",
        .val_type           = config_type_int,
        .component_types    = config_component_broker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_zsroot |  config_target_gid_type_global,
        .int_range_lo       = FOHH_CONNECTION_SETUP_TIMEOUT_SEC_MIN,
        .int_range_hi       = FOHH_CONNECTION_SETUP_TIMEOUT_SEC_MAX,
        .int_default        = FOHH_CONNECTION_SETUP_TIMEOUT_SEC,
        .feature_group      = FEATURE_GROUP_FOHH,
        .value_traits       = config_value_traits_normal,
    },

    {
        .key                = BROKER_CLIENT_AUTH_TIMEOUT,
        .desc               = BROKER_CLIENT_AUTH_TIMEOUT_DESC,
        .details            = "Control timeout config in seconds to delete unauthenticated client connections, between 20 seconds to 120 seconds\n"
                              "Order of check: component id, root customer gid, global id\n"
                              "default: 20 (i.e. client connection that doesn't complete authentication within 20 secs will be deleted by default)",
        .val_type           = config_type_int,
        .component_types    = config_component_broker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_zsroot | config_target_gid_type_global,
        .int_range_lo       = BROKER_CLIENT_AUTH_TIMEOUT_MIN_SEC,
        .int_range_hi       = BROKER_CLIENT_AUTH_TIMEOUT_MAX_SEC,
        .int_default        = BROKER_CLIENT_AUTH_TIMEOUT_SEC,
        .feature_group      = FEATURE_GROUP_BROKER_SESSION_AUTHENTICATION,
        .value_traits       = config_value_traits_normal,
    },

    {
        .key                = BROKER_CLIENT_IP_ANCHOR_RX_DATA_TIMEOUT,
        .desc               = BROKER_CLIENT_IP_ANCHOR_RX_DATA_TIMEOUT_DESC,
        .details            = "Control timeout config in seconds to delete idle client ip anchor connections, between 600 seconds to 86400 seconds\n"
                              "Order of check: component id, root customer gid, global id\n"
                              "default: 43200 (i.e. client ip anchor connection which stay idle for 43200 secs will be deleted by default)",
        .val_type           = config_type_int,
        .component_types    = config_component_broker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_zsroot |  config_target_gid_type_global,
        .int_range_lo       = BROKER_CLIENT_IP_ANCHOR_RX_DATA_TIMEOUT_SEC_MIN,
        .int_range_hi       = BROKER_CLIENT_IP_ANCHOR_RX_DATA_TIMEOUT_SEC_MAX,
        .int_default        = BROKER_CLIENT_IP_ANCHOR_RX_DATA_TIMEOUT_SEC,
        .feature_group      = FEATURE_GROUP_BROKER_CLIENT,
        .value_traits       = config_value_traits_normal,
    },
    {
        .key                = BROKER_DISABLE_FC_MTR,
        .desc               = BROKER_DISABLE_FC_MTR_DESC,
        .details            = "0: Flow Control for ZDX MTR is enabled.\n"
                              "1: Flow Control for ZDX MTR is disabled.\n"
                              "Order of check: component id, root customer gid, global id\n"
                              "default: 1, (i.e Flow control for ZDX MTR is disabled)",
        .val_type           = config_type_int,
        .component_types    = config_component_broker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_zsroot |  config_target_gid_type_global,
        .int_range_lo       = BROKER_DISABLE_FC_MTR_MIN,
        .int_range_hi       = BROKER_DISABLE_FC_MTR_MAX,
        .int_default        = DEFAULT_BROKER_DISABLE_FC_MTR,
        .feature_group      = FEATURE_GROUP_ZDX_BROKER_DISABLE_FC_MTR,
        .value_traits       = config_value_traits_feature_enablement,
    },

    {
        .key                = BROKER_FEATURE_DNS_STRICT_CHECK,
        .desc               = BROKER_FEATURE_DNS_STRICT_CHECK_DESC,
        .details            = "Broker strict DNS check will query AppConnector for an exact DNS type match of A or AAAA\n"
                              "1: feature is enabled.\n"
                              "0: feature is disabled.\n"
                              "Order of check: component id, component group id, customer gid, global id\n"
                              "default: 1, feature is enabled",
        .val_type           = config_type_int,
        .component_types    = config_component_broker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_cust | config_target_gid_type_global,
        .int_range_lo       = BROKER_FEATURE_DNS_STRICT_CHECK_MIN,
        .int_range_hi       = BROKER_FEATURE_DNS_STRICT_CHECK_MAX,
        .int_default        = DEFAULT_BROKER_FEATURE_DNS_STRICT_CHECK,
        .feature_group      = FEATURE_GROUP_BROKER_DNS_STRICT_CHECK,
        .value_traits       = config_value_traits_feature_enablement,
    },

    {
        .key                = BROKER_FEATURE_SIEM_QUEUE_SHRINKAGE,
        .desc               = BROKER_FEATURE_SIEM_QUEUE_SHRINKAGE_DESC,
        .details            = "Wait time before broker to slogger SIEM TX queue gets shrunk to 1MB in case of persistent FOHH connection backoff"
                              "0:    (feature disabled)"
                              "1s:   Minimum wait time before shrinking queue"
                              "7day: Maximum wait time before shrinking queue"
                              "Order of check: component id, customer gid, global id\n"
                              "default: 900, feature is enabled by default, configure a value to change it to non-default",
        .val_type           = config_type_int,
        .component_types    = config_component_broker,
        .target_gid_types   = config_target_gid_type_cust | config_target_gid_type_inst | config_target_gid_type_global,
        .int_range_lo       = BROKER_SIEM_QUEUE_SHRINKAGE_WAIT_TIME_INTERVAL_MIN,
        .int_range_hi       = BROKER_SIEM_QUEUE_SHRINKAGE_WAIT_TIME_INTERVAL_MAX,
        .int_default        = BROKER_SIEM_QUEUE_SHRINKAGE_WAIT_TIME_INTERVAL_DEFAULT,
        .feature_group      = FEATURE_GROUP_BROKER_SIEM,
        .value_traits       = config_value_traits_normal,
    },


    {
        .key                = DTLS_FEATURE_MTU,
        .desc               = DTLS_FEATURE_MTU_DESC,
        .details            = "Order of check: component id, customer gid\n"
                              "default: 1482 bytes (DEFAULT_STREAM_MTU)",
        .val_type           = config_type_int,
        .component_types    = config_component_broker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_cust,
        .int_range_lo       = LOW_DTLS_MTU,
        .int_range_hi       = HIGH_DTLS_MTU,
        .int_default        = DEFAULT_STREAM_MTU,
        .feature_group      = FEATURE_GROUP_DTLS,
        .value_traits       = config_value_traits_normal,
    },

    {
        .key                = RATE_LIMIT_STATUS_FEATURE,
        .desc               = RATE_LIMIT_STATUS_FEATURE_DESC,
        .details            = "1: feature is enabled.\n"
                              "0: feature is disabled.\n"
                              "Order of check: component id, customer gid, root customer gid, global id\n"
                              "default: 1, feature is enabled",
        .val_type           = config_type_int,
        .component_types    = config_component_broker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_cust |
                              config_target_gid_type_zsroot |  config_target_gid_type_global,
        .int_range_lo       = RATE_LIMIT_STATUS_MIN,
        .int_range_hi       = RATE_LIMIT_STATUS_MAX,
        .int_default        = DEFAULT_RATE_LIMIT_STATUS,
        .feature_group      = FEATURE_GROUP_BROKER_RATE_LIMITING,
        .value_traits       = config_value_traits_feature_enablement,
    },

    {
        .key                = CC_RATE_LIMIT_STATUS_FEATURE,
        .desc               = RATE_LIMIT_STATUS_FEATURE_DESC,
        .details            = "Applies only to BC/CC clients.\n"
                              "1: feature is enabled.\n"
                              "0: feature is disabled.\n"
                              "Order of check: component id, customer gid, global id\n"
                              "default: 1, feature is enabled",
        .val_type           = config_type_int,
        .component_types    = config_component_broker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_cust | config_target_gid_type_zsroot |  config_target_gid_type_global,
        .int_range_lo       = RATE_LIMIT_STATUS_MIN,
        .int_range_hi       = RATE_LIMIT_STATUS_MAX,
        .int_default        = DEFAULT_RATE_LIMIT_STATUS,
        .feature_group      = FEATURE_GROUP_BROKER_RATE_LIMITING,
        .value_traits       = config_value_traits_feature_enablement,
    },

    {
        .key                = RATE_LIMIT_MAX_MT_REQ_RATE,
        .desc               = RATE_LIMIT_STATUS_FEATURE_DESC,
        .details            = "Number of mtunnel requests allowed per second, on the same application, for each client.\n"
                              "Application: domain+port+proto\n"
                              "Order of check: component id, customer gid, global id\n"
                              "default: 100 mt/s",
        .val_type           = config_type_int,
        .component_types    = config_component_broker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_cust | config_target_gid_type_zsroot |  config_target_gid_type_global,
        .int_range_lo       = MAX_MT_REQ_RATE_MIN_VAL,
        .int_range_hi       = MAX_MT_REQ_RATE_MAX_VAL,
        .int_default        = DEFAULT_MAX_MT_REQ_RATE,
        .feature_group      = FEATURE_GROUP_BROKER_RATE_LIMITING,
        .value_traits       = config_value_traits_normal,
    },

    {
        .key                = RATE_LIMIT_MT_REQ_BUCKET_SIZE,
        .desc               = RATE_LIMIT_STATUS_FEATURE_DESC,
        .details            = "Additional buffer for handling bursts of mtunnel requests on same application, should be set >=10x rate.\n"
                              "Order of check: component id, customer gid, global id\n"
                              "default: 1000 mtunnel requests",
        .val_type           = config_type_int,
        .component_types    = config_component_broker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_cust | config_target_gid_type_zsroot |  config_target_gid_type_global,
        .int_range_lo       = RATE_LIMIT_MT_REQ_BUCKET_SIZE_MIN_VAL,
        .int_range_hi       = RATE_LIMIT_MT_REQ_BUCKET_SIZE_MAX_VAL,
        .int_default        = DEFAULT_MT_REQ_BUCKET_SIZE,
        .feature_group      = FEATURE_GROUP_BROKER_RATE_LIMITING,
        .value_traits       = config_value_traits_normal,
    },

    {
        .key                = RATE_LIMIT_CC_MAX_MT_REQ_RATE,
        .desc               = RATE_LIMIT_STATUS_FEATURE_DESC,
        .details            = "Applies only to BC/CC clients.\n"
                              "Number of mtunnel requests allowed per second, on the same application, for each CC/BC client.\n"
                              "Application: domain+port+proto\n"
                              "Order of check: component id, customer gid, global id\n"
                              "default: 200 mt/s",
        .val_type           = config_type_int,
        .component_types    = config_component_broker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_cust | config_target_gid_type_zsroot |  config_target_gid_type_global,
        .int_range_lo       = CC_MAX_MT_REQ_RATE_MIN_VAL,
        .int_range_hi       = CC_MAX_MT_REQ_RATE_MAX_VAL,
        .int_default        = DEFAULT_CC_MAX_MT_REQ_RATE,
        .feature_group      = FEATURE_GROUP_BROKER_RATE_LIMITING,
        .value_traits       = config_value_traits_normal,
    },

    {
        .key                = RATE_LIMIT_CC_MT_REQ_BUCKET_SIZE,
        .desc               = RATE_LIMIT_STATUS_FEATURE_DESC,
        .details            = "Applies only to BC/CC clients.\n"
                              "Additional buffer for handling bursts of mtunnel requests on same application, should be set >=10x rate.\n"
                              "Order of check: component id, customer gid, global id\n"
                              "default: 2000 mtunnel requests",
        .val_type           = config_type_int,
        .component_types    = config_component_broker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_cust | config_target_gid_type_zsroot |  config_target_gid_type_global,
        .int_range_lo       = CC_MT_REQ_BUCKET_SIZE_MIN_VAL,
        .int_range_hi       = CC_MT_REQ_BUCKET_SIZE_MAX_VAL,
        .int_default        = DEFAULT_CC_MT_REQ_BUCKET_SIZE,
        .feature_group      = FEATURE_GROUP_BROKER_RATE_LIMITING,
        .value_traits       = config_value_traits_normal,
    },

    {
        .key                = RATE_LIMIT_MAX_NEW_APP_RATE,
        .desc               = RATE_LIMIT_STATUS_FEATURE_DESC,
        .details            = "Number of mtunnel requests allowed per second, on different applications, for each client.\n"
                              "Application: domain+port+proto\n"
                              "Order of check: component id, customer gid, global id\n"
                              "default: 100 mt/s",
        .val_type           = config_type_int,
        .component_types    = config_component_broker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_cust | config_target_gid_type_zsroot |  config_target_gid_type_global,
        .int_range_lo       = MAX_NEW_APP_RATE_MIN_VAL,
        .int_range_hi       = MAX_NEW_APP_RATE_MAX_VAL,
        .int_default        = DEFAULT_MAX_NEW_APP_RATE,
        .feature_group      = FEATURE_GROUP_BROKER_RATE_LIMITING,
        .value_traits       = config_value_traits_normal,
    },

    {
        .key                = RATE_LIMIT_NEW_APP_BUCKET_SIZE,
        .desc               = RATE_LIMIT_STATUS_FEATURE_DESC,
        .details            = "Additional buffer for handling bursts of mtunnel requests on different applications, should be set >=10x rate.\n"
                              "Order of check: component id, customer gid, global id\n"
                              "default: 1000 mtunnel requests",
        .val_type           = config_type_int,
        .component_types    = config_component_broker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_cust | config_target_gid_type_zsroot |  config_target_gid_type_global,
        .int_range_lo       = NEW_APP_BUCKET_SIZE_MIN_VAL,
        .int_range_hi       = NEW_APP_BUCKET_SIZE_MAX_VAL,
        .int_default        = DEFAULT_NEW_APP_BUCKET_SIZE,
        .feature_group      = FEATURE_GROUP_BROKER_RATE_LIMITING,
        .value_traits       = config_value_traits_normal,
    },

    {
        .key                = BROKER_CLIENT_PATH_CACHE_MAX_SIZE_PER_DOMAIN,
        .desc               = BROKER_CLIENT_PATH_CACHE_MAX_SIZE_PER_DOMAIN_DESC,
        .details            = "Applies only to BC/CC/SIPA clients and not ZCC.\n"
                              "Order of check: component id, root customer gid, global id\n"
                              "default: 10 (For eg. if 10, hash (user_id %10) is the hash key)",
        .val_type           = config_type_int,
        .component_types    = config_component_broker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_zsroot |  config_target_gid_type_global,
        .int_range_lo       = BROKER_CLIENT_PATH_CACHE_MAX_SIZE_PER_DOMAIN_MIN,
        .int_range_hi       = BROKER_CLIENT_PATH_CACHE_MAX_SIZE_PER_DOMAIN_MAX,
        .int_default        = DEFAULT_BROKER_CLIENT_PATH_CACHE_MAX_SIZE_PER_DOMAIN,
        .feature_group      = FEATURE_GROUP_REDIRECT_POLICY,
        .value_traits       = config_value_traits_normal,
    },
    {
        .key                = BROKER_FEATURE_AUTH_STATE_LOG,
        .desc               = BROKER_FEATURE_AUTH_STATE_LOG_DESC,
        .details            = "Auth state log is to detect and log the changes in client's dynamic attr i.e saml,scim,trusted n/w etc.\n"
                              "1: feature is enabled.\n"
                              "0: feature is disabled.\n"
                              "Order of check: component id, component group id, customer gid, root customer gid, global id\n"
                              "default: 1, feature is enabled",
        .val_type           = config_type_int,
        .component_types    = config_component_broker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_cust | config_target_gid_type_zsroot |  config_target_gid_type_global,
        .int_range_lo       = BROKER_FEATURE_AUTH_STATE_LOG_MIN,
        .int_range_hi       = BROKER_FEATURE_AUTH_STATE_LOG_MAX,
        .int_default        = DEFAULT_BROKER_FEATURE_AUTH_STATE_LOG,
        .feature_group      = FEATURE_GROUP_BROKER_CLIENT,
        .value_traits       = config_value_traits_feature_enablement,
    },
    {
        .key                = QUICKACK_CONFIG_CLIENT,
        .desc               = "Enable/disable quickack for a customer.",
        .details            = "This config enables the socket setting for quickack in accept/listen and connect of the socket"
                              "to force the delayed acknowledgements to stop.\n"
                              "0: Quickack is disabled\n"
                              "1: Quickack is enabled\n"
                              "Order of check: customer gid, global\n"
                              "default: 0, feature is disabled.",
        .val_type           = config_type_int,
        .component_types    = config_component_broker,
        .target_gid_types   = config_target_gid_type_cust | config_target_gid_type_global,
        .int_range_lo       = 0,
        .int_range_hi       = 1,
        .int_default        = DEFAULT_BROKER_QUICKACK,
        .feature_group      = FEATURE_GROUP_QUICKACK,
        .value_traits       = config_value_traits_feature_enablement,
    },
    {
        .key                = QUICKACK_READ_CONFIG_CLIENT,
        .desc               = "Enable/disable quickack at read callback for a customer.",
        .details            = "This config enables the socket setting for quickack at "
                              "read callback to force the delayed acknowledgements to stop.\n"
                              "0: Quickack is disabled\n"
                              "1: Quickack is enabled\n"
                              "Order of check: customer gid, global\n"
                              "default: 0, feature is disabled.",
        .val_type           = config_type_int,
        .component_types    = config_component_broker,
        .target_gid_types   = config_target_gid_type_cust | config_target_gid_type_global,
        .int_range_lo       = 0,
        .int_range_hi       = 1,
        .int_default        = DEFAULT_BROKER_QUICKACK_READ,
        .feature_group      = FEATURE_GROUP_QUICKACK,
        .value_traits       = config_value_traits_feature_enablement,
    },
    {
        .key                = QUICKACK_CONFIG_ASSISTANT,
        .desc               = "Enable quickack mode if set or disable quickack mode if cleared on app connector side for data connection between app connector and broker",
        .details            = "0: Quickack on assistant is disabled\n"
                              "1: Quickack on assistant is enabled\n"
                              "Order of check: component id, component group id, customer gid, global\n"
                              "default: 0 (i.e. Quickack on assistant is disabled by default)",
        .val_type           = config_type_int,
        .component_types    = config_component_broker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_inst_grp | config_target_gid_type_cust | config_target_gid_type_global,
        .int_range_lo       = 0,
        .int_range_hi       = 1,
        .int_default        = DEFAULT_ASST_QUICKACK,
        .feature_group      = FEATURE_GROUP_QUICKACK,
        .value_traits       = config_value_traits_feature_enablement,
    },
    {
        .key                = MULTIDC_REDIRECT_VAL_FOR_ASSISTANT,
        .desc               = "If MultiDC redirect val for assistant is set.Redirect broker will send brokers from multi dc as redirect message to assistant",
        .details            = "0: MultiDC redirect on assistant is disabled\n"
                              "1: MultiDC redirect on assistant is enabled\n"
                              "Order of check: customer gid, global\n"
                              "default: 1 (i.e. MultiDC redirect for assistant is enabled by default)",
        .val_type           = config_type_int,
        .component_types    = config_component_broker,
        .target_gid_types   = config_target_gid_type_cust | config_target_gid_type_global,
        .int_range_lo       = MULTIDC_REDIRECT_MIN_VAL_FOR_ASSISTANT,
        .int_range_hi       = MULTIDC_REDIRECT_MAX_VAL_FOR_ASSISTANT ,
        .int_default        = DEFAULT_MULTIDC_REDIRECT_VAL_FOR_ASSISTANT,
        .feature_group      = FEATURE_GROUP_BROKER_BALANCE_REDIRECT,
        .value_traits       = config_value_traits_feature_enablement,
    },
    {
        .key                = MULTIDC_REDIRECT_VAL_FOR_PSE,
        .desc               = "If MultiDC redirect val for PSE is set.Redirect broker will send brokers from multi dc as redirect message to PSE",
        .details            = "0: MultiDC redirect on PSE is disabled\n"
                              "1: MultiDC redirect on PSE is enabled\n"
                              "Order of check: customer gid, global\n"
                              "default: 1 (i.e. MultiDC redirect for PSE is enabled by default)",
        .val_type           = config_type_int,
        .component_types    = config_component_broker,
        .target_gid_types   = config_target_gid_type_cust | config_target_gid_type_global,
        .int_range_lo       = MULTIDC_REDIRECT_MIN_VAL_FOR_PSE,
        .int_range_hi       = MULTIDC_REDIRECT_MAX_VAL_FOR_PSE,
        .int_default        = DEFAULT_MULTIDC_REDIRECT_VAL_FOR_PSE,
        .feature_group      = FEATURE_GROUP_BROKER_BALANCE_REDIRECT,
        .value_traits       = config_value_traits_feature_enablement,
    },
    {
        .key                = MULTIDC_REDIRECT_VAL_FOR_SITEC,
        .desc               = "If MultiDC redirect val for PCC is set.Redirect broker will send brokers from multi dc as redirect message to PCC",
        .details            = "0: MultiDC redirect for PCC is disabled\n"
                              "1: MultiDC redirect for PCC is enabled\n"
                              "Order of check: customer gid, global\n"
                              "default: 0 (i.e. MultiDC redirect for PCC is disabled by default)",
        .val_type           = config_type_int,
        .component_types    = config_component_broker,
        .target_gid_types   = config_target_gid_type_cust | config_target_gid_type_global,
        .int_range_lo       = MULTIDC_REDIRECT_MIN_VAL_FOR_SITEC,
        .int_range_hi       = MULTIDC_REDIRECT_MAX_VAL_FOR_SITEC,
        .int_default        = DEFAULT_MULTIDC_REDIRECT_VAL_FOR_SITEC,
        .feature_group      = FEATURE_GROUP_BROKER_BALANCE_REDIRECT,
        .value_traits       = config_value_traits_feature_enablement,
    },
    {
        .key                = QUICKACK_READ_CONFIG_ASSISTANT,
        .desc               = "Enable quickack mode if set or disable quickack mode if cleared on app connector side for data connection between app connector and broker",
        .details            = "0: Quickack read on assistant is disabled\n"
                              "1: Quickack read on assistant is enabled\n"
                              "Order of check: component id, component group id, customer gid, global\n"
                              "default: 0 (i.e. Quickack read on assistant is disabled by default)",
        .val_type           = config_type_int,
        .component_types    = config_component_broker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_inst_grp | config_target_gid_type_cust | config_target_gid_type_global,
        .int_range_lo       = 0,
        .int_range_hi       = 1,
        .int_default        = DEFAULT_ASST_QUICKACK_READ,
        .feature_group      = FEATURE_GROUP_QUICKACK,
        .value_traits       = config_value_traits_feature_enablement,

    },
    {
        .key                = LIBEVENT_LOW_WRITE_WATERMARK_BROKER,
        .desc               = "Enable Low Write Watermark on Broker",
        .details            = "0: LibEvent Low Write Watermark on Broker is disabled\n"
                              "1: LibEvent Low Write Watermark on Broker is enabled\n"
                              "Order of check: customer gid, global\n"
                              "default: 0 (i.e. LibEvent Low Write Watermark on Broker is disabled by default",
        .val_type           = config_type_int,
        .component_types    = config_component_broker,
        .target_gid_types   = config_target_gid_type_cust | config_target_gid_type_global,
        .int_range_lo       = LIBEVENT_LOW_WRITE_WATERMARK_MIN,
        .int_range_hi       = LIBEVENT_LOW_WRITE_WATERMARK_MAX,
        .int_default        = LIBEVENT_LOW_WRITE_WATERMARK_DEFAULT,
        .feature_group      = FEATURE_GROUP_LIBEVENT_LOW_WRITE_WATERMARK,
        .value_traits       = config_value_traits_feature_enablement,

    },

    {
        .key                = ASST_ZISTATS_UPLOAD_CONFIG,
        .desc               = "Enable/disable connector stats upload to ZI endpoint.",
        .details            = "This config enables broker to upload connector stats to zi endpoint.\n"
                              "Order of check: customer gid, global\n"
                              "default: 0, feature is disabled.",
        .val_type           = config_type_int,
        .component_types    = config_component_broker,
        .target_gid_types   = config_target_gid_type_cust | config_target_gid_type_global,
        .int_range_lo       = 0,
        .int_range_hi       = 1,
        .int_default        = DEFAULT_ASST_ZISTATS_UPLOAD_DISABLED,
        .feature_group      = FEATURE_GROUP_APP_CONNECTOR_STATS,
        .value_traits       = config_value_traits_feature_enablement,
    },
    {
        .key                = PBROKER_ZISTATS_UPLOAD_CONFIG,
        .desc               = "Enable/disable Private broker stats upload to zi",
        .details            = "This config enables broker to upload private broker stats to zi endpoint.\n"
                              "Order of check: customer gid, global\n"
                              "default: 0, feature is disabled.",
        .val_type           = config_type_int,
        .component_types    = config_component_broker,
        .target_gid_types   = config_target_gid_type_cust | config_target_gid_type_global,
        .int_range_lo       = 0,
        .int_range_hi       = 1,
        .int_default        = DEFAULT_PBROKER_ZISTATS_UPLOAD_DISABLED,
        .feature_group      = FEATURE_GROUP_PRIVATE_SERVICE_EDGE_STATS,
        .value_traits       = config_value_traits_feature_enablement,
    },
    {
        .key                = SITEC_ZISTATS_UPLOAD_CONFIG,
        .desc               = "Enable/disable site controller stats upload to zi",
        .details            = "This config enables broker to upload site controller stats to zi endpoint.\n"
                                "Order of check: customer gid, global\n"
                                "default: 0, feature is disabled.",
        .val_type           = config_type_int,
        .component_types    = config_component_broker,
        .target_gid_types   = config_target_gid_type_cust | config_target_gid_type_global,
        .int_range_lo       = 0,
        .int_range_hi       = 1,
        .int_default        = DEFAULT_SITEC_ZISTATS_UPLOAD_DISABLED,
        .feature_group      = FEATURE_GROUP_SITE_CONTROLLER_STATS,
        .value_traits       = config_value_traits_feature_enablement,
    },
    {
        .key                = BROKER_LATENCY_PROBE,
        .desc               = "latency based broker feature",
        .details            = "Enable/Disable Latency based broker selection feature.\n"
                              "Order of check: component id, customer gid or root customer id, global\n"
                              "default: 0, feature is disabled.",
        .val_type           = config_type_int,
        .component_types    = config_component_broker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_cust |
                              config_target_gid_type_zsroot | config_target_gid_type_global,
        .int_range_lo       = BROKER_LATENCY_PROBE_FEATURE_MIN,
        .int_range_hi       = BROKER_LATENCY_PROBE_FEATURE_MAX,
        .int_default        = DEFAULT_BROKER_LATENCY_PROBE_FEATURE,
        .feature_group      = FEATURE_GROUP_LATENCY_BASED_BROKER,
        .value_traits       = config_value_traits_feature_enablement,
    },
    {
        .key                = CONFIG_FEATURE_BROKER_ZAPP_IDLE_TUNNEL_BURST_COUNT,
        .desc               = "idle tunnel burst packet counter",
        .details            = "latency based broker feature idle tunnel burst packet counter config.\n"
                              "Order of check: root customer id, global\n"
                              "Default: count is 20.",
        .val_type           = config_type_int,
        .component_types    = config_component_broker,
        .target_gid_types   = config_target_gid_type_zsroot | config_target_gid_type_global,
        .int_range_lo       = CONFIG_FEATURE_BROKER_ZAPP_IDLE_TUNNEL_BURST_COUNT_MIN,
        .int_range_hi       = CONFIG_FEATURE_BROKER_ZAPP_IDLE_TUNNEL_BURST_COUNT_MAX,
        .int_default        = DEFAULT_CONFIG_FEATURE_BROKER_ZAPP_IDLE_TUNNEL_BURST_COUNT,
        .feature_group      = FEATURE_GROUP_LATENCY_BASED_BROKER,
        .value_traits       = config_value_traits_normal,
    },
    {
        .key                = ZPN_LP_FEATURE_CONFIG,
        .desc               = "logical partitioning feature",
        .details            = "Enable/Disable Logical Partitioning feature.\n"
                              "Order of check: component id, root customer id, global\n"
                              "Default: 0 , feature is disabled",
        .val_type           = config_type_int,
        .component_types    = config_component_broker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_zsroot |  config_target_gid_type_global,
        .int_range_lo       = ZPN_LP_FEATURE_CONFIG_MIN,
        .int_range_hi       = ZPN_LP_FEATURE_CONFIG_MAX,
        .int_default        = ZPN_LP_FEATURE_CONFIG_DEFAULT,
        .feature_group      = FEATURE_GROUP_LOGICAL_PARTITION_REDIRECT,
        .value_traits       = config_value_traits_feature_enablement,
    },
    {
        .key                = ZPN_CONFIG_LP_PROFILE_VERSION,
        .desc               = "logical partitioning profile version",
        .details            = "LP profile version is auto generated through atlantic ui.\n"
                              "Order of check: component id, root customer id, global\n"
                              "Default: -1",
        .val_type           = config_type_int,
        .component_types    = config_component_broker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_zsroot |  config_target_gid_type_global,
        .int_range_lo       = ZPN_CONFIG_LP_PROFILE_VERSION_MIN,
        .int_range_hi       = ZPN_CONFIG_LP_PROFILE_VERSION_MAX,
        .int_default        = DEFAULT_ZPN_CONFIG_LP_PROFILE_VERSION,
        .feature_group      = FEATURE_GROUP_LOGICAL_PARTITION_REDIRECT,
        .value_traits       = config_value_traits_normal,
    },
    {
        .key                = CONFIG_FEATURE_LP_MDC_SDC_MIXUP_INST,
        .desc               = "Mix up partitioned and unpartitioned brokers while selecting brokers from secondary dc",
        .details            = "If config is set to 1;\n"
                                "\t We only iterate once over all secondary DCs. For each SDC in selected secondary DC list, "
                                    "when partitioned brokers are available, it will be considered for selection "
                                    "else unpartitioned brokers will be considered for selection in that DC.\n"
                                "If config is set to 0, \n"
                                    "\t first iteration considers only partitioned brokers for selection. "
                                    "If the selection yielded nothing, only then unpartitioned brokers are added to the mix..\n"
                                "Order of check: component id, root customer id, global\n"
                                "default: 0 , feature is disabled ",
        .val_type           = config_type_int,
        .component_types    = config_component_broker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_zsroot |  config_target_gid_type_global,
        .int_range_lo       = CONFIG_FEATURE_LP_MDC_SDC_MIXUP_INST_MIN,
        .int_range_hi       = CONFIG_FEATURE_LP_MDC_SDC_MIXUP_INST_MAX,
        .int_default        = CONFIG_FEATURE_LP_MDC_SDC_MIXUP_INST_DEFAULT,
        .feature_group      = FEATURE_GROUP_LOGICAL_PARTITION_REDIRECT,
        .value_traits       = config_value_traits_feature_enablement,
    },
    {
        .key                = BROKER_DNS_ASSISTANT_CHECK_UNICAST_FEATURE,
        .desc               = "Dns check route optimisation",
        .details            = "This config enables broker to optimize the zpn_dns_assistant check route \n"
                              "The zpn_dns_assistant_check is sent to the dispatcher that requested it instead of the entire pool.\n"
                              "Order of check: component id, root customer id, global\n"
                              "Default : 1 ; feature is Enabled",
        .val_type           = config_type_int,
        .component_types    = config_component_broker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_zsroot | config_target_gid_type_global,
        .int_range_lo       = BROKER_DNS_ASSISTANT_CHECK_UNICAST_FEATURE_MIN,
        .int_range_hi       = BROKER_DNS_ASSISTANT_CHECK_UNICAST_FEATURE_MAX,
        .int_default        = DEFAULT_BROKER_DNS_ASSISTANT_CHECK_UNICAST_FEATURE,
        .feature_group      = FEATURE_GROUP_BROKER_DNS,
        .value_traits       = config_value_traits_feature_enablement,
    },
    {
        .key                = CONFIG_FEATURE_BROKER_CPU_REPORT_ALGO,
        .desc               = "CPU report algo",
        .details            = "Feature: CPU report algo, Affected Components: Broker\n"
                               "Config override config.feature.broker.cpu_report_algo specifies how this public broker reports its cpu usage.\n"
                               "Possible values:\n"
                               "BROKER_CPU_REPORT_ALGO_SYSTEM                 = 1 : default, report system cpu usage only.\n"
                               "BROKER_CPU_REPORT_ALGO_SYSTEM_OR_THREAD_S     = 2 : report the higher of system cpu and average cpu of any thread over 60 seconds\n"
                               "BROKER_CPU_REPORT_ALGO_SYSTEM_OR_THREAD_L     = 3 : report the higher of system cpu and average cpu of any thread over 10 minutes\n"
                               "BROKER_CPU_REPORT_ALGO_SYSTEM_OR_GROUP_S      = 4 : report The higher of system cpu and average cpu of any thread group over 60 seconds.\n"
                               "BROKER_CPU_REPORT_ALGO_SYSTEM_OR_GROUP_L      = 5 : report The higher of system cpu and average cpu of any thread group over 10 minutes.\n"
                               "Order of check: component id, root customer id, global",
        .val_type           = config_type_int,
        .component_types    = config_component_broker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_zsroot | config_target_gid_type_global,
        .int_range_lo       = CONFIG_FEATURE_BROKER_CPU_REPORT_ALGO_MIN,
        .int_range_hi       = CONFIG_FEATURE_BROKER_CPU_REPORT_ALGO_MAX,
        .int_default        = BROKER_CPU_REPORT_ALGO_DEFAULT,
        .feature_group      = FEATURE_GROUP_BROKER_REDIRECT,
        .value_traits       = config_value_traits_normal,
    },
    {
        .key                = ZTHREAD_ON_CRASH_DO_BROKER_CONFIG_OVERRIDE_KEY,
        .desc               = "Reduce client wait time during core",
        .details            = "Parameters for config-override selection for extra actions on crash for broker\n"
                              "0: no action \t 1: no coredump \t 2: close all sockets\n"
                              "Order of check: component id, root customer id, global\n"
                              "Default: 2 (zthread_on_crash_do_close_all_sockets)",
        .val_type           = config_type_int,
        .component_types    = config_component_broker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_zsroot | config_target_gid_type_global,
        .int_range_lo       = ZTHREAD_ON_CRASH_DO_BROKER_CONFIG_OVERRIDE_KEY_MIN,
        .int_range_hi       = ZTHREAD_ON_CRASH_DO_BROKER_CONFIG_OVERRIDE_KEY_MAX,
        .int_default        = ZTHREAD_ON_CRASH_DO_BROKER_CONFIG_OVERRIDE_DEFAULT,
        .feature_group      = FEATURE_GROUP_BROKER,
        .value_traits       = config_value_traits_normal,
    },
    {
        .key                = BROKER_SIPA_CLIENT_STATUS_TIMEOUT_SEC,
        .desc               = "Sipa client timeout",
        .details            = "Configure the timeout for sipa client \n"
                                "This disconnects the client if no activity beyond the configured time \n"
                                "Order of check: component id, root customer id, global\n"
                                "Default: 900 secs",
        .val_type           = config_type_int,
        .component_types    = config_component_broker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_zsroot | config_target_gid_type_global,
        .int_range_lo       = BROKER_SIPA_CLIENT_STATUS_TIMEOUT_SEC_MIN,
        .int_range_hi       = BROKER_SIPA_CLIENT_STATUS_TIMEOUT_SEC_MAX,
        .int_default        = BROKER_SIPA_CLIENT_STATUS_TIMEOUT_DEFAULT_SEC,
        .feature_group      = FEATURE_GROUP_BROKER_SIPA_CLIENT,
        .value_traits       = config_value_traits_normal,
    },
    {
        .key                = BROKER_CLIENT_STATUS_TIMEOUT_SEC,
        .desc               = "Broker clients idle timeout for connections with fohh_status_xxx heartbeat",
        .details            = "Order of check - Public: component id, root customer id, global\n"
                              "default: 43200 sec (12 hr)",
        .val_type           = config_type_int,
        .component_types    = config_component_broker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_zsroot |
                              config_target_gid_type_global,
        .int_range_lo       = BROKER_CLIENT_STATUS_TIMEOUT_SEC_MIN, // 5 min
        .int_range_hi       = BROKER_CLIENT_STATUS_TIMEOUT_SEC_MAX, // 24 hrs
        .int_default        = BROKER_CLIENT_STATUS_TIMEOUT_DEFAULT_SEC,
        .feature_group      = FEATURE_GROUP_FOHH,
        .value_traits       = config_value_traits_normal,
    },
    {
        .key                = BROKER_CLIENT_ENABLE_CLEANUP_STALE,
        .desc               = "Enable or Disable cleanup stale ZCC connections",
        .details            = "0: Disable\n"
                              "1: Enable\n"
                              "DEFAULT: 0\n",
        .val_type           = config_type_int,
        .component_types    = config_component_broker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_cust | config_target_gid_type_zsroot | config_target_gid_type_global,
        .int_range_lo       = BROKER_CLIENT_ENABLE_CLEANUP_STALE_VAL_MIN,
        .int_range_hi       = BROKER_CLIENT_ENABLE_CLEANUP_STALE_VAL_MAX,
        .int_default        = DEFAULT_BROKER_CLIENT_ENABLE_CLEANUP_STALE,
        .feature_group      = FEATURE_GROUP_BROKER_CLIENT,
        .value_traits       = config_value_traits_feature_enablement,
    },
    {
        .key                 = PBROKER_FEATURE_AUTH_LOG_LSS,
        .desc                = "LSS auth logging for PSE",
        .details             = "Feature flag to enable/disable LSS auth logging support for PSE\n"
                               "Order of check: customer gid, root customer id, global\n"
                               "Default: 'disabled' ",
        .val_type            = config_type_str,
        .component_types     = config_component_broker,
        .target_gid_types    = config_target_gid_type_cust | config_target_gid_type_zsroot | config_target_gid_type_global,
        .int_range_lo        = 0,
        .int_range_hi        = 0,
        .int_default         = 0,
        .str_default         = DEFAULT_PBROKER_FEATURE_AUTH_LOG_LSS,
        .feature_group       = FEATURE_GROUP_PRIVATE_SERVICE_EDGE_LSS,
        .value_traits        = config_value_traits_feature_enablement,
        ZPATH_CONFIG_OVERRIDE_DESC_INLINE_SET_STR_VALID_CB(zpn_broker_config_override_str_validator)
    },
    {
        .key                 = SITEC_FEATURE_AUTH_LOG_LSS,
        .desc                = "LSS auth logging for site controller",
        .details             = "Feature flag to enable/disable LSS auth logging support for site controller\n"
                               "Order of check: customer gid, root customer id, global\n"
                               "Default: 'disabled' ",
        .val_type            = config_type_str,
        .component_types     = config_component_broker,
        .target_gid_types    = config_target_gid_type_cust | config_target_gid_type_zsroot | config_target_gid_type_global,
        .int_range_lo        = 0,
        .int_range_hi        = 0,
        .int_default         = 0,
        .str_default         = DEFAULT_SITEC_FEATURE_AUTH_LOG_LSS,
        .feature_group       = FEATURE_GROUP_SITE_CONTROLLER_LSS,
        .value_traits        = config_value_traits_feature_enablement,
        ZPATH_CONFIG_OVERRIDE_DESC_INLINE_SET_STR_VALID_CB(zpn_broker_config_override_str_validator)
    },
    {
        .key                = ZPN_BROKER_ENABLE_HOSTNAME_VALIDATION,
        .desc               = "Enable hostname validation",
        .details            = "Enable hostname validation coming from client auth\n"
                              "1: feature is enabled.\n"
                              "0: feature is disabled.\n"
                              "Order of check: component id, component group id, customer gid, root customer gid, global id\n"
                              "default: 0, hostname validation is disabled.",
        .val_type           = config_type_int,
        .component_types    = config_component_broker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_cust |
                              config_target_gid_type_zsroot | config_target_gid_type_global,
        .int_range_lo       = ZPN_BROKER_ENABLE_HOSTNAME_VALIDATION_MIN,
        .int_range_hi       = ZPN_BROKER_ENABLE_HOSTNAME_VALIDATION_MAX,
        .int_default        = ZPN_DEFAULT_BROKER_ENABLE_HOSTNAME_VALIDATION,
        .feature_group      = FEATURE_GROUP_BROKER_HOSTNAME_VALIDATION,
        .value_traits       = config_value_traits_feature_enablement,
    },
    {
        .key                = CONFIG_FEATURE_BROKER_SEND_PENDING_MTUNNEL_END,
        .desc               = CONFIG_FEATURE_BROKER_SEND_PENDING_MTUNNEL_END_DESC,
        .details            = "0: Do not send pending mtunnel_end to assistant\n"
                              "1: Send pending mtunnel_end to assistant\n"
                              "Order of check: broker gid, customer gid, global\n"
                              "default: 1 (Enabled)",
        .val_type           = config_type_int,
        .component_types    = config_component_broker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_cust |
                              config_target_gid_type_global,
        .int_range_lo       = MIN_CONFIG_FEATURE_BROKER_SEND_PENDING_MTUNNEL_END,
        .int_range_hi       = MAX_CONFIG_FEATURE_BROKER_SEND_PENDING_MTUNNEL_END,
        .int_default        = DEFAULT_CONFIG_FEATURE_BROKER_SEND_PENDING_MTUNNEL_END,
        .feature_group      = FEATURE_GROUP_BROKER_MTUNNEL,
        .value_traits       = config_value_traits_feature_enablement,
    },
    {
        .key                = BROKER_MAX_MTUNNEL_PER_USER,
        .desc               = "max number of concurrent mtunnel per user that is allowed",
        .details            = "Order of check: customer gid\n"
                              "max: 2147483647\n"
                              "default: 2147483647",
        .val_type           = config_type_int,
        .component_types    = config_component_broker,
        .target_gid_types   = config_target_gid_type_cust | config_target_gid_type_zsroot,
        .int_range_lo       = BROKER_MAX_MTUNNEL_PER_USER_LOW,
        .int_range_hi       = BROKER_MAX_MTUNNEL_PER_USER_HIGH,
        .int_default        = BROKER_MAX_MTUNNEL_PER_USER_DEFAULT,
        .feature_group      = FEATURE_GROUP_BROKER_MTUNNEL,
        .value_traits       = config_value_traits_normal,
    },
    {
        .key                = CONFIG_FEATURE_BROKER_SYNC_PAUSE_WALLY,
        .desc               = "Global database sync pause/resume",
        .details            = "Instructs broker to pause/resume db sync on certain tables in master\n"
                              "1: feature is enabled.\n"
                              "0: feature is disabled.\n"
                              "Order of check: component id, root customer id, global\n"
                              "default: 0, feature is disabled(no tables are paused)",
        .val_type           = config_type_int,
        .component_types    = config_component_broker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_zsroot | config_target_gid_type_global,
        .int_range_lo       = CONFIG_FEATURE_BROKER_SYNC_PAUSE_WALLY_DISABLED,
        .int_range_hi       = CONFIG_FEATURE_BROKER_SYNC_PAUSE_WALLY_ENABLED,
        .int_default        = CONFIG_FEATURE_BROKER_SYNC_PAUSE_WALLY_DEFAULT,
        .feature_group      = FEATURE_GROUP_BROKER_SYNC_PAUSE,
        .value_traits       = config_value_traits_feature_enablement,
    },
    {
        .key                = CONFIG_FEATURE_BROKER_CLIENT_DOUBLE_PRECISION_GEO_LOCATION_PCT,
        .desc               = "Double based precision location calculation percentage",
        .details            = "100: double based precise location calculation feature is fully enabled; \n"
                              "0: double based precise location calculation feature is disabled  \n"
                              "between 0 and 100: broker has a randomized chance for the client location to be calculated based on double with precision;\n"
                              "Order of check: Broker gid, global\n"
                              "default: 100 (i.e. precise location enabled)",
        .val_type           = config_type_int,
        .component_types    = config_component_broker,
        .target_gid_types   = config_target_gid_type_inst |
                              config_target_gid_type_global,
        .int_range_lo       = MIN_CONFIG_FEATURE_BROKER_CLIENT_DOUBLE_PRECISION_GEO_LOCATION_PCT,
        .int_range_hi       = MAX_CONFIG_FEATURE_BROKER_CLIENT_DOUBLE_PRECISION_GEO_LOCATION_PCT,
        .int_default        = DEFAULT_CONFIG_FEATURE_BROKER_CLIENT_DOUBLE_PRECISION_GEO_LOCATION_PCT,
        .feature_group      = FEATURE_GROUP_BROKER_REDIRECT,
        .value_traits       = config_value_traits_normal,
    },
    {
        .key                = BROKER_FEATURE_MALLOC_FASTBIN,
        .desc               = CONFIG_FEATURE_BROKER_MALLOC_FASTBIN_DESC,
        .details            = "Malloc Fastbin usage can be controlled by Mallopt; \n"
                              "by default, it is enabled and set to max chunk size of 128; \n"
                              "Setting the Malloc Fastbin flag to 0, disables fastbin usage; \n"
                              "we restrict to 128 or 0.",
        .val_type           = config_type_int,
        .component_types    = config_component_broker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_inst_grp | config_target_gid_type_global,
        .int_range_lo       = BROKER_FEATURE_MALLOC_FASTBIN_DISABLED,
        .int_range_hi       = BROKER_FEATURE_MALLOC_FASTBIN_ENABLED,
        .int_default        = BROKER_FEATURE_MALLOC_FASTBIN_DEFAULT,
        .feature_group      = FEATURE_GROUP_BROKER,
        .value_traits       = config_value_traits_normal,
    },
    {
        .key                = CONFIG_FEATURE_BROKER_ADAPTIVE_LOAD_MONITOR,
        .desc               = CONFIG_FEATURE_ADAPTIVE_LOAD_MONITOR_DESC,
        .details            = "Instructs broker enable/disable the adaptive load monitoring\n"
                              "1: feature is enabled.\n"
                              "0: feature is disabled.\n"
                              "Order of check: component id, global\n"
                              "default: 1, feature is enabled",
        .val_type           = config_type_int,
        .component_types    = config_component_broker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_global,
        .int_range_lo       = MIN_CONFIG_FEATURE_BROKER_ADAPTIVE_LOAD_MONITOR,
        .int_range_hi       = MAX_CONFIG_FEATURE_BROKER_ADAPTIVE_LOAD_MONITOR,
        .int_default        = DEFAULT_CONFIG_FEATURE_BROKER_ADAPTIVE_LOAD_MONITOR,
        .feature_group      = FEATURE_GROUP_BROKER_REDIRECT,
        .value_traits       = config_value_traits_feature_enablement,
    },
    {
        .key                = CONFIG_FEATURE_BROKER_ADAPTIVE_LOAD_UPDATE_INTERVAL,
        .desc               = CONFIG_FEATURE_BROKER_ADAPTIVE_LOAD_UPDATE_INTERVAL_DESC,
        .details            = "Control adaptive load update interval in seconds\n"
                              "Minimum: 5 seconds\n"
                              "Maximum: 30 seconds\n"
                              "Order of check: component id, global\n"
                              "default: 10 seconds",
        .val_type           = config_type_int,
        .component_types    = config_component_broker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_global,
        .int_range_lo       = MIN_CONFIG_FEATURE_BROKER_ADAPTIVE_LOAD_UPDATE_INTERVAL,
        .int_range_hi       = MAX_CONFIG_FEATURE_BROKER_ADAPTIVE_LOAD_UPDATE_INTERVAL,
        .int_default        = DEFAULT_CONFIG_FEATURE_BROKER_ADAPTIVE_LOAD_UPDATE_INTERVAL,
        .feature_group      = FEATURE_GROUP_BROKER_REDIRECT,
        .value_traits       = config_value_traits_normal,
    },
    {
        .key                = CONFIG_FEATURE_BROKER_ADAPTIVE_LOAD_CPU_SPIKE_THRESHOLD,
        .desc               = CONFIG_FEATURE_BROKER_ADAPTIVE_LOAD_CPU_SPIKE_THRESHOLD_DESC,
        .details            = "Control threshold (in percent) for detecting spike in cpu utilisation\n"
                              "Minimum: 2 percent\n"
                              "Maximum: 100 percent\n"
                              "Order of check: component id, global\n"
                              "default: 20percent",
        .val_type           = config_type_int,
        .component_types    = config_component_broker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_global,
        .int_range_lo       = MIN_CONFIG_FEATURE_BROKER_ADAPTIVE_LOAD_CPU_SPIKE_THRESHOLD,
        .int_range_hi       = MAX_CONFIG_FEATURE_BROKER_ADAPTIVE_LOAD_CPU_SPIKE_THRESHOLD,
        .int_default        = DEFAULT_CONFIG_FEATURE_BROKER_ADAPTIVE_LOAD_CPU_SPIKE_THRESHOLD,
        .feature_group      = FEATURE_GROUP_BROKER_REDIRECT,
        .value_traits       = config_value_traits_normal,
    },
    {
        .key                = CONFIG_FEATURE_BROKER_ADAPTIVE_LOAD_MEMORY_SPIKE_THRESHOLD,
        .desc               = CONFIG_FEATURE_BROKER_ADAPTIVE_LOAD_MEMORY_SPIKE_THRESHOLD_DESC,
        .details            = "Control threshold (in percent) for detecting spike in memory utilisation\n"
                              "Minimum: 2 percent\n"
                              "Maximum: 100 percent\n"
                              "Order of check: component id, global\n"
                              "default: 5percent",
        .val_type           = config_type_int,
        .component_types    = config_component_broker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_global,
        .int_range_lo       = MIN_CONFIG_FEATURE_BROKER_ADAPTIVE_LOAD_MEMORY_SPIKE_THRESHOLD,
        .int_range_hi       = MAX_CONFIG_FEATURE_BROKER_ADAPTIVE_LOAD_MEMORY_SPIKE_THRESHOLD,
        .int_default        = DEFAULT_CONFIG_FEATURE_BROKER_ADAPTIVE_LOAD_MEMORY_SPIKE_THRESHOLD,
        .feature_group      = FEATURE_GROUP_BROKER_REDIRECT,
        .value_traits       = config_value_traits_normal,
    },
    {
        .key                = CONFIG_FEATURE_BROKER_ADAPTIVE_LOAD_PROC_FD_SPIKE_THRESHOLD,
        .desc               = CONFIG_FEATURE_BROKER_ADAPTIVE_LOAD_PROC_FD_SPIKE_THRESHOLD_DESC,
        .details            = "Control threshold (in percent) for detecting spike in memory utilisation\n"
                              "Minimum: 2 percent\n"
                              "Maximum: 100 percent\n"
                              "Order of check: component id, global\n"
                              "default: 5percent",
        .val_type           = config_type_int,
        .component_types    = config_component_broker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_global,
        .int_range_lo       = MIN_CONFIG_FEATURE_BROKER_ADAPTIVE_LOAD_PROC_FD_SPIKE_THRESHOLD,
        .int_range_hi       = MAX_CONFIG_FEATURE_BROKER_ADAPTIVE_LOAD_PROC_FD_SPIKE_THRESHOLD,
        .int_default        = DEFAULT_CONFIG_FEATURE_BROKER_ADAPTIVE_LOAD_PROC_FD_SPIKE_THRESHOLD,
        .feature_group      = FEATURE_GROUP_BROKER_REDIRECT,
        .value_traits       = config_value_traits_normal,
    },
    {
        .key                = CONFIG_FEATURE_BROKER_SLOW_MAINTENANCE_DRAIN,
        .desc               = CONFIG_FEATURE_BROKER_SLOW_MAINTENANCE_DRAIN_DESC,
        .details            = "Instructs broker enable/disable slow client drain during maintenance \n"
                              "1: feature is enabled.\n"
                              "0: feature is disabled.\n"
                              "Order of check: component id, root customer gid, global id\n"
                              "default: 1, feature is enabled",
        .val_type           = config_type_int,
        .component_types    = config_component_broker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_zsroot |  config_target_gid_type_global,
        .int_range_lo       = MIN_CONFIG_FEATURE_BROKER_SLOW_MAINTENANCE_DRAIN,
        .int_range_hi       = MAX_CONFIG_FEATURE_BROKER_SLOW_MAINTENANCE_DRAIN,
        .int_default        = DEFAULT_CONFIG_FEATURE_BROKER_SLOW_MAINTENANCE_DRAIN,
        .feature_group      = FEATURE_GROUP_BROKER_REDIRECT,
        .value_traits       = config_value_traits_feature_enablement,
    },
    {
        .key                = CONFIG_FEATURE_BROKER_SLOW_MAINTENANCE_DRAIN_THRESHOLD,
        .desc               = CONFIG_FEATURE_BROKER_SLOW_MAINTENANCE_DRAIN_THRESHOLD_DESC,
        .details            = "Client count threshold above which slow maintenance drain is done \n"
                              "Minimum: 0, indicating, slow drain is always done.\n"
                              "Maximum: an arbitrarily large conn. count (1 Million).\n"
                              "Order of check: component id, root customer gid, global id\n"
                              "default: 5k connections",
        .val_type           = config_type_int,
        .component_types    = config_component_broker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_zsroot |  config_target_gid_type_global,
        .int_range_lo       = MIN_CONFIG_FEATURE_BROKER_SLOW_MAINTENANCE_DRAIN_THRESHOLD,
        .int_range_hi       = MAX_CONFIG_FEATURE_BROKER_SLOW_MAINTENANCE_DRAIN_THRESHOLD,
        .int_default        = DEFAULT_CONFIG_FEATURE_BROKER_SLOW_MAINTENANCE_DRAIN_THRESHOLD,
        .feature_group      = FEATURE_GROUP_BROKER_REDIRECT,
        .value_traits       = config_value_traits_normal,
    },
    {
        .key                = CONFIG_FEATURE_BRK_CLIENT_STATE_CAP_SUPPORT,
        .desc               = "client state capability support for customer",
        .details            = "Enable/Disable per customer or all customer connections for client state capability support .\n"
                                "Order of check: customer gid or global\n"
                                "default: 0, feature is disabled.",
        .val_type           = config_type_int,
        .component_types    = config_component_broker,
        .target_gid_types   =  config_target_gid_type_cust | config_target_gid_type_global,
        .int_range_lo       = CONFIG_FEATURE_BRK_CLIENT_STATE_CAP_SUPPORT_MIN,
        .int_range_hi       = CONFIG_FEATURE_BRK_CLIENT_STATE_CAP_SUPPORT_MAX,
        .int_default        = CONFIG_FEATURE_BRK_CLIENT_STATE_CAP_SUPPORT_DEFAULT,
        .feature_group      = FEATURE_GROUP_BROKER_REDIRECT,
        .value_traits       = config_value_traits_feature_enablement
    },
    {
        .key                = CONFIG_FEATURE_BROKER_AUTH_SNI,
        .desc               = "ZPA authenticated SNI tunnel support",
        .details            = "Enable/Disable authenticated SNI proxy\n"
                              "Order of check: instance gid, global\n"
                              "default: 0, feature is disabled.",
        .val_type           = config_type_int,
        .component_types    = config_component_broker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_global,
        .int_range_lo       = 0,
        .int_range_hi       = 1,
        .int_default        = CONFIG_FEATURE_BROKER_AUTH_SNI_DEFAULT_STATUS,
        .feature_group      = FEATURE_GROUP_AUTH_SNI,
        .value_traits       = config_value_traits_feature_enablement
    },
    {
        .key                = CONFIG_FEATURE_BROKER_ZPN_VERSION_CONTROL,
        .desc               = CONFIG_FEATURE_BROKER_ZPN_VERSION_CONTROL_DESC,
        .details            = "Enables validation of version of the client connecting to broker \n"
                              "1: feature is enabled.\n"
                              "0: feature is disabled.\n"
                              "Order of check: component id, global id\n"
                              "default: 0, feature is disabled",
        .val_type           = config_type_int,
        .component_types    = config_component_broker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_global,
        .int_range_lo       = MIN_CONFIG_FEATURE_BROKER_ZPN_VERSION_CONTROL,
        .int_range_hi       = MAX_CONFIG_FEATURE_BROKER_ZPN_VERSION_CONTROL,
        .int_default        = DEFAULT_CONFIG_FEATURE_BROKER_ZPN_VERSION_CONTROL,
        .feature_group      = FEATURE_GROUP_BROKER_CLIENT,
        .value_traits       = config_value_traits_feature_enablement,
    },
    {
        .key                = CONFIG_FEATURE_BROKER_ASST_DATABROKER_RESILIENCE,
        .desc               = "App connector data path resilency using two hop through control broker",
        .details            = "1: two hop feature is enabled at broker; \n"
                              "0: two hop feature is is disabled at broker \n"
                              "default: 0 (i.e. two hop is disabled at broker)",
        .val_type           = config_type_int,
        .component_types    = config_component_broker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_cust | config_target_gid_type_global,
        .int_range_lo       = MIN_CONFIG_FEATURE_BROKER_ASST_DATABROKER_RESILIENCE,
        .int_range_hi       = MAX_CONFIG_FEATURE_BROKER_ASST_DATABROKER_RESILIENCE,
        .int_default        = DEFAULT_BROKER_ASST_DATABROKER_RESILIENCE,
        .feature_group      = FEATURE_GROUP_ASST_DATABROKER_RESILIENCE,
        .value_traits       = config_value_traits_feature_enablement,
    },
    {
        .key                = CONFIG_FEATURE_BROKER_ZPM_CONNECTION_CONFIG,
        .desc               = CONFIG_FEATURE_BROKER_ZPM_CONNECTION_CONFIG_DESC,
        .details            = "Enables/disables the broker zpm connection\n"
                              "1: feature is enabled.\n"
                              "0: feature is disabled.\n"
                              "Order of check: component id, root customer id, global id\n"
                              "default: 0, feature is disabled",
        .val_type           = config_type_int,
        .component_types    = config_component_broker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_global,
        .int_range_lo       = MIN_CONFIG_FEATURE_BROKER_ZPM_CONNECTION_CONFIG,
        .int_range_hi       = MAX_CONFIG_FEATURE_BROKER_ZPM_CONNECTION_CONFIG,
        .int_default        = DEFAULT_CONFIG_FEATURE_BROKER_ZPM_CONNECTION_CONFIG,
        .feature_group      = FEATURE_GROUP_BROKER_CLIENT,
        .value_traits       = config_value_traits_feature_enablement,
    },
    {
        .key                = NETWORK_PRESENCE_FEATURE,
        .desc               = "enable np feature",
        .details            = "0: np is not enabled\n"
                              "1: np is enabled\n"
                              "Order of check: customer_gid, global\n"
                              "default: 0",
        .val_type           = config_type_int,
        .component_types    = config_component_broker,
        .target_gid_types   = config_target_gid_type_cust | config_target_gid_type_global,
        .int_range_lo       = 0,
        .int_range_hi       = 1,
        .int_default        = NETWORK_PRESENCE_FEATURE_DEFAULT,
        .feature_group      = FEATURE_GROUP_NETWORK_PRESENCE,
        .value_traits       = config_value_traits_feature_enablement,
    },
    {
        .key                = NETWORK_PRESENCE_HARD_DISABLED,
        .desc               = "when enabled disables np",
        .details            = "0: np hard disablement is off\n"
                              "1: np hard disablement is on\n"
                              "Order of check: global\n"
                              "default: 0",
        .val_type           = config_type_int,
        .component_types    = config_component_broker,
        .target_gid_types   = config_target_gid_type_global,
        .int_range_lo       = 0,
        .int_range_hi       = 1,
        .int_default        = NETWORK_PRESENCE_HARD_DISABLED_DEFAULT,
        .feature_group      = FEATURE_GROUP_NETWORK_PRESENCE,
        .value_traits       = config_value_traits_systemwide,
    },
    {
        .key                = NETWORK_PRESENCE_ACCESS_POLICY_FEATURE,
        .desc               = "enable np access policy feature",
        .details            = "0: np access policy feature is not enabled\n"
                              "1: np access policy feature is enabled\n"
                              "Order of check: customer_gid, global\n"
                              "default: 0",
        .val_type           = config_type_int,
        .component_types    = config_component_broker,
        .target_gid_types   = config_target_gid_type_cust | config_target_gid_type_global,
        .int_range_lo       = 0,
        .int_range_hi       = 1,
        .int_default        = NETWORK_PRESENCE_ACCESS_POLICY_FEATURE_DEFAULT,
        .feature_group      = FEATURE_GROUP_NETWORK_PRESENCE_ACCESS_POLICY,
        .value_traits       = config_value_traits_feature_enablement,
    },
    {
        .key                = NETWORK_PRESENCE_ACCESS_POLICY_HARD_DISABLED,
        .desc               = "when enabled disables np access policy feature",
        .details            = "0: np access policy hard disablement is off\n"
                              "1: np acccess policy hard disablement is on\n"
                              "Order of check: global\n"
                              "default: 0",
        .val_type           = config_type_int,
        .component_types    = config_component_broker,
        .target_gid_types   = config_target_gid_type_global,
        .int_range_lo       = 0,
        .int_range_hi       = 1,
        .int_default        = NETWORK_PRESENCE_HARD_DISABLED_DEFAULT,
        .feature_group      = FEATURE_GROUP_NETWORK_PRESENCE_ACCESS_POLICY,
        .value_traits       = config_value_traits_systemwide,
    },
    {
        .key                = NETWORK_PRESENCE_BROKER_KEEP_ALIVE_TIMEOUT_SECONDS,
        .desc               = "Keep-alive timeout in seconds for network presence",
        .details            = "Order of check: customer gid, global\n"
                              "default: 120",
        .val_type           = config_type_int,
        .component_types    = config_component_broker,
        .target_gid_types   = config_target_gid_type_cust | config_target_gid_type_global,
        .int_range_lo       = NETWORK_PRESENCE_BROKER_KEEP_ALIVE_TIMEOUT_SECONDS_MIN,
        .int_range_hi       = NETWORK_PRESENCE_BROKER_KEEP_ALIVE_TIMEOUT_SECONDS_MAX,
        .int_default        = NETWORK_PRESENCE_BROKER_KEEP_ALIVE_TIMEOUT_SECONDS_DEFAULT,
        .feature_group      = FEATURE_GROUP_NETWORK_PRESENCE,
        .value_traits       = config_value_traits_normal
    },
    {
        .key                = NETWORK_PRESENCE_LONG_TERM_EXPIRY_HOURS,
        .desc               = "TTL for the long term IP reservation in hours",
        .details            = "Client IP allocation records can stay long term state at least \n"
                              "long_term_expiry_hours since its last updated timestamp.\n"
                              "default: 24 (Long term Client IP address expires after 24 hours)",
        .val_type           = config_type_int,
        .component_types    = config_component_broker,
        .target_gid_types   = config_target_gid_type_cust | config_target_gid_type_global,
        .int_range_lo       = NETWORK_PRESENCE_LONG_TERM_EXPIRY_HOURS_VAL_MIN,
        .int_range_hi       = NETWORK_PRESENCE_LONG_TERM_EXPIRY__HOURS_VAL_MAX,
        .int_default        = NETWORK_PRESENCE_LONG_TERM_EXPIRY_HOURS_DEFAULT,
        .feature_group      = FEATURE_GROUP_NETWORK_PRESENCE,
        .value_traits       = config_value_traits_normal
    },
    {
        .key                = CONFIG_FEATURE_EXTRANET_BROKER_ENABLED,
        .desc               = "Feature flag for extranet on public brokers for a specific customer",
        .details            = "0: extranet feature customer+public brokers is off\n"
                              "1: extranet feature customer+public brokers is on\n"
                              "Order of check: hard disabled, customer gid, global\n"
                              "default: 0",
        .val_type           = config_type_int,
        .component_types    = config_component_broker,
        .target_gid_types   = config_target_gid_type_cust | config_target_gid_type_global,
        .int_range_lo       = CONFIG_FEATURE_EXTRANET_BROKER_ENABLED_MIN,
        .int_range_hi       = CONFIG_FEATURE_EXTRANET_BROKER_ENABLED_MAX,
        .int_default        = CONFIG_FEATURE_EXTRANET_BROKER_ENABLED_DEFAULT,
        .feature_group      = FEATURE_GROUP_EXTRANET,
        .value_traits       = config_value_traits_feature_enablement,
    },
    {
        .key                = CONFIG_FEATURE_EXTRANET_ZIA_HEALTH_ENABLED,
        .desc               = "Enables/disables broker sending extranet's zia_health log/topic to natural/kafka",
        .details            = "0: zia_health log/topic is not sent\n"
                              "1: zia_health log/topic is enabled\n"
                              "Order of check: instance gid, customer gid, global\n"
                              "default: 0, zia_health log/topic is not sent",
        .val_type           = config_type_int,
        .component_types    = config_component_broker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_cust | config_target_gid_type_global,
        .int_range_lo       = CONFIG_FEATURE_EXTRANET_ZIA_HEALTH_ENABLED_MIN,
        .int_range_hi       = CONFIG_FEATURE_EXTRANET_ZIA_HEALTH_ENABLED_MAX,
        .int_default        = CONFIG_FEATURE_EXTRANET_ZIA_HEALTH_ENABLED_DEFAULT,
        .feature_group      = FEATURE_GROUP_EXTRANET,
        .value_traits       = config_value_traits_feature_enablement,
    },
    {
        .key                = CONFIG_FEATURE_BROKER_BASELINE_MIN_VERSION_APPC,
        .desc               = CONFIG_FEATURE_BROKER_BASELINE_MIN_VERSION_APPC_DESC,
        .details            = "Specifies the minimum baseline version of App Connector that supports ZPN Version Control feature and automatic version upgrade. For any App Connector below this version, automatic upgrade will not be supported, any connector with invalid version cannot connect to broker. \n",
        .val_type           = config_type_str,
        .component_types    = config_component_broker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_global,
        .str_default        = DEFAULT_CONFIG_FEATURE_BROKER_BASELINE_MIN_VERSION_APPC,
        .feature_group      = FEATURE_GROUP_BROKER_CLIENT,
        .value_traits       = config_value_traits_normal,
    },
    {
        .key                = CONFIG_FEATURE_BROKER_BASELINE_MIN_VERSION_PSE,
        .desc               = CONFIG_FEATURE_BROKER_BASELINE_MIN_VERSION_PSE_DESC,
        .details            = "Specifies the minimum baseline version of PSE that supports ZPN Version Control feature and automatic version upgrade. For any PSE below this version, automatic upgrade will not be supported, any private service edge with invalid version cannot connect to broker. \n",
        .val_type           = config_type_str,
        .component_types    = config_component_broker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_global,
        .str_default        = DEFAULT_CONFIG_FEATURE_BROKER_BASELINE_MIN_VERSION_PSE,
        .feature_group      = FEATURE_GROUP_BROKER_CLIENT,
        .value_traits       = config_value_traits_normal,
    },
    {
        .key                = BROKER_FEATURE_DONT_DUMP,
        .desc               = CONFIG_FEATURE_BROKER_DONT_DUMP_DESC,
        .details            = "Don't dump for core memory can be enabled for public broker\n"
                              "0: disable\n"
                              "1: enable\n"
                              "default: 0/disabled",
        .val_type           = config_type_int,
        .component_types    = config_component_broker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_global,
        .int_range_lo       = FEATURE_DONT_DUMP_DISABLED,
        .int_range_hi       = FEATURE_DONT_DUMP_ENABLED,
        .int_default        = FEATURE_DONT_DUMP_DEFAULT,
        .feature_group      = FEATURE_GROUP_DONT_DUMP,
        .value_traits       = config_value_traits_feature_enablement,
    },
    {
        .key                = ZPN_BROKER_FOHH_CONNECTION_DISABLE_READ_CONFIG_FLAG,
        .desc               = "Feature flag to configure read disable feature on public brokers",
        .details            = "1: fohh connection read disable feature is enabled\n"
                              "   which triggers read disable (block) entire fohh connection socket towards zcc client\n"
                              "   if mtunnel tx buff towards appc reaches high water mark and stays above low water for allowed maximum time\n"
                              "0: fohh connection read disable (block) feature is disabled\n"
                              "   which triggers read disable (block) entire fohh connection socket towards zcc client\n"
                              "   if one of mtunnels triggers pause message towards zcc client\n"
                              "default: 0",
        .val_type           = config_type_int,
        .component_types    = config_component_broker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_cust | config_target_gid_type_zsroot | config_target_gid_type_global ,
        .int_range_lo       = LOW_ZPN_BROKER_FOHH_CONNECTION_DISABLE_READ_CONFIG_FLAG,
        .int_range_hi       = HIGH_ZPN_BROKER_FOHH_CONNECTION_DISABLE_READ_CONFIG_FLAG,
        .int_default        = ZPN_BROKER_FOHH_CONNECTION_DISABLE_READ_CONFIG_FLAG_DEFAULT_VALUE,
        .value_traits       = config_value_traits_feature_enablement,
        .feature_group      = FEATURE_GROUP_FOHH
    },
    {
        .key                = ZPN_BROKER_FOHH_CONNECTION_DISABLE_READ_CLIENT_TX_BUFF_HIGH,
        .desc               = "read disable feature - tx buff high water mark config in bytes",
        .details            = "if tx buff towards appc reaches high water mark and stays above low water for allowed maximum time\n"
                              "read disable (block) socket towards zcc client is triggered",
        .val_type           = config_type_int,
        .component_types    = config_component_broker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_cust | config_target_gid_type_zsroot | config_target_gid_type_global ,
        .int_range_lo       = LOW_ZPN_BROKER_FOHH_CONNECTION_DISABLE_READ_CLIENT_TX_BUFF_HIGH,
        .int_range_hi       = HIGH_ZPN_BROKER_FOHH_CONNECTION_DISABLE_READ_CLIENT_TX_BUFF_HIGH,
        .int_default        = ZPN_BROKER_FOHH_CONNECTION_DISABLE_READ_CLIENT_TX_BUFF_HIGH_DEFAULT,
        .value_traits       = config_value_traits_normal,
        .feature_group      = FEATURE_GROUP_FOHH
    },
    {
        .key                = ZPN_BROKER_FOHH_CONNECTION_DISABLE_READ_CLIENT_TX_BUFF_LOW,
        .desc               = "read disable feature - tx buff low water mark config in bytes",
        .details            = "if tx buff towards appc reaches high water mark and stays above low water for allowed maximum time\n"
                              "read disable (block) socket towards zcc client is triggered",
        .val_type           = config_type_int,
        .component_types    = config_component_broker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_cust | config_target_gid_type_zsroot | config_target_gid_type_global ,
        .int_range_lo       = LOW_ZPN_BROKER_FOHH_CONNECTION_DISABLE_READ_CLIENT_TX_BUFF_LOW,
        .int_range_hi       = HIGH_ZPN_BROKER_FOHH_CONNECTION_DISABLE_READ_CLIENT_TX_BUFF_LOW,
        .int_default        = ZPN_BROKER_FOHH_CONNECTION_DISABLE_READ_CLIENT_TX_BUFF_LOW_DEFAULT,
        .value_traits       = config_value_traits_normal,
        .feature_group      = FEATURE_GROUP_FOHH
    },
    {
        .key                = ZPN_BROKER_FOHH_CONNECTION_DISABLE_READ_CLIENT_TX_BUFF_HIGH_ALLOW_TIME_MAX_US,
        .desc               = "read disable feature - allowed maximum time config in us",
        .details            = "if tx buff towards appc reaches high water mark and stays above low water for allowed maximum time\n"
                              "read disable (block) socket towards zcc client is triggered",
        .val_type           = config_type_int,
        .component_types    = config_component_broker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_cust | config_target_gid_type_zsroot | config_target_gid_type_global ,
        .int_range_lo       = LOW_ZPN_BROKER_FOHH_CONNECTION_DISABLE_READ_CLIENT_TX_BUFF_HIGH_ALLOW_TIME_MAX_US,
        .int_range_hi       = HIGH_ZPN_BROKER_FOHH_CONNECTION_DISABLE_READ_CLIENT_TX_BUFF_HIGH_ALLOW_TIME_MAX_US,
        .int_default        = ZPN_BROKER_FOHH_CONNECTION_DISABLE_READ_CLIENT_TX_BUFF_HIGH_ALLOW_TIME_MAX_US_DEFAULT,
        .value_traits       = config_value_traits_normal,
        .feature_group      = FEATURE_GROUP_FOHH
    },
    {
        .key                = CONFIG_FEATURE_BROKER_QBR_INSIGHTS_FEATURE,
        .feature_group      = FEATURE_GROUP_QBR,
        .desc               = "When enabled, new fields for QBR report will be made available in the transaction logs",
        .details            = "0: feature is disabled\n"
                              "1: feature is enabled\n"
                              "default: 0",
        .val_type           = config_type_int,
        .value_traits       = config_value_traits_feature_enablement,
        .component_types    = config_component_broker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_cust | config_target_gid_type_global,
        .int_range_lo       = CONFIG_FEATURE_QBR_INSIGHTS_FEATURE_VAL_MIN,
        .int_range_hi       = CONFIG_FEATURE_QBR_INSIGHTS_FEATURE_VAL_MAX,
        .int_default        = CONFIG_FEATURE_QBR_INSIGHTS_FEATURE_DEFAULT
    },
    {
        .key                = CONFIG_FEATURE_EXTRANET_DNS_NEG_CACHE_TIME_S,
        .desc               = "Extranet specific negative DNS cache timeout",
        .details            = "Configure the timeout for negative DNS cache entry for an extranet app.\n"
                              "Order of check: instance id, customer id, global\n"
                              "default: 120 seconds",
        .val_type           = config_type_int,
        .component_types    = config_component_broker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_cust | config_target_gid_type_global,
        .int_range_lo       = CONFIG_FEATURE_EXTRANET_DNS_NEG_CACHE_TIME_S_MIN,
        .int_range_hi       = CONFIG_FEATURE_EXTRANET_DNS_NEG_CACHE_TIME_S_MAX,
        .int_default        = CONFIG_FEATURE_EXTRANET_DNS_NEG_CACHE_TIME_S_DEFAULT,
        .feature_group      = FEATURE_GROUP_EXTRANET,
        .value_traits       = config_value_traits_normal,
    },
    {
        .key                = FOHH_FLOW_CONTROL_ENHANCEMENTS,
        .feature_group      = FEATURE_GROUP_FOHH_FLOW_CONTROL_ENHANCEMENTS,
        .desc               = "Enable Fohh flow control enhancements",
        .details            = "Enable Fohh flow control enhancements\n"
                              "default: 0 (behaviour disabled by default)",
        .val_type           = config_type_int,
        .component_types    = config_component_broker,
        .target_gid_types   = config_target_gid_type_cust | config_target_gid_type_global,
        .int_range_lo       = FOHH_FLOW_CONTROL_ENHANCEMENTS_MIN,
        .int_range_hi       = FOHH_FLOW_CONTROL_ENHANCEMENTS_MAX,
        .int_default        = DEFAULT_FOHH_FLOW_CONTROL_ENHANCEMENTS,
        .value_traits       = config_value_traits_feature_enablement,
    },
    {
        .key                = CONFIG_FEATURE_BROKER_MCONN_BATCH_WINDOW_UPDATES,
        .feature_group      = FEATURE_GROUP_MCONN_WINDOW_UPDATES,
        .desc               = "When enabled, Mconn window updates are batched ",
        .details            = "0: feature is disabled\n"
                              "1: feature is enabled\n"
                              "default: 0",
        .val_type           = config_type_int,
        .value_traits       = config_value_traits_feature_enablement,
        .component_types    = config_component_broker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_inst_grp | config_target_gid_type_cust | config_target_gid_type_global,
        .int_range_lo       = CONFIG_FEATURE_BROKER_MCONN_BATCH_WINDOW_UPDATES_MIN,
        .int_range_hi       = CONFIG_FEATURE_BROKER_MCONN_BATCH_WINDOW_UPDATES_MAX,
        .int_default        = CONFIG_FEATURE_BROKER_MCONN_BATCH_WINDOW_UPDATES_DEFAULT
    },
    {
        .key                = CONFIG_FEATURE_BROKER_SYN_APP_RTT,
        .feature_group      = FEATURE_GROUP_APP_RTT,
        .desc               = "When enabled, APP RTT uses synchronous status messages ",
        .details            = "0: feature is disabled\n"
                              "1: feature is enabled\n"
                              "default: 0",
        .val_type           = config_type_int,
        .value_traits       = config_value_traits_feature_enablement,
        .component_types    = config_component_broker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_inst_grp | config_target_gid_type_cust | config_target_gid_type_global,
        .int_range_lo       = CONFIG_FEATURE_BROKER_SYN_APP_RTT_MIN,
        .int_range_hi       = CONFIG_FEATURE_BROKER_SYN_APP_RTT_MAX,
        .int_default        = CONFIG_FEATURE_BROKER_SYN_APP_RTT_DEFAULT
    },
    {
        .key                = CONFIG_FEATURE_BROKER_FOHH_PIPELINE_LATENCY_TRACE,
        .feature_group      = FEATURE_GROUP_PIPELINE_LATENCY_TRACE,
        .desc               = "When enabled, Fohh pipeline latency tracing is active ",
        .details            = "0: feature is disabled\n"
                              "1: feature is enabled\n"
                              "default: 0",
        .val_type           = config_type_int,
        .value_traits       = config_value_traits_feature_enablement,
        .component_types    = config_component_broker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_inst_grp | config_target_gid_type_cust | config_target_gid_type_global,
        .int_range_lo       = CONFIG_FEATURE_BROKER_FOHH_PIPELINE_LATENCY_TRACE_MIN,
        .int_range_hi       = CONFIG_FEATURE_BROKER_FOHH_PIPELINE_LATENCY_TRACE_MAX,
        .int_default        = CONFIG_FEATURE_BROKER_FOHH_PIPELINE_LATENCY_TRACE_DEFAULT
    },
    {
        .key                = CONFIG_FEATURE_BROKER_CLIENT_SYN_APP_RTT,
        .feature_group      = FEATURE_GROUP_APP_RTT,
        .desc               = "When enabled, APP RTT uses synchronous status messages ",
        .details            = "0: feature is disabled\n"
                              "1: feature is enabled\n"
                              "default: 0",
        .val_type           = config_type_int,
        .value_traits       = config_value_traits_feature_enablement,
        .component_types    = config_component_broker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_cust | config_target_gid_type_global,
        .int_range_lo       = CONFIG_FEATURE_BROKER_SYN_APP_RTT_MIN,
        .int_range_hi       = CONFIG_FEATURE_BROKER_SYN_APP_RTT_MAX,
        .int_default        = CONFIG_FEATURE_BROKER_SYN_APP_RTT_DEFAULT
    },
    {
        .key                = CONFIG_FEATURE_BROKER_CLIENT_FOHH_PIPELINE_LATENCY_TRACE,
        .feature_group      = FEATURE_GROUP_PIPELINE_LATENCY_TRACE,
        .desc               = "When enabled, Fohh pipeline latency tracing is active ",
        .details            = "0: feature is disabled\n"
                              "1: feature is enabled\n"
                              "default: 0",
        .val_type           = config_type_int,
        .value_traits       = config_value_traits_feature_enablement,
        .component_types    = config_component_broker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_cust | config_target_gid_type_global,
        .int_range_lo       = CONFIG_FEATURE_BROKER_FOHH_PIPELINE_LATENCY_TRACE_MIN,
        .int_range_hi       = CONFIG_FEATURE_BROKER_FOHH_PIPELINE_LATENCY_TRACE_MAX,
        .int_default        = CONFIG_FEATURE_BROKER_FOHH_PIPELINE_LATENCY_TRACE_DEFAULT
    },
    {
        .key                = ZPN_BROKER_MTUNNEL_FIN_EXPIRE_TIME_S,
        .feature_group      = FEATURE_GROUP_PIPELINE_LATENCY_TRACE,
        .desc               = "Configure fin expire timeout in seconds",
        .details            = "After receive fin, mtunnel will expire after specified time if it is still alive\n"
                              "default: 5min",
        .val_type           = config_type_int,
        .value_traits       = config_value_traits_normal,
        .component_types    = config_component_broker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_cust | config_target_gid_type_global,
        .int_range_lo       = LOW_ZPN_BROKER_MTUNNEL_FIN_EXPIRE_TIME_S,
        .int_range_hi       = HIGH_ZPN_BROKER_MTUNNEL_FIN_EXPIRE_TIME_S,
        .int_default        = ZPN_BROKER_MTUNNEL_FIN_EXPIRE_TIME_S_DEFAULT
    },
    {
        .key                = BROKER_FEATURE_AUTH_LOG_REDIRECTION_STATUS,
        .desc               = BROKER_FEATURE_AUTH_LOG_REDIRECTION_STATUS_DESC,
        .details            = "close_reason in the client auth log should reflect the redirect reason.\n"
                              "1: feature is enabled.\n"
                              "0: feature is disabled.\n"
                              "Order of check: instance id, customer gid, global id\n"
                              "default: 0, feature is disabled",
        .val_type           = config_type_int,
        .component_types    = config_component_broker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_cust |  config_target_gid_type_global,
        .int_range_lo       = BROKER_FEATURE_AUTH_LOG_REDIRECTION_STATUS_MIN,
        .int_range_hi       = BROKER_FEATURE_AUTH_LOG_REDIRECTION_STATUS_MAX,
        .int_default        = DEFAULT_BROKER_FEATURE_AUTH_LOG_REDIRECTION_STATUS,
        .feature_group      = FEATURE_GROUP_BROKER_CLIENT,
        .value_traits       = config_value_traits_feature_enablement,
    },
    {
        .key                = CONFIG_FEATURE_APP_THREAD_HEARTBEAT_OVERRIDE_BROKER,
        .feature_group      = FEATURE_GROUP_APP_THREAD_HEARTBEAT_OVERRIDE,
        .desc               = "When enabled, Reset App Thread Heartbeat for public broker to new value in seconds",
        .details            = "default: 20s, Value range is 20-300s",
        .val_type           = config_type_int,
        .value_traits       = config_value_traits_normal,
        .component_types    = config_component_broker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_global,
        .int_range_lo       = APP_THREAD_HEARTBEAT_OVERRIDE_MIN,
        .int_range_hi       = APP_THREAD_HEARTBEAT_OVERRIDE_MAX,
        .int_default        = APP_THREAD_HEARTBEAT_OVERRIDE_DEFAULT
    },
    {
        .key                = CONFIG_FEATURE_NP_MULTI_LAN_SUBNET_ENABLE,
        .feature_group      = FEATURE_GROUP_NETWORK_PRESENCE,
        .desc               = "Feature enable for multilan subnet support",
        .details            = "Enable multilan subnet support\n"
                              "default: 0 (disabled)",
        .val_type           = config_type_int,
        .value_traits       = config_value_traits_feature_enablement,
        .component_types    = config_component_broker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_cust | config_target_gid_type_global,
        .int_range_lo       = LOW_CONFIG_FEATURE_NP_MULTI_LAN_SUBNET_ENABLE,
        .int_range_hi       = HIGH_CONFIG_FEATURE_NP_MULTI_LAN_SUBNET_ENABLE,
        .int_default        = DEFAULT_CONFIG_FEATURE_NP_MULTI_LAN_SUBNET_ENABLE
    }
};

int zpn_broker_config_override_desc_register_all(enum Zpn_Instance_Type inst_type)
{
    int res = ZPN_RESULT_NO_ERROR;

    for (int i = 0; i < (sizeof(zpn_broker_config_override_flags_descriptions_common) / sizeof(struct zpath_config_override_desc)); i++) {
        res = zpath_config_override_desc_register(&zpn_broker_config_override_flags_descriptions_common[i]);

        if (res) {
            ZPN_LOG(AL_ERROR, "Unable to register zpn broker config override[%d] for key: %s, err: %s",
                              i, zpn_broker_config_override_flags_descriptions_common[i].key, zpath_result_string(res));
            return res;
        }
    }

    if (inst_type == ZPN_INSTANCE_TYPE_PRIVATE_BROKER) {
        // Component Private broker specific registration
        for (int i = 0; i < (sizeof(zpn_broker_config_override_flags_descriptions_private_broker) / sizeof(struct zpath_config_override_desc)); i++) {
            res = zpath_config_override_desc_register(&zpn_broker_config_override_flags_descriptions_private_broker[i]);

            if (res) {
                ZPN_LOG(AL_ERROR, "Unable to register zpn private broker config override[%d] for key: %s, err: %s",
                                  i, zpn_broker_config_override_flags_descriptions_private_broker[i].key, zpath_result_string(res));
                return res;
            }
        }
    } else {
        // Component Public broker specific registration
        for (int i = 0; i < (sizeof(zpn_broker_config_override_flags_descriptions_public_broker) / sizeof(struct zpath_config_override_desc)); i++) {
            res = zpath_config_override_desc_register(&zpn_broker_config_override_flags_descriptions_public_broker[i]);

            if (res) {
                ZPN_LOG(AL_ERROR, "Unable to register zpn broker config override[%d] for key: %s, err: %s",
                                  i, zpn_broker_config_override_flags_descriptions_public_broker[i].key, zpath_result_string(res));
                return res;
            }
        }
    }
    return res;
}

int zpn_broker_config_override_str_validator(const char *value_str)
{
    int valid = 0;

    if (!value_str) {
        return valid;
    }

    if (strncmp(value_str, FEATURE_FLAG_ENABLED_STR, strlen(FEATURE_FLAG_ENABLED_STR)) == 0 ||
            strncmp(value_str, FEATURE_FLAG_DISABLED_STR, strlen(FEATURE_FLAG_DISABLED_STR)) == 0) {
        valid = 1;
    }

    return valid;
}
