/*
 * zpn_broker_sys_stats.h. Copyright (C) 2015 Zscaler, Inc. All Rights Reserved.
 */

#ifndef _ZPN_BROKER_SYS_STATS_H_
#define _ZPN_BROKER_SYS_STATS_H_

#include "zpn/zpn_broker_load_common.h"

/* Public broker system stats */
struct zpn_broker_sys_stats {                  /* _ARGO: object_definition */
    int      system_mem_util;                  /* _ARGO: integer */
    int      process_mem_util;                 /* _ARGO: integer */
    uint16_t s_cpu_util;                       /* _ARGO: integer */
    uint16_t s_mem_util;                       /* _ARGO: integer */
    uint32_t s_active_mtun;                    /* _ARGO: integer */
    uint32_t s_bytes_xfer;                     /* _ARGO: integer */
    uint64_t s_nonidle;                        /* _ARGO: integer */
    uint64_t s_idle;                           /* _ARGO: integer */

    int32_t s_udp4_port_util;                  /* _ARGO: integer */
    int32_t s_udp6_port_util;                  /* _ARGO: integer */
    int32_t s_sys_fd_util;                     /* _ARGO: integer */
    int32_t s_proc_fd_util;                    /* _ARGO: integer */
    char *balance_role;                        /* _ARGO: string */
    char *balance_mode;                        /* _ARGO: string */
};

struct zpn_broker_load_stats {                  /* _ARGO: object_definition */
    int16_t cpu_util;                           /* _ARGO: integer */
    int16_t mem_util;                           /* _ARGO: integer */
    int64_t active_mtun;                        /* _ARGO: integer */
    int64_t bytes_xfer;                         /* _ARGO: integer */
    char *balance_role;                         /* _ARGO: string */
    char *balance_mode;                         /* _ARGO: string */
    uint32_t load_skew;                         /* _ARGO: integer */
    uint16_t status;                            /* _ARGO: integer */
    uint16_t proc_fd_util;                      /* _ARGO: integer */
    uint16_t report_count;                      /* _ARGO: integer */
    int64_t num_clients;                        /* _ARGO: integer */
    uint16_t load;                              /* _ARGO: integer */
};

extern struct argo_structure_description *zpn_broker_sys_stats_description;
#endif /* _ZPN_BROKER_SYS_STATS_H_ */
