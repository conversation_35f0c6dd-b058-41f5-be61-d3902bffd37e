/*
 * zpn_broker_client_apps.h. Copyright (C) 2019 Zscaler Inc. All Rights Reserved
 */

/*
 * This library provides an application list for download to a client.
 *
 * This library frequently refers to a 'domain'. In the context of
 * this library, a 'domain' is a text-form domain or IP subnet.
 */


#ifndef __ZPN_BROKER_CLIENT_APPS_H__
#define __ZPN_BROKER_CLIENT_APPS_H__

#include "zpn/zpn_rpc.h"
#include "zhash/zhash_table.h"
#include "zpn/zpn_client_tracker.h"

/*
 * The amount of time to push out recalculating when new data arrives
 * that might affect a customer
 */
#define DEFAULT_RECALC_PUSH_US (1000*1000)

/*
 * Maximum number of apps we support (NOT domains- domains can be
 * larger. This is just apps)
 */
#define MAX_CLIENT_APPS 100000

/*
 * Amount of time to push client config update when new updates keep arriving  - delay 100 ms
 */
 #define DEFAULT_CLIENT_UPDATE_PUSH_US  (100*1000)

#define DEFAULT_MAX_APP_DOWNLOAD_EMBEDDED_POLICY_TEST 2

struct zpn_client_app_state;
struct zpn_broker_client_fohh_state;
struct zpn_app_client_check;
struct zpn_app_policy_check;
struct zpath_debug_state;

void zpn_policy_enable_app_scaling_feature_test();
void zpn_activate_dr_mode();
void zpn_policy_enable_app_download_restriction();

/* et_25587_test */
void et_25587_test_init();
void et_25587_check_unblock();

/*
 * This callback is called when an app must be sent to the client.
 *
 * This callback is called on the thread that asked for the apps.
 *
 * argo_object is an object containing a zpn_client_app structure.
 */
typedef int (zpn_broker_client_app_callback_f)(struct argo_object *app,
                                               char *char_cookie,
                                               int64_t int_cookie);

/*
 * This callback is called when the first working set of apps has been
 * fully sent to the client
 *
 * This callback is called on the thread that asked for the apps.
 */
typedef int (zpn_broker_client_app_complete_callback_f)(char *char_cookie,
                                                        int64_t int_cookie);

/* send tld_1 domians to a client */
typedef int (tld_1_send_to_client_f)(const char *client_tunnel_id,
                                     char **aggregated_domain_list,
                                     int aggregated_domain_count);

typedef void (zpn_policy_updated_callback_f)(int64_t customer_gid, const char *error);
typedef int (zpn_send_agg_domain_callback_f)(int64_t customer_gid,
                                             char** aggregated_domain_list,
                                             int aggregated_domain_count,
                                             int skip_app_scaling_feature_check,
                                             int64_t timer_fired_us,
                                             enum zpn_client_type client,
                                             enum zpn_platform_type platform);

struct zpn_client_app_state *zpn_broker_client_apps_create_client_app_state(struct zpn_broker_client_fohh_state *c_state,
                                                            int segment_download,
                                                            zpn_broker_client_app_callback_f *app_callback,
                                                            zpn_broker_client_app_complete_callback_f *complete_callback);

/* Client Policy/App Auth log */
struct zpn_broker_client_app_stats {
    int64_t app_update_calls;
    int64_t app_update_start_us;
    int64_t app_update_complete_us;

    int64_t client_policy_evaluate_calls;
    int64_t client_policy_evaluate_start_us;
    int64_t client_policy_evaluate_complete_us;

    int64_t client_policy_rules_count;
    int64_t client_policy_timer_push_count;

    int64_t max_new_client_cb_in_queue;
    int64_t max_refresh_timer_cb_in_queue;
    int64_t max_app_complete_cb_in_queue;
    int64_t max_inform_one_cb_in_queue;

    /* The app download queue size for the customer when this client is enqueued */
    int64_t app_download_high_priority_queue_size_in;
    int64_t app_download_low_priority_queue_size_in;

    /* The app download queue size for the customer when this client is dequeued */
    int64_t app_download_high_priority_queue_size_out;
    int64_t app_download_low_priority_queue_size_out;

    int64_t app_thread_high_priority_enqueue_us;
    int64_t app_thread_high_priority_dequeue_us;
    int64_t app_thread_low_priority_enqueue_us;
    int64_t app_thread_low_priority_dequeue_us;
    int64_t app_thread_high_priority_wait_us;
    /*
     * The enqueued and dequeued count of the sent applications for each new client
     * The difference is the count of applications left in the queue to be sent,
     * for successful the app download , the two counts should be the same (no objects left in the queue).
     */
    int64_t client_app_sent_cnt_in;
    int64_t client_app_sent_cnt_out;

    int64_t app_process_termination_count;

    int64_t client_total_app_count;
    int64_t client_bypass_app_count;
    int64_t client_policy_deny_app_download_count;
    int64_t client_inclusive_app_download_count;

    int64_t latest_app_sent_us;

    uint64_t app_re_download_time_us;

    char app_download_debug_str[128];
};

/*
 * State associated with this customer.
 *
 * Lifecycle of this object:
 *
 * 1. On creation, is marked 'intializing'- during this time two
 *    asynchronous events occur- fetching customer policy, fetching
 *    customer applications, and fetching customer application_groups.
 *    Any new clients that are added during this time do 'nothing'-
 *    they are in limbo, waiting on config to complete. Any 'updates'
 *    to policy or applications received by the customer will be
 *    ignored until the the full set of applications is complete.
 *
 * 2. The moment full applications are received, this customer sends a
 *    'update_domains' message with all current domains to every
 *    client. This message is ref-counted to save memory, as it can be
 *    a very large set. (50K+ clients on a machine)
 *
 * 3. Now the customer is put into operational mode. During this time,
 *    the customer will receive notifications from wally about
 *    customer policy changes, customer application changes, customer
 *    application to application group changes, and customer
 *    application group changes.
 *
 *    3.a. On a policy change, a timer is created for 200ms in the
 *         future. For every policy change, this timer is pushed to
 *         200ms in the future. (To aggregate policy changes). When
 *         the timer fires, a new policy is fetched until the policy
 *         is up-to-date. (Using timer as necessary). Once the new
 *         policy is available, the full set of domains is once more
 *         sent to every client, just as in #2 above, using an
 *         'update_domains' message
 *
 *         3.a.1. If a policy change is in progress, any application
 *                change (3b) timer or state is cleared, and any
 *                updates to that state are ignored until the
 *                update_domains message is sent. Once the
 *                update_domains is sent, further application changes
 *                are monitored according to 3.b. as before
 *
 *    3.b. On a application change, app_group<->application change, or
 *         app_group change, the set of domains affected is
 *         determined. Each of these domains is added to an 'update
 *         set' of domains, and a timer is set for 200ms in the
 *         future. For each new change, this timer is re-set for 200ms
 *         in the future. Once the timer fires, the set of domains
 *         that was affected is sent to all clients using an
 *         'update_domains' message
 */
TAILQ_HEAD(zpn_client_app_state_head, zpn_client_app_state);
struct customer_state {
    /*
     * Lock on adding/removing clients, and on callbacks for updating
     * state
     */
    zpath_mutex_t lock;
    zpath_mutex_t stats_lock;

    unsigned initializing_apps:1;
    unsigned initializing_connection:1;
    unsigned initializing_testing:1;
    unsigned initializing_testing_set:1;

    unsigned policy_change:1;
    unsigned timeout_occurred:1;
    unsigned inform_all_domains:1;

    int64_t customer_gid;

    /* hash table to indicate if a scope is ready(policy loaded) or not */
    /* scope_gid => (&policy_ready oe &policy_not_ready) */
    struct zhash_table *scope_set;

    /*
     * Per customer app scaling feature status
     */
    int64_t app_scaling_feature_status_cur; // Updated locally when config_override callbacks occur (to detect changes)
    int64_t android_app_scaling_feature_status;
    int64_t android_app_scaling_feature_status_cur;
#ifdef SIPA_APP_SCALE_CODE_ENABLED
    int64_t sipa_app_scaling_feature_status;
    int64_t sipa_app_scaling_feature_status_cur;
#endif
    int64_t cc_broker_app_scaling_feature_status;
    int64_t cc_broker_app_scaling_feature_status_cur;
    int64_t cc_pse_app_scaling_feature_status;
    int64_t cc_pse_app_scaling_feature_status_cur;
    int64_t vdi_broker_app_scaling_feature_status;
    int64_t vdi_broker_app_scaling_feature_status_cur;
    int64_t vdi_pse_app_scaling_feature_status;
    int64_t vdi_pse_app_scaling_feature_status_cur;
    int64_t current_app_scaling_no_domain_download_clients;
    int64_t current_app_scaling_no_domain_download_v2_clients;
    int64_t current_app_scaling_no_ip_download_clients;
    int64_t total_app_scaling_no_domain_download_clients;
    int64_t total_app_scaling_no_domain_download_v2_clients;
    int64_t total_app_scaling_no_ip_download_clients;

    int64_t restrict_app_down_embedded_feature_status;
    int64_t cust_user_risk_status_in_broker;
    int64_t workload_tag_grp_cust_status;

    /* Accumulated count of num of clients enabled with restrict
     * app download feature for this customer
     */
    int64_t current_restrict_app_download_clients;
    int64_t total_restrict_app_download_clients;

    int64_t max_app_download_ios_value;
    int64_t max_app_download_android_value;
    int64_t policy_re_eval_on_posture_chg_feature_status;
    int64_t c2c_ip_feature_status;
    int16_t c2c_ip_feature_status_incarnation;
    int16_t c2c_ip_feature_status_release_all;

    int64_t policy_svcp_enabled_feature_status;
    int64_t step_up_auth_feature_status;

    int64_t policy_fqdn_to_srv_ip_feature_status;

    int64_t app_scaling_bypass_improvement_feature_status;

    int64_t aae_profile_feature_status;
    int64_t policy_re_eval_on_scim_update_feature_status;

    /* DNS TXT support status */
    int64_t dns_txt_query_support_status;

    /*
     * If the timer exists, then we are delaying updates to clients
     * until the timer fires.
     */
    struct event *timer;

    /* The thread on which we do things. (customer functionality is
     * often called from wally/etc. But our timer lives here.. */
    struct zevent_base *calc_thread;

    /*
     * Used to determine how many asynchronous fetches we are waiting
     * to complete to have full wally state for customer application +
     * relation + application group. This can be used for gating
     * application rebuilds (for sending to cients, etc)
     *
     * If this becomes zero, we have achieved an initial configuration
     * download
     */
    int async_count;

    /* How many download callback is in the queue */
    int64_t app_download_high_priority_queue_size;
    int64_t app_download_low_priority_queue_size;

    int64_t enforced_client_disconnection_count;

    /*
     * Used to accumulate domains that have changed since last app
     * distribution
     */
    struct zhash_table *domain_accumulate;
    struct zhash_table *gid_accumulate;

    /*
     * Used to cache integer application gid set for each
     * application. This hash table contains the gid of the
     * application and the gid of every application group linked to
     * it. This can be used directly by policy.
     */
    struct zhash_table *app_gid_hashes;

    /*
     * All the clients associated with this customer
     */
    struct zpn_client_app_state_head clients;

    zpn_policy_updated_callback_f *config_update_cb;
    zpn_send_agg_domain_callback_f *agg_domain_cb;

    /*Frequency for server side posture re evaluation*/
    int64_t policy_svcp_re_eval_freq_sec;
};

/*
 * State associated with one client
 *
 * Lifecycle of this object:
 *
 * 1. On creation, registers itself with customer state. If customer
 *    state does not exist, it is initialized
 *
 * 2. On destruction, marks itself as being destroyed, and sends a
 *    message to itself to destroy. The message to itself will then be
 *    behind/after any message sent by another system to this object
 *    (i.e. from the customer state) and will successfully be the last
 *    message this object will process.
 *
 * 3. Can receive different messages:
 *
 *    3A. Synchronously receive zpn_broker_client_apps_done. In this
 *        case, destruction is begun. The object removes itself from
 *        the customer, and sends itself a destruction message. It
 *        marks itself as being destroyed. If the object is already
 *        marked as destroyed, it is a severe error.
 *
 *    3B. Asynchronously receive 'update_domains' message. This
 *        message will update the specified domains only. See the
 *        update_domains call for details. Message is ignored if
 *        client is marked for deletion.
 *
 *    3C. Asynchronously receive 'delete client' message (from
 *        itself), which is used to ensure there are no outstanding
 *        messages destined to this object.
 *
 *    3D. Synchronously be told about configuration change
 *        (i.e. client hash tables change). In this case, the client
 *        state requests that the customer state initiate a domain
 *        download of all domains.
 *
 * Basic domain processing:
 *
 * For 'reset_domains' message: Mark all updated or matched domains
 * with a marker. Then, when done updating domains, go through all
 * existing domains and remove/delete them (and send such to client)
 *
 * For 'update_domains' message: Just update domains, no marking
 * required, and no post-cleanup.
 *
 * Basic update processing:
 *
 * Given a domain:
 *
 * 1. Build an object representing the ports this user is supposed to
 *    have access to based on policy:
 *
 *    1A. Start with an empty object. (no ports/protocols to intercept)
 *
 *    1B. Use customer->domain_to_gids to enumerate all the apps for
 *        this domain. For each of these apps, check if it is download
 *        or bypass, based on policy. If the app itself is 'bypass',
 *        that configuration overrides policy. If the app is to be
 *        intercepted, add the ports to the object.
 *
 * 2. Compare the object to the downloaded object of the same name. If
 *    there is a difference (added, deleted, or changed), then make a
 *    callback to the client to change the state, and update our
 *    cache. Mark the cache as updated with current version.
 *
 */
struct zpn_client_app_state {

    char *debug_str;

    /*
     * This is a pointer of c_state just for debugging on gdb
     * do NOT use/reference it for any other purpose
     */

    void *c_state_do_not_use;

    /* References to client: */
    int64_t customer_gid;

    /* References to client's scope */
    int64_t scope_gid;
    char attr[MAX_ATTR_SIZE]; /* [attr name]|[attr value] */
    int64_t machine_gid;      /* machine tunnel gid */
    int conn_thread_id;
    int app_thread_id;

    /* Reference for the owner (client connection), and each
     * asynchronous callback outstanding. */
    int32_t reference_count;

    enum zpn_client_type client_type;
    int64_t client_aux_id;  // Pass mt/ec/bc group gid to bypass policy eval.
    int64_t loc_id;
    int64_t sub_loc_id;
    struct zhash_table *string_hash;
    struct zhash_table *saml_string_hash;
    struct zhash_table *scim_string_hash;
    zpn_broker_client_app_callback_f *app_callback;
    zpn_broker_client_app_complete_callback_f *complete_callback;
    char *char_cookie;
    int64_t int_cookie;

    char tunnel_id[ZPN_TUNNEL_ID_BYTES_TEXT + 1];

    /*
     * If the timer exists, then we are delaying updates to this
     * client until the timer fires.
     */
    struct event *timer;

    /* More client info: */
    struct zevent_base *client_fohh_thread;

    /* The thread on which we calculate stuff */
    struct zevent_base *calc_thread;

    /* Reference to customer state. Customer never disappears :) */
    struct customer_state *customer;

    /* Our entry in the customer client list */
    TAILQ_ENTRY(zpn_client_app_state) list;

    /* Contains references to zpn_client_app objects, indexed by
     * domain */
    struct zhash_table *apps_sent;
    zpath_mutex_t apps_sent_lock;

    /* data goes to auth.log */
    struct zpn_broker_client_app_stats stats;

    unsigned first_pass_complete:1;
    unsigned deleted:1;
    unsigned segment_download:1;

    unsigned no_ip_download:1;
    unsigned no_domain_download:1;
    unsigned is_first_app_enqueued:1;
    unsigned is_last_app_enqueued:1;
    unsigned is_cstate_done:1;
    unsigned skip_pattern_domain:1;
    unsigned no_domain_download_v2:1;

    /* Platform of the client connection eq ios,android */
    enum zpn_platform_type platform_type;

    /* do not free these pointers in client_release(), they are freeed somewhere else */
    struct client_domains *domains;
    struct client_app_gids *app_gids;

    /* for monitoring */
    int64_t new_client_cb_in_queue;
    int64_t refresh_timer_cb_in_queue;
    int64_t app_complete_cb_in_queue;
    int64_t inform_one_cb_in_queue;

    int64_t re_init_start_us;

    zpn_client_tracker_t *tracker;
};

/*
 * Return code for c2c ip feature flag check.
 */
enum zpn_broker_c2c_ip_feature_flag_action
{
    /* feature is disabled cleanup c2c client on broker */
    zpn_broker_c2c_ip_client_cleanup,

    /* Feature is enabled, renew/request the IP */
    zpn_broker_c2c_ip_client_renew,

    /* Feature flag for customer not found */
    zpn_broker_c2c_ip_client_check_global,

    /* no action */
    zpn_broker_c2c_ip_client_no_action
};

#define CLIENT_APP_CACHE_MAGIC 0xdeadbeefdeadbeef
/* Per Client App Cache */
struct zpn_client_app_cache {
    /* Getting rid from internal cache only maintain in rpc send */
    /* uint16_t *ingress_ports; */
    /* uint16_t *ingress_port_ranges; */
    uint64_t  magic;
    uint16_t *tcp_port_ranges;
    uint16_t *udp_port_ranges;

    /* Getting rid from internal cache only maintain in rpc send */
    /* uint16_t ports_count; */
    /* uint16_t port_ranges_count; */

    uint16_t  tcp_port_ranges_count;
    uint16_t  udp_port_ranges_count;
    uint16_t  bypass              :1;
    uint16_t  ip_anchored         :1;
    uint16_t  inspected           :1;
    uint16_t  bypass_on_reauth    :1;
    //Match Style bitmap should reflect changes in enum zpn_match_style
    uint16_t  match_style         :2;
    uint16_t  double_encrypt      :1;
    uint16_t  deleted             :1;
    uint16_t  bypass_type         :3; // NEVER, OFF_NET, ON_NET, ALWAYS, REAUTH
    uint16_t  icmp_access_type    :2; // NONE, PING, PING_TRACEROUTING
    uint16_t  spare               :3; //NOTE: Reduce this number if adding new flags
};

enum zpn_bypass_type {
    NEVER = 0,
    OFF_NET,
    ON_NET,
    ALWAYS,
    REAUTH,
};

enum zpn_icmp_access_type {
    NONE = 0,
    PING,
    PING_TRACEROUTING,
};
/*
 * Is ALWAYS asynchronous.
 *
 * Callbacks ALWAYS execute on the same thread as this routine was
 * called from.
 *
 * segment_download should be specified if the client wants/expects
 * individual segments downloaded. (Generally edge connector and IP
 * anchoring)
 *
 * general_context_hash MUST remain valid until zpn_broker_client_apps_done or
 * zpn_broker_client_apps_update is called.
 *
 * saml_string_hash should contain any saml attributes configured for the user from their idp
 *
 * scim_string_hash contains any scim values that we have received from the customer's scim configuration
 *
 * the callbacks occur on the thread that does this registration.
 */
int zpn_broker_client_apps_start(struct zpn_broker_client_fohh_state *c_state,
                                 int segment_download,
                                 zpn_broker_client_app_callback_f *app_callback,
                                 zpn_broker_client_app_complete_callback_f *complete_callback);

/*
 * Update the hash tables or callback state for the user.
 *
 * This will reprocess all applications against policy differences and
 * make callbacks as appropriate.
 */
int zpn_broker_client_apps_update(struct zpn_client_app_state *client,
                                  struct zhash_table *string_hash,
                                  struct zhash_table *saml_hash,
                                  struct zhash_table *scim_hash);

/*
 * Once returned from, no further callbacks will be issued, but it is
 * possible that callbacks will still be 'in flight'. It is the
 * responsibility of the caller to ensure that this is not an issue.
 */
int zpn_broker_client_apps_done(struct zpn_broker_client_fohh_state *c_state);

/*
 * This call lets this library know that policy has changed. This can
 * be called as often as desired (i.e. could be on any row arrival,
 * etc)
 */
int zpn_broker_client_app_customer_policy_update(int64_t customer_gid);

/*
 * For debugging/test code only. returns non-zero if a timer has fired
 * for the specified customer since the last call to this routine.
 */
int zpn_broker_client_app_timer_since_last_call(int64_t customer_gid);

/*
 * Count of "net" downloaded apps. This does not account for multiple updates/deletes to the same app.
 */
int64_t zpn_broker_client_apps_sent_count(struct zpn_client_app_state *client);

/*
 * Init this library
 *
 * lone_customer_gid must be zero if running multitenant, or specified
 * if single tenant.
 */
int zpn_broker_client_apps_init(struct wally *lone_wally, int64_t lone_customer_gid, int64_t push_us, int dr_mode_is_active);

void accumulate_free(struct zpn_app_client_check *client_check);
int zpn_broker_client_fill_app_client_check_with_app(struct zpn_broker_client_fohh_state *c_state,
                                                     struct zpn_app_client_check *client_check);
void zpn_add_app_scaling_callback_funcs(int64_t customer_gid, void *config_cb, void *agg_domain_cb);
int zpn_is_app_scaling_enabled(int64_t customer_gid);

int dump_downloaded_app(struct zpath_debug_state *request_state, struct zpn_client_app_state *client, const char* tunnel_id, int* pretty);

int32_t get_client_ref_count(void *cookie);
void set_enforced_disconnection_count(void *cookie);
void reset_init_testing(void *cookie);
int zpn_app_process_stats_fill(void *cookie, int counter, void *structure_data);

int64_t zpn_broker_client_app_get_scope_gid(void *cookie);

int64_t zpn_broker_get_restrict_app_download_embedded_status_for_customer(int64_t customer_gid);
int64_t zpn_broker_get_user_risk_status_for_broker_customer(int64_t customer_gid);
int64_t zpn_broker_get_workload_tag_grp_status(int64_t customer_gid);
int64_t zpn_broker_get_workload_tag_grp_status_detailed(int64_t customer_gid, int64_t *hard_disable_ret, int64_t *customer_config_ret);

int zpn_broker_policy_re_eval_on_posture_change_is_enabled(int64_t customer_gid);
int64_t zpn_broker_get_svcp_re_eval_duration(int64_t customer_gid);
int zpn_broker_policy_svcp_is_enabled(int64_t customer_gid);
int zpn_broker_step_up_auth_is_enabled(int64_t customer_gid);

int zpn_broker_dns_txt_query_support_is_enabled(int64_t customer_gid);

int zpn_broker_policy_fqdn_to_srv_ip_enabled(int64_t customer_gid);
void zpn_broker_set_c2c_ip_feature_incarnation(struct zpn_broker_client_fohh_state *c_state);
int zpn_broker_app_scaling_bypass_improvement_enabled(int64_t customer_gid);

enum zpn_broker_c2c_ip_feature_flag_action zpn_broker_c2c_ip_feature_check(struct zpn_broker_client_fohh_state *c_state, int *release_all);

/* Wrapper functions for dta dna search domain feature */
int64_t zpn_client_app_state_get_scope_gid(void *arg);
char * zpn_client_app_state_get_tunnel_id(void *arg);
void zpn_client_app_state_release(void *arg);
int64_t zpn_broker_is_workload_tag_grp_hard_disabled();
int64_t zpn_broker_is_extranet_hard_disabled();
int64_t zpn_broker_is_extranet_enabled(int64_t customer_gid, int *pse_in_dr_mode);
int zpn_broker_get_aae_profile_status_for_customer(int64_t customer_gid);
int64_t zpn_broker_is_aae_profile_hard_disabled();
int zpn_broker_aae_profile_is_disabled();
int zpn_broker_get_aae_profile_status(int64_t customer_gid);
int zpn_broker_is_policy_re_eval_on_scim_update_enabled(int64_t customer_gid);

void zpn_app_scaling_cc_hard_disable_feature_flag_changed_cb(const int64_t *config_value, int64_t customer_gid);
void zpn_app_scaling_vdi_hard_disable_feature_flag_changed_cb(const int64_t *config_value, int64_t customer_gid);

/* Get appscaling feature flag value for specific client */
int zpn_broker_get_app_scaling_flag_for_specific_client(struct zpn_client_app_state *client, struct zpn_broker_client_fohh_state *c_state);

/* Utility function to capture platform & clients for which action needs to be performed */
void zpn_broker_client_appscale_set_platform_clients(struct zpn_lib_appscale_client_cookie *pclient,
                                                     enum zpn_client_type client,
                                                     enum zpn_platform_type platform);

/* Utility function to check if RPC needs to be send to current client */
int zpn_broker_client_apps_send_to_specific_client(struct zpn_lib_appscale_client_cookie *cookie,
                                                   struct zpn_client_app_state *client,
                                                   struct zpn_broker_client_fohh_state *c_state);
#endif /* __ZPN_BROKER_CLIENT_APPS_H__ */
