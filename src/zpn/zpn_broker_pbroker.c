/*
 * zpn_broker_pbroker.c. Copyright (C) 2019 Zscaler Inc. All Rights Reserved
 */

#include "zpn/zpn_broker_pbroker.h"

#include "base64/base64.h"
#include "fohh/fohh.h"
#include "fohh/fohh_private.h"
#include <openssl/rand.h>
#include "wally/wally.h"
#include "wally/wally_column_nullify.h"
#include "wally/wally_filter_table.h"
#include "wally/wally_fohh_server.h"
#include "zhash/zhash_table.h"
#include "zpath_lib/zpath_app.h"
#include "zpath_lib/zpath_cloud.h"
#include "zpath_lib/zpath_config_override.h"
#include "zpath_lib/zpath_config_override_keys.h"
#include "zpath_lib/zpath_customer.h"
#include "zpath_lib/zpath_debug.h"
#include "zpath_lib/zpath_domain_lookup.h"
#include "zpath_lib/zpath_instance.h"
#include "zpath_lib/zpath_local.h"
#include "zpath_lib/zpath_log_store.h"
#include "zpath_lib/zpath_feature_flag_keys.h"
#include "admin_probe/admin_probe_rpc.h"

#include "argo/argo_buf.h"
#include "fohh/fohh_log.h"
#include "zpath_lib/zpath_et_wally_userdb.h"
#include "zpn/zpn_application.h"
#include "zpn/zpn_application_domain.h"
#include "zpn/zpn_broker.h"
#include "zpn/zpn_broker_assert.h"
#include "zpn/zbalance/zpn_balance_redirect.h"
#include "zpn/zpn_broker_client.h"
#include "zpn/zpn_broker_dispatch.h"
#include "zpn/zpn_broker_dns.h"
#include "zpn/zpn_broker_mtunnel.h"
#include "zpn/zpn_broker_private.h"
#include "zpn/zpn_broker_siem.h"
#include "zpn/zpn_issuedcert.h"
#include "zpn/zpn_lib.h"
#include "zpn/zpn_pbroker_group.h"
#include "zpn/zpn_pbroker_to_group.h"
#include "zpn/zpn_private_broker_load_table.h"
#include "zpn/zpn_private_broker_table.h"
#include "zpn/zpn_rpc.h"
#include "zpn/zpn_signing_cert.h"
#include "zpn/zpn_broker_pbroker_stats.h"
#include "zpn/zpn_broker_cryptosvc.h"
#include "zpn_event/zpn_event.h"
#include "zpn_event/zpn_event_data.h"
#include "zpn_event/zpn_event_stats.h"
#include "zpn/zpn_siem_tx.h"
#include "zpn/zpn_broker_dispatch_c2c_client_check.h"
#include "zpn/zpn_private_broker_version.h"
#include "zpn/zpn_version_control/zpn_version_control.h"
#include "zpn/zpn_version_control/zpn_version_control_utils.h"
#include "zpn/zpn_site.h"
#include "zpn/zpn_firedrill_site.h"

#ifdef ZPN_TESTING
#include "test_misc/testing_macros.h"
#include "zpn/gtests/zpn_broker_maintanence_auth_log_tests/test_headers/zpn_broker_maintanence_auth_log_tests_weak_headers.h"
static int zpn_broker_pbroker_conn_callback(struct fohh_connection *connection,
                                            enum fohh_connection_state state,
                                            void *cookie);
static void zpn_broker_pbroker_control_conn_monitor_cb(evutil_socket_t sock, short flags, void *cookie);

void zpath_wrapper_zpn_broker_pbroker_control_conn_monitor_cb(evutil_socket_t sock, short flags, void *cookie) {
    zpn_broker_pbroker_control_conn_monitor_cb(sock, flags, cookie);
}
int zpath_wrapper_zpn_broker_pbroker_conn_callback(struct fohh_connection *connection,
                                                   enum fohh_connection_state state,
                                                   void *cookie) {
    return zpn_broker_pbroker_conn_callback(connection, state, cookie);
}
#else
#include "test_misc/production_macros.h"
#endif // ZPN_TESTING

STATIC_INLINE void zpath_wrapper_zpn_free(void* ptr, int line, const char* file) {
    zpath_free(ptr, line, file);
}

#define DIGITS_IN_INT64_MAX 19

extern char *geoip_db_file;
extern int64_t g_extranet_hard_disabled;

extern struct argo_structure_description *zpn_broker_mission_critical_description;

struct pbctl_fohh_info {
    struct fohh_connection *f_conn;
    int64_t f_conn_incarnation;
};

struct pbctl_tunnel_to_fohh {
    zpath_mutex_t lock;
    struct zhash_table *table;
};

struct pbctl_gid_tunnel_id_info {
    char tunnel_id[ZPN_TUNNEL_ID_BYTES_TEXT + 1];
    int64_t pbroker_gid;
    uint64_t debug_flag;
};

struct pbctl_gid_to_tunnel_id {
    zpath_mutex_t lock;
    struct zhash_table *table;
};

enum pbroker_config_conn_type {
    pbroker_config,
    pbroker_static_config,
    pbroker_override,
    pbroker_userdb
};

struct connected_pbroker_config_fohh_state {
    const char *type;
    struct fohh_connection *f_conn;

    struct event *timer;

    /* 128 bit (16 bytes, 24 bytes base64) random ID for this TLS
     * connection, in base64 */
    uint8_t tunnel_id_bin[ZPN_TUNNEL_ID_BYTES];
    char tunnel_id[ZPN_TUNNEL_ID_BYTES_TEXT + 1];
    uint64_t monitor_count;
};


static struct pbctl_tunnel_to_fohh pbctl_tunnel_to_fohh;

static struct pbctl_gid_to_tunnel_id pbctl_gid_to_tunnel;

static struct wally_fohh_server *wally_servers[ZPATH_MAX_SHARDS];

static struct wally_fohh_server *wally_servers_static[ZPATH_MAX_SHARDS];
static struct wally_fohh_server *wally_server_global;

/* Local prototypes */

static int zpn_broker_pbctl_gid_to_tunnel_lookup(int64_t pbroker_gid, char *ret_tunnel_id, size_t ret_tunnel_id_len);
static void zpn_broker_pbctl_gid_to_tunnel_store(int64_t pbroker_gid, char *tunnel_id, size_t tunnel_id_len);
static void zpn_broker_pbctl_gid_to_tunnel_remove(int64_t pbroker_gid);
static struct fohh_connection* zpn_broker_pbctl_get_fohh_connection_by_pbroker_gid(int64_t pbroker_gid);
static int zpn_broker_pbctl_get_debug_flags_by_pbroker_gid(int64_t pbroker_gid, uint64_t *debug_flag);
static int zpn_broker_pbctl_set_debug_flags_by_pbroker_gid(int64_t pbroker_gid, uint64_t debug_flag);
static int zpn_broker_pbroker_control_conn_redirect(struct fohh_connection *f_conn);
static int zpn_broker_pbroker_config_conn_redirect(struct fohh_connection *f_conn);
static int zpn_broker_pbroker_create_config_conn_cookie(struct fohh_connection *f_conn, enum pbroker_config_conn_type type);
static void zpn_broker_pbroker_destroy_config_conn_cookie(struct fohh_connection *f_conn);
static void zpn_broker_pbroker_log_conn_redirect(struct fohh_connection *f_conn, void *cookie);

/* book-keeping for all pbroker SNIs (used during clean up) */
#define MAX_ZPN_BROKER_PBROKER_SNIS 10
int zpn_broker_pbroker_snis_count = 0;
char* zpn_broker_pbroker_snis[MAX_ZPN_BROKER_PBROKER_SNIS];
int zpn_broker_pbroker_sni_flags[MAX_ZPN_BROKER_PBROKER_SNIS];
static void zpn_broker_pbroker_add_sni(char* sni_str, int wildcard);
static void zpn_broker_pbroker_update_sni(char* old_sni_str, char *new_sni_str, int wildcard);

static int zpn_broker_pbroker_validate_version(const int64_t pb_gid,
                                                wally_response_callback_f callback_f,
                                                void *callback_cookie,
                                                int64_t callback_id);
/* TODO: currently this file is not tracking all connections (ovd, userdb, etc) */
static int disconnect_pbrokers = 0;
static const char* disconnect_pbrokers_reason = NULL;
static int redirect_pbrokers = 0;
static const char* redirect_pbrokers_reason = NULL;
static int zpn_pbroker_version_cb(void *argo_cookie_ptr, void *argo_structure_cookie_ptr, struct argo_object *object);

STATIC_INLINE void zpath_wrapper_zpn_broker_pbroker_control_conn_redirect(struct fohh_connection *f_conn)
{
    zpn_broker_pbroker_control_conn_redirect(f_conn);
}

int zpn_broker_pbctl_tunnel_fohh_lookup(struct fohh_connection **conn,
                                        int64_t *conn_incarnation,
                                        const char *tunnel_id,
                                        size_t tunnel_id_len) {
    int ret = ZPN_RESULT_NOT_FOUND;

    ZPATH_MUTEX_LOCK(&pbctl_tunnel_to_fohh.lock, __FILE__, __LINE__);
    struct pbctl_fohh_info *info = zhash_table_lookup(pbctl_tunnel_to_fohh.table, tunnel_id, tunnel_id_len, NULL);
    if (info) {
        *conn = info->f_conn;
        *conn_incarnation = info->f_conn_incarnation;
        ret = ZPN_RESULT_NO_ERROR;
    }
    ZPATH_MUTEX_UNLOCK(&pbctl_tunnel_to_fohh.lock, __FILE__, __LINE__);

    return ret;
}

static void pbctl_tunnel_fohh_store(struct fohh_connection *f_conn, char *tunnel_id, size_t tunnel_id_len) {
    struct pbctl_fohh_info *info = ZPN_MALLOC(sizeof(*info));
    info->f_conn = f_conn;
    info->f_conn_incarnation = fohh_connection_incarnation(f_conn);

    ZPATH_MUTEX_LOCK(&pbctl_tunnel_to_fohh.lock, __FILE__, __LINE__);
    zhash_table_store(pbctl_tunnel_to_fohh.table, tunnel_id, tunnel_id_len, 0, info);
    ZPATH_MUTEX_UNLOCK(&pbctl_tunnel_to_fohh.lock, __FILE__, __LINE__);
};

static void pbctl_tunnel_fohh_remove(char *tunnel_id, size_t tunnel_id_len) {
    ZPATH_MUTEX_LOCK(&pbctl_tunnel_to_fohh.lock, __FILE__, __LINE__);
    struct pbctl_fohh_info *info = zhash_table_lookup(pbctl_tunnel_to_fohh.table, tunnel_id, tunnel_id_len, NULL);
    if (info) {
        zhash_table_remove(pbctl_tunnel_to_fohh.table, tunnel_id, tunnel_id_len, NULL);
        ZPN_FREE(info);
    }
    ZPATH_MUTEX_UNLOCK(&pbctl_tunnel_to_fohh.lock, __FILE__, __LINE__);
};

static int zpn_is_pbroker_authlog_lss_feature_enabled(int64_t customer_gid)
{
    char    *config_val         = NULL;
    int64_t root_customer_gid   = 0;
    int     ret                 = 0;

    root_customer_gid = ZPATH_GID_GET_CUSTOMER_GID(zpath_instance_global_state.current_config->gid);
    config_val = zpath_config_override_get_config_str( PBROKER_FEATURE_AUTH_LOG_LSS,
                                                       &config_val,
                                                       DEFAULT_PBROKER_FEATURE_AUTH_LOG_LSS,
                                                       customer_gid,
                                                       root_customer_gid,
                                                       (int64_t) ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                       (int64_t) 0 );

    if (strcmp(config_val, DEFAULT_PBROKER_FEATURE_AUTH_LOG_LSS) == 0) {
        // Feature flag is not enabled. return false
        ret = 0;
    }
    else {
        // Feature flag is enabled. return true
        ret = 1;
    }

    return ret;
}

/*
 * This function will be called to update broker's connected pbroker lat/lon/countrycode
 *
 */
int
zpn_broker_pbroker_update_pb_lat_lon(struct zpn_sys_auth_log *log, struct fohh_connection *f_conn)
{
    struct zpn_private_broker_group *group;
    int                        res;

    res = zpn_pbroker_group_get_by_gid(log->grp_gid,
                                         &group,
                                         0,
                                         NULL,
                                         NULL,
                                         0);
    if (res == ZPATH_RESULT_ASYNCHRONOUS) {
        return ZPN_RESULT_NO_ERROR;
    }
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not get private broker group info for gid = %"PRId64": %s",
                                                       log->grp_gid, zpn_result_string(res));
        return ZPN_RESULT_ERR;
    } else {
        log->lat = group->latitude;
        log->lon = group->longitude;
        if (group->country_code) {
            if (log->cc && strcmp(log->cc, group->country_code)) {
                ZPN_FREE(log->cc);
                log->cc = NULL;
            }

            if (NULL == log->cc) {
                log->cc = ZPN_STRDUP(group->country_code, strlen(group->country_code));
            }
        }
    }

    if (geoip_db_file && (log->lat == 0) && (log->lon == 0)) {
            fohh_connection_address(f_conn, &(log->pub_ip), NULL);
            char str[ARGO_INET_ADDRSTRLEN];
            double lat, lon;
            char cc[CC_STR_LEN + 1];

            if (zpath_geoip_lookup_double(&log->pub_ip, &lat, &lon, &cc[0]) == ZPATH_RESULT_NO_ERROR) {
                ZPN_DEBUG_PRIVATE_BROKER("Private Broker IP = %s, lat = %f, lon = %f, cc = %s",
                                    argo_inet_generate(str, &(log->pub_ip)), lat, lon, cc);
                log->lat = lat;
                log->lon = lon;
                cc[CC_STR_LEN] = '\0';
                if(log->cc && strcmp(log->cc, cc)){
                    ZPN_FREE(log->cc);
                    log->cc = NULL;
                }

                if(log->cc == NULL){
                    log->cc = ZPN_STRDUP(cc, strlen(cc));
                }
            }
    }

    return ZPN_RESULT_NO_ERROR;
}

static void zpn_broker_pbroker_auth_log(struct fohh_connection *f_conn, struct zpn_sys_auth_log *log, enum fohh_connection_state state)
{
    struct fohh_tcp_info info;

    log->log_date = epoch_us();
    log->g_cst = ZPATH_GID_GET_CUSTOMER_GID(log->gid);
    log->g_brk = broker_instance_id;
    log->client_type = zpn_client_type_string(zpn_client_type_private_broker);
    log->auth_type = zpn_auth_type_string(zpn_tunnel_auth_private_broker);

    if (!log->pub_ip.length) {
        uint16_t pub_port_ne;
        fohh_connection_address_and_port(f_conn, &(log->pub_ip), &pub_port_ne, NULL, NULL);
        log->pub_port = ntohs(pub_port_ne);
    }

    memset(&info, 0, sizeof(info));
    if (fohh_connection_get_tcp_info(f_conn, &info) == FOHH_RESULT_NO_ERROR) {
        log->tcpi_snd_mss = info.tcpi_snd_mss;
        log->tcpi_rcv_mss = info.tcpi_rcv_mss;
        log->tcpi_rtt = info.tcpi_rtt;
        log->tcpi_rttvar = info.tcpi_rttvar;
        log->tcpi_snd_cwnd = info.tcpi_snd_cwnd;
        log->tcpi_advmss = info.tcpi_advmss;
        log->tcpi_reordering = info.tcpi_reordering;
        log->tcpi_rcv_rtt = info.tcpi_rcv_rtt;
        log->tcpi_rcv_space = info.tcpi_rcv_space;
        log->tcpi_total_retrans = info.tcpi_total_retrans;
        log->tcpi_thru_put = info.tcpi_thru_put;
        log->tcpi_unacked = info.tcpi_unacked;
        log->tcpi_sacked = info.tcpi_sacked;
        log->tcpi_lost = info.tcpi_lost;
        log->tcpi_fackets = info.tcpi_fackets;

        log->tcpi_last_data_sent = info.tcpi_last_data_sent;
        log->tcpi_last_ack_sent = info.tcpi_last_ack_sent;
        log->tcpi_last_data_recv = info.tcpi_last_data_recv;
        log->tcpi_last_ack_recv = info.tcpi_last_ack_recv;
        log->tcpi_bytes_acked = info.tcpi_bytes_acked;
        log->tcpi_bytes_received = info.tcpi_bytes_received;
        log->tcpi_segs_out = info.tcpi_segs_out;
        log->tcpi_segs_in = info.tcpi_segs_in;

        if ((info.tcpi_rtt / 1000) > CONTROL_CONN_HIGH_RTT_LOG_THRESHOLD_MS) {
            ZPN_LOG(AL_NOTICE, "%s: Private Broker connection high RTT: %d ms, exceeded RTT threshold: %d ms", fohh_description(f_conn), info.tcpi_rtt / 1000, CONTROL_CONN_HIGH_RTT_LOG_THRESHOLD_MS);
        }
    }

    fohh_connection_get_stats(f_conn,
                              &(log->transmit_bytes),
                              &(log->receive_bytes),
                              &(log->transmit_objects),
                              &(log->receive_objects),
                              &(log->transmit_raw_tlv),
                              &(log->receive_raw_tlv));
    /* Skip quiet app connections */
    if (!fohh_connection_is_quiet(f_conn)) {
        log->app_rtt_us = fohh_connection_get_max_app_rtt(f_conn);
        if (fohh_histogram_get_counts_array(f_conn, FOHH_HISTOGRAM_APP_RTT, log->app_rtt, FOHH_HISTOGRAM_MAX_BUCKETS) == FOHH_RESULT_NO_ERROR) {
            log->app_rtt_count = FOHH_HISTOGRAM_MAX_BUCKETS;
        } else {
            ZPN_LOG(AL_DEBUG, "%s: Unable to get app rtt histogram", fohh_description(f_conn));
        }
    }

    /* FIXME: Do we need to send max value for all TCP_INFO metrics? */
    log->tcp_rtt_us = fohh_connection_get_max_tcp_rtt(f_conn);
    if (fohh_histogram_get_counts_array(f_conn, FOHH_HISTOGRAM_TCP_RTT, log->tcp_rtt, FOHH_HISTOGRAM_MAX_BUCKETS) == FOHH_RESULT_NO_ERROR) {
        log->tcp_rtt_count = FOHH_HISTOGRAM_MAX_BUCKETS;
    } else {
        ZPN_LOG(AL_DEBUG, "%s: Unable to get tcp rtt histogram", fohh_description(f_conn));
    }

    if (fohh_histogram_get_counts_array(f_conn, FOHH_HISTOGRAM_TCP_WIN, log->tcp_congestion_win, FOHH_HISTOGRAM_MAX_BUCKETS) == FOHH_RESULT_NO_ERROR) {
        log->tcp_congestion_win_count = FOHH_HISTOGRAM_MAX_BUCKETS;
    } else {
        ZPN_LOG(AL_DEBUG, "%s: Unable to get tcp congestion win histogram", fohh_description(f_conn));
    }

    if (state == fohh_connection_connected) {
        if (!log->auth_us) log->auth_us = epoch_us();
        log->status = ZPN_STATUS_AUTHENTICATED;
    } else {
        log->deauth_us = epoch_us();
        log->status = ZPN_STATUS_DISCONNECTED;
    }

    log->tlv_type = zpn_tlv_type_str(zpn_fohh_tlv);
    zpn_broker_pbroker_update_pb_lat_lon(log, f_conn);

    /* Log to local for debug purpose if enabled */
    if (zpn_pb_auth_collection) {
        argo_log_structure_immediate(zpn_pb_auth_collection,
                                     argo_log_priority_info,
                                     0,
                                     "sys_auth_log",
                                     zpn_sys_auth_log_description,
                                     log);
    }

    if(zpn_is_pbroker_authlog_lss_feature_enabled(log->g_cst)) {
        zpn_broker_siem_pb_auth_log(log, 1);
    }

    zpath_customer_log_struct(log->g_cst,
                              zpath_customer_log_type_zpn_sys_auth,
                              "sys_auth",
                              NULL,
                              NULL,
                              NULL,
                              NULL,
                              zpn_sys_auth_log_description,
                              log);
    if (log->slogger_info) {
        ZPN_FREE(log->slogger_info);
        log->slogger_info = NULL;
    }
}

static void pb_auth_log(struct fohh_connection *f_conn, enum fohh_connection_state state)
{
    struct zpn_broker_pbroker_fohh_state *pb_state = fohh_connection_get_dynamic_cookie(f_conn);
    struct zpn_sys_auth_log *log = &(pb_state->log);

    if (pb_state->status_report_received == 0) {
        return;
    }

    log->tunnel_type = ZPN_TUNNEL_CONTROL;

    zpn_broker_pbroker_auth_log(f_conn, log, state);

    pb_state->first_auth_log_sent = 1;
}

static void cleanup_sys_auth_log_brokers_unique(struct zpn_sys_auth_log *log) {
    if (log->log_brokers_uniq) {
        for (int i = 0; i < log->log_brokers_uniq_count; ++i) {
            ZPN_FREE(log->log_brokers_uniq[i]);
        }
        ZPN_FREE(log->log_brokers_uniq);
        log->log_brokers_uniq = NULL;
        log->log_brokers_uniq_count = 0;
    }
}

static void cleanup_sys_auth_log_all_log_brokers_data(struct zpn_sys_auth_log *log) {
    if (log->all_log_brokers_data) {
        for (int i = 0; i < log->all_log_brokers_data_count; ++i) {
            ZPN_FREE(log->all_log_brokers_data[i]);
            log->all_log_brokers_data[i] = NULL;
        }
        ZPN_FREE(log->all_log_brokers_data);
        log->all_log_brokers_data = NULL;
        log->all_log_brokers_data_count = 0;
    }

}

static int zpn_pbroker_status_report_cb(void *argo_cookie_ptr,
                                        void *argo_structure_cookie_ptr,
                                        struct argo_object *object)
{
    struct zpn_pbroker_status_report *pbreport = object->base_structure_void;
    struct fohh_connection *f_conn = argo_structure_cookie_ptr;
    struct zpn_broker_pbroker_fohh_state *pb_state;
    struct zpn_sys_auth_log *log;
    if (zpn_debug_get(ZPN_DEBUG_PRIVATE_BROKER_LOAD_IDX)) {
        char buf[ARGO_BUF_DEFAULT_SIZE];
        if (argo_object_dump(object, buf, sizeof(buf), NULL, 0) == ARGO_RESULT_NO_ERROR) {
            ZPN_DEBUG_PRIVATE_BROKER_LOAD("%s: %ld: %s", fohh_description(f_conn), (long)fohh_peer_get_id(f_conn), buf);
        }
    }

    pb_state = fohh_connection_get_dynamic_cookie(f_conn);
    if (!pb_state) {
        ZPN_LOG(AL_NOTICE, "Failed to retrieve pb state from control connection");
        return ZPATH_RESULT_NO_ERROR;
    }
    log = &(pb_state->log);
    log->cpu_util = pbreport->cpu_util;
    log->mem_util =  pbreport->mem_util;
    log->number_of_clients = pbreport->number_of_clients;
    log->udp4_port_util = pbreport->udp4_port_util;
    log->udp6_port_util = pbreport->udp6_port_util;
    log->sys_fd_util = pbreport->sys_fd_util;
    log->proc_fd_util = pbreport->proc_fd_util;

    log->last_os_upgrade_time = pbreport->last_os_upgrade_time;
    log->last_sarge_upgrade_time = pbreport->last_sarge_upgrade_time;

    log->vm_start_s = pbreport->sys_uptime_s;
    log->app_start_s = pbreport->pb_uptime_s;

    if (log->dft_rt_intf) {
        ZPN_FREE(log->dft_rt_intf);
        log->dft_rt_intf = NULL;
    }
    if (log->platform_version) {
        ZPN_FREE(log->platform_version);
        log->platform_version = NULL;
    }

    if (pbreport->dft_rt_intf) {
        log->dft_rt_intf = ZPN_STRDUP(pbreport->dft_rt_intf, strlen(pbreport->dft_rt_intf));
    }
    if (pbreport->platform_version) {
        log->platform_version = ZPN_STRDUP(pbreport->platform_version, strlen(pbreport->platform_version));
    }

    log->dft_rt_gw = pbreport->dft_rt_gw;
    log->resolver = pbreport->resolver;

    log->intf_count = pbreport->intf_count;
    log->intf_rb = pbreport->intf_rb;
    log->intf_rp = pbreport->intf_rp;
    log->intf_re = pbreport->intf_re;
    log->intf_rd = pbreport->intf_rd;
    log->intf_tb = pbreport->intf_tb;
    log->intf_tp = pbreport->intf_tp;
    log->intf_te = pbreport->intf_te;
    log->intf_td = pbreport->intf_td;

    log->delta_intf_rb = pbreport->delta_intf_rb;
    log->delta_intf_rp = pbreport->delta_intf_rp;
    log->delta_intf_re = pbreport->delta_intf_re;
    log->delta_intf_rd = pbreport->delta_intf_rd;
    log->delta_intf_tb = pbreport->delta_intf_tb;
    log->delta_intf_tp = pbreport->delta_intf_tp;
    log->delta_intf_te = pbreport->delta_intf_te;
    log->delta_intf_td = pbreport->delta_intf_td;

    log->total_intf_b = pbreport->total_intf_b;
    log->total_intf_p = pbreport->total_intf_p;
    log->total_intf_e = pbreport->total_intf_e;
    log->total_intf_d = pbreport->total_intf_d;

    log->delta_total_intf_b = pbreport->delta_total_intf_b;
    log->delta_total_intf_p = pbreport->delta_total_intf_p;
    log->delta_total_intf_e = pbreport->delta_total_intf_e;
    log->delta_total_intf_d = pbreport->delta_total_intf_d;
    zpn_broker_pbroker_update_pb_lat_lon(log, f_conn);

    snprintf(log->ovd_broker_cn, sizeof(log->ovd_broker_cn), "%s", pbreport->ovd_broker_cn);

    cleanup_sys_auth_log_brokers_unique(log);
    if (pbreport->log_brokers_uniq_count) {
        log->log_brokers_uniq = ZPN_CALLOC(sizeof(char*)* pbreport->log_brokers_uniq_count);
        int ii = 0;
        for (ii = 0; ii < pbreport->log_brokers_uniq_count; ii++) {
            log->log_brokers_uniq[ii] = ZPN_STRDUP(pbreport->log_brokers_uniq[ii], strlen(pbreport->log_brokers_uniq[ii]));
        }
        log->log_brokers_uniq_count = pbreport->log_brokers_uniq_count;
    }

    cleanup_sys_auth_log_all_log_brokers_data(log);
    if (pbreport->all_log_brokers_data_count) {
        log->all_log_brokers_data = ZPN_CALLOC(sizeof(char*)* pbreport->all_log_brokers_data_count);
        int ii = 0;
        for (ii = 0; ii < pbreport->all_log_brokers_data_count; ii++) {
            log->all_log_brokers_data[ii] = ZPN_STRDUP(pbreport->all_log_brokers_data[ii], strlen(pbreport->all_log_brokers_data[ii]));
        }
        log->all_log_brokers_data_count = pbreport->all_log_brokers_data_count;
    }

    pb_state->status_report_received = 1;
    if (pb_state->first_auth_log_sent == 0) {
        pb_auth_log(f_conn, fohh_get_state(f_conn));
    }
    return ZPATH_RESULT_NO_ERROR;
}

// One time callback.
static int zpn_pbroker_environment_report_cb(void *argo_cookie_ptr,
                                             void *argo_structure_cookie_ptr,
                                             struct argo_object *object)
{
    struct zpn_pbroker_environment_report *report = object->base_structure_void;
    if (!report->sarge_version)
        goto done;

    struct fohh_connection *f_conn = argo_structure_cookie_ptr;
    struct zpn_broker_pbroker_fohh_state *pb_state = fohh_connection_get_dynamic_cookie(f_conn);
    pb_state->log_sarge_version = ZPN_STRDUP(report->sarge_version, strlen(report->sarge_version));
    pb_state->log.sarge_version = pb_state->log_sarge_version;

done:
    return ZPATH_RESULT_NO_ERROR;
}

static int zpn_private_broker_load_cb(void *argo_cookie_ptr,
                                      void *argo_structure_cookie_ptr,
                                      struct argo_object *object)
{
    struct zpn_private_broker_load *pbl = object->base_structure_void;
    struct zpn_private_broker *pb;
    struct fohh_connection *f_conn = argo_structure_cookie_ptr;
    int64_t gid;
    int res;
    if (zpn_debug_get(ZPN_DEBUG_PRIVATE_BROKER_LOAD_IDX)) {
        char buf[2000];
        if (argo_object_dump(object, buf, sizeof(buf), NULL, 0) == ARGO_RESULT_NO_ERROR) {
            ZPN_DEBUG_PRIVATE_BROKER_LOAD("%s: %ld: %s", fohh_description(f_conn), (long)fohh_peer_get_id(f_conn), buf);
        }
    }

    gid = fohh_peer_get_id(f_conn);
    if (!gid) {
        ZPN_LOG(AL_ERROR, "%s: Invalid peer", fohh_description(f_conn));
        return ZPATH_RESULT_NO_ERROR;
    }

    if (ZPATH_GID_GET_CUSTOMER_GID(gid) != pbl->customer_gid) {
        ZPN_LOG(AL_ERROR, "%s: Invalid peer, is %ld, says customer is %ld", fohh_description(f_conn), (long) gid, (long) pbl->customer_gid);
        return ZPATH_RESULT_NO_ERROR;
    }

    res = zpn_private_broker_get_by_id(gid, &pb, NULL, NULL, 0);
    if (res) {
        if (res != ZPN_RESULT_ASYNCHRONOUS) {
            /* Some error. Yuck. */
            ZPN_LOG(AL_ERROR, "%s: Could not get private broker for gid = %ld: %s", fohh_description(f_conn), (long) gid, zpn_result_string(res));
        }
        return ZPATH_RESULT_NO_ERROR;
    }
    if (!pb->enabled) {
        ZPN_LOG(AL_NOTICE, "%s: private broker not enabled. Not reporting load", fohh_description(f_conn));
        return ZPATH_RESULT_NO_ERROR;
    }

    pbl->modified_time = epoch_s();
    pbl->gid = gid;

    res = zpn_private_broker_load_write(gid,
                                        pbl->cpu_util,
                                        pbl->mem_util,
                                        pbl->active_mtun,
                                        pbl->clients,
                                        pbl->bytes_xfer,
                                        pbl->publish,
                                        pbl->publish_count);
    if (res) {
        ZPN_LOG(AL_ERROR, "%s: Error writing private broker load: %s", fohh_description(f_conn), zpn_result_string(res));
    }
    return ZPATH_RESULT_NO_ERROR;
}

/* This is called every ZPN_TUNNEL_MONITOR_INTERVAL_S seconds */
static void zpn_broker_pbroker_control_conn_monitor_cb(evutil_socket_t sock, short flags, void *cookie)
{
    int res;
    int64_t customer_id = 0;
    struct zpath_customer *customer = NULL;
    struct fohh_connection *f_conn = cookie;
    struct zpn_broker_pbroker_fohh_state *pb_state = fohh_connection_get_dynamic_cookie(f_conn);
    if (!pb_state) {
        ZPN_LOG(AL_NOTICE, "%s: Private broker control conn monitor cb has no cookie", fohh_description(f_conn));
        return;
    }
    pb_state->monitor_count++;

    /* Drop connection when broker is shutting down. */
    if (disconnect_pbrokers) {
        const char *reason = disconnect_pbrokers_reason ? : FOHH_CLOSE_REASON_UPGRADE;
        ZPN_LOG(AL_INFO, "%s: Disconnect pbroker control connection due to %s",
                fohh_description(f_conn), reason);
        fohh_connection_delete(f_conn, reason);
        return;
    }

    /* Drop connection when customer is disabled */
    customer_id = ZPATH_GID_GET_CUSTOMER_GID(fohh_peer_get_id(f_conn));
    if (customer_id != 0) {
        res = zpath_customer_get(customer_id,
                                 &customer,
                                 NULL,
                                 NULL,
                                 0);

        if (res == ZPN_RESULT_NOT_FOUND || (res == ZPN_RESULT_NO_ERROR && customer->disable_traffic == 1)) {
            ZPN_LOG(AL_NOTICE, "%s: Disconnecting pbroker(%"PRId64") as customer(%"PRId64") is not found or is disabled",
                            fohh_description(f_conn), fohh_peer_get_id(f_conn), customer_id);
            /* Kill the connection */
            fohh_connection_delete(f_conn, ZPN_ERR_CUSTOMER_DISABLED);
            return;
        }
    }

    if (redirect_pbrokers &&
        ((pb_state->monitor_count % ZPN_TUNNEL_MONITOR_REDIRECT_INTERVAL_COUNT) == 0
         || strcmp(redirect_pbrokers_reason, BRK_REDIRECT_REASON_BROKER_RESTART) == 0)) {
        ZPN_LOG(AL_INFO, "%s: Redirect pbroker control connection due to %s",
                fohh_description(f_conn),
                redirect_pbrokers_reason ? : FOHH_CLOSE_REASON_UPGRADE);
        zpath_wrapper_zpn_broker_pbroker_control_conn_redirect(f_conn);

        if (zpn_is_broker_auth_log_redirect_status_feature_enabled() && zpn_is_redirect_reason_maintanence(redirect_pbrokers_reason)) {
            pb_state->is_redirect_sent = 1;
        }
    }

    if ((pb_state->monitor_count % ZPN_TUNNEL_MONITOR_LOG_INTERVAL_COUNT) == 0) {
        pb_auth_log(f_conn, fohh_get_state(f_conn));
    }
}

/* This is called every ZPN_TUNNEL_MONITOR_INTERVAL_S seconds */
static void zpn_broker_pbroker_config_conn_monitor_cb(evutil_socket_t sock, short flags, void *cookie)
{
    int res;
    int64_t customer_id = 0;
    struct zpath_customer *customer = NULL;
    struct connected_pbroker_config_fohh_state *pb_state = cookie;
    if (!pb_state) {
        ZPN_LOG(AL_NOTICE, "Private broker config conn monitor cb has no cookie");
        return;
    }

    pb_state->monitor_count++;

    struct fohh_connection *f_conn = pb_state->f_conn;

    /* Drop connection when broker is shutting down. */
    if (disconnect_pbrokers) {
        const char *reason = disconnect_pbrokers_reason ? : FOHH_CLOSE_REASON_UPGRADE;
        ZPN_LOG(AL_INFO, "%s: Disconnect pbroker %s connection due to %s",
                fohh_description(f_conn), pb_state->type, reason);
        fohh_connection_delete(f_conn, reason);
        return;
    }

    /* Drop connection when customer is disabled */
    customer_id = ZPATH_GID_GET_CUSTOMER_GID(fohh_peer_get_id(f_conn));
    if (customer_id != 0) {
        res = zpath_customer_get(customer_id,
                                 &customer,
                                 NULL,
                                 NULL,
                                 0);

        if (res == ZPN_RESULT_NOT_FOUND || (res == ZPN_RESULT_NO_ERROR && customer->disable_traffic == 1)) {
            ZPN_LOG(AL_NOTICE, "%s: Disconnecting pbroker(%"PRId64") as customer(%"PRId64") is not found or is disabled",
                            fohh_description(f_conn), fohh_peer_get_id(f_conn), customer_id);
            /* Kill the connection */
            fohh_connection_delete(f_conn, ZPN_ERR_CUSTOMER_DISABLED);
            return;
        }
    }

    if (redirect_pbrokers &&
        ((pb_state->monitor_count % ZPN_TUNNEL_MONITOR_REDIRECT_INTERVAL_COUNT) == 0
         || strcmp(redirect_pbrokers_reason, BRK_REDIRECT_REASON_BROKER_RESTART) == 0)) {
        ZPN_LOG(AL_INFO, "%s: Redirect pbroker %s connection due to %s",
                fohh_description(f_conn),
                pb_state->type,
                redirect_pbrokers_reason ? : FOHH_CLOSE_REASON_UPGRADE);
        zpn_broker_pbroker_config_conn_redirect(pb_state->f_conn);
    }
}

static void zpn_broker_pbroker_conn_clean(struct fohh_connection *connection)
{
    /* Free/clear all memory/events. Does NOT release the connection */
    ZPN_LOG(AL_CRITICAL, "Implement me- clean conn");
}

static int pbroker_tcp_info_report_cb(void *argo_cookie_ptr,
                                      void *argo_structure_cookie_ptr,
                                      struct argo_object *object)
{
    struct fohh_connection *f_conn = argo_structure_cookie_ptr;
    struct zpn_broker_pbroker_fohh_state *pb_state = fohh_connection_get_dynamic_cookie(f_conn);
    struct zpn_tcp_info_report *req = object->base_structure_void;

    /* Do nothing for now, we will use this later */
    ZPN_LOG(AL_DEBUG, "Static Wally Capability string connection = %p value = %d", f_conn, req->static_wally_enabled);

    /* Only thing we are grabbing at the moment from the tcp_info
       state is the private IP and version */
    pb_state->log.priv_ip = req->priv_ip;
    if (pb_state->log_version) {
        ZPN_FREE(pb_state->log_version);
    }

    pb_state->log_version = ZPN_STRDUP(req->version, strlen(req->version));
    pb_state->log.version = pb_state->log_version;

    if (pb_state->log.platform) {
        ZPN_FREE(pb_state->log.platform);
        pb_state->log.platform = NULL;
    }

    if (pb_state->log.platform_detail) {
        ZPN_FREE(pb_state->log.platform_detail);
        pb_state->log.platform_detail = NULL;
    }

    if (pb_state->log.runtime_os) {
        ZPN_FREE(pb_state->log.runtime_os);
        pb_state->log.runtime_os = NULL;
    }

    if(pb_state->log.geoip_version) {
        ZPN_FREE(pb_state->log.geoip_version);
        pb_state->log.geoip_version = NULL;
    }

    if(pb_state->log.isp_version) {
        ZPN_FREE(pb_state->log.isp_version);
        pb_state->log.isp_version = NULL;
    }

    if (pb_state->log.platform_arch) {
        ZPN_FREE(pb_state->log.platform_arch);
        pb_state->log.platform_arch = NULL;
    }

    char *platform = req->platform ? req->platform : "UNKNOWN";
    pb_state->log.platform = ZPN_STRDUP(platform, strlen(platform));

    char *geoip_version = req->geoip_version ? req->geoip_version : "\0";
    char *isp_version = req->isp_version ? req->isp_version : "\0";
    pb_state->log.geoip_version = ZPN_STRDUP(geoip_version, strlen(geoip_version));
    pb_state->log.isp_version = ZPN_STRDUP(isp_version, strlen(isp_version));

    const char *platform_detail = req->platform_detail ? req->platform_detail : "UNKNOWN";
    pb_state->log.platform_detail = ZPN_STRDUP(platform_detail, strlen(platform_detail));

    const char *platform_arch = req->platform_arch ? req->platform_arch : "UNKNOWN";
    pb_state->log.platform_arch = ZPN_STRDUP(platform_arch, strlen(platform_arch));

    const char *runtime_os = req->runtime_os ? req->runtime_os : "Unknown";
    pb_state->log.runtime_os = ZPN_STRDUP(runtime_os, strnlen(runtime_os, FOHH_MAX_NAMELEN));

    ZPN_DEBUG_PRIVATE_BROKER("Private Broker gid %"PRId64", Platform = %s and Platform Detail = %s, Platform arch = %s, runtime OS = %s",
                              pb_state->log.gid, platform, platform_detail, platform_arch, runtime_os);

    /* If we have already figured out private broker group, then send an auth log */
    if (!pb_state->received_tcp_info) {
        pb_state->received_tcp_info = 1;
        if (pb_state->log.grp_gid) {
            pb_auth_log(f_conn, fohh_get_state(f_conn));
        }
    }
    return ZPATH_RESULT_NO_ERROR;
}

static int pbroker_broker_request_cb(void *argo_cookie_ptr,
                                     void *argo_structure_cookie_ptr,
                                     struct argo_object *object) {
    struct fohh_connection *f_conn = argo_structure_cookie_ptr;
    struct zpn_broker_request *msg = object->base_structure_void;
    struct zpn_broker_pbroker_fohh_state *pb_state = fohh_connection_get_dynamic_cookie(f_conn);

    if (zpn_debug_get(ZPN_DEBUG_DISPATCHER_IDX)) {
        char dump[8000];
        if (argo_object_dump(object, dump, sizeof(dump), NULL, 0) == ARGO_RESULT_NO_ERROR) {
            ZPN_LOG(AL_DEBUG, "%s: Rx: from %s: %s", pb_state->tunnel_id, fohh_peer_cn(f_conn), dump);
        }
    }

    if (!msg->g_app) {
        ZPN_LOG(AL_WARNING, "%s: app id is not set for broker request", pb_state->tunnel_id);
        return ZPN_RESULT_BAD_DATA;
    } else if (!msg->mtunnel_id) {
        ZPN_LOG(AL_WARNING, "%s: mtunnel_id is not set for broker request", pb_state->tunnel_id);
        return ZPN_RESULT_BAD_DATA;
    } else if (!msg->req_type || strcmp(msg->req_type, ZPN_BRK_REQ_TYPE_PEEK) != 0) {
        ZPN_LOG(AL_WARNING, "%s: wrong broker request type", pb_state->tunnel_id);
        return ZPN_RESULT_BAD_DATA;
    } else if (!msg->domain) {
        ZPN_LOG(AL_WARNING, "%s: domain is not set for broker request", pb_state->tunnel_id);
        return ZPN_RESULT_BAD_DATA;
    } else if (!msg->c_port) {
        ZPN_LOG(AL_WARNING, "%s: port is not set for broker request", pb_state->tunnel_id);
        return ZPN_RESULT_BAD_DATA;
    } else if (!msg->ip_protocol) {
        ZPN_LOG(AL_WARNING, "%s: protocol is not set for broker request", pb_state->tunnel_id);
        return ZPN_RESULT_BAD_DATA;
    }
    // TODO: Add more checks.

    msg->g_brk = broker_instance_id;
    msg->brk_name = zpn_broker_get_instance_full_name();
    msg->mtunnel_id = PB_PRE_LEARN_REQUEST_MTUNNEL_ID;

    if (zpn_broker_dispatch_send_request(msg, zpn_err_brk_req_no_error, 0, NULL)) {
        ZPN_LOG(AL_WARNING, "%s: could not send pre-learn broker request to dispatcher", pb_state->tunnel_id);
    }

    return ZPN_RESULT_NO_ERROR;
}

static int pbroker_dns_dispatch_check_cb(void *argo_cookie_ptr,
                                         void *argo_structure_cookie_ptr,
                                         struct argo_object *object) {
    struct fohh_connection *f_conn = argo_structure_cookie_ptr;
    struct zpn_dns_dispatch_check *check_in = object->base_structure_void;
    struct zpn_broker_pbroker_fohh_state *pb_state = fohh_connection_get_dynamic_cookie(f_conn);
    struct zpn_dns_dispatch_check *cached_check = NULL;
    int64_t scope_gid = 0;

    int64_t g_app;
    int is_wildcard;
    char buf[ARGO_BUF_DEFAULT_SIZE];
    int res;
    struct zpn_application *app = NULL;

    if (zpn_debug_get(ZPN_DEBUG_BROKER_DNS_IDX)) {
        if (argo_object_dump(object, buf, sizeof(buf), NULL, 0) == ARGO_RESULT_NO_ERROR) {
            ZPN_LOG(AL_DEBUG, "%s: %s: Ctl rx zpn_dns_dispatch_check: %s", pb_state->tunnel_id, fohh_peer_cn(f_conn), buf);
        }
    }

    if (!ZPN_DNS_CHECK_TARGETS_EQUAL(check_in)) {
        ZPN_LOG(AL_ERROR, "Bad check counts");
        if (argo_object_dump(object, buf, sizeof(buf), NULL, 0) == ARGO_RESULT_NO_ERROR) {
            ZPN_LOG(AL_ERROR, "%s: %s: Rx zpn_dns_dispatch_check: %s", pb_state->tunnel_id, fohh_peer_cn(f_conn), buf);
        }
        return ZPN_RESULT_ERR;
    }

    if (!pb_state) {
        ZPN_LOG(AL_ERROR, "%s: No pb_state", fohh_description(f_conn));
        return ZPN_RESULT_NO_ERROR;
    }

    struct zpn_dns_dispatch_check check_out = {0};
    check_out.id = check_in->id;
    check_out.name = check_in->name;
    check_out.type = check_in->type;

    if (!check_in->name || !check_in->type) {
        check_out.error = ZPN_ERR_BAD_REQUEST;
    } else if ((strcmp(check_in->type, ZPN_DNS_CHECK_TYPE_A) != 0) &&
               (strcmp(check_in->type, ZPN_DNS_CHECK_TYPE_AAAA) != 0) &&
               (strcmp(check_in->type, ZPN_DNS_CHECK_TYPE_SRV) != 0) &&
               (strcmp(check_in->type, ZPN_DNS_CHECK_TYPE_TXT) != 0)) {
        ZPN_DEBUG_APPLICATION("%s: Rx zpn_dns_dispatch_check: %ld : %s : %s, Invalid type", pb_state->tunnel_id,
                              (long) check_in->id, check_in->name, check_in->type);
        check_out.error = ZPN_ERR_BAD_REQUEST;
    } else {
        zpath_downcase(check_in->name);

        // This will be user's scope gid that connected to PSE
        scope_gid = check_in->scope_gid;

        if (zpn_is_dr_mode_active()) {
            g_app = zpn_application_domain_search_by_customer(pb_state->log.g_cst, check_in->name, strnlen(check_in->name, MAX_DOMAIN_LEN_SIZE), NULL,
                                                              NULL, &is_wildcard, 0, 0, zpn_pbroker_conn_search, zpn_client_type_private_broker);
        } else {
            g_app = zpn_application_domain_search(scope_gid, check_in->name, strnlen(check_in->name, MAX_DOMAIN_LEN_SIZE), NULL, NULL,
                                                  &is_wildcard, 0, 0, zpn_pbroker_conn_search, zpn_client_type_private_broker);
        }
        if (!g_app || zpn_application_get_by_id_immediate(g_app, &app) != ZPN_RESULT_NO_ERROR) {
            ZPN_DEBUG_APPLICATION("%s: Rx zpn_dns_dispatch_check: %ld : %s : %s, Application not found",
                                  pb_state->tunnel_id, (long) check_in->id, check_in->name, check_in->type);
            check_out.error = ZPN_ERR_INVALID_APPLICATION;
            if (g_app) {
                zpn_application_search_debug_stats_counter(zpn_pbroker_conn_search, zpn_application_not_found);
            }
        } else {
            if (check_in->is_extranet_app) {
                char *err_str = "";
                int pse_in_dr_mode = 0;
                if (g_extranet_hard_disabled) {
                    err_str = "hard disabled";
                    check_out.error = ZPN_DNS_REJECT_HARD_DISABLED_EXTRANET;
                } else if (zpn_broker_is_extranet_enabled(ZPATH_GID_GET_CUSTOMER_GID(fohh_peer_get_id(f_conn)), &pse_in_dr_mode) == 0) {
                    if (pse_in_dr_mode) {
                        err_str = "not allowed in DR mode";
                    } else {
                        err_str = "not enabled";
                    }
                    check_out.error = ZPN_DNS_REJECT_EXTRANET_NOT_ENABLED;
                } else {
                    check_out.error = NULL;
                }
                if (check_out.error) {
                    ZPN_LOG(AL_ERROR, "Tunnel id %s, accessing extranet app %s, however extranet feature is %s",
                                pb_state->tunnel_id, check_in->name, err_str);
                    goto out;
                }
            }
            // If non wildcard and not SRV and TXT FQDN and fqdn dns check is disabled and strict DNS checking is NOT requested, we return immediate result.
            if (!is_wildcard && (strcmp(check_in->type, ZPN_DNS_CHECK_TYPE_SRV) != 0) &&
                (strcmp(check_in->type, ZPN_DNS_CHECK_TYPE_TXT) != 0) && !app->fqdn_dns_check && !check_in->strict) {
                ZPN_DEBUG_APPLICATION(
                        "%s: Rx zpn_dns_dispatch_check: %ld : %s : %s, Application is not wildcard, sending immediate response",
                        pb_state->tunnel_id, (long) check_in->id, check_in->name, check_in->type);
            } else {
                res = zpn_broker_dns_query(pb_state->log.g_cst,
                                           pb_state->tunnel_id, // Pass PB tunnel id.
                                           check_in->id,
                                           check_in->type,
                                           check_in->name,
                                           strlen(check_in->name),
                                           1,
                                           &cached_check,
                                           NULL,
                                           0,
                                           check_in->strict,
                                           0, // No need to pass scope gid as this request will go from public broker to dispatcher
                                           check_in->zia_cloud_name,
                                           check_in->zia_org_id,
                                           check_in->is_extranet_app);

                if (res && (res == ZPN_RESULT_ASYNCHRONOUS)) {
                    /* This is the only case where we do not send an
                    * immediate response */
                    ZPN_DEBUG_APPLICATION(
                            "%s: Rx zpn_dns_dispatch_check: %ld : %s : %s, Waiting for asynchronous response from dispatcher",
                            pb_state->tunnel_id, (long) check_in->id, check_in->name, check_in->type);
                    return ZPN_RESULT_NO_ERROR;
                } else if (res && (res == ZPN_RESULT_NOT_FOUND)) {
                    ZPN_DEBUG_APPLICATION("%s: Rx zpn_dns_dispatch_check: %ld : %s : %s, DNS failed", pb_state->tunnel_id,
                                        (long) check_in->id, check_in->name, check_in->type);
                    check_out.error = ZPN_ERR_INVALID_APPLICATION;
                    zpn_application_search_debug_stats_counter(zpn_pbroker_conn_search, zpn_dns_query_not_found);
                } else if (res && (res == ZPN_RESULT_INCOMPLETE)) {
                        /* On public broker, this error code means we have found a valid cache entry,
                         * but cannot use it due to strict being requested and DNS type mismatch
                         * However: send the DNS response object back to private broker as it..and
                         * let private broker take an appropriate call on its end
                         */
                    check_out.strict = check_in->strict;
                    zpn_broker_dns_dispatch_check_fill_from_cache(&check_out, cached_check, check_in->id);
                } else if (res) {
                    ZPN_LOG(AL_ERROR, "%s: Rx zpn_dns_dispatch_check: %ld : %s : %s, Error %s performing DNS",
                            pb_state->tunnel_id, (long) check_in->id, check_in->name, check_in->type,
                            zpn_result_string(res));
                    check_out.error = ZPN_ERR_INVALID_APPLICATION;
                    zpn_application_search_debug_stats_counter(zpn_pbroker_conn_search, zpn_dns_query_failed);
                } else {
                    /* Success! From cache! */
                    zpn_broker_dns_dispatch_check_fill_from_cache(&check_out, cached_check, check_in->id);
                    /* Override has_a and has_aaaa fields on PSE if non-strict case */
                    if (!check_in->strict) {
                        check_out.has_a = 0;
                        check_out.has_aaaa = 0;
                    }
                }
            }
        }
    }
out:
    /* check has been filled in at this point, and must be transmitted back to pbroker */
    res = zpn_send_zpn_dns_dispatch_check_struct(f_conn, 0, &check_out);
    if (res) {
        ZPN_LOG(AL_WARNING, "%s: overrun? zpn_send_zpn_dns_dispatch_check failed: %s", pb_state->tunnel_id,
                zpn_result_string(res));
    }

    /* And log it (+ tunnel ID) */
    check_out.tunnel_id = pb_state->tunnel_id;
    res = zpn_broker_dns_log_dispatch(&check_out);
    if (res) {
        ZPN_LOG(AL_WARNING, "%s: Could not log dispatch DNS check: %s", pb_state->tunnel_id, zpn_result_string(res));
    }

    if (cached_check) {
        argo_structure_release(cached_check);
    }

    return ZPN_RESULT_NO_ERROR;
}

static void get_pbroker_group(struct fohh_connection *f_conn);
static int get_pbroker_resume(void *response_callback_cookie,
                              struct wally_registrant *registrant,
                              struct wally_table *table,
                              int64_t request_id,
                              int row_count)
{
    struct fohh_connection *f_conn = response_callback_cookie;
    if (fohh_connection_incarnation(f_conn) != request_id) {
        /* Connection changed behind our back */
        return ZPATH_RESULT_NO_ERROR;
    }
    get_pbroker_group(f_conn);
    return ZPATH_RESULT_NO_ERROR;
}

static void get_pbroker_group(struct fohh_connection *f_conn)
{
    struct zpn_broker_pbroker_fohh_state *pb_state;
    int res;
    struct zpn_private_broker_to_group *to_group;


    pb_state = fohh_connection_get_dynamic_cookie(f_conn);
    if (pb_state->log.grp_gid) return;

    res = zpn_pbroker_to_group_get_by_private_broker_gid(pb_state->log.gid,
                                                         &to_group,
                                                         1,
                                                         get_pbroker_resume,
                                                         f_conn,
                                                         fohh_connection_incarnation(f_conn));
    if (res == ZPN_RESULT_NO_ERROR) {
        pb_state->log.grp_gid = to_group->private_broker_group_gid;
        ZPN_LOG(AL_NOTICE, "%s: %ld: Retrieved pbroker group as %ld", fohh_description(f_conn), (long) pb_state->log.gid, (long) pb_state->log.grp_gid);

        /* If we have both reveived our first TCP info update as well
         * as figured out the pb group (we are here aren't we??) then
         * send pb auth log */
        if (pb_state->received_tcp_info) {
            pb_auth_log(f_conn, fohh_get_state(f_conn));
        }
    }
}

static int zpn_broker_pbroker_key_decrypt_cb(void*  argo_cookie_ptr,
                                             void*   argo_structure_cookie_ptr,
                                             struct argo_object*   object)
{
    struct zpn_file_fetch_key *key_t = object->base_structure_void;

    struct zpn_file_key *key = ZPN_CALLOC(sizeof(struct zpn_file_key));
    if (NULL == key) {
        ZPN_LOG(AL_ERROR, "Broker failed mem alloc for pvt key decryption fpr file: %s", key_t->filename);
        return ZPN_RESULT_ERR;
    }

    key->f_conn = argo_structure_cookie_ptr;
    key->fetch_retry_count = 0;
    key->is_update = key_t->is_update;
    snprintf(key->enc_or_dec_pvt_key, KEY_BUFF_SIZE, "%s", key_t->enc_or_dec_pvt_key);
    snprintf(key->enc_or_dec_pvt_key2, KEY_BUFF_SIZE, "%s", key_t->enc_or_dec_pvt_key2);
    snprintf(key->filename, FILE_SIZE, "%s", key_t->filename);
    snprintf(key->version, VERSION_LEN, "%s", key_t->version);
    if(zpn_broker_pbroker_cryptosvc_get_decrypted_key(key)){
        ZPN_LOG(AL_DEBUG, "zpn_broker_pbroker_key_decrypt_cb: decrypt failed!.");
    }


    return FOHH_RESULT_NO_ERROR;
}

static int zpn_broker_pbroker_fqdn_c2c_client_check_cb(void* argo_cookie_ptr,
                                                       void* argo_structure_cookie_ptr,
                                                       struct argo_object*   object)
{
    struct zpn_broker_dispatcher_c2c_app_check *check = object->base_structure_void;
    struct fohh_connection *f_conn = argo_structure_cookie_ptr;
    struct zpn_broker_pbroker_fohh_state *pb_state = fohh_connection_get_dynamic_cookie(f_conn);
    int res = 0;

    if (check->app_fqdn == NULL) {
        ZPN_LOG(AL_ERROR, "C2C: Received NULL fqdn");
        return ZPN_RESULT_NO_ERROR;
    }

    if (!is_c2c_ldbypass_feature_enabled(check->customer_gid)) {
        ZPN_LOG(AL_WARNING, "C2C LD Bypass 2.0 feature is disabled on broker for customer:%"PRId64".. Dropping fqdn:%s remote C2C check request",
                                                             check->customer_gid, check->app_fqdn);
        return ZPN_RESULT_NO_ERROR;
    }

    res = zpn_broker_fqdn_c2c_check_query(check->customer_gid,
                                          pb_state->tunnel_id,
                                          NULL,
                                          check->app_fqdn,
                                          strnlen(check->app_fqdn, ZPN_C2C_FQDN_LEN_MAX),
                                          &check->result,
                                          &check->expire_s,
                                          check->id,
                                          0);
    if (res == ZPN_RESULT_ASYNCHRONOUS) {
        ZPN_LOG(LOG_DEBUG, "%s: C2C dispatcher check returned async for fqdn:%s customer:%"PRId64"", pb_state->tunnel_id, check->app_fqdn, check->customer_gid);
    } else if (res == ZPN_RESULT_NO_ERROR) {
        int ret = zpn_send_dispatch_fqdn_c2c_check_struct(f_conn, fohh_connection_incarnation(f_conn), check);
        if(ret) {
            ZPN_LOG(AL_ERROR, "%s: zpn_send_dispatch_fqdn_c2c_check_struct check returned error for fqdn:%s customer:%"PRId64" error: %s", pb_state->tunnel_id, check->app_fqdn, check->customer_gid, zpn_result_string(res));
        }
    } else {
        ZPN_LOG(AL_ERROR, "%s: C2C dispatcher check returned error for fqdn:%s customer:%"PRId64" error: %s", pb_state->tunnel_id, check->app_fqdn, check->customer_gid, zpn_result_string(res));
        check->error = ZPN_ERR_C2C_BROKER_DISP_ERROR;
        int ret = zpn_send_dispatch_fqdn_c2c_check_struct(f_conn, fohh_connection_incarnation(f_conn), check);
        if(ret) {
            ZPN_LOG(AL_ERROR, "%s: zpn_send_dispatch_fqdn_c2c_check_struct check returned error for fqdn:%s customer:%"PRId64" error: %s", pb_state->tunnel_id, check->app_fqdn, check->customer_gid, zpn_result_string(res));
        }
    }

    return ZPN_RESULT_NO_ERROR;
}

static int zpn_broker_pbroker_admin_probe_status_report_cb(void*                 argo_cookie_ptr,
                                                           void*                 argo_structure_cookie_ptr,
                                                           struct argo_object*   object)
{
    const struct fohh_connection *f_conn = argo_structure_cookie_ptr;
    struct zpn_command_probe_status *report = object->base_structure_void;

    if (!report || !f_conn) {
        ZPN_LOG(AL_ERROR, "Command Probe : argo callback argument is not valid");
        return ZPN_RESULT_NO_ERROR;
    }

    if (zpn_debug_get(ZPN_DEBUG_PRIVATE_BROKER_IDX)) {
        char buf[8000];
        if (argo_object_dump(object, buf, sizeof(buf), NULL, 0) == ARGO_RESULT_NO_ERROR) {
            ZPN_LOG(AL_DEBUG, "Pbroker Command Probe : Rx: %s", buf);
        }
    }

    if (admin_probe_collection) {
        argo_log_structure_immediate(admin_probe_collection,
                                     argo_log_priority_info,
                                     0,
                                     "pbroker_status_report",
                                     zpn_command_probe_status_description,
                                     report);
    }


    ZPN_LOG(AL_NOTICE, "Command Probe %s : zpn_command_probe_status report from private broker", report->command_uuid? report->command_uuid: "");

    return zpath_customer_log_struct(report->customer_gid,
                                     zpath_customer_log_type_admin_probe,
                                     "command_probe",
                                     NULL,
                                     NULL,
                                     NULL,
                                     NULL,
                                     zpn_command_probe_status_description,
                                     report);
}

/*
 * Check if config override for broker to send PSE control connection disconnect event to
 * zpn_event topic is enabled.
 * Check level: PB -> PB Group -> Customer -> Broker -> Root Customer -> Global(1)
 * By default enabled.
 */
int zpn_broker_pbroker_is_send_pb_disconnect_event_enabled(int64_t pbroker_gid,
                                                           int64_t pbroker_group_gid,
                                                           int64_t customer_gid)
{
    int64_t config_value = 1;
    int64_t root_customer_gid = ZPATH_GID_GET_CUSTOMER_GID(zpath_instance_global_state.current_config->gid);

    config_value = zpath_config_override_get_config_int(CONFIG_SEND_PB_DISCONNECT_EVENT,
                                                        &config_value,
                                                        CONFIG_SEND_PB_DISCONNECT_EVENT_DEFAULT,
                                                        pbroker_gid,
                                                        pbroker_group_gid,
                                                        customer_gid,
                                                        zpath_instance_global_state.current_config->gid,
                                                        root_customer_gid,
                                                        (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                        0);

    return config_value ? 1 : 0;
}

static int zpn_broker_pbroker_mission_critical_conn_callback(struct fohh_connection *connection,
                                                            enum fohh_connection_state state,
                                                            void *cookie)
{
    int res = 0;
    /* As of now, this channel will be used for firedrill data fetching */
    ZPN_LOG(AL_NOTICE, "%ld: %s: Private broker mission critical connection connected", (long)fohh_peer_get_id(connection), fohh_description(connection));

    if (state == fohh_connection_connected) {

       /* Connected, register callbacks */
       struct argo_state *argo = fohh_argo_get_rx(connection);

       if ((res = argo_register_structure(argo, zpn_broker_mission_critical_description, zpn_broker_mission_critical_cb, connection))) {
           ZPN_LOG(AL_ERROR, "Could not register zpn_private_mission_critical for %s", fohh_description(connection));
           return res;
       }

    } else {
            /* disconnect and update stats, what else ? */
    }
    return FOHH_RESULT_NO_ERROR;
}

static int zpn_broker_pbroker_conn_callback(struct fohh_connection *connection,
                                            enum fohh_connection_state state,
                                            void *cookie)
{
    struct zpn_event_system_control_connection_disconnect event_data;
    struct zpn_event_broker_stats *zpn_event_broker_stats_data;
    int64_t pb_gid;
    int64_t customer_gid = 0;
    struct zpath_customer *customer = NULL;
    struct zpn_broker_pbroker_fohh_state *pb_state;
    struct zpn_private_broker *pbroker = NULL;
    int res;

    if (state == fohh_connection_connected) {
        ZPN_LOG(AL_NOTICE, "%ld: %s: Private broker control connection connected", (long)fohh_peer_get_id(connection), fohh_description(connection));

        pb_state = fohh_connection_get_dynamic_cookie(connection);
        if (pb_state) {
            ZPN_LOG(AL_ERROR, "%s: Pre-existing dynamic cookie on connected", fohh_description(connection));
            return ZPN_RESULT_ERR;
        }

        pb_gid = fohh_peer_get_id(connection);

        /* Drop connection when customer is disabled */
        customer_gid = ZPATH_GID_GET_CUSTOMER_GID(pb_gid);
        if (customer_gid != 0) {
            res = zpath_customer_get(customer_gid,
                                     &customer,
                                     NULL,
                                     NULL,
                                     0);

            if (res == ZPN_RESULT_NOT_FOUND || (res == ZPN_RESULT_NO_ERROR && customer->disable_traffic == 1)) {
                ZPN_DEBUG_PRIVATE_BROKER("%s: Could not accept connection with pbroker(%"PRId64"), customer(%"PRId64") is not found or is disabled",
                            fohh_description(connection), pb_gid, customer_gid);
                return ZPN_RESULT_ERR;
            }
        }

        pb_state = ZPN_CALLOC(sizeof(*pb_state));
        fohh_connection_set_dynamic_cookie(connection, pb_state);

        /* Generate an ID for this connection */
        res = RAND_bytes(&(pb_state->tunnel_id_bin[0]), ZPN_TUNNEL_ID_BYTES);
        if (1 != res) {
            ZPN_BROKER_ASSERT_HARD(0, "Crypto Random Generator not seeded!");
            ZPN_FREE(pb_state);
            zpn_broker_pbroker_conn_clean(connection);
            return res;
        }
        base64_encode_binary(pb_state->tunnel_id, pb_state->tunnel_id_bin, ZPN_TUNNEL_ID_BYTES);
        pb_state->log.tunnel_id = pb_state->tunnel_id;
        pb_state->log.gid = pb_gid;
        pb_state->log.g_cst = ZPATH_GID_GET_CUSTOMER_GID(pb_state->log.gid);

        /*
         * Fetch pbroker from zpn_private_broker table. Note:- this is
         * prefetched during authentication, so should be
         * available directly
         */
        res = zpn_private_broker_get_by_id(fohh_peer_get_id(connection),
                                           &pbroker,
                                           NULL,
                                           NULL,
                                           0);
        if (res) {
            ZPN_LOG(AL_WARNING, "Error fetching pbroker %ld: %s", (long) fohh_peer_get_id(connection), zpn_result_string(res));
        } else {
            pb_state->log.g_microtenant = is_scope_default(pbroker->scope_gid) ? 0 : pbroker->scope_gid;
        }

        get_pbroker_group(connection);

        /* Set up monitor timer. Note: First auth log is generated when we get tcp_info_report. */
        pb_state->timer = event_new(zevent_event_base(zevent_self()),
                                    -1,
                                    EV_PERSIST,
                                    zpn_broker_pbroker_control_conn_monitor_cb,
                                    connection);

        if (!pb_state->timer) {
            ZPN_LOG(AL_CRITICAL, "Memory");
            ZPN_FREE(pb_state);
            zpn_broker_pbroker_conn_clean(connection);
            return FOHH_RESULT_NO_MEMORY;
        }

        struct timeval tv;
        tv.tv_sec = ZPN_TUNNEL_MONITOR_INTERVAL_S;
        tv.tv_usec = 0;
        if (event_add(pb_state->timer, &tv)) {
            ZPN_LOG(AL_CRITICAL, "Could not add pbroker timer");
            event_free(pb_state->timer);
            ZPN_FREE(pb_state);
            zpn_broker_pbroker_conn_clean(connection);
            return FOHH_RESULT_NO_MEMORY;
        }

        pbctl_tunnel_fohh_store(connection, pb_state->tunnel_id, ZPN_TUNNEL_ID_BYTES_TEXT);

        /* Store pbroker gid to tunnel id mapping */
        zpn_broker_pbctl_gid_to_tunnel_store(pb_state->log.gid, pb_state->tunnel_id, strlen(pb_state->tunnel_id));
        ZPN_LOG(AL_DEBUG, "%s: Connected to Pbroker with gid: %ld with cn %s",
                fohh_description(connection), (long)pb_state->log.gid, fohh_peer_cn(connection));

        /* Connected, register callbacks */
        struct argo_state *argo = fohh_argo_get_rx(connection);

        if ((res = argo_register_structure(argo, zpn_private_broker_load_description, zpn_private_broker_load_cb, connection))) {
            ZPN_LOG(AL_ERROR, "Could not register zpn_private_broker_load for private broker for %s", fohh_description(connection));
            return res;
        }
        if ((res = argo_register_structure(argo, zpn_pbroker_status_report_description, zpn_pbroker_status_report_cb, connection))) {
            ZPN_LOG(AL_ERROR, "Could not register zpn_pbroker_status_report for private broker for %s", fohh_description(connection));
            return res;
        }
        if ((res = argo_register_structure(argo, zpn_pbroker_environment_report_description, zpn_pbroker_environment_report_cb, connection))) {
            ZPN_LOG(AL_ERROR, "Could not register zpn_pbroker_environment_report for private broker for %s", fohh_description(connection));
            return res;
        }
        if ((res = argo_register_structure(argo, zpn_tcp_info_report_description, pbroker_tcp_info_report_cb, connection))) {
            ZPN_LOG(AL_ERROR, "Could not register zpn_tcp_info_report for connection %s", fohh_description(connection));
            return res;
        }
        if ((res = argo_register_structure(argo, zpn_broker_request_description, pbroker_broker_request_cb, connection))) {
            ZPN_LOG(AL_ERROR, "Could not register zpn_broker_request for connection %s", fohh_description(connection));
            return res;
        }
        if ((res = argo_register_structure(argo, zpn_dns_dispatch_check_description, pbroker_dns_dispatch_check_cb, connection))) {
            ZPN_LOG(AL_ERROR, "Could not register zpn_dns_dispatch_check for connection %s", fohh_description(connection));
            return res;
        }
        if ((res = argo_register_structure(argo, zpn_command_probe_status_description, zpn_broker_pbroker_admin_probe_status_report_cb, connection))) {
            ZPN_LOG(AL_ERROR, "Could not register zpn_command_probe_status_description for connection %s", fohh_description(connection));
            return res;
        }
        if ((res = argo_register_structure(argo, zpn_broker_dispatcher_c2c_app_check_description, zpn_broker_pbroker_fqdn_c2c_client_check_cb, connection))) {
            ZPN_LOG(AL_ERROR, "Could not register zpn_broker_dispatcher_c2c_app_check_description for pbroker connection %s", fohh_description(connection));
            return res;
        }
        if ((res = argo_register_structure(argo, zpn_file_fetch_key_description, zpn_broker_pbroker_key_decrypt_cb, connection))) {
            ZPN_LOG(AL_ERROR, "Could not register zpn_health_report for connection %s", fohh_description(connection));
            return res;
        }
        /* Register zpn_version */
        if ((res = argo_register_structure(argo, zpn_version_description, zpn_pbroker_version_cb, connection))) {
            ZPN_LOG(AL_ERROR, "Could not register zpn_version for connection for %s", fohh_description(connection));
            return res;
        }
        zpn_fohh_worker_pbroker_connect_ctrl(fohh_connection_get_thread_id(connection));
    } else {
        const char *reason = fohh_close_reason(connection);
        ZPN_LOG(AL_NOTICE, "%ld: %s: Private broker control connection disconnected: %s", (long)fohh_peer_get_id(connection), fohh_description(connection), reason);

        if (connection->state == fohh_connection_connected) {
            zpn_fohh_worker_pbroker_disconnect_ctrl(fohh_connection_get_thread_id(connection));
        }

        pb_state = fohh_connection_get_dynamic_cookie(connection);
        if (pb_state) {
            /* Fill up event data for pbroker control connection disconnect and send it to zpn_event topic. */
            if (ZPN_BROKER_IS_PUBLIC() &&
                zpn_broker_pbroker_is_send_pb_disconnect_event_enabled(pb_state->log.gid, pb_state->log.grp_gid, pb_state->log.g_cst) &&
                !zpn_broker_pbroker_get_disconnect_pbrokers_flag() &&
                !zpn_broker_pbroker_get_redirect_pbrokers_flag() &&
                !connection->redirect_list_sent_to_client) {
                /* Don't send event if broker is disconnecting/redirecting pbrokers because broker is going
                 * to shut down or if redirect list is sent to pbroker to connect to some other broker. In
                 * this case, pbroker can connect to some other broker and customer doesn't need to get a
                 * notification for this. */
                event_data.system_id = pb_state->log.gid;
                event_data.system_group_id = pb_state->log.grp_gid;
                event_data.g_cst = pb_state->log.g_cst;
                event_data.g_microtenant = pb_state->log.g_microtenant;
                event_data.message = "Private broker control connection with broker got disconnected";
                res = zpn_event_send(pb_state->log.g_cst,
                                     zpn_event_priority_low,
                                     zpn_event_disposition_bad,
                                     zpn_event_log_name_system_control_connection_disconnected,
                                     zpn_event_system_pb,
                                     zpn_event_category_connectivity_and_upgrade,
                                     zpn_event_system_control_connection_disconnect_desc,
                                     &event_data);
                if (res) {
                    ZPN_LOG(AL_ERROR, "Could not send event data for pbroker %ld control connection disconnect",
                            (long) pb_state->log.gid);
                } else {
                    zpn_event_broker_stats_data = zpn_event_get_broker_stats_data_obj();
                    __sync_add_and_fetch_8(&(zpn_event_broker_stats_data->num_event_pb_control_connection_disconnect), 1);
                }
            }

            if (zpn_is_broker_auth_log_redirect_status_feature_enabled() && pb_state->is_redirect_sent) {
                ZPN_LOG(AL_DEBUG, "%s: Overriding close_reason to %s", fohh_description(connection), FOHH_CLOSE_REASON_BROKER_REDIRECT);
                pb_state->log.close_reason = FOHH_CLOSE_REASON_BROKER_REDIRECT;
                pb_state->is_redirect_sent = 0;
            } else {
                pb_state->log.close_reason = reason;
            }

            pb_auth_log(connection, fohh_connection_disconnected);

            fohh_connection_set_dynamic_cookie(connection, NULL);
            if (pb_state->log_version) {
                ZPN_FREE(pb_state->log_version);
                pb_state->log_version = NULL;
            }
            if (pb_state->log_sarge_version) {
                ZPN_FREE(pb_state->log_sarge_version);
                pb_state->log_sarge_version = NULL;
            }
            if (pb_state->log.platform) {
                ZPN_FREE(pb_state->log.platform);
                pb_state->log.platform = NULL;
            }
            if (pb_state->log.platform_detail) {
                ZPN_FREE(pb_state->log.platform_detail);
                pb_state->log.platform_detail = NULL;
            }
            if (pb_state->log.platform_arch) {
                ZPN_FREE(pb_state->log.platform_arch);
                pb_state->log.platform_arch = NULL;
            }
            if (pb_state->log.runtime_os) {
                ZPN_FREE(pb_state->log.runtime_os);
                pb_state->log.runtime_os = NULL;
            }

            if(pb_state->log.geoip_version) {
                ZPN_FREE(pb_state->log.geoip_version);
                pb_state->log.geoip_version = NULL;
            }

            if(pb_state->log.isp_version) {
                ZPN_FREE(pb_state->log.isp_version);
                pb_state->log.isp_version = NULL;
            }

            if (pb_state->timer) {
                event_del(pb_state->timer);
                event_free(pb_state->timer);
                pb_state->timer = NULL;
            }

            if (pb_state->log.cc) {
                ZPN_FREE(pb_state->log.cc);
                pb_state->log.cc = NULL;
            }

            if (pb_state->log.dft_rt_intf) {
                ZPN_FREE(pb_state->log.dft_rt_intf);
                pb_state->log.dft_rt_intf = NULL;
            }

            if (pb_state->log.platform_version) {
                ZPN_FREE(pb_state->log.platform_version);
                pb_state->log.platform_version = NULL;
            }

            cleanup_sys_auth_log_brokers_unique(&pb_state->log);

            cleanup_sys_auth_log_all_log_brokers_data(&pb_state->log);

            /* Remove pbroker_gid to tunnel_id mapping */
            zpn_broker_pbctl_gid_to_tunnel_remove(pb_state->log.gid);
            if ( pb_state->log.slogger_info) {
                ZPN_FREE(pb_state->log.slogger_info);
                pb_state->log.slogger_info = NULL;
            }

            pbctl_tunnel_fohh_remove(pb_state->tunnel_id, ZPN_TUNNEL_ID_BYTES_TEXT);
            zpath_wrapper_zpn_free(pb_state, __LINE__, __FILE__);
        }
    }

    return FOHH_RESULT_NO_ERROR;
}

static int zpn_broker_pbroker_create_config_conn_cookie(struct fohh_connection *f_conn,
                                                        enum pbroker_config_conn_type type)
{
    static const char *conn_type_text[] = {
        "config",
        "static_config",
        "override",
        "userdb"
    };

    struct connected_pbroker_config_fohh_state *pb_state = ZPN_CALLOC(sizeof(*pb_state));

    /* Generate an ID for this client */
    const int res = RAND_bytes(&(pb_state->tunnel_id_bin[0]), ZPN_TUNNEL_ID_BYTES);
    if (1 != res) {
        ZPN_BROKER_ASSERT_HARD(0, "Crypto Random Generator not seeded!");
        ZPN_FREE(pb_state);
        return FOHH_RESULT_ERR;
    }
    base64_encode_binary(pb_state->tunnel_id, pb_state->tunnel_id_bin, ZPN_TUNNEL_ID_BYTES);

    pb_state->timer = event_new(zevent_event_base(zevent_self()),
                                -1,
                                EV_PERSIST,
                                zpn_broker_pbroker_config_conn_monitor_cb,
                                pb_state);
    if (!pb_state->timer) {
        ZPN_LOG(AL_CRITICAL, "Memory");
        ZPN_FREE(pb_state);
        return FOHH_RESULT_NO_MEMORY;
    }

    struct timeval tv;
    tv.tv_sec = ZPN_TUNNEL_MONITOR_INTERVAL_S;
    tv.tv_usec = 0;
    if (event_add(pb_state->timer, &tv)) {
        ZPN_LOG(AL_CRITICAL, "Could not add pbroker config timer");
        event_free(pb_state->timer);
        ZPN_FREE(pb_state);
        return FOHH_RESULT_NO_MEMORY;
    }

    pb_state->type = conn_type_text[type];
    pb_state->f_conn = f_conn;

    fohh_connection_set_dynamic_cookie(f_conn, pb_state);
    return FOHH_RESULT_NO_ERROR;
}

static void zpn_broker_pbroker_destroy_config_conn_cookie(struct fohh_connection *f_conn)
{
    struct connected_pbroker_config_fohh_state *pb_state = fohh_connection_get_dynamic_cookie(f_conn);
    if (pb_state) {
        if (pb_state->timer) {
            event_free(pb_state->timer);
        }
        fohh_connection_set_dynamic_cookie(f_conn, NULL);
        ZPN_FREE(pb_state);
    }
}

static int zpn_broker_pbroker_userdb_sni_get_identifier(const char *sni, int64_t *zone, int64_t *userdb)
{
    char *walk = NULL;
    int64_t cst_gid = strtoll(sni, &walk, 10);
    if (cst_gid == 0 || *walk != '.')
        return ZPN_RESULT_BAD_ARGUMENT;

    walk++;
    int64_t my_zone = strtoll(walk, &walk, 10);
    if (!my_zone || *walk != '.')
        return ZPN_RESULT_BAD_ARGUMENT;

    walk++;
    int64_t my_userdb = strtoll(walk, &walk, 10);
    if (!my_userdb)
        return ZPN_RESULT_BAD_ARGUMENT;

    *zone = my_zone;
    *userdb = my_userdb;

    return ZPN_RESULT_NO_ERROR;
}

static int zpn_broker_pbroker_userdb_conn_callback(struct fohh_connection *connection,
                                                   enum fohh_connection_state state,
                                                   void *cookie)
{
    ZPN_DEBUG_PRIVATE_BROKER("%s: Userdb conn callback. Setting up filter tables", fohh_description(connection));

    static char *allow_tables[] = {
            "zpn_scim_user",
            "zpn_scim_user_attribute",
            "zpn_scim_user_group",
            "zpn_scim_group",
            "zpn_user_risk",
            "zpn_aae_profile_conclusion"
    };
    static int allow_tables_count = sizeof(allow_tables) / sizeof(allow_tables[0]);

    if (state == fohh_connection_connected) {
        int res;
        int64_t zone;
        int64_t userdb;
        struct zpath_customer *customer = NULL;

        if (zpn_broker_pbroker_userdb_sni_get_identifier(connection->sni_name, &zone, &userdb)) {
            ZPN_LOG(AL_ERROR, "Failed to parse zone/userdb in pbuserdb sni (sni=%s)", connection->sni_name);
            return FOHH_RESULT_ERR;
        }

        struct wally_fohh_server *userdb_wally_server = zpath_et_wally_userdb_get_userdb_wally_server(zone, userdb);
        if (!userdb_wally_server) {
            ZPN_LOG(AL_ERROR, "Failed to find requested userdb wally (sni=%s)", connection->sni_name);
            return FOHH_RESULT_ERR;
        }

        int64_t customer_gid = ZPATH_GID_MASK_CUSTOMER & fohh_peer_get_id(connection);

        /* Drop connection when customer is disabled */
        if (customer_gid != 0) {
            res = zpath_customer_get(customer_gid,
                                     &customer,
                                     NULL,
                                     NULL,
                                     0);

            if (res == ZPN_RESULT_NOT_FOUND || (res == ZPN_RESULT_NO_ERROR && customer->disable_traffic == 1)) {
                ZPN_DEBUG_PRIVATE_BROKER("%s: Could not accept connection with pbroker(%"PRId64"), customer(%"PRId64") is not found or is disabled",
                            fohh_description(connection), fohh_peer_get_id(connection), customer_gid);
                return ZPN_RESULT_ERR;
            }
        }

        struct filter_entry entries[] = { /* TABLE NAME          COL NAME           KEY              KEYSIZE  */
                {"zpn_scim_user",           "customer_gid", &customer_gid, sizeof(int64_t)},
                {"zpn_scim_user_attribute", "customer_gid", &customer_gid, sizeof(int64_t)},
                {"zpn_scim_user_group",     "customer_gid", &customer_gid, sizeof(int64_t)},
                {"zpn_scim_group",          "customer_gid", &customer_gid, sizeof(int64_t)},
                {"zpn_user_risk",           "customer_gid", &customer_gid, sizeof(int64_t)},
                {"zpn_aae_profile_conclusion",  "customer_gid", &customer_gid, sizeof(int64_t)}
        };
        struct zhash_table* table_filter = NULL;

        if (add_multiple_filter_keys(entries, sizeof(entries)/sizeof(struct filter_entry), &table_filter) != WALLY_RESULT_NO_ERROR) {
            ZPN_LOG(AL_ERROR, "Failed to add filter table entries");
            return FOHH_RESULT_ERR;
        }

        if (!wally_fohh_create_fohh_from_client(userdb_wally_server,
                                                connection,
                                                allow_tables,
                                                allow_tables_count,
                                                NULL,
                                                0,
                                                0,
                                                0,
                                                0,
                                                table_filter,
                                                NULL,
                                                zpn_broker_pbroker_userdb_conn_callback)) {
            ZPN_LOG(AL_ERROR, "Could not hand off FOHH to wally for pbroker userdb: %s", fohh_description(connection));
            return FOHH_RESULT_ERR;
        }

        res = zpn_broker_pbroker_create_config_conn_cookie(connection, pbroker_userdb);
        if (res) {
            return res;
        }

        /* Previous changes were because of ET-44125, we don't need that now, current changes are part of ET-89307 */
        zpn_fohh_worker_pbroker_connect_userdb(fohh_connection_get_thread_id(connection));

        ZPN_LOG(AL_NOTICE, "%s: Private broker userdb connection UP", fohh_description(connection));

    } else {
        /* Connection probably went away... */
        if (connection->state == fohh_connection_connected) {
            /* We only count report connection going down when previous state is CONNECTED */
            ZPN_LOG(AL_NOTICE, "%s: Private broker userdb connection DOWN", fohh_description(connection));

            zpn_broker_pbroker_destroy_config_conn_cookie(connection);
            zpn_fohh_worker_pbroker_disconnect_userdb(fohh_connection_get_thread_id(connection));
        }
        return FOHH_RESULT_ERR;
    }

    return FOHH_RESULT_NO_ERROR;
}

static int zpn_broker_pbroker_config_override_conn_callback(struct fohh_connection *connection,
                                                            enum fohh_connection_state state,
                                                            void *cookie)
{
    ZPN_LOG(AL_DEBUG, "%s: Config override conn callback. Setting up filter tables", fohh_description(connection));

    static char *filter_tables[] = {
            "zpath_config_override",
            "et_customer_userdb",
            "et_customer_zone"
    };
    static int filter_tables_count = sizeof(filter_tables) / sizeof(filter_tables[0]);

    static char *allow_tables[] = {
            "et_userdb",
            "et_translate",
            "et_translate_code",
            "zpath_cloud"
    };
    static int allow_tables_count = sizeof(allow_tables) / sizeof(allow_tables[0]);

    static struct wally_column_nullify_config nullify_cfg[] = {
            {"et_userdb", "name"},
            {"et_userdb", "description"},
            {"et_userdb", "userdb_url"},
            {"et_userdb", "userdb_username"},
            {"et_userdb", "userdb_password"},
            {"zpath_cloud", "ip_anchor_clouds"},
            {"zpath_cloud", "secret"},
            {"zpath_cloud", "file_key"},
            {"zpath_cloud", "file_key2"},
    };
    static int nullify_cfg_count = sizeof(nullify_cfg) / sizeof(nullify_cfg[0]);

    if (state == fohh_connection_connected) {
        int res;
        struct zpath_customer *customer = NULL;
        /* Allocate state for this connection, so we can track its stuff... */
        int64_t gid = fohh_peer_get_id(connection);
        int64_t customer_gid = ZPATH_GID_MASK_CUSTOMER & gid;
        int64_t global_gid = ZPATH_GLOBAL_CONFIG_OVERRIDE_GID;

        /* Drop connection when customer is disabled */
        if (customer_gid != 0) {
            res = zpath_customer_get(customer_gid,
                                     &customer,
                                     NULL,
                                     NULL,
                                     0);

            if (res == ZPN_RESULT_NOT_FOUND || (res == ZPN_RESULT_NO_ERROR && customer->disable_traffic == 1)) {
                ZPN_DEBUG_PRIVATE_BROKER("%s: Could not accept connection with pbroker(%"PRId64"), customer(%"PRId64") is not found or is disabled",
                            fohh_description(connection), gid, customer_gid);
                return FOHH_RESULT_ERR;
            }
        }

        struct filter_entry entries[] = { /* TABLE NAME         COL NAME           KEY              KEYSIZE  */
                {"et_customer_userdb",    "customer_gid", &customer_gid, sizeof(int64_t)},
                {"et_customer_zone",      "customer_gid", &customer_gid, sizeof(int64_t)},
                {"zpath_config_override", "customer_gid", &customer_gid, sizeof(int64_t)},
                {"zpath_config_override", "customer_gid", &global_gid,   sizeof(int64_t)}
        };
        struct zhash_table* table_filter = NULL;

        if (add_multiple_filter_keys(entries, sizeof(entries)/sizeof(struct filter_entry), &table_filter) != WALLY_RESULT_NO_ERROR) {
            ZPN_LOG(AL_ERROR, "Failed to add filter table entries");
            return FOHH_RESULT_ERR;
        }

        struct zhash_table *nullify_state = wally_column_nullify_build_state(nullify_cfg, nullify_cfg_count);
        if (!nullify_state) {
            ZPN_LOG(AL_ERROR, "Could not initialize wally column nullify state");
            deallocate_table_filter(&table_filter);
            return FOHH_RESULT_ERR;
        }

        if (!wally_fohh_create_fohh_from_client(wally_server_global,
                                                connection,
                                                allow_tables,
                                                allow_tables_count,
                                                filter_tables,
                                                filter_tables_count,
                                                ZPATH_GID_MASK_CUSTOMER,
                                                ZPATH_GID_MASK_CUSTOMER & gid,
                                                global_gid,
                                                table_filter,
                                                nullify_state,
                                                zpn_broker_pbroker_config_override_conn_callback)) {
            ZPN_LOG(AL_ERROR, "Could not hand off FOHH to wally for pbroker override: %s", fohh_description(connection));
            return FOHH_RESULT_ERR;
        }

        res = zpn_broker_pbroker_create_config_conn_cookie(connection, pbroker_override);
        if (res) {
            return res;
        }

        /* Previous changes were because of ET-44125, we don't need that now, current changes are part of ET-89307 */
        zpn_fohh_worker_pbroker_connect_override(fohh_connection_get_thread_id(connection));

        ZPN_LOG(AL_NOTICE, "%s: Private broker override connection UP", fohh_description(connection));

    } else {
        /* Connection probably went away... */
        if (connection->state == fohh_connection_connected) {
            /* We only count report connection going down when previous state is CONNECTED */
            ZPN_LOG(AL_NOTICE, "%s: Private broker override connection DOWN", fohh_description(connection));

            zpn_broker_pbroker_destroy_config_conn_cookie(connection);
            zpn_fohh_worker_pbroker_disconnect_override(fohh_connection_get_thread_id(connection));
        }
        return FOHH_RESULT_ERR;
    }

    return FOHH_RESULT_NO_ERROR;
}

static int zpn_broker_pbroker_config_conn_callback(struct fohh_connection *connection,
                                                   enum fohh_connection_state state,
                                                   void *cookie)
{
    ZPN_LOG(AL_DEBUG, "%s: Config conn callback. Setting up filter tables", fohh_description(connection));
    static char *allow_tables[] =
        {
         "zpn_rule_condition_operand",
         "zpn_rule_condition_set",
         "zpn_rule_to_server_group",
         "zpn_rule_to_assistant_group",
         "zpn_client",
         "zpn_issuedcert",
         "zpn_private_broker_group",
         "zpn_private_broker_to_group",
         "zpn_machine",
         "zpn_machine_group",
         "zpn_machine_to_group",
         "zpn_inspection_application",
         "zpn_approval",
         "zpn_approval_mapping",
         "zpn_location",
         "zpn_location_group_to_location"
        };
    static int allow_tables_count = sizeof(allow_tables) / sizeof(allow_tables[0]);

    /**
     * ATTENTION:
     *    When adding new tables to `filter_tables[]`, it means the tables are
     *    required by PSE. In DDIL offline mode, PSE will talk with PCC (Site Controller)
     *    instead of broker, so probably you need add these new tables to
     *    `filter_tables[]` of `zpn_sitec_pse_conn.c` as well.
     */
    static char *filter_tables[] =
        {
         "zpath_customer",
         "zpn_app_group_relation",
         "zpn_app_server",
         "zpn_application",
         "zpn_application_domain",
         "zpn_application_group",
         "zpn_application_group_application_mapping",
         "zpn_assistant",
         "zpn_assistant_group",
         "zpn_assistant_version",
         "zpn_assistantgroup_assistant_relation",
         "zpn_customer_config",
         "zpn_c2c_client_registration",
         "zpn_c2c_ip_ranges",
         "zpn_idp",
         "zpn_idp_cert",
         "zpn_inspection_application",
         "zpn_approval",
         "zpn_approval_mapping",
         "zpn_policy_set",
         "zpn_posture_profile_db",
         "zpn_private_broker",
         "zpn_private_broker_load",
         "zpn_private_broker_version",
         "zpn_step_up_auth_level",
         "zpn_rule_to_step_up_auth_level_mapping",
         "zpn_sub_module_upgrade",
         "zpn_customer_resiliency_settings",
         "zpn_privatebrokergroup_trustednetwork_mapping",
         "zpn_rule",
         "zpn_rule_to_location",
         "zpn_rule_to_location_group",
         "zpn_saml_attrs",
         "zpn_scim_attr_header",
         "zpn_server_group",
         "zpn_server_group_assistant_group",
         "zpn_servergroup_server_relation",
         "zpn_shared_customer_domain",
         "zpn_signing_cert",
         "zpn_trusted_network",
         "zpn_znf",
         "zpn_znf_to_group",
         "zpn_znf_group",
         "zpn_command_probe",
         "zpn_scope",
         "zpn_branch_connector",
         "zpn_branch_connector_to_group",
         "zpn_branch_connector_group",
         "zpn_svcp_profile",
         "zpn_ddil_config",
         "zpn_site",
         "zpn_site_controller",
         "zpn_site_controller_group",
         "zpn_site_controller_to_group",
         "zpn_site_controller_version",
         "zpn_workload_tag_group",
         "zpn_firedrill_site"
        };
    static int filter_tables_count = sizeof(filter_tables) / sizeof(filter_tables[0]);

    if (state == fohh_connection_connected) {
        int res;
        struct zpath_customer *customer = NULL;
        /* Allocate state for this connection, so we can track its stuff... */
        int64_t gid = fohh_peer_get_id(connection);
        int64_t customer_gid = ZPATH_GID_MASK_CUSTOMER & gid;
        int shard = ZPATH_SHARD_FROM_GID(gid);

        struct argo_state *argo = fohh_argo_get_rx(connection);

        /* Register zpn_version */
        if ((res = argo_register_structure(argo, zpn_version_description, zpn_pbroker_version_cb, connection))) {
            ZPN_LOG(AL_ERROR, "Could not register zpn_version for connection for %s", fohh_description(connection));
            return res;
        }

        if (!gid || !shard || (shard > ZPATH_CLOUD_SHARD_COUNT)) {
            ZPN_LOG(AL_ERROR, "Broker received pbroker config connection: %s, pbroker = %ld, shard = %d (Out of range)",
                    fohh_description(connection),
                    (long) gid,
                    shard);
            return FOHH_RESULT_ERR;
        }

        /* Drop connection when customer is disabled */
        if (customer_gid != 0) {
            res = zpath_customer_get(customer_gid,
                                     &customer,
                                     NULL,
                                     NULL,
                                     0);

            if (res == ZPN_RESULT_NOT_FOUND || (res == ZPN_RESULT_NO_ERROR && customer->disable_traffic == 1)) {
                ZPN_DEBUG_PRIVATE_BROKER("%s: Could not accept connection with pbroker(%"PRId64"), customer(%"PRId64") is not found or is disabled",
                            fohh_description(connection), gid, customer_gid);
                return FOHH_RESULT_ERR;
            }
        }

        struct filter_entry entries[] = { /* TABLE NAME                     COL NAME        KEY             KEYSIZE  */
                                          {"zpn_rule_condition_operand",    "customer_gid", &customer_gid, sizeof(int64_t)},
                                          {"zpn_rule_condition_set",        "customer_gid", &customer_gid, sizeof(int64_t)},
                                          {"zpn_client",                    "customer_gid", &customer_gid, sizeof(int64_t)},
                                          {"zpn_issuedcert",                "customer_gid", &customer_gid, sizeof(int64_t)},
                                          {"zpn_private_broker_to_group",   "private_broker_gid", &gid, sizeof(int64_t)},
                                          {"zpn_private_broker_to_group",   "customer_gid",  &customer_gid, sizeof(int64_t)},
                                          {"zpn_private_broker_group",      "customer_gid",  &customer_gid, sizeof(int64_t)},
                                          {"zpn_inspection_application",    "customer_gid",  &customer_gid, sizeof(int64_t)},
                                          {"zpn_approval",                  "customer_gid",  &customer_gid, sizeof(int64_t)},
                                          {"zpn_approval_mapping",          "customer_gid",  &customer_gid, sizeof(int64_t)},
                                          {"zpn_location",                  "customer_gid",  &customer_gid, sizeof(int64_t)}
                                        };
        struct zhash_table* table_filter = NULL;

        if (add_multiple_filter_keys(entries, sizeof(entries)/sizeof(struct filter_entry), &table_filter) != WALLY_RESULT_NO_ERROR) {
            ZPN_LOG(AL_ERROR, "Failed to add filter table entries");
            return FOHH_RESULT_ERR;
        }

        if (!wally_fohh_create_fohh_from_client(wally_servers[shard],
                                                connection,
                                                allow_tables,
                                                allow_tables_count,
                                                filter_tables,
                                                filter_tables_count,
                                                ZPATH_GID_MASK_CUSTOMER,
                                                ZPATH_GID_MASK_CUSTOMER & gid,
                                                0,
                                                table_filter,
                                                NULL,
                                                zpn_broker_pbroker_config_conn_callback)) {
            ZPN_LOG(AL_ERROR, "Could not hand off FOHH to wally for pbroker config: %s", fohh_description(connection));
            return FOHH_RESULT_ERR;
        }


        res = zpn_broker_pbroker_create_config_conn_cookie(connection, pbroker_config);
        if (res) {
            ZPN_LOG(AL_ERROR, "Failed to create config connection cookie");
            return res;
        }

        /* Previous changes were because of ET-44125, we don't need that now, current changes are part of ET-89307 */
        zpn_fohh_worker_pbroker_connect_config(fohh_connection_get_thread_id(connection));
        ZPN_LOG(AL_NOTICE, "%s: Private broker config connection UP", fohh_description(connection));

    } else {
        /* Connection probably went away... */
        if (connection->state == fohh_connection_connected) {
            /* We only count report connection going down when previous state is CONNECTED */
            ZPN_LOG(AL_NOTICE, "%s: Private broker config connection DOWN", fohh_description(connection));

            zpn_broker_pbroker_destroy_config_conn_cookie(connection);
            zpn_fohh_worker_pbroker_disconnect_config(fohh_connection_get_thread_id(connection));
        }
        return FOHH_RESULT_ERR;
    }

    return FOHH_RESULT_NO_ERROR;
}

static int zpn_broker_pbroker_static_config_conn_callback(struct fohh_connection *connection,
                                                          enum fohh_connection_state state,
                                                           void *cookie)
{
    ZPN_LOG(AL_DEBUG, "%s: Static Config conn callback. Setting up filter tables", fohh_description(connection));

    static char *allow_static_tables[] =
        {
         "zpn_client",
        };
    static int allow_static_tables_count = sizeof(allow_static_tables) / sizeof(allow_static_tables[0]);

    static char **filter_static_tables = NULL;
    static int filter_static_tables_count = 0;

    if (state == fohh_connection_connected) {
        int res;
        struct zpath_customer *customer = NULL;
        /* Allocate state for this connection, so we can track its stuff... */
        int64_t gid = fohh_peer_get_id(connection);
        int64_t customer_gid = ZPATH_GID_MASK_CUSTOMER & gid;
        int shard = ZPATH_SHARD_FROM_GID(gid);

        if (!gid || !shard || (shard > ZPATH_CLOUD_SHARD_COUNT)) {
            ZPN_LOG(AL_ERROR, "Broker received pbroker static config connection: %s, pbroker = %ld, shard = %d (Out of range)",
                    fohh_description(connection),
                    (long) gid,
                    shard);
            return FOHH_RESULT_ERR;
        }

        /* Drop connection when customer is disabled */
        if (customer_gid != 0) {
            res = zpath_customer_get(customer_gid,
                                     &customer,
                                     NULL,
                                     NULL,
                                     0);

            if (res == ZPN_RESULT_NOT_FOUND || (res == ZPN_RESULT_NO_ERROR && customer->disable_traffic == 1)) {
                ZPN_DEBUG_PRIVATE_BROKER("%s: Could not accept connection with pbroker(%"PRId64"), customer(%"PRId64") is not found or is disabled",
                            fohh_description(connection), gid, customer_gid);
                return FOHH_RESULT_ERR;
            }
        }

        struct filter_entry static_entries[] = { /* TABLE NAME                     COL NAME        KEY             KEYSIZE  */
                                                  {"zpn_client",                    "customer_gid", &customer_gid, sizeof(int64_t)}
                                                 };
        struct zhash_table* static_table_filter = NULL;

        if (add_multiple_filter_keys(static_entries, sizeof(static_entries)/sizeof(struct filter_entry), &static_table_filter) != WALLY_RESULT_NO_ERROR) {
            ZPN_LOG(AL_ERROR, "Failed to add filter static table entries");
            return FOHH_RESULT_ERR;
        }

        if (!wally_fohh_create_fohh_from_client(wally_servers_static[shard],
                                                connection,
                                                allow_static_tables,
                                                allow_static_tables_count,
                                                filter_static_tables,
                                                filter_static_tables_count,
                                                ZPATH_GID_MASK_CUSTOMER,
                                                ZPATH_GID_MASK_CUSTOMER & gid,
                                                0,
                                                static_table_filter,
                                                NULL,
                                                zpn_broker_pbroker_static_config_conn_callback)) {
            ZPN_LOG(AL_ERROR, "Could not hand off FOHH to wally for pbroker static tables config: %s", fohh_description(connection));
            return FOHH_RESULT_ERR;
        }

        res = zpn_broker_pbroker_create_config_conn_cookie(connection, pbroker_static_config);
        if (res) {
            ZPN_LOG(AL_ERROR, "Failed to create static config connection cokkie");
            return res;
        }

        /* Previous changes were because of ET-44125, we don't need that now, current changes are part of ET-89307 */
        zpn_fohh_worker_pbroker_connect_static_config(fohh_connection_get_thread_id(connection));
        ZPN_LOG(AL_NOTICE, "%s: Private broker static config connection UP", fohh_description(connection));

    } else {
        /* Connection probably went away... */
        if (connection->state == fohh_connection_connected) {
            /* We only count report connection going down when previous state is CONNECTED */
            ZPN_LOG(AL_NOTICE, "%s: Private broker static config connection DOWN", fohh_description(connection));

            zpn_broker_pbroker_destroy_config_conn_cookie(connection);
            zpn_fohh_worker_pbroker_disconnect_static_config(fohh_connection_get_thread_id(connection));
        }
        return FOHH_RESULT_ERR;
    }

    return FOHH_RESULT_NO_ERROR;
}



static int zpn_broker_pbroker_unblock_callback(struct fohh_connection *connection,
                                               enum fohh_queue_element_type element_type,
                                               void *cookie)
{
    /*TODO: Implement this function. */
    ZPN_LOG(AL_CRITICAL, "Implement me");
    return FOHH_RESULT_NO_ERROR;
}


static int zpn_broker_pbroker_config_unblock_callback(struct fohh_connection *connection,
                                                        enum fohh_queue_element_type element_type,
                                                        void *cookie)
{
    ZPN_LOG(AL_CRITICAL, "Implement me");
    return FOHH_RESULT_NO_ERROR;
}

static int zpn_broker_pbroker_static_config_unblock_callback(struct fohh_connection *connection,
                                                             enum fohh_queue_element_type element_type,
                                                             void *cookie)
{
    ZPN_LOG(AL_CRITICAL, "Implement me");
    return FOHH_RESULT_NO_ERROR;
}

static int zpn_broker_pbroker_config_override_unblock_callback(struct fohh_connection *connection,
                                                               enum fohh_queue_element_type element_type,
                                                               void *cookie)
{
    ZPN_LOG(AL_CRITICAL, "Implement me");
    return FOHH_RESULT_NO_ERROR;
}

/*
 * Callback that occurs after all the certificates have been fetched
 * from storage.
 *
 * We just queue up reprocessing of the connection, which will trigger
 * creation of the cert context again.
 */
int zpn_broker_pbroker_ctx_callback_callback(void *response_callback_cookie,
                                             struct wally_registrant *registrant,
                                             struct wally_table *table,
                                             int64_t request_id,
                                             int row_count)
{
    struct fohh_connection *f_conn = response_callback_cookie;
    if (fohh_connection_incarnation(f_conn) != request_id) {
        __sync_fetch_and_add_8(&(zpn_fohh_workers[f_conn->fohh_thread_id].pbroker_stats.num_pbroker_fohh_incarnation_mismatch), 1);
        return WALLY_RESULT_NO_ERROR;
    }

    fohh_connection_process(f_conn, request_id);
    return WALLY_RESULT_NO_ERROR;
}


int zpn_broker_pbroker_verify_callback(struct fohh_connection *f_conn)
{
    int64_t pbroker_id;
    int res=0;
    int64_t customer_id;
    X509 *cert;
    struct zpn_private_broker *pbroker;
    int ip_acl_iter;
    struct argo_inet pbroker_addr;
    int ip_acl_validation_failed;

    /* Get customer_id reliably */
    res = zpn_broker_client_peer_get_set_customer_id_fohh(f_conn);
    if (res) {
        /* the called function logs plenty */
        return res;
    }
    /* Guaranteed to have customer_id if we got here */
    customer_id = ZPATH_GID_GET_CUSTOMER_GID(fohh_peer_get_id(f_conn));

    char *cn = fohh_peer_cn(f_conn);
    if (!cn) {
        ZPN_LOG(AL_WARNING, "No peer CN");
        return ZPN_RESULT_ERR;
    }

    cert = fohh_peer_cert(f_conn);

    if (!cert) {
        ZPN_LOG(AL_WARNING, "No peer cert??");
        return ZPN_RESULT_ERR;
    }

    res = zpn_issuedcert_verify(customer_id,
                                cert,
                                cn,
                                "BROKER",
                                &pbroker_id,
                                zpn_broker_pbroker_ctx_callback_callback,
                                f_conn,
                                fohh_connection_incarnation(f_conn));
    X509_free(cert);

    if (res) {
        if (res == ZPN_RESULT_ASYNCHRONOUS) {
            ZPN_DEBUG_PRIVATE_BROKER("Asynchronous verification request for CN = %s", cn);
        } else {
            ZPN_LOG(AL_WARNING, "Error verifying request for CN = %s, = %s", cn, zpn_result_string(res));
        }
        return res;

    } else {
        ZPN_DEBUG_PRIVATE_BROKER("Verified cert for CN = %s", cn);
    }

    fohh_peer_set_id(f_conn, pbroker_id);

    res = zpn_private_broker_get_by_id(pbroker_id,
                                       &pbroker,
                                       zpn_broker_pbroker_ctx_callback_callback,
                                       f_conn,
                                       fohh_connection_incarnation(f_conn));
    if (res) {
        if (res == ZPN_RESULT_ASYNCHRONOUS) {
            ZPN_DEBUG_PRIVATE_BROKER("Asynchronous pbroker fetch for gid = %ld", (long) pbroker_id);
        } else {
            ZPN_LOG(AL_WARNING, "Error fetching pbroker %ld: %s", (long) pbroker_id, zpn_result_string(res));
        }
        return res;
    } else {
        ZPN_DEBUG_PRIVATE_BROKER("%s: Pvt broker  cert verified, pvt broker %ld fetched.", cn, (long) pbroker_id);
    }

    res=zpn_broker_pbroker_validate_version(pbroker_id,
                                            zpn_broker_pbroker_ctx_callback_callback,
                                            f_conn,
                                            fohh_connection_incarnation(f_conn));

    if (res) {
        if (res != ZPN_RESULT_ASYNCHRONOUS) {
            ZPN_LOG(AL_ERROR, "Could not validate version for private broker : %s", zpn_result_string(res));
        }
        return res;
    }
    /*
     * Verify ACL. Since ACL is configured on the pbroker object itself, the first time when a pbroke comes up,
     * we will not have this entry. Only after that user will be allowed to configure this.
     */
    fohh_connection_address(f_conn, &pbroker_addr, NULL);
    if (0 == pbroker->ip_acl_count) {
        ip_acl_validation_failed = 0;
    } else {
        ip_acl_validation_failed = 1;
    }
    ip_acl_iter = 0;
    while (ip_acl_iter != pbroker->ip_acl_count) {
        if (argo_inet_is_contained(&pbroker->ip_acl[ip_acl_iter], &pbroker_addr)) {
            ip_acl_validation_failed = 0;
            break;
        }
        ip_acl_iter++;
    }

    if (ip_acl_validation_failed) {
        char pbroker_addr_str[ARGO_INET_ADDRSTRLEN];
        ZPN_LOG(AL_ERROR, "Pvt broker's IP(%s) didn't match with ACL - terminating the pvt broker",
                argo_inet_generate(pbroker_addr_str, &pbroker_addr));
        return ZPN_RESULT_ERR;
    }

    struct zpn_private_broker_to_group *group_mappings;
    res = zpn_pbroker_to_group_get_by_private_broker_gid(pbroker_id,
                                                         &group_mappings,
                                                         1,
                                                         zpn_broker_pbroker_ctx_callback_callback,
                                                         f_conn,
                                                         fohh_connection_incarnation(f_conn));
    if (res) {
        if (res != ZPN_RESULT_ASYNCHRONOUS) {
            ZPN_LOG(AL_ERROR, "Could not retrieve private broker group mapping info : %s", zpn_result_string(res));
        }
        return res;
    }

    struct zpn_private_broker_group *group;
    res = zpn_pbroker_group_get_by_gid(group_mappings->private_broker_group_gid,
                                       &group,
                                       1,
                                       zpn_broker_pbroker_ctx_callback_callback,
                                       f_conn,
                                       fohh_connection_incarnation(f_conn));
    if (res) {
        if (res != ZPN_RESULT_ASYNCHRONOUS) {
            ZPN_LOG(AL_ERROR, "Could not retrieve  private broker group info : %s", zpn_result_string(res));
        }
        return res;
    }

    return ZPN_RESULT_NO_ERROR;
}

static int customer_log_collection_callback(struct customer_log_collection_info* cust_log_collection_info, void* data)
{
    if (!cust_log_collection_info) {
        return ZPN_RESULT_ERR;
    }

    int res;
    res = zpath_customer_log_struct(ZPATH_GID_GET_CUSTOMER_GID(cust_log_collection_info->peer_gid),
                                    cust_log_collection_info->customer_log_type,
                                    cust_log_collection_info->log_type,
                                    NULL,
                                    NULL,
                                    NULL,
                                    NULL,
                                    cust_log_collection_info->log_description,
                                    data);
    if (res) {
        ZPN_LOG(AL_WARNING, "Failed to log %s into infra: %s", cust_log_collection_info->log_type, zpn_result_string(res));
        return res;
    }

    return ZPN_RESULT_NO_ERROR;
}

static void zpn_broker_pbroker_log_connection_monitor_timer_cb(evutil_socket_t sock, short flags, void *cookie)
{
    struct fohh_connection *connection = cookie;

    /* Drop connection when broker is shutting down. */
    if (disconnect_pbrokers) {
        const char *reason = disconnect_pbrokers_reason ? : FOHH_CLOSE_REASON_UPGRADE;
        ZPN_LOG(AL_INFO, "%s: Disconnect pbroker log connection due to %s",
                fohh_description(connection), reason);
        fohh_connection_delete(connection, reason);
        return;
    }

    if (zpn_broker_pbroker_get_redirect_pbrokers_flag()) {
        const char *redirect_reason = zpn_broker_pbroker_get_redirect_pbrokers_reason();
        ZPN_LOG(AL_INFO, "%s: Redirect pbroker log connection due to %s",
                fohh_description(connection),
                redirect_reason ? redirect_reason : FOHH_CLOSE_REASON_UPGRADE);
        zpn_broker_pbroker_log_conn_redirect(connection, log_rx_get_app_info(connection));
    }
}



static void zpn_broker_pbroker_log_connection_periodic_logging_timer_cb(evutil_socket_t sock, short flags, void *cookie)
{
    struct fohh_connection *connection = cookie;
    struct zpn_sys_auth_log log;
    struct zpn_private_broker *pbroker = NULL;

    memset(&log, 0, sizeof(log));

    log.gid = fohh_peer_get_id(connection);
    log.tunnel_type = ZPN_TUNNEL_LOG;

    struct zpn_private_broker_to_group *to_group;

    int res;
    res = zpn_pbroker_to_group_get_by_private_broker_gid(log.gid,
                                                         &to_group,
                                                         1,
                                                         get_pbroker_resume,
                                                         connection,
                                                         fohh_connection_incarnation(connection));
    if (res == ZPN_RESULT_NO_ERROR) {
        log.grp_gid = to_group->private_broker_group_gid;
    } else {
        ZPN_LOG(AL_NOTICE, "%s: %ld: Could not retrieve pbroker group", fohh_description(connection), (long)log.gid);
    }

    res = zpn_private_broker_get_by_id(fohh_peer_get_id(connection),
                                        &pbroker,
                                        NULL,
                                        NULL,
                                        0);
    if (res) {
        ZPN_LOG(AL_WARNING, "Error fetching pbroker %ld: %s", (long) fohh_peer_get_id(connection), zpn_result_string(res));
    } else {
        log.g_microtenant = is_scope_default(pbroker->scope_gid) ? 0 : pbroker->scope_gid;
    }
    zpn_broker_pbroker_auth_log(connection,
                                &log,
                                fohh_get_state(connection));

    if(log.cc){
        ZPN_FREE(log.cc);
        log.cc = NULL;
    }
    if(log.slogger_info){
        ZPN_FREE(log.slogger_info);
        log.slogger_info = NULL;
    }
}


static int
check_customer_gids(int64_t customer_gid, int64_t* siem_gids, size_t siem_gid_count)
{
    size_t ii = 0;
    for (ii = 0; ii < siem_gid_count; ii++) {
        if (ZPATH_GID_GET_CUSTOMER_GID(siem_gids[ii]) != customer_gid) {
            ZPN_LOG(AL_ERROR, "Customer gid check failed for siem log transmit: %ld, siem gid = %ld", (long)customer_gid, (long)siem_gids[ii]);
            return ZPN_RESULT_ERR;
        }
    }
    return ZPN_RESULT_NO_ERROR;
}
int zpn_trans_log_siem_data_fill_callback(struct customer_log_collection_info* cust_log_collection_info, void* data, int free)
{
    if (!cust_log_collection_info) {
        return ZPN_RESULT_ERR;
    }
    struct argo_object* obj = (struct argo_object*) data;
    struct zpn_trans_log* trans_log = (struct zpn_trans_log*) obj->base_structure_void;
    if ( free ) {
        ZPN_FREE(trans_log->slogger_info);
        trans_log->slogger_info = NULL;
        return ZPN_RESULT_NO_ERROR;
    }
    int res;
    res = check_customer_gids(ZPATH_GID_GET_CUSTOMER_GID(cust_log_collection_info->peer_gid), trans_log->gids, trans_log->gids_count);
    if (res == ZPN_RESULT_ERR) {
        return res;
    }
    size_t slogger_info_len = ( ((DIGITS_IN_INT64_MAX + 1 )+ (ZPN_SLOGGER_NAME_LEN + 1) ) * trans_log->gids_count) + 1;
    trans_log->slogger_info = ZPN_CALLOC(slogger_info_len);
    zpn_siem_get_slogger_info(trans_log->slogger_info,slogger_info_len,trans_log->gids, trans_log->gids_count,"zpn_trans_log");
    return ZPN_RESULT_NO_ERROR;
}
int zpn_trans_log_siem_callback(struct customer_log_collection_info* cust_log_collection_info, void* data)
{
    if (!cust_log_collection_info) {
        return ZPN_RESULT_ERR;
    }

    struct argo_object* obj = (struct argo_object*) data;
    struct zpn_trans_log* trans_log = (struct zpn_trans_log*) obj->base_structure_void;

    int res;
    res = check_customer_gids(ZPATH_GID_GET_CUSTOMER_GID(cust_log_collection_info->peer_gid), trans_log->gids, trans_log->gids_count);
    if (res == ZPN_RESULT_ERR) {
        return res;
    }
    res = zpn_siem_log_transmit(obj, trans_log->gids, trans_log->gids_count, cust_log_collection_info->check_for_site);
    if (res) {
        ZPN_LOG(AL_WARNING, "Failed to log %s into siem infra: %s", cust_log_collection_info->log_type, zpn_result_string(res));
        return res;
    }

    return ZPN_RESULT_NO_ERROR;
}

int zpn_auth_log_siem_data_fill_callback(struct customer_log_collection_info* cust_log_collection_info, void* data, int free)
{
    if (!cust_log_collection_info) {
        return ZPN_RESULT_ERR;
    }

    struct argo_object* obj = (struct argo_object*) data;
    struct zpn_auth_log* auth_log = (struct zpn_auth_log*) obj->base_structure_void;

    if ( free ) {
        ZPN_FREE(auth_log->slogger_info);
        auth_log->slogger_info = NULL;
        return ZPN_RESULT_NO_ERROR;
    }
    int res;
    res = check_customer_gids(ZPATH_GID_GET_CUSTOMER_GID(cust_log_collection_info->peer_gid), auth_log->gids, auth_log->gids_count);
    if (res == ZPN_RESULT_ERR) {
        return res;
    }
    size_t slogger_info_len = ( ((DIGITS_IN_INT64_MAX + 1 )+ (ZPN_SLOGGER_NAME_LEN + 1) ) * auth_log->gids_count) + 1;
    auth_log->slogger_info = ZPN_CALLOC(slogger_info_len);
    zpn_siem_get_slogger_info(auth_log->slogger_info,slogger_info_len,auth_log->gids, auth_log->gids_count,"zpn_auth_log");
    return ZPN_RESULT_NO_ERROR;

}
int zpn_auth_log_siem_callback(struct customer_log_collection_info* cust_log_collection_info, void* data)
{
    if (!cust_log_collection_info) {
        return ZPN_RESULT_ERR;
    }

    struct argo_object* obj = (struct argo_object*) data;
    struct zpn_auth_log* auth_log = (struct zpn_auth_log*) obj->base_structure_void;

    int res;
    res = check_customer_gids(ZPATH_GID_GET_CUSTOMER_GID(cust_log_collection_info->peer_gid), auth_log->gids, auth_log->gids_count);
    if (res == ZPN_RESULT_ERR) {
        return res;
    }
    res = zpn_siem_log_transmit(obj, auth_log->gids, auth_log->gids_count, cust_log_collection_info->check_for_site);
    if (res) {
        ZPN_LOG(AL_WARNING, "Failed to log %s into siem infra: %s", cust_log_collection_info->log_type, zpn_result_string(res));
        return res;
    }

    return ZPN_RESULT_NO_ERROR;
}
int zpn_ast_auth_log_siem_data_fill_callback(struct customer_log_collection_info* cust_log_collection_info, void* data, int free)
{
    if (!cust_log_collection_info) {
        return ZPN_RESULT_ERR;
    }

    struct argo_object* obj = (struct argo_object*) data;
    struct zpn_ast_auth_log* ast_auth_log = (struct zpn_ast_auth_log*) obj->base_structure_void;
    if ( free ) {
        ZPN_FREE(ast_auth_log->slogger_info);
        ast_auth_log->slogger_info = NULL;
        return ZPN_RESULT_NO_ERROR;
    }

    int res;
    res = check_customer_gids(ZPATH_GID_GET_CUSTOMER_GID(cust_log_collection_info->peer_gid), ast_auth_log->gids, ast_auth_log->gids_count);
    if (res == ZPN_RESULT_ERR) {
        return res;
    }
    size_t slogger_info_len = ( ((DIGITS_IN_INT64_MAX + 1 )+ (ZPN_SLOGGER_NAME_LEN + 1) ) * ast_auth_log->gids_count) + 1;
    ast_auth_log->slogger_info = ZPN_CALLOC(slogger_info_len);
    zpn_siem_get_slogger_info(ast_auth_log->slogger_info,slogger_info_len,ast_auth_log->gids,ast_auth_log->gids_count,"zpn_ast_auth_log");
    return ZPN_RESULT_NO_ERROR;

}

int zpn_ast_auth_log_siem_callback(struct customer_log_collection_info* cust_log_collection_info, void* data)
{
    if (!cust_log_collection_info) {
        return ZPN_RESULT_ERR;
    }

    struct argo_object* obj = (struct argo_object*) data;
    struct zpn_ast_auth_log* ast_auth_log = (struct zpn_ast_auth_log*) obj->base_structure_void;

    int res;
    res = check_customer_gids(ZPATH_GID_GET_CUSTOMER_GID(cust_log_collection_info->peer_gid), ast_auth_log->gids, ast_auth_log->gids_count);
    if (res == ZPN_RESULT_ERR) {
        return res;
    }
    res = zpn_siem_log_transmit(obj, ast_auth_log->gids, ast_auth_log->gids_count, cust_log_collection_info->check_for_site);
    if (res) {
        ZPN_LOG(AL_WARNING, "Failed to log %s into siem infra: %s", cust_log_collection_info->log_type, zpn_result_string(res));
        return res;
    }

    return ZPN_RESULT_NO_ERROR;
}

int zpn_broker_pbroker_peer_geoip_lookup(struct fohh_connection *f_conn,
                                         struct argo_inet *peer_ip,
                                         struct site *peer_site)
{
    if (!f_conn || !peer_ip || !peer_site) {
        return ZPN_RESULT_BAD_ARGUMENT;
    }

    memset(peer_ip, 0, sizeof(*peer_ip));
    memset(peer_site, 0, sizeof(*peer_site));

    const int64_t peer_gid = fohh_peer_get_id(f_conn);
    struct zpn_private_broker_to_group *grp_relation;
    int res = zpn_pbroker_to_group_get_by_private_broker_gid(peer_gid, &grp_relation, 0, NULL, NULL, 0);
    if (res) {
        ZPN_LOG(AL_ERROR, "%s: Could not get private broker to group for gid = %"PRId64": %s",
                fohh_description(f_conn), peer_gid, zpn_result_string(res));
        return res;
    }

    struct zpn_private_broker_group *grp;
    res = zpn_pbroker_group_get_by_gid(grp_relation->private_broker_group_gid, &grp, 0, NULL, NULL, 0);
    if (res) {
        ZPN_LOG(AL_ERROR, "%s: Could not get private broker group info for gid = %"PRId64": %s",
                fohh_description(f_conn), grp_relation->private_broker_group_gid, zpn_result_string(res));
        return res;
    }

    fohh_connection_address(f_conn, peer_ip, NULL);

    peer_site->lat = grp->latitude;
    peer_site->lon = grp->longitude;
    if (grp->country_code) {
        snprintf(peer_site->cc, sizeof(peer_site->cc), "%s", grp->country_code);
    }

    return ZPN_RESULT_NO_ERROR;
}

static int zpn_broker_pbroker_control_conn_redirect(struct fohh_connection *f_conn)
{
    if (!zpn_broker_balance_is_conn_redirect_needed(f_conn, redirect_pbrokers, NULL)) {
        return ZPN_RESULT_NO_ERROR;
    }

    const struct zpn_broker_pbroker_fohh_state *pb_state = fohh_connection_get_dynamic_cookie(f_conn);
    ZPN_DEBUG_BALANCE("Redirecting control conn for peer with tunnel %s: %s",
                      pb_state->tunnel_id, fohh_description(f_conn));

    struct argo_inet peer_ip;
    struct site peer_site;
    int is_redirect_to_alt_cloud = 0;
    int res = zpn_broker_pbroker_peer_geoip_lookup(f_conn, &peer_ip, &peer_site);
    if (res) {
        return res;
    }

    zpn_broker_balance_conn_redirect(f_conn,
                                     ZPATH_GID_GET_CUSTOMER_GID(fohh_peer_get_id(f_conn)),
                                     ZPATH_GID_GET_CUSTOMER_GID(fohh_peer_get_id(f_conn)),
                                     &peer_ip, &peer_site,
                                     pb_state->tunnel_id,
                                     redirect_pbrokers,
                                     redirect_pbrokers_reason,
                                     0,
                                     &is_redirect_to_alt_cloud,
                                     NULL,
                                     NULL,
                                     zpn_client_type_private_broker,
                                     0);

    if (is_redirect_to_alt_cloud) {
        __sync_fetch_and_add_8(&(zpn_fohh_workers[f_conn->fohh_thread_id].pbroker_stats.num_pb_ctrl_alt_cloud_redirects), 1);
    }
    return ZPN_RESULT_NO_ERROR;
}

static int zpn_broker_pbroker_config_conn_redirect(struct fohh_connection *f_conn)
{
    if (!zpn_broker_balance_is_conn_redirect_needed(f_conn, redirect_pbrokers, NULL)) {
        return ZPN_RESULT_NO_ERROR;
    }

    struct connected_pbroker_config_fohh_state *pb_state = fohh_connection_get_dynamic_cookie(f_conn);
    ZPN_DEBUG_BALANCE("Redirecting %s conn for peer with tunnel %s: %s",
                      pb_state->type, pb_state->tunnel_id, fohh_description(f_conn));

    struct argo_inet peer_ip;
    struct site peer_site;
    int is_redirect_to_alt_cloud = 0;
    const int res = zpn_broker_pbroker_peer_geoip_lookup(f_conn, &peer_ip, &peer_site);
    if (res) {
        return res;
    }

    zpn_broker_balance_conn_redirect(f_conn,
                                     ZPATH_GID_GET_CUSTOMER_GID(fohh_peer_get_id(f_conn)),
                                     ZPATH_GID_GET_CUSTOMER_GID(fohh_peer_get_id(f_conn)),
                                     &peer_ip, &peer_site,
                                     pb_state->tunnel_id,
                                     redirect_pbrokers,
                                     redirect_pbrokers_reason,
                                     0,
                                     &is_redirect_to_alt_cloud,
                                     NULL,
                                     NULL,
                                     zpn_client_type_private_broker,
                                     0);

    if (is_redirect_to_alt_cloud) {
        if (strcmp(pb_state->type, "config") == 0) {
            __sync_fetch_and_add_8(&(zpn_fohh_workers[f_conn->fohh_thread_id].pbroker_stats.num_pb_cfg_alt_cloud_redirects), 1);
        } else if (strcmp(pb_state->type, "override") == 0) {
            __sync_fetch_and_add_8(&(zpn_fohh_workers[f_conn->fohh_thread_id].pbroker_stats.num_pb_ovd_alt_cloud_redirects), 1);
        } else if (strcmp(pb_state->type, "static_config") == 0) {
            __sync_fetch_and_add_8(&(zpn_fohh_workers[f_conn->fohh_thread_id].pbroker_stats.num_pb_rcfg_alt_cloud_redirects), 1);
        } else if (strcmp(pb_state->type, "userdb") == 0) {
            __sync_fetch_and_add_8(&(zpn_fohh_workers[f_conn->fohh_thread_id].pbroker_stats.num_pb_userdb_alt_cloud_redirects), 1);
        }
    }
    return ZPN_RESULT_NO_ERROR;
}

static void zpn_broker_pbroker_conn_info_callback(struct fohh_connection *f_conn, void *cookie)
{
    zpn_broker_pbroker_control_conn_redirect(f_conn);
}

static void zpn_broker_pbroker_cfg_conn_info_callback(struct fohh_connection *f_conn, void *cookie)
{
    zpn_broker_pbroker_config_conn_redirect(f_conn);
}

int
zpn_broker_pbroker_init()
{
    int res;
    res = zpn_broker_pbroker_stats_init();
    if (res) {
        ZPN_LOG(AL_ERROR, "Error occurred while pbroker stats init, error: %s", zpath_result_string(res));
        return res;
    }

    return ZPN_RESULT_NO_ERROR;
}


static void zpn_broker_pbroker_update_connection_snis_internal(struct fohh_generic_server *sni_server,
                                                        char *type, int wildcard_prefix,
                                                        char *old_alt_cloud, char *new_alt_cloud)
{
    char old_sni_str[ZPN_MAX_SNI_NAME_LEN + 1], new_sni_str[ZPN_MAX_SNI_NAME_LEN + 1];
    int res;

    snprintf(old_sni_str, sizeof(old_sni_str), "%s.%s", type,
                    old_alt_cloud ? old_alt_cloud : zpn_broker_get_default_cloud_name());
    snprintf(new_sni_str, sizeof(new_sni_str), "%s.%s", type,
                    new_alt_cloud ? new_alt_cloud : zpn_broker_get_default_cloud_name());

    res = fohh_generic_server_re_register(sni_server, old_sni_str, new_sni_str, wildcard_prefix);
    if (res) {
        ZPN_LOG(AL_ERROR, "alt_cloud: Could not re-register pbroker conn type: %s  generic server for %s to %s",
                            type, old_sni_str, new_sni_str);
        return;
    }
    zpn_broker_pbroker_update_sni(old_sni_str, new_sni_str, wildcard_prefix);

    ZPN_LOG(AL_NOTICE, "alt_cloud: Updated pbroker type: %s sni server from: %s  to: %s", type, old_sni_str, new_sni_str);
}

/*
 * zpn_broker_pbroker_update_connection_snis
 *  Update SNIs for all pbroker connetion when alt-cloud changes
 */
void zpn_broker_pbroker_update_connection_snis(char *old_alt_cloud, char *new_alt_cloud)
{
    struct fohh_generic_server *sni_server = NULL;

    /* Lookup sni server for broker */
    sni_server = zpath_debug_lookup_fohh_generic_server("broker");
    if (!sni_server) {
        ZPN_LOG(AL_ERROR, "Unable to lookup sni_server for broker; returning");
        return;
    }

    zpn_broker_pbroker_update_connection_snis_internal(sni_server, "pbctl", 1, old_alt_cloud, new_alt_cloud);
    zpn_broker_pbroker_update_connection_snis_internal(sni_server, "pbcfg", 1, old_alt_cloud, new_alt_cloud);
    zpn_broker_pbroker_update_connection_snis_internal(sni_server, "pbovd", 1, old_alt_cloud, new_alt_cloud);
    zpn_broker_pbroker_update_connection_snis_internal(sni_server, "pbuserdb", 1, old_alt_cloud, new_alt_cloud);
    zpn_broker_pbroker_update_connection_snis_internal(sni_server, "pbstats", 1, old_alt_cloud, new_alt_cloud);
    zpn_broker_pbroker_update_connection_snis_internal(sni_server, "pblog", 1, old_alt_cloud, new_alt_cloud);
}

struct pb_log_info {
    struct fohh_connection *f_conn;
    int64_t f_conn_incarnation;

    /* 128 bit (16 bytes, 24 bytes base64) random ID for this TLS
     * connection, in base64 */
    uint8_t tunnel_id_bin[ZPN_TUNNEL_ID_BYTES];
    char tunnel_id[ZPN_TUNNEL_ID_BYTES_TEXT + 1];
};

static void* zpn_pb_app_info_callback(struct fohh_connection *f_conn)
{
    int64_t pb_gid;
    struct pb_log_info *pb_info;
    int res;

    pb_gid = fohh_peer_get_id(f_conn);
    if (!pb_gid) return NULL;

    pb_info = ZPN_CALLOC(sizeof(*pb_info));

    /* Generate an ID for this client */
    res = RAND_bytes(&(pb_info->tunnel_id_bin[0]), ZPN_TUNNEL_ID_BYTES);
    if (1 != res) {
        ZPN_BROKER_ASSERT_HARD(0, "Crypto Random Generator not seeded!");
        ZPN_FREE(pb_info);
        return NULL;
    }
    base64_encode_binary(pb_info->tunnel_id, pb_info->tunnel_id_bin, ZPN_TUNNEL_ID_BYTES);
    pb_info->f_conn = f_conn;
    pb_info->f_conn_incarnation = fohh_connection_incarnation(f_conn);

    return pb_info;
}

static void zpn_broker_pbroker_log_stats_cb(struct fohh_connection *f_conn, enum fohh_connection_state state)
{
    if (state == fohh_connection_connected) {
        zpn_fohh_worker_pbroker_connect_log(f_conn->fohh_thread_id);
    } else {
        zpn_fohh_worker_pbroker_disconnect_log(f_conn->fohh_thread_id);
    }
}

static void zpn_broker_pbroker_log_conn_redirect(struct fohh_connection *f_conn, void *cookie)
{
    if (!zpn_broker_balance_is_conn_redirect_needed(f_conn, redirect_pbrokers, NULL)) {
        return;
    }

    struct pb_log_info *pb_info = (struct pb_log_info *)cookie;
    ZPN_LOG(AL_DEBUG, "%s: Redirecting for peer with tunnel %s", fohh_description(f_conn), pb_info->tunnel_id);

    if (fohh_connection_incarnation(f_conn) != pb_info->f_conn_incarnation) {
        return;
    }

    struct argo_inet peer_ip;
    struct site peer_site;
    int is_redirect_to_alt_cloud = 0;
    int res = zpn_broker_pbroker_peer_geoip_lookup(f_conn, &peer_ip, &peer_site);
    if (res) {
        return;
    }

    zpn_broker_balance_conn_redirect(f_conn,
                                     ZPATH_GID_GET_CUSTOMER_GID(fohh_peer_get_id(f_conn)),
                                     ZPATH_GID_GET_CUSTOMER_GID(fohh_peer_get_id(f_conn)),
                                     &peer_ip, &peer_site,
                                     pb_info->tunnel_id,
                                     redirect_pbrokers,
                                     redirect_pbrokers_reason,
                                     0,
                                     &is_redirect_to_alt_cloud,
                                     NULL,
                                     NULL,
                                     zpn_client_type_private_broker,
                                     0);

    if (is_redirect_to_alt_cloud) {
        __sync_fetch_and_add_8(&(zpn_fohh_workers[f_conn->fohh_thread_id].pbroker_stats.num_pb_log_alt_cloud_redirects), 1);
    }
}

int zpn_broker_pbroker_listen(struct fohh_generic_server *sni_server)
{
    struct fohh_connection *f_conn;
    static int initialized = 0;
    int i;
    char sni_str[ZPN_MAX_SNI_NAME_LEN + 1];
    int res;

    if (!initialized) {

        /* Set our verifier for pbroker c2 client conns from ZApp */
        res = zpn_set_pbroker_cn_verify_cb(zpn_broker_pbroker_verify_callback);
        if (res) {
            ZPN_LOG(AL_ERROR, "zpn_broker_pbroker_listen: Unable to set pbroker cn verifier");
            return res;
        }

        pbctl_tunnel_to_fohh.lock = ZPATH_MUTEX_INIT;
        pbctl_tunnel_to_fohh.table = zhash_table_alloc(&zpn_allocator);
        if (!pbctl_tunnel_to_fohh.table) {
            ZPN_LOG(AL_ERROR, "Couldn't allocate pbctl_tunnel_to_fohh table");
            return ZPN_RESULT_NO_MEMORY;
        }

        pbctl_gid_to_tunnel.lock = ZPATH_MUTEX_INIT;
        pbctl_gid_to_tunnel.table = zhash_table_alloc(&zpn_allocator);
        if (!pbctl_gid_to_tunnel.table) {
            ZPN_LOG(AL_ERROR, "Couldn't allocate pbctl_tunnel_to_fohh table");
            return ZPN_RESULT_NO_MEMORY;
        }

        /* Create wally servers for non-static tables */
        for (i = 1; i <= ZPATH_CLOUD_SHARD_COUNT; i++) {
            if (!wally_servers[i]) {
                ZPN_LOG(AL_NOTICE, "Initializing Wally Server for pvt brokers, shard %d", i);
                wally_servers[i] = wally_fohh_server_create(zpath_shard_wally[i],
                                                            argo_serialize_binary,
                                                            fohh_connection_style_argo,
                                                            1,
                                                            NULL,
                                                            0,
                                                            NULL,
                                                            NULL,
                                                            NULL,
                                                            1);
                if (!wally_servers[i]) {
                    ZPN_LOG(AL_ERROR, "Could not initialize wally server for shard %d", i);
                    return ZPN_RESULT_ERR;
                }
            }
        }

        if (is_static_wally_enabled) {
            for (i = 1; i <= ZPATH_CLOUD_SHARD_COUNT; i++) {
                if (!wally_servers_static[i]) {
                    ZPN_LOG(AL_NOTICE, "Initializing Wally Server for pvt brokers for static tables, shard %d", i);
                    wally_servers_static[i] = wally_fohh_server_create(zpath_shard_wally_static[i],
                                                                       argo_serialize_binary,
                                                                       fohh_connection_style_argo,
                                                                       1,
                                                                       NULL,
                                                                       0,
                                                                       NULL,
                                                                       NULL,
                                                                       NULL,
                                                                       1);
                    if (!wally_servers_static[i]) {
                        ZPN_LOG(AL_ERROR, "Could not initialize wally server for shard %d for static tables", i);
                        return ZPN_RESULT_ERR;
                    }
                }
            }
        }

        wally_server_global = wally_fohh_server_create(zpath_global_wally,
                                                       argo_serialize_binary,
                                                       fohh_connection_style_argo,
                                                       1,
                                                       NULL,
                                                       0,
                                                       NULL,
                                                       NULL,
                                                       NULL,
                                                       1);
        if (!wally_server_global) {
            ZPN_LOG(AL_ERROR, "Could not initialize wally global server");
            return ZPN_RESULT_ERR;
        }

        initialized = 1;
    }

    if (!sni_server) return ZPN_RESULT_ERR;

    /* create cloud_name after checking alt-cloud name */
    char cloud_name[ZPN_MAX_CLOUD_NAME_LEN + 1];
    zpn_broker_get_cloud_name(cloud_name, sizeof(cloud_name));

    f_conn = fohh_server_create(0, //int quiet,
                                argo_serialize_binary, // enum argo_serialize_mode encoding,
                                fohh_connection_style_argo, // enum fohh_connection_style style,
                                NULL, // void *cookie,
                                zpn_broker_pbroker_conn_callback,
                                NULL,
                                zpn_broker_pbroker_unblock_callback,
                                NULL,
                                NULL,
                                0,
                                NULL, // char *root_cert_file_name,
                                NULL, // char *my_cert_file_name,
                                NULL, // char *my_cert_key_file_name,
                                1, // int require pvt broker_cert,
                                1, // int use_ssl);
                                zpn_broker_client_ctx_callback,     // ssl ctx callback
                                zpn_broker_pbroker_verify_callback, // verify callback
                                zpn_broker_client_post_verify_region_check_cb, // post verify callback
                                1, // Allow binary argo.
                                ZPN_PBROKER_BROKER_RX_TIMEOUT_S); // timeout
    if (!f_conn) {
        return ZPN_RESULT_ERR;
    }

    snprintf(sni_str, sizeof(sni_str), "pbctl.%s", cloud_name);
    res = fohh_generic_server_register(sni_server,
                                       f_conn,
                                       sni_str,
                                       1,
                                       FOHH_WORKER_ZPN_PBCTL);
    if (res) {
        return res;
    }
    zpn_broker_pbroker_add_sni(sni_str, 1);

    fohh_set_info_callback(f_conn, zpn_broker_pbroker_conn_info_callback, zpn_broker_verify_alt_cloud_info_callback);

    /* pbcfg sni  */

    f_conn = fohh_server_create(0, //int quiet,
                                argo_serialize_binary, // enum argo_serialize_mode encoding,
                                fohh_connection_style_argo, // enum fohh_connection_style style,
                                NULL, // void *cookie,
                                zpn_broker_pbroker_config_conn_callback,
                                NULL,
                                zpn_broker_pbroker_config_unblock_callback,
                                NULL,
                                NULL,
                                0,
                                NULL, // char *root_cert_file_name,
                                NULL, // char *my_cert_file_name,
                                NULL, // char *my_cert_key_file_name,
                                1, // int require pvt broker cert,
                                1, // int use_ssl);
                                zpn_broker_client_ctx_callback, // ssl ctx callback
                                zpn_broker_pbroker_verify_callback, // verify callback
                                zpn_broker_client_post_verify_region_check_cb, // post verify callback
                                1, // Allow binary argo.
                                ZPN_PBROKER_BROKER_RX_TIMEOUT_S); // timeout
    if (!f_conn) {
        return ZPN_RESULT_ERR;
    }

    snprintf(sni_str, sizeof(sni_str), "pbcfg.%s", cloud_name);
    res = fohh_generic_server_register(sni_server,
                                       f_conn,
                                       sni_str,
                                       1,
                                       FOHH_WORKER_ZPN_PBCFG);
    if (res) {
        return res;
    }
    zpn_broker_pbroker_add_sni(sni_str, 1);

    fohh_set_info_callback(f_conn, zpn_broker_pbroker_cfg_conn_info_callback, zpn_broker_verify_alt_cloud_info_callback);

    if (is_static_wally_enabled) {
        f_conn = fohh_server_create(0, //int quiet,
                                    argo_serialize_binary, // enum argo_serialize_mode encoding,
                                    fohh_connection_style_argo, // enum fohh_connection_style style,
                                    NULL, // void *cookie,
                                    zpn_broker_pbroker_static_config_conn_callback,
                                    NULL,
                                    zpn_broker_pbroker_static_config_unblock_callback,
                                    NULL,
                                    NULL,
                                    0,
                                    NULL, // char *root_cert_file_name,
                                    NULL, // char *my_cert_file_name,
                                    NULL, // char *my_cert_key_file_name,
                                    1, // int require pvt broker cert,
                                    1, // int use_ssl);
                                    zpn_broker_client_ctx_callback, // ssl ctx callback
                                    zpn_broker_pbroker_verify_callback, // verify callback
                                    zpn_broker_client_post_verify_region_check_cb, // post verify callback
                                    1, // Allow binary argo.
                                    ZPN_PBROKER_BROKER_RX_TIMEOUT_S); // timeout
        if (!f_conn) {
            return ZPN_RESULT_ERR;
        }

        snprintf(sni_str, sizeof(sni_str), "pbrcfg.%s", ZPATH_LOCAL_CLOUD_NAME);
        res = fohh_generic_server_register(sni_server,
                                           f_conn,
                                           sni_str,
                                           1,
                                           FOHH_WORKER_ZPN_PBSCFG);
        if (res) {
            return res;
        }
        zpn_broker_pbroker_add_sni(sni_str, 1);

        fohh_set_info_callback(f_conn, zpn_broker_pbroker_cfg_conn_info_callback, zpn_broker_verify_alt_cloud_info_callback);
    }

    f_conn = fohh_server_create(0, //int quiet,
                                argo_serialize_binary, // enum argo_serialize_mode encoding,
                                fohh_connection_style_argo, // enum fohh_connection_style style,
                                NULL, // void *cookie,
                                zpn_broker_pbroker_config_override_conn_callback,
                                NULL,
                                zpn_broker_pbroker_config_override_unblock_callback,
                                NULL,
                                NULL,
                                0,
                                NULL, // char *root_cert_file_name,
                                NULL, // char *my_cert_file_name,
                                NULL, // char *my_cert_key_file_name,
                                1, // int require pvt broker cert,
                                1, // int use_ssl);
                                zpn_broker_client_ctx_callback, // ssl ctx callback
                                zpn_broker_pbroker_verify_callback, // verify callback
                                zpn_broker_client_post_verify_region_check_cb, // post verify callback
                                1, // Allow binary argo.
                                ZPN_PBROKER_BROKER_RX_TIMEOUT_S); // timeout
    if (!f_conn) {
        return ZPN_RESULT_ERR;
    }

    snprintf(sni_str, sizeof(sni_str), "pbovd.%s", cloud_name);
    res = fohh_generic_server_register(sni_server,
                                       f_conn,
                                       sni_str,
                                       1,
                                       FOHH_WORKER_ZPN_PBOVD);
    if (res) {
        return res;
    }
    zpn_broker_pbroker_add_sni(sni_str, 1);

    fohh_set_info_callback(f_conn, zpn_broker_pbroker_cfg_conn_info_callback, zpn_broker_verify_alt_cloud_info_callback);

    f_conn = fohh_server_create(0,                           // int quiet,
                                argo_serialize_binary,       // enum argo_serialize_mode encoding,
                                fohh_connection_style_argo,  // enum fohh_connection_style style,
                                NULL,                        // void *cookie,
                                zpn_broker_pbroker_userdb_conn_callback,
                                NULL,
                                zpn_broker_pbroker_config_override_unblock_callback,
                                NULL,
                                NULL,
                                0,
                                NULL,                                // char *root_cert_file_name,
                                NULL,                                // char *my_cert_file_name,
                                NULL,                                // char *my_cert_key_file_name,
                                1,                                   // int require pvt broker cert,
                                1,                                   // int use_ssl);
                                zpn_broker_client_ctx_callback,      // ssl ctx callback
                                zpn_broker_pbroker_verify_callback,  // verify callback
                                zpn_broker_client_post_verify_region_check_cb, // post verify callback
                                1,                                   // Allow binary argo.
                                ZPN_PBROKER_BROKER_RX_TIMEOUT_S);    // timeout
    if (!f_conn) {
        return ZPN_RESULT_ERR;
    }

    snprintf(sni_str, sizeof(sni_str), "pbuserdb.%s", cloud_name);
    res = fohh_generic_server_register(sni_server, f_conn, sni_str, 1, FOHH_WORKER_ZPN_PBUSERDB);
    if (res) {
        return res;
    }
    zpn_broker_pbroker_add_sni(sni_str, 1);

    fohh_set_info_callback(f_conn, zpn_broker_pbroker_cfg_conn_info_callback, zpn_broker_verify_alt_cloud_info_callback);

    /* Stats log connection: Only for public broker. */
    f_conn = fohh_server_create(0, //int quiet,
                                argo_serialize_binary, // enum argo_serialize_mode encoding,
                                fohh_connection_style_argo, // enum fohh_connection_style style,
                                NULL, // void *cookie,
                                zpn_broker_pbroker_stats_conn_callback,
                                NULL,
                                zpn_broker_pbroker_config_override_unblock_callback,
                                NULL,
                                NULL,
                                0,
                                NULL, // char *root_cert_file_name,
                                NULL, // char *my_cert_file_name,
                                NULL, // char *my_cert_key_file_name,
                                1, // int require_pbroker_cert,
                                1, // int use_ssl);
                                zpn_broker_client_ctx_callback, // ssl ctx callback
                                zpn_broker_pbroker_verify_callback, // verify callback
                                zpn_broker_client_post_verify_region_check_cb, // post verify callback
                                1, // Allow binary argo.
                                ZPN_PBROKER_BROKER_RX_TIMEOUT_S); // timeout
    if (!f_conn) {
        return ZPN_RESULT_ERR;
    }

    snprintf(sni_str, sizeof(sni_str), "pbstats.%s", cloud_name);

    ZPN_DEBUG_STARTUP("Register SNI Server: *.%s", sni_str);
    res = fohh_generic_server_register(sni_server, f_conn, sni_str, 1, FOHH_WORKER_ZPN_PBSTATS);
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not register generic server for SNI: %s, error: %s", sni_str, zpath_result_string(res));
        return res;
    }
    zpn_broker_pbroker_add_sni(sni_str, 1);

    fohh_set_info_callback(f_conn, zpn_broker_pbroker_stats_conn_info_callback, zpn_broker_verify_alt_cloud_info_callback);

    /* #missioncritical pbmc - private broker mission critical conn to broker */

    f_conn = fohh_server_create(0, //int quiet,
                                argo_serialize_binary, // enum argo_serialize_mode encoding,
                                fohh_connection_style_argo, // enum fohh_connection_style style,
                                NULL, // void *cookie,
                                zpn_broker_pbroker_mission_critical_conn_callback,
                                NULL,
                                zpn_broker_pbroker_unblock_callback,
                                NULL,
                                NULL,
                                0,
                                NULL, // char *root_cert_file_name,
                                NULL, // char *my_cert_file_name,
                                NULL, // char *my_cert_key_file_name,
                                1, // int require pvt broker_cert,
                                1, // int use_ssl);
                                zpn_broker_client_ctx_callback,     // ssl ctx callback
                                zpn_broker_pbroker_verify_callback, // verify callback
                                zpn_broker_client_post_verify_region_check_cb, // post verify callback
                                1, // Allow binary argo.
                                ZPN_PBROKER_BROKER_RX_TIMEOUT_S); // timeout
    if (!f_conn) {
        return ZPN_RESULT_ERR;
    }

    snprintf(sni_str, sizeof(sni_str), "pbmc.%s", cloud_name);
    res = fohh_generic_server_register(sni_server,
                                       f_conn,
                                       sni_str,
                                       1,
                                       FOHH_WORKER_ZPN_MC);
    if (res) {
        return res;
    }
    zpn_broker_pbroker_add_sni(sni_str, 1);

    fohh_set_info_callback(f_conn, zpn_broker_pbroker_conn_info_callback, zpn_broker_verify_alt_cloud_info_callback);

    /* pbmc ends */

    struct zhash_table* log_collection_map = zhash_table_alloc(&zpn_allocator);
    if (!log_collection_map) {
        ZPN_LOG(AL_ERROR, "Out of memory");
        return ZPN_RESULT_NO_MEMORY;
    }

    struct customer_log_collection_info *customer_trans_log = create_customer_log_collection("transaction",                            //log type
                                                                                             (int)zpath_customer_log_type_zpn_transaction,  //customer log type
                                                                                             zpn_trans_log_description,                //argo log description
                                                                                             zpn_transaction_collection,               //collection name
                                                                                             1,                                        //transmit to log infra
                                                                                             customer_log_collection_callback,         //transmit callback
                                                                                             zpn_trans_log_siem_callback,             //siem callback
                                                                                             zpn_trans_log_siem_data_fill_callback,   //siem data fill callback
                                                                                             0);

    char* transaction_collection_name = argo_log_get_name(zpn_transaction_collection);
    if (zhash_table_store(log_collection_map, transaction_collection_name, strlen(transaction_collection_name), 1, customer_trans_log)) {
        ZPN_LOG(AL_ERROR, "Failed to setup transaction log rx");
    }

    struct customer_log_collection_info *customer_auth_log = create_customer_log_collection("client_auth",                            //log type
                                                                                            (int)zpath_customer_log_type_zpn_auth,    //customer log type
                                                                                            zpn_auth_log_description,                 //argo log description
                                                                                            zpn_auth_collection,                      //collection name
                                                                                            1,                                        //transmit to log infra
                                                                                            customer_log_collection_callback,         //transmit callback
                                                                                            zpn_auth_log_siem_callback ,              //siem callback
                                                                                            zpn_auth_log_siem_data_fill_callback,
                                                                                            0);

    char* auth_collection_name = argo_log_get_name(zpn_auth_collection);
    if (zhash_table_store(log_collection_map, auth_collection_name, strlen(auth_collection_name), 1, customer_auth_log)) {
        ZPN_LOG(AL_ERROR, "Failed to setup auth log rx");
    }

    struct customer_log_collection_info *customer_ast_auth_log = create_customer_log_collection("asst_auth",                               //log type
                                                                                                (int)zpath_customer_log_type_zpn_ast_auth, //customer log type
                                                                                                zpn_ast_auth_log_description,              //argo log description
                                                                                                zpn_auth_collection,                       //collection name
                                                                                                1,                                         //transmit to log infra
                                                                                                customer_log_collection_callback,          //transmit callback
                                                                                                zpn_ast_auth_log_siem_callback,            //siem callback
                                                                                                zpn_ast_auth_log_siem_data_fill_callback,
                                                                                                0);

    char* ast_auth_collection_name = argo_log_get_name(zpn_ast_auth_collection);
    if (zhash_table_store(log_collection_map, ast_auth_collection_name, strlen(ast_auth_collection_name), 1, customer_ast_auth_log)) {
        ZPN_LOG(AL_ERROR, "Failed to setup auth log rx");
    }

    struct customer_log_collection_info *event_log = create_customer_log_collection(NULL,                               //log type
                                                                                    0,                                  //customer log type
                                                                                    NULL,                               //argo log description
                                                                                    zpn_event_collection,               //collection name
                                                                                    0,                                  //transmit to log infra
                                                                                    NULL,                               //transmit callback
                                                                                    NULL,                               //siem callback
                                                                                    NULL,
                                                                                    0);

    char* zpn_event_collection_name = argo_log_get_name(zpn_event_collection);
    if (zhash_table_store(log_collection_map, zpn_event_collection_name, strlen(zpn_event_collection_name), 1, event_log)) {
        ZPN_LOG(AL_ERROR, "Failed to setup event log rx");
    }

    struct customer_log_collection_info *customer_dns_log = create_customer_log_collection(NULL,                        //log type
                                                                                           0,                           //customer log type
                                                                                           NULL,                        //argo log description
                                                                                           zpn_dns_collection,          //collection name
                                                                                           0,                           //transmit to log infra
                                                                                           NULL,                        //transmit callback
                                                                                           NULL,                        //siem callback
                                                                                           NULL,
                                                                                           0);

    char* dns_collection_name = argo_log_get_name(zpn_dns_collection);
    if (zhash_table_store(log_collection_map, dns_collection_name, strlen(dns_collection_name), 1, customer_dns_log)) {
        ZPN_LOG(AL_ERROR, "Failed to setup dns log rx");
    }

    snprintf(sni_str, sizeof(sni_str), "pblog.%s", cloud_name);

    res = fohh_log_receive(sni_server,                                     //fohh_generic_server
                           sni_str,                                        //domain
                           1,                                              //wildcard_prefix
                           zpn_broker_client_ctx_callback,                 //ssl ctx oallback
                           zpn_broker_pbroker_verify_callback,             //verify callback
                           fohh_log_conn_info_callback,                    //info callback
                           zpn_broker_verify_alt_cloud_info_callback,      // verify info callback
                           zpn_broker_client_post_verify_region_check_cb,  // post verify callback
                           zpn_pb_app_info_callback,                       // app info callback
                           zpn_broker_pbroker_log_conn_redirect,           //log conn redirect callback
                           zpn_broker_pbroker_log_stats_cb,                //log stats callback
                           zpn_broker_pbroker_log_connection_monitor_timer_cb,          //log_tunnel_timer_callback
                           zpn_broker_pbroker_log_connection_periodic_logging_timer_cb, //log_timer_callback
                           log_collection_map,                            //log collection map
                           NULL,
                           0);
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not set up log receiver: %s", zpn_result_string(res));
        return res;
    }

    zpn_broker_pbroker_add_sni(sni_str, 1);
    ZPN_LOG(AL_NOTICE, "Done setting up log receiver");

    return ZPN_RESULT_NO_ERROR;
}

/* Pbroker control gid to tunnel lookup */
static int zpn_broker_pbctl_gid_to_tunnel_lookup(int64_t pbroker_gid, char *ret_tunnel_id, size_t ret_tunnel_id_len)
{
    int ret = ZPN_RESULT_NOT_FOUND;

    ZPATH_MUTEX_LOCK(&pbctl_gid_to_tunnel.lock, __FILE__, __LINE__);
    struct pbctl_gid_tunnel_id_info *info = zhash_table_lookup(pbctl_gid_to_tunnel.table, &pbroker_gid, sizeof(pbroker_gid), NULL);
    if (info) {
        snprintf(ret_tunnel_id, ret_tunnel_id_len, "%s", info->tunnel_id);
        ret = ZPN_RESULT_NO_ERROR;
    }
    ZPATH_MUTEX_UNLOCK(&pbctl_gid_to_tunnel.lock, __FILE__, __LINE__);

    return ret;
}

/* Pbroker control gid to tunnel store */
static void zpn_broker_pbctl_gid_to_tunnel_store(int64_t pbroker_gid, char *tunnel_id, size_t tunnel_id_len)
{
    struct pbctl_gid_tunnel_id_info *info = ZPN_CALLOC(sizeof(*info));
    if (!info) {
        ZPN_LOG(AL_CRITICAL, "Memory allocation error allocating pbctl_gid_tunnel_id_info for gid: %ld", (long)pbroker_gid);
        return;
    }

    info->pbroker_gid = pbroker_gid;
    snprintf(info->tunnel_id, sizeof(info->tunnel_id), "%s", tunnel_id);

    ZPATH_MUTEX_LOCK(&pbctl_gid_to_tunnel.lock, __FILE__, __LINE__);
    zhash_table_store(pbctl_gid_to_tunnel.table, &pbroker_gid, sizeof(pbroker_gid), 0, info);
    ZPATH_MUTEX_UNLOCK(&pbctl_gid_to_tunnel.lock, __FILE__, __LINE__);
}

/* Pbroker control gid to tunnel remove */
static void zpn_broker_pbctl_gid_to_tunnel_remove(int64_t pbroker_gid)
{
    ZPATH_MUTEX_LOCK(&pbctl_gid_to_tunnel.lock, __FILE__, __LINE__);
    struct pbctl_fohh_info *info = zhash_table_lookup(pbctl_gid_to_tunnel.table, &pbroker_gid, sizeof(pbroker_gid), NULL);
    if (info) {
        zhash_table_remove(pbctl_gid_to_tunnel.table, &pbroker_gid, sizeof(pbroker_gid), NULL);
        ZPN_FREE(info);
    }
    ZPATH_MUTEX_UNLOCK(&pbctl_gid_to_tunnel.lock, __FILE__, __LINE__);
}

/* Get fohh connection by pbroker gid */
static struct fohh_connection* zpn_broker_pbctl_get_fohh_connection_by_pbroker_gid(int64_t pbroker_gid)
{
    char tunnel_id[ZPN_TUNNEL_ID_BYTES_TEXT + 1];
    int res;
    struct fohh_connection *f_conn = NULL;
    int64_t conn_incarnation = 0;

    /* Lookup tunnel_id by gid */
    res = zpn_broker_pbctl_gid_to_tunnel_lookup(pbroker_gid, tunnel_id, sizeof(tunnel_id));
    if (res) {
        return NULL;
    }

    /* Lookup connection by tunnel_id */
    res = zpn_broker_pbctl_tunnel_fohh_lookup(&f_conn, &conn_incarnation, tunnel_id, strnlen(tunnel_id, ZPN_TUNNEL_ID_BYTES_TEXT));
    if (res) {
        return NULL;
    }

    return f_conn;
}

/* Get debug flags by pbroker gid */
static int zpn_broker_pbctl_get_debug_flags_by_pbroker_gid(int64_t pbroker_gid, uint64_t *debug_flag)
{
    int ret = ZPN_RESULT_NOT_FOUND;

    if (!debug_flag) {
        ZPN_LOG(AL_ERROR, "Invalid null debug_flag arg");
        return ZPN_RESULT_BAD_ARGUMENT;
    }

    ZPATH_MUTEX_LOCK(&pbctl_gid_to_tunnel.lock, __FILE__, __LINE__);
    struct pbctl_gid_tunnel_id_info *info = zhash_table_lookup(pbctl_gid_to_tunnel.table, &pbroker_gid, sizeof(pbroker_gid), NULL);
    if (info) {
        *debug_flag = info->debug_flag;
        ret = ZPN_RESULT_NO_ERROR;
    }
    ZPATH_MUTEX_UNLOCK(&pbctl_gid_to_tunnel.lock, __FILE__, __LINE__);

    return ret;
}

/* Set debug flags by pbroker gid */
static int zpn_broker_pbctl_set_debug_flags_by_pbroker_gid(int64_t pbroker_gid, uint64_t debug_flag)
{
    int ret = ZPN_RESULT_NOT_FOUND;

    ZPATH_MUTEX_LOCK(&pbctl_gid_to_tunnel.lock, __FILE__, __LINE__);
    struct pbctl_gid_tunnel_id_info *info = zhash_table_lookup(pbctl_gid_to_tunnel.table, &pbroker_gid, sizeof(pbroker_gid), NULL);
    if (info) {
        info->debug_flag = debug_flag;
        ret = ZPN_RESULT_NO_ERROR;
    }
    ZPATH_MUTEX_UNLOCK(&pbctl_gid_to_tunnel.lock, __FILE__, __LINE__);

    return ret;
}

/* Set pbroker debug flags from broker */
int zpn_broker_pbroker_debug_flag(struct zpath_debug_state *request_state,
                                  const char **query_values,
                                  int query_value_count,
                                  void *cookie)
{
    int64_t pbroker_gid = 0;
    uint64_t debug_flag = 0;
    struct fohh_connection *f_conn = NULL;
    struct zpn_private_broker *pbroker = NULL;
    const char *w;
    int res;

    if (!query_values[0]) {
        return ZPATH_RESULT_ERR;
    }

    /* Parse first integer of CN */
    for (w = query_values[0]; *w; w++) {
        if (isdigit(*w)) {
            pbroker_gid = strtoll(w, NULL, 0);
            break;
        }
    }

    ZPATH_LOG(AL_DEBUG, "zpn_broker_pbroker_debug_flag(), pbroker name = %s, id = %ld",
              query_values[0], (long)pbroker_gid);

    /* Fetch pbroker connection */
    f_conn = zpn_broker_pbctl_get_fohh_connection_by_pbroker_gid(pbroker_gid);
    if (!f_conn) {
        ZPN_LOG(AL_ERROR, "Cannot find pbroker_gid: %ld connection, may be disconnected", (long)pbroker_gid);
        zpath_debug_cb_printf_response(request_state, "Cannot find pbroker %s, it may not be connected to this broker\n", query_values[0]);
        return ZPATH_RESULT_NO_ERROR;
    }

    /* Check pbroker db state */
    res = zpn_private_broker_get_by_id(pbroker_gid, &pbroker, NULL, NULL, 0);
    if (res) {
        if (res != ZPN_RESULT_ASYNCHRONOUS) {
            /* Some error. Yuck. */
            ZPN_LOG(AL_ERROR, "%s: Could not get private broker for gid = %ld: %s", fohh_description(f_conn), (long)pbroker_gid, zpn_result_string(res));
        }
        return ZPATH_RESULT_NO_ERROR;
    }

    /* Dont operate if disabled */
    if (!pbroker->enabled) {
        ZPN_LOG(AL_NOTICE, "%s: private broker %ld is not enabled. Not continuing with setting debug_flag", fohh_description(f_conn), (long)pbroker_gid);
        zpath_debug_cb_printf_response(request_state, "Cannot send commad to pbroker %s, it is disabled\n", query_values[0]);
        return ZPATH_RESULT_NO_ERROR;
    }

    /* Fetch debug flag */
    res = zpn_broker_pbctl_get_debug_flags_by_pbroker_gid(pbroker_gid, &debug_flag);
    if (res) {
        ZPN_LOG(AL_ERROR, "Unable to get debug_flag for pbroker_gid: %ld", (long)pbroker_gid);
        return res;
    }

    /* Block scope */
    {
        int count;

        if (!query_values[1]) {
            zpath_debug_cb_printf_response(request_state, "DEBUG, %s = %"PRIx64"\n", query_values[0], debug_flag);
            for (count = 0; zpn_debug_names[count]; count++) {
                zpath_debug_cb_printf_response(request_state,
                                               "%s : %s\n",
                                               (debug_flag & (1 << count)) ? "ON" : "OFF",
                                               zpn_debug_names[count]);
            }
        } else {
            for (count = 0; zpn_debug_names[count]; count++) {
                if (!strncmp(query_values[1], zpn_debug_names[count], strlen(zpn_debug_names[count]))) {
                    zpath_debug_cb_printf_response(request_state,
                                                   "Debug flag %s was %s, setting it to %s\n",
                                                   query_values[1],
                                                   (debug_flag & (1 << count)) ? "ON" : "OFF",
                                                   (debug_flag & (1 << count)) ? "OFF" : "ON");

                    if (debug_flag & (1 << count)) {
                        debug_flag &= ~(1 << count);
                    } else {
                        debug_flag |= (1 << count);
                    }

                    /* Update pbroker */
                    zpn_send_pbroker_log_control(f_conn,
                                                 fohh_connection_incarnation(f_conn),
                                                 pbroker_gid,
                                                 ZPN_PBCTL_LOG_CONTROL_OPCODE_SET_LOGGING_FLAG,                 // type-1 ==> Debug flags control
                                                 ZPN_PBCTL_LOG_CONTROL_OPCODE_SET_LOG_UPLOAD_STATE_DEFAULT,     // upload-0 ==> (default, i.e nop)
                                                 count  // debug_flag bit
                                                 );
                    break;
                }
            }

            /* Update debug flag */
            zpn_broker_pbctl_set_debug_flags_by_pbroker_gid(pbroker_gid, debug_flag);
            if (res) {
                ZPN_LOG(AL_ERROR, "Unable to get debug_flag for pbroker_gid: %ld", (long)pbroker_gid);
                return res;
            }

            if (!zpn_debug_names[count]) {
                /* Flag name is wrong */
                zpath_debug_cb_printf_response(request_state, "Cannot find flag %s\n", query_values[1]);
            }
        }
    }

    return ZPATH_RESULT_NO_ERROR;
}

static void zpn_broker_pbroker_add_sni(char* sni_str, int wildcard) {
    ZPN_ASSERT(zpn_broker_pbroker_snis_count < MAX_ZPN_BROKER_PBROKER_SNIS);
    zpn_broker_pbroker_snis[zpn_broker_pbroker_snis_count] = ZPN_STRDUP(sni_str, strlen(sni_str));
    zpn_broker_pbroker_sni_flags[zpn_broker_pbroker_snis_count] = wildcard;
    zpn_broker_pbroker_snis_count++;
}

static void zpn_broker_pbroker_update_sni(char* old_sni_str, char *new_sni_str, int wildcard) {
    ZPN_ASSERT(zpn_broker_pbroker_snis_count < MAX_ZPN_BROKER_PBROKER_SNIS);

    for (int count = 0; count < zpn_broker_pbroker_snis_count; count++) {
        if (0 == strcmp(zpn_broker_pbroker_snis[count], old_sni_str)) {
            ZPN_FREE(zpn_broker_pbroker_snis[count]);
            zpn_broker_pbroker_snis[count] = ZPN_STRDUP(new_sni_str, strlen(new_sni_str));
            zpn_broker_pbroker_sni_flags[count] = wildcard;
            return;
        }
    }
    ZPN_LOG(AL_ERROR, "Could not update broker_pbroker sni: %s to %s", old_sni_str, new_sni_str);
}

void zpn_broker_pbroker_get_snis(char*** snis_out, int** flags_out, int* count_out)
{
    if (snis_out) {
        *snis_out = zpn_broker_pbroker_snis;
    }
    if (flags_out) {
        *flags_out = zpn_broker_pbroker_sni_flags;
    }
    if (count_out) {
        *count_out = zpn_broker_pbroker_snis_count;
    }
}

/* Initiate redirecting all pbrokers, triggered when broker is about to be shut down.
 * Actual redirects will be carried out by *_monitor_cb which is triggered periodically.
 */
void zpn_broker_pbroker_redirect_pbrokers(int do_it, const char* reason)
{
    redirect_pbrokers = do_it;
    redirect_pbrokers_reason = reason;
}

int zpn_broker_pbroker_get_redirect_pbrokers_flag()
{
    return redirect_pbrokers;
}

const char *zpn_broker_pbroker_get_redirect_pbrokers_reason()
{
    return redirect_pbrokers_reason;
}

/* Initiate disconnecting all pbrokers, triggered when broker is about to be shut down.
 * Actual disconnects will be carried out by *_monitor_cb which is triggered periodically.
 */
void zpn_broker_pbroker_disconnect_pbrokers(int do_it, const char* reason)
{
    disconnect_pbrokers = do_it;
    disconnect_pbrokers_reason = reason;
}

int zpn_broker_pbroker_get_disconnect_pbrokers_flag()
{
    return disconnect_pbrokers;
}

const char *zpn_broker_pbroker_get_disconnect_pbrokers_reason()
{
    return disconnect_pbrokers_reason;
}

void zpn_broker_pbroker_get_connection_counts(int* control) {
    if (control) {
        *control = 0;
        ZPATH_MUTEX_LOCK(&pbctl_tunnel_to_fohh.lock, __FILE__, __LINE__);
        *control = (int)zhash_table_get_size(pbctl_tunnel_to_fohh.table);
        ZPATH_MUTEX_UNLOCK(&pbctl_tunnel_to_fohh.lock, __FILE__, __LINE__);
    }
}

int64_t zpn_broker_pbroker_total_pbrokers_connections_to_redirect(const struct zpn_fohh_worker_pbroker_stats* pbroker_stats) {
    return pbroker_stats->num_pbroker_cfg
         + pbroker_stats->num_pbroker_rcfg
         + pbroker_stats->num_pbroker_ovd
         + pbroker_stats->num_pbroker_stats
         + pbroker_stats->num_pbroker_data
         + pbroker_stats->num_pbroker_ctrl
         + pbroker_stats->num_pbroker_log
         + pbroker_stats->num_pbroker_userdb;
}

/* zpn_broker_pbroker_validate_version
 * Verify if version of the pbroker is allowed to connect with broker
 * based on the data in zpn_version_control table
 */
static int zpn_broker_pbroker_validate_version(const int64_t pb_gid,
                                                wally_response_callback_f callback_f,
                                                void *callback_cookie,
                                                int64_t callback_id) {
    struct zpn_private_broker_version *version;
    int result;
    /* Perform Version Control check only from Public Broker */
    if (!ZPN_BROKER_IS_PUBLIC()) {
        return ZPN_RESULT_NO_ERROR;
    }
    if (zpn_broker_feature_is_version_control_enabled() == 0) {
        ZPN_DEBUG_VER_CONTROL("ZPN_VERSION_CONTROL Feature is disabled ");
        return ZPN_RESULT_NO_ERROR;
    }
    result = zpn_private_broker_version_get_by_id(pb_gid, &version, callback_f, callback_cookie, callback_id);
    if (ZPN_RESULT_NO_ERROR == result) {
        ZPN_DEBUG_PRIVATE_BROKER("Received pbroker %"PRId64" with version %s ",pb_gid, version->current_version);
        /* If the client version is lesser than BASELINE VERSION
         * Then continue with Version check here.
         * If the client version is same are greater than BASELINE VERSION,
         * Then version check is performed in zpn_version rpc, that gracefully handles invalid version
         */
        if(zpn_is_version_less_than_baseline_version(version->current_version, zpn_version_control_client_type_private_broker)) {
            // Check the version against the broker version and return error
            enum zpn_version_control_client_type client_type=zpn_version_control_client_type_private_broker;
            enum zpn_version_control_server_type server_type=zpn_version_control_server_type_broker;
            result = zpn_check_client_version_allowed(client_type,  server_type, version->current_version);
            if (ZPN_RESULT_NO_ERROR != result) {
                ZPN_LOG(AL_ERROR, "Received pbroker %"PRId64" with version %s is REJECTED - %s",pb_gid, version->current_version,zpn_result_string(result));
            }
        }
    } else {
        /* The zpn_private_broker_version table is populated based on authlog by AUM.
         * Allow the connection temporarily, so that authlog could be generated and consumed by AUM
         */
        if (WALLY_RESULT_NOT_FOUND == result) {
            ZPN_LOG(AL_WARNING, "This is a new private broker, connection is allowed temporarily");
            return ZPN_RESULT_NO_ERROR;
        }
        if (WALLY_RESULT_ASYNCHRONOUS == result) {
            ZPN_DEBUG_PRIVATE_BROKER("Asynchronous private broker version fetch for gid = %"PRId64"", pb_gid);
        } else {
            ZPN_LOG(AL_ERROR, "FAILED - Fetching pbroker version from zpn_private_broker_version table - %s",zpn_result_string(result));
        }
    }
    return result;
}

static int zpn_pbroker_version_cb(void *argo_cookie_ptr __attribute__((unused)), void *argo_structure_cookie_ptr, struct argo_object *object) {
    struct zpn_version *version = object->base_structure_void;
    struct fohh_connection *f_conn = argo_structure_cookie_ptr;

    //send back zpn_version_ack to pbroker based on version received
    struct zpn_tlv tlv;
    tlv.type = zpn_fohh_tlv;
    tlv.conn.f_conn = f_conn;
    tlv.conn_incarnation = fohh_connection_incarnation(f_conn);
    tlv.tlv_incarnation = 0;
    if (zpn_debug_get(ZPN_DEBUG_VER_CONTROL_IDX)) {
        char buf[ARGO_BUF_DEFAULT_SIZE];
        if (argo_object_dump(object, buf, sizeof(buf), NULL, 0) == ARGO_RESULT_NO_ERROR) {
            ZPN_DEBUG_PRIVATE_BROKER("%s: %s: Rx zpn_version: %s", zpn_tlv_description(&tlv), zpn_tlv_peer_cn(&tlv), buf);
        }
    }
    return zpn_process_client_zpn_version_msg(version, &tlv, zpn_client_type_private_broker);

}
