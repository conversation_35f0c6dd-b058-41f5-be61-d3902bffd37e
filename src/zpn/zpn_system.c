/*
 * zpn_system.c. Copyright (C) 2020 Zscaler Inc. All Rights Reserved.
 */
#include "zpath_lib/zpath_system.h"
#include <dirent.h>
#include <sys/resource.h>
#include <sys/stat.h>
#include <fcntl.h>
#include <ifaddrs.h>
#include "zpn/zpn_rpc.h"

#include "zpn/zpn_private_broker_private.h"
#include "zpn/zpn_private_broker_util.h"
#include "zpn/zpn_system.h"
#include "zpn/assistant_log.h"
#include "zpn/zpn_sitec_log.h"
#include "zpath_lib/zpath_system_stats.h"
#include "zpath_lib/zpath_config_feature_groups.h"
#include "zpath_lib/zpath_config_override_keys.h"
#include "zpath_lib/zpath_config_override_desc.h"
#include "zpath_lib/zpath_config_override.h"
#include <inttypes.h>


/*
 * The VM that we package for connectors have memory buffers&cache disabled. This means there is no memory reserved for
 * I/O. In case when connector software uses up 100% of the memory, IO can get stuck and cause system weirdness(we
 * have seen one customer issue where the VM became non-responsive to ssh for hours as IO is stuck). So we should
 * limit the system memory usage to something less than 100%.
 *
 * SOFT & EXTRA_SOFT determine when to log little vs detailed information about memory usage. Someday we will have
 * ULTRA_SOFT too, like pillows!
 */
#define ASSISTANT_MAX_MEMORY_USAGE_EXTRA_SOFT_LIMIT    70
#define ASSISTANT_MAX_MEMORY_USAGE_SOFT_LIMIT          95
#define ASSISTANT_MAX_MEMORY_USAGE_HARD_LIMIT          98

/* PBroker memory logging limits, same as Connector for now  */
#define PBROKER_MAX_MEMORY_USAGE_EXTRA_SOFT_LIMIT    70
#define PBROKER_MAX_MEMORY_USAGE_SOFT_LIMIT          95
#define PBROKER_MAX_MEMORY_USAGE_HARD_LIMIT          98

/* Sitec memory logging limits, same as Connector for now  */
#define SITEC_MAX_MEMORY_USAGE_EXTRA_SOFT_LIMIT    70
#define SITEC_MAX_MEMORY_USAGE_SOFT_LIMIT          95
#define SITEC_MAX_MEMORY_USAGE_HARD_LIMIT          98

/* Process system uptime */
#define SECS_PER_MIN 60
#define MINS_PER_HOUR 60
#define HOURS_PER_DAY 24

#define LINUX_LINE_SIZE 80
#define BUFFER_SIZE 4096

/*
 * We need a minimum of 200MB of disk space left to keep operating. Actually our real disk requirement is much less
 * than that a. ~6MB for connector binary(for root user, think sarge) b. few KB for writing the certs and version files
 * (for non-root user, think connector-child process). But, if we find that the usable disk space is close to 200MB,
 * then it can soon hit the dangerously low mark.
 *
 * Also let us monitor the disk usage for non-root users as the non-root user, connector-child also require file access.
 */
#define ASSISTANT_MIN_DISK_AVAILABLE_BYTES_SOFT_LIMIT  (200 * 1024 * 1024)
#define ASSISTANT_MIN_DISK_AVAILABLE_BYTES_HARD_LIMIT  (100 * 1024 * 1024)

/* PBroker avaliable disk limits are same as Connector for now */
#define PBROKER_MIN_DISK_AVAILABLE_BYTES_SOFT_LIMIT  (200 * 1024 * 1024)
#define PBROKER_MIN_DISK_AVAILABLE_BYTES_HARD_LIMIT  (100 * 1024 * 1024)

/* Sitec avaliable disk limits are same as Connector for now */
#define SITEC_MIN_DISK_AVAILABLE_BYTES_SOFT_LIMIT  (200 * 1024 * 1024)
#define SITEC_MIN_DISK_AVAILABLE_BYTES_HARD_LIMIT  (100 * 1024 * 1024)

/* System threshold */
struct zpn_system_threshold g_system_threshold = { 0 };

static struct zpath_config_override_desc zpn_system_stats_descriptions[] = {
    {
        .key                = ZPN_SYSTEM_NET_STATS_ENABLED,
        .desc               = "when enabled allows zpn system network stats from broker, pse and connector to be sent to magellan",
        .details            = "0: ZPN system network stats is not enabled\n"
                              "1: ZPN system network stats is enabled\n"
                              "Order of check: global\n"
                              "default: 0 (i.e. ZPN system network stats is not enabled by default)",
        .val_type           = config_type_int,
        .component_types    = config_component_pbroker | config_component_broker | config_component_appc,
        .target_gid_types   = config_target_gid_type_global,
        .int_range_lo       = 0,
        .int_range_hi       = 1,
        .int_default        = ZPN_SYSTEM_NET_STATS_ENABLED_DEFAULT,
        .feature_group      = FEATURE_GROUP_ZPN_SYS_STATS,
        .value_traits       = config_value_traits_feature_enablement,
    }
};

int zpn_sys_stats_init_keys(void)
{
    int res = ZPN_RESULT_NO_ERROR;
    for (int i = 0; i < (sizeof(zpn_system_stats_descriptions)/sizeof(struct zpath_config_override_desc)); i++) {
        res = zpath_config_override_desc_register(&zpn_system_stats_descriptions[i]);
        if (res) {
            break;
        }
    }
    return res;
}

/* Set thresholds based on type of system i.e Connector or PBroker */
void
zpn_set_system_threshold_type(enum zpn_system_threshold_type type,
                              zpn_cloud_adjusted_time_us_cb cloud_adj_time_us,
                              zpn_system_assert_cb assert_cb,
                              int is_dev_env,
                              zpn_system_is_ready_cb is_ready_cb,
                              zpn_system_get_cpu_util_cb cpu_util_cb,
                              zpn_system_get_cpu_steal_perc_cb cpu_steal_perc_cb,
                              zpn_system_get_sys_mem_util_cb sys_mem_util_cb,
                              zpn_system_get_proc_mem_util_cb proc_mem_util_cb,
                              zpn_system_get_available_cpus_cb available_cpus_cb,
                              int64_t sys_uptime_s)
{

    struct zpn_system_threshold *s = &g_system_threshold;

    /* Set types and callbacks */
    s->type = type;
    s->cloud_adj_time_us = cloud_adj_time_us;
    s->assert_cb = assert_cb;
    s->is_ready_cb = is_ready_cb;
    s->cpu_util_cb = cpu_util_cb;
    s->cpu_steal_perc_cb = cpu_steal_perc_cb;
    s->sys_mem_util_cb = sys_mem_util_cb;
    s->proc_mem_util_cb = proc_mem_util_cb;
    s->available_cpus_cb = available_cpus_cb;
    s->sys_uptime_s = sys_uptime_s;
    s->is_dev_env = is_dev_env;

    switch (type) {

        /* Set thresholds for connector */
    case ZPN_SYSTEM_THRESHOLD_TYPE_ASSISTANT:

        /* Disk thresholds */
        s->disk_min_avail_bytes_soft_limit = ASSISTANT_MIN_DISK_AVAILABLE_BYTES_SOFT_LIMIT;
        s->disk_min_avali_bytes_hard_limit = ASSISTANT_MIN_DISK_AVAILABLE_BYTES_HARD_LIMIT;

        /* Memory thresholds */
        s->mem_max_usage_bytes_extra_soft_limit = ASSISTANT_MAX_MEMORY_USAGE_EXTRA_SOFT_LIMIT;
        s->mem_max_usage_bytes_soft_limit = ASSISTANT_MAX_MEMORY_USAGE_SOFT_LIMIT;
        s->mem_max_usage_bytes_hard_limit = ASSISTANT_MAX_MEMORY_USAGE_HARD_LIMIT;
        s->module_name = ASSISTANT_MODULE_NAME;
        s->module_logging_name = ASSISTANT_ARGO_LOG_NAME;
        break;

        /* Set thresolds for Pbroker */
    case ZPN_SYSTEM_THRESHOLD_TYPE_PBROKER:

        /* Disk thresholds */
        s->disk_min_avail_bytes_soft_limit = PBROKER_MIN_DISK_AVAILABLE_BYTES_SOFT_LIMIT;
        s->disk_min_avali_bytes_hard_limit = PBROKER_MIN_DISK_AVAILABLE_BYTES_HARD_LIMIT;

        /* Memory thresholds */
        s->mem_max_usage_bytes_extra_soft_limit = PBROKER_MAX_MEMORY_USAGE_EXTRA_SOFT_LIMIT;
        s->mem_max_usage_bytes_soft_limit = PBROKER_MAX_MEMORY_USAGE_SOFT_LIMIT;
        s->mem_max_usage_bytes_hard_limit = PBROKER_MAX_MEMORY_USAGE_HARD_LIMIT;
        s->module_name = SERVICE_EDGE_LOG_NAME;
        s->module_logging_name = PRIVATE_BROKER_ARGO_LOG_NAME;
        break;

    /* Set thresolds for Sitec */
    case ZPN_SYSTEM_THRESHOLD_TYPE_SITEC:

        /* Disk thresholds */
        s->disk_min_avail_bytes_soft_limit = SITEC_MIN_DISK_AVAILABLE_BYTES_SOFT_LIMIT;
        s->disk_min_avali_bytes_hard_limit = SITEC_MIN_DISK_AVAILABLE_BYTES_HARD_LIMIT;

        /* Memory thresholds */
        s->mem_max_usage_bytes_extra_soft_limit = SITEC_MAX_MEMORY_USAGE_EXTRA_SOFT_LIMIT;
        s->mem_max_usage_bytes_soft_limit = SITEC_MAX_MEMORY_USAGE_SOFT_LIMIT;
        s->mem_max_usage_bytes_hard_limit = SITEC_MAX_MEMORY_USAGE_HARD_LIMIT;
        s->module_name = SITEC_LOG_NAME;
        s->module_logging_name = SITEC_ARGO_LOG_NAME;
        break;

        /* Unexpected type */
    case ZPN_SYSTEM_THRESHOLD_TYPE_INVALID:
    default:
        s->module_logging_name = ZPN_SYSTEM_DEFAULT_LOG_NAME;
        s->module_name = ZPN_SYSTEM_DEFAULT_LOG_NAME;

        /* Hard assert in dev environment, we dont expect this */
        ZPN_SYSTEM_ASSERT(1, 1, "Unexpected threshold type");

        bzero(s, sizeof(*s));
        s->type = ZPN_SYSTEM_THRESHOLD_TYPE_INVALID;
        s->cloud_adj_time_us = NULL;
        s->assert_cb = NULL;
        s->is_ready_cb = NULL;
        s->cpu_util_cb = NULL;
        s->sys_mem_util_cb = NULL;
        s->proc_mem_util_cb = NULL;
        s->is_dev_env = 0;
        s->module_name = NULL;
        s->module_logging_name = NULL;
        break;
    }
}

/* Get threshold type */
enum zpn_system_threshold_type
zpn_get_system_threshold_type(void)
{
    return g_system_threshold.type;
}

/*
 * Stats upload callback to fill disk related info.
 */
int
zpn_system_disk_stats_fill(void*     cookie,
                           int       counter,
                           void*     structure_data)
{
    struct zpn_system_disk_stats *    out_data;

    out_data = (struct zpn_system_disk_stats *)structure_data;
    out_data->cloud_time_us = zpn_system_get_current_time_cloud_us();

    return zpath_system_disk_stats_fill(cookie, counter, structure_data);
}


/*
 * Stats upload callback to fill cpu related info.
 */
int
zpn_system_cpu_stats_fill(void*     cookie,
                          int       counter,
                          void*     structure_data)
{
    struct zpn_system_cpu_stats *    out_data;
    int res;
    /*
     * its ok to not consider err case below as even in that case values are zero-ed out. more than anything its
     * highly unlikely to fail
     */
    res = zpath_system_cpu_stats_fill(cookie, counter, structure_data);

    out_data = (struct zpn_system_cpu_stats *)structure_data;
    out_data->cloud_time_us = zpn_system_get_current_time_cloud_us();
    out_data->cpu_util_percent = zpn_system_get_cpu_util();
    out_data->cpu_steal_percent = zpn_system_get_cpu_steal_perc();

    return res;
}

int
zpn_system_memory_stats_fill(void     *cookie __attribute__((unused)),
                             int       counter,
                             void     *structure_data) {
    struct zpn_system_memory_stats *out_data;
    int                             system_used_percent = 0;
    int                             process_used_percent = 0;

    out_data = (struct zpn_system_memory_stats *)structure_data;

    out_data->cloud_time_us = zpn_system_get_current_time_cloud_us();

    zpath_system_get_system_memory(&out_data->system_total_bytes,
                                   &out_data->system_free_bytes,
                                   &out_data->system_used_buffer_bytes,
                                   &out_data->system_configured_swap_bytes,
                                   &out_data->system_free_swap_bytes);
    out_data->process_used_bytes = zpath_system_get_process_mem_usage_bytes();
    zpath_system_get_system_and_process_memory_util_percentage(&system_used_percent,
                                                               &process_used_percent);
    out_data->system_used_percent = system_used_percent;
    out_data->process_used_percent = process_used_percent;

    return ZPATH_RESULT_NO_ERROR;
}

/*
 * Stats upload callback to fill memory related info.
 */
int
assistant_pbroker_system_memory_stats_fill(void*     cookie,
                                           int       counter,
                                           void*     structure_data)
{
    struct zpn_system_memory_stats *    out_data;

    (void)cookie;

    out_data = (struct zpn_system_memory_stats *)structure_data;

    out_data->cloud_time_us = zpn_system_get_current_time_cloud_us();
    zpath_system_get_system_memory(&out_data->system_total_bytes,
                                   &out_data->system_free_bytes,
                                   &out_data->system_used_buffer_bytes,
                                   &out_data->system_configured_swap_bytes,
                                   &out_data->system_free_swap_bytes);
    out_data->process_used_bytes = zpath_system_get_process_mem_usage_bytes();
    out_data->system_used_percent = zpn_system_get_system_mem_util();
    out_data->process_used_percent = zpn_system_get_process_mem_util();
    return ZPATH_RESULT_NO_ERROR;
}

/*
 * Stats upload callback to fill network related info.
 */
int
zpn_system_network_stats_fill(void*     cookie,
                              int       counter,
                              void*     structure_data)
{
    struct zpn_system_network_stats *    out_data;

    (void)cookie;

    out_data = (struct zpn_system_network_stats *)structure_data;

    int64_t config_value = 0;
    config_value = zpath_config_override_get_config_int(ZPN_SYSTEM_NET_STATS_ENABLED,
                                                             &config_value,
                                                             ZPN_SYSTEM_NET_STATS_ENABLED_DEFAULT,
                                                             (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                             (int64_t)0);

    if (!config_value) {
        ZPN_LOG(AL_INFO, "ZPN System network stats is not enabled to sent stats to magellan");
        return ZPATH_RESULT_NO_ERROR;
    }

    zpn_system_get_system_network_netstat(&out_data->listen_overflows,
                                          &out_data->listen_drops,
                                          &out_data->tcp_loss_failures,
                                          &out_data->tcp_lost_retransmit,
                                          &out_data->tcp_retrans_fail,
                                          &out_data->tcp_from_zero_window_adv,
                                          &out_data->tcp_to_zero_window_adv);
    zpn_system_get_system_network_snmp(&out_data->in_receives,
                                       &out_data->in_hdr_errors,
                                       &out_data->in_addr_errors,
                                       &out_data->forw_datagrams,
                                       &out_data->in_unknown_protos,
                                       &out_data->in_discards,
                                       &out_data->in_delivers,
                                       &out_data->in_csum_error);
    return ZPATH_RESULT_NO_ERROR;
}


/*
 * Stats upload callback to fill file descriptor related info.
 *
 * Only the first time it is possible that fd_info.ready is 0. Other times, the monitor thread will fill the info
 * for the stats to just consume. It is ok even if the data is stale by few seconds as the stats export is not going
 * to be very frequent than the monitor frequency - if this notion changes, this logic should be revisited.
 *
 */
int
zpn_system_fd_stats_fill(void*     cookie,
                               int       counter,
                               void*     structure_data)
{
    struct zpn_system_fd_stats *    out_data;
    int res;

    res = zpath_system_fd_stats_fill(cookie, counter, structure_data);
    out_data = (struct zpn_system_fd_stats *)structure_data;
    out_data->cloud_time_us = zpn_system_get_current_time_cloud_us();

    return res;
}


/*
 * Stats upload callback to fill socket related info.
 *
 * Only the first time it is possible that sock_info.ready is 0. Other times, the monitor thread will fill the info
 * for the stats to just consume. It is ok even if the data is stale by few seconds as the stats export is not going
 * to be very frequent than the monitor frequency - if this notion changes, this logic should be revisited.
 *
 * Send the invalid stats as -1.
 *
 */
int
zpn_system_sock_stats_fill(void*     cookie,
                                 int       counter,
                                 void*     structure_data)
{
    struct zpn_system_sock_stats *    out_data;
    int res;

    res = zpath_system_sock_stats_fill(cookie, counter, structure_data);
    out_data = (struct zpn_system_sock_stats *)structure_data;
    out_data->cloud_time_us = zpn_system_get_current_time_cloud_us();

    return res;
}

/*
 * Fill assistant system stats into zpn_assistant_comprehensive_stats, which will be consumed by GUI/LSS
 */
int
zpn_system_comprehensive_stats_fill(void* structure_data, void* cookie)
{
    double                                      cpu_load_avg[3];
    struct zpn_assistant_comprehensive_stats*   out_data;
    out_data = (struct zpn_assistant_comprehensive_stats*)structure_data;

    /* Disk stats */
    zpath_system_get_disk_info(&out_data->available_disk_bytes, NULL, NULL);

    /* CPU stats */
    zpath_system_get_cpu_load_averages(cpu_load_avg, cookie);
    /* Since comprehensive stats is filled every 5min,
     * we'll fill in the 5min load average data */
    out_data->cpu_load_avg_per_core = cpu_load_avg[1];
    out_data->cpu_util_percent = zpath_system_get_max_cpu_util();
    zpath_system_reset_max_cpu_util();

    /* Memory stats */
    out_data->mem_util_system_percent = zpath_system_get_max_system_mem_util();
    out_data->mem_util_process_percent = zpath_system_get_max_process_mem_util();
    zpath_system_reset_max_system_and_process_memory_util();

    if (!zpath_is_system_fd_info_ready()) {
        ZPN_SYSTEM_IS_READY(1, "system component is not yet initialized to get FD stats");
        zpath_system_fd_stats_get(0);
    }

    if (!zpath_is_system_sock_info_ready()) {
        ZPN_SYSTEM_IS_READY(1, "system component is not yet initialized to get sock stats");
        zpath_system_sock_stats_get(0);
    }

    /* FD stats */
    zpath_system_get_fd_in_use(&out_data->system_fd_in_use, &out_data->process_fd_in_use);
    zpath_system_get_fd_max(&out_data->system_fd_max, &out_data->process_fd_max);

    /* Socket stats */
    zpath_system_get_num_usable_ports(&out_data->num_usable_ports);
    zpath_system_get_tcp_socket_inuse(&out_data->num_system_tcpv4_socket_inuse,
                                      &out_data->num_system_tcpv6_socket_inuse);
    zpath_system_get_udp_socket_inuse(&out_data->num_system_udpv4_socket_inuse,
                                      &out_data->num_system_udpv6_socket_inuse);

    return ZPATH_RESULT_NO_ERROR;
}

int
zpn_system_pbroker_comprehensive_stats_fill(void* structure_data, void* cookie)
{
    double                                      cpu_load_avg[3];


    struct zpn_pbroker_comprehensive_stats*   out_data;
    out_data = (struct zpn_pbroker_comprehensive_stats*)structure_data;

    /* Disk stats */
    zpath_system_get_disk_info(&out_data->available_disk_bytes, NULL, NULL);

    /* CPU stats */
    zpath_system_get_cpu_load_averages(cpu_load_avg, cookie);
    /* Since comprehensive stats is filled every 5min,
     * we'll fill in the 5min load average data */
    out_data->cpu_load_avg_per_core = cpu_load_avg[1];
    out_data->cpu_util_percent = zpath_system_get_max_cpu_util();
    zpath_system_reset_max_cpu_util();

    /* Memory stats */
    out_data->mem_util_system_percent = zpath_system_get_max_system_mem_util();
    out_data->mem_util_process_percent = zpath_system_get_max_process_mem_util();
    zpath_system_reset_max_system_and_process_memory_util();

    if (!zpath_is_system_fd_info_ready()) {
        ZPN_SYSTEM_IS_READY(1, "system component is not yet initialized");
        zpath_system_fd_stats_get(0);
        zpath_system_sock_stats_get(0);
    }

    /* FD stats */
    zpath_system_get_fd_in_use(&out_data->system_fd_in_use, &out_data->process_fd_in_use);
    zpath_system_get_fd_max(&out_data->system_fd_max, &out_data->process_fd_max);

    /* Socket stats */
    zpath_system_get_num_usable_ports(&out_data->num_usable_ports);
    zpath_system_get_tcp_socket_inuse(&out_data->num_system_tcpv4_socket_inuse,
                                      &out_data->num_system_tcpv6_socket_inuse);
    zpath_system_get_udp_socket_inuse(&out_data->num_system_udpv4_socket_inuse,
                                      &out_data->num_system_udpv6_socket_inuse);

    return ZPATH_RESULT_NO_ERROR;
}

int
zpn_system_sitec_comprehensive_stats_fill(void* structure_data, void* cookie)
{
    double                                      cpu_load_avg[3];

    struct zpn_sitec_comprehensive_stats*   out_data;
    out_data = (struct zpn_sitec_comprehensive_stats*)structure_data;

    /* Disk stats */
    zpath_system_get_disk_info(&out_data->available_disk_bytes, NULL, NULL);

    /* CPU stats */
    zpath_system_get_cpu_load_averages(cpu_load_avg, cookie);
    /* Since comprehensive stats is filled every 5min,
     * we'll fill in the 5min load average data */
    out_data->cpu_load_avg_per_core = cpu_load_avg[1];
    out_data->cpu_util_percent = zpath_system_get_max_cpu_util();
    zpath_system_reset_max_cpu_util();

    /* Memory stats */
    out_data->mem_util_system_percent = zpath_system_get_max_system_mem_util();
    out_data->mem_util_process_percent = zpath_system_get_max_process_mem_util();
    zpath_system_reset_max_system_and_process_memory_util();

    if (!zpath_is_system_fd_info_ready()) {
        ZPN_SYSTEM_IS_READY(1, "system component is not yet initialized");
        zpath_system_fd_stats_get(0);
        zpath_system_sock_stats_get(0);
    }

    /* FD stats */
    zpath_system_get_fd_in_use(&out_data->system_fd_in_use, &out_data->process_fd_in_use);
    zpath_system_get_fd_max(&out_data->system_fd_max, &out_data->process_fd_max);

    /* Socket stats */
    zpath_system_get_num_usable_ports(&out_data->num_usable_ports);
    zpath_system_get_tcp_socket_inuse(&out_data->num_system_tcpv4_socket_inuse,
                                      &out_data->num_system_tcpv6_socket_inuse);
    zpath_system_get_udp_socket_inuse(&out_data->num_system_udpv4_socket_inuse,
                                      &out_data->num_system_udpv6_socket_inuse);

    return ZPATH_RESULT_NO_ERROR;
}

static void
zpn_system_assistant_monitor_memory(struct zpn_assistant_system_stats *stats)
{
    if (stats->system_mem_util >= g_system_threshold.mem_max_usage_bytes_hard_limit) {
        ZPN_LOG(AL_CRITICAL, "Memory utilization is very high(%d%%), restarting..", stats->system_mem_util);
        sleep(1);
        exit(1);
    } else if (stats->system_mem_util >= g_system_threshold.mem_max_usage_bytes_soft_limit) {
        ZPN_LOG(AL_CRITICAL, "Memory utilization is high(%d%%)", stats->system_mem_util);
        zpath_allocator_log();
    } else if (stats->system_mem_util >= g_system_threshold.mem_max_usage_bytes_extra_soft_limit) {
        ZPN_LOG(AL_INFO, "Memory utilization is rising, currently at (%d%%)", stats->system_mem_util);
        zpath_allocator_log_highest_consumer_only();
    }
}


static void
zpn_system_assistant_monitor_disk(struct zpn_assistant_system_stats *stats)
{
    if (-1 != stats->free_disk_bytes_for_non_root_user) {
        if (stats->free_disk_bytes_for_non_root_user <= g_system_threshold.disk_min_avali_bytes_hard_limit) {
            ZPN_LOG(AL_CRITICAL, "Disk availability is extremely low(%"PRId64")bytes. Check 'df -h', restarting..",
                          stats->free_disk_bytes_for_non_root_user);
            sleep(1);
            exit(1);
        } else if (stats->free_disk_bytes_for_non_root_user <= g_system_threshold.disk_min_avail_bytes_soft_limit) {
            ZPN_LOG(AL_CRITICAL, "Disk availability is very low(%"PRId64")bytes. Check 'df -h'",
                            stats->free_disk_bytes_for_non_root_user);
        }
    }
    else {
        ZPN_LOG(AL_INFO, "Could not get the disk usage info");
    }
}

void
zpn_system_sitec_monitor_memory(struct zpn_sitec_system_stats *stats)
{
    if (stats->system_mem_util >= g_system_threshold.mem_max_usage_bytes_hard_limit) {
        ZPN_LOG(AL_CRITICAL, "Memory utilization is very high(%d%%), restarting..", stats->system_mem_util);
        sleep(1);
        exit(1);
    } else if (stats->system_mem_util >= g_system_threshold.mem_max_usage_bytes_soft_limit) {
        ZPN_LOG(AL_CRITICAL, "Memory utilization is high(%d%%)", stats->system_mem_util);
        zpath_allocator_log();
    } else if (stats->system_mem_util >= g_system_threshold.mem_max_usage_bytes_extra_soft_limit) {
        ZPN_LOG(AL_INFO, "Memory utilization is rising, currently at (%d%%)", stats->system_mem_util);
        zpath_allocator_log_highest_consumer_only();
    }
}


void
zpn_system_sitec_monitor_disk(struct zpn_sitec_system_stats *stats)
{
    if (-1 != stats->free_disk_bytes_for_non_root_user) {
        if (stats->free_disk_bytes_for_non_root_user <= g_system_threshold.disk_min_avali_bytes_hard_limit) {
            ZPN_LOG(AL_CRITICAL, "Disk availability is extremely low(%"PRId64")bytes. Check 'df -h', restarting..",
                          stats->free_disk_bytes_for_non_root_user);
            sleep(1);
            exit(1);
        } else if (stats->free_disk_bytes_for_non_root_user <= g_system_threshold.disk_min_avail_bytes_soft_limit) {
            ZPN_LOG(AL_CRITICAL, "Disk availability is very low(%"PRId64")bytes. Check 'df -h'",
                            stats->free_disk_bytes_for_non_root_user);
        }
    }
    else {
        ZPN_LOG(AL_INFO, "Could not get the disk usage info");
    }
}


void
zpn_system_pbroker_monitor_memory(struct zpn_private_broker_system_stats* stats)
{
    if (stats->system_mem_util >= g_system_threshold.mem_max_usage_bytes_hard_limit) {
        PBROKER_LOG(AL_CRITICAL, "Memory utilization is very high(%d%%), restarting..", stats->system_mem_util);
        sleep(1);
        abort();
    } else if (stats->system_mem_util >= g_system_threshold.mem_max_usage_bytes_soft_limit) {
        PBROKER_LOG(AL_CRITICAL, "Memory utilization is high(%d%%)", stats->system_mem_util);
        zpath_allocator_log();
    } else if (stats->system_mem_util >= g_system_threshold.mem_max_usage_bytes_extra_soft_limit) {
        PBROKER_LOG(AL_INFO, "Memory utilization is rising, currently at (%d%%)", stats->system_mem_util);
        zpath_allocator_log_highest_consumer_only();
    }
}


void
zpn_system_pbroker_monitor_disk(struct zpn_private_broker_system_stats *stats)
{
    if (-1 != stats->free_disk_bytes_for_non_root_user) {
        if (stats->free_disk_bytes_for_non_root_user <= g_system_threshold.disk_min_avali_bytes_hard_limit) {
            PBROKER_LOG(AL_CRITICAL, "Disk availability is extremely low(%"PRId64")bytes. Check 'df -h', restarting..",
                          stats->free_disk_bytes_for_non_root_user);
            sleep(1);
            exit(1);
        } else if (stats->free_disk_bytes_for_non_root_user <= g_system_threshold.disk_min_avail_bytes_soft_limit) {
            PBROKER_LOG(AL_CRITICAL, "Disk availability is very low(%"PRId64")bytes. Check 'df -h'",
                          stats->free_disk_bytes_for_non_root_user);
        }
    } else {
        PBROKER_LOG(AL_INFO, "Could not get the disk usage info");
    }
}

/* Get cloud time bssed on system */
int64_t
zpn_system_get_current_time_cloud_us(void)
{
    enum zpn_system_threshold_type type = zpn_get_system_threshold_type();

    switch (type) {
    case ZPN_SYSTEM_THRESHOLD_TYPE_PBROKER:
    case ZPN_SYSTEM_THRESHOLD_TYPE_ASSISTANT:
        return g_system_threshold.cloud_adj_time_us ? g_system_threshold.cloud_adj_time_us() : 0;

    default:
        return epoch_us();
    }
}

/* Get available cpus bssed on system */
int64_t
zpn_system_get_available_cpus(void)
{
    enum zpn_system_threshold_type type = zpn_get_system_threshold_type();

    switch (type) {
    case ZPN_SYSTEM_THRESHOLD_TYPE_PBROKER:
    case ZPN_SYSTEM_THRESHOLD_TYPE_ASSISTANT:
        return g_system_threshold.available_cpus_cb ? g_system_threshold.available_cpus_cb() : 0;

    default:
        return 0;
    }
}

/* Assistant monitor routine */
void
zpn_system_assistant_monitor(struct zpn_assistant_system_stats *stats)

{
    ZPN_SYSTEM_IS_READY(1, "system component is not yet initialized");

    zpn_system_assistant_monitor_memory(stats);
    zpn_system_assistant_monitor_disk(stats);
    zpath_system_monitor_fd();
    zpath_system_monitor_socket();
}

/* Sitec monitor routine */
void
zpn_system_pbroker_monitor(struct zpn_private_broker_system_stats *stats)
{
    ZPN_SYSTEM_IS_READY(1, "system component is not yet initialized");

    zpn_system_pbroker_monitor_memory(stats);
    zpn_system_pbroker_monitor_disk(stats);
    zpath_system_monitor_fd();
    zpath_system_monitor_socket();
}

void
zpn_system_sitec_monitor(struct zpn_sitec_system_stats *stats)
{
    ZPN_SYSTEM_IS_READY(1, "system component is not yet initialized");

    zpn_system_sitec_monitor_memory(stats);
    zpn_system_sitec_monitor_disk(stats);
    zpath_system_monitor_fd();
    zpath_system_monitor_socket();
}


/* Public broker monitor routine */
void
zpn_system_public_broker_monitor(void)
{
    zpath_system_monitor_fd();
    zpath_system_monitor_socket();
}

int
assistant_is_ready(void)
{
    return (zpath_is_system_fd_info_ready() ? ZPATH_RESULT_NO_ERROR : ZPN_RESULT_NOT_READY);
}

int
zpn_system_init(enum zpn_system_threshold_type type,
                zpn_cloud_adjusted_time_us_cb cloud_adj_time_us_cb,
                zpn_system_assert_cb assert_cb,
                int is_dev_env,
                zpn_system_is_ready_cb is_ready_cb,
                zpn_system_get_cpu_util_cb cpu_util_cb,
                zpn_system_get_cpu_steal_perc_cb cpu_steal_perc_cb,
                zpn_system_get_sys_mem_util_cb sys_mem_util_cb,
                zpn_system_get_proc_mem_util_cb proc_mem_util_cb,
                zpn_system_get_available_cpus_cb available_cpus_cb,
                int64_t sys_uptime_s)

{
    zpn_set_system_threshold_type(type,
                                  cloud_adj_time_us_cb,
                                  assert_cb,
                                  is_dev_env,
                                  is_ready_cb,
                                  cpu_util_cb,
                                  cpu_steal_perc_cb,
                                  sys_mem_util_cb,
                                  proc_mem_util_cb,
                                  available_cpus_cb,
                                  sys_uptime_s);

    zpath_system_stats_mutex_init();

    return ZPN_RESULT_NO_ERROR;
}

void
zpn_sitec_system_get_cpu_util(struct zpn_sitec_system_stats *stats)
{
    struct zpath_system_cpu_stats_util cpu_stats_util = {0};

    cpu_stats_util.nonidle = stats->nonidle;
    cpu_stats_util.idle = stats->idle;
    cpu_stats_util.cpu_steal = stats->cpu_steal;

    if (ZPATH_RESULT_NO_ERROR != zpath_system_get_cpu_stats(&cpu_stats_util)) {
        PBROKER_LOG(AL_ERROR, "Could not get system cpu stats");
        return;
    }

    // Fill the result.
    stats->cpu_util = cpu_stats_util.cpu_util;
    stats->cpu_steal_perc = cpu_stats_util.cpu_steal_perc;
    stats->nonidle = cpu_stats_util.nonidle;
    stats->idle = cpu_stats_util.idle;
    stats->cpu_steal = cpu_stats_util.cpu_steal;

    return;
}

void
zpn_pbroker_system_get_cpu_util(struct zpn_private_broker_system_stats *stats)
{
    struct zpath_system_cpu_stats_util cpu_stats_util = {0};

    cpu_stats_util.nonidle = stats->nonidle;
    cpu_stats_util.idle = stats->idle;
    cpu_stats_util.cpu_steal = stats->cpu_steal;

    if (ZPATH_RESULT_NO_ERROR != zpath_system_get_cpu_stats(&cpu_stats_util)) {
        PBROKER_LOG(AL_ERROR, "Could not get system cpu stats");
        return;
    }

    // Fill the result.
    stats->cpu_util = cpu_stats_util.cpu_util;
    stats->cpu_steal_perc = cpu_stats_util.cpu_steal_perc;
    stats->nonidle = cpu_stats_util.nonidle;
    stats->idle = cpu_stats_util.idle;
    stats->cpu_steal = cpu_stats_util.cpu_steal;

    return;
}
void
zpn_assistant_system_get_cpu_util(struct zpn_assistant_system_stats *stats)
{
    struct zpath_system_cpu_stats_util cpu_stats_util = {0};

    cpu_stats_util.nonidle = stats->nonidle;
    cpu_stats_util.idle = stats->idle;
    cpu_stats_util.cpu_steal = stats->cpu_steal;

    if (ZPATH_RESULT_NO_ERROR != zpath_system_get_cpu_stats(&cpu_stats_util)) {
        ASSISTANT_LOG(AL_ERROR, "Could not get system cpu stats");
        return;
    }

    // Fill the result.
    stats->cpu_util = cpu_stats_util.cpu_util;
    stats->cpu_steal_perc = cpu_stats_util.cpu_steal_perc;
    stats->nonidle = cpu_stats_util.nonidle;
    stats->idle = cpu_stats_util.idle;
    stats->cpu_steal = cpu_stats_util.cpu_steal;

    return;
}

void
zpn_sitec_system_get_cpu_util_from_cgroups(struct zpn_sitec_system_stats *stats, int cgroups_version)
{
    struct zpath_system_cpu_stats_util cpu_stats_util = {0};

    cpu_stats_util.nonidle = stats->nonidle;
    cpu_stats_util.idle = stats->idle;
    cpu_stats_util.cpu_steal = stats->cpu_steal;

    if (ZPATH_RESULT_NO_ERROR != zpath_system_get_cpu_stats_from_cgroups(&cpu_stats_util, cgroups_version,
                                        g_system_threshold.sys_uptime_s, zpn_system_get_available_cpus())) {
        SITEC_LOG(AL_ERROR, "Could not get system cpu stats from cgroups, getting from host instead");
        if (ZPATH_RESULT_NO_ERROR != zpath_system_get_cpu_stats(&cpu_stats_util)) {
            SITEC_LOG(AL_ERROR, "Could not get system cpu stats");
            return;
        }
    }

    // Fill the result.
    stats->cpu_util = cpu_stats_util.cpu_util;
    stats->cpu_steal_perc = cpu_stats_util.cpu_steal_perc;
    stats->nonidle = cpu_stats_util.nonidle;
    stats->idle = cpu_stats_util.idle;
    stats->cpu_steal = cpu_stats_util.cpu_steal;

    return;
}

void
zpn_pbroker_system_get_cpu_util_from_cgroups(struct zpn_private_broker_system_stats *stats, int cgroups_version)
{
    struct zpath_system_cpu_stats_util cpu_stats_util = {0};

    cpu_stats_util.nonidle = stats->nonidle;
    cpu_stats_util.idle = stats->idle;
    cpu_stats_util.cpu_steal = stats->cpu_steal;

    if (ZPATH_RESULT_NO_ERROR != zpath_system_get_cpu_stats_from_cgroups(&cpu_stats_util, cgroups_version,
                                        g_system_threshold.sys_uptime_s, zpn_system_get_available_cpus())) {
        PBROKER_LOG(AL_ERROR, "Could not get system cpu stats from cgroups, getting from host instead");
        if (ZPATH_RESULT_NO_ERROR != zpath_system_get_cpu_stats(&cpu_stats_util)) {
            ASSISTANT_LOG(AL_ERROR, "Could not get system cpu stats");
            return;
        }
    }

    // Fill the result.
    stats->cpu_util = cpu_stats_util.cpu_util;
    stats->cpu_steal_perc = cpu_stats_util.cpu_steal_perc;
    stats->nonidle = cpu_stats_util.nonidle;
    stats->idle = cpu_stats_util.idle;
    stats->cpu_steal = cpu_stats_util.cpu_steal;

    return;
}

void
zpn_assistant_system_get_cpu_util_from_cgroups(struct zpn_assistant_system_stats *stats, int cgroups_version)
{
    struct zpath_system_cpu_stats_util cpu_stats_util = {0};

    cpu_stats_util.nonidle = stats->nonidle;
    cpu_stats_util.idle = stats->idle;
    cpu_stats_util.cpu_steal = stats->cpu_steal;

    if (ZPATH_RESULT_NO_ERROR != zpath_system_get_cpu_stats_from_cgroups(&cpu_stats_util, cgroups_version,
                                        g_system_threshold.sys_uptime_s, zpn_system_get_available_cpus())) {
        ASSISTANT_LOG(AL_ERROR, "Could not get system cpu stats from cgroups, getting from host instead");
        if (ZPATH_RESULT_NO_ERROR != zpath_system_get_cpu_stats(&cpu_stats_util)) {
            ASSISTANT_LOG(AL_ERROR, "Could not get system cpu stats");
            return;
        }
    }

    // Fill the result.
    stats->cpu_util = cpu_stats_util.cpu_util;
    stats->cpu_steal_perc = cpu_stats_util.cpu_steal_perc;
    stats->nonidle = cpu_stats_util.nonidle;
    stats->idle = cpu_stats_util.idle;
    stats->cpu_steal = cpu_stats_util.cpu_steal;

    return;
}

uint16_t zpn_system_get_cpu_util(void)
{
    return (g_system_threshold.cpu_util_cb ? g_system_threshold.cpu_util_cb() : 0);
}

uint16_t zpn_system_get_cpu_steal_perc(void)
{
    return (g_system_threshold.cpu_steal_perc_cb ? g_system_threshold.cpu_steal_perc_cb() : 0);
}

int zpn_system_get_system_mem_util(void)
{
    return (g_system_threshold.sys_mem_util_cb ? g_system_threshold.sys_mem_util_cb() : 0);
}

int zpn_system_get_process_mem_util(void)
{
    return (g_system_threshold.proc_mem_util_cb ? g_system_threshold.proc_mem_util_cb() : 0);
}

int zpn_process_system_uptime(int64_t sys_uptime, char* uptime_info, int16_t buf_size)
{
    int16_t sys_up_secs = sys_uptime % SECS_PER_MIN;
    int16_t sys_up_mins = (sys_uptime % (MINS_PER_HOUR * SECS_PER_MIN))/MINS_PER_HOUR;
    int16_t sys_up_hours = (sys_uptime % (HOURS_PER_DAY * MINS_PER_HOUR * SECS_PER_MIN))/(MINS_PER_HOUR * SECS_PER_MIN);
    int16_t sys_up_days =  sys_uptime/(HOURS_PER_DAY * MINS_PER_HOUR * SECS_PER_MIN);

    if(sys_up_days) {
        snprintf(uptime_info, buf_size, "%2d hrs:%2d mins:%2d secs up for %d days",sys_up_hours, sys_up_mins, sys_up_secs, sys_up_days );
    }
    else if(sys_up_hours) {
        snprintf(uptime_info, buf_size, "%2d hrs:%2d mins:%2d secs",sys_up_hours, sys_up_mins, sys_up_secs );
    }
    else if(sys_up_mins) {
        snprintf(uptime_info, buf_size, "%2d mins:%2d secs", sys_up_mins, sys_up_secs );
    }
    else {
        snprintf(uptime_info, buf_size, "%"PRId64" secs", sys_uptime );
    }

    return ZPN_RESULT_NO_ERROR;
}

int
zpn_system_inventory_stats_fill(void*     cookie,
                                int       counter,
                                void*     structure_data)
{
    struct stat st;
    struct zpn_system_inventory_stats *out_data;
    struct zpn_system_state *state;

    out_data = (struct zpn_system_inventory_stats*)structure_data;
    state = (struct zpn_system_state*)cookie;

    if (stat("/sbin/setcap", &st) != 0) {
        out_data->sbin_setcap_stat_errno = errno;
    } else {
        out_data->sbin_setcap_stat_errno = 0;
    }

    out_data->configured_cpus = state->available_cpus;
    out_data->available_cpus = state->available_cpus;
    out_data->fohh_threads  = state->fohh_threads;
    out_data->is_container_env = state->is_container_env;
    out_data->is_zscaler_os = state->is_zscaler_os;

    return ZPATH_RESULT_NO_ERROR;
}

int zpn_system_convert_to_bytes(char* buffer, uint64_t* value, int maxlen)
{
    unsigned long long result = strtoull(buffer, NULL, 10);
    if ((errno == ERANGE) || (errno == EINVAL)) {
        return ZPATH_RESULT_ERR;
    }

    int buffer_len = strnlen(buffer, maxlen);

    if (!buffer_len) {
        return ZPATH_RESULT_ERR;
    }

    char* suffix = buffer + buffer_len - 1;

    if (*suffix == 'K' || *suffix == 'k')
        result *= 1024;
    else if (*suffix == 'M' || *suffix == 'm')
        result *= 1024 * 1024;
    else if (*suffix == 'G' || *suffix == 'g')
        result *= 1024 * 1024 * 1024;

    *value = result;
    return ZPATH_RESULT_NO_ERROR;
}

int zpn_system_read_swap_for_cgroups_v1(uint64_t* SwapTotal, uint64_t* SwapFree)
{
    FILE* file = fopen("/sys/fs/cgroup/memory/memory.stat", "r");
    if (file == NULL) {
        return ZPATH_RESULT_ERR;
    }

    char buffer[256];
    unsigned long long swap = 0;
    unsigned long long total_swap = 0;
    char* token_save_ptr = NULL;

    while (fgets(buffer, sizeof(buffer), file) != NULL) {
        char* token = strtok_r(buffer, " ", &token_save_ptr);
        if (token != NULL && strcmp(token, "swap") == 0) {
            token = strtok_r(NULL, " ", &token_save_ptr);
            if (token != NULL) {
                swap = strtoull(token, NULL, 10);
            }
        } else if (token != NULL && strcmp(token, "total_swap") == 0) {
            token = strtok_r(NULL, " ", &token_save_ptr);
            if (token != NULL) {
                total_swap = strtoull(token, NULL, 10);
            }
        }
    }

    fclose(file);

    *SwapFree = total_swap - swap;
    *SwapTotal = total_swap;
    return ZPATH_RESULT_NO_ERROR;

}

int zpn_system_read_value_from_file(char *file_path, uint64_t* value)
{
    FILE *fp = fopen(file_path, "r");
    if (fp == NULL) {
        return ZPATH_RESULT_ERR;
    }

    char buffer[256];
    if (fgets(buffer, sizeof(buffer), fp) == NULL) {
        fclose(fp);
        return ZPATH_RESULT_ERR;
    }

    int ret = zpn_system_convert_to_bytes(buffer, value, 256);

    fclose(fp);

    return ret;

}

int
zpn_system_get_memory_usage_info_from_cgroups_v1(uint64_t *memtotal_abs_mem, uint64_t* memfree_abs_mem,
                                                 uint64_t *swaptotal_abs_mem, uint64_t *swapfree_abs_mem,
                                                 uint64_t *system_used_abs_mem) {

    uint64_t MemUsage, MemTotal, SwapTotal, SwapFree;

    MemUsage = MemTotal = SwapTotal = SwapFree = 0;

    if (zpn_system_read_value_from_file("/sys/fs/cgroup/memory/memory.usage_in_bytes", &MemUsage)) {
        ZPN_LOG(AL_ERROR, " Unable to get value from /sys/fs/cgroup/memory/memory.usage_in_bytes");
        return ZPATH_RESULT_ERR;
    }

    if (zpn_system_read_value_from_file("/sys/fs/cgroup/memory/memory.limit_in_bytes", &MemTotal)) {
        ZPN_LOG(AL_ERROR, " Unable to get value from /sys/fs/cgroup/memory/memory.limit_in_bytes");
        return ZPATH_RESULT_ERR;
    }

    if (zpn_system_read_swap_for_cgroups_v1(&SwapTotal, &SwapFree)) {
        return ZPATH_RESULT_ERR;
    }

    /*absolute memory usage info in KB*/
    *memtotal_abs_mem = MemTotal / 1024;
    *memfree_abs_mem = (MemTotal - MemUsage) / 1024;
    *swaptotal_abs_mem = SwapTotal / 1024;
    *swapfree_abs_mem = SwapFree / 1024;
    *system_used_abs_mem = MemUsage / 1024;

    return ZPATH_RESULT_NO_ERROR;
}

int zpn_system_get_host_memory_total(uint64_t *mem_total, uint64_t *swap_total)
{
    int fd = -1;
    char buffer[4096+1];
    ssize_t len;
    uint64_t MemTotal;
    uint64_t SwapTotal;

    if (fd <= 0) {
        fd = open("/proc/meminfo", O_RDONLY);
    }

    if (fd == -1) {
        ZPN_LOG(AL_ERROR, " Unable to open /proc/meminfo (%s)", strerror(errno));
        return ZPN_RESULT_ERR;
    }

    MemTotal = SwapTotal = 0;
    lseek(fd, 0, SEEK_SET);
    if ((len = read(fd, buffer, sizeof(buffer)-1)) > 0)
    {
        const char *p = buffer;

        buffer[len] = '\0';

        /* Parse the meminfo file */
        while (*p != '\0') {
            const int LINE_SIZE = 80;
            char line[LINE_SIZE];
            int i = 0;

            memset(line, 0, LINE_SIZE);

            while ((*p != '\n') && (i < (LINE_SIZE - 1))) {
                line[i] = *p;
                i++;
                p++;
            }

            line[i] = '\0';

            if (!strncmp(line, "MemTotal", strlen("MemTotal"))) {
                if(sscanf(line, "MemTotal: %" SCNu64, &MemTotal) != 1){
                    ZPN_LOG(AL_ERROR, "Failed to parse MemTotal");
                    close(fd);
                    return ZPN_RESULT_ERR;
                }
            }

            if (!strncmp(line, "SwapTotal", strlen("SwapTotal"))) {
                if(sscanf(line, "SwapTotal: %" SCNu64, &SwapTotal) != 1){
                    ZPN_LOG(AL_ERROR, "Failed to parse SwapTotal");
                    close(fd);
                    return ZPN_RESULT_ERR;
                }
            }

            /* Skip the '\n' */
            p++;
        }

    }

    if (MemTotal == 0) {
        ZPN_LOG(AL_ERROR, "MemTotal: %"PRId64", SwapTotal: %"PRId64"", MemTotal, SwapTotal);
        goto err;
    }

    *mem_total = MemTotal * 1024;
    *swap_total = SwapTotal * 1024;
    close(fd);
    return ZPN_RESULT_NO_ERROR;

err:
    close(fd);
    return ZPN_RESULT_ERR;
}

int zpn_system_read_memory_value_from_file(const char *file_path, uint64_t* value)
{
    int ret;
    FILE *fp = fopen(file_path, "r");
    if (fp == NULL) {
        return ZPATH_RESULT_ERR;
    }

    char buffer[256];
    if (fgets(buffer, sizeof(buffer), fp) == NULL) {
        fclose(fp);
        return ZPATH_RESULT_ERR;
    }

    if (strstr(buffer, "max") != NULL) {
        //max keyword indicates, the host limit is to be used as the theoretical max.
        uint64_t mem_total;
        uint64_t swap_total;
        ret = zpn_system_get_host_memory_total(&mem_total, &swap_total);
        if (ret == ZPATH_RESULT_NO_ERROR) {
            if (strstr(buffer, "swap") != NULL) {
                *value = swap_total;
            } else {
                *value = mem_total;
            }
        } else {
            ZPN_LOG(AL_ERROR, "Fetching Memory Limit from host due to max keyword failed %"PRId64"", *value);
        }
        fclose(fp);
        return ret;
    }

    ret = zpn_system_convert_to_bytes(buffer, value, 256);

    fclose(fp);

    return ret;

}

int
zpn_system_get_memory_usage_info_from_cgroups_v2(uint64_t *memtotal_abs_mem, uint64_t* memfree_abs_mem,
                                                 uint64_t *swaptotal_abs_mem, uint64_t *swapfree_abs_mem,
                                                 uint64_t *system_used_abs_mem) {

    uint64_t MemUsage, MemTotal, SwapTotal, SwapUsage;

    MemUsage = MemTotal = SwapTotal = SwapUsage = 0;

    if (zpn_system_read_value_from_file("/sys/fs/cgroup/memory.current", &MemUsage))
    {
        ZPN_LOG(AL_ERROR, " Unable to get value from /sys/fs/cgroup/memory.current");
        return ZPATH_RESULT_ERR;
    }

    if (zpn_system_read_memory_value_from_file("/sys/fs/cgroup/memory.max", &MemTotal))
    {
        ZPN_LOG(AL_ERROR, " Unable to get value from /sys/fs/cgroup/memory.max");
        return ZPATH_RESULT_ERR;
    }

    if (zpn_system_read_value_from_file("/sys/fs/cgroup/memory.swap.current", &SwapUsage)) {
        ZPN_LOG(AL_ERROR, " Unable to get value from /sys/fs/cgroup/memory.swap.current");
        return ZPATH_RESULT_ERR;
    }

    if (zpn_system_read_memory_value_from_file("/sys/fs/cgroup/memory.swap.max", &SwapTotal))
    {
        ZPN_LOG(AL_ERROR, " Unable to get value from /sys/fs/cgroup/memory.swap.max");
        return ZPATH_RESULT_ERR;
    }

    /*absolute memory usage info in KB*/
    *memtotal_abs_mem = MemTotal / 1024;
    *memfree_abs_mem = (MemTotal - MemUsage) / 1024;
    *swaptotal_abs_mem = SwapTotal / 1024;
    *swapfree_abs_mem = (SwapTotal - SwapUsage) / 1024;
    *system_used_abs_mem = MemUsage / 1024;

    return ZPATH_RESULT_NO_ERROR;
}

int
zpn_system_get_memory_usage_info_from_cgroups_impl(uint64_t *memtotal_abs_mem, uint64_t* memfree_abs_mem,
                                                   uint64_t *swaptotal_abs_mem, uint64_t *swapfree_abs_mem,
                                                   uint64_t *system_used_abs_mem, uint64_t *process_used_abs_mem,
                                                   int cgroups_version) {

    // Collecting process memory usage is the same procedure within containers too
    *process_used_abs_mem = zpath_system_get_process_mem_usage_bytes();

    if (cgroups_version == ZPN_SYSTEM_USE_CGROUP_V1) {
        return zpn_system_get_memory_usage_info_from_cgroups_v1(memtotal_abs_mem, memfree_abs_mem,
                                                                swaptotal_abs_mem, swapfree_abs_mem,
                                                                system_used_abs_mem);
    } else if (cgroups_version == ZPN_SYSTEM_USE_CGROUP_V2) {
        return zpn_system_get_memory_usage_info_from_cgroups_v2(memtotal_abs_mem, memfree_abs_mem,
                                                                swaptotal_abs_mem, swapfree_abs_mem,
                                                                system_used_abs_mem);
    } else {
        return ZPATH_RESULT_ERR;
    }
    return ZPATH_RESULT_NO_ERROR;
}

/*
• utility to get network stat information from /proc/net/netstat.
• listen_overflows : number of times TCP connections dropped due to listening socket queue full
• listen_drops : number of TCP connections dropped before being fully established
• tcp_loss_failures: number of TCP connections with loss failures
• tcp_lost_retransmit : number of TCP restransmissions that were lost/failed
• tcp_retrans_fail: number of TCP retransmission failures
• tcp_from_zero_window_adv: number of times TCP sender advertises zero window to receiver
• tcp_to_zero_window_adv: number of times TCP receiver advertises zero window to sender
•
Returns:
• ZPN_RESULT_NO_ERROR if the netstat information is successfully retrieved.
• ZPN_RESULT_ERR if an error occurs while reading or parsing /proc/net/netstat.
*/
int
zpn_system_get_system_network_netstat (uint64_t *listen_overflows, uint64_t *listen_drops,
                                       uint64_t *tcp_loss_failures, uint64_t *tcp_lost_retransmit,
                                       uint64_t *tcp_retrans_fail, uint64_t *tcp_from_zero_window_adv,
                                       uint64_t *tcp_to_zero_window_adv) {
#ifdef __linux__
    char buffer[BUFFER_SIZE + 1] = {'\0'} ;
    int flag = 0, counter = 0;
    uint64_t listen_overflows1, listen_drops1, tcp_loss_failures1, tcp_lost_retransmit1, tcp_retrans_fail1, tcp_from_zero_window_adv1, tcp_to_zero_window_adv1;

    FILE* file = fopen("/proc/net/netstat", "r");
    if(file == NULL){
        ZPN_LOG(AL_ERROR, " Unable to open /proc/net/netstat (%s)", strerror(errno));
        return ZPN_RESULT_ERR;
    }

    listen_overflows1 = listen_drops1 = tcp_loss_failures1 = tcp_lost_retransmit1 = tcp_retrans_fail1 = tcp_from_zero_window_adv1 = tcp_to_zero_window_adv1 = 0;

    while(fgets(buffer, sizeof(buffer), file)) {
        if(strstr(buffer,"TcpExt:")) {
            if (!flag) {
                flag = 1;
                continue;
            }
            char* token = strtok(buffer, " ");
            while (token != NULL) {
                if (counter == 19) {
                    listen_overflows1 = strtoul(token, NULL, 10);
                } else if (counter == 20) {
                    listen_drops1 = strtoul(token, NULL, 10);
                } else if (counter == 37) {
                    tcp_loss_failures1 = strtoul(token, NULL, 10);
                } else if (counter == 34) {
                    tcp_lost_retransmit1 = strtoul(token, NULL, 10);
                } else if (counter == 77) {
                    tcp_retrans_fail1 = strtoul(token, NULL, 10);
                } else if (counter == 94) {
                    tcp_from_zero_window_adv1 = strtoul(token, NULL, 10);
                } else if (counter == 95) {
                    tcp_to_zero_window_adv1 = strtoul(token, NULL, 10);
                }
                token = strtok(NULL, " ");
                counter++;
            }
            break;
        }
    }

    *listen_overflows = listen_overflows1;
    *listen_drops = listen_drops1;
    *tcp_loss_failures = tcp_loss_failures1;
    *tcp_lost_retransmit = tcp_lost_retransmit1;
    *tcp_retrans_fail = tcp_retrans_fail1;
    *tcp_from_zero_window_adv = tcp_from_zero_window_adv1;
    *tcp_to_zero_window_adv = tcp_to_zero_window_adv1;
    fclose(file);
#endif
    return ZPN_RESULT_NO_ERROR;
}

/*
• utility to get network stat information from /proc/net/snmp.
• in_receives : number of IP packets received
• in_hdr_errors : number of IP packets received with header errors
• in_addr_errors: number of IP pcakets received with with destination invalid/couldn't be resolved
• forw_datagrams : number of IP packets forwarded to other network interface
• in_unknown_protos: number of IP packets received with unknown/unsupported protocol
• in_discards: number of IP packets discarded by system
• in_delivers: number of IP packets delivered successfully
• in_csum_error: number of IP packets received with checksum errors
•
Returns:
• ZPN_RESULT_NO_ERROR if the netstat information is successfully retrieved.
• ZPN_RESULT_ERR if an error occurs while reading or parsing /proc/net/snmp.
*/
int
zpn_system_get_system_network_snmp(uint64_t *in_receives, uint64_t *in_hdr_errors,
                                   uint64_t *in_addr_errors, uint64_t *forw_datagrams,
                                   uint64_t *in_unknown_protos, uint64_t *in_discards,
                                   uint64_t *in_delivers, uint64_t *in_csum_error) {
#ifdef __linux__
    char buffer[BUFFER_SIZE + 1] = {'\0'} ;
    int flag1 = 0, flag2 = 0, counter1 = 0, counter2 = 0;
    uint64_t in_receives1, in_hdr_errors1, in_addr_errors1, forw_datagrams1, in_unknown_protos1, in_discards1, in_delivers1, in_csum_error1;

    FILE* file = fopen("/proc/net/snmp", "r");
    if(file == NULL){
        ZPN_LOG(AL_ERROR, " Unable to open /proc/net/snmp (%s)", strerror(errno));
        return ZPN_RESULT_ERR;
    }

    in_receives1 = in_hdr_errors1 = in_addr_errors1 = forw_datagrams1 = in_unknown_protos1 = in_discards1 = in_delivers1 = in_csum_error1 = 0;

    while(fgets(buffer, sizeof(buffer), file)) {
        if (strstr(buffer, "Ip:")) {
            if (!flag1) {
                flag1 = 1;
            } else {
                char* token = strtok(buffer, " ");
                while(token != NULL) {
                    if (counter1 == 3) {
                        in_receives1 = strtoul(token, NULL, 10);
                    } else if (counter1 == 4) {
                        in_hdr_errors1 = strtoul(token, NULL, 10);
                    } else if (counter1 == 5) {
                        in_addr_errors1 = strtoul(token, NULL, 10);
                    } else if (counter1 == 6) {
                        forw_datagrams1 = strtoul(token, NULL, 10);
                    } else if (counter1 == 7) {
                        in_unknown_protos1 = strtoul(token, NULL, 10);
                    } else if (counter1 == 8) {
                        in_discards1 = strtoul(token, NULL, 10);
                    } else if (counter1 == 9) {
                        in_delivers1 = strtoul(token, NULL, 10);
                    }
                    token = strtok(NULL, " ");
                    counter1++;
                }
            }
        } else if (strstr(buffer, "Tcp:")) {
            if (!flag2) {
                flag2 = 1;
            } else {
                char* token = strtok(buffer, " ");
                while(token != NULL){
                    if (counter2 == 15) {
                        in_csum_error1 = strtoul(token, NULL, 10);
                        break;
                    }
                    token = strtok(NULL, " ");
                    counter2++;
                }
            }
        }
    }

    *in_receives = in_receives1;
    *in_hdr_errors = in_hdr_errors1;
    *in_addr_errors = in_addr_errors1;
    *forw_datagrams = forw_datagrams1;
    *in_unknown_protos = in_unknown_protos1;
    *in_discards = in_discards1;
    *in_delivers = in_delivers1;
    *in_csum_error = in_csum_error1;
    fclose(file);
#endif
    return ZPN_RESULT_NO_ERROR;
}

/*
• utility to get memory usage information in KB from /proc/meminfo.
• memtotal_abs_mem : total usable RAM
• memfree_abs_mem : free RAM (the memory which is not used for anything at all)
• swaptotal_abs_mem: total amount of swap space available in the system
• swapfree_abs_mem : unused swap space
• system_used_abs_mem: Total used memory by the whole system
• process_used_abs_mem: Total memory used by the process
•
Returns:
• ZPN_RESULT_NO_ERROR if the memory usage information is successfully retrieved.
• ZPN_RESULT_ERR if an error occurs while reading or parsing /proc/meminfo.
*/
int
zpn_system_get_memory_usage_info (uint64_t *memtotal_abs_mem, uint64_t* memfree_abs_mem,
                                  uint64_t *swaptotal_abs_mem, uint64_t *swapfree_abs_mem,
                                  uint64_t *system_used_abs_mem, uint64_t *process_used_abs_mem) {
#ifdef __linux__
    int fd = -1;
    char buffer[BUFFER_SIZE + 1] = {'\0'} ;
    int len;
    uint64_t MemTotal, SwapTotal, MemFree, SwapFree, Cached, SReclaimable, Buffers;
    uint64_t total, cache, buffer_and_cache, free, process_total_bytes;

    process_total_bytes = zpath_system_get_process_mem_usage_bytes();

    fd = open("/proc/meminfo", O_RDONLY);
    if(fd == -1){
        ZPN_LOG(AL_ERROR, " Unable to open /proc/meminfo (%s)", strerror(errno));
        return ZPN_RESULT_ERR;
    }

    MemTotal = SwapTotal = MemFree = SwapFree = Cached = SReclaimable = Buffers = 0;
    total = 0;
    free = 0;
    buffer_and_cache = 0;
    cache = 0;
    len = 0;
    lseek(fd, 0, SEEK_SET);
    if ((len = read(fd, buffer, sizeof(buffer)-1)) > 0)
    {
        char *ptr = buffer;
        buffer[len] = '\0';

        /* Parse the meminfo file */
        while (*ptr != '\0') {
            char line[LINUX_LINE_SIZE];
            int i = 0;

            memset(line, 0, LINUX_LINE_SIZE);

            while ((*ptr != '\n') && (i < (LINUX_LINE_SIZE - 1))) {
                line[i] = *ptr;
                i++;
                ptr++;
            }
            line[i] = '\0';

            if (!strncmp(line, "MemTotal", strlen("MemTotal"))) {
                if(sscanf(line, "MemTotal: %" SCNu64, &MemTotal) != 1){
                    ZPN_LOG(AL_ERROR, "Failed to parse MemTotal");
                    close(fd);
                    return ZPN_RESULT_ERR;
                }
            }

            if (!strncmp(line, "SwapTotal", strlen("SwapTotal"))) {
                if(sscanf(line, "SwapTotal: %" SCNu64, &SwapTotal) != 1){
                    ZPN_LOG(AL_ERROR, "Failed to parse SwapTotal");
                    close(fd);
                    return ZPN_RESULT_ERR;
                }
            }

            if (!strncmp(line, "MemFree", strlen("MemFree"))) {
                if(sscanf(line, "MemFree: %" SCNu64, &MemFree) != 1){
                    ZPN_LOG(AL_ERROR, "Failed to parse MemFree");
                    close(fd);
                    return ZPN_RESULT_ERR;
                }
            }

            if (!strncmp(line, "SwapFree", strlen("SwapFree"))) {
                if(sscanf(line, "SwapFree: %" SCNu64, &SwapFree) != 1){
                    ZPN_LOG(AL_ERROR, "Failed to parse SwapFree");
                    close(fd);
                    return ZPN_RESULT_ERR;
                }
            }

            if (!strncmp(line, "Cached", strlen("Cached"))) {
                if(sscanf(line, "Cached: %" SCNu64, &Cached) != 1){
                    ZPN_LOG(AL_ERROR, "Failed to parse Cached");
                    close(fd);
                    return ZPN_RESULT_ERR;
                }
            }

            if (!strncmp(line, "SReclaimable", strlen("SReclaimable"))) {
                if(sscanf(line, "SReclaimable: %" SCNu64, &SReclaimable) != 1){
                    ZPN_LOG(AL_ERROR, "Failed to parse SReclaimable");
                    close(fd);
                    return ZPN_RESULT_ERR;
                }
            }

            if (!strncmp(line, "Buffers", strlen("Buffers"))) {
                if(sscanf(line, "Buffers: %" SCNu64, &Buffers) != 1){
                    ZPN_LOG(AL_ERROR, "Failed to parse Buffers");
                    close(fd);
                    return ZPN_RESULT_ERR;
                }
            }
            /* Skip the '\n' */
            ptr++;
        }

        total = MemTotal + SwapTotal;
        cache = Cached + SReclaimable;
        buffer_and_cache = Buffers + cache;
        free = MemFree + SwapFree;

        /*absolute memory usage info in KB*/
        *memtotal_abs_mem = MemTotal;
        *memfree_abs_mem = MemFree;
        *swaptotal_abs_mem = SwapTotal;
        *swapfree_abs_mem = SwapFree;
        *system_used_abs_mem = total - (free + buffer_and_cache);
        *process_used_abs_mem = process_total_bytes;
    }
    close(fd);
#endif
    return ZPN_RESULT_NO_ERROR;
}

int
zpn_system_get_memory_usage_info_from_cgroups(uint64_t *memtotal_abs_mem, uint64_t* memfree_abs_mem,
                                              uint64_t *swaptotal_abs_mem, uint64_t *swapfree_abs_mem,
                                              uint64_t *system_used_abs_mem, uint64_t *process_used_abs_mem,
                                              int cgroups_version) {

    int ret;
    ret = zpn_system_get_memory_usage_info_from_cgroups_impl(memtotal_abs_mem, memfree_abs_mem,
                                                           swaptotal_abs_mem, swapfree_abs_mem,
                                                           system_used_abs_mem, process_used_abs_mem,
                                                           cgroups_version);
    if (!ret) {
        return ret;
    }

    return zpn_system_get_memory_usage_info(memtotal_abs_mem, memfree_abs_mem,
                                            swaptotal_abs_mem, swapfree_abs_mem,
                                            system_used_abs_mem, process_used_abs_mem);
}
