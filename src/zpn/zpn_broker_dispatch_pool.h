/*
 * zpn_broker_dispatch_pool.h. Copyright (C) 2018 Zscaler Inc. All Rights Reserved
 */

#ifndef __ZPN_BROKER_DISPATCH_POOL_H__
#define __ZPN_BROKER_DISPATCH_POOL_H__

#include "fohh/fohh.h"
#include "zpath_lib/zpath_instance.h"
#include "zpn/zpn_lib.h"

/*
 * Some constants...
 */
#define MAX_DISPATCHER_BACKOFF_S 10 /* Maximum amount of time we will back off a connection to dispatcher */
#define MAX_POOLS                100
#define MAX_DISPATCHERS_PER_POOL 20
#define MAX_DISPATCHERS          (MAX_POOLS * MAX_DISPATCHERS_PER_POOL)

#define DEFAULT_NEXT_POOL_INTERVAL_S     (30*60)           /* 30 minutes for health to decay */
#define NEXT_POOL_INTERVAL_S     next_pool_interval_s
#define NEXT_POOL_T1             (next_pools_created_s + (NEXT_POOL_INTERVAL_S * 1))
#define NEXT_POOL_T5             (next_pools_created_s + (NEXT_POOL_INTERVAL_S * 5))
#define NEXT_POOL_PHASE(now_s)   (((now_s) - next_pools_created_s) / NEXT_POOL_INTERVAL_S)
#define NEXT_POOL_MAX_TIME       (NEXT_POOL_INTERVAL_S * 6)
#define BEST_DISPATCHER_SELECTION_QUEUE_DEPTH_THRESHOLD 1000
#define DISPATCHER_COLOCATION_DISTANCE_THRESHOLD 10
#define MAX_CHANNELS_PER_DISPATCHER 5
#define BRK_DSP_STATS_INTERVAL_S (5*60) /* 5 min */

/*
 * If broker doesn't receive response from dsp, brk will send zpn_dispatcher_status
 * request every 1 min.
 *
 * If first response is NOT_READY from dsp, dsp needs to automatically send the READY
 * response after 6 min. If brk doesn't receive response after 7 min (6 + 1 min extra buffer),
 * brk will send zpn_dispatcher_status request again every 1 min.
 */
#define DSP_STATUS_REQUEST_RETRY_INTERVAL_S 60
#define DSP_NOT_READY_STATUS_RESPONSE_MAX_WAIT_S (7*60)


extern int64_t next_pool_interval_s;

enum zpdp_pool_state
    {
     pool_state_no_pools = 0,     // No pools
     pool_state_unchanged,        // No change (ignoring custom pool set update)
     pool_state_new_pool,         // We have a new pool, either original or next
     pool_state_simple_update,    // There has been a state change that doesn't affect pool count
     pool_state_complete          // The new pool has been made the current pool
    };

struct zpn_broker_dispatcher_channel {
    uint8_t id;
    struct fohh_connection *f_conn;
    struct zpn_broker_dispatcher *dispatcher;
    uint8_t connected;
};

/*
 * Stats for broker - dispatcher communication.
 */
struct zpn_broker_dispatcher_stats {                                            /* _ARGO: object_definition */
    int64_t zpn_brk_req_sent_total;                                             /* _ARGO: integer */

    /* Re-dispatch to same dispatcher case. */
    int64_t zpn_brk_req_redispatch_app_in_learn_mode;                           /* _ARGO: integer */
    int64_t zpn_brk_req_redispatch_app_retry;                                   /* _ARGO: integer */
    int64_t zpn_brk_req_redispatch_sticky_changed;                              /* _ARGO: integer */

    /* Re-dispatch to different DC dispatcher case. */
    int64_t zpn_brk_req_redispatch_no_connector;                                /* _ARGO: integer */
    int64_t zpn_brk_req_redispatch_invalid_domain;                              /* _ARGO: integer */

    /* Re-dispatch to different DC dispatcher success case. */
    int64_t zpn_brk_req_redispatch_no_connector_success;                        /* _ARGO: integer */
    int64_t zpn_brk_req_redispatch_invalid_domain_success;                      /* _ARGO: integer */

    /* Application multi match errors due to most specific app instead of matched application segment. */
    int64_t zpn_brk_req_app_multi_match_no_connector;                           /* _ARGO: integer */
    int64_t zpn_brk_req_sipa_no_connector;                                      /* _ARGO: integer */
    int64_t zpn_brk_req_app_multi_match_invalid_domain;                         /* _ARGO: integer */
    int64_t zpn_brk_req_sipa_invalid_domain;                                    /* _ARGO: integer */

    /* Re-dispatch to different DC failure case (when no dispatcher is ready in different DC). */
    int64_t zpn_brk_req_redispatch_failed_no_disp_ready_in_diff_dc;             /* _ARGO: integer */

    int64_t zpn_brk_req_send_fail;                                              /* _ARGO: integer */
    int64_t zpn_brk_req_send_fail_no_disp;                                      /* _ARGO: integer */
    int64_t zpn_brk_ack_send_fail;                                              /* _ARGO: integer */
    int64_t zpn_brk_ack_send_fail_bad_param;                                    /* _ARGO: integer */
    int64_t zpn_brk_ack_send_fail_no_disp;                                      /* _ARGO: integer */
    int64_t zpn_brk_c2c_msg_send_fail;                                          /* _ARGO: integer */
    int64_t zpn_brk_c2c_msg_send_disp_fail;                                     /* _ARGO: integer */
    int64_t zpn_brk_req_ack_err_sent;                                           /* _ARGO: integer */
    int64_t zpn_brk_req_ack_err_send_fail_bad_param;                            /* _ARGO: integer */
    int64_t zpn_brk_req_ack_err_send_fail;                                      /* _ARGO: integer */

    /* Number of times we re-dispatched to different DC dispatcher because first dispatcher didn't respond within 2 sec. */
    int64_t zpn_brk_req_redispatch_timeout;                                     /* _ARGO: integer */
    /* We re-dispatched to diff DC due to timeout and we received route info. */
    int64_t zpn_brk_req_redispatch_timeout_success;                             /* _ARGO: integer */
    /* We re-dispatched to diff DC due to timeout and we received route info from first dispatcher. */
    int64_t zpn_brk_req_redispatch_timeout_first_disp_success;                  /* _ARGO: integer */
    /* We re-dispatched to diff DC due to timeout and we received route info from second dispatcher. */
    int64_t zpn_brk_req_redispatch_timeout_second_disp_success;                 /* _ARGO: integer */
    /*
     * Number of times later bind req discarded because we received 2 bind req
     * in case of re-dispatch to diff DC due to timeout.
     */
    int64_t zpn_brk_req_redispatch_timeout_bind_discard;                        /* _ARGO: integer */
    /*
     * Number of times we didn't act upon the error received from dispatcher in
     * timeout retry case because either we were waiting for the response from
     * 2nd dispatcher or another dispatcher already provided the route info.
     */
    int64_t zpn_brk_req_redispatch_timeout_error_discard;                       /* _ARGO: integer */

    /* Re-dispatch to different DC dispatcher app access success case. */
    int64_t zpn_brk_req_redispatch_no_connector_app_access_success;             /* _ARGO: integer */
    int64_t zpn_brk_req_redispatch_invalid_domain_app_access_success;           /* _ARGO: integer */

    /* We re-dispatched to diff DC due to timeout and we received bind request. */
    int64_t zpn_brk_req_redispatch_timeout_app_access_success;                  /* _ARGO: integer */
    /* We re-dispatched to diff DC due to timeout and we received bind request because of first dispatcher. */
    int64_t zpn_brk_req_redispatch_timeout_first_disp_app_access_success;       /* _ARGO: integer */
    /* We re-dispatched to diff DC due to timeout and we received bind request because of second dispatcher. */
    int64_t zpn_brk_req_redispatch_timeout_second_disp_app_access_success;      /* _ARGO: integer */

    int64_t zpn_brk_req_response_received;                                      /* _ARGO: integer */

    /*
     * Primary dispatcher didn't respond within 2 sec, so we re-dispatched to another dispatcher in diff DC,
     * second dispatcher also didn't respond within 2 sec.
     */
    int64_t zpn_brk_req_redispatch_second_disp_timeout;                         /* _ARGO: integer */

    /* In case of timeout redispatch - both primary and secondary dispatcher didn't respond at all for 30 sec and mtunnel got timed out. */
    int64_t zpn_brk_req_redispatch_timeout_no_response;                         /* _ARGO: integer */

    /* Broker - dispatcher circuit breaker state related stats. Each counter denotes the number of dispatchers in that state. */
    int64_t ckt_breaker_state_close;                                            /* _ARGO: integer */
    int64_t ckt_breaker_state_partial_open_10;                                  /* _ARGO: integer */
    int64_t ckt_breaker_state_partial_open_20;                                  /* _ARGO: integer */
    int64_t ckt_breaker_state_partial_open_40;                                  /* _ARGO: integer */
    int64_t ckt_breaker_state_open;                                             /* _ARGO: integer */
    int64_t ckt_breaker_state_partial_open_90;                                  /* _ARGO: integer */
    int64_t ckt_breaker_state_partial_open_80;                                  /* _ARGO: integer */
    int64_t ckt_breaker_state_partial_open_60;                                  /* _ARGO: integer */
    /* Number of times circuit breaker implementation failed because both primary and secondary are timing out. */
    int64_t ckt_breaker_fail_both_timeout;                                      /* _ARGO: integer */
    /* This is incremented for secondary dispatcher when circuit breaker failed as secondary is timing out. */
    int64_t ckt_breaker_fail_secondary_timeout;                                 /* _ARGO: integer */
    /* Number of times circuit breaker failed because there is no secondary dispatcher. */
    int64_t ckt_breaker_fail_no_secondary_dsp;                                  /* _ARGO: integer */
    int64_t ckt_breaker_zpn_brk_req_sent;                                       /* _ARGO: integer */
    int64_t ckt_breaker_dns_req_sent;                                           /* _ARGO: integer */
    int64_t ckt_breaker_c2c_app_chk_req_sent;                                   /* _ARGO: integer */

    int64_t dns_req_sent_total;                                                 /* _ARGO: integer */
    int64_t dns_req_timeout;                                                    /* _ARGO: integer */

    /*
     * Number of times broker re-sent zpn_dispatcher_status to dsp
     * because broker didn't receive any response from dsp.
     */
    int64_t dsp_status_no_resp_retry;                                           /* _ARGO: integer */
    /*
     * Number of times broker re-sent zpn_dispatcher_status to dsp
     * because firstly dsp said NOT_READY and no response received
     * after that till 7 min.
     */
    int64_t dsp_status_not_ready_retry;                                         /* _ARGO: integer */
};

/*
 * Circuit breaker state for broker - dispatcher connection.
 *
 * brk_dsp_circuit_breaker_state_close - 100% traffic is going to current primary dispatcher (Dispatcher 1).
 *
 * brk_dsp_circuit_breaker_state_partial_open_X - Shift X % traffic to next best dispatcher,
 * Complete X % route and c2c check requests will go to Dispatcher 2. DNS requests will be
 * split across multiple other dispatchers (Dispatcher 2, Dispatcher 3, ...).
 *
 * brk_dsp_circuit_breaker_state_open - No traffic will be sent to Dispatcher 1. All broadcast
 * messages will still go through.
 */
enum zpn_brk_dsp_circuit_breaker_state {
    brk_dsp_circuit_breaker_state_close = 0,
    brk_dsp_circuit_breaker_state_partial_open_10,
    brk_dsp_circuit_breaker_state_partial_open_20,
    brk_dsp_circuit_breaker_state_partial_open_40,
    brk_dsp_circuit_breaker_state_open,
    brk_dsp_circuit_breaker_state_partial_open_90, // Used while recovery
    brk_dsp_circuit_breaker_state_partial_open_80, // Used while recovery
    brk_dsp_circuit_breaker_state_partial_open_60, // Used while recovery
};

enum dsp_status {
    dsp_status_not_received = 0,
    dsp_status_not_ready,
    dsp_status_ready
};

/*
 * Dispatchers NEVER disappear. They might become inactive, but they
 * never disappear.
 *
 * Dispatchers can be inactive if:
 *  - Configured inactive in instance table.
 *  - They cannot be reached via FOHH at the moment.
 */
struct zpn_broker_dispatcher {
    int64_t instance_id;

    char *domain_name;
    char *new_domain_name;
    double lat;
    double lon;

    double distance;

    /* The epoch at which the dispatcher started. If 0, we don't know yet */
    int64_t epoch_started;
    int ready;
    int instance_run;
    int instance_active;

    struct zpn_broker_dispatcher_channel channels[MAX_CHANNELS_PER_DISPATCHER];
    uint8_t channel_count;
    uint8_t channel_connected_count;
    uint64_t channel_idx;

    char *pool_name;
    int block_app_reg_msg;

    struct zpn_broker_dispatcher_stats stats;
    struct argo_log_registered_structure *stats_argo;

    /*
     * exported_stats is the copy of the zpn_broker_dispatcher_stats object
     * which is exported to the cloud. This copy is made to calculate the delta
     * between current and previous stats.
     */
    struct zpn_broker_dispatcher_stats exported_stats;
    /* Circuit breaker state */
    enum zpn_brk_dsp_circuit_breaker_state brk_dsp_circuit_breaker_state;
    zpath_rwlock_t brk_dsp_circuit_breaker_state_lock;
    /* To decided if the request will go to primary or secondary dispatcher in case of open/partially open circuit. */
    uint64_t request_idx;
    /* To decided if the DNS request will go to primary or secondary dispatcher in case of open/partially open circuit. */
    uint64_t dns_request_idx;
    /* To decide the next best dispatcher to send the DNS request in case of open/partially open circuit. */
    uint64_t dns_request_next_dsp_idx;

    int64_t last_dsp_status_sent_s;
    int64_t last_dsp_status_rcvd_s;
    int is_old_dsp;
    zpath_mutex_t status_lock;
};

/*
 * Dispatcher pool.
 *
 * Static once created. (At least until it's freed. But it is
 * slow-freed...)
 */
struct zpn_broker_dispatcher_pool {
    char *pool_name;

    /* SORTED set of pool members- sorted by DISTANCE. ONLY active
     * dispatchers allowed. Note that inactive dispatchers may
     * transiently be in here, but it doesn't last long. */
    struct zpn_broker_dispatcher *dispatchers[MAX_DISPATCHERS_PER_POOL];
    size_t dispatcher_count;
};

/*
 * A set of pools at some moment in time...
 *
 * Static once created.
 */
struct zpn_broker_dispatcher_pool_set {
    int64_t creation_epoch_s;
    struct zhash_table *pools_by_name;
    struct zpn_broker_dispatcher_pool pools[MAX_POOLS];
    size_t pool_count;
};

/*
 * Get all current dispatchers. Ignores pools.
 */
int zpn_broker_dispatcher_all(struct zpn_broker_dispatcher **dispatchers, size_t *dispatcher_count);

/*
 * Get all dispatchers for a pool given 'that which we are hashing'
 */
int zpn_broker_dispatcher_pool_all(int64_t key, struct zpn_broker_dispatcher **dispatchers, size_t *dispatcher_count);

// Check if pool name for a given key matches with a given pool_name.
int zpn_broker_dispatcher_pool_name_match(int64_t key, const char *pool_name);

/*
 * Get closest connected dispatcher
 *
 * Either key or dsp_pool is needed. If both provided, best dispatcher
 * from given dsp_pool will be returned.
 */
struct zpn_broker_dispatcher *zpn_broker_dispatcher_best(int64_t key,
                                                         enum zpn_err_brk_req last_disp_error,
                                                         int64_t last_disp_id,
                                                         struct zpn_broker_dispatcher_pool *dsp_pool);

/*
 * Thread for watching dispatchers coming and going...
 */
void *zpn_broker_dispatcher_watch_thread(struct zthread_info *zthread_arg, void *cookie);

/*
 * Dump current pool state
 */
void zpn_broker_dispatch_pool_dump_state(char *out_buf, size_t out_buf_size, int do_json);

void zpn_broker_dispatch_pool_query(int64_t gid, char *buf, size_t buf_size);

/*
 * Init
 */
void zpn_broker_dispatch_pool_init(void);

/* Add a custom customer */
int zpn_broker_dispatch_pool_add_custom_customer(int64_t customer_gid, const char *pool_name);
/* Remove a custom customer */
int zpn_broker_dispatch_pool_remove_custom_customer(int64_t customer_gid);

/* Does round robin between dispatcher channels */
struct fohh_connection *zpn_broker_dispatcher_channel_select(struct zpn_broker_dispatcher *dsp);

/*
 * For testing only:
 */
void zpn_broker_dispatcher_test_set_pools(struct zpn_broker_dispatcher_pool_set *current, struct zpn_broker_dispatcher_pool_set *next);
void zpn_broker_dispatcher_test_set_created(int64_t when_s);
enum zpdp_pool_state zpn_broker_dispatcher_check_pool_change(double local_lat, double local_lon, int test_mode);
extern int64_t zpdp_test_current_sequence;
extern struct zpath_instance zpdp_test_instances[100];
extern size_t zpdp_test_instances_count;
char *zpn_broker_dispatch_pool_get_pool_name_from_customer_gid(int64_t customer_gid);
int zpn_broker_dispatcher_is_ready(struct zpn_broker_dispatcher *dispatcher);

/*
 * For dev/qa envs, pins to specic dispatcher in pools
 */
void pin_test_dispatcher_name( const char* disp_name);
struct zpn_broker_dispatcher *get_pinned_dispatcher();

/* Evaluate and set the circuit breaker state for each dispatcher and update the stats. */
void zpn_brk_dsp_circuit_breaker_check(struct zpn_broker_dispatcher_stats *stats);

/* Set the circuit breaker state for the given dispatcher. */
void zpn_brk_dsp_circuit_breaker_set_state(struct zpn_broker_dispatcher *dispatcher,
                                           enum zpn_brk_dsp_circuit_breaker_state state);

/* Get the circuit breaker state for the given dispatcher. */
enum zpn_brk_dsp_circuit_breaker_state zpn_brk_dsp_circuit_breaker_get_state(struct zpn_broker_dispatcher *dispatcher);

/* Get best dispatcher to send request considering circuit breaker state. */
struct zpn_broker_dispatcher *zpn_brk_dsp_circuit_breaker_get_best_dsp(int64_t customer_gid,
                                                                       enum zpn_err_brk_req last_disp_error,
                                                                       int64_t last_disp_id,
                                                                       int *is_ckt_breaker);

/* Get best dispatcher to send DNS request considering circuit breaker state. */
struct zpn_broker_dispatcher *zpn_brk_dsp_circuit_breaker_get_best_dsp_for_dns(int64_t customer_gid, int *is_ckt_breaker);

/* Get circuit breaker state in the string format. */
const char* zpn_brk_dsp_circuit_breaker_state_get_str(enum zpn_brk_dsp_circuit_breaker_state state);

/* Get percent open of circuit for the given circuit breaker state. */
int zpn_brk_dsp_circuit_breaker_get_open_percent(enum zpn_brk_dsp_circuit_breaker_state state);

size_t zpn_brk_dsp_circuit_breaker_get_state_cnt();
void zpn_brk_dsp_circuit_breaker_config_override_monitor_init();

/* Return 1 if the broker - dispatcher circuit breaker feature is enabled, otherwise 0. */
int zpn_brk_dsp_circuit_breaker_is_enabled();

/* To set the zevent base of broker misc thread so that it could be used for brk dsp ckt breaker work. */
void zpn_broker_dispatch_brk_misc_zevent_base_set(struct zevent_base *zbase);

void zpn_brk_dsp_circuit_breaker_test_set();

void zpn_broker_dispatcher_stats_obj_set(struct zpn_broker_dispatcher_stats *stats);

#endif /* __ZPN_BROKER_DISPATCH_POOL_H__ */
