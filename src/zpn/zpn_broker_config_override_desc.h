/*
 * zpn_broker_config_override_desc.h. Copyright (C) 2023-2024 Zscaler Inc, All Rights Reserved
 */

#ifndef _ZPN_BROKER_CONFIG_OVERRIDE_DESC_H_
#define _ZPN_BROKER_CONFIG_OVERRIDE_DESC_H_

#define CONFIG_FEATURE_ALT_CLOUD_DESC                           "feature flag to enable or disable support for alt cloud"
#define DNS_TXT_QUERY_SUPPORT_FEATURE_DESC                      "feature flag to enable or disable support for DNS TXT queries"
#define ZIA_INSPECTION_DISABLE_DESC                             "feature flag to disable support for ZIA Inspection"
#define DTLS_FEATURE_DESC                                       "enable/disable dtls connection for data path"
#define DTLS_FEATURE_CLIENT_DESC                                "feature flag to enable or disable the support of dtls client connection for data path"
#define FOHH_CONNECTION_SETUP_TIMEOUT_DESC                      "timeout option to delete fohh connections that doesn't progress to connected state"
#define BROKER_CLIENT_AUTH_TIMEOUT_DESC                         "timeout option to delete client connection that doesn't complete authentication within the configured time"
#define BROKER_CLIENT_IP_ANCHOR_RX_DATA_TIMEOUT_DESC            "timeout option to delete client ip anchor connection which stay idle for the configured time"
#define BROKER_DISABLE_FC_MTR_DESC                              "to disable flow control for mtr probes"
#define DTLS_FEATURE_MTU_DESC                                   "customize interface MTU for dtls connection"
#define DTLS_CLIENT_MTU_DESC                                    "override the dtls clients session mtu at the customer level."
#define RATE_LIMIT_STATUS_FEATURE_DESC                          "feature flag to enable or disable the support of Broker Client Rate Limiting"
#define BROKER_CLIENT_PATH_CACHE_MAX_SIZE_PER_DOMAIN_DESC       "feature flag to control maximum uid length"
#define BROKER_FEATURE_AUTH_STATE_LOG_DESC                      "feature flag to enable or disable the support of Broker auth state log"
#define BROKER_FEATURE_DNS_STRICT_CHECK_DESC                      "feature flag to enable or disable the support of Broker strict DNS check for type A and type AAAA"
#define BROKER_FEATURE_SIEM_QUEUE_SHRINKAGE_DESC                "feature flag to set time to wait before decreasing broker to slogger TX queue size in case of FOHH connection backoff"
#define CONFIG_FEATURE_BROKER_SEND_PENDING_MTUNNEL_END_DESC     "Public broker Send pending mtunnel_end request to a assistant if that was received from client before establishing tunnel"
#define CONFIG_FEATURE_PSE_SEND_PENDING_MTUNNEL_END_DESC        "Private broker Send pending mtunnel_end request to a assistant if that was received from client before establishing tunnel"
#define CONFIG_FEATURE_BROKER_CLIENT_DOUBLE_PRECISION_GEO_LOCATION_DESC      "Public broker to calculate double precision geographic distance to client"
#define CONFIG_FEATURE_BROKER_MALLOC_FASTBIN_DESC               "Broker Malloc Fastbin max chunk size"
#define CONFIG_FEATURE_ADAPTIVE_LOAD_MONITOR_DESC                           "Adaptive load monitoring of broker system resources cpu, memory and process fd utilisation"
#define CONFIG_FEATURE_BROKER_ADAPTIVE_LOAD_UPDATE_INTERVAL_DESC           "interval duration for reporting the adaptive load on broker"
#define CONFIG_FEATURE_BROKER_ADAPTIVE_LOAD_CPU_SPIKE_THRESHOLD_DESC        "threshold configuration to detect the spike in cpu utilisation"
#define CONFIG_FEATURE_BROKER_ADAPTIVE_LOAD_MEMORY_SPIKE_THRESHOLD_DESC     "threshold configuration to detect the spike in memory utilisation"
#define CONFIG_FEATURE_BROKER_ADAPTIVE_LOAD_PROC_FD_SPIKE_THRESHOLD_DESC    "threshold configuration to detect the spike in process fd utilisation"
#define CONFIG_FEATURE_BROKER_SLOW_MAINTENANCE_DRAIN_DESC            "feature flag to enable or disable slow client drain during maintenance"
#define CONFIG_FEATURE_BROKER_SLOW_MAINTENANCE_DRAIN_THRESHOLD_DESC  "threshold for drainable client count, above which slow drain during maintenance is done."

#define CONFIG_FEATURE_BROKER_ZPN_VERSION_CONTROL_DESC                "feature flag to enable validation of client version connecting to broker"
#define CONFIG_FEATURE_BROKER_ZPM_CONNECTION_CONFIG_DESC              "feature flag to enable/disable broker zpm connection"

#define CONFIG_FEATURE_BROKER_BASELINE_MIN_VERSION_PSE_DESC "Minimum PSE version that supports ZPN Version Control feature with automatic upgrade"

#define CONFIG_FEATURE_BROKER_BASELINE_MIN_VERSION_APPC_DESC  "Minimum APP Connector version that supports ZPN Version Control feature with automatic upgrade"

#define CONFIG_FEATURE_BROKER_DONT_DUMP_DESC                          "feature flag to control dont dump enablement"

#define BROKER_FEATURE_AUTH_LOG_REDIRECTION_STATUS_DESC                      "feature flag to enable support of Broker puting the redirect reason into the auth_log close_reason"

int zpn_broker_config_override_desc_register_all(enum Zpn_Instance_Type inst_type);
int zpn_broker_config_override_str_validator(const char *value_str);

#endif  /* _ZPN_BROKER_CONFIG_OVERRIDE_DESC_H_ */
