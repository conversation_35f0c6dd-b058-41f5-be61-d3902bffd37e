/*
 * zpn_broker_client_scim.c. Copyright (C) 2020-2024 Zscaler Inc. All Rights Reserved
 */

#include "zpn/zpn_broker_client_scim.h"

#include "zpn/zpn_broker_private.h"

#include "zpn/zpn_scim_user.h"
#include "zpn/zpn_scim_user_attribute.h"
#include "zpn/zpn_scim_user_group.h"
#include "zpn/zpn_scim_group.h"
#include "zpn/zpn_policy_engine.h"
#include "zpn/zpn_scim_attr_header.h"
#include "zpn/zpn_idp.h"
#include "zpn/zpn_rpc.h"
#include "zpath_lib/zpath_debug.h"
#include "zpn/zpn_broker_client.h"
#include "zpn/zpn_broker_client_private.h"
#include "zpn/zpn_userdb_common.h"
#include "zpath_lib/zpath_et_wally_userdb.h"
#include "zpn/zpn_broker_common.h"
#include "zpn/zpn_aae.h"
#include "zpn/zpn_aae_profile_conclusion.h"
#include "zpn/zpn_broker_client_apps.h"
#include "zpn/zpn_client_table.h"

#include "fohh/fohh.h"
#include "wally/wally.h"
#include "zpath_lib/zpath_et_wally_userdb.h"
#include "zpn/zpn_lib.h"


/*
 * This file mostly contains handling of wally callbacks
 * To do this safely we always put the c_state updates onto the thread that owns the c_state
 * This should mean that all our updates/accesses are isolated to a single thread and should not require any locking
 *
 * This is similar to how we handle any other dynamic c_state data such as Trusted Networks and Postures only it is
 * using wally callbacks instead of RPCs
 */

/* ATTENTION: Adding new enums MUST update the needed bit flags for is_np_scim_enabled and
 * is_np_scim_enabled_prev in zpn_broker_client_scim_state */
enum np_scim_group_state{
    NP_SCIM_DISABLED = 0,
    NP_SCIM_ENABLED,
    NP_SCIM_NOT_SEEN,
};

struct zpn_scim_user_attribute_strings {
    char **strs;
    size_t strs_count;
};

extern struct zpn_aae_profile_conclusion_stats zpn_aae_stats;
extern struct zpn_broker_client_stats zpn_broker_client_stats_value[FOHH_MAX_THREADS];

int zpn_broker_client_scim_register_for_scim_user_attributes(struct zpn_broker_client_fohh_state *c_state,
                                                             wally_response_callback_f *callback,
                                                             void *cookie,
                                                             int64_t int_cookie,
                                                             char *wally_debug_str);

int zpn_broker_client_scim_register_for_scim_user_group(struct zpn_broker_client_fohh_state *c_state,
                                                        wally_response_callback_f *callback,
                                                        void *cookie,
                                                        int64_t int_cookie,
                                                        char *wally_debug_str);

int zpn_broker_client_scim_register_for_scim_user(struct zpn_broker_client_fohh_state *c_state,
                                                  wally_response_callback_f *callback,
                                                  void *cookie,
                                                  int64_t int_cookie,
                                                  char *wally_debug_str);

int zpn_broker_client_scim_register_for_aae_profile_conclusion(struct zpn_broker_client_fohh_state *c_state,
                                                               wally_response_callback_f *callback,
                                                               void *cookie,
                                                               int64_t int_cookie,
                                                               char *wally_debug_str);

int zpn_scim_user_attribute_attributes_to_strings_log(struct zpn_scim_user_attribute *rows,
                                                      size_t rows_count,
                                                      struct zpn_broker_client_fohh_state *c_state);

static void zpn_broker_client_scim_user_table_complete_on_thread(struct zevent_base *base,
                                                                 void *c_state_cookie,
                                                                 int64_t int_cookie,
                                                                 void *registrant_cookie,
                                                                 void *extra_cookie2,
                                                                 void *extra_cookie3,
                                                                 int64_t request_id) {
    struct zpn_broker_client_fohh_state *c_state = c_state_cookie;

    if (!c_state) {
        ZPN_LOG(AL_WARNING, "c_state was null, cannot attach scim user attribute...");
        return;
    }

    if (c_state->incarnation != request_id || !c_state->scim_state) {
        //c_state cleaned
        return;
    }

    if (c_state->scim_state->scim_user_attribute_registrant == registrant_cookie) {
        if (!c_state->scim_state->seen_active) {
            ZPN_LOG(AL_ERROR,
                    "%s: scim attributes had no active.... we can't determine user status accurately",
                    c_state->tunnel_id);
        }
        ZPN_LOG(AL_DEBUG, "%s: got all current scim attributes", c_state->tunnel_id);
        c_state->scim_state->initial_attributes_finished = 1;

        zpn_client_tracker_end(&c_state->tracker, client_track_scim_user_attr, ZPN_RESULT_NO_ERROR);

    } else if (c_state->scim_state->scim_user_group_registrant == registrant_cookie) {
        ZPN_LOG(AL_DEBUG, "%s: got all current scim groups", c_state->tunnel_id);
        c_state->scim_state->initial_groups_finished = 1;

        zpn_client_tracker_end(&c_state->tracker, client_track_scim_user_group, ZPN_RESULT_NO_ERROR);

    } else if (c_state->scim_state->scim_user_registrant == registrant_cookie) {
        ZPN_LOG(AL_DEBUG, "%s: got scim user", c_state->tunnel_id);
        c_state->scim_state->initial_scim_user_finished = 1;

        zpn_client_tracker_end(&c_state->tracker, client_track_scim_user, ZPN_RESULT_NO_ERROR);
    } else if (c_state->scim_state->aae_conclusion &&
               c_state->scim_state->aae_conclusion_registrant == registrant_cookie) {
        ZPN_DEBUG_AAE("%s: Finished registration for the zpn_aae_profile_conclusion table", c_state->tunnel_id);

        if(!c_state->scim_state->aae_conclusion->iam_device_id ||
            c_state->scim_state->initial_aae_conclusion_finished) {
           /*
            * If no device id, just increment the stats as this is the only callback we get
            * if device id is there, we expect this to be called twice since we are checking for both user and device profile.
            */
           if (c_state->scim_state->aae_conclusion->device_profile_cnt ||
                c_state->scim_state->aae_conclusion->user_profile_cnt) {
                __sync_add_and_fetch(&(zpn_aae_stats.profile_found_for_user), 1);
            } else {
                __sync_add_and_fetch(&(zpn_aae_stats.profile_not_found_for_user), 1);
            }
            __sync_add_and_fetch(&(zpn_aae_stats.num_registration_success), 1);
        }

        c_state->scim_state->initial_aae_conclusion_finished = 1;

        zpn_client_tracker_end(&c_state->tracker, client_track_aae_profile_conclusion, ZPN_RESULT_NO_ERROR);
    } else {
        // seems improbable do nothing maybe we need to log this but likely due to a c_state reuse
    }

    //We got a callback so we need to let auth know
    //We are already on the client thread so this is legal to call directly
    c_state->scim_state->auth_callback(c_state);
}

int zpn_client_scim_table_complete_callback(void *cookie,
                                            struct wally_registrant *registrant,
                                            struct wally_table *table,
                                            int64_t request_id,
                                            int row_count) {
    struct zpn_broker_client_fohh_state *c_state = cookie;

    if (!c_state) {
        return ZPN_RESULT_NO_ERROR;
    }

    if (!c_state->scim_state || c_state->incarnation != request_id) {
        return ZPN_RESULT_NO_ERROR;
    }

    int res;
    res = zevent_base_big_call(c_state->client_thread,
                               zpn_broker_client_scim_user_table_complete_on_thread,
                               c_state,
                               0,
                               registrant,
                               NULL,
                               NULL,
                               request_id);

    if (res) {
        ZPN_LOG(AL_WARNING,
                "%s: failed to call table complete for user...",
                c_state->tunnel_id);
    }
    return ZPN_RESULT_NO_ERROR;
}

/*
 * Takes a row_id for a scim attribute row and removes any existing state
 * from the users c_state making it safe to attach any updated state for that row
 * also effectively deletes the state if no one attaches updated state
 *
 * Will handle the case in which it has not been added
 */
static void
zpn_broker_client_scim_user_attribute_clear_old_state(struct zpn_broker_client_fohh_state *c_state, int64_t row_id) {
    struct zpn_scim_user_attribute_strings *attribute_strings;
    attribute_strings = zhash_table_lookup(c_state->scim_state->attribute_strings_hash,
                                           &row_id,
                                           sizeof(row_id),
                                           NULL);

    // found so clear old state
    if (attribute_strings) {
        size_t i;
        for (i = 0; i < attribute_strings->strs_count; i++) {
            zhash_table_remove(c_state->scim_state->scim_state_hash,
                               attribute_strings->strs[i],
                               strlen(attribute_strings->strs[i]),
                               NULL);
            ZPN_BCA_FREE(attribute_strings->strs[i]);
        }
        ZPN_BCA_FREE(attribute_strings->strs);
        zhash_table_remove(c_state->scim_state->attribute_strings_hash,
                           &row_id,
                           sizeof(row_id),
                           NULL);
        ZPN_BCA_FREE(attribute_strings);
    }
}

int zpn_scim_user_attribute_attributes_to_strings_log(struct zpn_scim_user_attribute *rows,
                                                      size_t rows_count,
                                                      struct zpn_broker_client_fohh_state *c_state)
{
    int res = ZPATH_RESULT_NO_ERROR;
    char cur_attr_string[1024];
    char com_attr_string[1052];
    int length = 0;

    if(c_state->scim_user_attribute_index <  ZPN_SCIM_USER_ATTRIBUTE_PER_USER_MAX) {
       for (int attr_index = 0; attr_index < c_state->scim_user_attribute_index; attr_index++) {
           if (c_state->scim_user_attribute_gid[attr_index] == (long) rows->attribute_gid) {
               if (c_state->log_state.scim_attributes[attr_index]) {
                   ZPN_BCA_FREE(c_state->log_state.scim_attributes[attr_index]);
                   c_state->log_state.scim_attributes[attr_index] = NULL;
               }
               if(!rows->deleted) {
                   for (int cur_attribute_index = 0; cur_attribute_index < rows->attribute_values_count; cur_attribute_index++) {
                       length+= snprintf(cur_attr_string+length, ZPN_SCIM_USER_ATTR_MAX_LEN,
                               "%s,",
                               rows->attribute_values[cur_attribute_index]);
                   }
                   snprintf(com_attr_string,sizeof(com_attr_string),
                           "\"%ld\":[\"%s\"]",
                           (long) rows->attribute_gid,
                           cur_attr_string);
                   c_state->log_state.scim_attributes[attr_index] = ZPN_BCA_STRDUP(com_attr_string, strlen(com_attr_string));
               }
               else {
                   c_state->log_state.scim_attributes[attr_index] = NULL;
               }
               return res;
           }
       }
       if(!rows->deleted) {
           for (int cur_attribute_index = 0; cur_attribute_index < rows->attribute_values_count; cur_attribute_index++) {
               length+= snprintf(cur_attr_string+length, ZPN_SCIM_USER_ATTR_MAX_LEN,
                       "%s,",
                       rows->attribute_values[cur_attribute_index]);
           }
           snprintf(com_attr_string,sizeof(com_attr_string),
                   "\"%ld\":[\"%s\"]",
                   (long) rows->attribute_gid,
                   cur_attr_string);

           for (int attr_index = 0; attr_index < c_state->scim_user_attribute_index; attr_index++) {
               if(c_state->log_state.scim_attributes[attr_index] == NULL && c_state->log_state.scim_attributes_count) {
                   c_state->log_state.scim_attributes[attr_index] = ZPN_BCA_STRDUP(com_attr_string, strlen(com_attr_string));
                   c_state->scim_user_attribute_gid[attr_index] = (long) rows->attribute_gid;
                   return res;
               }
           }
           // Free memory allocated if any, before we overwrite this pointer below
           if (c_state->log_state.scim_attributes[c_state->scim_user_attribute_index]) {
                ZPN_BCA_FREE(c_state->log_state.scim_attributes[c_state->scim_user_attribute_index]);
                c_state->log_state.scim_attributes[c_state->scim_user_attribute_index] = NULL;
           }
           c_state->log_state.scim_attributes[c_state->scim_user_attribute_index] = ZPN_BCA_STRDUP(com_attr_string, strlen(com_attr_string));
           c_state->log_state.scim_attributes_count++;
           c_state->scim_user_attribute_gid[c_state->scim_user_attribute_index] = (long) rows->attribute_gid;
           c_state->scim_user_attribute_index++;
       }
    }
    return res;
}

int zpn_scim_group_to_rule_string_from_group_log(struct zpn_scim_user_group *group,
                                                 struct zpn_broker_client_fohh_state *c_state)
{
    int res = ZPATH_RESULT_NO_ERROR;
    if(c_state->scim_user_group_index < ZPN_SCIM_USER_GROUP_PER_USER_MAX) {
        for (int group_index = 0; group_index < c_state->scim_user_group_index; group_index++) {
            if (c_state->scim_user_group_gid[group_index] == (long) group->group_gid) {
                if(c_state->log_state.scim_user_group[group_index]) {
                    ZPN_BCA_FREE(c_state->log_state.scim_user_group[group_index]);
                    c_state->log_state.scim_user_group[group_index] = NULL;
                }
                char user_group_string[100];
                if(!group->deleted) {
                    sxprintf(user_group_string, user_group_string + sizeof(user_group_string),
                            "\"%ld\"",
                            (long) group->group_gid);
                    c_state->log_state.scim_user_group[group_index] = ZPN_BCA_STRDUP(user_group_string,strlen(user_group_string));
                }
                else {
                    c_state->log_state.scim_user_group[group_index] = NULL;
                }
                return res;
            }
        }
        if(!group->deleted) {
            char user_group_string[100];
            sxprintf(user_group_string, user_group_string + sizeof(user_group_string),
                    "\"%ld\"",
                    (long) group->group_gid);

            for (int group_index = 0; group_index < c_state->scim_user_group_index; group_index++) {
                if(c_state->log_state.scim_user_group[group_index] == NULL && c_state->log_state.scim_user_group_count) {
                    c_state->log_state.scim_user_group[group_index] = ZPN_BCA_STRDUP(user_group_string,strlen(user_group_string));
                    c_state->scim_user_group_gid[group_index] = (long) group->group_gid;
                    return res;
                }
            }

            // Free memory allocated if any, before we overwrite this pointer below
            if (c_state->log_state.scim_user_group[c_state->scim_user_group_index]) {
                ZPN_BCA_FREE(c_state->log_state.scim_user_group[c_state->scim_user_group_index]);
                c_state->log_state.scim_user_group[c_state->scim_user_group_index] = NULL;
            }

            c_state->log_state.scim_user_group[c_state->scim_user_group_index] = ZPN_BCA_STRDUP(user_group_string,strlen(user_group_string));
            c_state->log_state.scim_user_group_count++;
            c_state->scim_user_group_gid[c_state->scim_user_group_index] = (long) group->group_gid;
            c_state->scim_user_group_index++;
        }
    }
    return res;
}

int zpn_broker_client_scim_user_attribute_attach(struct zpn_broker_client_fohh_state *c_state,
                                                 struct zpn_scim_user_attribute *attr_row) {
    int res = ZPN_RESULT_NO_ERROR;
    struct zpn_scim_user_attribute_strings *attribute_strings = NULL;

    if (!attr_row) {
        ZPN_LOG(AL_WARNING, "%s: could not attach a scim state because the row was fully deleted?", c_state->tunnel_id);
        return ZPN_RESULT_ERR;
    }

    if (attr_row->attribute_gid == c_state->scim_state->active_attr_gid) {
        c_state->scim_state->active = !attr_row->deleted &&
                                      attr_row->attribute_values_count &&
                                      strcasecmp(attr_row->attribute_values[0], "true") == 0;
        c_state->scim_state->seen_active = 1;
        ZPATH_DEBUG_SCIM("%s: was marked as %s",
                         c_state->tunnel_id,
                         (c_state->scim_state->active ? "active" : "disabled"));
    }

    // Only add new values on row valid rows
    if (!attr_row->deleted) {
        attribute_strings = ZPN_BCA_MALLOC(sizeof(*attribute_strings));
        if (!attribute_strings) {
            ZPN_LOG(AL_ERROR, "%s: Could not attach scim attributes to c_state.. no memory", c_state->tunnel_id);
            return ZPN_RESULT_NO_MEMORY;
        }

        char **new_strs;
        new_strs = ZPN_BCA_MALLOC(sizeof(new_strs[0]) * (attr_row->attribute_values_count + 1));
        if (!new_strs) {
            ZPN_LOG(AL_ERROR, "%s: Could not attach scim attributes to c_state.. no memory", c_state->tunnel_id);
            ZPN_BCA_FREE(attribute_strings);
            return ZPN_RESULT_NO_MEMORY;
        }

        size_t string_count = attr_row->attribute_values_count;
        res = zpn_scim_user_attribute_attributes_to_strings(&attr_row, 1, new_strs, &string_count);

        if (res) {
            ZPN_LOG(AL_ERROR,
                    "%s: Could not attach scim attributes to c_state %s",
                    c_state->tunnel_id,
                    zpn_result_string(res));
            ZPN_BCA_FREE(attribute_strings);
            ZPN_BCA_FREE(new_strs);
            return ZPN_RESULT_ERR;
        }

        attribute_strings->strs_count = string_count;
        attribute_strings->strs = new_strs;

        zhash_table_store(c_state->scim_state->attribute_strings_hash,
                          &attr_row->id,
                          sizeof(attr_row->attribute_gid),
                          0,
                          attribute_strings);

        size_t i;
        for (i = 0; i < attribute_strings->strs_count; i++) {
            ZPATH_DEBUG_SCIM("%s: attaching scim attr to c_state: %s", c_state->tunnel_id, attribute_strings->strs[i]);
            zhash_table_store(c_state->scim_state->scim_state_hash,
                              attribute_strings->strs[i],
                              strlen(attribute_strings->strs[i]),
                              0,
                              c_state->scim_state->scim_state_hash);
        }
        // Update SCIM Version for policy re-evaluation
        zpn_broker_client_update_scim_version(c_state);
        zpn_broker_client_stats_value[c_state->conn_thread_id].scim_user_attribute_update++;
    }
    return res;
}

static void zpn_broker_client_scim_user_attribute_attach_row_on_thread(struct zevent_base *base,
                                                                       void *c_state_cookie,
                                                                       int64_t int_cookie,
                                                                       void *row_cookie,
                                                                       void *extra_cookie2,
                                                                       void *extra_cookie3,
                                                                       int64_t request_id) {
    int res;

    if (!row_cookie) {
        ZPN_LOG(AL_WARNING, "scim user attribute attch row called without a row");
        return;
    }
    struct argo_object *row = row_cookie;
    struct zpn_scim_user_attribute *user_attribute = row->base_structure_void;


    struct zpn_broker_client_fohh_state *c_state = c_state_cookie;
    if (!c_state) {
        ZPN_LOG(AL_WARNING, "c_state was null, cannot attach scim user attribute...");
        goto done;
    }

    if (c_state->incarnation != request_id || !c_state->scim_state) {
        //c_state cleaned
        goto done;
    }

    if (user_attribute->user_gid != c_state->scim_state->scim_user_id) {
        goto done;
    }

    // Regardless of what happens the old state is no longer valid so remove it
    zpn_broker_client_scim_user_attribute_clear_old_state(c_state, user_attribute->id);

    res = zpn_broker_client_scim_user_attribute_attach(c_state, user_attribute);

    if(zpn_is_broker_auth_state_log_feature_enabled(c_state->customer_gid)) {
        zpn_scim_user_attribute_attributes_to_strings_log(user_attribute, 1, c_state);
        zpn_client_authenticate_state_log(c_state);
    }

    if (!res && c_state->app_state) {
        zpn_broker_client_apps_update(c_state->app_state,
                                      c_state->general_context_hash,
                                      c_state->saml_enabled ? c_state->saml_hash : NULL,
                                      zpn_broker_client_scim_get_scim_state_hashed(c_state));
    }
    done:
    argo_object_release(row);
}

int zpn_broker_client_scim_user_attribute_row_callback(void *cookie,
                                                       struct wally_registrant *registrant,
                                                       struct wally_table *table,
                                                       struct argo_object *previous_row,
                                                       struct argo_object *row,
                                                       int64_t request_id) {
    int res = ZPN_RESULT_NO_ERROR;
    struct zpn_tlv *tlv = NULL;
    struct zpn_broker_client_fohh_state *c_state = cookie;

    if (!c_state) {
        //hmmmm
        return ZPN_RESULT_NO_ERROR;
    }

    zpn_lock_c_state_and_tlv(c_state);

    tlv = c_state_get_tlv(c_state);
    if (zpn_tlv_incarnation_validation(tlv, request_id) != ZPN_RESULT_NO_ERROR || !c_state->scim_state) {
        //c_state cleaned
        zpn_unlock_c_state_and_tlv(c_state);
        return ZPN_RESULT_NO_ERROR;
    }

    if (!c_state->scim_state->attribute_strings_hash) {
        ZPN_LOG(AL_ERROR, "%s: Callback for user attribute without a state hash... ", c_state->tunnel_id);
        zpn_unlock_c_state_and_tlv(c_state);
        return ZPN_RESULT_NO_ERROR;
    }

    struct zpn_scim_user_attribute *attr_row = row->base_structure_void;
    if (c_state->scim_state->scim_user_id != attr_row->user_gid) {
        //Not our row nothing to do
        zpn_unlock_c_state_and_tlv(c_state);
        return ZPN_RESULT_NO_ERROR;
    }

    struct zevent_base *client_thread = c_state->client_thread;
    char *tunnel_id = c_state->tunnel_id;

    zpn_unlock_c_state_and_tlv(c_state);

    argo_object_hold(row);
    res = zevent_base_big_call(client_thread,
                               zpn_broker_client_scim_user_attribute_attach_row_on_thread,
                               c_state,
                               0,
                               row,
                               NULL,
                               NULL,
                               request_id);

    if (res) {
        ZPN_LOG(AL_WARNING,
                "%s: failed to call async attribute update function probably leaked memory...",
                tunnel_id);
    }
    return ZPN_RESULT_NO_ERROR;

}

static int zpn_broker_client_scim_group_attach(struct zpn_broker_client_fohh_state *c_state,
                                               struct zpn_scim_user_group *user_group_row)
{
    char user_group_string[1000];
    zpn_scim_group_to_rule_string_from_group(user_group_string, sizeof(user_group_string), user_group_row);

    if (user_group_row->deleted) {
        ZPN_LOG(AL_DEBUG, "%s: removing scim user group %s from c_state", c_state->tunnel_id, user_group_string);
        zhash_table_remove(c_state->scim_state->scim_state_hash,
                           user_group_string,
                           strlen(user_group_string),
                           NULL);
        // Update SCIM Version for policy re-evaluation
        zpn_broker_client_update_scim_version(c_state);
        zpn_broker_client_stats_value[c_state->conn_thread_id].scim_user_group_update++;
    } else {
        if (!zhash_table_lookup(c_state->scim_state->scim_state_hash, user_group_string, strlen(user_group_string), NULL)) {
            ZPATH_DEBUG_SCIM("%s: adding scim user group %s to c_state", c_state->tunnel_id, user_group_string);
            zhash_table_store(c_state->scim_state->scim_state_hash,
                              user_group_string,
                              strlen(user_group_string),
                              0,
                              c_state->scim_state->scim_state_hash);
            // Update SCIM Version for policy re-evaluation
            zpn_broker_client_update_scim_version(c_state);
            zpn_broker_client_stats_value[c_state->conn_thread_id].scim_user_group_update++;
        } else {
            ZPATH_DEBUG_SCIM("%s: Entry present for scim user group %s to c_state", c_state->tunnel_id, user_group_string);
        }
    }

    if (user_group_row->group_gid == c_state->scim_state->np_scim_group_id) {
        ZPATH_DEBUG_SCIM("%s: np_scim match or scim user group %s to c_state, scim_group_gid: %"PRId64, c_state->tunnel_id, user_group_string, c_state->scim_state->np_scim_group_id);
        if (user_group_row->deleted) {
            c_state->scim_state->is_np_scim_enabled = NP_SCIM_DISABLED;
        } else {
            c_state->scim_state->is_np_scim_enabled = NP_SCIM_ENABLED;
        }
    }

    return ZPN_RESULT_NO_ERROR;
}

static void zpn_broker_client_scim_user_group_attach_row_on_thread(struct zevent_base *base,
                                                                   void *c_state_cookie,
                                                                   int64_t int_cookie,
                                                                   void *row_cookie,
                                                                   void *extra_cookie2,
                                                                   void *extra_cookie3,
                                                                   int64_t request_id) {
    int res = ZPN_RESULT_NO_ERROR;

    if (!row_cookie) {
        ZPN_LOG(AL_WARNING, "group update was called with a null row...");
        return;
    }

    struct zpn_broker_client_fohh_state *c_state = c_state_cookie;
    struct argo_object *argo_row = row_cookie;

    if (!c_state) {
        ZPN_LOG(AL_WARNING, "c_state was null, cannot attach scim user attribute...");
        goto done;
    }

    if (c_state->incarnation != request_id || !c_state->scim_state) {
        goto done;
    }

    struct zpn_scim_user_group *group = argo_row->base_structure_void;

    if (group->user_gid != c_state->scim_state->scim_user_id) {
        goto done;
    }

    res = zpn_broker_client_scim_group_attach(c_state, group);

    if(zpn_is_broker_auth_state_log_feature_enabled(c_state->customer_gid)) {
        zpn_scim_group_to_rule_string_from_group_log(group,c_state);
        zpn_client_authenticate_state_log(c_state);
    }

    if (!res && c_state->app_state) {
        zpn_broker_client_apps_update(c_state->app_state,
                                      c_state->general_context_hash,
                                      c_state->saml_enabled ? c_state->saml_hash : NULL,
                                      zpn_broker_client_scim_get_scim_state_hashed(c_state));
    }

done:
    argo_object_release(row_cookie);
}

int zpn_broker_client_scim_user_group_row_callback(void *cookie,
                                                   struct wally_registrant *registrant,
                                                   struct wally_table *table,
                                                   struct argo_object *previous_row,
                                                   struct argo_object *row,
                                                   int64_t request_id) {
    int res = ZPN_RESULT_NO_ERROR;
    struct zpn_tlv *tlv = NULL;
    struct zpn_broker_client_fohh_state *c_state = cookie;

    if (!c_state) {
        //hmmmm
        return ZPN_RESULT_NO_ERROR;
    }

    zpn_lock_c_state_and_tlv(c_state);

    tlv = c_state_get_tlv(c_state);
    if (zpn_tlv_incarnation_validation(tlv, request_id) != ZPN_RESULT_NO_ERROR || !c_state->scim_state) {
        //c_state cleaned
        zpn_unlock_c_state_and_tlv(c_state);
        return ZPN_RESULT_NO_ERROR;
    }

    //Hold onto the row for now
    struct zpn_scim_user_group *user_group_row = row->base_structure_void;
    if (c_state->scim_state->scim_user_id != user_group_row->user_gid) {
        //Not our row nothing to do
        zpn_unlock_c_state_and_tlv(c_state);
        return ZPN_RESULT_NO_ERROR;
    }

    struct zevent_base *client_thread = c_state->client_thread;
    char *tunnel_id = c_state->tunnel_id;

    zpn_unlock_c_state_and_tlv(c_state);

    argo_object_hold(row);
    res = zevent_base_big_call(client_thread,
                               zpn_broker_client_scim_user_group_attach_row_on_thread,
                               c_state,
                               0,
                               row,
                               NULL,
                               NULL,
                               request_id);

    if (res) {
        ZPN_LOG(AL_WARNING,
                "%s: failed to call async user group update function probably leaked memory...",
                tunnel_id);
        argo_object_release(row);
    }

    return ZPN_RESULT_NO_ERROR;
}

static void zpn_broker_client_scim_user_delete_on_thread(struct zevent_base *event_base,
                                                         void *c_state_cookie,
                                                         int64_t request_id) {
    struct zpn_broker_client_fohh_state *c_state = c_state_cookie;

    if (!c_state) {
        //hmmmm
        return;
    }

    if (c_state->incarnation != request_id || !c_state->scim_state) {
        //c_state cleaned or gone
        return;
    }

    c_state->scim_state->deleted = 1;

    ZPN_LOG(AL_INFO, "%s: scim user was deleted while connected their session will be terminated", c_state->tunnel_id);

    //Tickle the user but their disabled so we can just short circuit their auth state
    //Future policy will always fail at this point but we can also remove forwarding of policy here as well
    if (c_state->app_state) {
        zpn_broker_client_apps_update(c_state->app_state,
                                      c_state->general_context_hash,
                                      NULL,
                                      NULL);
    }
}

int zpn_broker_client_scim_user_row_callback(void *cookie,
                                             struct wally_registrant *registrant,
                                             struct wally_table *table,
                                             struct argo_object *previous_row,
                                             struct argo_object *row,
                                             int64_t request_id) {
    int res = ZPN_RESULT_NO_ERROR;
    struct zpn_tlv *tlv = NULL;
    struct zpn_broker_client_fohh_state *c_state = cookie;

    if (!c_state) {
        //hmmmm
        return ZPN_RESULT_NO_ERROR;
    }

    zpn_lock_c_state_and_tlv(c_state);

    tlv = c_state_get_tlv(c_state);
    if (zpn_tlv_incarnation_validation(tlv, request_id) != ZPN_RESULT_NO_ERROR || !c_state->scim_state) {
        //c_state cleaned
        zpn_unlock_c_state_and_tlv(c_state);
        return ZPN_RESULT_NO_ERROR;
    }

    struct zpn_scim_user *user_row = row->base_structure_void;

    if (c_state->scim_state->scim_user_id == user_row->id && user_row->deleted) {
        struct zevent_base *client_thread = c_state->client_thread;
        char *tunnel_id = c_state->tunnel_id;

        zpn_unlock_c_state_and_tlv(c_state);

        res = zevent_base_call(client_thread,
                               zpn_broker_client_scim_user_delete_on_thread,
                               c_state,
                               request_id);

        if (res) {
            ZPN_LOG(AL_CRITICAL,
                    "%s: failed to call async user delete function probably leaked memory...",
                    tunnel_id);
        }
    } else {
        zpn_unlock_c_state_and_tlv(c_state);
    }

    return ZPN_RESULT_NO_ERROR;
}

static int zpn_broker_client_aae_profile_update_hash(const struct zpn_aae_conclusion *aae_profile,
                                                     const struct zpn_broker_client_fohh_state *c_state,
                                                     int user_profile,
                                                     int delete)
{
    if (!c_state->is_aae_profile_feature_enabled) {
        return ZPN_RESULT_NO_ERROR;
    }

    return zpn_aae_profile_update_scim_hash(aae_profile, c_state->tunnel_id,
                                            zpn_broker_client_scim_get_scim_state_hashed(c_state), user_profile, delete);
}

/*
 * For change on thread two things to be done
 * If we get the callback for the user profile without device id - update that
 * else if we get update on the specific device id - then update that
 *
 * Update includes two things
 * 1. Updating information in the c_state
 * 2. Also updating the scim hash
 */
static void zpn_broker_client_aae_profile_conclusion_change_on_thread(struct zevent_base *event_base,
                                                        void *c_state_cookie,
                                                        int64_t deleted,
                                                        void *argo_row,
                                                        void *extra_cookie1,
                                                        void *extra_cookie2,
                                                        int64_t request_id)
{
    (void)event_base;
    (void)extra_cookie1;
    (void)extra_cookie2;

    if (!argo_row) {
        ZPN_LOG(AL_WARNING, "aae profile conclusion change on thread called without a row");
        return;
    }

    struct zpn_broker_client_fohh_state *c_state = c_state_cookie;
    struct argo_object *row = argo_row;

    if (!c_state) {
        goto DONE;
    }

    if (c_state->incarnation != request_id || !c_state->scim_state || !c_state->scim_state->aae_conclusion) {
        goto DONE;
    }

    struct zpn_aae_profile_conclusion *profile_row = row->base_structure_void;
    struct zpn_aae_conclusion *c_state_profile = c_state->scim_state->aae_conclusion;
    int something_updated = 0; // Do APPS update ONLY if something we are interested in changed
    int res = ZPN_RESULT_NO_ERROR;

    /* IAMUser ID and Device id is always stored as part of c_state - so reuse that */
    if (profile_row) {
        if (profile_row->iam_device_id == NULL) {
            res = zpn_broker_client_aae_profile_update_hash(c_state_profile, c_state, 1, 1);
            if (res) {
                ZPN_LOG(AL_ERROR, "%s: Failed to remove the AAE User Profile ID in the SCIM hash, error = %s",
                        c_state->tunnel_id, zpath_result_string(res));
                //DONT RETURN;
            }
            // Clean up the profile entries
            for (int i = 0; i < c_state_profile->user_profile_cnt && i < MAX_AAE_PROFILE_CNT; i++) {
                memset(c_state_profile->user_profile_array[i], 0, MAX_PROFILE_ID_LEN);
            }
            c_state_profile->user_profile_cnt = 0;

            ZPN_DEBUG_AAE("AAE change-on-thread User Row sequence %"PRId64", id %"PRId64", customer_gid %"PRId64", tunnel_id %s "
                          "iam_user_id_iam_device_id %s, profile_count %d, deleted %"PRId64,
                          profile_row->sequence, profile_row->id, profile_row->customer_gid, c_state->tunnel_id,
                          profile_row->iam_user_id_iam_device_id, profile_row->profile_ids_count,
                          profile_row->deleted);
            // if not deleted then add these entries
            if (profile_row->deleted == 0) {
                ZPN_DEBUG_AAE("AAE change-on-thread, adding user-profile: tunnel id %s, iam_user_id %s, user profile cnt %d",
                              c_state->tunnel_id, profile_row->iam_user_id, profile_row->profile_ids_count);
                for (int i = 0; i < profile_row->profile_ids_count && i < MAX_AAE_PROFILE_CNT; i++) {
                    snprintf(c_state_profile->user_profile_array[i],
                             MAX_PROFILE_ID_LEN, "%s",
                             profile_row->profile_ids[i]);
                    ZPN_DEBUG_AAE("AAE user-profile [%d] = %s", i, profile_row->profile_ids[i]);
                }
                if (profile_row->profile_ids_count > MAX_AAE_PROFILE_CNT) {
                    ZPN_LOG(AL_WARNING, "AAE conclusion change on thread - customer_gid %"PRId64" and tunnel id %s "
                            "for iam_user_id %s: found profiles %d, taking only allowed profiles %d",
                            c_state->customer_gid, c_state->tunnel_id, profile_row->iam_user_id,
                            profile_row->profile_ids_count, MAX_AAE_PROFILE_CNT);
                    c_state_profile->user_profile_cnt = MAX_AAE_PROFILE_CNT;
                    __sync_add_and_fetch(&(zpn_aae_stats.iam_user_id_profiles_gt_max_allowed), 1);
                } else {
                    c_state_profile->user_profile_cnt = profile_row->profile_ids_count;
                }
                if (c_state_profile->user_profile_cnt == 0) {
                    __sync_add_and_fetch(&(zpn_aae_stats.user_profile_conclusion_list_is_empty), 1);
                } else {
                    __sync_add_and_fetch(&(zpn_aae_stats.user_profile_present), 1);
                }
            }

            res = zpn_broker_client_aae_profile_update_hash(c_state_profile, c_state, 1, 0);
            if (res) {
                ZPN_LOG(AL_ERROR, "%s: Failed to store the AAE User Profile ID in the SCIM hash, error = %s",
                        c_state->tunnel_id, zpath_result_string(res));
                goto DONE;
            }
            something_updated = 1;
        } else if (profile_row->iam_device_id &&
                  (c_state->scim_state->aae_conclusion->iam_device_id) &&
                  (strcmp(c_state->scim_state->aae_conclusion->iam_device_id, profile_row->iam_device_id) == 0)) {

            res = zpn_broker_client_aae_profile_update_hash(c_state_profile, c_state, 0, 1);
            if (res) {
                ZPN_LOG(AL_ERROR, "%s: Failed to remove the AAE Device Profile ID in the SCIM hash, error = %s",
                        c_state->tunnel_id, zpath_result_string(res));
                //DONT RETURN;
            }

            // Clean up the profile entries
            for (int i = 0; i < c_state_profile->device_profile_cnt && i < MAX_AAE_PROFILE_CNT; i++) {
                memset(c_state_profile->device_profile_array[i], 0, MAX_PROFILE_ID_LEN);
            }
            c_state_profile->device_profile_cnt = 0;

            ZPN_DEBUG_AAE("AAE change-on-thread device row sequence %"PRId64", id %"PRId64", customer_gid %"PRId64", tunnel_id %s "
                          "iam_user_id_iam_device_id %s, profile_count %d, deleted %"PRId64,
                          profile_row->sequence, profile_row->id, profile_row->customer_gid, c_state->tunnel_id,
                          profile_row->iam_user_id_iam_device_id, profile_row->profile_ids_count,
                          profile_row->deleted);
            // if not deleted then add these entries
            if (profile_row->deleted == 0) {
                ZPN_DEBUG_AAE("AAE change-on-thread, adding device-profile: tunnel id %s, iam_user_id %s, iam_device_id %s, device profile cnt %d",
                c_state->tunnel_id, profile_row->iam_user_id,
                profile_row->iam_device_id, profile_row->profile_ids_count);
                for (int i = 0; i < profile_row->profile_ids_count && i < MAX_AAE_PROFILE_CNT; i++) {
                    snprintf(c_state_profile->device_profile_array[i],
                             MAX_PROFILE_ID_LEN, "%s",
                             profile_row->profile_ids[i]);
                    ZPN_DEBUG_AAE("AAE device-profile [%d] = %s", i, profile_row->profile_ids[i]);
                }
                if (profile_row->profile_ids_count > MAX_AAE_PROFILE_CNT) {
                    ZPN_LOG(AL_WARNING, "AAE conclusion change on thread - customer_gid %"PRId64" and tunnel id %s "
                            "for iam_user_id %s and iam_device_id %s: found profiles %d, taking only allowed profiles %d",
                            c_state->customer_gid, c_state->tunnel_id, profile_row->iam_user_id,
                            profile_row->iam_device_id, profile_row->profile_ids_count, MAX_AAE_PROFILE_CNT);
                    c_state_profile->device_profile_cnt = MAX_AAE_PROFILE_CNT;
                    __sync_add_and_fetch(&(zpn_aae_stats.iam_device_id_profiles_gt_max_allowed), 1);
                } else {
                    c_state_profile->device_profile_cnt = profile_row->profile_ids_count;
                }
                if(c_state_profile->device_profile_cnt == 0) {
                    __sync_add_and_fetch(&(zpn_aae_stats.device_profile_conclusion_list_is_empty), 1);
                } else {
                    __sync_add_and_fetch(&(zpn_aae_stats.deviceid_profile_present), 1);
                }
            }

            res = zpn_broker_client_aae_profile_update_hash(c_state_profile, c_state, 0, 0);
            if (res) {
                ZPN_LOG(AL_ERROR, "%s: Failed to store the AAE Device Profile ID in the SCIM hash, error = %s",
                        c_state->tunnel_id, zpath_result_string(res));
                goto DONE;
            }
            something_updated = 1;
        }
    }

    /*
     * If feature is disabled no need to update the version and do app download
     */
    if (something_updated && c_state->is_aae_profile_feature_enabled) {
        /* Increment the version only after the SCIM hash is updated */
        c_state_profile->version++;
        if (c_state->app_state) {
            zpn_broker_client_apps_update(c_state->app_state,
                                      c_state->general_context_hash,
                                      c_state->saml_enabled ? c_state->saml_hash : NULL,
                                      zpn_broker_client_scim_get_scim_state_hashed(c_state));
        }
    }

DONE:
    argo_object_release(row);
}

int zpn_broker_client_scim_aae_row_callback(void *cookie,
                                            struct wally_registrant *registrant,
                                            struct wally_table *table,
                                            struct argo_object *previous_row,
                                            struct argo_object *row,
                                            int64_t request_id)
{
    (void)registrant;
    (void)table;
    (void)previous_row;
    int res = ZPN_RESULT_NO_ERROR;
    struct zpn_broker_client_fohh_state *c_state = cookie;
    struct zpn_aae_profile_conclusion *con_row = row->base_structure_void;
    struct zpn_tlv *tlv = NULL;

    if (!c_state) {
        return ZPN_RESULT_NO_ERROR;
    }

    zpn_lock_c_state_and_tlv(c_state);

    tlv = c_state_get_tlv(c_state);
    if (zpn_tlv_incarnation_validation(tlv, request_id) != ZPN_RESULT_NO_ERROR ||
        !c_state->scim_state ||
        !c_state->scim_state->aae_conclusion) {
        //c_state cleaned
        zpn_unlock_c_state_and_tlv(c_state);
        return ZPN_RESULT_NO_ERROR;
    }

    // Only if the row belongs to the IAM user id we are interested in
    if (con_row->iam_user_id &&
        strcmp(c_state->scim_state->aae_conclusion->iam_user_id, con_row->iam_user_id) == 0) {
        struct zevent_base *client_thread = c_state->client_thread;
        char *tunnel_id = c_state->tunnel_id;

        zpn_unlock_c_state_and_tlv(c_state);
        argo_object_hold(row);
        res = zevent_base_big_call(client_thread,
                                   zpn_broker_client_aae_profile_conclusion_change_on_thread,
                                   c_state,
                                   con_row->deleted,
                                   row,
                                   NULL,
                                   NULL,
                                   request_id);
        if (res) {
            ZPN_LOG(AL_CRITICAL,
                    "tunnel_id %s: failed to call async aae profile conclusion change on thread",
                    tunnel_id);
            argo_object_release(row);
        }
    } else {
        zpn_unlock_c_state_and_tlv(c_state);
    }

    return res;
}

static int zpn_broker_client_scim_allocate_state_and_register_callbacks(struct zpn_broker_client_fohh_state *c_state,
                                                                        int64_t active_attr_gid,
                                                                        int64_t np_scim_group_id,
                                                                        wally_response_callback_f *callback,
                                                                        scim_auth_callback *auth_callback,
                                                                        void *cookie,
                                                                        int64_t int_cookie,
                                                                        const char *debug_str) {
    int res = ZPN_RESULT_NO_ERROR;

    if (c_state->scim_state) {
        zpn_broker_client_scim_state_on_cstate_free(c_state);
    }

    c_state->scim_state = ZPN_BCA_CALLOC(sizeof(*(c_state->scim_state)));

    if (!c_state->scim_state) {
        ZPN_LOG(AL_ERROR, "%s: could not allocate scim_state", debug_str);
        return ZPN_RESULT_NO_MEMORY;
    }

    c_state->scim_state->np_scim_group_id = np_scim_group_id;
    c_state->scim_state->is_np_scim_enabled = NP_SCIM_NOT_SEEN;
    c_state->scim_state->is_np_scim_enabled_prev = NP_SCIM_NOT_SEEN;
    c_state->scim_state->active_attr_gid = active_attr_gid;
    c_state->scim_state->active = 0;
    c_state->scim_state->auth_callback = auth_callback;

    int64_t zone_id;
    int64_t userdb;

    res = zpath_et_wally_userdb_get_userdb_identifier_for_customer(c_state->customer_gid, &zone_id, &userdb);
    if (res) {
        if (res != ZPATH_RESULT_ASYNCHRONOUS) {
            ZPN_LOG(AL_ERROR, "%s: zpath_et_wally_userdb_get_userdb_identifier_for_customer returned: %s for customer: %"PRId64, debug_str, zpath_result_string(res), c_state->customer_gid);
        }
        zpn_broker_client_scim_state_on_cstate_free(c_state);
        return res;
    }

    res = zpath_et_wally_userdb_identifier_hash(zone_id, userdb, &(c_state->scim_state->userdb_identifier_hash));
    if (res) {
        if (res != ZPATH_RESULT_ASYNCHRONOUS) {
            ZPN_LOG(AL_ERROR, "%s: zpath_et_wally_userdb_get_userdb_identifier_hash returned: %s for customer: %"PRId64, debug_str, zpath_result_string(res), c_state->customer_gid);
        }
        zpn_broker_client_scim_state_on_cstate_free(c_state);
        return res;
    }

    c_state->scim_state->zone = zone_id;
    c_state->scim_state->userdb = userdb;

    char idp_gid_username[ZPN_USERDB_IDP_GID_USERNAME_MAX_LEN];
    res = zpn_userdb_gen_idp_gid_username(c_state->idp_gid,
                                          c_state->user_id,
                                          idp_gid_username,
                                          sizeof(idp_gid_username));

    if (res) {
        if (res != ZPATH_RESULT_ASYNCHRONOUS) {
            ZPN_LOG(AL_ERROR, "%s: zpn_userdb_gen_idp_gid_username returned: %s for customer: %"PRId64, debug_str, zpath_result_string(res), c_state->customer_gid);
        }
        zpn_broker_client_scim_state_on_cstate_free(c_state);
        return res;
    }

    struct zpn_scim_user *user;
    size_t user_count = 1;
    res = zpn_scim_user_get_by_idp_gid_username(c_state->customer_gid,
                                                idp_gid_username,
                                                &user,
                                                &user_count,
                                                callback,
                                                cookie,
                                                int_cookie);
    if (res) {
        if (res != ZPATH_RESULT_ASYNCHRONOUS) {
            ZPN_LOG(AL_ERROR, "%s: zpn_scim_user_get_by_idp_gid_username returned: %s for idp_gid_username: %s, customer: %"PRId64, debug_str, zpath_result_string(res), idp_gid_username, c_state->customer_gid);
        }
        zpn_broker_client_scim_state_on_cstate_free(c_state);
        return res;
    }

    /* Set the AAE Profile */
    if (user &&
        user->iam_user_id &&
        strnlen(user->iam_user_id, MAX_IAM_USER_ID_LEN) > 0) {
        c_state->is_aae_profile_feature_enabled = zpn_broker_get_aae_profile_status_for_customer(c_state->customer_gid);
        c_state->scim_state->aae_conclusion = ZPN_AAE_CALLOC(sizeof(struct zpn_aae_conclusion));
        if (!c_state->scim_state->aae_conclusion) {
            ZPN_LOG(AL_ERROR, "%s: could not allocate memory for aae_conclusion", debug_str);
            zpn_broker_client_scim_state_on_cstate_free(c_state);
            return ZPN_RESULT_NO_MEMORY;
        }

        /* Get iam_device_id for a hw_serial_id */
        struct zpn_client *zpn_client = NULL;
        size_t zpn_client_count = 1;
        if (c_state->hw_serial_id) {
            res = zpn_client_get_iam_device_id(c_state->customer_gid,
                                               c_state->hw_serial_id,
                                               &zpn_client,
                                               &zpn_client_count,
                                               callback,
                                               cookie,
                                               int_cookie);
            if (res) {
                if (ZPN_RESULT_ASYNCHRONOUS != res) {
                    ZPN_LOG(AL_ERROR, "Get iam_device_id for hw_serial_id = %s, customer_gid = %"PRId64", Failed %s",
                            c_state->hw_serial_id, c_state->customer_gid, zpn_result_string(res));
                }
                zpn_broker_client_scim_state_on_cstate_free(c_state);
                return res;
            }
        }

        zpn_aae_initialize_aae_conclusion(c_state->scim_state->aae_conclusion,
                                          user->iam_user_id,
                                          (zpn_client ? zpn_client->iam_device_id : NULL));
    } else if ((user && !user->iam_user_id) ||
                (user && user->iam_user_id && strnlen(user->iam_user_id, MAX_IAM_USER_ID_LEN) == 0)) {
        __sync_add_and_fetch(&(zpn_aae_stats.iam_user_id_not_found), 1);
    }

    c_state->scim_state->attribute_strings_hash = zhash_table_alloc(&zpn_broker_client_allocator);
    if (!c_state->scim_state->attribute_strings_hash) {
        ZPN_LOG(AL_ERROR, "No memory to setup scim state");
        zpn_broker_client_scim_state_on_cstate_free(c_state);
        return ZPN_RESULT_NO_MEMORY;
    }

    c_state->scim_state->scim_state_hash = zhash_table_alloc(&zpn_broker_client_allocator);
    if (!c_state->scim_state->attribute_strings_hash) {
        ZPN_LOG(AL_ERROR, "No memory to setup scim state");
        zpn_broker_client_scim_state_on_cstate_free(c_state);
        return ZPN_RESULT_NO_MEMORY;
    }

    c_state->scim_state->debug_str = ZPN_BCA_STRDUP(debug_str, strlen(debug_str));
    if (!c_state->scim_state->debug_str) {
        ZPN_LOG(AL_ERROR, "No memory to setup scim state");
        zpn_broker_client_scim_state_on_cstate_free(c_state);
        return ZPN_RESULT_NO_MEMORY;
    }

    ZPN_LOG(AL_DEBUG, "%s: linking c_state with scim user id %"PRId64, c_state->tunnel_id, user->id);
    c_state->scim_state->scim_user_id = user->id;

    return res;
}

int zpn_broker_client_scim_start(struct zpn_broker_client_fohh_state *c_state,
                                 wally_response_callback_f *callback,
                                 scim_auth_callback *auth_callback) {
    int res = ZPN_RESULT_NO_ERROR;
    int64_t np_scim_group_id = 0;

    if (!c_state) {
        ZPN_LOG(AL_ERROR, "Unexpected lack of c_state");
        return ZPN_RESULT_ERR;
    }

    if (c_state->scim_state && c_state->scim_state->initial_setup_complete) {
        return ZPN_RESULT_NO_ERROR;
    }

    struct zpn_scim_attr_header *scim_attr_headers[SCIM_ATTR_HEADER_CUSTOMER_MAX_COUNT];
    size_t scim_attr_headers_count = SCIM_ATTR_HEADER_CUSTOMER_MAX_COUNT;

    zpn_client_tracker_start(&c_state->tracker, client_track_scim_idp);
    res = zpn_scim_attr_header_get_by_idp_gid(c_state->idp_gid,
                                              &(scim_attr_headers[0]),
                                              &scim_attr_headers_count);

    if (res == ZPN_RESULT_ASYNCHRONOUS) {
        ZPN_LOG(AL_INFO, "%s: scim_attr_header by idp returned async, that should not happen", c_state->tunnel_id);
        return res;
    }
    np_scim_group_id = zpn_scim_group_get_np_scim_group_id(c_state->idp_gid);
    zpn_client_tracker_end(&c_state->tracker, client_track_scim_idp, res);
    if (res) {
        ZPN_LOG(AL_WARNING,
                "%s: could not get scim attributes to search for Active attribute - %s",
                c_state->tunnel_id,
                zpath_result_string(res));
        return res;
    }

    size_t i;
    int64_t active_attr_gid = 0; // We need to find an attribute

    for (i = 0; i < scim_attr_headers_count; i++) {
        if (strcasecmp(scim_attr_headers[i]->name, "active") == 0) {
            active_attr_gid = scim_attr_headers[i]->gid;
            break;
        }
    }

    if (!active_attr_gid) {
        ZPN_LOG(AL_WARNING,
                "%s: customer does not have an active scim attribute, cannot apply scim policies...",
                c_state->tunnel_id);
        return ZPN_RESULT_NO_ERROR;
    }

    res = zpn_broker_client_scim_allocate_state_and_register_callbacks(c_state,
                                                                       active_attr_gid,
                                                                       np_scim_group_id,
                                                                       callback,
                                                                       auth_callback,
                                                                       c_state,
                                                                       c_state->incarnation,
                                                                       c_state->tunnel_id);

    if (res && res != ZPN_RESULT_ASYNCHRONOUS) {
        ZPN_LOG(AL_CRITICAL, "%s:  scim registration failed res = %s", c_state->tunnel_id, zpn_result_string(res));
        return res;
    } else if (res == ZPN_RESULT_ASYNCHRONOUS) {
        ZPN_LOG(AL_DEBUG, "%s: scim start is async", c_state->tunnel_id);
        return res;
    }

    res = ZPN_RESULT_NO_ERROR;
    zpn_client_tracker_start(&c_state->tracker, client_track_scim_user_attr);
    res = zpn_broker_client_scim_register_for_scim_user_attributes(c_state,
                                                                   callback,
                                                                   c_state,
                                                                   c_state->incarnation,
                                                                   c_state->tunnel_id);
    if (res && res != ZPN_RESULT_ASYNCHRONOUS) {
        /* actual completion happens in zpn_broker_client_scim_user_table_complete_on_thread */
        zpn_client_tracker_end(&c_state->tracker, client_track_scim_user_attr, res);
        ZPN_LOG(AL_WARNING,
                "%s: failed to register for scim user attribute updates - %s",
                c_state->tunnel_id,
                zpath_result_string(res));
        return res;
    }

    zpn_client_tracker_start(&c_state->tracker, client_track_scim_user_group);
    res = zpn_broker_client_scim_register_for_scim_user_group(c_state,
                                                              callback,
                                                              c_state,
                                                              c_state->incarnation,
                                                              c_state->tunnel_id);

    if (res && res != ZPN_RESULT_ASYNCHRONOUS) {
        /* actual completion happens in zpn_broker_client_scim_user_table_complete_on_thread */
        zpn_client_tracker_end(&c_state->tracker, client_track_scim_user_group, res);
        ZPN_LOG(AL_WARNING,
                "%s: failed to register for scim user group updates - %s",
                c_state->tunnel_id,
                zpath_result_string(res));
        return res;
    }

    zpn_client_tracker_start(&c_state->tracker, client_track_scim_user);
    res = zpn_broker_client_scim_register_for_scim_user(c_state,
                                                        callback,
                                                        c_state,
                                                        c_state->incarnation,
                                                        c_state->tunnel_id);

    if (res && res != ZPN_RESULT_ASYNCHRONOUS) {
        /* actual completion happens in zpn_broker_client_scim_user_table_complete_on_thread */
        zpn_client_tracker_end(&c_state->tracker, client_track_scim_user, res);
        ZPN_LOG(AL_WARNING,
                "%s: failed to register for scim user updates - %s",
                c_state->tunnel_id,
                zpath_result_string(res));
        return res;
    }

    /*
     * aae_conclusion will be null when feature is disabled
     * so aae_profile_conclusion registration will not happen
     */
    if (c_state->scim_state->aae_conclusion &&
        c_state->scim_state->aae_conclusion->iam_user_id) {
        zpn_client_tracker_start(&c_state->tracker, client_track_aae_profile_conclusion);
        res = zpn_broker_client_scim_register_for_aae_profile_conclusion(c_state,
                                                                         callback,
                                                                         c_state,
                                                                         c_state->incarnation,
                                                                         c_state->tunnel_id);
        if (res && res != ZPN_RESULT_ASYNCHRONOUS) {
            /* actual completion happens in zpn_broker_client_scim_user_table_complete_on_thread */
            zpn_client_tracker_end(&c_state->tracker, client_track_aae_profile_conclusion, res);
            __sync_add_and_fetch(&(zpn_aae_stats.num_registration_failure), 1);
            ZPN_LOG(AL_WARNING,
                    "tunnel_id %s: failed to register for aae profile conclusion "
                    " error %s for iam_user_id - %s",
                    c_state->tunnel_id,
                    zpath_result_string(res),
                    c_state->scim_state->aae_conclusion->iam_user_id);
            return res;
        }
        if (c_state->is_aae_profile_feature_enabled) {
            __sync_add_and_fetch(&(zpn_aae_stats.num_client_connects), 1);
        }
    }

    if (res == ZPN_RESULT_NO_ERROR || res == ZPN_RESULT_ASYNCHRONOUS) {
        // We made it through initialization so we don't need to do this again
        c_state->scim_state->initial_setup_complete = 1;
    }

    return res;
}


int zpn_broker_client_scim_register_for_scim_user_attributes(struct zpn_broker_client_fohh_state *c_state,
                                                             wally_response_callback_f *callback,
                                                             void *cookie,
                                                             int64_t int_cookie,
                                                             char *wally_debug_str) {
    if (!c_state->scim_state) {
        //Connection went away...
        return ZPN_RESULT_ERR;
    }

    if (c_state->scim_state->scim_user_attribute_registrant) {
        //Already registered skip
        return ZPN_RESULT_NO_ERROR;
    }

    int res;

    struct wally_table *attribute_table;
    res = zpath_et_wally_userdb_get_wally_table("zpn_scim_user_attribute", c_state->customer_gid, &attribute_table);
    if (res) {
        ZPN_LOG(AL_ERROR, "%s: Unexpected lack of table when trying to register for SCIM %s", c_state->tunnel_id,
                zpn_result_string(res));
        return ZPN_RESULT_ERR;
    }

    struct zpn_scim_user_attribute *user_attributes[ZPN_SCIM_USER_ATTRIBUTE_PER_USER_MAX];
    size_t user_attributes_count = ZPN_SCIM_USER_ATTRIBUTE_PER_USER_MAX;
    c_state->log_state.scim_attributes = ZPN_BCA_CALLOC(sizeof(*(c_state->log_state.scim_attributes)) * user_attributes_count);
    c_state->log_state.scim_attributes_count = 0;

    res = zpn_scim_user_attribute_get_by_user_gid(c_state->customer_gid,
                                                  c_state->scim_state->scim_user_id, 0,
                                                  user_attributes,
                                                  &user_attributes_count,
                                                  NULL,
                                                  NULL,
                                                  0);

    if (res == ZPN_RESULT_NO_ERROR && user_attributes_count) {
        //We had already loaded some attributes for this user so go ahead an attach them
        //New and updated attributes will come in on registrations
        size_t i;
        for (i = 0; i < user_attributes_count; i++) {
            //Likely this will never do anything but worst case only adds 1 hash lookup
            //Given we are in the startup of the c_state and in the thread, there should be no way
            //That user attributes are there to be removed. However, for first pass I am being
            //explicitly careful - rather do a slight amount more work on policy state
            //than incorrectly apply policy
            zpn_broker_client_scim_user_attribute_clear_old_state(c_state, user_attributes[i]->id);
            res = zpn_broker_client_scim_user_attribute_attach(c_state, user_attributes[i]);
            if (res) {
                return res;
            }
        }
    }

    struct wally_index_column *attribute_column;

    attribute_column = zpn_scim_user_attribute_get_user_gid_column(c_state->customer_gid);

    if (!attribute_column) {
        ZPN_LOG(AL_ERROR, "%s: Unexpected lack of column when trying to register for SCIM", c_state->tunnel_id);
        return ZPN_RESULT_ERR;
    }


    c_state->scim_state->scim_user_attribute_registrant = wally_table_create_registrant(attribute_table,
                                                                                        zpn_broker_client_scim_user_attribute_row_callback,
                                                                                        cookie,
                                                                                        wally_debug_str);

    if (!c_state->scim_state->scim_user_attribute_registrant) {
        ZPN_LOG(AL_CRITICAL, "%s: Could not create registrant for zpn_scim_attribute table for client",
                c_state->tunnel_id);
    } else {
        ZPN_DEBUG_AUTH("%s: Registering for scim_user_attribute customer %ld",
                       c_state->tunnel_id,
                       (long) c_state->customer_gid);

        res = wally_table_register_for_row(c_state->scim_state->scim_user_attribute_registrant,
                                           attribute_column,
                                           &(c_state->scim_state->scim_user_id),
                                           sizeof(c_state->scim_state->scim_user_id),
                                           int_cookie,
                                           0, /* Sequence */
                                           0, /* Atleast one */
                                           0, /* just callback */
                                           0, /* Unique registration */
                                           zpn_client_scim_table_complete_callback,
                                           cookie);

        if (res == WALLY_RESULT_ASYNCHRONOUS) {
            res = WALLY_RESULT_NO_ERROR;
        } else if (res != WALLY_RESULT_NO_ERROR) {
            ZPN_LOG(AL_CRITICAL, "registration failed");
            return res;
        } else {
            /* Already registered */
        }
    }

    return ZPN_RESULT_NO_ERROR;
}


int zpn_broker_client_scim_register_for_scim_user_group(struct zpn_broker_client_fohh_state *c_state,
                                                        wally_response_callback_f *callback,
                                                        void *cookie,
                                                        int64_t int_cookie,
                                                        char *wally_debug_str) {
    if (!c_state->scim_state) {
        //Connection went away...
        return ZPN_RESULT_ERR;
    }

    if (c_state->scim_state->scim_user_group_registrant) {
        //already registered skip
        return ZPN_RESULT_NO_ERROR;
    }

    int res;

    struct wally_table *scim_group_table;
    res = zpath_et_wally_userdb_get_wally_table("zpn_scim_user_group", c_state->customer_gid, &scim_group_table);
    if (res) {
        ZPN_LOG(AL_ERROR, "%s: Unexpected lack of table when trying to register for SCIM %s", c_state->tunnel_id,
                zpn_result_string(res));
        return ZPN_RESULT_ERR;
    }

    struct wally_index_column *scim_group_column;

    scim_group_column = zpn_scim_user_group_get_user_gid_column(c_state->customer_gid);

    if (!scim_group_column) {
        ZPN_LOG(AL_ERROR, "%s: Unexpected lack of column when trying to register for SCIM", c_state->tunnel_id);
        return ZPN_RESULT_ERR;
    }

    //TODO(blewis) may be able to get rid of this if we aren't using hardcoded fetching
    size_t user_groups_count = ZPN_SCIM_USER_GROUP_PER_USER_MAX;
    struct zpn_scim_user_group *user_groups[ZPN_SCIM_USER_GROUP_PER_USER_MAX];
    c_state->log_state.scim_user_group = ZPN_BCA_CALLOC(sizeof(*(c_state->log_state.scim_user_group)) * user_groups_count);
    c_state->log_state.scim_user_group_count = 0;
    res = zpn_scim_user_group_get_by_user_gid(c_state->customer_gid,
                                              c_state->scim_state->scim_user_id,
                                              user_groups,
                                              &user_groups_count, 0, NULL, NULL, 0);
    if (res == ZPN_RESULT_NO_ERROR) {
        //We found some already loaded scim groups for the user so we need to attach them now
        size_t i;
        for (i = 0; i < user_groups_count; i++) {
            res = zpn_broker_client_scim_group_attach(c_state, user_groups[i]);
            if (res) {
                return res;
            }
        }
    }

    c_state->scim_state->scim_user_group_registrant = wally_table_create_registrant(scim_group_table,
                                                                                    zpn_broker_client_scim_user_group_row_callback,
                                                                                    cookie,
                                                                                    wally_debug_str);

    if (!c_state->scim_state->scim_user_group_registrant) {
        ZPN_LOG(AL_CRITICAL,
                "%s: Could not create registrant for zpn_scim_user_group table for client",
                c_state->tunnel_id);
        return ZPN_RESULT_NO_MEMORY;
    } else {
        ZPN_DEBUG_AUTH("%s: Registering for scim_user_group customer %ld",
                       c_state->tunnel_id,
                       (long) c_state->customer_gid);

        res = wally_table_register_for_row(c_state->scim_state->scim_user_group_registrant,
                                           scim_group_column,
                                           &(c_state->scim_state->scim_user_id),
                                           sizeof(c_state->scim_state->scim_user_id),
                                           int_cookie,
                                           0, /* Sequence */
                                           0, /* Atleast one */
                                           0, /* just callback */
                                           0, /* Unique registration */
                                           zpn_client_scim_table_complete_callback,
                                           cookie);

        if (res == WALLY_RESULT_ASYNCHRONOUS) {
            res = WALLY_RESULT_NO_ERROR;
        } else if (res != WALLY_RESULT_NO_ERROR) {
            ZPN_LOG(AL_CRITICAL, "registration failed");
        } else {
            /* Already registered */
        }
    }
    return res;
}

int zpn_broker_client_scim_register_for_scim_user(struct zpn_broker_client_fohh_state *c_state,
                                                  wally_response_callback_f *callback,
                                                  void *cookie,
                                                  int64_t int_cookie,
                                                  char *wally_debug_str) {
    if (!c_state->scim_state) {
        //Connection went away...
        return ZPN_RESULT_ERR;
    }

    if (c_state->scim_state->scim_user_registrant) {
        //already registered skip
        return ZPN_RESULT_NO_ERROR;
    }

    int res;
    struct wally_table *scim_user_table;

    res = zpath_et_wally_userdb_get_wally_table("zpn_scim_user", c_state->customer_gid, &scim_user_table);
    if (res) {
        ZPN_LOG(AL_ERROR, "%s: Unexpected lack of table when trying to register for SCIM %s", c_state->tunnel_id,
                zpn_result_string(res));
        return ZPN_RESULT_ERR;
    }

    struct wally_index_column *scim_user_column;

    scim_user_column = zpn_scim_user_get_gid_column(c_state->customer_gid);

    if (!scim_user_column) {
        ZPN_LOG(AL_ERROR, "%s: Unexpected lack of column when trying to register for SCIM", c_state->tunnel_id);
        return ZPN_RESULT_ERR;
    }

    c_state->scim_state->scim_user_registrant = wally_table_create_registrant(scim_user_table,
                                                                              zpn_broker_client_scim_user_row_callback,
                                                                              cookie,
                                                                              wally_debug_str);

    if (!c_state->scim_state->scim_user_registrant) {
        ZPN_LOG(AL_CRITICAL,
                "%s: Could not create registrant for zpn_scim_user table for client",
                c_state->tunnel_id);
        return ZPN_RESULT_NO_MEMORY;
    } else {
        ZPN_DEBUG_AUTH("%s: Registering for scim_user customer %ld",
                       c_state->tunnel_id,
                       (long) c_state->customer_gid);

        char idp_gid_username[ZPN_USERDB_IDP_GID_USERNAME_MAX_LEN];
        res = zpn_userdb_gen_idp_gid_username(c_state->idp_gid,
                                              c_state->user_id,
                                              idp_gid_username,
                                              sizeof(idp_gid_username));
        if (res) {
            ZPN_LOG(AL_CRITICAL,
                    "%s: Could generate idp_gid_username for client",
                    c_state->tunnel_id);
            return res;
        }

        res = wally_table_register_for_row(c_state->scim_state->scim_user_registrant,
                                           scim_user_column,
                                           &(idp_gid_username),
                                           strlen(idp_gid_username),
                                           int_cookie,
                                           0, /* Sequence */
                                           0, /* Atleast one */
                                           0, /* just callback */
                                           0, /* Unique registration */
                                           zpn_client_scim_table_complete_callback,
                                           cookie);

        if (res == WALLY_RESULT_ASYNCHRONOUS) {
            res = WALLY_RESULT_NO_ERROR;
        } else if (res != WALLY_RESULT_NO_ERROR) {
            ZPN_LOG(AL_CRITICAL, "registration failed");
        } else {
            /* Already registered */
        }
    }
    return res;
}

/*
 * This is to register for AAE Profile Conflusion table
 * based on the iam_user_id, by the time request comes here
 * we already have iam_user_id and device_id
 */
int zpn_broker_client_scim_register_for_aae_profile_conclusion(struct zpn_broker_client_fohh_state *c_state,
                                                               wally_response_callback_f *callback,
                                                               void *cookie,
                                                               int64_t int_cookie,
                                                               char *wally_debug_str) {

    if (!c_state->scim_state) {
        // Connection went away...
        return ZPN_RESULT_ERR;
    }

    // Use-case when the AAE profile is not enabled
    if (!c_state->scim_state->aae_conclusion) {
        // Feature is not enabled, still go ahead
        return ZPN_RESULT_NO_ERROR;
    }

    if (c_state->scim_state->aae_conclusion_registrant) {
        // Already registered, skip
        return ZPN_RESULT_NO_ERROR;
    }

    int res = ZPN_RESULT_NO_ERROR;
    struct wally_table *aae_profile_conclusion = NULL;

    res = zpath_et_wally_userdb_get_wally_table("zpn_aae_profile_conclusion", c_state->customer_gid, &aae_profile_conclusion);
    if (res) {
        ZPN_LOG(AL_ERROR, "%s: Unexpected lack of table when trying to register for AAE Profile Conclusion %s",
                c_state->tunnel_id, zpn_result_string(res));
        return ZPN_RESULT_ERR;
    }

    /* Get the column, if got the column then get the iam_userid */
    struct wally_index_column *aae_index_column = NULL;
    aae_index_column = zpn_aae_profile_conclusion_gid_column(c_state->customer_gid);
    if (!aae_index_column) {
        ZPN_LOG(AL_ERROR, "tunnel_id %s, customer_gid:%"PRId64": Unexpected lack of "
                "column when trying to register for zpn_aae_profile_conclusion table",
                c_state->tunnel_id, c_state->customer_gid);
        return ZPN_RESULT_ERR;
    }

    /* Before registering get specific profile for the iam_user_id */
    struct zpn_aae_profile_conclusion *usr_conclusion = NULL;
    struct zpn_aae_profile_conclusion *device_conclusion = NULL;
    size_t get_specific_row = 1;
    res = zpn_aae_conclusion_get_by_iam_userid_deviceid(c_state->customer_gid,
                                                        c_state->scim_state->aae_conclusion->iam_user_id,
                                                        strnlen(c_state->scim_state->aae_conclusion->iam_user_id, MAX_IAM_USER_ID_LEN),
                                                        NULL,
                                                        0,
                                                        &usr_conclusion,
                                                        &get_specific_row,
                                                        callback,
                                                        cookie,
                                                        int_cookie);
    if (res && (res != ZPATH_RESULT_NOT_FOUND)) {
        // In case of error or async return
        zpn_broker_client_scim_state_on_cstate_free(c_state);
        if (ZPN_RESULT_ASYNCHRONOUS == res) {
            ZPN_LOG(AL_NOTICE, "ASYNC - Getting the AAE Profile Conclusions "
                    "by iam_user_id %s for customer %"PRId64" and "
                    "tunnel id %s",
                    c_state->scim_state->aae_conclusion->iam_user_id,
                    c_state->customer_gid,
                    c_state->tunnel_id);
        } else {
            ZPN_LOG(AL_ERROR, "tunnel_id %s: Could not get AAE Profile Conclusions "
                    "by iam_user_id %s for customer %"PRId64" "
                    "error %s", c_state->tunnel_id,
                    c_state->scim_state->aae_conclusion->iam_user_id,
                    c_state->customer_gid,
                    zpath_result_string(res));
        }
        return res;
    } else if (res) {
        ZPN_DEBUG_AAE("AAE Get user conclusion error %s, tunnel_id %s: iam_user_id %s for customer %"PRId64,
                       zpath_result_string(res), c_state->tunnel_id, c_state->scim_state->aae_conclusion->iam_user_id,
                       c_state->customer_gid);
    }

    /* Get the profiles specific to iam_user_id and iam_device_id */
    if (c_state->scim_state->aae_conclusion->iam_device_id) {
        res = zpn_aae_conclusion_get_by_iam_userid_deviceid(c_state->customer_gid,
                                                            c_state->scim_state->aae_conclusion->iam_user_id,
                                                            strnlen(c_state->scim_state->aae_conclusion->iam_user_id, MAX_IAM_USER_ID_LEN),
                                                            c_state->scim_state->aae_conclusion->iam_device_id,
                                                            strnlen(c_state->scim_state->aae_conclusion->iam_device_id, MAX_IAM_DEVICE_ID_LEN),
                                                            &device_conclusion,
                                                            &get_specific_row,
                                                            callback,
                                                            cookie,
                                                            int_cookie);
        if (res && (res != ZPATH_RESULT_NOT_FOUND)) {
            // In case of error or async return
            zpn_broker_client_scim_state_on_cstate_free(c_state);
            if (ZPN_RESULT_ASYNCHRONOUS == res) {
                ZPN_LOG(AL_NOTICE, "tunnel_id %s: ASYNC - Getting the AAE Profile Conclusions "
                    "by iam_user_id %s and iam_device_id %s for customer %"PRId64,
                    c_state->tunnel_id,
                    c_state->scim_state->aae_conclusion->iam_user_id,
                    c_state->scim_state->aae_conclusion->iam_device_id,
                    c_state->customer_gid);
            } else {
                ZPN_LOG(AL_ERROR, "tunnel_id %s: Could not get AAE Profile Conclusions "
                    "by iam_user_id %s and iam_device_id %s for customer %"PRId64" "
                    "error %s", c_state->tunnel_id,
                    c_state->scim_state->aae_conclusion->iam_user_id,
                    c_state->scim_state->aae_conclusion->iam_device_id,
                    c_state->customer_gid,
                    zpath_result_string(res));
            }
            return res;
        } else if (res) {
            ZPN_DEBUG_AAE("AAE Get device conclusion error %s, tunnel_id %s: iam_user_id %s iam_device_id %s "
                          "for customer %"PRId64, zpath_result_string(res), c_state->tunnel_id,
                          c_state->scim_state->aae_conclusion->iam_user_id,
                          c_state->scim_state->aae_conclusion->iam_device_id, c_state->customer_gid);
        }
    }

    zpn_aae_initialize_aae_conclusion_array(c_state->scim_state->aae_conclusion,
                                            usr_conclusion,
                                            device_conclusion);

    if (c_state->scim_state->aae_conclusion->user_profile_cnt) {
        __sync_add_and_fetch(&(zpn_aae_stats.user_profile_present), 1);
        res = zpn_broker_client_aae_profile_update_hash(c_state->scim_state->aae_conclusion, c_state, 1, 0);
        if (res) {
            ZPN_LOG(AL_ERROR, "%s: Failed to store the AAE User Profile ID in the scim hash, error = %s",
                    c_state->tunnel_id, zpath_result_string(res));
            return res;
        }
    } else if (usr_conclusion) {
       __sync_add_and_fetch(&(zpn_aae_stats.user_profile_conclusion_list_is_empty), 1);
    }

    if (c_state->scim_state->aae_conclusion->device_profile_cnt) {
        __sync_add_and_fetch(&(zpn_aae_stats.deviceid_profile_present), 1);
        res = zpn_broker_client_aae_profile_update_hash(c_state->scim_state->aae_conclusion, c_state, 0, 0);
        if (res) {
            ZPN_LOG(AL_ERROR, "%s: Failed to store the AAE Device Profile ID in the scim hash, error = %s",
                    c_state->tunnel_id, zpath_result_string(res));
            return res;
        }
    } else if (device_conclusion) {
        __sync_add_and_fetch(&(zpn_aae_stats.device_profile_conclusion_list_is_empty), 1);
    }

    c_state->scim_state->aae_conclusion_registrant =
                     wally_table_create_registrant(aae_profile_conclusion,
                                                   zpn_broker_client_scim_aae_row_callback,
                                                   cookie,
                                                   wally_debug_str);
    if (!c_state->scim_state->aae_conclusion_registrant) {
        ZPN_LOG(AL_CRITICAL,
                "%s: Could not create registrant for zpn_aae_profile_conclusion table for client",
                c_state->tunnel_id);
        return ZPN_RESULT_NO_MEMORY;
    } else {
        ZPN_DEBUG_AUTH("%s: Registering for zpn_aae_profile_conclusion customer %"PRId64,
                       c_state->tunnel_id,
                       c_state->customer_gid);
    }

    char register_str[AAE_REGISTER_STR_MAX_SZ] = {0};
    zpn_aae_get_iam_userid_str(register_str, sizeof(register_str), c_state->scim_state->aae_conclusion->iam_user_id);
    res = wally_table_register_for_row(c_state->scim_state->aae_conclusion_registrant,
                                       aae_index_column,
                                       register_str,
                                       strnlen(register_str, AAE_REGISTER_STR_MAX_SZ),
                                       int_cookie,
                                       0, /* Sequence */
                                       0, /* Atleast one */
                                       0, /* just callback */
                                       0, /* Unique registration */
                                       zpn_client_scim_table_complete_callback,
                                       cookie);

    if (res == WALLY_RESULT_ASYNCHRONOUS) {
        res = WALLY_RESULT_NO_ERROR;
    } else if (res != WALLY_RESULT_NO_ERROR) {
        ZPN_LOG(AL_CRITICAL, "tunnel_id %s: Wally register for row failed "
                "for aae profile conclusion table for iam_user_id %s ",
                c_state->tunnel_id,
                c_state->scim_state->aae_conclusion->iam_user_id);
        return res;
    } else {
        /* Already registered */
    }

    if (c_state->scim_state->aae_conclusion->iam_device_id) {
        zpn_aae_get_iam_userid_deviceid_str(register_str, sizeof(register_str),
                                            c_state->scim_state->aae_conclusion->iam_user_id,
                                            c_state->scim_state->aae_conclusion->iam_device_id);

        res = wally_table_register_for_row(c_state->scim_state->aae_conclusion_registrant,
                                           aae_index_column,
                                           register_str,
                                           strnlen(register_str, AAE_REGISTER_STR_MAX_SZ),
                                           int_cookie,
                                           0, /* Sequence */
                                           0, /* Atleast one */
                                           0, /* just callback */
                                           0, /* Unique registration */
                                           zpn_client_scim_table_complete_callback,
                                           cookie);

        if (res == WALLY_RESULT_ASYNCHRONOUS) {
            res = WALLY_RESULT_NO_ERROR;
        } else if (res != WALLY_RESULT_NO_ERROR) {
            ZPN_LOG(AL_CRITICAL, "tunnel_id %s: Wally register for row failed for aae profile conclusion table "
                    "for iam_user_id %s and iam_device_id %s for customer %"PRId64,
                    c_state->tunnel_id,
                    c_state->scim_state->aae_conclusion->iam_user_id,
                    c_state->scim_state->aae_conclusion->iam_device_id,
                    c_state->customer_gid);
            return res;
        } else {
            /* Already registered */
        }
    }

    return res;
}

struct zhash_table *zpn_broker_client_scim_get_scim_state_hashed(const struct zpn_broker_client_fohh_state *c_state) {
    if (!c_state) {
        return NULL;
    }

    if (zpn_client_static_config[c_state->client_type].scim_compatible &&
        c_state->scim_enabled &&
        c_state->scim_state) {
        return c_state->scim_state->scim_state_hash;
    }

    return NULL;
}

int zpn_broker_client_scim_user_active(struct zpn_broker_client_fohh_state *c_state) {
    if (!c_state->scim_enabled) {
        return 1; // user is active if the scim is disabled
    } else if (c_state->scim_state && c_state->scim_state->seen_active) {
        // Only active if its tagged as active and the user isn't marked deleted
        return c_state->scim_state->active && !c_state->scim_state->deleted;
    } else {
        return 0; //scim is enabled but no state? must be bad kill it!
    }
}

int zpn_broker_client_scim_user_moved(struct zpn_broker_client_fohh_state *c_state) {
    if (!c_state->scim_enabled || !c_state->scim_state) {
        // If user does not use scim then we will not check for 'moved'
        return 0;
    }

    int64_t zone = 0;
    int64_t userdb = 0;
    int res = ZPATH_RESULT_NO_ERROR;

    res = zpath_et_wally_userdb_get_userdb_identifier_for_customer(c_state->customer_gid, &zone, &userdb);
    if (res) {
        ZPN_LOG(AL_ERROR,
                "%s: could not locate a userdb for a scim enabled customer... this should not happen cust:%"PRId64" idp_gid_username=%"PRId64".%s",
                c_state->tunnel_id,
                c_state->customer_gid,
                c_state->idp_gid,
                c_state->user_id);
        return 0; // Could not find any state for the user, so we can't detect a move
    }

    // If either of our tuple has changed, then we will mark the user as 'moved'
    return c_state->scim_state->zone != zone || c_state->scim_state->userdb != userdb;
}

int zpn_broker_client_scim_done(struct zpn_broker_client_fohh_state *c_state) {
    if (!c_state->scim_enabled) {
        return ZPN_RESULT_NO_ERROR;
    }
    if (!c_state->scim_state) {
        return ZPN_RESULT_ERR;
    }
    struct zpn_broker_client_scim_state *scim_state = c_state->scim_state;

    if (!scim_state->initial_scim_user_finished ||
        !scim_state->initial_groups_finished ||
        !scim_state->initial_attributes_finished ||
        (c_state->scim_state->aae_conclusion && !scim_state->initial_aae_conclusion_finished)) {
        return ZPN_RESULT_ASYNCHRONOUS;
    }

    if (!scim_state->seen_active) {
        ZPN_LOG(AL_WARNING, "%s: user never saw the active attribute for scim", c_state->tunnel_id);
        return ZPN_RESULT_ERR;
    }

    return ZPN_RESULT_NO_ERROR;
}

int zpn_broker_client_should_terminate_scim_user(struct zpn_broker_client_fohh_state *c_state) {

    if (!c_state->scim_enabled) {
        return 0; // we shouldn't terminate user if the scim is disabled.
    }

    if (zpn_broker_client_scim_done(c_state) == ZPN_RESULT_NO_ERROR && !zpn_broker_client_scim_user_active(c_state)) {
        return 1; // Scim user was deactivated or deleted. Return true to terminate this scim user.
    }

    return 0; // Scim user is active.
}

int zpn_broker_client_scim_idp_enabled_scim(struct zpn_broker_client_fohh_state *c_state) {
    int res;
    size_t i;
    struct zpn_idp *idps[100];
    size_t idps_count = 100;

    res = zpn_idp_get_customer_gid(c_state->customer_gid, &(idps[0]), &idps_count, NULL, NULL, 0);
    if (res == ZPN_RESULT_ASYNCHRONOUS) {
        return res;
    }

    if (idps_count == 0) {
        //We dont enforce idp configuration deletions within scim so we wont do anything here if that is
        //a requirement it needs to be handled somewhere else
        return ZPN_RESULT_NO_ERROR;
    }

    res = ZPN_RESULT_NO_ERROR;
    //Using expired as return because the user state is now stale so 'expired' if change detected

    for(i = 0; i < idps_count; i++) {
        if (idps[i]->gid == c_state->idp_gid){
            //SCIM was toggled on
            if (idps[i]->enable_scim_based_policy != c_state->scim_enabled) {
                res = ZPN_RESULT_EXPIRED;
            }
            //compare disable policy setting with auth 'enable' setting
            if (idps[i]->disable_saml_based_policy == c_state->saml_enabled) {
                res = ZPN_RESULT_EXPIRED;
            }
            break;
        }
    }

    return res;
}

void zpn_broker_client_scim_free_scim_attribute_strings(void *element, void *cookie) {
    if (!element) {
        ZPN_LOG(AL_WARNING,
                "something strange happened while trying to clean up scim attribute strings... should investigate but won't hurt the system");
        return;
    }
    struct zpn_scim_user_attribute_strings *attribute_strings = element;

    size_t i;
    for (i = 0; i < attribute_strings->strs_count; i++) {
        ZPN_BCA_FREE(attribute_strings->strs[i]);
    }
    ZPN_BCA_FREE(attribute_strings->strs);
    ZPN_BCA_FREE(attribute_strings);
}

void zpn_broker_client_scim_state_free(struct zpn_broker_client_scim_state *scim_state) {
    if (!scim_state) {
        return;
    }

    if (scim_state->scim_user_attribute_registrant) {
        wally_table_destroy_registrant(scim_state->scim_user_attribute_registrant);
        scim_state->scim_user_attribute_registrant = NULL;
    }

    if (scim_state->scim_user_group_registrant) {
        wally_table_destroy_registrant(scim_state->scim_user_group_registrant);
        scim_state->scim_user_group_registrant = NULL;
    }

    if (scim_state->scim_user_registrant) {
        wally_table_destroy_registrant(scim_state->scim_user_registrant);
        scim_state->scim_user_registrant = NULL;
    }

    if (scim_state->attribute_strings_hash) {
        zhash_table_free_and_call(scim_state->attribute_strings_hash,
                                  zpn_broker_client_scim_free_scim_attribute_strings,
                                  NULL);
        scim_state->attribute_strings_hash = NULL;
    }

    if (scim_state->scim_state_hash) {
        zhash_table_free(scim_state->scim_state_hash);
        scim_state->scim_state_hash = NULL;
    }

    if (scim_state->debug_str) {
        ZPN_BCA_FREE(scim_state->debug_str);
        scim_state->debug_str = NULL;
    }

    if (scim_state->aae_conclusion) {
        if (scim_state->aae_conclusion->iam_user_id) {
            ZPN_AAE_FREE_AND_NULL(scim_state->aae_conclusion->iam_user_id);
        }
        if (scim_state->aae_conclusion->iam_device_id) {
            ZPN_AAE_FREE_AND_NULL(scim_state->aae_conclusion->iam_device_id);
        }
        ZPN_AAE_FREE_AND_NULL(scim_state->aae_conclusion);
    }

    ZPN_BCA_FREE(scim_state);
}

void zpn_broker_client_scim_state_on_cstate_free(struct zpn_broker_client_fohh_state *c_state) {
    if (!c_state) {
        return;
    }
    zpn_broker_client_scim_state_free(c_state->scim_state);

    if (c_state->log_state.scim_user_group) {

        for (int i = 0 ; i < c_state->log_state.scim_user_group_count; i++) {
            ZPN_BCA_FREE(c_state->log_state.scim_user_group[i]);
            c_state->log_state.scim_user_group[i] = NULL;
        }
        c_state->log_state.scim_user_group_count = 0;

        ZPN_BCA_FREE(c_state->log_state.scim_user_group);
        c_state->log_state.scim_user_group = NULL;
    }

    if (c_state->log_state.scim_attributes) {

        for (int i = 0 ; i < c_state->log_state.scim_attributes_count; i++) {
            ZPN_BCA_FREE(c_state->log_state.scim_attributes[i]);
            c_state->log_state.scim_attributes[i] = NULL;
        }
        c_state->log_state.scim_attributes_count = 0;

        ZPN_BCA_FREE(c_state->log_state.scim_attributes);
        c_state->log_state.scim_attributes = NULL;
    }
    c_state->scim_state = NULL;
}


int zpn_broker_is_np_scim_enabled(const struct zpn_broker_client_fohh_state *c_state, int update_prev_if_not_seen_yet)
{
    if (c_state->scim_enabled && c_state->scim_state) {
        if (update_prev_if_not_seen_yet && c_state->scim_state->is_np_scim_enabled_prev == NP_SCIM_NOT_SEEN) {
            c_state->scim_state->is_np_scim_enabled_prev = c_state->scim_state->is_np_scim_enabled;
        }
        return c_state->scim_state->is_np_scim_enabled == NP_SCIM_ENABLED;
    }
    return 0;
}

int zpn_broker_is_np_scim_toggled(const struct zpn_broker_client_fohh_state *c_state)
{
    int res = 0;
    if (c_state->scim_enabled && c_state->scim_state) {
        if (c_state->scim_state->is_np_scim_enabled != c_state->scim_state->is_np_scim_enabled_prev) {
            res = 1;
            c_state->scim_state->is_np_scim_enabled_prev = c_state->scim_state->is_np_scim_enabled;
        }
    }
    return res;
}

int64_t zpn_get_client_np_scim_group(const struct zpn_broker_client_fohh_state *c_state)
{
    return (c_state && c_state->scim_state) ? c_state->scim_state->np_scim_group_id : 0;
}

void zpn_set_client_np_scim_group(struct zpn_broker_client_fohh_state *c_state, int64_t np_scim_group_id)
{
    if (c_state && c_state->scim_state) c_state->scim_state->np_scim_group_id = np_scim_group_id;
}

int64_t zpn_get_client_scim_user_id(const struct zpn_broker_client_fohh_state *c_state)
{
    return (c_state && c_state->scim_state) ? c_state->scim_state->scim_user_id : 0;
}

struct zpn_aae_conclusion *zpn_broker_client_scim_get_aae_conclusion(const struct zpn_broker_client_fohh_state *c_state)
{
    return (c_state && c_state->scim_state) ? c_state->scim_state->aae_conclusion : NULL;
}

int64_t zpn_broker_client_scim_get_aae_conclusion_version(const struct zpn_broker_client_fohh_state *c_state)
{
    return (c_state && c_state->scim_state && c_state->scim_state->aae_conclusion) ? c_state->scim_state->aae_conclusion->version : 0;
}

const char *zpn_broker_client_scim_get_aae_conclusion_iam_user_id(const struct zpn_broker_client_fohh_state *c_state)
{
    return (c_state && c_state->scim_state && c_state->scim_state->aae_conclusion) ? c_state->scim_state->aae_conclusion->iam_user_id : NULL;
}

const char *zpn_broker_client_scim_get_aae_conclusion_iam_device_id(const struct zpn_broker_client_fohh_state *c_state)
{
    return (c_state && c_state->scim_state && c_state->scim_state->aae_conclusion) ? c_state->scim_state->aae_conclusion->iam_device_id : NULL;
}

int zpn_broker_client_scim_init_aae_profile_in_scim_hash(struct zpn_broker_client_fohh_state *c_state) {
    if (!c_state || !c_state->scim_state) {
        return ZPN_RESULT_NO_ERROR;
    }

    return zpn_aae_init_profile_in_scim_hash(c_state->scim_state->aae_conclusion, c_state->tunnel_id,
                                             zpn_broker_client_scim_get_scim_state_hashed(c_state));
}

int zpn_broker_client_aae_feature_toggled(struct zpn_broker_client_fohh_state *c_state, int is_feature_enabled)
{

    int res = ZPN_RESULT_NO_ERROR;

    if (!c_state || !c_state->scim_state || !c_state->scim_state->aae_conclusion) {
        return ZPN_RESULT_NO_ERROR;
    }

    if (is_feature_enabled) {
        res = zpn_aae_init_profile_in_scim_hash(c_state->scim_state->aae_conclusion, c_state->tunnel_id,
                                                zpn_broker_client_scim_get_scim_state_hashed(c_state));
        if (res) {
            ZPN_LOG(AL_ERROR, "%"PRId64":%s : AAE Profile Insert Failed with err: %s",
                               c_state->customer_gid, c_state->tunnel_id, zpn_result_string(res));
        }
        __sync_add_and_fetch(&(zpn_aae_stats.num_client_connects), 1);
    } else {
        if (c_state->scim_state->aae_conclusion->user_profile_cnt) {
            res = zpn_aae_profile_update_scim_hash(c_state->scim_state->aae_conclusion, c_state->tunnel_id,
                                                   zpn_broker_client_scim_get_scim_state_hashed(c_state), 1, 1);
            if (res) {
                ZPN_LOG(AL_ERROR, "%"PRId64":%s : Failed to remove the AAE User Profile ID in the scim hash, error = %s",
                                   c_state->customer_gid, c_state->tunnel_id, zpath_result_string(res));
                return res;
            }
        }

        if (c_state->scim_state->aae_conclusion->device_profile_cnt) {
            res = zpn_aae_profile_update_scim_hash(c_state->scim_state->aae_conclusion, c_state->tunnel_id,
                                                   zpn_broker_client_scim_get_scim_state_hashed(c_state), 0, 1);
            if (res) {
                ZPN_LOG(AL_ERROR, "%"PRId64":%s : Failed to remove the AAE Device Profile ID in the SCIM hash, error = %s",
                                   c_state->customer_gid, c_state->tunnel_id, zpath_result_string(res));
                return res;
            }
        }
        __sync_add_and_fetch(&(zpn_aae_stats.num_client_disconnects), 1);
    }

    c_state->scim_state->aae_conclusion->version++;
    if (c_state->app_state) {
        zpn_broker_client_apps_update(c_state->app_state,
                                    c_state->general_context_hash,
                                    c_state->saml_enabled ? c_state->saml_hash : NULL,
                                    zpn_broker_client_scim_get_scim_state_hashed(c_state));
    }

    return res;
}

void zpn_broker_client_fill_aae_mtunnel_log(const struct zpn_broker_client_fohh_state *c_state, char ***user_profiles, int *user_profile_count, char ***device_profiles, int *device_profile_count)
{
    if (c_state && c_state->is_aae_profile_feature_enabled) {
        struct zpn_aae_conclusion *aae_conclusion = zpn_broker_client_scim_get_aae_conclusion(c_state);
        int i = 0;

        if (aae_conclusion) {
            *user_profile_count = aae_conclusion->user_profile_cnt;
            if (*user_profile_count > 0) {
                *user_profiles = ZPN_AAE_CALLOC(sizeof(**user_profiles) * aae_conclusion->user_profile_cnt);
                for (i = 0; i < aae_conclusion->user_profile_cnt; i++) {
                    (*user_profiles)[i] = ZPN_AAE_STRDUP(aae_conclusion->user_profile_array[i], strlen(aae_conclusion->user_profile_array[i]));
                }
            }

            *device_profile_count = aae_conclusion->device_profile_cnt;
            if (*device_profile_count > 0) {
                *device_profiles = ZPN_AAE_CALLOC(sizeof(**device_profiles) * aae_conclusion->device_profile_cnt);
                for (i = 0; i < aae_conclusion->device_profile_cnt; i++) {
                    (*device_profiles)[i] = ZPN_AAE_STRDUP(aae_conclusion->device_profile_array[i], strlen(aae_conclusion->device_profile_array[i]));
                }
            }
        }
    }
}

void zpn_broker_client_free_aae_mtunnel_log(char ***user_profiles, int user_profile_count, char ***device_profiles, int device_profile_count)
{
    int i = 0;

    if (*user_profiles) {
        for (i = 0; i < user_profile_count; i++)
        {
            if ((*user_profiles)[i]) {
                ZPN_AAE_FREE_AND_NULL((*user_profiles)[i]);
            }
        }
        ZPN_AAE_FREE_AND_NULL(*user_profiles);
    }

    if (*device_profiles) {
        for (i = 0; i < device_profile_count; i++)
        {
            if ((*device_profiles)[i]) {
                ZPN_AAE_FREE_AND_NULL((*device_profiles)[i]);
            }
        }
        ZPN_AAE_FREE_AND_NULL(*device_profiles);
    }
}

int64_t zpn_broker_client_get_scim_version(const struct zpn_broker_client_fohh_state *c_state)
{
    return ((c_state && c_state->scim_state) ? c_state->scim_state->version : 0);
}

void zpn_broker_client_update_scim_version(const struct zpn_broker_client_fohh_state *c_state)
{
    if (c_state && c_state->scim_state) {
        c_state->scim_state->version++;
    }
}
