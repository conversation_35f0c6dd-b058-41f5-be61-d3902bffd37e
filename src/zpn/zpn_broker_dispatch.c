/*
 * zpn_broker_dispatch.c. Copyright (C) 2014 Zscaler Inc. All Rights Reserved
*
 * Functionality:
 * 1. Chooses a dispatcher to use for sending load balancing requests.
 * 2. Forwards health information to dispatchers.
 *
 * Threads involved:
 * 1. FOHH thread as this code can be triggered only when events are received either from dispatcher or connector.
 * 2. dispatch_watcher thread
 * 3. During init the initialization thread.
 */

#include "zpn/zpn_broker_dispatch.h"

#include "fohh/fohh.h"
#include "fohh/fohh_private.h"  // For test code :(
#include "zhash/zhash_table.h"
#include "zpath_lib/zpath_constellation.h"
#include "zpath_lib/zpath_instance.h"
#include "zrdt/zrdt.h"
#include "zthread/zthread.h"

#include "zpn/dsp_debug.h"
#include "zpath_lib/zpath_debug.h"
#include "zpn/zpn_application.h"
#include "zpn/zpn_application_domain.h"
#include "zpn/zpn_broker.h"
#include "zpn/zpn_broker_assert.h"
#include "zpn/zpn_broker_assistant.h"
#include "zpn/zpn_broker_client.h"
#include "zpn/zpn_broker_client_path_cache.h"
#include "zpn/zpn_broker_dispatch.h"
#include "zpn/zpn_broker_dispatch_pool.h"
#include "zpn/zpn_broker_dns.h"
#include "zpn/zpn_broker_mtunnel.h"
#include "zpn/zpn_broker_private.h"
#include "zpn/zpn_dispatcher.h"
#include "zpn/zpn_lib.h"
#include "zpn/zpn_pb_client.h"
#include "zpn/zpn_broker_client_path_cache.h"
#include "zpn/zpn_broker_client.h"
#include "zpn/zpn_broker_dispatch_pool.h"
#include "zpn/zpn_broker_transit.h"
#include "zpn/zpn_c2c_client_registration.h"
#include "zpn/zpn_pbroker_broker_control.h"
#include "zpn/zpn_rpc.h"
#include "zpn/zpn_zrdt_client.h"
#include "zpn/zpn_fohh_client.h"
#include "zpn/zpn_assistantgroup_assistant_relation.h"
#include "zpn/zpn_private_broker_util.h"
#include "zpath_lib/zpath_config_override.h"
#include "zpath_lib/zpath_config_override_keys.h"
#include "zpath_lib/zpath_config_override_desc.h"
#include "fohh/fohh_resolver.h"
#include "zpn/zpn_broker_client_neg_path_cache.h"
#include "zpn/zpn_dispatcher_cfg_override_desc.h"
#include "zpn/zins_inspection/zins_inspection.h"
#include "zpn/zpn_eas/zpn_eas.h"
#include "zpn/zpn_broker_dispatch_c2c_client_check.h"
#include "zpn/zpn_broker_dispatch_pool_compiled.h"

#define LOCAL_DISP_CB_THREAD_COUNT 2
#define LOCAL_DISP_CB_THREAD_POOL "ld-cb"


#define LOCAL_DISP_CN_NAME        "[local dispatcher]"

enum br_route {
    br_route_disp,
    br_route_local_disp,
    br_route_pb_fohh,
    br_route_pb_zrdt
};

extern struct zpn_pbroker_mtunnel_stats mstats;
static struct zhash_table *dispatchers_table = NULL;
static struct zpn_dispatcher *local_disp = NULL;
static int pool_initialized;
static int local_disp_initialized;
static uint8_t dsp_channel_count;

extern struct zpn_pbroker_dns_dispatcher_stats pse_dns_disp_stats;

static
struct zpn_broker_dispatch_stats {                                              /* _ARGO: object_definition */
    /* stats that are done in the context of init thread */
    uint64_t init;                                                              /* _ARGO: integer */
    uint64_t init_done;                                                         /* _ARGO: integer */
    uint64_t init_fohh_stats;                                                   /* _ARGO: integer */
}stats;

/*
 * FIXME: add stats for all other messages. Right now just starting with health msg.
 */
struct zpn_broker_dispatch_fohh_stats {                                         /* _ARGO: object_definition */
    uint64_t zpn_broker_dispatch_send_health_sent_success;                      /* _ARGO: integer */
    uint64_t zpn_broker_dispatch_send_health_sent_failure;                      /* _ARGO: integer */
    uint64_t zpn_broker_dispatch_send_health_dropped_no_dispatchers;            /* _ARGO: integer */
    uint64_t zpn_broker_dispatch_send_discover_frame;                           /* _ARGO: integer */
    uint64_t zpn_broker_dispatch_receive_discover_frame;                        /* _ARGO: integer */
    uint64_t zpn_broker_dispatch_send_discover_frame_failed;                    /* _ARGO: integer */
    uint64_t zpn_broker_dispatch_find_connector_failed;                         /* _ARGO: integer */
};
static
struct zpn_broker_dispatch_audit {                                              /* _ARGO: object_definition */
    uint64_t    stats_cleared;                                                  /* _ARGO: integer */
}audit;

static struct zpath_config_override_desc zpn_broker_dispatch_config_override_desc[] = {
    {
        .key                = CONFIG_FEATURE_BROKER_REDISPATCH_BRK_REQ_TO_DIFF_DC,
        .desc               = "Re-dispatch zpn_broker_request to a dispatcher in different DC if first returned "
                              "NO_CONNECTOR_AVAILABLE or INVALID_DOMAIN error.",
        .details            = "0: Re-dispatch to different DC is disabled\n"
                              "1: Re-dispatch to different DC is enabled\n"
                              "Order of check: customer gid, broker gid, root customer, global\n"
                              "default: 1 (Enabled)",
        .val_type           = config_type_int,
        .component_types    = config_component_broker,
        .target_gid_types   = config_target_gid_type_cust | config_target_gid_type_inst | config_target_gid_type_zsroot |
                              config_target_gid_type_global,
        .int_range_lo       = MIN_CONFIG_FEATURE_BROKER_REDISPATCH_BRK_REQ_TO_DIFF_DC,
        .int_range_hi       = MAX_CONFIG_FEATURE_BROKER_REDISPATCH_BRK_REQ_TO_DIFF_DC,
        .int_default        = DEFAULT_CONFIG_FEATURE_BROKER_REDISPATCH_BRK_REQ_TO_DIFF_DC,
        .feature_group      = FEATURE_GROUP_BROKER_REQUEST_DIFFERENT_DISPATCHER,
        .value_traits       = config_value_traits_feature_enablement,
    },

    {
        .key                = CONFIG_FEATURE_TIMEOUT_REDISPATCH_BRK_REQ_TO_DIFF_DC,
        .desc               = "Re-dispatch zpn_broker_request to a dispatcher in different DC if first dispatcher "
                              "didn't respond within configured time (by default 2 sec).",
        .details            = "0: Timeout re-dispatch to different DC is disabled\n"
                              "1: Timeout re-dispatch to different DC is enabled\n"
                              "Order of check: customer gid, broker gid, global\n"
                              "default: 1 (Enabled)",
        .val_type           = config_type_int,
        .component_types    = config_component_broker,
        .target_gid_types   = config_target_gid_type_cust | config_target_gid_type_inst | config_target_gid_type_global,
        .int_range_lo       = CONFIG_FEATURE_TIMEOUT_REDISPATCH_BRK_REQ_TO_DIFF_DC_MIN,
        .int_range_hi       = CONFIG_FEATURE_TIMEOUT_REDISPATCH_BRK_REQ_TO_DIFF_DC_MAX,
        .int_default        = CONFIG_FEATURE_TIMEOUT_REDISPATCH_BRK_REQ_TO_DIFF_DC_DEFAULT,
        .feature_group      = FEATURE_GROUP_BROKER_TIMEOUT_DIFFERENT_DISPATCHER,
        .value_traits       = config_value_traits_feature_enablement,
    },

    {
        .key                = CONFIG_FEATURE_BRK_REQ_TIMEOUT_S,
        .desc               = "Timeout in seconds after which user broker will re-dispatch zpn_broker_request "
                              "to a dispatcher in different DC.",
        .details            = "Timeout in seconds after which brk req will be re-dispatched\n"
                              "Order of check: customer gid, broker gid, global\n"
                              "default: 2 sec",
        .val_type           = config_type_int,
        .component_types    = config_component_broker,
        .target_gid_types   = config_target_gid_type_cust | config_target_gid_type_inst | config_target_gid_type_global,
        .int_range_lo       = CONFIG_FEATURE_BRK_REQ_TIMEOUT_S_MIN,
        .int_range_hi       = CONFIG_FEATURE_BRK_REQ_TIMEOUT_S_MAX,
        .int_default        = CONFIG_FEATURE_BRK_REQ_TIMEOUT_S_DEFAULT,
        .feature_group      = FEATURE_GROUP_BROKER_TIMEOUT_DIFFERENT_DISPATCHER,
        .value_traits       = config_value_traits_normal,
    },

    {
        .key                = CONFIG_FEATURE_BRK_DSP_CKT_BREAKER_ENABLE,
        .desc               = "To enable/disable broker - dispatcher circuit breaker feature",
        .details            = "Enable/disable broker - dispatcher circuit breaker feature\n"
                              "Order of check: broker gid, global\n"
                              "default: 0 (disabled)",
        .val_type           = config_type_int,
        .component_types    = config_component_broker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_global,
        .int_range_lo       = MIN_CONFIG_FEATURE_BRK_DSP_CKT_BREAKER_ENABLE,
        .int_range_hi       = MAX_CONFIG_FEATURE_BRK_DSP_CKT_BREAKER_ENABLE,
        .int_default        = DEFAULT_CONFIG_FEATURE_BRK_DSP_CKT_BREAKER_ENABLE,
        .feature_group      = FEATURE_GROUP_BROKER_DISPATCHER_CIRCUIT_BREAKER,
        .value_traits       = config_value_traits_feature_enablement,
    },

    {
        .key                = CONFIG_FEATURE_BRK_DSP_CKT_BREAKER_HARD_DISABLE,
        .desc               = "To hard disable broker - dispatcher circuit breaker feature",
        .details            = "Hard disable broker - dispatcher circuit breaker feature\n"
                              "Order of check: global\n"
                              "default: 0",
        .val_type           = config_type_int,
        .component_types    = config_component_broker,
        .target_gid_types   = config_target_gid_type_global,
        .int_range_lo       = MIN_CONFIG_FEATURE_BRK_DSP_CKT_BREAKER_HARD_DISABLE,
        .int_range_hi       = MAX_CONFIG_FEATURE_BRK_DSP_CKT_BREAKER_HARD_DISABLE,
        .int_default        = DEFAULT_CONFIG_FEATURE_BRK_DSP_CKT_BREAKER_HARD_DISABLE,
        .feature_group      = FEATURE_GROUP_BROKER_DISPATCHER_CIRCUIT_BREAKER,
        .value_traits       = config_value_traits_systemwide,
    },

    {
        .key                = CONFIG_FEATURE_BRK_DSP_CKT_BREAKER_TIMEOUT_THRESHOLD_PERCENT,
        .desc               = "To change the timeout threshold percent for broker - dispatcher circuit breaker feature",
        .details            = "Change the timeout threshold percent\n"
                              "Order of check: broker gid, global\n"
                              "default: 10",
        .val_type           = config_type_int,
        .component_types    = config_component_broker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_global,
        .int_range_lo       = MIN_CONFIG_FEATURE_BRK_DSP_CKT_BREAKER_TIMEOUT_THRESHOLD_PERCENT,
        .int_range_hi       = MAX_CONFIG_FEATURE_BRK_DSP_CKT_BREAKER_TIMEOUT_THRESHOLD_PERCENT,
        .int_default        = DEFAULT_CONFIG_FEATURE_BRK_DSP_CKT_BREAKER_TIMEOUT_THRESHOLD_PERCENT,
        .feature_group      = FEATURE_GROUP_BROKER_DISPATCHER_CIRCUIT_BREAKER,
        .value_traits       = config_value_traits_normal,
    },

    {
        .key                = CONFIG_FEATURE_BRK_DSP_CKT_BREAKER_CHINA_TIMEOUT_THRESHOLD_PERCENT,
        .desc               = "To change the timeout threshold percent for China DC for broker - dispatcher circuit breaker feature",
        .details            = "Change the timeout threshold percent for China DC\n"
                              "Order of check: broker gid, global\n"
                              "default: 40",
        .val_type           = config_type_int,
        .component_types    = config_component_broker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_global,
        .int_range_lo       = MIN_CONFIG_FEATURE_BRK_DSP_CKT_BREAKER_CHINA_TIMEOUT_THRESHOLD_PERCENT,
        .int_range_hi       = MAX_CONFIG_FEATURE_BRK_DSP_CKT_BREAKER_CHINA_TIMEOUT_THRESHOLD_PERCENT,
        .int_default        = DEFAULT_CONFIG_FEATURE_BRK_DSP_CKT_BREAKER_CHINA_TIMEOUT_THRESHOLD_PERCENT,
        .feature_group      = FEATURE_GROUP_BROKER_DISPATCHER_CIRCUIT_BREAKER,
        .value_traits       = config_value_traits_normal,
    },

    {
        .key                = CONFIG_FEATURE_BRK_DSP_CKT_BREAKER_NUM_DSP_SPLIT_DNS_REQ,
        .desc               = "To change the number of dispatchers to split DNS request for broker - dispatcher circuit breaker feature",
        .details            = "Change the number of dispatchers to split DNS request when primary dispatcher circuit is open/partially open\n"
                              "Order of check: broker gid, global\n"
                              "default: 2",
        .val_type           = config_type_int,
        .component_types    = config_component_broker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_global,
        .int_range_lo       = MIN_CONFIG_FEATURE_BRK_DSP_CKT_BREAKER_NUM_DSP_SPLIT_DNS_REQ,
        .int_range_hi       = MAX_CONFIG_FEATURE_BRK_DSP_CKT_BREAKER_NUM_DSP_SPLIT_DNS_REQ,
        .int_default        = DEFAULT_CONFIG_FEATURE_BRK_DSP_CKT_BREAKER_NUM_DSP_SPLIT_DNS_REQ,
        .feature_group      = FEATURE_GROUP_BROKER_DISPATCHER_CIRCUIT_BREAKER,
        .value_traits       = config_value_traits_normal,
    },

    {
        .key                = CONFIG_FEATURE_BRK_DSP_CKT_BREAKER_EVALUATION_INTERVAL_MINUTE,
        .desc               = "To change the evaluation interval for broker - dispatcher circuit breaker feature",
        .details            = "Change the evaluation interval (in minute) for broker - dispatcher circuit breaker feature\n"
                              "Order of check: broker gid, global\n"
                              "default: 5  min\n"
                              "Note: Only multiple of 5 is allowed, else default will be used",
        .val_type           = config_type_int,
        .component_types    = config_component_broker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_global,
        .int_range_lo       = MIN_CONFIG_FEATURE_BRK_DSP_CKT_BREAKER_EVALUATION_INTERVAL_MINUTE,
        .int_range_hi       = MAX_CONFIG_FEATURE_BRK_DSP_CKT_BREAKER_EVALUATION_INTERVAL_MINUTE,
        .int_default        = DEFAULT_CONFIG_FEATURE_BRK_DSP_CKT_BREAKER_EVALUATION_INTERVAL_MINUTE,
        .feature_group      = FEATURE_GROUP_BROKER_DISPATCHER_CIRCUIT_BREAKER,
        .value_traits       = config_value_traits_normal,
    },

    {
        .key                = CONFIG_FEATURE_BRK_REQ_TIMEOUT_MAX_JITTER_US,
        .desc               = "Max jitter value in micro seconds to add to broker request timeout",
        .details            = "Random jitter selected from 1 to this configured max jitter value  will be added to timeout\n"
                              "Order of check: customer gid, broker gid, global\n"
                              "default: 100000",
        .val_type           = config_type_int,
        .component_types    = config_component_broker,
        .target_gid_types   = config_target_gid_type_cust | config_target_gid_type_inst | config_target_gid_type_global,
        .int_range_lo       = MIN_CONFIG_FEATURE_BRK_REQ_TIMEOUT_MAX_JITTER_US,
        .int_range_hi       = MAX_CONFIG_FEATURE_BRK_REQ_TIMEOUT_MAX_JITTER_US,
        .int_default        = DEFAULT_CONFIG_FEATURE_BRK_REQ_TIMEOUT_MAX_JITTER_US,
        .feature_group      = FEATURE_GROUP_BROKER_TIMEOUT_DIFFERENT_DISPATCHER,
        .value_traits       = config_value_traits_normal,
    }
};

#include "zpn/zpn_broker_dispatch_compiled_c.h"
static struct zpn_broker_dispatch_fohh_stats *fohh_stats[FOHH_MAX_THREADS];
static struct zpn_broker_dispatcher_stats broker_dispatcher_stats;
static struct argo_structure_description* zpn_broker_dispatch_fohh_stats_description;
static struct argo_structure_description* zpn_broker_dispatch_stats_description;
static struct argo_structure_description* zpn_broker_dispatch_audit_description;
struct argo_structure_description* zpn_broker_dispatcher_stats_description;
static int zpn_broker_dispatch_stats_dump(struct zpath_debug_state*, const char**, int ,void*);
static int zpn_broker_dispatch_stats_clear(struct zpath_debug_state*, const char**, int , void*);
static int send_broker_req_ack_error(struct zpn_broker_client_fohh_state *a_state,
                                            struct zpn_broker_request_ack *req_ack,
                                            int allow_all_xport);

static int
zpn_broker_dispatch_conn_callback(struct fohh_connection *connection,
                                  enum fohh_connection_state state,
                                  void *cookie);
static int
zpn_broker_dispatch_conn_unblock(struct fohh_connection *connection,
                                 enum fohh_queue_element_type element_type,
                                 void *cookie);

int64_t zpn_dispatcher_hold_time_get() {

    int64_t config_value;
    int64_t instance_id;

    instance_id = ZPN_BROKER_GET_GID();
    config_value = zpath_config_override_get_config_int(ZPN_DISPATCHER_HOLD_TIME,
                                                        &config_value,
                                                        DEFAULT_ZPN_DISPATCHER_HOLD_TIME,
                                                        instance_id,
                                                        (int64_t) ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                        (int64_t) 0);

    return (config_value > 0) ? config_value : DEFAULT_ZPN_DISPATCHER_HOLD_TIME;

}

static void zpn_broker_mtunnel_timeout_retry_disp_timer_cb(evutil_socket_t sock, short flags, void *cookie)
{
    struct zpn_broker_mtunnel *mtunnel = cookie;
    int bucket_id = -1;

    mtunnel_bucket_lock(mtunnel, &bucket_id);
    mtunnel_lock(mtunnel);

    if (mtunnel->flag.timeout_redispatch_to_diff_dc) {
        mtunnel->timer_disp_id = mtunnel->redispatch_to_diff_dc_disp_id;
        if (mtunnel->num_route_info_received_from_disp) {
            ZPN_DEBUG_MTUNNEL("%s: Already received route info from %"PRId64" dispatcher, cancelling re-dispatch scheduled on timeout_retry_disp_learn_mode_timer because of %"PRId64" dispatcher",
                              mtunnel->mtunnel_id, mtunnel->last_dispatcher_id, mtunnel->timer_disp_id);
            goto done;
        }
    } else {
        ZPN_LOG(AL_ERROR, "%s: callback incorrectly scheduled on timeout_retry_disp_learn_mode_timer, this mtunnel was not re-dispatched to diff DC due to timeout",
                mtunnel->mtunnel_id);
        goto done;
    }

    /*
     * Timer is only scheduled in case of APP_IN_LEARN_MODE or APP_RETRY.
     * Use of mtunnel->last_disp_error is same in case of both error, i.e. prefer the last used dispatcher.
     * So just set the last_disp_error to APP_IN_LEARN_MODE.
     */
    mtunnel->last_disp_error = zpn_err_brk_req_app_in_learn_mode;

    if (mtunnel->state == zbms_dispatch_sent) {
        mtunnel->state = zbms_authenticated;
        mtunnel->policy_start_us = epoch_us();
        ZPN_DEBUG_MTUNNEL("%s: timeout_retry_disp_learn_mode_timer fired, re-dispatch request", mtunnel->mtunnel_id);
        if (mtunnel->learn_start_each_us) {
            mtunnel->log.learn_us += epoch_us() - mtunnel->learn_start_each_us;
            mtunnel->learn_start_each_us = 0;
        }
        mtunnel_locked_state_machine(mtunnel);
    } else {
        ZPN_LOG(AL_WARNING, "%s: timeout_retry_disp_learn_mode_timer callback but not in proper state (%s)", mtunnel->mtunnel_id, mtunnel_state(mtunnel->state));
    }

done:
    mtunnel_unlock(mtunnel);
    mtunnel_bucket_unlock(bucket_id);
}

static void zpn_broker_mtunnel_timer_callback(evutil_socket_t sock, short flags, void *cookie)
{
    struct zpn_broker_mtunnel *mtunnel = cookie;
    int bucket_id = -1;

    mtunnel_bucket_lock(mtunnel, &bucket_id);
    mtunnel_lock(mtunnel);

    if (mtunnel->flag.timeout_redispatch_to_diff_dc) {
        mtunnel->timer_disp_id = mtunnel->last_dispatcher_id;
        if (mtunnel->num_route_info_received_from_disp) {
            ZPN_DEBUG_MTUNNEL("%s: Already received route info from %"PRId64" dispatcher, cancelling re-dispatch scheduled on timer because of %"PRId64" dispatcher",
                              mtunnel->mtunnel_id, mtunnel->redispatch_to_diff_dc_disp_id, mtunnel->timer_disp_id);
            goto done;
        }
    } else {
        mtunnel->timer_disp_id = mtunnel->dispatcher_id;
    }

    /*
     * Timer is only scheduled in case of APP_IN_LEARN_MODE or APP_RETRY.
     * Use of mtunnel->last_disp_error is same in case of both error, i.e. prefer the last used dispatcher.
     * So just set the last_disp_error to APP_IN_LEARN_MODE.
     */
    mtunnel->last_disp_error = zpn_err_brk_req_app_in_learn_mode;

    if (mtunnel->state == zbms_dispatch_sent) {
        mtunnel->state = zbms_authenticated;
        mtunnel->policy_start_us = epoch_us();
        ZPN_DEBUG_MTUNNEL("%s: timer fired, re-dispatch request", mtunnel->mtunnel_id);
        if (mtunnel->learn_start_each_us) {
            mtunnel->log.learn_us += epoch_us() - mtunnel->learn_start_each_us;
            mtunnel->learn_start_each_us = 0;
        }
        mtunnel_locked_state_machine(mtunnel);
    } else if (mtunnel->state == zbms_probe_sent) {
        mtunnel->state = zbms_request_received;
        ZPN_DEBUG_MTUNNEL("%s: timer fired, re-probe request", mtunnel->mtunnel_id);
        mtunnel_locked_state_machine(mtunnel);
    } else {
        ZPN_LOG(AL_WARNING, "%s: timer callback but not in proper state (%s)", mtunnel->mtunnel_id, mtunnel_state(mtunnel->state));
    }

done:
    mtunnel_unlock(mtunnel);
    mtunnel_bucket_unlock(bucket_id);
}

/*
 * All the requests to dispatcher is sent from c-state's thread because the patch cache inside c-state tries to be
 * lockless and it mandates to be queried from c-state context. Lets schedule to retry the dispatching from c-state's
 * thread context.
 */
static void poke_dispatch_retry(struct zpn_broker_mtunnel *locked_mtunnel)
{
    struct event_base *client_ev_base;

    if (locked_mtunnel->timer) {
        event_del(locked_mtunnel->timer);
    }

    if (!locked_mtunnel->timer) {
        client_ev_base = zpn_broker_client_ev_base(locked_mtunnel);
        if (!client_ev_base) {
            /* client_ev_base gone? */
            ZPN_LOG(AL_ERROR,
                    "%s: Could not create timer on app_in_learn_mode, no client event base",
                    locked_mtunnel->mtunnel_id);
            return;
        }
        locked_mtunnel->timer = event_new(client_ev_base,
                                          -1,
                                          EV_FINALIZE,
                                          zpn_broker_mtunnel_timer_callback,
                                          locked_mtunnel);
        if (!locked_mtunnel->timer) {
            ZPN_LOG(AL_WARNING, "%s: Could not create timer on app_in_learn_mode", locked_mtunnel->mtunnel_id);
            locked_mtunnel->log.action = "close";
            mtunnel_locked_destroy(locked_mtunnel, 1, 0, 0, BRK_MT_SETUP_FAIL_MEMORY);
            mtunnel_locked_state_machine(locked_mtunnel);
            return;
        }
    }

    struct timeval tv;
    int64_t t = ZPN_MTUNNEL_DISPATCH_INTERVAL_US;
    /*
     * Dispatch count : interval, assuming interval of 100ms
     * and max redispatch of 5.
     *
     * 1: 100
     * 2: 300
     * 3: 500
     * 4: 700
     * 5: 900
     *
     * total = 2500 = 2.5s
     */
    if (locked_mtunnel->dispatch_count > 0) {
        t = t * ((locked_mtunnel->dispatch_count * 2) - 1);
    }
    tv.tv_sec = t / 1000000;
    tv.tv_usec = t % 1000000;
    if (event_add(locked_mtunnel->timer, &tv)) {
        ZPN_LOG(AL_WARNING, "%s: Could not add timer", locked_mtunnel->mtunnel_id);
        event_free(locked_mtunnel->timer);
        locked_mtunnel->timer = NULL;
        locked_mtunnel->log.action = "close";
        mtunnel_locked_destroy(locked_mtunnel, 1, 0, 0, BRK_MT_SETUP_FAIL_MEMORY);
        mtunnel_locked_state_machine(locked_mtunnel);
    } else {
        ZPN_DEBUG_MTUNNEL("%s: Learn mode, waiting %" PRId64 " microseconds", locked_mtunnel->mtunnel_id, t);
        locked_mtunnel->learn_start_each_us = epoch_us();
        if (!locked_mtunnel->log.learn_start_us) {
            locked_mtunnel->log.learn_start_us = locked_mtunnel->learn_start_each_us;
        }
    }
}

static void poke_dispatch_retry_on_diff_dc_disp(struct zpn_broker_mtunnel *locked_mtunnel)
{
    struct event_base*      client_ev_base;

    if (locked_mtunnel->timeout_retry_disp_learn_mode_timer) {
        /* Just delete event from queue not free so it can be reused */
        event_del(locked_mtunnel->timeout_retry_disp_learn_mode_timer);
    }

    if (!locked_mtunnel->timeout_retry_disp_learn_mode_timer) {
       client_ev_base = zpn_broker_client_ev_base(locked_mtunnel);
        if (!client_ev_base) {
            /* client_ev_base gone? */
            ZPN_LOG(AL_ERROR, "%s: Could not create timer on app_in_learn_mode, no client event base",
                    locked_mtunnel->mtunnel_id);
            return;
        }

        locked_mtunnel->timeout_retry_disp_learn_mode_timer = event_new(client_ev_base,
                                                                        -1,
                                                                        EV_FINALIZE,
                                                                        zpn_broker_mtunnel_timeout_retry_disp_timer_cb,
                                                                        locked_mtunnel);
        if (!locked_mtunnel->timeout_retry_disp_learn_mode_timer) {
            ZPN_LOG(AL_WARNING, "%s: Could not create timer on app_in_learn_mode", locked_mtunnel->mtunnel_id);
            locked_mtunnel->log.action = "close";
            mtunnel_locked_destroy(locked_mtunnel, 1, 0, 0, BRK_MT_SETUP_FAIL_MEMORY);
            mtunnel_locked_state_machine(locked_mtunnel);
            return;
        }
    }

   struct timeval tv;
   int64_t t = ZPN_MTUNNEL_DISPATCH_INTERVAL_US;
   /*
    * Dispatch count : interval, assuming interval of 100ms
    * and max redispatch of 5.
    *
    * 1: 100
    * 2: 300
    * 3: 500
    * 4: 700
    * 5: 900
    *
    * total = 2500 = 2.5s
    */
    if (locked_mtunnel->dispatch_count > 0) {
        t = t * ((locked_mtunnel->dispatch_count * 2) - 1);
    }

    tv.tv_sec = t / 1000000;
    tv.tv_usec = t % 1000000;
    if (event_add(locked_mtunnel->timeout_retry_disp_learn_mode_timer, &tv)) {
        ZPN_LOG(AL_WARNING, "%s: Could not add timer", locked_mtunnel->mtunnel_id);
        event_free(locked_mtunnel->timeout_retry_disp_learn_mode_timer);
        locked_mtunnel->timeout_retry_disp_learn_mode_timer = NULL;
        locked_mtunnel->log.action = "close";
        mtunnel_locked_destroy(locked_mtunnel, 1, 0, 0, BRK_MT_SETUP_FAIL_MEMORY);
        mtunnel_locked_state_machine(locked_mtunnel);
    } else {
        ZPN_DEBUG_MTUNNEL("%s: Learn mode, waiting %"PRId64" microseconds", locked_mtunnel->mtunnel_id, t);
        locked_mtunnel->learn_start_each_us = epoch_us();
        if (!locked_mtunnel->log.learn_start_us) {
            locked_mtunnel->log.learn_start_us = locked_mtunnel->learn_start_each_us;
        }
    }
}

static void zpn_broker_dispatch_fwd_req_deferred(void *cookie1, void *cookie2) {
    struct argo_object *obj = cookie1;
    struct zpn_broker_request *msg = obj->base_structure_void;

    msg->error = NULL; // Reset error. Empty and NULL strings do make a difference on Java dispatcher.
    msg->req_type = ZPN_BRK_REQ_TYPE_PEEK;

    if (zpn_broker_dispatch_fwd_request(msg)) {
        ZPN_LOG(AL_WARNING, "%s: could not forward pre-learn request", msg->mtunnel_id);
    } else {
        ZPN_DEBUG_DISPATCHER("%s: forwarded pre-learn request", msg->mtunnel_id);
    }
    argo_object_release(obj);
}

int64_t zpn_broker_dispatch_get_brk_req_timeout_us(int64_t customer_id)
{
    int64_t config_value = CONFIG_FEATURE_BRK_REQ_TIMEOUT_S_DEFAULT;

    config_value = zpath_config_override_get_config_int(CONFIG_FEATURE_BRK_REQ_TIMEOUT_S,
                                                        &config_value,
                                                        CONFIG_FEATURE_BRK_REQ_TIMEOUT_S_DEFAULT,
                                                        customer_id,
                                                        zpath_instance_global_state.current_config->gid,
                                                        (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                        0);

    return SECOND_TO_US(config_value);
}

int64_t zpn_broker_dispatch_get_brk_req_timeout_max_jitter_us(int64_t customer_id)
{
    int64_t config_value = DEFAULT_CONFIG_FEATURE_BRK_REQ_TIMEOUT_MAX_JITTER_US;

    config_value = zpath_config_override_get_config_int(CONFIG_FEATURE_BRK_REQ_TIMEOUT_MAX_JITTER_US,
                                                        &config_value,
                                                        DEFAULT_CONFIG_FEATURE_BRK_REQ_TIMEOUT_MAX_JITTER_US,
                                                        customer_id,
                                                        zpath_instance_global_state.current_config->gid,
                                                        (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                        0);

    return config_value;
}

int64_t zpn_broker_dispatch_get_brk_req_timeout_with_jitter_us(int64_t customer_id)
{
    int64_t timeout_us = 0;
    int64_t max_jitter_us = 0;
    uint64_t random_jitter_us = 0;

    timeout_us = zpn_broker_dispatch_get_brk_req_timeout_us(customer_id);

    max_jitter_us = zpn_broker_dispatch_get_brk_req_timeout_max_jitter_us(customer_id);
    if (max_jitter_us == 0) {
        // No jitter
        return timeout_us;
    }

    /* Find random jitter value within range */
    random_jitter_us = zpn_xorshift64_rand(1, max_jitter_us);

    return timeout_us + random_jitter_us;
}

int zpn_broker_dispatch_is_timeout_redispatch_to_diff_dc_enabled(int64_t customer_id)
{
    int64_t config_value = CONFIG_FEATURE_TIMEOUT_REDISPATCH_BRK_REQ_TO_DIFF_DC_DEFAULT;

    config_value = zpath_config_override_get_config_int(CONFIG_FEATURE_TIMEOUT_REDISPATCH_BRK_REQ_TO_DIFF_DC,
                                                        &config_value,
                                                        CONFIG_FEATURE_TIMEOUT_REDISPATCH_BRK_REQ_TO_DIFF_DC_DEFAULT,
                                                        customer_id,
                                                        zpath_instance_global_state.current_config->gid,
                                                        (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                        0);

    return config_value ? 1 : 0;

}

int zpn_broker_dispatch_is_redispatch_to_diff_dc_enabled(int64_t customer_id)
{
    int64_t config_value = 0;
    int64_t root_customer_gid = ZPATH_GID_GET_CUSTOMER_GID(zpath_instance_global_state.current_config->gid);

    config_value = zpath_config_override_get_config_int(CONFIG_FEATURE_BROKER_REDISPATCH_BRK_REQ_TO_DIFF_DC,
                                                        &config_value,
                                                        DEFAULT_CONFIG_FEATURE_BROKER_REDISPATCH_BRK_REQ_TO_DIFF_DC,
                                                        customer_id,
                                                        zpath_instance_global_state.current_config->gid,
                                                        root_customer_gid,
                                                        (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                        0);

    return config_value ? 1 : 0;

}

/*
 * Get the reason for re-dispatch. Only those error codes are added here for which
 * we re-dispatch zpn_broker_request. Otherwise it would be fresh request, so return
 * ZPN_ERR_BRK_REQ_NONE.
 */
char *zpn_broker_dispatch_get_redispatch_reason(enum zpn_err_brk_req last_disp_error)
{
    if (last_disp_error == zpn_err_brk_req_no_connector_available) {
        return ZPN_ERR_BRK_REQ_NO_CONNECTOR_AVAILABLE;
    } else if (last_disp_error == zpn_err_brk_req_invalid_domain) {
        return ZPN_ERR_BRK_REQ_INVALID_DOMAIN;
    } else if (last_disp_error == zpn_err_brk_req_app_in_learn_mode) {
        return ZPN_ERR_BRK_REQ_APP_IN_LEARN_MODE;
    } else if (last_disp_error == zpn_err_brk_req_timeout_redispatch_to_diff_dc) {
        return ZPN_ERR_BRK_REQ_TIMEOUT;
    } else if (last_disp_error == zpn_err_brk_req_sticky_changed) {
        return ZPN_ERR_BRK_REQ_STICKY_CHANGED;
    }

    return ZPN_ERR_BRK_REQ_NONE; // Fresh request
}

int zpn_broker_dispatch_mtunnel_set_last_disp_error(struct zpn_broker_mtunnel *mtunnel, const char *error)
{
    if (!mtunnel || !error) {
        return ZPN_RESULT_BAD_ARGUMENT;
    }

    if (strcmp(error, ZPN_ERR_BRK_REQ_NO_CONNECTOR_AVAILABLE) == 0) {
        mtunnel->last_disp_error = zpn_err_brk_req_no_connector_available;
    } else if (strcmp(error, ZPN_ERR_BRK_REQ_APP_NOT_REACHABLE) == 0) {
        mtunnel->last_disp_error = zpn_err_brk_req_app_not_reachable;
    } else if (strcmp(error, ZPN_ERR_BRK_REQ_APP_IN_LEARN_MODE) == 0) {
        mtunnel->last_disp_error = zpn_err_brk_req_app_in_learn_mode;
    } else if (strcmp(error, ZPN_ERR_BRK_REQ_APP_RETRY) == 0) {
        mtunnel->last_disp_error = zpn_err_brk_req_app_retry;
    } else if (strcmp(error, ZPN_ERR_BRK_REQ_STICKY_CHANGED) == 0) {
        mtunnel->last_disp_error = zpn_err_brk_req_sticky_changed;
    } else if (strcmp(error, ZPN_ERR_BRK_REQ_MULTIPLE_CLIENT_REGISTRATIONS_FOR_DOMAIN) == 0) {
        mtunnel->last_disp_error = zpn_err_brk_req_multiple_client_registrations_for_domain;
    } else if (strcmp(error, ZPN_ERR_BRK_REQ_INVALID_DOMAIN) == 0) {
        mtunnel->last_disp_error = zpn_err_brk_req_invalid_domain;
    }

    return ZPN_RESULT_NO_ERROR;
}

/*
* Update stats if promotion was sucessful due to c2c local dispatcher bypass
*/
static void zpn_broker_request_c2c_bypass_update_stats(const struct zpn_broker_request *req, const struct zpn_broker_mtunnel *mtunnel)
{
    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();
    struct zpn_private_broker_mtunnel_stats *mtstats = &gs->private_broker_state->mtunnel_stats;

    /* Count only for mtunnels marked by local dispatcher as promoted due to c2c regex bypass */
    if (req->path_decision & ZPN_TX_PATH_DECISION_DSP_DISPATCH_C2C_LOCAL_BYPASS_REGEX_MATCH ) {
        switch (mtunnel->client_type) {
            case zpn_client_type_zapp:
                __sync_fetch_and_add_8(&(mtstats->zcc_mtunnels_c2c_regex_bypass), 1);
                break;

            case zpn_client_type_branch_connector:
                __sync_fetch_and_add_8(&(mtstats->bc_mtunnels_c2c_regex_bypass), 1);
                break;

            case zpn_client_type_edge_connector:
                __sync_fetch_and_add_8(&(mtstats->ec_mtunnels_c2c_regex_bypass), 1);
                break;

            case zpn_client_type_vdi:
                __sync_fetch_and_add_8(&(mtstats->vdi_mtunnels_c2c_regex_bypass), 1);
                break;

            case zpn_client_type_machine_tunnel:
                __sync_fetch_and_add_8(&(mtstats->mt_mtunnels_c2c_regex_bypass), 1);
                break;

            case zpn_client_type_zapp_partner:
                __sync_fetch_and_add_8(&(mtstats->zp_mtunnels_c2c_regex_bypass), 1);
                break;

            default:
                __sync_fetch_and_add_8(&(mtstats->other_mtunnels_c2c_regex_bypass), 1);
                break;
        }

        /* Count total bypasses/promotes */
        __sync_fetch_and_add_8(&(mtstats->total_mtunnels_c2c_regex_bypass), 1);
        __sync_fetch_and_add_8(&(mstats.mt_promote_c2c_regex_bypass_count), 1);

        /* Record average regex eval time computed over lifetime of c2c regex evals */
        ZPN_ATOMIC_STORE(&(mtstats->c2c_regex_eval_avg_time_us), zpn_broker_c2c_regex_get_avg_eval_time_us());

        ZPN_DEBUG_MTUNNEL("%s: c2c disp bypass promoted mtunnel stats updated", req->mtunnel_id);
    }
}

/*
 * Replace the old broker request ack message processing
 */
static int zpn_broker_request_error(struct zpn_broker_request *req, char *disp_desc)
{
    struct zpn_broker_mtunnel *mtunnel;
    struct zpn_broker_dispatcher_stats *brk_dsp_stats = NULL;
    struct zpn_broker_dispatcher *dispatcher = NULL;
    size_t mtunnel_len;
    uint64_t hash;
    int bucket_id = -1;
    int64_t neg_cache_ttl_us;
    int res;

    if (ZPN_BROKER_IS_PUBLIC()) {
        __sync_add_and_fetch_8(&broker_dispatcher_stats.zpn_brk_req_response_received, 1);
        brk_dsp_stats = zpn_broker_dispatcher_get_stats(req->g_dsp);
        if (brk_dsp_stats) {
            __sync_add_and_fetch_8(&(brk_dsp_stats->zpn_brk_req_response_received), 1);
        }
    }

    if (!req->mtunnel_id) {
        ZPN_LOG(AL_WARNING, "Broker request error received without mtunnel_id from %s", disp_desc);
        return ZPN_RESULT_NO_ERROR;
    }

    if (zpn_debug_get(ZPN_DEBUG_MTUNNEL_IDX) || zpn_debug_get(ZPN_DEBUG_DISPATCHER_IDX)) {
        char dump[8000];
        if (argo_structure_dump(zpn_broker_request_description, req, dump, sizeof(dump), NULL, 0) == ARGO_RESULT_NO_ERROR) {
            size_t len = strlen(dump);
            if (len && (dump[len - 1] == '\n')) dump[len - 1] = 0;
            ZPN_LOG(AL_DEBUG, "%s: Broker request error received: %s", req->mtunnel_id, dump);
        }
    }

    if (req->g_brk != ZPN_BROKER_GET_GID()) {
        ZPN_LOG(AL_CRITICAL, "%s: Broker request error received for different broker. Implement me.", req->mtunnel_id);
        return ZPN_RESULT_NO_ERROR;
    }

    mtunnel_len = strlen(req->mtunnel_id);
    if (mtunnel_len < ZPN_MTUNNEL_ID_BYTES_TEXT_MIN) {
        ZPN_LOG(AL_WARNING, "%s: Broker request error received with invalid mtunnel_id from %s", req->mtunnel_id, disp_desc);
        return ZPN_RESULT_NO_ERROR;
    }

    if (!req->error) {
        ZPN_LOG(AL_WARNING, "%s: Broker request error received without error? from %s", req->mtunnel_id, disp_desc);
        return ZPN_RESULT_NO_ERROR;
    }

    hash = CityHash64(req->mtunnel_id, strlen(req->mtunnel_id));
    mtunnel = mtunnel_lookup_and_bucket_lock(req->mtunnel_id, hash, &bucket_id);
    if (!mtunnel) {
        ZPN_LOG(AL_WARNING, "%s: Broker request error received, but mtunnel not found from %s", req->mtunnel_id, disp_desc);
        return ZPN_RESULT_NO_ERROR;
    }

    mtunnel_lock(mtunnel);
    mtunnel->path_decision |= req->path_decision;
    zpn_tx_path_decision_get_str(mtunnel->path_decision, mtunnel->log.path_decision, sizeof(mtunnel->log.path_decision));
    zpn_broker_dispatch_mtunnel_set_last_disp_error(mtunnel, req->error);
    mtunnel->flag.response_received_from_disp = 1;
    mtunnel->dispatcher_id = req->g_dsp;

    /* We are now locked. */
    if (mtunnel->flag.timeout_redispatch_to_diff_dc && mtunnel->num_route_info_received_from_disp) {
        /*
         * We sent brk req to 2 dispatchers and already received route info from one of them.
         * No need to process this error now - ignore.
         */
        ZPN_DEBUG_MTUNNEL("%s: Received %s error from %"PRId64" dispatcher but had already received route info from %"PRId64" dispatcher - ignoring",
                          mtunnel->mtunnel_id, req->error, req->g_dsp, mtunnel->log.g_dsp);
        __sync_add_and_fetch_8(&broker_dispatcher_stats.zpn_brk_req_redispatch_timeout_error_discard, 1);
        if (brk_dsp_stats) {
            __sync_add_and_fetch_8(&(brk_dsp_stats->zpn_brk_req_redispatch_timeout_error_discard), 1);
        }
        goto done;
    }

    if (strcmp(req->error, ZPN_ERR_BRK_REQ_STICKY_CHANGED) == 0) {
        /* Remember IPs and re-dispatch! */
        mtunnel->assistant_id = req->g_ast;
        mtunnel->assistant_group_id = req->g_ast_grp;

        mtunnel->log.g_ast = req->g_ast;
        mtunnel->log.g_ast_grp = req->g_ast_grp;
        mtunnel->application_server_id = req->g_aps;
        mtunnel->log.g_aps = req->g_aps;
        mtunnel->log.s_ip = req->s_ip;
        mtunnel->state = zbms_authenticated;
        mtunnel->policy_start_us = epoch_us();
        if (ZPN_BROKER_IS_PUBLIC()) {
            __sync_add_and_fetch_8(&broker_dispatcher_stats.zpn_brk_req_redispatch_sticky_changed, 1);
            if (brk_dsp_stats) {
                __sync_add_and_fetch_8(&(brk_dsp_stats->zpn_brk_req_redispatch_sticky_changed), 1);
            }
        }

        mtunnel_locked_state_machine(mtunnel);
    } else if ((strcmp(req->error, ZPN_ERR_BRK_REQ_NO_CONNECTOR_AVAILABLE) == 0 || strcmp(req->error, ZPN_ERR_BRK_REQ_INVALID_DOMAIN) == 0) &&
               ZPN_BROKER_IS_PUBLIC() &&
               (mtunnel->path_decision & ZPN_TX_PATH_DECISION_BRK_TX_TO_DSP_IN_DIFF_DC) == 0 &&
               (mtunnel->path_decision & ZPN_TX_PATH_DECISION_BRK_TX_TO_DSP_TIMEOUT_RETRY) == 0 &&
               zpn_broker_dispatch_is_redispatch_to_diff_dc_enabled(mtunnel->customer_id)) {
        /* Remember IPs and re-dispatch to a dispatcher in different DC (same pool). */
        mtunnel->assistant_id = req->g_ast;
        mtunnel->assistant_group_id = req->g_ast_grp;

        mtunnel->log.g_ast = req->g_ast;
        mtunnel->log.g_ast_grp = req->g_ast_grp;
        mtunnel->application_server_id = req->g_aps;
        mtunnel->log.g_aps = req->g_aps;
        mtunnel->log.s_ip = req->s_ip;
        mtunnel->state = zbms_authenticated;
        mtunnel->policy_start_us = epoch_us();
        mtunnel->path_decision |= ZPN_TX_PATH_DECISION_BRK_TX_TO_DSP_IN_DIFF_DC;
        mtunnel->last_dispatcher_id = req->g_dsp;
        mtunnel->brk_req_redispatch_to_diff_dc_reason = mtunnel->last_disp_error;
        mtunnel->flag.neg_path_cache_on_brk = req->neg_cache_on_brk ? 1 : 0;
        if (strcmp(req->error, ZPN_ERR_BRK_REQ_NO_CONNECTOR_AVAILABLE) == 0) {
            __sync_add_and_fetch_8(&broker_dispatcher_stats.zpn_brk_req_redispatch_no_connector, 1);
            if (brk_dsp_stats) {
                __sync_add_and_fetch_8(&(brk_dsp_stats->zpn_brk_req_redispatch_no_connector), 1);
            }
        } else {
            __sync_add_and_fetch_8(&broker_dispatcher_stats.zpn_brk_req_redispatch_invalid_domain, 1);
            if (brk_dsp_stats) {
                __sync_add_and_fetch_8(&(brk_dsp_stats->zpn_brk_req_redispatch_invalid_domain), 1);
            }
        }

        ZPN_DEBUG_MTUNNEL("%s: Received %s error, re-dispatching zpn_broker_request to a dispatcher in different DC",
                          mtunnel->mtunnel_id, req->error);
        mtunnel_locked_state_machine(mtunnel);
    } else if ((strcmp(req->error, ZPN_ERR_BRK_REQ_APP_IN_LEARN_MODE) == 0) ||
               (strcmp(req->error, ZPN_ERR_BRK_REQ_APP_RETRY) == 0)) {
        if (ZPN_BROKER_IS_PRIVATE()) {
            // Forward pre-learn request to broker.
            if (!mtunnel->flag.pre_learn_brk_req_fwded) {
                struct argo_object *obj = argo_object_create(zpn_broker_request_description, req);
                if (!obj) {
                    ZPN_LOG(AL_ERROR, "Could not create zpn_broker_request argo object");
                } else if (zevent_defer(zpn_broker_dispatch_fwd_req_deferred, obj, NULL, 0)) {
                    ZPN_LOG(AL_ERROR, "Could not dispatch pre-learn request forwarding call");
                    argo_object_release(obj);
                }
                mtunnel->flag.pre_learn_brk_req_fwded = 1; //  Try to forward just once.
            }
        }

        if (ZPN_BROKER_IS_PUBLIC()) {
            if (strcmp(req->error, ZPN_ERR_BRK_REQ_APP_IN_LEARN_MODE) == 0) {
                __sync_add_and_fetch_8(&broker_dispatcher_stats.zpn_brk_req_redispatch_app_in_learn_mode, 1);
                if (brk_dsp_stats) {
                    __sync_add_and_fetch_8(&(brk_dsp_stats->zpn_brk_req_redispatch_app_in_learn_mode), 1);
                }
            } else {
                __sync_add_and_fetch_8(&broker_dispatcher_stats.zpn_brk_req_redispatch_app_retry, 1);
                if (brk_dsp_stats) {
                    __sync_add_and_fetch_8(&(brk_dsp_stats->zpn_brk_req_redispatch_app_retry), 1);
                }
            }
            dispatcher = zhash_table_lookup(dispatchers_table, &(req->g_dsp), sizeof(req->g_dsp), NULL);
        }
        if (ZPN_BROKER_IS_PUBLIC() && dispatcher &&
            zpn_brk_dsp_circuit_breaker_get_state(dispatcher) == brk_dsp_circuit_breaker_state_open) {
            /* This dispatcher circuit is open now - don't create timer, just send the request to another dispatcher right away. */
            mtunnel->assistant_id = req->g_ast;
            mtunnel->assistant_group_id = req->g_ast_grp;
            mtunnel->log.g_ast = req->g_ast;
            mtunnel->log.g_ast_grp = req->g_ast_grp;
            mtunnel->application_server_id = req->g_aps;
            mtunnel->log.g_aps = req->g_aps;
            mtunnel->log.s_ip = req->s_ip;
            mtunnel->state = zbms_authenticated;
            mtunnel->policy_start_us = epoch_us();

            mtunnel_locked_state_machine(mtunnel);
        } else if (ZPN_BROKER_IS_PUBLIC() &&
                   mtunnel->flag.timeout_redispatch_to_diff_dc &&
                   mtunnel->dispatcher_id == mtunnel->redispatch_to_diff_dc_disp_id) {
            /*
             * We had re-dispatched to diff DC due to timeout and APP_IN_LEARN_MODE / APP_RETRY came because of
             * second dispatcher. So schedule to fire the brk request again on second dispatcher.
             */
            poke_dispatch_retry_on_diff_dc_disp(mtunnel);
        } else {
            poke_dispatch_retry(mtunnel);
        }
    } else if (ZPN_BROKER_IS_PRIVATE() && !zpn_is_dr_mode_active() && zpn_pbroker_should_promote_mtunnel_ddil(mtunnel)) {
        int res = ZPN_RESULT_ERR;

        mtunnel->promoted_to_public = 0;
        mtunnel->pse_to_broker_incarnation = 0;
        res = zpn_pb_mtunnel_promote_locked(mtunnel, bucket_id);

        if (res) {
            __sync_fetch_and_add_8(&(mstats.mt_promote_fail_count), 1);
            ZPN_LOG(AL_ERROR,
                    "%s: could not promote: %s", mtunnel->mtunnel_id, zpn_result_string(res));
            mtunnel->log.action = "close";
            mtunnel_locked_destroy(mtunnel, 1, 0, 0, BRK_MT_SETUP_FAIL_CANNOT_PROMOTE);
            mtunnel_locked_state_machine(mtunnel);
        } else {
            __sync_fetch_and_add_8(&(mstats.mt_promote_success_count), 1);
            mtunnel->promoted_to_public = 1;

            /* In case of sucessful promotion, check and update bypass stats if promotion is for c2c regex bypass */
            zpn_broker_request_c2c_bypass_update_stats(req, mtunnel);
        }
    } else {
        /* Track dispatcher errors for sipa and mm in case of most specific app gid selected instead of matched application gid */
        int mm_app_diff = mtunnel->flag.specific_app_and_matched_app_different;
        int is_sipa = mtunnel->client_type == zpn_client_type_ip_anchoring;

        if (strcmp(req->error, ZPN_ERR_BRK_REQ_NO_CONNECTOR_AVAILABLE) == 0) {
            if (mm_app_diff) __sync_add_and_fetch_8(&broker_dispatcher_stats.zpn_brk_req_app_multi_match_no_connector, 1);
            if (is_sipa) __sync_add_and_fetch_8(&broker_dispatcher_stats.zpn_brk_req_sipa_no_connector, 1);

            if (brk_dsp_stats) {
                if (mm_app_diff) __sync_add_and_fetch_8(&(brk_dsp_stats->zpn_brk_req_app_multi_match_no_connector), 1);
                if (is_sipa) __sync_add_and_fetch_8(&(brk_dsp_stats->zpn_brk_req_sipa_no_connector), 1);
            }
        } else if (strcmp(req->error, ZPN_ERR_BRK_REQ_INVALID_DOMAIN) == 0) {
            if (mm_app_diff) __sync_add_and_fetch_8(&broker_dispatcher_stats.zpn_brk_req_app_multi_match_invalid_domain, 1);
            if (is_sipa) __sync_add_and_fetch_8(&broker_dispatcher_stats.zpn_brk_req_sipa_invalid_domain, 1);
            if (brk_dsp_stats) {
                if (mm_app_diff) __sync_add_and_fetch_8(&(brk_dsp_stats->zpn_brk_req_app_multi_match_invalid_domain), 1);
                if (is_sipa) __sync_add_and_fetch_8(&(brk_dsp_stats->zpn_brk_req_sipa_invalid_domain), 1);
            }
        }

        mtunnel->num_error_received_from_disp++;
        if (mtunnel->flag.timeout_redispatch_to_diff_dc && mtunnel->num_error_received_from_disp < 2) {
            /* We sent brk req to 2 dispatchers - don't close the mtunnel until we receive error from both of them. */
            ZPN_DEBUG_MTUNNEL("%s: Received %s error from %"PRId64" dispatcher, waiting for response from another dispatcher",
                              mtunnel->mtunnel_id, req->error, req->g_dsp);
            __sync_add_and_fetch_8(&broker_dispatcher_stats.zpn_brk_req_redispatch_timeout_error_discard, 1);
            if (brk_dsp_stats) {
                __sync_add_and_fetch_8(&(brk_dsp_stats->zpn_brk_req_redispatch_timeout_error_discard), 1);
            }
            /*
             * If another dispatcher doesn't respond and mtunnel gets closed with BRK_MT_SETUP_TIMEOUT -
             * fill the correct dispatcher id which didn't respond in transaction log.
             */
            if (req->g_dsp == mtunnel->last_dispatcher_id) {
                mtunnel->log.g_dsp = mtunnel->redispatch_to_diff_dc_disp_id;
            } else {
                mtunnel->log.g_dsp = mtunnel->last_dispatcher_id;
            }
            goto done;
        }

        /* Received error from dispatcher. Negative cache it if asked by dispatcher. */
        if (ZPN_BROKER_IS_PUBLIC() &&
            zpn_broker_client_neg_path_cache_enabled(mtunnel->customer_id) &&
            req->neg_cache_on_brk) {
            neg_cache_ttl_us = zpn_broker_client_neg_path_cache_ttl_us(mtunnel->customer_id);
            res = zpn_broker_client_neg_path_cache_set(req->g_app,
                                                       req->domain,
                                                       req->c_port,
                                                       req->ip_protocol,
                                                       req->g_dsp,
                                                       req->error,
                                                       neg_cache_ttl_us);
            if (res) {
                ZPN_LOG(AL_ERROR, "Could not set negative path cache for g_app(%"PRId64"), domain(%s), c_port(%d), "
                        "ip_protocol(%d), g_dsp(%"PRId64"), error_str(%s), ttl_us(%"PRId64"): %s",
                        req->g_app, req->domain, req->c_port, req->ip_protocol, req->g_dsp,
                        req->error, neg_cache_ttl_us, zpn_result_string(res));
            }
        }

        mtunnel->log.g_dsp = req->g_dsp;
        mtunnel->log.action = "close";
        mtunnel_locked_destroy(mtunnel, 1, 0, 0, req->error);
        mtunnel_locked_state_machine(mtunnel);
    }

done:
    mtunnel_unlock(mtunnel);
    mtunnel_bucket_unlock(bucket_id);

    return ZPN_RESULT_NO_ERROR;
}

void eas_pb_forward_broker_request(struct zevent_base *base, void *void_cookie, int64_t int_cookie) {

    struct argo_object *object  = void_cookie;
    struct zpn_broker_request *req = object->base_structure_void;
    req->error = "EAS_ALWAYS_FORWARD";

    zpn_broker_request_error(req,"EAS_ALWAYS_FORWARD");
}

static struct zpn_pb_client *get_pb_client(struct zpn_broker_client_fohh_state *c_state, int allow_all_xport, const char* mtunnel_id) {
    struct zpn_pb_client *pb_client;

    if (!c_state ) {
        ZPN_LOG(AL_ERROR, "%s: No c_state", mtunnel_id);
        return NULL;
    }

    /* Find the pb_client to use
     * This decision is done on the per mtunnel basis.
     * Here we prefer zrdt/dtls connection to public broker, but we can
     * change to fohh connection per next mtunnel request.
     */
    if (zpn_broker_is_dtls_enabled_on_broker() && allow_all_xport && c_state->public_broker_client_zrdt &&
        zpn_pb_connected(c_state->public_broker_client_zrdt)) {
        pb_client = c_state->public_broker_client_zrdt;
        ZPN_DEBUG_PBC("%s: Pick ZRDT connection to promote, %s", mtunnel_id,
                      zpn_tlv_description(&(pb_client->outbound_pb_to_b_client_zrdt->zrdt_tlv_state.tlv)));
    } else {
        pb_client = c_state->public_broker_client;
        ZPN_DEBUG_PBC("%s: Pick FOHH connection to promote, %s", mtunnel_id,
                      zpn_tlv_description(&(pb_client->outbound_pb_to_b_client->fohh_tlv_state.tlv)));
    }

    return pb_client; // can be NULL
}

static void promote_pb_c2c_mtunnel_cb(struct zevent_base *base,
                                      void *cookie_a_state,
                                      int64_t cookie_a_state_incarnation,
                                      void *cookie_mtunnel_id,
                                      void *cookie_obj_zpn_broker_request_ack,
                                      void *extra_cookie3,
                                      int64_t cookie_int) {
    struct zpn_broker_request_ack *new_req = NULL;
    char *mtunnel_id = cookie_mtunnel_id;
    struct zpn_broker_client_fohh_state *c_state = cookie_a_state;

    struct zpn_pb_client *pb_client = NULL;
    struct zpn_tlv *broker_tlv;
    enum zrdt_stream_type atype = zrdt_stream_buffer;
    enum zrdt_stream_type ctype = zrdt_stream_buffer;
    struct zpn_mconn *client_mconn;
    struct zpn_mconn *assistant_mconn;
    size_t mtunnel_id_len;
    uint64_t mtunnel_id_hash;
    struct zpn_broker_mtunnel *mtunnel = NULL;
    int res = ZPN_RESULT_NO_ERROR;
    int bucket_id = -1;
    // select the pb_client based on what connection was used by broker

    if (!mtunnel_id || !c_state || !cookie_obj_zpn_broker_request_ack) {
        ZPN_LOG(AL_ERROR, "incorrect parameters");
        goto FAIL;
    }

    if (c_state->incarnation != cookie_a_state_incarnation) {
        goto FAIL;
    }

    new_req = ((struct argo_object *)cookie_obj_zpn_broker_request_ack)->base_structure_void;
    if (!new_req) {
        goto FAIL;
    }

    mtunnel_id_len = strlen(new_req->mtunnel_id);
    mtunnel_id_hash = CityHash64(new_req->mtunnel_id, mtunnel_id_len);
    /* broker mtunnel */
    mtunnel = mtunnel_lookup_and_bucket_lock(new_req->mtunnel_id, mtunnel_id_hash, &bucket_id);
    if (NULL == mtunnel) {
        ZPN_LOG(AL_ERROR, "%s: mtunnel not found. hash %" PRIu64, new_req->mtunnel_id, mtunnel_id_hash);
        goto FAIL;
    }

    mtunnel_lock(mtunnel);

    /* Find the pb_client to use
     * This decision is done on the per mtunnel basis in the requesting broker in c2c_pb_zrdt
     * Here we prefer zrdt/dtls connection to public broker, but we can
     * change to fohh connection per next mtunnel request. :q
     */
    if (zpn_broker_is_dtls_enabled_on_broker() &&
        mtunnel->c2c_pb_zrdt && c_state->public_broker_client_zrdt &&
        zpn_pb_connected(c_state->public_broker_client_zrdt)) {
        pb_client = c_state->public_broker_client_zrdt;
        ZPN_DEBUG_MTUNNEL("%s: C2C Pick ZRDT connection to promote, %s", new_req->mtunnel_id,
                      zpn_tlv_description(&(pb_client->outbound_pb_to_b_client_zrdt->zrdt_tlv_state.tlv)));
    } else if (c_state->public_broker_client) {
        pb_client = c_state->public_broker_client;
        ZPN_DEBUG_MTUNNEL("%s: C2C Pick FOHH connection to promote, %s", new_req->mtunnel_id,
                      zpn_tlv_description(&(pb_client->outbound_pb_to_b_client->fohh_tlv_state.tlv)));
    }

    if (!pb_client) {
        ZPN_LOG(AL_ERROR, "%s: No pb_client", mtunnel->mtunnel_id);

        goto FAIL;
    }

    c_state->default_pb_client_tlv_type = pb_client->outbound_tlv_type;

    mtunnel->client_tlv_type = pb_client->outbound_tlv_type;

    broker_tlv = zpn_pb_get_tlv(pb_client);
    if (!broker_tlv) {
        ZPN_LOG(AL_ERROR, "%s: No TLV", mtunnel->mtunnel_id);

        goto FAIL;
    }

    if (zpn_tlv_sanity_check(broker_tlv) != ZPN_RESULT_NO_ERROR) {
        ZPN_LOG(AL_CRITICAL, "tlv no connection?");

        goto FAIL;
    }

    /* BEFORE we connect the mconns we will send the mtunnel
     * request. This ensures in the common case that the mtunnel
     * request arrives before any data. In DTLS this isn't quite as
     * important, but for TLS connections it is required. */

    // send to broker with peer_mtunnel_id
    new_req->mtunnel_id = mtunnel_id;

    res = tlv_argo_serialize(broker_tlv, zpn_broker_request_ack_description, new_req, 0,
                             fohh_queue_element_type_mission_critical);
    if (ZPN_RESULT_NO_ERROR != res) {
        goto FAIL;
    }

    client_mconn = zpn_broker_mtunnel_client_mconn(mtunnel);
    assistant_mconn = zpn_broker_mtunnel_assistant_mconn(mtunnel);

    ZPN_DEBUG_MTUNNEL("%s:  %s Promoting to public broker, tlv = %p, mconn = %p, c_tag = %d ", mtunnel->mtunnel_id,
                  mtunnel->mtunnel_id_parent, broker_tlv, &(mtunnel->client_tlv.mconn), mtunnel->log.c_tag);

    if (pb_client->outbound_tlv_type == zpn_zrdt_tlv) {
        mtunnel->client_tlv_type = zpn_zrdt_tlv;

        if (mtunnel->assistant_tlv_type == zpn_zrdt_tlv) {
            /* Client side has RDT */
            atype = zrdt_stream_transit;
            ctype = zrdt_stream_transit;

        } else {
            atype = zrdt_stream_reliable_endpoint;
            ctype = zrdt_stream_reliable_endpoint;
        }

        res = zrdt_stream_create(
                zpn_mconn_zrdt_tlv_get_conn(&(pb_client->outbound_pb_to_b_client_zrdt->zrdt_tlv_state)),
                &(mtunnel->client_tlv_zrdt.stream),
                mtunnel->log.c_tag,
                atype,
                zpn_zrdt_read_cb,
                zpn_zrdt_write_cb,
                zpn_zrdt_event_cb,
                &(pb_client->outbound_pb_to_b_client_zrdt->zrdt_tlv_state));
        if (res) {
            ZPN_LOG(AL_DEBUG, "%s: Could not create stream", mtunnel->mtunnel_id);

            goto FAIL;
        }

        ZPN_DEBUG_MTUNNEL( "%s: Created stream promoted c_tag=%d %s", mtunnel->mtunnel_id, mtunnel->log.c_tag, zrdt_stream_type_string(atype));
    } else {
        mtunnel->client_tlv_type = zpn_fohh_tlv;
        mtunnel->_client_f_conn = pb_client->inbound_client_connection;

        if (mtunnel->assistant_tlv_zrdt.stream) {
            /* Client side has RDT */
            if (mtunnel->ip_protocol == IPPROTO_UDP || mtunnel->ip_protocol == IPPROTO_ICMP) {
                ctype = zrdt_stream_unreliable_endpoint;
            } else {
                ctype = zrdt_stream_reliable_endpoint;
            }
        }
    }

    if (mtunnel->assistant_tlv_zrdt.stream && (ctype != zrdt_stream_buffer) &&
        zrdt_stream_get_type(mtunnel->assistant_tlv_zrdt.stream) == zrdt_stream_buffer) {
        (void) zrdt_stream_set_type(mtunnel->assistant_tlv_zrdt.stream, ctype);
    }

    zpn_mconn_connect_peer(client_mconn, assistant_mconn);

    zpn_mconn_set_fohh_thread_id(client_mconn, zpn_tlv_get_thread_id(broker_tlv));
    res = zpn_mconn_add_local_owner(
            client_mconn,
            0,
            broker_tlv,
            &(mtunnel->log.c_tag),  // Must last as long as the mconn!! (Don't use the stack...)
            sizeof(mtunnel->log.c_tag),
            (broker_tlv->type == zpn_fohh_tlv) ? &zpn_mconn_fohh_tlv_calls : &zpn_mconn_zrdt_tlv_calls);
    if (res) {
        ZPN_LOG(AL_ERROR, "%s: local owner bind failed: %s", mtunnel->mtunnel_id, zpn_result_string(res));

        goto FAIL;
    }
    /* Update flow control flag */
    if (broker_tlv->type == zpn_fohh_tlv) {
        const struct zpn_fohh_client *fohh_client = pb_client->outbound_pb_to_b_client;
        mtunnel->client_tlv.remote_fc_status = fohh_client->fohh_tlv_state.remote_fc_status;
    }

    ZPN_DEBUG_MTUNNEL("%s: %s: c2c Promoted to broker, Tag %d, %s", mtunnel->mtunnel_id, mtunnel->mtunnel_id_parent,
                      (int)mtunnel->log.c_tag, zpn_tlv_description(broker_tlv));

    mtunnel->log.tunnel_us = zpn_cloud_adjusted_epoch_us() - mtunnel->log.startrx_us;

    mtunnel->state = zbms_complete;
    mtunnel->log.action = "active";
    mtunnel->promoted_to_public = 1;

    mtunnel_locked_state_machine(mtunnel);

    // cleanup, fall through
FAIL:
    if (mtunnel) {
        mtunnel_unlock(mtunnel);
        mtunnel_bucket_unlock(bucket_id);
    }

    if (cookie_obj_zpn_broker_request_ack) {
        argo_object_release(cookie_obj_zpn_broker_request_ack);
    }
    if (mtunnel_id) {
        ZPN_FREE(mtunnel_id);
    }

    return;
}
/*
 * Promote mtunnel to public broker. This is for recieving(term) broker. It requires unlocked mtunnel
 */
int promote_pb_c2c_mtunnel(char *mtunnel_id_parent, struct zpn_broker_request_ack *req) {

    size_t mtunnel_id_len;
    uint64_t mtunnel_id_hash;
    int res = ZPN_RESULT_NO_ERROR;
    struct zpn_broker_client_fohh_state *c_state;
    struct argo_object *object_copy = NULL;
    int thread_id;
    char *tmp_mtunnel_id = NULL;
    struct zpn_broker_connected_clients *connected_clients = &(broker.clients);
    struct zpn_broker_mtunnel *mtunnel;
    int bucket_id = -1;

    if (NULL == mtunnel_id_parent) {
        ZPN_LOG(AL_ERROR, "No mtunnel_id_parent for %s", req->mtunnel_id);
        return ZPATH_RESULT_ERR;
    }

    mtunnel_id_len = strlen(req->mtunnel_id);
    mtunnel_id_hash = CityHash64(req->mtunnel_id, mtunnel_id_len);
    /* broker mtunnel */
    mtunnel = mtunnel_lookup_and_bucket_lock(req->mtunnel_id, mtunnel_id_hash, &bucket_id);
    if (NULL == mtunnel) {
        ZPN_LOG(AL_ERROR, "%s: mtunnel not found. hash %" PRIu64, req->mtunnel_id, mtunnel_id_hash);
        ZPN_FREE(mtunnel_id_parent);
        return ZPN_RESULT_NO_ERROR;
    }

    mtunnel_lock(mtunnel);
    pthread_mutex_lock(&(connected_clients->lock));

    c_state = zpn_broker_mtunnel_assistant_c_state(mtunnel);
    if (!c_state) {
        ZPN_LOG(AL_ERROR, "%s: No c_state", mtunnel->mtunnel_id);
        res = ZPATH_RESULT_ERR;
        pthread_mutex_unlock(&(connected_clients->lock));
        goto FAIL;
    }

    thread_id = c_state_get_fohh_thread_id(c_state);

    // unlock
    mtunnel_unlock(mtunnel);
    mtunnel_bucket_unlock(bucket_id);
    mtunnel = NULL;
    pthread_mutex_unlock(&(connected_clients->lock));

    // deep copy , freed in promote_pb_c2c_mtunnel_cb,
    object_copy = argo_object_create(zpn_broker_request_ack_description, req);
    if (!object_copy) {
        res = ZPN_RESULT_NO_MEMORY;
        goto FAIL;
    }

    if (zevent_base_big_call(fohh_thread_id_zevent_base(thread_id), promote_pb_c2c_mtunnel_cb, c_state,
                             c_state->incarnation, mtunnel_id_parent, object_copy, NULL, 0)) {

        goto FAIL;
    }

    return res;
FAIL:
    if (mtunnel) {
        mtunnel_unlock(mtunnel);
        mtunnel_bucket_unlock(bucket_id);
    }

    if (tmp_mtunnel_id) {
        ZPN_FREE(tmp_mtunnel_id);
    }

    if (object_copy) {
        argo_object_release(object_copy);
    }

    if( mtunnel_id_parent ){
        ZPN_FREE(mtunnel_id_parent);
    }

    return res;
}
/*
 * c2c part of broker request processing,
 */
static void process_c2c_broker_request_cb(struct zevent_base *base,
                                       void *cookie_a_state,
                                       int64_t cookie_route,
                                       void *cookie_msg_object,
                                       void *extra_cookie2,
                                       void *extra_cookie3,
                                       int64_t cookie_a_state_incarnation) {
    /* This is c2c mtunnel */
    struct argo_object *msg_obj = cookie_msg_object;
    struct zpn_broker_request *msg = msg_obj->base_structure_void;
    enum br_route route = cookie_route;
    struct zpn_broker_client_fohh_state *a_state = cookie_a_state;
    struct zpn_broker_mtunnel *mtunnel = NULL;

    uint64_t mtunnel_hash;
    size_t mtunnel_id_len;
    int multiple_brokers = 0;
    int res;
    int bucket_id = -1;

    if( a_state->incarnation != cookie_a_state_incarnation) {
        ZPN_LOG(AL_ERROR, "c2c: c_state incarnation is incorrect %s",  msg->mtunnel_id);
        res = C2C_CLIENT_CONN_EXPIRED;
        goto FAIL;
    }

    if (!c2c_is_enabled_on_broker()) {
        ZPN_LOG(AL_ERROR, "c2c: broker is not enabled");
        zpn_send_broker_req_ack_error_locked(a_state, msg, c2c_result_string(C2C_BROKER_NOT_ENABLED, C2C_SUCCESS_FULL),
                                             1, msg->mtunnel_id);
        goto FREE;
    }

    ZPN_BROKER_ASSERT_SOFT(ZPATH_GID_GET_CUSTOMER_GID(msg->g_app) == a_state->customer_gid, "customer gid mismatch");

    if (!c2c_is_enabled_for_customer(a_state->customer_gid)) {
        ZPN_LOG(AL_ERROR, "c2c: customer %" PRId64 " is not enabled. %s", a_state->customer_gid, msg->mtunnel_id);
        zpn_send_broker_req_ack_error_locked(
                a_state, msg, c2c_result_string(C2C_CUSTOMER_NOT_ENABLED, C2C_SUCCESS_FULL), 1, msg->mtunnel_id);
        goto FREE;
    }

    if (ZPN_BROKER_IS_PRIVATE()) {
        if (br_route_pb_fohh == route || br_route_pb_zrdt == route|| msg->g_brk == msg->g_dsp) {
            // the broker request came from parent broker
            ZPN_DEBUG_C2C("PB BrokerReq from %s broker. %s", msg->g_brk == msg->g_dsp ? "private" : "parent",
                          msg->mtunnel_id);

            // create tunnel
            mtunnel_id_len = strlen(msg->mtunnel_id);
            mtunnel_hash = CityHash64(msg->mtunnel_id, mtunnel_id_len);
            mtunnel = mtunnel_lookup_and_bucket_lock(msg->mtunnel_id, mtunnel_hash, &bucket_id);
            if (mtunnel) {
                mtunnel_lock(mtunnel);
                if (msg->g_brk == msg->g_dsp) {
                    // tunnel must exist in this case
                    mtunnel_unlock(mtunnel);
                    mtunnel_bucket_unlock(bucket_id);

                    ZPN_DEBUG_C2C("%s: c2c single private broker %" PRId64, msg->mtunnel_id, msg->g_brk);
                } else if ((msg->path_decision & ZPN_TX_PATH_DECISION_BRK_TX_TO_DSP_TIMEOUT_RETRY) != 0 ||
                           (mtunnel->path_decision & ZPN_TX_PATH_DECISION_BRK_TX_TO_DSP_TIMEOUT_RETRY) != 0) {
                    /* User broker sent 2 zpn_broker_request and both of them landed here - ignore the later one. */
                    mtunnel_unlock(mtunnel);
                    mtunnel_bucket_unlock(bucket_id);
                    ZPN_DEBUG_C2C("%s: 2 brk req came due to timeout retry - ignoring the later one", msg->mtunnel_id);
                    goto FREE;
                } else {
                    ZPN_LOG(AL_ERROR, "%s: c2c tunnel exist unexpectedly ", msg->mtunnel_id);

                    res = C2C_NOT_AVAILABLE;
                    goto FAIL;
                }
            } else { //mtunnel is null
                mtunnel = bt_transit_mtunnel_create_client_no_lock(msg, a_state);
                if (NULL == mtunnel) {
                    ZPN_LOG(AL_ERROR, "c2c: failed to create mtunnel for %s", msg->mtunnel_id);

                    res = C2C_FAIL_TO_CREATE_MTUNNEL;
                    goto FAIL;
                }
                mtunnel_lock(mtunnel);
                mtunnel->log.c_tag = msg->tag_id;
                multiple_brokers = 1;
                mtunnel->c2c_pb_zrdt = route == br_route_pb_zrdt ? 1 : 0;
                mtunnel_unlock(mtunnel);
            }

        } else {
            ZPN_LOG(AL_ERROR, "%s: c2c broker_request for unsupported flow ", msg->mtunnel_id);

            res = C2C_NOT_AVAILABLE;
            goto FAIL;
        }

    } else {
        /* Stamp our ID and time onto this request */
        bucket_id = -1;
        msg->g_bfw = ZPN_BROKER_GET_GID();
        msg->bfw_us = epoch_us();

        mtunnel_id_len = strlen(msg->mtunnel_id);
        mtunnel_hash = CityHash64(msg->mtunnel_id, mtunnel_id_len);
        mtunnel = mtunnel_lookup_and_bucket_lock(msg->mtunnel_id, mtunnel_hash, &bucket_id);
        if (mtunnel) {
            mtunnel_lock(mtunnel);
        }

        if (msg->g_brk != msg->g_bfw) {
            // multiple public brokers
            multiple_brokers = 1;
            ZPN_LOG(AL_INFO, "c2c: %s: forward request from brokerA %" PRId64 " to brokerB %" PRId64, msg->mtunnel_id,
                    msg->g_brk, msg->g_bfw);

            if (!mtunnel) {
                mtunnel = bt_transit_mtunnel_create_client_no_lock(msg, a_state);
                if (NULL == mtunnel) {
                    ZPN_LOG(AL_ERROR, "c2c: failed to create mtunnel for %s ", msg->mtunnel_id);

                    res = C2C_FAIL_TO_CREATE_MTUNNEL;
                    goto FAIL;
                }
            } else {
                if ((msg->path_decision & ZPN_TX_PATH_DECISION_BRK_TX_TO_DSP_TIMEOUT_RETRY) != 0 ||
                    (mtunnel->path_decision & ZPN_TX_PATH_DECISION_BRK_TX_TO_DSP_TIMEOUT_RETRY) != 0) {
                    /* User broker sent 2 zpn_broker_request and both of them landed here - ignore the later one. */
                    mtunnel_unlock(mtunnel);
                    mtunnel_bucket_unlock(bucket_id);
                    ZPN_DEBUG_C2C("%s: 2 brk req came due to timeout retry - ignoring the later one", msg->mtunnel_id);
                    goto FREE;
                }
                ZPN_LOG(AL_ERROR, "c2c: tunnel exist unexpectedly %s", msg->mtunnel_id);

                res = C2C_MTUNNEL_BAD_STATE;
                goto FAIL;
            }
            ZPN_DEBUG_C2C("%s: transit tunnel created parent=%s", mtunnel->mtunnel_id, mtunnel->mtunnel_id_parent);
        } else {
            if (!mtunnel) {
                res = C2C_MTUNNEL_NOT_FOUND;
                goto FAIL;
            }
            mtunnel->log.c2c = 1;
            if (mtunnel->flag.c2c_received_brk_req == 1 &&
                (mtunnel->path_decision & ZPN_TX_PATH_DECISION_BRK_TX_TO_DSP_TIMEOUT_RETRY) != 0) {
                /* Broker had dispatched zpn_broker_request twice because of timeout retry - now both of them landed here - ignore the later one. */
                mtunnel_unlock(mtunnel);
                mtunnel_bucket_unlock(bucket_id);
                ZPN_DEBUG_C2C("%s: 2 brk req came due to timeout retry - ignoring the later one", msg->mtunnel_id);
                goto FREE;
            }
            mtunnel->flag.c2c_received_brk_req = 1;
            mtunnel_unlock(mtunnel);
            mtunnel_bucket_unlock(bucket_id);

            ZPN_DEBUG_C2C(" %s: c2c single broker %" PRId64, msg->mtunnel_id, msg->g_brk);
        }
    }
    // forward to destination client
    res = zpn_broker_client_forward_c2c_broker_request(mtunnel, multiple_brokers, a_state, msg);
    if (res) {
        ZPN_LOG(AL_ERROR, "%s: Failed to forward request to client '%s' %s", msg->mtunnel_id, msg->client_cname,
                zpn_result_string(res));
        res = C2C_MTUNNEL_FAILED_FORWARD;
        zpn_send_broker_req_ack_error_locked(
                a_state, msg, c2c_result_string(res, C2C_SUCCESS_FULL), mtunnel->allow_all_xport,
                mtunnel->mtunnel_id_parent ? mtunnel->mtunnel_id_parent : mtunnel->mtunnel_id);
    }

    goto FREE;
FAIL:

    if (mtunnel) {
        zpn_send_broker_req_ack_error_locked(
                a_state, msg, c2c_result_string(res, C2C_SUCCESS_FULL), mtunnel->allow_all_xport,
                mtunnel->mtunnel_id_parent ? mtunnel->mtunnel_id_parent : mtunnel->mtunnel_id);

        mtunnel_unlock(mtunnel);
        mtunnel_bucket_unlock(bucket_id);
    } else {
        // error occure before mtunnel was obtain/created
        zpn_send_broker_req_ack_error_locked(a_state, msg, c2c_result_string(res, C2C_SUCCESS_FULL), 1,
                                             msg->mtunnel_id);
    }

FREE:

    argo_object_release(msg_obj);

    return;
}

static int zpn_broker_request_ack_internal(struct zpn_broker_request_ack *ack)
{
    struct zpn_broker_mtunnel *mtunnel;
    size_t mtunnel_id_len;
    uint64_t mtunnel_id_hash;
    int bucket_id = -1;

    ZPN_BROKER_ASSERT_HARD(ack != NULL, "Broker dispatch: request ack is NULL");

    if (!ack->mtunnel_id) {
        ZPN_LOG(AL_ERROR, "Expecting mtunnel_id or tag_id in mtunnel_end request");
        return ZPN_RESULT_ERR;
    }

    mtunnel_id_len = strlen(ack->mtunnel_id);
    mtunnel_id_hash = CityHash64(ack->mtunnel_id, mtunnel_id_len);
    mtunnel = mtunnel_lookup_and_bucket_lock(ack->mtunnel_id, mtunnel_id_hash, &bucket_id);
    if (mtunnel) {
        mtunnel_lock(mtunnel);

        mtunnel->log.action = "close";
        mtunnel->assistant_id = ack->g_ast;
        mtunnel->server_group_id = ack->g_srv_grp;
        mtunnel->fwd_broker_id = ack->g_bfw;

        mtunnel_locked_destroy(mtunnel, 1, 0, 0, ack->error);
        mtunnel_locked_state_machine(mtunnel);
        mtunnel_unlock(mtunnel);
        mtunnel_bucket_unlock(bucket_id);
    } else {
        /* mtunnel probably has been deleted already. Just ignore the ack */
    }

    return ZPN_RESULT_NO_ERROR;
}

static int zpn_broker_dispatch_create_and_send_zpn_broker_request_ack(struct zpn_broker_request *msg, const char* error)
{
    struct zpn_broker_request_ack ack;
    int res = ZPN_RESULT_ERR;

    ZPN_BROKER_ASSERT_HARD(msg != NULL, "Broker dispatch: msg is NULL!");

    memset(&ack, 0, sizeof(ack));

    ack.g_ast = msg->g_ast;
    ack.g_app = msg->g_app;
    ack.g_aps = msg->g_aps;
    ack.g_brk = msg->g_brk;
    ack.g_dsp = msg->g_dsp;
    ack.g_bfw =  msg->g_bfw;
    ack.mtunnel_id = msg->mtunnel_id;
    ack.domain = msg->domain;
    ack.ip_protocol = msg->ip_protocol;
    ack.app_type = msg->app_type;
    ack.error = error;

    if(msg->g_brk == msg->g_bfw) {
        /* Control broker and User broker both are same. */
        /* Destroy the mtunnel and Send mtunnel_end to ZCC */
        res = zpn_broker_request_ack_internal(&ack);
    } else {
        /* Control broker will send broker_request_ack to Dispatcher to User Broker as CANNOT_FIND_CONNECTOR */
        res = zpn_broker_dispatch_send_request_ack(&ack);
    }

    return res;
}

enum zpn_err_brk_req zpn_broker_dispatch_get_last_disp_error(const char *mtunnel_id)
{
    struct zpn_broker_mtunnel *mtunnel;
    enum zpn_err_brk_req last_disp_error = zpn_err_brk_req_no_error;
    uint64_t hash;
    int bucket_id = -1;

    if (!mtunnel_id) {
        return last_disp_error;
    }

    hash = CityHash64(mtunnel_id, strnlen(mtunnel_id, ZPN_MTUNNEL_ID_BYTES_TEXT + 1));
    mtunnel = mtunnel_lookup_and_bucket_lock(mtunnel_id, hash, &bucket_id);
    if (!mtunnel) {
        ZPN_LOG(AL_ERROR, "%s: Message received from dispatcher but mtunnel not found", mtunnel_id);
        return last_disp_error;
    }

    mtunnel_lock(mtunnel);
    last_disp_error = mtunnel->last_disp_error;
    mtunnel_unlock(mtunnel);
    mtunnel_bucket_unlock(bucket_id);

    return last_disp_error;
}


/*
 * disp_in_conn can be NULL, in case of pool
 */
static int zpn_broker_request_common(struct argo_object *object,
                                     char *disp_desc,
                                     enum br_route route ) {

    struct zpn_broker_request *msg = object->base_structure_void;
    struct fohh_connection *out_conn;
    int64_t out_conn_incarnation;
    int res;

    char dump[8000];

    if ((zpn_debug_get(ZPN_DEBUG_MTUNNEL_IDX) || zpn_debug_get(ZPN_DEBUG_DISPATCHER_IDX)) &&
        argo_structure_dump(zpn_broker_request_description, msg, dump, sizeof(dump), NULL, 0) == ARGO_RESULT_NO_ERROR) {
        size_t len = strlen(dump);
        if (len && (dump[len - 1] == '\n'))
            dump[len - 1] = 0;
        ZPN_LOG(AL_DEBUG, "%s: Rx: from %s: %s", msg->mtunnel_id, disp_desc, dump);
    }
    if (zpn_meta_transaction_collection) {
        argo_log_structure_immediate(zpn_meta_transaction_collection,
                                     argo_log_priority_info,
                                     0,
                                     "BrkRqFromDsp",
                                     zpn_broker_request_description,
                                     msg);
    }

    // Don't process broker request messages for forwarded pre-learn broker requests.
    if (strcmp(msg->mtunnel_id, PB_PRE_LEARN_REQUEST_MTUNNEL_ID) == 0) {
        if (!msg->error) {
            ZPN_LOG(AL_CRITICAL, "%s: dispatcher is not supposed to send brk_req to ast brk on PEEK!!!", disp_desc);
        }
        return ZPN_RESULT_NO_ERROR;
    }

    if (msg->error) {
        /*
         * Some error has occured, Dispatcher sent us broker request with error,
         * processing as broker_req_ack as before
         */
        return zpn_broker_request_error(msg, disp_desc);
    }

    // zia inspection for private brokers
    if (ZPN_BROKER_IS_PRIVATE() && zins_check_inspection_private_broker_request(msg)) {
        // force promotion to public broker, where zia inspection will be  handled
        ZPN_LOG_ZINS(AL_INFO, "%s: ZIA INSPECTION, forcing promotion to public broker.  disp=%s", msg->mtunnel_id,
                     disp_desc);

        msg->error = "BRK_MT_ZIA_INSPECTION_PSE_2";
        return zpn_broker_request_error(msg, disp_desc);
    }

    msg->bfw_name = zpn_broker_get_instance_full_name();

    /* Normal broker request received from dispatcher */
    if (msg->g_ast) {
        /* request is for the connector, as g_ast has value */
        /* Stamp our ID and time onto this request */
        msg->g_bfw = ZPN_BROKER_GET_GID();
        msg->bfw_us = epoch_us();

        /* This is the normal client to connector mtunnel */
        res = zpn_broker_assistant_get_conn(msg->g_ast, &out_conn, &out_conn_incarnation);
        if (res) {
            if (ZPN_BROKER_IS_PRIVATE()) {
                // Couldn't find assistant's active control connection. Promoting to Public broker as 2 hop
                ZPN_DEBUG_MTUNNEL("%s: Could not find active control connection for assistant:%"PRId64", promoting to public broker",
                                msg->mtunnel_id, msg->g_ast);
                msg->error = "BRK_MT_SETUP_FAIL_CTRL_BRK_CANNOT_FIND_CONNECTOR";
                return zpn_broker_request_error(msg, disp_desc);
            }
            /* Control Broker will send zpn broker request ack to dispatcher */
            res = zpn_broker_dispatch_create_and_send_zpn_broker_request_ack(
                    msg, BRK_MT_SETUP_FAIL_CTRL_BRK_CANNOT_FIND_CONNECTOR);

            if (res) {
                ZPN_LOG(AL_ERROR,
                "%s: attempt to forward dispatch request for assistant %ld, from %s but lost the connection to the assistant. "
                "Failed to forward zpn_broker_request_ack to dispatcher, thus dropping the zpn_broker_request",
                msg->mtunnel_id, (long)msg->g_ast, disp_desc);
                return ZPN_RESULT_NO_ERROR;
            } else {
                ZPN_DEBUG_MTUNNEL("%s: zpn_broker_request_ack, err: %s forwarded to %s",
                    msg->mtunnel_id, BRK_MT_SETUP_FAIL_CTRL_BRK_CANNOT_FIND_CONNECTOR, disp_desc);
                return ZPN_RESULT_NO_ERROR;
            }
        }
        /* Since Dispatcher is not upgraded we are overriding the values on control broker*/
        char* app_domain = NULL;
        int64_t cst_id = ZPATH_GID_GET_CUSTOMER_GID(msg->g_app);
        if(zpn_broker_is_waf_enabled(msg->g_brk, cst_id) && !msg->g_app_domain) {
            /*
             * We need app_domain to compare with matched domain in zpn_inspection table to decide if
             * waf is disabled for this domain.
             */
            app_domain  = ZPN_CALLOC(MAX_APPLICATION_DOMAIN_LEN);
            if (zpn_is_dr_mode_active()) {
                msg->g_app_domain =  zpn_application_configured_domain_search_by_customer(ZPATH_GID_GET_CUSTOMER_GID(msg->g_app),
                                                                                          msg->domain, strnlen(msg->domain, MAX_DOMAIN_LEN_SIZE),
                                                                                          app_domain, NULL,
                                                                                          msg->ip_protocol, msg->c_port);
            } else {
                msg->g_app_domain =  zpn_application_configured_domain_search(msg->g_microtenant,
                                                                              msg->domain, strnlen(msg->domain, MAX_DOMAIN_LEN_SIZE),
                                                                              app_domain, NULL,
                                                                              msg->ip_protocol, msg->c_port);
            }
            msg->app_domain = app_domain;
        }
        res = zpn_send_zpn_broker_request_struct(out_conn, out_conn_incarnation, msg);
        if (res) {
            ZPN_LOG(AL_WARNING, "%s: Dispatch forward failed: %s", msg->mtunnel_id, zpn_result_string(res));
        } else {
            ZPN_DEBUG_MTUNNEL("%s: Dispatch forwarded to %s", msg->mtunnel_id, fohh_description(out_conn));
            if (zpn_meta_transaction_collection) {
                argo_log_structure_immediate(zpn_meta_transaction_collection,
                                             argo_log_priority_info,
                                             0,
                                             "BrkRqToAsst",
                                             zpn_broker_request_description,
                                             msg);
            }
        }
        if (app_domain) {
            ZPN_FREE(app_domain);
            app_domain = NULL;
        }
    } else if (eas_check_broker_request(msg)) {
        /* mtunnel for ZIA's ZVPN/IPSec aka extranet */
        const char *error_code = eas_send_broker_request_to_zvpn(object);

        if (error_code) {
            ZPN_LOG(AL_ERROR, "%s:Failed to sent ZVPN zpn_broker_request %s", msg->mtunnel_id ?: "", error_code);
            res = zpn_broker_dispatch_create_and_send_zpn_broker_request_ack(msg, error_code);
            if (res) {
                ZPN_LOG(AL_ERROR, "%s:Failed to sent ZVPN zpn_broker_request_ack %s", msg->mtunnel_id ?: "",
                        BRK_MT_BROKER_REQUEST_ZVPN_NOT_FOUND);
            }
        }

        return ZPN_RESULT_NO_ERROR;

    } else if (msg->client_cname) {
        /* This is c2c mtunnel */
        struct zpn_broker_client_fohh_state *a_state = NULL;
        int64_t a_state_incarnation;
        int thread_num;
        a_state = zpn_broker_find_connected_client_by_cname(msg->client_cname, &a_state_incarnation, &thread_num );
        if (NULL == a_state) {
            ZPN_LOG(AL_WARNING, "%s:recieved zpn_broker_request for client %s, but client is not found.",
                    msg->mtunnel_id, msg->client_cname);
            zpn_send_broker_req_ack_error_locked(NULL, msg, c2c_result_string(C2C_CLIENT_NOT_FOUND, C2C_SUCCESS_FULL), 1,
                                                 msg->mtunnel_id);

            return ZPN_RESULT_NO_ERROR;
        }

        struct argo_object * msg_obj = argo_object_create_from_object(object);

        if (zevent_base_big_call(fohh_thread_id_zevent_base(thread_num), process_c2c_broker_request_cb, a_state, route,
                                 msg_obj, NULL, NULL, a_state_incarnation)) {
            ZPN_LOG(AL_ERROR,"%s failed to run process_c2c_broker_request_cb",msg->mtunnel_id);
            zpn_send_broker_req_ack_error_locked(NULL, msg, c2c_result_string(C2C_CLIENT_CONN_INVALID_STATE, C2C_SUCCESS_FULL), 1,
                                                 msg->mtunnel_id);

            return ZPN_RESULT_NO_ERROR;
        }

    } else {
        ZPN_LOG(AL_ERROR, "%s: NOT SENDING BROKER REQUEST: broker request with no assistant, c2c or odc, the mtunnel will timeout %s", msg->mtunnel_id, disp_desc);
        return ZPN_RESULT_NO_ERROR;
    }
    return ZPN_RESULT_NO_ERROR;
}

static void zpn_broker_request_pool_cb(struct zevent_base *zbase, void *cookie1, int64_t cookie2) {
    struct argo_object *obj = cookie1;
    zpn_broker_request_common(obj, "[local dispatcher]", br_route_local_disp);
    // object was created in zpn_broker_request_local_cb
    // it can only be released here
    argo_object_release(obj);
}

static int zpn_broker_request_local_cb(struct fohh_connection *f_conn, int64_t f_conn_incarnation, struct zpn_broker_request *msg) {
    struct argo_object *obj = argo_object_create(zpn_broker_request_description, msg);
    if (!obj) {
        ZPN_LOG(AL_ERROR, "Could not create zpn_broker_request argo object");
        return ZPN_RESULT_NO_ERROR;
    }

    if (zevent_base_call(zevent_get_for_class(LOCAL_DISP_CB_THREAD_POOL), zpn_broker_request_pool_cb, obj, 0)) {
        ZPN_LOG(AL_ERROR, "Could not dispatch zpn_broker_request to callback thread pool");
        argo_object_release(obj);
    }

    return ZPN_RESULT_NO_ERROR;
}

/*
 * callback for broker_request, recieved via private broker client connection
 * used for PB c2c cases
 */
int zpn_c2c_pbroker_broker_request_fohh_cb(void *argo_cookie_ptr,
                                      void *argo_structure_cookie_ptr,
                                      struct argo_object *object) {

    zpn_broker_request_common(object,  "PB-FOHH", br_route_pb_fohh);

    return ZPN_RESULT_NO_ERROR;
}
int zpn_c2c_pbroker_broker_request_zrdt_cb(void *argo_cookie_ptr,
                                      void *argo_structure_cookie_ptr,
                                      struct argo_object *object) {

    zpn_broker_request_common(object, "PB-ZRDT", br_route_pb_zrdt);

    return ZPN_RESULT_NO_ERROR;
}
/*
 * Brokers receive broker requests only as intermediate delivery
 * systems or directly if there is error.
 */
static int zpn_broker_request_cb(void *argo_cookie_ptr,
                                 void *argo_structure_cookie_ptr,
                                 struct argo_object *object)
{
    struct fohh_connection *in_conn = argo_structure_cookie_ptr;

    return zpn_broker_request_common(object,fohh_description(in_conn), br_route_disp);
}

static int zpn_broker_app_route_info_common(struct zpn_app_route_info *msg, char *peer_cn, char *peer_desc) {
    struct zpn_broker_mtunnel *mtunnel;
    struct zpn_broker_dispatcher_stats *brk_dsp_stats = NULL;
    char dump[8000];
    size_t mtunnel_len;
    uint64_t hash;
    int c2c = 0;
    int bucket_id = -1;

    if (zpn_meta_transaction_collection) {
        argo_log_structure_immediate(zpn_meta_transaction_collection,
                                     argo_log_priority_info,
                                     0,
                                     "AppRouteInfo",
                                     zpn_app_route_info_description,
                                     msg);
    }

    if (ZPN_BROKER_IS_PUBLIC()) {
        __sync_add_and_fetch_8(&broker_dispatcher_stats.zpn_brk_req_response_received, 1);
        brk_dsp_stats = zpn_broker_dispatcher_get_stats(msg->g_dsp);
        if (brk_dsp_stats) {
            __sync_add_and_fetch_8(&(brk_dsp_stats->zpn_brk_req_response_received), 1);
        }
    }

    if(msg->app_type != NULL ) {
        c2c = 0 == strncmp(msg->app_type,"c2c",3) ? 1:0;
    }

    if (!msg->mtunnel_id) {
        ZPN_LOG(AL_ERROR, "%s: Received route_info without mtunnel ID, c2c=%d", peer_desc,c2c);
        return ZPN_RESULT_NO_ERROR;
    }

    if (zpn_debug_get(ZPN_DEBUG_MTUNNEL_IDX) || zpn_debug_get(ZPN_DEBUG_DISPATCHER_IDX)) {
        if (argo_structure_dump(zpn_app_route_info_description, msg, dump, sizeof(dump), NULL, 0) == ARGO_RESULT_NO_ERROR) {
            size_t len = strlen(dump);
            if (len && (dump[len - 1] == '\n')) dump[len - 1] = 0;
            ZPN_LOG(AL_DEBUG, "%s: Rx: from %s c2c=%d: %s", msg->mtunnel_id, peer_cn, c2c, dump);
        }
    }

    // Don't process info messages for forwarded pre-learn broker requests.
    if (strcmp(msg->mtunnel_id, PB_PRE_LEARN_REQUEST_MTUNNEL_ID) == 0) {
        return ZPN_RESULT_NO_ERROR;
    }

    mtunnel_len = strlen(msg->mtunnel_id);
    if (mtunnel_len < ZPN_MTUNNEL_ID_BYTES_TEXT_MIN) {
        ZPN_LOG(AL_WARNING, "%s: zpn_app_route_info received with invalid mtunnel_id, c2c=%d from %s", msg->mtunnel_id, c2c, peer_desc);
        return ZPN_RESULT_NO_ERROR;
    }

    hash = CityHash64(msg->mtunnel_id, strlen(msg->mtunnel_id));
    mtunnel = mtunnel_lookup_and_bucket_lock(msg->mtunnel_id, hash, &bucket_id);
    if (!mtunnel) {
        ZPN_LOG(AL_WARNING, "%s: zpn_app_route_info received, but mtunnel not found from %s c2c=%d", msg->mtunnel_id, peer_desc,c2c);
        return ZPN_RESULT_NO_ERROR;
    }

    /* We are now locked. */
    mtunnel_lock(mtunnel);

    mtunnel->flag.response_received_from_disp = 1;
    /* Update stats if re-dispatched to diff DC due to timeout and now received the response. */
    if (ZPN_BROKER_IS_PUBLIC() &&
        mtunnel->flag.timeout_redispatch_to_diff_dc &&
        mtunnel->num_route_info_received_from_disp == 0) {
        /* Case when dispatchr A didn't respond within 2 sec and we had re-dispatched to dispatcher B in different DC. */
        if (msg->g_dsp == mtunnel->last_dispatcher_id) {
            /* We got the route info because of dispatcher A. */
            __sync_add_and_fetch_8(&broker_dispatcher_stats.zpn_brk_req_redispatch_timeout_first_disp_success, 1);
            if (brk_dsp_stats) {
                __sync_add_and_fetch_8(&(brk_dsp_stats->zpn_brk_req_redispatch_timeout_first_disp_success), 1);
            }
        } else {
            /* We got the route info because of dispatcher B. */
            __sync_add_and_fetch_8(&broker_dispatcher_stats.zpn_brk_req_redispatch_timeout_second_disp_success, 1);
            if (brk_dsp_stats) {
                __sync_add_and_fetch_8(&(brk_dsp_stats->zpn_brk_req_redispatch_timeout_second_disp_success), 1);
            }
        }
        __sync_add_and_fetch_8(&broker_dispatcher_stats.zpn_brk_req_redispatch_timeout_success, 1);
        if (brk_dsp_stats) {
            __sync_add_and_fetch_8(&(brk_dsp_stats->zpn_brk_req_redispatch_timeout_success), 1);
        }
    }
    mtunnel->num_route_info_received_from_disp++;

    if (ZPN_BROKER_IS_PUBLIC() &&
        mtunnel->flag.timeout_redispatch_to_diff_dc &&
        mtunnel->state > zbms_dispatch_sent) {
        /*
         * We had re-dispatched to diff DC due to timeout and now the mtunnel is already complete because of
         * another dispatcher. So no need to process this route info now - ignore.
         */
        ZPN_DEBUG_MTUNNEL("%s: Received route info from %"PRId64" dispatcher but mtunnel is already complete - ignoring",
                          msg->mtunnel_id, msg->g_dsp);
        goto done;
    }

    /*
     * Update re-dispatch to different DC success counter.
     * We're on user broker and zpn_app_route_info indicates the brk req was successful.
     */
    if (ZPN_BROKER_IS_PUBLIC()) {
        if (mtunnel->brk_req_redispatch_to_diff_dc_reason == zpn_err_brk_req_no_connector_available) {
            __sync_add_and_fetch_8(&broker_dispatcher_stats.zpn_brk_req_redispatch_no_connector_success, 1);
            if (brk_dsp_stats) {
                __sync_add_and_fetch_8(&(brk_dsp_stats->zpn_brk_req_redispatch_no_connector_success), 1);
            }
        } else if (mtunnel->brk_req_redispatch_to_diff_dc_reason == zpn_err_brk_req_invalid_domain) {
            __sync_add_and_fetch_8(&broker_dispatcher_stats.zpn_brk_req_redispatch_invalid_domain_success, 1);
            if (brk_dsp_stats) {
                __sync_add_and_fetch_8(&(brk_dsp_stats->zpn_brk_req_redispatch_invalid_domain_success), 1);
            }
        }
    }

    mtunnel->log.g_dsp = msg->g_dsp;
    mtunnel->log.rt_info_tx_us = msg->dsp_tx_us;
    mtunnel->log.rt_info_rx_us = epoch_us();
    mtunnel->log.c2c = c2c;

    eas_on_app_route_info_locked(mtunnel, msg);

    if (c2c) {
        // collect c2c stat
        zpn_fohh_worker_c2c_mtunnel(mtunnel->c_state_fohh_thread_id);
    }

    /* We will generally be in one of two states here, with quite different behaviors.
     * If broker request was for connector app, g_ast would be not 0
     * For client apps, app_type is populated with 'c2c'
     * For extranet apps, zia_instance_id would not be 0
    */
    if (!msg->g_ast && (mtunnel->log.c2c == 0) && (msg->zia_instance_id == 0)) {
        //
        /* Needed connector info! That was the whole
         * point! */
        mtunnel_locked_destroy(mtunnel, 1, 0, 0, BRK_MT_SETUP_FAIL_PROBE);
        mtunnel_locked_state_machine(mtunnel);
    } else {
        /* In this case, we can stamp the info from this message such
         * that it isn't lost, in case something goes wrong
         * downstream */
        mtunnel->assistant_id = msg->g_ast;

        if (mtunnel->assistant_id) {
            struct zpn_assistantgroup_assistant_relation *ag_relation = NULL;
            size_t count = 1;

            zpn_assistantgroup_assistant_relation_get_by_assistant(mtunnel->assistant_id,
                                                                   &ag_relation,
                                                                   &count,
                                                                   NULL,
                                                                   NULL,
                                                                   0);
            if (ag_relation) {
                mtunnel->assistant_group_id = ag_relation->assistant_group_id;
            }
        }

        mtunnel->log.g_ast = msg->g_ast;
        mtunnel->log.g_ast_grp = mtunnel->assistant_group_id;
        mtunnel->application_server_id = msg->g_aps;
        mtunnel->log.g_aps = msg->g_aps;

    }

done:
    mtunnel_unlock(mtunnel);
    mtunnel_bucket_unlock(bucket_id);

    return ZPATH_RESULT_NO_ERROR;
}

static void zpn_broker_app_route_info_pool_cb(struct zevent_base *zbase, void *cookie1, int64_t cookie2) {
    struct argo_object *obj = cookie1;
    zpn_broker_app_route_info_common(obj->base_structure_void, "[local dispatcher]", "[local dispatcher]");
    argo_object_release(obj);
}

int zpn_broker_app_route_info_local_cb(struct fohh_connection *f_conn,
                                       int64_t f_conn_incarnation,
                                       struct zpn_app_route_info *msg) {
    struct argo_object *obj = argo_object_create(zpn_app_route_info_description, msg);
    if (!obj) {
        ZPN_LOG(AL_ERROR, "Could not create zpn_app_route_info argo object");
        return ZPN_RESULT_NO_ERROR;
    }

    if (zevent_base_call(zevent_get_for_class(LOCAL_DISP_CB_THREAD_POOL), zpn_broker_app_route_info_pool_cb, obj, 0)) {
        ZPN_LOG(AL_ERROR, "Could not dispatch zpn_app_route_info to callback thread pool");
        argo_object_release(obj);
    }

    return ZPN_RESULT_NO_ERROR;
}

static int zpn_broker_app_route_info_cb(void *argo_cookie_ptr,
                                        void *argo_structure_cookie_ptr,
                                        struct argo_object *object)
{
    struct fohh_connection *f_conn = argo_structure_cookie_ptr;
    struct zpn_app_route_info *info = object->base_structure_void;

    return zpn_broker_app_route_info_common(info, fohh_peer_cn(f_conn), fohh_description(f_conn));
}

/*
 * Brokers receive broker request acks on failure. But they can be an
 * intermediary for the message delivery, or it can be for them
 * directly.
 */
static int zpn_broker_request_ack_cb(void *argo_cookie_ptr,
                                     void *argo_structure_cookie_ptr,
                                     struct argo_object *object)
{
    struct fohh_connection *f_conn = argo_structure_cookie_ptr;
    struct zpn_broker_mtunnel *mtunnel;
    size_t mtunnel_len;
    uint64_t hash;
    struct zpn_broker_request_ack *ack = object->base_structure_void;
    char dump[8000];
    int bucket_id = -1;

    if (!ack->mtunnel_id) {
        ZPN_LOG(AL_ERROR, "%s: Received ack without mtunnel ID", fohh_description(f_conn));
        return ZPN_RESULT_NO_ERROR;
    }

    if (zpn_debug_get(ZPN_DEBUG_MTUNNEL_IDX) || zpn_debug_get(ZPN_DEBUG_DISPATCHER_IDX)) {
        if (argo_object_dump(object, dump, sizeof(dump), NULL, 0) == ARGO_RESULT_NO_ERROR) {
            ZPN_LOG(AL_DEBUG, "%s: Rx: %s", ack->mtunnel_id, dump);
        }
    }

    if (ack->g_brk != broker_instance_id) {
        ZPN_LOG(AL_CRITICAL, "Broker request ack received for different broker. Implement me.");
        return ZPN_RESULT_NO_ERROR;
    }

    mtunnel_len = strlen(ack->mtunnel_id);
    if (mtunnel_len < ZPN_MTUNNEL_ID_BYTES_TEXT_MIN) {
        ZPN_LOG(AL_WARNING, "Broker request ack received with invalid mtunnel_id = %s from %s", ack->mtunnel_id, fohh_description(f_conn));
        return ZPN_RESULT_NO_ERROR;
    }

    if (!ack->error) {
        ZPN_LOG(AL_WARNING, "%s: Broker request ack received without error? from %s", ack->mtunnel_id, fohh_description(f_conn));
        return ZPN_RESULT_NO_ERROR;
    }

    hash = CityHash64(ack->mtunnel_id, strlen(ack->mtunnel_id));
    mtunnel = mtunnel_lookup_and_bucket_lock(ack->mtunnel_id, hash, &bucket_id);
    if (!mtunnel) {
        ZPN_LOG(AL_WARNING, "%s: Broker request ack received, but mtunnel not found from %s", ack->mtunnel_id, fohh_description(f_conn));
        return ZPN_RESULT_NO_ERROR;
    }

    mtunnel_lock(mtunnel);

    if (ZPN_BROKER_IS_PUBLIC() && mtunnel->flag.timeout_redispatch_to_diff_dc) {
        if (strcmp(ack->error, AST_MT_SETUP_ERR_DUP_MT_ID) == 0) {
            /* We had sent zpn_broker_request to 2 dispatchers, so old AC can complain about the duplicate mtunnel id - ignore. */
            ZPN_DEBUG_MTUNNEL("%s: Received %s error from %"PRId64" AC, could be because we sent 2 brk req due to timeout and both landed on same AC - ignoring",
                              mtunnel->mtunnel_id, ack->error, ack->g_ast);
            mtunnel_unlock(mtunnel);
            mtunnel_bucket_unlock(bucket_id);
            return ZPN_RESULT_NO_ERROR;
        } else if (mtunnel->state > zbms_dispatch_sent) {
            /*
             * We had re-dispatched to diff DC due to timeout and now the mtunnel is already complete,
             * no need to process this error coming from AC now.
             */
            ZPN_DEBUG_MTUNNEL("%s: Received %s error from AC but mtunnel is already complete - ignoring",
                              mtunnel->mtunnel_id, ack->error);
            mtunnel_unlock(mtunnel);
            mtunnel_bucket_unlock(bucket_id);
            return ZPN_RESULT_NO_ERROR;
        }
    }

    mtunnel->assistant_id = ack->g_ast;
    mtunnel->assistant_group_id = ack->g_ast_grp;

    mtunnel->log.action = "close";
    mtunnel->log.a_ip = ack->a_inet;
    mtunnel->log.a_port = ack->a_port;
    mtunnel->log.server_us = ack->server_us;
    mtunnel->fwd_broker_id = ack->g_bfw;
    if (ack->s_inet.length) {
            mtunnel->log.s_ip = ack->s_inet;
            mtunnel->log.s_port = ack->s_port;
    }
    mtunnel->server_group_id = ack->g_srv_grp;
    mtunnel->log.bind_ast_tx_us = ack->ast_bind_tx_us;
    mtunnel->log.brk_req_ack_ast_tx_us = ack->ast_tx_us;
    mtunnel->log.brk_req_ack_cbrk_tx_us = ack->bfw_tx_us;
    mtunnel->log.brk_req_ack_dsp_tx_us = ack->dsp_tx_us;
    mtunnel->log.brk_req_ack_rx_us = epoch_us();
    mtunnel->path_decision |= ack->path_decision;
    zpn_tx_path_decision_get_str(mtunnel->path_decision, mtunnel->log.path_decision,
                                 sizeof(mtunnel->log.path_decision));
    mtunnel->log.g_dsp = ack->g_dsp;


    mtunnel_locked_destroy(mtunnel, 1, 0, 0, ack->error);
    mtunnel_locked_state_machine(mtunnel);

    mtunnel_unlock(mtunnel);
    mtunnel_bucket_unlock(bucket_id);

    return ZPN_RESULT_NO_ERROR;
}

static int zpn_broker_request_ack_local_cb(struct fohh_connection *f_conn, int64_t f_conn_incarnation, struct zpn_broker_request_ack *msg) {
    ZPN_LOG(AL_ERROR, "local dispatcher is not supposed to send zpn_broker_request_ack");
    zthread_assert_text("local dispatcher is not supposed to send zpn_broker_request_ack");
    return ZPN_RESULT_NOT_IMPLEMENTED;
}

int zpn_broker_dispatch_send_and_log_learn_frame(int fohh_thread_id, struct zpn_app_route_discovery *disc)
{
    struct fohh_connection *out_conn;
    int64_t out_conn_incarnation;
    int res;
    res = zpn_broker_assistant_get_conn(disc->g_ast,
                                        &out_conn,
                                        &out_conn_incarnation);
    if (res) {
        ZPN_LOG(AL_WARNING, "Failed to find connector %ld: %s", (long) disc->g_ast, zpath_result_string(res));
        fohh_stats[fohh_thread_id]->zpn_broker_dispatch_find_connector_failed++;
        return ZPN_RESULT_ERR;
    }

    ZPN_BROKER_ASSERT_SOFT(disc->lifetime >= 0,
                           "Broker discover message from dsp = %ld with unexpected lifetime, app = %ld lifetime = %ld",
                           (long)disc->g_app, (long)disc->g_dsp, (long)disc->lifetime);

    disc->cbrk_tx_us = zpn_cloud_adjusted_epoch_us();
    res = zpn_send_zpn_app_route_discovery_struct(out_conn,
                                                  out_conn_incarnation,
                                                  disc);
    if (res) {
        ZPN_LOG(AL_WARNING, "Attempt to forward route discovery(%s) for assistant %ld, app = %ld, domain = %s, but "
                            "transmit failed. : %s",
                disc->disc_type,
                (long) disc->g_ast,
                (long) disc->g_app,
                disc->domain,
                zpath_result_string(res));
        fohh_stats[fohh_thread_id]->zpn_broker_dispatch_send_discover_frame_failed++;
        return ZPN_RESULT_ERR;
    } else {
        ZPN_DEBUG_DISPATCHER("Forwarded route discovery(%s) for assistant %ld, app = %ld, domain = %s.",
                             disc->disc_type,
                             (long) disc->g_ast,
                             (long) disc->g_app,
                             disc->domain);

        if (zpn_health_collection) {
            if ((res = argo_log_structure_immediate(zpn_health_collection,
                                                    argo_log_priority_info,
                                                    0,
                                                    "learn",
                                                    zpn_app_route_discovery_description,
                                                    disc))) {
                ZPN_LOG(AL_WARNING, "Could not log learn frame message: %s", zpath_result_string(res));
            }
        }
    }

    return ZPN_RESULT_NO_ERROR;
}

static int zpn_broker_app_route_discovery_common(struct zpn_app_route_discovery *msg, int64_t dsp_id, int fohh_thread_id, char *peer_desc) {
    int res;

    msg->g_brk = ZPN_BROKER_GET_GID();
    if (dsp_id) msg->g_dsp = dsp_id;
    fohh_stats[fohh_thread_id]->zpn_broker_dispatch_receive_discover_frame++;
    res = zpn_broker_dispatch_send_and_log_learn_frame(fohh_thread_id, msg);
    if (res) {
        ZPN_LOG(AL_WARNING, "%s: Attempt to forward route discovery(%s) for assistant %ld, app = %ld, domain = %s, but transmit failed.",
                peer_desc,
                msg->disc_type,
                (long) msg->g_ast,
                (long) msg->g_app,
                msg->domain);
    } else {
        ZPN_DEBUG_DISPATCHER("%s: Forwarded route discovery(%s) for assistant %ld, app = %ld, domain = %s.",
                             peer_desc,
                             msg->disc_type,
                             (long) msg->g_ast,
                             (long) msg->g_app,
                             msg->domain);
        fohh_stats[fohh_thread_id]->zpn_broker_dispatch_send_discover_frame++;
    }
    return ZPN_RESULT_NO_ERROR;
}

static void zpn_broker_app_route_discovery_pool_cb(struct zevent_base *zbase, void *cookie1, int64_t cookie2) {
    struct argo_object *obj = cookie1;
    zpn_broker_app_route_discovery_common(obj->base_structure_void, ZPN_BROKER_GET_GID(), 0, "[local dispatcher]");
    argo_object_release(obj);
}

static int zpn_broker_app_route_discovery_local_cb(struct fohh_connection *f_conn, int64_t f_conn_incarnation, struct zpn_app_route_discovery *msg) {
    struct argo_object *obj = argo_object_create(zpn_app_route_discovery_description, msg);
    if (!obj) {
        ZPN_LOG(AL_ERROR, "Could not create zpn_app_route_discovery argo object");
        return ZPN_RESULT_NO_ERROR;
    }

    if (zevent_base_call(zevent_get_for_class(LOCAL_DISP_CB_THREAD_POOL), zpn_broker_app_route_discovery_pool_cb, obj, 0)) {
        ZPN_LOG(AL_ERROR, "Could not dispatch zpn_app_route_discovery to callback thread pool");
        argo_object_release(obj);
    }

    return ZPN_RESULT_NO_ERROR;
}

static int zpn_broker_app_route_discovery_cb(void *argo_cookie_ptr,
                                             void *argo_structure_cookie_ptr,
                                             struct argo_object *object)
{
    struct zpn_app_route_discovery *disc = object->base_structure_void;
    struct fohh_connection *in_conn = argo_structure_cookie_ptr;
    struct zpn_broker_dispatcher *dispatcher = fohh_connection_get_dynamic_cookie(in_conn);
    int64_t dsp_id = dispatcher ? dispatcher->instance_id : 0;

    return zpn_broker_app_route_discovery_common(disc, dsp_id, fohh_connection_get_thread_id(in_conn), fohh_description(in_conn));
}

int zpn_dns_dispatch_check_common(struct zpn_dns_dispatch_check *msg, const char *peer_cn) {
    char dump[8000];
    int res;
    int is_local_dispatch_resp = 0;

    if (zpn_debug_get(ZPN_DEBUG_DISPATCHER_IDX)) {
        if (argo_structure_dump(zpn_dns_dispatch_check_description, msg, dump, sizeof(dump), NULL, 0) == ARGO_RESULT_NO_ERROR) {
            ZPN_LOG(AL_DEBUG, "%s: Rx: %s", peer_cn, dump);
        }
    }

    // Drop negative dns reply from local dispatcher to avoid false negative replies to clients when local dispatcher is faster.
    // Note: dns check is sent concurrently to both local and global dispatchers.
    if (msg->error && ZPN_BROKER_IS_PRIVATE()) {
         __sync_add_and_fetch(&(pse_dns_disp_stats.total_dns_local_dispatch_err_resp),1);
        return ZPN_RESULT_NO_ERROR;
    }

    if (!ZPN_DNS_CHECK_TARGETS_EQUAL(msg)) {
        ZPN_LOG(AL_ERROR, "Bad check counts");
        return ZPN_RESULT_ERR;
    }

    if (!msg->tunnel_id) {
        ZPN_LOG(AL_ERROR, "Nonexistent tunnel_id");
        return ZPN_RESULT_ERR;
    }

    if (strcmp(peer_cn, LOCAL_DISP_CN_NAME) == 0) {
        is_local_dispatch_resp = 1;
    } else {
        is_local_dispatch_resp = 0;
    }

    res = zpn_broker_dns_response_from_dispatcher(msg, is_local_dispatch_resp);

    if (res) {
        if (argo_structure_dump(zpn_dns_dispatch_check_description, msg, dump, sizeof(dump), NULL, 0) == ARGO_RESULT_NO_ERROR) {
            ZPN_DEBUG_DISPATCHER("%s: Could not process dispatcher dns_check: %s", peer_cn, dump);
        }
    }

    return ZPN_RESULT_NO_ERROR;
}

static int zpn_dns_dispatch_check_cb(void *argo_cookie_ptr,
                                     void *argo_structure_cookie_ptr,
                                     struct argo_object *object)
{
    struct fohh_connection *f_conn = argo_structure_cookie_ptr;
    struct zpn_dns_dispatch_check *check = object->base_structure_void;

    return zpn_dns_dispatch_check_common(check, fohh_peer_cn(f_conn));
}

static void zpn_dns_dispatch_check_pool_cb(struct zevent_base *zbase, void *cookie1, int64_t cookie2) {
    struct argo_object *obj = cookie1;
    __sync_add_and_fetch(&(pse_dns_disp_stats.total_dns_local_dispatch_resp),1);

    zpn_dns_dispatch_check_common(obj->base_structure_void, LOCAL_DISP_CN_NAME);
    argo_object_release(obj);
}

static int zpn_dns_dispatch_check_local_cb(struct fohh_connection *f_conn,
                                           int64_t f_conn_incarnation,
                                           struct zpn_dns_dispatch_check *msg) {
    struct argo_object *obj = argo_object_create(zpn_dns_dispatch_check_description, msg);
    if (!obj) {
        ZPN_LOG(AL_ERROR, "Couldn't create zpn_dns_dispatch_check argo object");
        return ZPN_RESULT_NO_ERROR;
    }

    if (zevent_base_call(zevent_get_for_class(LOCAL_DISP_CB_THREAD_POOL), zpn_dns_dispatch_check_pool_cb, obj, 0)) {
        ZPN_LOG(AL_ERROR, "Could not dispatch zpn_dns_dispatch_check to callback thread pool");
        argo_object_release(obj);
    }

    return ZPN_RESULT_NO_ERROR;
}

static int zpn_dns_assistant_check_common(struct zpn_dns_assistant_check *msg, char *peer_cn) {
    char dump[8000];
    int64_t g_cst = 0;
    int res = ZPN_RESULT_NO_ERROR;
    int is_partner_app = 0;
    int is_local_app = 0;

    if (zpn_debug_get(ZPN_DEBUG_DISPATCHER_IDX)) {
        if (argo_structure_dump(zpn_dns_assistant_check_description, msg, dump, sizeof(dump), NULL, 0) == ARGO_RESULT_NO_ERROR) {
            ZPN_LOG(AL_DEBUG, "%s: Rx: %s", peer_cn, dump);
        }
    }

    /* Either it is partner app or it is local app */
    if (msg->zia_partner_id > 0 && msg->zia_location_id > 0 && msg->zia_instance_id > 0 &&
        msg->zia_tunnel_id > 0 && msg->zia_org_id > 0 && msg->zia_cloud_name) {
        is_partner_app = 1;
        if (!msg->g_cst) {
            if (argo_structure_dump(zpn_dns_assistant_check_description, msg, dump, sizeof(dump), NULL, 0) == ARGO_RESULT_NO_ERROR) {
                ZPN_LOG(AL_CRITICAL, "%s: Rx Missing g_cst in the partner app response: %s", peer_cn, dump);
            }
            return ZPN_RESULT_NO_ERROR;
        }
    } else if (msg->g_ast) {
        is_local_app = 1;
    } else {
        // Don't know where to route, so bailing out
        if (argo_structure_dump(zpn_dns_assistant_check_description, msg, dump, sizeof(dump), NULL, 0) == ARGO_RESULT_NO_ERROR) {
            ZPN_LOG(AL_WARNING, "%s: Rx neither partner or local app incomplete zpn_dns_assistant_check: %s", peer_cn, dump);
        }
        return ZPN_RESULT_NO_ERROR;
    }

    if ((!msg->g_app) ||
        (!msg->name) ||
        (!msg->type)) {
        if (argo_structure_dump(zpn_dns_assistant_check_description, msg, dump, sizeof(dump), NULL, 0) == ARGO_RESULT_NO_ERROR) {
            ZPN_LOG(AL_WARNING, "%s: Rx incomplete zpn_dns_assistant_check: %s", peer_cn, dump);
        }
        return ZPN_RESULT_NO_ERROR;
    }

    /* Verify the requested app/assistant are consistent */
    if (is_local_app) g_cst = ZPATH_GID_GET_CUSTOMER_GID(msg->g_ast);
    if (is_partner_app) g_cst = msg->g_cst;
    if (g_cst != ZPATH_GID_GET_CUSTOMER_GID(msg->g_app)) {
        if (argo_structure_dump(zpn_dns_assistant_check_description, msg, dump, sizeof(dump), NULL, 0) == ARGO_RESULT_NO_ERROR) {
            ZPN_LOG(AL_CRITICAL, "%s: Rx mismatch g_ast/g_app: %s", peer_cn, dump);
        }
        return ZPN_RESULT_NO_ERROR;
    }

    /* Re-verify the app exists in the set of apps the customer has to
     * make sure the dispatcher isn't doing something wrong/rude?  May
     * as well.. */
    if (zpn_application_search_by_customer(g_cst, msg->name, strnlen(msg->name, MAX_DOMAIN_LEN_SIZE), NULL, 0, NULL) != ZPN_RESULT_NO_ERROR) {
        if (argo_structure_dump(zpn_dns_assistant_check_description, msg, dump, sizeof(dump), NULL, 0) == ARGO_RESULT_NO_ERROR) {
            ZPN_LOG(AL_CRITICAL, "%s: Rx, but app not found: %s", peer_cn, dump);
        }
        return ZPN_RESULT_NO_ERROR;
    }

    if (is_local_app) {
        res = zpn_broker_assistant_send_dns_check(msg);
    } else if (is_partner_app) {
        res = zpn_broker_eas_send_dns_check_to_zvpn(msg);
    }

    if (res == ZPN_RESULT_NOT_FOUND) {
        ZPN_LOG(AL_WARNING, "%s: Assistant %ld not found for DNS check %ld: %s %s",
                peer_cn,
                (long) msg->g_ast,
                (long) msg->g_app,
                msg->name ? msg->name : "NULL",
                msg->type ? msg->type : "NULL");
    } else if (res) {
        ZPN_LOG(AL_WARNING, "%s: Could not send to assistant %ld a DNS check %ld: %s %s",
                peer_cn,
                (long) msg->g_ast,
                (long) msg->g_app,
                msg->name ? msg->name : "NULL",
                msg->type ? msg->type : "NULL");
    }

    return ZPN_RESULT_NO_ERROR;
}

static int zpn_dns_assistant_check_cb(void *argo_cookie_ptr,
                                      void *argo_structure_cookie_ptr,
                                      struct argo_object *object)
{
    struct zpn_dns_assistant_check *check_in = object->base_structure_void;
    struct fohh_connection *f_conn = argo_structure_cookie_ptr;

    return zpn_dns_assistant_check_common(check_in, fohh_peer_cn(f_conn));
}

static void zpn_dns_assistant_check_pool_cb(struct zevent_base *zbase, void *cookie1, int64_t cookie2) {
    struct argo_object *obj = cookie1;
    zpn_dns_assistant_check_common(obj->base_structure_void, "[local dispatcher]");
    argo_object_release(obj);
}

static int zpn_dns_assistant_check_local_cb(struct fohh_connection *f_conn, int64_t f_conn_incarnation, struct zpn_dns_assistant_check *msg) {
    struct argo_object *obj = argo_object_create(zpn_dns_assistant_check_description, msg);
    if (!obj) {
        ZPN_LOG(AL_ERROR, "Couldn't create zpn_dns_assistant_check argo object");
        return ZPN_RESULT_NO_ERROR;
    }

    if (zevent_base_call(zevent_get_for_class(LOCAL_DISP_CB_THREAD_POOL), zpn_dns_assistant_check_pool_cb, obj, 0)) {
        ZPN_LOG(AL_ERROR, "Could not dispatch zpn_dns_assistant_check to callback thread pool");
        argo_object_release(obj);
    }

    return ZPN_RESULT_NO_ERROR;
}

static int zpn_dispatcher_status_cb(void *argo_cookie_ptr,
                                    void *argo_structure_cookie_ptr,
                                    struct argo_object *object)
{
    struct zpn_dispatcher_status *status = object->base_structure_void;
    struct fohh_connection *f_conn = argo_structure_cookie_ptr;
    struct zpn_broker_dispatcher *dispatcher = fohh_connection_get_dynamic_cookie(f_conn);
    int64_t now_s = epoch_s();
    if (zpn_debug_get(ZPN_DEBUG_DISPATCHER_IDX)) {
        char dump[2000];
        if (argo_object_dump(object, dump, sizeof(dump), NULL, 0) == ARGO_RESULT_NO_ERROR) {
            size_t len = strlen(dump);
            if (len && (dump[len - 1] == '\n')) dump[len - 1] = 0;
            ZPN_LOG(AL_DEBUG, "%s: Rx: from %s: %s", fohh_description(f_conn), fohh_peer_cn(f_conn), dump);
        }
    }

    if (!dispatcher) {
        ZPN_LOG(AL_ERROR, "%s: No dispatcher on callback", fohh_description(f_conn));
        return ZPN_RESULT_ERR;
    }

    ZPATH_MUTEX_LOCK(&(dispatcher->status_lock), __FILE__, __LINE__);
    dispatcher->epoch_started = status->app_start_epoch;
    dispatcher->block_app_reg_msg = status->block_app_reg_msg;
    dispatcher->last_dsp_status_rcvd_s = now_s;

    if (status->ready_status == dsp_status_not_ready) {
        /* Not ready */
        ZPN_DEBUG_DISPATCHER("%s: Received ready_status = %d (NOT_READY), marking dsp as not ready", fohh_description(f_conn), status->ready_status);
        dispatcher->ready = 0;
    } else if (status->ready_status == dsp_status_ready) {
        /* Ready */
        ZPN_DEBUG_DISPATCHER("%s: Received ready_status = %d (READY), marking dsp as ready", fohh_description(f_conn), status->ready_status);
        dispatcher->ready = 1;
    } else {
        /* Old dispatcher, fall back to earlier logic. */
        dispatcher->is_old_dsp = 1;
        if ((now_s - status->app_start_epoch) > zpn_dispatcher_hold_time_get()) {
            ZPN_DEBUG_DISPATCHER("%s: Dispatcher started %"PRId64" sec ago that it is immediately ready", fohh_description(f_conn), (now_s - status->app_start_epoch));
            dispatcher->ready = 1;
        } else {
            ZPN_DEBUG_DISPATCHER("%s: Dispatcher started only %"PRId64" sec ago, and cannot be immediately made ready", fohh_description(f_conn), (now_s - status->app_start_epoch));
            dispatcher->ready = 0;
        }
    }

    ZPATH_MUTEX_UNLOCK(&(dispatcher->status_lock), __FILE__, __LINE__);
    return ZPN_RESULT_NO_ERROR;
}

static int zpn_dispatcher_status_local_cb(struct fohh_connection *f_conn, int64_t f_conn_incarnation, struct zpn_dispatcher_status *msg) {
    ZPN_LOG(AL_ERROR, "local dispatcher is not supposed to send zpn_dispatcher_status");
    zthread_assert_text("local dispatcher is not supposed to send zpn_dispatcher_status");
    return ZPN_RESULT_NOT_IMPLEMENTED;
}

int zpn_broker_dispatch_send_c2c_app_check(struct zpn_broker_dispatcher_c2c_app_check *check_out, enum zpn_err_brk_req last_disp_error, int64_t *last_disp_id)
{
    int res = 0;
    int is_ckt_breaker = 0;

    struct zpn_broker_dispatcher *dispatcher = zpn_brk_dsp_circuit_breaker_get_best_dsp(check_out->customer_gid, last_disp_error, *last_disp_id, &is_ckt_breaker);
    if (!dispatcher) {
        ZPN_LOG(LOG_NOTICE, "zpn_broker_dispatch_send_c2c_app_check: Dispatcher not ready. Customer:%"PRId64"",check_out->customer_gid);
        return ZPATH_RESULT_NO_ERROR;
    }

    struct fohh_connection *f_conn = zpn_broker_dispatcher_channel_select(dispatcher);
    res = zpn_send_dispatch_fqdn_c2c_check_struct(f_conn, fohh_connection_incarnation(f_conn), check_out);
    if(res) {
        ZPN_LOG(LOG_NOTICE, "zpn_broker_dispatch_send_c2c_app_check: Error sending check to dispatcher for domain:%s Customer:%"PRId64" to:%s",
                                        check_out->app_fqdn, check_out->customer_gid, fohh_description(f_conn));
    } else {
        ZPN_DEBUG_BROKER_C2C_FQDN_CHECK("Sent remote c2c check for fqdn:%s Customer:%"PRId64" to Dispatcher:%s",
                                check_out->app_fqdn, check_out->customer_gid, fohh_description(f_conn));
        *last_disp_id = dispatcher->instance_id;
        if (is_ckt_breaker) {
            __sync_add_and_fetch_8(&broker_dispatcher_stats.ckt_breaker_c2c_app_chk_req_sent, 1);
            __sync_add_and_fetch_8(&(dispatcher->stats.ckt_breaker_c2c_app_chk_req_sent), 1);
        }
    }

    return res;
}

static int zpn_broker_dispatcher_c2c_app_check_cb(void *argo_cookie_ptr,
                                                  void *argo_structure_cookie_ptr,
                                                  struct argo_object *object)
{
    struct zpn_broker_dispatcher_c2c_app_check *zbd_app_check = object->base_structure_void;
    struct fohh_connection *f_conn = argo_structure_cookie_ptr;
    int res = 0;

    if (zbd_app_check == NULL) {
        ZPN_LOG(AL_NOTICE, "c2c:received NULL response from dispatcher");
        return ZPN_RESULT_NO_ERROR;
    }

    if (zpn_debug_get(ZPN_DEBUG_BROKER_C2C_FQDN_CHECK_IDX) || zbd_app_check->id == zpn_request_type_query) {
        char dump[2000];
        if (argo_object_dump(object, dump, sizeof(dump), NULL, 0) == ARGO_RESULT_NO_ERROR) {
            size_t len = strnlen(dump, 2000);
            if (len && (dump[len - 1] == '\n')) dump[len - 1] = 0;
            ZPN_DEBUG_BROKER_C2C_FQDN_CHECK("%s: C2C Check Rx: from %s: %s", fohh_description(f_conn), fohh_peer_cn(f_conn), dump);
        }
    }

    res = zpn_broker_c2c_fqdn_check_response_from_dispatcher(zbd_app_check);
    if (res == ZPN_RESULT_NOT_FOUND) {
        ZPN_LOG(AL_ERROR, "%s:%s Received response for unknown fqdn:%s", fohh_description(f_conn), fohh_peer_cn(f_conn), zbd_app_check->app_fqdn);
    }

    return ZPN_RESULT_NO_ERROR;
}

static int
zpn_broker_dispatch_conn_callback(struct fohh_connection *connection,
                                  enum fohh_connection_state state,
                                  void *cookie)
{
    struct zpn_broker_dispatcher_channel *channel = cookie;
    struct zpn_broker_dispatcher *dispatcher = channel->dispatcher;
    struct argo_state *argo;
    int res;

    if (state == fohh_connection_connected) {
        ZPN_LOG(AL_NOTICE, "Dispatcher %s@%u connected", dispatcher->domain_name, channel->id);

        fohh_connection_set_dynamic_cookie(connection, channel->dispatcher);

        argo = fohh_argo_get_rx(connection);

        /* Register zpn_broker_request */
        if ((res = argo_register_structure(argo, zpn_broker_request_description, zpn_broker_request_cb, connection))) {
            ZPN_LOG(AL_ERROR, "Could not register zpn_broker_request for dispatcher connection %s", fohh_description(connection));
            return res;
        }

        /* Register zpn_app_route_discovery */
        if ((res = argo_register_structure(argo, zpn_app_route_discovery_description, zpn_broker_app_route_discovery_cb, connection))) {
            ZPN_LOG(AL_ERROR, "Could not register zpn_app_route_discovery for dispatcher connection %s", fohh_description(connection));
            return res;
        }

        /* Register zpn_app_route_info */
        if ((res = argo_register_structure(argo, zpn_app_route_info_description, zpn_broker_app_route_info_cb, connection))) {
            ZPN_LOG(AL_ERROR, "Could not register zpn_app_route_info for dispatcher connection %s", fohh_description(connection));
            return res;
        }

        /* Register zpn_broker_request_ack */
        if ((res = argo_register_structure(argo, zpn_broker_request_ack_description, zpn_broker_request_ack_cb, connection))) {
            ZPN_LOG(AL_ERROR, "Could not register zpn_broker_request_ack for dispatcher connection %s", fohh_description(connection));
            return res;
        }

        /* Register zpn_dns_dispatch_check */
        if ((res = argo_register_structure(argo, zpn_dns_dispatch_check_description, zpn_dns_dispatch_check_cb, connection))) {
            ZPN_LOG(AL_ERROR, "Could not register zpn_dns_dispatch_check for dispatcher connection %s", fohh_description(connection));
            return res;
        }

        /* Register zpn_dns_assistant_check */
        if ((res = argo_register_structure(argo, zpn_dns_assistant_check_description, zpn_dns_assistant_check_cb,
                                           connection))) {
            ZPN_LOG(AL_ERROR, "Could not register zpn_dns_assistant_check for dispatcher connection %s", fohh_description(connection));
            return res;
        }

        /* Register zpn_dispatcher_status callback */
        if ((res = argo_register_structure(argo, zpn_dispatcher_status_description, zpn_dispatcher_status_cb, connection))) {
            ZPN_LOG(AL_ERROR, "Could not register zpn_dns_assistant_check for dispatcher connection %s", fohh_description(connection));
            return res;
        }

        /* Register zpn_broker_dispatcher_c2c_app_check */
        if ((res = argo_register_structure(argo, zpn_broker_dispatcher_c2c_app_check_description, zpn_broker_dispatcher_c2c_app_check_cb, connection))) {
            ZPN_LOG(AL_ERROR, "Could not register zpn_broker_dispatcher_c2c_app_check for dispatcher connection %s", fohh_description(connection));
            return res;
        }

        /* Send zpn_broker_info */
        if ((res = zpn_send_zpn_broker_info(connection, 0, g_broker_common_cfg->public_broker.broker_id,
                                            ZPATH_LOCAL_FULL_NAME, channel->id))) {
            ZPN_LOG(AL_ERROR, "%s: Could not send zpn_broker_info", fohh_description(connection));
            return res;
        }

        if (channel->id == 0) {
            ZPATH_MUTEX_LOCK(&(dispatcher->status_lock), __FILE__, __LINE__);
            dispatcher->last_dsp_status_rcvd_s = 0;
            dispatcher->ready = 0;
            dispatcher->is_old_dsp = 0;
            dispatcher->epoch_started = epoch_s();
            /* Send zpn_dispatcher_status request */
            if ((res = zpn_send_zpn_dispatcher_status(connection, 0))) {
                ZPATH_MUTEX_UNLOCK(&(dispatcher->status_lock), __FILE__, __LINE__);
                ZPN_LOG(AL_ERROR, "%s: Could not send zpn_dispatcher_status", fohh_description(connection));
                return res;
            }
            ZPN_DEBUG_DISPATCHER("Sent zpn_dispatcher_status request to %s", fohh_description(connection));
            dispatcher->last_dsp_status_sent_s = epoch_s();
            ZPATH_MUTEX_UNLOCK(&(dispatcher->status_lock), __FILE__, __LINE__);

            /* Now report all our connected assistant to the dispatcher */
            zpn_broker_update_connected_assistants_to_dispatcher(dispatcher, connection);

            /* Now report all the registered c2c application to the dispatcher
            send to control channel
            */
            zpn_broker_update_c2c_app_registration_to_dispatcher(dispatcher, connection);

            /* Now report all the registered extranet zvpn ipsec tunnels to the dispatcher */
            eas_broker_update_zvpn_registration_to_dispatcher(dispatcher, connection);
        }

        channel->connected = 1;
        __sync_fetch_and_add_1(&dispatcher->channel_connected_count, 1);
    } else {
        ZPN_LOG(AL_NOTICE, "Dispatcher %s@%u disconnected", dispatcher->domain_name, channel->id);
        if (channel->connected) {
            channel->connected = 0;
            __sync_fetch_and_sub_1(&dispatcher->channel_connected_count, 1);
        }
    }
    return ZPN_RESULT_NO_ERROR;
}

static int
zpn_broker_dispatch_conn_unblock(struct fohh_connection *connection,
                                 enum fohh_queue_element_type element_type,
                                 void *cookie)
{
    struct zpn_broker_dispatcher_channel *channel = cookie;
    ZPN_LOG(AL_INFO, "Previously blocked tx queue for dispatcher %s@%u is now completely drained",
            channel->dispatcher->domain_name, channel->id);
    return FOHH_RESULT_NO_ERROR;
}

/*
 * return if a dispatcher gid is ready
 */
int
zpn_broker_dispatcher_gid_is_ready(int64_t g_dsp)
{
    struct zpn_broker_dispatcher *dsp;

    dsp = zhash_table_lookup(dispatchers_table, &g_dsp, sizeof(g_dsp), NULL);
    if (dsp) {
        return zpn_broker_dispatcher_is_ready(dsp);
    }

    return 0;
}

/*
 * returns -1 if the dsp is not valid or not ready yet.
 * Note that epoch_started can be 0 as well if it haven't received the status message. But status is the first
 * message that is received on any fohh connection.
 */
int64_t
zpn_broker_dispatcher_get_start_s(int64_t g_dsp)
{
    struct zpn_broker_dispatcher *dsp;

    dsp = zhash_table_lookup(dispatchers_table, &g_dsp, sizeof(g_dsp), NULL);
    if (!dsp) {
        return -1;
    }
    if (!dsp->ready) {
        return -1;
    }
    /*
     * This routine is called while creating and validating the path cache and taking
     * lock here would cause lock contention issue.
     */
    // coverity[missing_lock : Intentional]
    return dsp->epoch_started;
}

struct zpn_broker_dispatcher_stats *zpn_broker_dispatcher_get_stats(int64_t g_dsp)
{
    struct zpn_broker_dispatcher *dsp;

    dsp = zhash_table_lookup(dispatchers_table, &g_dsp, sizeof(g_dsp), NULL);
    if (!dsp) {
        return NULL;
    }

    return &(dsp->stats);
}

void zpn_broker_dispatcher_stats_copy(struct zpn_broker_dispatcher_stats *src,
                                      struct zpn_broker_dispatcher_stats *dst)
{
    dst->zpn_brk_req_sent_total = __atomic_load_n(&(src->zpn_brk_req_sent_total), __ATOMIC_RELAXED);
    dst->zpn_brk_req_redispatch_app_in_learn_mode = __atomic_load_n(&(src->zpn_brk_req_redispatch_app_in_learn_mode), __ATOMIC_RELAXED);
    dst->zpn_brk_req_redispatch_app_retry = __atomic_load_n(&(src->zpn_brk_req_redispatch_app_retry), __ATOMIC_RELAXED);
    dst->zpn_brk_req_redispatch_sticky_changed = __atomic_load_n(&(src->zpn_brk_req_redispatch_sticky_changed), __ATOMIC_RELAXED);
    dst->zpn_brk_req_redispatch_no_connector = __atomic_load_n(&(src->zpn_brk_req_redispatch_no_connector), __ATOMIC_RELAXED);
    dst->zpn_brk_req_redispatch_invalid_domain = __atomic_load_n(&(src->zpn_brk_req_redispatch_invalid_domain), __ATOMIC_RELAXED);
    dst->zpn_brk_req_redispatch_no_connector_success = __atomic_load_n(&(src->zpn_brk_req_redispatch_no_connector_success), __ATOMIC_RELAXED);
    dst->zpn_brk_req_redispatch_invalid_domain_success = __atomic_load_n(&(src->zpn_brk_req_redispatch_invalid_domain_success), __ATOMIC_RELAXED);
    dst->zpn_brk_req_app_multi_match_no_connector = __atomic_load_n(&(src->zpn_brk_req_app_multi_match_no_connector), __ATOMIC_RELAXED);
    dst->zpn_brk_req_sipa_no_connector = __atomic_load_n(&(src->zpn_brk_req_sipa_no_connector), __ATOMIC_RELAXED);
    dst->zpn_brk_req_app_multi_match_invalid_domain = __atomic_load_n(&(src->zpn_brk_req_app_multi_match_invalid_domain), __ATOMIC_RELAXED);
    dst->zpn_brk_req_sipa_invalid_domain = __atomic_load_n(&(src->zpn_brk_req_sipa_invalid_domain), __ATOMIC_RELAXED);
    dst->zpn_brk_req_redispatch_failed_no_disp_ready_in_diff_dc = __atomic_load_n(&(src->zpn_brk_req_redispatch_failed_no_disp_ready_in_diff_dc), __ATOMIC_RELAXED);
    dst->zpn_brk_req_send_fail = __atomic_load_n(&(src->zpn_brk_req_send_fail), __ATOMIC_RELAXED);
    dst->zpn_brk_req_send_fail_no_disp = __atomic_load_n(&(src->zpn_brk_req_send_fail_no_disp), __ATOMIC_RELAXED);
    dst->zpn_brk_ack_send_fail = __atomic_load_n(&(src->zpn_brk_ack_send_fail), __ATOMIC_RELAXED);
    dst->zpn_brk_ack_send_fail_bad_param = __atomic_load_n(&(src->zpn_brk_ack_send_fail_bad_param), __ATOMIC_RELAXED);
    dst->zpn_brk_ack_send_fail_no_disp = __atomic_load_n(&(src->zpn_brk_ack_send_fail_no_disp), __ATOMIC_RELAXED);
    dst->zpn_brk_c2c_msg_send_fail = __atomic_load_n(&(src->zpn_brk_c2c_msg_send_fail), __ATOMIC_RELAXED);
    dst->zpn_brk_c2c_msg_send_disp_fail = __atomic_load_n(&(src->zpn_brk_c2c_msg_send_disp_fail), __ATOMIC_RELAXED);
    dst->zpn_brk_req_ack_err_sent = __atomic_load_n(&(src->zpn_brk_req_ack_err_sent), __ATOMIC_RELAXED);
    dst->zpn_brk_req_ack_err_send_fail_bad_param = __atomic_load_n(&(src->zpn_brk_req_ack_err_send_fail_bad_param), __ATOMIC_RELAXED);
    dst->zpn_brk_req_ack_err_send_fail = __atomic_load_n(&(src->zpn_brk_req_ack_err_send_fail), __ATOMIC_RELAXED);
    dst->zpn_brk_req_redispatch_timeout = __atomic_load_n(&(src->zpn_brk_req_redispatch_timeout), __ATOMIC_RELAXED);
    dst->zpn_brk_req_redispatch_timeout_success = __atomic_load_n(&(src->zpn_brk_req_redispatch_timeout_success), __ATOMIC_RELAXED);
    dst->zpn_brk_req_redispatch_timeout_first_disp_success = __atomic_load_n(&(src->zpn_brk_req_redispatch_timeout_first_disp_success), __ATOMIC_RELAXED);
    dst->zpn_brk_req_redispatch_timeout_second_disp_success = __atomic_load_n(&(src->zpn_brk_req_redispatch_timeout_second_disp_success), __ATOMIC_RELAXED);
    dst->zpn_brk_req_redispatch_timeout_bind_discard = __atomic_load_n(&(src->zpn_brk_req_redispatch_timeout_bind_discard), __ATOMIC_RELAXED);
    dst->zpn_brk_req_redispatch_timeout_error_discard = __atomic_load_n(&(src->zpn_brk_req_redispatch_timeout_error_discard), __ATOMIC_RELAXED);
    dst->zpn_brk_req_redispatch_no_connector_app_access_success = __atomic_load_n(&(src->zpn_brk_req_redispatch_no_connector_app_access_success), __ATOMIC_RELAXED);
    dst->zpn_brk_req_redispatch_invalid_domain_app_access_success = __atomic_load_n(&(src->zpn_brk_req_redispatch_invalid_domain_app_access_success), __ATOMIC_RELAXED);
    dst->zpn_brk_req_redispatch_timeout_app_access_success = __atomic_load_n(&(src->zpn_brk_req_redispatch_timeout_app_access_success), __ATOMIC_RELAXED);
    dst->zpn_brk_req_redispatch_timeout_first_disp_app_access_success = __atomic_load_n(&(src->zpn_brk_req_redispatch_timeout_first_disp_app_access_success), __ATOMIC_RELAXED);
    dst->zpn_brk_req_redispatch_timeout_second_disp_app_access_success = __atomic_load_n(&(src->zpn_brk_req_redispatch_timeout_second_disp_app_access_success), __ATOMIC_RELAXED);
    dst->zpn_brk_req_response_received = __atomic_load_n(&(src->zpn_brk_req_response_received), __ATOMIC_RELAXED);
    dst->zpn_brk_req_redispatch_second_disp_timeout = __atomic_load_n(&(src->zpn_brk_req_redispatch_second_disp_timeout), __ATOMIC_RELAXED);
    dst->zpn_brk_req_redispatch_timeout_no_response = __atomic_load_n(&(src->zpn_brk_req_redispatch_timeout_no_response), __ATOMIC_RELAXED);
    dst->ckt_breaker_state_close = __atomic_load_n(&(src->ckt_breaker_state_close), __ATOMIC_RELAXED);
    dst->ckt_breaker_state_partial_open_10 = __atomic_load_n(&(src->ckt_breaker_state_partial_open_10), __ATOMIC_RELAXED);
    dst->ckt_breaker_state_partial_open_20 = __atomic_load_n(&(src->ckt_breaker_state_partial_open_20), __ATOMIC_RELAXED);
    dst->ckt_breaker_state_partial_open_40 = __atomic_load_n(&(src->ckt_breaker_state_partial_open_40), __ATOMIC_RELAXED);
    dst->ckt_breaker_state_open = __atomic_load_n(&(src->ckt_breaker_state_open), __ATOMIC_RELAXED);
    dst->ckt_breaker_state_partial_open_90 = __atomic_load_n(&(src->ckt_breaker_state_partial_open_90), __ATOMIC_RELAXED);
    dst->ckt_breaker_state_partial_open_80 = __atomic_load_n(&(src->ckt_breaker_state_partial_open_80), __ATOMIC_RELAXED);
    dst->ckt_breaker_state_partial_open_60 = __atomic_load_n(&(src->ckt_breaker_state_partial_open_60), __ATOMIC_RELAXED);
    dst->ckt_breaker_fail_both_timeout = __atomic_load_n(&(src->ckt_breaker_fail_both_timeout), __ATOMIC_RELAXED);
    dst->ckt_breaker_fail_secondary_timeout = __atomic_load_n(&(src->ckt_breaker_fail_secondary_timeout), __ATOMIC_RELAXED);
    dst->ckt_breaker_fail_no_secondary_dsp = __atomic_load_n(&(src->ckt_breaker_fail_no_secondary_dsp), __ATOMIC_RELAXED);
    dst->ckt_breaker_zpn_brk_req_sent = __atomic_load_n(&(src->ckt_breaker_zpn_brk_req_sent), __ATOMIC_RELAXED);
    dst->ckt_breaker_dns_req_sent = __atomic_load_n(&(src->ckt_breaker_dns_req_sent), __ATOMIC_RELAXED);
    dst->ckt_breaker_c2c_app_chk_req_sent = __atomic_load_n(&(src->ckt_breaker_c2c_app_chk_req_sent), __ATOMIC_RELAXED);
    dst->dns_req_sent_total = __atomic_load_n(&(src->dns_req_sent_total), __ATOMIC_RELAXED);
}

int64_t zpn_broker_dispatcher_get_ckt_breaker_evaluation_interval_s()
{
    int64_t config_value = DEFAULT_CONFIG_FEATURE_BRK_DSP_CKT_BREAKER_EVALUATION_INTERVAL_MINUTE;

    config_value = zpath_config_override_get_config_int(CONFIG_FEATURE_BRK_DSP_CKT_BREAKER_EVALUATION_INTERVAL_MINUTE,
                                                        &config_value,
                                                        DEFAULT_CONFIG_FEATURE_BRK_DSP_CKT_BREAKER_EVALUATION_INTERVAL_MINUTE,
                                                        zpath_instance_global_state.current_config->gid,
                                                        (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                        0);

    if (config_value % 5 != 0) {
        ZPN_LOG(AL_ERROR, "Invalid brk dsp ckt breaker interval configured: %"PRId64" min, only multiple of 5 is allowed, using default %d min",
                config_value, DEFAULT_CONFIG_FEATURE_BRK_DSP_CKT_BREAKER_EVALUATION_INTERVAL_MINUTE);
        config_value = DEFAULT_CONFIG_FEATURE_BRK_DSP_CKT_BREAKER_EVALUATION_INTERVAL_MINUTE;
    }

    return (config_value * 60);
}

/*
 * This is called every 5 min for each dispatcher.
 */
int zpn_broker_dispatcher_stats_fill_cb(void *cookie,
                                        int counter,
                                        void *structure_data)
{
    static int64_t last_cycle = -1;
    int64_t current_cycle = epoch_s() / BRK_DSP_STATS_INTERVAL_S;
    static int64_t total_call = 0;
    static int64_t last_evaluation_call = 0;

    if (!zpath_service_init_complete) {
        return ZPN_RESULT_NO_ERROR;
    }

    /*
     * This callback is called every 5 min for each dispatcher,
     * we need to check circuit breaker state once every 5 min
     * before any stats is sent in that 5 min cycle.
     * So call zpn_brk_dsp_circuit_breaker_check when the very
     * first time this cb is called in a 5 min cycle.
     */
    if (current_cycle != last_cycle) {
        last_cycle = current_cycle;
        total_call++;
        int64_t evaluation_interval_s = zpn_broker_dispatcher_get_ckt_breaker_evaluation_interval_s();
        if (total_call - last_evaluation_call >= evaluation_interval_s / BRK_DSP_STATS_INTERVAL_S) {
            last_evaluation_call = total_call;
            ZPN_LOG(AL_NOTICE, "Starting broker dispatcher circuit breaker state evaluation");
            zpn_brk_dsp_circuit_breaker_check(&broker_dispatcher_stats);
        }
    }
    if (cookie && last_evaluation_call == total_call) {
        /* This is per dispatcher stats - keep current stats copy to exported_stats for next evaluation. */
        struct zpn_broker_dispatcher *dsp = cookie;
        zpn_broker_dispatcher_stats_copy(&(dsp->stats), &(dsp->exported_stats));
    }

    return ZPN_RESULT_NO_ERROR;
}

/*
 * Allocate and connect to a dispatcher. Never goes away.
 *
 * If test_mode is set, it doesn't actually create a working fohh_connection
 */
struct zpn_broker_dispatcher *
zpn_broker_dispatcher_get(int64_t instance_id,
                          const char *domain_name,
                          size_t domain_name_len,
                          int test_mode,
                          char *pool_name)
{
    struct zpn_broker_dispatcher *ret = NULL;
    char stats_name[512];
    int res;
    int need_update = 0;

    ret = zhash_table_lookup(dispatchers_table, &instance_id, sizeof(instance_id), NULL);
    if (!ret) {
        ret = ZPN_CALLOC(sizeof(*ret));
        if (!ret) {
            ZPN_LOG(AL_ERROR, "Alloc (Memory leak)");
            return NULL;
        }

        ret->instance_id = instance_id;
        ret->domain_name = ZPN_STRDUP(domain_name, domain_name_len);
        if (!ret->domain_name) {
            ZPN_LOG(AL_ERROR, "Failed to alloc dispatcher domain_name: %s", domain_name);
            ZPN_FREE(ret);
            return NULL;
        }

        ret->pool_name = ZPN_STRDUP(pool_name, strlen(pool_name));
        if (!ret->pool_name) {
            ZPN_LOG(AL_ERROR, "Fail to alloc pool_name");
            ZPN_FREE(ret->domain_name);
            ZPN_FREE(ret);
            return NULL;
        }

        if (!test_mode) {
            snprintf(stats_name, sizeof(stats_name), "zpn_broker_dispatcher_stats_%s", domain_name);
            ret->stats_argo = argo_log_register_structure(argo_log_get("statistics_log"),
                                                          stats_name,
                                                          AL_INFO,
                                                          SECOND_TO_US(BRK_DSP_STATS_INTERVAL_S), /* 5 min interval */
                                                          zpn_broker_dispatcher_stats_description,
                                                          &(ret->stats),
                                                          1,
                                                          zpn_broker_dispatcher_stats_fill_cb,
                                                          ret);
            if (!ret->stats_argo) {
                ZPN_LOG(AL_ERROR, "Could not register stats structure for %s", stats_name);
                ZPN_FREE(ret->pool_name);
                ZPN_FREE(ret->domain_name);
                ZPN_FREE(ret);
                return NULL;
            }
        }

        // Initialize channels.
        ret->channel_count = dsp_channel_count;
        uint8_t i;
        for (i = 0; i < dsp_channel_count; ++i) {
            ret->channels[i].id = i;
            ret->channels[i].dispatcher = ret;
            ret->channels[i].connected = 0;
            char fohh_stats_name[256];
            snprintf(fohh_stats_name, sizeof(fohh_stats_name), "%s@%u", ret->domain_name, i);
            ret->status_lock = ZPATH_MUTEX_INIT;

            if (test_mode) {
                ret->channels[i].f_conn = ZPN_CALLOC(sizeof(*ret->channels[i].f_conn));
                ret->channels[i].f_conn->state = fohh_connection_connected;
                ret->channels[i].f_conn->last_rx_bytes_us = epoch_us();
                ret->channels[i].f_conn->last_rx_obj_us = epoch_us();
                ret->channels[i].connected = 1;
                ret->channel_connected_count++;
                ret->ready = 1;
            } else {
                char buf[100];
                snprintf(buf, sizeof(buf), "%s%s", FOHH_WORKER_ZPN_DISPATCHER_POOL, pool_name);
                ret->channels[i].f_conn = fohh_client_create(buf,
                                                             fohh_stats_name,
                                                             argo_serialize_json_pretty,
                                                             fohh_connection_style_argo_tlv,
                                                             0,                  // Not quiet
                                                             &ret->channels[i],  // Cookie
                                                             zpn_broker_dispatch_conn_callback,
                                                             NULL,
                                                             zpn_broker_dispatch_conn_unblock,
                                                             NULL,
                                                             ret->domain_name,
                                                             ret->domain_name,  // SNI
                                                             NULL,
                                                             htons(ZPN_DISPATCHER_PORT_TLV),
                                                             NULL,
                                                             1,  // Use SSL
                                                             15);
            }
            if (!ret->channels[i].f_conn) {
                ZPN_LOG(AL_ERROR, "Could not create fohh_connection to %s", fohh_stats_name);
                if (!test_mode) {
                    argo_log_deregister_structure(ret->stats_argo, 0);
                }
                ZPATH_MUTEX_DESTROY(&(ret->status_lock), _FILE_, _LINE_);
                ZPN_FREE(ret->pool_name);
                ZPN_FREE(ret->domain_name);
                ZPN_FREE(ret);
                return NULL;
            }
            fohh_set_sticky(ret->channels[i].f_conn, 1);
            fohh_set_max_backoff(ret->channels[i].f_conn, MAX_DISPATCHER_BACKOFF_S);
        }
        ret->brk_dsp_circuit_breaker_state_lock = ZPATH_RWLOCK_INIT;
        zpn_brk_dsp_circuit_breaker_set_state(ret, brk_dsp_circuit_breaker_state_close);
        res = zhash_table_store(dispatchers_table, &instance_id, sizeof(instance_id), 0, ret);
        if (res) {
            ZPN_LOG(AL_CRITICAL, "Could not store into hash table?");
        }
    } else {
        if (strcmp(domain_name, ret->domain_name)) {
            //There is change in the domain name.
            ZPN_LOG(AL_NOTICE, "Domain name for the dispatcher changed from %s to %s",
                    ret->domain_name, domain_name);
            //cache new domain name
            if (ret->new_domain_name != NULL) {
                ZPN_FREE(ret->new_domain_name);
                ret->new_domain_name = NULL;
            }
            ret->new_domain_name = ZPN_STRDUP(domain_name, domain_name_len);

            for (int i = 0; i < dsp_channel_count; ++i) {
                if (fohh_get_state(ret->channels[i].f_conn) == fohh_connection_backoff) {
                    need_update = 1;
                    break;
                }
            }

            if (need_update) {
                ZPN_LOG(AL_NOTICE, "Dispatcher connections not in connected state. updating new domain name");
                //delete the old domain name from resolver and update domain name in disp obj
                ZPN_FREE_SLOW(ret->domain_name);
                ret->domain_name = ret->new_domain_name;
                ret->new_domain_name = NULL;
                //Update new domain name to the fohh connection and resolver.
                for (int i = 0; i < dsp_channel_count; ++i) {
                    fohh_update_domain_name(ret->channels[i].f_conn, domain_name);
                    fohh_update_sni_name(ret->channels[i].f_conn, domain_name);
                    fohh_update_remote_host(ret->channels[i].f_conn, domain_name);
                }
            }
        }
    }
    return ret;
}


static int zpn_broker_dispatch_pool_dump(struct zpath_debug_state *request_state,
                                         const char **query_values,
                                         int query_value_count,
                                         void *cookie)
{
    char out_buf[100000];

    out_buf[0] = 0;
    zpn_broker_dispatch_pool_dump_state(out_buf, sizeof(out_buf), query_values[0] ? 1 : 0);
    ZDP("%s", out_buf);
	return ZPN_RESULT_NO_ERROR;
}

static int zpn_broker_dispatch_pool_activate(struct zpath_debug_state *request_state,
                                             const char **query_values,
                                             int query_value_count,
                                             void *cookie)
{
    char out_buf[10000];

    out_buf[0] = 0;
    zpn_broker_dispatcher_test_set_created(epoch_s() - NEXT_POOL_MAX_TIME);
    ZDP("Done\n\nCurrent State:\n\n");
    zpn_broker_dispatch_pool_dump_state(out_buf, sizeof(out_buf), 0);
    ZDP("%s", out_buf);
	return ZPN_RESULT_NO_ERROR;
}

static int zpn_broker_dispatch_pool_debug_query(struct zpath_debug_state *request_state,
                                                const char **query_values,
                                                int query_value_count,
                                                void *cookie)
{
    char out_buf[10000];
    out_buf[0] = 0;

    int64_t gid;
    if (!query_values[0]) {
        ZDP("Missing argument: gid\n");
        return ZPN_RESULT_BAD_ARGUMENT;
    }
    gid = strtoul(query_values[0], NULL, 0);

    zpn_broker_dispatch_pool_query(gid, out_buf, sizeof(out_buf));

    ZDP("%s", out_buf);

    return ZPN_RESULT_NO_ERROR;
}

static int zpn_broker_dispatch_pool_interval(struct zpath_debug_state *request_state,
                                             const char **query_values,
                                             int query_value_count,
                                             void *cookie)
{
    int64_t value;

    if (!query_values[0]) {
        ZDP("Missing argument: value\n");
        return ZPN_RESULT_BAD_ARGUMENT;
    }
    value = strtoul(query_values[0], NULL, 0);

    if (value < 0) return ZPN_RESULT_BAD_ARGUMENT;
    if (value > (60*60)) return ZPN_RESULT_BAD_ARGUMENT;

    next_pool_interval_s = value;

    ZDP("Set interval to %lds\n", (long) value);

	return ZPN_RESULT_NO_ERROR;
}

static void override_dispatcher_config(struct zpn_dispatcher_config *cfg) {
    int64_t config_value = 0;
    cfg->local_disp = 1;

#define CHECK_CONFIG(option, key)                                                                               \
    config_value = zpath_config_override_get_config_int(key, &config_value, DEFAULT_##key, cfg->gid,            \
                                                       (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID, (int64_t)0);  \
                                                                                                                \
    if (config_value > 0 && config_value != DEFAULT_##key) {                                                    \
        cfg->option = config_value;                                                                             \
        ZPN_LOG(AL_NOTICE, "Config override by DB: " #option "=%"PRId64, cfg->option);                          \
    }

    CHECK_CONFIG(dispatch_rtt_threshold_us, DSP_DISPATCH_RTT_THRESHOLD_US);
    CHECK_CONFIG(dispatch_rtt_dynamic_threshold_pct, DSP_DISPATCH_RTT_DYNAMIC_THRESHOLD_PCT);
    CHECK_CONFIG(dispatch_dist_threshold_mi, DSP_DISPATCH_DIST_THRESHOLD_MI);
    CHECK_CONFIG(discovery_ast_target_limit, DSP_DISCOVERY_AST_TARGET_LIMIT);
    CHECK_CONFIG(discovery_timeout_s, DSP_DISCOVERY_TIMEOUT_S);
    CHECK_CONFIG(discovery_timeout_cache_s, DSP_DISCOVERY_TIMEOUT_CACHE_S);
    CHECK_CONFIG(discovery_renewal_s, DSP_DISCOVERY_RENEWAL_S);
    CHECK_CONFIG(discovery_expiration_s, DSP_DISCOVERY_EXPIRATION_S);
    CHECK_CONFIG(dns_positive_cache_s, DSP_DNS_POSITIVE_CACHE_S);
    CHECK_CONFIG(dns_negative_cache_s, DSP_DNS_NEGATIVE_CACHE_S);
    CHECK_CONFIG(dns_timeout_s, DSP_DNS_TIMEOUT_S);
    CHECK_CONFIG(dns_max_checks_per_ast_group, DSP_DNS_MAX_CHECKS_PER_AST_GROUP);
    CHECK_CONFIG(session_cache_s, DSP_SESSION_CACHE_S);
    CHECK_CONFIG(ast_pref_cache_s, DSP_AST_PREF_CACHE_S);
    CHECK_CONFIG(max_reg_lifetime_s, DSP_MAX_REG_LIFETIME_S);
    CHECK_CONFIG(max_c2c_reg_lifetime_s, DSP_MAX_C2C_REG_LIFETIME_S);
    CHECK_CONFIG(max_health_lifetime_s, DSP_MAX_HEALTH_LIFETIME_S);
    CHECK_CONFIG(expiration_window_s, DSP_EXPIRATION_WINDOW_S);

#undef CHECK_CONFIG
}

/*
 * Update app access success stats in case of re-dispatch to diff DC.
 * Mtunnel is assumed to be locked at this point.
 */
void zpn_broker_dispatcher_stats_app_access_success_update(struct zpn_broker_mtunnel *mtunnel,
                                                           int64_t success_disp_id)
{
    struct zpn_broker_dispatcher_stats *brk_dsp_stats = NULL;

    if (ZPN_BROKER_IS_PUBLIC()) {
        brk_dsp_stats = zpn_broker_dispatcher_get_stats(success_disp_id);
    }

    if (mtunnel->brk_req_redispatch_to_diff_dc_reason == zpn_err_brk_req_no_connector_available) {
        __sync_add_and_fetch_8(&broker_dispatcher_stats.zpn_brk_req_redispatch_no_connector_app_access_success, 1);
        if (brk_dsp_stats) {
            __sync_add_and_fetch_8(&(brk_dsp_stats->zpn_brk_req_redispatch_no_connector_app_access_success), 1);
        }
    } else if (mtunnel->brk_req_redispatch_to_diff_dc_reason == zpn_err_brk_req_invalid_domain) {
        __sync_add_and_fetch_8(&broker_dispatcher_stats.zpn_brk_req_redispatch_invalid_domain_app_access_success, 1);
        if (brk_dsp_stats) {
            __sync_add_and_fetch_8(&(brk_dsp_stats->zpn_brk_req_redispatch_invalid_domain_app_access_success), 1);
        }
    } else if (mtunnel->brk_req_redispatch_to_diff_dc_reason == zpn_err_brk_req_timeout_redispatch_to_diff_dc) {
        /* Case when dispatchr A didn't respond within 2 sec and we had re-dispatched to dispatcher B in different DC. */
        __sync_add_and_fetch_8(&broker_dispatcher_stats.zpn_brk_req_redispatch_timeout_app_access_success, 1);
        if (brk_dsp_stats) {
            __sync_add_and_fetch_8(&(brk_dsp_stats->zpn_brk_req_redispatch_timeout_app_access_success), 1);
        }
        if (success_disp_id == mtunnel->last_dispatcher_id) {
            /* Success because of dispatcher A. */
            __sync_add_and_fetch_8(&broker_dispatcher_stats.zpn_brk_req_redispatch_timeout_first_disp_app_access_success, 1);
            if (brk_dsp_stats) {
                __sync_add_and_fetch_8(&(brk_dsp_stats->zpn_brk_req_redispatch_timeout_first_disp_app_access_success), 1);
            }
        } else if (mtunnel->flag.timeout_redispatch_to_diff_dc && success_disp_id == mtunnel->redispatch_to_diff_dc_disp_id) {
            /* Success because of dispatcher B. */
            __sync_add_and_fetch_8(&broker_dispatcher_stats.zpn_brk_req_redispatch_timeout_second_disp_app_access_success, 1);
            if (brk_dsp_stats) {
                __sync_add_and_fetch_8(&(brk_dsp_stats->zpn_brk_req_redispatch_timeout_second_disp_app_access_success), 1);
            }
        }
    }
}

struct zpn_broker_dispatcher_stats *zpn_broker_dispatcher_stats_data_obj_get()
{
    return &broker_dispatcher_stats;
}

static int zpn_broker_dispatcher_stats_dump_cb(struct zpath_debug_state* request_state,
                                               const char** query_values,
                                               int query_value_count,
                                               void* cookie)
{
    struct zpn_broker_dispatcher_stats *stats;
    struct zpath_instance *instance;
    char jsonout[10000];
    int res;

    if (query_values[0]) {
        /* Dump stats for specific dispatcher. */
        res = zpath_verify_domain_name_by_instance(query_values[0], "zpn_dispatcher", &instance, 1);
        if (res || !instance) {
            ZDP("Could not get instance details for %s: %s, can't dump the stats\n", query_values[0], zpn_result_string(res));
            return ZPN_RESULT_NO_ERROR;
        }
        stats = zpn_broker_dispatcher_get_stats(instance->gid);
    } else {
        /* Dump overall total stats for this broker. */
        stats = zpn_broker_dispatcher_stats_data_obj_get();
    }

    res = argo_structure_dump(zpn_broker_dispatcher_stats_description, stats, jsonout, sizeof(jsonout), NULL, 1);
    if (res == ARGO_RESULT_NO_ERROR) {
        ZDP("%s\n", jsonout);
    } else {
        ZDP("Could not dump zpn_broker_dispatcher_stats: %s\n", zpn_result_string(res));
    }

    return ZPN_RESULT_NO_ERROR;
}

static int zpn_brk_dsp_circuit_breaker_set_state_cb(struct zpath_debug_state* request_state,
                                                    const char** query_values,
                                                    int query_value_count,
                                                    void* cookie)
{
    struct zpn_broker_dispatcher *dispatcher = NULL;
    struct zpath_instance *instance = NULL;
    size_t state_str_size, i;
    enum zpn_brk_dsp_circuit_breaker_state current_state;
    int res;

    if (!query_values[0] || !query_values[1]) {
        ZDP("Please provide dispatcher name and state to set\n");
        return ZPN_RESULT_NO_ERROR;
    }

    if (!zpn_brk_dsp_circuit_breaker_is_enabled()) {
        ZDP("Broker - dispatcher circuit breaker feature is disabled, no circuit state change is permitted. Please enable the feature to use this debug interface\n");
        return ZPN_RESULT_NO_ERROR;
    }

    res = zpath_verify_domain_name_by_instance(query_values[0], "zpn_dispatcher", &instance, 1);
    if (res || !instance) {
        ZDP("Could not get instance details for %s: %s, can't set the state\n", query_values[0], zpn_result_string(res));
        return ZPN_RESULT_NO_ERROR;
    }

    dispatcher = zhash_table_lookup(dispatchers_table, &(instance->gid), sizeof(instance->gid), NULL);
    if (!dispatcher) {
        ZDP("Could not get the dispatcher for %s, instance_gid = %"PRId64", can't set the state\n", query_values[0], instance->gid);
        return ZPN_RESULT_NO_ERROR;
    }

    state_str_size = zpn_brk_dsp_circuit_breaker_get_state_cnt();
    for (i = 0; i < state_str_size; i++) {
        if (strcmp(zpn_brk_dsp_circuit_breaker_state_get_str(i), query_values[1]) == 0) {
            current_state = zpn_brk_dsp_circuit_breaker_get_state(dispatcher);
            if (i == current_state) {
                ZDP("Circuit breaker state is already set to %s for %s\n",
                    zpn_brk_dsp_circuit_breaker_state_get_str(current_state), dispatcher->domain_name);
                return ZPN_RESULT_NO_ERROR;
            }
            ZPN_LOG(AL_NOTICE, "Circuit breaker state transition for %s: %s to %s, reason: debug interface",
                    dispatcher->domain_name, zpn_brk_dsp_circuit_breaker_state_get_str(current_state),
                    zpn_brk_dsp_circuit_breaker_state_get_str(i));
            zpn_brk_dsp_circuit_breaker_set_state(dispatcher, i);
            ZDP("Successfully set the circuit breaker state for %s from %s to %s\n",
                dispatcher->domain_name, zpn_brk_dsp_circuit_breaker_state_get_str(current_state),
                zpn_brk_dsp_circuit_breaker_state_get_str(i));
            return ZPN_RESULT_NO_ERROR;
        }
    }

    ZDP("Invalid circuit breaker state provided, accepted values are CLOSE/OPEN/PARTIAL_OPEN_X where X = 10/20/40/60/80/90\n");
    return ZPN_RESULT_NO_ERROR;
}

int zpn_dispatcher_set_c2c_regex_cb(zpn_broker_dispatch_c2c_regex_check_cb cb) {
    if (!cb) {
        return ZPN_RESULT_ERR;
    }
    local_disp->c2c_regex_check_cb = cb;
    return ZPN_RESULT_NO_ERROR;
}

int zpn_broker_dispatch_stats_init()
{
    int res;

    zpn_broker_dispatcher_stats_description = argo_register_global_structure(ZPN_BROKER_DISPATCHER_STATS_HELPER);
    if (!zpn_broker_dispatcher_stats_description) {
        ZPN_LOG(AL_ERROR, "Argo description register failed for zpn_broker_dispatcher_stats");
        return ZPN_RESULT_ERR;
    }
    if (!argo_log_register_structure(argo_log_get("statistics_log"),
                                     "zpn_broker_dispatcher_stats",
                                     AL_INFO,
                                     SECOND_TO_US(BRK_DSP_STATS_INTERVAL_S), /* 5 min interval */
                                     zpn_broker_dispatcher_stats_description,
                                     &broker_dispatcher_stats,
                                     1,
                                     zpn_broker_dispatcher_stats_fill_cb,
                                     NULL)) {
        ZPN_LOG(AL_ERROR, "Could not register zpn_broker_dispatcher_stats");
        return ZPN_RESULT_ERR;
    }

    /* Add debug command to dump zpn_broker_dispatcher_stats - only for dev/qa (for testing). */
    if (zpn_broker_is_dev_environment()) {
        res = zpath_debug_add_read_command("Dump zpn_broker_dispatcher_stats",
                                      "/zpn/broker_dispatcher_stats/dump",
                                      zpn_broker_dispatcher_stats_dump_cb,
                                      NULL,
                                      "dispatcher", "Dispatcher name to dump specific dispatcher stats, else overall total stats is dumped",
                                      NULL);
        if (res) {
            ZPN_LOG(AL_ERROR, "Could not add /zpn/broker_dispatcher_stats/dump debug command: %s", zpn_result_string(res));
            return res;
        }
    }

    return ZPN_RESULT_NO_ERROR;
}
/*
 * unit test init
 */
int zpn_broker_dispatch_init_for_unit_tests() {
    dispatchers_table = zhash_table_alloc(&zpn_allocator);
    if (!dispatchers_table)
        return ZPN_RESULT_NO_MEMORY;

    return ZPN_RESULT_NO_ERROR;
}
/*
 * 1. Init stats
 * 2. create watcher thread
 */
int zpn_broker_dispatch_init(int init_pool, int init_local_disp, uint8_t channel_count, int test_mode)
{
    pthread_t thread;
    int res;
    char print_buf[2000];

    stats.init++;
    {
        int curr_fohh_thread;
        int max_fohh_thread = fohh_thread_count();
        for (curr_fohh_thread = 0; curr_fohh_thread < max_fohh_thread; curr_fohh_thread++) {
            fohh_stats[curr_fohh_thread] = ZPN_CALLOC(sizeof(struct zpn_broker_dispatch_fohh_stats));
            stats.init_fohh_stats++;
        }
    }

    if (init_pool) {
        if (channel_count < 1 || channel_count > MAX_CHANNELS_PER_DISPATCHER) {
            ZPN_LOG(AL_ERROR, "Invalid dispatcher channel count: %u", channel_count);
            return ZPATH_RESULT_BAD_ARGUMENT;
        }
        dsp_channel_count = channel_count;
        dispatchers_table = zhash_table_alloc(&zpn_allocator);
        if (!dispatchers_table) return ZPN_RESULT_NO_MEMORY;

        zpn_broker_dispatch_pool_init();

        if (!test_mode) {
            if (!(zpn_broker_dispatch_fohh_stats_description =
                          argo_register_global_structure(ZPN_BROKER_DISPATCH_FOHH_STATS_HELPER))) {
                return ZPATH_RESULT_ERR;
            }
            if (!(zpn_broker_dispatch_stats_description =
                          argo_register_global_structure(ZPN_BROKER_DISPATCH_STATS_HELPER))) {
                return ZPATH_RESULT_ERR;
            }
            if (!(zpn_broker_dispatch_audit_description =
                          argo_register_global_structure(ZPN_BROKER_DISPATCH_AUDIT_HELPER))) {
                return ZPATH_RESULT_ERR;
            }

            zpn_broker_dispatcher_stats_obj_set(&broker_dispatcher_stats);

            /* Just launch a thread for running this stuff... */
            res = zthread_create(&thread, zpn_broker_dispatcher_watch_thread, NULL, "dispatch_watcher", 60,
                                 16 * 1024 * 1024, 60 * 1000 * 1000, NULL);
            if (res) return ZPN_RESULT_ERR;

            res = zpath_debug_add_read_command("Dump info related to zpn broker dispatch operation.",
                                          "/zpn/broker_dispatch/stats_dump", zpn_broker_dispatch_stats_dump, NULL,
                                          NULL);
            if (res) {
                ZPN_LOG(AL_ERROR, "couldn't add /zpn/broker_dispatch/stats_dump");
                return res;
            }

            res = zpath_debug_add_write_command("Clear stats related to zpn broker dispatch operation.",
                                          "/zpn/broker_dispatch/stats_clear", zpn_broker_dispatch_stats_clear, NULL,
                                          NULL);
            if (res) {
                ZPN_LOG(AL_ERROR, "couldn't add /zpn/broker_dispatch/stats_clear");
                return res;
            }

            res = zpath_debug_add_read_command("Dump dispatcher pool state",
                                          "/dispatcher/pool/dump",
                                          zpn_broker_dispatch_pool_dump,
                                          NULL,
                                          "json", "Output in JSON",
                                          NULL);

            if (res) {
                ZPN_LOG(AL_ERROR, "Could not add the debug interface /dispatcher/pool/dump: %s",
                        zpn_result_string(res));
                return res;
            }
            res = zpath_debug_add_read_command("Get dispatcher pool selection for a GID",
                                          "/dispatcher/pool/query",
                                          zpn_broker_dispatch_pool_debug_query,
                                          NULL,
                                          "gid", "The GID to use to dictate hashing into dispatcher pools",
                                          NULL);

            if (res) {
                ZPN_LOG(AL_ERROR, "Could not add the debug interface /dispatcher/pool/query: %s",
                        zpn_result_string(res));
                return res;
            }

            res = zpath_debug_add_write_command("Force the current pool configuration to be active.",
                                          "/dispatcher/pool/activate",
                                          zpn_broker_dispatch_pool_activate,
                                          NULL,
                                          NULL);

            if (res) {
                ZPN_LOG(AL_ERROR, "Could not add the debug interface /dispatcher/pool/activate: %s",
                        zpn_result_string(res));
                return res;
            }

            res = zpath_debug_add_write_command(
                    "Set the dispatcher pool interval phase time. Defaults to 1800 (seconds- 30 minutes)",
                    "/dispatcher/pool/interval",
                    zpn_broker_dispatch_pool_interval,
                    NULL,
                    "value", "The number of seconds in each interval",
                    NULL);

            if (res) {
                ZPN_LOG(AL_ERROR, "Could not add the debug interface /dispatcher/pool/interval: %s",
                        zpn_result_string(res));
                return res;
            }

            if (zpn_broker_is_dev_environment()) {
                /* Debug command to set the circuit breaker state - only available in dev/qa clouds to ease the testing */
                res = zpath_debug_add_write_command("Set circuit breaker state for given dispatcher",
                                                    "/dispatcher/circuit_breaker/state/set",
                                                    zpn_brk_dsp_circuit_breaker_set_state_cb,
                                                    NULL,
                                                    "dispatcher", "Dispatcher name to set the state",
                                                    "state", "CLOSE/OPEN/PARTIAL_OPEN_X where X = 10/20/40/60/80/90",
                                                    NULL);
                if (res) {
                    ZPN_LOG(AL_ERROR, "Could not add the debug interface dispatcher/circuit_breaker/state/set: %s",
                            zpn_result_string(res));
                    return res;
                }
            }
        }
        pool_initialized = 1;
    }

    if (init_local_disp) {
        // Init callback thread pool.
        int i;
        for (i = 0; i < LOCAL_DISP_CB_THREAD_COUNT; ++i) {
            char thread_name[100];
            snprintf(thread_name, sizeof(thread_name), "local_disp_cb_thread_%d", i);
            struct zevent_base *zbase = zevent_handler_create(thread_name, 16 * 1024 * 1024, DISPATCHER_MAX_HEARTBEAT_MISS_INTERVAL_S);
            if (!zbase) {
                ZPN_LOG(AL_ERROR, "Could not create zevent");
                return ZPN_RESULT_ERR;
            }

            zevent_add_to_class(zbase, LOCAL_DISP_CB_THREAD_POOL);
        }

        dsp_log_init(zpn_event_collection, DSP_DEBUG_DROP_RX_BIT);
        zpn_dispatcher_cfg_override_desc_register_all();
        struct zpn_dispatcher_config config = zpn_dispatcher_config_get_default(ZPN_BROKER_GET_GID());
        override_dispatcher_config(&config);
        ZPN_LOG(AL_NOTICE, "Dispatcher config at startup\n%s",
                zpn_dispatcher_config_dump(&config, print_buf, sizeof(print_buf)));

        struct zpn_dispatcher_rpc rpc = {.send_zpn_broker_request = zpn_broker_request_local_cb,
                                         .send_zpn_broker_request_ack = zpn_broker_request_ack_local_cb,
                                         .send_zpn_app_route_info = zpn_broker_app_route_info_local_cb,
                                         .send_zpn_app_route_discovery = zpn_broker_app_route_discovery_local_cb,
                                         .send_zpn_dns_dispatch_check = zpn_dns_dispatch_check_local_cb,
                                         .send_zpn_dns_assistant_check = zpn_dns_assistant_check_local_cb,
                                         .send_zpn_dispatcher_status = zpn_dispatcher_status_local_cb};

        local_disp = zpn_dispatcher_create(config, rpc, zpn_cloud_adjusted_epoch_us, 1);
        if (!local_disp) {
            ZPN_LOG(AL_ERROR, "Couldn't initialize local dispatcher");
            return ZPN_RESULT_ERR;
        }
        zpn_dispatcher_register_private_broker(local_disp, ZPN_BROKER_GET_GID());

        if (zpn_dispatcher_enable_stats_logging(local_disp)) {
            return ZPN_RESULT_ERR;
        }
        if (zpn_dispatcher_enable_debug_interface(local_disp)) {
            return ZPN_RESULT_ERR;
        }

        local_disp_initialized = 1;
    }

    stats.init_done++;
    return ZPN_RESULT_NO_ERROR;
}

int zpn_local_dispatcher_stats_fill(void* cookie, int counter, void* structure_data)
{
    struct dsp_stats *out_data = (struct dsp_stats *)structure_data;
    memset(out_data, 0, sizeof(struct dsp_stats));
    memcpy(out_data, zpn_dispatcher_get_stats(local_disp), sizeof(struct dsp_stats));
    return ZPATH_RESULT_NO_ERROR;
}

int zpn_broker_dispatch_override_init()
{
    size_t override_desc_cnt = sizeof(zpn_broker_dispatch_config_override_desc) /
                               sizeof(zpn_broker_dispatch_config_override_desc[0]);
    size_t i;
    int res;

    for (i = 0; i < override_desc_cnt; i++) {
        res = zpath_config_override_desc_register(&zpn_broker_dispatch_config_override_desc[i]);
        if (res) {
            ZPN_LOG(AL_ERROR, "Could not register override description for %s: %s",
                    zpn_broker_dispatch_config_override_desc[i].key, zpn_result_string(res));
            return res;
        }
    }

    zpn_brk_dsp_circuit_breaker_config_override_monitor_init();

    return ZPN_RESULT_NO_ERROR;
}

int zpn_broker_dispatch_send_best(int64_t g_cst, struct argo_object *object)
{
    ZPN_BROKER_ASSERT_HARD(pool_initialized, "Dispatcher pool is not initialized");

    struct zpn_broker_dispatcher *dispatcher = zpn_brk_dsp_circuit_breaker_get_best_dsp(g_cst, zpn_err_brk_req_no_error, 0, NULL);

    if (!dispatcher) return ZPN_RESULT_NOT_READY;

    if (zpn_debug_get(ZPN_DEBUG_DISPATCHER_IDX)) {
        char dump[2000];
        if (argo_object_dump(object,
                             dump,
                             sizeof(dump),
                             NULL,
                             0) == ARGO_RESULT_NO_ERROR) {
            ZPN_DEBUG_DISPATCHER("%s: send %s", dispatcher->domain_name, dump);
        }
    }

    struct fohh_connection *f_conn = zpn_broker_dispatcher_channel_select(dispatcher);
    return fohh_argo_serialize_object(f_conn, object, 0, fohh_queue_element_type_control);
}

int zpn_broker_dispatch_send_struct_best(int64_t g_cst, struct argo_structure_description *desc, void *data)
{
    ZPN_BROKER_ASSERT_HARD(pool_initialized, "Dispatcher pool is not initialized");

    struct zpn_broker_dispatcher *dispatcher = NULL;
    int is_ckt_breaker = 0;
    int res;

    if (strcmp(argo_description_get_type(desc), "zpn_dns_dispatch_check") == 0) {
        dispatcher = zpn_brk_dsp_circuit_breaker_get_best_dsp_for_dns(g_cst, &is_ckt_breaker);
        if (!dispatcher) {
            ZPN_LOG(AL_ERROR, "No dispatcher to send DNS request for %"PRId64 " customer", g_cst);
            return ZPN_RESULT_NOT_READY;
        }
        struct zpn_dns_dispatch_check *check = data;
        check->g_dsp = dispatcher->instance_id;
    } else {
        dispatcher = zpn_brk_dsp_circuit_breaker_get_best_dsp(g_cst, zpn_err_brk_req_no_error, 0, &is_ckt_breaker);
    }

    if (!dispatcher) {
        ZPN_LOG(AL_ERROR, "No dispatcher ready for %"PRId64 " customer", g_cst);
        return ZPN_RESULT_NOT_READY;
    }

    if (zpn_debug_get(ZPN_DEBUG_DISPATCHER_IDX)) {
        char dump[2000];
        if (argo_structure_dump(desc,
                                data,
                                dump,
                                sizeof(dump),
                                NULL,
                                0) == ARGO_RESULT_NO_ERROR) {
            ZPN_DEBUG_DISPATCHER("%s: send %s", dispatcher->domain_name, dump);
        }
    }

    struct fohh_connection *f_conn = zpn_broker_dispatcher_channel_select(dispatcher);
    res = fohh_argo_serialize(f_conn, desc, data, 0, fohh_queue_element_type_control);
    if (!res && (strcmp(argo_description_get_type(desc), "zpn_dns_dispatch_check") == 0)) {
        __sync_add_and_fetch_8(&broker_dispatcher_stats.dns_req_sent_total, 1);
        __sync_add_and_fetch_8(&(dispatcher->stats.dns_req_sent_total), 1);
        if (is_ckt_breaker) {
            __sync_add_and_fetch_8(&broker_dispatcher_stats.ckt_breaker_dns_req_sent, 1);
            __sync_add_and_fetch_8(&(dispatcher->stats.ckt_breaker_dns_req_sent), 1);
        }
    }

    return res;
}

int zpn_broker_dispatch_send_pool(int64_t g_cst, struct argo_object *object)
{
    ZPN_BROKER_ASSERT_HARD(pool_initialized, "Dispatcher pool is not initialized");

    struct zpn_broker_dispatcher *dispatchers[1000];
    size_t dispatcher_count = sizeof(dispatchers) / sizeof(dispatchers[0]);
    size_t i;
    int res;

    res = zpn_broker_dispatcher_pool_all(g_cst, &(dispatchers[0]), &dispatcher_count);
    if (res) {
        return res;
    }

    if (!dispatcher_count) return ZPN_RESULT_NOT_READY;

    for (i = 0; i < dispatcher_count; i++) {
        struct fohh_connection *f_conn = zpn_broker_dispatcher_channel_select(dispatchers[i]);
        fohh_argo_serialize_object(f_conn, object, 0, fohh_queue_element_type_control);
    }
    return ZPN_RESULT_NO_ERROR;
}

int zpn_broker_dispatch_send_dispatcher(int64_t g_dsp, struct argo_object *object) {
    struct zpn_broker_dispatcher *dispatcher;

    dispatcher = zhash_table_lookup(dispatchers_table, &g_dsp, sizeof(g_dsp), NULL);

    if (!dispatcher) {
        ZPN_LOG(AL_ERROR, "Dispatcher with dispatcher id = %ld not found", (long)g_dsp);
        return ZPN_RESULT_NOT_READY;
    }

    struct fohh_connection *f_conn = zpn_broker_dispatcher_channel_select(dispatcher);
    fohh_argo_serialize_object(f_conn, object, 0, fohh_queue_element_type_control);

    char dump[8000];

    if (zpn_debug_get(ZPN_DEBUG_DISPATCHER_IDX)) {
        if (argo_object_dump(object, dump, sizeof(dump), NULL, 0) == ARGO_RESULT_NO_ERROR) {
            ZPN_LOG(AL_DEBUG, "%s: sent: %s", fohh_peer_cn(f_conn), dump);
        }
    }

    return ZPN_RESULT_NO_ERROR;
}

int zpn_broker_dispatch_send_struct_pool(int64_t g_cst, struct argo_structure_description *desc, void *data, enum fohh_queue_element_type elem_type)
{

    // only used in dev enviroments, when we need specific dispacther
    if( zpn_broker_is_dev_environment() ) {
        struct zpn_broker_dispatcher *sp_disp = get_pinned_dispatcher();
        if( sp_disp != NULL) {
            if ((sp_disp->block_app_reg_msg == 1) && (strcmp(argo_description_get_type(desc), "zpn_app_route_registration") == 0) ) {
                /* Skip sending route registration message to dispatcher */
                ZPN_DEBUG_DISPATCHER("%s: stopped sending rpc: %s", sp_disp->domain_name, argo_description_get_type(desc));
                return ZPN_RESULT_NO_ERROR;
            }
            // either flag is enabled
            if (zpn_debug_get(ZPN_DEBUG_DISPATCHER_IDX) || zpn_debug_get(ZPN_DEBUG_C2C_IDX)) {
                char dump[2000];
                if (argo_structure_dump(desc,
                                        data,
                                        dump,
                                        sizeof(dump),
                                        NULL,
                                        0) == ARGO_RESULT_NO_ERROR) {
                    ZPN_DEBUG_DISPATCHER("pinned dispatcher %s: send %s", sp_disp->domain_name, dump);
                }
            }
            struct fohh_connection *f_conn = zpn_broker_dispatcher_channel_select(sp_disp);
            fohh_argo_serialize(f_conn, desc, data, 0, elem_type);

            return ZPN_RESULT_NO_ERROR;
        }
    }
    ZPN_BROKER_ASSERT_HARD(pool_initialized, "Dispatcher pool is not initialized");

    struct zpn_broker_dispatcher *dispatchers[1000];
    size_t dispatcher_count = sizeof(dispatchers) / sizeof(dispatchers[0]);
    size_t i;
    int res;

    res = zpn_broker_dispatcher_pool_all(g_cst, &(dispatchers[0]), &dispatcher_count);
    if (res) {
        return res;
    }

    if (!dispatcher_count) return ZPN_RESULT_NOT_READY;

    for (i = 0; i < dispatcher_count; i++) {
        if ((dispatchers[i]->block_app_reg_msg == 1) && (strcmp(argo_description_get_type(desc), "zpn_app_route_registration") == 0) ) {
            /* Skip sending route registration message to dispatcher */
            ZPN_DEBUG_DISPATCHER("%s: stopped sending rpc: %s", dispatchers[i]->domain_name, argo_description_get_type(desc));
            continue;
        }
        if (zpn_debug_get(ZPN_DEBUG_DISPATCHER_IDX)) {
            char dump[2000];
            if (argo_structure_dump(desc,
                                    data,
                                    dump,
                                    sizeof(dump),
                                    NULL,
                                    0) == ARGO_RESULT_NO_ERROR) {
                ZPN_DEBUG_DISPATCHER("%s: send %s", dispatchers[i]->domain_name, dump);
            }
        }
        struct fohh_connection *f_conn = zpn_broker_dispatcher_channel_select(dispatchers[i]);
        fohh_argo_serialize(f_conn, desc, data, 0, elem_type);
    }
    return ZPN_RESULT_NO_ERROR;
}

int zpn_broker_dispatch_send_request(struct zpn_broker_request *req,
                                     enum zpn_err_brk_req last_disp_error,
                                     int64_t last_disp_id,
                                     int *no_disp_ready)
{
    int ret;
    int is_ckt_breaker = 0;
    ZPN_BROKER_ASSERT_HARD(pool_initialized, "Dispatcher pool is not initialized");

    struct zpn_broker_dispatcher *dispatcher = zpn_brk_dsp_circuit_breaker_get_best_dsp(ZPATH_GID_GET_CUSTOMER_GID(req->g_app), last_disp_error, last_disp_id, &is_ckt_breaker);

    if (!dispatcher) {
        if (no_disp_ready) {
            *no_disp_ready = 1;
        }
        ZPN_LOG(AL_ERROR, "%s: No dispatcher available at all.", req->mtunnel_id);
        __sync_add_and_fetch_8(&broker_dispatcher_stats.zpn_brk_req_send_fail_no_disp, 1);
        return ZPN_RESULT_NOT_READY;
    }

    req->g_dsp = dispatcher->instance_id;

    if (zpn_debug_get(ZPN_DEBUG_MTUNNEL_IDX)) {
        char dump[2000];
        if (argo_structure_dump(zpn_broker_request_description,
                                req,
                                dump,
                                sizeof(dump),
                                NULL,
                                0) == ARGO_RESULT_NO_ERROR) {
            ZPN_DEBUG_MTUNNEL("%s: send to %s: %s", req->mtunnel_id, dispatcher->domain_name, dump);
        }
    }

    struct fohh_connection *f_conn = zpn_broker_dispatcher_channel_select(dispatcher);

    ret = zpn_send_zpn_broker_request_struct(f_conn, 0, req);
    if ((ZPN_RESULT_NO_ERROR == ret) && zpn_meta_transaction_collection) {
        argo_log_structure_immediate(zpn_meta_transaction_collection,
                                     argo_log_priority_info,
                                     0,
                                     "BrkRqToDsp",
                                     zpn_broker_request_description,
                                     req);
    }
    if (ret) {
        ZPN_LOG(AL_ERROR, "%s: could not send to best dispatcher %s (%s)",
                req->mtunnel_id, dispatcher->domain_name, zpn_result_string(ret));
        __sync_add_and_fetch_8(&broker_dispatcher_stats.zpn_brk_req_send_fail, 1);
        __sync_add_and_fetch_8(&(dispatcher->stats.zpn_brk_req_send_fail), 1);
    } else {
        __sync_add_and_fetch_8(&broker_dispatcher_stats.zpn_brk_req_sent_total, 1);
        __sync_add_and_fetch_8(&(dispatcher->stats.zpn_brk_req_sent_total), 1);
        if (is_ckt_breaker) {
            __sync_add_and_fetch_8(&broker_dispatcher_stats.ckt_breaker_zpn_brk_req_sent, 1);
            __sync_add_and_fetch_8(&(dispatcher->stats.ckt_breaker_zpn_brk_req_sent), 1);
        }
    }

    return ret;
}

int zpn_broker_dispatch_send_request_ack(struct zpn_broker_request_ack *ack)
{
    ZPN_BROKER_ASSERT_HARD(pool_initialized, "Dispatcher pool is not initialized");

    struct zpn_broker_dispatcher *dispatcher;
    struct zpn_broker_dispatcher_stats *brk_dsp_stats = NULL;
    int res;

    if (!ack->g_dsp) {
        ZPN_LOG(AL_ERROR, "%s: No dispatcher_id.", ack->mtunnel_id);
        __sync_add_and_fetch_8(&broker_dispatcher_stats.zpn_brk_ack_send_fail_bad_param, 1);
        return ZPN_RESULT_NO_ERROR;
    }

    if (!ack->mtunnel_id) {
        ZPN_LOG(AL_ERROR, "Received dispatch ack without mtunnel_id");
        __sync_add_and_fetch_8(&broker_dispatcher_stats.zpn_brk_ack_send_fail_bad_param, 1);
        if (ZPN_BROKER_IS_PUBLIC()) {
            brk_dsp_stats = zpn_broker_dispatcher_get_stats(ack->g_dsp);
            if (brk_dsp_stats) {
                __sync_add_and_fetch_8(&(brk_dsp_stats->zpn_brk_ack_send_fail_bad_param), 1);
            }
        }
        return ZPN_RESULT_NO_ERROR;
    }

    dispatcher = zhash_table_lookup(dispatchers_table, &(ack->g_dsp), sizeof(ack->g_dsp), NULL);

    if (!dispatcher) {
        ZPN_LOG(AL_ERROR, "%s: Could not find dispatcher to which to return error for app %ld.", ack->mtunnel_id, (long)ack->g_app);
        __sync_add_and_fetch_8(&broker_dispatcher_stats.zpn_brk_ack_send_fail_no_disp, 1);
        return ZPN_RESULT_NO_ERROR;
    }

    ack->bfw_tx_us = epoch_us();
    struct fohh_connection *f_conn = zpn_broker_dispatcher_channel_select(dispatcher);
    res = zpn_send_zpn_broker_request_ack_struct(f_conn, 0, ack);
    if (res) {
        ZPN_LOG(AL_WARNING, "%s: Error returning request_ack: %s", ack->mtunnel_id, zpn_result_string(res));
        __sync_add_and_fetch_8(&broker_dispatcher_stats.zpn_brk_ack_send_fail, 1);
        __sync_add_and_fetch_8(&(dispatcher->stats.zpn_brk_ack_send_fail), 1);
    } else {
        ZPN_DEBUG_MTUNNEL("%s: Sent broker request ack for app %ld, mtunnel %s, broker %ld, with err = %s to dispatcher %ld",
                          ack->mtunnel_id,
                          (long) ack->g_app,
                          ack->mtunnel_id,
                          (long) ack->g_brk,
                          ack->error,
                          (long) ack->g_dsp);
    }

    return ZPN_RESULT_NO_ERROR;
}

int zpn_broker_dispatch_send_health(int fohh_thread_id, struct zpn_health_report *rep)
{
    ZPN_BROKER_ASSERT_HARD(pool_initialized, "Dispatcher pool is not initialized");

    int res;

    res = zpn_broker_dispatch_send_struct_pool(rep->g_cst, zpn_health_report_description, rep, fohh_queue_element_type_control);
    if (res == ZPN_RESULT_NO_ERROR) {
        int64_t now = epoch_s();
        char ip[ARGO_INET_ADDRSTRLEN];
        ZPN_DEBUG_HEALTH("Sent health report to pool (alive = %d, status = %12s, rtt = %7.3fms, report = %10ld, expires = %3ld) for cst %ld, app %ld, app_server %19ld, asst %ld, server %15s, s_port %4d, a_lat %lf, a_lon %lf, domain %s",
                         rep->alive,
                         rep->status ? rep->status : "NULL",
                         rep->aps_rtt / 1000.0,
                         (long)rep->report,
                         (long)(rep->expires - now),
                         (long) rep->g_cst,
                         (long) rep->g_app,
                         (long) rep->g_aps,
                         (long) rep->g_ast,
                         argo_inet_generate(ip, &(rep->s_ip)),
                         (int) rep->s_port,
                         rep->a_lat,
                         rep->a_lon,
                         rep->domain);
    }
    return ZPN_RESULT_NO_ERROR;
}

int zpn_broker_dispatch_send_app(struct zpn_app_route_registration *rep)
{
    ZPN_BROKER_ASSERT_HARD(pool_initialized, "Dispatcher pool is not initialized");

    int res;

    res = zpn_broker_dispatch_send_struct_pool(rep->g_cst, zpn_app_route_registration_description, rep,fohh_queue_element_type_control);
    if (res == ZPN_RESULT_NO_ERROR) {
        ZPN_DEBUG_HEALTH("Sent app report (alive = %d) for cst %ld, app %ld, asst %ld",
                         rep->alive,
                         (long) rep->g_cst,
                         (long) rep->g_app,
                         (long) rep->g_ast);
    }
    return ZPN_RESULT_NO_ERROR;
}

int zpn_broker_dispatch_send_auth_report_all(struct zpn_ast_auth_report *rep)
{
    ZPN_BROKER_ASSERT_HARD(pool_initialized, "Dispatcher pool is not initialized");

    int res;

    res = zpn_broker_dispatch_send_struct_pool(rep->g_cst, zpn_ast_auth_report_description, rep,fohh_queue_element_type_control);

    if (res == ZPN_RESULT_NO_ERROR) {
        ZPN_DEBUG_HEALTH("Sent ast auth report done");
    } else {
        ZPN_LOG(AL_ERROR, "%ld: Send to pool failed", (long) rep->g_cst);
    }

    return ZPN_RESULT_NO_ERROR;
}

static void zpn_broker_dispatch_dump_c2c_app_registration(const struct zpn_broker_dispatcher_app_registration *req)
{
    struct argo_object *obj = argo_object_create(zpn_broker_dispatcher_app_registration_description, (void *)req);
    if (obj) {
        char buf[2048];
        if (argo_object_dump(obj, buf, sizeof(buf), NULL, 0) == ARGO_RESULT_NO_ERROR) {
            ZPN_LOG(AL_DEBUG, "Sending c2c app reg to dispatcher: %s", buf);
        }
        argo_object_release(obj);
    }
}

int zpn_broker_dispatch_send_c2c_app_registration(int64_t customer_gid,
                                                  int64_t connect_us,
                                                  int64_t expire_s,
                                                  const char *cname,
                                                  int alive,
                                                  const char *client_fqdn,
                                                  const struct argo_inet *client_ip,
                                                  const char *hw_serial_id,
                                                  const char *log_msg) {
    struct zpn_broker_dispatcher_app_registration req = {0};
    const char *fqdn[MAX_CLIENT_FQDN];
    char ip[ARGO_INET_ADDRSTRLEN] = {0};
    int ret = ZPN_RESULT_NO_ERROR;

    if( cname == NULL ) {
        return ZPN_RESULT_BAD_ARGUMENT;
    }

    memset(&req, 0, sizeof(req));

    req.customer_gid = customer_gid;
    req.client_cname = cname;

    if (client_fqdn) {
        fqdn[req.fqdn_count++] = client_fqdn;
    }
    if (client_ip && client_ip->length) {
        fqdn[req.fqdn_count++] = argo_inet_generate(ip, client_ip);
    }

    if (!req.fqdn_count) {
        // Nothing to send.
        return ZPN_RESULT_NO_ERROR;
    }

    req.client_fqdn = fqdn;
    req.connect_us = connect_us;
    req.alive = alive;
    req.g_brk = ZPN_BROKER_GET_GID();

    req.machine_id = hw_serial_id;
    req.expire_s = expire_s;

    if (zpn_debug_get(ZPN_DEBUG_DISPATCHER_IDX) || zpn_debug_get(ZPN_DEBUG_C2C_IDX)) {
        zpn_broker_dispatch_dump_c2c_app_registration(&req);
    }

    if (zpn_meta_transaction_collection) {
        argo_log_structure_immediate(zpn_meta_transaction_collection,
                                     argo_log_priority_info,
                                     0,
                                     "BrkToDispC2CReg",
                                     zpn_broker_dispatcher_app_registration_description,
                                     &req);
    }

    if (ZPN_BROKER_IS_PRIVATE()) {
        // sending from PB
        ret = zpn_broker_dispatch_send_c2c_app_registration_local(&req);

    } else {
        ret = zpn_broker_dispatch_send_struct_pool(customer_gid, zpn_broker_dispatcher_app_registration_description,
                                                   &req, fohh_queue_element_type_control);
    }

    if (ret) {
        ZPN_LOG(AL_ERROR, "Could not send c2c app %s %s with fqdn=%s ip=%s to dispatcher for %s due to %s",
                log_msg ? : "",
                alive ? "registration" : "deregistration",
                client_fqdn ? : "(null)",
                ip[0] ? ip : "(null)",
                cname,
                zpn_result_string(ret));
        __sync_add_and_fetch_8(&broker_dispatcher_stats.zpn_brk_c2c_msg_send_fail, 1);
    }

    return ret;
}

/*
 *  sends c2c registration to provided dispatcher
 */
int zpn_broker_dispatch_send_c2c_registration_to_one_dispatcher(struct zpn_broker_client_fohh_state *cs,
                                                                struct fohh_connection *f_conn,
                                                                int64_t g_dsp)
{
    struct zpn_broker_dispatcher_app_registration req;
    struct zpn_broker_dispatcher_stats *brk_dsp_stats = NULL;
    const char *fqdn[MAX_CLIENT_FQDN];
    char ip[ARGO_INET_ADDRSTRLEN] = {0};

    memset(&req, 0, sizeof(req));

    req.customer_gid = cs->customer_gid;
    req.client_cname = cs->endpoint_cname ? : cs->cname;

    if (cs->client_fqdn) {
        fqdn[req.fqdn_count++] = cs->client_fqdn;
    }
    if (cs->c2c_ip.reserved_ip.length) {
        fqdn[req.fqdn_count++] = argo_inet_generate(ip, &cs->c2c_ip.reserved_ip);
    }

    req.client_fqdn = fqdn;
    req.connect_us = cs->connect_us;
    req.alive = cs->fqdn_registered || cs->c2c_ip.reserved_ip.length;
    req.g_brk = ZPN_BROKER_GET_GID();
    req.expire_s = zpn_cloud_adjusted_epoch_s() + cs->c2c_expiration_ttl_s;
    req.machine_id = cs->hash_hardware_id;

    if (zpn_debug_get(ZPN_DEBUG_DISPATCHER_IDX) || zpn_debug_get(ZPN_DEBUG_C2C_IDX)) {
        zpn_broker_dispatch_dump_c2c_app_registration(&req);
    }

    // Serialize directly to f_conn without tlv
    const int ret = fohh_argo_serialize(f_conn, zpn_broker_dispatcher_app_registration_description,
                                        &req, 0, fohh_queue_element_type_control);

    if (ret) {
        ZPN_LOG(AL_ERROR, "Could not send c2c app registration with fqdn=%s ip=%s to dispatcher for %s - %s",
                cs->client_fqdn ? : "n/a", ip[0] ? ip : "n/a", req.client_cname, zpn_result_string(ret));
        __sync_add_and_fetch_8(&broker_dispatcher_stats.zpn_brk_c2c_msg_send_disp_fail, 1);
        if (ZPN_BROKER_IS_PUBLIC()) {
            brk_dsp_stats = zpn_broker_dispatcher_get_stats(g_dsp);
            if (brk_dsp_stats) {
                __sync_add_and_fetch_8(&(brk_dsp_stats->zpn_brk_c2c_msg_send_disp_fail), 1);
            }
        }
    }

    return ret;
}

static int
zpn_broker_dispatch_stats_dump(struct zpath_debug_state*   request_state,
                               const char**                query_values,
                               int                         query_value_count,
                               void*                       cookie)
{
    char        jsonout[10000];

    if (ARGO_RESULT_NO_ERROR ==
        argo_structure_dump(zpn_broker_dispatch_audit_description, &audit, jsonout, sizeof(jsonout), NULL, 1)){
        ZDP("%s\n", jsonout);
    }
    if (ARGO_RESULT_NO_ERROR ==
        argo_structure_dump(zpn_broker_dispatch_stats_description, &stats, jsonout, sizeof(jsonout), NULL,
                            1)){
        ZDP("%s\n", jsonout);
    }

    {
        int curr_fohh_thread;
        int max_fohh_thread = fohh_thread_count();
        for (curr_fohh_thread = 0; curr_fohh_thread < max_fohh_thread; curr_fohh_thread++) {
            if (ARGO_RESULT_NO_ERROR == argo_structure_dump(zpn_broker_dispatch_fohh_stats_description,
                                fohh_stats[curr_fohh_thread], jsonout, sizeof(jsonout), NULL, 1)) {
                ZDP("%s\n", jsonout);
            }
        }
    }

    return ZPATH_RESULT_NO_ERROR;
}


static int
zpn_broker_dispatch_stats_clear(struct zpath_debug_state*   request_state,
                                const char**                query_values,
                                int                         query_value_count,
                                void*                       cookie)
{
    bzero(&stats, sizeof(stats));
    {
        int curr_fohh_thread;
        int max_fohh_thread = fohh_thread_count();
        for (curr_fohh_thread = 0; curr_fohh_thread < max_fohh_thread; curr_fohh_thread++) {
            bzero(fohh_stats[curr_fohh_thread], sizeof(*fohh_stats[curr_fohh_thread]));
        }
    }
    __sync_add_and_fetch_8(&audit.stats_cleared, 1);
    return ZPATH_RESULT_NO_ERROR;
}

int zpn_broker_dispatch_fwd_request(struct zpn_broker_request *msg) {
    ZPN_BROKER_ASSERT_HARD(ZPN_BROKER_IS_PRIVATE(), "Not a private broker");

    struct zpn_connected_broker *brk = zpn_pbroker_get_connected_broker();
    if (!brk) {
        ZPN_LOG(AL_WARNING, "Couldn't get connected broker");
        return ZPN_RESULT_CANT_WRITE;
    }

    int res = zpn_send_zpn_broker_request_struct(brk->f_conn, brk->f_conn_incarnation, msg);
    if (res) {
        ZPN_LOG(AL_WARNING, "Couldn't send to broker: %s %s", fohh_description(brk->f_conn), zpath_result_string(res));
    }
    return res;
}

int zpn_broker_dispatch_fwd_dns_req(struct zpn_dns_dispatch_check *msg) {
    ZPN_BROKER_ASSERT_HARD(ZPN_BROKER_IS_PRIVATE(), "Not a private broker");

    struct zpn_connected_broker *brk = zpn_pbroker_get_connected_broker();
    if (!brk) {
        ZPN_LOG(AL_WARNING, "Couldn't get connected broker");
        return ZPN_RESULT_CANT_WRITE;
    }

    int res = zpn_send_zpn_dns_dispatch_check_struct(brk->f_conn, brk->f_conn_incarnation, msg);
    if (res) {
        ZPN_LOG(AL_WARNING, "Couldn't send to broker: %s %s", fohh_description(brk->f_conn), zpath_result_string(res));
        return res;
    } else {
        ZPN_LOG(AL_DEBUG, "Forwarded dns dispatch check for app=%ld domain=%s", (long)msg->g_app, msg->name);
    }

    return res;
}

int zpn_broker_dispatch_fwd_fqdn_c2c_check_req(struct zpn_broker_dispatcher_c2c_app_check *msg) {

    struct zpn_connected_broker *brk = zpn_pbroker_get_connected_broker();
    if (!brk) {
        ZPN_LOG(AL_WARNING, "Couldn't get connected control broker for remote c2c check fqdn:%s", msg->app_fqdn);
        return ZPN_RESULT_CANT_WRITE;
    }

    int res = zpn_send_dispatch_fqdn_c2c_check_struct(brk->f_conn, brk->f_conn_incarnation, msg);
    if (res) {
        ZPN_LOG(AL_WARNING, "Couldn't send remote C2C to broker: %s %s fqdn:%s", fohh_description(brk->f_conn), zpath_result_string(res), msg->app_fqdn);
        return res;
    } else {
        ZPN_DEBUG_BROKER_C2C_FQDN_CHECK("Sent remote c2c check for fqdn:%s to Broker:%s", msg->app_fqdn, fohh_description(brk->f_conn));
    }

    return res;
}

int zpn_broker_dispatch_send_request_local(struct zpn_broker_request *req, int is_c2c_regex_match, int is_ld_bypass) {
    ZPN_BROKER_ASSERT_HARD(local_disp_initialized, "Local dispatcher is not initialized");

    if (is_c2c_regex_match) {
        zpn_dispatcher_on_msg_broker_request_c2c_match(local_disp, req, is_ld_bypass);
    } else {
        zpn_dispatcher_on_msg_broker_request(local_disp, req);
    }

    req->g_dsp = ZPN_BROKER_GET_GID(); // TODO: Get from distpatcher.

    return ZPN_RESULT_NO_ERROR;
}

int zpn_broker_dispatch_send_request_ack_local(struct zpn_broker_request_ack *req) {

    return ZPN_RESULT_NOT_IMPLEMENTED;
}

int zpn_broker_dispatch_send_health_local(int fohh_thread_id, struct zpn_health_report *rep) {
    ZPN_BROKER_ASSERT_HARD(local_disp_initialized, "Local dispatcher is not initialized");

    zpn_dispatcher_on_msg_health_report(local_disp, rep);
    return ZPN_RESULT_NO_ERROR;
}

int zpn_broker_dispatch_send_app_local(struct zpn_app_route_registration *rep) {
    ZPN_BROKER_ASSERT_HARD(local_disp_initialized, "Local dispatcher is not initialized");

    zpn_dispatcher_on_msg_app_route_registration(local_disp, rep);
    return ZPN_RESULT_NO_ERROR;
}

int zpn_broker_dispatch_send_auth_report_local(struct zpn_ast_auth_report *rep) {
    ZPN_BROKER_ASSERT_HARD(local_disp_initialized, "Local dispatcher is not initialized");

    zpn_dispatcher_on_msg_ast_auth_report(local_disp, rep);
    return ZPN_RESULT_NO_ERROR;
}

int zpn_broker_dispatch_send_dns_req_local(struct zpn_dns_dispatch_check *rep) {
    ZPN_BROKER_ASSERT_HARD(local_disp_initialized, "Local dispatcher is not initialized");

    zpn_dispatcher_on_msg_dns_dispatch_check(local_disp, rep);
    return ZPN_RESULT_NO_ERROR;
}

int zpn_broker_dispatch_is_c2c_local_route_exist(int64_t customer_gid, const char *fqdn) {
    ZPN_BROKER_ASSERT_HARD(local_disp_initialized, "Local dispatcher is not initialized");

    return zpn_dispatcher_is_c2c_route_exist(local_disp, customer_gid, fqdn);
}

int zpn_broker_dispatch_send_dns_reply_local(struct zpn_dns_assistant_check *rep) {
    ZPN_BROKER_ASSERT_HARD(local_disp_initialized, "Local dispatcher is not initialized");

    zpn_dispatcher_on_msg_dns_assistant_check(local_disp, rep);
    return ZPN_RESULT_NO_ERROR;
}

int zpn_broker_dispatch_send_c2c_app_registration_local(struct zpn_broker_dispatcher_app_registration *req) {
    ZPN_BROKER_ASSERT_HARD(local_disp_initialized, "Local dispatcher is not initialized");

    zpn_dispatcher_on_msg_broker_dispatcher_app_registration(local_disp, req);

    return ZPN_RESULT_NO_ERROR;
}
/*
 * sends broker_req_ack in case of error during processing of broker_req in the broker
 */
int zpn_send_broker_req_ack_error_locked(struct zpn_broker_client_fohh_state *a_state,
                                         const struct zpn_broker_request *req,
                                         const char *error_desc,
                                         int allow_all_xport,
                                         const char *mtunnel_id) {
    // message must be used on same thread !
    struct zpn_broker_request_ack req_ack;

    req_ack.mtunnel_id = mtunnel_id;

    // copy data from broker request to response
    req_ack.g_app = req->g_app;
    req_ack.g_aps = req->g_aps;
    req_ack.g_ast = req->g_ast;
    req_ack.g_ast_grp = req->g_ast_grp;
    req_ack.g_brk = req->g_brk;

    req_ack.g_dsp = req->g_dsp;
    req_ack.g_bfw = req->g_bfw;
    req_ack.app_type = req->app_type;
    req_ack.domain = req->domain;
    req_ack.ip_protocol = req->ip_protocol;
    req_ack.app_type = req->app_type;
    req_ack.seq_num = req->seq_num;
    req_ack.a_inet = req->s_ip;
    req_ack.a_port = req->c_port;
    req_ack.s_inet = req->s_ip;
    req_ack.s_port = req->c_port;
    req_ack.g_srv_grp = req->g_srv_grp;
    req_ack.error = error_desc;

    return send_broker_req_ack_error(a_state, &req_ack, allow_all_xport);
}

int zpn_forward_broker_req_ack_error(struct zpn_broker_client_fohh_state *a_state,
                                         const struct zpn_broker_request_ack *req,
                                         int allow_all_xport,
                                         const char *mtunnel_id) {
    struct zpn_broker_request_ack req_ack;

    req_ack.mtunnel_id = mtunnel_id;

    // copy data from broker request to response
    req_ack.g_app = req->g_app;
    req_ack.g_aps = req->g_aps;
    req_ack.g_ast = req->g_ast;
    req_ack.g_brk = req->g_brk;

    req_ack.g_dsp = req->g_dsp;
    req_ack.g_bfw = req->g_bfw;
    req_ack.app_type = req->app_type;
    req_ack.domain = req->domain;
    req_ack.ip_protocol = req->ip_protocol;
    req_ack.app_type = req->app_type;
    req_ack.seq_num = req->seq_num;
    req_ack.a_inet = req->a_inet;
    req_ack.a_port = req->a_port;
    req_ack.s_inet = req->s_inet;
    req_ack.s_port = req->s_port;

    req_ack.error = req->error;

    return send_broker_req_ack_error(a_state, &req_ack, allow_all_xport);
}

/*
 *
 */
static int send_broker_req_ack_error(struct zpn_broker_client_fohh_state *a_state,
                                            struct zpn_broker_request_ack *req_ack,
                                            int allow_all_xport) {
    int res = ZPN_RESULT_NO_ERROR;

    if (ZPN_BROKER_IS_PRIVATE()) {
        if (req_ack->g_brk != req_ack->g_dsp) {
            // send to public broker via pb_client

            if (a_state) {
                struct zpn_tlv *broker_tlv;
                struct zpn_pb_client *pb_client;

                pb_client = get_pb_client(a_state, allow_all_xport, req_ack->mtunnel_id);
                if (!pb_client) {
                    ZPN_LOG(AL_ERROR, "%s: No pb client", req_ack->mtunnel_id);
                    return ZPN_RESULT_ERR;
                }

                broker_tlv = zpn_pb_get_tlv(pb_client);
                if (!broker_tlv) {
                    ZPN_LOG(AL_ERROR, "%s: No PB TLV", req_ack->mtunnel_id);
                    return ZPN_RESULT_ERR;
                }

                res = tlv_argo_serialize(broker_tlv, zpn_broker_request_ack_description, req_ack, 0,
                                         fohh_queue_element_type_mission_critical);
                if (ZPN_RESULT_NO_ERROR != res) {
                    return ZPN_RESULT_ERR;
                }
            }
        }

    } else {
        ZPN_BROKER_ASSERT_HARD(pool_initialized, "Dispatcher pool is not initialized");

        struct zpn_broker_dispatcher *dispatcher;

        if (!req_ack->g_dsp) {
            ZPN_LOG(AL_ERROR, "%s: No dispatcher_id.", req_ack->mtunnel_id);
            __sync_add_and_fetch_8(&broker_dispatcher_stats.zpn_brk_req_ack_err_send_fail_bad_param, 1);
            return ZPN_RESULT_NO_ERROR;
        }

        // find the dispatcher that sent the request
        dispatcher = zhash_table_lookup(dispatchers_table, &(req_ack->g_dsp), sizeof(req_ack->g_dsp), NULL);

        if (!dispatcher) {
            // try the best one
            dispatcher = zpn_brk_dsp_circuit_breaker_get_best_dsp(ZPATH_GID_GET_CUSTOMER_GID(req_ack->g_app), zpn_err_brk_req_no_error, 0, NULL);

            if (!dispatcher) {
                ZPN_LOG(AL_ERROR, "%s: Could not find dispatcher to which to return error for app %ld.",
                        req_ack->mtunnel_id, (long)req_ack->g_app);
                __sync_add_and_fetch_8(&broker_dispatcher_stats.zpn_brk_req_ack_err_send_fail_bad_param, 1);
                return ZPN_RESULT_NO_ERROR;
            }
        }

        if (!dispatcher) {
            ZPN_LOG(AL_ERROR, "%s: No dispatcher available at all.", req_ack->mtunnel_id);
            return ZPN_RESULT_NOT_READY;
        }
        req_ack->g_dsp = dispatcher->instance_id;

        if (zpn_debug_get(ZPN_DEBUG_C2C_IDX)) {
            char dump[2000];
            if (argo_structure_dump(zpn_broker_request_ack_description, req_ack, dump, sizeof(dump), NULL, 0) ==
                ARGO_RESULT_NO_ERROR) {
                ZPN_DEBUG_LOG(AL_DEBUG, "%s: send to %s: %s", req_ack->mtunnel_id, dispatcher->domain_name, dump);
            }
        }

        struct fohh_connection *f_conn = zpn_broker_dispatcher_channel_select(dispatcher);

        res = zpn_send_zpn_broker_request_ack_struct(f_conn, 0, req_ack);

        if (res) {
            ZPN_LOG(AL_ERROR, "%s: could not send to best dispatcher %s (%s)", req_ack->mtunnel_id,
                    dispatcher->domain_name, zpn_result_string(res));
            __sync_add_and_fetch_8(&broker_dispatcher_stats.zpn_brk_req_ack_err_send_fail, 1);
            __sync_add_and_fetch_8(&(dispatcher->stats.zpn_brk_req_ack_err_send_fail), 1);
        } else {
            __sync_add_and_fetch_8(&broker_dispatcher_stats.zpn_brk_req_ack_err_sent, 1);
            __sync_add_and_fetch_8(&(dispatcher->stats.zpn_brk_req_ack_err_sent), 1);
        }
    }

    return res;
}
