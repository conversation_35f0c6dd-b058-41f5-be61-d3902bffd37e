/*
 * zpn_broker_dispatch_pool.c. Copyright (C) 2018 Zscaler Inc. All Rights Reserved
 */

/*
 *
 * Distribution of health and connection requests occurs by hashing a 'key'
 *
 * key is currently based on app_id. In the future it will be based on
 * customer.
 *
 * Switching what is used for 'key' is extremely difficult unless
 * doing so with only one dispatcher pool.
 *
 * Consider the following timeline:
 *
 * Time = T0: last pool configuration change. Whenever a pool is added
 *            or removed, T0 starts at that time. At any point
 *            afterwards until T5 is reached, ANY dispatcher change
 *            will update T0 to the time of the change.
 *
 * Time = T1: Beginning of phase 1, end of idle time waiting for
 *            configuration changes.
 *
 * Time = T2: Beginning of phase 2
 *
 * Time = T3: Beginning of phase 3
 *
 * Time = T4: Beginning of phase 4.
 *
 * Time = T5: Update complete.
 *
 *
 * Dispatcher pool update/replacement mechanism:
 *
 * When a change is detected that changes the number of pools, a timer
 * is started. This it T0. So long as pool changes continue to be made
 * (even if it does not result in the addition/removal of a pool), T0
 * keeps advancing.
 *
 * The original configuration is “Config A”
 *
 * The new configuration is “Config B”
 *
 * Hash all keys into 4 groups, call them keys 1, 2, 3, 4. When
 * hashing for this purpose, the most significant 2 bits of the hash
 * are used.
 *
 * Phase 0, Before T1
 *       Health is sent to Config A for Keys 1, 2, 3, 4
 *       Health is sent to Config B for nothing
 *       Mtunnels for Key 1 use Config A
 *       Mtunnels for Key 2 use Config A
 *       Mtunnels for Key 3 use Config A
 *       Mtunnels for Key 4 use Config A
 *
 * Phase 1, T1 to T2:
 *       Health is sent to Config A for Key  1, 2, 3, 4
 *       Health is sent to Config B for Key  1
 *       Mtunnels for Key 1 use Config A
 *       Mtunnels for Key 2 use Config A
 *       Mtunnels for Key 3 use Config A
 *       Mtunnels for Key 4 use Config A
 *
 * Phase 2, T2 to T3:
 *       Health is sent to Config A for Key  2, 3, 4
 *       Health is sent to Config B for Key  1, 2
 *       Mtunnels for Key 1 use Config B
 *       Mtunnels for Key 2 use Config A
 *       Mtunnels for Key 3 use Config A
 *       Mtunnels for Key 4 use Config A
 *
 * Phase 3, T3 to T4:
 *       Health is sent to Config A for Key  3, 4
 *       Health is sent to Config B for Key  1, 2, 3
 *       Mtunnels for Key 1 use Config B
 *       Mtunnels for Key 2 use Config B
 *       Mtunnels for Key 3 use Config A
 *       Mtunnels for Key 4 use Config A
 *
 * Phase 4, T4 to T5:
 *       Health is sent to Config A for Key  4
 *       Health is sent to Config B for Key  1, 2, 3, 4
 *       Mtunnels for Key 1 use Config B
 *       Mtunnels for Key 2 use Config B
 *       Mtunnels for Key 3 use Config B
 *       Mtunnels for Key 4 use Config A
 *
 * Phase 5, After T5
 *       Health is sent to Config A for nothing
 *       Health is sent to Config B for Key  1, 2, 3, 4
 *       Mtunnels for Key 1 use Config B
 *       Mtunnels for Key 2 use Config B
 *       Mtunnels for Key 3 use Config B
 *       Mtunnels for Key 4 use Config B
 */

#include <math.h>

#include "zthread/zthread.h"
#include "zhash/zhash_table.h"
#include "fohh/fohh.h"
#include "zpath_lib/zpath_instance.h"
#include "zpath_lib/zpath_constellation.h"
#include "zpath_lib/zpath_entity.h"
#include "zpath_lib/zpath_config_override.h"
#include "zpath_lib/zpath_config_override_keys.h"

#include "zpn/zpn_lib.h"

#include "zpn/zpn_broker_dispatch.h"
#include "zpn/zpn_broker_dispatch_pool.h"
#include "zpn/zpn_broker.h"

static struct zpn_broker_dispatcher_pool_set *current_pools = NULL;
static struct zpn_broker_dispatcher_pool_set *next_pools = NULL;
static struct zpn_broker_dispatcher_pool_set *custom_pools = NULL;
static int64_t current_pools_created_s = 0;
static int64_t next_pools_created_s = 0;
static double my_lat = 0;
static double my_lon = 0;
static int64_t brk_dsp_circuit_breaker_hard_disabled = 0;
static int64_t brk_dsp_circuit_breaker_enabled = 0;
struct zevent_base *broker_misc_thread_zevent_base = NULL;
static int brk_dsp_circuit_breaker_test = 0;
struct zpn_broker_dispatcher_stats *brk_dsp_stats = NULL;

int64_t next_pool_interval_s = DEFAULT_NEXT_POOL_INTERVAL_S;

/*
 * Some static variables, etc, to ease testing
 */
int64_t zpdp_test_current_sequence;
struct zpath_instance zpdp_test_instances[100];
size_t zpdp_test_instances_count = 0;

char *test_dispatcher_name;

static struct zhash_table *custom_pool_name_from_gid = NULL;

/* Would like to use ZPATH_MUTEX but linux doesn't support its static initialization?! */
#define ZBDP_MUTEX_INIT               (pthread_mutex_t)PTHREAD_MUTEX_INITIALIZER
#define ZBDP_MUTEX_LOCK(x, f, l)      pthread_mutex_lock(x)
#define ZBDP_MUTEX_UNLOCK(x, f, l)    pthread_mutex_unlock(x)
static pthread_mutex_t custom_pool_lock = ZBDP_MUTEX_INIT;

const char *zpn_brk_dsp_circuit_breaker_state_str[] = {
    [brk_dsp_circuit_breaker_state_close] = "CLOSE",
    [brk_dsp_circuit_breaker_state_partial_open_10] = "PARTIAL_OPEN_10",
    [brk_dsp_circuit_breaker_state_partial_open_20] = "PARTIAL_OPEN_20",
    [brk_dsp_circuit_breaker_state_partial_open_40] = "PARTIAL_OPEN_40",
    [brk_dsp_circuit_breaker_state_open] = "OPEN",
    [brk_dsp_circuit_breaker_state_partial_open_90] = "PARTIAL_OPEN_90",
    [brk_dsp_circuit_breaker_state_partial_open_80] = "PARTIAL_OPEN_80",
    [brk_dsp_circuit_breaker_state_partial_open_60] = "PARTIAL_OPEN_60"
};

int zpn_brk_dsp_circuit_breaker_open_percent[] = {
    [brk_dsp_circuit_breaker_state_close] = 0,
    [brk_dsp_circuit_breaker_state_partial_open_10] = 10,
    [brk_dsp_circuit_breaker_state_partial_open_20] = 20,
    [brk_dsp_circuit_breaker_state_partial_open_40] = 40,
    [brk_dsp_circuit_breaker_state_open] = 100,
    [brk_dsp_circuit_breaker_state_partial_open_90] = 90,
    [brk_dsp_circuit_breaker_state_partial_open_80] = 80,
    [brk_dsp_circuit_breaker_state_partial_open_60] = 60
};

static int compare_pool_sets(const void *a, const void *b) {
    const struct zpn_broker_dispatcher_pool *aa = a;
    const struct zpn_broker_dispatcher_pool *bb = b;
    return strcmp(aa->pool_name, bb->pool_name);
}

static int compare_dispatchers(const void *a, const void *b) {
    const struct zpn_broker_dispatcher *aa = *((const struct zpn_broker_dispatcher **) a);
    const struct zpn_broker_dispatcher *bb = *((const struct zpn_broker_dispatcher **) b);
    ZPN_DEBUG_DISPATCHER("Dist to a=%s is %8.2f, dist to b=%s is %8.2f", aa->domain_name, aa->distance, bb->domain_name, bb->distance);
    if (aa->distance < bb->distance) return -1;
    if (aa->distance > bb->distance) return 1;
    return 0;
}


static struct zpn_broker_dispatcher_pool_set *pool_set_alloc() {
    struct zpn_broker_dispatcher_pool_set *pool_set = ZPN_CALLOC(sizeof(*pool_set));
    pool_set->pools_by_name = zhash_table_alloc(&zpn_allocator);
    pool_set->creation_epoch_s = epoch_s();
    return pool_set;
}


static void pool_set_free(struct zpn_broker_dispatcher_pool_set *pool_set) {
    size_t i;

    if (!pool_set)
        return;

    zhash_table_free(pool_set->pools_by_name);
    for (i = 0; i < pool_set->pool_count; i++) {
        ZPN_FREE(pool_set->pools[i].pool_name);
    }
    ZPN_FREE(pool_set);
}


static void pool_set_free_slow(struct zpn_broker_dispatcher_pool_set *pool_set) {
    size_t i;

    if (!pool_set)
        return;

    zhash_table_free_slow(pool_set->pools_by_name);
    for (i = 0; i < pool_set->pool_count; i++) {
        ZPN_FREE_SLOW(pool_set->pools[i].pool_name);
    }
    ZPN_FREE_SLOW(pool_set);
}


/* Sort pools + dispatchers. Pools sorted by name, dispatchers sorted by distance to us.
 non static, so it can be used in unit tests
*/
void pool_set_sort(struct zpn_broker_dispatcher_pool_set *pool_set) {
    int res;
    qsort(&(pool_set->pools[0]), pool_set->pool_count, sizeof(pool_set->pools[0]), compare_pool_sets);

    for (size_t i = 0; i < pool_set->pool_count; i++) {
        struct zpn_broker_dispatcher_pool *pool = &(pool_set->pools[i]);
        ZPN_LOG(AL_NOTICE, "Sorting pool %s of %ld dispatchers", pool->pool_name, (long)pool->dispatcher_count);
        qsort(&(pool->dispatchers[0]), pool->dispatcher_count, sizeof(pool->dispatchers[0]), compare_dispatchers);

        // pool_set->pools_by_name needs to be updated after the sort of pool_set->pools[0] ET-35636
        zhash_table_remove(pool_set->pools_by_name, pool->pool_name, strlen(pool->pool_name), NULL);
        res = zhash_table_store(pool_set->pools_by_name, pool->pool_name, strlen(pool->pool_name), 1, pool);
        if (res) {
            ZPN_LOG(AL_NOTICE, "pool_set_sort failed to store new pool %s", pool->pool_name);
        }
    }
}


static void pool_set_compare(struct zpn_broker_dispatcher_pool_set *pool_set_old,
                             struct zpn_broker_dispatcher_pool_set *pool_set_new,
                             int *pool_changed,
                             int *refactor_pool) {
    if (!pool_set_old) {
        *pool_changed = 1;
        return;
    }

    /* If pool count is different... */
    if (pool_set_old->pool_count != pool_set_new->pool_count) {
        ZPN_DEBUG_DISPATCHER("Pool count changed.");
        if (refactor_pool) *refactor_pool = 1;
        *pool_changed = 1;
    } else {
        /* Or pool name is different... */
        for (size_t i = 0; i < pool_set_old->pool_count; i++) {
            if (strcmp(pool_set_old->pools[i].pool_name, pool_set_new->pools[i].pool_name)) {
                ZPN_DEBUG_DISPATCHER("Pool name changed.");
                if (refactor_pool) *refactor_pool = 1;
                *pool_changed = 1;
            }
            if (pool_set_old->pools[i].dispatcher_count != pool_set_new->pools[i].dispatcher_count) {
                ZPN_DEBUG_DISPATCHER("Pool dispatcher count changed.");
                *pool_changed = 1;
            }
        }

        if (!*pool_changed) {
            /* Or pool members are different... */
            for (size_t i = 0; i < pool_set_old->pool_count; i++) {
                for (size_t j = 0; j < pool_set_old->pools[i].dispatcher_count; j++) {
                    if (pool_set_old->pools[i].dispatchers[j] != pool_set_new->pools[i].dispatchers[j]) {
                        ZPN_DEBUG_DISPATCHER("Pool dispatcher changed.");
                        *pool_changed = 1;
                    }
                }
            }
        }
    }
}

/*
 * For the moment, this routine only runs for MAX_DISPATCHERS or so
 * dispatchers. Suck it up.
 *
 * This routine returns a new pool if the pool is different from
 * either the currently active pool or the next active pool. The
 * returned pool should replace an existing pool, except if
 * 'refactor_pool' is set.
 *
 * This routine also returns a new custom pool if it
 * differs from currently used one.
 *
 * refactor_pool will be set if the actual number of non-custom pools
 * changes.
 */
static void gen_pools_from_instances(
        struct zpn_broker_dispatcher_pool_set **new_pools,
        struct zpn_broker_dispatcher_pool_set **new_custom_pools,
        int *refactor_pool,
        double local_lat,
        double local_lon,
        int test_mode) {
    struct zpath_instance *instances[MAX_DISPATCHERS];
    size_t instance_count = MAX_DISPATCHERS;
    size_t i;
    int res;

    struct zpn_broker_dispatcher_pool_set *pool_set;
    struct zpn_broker_dispatcher_pool_set *comp_pool_set = NULL;
    struct zpn_broker_dispatcher_pool_set *new_pool_set = NULL;
    struct zpn_broker_dispatcher_pool_set *new_custom_pool_set = NULL;

    char domain_name[ZPN_MAX_DOMAIN_NAME_LEN + 1];
    size_t domain_name_len;

    int updated_lat_lon = 0;
    int updated_custom_lat_lon = 0;
    int pool_changed = 0;
    int custom_pool_changed = 0;
    *new_pools = NULL;
    *new_custom_pools = NULL;
    *refactor_pool = 0;

    if (test_mode) {
        /* If testing, just 'get' the statically configured (by test
         * code, presumably) instances */
        if (!zpdp_test_instances_count) {
            return;
        }
        for (i = 0; i < zpdp_test_instances_count; i++) {
            instances[i] = &(zpdp_test_instances[i]);
        }
        instance_count = zpdp_test_instances_count;
    } else {
        res = zpath_instance_get_instances_for_role("zpn_dispatcher", &(instances[0]), &instance_count, 0);
        if (res || (!instance_count)) {
            return;
        }
    }

    new_pool_set = pool_set_alloc();
    new_custom_pool_set = pool_set_alloc();

    /* For each instance, get its dispatcher reference. If necessary,
     * create the dispatcher */
    for (i = 0; i < instance_count; i++) {
        struct zpn_broker_dispatcher *dispatcher;
        char pool_name[100];
        size_t pool_name_len;
        const char *instance_name;
        struct zpn_broker_dispatcher_pool *pool;
        struct zpath_instance *instance = instances[i];

        /* Extract the pool name and instance name */
        {
            const char *fields[4];
            size_t fields_length[4];
            int count;

            count = zpath_entity_extract_strings(instance->name, &(fields[0]), &(fields_length[0]));
            if (count != 4) {
                ZPN_LOG(AL_ERROR, "Could not parse instance name %s", instance->name);
                continue;
            } else {
                const char *w2;
                char *w1, *e;
                w1 = &(pool_name[0]);
                e = w1 + sizeof(pool_name);
                w2 = fields[3];

                while ((*w2) && (w1 < e) && ((*w2) != '.') && ((*w2) != '-')) {
                    (*w1) = (*w2);
                    w1++;
                    w2++;
                }
                if (w1 >= e) {
                    ZPN_LOG(AL_ERROR, "Instance name too long?");
                    continue;
                }
                *w1 = 0;
                pool_name_len = w1 - pool_name;
                instance_name = fields[3];
            }
            if (broker_alt_cloud_support_enabled && instance->alt_cloud) {
                snprintf(domain_name, sizeof(domain_name), "%s.%s", instance_name, instance->alt_cloud);
            } else {
                zpath_instance_to_domain_name(instance, domain_name, sizeof(domain_name));
            }
            domain_name_len = strlen(domain_name);
        }

        dispatcher = zpn_broker_dispatcher_get(instance->gid,
                                               domain_name,
                                               domain_name_len,
                                               test_mode,
                                               pool_name);
        if (!dispatcher) {
            ZPN_LOG(AL_ERROR, "Could not allocate dispatcher for %s", instance_name);
            continue;
        }

        dispatcher->instance_run = instance->run;
        dispatcher->instance_active = instance->active;

        /* If not running, don't even try connect */
        if (!instance->run) {
            continue;
        }

        /* Update and remember lat/lon changes */
        if (dispatcher->lat != instance->latitude) {
            if (pool_name[0] == 'c') {
                updated_custom_lat_lon = 1;
            } else {
                updated_lat_lon = 1;
            }
            dispatcher->lat = instance->latitude;
        }
        if (dispatcher->lon != instance->longitude) {
            if (pool_name[0] == 'c') {
                updated_custom_lat_lon = 1;
            } else {
                updated_lat_lon = 1;
            }
            dispatcher->lon = instance->longitude;
        }
        dispatcher->distance = zpath_circle_distance_double(local_lat, local_lon, dispatcher->lat, dispatcher->lon);

        ZPN_DEBUG_DISPATCHER("%2d: Check found dispatcher %s, lat = %f, lon = %f", (int) i, dispatcher->domain_name, dispatcher->lat, dispatcher->lon);

        /* Look up pool, and allocate pool if not found... */
        pool_set = pool_name[0] == 'c' ? new_custom_pool_set : new_pool_set;
        pool = zhash_table_lookup(pool_set->pools_by_name, pool_name, pool_name_len, NULL);
        if (!pool) {
            if (pool_set->pool_count >= MAX_POOLS) {
                ZPN_LOG(AL_ERROR, "Exceeded max %spools count", pool_name[0] == 'c' ? "custom " : "");
                continue;
            }
            pool = &(pool_set->pools[pool_set->pool_count]);

            pool->pool_name = ZPN_STRDUP(pool_name, pool_name_len);
            res = zhash_table_store(pool_set->pools_by_name,
                                    pool->pool_name,
                                    pool_name_len,
                                    1,
                                    pool);
            if (res) {
                ZPN_LOG(AL_ERROR, "Hash error?");
                continue;
            }

            pool_set->pool_count++;
        }

        /* Add dispatcher to pool.. */
        if (pool->dispatcher_count >= MAX_DISPATCHERS_PER_POOL) {
            ZPN_LOG(AL_ERROR, "Exceeded maximum dispatchers per pool, pool %s", pool_name);
            continue;
        }

        ZPN_LOG(AL_NOTICE, "Adding to pool %s: Dispatcher %s", pool_name, instance_name);

        pool->dispatchers[pool->dispatcher_count] = dispatcher;
        pool->dispatcher_count++;
    }

    /* Okay, now we check if it's different from the current or next
     * pool. If it's different, we return the new pool. */
    comp_pool_set = NULL;
    if (next_pools) {
        comp_pool_set = next_pools;
    } else if (current_pools) {
        comp_pool_set = current_pools;
    }

    if ((my_lat != local_lat) ||
        (my_lon != local_lon)) {
        updated_lat_lon = 1;
        updated_custom_lat_lon = 1;
        my_lat = local_lat;
        my_lon = local_lon;
    }

    pool_set_sort(new_pool_set);
    pool_set_sort(new_custom_pool_set);

    /* Check if we have newer pool data...
     * Note: This checks bot for local system lat/lon change as
     * well as dispatcher lat/lon change */
    if (updated_lat_lon) {
        ZPN_DEBUG_DISPATCHER("Updated lat/lon");
        pool_changed = 1;
    }
    if (updated_custom_lat_lon) {
        ZPN_DEBUG_DISPATCHER("Updated custom lat/lon");
        custom_pool_changed = 1;
    }

    pool_set_compare(comp_pool_set, new_pool_set, &pool_changed, refactor_pool);
    pool_set_compare(custom_pools, new_custom_pool_set, &custom_pool_changed, NULL);

    // Save original logic, if pool count is 0, return NULL pool_set (for whatever reason).
    if (pool_changed && new_pool_set->pool_count > 0) {
        *new_pools = new_pool_set;
    } else {
        pool_set_free(new_pool_set);
    }

    // However, if custom pool count is 0, return empty custom_pool_set, so that we properly cleanup custom pools.
    if (custom_pool_changed) {
        *new_custom_pools = new_custom_pool_set;
    } else {
        pool_set_free(new_custom_pool_set);
    }
}



/*
 * If test_mode is set, then this routine fetches data not from wally but from slightly static configuration
 */
enum zpdp_pool_state zpn_broker_dispatcher_check_pool_change(double local_lat, double local_lon, int test_mode)
{
    static int64_t last_sequence = 0, current_sequence;
    static int last_alt_cloud_feature_status = 0, current_alt_cloud_feature_status;

    struct zpn_broker_dispatcher_pool_set *new_pools = NULL;
    struct zpn_broker_dispatcher_pool_set *tmp_pools = NULL;
    struct zpn_broker_dispatcher_pool_set *new_custom_pools = NULL;
    struct zpn_broker_dispatcher_pool_set *tmp_custom_pools = NULL;
    int refactor_pools;
    enum zpdp_pool_state ret = pool_state_unchanged;

    ZPN_DEBUG_DISPATCHER("Checking for pool change, our_lat = %6.2f, our_lon = %6.2f", local_lat, local_lon);

    current_sequence = zpath_instance_last_update_sequence();
    current_alt_cloud_feature_status = broker_alt_cloud_support_enabled;
    if (test_mode) current_sequence = zpdp_test_current_sequence;

    if (current_sequence != last_sequence ||
        current_alt_cloud_feature_status != last_alt_cloud_feature_status) {

        if (current_sequence != last_sequence) {
            ZPN_LOG(AL_NOTICE, "Dispatcher pools: Instance sequence change from %ld to %ld...", (long) last_sequence, (long) current_sequence);
        } else {
            ZPN_LOG(AL_NOTICE, "Dispatcher pools: alt cloud feature change from %d to %d...", last_alt_cloud_feature_status, current_alt_cloud_feature_status);
        }

        last_sequence = current_sequence;
        last_alt_cloud_feature_status = current_alt_cloud_feature_status;
        refactor_pools = 0;
        gen_pools_from_instances(&new_pools, &new_custom_pools, &refactor_pools, local_lat, local_lon, test_mode);
        if (new_pools) {
            ZPN_LOG(AL_NOTICE, "Dispatcher pools: Created new pool set with %ld pools", (long) new_pools->pool_count);
            if (!current_pools) {
                current_pools = new_pools;
                current_pools_created_s = epoch_s();
                ret = pool_state_new_pool;
                ZPN_LOG(AL_NOTICE, "Dispatcher pools: Installing new pool set as current pool set, immediately. Current epoch = %ld", (long) current_pools_created_s);
            } else if (refactor_pools) {
                /* Refactoring! create or replace... */
                if (!next_pools) {
                    next_pools = new_pools;
                    next_pools_created_s = epoch_s();
                    ret = pool_state_new_pool;
                    ZPN_LOG(AL_NOTICE, "Dispatcher pools: Installing new pool set as next pool set, immediately. Current epoch = %ld", (long) next_pools_created_s);
                } else {
                    tmp_pools = next_pools;
                    next_pools = new_pools;
                    next_pools_created_s = epoch_s();
                    ret = pool_state_simple_update;
                    ZPN_LOG(AL_NOTICE, "Dispatcher pools: Replacing next pool set with new pool set, immediately. Current epoch = %ld", (long) next_pools_created_s);
                    pool_set_free_slow(tmp_pools);
                }
            } else {
                /* Not refactoring! replace only!... */
                if (!next_pools) {
                    tmp_pools = current_pools;
                    current_pools = new_pools;
                    ret = pool_state_simple_update;
                    pool_set_free_slow(tmp_pools);
                    ZPN_LOG(AL_NOTICE, "Dispatcher pools: Replaced current pool with updated pool; not enough change to refactor");
                } else {
                    tmp_pools = next_pools;
                    next_pools = new_pools;
                    next_pools_created_s = epoch_s();
                    ret = pool_state_simple_update;
                    ZPN_LOG(AL_NOTICE, "Dispatcher pools: Replacing next pool set with updated pool");
                    pool_set_free_slow(tmp_pools);
                }
            }
        } else {
            ZPN_LOG(AL_NOTICE, "Dispatcher pools: No new pool set, despite sequence change");
        }

        if (new_custom_pools) {
            tmp_custom_pools = custom_pools;
            custom_pools = new_custom_pools;
            pool_set_free_slow(tmp_custom_pools);
            ZPN_LOG(AL_NOTICE, "Dispatcher pools: Installing new custom pool set. Current epoch = %ld", (long) epoch_s());
        } else {
            ZPN_LOG(AL_NOTICE, "Dispatcher pools: No new custom pool set, despite sequence change");
        }
    }

    if (current_pools && next_pools) {
        int64_t now = epoch_s();
        if (NEXT_POOL_PHASE(now) > 4) {
            tmp_pools = current_pools;
            current_pools = next_pools;
            ret = pool_state_complete;
            next_pools = NULL;
            ZPN_LOG(AL_NOTICE, "Dispatcher pools: Replacing current pool set with next pool set, immediately. Current epoch = %ld", (long) now);
            pool_set_free_slow(tmp_pools);
            current_pools_created_s = next_pools_created_s;
        }
    }
    if (current_pools) {
        size_t i;
        ZPN_DEBUG_DISPATCHER("Current_pools exists, has %ld pools", (long)current_pools->pool_count);
        for (i = 0; i < current_pools->pool_count; i++) {
            char dump[2000];
            char *s = dump;
            char *e = s + sizeof(dump);
            size_t j;
            s += sxprintf(s, e, "  Current Pool %zu: %s:", i, current_pools->pools[i].pool_name);
            for (j = 0; j < current_pools->pools[i].dispatcher_count; j++) {
                s += sxprintf(s, e, " %s", current_pools->pools[i].dispatchers[j]->domain_name);
            }
            ZPN_DEBUG_DISPATCHER("%s", dump);
        }
    }
    if (next_pools) {
        size_t i;
        ZPN_DEBUG_DISPATCHER("Next_pools exists, has %ld pools", (long)next_pools->pool_count);
        for (i = 0; i < next_pools->pool_count; i++) {
            char dump[2000];
            char *s = dump;
            char *e = s + sizeof(dump);
            size_t j;
            s += sxprintf(s, e, "  Current Pool %zu: %s:", i, next_pools->pools[i].pool_name);
            for (j = 0; j < next_pools->pools[i].dispatcher_count; j++) {
                s += sxprintf(s, e, " %s", next_pools->pools[i].dispatchers[j]->domain_name);
            }
            ZPN_DEBUG_DISPATCHER("%s", dump);
        }
    }
    if (!current_pools && !next_pools) {
        ret = pool_state_no_pools;
        ZPN_LOG(AL_ERROR, "No dispatcher pools");
    }
    if (custom_pools) {
        int i;
        ZPN_DEBUG_DISPATCHER("Custom_pools exists, has %ld pools", (long)custom_pools->pool_count);
        for (i = 0; i < custom_pools->pool_count; i++) {
            char dump[2000];
            char *s = dump;
            char *e = s + sizeof(dump);
            int j;
            s += sxprintf(s, e, "  Custom Pool %d: %s:", i, custom_pools->pools[i].pool_name);
            for (j = 0; j < custom_pools->pools[i].dispatcher_count; j++) {
                s += sxprintf(s, e, " %s", custom_pools->pools[i].dispatchers[j]->domain_name);
            }
            ZPN_DEBUG_DISPATCHER("%s", dump);
        }
    }

    return ret;
}

void zpn_broker_dispatcher_ready_status_check(struct zpn_broker_dispatcher *dispatcher)
{
    int res;

    if (!dispatcher->ready && dispatcher->channel_connected_count == dispatcher->channel_count &&
        dispatcher->instance_run && dispatcher->instance_active) {
        int64_t now_s = epoch_s();
        ZPATH_MUTEX_LOCK(&(dispatcher->status_lock), __FILE__, __LINE__);
        if (dispatcher->last_dsp_status_rcvd_s == 0) {
            /* Haven't received dispatcher status so far, keep sending request every 1 min until received. */
            if (now_s - dispatcher->last_dsp_status_sent_s >= DSP_STATUS_REQUEST_RETRY_INTERVAL_S) {
                res = zpn_send_zpn_dispatcher_status(dispatcher->channels[0].f_conn, 0);
                if (res) {
                    ZPN_LOG(AL_ERROR, "%s: Could not send zpn_dispatcher_status: %s", fohh_description(dispatcher->channels[0].f_conn), zpn_result_string(res));
                } else {
                    ZPN_DEBUG_DISPATCHER("No response rcvd, re-sent zpn_dispatcher_status request to %s", fohh_description(dispatcher->channels[0].f_conn));
                    dispatcher->last_dsp_status_sent_s = epoch_s();
                    __sync_add_and_fetch_8(&(dispatcher->stats.dsp_status_no_resp_retry), 1);
                    if (brk_dsp_stats) {
                        __sync_add_and_fetch_8(&(brk_dsp_stats->dsp_status_no_resp_retry), 1);
                    }
                }
            }
        } else if (dispatcher->is_old_dsp == 0) {
            /* We initially received dsp status as NOT_READY. */
            if (now_s - dispatcher->last_dsp_status_rcvd_s >= DSP_NOT_READY_STATUS_RESPONSE_MAX_WAIT_S &&
                now_s - dispatcher->last_dsp_status_sent_s >= DSP_STATUS_REQUEST_RETRY_INTERVAL_S) {
                res = zpn_send_zpn_dispatcher_status(dispatcher->channels[0].f_conn, 0);
                if (res) {
                    ZPN_LOG(AL_ERROR, "%s: Could not send zpn_dispatcher_status: %s", fohh_description(dispatcher->channels[0].f_conn), zpn_result_string(res));
                } else {
                    ZPN_DEBUG_DISPATCHER("Dsp NOT_READY, last status received %"PRId64" sec ago, re-sent zpn_dispatcher_status request to %s",
                                         (now_s - dispatcher->last_dsp_status_rcvd_s), fohh_description(dispatcher->channels[0].f_conn));
                    dispatcher->last_dsp_status_sent_s = epoch_s();
                    __sync_add_and_fetch_8(&(dispatcher->stats.dsp_status_not_ready_retry), 1);
                    if (brk_dsp_stats) {
                        __sync_add_and_fetch_8(&(brk_dsp_stats->dsp_status_not_ready_retry), 1);
                    }
                }
            }
        } else {
            /* Old dsp - fallback to old logic. */
            if ((now_s - dispatcher->epoch_started) > zpn_dispatcher_hold_time_get()) {
                ZPN_LOG(AL_NOTICE, "%s: Dispatcher was started %"PRId64" sec ago and is now ready", dispatcher->domain_name,
                        (now_s - dispatcher->epoch_started));
                // coverity[LOCK_EVASION]
                dispatcher->ready = 1;
            }

        }
        ZPATH_MUTEX_UNLOCK(&(dispatcher->status_lock), __FILE__, __LINE__);
    }
}

void zpn_broker_dispatcher_ready_status_check_all()
{
    struct zpn_broker_dispatcher *dispatchers[1000] = {};
    size_t dispatcher_count = sizeof(dispatchers) / sizeof(dispatchers[0]);
    size_t i = 0;

    zpn_broker_dispatcher_all(&(dispatchers[0]), &dispatcher_count);
    for (i = 0; i < dispatcher_count; i++) {
        zpn_broker_dispatcher_ready_status_check(dispatchers[i]);
    }
}

void *zpn_broker_dispatcher_watch_thread(struct zthread_info *zthread_arg, void *cookie)
{
    ZPN_LOG(AL_NOTICE, "Dispatcher watch thread starting...");

    while (1) {
        zthread_heartbeat(zthread_arg);
        zpn_broker_dispatcher_check_pool_change(zpath_instance_global_state.current_config->latitude,
                                                zpath_instance_global_state.current_config->longitude,
                                                0);
        zthread_heartbeat(zthread_arg);
        zpn_broker_dispatcher_ready_status_check_all();
        zthread_heartbeat(zthread_arg);
        sleep(1);
    }

    return NULL;
}

void zpn_broker_dispatcher_stats_obj_set(struct zpn_broker_dispatcher_stats *stats)
{
    brk_dsp_stats = stats;
}

static void array_add_dispatcher(struct zpn_broker_dispatcher *dispatcher,
                                 struct zpn_broker_dispatcher **dispatchers,
                                 size_t array_max_size,
                                 size_t *array_size)
{
    size_t i;
    for (i = 0; i < (*array_size); i++) {
        if (dispatchers[i] == dispatcher) return;
    }
    if ((*array_size) == array_max_size) {
        ZPN_LOG(AL_CRITICAL, "Array size exceeded, max = %ld", (long) array_max_size);
        return;
    }
    dispatchers[*array_size] = dispatcher;
    (*array_size)++;
    return;
}

static void array_add_pool(struct zpn_broker_dispatcher_pool *pool,
                           struct zpn_broker_dispatcher **dispatchers,
                           size_t array_max_size,
                           size_t *array_size)
{
    size_t i;

    if (pool) {
        for (i = 0; i < pool->dispatcher_count; i++) {
            array_add_dispatcher(pool->dispatchers[i], dispatchers, array_max_size, array_size);
        }
    }
}

static void array_add_set(struct zpn_broker_dispatcher_pool_set *set,
                          struct zpn_broker_dispatcher **dispatchers,
                          size_t array_max_size,
                          size_t *array_size)
{
    size_t i;

    if (set) {
        for (i = 0; i < set->pool_count; i++) {
            array_add_pool(&(set->pools[i]), dispatchers, array_max_size, array_size);
        }
    }
}

struct zpn_broker_dispatcher_pool *pool_from_set(uint64_t hash, struct zpn_broker_dispatcher_pool_set *set)
{
    if (!set->pool_count) {
        return NULL;
    } else if (set->pool_count == 1) {
        return &(set->pools[0]);
    } else {
        hash %= set->pool_count;
        return &(set->pools[hash]);
    }
    return NULL;
}

static void choose_health_pools(int64_t key,
                                struct zpn_broker_dispatcher_pool **pool1,
                                struct zpn_broker_dispatcher_pool **pool2)
{
    uint64_t hash = CityHash64((const char *)&key, sizeof(key));

    if (!current_pools) {
        ZPN_DEBUG_DISPATCHER("Key=%016lx no current pool", (long) key);
        *pool1 = NULL;
        *pool2 = NULL;
    } else if (!next_pools) {
        ZPN_DEBUG_DISPATCHER("Key=%016lx no next pool", (long) key);
        *pool1 = pool_from_set(hash, current_pools);
        *pool2 = NULL;
    } else {
        int phase = NEXT_POOL_PHASE(epoch_s());
        if (phase < 1) {
            ZPN_DEBUG_DISPATCHER("Key=%016lx phase < 1, so current_pool", (long) key);
            *pool1 = pool_from_set(hash, current_pools);
            *pool2 = NULL;
        } else if (phase > 4) {
            ZPN_DEBUG_DISPATCHER("Key=%016lx phase > 4, so next_pool", (long) key);
            *pool1 = pool_from_set(hash, next_pools);
            *pool2 = NULL;
        } else {
            uint64_t keyset = (hash >> 62) + 1;
            /* Phase 1, 2, 3 or 4. */
            if (keyset >= phase) {
                *pool1 = pool_from_set(hash, current_pools);
            } else {
                *pool1 = NULL;
            }
            if (keyset <= phase) {
                *pool2 = pool_from_set(hash, next_pools);
            } else {
                *pool2 = NULL;
            }
            if (!(*pool1)) {
                *pool1 = *pool2;
                *pool2 = NULL;
            }
        }
        return;
    }
    return;
}

static void choose_best_pool(int64_t key,
                             struct zpn_broker_dispatcher_pool **pool)
{
    struct zpn_broker_dispatcher_pool *pool2;
    choose_health_pools(key, pool, &pool2);
    return;
}

/*
 * Get all current dispatchers. Ignores pools.
 */
int zpn_broker_dispatcher_all(struct zpn_broker_dispatcher **dispatchers, size_t *dispatcher_count)
{
    size_t count = 0;
    array_add_set(current_pools, dispatchers, *dispatcher_count, &count);
    array_add_set(next_pools, dispatchers, *dispatcher_count, &count);
    array_add_set(custom_pools, dispatchers, *dispatcher_count, &count);
    *dispatcher_count = count;
    return ZPN_RESULT_NO_ERROR;
}

static struct zpn_broker_dispatcher_pool *custom_pool_from_gid(int64_t gid)
{
    if (!custom_pools)
        return NULL;

    ZBDP_MUTEX_LOCK(&(custom_pool_lock), __FILE__, __LINE__);
    char *pool_name = zhash_table_lookup(custom_pool_name_from_gid, &gid, sizeof(gid), NULL);
    ZBDP_MUTEX_UNLOCK(&(custom_pool_lock), __FILE__, __LINE__);
    if (!pool_name) {
        return NULL;
    }

    struct zpn_broker_dispatcher_pool *pool = zhash_table_lookup(custom_pools->pools_by_name, pool_name, strlen(pool_name), NULL);

    return pool;
}

/*
 * Get all dispatchers for a pool given 'that which we are hashing'
 */
int zpn_broker_dispatcher_pool_all(int64_t key, struct zpn_broker_dispatcher **dispatchers, size_t *dispatcher_count)
{
    struct zpn_broker_dispatcher_pool *pool1, *pool2;

    size_t max = *dispatcher_count;
    *dispatcher_count = 0;

    if ((pool1 = custom_pool_from_gid(key))) {
        array_add_pool(pool1, dispatchers, max, dispatcher_count);
        return ZPATH_RESULT_NO_ERROR;
    } else {
        pool1 = pool2 = NULL;
        choose_health_pools(key, &pool1, &pool2);
        array_add_pool(pool1, dispatchers, max, dispatcher_count);
        array_add_pool(pool2, dispatchers, max, dispatcher_count);
        return ZPATH_RESULT_NO_ERROR;
    }
}

int zpn_broker_dispatcher_pool_name_match(int64_t key, const char *pool_name) {
    struct zpn_broker_dispatcher_pool *pool1;
    struct zpn_broker_dispatcher_pool *pool2;

    if ((pool1 = custom_pool_from_gid(key))) {
        if (strcmp(pool_name, pool1->pool_name) == 0)
            return 1;
    } else {
        choose_health_pools(key, &pool1, &pool2);
        if ((pool1 && strcmp(pool_name, pool1->pool_name) == 0) || (pool2 && strcmp(pool_name, pool2->pool_name) == 0))
            return 1;
    }
    return 0;
}

int zpn_broker_dispatcher_is_ready(struct zpn_broker_dispatcher *dispatcher) {
    if (dispatcher->channel_connected_count == dispatcher->channel_count && dispatcher->instance_run &&
        dispatcher->instance_active && dispatcher->ready) {
        return 1;
    }

    return 0;
}

int zpn_broker_dispatcher_is_same_dc(struct zpn_broker_dispatcher *dispatcher, int64_t last_disp_id)
{
    struct zpath_instance *instance = zpath_instance_get_no_locking(last_disp_id);
    if (!instance) {
        ZPN_LOG(AL_ERROR, "Could not get zpath_instance for gid = %" PRId64, last_disp_id);
        return 0;
    }

    if (instance->latitude == dispatcher->lat && instance->longitude == dispatcher->lon) {
        return 1;
    }

    return 0;
}

static void zpn_broker_dispatcher_get_ready_within_site(struct zpn_broker_dispatcher_pool *pool,
                                                        size_t *offset,
                                                        struct zpn_broker_dispatcher **dispatchers,
                                                        size_t *dispatchers_count,
                                                        enum zpn_err_brk_req last_disp_error,
                                                        int64_t last_disp_id) {
    double distance = -1;
    size_t count = 0;
    struct zpn_broker_dispatcher *open_circuit_dsp = NULL;

    size_t i;
    for (i = *offset; i < pool->dispatcher_count && count < *dispatchers_count; i++) {
        struct zpn_broker_dispatcher *dispatcher = pool->dispatchers[i];
        if (!zpn_broker_dispatcher_is_ready(dispatcher))
            continue;

        /*
         * Use dispatcher from different DC if last error returned was NO_CONNECTOR_AVAILABLE or INVALID_DOMAIN
         * or if we're re-dispatching because first dispatcher didn't respond within 2 sec.
         */
        if (last_disp_error == zpn_err_brk_req_no_connector_available ||
            last_disp_error == zpn_err_brk_req_invalid_domain ||
            last_disp_error == zpn_err_brk_req_timeout_redispatch_to_diff_dc) {
            if (zpn_broker_dispatcher_is_same_dc(dispatcher, last_disp_id)) {
                continue;
            }

            if (zpn_brk_dsp_circuit_breaker_get_state(dispatcher) != brk_dsp_circuit_breaker_state_close) {
                open_circuit_dsp = dispatcher;
                continue;
            }
        }

        if ((last_disp_error == zpn_err_brk_req_app_in_learn_mode ||
            last_disp_error == zpn_err_brk_req_app_retry ||
            last_disp_error == zpn_err_brk_req_sticky_changed) &&
            (zpn_brk_dsp_circuit_breaker_get_state(dispatcher) == brk_dsp_circuit_breaker_state_open)) {
            open_circuit_dsp = dispatcher;
            continue;
        }

        /* If we're looking for secondary dispatcher for circuit breaker case, only return close circuit dispatcher. */
        if (last_disp_error == zpn_err_brk_req_circuit_breaker_state_open &&
            ((last_disp_id == dispatcher->instance_id) ||
            (zpn_brk_dsp_circuit_breaker_get_state(dispatcher) != brk_dsp_circuit_breaker_state_close))) {
            continue;
        }

        if (count == 0)
            distance = dispatcher->distance;
        else if (fabs(dispatcher->distance - distance) > DISPATCHER_COLOCATION_DISTANCE_THRESHOLD)
            break;

        dispatchers[count++] = dispatcher;
    }

    if (count == 0 && open_circuit_dsp) {
        /* In case of re-dispatch - if no other dispatcher is available, return the open circuit one. */
        dispatchers[count++] = open_circuit_dsp;
    }

    *offset = i;
    *dispatchers_count = count;
}

static uint64_t zpn_broker_dispatcher_get_queue_depth(const struct zpn_broker_dispatcher *dsp) {
    uint64_t sum = 0;
    uint8_t i;
    for (i = 0; i < dsp->channel_count; ++i) {
        sum += fohh_connection_get_queue_depth(dsp->channels[i].f_conn);
    }
    return sum;
}

static uint64_t zpn_broker_dispatcher_get_avg_rtt(const struct zpn_broker_dispatcher *dsp) {
    uint64_t sum = 0;
    uint8_t i;
    for (i = 0; i < dsp->channel_count; ++i) {
        sum += fohh_connection_get_app_rtt_us(dsp->channels[i].f_conn);
    }
    return sum / dsp->channel_count;
}


/*
 * Get closest or next closest connected dispatcher
 */
struct zpn_broker_dispatcher *zpn_broker_dispatcher_best(int64_t key,
                                                         enum zpn_err_brk_req last_disp_error,
                                                         int64_t last_disp_id,
                                                         struct zpn_broker_dispatcher_pool *dsp_pool)
{
    struct zpn_broker_dispatcher_pool *pool = dsp_pool;

    // only used in dev enviroments, when we need specific dispacther
    if( zpn_broker_is_dev_environment() ) {
        struct zpn_broker_dispatcher *sp_disp = get_pinned_dispatcher();
        if( sp_disp != NULL) {
            return sp_disp;
        }
    }
    if (!pool) {
        pool = custom_pool_from_gid(key);
        if (!pool) {
            choose_best_pool(key, &pool);
        }
    }

    if (pool) {
        /* Prefer last used dispatcher in case of ZPN_ERR_BRK_REQ_APP_IN_LEARN_MODE, ZPN_ERR_BRK_REQ_APP_RETRY and ZPN_ERR_BRK_REQ_STICKY_CHANGED. */
        if (last_disp_error == zpn_err_brk_req_app_in_learn_mode ||
            last_disp_error == zpn_err_brk_req_app_retry ||
            last_disp_error == zpn_err_brk_req_sticky_changed) {
            for (size_t i = 0; i < pool->dispatcher_count; i++) {
                struct zpn_broker_dispatcher *dispatcher = pool->dispatchers[i];
                if (dispatcher->instance_id == last_disp_id) {
                    if (zpn_broker_dispatcher_is_ready(dispatcher) &&
                        zpn_brk_dsp_circuit_breaker_get_state(dispatcher) != brk_dsp_circuit_breaker_state_open) {
                        return dispatcher;
                    }
                    break;
                }
            }
        }

        int thread_id = zthread_self()->stack.thread_num;
        uint64_t thread_id_hash = CityHash64((const char *)&thread_id, sizeof(thread_id));

        struct zpn_broker_dispatcher *dispatchers[MAX_DISPATCHERS_PER_POOL];
        size_t dispatchers_count = MAX_DISPATCHERS_PER_POOL;
        size_t offset = 0;
        zpn_broker_dispatcher_get_ready_within_site(pool, &offset, dispatchers, &dispatchers_count, last_disp_error, last_disp_id);
        struct zpn_broker_dispatcher *closest =
                dispatchers_count ? dispatchers[thread_id_hash % dispatchers_count] : NULL;

        dispatchers_count = MAX_DISPATCHERS_PER_POOL;
        zpn_broker_dispatcher_get_ready_within_site(pool, &offset, dispatchers, &dispatchers_count, last_disp_error, last_disp_id);
        struct zpn_broker_dispatcher *next_closest =
                dispatchers_count ? dispatchers[thread_id_hash % dispatchers_count] : NULL;

        // Choose a dispatcher with lesser queue depth among two closest.
        // Add threshold to kind of stick to the closest one for a while.
        if (closest && next_closest) {
            uint64_t closest_depth = zpn_broker_dispatcher_get_queue_depth(closest);
            uint64_t next_closest_depth = zpn_broker_dispatcher_get_queue_depth(next_closest);
            if (closest_depth <= next_closest_depth + BEST_DISPATCHER_SELECTION_QUEUE_DEPTH_THRESHOLD) {
                return closest;
            } else {
                ZPN_LOG(AL_WARNING,
                        "Dispatcher %s (depth=%" PRIu64 ") was selected over closest %s (depth=%" PRIu64 ")",
                        next_closest->domain_name, next_closest_depth, closest->domain_name, closest_depth);
                return next_closest;
            }
        } else if (closest) {
            return closest;
        } else {
            if (last_disp_error == zpn_err_brk_req_no_connector_available ||
                last_disp_error == zpn_err_brk_req_invalid_domain ||
                last_disp_error == zpn_err_brk_req_timeout_redispatch_to_diff_dc) {
                ZPN_LOG(AL_ERROR, "%ld: No dispatchers ready in pool %s in different DC", (long)key, pool->pool_name);
            } else {
                ZPN_LOG(AL_ERROR, "%ld: No dispatchers ready in pool %s", (long)key, pool->pool_name);
            }
            return NULL;
        }
    } else {
        ZPN_LOG(AL_ERROR, "%ld: No dispatcher pool...?", (long) key);
        return NULL;
    }
}

void zpn_broker_dispatcher_test_set_pools(struct zpn_broker_dispatcher_pool_set *current, struct zpn_broker_dispatcher_pool_set *next)
{
    current_pools = current;
    next_pools = next;
}

void zpn_broker_dispatcher_test_set_created(int64_t when_s)
{
    next_pools_created_s = when_s;
}

static int dump_set(char *s, char *e, struct zpn_broker_dispatcher_pool_set *set, char *name, int64_t when_created_s, int do_json)
{
    size_t i;
    size_t j;
    int64_t now = epoch_s();
    int64_t delta = now - when_created_s;
    char *s_start = s;
    if (do_json) {
        s += sxprintf(s, e, ",\n");
        s += sxprintf(s, e, "\"%s\":{\n", name);
        if (set) {
            s += sxprintf(s, e, "  \"created\":%ld,\n", (long) when_created_s);
            s += sxprintf(s, e, "  \"pools\":[\n");
            for (i = 0; i < set->pool_count; i++) {
                s += sxprintf(s, e,
                              "      {\n"
                              "       \"Dispatchers\":[\n");
                for (j = 0; j < set->pools[i].dispatcher_count; j++) {
                    s += sxprintf(s, e,
                                  "          {\n"
                                  "           \"domain\":\"%s\",\n"
                                  "           \"ready\":\"%s\",\n"
                                  "           \"active\":\"%s\",\n"
                                  "           \"rtt_us\":%ld,\n"
                                  "           \"queue_objs\":%ld,\n"
                                  "           \"channels\":%u,\n"
                                  "           \"distance\":%.0f,\n"
                                  "           \"id\":%"PRId64",\n"
                                  "           \"circuit_breaker_state\":\"%s\"\n"
                                  "          }%s\n",
                                  set->pools[i].dispatchers[j]->domain_name,
                                  (zpn_broker_dispatcher_is_ready(set->pools[i].dispatchers[j])) ? "true" : "false",
                                  set->pools[i].dispatchers[j]->instance_active ? "true" : "false",
                                  (long)zpn_broker_dispatcher_get_avg_rtt(set->pools[i].dispatchers[j]),
                                  (long)zpn_broker_dispatcher_get_queue_depth(set->pools[i].dispatchers[j]),
                                  set->pools[i].dispatchers[j]->channel_connected_count,
                                  set->pools[i].dispatchers[j]->distance,
                                  set->pools[i].dispatchers[j]->instance_id,
                                  zpn_brk_dsp_circuit_breaker_state_get_str(zpn_brk_dsp_circuit_breaker_get_state(set->pools[i].dispatchers[j])),
                                  (j + 1) < set->pools[i].dispatcher_count ? "," : "" );
                }
                s += sxprintf(s, e,
                              "       ]\n"
                              "      }%s\n",
                              (i + 1) < set->pool_count ? ",":"");
            }
            s += sxprintf(s, e, "  ]\n");
        }
        s += sxprintf(s, e, "}");
    } else {
        if (set) {
            s += sxprintf(s, e, "Dumping pools set %s, created %ldm %lds ago:\n", name, (long) delta / 60, (long) delta % 60);
            for (i = 0; i < set->pool_count; i++) {
                s += sxprintf(s, e, "  Pool %zu: %s: Dispatchers, in range order:", i, set->pools[i].pool_name);
                if (!set->pools[i].dispatcher_count) {
                    s += sxprintf(s, e, "NONE!\n");
                } else {
                    s += sxprintf(s, e, "\n");
                    for (j = 0; j < set->pools[i].dispatcher_count; j++) {
                        s += sxprintf(s, e, "   %s (%s, %s, app_rtt=%ldus, objs=%ld, channels=%u, distance=%.0f, id=%"PRId64", circuit_breaker_state=%s)\n",
                                      set->pools[i].dispatchers[j]->domain_name,
                                      (zpn_broker_dispatcher_is_ready(set->pools[i].dispatchers[j])) ? "ready" : "NOT ready",
                                      set->pools[i].dispatchers[j]->instance_active ? "active" : "NOT active",
                                      (long)zpn_broker_dispatcher_get_avg_rtt(set->pools[i].dispatchers[j]),
                                      (long)zpn_broker_dispatcher_get_queue_depth(set->pools[i].dispatchers[j]),
                                      set->pools[i].dispatchers[j]->channel_connected_count,
                                      set->pools[i].dispatchers[j]->distance,
                                      set->pools[i].dispatchers[j]->instance_id,
                                      zpn_brk_dsp_circuit_breaker_state_get_str(zpn_brk_dsp_circuit_breaker_get_state(set->pools[i].dispatchers[j])));
                    }
                }
            }
        } else {
            s += sxprintf(s, e, "Dumping pools set %s: NULL (does not exist)\n", name);
        }
    }
    return s - s_start;
}

void zpn_broker_dispatch_pool_dump_state(char *out_buf, size_t out_buf_size, int do_json)
{
    char *s = out_buf;
    char *e = s + out_buf_size;

    int64_t now = epoch_s();
    struct zpn_broker_dispatcher_pool_set *my_custom_pools = custom_pools; // In case custom pool gets reinstalled under us.

    if (do_json) {
        s += sxprintf(s, e, "{\n");
        s += sxprintf(s, e, "\"last_change\":%ld", (long) next_pools_created_s);
        s += dump_set(s, e, current_pools, "Current Pool Set", current_pools_created_s, 1);
        s += dump_set(s, e, next_pools, "Next Pool Set", next_pools_created_s, 1);
        s += dump_set(s, e, my_custom_pools, "Custom Pool Set", my_custom_pools ? my_custom_pools->creation_epoch_s : 0, 1);
        s += sxprintf(s, e, "}\n");
    } else {
        if (next_pools_created_s) {
            s += sxprintf(s, e, "Last pool change was %ldm %lds ago\n", (long) (now - next_pools_created_s) / 60, (long) (now - next_pools_created_s) % 60);
        } else {
            s += sxprintf(s, e, "No pool change since system started\n");
        }
        if (next_pools) {
            s += sxprintf(s, e, "Current pool phase = %ld\n", (long) NEXT_POOL_PHASE(now));
            int64_t next_phase_begins = ((NEXT_POOL_PHASE(now) + 1) * NEXT_POOL_INTERVAL_S) + next_pools_created_s;
            int64_t next_phase_delta = next_phase_begins - now;
            s += sxprintf(s, e, "Next phase begins in %ldm %lds\n", (long) next_phase_delta / 60, (long) next_phase_delta % 60);
        }
        s += dump_set(s, e, current_pools, "Current Pool Set", current_pools_created_s, 0);
        s += dump_set(s, e, next_pools, "Next Pool Set", next_pools_created_s, 0);
        s += dump_set(s, e, my_custom_pools, "Custom Pool Set", my_custom_pools ? my_custom_pools->creation_epoch_s : 0, 0);
    }

    if (zpn_broker_is_dev_environment()) {
        struct zpn_broker_dispatcher *pinned_disp = get_pinned_dispatcher();

        if (NULL != pinned_disp) {
            s += sxprintf(s, e, "Pinned dispatcher:\n");
            s += sxprintf(s, e, "   %s (%s, %s, app_rtt=%ldus, objs=%ld, channels=%u, distance=%.0f, id=%"PRId64")\n",
                          pinned_disp->domain_name,
                          (zpn_broker_dispatcher_is_ready(pinned_disp)) ? "ready" : "NOT ready",
                          pinned_disp->instance_active ? "active" : "NOT active",
                          (long)zpn_broker_dispatcher_get_avg_rtt(pinned_disp),
                          (long)zpn_broker_dispatcher_get_queue_depth(pinned_disp),
                          pinned_disp->channel_connected_count,
                          pinned_disp->distance,
                          pinned_disp->instance_id);
        }
    }
}

void zpn_broker_dispatch_pool_query(int64_t gid, char *buf, size_t buf_size) {
    char *s = buf;
    char *e = s + buf_size;

    struct zpn_broker_dispatcher_pool *pool = custom_pool_from_gid(gid);
    if (!pool) {
        choose_best_pool(gid, &pool);
    }
    if (!pool) {
        s += sxprintf(s, e, "No pool???\n");
        return;
    }
    s += sxprintf(s, e, "%016lx Uses pool %s:\n", (long)gid, pool->pool_name);

    s += sxprintf(s, e, "Primary dispatchers:\n");
    struct zpn_broker_dispatcher *dispatchers[MAX_DISPATCHERS_PER_POOL] = {};
    size_t dispatchers_count = MAX_DISPATCHERS_PER_POOL;
    size_t offset = 0;
    zpn_broker_dispatcher_get_ready_within_site(pool, &offset, dispatchers, &dispatchers_count, zpn_err_brk_req_no_error, 0);
    size_t i;
    for (i = 0; i < dispatchers_count; ++i) {
        s += sxprintf(s, e, "   %s (%s, app_rtt=%ldus, objs=%ld, channels=%u, distance=%.0f, circuit_breaker_state=%s)\n", dispatchers[i]->domain_name,
                      dispatchers[i]->ready ? "ready" : "NOT ready",
                      (long)zpn_broker_dispatcher_get_avg_rtt(dispatchers[i]),
                      (long)zpn_broker_dispatcher_get_queue_depth(dispatchers[i]),
                      dispatchers[i]->channel_connected_count,
                      dispatchers[i]->distance,
                      zpn_brk_dsp_circuit_breaker_state_get_str(zpn_brk_dsp_circuit_breaker_get_state(dispatchers[i])));
    }

    s += sxprintf(s, e, "Secondary dispatchers:\n");
    dispatchers_count = MAX_DISPATCHERS_PER_POOL;
    zpn_broker_dispatcher_get_ready_within_site(pool, &offset, dispatchers, &dispatchers_count, zpn_err_brk_req_no_error, 0);
    for (i = 0; i < dispatchers_count; ++i) {
        s += sxprintf(s, e, "   %s (%s, app_rtt=%ldus, objs=%ld, channels=%u, distance=%.0f, circuit_breaker_state=%s)\n", dispatchers[i]->domain_name,
                      dispatchers[i]->ready ? "ready" : "NOT ready",
                      (long)zpn_broker_dispatcher_get_avg_rtt(dispatchers[i]),
                      (long)zpn_broker_dispatcher_get_queue_depth(dispatchers[i]),
                      dispatchers[i]->channel_connected_count,
                      dispatchers[i]->distance,
                      zpn_brk_dsp_circuit_breaker_state_get_str(zpn_brk_dsp_circuit_breaker_get_state(dispatchers[i])));
    }
}

void zpn_broker_dispatch_pool_init(void)
{
    static int initialized = 0;
    if (initialized) return;

    ZBDP_MUTEX_LOCK(&(custom_pool_lock), __FILE__, __LINE__);
    if (!initialized) {
        if (!custom_pool_name_from_gid) custom_pool_name_from_gid = zhash_table_alloc(&zpn_allocator);
        initialized = 1;
    }
    ZBDP_MUTEX_UNLOCK(&(custom_pool_lock), __FILE__, __LINE__);
}


int zpn_broker_dispatch_pool_add_custom_customer(int64_t customer_gid, const char *pool_name)
{
    char *name;
    int res;

    /* OMG make me better some day... */
    zpn_broker_dispatch_pool_init();

    ZBDP_MUTEX_LOCK(&(custom_pool_lock), __FILE__, __LINE__);

    name = zhash_table_lookup(custom_pool_name_from_gid, &customer_gid, sizeof(customer_gid), NULL);
    if (name) {
        ZPN_LOG(AL_ERROR, "Duplicate customer gid -> pool map found. %ld -> %s already exists, cannot add %ld -> %s", (long) customer_gid, name, (long) customer_gid, pool_name);
        ZBDP_MUTEX_UNLOCK(&(custom_pool_lock), __FILE__, __LINE__);
        return ZPN_RESULT_ERR;
    }

    name = ZPN_STRDUP(pool_name, strlen(pool_name));
    res = zhash_table_store(custom_pool_name_from_gid, &customer_gid, sizeof(customer_gid), 0, name);
    if (res) {
        ZPN_FREE(name);
        ZPN_LOG(AL_ERROR, "Bad store");
    }
    ZBDP_MUTEX_UNLOCK(&(custom_pool_lock), __FILE__, __LINE__);
    return res;
}


int zpn_broker_dispatch_pool_remove_custom_customer(int64_t customer_gid)
{
    char *name;

    /* OMG make me better some day... */
    zpn_broker_dispatch_pool_init();

    ZBDP_MUTEX_LOCK(&(custom_pool_lock), __FILE__, __LINE__);

    name = zhash_table_lookup(custom_pool_name_from_gid, &customer_gid, sizeof(customer_gid), NULL);
    if (name) {
        zhash_table_remove(custom_pool_name_from_gid, &customer_gid, sizeof(customer_gid), NULL);
        ZPN_FREE_SLOW(name);
        ZBDP_MUTEX_UNLOCK(&(custom_pool_lock), __FILE__, __LINE__);
        return ZPN_RESULT_NO_ERROR;
    }
    ZBDP_MUTEX_UNLOCK(&(custom_pool_lock), __FILE__, __LINE__);

    return ZPN_RESULT_NOT_FOUND;
}


/*
 * Given a customer gid, return the corresponding dispatcher pool name.
 */
char *
zpn_broker_dispatch_pool_get_pool_name_from_customer_gid(int64_t   customer_gid)
{
    char*                               pool_name;
    struct zpn_broker_dispatcher_pool*  pool;

    ZBDP_MUTEX_LOCK(&(custom_pool_lock), __FILE__, __LINE__);
    pool_name = zhash_table_lookup(custom_pool_name_from_gid, &customer_gid, sizeof(customer_gid), NULL);
    ZBDP_MUTEX_UNLOCK(&(custom_pool_lock), __FILE__, __LINE__);
    if (pool_name) {
        return pool_name;
    }

    choose_best_pool(customer_gid, &pool);

    if (pool) {
        return pool->pool_name;
    }

    return NULL;
}


struct fohh_connection *zpn_broker_dispatcher_channel_select(struct zpn_broker_dispatcher *dsp) {
    // Do fair round-robin in common case when all channels are connected.
    uint8_t idx = __sync_fetch_and_add_8(&dsp->channel_idx, 1) % dsp->channel_count;
    struct fohh_connection *f_conn = dsp->channels[idx].f_conn;
    if (fohh_get_state(f_conn) == fohh_connection_connected)
        return f_conn;

    // Try to find next alive channel in rare case when some of the channels are down.
    uint8_t i;
    for (i = 0; i < dsp->channel_count - 1; ++i) {
        f_conn = dsp->channels[++idx % dsp->channel_count].f_conn;
        if (fohh_get_state(f_conn) == fohh_connection_connected)
            break;
    }

    // Return whatever f_conn for backward compatibility.
    return f_conn;
}


/*
* pin the dispatcher to be used
*/
void pin_test_dispatcher_name(const char *disp_name) {
    if (disp_name) {
        // this is variable last until the end of broker
        test_dispatcher_name = ZPN_STRDUP(disp_name, strlen(disp_name));
    }
}
/*
* for testing, find specific dispatcher by name across both pools, custom pools not included
* the dispacther must be in the pools
* use curl 127.0.0.1:8000/dispatcher/pool/dump
* to determine the which disaptcher is available
* Should be called in dev enviornment only.
*/
struct zpn_broker_dispatcher *get_pinned_dispatcher()
{

    if( test_dispatcher_name == NULL ) {
        return NULL;
    }

    ZPN_LOG(AL_CRITICAL,"ONLY IN DEV ENVR, Looking for dispatcher: %s ",test_dispatcher_name);

    int pools = 3;
    while (pools > 0) {
        struct zpn_broker_dispatcher_pool_set *set = pools == 3 ? current_pools : next_pools;
        if (!set) {
            return NULL;
        }
        pools--;
        size_t i;
        size_t j;
        for (i = 0; i < set->pool_count; i++) {
            for (j = 0; j < set->pools[i].dispatcher_count; j++) {
                if (0 == strcmp( set->pools[i].dispatchers[j]->domain_name,test_dispatcher_name)) {
                    return set->pools[i].dispatchers[j];
                }
            }
        }
    }
    return NULL;
}

void zpn_brk_dsp_circuit_breaker_test_set() {
    brk_dsp_circuit_breaker_test = 1;
}

void zpn_broker_dispatch_brk_misc_zevent_base_set(struct zevent_base *zbase)
{
    broker_misc_thread_zevent_base = zbase;
}

/* Get the circuit breaker state in string format. */
const char* zpn_brk_dsp_circuit_breaker_state_get_str(enum zpn_brk_dsp_circuit_breaker_state state)
{
    return zpn_brk_dsp_circuit_breaker_state_str[state];
}

/* Reset circuit breaker related stats in zpn_broker_dispatcher_stats to 0. */
void zpn_brk_dsp_circuit_breaker_reset_stats(struct zpn_broker_dispatcher_stats *stats)
{
    stats->ckt_breaker_state_close = 0;
    stats->ckt_breaker_state_partial_open_10 = 0;
    stats->ckt_breaker_state_partial_open_20 = 0;
    stats->ckt_breaker_state_partial_open_40 = 0;
    stats->ckt_breaker_state_partial_open_60 = 0;
    stats->ckt_breaker_state_partial_open_80 = 0;
    stats->ckt_breaker_state_partial_open_90 = 0;
    stats->ckt_breaker_state_open = 0;
    stats->ckt_breaker_fail_both_timeout = 0;
    stats->ckt_breaker_fail_secondary_timeout = 0;
    stats->ckt_breaker_fail_no_secondary_dsp = 0;
}

/* Set circuit breaker state for the given dispatcher. */
void zpn_brk_dsp_circuit_breaker_set_state(struct zpn_broker_dispatcher *dispatcher,
                                           enum zpn_brk_dsp_circuit_breaker_state state)
{
    ZPATH_RWLOCK_WRLOCK(&(dispatcher->brk_dsp_circuit_breaker_state_lock), __FILE__, __LINE__);
    dispatcher->brk_dsp_circuit_breaker_state = state;
    ZPATH_RWLOCK_UNLOCK(&(dispatcher->brk_dsp_circuit_breaker_state_lock), __FILE__, __LINE__);
}

/* Get circuit breaker state for the given dispatcher. */
enum zpn_brk_dsp_circuit_breaker_state zpn_brk_dsp_circuit_breaker_get_state(struct zpn_broker_dispatcher *dispatcher)
{
    enum zpn_brk_dsp_circuit_breaker_state state;

    ZPATH_RWLOCK_RDLOCK(&(dispatcher->brk_dsp_circuit_breaker_state_lock), __FILE__, __LINE__);
    state = dispatcher->brk_dsp_circuit_breaker_state;
    ZPATH_RWLOCK_UNLOCK(&(dispatcher->brk_dsp_circuit_breaker_state_lock), __FILE__, __LINE__);

    return state;
}

/* Get percent open of circuit for the given circuit breaker state. */
int zpn_brk_dsp_circuit_breaker_get_open_percent(enum zpn_brk_dsp_circuit_breaker_state state)
{
    return zpn_brk_dsp_circuit_breaker_open_percent[state];
}

size_t zpn_brk_dsp_circuit_breaker_get_state_cnt()
{
    return (sizeof(zpn_brk_dsp_circuit_breaker_state_str) / sizeof(zpn_brk_dsp_circuit_breaker_state_str[0]));
}

static void zpn_brk_dsp_circuit_breaker_close_all(struct zevent_base *base, void *void_cookie, int64_t int_cookie)
{
    struct zpn_broker_dispatcher *dispatchers[1000] = {};
    size_t dispatcher_count = sizeof(dispatchers) / sizeof(dispatchers[0]);
    enum zpn_brk_dsp_circuit_breaker_state state = brk_dsp_circuit_breaker_state_close;
    size_t i = 0;

    zpn_broker_dispatcher_all(&(dispatchers[0]), &dispatcher_count);
    for (i = 0; i < dispatcher_count; i++) {
        state = zpn_brk_dsp_circuit_breaker_get_state(dispatchers[i]);
        if (state != brk_dsp_circuit_breaker_state_close) {
            ZPN_LOG(AL_NOTICE, "Circuit breaker state transition for %s: %s to %s, reason: feature disabled",
                    dispatchers[i]->domain_name, zpn_brk_dsp_circuit_breaker_state_get_str(state),
                    zpn_brk_dsp_circuit_breaker_state_get_str(brk_dsp_circuit_breaker_state_close));
            zpn_brk_dsp_circuit_breaker_set_state(dispatchers[i], brk_dsp_circuit_breaker_state_close);
        }
    }
}

/*
 * This cb is called by config monitor thread if the hard disable flag for circuit breaker feature is changed.
 * If feature gets hard disabled - close all dispatcher circuit, otherwise do nothing - state change will
 * happen in the next evaluation cycle, if needed.
 */
static void zpn_brk_dsp_circuit_breaker_hard_disable_cb(const int64_t *target_address, int64_t impacted_gid)
{
    static int64_t hard_disabled = DEFAULT_CONFIG_FEATURE_BRK_DSP_CKT_BREAKER_HARD_DISABLE;

    if (!hard_disabled && brk_dsp_circuit_breaker_hard_disabled) {
        /* Got hard disabled now -  close all circuit */
        if (zevent_base_call(broker_misc_thread_zevent_base, zpn_brk_dsp_circuit_breaker_close_all, NULL, 0) != 0) {
            ZPN_LOG(AL_ERROR, "Could not close brk dsp circuit after feature hard disable");
        }
    }

    hard_disabled = brk_dsp_circuit_breaker_hard_disabled;
}

/*
 * This cb is called by config monitor thread if the circuit breaker feature is enabled/disabled.
 * If feature gets disabled - close all dispatcher circuit, otherwise do nothing - state change will
 * happen in the next evaluation cycle, if needed.
 */
static void zpn_brk_dsp_circuit_breaker_enable_cb(const int64_t *target_address, int64_t impacted_gid)
{
    static int64_t enabled = DEFAULT_CONFIG_FEATURE_BRK_DSP_CKT_BREAKER_ENABLE;

    if (enabled && !brk_dsp_circuit_breaker_enabled) {
        /* Got disabled now -  close all circuit */
        if (zevent_base_call(broker_misc_thread_zevent_base, zpn_brk_dsp_circuit_breaker_close_all, NULL, 0) != 0) {
            ZPN_LOG(AL_ERROR, "Could not close brk dsp circuit after feature disable");
        }
    }

    enabled = brk_dsp_circuit_breaker_enabled;
}

void zpn_brk_dsp_circuit_breaker_config_override_monitor_init()
{
    zpath_config_override_monitor_int(CONFIG_FEATURE_BRK_DSP_CKT_BREAKER_HARD_DISABLE,
                                      &brk_dsp_circuit_breaker_hard_disabled,
                                      zpn_brk_dsp_circuit_breaker_hard_disable_cb,
                                      DEFAULT_CONFIG_FEATURE_BRK_DSP_CKT_BREAKER_HARD_DISABLE,
                                      (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                      0);

    zpath_config_override_monitor_int(CONFIG_FEATURE_BRK_DSP_CKT_BREAKER_ENABLE,
                                      &brk_dsp_circuit_breaker_enabled,
                                      zpn_brk_dsp_circuit_breaker_enable_cb,
                                      DEFAULT_CONFIG_FEATURE_BRK_DSP_CKT_BREAKER_ENABLE,
                                      zpath_instance_global_state.current_config->gid,
                                      (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                      0);
}

/* Return 1 if the circuit breaker feature is enabled, otherwise 0. */
int zpn_brk_dsp_circuit_breaker_is_enabled()
{
    if (brk_dsp_circuit_breaker_test) {
        return 1;
    }

    if (brk_dsp_circuit_breaker_hard_disabled) {
        return 0;
    }

    return brk_dsp_circuit_breaker_enabled ? 1 : 0;
}

/*
 * Return 1 if next request needs to be sent to secondary dispatcher because the
 * primary dispatcher circuit is open/partially open, else 0.
 */
int zpn_brk_dsp_circuit_breaker_is_open_for_req(struct zpn_broker_dispatcher *dispatcher)
{
    enum zpn_brk_dsp_circuit_breaker_state state = zpn_brk_dsp_circuit_breaker_get_state(dispatcher);
    uint8_t idx = 0;
    uint8_t open_till_idx = 0;

    if (!zpn_brk_dsp_circuit_breaker_is_enabled() || (state == brk_dsp_circuit_breaker_state_close)) {
        return 0;
    }
    /*
     * If 40% open, then out of 10 requests, first 4 will go to secondary dispatcher,
     * rest will to primary dispatcher.
     */
    open_till_idx = zpn_brk_dsp_circuit_breaker_get_open_percent(state) / 10;
    idx = __sync_fetch_and_add_8(&(dispatcher->request_idx), 1) % 10;
    if (idx < open_till_idx) {
        return 1;
    }

    return 0;
}

/*
 * Return 1 if next DNS request needs to be sent to secondary dispatcher because the
 * primary dispatcher circuit is open/partially open, else 0.
 */
int zpn_brk_dsp_circuit_breaker_is_open_for_dns_req(struct zpn_broker_dispatcher *dispatcher)
{
    enum zpn_brk_dsp_circuit_breaker_state state = zpn_brk_dsp_circuit_breaker_get_state(dispatcher);
    uint8_t idx = 0;
    uint8_t open_till_idx = 0;

    if (!zpn_brk_dsp_circuit_breaker_is_enabled() || (state == brk_dsp_circuit_breaker_state_close)) {
        return 0;
    }
    /*
     * If 40% open, then out of 10 DNS requests, first 4 will go to secondary dispatcher,
     * rest will to primary dispatcher.
     */
    open_till_idx = zpn_brk_dsp_circuit_breaker_get_open_percent(state) / 10;
    idx = __sync_fetch_and_add_8(&(dispatcher->dns_request_idx), 1) % 10;
    if (idx < open_till_idx) {
        return 1;
    }

    return 0;
}

/* Find the best dispatcher to send request considering circuit breaker state. */
struct zpn_broker_dispatcher *zpn_brk_dsp_circuit_breaker_get_best_dsp(int64_t customer_gid,
                                                                       enum zpn_err_brk_req last_disp_error,
                                                                       int64_t last_disp_id,
                                                                       int *is_ckt_breaker)
{
    struct zpn_broker_dispatcher *secondary_dsp = NULL;
    if (is_ckt_breaker) *is_ckt_breaker = 0;
    struct zpn_broker_dispatcher *dispatcher = zpn_broker_dispatcher_best(customer_gid, last_disp_error, last_disp_id, NULL);
    if (!dispatcher) {
        ZPN_LOG(AL_ERROR, "No dispatcher for %"PRId64" customer", customer_gid);
        return NULL;
    }

    if (last_disp_error == zpn_err_brk_req_no_error &&
        zpn_brk_dsp_circuit_breaker_is_open_for_req(dispatcher)) {
        /* This request needs to go to secondary dispatcher as per circuit breaker state */
        secondary_dsp = zpn_broker_dispatcher_best(customer_gid,
                                                   zpn_err_brk_req_circuit_breaker_state_open,
                                                   dispatcher->instance_id,
                                                   NULL);
        if (!secondary_dsp) {
            ZPN_LOG(AL_WARNING, "No secondary dispatcher for circuit breaker for customer %"PRId64", using primary %s",
                    customer_gid, dispatcher->domain_name);
        } else {
            /* Use secondary dispatcher */
            ZPN_DEBUG_DISPATCHER("Selecting %s over primary %s to send request for %"PRId64" customer, because of brk dsp circuit breaker",
                                 secondary_dsp->domain_name, dispatcher->domain_name, customer_gid);
            dispatcher = secondary_dsp;
            if (is_ckt_breaker) *is_ckt_breaker = 1;
        }
    }

    return dispatcher;
}

/* Get the number of dispatchers to split DNS requests when primary is open/partially open. */
int64_t zpn_brk_dsp_circuit_breaker_get_num_dsp_split_dns_req()
{
    int64_t config_value = DEFAULT_CONFIG_FEATURE_BRK_DSP_CKT_BREAKER_NUM_DSP_SPLIT_DNS_REQ;

    if (brk_dsp_circuit_breaker_test) {
        return DEFAULT_CONFIG_FEATURE_BRK_DSP_CKT_BREAKER_NUM_DSP_SPLIT_DNS_REQ;
    }

    config_value = zpath_config_override_get_config_int(CONFIG_FEATURE_BRK_DSP_CKT_BREAKER_NUM_DSP_SPLIT_DNS_REQ,
                                                        &config_value,
                                                        DEFAULT_CONFIG_FEATURE_BRK_DSP_CKT_BREAKER_NUM_DSP_SPLIT_DNS_REQ,
                                                        zpath_instance_global_state.current_config->gid,
                                                        (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                        0);

    return config_value;
}

/* Get the best dispatcher to send DNS request considering circuit breaker state. */
struct zpn_broker_dispatcher *zpn_brk_dsp_circuit_breaker_get_best_dsp_for_dns(int64_t customer_gid, int *is_ckt_breaker)
{
    struct zpn_broker_dispatcher_pool *pool = NULL;
    struct zpn_broker_dispatcher *dispatchers[MAX_DISPATCHERS_PER_POOL] = {};
    struct zpn_broker_dispatcher *primary_dsp = NULL;
    int next_dsp_cnt = 0;
    int64_t num_dsp_split_dns_req = DEFAULT_CONFIG_FEATURE_BRK_DSP_CKT_BREAKER_NUM_DSP_SPLIT_DNS_REQ;
    int64_t idx = 0;

    if (is_ckt_breaker) *is_ckt_breaker = 0;

    primary_dsp = zpn_broker_dispatcher_best(customer_gid, zpn_err_brk_req_no_error, 0, NULL);
    if (!primary_dsp) {
        ZPN_LOG(AL_ERROR, "No dispatcher for %"PRId64" customer to send DNS request", customer_gid);
        return NULL;
    }

    if (zpn_brk_dsp_circuit_breaker_is_open_for_dns_req(primary_dsp)) {
        /* This DNS request needs to go to next dispatcher as per circuit breaker state */
        pool = custom_pool_from_gid(customer_gid);
        if (!pool) {
            choose_best_pool(customer_gid, &pool);
        }

        if (pool) {
            for (size_t i = 0; i < pool->dispatcher_count; i++) {
                if (primary_dsp->instance_id != pool->dispatchers[i]->instance_id &&
                    zpn_broker_dispatcher_is_ready(pool->dispatchers[i]) &&
                    zpn_brk_dsp_circuit_breaker_get_state(pool->dispatchers[i]) == brk_dsp_circuit_breaker_state_close) {
                    dispatchers[next_dsp_cnt++] = pool->dispatchers[i];
                }
            }
            if (next_dsp_cnt > 0) {
                num_dsp_split_dns_req = zpn_brk_dsp_circuit_breaker_get_num_dsp_split_dns_req();
                if (num_dsp_split_dns_req <= 0) {
                    /* Should never happen as config override limit won't allow this, but just to be safe, to prevent divide by 0. */
                    num_dsp_split_dns_req = DEFAULT_CONFIG_FEATURE_BRK_DSP_CKT_BREAKER_NUM_DSP_SPLIT_DNS_REQ;
                }
                if (next_dsp_cnt < num_dsp_split_dns_req) {
                    num_dsp_split_dns_req = next_dsp_cnt;
                }
                idx = __sync_fetch_and_add_8(&(primary_dsp->dns_request_next_dsp_idx), 1) % num_dsp_split_dns_req;
                ZPN_DEBUG_DISPATCHER("Selecting %s over primary %s to send DNS request for %"PRId64" customer, because of brk dsp circuit breaker",
                                     dispatchers[idx]->domain_name, primary_dsp->domain_name, customer_gid);
                if (is_ckt_breaker) *is_ckt_breaker = 1;
                return dispatchers[idx];
            } else {
                ZPN_LOG(AL_WARNING, "No secondary dispatcher to send DNS request for circuit breaker for customer %"PRId64", using primary %s",
                        customer_gid, primary_dsp->domain_name);
            }
        } else {
            ZPN_LOG(AL_ERROR, "No dispatcher pool to select secondary dispatcher to send DNS request for circuit breaker for %"PRId64" customer", customer_gid);
        }
    }

    return primary_dsp;
}

/* Check if the broker is in China DC or non-China DC - based on that return the timeout threshold. */
int64_t zpn_brk_dsp_circuit_breaker_get_timeout_threshold()
{
    int is_china_dc = 0;
    int64_t config_value = 0;
    struct zpath_instance *instance = NULL;

    if (brk_dsp_circuit_breaker_test) {
        return DEFAULT_CONFIG_FEATURE_BRK_DSP_CKT_BREAKER_TIMEOUT_THRESHOLD_PERCENT;
    }

    instance = zpath_instance_get_no_locking(zpath_instance_global_state.current_config->gid);
    if (!instance) {
        ZPN_LOG(AL_ERROR, "Could not get instance for broker id %"PRId64", using regular timeout threshold for brk dsp circuit breaker",
                zpath_instance_global_state.current_config->gid);
    } else {
        if (instance->country_code && strcmp(instance->country_code, "CN") == 0) {
            is_china_dc = 1;
        }
    }

    if (is_china_dc) {
        config_value = zpath_config_override_get_config_int(CONFIG_FEATURE_BRK_DSP_CKT_BREAKER_CHINA_TIMEOUT_THRESHOLD_PERCENT,
                                                            &config_value,
                                                            DEFAULT_CONFIG_FEATURE_BRK_DSP_CKT_BREAKER_CHINA_TIMEOUT_THRESHOLD_PERCENT,
                                                            zpath_instance_global_state.current_config->gid,
                                                            (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                            0);
    } else {
        config_value = zpath_config_override_get_config_int(CONFIG_FEATURE_BRK_DSP_CKT_BREAKER_TIMEOUT_THRESHOLD_PERCENT,
                                                            &config_value,
                                                            DEFAULT_CONFIG_FEATURE_BRK_DSP_CKT_BREAKER_TIMEOUT_THRESHOLD_PERCENT,
                                                            zpath_instance_global_state.current_config->gid,
                                                            (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                            0);
    }

    return config_value;
}

/* Evaluate and change the circuit breaker state for the given primary dispatcher, if needed. */
void zpn_brk_dsp_circuit_breaker_state_machine(struct zpn_broker_dispatcher *dispatcher,
                                               struct zpn_broker_dispatcher_pool *pool,
                                               struct zpn_broker_dispatcher_stats *stats)
{
    int64_t delta_num_req_sent_total_primary = 0;
    int64_t delta_num_req_timeout_primary = 0;
    int64_t delta_num_req_sent_total_secondary = 0;
    int64_t delta_num_req_timeout_secondary = 0;
    int primary_timeout_percent = 0;
    int secondary_timeout_percent = 0;
    int max_secondary_timeout_percent = 0;
    struct zpn_broker_dispatcher *secondary_dsp_with_max_timeout = NULL;
    struct zpn_broker_dispatcher *secondary_dispatchers[MAX_DISPATCHERS_PER_POOL] = {};
    enum zpn_brk_dsp_circuit_breaker_state next_state = brk_dsp_circuit_breaker_state_close;
    size_t idx = 0;
    size_t i = 0;

    /* Reset stats */
    zpn_brk_dsp_circuit_breaker_reset_stats(&(dispatcher->stats));

    if (!zpn_brk_dsp_circuit_breaker_is_enabled()) {
        /* Feature is disabled, just update the stats, circuit will be closed by config monitor thread, if not already. */
        dispatcher->stats.ckt_breaker_state_close = 1;
        stats->ckt_breaker_state_close++;
        return;
    }

    /* Calculate timeout on primary dispatcher */
    delta_num_req_sent_total_primary = __atomic_load_n(&(dispatcher->stats.zpn_brk_req_sent_total), __ATOMIC_RELAXED) -
                                       __atomic_load_n(&(dispatcher->exported_stats.zpn_brk_req_sent_total), __ATOMIC_RELAXED);
    delta_num_req_timeout_primary = (__atomic_load_n(&(dispatcher->stats.zpn_brk_req_redispatch_timeout), __ATOMIC_RELAXED) -
                                     __atomic_load_n(&(dispatcher->exported_stats.zpn_brk_req_redispatch_timeout), __ATOMIC_RELAXED)) +
                                    (__atomic_load_n(&(dispatcher->stats.zpn_brk_req_redispatch_second_disp_timeout), __ATOMIC_RELAXED) -
                                     __atomic_load_n(&(dispatcher->exported_stats.zpn_brk_req_redispatch_second_disp_timeout), __ATOMIC_RELAXED));
    primary_timeout_percent = delta_num_req_sent_total_primary ?
                              ((delta_num_req_timeout_primary * 100) / delta_num_req_sent_total_primary) : 0;

    ZPATH_RWLOCK_WRLOCK(&(dispatcher->brk_dsp_circuit_breaker_state_lock), __FILE__, __LINE__);
    if (dispatcher->brk_dsp_circuit_breaker_state != brk_dsp_circuit_breaker_state_close) {
        /* Find the secondary dispatcher and calculate timeout happening on secondary dispatcher. */
        secondary_dispatchers[idx++] = zpn_broker_dispatcher_best(0,
                                                                  zpn_err_brk_req_circuit_breaker_state_open,
                                                                  dispatcher->instance_id,
                                                                  pool);
        if (!secondary_dispatchers[0]) {
            /* No secondary dispatcher, we can't do circuit breaker, mark primary dispatcher circuit as close. */
            ZPN_LOG(AL_NOTICE, "Circuit breaker state transition for %s: %s to %s, reason: no secondary dispatcher",
                    dispatcher->domain_name, zpn_brk_dsp_circuit_breaker_state_get_str(dispatcher->brk_dsp_circuit_breaker_state),
                    zpn_brk_dsp_circuit_breaker_state_get_str(brk_dsp_circuit_breaker_state_close));
            dispatcher->brk_dsp_circuit_breaker_state = brk_dsp_circuit_breaker_state_close;
            dispatcher->stats.ckt_breaker_fail_no_secondary_dsp = 1;
            dispatcher->stats.ckt_breaker_state_close = 1;
            stats->ckt_breaker_fail_no_secondary_dsp++;
            stats->ckt_breaker_state_close++;
            ZPATH_RWLOCK_UNLOCK(&(dispatcher->brk_dsp_circuit_breaker_state_lock), __FILE__, __LINE__);
            return;
        }
        /* Get all the possible secondary dispatchers in the colocation. */
        for (i = 0; i < pool->dispatcher_count; i++) {
            if (dispatcher->instance_id != pool->dispatchers[i]->instance_id &&
                secondary_dispatchers[0]->instance_id != pool->dispatchers[i]->instance_id &&
                fabs(secondary_dispatchers[0]->distance - pool->dispatchers[i]->distance) <= DISPATCHER_COLOCATION_DISTANCE_THRESHOLD &&
                zpn_broker_dispatcher_is_ready(pool->dispatchers[i]) &&
                zpn_brk_dsp_circuit_breaker_get_state(pool->dispatchers[i]) == brk_dsp_circuit_breaker_state_close) {
                secondary_dispatchers[idx++] = pool->dispatchers[i];
            }
        }
        /* Find the timeout percentage in past 5 min */
        for (i = 0; i < idx; i++) {
            delta_num_req_sent_total_secondary = __atomic_load_n(&(secondary_dispatchers[i]->stats.zpn_brk_req_sent_total), __ATOMIC_RELAXED) -
                                                 __atomic_load_n(&(secondary_dispatchers[i]->exported_stats.zpn_brk_req_sent_total), __ATOMIC_RELAXED);
            delta_num_req_timeout_secondary = (__atomic_load_n(&(secondary_dispatchers[i]->stats.zpn_brk_req_redispatch_timeout), __ATOMIC_RELAXED) -
                                               __atomic_load_n(&(secondary_dispatchers[i]->exported_stats.zpn_brk_req_redispatch_timeout), __ATOMIC_RELAXED)) +
                                              (__atomic_load_n(&(secondary_dispatchers[i]->stats.zpn_brk_req_redispatch_second_disp_timeout), __ATOMIC_RELAXED) -
                                               __atomic_load_n(&(secondary_dispatchers[i]->exported_stats.zpn_brk_req_redispatch_second_disp_timeout), __ATOMIC_RELAXED));
            secondary_timeout_percent = delta_num_req_sent_total_secondary ?
                                        ((delta_num_req_timeout_secondary * 100) / delta_num_req_sent_total_secondary) : 0;
            if (secondary_timeout_percent > max_secondary_timeout_percent) {
                max_secondary_timeout_percent = secondary_timeout_percent;
                secondary_dsp_with_max_timeout = secondary_dispatchers[i];
            }
            if (secondary_timeout_percent > zpn_brk_dsp_circuit_breaker_get_timeout_threshold()) {
                secondary_dispatchers[i]->stats.ckt_breaker_fail_secondary_timeout = 1;
                stats->ckt_breaker_fail_secondary_timeout++;
            }
        }
        if (!secondary_dsp_with_max_timeout) {
            secondary_dsp_with_max_timeout = secondary_dispatchers[0];
        }
    }

    switch(dispatcher->brk_dsp_circuit_breaker_state) {
        case brk_dsp_circuit_breaker_state_close:
            if (primary_timeout_percent > zpn_brk_dsp_circuit_breaker_get_timeout_threshold()) {
                /* timeout > threshold, shift 10% traffic to secondary dispatcher. */
                int secondary_dsp_available = 0;
                for (i = 0; i < pool->dispatcher_count; i++) {
                    if (dispatcher->instance_id != pool->dispatchers[i]->instance_id &&
                        zpn_broker_dispatcher_is_ready(pool->dispatchers[i]) &&
                        zpn_brk_dsp_circuit_breaker_get_state(pool->dispatchers[i]) == brk_dsp_circuit_breaker_state_close) {
                        /* Secondary dispatcher available */
                        secondary_dsp_available = 1;
                        ZPN_LOG(AL_NOTICE, "Circuit breaker state transition for %s: %s to %s, reason: primary is timing out [%d%%]",
                                dispatcher->domain_name, zpn_brk_dsp_circuit_breaker_state_get_str(dispatcher->brk_dsp_circuit_breaker_state),
                                zpn_brk_dsp_circuit_breaker_state_get_str(brk_dsp_circuit_breaker_state_partial_open_10),
                                primary_timeout_percent);
                        dispatcher->brk_dsp_circuit_breaker_state = brk_dsp_circuit_breaker_state_partial_open_10;
                        dispatcher->stats.ckt_breaker_state_partial_open_10 = 1;
                        stats->ckt_breaker_state_partial_open_10++;
                        break;
                    }
                }
                if (!secondary_dsp_available) {
                    ZPN_LOG(AL_ERROR, "Circuit breaker state transition failed for %s: %s to %s, reason: primary is timing out [%d%%] but no secondary",
                            dispatcher->domain_name, zpn_brk_dsp_circuit_breaker_state_get_str(dispatcher->brk_dsp_circuit_breaker_state),
                            zpn_brk_dsp_circuit_breaker_state_get_str(brk_dsp_circuit_breaker_state_partial_open_10),
                            primary_timeout_percent);
                    dispatcher->stats.ckt_breaker_fail_no_secondary_dsp = 1;
                    stats->ckt_breaker_fail_no_secondary_dsp++;
                    dispatcher->stats.ckt_breaker_state_close = 1;
                    stats->ckt_breaker_state_close++;
                }
            } else {
                dispatcher->stats.ckt_breaker_state_close = 1;
                stats->ckt_breaker_state_close++;
            }
            break;
        case brk_dsp_circuit_breaker_state_partial_open_10:
        case brk_dsp_circuit_breaker_state_partial_open_20:
        case brk_dsp_circuit_breaker_state_partial_open_40:
            if (primary_timeout_percent <= zpn_brk_dsp_circuit_breaker_get_timeout_threshold()) {
                /* Primary dispatcher is normal now, let the traffic go to primary dispatcher only. */
                ZPN_LOG(AL_NOTICE, "Circuit breaker state transition for %s: %s to %s, reason: primary is normal now [%d%%]",
                        dispatcher->domain_name, zpn_brk_dsp_circuit_breaker_state_get_str(dispatcher->brk_dsp_circuit_breaker_state),
                        zpn_brk_dsp_circuit_breaker_state_get_str(brk_dsp_circuit_breaker_state_close), primary_timeout_percent);
                dispatcher->brk_dsp_circuit_breaker_state = brk_dsp_circuit_breaker_state_close;
                dispatcher->stats.ckt_breaker_state_close = 1;
                stats->ckt_breaker_state_close++;
                break;
            }
            if (max_secondary_timeout_percent > zpn_brk_dsp_circuit_breaker_get_timeout_threshold()) {
                /* Secondary dispatcher is timing out, move all shifted traffic back to primary dispatcher. */
                ZPN_LOG(AL_NOTICE, "Circuit breaker state transition for %s: %s to %s, reason: secondary %s is timing out [%d%%]",
                        dispatcher->domain_name, zpn_brk_dsp_circuit_breaker_state_get_str(dispatcher->brk_dsp_circuit_breaker_state),
                        zpn_brk_dsp_circuit_breaker_state_get_str(brk_dsp_circuit_breaker_state_close),
                        secondary_dsp_with_max_timeout->domain_name, max_secondary_timeout_percent);
                dispatcher->brk_dsp_circuit_breaker_state = brk_dsp_circuit_breaker_state_close;
                dispatcher->stats.ckt_breaker_state_close = 1;
                dispatcher->stats.ckt_breaker_fail_both_timeout = 1;
                stats->ckt_breaker_state_close++;
                stats->ckt_breaker_fail_both_timeout++;
                break;
            }
            /* Primary dispatcher is still timing out and secondary is normal - move to next state. */
            ZPN_LOG(AL_NOTICE, "Circuit breaker state transition for %s: %s to %s, reason: primary is timing out [%d%%], secondary %s is normal [%d%%]",
                    dispatcher->domain_name, zpn_brk_dsp_circuit_breaker_state_get_str(dispatcher->brk_dsp_circuit_breaker_state),
                    zpn_brk_dsp_circuit_breaker_state_get_str(dispatcher->brk_dsp_circuit_breaker_state + 1), primary_timeout_percent,
                    secondary_dsp_with_max_timeout->domain_name, max_secondary_timeout_percent);
            if (dispatcher->brk_dsp_circuit_breaker_state == brk_dsp_circuit_breaker_state_partial_open_10) {
                dispatcher->stats.ckt_breaker_state_partial_open_20 = 1;
                stats->ckt_breaker_state_partial_open_20++;
            } else if (dispatcher->brk_dsp_circuit_breaker_state == brk_dsp_circuit_breaker_state_partial_open_20) {
                dispatcher->stats.ckt_breaker_state_partial_open_40 = 1;
                stats->ckt_breaker_state_partial_open_40++;
            } else {
                dispatcher->stats.ckt_breaker_state_open = 1;
                stats->ckt_breaker_state_open++;
            }
            dispatcher->brk_dsp_circuit_breaker_state++;
            break;
        case brk_dsp_circuit_breaker_state_open:
            if (max_secondary_timeout_percent > zpn_brk_dsp_circuit_breaker_get_timeout_threshold()) {
                /* Secondary dispatcher is timing out, move all shifted traffic back to primary dispatcher. */
                ZPN_LOG(AL_NOTICE, "Circuit breaker state transition for %s: %s to %s, reason: secondary %s is timing out [%d%%]",
                        dispatcher->domain_name, zpn_brk_dsp_circuit_breaker_state_get_str(dispatcher->brk_dsp_circuit_breaker_state),
                        zpn_brk_dsp_circuit_breaker_state_get_str(brk_dsp_circuit_breaker_state_close), secondary_dsp_with_max_timeout->domain_name,
                        max_secondary_timeout_percent);
                dispatcher->brk_dsp_circuit_breaker_state = brk_dsp_circuit_breaker_state_close;
                dispatcher->stats.ckt_breaker_state_close = 1;
                dispatcher->stats.ckt_breaker_fail_both_timeout = 1;
                stats->ckt_breaker_state_close++;
                stats->ckt_breaker_fail_both_timeout++;
                break;
            }
            /* Secondary is normal with 100% traffic of primary dispatcher. Initiate recovery now for primary dispatcher. */
            ZPN_LOG(AL_NOTICE, "Circuit breaker state transition for %s: %s to %s, reason: secondary %s is normal [%d%%], initiating recovery for primary",
                    dispatcher->domain_name, zpn_brk_dsp_circuit_breaker_state_get_str(dispatcher->brk_dsp_circuit_breaker_state),
                    zpn_brk_dsp_circuit_breaker_state_get_str(brk_dsp_circuit_breaker_state_partial_open_90), secondary_dsp_with_max_timeout->domain_name,
                    max_secondary_timeout_percent);
            dispatcher->brk_dsp_circuit_breaker_state = brk_dsp_circuit_breaker_state_partial_open_90;
            dispatcher->stats.ckt_breaker_state_partial_open_90 = 1;
            stats->ckt_breaker_state_partial_open_90++;
            break;
        case brk_dsp_circuit_breaker_state_partial_open_90:
        case brk_dsp_circuit_breaker_state_partial_open_80:
        case brk_dsp_circuit_breaker_state_partial_open_60:
            if (max_secondary_timeout_percent > zpn_brk_dsp_circuit_breaker_get_timeout_threshold()) {
                /* Secondary dispatcher is timing out, move all shifted traffic back to primary dispatcher. */
                ZPN_LOG(AL_NOTICE, "Circuit breaker state transition for %s: %s to %s, reason: secondary %s is timing out [%d%%]",
                        dispatcher->domain_name, zpn_brk_dsp_circuit_breaker_state_get_str(dispatcher->brk_dsp_circuit_breaker_state),
                        zpn_brk_dsp_circuit_breaker_state_get_str(brk_dsp_circuit_breaker_state_close), secondary_dsp_with_max_timeout->domain_name,
                        max_secondary_timeout_percent);
                dispatcher->brk_dsp_circuit_breaker_state = brk_dsp_circuit_breaker_state_close;
                dispatcher->stats.ckt_breaker_state_close = 1;
                dispatcher->stats.ckt_breaker_fail_both_timeout = 1;
                stats->ckt_breaker_state_close++;
                stats->ckt_breaker_fail_both_timeout++;
                break;
            }
            if (primary_timeout_percent > zpn_brk_dsp_circuit_breaker_get_timeout_threshold()) {
                /* Primary dispatcher is not normal yet, can't proceed with recovery now - keep 100% traffic of primary on secondary dispatcher. */
                ZPN_LOG(AL_NOTICE, "Circuit breaker state transition for %s: %s to %s, reason: primary is timing out [%d%%], secondary %s is normal [%d%%]",
                        dispatcher->domain_name, zpn_brk_dsp_circuit_breaker_state_get_str(dispatcher->brk_dsp_circuit_breaker_state),
                        zpn_brk_dsp_circuit_breaker_state_get_str(brk_dsp_circuit_breaker_state_open), primary_timeout_percent,
                        secondary_dsp_with_max_timeout->domain_name, max_secondary_timeout_percent);
                dispatcher->brk_dsp_circuit_breaker_state = brk_dsp_circuit_breaker_state_open;
                dispatcher->stats.ckt_breaker_state_open = 1;
                stats->ckt_breaker_state_open++;
                break;
            }
            /* Both primary and secondary dispatchers are normal, proceed with the next state of recovery. */
            if (dispatcher->brk_dsp_circuit_breaker_state == brk_dsp_circuit_breaker_state_partial_open_90) {
                next_state = brk_dsp_circuit_breaker_state_partial_open_80;
                dispatcher->stats.ckt_breaker_state_partial_open_80 = 1;
                stats->ckt_breaker_state_partial_open_80++;
            } else if (dispatcher->brk_dsp_circuit_breaker_state == brk_dsp_circuit_breaker_state_partial_open_80) {
                next_state = brk_dsp_circuit_breaker_state_partial_open_60;
                dispatcher->stats.ckt_breaker_state_partial_open_60 = 1;
                stats->ckt_breaker_state_partial_open_60++;
            } else {
                next_state = brk_dsp_circuit_breaker_state_close;
                dispatcher->stats.ckt_breaker_state_close = 1;
                stats->ckt_breaker_state_close++;
            }
            ZPN_LOG(AL_NOTICE, "Circuit breaker state transition for %s: %s to %s, reason: primary [%d%%] and secondary %s are normal [%d%%], recovery for primary",
                    dispatcher->domain_name, zpn_brk_dsp_circuit_breaker_state_get_str(dispatcher->brk_dsp_circuit_breaker_state),
                    zpn_brk_dsp_circuit_breaker_state_get_str(next_state), primary_timeout_percent,
                    secondary_dsp_with_max_timeout->domain_name, max_secondary_timeout_percent);
            dispatcher->brk_dsp_circuit_breaker_state = next_state;
            break;
        default:
            ZPN_LOG(AL_ERROR, "Unrecognized circuit breaker state: %d - implement me!",
                    dispatcher->brk_dsp_circuit_breaker_state);
            break;
    }

    ZPATH_RWLOCK_UNLOCK(&(dispatcher->brk_dsp_circuit_breaker_state_lock), __FILE__, __LINE__);
}

/*
 * 1. Find the current primary dispatcher in the given pool
 * 2. Check the circuit breaker state and change, if needed
 * 3. Set circuit breaker state to close for all other dispatchers in this pool
 * 4. Update the stats
 */
void zpn_brk_dsp_circuit_breaker_pool_check(struct zpn_broker_dispatcher_pool *pool,
                                            struct zpn_broker_dispatcher_stats *stats)
{
    struct zpn_broker_dispatcher *primary_dispatchers[MAX_DISPATCHERS_PER_POOL] = {};
    enum zpn_brk_dsp_circuit_breaker_state state = brk_dsp_circuit_breaker_state_close;
    int is_primary = 0;
    size_t i = 0, j = 0;
    size_t idx = 0;

    /* Run brk dsp circuit breaker state machine on primary dispatchers */
    primary_dispatchers[idx++] = zpn_broker_dispatcher_best(0,
                                                            zpn_err_brk_req_no_error,
                                                            0,
                                                            pool);
    if (!primary_dispatchers[0]) {
        /* No dispatcher in this pool, we can't do circuit breaker. */
        ZPN_LOG(AL_CRITICAL, "No dispatcher ready in %s pool", pool->pool_name);
        return;
    }
    /* Get all the possible primary dispatchers in the colocation. */
    for (i = 0; i < pool->dispatcher_count; i++) {
        if (primary_dispatchers[0]->instance_id != pool->dispatchers[i]->instance_id &&
            fabs(primary_dispatchers[0]->distance - pool->dispatchers[i]->distance) <= DISPATCHER_COLOCATION_DISTANCE_THRESHOLD &&
            zpn_broker_dispatcher_is_ready(pool->dispatchers[i])) {
            primary_dispatchers[idx++] = pool->dispatchers[i];
        }
    }
    for (i = 0; i < idx; i++) {
        zpn_brk_dsp_circuit_breaker_state_machine(primary_dispatchers[i], pool, stats);
    }

    /* Set circuit breaker state to close for all other dispatchers in this pool */
    for (i = 0; i < pool->dispatcher_count; i++) {
        is_primary = 0;
        for (j = 0; j < idx; j++) {
            if (pool->dispatchers[i]->instance_id == primary_dispatchers[j]->instance_id) {
                is_primary = 1;
                break;
            }
        }
        if (is_primary) {
            continue;
        }
        zpn_brk_dsp_circuit_breaker_reset_stats(&(pool->dispatchers[i]->stats));
        state = zpn_brk_dsp_circuit_breaker_get_state(pool->dispatchers[i]);
        if (state != brk_dsp_circuit_breaker_state_close) {
            ZPN_LOG(AL_NOTICE, "Circuit breaker state transition for %s: %s to %s, reason: not primary now",
                    pool->dispatchers[i]->domain_name, zpn_brk_dsp_circuit_breaker_state_get_str(state),
                    zpn_brk_dsp_circuit_breaker_state_get_str(brk_dsp_circuit_breaker_state_close));
            zpn_brk_dsp_circuit_breaker_set_state(pool->dispatchers[i], brk_dsp_circuit_breaker_state_close);
        }
        pool->dispatchers[i]->stats.ckt_breaker_state_close = 1;
        stats->ckt_breaker_state_close++;
    }
}

/*
 * This is called every 5 min, it does the followings -
 * 1. Iterate through each dispatcher pool
 * 2. Select current primary dispatcher
 * 3. Check the circuit breaker state and change, if needed
 * 4. Set circuit breaker state to close for all other dispatchers
 * 5. Update the stats
 */
void zpn_brk_dsp_circuit_breaker_check(struct zpn_broker_dispatcher_stats *stats)
{
    size_t i;

    zpn_brk_dsp_circuit_breaker_reset_stats(stats);

    if (current_pools) {
        for (i = 0; i < current_pools->pool_count; i++) {
            zpn_brk_dsp_circuit_breaker_pool_check(&(current_pools->pools[i]), stats);
        }
    }
    if (next_pools) {
        for (i = 0; i < next_pools->pool_count; i++) {
            /* Only process if this pool is already not processed as part of current_pools */
            if (!current_pools ||
                !zhash_table_lookup(current_pools->pools_by_name, next_pools->pools[i].pool_name, strlen(next_pools->pools[i].pool_name), NULL)) {
                zpn_brk_dsp_circuit_breaker_pool_check(&(next_pools->pools[i]), stats);
            }
        }
    }
    if (custom_pools) {
        for (i = 0; i < custom_pools->pool_count; i++) {
            zpn_brk_dsp_circuit_breaker_pool_check(&(custom_pools->pools[i]), stats);
        }
    }
}
