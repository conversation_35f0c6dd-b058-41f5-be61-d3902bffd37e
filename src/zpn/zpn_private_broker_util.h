#ifndef _ZPN_PRIVATE_BROKER_UTIL_H_
#define _ZPN_PRIVATE_BROKER_UTIL_H_

#include "zpn/zpn_private_broker_private.h"
#include "zpn/zpn_rpc.h"
#include "zpn/zpn_file_fetch.h"
#include "zpath_lib/zpath_geoip.h"
#include "fohh/fohh_private.h"
#include "zpn/zpn_firedrill_site.h"
#include "zpn/zpn_private_broker_site_config.h"

#define ZPN_PRIVATE_BROKER_ALT_CLOUD_CONN_MAX_INIT_TIME_S            60      // 60 seconds
#define ZPN_PRIVATE_BROKER_CONN_MAX_BACKOFF_TIME_S                   (ZPN_PRIVATE_BROKER_ALT_CLOUD_CONN_MAX_INIT_TIME_S >> 1)

enum zpn_pb_firedrill_state {
    ZPN_PRIVATE_BROKER_FIREDRILL_DISABLED = 0,
    ZPN_PRIVATE_BROKER_FIREDRILL_ENABLED,
    ZPN_PRIVATE_BROKER_FIREDRILL_TRANSIT
};

extern char pb_sni_customer_domain_ctl[PRIVATE_BROKER_SNI_DOMAIN_LEN];
extern char pb_sni_customer_domain_cfg[PRIVATE_BROKER_SNI_DOMAIN_LEN];
extern char pb_sni_customer_domain_rcfg[PRIVATE_BROKER_SNI_DOMAIN_LEN];
extern char pb_sni_customer_domain_ovd[PRIVATE_BROKER_SNI_DOMAIN_LEN];
extern char pb_sni_customer_domain_log[PRIVATE_BROKER_SNI_DOMAIN_LEN];
extern char pb_sni_customer_domain_userdb_tmpl[PRIVATE_BROKER_SNI_DOMAIN_LEN];
extern char g_pbroker_runtime_os[FOHH_MAX_NAMELEN];

#define ZPN_DR_MODE_FLAG        "ZPA_DR_MODE"

/* Clear private broker global state */
extern int zpn_private_broker_global_state_clear(void);

/* Get private broker global state */
extern struct zpn_private_broker_global_state* zpn_get_private_broker_global_state(void);
extern int zpn_private_broker_state_get_configured_cpus();
extern int zpn_private_broker_state_get_available_cpus();
extern int64_t zpn_dispatcher_get_avg_eval_time_us();
extern int64_t zpn_dispatcher_get_max_eval_time_us();
extern void zpn_private_broker_state_set_fohh_threads(int threads);
extern uint32_t zpn_private_broker_state_get_fohh_threads();
int zpn_private_broker_state_get_system_mem_usage();
int zpn_private_broker_state_get_process_mem_usage();
void zpn_private_broker_state_set_container_env();
uint32_t zpn_private_broker_state_is_container_env();

#define PBROKER_SITE_CONFIG_RDLOCK() \
    ZPATH_RWLOCK_RDLOCK(&(zpn_get_private_broker_global_state()->site_config_lock), __FILE__, __LINE__)

#define PBROKER_SITE_CONFIG_WRLOCK() \
    ZPATH_RWLOCK_WRLOCK(&(zpn_get_private_broker_global_state()->site_config_lock), __FILE__, __LINE__)

#define PBROKER_SITE_CONFIG_UNLOCK() \
    ZPATH_RWLOCK_UNLOCK(&(zpn_get_private_broker_global_state()->site_config_lock), __FILE__, __LINE__)

void zpn_private_broker_set_site_gid(int64_t site_gid);
void zpn_private_broker_set_site_gid_with_lock(int64_t site_gid);
int64_t zpn_private_broker_get_site_gid();
int64_t zpn_private_broker_get_site_gid_with_lock();

void zpn_private_broker_set_sitec_preferred(int sitec_preferred);
void zpn_private_broker_set_sitec_preferred_with_lock(int sitec_preferred);
int zpn_private_broker_get_sitec_preferred();
int zpn_private_broker_get_sitec_preferred_with_lock();

int zpn_private_broker_set_offline_domain(const char *offline_domain);
int zpn_private_broker_set_offline_domain_with_lock(const char *offline_domain);
const char* zpn_private_broker_get_offline_domain();
int zpn_private_broker_get_offline_domain_with_lock(char *offline_domain, size_t len);
int zpn_private_broker_has_offline_domain();

void zpn_private_broker_set_reenroll_period(uint64_t reenroll_period);
void zpn_private_broker_set_reenroll_period_with_lock(uint64_t reenroll_period);
uint64_t zpn_private_broker_get_reenroll_period();
uint64_t zpn_private_broker_get_reenroll_period_with_lock();

void zpn_private_broker_set_allow_c2site(int allow_c2site);
void zpn_private_broker_set_allow_c2site_with_lock(int allow_c2site);
int zpn_private_broker_get_allow_c2site();
int zpn_private_broker_get_allow_c2site_with_lock();

void zpn_private_broker_set_max_allowed_downtime_s(int64_t max_allowed_downtime_s);
void zpn_private_broker_set_max_allowed_downtime_s_with_lock(int64_t max_allowed_downtime_s);
int64_t zpn_private_broker_get_max_allowed_downtime_s();
int64_t zpn_private_broker_get_max_allowed_downtime_s_with_lock();

void zpn_private_broker_set_is_switchtime_enabled(int disabled);
void zpn_private_broker_set_is_switchtime_enabled_with_lock(int disabled);
int zpn_private_broker_get_is_switchtime_enabled();
int zpn_private_broker_get_is_switchtime_enabled_with_lock();

void zpn_private_broker_set_max_allowed_switchtime_s(int64_t max_allowed_switchtime_s);
void zpn_private_broker_set_max_allowed_switchtime_s_with_lock(int64_t max_allowed_switchtime_s);
int64_t zpn_private_broker_get_max_allowed_switchtime_s();
int64_t zpn_private_broker_get_max_allowed_switchtime_s_with_lock();

void zpn_private_broker_set_site_is_active(int site_is_active);
void zpn_private_broker_set_site_is_active_with_lock(int site_is_active);
int zpn_private_broker_get_site_is_active();
int zpn_private_broker_get_site_is_active_with_lock();

void zpn_private_broker_global_state_dump(void);

int zpn_private_broker_get_firedrill_state();

int64_t zpn_private_broker_get_firedrill_interval();

int64_t zpn_private_broker_get_firedrill_starttime();

void zpn_private_broker_set_firedrill_state(int64_t firedrill_status);

void zpn_private_broker_set_firedrill_interval(int64_t firedrill_interval);

void zpn_private_broker_set_firedrill_starttime(int64_t firedrill_starttime);
typedef void (zpn_dr_wally_init_f)(char *hostname,
                                    struct wally *dr_wally,
                                    struct wally_test_origin **dr_test_origin,
                                    struct wally_origin **dr_origin
                                    );

struct zpn_private_broker_state*
zpn_private_broker_wally_init(int64_t     private_broker_id,
                              int         shard_index,
                              const char *cloud_name,
                              const char *broker,
                              const char *private_broker_name,
                              const char *cert_name,
                              int         auto_upgrade_disabled,
                              int         stats_log_to_disk,
                              int         use_sqlt, int dr_mode_enabled,
                              zpn_dr_wally_init_f *zpn_dr_wally_init,
                              const char *default_cloud_name);

int
zpn_private_broker_static_wally_init(struct zpn_private_broker_state* private_broker_state,
                                     int         shard_index,
                                     const char *cloud_name,
                                     const char *broker,
                                     int         use_sqlt, int dr_mode_enabled,
                                     const char *default_cloud_name);
int
zpn_private_broker_wally_override_init(struct zpn_private_broker_state* pb_state,
                                       const char *cloud_name,
                                       const char *broker,
                                       int64_t pbroker_gid,
                                       int use_sqlt,
                                       int drmode,
                                       zpn_dr_wally_init_f *zpn_dr_wally_init,
                                       const char *default_cloud_name);


int
zpn_private_broker_conn_sni_init(void);

void
zpn_private_broker_client_connection_monitor_cfg_ovd_init(int64_t     private_broker_id,
                                                          int64_t     private_broker_group_id,
                                                          int64_t     customer_id);

int64_t is_zpn_private_broker_client_connection_monitor_enabled();

int64_t zpn_private_broker_get_client_connection_monitor_interval();

int zpn_private_broker_init_self_to_broker_ssl_ctx(char *f_cloud, char *f_cert, char *f_key_pem);

int zpn_private_broker_init_self_to_private_ssl_ctx(char *f_cloud, char *f_cert, char *f_key_pem);

SSL_CTX*
zpn_private_broker_get_self_to_broker_ssl_ctx(void);

SSL_CTX*
zpn_private_broker_get_self_to_broker_dtls_ctx(void);


int64_t zpn_private_broker_get_cloud_time_delta_us(void);

void zpn_private_broker_set_cloud_time_delta_us(int64_t cloud_time_delta);

int64_t zpn_private_broker_cloud_adjusted_epoch_us(void);

int64_t zpn_cloud_adjusted_epoch_us(void);

int64_t zpn_private_broker_cloud_adjusted_epoch_s(void);

int64_t zpn_cloud_adjusted_epoch_s(void);

int pbroker_get_num_hw_id_changed(void);

int64_t* pbroker_get_hw_id_changed_time_us(void);

/*
 * 1 if the broker_control is connected, 0 otherwise
 */
int pbroker_broker_control_is_connected(void);

char *pbroker_get_name_by_id(const int64_t *private_broker_id);

char* pbroker_get_configured_name(void);

void pbroker_set_hw_id_changed_and_log_time_us(int64_t id_changed_time_us, int num_of_hw_id_changed);

void pbroker_init_cert_validity_counters(const char *cert_file_name);

void set_is_pbroker_dev_environment(void);

int private_broker_is_dev_environment(void);

char* pb_get_override_server_cn();

int zdtls_enabled_on_pbroker(int64_t pbroker_gid, int64_t pbroker_grp_gid);

char* pbroker_state_get_uptime_str(char *buf, int buf_len);

char *get_pbroker_stats_conn_sni();

int is_pbroker_running_drmode();

int is_pbroker_group_marked_to_support_dr();

int is_pbroker_dr_flag_exist();

void pbroker_set_swap_config(uint64_t swap);
int pbroker_get_swap_config();

void fill_dr_running_mode_for_pb(char *dr_running_mode);

struct zpath_allocator * zpn_private_broker_get_allocator();

int get_rollback_cnt(void);
int write_to_file_and_update(void);
/*
 * Common routine for PSE, where we allocate an fohh_info struct and send it along with the
 * 'ALT_CLOUD_AWARE' capability string. Used by almost all the fohh connection handlers for
 * the PSE to broker connections.
 */
void zpn_private_broker_send_fohh_info_with_alt_cloud_capability(struct fohh_connection *connection);

/* Retrieve the cloud-name to use to connect to broker/rbroker */
char* pbroker_get_redir_cloud_name(void);

/* Retrieve the cloud-name to use to start the listeners */
char* pbroker_get_listener_cloud(void);

/* Common callback function for handling zpn_broker_redirect request */
int zpn_private_broker_handle_redirect_request(void* argo_cookie_ptr, void* argo_structure_cookie_ptr, struct argo_object* object);

/*
 * To replace current running file as fallback and then reloading
 */
int geoip_replace_and_trigger_reload(char *filename, struct zpn_file_fetch_key *key, struct zcrypt_key local_key);
int ispip_replace_and_trigger_reload(char *filename, struct zpn_file_fetch_key *key, struct zcrypt_key local_key);

int zpn_private_broker_get_dr_stats(int64_t *dr_activation_on_cnt, int64_t *dr_activation_test_cnt,
                                    int64_t *dr_activation_off_cnt, int64_t *dr_activation_err_cnt,
                                    int64_t *dr_activation_req_cnt, int64_t *dr_activation_resp_cnt,
                                    int64_t *dr_activation_no_resp_cnt, int64_t *dr_activation_err_resp_cnt,
                                    int64_t *dr_auto_cfg_dump_cnt, int64_t *dr_auto_cfg_dump_fail_cnt,
                                    int64_t *dr_config_snapshot_dump_cnt, int64_t *dr_config_snapshot_dump_fail_cnt,
                                    int64_t *dr_config_snapshot_current_count);

int pbroker_store_alt_cloud_to_file(char *listener_alt_cloud, char *rdir_atl_cloud);
int pbroker_read_alt_cloud_from_file(char *listener_alt_cloud, char *rdir_atl_cloud);

/* Config override callback function for alt_cloud feature */
void zpn_private_broker_alt_cloud_config_override_monitor_cb(const int64_t *config_value, int64_t impacted_gid);

int zpn_private_broker_userdb_conn_cb(struct fohh_connection *connection,
                                      enum fohh_connection_state state,
                                      void *cookie);
int zpn_pbroker_pse_cfg_override_register(void);

int zpn_private_broker_fohh_connection_sanity_callback_with_lock(struct fohh_connection* connection);

void zpn_private_broker_switch_to_pcc();

void zpn_private_broker_switch_to_cloud();

int zpn_private_broker_firedrill_timer_activate(int64_t firedrill_interval);

void zpn_pbroker_mission_critical_conn_create(struct fohh_thread *thread, void *cookie, int64_t int_cookie);

void zpn_private_broker_mc_timer_cb(evutil_socket_t sock, int16_t flags, void *cookie);
void zpn_pbroker_update_firedrill_config_to_site_config_file(struct zpn_firedrill_config *firedrill_cfg);

void update_global_state_from_site_config(zpn_pbroker_site_config_t *site_config);
#endif
