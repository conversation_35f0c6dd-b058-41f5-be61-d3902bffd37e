/*
 * zpn_broker_dispatch_c2c_client_check.h Copyright (C) 2024 Zscaler Inc. All Rights Reserved
 */

#ifndef __ZPN_BROKER_DISPATCH_C2C_CLIENT_CHECK_H__
#define __ZPN_BROKER_DISPATCH_C2C_CLIENT_CHECK_H__


#define ZPN_BROKER_C2C_ERROR_CACHE_TIME_S               3
#define ZPN_C2C_BYPASS_CACHE_STATS_INTERVAL_US          (300 * 1000000)
#define ZPN_C2C_FQDN_CHECK_KEY_LEN_MAX                  512
#define ZPN_C2C_FQDN_LEN_MAX                            490 // since key len is max 512
#define DATE_TIME_LEN                                   32

#define ZPN_ERR_C2C_CHECK_NOT_FOUND                       "ZPN_ERR_C2C_CHECK_NOT_FOUND"
#define ZPN_ERR_C2C_CHECK_DSP_TIMEOUT                     "ZPN_ERR_C2C_CHECK_DSP_TIMEOUT"
#define ZPN_ERR_C2C_BROKER_DISP_ERROR                     "ZPN_ERR_C2C_BROKER_REQUEST_FAILURE"
#define ZPN_C2C_CHECK_CACHE_KEY_HASH_TO_BUCKET(hash) ((hash >> 32) & (c2c_ldbypass_cache_bucket_count - 1))

#define ZPN_BROKER_C2C_CACHE_ENTRY_STATE_INIT               0
#define ZPN_BROKER_C2C_CACHE_ENTRY_STATE_OUTSTANDING        1
#define ZPN_BROKER_C2C_CACHE_ENTRY_STATE_RETRY_OUTSTANDING  2
#define ZPN_BROKER_C2C_CACHE_ENTRY_STATE_VALID              3
#define ZPN_BROKER_C2C_CACHE_ENTRY_STATE_INVALID            4

#define DUMP_FMT "Domain:%s, is_c2c:%s, created:%s (%"PRId64"), expires:%s (%"PRId64")\n"

/*
 * zpn_c2c_fqdn_bypass_cache_stats - PSE and Broker c2c fqdn cache stats
 */

struct zpn_c2c_fqdn_bypass_cache_stats {                    /* _ARGO: object_definition */
        int64_t         total_cache_request;                /* _ARGO: integer */
        int64_t         cache_hits;                         /* _ARGO: integer */
        int64_t         cache_misses;                       /* _ARGO: integer */
        int64_t         cache_in_use_entries;               /* _ARGO: integer */
        int64_t         cache_max_entries;                  /* _ARGO: integer */
        int16_t         cache_util_factor;                  /* _ARGO: integer */
        int16_t         cache_hit_ratio;                    /* _ARGO: integer */
        int64_t         cache_replacement_count;            /* _ARGO: integer */
        int64_t         cache_deleted;                      /* _ARGO: integer */
        int64_t         cache_refresh_req_count;            /* _ARGO: integer */
        int64_t         eviction_during_set;                /* _ARGO: integer */
        int64_t         eviction_during_get;                /* _ARGO: integer */

        /* Counts of initial timeouts */
        int64_t         init_disp_check_timeout_count;      /* _ARGO: integer */
        int64_t         init_fallback_disp_timeout_count;   /* _ARGO: integer */

        /* Averages over logging interval of cache */
        int64_t         avg_cache_get_time_us;              /* _ARGO: integer */
        int64_t         avg_cache_add_time_us;              /* _ARGO: integer */
        int64_t         avg_cache_del_time_us;              /* _ARGO: integer */

        /* Average over logging interval of cache */
        int64_t         avg_disp_init_check_time_us;        /* _ARGO: integer */

        /* Max over logging interval of cache */
        int64_t         max_cache_get_time_us;              /* _ARGO: integer */
        int64_t         max_cache_add_time_us;              /* _ARGO: integer */
        int64_t         max_cache_del_time_us;              /* _ARGO: integer */

        int64_t         max_disp_check_time_us;             /* _ARGO: integer */

        /* C2C disp check RPC rx and tx counts */
        int64_t         disp_check_req_tx_count;            /* _ARGO: integer */
        int64_t         disp_check_req_rx_count;            /* _ARGO: integer */
        int64_t         disp_check_req_tx_fail_count;       /* _ARGO: integer */
        int64_t         disp_check_req_rx_drop_count;       /* _ARGO: integer */

        /* Dispatcher response status for c2c fqdn check request */
        int64_t         total_c2c_fqdn_resp;                /* _ARGO: integer */
        int64_t         total_app_fqdn_resp;                /* _ARGO: integer */
};

int zpn_broker_c2c_fqdn_check_response_from_dispatcher(struct zpn_broker_dispatcher_c2c_app_check *check);

int
zpn_broker_fqdn_c2c_check_query( int64_t customer_gid,
                                 const char *tunnel_id,
                                 const char *mtunnel_id,
                                 const char *fqdn,
                                 size_t fqdn_len,
                                 int *result,
                                 int64_t *cache_entry_expires_at,
                                 int req_id,
                                 int64_t mtunnel_incarnation);

int zpn_broker_c2c_fqdn_cache_dump(struct zpath_debug_state *request_state,
                                   const char **query_values,
                                   int query_value_count,
                                   void *cookie);

int zpn_broker_c2c_fqdn_cache_expire(struct zpath_debug_state *request_state,
                                     const char **query_values,
                                     int query_value_count,
                                     void *cookie);

int zpn_broker_c2c_fqdn_cache_stats(struct zpath_debug_state *request_state,
                                    const char **query_values,
                                    int query_value_count,
                                    void *cookie);

int zpn_broker_c2c_fqdn_cache_reset_stats(struct zpath_debug_state *request_state,
                                          const char **query_values,
                                          int query_value_count,
                                          void *cookie);

int zpn_broker_c2c_fqdn_cache_refresh(struct zpath_debug_state *request_state,
                                      const char **query_values,
                                      int query_value_count,
                                      void *cookie);

int zpn_broker_c2c_fqdn_cache_flush(struct zpath_debug_state *request_state,
                                    const char **query_values,
                                    int query_value_count,
                                    void *cookie);

int zpn_broker_c2c_fqdn_cache_query(struct zpath_debug_state *request_state,
                                    const char **query_values,
                                    int query_value_count,
                                    void *cookie);

int c2c_ldbypass_phase2_config_flag_status(struct zpath_debug_state *request_state,
                                           const char **query_values,
                                           int query_value_count,
                                           void *cookie);

int
zpn_broker_c2c_fqdn_cache_debug_cmds_init(void);

int is_c2c_ldbypass_local_route_config_enabled(const char *fqdn);

int is_c2c_ldbypass_feature_enabled(int64_t customer_gid);

int
zpn_broker_dispatch_c2c_client_check_init(struct event_base *ev_base);

#endif /* __ZPN_BROKER_DISPATCH_C2C_CLIENT_CHECK_H__ */
