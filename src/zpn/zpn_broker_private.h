/*
 * zpn_broker_private.h.
 *
 * Private data structures, only associated with a broker.
 */

#ifndef _ZPN_BROKER_PRIVATE_H_
#define _ZPN_BROKER_PRIVATE_H_
#include <sys/queue.h>
#include <stdint.h>
#include <pthread.h>

#include "zpath_lib/zpath_geoip.h"
#include "zpath_lib/zpath_match_style.h"

#include "argo/argo.h"
#include "argo/argo_hash.h"

#include "fohh/fohh.h"

#include "zpn/zpn_rpc.h"

#include "zpn/zpn_mconn_fohh_tlv.h"
#include "zpn/zpn_mconn_zrdt_tlv.h"
#include "zpn/zpn_fohh_worker.h"

#include "zpath_misc/zmicro_heap.h"
#include "zpath_misc/zmicro_hash.h"

#include "zpn/zpn_lib.h"

#include "zpn/zpn_broker_common.h"
#include "zpn/zpn_broker.h"
#include "zpn/zpn_broker_client_apps.h"
#include "zpn/zpn_broker_np_client.h"
#include "zpn/zpn_client_tracker.h"
#include "zpn/zpn_scim_user_attribute.h"
#include "zpn/zpn_scim_user_group.h"
#include "zpn/zpn_broker_drain.h"
#include "zpn/zpn_broker_natural.h"

/* Must be a power of 2. Should be sized such that checking for
 * timeouts on all objects within a bucket with a lock held does not
 * result in significant latency penalties. Making this a very large
 * number should not have much impact on performance other than (at
 * very large sizes) caching hit rate, and slower timeout
 * responsiveness
 */
#define ZPN_BROKER_BUCKETS 8192

/* Mtunnel Housekeeping:
 *
 * mtunnel housekeeping runs in a thread independent from all worker
 * threads. It will lock and evaluate one bucket of mtunnels at a
 * time, at some interval.
 *
 * Housekeeping does the following (plus maybe more if this comment
 * doesn't get updated):
 *
 * - potentially ouput to logging the state of the mtunnel (generally
 *   debugging only)
 *
 * - 'touch' zrdt streams. (used for zombie stream detection)
 *
 * - close mtunnels that don't complete setup. (BRK_MT_SETUP_TIMEOUT)
 *
 * - close mtunnels that exceed idle timeout
 *
 * - close mtunnels that are partially closed and don't seem like they
 *   will ever close (see zpn_broker_mtunnel_done)
 *
 * - trigger logging long-lived mtunnels
 *
 * - Test if reauth limit is expired.
 *
 * - Trigger re-evaluation of policy with respect to this mtunnel if
 *   customer configuration or policy changes. (Special- see below)
 *
 * - free reaped mtunnels
 *
 * Housekeeping triggering re-evaluation of an mtunnel (re-test
 * policy) is expensive, and is limited to 10 per interval. If this
 * limit is reached, the housekeeping check stops for that interval,
 * potentially without switching buckets. This means that houskeeping
 * can significantly slow down how frequently it checks mtunnels in an
 * environment where there is a lot of application or policy
 * flux. This impact will cross tenant boundaries. The limiting of
 * re-evaluation is done to keep jitter down, as the re-evaluation is
 * handed off to the worker threads associated with the client.
 *
 * Housekeeping tries to process at least one entire bucket worth of
 * mtunnels every interval. In an environment where there is no
 * re-evaluation required, it will always do this. (Because there is
 * currently no means of 'saving' state between intervals to remember
 * where in the bucket queue the housekeeping stopped). The Number of
 * broker buckets is intended to be large enough that processing
 * 1/ZPN_BROKER_BUCKETS in a single interval is not too expensive.
 *
 * A minimum of one complete bucket is scanned per interval. Multiple
 * buckets will be scanned until we exceed checks_per_interval.
 *
 * Finally, for the case where there are very few mtunnels, a cap has
 * been placed on how many buckets can be evaluated per iteration to
 * keep CPU load down for the monitor thread. (testing 8000 buckets
 * once per millisecond is actually quite a bit of load)
 */
#define MTUNNEL_HOUSEKEEPING_CHECKS_PER_INTERVAL 50
// 50 mtunnels/interval is 50K mtunnels/s max visit rate.
#define MTUNNEL_HOUSEKEEPING_INTERVAL_US 1000
// 10 reevaluations per interval at 1ms intervals is 10K reevaluations per second.
#define MTUNNEL_HOUSEKEEPING_RE_EVALUATE_PER_INTERVAL 10
// 100 buckets/interval means all buckets can be checked in 8192/100 intervals, or 81 intervals, or 81 milliseconds.
#define MTUNNEL_HOUSEKEEPING_MAX_BUCKETS_PER_INTERVAL 100

/*
 * We use upper bits of hash for bucketing, sice lower bits are used
 * for other, more normal hashing within a bucket
 */
#define MTUNNEL_HASH_TO_BUCKET(hash) ((hash >> 32) & (ZPN_BROKER_BUCKETS - 1))

//LIST_HEAD(zpn_broker_mtunnel_head, zpn_broker_mtunnel);
TAILQ_HEAD(zpn_broker_mtunnel_head_tailq, zpn_broker_mtunnel);

TAILQ_HEAD(posture_profile_object_head, posture_profile_object);
struct posture_profile_object {
    struct argo_object *object;
    char *policy_id_str; /* Allocated */
    int64_t gid; /* The GID of the posture profile. Can be 0 if the posture was not found */
    char gid_str[22]; /* The GID as a string, since that is how we will log it */
    TAILQ_ENTRY(posture_profile_object) list;
};

struct session_postures {                 /* _ARGO: object_definition */
    struct argo_object **postures;        /* _ARGO: argo_object */
    int posture_count;                    /* _ARGO: integer, quiet, count: tokens */
};


/* max no. of logging IDs stored in an mtunnel. 5 for now */
#define ZPN_MAX_LOGGING_IDS 5
#define ZPN_MAX_SERVER_GROUP_GIDS 5
#define ZPN_MAX_ASSISTANT_GROUP_GIDS 48
#define ZPN_MAX_LOCS_FOR_RULE 50
#define ZPN_MAX_LOC_GRP_FOR_RULE 10
#define ZPN_MAX_LOC_GRP_TO_LOC 15
#define ZPN_MAX_EXTRANET_LOCS 200

#if 0
/*
 * Mtunnel stats. Designed so they can be aggregated.
 */
struct zpn_broker_mtunnel_stats {
    /* The amount of time that passes between when we receive a
     * request from a client until we receive a tunnel back from the
     * assistant. This value is zero if asssitant setup does not
     * complete. */
    int64_t assistant_setup_us;

    /* The amount of time that passes between when we receive a tunnel
     * back from the assistant and we get the mtunnel set up with the
     * client. This value is zero if client setup does not
     * complete. */
    int64_t client_setup_us;

    /* Bytes transferred from client to assistant. */
    int64_t client_to_assistant_bytes;

    /* Bytes transferred from assistant to client. */
    int64_t assistant_to_client_bytes;

    /* The number of tunnels these stats represent. Yes, this is '1'
     * for individual tunnels. But aggregated stats are much more
     * interesting. */
    int64_t count;
};
#endif


/*
 * Mtunnel state associated with a single lock.
 *
 * This state is multiplexed by the most significant bits of the hashed mtunnel_id.
 */
struct zpn_broker_bucket {
    pthread_mutex_t lock;
    struct argo_hash_table *mtunnel_by_id;
    struct argo_hash_table *f_conn_by_tunnel_id;
    struct zpn_broker_mtunnel_head_tailq mtunnel_list;
    struct zpn_broker_mtunnel_head_tailq reaped_list;
    int64_t reaped_start_us;
    int64_t reaped_max_count;
    int64_t reaped_max_null_count;
};

LIST_HEAD(zpn_broker_client_head, zpn_broker_client_fohh_state);
LIST_HEAD(zpn_broker_client_customer_gid_head, zpn_broker_client_fohh_state);

typedef void (connected_customer_pagination_cookie_cleanup_f)(void *cookie);

struct zpn_broker_client_connected_customer_pagination_state {
    struct zpn_broker_client_fohh_state *next_to_process;
    int64_t last_started_version;
    int update_in_progress;
    int64_t version;
    void *cookie;
    connected_customer_pagination_cookie_cleanup_f *cleanup;
};

void zpn_broker_client_connected_customer_pagination_state_cleanup(struct zpn_broker_client_connected_customer_pagination_state *ps);

/*
 * Track clients for a customer and statistics
 */
struct zpn_broker_connected_customer_clients {
    /* customer gid*/
    int64_t customer_gid;

    /* statistics per client type */
    int64_t all_client_type_stats[zpn_client_type_total_count];

    /* passive connections per client type */
    int64_t all_client_type_passive_stats[zpn_client_type_total_count];

    /* drainable connections count - used for dry_run */
    int64_t drainable_conn_count;

    /* list of connected clients for the customer */
    struct zpn_broker_client_customer_gid_head customer_client_list;

    // Manages the update so that we can paginate and not starve out the broker
    struct zpn_broker_client_connected_customer_pagination_state config_update_state;
    struct zpn_broker_client_connected_customer_pagination_state aggregate_domains_update_state;
};

/*
 * Track connected clients of the broker
 */
struct zpn_broker_connected_clients {
    pthread_mutex_t lock;

    /* Indexed by tunnel id */
    struct argo_hash_table *tun_ids;

    /* Indexed by cname */
    struct argo_hash_table *cnames;

    /* Indexed by c2c ids */
    struct argo_hash_table *client_fqdns;
    struct argo_hash_table *client_fqdns_per_customer;

    /* Indexed by c2c ip range */
    struct zhash_table *ip_ranges;

    /* EAS/ZVPN Control Connections Indexed by ZIA ZVPN */
    pthread_mutex_t zvpn_ctrl_lock;
    struct argo_hash_table *zvpn_ctrl_conns;

    /* EAS/ZVPN Data Connections Indexed by ZIA ZVPN */
    pthread_mutex_t zvpn_data_lock;
    struct argo_hash_table *zvpn_data_conns;
    /* Indexed by np client subnets */
    struct zhash_table *np_subnets;

    /* List of connected clients */
    struct zpn_broker_client_head client_list;
};

/*
 * This is a hash table of customer gid to cstate per thread
 * key : Customer gid
 * value : list of all cstates for that customer gid
 */
struct zpn_broker_cgid_to_cstate_per_thread {
    /* Indexed by customer_gid */
    struct zhash_table *customer_gid[FOHH_MAX_THREADS];
};

/*
 * All the state associated with a broker.
 *
 * Tunnels are divided into buckets to allow multithreaded locking
 * access easier.
 */
struct zpn_broker {
    int thread_count;                     /* Number of workder threads */
    struct zdtls_mux *mux;
    /* Buckets and buckets of mtunnels. */
    struct zpn_broker_bucket buckets[ZPN_BROKER_BUCKETS];
    struct zpn_broker_connected_clients clients;
    struct zpn_broker_cgid_to_cstate_per_thread cgid_to_cstate_per_thread;
};


enum zpn_broker_mtunnel_state {
    zbms_request_received = 0,
    zbms_authenticated,           // authenticated is preparatory for policy...
    zbms_probe_sent,
    zbms_zia_inspection_start,
    zbms_zia_inspection_ready,
    zbms_zia_inspection_bypass, // continue with original tunnel, but resets appconn local owner
    zbms_dispatch_sent,
    zbms_assistant_bound,
    zbms_complete,
    zbms_reaping,
    zbms_free
};


enum zpn_broker_mtunnel_type {
    zbmt_normal = 0,
    zbmt_transit_c2c /* tunnel is used for broker to broker use case. the dynamic cookie is different here */
};

enum zpn_fqdn_to_srv_ip_counter {
     zpn_fqdn_to_srv_ip_counter_success = 0,
     zpn_fqdn_to_srv_ip_counter_reject,
     zpn_fqdn_to_srv_ip_counter_async
};

/*
 * State associated with an mtunnel, with respect to this broker.
 */
struct zpn_broker_mtunnel {
    /* Everyone needs a bucket list */
    TAILQ_ENTRY(zpn_broker_mtunnel) bucket_list;

    /* The mconn state for client + assistant */
    struct zpn_mconn_fohh_tlv client_tlv;
    struct zpn_mconn_fohh_tlv assistant_tlv;

    struct zpn_mconn_zrdt_tlv client_tlv_zrdt;
    struct zpn_mconn_zrdt_tlv assistant_tlv_zrdt;

    enum zpn_tlv_type client_tlv_type;
    enum zpn_tlv_type assistant_tlv_type;

    /* Used for PSE mtunnel stats */
    enum zpn_client_type client_type;

    /* We need to know the fohh_connection here, before it's bound to
     * anything. */
    struct fohh_connection *_client_f_conn;    /* Should not be accessed directly */
    struct fohh_connection *assistant_f_conn;

    struct zrdt_conn *client_z_conn;
    struct zrdt_conn *assistant_z_conn;

    /* Timer event for timers that need to fire in the future... */
    struct event *timer;

    /* Timers to delay the mtunnel_end handling for dtls/zrdt. zrdt streams are truly multiplexed unlike fohh which maintains the
     * ordering of packets across tags in the order it was sent because of TCP's in-order delivery guarantee.
     * Currntly zrdt clients (assistant, zcc etc.) treat tx/rx over streams similar to tx/rx over fohh tags, send mtunnel_end on
     * stream 0 after sending all the data on stream x. Broker can get the mtunnel_end while the zrdt library in a client (zcc) still
     * has queued up data for that mtunnel (on stream x) and still sending it to broker.
     *
     * To avoid packet loss in such cases, we delay the mtunnel_end received from the clients. Though not fully deterministic this
     * should work most of the time.
     */
    struct zpn_mtunnel_end_thread_cb_cookie *mtunnel_end_client;
    struct zpn_mtunnel_end_thread_cb_cookie *mtunnel_end_client_dropdata;
    struct zpn_mtunnel_end_thread_cb_cookie *mtunnel_end_assistant;
    struct zpn_mtunnel_end_thread_cb_cookie *mtunnel_end_assistant_dropdata;

    /* The tags used by client/assistant. They exist here so they can
     * be used as lookup keys in other data structures.
     * int32_t client_tag;
     * int32_t assistant_tag;
     *
     * Moved into log structure
     */

#if 0
    // NOT USED
    /* These are just the indication whether or not the
     * client/assistant mconns are 'bound' */
    int32_t client_bound;
    int32_t assistant_bound;
#endif

    /* Number of times tunnel has been dispatched */
    int32_t dispatch_count;

    /* parameters to help track back to the cstate */
    char *tunnel_id;
    int c_state_fohh_thread_id;

    char *mtunnel_id;
    uint64_t mtunnel_id_hash;

    /* The actual requested app name */
    char *req_app_name;
    enum zpn_mtunnel_type req_app_type;

    /* The app name as from the DB. (Will be different from app_name
     * in the case of wildcards */
    char *db_app_name;

    struct argo_inet client_ip;
    char c_cc[CC_STR_LEN + 1];             /* Need to NULL terminate */
    char c_city[CITY_STR_LEN + 1];         /* Need to NULL terminate */

    /* New fields for QBR */
    char *a_cc;
    char *a_city;
    char *a_sc;
    char *public_cloud;
    char *private_cloud;
    char *region_id;

    enum zpn_broker_mtunnel_state state;

    char *err;

    char o_user_id[ZPN_MAX_USER_LEN];
    char user_id[ZPN_MAX_USER_LEN];

    int64_t assistant_id;
    int64_t assistant_group_id;
    int64_t server_group_id;
    int64_t application_id;
    int64_t application_domain_id;
    char *application_domain;
    int64_t most_specific_application_id;
    int64_t broker_id;
    int64_t fwd_broker_id;
    int64_t dispatcher_id;
    int64_t customer_id;
    int64_t application_server_id;
    int64_t app_group_id;
    int64_t broker_group_id; /* PBroker group id */

    int32_t async_count;
    int32_t client_port;

    unsigned assistant_ack_sent:1;  /* Ack sent to assistant */
    unsigned client_ack_sent:1;  /* Ack sent to client */
    unsigned no_more_logs:1;  /* Whether or not we can send more logs for this mtunnel */
    unsigned promoted_to_public:1;  /* Whether this mtunnel is promoted to public broker */
    unsigned zia_inspection:1; // ZIA/ZPA inspection 1 - inspect via zia, 0, - normal mtunnel
    unsigned zia_term:1; // ZIA/ZPA inspection  - TERM mtunnel i.e mtunnel SME to Connector
    unsigned zia_origin:1; // ZIA/ZPA inspection  - ORIGIN mtunnel i.e mtunnel SME to Connector
    // ZIA/ZPA inspection  - the ZIA inspection was already attemted on this mtunnel.
    // The state machine can reset state ( e.g. learning mode) or this is TERM mtunnel. Do not attempt ZIA inspection if set to 1
    unsigned zia_inspection_attempted:1;
    unsigned zia_sme_request_sent : 1;  // mtunnel request is sent to SME
    unsigned fqdn_to_serv_ip:1; // fqdn to server ip feature is supported or not for this mtunnel
    unsigned is_fqdn_to_serv_ip_policy_check_async:1; // fqdn to server ip policy lookup in-progress (ASYNC)
    int64_t fqdn_to_serv_ip_policy_reval_start_us;

#if USE_INSP_RES
    unsigned zia_results:1; //  are ZIA Results recieved
#endif

    pthread_mutex_t lock;
    int termination_started;

    struct zpn_trans_log log;

    int64_t rule_gid;					/* gid of access rule that was applied on this mtunnel*/
    int64_t reauth_rule_gid;			/* gid of the reauth/idle rule that was applied on this mtunnel*/
    int64_t insp_rule_gid;              /* gid of inspection rule applied for this mtunnel*/
    int64_t insp_profile_gid;           /* gid of inspection profile to be applied */
    int64_t insp_appl_gid;              /* gid of inspection application to be applied */
    int64_t approval_id;                /* id of the just-in-time approval applied to this mtunnel */

    /* The following are used to track if access rules, reauth rules,
     * or applications have changed configuration for this mtunnel
     * during the lifetime of this mtunnel. If they change, then
     * policy/etc can be reevaluated. */
    int64_t access_rule_version;
    int64_t reauth_rule_version;
    int64_t application_version;
    int64_t user_risk_version;
    int64_t aae_conclusion_version;
    int64_t scim_version;
    int64_t access_rule_version_default;
    int64_t reauth_rule_version_default;
    int64_t application_version_default;
    int64_t scope_version;
    int64_t scope_gid;
    int reevaluation_in_progress;

    int64_t logging_ids[ZPN_MAX_LOGGING_IDS];
    int logging_ids_count;

    /* Server group ids are only set if the request comes from an edge
     * connector and can specify server groups allowed for the
     * mtunnel */
    int64_t server_group_gid[ZPN_MAX_SERVER_GROUP_GIDS];
    int server_group_gid_count;
    int64_t assistant_group_gid[ZPN_MAX_ASSISTANT_GROUP_GIDS];
    int assistant_group_gid_count;
    int64_t *extranet_locs;
    int extranet_locs_count;

    uint32_t double_encrypt;            /* Tunnel feature passed from client */
    enum zpn_probe_type zpn_probe_type; /* Tunnel feature passed from client */

    /* Last time a log was generated */
    int64_t log_s;

    int64_t start_us;

    uint16_t ip_protocol;

    int64_t idle_timeout;	/* idle timeout of the reauth policy that matches this mtunnel*/
    int64_t reauth_timeout;	/* reauth timeout of the reauth policy that matches this mtunnel. measured in seconds from SAML validity beginning */
    int64_t jit_timeout;        /* absolute UTC timestamp jit timeout for this mtunnel based on the approval for just-in-time access */

    int64_t incarnation;
    int64_t pse_to_broker_incarnation; /* Upstream public broker conn incarnation at time of mtunnel promotion, only present on promoted mtunnels */

    int64_t policy_start_us;
    int64_t posture_profile_version;
    int64_t sv_posture_profile_version;
    /* This is the starting time of every learn cycle, the one in trans log is the initial learn start time */
    int64_t learn_start_each_us;

    int64_t last_scope_check_us;

    enum zpn_match_style match_style;

    /* How decision is made leading to the path selection */
    uint64_t path_decision;

    int32_t         brk_req_seq_num;

    struct {
        unsigned    donot_send_directly_to_connector:1;
        unsigned    brk_req_using_path_cache_succeeded:1;
        unsigned    pre_learn_brk_req_fwded :1;  // applicable only for pbroker
        unsigned    waf_enabled : 1;
        unsigned    jit_disabled : 1; // indicates JIT approval enabled or disabled
        unsigned    adp_enabled : 1; // indicates AD Protection enabled
        unsigned    auto_app_protect_enabled : 1; // indicates Auto App Protection enabled
        unsigned    specific_app_and_matched_app_different :1;
        unsigned    ptag_enabled : 1; // indicates Protocol Tagging enabled
        unsigned    is_pra_session : 1; // indicates privileged remote access session
        unsigned    timeout_redispatch_to_diff_dc : 1; // Set if we re-dispatch to different DC dispatcher if first dispatcher didn't respond within 2 sec.
        unsigned    response_received_from_disp : 1; // Set if we received any response from dispatcher (error or route info).
        unsigned    c2c_received_brk_req : 1; // Single broker c2c case - whether we received brk req from dispatcher or not.
        unsigned    c2c_regex_match : 1;
        unsigned    is_c2c_bypass_ld : 1;
        unsigned    is_c2c_terminate_access_on_fail : 1;
        unsigned    neg_path_cache_on_brk : 1; // Whether to negative path cache it or not.
        unsigned    second_disp_timeout_stats_updated : 1; // Whether secondary dispatcher timeout stats updated or not.
        unsigned    ast_grp_used_in_cache_key : 1; // Whether Assistant group gid was used to path cache key or not.
    } flag;

    int allow_all_xport;

    int session_termination_on_reauth;
    enum zpn_broker_mtunnel_type zpn_broker_mtunnel_type;
    char *c2c_uid_peer;
    struct argo_inet c2c_priv_ip;
    struct argo_inet c2c_pub_ip;
    char *brk_name; // from zpn_broker_request
    char *mtunnel_id_parent;

    int64_t o_location_id;
    uint64_t ssl_err;
    uint8_t insp_status;
    uint8_t connector_close_to_app;
    int c2c_pb_zrdt; // 1 broker uses zrdt for PB  , 0 fohh

    enum zpn_err_brk_req last_disp_error;
    /*
     * Set if first disp returned NO_CONNECTOR_AVAILABLE or INVALID_DOMAIN or
     * didn't respond within 2 sec and we're re-dispatching brk req to some
     * other disp in different DC.
     */
    int64_t last_dispatcher_id;

    // pointer to ZIA Inspection connection to SME
    const void* sme_conn;
    // ZIA Inspection origin mtunnel id
    char *origin_mtunnel_id;
    int64_t zia_ctrl_start_us; // start of ctrl path for ZIA inspection
    int64_t zia_user_id;
    // ZIA Device ID
    u_int32_t si_device_id;
    // ZIA Device trust level received from ZCC
    u_int16_t si_device_trust;
    // CC/BC parent location
    int64_t o_plocation_id;

    enum zpn_err_brk_req brk_req_redispatch_to_diff_dc_reason;

    // used for ZIA Inspection, provide c_state infomation to policy checks
    // NULL if not ZIA Inspection
    struct zpn_broker_client_fohh_state *origin_c_state;
    int64_t origin_c_state_incarnation;

    /*
     * Number of errors received from dispatchers - useful when we want to know if both dispatchers
     * returned error before closing the mtunnel.
     */
    int num_error_received_from_disp;

    int num_route_info_received_from_disp;
    int64_t redispatch_to_diff_dc_disp_id;
    /* Timer to schedule brk request fire in future for second dispatcher in case of timeout retry. */
    struct event *timeout_retry_disp_learn_mode_timer;
    /* Dispatcher id because of which timer is firing brk request. */
    int64_t timer_disp_id;

    uint32_t *o_dwgs;
    int o_dwgs_count;

    int mconn_track_perf_stats_level;
    /* Error returned by origin broker in zpn_mtunnel_stats for c2c/s2c transit request. */
    int transit_req_ack_error_code;
    int extranet_enabled;
    int64_t zpn_er_id;

    /* Stepup auth */
    int64_t required_stepup_auth_level_gid;
    int64_t current_stepup_auth_level_gid;
    int64_t required_stepup_auth_level_expiry_s;
    int64_t current_stepup_auth_level_expiry_s;
    char *required_stepup_auth_level_name; /* For human friendly ZPN logging purpose */
    char *current_stepup_auth_level_name;  /* For human friendly ZPA logging purpose */
    char *required_stepup_auth_level_id; /* actual auth level id string, to be passed to ZCC*/

    /* The authentication request we received from client. NOTE: This
     * can be one of several different object types!!! */
    struct argo_object *auth_request_x;
};

struct zpn_mtunnel_end_thread_cb_cookie {
    struct zpn_mtunnel_end tunnel_end;
    struct event *timer;
    void *c_state;
    uint64_t c_state_incarnation;
    void *a_state;
    uint64_t a_state_incarnation;
    int defer_count;
};

struct zpn_broker_mtunnel_free_queue_stats {
    int64_t allocations;
    int64_t free_queue_count;
};

struct zpn_broker_mtunnel_free_queue {
    pthread_mutex_t lock;
    struct zpn_broker_mtunnel_head_tailq mt_list;
    struct zpn_broker_mtunnel_free_queue_stats stats;
};

/*
 * Broker receives incoming connections in two forms: Connections from
 * asssitants, and connections from clients.
 *
 * For the time being, we filter these by running multiple services,
 * on multiple ports. Makes it so we don't have to do dynamic SSL
 * configuration yet.
 */

/*
 *
 * State associated with a CLIENT FOHH connection, with respect to
 * this broker.
 *
 * We recognize this connection based on the port at which it arrives.
 *
 * The connection, as it arrives, must be verified against various
 * root certificates. Unfortunately, openssl doesn't really give us
 * the ability to dynamically query root certificates, so we will have
 * to pull them all out and have them available. (Not a big deal,
 * really, I guess...)
 *
 * XXX - Must allow for dynamic certificate root verification, or
 * getting all roots concurrently. (This will probably need to be
 * integrated into base FOHH functionality, I'm guessing)
 */
struct zpn_broker_fohh_state_free_queue_stats {
    int64_t allocations;
    int64_t free_queue_count;
};

TAILQ_HEAD(zpn_broker_client_fohh_state_tailq, zpn_broker_client_fohh_state);
ZTAILQ_HEAD(zpn_broker_client_path_cache_head, zpn_broker_client_path_cache);

/* Data needed for Rate Limiting */
struct rate_limit {

    /* global view per c_state */
    int64_t num_mtunnel_req;                     // Total num of mtunnel request
    int64_t num_mtunnel_req_rate_limited;        // Mtunnel Req rate limited except for new apps
    int64_t num_new_app_req;                     // Num of new app mtunnel requests
    int64_t num_new_app_req_rate_limited;        // New app mtunnel request rate limited

    /* new app mtunnel request rate limiting required data */
    int64_t mt_new_app_req_bucket_token_count;   // Bucket token cnt available
    int64_t mt_new_app_req_bucket_last_token_consumed_sec;
    int64_t mt_new_app_bucket_size;              // Bucket size
    int64_t mt_new_app_req_threshold;            // Threshold

    unsigned no_new_requests:1;

    zpath_mutex_t       lock;
    struct zhash_table* app_container_store;
};

#define HRDID_STR_SIZE (16 + 1)
struct zpn_broker_client_fohh_state {
    /* Standard TLV state must come first in this structure. */
    struct zpn_fohh_tlv tlv_state;
    struct zpn_zrdt_tlv zrdt_tlv_state;
    enum zpn_tlv_type tlv_type;
    int conn_thread_id;    /* thread which handles the connection, needed for stats when conn goes away */

    zpath_mutex_t lock;

    /* What type of client is connected. ZAPP, SLOGGER, etc */
    enum zpn_client_type client_type;

     /* of the form CLIENT_TYPE|id|<client_type>*/
    char client_type_str[ZPN_MAX_CLIENT_TYPE_LEN];

    /* Client connection platform example: iOS, android */
    enum zpn_platform_type platform_type;

    /* How the peer was authenticated. Peer may be something like a
     * private broker, even while the client is ZAPP, for example */
    enum zpn_tunnel_auth auth_type;

    /* 128 bit (16 bytes, 24 bytes base64) random ID for this TLS
     * connection, in base64 */
    uint8_t tunnel_id_bin[ZPN_TUNNEL_ID_BYTES];
    char tunnel_id[ZPN_TUNNEL_ID_BYTES_TEXT + 1];

    /* This is the actual IP address of the system connected to
     * us. The information in the logs may be different depending what
     * type of system is communicating with us. */
    struct argo_inet client_ip;

    /* machine tunnel gid, we need this to track scope */
    int64_t machine_gid;

    int64_t client_id;
    int64_t client_aux_id;
    /* Here should go any state we need to verify the authenticity of
     * the client. For the time being, user_gid is the ID of the
     * authenticated user, and nothing more. */
    char *user_id;  /* Allocated. Exactly matches nameid from SAML
                     * assertion if SAML authentication is used. */
    char *cname;    /* Allocated. */
    char *hw_serial_id; /* Allocated. */

    /* This is the id in zpn_client_table corresponding to the verified cname */
    int64_t cert_id;

    int64_t idp_gid;
    char idp_gid_str[100]; /* To be removed once micro_string_hash is gone */
    /* The authentication request we received from client. NOTE: This
     * can be one of several different object types!!! */
    struct argo_object *auth_request_x;
    int64_t auth_request_id;

    /* We can get the customer GID directly from the certificate used
     * to communicate with us. */
    int64_t customer_gid;

    /* We can get the scope GID directly from
     * the attribute to scope hash table */
    int64_t scope_gid;
    char attr[MAX_ATTR_SIZE]; /* [attr name]|[attr value] */

    /* Client states */
    uint32_t state_dr_on:1,                      // client's dr state on
             state_dr_off:1,                     // client's dr state off
             state_dr_test:1,                    // client's dr state test
             state_ddil_on:1,                    // client's ddil state on
             state_ddil_off:1,                   // client's ddil state off
             state_ddil_test:1,                  // client's ddil state test

    /* Client capabilities */
             capability_mtn:1,                   // Multiple trusted networks
             capability_aca:1,                   // App complete acknowledgement
             capability_prior_info:1,            // Client sends prior info (pub/priv IP and last broker set redirected to)
             capability_no_ip_download:1,        // Client stating not to download IP apps
             capability_no_domain_download:1,    // Client stating not to download domain apps
             capability_c2c:1,                   // Client-to-Client
             capability_latency_probe:1,         // Client broker latency check capability
             capability_alt_cloud_aware:1,       // client is Alt cloud aware
             capability_svcp:1,                  // Server validated certificate posture
             capability_stepup_auth:1,           // StepUp Authentication capability
             capability_zia_inspection:1,        // client supports ZIA Inspection
             capability_ipv6_redirect:1,         // client supports IPv6 redirect
             capability_client_state:1,          // client supports forwarding client state
             capability_np:1,                    // Network Presence
    /* Capabilities stats per c_state */
             capabilities_received:1,        // Set this after capabilities is received

             auth_complete:1,
             auth_failed:1,

             ack_needed:1,

             redir_waiting_auth_complete:1,
             redir_network_change:1,
             redir_alt_cloud_change:1,
             is_user_risk_enabled:1,

    /* Private broker state flags */
             fwd_auth:1,
             fwd_postures:1,
             fwd_trusted_networks:1,

    /* AAE feature flag status */
             is_aae_profile_feature_enabled:1;

    uint32_t is_stale: 1,

    /* No domain download v2 for all new appscaling clients */
             capability_no_domain_download_v2:1,

             spare:30; /* DO NOT FORGET TO DECREMENT THIS WHENEVER YOU ADD A BIT ABOVE! */

    int64_t last_redirect_timestamp_s;     // If sent in the version msg, the redirect_timestamp_s is saved here. Else 0.
    int64_t app_complete_us;
    int64_t app_complete_ack_us;
    int64_t search_suffix_complete_us;
    /* Timer event for delaying sending search domains */
    struct event *shared_domain_timer;

    int64_t app_complete_sent_epoch_us;

    int64_t start_epoch_us;
    int64_t auth_epoch_s;

    int64_t saml_not_before;
    int64_t saml_not_on_or_after;

    int64_t monitor_count;

    /* All the state maintained for downloading apps to client */
    struct zpn_client_app_state *app_state;

    /* All the state maintained for SCIM */
    int scim_enabled;
    int scim_user_attribute_index;
    int64_t scim_user_attribute_gid[ZPN_SCIM_USER_ATTRIBUTE_PER_USER_MAX];
    int scim_user_group_index;
    int64_t scim_user_group_gid[ZPN_SCIM_USER_GROUP_PER_USER_MAX];
    struct zpn_broker_client_scim_state *scim_state;

    /* All the state maintained for user_risk version */
    struct zpn_broker_client_user_risk_state *user_risk_state;

    /* All app stats */
    struct zpn_broker_client_app_stats *app_stats;

    /* Registrant for search domains that a client may be interested
     * in */
    struct wally_registrant *search_domain_registrant;

    /*
     * Hash table containing characteristics of the logged in
     * customer. These are of the form "%s|%s|%s".
     *
     * First string = Info source, which for clients is either "userid"
     * or "saml".
     *
     * Second string = Info name, which is generally a saml attribute
     * name, or "userid" for user ID.
     *
     * Third string = Info value, which is generally a saml value, or
     * the userid (string form)
     *
     * Note: userid generally comes from
     */
    struct zhash_table *general_context_hash;

    int saml_enabled;
    struct zhash_table *saml_hash;

    struct zhash_table *scim_hash;

    /* Maintaining a hash for tracking if SAML expired messages are being sent. */
    struct zhash_table* domain_log_hash;

    /* Queue of posture profiles we have received, since we may/may
     * not clear our authentication state as we receive these... */
    struct posture_profile_object_head posture_profiles;
    size_t posture_profiles_count;

    /* Trusted network object */
    struct argo_object *trusted_networks;

    /* The following strings are the GID string version of trusted
     * networks. It may contain GID (normal) or the UDID of the
     * trusted network (if GID lookup failed */
    char **trusted_networks_gid_str;
    size_t trusted_networks_gid_str_count;

    /* The following strings are the name string version of trusted
       * networks. It may contain name (normal) or null
       * trusted network name (if GID lookup failed */
    char **trusted_networks_gid_name_str;
    size_t trusted_networks_gid_name_str_count;

    /* Auth log related stuff here */

    char c_cc[CC_STR_LEN + 1]; /* Client CC */
    char brk_cc[CC_STR_LEN + 1]; /* Broker CC */
    char c_city[CITY_STR_LEN + 1]; /* Client City Info */
    struct zpn_auth_log log;
    struct zpn_auth_state_log log_state;

    LIST_ENTRY(zpn_broker_client_fohh_state) list_entry;
    LIST_ENTRY(zpn_broker_client_fohh_state) customer_gid_entry;
    int customer_gid_entry_incomplete;

    /* version refers the the zpn_version_object. Only release the object, if releasing... */
    struct argo_object *zpn_version_object;
    struct zpn_version *version;

    /* Version callback stats */
    unsigned version_cb_received:1;        // Set this after version_cb received

    unsigned int version_supports_reauth:1;

    int fwd_to_next_hop;
    int hops_count;
    char *self_hop_type;
    int self_hop;

    char **hop_type;
    int hop_type_count;

    int64_t *brks;
    int brks_count;

    int64_t *brks_grp;
    int brks_grp_count;

    char **tunnel_ids;
    int tunnel_ids_count;

    char **sys_type;
    int sys_type_count;

    char **hop_debug;
    int hop_debug_count;

    TAILQ_ENTRY(zpn_broker_client_fohh_state)  entry;
    int64_t incarnation;

    /*
     * Data needed for private broker to broker forwarding
     */
    enum zpn_tlv_type default_pb_client_tlv_type;
    struct zpn_pb_client *public_broker_client;
    struct zpn_pb_client *public_broker_client_zrdt;

    struct event *conn_follow_timer;
    struct zevent_base *client_thread;


    /* Private broker support client state */

    /* PBroker fwd conn - Not fohh_connection, pbroker_fwd_conn */
    void *fwd_conn_cookie;

    /* peer id passed up from pbroker used in connections that need data about the downstream peer */
    int64_t downstream_peer_id;

    int64_t max_num_mtunnel;

    /* Data needed for Rate Limiting */
    struct {
        /* global view per c_state */
        int64_t num_mtunnel_req;
        int64_t num_mtunnel_req_rate_limited;
        int64_t num_new_app_req;
        int64_t num_new_app_req_rate_limited;

        /* new app request rate limiting required data */
        int64_t mt_new_app_req_bucket_token_count;
        int64_t mt_new_app_req_bucket_last_token_consumed_sec;
        int64_t mt_new_app_bucket_size;
        int64_t mt_new_app_req_threshold;

        unsigned no_new_requests:1;

        zpath_mutex_t       lock;
        struct zhash_table* app_container_store;
    } rate_limit;

    struct {
        struct zpn_broker_client_path_cache_head    list;
        struct zhash_table*                         tbl;
        int64_t                                     current_count;
        int64_t                                     total_so_far_count;
    } path_cache;

    int fqdn_registered; /* c2c FQDN registration state: C2C_FQDN_REGISTERED(1) or C2C_FQDN_NOT_REGISTERED(0) */
    char *client_fqdn;   /* Allocated. Provided by zapp client via authentication message.
                          * This identity is used to for client-to-client functionality.
                          */
    char *endpoint_cname;   /* Allocated. cname of zapp client beyond private broker. Use cname if NULL. */
    int fqdn_register_error;
    int32_t last_used_c2c_tag_id;
    int64_t c2c_resend_interval_count;
    int64_t c2c_expiration_ttl_s;
    uint8_t c2c_fqdn_missing;

    struct {
        struct argo_inet    reserved_ip;
        int64_t             reserved_us;
        int64_t             ip_range_gid;
        char *              location_hint;
        int32_t             status_code;
        int64_t             retry_interval_count;
        int64_t             ipars_conn_incarnation; // Public broker only
        uint16_t            feature_status_incarnation;
        uint8_t             renewal_in_progress:1,
                            spare:7;
    } c2c_ip;

    /* All the state maintained for downloading np apps to client */
    struct zpn_client_np_app_state *np_app_state;
    /* All NP app stats */
    struct zpn_broker_np_client_app_stats *np_app_stats;

    struct {
        int64_t             long_term_gateway_gid;
        int64_t             long_term_subnet_gid;
        struct argo_inet    long_term_ip;
        int64_t             long_term_promoted_us;
        int64_t             ipars_conn_incarnation;
        int16_t             keep_alive_timeout_s;
        int64_t             keep_alive_expires_at;
        int64_t             request_id_for_renew;
        uint16_t            feature_status_incarnation;
        int8_t              policy_action;
        int64_t             policy_version;
        int64_t             posture_profile_version;
        int64_t             scim_version;
    } np;

    int64_t connection_ttl_s; /* connectivty detection ttl in sec */
    int64_t connect_us;
    int64_t posture_update_us;
    int64_t posture_profile_version;

    uint64_t icmp_rate_limit_cntr;
    uint64_t icmp_rate_limit_last_access_s;

    struct {
        int     mon_enable;
        int64_t log_count;
    } pb_conn_mon;

    int64_t cust_part_gid;           /* partition gid of the customer */
    int16_t is_target_part_flag;     /* instance part of the customer partition or not */
    int16_t is_inst_not_part_flag;   /* instance in free pool aka unpartitioned instance */

    char *zia_cloud_name;  // Needed for location lookup.
    int32_t orgid;

    int64_t o_location_id;           // Needed for location during auth for client forwarding policies ET-53427
    int64_t o_sub_location_id;
    int async_count;

    int is_customer_ready_async;

    // hash of hardware_id for c2c, not used for crypto
    char hash_hardware_id[HRDID_STR_SIZE];
    zpn_client_tracker_t tracker;
    /* 0: active(default), 1: passive/probe, other values reserved for future use */
    int curr_connection_mode;
    int is_lp_enabled;

    /* customer drain connections related */
    struct zpn_conn_drain_info      drain_info[drain_type_total_count]; /* one for each drain type */
    enum zpn_conn_drain_type        drain_initiator;    /* what initiated the drain; manual/auto */

    /* Queue of server validated posture profiles, since we may/may
     * not clear our authentication state as we validate these... */
    struct posture_profile_object_head sv_posture_profiles;
    size_t sv_posture_profiles_count;
    int64_t sv_posture_update_us;
    int64_t sv_posture_reval_frequency;
    int64_t sv_posture_profile_version;
    int8_t num_sv_postures_sent;
    int8_t svcp_challenge_response_is_delayed;
    struct zhash_table *posture_challenge_hash;

    /* Queue for Chrome Posture profiles */
    struct posture_profile_object_head gposture_profiles;
    size_t gposture_profiles_count;

    /* Chrome Managed Browser */
    char g_browser_version[MAX_GPOSTURE_STR_LEN];
    char g_key_trust_level[MAX_GPOSTURE_STR_LEN];
    char g_operating_system[MAX_GPOSTURE_STR_LEN];
    char g_disk_encryption[MAX_GPOSTURE_STR_LEN];
    char g_os_firewall[MAX_GPOSTURE_STR_LEN];
    char g_screen_lock_secured[MAX_GPOSTURE_STR_LEN];
    char g_safe_browsing_protection_level[MAX_GPOSTURE_STR_LEN];
    char g_crowd_strike_agent[MAX_GPOSTURE_STR_LEN];

    int64_t *gprofile_gids;
    int gprofile_gid_count;
    int managed_browser_payload_version;

    char *redir_reason;             /* reason for broker redirection */
    int8_t no_brokers_to_redir;     /* used by site controller to close conn */

    /*
     * Temporary queue of svccp profiles.
     */
    struct posture_profile_object_head sv_posture_profiles_temp;
    size_t sv_posture_profiles_count_temp;
    int svcp_response_validation_in_progress;

    /*
     * SVCP Posture Profile Hanlding timer. If this timer expires, we should
     * treat this as having finished receiving all the posture profiles responses from the client
     */
    struct event *svcp_profiles_timer;

    /* StepUp Auth related entries */
    struct zhash_table *stepup_auth_level_htbl;
    zpath_rwlock_t stepup_auth_level_htbl_lock;

    // zia inspection 3.0
    void *zins_cookie; // pointer to zins_conn
    unsigned zpn_zia_identity_msg_sent : 1;
    unsigned zpn_zia_identity_msg_rcv : 1;
    struct argo_object *zins_identity;
    const char *zia_identity_status;

    /* domain extracted from the user name, received from auth structure */
    char *domain_name;
    // PRA Client states
    unsigned int is_pra:1;
    unsigned int is_pra_third_party_login:1;
    struct zpn_natural_log_throttle_data natural_log;
    char *client_state;
    int client_state_enabled_for_customer;

    // Extranet Apps
    void *zvpn_cookie;
    unsigned int zvpn_registered:1;

    /* epoch time when NP subnet download is complete */
    int64_t lan_subnets_complete_us;
    int64_t client_subnets_complete_us;

    uint32_t stepup_auth_al_counter:1;
    int redirect_ret_code;
};


enum zpn_broker_access_policy_post_action_type {
    post_action_type_unknown = 0,
    post_action_type_stepup_auth,
    post_action_type_max
};

/* Whenever we add a new post action type, add it before the 'max' type */
struct zpn_broker_access_policy_post_action_type_st {
    enum zpn_broker_access_policy_post_action_type post_action_type;
    char *post_action_type_str;
    size_t post_action_type_str_len;
};

struct zpn_broker_client_fohh_state_free_queue_stats {
    int64_t allocations;
    int64_t free_queue_count;
};

struct zpn_broker_client_fohh_state_free_queue {
    zpath_mutex_t                                          lock;
    struct zpn_broker_client_fohh_state_tailq              list;
    struct zpn_broker_fohh_state_free_queue_stats          stats;
};

/*
 * Boy, this looks a LOT like client state...
 *
 * Note that this info comes/goes with fohh_connections.
 */

TAILQ_HEAD(zpn_broker_assistant_fohh_state_tailq, zpn_broker_assistant_fohh_state);


/*
 * Store the capability string that is sent from the connector and for ease/faster use, convert it to integer
 * and store it as well.
 */
struct zpn_broker_assistant_capability {
    /* translates to ASC in RPC messages towards ubroker to dispatcher */
    int     capability_sticky_cache_enabled;
    /* translates to APU in RPC messages towards ubroker to dispatcher */
    int     capability_pathing_from_ubrk_enabled;
    int     capabilities_count;
    char**  capability_str;
};

/*
 * This is only related to assistant data channel (i.e this is only the connectors talking to the user broker via
 * data channel)
 */
struct zpn_broker_assistant_fohh_state {
    /* Standard TLV state must come first in this structure. */
    struct zpn_fohh_tlv tlv_state;
    struct zpn_zrdt_tlv zrdt_tlv_state;
    enum zpn_tlv_type tlv_type;

    /* Information regarding the authenticity/identity of the
     * assistant. */
    int64_t assitant_gid;
    int64_t assistant_group_gid;
    int64_t start_epoch_us;

    /* Allocated: */
    char *version;
    char *cname;

    /* The last used tag_id. i.e. the next one to use is this + 1. */
    int32_t last_used_tag_id;

    /* New fields for QBR */
    char *public_cloud;
    char *private_cloud;
    char *region_id;

    struct zpn_ast_auth_log log;

    /* 128 bit (16 bytes, 24 bytes base64) random ID for this TLS
     * connection, in base64 */
    uint8_t tunnel_id_bin[ZPN_TUNNEL_ID_BYTES];
    char tunnel_id[ZPN_TUNNEL_ID_BYTES_TEXT + 1];

    TAILQ_ENTRY(zpn_broker_assistant_fohh_state)  entry;
    int64_t incarnation;
    struct zpn_broker_assistant_capability  capability;
    int disabled;

    uint64_t monitor_count;

    uint32_t deletion_in_progress:1;
};

/*
 * State associated with any pbroker control connection.
 */
struct zpn_broker_pbroker_fohh_state {
    /* Information about the pbroker. */


    /* Allocated: (referenced by auth log) */
    char *log_version;
    char *log_sarge_version;
    char *log_type;
    char *log_dft_rt_intf;
    char *log_platform;
    char *client_type;
    char *close_reason;

    /* Not allocated. Duh. */
    char log_cc[3];

    /* 128 bit (16 bytes, 24 bytes base64) random ID for this TLS
     * connection, in base64 */
    uint8_t tunnel_id_bin[ZPN_TUNNEL_ID_BYTES];
    char tunnel_id[ZPN_TUNNEL_ID_BYTES_TEXT + 1];

    unsigned received_tcp_info:1;

    struct zpn_sys_auth_log log;

    struct event *timer;

    unsigned int status_report_received;
    unsigned int first_auth_log_sent;
    uint64_t monitor_count;

    int is_redirect_sent;
};

struct zpn_broker_sitec_fohh_state {
    char *log_version;
    char *log_sarge_version;
    char *log_type;
    char *log_dft_rt_intf;
    char *log_platform;
    char *client_type;
    char *close_reason;
    char log_cc[3];
    uint8_t tunnel_id_bin[ZPN_TUNNEL_ID_BYTES];
    char tunnel_id[ZPN_TUNNEL_ID_BYTES_TEXT + 1];
    unsigned received_tcp_info:1;
    struct zpn_sitec_auth_log log;
    struct event *timer;
    unsigned int status_report_received;
    unsigned int first_auth_log_sent;
    uint64_t monitor_count;
    int is_redirect_sent;
};

struct zpn_broker_client_aggregated_domain_cookie {
    int domain_cnt;
    char **domain_list;
    int ref_cnt;
    struct zpn_broker_client_fohh_state *c_state;
    int64_t timer_fired_us; // Time when the query for aggregated domain came in
    struct zpn_lib_appscale_client_cookie cplat;
};

extern struct zpn_broker broker;
extern struct event_base *broker_timer_base;

extern struct zpn_tlv *c_state_get_tlv(struct zpn_broker_client_fohh_state *c_state);
extern int c_state_get_fohh_thread_id(struct zpn_broker_client_fohh_state *c_state);
extern int c_state_is_ddil_connection(struct zpn_broker_client_fohh_state *c_state);
int64_t c_state_get_downstream_peer_id(struct zpn_broker_client_fohh_state *c_state);
extern int c_state_get_mtunnel_count(struct zpn_broker_client_fohh_state *c_state);

/*
 * This function groups together c_state based on customer gid
 * c_state is attached to list customer_gid_entry based on the customer gid
 * this func needs to be called in conjunction with add_client
 *
 * As zdtls customer gid is determined later than add_client so this
 * code cannot be part of add_client but called separately based on
 * when the customer gid is available
 *
 * @c_state : connection between broker and client
 *
 */
void zpn_broker_add_cstate_to_customer_gid_entry(struct zpn_broker_client_fohh_state *c_state);
void zpn_broker_remove_cstate_from_customer_gid_entry(struct zpn_broker_client_fohh_state *c_state, int64_t customer_gid);
void zpn_broker_update_cstate_customer_gid_entry(struct zpn_broker_client_fohh_state *c_state, int64_t prev_customer_gid);

void zpn_broker_add_client(struct zpn_broker_client_fohh_state *zc);
void zpn_broker_remove_client(struct zpn_broker_client_fohh_state *zc);

void zpn_broker_c_state_free_q_init();
void zpn_broker_a_state_free_q_init();
void zpn_broker_a_state_active_q_init();

int zpn_broker_is_dev_environment();

int mtunnel_request_ack(struct zpn_broker_mtunnel *mtunnel,
                        int64_t conn_incarnation,
                        int32_t tag,
                        const char *tunnel_id,
                        const char *error,
                        const char *reason);

struct fohh_connection *zpn_broker_mtunnel_client_f_conn(struct zpn_broker_mtunnel *mtunnel);
struct zrdt_conn *zpn_broker_mtunnel_client_z_conn(struct zpn_broker_mtunnel *mtunnel);
int zpn_client_authenticate_log(struct zpn_broker_client_fohh_state *c_state, int is_deauth) ;
int zpn_client_authenticate_state_log(struct zpn_broker_client_fohh_state *c_state);
int64_t zpn_is_broker_auth_state_log_feature_enabled(int64_t customer_gid);
int64_t zpn_is_broker_strict_dns_check_feature_enabled(int64_t customer_gid);

struct zpn_broker_client_fohh_state *zpn_broker_c_state_alloc(void);
void zpn_broker_c_state_free(struct zpn_broker_client_fohh_state *c_state);
int zpn_broker_pbroker_verify_callback(struct fohh_connection *f_conn);
/*
 * This async callback is always on the same thread context of the c_state
 */
int zpn_client_authenticate_async_wally_cb(void *response_callback_cookie,
                                           struct wally_registrant *registrant,
                                           struct wally_table *table,
                                           int64_t request_id,
                                           int row_count);
int zpn_client_authenticate_validate_async_wally_cb(void *response_callback_cookie,
                                                    struct wally_registrant *registrant,
                                                    struct wally_table *table,
                                                    int64_t request_id,
                                                    int row_count);
int zpn_client_authenticate(struct zpn_broker_client_fohh_state *c_state);
int zpn_get_idp_gid_from_username(struct zpn_broker_client_fohh_state *c_state);
int zpn_client_register_for_apps(struct zpn_broker_client_fohh_state *c_state, int do_segments);
void zpn_broker_client_count_client_capabilities_sent(struct zpn_broker_client_fohh_state *c_state, void *auth, int *use_segments);
void zpn_broker_client_record_capabilities_inc(struct zpn_broker_client_fohh_state* c_state);

int zpn_update_hops_inner(struct zpn_broker_client_fohh_state *c_state,
                          int hops_count,
                          char **hop_type,
                          int hop_type_count,
                          int64_t *brks,
                          int brks_count,
                          int64_t *brks_grp,
                          int brks_grp_count,
                          char **tunnel_ids,
                          int tunnel_ids_count,
                          char **sys_type,
                          int sys_type_count,
                          char **hop_debug,
                          int hop_debug_count);
void zpn_update_current_hop(struct zpn_broker_client_fohh_state *c_state, int current_hop_index);
void connection_follow_timer_callback(int sock, short flags, void *cookie);

int zpn_broker_client_vdi_stats(struct zpath_debug_state *request_state,
                                const char **query_values,
                                int query_value_count,
                                void *cookie);

int64_t zpn_is_broker_auth_log_redirect_status_feature_enabled();
int64_t zpn_is_redirect_reason_maintanence(const char* redirect_reason);
void zpn_broker_update_auth_app_down_state(struct zpn_broker_client_fohh_state *c_state);
#endif /* _ZPN_BROKER_PRIVATE_H_ */
