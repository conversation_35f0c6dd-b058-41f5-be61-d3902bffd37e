/*
 * zpn_pbroker_group.c. Copyright (C) 2019 Zscaler Inc. All Rights Reserved.
 */

#include "wally/wally.h"
#include "zpath_lib/zpath_app.h"

#include "zpath_lib/zpath_debug.h"

#include "zpn/zpn_lib.h"

#include "zpn/zpn_pbroker_group.h"
#include "zpn/zpn_pbroker_group_compiled.h"
#include "zpath_misc/zpath_dta_checkpoint.h"

/*
 * Server group implementation.
 */

struct customer_load_state {
    int done;
    zpath_mutex_t lock;
    struct wally_callback_queue *queue;
};

static zpath_mutex_t hash_lock;
static struct zhash_table *customer_table = NULL;
static struct zhash_table *debug_pse_groups_exclusive_for_business_continuity = NULL;

struct argo_structure_description *zpn_pbroker_group_description = NULL;

static struct wally_index_column **zpn_pbroker_group_gid_column = NULL;
static struct wally_index_column **zpn_pbroker_group_customer_gid_column = NULL;

#define KM_TO_MILES 1.60934

static void zpn_private_broker_group_row_fixup(struct argo_object *row)
{
    struct zpn_private_broker_group *grp = row->base_structure_void;
    if(grp->scope_gid == 0) grp->scope_gid = grp->customer_gid;

    if (grp->grace_distance_value_unit) {
        if (!strncasecmp(grp->grace_distance_value_unit, "KMS", sizeof("KMS"))) {
            /* Grace distance value configured in KMS. Convert it into Miles as redirection logic expects it to be in MILES. */
            grp->calc_grace_distance_value_miles = grp->grace_distance_value / KM_TO_MILES;
        } else if(!strncasecmp(grp->grace_distance_value_unit, "MILES", sizeof("MILES"))) {
            /* Grace distance value configured in MILES.*/
            grp->calc_grace_distance_value_miles = grp->grace_distance_value;
        } else {
            ZPN_LOG(AL_ERROR, "zpn_private_broker_group_row_fixup: Unexpected Unit:%s for Grace distance value",
                    grp->grace_distance_value_unit);
        }
    }

    if (zpn_debug_get(ZPN_DEBUG_PRIVATE_BROKER_IDX)) {
        char dump[8000];
        if (argo_object_dump(row, dump, sizeof(dump), NULL, 0) == ARGO_RESULT_NO_ERROR) {
            ZPN_DEBUG_PRIVATE_BROKER("Row callback: %s", dump);
        }
    }
}

static int zpn_pbroker_group_row_callback(void *cookie,
                                            struct wally_registrant *registrant,
                                            struct wally_table *table,
                                            struct argo_object *previous_row,
                                            struct argo_object *row,
                                            int64_t request_id)
{
#ifdef DTA_CHECKPOINT
    struct zpn_private_broker_group *pbg = row->base_structure_void;
    if(pbg->scope_gid && pbg->scope_gid != pbg->customer_gid) return WALLY_RESULT_NO_ERROR;
#endif
    if (zpn_debug_get(ZPN_DEBUG_PRIVATE_BROKER_IDX)) {
        char dump[8000];
        if (argo_object_dump(row, dump, sizeof(dump), NULL, 0) == ARGO_RESULT_NO_ERROR) {
            ZPN_DEBUG_PRIVATE_BROKER("Row callback: %s", dump);
        }
    }

    return WALLY_RESULT_NO_ERROR;
}

void argo_zpn_private_broker_group_init()
{
    if(!argo_register_global_structure(ZPN_PRIVATE_BROKER_GROUP_HELPER))
        ZPN_LOG(AL_ERROR, "Could not register zpn_private_broker_group argo object");
}

int zpn_pbroker_pse_grp_exclusive_for_business_continuity_flag_is_set(int64_t pse_grp_gid)
{
    if (zhash_table_lookup(debug_pse_groups_exclusive_for_business_continuity,
                           &pse_grp_gid,
                           sizeof(pse_grp_gid),
                           NULL)) {
        return 1;
    }

    return 0;
}

int zpn_pbroker_set_pse_grp_exclusive_for_business_continuity_flag(int64_t pse_grp_gid,
                                                                   int flag)
{
    int res;
    int dummy;

    if (flag) {
        res = zhash_table_store_not_exist(debug_pse_groups_exclusive_for_business_continuity,
                                          &pse_grp_gid,
                                          sizeof(pse_grp_gid),
                                          0, &dummy);
    } else {
        res = zhash_table_remove(debug_pse_groups_exclusive_for_business_continuity,
                                 &pse_grp_gid,
                                 sizeof(pse_grp_gid),
                                 NULL);
    }

    return res;
}

void zpn_pbroker_debug_pse_groups_exclusive_for_business_continuity_hash_init() {
    debug_pse_groups_exclusive_for_business_continuity = zhash_table_alloc(&zpn_allocator);
}

int zpn_pbroker_group_init(struct wally *single_tenant_wally,
                           int64_t single_tenant_gid,
                           wally_row_callback_f *row_cb,
                           int single_tenant_fully_loaded,
                           int register_with_zpath_table)
{
    int res;

    customer_table = zhash_table_alloc(&zpn_allocator);
    hash_lock = ZPATH_MUTEX_INIT;

    zpn_pbroker_debug_pse_groups_exclusive_for_business_continuity_hash_init();
    zpn_pbroker_group_description = argo_register_global_structure(ZPN_PRIVATE_BROKER_GROUP_HELPER);
    if (!zpn_pbroker_group_description) {
        ZPN_LOG(AL_ERROR, "No description: zpn_pbroker_group_init");
        return ZPN_RESULT_ERR;
    }

    if (single_tenant_wally) {
        struct wally_table *table;
        int shard_index = ZPATH_SHARD_FROM_GID(single_tenant_gid);
        zpn_pbroker_group_gid_column = ZPN_CALLOC(ZPATH_MAX_SHARDS * sizeof(*zpn_pbroker_group_gid_column));
        zpn_pbroker_group_customer_gid_column =
            ZPN_CALLOC(ZPATH_MAX_SHARDS * sizeof(*zpn_pbroker_group_customer_gid_column));

        if (single_tenant_fully_loaded) {
            /* This is generally used by private brokers */
            res = zpath_app_fully_loaded_customer_table(&table,
                                                        ZPATH_GID_GET_CUSTOMER_GID(single_tenant_gid),
                                                        single_tenant_wally,
                                                        zpn_pbroker_group_description,
                                                        (row_cb ? row_cb : zpn_pbroker_group_row_callback),
                                                        NULL,
                                                        zpn_private_broker_group_row_fixup,
                                                        register_with_zpath_table);
            if (res) {
                ZPN_LOG(AL_ERROR, "Could not fully load zpn_private_broker_group table");
                return ZPN_RESULT_ERR;
            }
        } else {
            /* This is generally used by connectors */
            table = wally_table_create(single_tenant_wally,
                                       1,
                                       zpn_pbroker_group_description,
                                       (row_cb ? row_cb : zpn_pbroker_group_row_callback),
                                       NULL,
                                       1,
                                       1,
                                       zpn_private_broker_group_row_fixup);
            if (!table) {
                ZPN_LOG(AL_ERROR, "Could not get zpn_private_broker_group table");
                return ZPN_RESULT_ERR;
            }
        }

        zpn_pbroker_group_gid_column[shard_index] = wally_table_get_index(table, "gid");
        if (!zpn_pbroker_group_gid_column[shard_index]) {
            ZPN_LOG(AL_ERROR, "Could not get column");
            return ZPN_RESULT_ERR;
        }

        zpn_pbroker_group_customer_gid_column[shard_index] = wally_table_get_index(table, "customer_gid");
        if (!zpn_pbroker_group_customer_gid_column[shard_index]) {
            ZPN_LOG(AL_ERROR, "Could not get column");
            return ZPN_RESULT_ERR;
        }
    } else {
        res = zpath_app_fully_loaded_sharded_table(zpn_pbroker_group_description,
                                                   zpn_pbroker_group_row_callback,
                                                   NULL,
                                                   zpn_private_broker_group_row_fixup);
        if (res) {
            return res;
        }

        zpn_pbroker_group_gid_column = zpath_app_get_sharded_index("zpn_private_broker_group", "gid");
        if (!zpn_pbroker_group_gid_column) {
            ZPN_LOG(AL_ERROR, "Could not get zpn_pbroker_group_gid_column");
            return ZPN_RESULT_ERR;
        }

        zpn_pbroker_group_customer_gid_column = zpath_app_get_sharded_index("zpn_private_broker_group", "customer_gid");
        if (!zpn_pbroker_group_customer_gid_column) {
            ZPN_LOG(AL_ERROR, "Could not get zpn_pbroker_group_customer_gid_column");
            return ZPN_RESULT_ERR;
        }
    }

    return ZPN_RESULT_NO_ERROR;
}


int zpn_pbroker_group_get_by_gid(int64_t gid,
                                 struct zpn_private_broker_group **group,
				                 int register_on_miss,
                                 wally_response_callback_f callback_f,
                                 void *callback_cookie,
                                 int64_t callback_id)
{
    int res;
    size_t row_count = 1;
    int shard_index = ZPATH_SHARD_FROM_GID(gid);

    res = wally_table_get_rows_fast(zpn_pbroker_group_gid_column[shard_index],
                                    &gid,
                                    sizeof(gid),
                                    (void **) group,
                                    &row_count,
                                    register_on_miss,
                                    callback_f,
                                    callback_cookie,
                                    callback_id);
#ifdef DTA_CHECKPOINT
    ROW_FILTER_DTA_CHECKPOINT(group, row_count, ZPATH_GID_GET_CUSTOMER_GID(gid));
#endif
    return res;
}

int zpn_pbroker_group_get_by_customer_gid(int64_t customer_gid,
                                          struct zpn_private_broker_group **group,
                                          wally_response_callback_f callback_f,
                                          void *callback_cookie,
                                          int64_t callback_id)
{
    int res;
    size_t row_count = 1;
    int shard_index = ZPATH_SHARD_FROM_GID(customer_gid);

    res = wally_table_get_rows_fast(zpn_pbroker_group_customer_gid_column[shard_index],
                                    &customer_gid,
                                    sizeof(customer_gid),
                                    (void **) group,
                                    &row_count,
                                    1,
                                    callback_f,
                                    callback_cookie,
                                    callback_id);
#ifdef DTA_CHECKPOINT
    ROW_FILTER_DTA_CHECKPOINT(group, row_count, customer_gid);
#endif
    return res;
}

static void zpn_pbroker_group_response_callback_deferred(void *cookie1, void *cookie2)
{
    struct customer_load_state *load_state = cookie1;
    ZPATH_MUTEX_LOCK(&(load_state->lock), __FILE__, __LINE__);
    if (!load_state->done) {
        wally_callback_queue_callback(load_state->queue);
        load_state->done = 1;
    }
    ZPATH_MUTEX_UNLOCK(&(load_state->lock), __FILE__, __LINE__);
}


static int zpn_pbroker_group_response_callback(void *response_callback_cookie,
                                               struct wally_registrant *registrant,
                                               struct wally_table *table,
                                               int64_t request_id,
                                               int row_count)
{
    zevent_defer(zpn_pbroker_group_response_callback_deferred, response_callback_cookie, NULL, 0);
    return ZPATH_RESULT_NO_ERROR;
}

int zpn_pbroker_group_load(int64_t customer_gid,
                           wally_response_callback_f callback_f,
                           void *callback_cookie,
                           int64_t callback_id)
{
    /* This code is optimized to not need locks for the common case */
    struct customer_load_state *load_state;
    int res;

    if (!customer_gid) {
        ZPN_LOG(AL_ERROR, "customer_gid = 0");
        return ZPN_RESULT_ERR;
    }

    load_state = zhash_table_lookup(customer_table, &customer_gid, sizeof(customer_gid), NULL);
    if (!load_state) {
        ZPATH_MUTEX_LOCK(&hash_lock, __FILE__, __LINE__);
        load_state = zhash_table_lookup(customer_table, &customer_gid, sizeof(customer_gid), NULL);
        if (!load_state) {
            load_state = ZPN_CALLOC(sizeof(*load_state));
            load_state->lock = ZPATH_MUTEX_INIT;
            zhash_table_store(customer_table, &customer_gid, sizeof(customer_gid), 0, load_state);
        }
        ZPATH_MUTEX_UNLOCK(&hash_lock, __FILE__, __LINE__);
    }
    if (load_state->done) return ZPATH_RESULT_NO_ERROR;

    ZPATH_MUTEX_LOCK(&(load_state->lock), __FILE__, __LINE__);
    if (load_state->done) {
        /* Might have been set while we wait for lock... */
        ZPATH_MUTEX_UNLOCK(&(load_state->lock), __FILE__, __LINE__);
        return ZPATH_RESULT_NO_ERROR;
    }
    if (!load_state->queue) {
        /* Need to register */
        /* NOTE: register_for_row can call our callback SYNCHRONOUSLY
         * if wally already has state for this connection. We are not
         * handling that in this case because it should be impossible
         * for something else to have read this state before this
         * routine, and this routine only reads it once. If this use
         * model were to change, this code would have to be
         * improved. */

        int shard_id = ZPATH_GID_GET_SHARD(customer_gid);
        load_state->queue = wally_callback_queue_create();
        res = wally_table_register_for_row(NULL,
                                           zpn_pbroker_group_customer_gid_column[shard_id],
                                           &customer_gid,
                                           sizeof(customer_gid),
                                           0, // int64_t request_id,
                                           0, // int64_t request_sequence,
                                           zpn_app_request_atleast_one, // int request_atleast_one,
                                           0, // int just_callback,
                                           0, // int unique_registration,
                                           zpn_pbroker_group_response_callback,
                                           load_state);
        /* Result is ALWAYS asynchronous, even if the callback will
         * come synchronously... Should fix that some day. */
        if (res != ZPN_RESULT_ASYNCHRONOUS) {
            ZPN_LOG(AL_ERROR, "Received response = %s", zpn_result_string(res));
        }
    }
    wally_callback_queue_add(load_state->queue, callback_f, callback_cookie, callback_id);

    ZPATH_MUTEX_UNLOCK(&(load_state->lock), __FILE__, __LINE__);

    return ZPN_RESULT_ASYNCHRONOUS;
}
