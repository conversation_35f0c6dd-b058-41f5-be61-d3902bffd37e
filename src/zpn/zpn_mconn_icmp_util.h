/*
 * zpn_mconn_icmp_util.h. Copyright (C) 2020 Zscaler Inc. All Rights Reserved
 *
 */

#ifndef __ZPN_MCONN_ICMP_UTIL_H__
#define __ZPN_MCONN_ICMP_UTIL_H__

#include <stdint.h>
#include <event2/buffer.h>

#include "zpn/zpn_mconn.h"

#define ICMP_HDR_LEN 8

#define ZPN_MCONN_ICMP_PKT_NO_ERR                             0
#define ZPN_MCONN_ICMP_PKT_ACCESS_ERR                         1
#define ZPN_MCONN_ICMP_PKT_INTERNAL_ERR                       2
#define ZPN_MCONN_ICMP_PKT_FORWARD_TO_PEER                    3
#define ZPN_MCONN_ICMP_PKT_FORWARD_REPLY_TO_CLIENT            4
#define ZPN_MCONN_ICMP_PKT_FORWARD_TERROR_TO_CLIENT           5
#define ZPN_MCONN_ICMP_PKT_FORWARD_DERROR_TO_CLIENT           6
#define ZPN_MCONN_ICMP_PKT_MALFORMED_DROP                     7
#define ZPN_MCONN_ICMP_PKT_TIME_EXCEEDED_FRAG_DROP            8
#define ZPN_MCONN_ICMP_PKT_TIME_EXCEEDED_DROP                 9
#define ZPN_MCONN_ICMP_PKT_FRAG_DROP                          10
#define ZPN_MCONN_ICMP_PKT_RATE_LIMIT_EXCEEDED_ERR            11
#define ZPN_MCONN_ICMP_PKT_ZERO_LEN_DROP                      12

#define ZPN_MCONN_ICMP_ACCESS_TYPE_NONE                         0
#define ZPN_MCONN_ICMP_ACCESS_TYPE_PING                         1
#define ZPN_MCONN_ICMP_ACCESS_TYPE_PING_TRACEROUTING            2

int zpn_mconn_icmp_pkt_handler(struct zpn_mconn *mconn, struct evbuffer *icmp_check_buf);
int zpn_mconn_icmpv6_pkt_handler(struct zpn_mconn *mconn, struct evbuffer *icmp_check_buf);
struct evbuffer *zpn_mconn_icmp_packetize(uint8_t *buf, size_t buf_len, int icmp_type,
                                          int icmp_code, uint16_t req_id, uint16_t req_seq,
                                          struct in_addr ip_src, uint16_t ip_id);

struct evbuffer *zpn_mconn_icmpv6_packetize(uint8_t *icmp_buf, size_t icmp_buf_len,
                                            int icmp_type, int icmp_code, uint16_t req_id,
                                            uint16_t req_seq, struct in6_addr ip6_src);
#endif // __ZPN_MCONN_ICMP_UTIL_H__
