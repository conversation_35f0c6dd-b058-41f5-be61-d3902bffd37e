/*
 * zpn_rpc.h. Copyright (C) 2014 Zscaler Inc. All Rights Reserved.
 */

#ifndef _ZPN_RPC_H_
#define _ZPN_RPC_H_

/*
 * This file describes most of the RPC's used within the ZPN system.
 *
 * The RPC's are defined as 'C' structures, but are actually
 * transmitted via JSON objects to ease forward/backward
 * compatibility.
 *
 * A Note on TLV's:
 *
 * TLS connections between systems all use a simple TLV communication
 * mechanism. The TLV format is as follows:
 *
 * 00112233 : 4 bytes, network byte order TAG.
 * 44556677 : 4 bytes, network byte order LENGTH.
 * ...      : LENGTH bytes of data.
 *
 * The LENGTH specified by the LENGTH of the TLV is the LENGTH of the
 * payload data only. It does not include the 'TAG' and 'LENGTH'
 * itself. (i.e. a full TLV size is LENGTH+8 bytes)
 *
 * A TAG + LENGTH=0 field can be valid.
 *
 * The TAG usage is as follows:
 *
 * TAG 0 ........ Used exclusively for sending JSON objects between
 *                peers. JSON objects are the mechanism by which RPCs
 *                are performed.
 *
 * TAG 1 - 1023 . Reserved
 *
 * TAG 1024+ .... Used for identifying 'microtunnels'- streams within
 *                the stream. These tags can eventually be
 *                re-used. (won't happen often...)
 *
 * The most annoying part of tunnels within tunnels is that individual
 * streams need to have independent flow control to avoid various
 * blocking conditions. The pause/resume RPCs were created to deal
 * with this flow control.
 *
 *
 * The client startup sequence is as follows, once a TLS connection
 * has been established. Currently the broker gets 100% of the client
 * authentication information from the CN of the certificate. (It is
 * just a user name)
 *
 * Client --> Broker: zpn_version is sent.
 *
 * Broker --> Client: zpn_version_ack. The ack will be empty if there
 *    is no error. The client does NOT have to wait for this ack to
 *    start authentication. The ack comes back only for completeness.
 *    Not waiting for the ack removes one RTT from connection
 *    setup. Note that the broker doesn't actually check a version at
 *    this time- it always acknowledges without error. In fact, the
 *    broker doesn't even verify that a version was sent...
 *
 * Client --> Broker: zpn_client_authenticate. This is a placeholder,
 *    and is not currently implemented.
 *
 * Broker --> Client: Authentication acknowledgement. Also a
 *    placeholder.
 *
 * At this point, the TLS connection is ready to use.
 *
 *
 * In order to create a tunnel for an application, the following take
 * place:
 *
 * Client --> Broker: zpn_mtunnel_request. (See below for details)
 *
 * Broker --> Client: zpn_mtunnel_request_ack. (See below) OR:
 *
 * Broker --> Client: zpn_mtunnel_end. (See below)
 *
 * A valid 'request_ack' is an indication that data can now be sent on
 * the specified tag_id in order to get to the end system.
 */
/*
 * A couple notes on ID's and tags.
 *
 ******
 * int64_t  *_id
 *
 * Most of these are GID's within the zpath/shift management
 * infrastructure. They represent configured entities within the
 * overall product.
 *
 ******
 * int64_t id;
 *
 * These id's are used to associate requests with acknowledgements.
 * The ID is chosen by the client, usually in sequential order. id 0
 * should not be used. (just start at 1. It makes everything easier)
 * There is no concern with using sequential numbers
 * (i.e. predictability) because this is all within a TLS stream.
 *
 ******
 * char *mtunnel_id;
 *
 * These id's represent a microtunnel. The mtunnel_id is an opaque
 * string, currently comprised of 256 random bits. (yes,
 * overkill). The client does not really need to make use of
 * mtunnel_id. However, the mtunnel_id is sent to the client along
 * with tag_id for debugging purposes. The mtunnel_id is sufficiently
 * large/random such that there should never be an mtunnel_id repeat
 * in the lifetime of the product. Or the solar system, for that
 * matter. Unless someone got the crypto RNG wrong. (likely)
 *
 ******
 * char *error;
 *
 * These strings represent failure reasons when there is a
 * communication error. They will eventually need to be formalized to
 * ease recognizing what situations are occuring in the system. (not
 * authorized, unknown service, server failure, system failure, etc)
 * In acknowledgements that are 'normal' or 'successful', the error
 * string doesn't exist.
 */

/*
 * NOTE: RPC messages for IPARS are defined in zpn_ipars_rpc.h separately.
 */

#define ZPN_CLIENT_MAX_BROKERS 11
#define ZPN_DNS_MAX_TARGETS 50
#define ZPN_DNS_MAX_CNAMES 5
#define ZPN_DNS_MAX_TXTS 100
#define ZPN_PBCTL_LOG_CONTROL_OPCODE_SET_LOGGING_FLAG 1
#define ZPN_PBCTL_LOG_CONTROL_OPCODE_SET_LOG_UPLOAD_STATE_DEFAULT 0
#define SVCP_MAX_PAYLOADS 50
#define ZPN_NATURAL_THROTTLE_LOG_FLUSH_REASON_SIZE    128

#define ZPN_SCCTL_LOG_CONTROL_OPCODE_SET_LOGGING_FLAG 1
#define ZPN_SCCTL_LOG_CONTROL_OPCODE_SET_LOG_UPLOAD_STATE_DEFAULT 0

#define MAX_GPOSTURE_STR_LEN 64
#define MAX_GD_COOKIE_ID_LEN (MAX_GPOSTURE_STR_LEN + 1)

#include "argo/argo.h"
#include "fohh/fohh.h"
#include "zpath_lib/zpath_geoip_common.h"
#include "zpn/zpn_lib.h"
#include "ztlv/zpn_tlv.h"
#include "zpn/zpn_zrdt.h"
#include "zpn_zdx/zpn_zdx_lib.h"
#include "zpn/zpn_system.h"

// Using this macro to be argo friendly since argo doesn't support + 1 in object defintions
#define CC_STR_LEN_P1 (CC_STR_LEN + 1)

extern struct argo_structure_description *zpn_version_description;
extern struct argo_structure_description *zpn_version_ack_description;
extern struct argo_structure_description *zpn_client_authenticate_description;
extern struct argo_structure_description *zpn_client_authenticate_ack_description;
extern struct argo_structure_description *zpn_machine_tunnel_client_authenticate_description;
extern struct argo_structure_description *zpn_machine_tunnel_client_authenticate_ack_description;
extern struct argo_structure_description *zpn_trusted_client_authenticate_description;
extern struct argo_structure_description *zpn_trusted_client_authenticate_ack_description;
extern struct argo_structure_description *zpn_exporter_client_authenticate_description;
extern struct argo_structure_description *zpn_exporter_client_authenticate_ack_description;
extern struct argo_structure_description *zpn_bi_client_authenticate_description;
extern struct argo_structure_description *zpn_bi_client_authenticate_ack_description;
extern struct argo_structure_description *zpn_pbroker_client_authenticate_description;
extern struct argo_structure_description *zpn_pbroker_client_authenticate_ack_description;
extern struct argo_structure_description *zpn_pbroker_client_switch_to_cloud_description;
extern struct argo_structure_description *zpn_ec_client_authenticate_description;
extern struct argo_structure_description *zpn_ec_client_authenticate_ack_description;
extern struct argo_structure_description *zpn_vdi_client_authenticate_description;
extern struct argo_structure_description *zpn_vdi_client_authenticate_ack_description;
extern struct argo_structure_description *zpn_ia_client_authenticate_description;

extern struct argo_structure_description *zpn_ia_client_authenticate_ack_description;
extern struct argo_structure_description *zpn_zins_client_authenticate_description;
extern struct argo_structure_description *zpn_zins_client_authenticate_ack_description;
extern struct argo_structure_description *zpn_zia_identity_description;

extern struct argo_structure_description *zpn_broker_redirect_description;
extern struct argo_structure_description *zpn_client_app_description;
extern struct argo_structure_description *zpn_client_app_complete_description;
extern struct argo_structure_description *zpn_client_search_domain_description;
extern struct argo_structure_description *zpn_client_search_domain_complete_description;
extern struct argo_structure_description *zpn_client_connection_upgrade_description;
extern struct argo_structure_description *zpn_client_connection_upgrade_response_description;
extern struct argo_structure_description *zpn_broker_client_latency_probe_stats_description;
extern struct argo_structure_description *zpn_broker_vdi_auth_stats_description;
extern struct argo_structure_description *zpn_svcp_stats_description;
extern struct argo_structure_description *zpn_stepup_auth_stats_description;

extern struct argo_structure_description *zpn_client_config_updated_description;
extern struct argo_structure_description *zpn_client_aggregated_domains_description;

extern struct argo_structure_description *zpn_posture_profile_start_description;
extern struct argo_structure_description *zpn_posture_profile_description;
extern struct argo_structure_description *zpn_posture_profile_ack_description;
extern struct argo_structure_description *zpn_server_validated_cert_posture_check_request_description;
extern struct argo_structure_description *zpn_server_validated_cert_posture_check_response_payload_description;
extern struct argo_structure_description *zpn_server_validated_cert_posture_check_response_description;
extern struct argo_structure_description *zpn_server_validated_cert_posture_check_done_description;
extern struct argo_structure_description *zpn_svcp_pb_posture_profile_description;

extern struct argo_structure_description *zpn_trusted_networks_description;
extern struct argo_structure_description *zpn_trusted_networks_ack_description;

extern struct argo_structure_description *zpn_mtunnel_request_description;
extern struct argo_structure_description *zpn_mtunnel_request_int_description;
extern struct argo_structure_description *zpn_mtunnel_request_ack_description;
extern struct argo_structure_description *zpn_mtunnel_bind_description;
extern struct argo_structure_description *zpn_mtunnel_bind_ack_description;
extern struct argo_structure_description *zpn_mtunnel_end_description;
extern struct argo_structure_description *zpn_mtunnel_tag_pause_description;
extern struct argo_structure_description *zpn_mtunnel_tag_resume_description;
extern struct argo_structure_description *zpn_broker_request_description;
extern struct argo_structure_description *zpn_broker_request_ack_description;

extern struct argo_structure_description *zpn_app_route_info_description;
extern struct argo_structure_description *zpn_health_report_description;
extern struct argo_structure_description *zpn_app_route_registration_description;
extern struct argo_structure_description *zpn_app_route_log_description;
extern struct argo_structure_description *zpn_app_route_discovery_description;
extern struct argo_structure_description *zpn_trans_log_description;
extern struct argo_structure_description *zpn_health_log_description;
extern struct argo_structure_description *zpn_auth_log_description;
extern struct argo_structure_description *zpn_audit_log_description;
extern struct argo_structure_description *zpn_ast_auth_log_description;
extern struct argo_structure_description *zpn_sys_auth_log_description;
extern struct argo_structure_description *zpn_sitec_auth_log_description;
extern struct argo_structure_description *zpn_ast_auth_report_description;
extern struct argo_structure_description *zpn_tcp_info_report_description;
extern struct argo_structure_description *zpn_zrdt_info_report_description;
extern struct argo_structure_description *zpn_asst_environment_report_description;
extern struct argo_structure_description *zpn_pbroker_environment_report_description;
extern struct argo_structure_description *zpn_asst_active_control_connection_description;
extern struct argo_structure_description *zpn_dns_client_check_description;
extern struct argo_structure_description *zpn_app_client_check_description;
extern struct argo_structure_description *zpn_app_client_check_full_app_description;
extern struct argo_structure_description *zpn_dns_dispatch_check_description;
extern struct argo_structure_description *zpn_broker_dispatcher_c2c_app_check_description;
extern struct argo_structure_description *zpn_dns_assistant_check_description;
extern struct argo_structure_description *zpn_assistant_log_control_description;
extern struct argo_structure_description *zpn_assistant_stats_control_description;
extern struct argo_structure_description *zpn_assistant_restart_description;
extern struct argo_structure_description *zpn_assistant_status_report_description;
extern struct argo_structure_description *zpn_assistant_pvt_key_control_description;
extern struct argo_structure_description *zpn_assistant_gen_cert_control_description;
extern struct argo_structure_description *zpn_assistant_app_cert_key_control_description;
extern struct argo_structure_description *zpn_asst_state_description;
extern struct argo_structure_description *zpn_asst_restart_reason_description;
extern struct argo_structure_description *zpn_fohh_tlv_window_update_description;
extern struct argo_structure_description *zpn_fohh_tlv_window_update_batch_description;
extern struct argo_structure_description *zpn_dispatcher_status_description;
extern struct argo_structure_description *zpn_broker_info_description;
extern struct argo_structure_description *zpn_http_trans_log_description;
extern struct argo_structure_description *zpn_clientless_app_query_description;
extern struct argo_structure_description *zpn_clientless_app_query_ack_description;

extern struct argo_structure_description *zpn_pbroker_log_control_description;
extern struct argo_structure_description *zpn_pbroker_stats_control_description;
extern struct argo_structure_description *zpn_pbroker_stats_upload_description;
extern struct argo_structure_description *zpn_pbroker_status_report_description;
extern struct argo_structure_description *zpn_pbroker_file_stats_description;
extern struct argo_structure_description *zpn_pbroker_dns_dispatcher_stats_description;

extern struct argo_structure_description *zpn_pse_resiliency_stats_description;
extern struct argo_structure_description *zpn_pbroker_comprehensive_stats_description;

extern struct argo_structure_description *zpn_system_inventory_description;
extern struct argo_structure_description *zpn_assistant_data_stats_description;
extern struct argo_structure_description *zpn_assistant_rpc_stats_description;
extern struct argo_structure_description *zpn_assistant_scache_stats_description;
extern struct argo_structure_description *zpn_assistant_app_stats_description;
extern struct argo_structure_description *zpn_assistant_data_mtunnel_global_stats_description;
extern struct argo_structure_description *zpn_assistant_data_mtunnel_stats_description;
extern struct argo_structure_description *zpn_assistant_dns_stats_description;
extern struct argo_structure_description *zpn_broker_client_path_cache_stats_description;
extern struct argo_structure_description *zpn_broker_client_neg_path_cache_stats_description;
extern struct argo_structure_description *zpn_client_search_domain_all_description;
extern struct argo_structure_description *zpn_assistant_comprehensive_stats_description;
extern struct argo_structure_description *zpn_assistant_monitor_stats_description;
extern struct argo_structure_description *zpn_assistant_fproxy_stats_description;
extern struct argo_structure_description *zpn_private_broker_fproxy_stats_description;
extern struct argo_structure_description *zpn_sitec_fproxy_stats_description;
extern struct argo_structure_description *zpn_a2pb_authentication_description;
extern struct argo_structure_description *zpn_a2pb_authentication_ack_description;
extern struct argo_structure_description *zpn_assistant_zvm_stats_description;
extern struct argo_structure_description *zpn_assistant_upgrade_stats_description;
extern struct argo_structure_description *zpn_private_broker_upgrade_stats_description;
extern struct argo_structure_description *zpn_sitec_upgrade_stats_description;
extern struct argo_structure_description *np_connector_wireguard_stats_description;

extern struct argo_structure_description *zpn_client_broker_app_registration_description;
extern struct argo_structure_description *zpn_client_private_broker_app_registration_description;
extern struct argo_structure_description *zpn_client_app_registration_notification_description;
extern struct argo_structure_description *zpn_broker_dispatcher_app_registration_description;

extern struct argo_structure_description *zpn_transit_req_description;
extern struct argo_structure_description *zpn_mtunnel_stats_description;
extern struct argo_structure_description *zpn_assistant_ncache_stats_description;
extern struct argo_structure_description *zpn_auth_state_log_description;

extern struct argo_structure_description *zpn_file_fetch_key_description;
extern struct argo_structure_description *zpn_exporter_log_data_description;
extern struct argo_structure_description *zpn_exporter_data_ack_description;
extern struct argo_structure_description *zpn_exporter_log_data_ack_description;

extern struct argo_structure_description *zpn_decrypt_key_request_description;
extern struct argo_structure_description *zpn_decrypt_key_response_description;

extern struct argo_structure_description *zpn_private_broker_dr_stats_description;
extern struct argo_structure_description *zpn_assistant_dr_stats_description;
extern struct argo_structure_description *zpn_pbroker_mtunnel_stats_description;
extern struct argo_structure_description *zpn_pbclient_debug_stats_description;
extern struct argo_structure_description *zpn_exporter_pra_guac_proxy_data_description;
extern struct argo_structure_description *zpn_client_state_description;
extern struct argo_structure_description *client_state_description;

extern struct argo_structure_description *zpn_sitec_log_control_description;
extern struct argo_structure_description *zpn_sitec_stats_control_description;
extern struct argo_structure_description *zpn_sitec_status_report_description;
extern struct argo_structure_description *zpn_sitec_environment_report_description;
extern struct argo_structure_description *zpn_firedrill_stats_description;
extern struct argo_structure_description *zpn_sitec_firedrill_exit_description;
extern struct argo_structure_description *zpn_sitec_comprehensive_stats_description;
extern struct argo_structure_description *zpn_sitec_siem_log_stats_description;
extern struct argo_structure_description *zpn_sitec_siem_log_stats_per_conn_description;

extern struct argo_structure_description *zpn_mtunnel_data_mconn_stats_description;
extern struct argo_structure_description *zpn_managed_chrome_payload_description;

extern struct argo_structure_description *zpn_zia_instance_info_description;
extern struct argo_structure_description *zpn_zia_instance_info_ack_description;
extern struct argo_structure_description *zpn_zia_location_update_description;
extern struct argo_structure_description *zpn_zia_location_update_ack_description;
extern struct argo_structure_description *zpn_zia_health_description;
extern struct argo_structure_description *zpn_zia_instance_data_description;
extern struct argo_structure_description *zpn_zia_instance_data_ack_description;
extern struct argo_structure_description *zpn_broker_dispatcher_zia_location_registration_description;
extern struct argo_structure_description *zpn_add_proxy_description;
extern struct argo_structure_description *zpn_add_proxy_ack_description;
extern struct argo_structure_description *zpn_delete_proxy_description;

/***************************************************/

/*
 * A version is always sent as a first message, from client to server,
 * in any client/server connection. (i.e. sent from client to broker,
 * from assistant to broker, and from broker to dispatcher)
 *
 * A version_ack is always sent, but is empty if there is no
 * error. Otherwise error contains a sad string.
 */
struct zpn_version {                   /* _ARGO: object_definition */
    int32_t version_major;             /* _ARGO: integer */
    int32_t version_minor;             /* _ARGO: integer */
    int32_t version_patch;             /* _ARGO: integer */
    const char *ver_str;               /* _ARGO: string */
    int64_t redirect_timestamp_s;      /* _ARGO: integer */
    char *client_state;                /* _ARGO: string */
    /* Platform or Operating System of the Client
    * ZCC has different version for Mac, Windows, Linux
    * This identifies the client type.
    */
    const char *platform;              /* _ARGO: integer */
};

struct zpn_version_ack {               /* _ARGO: object_definition */
    const char *error;                 /* _ARGO: string */
};


struct zpn_client_state {               /* _ARGO: object_definition */
    const char *client_state;           /* _ARGO: string */
};

static inline const char* zpn_rpc_conn_mode_str(int conn_mode)
{
    if (conn_mode == 1) {
        return "PASSIVE";
    } else if (conn_mode == 0) {
        return "ACTIVE";
    }
    return "-"; // When showing c_state then we don't want to show any role
}

/*
 * Simple SAML authentication.
 *
 * If the client has no assertion/fingerprint to authenticate, you can
 * attempt to authenticate with these fields NULL, and it is up to the
 * service configuration whether to trust you or not...
 *
 * Since ALL clients have certificates, you had best send a HW
 * fingerprint, since all certificates are pinned to a HW
 * fingerprint...
 *
 * New stuff:
 *
 * capabilities- array of strings indicating client capabilities:
 *    "MTN": Multiple trusted networks support
 *    "ACA": App Complete Acknowledgement will be sent by this client.
 *    Others to be added as needed.
 *
 * hostname- The name the host thinks it is.
 *
 * platform- The platform the host is running. (standard enum, as a
 *           string)
 *
 * states- array of strings indicating client states:
 *    "dr_on"   : Client's current DR state is ON
 *    "dr_off"  : Client's current DR state is OFF.
 *    Others to be added as needed.
 *
 */
struct zpn_client_authenticate {       /* _ARGO: object_definition */
    int64_t id;                        /* _ARGO: integer */
    const char *login_name;            /* _ARGO: string */
    const char *saml_assertion_xml;    /* _ARGO: string */
    const char *saml_assertion_base64; /* _ARGO: string */
    const char *hw_serial_id;          /* _ARGO: string */
    const char *cpu_serial_id;         /* _ARGO: string */
    const char *storage_serial_id;     /* _ARGO: string */
    double latitude;                   /* _ARGO: double */
    double longitude;                  /* _ARGO: double */
    struct argo_inet *priv_ip;         /* _ARGO: inet */
    const char *client_type;           /* _ARGO: string */
    const char **capabilities;         /* _ARGO: string */
    int capabilities_count;            /* _ARGO: quiet, integer, count:capabilities */
    const char *hostname;              /* _ARGO: string */
    const char *platform;              /* _ARGO: string */
    const char **states;               /* _ARGO: string */
    int states_count;                  /* _ARGO: quiet, integer, count:states */

    /* Data from prior connections from this client to the cloud. This
     * information is not to be considered totally trustworthy, but
     * can be used for simple operations like redirect, etc */
    struct argo_inet *prior_priv_ip;   /* _ARGO: inet */
    struct argo_inet *prior_pub_ip;    /* _ARGO: inet */
    char **prior_brokers;              /* _ARGO: string */
    int prior_brokers_count;           /* _ARGO: integer, quiet, count: prior_brokers */

    int hops_count;                    /* _ARGO: integer */
    char *self_hop_type;              /* _ARGO: string */
    char **hop_type;                  /* _ARGO: string */
    int hop_type_count;               /* _ARGO: integer, quiet, count: hop_type */

    int64_t *brks;                     /* _ARGO: integer, stringify */
    int brks_count;                    /* _ARGO: integer, quiet, count: brks */

    int64_t *brks_grp;                 /* _ARGO: integer, stringify */
    int brks_grp_count;                /* _ARGO: integer, quiet, count: brks_grp */

    char **tunnel_ids;                  /* _ARGO: string */
    int tunnel_ids_count;               /* _ARGO: integer, quiet, count: tunnel_ids */

    char **sys_type;                  /* _ARGO: string */
    int sys_type_count;               /* _ARGO: integer, quiet, count: sys_type */

    char **hop_debug;                  /* _ARGO: string */
    int hop_debug_count;               /* _ARGO: integer, quiet, count: hop_debug */

    char *external_device_id;          /* _ARGO: string */

    char *os_version;                  /* _ARGO: string */
    char *manufacturer;                /* _ARGO: string */
    char *device_model;                /* _ARGO: string */

    /* ZCC sends this field in case of partner connection only. This field
     * denotes the domain name of primary tenant. */
    char *primary_login_domain;        /* _ARGO: string */

    /* 0: active(default), 1: passive/probe, other values reserved for future use */
    int connection_mode;               /* _ARGO: integer */

    char *current_alt_cloud;           /* _ARGO: string */
    /*
    *ZIA Inspection: zia user idenity
    */
    int64_t o_user_id;                 /* _ARGO: integer, nozero */
    int64_t o_location_id;             /* _ARGO: integer, nozero */

    /* contains one of:
    *     user name ( email)
    *     location name - if  o_user_id == o_location_id
    *     NULL
    */
    char *o_identity_name;             /* _ARGO: string */

};

struct zpn_client_connection_upgrade {  /* _ARGO: object_definition */
    /* 0: active(default), 1: passive/probe, other values reserved for future use */
    int current_connection_mode;        /* _ARGO: integer */
    int new_connection_mode;            /* _ARGO: integer */
    int request_id;                     /* _ARGO: integer */
};

struct zpn_client_connection_upgrade_response { /* _ARGO: object_definition */
    /* 0: ok, failure otherwise */
    int error;                                  /* _ARGO: integer */
    const char *error_msg;                      /* _ARGO: string */
    int request_id;                             /* _ARGO: integer */
};

struct zpn_client_authenticate_ack {   /* _ARGO: object_definition */
    int64_t id;                        /* _ARGO: integer */
    const char *current_broker;        /* _ARGO: string */
    int64_t broker_type;               /* _ARGO: integer */
    const char *tunnel_id;             /* _ARGO: string */
    const char *error;                 /* _ARGO: string */
    struct argo_inet *pub_ip;          /* _ARGO: inet */
    int64_t saml_not_before;           /* _ARGO: integer */
};

struct zpn_machine_tunnel_client_authenticate {       /* _ARGO: object_definition */
    int64_t id;                                       /* _ARGO: integer */
    const char *hw_serial_id;                         /* _ARGO: string */
    const char *cpu_serial_id;                        /* _ARGO: string */
    const char *storage_serial_id;                    /* _ARGO: string */
    struct argo_inet *priv_ip;                        /* _ARGO: inet */
    const char *hostname;                             /* _ARGO: string */
    const char *platform;                             /* _ARGO: string */
    const char **capabilities;                        /* _ARGO: string */
    int capabilities_count;                           /* _ARGO: quiet, integer, count:capabilities */
    const char **states;                              /* _ARGO: string */
    int states_count;                                 /* _ARGO: quiet, integer, count:states */

    /* Data from prior connections from this machine tunnel client to the cloud. This
     * information is not to be considered totally trustworthy, but
     * can be used for simple operations like redirect, etc */
    struct argo_inet *prior_priv_ip;   /* _ARGO: inet */
    struct argo_inet *prior_pub_ip;    /* _ARGO: inet */
    char **prior_brokers;              /* _ARGO: string */
    int prior_brokers_count;           /* _ARGO: integer, quiet, count: prior_brokers */

    int hops_count;									/* _ARGO: integer */
    char *self_hop_type;							/* _ARGO: string */
    char **hop_type;								/* _ARGO: string */
    int hop_type_count;								/* _ARGO: integer, quiet, count: hop_type */

    int64_t *brks;									/* _ARGO: integer, stringify */
    int brks_count;									/* _ARGO: integer, quiet, count: brks */

    int64_t *brks_grp;								/* _ARGO: integer, stringify */
    int brks_grp_count;								/* _ARGO: integer, quiet, count: brks_grp */

    char **tunnel_ids;								/* _ARGO: string */
    int tunnel_ids_count;							/* _ARGO: integer, quiet, count: tunnel_ids */

    char **sys_type;								/* _ARGO: string */
    int sys_type_count;								/* _ARGO: integer, quiet, count: sys_type */

    char **hop_debug;								/* _ARGO: string */
    int hop_debug_count;							/* _ARGO: integer, quiet, count: hop_debug */

    int64_t downstream_peer_id;                     /* _ARGO: integer */

    char *os_version;                               /* _ARGO: string */
    char *manufacturer;                             /* _ARGO: string */
    char *device_model;                             /* _ARGO: string */

    /* 0: active(default), 1: passive/probe, other values reserved for future use */
    int connection_mode;               /* _ARGO: integer */
    char *current_alt_cloud;                        /* _ARGO: string */

    /*
     *ZIA Inspection: zia user idenity
     */
    int64_t o_user_id;                 /* _ARGO: integer, nozero */
    int64_t o_location_id;             /* _ARGO: integer, nozero */

    /* contains one of:
    *     user name ( email)
    *     location name - if  o_user_id == o_location_id
    *     NULL
    */
    char *o_identity_name;             /* _ARGO: string */

};

struct zpn_machine_tunnel_client_authenticate_ack {   /* _ARGO: object_definition */
    int64_t id;                                       /* _ARGO: integer */
    const char *current_broker;                       /* _ARGO: string */
    const char *tunnel_id;                            /* _ARGO: string */
    const char *error;                                /* _ARGO: string */
    /* error_dup was added for backward compatibilty to drop25 and old PSE releases */
    const char *error_dup;                            /* _ARGO: string */
    int64_t broker_type;                              /* _ARGO: integer */
};

/*
 * Simple trusted peer authentication. (Slogger)
 */
struct zpn_trusted_client_authenticate { /* _ARGO: object_definition */
    int64_t id;                          /* _ARGO: integer */
    int64_t customer_gid;                /* _ARGO: integer */
    const char **capabilities;           /* _ARGO: string */
    int capabilities_count;              /* _ARGO: quiet, integer, count:capabilities */
};
struct zpn_trusted_client_authenticate_ack {  /* _ARGO: object_definition */
    int64_t id;                               /* _ARGO: integer */
    const char *current_broker;               /* _ARGO: string */
    const char *tunnel_id;                    /* _ARGO: string */
    const char *error;                        /* _ARGO: string */
};

/*
 * Exporter trusted peer authentication.
 */
struct zpn_exporter_client_authenticate {     /* _ARGO: object_definition */
    int64_t id;                               /* _ARGO: integer */
    int64_t customer_gid;                     /* _ARGO: integer */
    const char *assertion_base64;             /* _ARGO: string */
    double latitude;                          /* _ARGO: double */
    double longitude;                         /* _ARGO: double */
    struct argo_inet *public_ip;              /* _ARGO: inet */
    struct argo_inet *private_ip;             /* _ARGO: inet */
    const char **capabilities;                /* _ARGO: string */
    int capabilities_count;                   /* _ARGO: quiet, integer, count:capabilities */
    int64_t pra_scope_gid;                    /* _ARGO: integer */
    int is_pra_third_party_login;             /* _ARGO: integer */
};

struct zpn_exporter_client_authenticate_ack { /* _ARGO: object_definition */
    int64_t id;                               /* _ARGO: integer */
    const char *current_broker;               /* _ARGO: string */
    const char *tunnel_id;                    /* _ARGO: string */
    const char *error;                        /* _ARGO: string */
};

/*
 * Browser isolation peer authentication.
 */
struct zpn_bi_client_authenticate {     /* _ARGO: object_definition */
    int64_t id;                               /* _ARGO: integer */
    const char *assertion_base64;             /* _ARGO: string */
    double latitude;                          /* _ARGO: double */
    double longitude;                         /* _ARGO: double */
    struct argo_inet *public_ip;              /* _ARGO: inet */
    struct argo_inet *private_ip;             /* _ARGO: inet */
    const char **capabilities;                /* _ARGO: string */
    int capabilities_count;                   /* _ARGO: quiet, integer, count:capabilities */
};

struct zpn_bi_client_authenticate_ack {     /* _ARGO: object_definition */
    int64_t id;                               /* _ARGO: integer */
    const char *current_broker;               /* _ARGO: string */
    const char *tunnel_id;                    /* _ARGO: string */
    const char *error;                        /* _ARGO: string */
};

/*
 * Authenticating private broker as a client
 */
struct zpn_pbroker_client_authenticate {      /* _ARGO: object_definition */
    int64_t id;                               /* _ARGO: integer */
    const char *assertion_base64;             /* _ARGO: string */
    struct argo_inet *private_ip;             /* _ARGO: inet */
    const char **capabilities;                /* _ARGO: string */
    int capabilities_count;                   /* _ARGO: quiet, integer, count:capabilities */
};

struct zpn_pbroker_client_authenticate_ack {  /* _ARGO: object_definition */
    int64_t id;                               /* _ARGO: integer */
    const char *current_broker;               /* _ARGO: string */
    const char *tunnel_id;                    /* _ARGO: string */
    const char *error;                        /* _ARGO: string */
};

/*
 * PSE uses this one way message to instruct ZCC switch from sitec(PCC) to public cloud
 */
struct zpn_pbroker_client_switch_to_cloud {   /* _ARGO: object_definition */
    int8_t is_cloud_reachable;                /* _ARGO: integer */
    int64_t num_mtunnels;                     /* _ARGO: integer */
};

/*
 * Authenticating edge connector
 */
struct zpn_ec_client_authenticate {           /* _ARGO: object_definition */
    int64_t id;                               /* _ARGO: integer */
    int64_t downstream_peer_id;               /* _ARGO: integer */

    const char *platform;                     /* _ARGO: string */
    const char *cloud_name;                   /* _ARGO: string */
    int64_t orgid;                            /* _ARGO: integer */
    struct argo_inet *private_ip;             /* _ARGO: inet */
    const char **capabilities;                /* _ARGO: string */
    int capabilities_count;                   /* _ARGO: quiet, integer, count:capabilities */
    int hops_count;                           /* _ARGO: integer */
    char *self_hop_type;                      /* _ARGO: string */
    char **hop_type;                          /* _ARGO: string */
    int hop_type_count;                       /* _ARGO: integer, quiet, count: hop_type */
    int64_t o_location_id;                    /* _ARGO: integer */

    int64_t *brks;                            /* _ARGO: integer, stringify */
    int brks_count;                           /* _ARGO: integer, quiet, count: brks */

    int64_t *brks_grp;                        /* _ARGO: integer, stringify */
    int brks_grp_count;                       /* _ARGO: integer, quiet, count: brks_grp */

    char **tunnel_ids;                        /* _ARGO: string */
    int tunnel_ids_count;                     /* _ARGO: integer, quiet, count: tunnel_ids */

    char **sys_type;                          /* _ARGO: string */
    int sys_type_count;                       /* _ARGO: integer, quiet, count: sys_type */

    char **hop_debug;                         /* _ARGO: string */
    int hop_debug_count;                      /* _ARGO: integer, quiet, count: hop_debug */

};


/*
 * Authenticating vdi client
 */
struct zpn_vdi_client_authenticate {       /* _ARGO: object_definition */
    int64_t id;                               /* _ARGO: integer */
    int64_t downstream_peer_id;               /* _ARGO: integer */

    const char *platform;                     /* _ARGO: string */
    const char *cloud_name;                   /* _ARGO: string */
    int64_t orgid;                            /* _ARGO: integer */
    struct argo_inet *private_ip;             /* _ARGO: inet */
    const char **capabilities;                /* _ARGO: string */
    int capabilities_count;                   /* _ARGO: quiet, integer, count:capabilities */
    int hops_count;                           /* _ARGO: integer */
    char *self_hop_type;                      /* _ARGO: string */
    char **hop_type;                          /* _ARGO: string */
    int hop_type_count;                       /* _ARGO: integer, quiet, count: hop_type */
    int64_t o_location_id;                    /* _ARGO: integer */

    int64_t *brks;                            /* _ARGO: integer, stringify */
    int brks_count;                           /* _ARGO: integer, quiet, count: brks */

    int64_t *brks_grp;                        /* _ARGO: integer, stringify */
    int brks_grp_count;                       /* _ARGO: integer, quiet, count: brks_grp */

    char **tunnel_ids;                        /* _ARGO: string */
    int tunnel_ids_count;                     /* _ARGO: integer, quiet, count: tunnel_ids */

    char **sys_type;                          /* _ARGO: string */
    int sys_type_count;                       /* _ARGO: integer, quiet, count: sys_type */

    char **hop_debug;                         /* _ARGO: string */
    int hop_debug_count;                      /* _ARGO: integer, quiet, count: hop_debug */

    const char *username;                     /* _ARGO: string */
    int64_t last_auth_time;                   /* _ARGO: integer */
};

struct zpn_ec_client_authenticate_ack {       /* _ARGO: object_definition */
    int64_t id;                               /* _ARGO: integer */
    int64_t broker_type;                      /* _ARGO: integer */
    const char *current_broker;               /* _ARGO: string */
    const char *tunnel_id;                    /* _ARGO: string */
    const char *error;                        /* _ARGO: string */
};

struct zpn_vdi_client_authenticate_ack {       /* _ARGO: object_definition */
    int64_t id;                               /* _ARGO: integer */
    int64_t broker_type;                      /* _ARGO: integer */
    const char *current_broker;               /* _ARGO: string */
    const char *tunnel_id;                    /* _ARGO: string */
    const char *error;                        /* _ARGO: string */
};
/*
 * Authenticating IP anchoring (SME)
 */
struct zpn_ia_client_authenticate {           /* _ARGO: object_definition */
    int64_t id;                               /* _ARGO: integer */
    const char *cloud_name;                   /* _ARGO: string */
    int64_t orgid;                            /* _ARGO: integer */
    int64_t customer_gid;                     /* _ARGO: integer */
    struct argo_inet *private_ip;             /* _ARGO: inet */
    const char **capabilities;                /* _ARGO: string */
    int capabilities_count;                   /* _ARGO: quiet, integer, count:capabilities */
};

struct zpn_ia_client_authenticate_ack {       /* _ARGO: object_definition */
    int64_t id;                               /* _ARGO: integer */
    int64_t customer_gid;                     /* _ARGO: integer */
    int64_t broker_type;                      /* _ARGO: integer */
    const char *current_broker;               /* _ARGO: string */
    const char *tunnel_id;                    /* _ARGO: string */
    const char *error;                        /* _ARGO: string */
};

/*
 * Authenticating zia inspection tunnel, Broker sends to SME
 */
struct zpn_zins_client_authenticate { /* _ARGO: object_definition */
    int64_t id;                       /* _ARGO: integer */
    int64_t customer_gid;             /* _ARGO: integer */
    const char *tunnel_id;            /* _ARGO: string */
    int64_t orgid;                    /* _ARGO: integer */
    const char **capabilities;        /* _ARGO: string */
    int capabilities_count;           /* _ARGO: quiet, integer, count:capabilities */
    int proxy_mtunnel;                /* _ARGO: integer */
};

/* stats for capturing vdi authentication failures */
struct zpn_broker_vdi_authentication_stats { /* _ARGO: object_definition */
    int64_t username_invalid;                /* _ARGO: integer */
    int64_t no_idp_gid_for_user;             /* _ARGO: integer */
    int64_t no_idp_for_domain;               /* _ARGO: integer */
    int64_t system_error;                    /* _ARGO: integer */
    int64_t scim_disabled;                   /* _ARGO: integer */
    int64_t vdi_disabled;                    /* _ARGO: integer */
    int64_t auth_params_error;               /* _ARGO: integer */
    int64_t scope_error;                     /* _ARGO: integer */
    int64_t orgid_not_configured;            /* _ARGO: integer */
    int64_t branch_info_fail;                /* _ARGO: integer */
    int64_t pb_client_fail;                /* _ARGO: integer */

    // count of auth validation failuresm,
    // it does includes counts from above counters
    int64_t auth_validation_failed;          /* _ARGO: integer */
    int64_t scim_sync_failed;                /* _ARGO: integer */
    int64_t auth_success;                    /* _ARGO: integer */
};

struct zpn_broker_client_latency_probe_stats {  /* _ARGO: object_definition */
    int64_t total_client_conn;                  /* _ARGO: integer */
    int64_t passive_conn;                       /* _ARGO: integer */
    int64_t active_conn;                        /* _ARGO: integer */
    int64_t upgrade_req;                        /* _ARGO: integer */
    int64_t downgrade_req;                      /* _ARGO: integer */
    int64_t upgrade_req_success;                /* _ARGO: integer */
    int64_t upgrade_req_fail;                   /* _ARGO: integer */
    int64_t downgrade_req_success;              /* _ARGO: integer */
    int64_t downgrade_req_fail;                 /* _ARGO: integer */
};

/*
 * Stats related to Server Side Posture
*/
struct zpn_svcp_stats {                                /* _ARGO: object_definition */
    int64_t num_svcp_capability_connect;               /* _ARGO: integer */
    int64_t num_svcp_capability_disconnect;            /* _ARGO: integer */
    int64_t num_svcp_capability_active;                /* _ARGO: integer */
    int64_t num_svcp_responses;                        /* _ARGO: integer */
    int64_t num_svcp_challenge_unknown;                /* _ARGO: integer */
    int64_t num_svcp_challenge_success;                /* _ARGO: integer */
    int64_t num_svcp_challenge_fails;                  /* _ARGO: integer */
    int64_t num_svcp_challenge_delayed;                /* _ARGO: integer */
    int64_t num_svcp_missing_resp;                     /* _ARGO: integer */
    int64_t num_svcp_missing_udid_in_resp;             /* _ARGO: integer */
    int64_t num_svcp_missing_challenge_on_server;      /* _ARGO: integer */
    int64_t num_svcp_no_payload_in_resp;               /* _ARGO: integer */
    int64_t num_svcp_missing_postures_on_server;       /* _ARGO: integer */
    int64_t num_svcp_missing_payload_in_resp;          /* _ARGO: integer */
    int64_t num_svcp_missing_udid_on_server;           /* _ARGO: integer */
    int64_t num_svcp_missing_client_cert_on_resp;      /* _ARGO: integer */
    int64_t num_svcp_missing_signed_challenge_on_resp; /* _ARGO: integer */
    int64_t num_svcp_out_of_order_responses;           /* _ARGO: integer */
    int64_t num_svcp_reauth_callback;                  /* _ARGO: integer */
    int64_t num_svcp_reauth_callback_ignored;          /* _ARGO: integer */
    int64_t num_svcp_callback_ignored;                 /* _ARGO: integer */
    int64_t num_svcp_callback_old_unsupported_client;  /* _ARGO: integer */
    int64_t num_svcp_incorrect_intermediate_cert_on_resp; /* _ARGO: integer */
};

/*
 * Stats related to Stepup Auth
*/
struct zpn_stepup_auth_stats {                                                         /* _ARGO: object_definition */
    int64_t num_stepup_clients_no_als_sent;                                            /* _ARGO: integer */
    int64_t num_stepup_success;                                                        /* _ARGO: integer */
    int64_t num_stepup_failed;                                                         /* _ARGO: integer */
    int64_t num_stepup_failed_due_to_expiry;                                           /* _ARGO: integer */
    int64_t num_stepup_unknown;                                                        /* _ARGO: integer */
    int64_t num_stepup_failed_due_to_invalid_client;                                   /* _ARGO: integer */
    int64_t num_stepup_failed_due_to_feature_disabled;                                 /* _ARGO: integer */
    int64_t num_stepup_mtunnel_al_expired;                                             /* _ARGO: integer */
    int64_t num_stepup_mtunnel_al_changed;                                             /* _ARGO: integer */
    int64_t num_stepup_auth_level_fetch_failed;                                        /* _ARGO: integer */
    int64_t num_stepup_auth_level_mapping_fetch_failed;                                /* _ARGO: integer */
    int64_t num_stepup_zpn_step_up_auth_level_config_updates;                          /* _ARGO: integer */
    int64_t num_stepup_zpn_rule_to_step_up_auth_level_mapping_config_updates;          /* _ARGO: integer */
};

/* zpn client state, currently applicable only for zcc/zapp */
enum zpn_client_conn_mode {
    zpn_client_conn_mode_active = 0,
    zpn_client_conn_mode_passive,
    zpn_client_conn_mode_invalid /* let this be at last */
};

/*
 * zpn_instance_info description
 * type - 0: private_mtn, 1: private, 2: public_private_mtn, 3: public_private, 4: public_primary, 5: public_secondary
 * gid - gid of the broker
 * longitude and latitude - of broker instance
 * distance_km - broker distance from client
 */
struct zpn_instance_info {      /* _ARGO: object_definition */
    int type;                   /* _ARGO: integer */
    int64_t gid;                /* _ARGO: integer */
    double longitude;           /* _ARGO: double */
    double latitude;            /* _ARGO: double */
    uint32_t distance_km;       /* _ARGO: integer */
};
/*
 * SME returns zpn_zins_client_authenticate_ack
 */
struct zpn_zins_client_authenticate_ack { /* _ARGO: object_definition */
    int64_t id;                           /* _ARGO: integer */
    const char *error;                    /* _ARGO: string */

    // SME ip, use is sme identified i.e. can contain port <ip>:<port>
	//  or anything else, logged in auth_log as is
    char *sme_ip;                          /* _ARGO: string */
    const char **capabilities;            /* _ARGO: string */
    int capabilities_count;               /* _ARGO: quiet, integer, count:capabilities */
};

/*
 send by ZCC to Broker, with zia identity, multiple message are ok, as device trust might change
*/
struct zpn_zia_identity {  /* _ARGO: object_definition */
    int64_t id;                 /* _ARGO: integer */
    // broker would send 0 or 1( enable)
    u_int16_t is_inspection;    /* _ARGO: integer */
    int64_t si_org_id;          /* _ARGO: integer */
    // ZIA User ID
    u_int64_t si_user_id;       /* _ARGO: integer */
    // ZIA Device ID
    u_int32_t si_device_id;     /* _ARGO: integer */
    // ZIA Device trust level received from ZCC
    u_int16_t si_device_trust;  /* _ARGO: integer */
    // zia cloud
    const char *zia_cloud;      /* _ARGO: string */
};

/*
 * RPC for the broker to inform the client of better
 * brokers/alternative brokers to use, as well as to inform
 * (optionally) the client that the current broker is going out of
 * service. This allows the client to perform gracefull
 * failover/shutdown of mtunnels.
 *
 * redirect_only: if 1, the current broker is a redirect broker
 * force_redirect: if set, forces the client to perform the redirect immediately.
 * brokers: array of alternate brokers to connect to
 * broker_count: number of brokers in the brokers array
 * shutdown_time: if shutdown is 1, the time broker will be shut down
 * reason: info, see BRK_REDIRECT_REASON_XYZ defined in zpn_lib.h
 *
 * If the client ends up with no brokers available, it should simply
 * reauthenticate to get a new set of brokers to use.
 */
struct zpn_broker_redirect {           /* _ARGO: object_definition */
    int redirect_only;                 /* _ARGO: integer */
    int force_redirect;                /* _ARGO: integer */
    char *brokers[ZPN_CLIENT_MAX_BROKERS]; /* _ARGO: string */
    int broker_count;                  /* _ARGO: integer, quiet, count: brokers */
    int64_t shutdown_time;             /* _ARGO: integer */
    int64_t timestamp_s;               /* _ARGO: integer */
    struct argo_inet *pub_ip;          /* _ARGO: inet */
    const char *reason;                /* _ARGO: string */
    char *sni_suffixes[ZPN_CLIENT_MAX_BROKERS]; /* _ARGO: string */
    int sni_suffixes_count;            /* _ARGO: integer, quiet, count: sni_suffixes */
    char *alt_cloud;                   /* _ARGO: string */
    struct argo_object  *brokers_info[ZPN_CLIENT_MAX_BROKERS];      /* _ARGO: argo_object */
    int64_t broker_info_count;                                      /* _ARGO: integer, count: brokers_info */
    int allow_probe;                                                /* _ARGO: integer */
    char *client_state;                /* _ARGO: string */
    int redirect_source;               /* _ARGO: integer */
};

struct client_state {                  /* _ARGO: object_definition */
    int64_t id;                        /* _ARGO: integer */
    char **trusted_networks;           /* _ARGO: string */
    int trusted_networks_count;        /* _ARGO: quiet, integer, count: trusted_networks */
};

/*
 * RPC for sending application information from broker to client.
 *
 * app_domain is the domain name for which this application
 * response. 'hr.chanak.com' or some such.
 *
 * ingress_ports is an array of host byte order ports that this
 * application expects to see data on. Often port 80 and port 443. A
 * simple array.
 */
struct zpn_client_app {                /* _ARGO: object_definition */
    char *app_domain;                  /* _ARGO: string */
    int32_t *ingress_ports;            /* _ARGO: integer */
    int32_t ports_count;               /* _ARGO: quiet, integer, count: ingress_ports */
    int32_t *ingress_port_ranges;      /* _ARGO: integer */
    int32_t port_ranges_count;         /* _ARGO: quiet, integer, count: ingress_port_ranges */
    int32_t *tcp_port_ranges;          /* _ARGO: integer */
    int32_t tcp_port_ranges_count;     /* _ARGO: quiet, integer, count: tcp_port_ranges */
    int32_t *udp_port_ranges;          /* _ARGO: integer */
    int32_t udp_port_ranges_count;     /* _ARGO: quiet, integer, count: udp_port_ranges */

    int deleted;                       /* _ARGO: integer */
    int bypass;                        /* _ARGO: integer */
    const char *icmp_access_type;      /* _ARGO: string */
    int ip_anchored;                   /* _ARGO: integer, nozero */
    int inspected;                     /* _ARGO: integer, nozero */
    int bypass_on_reauth;              /* _ARGO: integer */
    uint32_t double_encrypt;           /* _ARGO: integer */
    const char *bypass_type;           /* _ARGO: string */
    int64_t app_gid;                   /* _ARGO: integer, nozero */
    int match_style;                   /* _ARGO: integer, nozero */
    uint8_t has_next;                  /* _ARGO: integer */
    /* app scaling for Forwarding Clients */
    struct argo_object **app_segments; /* _ARGO: argo_object, quiet */
    size_t app_segments_count;         /* _ARGO: integer, quiet, count: app_segments*/
};

/*
 * RPC for letting the client know that it has received up-to-date
 * information for all apps. (More apps may arrive asynchronously as
 * adminstrators add/remove them, but the working set is complete as
 * of the arrival of this message)
 */
struct zpn_client_app_complete {       /* _ARGO: object_definition */
    const char *error;                 /* _ARGO: string */
};

/*
 * Sent by client when it has received app_complete. We added this
 * message so that we can detect situations where the client is not
 * successfully receiving all the applicaitons we are sending. This
 * doesn't seem like it should be necessary, but experience has shown
 * this is a good thing to be able to track/monitor.
 *
 * A client that sends this message will have a capabilities entry of
 * "ACA"
 */
struct zpn_client_app_complete_ack {   /* _XXARGO: object_definition */
    const char *error;                 /* _XXARGO: string */
};

/*
 * RPC for sending DNS search suffix information from broker to
 * client. These are NOT in the form of wildcards.
 *
 */
struct zpn_client_search_domain {      /* _ARGO: object_definition */
    const char *domain;                /* _ARGO: string */
    int capture;                       /* _ARGO: integer, nozero */
    int deleted;                       /* _ARGO: integer */
};

/*
 * RPC for letting the client know that it has received up-to-date
 * information for all apps. (More apps may arrive asynchronously as
 * adminstrators add/remove them, but the working set is complete as
 * of the arrival of this message)
 *
 * all_domains and capture are arrays representing the sum of all
 * search domains sent to the client. (Can be used by more modern
 * clients for easier search domain handling)
 */
struct zpn_client_search_domain_complete {  /* _ARGO: object_definition */
    char **all_domains;                     /* _ARGO: string */
    int all_domains_count;                  /* _ARGO: integer, quiet, count:all_domains */
    int *capture;                           /* _ARGO: integer */
    int capture_count;                      /* _ARGO: integer, quiet, count:capture */
    const char *error;                      /* _ARGO: string */
};

/*
 * This RPC is sent to client a short time after search domain
 * configuration changes. It comprises the state of search suffic
 * configuration when sent
 */
struct zpn_client_search_domain_all      {  /* _ARGO: object_definition */
    char **all_domains;                     /* _ARGO: string */
    int all_domains_count;                  /* _ARGO: integer, quiet, count:all_domains */
    int *capture;                           /* _ARGO: integer */
    int capture_count;                      /* _ARGO: integer, quiet, count:capture */
    const char *error;                      /* _ARGO: string */
};

/*
 * RPC to send the aggregated domains
 *  (TLD+1 domains) in case of no_ip_download
 *           and no_domain_download capability
 *
 * @include_domain_list :- List of aggregated domains
 * @include_domain_list_cnt :- Number of aggregated domains
 *
 * To let argo know how many elements are in include_domain_list, use the count:include_domain_list parameter
 *
 */
struct zpn_client_aggregated_domains {                                      /* _ARGO: object_definition */
    int include_domain_list_cnt;                                            /* _ARGO: integer, count:include_domain_list */
    char **include_domain_list;                                             /* _ARGO: string */
};

/*
 * RPC for letting the client know config has updated so
 * client can flush their cache
 */
struct zpn_client_config_updated {       /* _ARGO: object_definition */
    const char *error;                   /* _ARGO: string */
};

/*
 * RPCs for communicating postures between client and broker.
 */
/*
 * zpn_posture_profile_start : Sent from client to broker. Drop/ignore
 *    all posture information received before this message. The
 *    'unused' field is not used- it is there just to make the
 *    structure non-zero-length.
 */
struct zpn_posture_profile_start {     /* _ARGO: object_definition */
    int unused;                        /* _ARGO: integer */
};

/*
 * zpn_posture_profile : Sent from client to broker. A single posture
 *    profile. The ID is a simple 64 bit integer, specified by mobile
 *    admin. The 'matches' field will be 0 for lack of match, 1 for
 *    successful match.
 */
struct zpn_posture_profile {           /* _ARGO: object_definition */
    char *id_str;                      /* _ARGO: string */
    int64_t matches;                   /* _ARGO: integer */
};

/*
 * zpn_posture_profile_ack : Sent from broker to client, in response
 *    to each posture profile received. The 'error' field will be
 *    NULL/Nonexistent if no error occurred, otherwise will be filled
 *    with a reason string for an error.
 */
struct zpn_posture_profile_ack {       /* _ARGO: object_definition */
    const char *id_str;                /* _ARGO: string */
    const char *error;                 /* _ARGO: string */
};

/*
 * zpn_server_validated_cert_posture_check_request : Sent from broker
 *     to client.
 *     The 'challenge' is a 32-byte printable ASCII string.
 */
struct zpn_server_validated_cert_posture_check_request {    /* _ARGO: object_definition */
    const char *posture_udid;                               /* _ARGO: string */
    const char *root_certificate;                           /* _ARGO: string */
    const char *challenge;                                  /* _ARGO: string */
    int non_exportable_private_key;                         /* _ARGO: integer */
};

/*
 * zpn_server_validated_cert_posture_check_response: Sent from client
 *     to broker.
 *     The 'signed_challenge' is a BASE-64 encoded string.
 */
struct zpn_server_validated_cert_posture_check_response_payload {   /* _ARGO: object_definition */
    char *client_certificate;                                 /* _ARGO: string */
    char **intermediate_certificates;                         /* _ARGO: string */
    int intermediate_certificates_count;                            /* _ARGO: integer, quiet, count:intermediate_certificates */
    const char *signed_challenge;                                   /* _ARGO: string */
};

struct zpn_server_validated_cert_posture_check_response {    /* _ARGO: object_definition */
    const char *posture_udid;                                /* _ARGO: string */
    struct argo_object *payloads[SVCP_MAX_PAYLOADS];         /* _ARGO: argo_object */
    int payloads_count;                                      /* _ARGO: integer, quiet, count:payloads */
    int error_code;                                          /* _ARGO: integer */
};

/*
 * zpn_server_validated_cert_posture_check_done: Send from broker
 *     to client.
 */

struct zpn_server_validated_cert_posture_check_done {    /* _ARGO: object_definition */
    const char *posture_udid;                            /* _ARGO: string */
    const char *error;                                   /* _ARGO: string */
};


/*
 * PSE sends this message to the public broker after validating the posture, the public broker will store the result in the c_state
 * and use it for posture related policy validation for the promoted two hop mtunnels.
 */
struct zpn_svcp_pb_posture_profile {    /* _ARGO: object_definition */
    char *posture_udid;                 /* _ARGO: string */
    char *policy_id_str;                /* _ARGO: string */
    int is_verification_passed;         /* _ARGO: integer */
    int is_first_posture;               /* _ARGO: integer */
    int clear_sv_postures;              /* _ARGO: integer */
    int is_last_posture;                /* _ARGO: integer */
};

/*
 * RPC for client to tell us the set of trusted networks that it has
 * detected.
 *
 * If no trusted networks are detected, can send either a zero length
 * array, or send an empty object. However, it is required to send
 * this field if the client indicates "MTN" as part of its
 * capabilities.
 */
struct zpn_trusted_networks {          /* _ARGO: object_definition */
    int64_t id;                        /* _ARGO: integer */
    char **trusted_networks;           /* _ARGO: string */
    int trusted_networks_count;        /* _ARGO: quiet, integer, count: trusted_networks */
};

struct zpn_trusted_networks_ack {      /* _ARGO: object_definition */
    int64_t id;                        /* _ARGO: integer */
};

/*
 * Client request for a mtunnel, by application name. The client is
 * expected to choose an unused tag_id...
 *
 * It is possible (probably) to receive an mtunnel_end message for a
 * failed request.
 */
struct zpn_mtunnel_request {           /* _ARGO: object_definition */
    int32_t tag_id;                    /* _ARGO: integer */
    char *app_name;                    /* _ARGO: string */
    const char *app_type;              /* _ARGO: string */
    int32_t tcp_server_port;           /* _ARGO: integer */
    uint32_t double_encrypt;           /* _ARGO: integer */
    uint32_t zpn_probe_type;           /* _ARGO: integer */
    uint16_t ip_protocol;              /* _ARGO: integer */
    uint16_t server_port;              /* _ARGO: integer */
    int64_t publish_gid;               /* _ARGO: integer */
    int64_t *server_group_gid;         /* _ARGO: integer */
    int server_group_gid_count;        /* _ARGO: integer, quiet, count: server_group_gid */

    /*
     * The following fields all come in the case of ip anchoring or
     * edge connector (and maybe others in the future) The system
     * trusts these from the client, so if policy is configured using
     * their information (which may occur in the future) then
     * client_type should be used to have some assurance these fields
     * are trustworthy.
     *
     * o_ = Original- came from the client's view of the world.
     * 2023 - ZCC should send these in case of ZIA inspection: o_user_id or o_location_id and o_identity_name
     */
    int64_t o_user_id;                 /* _ARGO: integer */
    int64_t o_location_id;             /* _ARGO: integer */
    // parent location id.
    int64_t o_plocation_id;            /* _ARGO: integer, nozero */
    struct argo_inet *o_sip;           /* _ARGO: inet */
    struct argo_inet *o_dip;           /* _ARGO: inet */
    int32_t o_sport;                   /* _ARGO: integer */
    int32_t o_dport;                   /* _ARGO: integer */
    int32_t allow_all_xport;          /* _ARGO: integer */
    int64_t app_gid;                  /* _ARGO: integer */
    /* contains one of:
    *     user name ( email)
    *     location name - if  o_user_id == o_location_id
    *     NULL
    */
    char *o_identity_name;             /* _ARGO: string */

    // ZIA Inspection
    const char *origin_mtunnel_id;    /* _ARGO: string */
    // public IP of the ZCC/ CC/ BC
    struct argo_inet *o_public_ip;           /* _ARGO: inet */

    // ZIA Device ID
    u_int32_t si_device_id;           /* _ARGO: integer, nozero */
    // ZIA Device trust level received from ZCC
    u_int16_t si_device_trust;        /* _ARGO: integer, nozero */

    uint32_t *o_dwgs;                 /* _ARGO: integer */
    int o_dwgs_count;                 /* _ARGO: integer, quiet, count:o_dwgs */

    /*Chrome Enterprise Browser*/
    const char *g_success;                            /* _ARGO: string */
    const char *g_browser_version;                    /* _ARGO: string */
    const char *g_key_trust_level;                    /* _ARGO: string */
    const char *g_operating_system;                   /* _ARGO: string */
    const char *g_disk_encryption;                    /* _ARGO: string */
    const char *g_os_firewall;                        /* _ARGO: string */
    const char *g_screen_lock_secured;                /* _ARGO: string */
    const char *g_safe_browsing_protection_level;     /* _ARGO: string */
    const char *g_crowd_strike_agent;                 /* _ARGO: string */

    /* Chrome Enterprise Browser v2 */
    int64_t *gprofile_gids;                           /* _ARGO: integer */
    int gprofile_gid_count;                           /* _ARGO: integer, quiet, count: gprofile_gids */
    int managed_browser_payload_version;              /* _ARGO: integer */

    const char *gd_cookie_domain_id;                  /* _ARGO: string */
};


/*
 * Internal Client (such as private broker on behalf of real client) mtunnel request
 * Request contains extra fields for relaying downstream hops information to the next upstream hop
 */
struct zpn_mtunnel_request_int {       /* _ARGO: object_definition */
    int32_t tag_id;                    /* _ARGO: integer */
    char *app_name;                    /* _ARGO: string */
    const char *app_type;              /* _ARGO: string */
    int32_t tcp_server_port;           /* _ARGO: integer */
    uint32_t double_encrypt;           /* _ARGO: integer */
    uint32_t zpn_probe_type;           /* _ARGO: integer */
    uint16_t ip_protocol;              /* _ARGO: integer */
    uint16_t server_port;              /* _ARGO: integer */
    int64_t publish_gid;               /* _ARGO: integer */
    int64_t *server_group_gid;         /* _ARGO: integer */
    int server_group_gid_count;        /* _ARGO: integer, quiet, count: server_group_gid */

    /*
     * The following fields all come in the case of ip anchoring or
     * edge connector (and maybe others in the future) The system
     * trusts these from the client, so if policy is configured using
     * their information (which may occur in the future) then
     * client_type should be used to have some assurance these fields
     * are trustworthy.
     *
     * o_ = Original- came from the client's view of the world.
     */
    int64_t o_user_id;                 /* _ARGO: integer */
    int64_t o_location_id;             /* _ARGO: integer */
    struct argo_inet *o_sip;           /* _ARGO: inet */
    struct argo_inet *o_dip;           /* _ARGO: inet */
    int32_t o_sport;                   /* _ARGO: integer */
    int32_t o_dport;                   /* _ARGO: integer */

    /* contains one of:
    *     user name ( email)
    *     location name - if  o_user_id == o_location_id
    *     NULL
    */
    char *o_identity_name;             /* _ARGO: string */

    /* Multi-hop mtunnel tracking fields */
    /**************************************************************************/

    int hops_count;                                         /* _ARGO: integer */

    /* Hop type may be: "ORIG", "TRANSIT", "TERM" */
    char **hop_type;                                        /* _ARGO: string */
    int hop_type_count;                                     /* _ARGO: integer, quiet, count: hop_type */

    /* mtunnel ids */
    char **mtunnel_ids;                                     /* _ARGO: string */
    int mtunnel_ids_count;                                  /* _ARGO: integer, quiet, count: mtunnel_ids */

    /* broker gids */
    int64_t *brks;                                          /* _ARGO: integer */
    int brks_count;                                         /* _ARGO: integer, quiet, count: brks */

    /* broker group gids */
    int64_t *brks_grp;                                      /* _ARGO: integer */
    int brks_grp_count;                                     /* _ARGO: integer, quiet, count: brks_grp */

    struct argo_inet *hop_pub_ips;                          /* _ARGO: inet */
    int hop_pub_ips_count;                                  /* _ARGO: integer, quiet, count: hop_pub_ips */

    char **c_tlv_types;                                     /* _ARGO: string */
    int c_tlv_types_count;                                  /* _ARGO: integer, quite, count: c_tlv_types */

    /* start rx times */
    int64_t orig_start_rx_us;                               /* _ARGO: integer */
    int64_t *start_rx_times_us;                             /* _ARGO: integer */
    int start_rx_times_us_count;                            /* _ARGO: integer, quiet, count: start_rx_times_us */

    /* end times */
    int64_t *end_times_us;                                  /* _ARGO: integer */
    int end_times_us_count;                                 /* _ARGO: integer, quiet, count: end_times_us */

    /* system type may be: "SERVICE_EDGE", "BROKER" */
    char **sys_type;                                        /* _ARGO: string */
    int sys_type_count;                                     /* _ARGO: integer, quiet, count: sys_type */

    /**************************************************************************/

    int32_t allow_all_xport;                               /* _ARGO: integer */
    int64_t app_gid;                                       /* _ARGO: integer */

    // ZIA Inspection
    // ZIA Device ID
    u_int32_t si_device_id;           /* _ARGO: integer, nozero */
    // ZIA Device trust level received from ZCC
    u_int16_t si_device_trust;        /* _ARGO: integer, nozero */

    uint32_t *o_dwgs;                 /* _ARGO: integer */
    int o_dwgs_count;                 /* _ARGO: integer, quiet, count:o_dwgs */
};


struct zpn_mtunnel_request_ack {       /* _ARGO: object_definition */
    int32_t tag_id;                    /* _ARGO: integer */
    int a_port;                        /* _ARGO: integer, nozero */
    int s_port;                        /* _ARGO: integer, nozero */
    struct argo_inet *a_ip;            /* _ARGO: inet, nozero */
    struct argo_inet *s_ip;            /* _ARGO: inet, nozero */
    const char *mtunnel_id;            /* _ARGO: string */
    const char *error;                 /* _ARGO: string */
    int err_code;                      /* _ARGO: integer */
    const char *reason;                /* _ARGO: string */
    int32_t allow_all_xport;           /* _ARGO: integer */
    int64_t reauth_timeout_s;          /* _ARGO: integer */

    // send back to SME for logs, as it came from zpn_zins_client_authenticate_ack
    char *sme_ip;                      /* _ARGO: string */

    // Supporting StepUp-Auth. This will refer to the level-ID
    const char *stepup_auth_level_id;  /* _ARGO: string */
};


/*
 * Request a new mtunnel binding from a broker. This is used by
 * assistants to get a tag_id to use for a mtunnel back to a
 * broker. It is not used by clients, as they cannot choose a
 * mtunnel_id. (And worth noting that the assistant isn't choosing a
 * mtunnel_id- it is just referring to the one which it was passed )
 * server_us - Time it took assistant to set up server connection
 *
 * brk_req_s_inet - the s_inet set by dispatcher and sent to connector via brk_req. This is used by broker when it wants
 * to cache dispatcher's path cache.
 * s_inet - the server ip that connector choose (it can override dispatcher's command). This is what is shown in
 * transaction log.
 *
 * brk_req_dsp_tx_us - time at which dispatcher sent BrkRq
 * brk_req_ast_rx_us - time at which assistant received BrkRq from the control broker.
 * bfw_us - time at which control broker (aka forwarding broker) forwarded BrkRq
 * ast_tx_us - time at which connector converted BrkRq to Bind and sent to user broker (aka data broker)
 * g_app - application gid (to help broker build the path cache)
 * g_ast_grp - assistant group (to help broker build the path cache)
 * g_app_grp - application group (to help broker build the path cache)
 * g_srv_grp - server group (to help broker build the path cache)
 * path_decision - collection of all the decision points leading to the path selection. Please take a look at zpn_lib.h
 * for all the list of path decisions. Its a macro starting with ZPN_TX_PATH_DECISION*
 * dsp_bypassed - the zpn_broker_request which is the cause of this bind msg was sent directly from ubrk (bypassing
 * dsp). This doesn't mean this bind message has bypassed the dsp, it could very well travel via dsp.
 */
struct zpn_mtunnel_bind {              /* _ARGO: object_definition */
    const char *mtunnel_id;            /* _ARGO: string */
    int64_t g_aps;                     /* _ARGO: integer */
    int64_t g_bfw;                     /* _ARGO: integer */
    int64_t brk_req_dsp_tx_us;         /* _ARGO: integer */
    int64_t bfw_us;                    /* _ARGO: integer */
    int64_t brk_req_ast_rx_us;         /* _ARGO: integer */
    int64_t ast_tx_us;                 /* _ARGO: integer */
    int32_t server_us;                 /* _ARGO: integer */
    struct argo_inet brk_req_s_inet;   /* _ARGO: inet */
    struct argo_inet s_inet;           /* _ARGO: inet */
    int32_t s_port;                    /* _ARGO: integer */
    struct argo_inet a_inet;           /* _ARGO: inet */
    int32_t a_port;                    /* _ARGO: integer */
    int64_t g_app;                     /* _ARGO: integer */
    int64_t g_ast_grp;                 /* _ARGO: integer */
    int64_t g_app_grp;                 /* _ARGO: integer */
    int64_t g_srv_grp;                 /* _ARGO: integer */
    int64_t g_dsp;                     /* _ARGO: integer */
    uint64_t path_decision;            /* _ARGO: integer */
    int8_t dsp_bypassed;               /* _ARGO: integer */
    uint8_t insp_status;               /* _ARGO: integer */
    uint64_t ssl_err;                  /* _ARGO: integer */
    // from ZVPN
    int64_t zia_tunnel_id;              /* _ARGO: integer, nozero */
    int64_t zia_instance_id;            /* _ARGO: integer, nozero  */
    uint64_t zia_instance_type;         /* _ARGO: integer, nozero  */
};


/*
 * User broker (data broker) will send this message to the connector to indicate that the BIND message is accepted
 * and the mtunnel is ready for the next stage.
 * ubrk_tx_us - time at which the user broker sent this message.
 */
struct zpn_mtunnel_bind_ack {          /* _ARGO: object_definition */
    const char *mtunnel_id;            /* _ARGO: string */
    int32_t tag_id;                    /* _ARGO: integer */
    const char *error;                 /* _ARGO: string */
    int32_t err_code;                  /* _ARGO: integer, nozero */
    int64_t ubrk_tx_us;                /* _ARGO: integer */
};


/*
 * Pause/Resume an mtunnel, by tag.  This is by tag rather than by
 * mtunnel_id because it's a rather low-level pause/resume mechanism,
 * where mtunnel_id's aren't necessarily known.
 *
 * The sender of this message is expecting the receiver to stop
 * sending data on the specified tag, until the receiver subsequently
 * receives a resume.
 */
struct zpn_mtunnel_tag_pause {         /* _ARGO: object_definition */
    int32_t tag_id;                    /* _ARGO: integer */
};
struct zpn_mtunnel_tag_resume {        /* _ARGO: object_definition */
    int32_t tag_id;                    /* _ARGO: integer */
};


/*
 * Terminate a mtunnel. (Can be originated in either direction or
 * simultaneously) mtunnel_id is optional, but usually present.
 *
 * This is the standard mtunnel termination indicator. (connection
 * going away) Once sent, the mtunnel_id and (if present) the
 * request_id will become invalid, and all associated state will be
 * torn down.
 *
 * mtunnel_id is used to reference mtunnels in all systems except
 * client. If mtunnel_id is not available, then...
 *
 * request_id is used to reference mtunnels. If request_id is not
 * available, then...
 *
 * tag_id can be used.
 *
 * Note that a zero request_id or tag_id is an indication that thase
 * fields are invalid.
 *
 * error is used to convey an error condition that occurred, if there
 * was an error. (Tunnels end without error all the time...)
 *
 * drop_data: drop all buffered data and close immediately
 */
struct zpn_mtunnel_end {               /* _ARGO: object_definition */
    const char *mtunnel_id;            /* _ARGO: string */
    int32_t tag_id;                    /* _ARGO: integer, nozero */
    const char *error;                 /* _ARGO: string */
    int err_code;                      /* _ARGO: integer */
    int32_t drop_data;                 /* _ARGO: integer */
    // ZIA Inspection, SME can close mtunnel in 3 general cases:
    //     0 - normal closure, there is no error
    //     1 - blocked by inspection. Error/err_code would have a specific reason. Broker would fail_close
    //     2 - internal system problem. Error/err_code would have a specific reason. Broker might attempt fail_open
    int32_t zia_inspection_res;     /* _ARGO: integer, nozero */
};

enum zpn_waf_inspection_type {
    zpn_waf_inspt_none     = 0,
    zpn_waf_inspt_all      = 1,
};

/*
 * Request a mtunnel- Originated by user_broker. Modified by dispatcher.
 * Received on behalf of assistant by control_broker. Then forwarded to the connector.
 * On successful dispatching, this is forwarded ot control_broker by dispatcher; on failed dispatching, it is returned
 * back to user_broker with error string set.
 *
 * Acknowledgement(bind) must get from connector to the user_broker
 * Negative acknowledgement(zpn_broker_request_ack) will go from connector->control_broker->dispatcher->user_broker
 *
 * mtunnel_id - Identifier for the mtunnel being created.
 *
 * g_app - The requested application.
 *
 * g_aps - Application server - Filled in by dispatcher when the
 *     dispatcher chooses an assistant/app_server to use to satisfy
 *     the mtunnel.
 *
 * g_ast - Assistant - Filled in by dispatcher when the dispatcher chooses
 *     an assistant/app_server to use to satisfy the mtunnel.
 *
 * g_brk- The broker who is making the request on the client's behalf.
 *
 * g_bfw- The broker who forwards the request from dispatcher to
 *     assistant.
 *
 * g_dsp - The dispatcher the broker used to load balance the tunnel.
 *
 * g_loc - The location ID from which the request was made. (Often
 *     doesn't exist)
 *
 * g_app_grp - The application group of the server to use.
 *
 * g_ast_grp - The assistant group of the server to use.
 *
 * g_srv_grp - The server group of the server to use.
 *
 * g_ins_profile - The gid of the inspection profile to use.
 *
 * g_ins_type - see enum zpn_waf_inspection_type for values.
 *
 * srv_ip - The IP address of the server to use.
 *
 * brk_name - The resolvable domain name of the broker the client is
 *     connected to.
 *
 * bfw_us - Timestamp applied when broker forwards message from
 *     dispatcher to assistant.
 *
 * c_uid - The client user ID making the original request. uid is
 *     generic 'text' and is simply the authenticated (via SAML/Cert)
 *     user name.
 *
 * c_pub_ip - The public IP address of the client making the original
 *     request.
 *
 * c_priv_ip - The private IP address of the client making the
 *     original request.
 *
 * c_lat - The client latitude. (geo-ip)
 *
 * c_lon - The client longitude. (geo-ip)
 *
 * c_cc - The client country code. (geo-ip)
 *
 * c_port - The port (TCP) the client requested access to.
 *
 * cfg_lb_alg - The load balancing algorithm to use.
 *
 * cfg_idle_s - The idle timeout (in seconds) to use for this
 *     connection. Zero/Non-existent means no idle_timeout. This will
 *     either be the default value for the app_id or some non-default
 *     value based on user/role/rule
 *
 * cfg_age_s - The maximum age (in seconds) this connection is allowed
 *     to live, regardless of whether or not it is idle.
 *     Zero/non-existent means it can last forever. This will either
 *     be the default value for the app_id, or some non-default value
 *     based on user/role/rule.
 *
 * error - If set, then the request is in error, and should be
 *     propagated back to the originating broker.
 *
 * tun - The mtunnel has internal end to end tunnel (ssl)
 *
 * path_decision - collection of all the decision points leading to the path selection
 *
 * zpn_broker_request_ack is seen only in the case of an error.
 *
 * tlv_type: tells connector to use which tlv type for data connection.
 */
struct zpn_broker_request {            /* _ARGO: object_definition */
    /* Created by broker */
    const char *mtunnel_id;            /* _ARGO: string */

    /* Based on requested app: */
    int64_t g_app;                     /* _ARGO: integer */
    int64_t g_aps;                     /* _ARGO: integer */
    int64_t g_ast;                     /* _ARGO: integer */
    int64_t g_brk;                     /* _ARGO: integer */
    int64_t g_bfw;                     /* _ARGO: integer */
    int64_t g_dsp;                     /* _ARGO: integer */
    int64_t g_loc;                     /* _ARGO: integer */
    int64_t g_app_grp;                 /* _ARGO: integer */
    int64_t g_app_domain;              /* _ARGO: integer */
    int64_t g_ast_grp;                 /* _ARGO: integer */
    int64_t g_srv_grp;                 /* _ARGO: integer */
    int64_t g_ins_app;                 /* _ARGO: integer */
    int64_t g_ins_profile;             /* _ARGO: integer,    synonym: InspectionProfile  */
    int64_t g_ins_rule;                /* _ARGO: integer,    synonym: InspectionRule  */
    int     g_ins_type;                /* _ARGO: integer,    synonym: EnumInspectionType */

    struct argo_inet s_ip;             /* _ARGO: inet */

    /* Broker by name, since Asst doesn't know broker ID->Name
     * table */
    char *brk_name;                    /* _ARGO: string */
    /* Control broker name */
    char *bfw_name;                    /* _ARGO: string */

    /* Domain requested. Corresponds to g_app, either by direct match
     * or by wildcard match. */
    char *domain;                      /* _ARGO: string */
    /* Configured Application Domain - Longest prefix match of domain */
    char *app_domain;                  /* _ARGO: string */

    /* The app type requested. Either 'name', 'ip' or 'dns-srv' */
    const char *app_type;              /* _ARGO: string */

    /* Client info: */
    const char *c_uid;                 /* _ARGO: string */
    struct argo_inet *c_pub_ip;        /* _ARGO: inet */
    struct argo_inet *c_priv_ip;       /* _ARGO: inet */
    double c_lat;                      /* _ARGO: double */
    double c_lon;                      /* _ARGO: double */
    const char *c_cc;                  /* _ARGO: string */
    int32_t c_port;                    /* _ARGO: integer */


    /* LB/Conn config */
    const char *cfg_alg;               /* _ARGO: string */
    int32_t cfg_idle_s;                /* _ARGO: integer */
    int32_t cfg_age_s;                 /* _ARGO: integer */

    const char *error;                 /* _ARGO: string */

    uint32_t double_encrypt;           /* _ARGO: integer */
    uint32_t zpn_probe_type;           /* _ARGO: integer */

    uint16_t ip_protocol;              /* _ARGO: integer */

    double b_lat;                      /* _ARGO: double */
    double b_lon;                      /* _ARGO: double */

    int32_t seq_num;                   /* _ARGO: integer */

    const char *req_type;              /* _ARGO: string */

    int64_t *filter_ast_grp;           /* _ARGO: integer */
    int filter_ast_grp_count;          /* _ARGO: integer, quiet, count: filter_ast_grp */

    /* Hint to dispatcher to ignore its caches. Currently 1 means bypass both session and connector caches. Not applicable to connector's sticky caches */
    uint16_t ignore_cache;             /* _ARGO: integer */

    /* data broker (aka user broker) tx_us - inserted by data broker */
    int64_t ubrk_tx_us;                /* _ARGO: integer */
    /* dispatcher tx_us - inserted by dispatcher */
    int64_t dsp_tx_us;                 /* _ARGO: integer */
    /* control broker (aka forwarding broker) tx_us - inserted by control broker */
    int64_t bfw_us;                    /* _ARGO: integer */
    /* Dispatcher will copy whatever is seen in BrkRq and also include its own path selection logic in this variable */
    uint64_t path_decision;            /* _ARGO: integer */
    /* Flag: Sent directly from ubroker to connector(bypassing dispatcher) as ubroker had the cache */
    uint8_t dsp_bypassed;              /* _ARGO: integer */
    int64_t msg_tx_us;                 /* _ARGO: integer */

    int32_t allow_all_xport;           /* _ARGO: integer */

    int32_t tag_id;                    /* _ARGO: integer */
    const char *client_cname;          /* _ARGO: string */

    // icmp_access_type - "PING_TRACEROUTING:2", "PING:1", "NONE: 0"
    uint8_t icmp_access_type;          /* _ARGO: integer */
    uint8_t connector_close_to_app;    /* _ARGO: integer */

    int64_t g_microtenant;             /* _ARGO: integer */

    /*
     * Fields sent by dispatcher to indicate if broker needs to negative cache
     * the failed zpn_broker_request and if yes then for how long (ttl).
     */
    int neg_cache_on_brk;              /* _ARGO: integer */
    int64_t neg_cache_lifetime_us;     /* _ARGO: integer */

    /* zvpn/eas/extranet  fields
      populated on mtunnel request by broker
      only if app segment extranet_enabled is 1
    */
    u_int64_t si_user_id;      /* _ARGO: integer, nozero  */
    u_int32_t si_device_id;    /* _ARGO: integer, nozero  */
    u_int16_t si_device_trust; /* _ARGO: integer, nozero  */

    int64_t zia_org_id;          /* _ARGO: integer, nozero  */
    const char *zia_cloud_name;  /* _ARGO: string */
    // source port from mtunnel request
    int32_t o_port;  /* _ARGO: integer, nozero  */
    /* end of mtunnel request params */

    // set by dispatcher
    int64_t zia_partner_id;     /* _ARGO: integer, nozero  */

    // populated by Dispatcher, broker sets to 0
    int64_t zia_location_id;    /* _ARGO: integer, nozero  */
    int64_t zia_instance_id;    /* _ARGO: integer, nozero  */
    int64_t zia_tunnel_id;      /* _ARGO: integer, nozero  */

    int64_t *filter_locs;           /* _ARGO: integer */
    int filter_locs_count;          /* _ARGO: integer, quiet, count: filter_locs */

    //dispatcher expects count to be populated hence count is not quiet
    int64_t *g_apps;                   /* _ARGO: integer */
    int g_apps_count;                  /* _ARGO: integer, count: g_apps */

    /*
     * Reason for re-dispatch. Possible values: NO_CONNECTOR_AVAILABLE, INVALID_DOMAIN,
     * APP_IN_LEARN_MODE, STICKY_CHANGED, TIMEOUT or NONE (for fresh request).
     * Only there for stats and analytics at dispatcher end.
     */
    char *redispatch_reason;           /* _ARGO: string */

    /*
     * GID of last dispatcher in case of re-dispatch, otherwise 0.
     * Only there for stats and analytics at dispatcher end.
     */
    int64_t last_g_dsp;                /* _ARGO: integer */
};


/*
 * zpn_broker_request_ack is really a negative ACK for zpn_broker_request message. This is send when the mtunnel
 * creation fails.
 *
 * Make sure all the fields that we sent out of zpn_mtunnel_bind are sent here. zpn_mtunnel_bind is sent when a
 * tunnel is successfully setup, zpn_broker_request_ack is set when the bind failed. All these information will be
 * used to do transaction logging by broker.
 *
 * g_brk- The broker who is making the request on the client's behalf - data broker (aka user broker)
 * g_bfw- The broker who forwards the request from dispatcher to assistant - control broker (aka forwarding broker)
 * g_aps -> application server
 * ast_bind_tx_us -> time at which the bind message is sent by the connector.
 * ast_tx_us -> time at which the broker request ack message is sent by the connector.
 * bfw_tx_us -> time at which the forwarding broker (control broker) forwarded the message to dispatcher
 * dsp_tx_us -> time at which the dispatcher forwarded the message to the user broker (data broker)
 * a_inet & a_port -> assistant inet & port
 * path_decision - collection of all the decision points leading to the path selection
 * dsp_bypassed - the zpn_broker_request which is the cause of this ack msg was sent directly from ubrk (bypassing
 * dsp). This doesn't mean this ack message has bypassed the dsp, it could very well travel via dsp back to ubrk.
 */
struct zpn_broker_request_ack {        /* _ARGO: object_definition */
    const char *mtunnel_id;            /* _ARGO: string */

    int64_t g_app;                     /* _ARGO: integer */
    int64_t g_aps;                     /* _ARGO: integer */
    int64_t g_ast;                     /* _ARGO: integer */
    int64_t g_ast_grp;                 /* _ARGO: integer */
    int64_t g_brk;                     /* _ARGO: integer */
    int64_t g_dsp;                     /* _ARGO: integer */
    int64_t g_bfw;                     /* _ARGO: integer */
    int64_t g_srv_grp;                 /* _ARGO: integer */
    int64_t ast_bind_tx_us;            /* _ARGO: integer */
    int64_t ast_tx_us;                 /* _ARGO: integer */
    int64_t bfw_tx_us;                 /* _ARGO: integer */
    int64_t dsp_tx_us;                 /* _ARGO: integer */
    int64_t server_us;                 /* _ARGO: integer */
    struct argo_inet a_inet;           /* _ARGO: inet */
    int32_t a_port;                    /* _ARGO: integer */
    struct argo_inet s_inet;           /* _ARGO: inet */
    int32_t          s_port;           /* _ARGO: integer */
    char *domain;                      /* _ARGO: string */
    const char *app_type;              /* _ARGO: string */
    uint16_t ip_protocol;              /* _ARGO: integer */

    const char *error;                 /* _ARGO: string */
    int32_t err_code;                  /* _ARGO: integer, nozero */

    int32_t seq_num;                   /* _ARGO: integer */
    uint64_t path_decision;            /* _ARGO: integer */
    int8_t dsp_bypassed;               /* _ARGO: integer */
    uint8_t insp_status;               /* _ARGO: integer */
    uint64_t ssl_err;                  /* _ARGO: integer */
};


/*
 * Set of information coming back (immediately) from dispatcher
 * dsp_tx_us - dispatcher sent time
 */
struct zpn_app_route_info {            /* _ARGO: object_definition */
    const char *mtunnel_id;            /* _ARGO: string */

    int64_t g_app;                     /* _ARGO: integer */
    int64_t g_aps;                     /* _ARGO: integer */
    int64_t g_ast;                     /* _ARGO: integer */
    int64_t g_cbrk;                    /* _ARGO: integer */
    char *domain;                      /* _ARGO: string */
    struct argo_inet s_ip;             /* _ARGO: inet */

    int s_port;                        /* _ARGO: integer */

    const char *error;                 /* _ARGO: string */

    int64_t dsp_tx_us;                 /* _ARGO: integer, nozero */

    /* Dispatcher will copy whatever is seen in BrkRq and also include its own path selection logic in this variable */
    uint64_t path_decision;            /* _ARGO: integer */
    const char *app_type;              /* _ARGO: string */
    int64_t g_dsp;                     /* _ARGO: integer */

    /* zvpn/eas/extranet  fields */
    u_int64_t si_user_id;               /* _ARGO: integer, nozero  */
    u_int32_t si_device_id;             /* _ARGO: integer, nozero  */
    u_int16_t si_device_trust;          /* _ARGO: integer, nozero  */

    int64_t zia_org_id;                 /* _ARGO: integer, nozero  */
    const char *zia_cloud_name;         /* _ARGO: string */

    int64_t zia_location_id;            /* _ARGO: integer, nozero  */
    int64_t zia_instance_id;            /* _ARGO: integer, nozero  */

    int64_t zia_tunnel_id;              /* _ARGO: integer, nozero  */
    int64_t zia_partner_id;             /* _ARGO: integer, nozero  */
};

/*
 * TCP connection report when the connection is initially setup. This is sent only ONCE for the lifetime of a
 * connection.
 *
 * This can be sent by either assistant or client to report the
 * statistics of the fohh connection with broker.
 *
 */
struct zpn_tcp_info_report {                  /* _ARGO: object_definition */
    char *version;                            /* _ARGO: string */
    char *platform;                           /* _ARGO: string */
    const char *platform_detail;              /* _ARGO: string */
    const char *runtime_os;                   /* _ARGO: string */
    struct argo_inet priv_ip;                 /* _ARGO: inet */
    int32_t tcpi_snd_mss;                     /* _ARGO: integer */
    int32_t tcpi_rcv_mss;                     /* _ARGO: integer */

    int32_t tcpi_rtt;                         /* _ARGO: integer */
    int32_t tcpi_rttvar;                      /* _ARGO: integer */
    int32_t tcpi_snd_cwnd;                    /* _ARGO: integer */
    int32_t tcpi_advmss;                      /* _ARGO: integer */
    int32_t tcpi_reordering;                  /* _ARGO: integer */
    int32_t tcpi_rcv_rtt;                     /* _ARGO: integer */
    int32_t tcpi_rcv_space;                   /* _ARGO: integer */
    int32_t tcpi_total_retrans;               /* _ARGO: integer */
    int64_t tcpi_thru_put;                    /* _ARGO: integer */
    char *geoip_version;                      /* _ARGO: string */
    char *isp_version;                        /* _ARGO: string */
    uint8_t static_wally_enabled;             /* _ARGO: integer */
    const char *platform_arch;                /* _ARGO: string */
};

/* ZRDT info report */
struct zpn_zrdt_info_report {                  /* _ARGO: object_definition */
    char *version;                            /* _ARGO: string */
    char *platform;                           /* _ARGO: string */
    struct argo_inet priv_ip;                 /* _ARGO: inet */
};

/*
 * Assistant sending its environment details to the cloud.
 * Please see zpn_assistant_status_report when deciding if a field should get it here.
 *    A value of -1 for configured_cpus and available_cpus indicates an invalid value
 *    and occurs when the assistant is unable to obtain the information from the system.
 */
struct zpn_asst_environment_report {        /* _ARGO: object_definition */
    char *sarge_version;                    /* _ARGO: string */
    int32_t configured_cpus;                /* _ARGO: integer */
    int32_t available_cpus;                 /* _ARGO: integer */
    char *guacd_version;                    /* _ARGO: string */
};

// PB static environment report.
struct zpn_pbroker_environment_report {        /* _ARGO: object_definition */
    const char *sarge_version;                 /* _ARGO: string */
};

struct zpn_sitec_environment_report {        /* _ARGO: object_definition */
    const char *sarge_version;                 /* _ARGO: string */
};

/*
 * Assistant sending its current state to control connection(both broker & pbroker) & data connection of public broker.
 * capabilities- array of strings indicating connector capabilities:
 *    "ASC": Sticky cache is available in the connector to switch back and forth between health & no-health applications
 *    "APU": Allows pathing from user broker (in addition to inherent capability to accept from dispatcher)
 */
struct zpn_asst_state {                      /* _ARGO: object_definition */
    const char **capabilities;               /* _ARGO: string */
    int capabilities_count;                  /* _ARGO: quiet, integer, count:capabilities */
    int disabled;                            /* _ARGO: integer */
    /* New fields for QBR */
    const char *public_cloud;                /* _ARGO: string */
    const char *private_cloud;               /* _ARGO: string */
    const char *region_id;                   /* _ARGO: string */
};


/*
 * Assistant sending that a particular control connection has become the master
 */
struct zpn_asst_active_control_connection {     /* _ARGO: object_definition */
    int64_t place_holder;                       /* _ARGO: integer */
};

/*
 * The only health we report at the moment is whether or not the
 * server is alive/dead.
 *
 * s_ip: The server IP address for this health report.
 *
 * s_port: The server port challenged for this health report.
 *
 * domain: The domain queried for this health report.
 *
 * alive: is boolean- 1 for alive and usable, 0 for not alive or
 *    usable.
 *
 * status: Current status of health. Can be one of:
 *
 *    "ready" - Everything normal. Sent only if alive = 1.
 *
 *    "agedout" - Sent if alive = 1 and entry was aged out.
 *
 *    "unreachable" - Sent if alive = 0 and entry is not
 *       reachable. (Health check fails)
 *
 *    "unconfigured" - Sent if alive = 0 and entry was removed because
 *       of configuration change.
 *
 * report - Epoch of the reporting time
 *
 * exires - Epoch after which this health report should no longer be
 *    considered valid.
 *
 * aps_rtt - RTT from assistant to application server, only valid if alive is 1
 *
 * ba_rtt - RTT between broker and assistant
 *
 * tun_ssl - is boolean - 1 if capable of inner ssl tunnel.
 *
 * tr - is trigger reason. Making it really really small variable as today broker->dispatcher encoding is string.
 * With string encoding, the dispatcher have to work hard to parse each fields (think CPU) and we have to be careful
 * because as of early 2019 a typical dispatcher gets 15k messages per second. Once it becomes binary encoding, we can
 * have a pretty large and meaningful name (like trigger_reason).
 *
 * tr_g_dsp - triggered by virtue of this particular g_dsp poking the connector.
 *
 * tr_g_dsp_msg_origin_epoch_s - when did the discovery message which poked the connector actually start from the
 * dispatcher.
 *
 * health log is same as health report, but uses strings for logging
 * in order to protect javascript non-64-bit. expires is left as an
 * integer because that won't wrap for a few years yet... (2038)
 */
struct zpn_health_report {             /* _ARGO: object_definition */
    int64_t g_brk;                     /* _ARGO: integer */
    int64_t g_cst;                     /* _ARGO: integer */
    int64_t g_microtenant;             /* _ARGO: integer */
    int64_t g_app;                     /* _ARGO: integer */
    int64_t g_aps;                     /* _ARGO: integer */
    int64_t g_ast;                     /* _ARGO: integer */
    int64_t g_app_grp;                 /* _ARGO: integer */
    int64_t g_ast_grp;                 /* _ARGO: integer */
    int64_t g_srv_grp;                 /* _ARGO: integer */
    int alive;                         /* _ARGO: integer */
    char *status;                      /* _ARGO: string */
    struct argo_inet s_ip;             /* _ARGO: inet */
    int32_t s_port;                    /* _ARGO: integer */
    char *domain;                      /* _ARGO: string */
    const char *app_type;              /* _ARGO: string */
    int64_t report;                    /* _ARGO: integer */
    int64_t expires;                   /* _ARGO: integer */
    int64_t aps_rtt;                   /* _ARGO: integer */
    int64_t min_aps_rtt;               /* _ARGO: integer */
    int64_t ba_rtt;                    /* _ARGO: integer */
    int tun_ssl;                       /* _ARGO: integer */
    int wildcard;                      /* _ARGO: integer */
    int16_t ip_protocol;               /* _ARGO: integer */

    double a_lat;                      /* _ARGO: double */
    double a_lon;                      /* _ARGO: double */

    char *tr;                          /* _ARGO: string */
    int64_t tr_g_dsp;                  /* _ARGO: integer */
    int64_t tr_g_dsp_msg_origin_epoch_s;        /* _ARGO: integer */

};

#define ASST_RSTR_SESS_CMD                 "Received a session command"
#define ASST_RSTR_HOST_SESS_CMD            "Host received a session command"
#define ASST_RSTR_CERT_EXPIRING            "Certificate is expiring"
#define ASST_RSTR_CERT_REENROLL            "Certificate reenroll due to site config"
#define ASST_RSTR_DEL_ZPA_PORTAL           "Deleted from the ZPA Admin Portal"
#define ASST_RSTR_DR_ON                    "Disaster Recovery Mode was turned on"
#define ASST_RSTR_DC_CONFIG_CON            "Disconnected from the configuration connection"
#define ASST_RSTR_INVAL_PAUSE_CNTR         "Invalid pause state counter"
#define ASST_RSTR_IN_PAUSE_STATE           "Was in a pause state"
#define ASST_RSTR_SUBCMP_FAIL_STR          "Subcomponent failed to start"
#define ASST_RSTR_UPGR_TIME_MIS            "Upgrade time is missing"
#define ASST_RSTR_UPGRADING                "Upgrading"

struct zpn_asst_restart_reason {    /* _ARGO: object_definition */
    int64_t assistant_id;           /* _ARGO: integer */
    char reason[256];               /* _ARGO: string */
    int64_t report_epoch_s;         /* _ARGO: integer */
};

/*
 * RPCs for communicating exporter log data from client to broker
 */
/* To pass exporter app log data */
struct zpn_exporter_log_data {               /* _ARGO: object_definition */
    int32_t tag_id;                     /* _ARGO: integer */
    char *console_user;                 /* _ARGO: string */
    uint32_t console_cred_type;         /* _ARGO: integer */
    int64_t capabilities_policy_id;     /* _ARGO: integer */
    char    *file_transfer_list;        /* _ARGO: string */
    int64_t cred_policy_id;             /* _ARGO: integer */
    char *guac_error_string;            /* _ARGO: string */
    char *console_conn_type;            /* _ARGO: string */
    int32_t is_pra_session;             /* _ARGO: integer */
    char *session_recording;            /* _ARGO: string */
    char *pra_conn_id;                  /* _ARGO: string */
    char *shared_users_list;            /* _ARGO: string */
    char *shared_mode;                  /* _ARGO: string */
    char *shared_user_email;            /* _ARGO: string */
    int32_t shared_user_event;          /* _ARGO: integer */
    char *credential_id;                /* _ARGO: string */
    char *credential_pool_id;           /* _ARGO: string */
};

struct zpn_exporter_log_data_ack {               /* _ARGO: object_definition */
    int32_t tag_id;                         /* _ARGO: integer */
    const char *error;                      /* _ARGO: string */
};

struct zpn_exporter_data_ack {               /* _ARGO: object_definition */
    int32_t tag_id;                         /* _ARGO: integer */
    const char *error;                      /* _ARGO: string */
    int32_t cmd_type;                       /* _ARGO: integer */
    int64_t cmd_data;                       /* _ARGO: integer */
};
struct zpn_managed_chrome_payload {                   /* _ARGO: object_definition */
    const char *success;                              /* _ARGO: string */
    const char *error;                                /* _ARGO: string */
    int64_t creation_time;                            /* _ARGO: integer */
    const char *browser_version;                      /* _ARGO: string */
    const char *key_trust_level;                      /* _ARGO: string */
    const char *operating_system;                     /* _ARGO: string */
    const char *disk_encryption;                      /* _ARGO: string */
    const char *os_firewall;                          /* _ARGO: string */
    const char *screen_lock_secured;                  /* _ARGO: string */
    const char *secure_boot_mode;                     /* _ARGO: string */
    const char *safe_browsing_protection_level;       /* _ARGO: string */
    const char *crowd_strike_agent;                   /* _ARGO: string */
    const char *g_builtin_dns_client_enabled;         /* _ARGO: string */
    const char *g_chrome_remote_desktop_app_blocked;  /* _ARGO: string */
    const char *device_affiliation_ids;               /* _ARGO: string */
    const char *device_enrollment_domain;             /* _ARGO: string */
    const char *device_host_name;                     /* _ARGO: string */
    const char *device_manufacturer;                  /* _ARGO: string */
    const char *device_model;                         /* _ARGO: string */
    const char *disk_encrypted;                       /* _ARGO: string */
    const char *display_name;                         /* _ARGO: string */
    const char *mac_addr;                             /* _ARGO: string */
    const char *os_version;                           /* _ARGO: string */
    const char *password_protection_warning_trigger;  /* _ARGO: string */
    const char *profile_affiliation_ids;              /* _ARGO: string */
    const char *realtime_url_checkMode;               /* _ARGO: string */
    const char *serial_number;                        /* _ARGO: string */
    const char *site_isolation_enabled;               /* _ARGO: string */
    const char *system_dns_servers;                   /* _ARGO: string */
    const char *trigger;                              /* _ARGO: string */
    char gd_cookie_domain_id[MAX_GD_COOKIE_ID_LEN];   /* _ARGO: string */
    const char *mac_addresses;                        /* _ARGO: string */
    int64_t g_cst;                                    /* _ARGO: integer, stringify */
};

struct zpn_managed_browser_profiles {
    int64_t *gprofile_gids;
    int gprofile_gid_count;
    int managed_browser_payload_version;
};

struct zpn_exporter_pra_guac_proxy_data {     /* _ARGO: object_definition */
    int32_t tag_id;                           /* _ARGO: integer */
    char *sess_user;                          /* _ARGO: string */
    char *pra_conn_id;                        /* _ARGO: string */
    int32_t event_type;                       /* _ARGO: integer */
    char *user_email;                         /* _ARGO: string */
    int64_t capabilities_policy_id;           /* _ARGO: integer */
};


/* NOTE: THIS MUST EXACTLY MIRROR zpn_health_report, with the
 * exception of different _ARGO markup */
struct zpn_health_log {                /* _ARGO: object_definition */
    int64_t g_brk;                     /* _ARGO: integer, stringify, nozero */
    int64_t g_cst;                     /* _ARGO: integer, stringify, nozero */
    int64_t g_microtenant;             /* _ARGO: integer, stringify, nozero */
    int64_t g_app;                     /* _ARGO: integer, stringify, nozero */
    int64_t g_aps;                     /* _ARGO: integer, stringify, nozero */
    int64_t g_ast;                     /* _ARGO: integer, stringify, nozero */
    int64_t g_app_grp;                 /* _ARGO: integer, stringify, nozero */
    int64_t g_ast_grp;                 /* _ARGO: integer, stringify, nozero */
    int64_t g_srv_grp;                 /* _ARGO: integer, stringify, nozero */
    int alive;                         /* _ARGO: integer */
    char *status;                      /* _ARGO: string */
    struct argo_inet s_ip;             /* _ARGO: inet */
    int32_t s_port;                    /* _ARGO: integer */
    char *domain;                      /* _ARGO: string */
    const char *app_type;              /* _ARGO: string */
    int64_t report;                    /* _ARGO: integer */
    int64_t expires;                   /* _ARGO: integer */
    int64_t aps_rtt;                   /* _ARGO: integer */
    int64_t min_aps_rtt;                /* _ARGO: integer */
    int64_t ba_rtt;                    /* _ARGO: integer */
    int tun_ssl;                       /* _ARGO: integer */
    int wildcard;                      /* _ARGO: integer */
    int16_t ip_protocol;               /* _ARGO: integer */

    double a_lat;                      /* _ARGO: double */
    double a_lon;                      /* _ARGO: double */

    char *tr;                          /* _ARGO: string */
    int64_t tr_g_dsp;                  /* _ARGO: integer */
    int64_t tr_g_dsp_msg_origin_epoch_s;        /* _ARGO: integer */
};


/*
 * Purpose: connector should send this every few sec (5 seconds today) to teach dispatcher about all the apps that it
 * can serve. It is not ok to just send only during the startup, because there can be dispatchers rebooting and this
 * is the only message through which it learns about all the apps.
 *
 * health_report can take three fields "NONE", "ON-ACCESS" & "CONTINUOUS".
 *
 * learning field is used to determine if the app is on-access(1) or continous(0). We will deprecate this field soon
 * as health_report field now conveys this info too.
 */
struct zpn_app_route_registration {    /* _ARGO: object_definition */
    int64_t g_brk;                     /* _ARGO: integer */
    int64_t g_cst;                     /* _ARGO: integer */
    int64_t g_app;                     /* _ARGO: integer */
    int64_t g_ast;                     /* _ARGO: integer */
    int64_t g_app_grp;                 /* _ARGO: integer */
    int64_t g_ast_grp;                 /* _ARGO: integer */
    int64_t g_srv_grp;                 /* _ARGO: integer */

    int alive;                         /* _ARGO: integer */
    int learning;                      /* _ARGO: integer */
    char *health_report;               /* _ARGO: string */

    int64_t report;                    /* _ARGO: integer */
    int64_t expires;                   /* _ARGO: integer */

    double a_lat;                      /* _ARGO: double */
    double a_lon;                      /* _ARGO: double */
};

/* NOTE: THIS MUST EXACTLY MIRROR zpn_app_route_registration, with the
 * exception of different _ARGO markup */
struct zpn_app_route_log {             /* _ARGO: object_definition */
    int64_t g_brk;                     /* _ARGO: integer, stringify */
    int64_t g_cst;                     /* _ARGO: integer, stringify */
    int64_t g_app;                     /* _ARGO: integer, stringify */
    int64_t g_ast;                     /* _ARGO: integer, stringify */
    int64_t g_app_grp;                 /* _ARGO: integer, stringify */
    int64_t g_ast_grp;                 /* _ARGO: integer, stringify */
    int64_t g_srv_grp;                 /* _ARGO: integer, stringify */

    int alive;                         /* _ARGO: integer */
    int learning;                      /* _ARGO: integer */
    char *health_report;               /* _ARGO: string */

    int64_t report;                    /* _ARGO: integer */
    int64_t expires;                   /* _ARGO: integer */

    double a_lat;                      /* _ARGO: double */
    double a_lon;                      /* _ARGO: double */
};

/*
 * disc_msg_time - the epoch_s on when the dispatcher started the discovery process.
 * cbrk_tx_us    - time at which the control broker forwarded this message.
 */
struct zpn_app_route_discovery {       /* _ARGO: object_definition */
    int64_t g_brk;                     /* _ARGO: integer */
    int64_t g_cst;                     /* _ARGO: integer */
    int64_t g_app;                     /* _ARGO: integer */
    int64_t g_ast;                     /* _ARGO: integer */
    int64_t g_dsp;                     /* _ARGO: integer */
    int64_t g_app_grp;                 /* _ARGO: integer */
    int64_t g_ast_grp;                 /* _ARGO: integer */
    int64_t g_srv_grp;                 /* _ARGO: integer */
    char *disc_type;                   /* _ARGO: string */

    int64_t disc_msg_time;             /* _ARGO: integer */
    int64_t cbrk_tx_us;                /* _ARGO: integer */
    int32_t s_port;                    /* _ARGO: integer */
    char *domain;                      /* _ARGO: string */
    const char *app_type;              /* _ARGO: string */
    int64_t lifetime;                  /* _ARGO: integer */
    int16_t ip_protocol;               /* _ARGO: integer */
};

/* Event logs for customer. */
struct zpn_event_log {
    const char *g_cst;
    const char *type;
    const char *message;

    /* Sometimes present: */
    const char *g_brk;
    const char *g_dsp;
    const char *g_ast;
    const char *g_svr;
    const char *g_app;
    const char *g_usr;
    const char *mtunnel_id;
    const char *vdi;
};

#define FOHH_FLOW_CTRL_SIZE 10
#define FOHH_MCONN_TRACK_RX_DIFF_RX_DATA_HISTOGRAM_MAX 5
#define FOHH_MCONN_TRACK_TX_PEER_RX_DATA_HISTOGRAM_MAX 5

#define FOHH_MCONN_QUEUE_DATA_HISTOGRAM_MAX 4

/*
 * Microtunnel transaction log
 */
struct zpn_trans_log {                          /* _ARGO: object_definition */
    int64_t log_date;                           /* _ARGO: integer, adj_time,         synonym: LogTimestamp */
    const char *tunnel_id;                      /* _ARGO: string,                    synonym: SessionID */
    const char *mtunnel_id;                     /* _ARGO: string,                    synonym: ConnectionID  */

    int64_t *gids;                              /* _ARGO: integer */
    int gids_count;                             /* _ARGO: integer, quiet, count: gids */

    /* Multi-hop mtunnel tracking fields, self hop is our index in hops array */
    /**************************************************************************/
    int fwd_to_next_hop;                                    /* _ARGO: integer, synonym: FwdToNextHop */
    int self_hop;                                           /* _ARGO: integer, synonym: CurrentHopIndex */
    int hops_count;                                         /* _ARGO: integer */

    /* Hop type may be: "ORIG", "TRANSIT", "TERM" */
    char *self_hop_type;                                    /* _ARGO: string, synonym: CurrentHopType */
    char **hop_type;                                        /* _ARGO: string, synonym: HopTypes */
    int hop_type_count;                                     /* _ARGO: integer, quiet, count: hop_type */

    /* mtunnel ids */
    char **mtunnel_ids;                                     /* _ARGO: string, synonym: HopConnectionIDs */
    int mtunnel_ids_count;                                  /* _ARGO: integer, quiet, count: mtunnel_ids */

    /* broker gids */
    int64_t *brks;                                          /* _ARGO: integer, stringify, synonym: HopZENIDs */
    int brks_count;                                         /* _ARGO: integer, quiet, count: brks */

    /* broker group gids */
    int64_t *brks_grp;                                      /* _ARGO: integer, stringify, synonym: HopZENGroupIDs */
    int brks_grp_count;                                     /* _ARGO: integer, quiet, count: brks_grp */

    struct argo_inet *hop_pub_ips;                          /* _ARGO: inet, stringify, synonym: HopPubIPs */
    int hop_pub_ips_count;                                  /* _ARGO: integer, quiet, count: hop_pub_ips */

    char **c_tlv_types;                                     /* _ARGO: string */
    int c_tlv_types_count;                                  /* _ARGO: integer, quite, count: c_tlv_types */

    /* start rx times */
    int64_t orig_start_rx_time_us;                          /* _ARGO: integer, adj_time, synonym: HopInitialConnectionStartTime */
    int64_t *start_rx_times_us;                             /* _ARGO: integer, adj_time, synonym: HopConnectionStartTimes */
    int start_rx_times_us_count;                            /* _ARGO: integer, quiet, count: start_rx_times_us */

    /* end times */
    int64_t *end_times_us;                                  /* _ARGO: integer, adj_time, synonym: HopConnectionEndTimes */
    int end_times_us_count;                                 /* _ARGO: integer, quiet, count: end_times_us */

    /* Local hop end_time_us - start_rx_time_us */
    int64_t local_delta_end_start_us;                       /* _ARGO: integer, synonym: HopDuration */

    /* system type may be: "SERVICE_EDGE", "BROKER" */
    char **sys_type;                                        /* _ARGO: string,  synonym: HopSystemType */
    int sys_type_count;                                     /* _ARGO: integer, quiet, count: sys_type */

    /**************************************************************************/

    /* IDP Information */
    int64_t idp_gid;                            /* _ARGO: integer,                   synonym: Idp */

    /* The tags used for the client/assistant side of the mtunnel */
    int32_t c_tag;                              /* _ARGO: integer */
    int32_t a_tag;                              /* _ARGO: integer */

    const char *domain;                         /* _ARGO: string,                    synonym: Host, synonym: Destination */
    const char *app_type;                       /* _ARGO: string */

    /* Allow/Deny, and reason for closing connection */
    const char *action;                         /* _ARGO: string,                    synonym: ConnectionStatus */

    const char *reason;                         /* _ARGO: string,                    synonym: InternalReason, synonym: ConnectionReason */

    /* GIDs in config database: */
    int64_t g_cst;                              /* _ARGO: integer, stringify,        synonym: Customer */
    int64_t g_microtenant;                      /* _ARGO: integer, stringify, nozero        synonym: MicroTenantID */
    int64_t g_app;                              /* _ARGO: integer, stringify,        synonym: Application */
    int64_t g_app_microtenant;                  /* _ARGO: integer, stringify, nozero       synonym: AppMicroTenantID */
    int64_t *g_apps;                            /* _ARGO: integer, stringify,        synonym: ApplicationSet */
    int64_t g_apps_count;                       /* _ARGO: quiet, integer, count: g_apps */
    int64_t g_aps;                              /* _ARGO: integer, stringify,        synonym: Server */
    int64_t g_ast;                              /* _ARGO: integer, stringify,        synonym: Connector */
    int64_t g_ast_grp;                          /* _ARGO: integer, stringify */
    int64_t g_srv_grp;                          /* _ARGO: integer, stringify */
    int64_t g_brk;                              /* _ARGO: integer, stringify,        synonym: ClientZEN */
    int64_t g_pbrk;                             /* _ARGO: integer, stringify, nozero */
    int64_t g_bfw;                              /* _ARGO: integer, stringify,        synonym: ConnectorZEN */
    int64_t g_dsp;                              /* _ARGO: integer, stringify */
    int64_t *g_dsp_list;                        /* _ARGO: integer, stringify */
    int64_t g_dsp_list_count;                   /* _ARGO: quiet, integer, count: g_dsp_list */
    int64_t g_rul;                              /* _ARGO: integer, stringify,        synonym: Policy, synonym: AccessPolicy */
    /* Rule set and sequence number of access policy: */
    int64_t g_rul_set;                          /* _ARGO: integer, stringify */
    int64_t g_rul_set_seq;                      /* _ARGO: integer, stringify */
    int64_t g_loc;                              /* _ARGO: integer, stringify */
    int64_t g_exp;                              /* _ARGO: integer, stringify,        synonym: Exporter */
    int64_t g_app_grp;                          /* _ARGO: integer, stringify,        synonym: AppGroup */
    int64_t *g_app_grps;                        /* _ARGO: integer, stringify,        synonym: AppGroupSet */
    int64_t g_app_grps_count;                   /* _ARGO: quiet, integer, count: g_app_grps */
    int64_t g_rul_set_rebuild_started_us;       /* _ARGO: integer */
    int64_t g_rul_set_rebuild_finished_us;      /* _ARGO: integer */
    int64_t g_rul_set_last_rebuild_time_us;     /* _ARGO: integer */
    int64_t g_apprl_id;                         /* _ARGO: integer, stringify,        synonym: PRAApprovalID */
    int64_t g_pra_capabilities_policy_id;       /* _ARGO: integer, stringify,        synonym: PRACapabilityPolicyID */
    int64_t g_cred_policy_id;                   /* _ARGO: integer, stringify,        synonym: PRACredentialPolicyID */

    /* Rule set and sequence number of auth policy: */
    int64_t g_auth_rul;							/* _ARGO: integer, stringify,        synonym: ReauthPolicy */
    int64_t g_auth_rul_set;                     /* _ARGO: integer, stringify */
    int64_t g_auth_rul_set_seq;                 /* _ARGO: integer, stringify */
    int64_t g_auth_rul_set_rebuild_started_us;  /* _ARGO: integer */
    int64_t g_auth_rul_set_rebuild_finished_us; /* _ARGO: integer */
    int64_t g_auth_rul_last_rebuild_time_us;    /* _ARGO: integer */

    /* Rule set and sequence number of insp policy: */
    int64_t g_insp_rul;							/* _ARGO: integer, stringify,        synonym: InspectPolicy */
    int64_t g_insp_rul_set;                     /* _ARGO: integer, stringify */
    int64_t g_insp_rul_set_seq;                 /* _ARGO: integer, stringify */
    int64_t g_insp_rul_set_rebuild_started_us;  /* _ARGO: integer */
    int64_t g_insp_rul_set_rebuild_finished_us; /* _ARGO: integer */
    int64_t g_insp_rul_last_rebuild_time_us;    /* _ARGO: integer */
    char insp_status_str[ZPN_TX_INSP_STATUS_STR_LEN];    /* _ARGO: string */

    /* Client info: */
    const char *c_uid;                          /* _ARGO: string,                    synonym: Username */
    struct argo_inet c_pub_ip;                  /* _ARGO: inet,                      synonym: ClientPublicIP */
    struct argo_inet c_priv_ip;                 /* _ARGO: inet,                      synonym: ClientPrivateIP */
    double c_lat;                               /* _ARGO: double,                    synonym: ClientLatitude */
    double c_lon;                               /* _ARGO: double,                    synonym: ClientLongitude */
    const char *c_cc;                           /* _ARGO: string,                    synonym: ClientCountryCode */
    const char *c_city;                         /* _ARGO: string,                    synonym: ClientCity */
    const char *g_vdi;                          /* _ARGO: string */
    const char *platform;                       /* _ARGO: string,                    synonym: Platform */
    const char *hostname;                       /* _ARGO: string,                    synonym: Hostname */
    /* Service port seen by client */
    int32_t c_service_port;                     /* _ARGO: integer,                   synonym: ServicePort */
    char *c_pra_console_user;                   /* _ARGO: string,                    synonym: PRACredentialUserName */
    const char *c_pra_console_login_type;       /* _ARGO: string,                    synonym: PRACredentialLoginType */
    char *c_pra_file_transfer_list;             /* _ARGO: string,                    synonym: PRAFileTransferList */
    char *c_pra_error_string;                   /* _ARGO: string,                    synonym: PRAErrorStatus */
    char *c_pra_console_conn_type;              /* _ARGO: string,                    synonym: PRAConsoleType */
    char *c_pra_sess_type;                      /* _ARGO: string,                    synonym: PRASessionType */
    char *c_pra_conn_id;                        /* _ARGO: string,                    synonym: PRAConnectionID */
    char *c_pra_session_recording;              /* _ARGO: string,                    synonym: PRARecordingStatus */

    /* Assistant info: */
    struct argo_inet s_ip;                      /* _ARGO: inet,                      synonym: ServerIP */
    struct argo_inet a_ip;                      /* _ARGO: inet,                      synonym: ConnectorIP */
    int32_t s_port;                             /* _ARGO: integer,                   synonym: ServerPort */
    int32_t a_port;                             /* _ARGO: integer,                   synonym: ConnectorPort */
    /* New fields for QBR */
    const char *g_ast_city;                     /* _ARGO: string */
    const char *g_ast_country_code;             /* _ARGO: string */
    const char *g_ast_subdivision_code;         /* _ARGO: string */
    const char *g_ast_public_cloud;             /* _ARGO: string */
    const char *g_ast_private_cloud;            /* _ARGO: string */
    const char *g_ast_region_id;                /* _ARGO: string */

    /* Time deltas */
    /* Time to process fetch/process policy: */
    int32_t policy_us;                          /* _ARGO: integer,                   synonym: PolicyProcessingTime, synonym: AccessPolicyProcessingTime */
    /*
     * Time between dispatch request sent by user broker and received by forwarding/control broker. This is zero in
     * case of path cache hit
     */
    int32_t disp_us;                            /* _ARGO: integer,                   synonym: CAProcessingTime */
    /* Time between dispatching and bind from assistant */
    int32_t bind_us;                            /* _ARGO: integer,                   synonym: ConnectorZENSetupTime */
    /* Total time to create tunnel */
    int32_t tunnel_us;                          /* _ARGO: integer,                   synonym: ConnectionSetupTime */
    /* Time it took assistant to set up server connection */
    int32_t server_us;                          /* _ARGO: integer,                   synonym: ServerSetupTime */
    /* Time we spent waiting for Dispatcher to learn */
    int32_t learn_us;                           /* _ARGO: integer,                   synonym: AppLearnTime */

    /* Timestamps */
    /* Request received */
    int64_t startrx_us;                         /* _ARGO: integer, adj_time,         synonym: TimestampConnectionStart */
    /* Data broker(aka user broker) sent this message to dispatcher at this time */
    int64_t disptx_us;                          /* _ARGO: integer, adj_time,         synonym: TimestampCATx */
    /*
     * User broker send brk_req directly to assistant when path cache is HIT. This is time at which that TX is done.
     * User broker TO Assistant
     */
    int64_t brk_req_utoa_tx_us;                 /* _ARGO: integer, nozero */
    /* FIRST dispatch probe tx: */
    int64_t probetx_us;                         /* _ARGO: integer, adj_time */
    /* LAST dispatch probe rx: */
    int64_t proberx_us;                         /* _ARGO: integer, adj_time */
    /* Mtunnel terminated timestamp */
    int64_t end_us;                             /* _ARGO: integer, adj_time,         synonym: TimestampConnectionEnd */
    /* Learn wait start timestamp */
    int64_t learn_start_us;                     /* _ARGO: integer, adj_time,         synonym: TimestampAppLearnStart */
    /*
     * Time at which routeInfo message is sent by the dispatcher, rt_info_tx_us - disptx_us will give the time taken
     * to reach dispatcher
     */
    int64_t rt_info_tx_us;                      /* _ARGO: integer, nozero */
    /*
     * Time at which routeInfo message is received by the user broker (aka data broker) -
     * rt_info_rx_us - rt_info_tx_us  will give the time taken to travel from dsp to user broker
     */
    int64_t rt_info_rx_us;                      /* _ARGO: integer, nozero */
    /*
     * Time at which BrkRq message is sent by dispatcher and  control broker - the values are in bind message. This
     * transforms to bind message when it hits broker and so, continue tracking the time flow in the bind message
     * timestamps.
     * disprx_us == brk_req_bfw_tx_us today, i.e duplicate. disprx_us is a bad field, it is not dispatcher time, but
     * forwarding broker time. We should deprecate that field. This is the first step towards that.
     */
    int64_t brk_req_dsp_tx_us;                  /* _ARGO: integer, nozero */
    int64_t brk_req_bfw_tx_us;                  /* _ARGO: integer, nozero */
    int64_t disprx_us;                          /* _ARGO: integer, adj_time,         synonym: TimestampCARx */
    int64_t brk_req_ast_rx_us;                  /* _ARGO: integer, nozero */

    /*
     * TIme at which BrkRqAck message is sent by connector, control broker, dispatcher and received by user broker
     */
    int64_t brk_req_ack_ast_tx_us;              /* _ARGO: integer, nozero */
    int64_t brk_req_ack_cbrk_tx_us;             /* _ARGO: integer, nozero */
    int64_t brk_req_ack_dsp_tx_us;              /* _ARGO: integer, nozero */
    int64_t brk_req_ack_rx_us;                  /* _ARGO: integer, nozero */

    /*
     * Time at which Bind message is sent by connector and received by user broker
     */
    int64_t bind_ast_tx_us;                     /* _ARGO: integer, nozero */
    int64_t bindrx_us;                          /* _ARGO: integer, adj_time,         synonym: TimestampConnectorZENSetupComplete */


    /* Client stats */
    /* timestamp of first byte received */
    int64_t c_rx_us_b;                          /* _ARGO: integer, adj_time,         synonym: TimestampZENFirstRxClient */
    /* timestamp of first byte transmitted */
    int64_t c_tx_us_b;                          /* _ARGO: integer, adj_time,         synonym: TimestampZENFirstTxClient */
    /* timestamp of most recent byte received */
    int64_t c_rx_us;                            /* _ARGO: integer, adj_time,         synonym: TimestampZENLastRxClient */
    /* timestamp of most recent byte transmitted */
    int64_t c_tx_us;                            /* _ARGO: integer, adj_time,         synonym: TimestampZENLastTxClient */
    /* total bytes received */
    int64_t c_rxbytes;                          /* _ARGO: integer,                   synonym: ZENTotalBytesRxClient */
    /* delta bytes received since last log */
    int64_t c_rxdelta;                          /* _ARGO: integer,                   synonym: ZENBytesRxClient */
    /* total bytes transmitted */
    int64_t c_txbytes;                          /* _ARGO: integer,                   synonym: ZENTotalBytesTxClient */
    /* delta bytes transmitted since last log */
    int64_t c_txdelta;                          /* _ARGO: integer,                   synonym: ZENBytesTxClient */
    /* pause messages transmitted */
    int32_t c_txpause;                          /* _ARGO: integer, nozero */
    /* pause messages received */
    int32_t c_rxpause;                          /* _ARGO: integer, nozero */
    /* pause messages transmitted */
    int32_t c_pauseto;                          /* _ARGO: integer, nozero */
    /* max pause time for receiving data */
    int64_t c_rxpause_time_max_us;                /* _ARGO: integer, nozero */
    /* max pause time for transmitting data */
    int64_t c_txpause_time_max_us;                /* _ARGO: integer, nozero */
    /* epoch time when max rxpause time happened */
    int64_t c_rxpause_time_max_epoch_us;          /* _ARGO: integer, nozero */
    /* epoch time when max txpause time happened */
    int64_t c_txpause_time_max_epoch_us;          /* _ARGO: integer, nozero */
    /* total pause time elapsed for receiving data */
    int64_t c_rxpause_time_total_us;              /* _ARGO: integer, nozero */
    /* total pause time elapsed for transmitting data */
    int64_t c_txpause_time_total_us;              /* _ARGO: integer, nozero */
    /* resume messages transmitted */
    int32_t c_txresume;                         /* _ARGO: integer, nozero */
    /* resume messages received */
    int32_t c_rxresume;                         /* _ARGO: integer, nozero */
    /* Time at which we rx/tx end from/to client */
    int64_t c_rx_end_us;                        /* _ARGO: integer, adj_time, nozero */
    int64_t c_tx_end_us;                        /* _ARGO: integer, adj_time, nozero */
    /* Number of bytes that were dropped (not transmitted) */
    int32_t c_notx_bytes;                       /* _ARGO: integer, nozero */

    /* Client flow control, max bytes transmittable and receivable,
     * and last time we received or transmitted flow control */
    int64_t c_fc_tx_max;                        /* _ARGO: integer, nozero */
    int64_t c_fc_rx_max;                        /* _ARGO: integer, nozero */
    int64_t c_fc_rx_us;                         /* _ARGO: integer, adj_time, nozero */
    int64_t c_fc_tx_us;                         /* _ARGO: integer, adj_time, nozero */
    int64_t c_bad_udp;                          /* _ARGO: integer, nozero */
    int64_t c_bad_icmp;                         /* _ARGO: integer, nozero */

/* From here */
/* Assistant stats. Same as client stats */
    int64_t a_rx_us_b;                          /* _ARGO: integer, adj_time,         synonym: TimestampZENFirstRxConnector */
    int64_t a_tx_us_b;                          /* _ARGO: integer, adj_time,         synonym: TimestampZENFirstTxConnector */
    int64_t a_rx_us;                            /* _ARGO: integer, adj_time,         synonym: TimestampZENLastRxConnector */
    int64_t a_tx_us;                            /* _ARGO: integer, adj_time,         synonym: TimestampZENLastTxConnector */
    int64_t a_rxbytes;                          /* _ARGO: integer,                   synonym: ZENTotalBytesRxConnector */
    int64_t a_rxdelta;                          /* _ARGO: integer,                   synonym: ZENBytesRxConnector */
    int64_t a_txbytes;                          /* _ARGO: integer,                   synonym: ZENTotalBytesTxConnector */
    int64_t a_txdelta;                          /* _ARGO: integer,                   synonym: ZENBytesTxConnector */
    int32_t a_txpause;                          /* _ARGO: integer, nozero */
    int32_t a_rxpause;                          /* _ARGO: integer, nozero */
    int32_t a_pauseto;                          /* _ARGO: integer, nozero */
    int32_t a_txresume;                         /* _ARGO: integer, nozero */
    int32_t a_rxresume;                         /* _ARGO: integer, nozero */
    int64_t a_rxpause_time_max_us;              /* _ARGO: integer, nozero */
    int64_t a_txpause_time_max_us;              /* _ARGO: integer, nozero */
    int64_t a_rxpause_time_max_epoch_us;        /* _ARGO: integer, nozero */
    int64_t a_txpause_time_max_epoch_us;        /* _ARGO: integer, nozero */
    int64_t a_rxpause_time_total_us;            /* _ARGO: integer, nozero */
    int64_t a_txpause_time_total_us;            /* _ARGO: integer, nozero */
    int64_t a_rx_end_us;                        /* _ARGO: integer, adj_time, nozero */
    int64_t a_tx_end_us;                        /* _ARGO: integer, adj_time, nozero */
    int32_t a_notx_bytes;                       /* _ARGO: integer, nozero */

    int64_t a_fc_tx_max;                        /* _ARGO: integer, nozero */
    int64_t a_fc_rx_max;                        /* _ARGO: integer, nozero */
    int64_t a_fc_rx_us;                         /* _ARGO: integer, adj_time, nozero */
    int64_t a_fc_tx_us;                         /* _ARGO: integer, adj_time, nozero */
    int64_t a_bad_udp;                          /* _ARGO: integer, nozero */
    int64_t a_bad_icmp;                          /* _ARGO: integer, nozero */

    uint32_t double_encrypt;                    /* _ARGO: integer,                   synonym: DoubleEncryption */
    uint32_t zpn_probe_type;                    /* _ARGO: integer,                   synonym: ZdxMTRProbeRequest */
    uint16_t ip_proto;                          /* _ARGO: integer,                   synonym: IPProtocol */

    const char *client_type;                    /* _ARGO: string */
    int32_t icmp_access_type;                   /* _ARGO: integer */

    int32_t async_count;                        /* _ARGO: integer, nozero */
    int32_t is_wildcard;                        /* _ARGO: integer */
    int32_t drop_udp_framed_data;               /* _ARGO: integer, nozero */
    int64_t c_bytes_dropped_udp;                /* _ARGO: integer */
    int64_t a_bytes_dropped_udp;                /* _ARGO: integer */

    /* assistant group gids (for filtering) */
    char **filter_ast_grp_str_array;            /* _ARGO: string */
    int filter_ast_grp_str_count;               /* _ARGO: integer, quiet, count: filter_ast_grp_str_array */

    /* extranet locs */
    char **filter_extranet_locs;                /* _ARGO: string */
    int filter_extranet_locs_count;             /* _ARGO: integer, quiet, count: filter_extranet_locs */

    /* filter_ast_grp and filter_ast_grp_count is deprecated, use filter_ast_grp_str_array instead */
    int64_t *filter_ast_grp;           /* _ARGO: integer */
    int filter_ast_grp_count;          /* _ARGO: integer, quiet, count: filter_ast_grp */

    /* Data about the original connection that was intercepted by the
     * client. (edge connector, zapp, whatever) */
    /*
     * The following fields all come in the case of ip anchoring or
     * edge connector (and maybe others in the future) The system
     * trusts these from the client, so if policy is configured using
     * their information (which may occur in the future) then
     * client_type should be used to have some assurance these fields
     * are trustworthy.
     *
     * o_ = Original- came from the client's view of the world.
     */
    int64_t o_user_id;                 /* _ARGO: integer, nozero */
    int64_t o_location_id;             /* _ARGO: integer, nozero */
    struct argo_inet o_sip;            /* _ARGO: inet, nozero */
    struct argo_inet o_dip;            /* _ARGO: inet, nozero */
    int32_t o_sport;                   /* _ARGO: integer, nozero */
    int32_t o_dport;                   /* _ARGO: integer, nozero */

    /* contains one of:
    *     user name ( email)
    *     location name - if  o_user_id == o_location_id
    *     NULL
    */
    char *o_identity_name;             /* _ARGO: string, nozero */


    char path_decision[ZPN_TX_PATH_DECISION_STR_LEN];    /* _ARGO: string */
    /*
     * The following field is the SLA 'time spent for connection
     * setup', and the field indicating how valid the SLA is.
     *
     * These fields are only used for transaction logs on public
     * brokers. The field is not even calculated for private brokers.
     *
     * In the following cases, the fields mean:
     *
     * startrx_us: The time at which we received the mtunnel request
     *    from client.
     *
     * c_tx_end_us: The time at which we sent end mtunnel to client.
     *
     * rt_info_rx_us: The time at which we received the route info
     *    message from the dispatcher (This is the dispatcher telling
     *    the originating broker about the decision the dispatcher
     *    made)
     *
     * brk_req_utoa_tx_us : The time at which a setup request
     *    is sent directly to a connector when using a cached
     *    dispatcher decision.
     *
     * learn_us: non-zero to indicate how much time has been sent
     *    letting the dispatcher do learning.
     *
     * A) Connection is terminated for policy or configuration
     *    reasons:
     *
     *    sla_us = c_tx_end_us - startrx_us
     *
     * B) Successful dispatch to real dispatcher. This does not
     *    include time the dispatcher attempted to learn.
     *
     *    sla_us = (rt_info_rx_us - startrx_us) - learn_us
     *
     * C) Unsuccessful dispatch to real dispatcher: (app not
     *    reachable, etc). This does not include time the dispatcher
     *    attempted to learn. All errors coming from the assistant and
     *    not from the dispatcher are prefixed with AST_
     *
     *    sla_us = (c_tx_end_us - startrx_us) - learn_us
     *
     * D) Cached connection completion:
     *
     *    sla_us = (brk_req_utoa_tx_us - startrx_us)
     *
     * NOTE: The above times are calculated for all cases as the
     * following pseudocode:
     *
     * if (connection_closed && this_is_public_broker) {
     *    if (rt_info_rx_us) {
     *       sla_valid = 1;
     *       sla_us = (rt_info_rx_us - startrx_us) - learn_us;
     *    } else if (brk_req_utoa_tx_us) {
     *       sla_valid = 1;
     *       sla_us = (brk_req_utoa_tx_us- startrx_us);
     *    } else if (strncmp(reason, "AST", 3)) {
     *       // The error/failure was NOT generated by assistant.
     *       sla_valid = 1;
     *       sla_us = (c_tx_end_us - startrx_us) - learn_us
     *    } else {
     *       sla_valid = 0;
     *       sla_us = 0;
     *    }
     * } else {
     *    sla_valid = 0;
     *    sla_us = 0;
     * }
     *
     * A couple cases to consider, above, to demonstrate some
     * correctness:
     *
     * For BRK_MT_SETUP_TIMEOUT. If we never get a rt_info message
     * back, we will get a timeout, and the sla will be valid and 30s
     *
     * Most allocation/weird failures will be caught in the third
     * block.
     *
     * Difficulties communicating between dispatcher and connector
     * will be reflected as AST_errors, or timeouts where we DID get a
     * rt_info message back.
     *
     * For simple policy errors,the third case is hit, and these are
     * easily handled.
     *
     * learn_us is always zero unless learning actually occurred,
     * which makes subtracting it out in basically all the cases a
     * valid thing to do.
     */
    int64_t sla_us;                 /* _ARGO: integer, nozero */
    int sla_valid;                  /* _ARGO: integer, nozero */

    char **log_features;            /* _ARGO: string */
    int log_features_count;         /* _ARGO: quiet, integer, count: log_features */

    int allow_all_xport;            /* _ARGO: integer */
    char *c_tlv_type;               /* _ARGO: string */
    char *a_tlv_type;               /* _ARGO: string */

    /* value 1 if it is client to client tunnel */
    int c2c;                        /* _ARGO: integer             synonym: ClientToClient  */
    /* the peer user */
    const char *c2c_uid_peer;       /* _ARGO: string           */

    /* Report fohh pause,resume, timeout counts
     * mconn client:
     *   to_client_paused_count, from_client_paused_count, to_client_resume_count, from_client_resume_count, to_client_pause_timed_out_count
     * mconn assistant:
     *   to_client_paused_count, from_client_paused_count, to_client_resume_count, from_client_resume_count, to_client_pause_timed_out_count
     */
    int32_t flow_ctrl[FOHH_FLOW_CTRL_SIZE]; /* _ARGO: integer */

    uint8_t connector_close_to_app;         /* _ARGO: integer */
    int64_t posture_profile_version;        /* _ARGO: integer, nozero */

    /* partition gids for public broker */
    int64_t *g_inst_part_gids;                   /* _ARGO: integer,                   synonym: partition gids */
    int16_t g_part_gid_count;                    /* _ARGO: quiet, integer,            count: g_inst_part_gids */
    int64_t g_cst_part_gid;                      /* _ARGO: integer */
    int64_t mtunnel_req_policy_check_start_us;   /* _ARGO: integer */
    int64_t mtunnel_req_policy_check_time_us;    /* _ARGO: integer */

    int64_t sv_posture_profile_version;        /* _ARGO: integer, nozero */

    /*saml assertion logs */
    int64_t saml_not_before;                     /* _ARGO: integer */
    int64_t saml_not_on_or_after;                /* _ARGO: integer */
    /* PRA session sharing/proctoring */
    char *c_pra_shared_users_list;               /* _ARGO: string,                    synonym: PRASharedUsersList */
    char *c_pra_shared_mode;                     /* _ARGO: string,                    synonym: PRASharedMode */

    const char *app_match_style;              /* _ARGO: string */

    // ZIA/ZPA inspection
    // 1 - app is set be inspected via zia, i.e inspection will be attempted,
    int8_t zia_inspection; /* _ARGO: integer, nozero, synonym: ZiaInspection */

    // ZIA inspection status code, in cases when the mtunnel was allowed
    // to continue without performing zia inspection.
    // This happens when inspection PAC/SME was not available or misconfigured
    // if inspection we perform, or not requested, this field is null
    const char *zia_inspection_status_code;     /* _ARGO: string , synonym: ZiaInspectionStatusCode   */
    int8_t zia_inspection_res;                  /* _ARGO: integer, nozero, synonym: ZiaInspectionRes */
    int8_t zia_inspection_bypassed;             /* _ARGO: integer, nozero, synonym:  ZiaInspectionBypassed */

    // time in ms, to complete the control path to sme; it could be bypassed
    int64_t zia_ctrl_path_ms;               /* _ARGO: integer, nozero */
    // CC/BC parent location
    int64_t o_plocation_id;                 /* _ARGO: integer, nozero */

    int peer_disconnect_before_term;             /* _ARGO: integer */
    int assistant_indicate_end;                  /* _ARGO: integer */

    char *slogger_info;      /* _ARGO: string */
    int64_t sitec_id;        /* _ARGO: integer, stringify,          synonym: SiteController */

    // count to capture skipped logs for natural at broker's end
    int64_t skipped_error_count_natural;         /* _ARGO: integer */
    // string to capture the reason of throtled log flushing
    char natural_throttle_log_flush_reason[ZPN_NATURAL_THROTTLE_LOG_FLUSH_REASON_SIZE];      /* _ARGO: string  */

    /* Data broker re-dispatched brk req to a dispatcher in different DC due to timeout at this time */
    int64_t redisptx_us;                        /* _ARGO: integer, adj_time */

    /* mconn performance measurements stats */

    /*
     * histogram, at broker on the client side, inter packet gaps delays distribution
     * grouped into buckets of: 50ms, 200ms, 700ms, 1500ms, 1500ms plus delays
     */
    int c_rx_diff_rx_data_hist[FOHH_MCONN_TRACK_RX_DIFF_RX_DATA_HISTOGRAM_MAX];    /* _ARGO: integer, nozero */
    /*
     *
     * histogram, at broker, delays distribution from client side towards assistant side (from ingress to egress)
     * grouped into buckets of: 20ms, 50ms, 100ms, 500ms, 500ms plus
     */
    int a_tx_peer_rx_data_hist[FOHH_MCONN_TRACK_TX_PEER_RX_DATA_HISTOGRAM_MAX];    /* _ARGO: integer, nozero */

    uint64_t  c_client_rx_data_len[FOHH_MCONN_QUEUE_DATA_HISTOGRAM_MAX]; /* _ARGO: integer */
    uint64_t  a_client_rx_data_len[FOHH_MCONN_QUEUE_DATA_HISTOGRAM_MAX]; /* _ARGO: integer */
    uint64_t  c_tx_buf_len[FOHH_MCONN_QUEUE_DATA_HISTOGRAM_MAX];         /* _ARGO: integer */
    uint64_t  a_tx_buf_len[FOHH_MCONN_QUEUE_DATA_HISTOGRAM_MAX];         /* _ARGO: integer */

    /* max time in us taken from client towards assistant, or from ingress (rx) to egress (tx)  */
    int a_tx_peer_rx_data_max_us;                               /* _ARGO: integer, nozero */
    /* epoch of max time in us taken from client towards assistant, or from ingress (rx) to egress (tx)  */
    int64_t a_tx_peer_rx_data_max_epoch_us;                     /* _ARGO: integer, nozero */
    /* number of packets received from client towards assistant during a_tx_peer_rx_data_max_us */
    int a_tx_peer_rx_data_max_cnt;                              /* _ARGO: integer, nozero */
    /* total number of attempts to schedule packets at egress mconn towards assistant during tx_peer_rx_data_max_us */
    int a_tx_peer_rx_data_max_unblock_cnt;                      /* _ARGO: integer, nozero */
    /* max time in us taken to schedule packet waiting on egress to be sent towards assistant */
    int a_tx_data_unblock_max_us;                               /* _ARGO: integer, nozero */
    /* max number of attempts to schedule packets during a_tx_data_unblock_max_us */
    int a_tx_data_unblock_max_cnt;                              /* _ARGO: integer, nozero */
    /* total time in us taken to schedule packets waiting on egress to be sent towards client */
    int64_t a_tx_data_unblock_tot_us;                           /* _ARGO: integer, nozero */

    /*
     * histogram, at broker on the assistant side, inter packet gaps delays distribution
     * grouped into buckets of: 50ms, 200ms, 700ms, 1500ms, 1500ms plus delays
     */
    int a_rx_diff_rx_data_hist[FOHH_MCONN_TRACK_RX_DIFF_RX_DATA_HISTOGRAM_MAX];    /* _ARGO: integer, nozero */
    /*
     * histogram, at broker, delays distribution from assistant side towards client side (from ingress to egress)
     * grouped into buckets of: 20ms, 50ms, 100ms, 500ms, 500ms plus
     */
    int c_tx_peer_rx_data_hist[FOHH_MCONN_TRACK_TX_PEER_RX_DATA_HISTOGRAM_MAX];    /* _ARGO: integer, nozero */
    /* max time in us taken from assistant towards client, or from ingress (rx) to egress (tx) */
    int c_tx_peer_rx_data_max_us;                              /* _ARGO: integer, nozero */
    /* epoch of max time in us taken from assistant towards client, or from ingress (rx) to egress (tx)  */
    int64_t c_tx_peer_rx_data_max_epoch_us;                    /* _ARGO: integer, nozero */
    /* number of packets received from assistant towards client during c_tx_peer_rx_data_max_us */
    int c_tx_peer_rx_data_max_cnt;                             /* _ARGO: integer, nozero */
    /* total number of attempts to schedule packets at egress mconn towards client during tx_peer_rx_data_max_us */
    int c_tx_peer_rx_data_max_unblock_cnt;                     /* _ARGO: integer, nozero */
    /* max time in us taken to schedule packet waiting on egress to be sent towards client */
    int c_tx_data_unblock_max_us;                              /* _ARGO: integer, nozero */
    /* max number of attempts to schedule packets during a_tx_data_unblock_max_us */
    int c_tx_data_unblock_max_cnt;                             /* _ARGO: integer, nozero */
    /* total time in us taken to schedule packets waiting on egress to be sent towards client */
    int64_t c_tx_data_unblock_tot_us;                          /* _ARGO: integer, nozero */

    uint8_t is_fqdn_to_server_ip;               /* _ARGO: integer*/
    int64_t g_fqdn_to_server_ip_first_eval_rul; /* _ARGO: integer */

    /* Workload tag stats */
    uint32_t *wtag_grp_found;                                  /* _ARGO: integer */
    int wtag_grp_found_rcvd;                                   /* _ARGO: integer, count: wtag_grp_found */

    uint32_t *wtag_grp_not_found;                              /* _ARGO: integer */
    int wtag_grp_not_found_rcvd;                               /* _ARGO: integer, count: wtag_grp_not_found */

    /*
     * Note following are performance stats coming from assistant
     * Note prefix:
     * a_s_ - at assistant on server side
     * a_b_ - at assistant on broker side
     */
    /*
     * histogram, at assistant on the broker side, inter packet gaps delays distribution
     * grouped into buckets of: 50ms, 200ms, 700ms, 1500ms, 1500ms plus delays
     */
    int a_b_rx_diff_rx_data_hist[FOHH_MCONN_TRACK_RX_DIFF_RX_DATA_HISTOGRAM_MAX];    /* _ARGO: integer, nozero */
    /*
     * histogram, at assistant, delays distribution from broker side towards server side (from ingress to egress)
     * grouped into buckets of: 20ms, 50ms, 100ms, 500ms, 500ms plus
     */
    int a_s_tx_peer_rx_data_hist[FOHH_MCONN_TRACK_TX_PEER_RX_DATA_HISTOGRAM_MAX];    /* _ARGO: integer, nozero */
    /* at assistant max time delay in us from broker side towards server side (from ingress to egress) */
    int a_s_tx_peer_rx_data_max_us;                               /* _ARGO: integer, nozero */
    /* epoch for a_s_tx_peer_rx_data_max_us */
    int64_t a_s_tx_peer_rx_data_max_epoch_us;                     /* _ARGO: integer, nozero */

    /*
     * histogram, at assistant on the server side, inter packet gaps delays distribution
     * grouped into buckets of: 50ms, 200ms, 700ms, 1500ms, 1500ms plus delays
     */
    int a_s_rx_diff_rx_data_hist[FOHH_MCONN_TRACK_RX_DIFF_RX_DATA_HISTOGRAM_MAX];    /* _ARGO: integer, nozero */
    /*
     * histogram, at assistant, delays distribution from assistant side towards broker side (from ingress to egress)
     * grouped into buckets of: 20ms, 50ms, 100ms, 500ms, 500ms plus
     */
    int a_b_tx_peer_rx_data_hist[FOHH_MCONN_TRACK_TX_PEER_RX_DATA_HISTOGRAM_MAX];    /* _ARGO: integer, nozero */
    /* at assistant, max time delay in us from server side towards broker side (from ingress to egress) */
    int a_b_tx_peer_rx_data_max_us;                              /* _ARGO: integer, nozero */
    /* epoch of a_b_tx_peer_rx_data_max_us  */
    int64_t a_b_tx_peer_rx_data_max_epoch_us;                    /* _ARGO: integer, nozero */

    /* number of packets received from assistant towards broker during a_b_tx_peer_rx_data_max_us */
    int a_b_tx_peer_rx_data_max_cnt;                             /* _ARGO: integer, nozero */
    /* total number of attempts to schedule packets at egress mconn towards broker during a_b_tx_peer_rx_data_max_us */
    int a_b_tx_peer_rx_data_max_unblock_cnt;                     /* _ARGO: integer, nozero */

    /* max time in us taken to schedule packet waiting on egress to be sent towards broker from assistant*/
    int a_b_tx_data_unblock_max_us;                              /* _ARGO: integer, nozero */
    /* max number of attempts to schedule packets during a_b_tx_data_unblock_max_us */
    int a_b_tx_data_unblock_max_cnt;                             /* _ARGO: integer, nozero */
    /* total time in us taken to schedule packets waiting on egress to be sent towards broker from assistant */
    int64_t a_b_tx_data_unblock_tot_us;                          /* _ARGO: integer, nozero */

    // eas/zvpn/partner/extranet
    // intent indicaction of transction is ZVPN one
    // from App Segment
    int32_t extranet;           /* _ARGO: integer, nozero  */

    // dispatcher or c_state
    char *zia_cloud;       /* _ARGO: string */
    int64_t zia_org_id;          /* _ARGO: integer, nozero  */

    // is this ZPVN instance/type ? if so, Dispatcher , app_route_info
    int64_t zia_instance_id;    /* _ARGO: integer, nozero  */
    int64_t zia_instance_type;  /* _ARGO: integer, nozero  */
    int64_t zia_ipsec_id;       /* _ARGO: integer, nozero */
    int64_t zia_location_id;    /* _ARGO: integer, nozero  */
    int64_t zia_partner_id;     /* _ARGO: integer, nozero  */


    /*Chrome Enterprise Browser*/
    char g_browser_version[MAX_GPOSTURE_STR_LEN];                    /* _ARGO: string,                synonym: BrowserVersion */
    char g_key_trust_level[MAX_GPOSTURE_STR_LEN];                    /* _ARGO: string,                synonym: KeyTrustLevel */
    char g_operating_system[MAX_GPOSTURE_STR_LEN];                   /* _ARGO: string,                synonym: OperatingSystem */
    char g_disk_encryption[MAX_GPOSTURE_STR_LEN];                    /* _ARGO: string,                synonym: DiskEncryption */
    char g_os_firewall[MAX_GPOSTURE_STR_LEN];                        /* _ARGO: string,                synonym: OsFirewall */
    char g_screen_lock_secured[MAX_GPOSTURE_STR_LEN];                /* _ARGO: string,                synonym: ScreenLockSecured */
    char g_safe_browsing_protection_level[MAX_GPOSTURE_STR_LEN];     /* _ARGO: string,                synonym: SafeBrowsingProtectionLevel */
    char g_crowd_strike_agent[MAX_GPOSTURE_STR_LEN];                 /* _ARGO: string,                synonym: CrowdStrikeAgent */

    int64_t *gprofile_gids;                                          /* _ARGO: integer,                synonym: GProfileGIDs */
    int gprofile_gid_count;                                          /* _ARGO: integer, quiet, count: gprofile_gids */
    int managed_browser_payload_version;                             /* _ARGO: integer */

    /* Stepup auth */
    int64_t required_stepup_auth_level_gid;                          /* _ARGO: integer,               synonym: RequiredStepupAuthLevelGid */
    int64_t current_stepup_auth_level_gid;                           /* _ARGO: integer,               synonym: CurrentStepupAuthLevelGid */
    int64_t required_stepup_auth_level_expiry_s;                     /* _ARGO: integer */
    int64_t current_stepup_auth_level_expiry_s;                      /* _ARGO: integer */
    char *c_pra_credential_id;                                       /* _ARGO: string */
    char *c_pra_credential_pool_id;                                  /* _ARGO: string */

    /* AAE Profile Conclusion */
    char **aae_conclusion_user_profiles;                             /* _ARGO: string */
    int aae_conclusion_user_profile_count;                           /* _ARGO: quiet, integer, count: aae_conclusion_user_profiles */
    char **aae_conclusion_device_profiles;                           /* _ARGO: string */
    int aae_conclusion_device_profile_count;                         /* _ARGO: quiet, integer, count: aae_conclusion_device_profiles */

    char gd_cookie_domain_id[MAX_GD_COOKIE_ID_LEN];                  /* _ARGO: string,                synonym: GDCookieDomainId */

    /* disable read count when client tx buff is above high level */
    int32_t c_disable_read_client_count;                             /* _ARGO: integer, nozero */
    /* enable read count when client tx buff is below low level*/
    int32_t c_enable_read_client_count;                              /* _ARGO: integer, nozero */
    /* max read disable time when client tx buff is above high level */
    int64_t c_disable_read_client_time_max_us;                       /* _ARGO: integer, nozero */
    /* epoch time when max read disable time when client tx buff is above high level  */
    int64_t c_disable_read_client_time_max_epoch_us;                 /* _ARGO: integer, nozero */
    /* total cumulative read disable time elapsed when client tx buff is above high level */
    int64_t c_disable_read_client_time_total_us;                     /* _ARGO: integer, nozero */

};

// the size of static array , which holds fohh_queue's stats
#define FOHH_QUEUE_STATS_SIZE 9
/*
 * Authentication log for client
 *
 * g_cst - The customer for the log.
 *
 * c_uid - The user ID from SAML.
 *
 * c_pub_ip - The client public IP.
 *
 * c_priv_ip (opt) - The client private IP.
 *
 * c_lat - Latitude of public IP.
 *
 * c_lon - Longitude of public IP.
 *
 * g_cc - Country code.
 *
 * g_brk - The broker generating this message.
 *
 * auth_us - Time the authentication is checked.
 *
 * deauth_us - Time the client disconnect itself from broker
 *
 * result - 1: success, 0: fail
 */
struct zpn_auth_log {                           /* _ARGO: object_definition */
    int64_t log_date;                           /* _ARGO: integer, adj_time,         synonym: LogTimestamp */
    char *version;                              /* _ARGO: string,                    synonym: Version */

    int64_t g_cst;                              /* _ARGO: integer, stringify,        synonym: Customer */
    int64_t g_microtenant;                      /* _ARGO: integer, stringify, nozero       synonym: MicroTenantID */
    int64_t g_brk;                              /* _ARGO: integer, stringify,        synonym: ZEN */
    int64_t g_exp;                              /* _ARGO: integer, stringify,        synonym: Exporter */
    int64_t g_pbrk;                             /* _ARGO: integer, stringify, nozero        synonym: PrivateBroker */

    int64_t *gids;                              /* _ARGO: integer */
    int gids_count;                             /* _ARGO: integer, quiet, count: gids */

    const char *cname;                          /* _ARGO: string,                    synonym: CertificateCN */
    const char *c_uid;                          /* _ARGO: string,                    synonym: Username */
    const char *tunnel_id;                      /* _ARGO: string,                    synonym: SessionID */
    struct argo_inet priv_ip;                   /* _ARGO: inet,                      synonym: PrivateIP */
    struct argo_inet pub_ip;                    /* _ARGO: inet,                      synonym: PublicIP */
    /* The port associated with the public IP */
    int pub_port;                               /* _ARGO: integer */
    double c_lat;                               /* _ARGO: double,                    synonym: Latitude */
    double c_lon;                               /* _ARGO: double,                    synonym: Longitude */
    char *cc;                                   /* _ARGO: string,                    synonym: CountryCode */
    char *c_city;                               /* _ARGO: string,                    synonym: City */

    int64_t idp_gid;                            /* _ARGO: integer,                   synonym: Idp, synonym: IDP */

    int64_t auth_start_us;                      /* _ARGO: integer, adj_time,         synonym: TimestampAuthenticationStart */
    int64_t auth_us;                            /* _ARGO: integer, adj_time,         synonym: TimestampAuthentication */
    int64_t deauth_us;                          /* _ARGO: integer, adj_time,         synonym: TimestampUnAuthentication */
    char *status;                               /* _ARGO: string,                    synonym: SessionStatus */

    int32_t tcpi_snd_mss;                       /* _ARGO: integer */
    int32_t tcpi_rcv_mss;                       /* _ARGO: integer */
    int32_t tcpi_rtt;                           /* _ARGO: integer */
    int32_t tcpi_rttvar;                        /* _ARGO: integer */
    int32_t tcpi_snd_cwnd;                      /* _ARGO: integer */
    int32_t tcpi_advmss;                        /* _ARGO: integer */
    int32_t tcpi_reordering;                    /* _ARGO: integer */
    int32_t tcpi_rcv_rtt;                       /* _ARGO: integer */
    int32_t tcpi_rcv_space;                     /* _ARGO: integer */
    int32_t tcpi_total_retrans;                 /* _ARGO: integer */
    int64_t tcpi_thru_put;                      /* _ARGO: integer */

    uint32_t tcpi_last_data_sent;               /* _ARGO: integer */
    uint32_t tcpi_last_ack_sent;                /* _ARGO: integer */
    uint32_t tcpi_last_data_recv;               /* _ARGO: integer */
    uint32_t tcpi_last_ack_recv;                /* _ARGO: integer */

    uint64_t tcpi_bytes_acked;                  /* _ARGO: integer */
    uint64_t tcpi_bytes_received;               /* _ARGO: integer */
    uint64_t tcpi_segs_out;                     /* _ARGO: integer */
    uint64_t tcpi_segs_in;                      /* _ARGO: integer */

    uint64_t transmit_bytes;                    /* _ARGO: integer,                   synonym: TotalBytesTx */
    uint64_t receive_bytes;                     /* _ARGO: integer,                   synonym: TotalBytesRx */
    uint64_t transmit_objects;                  /* _ARGO: integer */
    uint64_t receive_objects;                   /* _ARGO: integer */
    uint64_t transmit_raw_tlv;                  /* _ARGO: integer */
    uint64_t receive_raw_tlv;                   /* _ARGO: integer */

    uint32_t sock_buf;                          /* _ARGO: integer */
    uint32_t delay_optimized;                   /* _ARGO: integer */
    uint32_t chunk_size;                        /* _ARGO: integer */
    uint32_t allowed_chunk;                     /* _ARGO: integer */

    const char *client_type;                    /* _ARGO: string,                    synonym: ClientType */
    const char *auth_type;                      /* _ARGO: string,                    synonym: AuthType */
    char *attributes;                           /* _ARGO: string,                    synonym: SAMLAttributes */
    char **postures_hit;                        /* _ARGO: string,                    synonym: PosturesHit */
    int postures_hit_count;                     /* _ARGO: quiet, integer, count: postures_hit */
    char **postures_miss;                       /* _ARGO: string,                    synonym: PosturesMiss */
    int postures_miss_count;                    /* _ARGO: quiet, integer, count: postures_miss */
    char **trusted_networks;                    /* _ARGO: string,                    synonym: TrustedNetworks */
    int trusted_networks_count;                 /* _ARGO: quiet, integer, count: trusted_networks */
    char **trusted_networks_names;              /* _ARGO: string,                    synonym: TrustedNetworksNames */
    int trusted_networks_names_count;           /* _ARGO: quiet, integer, count: trusted_networks_names */
    const char *hostname;                       /* _ARGO: string,                    synonym: Hostname */
    const char *platform;                       /* _ARGO: string,                    synonym: Platform */
    const char *close_reason;                   /* _ARGO: string */

    int32_t app_rtt[FOHH_HISTOGRAM_MAX_BUCKETS];/* _ARGO: integer */
    int app_rtt_count;                          /* _ARGO: quiet, integer, count: app_rtt */
    int32_t tcp_congestion_win[FOHH_HISTOGRAM_MAX_BUCKETS]; /* _ARGO: integer */
    int tcp_congestion_win_count;               /* _ARGO: quiet, integer, count: tcp_congestion_win */

    double broker_latitude;                     /* _ARGO: double,                    synonym: ZENLatitude */
    double broker_longitude;                    /* _ARGO: double,                    synonym: ZENLongitude */

    // ET-76185 This value can come from multiple sources
    //  - The broker has a static cc thats safe to just assign
    //  - The PSE cc is assigned out of wally
    // We can either always strdup and free, or just do what the broker does
    // and inline it with the rest of the struct.
    // Then we can just snprintf into it, and not worry about trying
    // to figure out if we can free, or dealing with new heap allocs
    // They are small so inline seems better and more consistent.
    // Size is based on CC_STR_LEN+1 but argo runs before the preprocessor so macros do not work!
    char broker_cc[CC_STR_LEN_P1];                          /* _ARGO: string,                    synonym: ZENCountryCode */

    char *brk_redirect[ZPN_CLIENT_MAX_BROKERS]; /* _ARGO: string */
    int brk_redirect_count;                     /* _ARGO: quiet, integer, count: brk_redirect */
    int brk_type[ZPN_CLIENT_MAX_BROKERS];       /* _ARGO: integer */
    int brk_type_count;                         /* _ARGO: quiet, integer, count: brk_type */
    int64_t brk_redirect_ts_us;                 /* _ARGO: integer, adj_time */

    int64_t app_download_calls;                 /* _ARGO: integer */
    int64_t app_download_count;                 /* _ARGO: integer */
    int64_t app_download_net;                   /* _ARGO: integer */
    int64_t app_download_start_us;              /* _ARGO: integer */
    int64_t app_download_complete_us;           /* _ARGO: integer */

    int64_t app_update_calls;                   /* _ARGO: integer */
    int64_t app_update_start_us;                /* _ARGO: integer */
    int64_t app_update_complete_us;             /* _ARGO: integer */

    int64_t client_policy_evaluate_calls;          /* _ARGO: integer */
    int64_t client_policy_evaluate_start_us;       /* _ARGO: integer */
    int64_t client_policy_evaluate_complete_us;    /* _ARGO: integer */

    int64_t client_policy_rules_count;          /* _ARGO: integer */
    int64_t client_policy_timer_push_count;     /* _ARGO: integer */

    int64_t max_new_client_cb_in_queue;         /* _ARGO: integer */
    int64_t max_refresh_timer_cb_in_queue;      /* _ARGO: integer */
    int64_t max_app_complete_cb_in_queue;       /* _ARGO: integer */
    int64_t max_inform_one_cb_in_queue;         /* _ARGO: integer */

    /* The app download queue size for the customer when this client is enqueued */
    int64_t app_download_high_priority_queue_size_in; /* _ARGO: integer */
    int64_t app_download_low_priority_queue_size_in;  /* _ARGO: integer */

        /* The app download queue size for the customer when this client is dequeued */
    int64_t app_download_high_priority_queue_size_out; /* _ARGO: integer */
    int64_t app_download_low_priority_queue_size_out;  /* _ARGO: integer */

    int64_t app_thread_high_priority_enqueue_us;   /* _ARGO: integer */
    int64_t app_thread_high_priority_dequeue_us;   /* _ARGO: integer */
    int64_t app_thread_low_priority_enqueue_us;    /* _ARGO: integer */
    int64_t app_thread_low_priority_dequeue_us;    /* _ARGO: integer */

    int64_t client_app_sent_cnt_in;   /* _ARGO: integer */
    int64_t client_app_sent_cnt_out;  /* _ARGO: integer */

    int64_t app_process_termination_count;         /* _ARGO: integer */

    int64_t latest_app_sent_us;                    /* _ARGO: integer */
    char app_download_debug_str[128];              /* _ARGO: string */

    /* NP app download stats */
    int64_t client_subnet_start_us;                 /* _ARGO: integer */
    int64_t client_subnet_complete_us;              /* _ARGO: integer */
    int64_t lan_subnet_start_us;                    /* _ARGO: integer */
    int64_t lan_subnet_complete_us;                 /* _ARGO: integer */
    int64_t domain_start_us;                        /* _ARGO: integer */
    int64_t domain_complete_us;                     /* _ARGO: integer */

    int64_t client_subnet_sent_cnt_out;             /* _ARGO: integer */
    int64_t lan_subnet_sent_cnt_out;                /* _ARGO: integer */
    int64_t domain_sent_cnt_out;                    /* _ARGO: integer */

    int64_t first_subnet_sent_us;                   /* _ARGO: integer */
    int64_t latest_subnet_sent_us;                  /* _ARGO: integer */
    int64_t first_domain_sent_us;                   /* _ARGO: integer */
    int64_t latest_domain_sent_us;                  /* _ARGO: integer */

    int fwd_to_next_hop;                        /* _ARGO: integer */
    int hops_count;                             /* _ARGO: integer */
    int self_hop;                               /* _ARGO: integer */

    /* Hop type may be: "ORIG", "TRANSIT", "TERM" */
    char *self_hop_type;                        /* _ARGO: string */
    char **hop_type;                            /* _ARGO: string */
    int hop_type_count;                         /* _ARGO: integer, quiet, count: hop_type */

    /* broker gids */
    int64_t *brks;                               /* _ARGO: integer, stringify */
    int brks_count;                              /* _ARGO: integer, quiet, count: brks */

    int64_t *brks_grp;                           /* _ARGO: integer, stringify */
    int brks_grp_count;                         /* _ARGO: integer, quiet, count: brks_grp */

    /* tunnel ids */
    char **tunnel_ids;                           /* _ARGO: string */
    int tunnel_ids_count;                        /* _ARGO: integer, quiet, count: tunnel_ids */

    char **sys_type;                           /* _ARGO: string */
    int sys_type_count;                        /* _ARGO: integer, quiet, count: sys_type */

    /* Data needed for Rate Limiting */
    int64_t num_app_req;                          /* _ARGO: integer */
    int64_t num_app_req_rate_limited;             /* _ARGO: integer */
    int64_t app_req_rate_limited;                 /* _ARGO: integer */
    int64_t num_mtunnel_req_rate_limited;         /* _ARGO: integer */
    char last_rate_limited_app[256];              /* _ARGO: string */

    char *tlv_type;                              /* _ARGO: string */

    /* ZRDT related states */
    int64_t streams_created;                      /* _ARGO: integer */
    int64_t streams_destroyed;                    /* _ARGO: integer */
    int64_t packets_sent;                        /* _ARGO: integer */
    int64_t packets_recv;                        /* _ARGO: integer */
    int64_t peer_loss;                           /* _ARGO: integer */
    int64_t local_loss;                          /* _ARGO: integer */

    int32_t zrdt_rtt[ZRDT_CONN_HISTO_SIZE];      /* _ARGO: integer */
    int zrdt_rtt_count;                          /* _ARGO: quiet, integer, count: zrdt_rtt */
    int fqdn_registered;                         /* _ARGO: integer         synonym: FQDNRegistered  */
    const char *fqdn_register_error;             /* _ARGO: string          synonym: FQDNRegisteredError */
    // c2c_fqdn is not the same as hostname
    const char *c2c_fqdn;                        /* _ARGO: string          synonym: C2CFQDN  */
    const struct argo_inet *c2c_ip;              /* _ARGO: inet            synonym: C2CIP  */

    // socket buffer size and used bytes
    int sock_buf_size ;                          /* _ARGO: integer, nozero */
    int sock_buf_used ;                          /* _ARGO: integer, nozero */
    /* fohh_queue 3 stat - current, queued and dequeued for  types:
        fohh_queue_element_type_data,
        fohh_queue_element_type_control,
        fohh_queue_element_type_mission_critical


    */
    int32_t fohh_queue[FOHH_QUEUE_STATS_SIZE];  /* _ARGO: integer */

    /* Timestamp of last tx/rx */
    uint64_t last_tx_bytes_us;                  /* _ARGO: integer, adj_time, nozero */
    uint64_t last_rx_bytes_us;                  /* _ARGO: integer, adj_time, nozero */
    uint64_t last_tx_obj_us;                    /* _ARGO: integer, adj_time, nozero */
    uint64_t last_rx_obj_us;                    /* _ARGO: integer, adj_time, nozero */

    uint64_t transmit_objects_fail;             /* _ARGO: integer, nozero */
    uint32_t fohh_number_of_tickle_errors;      /* _ARGO: integer, nozero */
    int64_t fohh_last_tickle_us;                /* _ARGO: integer, adj_time, nozero */
    int64_t fohh_last_status_recieved_us;       /* _ARGO: integer, adj_time, nozero */

    char *external_device_id;                   /* _ARGO: string,           synonym: externalDeviceId */

    /* partition gids for public broker */
    int64_t *g_inst_part_gids;                  /* _ARGO: integer,                   synonym: partition gids */
    int16_t g_part_gid_count;                   /* _ARGO: quiet, integer,            count: g_inst_part_gids */
    int64_t g_cst_part_gid;                     /* _ARGO: integer */


    char *os_version;                           /* _ARGO: string,           synonym: osVersion */
    char *manufacturer;                         /* _ARGO: string,           synonym: Manufacturer */
    char *device_model;                         /* _ARGO: string,           synonym: deviceModel */
    // branch connector id, for client type zpn_client_type_branch_connector
    int64_t branch_gid;                        /* _ARGO: integer, nozero */

    char *primary_login_domain;                 /* _ARGO: string,           synonym: primaryLoginDomain */

    /* Redirect policy related states */
    int64_t redirect_policy_id;                 /* _ARGO: integer,          synonym: RedirectPolicyID */
    char *redirect_policy_name;                 /* _ARGO: string,           synonym: RedirectPolicyName */
    char *redirect_policy_action;               /* _ARGO: string,           synonym: RedirectPolicyAction */

    const char *socket_error;                   /* _ARGO: string */
    const char *ssl_state;                      /* _ARGO: string */
    const char  **ssl_str_err;                  /* _ARGO: string */
    int ssl_str_err_count;                      /* _ARGO: integer, quiet, count: ssl_str_err */
    int *ssl_num_err;                           /* _ARGO: integer */
    int ssl_num_err_count;                      /* _ARGO: integer, quiet, count: ssl_num_err */
    const char *valid_hostname;                 /* _ARGO: string  */
    // points to c_state hw_serial_id, as sent by ZCC
    const char *hw_serial_id;                   /* _ARGO: string */
    char current_auth_status[1024];             /* _ARGO: string */
    char fohh_status[512];                      /* _ARGO: string */
    int64_t posture_update_us;                  /* _ARGO: integer, adj_time, nozero */
    int64_t posture_profile_version;            /* _ARGO: integer, nozero */

    int user_risk_zia_orig_score;               /* _ARGO: integer */
    int user_risk_zia_override_score;           /* _ARGO: integer */
    int64_t user_risk_zia_orig_score_count;      /* _ARGO: integer */
    int64_t user_risk_zia_override_score_count; /* _ARGO: integer */

    int64_t sv_posture_update_us;                  /* _ARGO: integer, adj_time, nozero */
    int64_t sv_posture_profile_version;            /* _ARGO: integer, nozero */
    int64_t svcp_capability;                        /* _ARGO: integer */
    int64_t saml_not_before;                    /* _ARGO: integer */
    int64_t saml_not_on_or_after;               /* _ARGO: integer */
    char **sv_postures_hit;                     /* _ARGO: string,                    synonym: SvPosturesHit */
    int sv_postures_hit_count;                  /* _ARGO: quiet, integer, count: sv_postures_hit */
    char **sv_postures_miss;                    /* _ARGO: string,                    synonym: SvPosturesMiss */
    int sv_postures_miss_count;                 /* _ARGO: quiet, integer, count: sv_postures_miss */
    // ZIA Inspection related
    // not strictly hostname or domain for the connections
    const char *zia_cloud;                      /* _ARGO: string */
    char *zia_sme_ip;                           /* _ARGO: string */

    // EAS specific
    int64_t zia_inst_id;                        /* _ARGO: integer */
    uint64_t zia_inst_type;                     /* _ARGO: integer */

    char *slogger_info;                         /* _ARGO: string */

    int64_t sitec_id;                           /* _ARGO: integer, stringify,        synonym: SiteController */
    int64_t zia_org_id;                          /* _ARGO: integer, nozero */

    /*Chrome Enterprise Browser*/
    char g_browser_version[MAX_GPOSTURE_STR_LEN];                    /* _ARGO: string,                synonym: BrowserVersion*/
    char g_key_trust_level[MAX_GPOSTURE_STR_LEN];                    /* _ARGO: string,                synonym: KeyTrustLevel*/
    char g_operating_system[MAX_GPOSTURE_STR_LEN];                   /* _ARGO: string,                synonym: OperatingSystem*/
    char g_disk_encryption[MAX_GPOSTURE_STR_LEN];                    /* _ARGO: string,                synonym: DiskEncryption*/
    char g_os_firewall[MAX_GPOSTURE_STR_LEN];                        /* _ARGO: string,                synonym: OsFirewall*/
    char g_screen_lock_secured[MAX_GPOSTURE_STR_LEN];                /* _ARGO: string,                synonym: ScreenLockSecured*/
    char g_safe_browsing_protection_level[MAX_GPOSTURE_STR_LEN];     /* _ARGO: string,                synonym: SafeBrowsingProtectionLevel*/
    char g_crowd_strike_agent[MAX_GPOSTURE_STR_LEN];                 /* _ARGO: string,                synonym: CrowdStrikeAgent*/

    int64_t *gprofile_gids;                                          /* _ARGO: integer,                synonym: GProfileGIDs */
    int gprofile_gid_count;                                          /* _ARGO: integer, quiet, count: gprofile_gids */
    int managed_browser_payload_version;                             /* _ARGO: integer */

    /* AAE ZIdentity ID */
    const char *iam_device_id;                  /* _ARGO: string,                    synonym: ZIdentityDeviceId */
    const char *iam_user_id;                    /* _ARGO: string,                    synonym: ZIdentityUserId */

    int64_t client_total_app_count;                                 /* _ARGO: integer */
    int64_t client_bypass_app_count;                                /* _ARGO: integer */
    int64_t client_policy_deny_app_download_count;                  /* _ARGO: integer */
    int64_t client_inclusive_app_download_count;                    /* _ARGO: integer */
    int64_t app_thread_high_priority_wait_us;                       /* _ARGO: integer */
    int64_t ip_app_download_count;                                  /* _ARGO: integer */
    int64_t domain_app_download_count;                              /* _ARGO: integer */
    int64_t aggregated_domain_count;                                /* _ARGO: integer */
    char app_down_state[20];                                        /* _ARGO: string */
};

struct zpn_audit_log {                 /* _ARGO: object_definition */
    int32_t modified_time;             /* _ARGO: integer                    synonym: modifiedTime */
    int32_t creation_time;             /* _ARGO: integer                    synonym: creationTime */
    int64_t modifiedby_userid;         /* _ARGO: integer                    synonym: modifiedBy */
    char *request_id;                  /* _ARGO: string,                    synonym: requestId */
    char *session_id;                  /* _ARGO: string,                    synonym: sessionId */
    char *audit_old_value;             /* _ARGO: string,                    synonym: auditOldValue */
    char *audit_new_value;             /* _ARGO: string,                    synonym: auditNewValue */
    char *operation_type;              /* _ARGO: string,                    synonym: auditOperationType */
    char *object_type;                 /* _ARGO: string,                    synonym: objectType */
    char *object_name;                 /* _ARGO: string,                    synonym: objectName */
    int64_t object_id;                 /* _ARGO: integer,                   synonym: objectId */
    int64_t customer_id;               /* _ARGO: integer,                   synonym: customerId */
    char *modifiedby_user;             /* _ARGO: string,                    synonym: modifiedByUser */
    int32_t is_client_audit;           /* _ARGO: integer                    synonym: isClientAudit, synonym: clientAuditUpdate */
};

/*
 * Authentication log for assistant
 *
 * g_cst - The customer for the log.
 *
 * g_ast - The assistant ID.
 *
 * type - type of connection for this log
 *        ZPN_ASSISTANT_BROKER_CONTROL
 *        ZPN_ASSISTANT_PBROKER_CONTROL
 *        ZPN_ASSISTANT_BROKER_DATA
 *        ZPN_ASSISTANT_BROKER_CONFIG
 *
 * c_pub_ip - The client public IP.
 *
 * c_priv_ip (opt) - The client private IP.
 *
 * c_lat - Latitude of public IP.
 *
 * c_lon - Longitude of public IP.
 *
 * g_cc - Country code.
 *
 * g_brk - The broker generating this message.
 *
 * auth_us - Time the authentication is checked.
 *
 * deauth_us - Time the client disconnect itself from broker
 *
 * first_log - this flag is set if the auth log for this assistant is the first from this broker's pov. Note that any
 * delta values that are calculated by this broker is supposed to be dirty if this flag is set as the broker may not
 * have had the complete picture to calculate the logs.
 *
 * result - 1: success, 0: fail
 *
 * Note: sys_uptime_s - synonym HostUpTime will be deprecated.
 */
struct zpn_ast_auth_log {                       /* _ARGO: object_definition */
    int64_t log_date;                           /* _ARGO: integer, adj_time,         synonym: LogTimestamp */
    char *version;                              /* _ARGO: string,                    synonym: Version */
    char *sarge_version;                        /* _ARGO: string,                    synonym: PackageVersion */

    int64_t g_cst;                              /* _ARGO: integer, stringify,        synonym: Customer */
    int64_t g_microtenant;                      /* _ARGO: integer, stringify,  nozero        synonym: MicroTenantID */
    int64_t g_ast;                              /* _ARGO: integer, stringify,        synonym: Connector */
    int64_t g_brk;                              /* _ARGO: integer, stringify,        synonym: ZEN */
    int64_t g_pbrk;                             /* _ARGO: integer, stringify,  nozero        synonym: ServiceEdge */
    int64_t g_ast_grp;                          /* _ARGO: integer, stringify,        synonym: ConnectorGroup */

    int64_t *gids;                              /* _ARGO: integer */
    int gids_count;                             /* _ARGO: integer, quiet, count: gids */

    struct argo_inet priv_ip;                   /* _ARGO: inet,                      synonym: PrivateIP */
    struct argo_inet pub_ip;                    /* _ARGO: inet,                      synonym: PublicIP */
    int32_t pub_port;                           /* _ARGO: integer,                   synonym: PublicPort */
    double a_lat;                               /* _ARGO: double,                    synonym: Latitude */
    double a_lon;                               /* _ARGO: double,                    synonym: Longitude */
    char *cc;                                   /* _ARGO: string,                    synonym: CountryCode */

    /* New fields for QBR */
    char *g_ast_city;                           /* _ARGO: string */
    char *g_ast_country_code;                   /* _ARGO: string */
    char *g_ast_subdivision_code;               /* _ARGO: string */
    const char *g_ast_public_cloud;             /* _ARGO: string */
    const char *g_ast_private_cloud;            /* _ARGO: string */
    const char *g_ast_region_id;                /* _ARGO: string */

    char *type;                                 /* _ARGO: string,                    synonym: SessionType */
    int64_t auth_us;                            /* _ARGO: integer, adj_time,         synonym: TimestampAuthentication */
    int64_t deauth_us;                          /* _ARGO: integer, adj_time          synonym: TimestampUnAuthentication */
    char *status;                               /* _ARGO: string,                    synonym: SessionStatus */

    int32_t tcpi_snd_mss;                       /* _ARGO: integer */
    int32_t tcpi_rcv_mss;                       /* _ARGO: integer */
    int32_t tcpi_rtt;                           /* _ARGO: integer */
    int32_t tcpi_rttvar;                        /* _ARGO: integer */
    int32_t tcpi_snd_cwnd;                      /* _ARGO: integer */
    int32_t tcpi_advmss;                        /* _ARGO: integer */
    int32_t tcpi_reordering;                    /* _ARGO: integer */
    int32_t tcpi_rcv_rtt;                       /* _ARGO: integer */
    int32_t tcpi_rcv_space;                     /* _ARGO: integer */
    int32_t tcpi_total_retrans;                 /* _ARGO: integer */
    int64_t tcpi_thru_put;                      /* _ARGO: integer */
    int32_t tcpi_unacked;                       /* _ARGO: integer */
    int32_t tcpi_sacked;                        /* _ARGO: integer */
    int32_t tcpi_lost;                          /* _ARGO: integer */
    int64_t tcpi_fackets;                       /* _ARGO: integer */

    int32_t cpu_util;                           /* _ARGO: integer,                   synonym: CPUUtilization */
    int32_t mem_util;                           /* _ARGO: integer,                   synonym: MemUtilization */
    int32_t service_count;                      /* _ARGO: integer,                   synonym: ServiceCount */
    char *dft_rt_intf;                          /* _ARGO: string,                    synonym: InterfaceDefRoute */
    struct argo_inet dft_rt_gw;                 /* _ARGO: inet,                      synonym: DefRouteGW */
    struct argo_inet resolver;                  /* _ARGO: inet,                      synonym: PrimaryDNSResolver */
    int64_t sys_uptime_s;                       /* _ARGO: integer,                   synonym: HostStartTime, synonym: HostUpTime */
    int64_t ast_uptime_s;                       /* _ARGO: integer,                   synonym: ConnectorStartTime, synonym: ConnectorUpTime */
    int intf_count;                             /* _ARGO: integer,                   synonym: NumOfInterfaces */
    int64_t intf_rb;                            /* _ARGO: integer,                   synonym: BytesRxInterface  */
    int64_t intf_rp;                            /* _ARGO: integer,                   synonym: PacketsRxInterface */
    int64_t intf_re;                            /* _ARGO: integer,                   synonym: ErrorsRxInterface */
    int64_t intf_rd;                            /* _ARGO: integer,                   synonym: DiscardsRxInterface */
    int64_t intf_tb;                            /* _ARGO: integer,                   synonym: BytesTxInterface */
    int64_t intf_tp;                            /* _ARGO: integer,                   synonym: PacketsTxInterface */
    int64_t intf_te;                            /* _ARGO: integer,                   synonym: ErrorsTxInterface */
    int64_t intf_td;                            /* _ARGO: integer,                   synonym: DiscardsTxInterface */

    char *platform;                             /* _ARGO: string,                    synonym: Platform */
    char *platform_detail;                      /* _ARGO: string,                    synonym: PlatformDetail */
    char *runtime_os;                           /* _ARGO: string,                    synonym: RuntimeOS */

    uint32_t tcpi_last_data_sent;               /* _ARGO: integer */
    uint32_t tcpi_last_ack_sent;                /* _ARGO: integer */
    uint32_t tcpi_last_data_recv;               /* _ARGO: integer */
    uint32_t tcpi_last_ack_recv;                /* _ARGO: integer */

    uint64_t tcpi_bytes_acked;                  /* _ARGO: integer */
    uint64_t tcpi_bytes_received;               /* _ARGO: integer */
    uint64_t tcpi_segs_out;                     /* _ARGO: integer */
    uint64_t tcpi_segs_in;                      /* _ARGO: integer */

    uint64_t transmit_bytes;                    /* _ARGO: integer,                   synonym: TotalBytesTx */
    uint64_t receive_bytes;                     /* _ARGO: integer,                   synonym: TotalBytesRx */
    uint64_t transmit_objects;                  /* _ARGO: integer */
    uint64_t receive_objects;                   /* _ARGO: integer */
    uint64_t transmit_raw_tlv;                  /* _ARGO: integer */
    uint64_t receive_raw_tlv;                   /* _ARGO: integer */

    const char *tunnel_id;                      /* _ARGO: string,                    synonym: SessionID */
    const char *auth_type;                      /* _ARGO: string */
    const char *close_reason;                   /* _ARGO: string */

    int32_t app_rtt_us;                                     /* _ARGO: integer */
    int32_t app_rtt[FOHH_HISTOGRAM_MAX_BUCKETS];            /* _ARGO: integer */
    int app_rtt_count;                                      /* _ARGO: quiet, integer, count: app_rtt */
    int32_t delta_app_rtt[FOHH_HISTOGRAM_MAX_BUCKETS];      /* _ARGO: integer */
    int delta_app_rtt_count;                                /* _ARGO: quiet, integer, count: delta_app_rtt */
    int32_t tcp_rtt_us;                                     /* _ARGO: integer */
    int32_t tcp_rtt[FOHH_HISTOGRAM_MAX_BUCKETS];            /* _ARGO: integer */
    int tcp_rtt_count;                                      /* _ARGO: quiet, integer, count: tcp_rtt */
    int32_t tcp_congestion_win[FOHH_HISTOGRAM_MAX_BUCKETS]; /* _ARGO: integer */
    int tcp_congestion_win_count;                           /* _ARGO: quiet, integer, count: tcp_congestion_win */

    int32_t first_log;                           /* _ARGO: integer */

    int64_t current_target_count;                /* _ARGO: integer */
    int64_t total_mtunnel_count;                 /* _ARGO: integer */
    int64_t delta_mtunnel_count;                 /* _ARGO: integer */

    int64_t delta_intf_rb;                       /* _ARGO: integer */
    int64_t delta_intf_rp;                       /* _ARGO: integer */
    int64_t delta_intf_re;                       /* _ARGO: integer */
    int64_t delta_intf_rd;                       /* _ARGO: integer */
    int64_t delta_intf_tb;                       /* _ARGO: integer */
    int64_t delta_intf_tp;                       /* _ARGO: integer */
    int64_t delta_intf_te;                       /* _ARGO: integer */
    int64_t delta_intf_td;                       /* _ARGO: integer */

    int64_t total_intf_b;                        /* _ARGO: integer */
    int64_t total_intf_p;                        /* _ARGO: integer */
    int64_t total_intf_e;                        /* _ARGO: integer */
    int64_t total_intf_d;                        /* _ARGO: integer */
    int64_t delta_total_intf_b;                  /* _ARGO: integer */
    int64_t delta_total_intf_p;                  /* _ARGO: integer */
    int64_t delta_total_intf_e;                  /* _ARGO: integer */
    int64_t delta_total_intf_d;                  /* _ARGO: integer */

    char log_broker_cn[FOHH_MAX_NAMELEN];        /* _ARGO: string */
    char *tlv_type;                              /* _ARGO: string */

    /* ZRDT related states */
    int64_t streams_created;                      /* _ARGO: integer */
    int64_t streams_destroyed;                    /* _ARGO: integer */
    int64_t packets_sent;                        /* _ARGO: integer */
    int64_t packets_recv;                        /* _ARGO: integer */
    int64_t peer_loss;                           /* _ARGO: integer */
    int64_t local_loss;                          /* _ARGO: integer */

    int32_t zrdt_rtt[ZRDT_CONN_HISTO_SIZE];      /* _ARGO: integer */
    int zrdt_rtt_count;                          /* _ARGO: quiet, integer, count: zrdt_rtt */
    char ovd_broker_cn[FOHH_MAX_NAMELEN];        /* _ARGO: string */
    char features[TCP_QUICKACK_FEATURE_MAXLEN];  /* _ARGO: string */

    const char *log_type;                        /* _ARGO: string,  synonym: ConnectionLogType */
    char restart_reason[256];                    /* _ARGO: string */
    int64_t report_restart_epoch_s;              /* _ARGO: integer */

    char *guacd_version;                         /* _ARGO: string */
    char *slogger_info;                          /* _ARGO: string */
    int64_t sitec_id;                            /* _ARGO: integer, stringify,      synonym: SiteController */

    int connector_type;                          /* _ARGO: integer */
    int64_t np_connector_gid;                    /* _ARGO: integer */
    int64_t np_connector_gid_str;                /* _ARGO: integer, stringify */
    int np_connector_state;                      /* _ARGO: integer */
    char *frr_version;                           /* _ARGO: string */

    char *platform_arch;                         /* _ARGO: string */
    uint64_t last_os_upgrade_time;                /* _ARGO: integer */
    uint64_t last_sarge_upgrade_time;             /* _ARGO: integer */
    char *platform_version;                      /* _ARGO: string */
};

/*
 * Authentication log for private broker
 *
 * g_cst - The customer for the log.
 *
 * g_ast - The assistant ID.
 *
 * type - type of connection for this log
 *        ZPN_ASSISTANT_BROKER_CONTROL
 *        ZPN_ASSISTANT_BROKER_DATA
 *        ZPN_ASSISTANT_BROKER_CONFIG
 *
 * c_pub_ip - The client public IP.
 *
 * c_priv_ip (opt) - The client private IP.
 *
 * c_lat - Latitude of public IP.
 *
 * c_lon - Longitude of public IP.
 *
 * g_cc - Country code.
 *
 * g_brk - The broker generating this message.
 *
 * auth_us - Time the authentication is checked.
 *
 * deauth_us - Time the client disconnect itself from broker
 *
 * result - 1: success, 0: fail
 */
struct zpn_sys_auth_log {                       /* _ARGO: object_definition */
    int64_t log_date;                           /* _ARGO: integer, adj_time,         synonym: LogTimestamp */
    char *version;                              /* _ARGO: string,                    synonym: Version */
    char *sarge_version;                        /* _ARGO: string,                    synonym: PackageVersion */

    int64_t gid;                                /* _ARGO: integer, stringify,        synonym: ServiceEdge */
    int64_t grp_gid;                            /* _ARGO: integer, stringify,        synonym: ServiceEdgeGroup */
    int64_t g_brk;                              /* _ARGO: integer, stringify,        synonym: ZEN */
    int64_t g_cst;                              /* _ARGO: integer, stringify,        synonym: Customer */
    int64_t g_microtenant;                      /* _ARGO: integer, stringify, nozero       synonym: MicroTenantID */

    struct argo_inet priv_ip;                   /* _ARGO: inet,                      synonym: PrivateIP */
    struct argo_inet pub_ip;                    /* _ARGO: inet,                      synonym: PublicIP */
    int32_t pub_port;                           /* _ARGO: integer,                   synonym: PublicPort */
    double lat;                                 /* _ARGO: double,                    synonym: Latitude */
    double lon;                                 /* _ARGO: double,                    synonym: Longitude */
    char *cc;                                   /* _ARGO: string,                    synonym: CountryCode */

    char *tunnel_type;                          /* _ARGO: string,                    synonym: SessionType */
    int64_t auth_us;                            /* _ARGO: integer, adj_time,         synonym: TimestampAuthentication */
    int64_t deauth_us;                          /* _ARGO: integer, adj_time,         synonym: TimestampUnAuthentication */
    char *status;                               /* _ARGO: string,                    synonym: SessionStatus */

    int32_t tcpi_snd_mss;                       /* _ARGO: integer */
    int32_t tcpi_rcv_mss;                       /* _ARGO: integer */
    int32_t tcpi_rtt;                           /* _ARGO: integer */
    int32_t tcpi_rttvar;                        /* _ARGO: integer */
    int32_t tcpi_snd_cwnd;                      /* _ARGO: integer */
    int32_t tcpi_advmss;                        /* _ARGO: integer */
    int32_t tcpi_reordering;                    /* _ARGO: integer */
    int32_t tcpi_rcv_rtt;                       /* _ARGO: integer */
    int32_t tcpi_rcv_space;                     /* _ARGO: integer */
    int32_t tcpi_total_retrans;                 /* _ARGO: integer */
    int64_t tcpi_thru_put;                      /* _ARGO: integer */
    int32_t tcpi_unacked;                       /* _ARGO: integer */
    int32_t tcpi_sacked;                        /* _ARGO: integer */
    int32_t tcpi_lost;                          /* _ARGO: integer */
    int64_t tcpi_fackets;                       /* _ARGO: integer */

    int32_t cpu_util;                           /* _ARGO: integer,                   synonym: CPUUtilization */
    int32_t mem_util;                           /* _ARGO: integer,                   synonym: MemUtilization */
    int64_t number_of_clients;                  /* _ARGO: integer,                   synonym: ClientsTotal */

    /* precentage of UDP port utilization */
    int32_t udp4_port_util;                     /* _ARGO: integer */
    int32_t udp6_port_util;                     /* _ARGO: integer */

    /* precentage of system FD used */
    int32_t sys_fd_util;                        /* _ARGO: integer */

    /* precentage of process FD used */
    int32_t proc_fd_util;                       /* _ARGO: integer */

    char *dft_rt_intf;                          /* _ARGO: string,                    synonym: InterfaceDefRoute */
    struct argo_inet dft_rt_gw;                 /* _ARGO: inet,                      synonym: DefRouteGW */
    struct argo_inet resolver;                  /* _ARGO: inet,                      synonym: PrimaryDNSResolver */
    int64_t vm_start_s;                         /* _ARGO: integer,                   synonym: HostStartTime, synonym: HostUpTime */
    int64_t app_start_s;                        /* _ARGO: integer,                   synonym: ServiceEdgeStartTime */
    int intf_count;                             /* _ARGO: integer,                   synonym: NumOfInterfaces */
    int64_t intf_rb;                            /* _ARGO: integer,                   synonym: BytesRxInterface  */
    int64_t intf_rp;                            /* _ARGO: integer,                   synonym: PacketsRxInterface */
    int64_t intf_re;                            /* _ARGO: integer,                   synonym: ErrorsRxInterface */
    int64_t intf_rd;                            /* _ARGO: integer,                   synonym: DiscardsRxInterface */
    int64_t intf_tb;                            /* _ARGO: integer,                   synonym: BytesTxInterface */
    int64_t intf_tp;                            /* _ARGO: integer,                   synonym: PacketsTxInterface */
    int64_t intf_te;                            /* _ARGO: integer,                   synonym: ErrorsTxInterface */
    int64_t intf_td;                            /* _ARGO: integer,                   synonym: DiscardsTxInterface */

    int64_t delta_intf_rb;                       /* _ARGO: integer */
    int64_t delta_intf_rp;                       /* _ARGO: integer */
    int64_t delta_intf_re;                       /* _ARGO: integer */
    int64_t delta_intf_rd;                       /* _ARGO: integer */
    int64_t delta_intf_tb;                       /* _ARGO: integer */
    int64_t delta_intf_tp;                       /* _ARGO: integer */
    int64_t delta_intf_te;                       /* _ARGO: integer */
    int64_t delta_intf_td;                       /* _ARGO: integer */

    int64_t total_intf_b;                        /* _ARGO: integer */
    int64_t total_intf_p;                        /* _ARGO: integer */
    int64_t total_intf_e;                        /* _ARGO: integer */
    int64_t total_intf_d;                        /* _ARGO: integer */
    int64_t delta_total_intf_b;                  /* _ARGO: integer */
    int64_t delta_total_intf_p;                  /* _ARGO: integer */
    int64_t delta_total_intf_e;                  /* _ARGO: integer */
    int64_t delta_total_intf_d;                  /* _ARGO: integer */

    char *platform;                             /* _ARGO: string,                    synonym: Platform */
    char *platform_detail;                      /* _ARGO: string,                    synonym: PlatformDetail */
    char *runtime_os;                           /* _ARGO: string,                    synonym: RuntimeOS */

    uint32_t tcpi_last_data_sent;               /* _ARGO: integer */
    uint32_t tcpi_last_ack_sent;                /* _ARGO: integer */
    uint32_t tcpi_last_data_recv;               /* _ARGO: integer */
    uint32_t tcpi_last_ack_recv;                /* _ARGO: integer */

    uint64_t tcpi_bytes_acked;                  /* _ARGO: integer */
    uint64_t tcpi_bytes_received;               /* _ARGO: integer */
    uint64_t tcpi_segs_out;                     /* _ARGO: integer */
    uint64_t tcpi_segs_in;                      /* _ARGO: integer */

    uint64_t transmit_bytes;                    /* _ARGO: integer,                   synonym: TotalBytesTx */
    uint64_t receive_bytes;                     /* _ARGO: integer,                   synonym: TotalBytesRx */
    uint64_t transmit_objects;                  /* _ARGO: integer */
    uint64_t receive_objects;                   /* _ARGO: integer */
    uint64_t transmit_raw_tlv;                  /* _ARGO: integer */
    uint64_t receive_raw_tlv;                   /* _ARGO: integer */

    const char *tunnel_id;                      /* _ARGO: string,                    synonym: SessionID */
    const char *client_type;                    /* _ARGO: string */
    const char *auth_type;                      /* _ARGO: string */
    const char *close_reason;                   /* _ARGO: string */

    int32_t app_rtt_us;                                     /* _ARGO: integer */
    int32_t app_rtt[FOHH_HISTOGRAM_MAX_BUCKETS];            /* _ARGO: integer */
    int app_rtt_count;                                      /* _ARGO: quiet, integer, count: app_rtt */
    int32_t tcp_rtt_us;                                     /* _ARGO: integer */
    int32_t tcp_rtt[FOHH_HISTOGRAM_MAX_BUCKETS];            /* _ARGO: integer */
    int tcp_rtt_count;                                      /* _ARGO: quiet, integer, count: tcp_rtt */
    int32_t tcp_congestion_win[FOHH_HISTOGRAM_MAX_BUCKETS]; /* _ARGO: integer */
    int tcp_congestion_win_count;                           /* _ARGO: quiet, integer, count: tcp_congestion_win */
    char ovd_broker_cn[FOHH_MAX_NAMELEN];        /* _ARGO: string */

    char **log_brokers_uniq;                  /* _ARGO: string */
    int log_brokers_uniq_count;               /* _ARGO: quiet, integer, count:log_brokers_uniq */

    char **all_log_brokers_data;              /* _ARGO: string */
    int all_log_brokers_data_count;           /* _ARGO: quiet, integer, count:all_log_brokers_data */

    char *tlv_type;                              /* _ARGO: string */
    char *geoip_version;                      /* _ARGO: string */
    char *isp_version;                        /* _ARGO: string */
    char *slogger_info;                       /* _ARGO: string */
    int64_t g_sitec;                          /* _ARGO: integer, stringify,        synonym: sitec */
    char *platform_arch;                      /* _ARGO: string */
    int64_t sitec_id;                         /* _ARGO: integer, stringify,        synonym: SiteController */
    uint64_t last_os_upgrade_time;             /* _ARGO: integer */
    uint64_t last_sarge_upgrade_time;          /* _ARGO: integer */
    char *platform_version;                   /* _ARGO: string */
};


/* site controller auth log*/
struct zpn_sitec_auth_log {                        /* _ARGO: object_definition */
    int64_t log_date;                           /* _ARGO: integer, adj_time,         synonym: LogTimestamp */
    char *version;                              /* _ARGO: string,                    synonym: Version */
    char *sarge_version;                        /* _ARGO: string,                    synonym: PackageVersion */

    int64_t gid;                                /* _ARGO: integer, stringify,        synonym: SiteController */
    int64_t grp_gid;                            /* _ARGO: integer, stringify,        synonym: SiteControllerGroup */
    int64_t g_brk;                              /* _ARGO: integer, stringify,        synonym: ZEN */
    int64_t g_cst;                              /* _ARGO: integer, stringify,        synonym: Customer */
    int64_t g_microtenant;                      /* _ARGO: integer, stringify, nozero       synonym: MicroTenantID */

    struct argo_inet priv_ip;                   /* _ARGO: inet,                      synonym: PrivateIP */
    struct argo_inet pub_ip;                    /* _ARGO: inet,                      synonym: PublicIP */
    int32_t pub_port;                           /* _ARGO: integer,                   synonym: PublicPort */
    double lat;                                 /* _ARGO: double,                    synonym: Latitude */
    double lon;                                 /* _ARGO: double,                    synonym: Longitude */
    char *cc;                                   /* _ARGO: string,                    synonym: CountryCode */

    char *tunnel_type;                          /* _ARGO: string,                    synonym: SessionType */
    int64_t auth_us;                            /* _ARGO: integer, adj_time,         synonym: TimestampAuthentication */
    int64_t deauth_us;                          /* _ARGO: integer, adj_time,         synonym: TimestampUnAuthentication */
    char *status;                               /* _ARGO: string,                    synonym: SessionStatus */

    int32_t tcpi_snd_mss;                       /* _ARGO: integer */
    int32_t tcpi_rcv_mss;                       /* _ARGO: integer */
    int32_t tcpi_rtt;                           /* _ARGO: integer */
    int32_t tcpi_rttvar;                        /* _ARGO: integer */
    int32_t tcpi_snd_cwnd;                      /* _ARGO: integer */
    int32_t tcpi_advmss;                        /* _ARGO: integer */
    int32_t tcpi_reordering;                    /* _ARGO: integer */
    int32_t tcpi_rcv_rtt;                       /* _ARGO: integer */
    int32_t tcpi_rcv_space;                     /* _ARGO: integer */
    int32_t tcpi_total_retrans;                 /* _ARGO: integer */
    int64_t tcpi_thru_put;                      /* _ARGO: integer */
    int32_t tcpi_unacked;                       /* _ARGO: integer */
    int32_t tcpi_sacked;                        /* _ARGO: integer */
    int32_t tcpi_lost;                          /* _ARGO: integer */
    int64_t tcpi_fackets;                       /* _ARGO: integer */

    int32_t cpu_util;                           /* _ARGO: integer,                   synonym: CPUUtilization */
    int32_t mem_util;                           /* _ARGO: integer,                   synonym: MemUtilization */

    /* precentage of UDP port utilization */
    int32_t udp4_port_util;                     /* _ARGO: integer */
    int32_t udp6_port_util;                     /* _ARGO: integer */

    /* precentage of system FD used */
    int32_t sys_fd_util;                        /* _ARGO: integer */

    /* precentage of process FD used */
    int32_t proc_fd_util;                       /* _ARGO: integer */

    char *dft_rt_intf;                          /* _ARGO: string,                    synonym: InterfaceDefRoute */
    struct argo_inet dft_rt_gw;                 /* _ARGO: inet,                      synonym: DefRouteGW */
    struct argo_inet resolver;                  /* _ARGO: inet,                      synonym: PrimaryDNSResolver */
    int64_t vm_start_s;                         /* _ARGO: integer,                   synonym: HostStartTime, synonym: HostUpTime */
    int64_t app_start_s;                        /* _ARGO: integer,                   synonym: SiteControllerStartTime */
    int intf_count;                             /* _ARGO: integer,                   synonym: NumOfInterfaces */
    int64_t intf_rb;                            /* _ARGO: integer,                   synonym: BytesRxInterface  */
    int64_t intf_rp;                            /* _ARGO: integer,                   synonym: PacketsRxInterface */
    int64_t intf_re;                            /* _ARGO: integer,                   synonym: ErrorsRxInterface */
    int64_t intf_rd;                            /* _ARGO: integer,                   synonym: DiscardsRxInterface */
    int64_t intf_tb;                            /* _ARGO: integer,                   synonym: BytesTxInterface */
    int64_t intf_tp;                            /* _ARGO: integer,                   synonym: PacketsTxInterface */
    int64_t intf_te;                            /* _ARGO: integer,                   synonym: ErrorsTxInterface */
    int64_t intf_td;                            /* _ARGO: integer,                   synonym: DiscardsTxInterface */

    int64_t delta_intf_rb;                       /* _ARGO: integer */
    int64_t delta_intf_rp;                       /* _ARGO: integer */
    int64_t delta_intf_re;                       /* _ARGO: integer */
    int64_t delta_intf_rd;                       /* _ARGO: integer */
    int64_t delta_intf_tb;                       /* _ARGO: integer */
    int64_t delta_intf_tp;                       /* _ARGO: integer */
    int64_t delta_intf_te;                       /* _ARGO: integer */
    int64_t delta_intf_td;                       /* _ARGO: integer */

    int64_t total_intf_b;                        /* _ARGO: integer */
    int64_t total_intf_p;                        /* _ARGO: integer */
    int64_t total_intf_e;                        /* _ARGO: integer */
    int64_t total_intf_d;                        /* _ARGO: integer */
    int64_t delta_total_intf_b;                  /* _ARGO: integer */
    int64_t delta_total_intf_p;                  /* _ARGO: integer */
    int64_t delta_total_intf_e;                  /* _ARGO: integer */
    int64_t delta_total_intf_d;                  /* _ARGO: integer */

    char *platform;                             /* _ARGO: string,                    synonym: Platform */
    char *platform_detail;                      /* _ARGO: string,                    synonym: PlatformDetail */
    char *runtime_os;                           /* _ARGO: string,                    synonym: RuntimeOS */

    uint32_t tcpi_last_data_sent;               /* _ARGO: integer */
    uint32_t tcpi_last_ack_sent;                /* _ARGO: integer */
    uint32_t tcpi_last_data_recv;               /* _ARGO: integer */
    uint32_t tcpi_last_ack_recv;                /* _ARGO: integer */

    uint64_t tcpi_bytes_acked;                  /* _ARGO: integer */
    uint64_t tcpi_bytes_received;               /* _ARGO: integer */
    uint64_t tcpi_segs_out;                     /* _ARGO: integer */
    uint64_t tcpi_segs_in;                      /* _ARGO: integer */

    uint64_t transmit_bytes;                    /* _ARGO: integer,                   synonym: TotalBytesTx */
    uint64_t receive_bytes;                     /* _ARGO: integer,                   synonym: TotalBytesRx */
    uint64_t transmit_objects;                  /* _ARGO: integer */
    uint64_t receive_objects;                   /* _ARGO: integer */
    uint64_t transmit_raw_tlv;                  /* _ARGO: integer */
    uint64_t receive_raw_tlv;                   /* _ARGO: integer */

    const char *tunnel_id;                      /* _ARGO: string,                    synonym: SessionID */
    const char *client_type;                    /* _ARGO: string */
    const char *auth_type;                      /* _ARGO: string */
    const char *close_reason;                   /* _ARGO: string */

    int32_t app_rtt_us;                                     /* _ARGO: integer */
    int32_t app_rtt[FOHH_HISTOGRAM_MAX_BUCKETS];            /* _ARGO: integer */
    int app_rtt_count;                                      /* _ARGO: quiet, integer, count: app_rtt */
    int32_t tcp_rtt_us;                                     /* _ARGO: integer */
    int32_t tcp_rtt[FOHH_HISTOGRAM_MAX_BUCKETS];            /* _ARGO: integer */
    int tcp_rtt_count;                                      /* _ARGO: quiet, integer, count: tcp_rtt */
    int32_t tcp_congestion_win[FOHH_HISTOGRAM_MAX_BUCKETS]; /* _ARGO: integer */
    int tcp_congestion_win_count;                           /* _ARGO: quiet, integer, count: tcp_congestion_win */
    char ovd_broker_cn[FOHH_MAX_NAMELEN];        /* _ARGO: string */

    char **log_brokers_uniq;                  /* _ARGO: string */
    int log_brokers_uniq_count;               /* _ARGO: quiet, integer, count:log_brokers_uniq */

    char **all_log_brokers_data;              /* _ARGO: string */
    int all_log_brokers_data_count;           /* _ARGO: quiet, integer, count:all_log_brokers_data */

    char *tlv_type;                              /* _ARGO: string */
    char *geoip_version;                      /* _ARGO: string */
    char *isp_version;                        /* _ARGO: string */
    uint64_t last_os_upgrade_time;             /* _ARGO: integer */
    uint64_t last_sarge_upgrade_time;          /* _ARGO: integer */
    char *platform_version;                   /* _ARGO: string */
    char *status_conn_info;                   /* _ARGO: string */
    int64_t master_last_sync_time;           /* _ARGO: integer */
    int64_t shard_last_sync_time;            /* _ARGO: integer */
    int64_t userdb_last_sync_time;           /* _ARGO: integer */
};

/*
 * Authentication report for assistant, sent to Dispatcher
 */
struct zpn_ast_auth_report {                    /* _ARGO: object_definition */
    int64_t g_cst;                              /* _ARGO: integer */
    int64_t g_ast;                              /* _ARGO: integer */
    int64_t g_brk;                              /* _ARGO: integer */
    int64_t g_ast_grp;                          /* _ARGO: integer */

    double a_lat;                               /* _ARGO: double */
    double a_lon;                               /* _ARGO: double */

    int64_t auth_us;                            /* _ARGO: integer */
    int64_t deauth_us;                          /* _ARGO: integer */
    char *status;                               /* _ARGO: string */
    char **capabilities;                        /* _ARGO: string */
    int capabilities_count;                     /* _ARGO: quiet, integer, count:capabilities */
    int32_t cpu;                                /* _ARGO: integer */
    int32_t mem;                                /* _ARGO: integer */
    /* precentage of UDP port utilization */
    int32_t udp4_port_util;                     /* _ARGO: integer */
    int32_t udp6_port_util;                     /* _ARGO: integer */
    /* precentage of TCP port utilization */
    int32_t tcp4_port_util;                     /* _ARGO: integer */
    int32_t tcp6_port_util;                     /* _ARGO: integer */
    /* precentage of system FD used */
    int32_t sys_fd_util;                        /* _ARGO: integer */
    /* precentage of process FD used */
    int32_t proc_fd_util;                       /* _ARGO: integer */
    /* current target count */
    int64_t target;                             /* _ARGO: integer */
    /* cumulative mtunnel count */
    int64_t mtunnel;                            /* _ARGO: integer */
    int64_t disabled;                           /* _ARGO: integer */
};

/*
 * DNS test RPC for client to query if a domain is valid.
 *
 * 'name' should be a lower-case domain name only.
 *
 * 'type' can be 'A', 'AAAA', or 'SRV'.
 *
 * Note that 'A' and 'AAAA' are treated identically, as IPv4/v6
 * continuity can be deliberately switched on an end-to-end
 * connection.
 */

#define ZPN_DNS_CHECK_TARGETS_EQUAL(x) ((x->target_name_count == x->target_port_count) && \
                                        (x->target_name_count == x->target_priority_count) && \
                                        (x->target_name_count == x->target_weight_count))

//@formatter:off
struct zpn_app_client_check_full_app { /* _ARGO: object_definition */
    int64_t gid;                             /* _ARGO: integer */

    int *udp_ports_array;               /* _ARGO: integer */
    int *tcp_ports_array;               /* _ARGO: integer */

    /* These are NOT pair counts - these are array element counts */
    int udp_ports_count;                     /* _ARGO: integer, quiet, count: udp_ports_array */
    int tcp_ports_count;                     /* _ARGO: integer, quiet, count: tcp_ports_array */
};
//@formatter:on

struct zpn_app_client_check {                   /* _ARGO: object_definition */
    int64_t id;                                 /* _ARGO: integer */
    int64_t g_cst;                              /* _ARGO: integer, nozero, stringify */
    char *tunnel_id;                            /* _ARGO: string */
    char *name;                                 /* _ARGO: string */
    const char *target_name[ZPN_DNS_MAX_TARGETS]; /* _ARGO: string */
    int32_t target_port[ZPN_DNS_MAX_TARGETS];   /* _ARGO: integer */
    int32_t target_priority[ZPN_DNS_MAX_TARGETS]; /* _ARGO: integer */
    int32_t target_weight[ZPN_DNS_MAX_TARGETS]; /* _ARGO: integer */
    int target_name_count;                      /* _ARGO: quiet, integer, count: target_name */
    int target_port_count;                      /* _ARGO: quiet, integer, count: target_port */
    int target_priority_count;                  /* _ARGO: quiet, integer, count: target_priority */
    int target_weight_count;                    /* _ARGO: quiet, integer, count: target_weight */
    const char *cnames[ZPN_DNS_MAX_CNAMES];     /* _ARGO: string */
    int cnames_count;                           /* _ARGO: quiet, integer, count: cnames */
    const char *txts[ZPN_DNS_MAX_TXTS];         /* _ARGO: string */
    int txt_count;                              /* _ARGO: integer, count: txts */
    int64_t elapsed_us;                         /* _ARGO: integer, nozero */
    char *type;                                 /* _ARGO: string */
    char *error;                                /* _ARGO: string */
    int err_num;                                /* _ARGO: integer */
    int64_t ttl;                                /* _ARGO: integer */

    /* app check response fields */
    int32_t *ingress_port_ranges;               /* _ARGO: integer */
    int32_t port_ranges_count;                  /* _ARGO: quiet, integer, count: ingress_port_ranges */
    int32_t *tcp_port_ranges;                   /* _ARGO: integer */
    int32_t tcp_port_ranges_count;              /* _ARGO: quiet, integer, count: tcp_port_ranges */
    int32_t *udp_port_ranges;                   /* _ARGO: integer */
    int32_t udp_port_ranges_count;              /* _ARGO: quiet, integer, count: udp_port_ranges */
    int bypass;                                 /* _ARGO: integer */
    const char *icmp_access_type;               /* _ARGO: string */
    int ip_anchored;                            /* _ARGO: integer, nozero */
    int inspected;                              /* _ARGO: integer, nozero */
    int bypass_on_reauth;                       /* _ARGO: integer */
    uint32_t double_encrypt;                    /* _ARGO: integer */
    const char *bypass_type;                    /* _ARGO: string */
    /* it is whatever is matched FQDN or star domain in the diamond table */
    char *app_domain;                           /* _ARGO: string */

    uint8_t strict;                             /* _ARGO: integer */
    uint8_t has_a;                              /* _ARGO: integer */
    uint8_t has_aaaa;                           /* _ARGO: integer */

    /* This point to accumulate object which actually store array data for app check response */
    /* It should be freeed after response is sent out */
    void *accumulate;

    /* app scaling for Forwarding Clients */
    struct argo_object **app_segments;          /* _ARGO: argo_object*/
    size_t app_segments_count;                  /* _ARGO: integer, quiet, count: app_segments*/
};

struct zpn_dns_client_check {                   /* _ARGO: object_definition */
    int64_t id;                                 /* _ARGO: integer */
    int64_t g_cst;                              /* _ARGO: integer, nozero, stringify */
    char *tunnel_id;                            /* _ARGO: string */
    char *name;                                 /* _ARGO: string */
    const char *target_name[ZPN_DNS_MAX_TARGETS]; /* _ARGO: string */
    int32_t target_port[ZPN_DNS_MAX_TARGETS];   /* _ARGO: integer */
    int32_t target_priority[ZPN_DNS_MAX_TARGETS]; /* _ARGO: integer */
    int32_t target_weight[ZPN_DNS_MAX_TARGETS]; /* _ARGO: integer */
    int target_name_count;                      /* _ARGO: quiet, integer, count: target_name */
    int target_port_count;                      /* _ARGO: quiet, integer, count: target_port */
    int target_priority_count;                  /* _ARGO: quiet, integer, count: target_priority */
    int target_weight_count;                    /* _ARGO: quiet, integer, count: target_weight */
    const char *cnames[ZPN_DNS_MAX_CNAMES];     /* _ARGO: string */
    int cnames_count;                           /* _ARGO: quiet, integer, count: cnames */
    const char *txts[ZPN_DNS_MAX_TXTS];         /* _ARGO: string */
    int txt_count;                              /* _ARGO: integer, count: txts */
    int64_t elapsed_us;                         /* _ARGO: integer, nozero */
    char *type;                                 /* _ARGO: string */
    char *error;                                /* _ARGO: string */
    int64_t err_code;                           /* _ARGO: integer */
    uint8_t strict;                             /* _ARGO: integer */
    uint8_t has_a;                              /* _ARGO: integer */
    uint8_t has_aaaa;                           /* _ARGO: integer */
};

struct zpn_dns_dispatch_check {                 /* _ARGO: object_definition */
    int64_t id;                                 /* _ARGO: integer */
    char *tunnel_id;                            /* _ARGO: string */
    int64_t g_brk;                              /* _ARGO: integer */
    int64_t g_app;                              /* _ARGO: integer */
    int64_t g_ast;                              /* _ARGO: integer */
    char *name;                                 /* _ARGO: string */
    const char *target_name[ZPN_DNS_MAX_TARGETS]; /* _ARGO: string */
    int32_t target_port[ZPN_DNS_MAX_TARGETS];   /* _ARGO: integer */
    int32_t target_priority[ZPN_DNS_MAX_TARGETS]; /* _ARGO: integer */
    int32_t target_weight[ZPN_DNS_MAX_TARGETS]; /* _ARGO: integer */
    int target_name_count;                      /* _ARGO: quiet, integer, count: target_name */
    int target_port_count;                      /* _ARGO: quiet, integer, count: target_port */
    int target_priority_count;                  /* _ARGO: quiet, integer, count: target_priority */
    int target_weight_count;                    /* _ARGO: quiet, integer, count: target_weight */
    const char *cnames[ZPN_DNS_MAX_CNAMES];     /* _ARGO: string */
    int cnames_count;                           /* _ARGO: quiet, integer, count: cnames */
    const char *txts[ZPN_DNS_MAX_TXTS];         /* _ARGO: string */
    int txt_count;                              /* _ARGO: integer, count: txts */
    char *type;                                 /* _ARGO: string */
    char *error;                                /* _ARGO: string */
    double b_lat;                               /* _ARGO: double */
    double b_lon;                               /* _ARGO: double */
    int64_t scope_gid;                          /* _ARGO: integer */
    uint8_t strict;                             /* _ARGO: integer */
    uint8_t has_a;                              /* _ARGO: integer */
    uint8_t has_aaaa;                           /* _ARGO: integer */
    int32_t zia_org_id;                         /* _ARGO: integer */
    char *zia_cloud_name;                       /* _ARGO: string  */
    int8_t is_extranet_app;                     /* _ARGO: integer  */
    int32_t responded_zia_org_id;               /* _ARGO: integer */
    char *responded_zia_cloud_name;             /* _ARGO: string  */
    int64_t g_dsp;                              /* _ARGO: integer */
};

/*
 * Sent by dispatcher; received by connector; data filled up and sent by connector; received by dispatcher;
 */
struct zpn_dns_assistant_check {                /* _ARGO: object_definition */
    int64_t g_cst;                              /* _ARGO: integer, nozero */
    int64_t g_ast;                              /* _ARGO: integer, nozero */
    int64_t g_app;                              /* _ARGO: integer, nozero */
    int64_t g_dsp;                              /* _ARGO: integer, nozero */
    char *name;                                 /* _ARGO: string */
    const char **target_name;                   /* _ARGO: string */
    int32_t *target_port;                       /* _ARGO: integer */
    int32_t *target_priority;                   /* _ARGO: integer */
    int32_t *target_weight;                     /* _ARGO: integer */
    int target_name_count;                      /* _ARGO: quiet, integer, count: target_name */
    int target_port_count;                      /* _ARGO: quiet, integer, count: target_port */
    int target_priority_count;                  /* _ARGO: quiet, integer, count: target_priority */
    int target_weight_count;                    /* _ARGO: quiet, integer, count: target_weight */
    const char *cnames[ZPN_DNS_MAX_CNAMES];     /* _ARGO: string */
    int cnames_count;                           /* _ARGO: quiet, integer, count: cnames */
    const char *txts[ZPN_DNS_MAX_TXTS];         /* _ARGO: string */
    int txt_count;                              /* _ARGO: integer, count: txts */
    struct argo_inet *ips;                      /* _ARGO: inet */
    int ips_count;                              /* _ARGO: quiet, integer, count: ips */
    char *type;                                 /* _ARGO: string */
    char *error;                                /* _ARGO: string */
    int64_t dsp_tx_us;                          /* _ARGO: integer */
    int64_t cbrk_to_zia_tx_us;                  /* _ARGO: integer */
    int64_t zia_tx_us;                          /* _ARGO: integer */
    int64_t cbrk_to_ast_tx_us;                  /* _ARGO: integer */
    int64_t cbrk_to_dsp_tx_us;                  /* _ARGO: integer */
    int64_t ast_tx_us;                          /* _ARGO: integer */
    int32_t a_rcode;                            /* _ARGO: integer */
    int32_t aaaa_rcode;                         /* _ARGO: integer */
    int32_t srv_rcode;                          /* _ARGO: integer */
    int32_t txt_rcode;                          /* _ARGO: integer */
    uint8_t has_a;                              /* _ARGO: integer */
    uint8_t has_aaaa;                           /* _ARGO: integer */
    int64_t zia_partner_id;                     /* _ARGO: integer */
    int64_t zia_location_id;                    /* _ARGO: integer */
    int64_t zia_instance_id;                    /* _ARGO: integer */
    int64_t zia_tunnel_id;                      /* _ARGO: integer */
    int64_t zia_org_id;                         /* _ARGO: integer */
    char *zia_cloud_name;                       /* _ARGO: string  */
};

/*
 * zpn_broker_dispatcher_c2c_app_check
 */
struct zpn_broker_dispatcher_c2c_app_check { /* _ARGO: object_definition */
    int64_t    id;                       /* _ARGO: integer */
    int64_t    dsp_tx_us;                /* _ARGO: integer */
    const char *app_fqdn;                /* _ARGO: string */
    int64_t    customer_gid;             /* _ARGO: integer */
    int        result;                   /* _ARGO: integer */
    int64_t    expire_s;                 /* _ARGO: integer */
    const char *domain;                  /* _ARGO: string */
    const char *error;                   /* _ARGO: string */
};

/*
 * will be used as id for above query
 */
enum zpn_broker_dispatcher_c2c_request_type {
    zpn_request_type_normal = 0,
    zpn_request_type_refresh = 1,
    zpn_request_type_query = 2,
};

/*
 * This message is to control assistant log upload, it is sent between broker and assistant
 * If it is from broker to assistant, it is interpreted as a command
 * If it is from assistant to broker, it is interpreted as a status
 * g_ast:  GID of the assistant
 * type:   0 - upload command
 *         1 - flag command
 * upload: 0 - upload off
 *         1 - upload on
 * flag:   current debug flag if status, or bit (location number) to turn (on/off) if command
 */
struct zpn_assistant_log_control {              /* _ARGO: object_definition */
    int64_t g_ast;                              /* _ARGO: integer */
    int type;                                   /* _ARGO: integer */
    int upload;                                 /* _ARGO: integer */
    uint64_t flag;                               /* _ARGO: integer */
};

struct zpn_assistant_stats_control {            /* _ARGO: object_definition */
    int64_t g_ast;                              /* _ARGO: integer */
    int upload;                                 /* _ARGO: integer */
};

struct zpn_assistant_pvt_key_control {            /* _ARGO: object_definition */
    uint64_t cert_id;                             /* _ARGO: integer */
    uint64_t status;                             /* _ARGO: integer */
    char *enc_or_dec_pvt_key;                     /* _ARGO: string */
    char *domain;                                 /* _ARGO: string */
};

struct zpn_assistant_gen_cert_control { /* _ARGO: object_definition */
    uint64_t    cust_gid;           /* _ARGO: integer, stringify */
    uint64_t    app_gid;           /* _ARGO: integer, stringify */
    const char  *cn;               /* _ARGO: string */
};

struct zpn_assistant_app_cert_key_control { /* _ARGO: object_definition */
    uint64_t    status;                 /* _ARGO: integer, stringify */
    uint64_t    app_gid;                /* _ARGO: integer, stringify */
    const char  *cn;                    /* _ARGO: string */
    uint64_t    cert_id;                /* _ARGO: integer, stringify */
    const char  *app_cert;              /* _ARGO: string */
    const char  *dec_pvt_key;           /* _ARGO: string */
    uint64_t    validity;               /* _ARGO: integer, stringify */
    const char  *status_msg;             /* _ARGO: string */
};

/*
 * This message is to control pbroker log upload, it is sent between broker and pbroker
 * If it is from broker to pbroker, it is interpreted as a command
 * If it is from pbroker to broker, it is interpreted as a status
 * g_pbroker:  GID of pbroker
 * type:   0 - upload command
 *         1 - flag command
 * upload: 0 - upload off
 *         1 - upload on
 * flag:   current debug flag if status, or bit (location number) to turn (on/off) if command
 */
struct zpn_pbroker_log_control {                 /* _ARGO: object_definition */
    int64_t g_pbroker;                           /* _ARGO: integer */
    int type;                                    /* _ARGO: integer */
    int upload;                                  /* _ARGO: integer */
    uint64_t flag;                               /* _ARGO: integer */
};

struct zpn_pbroker_stats_control {               /* _ARGO: object_definition */
    int64_t g_pbroker;                           /* _ARGO: integer */
    int upload;                                  /* _ARGO: integer */
};

struct zpn_sitec_log_control {                 /* _ARGO: object_definition */
    int64_t sitec_gid;                           /* _ARGO: integer */
    int type;                                    /* _ARGO: integer */
    int upload;                                  /* _ARGO: integer */
    uint64_t flag;                               /* _ARGO: integer */
};

struct zpn_sitec_stats_control {               /* _ARGO: object_definition */
    int64_t sitec_gid;                           /* _ARGO: integer */
    int upload;                                  /* _ARGO: integer */
};

/*
 * In case of emergency where a connector is stuck without upgrade or in bad state, broker should be able to send
 * this restart message on the control channel.
 */
struct zpn_assistant_restart {                  /* _ARGO: object_definition */
    int64_t g_ast;                              /* _ARGO: integer */
};




/*
 * This message is used to report assistant health to connected broker. It is different
 * from zpn_health_report messages as those are for Apps.
 *
 * It is related to zpn_asst_environment_report
 * zpn_asst_environment_report have all the static/non-changing environment details that a assistant would like to
 * inform the broker about. Both control_broker & data_broker care about the static properties of a connector. Most
 * often, but not all the time, the static properties are the ones which will be used as a key for any of the search.
 *
 * zpn_assistant_status_report have all the dynamic properties of the assistant that it wants to tell the broker
 * about. Infact only the control_broker really cares about the dynamic properties.
 */
struct zpn_assistant_status_report {            /* _ARGO: object_definition */
    int64_t g_ast;                              /* _ARGO: integer */
    int cpu_util;                               /* _ARGO: integer */
    int mem_util;                               /* _ARGO: integer */
    int32_t udp4_port_util;                     /* _ARGO: integer */
    int32_t udp6_port_util;                     /* _ARGO: integer */
    int32_t tcp4_port_util;                     /* _ARGO: integer */
    int32_t tcp6_port_util;                     /* _ARGO: integer */
    int32_t sys_fd_util;                        /* _ARGO: integer */
    int32_t proc_fd_util;                       /* _ARGO: integer */
    int service_count;                          /* _ARGO: integer */
    char *dft_rt_intf;                          /* _ARGO: string */
    struct argo_inet dft_rt_gw;                 /* _ARGO: inet */
    struct argo_inet resolver;                  /* _ARGO: inet */
    int64_t sys_uptime_s;                       /* _ARGO: integer */
    int64_t ast_uptime_s;                       /* _ARGO: integer */
    int intf_count;                             /* _ARGO: integer */
    int64_t intf_rb;                            /* _ARGO: integer */
    int64_t intf_rp;                            /* _ARGO: integer */
    int64_t intf_re;                            /* _ARGO: integer */
    int64_t intf_rd;                            /* _ARGO: integer */
    int64_t intf_tb;                            /* _ARGO: integer */
    int64_t intf_tp;                            /* _ARGO: integer */
    int64_t intf_te;                            /* _ARGO: integer */
    int64_t intf_td;                            /* _ARGO: integer */
    const char *platform;                       /* _ARGO: string */
    const char *platform_detail;                /* _ARGO: string */
    const char *runtime_os;                     /* _ARGO: string */
    int64_t time_delta_us;                      /* _ARGO: integer */

    int64_t current_target_count;               /* _ARGO: integer */
    int64_t delta_mtunnel_count;                /* _ARGO: integer */
    int64_t total_mtunnel_count;                /* _ARGO: integer */

    int64_t delta_intf_rb;                       /* _ARGO: integer */
    int64_t delta_intf_rp;                       /* _ARGO: integer */
    int64_t delta_intf_re;                       /* _ARGO: integer */
    int64_t delta_intf_rd;                       /* _ARGO: integer */
    int64_t delta_intf_tb;                       /* _ARGO: integer */
    int64_t delta_intf_tp;                       /* _ARGO: integer */
    int64_t delta_intf_te;                       /* _ARGO: integer */
    int64_t delta_intf_td;                       /* _ARGO: integer */

    int64_t total_intf_b;                        /* _ARGO: integer */
    int64_t total_intf_p;                        /* _ARGO: integer */
    int64_t total_intf_e;                        /* _ARGO: integer */
    int64_t total_intf_d;                        /* _ARGO: integer */
    int64_t delta_total_intf_b;                  /* _ARGO: integer */
    int64_t delta_total_intf_p;                  /* _ARGO: integer */
    int64_t delta_total_intf_e;                  /* _ARGO: integer */
    int64_t delta_total_intf_d;                  /* _ARGO: integer */

    char log_broker_cn[FOHH_MAX_NAMELEN];        /* _ARGO: string */
    char ovd_broker_cn[FOHH_MAX_NAMELEN];        /* _ARGO: string */

    enum connector_type connector_type;          /* _ARGO: integer */
    enum np_connector_status np_state;           /* _ARGO: integer */
    int64_t np_connector_gid;                    /* _ARGO: integer */
    const char *platform_arch;                   /* _ARGO: string */
    uint64_t last_os_upgrade_time;               /* _ARGO: integer */
    uint64_t last_sarge_upgrade_time;            /* _ARGO: integer */
    char *platform_version;                      /* _ARGO: string */

    const char *frr_version;                     /* _ARGO: string */
};


/*
 * This message is used to report pbroker health to connected broker.
 * The message contains dynamic properties such as cpu_util and mem_util.
 * It is currently sent by the pbroker upstream every ZPN_LOAD_MONITOR_TIMER_SEC seconds
 */
struct zpn_pbroker_status_report {            /* _ARGO: object_definition */
    int64_t g_pbrk;                           /* _ARGO: integer */
    int cpu_util;                             /* _ARGO: integer */
    int mem_util;                             /* _ARGO: integer */
    int32_t udp4_port_util;                   /* _ARGO: integer */
    int32_t udp6_port_util;                   /* _ARGO: integer */
    int32_t sys_fd_util;                      /* _ARGO: integer */
    int32_t proc_fd_util;                     /* _ARGO: integer */
    int64_t sys_uptime_s;                     /* _ARGO: integer */
    int64_t pb_uptime_s;                      /* _ARGO: integer */
    char *dft_rt_intf;                          /* _ARGO: string */
    struct argo_inet dft_rt_gw;                 /* _ARGO: inet */
    struct argo_inet resolver;                  /* _ARGO: inet */
    int intf_count;                             /* _ARGO: integer */
    int64_t intf_rb;                            /* _ARGO: integer */
    int64_t intf_rp;                            /* _ARGO: integer */
    int64_t intf_re;                            /* _ARGO: integer */
    int64_t intf_rd;                            /* _ARGO: integer */
    int64_t intf_tb;                            /* _ARGO: integer */
    int64_t intf_tp;                            /* _ARGO: integer */
    int64_t intf_te;                            /* _ARGO: integer */
    int64_t intf_td;                            /* _ARGO: integer */
    const char *platform;                       /* _ARGO: string */
    int64_t time_delta_us;                      /* _ARGO: integer */

    int64_t current_target_count;               /* _ARGO: integer */
    int64_t delta_mtunnel_count;                /* _ARGO: integer */
    int64_t total_mtunnel_count;                /* _ARGO: integer */

    int64_t delta_intf_rb;                       /* _ARGO: integer */
    int64_t delta_intf_rp;                       /* _ARGO: integer */
    int64_t delta_intf_re;                       /* _ARGO: integer */
    int64_t delta_intf_rd;                       /* _ARGO: integer */
    int64_t delta_intf_tb;                       /* _ARGO: integer */
    int64_t delta_intf_tp;                       /* _ARGO: integer */
    int64_t delta_intf_te;                       /* _ARGO: integer */
    int64_t delta_intf_td;                       /* _ARGO: integer */

    int64_t total_intf_b;                        /* _ARGO: integer */
    int64_t total_intf_p;                        /* _ARGO: integer */
    int64_t total_intf_e;                        /* _ARGO: integer */
    int64_t total_intf_d;                        /* _ARGO: integer */
    int64_t delta_total_intf_b;                  /* _ARGO: integer */
    int64_t delta_total_intf_p;                  /* _ARGO: integer */
    int64_t delta_total_intf_e;                  /* _ARGO: integer */
    int64_t delta_total_intf_d;                  /* _ARGO: integer */

    char log_broker_cn[FOHH_MAX_NAMELEN];        /* _ARGO: string */
    char ovd_broker_cn[FOHH_MAX_NAMELEN];        /* _ARGO: string */
    char **log_brokers_uniq;                  /* _ARGO: string */
    int log_brokers_uniq_count;               /* _ARGO: quiet, integer, count:log_brokers_uniq */
    char **all_log_brokers_data;              /* _ARGO: string */
    int all_log_brokers_data_count;           /* _ARGO: quiet, integer, count:all_log_brokers_data */

    /* Load Datapoints for redirect by Load per Datapoint*/
    int64_t number_of_clients;                   /* _ARGO: integer */
    const char *platform_arch;                   /* _ARGO: string */
    uint64_t last_os_upgrade_time;                /* _ARGO: integer */
    uint64_t last_sarge_upgrade_time;             /* _ARGO: integer */
    char *platform_version;                      /* _ARGO: string */
};

struct zpn_sitec_status_report {            /* _ARGO: object_definition */
    int64_t sitec_gid;                        /* _ARGO: integer */
    int cpu_util;                             /* _ARGO: integer */
    int mem_util;                             /* _ARGO: integer */
    int32_t udp4_port_util;                   /* _ARGO: integer */
    int32_t udp6_port_util;                   /* _ARGO: integer */
    int32_t sys_fd_util;                      /* _ARGO: integer */
    int32_t proc_fd_util;                     /* _ARGO: integer */
    int64_t sys_uptime_s;                     /* _ARGO: integer */
    int64_t sc_uptime_s;                      /* _ARGO: integer */
    char *dft_rt_intf;                          /* _ARGO: string */
    struct argo_inet dft_rt_gw;                 /* _ARGO: inet */
    struct argo_inet resolver;                  /* _ARGO: inet */
    int intf_count;                             /* _ARGO: integer */
    int64_t intf_rb;                            /* _ARGO: integer */
    int64_t intf_rp;                            /* _ARGO: integer */
    int64_t intf_re;                            /* _ARGO: integer */
    int64_t intf_rd;                            /* _ARGO: integer */
    int64_t intf_tb;                            /* _ARGO: integer */
    int64_t intf_tp;                            /* _ARGO: integer */
    int64_t intf_te;                            /* _ARGO: integer */
    int64_t intf_td;                            /* _ARGO: integer */
    const char *platform;                       /* _ARGO: string */
    int64_t time_delta_us;                      /* _ARGO: integer */

    int64_t current_target_count;               /* _ARGO: integer */
    int64_t delta_mtunnel_count;                /* _ARGO: integer */
    int64_t total_mtunnel_count;                /* _ARGO: integer */

    int64_t delta_intf_rb;                       /* _ARGO: integer */
    int64_t delta_intf_rp;                       /* _ARGO: integer */
    int64_t delta_intf_re;                       /* _ARGO: integer */
    int64_t delta_intf_rd;                       /* _ARGO: integer */
    int64_t delta_intf_tb;                       /* _ARGO: integer */
    int64_t delta_intf_tp;                       /* _ARGO: integer */
    int64_t delta_intf_te;                       /* _ARGO: integer */
    int64_t delta_intf_td;                       /* _ARGO: integer */

    int64_t total_intf_b;                        /* _ARGO: integer */
    int64_t total_intf_p;                        /* _ARGO: integer */
    int64_t total_intf_e;                        /* _ARGO: integer */
    int64_t total_intf_d;                        /* _ARGO: integer */
    int64_t delta_total_intf_b;                  /* _ARGO: integer */
    int64_t delta_total_intf_p;                  /* _ARGO: integer */
    int64_t delta_total_intf_e;                  /* _ARGO: integer */
    int64_t delta_total_intf_d;                  /* _ARGO: integer */

    char log_broker_cn[FOHH_MAX_NAMELEN];        /* _ARGO: string */
    char ovd_broker_cn[FOHH_MAX_NAMELEN];        /* _ARGO: string */
    char **log_brokers_uniq;                  /* _ARGO: string */
    int log_brokers_uniq_count;               /* _ARGO: quiet, integer, count:log_brokers_uniq */
    char **all_log_brokers_data;              /* _ARGO: string */
    int all_log_brokers_data_count;           /* _ARGO: quiet, integer, count:all_log_brokers_data */
    uint64_t last_os_upgrade_time;                /* _ARGO: integer */
    uint64_t last_sarge_upgrade_time;             /* _ARGO: integer */
    char *platform_version;                      /* _ARGO: string */
    char *status_conn_info;                   /* _ARGO: string */
    int64_t master_last_sync_time;           /* _ARGO: integer */
    int64_t shard_last_sync_time;            /* _ARGO: integer */
    int64_t userdb_last_sync_time;           /* _ARGO: integer */
};

/*
 * This message is used to update windows size for FOHH TLV connections.
 *    - tag_id: tag id of the mconn. 0 means the whole FOHH connection
 *    - tx_limit:
 *    - remote_tx_len:
 */
struct zpn_fohh_tlv_window_update {             /* _ARGO: object_definition */
    int32_t tag_id;                             /* _ARGO: integer */
    int64_t tx_limit;                           /* _ARGO: integer */
    int64_t rx_data;                            /* _ARGO: integer */
};

struct zpn_fohh_tlv_window_update_batch {     /* _ARGO: object_definition */
    int32_t *tag_id;                          /* _ARGO: integer */
    int tag_id_count;                         /* _ARGO: integer, quiet, count: tag_id */
    int64_t *tx_limit;                        /* _ARGO: integer */
    int tx_limit_count;                       /* _ARGO: integer, quiet, count: tx_limit */
    int64_t *rx_data;                         /* _ARGO: integer */
    int rx_data_count;                        /* _ARGO: integer, quiet, count: rx_data */
};

/*
 * This message is sent between broker and dispatcher and allows the
 * broker to query some pertinent information about the status of the
 * dispatcher.
 */
struct zpn_dispatcher_status {               	/* _ARGO: object_definition */
    /* Number of brokers that are currently connected to the dispatcher: */
    int64_t connected_brokers;	      	      	/* _ARGO: integer */
    /* Epoch, in seconds, of the dispatcher start time */
    int64_t app_start_epoch;                    /* _ARGO: integer */
    /* Uptime, in seconds, of the dispatcher */
    int64_t app_uptime;                         /* _ARGO: integer */
    /* When set, broker won't send app route info to dispatcher */
    int32_t block_app_reg_msg;                  /* _ARGO: integer */
    /*
     * Indicates whether dispatcher is ready to accept requests from broker
     * 0 - Old dispatcher which doesn't know about this field yet
     * 1 - Not ready
     * 2 - Ready
     */
    int ready_status;                           /* _ARGO: integer */
};

/*
 * First message sent to dispatcher upon connection.
 */
struct zpn_broker_info {                        /* _ARGO: object_definition */
    int64_t g_brk;                              /* _ARGO: integer */
    const char *name;                           /* _ARGO: string */
    uint8_t channel_id;                         /* _ARGO: integer */
};

enum zpn_domain_type {
    zpn_domain_type_app             = 0,
    zpn_domain_type_user_portal     = 1,
    zpn_domain_type_pra_portal      = 2,
};

/*
 * Microtunnel transaction log
 */
struct zpn_http_trans_log {                     /* _ARGO: object_definition */
    int64_t log_date;                           /* _ARGO: integer,                   synonym: LogTimestamp */
    char *mtunnel_id;                           /* _ARGO: string,                    synonym: ConnectionID  */
    int64_t g_exp;                              /* _ARGO: integer, stringify,        synonym: Exporter */

    int64_t req_rx_start_us;                    /* _ARGO: integer,                   synonym: TimestampRequestReceiveStart */
    int64_t req_rx_hdr_done_us;                 /* _ARGO: integer,                   synonym: TimestampRequestReceiveHeaderFinish */
    int64_t req_rx_done_us;                     /* _ARGO: integer,                   synonym: TimestampRequestReceiveFinish */
    int64_t req_tx_start_us;                    /* _ARGO: integer,                   synonym: TimestampRequestTransmitStart */
    int64_t req_tx_done_us;                     /* _ARGO: integer,                   synonym: TimestampRequestTransmitFinish */
    int64_t rsp_rx_start_us;                    /* _ARGO: integer,                   synonym: TimestampResponseReceiveStart */
    int64_t rsp_rx_done_us;                     /* _ARGO: integer,                   synonym: TimestampResponseReceiveFinish */
    int64_t rsp_tx_start_us;                    /* _ARGO: integer,                   synonym: TimestampResponseTransmitStart */
    int64_t rsp_tx_done_us;                     /* _ARGO: integer,                   synonym: TimestampResponseTransmitFinish */

    int64_t req_rx_total_time_us;               /* _ARGO: integer,                   synonym: TotalTimeRequestReceive */
    int64_t req_tx_total_time_us;               /* _ARGO: integer,                   synonym: TotalTimeRequestTransmit */
    int64_t rsp_rx_total_time_us;               /* _ARGO: integer,                   synonym: TotalTimeResponseReceive */
    int64_t rsp_tx_total_time_us;               /* _ARGO: integer,                   synonym: TotalTimeResponseTransmit */
    int64_t req_tx_setup_time_us;               /* _ARGO: integer,                   synonym: TotalTimeConnectionSetup */
    int64_t req_tx_to_rsp_rx_time_us;           /* _ARGO: integer,                   synonym: TotalTimeServerResponse */

    const char *method;                         /* _ARGO: string,                    synonym: Method */
    const char *protocol;                       /* _ARGO: string,                    synonym: Protocol */
    const char *host;                           /* _ARGO: string,                    synonym: Host, synonym: Destination */
    const char *origin;                         /* _ARGO: string,                    synonym: Origin */
    const char *url;                            /* _ARGO: string,                    synonym: URL */
    const char *user_agent;                     /* _ARGO: string,                    synonym: UserAgent */
    const char *x_forwarded_for;                /* _ARGO: string,                    synonym: XFF */
    const char *forwarded;                      /* _ARGO: string,                    synonym: Forwarded */
    char *nameid;                               /* _ARGO: string,                    synonym: NameID */

    int response_status;                        /* _ARGO: integer,                   synonym: StatusCode */
    int64_t req_size;                           /* _ARGO: integer,                   synonym: RequestSize */
    int64_t rsp_size;                           /* _ARGO: integer,                   synonym: ResponseSize */

    int32_t port;                               /* _ARGO: integer,                   synonym: ApplicationPort */
    struct argo_inet client_public_ip;          /* _ARGO: inet,                      synonym: ClientPublicIp */
    int32_t client_public_port;                 /* _ARGO: integer,                   synonym: ClientPublicPort */
    struct argo_inet client_private_ip;         /* _ARGO: inet,                      synonym: ClientPrivateIp */

    int64_t customer_gid;                       /* _ARGO: integer, stringify,        synonym: Customer */
    const char *mt_status;                      /* _ARGO: string,                    synonym: ConnectionStatus */
    char *mt_reason;                            /* _ARGO: string,                    synonym: ConnectionReason */

    char *portal_api_status;                    /* _ARGO: string,                    synonym: PortalApiStatus */
    const char *cors_token_status;              /* _ARGO: string,                    synonym: CorsToken */
    const char *tls_fprint_status;              /* _ARGO: string,                    synonym: TerminationReason */

    /* CSP */
    /* Browser Info */
    char *br_name;                              /* _ARGO: string,                    synonym: BrowserName*/
    char *br_ver;                               /* _ARGO: string,                    synonym: BrowserVersion*/
    char *br_eng;                               /* _ARGO: string,                    synonym: BrowserEngine*/
    char *br_eng_ver;                           /* _ARGO: string,                    synonym: BrowserEngineVersion*/
    uint8_t is_local_strg;                      /* _ARGO: integer,                   synonym: LocalStorageEnabled*/
    uint8_t is_sess_strg;                       /* _ARGO: integer,                   synonym: SessionStorageEnabled*/
    uint8_t is_ck;                              /* _ARGO: integer,                   synonym: CookieEnabled*/
    char *pl_hash;                              /* _ARGO: string,                    synonym: PluginHash*/
    char *pl_str;                               /* _ARGO: string,                    synonym: SupportedPlugin*/
    uint32_t pl_cnt;                            /* _ARGO: integer,                   synonym: SupportedPluginCount*/
    uint8_t is_flsh;                            /* _ARGO: integer,                   synonym: FlashInstalled*/
    char *flsh_ver;                             /* _ARGO: string,                    synonym: FlashVersion*/
    uint8_t is_sl;                              /* _ARGO: integer,                   synonym: SilverLightInstalled*/
    char *sl_ver;                               /* _ARGO: string,                    synonym: SilverLightVersion*/
    uint8_t is_mime;                            /* _ARGO: integer,                   synonym: MIMESupport*/
    char *mime_hash;                            /* _ARGO: string,                    synonym: MIMEHASH*/
    char *fp_ua;                                /* _ARGO: string,                    synonym: ReceivedUserAgent*/
    char *cv_hash;                              /* _ARGO: string,                    synonym: CanvasHash*/

    /* System Info */
    char *arch;                                 /* _ARGO: string,                    synonym: CpuArchitecture*/
    char *os;                                   /* _ARGO: string,                    synonym: OperatingSystem*/
    char *os_ver;                               /* _ARGO: string,                    synonym: OperatingSystemVersion*/
    char *curr_scr_res;                         /* _ARGO: string,                    synonym: CurrentScreenResolution*/
    char *avail_scr_res;                        /* _ARGO: string,                    synonym: AvailableScreenResolution*/
    char *tz;                                   /* _ARGO: string,                    synonym: OperatingSystem*/
    char *usr_lang;                             /* _ARGO: string,                    synonym: UserLanguage*/
    char *sys_lang;                             /* _ARGO: string,                    synonym: SystemLanguage*/
    uint8_t is_mob;                             /* _ARGO: integer,                   synonym: MobileDevice*/
    char *mob_type;                             /* _ARGO: string,                    synonym: MobileDeviceType*/
    uint8_t is_java;                            /* _ARGO: integer,                   synonym: JavaInstalled*/
    char *java_ver;                             /* _ARGO: string,                    synonym: JavaVersion*/
    char *fnt_hash;                             /* _ARGO: string,                    synonym: FontHash*/
    uint32_t fnt_cnt;                           /* _ARGO: integer,                   synonym: FontCount*/

    /* Location Info */
    double lat;                                 /* _ARGO: double,                   synonym: Latitude*/
    double lon;                                 /* _ARGO: double,                   synonym: Longitude*/
    char *ja3_hash;                             /* _ARGO: string,                   synonym: JA3Hash*/
    char *ja3_hash_ex_srt;                      /* _ARGO: string,                   synonym: JA3HashExtnSorted*/
    char *dev_hash;                             /* _ARGO: string,                   synonym: DeviceHash*/
    uint8_t  cfg_mon;                           /* _ARGO: integer,                  synonym: MonitoringEnabled*/
    int64_t cfg_mask;                           /* _ARGO: integer,                  synonym: ConfiguredBrowserProtectionProfileMask*/
    int64_t profile_id;                         /* _ARGO: integer, stringify,       synonym: BrowserProtectionProfileID*/
    int64_t rule_gid;                           /* _ARGO: integer, stringify,       synonym: BrowserProtectionPolicyRuleID*/
    int64_t chg_mask;                           /* _ARGO: integer,                  synonym: ChangeDetectedMask*/
    uint8_t is_1st_bfp;                         /* _ARGO: integer,                  synonym: FirstFingerPrint*/
    uint8_t is_bfp_data;                        /* _ARGO: integer,                  synonym: FingerPrintDataPresent*/
    uint8_t domain_type;                        /* _ARGO: integer,                  synonym: DomainType*/
    uint64_t bfp_status;                        /* _ARGO: integer */
    char *m_bfp;                                /* _ARGO: string,                   synonym: MasterBrowserFingerPrintData */

    int64_t g_app;                              /* _ARGO: integer, stringify,       synonym: ApplicationSegmentId*/
    int64_t g_cst;                              /* _ARGO: integer, stringify,       synonym: CustomerGID */
    char  *fname;                               /* _ARGO: string,                   synonym: UserFirstName */
    char  *lname;                               /* _ARGO: string,                   synonym: UserLastName */

    /* To track similar HTTP requests across various mtunnels we neep context mappings */
    char  *session_id;                      /* _ARGO: string */
    char  *domain_id;                       /* _ARGO: string */
    /* CSP */
};

/*
 * Microtunnel transaction log
 */
struct zpn_pbroker_trans_log {                     /* _ARGO: object_definition */
    int64_t log_date;                           /* _ARGO: integer,                   synonym: LogTimestamp */
    char *mtunnel_id;                           /* _ARGO: string,                    synonym: ConnectionID  */
    int64_t g_brk;                              /* _ARGO: integer,                   synonym: Broker */
    int64_t g_pbrk;                             /* _ARGO: integer,  nozero           synonym: PrivateBroker */

    int64_t req_rx_start_us;                    /* _ARGO: integer,                   synonym: TimestampRequestReceiveStart */
    int64_t req_rx_done_us;                     /* _ARGO: integer,                   synonym: TimestampRequestReceiveFinish */
    int64_t req_tx_start_us;                    /* _ARGO: integer,                   synonym: TimestampRequestTransmitStart */
    int64_t req_tx_done_us;                     /* _ARGO: integer,                   synonym: TimestampRequestTransmitFinish */
    int64_t rsp_rx_start_us;                    /* _ARGO: integer,                   synonym: TimestampResponseReceiveStart */
    int64_t rsp_rx_done_us;                     /* _ARGO: integer,                   synonym: TimestampResponseReceiveFinish */
    int64_t rsp_tx_start_us;                    /* _ARGO: integer,                   synonym: TimestampResponseTransmitStart */
    int64_t rsp_tx_done_us;                     /* _ARGO: integer,                   synonym: TimestampResponseTransmitFinish */

    int64_t req_rx_total_time_us;               /* _ARGO: integer,                   synonym: TotalTimeRequestReceive */
    int64_t req_tx_total_time_us;               /* _ARGO: integer,                   synonym: TotalTimeRequestTransmit */
    int64_t rsp_rx_total_time_us;               /* _ARGO: integer,                   synonym: TotalTimeResponseReceive */
    int64_t rsp_tx_total_time_us;               /* _ARGO: integer,                   synonym: TotalTimeResponseTransmit */
    int64_t req_tx_setup_time_us;               /* _ARGO: integer,                   synonym: TotalTimeConnectionSetup */
    int64_t req_tx_to_rsp_rx_time_us;           /* _ARGO: integer,                   synonym: TotalTimeServerResponse */

    const char *protocol;                       /* _ARGO: string,                    synonym: Protocol */
    const char *host;                           /* _ARGO: string,                    synonym: Host, synonym: Destination */
    char *nameid;                               /* _ARGO: string,                    synonym: NameID */

    int response_status;                        /* _ARGO: integer,                   synonym: StatusCode */
    int64_t req_size;                           /* _ARGO: integer,                   synonym: RequestSize */
    int64_t rsp_size;                           /* _ARGO: integer,                   synonym: ResponseSize */

    int32_t port;                               /* _ARGO: integer,                   synonym: ApplicationPort */
    struct argo_inet client_public_ip;          /* _ARGO: inet,                      synonym: ClientPublicIp */
    int32_t client_public_port;                 /* _ARGO: integer,                   synonym: ClientPublicPort */
    struct argo_inet client_private_ip;         /* _ARGO: inet,                      synonym: ClientPrivateIp */

    int64_t customer_gid;                       /* _ARGO: integer,                   synonym: Customer */
    const char *mt_status;                      /* _ARGO: string,                    synonym: ConnectionStatus */
    char *mt_reason;                            /* _ARGO: string,                    synonym: ConnectionReason */
};



/*
 * Clientless App Query
 */
struct zpn_clientless_app_query {               /* _ARGO: object_definition */
    int32_t query_id;                           /* _ARGO: integer */
    char *app_name;                             /* _ARGO: string */
    int32_t tcp_server_port;                    /* _ARGO: integer */
    uint16_t ip_protocol;                       /* _ARGO: integer */
    uint16_t server_port;                       /* _ARGO: integer */
    int64_t publish_gid;                        /* _ARGO: integer */
};

struct zpn_clientless_app_query_ack {           /* _ARGO: object_definition */
    int32_t query_id;                           /* _ARGO: integer */
    const char *result;                         /* _ARGO: string */
    const char *reason;                         /* _ARGO: string */
};

/*
 * Assistant sends stats about data connections. Broker consumes it a. writes to disk b. sends via kafka for analytics.
 */
struct zpn_assistant_data_stats {                       /* _ARGO: object_definition */
    int64_t     cloud_time_us;                          /* _ARGO: integer */
    int64_t     active_conn_to_broker_fohh;             /* _ARGO: integer */
    int64_t     active_conn_to_broker_dtls;             /* _ARGO: integer */
    int64_t     backed_off_conn_to_broker_fohh;         /* _ARGO: integer */
    int64_t     backed_off_conn_to_broker_dtls;         /* _ARGO: integer */
    int64_t     to_broker_bytes_fohh;                   /* _ARGO: integer */
    int64_t     to_broker_bytes_dtls;                   /* _ARGO: integer */
    int64_t     to_broker_bytes_Bps_fohh;               /* _ARGO: integer */
    int64_t     to_broker_bytes_Bps_dtls;               /* _ARGO: integer */
    int64_t     from_broker_bytes_fohh;                 /* _ARGO: integer */
    int64_t     from_broker_bytes_dtls;                 /* _ARGO: integer */
    int64_t     from_broker_bytes_Bps_fohh;             /* _ARGO: integer */
    int64_t     from_broker_bytes_Bps_dtls;             /* _ARGO: integer */
    int64_t     active_conn_to_pbroker_fohh;            /* _ARGO: integer */
    int64_t     active_conn_to_pbroker_dtls;            /* _ARGO: integer */
    int64_t     backed_off_conn_to_pbroker_fohh;        /* _ARGO: integer */
    int64_t     backed_off_conn_to_pbroker_dtls;        /* _ARGO: integer */
    int64_t     to_pbroker_bytes_fohh;                  /* _ARGO: integer */
    int64_t     to_pbroker_bytes_dtls;                  /* _ARGO: integer */
    int64_t     to_pbroker_bytes_Bps_fohh;              /* _ARGO: integer */
    int64_t     to_pbroker_bytes_Bps_dtls;              /* _ARGO: integer */
    int64_t     from_pbroker_bytes_fohh;                /* _ARGO: integer */
    int64_t     from_pbroker_bytes_dtls;                /* _ARGO: integer */
    int64_t     from_pbroker_bytes_Bps_fohh;            /* _ARGO: integer */
    int64_t     from_pbroker_bytes_Bps_dtls;            /* _ARGO: integer */
};

/*
 * Assistant sends stats about RPC messages that it tx/tx to/from the cloud from control and data connections. Broker
 * consumes it a. writes to disk b. sends via kafka for analytics.
 */
struct zpn_assistant_rpc_stats {               /* _ARGO: object_definition */
    int64_t     cloud_time_us;                 /* _ARGO: integer */
    int64_t     rx_brk_req_from_dsp;           /* _ARGO: integer */
    int64_t     rx_brk_req_from_ubrk;          /* _ARGO: integer */
    int64_t     rx_brk_req_from_pbrk;          /* _ARGO: integer */
    int64_t     rx_bind_ack;                   /* _ARGO: integer */
    int64_t     rx_route_disc;                 /* _ARGO: integer */
    int64_t     rx_dns_check_from_dsp;         /* _ARGO: integer */
    int64_t     rx_dns_check_from_pbrk;        /* _ARGO: integer */
    int64_t     rx_mtunnel_end;                /* _ARGO: integer */
    int64_t     rx_tag_pause;                  /* _ARGO: integer */
    int64_t     rx_tag_resume;                 /* _ARGO: integer */
    int64_t     rx_win_update;                 /* _ARGO: integer */
    int64_t     rx_redirect;                   /* _ARGO: integer */
    int64_t     tx_brk_req_ack_to_dsp;         /* _ARGO: integer */
    int64_t     tx_brk_req_ack_to_dsp_fail;    /* _ARGO: integer */
    int64_t     tx_brk_req_ack_to_ubrk;        /* _ARGO: integer */
    int64_t     tx_brk_req_ack_to_ubrk_fail;   /* _ARGO: integer */
    int64_t     tx_brk_req_ack_to_pbrk;        /* _ARGO: integer */
    int64_t     tx_brk_req_ack_to_pbrk_fail;   /* _ARGO: integer */
    int64_t     tx_bind_req;                   /* _ARGO: integer */
    int64_t     tx_bind_req_fail;              /* _ARGO: integer */
    int64_t     tx_route_reg;                  /* _ARGO: integer */
    int64_t     tx_route_reg_fail;             /* _ARGO: integer */
    int64_t     tx_health_rpt;                 /* _ARGO: integer */
    int64_t     tx_health_rpt_fail;            /* _ARGO: integer */
    int64_t     tx_dns_check_to_dsp;           /* _ARGO: integer */
    int64_t     tx_dns_check_to_dsp_fail;      /* _ARGO: integer */
    int64_t     tx_dns_check_to_pbrk;          /* _ARGO: integer */
    int64_t     tx_dns_check_to_pbrk_fail;     /* _ARGO: integer */
    int64_t     tx_dns_check_no_result;        /* _ARGO: integer */
    int64_t     tx_comprehensive_stats;        /* _ARGO: integer */
    int64_t     tx_comprehensive_stats_fail;   /* _ARGO: integer */
    int64_t     rx_ctrl_redirect;              /* _ARGO: integer */
    int64_t     rx_cfg_redirect;               /* _ARGO: integer */
    int64_t     rx_ovd_redirect;               /* _ARGO: integer */
    int64_t     rx_stats_redirect;             /* _ARGO: integer */
    int64_t     rx_log_redirect;               /* _ARGO: integer */
};


/*
 * Assistant sends stats about RPC messages that it tx/tx to/from the cloud from control and data connections. Broker
 * consumes it a. writes to disk b. sends via kafka for analytics.
 */
struct zpn_assistant_scache_stats {     /* _ARGO: object_definition */
    int64_t cloud_time_us;              /* _ARGO: integer */
    int64_t total;                      /* _ARGO: integer */
    int64_t active;                     /* _ARGO: integer */
    int64_t get_success;                /* _ARGO: integer */
    int64_t get_failure;                /* _ARGO: integer */
};


/*
 * Assistant sends stats about applications/services/targets . Broker consumes it a. writes to disk b. sends via kafka
 * for analytics.
 */
struct zpn_assistant_app_stats {                       /* _ARGO: object_definition */
    int64_t     cloud_time_us;                         /* _ARGO: integer */
    int64_t     num_apps;                              /* _ARGO: integer */
    int64_t     num_alive_apps;                        /* _ARGO: integer */
    int64_t     num_on_access_apps;                    /* _ARGO: integer */
    int64_t     num_services;                          /* _ARGO: integer */
    int64_t     num_targets;                           /* _ARGO: integer */
    int64_t     num_alive_targets;                     /* _ARGO: integer */
};


/*
 * Assistant sends stats about mtunnels. Broker consumes it a. writes to disk b. sends via kafka for analytics.
 */
struct zpn_assistant_data_mtunnel_global_stats {                /* _ARGO: object_definition */
    int64_t     cloud_time_us;                                  /* _ARGO: integer */
    int64_t     total_mtunnel_created;                          /* _ARGO: integer */
    int64_t     total_mtunnel_freed;                            /* _ARGO: integer */
    int64_t     current_active_mtunnel_count;                   /* _ARGO: integer */
    int64_t     peak_active_mtunnel_count;                      /* _ARGO: integer */
    int64_t     peak_active_mtunnel_cloud_time_us;              /* _ARGO: integer */
    int64_t     num_udp_transactions;                           /* _ARGO: integer */
    int64_t     num_mtls_transactions;                          /* _ARGO: integer */
    int64_t     num_tcp_transactions;                           /* _ARGO: integer */
    int64_t     num_udp_with_DE_transactions;                   /* _ARGO: integer */
    int64_t     num_tcp_with_DE_transactions;                   /* _ARGO: integer */
    int64_t     num_icmp_transactions;                          /* _ARGO: integer */
    int64_t     num_mtunnels_created_pra;                       /* _ARGO: integer */
    int64_t     num_mtunnels_freed_pra;                         /* _ARGO: integer */
    int64_t     num_mtunnel_drops_max_sessions_reached_pra;     /* _ARGO: integer */
    int64_t     num_mtunnel_drops_cpu_limit_reached_pra;        /* _ARGO: integer */
    int64_t     num_mtunnel_drops_mem_limit_reached_pra;        /* _ARGO: integer */
};

/*
 * Assistant sends stats about mtunnels. Broker consumes it a. writes to disk b. sends via kafka for analytics.
 */
struct zpn_assistant_data_mtunnel_stats {                    /* _ARGO: * object_definition */
        int64_t     tun_connect_double_encrypt_server_side;         /* _ARGO: integer */
    int64_t     tun_connect_inspect_server_side;                /* _ARGO: integer */
    int64_t     tun_connect_double_encrypt_server_side_fail;    /* _ARGO: integer */
    int64_t     tun_connect_inspect_server_side_fail;           /* _ARGO: integer */
    int64_t     tun_connect_double_encrypt_client_side;         /* _ARGO: integer */
    int64_t     tun_connect_double_encrypt_client_side_fail;    /* _ARGO: integer */
    int64_t     tun_connect_inspect_client_side;                /* _ARGO: integer */
    int64_t     tun_connect_inspect_client_side_fail;           /* _ARGO: integer */
    int64_t     tun_connect_double_encrypt_server_side1;        /* _ARGO: integer */
    int64_t     tun_connect_double_encrypt_server_side_fail1;   /* _ARGO: integer */
    int64_t     server_side_bev_create;                         /* _ARGO: integer */
    int64_t     server_side_bev_create_fail;                    /* _ARGO: integer */

    int64_t     terminate_bev_free;                             /* _ARGO: integer */
    int64_t     process_event_bev_free_on_local_owner_fail;     /* _ARGO: integer */
    int64_t     process_event_bev_free_on_server_sock_close;    /* _ARGO: integer */
    int64_t     verify_and_connect_bev_free_on_connect_failure; /* _ARGO: integer */
    int64_t     zdx_injection_total_requested;                  /* _ARGO: integer */
    int64_t     zdx_injection_total_bytes_sent;                 /* _ARGO: integer */
    int64_t     zdx_injection_to_mconn_success;                 /* _ARGO: integer */
    int64_t     zdx_injection_to_mconn_fail_mtunnel_gone;       /* _ARGO: integer */
    int64_t     zdx_injection_to_mconn_fail_no_memory;          /* _ARGO: integer */

    int64_t     zdx_webprobe_http_requests;                                  /* _ARGO: integer */
    int64_t     zdx_webprobe_https_requests;                                 /* _ARGO: integer */
    int64_t     zdx_webprobe_https_requests_rejected_feature_disabled;       /* _ARGO: integer */

    //Webprobe inject to server stats
    int64_t     zdx_webprobe_client_injection_failed_to_queue_request;       /* _ARGO: integer */

    //Webprobe inject to zcc stats
    int64_t     zdx_webprobe_client_side_success;                            /* _ARGO: integer */
    int64_t     zdx_webprobe_client_side_fail;                               /* _ARGO: integer */
    int64_t     zdx_webprobe_client_injection_total_requested;               /* _ARGO: integer */
    int64_t     zdx_webprobe_client_injection_to_mconn_fail_mtunnel_gone;    /* _ARGO: integer */
    int64_t     zdx_webprobe_client_injection_failed_to_queue_response;      /* _ARGO: integer */
    int64_t     zdx_webprobe_client_injection_already_injected_skipping;     /* _ARGO: integer */
    int64_t     zdx_webprobe_client_injection_to_mconn_fail_no_memory;       /* _ARGO: integer */
    int64_t     zdx_webprobe_client_injection_to_mconn_fail_bad_argument;    /* _ARGO: integer */
    int64_t     zdx_webprobe_client_injection_to_mconn_serialize_err;        /* _ARGO: integer */
    int64_t     zdx_webprobe_client_injection_to_mconn_success;              /* _ARGO: integer */
    int64_t     zdx_webprobe_client_injection_total_bytes_sent;              /* _ARGO: integer */

    int64_t     zdx_webprobe_client_injection_unblock_to_mconn_total_requested;           /* _ARGO: integer */
    int64_t     zdx_webprobe_client_injection_unblock_to_mconn_total_requested_error;     /* _ARGO: integer */
    int64_t     zdx_webprobe_client_injection_unblock_to_mconn_fail_mtunnel_gone;         /* _ARGO: integer */
    int64_t     zdx_webprobe_client_injection_unblock_to_mconn_fail_resume_pause_error;   /* _ARGO: integer */

    // Webprobe https inject to zcc stats
    int64_t     zdx_webprobe_https_client_side_success;                                   /* _ARGO: integer */
    int64_t     zdx_webprobe_https_client_side_fail;                                      /* _ARGO: integer */
    int64_t     zdx_webprobe_https_client_injection_total_requested;                      /* _ARGO: integer */
    int64_t     zdx_webprobe_https_client_injection_to_mconn_fail_mtunnel_gone;           /* _ARGO: integer */
    int64_t     zdx_webprobe_https_client_injection_failed_to_queue_response;             /* _ARGO: integer */
    int64_t     zdx_webprobe_https_client_injection_already_injected_skipping;            /* _ARGO: integer */
    int64_t     zdx_webprobe_https_client_injection_to_mconn_fail_no_memory;              /* _ARGO: integer */
    int64_t     zdx_webprobe_https_client_injection_to_mconn_fail_bad_argument;           /* _ARGO: integer */
    int64_t     zdx_webprobe_https_client_injection_to_mconn_serialize_err;               /* _ARGO: integer */
    int64_t     zdx_webprobe_https_client_injection_to_mconn_success;                     /* _ARGO: integer */
    int64_t     zdx_webprobe_https_client_injection_total_bytes_sent;                     /* _ARGO: integer */

    //Webprobe http inject to server stats
    int64_t     zdx_webprobe_http_server_injection_to_mconn_total_requested;               /* _ARGO: integer */
    int64_t     zdx_webprobe_http_server_injection_to_mconn_fail_bad_argument;             /* _ARGO: integer */
    int64_t     zdx_webprobe_http_server_injection_to_mconn_fail_mtunnel_gone;             /* _ARGO: integer */
    int64_t     zdx_webprobe_http_server_injection_to_mconn_already_connected;             /* _ARGO: integer */
    int64_t     zdx_webprobe_http_server_injection_to_mconn_fail_no_memory;                /* _ARGO: integer */
    int64_t     zdx_webprobe_http_server_injection_to_mconn_fail_no_req_data;              /* _ARGO: integer */
    int64_t     zdx_webprobe_http_server_injection_to_mconn_success;                       /* _ARGO: integer */
    int64_t     zdx_webprobe_http_server_injection_to_mconn_total_bytes_sent;              /* _ARGO: integer */

    int64_t     zdx_webprobe_http_server_side_bev_create;                                  /* _ARGO: integer */
    int64_t     zdx_webprobe_http_server_side_bev_create_fail;                             /* _ARGO: integer */
    int64_t     zdx_webprobe_http_server_side_bev_connect_init_fail;                       /* _ARGO: integer */
    int64_t     zdx_webprobe_http_server_side_bev_socket_init_fail;                        /* _ARGO: integer */
    int64_t     zdx_webprobe_http_server_side_bev_connect_event_cb_fail;                   /* _ARGO: integer */
    int64_t     zdx_webprobe_http_server_side_bev_connected;                               /* _ARGO: integer */
    int64_t     zdx_webprobe_http_server_side_bev_fail_mtunnel_gone;                       /* _ARGO: integer */
    int64_t     zdx_webprobe_http_server_side_bev_free_connect_error;                      /* _ARGO: integer */
    int64_t     zdx_webprobe_http_server_side_bev_free_on_local_owner_fail;                /* _ARGO: integer */

    //Webprobe https inject to server stats
    int64_t     zdx_webprobe_https_server_injection_to_mconn_total_requested;               /* _ARGO: integer */
    int64_t     zdx_webprobe_https_server_injection_to_mconn_fail_bad_argument;             /* _ARGO: integer */
    int64_t     zdx_webprobe_https_server_injection_to_mconn_fail_mtunnel_gone;             /* _ARGO: integer */
    int64_t     zdx_webprobe_https_server_injection_to_mconn_already_connected;             /* _ARGO: integer */
    int64_t     zdx_webprobe_https_server_injection_to_mconn_fail_no_memory;                /* _ARGO: integer */
    int64_t     zdx_webprobe_https_server_injection_to_mconn_fail_no_req_data;              /* _ARGO: integer */
    int64_t     zdx_webprobe_https_server_injection_to_mconn_success;                       /* _ARGO: integer */
    int64_t     zdx_webprobe_https_server_injection_to_mconn_total_bytes_sent;              /* _ARGO: integer */

    int64_t     zdx_webprobe_https_server_side_bev_create;                                  /* _ARGO: integer */
    int64_t     zdx_webprobe_https_server_side_bev_create_fail;                             /* _ARGO: integer */
    int64_t     zdx_webprobe_https_server_side_bev_connect_init_fail;                       /* _ARGO: integer */
    int64_t     zdx_webprobe_https_server_side_bev_connect_event_cb_fail;                   /* _ARGO: integer */
    int64_t     zdx_webprobe_https_server_side_bev_connected;                               /* _ARGO: integer */
    int64_t     zdx_webprobe_https_server_side_bev_fail_mtunnel_gone;                       /* _ARGO: integer */
    int64_t     zdx_webprobe_https_server_side_bev_free_connect_error;                      /* _ARGO: integer */
    int64_t     zdx_webprobe_https_server_side_bev_free_on_local_owner_fail;                /* _ARGO: integer */

    int64_t     zdx_webprobe_https_server_side_stats_ssl_handshake_count;                   /* _ARGO: integer */
    int64_t     zdx_webprobe_https_server_side_stats_ssl_handshake_count_no_msg_callback_error;   /* _ARGO: integer */
    int64_t     zdx_webprobe_https_server_side_stats_ssl_get_fail_mtunnel_gone;             /* _ARGO: integer */
    int64_t     zdx_webprobe_https_server_side_ssl_messages_max_exceeded;                   /* _ARGO: integer */
    int64_t     zdx_webprobe_https_server_side_stats_ssl_handshake_request_bytes;           /* _ARGO: integer */
    int64_t     zdx_webprobe_https_server_side_stats_ssl_handshake_response_bytes;          /* _ARGO: integer */
    int64_t     zdx_webprobe_https_ssl_get_ex_data_null_count;                              /* _ARGO: integer */
    int64_t     zdx_webprobe_https_ssl_ctx_null;                                            /* _ARGO: integer */
    int64_t     zdx_webprobe_https_ssl_object_null_count;                                   /* _ARGO: integer */
    int64_t     zdx_webprobe_https_server_name_set_fail_count;                              /* _ARGO: integer */

    int64_t     zdx_webprobe_delay_from_mtunnel_request_to_webprobe_response_15s;           /* _ARGO: integer */
    int64_t     zdx_webprobe_delay_from_mtunnel_request_to_webprobe_response_30s;           /* _ARGO: integer */
    int64_t     zdx_webprobe_delay_from_mtunnel_request_to_webprobe_response_60s;           /* _ARGO: integer */
    int64_t     zdx_webprobe_delay_from_mtunnel_request_to_webprobe_response_60s_plus;      /* _ARGO: integer */
    int64_t     zdx_webprobe_delay_from_mtunnel_request_to_webprobe_response_60s_plus_max_val; /* _ARGO: integer */

    int64_t     zdx_webprobe_delay_from_mtunnel_request_to_mtunnel_complete_5s;             /* _ARGO: integer */
    int64_t     zdx_webprobe_delay_from_mtunnel_request_to_mtunnel_complete_5s_plus;        /* _ARGO: integer */

    int64_t     zdx_webprobe_delay_from_mtunnel_complete_to_webprobe_request_15s;           /* _ARGO: integer */
    int64_t     zdx_webprobe_delay_from_mtunnel_complete_to_webprobe_request_30s;           /* _ARGO: integer */
    int64_t     zdx_webprobe_delay_from_mtunnel_complete_to_webprobe_request_60s;           /* _ARGO: integer */
    int64_t     zdx_webprobe_delay_from_mtunnel_complete_to_webprobe_request_60s_plus;      /* _ARGO: integer */
    int64_t     zdx_webprobe_delay_from_mtunnel_complete_to_webprobe_request_60s_plus_max_val; /* _ARGO: integer */

    int64_t     tcp_bev_connected;                              /* _ARGO: integer */
    int64_t     tcp_bev_connect_error;                          /* _ARGO: integer */
    int64_t     mtls_bev_connected;                             /* _ARGO: integer */
    int64_t     mtls_bev_connect_error;                         /* _ARGO: integer */
    int64_t     mtls_ssl_created;                               /* _ARGO: integer */
    int64_t     mtls_ssl_free;                                  /* _ARGO: integer */

    int64_t     num_mtunnel_in_buckets_queue;                             /* _ARGO: integer */
    int64_t     num_mtunnel_in_reap_queue;                                /* _ARGO: integer */
    int64_t     num_mtunnel_in_free_queue;                                /* _ARGO: integer */
    int64_t     num_mtunnel_in_reap_state_but_not_moving_to_reap_queue;   /* _ARGO: integer */
    int64_t     num_mtunnel_in_reaping_queue_but_not_clean;               /* _ARGO: integer */

    int64_t     num_of_mtunnel_rejected_due_to_no_system_capacity;        /* _ARGO: integer */
    int64_t     appc_restart_invalid_version;                             /* _ARGO: integer */
    int64_t     denied_version_del_fail;                                  /* _ARGO: integer */
    int64_t     num_mtunnel_in_double_hop;                                /* _ARGO: integer */
    int64_t     broker_setup_timeout_not_double_hop_mtunnel;              /* _ARGO: integer */
    int64_t     num_double_hop_webprobe_http_connector_destroy;           /* _ARGO: integer */
    int64_t     num_double_hop_webprobe_https_connector_destroy;          /* _ARGO: integer */
    int64_t     num_double_hop_tun_connector_destroy;                     /* _ARGO: integer */
    int64_t     num_double_hop_inspect_connector_destroy;                 /* _ARGO: integer */
};

/*
 * ZPN ZDX probe leg info
 */

enum zpn_zdx_combiner_errors {
    NO_ERROR = 0,
    ORIGIN_NO_LEFT = 1,
    MISSING = 2
};

struct zpn_assistant_dns_stats {                       /* _ARGO: object_definition */
    int64_t     total_dns_request_done_on_A;           /* _ARGO: integer */
    int64_t     total_dns_request_done_on_AAAA;        /* _ARGO: integer */
    int64_t     total_dns_response_on_A;               /* _ARGO: integer */
    int64_t     total_dns_response_on_AAAA;            /* _ARGO: integer */
    int64_t     total_dns_response_on_A_with_addr;     /* _ARGO: integer */
    int64_t     total_dns_response_on_AAAA_with_addr;  /* _ARGO: integer */
    int64_t     total_dns_rcode_noerror_on_A;          /* _ARGO: integer */
    int64_t     total_dns_rcode_noerror_on_AAAA;       /* _ARGO: integer */
    int64_t     total_dns_rcode_SRVFAIL_on_A;          /* _ARGO: integer */
    int64_t     total_dns_rcode_SRVFAIL_on_AAAA;       /* _ARGO: integer */
    int64_t     total_dns_rcode_NXDOMAIN_on_A;         /* _ARGO: integer */
    int64_t     total_dns_rcode_NXDOMAIN_on_AAAA;      /* _ARGO: integer */
    int64_t     total_dns_rcode_others_on_A;           /* _ARGO: integer */
    int64_t     total_dns_rcode_others_on_AAAA;        /* _ARGO: integer */
    int64_t     total_dns_request_done_on_SRV;         /* _ARGO: integer */
    int64_t     total_dns_response_on_SRV;             /* _ARGO: integer */
    int64_t     total_dns_response_on_SRV_with_addr;   /* _ARGO: integer */
    int64_t     total_dns_rcode_noerror_on_SRV;        /* _ARGO: integer */
    int64_t     total_dns_rcode_SRVFAIL_on_SRV;        /* _ARGO: integer */
    int64_t     total_dns_rcode_NXDOMAIN_on_SRV;       /* _ARGO: integer */
    int64_t     total_dns_rcode_others_on_SRV;         /* _ARGO: integer */
    int64_t     total_dns_request_done_on_TXT;         /* _ARGO: integer */
    int64_t     total_dns_response_on_TXT;             /* _ARGO: integer */
    int64_t     total_dns_response_on_TXT_with_addr;   /* _ARGO: integer */
    int64_t     total_dns_rcode_noerror_on_TXT;        /* _ARGO: integer */
    int64_t     total_dns_rcode_SRVFAIL_on_TXT;        /* _ARGO: integer */
    int64_t     total_dns_rcode_NXDOMAIN_on_TXT;       /* _ARGO: integer */
    int64_t     total_dns_rcode_others_on_TXT;         /* _ARGO: integer */
};


/*
 * Stats related to broker's transaction decision caching.
 * invalidated maps to the number of times a cache is deleted because of a failed transaction. Important to see how
 * valuable the cache has been.
 */
struct zpn_broker_client_path_cache_stats {            /* _ARGO: object_definition */
    int64_t     hit;                                   /* _ARGO: integer */
    int64_t     miss;                                  /* _ARGO: integer */
    int64_t     invalidated;                           /* _ARGO: integer */
    int64_t     current_entries;                       /* _ARGO: integer */
    int64_t     current_entries_ast_grp;               /* _ARGO: integer */
    int64_t     total_entries_so_far;                  /* _ARGO: integer */
    int64_t     total_entries_so_far_ast_grp;          /* _ARGO: integer */
};

/*
 * Stats related to negative path cache.
 */
struct zpn_broker_client_neg_path_cache_stats {        /* _ARGO: object_definition */
    int64_t     hit;                                   /* _ARGO: integer */
    int64_t     miss;                                  /* _ARGO: integer */
    int64_t     current_entries;                       /* _ARGO: integer */
    int64_t     total_entries_so_far;                  /* _ARGO: integer */
    int64_t     flushed_entries;                       /* _ARGO: integer */
    int64_t     eviction_during_set;                   /* _ARGO: integer */
    int64_t     eviction_during_get;                   /* _ARGO: integer */
    int64_t     avg_set_time_us;                       /* _ARGO: integer */
    int64_t     max_set_time_us;                       /* _ARGO: integer */
    int64_t     avg_get_time_us;                       /* _ARGO: integer */
    int64_t     max_get_time_us;                       /* _ARGO: integer */
};

/*
 * Client App registration message
 */
struct zpn_client_broker_app_registration {         /* _ARGO: object_definition */
    const char *client_fqdn;                           /* _ARGO: string */
    int64_t    timestamp_us;                           /* _ARGO: integer */
};

/*
 * private broker c2c app registration
 */
struct zpn_client_private_broker_app_registration {  /* _ARGO: object_definition */
    const char *client_fqdn;                            /* _ARGO: string */

    // private broker sends client cname and tunnel id
    const char *tunnel_id;                              /* _ARGO: string */
    const char *cname;                                  /* _ARGO: string */
    int64_t timestamp_us;                               /* _ARGO: integer */
};

/*
 * Client App registration message
 */
struct zpn_client_app_registration_notification {      /* _ARGO: object_definition */
    const char *client_fqdn;                           /* _ARGO: string */
    const char *error_code;                            /* _ARGO: string */
    struct argo_inet *client_ip;                       /* _ARGO: inet */
    const char *location_hint;                         /* _ARGO: string */
};

#define MAX_CLIENT_FQDN    10

struct zpn_broker_dispatcher_app_registration {        /* _ARGO: object_definition */
    int64_t    g_brk;                                  /* _ARGO: integer */
    const char *client_cname;                          /* _ARGO: string */
    const char **client_fqdn;                          /* _ARGO: string */
    int        fqdn_count;                             /* _ARGO: integer, quiet, count: client_fqdn */
    int64_t    customer_gid;                           /* _ARGO: integer */
    int64_t    connect_us;                             /* _ARGO: integer */
    int8_t     alive;                                  /* _ARGO: integer */
    // absolute time when the registation weill expire, not a TTL
    int64_t    expire_s;                               /* _ARGO: integer */

    // this is hash of hw_serial_id, to reduce the size to predictable length
    const char *machine_id;                            /* _ARGO: string */
};

struct zpn_transit_req {                        /* _ARGO: object_definition */
    const char *mtunnel_id;                     /* _ARGO: string,                   */
    const char *peer_mtunnel_id;                /* _ARGO: string,                   */
    int64_t    g_brk;                           /* _ARGO: integer */
    int64_t    g_bfw;                           /* _ARGO: integer */

    const char **hop_fqdn;                      /* _ARGO: string */
    int        hop_fqdn_count;                  /* _ARGO: integer, quiet, count: hop_fqdn */

    struct argo_inet *a_ip;                     /* _ARGO: inet */

    // zia inspection needs these
    int a_port;                                 /* _ARGO: integer, nozero */
    struct argo_inet *s_ip;                     /* _ARGO: inet, nozero */
    int s_port;                                 /* _ARGO: integer, nozero */
    int64_t    g_dsp;                           /* _ARGO: integer */

};

struct zpn_add_proxy {                          /* _ARGO: object_definition */
    char *domain;                         /* _ARGO: string           */
    char *resolve_domain;                 /* _ARGO: string           */
    char *mtunnel_id;                     /* _ARGO: string           */
};

 struct zpn_delete_proxy {                /* _ARGO: object_definition */
    char *domain;                         /* _ARGO: string           */
 };

struct zpn_add_proxy_ack {                      /* _ARGO: object_definition */
    char *mtunnel_id;                     /* _ARGO: string           */
    int status;                                 /* _ARGO: integer */
};

struct zpn_pbroker_mtunnel_stats {              /* _ARGO: object_definition */
    int64_t mt_promote_success_count;           /* _ARGO: integer */
    int64_t mt_promote_fail_count;              /* _ARGO: integer */
    int64_t mt_promote_terminated_count;        /* _ARGO: integer */
    int64_t mt_promote_c2c_regex_bypass_count;        /* _ARGO: integer */
};

/* send mtunnel log stats */
struct zpn_mtunnel_stats {                     /* _ARGO: object_definition */
    const char  *mtunnel_id;                     /* _ARGO: string   */
    const char  *peer_mtunnel_id;                /* _ARGO: string   */
    int32_t     tag_id;                          /* _ARGO: integer */

    /* origin broker info: */
    int64_t     orig_start_rx_time_us;            /* _ARGO: integer */
    const char *domain;                           /* _ARGO: string  */
    const char *app_type;                         /* _ARGO: string  */

    /* Rule set and sequence number of access policy: */
    int64_t g_rul;                               /* _ARGO: integer */
    int64_t g_rul_set;                           /* _ARGO: integer */
    int64_t g_rul_set_seq;                       /* _ARGO: integer */
    int64_t g_loc;                               /* _ARGO: integer */
    int64_t g_exp;                               /* _ARGO: integer */
    int64_t g_app_grp;                           /* _ARGO: integer */
    int64_t *g_app_grps;                         /* _ARGO: integer */
    int64_t g_app_grps_count;                    /* _ARGO: quiet, integer, count: g_app_grps */
    int64_t g_rul_set_rebuild_started_us;        /* _ARGO: integer */
    int64_t g_rul_set_rebuild_finished_us;       /* _ARGO: integer */
    int64_t g_rul_set_last_rebuild_time_us;      /* _ARGO: integer */

    /* Rule set and sequence number of auth policy: */
    int64_t g_auth_rul;							/* _ARGO: integer, stringify,        synonym: ReauthPolicy */
    int64_t g_auth_rul_set;                     /* _ARGO: integer, stringify */
    int64_t g_auth_rul_set_seq;                 /* _ARGO: integer, stringify */
    int64_t g_auth_rul_set_rebuild_started_us;  /* _ARGO: integer */
    int64_t g_auth_rul_set_rebuild_finished_us; /* _ARGO: integer */
    int64_t g_auth_rul_last_rebuild_time_us;    /* _ARGO: integer */

    const char *client_type;                    /* _ARGO: string */

    const char  **hop_fqdn;                      /* _ARGO: string */
    int         hop_fqdn_count;                  /* _ARGO: integer, quiet, count: hop_fqdn */

    int32_t     *hop_tags;                       /* _ARGO: integer */
    int         hop_tags_count;                  /* _ARGO: integer, quiet, count: hop_tags */
    const char  *error_code;                     /* _ARGO: string */

    /* Multi-hop mtunnel tracking fields */
    /**************************************************************************/

    int hops_count;                                         /* _ARGO: integer */

    /* Hop type may be: "ORIG", "TRANSIT", "TERM" */
    char **hop_type;                                        /* _ARGO: string */
    int hop_type_count;                                     /* _ARGO: integer, quiet, count: hop_type */

    /* mtunnel ids */
    char **mtunnel_ids;                                     /* _ARGO: string */
    int mtunnel_ids_count;                                  /* _ARGO: integer, quiet, count: mtunnel_ids */

    /* broker gids */
    int64_t *brks;                                          /* _ARGO: integer */
    int brks_count;                                         /* _ARGO: integer, quiet, count: brks */

    /* broker group gids */
    int64_t *brks_grp;                                      /* _ARGO: integer */
    int brks_grp_count;                                     /* _ARGO: integer, quiet, count: brks_grp */

    struct argo_inet *hop_pub_ips;                          /* _ARGO: inet */
    int hop_pub_ips_count;                                  /* _ARGO: integer, quiet, count: hop_pub_ips */

    char **c_tlv_types;                                     /* _ARGO: string */
    int c_tlv_types_count;                                  /* _ARGO: integer, quite, count: c_tlv_types */

    /* start rx times */
    int64_t *start_rx_times_us;                             /* _ARGO: integer */
    int start_rx_times_us_count;                            /* _ARGO: integer, quiet, count: start_rx_times_us */

    /* end times */
    int64_t *end_times_us;                                  /* _ARGO: integer */
    int end_times_us_count;                                 /* _ARGO: integer, quiet, count: end_times_us */

    /* system type may be: "SERVICE_EDGE", "BROKER" */
    char **sys_type;                                        /* _ARGO: string */
    int sys_type_count;                                     /* _ARGO: integer, quiet, count: sys_type */

    /**************************************************************************/

    char *c_uid;                                            /* _ARGO: string */
    int64_t o_user_id;                                      /* _ARGO: integer, nozero */
    int64_t o_location_id;                                  /* _ARGO: integer, nozero */
    struct argo_inet *o_sip;                                /* _ARGO: inet */
    struct argo_inet *o_dip;                                /* _ARGO: inet */
    int32_t o_sport;                                        /* _ARGO: integer, nozero */
    int32_t o_dport;                                        /* _ARGO: integer, nozero */
    char *o_identity_name;                                  /* _ARGO: string */

    // c2c with zia_inspection, same as trans log
    int8_t zia_inspection;                                  /* _ARGO: integer, nozero */
    const char *zia_inspection_status_code;                 /* _ARGO: string           */
    int8_t zia_inspection_res;                              /* _ARGO: integer, nozero */
    int8_t zia_inspection_bypassed;                         /* _ARGO: integer, nozero */
    int64_t zia_ctrl_path_ms;                               /* _ARGO: integer, nozero */

};

/*
 * DR stats log for PB
 */
struct zpn_private_broker_dr_stats {                /* _ARGO: object_definition */
    /* DR related stats */
    int64_t dr_config_auto_dump_count;              /* _ARGO: integer */
    int64_t dr_config_max_dump_time;                /* _ARGO: integer */
    int64_t dr_config_dump_start_time_s;            /* _ARGO: integer */
    int64_t dr_config_dump_end_time_s;              /* _ARGO: integer */
    int64_t dr_config_dump_fail_count;              /* _ARGO: integer */

    /* Config snapshot stats */
    int64_t dr_config_snapshot_counter;             /* _ARGO: integer */
    int64_t dr_last_config_snapshot_start_time;     /* _ARGO: integer */
    int64_t dr_last_config_snapshot_end_time;       /* _ARGO: integer */
    int64_t dr_config_snapshot_max_time_taken;      /* _ARGO: integer */
    int64_t dr_last_config_snapshot_attempt_status; /* _ARGO: integer */
    int64_t dr_config_snapshot_failed_counter;      /* _ARGO: integer */

    int64_t dr_activate_on_count;                   /* _ARGO: integer */
    int64_t dr_activate_test_count;                 /* _ARGO: integer */
    int64_t dr_activate_off_count;                  /* _ARGO: integer */
    int64_t dr_activate_err_count;                  /* _ARGO: integer */

    int64_t dr_activate_req_count;                  /* _ARGO: integer */
    int64_t dr_activate_resp_count;                 /* _ARGO: integer */
    int64_t dr_activate_no_resp_count;              /* _ARGO: integer */
    int64_t dr_activate_err_resp_count;             /* _ARGO: integer */

    int64_t dr_config_snapshot_current_count;       /* _ARGO: integer */
};

/*
 * DR stats log for assistant
 */
struct zpn_assistant_dr_stats {                         /* _ARGO: object_definition */
    /* DR related stats */
    int64_t dr_config_auto_dump_count;                  /* _ARGO: integer */
    int64_t dr_config_max_dump_time;                    /* _ARGO: integer */
    int64_t dr_config_dump_start_time_s;                /* _ARGO: integer */
    int64_t dr_config_dump_end_time_s;                  /* _ARGO: integer */
    int64_t dr_config_dump_fail_count;                  /* _ARGO: integer */

    /* Config snapshot stats */
    int64_t dr_config_snapshot_counter;                 /* _ARGO: integer */
    int64_t dr_last_config_snapshot_start_time;         /* _ARGO: integer */
    int64_t dr_last_config_snapshot_end_time;           /* _ARGO: integer */
    int64_t dr_config_snapshot_max_time_taken;          /* _ARGO: integer */
    int64_t dr_last_config_snapshot_attempt_status;     /* _ARGO: integer */
    int64_t dr_config_snapshot_failed_counter;          /* _ARGO: integer */

    int64_t dr_activate_on_count;                       /* _ARGO: integer */
    int64_t dr_activate_test_count;                     /* _ARGO: integer */
    int64_t dr_activate_off_count;                      /* _ARGO: integer */
    int64_t dr_activate_err_count;                      /* _ARGO: integer */

    int64_t dr_activate_req_count;                      /* _ARGO: integer */
    int64_t dr_activate_resp_count;                     /* _ARGO: integer */
    int64_t dr_activate_no_resp_count;                  /* _ARGO: integer */
    int64_t dr_activate_err_resp_count;                 /* _ARGO: integer */

    int64_t dr_config_snapshot_current_count;           /* _ARGO: integer */
};

struct zpn_assistant_fproxy_stats {             /* _ARGO: object_definition */
    int64_t num_allocations;                    /* _ARGO: integer */
    int64_t free_queue_count;                   /* _ARGO: integer */
    int64_t num_fproxy_conn_alive;              /* _ARGO: integer */
    int64_t fproxy_rx_bytes;                    /* _ARGO: integer */
    int64_t fproxy_tx_bytes;                    /* _ARGO: integer */
    int64_t num_fproxy_conns;                   /* _ARGO: integer */
};

struct zpn_private_broker_fproxy_stats {        /* _ARGO: object_definition */
    int64_t num_allocations;                    /* _ARGO: integer */
    int64_t free_queue_count;                   /* _ARGO: integer */
    int64_t num_fproxy_conn_alive;              /* _ARGO: integer */
    int64_t fproxy_rx_bytes;                    /* _ARGO: integer */
    int64_t fproxy_tx_bytes;                    /* _ARGO: integer */
    int64_t num_fproxy_conns;                   /* _ARGO: integer */
};

/*
 * Upgrade stats for PSE
 */
struct zpn_private_broker_upgrade_stats {       /* _ARGO: object_definition */
    uint64_t os_upgrade_fail;                    /* _ARGO: integer */
    uint64_t sarge_upgrade_fail;                 /* _ARGO: integer */
    uint64_t os_upgrade_success;                 /* _ARGO: integer */
    uint64_t sarge_upgrade_success;              /* _ARGO: integer */
    uint64_t os_upgrade_timeout;                 /* _ARGO: integer */
    uint64_t sarge_os_cfg_read_fail;             /* _ARGO: integer */
    uint64_t sudo_path_fail;                     /* _ARGO: integer */
    uint64_t package_manager_path_fail;          /* _ARGO: integer */
};

struct zpn_sitec_fproxy_stats {                 /* _ARGO: object_definition */
    int64_t num_allocations;                    /* _ARGO: integer */
    int64_t free_queue_count;                   /* _ARGO: integer */
    int64_t num_fproxy_conn_alive;              /* _ARGO: integer */
    int64_t fproxy_rx_bytes;                    /* _ARGO: integer */
    int64_t fproxy_tx_bytes;                    /* _ARGO: integer */
    int64_t num_fproxy_conns;                   /* _ARGO: integer */
};

/*
 * Upgrade stats for PCC
 */
struct zpn_sitec_upgrade_stats {                /* _ARGO: object_definition */
    uint64_t os_upgrade_fail;                    /* _ARGO: integer */
    uint64_t sarge_upgrade_fail;                 /* _ARGO: integer */
    uint64_t os_upgrade_success;                 /* _ARGO: integer */
    uint64_t sarge_upgrade_success;              /* _ARGO: integer */
    uint64_t os_upgrade_timeout;                 /* _ARGO: integer */
    uint64_t sarge_os_cfg_read_fail;             /* _ARGO: integer */
    uint64_t sudo_path_fail;                     /* _ARGO: integer */
    uint64_t package_manager_path_fail;          /* _ARGO: integer */
};

/*
 * Comprehensive stats log for assistant
 */
struct zpn_assistant_comprehensive_stats {      /* _ARGO: object_definition */
    int64_t cloud_time_us;                      /* _ARGO: integer, adj_time,        synonym: LogTimestamp */
    int64_t g_cst;                              /* _ARGO: integer, stringify */
    int64_t g_microtenant;                      /* _ARGO: integer, stringify, nozero synonym: MicroTenantID */
    int64_t g_ast;                              /* _ARGO: integer, stringify,       synonym: Connector */
    int64_t g_sitec;                            /* _ARGO: integer, stringify,       synonym: SiteController */
    int64_t np_connector_gid;                   /* _ARGO: integer */

    double  cpu_load_avg_per_core;              /* _ARGO: double */
    int32_t cpu_util_percent;                   /* _ARGO: integer,                  synonym: CPUUtilization */
    int32_t mem_util_system_percent;            /* _ARGO: integer,                  synonym: SystemMemoryUtilization */
    int32_t mem_util_process_percent;           /* _ARGO: integer,                  synonym: ProcessMemoryUtilization */

    int32_t app_count;                          /* _ARGO: integer,                  synonym: AppCount */
    int32_t service_count;                      /* _ARGO: integer,                  synonym: ServiceCount */
    int32_t target_count;                       /* _ARGO: integer,                  synonym: TargetCount */
    int32_t alive_target_count;                 /* _ARGO: integer,                  synonym: AliveTargetCount */

    int64_t active_conn_to_broker;              /* _ARGO: integer,                  synonym: ActiveConnectionsToPublicSE */
    int64_t backed_off_conn_to_broker;          /* _ARGO: integer,                  synonym: DisconnectedConnectionsToPublicSE */
    int64_t active_conn_to_pbroker;             /* _ARGO: integer,                  synonym: ActiveConnectionsToPrivateSE */
    int64_t backed_off_conn_to_pbroker;         /* _ARGO: integer,                  synonym: DisconnectedConnectionsToPrivateSE */

    int64_t to_broker_bytes;                    /* _ARGO: integer,                  synonym: TransmittedBytesToPublicSE */
    int64_t from_broker_bytes;                  /* _ARGO: integer,                  synonym: ReceivedBytesFromPublicSE */
    int64_t to_pbroker_bytes;                   /* _ARGO: integer,                  synonym: TransmittedBytesToPrivateSE */
    int64_t from_pbroker_bytes;                 /* _ARGO: integer,                  synonym: ReceivedBytesFromPrivateSE */

    int64_t to_broker_bytes_delta;              /* _ARGO: integer,                  synonym: TransmittedBytesToPublicSEDelta */
    int64_t from_broker_bytes_delta;            /* _ARGO: integer,                  synonym: ReceivedBytesFromPublicSEDelta */
    int64_t to_pbroker_bytes_delta;             /* _ARGO: integer,                  synonym: TransmittedBytesToPrivateSEDelta */
    int64_t from_pbroker_bytes_delta;           /* _ARGO: integer,                  synonym: ReceivedBytesFromPrivateSEDelta */

    int64_t total_mtunnel_created;              /* _ARGO: integer,                  synonym: AppConnectionsCreated */
    int64_t total_mtunnel_freed;                /* _ARGO: integer,                  synonym: AppConnectionsCleared */
    int64_t current_active_mtunnel_count;       /* _ARGO: integer,                  synonym: AppConnectionsActive */

    int64_t num_system_tcpv4_socket_inuse;          /* _ARGO: integer,                  synonym: UsedTCPPortsIPv4 */
    int64_t num_system_udpv4_socket_inuse;          /* _ARGO: integer,                  synonym: UsedUDPPortsIPv4 */
    int64_t num_system_tcpv6_socket_inuse;          /* _ARGO: integer,                  synonym: UsedTCPPortsIPv6 */
    int64_t num_system_udpv6_socket_inuse;          /* _ARGO: integer,                  synonym: UsedUDPPortsIPv6 */
    int num_usable_ports;                       /* _ARGO: integer,                  synonym: AvailablePorts */

    int64_t system_fd_max;                      /* _ARGO: integer,                  synonym: SystemMaximumFileDescriptors */
    int64_t system_fd_in_use;                   /* _ARGO: integer,                  synonym: SystemUsedFileDescriptors */
    int64_t process_fd_max;                     /* _ARGO: integer,                  synonym: ProcessMaximumFileDescriptors */
    int64_t process_fd_in_use;                  /* _ARGO: integer,                  synonym: ProcessUsedFileDescriptors */

    int64_t available_disk_bytes;               /* _ARGO: integer,                  synonym: AvailableDiskBytes */

    int64_t num_mtunnels_created_pra;           /* _ARGO: integer,                  synonym: PRAConnectionsCreated */
    int64_t num_mtunnels_freed_pra;             /* _ARGO: integer,                  synonym: PRAConnectionsCleared */

    int64_t num_pdp_profile_count;                          /* _ARGO: integer */
    int64_t num_mtunnels_inspect_http;                      /* _ARGO: integer */
    int64_t num_mtunnels_inspect_DE_http;                   /* _ARGO: integer */
    int64_t num_mtunnels_inspect_https;                     /* _ARGO: integer */
    int64_t num_mtunnels_inspect_DE_https;                  /* _ARGO: integer */
    int64_t num_mtunnels_inspect_ldap;                      /* _ARGO: integer */
    int64_t num_mtunnels_inspect_DE_ldap;                   /* _ARGO: integer */
    int64_t num_mtunnels_inspect_smb;                       /* _ARGO: integer */
    int64_t num_mtunnels_inspect_DE_smb;                    /* _ARGO: integer */
    int64_t num_mtunnels_inspect_krb;                       /* _ARGO: integer */
    int64_t num_mtunnels_inspect_DE_krb;                    /* _ARGO: integer */
    int64_t num_mtunnels_autodetect;                        /* _ARGO: integer */
    int64_t num_mtunnels_autodetect_DE;                     /* _ARGO: integer */
    int64_t num_mtunnels_autoinspect;                       /* _ARGO: integer */
    int64_t num_mtunnels_autoinspect_tls;                   /* _ARGO: integer */
    int64_t num_mtunnels_autoinspect_DE;                    /* _ARGO: integer */
    int64_t num_mtunnels_autoinspect_DE_tls;                /* _ARGO: integer */
    int64_t num_mtunnels_inspect_disabled;                  /* _ARGO: integer */
    int64_t num_mtunnels_inspect_http_active;               /* _ARGO: integer */
    int64_t num_mtunnels_inspect_https_active;              /* _ARGO: integer */
    int64_t num_mtunnels_inspect_ldap_active;               /* _ARGO: integer */
    int64_t num_mtunnels_inspect_smb_active;                /* _ARGO: integer */
    int64_t num_mtunnels_inspect_krb_active;                /* _ARGO: integer */
    int64_t num_mtunnels_inspect_autodetect_active;         /* _ARGO: integer */
    int64_t num_mtunnels_inspect_autoinspect_active;        /* _ARGO: integer */
    int64_t num_mtunnels_inspect_appl_not_found;            /* _ARGO: integer */
    int64_t num_inspect_appl_cert_key_retrieval_requests;   /* _ARGO: integer */
    int64_t num_inspect_appl_cert_key_retrieval_failure;    /* _ARGO: integer */
    int64_t num_inspect_appl_cert_gen_requests;             /* _ARGO: integer */
    int64_t num_inspect_appl_cert_gen_failure;              /* _ARGO: integer */
    int64_t num_inspect_prfl_construct_failure;             /* _ARGO: integer */
    int64_t num_mtunnel_inspt_appl_SSL_connect_failure;     /* _ARGO: integer */
    int64_t num_inspect_connectors_double_free;             /* _ARGO: integer */

    int64_t num_websocket_upgrades;                         /* _ARGO: integer */
    int64_t num_websocket_inspections;                      /* _ARGO: integer */
   /* WAF API app-seg stats */
    int64_t num_mtunnels_total_waf_api_traffic;             /* _ARGO: integer */
    int64_t num_mtunnels_active_waf_api_traffic;            /* _ARGO: integer */
    int64_t num_mtunnels_inspect_waf_api_traffic;           /* _ARGO: integer */

    int64_t num_mtunnels_request_pipeline_created;          /* _ARGO: integer */
    int64_t num_mtunnels_response_pipeline_created;         /* _ARGO: integer */
    int64_t num_mtunnels_request_pipeline_destroyed;        /* _ARGO: integer */
    int64_t num_mtunnels_response_pipeline_destroyed;       /* _ARGO: integer */

    /* Customer DR stats */
    int64_t dr_activation_on_count;                         /* _ARGO: integer,                  synonym: DrActivationOnCnt */
    int64_t dr_activation_test_count;                       /* _ARGO: integer,                  synonym: DrActivationTestCnt */
    int64_t dr_activation_off_count;                        /* _ARGO: integer,                  synonym: DrActivationOffCnt */

    int64_t dr_activation_err_cnt;                          /* _ARGO: integer,                  synonym: DrActivationErrCnt */

    int64_t dr_activation_req_cnt;                          /* _ARGO: integer,                  synonym: DrActivationReqCnt */
    int64_t dr_activation_resp_cnt;                         /* _ARGO: integer,                  synonym: DrActivationRespCnt */
    int64_t dr_activation_no_resp_cnt;                      /* _ARGO: integer,                  synonym: DrActivationNoRespCnt */
    int64_t dr_activation_err_resp_cnt;                     /* _ARGO: integer,                  synonym: DrActivationErrRespCnt */

    int64_t dr_config_auto_dump_count;                      /* _ARGO: integer,                  synonym: DrCfgAutoDumpCnt */
    int64_t dr_config_auto_dump_fail_count;                 /* _ARGO: integer,                  synonym: DrCfgAutoDumpFailCnt */

    int64_t dr_config_snapshot_dump_count;                  /* _ARGO: integer,                  synonym: DrCfgSnapshotDumpCnt */
    int64_t dr_config_snapshot_dump_fail_count;             /* _ARGO: integer,                  synonym: DrCfgSnapshotDumpFailCnt */

    int64_t dr_config_snapshot_current_count;               /* _ARGO: integer,                  synonym: DrCfgSnapshotCurrentCnt */

    uint8_t vm_id_fail;                                     /* _ARGO: integer */
    uint8_t disk_id_fail;                                   /* _ARGO: integer */

    uint8_t imds_disabled;                                  /* _ARGO: integer */
    uint8_t imdsv2_required;                                /* _ARGO: integer */

    int64_t total_throughput_bits_per_sec;                  /* _ARGO: integer */
};

/*
 * NP connector wireguard stats
 */

struct np_connector_wireguard_stats {                         /* _ARGO: object_definition */

    int64_t this_npconnector_listen_port;                      /* _ARGO: integer */

    int64_t peers_count_total;                                 /* _ARGO: integer */
    int64_t peers_count_total_max;                             /* _ARGO: integer */
    int64_t peers_count_npgateways;                            /* _ARGO: integer */

    int64_t allowed_ips_total_cnt;                             /* _ARGO: integer */
    int64_t allowed_ips_total_cnt_max;                         /* _ARGO: integer */
    int64_t allowed_ips_npgateways_cnt;                        /* _ARGO: integer */

    int64_t rx_total_bytes;                                    /* _ARGO: integer */
    int64_t tx_total_bytes;                                    /* _ARGO: integer */
    int64_t rx_npgateways_bytes;                               /* _ARGO: integer */
    int64_t tx_npgateways_bytes;                               /* _ARGO: integer */

    int64_t rx_total_bytes_per_sec;                            /* _ARGO: integer */
    int64_t tx_total_bytes_per_sec;                            /* _ARGO: integer */
    int64_t rx_npgateways_bytes_per_sec;                       /* _ARGO: integer */
    int64_t tx_npgateways_bytes_per_sec;                       /* _ARGO: integer */

    int64_t handshake_npgateways_hist_0;                       /* _ARGO: integer */
    int64_t handshake_npgateways_hist_1;                       /* _ARGO: integer */
    int64_t handshake_npgateways_hist_2;                       /* _ARGO: integer */

    int64_t persistent_keepalive_off_count;                    /* _ARGO: integer */

    int64_t npconnector_stats_monitor_cnt;                     /* _ARGO: integer */
    int64_t npconnector_stats_monitor_error_cnt;               /* _ARGO: integer */
    int64_t allowed_ips_npgateways_get_error_cnt;              /* _ARGO: integer */

    int64_t peers_count_invalid;                               /* _ARGO: integer */
    int64_t allowed_ips_invalids_cnt;                          /* _ARGO: integer */
    int64_t rx_invalids_bytes;                                 /* _ARGO: integer */
    int64_t tx_invalids_bytes;                                 /* _ARGO: integer */
    int64_t rx_invalids_bytes_per_sec;                         /* _ARGO: integer */
    int64_t tx_invalids_bytes_per_sec;                         /* _ARGO: integer */

    int64_t peer_type_get_err_cnt;                             /* _ARGO: integer */
    int64_t peer_type_get_err_total_cnt;                       /* _ARGO: integer */
};

/*
 * ZVM stats for assistant
 */
struct zpn_assistant_zvm_stats {                            /* _ARGO: object_definition */

    /* ZVM stats*/
    uint32_t reg_id_fohh_http_create_count;                 /* _ARGO: integer */
    uint32_t reg_id_fohh_http_free_count;                   /* _ARGO: integer */

    uint32_t reg_id_imdsv2_token_alloc_count;               /* _ARGO: integer */
    uint32_t reg_id_imdsv2_token_free_count;                /* _ARGO: integer */

    uint32_t reg_id_api_ver_alloc_count;                    /* _ARGO: integer */
    uint32_t reg_id_api_ver_free_count;                     /* _ARGO: integer */

    uint32_t reg_id_query_count;                            /* _ARGO: integer */
    uint32_t reg_id_query_success_count;                    /* _ARGO: integer */
    uint32_t reg_id_query_failure_count;                    /* _ARGO: integer */
};

/*
 * Upgrade stats for assistant
 */
struct zpn_assistant_upgrade_stats {                        /* _ARGO: object_definition */
    uint64_t os_upgrade_fail;                               /* _ARGO: integer */
    uint64_t sarge_upgrade_fail;                            /* _ARGO: integer */
    uint64_t os_upgrade_success;                            /* _ARGO: integer */
    uint64_t sarge_upgrade_success;                         /* _ARGO: integer */
    uint64_t os_upgrade_timeout;                            /* _ARGO: integer */
    uint64_t sarge_os_cfg_read_fail;                        /* _ARGO: integer */
    uint64_t sudo_path_fail;                                /* _ARGO: integer */
    uint64_t package_manager_path_fail;                     /* _ARGO: integer */
};

/*
 * Stats for mmdb files download
 */
struct zpn_pbroker_file_stats {         /* _ARGO: object_definition */
    int total_key_retry_count_geo;      /* _ARGO: integer */
    int total_key_retry_count_isp;      /* _ARGO: integer */
    int total_file_retry_count_geo;     /* _ARGO: integer */
    int total_file_retry_count_isp;     /* _ARGO: integer */
    int num_fetch_fail_geo;             /* _ARGO: integer */
    int num_fetch_fail_isp;             /* _ARGO: integer */
    int key_fail_cnt_geo;               /* _ARGO: integer */
    int key_fail_cnt_isp;               /* _ARGO: integer */
    int num_key_cb_geo;                 /* _ARGO: integer */
    int num_key_cb_isp;                 /* _ARGO: integer */
    size_t file_size_geo;               /* _ARGO: integer */
    size_t file_size_isp;               /* _ARGO: integer */
    double download_time_geo;           /* _ARGO: double */
    double download_time_isp;           /* _ARGO: double */
    double download_speed_geo;          /* _ARGO: double */
    double download_speed_isp;          /* _ARGO: double */
    int rollback_cnt;                   /* _ARGO: integer */
};


/*
 * Stats for pse dns dispatcher resolution
 */
struct zpn_pbroker_dns_dispatcher_stats {           /* _ARGO: object_definition */
    // Local dispatcher DNS stats of PSE
    int64_t total_dns_local_dispatch_req;                    /* _ARGO: integer */
    int64_t total_dns_local_dispatch_err_req;                /* _ARGO: integer */
    int64_t total_dns_local_dispatch_resp;                   /* _ARGO: integer */
    int64_t total_dns_local_dispatch_err_resp;               /* _ARGO: integer */
    int64_t last_recvd_dns_local_dispatch_rtt;               /* _ARGO: integer */
    int64_t total_dns_local_dispatch_resp_delta;             /* _ARGO: integer */
    int64_t avg_dns_local_dispatch_rtt_us;                   /* _ARGO: integer */
    int64_t max_dns_local_dispatch_rtt_us;                   /* _ARGO: integer */


    // Public dispatcher DNS stats of PSE
    int64_t total_dns_public_dispatch_req;                   /* _ARGO: integer */
    int64_t total_dns_public_dispatch_err_req;               /* _ARGO: integer */
    int64_t total_dns_public_dispatch_resp;                  /* _ARGO: integer */
    int64_t total_dns_public_dispatch_err_resp;              /* _ARGO: integer */
    int64_t last_recvd_dns_public_dispatch_rtt;              /* _ARGO: integer */
    int64_t total_dns_public_dispatch_resp_delta;            /* _ARGO: integer */
    int64_t avg_dns_public_dispatch_rtt_us;                  /* _ARGO: integer */
    int64_t max_dns_public_dispatch_rtt_us;                  /* _ARGO: integer */
    int64_t total_dns_dispatch_err_timeout;                  /* _ARGO: integer */
};

/*
 * Comprehensive stats log for private broker
 */
struct zpn_pbroker_comprehensive_stats {        /* _ARGO: object_definition */
    int64_t cloud_time_us;                      /* _ARGO: integer, adj_time,        synonym: LogTimestamp */
    int64_t g_cst;                              /* _ARGO: integer, stringify */
    int64_t g_pse;                              /* _ARGO: integer, stringify,       synonym: PrivateSE */
    int64_t g_sitec;                            /* _ARGO: integer, stringify,       synonym: SiteController */
    int64_t g_microtenant;                      /* _ARGO: integer, stringify, nozero synonym: MicroTenantID */

    double  cpu_load_avg_per_core;              /* _ARGO: double */
    int32_t cpu_util_percent;                   /* _ARGO: integer,                  synonym: CPUUtilization */
    int32_t total_system_mem_mb;                /* _ARGO: integer,                  synonym: TotalSystemMemoryInMB */
    int32_t mem_util_system_percent;            /* _ARGO: integer,                  synonym: SystemMemoryUtilization */
    int32_t mem_util_process_percent;           /* _ARGO: integer,                  synonym: ProcessMemoryUtilization */

    int32_t app_count;                          /* _ARGO: integer,                  synonym: AppCount */

    int64_t active_conn_to_broker;              /* _ARGO: integer,                  synonym: ActiveConnectionsToPublicSE */
    int64_t backed_off_conn_to_broker;          /* _ARGO: integer,                  synonym: DisconnectedConnectionsToPublicSE */
    int64_t active_conn_to_connector;           /* _ARGO: integer,                  synonym: ActiveConnectionsToConnector */

    int64_t to_broker_bytes;                    /* _ARGO: integer,                  synonym: TransmittedBytesToPublicSE */
    int64_t from_broker_bytes;                  /* _ARGO: integer,                  synonym: ReceivedBytesFromPublicSE */
    int64_t to_connector_bytes;                 /* _ARGO: integer,                  synonym: TransmittedBytesToConnector */
    int64_t from_connector_bytes;               /* _ARGO: integer,                  synonym: ReceivedBytesFromConnector */

    int64_t to_broker_bytes_rate;               /* _ARGO: integer,                  synonym: TransmittingBytesRatePerSecToPublicSE */
    int64_t from_broker_bytes_rate;             /* _ARGO: integer,                  synonym: ReceivingBytesRatePerSecFromPublicSE */
    int64_t to_connector_bytes_rate;            /* _ARGO: integer,                  synonym: TransmittingBytesRatePerSecToConnector */
    int64_t from_connector_bytes_rate;          /* _ARGO: integer,                  synonym: ReceivingBytesRatePerSecFromConnector */

    int64_t to_broker_bytes_delta;              /* _ARGO: integer,                  synonym: TransmittedBytesToPublicSEDelta */
    int64_t from_broker_bytes_delta;            /* _ARGO: integer,                  synonym: ReceivedBytesFromPublicSEDelta */
    int64_t to_connector_bytes_delta;           /* _ARGO: integer,                  synonym: TransmittedBytesToConnectorDelta */
    int64_t from_connector_bytes_delta;         /* _ARGO: integer,                  synonym: ReceivedBytesFromConnectorDelta */

    int64_t total_mtunnel_created;              /* _ARGO: integer,                  synonym: AppConnectionsCreated */
    int64_t total_mtunnel_freed;                /* _ARGO: integer,                  synonym: AppConnectionsCleared */
    int64_t current_active_mtunnel_count;       /* _ARGO: integer,                  synonym: AppConnectionsActive */

    int64_t num_system_tcpv4_socket_inuse;      /* _ARGO: integer,                  synonym: UsedTCPPortsIPv4 */
    int64_t num_system_udpv4_socket_inuse;      /* _ARGO: integer,                  synonym: UsedUDPPortsIPv4 */
    int64_t num_system_tcpv6_socket_inuse;      /* _ARGO: integer,                  synonym: UsedTCPPortsIPv6 */
    int64_t num_system_udpv6_socket_inuse;      /* _ARGO: integer,                  synonym: UsedUDPPortsIPv6 */
    int num_usable_ports;                       /* _ARGO: integer,                  synonym: AvailablePorts */

    int64_t system_fd_max;                      /* _ARGO: integer,                  synonym: SystemMaximumFileDescriptors */
    int64_t system_fd_in_use;                   /* _ARGO: integer,                  synonym: SystemUsedFileDescriptors */
    int64_t process_fd_max;                     /* _ARGO: integer,                  synonym: ProcessMaximumFileDescriptors */
    int64_t process_fd_in_use;                  /* _ARGO: integer,                  synonym: ProcessUsedFileDescriptors */

    int64_t total_disk_bytes;                   /* _ARGO: integer,                  synonym: TotalDiskBytes */
    int64_t available_disk_bytes;               /* _ARGO: integer,                  synonym: AvailableDiskBytes */

    int64_t active_control_conn_to_connector;   /* _ARGO: integer,                  synonym: ActiveControlConnectionsToConnector */

    /* Customer DR stats */
    int64_t dr_activation_on_count;             /* _ARGO: integer,                  synonym: DrActivationOnCnt */
    int64_t dr_activation_test_count;           /* _ARGO: integer,                  synonym: DrActivationTestCnt */
    int64_t dr_activation_off_count;            /* _ARGO: integer,                  synonym: DrActivationOffCnt */

    int64_t dr_activation_err_cnt;              /* _ARGO: integer,                  synonym: DrActivationErrCnt */

    int64_t dr_activation_req_cnt;              /* _ARGO: integer,                  synonym: DrActivationReqCnt */
    int64_t dr_activation_resp_cnt;             /* _ARGO: integer,                  synonym: DrActivationRespCnt */
    int64_t dr_activation_no_resp_cnt;          /* _ARGO: integer,                  synonym: DrActivationNoRespCnt */
    int64_t dr_activation_err_resp_cnt;         /* _ARGO: integer,                  synonym: DrActivationErrRespCnt */

    int64_t dr_config_auto_dump_count;          /* _ARGO: integer,                  synonym: DrCfgAutoDumpCnt */
    int64_t dr_config_auto_dump_fail_count;     /* _ARGO: integer,                  synonym: DrCfgAutoDumpFailCnt */

    int64_t dr_config_snapshot_dump_count;      /* _ARGO: integer,                  synonym: DrCfgSnapshotDumpCnt */
    int64_t dr_config_snapshot_dump_fail_count; /* _ARGO: integer,                  synonym: DrCfgSnapshotDumpFailCnt */

    int64_t dr_config_snapshot_current_count;   /* _ARGO: integer,                  synonym: DrCfgSnapshotCurrentCnt */

    /* mtunnel related stats */
    int64_t mt_promote_success_count;           /* _ARGO: integer                   synonym: MtPromoteSuccessCnt */
    int64_t mt_promote_fail_count;              /* _ARGO: integer                   synonym: MtPromoteFailCnt */
    int64_t mt_promote_terminated_count;        /* _ARGO: integer                   synonym: MtPromoteTerminatedCnt */
    int64_t mt_promote_c2c_regex_bypass_count;        /* _ARGO: integer                   synonym: MtPromoteC2CRegexBypassCnt */

    /* Aggregate client connection stats */
    int64_t total_client_connections_created;            /* _ARGO: integer,                  synonym: TotalClientConnectionsCreated */
    int64_t total_client_connections_freed;              /* _ARGO: integer,                  synonym: TotalClientConnectionsCleared */
    int64_t total_client_connections_init_failed;        /* _ARGO: integer,                  synonym: TotalClientConnectionsInitFailed */
    int64_t total_active_client_connections_count;       /* _ARGO: integer,                  synonym: TotalClientConnectionsActive */
    int64_t approx_peak_active_client_connections_count; /* _ARGO: integer,                  synonym: ApproxPeakClientConnectionsActive */

    /* ZCC connection statistics */
    int64_t zcc_connections_created;                     /* _ARGO: integer,                  synonym: ZCCConnectionsCreated */
    int64_t zcc_connections_freed;                       /* _ARGO: integer,                  synonym: ZCCConnectionsCleared */
    int64_t zcc_active_connections_count;                /* _ARGO: integer,                  synonym: ZCCConnectionsActive */
    int64_t zcc_connections_init_failed;                 /* _ARGO: integer,                  synonym: ZCCConnectionsInitFailed */

    /* Branch Connector connection statistics */
    int64_t branch_connector_connections_created;        /* _ARGO: integer,                  synonym: BranchConnectorConnectionsCreated */
    int64_t branch_connector_connections_freed;          /* _ARGO: integer,                  synonym: BranchConnectorConnectionsCleared */
    int64_t branch_connector_active_connections_count;   /* _ARGO: integer,                  synonym: BranchConnectorConnectionsActive */
    int64_t branch_connector_connections_init_failed;    /* _ARGO: integer,                  synonym: BranchConnectorConnectionsInitFailed */

    /* Edge Connector connection statistics */
    int64_t edge_connector_connections_created;          /* _ARGO: integer,                  synonym: EdgeConnectorConnectionsCreated */
    int64_t edge_connector_connections_freed;            /* _ARGO: integer,                  synonym: EdgeConnectorConnectionsCleared */
    int64_t edge_connector_active_connections_count;     /* _ARGO: integer,                  synonym: EdgeConnectorConnectionsActive */
    int64_t edge_connector_connections_init_failed;      /* _ARGO: integer,                  synonym: EdgeConnectorConnectionsInitFailed */

    /* VDI connection statistics */
    int64_t vdi_connections_created;                     /* _ARGO: integer,                  synonym: VDIConnectionsCreated */
    int64_t vdi_connections_freed;                       /* _ARGO: integer,                  synonym: VDIConnectionsCleared */
    int64_t vdi_active_connections_count;                /* _ARGO: integer,                  synonym: VDIConnectionsActive */
    int64_t vdi_connections_init_failed;                 /* _ARGO: integer,                  synonym: VDIConnectionsInitFailed */

    /* Machine Tunnel connection statistics */
    int64_t machine_tunnel_connections_created;          /* _ARGO: integer,                  synonym: MachineTunnelConnectionsCreated */
    int64_t machine_tunnel_connections_freed;            /* _ARGO: integer,                  synonym: MachineTunnelConnectionsCleared */
    int64_t machine_tunnel_active_connections_count;     /* _ARGO: integer,                  synonym: MachineTunnelConnectionsActive */
    int64_t machine_tunnel_connections_init_failed;      /* _ARGO: integer,                  synonym: MachineTunnelConnectionsInitFailed */

    /* Zapp Partner connection statistics */
    int64_t zapp_partner_connections_created;          /* _ARGO: integer,                  synonym: ZappPartnerConnectionsCreated */
    int64_t zapp_partner_connections_freed;            /* _ARGO: integer,                  synonym: ZappPartnerConnectionsCleared */
    int64_t zapp_partner_active_connections_count;     /* _ARGO: integer,                  synonym: ZappPartnerConnectionsActive */
    int64_t zapp_partner_connections_init_failed;      /* _ARGO: integer,                  synonym: ZappPartnerConnectionsInitFailed */

    int64_t other_client_connections_created;            /* _ARGO: integer,                  synonym: OtherClientConnectionsCreated */
    int64_t other_client_connections_freed;              /* _ARGO: integer,                  synonym: OtherClientConnectionsCleared */
    int64_t other_client_active_connections_count;       /* _ARGO: integer,                  synonym: OtherClientConnectionsActive */
    int64_t other_client_connections_init_failed;        /* _ARGO: integer,                  synonym: OtherClientConnectionsInitFailed */

    /* MMDB stats*/
    uint64_t num_geo_queries;                            /* _ARGO: integer */
    uint64_t num_geo_cc_queries_failed;                  /* _ARGO: integer */
    uint64_t num_geo_city_queries_failed;                /* _ARGO: integer */
    uint64_t num_geo_subdivision_queries_failed;         /* _ARGO: integer */
    double geo_query_rate;                               /* _ARGO: double */
    uint64_t num_isp_queries;                            /* _ARGO: integer */
    uint64_t num_isp_queries_failed;                     /* _ARGO: integer */
    double isp_query_rate;                               /* _ARGO: double */

    /* Total and client-type specific mtunnel stats */
    int64_t total_mtunnels_created;             /* _ARGO: integer,                  synonym: TotalClientMtunnelsCreated */
    int64_t total_mtunnels_freed;               /* _ARGO: integer,                  synonym: TotalClientMtunnelsFreed */
    int64_t total_mtunnels_reaped_in;           /* _ARGO: integer,                  synonym: TotalClientMtunnelsReapedIn */
    int64_t total_mtunnels_reaped_out;          /* _ARGO: integer,                  synonym: TotalClientMtunnelsReapedOut */
    int64_t total_mtunnels_active;              /* _ARGO: integer,                  synonym: TotalClientMtunnelsActive */
    int64_t total_mtunnels_init_failed;         /* _ARGO: integer,                  synonym: TotalClientMtunnelsInitFailed */
    int64_t approx_mtunnels_peak_active;        /* _ARGO: integer,                  synonym: ApproxClientMtunnelsPeakActive */
    int64_t total_mtunnels_c2c_regex_bypass;        /* _ARGO: integer,                  synonym: TotalMtunnelsC2CRegexBypass */

    int64_t zcc_mtunnels_created;               /* _ARGO: integer,                  synonym: ZCCMtunnelsCreated */
    int64_t zcc_mtunnels_freed;                 /* _ARGO: integer,                  synonym: ZCCMtunnelsFreed */
    int64_t zcc_mtunnels_reaped_in;             /* _ARGO: integer,                  synonym: ZCCMtunnelsReapedIn */
    int64_t zcc_mtunnels_reaped_out;            /* _ARGO: integer,                  synonym: ZCCMtunnelsReapedOut */
    int64_t zcc_mtunnels_active;                /* _ARGO: integer,                  synonym: ZCCMtunnelsActive */
    int64_t zcc_mtunnels_init_failed;           /* _ARGO: integer,                  synonym: ZCCMtunnelsInitFailed */
    int64_t zcc_mtunnels_c2c_regex_bypass;      /* _ARGO: integer,                  synonym: ZCCMtunnelsC2CRegexBypass */

    int64_t bc_mtunnels_created;                /* _ARGO: integer,                  synonym: BranchConnectorMtunnelsCreated */
    int64_t bc_mtunnels_freed;                  /* _ARGO: integer,                  synonym: BranchConnectorMtunnelsFreed */
    int64_t bc_mtunnels_reaped_in;              /* _ARGO: integer,                  synonym: BranchConnectorMtunnelsReapedIn */
    int64_t bc_mtunnels_reaped_out;             /* _ARGO: integer,                  synonym: BranchConnectorMtunnelsReapedOut */
    int64_t bc_mtunnels_active;                 /* _ARGO: integer,                  synonym: BranchConnectorMtunnelsActive */
    int64_t bc_mtunnels_init_failed;            /* _ARGO: integer,                  synonym: BranchConnectorMtunnelsInitFailed */
    int64_t bc_mtunnels_c2c_regex_bypass;       /* _ARGO: integer,                  synonym: BranchConnectorMtunnelsC2CRegexBypass */

    int64_t ec_mtunnels_created;                /* _ARGO: integer,                  synonym: EdgeConnectorMtunnelsCreated */
    int64_t ec_mtunnels_freed;                  /* _ARGO: integer,                  synonym: EdgeConnectorMtunnelsFreed */
    int64_t ec_mtunnels_reaped_in;              /* _ARGO: integer,                  synonym: EdgeConnectorMtunnelsReapedIn */
    int64_t ec_mtunnels_reaped_out;             /* _ARGO: integer,                  synonym: EdgeConnectorMtunnelsReapedOut */
    int64_t ec_mtunnels_active;                 /* _ARGO: integer,                  synonym: EdgeConnectorMtunnelsActive */
    int64_t ec_mtunnels_init_failed;            /* _ARGO: integer,                  synonym: EdgeConnectorMtunnelsInitFailed */
    int64_t ec_mtunnels_c2c_regex_bypass;       /* _ARGO: integer,                  synonym:EdgeConnectorMtunnelsC2CRegexBypass */

    int64_t vdi_mtunnels_created;               /* _ARGO: integer,                  synonym: VDIMtunnelsCreated */
    int64_t vdi_mtunnels_freed;                  /* _ARGO: integer,                 synonym: VDIMtunnelsFreed */
    int64_t vdi_mtunnels_reaped_in;              /* _ARGO: integer,                 synonym: VDIMtunnelsReapedIn */
    int64_t vdi_mtunnels_reaped_out;             /* _ARGO: integer,                 synonym: VDIMtunnelsReapedOut */
    int64_t vdi_mtunnels_active;                 /* _ARGO: integer,                 synonym: VDIMtunnelsActive */
    int64_t vdi_mtunnels_init_failed;            /* _ARGO: integer,                 synonym: VDIMtunnelsInitFailed */
    int64_t vdi_mtunnels_c2c_regex_bypass;       /* _ARGO: integer,                 synonym:  VDIMtunnelsC2CRegexBypass */

    int64_t mt_mtunnels_created;                /* _ARGO: integer,                  synonym: MachineTunnelMtunnelsCreated */
    int64_t mt_mtunnels_freed;                  /* _ARGO: integer,                  synonym: MachineTunnelMtunnelsFreed */
    int64_t mt_mtunnels_reaped_in;              /* _ARGO: integer,                  synonym: MachineTunnelMtunnelsReapedIn */
    int64_t mt_mtunnels_reaped_out;             /* _ARGO: integer,                  synonym: MachineTunnelMtunnelsReapedOut */
    int64_t mt_mtunnels_active;                 /* _ARGO: integer,                  synonym: MachineTunnelMtunnelsActive */
    int64_t mt_mtunnels_init_failed;            /* _ARGO: integer,                  synonym: MachineTunnelMtunnelsInitFailed */
    int64_t mt_mtunnels_c2c_regex_bypass;           /* _ARGO: integer,    synonym:MachineTunnelMtunnelsC2CRegexBypass */

    int64_t zp_mtunnels_created;                /* _ARGO: integer,                  synonym: ZappPartnerMtunnelsCreated */
    int64_t zp_mtunnels_freed;                  /* _ARGO: integer,                  synonym: ZappPartnerMtunnelsFreed */
    int64_t zp_mtunnels_reaped_in;              /* _ARGO: integer,                  synonym: ZappPartnerMtunnelsReapedIn */
    int64_t zp_mtunnels_reaped_out;             /* _ARGO: integer,                  synonym: ZappPartnerMtunnelsReapedOut */
    int64_t zp_mtunnels_active;                 /* _ARGO: integer,                  synonym: ZappPartnerMtunnelsActive */
    int64_t zp_mtunnels_init_failed;            /* _ARGO: integer,                  synonym: ZappPartnerMtunnelsInitFailed */
    int64_t zp_mtunnels_c2c_regex_bypass;           /* _ARGO: integer,    synonym:ZappPartnerMtunnelsC2CRegexBypass */

    int64_t other_mtunnels_created;             /* _ARGO: integer,                  synonym: OtherClientMtunnelsCreated */
    int64_t other_mtunnels_freed;               /* _ARGO: integer,                  synonym: OtherClientMtunnelsFreed */
    int64_t other_mtunnels_reaped_in;           /* _ARGO: integer,                  synonym: OtherClientMtunnelsReapedIn */
    int64_t other_mtunnels_reaped_out;          /* _ARGO: integer,                  synonym: OtherClientMtunnelsReapedOut */
    int64_t other_mtunnels_active;              /* _ARGO: integer,                  synonym: OtherClientMtunnelsActive */
    int64_t other_mtunnels_init_failed;         /* _ARGO: integer,                  synonym: OtherClientMtunnelsInitFailed */
    int64_t other_mtunnels_c2c_regex_bypass;           /* _ARGO: integer,    synonym:OtherMtunnelsC2CRegexBypass */

    int64_t total_cache_dispatch_hit_count;     /* _ARGO: integer,                  synonym: TotalCacheDispatchHitCount */
    int64_t total_cache_dispatch_miss_count;    /* _ARGO: integer,                  synonym: TotalCacheDispatchMissCount */
    int64_t total_cloud_dispatch_fail_count;    /* _ARGO: integer,                  synonym: TotalCloudDispatchFailCount */
    int64_t total_max_dispatch_exceeded_count;  /* _ARGO: integer,                  synonym: TotalMaxDispatchExceededCount */
    int64_t total_cloud_dispatch_count;                 /* _ARGO: integer,          synonym: TotalCloudDispatchCount */
    int64_t local_dispatcher_state_eval_avg_time_us;    /* _ARGO: integer,          synonym: LocalDispStateEvalAvgTime */
    int64_t local_dispatcher_state_eval_max_time_us;    /* _ARGO: integer,          synonym: LocalDispStateEvalMaxTime */

    int64_t c2c_regex_eval_avg_time_us;         /* _ARGO: integer,                  synonym: C2CRegexEvalAvgTime */

    uint8_t     vm_id_fail;                    /* _ARGO: integer */
    uint8_t     disk_id_fail;                  /* _ARGO: integer */

    uint8_t     imds_disabled;                 /* _ARGO: integer */
    uint8_t     imdsv2_required;               /* _ARGO: integer */
    int64_t pse_restart_invalid_version;       /* _ARGO: integer*/
    int64_t denied_version_del_fail;           /* _ARGO: integer */
    uint8_t argo_high_tide_state;              /* _ARGO: integer*/
};

/*
 * Comprehensive stats log for site controller
 */
struct zpn_sitec_comprehensive_stats {          /* _ARGO: object_definition */
    int64_t cloud_time_us;                      /* _ARGO: integer, adj_time,        synonym: LogTimestamp */
    int64_t g_cst;                              /* _ARGO: integer, stringify */
    int64_t g_sitec;                            /* _ARGO: integer, stringify,       synonym: SiteController */
    int64_t g_microtenant;                      /* _ARGO: integer, stringify, nozero synonym: MicroTenantID */

    double  cpu_load_avg_per_core;              /* _ARGO: double */
    int32_t cpu_util_percent;                   /* _ARGO: integer,                  synonym: CPUUtilization */
    int32_t total_system_mem_mb;                /* _ARGO: integer,                  synonym: TotalSystemMemoryInMB */
    int32_t mem_util_system_percent;            /* _ARGO: integer,                  synonym: SystemMemoryUtilization */
    int32_t mem_util_process_percent;           /* _ARGO: integer,                  synonym: ProcessMemoryUtilization */

    int64_t active_conn_to_broker;              /* _ARGO: integer,                  synonym: ActiveConnectionsToPublicSE */
    int64_t backed_off_conn_to_broker;          /* _ARGO: integer,                  synonym: DisconnectedConnectionsToPublicSE */
    int64_t active_conn_to_connector;           /* _ARGO: integer,                  synonym: ActiveConnectionsToConnector */
    int64_t active_conn_to_pbroker;             /* _ARGO: integer,                  synonym: ActiveConnectionsToPrivateSE */

    int64_t to_broker_bytes;                    /* _ARGO: integer,                  synonym: TransmittedBytesToPublicSE */
    int64_t from_broker_bytes;                  /* _ARGO: integer,                  synonym: ReceivedBytesFromPublicSE */
    int64_t to_connector_bytes;                 /* _ARGO: integer,                  synonym: TransmittedBytesToConnector */
    int64_t from_connector_bytes;               /* _ARGO: integer,                  synonym: ReceivedBytesFromConnector */
    int64_t to_pbroker_bytes;                   /* _ARGO: integer,                  synonym: TransmittedBytesToPrivateSE */
    int64_t from_pbroker_bytes;                 /* _ARGO: integer,                  synonym: ReceivedBytesFromPrivateSE */

    int64_t to_broker_bytes_rate;               /* _ARGO: integer,                  synonym: TransmittingBytesRatePerSecToPublicSE */
    int64_t from_broker_bytes_rate;             /* _ARGO: integer,                  synonym: ReceivingBytesRatePerSecFromPublicSE */
    int64_t to_connector_bytes_rate;            /* _ARGO: integer,                  synonym: TransmittingBytesRatePerSecToConnector */
    int64_t from_connector_bytes_rate;          /* _ARGO: integer,                  synonym: ReceivingBytesRatePerSecFromConnector */
    int64_t to_pbroker_bytes_rate;              /* _ARGO: integer,                  synonym: TransmittingBytesRatePerSecToPrivateSE */
    int64_t from_pbroker_bytes_rate;            /* _ARGO: integer,                  synonym: ReceivingBytesRatePerSecFromPrivateSE */

    int64_t to_broker_bytes_delta;              /* _ARGO: integer,                  synonym: TransmittedBytesToPublicSEDelta */
    int64_t from_broker_bytes_delta;            /* _ARGO: integer,                  synonym: ReceivedBytesFromPublicSEDelta */
    int64_t to_connector_bytes_delta;           /* _ARGO: integer,                  synonym: TransmittedBytesToConnectorDelta */
    int64_t from_connector_bytes_delta;         /* _ARGO: integer,                  synonym: ReceivedBytesFromConnectorDelta */
    int64_t to_pbroker_bytes_delta;             /* _ARGO: integer,                  synonym: TransmittedBytesToPrivateSEDelta */
    int64_t from_pbroker_bytes_delta;           /* _ARGO: integer,                  synonym: ReceivedBytesFromPrivateSEDelta */

    int64_t num_system_tcpv4_socket_inuse;      /* _ARGO: integer,                  synonym: UsedTCPPortsIPv4 */
    int64_t num_system_udpv4_socket_inuse;      /* _ARGO: integer,                  synonym: UsedUDPPortsIPv4 */
    int64_t num_system_tcpv6_socket_inuse;      /* _ARGO: integer,                  synonym: UsedTCPPortsIPv6 */
    int64_t num_system_udpv6_socket_inuse;      /* _ARGO: integer,                  synonym: UsedUDPPortsIPv6 */
    int num_usable_ports;                       /* _ARGO: integer,                  synonym: AvailablePorts */

    int64_t system_fd_max;                      /* _ARGO: integer,                  synonym: SystemMaximumFileDescriptors */
    int64_t system_fd_in_use;                   /* _ARGO: integer,                  synonym: SystemUsedFileDescriptors */
    int64_t process_fd_max;                     /* _ARGO: integer,                  synonym: ProcessMaximumFileDescriptors */
    int64_t process_fd_in_use;                  /* _ARGO: integer,                  synonym: ProcessUsedFileDescriptors */

    int64_t total_disk_bytes;                   /* _ARGO: integer,                  synonym: TotalDiskBytes */
    int64_t available_disk_bytes;               /* _ARGO: integer,                  synonym: AvailableDiskBytes */

    /* MMDB stats*/
    uint64_t num_geo_queries;                            /* _ARGO: integer */
    uint64_t num_geo_cc_queries_failed;                  /* _ARGO: integer */
    uint64_t num_geo_city_queries_failed;                /* _ARGO: integer */
    uint64_t num_geo_subdivision_queries_failed;         /* _ARGO: integer */
    double geo_query_rate;                               /* _ARGO: double */
    uint64_t num_isp_queries;                            /* _ARGO: integer */
    uint64_t num_isp_queries_failed;                     /* _ARGO: integer */
    double isp_query_rate;                               /* _ARGO: double */

    /* Redirect stats */
    int64_t     redirect_success;                       /* _ARGO: integer, synonym: RedirectSuccess */
    int64_t     redirect_failure;                       /* _ARGO: integer, synonym: RedirectFailure */
    int64_t     redirect_success_rate;                  /* _ARGO: integer, synonym: RedirectSuccessRate */
    int64_t     redirect_failure_rate;                  /* _ARGO: integer, synonym: RedirectFailureRate */
    int64_t     gf_success;                             /* _ARGO: integer, synonym: GeoFencingRedirectSuccess */
    int64_t     gf_failure;                             /* _ARGO: integer, synonym: ReGeoFencingRedirectFailure */
    int64_t     redirects_to_alt_cloud;                 /* _ARGO: integer, synonym:NumRedirectToAltCloud */
    int64_t     no_brokers_in_alt_cloud;                /* _ARGO: integer, synonym:NoPSEAltCloud */
    int64_t     no_target_instance;                     /* _ARGO: integer, synonym:NoPSEToRedirect */

    /* SiteSP stats */
    uint64_t get_saml_req_success;                      /* _ARGO: integer, synonym: GenSamlReqSuccess */
    uint64_t saml_resp_validate_success;                /* _ARGO: integer, synonym: SamlRespValidationSuccess */
    uint64_t get_saml_success;                          /* _ARGO: integer, synonym: GetSamlSuccess */
    uint64_t missing_params;                            /* _ARGO: integer, synonym: LoginMissingParams */
    uint64_t sig_fail;                                  /* _ARGO: integer, synonym: SignSamlReqFailure */
    uint64_t session_create_fail;                       /* _ARGO: integer, synonym: SessionCreateFailure */
    uint64_t process_saml_resp_http_validation_fail;    /* _ARGO: integer, synonym: SamlRespHttpValidationFail */
    uint64_t forward_saml_resp;                         /* _ARGO: integer, synonym: FwdSamlResp */
    uint64_t session_lookup_fail;                       /* _ARGO: integer, synonym: SessionLookupFail */
    uint64_t saml_resp_decode_fail;                     /* _ARGO: integer, synonym: SamlRespDecodeFail */
    uint64_t saml_resp_validation_fail;                 /* _ARGO: integer, synonym: SampRespValidationFail */
    uint64_t get_saml_http_validation_fail;             /* _ARGO: integer, synonym: GetSamlHttpValidateFail */
    uint64_t challenge_validation_fail;                 /* _ARGO: integer, synonym: ChallengeValidationFail */
    uint64_t get_saml_fail;                             /* _ARGO: integer, synonym: GetSamlFail */

    /* siem stats*/
    uint64_t argo_log_copy_success;                     /* _ARGO: integer, synonym: SiemLogCopySuccess */
    uint64_t argo_log_copy_failure;                     /* _ARGO: integer, synonym: SiemLogCopyFailure */

    uint64_t trans_log_success;                         /* _ARGO: integer, synonym: SiemTransLogSuccess */
    uint64_t trans_log_failure;                         /* _ARGO: integer, synonym: SiemTransLogFailure */

    uint64_t auth_log_success;                          /* _ARGO: integer, synonym: SiemAuthLogSuccess */
    uint64_t auth_log_failure;                          /* _ARGO: integer, synonym: SiemAuthLogFailure */

    uint64_t ast_auth_log_success;                      /* _ARGO: integer, synonym: SiemAstAuthLogSuccess */
    uint64_t ast_auth_log_failure;                      /* _ARGO: integer, synonym: SiemAstAuthLogFailure */

    uint64_t pb_auth_log_success;                       /* _ARGO: integer, synonym: SiemPbAuthLogSuccess */
    uint64_t pb_auth_log_failure;                       /* _ARGO: integer, synonym: SiemPbAuthLogFailure */

    uint64_t ast_comprehensive_stats_log_success;       /* _ARGO: integer, synonym: SiemAstCompStatsSuccess */
    uint64_t ast_comprehensive_stats_log_failure;       /* _ARGO: integer, synonym: SiemAstCompStatsFailure */

    uint64_t pb_comprehensive_stats_log_success;        /* _ARGO: integer, synonym: SiemPbCompStatsSuccess */
    uint64_t pb_comprehensive_stats_log_failure;        /* _ARGO: integer, synonym: SiemPbCompStatsFailure */

    uint64_t sc_comprehensive_stats_log_success;        /* _ARGO: integer, synonym: SiemPccCompStatsSuccess */
    uint64_t sc_comprehensive_stats_log_failure;        /* _ARGO: integer, synonym: SiemPccCompStatsFailure */

    uint64_t ast_inspection_log_success;                /* _ARGO: integer, synonym: SiemAstInspSuccess */
    uint64_t ast_inspection_log_failure;                /* _ARGO: integer, synonym: SiemAstInspFailure */

    uint64_t ast_app_inspection_log_success;            /* _ARGO: integer, synonym: SiemAstAppInspSuccess */
    uint64_t ast_app_inspection_log_failure;            /* _ARGO: integer, synonym: SiemAstAppInspFailure */

    uint64_t ast_krb_inspection_log_success;            /* _ARGO: integer, synonym: SiemAstKrbInspSuccess */
    uint64_t ast_krb_inspection_log_failure;            /* _ARGO: integer, synonym: SiemAstKrbInspFailure */

    uint64_t ast_ldap_inspection_log_success;           /* _ARGO: integer, synonym: SiemAstLdapInspSuccess */
    uint64_t ast_ldap_inspection_log_failure;           /* _ARGO: integer, synonym: SiemAstLdapInspFailure */

    uint64_t ast_smb_inspection_log_success;            /* _ARGO: integer, synonym: SiemAstSmbInspSuccess */
    uint64_t ast_smb_inspection_log_failure;            /* _ARGO: integer, synonym: SiemAstSmbInspFailure */

    uint64_t ast_api_log_success;                       /* _ARGO: integer, synonym: SiemAstInspApiSuccess */
    uint64_t ast_api_log_failure;                       /* _ARGO: integer, synonym: SiemAstInspApiFailure */

    uint64_t siem_ids_lookup_success;                   /* _ARGO: integer, synonym: SiemIdLookupSuccess */
    uint64_t siem_ids_lookup_failure;                   /* _ARGO: integer, synonym: SiemIdLookupFailure */

    uint64_t siem_config_fetch_err_counter;             /* _ARGO: integer, synonym: SiemCfgFetchErr */
    uint64_t log_filtered_counter;                      /* _ARGO: integer, synonym: SiemLogFiltered */
    uint64_t no_memory_err_counter;                     /* _ARGO: integer, synonym: SiemNoMem */
    uint64_t serialize_err_counter;                     /* _ARGO: integer, synonym: SiemSerializeErr */
    uint64_t siem_active_connections;                   /* _ARGO: integer, synonym: SiemActiveConns */
    uint64_t firedrill_triggered_count;                 /* _ARGO: integer, synonym: FireDrillTriggeredCount */
    uint64_t firedrill_completed_count;                 /* _ARGO: integer, synonym: FireDrillCompletedCount */
    uint64_t firedrill_cmdline_disable_count;           /* _ARGO: integer, synonym: FireDrillCmdLineDisableCount */
};

struct zpn_sitec_siem_log_stats {                                 /* _ARGO: object_definition */
    uint64_t argo_log_copy_success;                               /* _ARGO: integer */
    uint64_t argo_log_copy_failure;                               /* _ARGO: integer */

    uint64_t trans_log_success;                                   /* _ARGO: integer */
    uint64_t trans_log_failure;                                   /* _ARGO: integer */

    uint64_t auth_log_success;                                    /* _ARGO: integer */
    uint64_t auth_log_failure;                                    /* _ARGO: integer */

    uint64_t ast_auth_log_success;                                /* _ARGO: integer */
    uint64_t ast_auth_log_failure;                                /* _ARGO: integer */

    uint64_t pb_auth_log_success;                                 /* _ARGO: integer */
    uint64_t pb_auth_log_failure;                                 /* _ARGO: integer */

    uint64_t ast_comprehensive_stats_log_success;                 /* _ARGO: integer */
    uint64_t ast_comprehensive_stats_log_failure;                 /* _ARGO: integer */

    uint64_t pb_comprehensive_stats_log_success;                  /* _ARGO: integer */
    uint64_t pb_comprehensive_stats_log_failure;                  /* _ARGO: integer */

    uint64_t sc_comprehensive_stats_log_success;                  /* _ARGO: integer */
    uint64_t sc_comprehensive_stats_log_failure;                  /* _ARGO: integer */

    uint64_t ast_inspection_log_success;                          /* _ARGO: integer */
    uint64_t ast_inspection_log_failure;                          /* _ARGO: integer */

    uint64_t ast_app_inspection_log_success;                      /* _ARGO: integer */
    uint64_t ast_app_inspection_log_failure;                      /* _ARGO: integer */

    uint64_t ast_krb_inspection_log_success;                      /* _ARGO: integer */
    uint64_t ast_krb_inspection_log_failure;                      /* _ARGO: integer */

    uint64_t ast_ldap_inspection_log_success;                     /* _ARGO: integer */
    uint64_t ast_ldap_inspection_log_failure;                     /* _ARGO: integer */

    uint64_t ast_smb_inspection_log_success;                      /* _ARGO: integer */
    uint64_t ast_smb_inspection_log_failure;                      /* _ARGO: integer */

    uint64_t ast_api_log_success;                                 /* _ARGO: integer */
    uint64_t ast_api_log_failure;                                 /* _ARGO: integer */

    uint64_t siem_ids_lookup_success;                             /* _ARGO: integer */
    uint64_t siem_ids_lookup_failure;                             /* _ARGO: integer */

    uint64_t siem_config_fetch_err_counter;                       /* _ARGO: integer */
    uint64_t log_filtered_counter;                                /* _ARGO: integer */
    uint64_t no_memory_err_counter;                               /* _ARGO: integer */
    uint64_t serialize_err_counter;                               /* _ARGO: integer */
    uint64_t siem_active_connections;                             /* _ARGO: integer */
};

struct zpn_sitec_siem_log_stats_per_conn {                        /* _ARGO: object_definition */
    uint64_t trans_log_success;                                   /* _ARGO: integer */
    uint64_t trans_log_failure;                                   /* _ARGO: integer */

    uint64_t auth_log_success;                                    /* _ARGO: integer */
    uint64_t auth_log_failure;                                    /* _ARGO: integer */

    uint64_t ast_auth_log_success;                                /* _ARGO: integer */
    uint64_t ast_auth_log_failure;                                /* _ARGO: integer */

    uint64_t pb_auth_log_success;                                 /* _ARGO: integer */
    uint64_t pb_auth_log_failure;                                 /* _ARGO: integer */

    uint64_t ast_comprehensive_stats_log_success;                 /* _ARGO: integer */
    uint64_t ast_comprehensive_stats_log_failure;                 /* _ARGO: integer */

    uint64_t pb_comprehensive_stats_log_success;                  /* _ARGO: integer */
    uint64_t pb_comprehensive_stats_log_failure;                  /* _ARGO: integer */

    uint64_t sc_comprehensive_stats_log_success;                  /* _ARGO: integer */
    uint64_t sc_comprehensive_stats_log_failure;                  /* _ARGO: integer */

    uint64_t ast_inspection_log_success;                          /* _ARGO: integer */
    uint64_t ast_inspection_log_failure;                          /* _ARGO: integer */

    uint64_t ast_app_inspection_log_success;                      /* _ARGO: integer */
    uint64_t ast_app_inspection_log_failure;                      /* _ARGO: integer */

    uint64_t ast_krb_inspection_log_success;                      /* _ARGO: integer */
    uint64_t ast_krb_inspection_log_failure;                      /* _ARGO: integer */

    uint64_t ast_ldap_inspection_log_success;                     /* _ARGO: integer */
    uint64_t ast_ldap_inspection_log_failure;                     /* _ARGO: integer */

    uint64_t ast_smb_inspection_log_success;                      /* _ARGO: integer */
    uint64_t ast_smb_inspection_log_failure;                      /* _ARGO: integer */

    uint64_t ast_api_log_success;                                 /* _ARGO: integer */
    uint64_t ast_api_log_failure;                                 /* _ARGO: integer */
};

/*
 * Additional debug stats for fohh conn and pbclients
 * see:  ET-56105
 */
struct zpn_pbclient_debug_stats {               /* _ARGO: object_definition */
    int64_t pb_client_close_fini_count;         /* _ARGO: integer,                  synonym: PbClientCloseFiniCount */
    int64_t pb_zrdt_client_close_count;         /* _ARGO: integer,                  synonym: PbZrdtClientCloseCount */
    int64_t pb_client_create_count;             /* _ARGO: integer,                  synonym: PbClientCreateCount */
    int64_t pb_client_no_create_count;          /* _ARGO: integer,                  synonym: PbClientNoCreateCount */
    int64_t pb_client_close_count;              /* _ARGO: integer,                  synonym: PbClientCloseCount */
    int64_t pb_client_connect_count;            /* _ARGO: integer,                  synonym: PbClientConnectCount */
    int64_t pb_client_disconnect_count;         /* _ARGO: integer,                  synonym: PbClientDisconnectCount */
    int64_t pb_client_start_auth_count;         /* _ARGO: integer,                  synonym: PbClientStartAuthCount */
    int64_t pb_client_auth_complete_count;      /* _ARGO: integer,                  synonym: PbClientStartAuthCompleteCount */

    int64_t fconn_close_count;                  /* _ARGO: integer,                  synonym: FconnCloseCount */
    int64_t fconn_direct_close_count;           /* _ARGO: integer,                  synonym: FconnDirectCloseCount */
    int64_t fconn_libevent_close_count;         /* _ARGO: integer,                  synonym: FconnLibeventCloseCount */
    int64_t fconn_free_count;                   /* _ARGO: integer,                  synonym: FconnFreeCount */
    int64_t fconn_alloc_count;                  /* _ARGO: integer,                  synonym: FconnAllocCount */
    int64_t fconn_bufferevent_alloc_count;      /* _ARGO: integer,                  synonym: FconnBuffereventAllocCount */
};

/*
 * Resiliency stats for PSE
 */
struct zpn_pse_resiliency_stats {               /* _ARGO: object_definition */
    uint64_t resiliency_last_start_time;        /* _ARGO: integer,                  synonym: ResiliencyLastStartTime */
    uint64_t resiliency_last_end_time;          /* _ARGO: integer,                  synonym: ResiliencyLastEndTime */
    uint64_t resiliency_activation_count;       /* _ARGO: integer,                  synonym: ResiliencyActivationCount */
    uint64_t resiliency_max_duration;           /* _ARGO: integer,                  synonym: ResiliencyMaxDuration */
    uint64_t resiliency_max_duration_start_time;/* _ARGO: integer,                  synonym: ResiliencyMaxDurationStartTime */

};
/*
 * Authentication message exchange between assistant and pbroker
 */
struct zpn_a2pb_authentication {      /* _ARGO: object_definition */
    const char *dr_running_mode;      /* _ARGO: string */
};

struct zpn_a2pb_authentication_ack {   /* _ARGO: object_definition */
    uint32_t auth_status;              /* _ARGO: integer */
    const char *message;               /* _ARGO: string */
};

/*
 * Stats that we use to monitor the connector state
 * this stats will be sent to broker every 5 mins and will help us to capture more insights into the connector
 */
struct zpn_assistant_monitor_stats {                                /* _ARGO: object_definition */
    int64_t cloud_time_us;                                          /* _ARGO: integer */

    int64_t max_time_elapsed_in_check_mtunnel;                      /* _ARGO: integer */
    int64_t max_time_elapsed_in_free_mtunnel;                       /* _ARGO: integer */

    int64_t num_mtunnel_in_buckets_queue;                           /* _ARGO: integer */
    int64_t num_mtunnel_in_reap_queue;                              /* _ARGO: integer */
    int64_t num_mtunnel_in_free_queue;                              /* _ARGO: integer */
    int64_t num_mtunnel_in_reap_state_but_not_moving_to_reap_queue; /* _ARGO: integer */
    int64_t num_mtunnel_in_reaping_queue_but_not_clean;             /* _ARGO: integer */

    int64_t zthread_time_skipped;                                   /* _ARGO: integer */
    int64_t zthread_time_skipped_exceed_max;                        /* _ARGO: integer */
    int64_t zthread_healthy_counter;                                /* _ARGO: integer */
    int64_t zthread_unhealthy_counter;                              /* _ARGO: integer */
    int64_t zthread_monitor_healthy_counter;                        /* _ARGO: integer */
    int64_t zthread_monitor_unhealthy_counter;                      /* _ARGO: integer */

    int64_t check_mtunnel_timer_alive_counter;                      /* _ARGO: integer */

    int64_t on_alt_cloud;                                           /* _ARGO: integer */
    int64_t alt_cloud_enabled;                                      /* _ARGO: integer */
    int64_t alt_cloud_resets;                                       /* _ARGO: integer */

    int64_t udp_health_timeout_failure_flag_enabled;                /* _ARGO: integer */

    int64_t allocator_libevent_max_bytes;                           /* _ARGO: integer */
    int64_t allocator_libevent_used_bytes;                          /* _ARGO: integer */
    int64_t allocator_libevent_used_percent;                        /* _ARGO: integer */
    int64_t allocator_libevent_out_queue_allowed_bytes;             /* _ARGO: integer */
};

struct zpn_system_inventory_stats {                    /* _ARGO: object_definition */
    uint32_t     sbin_setcap_stat_errno;               /* _ARGO: integer */
    uint32_t     available_cpus;                       /* _ARGO: integer */
    uint32_t     configured_cpus;                      /* _ARGO: integer */
    uint32_t     fohh_threads;                         /* _ARGO: integer */
    uint32_t     is_container_env;                     /* _ARGO: integer */
    uint32_t     is_zscaler_os;                        /* _ARGO: integer */
};

/*
 * Stats related to assistant ncache.
 */
struct zpn_assistant_ncache_stats {                    /* _ARGO: object_definition */
    int64_t    total_inet_entries;                     /* _ARGO: integer */
};

/*
 * To pass the excrypted and decrypted keys
 */
struct zpn_file_fetch_key {                       /* _ARGO: object_definition */
    int fetch_retry_count;                        /* _ARGO: integer */
    const char *enc_or_dec_pvt_key;               /* _ARGO: string */
    char *filename;                               /* _ARGO: string */
    int is_update;                                /* _ARGO: integer */
    int key_fetch_fail_count;                     /* _ARGO: integer */
    char *version;                                /* _ARGO: string */
    const char *enc_or_dec_pvt_key2;              /* _ARGO: string */
};


struct zpn_decrypt_key_request {    /* _ARGO: object_definition */
    char *requestor;                /* _ARGO: string */
    const char *encrypted_key;      /* _ARGO: string */
};

struct zpn_decrypt_key_response {   /* _ARGO: object_definition */
    int status;                     /* _ARGO: integer */
    char *requestor;                /* _ARGO: string */
    const char *decrypted_key;      /* _ARGO: string */
};

struct zpn_auth_state_log {                     /* _ARGO: object_definition */
    int64_t log_date;                           /* _ARGO: integer, adj_time,         synonym: LogTimestamp */
    char *version;                              /* _ARGO: string,                    synonym: Version */

    int64_t g_cst;                              /* _ARGO: integer, stringify,        synonym: Customer */
    int64_t g_brk;                              /* _ARGO: integer, stringify,        synonym: ZEN */
    int64_t g_exp;                              /* _ARGO: integer, stringify,        synonym: Exporter */
    int64_t g_pbrk;                             /* _ARGO: integer, stringify, nozero        synonym: PrivateBroker */

    const char *cname;                          /* _ARGO: string,                    synonym: CertificateCN */
    const char *c_uid;                          /* _ARGO: string,                    synonym: Username */
    const char *tunnel_id;                      /* _ARGO: string,                    synonym: SessionID */
    struct argo_inet priv_ip;                   /* _ARGO: inet,                      synonym: PrivateIP */
    struct argo_inet pub_ip;                    /* _ARGO: inet,                      synonym: PublicIP */
    /* The port associated with the public IP */
    int pub_port;                               /* _ARGO: integer */

    int64_t idp_gid;                            /* _ARGO: integer,                   synonym: Idp, synonym: IDP */

    char *attributes;                           /* _ARGO: string,                    synonym: SAMLAttributes */
    char **scim_attributes;                     /* _ARGO: string,                    synonym: SCIMAttributes */
    int  scim_attributes_count;                 /* _ARGO: quiet, integer, count: scim_attributes */
    char **scim_user_group;                      /* _ARGO: string,                    synonym: SCIMUserGroup */
    int  scim_user_group_count;                 /* _ARGO: quiet, integer, count: scim_user_group */
    char **postures_hit;                        /* _ARGO: string,                    synonym: PosturesHit */
    int postures_hit_count;                     /* _ARGO: quiet, integer, count: postures_hit */
    char **postures_miss;                       /* _ARGO: string,                    synonym: PosturesMiss */
    int postures_miss_count;                    /* _ARGO: quiet, integer, count: postures_miss */
    char **trusted_networks;                    /* _ARGO: string,                    synonym: TrustedNetworks */
    int trusted_networks_count;                 /* _ARGO: quiet, integer, count: trusted_networks */
    char **trusted_networks_names;              /* _ARGO: string,                    synonym: TrustedNetworksNames */
    int trusted_networks_names_count;           /* _ARGO: quiet, integer, count: trusted_networks_names */
    char **sv_postures_hit;                     /* _ARGO: string,                    synonym: SvPosturesHit */
    int sv_postures_hit_count;                  /* _ARGO: quiet, integer, count: sv_postures_hit */
    char **sv_postures_miss;                    /* _ARGO: string,                    synonym: SvPosturesMiss */
    int sv_postures_miss_count;                 /* _ARGO: quiet, integer, count: sv_postures_miss */
    int64_t sv_posture_update_us;                  /* _ARGO: integer, adj_time, nozero */
    int64_t sv_posture_profile_version;            /* _ARGO: integer, nozero */
};

struct zpn_mtunnel_data_mconn_stats {          /* _ARGO: object_definition */
    int32_t tag_id;                    /* _ARGO: integer */

    /*
     * histogram: number of packets on ingress
     * with inter packet delays falling into buckets of: 20ms, 50ms, 100ms, 500ms, 500ms plus
     * measured inter packet delays are from assistant
     */
    int as_rx_diff_rx_data_hist[FOHH_MCONN_TRACK_RX_DIFF_RX_DATA_HISTOGRAM_MAX];    /* _ARGO: integer, nozero */
    /*
     * histogram : number of packets from ingress to egress
     * with delays falling into buckets of: 20ms, 50ms, 100ms, 500ms, 500ms plus
     * measured delays are for time taken from client towards assistant
     */
    int as_tx_peer_rx_data_hist[FOHH_MCONN_TRACK_TX_PEER_RX_DATA_HISTOGRAM_MAX];    /* _ARGO: integer, nozero */

    /* max time in us taken from client towards assistant, or from ingress (rx) to egress (tx)  */
    int as_tx_peer_rx_data_max_us;                               /* _ARGO: integer, nozero */
    /* epoch of max time in us taken from client towards assistant, or from ingress (rx) to egress (tx)  */
    int64_t as_tx_peer_rx_data_max_epoch_us;                     /* _ARGO: integer, nozero */


    /*
     * histogram: number of packets on ingress
     * with inter packet delays falling into buckets of: 20ms, 50ms, 100ms, 500ms, 500ms plus
     * measured inter packet delays are from client
     */
    int ab_rx_diff_rx_data_hist[FOHH_MCONN_TRACK_RX_DIFF_RX_DATA_HISTOGRAM_MAX];    /* _ARGO: integer, nozero */
    /*
     * histogram : number of packets from ingress to egress
     * with delays falling into buckets of: 20ms, 50ms, 100ms, 500ms, 500ms plus
     * measured delays are for time taken from assistant towards client
     */
    int ab_tx_peer_rx_data_hist[FOHH_MCONN_TRACK_TX_PEER_RX_DATA_HISTOGRAM_MAX];    /* _ARGO: integer, nozero */
    /* max time in us taken from assistant towards client, or from ingress (rx) to egress (tx) */
    int ab_tx_peer_rx_data_max_us;                              /* _ARGO: integer, nozero */
    /* epoch of max time in us taken from assistant towards client, or from ingress (rx) to egress (tx)  */
    int64_t ab_tx_peer_rx_data_max_epoch_us;                    /* _ARGO: integer, nozero */

    /* number of packets received from assistant towards client during c_tx_peer_rx_data_max_us */
    int ab_tx_peer_rx_data_max_cnt;                             /* _ARGO: integer, nozero */
    /* total number of attempts to schedule packets at egress mconn towards client during tx_peer_rx_data_max_us */
    int ab_tx_peer_rx_data_max_unblock_cnt;                     /* _ARGO: integer, nozero */
    /* max time in us taken to schedule packet waiting on egress to be sent towards client */
    int ab_tx_data_unblock_max_us;                              /* _ARGO: integer, nozero */
    /* max number of attempts to schedule packets during a_tx_data_unblock_max_us */
    int ab_tx_data_unblock_max_cnt;                             /* _ARGO: integer, nozero */
    /* total time in us taken to schedule packets waiting on egress to be sent towards client */
    int64_t ab_tx_data_unblock_tot_us;                          /* _ARGO: integer, nozero */

 };

/*
 * EAS (zvpn ) messages
 */
struct zpn_zia_instance_info {         /* _ARGO: object_definition */
    int64_t msg_id;                    /* _ARGO: integer */
    struct argo_inet *zia_inst_ip;     /* _ARGO: inet */
    int64_t zia_inst_id;               /* _ARGO: integer */
    uint64_t zia_inst_type;            /* _ARGO: integer */
    const char **zia_inst_capabilities;  /* _ARGO: string */
    int zia_inst_capabilities_count;        /* _ARGO: quiet, integer, count:zia_inst_capabilities */
    double zia_inst_latitude;               /* _ARGO: double */
    double zia_inst_longitude;              /* _ARGO: double */
    const char *zia_cloud_name;        /* _ARGO: string */
    const char *zia_inst_country_code; /* _ARGO: string */
};

struct zpn_zia_instance_info_ack {  /* _ARGO: object_definition */
    int64_t msg_id;                 /* _ARGO: integer */
    const char *error;              /* _ARGO: string */
    int32_t err_code;               /* _ARGO: integer */
    const char *zpa_tunnel_id;      /* _ARGO: string */
    const char *zpa_current_broker; /* _ARGO: string */
};

struct zpn_zia_location_update {    /* _ARGO: object_definition */
    int64_t msg_id;                 /* _ARGO: integer */
    const char *zia_cloud_name;     /* _ARGO: string */
    int64_t zia_org_id;             /* _ARGO: integer */
    int64_t zia_partner_id;         /* _ARGO: integer */
    int64_t zia_location_id;        /* _ARGO: integer */
    int64_t zia_instance_id;        /* _ARGO: integer */
    int64_t zia_tunnel_id;          /* _ARGO: integer */
    int32_t tunnel_status;          /* _ARGO: integer */
    int32_t ttl_secs;               /* _ARGO: integer */
    int32_t tunnel_score;           /* _ARGO: integer */
    int32_t instance_score;         /* _ARGO: integer */
};

struct zpn_zia_location_update_ack {  /* _ARGO: object_definition */
    int64_t msg_id;                   /* _ARGO: integer */
    const char *error;                /* _ARGO: string */
    int32_t err_code;                 /* _ARGO: integer */
};

struct zpn_zia_health {    /* _ARGO: object_definition */
    // ZPA customer gid
    int64_t g_cst;               /* _ARGO: integer */
    const char *zia_cloud_name; /* _ARGO: string */
    int64_t zia_org_id;         /* _ARGO: integer */
    int64_t zia_partner_id;     /* _ARGO: integer */
    int64_t zia_location_id;    /* _ARGO: integer */
    int64_t zia_instance_id;    /* _ARGO: integer */
    int64_t zia_tunnel_id;      /* _ARGO: integer */
    int32_t tunnel_status;      /* _ARGO: integer */
    int32_t tunnel_score;       /* _ARGO: integer */
    int32_t instance_score;     /* _ARGO: integer */
    // error codes if broker rejects the location update
    const char *error;          /* _ARGO: string */
    int32_t err_code;           /* _ARGO: integer */
};

/*
 * send one time on data connection to user broker
 * the connection is dedicated to zia_cloud_name:zia_org_id
 */
struct zpn_zia_instance_data {         /* _ARGO: object_definition */
    int64_t msg_id;                    /* _ARGO: integer */
    const char **capabilities;         /* _ARGO: string */
    int capabilities_count;            /* _ARGO: quiet, integer, count:capabilities */
    const char *zia_cloud_name;        /* _ARGO: string */
    int64_t zia_org_id;                /* _ARGO: integer */
    int64_t zia_inst_id;               /* _ARGO: integer */
    uint64_t zia_inst_type;            /* _ARGO: integer */
    //
    struct argo_inet *zia_inst_ip;     /* _ARGO: inet */
};
struct zpn_zia_instance_data_ack {     /* _ARGO: object_definition */
    int64_t msg_id;                    /* _ARGO: integer */
    const char **capabilities;         /* _ARGO: string */
    int capabilities_count;            /* _ARGO: quiet, integer, count:capabilities */
    const char *error;                 /* _ARGO: string */
    int32_t err_code;                  /* _ARGO: integer */
};

/*
 * Registration message for ZVPN tunnel based on customer + partner + location
*/

struct zpn_broker_dispatcher_zia_location_registration {  /* _ARGO: object_definition */
    int64_t msg_id;                     /* _ARGO: integer */
    int64_t g_cst;                      /* _ARGO: integer */
    char *zia_cloud_name;               /* _ARGO: string */
    int64_t zia_org_id;                 /* _ARGO: integer */
    int64_t zia_partner_id;             /* _ARGO: integer */
    int64_t zia_location_id;            /* _ARGO: integer */
    int64_t zia_instance_id;            /* _ARGO: integer */
    int64_t zia_tunnel_id;              /* _ARGO: integer */
    int32_t tunnel_status;              /* _ARGO: integer */
    int64_t expires_epoch_s;            /* _ARGO: integer */
    int32_t tunnel_score;               /* _ARGO: integer */
    int32_t instance_score;             /* _ARGO: integer */
    double zia_inst_latitude;           /* _ARGO: double */
    double zia_inst_longitude;          /* _ARGO: double */
    char *zia_inst_country_code;        /* _ARGO: string */
};

/* ZMS flowlog */
struct zms_flowlog {							/* _ARGO: object_definition */
    int64_t             log_date;           /* _ARGO: integer, adj_time, synonym: LogTimestamp */
    char                *g_cname;           /* _ARGO: string, synonym: Customer */
	char 				*g_agid;            /* _ARGO: string, synonym: AgentID */
	char 				*g_ag_name;         /* _ARGO: string, synonym: AgentName */
	char 				*g_rsrcid;			/* _ARGO: string, synonym: ResourceID */
	char 				*g_rsrc_name;		/* _ARGO: string, synonym: ResourceName */
	char				*g_azid;			/* _ARGO: string, synonym: AppZoneID */
	char				*g_az_name;			/* _ARGO: string, synonym: AppZoneName */
	int64_t				conn_startime;		/* _ARGO: integer, synonym: ConnectionStartTime */
	struct argo_inet	src_ip;		        /* _ARGO: inet, synonym: SourceIP */
	struct argo_inet	dst_ip;		        /* _ARGO: inet, synonym: DestinationIP */
	int32_t 			dst_port;			/* _ARGO: integer, synonym: DestinationPort */
	int32_t				proto;				/* _ARGO: integer, synonym: Protocol */
	int32_t				*src_ports;			/* _ARGO: integer, synonym: SourcePorts */
	int32_t				src_port_cnt;		/* _ARGO: integer, count: src_ports */
    char                **app_name;         /* _ARGO: string, synonym: AppName*/
	int32_t				app_name_cnt;		/* _ARGO: integer, count: app_name */
    char                **exe_path;         /* _ARGO: string, synonym: AppExecutablePath*/
	int32_t				exe_path_cnt;		/* _ARGO: integer, count: exe_path */
    /* Flow Direction INBOUND(1), OUTBOUND(2) */
	char                *direction;			/* _ARGO: string, synonym: Direction */
	/** Enforcement: Policy nonce (if known)
	 */
	char				*enf_nonce;         /* _ARGO: string */
	char				*plcy_id;           /* _ARGO: string, synonym: PolicyID */
	char				*plcy_name;         /* _ARGO: string, synonym: PolicyName */
    /** Decision reason (0 POLICY_DISABLED, 1 RULE, 2 NO_POLICY_EXISTS) */
	char                *enf_reason;        /* _ARGO: string, synonym: EnforcementReason */
    /** Enforcement action (0 ALLOW, 1 BLOCK, 2 SIMBLOCK) */
	char                *enf_action;		/* _ARGO: string, synonym: EnforcementAction */
    /* Disposition (0 UNKNOWN, 1 CONNECTED, 2 REJECTED, 3 DROPPED) */
	char                *enf_disposition;	/* _ARGO: string, synonym: EnforcementDisposition */
};

/* sitec firedrill deactivate rpc to pse and appc */
struct zpn_sitec_firedrill_exit {        /* _ARGO: object_definition */
    int     firedrill_exit;              /* _ARGO: integer */
};

struct zpn_firedrill_stats {               /* _ARGO: object_definition */
    int firedrill_triggered_count;         /* _ARGO: integer */
    int firedrill_completed_count;         /* _ARGO: integer */
    int firedrill_transit_count;           /* _ARGO: integer */
    int firedrill_cmdline_disable_count;   /* _ARGO: integer */
};

struct zpn_broker_mission_critical {               /* _ARGO: object_definition */
    int64_t site_gid;                              /* _ARGO: integer */
};

struct zpn_broker_mission_critical_resp {          /* _ARGO: object_definition */
    int firedrill_status;                          /* _ARGO: integer */
    int firedrill_interval;                        /* _ARGO: integer */
};

int zpn_rpc_init(void);

/*
 * All return _NO_ERROR on success.
 *
 * _NO_ERROR is also returned on incarnation mismatch.
 *
 * _WOULD_BLOCK is returned if the transmission could not occur
 * because (duh) the request would have blocked.
 */
int tlv_argo_serialize(struct zpn_tlv *tlv,
                       struct argo_structure_description *description,
                       void *data,
                       int64_t conn_incarnation,
                       enum fohh_queue_element_type elem_type);

int tlv_argo_serialize_object(struct zpn_tlv *tlv,
                              struct argo_object *object,
                              int64_t conn_incarnation,
                              enum fohh_queue_element_type elem_type);

int zpn_send_zpn_version(struct zpn_tlv *tlv,
                         int64_t conn_incarnation,
                         int32_t version_major,
                         int32_t version_minor);

int zpn_send_zpn_version_ack(struct zpn_tlv *tlv,
                             int64_t conn_incarnation,
                             const char *error);

int zpn_send_zpn_client_state(struct zpn_tlv *tlv,
                              int64_t conn_incarnation,
                              const char *client_state);

int zpn_send_zpn_client_authenticate(struct zpn_tlv *tlv,
                                     int64_t conn_incarnation,
                                     int64_t id,
                                     const char *saml_assertion_xml,
                                     const char *hw_serial_id,
                                     const char *cpu_serial_id,
                                     const char *storage_serial_id,
                                     const char *login_name,
                                     const char *platform,
                                     char *primary_login_domain,
                                     const char *client_type,
                                     const char **capabilities,
                                     int capabilities_count,
                                     enum zpn_client_conn_mode role);

int zpn_send_zpn_authenticate_obj(struct fohh_connection *f_conn,
                                  int64_t f_conn_incarnation,
                                  struct argo_object *obj,
                                  struct argo_structure_description *obj_description);


int zpn_send_zpn_client_upgrade_request_response(struct zpn_tlv *tlv,
                                                int request_id,
                                                int res,
                                                const char *resp_str);

int zpn_send_zpn_client_authenticate_ack(struct zpn_tlv *tlv,
                                         int64_t conn_incarnation,
                                         int64_t id,
                                         int64_t saml_not_before,
                                         const char *tunnel_id,
                                         const char *current_broker,
                                         int64_t broker_type,
                                         const char *error,
                                         struct argo_inet *pub_ip);
int zpn_send_zpn_machine_tunnel_client_authenticate(struct zpn_tlv *tlv,
                                                    int64_t conn_incarnation,
                                                    int64_t id,
                                                    const char *hw_serial_id,
                                                    const char *cpu_serial_id,
                                                    const char *storage_serial_id,
                                                    struct argo_inet *priv_ip,
                                                    const char *hostname,
                                                    const char *platform);
int zpn_send_zpn_machine_tunnel_client_authenticate_ack(struct zpn_tlv *tlv,
                                                        int64_t conn_incarnation,
                                                        int64_t id,
                                                        const char *tunnel_id,
                                                        const char *current_broker,
                                                        int64_t broker_type,
                                                        const char *error);
int zpn_send_zpn_trusted_client_authenticate(struct fohh_connection *f_conn,
                                             int64_t f_conn_incarnation,
                                             int64_t id,
                                             int64_t customer_gid);
int zpn_send_zpn_trusted_client_authenticate_ack(struct zpn_tlv *tlv,
                                                 int64_t conn_incarnation,
                                                 int64_t id,
                                                 const char *tunnel_id,
                                                 const char *current_broker,
                                                 const char *error);
int zpn_send_zpn_exporter_client_authenticate(struct fohh_connection *f_conn,
                                              int64_t f_conn_incarnation,
                                              int64_t id,
                                              int64_t customer_gid,
                                              const char *assertion_base64,
                                              struct argo_inet *public_ip,
                                              struct argo_inet *private_ip,
                                              int64_t pra_scope_gid,
                                              int is_pra_third_party_login);
int zpn_send_zpn_exporter_client_authenticate_ack(struct zpn_tlv *tlv,
                                                 int64_t conn_incarnation,
                                                 int64_t id,
                                                 const char *tunnel_id,
                                                 const char *current_broker,
                                                 const char *error);
int zpn_send_zpn_bi_client_authenticate(struct zpn_tlv *tlv,
                                        int64_t conn_incarnation,
                                        int64_t id,
                                        const char *assertion_base64,
                                        struct argo_inet *public_ip,
                                        struct argo_inet *private_ip);
int zpn_send_zpn_bi_client_authenticate_ack(struct zpn_tlv *tlv,
                                            int64_t conn_incarnation,
                                            int64_t id,
                                            const char *tunnel_id,
                                            const char *current_broker,
                                            const char *error);

int zpn_send_zpn_pbroker_client_authenticate(struct fohh_connection *f_conn,
                                              int64_t f_conn_incarnation,
                                              int64_t id,
                                              const char *assertion_base64,
                                              struct argo_inet *private_ip);
int zpn_send_zpn_pbroker_client_authenticate_ack(struct fohh_connection *f_conn,
                                                  int64_t f_conn_incarnation,
                                                  int64_t id,
                                                  const char *tunnel_id,
                                                  const char *current_broker,
                                                  const char *error);
int zpn_send_zpn_pbroker_client_switch_to_cloud(struct fohh_connection *f_conn,
                                                int64_t f_conn_incarnation,
                                                int8_t is_cloud_reachable,
                                                int64_t num_mtunnels);
int zpn_send_zpn_ec_client_authenticate(struct zpn_tlv *tlv,
                                        int64_t conn_incarnation,
                                        int64_t id,
                                        int64_t orgid,
                                        int64_t o_location_id,
                                        const char *cloud_name,
                                        const char **capabilities,
                                        int capabilities_count,
                                        struct argo_inet *private_ip,
                                        const char *platform);
int zpn_send_zpn_vdi_client_authenticate(struct zpn_tlv *tlv,
                                            int64_t conn_incarnation,
                                            int64_t id,
                                            int64_t orgid,
                                            const char *cloud_name,
                                            const char **capabilities,
                                            int capabilities_count,
                                            struct argo_inet *private_ip,
                                            const char *platform,
                                            const char *user_name,
                                            int64_t last_auth_time);
int zpn_send_zpn_vdi_client_authenticate_ack(struct zpn_tlv *tlv,
                                                int64_t conn_incarnation,
                                                int64_t id,
                                                const char *tunnel_id,
                                                const char *current_broker,
                                                int64_t broker_type,
                                                const char *error);
int zpn_send_zpn_ec_client_authenticate_ack(struct zpn_tlv *tlv,
                                            int64_t conn_incarnation,
                                            int64_t id,
                                            const char *tunnel_id,
                                            const char *current_broker,
                                            int64_t broker_type,
                                            const char *error);

int zpn_send_zpn_ia_client_authenticate(struct zpn_tlv *tlv,
                                        int64_t conn_incarnation,
                                        int64_t id,
                                        const char *cloud_name,
                                        int64_t orgid,
                                        int64_t customer_gid,
                                        struct argo_inet *private_ip,
                                        const char **capabilities,
                                        int capabilities_count);

int zpn_send_zpn_ia_client_authenticate_ack(struct zpn_tlv *tlv,
                                            int64_t conn_incarnation,
                                            int64_t id,
                                            int64_t customer_gid,
                                            const char *tunnel_id,
                                            const char *current_broker,
                                            int64_t broker_type,
                                            const char *error);

int zpn_send_zpn_client_app(struct zpn_tlv *tlv,
                            int64_t conn_incarnation,
                            struct argo_object *object,
                            char *debug_str);
int zpn_send_zpn_client_app_complete(struct zpn_tlv *tlv,
                                     int64_t conn_incarnation,
                                     const char *error);
int zpn_send_zpn_client_search_domain(struct zpn_tlv *tlv,
                                      int64_t conn_incarnation,
                                      const char *domain,
                                      int capture,
                                      int deleted);
int zpn_send_zpn_client_search_domain_complete(struct zpn_tlv *tlv,
                                               int64_t conn_incarnation,
                                               char **domains,
                                               int *capture,
                                               int domains_count,
                                               const char *error);
int zpn_send_zpn_client_search_domain_all(struct zpn_tlv *tlv,
                                          int64_t conn_incarnation,
                                          char **domains,
                                          int *capture,
                                          int domains_count);

int zpn_send_zpn_tlv_aggregated_domains(struct zpn_tlv *tlv,
                                        char **domain_list,
                                        int domain_cnt);

int zpn_send_zpn_tlv_config_updated(struct zpn_tlv* tlv,
                                    const char *error);

int zpn_send_zpn_posture_profile_ack(struct zpn_tlv *tlv,
                                     int64_t f_conn_incarnation,
                                     const char *id_str,
                                     const char *error);
int zpn_send_zpn_mtunnel_request(struct zpn_tlv *tlv,
                                 int64_t conn_incarnation,
                                 int32_t tag_id,
                                 char *app_name,
                                 uint16_t ip_protocol,
                                 int32_t server_port,
                                 uint32_t double_encrypt,
                                 enum zpn_probe_type zpn_probe_type,
                                 int64_t publish_gid,
                                 char *app_type,
                                 int64_t o_location_id,
                                 int64_t app_gid,
                                 const struct zpn_managed_chrome_payload *gposture,
                                 const struct zpn_managed_browser_profiles *gprofiles);

int zpn_send_zpn_mtunnel_request_zia(struct zpn_tlv *tlv,
                                    int64_t conn_incarnation,
                                    int32_t tag_id,
                                    char *app_name,
                                    uint16_t ip_protocol,
                                    int32_t server_port,
                                    int64_t o_user_id,
                                    int64_t o_location_id,
                                    int64_t o_plocation_id,
                                    struct argo_inet *o_sip,
                                    struct argo_inet *o_dip,
                                    int32_t o_sport,
                                    int32_t o_dport,
                                    char *o_identity_name);

int zpn_send_zpn_mtunnel_request_int(struct fohh_connection *f_conn,
                                     int64_t f_conn_incarnation,
                                     int32_t tag_id,
                                     char *app_name,
                                     uint16_t ip_protocol,
                                     int32_t server_port,
                                     uint32_t double_encrypt,
                                     uint32_t zpn_probe_type,
                                     int64_t publish_gid);

int zpn_send_zpn_mtunnel_request_ack(struct zpn_tlv *tlv,
                                     int64_t conn_incarnation,
                                     int64_t reauth_timeout_s,
                                     int32_t tag_id,
                                     int a_port,
                                     int s_port,
                                     struct argo_inet *a_ip,
                                     struct argo_inet *s_ip,
                                     const char *mtunnel_id,
                                     const char *error,
                                     const char *reason,
                                     const char *stepup_al_id);

int zpn_send_zpn_mtunnel_bind(struct zpn_tlv *tlv,
                              int64_t conn_incarnation,
                              const char *mtunnel_id,
                              int32_t server_us,
                              int64_t fwd_broker_id,
                              int64_t fwd_broker_us,
                              int64_t g_aps,
                              struct argo_inet brk_req_s_inet,
                              struct argo_inet s_inet,
                              uint16_t s_port,
                              struct argo_inet a_inet,
                              uint16_t a_port,
                              int64_t brk_req_dsp_tx_us,
                              int64_t ast_tx_us,
                              int64_t brk_req_ast_rx_us,
                              int64_t g_app,
                              int64_t g_app_grp,
                              int64_t g_ast_grp,
                              int64_t g_srv_grp,
                              int64_t g_dsp,
                              uint64_t path_decision,
                              int8_t   dsp_bypassed,
                              uint8_t  insp_status,
                              uint64_t ssl_err);
int zpn_send_zpn_mtunnel_bind_ack_struct(struct zpn_tlv *tlv,
                                         int64_t f_conn_incarnation,
                                         struct zpn_mtunnel_bind_ack *ack);
int zpn_send_zpn_mtunnel_tag_pause(struct fohh_connection *f_conn,
                                   int64_t f_conn_incarnation,
                                   int32_t tag_id);
int zpn_send_zpn_mtunnel_tag_resume(struct fohh_connection *f_conn,
                                    int64_t f_conn_incarnation,
                                    int32_t tag_id);
int zpn_send_zpn_mtunnel_end(struct zpn_tlv *tlv,
                             int64_t conn_incarnation,
                             const char *mtunnel_id,
                             int32_t tag_id,
                             const char *error,
                             int32_t drop_data);
int zpn_send_zpn_client_connection_upgrade(struct zpn_tlv *tlv,
                                           int64_t conn_incarnation,
                                           int id,
                                           int curr_conn_mode,
                                           int new_conn_mode);

int zpn_send_zdx_probe_legs_info_tlv(struct zpn_tlv *tlv,
                                     int64_t conn_incarnation,
                                     struct zpn_zdx_probe_legs_info *info);

int zpn_send_zpn_broker_request_struct(struct fohh_connection *f_conn,
                                       int64_t f_conn_incarnation,
                                       struct zpn_broker_request *req);
int zpn_send_zpn_broker_request_struct_on_zpn_tlv(struct zpn_tlv *tlv,
                                                  int64_t tlv_incarnation,
                                                  struct zpn_broker_request *req);
int zpn_send_zpn_broker_request_ack_struct(struct fohh_connection *f_conn,
                                           int64_t f_conn_incarnation,
                                           struct zpn_broker_request_ack *ack);
int zpn_send_zpn_broker_request_ack_struct_on_zpn_tlv(struct zpn_tlv* tlv,
                                                      int64_t conn_incarnation,
                                                      struct zpn_broker_request_ack *ack);
int zpn_send_zpn_health_report_struct(struct fohh_connection *f_conn,
                                      int64_t f_conn_incarnation,
                                      struct zpn_health_report *rep);

int zpn_send_zpn_ast_auth_report_struct(struct fohh_connection *f_conn,
                                       int64_t f_conn_incarnation,
                                       struct zpn_ast_auth_report *rep);

int zpn_send_zpn_app_route_registration_struct(struct fohh_connection *f_conn,
                                               int64_t f_conn_incarnation,
                                               struct zpn_app_route_registration *rep);

int zpn_send_zpn_app_route_discovery_struct(struct fohh_connection *f_conn,
                                            int64_t f_conn_incarnation,
                                            struct zpn_app_route_discovery *rep);

int zpn_send_zpn_app_route_info_struct(struct fohh_connection *f_conn,
                                       int64_t f_conn_incarnation,
                                       struct zpn_app_route_info *rep);

/*
 * f_conn_info is the source of the data. f_conn is the connection
 * upon which to send it
 */
int zpn_send_zpn_tcp_info_report(struct fohh_connection *f_conn,
                                 int64_t f_conn_incarnation,
                                 struct fohh_connection *f_conn_info,
                                 const char *platform_detail,
                                 const char *runtime_os);

int zpn_send_zpn_zrdt_info_report(struct zpn_tlv *tlv,
                                  int64_t conn_incarnation,
                                  struct zrdt_conn *z_conn);

int zpn_send_zpn_asst_environment_report(struct fohh_connection *f_conn,
                                         int64_t f_conn_incarnation,
                                         struct zpn_asst_environment_report *data);

int zpn_send_zpn_pbroker_environment_report(struct fohh_connection *f_conn,
                                            int64_t f_conn_incarnation,
                                            const char *sarge_version);

int zpn_send_zpn_sitec_environment_report(struct fohh_connection *f_conn,
                                          int64_t f_conn_incarnation,
                                          const char *sarge_version);


int zpn_send_zpn_asst_active_control_connection(struct fohh_connection *f_conn,
                                                int64_t f_conn_incarnation);

int zpn_send_zpn_dns_client_check(struct zpn_tlv *tlv,
                                  int64_t conn_incarnation,
                                  struct zpn_dns_client_check *data);

int zpn_send_zpn_dns_client_check_f_conn(struct fohh_connection *f_conn,
                                         int64_t f_conn_incarnation,
                                         void *data,
                                         int is_app_check);

int zpn_send_zpn_app_client_check(struct zpn_tlv *tlv,
                               int64_t conn_incarnation,
                               struct zpn_app_client_check *data);

int zpn_send_zpn_dns_dispatch_check(struct fohh_connection *f_conn,
                                    int64_t f_conn_incarnation,
                                    int64_t id,
                                    char *tunnel_id,
                                    int64_t g_brk,
                                    int64_t g_app,
                                    char *name,
                                    const char **target_name,
                                    int32_t *target_port,
                                    int32_t *target_priority,
                                    int32_t *target_weight,
                                    int target_count,
                                    const char **cnames,
                                    int cnames_count,
                                    char *type,
                                    char *error);

int zpn_send_zpn_dns_dispatch_check_struct(struct fohh_connection *f_conn,
                                           int64_t f_conn_incarnation,
                                           struct zpn_dns_dispatch_check *msg);

int zpn_send_dispatch_fqdn_c2c_check_struct(struct fohh_connection *f_conn,
                                           int64_t f_conn_incarnation,
                                           struct zpn_broker_dispatcher_c2c_app_check *msg);

int zpn_send_zpn_broker_dispatcher_c2c_app_check_struct(struct fohh_connection *f_conn,
                                                        int64_t f_conn_incarnation,
                                                        struct zpn_broker_dispatcher_c2c_app_check *zbd_app_check);

int zpn_send_zpn_dns_assistant_check(struct fohh_connection *f_conn,
                                     int64_t f_conn_incarnation,
                                     int64_t g_ast,
                                     int64_t g_app,
                                     int64_t g_dsp,
                                     char *name,
                                     char *type,
                                     char *error,
                                     int64_t dsp_tx_us,
                                     int64_t cbrk_to_ast_tx_us);

int zpn_send_zpn_dns_assistant_check_struct(struct fohh_connection *f_conn,
                                            int64_t f_conn_incarnation,
                                            struct zpn_dns_assistant_check *msg);

int zpn_send_eas_dns_assistant_check(struct zpn_tlv *tlv,
                                     int64_t tlv_incarnation,
                                     struct zpn_dns_assistant_check *dns_req);

int zpn_send_zpn_assistant_log_control(struct fohh_connection *f_conn,
                                       int64_t f_conn_incarnation,
                                       int64_t g_ast,
                                       int type,
                                       int upload,
                                       uint64_t flag);
int zpn_send_zpn_assistant_stats_control(struct fohh_connection *f_conn,
                                       int64_t f_conn_incarnation,
                                       int64_t g_ast,
                                       int upload);

int zpn_send_zpn_assistant_enc_pvt_key(struct fohh_connection *f_conn,
                                       int64_t f_conn_incarnation,
                                       struct zpn_assistant_pvt_key_control *data);

int zpn_send_zpn_assistant_cert_req(struct fohh_connection *f_conn,
                                    int64_t f_conn_incarnation,
                                    struct zpn_assistant_gen_cert_control  *data);

int zpn_send_zpn_assistant_restart(struct fohh_connection *f_conn,
                                   int64_t f_conn_incarnation,
                                   int64_t g_ast);

int zpn_send_zpn_assistant_status_report(struct fohh_connection *f_conn,
                                         int64_t f_conn_incarnation,
                                         struct zpn_assistant_status_report *data);

int zpn_send_zpn_asst_state(struct zpn_tlv *tlv,
                            int64_t tlv_incarnation,
                            struct zpn_asst_state *data);
int zpn_send_zpn_asst_state_on_fohh(struct fohh_connection *f_conn,
                                       int64_t fohh_incarnation,
                                       struct zpn_asst_state *data);

int zpn_send_zpn_asst_restart_reason_on_fohh(struct fohh_connection *f_conn,
                                             int64_t fohh_incarnation,
                                             int64_t assistant_id,
                                             const char *restart_reason);

int zpn_send_zpn_fohh_window_update(struct fohh_connection *f_conn,
                                    int64_t f_conn_incarnation,
                                    int32_t tag_id,
                                    int64_t tx_limit,
                                    int64_t rx_data);

int zpn_send_zpn_fohh_window_batch_update(struct fohh_connection *f_conn,
                                          struct zpn_fohh_tlv_window_update_batch *zpn_fohh_tlv_window_update_batch);

int zpn_send_zpn_broker_redirect_struct(struct zpn_tlv *tlv,
                                        struct zpn_broker_redirect *redirect);

int zpn_send_zpn_dispatcher_status(struct fohh_connection *f_conn,
                                   int64_t f_conn_incarnation);

int zpn_send_zpn_dispatcher_status_struct(struct fohh_connection *f_conn,
                                          int64_t f_conn_incarnation,
                                          struct zpn_dispatcher_status *status);

int zpn_send_zpn_broker_info(struct fohh_connection *f_conn,
                             int64_t f_conn_incarnation,
                             int64_t gid,
                             const char *name,
                             uint8_t channel_id);

int zpn_send_zpn_clientless_app_query(struct fohh_connection *f_conn,
                                      int64_t f_conn_incarnation,
                                      struct zpn_clientless_app_query *query);

int zpn_send_zpn_clientless_app_query_ack(struct fohh_connection *f_conn,
                                          int64_t f_conn_incarnation,
                                          struct zpn_clientless_app_query_ack *query_ack);

int zpn_send_zpn_posture_profile_start(struct fohh_connection *f_conn,
                                       int64_t f_conn_incarnation,
                                       struct zpn_posture_profile_start *data);

int zpn_send_zpn_posture_profile(struct fohh_connection *f_conn,
                                 int64_t f_conn_incarnation,
                                 struct zpn_posture_profile *data);

int zpn_send_zpn_posture_profile_start_tlv(struct zpn_tlv *tlv,
                                           int64_t f_conn_incarnation,
                                           struct zpn_posture_profile_start *data);

int zpn_send_zpn_posture_profile_tlv(struct zpn_tlv *tlv,
                                     int64_t f_conn_incarnation,
                                     struct zpn_posture_profile *data);

int zpn_send_zpn_trusted_networks(struct fohh_connection *f_conn,
                                  int64_t f_conn_incarnation,
                                  struct zpn_trusted_networks *data);

int zpn_send_zpn_trusted_networks_ack(struct zpn_tlv *tlv,
                                      int64_t conn_incarnation,
                                      int64_t id);

int zpn_send_pbroker_log_control(struct fohh_connection *f_conn,
                                 int64_t f_conn_incarnation,
                                 int64_t g_pbroker,
                                 int type,
                                 int upload,
                                 uint64_t flag);

int zpn_send_sitec_log_control(struct fohh_connection *f_conn,
                                 int64_t f_conn_incarnation,
                                 int64_t g_pbroker,
                                 int type,
                                 int upload,
                                 uint64_t flag);

int zpn_send_pbroker_stats_control(struct fohh_connection *f_conn,
                                   int64_t f_conn_incarnation,
                                   int64_t g_pbroker,
                                   int upload);


int zpn_send_zpn_client_broker_app_registration(struct zpn_tlv *tlv,
                                                int64_t conn_incarnation,
                                                const char *client_fqdn);


int zpn_send_zpn_client_app_registration_notification(struct zpn_tlv *tlv,
                                                      int64_t conn_incarnation,
                                                      const char *client_fqdn,
                                                      struct argo_inet *client_ip,
                                                      const char *location_hint,
                                                      const char *error_code);

int zpn_send_zpn_broker_dispatcher_app_registration(struct zpn_tlv *tlv,
                                                    int64_t conn_incarnation,
                                                    struct zpn_broker_dispatcher_app_registration *data);

int zpn_send_zpn_transit_req(struct zpn_tlv *tlv, int64_t conn_incarnation, struct zpn_transit_req *data);

int zpn_send_zpn_mtunnel_stats(struct zpn_tlv *tlv, int64_t conn_incarnation, struct zpn_mtunnel_stats *data);



int zpn_send_zdx_client_authenticate_ack(struct fohh_connection *f_conn,
                                         int64_t conn_incarnation,
                                         struct zpn_client_authenticate_ack *ack);

int zpn_send_zpn_file_fetch_key(struct fohh_connection *f_conn,
                                       int64_t f_conn_incarnation,
                                       struct zpn_file_fetch_key *data);

int zpn_send_exporter_log_data(struct zpn_tlv *tlv,
                               int32_t tag_id,
                               char *console_user,
                               enum zpn_console_credential_type cred_type,
                               int64_t capabilities_policy_id,
                               char *file_transfer_list,
                               int64_t cred_policy_id,
                               char *guac_error_string,
                               char *console_conn_type,
                               int32_t is_pra_session,
                               char *session_recording,
                               char *pra_conn_id,
                               char *shared_users_list,
                               char *pra_shared_mode,
                               char *user_email,
                               int32_t event_type,
                               char *credential_id,
                               char *credential_pool_id);

int zpn_send_exporter_log_data_ack(struct zpn_tlv *tlv,
                                   int32_t tag_id,
                                   const char *error);

typedef int (*zpn_broker_dispatch_c2c_regex_check_cb)(const struct zpn_broker_request *brk_req);

int zpn_dispatcher_set_c2c_regex_cb(zpn_broker_dispatch_c2c_regex_check_cb cb);

int zpn_send_exporter_guac_proxy_data(struct zpn_tlv *tlv,
                                      int32_t tag_id,
                                      char *guac_conn_id,
                                      char *sess_user,
                                      int32_t event,
                                      char *user_email,
                                      int64_t capability_policy_id);

int zpn_send_exporter_guac_proxy_data_ack(struct zpn_tlv *tlv,
                                   int32_t tag_id,
                                   const char *error,
                                   int32_t cmd_type,
                                   int64_t cmd_data);

int zpn_send_decrypt_key_request(struct fohh_connection *f_conn,
                                 int64_t f_conn_incarnation,
                                 struct zpn_decrypt_key_request *data);

int zpn_send_decrypt_key_response(struct fohh_connection *f_conn,
                                  int64_t f_conn_incarnation,
                                  struct zpn_decrypt_key_response *data);

int zpn_send_zpn_mtunnel_data_mconn_stats(struct fohh_connection *f_conn,
                                          int64_t conn_incarnation,
                                          struct zpn_mtunnel_data_mconn_stats  *data);
int zpn_broker_mission_critical_ack(struct fohh_connection *f_conn,
                                        int64_t f_conn_incarnation,
                                        struct zpn_broker_mission_critical_resp *msg);

int zpn_broker_mission_critical_req(struct fohh_connection *f_conn,
                                        int64_t f_conn_incarnation,
                                        struct zpn_broker_mission_critical *msg);
#endif /* _ZPN_RPC_H_ */
