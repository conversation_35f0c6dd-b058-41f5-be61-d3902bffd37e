/*
 * zpn_idp.c. Copyright (C) 2015 Zscaler Inc. All Rights Reserved.
 */

#include "argo/argo.h"
#include "argo/argo_hash.h"
#include "argo/argo_buf.h"
#include "zpath_lib/zpath_app.h"
#include "zpath_lib/zpath_domain_lookup.h"
#include "zpath_lib/zpath_local.h"

#include "zsaml/zsaml.h"
#include "zevent/zevent.h"

#include "zpn/zpn_lib.h"

#include "zpn/zpn_idp.h"
#include "zpn/zpn_idp_compiled.h"


struct customer_load_state {
    int done;
    zpath_mutex_t lock;
    struct wally_callback_queue *queue;
};

static zpath_mutex_t hash_lock;
static struct zhash_table *customer_table = NULL;

struct argo_structure_description *zpn_idp_description = NULL;
struct wally_index_column **zpn_idp_customer_gid_column = NULL;

static void zpn_idp_row_fixup(struct argo_object *row)
{
    struct zpn_idp *idp = row->base_structure_void;
    int i;

    if (!idp->sso_type_count) {
        idp->is_user_idp = 1;
    } else {
        for (i = 0; i < idp->sso_type_count; i++) {
            if (strcasecmp(idp->sso_type[i], "user") == 0) {
                idp->is_user_idp = 1;
            }
        }
    }
}


static int zpn_idp_row_callback(void *cookie,
                                struct wally_registrant *registrant,
                                struct wally_table *table,
                                struct argo_object *previous_row,
                                struct argo_object *row,
                                int64_t request_id)
{
    if (zpn_debug_get(ZPN_DEBUG_IDP_IDX)) {
        char dump[ARGO_BUF_DEFAULT_SIZE];
        if (argo_object_dump(row, dump, sizeof(dump), NULL, 0) == ARGO_RESULT_NO_ERROR) {
            ZPN_DEBUG_IDP("Row callback: %s", dump);
        }
    }

    return WALLY_RESULT_NO_ERROR;
}

int zpn_idp_get_customer_gid(int64_t customer_gid,
                             struct zpn_idp **idps,
                             size_t *count,
                             wally_response_callback_f callback_f,
                             void *callback_cookie,
                             int64_t callback_id)
{
    int shard_index = ZPATH_SHARD_FROM_GID(customer_gid);
    int res;

    res = wally_table_get_rows_fast(zpn_idp_customer_gid_column[shard_index],
                                    &customer_gid,
                                    sizeof(customer_gid),
                                    (void **)&(idps[0]),
                                    count,
                                    1,
                                    callback_f,
                                    callback_cookie,
                                    callback_id);
    if (res) {
        if (res == WALLY_RESULT_ASYNCHRONOUS) {
            // Common
        } else {
            ZPN_LOG(AL_WARNING, "Fetch cert for %ld returned %s", (long) customer_gid, zpn_result_string(res));
        }
    }
    return res;
}

void argo_zpn_idp_init()
{
    if(!argo_register_global_structure(ZPN_IDP_HELPER))
        ZPN_LOG(AL_ERROR, "Could not register zpn_idp argo object");

}

int zpn_idp_init(struct wally *single_tenant_wally, int64_t tenant_gid, int fully_load, int register_with_zpath_table, wally_row_callback_f *row_cb)
{
    int res;
    struct wally_table *table;

    zpn_idp_description = argo_register_global_structure(ZPN_IDP_HELPER);
    if (!zpn_idp_description) return ZPN_RESULT_ERR;

    customer_table = zhash_table_alloc(&zpn_allocator);
    hash_lock = ZPATH_MUTEX_INIT;

    if (row_cb == NULL) {
        row_cb = zpn_idp_row_callback;
    }

    if (single_tenant_wally) {
        if (fully_load) {

            int shard_index = ZPATH_SHARD_FROM_GID(tenant_gid);
            zpn_idp_customer_gid_column = ZPN_CALLOC(ZPATH_MAX_SHARDS * sizeof(*zpn_idp_customer_gid_column));

            res = zpath_app_fully_loaded_customer_table(&table,
                                                        ZPATH_GID_GET_CUSTOMER_GID(tenant_gid),
                                                        single_tenant_wally,
                                                        zpn_idp_description,
                                                        row_cb,
                                                        NULL,
                                                        zpn_idp_row_fixup,
                                                        register_with_zpath_table);
            if (res) {
                ZPN_LOG(AL_ERROR, "Could not fully load zpn_idp table");
                return ZPN_RESULT_ERR;
            }

            zpn_idp_customer_gid_column[shard_index] = wally_table_get_index(table, "customer_gid");
            if (!zpn_idp_customer_gid_column[shard_index]) {
                ZPN_LOG(AL_ERROR, "Could not get column customer_gid=%"PRId64, tenant_gid);
                return ZPN_RESULT_ERR;
            }

        } else {
            int shard_index = ZPATH_SHARD_FROM_GID(tenant_gid);
            zpn_idp_customer_gid_column = ZPN_CALLOC(ZPATH_MAX_SHARDS * sizeof(*zpn_idp_customer_gid_column));

            table = wally_table_create(single_tenant_wally,
                                    1,
                                    zpn_idp_description,
                                    row_cb,
                                    NULL,
                                    1,
                                    0,
                                    zpn_idp_row_fixup);
            if (!table) {
                ZPN_LOG(AL_ERROR, "Could not get zpn_idp table");
                return ZPN_RESULT_ERR;
            }

            zpn_idp_customer_gid_column[shard_index] = wally_table_get_index(table, "customer_gid");
            if (!zpn_idp_customer_gid_column[shard_index]) {
                ZPN_LOG(AL_ERROR, "Could not get column for customer_gid=%"PRId64, tenant_gid);
                return ZPN_RESULT_ERR;
            }
        }
    } else {

        res = zpath_app_add_sharded_table(zpn_idp_description,
                                          row_cb,
                                          NULL,
                                          0,
                                          zpn_idp_row_fixup);
        if (res) {
            return res;
        }

        zpn_idp_customer_gid_column = zpath_app_get_sharded_index("zpn_idp", "customer_gid");
        if (!zpn_idp_customer_gid_column) {
            return ZPN_RESULT_ERR;
        }
    }

    return ZPN_RESULT_NO_ERROR;
}

static void zpn_idp_load_response_callback_deferred(void *cookie1, void *cookie2)
{
    struct customer_load_state *load_state = cookie1;
    ZPATH_MUTEX_LOCK(&(load_state->lock), __FILE__, __LINE__);
    if (!load_state->done) {
        wally_callback_queue_callback(load_state->queue);
        load_state->done = 1;
    }
    ZPATH_MUTEX_UNLOCK(&(load_state->lock), __FILE__, __LINE__);
}

static int zpn_idp_load_response_callback(void *response_callback_cookie,
                                                     struct wally_registrant *registrant,
                                                     struct wally_table *table,
                                                     int64_t request_id,
                                                     int row_count)
{
    zevent_defer(zpn_idp_load_response_callback_deferred, response_callback_cookie, NULL, 0);
    return ZPATH_RESULT_NO_ERROR;
}


int zpn_idp_table_load(int64_t customer_gid,
                       wally_response_callback_f callback_f,
                       void *callback_cookie,
                       int64_t callback_id)
{
    /* This code is optimized to not need locks for the common case */
    struct customer_load_state *load_state;
    int res;

    if (!customer_gid) {
        ZPN_LOG(AL_ERROR, "customer_gid = 0");
        return ZPN_RESULT_ERR;
    }

    load_state = zhash_table_lookup(customer_table, &customer_gid, sizeof(customer_gid), NULL);
    if (!load_state) {
        ZPATH_MUTEX_LOCK(&hash_lock, __FILE__, __LINE__);
        load_state = zhash_table_lookup(customer_table, &customer_gid, sizeof(customer_gid), NULL);
        if (!load_state) {
            load_state = ZPN_CALLOC(sizeof(*load_state));
            load_state->lock = ZPATH_MUTEX_INIT;
            zhash_table_store(customer_table, &customer_gid, sizeof(customer_gid), 0, load_state);
        }
        ZPATH_MUTEX_UNLOCK(&hash_lock, __FILE__, __LINE__);
    }
    if (load_state->done) return ZPATH_RESULT_NO_ERROR;

    ZPATH_MUTEX_LOCK(&(load_state->lock), __FILE__, __LINE__);
    if (load_state->done) {
        /* Might have been set while we wait for lock... */
        ZPATH_MUTEX_UNLOCK(&(load_state->lock), __FILE__, __LINE__);
        return ZPATH_RESULT_NO_ERROR;
    }
    if (!load_state->queue) {
        /* Need to register */
        /* NOTE: register_for_row can call our callback SYNCHRONOUSLY
         * if wally already has state for this connection. We are not
         * handling that in this case because it should be impossible
         * for something else to have read this state before this
         * routine, and this routine only reads it once. If this use
         * model were to change, this code would have to be
         * improved. */

        int shard_id = ZPATH_GID_GET_SHARD(customer_gid);
        load_state->queue = wally_callback_queue_create();
        res = wally_table_register_for_row(NULL,
                                           zpn_idp_customer_gid_column[shard_id],
                                           &customer_gid,
                                           sizeof(customer_gid),
                                           0, // int64_t request_id,
                                           0, // int64_t request_sequence,
                                           zpn_app_request_atleast_one, // int request_atleast_one,
                                           0, // int just_callback,
                                           0, // int unique_registration,
                                           zpn_idp_load_response_callback,
                                           load_state);
        /* Result is ALWAYS asynchronous, even if the callback will
         * come synchronously... Should fix that some day. */
        if (res != ZPN_RESULT_ASYNCHRONOUS) {
            ZPN_LOG(AL_ERROR, "Received response = %s", zpn_result_string(res));
        }
    }
    wally_callback_queue_add(load_state->queue, callback_f, callback_cookie, callback_id);

    ZPATH_MUTEX_UNLOCK(&(load_state->lock), __FILE__, __LINE__);

    return ZPN_RESULT_ASYNCHRONOUS;
}
