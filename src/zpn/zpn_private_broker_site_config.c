/*
 * zpn_private_broker_site_config.c. Copyright (C) 2024 Zscaler Inc. All Rights Reserved.
 */

#include <stdio.h>
#include <strings.h>

#include "zpn/zpn_broker_common.h"
#include "zpn/zpn_broker_private.h"
#include "zpn/zpn_firedrill_site.h"
#include "zpn/zpn_private_broker_util.h"
#include "zpn/zpn_private_broker_site_config.h"

#define ZPN_PBROKER_SITE_CONFIG_FILE                "site_config.cfg"
#define ZPN_PBROKER_SITE_CONFIG_MAX_LINE_SIZE       (ZPN_MAX_DOMAIN_NAME_LEN + 40)
#define ZPN_PBROKER_SITE_CONFIG_FILE_MAX_SIZE       512

#define ZPN_PBROKER_SITE_OFFLINE_DOMAIN_STR         "SITE_OFFLINE_DOMAIN"
#define ZPN_PBROKER_SITE_REENROLL_PERIOD_STR        "REENROLL_PERIOD"
#define ZPN_PBROKER_SITE_SITEC_PREFERRED_STR        "SITEC_PREFERRED"
#define ZPN_PBROKER_SITE_SITE_IS_ACTIVE_STR         "SITE_IS_ACTIVE"
#define ZPN_PBROKER_SITE_MAX_ALLOWED_DOWNTIME_S_STR  "MAX_ALLOWED_DOWNTIME_S"
#define ZPN_PBROKER_FIREDRILL_STATUS_STR             "FIREDRILL_STATUS"
#define ZPN_PBROKER_FIREDRILL_INTERVAL_S_STR         "FIREDRILL_INTERVAL_S"
#define ZPN_PBROKER_FIREDRILL_START_TIME_STR         "FIREDRILL_KICKOFF_TIME"

int zpn_private_broker_site_config_load(zpn_pbroker_site_config_t *config)
{
    FILE *fp;
    char line[ZPN_PBROKER_SITE_CONFIG_MAX_LINE_SIZE] = {0};

    if (!config) {
        return ZPN_RESULT_ERR;
    }

    bzero(config, sizeof(*config));

    fp = fopen(ZPN_PBROKER_SITE_CONFIG_FILE, "r");
    if (fp == NULL) {
        if (errno == ENOENT) {
            ZPATH_LOG(AL_DEBUG, "Site config file does not exist");
        } else {
            ZPN_LOG(AL_ERROR, "Failed to read file '%s', err: %s", ZPN_PBROKER_SITE_CONFIG_FILE, strerror(errno));
        }
        return ZPN_RESULT_ERR;
    }

    while (fgets(line, sizeof(line), fp)) {
        /* Check if the line is not empty, a comment, or just a newline */
        if (line[0] != '\0' && line[0] != '#' && strcmp(line, "\n") != 0 && strcmp(line, "\r\n") != 0) {
            if (strstr(line, ZPN_PBROKER_SITE_SITE_IS_ACTIVE_STR)) {
                sscanf(line, ZPN_PBROKER_SITE_SITE_IS_ACTIVE_STR":%d", &config->site_is_active);
            } else if (strstr(line, ZPN_PBROKER_SITE_SITEC_PREFERRED_STR)) {
                sscanf(line, ZPN_PBROKER_SITE_SITEC_PREFERRED_STR":%d", &config->sitec_preferred);
            } else if (strstr(line, ZPN_PBROKER_SITE_OFFLINE_DOMAIN_STR)) {
                sscanf(line, ZPN_PBROKER_SITE_OFFLINE_DOMAIN_STR":%99s", config->offline_domain);
            } else if (strstr(line, ZPN_PBROKER_SITE_REENROLL_PERIOD_STR)) {
                sscanf(line, ZPN_PBROKER_SITE_REENROLL_PERIOD_STR":%"PRId64, &config->reenroll_period);
            } else if (strstr(line, ZPN_PBROKER_SITE_MAX_ALLOWED_DOWNTIME_S_STR)) {
                sscanf(line, ZPN_PBROKER_SITE_MAX_ALLOWED_DOWNTIME_S_STR":%"PRId64, &config->max_allowed_downtime_s);
            } else if (strstr(line, ZPN_PBROKER_FIREDRILL_STATUS_STR)) {
                sscanf(line, ZPN_PBROKER_FIREDRILL_STATUS_STR":%d", &config->firedrill_cfg.status);
            } else if (strstr(line, ZPN_PBROKER_FIREDRILL_INTERVAL_S_STR)) {
                sscanf(line, ZPN_PBROKER_FIREDRILL_INTERVAL_S_STR":%"PRId64"", &config->firedrill_cfg.interval_s);
            } else if (strstr(line, ZPN_PBROKER_FIREDRILL_START_TIME_STR)) {
                sscanf(line, ZPN_PBROKER_FIREDRILL_START_TIME_STR":%"PRId64"", &config->firedrill_cfg.start_time);
            }
        }
    }

    fclose(fp);

    ZPN_LOG(AL_NOTICE, "Successfully read config file, sitec preferred: %d, site: %s, offline domain: %s, reenroll period: %"PRId64", max_allowed_downtime_s: %"PRId64"  \
                        firedrill status: %d, firedrill interval: %"PRId64", firedrill start time: %"PRId64"",
                       config->sitec_preferred,
                       config->site_is_active ? "active" : "inactive",
                       config->offline_domain, config->reenroll_period,
                       config->max_allowed_downtime_s,
                       config->firedrill_cfg.status,
                       config->firedrill_cfg.interval_s,
                       config->firedrill_cfg.start_time);

    return ZPN_RESULT_NO_ERROR;
}

int zpn_private_broker_site_config_save(const zpn_pbroker_site_config_t *config)
{
    FILE *fp;
    char out_str[ZPN_PBROKER_SITE_CONFIG_FILE_MAX_SIZE] = {0};
    char *cur = out_str;
    char *end = out_str + ZPN_PBROKER_SITE_CONFIG_FILE_MAX_SIZE;

    if (!config) {
        return ZPN_RESULT_ERR;
    }

    if (strcmp(config->offline_domain, "") == 0) {
        ZPN_LOG(AL_DEBUG, "Offline domain is not defined, try to remove the config file");
        if (unlink(ZPN_PBROKER_SITE_CONFIG_FILE) == 0) {
            ZPN_LOG(AL_NOTICE, "Successfully deleted site config file");
        } else {
            /* Check if the error is ENOENT (no such file or directory) */
            if (errno != ENOENT) {
                ZPN_LOG(AL_ERROR, "Cannot delete site config file, error: %s", strerror(errno));
                return ZPN_RESULT_ERR;
            }
        }
        return ZPN_RESULT_NO_ERROR;
    }

    cur += sxprintf(cur, end, "%s:%d\n", ZPN_PBROKER_SITE_SITE_IS_ACTIVE_STR, config->site_is_active);
    cur += sxprintf(cur, end, "%s:%d\n", ZPN_PBROKER_SITE_SITEC_PREFERRED_STR, config->sitec_preferred);
    cur += sxprintf(cur, end, "%s:%s\n", ZPN_PBROKER_SITE_OFFLINE_DOMAIN_STR, config->offline_domain);
    cur += sxprintf(cur, end, "%s:%"PRId64"\n", ZPN_PBROKER_SITE_REENROLL_PERIOD_STR, config->reenroll_period);
    cur += sxprintf(cur, end, "%s:%"PRId64"\n", ZPN_PBROKER_SITE_MAX_ALLOWED_DOWNTIME_S_STR, config->max_allowed_downtime_s);
    cur += sxprintf(cur, end, "%s:%d\n", ZPN_PBROKER_FIREDRILL_STATUS_STR, config->firedrill_cfg.status);
    cur += sxprintf(cur, end, "%s:%"PRId64"\n", ZPN_PBROKER_FIREDRILL_INTERVAL_S_STR, config->firedrill_cfg.interval_s);
    cur += sxprintf(cur, end, "%s:%"PRId64"\n", ZPN_PBROKER_FIREDRILL_START_TIME_STR, config->firedrill_cfg.start_time);

    fp = fopen(ZPN_PBROKER_SITE_CONFIG_FILE, "w");
    if (fp == NULL) {
        ZPN_LOG(AL_ERROR, "Failed to write file '%s', err: %s", ZPN_PBROKER_SITE_CONFIG_FILE, strerror(errno));
        return ZPN_RESULT_ERR;
    }

    if (fwrite(out_str, strlen(out_str), 1, fp) != 1) {
        ZPN_LOG(AL_ERROR, "Failed to write config file, err: %s", strerror(errno));
    } else {
        ZPN_LOG(AL_NOTICE, "Successfully write config file, sitec preferred: %d, offline domain: %s, reenroll period: %"PRId64" firedrill status: %d, interval: %"PRId64", startime: %"PRId64,
                            config->sitec_preferred, config->offline_domain, config->reenroll_period, config->firedrill_cfg.status, config->firedrill_cfg.interval_s, config->firedrill_cfg.start_time);
    }

    fclose(fp);

    return ZPN_RESULT_NO_ERROR;
}
