/*
 * zpn_fohh_client.c. Copyright (C) 2014 Zscaler Inc. All Rights Reserved
 */

#include "argo/argo_hash.h"
#include "fohh/fohh.h"
#include "fohh/fohh_http.h"

#include "zpath_misc/zpath_version.h"
#include "zpn/zpn.h"
#include "zpn/zpn_lib.h"
#include "zpn/zpn_rpc.h"
#include "zpn/zpn_mconn.h"
#include "zpn/zpn_mconn_bufferevent.h"
#include "zpn/zpn_mconn_fohh_tlv.h"
#include "zpn/zpn_mconn_udp_tlv.h"
#include "zpn/zpn_fohh_worker.h"
#include "zpn/zpn_connector_tun.h"

#include "zpn/zpn_fohh_client.h"

#define FAIL_TO_CONNECT_REDIRECT_TIME_US    (10*1000000)      /* 10 seconds */

static int zfc_unblock_callback(struct fohh_connection *connection, enum fohh_queue_element_type element_type,
                                void *cookie);
static int zfc_conn_callback(struct fohh_connection *connection, enum fohh_connection_state state, void *cookie);

const char *zfc_status_string(enum zfc_status status)
{
    switch (status) {
    case zfc_init:
        return "zfc_init";
    case zfc_ready:
        return "zfc_ready";
    case zfc_authenticate:
        return "zfc_authenticating";
    case zfc_not_connected:
        return "zfc_not_connected";
    case zfc_cert_error:
        return "zfc_cert_error";
    case zfc_error:
        return "zfc_error";
    case zfc_auth_fail:
        return "zfc_auth_fail";
    case zfc_invalid:
        return "zfc_invalid";
    default:
        return "ERROR";
    }
}

static int zfc_version_ack_cb(void *argo_cookie_ptr,
                              void *argo_structure_cookie_ptr,
                              struct argo_object *object)
{
    struct zpn_fohh_client *zfc = argo_structure_cookie_ptr;
    struct zpn_version_ack *ack = object->base_structure_void;

    if (ack->error) {
        ZPN_LOG(AL_ERROR, "%s: Version ack error: %s; closing client.", ZFC_DBG(zfc), ack->error);
    }
    return ZPN_RESULT_NO_ERROR;
}

static int zfc_machine_tunnel_authenticate_ack_cb(void *argo_cookie_ptr,
                                                  void *argo_structure_cookie_ptr,
                                                  struct argo_object *object)
{
    struct zpn_fohh_client *zfc = argo_structure_cookie_ptr;
    struct zpn_machine_tunnel_client_authenticate_ack *ack = object->base_structure_void;
    char dbg_str[1000];

    if (zpn_debug_get(ZPN_DEBUG_CLIENT_IDX)) {
        char dump[8000];
        if (argo_object_dump(object, dump, sizeof(dump), NULL, 1) == ARGO_RESULT_NO_ERROR) {
            ZPN_LOG(AL_DEBUG, "%s: Rx: %s", ZFC_DBG(zfc), dump);
        }
    }

    /* Save tunnel ID */
    if (zfc->tunnel_id) {
        ZPN_FREE(zfc->tunnel_id);
        zfc->tunnel_id = NULL;
    }
    if (ack->tunnel_id) {
        zfc->tunnel_id = ZPN_STRDUP(ack->tunnel_id, strlen(ack->tunnel_id));
    }

    /* Set/Reset debug string */

    if (ack->current_broker) {
        if (ack->tunnel_id) {
            snprintf(dbg_str, sizeof(dbg_str), "%s:%s", ack->tunnel_id, ack->current_broker);
        } else {
            snprintf(dbg_str, sizeof(dbg_str), "%s", ack->current_broker);
        }
    } else {
        snprintf(dbg_str, sizeof(dbg_str), "%s", zfc->broker_dns_name);
    }
    ZPATH_MUTEX_LOCK(&(zfc->lock), __FILE__, __LINE__);
    snprintf(zfc->dbg_str, sizeof(zfc->dbg_str), "%s", dbg_str);
    ZPATH_MUTEX_UNLOCK(&(zfc->lock), __FILE__, __LINE__);

    if (ack->error) {
        ZPN_LOG(AL_ERROR, "%s: Authenticate ack error: %s; closing client.", ZFC_DBG(zfc), ack->error);
        zfc->status = zfc_auth_fail;
    } else {
        ZPN_DEBUG_CLIENT("%s: Machine Client is authenticated", ZFC_DBG(zfc));
        zfc->status = zfc_ready;
        //fohh_set_debug(zfc->conn, 1);
    }

    if (zfc->status_cb) {
        (zfc->status_cb)(zfc, zfc->cookie_void, zfc->cookie_int, zfc->status, ack->error);
    }

    return ZPN_RESULT_NO_ERROR;
}

static int zfc_authenticate_ack_cb(void *argo_cookie_ptr,
                                   void *argo_structure_cookie_ptr,
                                   struct argo_object *object)
{
    struct zpn_fohh_client *zfc = argo_structure_cookie_ptr;
    struct zpn_client_authenticate_ack *ack = object->base_structure_void;
    char dbg_str[1000];

    ZPATH_MUTEX_LOCK(&(zfc->lock), __FILE__, __LINE__);
    if (zpn_debug_get(ZPN_DEBUG_CLIENT_IDX)) {
        char dump[8000];
        if (argo_object_dump(object, dump, sizeof(dump), NULL, 1) == ARGO_RESULT_NO_ERROR) {
            ZPN_LOG(AL_DEBUG, "%s: Rx: %s", ZFC_DBG(zfc), dump);
        }
    }

    /* Save tunnel ID */
    if (zfc->tunnel_id) {
        ZPN_FREE(zfc->tunnel_id);
        zfc->tunnel_id = NULL;
    }
    if (ack->tunnel_id) {
        zfc->tunnel_id = ZPN_STRDUP(ack->tunnel_id, strlen(ack->tunnel_id));
    }

    /* Set/Reset debug string */

    if (ack->current_broker) {
        if (ack->tunnel_id) {
            snprintf(dbg_str, sizeof(dbg_str), "%s:%s", ack->tunnel_id, ack->current_broker);
        } else {
            snprintf(dbg_str, sizeof(dbg_str), "%s", ack->current_broker);
        }
    } else {
        snprintf(dbg_str, sizeof(dbg_str), "%s", zfc->broker_dns_name);
    }
    snprintf(zfc->dbg_str, sizeof(zfc->dbg_str), "%s", dbg_str);

    if (ack->error) {
        ZPN_LOG(AL_ERROR, "%s: Authenticate ack error: %s; closing client.", ZFC_DBG(zfc), ack->error);
        zfc->status = zfc_auth_fail;
    } else {
        ZPN_DEBUG_CLIENT("%s: Client is authenticated", ZFC_DBG(zfc));
        zfc->status = zfc_ready;
        //fohh_set_debug(zfc->conn, 1);
    }

    ZPATH_MUTEX_UNLOCK(&(zfc->lock), __FILE__, __LINE__);

    if (zfc->status_cb) {
        (zfc->status_cb)(zfc, zfc->cookie_void, zfc->cookie_int, zfc->status, ack->error);
    }

    return ZPN_RESULT_NO_ERROR;
}

static int zfc_trusted_client_authenticate_ack_cb(void *argo_cookie_ptr,
                                                  void *argo_structure_cookie_ptr,
                                                  struct argo_object *object)
{
    struct zpn_fohh_client *zfc = argo_structure_cookie_ptr;
    struct zpn_trusted_client_authenticate_ack *ack = object->base_structure_void;
    char dbg_str[1000];

    ZPATH_MUTEX_LOCK(&(zfc->lock), __FILE__, __LINE__);
    if (zpn_debug_get(ZPN_DEBUG_CLIENT_IDX)) {
        char dump[8000];
        if (argo_object_dump(object, dump, sizeof(dump), NULL, 1) == ARGO_RESULT_NO_ERROR) {
            ZPN_LOG(AL_DEBUG, "%s: Rx: %s", ZFC_DBG(zfc), dump);
        }
    }

    /* Save tunnel ID */
    if (zfc->tunnel_id) {
        ZPN_FREE(zfc->tunnel_id);
        zfc->tunnel_id = NULL;
    }
    if (ack->tunnel_id) {
        zfc->tunnel_id = ZPN_STRDUP(ack->tunnel_id, strlen(ack->tunnel_id));
    }

    /* Set/Reset debug string */

    if (ack->current_broker) {
        if (ack->tunnel_id) {
            snprintf(dbg_str, sizeof(dbg_str), "%s:%s", ack->tunnel_id, ack->current_broker);
        } else {
            snprintf(dbg_str, sizeof(dbg_str), "%s", ack->current_broker);
        }
    } else {
        snprintf(dbg_str, sizeof(dbg_str), "%s", zfc->broker_dns_name);
    }
    snprintf(zfc->dbg_str, sizeof(zfc->dbg_str), "%s", dbg_str);

    if (ack->error) {
        ZPN_LOG(AL_ERROR, "%s: Authenticate ack error: %s; closing client.", ZFC_DBG(zfc), ack->error);
        zfc->status = zfc_auth_fail;
    } else {
        ZPN_DEBUG_CLIENT("%s: Client is authenticated", ZFC_DBG(zfc));
        zfc->status = zfc_ready;
        //fohh_set_debug(zfc->conn, 1);
    }

    ZPATH_MUTEX_UNLOCK(&(zfc->lock), __FILE__, __LINE__);

    if (zfc->status_cb) {
        (zfc->status_cb)(zfc, zfc->cookie_void, zfc->cookie_int, zfc->status, ack->error);
    }

    return ZPN_RESULT_NO_ERROR;
}

static int zfc_exporter_client_authenticate_ack_cb(void *argo_cookie_ptr,
                                                   void *argo_structure_cookie_ptr,
                                                   struct argo_object *object)
{
    struct zpn_fohh_client *zfc = argo_structure_cookie_ptr;
    struct zpn_exporter_client_authenticate_ack *ack = object->base_structure_void;
    char dbg_str[1000];

    ZPATH_MUTEX_LOCK(&(zfc->lock), __FILE__, __LINE__);
    if (zpn_debug_get(ZPN_DEBUG_CLIENT_IDX)) {
        char dump[8000];
        if (argo_object_dump(object, dump, sizeof(dump), NULL, 1) == ARGO_RESULT_NO_ERROR) {
            ZPN_LOG(AL_DEBUG, "%s: Rx: %s", ZFC_DBG(zfc), dump);
        }
    }

    /* Save tunnel ID */
    if (zfc->tunnel_id) {
        ZPN_FREE(zfc->tunnel_id);
        zfc->tunnel_id = NULL;
    }
    if (ack->tunnel_id) {
        zfc->tunnel_id = ZPN_STRDUP(ack->tunnel_id, strlen(ack->tunnel_id));
    }

    /* Set/Reset debug string */

    if (ack->current_broker) {
        if (ack->tunnel_id) {
            snprintf(dbg_str, sizeof(dbg_str), "%s:%s", ack->tunnel_id, ack->current_broker);
        } else {
            snprintf(dbg_str, sizeof(dbg_str), "%s", ack->current_broker);
        }
    } else {
        snprintf(dbg_str, sizeof(dbg_str), "%s", zfc->broker_dns_name);
    }
    snprintf(zfc->dbg_str, sizeof(zfc->dbg_str), "%s", dbg_str);

    if (ack->error) {
        ZPN_LOG(AL_ERROR, "%s: Authenticate ack error: %s; closing client.", ZFC_DBG(zfc), ack->error);
        zfc->status = zfc_auth_fail;
    } else {
        ZPN_DEBUG_CLIENT("%s: Client is authenticated", ZFC_DBG(zfc));
        zfc->status = zfc_ready;
        //fohh_set_debug(zfc->conn, 1);
    }

    ZPATH_MUTEX_UNLOCK(&(zfc->lock), __FILE__, __LINE__);

    if (zfc->status_cb) {
        (zfc->status_cb)(zfc, zfc->cookie_void, zfc->cookie_int, zfc->status, ack->error);
    }
    return ZPN_RESULT_NO_ERROR;
}

static int zfc_pbroker_client_authenticate_ack_cb(void *argo_cookie_ptr,
                                                   void *argo_structure_cookie_ptr,
                                                   struct argo_object *object) {
    struct zpn_fohh_client *zfc = argo_structure_cookie_ptr;
    struct zpn_pbroker_client_authenticate_ack *ack = object->base_structure_void;
    char dbg_str[1000];

    ZPATH_MUTEX_LOCK(&(zfc->lock), __FILE__, __LINE__);
    if (zpn_debug_get(ZPN_DEBUG_CLIENT_IDX)) {
        char dump[8000];
        if (argo_object_dump(object, dump, sizeof(dump), NULL, 1) == ARGO_RESULT_NO_ERROR) {
            ZPN_LOG(AL_DEBUG, "%s: Rx: %s", ZFC_DBG(zfc), dump);
        }
    }

    /* Save tunnel ID */
    if (zfc->tunnel_id) {
        ZPN_FREE(zfc->tunnel_id);
        zfc->tunnel_id = NULL;
    }
    if (ack->tunnel_id) {
        zfc->tunnel_id = ZPN_STRDUP(ack->tunnel_id, strlen(ack->tunnel_id));
    }

    /* Set/Reset debug string */

    if (ack->current_broker) {
        if (ack->tunnel_id) {
            snprintf(dbg_str, sizeof(dbg_str), "%s:%s", ack->tunnel_id, ack->current_broker);
        } else {
            snprintf(dbg_str, sizeof(dbg_str), "%s", ack->current_broker);
        }
    } else {
        snprintf(dbg_str, sizeof(dbg_str), "%s", zfc->broker_dns_name);
    }
    snprintf(zfc->dbg_str, sizeof(zfc->dbg_str), "%s", dbg_str);

    if (ack->error) {
        ZPN_LOG(AL_ERROR, "%s: Authenticate ack error: %s; closing client.", ZFC_DBG(zfc), ack->error);
        zfc->status = zfc_auth_fail;
    } else {
        ZPN_DEBUG_CLIENT("%s: Client is authenticated", ZFC_DBG(zfc));
        zfc->status = zfc_ready;
        //fohh_set_debug(zfc->conn, 1);
    }

    ZPATH_MUTEX_UNLOCK(&(zfc->lock), __FILE__, __LINE__);

    if (zfc->status_cb) {
        (zfc->status_cb)(zfc, zfc->cookie_void, zfc->cookie_int, zfc->status, ack->error);
    }
    return ZPN_RESULT_NO_ERROR;
}



static int zfc_mtunnel_tag_pause_cb(void *argo_cookie_ptr,
                                    void *argo_structure_cookie_ptr,
                                    struct argo_object *object)
{
    struct zpn_fohh_client *zfc = argo_structure_cookie_ptr;
    struct zpn_mtunnel_tag_pause *req = object->base_structure_void;
    struct zpn_fohh_tlv *fohh_tlv = NULL;
    struct zpn_mconn_fohh_tlv *mconn_tlv = NULL;
    struct zpn_mconn *mconn = NULL;

    char dump[8000];
    if (!zfc) {
        ZPN_LOG(AL_ERROR, "NULL fohh client state");
        return ZPN_RESULT_ERR;
    }

    if (argo_object_dump(object, dump, sizeof(dump), NULL, 1) == ARGO_RESULT_NO_ERROR) {
        ZPN_LOG(AL_DEBUG, "%s: Rx: %s", ZFC_DBG(zfc), dump);
    }

    if (!req->tag_id) {
        ZPN_LOG(AL_CRITICAL, "%s: mtunnel_pause without tag_id", ZFC_DBG(zfc));
        return ZPN_RESULT_NO_ERROR;
    }

    fohh_tlv = &(zfc->fohh_tlv_state);

    if (fohh_tlv->remote_fc_status == flow_ctrl_enabled) {
        /* Ignore pause/resume message if remote is doing flow control */
        return ZPN_RESULT_NO_ERROR;
    }

    mconn_tlv = zpn_fohh_tlv_get_local_owner(&(zfc->fohh_tlv_state), req->tag_id);
    if (!mconn_tlv) {
        ZPN_LOG(AL_INFO, "%s: Window update received for mtunnel tag_id = %d that isn't found", ZFC_DBG(zfc), (int)req->tag_id);
        return ZPN_RESULT_NO_ERROR;
    }

    mconn = &(mconn_tlv->mconn);

    if (mconn->global_owner) {
        (mconn->global_owner_calls->lock)(mconn,
                                          mconn->self,
                                          mconn->global_owner,
                                          mconn->global_owner_key,
                                          mconn->global_owner_key_length);
    } else {
        return ZPN_RESULT_NO_ERROR;
    }

    if (mconn->global_owner) {
        zpn_mconn_to_client_pause(mconn);
    }

    (mconn->global_owner_calls->unlock)(mconn,
                                        mconn->self,
                                        mconn->global_owner,
                                        mconn->global_owner_key,
                                        mconn->global_owner_key_length);

    return ZPN_RESULT_NO_ERROR;
}

static int zfc_mtunnel_tag_resume_cb(void *argo_cookie_ptr,
                                     void *argo_structure_cookie_ptr,
                                     struct argo_object *object)
{
    struct zpn_fohh_client *zfc = argo_structure_cookie_ptr;
    struct zpn_mtunnel_tag_resume *req = object->base_structure_void;
    struct zpn_fohh_tlv *fohh_tlv = NULL;
    struct zpn_mconn_fohh_tlv *mconn_tlv = NULL;
    struct zpn_mconn *mconn = NULL;

    if (!zfc) {
        ZPN_LOG(AL_ERROR, "NULL fohh client state");
        return ZPN_RESULT_ERR;
    }

    char dump[8000];
    if (argo_object_dump(object, dump, sizeof(dump), NULL, 1) == ARGO_RESULT_NO_ERROR) {
        ZPN_LOG(AL_DEBUG, "%s: Rx: %s", ZFC_DBG(zfc), dump);
    }

    if (!req->tag_id) {
        ZPN_LOG(AL_CRITICAL, "%s: mtunnel_pause without tag_id", ZFC_DBG(zfc));
        return ZPN_RESULT_NO_ERROR;
    }

    fohh_tlv = &(zfc->fohh_tlv_state);

    if (fohh_tlv->remote_fc_status == flow_ctrl_enabled) {
        /* Ignore pause/resume message if remote is doing flow control */
        return ZPN_RESULT_NO_ERROR;
    }

    mconn_tlv = zpn_fohh_tlv_get_local_owner(&(zfc->fohh_tlv_state), req->tag_id);
    if (!mconn_tlv) {
        ZPN_LOG(AL_INFO, "%s: Window update received for mtunnel tag_id = %d that isn't found", ZFC_DBG(zfc), (int)req->tag_id);
        return ZPN_RESULT_NO_ERROR;
    }

    mconn = &(mconn_tlv->mconn);

    if (mconn->global_owner) {
        (mconn->global_owner_calls->lock)(mconn,
                                          mconn->self,
                                          mconn->global_owner,
                                          mconn->global_owner_key,
                                          mconn->global_owner_key_length);
    } else {
        return ZPN_RESULT_NO_ERROR;
    }

    if (mconn->global_owner) {
        zpn_mconn_to_client_resume(mconn);
    }

    (mconn->global_owner_calls->unlock)(mconn,
                                        mconn->self,
                                        mconn->global_owner,
                                        mconn->global_owner_key,
                                        mconn->global_owner_key_length);

    return ZPN_RESULT_NO_ERROR;
}


static int zfc_client_fohh_tlv_window_update_cb(void *argo_cookie_ptr,
                                                void *argo_structure_cookie_ptr,
                                                struct argo_object *object)
{
    struct zpn_fohh_client *zfc = argo_structure_cookie_ptr;
    struct zpn_fohh_tlv_window_update *req = object->base_structure_void;
    struct zpn_fohh_tlv *fohh_tlv = NULL;
    struct zpn_mconn_fohh_tlv *mconn_tlv = NULL;
    int64_t now_us = epoch_us();

    if (!zfc) {
        ZPN_LOG(AL_ERROR, "NULL fohh client state");
        return ZPN_RESULT_ERR;
    }

    char dump[8000];
    if (zpn_debug_get(ZPN_DEBUG_FLOW_CONTROL_IDX)) {
        if (argo_object_dump(object, dump, sizeof(dump), NULL, 1) == ARGO_RESULT_NO_ERROR) {
            ZPN_LOG(AL_DEBUG, "%s: zpn_client_fohh_tlv_window_update_cb Rx: %s", ZFC_DBG(zfc), dump);
        }
    }

    fohh_tlv = &(zfc->fohh_tlv_state);

    if (!req->tag_id) {
        /* For overall FOHH connection */
        ZPATH_MUTEX_LOCK(&(fohh_tlv->lock), __FILE__, __LINE__);
        if (req->tx_limit > fohh_tlv->tx_limit) {
            fohh_tlv->tx_limit = req->tx_limit;
            fohh_tlv->tx_limit_update_us = now_us;
        }
        if (fohh_tlv->remote_rx_data != req->rx_data) {
            fohh_tlv->remote_rx_data = req->rx_data;
            fohh_tlv->remote_rx_data_change_us = now_us;
        }
        if (req->tx_limit) {
            fohh_tlv->remote_fc_status = flow_ctrl_enabled;
        } else {
            fohh_tlv->remote_fc_status = flow_ctrl_disabled;
        }
        ZPATH_MUTEX_UNLOCK(&(fohh_tlv->lock), __FILE__, __LINE__);

        zpn_mconn_fohh_tlv_activate_drain_timer(&(zfc->fohh_tlv_state));
    } else {
        struct zpn_mconn *mconn = NULL;

        mconn_tlv = zpn_fohh_tlv_get_local_owner(&(zfc->fohh_tlv_state), req->tag_id);
        if (!mconn_tlv) {
            ZPN_LOG(AL_INFO, "%s: Window update received for mtunnel tag_id = %d that isn't found", ZFC_DBG(zfc), (int)req->tag_id);
            return ZPN_RESULT_NO_ERROR;
        }

        mconn = &(mconn_tlv->mconn);

        if (mconn->global_owner) {
            (mconn->global_owner_calls->lock)(mconn,
                                              mconn->self,
                                              mconn->global_owner,
                                              mconn->global_owner_key,
                                              mconn->global_owner_key_length);
        } else {
            return ZPN_RESULT_NO_ERROR;
        }

        if (mconn->global_owner) {

            mconn_tlv->remote_fc_status = flow_ctrl_enabled;
            mconn_tlv->tx_limit = req->tx_limit;
            if (mconn_tlv->remote_rx_data != req->rx_data) {
                mconn_tlv->remote_rx_data = req->rx_data;
                mconn_tlv->remote_rx_data_change_us = now_us;
            }

            zpn_client_drain_tx_data(&(mconn_tlv->mconn));
        }

        (mconn->global_owner_calls->unlock)(mconn,
                                            mconn->self,
                                            mconn->global_owner,
                                            mconn->global_owner_key,
                                            mconn->global_owner_key_length);
    }

    return ZPN_RESULT_NO_ERROR;
}

static int zfc_client_fohh_tlv_window_update_batch_cb(void *argo_cookie_ptr,
                                                      void *argo_structure_cookie_ptr,
                                                      struct argo_object *object)
{
    struct zpn_fohh_client *zfc = argo_structure_cookie_ptr;
    struct zpn_fohh_tlv_window_update_batch *zpn_fohh_tlv_window_update_batch = object->base_structure_void;
    struct zpn_fohh_tlv *fohh_tlv = NULL;
    struct zpn_mconn_fohh_tlv *mconn_tlv = NULL;
    int64_t now_us = epoch_us();

    if (!zfc) {
        ZPN_LOG(AL_ERROR, "NULL fohh client state");
        return ZPN_RESULT_ERR;
    }

    char dump[8000];
    if (zpn_debug_get(ZPN_DEBUG_FLOW_CONTROL_IDX)) {
        if (argo_object_dump(object, dump, sizeof(dump), NULL, 1) == ARGO_RESULT_NO_ERROR) {
            ZPN_LOG(AL_DEBUG, "%s: zpn_client_fohh_tlv_window_update_cb Rx: %s", ZFC_DBG(zfc), dump);
        }
    }

    fohh_tlv = &(zfc->fohh_tlv_state);
    for (int idx = 0; idx < zpn_fohh_tlv_window_update_batch->tag_id_count; idx++) {
      if (!zpn_fohh_tlv_window_update_batch->tag_id[idx]) {
          /* For overall FOHH connection */
          ZPATH_MUTEX_LOCK(&(fohh_tlv->lock), __FILE__, __LINE__);
          if (zpn_fohh_tlv_window_update_batch->tx_limit[idx] > fohh_tlv->tx_limit) {
            fohh_tlv->tx_limit = zpn_fohh_tlv_window_update_batch->tx_limit[idx];
            fohh_tlv->tx_limit_update_us = now_us;
          }
          if (fohh_tlv->remote_rx_data != zpn_fohh_tlv_window_update_batch->rx_data[idx]) {
            fohh_tlv->remote_rx_data = zpn_fohh_tlv_window_update_batch->rx_data[idx];
            fohh_tlv->remote_rx_data_change_us = now_us;
          }
          if (zpn_fohh_tlv_window_update_batch->tx_limit[idx]) {
            fohh_tlv->remote_fc_status = flow_ctrl_enabled;
          } else {
            fohh_tlv->remote_fc_status = flow_ctrl_disabled;
          }
          ZPATH_MUTEX_UNLOCK(&(fohh_tlv->lock), __FILE__, __LINE__);
          zpn_mconn_fohh_tlv_activate_drain_timer(&(zfc->fohh_tlv_state));
      } else {
          mconn_tlv = zpn_fohh_tlv_get_local_owner(&(zfc->fohh_tlv_state), zpn_fohh_tlv_window_update_batch->tag_id[idx]);
          if (!mconn_tlv) {
            ZPN_LOG(AL_INFO, "%s: Window update received for mtunnel tag_id = %d that isn't found",
                    ZFC_DBG(zfc), (int)zpn_fohh_tlv_window_update_batch->tag_id[idx]);
            continue;
          }
          struct zpn_mconn *mconn = &(mconn_tlv->mconn);
          if (mconn->global_owner)
            (mconn->global_owner_calls->lock)(mconn,
                                              mconn->self,
                                              mconn->global_owner,
                                              mconn->global_owner_key,
                                              mconn->global_owner_key_length);
          else
            continue;
          if (mconn->global_owner) {
            mconn_tlv->remote_fc_status = flow_ctrl_enabled;
            mconn_tlv->tx_limit = zpn_fohh_tlv_window_update_batch->tx_limit[idx];
            if (mconn_tlv->remote_rx_data != zpn_fohh_tlv_window_update_batch->rx_data[idx]) {
                mconn_tlv->remote_rx_data = zpn_fohh_tlv_window_update_batch->rx_data[idx];
                mconn_tlv->remote_rx_data_change_us = now_us;
            }
            zpn_client_drain_tx_data(&(mconn_tlv->mconn));
          }
          (mconn->global_owner_calls->unlock)(mconn,
                                              mconn->self,
                                              mconn->global_owner,
                                              mconn->global_owner_key,
                                              mconn->global_owner_key_length);
      } //else
    } // for

    return ZPN_RESULT_NO_ERROR;
}

static int
zfc_unblock_callback(struct fohh_connection *connection, enum fohh_queue_element_type element_type, void *cookie)
{
    struct zpn_fohh_client *zfc = cookie;

    /*
     * comment for maintance proof
     */
    //if (element_type != fohh_queue_element_type_data) {
    //   goto done;
    //}
    zpn_mconn_fohh_tlv_activate_drain_timer(&(zfc->fohh_tlv_state));

    return ZPN_RESULT_NO_ERROR;
}

static void zpn_fohh_client_conn_monitor_cb(evutil_socket_t sock, short flags, void *cookie)
{
    struct zpn_fohh_tlv *fohh_tlv = cookie;
    struct fohh_connection *f_conn = zpn_mconn_fohh_tlv_get_conn(fohh_tlv);

    zpn_mconn_fohh_tlv_fc_monitor(fohh_tlv, 0);

    if (fohh_tlv->fc_blocked_timestamp_initial) {
        if (epoch_us() - fohh_tlv->fc_blocked_timestamp_initial > FOHH_TLV_BLOCKED_RESET_US) {
            ZPN_LOG(AL_ERROR, "Reset connection to %s, as it is flow control blocked for more than %ld seconds",
                    fohh_description(f_conn), (long)FOHH_TLV_BLOCKED_RESET_US/1000000);
            /* Kill the connection */
            fohh_connection_disconnect(f_conn, FOHH_CLOSE_REASON_DATA_CONN_FLOW_CONTROL);
        }
    }
}

/* Handy for ignoring specific messages */
static int ignore_object_cb(void *argo_cookie_ptr,
                            void *argo_structure_cookie_ptr,
                            struct argo_object *object)
{
    return ZPATH_RESULT_NO_ERROR;
}

static int
zfc_conn_callback(struct fohh_connection *connection,
                  enum fohh_connection_state state,
                  void *cookie)
{
    struct zpn_fohh_client *zfc = cookie;
    int res = FOHH_RESULT_NO_ERROR;

    if (state == fohh_connection_connected) {
        struct argo_state *argo;

        zfc->fail_to_connect_timestamp_us = 0;

        argo = fohh_argo_get_rx(connection);

        /* Register zpn_version_ack */
        if ((res = argo_register_structure(argo, zpn_version_ack_description, zfc_version_ack_cb, zfc))) {
            ZPN_LOG(AL_ERROR, "Could not register zc_version_ack for zc_client for %s", ZFC_DBG(zfc));
            return res;
        }

        /* Register authentication callback, but only if not simple. */
        if (!zfc->simple_mode) {
            if (zfc->type == zpn_client_type_slogger) {
                if ((res = argo_register_structure(argo, zpn_trusted_client_authenticate_ack_description, zfc_trusted_client_authenticate_ack_cb, zfc))) {
                    ZPN_LOG(AL_ERROR, "Could not register zc_authenticate_ack for zc_client for %s", ZFC_DBG(zfc));
                    return res;
                }
            } else if (zfc->type == zpn_client_type_exporter || zfc->type == zpn_client_type_exporter_noauth) {
                if ((res = argo_register_structure(argo, zpn_exporter_client_authenticate_ack_description, zfc_exporter_client_authenticate_ack_cb, zfc))) {
                    ZPN_LOG(AL_ERROR, "Could not register zc_authenticate_ack for zc_client for %s", ZFC_DBG(zfc));
                    return res;
                }
            } else if (zfc->type == zpn_client_type_edge_connector) {
                /* Uses the same ack as private broker, since this is
                 * likely private broker calling on behalf of
                 * exporter... */
                if ((res = argo_register_structure(argo, zpn_ec_client_authenticate_ack_description, zfc_pbroker_client_authenticate_ack_cb, zfc))) {
                    ZPN_LOG(AL_ERROR, "Could not register zc_authenticate_ack for zc_client for %s", ZFC_DBG(zfc));
                    return res;
                }
            } else if (zfc->type == zpn_client_type_vdi) {
                if ((res = argo_register_structure(argo, zpn_vdi_client_authenticate_ack_description, zfc_pbroker_client_authenticate_ack_cb, zfc))) {
                    ZPN_LOG(AL_ERROR, "Could not register zc_authenticate_ack for zc_client for %s", ZFC_DBG(zfc));
                    return res;
                }
            } else if (zfc->type == zpn_client_type_machine_tunnel) {
                if ((res = argo_register_structure(argo, zpn_machine_tunnel_client_authenticate_ack_description, zfc_machine_tunnel_authenticate_ack_cb, zfc))) {
                    ZPN_LOG(AL_ERROR, "Could not register zc_authenticate_ack for zc_client for %s", ZFC_DBG(zfc));
                    return res;
                }
            } else {
                if ((res = argo_register_structure(argo, zpn_client_authenticate_ack_description, zfc_authenticate_ack_cb, zfc))) {
                    ZPN_LOG(AL_ERROR, "Could not register zc_authenticate_ack for zc_client for %s", ZFC_DBG(zfc));
                    return res;
                }
            }
        }

        /* Register zpn_mtunnel_tag_pause_request */
        if ((res = argo_register_structure(argo, zpn_mtunnel_tag_pause_description, zfc_mtunnel_tag_pause_cb, zfc))) {
            ZPN_LOG(AL_ERROR, "Could not register zpn_mtunnel_tag_pause for zc_client for %s", ZFC_DBG(zfc));
            return res;
        }

        /* Register zpn_mtunnel_tag_resume_request */
        if ((res = argo_register_structure(argo, zpn_mtunnel_tag_resume_description, zfc_mtunnel_tag_resume_cb, zfc))) {
            ZPN_LOG(AL_ERROR, "Could not register zpn_mtunnel_tag_resume for zc_client for %s", ZFC_DBG(zfc));
            return res;
        }

        /* Register zpn_fohh_tlv_window_update */
        if ((res = argo_register_structure(argo, zpn_fohh_tlv_window_update_description, zfc_client_fohh_tlv_window_update_cb, zfc))) {
            ZPN_LOG(AL_ERROR, "Could not register zpn_client_fohh_tlv_window_update");
            return res;
        }

        if ((res = argo_register_structure(argo, zpn_fohh_tlv_window_update_batch_description, zfc_client_fohh_tlv_window_update_batch_cb, zfc))) {
            ZPN_LOG(AL_ERROR, "Could not register zpn_client_fohh_tlv_window_update");
            return res;
        }

        if ((res = argo_register_structure(argo, zpn_client_search_domain_description, ignore_object_cb, NULL))) {
            ZPN_LOG(AL_ERROR, "Could not register to ignore zpn_client_search_domain");
            return res;
        }
        if ((res = argo_register_structure(argo, zpn_client_search_domain_complete_description, ignore_object_cb, NULL))) {
            ZPN_LOG(AL_ERROR, "Could not register to ignore zpn_client_search_domain_complete");
            return res;
        }
        if ((res = argo_register_structure(argo, zpn_posture_profile_ack_description, ignore_object_cb, NULL))) {
            ZPN_LOG(AL_ERROR, "Could not register to ignore zpn_posture_profile_ack");
            return res;
        }

        zpn_send_zpn_version(&(zfc->fohh_tlv_state.tlv), fohh_connection_incarnation(connection), ZPATH_VERSION_MAJOR, ZPATH_VERSION_MINOR);

        if (zfc->simple_mode) {
            zfc->status = zfc_ready;
        } else {
            zfc->status = zfc_authenticate;
        }

        if (zfc->status_cb) {
            (zfc->status_cb)(zfc, zfc->cookie_void, zfc->cookie_int, zfc->status, NULL);
        }

        if (!zfc->simple_mode) {
            if (zfc->type == zpn_client_type_slogger) {
                if (zpn_send_zpn_trusted_client_authenticate(connection,
                                                             fohh_connection_incarnation(connection),
                                                             0,
                                                             zfc->customer_id)) {
                    ZPN_LOG(AL_CRITICAL, "Failed to send authentication request for trusted client");
                    return ZPN_RESULT_ERR;
                }
            } else if (zfc->type == zpn_client_type_exporter || zfc->type == zpn_client_type_exporter_noauth) {
                if (zpn_send_zpn_exporter_client_authenticate(connection,
                                                              fohh_connection_incarnation(connection),
                                                              0,
                                                              zfc->customer_id,
                                                              zfc->saml_assertion, // can be null for noauth
                                                              &(zfc->public_ip),
                                                              &(zfc->private_ip),
                                                              zfc->pra_scope_gid,
                                                              zfc->is_pra_third_party_login)) {
                    ZPN_LOG(AL_CRITICAL, "Failed to send authentication request for trusted client");
                    return ZPN_RESULT_ERR;
                }
            } else if (zfc->type == zpn_client_type_edge_connector) {
                if (zpn_send_zpn_ec_client_authenticate(&(zfc->fohh_tlv_state.tlv),
                                                        fohh_connection_incarnation(connection),
                                                        0,
                                                        0,
                                                        0,
                                                        NULL,
                                                        NULL,
                                                        0,
                                                        &(zfc->private_ip),
                                                        zfc->platform)) {
                    ZPN_LOG(AL_CRITICAL, "Failed to send authentication request for pbroker client ec");
                    return ZPN_RESULT_ERR;
                }
            } else if (zfc->type == zpn_client_type_machine_tunnel) {
                if (zpn_send_zpn_machine_tunnel_client_authenticate(&(zfc->fohh_tlv_state.tlv),
                                                                   fohh_connection_incarnation(connection),
                                                                   0,
                                                                   zfc->hw_id,
                                                                   zfc->cpu_id,
                                                                   zfc->storage_id,
                                                                   &(zfc->private_ip),
                                                                   zfc->hostname,
                                                                   zfc->platform)) {
                     ZPN_LOG(AL_CRITICAL, "Failed to send authentication request");
                     return ZPN_RESULT_ERR;
                }
            } else {
                /* Use supplied auth request if avaliable */
                if (zfc->auth_obj && zfc->auth_obj_description) {
                    if (zpn_send_zpn_authenticate_obj(connection,
                                                      fohh_connection_incarnation(connection),
                                                      zfc->auth_obj,
                                                      zfc->auth_obj_description)) {
                        ZPN_LOG(AL_CRITICAL, "Failed to send authentication object request");
                        return ZPN_RESULT_ERR;
                    }
                } else {
                    if (zpn_send_zpn_client_authenticate(&(zfc->fohh_tlv_state.tlv),
                                                         fohh_connection_incarnation(connection),
                                                         0,
                                                         zfc->saml_assertion,
                                                         zfc->hw_id,
                                                         zfc->cpu_id,
                                                         zfc->storage_id,
                                                         zfc->login_name,
                                                         NULL,
                                                         NULL,
                                                         NULL,
                                                         NULL,
                                                         0,
                                                         zpn_client_conn_mode_active)) {
                        ZPN_LOG(AL_CRITICAL, "Failed to send authentication request");
                        return ZPN_RESULT_ERR;
                    }
                }
            }
        }

        zpn_fohh_tlv_set_conn(&(zfc->fohh_tlv_state), connection);

        zpn_mconn_fohh_tlv_add_monitor_timer(connection, &(zfc->fohh_tlv_state), zpn_fohh_client_conn_monitor_cb, FOHH_TIMER_CONN_MONITOR_S, 0);

        /* Send initial window update to our peer */
        zpn_send_zpn_fohh_window_update(connection,
                                        fohh_connection_incarnation(connection),
                                        0,
                                        flow_control_enabled ? fohh_tlv_window_size : 0,
                                        0);
        zfc->fohh_tlv_state.last_wnd_update_us = epoch_us();

    } else {
        ZPN_DEBUG_CLIENT("%s: ZPN Fohh client disconnected. get_local_port = %d, count = %d, fail timestamp = %ld, now = %ld",
                         ZFC_DBG(zfc), fohh_connection_get_local_port(zfc->conn), zfc->redirect_broker_count, (long) zfc->fail_to_connect_timestamp_us, (long)epoch_us());

        /* Remove monitor timer */
        zpn_mconn_fohh_tlv_remove_monitor_timer(&(zfc->fohh_tlv_state));

        zfc->status = zfc_not_connected;
        if (zfc->status_cb) {
            (zfc->status_cb)(zfc, zfc->cookie_void, zfc->cookie_int, zfc->status, NULL);
        }

        zpn_fohh_tlv_clear_state(&(zfc->fohh_tlv_state));
    }

    return res;
}

struct zpn_fohh_client *zpn_fohh_client_init(enum zpn_client_type type,
                                             enum zpn_tunnel_auth auth_type,
                                             struct argo_object *auth_obj,
                                             struct argo_structure_description *auth_obj_description,
                                             const char *broker_name,
                                             const char *customer_domain,
                                             const char *saml_assertion_xml,
                                             const char *cloud_root_pem_filename,
                                             const char *client_certificate_pem_filename,
                                             const char *client_key_pem_filename,
                                             EVP_PKEY *client_key_mem,
                                             zfc_status_callback_f callback,
                                             void *cookie_void,
                                             int64_t cookie_int,
                                             const char *hw_id,
                                             const char *cpu_id,
                                             const char *storage_id,
                                             const char *login_name,
                                             const char *hostname,
                                             const char *platform,
                                             const char *cloud_name,
                                             int64_t customer_id,
                                             const char *auth_token,
                                             struct argo_inet *public_ip,
                                             struct argo_inet *private_ip,
                                             int64_t pra_scope_gid,
                                             int is_pra_third_party_login)
{
    struct zpn_fohh_client *zfc = NULL;
    SSL_CTX *ssl_ctx = NULL;
    int res;

    if (type == zpn_client_type_slogger) {
        if (!broker_name ||
            !client_certificate_pem_filename ||
            !client_key_pem_filename ||
            !cloud_root_pem_filename ||
            !cloud_name ||
            !customer_id) return NULL;
    } else if ((type == zpn_client_type_exporter) || (type == zpn_client_type_exporter_noauth)) {
        if (!broker_name ||
            !client_certificate_pem_filename ||
            !client_key_pem_filename ||
            !cloud_root_pem_filename ||
            !cloud_name ||
            !auth_token) return NULL;
    } else if (type == zpn_client_type_edge_connector || type == zpn_client_type_vdi) {
        if (!broker_name ||
            !customer_domain ||
            !(client_key_pem_filename || client_key_mem) || // either pem file or key->rsa provided ?
            !cloud_root_pem_filename ||
            !cloud_name) return NULL;
    } else if (type == zpn_client_type_machine_tunnel) {
        if (!broker_name ||
            !customer_id ||
            !client_certificate_pem_filename ||
            !client_key_pem_filename ||
            !hw_id ||
            !cloud_root_pem_filename ||
            !cloud_name) return NULL;
    } else {
        /* All private broker trusted ZApp going to broker */
        if (auth_type == zpn_tunnel_auth_private_broker) {
            if (!broker_name ||
                !customer_domain ||
                !auth_obj ||
                !auth_obj_description ||
                !client_certificate_pem_filename ||
                !client_key_pem_filename ||
                !login_name ||
                !cloud_root_pem_filename ||
                !cloud_name) return NULL;
        } else {
            if (
                !broker_name ||
                !saml_assertion_xml ||
                !client_certificate_pem_filename ||
                !client_key_pem_filename ||
                !hw_id || !cpu_id || !storage_id || !login_name ||
                !cloud_root_pem_filename ||
                !cloud_name) return NULL;
        }
    }

    ssl_ctx = fohh_client_ssl_ctx_create(cloud_root_pem_filename,
                                         client_certificate_pem_filename,
                                         client_key_pem_filename,
                                         client_key_mem);
    if (!ssl_ctx) {
        ZPN_LOG(AL_ERROR, "Could not create SSL context");
        return NULL;
    }

    zfc = ZPN_CALLOC(sizeof(struct zpn_fohh_client));

    if (zfc) {
        char sni_customer_domain[256];

        zfc->type = type;
        zfc->lock = ZPATH_MUTEX_INIT;
        if (broker_name) {
            zfc->broker_dns_name = ZPN_STRDUP(broker_name, strlen(broker_name));
            ZPATH_MUTEX_LOCK(&(zfc->lock), __FILE__, __LINE__);
            snprintf(zfc->dbg_str, sizeof(zfc->dbg_str), "%s", broker_name);
            ZPATH_MUTEX_UNLOCK(&(zfc->lock), __FILE__, __LINE__);
        }
        zfc->status_cb = callback;
        zfc->cookie_int = cookie_int;
        zfc->cookie_void = cookie_void;
        zfc->status = zfc_not_connected;
        if (hw_id) zfc->hw_id = ZPN_STRDUP(hw_id, strlen(hw_id));
        if (cpu_id) zfc->cpu_id = ZPN_STRDUP(cpu_id, strlen(cpu_id));
        if (storage_id) zfc->storage_id = ZPN_STRDUP(storage_id, strlen(storage_id));
        if (login_name) zfc->login_name = ZPN_STRDUP(login_name, strlen(login_name));
        if (hostname) zfc->hostname = ZPN_STRDUP(hostname, strlen(hostname));
        if (platform) zfc->platform = ZPN_STRDUP(platform, strlen(platform));
        if (saml_assertion_xml) zfc->saml_assertion = ZPN_STRDUP(saml_assertion_xml, strlen(saml_assertion_xml));
        if (auth_token) zfc->auth_token = ZPN_STRDUP(auth_token, strlen(auth_token));

        if (auth_obj) {
            zfc->auth_obj = argo_object_copy(auth_obj);
        }
        if (auth_obj_description) {
            zfc->auth_obj_description = auth_obj_description;
        }

        zfc->customer_id = customer_id;
        zfc->lock = (zpath_mutex_t) ZPATH_MUTEX_INIT;

        if (public_ip) zfc->public_ip = (*public_ip);
        if (private_ip) zfc->private_ip = (*private_ip);
        if (pra_scope_gid) zfc->pra_scope_gid = pra_scope_gid;
        if (is_pra_third_party_login) zfc->is_pra_third_party_login = is_pra_third_party_login;

        if (type == zpn_client_type_slogger) {
            snprintf(zfc->sni_customer_domain, sizeof(sni_customer_domain), "%s.%s", "slogger", cloud_name);
        } else if (type == zpn_client_type_exporter) {
            snprintf(zfc->sni_customer_domain, sizeof(sni_customer_domain), "%s.%s", "exporter", cloud_name);
        } else if (type == zpn_client_type_exporter_noauth) {
            snprintf(zfc->sni_customer_domain, sizeof(sni_customer_domain), "%s.%s", "exporter_na", cloud_name);
        } else if (type == zpn_client_type_zapp) {
            if (auth_type == zpn_tunnel_auth_private_broker) {
                snprintf(zfc->sni_customer_domain, sizeof(zfc->sni_customer_domain), "%s.pbza.%s", customer_domain, cloud_name);
            } else if (auth_type == zpn_tunnel_auth_zapp) {
                snprintf(zfc->sni_customer_domain, sizeof(zfc->sni_customer_domain), "%s.c2.%s", customer_domain, cloud_name);
            } else {
                ZPN_LOG(AL_ERROR, "Unexpected auth type %d", auth_type);
                goto fail_free;
            }
        } else if (type == zpn_client_type_zapp_partner) {
            if (auth_type == zpn_tunnel_auth_private_broker) {
                snprintf(zfc->sni_customer_domain, sizeof(zfc->sni_customer_domain), "%s.pbzap.%s", customer_domain, cloud_name);
            } else if (auth_type == zpn_tunnel_auth_zapp_partner) {
                snprintf(zfc->sni_customer_domain, sizeof(zfc->sni_customer_domain), "%s.c2p.%s", customer_domain, cloud_name);
            } else {
                ZPN_LOG(AL_ERROR, "Unexpected auth type %d", auth_type);
                goto fail_free;
            }
        } else if (type == zpn_client_type_machine_tunnel) {
            if (auth_type == zpn_tunnel_auth_private_broker) {
                snprintf(zfc->sni_customer_domain, sizeof(zfc->sni_customer_domain), "%s.pbmt.%s", customer_domain, cloud_name);
            } else if (auth_type == zpn_tunnel_auth_machine_tunnel) {
                snprintf(zfc->sni_customer_domain, sizeof(zfc->sni_customer_domain), "%ld.c3.%s", (long) customer_id, cloud_name);
            } else {
                ZPN_LOG(AL_ERROR, "Unexpected auth type %d", auth_type);
                goto fail_free;
            }
        } else if (type == zpn_client_type_edge_connector) {
            if (auth_type == zpn_tunnel_auth_private_broker) {
                snprintf(zfc->sni_customer_domain, sizeof(zfc->sni_customer_domain), "%s.pbec.%s", customer_domain, cloud_name);
            } else if (auth_type == zpn_tunnel_auth_znf) {
                snprintf(zfc->sni_customer_domain, sizeof(zfc->sni_customer_domain), "%s.ec.%s", customer_domain, cloud_name);
            } else {
                ZPN_LOG(AL_ERROR, "Unexpected auth type %d", auth_type);
                goto fail_free;
            }
        } else if (type == zpn_client_type_vdi) {
            if (auth_type == zpn_tunnel_auth_znf) {
                snprintf(zfc->sni_customer_domain, sizeof(zfc->sni_customer_domain), "%s.ec_vdi.%s", customer_domain, cloud_name);
            } else if (auth_type == zpn_tunnel_auth_branch_connector) {
                snprintf(zfc->sni_customer_domain, sizeof(zfc->sni_customer_domain), "%s.bc_vdi.%s", customer_domain, cloud_name);
            } else {
                ZPN_LOG(AL_ERROR, "Unexpected auth type %d", auth_type);
                goto fail_free;
            }
        } else {
            snprintf(zfc->sni_customer_domain, sizeof(sni_customer_domain), "%ld.ec.%s", (long) customer_id, cloud_name);
        }
        ZPN_DEBUG_CONFIG("sni_customer_domain = %s", zfc->sni_customer_domain);

        zfc->conn = fohh_client_create_2(FOHH_WORKER_ZPN_ITASCA_CLIENT,
                                         zfc->broker_dns_name,
                                         argo_serialize_json_pretty,
                                         fohh_connection_style_argo_tlv,
                                         0,
                                         zfc,
                                         zfc_conn_callback,
                                         zpn_fohh_tlv_data_callback,
                                         zfc_unblock_callback,
                                         NULL,
                                         zfc->broker_dns_name,
                                         zfc->sni_customer_domain, // SNI
                                         NULL,
                                         htons(ZPN_CLIENT_BROKER_PORT),
                                         ssl_ctx,
                                         1,
                                         15*60,
                                         1); /* Ignore service prefix */
        if (!zfc->conn) {
            goto fail_free;
        }
        fohh_set_sticky(zfc->conn, 1);

        /* zfc is not reused, so set tlv_incarnation to 0 */
        res = zpn_fohh_tlv_init(&(zfc->fohh_tlv_state), zfc->conn, 0);
        if (res) {
            ZPN_LOG(AL_CRITICAL, "Could not init tlv: %s", zpn_result_string(res));
            goto fail_free;
        }

        zfc->next_id = 1;
        zfc->ssl_ctx = ssl_ctx;
        return zfc;
    }

fail_free:
    if (zfc) {
        int i;

        if (zfc->conn) fohh_connection_release(zfc->conn);
        if (zfc->broker_dns_name) ZPN_FREE(zfc->broker_dns_name);
        if (zfc->hw_id) ZPN_FREE(zfc->hw_id);
        if (zfc->cpu_id) ZPN_FREE(zfc->cpu_id);
        if (zfc->storage_id) ZPN_FREE(zfc->storage_id);
        if (zfc->login_name) ZPN_FREE(zfc->login_name);
        if (zfc->saml_assertion) ZPN_FREE(zfc->saml_assertion);
        if (zfc->auth_token) ZPN_FREE(zfc->auth_token);
        for (i = 0; i < ZPN_CLIENT_MAX_BROKERS; i++) {
            if (zfc->redirect_brokers[i]) ZPN_FREE(zfc->redirect_brokers[i]);
        }
        ZPN_FREE(zfc);
        if (ssl_ctx) SSL_CTX_free(ssl_ctx);
    }

    return NULL;
}

struct zpn_fohh_client *zpn_fohh_client_simple_init(const char *broker_name,
                                                    const char *sni,
                                                    const char *sni_suffix,
                                                    SSL_CTX *ssl_ctx,
                                                    zfc_status_callback_f callback,
                                                    void *cookie_void,
                                                    int64_t cookie_int)
{
    struct zpn_fohh_client *zfc = NULL;
    int res;

    zfc = ZPN_CALLOC(sizeof(struct zpn_fohh_client));

    if (zfc) {
        zfc->simple_mode = 1;
        zfc->lock = ZPATH_MUTEX_INIT;
        if (broker_name) {
            zfc->broker_dns_name = ZPN_STRDUP(broker_name, strlen(broker_name));
            ZPATH_MUTEX_LOCK(&(zfc->lock), __FILE__, __LINE__);
            snprintf(zfc->dbg_str, sizeof(zfc->dbg_str), "%s", broker_name);
            ZPATH_MUTEX_UNLOCK(&(zfc->lock), __FILE__, __LINE__);
        }
        zfc->status_cb = callback;
        zfc->cookie_int = cookie_int;
        zfc->cookie_void = cookie_void;
        zfc->status = zfc_not_connected;

        snprintf(zfc->sni_customer_domain, sizeof(zfc->sni_customer_domain), "%s", sni);
        ZPN_DEBUG_CONFIG("sni_customer_domain = %s", zfc->sni_customer_domain);

        zfc->conn = fohh_client_create_2(FOHH_WORKER_ZPN_ITASCA_CLIENT,
                                         zfc->broker_dns_name,
                                         argo_serialize_binary,
                                         fohh_connection_style_argo_tlv,
                                         0,
                                         zfc,
                                         zfc_conn_callback,
                                         zpn_fohh_tlv_data_callback,
                                         zfc_unblock_callback,
                                         NULL,
                                         zfc->broker_dns_name,
                                         zfc->sni_customer_domain, // SNI
                                         sni_suffix,
                                         htons(ZPN_CLIENT_BROKER_PORT),
                                         ssl_ctx,
                                         1,
                                         15*60,
                                         1); /* Ignore service prefix */
        if (!zfc->conn) {
            goto fail_free;
        }
        fohh_set_sticky(zfc->conn, 1);

        /* zfc is not reused, so set tlv_incarnation to 0 */
        res = zpn_fohh_tlv_init(&(zfc->fohh_tlv_state), zfc->conn, 0);
        if (res) {
            ZPN_LOG(AL_CRITICAL, "Could not init tlv: %s", zpn_result_string(res));
            goto fail_free;
        }

        zfc->next_id = 1;

        return zfc;
    }

fail_free:
    if (zfc) {
        int i;

        if (zfc->conn) fohh_connection_release(zfc->conn);
        if (zfc->broker_dns_name) ZPN_FREE(zfc->broker_dns_name);
        if (zfc->hw_id) ZPN_FREE(zfc->hw_id);
        if (zfc->cpu_id) ZPN_FREE(zfc->cpu_id);
        if (zfc->storage_id) ZPN_FREE(zfc->storage_id);
        if (zfc->login_name) ZPN_FREE(zfc->login_name);
        if (zfc->saml_assertion) ZPN_FREE(zfc->saml_assertion);
        if (zfc->auth_token) ZPN_FREE(zfc->auth_token);
        for (i = 0; i < ZPN_CLIENT_MAX_BROKERS; i++) {
            if (zfc->redirect_brokers[i]) ZPN_FREE(zfc->redirect_brokers[i]);
        }
        ZPN_FREE(zfc);
    }
    return NULL;
}


static void defer_zfc_free(void *cookie1, void *cookie2)
{
    struct zpn_fohh_client *zfc = cookie1;
    if (zfc) {
        ZPN_FREE(zfc);
        zfc = NULL;
    }
}

void zpn_fohh_client_fini(struct zpn_fohh_client *zfc)
{
    if (zfc) {
        int i;

        ZPATH_MUTEX_LOCK(&(zfc->lock), __FILE__, __LINE__);
        zpn_fohh_tlv_destroy(&(zfc->fohh_tlv_state), NULL);

        struct fohh_connection *conn = zfc->conn;
        zfc->conn = NULL;
        /* Safe to unlock here to avoid deadlock with fohh_connection lock,
        then re-lock since we still own zfc and need to free other fields. */
        ZPATH_MUTEX_UNLOCK(&(zfc->lock), __FILE__, __LINE__);
        if (conn) {
            fohh_connection_set_dynamic_cookie(conn, NULL);
            fohh_connection_release(conn);
        }
        ZPATH_MUTEX_LOCK(&(zfc->lock), __FILE__, __LINE__);

        if (zfc->broker_dns_name) {
            ZPN_FREE(zfc->broker_dns_name);
            zfc->broker_dns_name = NULL;
        }
        if (zfc->tunnel_id) {
            ZPN_FREE(zfc->tunnel_id);
            zfc->tunnel_id = NULL;
        }
        if (zfc->hw_id) {
            ZPN_FREE(zfc->hw_id);
            zfc->hw_id = NULL;
        }
        if (zfc->cpu_id) {
            ZPN_FREE(zfc->cpu_id);
            zfc->cpu_id = NULL;
        }
        if (zfc->storage_id) {
            ZPN_FREE(zfc->storage_id);
            zfc->storage_id = NULL;
        }
        if (zfc->login_name) {
            ZPN_FREE(zfc->login_name);
            zfc->login_name = NULL;
        }
        if (zfc->saml_assertion) {
            ZPN_FREE(zfc->saml_assertion);
            zfc->saml_assertion = NULL;
        }
        if (zfc->auth_token) {
            ZPN_FREE(zfc->auth_token);
            zfc->auth_token = NULL;
        }
        for (i = 0; i < ZPN_CLIENT_MAX_BROKERS; i++) {
            if (zfc->redirect_brokers[i]) {
                ZPN_FREE(zfc->redirect_brokers[i]);
                zfc->redirect_brokers[i] = NULL;
            }
        }
        if (zfc->ssl_ctx) {
            SSL_CTX_free(zfc->ssl_ctx);
            zfc->ssl_ctx = NULL;
        }
        ZPATH_MUTEX_UNLOCK(&(zfc->lock), __FILE__, __LINE__);
        zevent_defer(defer_zfc_free, zfc, NULL, (60 * 1000 * 1000)); // Defer zfc free by 60 seconds
        zfc = NULL;
    }
}

void zpn_fohh_client_get_description(struct zpn_fohh_client *zfc, char *desc, size_t desc_len)
{
    char *s = desc;
    char *e = desc + desc_len;

    if (!zfc) {
        s += sxprintf(s, e, "No client");
    } else {
        if (zfc->conn) {
            s += sxprintf(s, e, "f_conn=%s ", fohh_description(zfc->conn));
        } else {
            s += sxprintf(s, e, "f_conn=NULL ");
        }
        if (zfc->tunnel_id) {
            s += sxprintf(s, e, "tunnel=%s ", zfc->tunnel_id);
        } else {
            s += sxprintf(s, e, "tunnel=NONE ");
        }
        s += sxprintf(s, e, "status=%s", zfc_status_string(zfc->status));
    }
}



int zpn_fohh_client_attach_mconn(struct zpn_fohh_client *zfc,
                                 struct zpn_mconn_fohh_tlv *mconn_fohh_tlv,
                                 void *local_owner_key,
                                 size_t local_owner_key_length)
{
    int res;

    zpn_mconn_set_fohh_thread_id(&(mconn_fohh_tlv->mconn), fohh_connection_get_thread_id(zfc->conn));

    res = zpn_mconn_add_local_owner(&(mconn_fohh_tlv->mconn),
                                    0,
                                    &(zfc->fohh_tlv_state),
                                    local_owner_key,
                                    local_owner_key_length,
                                    &zpn_mconn_fohh_tlv_calls);
    if (res) {
        ZPN_LOG(AL_CRITICAL, "Init local owner: %s", zpn_result_string(res));
        return res;
    }

    return FOHH_RESULT_NO_ERROR;
}

int zpn_fohh_client_detach_mconn(struct zpn_mconn_fohh_tlv *mconn_fohh_tlv,
                                 int drop_buffered_data,
                                 int dont_propagate_data,
                                 const char *err)
{
    int res;

    res = zpn_mconn_remove_local_owner(&(mconn_fohh_tlv->mconn),
                                       0,
                                       drop_buffered_data,
                                       dont_propagate_data,
                                       err);
    if (res) {
        ZPN_LOG(AL_CRITICAL, "Could not remove local owner: %s", zpn_result_string(res));
        return res;
    }

    return FOHH_RESULT_NO_ERROR;
}

int32_t zpn_fohh_client_next_tag_id(struct zpn_fohh_client *zfc)
{
    return __sync_fetch_and_add_4(&(zfc->next_id), 1);
}

struct fohh_connection *zpn_fohh_client_get_f_conn(struct zpn_fohh_client *zfc)
{
    return zfc->conn;
}

void zpn_fohh_client_get_local_address(struct zpn_fohh_client *zfc, struct argo_inet *inet)
{
    fohh_connection_address(zfc->conn, NULL, inet);
}
