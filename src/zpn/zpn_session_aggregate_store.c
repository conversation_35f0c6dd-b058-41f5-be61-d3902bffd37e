/*
 * zpn_session_aggregate_store.c. Copyright (C) 2019 Zscaler Inc. All Rights Reserved
 */

#include "zpath_lib/zpath_lib.h"
#include "zpn/zpn_lib.h"
#include "zpath_misc/zpath_misc.h"

#include <ctype.h>
#include "zpath_lib/zpath_debug.h"

#include "zpn/zpn_session_aggregate_store.h"
#include "zpn/zpn_session_aggregate_store_compiled.h"

// if we ever change the underlying data structure
// bump this version so that we don't accidentally pick up incompatible values
#define AGGREGATE_STORE_VERSION_NAMESPACE 3

struct {
    //TODO(blewis) this stat can go away when object store inconsistency is fixed
    int64_t inconsistent_occurrence_count;
} internal_stats;

static struct argo_structure_description *zpn_session_aggregate_store_keyed_value_description;
static struct argo_structure_description *zpn_session_aggregate_store_collection_description;

struct argo_object *zpn_session_aggregate_store_collection_create(const char *user_id) {
    struct zpn_session_aggregate_store_collection coll;

    memset(&coll, 0, sizeof(coll));
    coll.user_id = user_id;
    return argo_object_create(zpn_session_aggregate_store_collection_description, &coll);
}

/* A bit expensive but that's okay */
int zpn_session_aggregate_store_add_key_value_to_collection(struct argo_object **coll_object,
                                                            const char *key,
                                                            struct argo_object *value_object,
                                                            const char *dbg_str) {
    struct zpn_session_aggregate_store_collection collection;
    struct argo_object *new_aggregate_collection_object;
    struct argo_object *old_aggregate_collection_object = *coll_object;
    struct zpn_session_aggregate_store_collection *old_aggregate_collection;

    INLINE_VOID_INFERENCE(old_aggregate_collection, old_aggregate_collection_object->base_structure_void);

    int replaced = 0;
    int i;
    for (i = 0; i < old_aggregate_collection->keyed_values_size; i++) {
        struct zpn_session_aggregate_store_keyed_value *cur_pos;
        INLINE_VOID_INFERENCE(cur_pos, old_aggregate_collection->keyed_values[i]->base_structure_void);
        if (strcmp(key, cur_pos->key) == 0) {
            replaced = 1;
            argo_object_release(cur_pos->value);
            cur_pos->value = value_object;
            argo_object_hold(value_object);
        }
    }

    if (!replaced) {
        /* Not found, so add */
        struct zpn_session_aggregate_store_keyed_value new_keyed_value;
        new_keyed_value.key = key;
        new_keyed_value.value = value_object;
        struct argo_object *new_keyed_value_object = argo_object_create(zpn_session_aggregate_store_keyed_value_description, &new_keyed_value);
        if (!new_keyed_value_object) {
            ZPN_LOG(AL_ERROR, "%s: could not create new keyed value for aggregate storage - out of memory", dbg_str);
            return ZPATH_RESULT_NO_MEMORY;
        }

        memset(&collection, 0, sizeof(collection));
        collection.user_id = old_aggregate_collection->user_id;
        collection.keyed_values =
                ZPN_MALLOC(sizeof(collection.keyed_values[0]) * (old_aggregate_collection->keyed_values_size + 1));
        if (!collection.keyed_values) {
            ZPN_LOG(AL_ERROR,
                    "%s: could not allocate new aggregate collection for aggregate storage - out of memory",
                    dbg_str);
            return ZPN_RESULT_NO_MEMORY;
        }
        memcpy(collection.keyed_values,
               old_aggregate_collection->keyed_values,
               sizeof(collection.keyed_values[0]) * old_aggregate_collection->keyed_values_size);
        collection.keyed_values[old_aggregate_collection->keyed_values_size] = new_keyed_value_object;
        collection.keyed_values_size = old_aggregate_collection->keyed_values_size + 1;

        new_aggregate_collection_object =
                argo_object_create(zpn_session_aggregate_store_collection_description, &collection);
        ZPN_FREE(collection.keyed_values);
        argo_object_release(new_keyed_value_object);
        if (!new_aggregate_collection_object) {
            ZPN_LOG(AL_ERROR,
                    "%s: could not create new aggregate collection for aggregate storage - out of memory",
                    dbg_str);
            return ZPATH_RESULT_ERR;
        }
        (*coll_object) = new_aggregate_collection_object;
        argo_object_release(old_aggregate_collection_object);
    }

    return ZPATH_RESULT_NO_ERROR;
}

int zpn_session_aggregate_store_remove_key_value_from_collection(struct argo_object *coll_object,
                                                                 const char *key,
                                                                 int *new_size,
                                                                 const char *dbg_str) {
    //initialize outputs
    (*new_size) = 0;

    struct zpn_session_aggregate_store_collection *coll;
    INLINE_VOID_INFERENCE(coll, coll_object->base_structure_void);

    if (coll->keyed_values_size == 0) {
        ZPN_LOG(AL_DEBUG, "%s: empty aggregate collection nothing to remove", dbg_str);
        return ZPN_RESULT_NO_ERROR;
    }

    int working_size = coll->keyed_values_size;
    int found = 0;
    int i;

    for (i = 0; i < coll->keyed_values_size; i++) {
        struct zpn_session_aggregate_store_keyed_value *cur_pos;
        INLINE_VOID_INFERENCE(cur_pos, coll->keyed_values[i]->base_structure_void);
        if (found) {
            //move object down to previous index
            coll->keyed_values[i-1] = coll->keyed_values[i];
            coll->keyed_values[i] = NULL;
        } else if (strcmp(key, cur_pos->key) == 0) {
            found = 1;
            //cleanup the object
            ZPN_LOG(AL_DEBUG, "%s: removing key value pair at %s", dbg_str, key);
            argo_object_release(coll->keyed_values[i]);
            coll->keyed_values[i] = NULL;
            working_size--;
        }
    }

    if (!found) {
        ZPN_LOG(AL_DEBUG, "%s: key was not in aggregate collection so not removed", dbg_str);
    }

    coll->keyed_values_size = working_size;
    (*new_size) = coll->keyed_values_size;
    return ZPN_RESULT_NO_ERROR;
}

int zpn_session_aggregate_store_write(const char *dbg_str,
                                const char *key_str,
                                struct argo_object *aggregate_coll,
                                object_store_callback_f *callback,
                                void *callback_data,
                                int64_t callback_int)
{

    //TODO(blewis) once argo can handle larger objects make this accommodate that
    char buf[ARGO_BUF_DEFAULT_SIZE];
    size_t written;
    int res;

    res = argo_object_dump(aggregate_coll, buf, sizeof(buf), &written, 0);
    if (res) {
        ZPN_LOG(AL_ERROR, "%s: postures failed to serialize buff_max_size:%zu - %s", dbg_str, sizeof(buf), zpath_result_string(res));
        return res;
    }

    if (written >= sizeof(buf)) {
        ZPN_LOG(AL_ERROR, "%s: aggregate coll too large - written:%zu max:%zu", dbg_str, written, sizeof(buf));
        return ZPATH_RESULT_ERR;
    }

    return object_store_set(dbg_str, key_str, buf, callback, callback_data, callback_int, ostore_role_object_store);
}

int zpn_session_aggregate_store_get(const char *dbg_str,
                              const char *key,
                              struct argo_object **aggregate_coll,
                              object_store_callback_f *callback,
                              void *callback_data,
                              int64_t callback_int)
{
    struct argo_object *object;
    const char *object_value;
    int res;

    res = object_store_get(dbg_str, key, &object_value, callback, callback_data, callback_int, ostore_role_object_store);

    if (res) {
        return res;
    }

    //TODO(blewis) once object store race condition is resolved remove this
    //DO NOT remove without checking brokers with Ops - learn from my mistakes
    //Sincerely, Barret :)
    if (res == ZPATH_RESULT_NO_ERROR && object_value == NULL) {
        ZPN_LOG(AL_ERROR, "Object store returned inconsistent result - monitor - object_store key:%s", key);
        __sync_add_and_fetch_8(&(internal_stats.inconsistent_occurrence_count), 1);
        return ZPATH_RESULT_NOT_FOUND;
    }

    object = argo_deserialize_json(object_value, strlen(object_value));
    if (!object) {
        ZPN_LOG(AL_WARNING, "%s: Failed to deserialize postures stored in the object store, removing value from the object_store at key:%s", dbg_str, key);
        //explain this
        //On deserialization failure a bad value has made it into the object store
        //since there is no way to 'fix up' a broken value we will issue a delete
        //We will then procede as if the value wasn't found - this is consistent with
        //behaving as if the delete already succeeded
        // - We allowed  write larger than 2048 characters for example
        //   despite the underlying json being valid we cannot actually deserialize it
        res = object_store_delete(dbg_str, key, NULL, NULL, 0, ostore_role_object_store);
        if (res != ZPN_RESULT_NO_ERROR && res != ZPN_RESULT_ASYNCHRONOUS) {
            ZPN_LOG(AL_WARNING, "%s: Failed to remove postures from the object_store, key %s, err %s",
                dbg_str, key, zpath_result_string(res));
        }
        return ZPN_RESULT_NOT_FOUND;
    }

    if (zpn_session_aggregate_store_collection_description == argo_get_object_description(object)) {
        (*aggregate_coll) = object;
        return ZPN_RESULT_NO_ERROR;
    }

    ZPN_LOG(AL_WARNING, "%s: deserialized aggregate collection object was not of correct type", dbg_str);
    argo_object_release(object);
    return ZPN_RESULT_ERR;
}

int zpn_session_aggregate_store_collection_delete(const char *dbg_str,
                                            const char *key_str,
                                            object_store_callback_f *callback,
                                            void *callback_data,
                                            int64_t callback_int)
{
    int res;

    res = object_store_delete(dbg_str, key_str, callback, callback_data, callback_int, ostore_role_object_store);
    if (res != ZPN_RESULT_NO_ERROR && res != ZPN_RESULT_ASYNCHRONOUS) {
        ZPN_LOG(AL_WARNING, "%s: failed to remove aggregate collection from the object_store - %s", dbg_str, zpath_result_string(res));
       return res;
    }

    //Ignore async since we aren't dependant on this we are just trying to keep object store as up to date as possible
    return ZPN_RESULT_NO_ERROR;
}

int zpn_session_aggregate_store_is_keyed_value(struct argo_object *object) {
    return zpn_session_aggregate_store_keyed_value_description == argo_get_object_description(object);
}

int zpn_session_aggregate_store_gen_key(const char *storage_type, int64_t customer_gid, int64_t idp_gid, char *user_id, const char* infer_key, char *key, size_t key_len) {
    char identifier[256];

    /* Not all IDPs seem to normalize the username as expected so we will do that ourselves */
    char *normalized_user_id = ZPN_STRDUP(user_id, strlen(user_id));
    if (!normalized_user_id) {
        ZPN_LOG(AL_ERROR, "%s: could not generate key for storing aggregate collection - out of memory", user_id);
        return ZPN_RESULT_ERR;
    }

    size_t i;
    for(i = 0; normalized_user_id[i]; ++i){
        normalized_user_id[i] = tolower(normalized_user_id[i]);
    }

    sxprintf(identifier, identifier+sizeof(identifier), "%i|%ld|%ld|%s", AGGREGATE_STORE_VERSION_NAMESPACE, (long) customer_gid, (long) idp_gid, normalized_user_id);

    ZPN_FREE(normalized_user_id);

    int res = object_store_gen_constant_key(storage_type, infer_key, identifier, key, key_len, ostore_role_object_store);
    if (res) {
        ZPN_LOG(AL_ERROR, "%s: could not generate key for storing aggregate collection", user_id);
        return ZPN_RESULT_ERR;
    }

    ZPN_DEBUG_CLIENT("%ld: generated aggregate collection key [%s] for user %s", (long)customer_gid, key, user_id);
    return ZPN_RESULT_NO_ERROR;
}

static int zpn_session_aggregate_store_stats_get(struct zpath_debug_state *request_state,
                                                 const char **query_values,
                                                 int query_value_count,
                                                 void *object) {
    char *help_str = "Valid Stats:\ninconsistent_count";

    if (!query_values[0]) {
        ZDP("Missing argument: stat\n");
        ZDP("%s\n", help_str);
    } else {
        if (strcasecmp(query_values[0], "inconsistent_count") == 0) {
            ZDP("inconsistent_count: %" PRId64 "\n", internal_stats.inconsistent_occurrence_count);
        } else {
            ZDP("%s\n", help_str);
        }
    }

    return ZPATH_RESULT_NO_ERROR;
}

int zpn_session_aggregate_store_init(void) {
    if (!((zpn_session_aggregate_store_keyed_value_description = argo_register_global_structure(ZPN_SESSION_AGGREGATE_STORE_KEYED_VALUE_HELPER)))) return ZPATH_RESULT_ERR;
    if (!((zpn_session_aggregate_store_collection_description = argo_register_global_structure(ZPN_SESSION_AGGREGATE_STORE_COLLECTION_HELPER)))) return ZPATH_RESULT_ERR;

    int res;

    res = zpath_debug_add_read_command("Get Stats tracked by aggregate store [inconsistent_count]",
                                  "/aggregate_store/stats/get",
                                  zpn_session_aggregate_store_stats_get,
                                  NULL,
                                  "stat", "the stat being checked",
                                  NULL);
    if (res) {
        ZPN_LOG(AL_ERROR, "Error registering aggregate store stats endpoint");
        return res;
    }

    return ZPATH_RESULT_NO_ERROR;
}
