/*
 * zpn_pbroker_group.h. Copyright (C) 2019 Zscaler Inc, All Rights Reserved
 */

#ifndef _ZPN_PBROKER_GROUP_H_
#define _ZPN_PBROKER_GROUP_H_

#include "argo/argo.h"

struct zpn_private_broker_group         /* _ARGO: object_definition */
{
    /* Standard SQL fields. */
    int64_t sequence;                          /* _ARGO: integer, sequence */
    int32_t modified_time;                     /* _ARGO: integer */
    int32_t creation_time;                     /* _ARGO: integer */
    int64_t modifiedby_userid;                 /* _ARGO: integer */
    int8_t deleted;                            /* _ARGO: integer, deleted */
    int64_t request_id;                        /* _ARGO: integer, nodb, reqid */

    int64_t gid;                               /* _ARGO: integer, index, key */
    int64_t customer_gid;                      /* _ARGO: integer, index */
    int64_t scope_gid;                         /* _ARGO: integer, index */
    int64_t site_gid;                          /* _ARGO: integer, index */
    char *name;                                /* _ARGO: string */
    int enabled;                               /* _ARGO: integer */
    char *description;                         /* _ARGO: string */

    int64_t geolocation_gid;                   /* _ARGO: integer */
    int upgrade_day;                           /* _ARGO: integer */
    int upgrade_time;                          /* _ARGO: integer */
    char *is_public;                           /* _ARGO: string */
    int64_t version_profile_gid;               /* _ARGO: integer */
    char *location;                            /* _ARGO: string */
    double latitude;                           /* _ARGO: double */
    double longitude;                          /* _ARGO: double */
    char *country_code;                        /* _ARGO: string */
	int override_cst_level_version_profile;    /* _ARGO: integer */
    int use_in_dr_mode;                        /* _ARGO: integer */
    int grace_distance_enabled;                /* _ARGO: integer */
    double grace_distance_value;               /* _ARGO: double */
    char *grace_distance_value_unit;           /* _ARGO: string */
    double calc_grace_distance_value_miles;    /* _ARGO: double, nodb */
    char *alt_cloud;                           /* _ARGO: string */
    int exclusive_for_business_continuity;     /* _ARGO: integer */
};

extern struct argo_structure_description *zpn_pbroker_group_description;

typedef void zpn_pbroker_group_callback_f(int64_t gid, char *name, char *is_public, double lat, double lon);
int zpn_pbroker_group_init(struct wally *single_tenant_wally,
                           int64_t single_tenant_gid,
                           wally_row_callback_f *row_cb,
                           int single_tenant_fully_loaded,
                           int register_with_zpath_table);

int zpn_pbroker_group_get_by_gid(int64_t gid,
                                 struct zpn_private_broker_group **group,
				 int register_on_miss,
                                 wally_response_callback_f callback_f,
                                 void *callback_cookie,
                                 int64_t callback_id);

int zpn_pbroker_group_get_by_customer_gid(int64_t customer_gid,
                                          struct zpn_private_broker_group **group,
                                          wally_response_callback_f callback_f,
                                          void *callback_cookie,
                                          int64_t callback_id);

void zpn_pbroker_group_release(struct zpn_private_broker_group *group);

int zpn_pbroker_group_load(int64_t customer_gid,
                           wally_response_callback_f callback_f,
                           void *callback_cookie,
                           int64_t callback_id);

void argo_zpn_private_broker_group_init();

int zpn_pbroker_set_pse_grp_exclusive_for_business_continuity_flag(int64_t pse_grp_gid, int flag);
int zpn_pbroker_pse_grp_exclusive_for_business_continuity_flag_is_set(int64_t pse_grp_gid);
void zpn_pbroker_debug_pse_groups_exclusive_for_business_continuity_hash_init();

#endif /* _ZPN_PBROKER_GROUP_H_ */
