/*
 * zpn_policy_engine_build.h. Copyright (C) 2019 Zscaler, Inc. All Rights Reserved
 */

#ifndef __ZPN_POLICY_ENGINE_BUILD_H__
#define __ZPN_POLICY_ENGINE_BUILD_H__

#include "zpn/zpn_policy_engine.h"

/* stats logging for policy build time
* num_build_time_gt_2s : 2sec < build time
* num_build_time_gt_5s : 5sec < build time <= 10sec
* num_build_time_gt_10s : 10sec < build time
*/
struct zpe_policy_build_stats {        /* _ARGO: object_definition */
    int64_t num_total_builds;          /* _ARGO: integer */
    int64_t max_build_time_us;         /* _ARGO: integer */
    int64_t avg_build_time_us;         /* _ARGO: integer */
    int64_t total_build_time_us;       /* _ARGO: quiet, integer */
    int64_t num_total_failures;        /* _ARGO: integer */
    int64_t saml_not_found_cnt;        /* _ARGO: integer */
    int64_t scim_not_found_cnt;        /* _ARGO: integer */
    int64_t num_total_failures_long;   /* _ARGO: integer */
    int64_t num_build_time_gt_2s;      /* _ARGO: integer */
    int64_t num_build_time_gt_5s;      /* _ARGO: integer */
    int64_t num_build_time_gt_10s;     /* _ARGO: integer */
    int64_t num_policy_gt_15k;         /* _ARGO: integer */
    int64_t num_policy_gt_10k;         /* _ARGO: integer */
    int64_t num_policy_gt_7k;          /* _ARGO: integer */
    int64_t num_policy_gt_5k;          /* _ARGO: integer */
};

int zpe_generate_set_from_config(int64_t customer_gid,
                                 enum zpe_policy_type policy_type,
                                 struct zhash_table *grp_to_apps,
                                 int *async_count,
                                 struct zpe_policy_set **ret_set,
                                 struct zpe_policy_set_meta **ret_set_meta,
                                 struct zhash_table **app_to_rules,
                                 struct zpe_mini_policy_set **ret_rules_for_all,
                                 wally_response_callback_f *callback,
                                 void *void_cookie,
                                 int64_t int_cookie);

void zpe_free_mini_policy_set(struct zpe_mini_policy_set *m_policy);
void zpe_free_mini_policy_set_slow(struct zpe_mini_policy_set *m_policy);
void zpe_free_app_to_rules(struct zhash_table *app_to_rules);
void zpe_free_app_to_rules_slow(struct zhash_table *app_to_rules);

extern struct zpe_policy_build_stats policy_build_stats[ZPE_POLICY_TYPE_COUNT][ZTHREAD_MAX_THREADS];

#endif /* __ZPN_POLICY_ENGINE_BUILD_H__ */
