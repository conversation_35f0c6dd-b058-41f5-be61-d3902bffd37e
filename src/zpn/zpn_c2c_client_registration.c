/*
 * zpn_c2c_client_registration.c. Copyright (C) 2021 Zscaler Inc. All Rights Reserved.
 */

#include "zpath_lib/zpath_app.h"
#include "wally/wally_private.h"
#include "zpath_lib/zpath_debug.h"
#include <regex.h>
#include "zpn/zpn_customer_resiliency_settings.h"
#include "zpn/zpn_broker_client.h"
#include "zpn/zpn_broker.h"
#include "zpn/zpn_lib.h"
#include "zpath_lib/zpath_cloud.h"
#include "zpn/zpn_policy_engine.h"
#include "zpath_lib/zpath_config_override.h"
#include "zpath_lib/zpath_config_override_desc.h"
#include "zpn/zpn_c2c_client_registration.h"
#include "zpn/zpn_c2c_client_registration_compiled.h"
#include "zpn/zpn_broker_private.h"
#include "zpn/zpn_broker_dispatch.h"
#include "zpn/zpn_private_broker_util.h"
#include "zpn/zpn_broker_transit.h"
#include "zpn/zpn_pb_client.h"
#include "zpath_lib/zpath_config_override_keys.h"
#include "zpn/zpn_broker_private.h"
#include "zpath_misc/zpath_dta_checkpoint.h"

/*
 * Read/Write lock wrappers for c2c regex cache lock -
 */
#define zpn_c2c_regex_read_lock()     ZPATH_RWLOCK_RDLOCK(&(g_c2c_compiled_regex_cache_rw_lock), __FILE__, __LINE__)
#define zpn_c2c_regex_write_lock()    ZPATH_RWLOCK_WRLOCK(&(g_c2c_compiled_regex_cache_rw_lock), __FILE__, __LINE__)
#define zpn_c2c_regex_unlock()        ZPATH_RWLOCK_UNLOCK(&(g_c2c_compiled_regex_cache_rw_lock), __FILE__, __LINE__)

struct argo_structure_description *zpn_c2c_client_registration_description = NULL;

// used for integration tests
u_int global_c2c_debug_flags = 0;

struct customer_load_state {
    int done;
    zpath_mutex_t lock;
    struct wally_callback_queue *queue;
};

static void zpn_c2c_client_registration_row_fixup(struct argo_object *row)
{
    struct zpn_c2c_client_registration *reg = row->base_structure_void;
    if (reg->scope_gid == 0) {
        reg->scope_gid = reg->customer_gid;
    }
}

int zpn_broker_client_state_init(struct zpn_broker_client_fohh_state *c_state,
                                 enum zpn_client_type client_type,
                                 enum zpn_tunnel_auth auth_type,
                                 enum zpn_tlv_type tlv_type);

static struct wally_index_column **zpn_c2c_gid_index = NULL;
static struct zhash_table *customer_table = NULL;
static zpath_mutex_t hash_lock;

/* Single customer cache for compiled regex for use on PSE */

static struct zpn_c2c_regex_cache_entry g_c2c_compiled_regex_cache = { 0 };
static zpath_rwlock_t g_c2c_compiled_regex_cache_rw_lock = { 0 };

/* C2C bypass regex eval stats */
static int64_t g_c2c_bypass_eval_total_time_us = 0;
static int64_t g_c2c_bypass_eval_total_calls = 0;


static int zpn_broker_c2c_regex_customer_cache_get(int64_t customer_gid,  int *init_done, regex_t **compiled_re);
static int zpn_broker_c2c_regex_customer_cache_update(int64_t customer_gid, regex_t *compiled_re);
static int zpn_broker_c2c_regex_customer_cache_is_initilized(int64_t customer_gid);
static int zpn_broker_c2c_regex_customer_cache_clear(int64_t customer_gid);

/*
 * C2C FQDN Override flags declarations
 */


static struct zpath_config_override_desc zpn_c2c_descriptions_common[] = {
    {
        .key                = C2C_FEATURE_CUSTOMER,
        .desc               = "enable/disable C2C for customer",
        .details            = "0: Disabled\n"
                              "1: Enabled\n"
                              "Order of check : customer id, global\n"
                              "default: 1 - C2C customer is enabled",
        .val_type           = config_type_int,
        .value_traits       = config_value_traits_feature_enablement,
        .component_types    = config_component_broker | config_component_pbroker,
        .target_gid_types   = config_target_gid_type_cust | config_target_gid_type_global,
        .int_range_lo       = 0,
        .int_range_hi       = 1,
        .int_default        = C2C_DEFAULT_CUSTOMER_FEATURE,
        .feature_group      = FEATURE_GROUP_CLIENT_TO_CLIENT,
    },
    {
        .key                = C2C_BYPASS_LOCAL_DISPATCH_PHASE2,
        .desc               = "enable/disable feature pse local dispatcher bypass feature phase-2",
        .details            = "Order of check - : broker id, customer id, global\n"
                              "default: 0 (i.e. disabled), allowed values are 0 = Disabled, 1 = Enabled",
        .val_type           = config_type_int,
        .value_traits       = config_value_traits_feature_enablement,
        .component_types    = config_component_broker | config_component_pbroker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_cust |
                              config_target_gid_type_global,
        .int_range_lo       = C2C_BYPASS_LOCAL_DISPATCH_PHASE2_MIN,
        .int_range_hi       = C2C_BYPASS_LOCAL_DISPATCH_PHASE2_MAX,
        .int_default        = C2C_BYPASS_LOCAL_DISPATCH_PHASE2_DEFAULT,
        .feature_group      = FEATURE_GROUP_PRIVATE_SERVICE_EDGE_C2C_LOCAL_DISP_BYPASS_PHASE2,
    },
    {
        .key                = C2C_BYPASS_LOCAL_DISPATCH_PHASE2_HARD_DISABLED,
        .desc               = "Hard Disable flag to disable C2C pse local dispatcher bypass phase-2 feature for all the customers",
        .details            = "1: feature is hard disabled for all customers\n"
                              "0: feature is not hard disabled for all customers\n"
                              "default: 0 (feature is not hard disabled for all customers)\n",
        .val_type           = config_type_int,
        .value_traits       = config_value_traits_systemwide,
        .component_types    = config_component_broker | config_component_pbroker,
        .target_gid_types   = config_target_gid_type_global,
        .int_range_lo       = C2C_BYPASS_LOCAL_DISPATCH_PHASE2_HARD_DISABLED_MIN,
        .int_range_hi       = C2C_BYPASS_LOCAL_DISPATCH_PHASE2_HARD_DISABLED_MAX,
        .int_default        = C2C_BYPASS_LOCAL_DISPATCH_PHASE2_HARD_DISABLED_DEFAULT,
        .feature_group      = FEATURE_GROUP_PRIVATE_SERVICE_EDGE_C2C_LOCAL_DISP_BYPASS_PHASE2,
    }
};

static struct zpath_config_override_desc zpn_c2c_descriptions_private_broker[] = {
    {
        .key                = C2C_FEATURE,
        .desc               = "enable/disable C2C feature",
        .details            = "0: Disabled\n"
                              "1: Enabled\n"
                              "Order of check - Private: broker id, broker group id, customer id, global\n"
                              "default: 1 - C2C is enabled",
        .val_type           = config_type_int,
        .value_traits       = config_value_traits_systemwide,
        .component_types    = config_component_pbroker,
        .target_gid_types   = config_target_gid_type_inst |
                              config_target_gid_type_inst_grp | config_target_gid_type_cust |
                              config_target_gid_type_global,
        .int_range_lo       = 0,
        .int_range_hi       = 1,
        .int_default        = C2C_DEFAULT_BROKER_FEATURE,
        .feature_group      = FEATURE_GROUP_CLIENT_TO_CLIENT,
    },
    {
        .key                = C2C_MULTIPLE_REGISTRATION,
        .desc               = "enable/disable protection for C2C multiple FQDN registrations on same broker",
        .details            = "0: Disabled\n"
                              "1: Enabled\n"
                              "Order of check - Private: broker id, broker group id, customer id, global\n"
                              "default: 1 - Protection is enabled",
        .val_type           = config_type_int,
        .value_traits       = config_value_traits_feature_enablement,
        .component_types    = config_component_pbroker,
        .target_gid_types   = config_target_gid_type_inst |
                              config_target_gid_type_inst_grp | config_target_gid_type_cust |
                              config_target_gid_type_global,
        .int_range_lo       = 0,
        .int_range_hi       = 1,
        .int_default        = C2C_DEFAULT_MULTIPLE_REGISTRATION,
        .feature_group      = FEATURE_GROUP_C2C_MULTIPLE_FQDN_REG_PROTECTION,
    },
    {
        .key                = C2C_REG_RESEND_SEC,
        .desc               = "C2C dispatcher registration renewal in seconds",
        .details            = "Order of check - Private: broker id, broker group id, customer id, global\n"
                              "default: 300 sec",
        .val_type           = config_type_int,
        .value_traits       = config_value_traits_normal,
        .component_types    = config_component_pbroker,
        .target_gid_types   = config_target_gid_type_inst |
                              config_target_gid_type_inst_grp | config_target_gid_type_cust |
                              config_target_gid_type_global,
        .int_range_lo       = 60,
        .int_range_hi       = 3600,
        .int_default        = C2C_REG_RESEND_DEFAULT_TIMER_SEC,
        .feature_group      = FEATURE_GROUP_CLIENT_TO_CLIENT,
    },
    {
        .key                = C2C_REG_EXPIRY_SEC,
        .desc               = "C2C dispatcher registration expiration in seconds",
        .details            = "Order of check - Private: broker id, broker group id, customer id, global\n"
                              "default: 3600 sec",
        .val_type           = config_type_int,
        .value_traits       = config_value_traits_normal,
        .component_types    = config_component_pbroker,
        .target_gid_types   = config_target_gid_type_inst |
                              config_target_gid_type_inst_grp | config_target_gid_type_cust |
                              config_target_gid_type_global,
        .int_range_lo       = 3600,
        .int_range_hi       = 7200,
        .int_default        = C2C_REG_DEFAULT_EXPIRY_TTL_SEC,
        .feature_group      = FEATURE_GROUP_CLIENT_TO_CLIENT,
    },
    {
        .key                = C2C_BYPASS_LOCAL_DISPATCH_PROMOTE,
        .desc               = "C2C pse local dispatcher bypass promote",
        .details            = "Order of check - Private: broker id, broker group id, customer id, global\n"
                              "default: 0 (i.e. disabled), allowed values are 0 = Disabled, 1 = Enabled",
        .val_type           = config_type_int,
        .value_traits       = config_value_traits_feature_enablement,
        .component_types    = config_component_pbroker,
        .target_gid_types   = config_target_gid_type_inst |
                              config_target_gid_type_inst_grp | config_target_gid_type_cust |
                              config_target_gid_type_global,
        .int_range_lo       = C2C_BYPASS_LOCAL_DISPATCH_PROMOTE_MIN,
        .int_range_hi       = C2C_BYPASS_LOCAL_DISPATCH_PROMOTE_MAX,
        .int_default        = C2C_BYPASS_LOCAL_DISPATCH_PROMOTE_DEFAULT,
        .feature_group      = FEATURE_GROUP_PRIVATE_SERVICE_EDGE_C2C_LOCAL_DISP_BYPASS,
    },
    {
        .key                = C2C_BYPASS_LOCAL_DISPATCH_PHASE2_PSE_CACHE_CHAINS,
        .desc               = "C2C pse local dispatcher bypass phase-2: Max PSE cache chains per cache bucket",
        .details            = "Order of check - Private: broker id,customer id, global\n"
                              "default: 10 chains per bucket",
        .val_type           = config_type_int,
        .value_traits       = config_value_traits_normal,
        .component_types    = config_component_pbroker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_cust | config_target_gid_type_global,
        .int_range_lo       = C2C_BYPASS_LOCAL_DISPATCH_PHASE2_PSE_CACHE_CHAINS_MIN,
        .int_range_hi       = C2C_BYPASS_LOCAL_DISPATCH_PHASE2_PSE_CACHE_CHAINS_MAX,
        .int_default        = C2C_BYPASS_LOCAL_DISPATCH_PHASE2_PSE_CACHE_CHAINS_DEFAULT,
        .feature_group      = FEATURE_GROUP_PRIVATE_SERVICE_EDGE_C2C_LOCAL_DISP_BYPASS_PHASE2,
    },
    {
        .key                = C2C_BYPASS_LOCAL_DISPATCH_PHASE2_PSE_CACHE_BUCKETS,
        .desc               = "C2C pse local dispatcher bypass phase-2: Max PSE cache buckets",
        .details            = "Order of check - Private: broker id,customer id, global\n"
                              "Default: 2048 buckets. The config value must be power of 2. Example 1024, 2048, 4096 etc..",
        .val_type           = config_type_int,
        .value_traits       = config_value_traits_normal,
        .component_types    = config_component_pbroker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_cust | config_target_gid_type_global,
        .int_range_lo       = C2C_BYPASS_LOCAL_DISPATCH_PHASE2_PSE_CACHE_BUCKETS_MIN,
        .int_range_hi       = C2C_BYPASS_LOCAL_DISPATCH_PHASE2_PSE_CACHE_BUCKETS_MAX,
        .int_default        = C2C_BYPASS_LOCAL_DISPATCH_PHASE2_PSE_CACHE_BUCKETS_DEFAULT,
        .feature_group      = FEATURE_GROUP_PRIVATE_SERVICE_EDGE_C2C_LOCAL_DISP_BYPASS_PHASE2,
    },
    {
        .key                = C2C_BYPASS_LOCAL_DISPATCH_PHASE2_PSE_EXPIRY_S,
        .desc               = "C2C pse local dispatcher bypass phase-2: PSE cache entry expiry interval for expiring inactive entries",
        .details            = "Order of check - Private: broker id, customer id, global\n"
                              "default: 300 secs",
        .val_type           = config_type_int,
        .value_traits       = config_value_traits_normal,
        .component_types    = config_component_pbroker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_cust |
                              config_target_gid_type_global,
        .int_range_lo       = C2C_BYPASS_LOCAL_DISPATCH_PHASE2_PSE_EXPIRY_S_MIN,
        .int_range_hi       = C2C_BYPASS_LOCAL_DISPATCH_PHASE2_PSE_EXPIRY_S_MAX,
        .int_default        = C2C_BYPASS_LOCAL_DISPATCH_PHASE2_PSE_EXPIRY_S_DEFAULT,
        .feature_group      = FEATURE_GROUP_PRIVATE_SERVICE_EDGE_C2C_LOCAL_DISP_BYPASS_PHASE2,
    },
    {
        .key                = C2C_BYPASS_LOCAL_DISPATCH_PHASE2_PSE_RPC_TIMEOUT_S,
        .desc               = "C2C pse local dispatcher bypass phase-2: Pse remote C2C check RPC timeout for waiting response from Public broker.",
        .details            = "Order of check - Private: broker id, customer id, global\n"
                              "default: 8 sec",
        .val_type           = config_type_int,
        .value_traits       = config_value_traits_normal,
        .component_types    = config_component_pbroker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_cust |
                              config_target_gid_type_global,
        .int_range_lo       = C2C_BYPASS_LOCAL_DISPATCH_PHASE2_PSE_RPC_TIMEOUT_S_MIN,
        .int_range_hi       = C2C_BYPASS_LOCAL_DISPATCH_PHASE2_PSE_RPC_TIMEOUT_S_MAX,
        .int_default        = C2C_BYPASS_LOCAL_DISPATCH_PHASE2_PSE_RPC_TIMEOUT_S_DEFAULT,
        .feature_group      = FEATURE_GROUP_PRIVATE_SERVICE_EDGE_C2C_LOCAL_DISP_BYPASS_PHASE2,
    },
    {
        .key                = C2C_BYPASS_LOCAL_DISPATCH_PHASE2_PSE_TIMEOUT_ROUTE_LOCALLY,
        .desc               = "C2C pse local dispatcher bypass phase-2: enable/disable Route locally on PSE on remote C2C check timeout",
        .details            = "Order of check - : broker id, customer id, global\n"
                              "default: 1 (i.e. enabled)",
        .val_type           = config_type_int,
        .value_traits       = config_value_traits_feature_enablement,
        .component_types    = config_component_pbroker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_cust |
                              config_target_gid_type_global,
        .int_range_lo       = C2C_BYPASS_LOCAL_DISPATCH_PHASE2_PSE_TIMEOUT_ROUTE_LOCALLY_MIN,
        .int_range_hi       = C2C_BYPASS_LOCAL_DISPATCH_PHASE2_PSE_TIMEOUT_ROUTE_LOCALLY_MAX,
        .int_default        = C2C_BYPASS_LOCAL_DISPATCH_PHASE2_PSE_TIMEOUT_ROUTE_LOCALLY_DEFAULT,
        .feature_group      = FEATURE_GROUP_PRIVATE_SERVICE_EDGE_C2C_LOCAL_DISP_BYPASS_PHASE2,
    }
};

static struct zpath_config_override_desc zpn_c2c_descriptions_public_broker[] = {
    {
        .key                = C2C_FEATURE,
        .desc               = "enable/disable C2C feature",
        .details            = "0: Disabled\n"
                              "1: Enabled\n"
                              "Order of check - Public: component id, root customer id, global\n"
                              "default: 1 - C2C is enabled",
        .val_type           = config_type_int,
        .value_traits       = config_value_traits_systemwide,
        .component_types    = config_component_broker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_zsroot |
                              config_target_gid_type_global,
        .int_range_lo       = 0,
        .int_range_hi       = 1,
        .int_default        = C2C_DEFAULT_BROKER_FEATURE,
        .feature_group      = FEATURE_GROUP_CLIENT_TO_CLIENT,
    },
    {
        .key                = C2C_MULTIPLE_REGISTRATION,
        .desc               = "enable/disable protection for C2C multiple FQDN registrations on same broker",
        .details            = "0: Disabled\n"
                              "1: Enabled\n"
                              "Order of check - Public: component id, root customer id, global\n"
                              "default: 1 - Protection is enabled",
        .val_type           = config_type_int,
        .value_traits       = config_value_traits_feature_enablement,
        .component_types    = config_component_broker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_zsroot |
                              config_target_gid_type_global,
        .int_range_lo       = 0,
        .int_range_hi       = 1,
        .int_default        = C2C_DEFAULT_MULTIPLE_REGISTRATION,
        .feature_group      = FEATURE_GROUP_C2C_MULTIPLE_FQDN_REG_PROTECTION,
    },
    {
        .key                = C2C_REG_RESEND_SEC,
        .desc               = "C2C dispatcher registration renewal in seconds",
        .details            = "Order of check - Public: component id, root customer id, global\n"
                              "default: 300 sec",
        .val_type           = config_type_int,
        .value_traits       = config_value_traits_normal,
        .component_types    = config_component_broker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_zsroot |
                              config_target_gid_type_global,
        .int_range_lo       = 60,
        .int_range_hi       = 3600,
        .int_default        = C2C_REG_RESEND_DEFAULT_TIMER_SEC,
        .feature_group      = FEATURE_GROUP_CLIENT_TO_CLIENT,
    },
    {
        .key                = C2C_REG_EXPIRY_SEC,
        .desc               = "C2C dispatcher registration expiration in seconds",
        .details            = "Order of check - Public: component id, root customer id, global\n"
                              "default: 3600 sec",
        .val_type           = config_type_int,
        .value_traits       = config_value_traits_normal,
        .component_types    = config_component_broker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_zsroot |
                              config_target_gid_type_global,
        .int_range_lo       = 3600,
        .int_range_hi       = 7200,
        .int_default        = C2C_REG_DEFAULT_EXPIRY_TTL_SEC,
        .feature_group      = FEATURE_GROUP_CLIENT_TO_CLIENT,
    },
    {
        .key                = C2C_BYPASS_LOCAL_DISPATCH_PHASE2_BROKER_CACHE_CHAINS,
        .desc               = "C2C pse local dispatcher bypass phase-2: Max Broker cache chains per cache bucket",
        .details            = "Order of check - Public: broker id, global\n"
                              "default: 20 chains per bucket",
        .val_type           = config_type_int,
        .value_traits       = config_value_traits_normal,
        .component_types    = config_component_broker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_global,
        .int_range_lo       = C2C_BYPASS_LOCAL_DISPATCH_PHASE2_BROKER_CACHE_CHAINS_MIN,
        .int_range_hi       = C2C_BYPASS_LOCAL_DISPATCH_PHASE2_BROKER_CACHE_CHAINS_MAX,
        .int_default        = C2C_BYPASS_LOCAL_DISPATCH_PHASE2_BROKER_CACHE_CHAINS_DEFAULT,
        .feature_group      = FEATURE_GROUP_PRIVATE_SERVICE_EDGE_C2C_LOCAL_DISP_BYPASS_PHASE2,
    },
    {
        .key                = C2C_BYPASS_LOCAL_DISPATCH_PHASE2_BROKER_CACHE_BUCKETS,
        .desc               = "C2C pse local dispatcher bypass phase-2: Max Broker cache buckets",
        .details            = "Order of check - Public: broker id, global\n"
                              "Default: 8192 buckets. The config value must be power of 2. Example 2048, 4096, 8192 etc..",
        .val_type           = config_type_int,
        .value_traits       = config_value_traits_normal,
        .component_types    = config_component_broker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_global,
        .int_range_lo       = C2C_BYPASS_LOCAL_DISPATCH_PHASE2_BROKER_CACHE_BUCKETS_MIN,
        .int_range_hi       = C2C_BYPASS_LOCAL_DISPATCH_PHASE2_BROKER_CACHE_BUCKETS_MAX,
        .int_default        = C2C_BYPASS_LOCAL_DISPATCH_PHASE2_BROKER_CACHE_BUCKETS_DEFAULT,
        .feature_group      = FEATURE_GROUP_PRIVATE_SERVICE_EDGE_C2C_LOCAL_DISP_BYPASS_PHASE2,
    },
    {
        .key                = C2C_BYPASS_LOCAL_DISPATCH_PHASE2_BROKER_EXPIRY_S,
        .desc               = "C2C pse local dispatcher bypass phase-2: Broker cache entry expiry interval for expiring inactive entries",
        .details            = "Order of check - Public: broker id, global\n"
                              "default: 300 secs",
        .val_type           = config_type_int,
        .value_traits       = config_value_traits_normal,
        .component_types    = config_component_broker,
        .target_gid_types   = config_target_gid_type_inst  | config_target_gid_type_global,
        .int_range_lo       = C2C_BYPASS_LOCAL_DISPATCH_PHASE2_BROKER_EXPIRY_S_MIN,
        .int_range_hi       = C2C_BYPASS_LOCAL_DISPATCH_PHASE2_BROKER_EXPIRY_S_MAX,
        .int_default        = C2C_BYPASS_LOCAL_DISPATCH_PHASE2_BROKER_EXPIRY_S_DEFAULT,
        .feature_group      = FEATURE_GROUP_PRIVATE_SERVICE_EDGE_C2C_LOCAL_DISP_BYPASS_PHASE2,
    },
    {
        .key                = C2C_BYPASS_LOCAL_DISPATCH_PHASE2_BROKER_RPC_TIMEOUT_S,
        .desc               = "C2C pse local dispatcher bypass phase-2: Broker remote C2C check check RPC timeout for waiting response from Public Dispatcher.",
        .details            = "Order of check - Public: broker id, customer id, global\n"
                              "default: 3 sec",
        .val_type           = config_type_int,
        .value_traits       = config_value_traits_normal,
        .component_types    = config_component_broker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_cust |
                              config_target_gid_type_global,
        .int_range_lo       = C2C_BYPASS_LOCAL_DISPATCH_PHASE2_BROKER_RPC_TIMEOUT_S_MIN,
        .int_range_hi       = C2C_BYPASS_LOCAL_DISPATCH_PHASE2_BROKER_RPC_TIMEOUT_S_MAX,
        .int_default        = C2C_BYPASS_LOCAL_DISPATCH_PHASE2_BROKER_RPC_TIMEOUT_S_DEFAULT,
        .feature_group      = FEATURE_GROUP_PRIVATE_SERVICE_EDGE_C2C_LOCAL_DISP_BYPASS_PHASE2,
    }
};

/*
 * get rows for customer_gid
 */
static int zpn_c2c_client_customer_gid(int64_t customer_gid,
                                       struct zpn_c2c_client_registration **registrations,
                                       size_t *reg_count) {
    int res;

    if (NULL == zpn_c2c_gid_index) {
        // init not called
        return ZPATH_RESULT_ERR;
    }

    int shard_index = ZPATH_SHARD_FROM_GID(customer_gid);

    res = wally_table_get_rows_fast(zpn_c2c_gid_index[shard_index],
                                    &customer_gid,
                                    sizeof(customer_gid),
                                    (void **)registrations,
                                    reg_count,
                                    0,
                                    NULL,
                                    NULL,
                                    0);
#ifdef DTA_CHECKPOINT
    ROW_FILTER_DTA_CHECKPOINT(registrations, *reg_count, customer_gid);
#endif
    return res;
}


/*
 * sepparate function to help with unit testing
 * Broker specific match function without local regex cache support
 */
static int c2c_match_broker(int64_t customer_gid, const char *fqdn, const char *regex_str)
{
    regex_t re;
    int re_res;
    int re_exec;

    if (NULL == regex_str || 0 == strlen(regex_str)) {
        ZPN_LOG(AL_ERROR, "C2C_FQDN_BAD_REGEX: empty or null regex for customer_gid=%" PRId64, customer_gid);

        return C2C_FQDN_BAD_REGEX;
    }

    // compile regex
    re_res = regcomp(&re, regex_str, REG_EXTENDED | REG_NOSUB);

    if (0 != re_res) {
        ZPN_LOG(AL_ERROR, "c2c: invalid regex %s , error= %s , customer_gid=%" PRId64, regex_str,
                c2c_result_string(C2C_FQDN_BAD_REGEX, C2C_SUCCESS_FULL), customer_gid);

        return C2C_FQDN_BAD_REGEX;
    }

    // match with fqdn
    re_exec = regexec(&re, fqdn, 0, NULL, 0);
    regfree(&re);

    if (0 == re_exec) {
        ZPN_DEBUG_C2C("c2c: matched regex=%s with fqdn=%s, re_exec=%d, customer_gid=%" PRId64, regex_str, fqdn, re_exec, customer_gid);
        return C2C_FQDN_MATCH;
    }

    ZPN_DEBUG_C2C("not matched regex=%s with fqdn=%s, re_exec=%d, customer_gid=%" PRId64, regex_str, fqdn, re_exec, customer_gid);
    return C2C_FQDN_NO_MATCH;
}

/*
 * PSE specific match function with local regex cache support
 */
static int c2c_match_pse(int64_t customer_gid, const char *fqdn, const char *regex_str)
{
    regex_t *re = NULL;
    int re_res;
    int re_exec = REG_NOMATCH;
    regex_t *pre_compiled_regex = NULL;
    int res = ZPN_RESULT_NO_ERROR;
    int found_in_cache = 0;
    int cache_ready_to_store = 0;
    int updated_in_cache = 0;

    if ((NULL == regex_str) || (regex_str[0] == '\0')) {
        ZPN_LOG(AL_ERROR, "C2C_FQDN_BAD_REGEX: empty or null regex for customer_gid=%" PRId64, customer_gid);

        return C2C_FQDN_BAD_REGEX;
    }

    /* Only PSE will have customer pre-compiled c2c regex cache, use it if avaliable */
    res = zpn_broker_c2c_regex_customer_cache_get(customer_gid, &cache_ready_to_store, &pre_compiled_regex);
    found_in_cache = (res == ZPN_RESULT_NO_ERROR) ? 1 : 0;

    /* No cached precompiled regex was found, compile new regex on heap and save it to cache, next time it will be found in the cache  */
    if (!found_in_cache ) {
        regex_t *new_regex = ZPN_CALLOC(sizeof(regex_t));
        if (!new_regex) {
            ZPN_LOG(AL_ERROR, "Unable to allocate new regex for cache for customer_gid: %"PRId64, customer_gid);
            return C2C_FQDN_BAD_REGEX;
        }

        re = new_regex;

        // compile regex
        re_res = regcomp(re, regex_str, REG_EXTENDED | REG_NOSUB);

        if (0 != re_res) {
            ZPN_LOG(AL_ERROR, "c2c: invalid regex %s , error= %s , re_res=%d, customer_gid=%" PRId64, regex_str,
                    c2c_result_string(C2C_FQDN_BAD_REGEX, 0), re_res, customer_gid);

            ZPN_FREE(re);
            return C2C_FQDN_BAD_REGEX;
        }

        if (cache_ready_to_store) {

            /* Store the compiled regex into the cache, if cache is ready, it will be ready on a PSE  */
            res = zpn_broker_c2c_regex_customer_cache_update(customer_gid, re);

            if (res) {
                /* Store failed, free local compiled regex */
                ZPN_LOG(AL_ERROR, "Unable to store regex into cache for customer_gid: %"PRId64, customer_gid);
            } else {
                updated_in_cache = 1;
            }
        }
    } else if (pre_compiled_regex) {

        /* Use cached pre-compiled regex */
        re = pre_compiled_regex;
    }

    if (re) {
        // match with fqdn
        re_exec = regexec(re, fqdn, 0, NULL, 0);
    } else {
        ZPN_LOG(AL_ERROR, "Regex re is NULL for customer_gid=%"PRId64, customer_gid);
    }

    if (!updated_in_cache && !found_in_cache) {
        regfree(re);
        ZPN_FREE(re);
        re = NULL;
        ZPN_LOG(AL_ERROR, "Freeing allocated re, unable to update in cache, customer_gid: %"PRId64, customer_gid);
    }

    if (0 == re_exec) {
        ZPN_DEBUG_C2C("c2c: matched regex=%s with fqdn=%s, re_exec=%d, customer_gid=%" PRId64, regex_str, fqdn, re_exec, customer_gid);

        return C2C_FQDN_MATCH;
    }

    ZPN_DEBUG_C2C("not matched regex=%s with fqdn=%s, re_exec=%d, customer_gid=%" PRId64, regex_str, fqdn, re_exec, customer_gid);
    return C2C_FQDN_NO_MATCH;
}

/* Common match function for PSE/Broker */
int c2c_match(int64_t customer_gid, const char *fqdn, const char *regex_str)
{
    if (g_broker_common_cfg == NULL) {
        /* Hook for unit testing*/
        return c2c_match_broker(customer_gid, fqdn, regex_str);
    }

    if (ZPN_BROKER_IS_PUBLIC()) {
        return c2c_match_broker(customer_gid, fqdn, regex_str);
    } else {
        return c2c_match_pse(customer_gid, fqdn, regex_str);
    }
}


/* see declarations for comments */
int c2c_match_fqdn_against_policy(int64_t customer_gid, const char *fqdn) {
    int res;
    size_t entry;

    int last_error = C2C_FQDN_NO_MATCH;
    struct zpn_c2c_client_registration *c2c_registrations[ZPN_C2C_CLIENT_REGISTRATION_MAX_ENTRIES_PER_CUSTOMER + 1];
    size_t number_of_entries = ZPN_C2C_CLIENT_REGISTRATION_MAX_ENTRIES_PER_CUSTOMER + 1;

    // too defensive ?
    if (NULL == fqdn || 0 == strlen(fqdn)) {
        return C2C_FQDN_EMPTY;
    }

    res = zpn_c2c_client_customer_gid(customer_gid, &(c2c_registrations[0]), &number_of_entries);

    if (ZPATH_RESULT_NO_ERROR != res) {
        ZPN_LOG(AL_NOTICE, "looking for customer_id= %" PRId64 " in zpn_c2c_client failed, error=%s", customer_gid,
                zpn_result_string(res));
        return res == ZPATH_RESULT_NOT_FOUND ? C2C_FQDN_NO_ENTRIES : C2C_FQDN_BAD_SCHEMA;
    }

    if (number_of_entries == 0) {
        return C2C_FQDN_NO_ENTRIES;
    }
    if (number_of_entries > ZPN_C2C_CLIENT_REGISTRATION_MAX_ENTRIES_PER_CUSTOMER) {
        return C2C_FQDN_TOO_MANY_REGEX;
    }

    // find and process regex
    for (entry = 0; entry < number_of_entries; entry++) {
        res = c2c_match(customer_gid, fqdn, c2c_registrations[entry]->regex);

        if (C2C_FQDN_MATCH == res) {
            return C2C_FQDN_MATCH;
        }

        if (C2C_FQDN_NO_MATCH != res) {
            // remember this ( latest) error, in case we dont find match
            last_error = res;
        }
    }

    return last_error;
}

/*
 * Dump the table entry corresponding to customer id
 * curl localhost:8000/zpn/cfg/c2c/policy_by_customer_gid?customer_gid=xxx
 */
static int zpn_c2c_client_registration_dump_by_customer_gid(struct zpath_debug_state *request_state,
                                                            const char **query_values,
                                                            int query_value_count,
                                                            void *cookie) {
    struct zpn_c2c_client_registration *c2c_registrations[ZPN_C2C_CLIENT_REGISTRATION_MAX_ENTRIES_PER_CUSTOMER];
    uint64_t customer_gid;
    int res;
    char jsonout[1000];
    size_t number_of_entries;

    if (!query_values[0]) {
        ZDP("Require 'customer_gid=gid'\n");
        return ZPATH_RESULT_NO_ERROR;
    }

    customer_gid = strtoull(query_values[0], NULL, 0);
    number_of_entries = ZPN_C2C_CLIENT_REGISTRATION_MAX_ENTRIES_PER_CUSTOMER;

    res = zpn_c2c_client_customer_gid(customer_gid, c2c_registrations, &number_of_entries);
    if (ZPATH_RESULT_NO_ERROR != res) {
        ZDP("couldn't find load table by customer_gid, wally returned (%s)\n", zpath_result_string(res));
        return ZPATH_RESULT_NO_ERROR;
    }

    for (size_t iter_entries = 0; iter_entries < number_of_entries; iter_entries++) {
        if (ARGO_RESULT_NO_ERROR == argo_structure_dump(zpn_c2c_client_registration_description,
                                                        c2c_registrations[iter_entries], jsonout, sizeof(jsonout), NULL,
                                                        1)) {
            ZDP("%s\n", jsonout);
        }
    }
    if (number_of_entries) {
        ZDP("%zu matching entries in zpn_c2c_client_registration\n", number_of_entries);
    }

    return ZPATH_RESULT_NO_ERROR;
}

/*
 * run on c_state thread
 */

static void notify_registration_change(struct zpn_broker_client_fohh_state *c_state, int64_t expire_s)
{
    struct zpn_tlv *tlv = c_state_get_tlv(c_state);
    if (zpn_tlv_sanity_check(tlv) != ZPN_RESULT_NO_ERROR) {
        return;
    }

    // notify client
    const char *error_code = NULL;
    if (c_state->fqdn_register_error != C2C_FQDN_MATCH) {
        error_code = c2c_result_string(c_state->fqdn_register_error, C2C_SUCCESS_EMPTY);
    }

    zpn_send_zpn_client_app_registration_notification(
        tlv, zpn_tlv_conn_incarnation(tlv), c_state->client_fqdn, &c_state->c2c_ip.reserved_ip,
        c_state->c2c_ip.location_hint, error_code);

    // notify dispatcher for every item in
    zpn_broker_dispatch_send_c2c_app_registration(c_state->customer_gid,
                                                  c_state->connect_us,
                                                  expire_s,
                                                  c_state->endpoint_cname ?: c_state->cname,
                                                  c_state->fqdn_registered,
                                                  c_state->client_fqdn,
                                                  NULL,
                                                  c_state->hash_hardware_id,
                                                  "policy");

    // log
    c_state->log.fqdn_registered = c_state->fqdn_registered;
    c_state->log.fqdn_register_error = c2c_result_string(c_state->fqdn_register_error, C2C_SUCCESS_FULL);

    // the auth log will be updated either by auth monitor or disconnect
}

/*
 * run on c_state thread
 */
static void check_and_update_registration(struct zevent_base *base,
                                          void *cookie_c_state,
                                          int64_t customer_gid,
                                          void *cookie_regex,
                                          void *not_used_cookie2,
                                          void *not_used_cookie3,
                                          int64_t incarnation) {
    struct zpn_broker_client_fohh_state *c_state = cookie_c_state;
    char *regex = cookie_regex;
    int result_of_match;

    if (c_state != NULL && incarnation == c_state->incarnation && c_state->client_fqdn != NULL &&
        customer_gid == c_state->customer_gid) {
        /* new or update */
        result_of_match = c2c_match(c_state->customer_gid, c_state->client_fqdn, regex);

        // check if the registration state is changed
        if ((result_of_match == C2C_FQDN_MATCH && c_state->fqdn_registered == FQDN_NOT_REGISTERED) ||
            (result_of_match != C2C_FQDN_MATCH && c_state->fqdn_registered == FQDN_REGISTERED)) {
            // change, prev failed , now success
            const int64_t expire_s = zpn_cloud_adjusted_epoch_s() + c2c_get_expiration_ttl();
            c_state->fqdn_registered = result_of_match == C2C_FQDN_MATCH ? FQDN_REGISTERED : FQDN_NOT_REGISTERED;
            c_state->fqdn_register_error = result_of_match;

            c_state->log.fqdn_registered = c_state->fqdn_registered;
            c_state->log.fqdn_register_error = c2c_result_string(result_of_match, C2C_SUCCESS_FULL);

            notify_registration_change(c_state, expire_s);

        } else if (result_of_match != C2C_FQDN_MATCH && c_state->fqdn_registered == FQDN_NOT_REGISTERED) {
            // no change, prev and now are failed
            if (result_of_match != c_state->fqdn_register_error) {
                // error code changed, need to update log
                // no need to update dispatcher and client ( even though the error code is changed)
                // the auth log will be updated either by auth monitor or disconnect
                c_state->fqdn_register_error = result_of_match;
                c_state->log.fqdn_registered = c_state->fqdn_registered;
                c_state->log.fqdn_register_error = c2c_result_string(result_of_match, C2C_SUCCESS_FULL);
            }
        } else {
            // no change
        }
    }

    ZPN_FREE(regex);
}


static void unregister_c2c_client_deferred(struct zevent_base *base,
                                           void *cookie_c_state,
                                           int64_t incarnation,
                                           void *cookie_regex,
                                           void *not_used_cookie2,
                                           void *not_used_cookie3,
                                           int64_t c2c_error_code) {
    struct zpn_broker_client_fohh_state *c_state = cookie_c_state;

    if (c_state != NULL && incarnation == c_state->incarnation && c_state->fqdn_registered != FQDN_NOT_REGISTERED) {
        c_state->fqdn_registered = FQDN_NOT_REGISTERED;
        c_state->fqdn_register_error = c2c_error_code;

        // this should not happen often, ok to log always
        ZPN_LOG(AL_INFO, "C2C unregister tunnel=%s hash_hardware_id=%s - %s", c_state->tunnel_id, c_state->hash_hardware_id,
                c2c_result_string(c_state->fqdn_register_error, 1));
        // notify the dispatcher and client
        notify_registration_change(c_state, 0);
    }
}

/*
 * callback on c2c regrestration table update
 */
static void c2c_row_callback_deferred(void *cookie_row, void *cookie2)
{
    /* IMPORTANT: Don't forget to release row */
    struct argo_object *row = cookie_row;
    struct zpn_c2c_client_registration *c2c_reg = row->base_structure_void;
    struct zpn_broker_connected_clients *connected_clients = &(broker.clients);

    uint64_t hash;
    struct argo_hash_element *walk;

    ZPN_LOG(AL_INFO, "C2C: Updated regex row cb: c_gid=%" PRId64 " regex=%s ", c2c_reg->customer_gid, c2c_reg->regex);

    /* Invalidate the c2c regex cache on PSE */
    if (ZPN_BROKER_IS_PRIVATE()) {
        zpn_broker_c2c_regex_customer_cache_clear(c2c_reg->customer_gid);
    }

    pthread_mutex_lock(&(connected_clients->lock));

    hash = (1ll << connected_clients->client_fqdns_per_customer->table_size) - 1;
    hash &= CityHash64((const char *)&c2c_reg->customer_gid, sizeof(c2c_reg->customer_gid));

    LIST_FOREACH(walk, &(connected_clients->client_fqdns_per_customer->table[hash]), hash_bucket) {
        struct zpn_broker_client_fohh_state *c_state = walk->object;

        if (c_state != NULL) {
            if (c2c_reg->deleted) {
                /* deleted */
                // ignore delete if 1 regex per customer
                // TODO: for multiple regex implement
            } else {
                /* new or update, needs to run on c_state thread */
                zevent_base_big_call(fohh_thread_id_zevent_base(c_state->conn_thread_id),
                                     check_and_update_registration, c_state, c2c_reg->customer_gid,
                                     c2c_reg->regex ? ZPN_STRDUP(c2c_reg->regex, strlen(c2c_reg->regex)) : NULL, NULL,
                                     NULL, c_state->incarnation);
            }
        }
    }  // for each

    pthread_mutex_unlock(&(connected_clients->lock));

    argo_object_release(row);
}

/*
 * callback for row
 */
static int zpn_c2c_client_registration_row_callback(void *cookie,
                                                    struct wally_registrant *registrant,
                                                    struct wally_table *table,
                                                    struct argo_object *previous_row,
                                                    struct argo_object *row,
                                                    int64_t request_id)
{
#ifdef DTA_CHECKPOINT
    struct zpn_c2c_client_registration *c2c_reg = row->base_structure_void;
    if(c2c_reg->scope_gid && c2c_reg->scope_gid != c2c_reg->customer_gid) return WALLY_RESULT_NO_ERROR;
#endif

    argo_object_hold(row);

    zevent_defer(c2c_row_callback_deferred, row, NULL, 0);
    return ZPATH_RESULT_NO_ERROR;
}

void argo_zpn_c2c_client_registration_init()
{
    if(!argo_register_global_structure(ZPN_C2C_CLIENT_REGISTRATION_HELPER))
        ZPN_LOG(AL_ERROR, "Could not register zpn_c2c_client_registration argo object");

}

/*
 * init zpn_c2c_client_registration
 */
int zpn_c2c_client_registration_init(struct wally *single_tenant_wally, int64_t profile_id, int fully_load, int register_with_zpath_table, wally_row_callback_f *row_cb) {
    int res;
    int64_t customer_gid;

    customer_table = zhash_table_alloc(&zpn_allocator);

    zpn_c2c_client_registration_description = argo_register_global_structure(ZPN_C2C_CLIENT_REGISTRATION_HELPER);
    if (!zpn_c2c_client_registration_description) {
        return ZPN_RESULT_ERR;
    }

    if (!row_cb) {
        row_cb = zpn_c2c_client_registration_row_callback;
    }

    customer_gid = ZPATH_GID_GET_CUSTOMER_GID(profile_id);

    if (single_tenant_wally) {
        struct wally_table *table;
        int shard_index = ZPATH_SHARD_FROM_GID(profile_id);
        zpn_c2c_gid_index = ZPN_CALLOC(ZPATH_MAX_SHARDS * sizeof(*zpn_c2c_gid_index));

        if (fully_load) {
            res = zpath_app_fully_loaded_customer_table(&table,
                                                        customer_gid,
                                                        single_tenant_wally,
                                                        zpn_c2c_client_registration_description,
                                                        row_cb,
                                                        NULL,
                                                        zpn_c2c_client_registration_row_fixup,
                                                        register_with_zpath_table);
            if (res) {
                ZPN_LOG(AL_ERROR, "Could not fully load zpn_c2c_client_registration table");
                return ZPN_RESULT_ERR;
            }
        } else {
            table = wally_table_create(single_tenant_wally,
                                       1,
                                       zpn_c2c_client_registration_description,
                                       row_cb,
                                       NULL,
                                       1,
                                       0,
                                       zpn_c2c_client_registration_row_fixup);
            if (!table) {
                ZPN_LOG(AL_ERROR, "Could not get zpn_c2c_client_registration table");
                return ZPN_RESULT_ERR;
            }
        }

        zpn_c2c_gid_index[shard_index] = wally_table_get_index(table, "customer_gid");
        if (!zpn_c2c_gid_index[shard_index]) {
            ZPN_LOG(AL_ERROR, "Could not get column customer_gid  zpn_c2c_client_registration_init");
            return ZPN_RESULT_ERR;
        }

    } else {
        res = zpath_app_add_sharded_table(zpn_c2c_client_registration_description,
                                          row_cb, NULL, 0, zpn_c2c_client_registration_row_fixup);
        if (res) {
            ZPN_LOG(AL_ERROR, "zpn_c2c_client_registration_init failed");
            return res;
        }

        zpn_c2c_gid_index = zpath_app_get_sharded_index("zpn_c2c_client_registration", "customer_gid");
        if (!zpn_c2c_gid_index) {
            ZPN_LOG(AL_ERROR, "Cannot read the zpn_c2c_client_registration");
            return ZPN_RESULT_ERR;
        }
    }

    // setup debug interface
    res = zpath_debug_add_read_command(
            "Dump table entry of a private broker given the customer gid.", "/zpn/cfg/c2c/policy_by_customer_gid",
            zpn_c2c_client_registration_dump_by_customer_gid, NULL, "customer_gid", "GID of the customer", NULL);
    if (res) {
        ZPN_LOG(AL_ERROR, "couldn't add /zpn/cfg/c2c/policy_by_customer_gid");
    }

    /* exclude the common overrides for sitec; ie. do only for public and private broker */
    if (!ZPN_IS_SITEC()) {
        // Register Override flags
        int len = sizeof(zpn_c2c_descriptions_common) / sizeof(struct zpath_config_override_desc);

        for (int i = 0; i < len; i++) {
            zpath_config_override_desc_register(&zpn_c2c_descriptions_common[i]);
        }
    }

    if (ZPN_BROKER_IS_PUBLIC()) {
        // Register Override flags specific to public broker
        int len = sizeof(zpn_c2c_descriptions_public_broker) / sizeof(struct zpath_config_override_desc);

        for (int i = 0; i < len; i++) {
            zpath_config_override_desc_register(&zpn_c2c_descriptions_public_broker[i]);
        }
    } else if (ZPN_BROKER_IS_PRIVATE()) {
        // Register Override flags specific to private broker
        int len = sizeof(zpn_c2c_descriptions_private_broker) / sizeof(struct zpath_config_override_desc);

        for (int i = 0; i < len; i++) {
            zpath_config_override_desc_register(&zpn_c2c_descriptions_private_broker[i]);
        }
    }

    return ZPN_RESULT_NO_ERROR;
}

static void zpn_c2c_client_response_callback_deferred(void *cookie1, void *cookie2) {
    struct customer_load_state *load_state = cookie1;
    ZPATH_MUTEX_LOCK(&(load_state->lock), __FILE__, __LINE__);
    if (!load_state->done) {
        wally_callback_queue_callback(load_state->queue);
        load_state->done = 1;
    }
    ZPATH_MUTEX_UNLOCK(&(load_state->lock), __FILE__, __LINE__);
}

static int zpn_c2c_client_response_callback(void *response_callback_cookie,
                                            struct wally_registrant *registrant,
                                            struct wally_table *table,
                                            int64_t request_id,
                                            int row_count) {
    zevent_defer(zpn_c2c_client_response_callback_deferred, response_callback_cookie, NULL, 0);
    return ZPATH_RESULT_NO_ERROR;
}

/*
 * load data for specific customer, called during user authentication
 */
int zpn_c2c_client_load(int64_t customer_gid,
                        wally_response_callback_f callback_f,
                        void *callback_cookie,
                        int64_t callback_id) {
    /* This code is optimized to not need locks for the common case */
    struct customer_load_state *load_state;
    int res = ZPN_RESULT_NO_ERROR;

    if (!customer_gid) {
        ZPN_LOG(AL_ERROR, "c2c: customer_gid = 0");
        return ZPN_RESULT_ERR;
    }

    load_state = zhash_table_lookup(customer_table, &customer_gid, sizeof(customer_gid), NULL);
    if (!load_state) {
        ZPATH_MUTEX_LOCK(&hash_lock, __FILE__, __LINE__);
        load_state = zhash_table_lookup(customer_table, &customer_gid, sizeof(customer_gid), NULL);
        if (!load_state) {
            load_state = ZPN_CALLOC(sizeof(struct customer_load_state));
            load_state->lock = ZPATH_MUTEX_INIT;
            zhash_table_store(customer_table, &customer_gid, sizeof(customer_gid), 0, load_state);
        }
        ZPATH_MUTEX_UNLOCK(&hash_lock, __FILE__, __LINE__);
    }
    if (load_state->done)
        return ZPATH_RESULT_NO_ERROR;

    ZPATH_MUTEX_LOCK(&(load_state->lock), __FILE__, __LINE__);
    if (load_state->done) {
        /* Might have been set while we wait for lock... */
        ZPATH_MUTEX_UNLOCK(&(load_state->lock), __FILE__, __LINE__);
        return ZPATH_RESULT_NO_ERROR;
    }
    if (!load_state->queue) {
        /* Need to register */
        /* NOTE: register_for_row can call our callback SYNCHRONOUSLY
         * if wally already has state for this connection. We are not
         * handling that in this case because it should be impossible
         * for something else to have read this state before this
         * routine, and this routine only reads it once. If this use
         * model were to change, this code would have to be
         * improved. */

        int shard_id = ZPATH_GID_GET_SHARD(customer_gid);
        load_state->queue = wally_callback_queue_create();
        res = wally_table_register_for_row(NULL,
                                           zpn_c2c_gid_index[shard_id],
                                           &customer_gid,
                                           sizeof(customer_gid),
                                           0,  // int64_t request_id,
                                           0,  // int64_t request_sequence,
                                           zpn_app_request_atleast_one,  // int request_atleast_one,
                                           0,  // int just_callback,
                                           0,  // int unique_registration,
                                           zpn_c2c_client_response_callback,
                                           load_state);
        /* Result is ALWAYS asynchronous, even if the callback will
         * come synchronously... Should fix that some day. */
        if (res != ZPN_RESULT_ASYNCHRONOUS) {
            ZPN_LOG(AL_ERROR, "Received response = %s", zpn_result_string(res));
        }
    }
    wally_callback_queue_add(load_state->queue, callback_f, callback_cookie, callback_id);

    ZPATH_MUTEX_UNLOCK(&(load_state->lock), __FILE__, __LINE__);

    return ZPN_RESULT_ASYNCHRONOUS;
}

const char *c2c_result_strings[] = {
        [C2C_FQDN_MATCH] = "FQDNMATCH",
        [C2C_FQDN_NO_MATCH] = "FQDN_NO_MATCH",
        [C2C_FQDN_BAD_SCHEMA] = "FQDN_BAD_SCHEMA",
        [C2C_FQDN_BAD_REGEX] = "FQDN_BAD_REGEX",
        [C2C_FQDN_TOO_MANY_REGEX] = "FQDN_TOO_MANY_REGEX",
        [C2C_FQDN_NO_ENTRIES] = "FQDN_NO_ENTRIES",
        [C2C_FQDN_EMPTY] = "FQDN_EMPTY",
        [C2C_BROKER_NOT_ENABLED] = "BROKER_NOT_ENABLED",
        [C2C_CUSTOMER_NOT_ENABLED] = "CUSTOMER_NOT_ENABLED",
        [C2C_NOT_AVAILABLE] = "C2C_NOT_AVAILABLE",
        [C2C_INVALID_FQDN] = "C2C_INVALID_FQDN",
        [C2C_FAIL_TO_CREATE_MTUNNEL] = "C2C_FAIL_TO_CREATE_MTUNNEL",
        [C2C_TRANSIT_INCORRECT_BROKER] = "C2C_TRANSIT_INCORRECT_BROKER",
        [C2C_TRANSIT_TLV_ERROR] = "C2C_TRANSIT_TLV_ERROR",
        [C2C_TRANSIT_MTUNNEL_NOT_FOUND] = "C2C_TRANSIT_MTUNNEL_NOT_FOUND",
        [C2C_TRANSIT_OWNER_ERROR] = "C2C_TRANSIT_OWNER_ERROR",
        [C2C_TRANSIT_NOT_FOUND] = "C2C_TRANSIT_NOT_FOUND",
        [C2C_MTUNNEL_NOT_FOUND] = "C2C_MTUNNEL_NOT_FOUND",
        [C2C_MTUNNEL_BAD_STATE] = "C2C_MTUNNEL_BAD_STATE",
        [C2C_MTUNNEL_FAILED_FORWARD] = "C2C_MTUNNEL_FAILED_FORWARD",
        [C2C_CLIENT_CONN_EXPIRED] = "C2C_CLIENT_CONN_EXPIRED",
        [C2C_CLIENT_CONN_INVALID_STATE] = "C2C_CLIENT_CONN_INVALID_STATE",
        [C2C_CLIENT_NOT_FOUND] = "C2C_CLIENT_NOT_FOUND",
        [C2C_PARTNERS_NOT_ALLOWED] = "C2C_PARTNERS_NOT_ALLOWED",
        [C2C_UNREGISTERED_NEW_CONNECTION_FROM_SAME_MACHINE] = "C2C_UNREGISTERED_NEW_CONNECTION_FROM_SAME_MACHINE",
};

const char *c2c_result_string(int err_code, int dont_send_match) {
    if (err_code == C2C_NO_ERROR)
        return NULL;
    if (err_code >= (sizeof(c2c_result_strings) / sizeof(const char *)))
        return "C2C_INVALID_RESULT";
    if (err_code < 0)
        return "C2C_INVALID_RESULT";
    if (c2c_result_strings[err_code] == NULL)
        return "C2C_INVALID_RESULT";

    if (dont_send_match && err_code == C2C_FQDN_MATCH) {
        // if the code is C2C_FQDN_MATCH and flag is set, dont send string, but NULL
        return NULL;
    }

    return c2c_result_strings[err_code];
}

int c2c_error_code_from_str(const char *error_str)
{
    size_t c2c_result_str_cnt = sizeof(c2c_result_strings) / sizeof(c2c_result_strings[0]);
    size_t i;

    if (!error_str) {
        return C2C_NO_ERROR;
    }

    for (i = 0; i < c2c_result_str_cnt; i++) {
        if (strcmp(error_str, c2c_result_strings[i]) == 0) {
            return i;
        }
    }

    return C2C_NO_ERROR;
}

/*
 * feature overide for c2c
 */

/* get int config value for broker ids */
static int64_t get_int_config_broker_id(const char *name, int64_t default_value)
{
    int64_t config_value = 0;

    if (ZPN_BROKER_IS_PUBLIC()) {
        int64_t root_customer_gid = ZPATH_GID_GET_CUSTOMER_GID(zpath_instance_global_state.current_config->gid);
        config_value = zpath_config_override_get_config_int(name,
                                                            &config_value,
                                                            default_value,
                                                            // list of gids to check
                                                            zpath_instance_global_state.current_config->gid,
                                                            root_customer_gid,
                                                            (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                            (int64_t)0);
        ZPN_DEBUG_C2C("C2C config %s for broker gid %ld root customer gid %ld = %ld",
                      name,
                      (long)zpath_instance_global_state.current_config->gid,
                      (long)root_customer_gid,
                      (long)config_value);

    } else {
        int64_t customer_gid = ZPATH_GID_GET_CUSTOMER_GID(g_broker_common_cfg->private_broker.broker_id);
        config_value = zpath_config_override_get_config_int(name,
                                                            &config_value,
                                                            default_value,
                                                            // list of gids to check
                                                            g_broker_common_cfg->private_broker.broker_id,
                                                            g_broker_common_cfg->private_broker.pb_group_id,
                                                            customer_gid,
                                                            (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                            (int64_t)0);

        ZPN_DEBUG_C2C("C2C config %s for broker gid %ld brk grp gid %ld customer gid %ld = %ld",
                      name,
                      (long)g_broker_common_cfg->private_broker.broker_id,
                      (long)g_broker_common_cfg->private_broker.pb_group_id,
                      (long)customer_gid,
                      (long)config_value);
    }

    return config_value;
}

int c2c_is_enabled_on_broker() {
    return get_int_config_broker_id(C2C_FEATURE, C2C_DEFAULT_BROKER_FEATURE) ? 1 : 0;
}
int c2c_is_enabled_for_customer(int64_t customer_gid) {
    int64_t config_value = 0;

    if (ZPN_IS_SITEC()) {
        return 0;
    }

    config_value = zpath_config_override_get_config_int(C2C_FEATURE_CUSTOMER,
                                                        &config_value,
                                                        C2C_DEFAULT_CUSTOMER_FEATURE,
                                                        // list of gids to check
                                                        customer_gid,
                                                        (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                        (int64_t)0);

    ZPN_DEBUG_C2C("C2C config value %s for customer_id %ld is %ld", C2C_FEATURE_CUSTOMER, (long)customer_gid,
                  (long)config_value);

    return config_value ? 1 : 0;
}

int c2c_is_bypass_local_dispatch_by_regex_enabled_for_customer(int64_t customer_gid)
{
    int64_t config_value = 0;

    config_value =  get_int_config_broker_id(C2C_BYPASS_LOCAL_DISPATCH_PROMOTE, C2C_BYPASS_LOCAL_DISPATCH_PROMOTE_DEFAULT);

    ZPN_DEBUG_C2C("C2C config value %s for customer_id %"PRId64" is %"PRId64,
                  C2C_BYPASS_LOCAL_DISPATCH_PROMOTE, customer_gid, config_value);

    return config_value ? 1 : 0;
}

static int64_t get_bypass_local_dispatch_phase2_broker_config(int customer_gid_req, int64_t customer_gid, const char *name, int64_t default_value)
{
    int64_t config_value = 0;

    if (customer_gid_req) {
        if (ZPN_BROKER_IS_PUBLIC()) {
            config_value = zpath_config_override_get_config_int(name,
                                                                &config_value,
                                                                default_value,
                                                                zpath_instance_global_state.current_config->gid,
                                                                customer_gid,
                                                                (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                                (int64_t)0);

            ZPN_DEBUG_C2C("C2C config: %s for public broker gid %ld customer gid %"PRId64" = %ld",
                           name, (long)zpath_instance_global_state.current_config->gid, customer_gid, (long)config_value);
        } else {

            config_value = zpath_config_override_get_config_int(name,
                                                                &config_value,
                                                                default_value,
                                                                g_broker_common_cfg->private_broker.broker_id,
                                                                customer_gid,
                                                                (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                                (int64_t)0);

            ZPN_DEBUG_C2C("C2C config: %s for private broker gid %ld customer gid %"PRId64" = %ld",
                           name, (long)g_broker_common_cfg->private_broker.broker_id, customer_gid, (long)config_value);
        }
    } else {
        if (ZPN_BROKER_IS_PUBLIC()) {
            config_value = zpath_config_override_get_config_int(name,
                                                                &config_value,
                                                                default_value,
                                                                zpath_instance_global_state.current_config->gid,
                                                                (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                                (int64_t)0);

            ZPN_DEBUG_C2C("C2C config: %s for public broker gid %ld is %ld",
                           name,(long)zpath_instance_global_state.current_config->gid,(long)config_value);
        } else {

            config_value = zpath_config_override_get_config_int(name,
                                                                &config_value,
                                                                default_value,
                                                                g_broker_common_cfg->private_broker.broker_id,
                                                                (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                                (int64_t)0);

            ZPN_DEBUG_C2C("C2C config: %s for private broker gid %ld is %ld",
                           name,(long)g_broker_common_cfg->private_broker.broker_id,(long)config_value);
        }
    }

    return config_value;
}

int c2c_is_bypass_local_dispatch_phase2_enabled_for_customer(int64_t customer_gid)
{
    int64_t config_value = 0;

    config_value =  get_bypass_local_dispatch_phase2_broker_config(1, customer_gid, C2C_BYPASS_LOCAL_DISPATCH_PHASE2, C2C_BYPASS_LOCAL_DISPATCH_PHASE2_DEFAULT);

    ZPN_DEBUG_C2C("C2C config value: %s for customer_id %"PRId64" is %"PRId64,
                  C2C_BYPASS_LOCAL_DISPATCH_PHASE2, customer_gid, config_value);

    return config_value ? 1 : 0;
}

int c2c_is_bypass_local_dispatch_phase2_hard_disabled()
{
    int64_t config_value = 0;

    config_value = zpath_config_override_get_config_int(C2C_BYPASS_LOCAL_DISPATCH_PHASE2_HARD_DISABLED,
                                                        &config_value,
                                                        C2C_BYPASS_LOCAL_DISPATCH_PHASE2_HARD_DISABLED_DEFAULT,
                                                        (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                        (int64_t)0);

    ZPN_DEBUG_C2C("C2C config value: %s is %"PRId64,
                  C2C_BYPASS_LOCAL_DISPATCH_PHASE2_HARD_DISABLED, config_value);

    return config_value ? 1 : 0;
}

int c2c_get_bypass_local_dispatch_phase2_pse_cache_chains(int64_t customer_gid)
{
    return get_bypass_local_dispatch_phase2_broker_config(1, customer_gid, C2C_BYPASS_LOCAL_DISPATCH_PHASE2_PSE_CACHE_CHAINS, C2C_BYPASS_LOCAL_DISPATCH_PHASE2_PSE_CACHE_CHAINS_DEFAULT);
}

int c2c_get_bypass_local_dispatch_phase2_pse_cache_buckets(int64_t customer_gid)
{
    return get_bypass_local_dispatch_phase2_broker_config(1, customer_gid, C2C_BYPASS_LOCAL_DISPATCH_PHASE2_PSE_CACHE_BUCKETS, C2C_BYPASS_LOCAL_DISPATCH_PHASE2_PSE_CACHE_BUCKETS_DEFAULT);
}

int c2c_get_bypass_local_dispatch_phase2_pse_expiry_s(int64_t customer_gid)
{
    return get_bypass_local_dispatch_phase2_broker_config(1, customer_gid, C2C_BYPASS_LOCAL_DISPATCH_PHASE2_PSE_EXPIRY_S, C2C_BYPASS_LOCAL_DISPATCH_PHASE2_PSE_EXPIRY_S_DEFAULT);
}

int c2c_get_bypass_local_dispatch_phase2_pse_rpc_timeout_s(int64_t customer_gid)
{
    return get_bypass_local_dispatch_phase2_broker_config(1, customer_gid, C2C_BYPASS_LOCAL_DISPATCH_PHASE2_PSE_RPC_TIMEOUT_S, C2C_BYPASS_LOCAL_DISPATCH_PHASE2_PSE_RPC_TIMEOUT_S_DEFAULT);
}

int c2c_get_bypass_local_dispatch_phase2_pse_timeout_route_locally(int64_t customer_gid)
{
    return get_bypass_local_dispatch_phase2_broker_config(1, customer_gid, C2C_BYPASS_LOCAL_DISPATCH_PHASE2_PSE_TIMEOUT_ROUTE_LOCALLY, C2C_BYPASS_LOCAL_DISPATCH_PHASE2_PSE_TIMEOUT_ROUTE_LOCALLY_DEFAULT);
}

int c2c_get_bypass_local_dispatch_phase2_broker_cache_chains()
{
    return get_bypass_local_dispatch_phase2_broker_config(0, 0, C2C_BYPASS_LOCAL_DISPATCH_PHASE2_BROKER_CACHE_CHAINS, C2C_BYPASS_LOCAL_DISPATCH_PHASE2_BROKER_CACHE_CHAINS_DEFAULT);
}

int c2c_get_bypass_local_dispatch_phase2_broker_cache_buckets()
{
    return get_bypass_local_dispatch_phase2_broker_config(0, 0, C2C_BYPASS_LOCAL_DISPATCH_PHASE2_BROKER_CACHE_BUCKETS, C2C_BYPASS_LOCAL_DISPATCH_PHASE2_BROKER_CACHE_BUCKETS_DEFAULT);
}

int c2c_get_bypass_local_dispatch_phase2_broker_expiry_s()
{
    return get_bypass_local_dispatch_phase2_broker_config(0, 0, C2C_BYPASS_LOCAL_DISPATCH_PHASE2_BROKER_EXPIRY_S, C2C_BYPASS_LOCAL_DISPATCH_PHASE2_BROKER_EXPIRY_S_DEFAULT);
}

int c2c_get_bypass_local_dispatch_phase2_broker_rpc_timeout_s(int64_t customer_gid)
{
    return get_bypass_local_dispatch_phase2_broker_config(1, customer_gid, C2C_BYPASS_LOCAL_DISPATCH_PHASE2_BROKER_RPC_TIMEOUT_S, C2C_BYPASS_LOCAL_DISPATCH_PHASE2_BROKER_RPC_TIMEOUT_S_DEFAULT);
}

/*
the count ( config time/timer_s ) used for resend
the time precision is 10 sec -> ZPN_TUNNEL_MONITOR_INTERVAL_S
 */
int64_t c2c_get_resend_interval_count() {
    // ZPN_TUNNEL_MONITOR_INTERVAL_S is the timer cycle for each client connn set in zpn_broker_client_conn_callback
    int64_t val = get_int_config_broker_id(C2C_REG_RESEND_SEC, C2C_REG_RESEND_DEFAULT_TIMER_SEC);

    // can return 0
    return val / ZPN_TUNNEL_MONITOR_INTERVAL_S;
}

static int c2c_multiple_registration_protection() {
    // 1 if protection is enabled, 0 if disabled
    int64_t val = get_int_config_broker_id(C2C_MULTIPLE_REGISTRATION, C2C_DEFAULT_MULTIPLE_REGISTRATION);

    // can return 0
    return val;
}
int64_t c2c_get_expiration_ttl() {
    return get_int_config_broker_id(C2C_REG_EXPIRY_SEC, C2C_REG_DEFAULT_EXPIRY_TTL_SEC);
}

static int zpn_c2c_check_if_registrations_from_same_machine(struct zpn_broker_client_fohh_state *cs, struct zpn_broker_client_fohh_state *cs_cmp) {

    // check if other c_states for same customer and hardware_id have same fqdn registration

    if (cs == cs_cmp) {
        return 0; // Skip same c_state
    }
    if (cs_cmp->customer_gid != cs->customer_gid) {
        return 0; // Skip if not the same customer
    }
    if (cs_cmp->hash_hardware_id[0] == '\0' || cs->hash_hardware_id[0] == '\0') {
        return 0; // Skip if no hardware id
    }
    if (cs_cmp->endpoint_cname == NULL || cs->endpoint_cname == NULL) {
        return 0; // Skip if no endpoint_cname
    }
    if (cs_cmp->endpoint_cname[0] == '\0' || cs->endpoint_cname[0] == '\0') {
        return 0; // Skip if endpoint_cname is empty
    }

    // Same hardware id, but the cname has to be different (e.g., machine tunnel vs user tunnel)
    if (strncmp(cs_cmp->hash_hardware_id, cs->hash_hardware_id, sizeof(cs_cmp->hash_hardware_id)) == 0 &&
        strncmp(cs_cmp->endpoint_cname, cs->endpoint_cname, strlen(cs_cmp->endpoint_cname)) != 0) {
        return 1;
    }

    return 0;
}

static int zpn_c2c_walk_cname_cb_locked(void *cookie, void *object) {
    struct zpn_broker_client_fohh_state *cs = object;
    struct zpn_broker_client_fohh_state *cs_cmp = cookie;

    // check if other c_states for same customer and hardware_id have same fqdn registration
    // if so, there is double registration, unregister them
    if (zpn_c2c_check_if_registrations_from_same_machine(cs, cs_cmp)) {
        // there are multiple connections for same machine with same FQDN
        // unregister this older connection and set it to FQDN_NOT_REGISTERED
        // this should not happen often, ok to log always
        ZPN_LOG(AL_INFO,
                "multiple c2c registrations c_gid=%" PRId64
                " fqdn=%s hash_hardware_id=%s cname=%s tunnel_id=%s new_tunnel_id=%s",
                cs->customer_gid, cs->client_fqdn, cs->hash_hardware_id, cs->endpoint_cname, cs->tunnel_id,
                cs_cmp->tunnel_id);

        // call on c_state thread
        zevent_base_big_call(fohh_thread_id_zevent_base(cs->conn_thread_id), unregister_c2c_client_deferred, cs, cs->incarnation,
                             NULL, NULL, NULL, C2C_UNREGISTERED_NEW_CONNECTION_FROM_SAME_MACHINE);

    } else {
        ZPN_DEBUG_C2C("multiple c2c registrations NOT A MATCH c_gid=%" PRId64
                      " fqdn=%s name=%s hash_hardware_id=%s tunnel_id=%s",
                      cs->customer_gid, cs->client_fqdn, cs->endpoint_cname, cs->hash_hardware_id, cs->tunnel_id);
    }
    return ZPN_RESULT_NO_ERROR;
}
/*
 * assumes locked connected_clients->lock
 */
struct zpn_c2c_fqdn *zpn_create_or_update_local_c2c_registration_locked(const char *fqdn,
                                                                        struct zpn_broker_client_fohh_state *c_state) {
    struct zpn_broker_connected_clients *connected_clients = &(broker.clients);
    struct zpn_c2c_fqdn *fqdn_obj;
    int res = 0;

    fqdn_obj = argo_hash_lookup(connected_clients->client_fqdns, fqdn, strlen(fqdn), NULL);

    if (fqdn_obj) {
        ZPN_DEBUG_C2C("FQDN already registered '%s'", fqdn);
        struct zpn_broker_client_fohh_state *cs;
        // the fqdn+cname exists, check c_state
        // coverity[overrun-buffer-val]
        cs = argo_hash_lookup(fqdn_obj->cstates, c_state->tunnel_id, strlen(c_state->tunnel_id), c_state);

        if (NULL == cs) {
            // store all fqdn/cnames
            res = argo_hash_store(fqdn_obj->cstates, c_state->tunnel_id, strlen(c_state->tunnel_id), 1, c_state);
            if (ZPN_RESULT_NO_ERROR != res) {
                ZPN_LOG(AL_ERROR, "failed to add c2c cstate fqdn %s", fqdn);
                return NULL;
            }

            // store per customer
            argo_hash_store(connected_clients->client_fqdns_per_customer, &c_state->customer_gid,
                            sizeof(c_state->customer_gid), 1, c_state);

            // update the c_state
            c_state->fqdn_registered = FQDN_NOT_REGISTERED;
        }

        // check if other c_states for same customer and hardware_id have same fqdn registration
        // if so, there is double registration, unregister them
        if (c2c_multiple_registration_protection()) {
            argo_hash_walk(fqdn_obj->cstates,
                           NULL,  // all of them
                           zpn_c2c_walk_cname_cb_locked,
                           c_state);
        }

    } else {
        // new fqdn
        fqdn_obj = ZPN_CALLOC(sizeof(struct zpn_c2c_fqdn));
        fqdn_obj->fqdn = ZPN_STRDUP(fqdn, strlen(fqdn));

        c_state->log.fqdn_registered = FQDN_NOT_REGISTERED;
        c_state->log.fqdn_register_error = NULL;

        fqdn_obj->cstates = argo_hash_alloc(7, 1);

        res = argo_hash_store(fqdn_obj->cstates, c_state->tunnel_id, strlen(c_state->tunnel_id), 1, c_state);
        if (ZPN_RESULT_NO_ERROR != res) {
            ZPN_LOG(AL_ERROR, "failed to add c2c cstate fqdn %s", fqdn);
            goto ERROR_WITH_FREE;
        }

        res = argo_hash_store(connected_clients->client_fqdns, fqdn_obj->fqdn, strlen(fqdn_obj->fqdn), 1, fqdn_obj);
        if (ZPN_RESULT_NO_ERROR != res) {
            ZPN_LOG(AL_ERROR, "failed to add c2c fqdn %s", fqdn);
            goto ERROR_WITH_FREE;
        }

        // store per customer
        res = argo_hash_store(connected_clients->client_fqdns_per_customer, &c_state->customer_gid,
                              sizeof(c_state->customer_gid), 1, c_state);

        if (ZPN_RESULT_NO_ERROR != res) {
            argo_hash_remove(connected_clients->client_fqdns, fqdn_obj->fqdn, strlen(fqdn_obj->fqdn), NULL);
            ZPN_LOG(AL_ERROR, "failed to add c2c fqdn %s", fqdn);
            goto ERROR_WITH_FREE;
        }
    }

    return fqdn_obj;

ERROR_WITH_FREE:
    if (fqdn_obj->fqdn) {
        ZPN_FREE(fqdn_obj->fqdn);
    }
    if (fqdn_obj->cstates) {
        argo_hash_free(fqdn_obj->cstates);
    }
    ZPN_FREE(fqdn_obj);

    return NULL;
}

/*
 * must be executed on c_state thread
 */
int c2c_remove_registration_locked(struct zpn_broker_client_fohh_state *c_state)
{
    struct zpn_broker_connected_clients *connected_clients = &(broker.clients);
    int res;
    struct zpn_c2c_fqdn *fqdn_obj;
    struct zpn_broker_client_fohh_state *cs;

    // lookup the customer
    cs = argo_hash_lookup(connected_clients->client_fqdns_per_customer, &c_state->customer_gid,
                          sizeof(c_state->customer_gid), c_state);
    if (NULL != cs) {
        res = argo_hash_remove(connected_clients->client_fqdns_per_customer, &c_state->customer_gid,
                               sizeof(c_state->customer_gid), c_state);

        if (res != ZPN_RESULT_NO_ERROR) {
            ZPN_LOG(AL_ERROR, "Failed to remove customer_gid=%" PRId64 " from client_fqdns_per_customer", c_state->customer_gid);
        }
    }

    if (c_state->client_fqdn == NULL) {
        return ZPN_RESULT_NOT_FOUND;
    }

    fqdn_obj = argo_hash_lookup(connected_clients->client_fqdns, c_state->client_fqdn, strlen(c_state->client_fqdn), NULL);

    if (fqdn_obj) {
        // the fqdn+cname exists, check c_state
        // coverity[overrun-buffer-val]
        cs = argo_hash_lookup(fqdn_obj->cstates, c_state->tunnel_id, strlen(c_state->tunnel_id), c_state);
        if (NULL != cs) {
            res = argo_hash_remove(fqdn_obj->cstates, c_state->tunnel_id, strlen(c_state->tunnel_id), c_state);

            if (res != ZPN_RESULT_NO_ERROR) {
                ZPN_LOG(AL_ERROR, "Failed to remove c2c registration conn %s", c_state->tunnel_id);
                return res;
            }

            ZPN_DEBUG_C2C("fqdn_clients count for %s - %"PRId64, c_state->client_fqdn, fqdn_obj->cstates->element_count);

            if (0 == fqdn_obj->cstates->element_count) {

                // notify the dispatcher, only if the fqdn is found in the local list
                ZPN_DEBUG_C2C("unregister fqdn=%s cname=%s", cs->client_fqdn, cs->endpoint_cname);
                c_state->fqdn_registered = FQDN_NOT_REGISTERED;

                res = zpn_broker_dispatch_send_c2c_app_registration(
                        c_state->customer_gid,
                        cs->connect_us,
                        1, //expire now
                        cs->endpoint_cname,
                        0,  // alive
                        cs->client_fqdn,
                        NULL,  // ip
                        (const char *)&c_state->hash_hardware_id,
                        "remove");
                if (ZPN_RESULT_NO_ERROR != res) {
                    ZPN_LOG(AL_ERROR, "cannot register c2c %s", zpn_result_string(res));
                }

                res = argo_hash_remove(connected_clients->client_fqdns, fqdn_obj->fqdn, strlen(fqdn_obj->fqdn), NULL);
                if (res) {
                    ZPN_DEBUG_C2C("Could not find client to remove from client_fqdn table: %s", zpn_result_string(res));
                } else {
                    // cleanup
                    ZPN_FREE(fqdn_obj->fqdn);

                    argo_hash_free(fqdn_obj->cstates);

                    ZPN_FREE(fqdn_obj);
                }
            }
        }
    }

    c_state->fqdn_registered = FQDN_NOT_REGISTERED;

    // free memory with c2c_free_c_state_locked

    return ZPN_RESULT_NO_ERROR;
}
/*
 * must be executed on c_state thread
 * free c2c memory assosiated with c_state
 */
void c2c_free_c_state_locked(struct zpn_broker_client_fohh_state *c_state) {
    // free
    ZPN_FREE(c_state->client_fqdn);
    c_state->client_fqdn = NULL;
    c_state->log.c2c_fqdn = NULL;  // holds pointer to c_state->client_fqdn, so not a leak

    if (c_state->endpoint_cname) {
        ZPN_FREE(c_state->endpoint_cname);
        c_state->endpoint_cname = NULL;
    }
}
/*
 * gets cituhash from string to string, not to be used for crypto purposes
 * out_str should be 33 character long i.e HRDID_STR_SIZE
 */
int cityhash_as_string(const char *input_str, char *out_str, int out_len) {
    if (NULL == input_str || 0 == strlen(input_str)) {
        ZPN_DEBUG_C2C("c2c machine_id cityhash_as_string is empty");
        return ZPN_RESULT_ERR;
    }

    ZPN_DEBUG_C2C("creating cityhash_as_string for %s", input_str);

    uint64 hash = CityHash64(input_str, strlen(input_str));

    // 64 bits -> 16 hex chars +1 for \0.
    snprintf(out_str, out_len, "%016" PRIx64, hash);

    return ZPN_RESULT_NO_ERROR;
}

/*
 * register c2c specific rpc messsages
 */
static int c2c_register_rpc_callbacks(struct zpn_broker_client_fohh_state *c_state)
{
    struct argo_state *argo;
    struct zpn_tlv *tlv = c_state_get_tlv(c_state);
    int res;

    if (c_state->tlv_type == zpn_fohh_tlv) {
        argo = fohh_argo_get_rx(zpn_mconn_fohh_tlv_get_conn(&(c_state->tlv_state)));
    } else {
        struct zpn_zrdt_argo_state *argo_state;

        argo_state = zrdt_get_msg_codec_state(c_state->zrdt_tlv_state.msg_stream);
        argo = argo_state->rx_argo;
    }

    /* Register zpn_broker_request_ack */
    if ((res = argo_register_structure(argo, zpn_broker_request_ack_description, bt_broker_request_ack_cb_from_c2c_conn,
                                       c_state))) {
        ZPN_LOG(AL_ERROR, "Could not register zpn_broker_request_ack for c2c connection %s", zpn_tlv_description(tlv));
        return res;
    }

    return ZPN_RESULT_NO_ERROR;
}

static int register_c2c(struct zpn_broker_client_fohh_state *c_state,
                        int64_t connect_us,
                        const char *cname,
                        const char *client_fqdn,
                        const char *log_msg)
{
    int res = ZPN_RESULT_NO_ERROR;

    if (!c_state) {
        ZPN_LOG(AL_CRITICAL, "No c_state");
        return ZPN_RESULT_ERR;
    }

    struct zpn_tlv *tlv = c_state_get_tlv(c_state);
    if (zpn_tlv_sanity_check(tlv) != ZPN_RESULT_NO_ERROR) {
        ZPN_LOG(AL_CRITICAL, "No tlv");
        return ZPN_RESULT_ERR;
    }

    c2c_register_rpc_callbacks(c_state);

    if (c_state->client_fqdn) {
        ZPN_FREE(c_state->client_fqdn);
        c_state->client_fqdn = NULL;
    }

    if (c_state->endpoint_cname) {
        ZPN_FREE(c_state->endpoint_cname);
        c_state->endpoint_cname = NULL;
    }

    struct argo_inet *client_ip = &c_state->c2c_ip.reserved_ip;
    const char *location_hint = c_state->c2c_ip.location_hint;

    if (NULL == client_fqdn || 0 == strlen(client_fqdn) || NULL == cname) {
        ZPN_DEBUG_C2C("Received empty fqdn or cname: client_fqdn=%s cname=%s %s",
                      client_fqdn, cname, zpn_tlv_description(tlv));
        c_state->fqdn_registered = FQDN_NOT_REGISTERED;
        c_state->fqdn_register_error = C2C_FQDN_EMPTY;
        c_state->log.fqdn_registered = FQDN_NOT_REGISTERED;
        c_state->log.fqdn_register_error = c2c_result_string(C2C_FQDN_EMPTY, C2C_SUCCESS_FULL);
        c_state->log.c2c_fqdn = c_state->client_fqdn;

        zpn_send_zpn_client_app_registration_notification(
            tlv, zpn_tlv_conn_incarnation(tlv), client_fqdn, client_ip, location_hint,
            c2c_result_string(C2C_FQDN_EMPTY, C2C_SUCCESS_EMPTY));
        return ZPN_RESULT_NO_ERROR;
    }

    // always store new client_fqdn
    c_state->client_fqdn = ZPN_STRDUP(client_fqdn, strlen(client_fqdn));
    c_state->log.c2c_fqdn = c_state->client_fqdn;

    c_state->endpoint_cname = ZPN_STRDUP(cname, strlen(cname));

    /* ZCC should not send C2C registration for partner login. Add a check on broker for the same.
     * If broker receives C2C registration from partner client type which is not expected, just discard
     * the message and don't send notification with error code to the client. */
    if (c_state->client_type == zpn_client_type_zapp_partner) {
        ZPN_DEBUG_C2C("Partner client type is not allowed for c2c %s", zpn_tlv_description(tlv));
        c_state->fqdn_registered = FQDN_NOT_REGISTERED;
        c_state->fqdn_register_error = C2C_PARTNERS_NOT_ALLOWED;
        c_state->log.fqdn_registered = FQDN_NOT_REGISTERED;
        c_state->log.fqdn_register_error = c2c_result_string(C2C_PARTNERS_NOT_ALLOWED, C2C_SUCCESS_FULL);
        c_state->log.c2c_fqdn = c_state->client_fqdn;

        return ZPN_RESULT_NO_ERROR;
    }

    if (!c2c_is_enabled_on_broker()) {
        ZPN_DEBUG_C2C("c2c: broker is not enabled. %s", zpn_tlv_description(tlv));

        c_state->fqdn_registered = FQDN_NOT_REGISTERED;
        c_state->fqdn_register_error = C2C_BROKER_NOT_ENABLED;
        c_state->log.fqdn_registered = FQDN_NOT_REGISTERED;
        c_state->log.fqdn_register_error = c2c_result_string(C2C_BROKER_NOT_ENABLED, C2C_SUCCESS_FULL);

        zpn_send_zpn_client_app_registration_notification(
            tlv, zpn_tlv_conn_incarnation(tlv), client_fqdn, client_ip, location_hint,
            c2c_result_string(C2C_BROKER_NOT_ENABLED, C2C_SUCCESS_EMPTY));
        return ZPN_RESULT_NO_ERROR;
    }

    if (!c2c_is_enabled_for_customer(c_state->customer_gid)) {
        ZPN_DEBUG_C2C("c2c: customer %" PRId64 " is not enabled. %s", c_state->customer_gid, zpn_tlv_description(tlv));

        c_state->fqdn_registered = FQDN_NOT_REGISTERED;
        c_state->fqdn_register_error = C2C_CUSTOMER_NOT_ENABLED;
        c_state->log.fqdn_registered = FQDN_NOT_REGISTERED;
        c_state->log.fqdn_register_error = c2c_result_string(C2C_CUSTOMER_NOT_ENABLED, C2C_SUCCESS_FULL);

        zpn_send_zpn_client_app_registration_notification(
            tlv, zpn_tlv_conn_incarnation(tlv), client_fqdn, client_ip, location_hint,
            c2c_result_string(C2C_CUSTOMER_NOT_ENABLED, C2C_SUCCESS_EMPTY));
        return ZPN_RESULT_NO_ERROR;
    }

    // set the re-send  interval
    c_state->c2c_resend_interval_count = c2c_get_resend_interval_count();
    c_state->c2c_expiration_ttl_s = c2c_get_expiration_ttl();


    // take hash64 as string, 64 bits -> 16 hex chars +1 for \0.
    // if it fails, we continue anyway
    cityhash_as_string(c_state->hw_serial_id, c_state->hash_hardware_id, sizeof(c_state->hash_hardware_id));

    struct zpn_broker_connected_clients *connected_clients = &(broker.clients);
    pthread_mutex_lock(&(connected_clients->lock));

    struct zpn_c2c_fqdn *fqdn_obj = zpn_create_or_update_local_c2c_registration_locked(client_fqdn, c_state);
    if (NULL == fqdn_obj) {
        // somehow object was not created, bailed out
        c_state->fqdn_register_error = C2C_NOT_AVAILABLE;
        c_state->log.fqdn_register_error = c2c_result_string(C2C_NOT_AVAILABLE, C2C_SUCCESS_FULL);

        zpn_send_zpn_client_app_registration_notification(
            tlv, zpn_tlv_conn_incarnation(tlv), client_fqdn, client_ip, location_hint,
            c2c_result_string(C2C_NOT_AVAILABLE, C2C_SUCCESS_EMPTY));
        pthread_mutex_unlock(&(connected_clients->lock));
        return ZPN_RESULT_NO_ERROR;
    }

    /* policy check of client_fqdn */
    res = c2c_match_fqdn_against_policy(c_state->customer_gid, client_fqdn);

    c_state->fqdn_register_error = res;

    if (C2C_FQDN_MATCH != res) {
        ZPN_DEBUG_C2C("c2c: %s: cn=%s: fqdn=%s registration rejected by policy, error=%s",
                      zpn_tlv_description(tlv), zpn_tlv_peer_cn(tlv), client_fqdn, c2c_result_string(res, C2C_SUCCESS_FULL));

        // reject here
        zpn_send_zpn_client_app_registration_notification(
            tlv, zpn_tlv_conn_incarnation(tlv), client_fqdn, client_ip, location_hint,
            c2c_result_string(res, C2C_SUCCESS_EMPTY));

        c_state->fqdn_registered = FQDN_NOT_REGISTERED;
        c_state->log.fqdn_registered = FQDN_NOT_REGISTERED;
        c_state->log.fqdn_register_error = c2c_result_string(res, C2C_SUCCESS_FULL);

        /*
         * when the mstch fqdn against policy fails. unregister the c_state.
         * This step prevents the c2c_row_callback_deferred from iterating on state c_states.
         */
        c2c_remove_registration_locked(c_state);

        pthread_mutex_unlock(&(connected_clients->lock));
        return ZPN_RESULT_NO_ERROR;
    }

    c_state->fqdn_registered = FQDN_REGISTERED;

    res = zpn_broker_dispatch_send_c2c_app_registration(c_state->customer_gid,
                                                        connect_us,
                                                        zpn_cloud_adjusted_epoch_s() + c_state->c2c_expiration_ttl_s,
                                                        cname,
                                                        1,  // alive
                                                        client_fqdn,
                                                        NULL,  // ip
                                                        c_state->hash_hardware_id,
                                                        log_msg);
    if (ZPN_RESULT_NO_ERROR != res) {
        ZPN_LOG(AL_ERROR, "cannot register c2c %s", zpn_result_string(res));
    }


    pthread_mutex_unlock(&(connected_clients->lock));

    // log registration state
    c_state->log.fqdn_registered = c_state->fqdn_registered;
    c_state->log.fqdn_register_error = c2c_result_string(res, C2C_SUCCESS_FULL);

    ZPN_DEBUG_C2C("c2c: %s: cn=%s: fdqn=%s registration status=%s",
                  zpn_tlv_description(tlv), zpn_tlv_peer_cn(tlv),
                  client_fqdn, c2c_result_string(res, C2C_SUCCESS_FULL));

    // send the notificaton
    zpn_send_zpn_client_app_registration_notification(
        tlv, zpn_tlv_conn_incarnation(tlv),
        client_fqdn, client_ip, location_hint, c2c_result_string(res, C2C_SUCCESS_EMPTY));

    return res;
}

/*
 * callback for private broker app registration
 */
int c2c_client_private_broker_registration_cb(void *argo_cookie_ptr,
                                              void *argo_structure_cookie_ptr,
                                              struct argo_object *object)
{
    struct zpn_broker_client_fohh_state *c_state = argo_structure_cookie_ptr;
    struct zpn_broker_dispatcher_app_registration *req = object->base_structure_void;
    struct zpn_broker_connected_clients *connected_clients = &(broker.clients);

    if (!c_state) {
        ZPN_LOG(AL_CRITICAL, "Null c_state");
        return ZPN_RESULT_NO_ERROR;
    }

    argo_c2c_dump(object, "Rx", c_state);

    if (zpn_meta_transaction_collection) {
        argo_log_structure_immediate(zpn_meta_transaction_collection,
                                     argo_log_priority_info,
                                     0,
                                     "PBToBrkC2CReg",
                                     zpn_broker_dispatcher_app_registration_description,
                                     req);
    }

    if (!req->client_fqdn) {
        ZPN_LOG(AL_ERROR, "Invalid FQDN is passed");
        // need to send notication back to PB
        struct zpn_tlv *tlv = c_state_get_tlv(c_state);
        if (!tlv) {
            ZPN_LOG(AL_CRITICAL, "%s: No tlv", c_state->tunnel_id);
            return ZPN_RESULT_NO_ERROR;
        }

        zpn_send_zpn_client_app_registration_notification(
            tlv, zpn_tlv_conn_incarnation(tlv), NULL, &c_state->c2c_ip.reserved_ip,
            c_state->c2c_ip.location_hint, c2c_result_string(C2C_INVALID_FQDN, C2C_SUCCESS_EMPTY));
        return ZPN_RESULT_NO_ERROR;
    }

    struct argo_inet reserved_ip;
    for (int i = 0; i < req->fqdn_count; i++) {

        if (req->client_fqdn[i]) { /* Ensure we dont try to send null client_fqdn to dispatcher */
            if (argo_string_to_inet(req->client_fqdn[i], &reserved_ip) == ARGO_RESULT_NO_ERROR && reserved_ip.length != 0) {
                zpn_broker_dispatch_send_c2c_app_registration(c_state->customer_gid,
                                                              c_state->connect_us,
                                                              req->expire_s,
                                                              req->client_cname,
                                                              req->alive,
                                                              NULL,  // fqdn
                                                              &reserved_ip,
                                                              req->machine_id,
                                                              "PB new");

                if (!c_state->endpoint_cname || strcmp(req->client_cname, c_state->endpoint_cname) != 0) {
                    ZPN_FREE(c_state->endpoint_cname);
                    c_state->endpoint_cname = ZPN_STRDUP(req->client_cname, strlen(req->client_cname));
                }

                c2c_register_rpc_callbacks(c_state);
            } else {
                register_c2c(c_state, req->connect_us, req->client_cname, req->client_fqdn[i], "PB new");
            }
        } else {

            /* Treat NULL same as empty string above by doing a c2c_register */
            ZPN_LOG(AL_CRITICAL, "%s: Null client_fqdn[%d], count: %d", c_state->tunnel_id, i, req->fqdn_count);
            register_c2c(c_state, req->connect_us, req->client_cname, req->client_fqdn[i], "PB new");
        }
    }

    // store the original cname , PB one is stored by connection callback
    // there could be multiple connections with same cname
    int res = ZPN_RESULT_NO_ERROR;

    if (req->client_cname != NULL) {
        ZPN_DEBUG_C2C("pb add cname=%s %s", req->client_cname, c_state->tunnel_id);

        const size_t client_cname_len = strlen(req->client_cname);
        pthread_mutex_lock(&(connected_clients->lock));

        if (!argo_hash_lookup(connected_clients->cnames, req->client_cname, client_cname_len, c_state)) {
            res = argo_hash_store(connected_clients->cnames, req->client_cname, client_cname_len, 0, c_state);
            if (res) {
                ZPN_LOG(AL_ERROR, "Could not store in cnames table: %s", zpn_result_string(res));
            }
        }

        pthread_mutex_unlock(&(connected_clients->lock));
    }

    return res;
}

int c2c_client_broker_registration_cb(void *argo_cookie_ptr,
                                      void *argo_structure_cookie_ptr,
                                      struct argo_object *object)
{
    struct zpn_broker_client_fohh_state *c_state = argo_structure_cookie_ptr;
    struct zpn_client_broker_app_registration *req = object->base_structure_void;

    if (!c_state) {
        ZPN_LOG(AL_CRITICAL, "No c_state");
        return ZPN_RESULT_ERR;
    }

    if (zpn_debug_get(ZPN_DEBUG_C2C_IDX)) {
        char buf[2000];
        if (argo_object_dump(object, buf, sizeof(buf), NULL, 0) == ARGO_RESULT_NO_ERROR) {
            struct zpn_tlv *tlv = c_state_get_tlv(c_state);
            ZPN_LOG(AL_DEBUG, "%s: %s: Rx client to client app registration: %s", zpn_tlv_description(tlv),
                    zpn_tlv_peer_cn(tlv), buf);
        }
    }

    return register_c2c(c_state, c_state->connect_us, c_state->cname, req->client_fqdn, "new");
}

static void print_fqdn(struct zpath_debug_state *request_state, int64_t now, struct zpn_c2c_fqdn *fqdn_obj)
{
    char ip[ARGO_INET_ADDRSTRLEN] = {0};
    char connect_us[ARGO_LOG_GEN_TIME_STR_LEN] = {0};

    for (int i = 0; i < (1 << fqdn_obj->cstates->table_size); i++) {
        struct argo_hash_element *walk;

        LIST_FOREACH(walk, &(fqdn_obj->cstates->table[i]), hash_bucket) {
            struct zpn_broker_client_fohh_state *c_state = (void *)walk->object;

            if (c_state->c2c_ip.reserved_ip.length) {
                argo_inet_generate(ip, &c_state->c2c_ip.reserved_ip);
            } else {
                ip[0] = '\0';
            }

            argo_log_gen_time(c_state->connect_us, connect_us, sizeof(connect_us), 0, 0);

            ZDP("c_gid=%" PRId64 " cname=%s fqdn=%s registered=%d code=%s tlv=%s age_s=%" PRId64
                " connect_us=%s tunnel_id=%s hash_hardware_id=%s incarnation=%" PRId64 " %s\n",
                c_state->customer_gid, c_state->endpoint_cname, c_state->client_fqdn, c_state->fqdn_registered,
                c2c_result_string(c_state->fqdn_register_error, C2C_SUCCESS_FULL), zpn_tlv_type_str(c_state->tlv_type),
                (now - c_state->start_epoch_us) / 1000000, connect_us, c_state->tunnel_id, c_state->hash_hardware_id,
                c_state->incarnation, zpn_tlv_description(c_state_get_tlv(c_state)) );
        }
    }
}

static void print_cnames(struct zpath_debug_state *request_state,
                         char *cname,
                         int64_t now,
                         struct zpn_broker_client_fohh_state *zc,
                         int overflow)
{
    char ip[ARGO_INET_ADDRSTRLEN] = {0};
    if (zc->c2c_ip.reserved_ip.length) {
        argo_inet_generate(ip, &zc->c2c_ip.reserved_ip);
    }

    char connect_us[ARGO_LOG_GEN_TIME_STR_LEN] = {0};
    argo_log_gen_time(zc->connect_us, connect_us, sizeof(connect_us), 0, 0);

    ZDP("c_gid=%" PRId64 " cname=%s fqdn=%s tlv=%s age_s=%" PRId64 " registered=%d code=%s"
        " connect_us=%s tunnel_id=%s hash_hardware_id=%s incarnation=%" PRId64 " %s %s\n",
        zc->customer_gid, cname, zc->client_fqdn, zpn_tlv_type_str(zc->tlv_type), (now - zc->start_epoch_us) / 1000000,
        zc->fqdn_registered, c2c_result_string(zc->fqdn_register_error, C2C_SUCCESS_FULL), connect_us, zc->tunnel_id,
        zc->hash_hardware_id, zc->incarnation, overflow ? "overflow" : "", zpn_tlv_description(c_state_get_tlv(zc)));

    if (zc->public_broker_client) {
        ZDP("     PB client %s - %s\n", zpn_tlv_type_str(zc->public_broker_client->outbound_tlv_type),
            zc->public_broker_client->peer_cn);
    }
    if (zc->public_broker_client_zrdt) {
        ZDP("     PB client %s - %s\n", zpn_tlv_type_str(zc->public_broker_client_zrdt->outbound_tlv_type),
            zc->public_broker_client_zrdt->peer_cn);
    }
}

/*
 * /zpn/broker/c2c/registrations
 */
int zpn_broker_c2c_registrations_dump(struct zpath_debug_state *request_state,
                                      const char **query_values,
                                      int query_value_count,
                                      void *cookie) {
    struct zpn_broker_connected_clients *connected_clients;

    connected_clients = &(broker.clients);

    int count = 0;
    int i;
    struct zpn_c2c_fqdn *fqdn_obj;
    int64_t now = epoch_us();

    if (!query_values[0] || !query_values[1]) {
        if (!zpn_broker_is_dev_environment()) {
            ZDP("Require id and type\n");
            // in dev environmetn will print all of them if fqdn is not specified

        } else {
            pthread_mutex_lock(&(connected_clients->lock));
            for (i = 0; i < (1 << connected_clients->client_fqdns->table_size); i++) {
                struct argo_hash_element *walk;
                LIST_FOREACH(walk, &(connected_clients->client_fqdns->table[i]), hash_bucket) {
                    count++;

                    fqdn_obj = (void *)walk->object;
                    print_fqdn(request_state, now, fqdn_obj);
                }
            }
            pthread_mutex_unlock(&(connected_clients->lock));
            ZDP("Total: %d\n", count);
        }

        return ZPATH_RESULT_NO_ERROR;
    }

    pthread_mutex_lock(&(connected_clients->lock));

    if (!strcmp(query_values[1],"customer")) {
        uint64_t hash;
        struct argo_hash_element *walk;
        int64_t customer_gid = atol(query_values[0]);

        hash = (1ll << connected_clients->client_fqdns_per_customer->table_size) - 1;
        hash &= CityHash64((const char *)&customer_gid, sizeof(customer_gid));

        LIST_FOREACH(walk, &(connected_clients->client_fqdns_per_customer->table[hash]), hash_bucket) {
            struct zpn_broker_client_fohh_state *c_state = walk->object;
            print_cnames(request_state, c_state->cname, now, c_state, 0);
        }
    }
    else if (!strcmp(query_values[1],"fqdn")) {
        // fqdn is provided
        fqdn_obj = argo_hash_lookup(connected_clients->client_fqdns, query_values[0], strlen(query_values[0]), NULL);
        if (fqdn_obj) {
            print_fqdn(request_state, now, fqdn_obj);
        } else {
            ZDP("fqdn %s not found\n", query_values[1]);
        }
    } else {
        ZDP("Invalid type: %s\n", query_values[1]);
    }
    pthread_mutex_unlock(&(connected_clients->lock));

    return ZPN_RESULT_NO_ERROR;
}


/*
 * /zpn/broker/c2c/cnames
 */
int zpn_broker_c2c_cnames_dump(struct zpath_debug_state *request_state,
                               const char **query_values,
                               int query_value_count,
                               void *cookie) {
    struct zpn_broker_connected_clients *connected_clients;

    connected_clients = &(broker.clients);

    int count = 0;
    int i;
    int64_t now = epoch_us();
    static char str[1000];

    if (!query_values[0]) {
        if (!zpn_broker_is_dev_environment()) {
            ZDP("Require fqdn\n");
            // in dev environment will print all of them if fqdn is not specified
            return ZPATH_RESULT_NO_ERROR;
        }
    }
    pthread_mutex_lock(&(connected_clients->lock));

    if (query_values[0]) {
        struct zpn_c2c_fqdn *fqdn_obj;

        fqdn_obj = argo_hash_lookup(connected_clients->client_fqdns, query_values[0], strlen(query_values[0]), NULL);

        if (fqdn_obj) {
            print_fqdn(request_state, epoch_us(), fqdn_obj);
        } else {
            ZDP("%s not found\n", query_values[0]);
        }

    } else {
        for (i = 0; i < (1 << connected_clients->cnames->table_size); i++) {
            struct argo_hash_element *walk;
            LIST_FOREACH(walk, &(connected_clients->cnames->table[i]), hash_bucket) {

                struct zpn_broker_client_fohh_state *zc = (void *)walk->object;

                memcpy(str, walk->data, walk->data_len);
                str[walk->data_len] = 0;
                if (zc->endpoint_cname && zc->fqdn_registered) {
                    print_cnames(request_state, str, now, zc, 0);
                    count++;
                }
            }
        }
        ZDP("Total: %d\n", count);
    }
    pthread_mutex_unlock(&(connected_clients->lock));

    return ZPN_RESULT_NO_ERROR;
}

/*
 * add dummy fqdn entry for load testing
 */

int zpn_broker_c2c_fqdn_mock_entry(struct zpath_debug_state *request_state,
                                   const char **query_values,
                                   int query_value_count,
                                   void *cookie) {
    struct zpn_broker_connected_clients *connected_clients;

    static int counter = 1;
    char tmp[1001];

    connected_clients = &(broker.clients);

    struct zpn_broker_client_fohh_state *c_state;

    if (!zpn_broker_is_dev_environment()) {
        ZDP("This features is not supported in production\n");
        return ZPATH_RESULT_NO_ERROR;
    }

    if (!query_values[0] || !query_values[1] || !query_values[2]) {
        ZDP("Require fqdn,customer_id and cname\n");
        // in dev environmetn will print all of them if fqdn is not specified
        return ZPATH_RESULT_NO_ERROR;
    }

    // this c_state wont be freed, the broker needs to be restarted
    // c_state = (struct zpn_broker_client_fohh_state *)ZPN_CALLOC(sizeof(struct zpn_broker_client_fohh_state));
    c_state = zpn_broker_c_state_alloc();
    if (!c_state) {
        ZDP("failed to allocated memory\n");
        return ZPN_RESULT_NO_ERROR;
    }
    c_state->start_epoch_us = epoch_us();

    if (zpn_broker_client_state_init(c_state, zpn_client_type_zapp, zpn_tunnel_auth_zapp, zpn_fohh_tlv)) {
        ZDP("failed to initate c_state \n");
        zpn_broker_c_state_free(c_state);
        return ZPN_RESULT_NO_ERROR;
    }

    c_state->customer_gid = strtoll(query_values[1], NULL, 0);

    if (c_state->customer_gid == 0) {
        ZDP("customer_gid is invalid\n");
        zpn_broker_c_state_free(c_state);
        return ZPN_RESULT_NO_ERROR;
    }

    c_state->fqdn_registered = FQDN_REGISTERED;

    snprintf(tmp, 1000, "%d.%s", counter, query_values[0]);
    c_state->client_fqdn = ZPN_STRDUP(tmp, strlen(tmp));
    snprintf(tmp, 1000, "%d.%s", counter, query_values[2]);
    c_state->endpoint_cname = ZPN_STRDUP(tmp, strlen(tmp));

    counter++;

    pthread_mutex_lock(&(connected_clients->lock));

    // check and prevent creating dup mock entries for qa, multiple entries are ok for broker
    if (NULL !=
        argo_hash_lookup(connected_clients->client_fqdns, c_state->client_fqdn, strlen(c_state->client_fqdn), NULL)) {
        ZDP("skip fqdn=%s \n", c_state->client_fqdn);

        goto DONE;
    }

    zpn_create_or_update_local_c2c_registration_locked(c_state->client_fqdn, c_state);

    ZDP("added fqdn=%s cname=%s customer_id=%" PRId64 "\n", c_state->client_fqdn, c_state->endpoint_cname,
        c_state->customer_gid);

DONE:

    pthread_mutex_unlock(&(connected_clients->lock));
    // coverity[leaked_storage]
    return ZPN_RESULT_NO_ERROR;
}


int64_t zpn_broker_c2c_regex_get_avg_eval_time_us(void)
{
    return ( g_c2c_bypass_eval_total_calls > 0) ? ( g_c2c_bypass_eval_total_time_us / g_c2c_bypass_eval_total_calls) : 0;
}

/*
* zpn_broker_dispatch_c2c_regex_match_check_req - return 0 if no regex match or error or feature disabled
* Returns 1 when feature is enabled and regex matched
* This function will only be triggred by PSE.
*/
int zpn_broker_dispatch_c2c_regex_match_check_req(const char *mtunnel_id, const char *domain)
{

    int64_t start_us = epoch_us();
    int64_t end_us = 0;
    int64_t elapsed_us = 0;
    int res = 0;
    int match = C2C_FQDN_NO_MATCH;

    if (ZPN_BROKER_IS_PRIVATE()) {

        struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();

        if (!mtunnel_id) {
            ZPN_LOG(AL_ERROR, "Invalid NULL zpn_broker_request mtunnel_id");
            goto done;
        }

        if (!domain) {
            ZPN_LOG(AL_ERROR, "%s: Invalid NULL zpn_broker_request domain", mtunnel_id);
            goto done;
        }

        /* Check c2c capablity is enabled for this customer */
        if (!c2c_is_enabled_for_customer(gs->customer_id)) {
            ZPN_DEBUG_C2C("%s: C2C is disabled for customer_id=%"PRId64, mtunnel_id, gs->customer_id);
            goto done;
        }

        /* Check c2c capablity is enabled for this system */
        if (!c2c_is_enabled_on_broker()) {
            ZPN_DEBUG_C2C("%s: C2C is disabled for system GID: %"PRId64" for customer_id=%"PRId64, mtunnel_id, ZPN_BROKER_GET_GID(), gs->customer_id);
            goto done;
        }

        /* Check PSE is not in active DR mode */
        if (zpn_is_dr_mode_active()) {
            ZPN_DEBUG_C2C("%s: PSE DR mode is active for pse_gid=%"PRId64, mtunnel_id, ZPN_BROKER_GET_GID());
             goto done;
        }

        /* Check PSE  is not in resiliency mode */
        /* Check resiliency mode first in DR mode because in DR mode the resiliency data structures such as broker control connection from PSE is NULL */
        if (zpn_is_pse_resiliency_enabled() && zpn_pse_control_connection_is_cloud_unreachable()) {
            ZPN_DEBUG_C2C("%s: PSE resiliency is active for pse_gid=%"PRId64, mtunnel_id, ZPN_BROKER_GET_GID());
             goto done;
        }

        /*  Match c2c hostname regex if present */
        match = c2c_match_fqdn_against_policy(gs->customer_id, domain);
        res = (match == C2C_FQDN_MATCH) ? 1 : 0;
    }

done:

    end_us = epoch_us();
    elapsed_us = end_us - start_us;

    __sync_fetch_and_add_8(&g_c2c_bypass_eval_total_time_us, elapsed_us);
    __sync_fetch_and_add_8(&g_c2c_bypass_eval_total_calls, 1);

    ZPN_DEBUG_C2C("%s: c2c hostname domain = %s, match = %d, res = %d, elapsed_us: %"PRId64,
                  (mtunnel_id != NULL) ? mtunnel_id : "Unknown mtunnel",
                  (domain != NULL) ? domain : "Null req", match, res, zpn_broker_c2c_regex_get_avg_eval_time_us());

    return res;
}

/*
* zpn_broker_dispatch_c2c_regex_match_check - return 0 if no regex match or error or feature disabled
* Returns 1 when feature is enabled and regex matched
* This function will only be triggred by local dispatcher on a PSE
*/
int zpn_broker_dispatch_c2c_regex_match_check(const struct zpn_broker_request *req)
{
    int res = 0;

    /* Sanity checks, make sure broker request is valid */
    if (!req) {
        ZPN_LOG(AL_ERROR, "Invalid NULL zpn_broker_request");
        return 0;
    }

    if (ZPN_BROKER_IS_PRIVATE()) {

        struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();

        /* Check c2c regex bypass is enabled for this customer or system */
        if (!c2c_is_bypass_local_dispatch_by_regex_enabled_for_customer(gs->customer_id)) {
            ZPN_DEBUG_C2C("%s: C2C regex local dispatch bypass is disabled for customer_id=%"PRId64, req->mtunnel_id, gs->customer_id);
            return 0;
        }

        res = zpn_broker_dispatch_c2c_regex_match_check_req(req->mtunnel_id, req->domain);
    }
    return res;
}

/* Init pre-compiled c2c_regex cache for PSE */
int zpn_broker_c2c_regex_customer_cache_init(int64_t customer_gid)
{
    int res = ZPN_RESULT_ERR;

    zpn_c2c_regex_write_lock();
    if (!g_c2c_compiled_regex_cache.init_done) {
        g_c2c_compiled_regex_cache.customer_gid = customer_gid;
        g_c2c_compiled_regex_cache.init_done = 1;     /* Set cache itself as inited */
        g_c2c_compiled_regex_cache.compiled_re = NULL;
        ZPN_DEBUG_C2C("c2c-compiled regex cache init for customer: %"PRId64, customer_gid);
        zpn_c2c_regex_unlock();
        res = ZPN_RESULT_NO_ERROR;
    } else {
        ZPN_LOG(AL_ERROR, "c2c-compiled regex cache is already inited for customer: %"PRId64, customer_gid);
    }

    return res;
}

/* Callback to defer free pre_compiled_regex */
static void pre_compiled_regex_defer_cb(void *cookie1, void *cookie2)
{
    regex_t *re = cookie1;

    if (re) {
        ZPN_DEBUG_C2C("c2c-compiled regex cache entry:%p freed", re);
        regfree(re);
        ZPN_FREE(re);
    }
}

/* Schedule  freeing */
static void zpn_broker_c2c_regex_customer_cache_clear_slow(regex_t *pre_compiled_regex)
{
    if (pre_compiled_regex) {

        /* Defer free by 60-seconds */
        zevent_defer(pre_compiled_regex_defer_cb, pre_compiled_regex, NULL, 60*1000*1000);
    }
}

/* Update pre-compiled c2c_regex cache for PSE */
static int zpn_broker_c2c_regex_customer_cache_update(int64_t customer_gid, regex_t *compiled_re)
{
    int res = ZPN_RESULT_ERR;

    res = zpn_broker_c2c_regex_customer_cache_is_initilized(customer_gid);
    if (res != ZPN_RESULT_NO_ERROR) {
        ZPN_LOG(AL_ERROR, "c2c compiled regex cache not ready error: %s on update for customer: %"PRId64,
                zpn_result_string(res), customer_gid);
        goto done;
    }

    zpn_c2c_regex_write_lock();

    /* Free any old data from the cache first */
    /* Schedule a slow free of regex, to ensure all users have stopped using it  */
    if (g_c2c_compiled_regex_cache.compiled_re) {
        zpn_broker_c2c_regex_customer_cache_clear_slow(g_c2c_compiled_regex_cache.compiled_re);
        g_c2c_compiled_regex_cache.compiled_re = NULL;
        ZPN_DEBUG_C2C("c2c-compiled regex cache cleared for customer: %"PRId64, customer_gid);
    }
    g_c2c_compiled_regex_cache.compiled_re = compiled_re;
    ZPN_DEBUG_C2C("c2c-compiled regex cache updated with compiled_re: %p for customer: %"PRId64, compiled_re, customer_gid);
    res = ZPN_RESULT_NO_ERROR;

    zpn_c2c_regex_unlock();

 done:

   return res;
}

/* Clear pre-compiled c2c_regex cache for PSE */
static int zpn_broker_c2c_regex_customer_cache_clear(int64_t customer_gid)
{
    /* Clear is simply a NULL cache update */
    return zpn_broker_c2c_regex_customer_cache_update(customer_gid, NULL);
}

/* pre-compiled c2c-regex cache get */
static int zpn_broker_c2c_regex_customer_cache_get(int64_t customer_gid,  int *init_done, regex_t **compiled_re)
{
    int res = ZPN_RESULT_ERR;

    if (!compiled_re) {
        ZPN_LOG(AL_ERROR, "Invalid NULL compiled_re for customer: %"PRId64, customer_gid);
        res = ZPN_RESULT_BAD_ARGUMENT;
        goto done;
    }
    *compiled_re = NULL;

    if (!init_done) {
        ZPN_LOG(AL_ERROR, "Invalid NULL init_done for customer: %"PRId64, customer_gid);
        res = ZPN_RESULT_BAD_ARGUMENT;
        goto done;
    }

    /* Check if cache is ready */
    res = zpn_broker_c2c_regex_customer_cache_is_initilized(customer_gid);
    if (res != ZPN_RESULT_NO_ERROR) {
        *init_done = 0;
        /* Cache not ready */
        goto done;
    }

    zpn_c2c_regex_read_lock();
    *init_done = g_c2c_compiled_regex_cache.init_done; /* Cache ready to accept data for this customer */
    /* See if data is present in the cache */
    if (g_c2c_compiled_regex_cache.compiled_re ) {
        ZPN_DEBUG_C2C("c2c-compiled regex cache get found compiled_re: %p for customer: %"PRId64, *compiled_re, customer_gid);
        *compiled_re = g_c2c_compiled_regex_cache.compiled_re;
        res = ZPN_RESULT_NO_ERROR;
    } else {
        *compiled_re = NULL;
        ZPN_DEBUG_C2C("c2c-compiled regex cache data get not found for customer: %"PRId64, customer_gid);
        res = ZPN_RESULT_NOT_FOUND;
    }
    zpn_c2c_regex_unlock();

done:

    return res;
}

/* is pre-compiled c2c regex cache initilized */
static int zpn_broker_c2c_regex_customer_cache_is_initilized(int64_t customer_gid)
{
    int res = ZPN_RESULT_NOT_READY;

    zpn_c2c_regex_read_lock();
    /* Cache is ready to accept data */
    if (g_c2c_compiled_regex_cache.init_done && (g_c2c_compiled_regex_cache.customer_gid == customer_gid)) {
        res = ZPN_RESULT_NO_ERROR;
        ZPN_DEBUG_C2C("c2c-compiled regex cache is initilized for customer: %"PRId64, customer_gid);
    }
    zpn_c2c_regex_unlock();

    return res;
}
