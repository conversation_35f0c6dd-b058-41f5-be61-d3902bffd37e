argo_parse_files(INPUT_FILES np_connector.c OUTPUT_DIR ${CMAKE_CURRENT_BINARY_DIR} OUTPUT_FILES_VAR generated_headers)

add_library(
    np_connector
    STATIC
    np_connector_lib.c
    np_connector.c
    np_connector_stats.c
    np_connector_bgp.c
    np_connector_config_override_desc.c
    np_connector_cfg_override_feature.c
    ${generated_headers}
)
target_link_libraries(np_connector PUBLIC npwg_lib zpn_enrollment np_lib zpn)
