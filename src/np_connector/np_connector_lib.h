/*
 * np_connector_lib.h. Copyright (C) 2024 Zscaler Inc, All Rights Reserved
 *
 */

#ifndef _NP_CONNECTOR_LIB_H_
#define _NP_CONNECTOR_LIB_H_

#include "zpath_lib/zpath_app.h"
#include "zpath_lib/zpath_assert.h"

#define NP_CONNECTOR_DEBUG_CFG_BIT            BIT_FLAG_U64(0)
#define NP_CONNECTOR_DEBUG_KEY_BIT            BIT_FLAG_U64(1)
#define NP_CONNECTOR_DEBUG_TABLE_BIT          BIT_FLAG_U64(2)
#define NP_CONNECTOR_DEBUG_CONFIG_BIT         BIT_FLAG_U64(3)
#define NP_CONNECTOR_DEBUG_BGP_BIT            BIT_FLAG_U64(4)

#define NP_CONNECTOR_DEBUG_LOG_NAMES {  \
        "cfg",                          \
        "key",                          \
        "table",                        \
        "config",                       \
        "bgp",                          \
        NULL                            \
}

extern struct zpath_allocator np_connector_allocator;
extern struct argo_log_collection *np_connector_event_collection;

#define NP_CONNECTOR_RESULT_NO_ERROR          Z<PERSON>TH_RESULT_NO_ERROR          /* AKA success */
#define NP_CONNECTOR_RESULT_ERR               ZPATH_RESULT_ERR               /* Generic error, when none other are appropriate */
#define NP_CONNECTOR_RESULT_NOT_FOUND         ZPATH_RESULT_NOT_FOUND         /* Could not find what was requested. */
#define NP_CONNECTOR_RESULT_NO_MEMORY         ZPATH_RESULT_NO_MEMORY         /* Could not allocate memory */
#define NP_CONNECTOR_RESULT_CANT_WRITE        ZPATH_RESULT_CANT_WRITE        /* Failure to write (output callback failed?) */
#define NP_CONNECTOR_RESULT_ERR_TOO_LARGE     ZPATH_RESULT_ERR_TOO_LARGE     /* Requested data doesn't fit in space provided */
#define NP_CONNECTOR_RESULT_BAD_ARGUMENT      ZPATH_RESULT_BAD_ARGUMENT      /* Asked for something wrong. */
#define NP_CONNECTOR_RESULT_INSUFFICIENT_DATA ZPATH_RESULT_INSUFFICIENT_DATA /* Was not provided enough data to perform operation */
#define NP_CONNECTOR_RESULT_NOT_IMPLEMENTED   ZPATH_RESULT_NOT_IMPLEMENTED   /* Yes, for features that are not yet implemented. */
#define NP_CONNECTOR_RESULT_BAD_DATA          ZPATH_RESULT_BAD_DATA          /* Tried to parse data, but format seemed wrong. */
#define NP_CONNECTOR_RESULT_WOULD_BLOCK       ZPATH_RESULT_WOULD_BLOCK       /* Attempting operation would result in blocking. Bad, naughty blocking. */
#define NP_CONNECTOR_RESULT_BAD_STATE         ZPATH_RESULT_BAD_STATE         /* Encountered bad internal state while attempting operation */
#define NP_CONNECTOR_RESULT_INCOMPLETE        ZPATH_RESULT_INCOMPLETE
#define NP_CONNECTOR_RESULT_ASYNCHRONOUS      ZPATH_RESULT_ASYNCHRONOUS
#define NP_CONNECTOR_RESULT_EXCESS_DYN_FIELDS ZPATH_RESULT_EXCESS_DYN_FIELDS /* The RPC has too many dynamic fields */
#define NP_CONNECTOR_RESULT_NOT_READY         ZPATH_RESULT_NOT_READY         /* Requested DB is not ready. */

#define NP_CONNECTOR_MALLOC(x) zpath_malloc(&np_connector_allocator, x, __LINE__, __FILE__)
#define NP_CONNECTOR_FREE(x) zpath_free(x, __LINE__, __FILE__)
#define NP_CONNECTOR_FREE_SLOW(x) zpath_free_slow(x, __LINE__, __FILE__)
#define NP_CONNECTOR_CALLOC(x) zpath_calloc(&np_connector_allocator, x, __LINE__, __FILE__)
#define NP_CONNECTOR_STRDUP(x, y) zpath_strdup(&np_connector_allocator, x, y, __LINE__, __FILE__)
#define NP_CONNECTOR_SAFE_STRDUP(x) ZDX_STRDUP(x, strlen(x) + 1)

const char *np_connector_result_string(int result);

extern uint64_t np_connector_debug_log;
extern const char *np_connector_debug_log_names[];

#define NP_CONNECTOR_LOG(priority, format...) if (1) ARGO_LOG(np_connector_event_collection, priority, "np_connector", ##format)
#define NP_CONNECTOR_DEBUG_LOG(condition, format...) if (1) ARGO_DEBUG_LOG(condition, np_connector_event_collection, argo_log_priority_debug, "np_connector", ##format)
#define NP_CONNECTOR_DEBUG_CFG(format...) NP_CONNECTOR_DEBUG_LOG(np_connector_debug_log & NP_CONNECTOR_DEBUG_CFG_BIT, ##format)
#define NP_CONNECTOR_DEBUG_KEY(format...) NP_CONNECTOR_DEBUG_LOG(np_connector_debug_log & NP_CONNECTOR_DEBUG_KEY_BIT, ##format)
#define NP_CONNECTOR_DEBUG_TABLE(format...) NP_CONNECTOR_DEBUG_LOG(np_connector_debug_log & NP_CONNECTOR_DEBUG_TABLE_BIT, ##format)
#define NP_CONNECTOR_DEBUG_CONFIG(format...) NP_CONNECTOR_DEBUG_LOG(np_connector_debug_log & NP_CONNECTOR_DEBUG_CONFIG_BIT, ##format)
#define NP_CONNECTOR_DEBUG_BGP(format...) NP_CONNECTOR_DEBUG_LOG(np_connector_debug_log & NP_CONNECTOR_DEBUG_BGP_BIT, ##format)

#define NP_CONNECTOR_ASSERT_SOFT(condition, format...)   \
    ZPATH_ASSERT_SOFT(condition, "np_connector", 0, "NP Connector", ##format)

#define NP_CONNECTOR_ASSERT_HARD(condition, format...)   \
    ZPATH_ASSERT_HARD(condition, "np_connector", 0, "NP Connector", ##format)

int np_connector_lib_init(struct argo_log_collection *event_log);

#endif // _NP_CONNECTOR_LIB_H_
