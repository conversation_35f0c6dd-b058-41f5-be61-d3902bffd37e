/*
 * np_connector_cfg_override_feature.c. Copyright (C) 2025 Zscaler Inc. All Rights Reserved.
 *
 */

#include "zpath_lib/zpath_config_override.h"
#include "np_connector/np_connector.h"
#include "np_connector/np_connector_cfg_override_feature.h"

int64_t
np_connector_cfg_override_get_multilan_subnets_enable()
{
    int64_t config_value = 0;

    config_value = zpath_config_override_get_config_int(CONFIG_FEATURE_NP_MULTI_LAN_SUBNET_ENABLE,
                                                        &config_value,
                                                        DEFAULT_CONFIG_FEATURE_NP_MULTI_LAN_SUBNET_ENABLE,
                                                        np_connector_get_connector_gid(),
                                                        np_connector_get_connector_group_gid(),
                                                        np_connector_get_customer_gid(),
                                                        (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                        0);
    return config_value ? 1 : 0;
}
