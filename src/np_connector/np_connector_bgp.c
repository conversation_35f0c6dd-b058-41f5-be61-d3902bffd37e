/*
 * np_connector_bgp.c . Copyright (C) 2025 Zscaler Inc, All Rights Reserved
 */

#include "argo/argo.h"
#include "zthread/zthread.h"
#include "zpath_lib/zpath_debug.h"

#include "npwg_lib/npwg_provider.h"

#include "np_lib/np_bgp.h"
#include "np_lib/np_bgp_config.h"
#include "np_lib/np_bgp_connectors_config.h"
#include "np_lib/np_bgp_gateways_config.h"
#include "np_lib/np_bgp_connector_session_config.h"
#include "np_lib/np_client_subnets.h"
#include "np_lib/np_lan_subnets.h"
#include "np_lib/np_connector_groups_lan_subnets_mapping.h"
#include "np_lib/np_frr_utils.h"

#include "np_connector/np_connector_lib.h"
#include "np_connector/np_connector_bgp.h"

#include "np_connector/np_connector_cfg_override_feature.h"

struct np_connector_bgp_state {
    int64_t np_connector_gid;
    int64_t np_connector_group_gid;
    int64_t customer_gid;
    struct wally *np_wally;

    struct np_bgp_state *bgp_state;

    int64_t init_us;

    uint8_t redundancy_feature_enabled:1, // feature flag
            redundant_mode_enabled:1; // np connector group level config

};

static struct np_connector_bgp_state state;

/* Forward declaration of the function in np_connector.h, ensure their signature match! */
void np_connector_add_or_update_gateway_from_another_thread(int64_t tenant_gateway_gid);

static int dump_peer_config_walk(void *cookie,
                                 void *value,
                                 void *key __attribute__((unused)),
                                 size_t key_len __attribute__((unused)))
{
    struct zpath_debug_state *request_state = (struct zpath_debug_state *)cookie;
    struct argo_object *object = (struct argo_object *)value;
    if (!object) return NP_CONNECTOR_RESULT_NO_ERROR;
    struct np_bgp_peer_config *peer = object->base_structure_void;
    if (!peer) return NP_CONNECTOR_RESULT_NO_ERROR;
    char jsonout[1000];
    if (ARGO_RESULT_NO_ERROR == argo_structure_dump(np_bgp_peer_config_description, peer, jsonout, sizeof(jsonout), NULL, 1)) {
        ZDP("peer config\n%s\n", jsonout);
    }
    return NP_CONNECTOR_RESULT_NO_ERROR;
}

static int dump_subnet_walk(void *cookie,
                            void *value,
                            void *key __attribute__((unused)),
                            size_t key_len __attribute__((unused)))
{
    struct zpath_debug_state *request_state = (struct zpath_debug_state *)cookie;
    struct argo_inet *subnet = (struct argo_inet *)value;
    char str[ARGO_INET_ADDRSTRLEN];

    ZDP("%s\n", argo_inet_generate(str ,subnet));

    return NP_CONNECTOR_RESULT_NO_ERROR;
}

static inline void dump_rows(struct argo_object *prev_row, struct argo_object *curr_row, const char *caller)
{
    char dump[1024] = {0};
    if (prev_row && (argo_object_dump(prev_row, dump, sizeof(dump), NULL, 0) == ARGO_RESULT_NO_ERROR)) {
        NP_CONNECTOR_LOG(AL_INFO, "%s Previous row: %s", caller, dump);
    }

    if (argo_object_dump(curr_row, dump, sizeof(dump), NULL, 0) == ARGO_RESULT_NO_ERROR) {
        NP_CONNECTOR_LOG(AL_INFO, "%s Current row: %s", caller, dump);
    }
}

static int
np_bgp_dump_state(struct zpath_debug_state  *request_state,
                  const char                **query_values __attribute__((unused)),
                  int                       query_value_count __attribute__((unused)),
                  void                      *cookie __attribute__((unused)))
{
    ZDP("np_connector_gid: %"PRId64"\n", state.np_connector_gid);
    ZDP("np_connector_group_gid: %"PRId64"\n", state.np_connector_group_gid);
    ZDP("customer_gid: %"PRId64"\n", state.customer_gid);

    if (state.bgp_state) {
        if (state.bgp_state->deletion_in_progress) {
            ZDP("np bgp state is being deleted\n");
        } else {
            ZPATH_MUTEX_LOCK(&(state.bgp_state->lock), __FILE__, __LINE__);
            ZDP("\tfrr_version: %s\n", state.bgp_state->frr_version ? state.bgp_state->frr_version : "unknown");
            ZDP("\trouter_id: %s\n", state.bgp_state->router_id ? state.bgp_state->router_id : "null");
            ZDP("\tasn: %"PRId64"\n", state.bgp_state->asn);
            ZDP("\tconfig_mode: %d\n", state.bgp_state->config_mode);
            ZDP("\toverride_config: %s\n", state.bgp_state->override_config ? state.bgp_state->override_config : "null");
            ZDP("\tforce_reload_config: %d\n", state.bgp_state->force_reload_config);
            ZDP("\tdisable_local_sourcing: %d\n", state.bgp_state->disable_local_sourcing);
            ZDP("\tstored client_subnets_count: %"PRId64"\n", state.bgp_state->client_subnets_count);
            ZDP("\tstored lan_subnets_count: %"PRId64"\n", state.bgp_state->lan_subnets_count);
            ZDP("\tstored peer_gateways_count: %"PRId64"\n", state.bgp_state->peer_gateways_count);
            ZDP("\tstored peer_routers_count: %"PRId64"\n", state.bgp_state->peer_routers_count);
            ZPATH_MUTEX_UNLOCK(&(state.bgp_state->lock), __FILE__, __LINE__);
        }
    } else {
        ZDP("np bgp state is NULL\n");
    }
    ZDP("init_us: %"PRId64"\n", state.init_us);
    ZDP("redundancy_feature_enabled: %s\n", state.redundancy_feature_enabled ? "yes" : "no");
    ZDP("redundant_mode_enabled: %s\n", state.redundant_mode_enabled ? "yes" : "no");
    return NP_CONNECTOR_RESULT_NO_ERROR;
}

static int
np_bgp_dump_peers(struct zpath_debug_state  *request_state,
                  const char                **query_values __attribute__((unused)),
                  int                       query_value_count __attribute__((unused)),
                  void                      *cookie __attribute__((unused)))
{
    if (state.bgp_state) {
        if (state.bgp_state->deletion_in_progress) {
            ZDP("np bgp state is being deleted\n");
        } else {
            ZPATH_MUTEX_LOCK(&(state.bgp_state->lock), __FILE__, __LINE__);
            ZDP("Dumping %"PRId64" gateway peers\n", state.bgp_state->peer_gateways_count);
            zhash_table_walk(state.bgp_state->peer_gateways, NULL, dump_peer_config_walk, request_state);
            ZDP("Dumping %"PRId64" router peers\n", state.bgp_state->peer_routers_count);
            zhash_table_walk(state.bgp_state->peer_routers, NULL, dump_peer_config_walk, request_state);
            ZPATH_MUTEX_UNLOCK(&(state.bgp_state->lock), __FILE__, __LINE__);
        }
    } else {
        ZDP("np bgp state is NULL\n");
    }
    return NP_CONNECTOR_RESULT_NO_ERROR;
}

static int
np_bgp_dump_subnets(struct zpath_debug_state  *request_state,
                    const char                **query_values __attribute__((unused)),
                    int                       query_value_count __attribute__((unused)),
                    void                      *cookie __attribute__((unused)))
{
    if (state.bgp_state) {
        if (state.bgp_state->deletion_in_progress) {
            ZDP("np bgp state is being deleted\n");
        } else {
            ZPATH_MUTEX_LOCK(&(state.bgp_state->lock), __FILE__, __LINE__);
            ZDP("Dumping %"PRId64" client subnets\n", state.bgp_state->client_subnets_count);
            zhash_table_walk(state.bgp_state->client_subnets, NULL, dump_subnet_walk, request_state);
            ZDP("Dumping %"PRId64" lan subnets\n", state.bgp_state->lan_subnets_count);
            zhash_table_walk(state.bgp_state->lan_subnets, NULL, dump_subnet_walk, request_state);
            ZPATH_MUTEX_UNLOCK(&(state.bgp_state->lock), __FILE__, __LINE__);
        }
    } else {
        ZDP("np bgp state is NULL\n");
    }
    return NP_CONNECTOR_RESULT_NO_ERROR;
}

static void
bgp_config_table_row_callback_bgp_thread(struct zevent_base *base __attribute__((unused)),
                                         void *void_cookie,
                                         int64_t int_cookie __attribute__((unused)))
{
    struct argo_object* row = void_cookie;
    struct np_bgp_connectors_config *bgp_conn = (struct np_bgp_connectors_config *)row->base_structure_void;

    if (!bgp_conn || (bgp_conn->connector_gid != state.np_connector_gid)) goto done;

    if (bgp_conn->deleted) {
        NP_CONNECTOR_LOG(AL_WARNING, "Connector BGP config is deleted, need re-provisioning. Destroying NP connector BGP state");
        np_connector_bgp_state_destroy();
        /* BGP config is deleted, we're supposed to clean up wireguard peers but for now we can leave it as is to avoid
         * adding extra complexity in dynamic BGP state management
         * if something bad happens, either feature should be disabled, or connector itself should be deprovisioned */
        goto done;
    }

    int res = np_bgp_update_instance_config(state.bgp_state, bgp_conn->gid, bgp_conn->asn, bgp_conn->router_id,
                                bgp_conn->config_mode, bgp_conn->override_config, bgp_conn->force_reload_config);
    if (res) {
        NP_CONNECTOR_LOG(AL_ERROR, "Unable to update bgp instance config: %s", zpath_result_string(res));
    }

done:
    argo_object_release(row);
    return;

}

static int
np_connector_bgp_config_table_row_callback(void                    *cookie __attribute__((unused)),
                                           struct wally_registrant *registrant __attribute__((unused)),
                                           struct wally_table      *table __attribute__((unused)),
                                           struct argo_object      *previous_row,
                                           struct argo_object      *row,
                                           int64_t                 request_id __attribute__((unused)))
{
    if (np_connector_debug_log & NP_CONNECTOR_DEBUG_BGP_BIT) {
        dump_rows(previous_row, row, __FUNCTION__);
    }

    if (!np_is_redundancy_feature_enabled(state.customer_gid)) {
        /* feature disabled, ignoring row callback */
        NP_CONNECTOR_LOG(AL_NOTICE, "ignoring np_bgp_connectors_config row callback as feature is disabled");
        return NP_CONNECTOR_RESULT_NO_ERROR;
    }

    struct np_bgp_connectors_config *bgp_conn = (struct np_bgp_connectors_config *)row->base_structure_void;
    if (!bgp_conn) {
        return NP_CONNECTOR_RESULT_NO_ERROR;
    }

    if (bgp_conn->deleted && !previous_row) {
        return NP_CONNECTOR_RESULT_NO_ERROR;
    }

    argo_object_hold(row);
    if (0 != zevent_base_call(state.bgp_state->zevent_base, bgp_config_table_row_callback_bgp_thread, row, 0)) {
        NP_CONNECTOR_LOG(AL_WARNING, "Failed to make thread call to bgp_config_table_row_callback_bgp_thread");
        argo_object_release(row);
    }
    return NP_CONNECTOR_RESULT_NO_ERROR;
}

/* BGP gateway config row arrives, make thread call to update gateway wireguard peer config with latest class E IP
 * When to update wireguard peer config?
 *   1. the row is a new row
 *   2. there is a previous row and current row has been deleted
 *   3. there is a previews row, current row isn't deleted but router_id changed
 * We need to do #2 to remove class E IP from allowed IP list. Most likely #2 will happen when gateway is also deleted,
 * in that case the gateway add_or_update will be no-op.
 */
static int
np_connector_bgp_gateways_config_table_row_callback(void                    *cookie __attribute__((unused)),
                                                    struct wally_registrant *registrant __attribute__((unused)),
                                                    struct wally_table      *table __attribute__((unused)),
                                                    struct argo_object      *previous_row,
                                                    struct argo_object      *row,
                                                    int64_t                 request_id __attribute__((unused)))
{
    if (np_connector_debug_log & NP_CONNECTOR_DEBUG_BGP_BIT) {
       dump_rows(previous_row, row, __FUNCTION__);
    }

    if (!np_is_redundancy_feature_enabled(state.customer_gid)) {
        /* feature disabled, ignoring row callback */
        NP_CONNECTOR_LOG(AL_NOTICE, "ignoring np_bgp_gateways_config row callback as feature is disabled");
        goto done;
    }

    struct np_bgp_gateways_config *bgp_gateway = (struct np_bgp_gateways_config *)row->base_structure_void;
    if (!bgp_gateway) {
        goto done;
    }

    if (bgp_gateway->deleted && !previous_row) {
        goto done;
    }

    if (!bgp_gateway->deleted && previous_row) {
        struct np_bgp_gateways_config *bgp_gateway_old = (struct np_bgp_gateways_config *)previous_row->base_structure_void;
        if (bgp_gateway_old && bgp_gateway->router_id && bgp_gateway_old->router_id &&
            argo_inet_is_same(bgp_gateway->router_id, bgp_gateway_old->router_id)) {
            /* row has been updated, but no change in router_id, skipping */
            goto done;
        }
    }

    np_connector_add_or_update_gateway_from_another_thread(bgp_gateway->gateway_gid);

done:
    return NP_CONNECTOR_RESULT_NO_ERROR;
}


static void
bgp_session_config_table_row_callback_bgp_thread(struct zevent_base *base __attribute__((unused)),
                                                 void *void_cookie,
                                                 int64_t int_cookie __attribute__((unused)))
{
    struct argo_object* row = void_cookie;

    struct np_bgp_connector_session_config *new_session_config = (struct np_bgp_connector_session_config *)row->base_structure_void;
    if (!new_session_config) goto done;

    if (new_session_config->bgp_config_gid != state.bgp_state->bgp_config_gid)  goto done;

    /*
     * the row will be created/deleted if the neighbor has been created/deleted
     * the row will be updated if the session config is updated
     * - incl. the bgp session parameters, and the neighbor ASN, IP info.
     * - but the neighbor_config_gid, neighbor_instance_type will not be updated
     *
     */
    if (new_session_config->deleted) {
        /* session config has been deleted, delete the corresponding in memory peer config */
        np_bgp_connector_delete_peer_config(state.bgp_state, new_session_config);
        /* previous row is obsolete, ignore processing it */
    } else {
        /* flush out and replace
         * TODO: optimize this routine */
        np_bgp_connector_add_or_update_peer_config(state.bgp_state, new_session_config);
    }

done:
    argo_object_release(row);
    return;
}

static int
np_connector_bgp_session_config_table_row_callback(void                    *cookie __attribute__((unused)),
                                                   struct wally_registrant *registrant __attribute__((unused)),
                                                   struct wally_table      *table __attribute__((unused)),
                                                   struct argo_object      *previous_row,
                                                   struct argo_object      *row,
                                                   int64_t                 request_id __attribute__((unused)))
{
    if (np_connector_debug_log & NP_CONNECTOR_DEBUG_BGP_BIT) {
       dump_rows(previous_row, row, __FUNCTION__);
    }

    if (!np_is_redundancy_feature_enabled(state.customer_gid)) {
        /* feature disabled, ignoring row callback */
        NP_CONNECTOR_LOG(AL_NOTICE, "ignoring np_bgp_connector_session_config row callback as feature is disabled");
        return NP_CONNECTOR_RESULT_NO_ERROR;
    }

    struct np_bgp_connector_session_config *session_config = (struct np_bgp_connector_session_config *)row->base_structure_void;
    if (!session_config) {
        return NP_CONNECTOR_RESULT_NO_ERROR;
    }

    if (session_config->deleted && !previous_row) {
        return NP_CONNECTOR_RESULT_NO_ERROR;
    }

    argo_object_hold(row);
    if (0 != zevent_base_call(state.bgp_state->zevent_base, bgp_session_config_table_row_callback_bgp_thread, row, 0)) {
         NP_CONNECTOR_LOG(AL_ERROR, "Failed to call bgp_session_config_table_row_callback on NP BGP thread");
        argo_object_release(row);
    }
    return NP_CONNECTOR_RESULT_NO_ERROR;
}

static void
lan_subnets_table_row_callback_bgp_thread_multilan(struct np_lan_subnets *subnet,struct np_lan_subnets *prev_subnet)
{
    if (!subnet || !subnet->lan_subnets) {
        NP_CONNECTOR_LOG(AL_ERROR, "Invalid subnet or lan_subnets is NULL.");
        return;
    }

    if (subnet->deleted) {
        return;
    }

    if (subnet->lan_subnets_count > NETWORK_SEGMENT_MAX_LAN_SUBNETS ||
       (prev_subnet && prev_subnet->lan_subnets_count > NETWORK_SEGMENT_MAX_LAN_SUBNETS)) {
        NP_CONNECTOR_LOG(AL_ERROR, "cur->lan_subnets_count %d, prev->lan_subnets_count %d exceed max %d", subnet->lan_subnets_count, prev_subnet->lan_subnets_count, NETWORK_SEGMENT_MAX_LAN_SUBNETS);
        return;
    }

    struct argo_inet *ips_to_add_holder[NETWORK_SEGMENT_MAX_LAN_SUBNETS];
    struct argo_inet *ips_to_delete_holder[NETWORK_SEGMENT_MAX_LAN_SUBNETS];
    int ips_to_add_count = 0;
    int ips_to_delete_count = 0;
    int res = NP_CONNECTOR_RESULT_ERR;

    if (prev_subnet && prev_subnet->lan_subnets) {
        /* lan subnet has been updated, check if it belongs to us */
        int64_t connector_group_gids[NP_CONNECTOR_GROUPS_LAN_SUBNETS_MAPPING_MAX_COUNT];
        size_t connector_groups_count = sizeof(connector_group_gids) / sizeof(connector_group_gids[0]);
        res = np_connector_groups_lan_subnets_mapping_get_groups_by_subnet_gid_immediate(subnet->gid,&(connector_group_gids[0]), &connector_groups_count);
        if (res) {
            /* this subnet isn't mapped to any connector, ignore it */
            NP_CONNECTOR_LOG(AL_NOTICE, "Ignoring lan subnets updates for gid %"PRId64" with connector group %"PRId64" for multilan segment, res %s",
                                        subnet->gid, state.np_connector_group_gid, zpath_result_string(res));
            return;
        } else {
            for (int i = 0; i < connector_groups_count; i++) {
                if (connector_group_gids[i] == state.np_connector_group_gid) {
                    res = np_lan_subnet_find_lan_subnets_delta_hash_table((const struct argo_inet **)subnet->lan_subnets, subnet->lan_subnets_count,
                                                                          (const struct argo_inet **)prev_subnet->lan_subnets, prev_subnet->lan_subnets_count,
                                                                           ips_to_add_holder, sizeof(ips_to_add_holder), &ips_to_add_count,
                                                                           ips_to_delete_holder, sizeof(ips_to_delete_holder), &ips_to_delete_count,
                                                                           subnet->gid, prev_subnet->gid);
                    if (res) {
                        NP_CONNECTOR_LOG(AL_ERROR, "ERROR finding delta for %"PRId64" with connector group %"PRId64" for multilan segment",
                              subnet->gid, state.np_connector_group_gid);
                        return;
                    }
                    if (ips_to_delete_count) {
                        for (int i = 0; i < ips_to_delete_count; i++) {
                            np_bgp_delete_subnet_config(state.bgp_state, ips_to_delete_holder[i], 1/*is_lan_subnet*/);
                        }
                    }

                    if (ips_to_add_count) {
                        for (int i = 0; i < ips_to_add_count; i++) {
                            np_bgp_add_subnet_config(state.bgp_state, ips_to_add_holder[i], 1/*is_lan_subnet*/);
                        }
                    }
                    break;
                }
            }
        }
    } else {
        /* new lan subnet, the mapping table row callback should take care of registration */
        return;
    }

    return;

}

static void
lan_subnets_table_row_callback_bgp_thread(struct zevent_base *base __attribute__((unused)),
                                          void               *void_cookie,
                                          int64_t            int_cookie __attribute__((unused)),
                                          void               *extra_cookie1,
                                          void               *extra_cookie2 __attribute__((unused)),
                                          void               *extra_cookie3 __attribute__((unused)),
                                          int64_t            extra_int_cookie __attribute__((unused)))
{
    struct argo_object* row = void_cookie;
    struct argo_object* previous_row = extra_cookie1;
    struct np_lan_subnets *subnet = (struct np_lan_subnets *)row->base_structure_void;
    struct np_lan_subnets *prev_subnet = previous_row ? (struct np_lan_subnets *)previous_row->base_structure_void : NULL;

    int64_t g_npc_multi_lan_feature_enable = np_connector_cfg_override_get_multilan_subnets_enable();
    if (g_npc_multi_lan_feature_enable) {
        lan_subnets_table_row_callback_bgp_thread_multilan(subnet, prev_subnet);
        goto done;

    }

    if (!subnet || !subnet->subnet) goto done;

    if (subnet->deleted) {

        goto done;
    }

    if (prev_subnet && prev_subnet->subnet) {
        /* lan subnet has been updated, check if it belongs to us */
        int64_t connector_group_gids[NP_CONNECTOR_GROUPS_LAN_SUBNETS_MAPPING_MAX_COUNT];
        size_t connector_groups_count = sizeof(connector_group_gids) / sizeof(connector_group_gids[0]);
        if (np_connector_groups_lan_subnets_mapping_get_groups_by_subnet_gid_immediate(subnet->gid,
                        &(connector_group_gids[0]), &connector_groups_count) != NP_CONNECTOR_RESULT_NO_ERROR) {
            /* this subnet isn't mapped to any connector, ignore it */
            NP_CONNECTOR_LOG(AL_NOTICE, "Ignoring lan subnet updates for gid %"PRId64" since it's not mapped to any connector group",
                                subnet->gid);
            goto done;
        } else {
            for (int i = 0; i < connector_groups_count; i++) {
                if (connector_group_gids[i] == state.np_connector_group_gid) {
                    if (!argo_inet_is_same(prev_subnet->subnet, subnet->subnet)) {
                        char dump[ARGO_INET_ADDRSTRLEN];
                        NP_CONNECTOR_LOG(AL_NOTICE, "lan subnet gid %"PRId64" updated from %s to %s, updating in memory hash",
                                            subnet->gid,
                                            argo_inet_generate(dump, prev_subnet->subnet),
                                            argo_inet_generate(dump, subnet->subnet));
                        np_bgp_delete_subnet_config(state.bgp_state, prev_subnet->subnet, 1/*is_lan_subnet*/);
                        np_bgp_add_subnet_config(state.bgp_state, subnet->subnet, 1/*is_lan_subnet*/);
                    }
                    break;
                }
            }
        }
    } else {
        /* new lan subnet, the mapping table row callback should take care of registration */
        goto done;
    }

done:
    if (previous_row) argo_object_release(previous_row);
    argo_object_release(row);
    return;
}

static int
np_connector_lan_subnet_table_row_callback(void                    *cookie __attribute__((unused)),
                                           struct wally_registrant *registrant __attribute__((unused)),
                                           struct wally_table      *table __attribute__((unused)),
                                           struct argo_object      *previous_row,
                                           struct argo_object      *row,
                                           int64_t                 request_id __attribute__((unused)))
{
    if (np_connector_debug_log & NP_CONNECTOR_DEBUG_BGP_BIT) {
        dump_rows(previous_row, row, __FUNCTION__);
    }

    if (!np_is_redundancy_feature_enabled(state.customer_gid)) {
        /* feature disabled, ignoring row callback */
        NP_CONNECTOR_LOG(AL_NOTICE, "ignoring np_lan_subnets row callback as feature is disabled");
        return NP_CONNECTOR_RESULT_NO_ERROR;
    }

    struct np_lan_subnets *subnet = (struct np_lan_subnets *)row->base_structure_void;
    if (!subnet) {
        return NP_CONNECTOR_RESULT_NO_ERROR;
    }

    if (subnet->deleted && !previous_row) {
        return NP_CONNECTOR_RESULT_NO_ERROR;
    }

    argo_object_hold(row);
    if (previous_row) argo_object_hold(previous_row);
    if (0 != zevent_base_big_call(state.bgp_state->zevent_base, lan_subnets_table_row_callback_bgp_thread,
                                    row, 0, previous_row, NULL, NULL, 0)) {
        if (previous_row) argo_object_release(previous_row);
        argo_object_release(row);
    }

    return NP_CONNECTOR_RESULT_NO_ERROR;
}

static void
connector_group_to_subnet_mapping_table_row_callback_bgp_thread(struct zevent_base *base __attribute__((unused)),
                                                                void *void_cookie,
                                                                int64_t int_cookie __attribute__((unused)))
{
    struct argo_object* row = void_cookie;
    struct np_connector_groups_lan_subnets_mapping *mapping = (struct np_connector_groups_lan_subnets_mapping *)row->base_structure_void;
    struct np_lan_subnets *subnet = NULL;
    int res = NP_CONNECTOR_RESULT_NO_ERROR;
    int64_t g_npc_multi_lan_feature_enable = np_connector_cfg_override_get_multilan_subnets_enable();

    if (!mapping) goto done;

    res = np_lan_subnets_get_by_id_immediate(mapping->lan_subnet_gid, &subnet);
    if (!subnet) {
        /* received mapping row callback but subnet has already been deleted? this should never happen */
        NP_CONNECTOR_LOG(AL_WARNING, "Received mapping row but LAN subnet %"PRId64" is not found: %s",
                            mapping->lan_subnet_gid, zpath_result_string(res));
        goto done;
    } else {

        if (g_npc_multi_lan_feature_enable) {
            if (subnet->lan_subnets && subnet->lan_subnets_count > 0) {
                if (mapping->deleted) {
                    for (int i = 0; i < subnet->lan_subnets_count; i++) {
                        if (subnet->lan_subnets[i]) {
                            np_bgp_delete_subnet_config(state.bgp_state, subnet->lan_subnets[i], 1/*is_lan_subnet*/);
                        } else {
                            NP_CONNECTOR_LOG(AL_WARNING, "LAN subnet entry at index %d is NULL for subnet GID %"PRId64".", i, mapping->lan_subnet_gid);
                        }
                    }
                } else {
                    for (int i = 0; i < subnet->lan_subnets_count; i++) {
                        if (subnet->lan_subnets[i]) {
                            np_bgp_add_subnet_config(state.bgp_state, subnet->lan_subnets[i], 1/*is_lan_subnet*/);
                        } else {
                            NP_CONNECTOR_LOG(AL_WARNING, "LAN subnet entry at index %d is NULL for subnet GID %"PRId64".", i, mapping->lan_subnet_gid);
                        }
                    }
                }
            } else {
                NP_CONNECTOR_LOG(AL_WARNING, "LAN subnet array is empty or invalid for subnet GID %"PRId64".", mapping->lan_subnet_gid);
            }
        } else {
            if (mapping->deleted) {
                np_bgp_delete_subnet_config(state.bgp_state, subnet->subnet, 1/*is_lan_subnet*/);
            } else {
                np_bgp_add_subnet_config(state.bgp_state, subnet->subnet, 1/*is_lan_subnet*/);
            }
        }
    }

done:
    argo_object_release(row);
    return;
}

static int
np_connector_group_lan_subnet_mapping_table_row_callback(void                    *cookie __attribute__((unused)),
                                                         struct wally_registrant *registrant __attribute__((unused)),
                                                         struct wally_table      *table __attribute__((unused)),
                                                         struct argo_object      *previous_row,
                                                         struct argo_object      *row,
                                                         int64_t                 request_id __attribute__((unused)))
{
    if (np_connector_debug_log & NP_CONNECTOR_DEBUG_BGP_BIT) {
        dump_rows(previous_row, row, __FUNCTION__);
    }

    if (!np_is_redundancy_feature_enabled(state.customer_gid)) {
        /* feature disabled, ignoring row callback */
        NP_CONNECTOR_LOG(AL_NOTICE, "ignoring np_connector_group_lan_subnet_mapping row callback as feature is disabled");
        return NP_CONNECTOR_RESULT_NO_ERROR;
    }

    struct np_connector_groups_lan_subnets_mapping *mapping = (struct np_connector_groups_lan_subnets_mapping *)row->base_structure_void;
    if (!mapping || (mapping->connector_group_gid != state.np_connector_group_gid)) {
        return NP_CONNECTOR_RESULT_NO_ERROR;
    }

    if (mapping->deleted && !previous_row) {
        return NP_CONNECTOR_RESULT_NO_ERROR;
    }

    argo_object_hold(row);
    /* assuming mapping row cannot be modified, we only need to act on the row itself and no need to check previous_row */
    if (0 != zevent_base_call(state.bgp_state->zevent_base, connector_group_to_subnet_mapping_table_row_callback_bgp_thread, row, 0)) {
        argo_object_release(row);
    }

    return NP_CONNECTOR_RESULT_NO_ERROR;
}

/*
 * Load NP Redundancy tables
 * This routine will block until all the rows are loaded, will only execute once
 * bgp_state is assumed to be initialized already
 */
static void
load_bgp_tables_on_bgp_thread(struct zevent_base *base __attribute__((unused)),
                              void               *void_cookie __attribute__((unused)),
                              int64_t            int_cookie __attribute__((unused)))
{
    static int tables_initialized = 0;
    struct np_bgp_connectors_config *bgp_conn = NULL;

    if (!tables_initialized) {
        int res = NP_CONNECTOR_RESULT_NO_ERROR;

        res = np_bgp_connectors_config_table_init(state.np_wally,
                                                  state.customer_gid,
                                                  np_connector_bgp_config_table_row_callback,
                                                  NULL,
                                                  0);
        if (res) {
            NP_CONNECTOR_LOG(AL_ERROR, "Cannot init table np_bgp_connectors_config: %s", zpath_result_string(res));
            return;
        }

        /* Do not proceed further if np config conn is not up, because the following table init calls will block
         * on waiting for wally rows and eventually will crash due to heartbeat timeout */
        int not_ready_count = 0;
        do {
            res = np_bgp_connectors_config_get_by_connector_gid(state.np_connector_gid, &bgp_conn, NULL, NULL, 0);
            if (res) {
                if ((not_ready_count++ % 10) == 0) {
                    if (res == NP_CONNECTOR_RESULT_NOT_READY) {
                        NP_CONNECTOR_LOG(AL_NOTICE, "NP Connector not yet connected to NP config channel, please check connectivity");
                    } else {
                        if (res != NP_CONNECTOR_RESULT_ASYNCHRONOUS) {
                            NP_CONNECTOR_LOG(AL_ERROR, "Get connector BGP config returned %s, please check BGP config", zpath_result_string(res));
                        } else {
                            NP_CONNECTOR_LOG(AL_NOTICE, "Waiting for NP connector to retrieve BGP configuration");
                        }
                    }
                }
                sleep(1);
            }
            zthread_heartbeat(NULL);
        } while (res);
        NP_CONNECTOR_LOG(AL_INFO, "Initialized np_bgp_connectors_config table");

        /* Fully load np_bgp_gateways_config customer table
         * gateway needs to lookup np_tenant_gateways gid by its bgp config gid for getting info for stats */
        res = np_bgp_gateways_config_table_init(state.np_wally,
                                                state.customer_gid,
                                                np_connector_bgp_gateways_config_table_row_callback,
                                                NULL,
                                                0);
        if (res) {
            NP_CONNECTOR_LOG(AL_ERROR, "Cannot init table np_bgp_gateways_config: %s", zpath_result_string(res));
            return;
        }

        /* Register the table later so the row callback can arrive after the column init is done */
        res = np_bgp_gateways_config_register_by_customer_gid(state.customer_gid);
        if (res) {
            NP_CONNECTOR_LOG(AL_ERROR, "Cannot register table np_bgp_gateways_config: %s", zpath_result_string(res));
            return;
        }

        NP_CONNECTOR_LOG(AL_INFO, "Initialized np_bgp_gateways_config table");

        /* This table can be huge, so load the table by connector's BGP config gid instead of fully load customer table */
        res = np_bgp_connector_session_config_table_init(state.np_wally,
                                                         state.customer_gid,
                                                         np_connector_bgp_session_config_table_row_callback,
                                                         NULL,
                                                         0/*do not fully load customer table*/);
        if (res) {
            NP_CONNECTOR_LOG(AL_ERROR, "Cannot init table np_bgp_connectors_session_config: %s", zpath_result_string(res));
            return;
        }

        if (bgp_conn) {
            /* load rows by connector's BGP config gid */
            res = np_bgp_connector_session_config_register_by_bgp_config_gid(bgp_conn->gid);
            if (res) {
                NP_CONNECTOR_LOG(AL_ERROR, "Cannot register table np_bgp_connector_session_config by bgp_config_gid %"PRId64": %s",
                                            bgp_conn->gid, zpath_result_string(res));
                return;
            }
        } else {
            NP_CONNECTOR_LOG(AL_ERROR, "bgp config is not found for connector, unable to load remaining tables");
            return;
        }

        NP_CONNECTOR_LOG(AL_INFO, "Initialized np_bgp_connector_session_config table");
        zthread_heartbeat(NULL);

        res = np_lan_subnets_table_init(state.np_wally,
                                        state.customer_gid,
                                        np_connector_lan_subnet_table_row_callback,
                                        NULL,
                                        1);
        if (res) {
            NP_CONNECTOR_LOG(AL_ERROR, "Cannot init table np_lan_subnets: %s", zpath_result_string(res));
            return;
        }
        NP_CONNECTOR_LOG(AL_INFO, "Initialized np_lan_subnets table");
        zthread_heartbeat(NULL);

        res = np_connector_groups_lan_subnets_mapping_table_init(state.np_wally,
                                                                 state.customer_gid,
                                                                 np_connector_group_lan_subnet_mapping_table_row_callback,
                                                                 NULL,
                                                                 0);
        if (res) {
            NP_CONNECTOR_LOG(AL_ERROR, "Cannot init table np_connector_groups_lan_subnets_mapping: %s", zpath_result_string(res));
            return;
        }

        res = np_connector_groups_lan_subnets_mapping_register_by_connector_group_gid(state.np_connector_group_gid);
        if (res) {
            NP_CONNECTOR_LOG(AL_ERROR, "Cannot register table np_connector_groups_lan_subnets_mapping: %s",
                                        zpath_result_string(res));
            return;
        }
        NP_CONNECTOR_LOG(AL_INFO, "Initialized np_connector_groups_lan_subnets_mapping table");
    }

    NP_CONNECTOR_LOG(AL_INFO, "NP Connector BGP tables Initialization Complete");
    tables_initialized = 1;
    return;
}

void
np_connector_bgp_load_config()
{
    if (!state.bgp_state || state.bgp_state->deletion_in_progress) {
        NP_CONNECTOR_LOG(AL_ERROR, "Failed to load NP BGP configs as bgp state is not initialized");
        return;
    }
    if (0 != zevent_base_call(state.bgp_state->zevent_base, load_bgp_tables_on_bgp_thread, NULL, 0)) {
        NP_CONNECTOR_LOG(AL_ERROR, "Failed to call np_connector_bgp_load_config on NP BGP thread");
    }
    return;
}

static void
client_subnets_callback_bgp_thread(struct zevent_base *base __attribute__((unused)),
                                   void               *void_cookie,
                                   int64_t            int_cookie __attribute__((unused)),
                                   void               *extra_cookie1,
                                   void               *extra_cookie2 __attribute__((unused)),
                                   void               *extra_cookie3 __attribute__((unused)),
                                   int64_t            extra_int_cookie __attribute__((unused)))
{
    struct argo_object* row = void_cookie;
    struct argo_object* previous_row = extra_cookie1;
    struct np_client_subnets *subnet = (struct np_client_subnets *)row->base_structure_void;
    struct np_client_subnets *prev_subnet = previous_row ? (struct np_client_subnets *)previous_row->base_structure_void : NULL;
    char dump[ARGO_INET_ADDRSTRLEN];

    if (np_connector_debug_log & NP_CONNECTOR_DEBUG_BGP_BIT) {
        dump_rows(previous_row, row, __FUNCTION__);
    }

    if (!subnet || !subnet->subnet) goto done;

    if (subnet->deleted) {
        NP_CONNECTOR_LOG(AL_NOTICE, "Deleting client subnet: %s from in memory hash", argo_inet_generate(dump, subnet->subnet));
        np_bgp_delete_subnet_config(state.bgp_state, subnet->subnet, 0);
        goto done;
    }

    if (prev_subnet && prev_subnet->subnet) {
        /* client subnet has been updated, check if it belongs to us */
        if (!argo_inet_is_same(prev_subnet->subnet, subnet->subnet)) {
            char dump2[ARGO_INET_ADDRSTRLEN];
            NP_CONNECTOR_LOG(AL_NOTICE, "client subnet gid %"PRId64" updated from %s to %s, updating in memory hash",
                                subnet->gid,
                                argo_inet_generate(dump, prev_subnet->subnet),
                                argo_inet_generate(dump2, subnet->subnet));
            np_bgp_delete_subnet_config(state.bgp_state, prev_subnet->subnet, 0);
            np_bgp_add_subnet_config(state.bgp_state, subnet->subnet, 0);
        }
    } else {
        /* new client subnet */
        NP_CONNECTOR_LOG(AL_NOTICE, "Adding client subnet: %s to in memory hash", argo_inet_generate(dump, subnet->subnet));
        np_bgp_add_subnet_config(state.bgp_state, subnet->subnet, 0);
    }

done:
    if (previous_row) argo_object_release(previous_row);
    argo_object_release(row);
    return;
}

int np_connector_bgp_client_subnet_handler(struct argo_object *previous_row, struct argo_object *row)
{
    if (!state.bgp_state || state.bgp_state->deletion_in_progress) {
        if (previous_row) argo_object_release(previous_row);
        argo_object_release(row);
        return NP_CONNECTOR_RESULT_NOT_READY;
    }
    if (0 != zevent_base_big_call(state.bgp_state->zevent_base, client_subnets_callback_bgp_thread,
                                    row, 0, previous_row, NULL, NULL, 0)) {
        if (previous_row) argo_object_release(previous_row);
        argo_object_release(row);
        return NP_CONNECTOR_RESULT_ERR;
    }
    return NP_CONNECTOR_RESULT_NO_ERROR;
}

void np_connector_bgp_set_np_connector_gid(int64_t np_connector_gid)
{
    state.np_connector_gid = np_connector_gid;
}

void np_connector_bgp_set_np_connector_group_gid(int64_t np_connector_group_gid)
{
    state.np_connector_group_gid = np_connector_group_gid;
}

void
np_connector_bgp_update_local_sourcing_config(int advertise_lan_segments_disabled)
{
    np_bgp_set_local_sourcing(state.bgp_state, advertise_lan_segments_disabled);
    return;
}

int np_connector_get_bgp_config_mode() {
    struct np_bgp_state *bgp_state = NULL;
    int bgp_config_mode = -1;
    bgp_state = state.bgp_state;

    if (bgp_state) {
        ZPATH_MUTEX_LOCK(&(state.bgp_state->lock), __FILE__, __LINE__);
        bgp_config_mode = np_bgp_get_config_mode(bgp_state);
        ZPATH_MUTEX_UNLOCK(&(state.bgp_state->lock), __FILE__, __LINE__);
    }

    return bgp_config_mode;
}

int np_connector_get_bgp_peer_info_from_neighbor_ip(const char *neighbor_ip, int64_t neighbor_asn, int64_t *peer_gid, int *peer_type) {
    struct np_bgp_state *bgp_state = NULL;
    int res = 0;
    bgp_state = state.bgp_state;

    if (bgp_state) {
        ZPATH_MUTEX_LOCK(&(state.bgp_state->lock), __FILE__, __LINE__);
        res = np_bgp_peer_ip_to_instance_gid_lookup(state.bgp_state, neighbor_ip,
                     neighbor_asn, peer_gid, peer_type);
        if (res) {
            NP_CONNECTOR_LOG(AL_ERROR, "Error fetching instance gid from neighbor ip:%s error:%s",
                                 neighbor_ip, zpath_result_string(res));
        }
        ZPATH_MUTEX_UNLOCK(&(state.bgp_state->lock), __FILE__, __LINE__);
    }

    return res;
}

int np_bgp_connector_reload_frr_conf(int force_regenerate) {
    struct np_bgp_state *bgp_state = NULL;
    bgp_state = state.bgp_state;

    if (!bgp_state || bgp_state->deletion_in_progress) {
        return NP_CONNECTOR_RESULT_NOT_READY;
    }

    return np_bgp_reload_frr_conf(bgp_state, np_bgp_instance_connector, force_regenerate);
}

/*
 * Called from NP thread to initialize BGP during feature/mode enablement events
 */
void
np_connector_bgp_state_init(struct wally *np_wally, int64_t customer_gid)
{
    static int config_init_done = 0;
    state.np_wally = np_wally;
    state.customer_gid = customer_gid;

    if (!state.bgp_state || state.bgp_state->deletion_in_progress) {
        state.bgp_state = np_bgp_state_create(state.customer_gid, np_bgp_instance_connector);
        if (!state.bgp_state) {
            NP_CONNECTOR_LOG(AL_CRITICAL, "Failed to create np bgp state!");
        } else {
            NP_CONNECTOR_LOG(AL_NOTICE, "Initialized NP BGP state");
        }
        state.init_us = epoch_us();
    } else {
        NP_CONNECTOR_LOG(AL_NOTICE, "NP BGP state is already initialized");
    }

    /* Below are necessary config inits that only needs to be called once per process lifetime */
    if (config_init_done) {
        NP_CONNECTOR_LOG(AL_NOTICE, "NP BGP config init is done");
        return;
    }

    char buf[256];
    if (np_frr_start(buf, sizeof(buf))) {
        NP_CONNECTOR_LOG(AL_ERROR, "Failed to start FRR: %s", buf);
    }

    if (npwg_provider_setup_interface_dummy(0/*is_gateway*/)) {
        NP_CONNECTOR_LOG(AL_ERROR, "Failed to setup dummy interface");
    }

    np_bgp_config_stats_init(customer_gid);

    /* Load BGP related wally tables */
    np_connector_bgp_load_config();

    config_init_done = 1;
    return;
}

/*
 * Called from NP thread to initialize BGP during feature/mode enablement events
 */
void
np_connector_bgp_state_destroy()
{
    int res;

    if (!state.bgp_state || state.bgp_state->deletion_in_progress) {
        NP_CONNECTOR_LOG(AL_NOTICE, "BGP state is uninitialized or already deleting, skipping destroy attempt");
        return;
    }
    res = np_bgp_state_destroy(state.bgp_state);
    if (res) {
        NP_CONNECTOR_LOG(AL_CRITICAL, "Failed to destroy np bgp state!");
    } else {
        NP_CONNECTOR_LOG(AL_NOTICE, "NP BGP state destroyed");
    }
    state.bgp_state = NULL;

    return;
}

void
np_connector_bgp_set_redundancy_feature(int enabled)
{
    state.redundancy_feature_enabled = enabled ? 1 : 0;
    // Update the latest feature state into np frr util module
    np_frr_util_set_redundancy_feature_flag_state(enabled ? 1 : 0);
}

void
np_connector_bgp_set_redundant_mode(int enabled)
{
    state.redundant_mode_enabled = enabled ? 1 : 0;
}

static int np_frr_bgp_dump_config_status_callback(struct zpath_debug_state *request_state,
                              const char **query_values,
                              int query_value_count, void *cookie)
{
    int config_mode = -1;
    int is_config_valid = 0;
    int res;

    config_mode = np_connector_get_bgp_config_mode();
    if (config_mode == -1) {
        ZDP("Invalid BGP config mode");
        return NP_CONNECTOR_RESULT_NO_ERROR;
    }

    res = np_frr_test_config_status((config_mode == np_bgp_config_mode_generated)?
                                    NP_BGP_FRR_CONFIG_FILENAME_GENERATED :
                                    NP_BGP_FRR_CONFIG_FILENAME_OVERRIDE, &is_config_valid);
    if (res) {
        ZDP("Error validating frr config for mode:%d error:%s", config_mode, zpath_result_string(res));
        return NP_CONNECTOR_RESULT_NO_ERROR;
    }

    if (is_config_valid) {
        ZDP("FRR config for mode - %d is valid", config_mode);
    } else {
        ZDP("FRR config for mode - %d is invalid", config_mode);
    }
    return NP_CONNECTOR_RESULT_NO_ERROR;
}

static int np_frr_bgp_dump_config_status_details_callback(struct zpath_debug_state *request_state,
                              const char **query_values,
                              int query_value_count, void *cookie)
{
    int config_mode = -1;
    char cmd_to_execute[512] = {0};
    char *out_buf = NULL;
    int res;

    config_mode = np_connector_get_bgp_config_mode();

    res = zpn_np_prepare_bgp_config_validate_cmd(config_mode, cmd_to_execute, sizeof(cmd_to_execute));
    if (res) {
        ZDP("Invalid BGP config mode for np connector");
        return NP_CONNECTOR_RESULT_NO_ERROR;
    }

    res = np_frr_util_execute_command(cmd_to_execute, &out_buf);
    if (res) {
        ZDP("Failed to execute np probe command:%s error:%s", cmd_to_execute, zpath_result_string(res));
    } else {
        ZDP("Config status details for config mode - %d\n %s", config_mode, out_buf);
    }

    np_frr_release_buf(out_buf);
    return NP_CONNECTOR_RESULT_NO_ERROR;
}

int
np_connector_bgp_init()
{
    int res;

    np_connector_debug_log = (NP_CONNECTOR_DEBUG_BGP_BIT) |
                             0;

    res = zpath_debug_add_read_command("dump the NP connector's BGP state",
                                       "/np/bgp/dump_state",
                                       np_bgp_dump_state,
                                       NULL,
                                       NULL);
    if (res){
        NP_CONNECTOR_LOG(AL_ERROR, "Couldn't add curl debug command /np/bgp/dump_state");
        return res;
    }

    res = zpath_debug_add_read_command("dump the NP connector's BGP peer config (currently stored in memory)",
                                       "/np/bgp/dump_peers",
                                       np_bgp_dump_peers,
                                       NULL,
                                       NULL);
    if (res){
        NP_CONNECTOR_LOG(AL_ERROR, "Couldn't add curl debug command /np/bgp/dump_peers");
        return res;
    }

    res = zpath_debug_add_read_command("Get FRR Config status",
                                       "/np/frr/config_status",
                                       np_frr_bgp_dump_config_status_callback,
                                       NULL,
                                       NULL);
    if (res) {
        NP_CONNECTOR_LOG(AL_ERROR, "Could not init np/frr/config_status debug command");
        return NP_CONNECTOR_RESULT_NO_ERROR;
    }

    res = zpath_debug_add_read_command("Get FRR Config status",
                                       "/np/frr/config_status_details",
                                       np_frr_bgp_dump_config_status_details_callback,
                                       NULL,
                                       NULL);
    if (res) {
        NP_CONNECTOR_LOG(AL_ERROR, "Could not init np/frr/config_status_details debug command");
        return NP_CONNECTOR_RESULT_NO_ERROR;
    }

    res = zpath_debug_add_read_command("dump the NP connector's BGP client & lan subnet config (currently stored in memory)",
                                       "/np/bgp/dump_subnets",
                                       np_bgp_dump_subnets,
                                       NULL,
                                       NULL);
    if (res){
        NP_CONNECTOR_LOG(AL_ERROR, "Couldn't add curl debug command /np/bgp/dump_subnets");
        return res;
    }

    return NP_CONNECTOR_RESULT_NO_ERROR;
}
