/*
* np_connector_stats.c. Copyright (C) 2025 Zscaler, Inc. All Rights Reserved.
*
*/

#include "zpn/zpn_lib.h"
#include "npwg_lib/npwg_provider.h"
#include "zpath_lib/zpath_system_stats.h"
#include "npwg_lib/npwg_stats.h"
#include "zpn/zpn_rpc.h"
#include "zpath_lib/zpath_debug.h"

#include "np_connector/np_connector_stats.h"
#include "np_connector/np_connector_lib.h"

// following formats are aligned as per above struct zpa_network_connector_wireguard_stats
#define NP_CONNECTOR_WIREGUARD_STATS_FORMAT_JSON                           \
        "{\"this_npconnector_listen_port\": %" PRId64 ","                  \
        " \"peers_count_total\": %" PRId64 ","                             \
        " \"peers_count_total_max\": %" PRId64 ","                         \
        " \"peers_count_npgateways\": %" PRId64 ","                        \
        " \"allowed_ips_total_cnt\": %" PRId64  ","                        \
        " \"allowed_ips_total_cnt_max\": %" PRId64  ","                    \
        " \"allowed_ips_npgateways_cnt\": %" PRId64  ","                   \
        " \"rx_total_bytes\": %" PRId64  ","                               \
        " \"tx_total_bytes\": %" PRId64  ","                               \
        " \"rx_npgateways_bytes\": %" PRId64  ","                          \
        " \"tx_npgateways_bytes\": %" PRId64  ","                          \
        " \"rx_total_bytes_per_sec\": %" PRId64  ","                       \
        " \"tx_total_bytes_per_sec\": %" PRId64  ","                       \
        " \"rx_npgateways_bytes_per_sec\": %" PRId64  ","                  \
        " \"tx_npgateways_bytes_per_sec\": %" PRId64  ","                  \
        " \"handshake_npgateways_hist_0\": %" PRId64  ","                  \
        " \"handshake_npgateways_hist_1\": %" PRId64  ","                  \
        " \"handshake_npgateways_hist_2\": %" PRId64  ","                  \
        " \"persistent_keepalive_off_count\": %" PRId64  ","               \
        " \"npconnector_stats_monitor_cnt\": %" PRId64  ","                \
        " \"npconnector_stats_monitor_error_cnt\": %" PRId64  ","          \
        " \"allowed_ips_npgateways_get_error_cnt\": %" PRId64  ","         \
        " \"peers_count_invalid\": %" PRId64  ","                          \
        " \"allowed_ips_invalids_cnt\": %" PRId64  ","                     \
        " \"rx_invalids_bytes\": %" PRId64  ","                            \
        " \"tx_invalids_bytes\": %" PRId64  ","                            \
        " \"rx_invalids_bytes_per_sec\": %" PRId64  ","                    \
        " \"tx_invalids_bytes_per_sec\": %" PRId64  ","                    \
        " \"peer_type_get_err_cnt\": %" PRId64  ","                        \
        " \"peer_type_get_err_total_cnt\": %" PRId64 "}\n"

#define NP_CONNECTOR_WIREGUARD_STATS_FORMAT_TEXT                        \
        "this_npconnector_listen_port             : %" PRIu64 "\n"      \
        "peers_count_total                        : %" PRId64 "\n"       \
        "peers_count_total_max                    : %" PRId64 "\n"       \
        "peers_count_npgateways                   : %" PRId64 "\n"       \
        "allowed_ips_total_cnt                    : %" PRId64 "\n"       \
        "allowed_ips_total_cnt_max                : %" PRId64 "\n"       \
        "allowed_ips_npgateways_cnt               : %" PRId64 "\n"       \
        "rx_total_bytes                           : %" PRId64 "\n"       \
        "tx_total_bytes                           : %" PRId64 "\n"       \
        "rx_npgateways_bytes                      : %" PRId64 "\n"       \
        "tx_npgateways_bytes                      : %" PRId64 "\n"       \
        "rx_total_bytes_per_sec                   : %" PRId64 "\n"       \
        "tx_total_bytes_per_sec                   : %" PRId64 "\n"       \
        "rx_npgateways_bytes_per_sec              : %" PRId64 "\n"       \
        "tx_npgateways_bytes_per_sec              : %" PRId64 "\n"       \
        "handshake_npgateways_hist_0              : %" PRId64 "\n"       \
        "handshake_npgateways_hist_1              : %" PRId64 "\n"       \
        "handshake_npgateways_hist_2              : %" PRId64 "\n"       \
        "persistent_keepalive_off_count           : %" PRId64 "\n"       \
        "npconnector_stats_monitor_cnt            : %" PRId64 "\n"       \
        "npconnector_stats_monitor_error_cnt      : %" PRId64 "\n"       \
        "allowed_ips_npgateways_get_error_cnt     : %" PRId64 "\n"       \
        "peers_count_invalid                      : %" PRId64 "\n"       \
        "allowed_ips_invalids_cnt                 : %" PRId64 "\n"       \
        "rx_invalids_bytes                        : %" PRId64 "\n"       \
        "tx_invalids_bytes                        : %" PRId64 "\n"       \
        "rx_invalids_bytes_per_sec                : %" PRId64 "\n"       \
        "tx_invalids_bytes_per_sec                : %" PRId64 "\n"       \
        "peer_type_get_err_cnt                    : %" PRId64 "\n"       \
        "peer_type_get_err_total_cnt              : %" PRId64 "\n"

struct np_connector_wireguard_stats last_reported_np_conn_wg_stats = {0};
static struct np_connector_wg_throughput_report wg_throughput_report = {0};

int np_connector_wireguard_stats_fill(void* cookie,
                                      int counter,
                                      void* structure_data)
{
    struct np_connector_wireguard_stats *np_conn_wg_stats = 0;
    int64_t np_conn_wg_throughput_bytes_per_sec = 0;
    np_conn_wg_stats = (struct np_connector_wireguard_stats *)structure_data;

    ZPATH_RWLOCK_RDLOCK(&(g_npwg_stats_wg.rw_lock), __FILE__, __LINE__);
    np_conn_wg_stats->this_npconnector_listen_port = g_npwg_stats_wg.this_wireguard_listen_port;
    np_conn_wg_stats->peers_count_npgateways = g_npwg_stats_wg.peers_count_npgateways;
    np_conn_wg_stats->peers_count_invalid = g_npwg_stats_wg.peers_count_invalid;
    np_conn_wg_stats->peers_count_total = g_npwg_stats_wg.peers_count_total;
    np_conn_wg_stats->peers_count_total_max = g_npwg_stats_wg.peers_count_total_max;

    np_conn_wg_stats->allowed_ips_npgateways_cnt = g_npwg_stats_wg.allowed_ips_npgateways_cnt;
    np_conn_wg_stats->allowed_ips_invalids_cnt = g_npwg_stats_wg.allowed_ips_invalids_cnt;
    np_conn_wg_stats->allowed_ips_total_cnt = g_npwg_stats_wg.allowed_ips_total_cnt;
    np_conn_wg_stats->allowed_ips_total_cnt_max = g_npwg_stats_wg.allowed_ips_total_cnt_max;

    np_conn_wg_stats->rx_npgateways_bytes_per_sec = g_npwg_stats_wg.rx_npgateways_bytes_per_sec;
    np_conn_wg_stats->tx_npgateways_bytes_per_sec = g_npwg_stats_wg.tx_npgateways_bytes_per_sec;
    np_conn_wg_stats->rx_invalids_bytes_per_sec = g_npwg_stats_wg.rx_invalids_bytes_per_sec;
    np_conn_wg_stats->tx_invalids_bytes_per_sec = g_npwg_stats_wg.tx_invalids_bytes_per_sec;
    np_conn_wg_stats->rx_total_bytes_per_sec = g_npwg_stats_wg.rx_total_bytes_per_sec;
    np_conn_wg_stats->tx_total_bytes_per_sec = g_npwg_stats_wg.tx_total_bytes_per_sec;

    np_conn_wg_stats->rx_npgateways_bytes = g_npwg_stats_wg.rx_npgateways_bytes;
    np_conn_wg_stats->tx_npgateways_bytes = g_npwg_stats_wg.tx_npgateways_bytes;
    np_conn_wg_stats->rx_invalids_bytes = g_npwg_stats_wg.rx_invalids_bytes;
    np_conn_wg_stats->tx_invalids_bytes = g_npwg_stats_wg.tx_invalids_bytes;
    np_conn_wg_stats->rx_total_bytes = g_npwg_stats_wg.rx_total_bytes;
    np_conn_wg_stats->tx_total_bytes = g_npwg_stats_wg.tx_total_bytes;

    np_conn_wg_stats->handshake_npgateways_hist_0 = g_npwg_stats_wg.handshake_npgateways_hist_0;
    np_conn_wg_stats->handshake_npgateways_hist_1 = g_npwg_stats_wg.handshake_npgateways_hist_1;
    np_conn_wg_stats->handshake_npgateways_hist_2 = g_npwg_stats_wg.handshake_npgateways_hist_2;

    np_conn_wg_stats->persistent_keepalive_off_count = g_npwg_stats_wg.persistent_keepalive_off_count;

    np_conn_wg_stats->npconnector_stats_monitor_cnt = g_npwg_stats_wg.npgateway_stats_monitor_cnt;
    np_conn_wg_stats->npconnector_stats_monitor_error_cnt = g_npwg_stats_wg.npgateway_stats_monitor_error_cnt;

    np_conn_wg_stats->allowed_ips_npgateways_get_error_cnt = g_npwg_stats_wg.allowed_ips_npgateways_get_error_cnt;
    np_conn_wg_stats->peer_type_get_err_cnt = g_npwg_stats_wg.peer_type_get_err_cnt;
    np_conn_wg_stats->peer_type_get_err_total_cnt = g_npwg_stats_wg.peer_type_get_err_total_cnt;

    // NP WireGuard statistics are reported every minute, while NP connector comprehensive statistics are reported every five minutes.
    // Therefore, aggregate the WireGuard throughput data over a 5-minute period and include the average throughput data in the comprehensive stats report.
    np_conn_wg_throughput_bytes_per_sec = np_conn_wg_stats->rx_total_bytes_per_sec + np_conn_wg_stats->tx_total_bytes_per_sec;
    wg_throughput_report.total_throughput_bytes_per_sec_aggregated += np_conn_wg_throughput_bytes_per_sec;
    wg_throughput_report.throughput_report_data_count++;

    ZPATH_RWLOCK_UNLOCK(&(g_npwg_stats_wg.rw_lock), __FILE__, __LINE__);
    memcpy(&last_reported_np_conn_wg_stats,np_conn_wg_stats, sizeof(*np_conn_wg_stats));
    return ZPN_RESULT_NO_ERROR;
}

/*
 * This function calculates the average throughput in bits per second, over a period of time,
 * for inclusion in the NP connector's comprehensive statistics.
 * It processes data from multiple 1-minute throughput reports and converts the result to a rate in bits per second.
*/
int64_t np_connector_get_throughput_bits_per_sec_for_comp_stats() {
    int64_t total_throughput_bits_per_sec = 0;

    ZPATH_RWLOCK_RDLOCK(&(g_npwg_stats_wg.rw_lock), __FILE__, __LINE__);
    // Check if aggregated data is available
    if (wg_throughput_report.throughput_report_data_count &&
        wg_throughput_report.total_throughput_bytes_per_sec_aggregated) {

        // Calculate average throughput in bytes per second
        int64_t avg_total_throughput_bytes_per_sec =
            wg_throughput_report.total_throughput_bytes_per_sec_aggregated /
            wg_throughput_report.throughput_report_data_count;

        // Convert average throughput to bits per second
        total_throughput_bits_per_sec = avg_total_throughput_bytes_per_sec * BYTES_TO_BITS_COVERSION;

        // Reset the throughput report data for the next aggregation period
        memset(&wg_throughput_report, 0, sizeof(wg_throughput_report));
    }
    ZPATH_RWLOCK_UNLOCK(&(g_npwg_stats_wg.rw_lock), __FILE__, __LINE__);

    return total_throughput_bits_per_sec;
}

int np_connector_wg_dump(struct zpath_debug_state *request_state,
                         const char **query_values,
                         int query_value_count, void *cookie)
{
    const char *format = NP_CONNECTOR_WIREGUARD_STATS_FORMAT_TEXT;

    if (query_values[0] && !strcasecmp(query_values[0], "json")) {
        format = NP_CONNECTOR_WIREGUARD_STATS_FORMAT_JSON;
    }

    struct np_connector_wireguard_stats *stats = &last_reported_np_conn_wg_stats;
    ZDP(format,
        stats->this_npconnector_listen_port,
        stats->peers_count_total,
        stats->peers_count_total_max,
        stats->peers_count_npgateways,
        stats->allowed_ips_total_cnt,
        stats->allowed_ips_total_cnt_max,
        stats->allowed_ips_npgateways_cnt,
        stats->rx_total_bytes,
        stats->tx_total_bytes,
        stats->rx_npgateways_bytes,
        stats->tx_npgateways_bytes,
        stats->rx_total_bytes_per_sec,
        stats->tx_total_bytes_per_sec,
        stats->rx_npgateways_bytes_per_sec,
        stats->tx_npgateways_bytes_per_sec,
        stats->handshake_npgateways_hist_0,
        stats->handshake_npgateways_hist_1,
        stats->handshake_npgateways_hist_2,
        stats->persistent_keepalive_off_count,
        stats->npconnector_stats_monitor_cnt,
        stats->npconnector_stats_monitor_error_cnt,
        stats->allowed_ips_npgateways_get_error_cnt,
        stats->peers_count_invalid,
        stats->allowed_ips_invalids_cnt,
        stats->rx_invalids_bytes,
        stats->tx_invalids_bytes,
        stats->rx_invalids_bytes_per_sec,
        stats->tx_invalids_bytes_per_sec,
        stats->peer_type_get_err_cnt,
        stats->peer_type_get_err_total_cnt);

    return ZPN_RESULT_NO_ERROR;
}
