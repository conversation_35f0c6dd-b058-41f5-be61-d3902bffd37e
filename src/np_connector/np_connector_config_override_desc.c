/*
 * np_connector_config_override_desc.c. Copyright (C) 2025 Zscaler Inc. All Rights Reserved.
 *
 * NP Connector's config override description registration.
 */

#include "np_connector/np_connector_config_override_desc.h"
#include "np_connector/np_connector_lib.h"


static struct zpath_config_override_desc np_connector_config_override_descriptions[] = {
        {
                .key                = CONFIG_FEATURE_NP_MULTI_LAN_SUBNET_ENABLE,
                .feature_group      = FEATURE_GROUP_NP_CONNECTOR,
                .desc               = "Enable/disable multi Lan subnets support for np connector",
                .details            = "1: feature is enabled\n"
                                      "0: feature is disabled \n"
                                      "default: 0",
                .val_type           = config_type_int,
                .value_traits       = config_value_traits_feature_enablement,
                .component_types    = config_component_appc,
                .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_inst_grp | config_target_gid_type_cust | config_target_gid_type_global,
                .int_range_lo       = LOW_CONFIG_FEATURE_NP_MULTI_LAN_SUBNET_ENABLE,
                .int_range_hi       = HIGH_CONFIG_FEATURE_NP_MULTI_LAN_SUBNET_ENABLE,
                .int_default        = DEFAULT_CONFIG_FEATURE_NP_MULTI_LAN_SUBNET_ENABLE
        }
};

int np_connector_config_ovd_desc_register_all() {

        int desc_count = sizeof(np_connector_config_override_descriptions) / sizeof(struct zpath_config_override_desc);

        int res = NP_CONNECTOR_RESULT_NO_ERROR;
        for (int i = 0; i < desc_count; i++) {
                res = zpath_config_override_desc_register(&np_connector_config_override_descriptions[i]);
                if (res) {
                        break;
                }
        }

        return res;
}
