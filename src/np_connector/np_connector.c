/*
 * np_connector.c . Copyright (C) 2024-2025 Zscaler Inc, All Rights Reserved
 */

#include "argo/argo.h"
#include "zthread/zthread.h"
#include "zcrypt/zcrypt.h"
#include "zpn_enrollment_lib/zpn_enrollment.h"
#include "zpn_assistantd/assistant_admin_probe.h"
#include "npwg_lib/npwg_provider.h"
#include "npwg_lib/npwg_stats.h"
#include "np_lib/np.h"
#include "np_lib/np_bgp.h"
#include "np_lib/np_bgp_gateways_config.h"
#include "np_lib/np_connectors.h"
#include "np_lib/np_tenant_gateways.h"
#include "np_lib/np_client_subnets.h"
#include "np_lib/np_connector_groups.h"
#include "np_lib/np_frr_utils.h"

#include "np_connector/np_connector_lib.h"
#include "np_connector/np_connector.h"
#include "np_connector/np_connector_bgp.h"
#include "np_connector/np_connector_config_override_desc.h"

#define NP_REDUNDANCY_FEATURE_DYNAMIC

ZTAILQ_HEAD(peer_config_head, peer_config);
struct peer_config {
    int64_t peer_gid; //gateway gid
    int number_of_allowed_ips; //number of client subnets
    char public_key[NPWG_KEY_LENGTH + 1];
    char listen_ip[ARGO_INET_ADDRSTRLEN];
    int listen_port;
    int64_t incarnation;

    ZTAILQ_ENTRY(peer_config) peer_config_entry;
};

struct np_connector_enroll_state {
    struct zcrypt_rsa_key *rsa_key;
    char api_hostname[100];
    enum zpn_enrollment_type enroll_type;
    enum zpn_enrollment_style enroll_style;
    char cloud[100];
    char provisioning_key[1024];
    char hw_fingerprint[(((INSTANCE_ID_BYTES + 2) / 3) * 4) + 1];
    char oauth_token[32];
};

/*
 * np_connector_state
 * mostly owned by the np_connector thread
 * except for the bgp_state, which should be owned by np_bgp thread once created
 */
struct np_connector_state {
    int64_t np_connector_gid;
    int64_t np_connector_group_gid;
    int64_t customer_gid;
    int64_t entity_gid;
    struct wally *np_wally;

    npwg_provider provider;

    pthread_t thread;
    struct zevent_base *zbase;
    struct event_base *ebase;
    struct zthread_info *tickle_me;

    /*
     * NP connector self interface keys
     * public key gets stored in np database in np_connector table
     * private key gets stored locally in ciphertext form encrypted by fingerprint id
     */
    struct zcrypt_key* cfg_hw_key;
    char private_key[NPWG_KEY_LENGTH + 1];
    char public_key[NPWG_KEY_LENGTH + 1];
    int64_t key_expire_s;

    /*
     * number of peers that have been successfully configured to wireguard driver.
     * this number is refreshed with size of in memory peers config upon a successful update call to driver.
     */
    size_t peers_in_config;

    int64_t init_us;

    struct np_connector_enroll_state enroll_state;

    struct zhash_table *peer_config_by_gateway_gid;

    /*all the tasks should reside in this queue*/
    struct peer_config_head peers_queue;
    int64_t number_of_peers_in_queue;

    int64_t config_incarnation;

    np_connector_state_cb_f *state_cb;
    int mtu;

    uint16_t gw_port;

    /*
     * we will support both version format
     * version_1: 1.2.3 (NO revision tag)
     * version_2: 1.2.3-4 (WITH revision tag)
     * coming from version upgrade (zpn_assistant_version.expected_frr_version)
     *
     * NP Connector side will always report version_2 format in auth log, a version with the revision tag
     *
     * Expectation for AUM
     * case 1:
     * if admin input version_1 on atlantic, thus zpn_assistant_version.expected_frr_version is version_1 (ie, 10.2)
     * and NP Connector report version_2 (10.2-1)
     * Since the expected version contains no revision, AUM should exclude checking the revision tag from the version NPC reported.API_DEPRECATED_WITH_REPLACEMENT_BEGIN
     *
     * case 2:
     * if admin input version_2 on atlantic, thus zpn_assistant_version.expected_frr_version is version_2 (ie, 10.2-1)
     * and NP Connector report version_2 (10.2-1)
     * Since expected version includes the revision tag, AUM should compare based on the full version_2 format
     *
     */
    char frr_version[256];
    int zscaler_frr;

    uint8_t  enabled:1,
             initialized:1,

            /*
             * 0 == wireguard is not running
             * 1 == wireguard up
             */
             wireguard_running:1,

            /*
             * 0 == not online (offline)
             * 1 == online
             */
             is_online:1,

            /*
             * need_read_config is a flag indicating
             * whether NP connector should re-construct common library cache
             * on peer info (provider->config->peer_map).
             *
             * this flag should be set to 1
             * when NP Capability is disabled && Connector destroy the existing peer cache.
             *
             * the flag should be checked
             * when NP Capability is enabled back && Connector needs to re-construct the npwg cache from wally cache.
             * it should be only done once, once it is done, its reset to 0.
             */
             need_read_config:1,

             terminating:1;

    /*
     * NP Redundancy related information
     */
    uint8_t redundancy_feature_enabled:1, // feature flag
            redundant_mode_enabled:1; // np connector group level config

};

static struct np_connector_state np_connector_state;

static struct argo_structure_description *np_connector_stats_description;

static struct np_connector_stats{                        /* _ARGO: * object_definition */
    int64_t local_privatekey_not_match_public_key;       /* _ARGO: integer */
    int64_t local_privatekey_match_public_key;           /* _ARGO: integer */
    int64_t key_expired;                                 /* _ARGO: integer */
    int64_t err_gen_key_fail;                            /* _ARGO: integer */
    int64_t gen_new_key;                                 /* _ARGO: integer */
    int64_t write_private_key_local;                     /* _ARGO: integer */
    int64_t upload_public_key_to_cloud;                  /* _ARGO: integer */
    int64_t set_peer_fail;                               /* _ARGO: integer */
    int64_t set_peer_fail_redundancy_mismatch;          /* _ARGO: integer */
    int64_t update_wireguard_driver_fail;                /* _ARGO: integer */
    int64_t remove_sunbets_from_gateway;                 /* _ARGO: integer */
    int64_t peer_config_alloc;                           /* _ARGO: integer */
    int64_t peer_config_free;                            /* _ARGO: integer */
    int64_t peer_pub_key_changed_fail_to_remove;         /* _ARGO: integer */
    int64_t send_state;                                  /* _ARGO: integer */
    int64_t start_wg_interface_wg_already_running;       /* _ARGO: integer */
    int64_t start_wg_interface_provider_exist;           /* _ARGO: integer */
    int64_t create_provider;                             /* _ARGO: integer */
    int64_t destroy_provider;                            /* _ARGO: integer */
    int64_t no_provider_when_stopping_wg;                /* _ARGO: integer */
    int64_t read_wally_cache_got_async;                  /* _ARGO: integer */
    int64_t read_wally_cache_got_error;                  /* _ARGO: integer */
    int64_t set_mtu_fail;                                /* _ARGO: integer */


} stats;
#include "np_connector/np_connector_compiled_c.h"

#define MAX_NP_CONNECTOR_HEARTBEAT_TIMEOUT_S 180
#define NP_CONNECTOR_THREAD_STACK_SIZE_BYTE 16 * 1024 * 1024
#define NP_CONNECTOR_THREAD_USER_INT 60 * 1000 * 1000
#define NP_CONNECTOR_HEARTBEAT_TIMER_S 5

#define NP_CONN_PKEY  "np-connector-pkey.crypt"

/* 180 days */
#define NP_CONNECTOR_KEY_EXPIRY_S ((int64_t)(180 *24 * 60ll * 60ll))

static void np_connector_load_peer_config_tables();
static void np_connector_set_frr_version();

/*
 * NP Connector has 2 state, ONLINE or OFFLINE.
 *
 * ONLINE :
 *    - np connector able to start wireguard driver with no error.
 *    - np connector able to configure at least one peer config with no error.
 *
 * OFFLINE:
 *    - when above condition is not met.
 *
 * 2 Conditions when arriving here
 *
 *    - when we updated peers config in wireguard driver
 *    - when we stop wireguard from running state
 */
static void np_connector_evaluate_state_change()
{
    int previous_state = np_connector_state.is_online;

    if (np_connector_state.wireguard_running && np_connector_state.peers_in_config > 0) {
        np_connector_state.is_online = 1;
    } else {
        np_connector_state.is_online = 0;
    }

    if ((previous_state != np_connector_state.is_online) && np_connector_state.state_cb) {
        np_connector_state.state_cb();
        stats.send_state++;
    }
}

static void np_connector_free_peer_config_from_cache()
{
    struct peer_config* cur_peer;
    struct peer_config *tmp_peer;
    int64_t peer_gid = 0;

    ZTAILQ_FOREACH_SAFE(cur_peer, &(np_connector_state.peers_queue), peer_config_entry, tmp_peer) {
        peer_gid = cur_peer->peer_gid;

        int res = zhash_table_remove(np_connector_state.peer_config_by_gateway_gid, &peer_gid, sizeof(peer_gid), cur_peer);
        if (res) {
            NP_CONNECTOR_LOG(AL_ERROR, "%"PRId64" can not remove peer config when its not valid", peer_gid);
        }

        ZTAILQ_REMOVE(&(np_connector_state.peers_queue), cur_peer, peer_config_entry);
        np_connector_state.number_of_peers_in_queue--;

        NP_CONNECTOR_FREE(cur_peer);
        stats.peer_config_free++;
    }
}

static int np_connector_stop_wireguard(int is_term)
{
    int res = NP_CONNECTOR_RESULT_NO_ERROR;

    res = npwg_delete_wireguard_interface();
    if (res) {
        NP_CONNECTOR_LOG(AL_WARNING, "%s , Could not stop wireguard... ", np_connector_state.enabled?"BAD CONFIG": "DISABLED state");
        return res;
    }

    np_connector_state.wireguard_running = 0;
    np_connector_state.peers_in_config = 0;
    if (is_term) {
        np_connector_state.terminating = 1;
    }

    np_connector_evaluate_state_change();
    if (is_term) {
        sleep(2);
    }

    if (np_connector_state.provider) {
        npwg_destroy_provider(&np_connector_state.provider);
        np_connector_state.provider = NULL;
        stats.destroy_provider++;
    }

    np_connector_free_peer_config_from_cache();
    npwg_provider_update_wg_status_file(0);

    return res;
}

static int np_connector_update_wireguard()
{
    int res = NP_CONNECTOR_RESULT_NO_ERROR;

    res = npwg_update_wireguard(np_connector_state.provider, np_connector_state.redundant_mode_enabled);
    if (res) {
        stats.update_wireguard_driver_fail++;
        NP_CONNECTOR_LOG(AL_WARNING, "Attempted to update wireguard but got error code %d", res);
        return res;
    }

    np_connector_state.peers_in_config = np_provider_get_config_peer_size(np_connector_state.provider);
    np_connector_evaluate_state_change();

    return res;
}

static int
np_connector_dump_stats(struct zpath_debug_state*  request_state,
                        __attribute__((unused))const char**               query_values,
                        __attribute__((unused))int                        query_value_count,
                        __attribute__((unused))void*                      cookie)
{
    char jsonout[10000];

    if (ARGO_RESULT_NO_ERROR == argo_structure_dump(np_connector_stats_description,
                                                    &stats, jsonout, sizeof(jsonout), NULL, 1)) {
        ZDP("%s\n", jsonout);
    }
    return NP_CONNECTOR_RESULT_NO_ERROR;
}

static int
np_connector_dump_state(struct zpath_debug_state*  request_state,
                        __attribute__((unused))const char**               query_values,
                        __attribute__((unused))int                        query_value_count,
                        __attribute__((unused))void*                      cookie)
{

    ZDP("gid : %"PRId64" \n", np_connector_state.np_connector_gid);
    ZDP("connector group gid : %"PRId64" \n", np_connector_state.np_connector_group_gid);
    ZDP("wireguard_running : %d \n", np_connector_state.wireguard_running);
    ZDP("enabled : %d \n", np_connector_state.enabled);
    ZDP("peers_in_config : %zu \n", np_connector_state.peers_in_config);
    ZDP("number of peers in queue : %"PRId64"  \n", np_connector_state.number_of_peers_in_queue);
    ZDP("state : %s \n", np_connector_state.is_online?"online":"offline");
    ZDP("mtu : %d \n", np_connector_state.mtu);
    ZDP("gw_port : %"PRIu16" \n", np_connector_state.gw_port);

    return NP_CONNECTOR_RESULT_NO_ERROR;
}

static int
np_connector_dump_peers(struct zpath_debug_state*              request_state,
                        __attribute__((unused))const char **   query_values,
                        __attribute__((unused))int             query_value_count,
                        __attribute__((unused))void*           cookie)
{
    struct peer_config* cur_peer;
    struct peer_config *tmp_peer;
    int peer_count = 0;

    ZDP("===local peers summary===\n");

    ZTAILQ_FOREACH_SAFE(cur_peer, &(np_connector_state.peers_queue), peer_config_entry, tmp_peer) {
        peer_count++;
        ZDP("peer #%d:\n", peer_count);
        ZDP("listener ip:port %s:%d\n", cur_peer->listen_ip, cur_peer->listen_port);
        ZDP("number of allowed IPs %d\n", cur_peer->number_of_allowed_ips);
        ZDP("incarnation %"PRId64" | global incarnation %"PRId64" \n", cur_peer->incarnation, np_connector_state.config_incarnation);
        ZDP("\n");
    }

    return NP_CONNECTOR_RESULT_NO_ERROR;

}

static int np_connector_debug_init(void)
{
    int res;

    res = zpath_debug_add_read_command("dump the stats of np_connector_stats",
                                  "/np_connector/np_connector_stats",
                                  np_connector_dump_stats,
                                  NULL,
                                  NULL);
    if (res){
        return NP_CONNECTOR_RESULT_ERR;
    }
    res = zpath_debug_add_read_command("dump the state of np_connector",
                                  "/np_connector/np_connector_state",
                                  np_connector_dump_state,
                                  NULL,
                                  NULL);
    if (res){
        return NP_CONNECTOR_RESULT_ERR;
    }

    res = zpath_debug_add_read_command("dump the local cache of peer config",
                                  "/np_connector/dump_peers",
                                  np_connector_dump_peers,
                                  NULL,
                                  NULL);
    if (res){
        return NP_CONNECTOR_RESULT_ERR;
    }

    res = zpath_debug_add_read_command("NP connector wireguard stats",
                                      "/np_connector/wg/stats",
                                      np_connector_wg_dump,
                                      NULL,
                                      "format",  "<optional> format=json, or plain text otherwise",
                                      NULL);
    if (res) {
        NP_CONNECTOR_LOG(AL_ERROR, "Could not init /npconnector/wg/stats debug command");
        return NP_CONNECTOR_RESULT_ERR;
    }

    return NP_CONNECTOR_RESULT_NO_ERROR;
}

/*
 * look for np-connector-pkey.crypt from current working directory (/opt/zscaler/var)
 * if there exist np-connector-pkey.crypt, means we have previously created a keypair
 */
static void np_connector_get_private_key()
{
    FILE *fp;
    uint8_t file_data[2000];
    uint8_t out_data[2000];
    size_t file_len;
    size_t out_len;
    int i;

    fp = fopen(NP_CONN_PKEY, "r");
    if (fp) {
        file_len = fread(file_data, 1, sizeof(file_data), fp);
        fclose(fp);
        out_len = sizeof(out_data);
        if (out_len < file_len) {
            NP_CONNECTOR_LOG(AL_ERROR, "File too large: %s\n", NP_CONN_PKEY);
            return;
        }

        if (zcrypt_decrypt(np_connector_state.cfg_hw_key, file_data, file_len, out_data, &out_len) != ZCRYPT_RESULT_NO_ERROR) {
            NP_CONNECTOR_LOG(AL_ERROR, "Cannot decrypt file: %s", NP_CONN_PKEY);
            return;
        }

        if (out_len > sizeof(np_connector_state.private_key)) {
            NP_CONNECTOR_LOG(AL_ERROR, "Decrypted file too large: %s, out_len %zu, private_key %ld", NP_CONN_PKEY, out_len, sizeof(np_connector_state.private_key));
            return;
        }

        for (i = 0; i < out_len; i++) {
            np_connector_state.private_key[i] = out_data[i];
        }

        out_data[i] = 0;

        return;
    }
}

static int np_connector_write_private_key_to_local(char* private_key, int private_key_len)
{
    FILE *fp;
    uint8_t file_data[4096];
    size_t file_len;

    fp = fopen(NP_CONN_PKEY, "w");
    if (!fp) {
        NP_CONNECTOR_LOG(AL_ERROR, "Cannot open %s for writing\n", NP_CONN_PKEY);
        return NP_CONNECTOR_RESULT_ERR;
    }

    file_len = sizeof(file_data);
    if (zcrypt_encrypt(np_connector_state.cfg_hw_key, private_key, private_key_len, file_data, &file_len) != ZCRYPT_RESULT_NO_ERROR) {
        NP_CONNECTOR_LOG(AL_ERROR, "Cannot encrypt for %s\n", NP_CONN_PKEY);
        fclose(fp);
        unlink(NP_CONN_PKEY);
        return NP_CONNECTOR_RESULT_ERR;
    }

    if (fwrite(file_data, file_len, 1, fp) != 1) {
        NP_CONNECTOR_LOG(AL_ERROR, "Cannot write to %s\n", NP_CONN_PKEY);
        fclose(fp);
        unlink(NP_CONN_PKEY);
        return NP_CONNECTOR_RESULT_ERR;
    }

    fclose(fp);

    return NPWG_RESULT_NO_ERROR;

}

/*
 * 2 confitions arriving here
 *
 *  1. When np connector thread first started, it freshly register its np_connectors.assistant_gid
 *     row callback, upon row callback, Connector have its wireguard public/private key pair ready,
 *     store it in cache in np_connector_state struct, and connector bring up wireguard interface here.
 *
 *  2. When NP Connector functionality gets disabled and enabled back during runtime. We will start wg interface.
 *
 * We only start wireguard interface in following conditions
 *  -  NP feature is in enabled state
 *  -  Wireguard is not currently running (np_connector_state.wireguard_running need to be set after npwg_start_wireguard)
 *  -  There is valid private key
 *  -
 */
static int np_connector_start_wg_interface(const char* private_key)
{
    int res = NP_CONNECTOR_RESULT_NO_ERROR;

    if (np_connector_state.terminating) {
        return NP_CONNECTOR_RESULT_NO_ERROR;
    }

    if (!np_connector_state.enabled) {
        NP_CONNECTOR_DEBUG_CFG("NP Connector in disabled state, skippings the wireguard driver update");
        return NP_CONNECTOR_RESULT_NO_ERROR;
    }

    if (np_connector_state.wireguard_running) {
        stats.start_wg_interface_wg_already_running++;
        return NP_CONNECTOR_RESULT_NO_ERROR;
    }

    if (!private_key) {
        NP_CONNECTOR_LOG(AL_ERROR, "Invalid Privatey key to start wireguard interface");
        return NP_CONNECTOR_RESULT_BAD_ARGUMENT;
    }

    if (np_connector_state.provider) {
        stats.start_wg_interface_provider_exist++;
        NP_CONNECTOR_LOG(AL_NOTICE, "Provider handler present when setting interface, freeing before create a new one");

        npwg_destroy_provider(&np_connector_state.provider);
        np_connector_state.provider = NULL;
        stats.destroy_provider++;

    }

    np_connector_state.provider = npwg_create_provider();
    if (!np_connector_state.provider) {
        NP_CONNECTOR_LOG(AL_WARNING, "Cannot create np_connector_state.provider when starting interface");
        return NP_CONNECTOR_RESULT_NO_MEMORY;
    }
    stats.create_provider++;

    res = npwg_set_interface_config(np_connector_state.provider, private_key, 0);
    if (res) {
        NP_CONNECTOR_LOG(AL_WARNING, "NP Connector: Could not npwg_set_interface_config, err %d", res);
        return NP_CONNECTOR_RESULT_ERR;
    }

    //safety guard, incase previously we exit without stopping wireguard.
    res = npwg_delete_wireguard_interface();
    if (res) {
        NP_CONNECTOR_LOG(AL_NOTICE, "Could not stop wireguard interface before starting it, error code %d, but its okay.. proceeding ", res);
    }

    res = npwg_start_wireguard(np_connector_state.provider);
    if (res) {
        np_connector_state.wireguard_running = 0;
        NP_CONNECTOR_LOG(AL_ERROR, "Could not start wireguard interface, error code %d ", res);
        return res;
    }
    np_connector_state.wireguard_running = 1;
    NP_CONNECTOR_LOG(AL_NOTICE, "Wireguard Interface UP");

    res = npwg_stats_wg_monitor_init(np_connector_state.provider);
    if (res) {
        NP_CONNECTOR_LOG(AL_ERROR, "ERROR npwg_stats_monitor_thread_init unable to init thread: %s",
                zpath_result_string(res));
        return res;
    }

    npwg_provider_update_wg_status_file(1);

    np_connector_load_peer_config_tables();

    return NP_CONNECTOR_RESULT_NO_ERROR;
}


/*
 * check np_connector_state.enabled before we update wireguard driver.
 * if np_connector_state.enabeld = 0, will skip the update driver, the config is saved in mem anyways
 */
static int np_connector_update_wireguard_check_state()
{
    if (!np_connector_state.enabled) {
        NP_CONNECTOR_DEBUG_CFG("NP Connector in disabled state, skippings the wireguard driver update");
        return NP_CONNECTOR_RESULT_NO_ERROR;
    }

    return np_connector_update_wireguard();
}


static void np_connectors_row_callback_np_thread(__attribute__((unused)) struct zevent_base *base,
                                                 void *void_cookie,
                                                 __attribute__((unused)) int64_t int_cookie)
{
    struct argo_object* row = void_cookie;
    struct np_connectors *np_connector = (struct np_connectors *)row->base_structure_void;
    int res;
    int need_to_gen_new_key = 1;

    if (!np_connector) {
        goto done;
    }

    if (np_connector->assistant_gid != np_connector_state.entity_gid) {
        NP_CONNECTOR_LOG(AL_ERROR, "RX assistant %"PRId64" from db does not match cur entity gid %"PRId64" ", np_connector->assistant_gid, np_connector_state.entity_gid);
        goto done;
    }

    if (np_connector->deleted) {
        NP_CONNECTOR_LOG(AL_WARNING, "NP Connector is deleted, please re-provision np connector...");
        sleep(1);
        exit(0);
    }

    np_connector_state.np_connector_gid = np_connector->gid;
    np_connector_bgp_set_np_connector_gid(np_connector->gid);
    np_connector_state.np_connector_group_gid = np_connector->connector_group_gid;
    np_connector_bgp_set_np_connector_group_gid(np_connector->connector_group_gid);

    np_connector_state.gw_port =(uint16_t)np_connector->gateway_listener_port;

    /*pull out private key from local cwd*/
    np_connector_get_private_key();

    if (np_connector_state.private_key[0] != '\0' && np_connector->public_key[0] != '\0') {
        /*
         * we have previously generate wireguard public key pair and have private key stored locally
         * we also have the corresponding public key that we stored in our db
         *
         * we will check if the public key and private key matches,
         * if they match, we will continue to use the same key pair (until key rotation)
         */
        res = npwg_validate_keys(np_connector_state.private_key, np_connector->public_key);
        if (res != NP_CONNECTOR_RESULT_NO_ERROR) {
            NP_CONNECTOR_DEBUG_KEY("NP Connector: Local private key does not matched public key, generating new key");
            stats.local_privatekey_not_match_public_key++;
        } else {
//ET-88120 enable key expiry check when we support NP key rotation
#if 0

            /* key matched, but lets check expiry*/
            if (epoch_s() > np_connector->public_key_expiry) {
                NP_CONNECTOR_LOG(AL_NOTICE, "Wireguard expired, generating new key");
                stats.key_expired++;
            } else {
                need_to_gen_new_key = 0;
                stats.local_privatekey_match_public_key++;
                snprintf(np_connector_state.public_key, sizeof(np_connector_state.public_key), "%s", np_connector->public_key);
            }
#else
            need_to_gen_new_key = 0;
            stats.local_privatekey_match_public_key++;
            snprintf(np_connector_state.public_key, sizeof(np_connector_state.public_key), "%s", np_connector->public_key);
#endif
        }
    }

    if (need_to_gen_new_key) {
        NP_CONNECTOR_DEBUG_KEY("NP Connector: Generating new key pair");
        res = npwg_gen_keys(np_connector_state.private_key, NPWG_KEY_LENGTH + 1,
                            np_connector_state.public_key, NPWG_KEY_LENGTH + 1);
        if (res) {
            stats.err_gen_key_fail++;
            NP_CONNECTOR_LOG(AL_WARNING, "NP Connector: Not able to genearte wireguard key pairs, err code %d", res);
            goto done;
        }
        stats.gen_new_key++;

        np_connector_state.key_expire_s = epoch_s() + NP_CONNECTOR_KEY_EXPIRY_S;

        if (np_connector_state.enroll_state.enroll_style == ZPN_ENROLLMENT_STYLE_V3) {
            /*since we generated new key, we have to upload to database via enrollment service */
            res = zpn_enroll_send_public_key_per_entity_v3(np_connector_state.enroll_state.rsa_key,
                                                           np_connector_state.enroll_state.api_hostname,
                                                           np_connector_state.enroll_state.enroll_type,
                                                           np_connector_state.public_key,
                                                           np_connector_state.key_expire_s,
                                                           np_connector_state.enroll_state.provisioning_key,
                                                           np_connector_state.enroll_state.hw_fingerprint,
                                                            np_connector_state.customer_gid);
            if (res) {
                NP_CONNECTOR_LOG(AL_ERROR, "Could not zpn_enroll_send_public_key_per_entity_v3, err code %d", res);
                goto done;
            }
        } else if (np_connector_state.enroll_state.enroll_style == ZPN_ENROLLMENT_STYLE_V4) {
            /*since we generated new key, we have to upload to database via enrollment service */
            res = zpn_enroll_send_public_key_per_entity_v4(np_connector_state.enroll_state.rsa_key,
                                                           np_connector_state.enroll_state.api_hostname,
                                                           np_connector_state.enroll_state.enroll_type,
                                                           np_connector_state.public_key,
                                                           np_connector_state.key_expire_s,
                                                           np_connector_state.enroll_state.provisioning_key,
                                                           np_connector_state.enroll_state.hw_fingerprint,
                                                           np_connector_state.customer_gid,
                                                           np_connector_state.enroll_state.oauth_token);
            if (res) {
                NP_CONNECTOR_LOG(AL_ERROR, "[V4 Enrollment] Could not zpn_enroll_send_public_key_per_entity_v4, err code %d", res);
                goto done;
            }
        } else {
            NP_CONNECTOR_LOG(AL_ERROR, "enroll version %d not support to upload public key, err code %d", np_connector_state.enroll_state.enroll_style, res);
            goto done;
        }
        stats.upload_public_key_to_cloud++;

        res = np_connector_write_private_key_to_local(np_connector_state.private_key, NPWG_KEY_LENGTH + 1);
        if (res) {
            NP_CONNECTOR_LOG(AL_WARNING, "NP Connector: np_connector_write_private_key_to_local failed");
            goto done;
        }
        stats.write_private_key_local++;
    }

    res = np_connector_start_wg_interface(np_connector_state.private_key);
    if (res) {
        NP_CONNECTOR_LOG(AL_WARNING, "NP Connector: Failed to bring up WireGuard Interface");
        goto done;
    }

done:
    argo_object_release(row);
    return;
}

static int np_connector_np_connectors_table_row_callback(__attribute__((unused))void *cookie,
                                                         __attribute__((unused))struct wally_registrant *registrant,
                                                         __attribute__((unused))struct wally_table *table,
                                                         __attribute__((unused))struct argo_object *previous_row,
                                                         struct argo_object *row,
                                                         __attribute__((unused))int64_t request_id)
{
    if (np_connector_debug_log & NP_CONNECTOR_DEBUG_TABLE_BIT) {
        char dump[8000];
        if (argo_object_dump(row, dump, sizeof(dump), NULL, 0) == ARGO_RESULT_NO_ERROR) {
            NP_CONNECTOR_LOG(AL_NOTICE, "Row callback: %s", dump);
        }
    }

    if (!np_connector_state.zbase) {
        NP_CONNECTOR_LOG(AL_ERROR, "RX NP Connectors row entries from db but NP Connector thread not init");
        return NP_CONNECTOR_RESULT_NO_ERROR;
    }

    argo_object_hold(row);
    if (0 != zevent_base_call(np_connector_state.zbase, np_connectors_row_callback_np_thread, row, 0)) {
        argo_object_release(row);
    }
    return NP_CONNECTOR_RESULT_NO_ERROR;
}

static void np_connector_delete_peer_config_cache(int64_t peer_gid)
{
    struct peer_config* cur_peer;

    cur_peer = zhash_table_lookup(np_connector_state.peer_config_by_gateway_gid, &peer_gid, sizeof(peer_gid), NULL);
    if (!cur_peer) {
        NP_CONNECTOR_LOG(AL_ERROR, "%"PRId64" not found in peer cache", peer_gid);
        return;
    }

    int res = zhash_table_remove(np_connector_state.peer_config_by_gateway_gid, &peer_gid, sizeof(peer_gid), cur_peer);
    if (res) {
        NP_CONNECTOR_LOG(AL_ERROR, "%"PRId64" can not remove peer config when its not valid", peer_gid);
        return;
    }

    ZTAILQ_REMOVE(&(np_connector_state.peers_queue), cur_peer, peer_config_entry);
    np_connector_state.number_of_peers_in_queue--;

    NP_CONNECTOR_FREE(cur_peer);
    stats.peer_config_free++;

}

static int np_connector_remove_gateway(struct np_tenant_gateways *np_tenant_gateway)
{
    if (!np_tenant_gateway) {
        NP_CONNECTOR_LOG(AL_ERROR, "No np_tenant_gateway to remove");
        return NP_CONNECTOR_RESULT_ERR;
    }

    int res = NP_CONNECTOR_RESULT_NO_ERROR;

    res = npwg_delete_peer_config(np_connector_state.provider, np_tenant_gateway->public_key);
    if (res) {
        NP_CONNECTOR_LOG(AL_ERROR, "cannot delete peer config for gateway %"PRId64" with res code %s ", np_tenant_gateway->gid, np_connector_result_string(res));
        return res;
    } else {
        np_connector_delete_peer_config_cache(np_tenant_gateway->gid);
    }

    res = np_connector_update_wireguard_check_state();
    if (res) {
        NP_CONNECTOR_LOG(AL_NOTICE, "Fail to write wireguard driver for gateway %"PRId64" , will try update again later, %s", np_tenant_gateway->gid, np_connector_result_string(res));
    }

    return NP_CONNECTOR_RESULT_NO_ERROR;
}

void np_connector_update_or_create_peer_config_cache(int64_t peer_gid,
                                                     int number_of_allowed_ip,
                                                     const char *public_key,
                                                     const struct argo_inet *listener_ip,
                                                     int listener_port)
{
    struct peer_config* cur_peer;
    int new = 0;

    cur_peer = zhash_table_lookup(np_connector_state.peer_config_by_gateway_gid, &peer_gid, sizeof(peer_gid), NULL);
    if (!cur_peer) {
        cur_peer = NP_CONNECTOR_CALLOC(sizeof(*cur_peer));
        if (!cur_peer) {
            NP_CONNECTOR_LOG(AL_ERROR, "%"PRId64" can not allocate peer info in cache ", peer_gid);
            return;
        } else {
            stats.peer_config_alloc++;
            new = 1;
        }
    }

    //update with latest info
    cur_peer->peer_gid = peer_gid;
    cur_peer->number_of_allowed_ips = number_of_allowed_ip;
    snprintf(cur_peer->public_key, sizeof(cur_peer->public_key), "%s", public_key);
    argo_inet_generate(cur_peer->listen_ip, listener_ip);
    cur_peer->listen_port = listener_port;

    if (new) {
        cur_peer->incarnation = np_connector_state.config_incarnation;

        ZTAILQ_INSERT_TAIL(&(np_connector_state.peers_queue), cur_peer, peer_config_entry);
        np_connector_state.number_of_peers_in_queue++;

        int res = zhash_table_store(np_connector_state.peer_config_by_gateway_gid, &(peer_gid), sizeof(peer_gid), 0, cur_peer);
        if (res) {
            NP_CONNECTOR_LOG(AL_ERROR, "%"PRId64" can not store peer config in cache ", peer_gid);
            return;
        }
    }
}

/* we might do duplicate update, but its expected, as we want to do best effort updating wireguard with config
 * we do not want to miss any config!
 *
 * scenario:
 * during intialization phase, NP Connector register all NP config and all existing entries would come all at once.
 * but its not gurantee if its a np_tenant_gateway row arrives early or np_client_subnets row arrives early.
 *
 * if there are 3 client_subnets mapped to 1 np_tenant_gateway.
 * the order of data arrival could be:
 *
 * client_subnet1, client_subnet2, gateway, client_subnet3
 *
 * for each row update, we will update in memory config baed on existing in-memory data.
 * but only the final row comes, we will update in memory config with the complete data.
 *
 * With NP Redundancy, we also need to add gateway's class E IP in allowed IP section. However this information is
 * stored in another table (np_bgp_gateways_config), so we need to handle gateway peer update from two places, just
 * like we have done for clinet subnets table.
 *
 * TODO: If we want to support selective peering between subset of gateways and a connector,
 * this route / caller has to change because currently we assume a full mesh
 */
static int np_connector_add_or_update_gateway(struct np_tenant_gateways *np_tenant_gateway)
{
    if (!np_tenant_gateway) {
        NP_CONNECTOR_LOG(AL_ERROR, "No np_tenant_gateway to add or update");
        return NP_CONNECTOR_RESULT_ERR;
    }

    int res = NP_CONNECTOR_RESULT_NO_ERROR;

    struct np_client_subnets **links = NP_CONNECTOR_CALLOC(sizeof(struct np_client_subnets *) * NPWG_MAX_ALLOWED_IPS_PER_PEER);
    if (!links) {
        NP_CONNECTOR_LOG(AL_ERROR, "Failed to allocate memory for client subnet links.");
        return NP_CONNECTOR_RESULT_NO_MEMORY;
    }
    size_t client_subnets_arr_len = NPWG_MAX_ALLOWED_IPS_PER_PEER;

    res = np_client_subnets_get_by_gateway_gid_immediate(np_tenant_gateway->gid,
                                                         links,
                                                         &client_subnets_arr_len);

    /*
     * valid situation where the gateway row entry arrives before NP clients subnets
     * we will wait for NP clients subnets info
     */
    if (res == NP_CONNECTOR_RESULT_ASYNCHRONOUS) {
        NP_CONNECTOR_DEBUG_CONFIG("Table np_client_subnets look up by %"PRId64" return async, will update gatewa config later when subnets row come", np_tenant_gateway->gid);
        NP_CONNECTOR_FREE(links);
        return NP_CONNECTOR_RESULT_NO_ERROR;
    } else if (res != NP_CONNECTOR_RESULT_NO_ERROR) {
        NP_CONNECTOR_FREE(links);
        NP_CONNECTOR_LOG(AL_ERROR, "Table np_client_subnets lookup by gateway gid %"PRId64" result in %s", np_tenant_gateway->gid, np_connector_result_string(res));
        return NP_CONNECTOR_RESULT_ERR;
    }

    NP_CONNECTOR_DEBUG_TABLE("Table np_client_subnets look up by gateway gid %"PRId64" return %zu results ", np_tenant_gateway->gid, client_subnets_arr_len);

    /*
     * check connector mode and gateway mode
     * if redundancy feature is not enabled, use default 51820 which is np_tenant_gateway->listener_port
     * if redundancy feature is enabled:
     *   if both mode are in redundant mode, use np_connector_state.gw_port passed in to npwg_set_gateway_peer
     *   if both mode are NOT in redundant mode, use default 51820 which is np_tenant_gateway->listener_port,
     *   if different mode, ignore entry.
     */
    int add_gw_class_e_ip = 0;
    uint16_t gateway_listener_port = (uint16_t)np_tenant_gateway->listener_port;
    if (np_connector_state.redundancy_feature_enabled) {
        if (np_connector_state.redundant_mode_enabled && np_tenant_gateway->redundant_mode_enabled) {
            gateway_listener_port = np_connector_state.gw_port;
            add_gw_class_e_ip = 1;
        } else if (!np_connector_state.redundant_mode_enabled && !np_tenant_gateway->redundant_mode_enabled) {
            gateway_listener_port = (uint16_t)np_tenant_gateway->listener_port;
        } else {
            /* redundancy mode is set differently on connector and gateway, skip setting this gateway as peer */
            stats.set_peer_fail_redundancy_mismatch++;
            NP_CONNECTOR_LOG(AL_ERROR, "Could not store peer config for gateway %"PRId64", redundancy mode mismatch", np_tenant_gateway->gid);
            NP_CONNECTOR_FREE(links);
            return NP_CONNECTOR_RESULT_NO_ERROR;
        }
    }

    /* Prepare the list of allowed ips to be configured for this gateway peer
     * If redundancy feature is enabled, we are running in redundant mode,and the gateway is also running
     * redundant mode, we need to add the gateway class E /16 subnet to allowed IP list in addition.
     */
    size_t max_allowed_inet;
    if (add_gw_class_e_ip) {
        max_allowed_inet = client_subnets_arr_len + 1;
    } else {
        max_allowed_inet = client_subnets_arr_len;
    }
    struct argo_inet *allowed_inet = NP_CONNECTOR_CALLOC(sizeof(struct argo_inet) * max_allowed_inet);
    if (!allowed_inet) {
        NP_CONNECTOR_LOG(AL_ERROR, "Failed to allocate memory for client subnets inet.");
        NP_CONNECTOR_FREE(links);
        return NP_CONNECTOR_RESULT_NO_MEMORY;
    }
    size_t allowed_inet_count = 0;
    for (int i = 0; i < client_subnets_arr_len; i++) {
        if (!links[i]->subnet) {
            NP_CONNECTOR_LOG(AL_NOTICE, "Subnet gid: %"PRId64" has NULL subnet, ignoring", links[i]->gid);
            continue;
        }
        allowed_inet[i] = *links[i]->subnet;
        allowed_inet_count++;
    }
    if (add_gw_class_e_ip && (allowed_inet_count < max_allowed_inet)) {
        /* Fetch gateway's class E IP from np_bgp_gateways_config table and append it */
        struct np_bgp_gateways_config *bgp_gateway = NULL;
        res = np_bgp_gateways_config_get_by_gateway_gid_immediate(np_tenant_gateway->gid, &bgp_gateway);
        if (res) {
            NP_CONNECTOR_DEBUG_BGP("Unable to get np_bgp_gateways_config for gateway gid: %"PRId64" - %s, skip adding class E IP",
                                        np_tenant_gateway->gid, zpath_result_string(res));
            /* If we are not able to get the class E IP, we will not increment allowed_inet_count */
        } else {
            if (bgp_gateway && bgp_gateway->router_id) {
                allowed_inet[allowed_inet_count++] = *(bgp_gateway->router_id);
                /* Add class E IP to allowed inet list and increment allowed_inet_count */
                char dump_str[ARGO_INET_ADDRSTRLEN];
                NP_CONNECTOR_DEBUG_BGP("Added class E IP %s for gateway gid: %"PRId64"",
                                        argo_inet_generate(dump_str, bgp_gateway->router_id), np_tenant_gateway->gid);
            } else {
                NP_CONNECTOR_LOG(AL_WARNING, "BGP gateway config for GID %"PRId64" retrieved successfully but either gateway or its router_id is NULL",
                                            np_tenant_gateway->gid);
            }
        }
    }
    res = npwg_set_gateway_peer(np_connector_state.provider, np_tenant_gateway->public_key, np_tenant_gateway->listener_ip_address,
                                gateway_listener_port, allowed_inet, allowed_inet_count);
    if (res) {
        stats.set_peer_fail++;
        NP_CONNECTOR_LOG(AL_ERROR, "Could not store peer config for gateway %"PRId64", err code %s ", np_tenant_gateway->gid, np_connector_result_string(res));
        goto done;
    } else {
        np_connector_update_or_create_peer_config_cache(np_tenant_gateway->gid, (int)allowed_inet_count, np_tenant_gateway->public_key, np_tenant_gateway->listener_ip_address, np_tenant_gateway->listener_port);
    }

    NP_CONNECTOR_DEBUG_CONFIG("Added peer config gateway %"PRId64" along with %zu allowed ip into wireguard config in mem, now total of %zu peers configured ", np_tenant_gateway->gid, allowed_inet_count, (size_t) np_provider_get_config_peer_size(np_connector_state.provider));

    res = np_connector_update_wireguard_check_state();
    if (res) {
        NP_CONNECTOR_LOG(AL_NOTICE, "Could not update or start wireguard config for gateway %"PRId64" , will try again later, %s", np_tenant_gateway->gid, np_connector_result_string(res));
    }

done:
    NP_CONNECTOR_FREE(links);
    NP_CONNECTOR_FREE(allowed_inet);

    return res;
}

static void add_or_update_gateway_on_np_thread(__attribute__((unused))struct zevent_base *base,
                                               __attribute__((unused)) void *void_cookie,
                                               int64_t int_cookie)
{
    int64_t tenant_gateway_gid = int_cookie;
    struct np_tenant_gateways *np_tenant_gateway = NULL;
    int res;

    res = np_tenant_gateways_get_by_id_immediate(tenant_gateway_gid, &np_tenant_gateway);
    if (res) {
        /* gateway not found, most likely row hasn't arrived yet, the np_tenant_gateway row callback routine will handle it */
        return;
    }

    if (np_tenant_gateway->gateway_state != NP_TENANT_GATEWAY_STATE_ONLINE &&
        np_tenant_gateway->gateway_state != NP_TENANT_GATEWAY_STATE_UNHEALTHY) {
        //the gateway is not online, np_tenant_gateway should get a row callback on this and let it handle..
        return;
    }

    res = np_connector_add_or_update_gateway(np_tenant_gateway);
    if (res) {
        NP_CONNECTOR_LOG(AL_ERROR, "Failed to add or update gateway peer, gateway gid: %"PRId64", res: %s",
                                    tenant_gateway_gid, zpath_result_string(res));
    }
}

void np_connector_add_or_update_gateway_from_another_thread(int64_t tenant_gateway_gid)
{
    if (0 != zevent_base_call(np_connector_state.zbase, add_or_update_gateway_on_np_thread, NULL, tenant_gateway_gid)) {
        NP_CONNECTOR_LOG(AL_ERROR, "Failed to call np_connector_add_or_update_gateway on NP Connector thread");
    }
    return;
}

static void np_tenant_gateways_row_callback_np_thread(__attribute__((unused))struct zevent_base *base,
                                                      void *void_cookie,
                                                      __attribute__((unused))int64_t int_cookie)
{
    struct argo_object* row = void_cookie;
    struct np_tenant_gateways *np_tenant_gateway = (struct np_tenant_gateways *)row->base_structure_void;
    if (!np_tenant_gateway) {
        goto done;
    }

    /*
     * 0 - disabled
     * 1 - Enrolled
     * 2 - Online
     * 3 - Unhealthy
     * 4 - Enrolling
     */
    if ((np_tenant_gateway->deleted) ||
        ((np_tenant_gateway->gateway_state != NP_TENANT_GATEWAY_STATE_ONLINE) &&
         (np_tenant_gateway->gateway_state != NP_TENANT_GATEWAY_STATE_UNHEALTHY))) {
        np_connector_remove_gateway(np_tenant_gateway);
        goto done;
    }

    struct peer_config *cur_peer;
    int res;

    cur_peer = zhash_table_lookup(np_connector_state.peer_config_by_gateway_gid, &(np_tenant_gateway->gid), sizeof(np_tenant_gateway->gid), NULL);
    if (cur_peer) {
        if (np_tenant_gateway->public_key && strcmp(cur_peer->public_key, np_tenant_gateway->public_key)) {
            NP_CONNECTOR_DEBUG_CONFIG("%"PRId64" public key changed, delete the old config", np_tenant_gateway->gid);
            res = npwg_delete_peer_config(np_connector_state.provider, cur_peer->public_key);
            if (res) {
                /*running outdate config..s */
                stats.peer_pub_key_changed_fail_to_remove++;
                NP_CONNECTOR_LOG(AL_WARNING, "can not delete peer config for gateway %"PRId64" with res code %s ", np_tenant_gateway->gid, np_connector_result_string(res));
            }
        }
    }

    res = np_connector_add_or_update_gateway(np_tenant_gateway);
    if (res) {
        NP_CONNECTOR_LOG(AL_ERROR, "Failed to add or update gateway peer, gateway gid: %"PRId64", res: %s",
                                    np_tenant_gateway->gid, zpath_result_string(res));
    }

done:
    argo_object_release(row);
    return;

}

static int np_connector_np_tenant_gateways_table_row_callback(__attribute__((unused))void *cookie,
                                                              __attribute__((unused))struct wally_registrant *registrant,
                                                              __attribute__((unused))struct wally_table *table,
                                                              struct argo_object *previous_row,
                                                              struct argo_object *row,
                                                              __attribute__((unused))int64_t request_id)
{
    if (np_connector_debug_log & NP_CONNECTOR_DEBUG_TABLE_BIT) {
        char dump[8000];
        if (argo_object_dump(row, dump, sizeof(dump), NULL, 0) == ARGO_RESULT_NO_ERROR) {
            NP_CONNECTOR_LOG(AL_NOTICE, "Row callback: %s", dump);
        }
    }

    struct np_tenant_gateways *np_tenant_gateway = (struct np_tenant_gateways *)row->base_structure_void;
    if (!np_tenant_gateway) {
        return NP_CONNECTOR_RESULT_NO_ERROR;
    }

    int64_t deleted = np_tenant_gateway->deleted;

    if (deleted && !previous_row) {
        return NP_CONNECTOR_RESULT_NO_ERROR;
    }

    if (!np_connector_state.zbase) {
        NP_CONNECTOR_LOG(AL_ERROR, "RX np gateways row entries from db but NP Connector thread not init");
        return NP_CONNECTOR_RESULT_NO_ERROR;
    }

    argo_object_hold(row);
    if (0 != zevent_base_call(np_connector_state.zbase, np_tenant_gateways_row_callback_np_thread, row, 0)) {
        argo_object_release(row);
    }
    return NP_CONNECTOR_RESULT_NO_ERROR;
}

static void np_client_subnets_row_callback_np_thread(struct zevent_base *base __attribute__((unused)),
                                                     void               *void_cookie,
                                                     int64_t            int_cookie __attribute__((unused)),
                                                     void               *extra_cookie1,
                                                     void               *extra_cookie2 __attribute__((unused)),
                                                     void               *extra_cookie3 __attribute__((unused)),
                                                     int64_t            extra_int_cookie __attribute__((unused)))
{
    struct argo_object* row = void_cookie;
    struct argo_object* previous_row = extra_cookie1;
    struct np_client_subnets *subnet = (struct np_client_subnets *)row->base_structure_void;
    if (!subnet) {
        goto done;
    }

    int64_t tenant_gateway_gid = subnet->gateway_gid;
    struct np_tenant_gateways *np_tenant_gateway = NULL;
    int res;

    res = np_tenant_gateways_get_by_id_immediate(tenant_gateway_gid, &np_tenant_gateway);
    if (res) {
        NP_CONNECTOR_DEBUG_CONFIG("Received client subnet with gateway %"PRId64" lookup return %s", tenant_gateway_gid, np_connector_result_string(res));
        goto bgp_subnet_handler;
    }

    if (np_tenant_gateway->deleted) {
        goto bgp_subnet_handler;
    }

    if (np_tenant_gateway->gateway_state != NP_TENANT_GATEWAY_STATE_ONLINE &&
        np_tenant_gateway->gateway_state != NP_TENANT_GATEWAY_STATE_UNHEALTHY) {
        //the gateway of this client subnets is not online, np_tenant_gateway should get a row callback on this and let it handle..
        goto bgp_subnet_handler;
    }

    if (subnet->deleted) {
        /*
         * subnets is deleted, but not gateway
         */
        stats.remove_sunbets_from_gateway++;
    }

    res = np_connector_add_or_update_gateway(np_tenant_gateway);
    if (res) {
        NP_CONNECTOR_LOG(AL_ERROR, "Failed to add or update gateway peer, gateway gid: %"PRId64", res: %s",
                                    tenant_gateway_gid, zpath_result_string(res));
    }

bgp_subnet_handler:
    if (np_connector_state.redundancy_feature_enabled && np_connector_state.redundant_mode_enabled) {
        /* NP Redundancy feature enabled and we are running in redundancy mode */
        res = np_connector_bgp_client_subnet_handler(previous_row, row);
        if (res) {
            NP_CONNECTOR_LOG(AL_ERROR, "Unable to make thread call to bgp thread for handling client subnet row callback: %s",
                                        zpath_result_string(res));
            goto done;
        }
        /* np_connector_bgp_client_subnet_handler will free the row objects */
        return;
    }

done:
    if (previous_row) argo_object_release(previous_row);
    argo_object_release(row);
    return;

}

int np_connector_np_client_subnets_table_row_callback(__attribute__((unused))void *cookie,
                                                      __attribute__((unused))struct wally_registrant *registrant,
                                                      __attribute__((unused))struct wally_table *table,
                                                      struct argo_object *previous_row,
                                                      struct argo_object *row,
                                                      __attribute__((unused))int64_t request_id)
{
    if (np_connector_debug_log & NP_CONNECTOR_DEBUG_TABLE_BIT) {
        char dump[8000];
        if (argo_object_dump(row, dump, sizeof(dump), NULL, 0) == ARGO_RESULT_NO_ERROR) {
            NP_CONNECTOR_LOG(AL_NOTICE, "Row callback: %s", dump);
        }
    }

    struct np_client_subnets *np_client_subnet = (struct np_client_subnets *)row->base_structure_void;
    if (!np_client_subnet) {
        return NP_CONNECTOR_RESULT_NO_ERROR;
    }

    int64_t deleted = np_client_subnet->deleted;

    if (deleted && !previous_row) {
        return NP_CONNECTOR_RESULT_NO_ERROR;
    }

    if (!np_connector_state.zbase) {
        NP_CONNECTOR_LOG(AL_ERROR, "RX np_client_subnets row entries from db but NP Connector thread not init");
        return NP_CONNECTOR_RESULT_NO_ERROR;
    }

    argo_object_hold(row);
    if (previous_row) argo_object_hold(previous_row);
    if (0 != zevent_base_big_call(np_connector_state.zbase, np_client_subnets_row_callback_np_thread,
                                    row, 0, previous_row, NULL, NULL, 0)) {
        if (previous_row) argo_object_release(previous_row);
        argo_object_release(row);
    }
    return NP_CONNECTOR_RESULT_NO_ERROR;
}

static void np_connector_groups_row_callback_np_thread(__attribute__((unused))struct zevent_base *base,
                                                       void *void_cookie,
                                                       __attribute__((unused))int64_t int_cookie)
{
    struct argo_object* row = void_cookie;
    struct np_connector_groups *np_connector_group = (struct np_connector_groups *)row->base_structure_void;
    if (!np_connector_group || np_connector_group->deleted) {
        goto done;
    }
    int res = NP_CONNECTOR_RESULT_NO_ERROR;

    /*
     * db value unset, connector value note default    -> set to default
     * db value unset, connector value default         -> do nothing
     * db value set,   conenctor value set to db value -> do nothing
     * db value set,   connector value not db value    -> set to db value if within the range
     * else, set interface to default
     */
    if (!np_connector_group->mtu && np_connector_state.mtu != NPWG_DEFAULT_MTU) {
        NP_CONNECTOR_LOG(AL_NOTICE, "MTU is not set in db, setting default %d for wireguard interface for connector group %"PRId64"", NPWG_DEFAULT_MTU, np_connector_group->gid);
        res = npwg_provider_set_mtu(NULL, NPWG_DEFAULT_MTU);
        if (res) {
            NP_CONNECTOR_LOG(AL_WARNING, "Failed setting mtu value %d with error code %d", NPWG_DEFAULT_MTU, res);
            stats.set_mtu_fail++;
            goto done;
        }
        np_connector_state.mtu = NPWG_DEFAULT_MTU;
    } else if ((!np_connector_group->mtu && (np_connector_state.mtu == NPWG_DEFAULT_MTU))||
               (np_connector_group->mtu && (np_connector_state.mtu == np_connector_group->mtu))) {
        NP_CONNECTOR_LOG(AL_NOTICE, "Connector MTU is already set to %d for wireguard interface for connector group %"PRId64"", np_connector_state.mtu , np_connector_group->gid);
        goto done;

    } else if (np_connector_group->mtu && np_connector_group->mtu >= NPWG_MINIMUM_MTU && np_connector_group->mtu <= NPWG_MAXIMUM_MTU) {
        NP_CONNECTOR_LOG(AL_NOTICE, "MTU set to %d from db , setting wireguard interface for connector group %"PRId64"", np_connector_group->mtu, np_connector_group->gid);
        res = npwg_provider_set_mtu(NULL, np_connector_group->mtu);
        if (res) {
            NP_CONNECTOR_LOG(AL_WARNING, "Failed setting mtu value %d with error code %d", np_connector_group->mtu, res);
            stats.set_mtu_fail++;
            goto done;
        }
        np_connector_state.mtu = np_connector_group->mtu;
    } else {
        NP_CONNECTOR_LOG(AL_NOTICE, "MTU set to an invalid value %d from db , setting default %d wireguard interface for connector group %"PRId64"", np_connector_group->mtu, NPWG_DEFAULT_MTU, np_connector_group->gid);
        res = npwg_provider_set_mtu(NULL, NPWG_DEFAULT_MTU);
        if (res) {
            NP_CONNECTOR_LOG(AL_WARNING, "Failed setting mtu value %d with error code %d", NPWG_DEFAULT_MTU, res);
            stats.set_mtu_fail++;
            goto done;
        }
        np_connector_state.mtu = NPWG_DEFAULT_MTU;
    }

    NP_CONNECTOR_LOG(AL_NOTICE, "Wireguard interface is now %d ", np_connector_state.mtu);

    /* check redundant_mode_enabled value */
    int redundancy_mode_changed = 0;
    redundancy_mode_changed = np_connector_state.redundant_mode_enabled != np_connector_group->redundant_mode_enabled;
    if (redundancy_mode_changed) {
        np_connector_state.redundant_mode_enabled = np_connector_group->redundant_mode_enabled ? 1 : 0;
        np_connector_bgp_set_redundant_mode(np_connector_group->redundant_mode_enabled);
        if (np_connector_state.redundant_mode_enabled && np_connector_state.redundancy_feature_enabled) {
            /* redundancy mode is set to enabled and feature is also enabled, initializing bgp state (if not initialized) */
            NP_CONNECTOR_LOG(AL_INFO, "Redundancy mode is set to enabled on np connector group level, initializing NP BGP state");
            np_connector_bgp_state_init(np_connector_state.np_wally, np_connector_state.customer_gid);
        } else if (!np_connector_state.redundant_mode_enabled) {
            /* redundancy mode is set to disabled , destroy bgp state (if initialized) */
            NP_CONNECTOR_LOG(AL_INFO, "Redundancy mode is set to disabled on np connector group level, destroying NP BGP state");
            np_connector_bgp_state_destroy();
            goto done;
        } else {
            /* do nothing */
            NP_CONNECTOR_LOG(AL_INFO, "Redundancy mode is set to enabled but feature is disabled, skip initializing BGP");
        }
    }

    if (np_connector_state.redundant_mode_enabled && np_connector_state.redundancy_feature_enabled) {
        /* Update local sourcing config to in memory BGP state */
        np_connector_bgp_update_local_sourcing_config(np_connector_group->advertise_lan_segments_disabled);
    }

done:
    argo_object_release(row);
    return;
}

static int np_connector_np_connector_groups_table_row_callback(__attribute__((unused))void *cookie,
                                                               __attribute__((unused))struct wally_registrant *registrant,
                                                               __attribute__((unused))struct wally_table *table,
                                                               struct argo_object *previous_row,
                                                               struct argo_object *row,
                                                               __attribute__((unused))int64_t request_id)
{
    if (np_connector_debug_log & NP_CONNECTOR_DEBUG_TABLE_BIT) {
        char dump[8000];
        if (argo_object_dump(row, dump, sizeof(dump), NULL, 0) == ARGO_RESULT_NO_ERROR) {
            NP_CONNECTOR_LOG(AL_NOTICE, "Row callback: %s", dump);
        }
    }

    struct np_connector_groups *np_connector_group = (struct np_connector_groups *)row->base_structure_void;
    if (!np_connector_group) {
        return NP_CONNECTOR_RESULT_NO_ERROR;
    }

    if (!np_connector_state.zbase) {
        NP_CONNECTOR_LOG(AL_ERROR, "RX np_connector_groups row entries from db but NP Connector thread not init");
        return NP_CONNECTOR_RESULT_NO_ERROR;
    }

    argo_object_hold(row);
    if (0 != zevent_base_call(np_connector_state.zbase, np_connector_groups_row_callback_np_thread, row, 0)) {
        argo_object_release(row);
    }
    return NP_CONNECTOR_RESULT_NO_ERROR;

}

/*
 * np_tenant_gateways table and np_client_subnets table init.
 *
 * this init should only happen once, and we should panic if the init fail
 * as this tells the system is really in bad state.
 * (
 *  table init usually happen in the system initialzation time, and intiailization fail if any of the table init fails,
 *  but we are moving peers config table later here, and we should still panic if the table init fails here
 * )
 *
 */
static void np_connector_load_peer_config_tables()
{
    static int init_peer_config = 0;
    int res;

    if (!init_peer_config) {

        res = np_connector_groups_table_init(np_connector_state.np_wally, np_connector_state.customer_gid, np_connector_np_connector_groups_table_row_callback, NULL, 0);
        if (res) {
            NP_CONNECTOR_LOG(AL_ERROR, "Cannot init table np_connector_groups, bad state!");
            goto fail;
        }

        res = np_connector_groups_register_by_connector_group_gid(np_connector_state.np_connector_group_gid);
        if (res) {
            NP_CONNECTOR_LOG(AL_ERROR, "Cannot register table np_connector_groups, bad state!");
            goto fail;
        }

        res = np_tenant_gateways_table_init(np_connector_state.np_wally, np_connector_state.customer_gid, np_connector_np_tenant_gateways_table_row_callback, NULL, 1);
        if (res) {
            NP_CONNECTOR_LOG(AL_ERROR, "Cannot init table np_tenant_gateways, bad state!");
            goto fail;
        }

        res = np_client_subnets_table_init(np_connector_state.np_wally, np_connector_state.customer_gid, np_connector_np_client_subnets_table_row_callback, NULL, 1);
        if (res) {
            NP_CONNECTOR_LOG(AL_ERROR, "Cannot init table np_client_subnets, bad state!");
            goto fail;
        }
    }

    init_peer_config = 1;
    return;

fail:
/* Should watchdog... */
    while (1) {
        sleep(1);
    }
}

static void np_connector_set_peer_from_wally_cache(int64_t peer_gid,
                                                   const char *public_key,
                                                   const struct argo_inet *listener_ip,
                                                   int listener_port,
                                                   int gateway_state,
                                                   int gateway_redundant_mode)
{
    int res;
    char listener_ip_str[ARGO_INET_ADDRSTRLEN];

    if (!public_key || !listener_ip || (listener_port < 0)) {
        NP_CONNECTOR_DEBUG_CONFIG("Invalid public key %s, listener_ip %s, listener port %s", public_key?"valid":"invalid",
                                                                                                                                           listener_ip?"valid":"invalid",
                                                                                                                                           (listener_port>0)?"valid":"invalid");
        return;
    }

    if (gateway_state != NP_TENANT_GATEWAY_STATE_ONLINE && gateway_state != NP_TENANT_GATEWAY_STATE_UNHEALTHY) {
        NP_CONNECTOR_DEBUG_CONFIG("Skipping offline gateway with gid %"PRId64", state %d", peer_gid, gateway_state);
        return;
    }

    argo_inet_generate(listener_ip_str, listener_ip);

    /* get all client subnets mapped to this gateway*/
    struct np_client_subnets **links = NP_CONNECTOR_CALLOC(sizeof(struct np_client_subnets *) * NPWG_MAX_ALLOWED_IPS_PER_PEER);
    if (!links) {
        NP_CONNECTOR_LOG(AL_ERROR, "Failed to allocate memory for client subnet links.");
        return;
    }
    size_t client_subnets_arr_len = NPWG_MAX_ALLOWED_IPS_PER_PEER;

    res = np_client_subnets_get_by_gateway_gid_immediate(peer_gid,
                                                         links,
                                                         &client_subnets_arr_len);
    if (res == NP_CONNECTOR_RESULT_ASYNCHRONOUS) {
        /* this shouldn't happen as we come here when the data is already there in wally cache,
         * but do stats and confirm, just in case
         */
        stats.read_wally_cache_got_async++;
        NP_CONNECTOR_FREE(links);
        return;
    } else if (res != NP_CONNECTOR_RESULT_NO_ERROR) {
        /* don't see a case for this scenario either
         * do stats and confirm, just in case
         */
        stats.read_wally_cache_got_error++;
        NP_CONNECTOR_FREE(links);
        return;
    }

    struct argo_inet *client_subnets_inet = NP_CONNECTOR_CALLOC(sizeof(struct argo_inet) * client_subnets_arr_len);
    if (!client_subnets_inet) {
        NP_CONNECTOR_LOG(AL_ERROR, "Failed to allocate memory for client subnets inet .");
        NP_CONNECTOR_FREE(links);
        return;
    }
    size_t client_subnets_inet_count = 0;
    for (int i = 0; i < client_subnets_arr_len; i++) {
        if (!links[i]->subnet) {
            NP_CONNECTOR_LOG(AL_NOTICE, "Subnet gid: %"PRId64" has NULL subnet, ignoring", links[i]->gid);
            continue;
        }
        client_subnets_inet[i] = *links[i]->subnet;
        client_subnets_inet_count++;
    }

    /*
     * check connector mode and gateway mode
     * if redundancy feature is not enabled, use default 51820 which is np_tenant_gateway->listener_port
     * if redundancy feature is enabled:
     *   if both mode are in redundant mode, use np_connector_state.gw_port passed in to npwg_set_gateway_peer
     *   if both mode are NOT in redundant mode, use default 51820 which is np_tenant_gateway->listener_port,
     *   if different mode, ignore entry.
     */
    uint16_t gateway_listener_port = (uint16_t)listener_port;
    if (np_connector_state.redundancy_feature_enabled) {
        if (np_connector_state.redundant_mode_enabled && gateway_redundant_mode) {
            gateway_listener_port = np_connector_state.gw_port;
        } else if (!np_connector_state.redundant_mode_enabled && !gateway_redundant_mode) {
            gateway_listener_port = (uint16_t)listener_port;
        } else {
            /* redundancy mode is set differently on connector and gateway, skip setting this gateway as peer */
            stats.set_peer_fail_redundancy_mismatch++;
            NP_CONNECTOR_LOG(AL_ERROR, "Could not store peer config for gateway %"PRId64", redundancy mode mismatch", peer_gid);
            goto done;
        }
    }
    res = npwg_set_gateway_peer(np_connector_state.provider, public_key, listener_ip,
                                gateway_listener_port, client_subnets_inet, client_subnets_inet_count);
    if (res) {
        stats.set_peer_fail++;
        NP_CONNECTOR_LOG(AL_ERROR, "could not store peer config for gateway %"PRId64", err code %s ", peer_gid, np_connector_result_string(res));
        goto done;
    } else {
        np_connector_update_or_create_peer_config_cache(peer_gid, (int)client_subnets_inet_count, public_key, listener_ip, listener_port);
    }
done:
    NP_CONNECTOR_FREE(links);
    NP_CONNECTOR_FREE(client_subnets_inet);
    return;
}

/*
 * Read Peer(tenant gateway) Config from wally cache into NPWG provider cache.
 */
static void np_connector_set_peers_interface_config_walk(npwg_provider provider)
{
    if (!provider) {
        NP_CONNECTOR_LOG(AL_WARNING, "No provider to cache wireguard config");
        return;
    }

    size_t peer_gids_arr_len = NPWG_MAX_PEERS;
    struct np_tenant_gateways *gateways[NPWG_MAX_PEERS];
    size_t iter;
    int res;

    res = np_tenant_gateways_get_by_customer_gid_immediate(np_connector_state.customer_gid,
                                                           &(gateways[0]),
                                                           &peer_gids_arr_len);

    if (res == NP_CONNECTOR_RESULT_ASYNCHRONOUS) {
        return;
    } else if (res != NP_CONNECTOR_RESULT_NO_ERROR) {
        return;
    }

    for (iter = 0; iter < peer_gids_arr_len; iter++) {
        np_connector_set_peer_from_wally_cache(gateways[iter]->gid, gateways[iter]->public_key,
                                               gateways[iter]->listener_ip_address, gateways[iter]->listener_port,
                                               gateways[iter]->gateway_state, gateways[iter]->redundant_mode_enabled);
    }


    /* finished reading config into npwg cache, turn off need_read_config*/
    np_connector_state.need_read_config = 0;
}

/*
 * NP Connector is in enabled state,
 * meaning we have to make sure wireguard is up and running with correct peer config
 */
void np_connector_check_status_in_enabled_state()
{
    if (!np_connector_state.wireguard_running) {
        np_connector_start_wg_interface(np_connector_state.private_key);
    }

    if (np_connector_state.need_read_config) {
        np_connector_set_peers_interface_config_walk(np_connector_state.provider);
    }

    np_connector_update_wireguard();
}

void np_connector_check_status_in_disabled_state()
{
    if (np_connector_state.wireguard_running) {
        np_connector_stop_wireguard(0);

        /* Wireguard was running, and got disabled at runtime.
         * we are cleaning up NPWG provider cache,
         * thus if later we are enabling wireguard back, we need to re-build the peer cache.
         */
        np_connector_state.need_read_config = 1;
    }
}

static void np_connector_check_status(__attribute__((unused)) evutil_socket_t sock,
                                      __attribute__((unused)) short flags,
                                      __attribute__((unused)) void *cookie)
{
    if (np_connector_state.enabled) {
        np_connector_check_status_in_enabled_state();
    } else {
        np_connector_check_status_in_disabled_state();
    }

}

static void np_connector_heartbeat_tickle(__attribute__((unused)) evutil_socket_t sock,
                                          __attribute__((unused)) short flags,
                                          __attribute__((unused)) void *cookie)
{
    if (np_connector_state.tickle_me) zthread_heartbeat(np_connector_state.tickle_me);

}

/*
 * Callback from np feature thread NP_FEATURE_TOGGLE_THREAD
 * Restart if redundancy mode is enabled but feature flag flips during runtime
 */
static void np_connector_redundancy_feature_status_toggled(int64_t customer_gid __attribute__((unused)), int enabled)
{
    np_connector_state.redundancy_feature_enabled = enabled ? 1 : 0;
    np_connector_bgp_set_redundancy_feature(enabled);
    NP_CONNECTOR_LOG(AL_NOTICE, "NP redundancy feature changed to %s", enabled ? "Enabled" : "Disabled");

    if (enabled) {
        /* NP redundancy feature changed from disabled to enabled */
        if (np_connector_state.redundant_mode_enabled) {
            /* if this toggle happens during runtime, let's restart to ensure we initialize everything correctly,
            * otherwise, we might stuck in an inconsistent state */
            NP_CONNECTOR_LOG(AL_NOTICE, "NP redundancy feature is enabled during run time, restarting...");
            sleep(2);
            exit(0);
        }
    } else {
        if (np_connector_state.redundant_mode_enabled) {
            /* NP redundancy feature changed from enabled to disabled */
            //np_connector_bgp_state_destroy();
            NP_CONNECTOR_LOG(AL_NOTICE, "NP redundancy feature is disabled during run time, restarting...");
            sleep(2);
            exit(0);
        }
    }
    /* Reset all gateway peers to update their listener ports */
    //np_connector_state.need_read_config = 1;
}

void *np_connector_thread(struct zthread_info *zthread_arg,
                          __attribute__((unused)) void *cookie)
{
    struct event_base *base = NULL;
    struct event *HB_timer;
    struct event *check_state_timer;
    struct timeval tv;
    struct np_connectors *np_connector = NULL;
    int res;

    np_connector_state.tickle_me = zthread_arg;

    base = event_base_new();
    if (!base) {
        NP_CONNECTOR_LOG(AL_ERROR, "Cannot create event_base : np_connector");
        goto fail;
    }

    np_connector_state.ebase = base;
    np_connector_state.zbase = zevent_attach(base);

    /*heartbeat init*/
    HB_timer = event_new(base,
                         -1,
                         EV_PERSIST,
                         np_connector_heartbeat_tickle,
                         NULL);
    if (!HB_timer) {
        NP_CONNECTOR_LOG(AL_ERROR, "Cannot activate timer for NP Connector - HB_timer");
        goto fail;
    }

    tv.tv_sec = NP_CONNECTOR_HEARTBEAT_TIMER_S;
    tv.tv_usec = 0;

    if (event_add(HB_timer, &tv)) {
        NP_CONNECTOR_LOG(AL_ERROR, "Cannot activate HB timer for HB_timer for np thread");
        goto fail;
    }

    check_state_timer = event_new(base,
                         -1,
                         EV_PERSIST,
                         np_connector_check_status,
                         NULL);
    if (!check_state_timer) {
        NP_CONNECTOR_LOG(AL_ERROR, "Cannot activate check_state_timer timer for NP Connector - HB_timer");
        goto fail;
    }

    /* every 1 min */
    tv.tv_sec = 60;
    tv.tv_usec = 0;

    if (event_add(check_state_timer, &tv)) {
        NP_CONNECTOR_LOG(AL_ERROR, "Cannot activate check_state_timer for check_state_timer for np thread");
        goto fail;
    }

    np_connector_set_frr_version();

    /* np related table init*/
    res = np_connectors_table_init(np_connector_state.np_wally, np_connector_state.customer_gid, np_connector_np_connectors_table_row_callback, NULL, 0);
    if (res) {
        NP_CONNECTOR_LOG(AL_ERROR, "Cannot init table np_connectors");
        goto fail;
    }

    /* Do not proceed further if np config conn is not up, because the following table init calls will block
     * on waiting for wally rows and eventually will crash due to heartbeat timeout */
    int not_ready_count = 0;
    do {
        res = np_connectors_get_by_assistant_gid(np_connector_state.entity_gid, &np_connector, NULL, NULL, 0);
        if (res) {
            if ((not_ready_count++ % 10) == 0) {
                if (res == NP_CONNECTOR_RESULT_NOT_READY) {
                    NP_CONNECTOR_LOG(AL_NOTICE, "NP Connector not yet connected to NP config channel, please check connectivity");
                } else {
                    if (res != NP_CONNECTOR_RESULT_ASYNCHRONOUS) {
                        NP_CONNECTOR_LOG(AL_NOTICE, "Get NP connector returned %s, please check NP connector config", zpn_result_string(res));
                    } else {
                        NP_CONNECTOR_LOG(AL_NOTICE, "Waiting for NP connector to retrieve configuration");
                    }
                }
            }
            sleep(1);
        }
        if (np_connector_state.tickle_me) zthread_heartbeat(np_connector_state.tickle_me);
    } while (res);

    zevent_base_dispatch(base);


fail:
    /* Should watchdog... */
    while (1) {
        sleep(1);
    }
}

int np_connector_pre_init(struct argo_log_collection *event_log)
{
    int res;

    res = np_connector_lib_init(event_log);
    if (res) {
        NP_CONNECTOR_LOG(AL_ERROR, "Cannot init np_connector_lib_init");
        return res;
    }

    res = npwg_provider_log_init(event_log);
    if (res) {
        NP_CONNECTOR_LOG(AL_ERROR, "Cannot init npwg_provider_log_init");
        return res;
    }

    return NP_CONNECTOR_RESULT_NO_ERROR;
}

int np_connector_init(int64_t customer_gid,
                      int64_t entity_gid,
                      struct wally *np_wally,
                      np_connector_state_cb_f *state_update_cb)
{
    int res;

    //if we reach here, means we are enabled
    np_connector_state.enabled = 1;
    np_connector_state.customer_gid = customer_gid;
    np_connector_state.entity_gid = entity_gid;
    np_connector_state.np_wally = np_wally;
    np_connector_state.state_cb = state_update_cb;
    np_connector_state.provider = NULL;
    np_connector_stats_description = argo_register_global_structure(NP_CONNECTOR_STATS_HELPER);
    np_set_redundancy_feature_config_monitor(np_connector_redundancy_feature_status_toggled);
    np_connector_state.redundancy_feature_enabled = np_is_redundancy_feature_enabled(customer_gid);
    np_connector_bgp_set_redundancy_feature(np_connector_state.redundancy_feature_enabled);

    if (!np_connector_stats_description) {
        NP_CONNECTOR_LOG(AL_ERROR, "Cannot argo_register_global_structure np_connector_stats_description");
        return NP_CONNECTOR_RESULT_ERR;
    }

    np_connector_state.peer_config_by_gateway_gid = zhash_table_alloc(&np_connector_allocator);
    if (!np_connector_state.peer_config_by_gateway_gid) {
        NP_CONNECTOR_LOG(AL_ERROR, "cannot allocate hash table peer_config_by_gateway_gid");
        return NP_CONNECTOR_RESULT_NO_MEMORY;
    }

    res = np_connector_config_ovd_desc_register_all();
    if (res) {
        NP_CONNECTOR_LOG(AL_ERROR, "Could not init np_connector_config_ovd_desc_register_all");
        return res;
    }

    ZTAILQ_INIT(&(np_connector_state.peers_queue));

    res = zthread_create(&(np_connector_state.thread),
                         np_connector_thread,
                         NULL,
                         "np_connector",
                         MAX_NP_CONNECTOR_HEARTBEAT_TIMEOUT_S,
                         NP_CONNECTOR_THREAD_STACK_SIZE_BYTE,
                         NP_CONNECTOR_THREAD_USER_INT,
                         NULL);
    if (res) {
        NP_CONNECTOR_LOG(AL_ERROR, "Cannot create NP connector thread");
        return res;
    }

    res = np_connector_debug_init();
    if (res) {
        NP_CONNECTOR_LOG(AL_ERROR, "Could not init np_connector_debug_init");
        return res;
    }

    np_connector_state.init_us = epoch_us();
    np_connector_state.initialized = 1;

    return res;
}

void np_connector_enroll_state_init(struct zcrypt_rsa_key *rsa_key,
                                    char *api_hostname,
                                    enum zpn_enrollment_type enroll_type,
                                    enum zpn_enrollment_style enroll_style,
                                    char *cloud,
                                    char *provisioning_key,
                                    char *hw_fingerprint,
                                    struct zcrypt_key* cfg_hw_key,
                                    const char *oauth_token)
{
    np_connector_state.enroll_state.rsa_key = rsa_key;
    np_connector_state.enroll_state.enroll_type = enroll_type;
    np_connector_state.enroll_state.enroll_style = enroll_style;
    np_connector_state.cfg_hw_key = cfg_hw_key;
    snprintf(np_connector_state.enroll_state.api_hostname, sizeof(np_connector_state.enroll_state.api_hostname), "%s", api_hostname);
    snprintf(np_connector_state.enroll_state.cloud, sizeof(np_connector_state.enroll_state.cloud), "%s", cloud);
    snprintf(np_connector_state.enroll_state.hw_fingerprint, sizeof(np_connector_state.enroll_state.hw_fingerprint), "%s", hw_fingerprint);
    snprintf(np_connector_state.enroll_state.provisioning_key, sizeof(np_connector_state.enroll_state.provisioning_key), "%s", provisioning_key);
    snprintf(np_connector_state.enroll_state.oauth_token, sizeof(np_connector_state.enroll_state.oauth_token), "%s", oauth_token);

    return;

}

static void np_connector_disable_on_np_thread(__attribute__((unused))struct zevent_base *base,
                                              __attribute__((unused))void *void_cookie,
                                              __attribute__((unused))int64_t int_cookie)
{
    np_connector_state.enabled = 0;
    NP_CONNECTOR_LOG(AL_NOTICE, "NP Connector get disabled");
}

static void np_connector_enable_on_np_thread(__attribute__((unused))struct zevent_base *base,
                                             __attribute__((unused))void *void_cookie,
                                             __attribute__((unused))int64_t int_cookie)
{
    np_connector_state.enabled = 1;
    NP_CONNECTOR_LOG(AL_NOTICE, "NP Connector get enabled");
}

/* could be called from other thread */
int np_connector_disable()
{
    if (0 != zevent_base_call(np_connector_state.zbase, np_connector_disable_on_np_thread, NULL, 0)) {
        NP_CONNECTOR_LOG(AL_NOTICE, "Failed to disable NP Connector on NP Connector thread");
        return NP_CONNECTOR_RESULT_ERR;
    }

    return NP_CONNECTOR_RESULT_NO_ERROR;
}

/* could be called from other thread */
int np_connector_enable()
{
    if (0 != zevent_base_call(np_connector_state.zbase, np_connector_enable_on_np_thread, NULL, 0)) {
        NP_CONNECTOR_LOG(AL_NOTICE, "Failed to enable NP Connector on NP Connector thread");
        return NP_CONNECTOR_RESULT_ERR;
    }

    return NP_CONNECTOR_RESULT_NO_ERROR;
}

int np_connector_get_state()
{
    return np_connector_state.is_online;
}

int np_connector_get_enablement()
{
    return np_connector_state.enabled;
}

int np_connector_is_initialized()
{
    return np_connector_state.initialized;
}

int64_t np_connector_get_connector_gid()
{
    return np_connector_state.np_connector_gid;
}

struct wally *np_connector_get_np_wally()
{
    return np_connector_state.np_wally;
}

int64_t np_connector_get_connector_group_gid()
{
    return np_connector_state.np_connector_group_gid;
}

int64_t np_connector_get_customer_gid()
{
    return np_connector_state.customer_gid;
}


void np_connector_monitor_log()
{
    NP_CONNECTOR_LOG(AL_NOTICE, "- - - - - NP Connector Status:ID=%ld, State:%s, enabled:%s, Wireguard:%s, ConfiguredPeers: %zu, MTU: %d, FRR Version: %s - - - - -",
                     (long) np_connector_state.np_connector_gid, np_connector_state.is_online?"Online":"Offline", np_connector_state.enabled?"Enabled":"Disabled",
                     np_connector_state.wireguard_running?"Active":"Inactive", np_connector_state.peers_in_config, np_connector_state.mtu, np_connector_state.frr_version);

    if (!np_connector_state.zscaler_frr) {
        NP_CONNECTOR_LOG(AL_WARNING, "Detected FRR installed by a source other than Zscaler. Please remove the current installation and restart NP Connector to use the Zscaler-provided FRR.");
    }
}


void np_connector_terminate_wireguard_before_exit()
{
    if (np_connector_state.wireguard_running) {

        //best effort terminate
        np_connector_stop_wireguard(1);
    }
}

/*
 * Parse RPM metadata line the format 'Key: Value' and extract the Value part.
 */
static int np_connector_parse_rpm_metadata_value(const char *buf, char *output_buf, size_t output_buf_size)
{
    const char *colon_pos = strchr(buf, ':');
    if (colon_pos == NULL) {
        return 0;
    }

    const char *value_start = colon_pos + 1;
    while (*value_start == ' ') {
        value_start++;
    }

    snprintf(output_buf, output_buf_size, "%s", value_start);

    output_buf[strcspn(output_buf, "\n")] = '\0';
    output_buf[strcspn(output_buf, " ")] = '\0';

    return 1;
}

#define COMMAND_RPM_QI_FRR "rpm -qi frr"
#define MAX_BUFFER_SIZE 128
#define COMMAND_OUTPUT_BUFFER_SIZE 1024
static void np_connector_set_frr_version() {
    FILE *fp;
    char buffer[COMMAND_OUTPUT_BUFFER_SIZE] = {0};
    char packager_buffer[MAX_BUFFER_SIZE] = {0};
    char summary_buffer[MAX_BUFFER_SIZE] = {0};
    char release_buffer[MAX_BUFFER_SIZE] = {0};
    char base_version[MAX_BUFFER_SIZE] = {0};
    int packager_found = 0, summary_found = 0, release_found = 0;
    int res;

    const char *command = COMMAND_RPM_QI_FRR;
    fp = popen(command, "r");

    if (fp == NULL) {
        NP_CONNECTOR_LOG(AL_ERROR, "Failed to run command '%s'", COMMAND_RPM_QI_FRR);
        return;
    }

    while (fgets(buffer, sizeof(buffer), fp) != NULL) {
        if (strncmp(buffer, "Packager", 8) == 0) {
            packager_found = np_connector_parse_rpm_metadata_value(buffer, packager_buffer, sizeof(packager_buffer));
        } else if (strncmp(buffer, "Summary", 7) == 0) {
            summary_found = np_connector_parse_rpm_metadata_value(buffer, summary_buffer, sizeof(summary_buffer));
        } else if (strncmp(buffer, "Release", 7) == 0) {
            release_found = np_connector_parse_rpm_metadata_value(buffer, release_buffer, sizeof(release_buffer));
        }
    }

    res = pclose(fp);
    if (res != 0) {
        NP_CONNECTOR_LOG(AL_ERROR, "%s failed or package 'frr' not installed!", COMMAND_RPM_QI_FRR);
        return;
    }

    if (!packager_found || !summary_found || !release_found) {
        NP_CONNECTOR_LOG(AL_ERROR, "packager_found=%d, summary_found=%d, release_found=%d, Last parsed line: %s",
                         packager_found, summary_found, release_found, buffer);
        return;
    }

    /* Packager    : Zscaler, Inc. */
    if (strncmp(packager_buffer, "Zscaler", 7) == 0) {
        np_connector_state.zscaler_frr = 1;
    } else {
        np_connector_state.zscaler_frr = 0;
        NP_CONNECTOR_LOG(AL_WARNING, "Detected FRR Package installed on the system but not by Zscaler!");
    }

    /* Release     : 1.el9 */
    char *dot_pos = strchr(release_buffer, '.');
    if (dot_pos != NULL) {
        *dot_pos = '\0';
        NP_CONNECTOR_LOG(AL_INFO, "Extract revision from release_buffer: '%s'", release_buffer);
    }

    /* Summary     : FRR-10.2 */
    if (sscanf(summary_buffer, "FRR-%s", base_version) != 1) {
        NP_CONNECTOR_LOG(AL_ERROR, "Unable to parse FRR base version from Summary!");
        return;
    }

    int written = snprintf(np_connector_state.frr_version, sizeof(np_connector_state.frr_version), "%s-%s",
                           base_version, release_buffer);
    if (written < 0 || written >= sizeof(np_connector_state.frr_version)) {
        NP_CONNECTOR_LOG(AL_ERROR, "FRR version string truncated: base_version='%s', release_buffer='%s'",
                         base_version, release_buffer);
        return;
    }

    NP_CONNECTOR_LOG(AL_NOTICE, "Detected FRR installed with version %s by Zscaler: %s",
                     np_connector_state.frr_version,
                     np_connector_state.zscaler_frr ? "TRUE" : "FALSE");
}

const char* np_connector_get_frr_version()
{
    return np_connector_state.frr_version;
}
