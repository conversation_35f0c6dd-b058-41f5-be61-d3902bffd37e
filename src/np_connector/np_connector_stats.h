/*
* np_connector_stats.h. Copyright (C) 2025 Zscaler, Inc. All Rights Reserved.
*
*/

#ifndef _NP_CONNECTOR_STATS_H_
#define _NP_CONNECTOR_STATS_H_

#include "zpath_lib/zpath_debug.h"

#define BYTES_TO_BITS_COVERSION 8

struct np_connector_wg_throughput_report {
    int64_t total_throughput_bytes_per_sec_aggregated;
    int64_t throughput_report_data_count;
};

int64_t np_connector_get_throughput_bits_per_sec_for_comp_stats();
int np_connector_wireguard_stats_fill(void* cookie, int counter, void* structure_data);
int np_connector_wg_dump(struct zpath_debug_state *request_state,
                         const char **query_values,
                         int query_value_count, void *cookie);

#endif /* __NP_CONNECTOR_STATS_H__ */
