/*
 * np_connector_lib.c. Copyright (C) 2024 Zscaler Inc. All Rights Reserved.
 */
#include "zpath_lib/zpath_debug.h"
#include "zpath_misc/zpath_misc.h"
#include "np_connector/np_connector_lib.h"

struct argo_log_collection *np_connector_event_collection = NULL;
struct zpath_allocator np_connector_allocator = ZPATH_ALLOCATOR_INIT("np_connector");

uint64_t np_connector_debug_log = 0;

static uint64_t np_connector_debug_log_catch_defaults =
         (NP_CONNECTOR_DEBUG_CFG_BIT) |
         (NP_CONNECTOR_DEBUG_KEY_BIT) |
         (NP_CONNECTOR_DEBUG_TABLE_BIT) |
         (NP_CONNECTOR_DEBUG_CONFIG_BIT) |
         (NP_CONNECTOR_DEBUG_BGP_BIT) |
         0;


const char *np_connector_result_strings[] = {
           [NP_CONNECTOR_RESULT_NO_ERROR] = "NP_CONNECTOR_RESULT_NO_ERROR",
           [NP_CONNECTOR_RESULT_ERR] = "NP_CONNECTOR_RESULT_ERR",
           [NP_CONNECTOR_RESULT_NOT_FOUND] = "NP_CONNECTOR_RESULT_NOT_FOUND",
           [NP_CONNECTOR_RESULT_NO_MEMORY] = "NP_CONNECTOR_RESULT_NO_MEMORY",
           [NP_CONNECTOR_RESULT_CANT_WRITE] = "NP_CONNECTOR_RESULT_CANT_WRITE",
           [NP_CONNECTOR_RESULT_ERR_TOO_LARGE] = "NP_CONNECTOR_RESULT_ERR_TOO_LARGE",
           [NP_CONNECTOR_RESULT_BAD_ARGUMENT] = "NP_CONNECTOR_RESULT_BAD_ARGUMENT",
           [NP_CONNECTOR_RESULT_INSUFFICIENT_DATA] = "NP_CONNECTOR_RESULT_INSUFFICIENT_DATA",
           [NP_CONNECTOR_RESULT_NOT_IMPLEMENTED] = "NP_CONNECTOR_RESULT_NOT_IMPLEMENTED",
           [NP_CONNECTOR_RESULT_BAD_DATA] = "NP_CONNECTOR_RESULT_BAD_DATA",
           [NP_CONNECTOR_RESULT_WOULD_BLOCK] = "NP_CONNECTOR_RESULT_WOULD_BLOCK",
           [NP_CONNECTOR_RESULT_BAD_STATE] = "NP_CONNECTOR_RESULT_BAD_STATE",
           [NP_CONNECTOR_RESULT_INCOMPLETE] = "NP_CONNECTOR_RESULT_INCOMPLETE",
           [NP_CONNECTOR_RESULT_ASYNCHRONOUS] = "NP_CONNECTOR_RESULT_ASYNCHRONOUS",
           [NP_CONNECTOR_RESULT_EXCESS_DYN_FIELDS] = "NP_CONNECTOR_RESULT_EXCESS_DYN_FIELDS",
           [NP_CONNECTOR_RESULT_NOT_READY] = "NP_CONNECTOR_RESULT_NOT_READY",
};

const char *np_connector_debug_log_names[] = NP_CONNECTOR_DEBUG_LOG_NAMES;

int np_connector_lib_init(struct argo_log_collection *event_log)
{
    np_connector_event_collection = event_log;

    zpath_debug_add_allocator(&np_connector_allocator, "np_connector");

    int res = zpath_debug_add_flag(&np_connector_debug_log, np_connector_debug_log_catch_defaults, "np_connector",
                                  np_connector_debug_log_names);
    return res;
}

const char *np_connector_result_string(int result)
{
    if (result >= (sizeof(np_connector_result_strings) / sizeof(const char *)) ||
        result < 0 ||
        np_connector_result_strings[result] == NULL) {
        return "INVALID_RESULT";
    }
    return np_connector_result_strings[result];
}
