/*
 * np_connector_bgp.h. Copyright (C) 2025 Zscaler Inc, All Rights Reserved
 */

#ifndef _NP_CONNECTOR_BGP_H_
#define _NP_CONNECTOR_BGP_H_

void np_connector_bgp_load_config();

void np_connector_bgp_state_init(struct wally *np_wally, int64_t customer_gid);
void np_connector_bgp_state_destroy();

void np_connector_bgp_set_np_connector_gid(int64_t np_connector_gid);
void np_connector_bgp_set_np_connector_group_gid(int64_t np_connector_group_gid);

int np_connector_get_bgp_config_mode();

int np_bgp_connector_reload_frr_conf(int force_regenerate);

int np_connector_get_bgp_peer_info_from_neighbor_ip(const char *neighbor_ip, int64_t neighbor_asn,
                                                         int64_t *peer_gid, int *peer_type);

void np_connector_bgp_update_local_sourcing_config(int advertise_lan_segments_disabled);

int np_connector_bgp_client_subnet_handler(struct argo_object *previous_row, struct argo_object *row);

void np_connector_bgp_set_redundancy_feature(int enabled);

void np_connector_bgp_set_redundant_mode(int enabled);

int np_connector_bgp_init();

#endif /* _NP_CONNECTOR_BGP_H_ */
