/*
 * pattern_match.h. Copyright(C) 2024 Zscaler Inc. All Rights Reserved
 */

/*
 *  This is used for application pattern matching integrated with diamond.
 *
 *  We can have a compiled list of patterns which can be matched against each app request.
 *  The undelying implementation can make use of either:
 *  1) PCRE - Underlying implementation uses pcre regex engine
 *  2) POSIX - Underlying implementation uses POSIX regex matching
 *  3) BASIC - Basic implementation using DP based approach used for initial testing
 *
 *  De<PERSON><PERSON> uses PCRE
 *
 *  Some of the pattern meaning is changed from regular regex matching for usecase.
 *  Meaning of each pattern will be listed in below table.
 *   ______________________________________________________
 *  |  Char   |         Meaning                           |
 *  |_________|___________________________________________|
 *  |   *     |  Wildcard, 0 or more of any characters    |
 *  |_________|___________________________________________|
 *  |   ?     |  Any 1 character                          |
 *  |_________|___________________________________________|
 *  |   ...   |  Remaining regex as same for now!         |
 *  |_________|___________________________________________|

 *
 */

#ifndef __PATTERN_MATCH_H_
#define __PATTERN_MATCH_H_

#include "zpath_misc/zpath_misc.h"

#if PATTERN_MATCH_DEBUG
#define PATTERN_MATCH_DEBUGF(fmt, args...)     \
        fprintf(stdout, "Pattern Match: [%s], [%d]: " fmt "\n", __func__, __LINE__, ##args)
#else
#define PATTERN_MATCH_DEBUGF(fmt, args...)
#endif

#define PATTERN_MATCH_ERROR(fmt, args...)     \
        fprintf(stderr, "Pattern Match: [%s], [%d]: " fmt "\n", __func__, __LINE__, ##args)
#define BUF_SIZE                        1024
#define APPLICATION_DOMAIN_MAX_LEN      512
#define DEFAULT_NUM_PATTERNS_PER_SUFFIX 100

#ifdef PATTERN_MATCH_TESTING
#define MAX_NUM_PATTERNS_PER_SUFFIX     15
#define PATTERN_MAX_LEN                 15
#else
#define PATTERN_MAX_LEN                 256
#define MAX_NUM_PATTERNS_PER_SUFFIX     50000
#endif

/*
 * Allocator used.
 */
extern struct zpath_allocator pattern_match_allocator;
#define PATTERN_MATCH_MALLOC(x) zpath_malloc(&pattern_match_allocator, x, __LINE__, __FILE__)
#define PATTERN_MATCH_FREE(x) zpath_free(x, __LINE__, __FILE__)
#define PATTERN_MATCH_CALLOC(x) zpath_calloc(&pattern_match_allocator, x, __LINE__, __FILE__)
#define PATTERN_MATCH_STRDUP(x, y) zpath_strdup(&pattern_match_allocator, x, y, __LINE__, __FILE__)

// Keeping the value same as argo.h with an offset of 20
enum pattern_match_error {
    PATTERN_MATCH_RESULT_NO_ERROR = 0,
    PATTERN_MATCH_RESULT_ERR_COMPILE = 21, // greater than largest diamond result in diamond.h to interpret error reason separate for each.
    PATTERN_MATCH_RESULT_NOT_FOUND,
    PATTERN_MATCH_RESULT_NO_MEMORY,
    PATTERN_MATCH_RESULT_ERR_TOO_LARGE = 25,
    PATTERN_MATCH_RESULT_BAD_ARGUMENT,
};

enum pattern_match_type {
    BASIC = 0,
    POSIX,
    PCRE,
};

struct pattern_list {
    LIST_ENTRY(pattern_list)    pattern_list_entry;
    const char                 *pattern;        // Input pattern
    union {
        void                   *regex;          // POSIX compiled pattern
        void                   *re;             // PCRE compiled pattern
        char                   *mod_pattern;    // DP compiled pattern
    };
};

LIST_HEAD(pattern_list_head, pattern_list);

struct pattern_match_ctx {
    size_t                      num_patterns;
    /* size_t                      max_pattern_len; */
    struct pattern_list_head    patterns;
};

/*
 * Function pointer invoked from the
 * pattern walk function. Keep signature in sync with diamond_walk_f
 */
typedef int (pattern_walk_f) (void *cookie,
                              int is_wildcard_prefix,
                              const char* phrase,
                              int phrase_len,
                              int is_pattern);


const char *pattern_match_result_string(enum pattern_match_error result);
struct pattern_match_ctx* pattern_match_create();
void pattern_match_free(struct pattern_match_ctx *ctx);
void set_pattern_match_type(enum pattern_match_type type);
enum pattern_match_error pattern_match(const struct pattern_match_ctx *ctx, const char *app, char **result, int result_size, int *num_pattern_matched);
enum pattern_match_error pattern_add(struct pattern_match_ctx *ctx, const char *pattern);
enum pattern_match_error pattern_remove(struct pattern_match_ctx *ctx, const char *pattern);
const char* pattern_lookup(const struct pattern_match_ctx *ctx, const char *pattern);
int pattern_walk(struct pattern_match_ctx *ctx, pattern_walk_f walk_f, void *cookie);

#ifdef PATTERN_MATCH_DEBUG
/*
 * Print input patterns
 */
static inline void
print_patterns(const char **patterns) {
    size_t pattern_index = 0;

    if (patterns == NULL) {
        return;
    }

    while (patterns[pattern_index] != NULL) {
        PATTERN_MATCH_DEBUGF("Index: %ld, pattern : %s",
               pattern_index, patterns[pattern_index]);
        pattern_index++;
    }
}

/*
 * Print compiled pattern_match_ctx
 */
static inline void
print_pattern_match_ctx( struct pattern_match_ctx *ctx) {
    struct pattern_list *patterns = NULL;
    struct pattern_list *temp = NULL;

    if (ctx == NULL) {
        return;
    }

    PATTERN_MATCH_DEBUGF("num pattern : [%ld]", ctx->num_patterns);
    LIST_FOREACH_SAFE(patterns, &(ctx->patterns), pattern_list_entry, temp) {
        PATTERN_MATCH_DEBUGF("pattern : [%s]", patterns->pattern);
    }
}
#endif

/*
 * Free input patterns
 */
static inline void
free_patterns(char **patterns) {
    size_t pattern_index = 0;

    if (patterns == NULL) {
        return;
    }
    while (patterns[pattern_index] !=  NULL) {
        PATTERN_MATCH_FREE(patterns[pattern_index]);
        pattern_index++;
    }
    PATTERN_MATCH_FREE(patterns);
}
#endif /* #ifndef __PATTERN_MATCH_H_ */
