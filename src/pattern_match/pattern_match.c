/*
 * pattern_match.c. Copyright(C) 2024 Zscaler Inc. All Rights Reserved
 */

#include <pcre.h>
#include <regex.h>
#include <stdbool.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <sys/queue.h>

#include "pattern_match/pattern_match.h"
#include "zpath_misc/zpath_version.h"

enum pattern_match_type pattern_match_type = PCRE;

struct zpath_allocator pattern_match_allocator = ZPATH_ALLOCATOR_INIT("pattern_match");

static const char *pattern_match_result_strings[] = {
    [PATTERN_MATCH_RESULT_NO_ERROR] = "PATTERN_MATCH_RESULT_NO_ERROR",
    [PATTERN_MATCH_RESULT_ERR_COMPILE] = "PATTERN_MATCH_RESULT_ERR_COMPILE",
    [PATTERN_MATCH_RESULT_NOT_FOUND] = "PATTERN_MATCH_RESULT_NOT_FOUND",
    [PATTERN_MATCH_RESULT_NO_MEMORY] = "PATTERN_MATCH_RESULT_NO_MEMORY",
    [PATTERN_MATCH_RESULT_BAD_ARGUMENT] = "PATTERN_MATCH_RESULT_BAD_ARGUMENT",
};

const char *
pattern_match_result_string(enum pattern_match_error result)
{
    if (result >= (sizeof(pattern_match_result_strings) / sizeof (const char *))) return "INVALID_RESULT";
    if (result < 0) return "INVALID_RESULT";
    if (pattern_match_result_strings[result] == NULL) return "INVALID_RESULT";
    return pattern_match_result_strings[result];
}
/* --------------------------------------------------------------------------*/
/**
 * @Synopsis  Set the pattern matching algorithm type. Currently, takes
 *           1) BASIC - DP based approach used for initial testing
 *           2) POSIX - Use POSIX regex matching
 *           3) PCRE - Use PCRE regex matching
 *           DEFAULT: PCRE
 * @Param [in] type
 */
/* ----------------------------------------------------------------------------*/
void
set_pattern_match_type(enum pattern_match_type type) {
    pattern_match_type = type;
}


/* --------------------------------------------------------------------------*/
/**
 * @Synopsis         This is used to identify if the char has different
 *                   meaning in the regex matching. If we want to use them as a normal
 *                   character and not their regex based behavior it should be
 *                   added here.
 *
 *                   Any character, that should not be interpreted with special meaning should be
 *                   added here.
 *
 * @Param [in] ch    input character
 *
 * @Returns          true if special character else false
 */
/* ----------------------------------------------------------------------------*/
static inline bool
is_special_characters(char ch) {
    return ((ch == '.'));  // || (ch == '?')
}


/* --------------------------------------------------------------------------*/
/**
 * @Synopsis         This is used to indicate if we are using regex library
 *
 * @Param [in] type  pattern match type
 *
 * @Returns          true if external regex else false
 */
/* ----------------------------------------------------------------------------*/
static inline bool
is_external_regex(enum pattern_match_type type) {
    return ((type == POSIX) || (type == PCRE));
}

/* --------------------------------------------------------------------------*/
/**
 * @Synopsis                 Create new pattern after:
 *                           1) removing consecutive wildcard characters
 *                           2) Changing the regex pattern behavior for special
 *                           characters
 *                           3) Creating new pattern
 *
 * @Param [in]  pattern      Input pattern provided
 * @Param [out] new_pattern  Modified pattern for use in pattern matching
 */
/* ----------------------------------------------------------------------------*/
void
pattern_match_create_pattern(const char *pattern, char *new_pattern, int new_pattern_len) {
    size_t     i;
    size_t     index = 0;
    size_t     pattern_len = strnlen(pattern, PATTERN_MAX_LEN);
    bool       first_wildcard = true;
    bool       external_regex = is_external_regex(pattern_match_type);

    // Add starting marker ^
    if ((index < new_pattern_len) && external_regex) {
        new_pattern[index++] = '^';
    }

    for (i = 0; (i < pattern_len) && (index < new_pattern_len); i++) {
        /* if first '*' copy it , consecutive '*' are removed */
        if (pattern[i] == '*') {
            if (first_wildcard) {
                if (external_regex) {
                    new_pattern[index++] = '.';
                }
                new_pattern[index++] = pattern[i];
                first_wildcard = false;
            }
        } else if (pattern[i] == '?') {
            if (external_regex) {
                new_pattern[index++] = '.';
            }
        } else { /* if any other character copy and reset first_wildcard */
            if (external_regex && is_special_characters(pattern[i])) {
                new_pattern[index++] = '\\';
            }
            new_pattern[index++] = pattern[i];
            first_wildcard = true;
        }
    }

    // Add end marker $
    if ((index < new_pattern_len) && external_regex) {
        new_pattern[index++] = '$';
    }

    if (index < new_pattern_len) new_pattern[index] = '\0';
}

/* --------------------------------------------------------------------------*/
/**
 * @Synopsis Add a pattern string to pattern_match_ctx
 *
 * @Param [in] ctx       Created pattern_match_ctx
 * @Param [in] pattern   Input pattern string
 *
 * @Returns  PATTERN_MATCH_RESULT_BAD_ARGUMENT, PATTERN_MATCH_RESULT_NO_MEMORY, PATTERN_MATCH_RESULT_NO_ERROR, PATTERN_MATCH_RESULT_ERR_COMPILE
 */
/* ----------------------------------------------------------------------------*/
enum pattern_match_error
pattern_add(struct pattern_match_ctx *ctx, const char *pattern) {
    size_t                           pattern_len;
    struct pattern_list             *cur_pattern = NULL;
    enum pattern_match_error         ret = PATTERN_MATCH_RESULT_NO_ERROR;
    char                            *mod_pattern = NULL;

    if (!pattern || !ctx) {
        ret = PATTERN_MATCH_RESULT_BAD_ARGUMENT;
        goto out;
    }

    pattern_len = strlen(pattern);
    if (pattern_len > PATTERN_MAX_LEN) {
        ret = PATTERN_MATCH_RESULT_ERR_TOO_LARGE;
        PATTERN_MATCH_ERROR("pattern len: %zu exceeded max length for pattern: %s", pattern_len, pattern);
        goto out;
    }

    if (ctx->num_patterns >= MAX_NUM_PATTERNS_PER_SUFFIX) {
        ret = PATTERN_MATCH_RESULT_ERR_TOO_LARGE;
        PATTERN_MATCH_ERROR("max patterns supported: %d exceeded for pattern: %s", MAX_NUM_PATTERNS_PER_SUFFIX, pattern);
        goto out;
    }

    cur_pattern = (struct pattern_list *)PATTERN_MATCH_CALLOC(sizeof(struct pattern_list));
    if (cur_pattern == NULL) {
        ret = PATTERN_MATCH_RESULT_NO_MEMORY;
        PATTERN_MATCH_ERROR("PATTERN_MATCH_MALLOC failed for pattern_list, for pattern: [%s] at index: [%ld]",
                            pattern, ctx->num_patterns);
        goto out;
    }

    cur_pattern->pattern = pattern;

    // Support specical characters
    if (is_external_regex(pattern_match_type)) {
        pattern_len += 2;  // Regex needs ^ and $
        pattern_len *= 2;
    }

    mod_pattern = (char *)PATTERN_MATCH_MALLOC(pattern_len + 1);
    if (mod_pattern == NULL) {
        ret = PATTERN_MATCH_RESULT_NO_MEMORY;
        PATTERN_MATCH_ERROR("PATTERN_MATCH_MALLOC failed for mod_pattern: [%s] at index: [%ld]",
                            pattern, ctx->num_patterns);
        goto out;
    }

    /* Create a new pattern removing consecutive '*' */
    pattern_match_create_pattern(pattern, mod_pattern, pattern_len);
    if (pattern_match_type == POSIX) {
        cur_pattern->regex = (regex_t *)(PATTERN_MATCH_MALLOC(sizeof(regex_t) * (pattern_len+1)));
        if (cur_pattern->regex == NULL) {
            ret = PATTERN_MATCH_RESULT_NO_MEMORY;
            PATTERN_MATCH_ERROR("PATTERN_MATCH_MALLOC failed for regex pattern: [%s] at index: [%ld]",
                                pattern, ctx->num_patterns);
            goto out;
        }

        ret = regcomp(cur_pattern->regex, mod_pattern, (REG_EXTENDED | REG_NOSUB));
        if (ret) {
            PATTERN_MATCH_ERROR("Could not compile regex for pattern: [%s]", cur_pattern->pattern);
            ret = PATTERN_MATCH_RESULT_ERR_COMPILE;
            goto out;
        }
    } else if (pattern_match_type == PCRE) {
        int erroffset;
        const char *error;

        cur_pattern->re = pcre_compile(mod_pattern, 0, &error, &erroffset, NULL);
        if (cur_pattern->re == NULL) {
            PATTERN_MATCH_ERROR("pcre_compile failed (offset: %d), %s\n", erroffset, error);
            ret = PATTERN_MATCH_RESULT_ERR_COMPILE;
            goto out;
        }
    } else {
        cur_pattern->mod_pattern = PATTERN_MATCH_STRDUP(mod_pattern, pattern_len);
        if (cur_pattern->mod_pattern == NULL) {
            ret = PATTERN_MATCH_RESULT_NO_MEMORY;
            PATTERN_MATCH_ERROR("PATTERN_MATCH_MALLOC failed for BASIC mod pattern: [%s] at index: [%ld]",
                                pattern, ctx->num_patterns);
            goto out;
        }

    }

    LIST_INSERT_HEAD(&(ctx->patterns), cur_pattern, pattern_list_entry);
    ctx->num_patterns++;

out:
    if (ret != PATTERN_MATCH_RESULT_NO_ERROR) {
        if (cur_pattern) {
            PATTERN_MATCH_FREE(cur_pattern->re);
            PATTERN_MATCH_FREE(cur_pattern);
        }
    }
    if (mod_pattern) PATTERN_MATCH_FREE(mod_pattern);
    return ret;
}

static inline void
pattern_list_free(struct pattern_list *patterns) {
    LIST_REMOVE(patterns, pattern_list_entry);
    (pattern_match_type == BASIC)   ? PATTERN_MATCH_FREE(patterns->mod_pattern)
    : (pattern_match_type == POSIX) ? PATTERN_MATCH_FREE(patterns->regex)
                                    : pcre_free(patterns->re);
    PATTERN_MATCH_FREE(patterns);

}
/* --------------------------------------------------------------------------*/
/**
 * @Synopsis             Remove a pattern from the list of patterns
 *
 * @Param [in] ctx       Created pattern_match_ctx
 * @Param [in] pattern   Pattern to remove
 *
 * @Returns              PATTERN_MATCH_RESULT_BAD_ARGUMENT, PATTERN_MATCH_RESULT_NO_ERROR
 */
/* ----------------------------------------------------------------------------*/
enum pattern_match_error
pattern_remove(struct pattern_match_ctx *ctx, const char *pattern) {
    struct pattern_list *patterns = NULL;
    struct pattern_list *temp = NULL;
    int                  pattern_found = 0;

    if ((ctx == NULL) || (pattern == NULL)) {
        return PATTERN_MATCH_RESULT_BAD_ARGUMENT;
    }

    LIST_FOREACH_SAFE(patterns, &(ctx->patterns), pattern_list_entry, temp) {
        if (patterns->pattern && !strncmp(patterns->pattern, pattern, PATTERN_MAX_LEN)) {
            pattern_list_free(patterns);
            ctx->num_patterns--;
            pattern_found = 1;
        }
    }

    return pattern_found ? PATTERN_MATCH_RESULT_NO_ERROR : PATTERN_MATCH_RESULT_NOT_FOUND;
}

/* --------------------------------------------------------------------------*/
/**
 * @Synopsis             Lookup for a pattern in list of patterns
 *
 * @Param [in] ctx       Created pattern_match_ctx
 * @Param [in] pattern   Pattern to lookup
 *
 * @Returns              Pattern or NULL
 */
/* ----------------------------------------------------------------------------*/
const char*
pattern_lookup(const struct pattern_match_ctx *ctx, const char *pattern) {
    struct pattern_list *patterns = NULL;
    struct pattern_list *temp = NULL;

    if ((ctx == NULL) || (pattern == NULL)) {
        return NULL;
    }

    LIST_FOREACH_SAFE(patterns, &(ctx->patterns), pattern_list_entry, temp) {
        if (patterns->pattern && !strncmp(patterns->pattern, pattern, PATTERN_MAX_LEN)) {
            return patterns->pattern;
        }
    }

    return NULL;
}

int pattern_walk(struct pattern_match_ctx *ctx,
                 pattern_walk_f walk_f,
                 void* cookie)
{
    int res = 0;
    struct pattern_list *pattern = NULL;
    struct pattern_list *temp = NULL;

    if (ctx == NULL) {
        return PATTERN_MATCH_RESULT_NO_ERROR;
    }

    if (walk_f == NULL) {
        return PATTERN_MATCH_RESULT_BAD_ARGUMENT;
    }

    LIST_FOREACH_SAFE(pattern, &(ctx->patterns), pattern_list_entry, temp) {
        if (pattern->pattern) {
            res = (walk_f) (cookie, 0, pattern->pattern, (int)strnlen(pattern->pattern, PATTERN_MAX_LEN), 1);
        }
    }

    if (res) {
        PATTERN_MATCH_ERROR("pattern walk failed error: %d\n", res);
        return res;
    } else {
        return PATTERN_MATCH_RESULT_NO_ERROR;
    }

}

/* --------------------------------------------------------------------------*/
/**
 * @Synopsis  Create pattern_match_ctx to work on.
 *
 * @Returns   Created pattern_match_ctx.
 */
/* ----------------------------------------------------------------------------*/
struct pattern_match_ctx*
pattern_match_create() {
    struct pattern_match_ctx *ctx = NULL;

    ctx = (struct pattern_match_ctx *)PATTERN_MATCH_CALLOC(sizeof(struct pattern_match_ctx));
    if (ctx == NULL) {
        PATTERN_MATCH_ERROR("PATTERN_MATCH_MALLOC failed for pattern_match_ctx");
        goto out;
    }

    LIST_INIT(&(ctx->patterns));

out:
    return ctx;
}

/* --------------------------------------------------------------------------*/
/**
 * @Synopsis         Free compiled pattern_match_ctx
 *
 * @Param [in] ctx   pattern_match_ctx to free
 */
/* ----------------------------------------------------------------------------*/
void
pattern_match_free(struct pattern_match_ctx *ctx) {
    struct pattern_list     *patterns = NULL;
    struct pattern_list     *temp = NULL;

    if (ctx == NULL) {
        return;
    }

    LIST_FOREACH_SAFE(patterns, &(ctx->patterns), pattern_list_entry, temp) {
        pattern_list_free(patterns);
    }

    PATTERN_MATCH_FREE(ctx);
}

/* --------------------------------------------------------------------------*/
/**
 * @Synopsis     App and pattern match function.
 *               This follows dynamic programming approach for wildcard pattern matching only.
 *               It is basic approach used to test some initial usecase, but has
 *               the limitation that cannot be used for complex patterns.
 *               The time complexity is O(app_len * pattern_len).
 *
 * NOTE:        'app' should not be NULL. It is the responsibility of the caller to
 *               do this check. We want to prevent unnecessary app check for each
 *               patterns.
 * @Param [in]   app
 * @Param [in]   pattern
 *
 * @Returns      If pattern matched true else false.
 */
/* ----------------------------------------------------------------------------*/
static bool
pattern_match_basic(const char *app, const char *pattern) {
    size_t      app_index;
    size_t      pattern_index;

    /* NOTE: 'app' should not be NULL. It is the responsibility of the caller to
     * do this check. We want to prevent unnecessary app check for each
     * patterns. */
    if (pattern == NULL) {
        PATTERN_MATCH_ERROR("pattern is empty");
        return false;
    }

    const size_t pattern_len = strnlen(pattern, PATTERN_MAX_LEN);
    const size_t app_len = strnlen(app, APPLICATION_DOMAIN_MAX_LEN);
    bool         dp[app_len + 1][pattern_len + 1];

    /* Initialize dp array */
    for (app_index = 0; app_index <= app_len; app_index++) {
        for (pattern_index = 0; pattern_index <= pattern_len; pattern_index++) {
            dp[app_index][pattern_index] = false;
        }
    }

    dp[0][0] = true;
    /* if first character in pattern is '*' */
    if ((pattern_len > 0) && (pattern[0] == '*')) {
        dp[0][1] = true;
    }

    /* Walk through the dp array */
    for (app_index = 1; app_index <= app_len; app_index++) {
        for (pattern_index = 1; pattern_index <= pattern_len; pattern_index++) {
            //both pattern and app character match
            if (app[app_index - 1] == pattern[pattern_index - 1]) {
                dp[app_index][pattern_index] = dp[app_index - 1][pattern_index - 1];
            } else if (pattern[pattern_index - 1] == '*') {
                /* wildcard in path -> match app ch with * or 0 character */
                dp[app_index][pattern_index] = (dp[app_index-1][pattern_index]) || (dp[app_index][pattern_index-1]);
            }
        }
    }

    return dp[app_len][pattern_len];
}

/* --------------------------------------------------------------------------*/
/**
 * @Synopsis     App and pattern match function. This uses POSIX regex engine
 *               matching.
 *
 * @Param [in]   app
 * @Param [in]   regex
 *
 * @Returns      If pattern matched true else false.
 */
/* ----------------------------------------------------------------------------*/
static bool
pattern_match_posix(const char *app, const regex_t *regex) {
    int     ret = PATTERN_MATCH_RESULT_NO_ERROR;
    char    msgbuf[100];

    /* Execute regular expression */
    ret = regexec(regex, app, 0, NULL, 0);
    if (!ret) {
        PATTERN_MATCH_DEBUGF("Match --> APP: [%s]", app);
    } else if (ret == REG_NOMATCH) {
        PATTERN_MATCH_DEBUGF("No match --> APP: [%s]", app);
    } else {
        regerror(ret, regex, msgbuf, sizeof(msgbuf));
        PATTERN_MATCH_ERROR("Regex match failed: %s", msgbuf);
        exit(1);
    }

    return (!ret) ? true : false;
}

/* --------------------------------------------------------------------------*/
/**
 * @Synopsis     App and pattern match function. This uses PCRE regex engine
 *               matching.
 *
 * @Param [in]   app
 * @Param [in]   regex
 *
 * @Returns      If pattern matched true else false.
 */
/* ----------------------------------------------------------------------------*/
static bool
pattern_match_pcre(const char *app, const pcre *re) {
    int rc;
    int ovector[APPLICATION_DOMAIN_MAX_LEN];
    int options = PCRE_ANCHORED;

    rc = pcre_exec(re, NULL, app, (int)strnlen(app, APPLICATION_DOMAIN_MAX_LEN), 0, options, ovector, sizeof(ovector));
    if (rc == PCRE_ERROR_NOMATCH) {
        PATTERN_MATCH_DEBUGF("No match --> APP: [%s]", app);
    } else if (rc < 0) {
        PATTERN_MATCH_ERROR("pcre match failed: %d", rc);
    } else {
        PATTERN_MATCH_DEBUGF("Match --> APP: [%s]", app);
#ifdef DEBUG_PATTERN_MATCH
        /* int rc2; */
        const char *substring;

        // loop through matches and return them
        for (int i=0; i<rc; i++)
        {
            pcre_get_substring(app, ovector, rc, i, &substring);
            PATTERN_MATCH_DEBUGF("%d: %s\n", i, substring);
            pcre_free_substring(substring);
        }
#endif
    }

    return (rc > 0) ? true : false;
}

/* --------------------------------------------------------------------------*/
/**
 * @Synopsis                         Given an app string, returns the list
 *                                   and number of matching patterns.
 *
 * @Param [in]   ctx                 Created pattern_match_ctx containing compiled patterns.
 * @Param [in]   app                 App str to match with the patterns.
 * @Param [out]  result              Matched pattern strings.
 * @Param [in]   result_size         Size of result array to store matched patterns.
 * @Param [out]  num_pattern_matched Num of patterns which matched.
 *
 * @Returns  PATTERN_MATCH_RESULT_BAD_ARGUMENT, PATTERN_MATCH_RESULT_NO_ERROR
 */
/* ----------------------------------------------------------------------------*/
enum pattern_match_error
pattern_match(const struct pattern_match_ctx *ctx, const char *app, char **result, int result_size, int *num_pattern_matched) {
    int                  ret = PATTERN_MATCH_RESULT_NO_ERROR;
    struct pattern_list *patterns = NULL;
    struct pattern_list *temp = NULL;
    int                  matched;

    if ((ctx == NULL) || (result == NULL) || (app == NULL)) {
        PATTERN_MATCH_ERROR("Parameter Error");
        ret = PATTERN_MATCH_RESULT_BAD_ARGUMENT;
        goto out;
    }

    *num_pattern_matched = 0;
    /* Walk through the list of patterns */
    LIST_FOREACH_SAFE(patterns, &(ctx->patterns), pattern_list_entry, temp) {
        if (patterns->pattern == NULL) {
            break;
        }

        if (*num_pattern_matched >= result_size) {
            PATTERN_MATCH_DEBUGF("Number of pattern matched domains going beyond result size %d", result_size);
            break;
        }

        /* Lets match the APP with the pattern */
        matched = ((pattern_match_type == BASIC)
                           ? pattern_match_basic(app, patterns->mod_pattern)
                           : ((pattern_match_type == POSIX) ? pattern_match_posix(app, patterns->regex)
                           : pattern_match_pcre(app, patterns->re)));
        if (matched) {
            result[(*num_pattern_matched)++] = (char *)patterns->pattern;
        }
    }

out:
    if (ret != PATTERN_MATCH_RESULT_NO_ERROR) {
        PATTERN_MATCH_ERROR("pattern_match failed with err: [%d]", ret);
    }
    return ret;
}
