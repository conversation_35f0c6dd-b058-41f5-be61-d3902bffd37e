/*
 * pattern_match_tests.cpp. Copyright (C) 2025 Zscaler, Inc. All Rights Reserved
 */

#include <gtest/gtest.h>
#include "pattern_match/tests/test_headers/pattern_match_test_headers.h"

using ::testing::WithParamInterface;
using ::testing::Values;

class PatternMatchTestSuite: public testing::Test {
public:
    void SetUp() override {
        ctx = pattern_match_create();
        ASSERT_TRUE(ctx);
    }
    void TearDown() override {
        pattern_match_free(ctx);
    }
    struct pattern_match_ctx *ctx;
};

TEST_F(PatternMatchTestSuite, TestPatternCtxCreate) {
    //Setup Expectations
    auto expected_ctx_ptr = nullptr;
    int  expected_ctx_num_patterns = 0;
    auto expected_ctx_list_head_ptr = nullptr;

    //Setup Test Data
    //Run Test
    //Use ctx set in Fixture Creation

    //Test Assertions
    ASSERT_NE(ctx, expected_ctx_ptr);
    EXPECT_EQ(ctx->num_patterns, expected_ctx_num_patterns);
    ASSERT_NE(&ctx->patterns, expected_ctx_list_head_ptr);
}

TEST_F(PatternMatchTestSuite, TestPatternMatchType) {
    //Setup Expectations
    auto expected_pattern_match_type_pcre = PCRE;
    auto expected_pattern_match_type_posix = POSIX;
    auto expected_pattern_match_type_basic = BASIC;

    //Use ctx set in Fixture Creation

    //Test Assertions
    EXPECT_EQ(expected_pattern_match_type_pcre, pattern_match_type);

    //Run Test
    set_pattern_match_type(POSIX);
    EXPECT_EQ(expected_pattern_match_type_posix, pattern_match_type);

    set_pattern_match_type(BASIC);
    EXPECT_EQ(expected_pattern_match_type_basic, pattern_match_type);

    //Reset back to default
    set_pattern_match_type(PCRE);
}

TEST_F(PatternMatchTestSuite, TestPatternMatchMaxSuffixPatterns) {
    int i, actual_result;
    std::string pattern ;

    //Setup Expectations
    auto expected_result_exceed_patterns = PATTERN_MATCH_RESULT_ERR_TOO_LARGE;
    auto expected_result = PATTERN_MATCH_RESULT_NO_ERROR;
    auto expected_num_patterns = MAX_NUM_PATTERNS_PER_SUFFIX;

    //Setup TestData
    for (i = 0; i < MAX_NUM_PATTERNS_PER_SUFFIX; i++) {
        pattern = "abc" + std::to_string(i);
        actual_result = pattern_add(ctx, pattern.c_str());
        EXPECT_EQ(actual_result, expected_result);
        EXPECT_EQ(ctx->num_patterns, i+1);
    }

    //Run Tests
    pattern = "abc" + std::to_string(i);
    actual_result = pattern_add(ctx, pattern.c_str());

    //Test Assertions
    EXPECT_EQ(actual_result, expected_result_exceed_patterns);
    EXPECT_EQ(ctx->num_patterns, expected_num_patterns);
}

using ParamAdd = std::tuple<std::string, int>;
class PatternMatchAddTestSuiteParametrized: public PatternMatchTestSuite,
                                         public WithParamInterface<ParamAdd> {
};

TEST_P(PatternMatchAddTestSuiteParametrized, TestPatternMatchAdd) {
    //Setup Expectation
    int expected_result = std::get<1>(GetParam());

    //Setup Test Data
    auto input_pattern = std::get<0>(GetParam());

    //Run Test
    auto actual_result = pattern_add(ctx, (input_pattern == "nullptr") ? nullptr: input_pattern.c_str());

    //Test Assertions
    EXPECT_EQ(actual_result, expected_result);
}

INSTANTIATE_TEST_SUITE_P(PatternMatchAddTests, PatternMatchAddTestSuiteParametrized,
        Values(std::make_tuple("www.google.com", PATTERN_MATCH_RESULT_NO_ERROR),
               std::make_tuple("nullptr", PATTERN_MATCH_RESULT_BAD_ARGUMENT),
               std::make_tuple("", PATTERN_MATCH_RESULT_NO_ERROR),
               std::make_tuple("adddatatohitmaxlength.google.com", PATTERN_MATCH_RESULT_ERR_TOO_LARGE),
               std::make_tuple("w*w.google.com", PATTERN_MATCH_RESULT_NO_ERROR),
               std::make_tuple("www.g?ogle.com", PATTERN_MATCH_RESULT_NO_ERROR),
               std::make_tuple("w+.google.com", PATTERN_MATCH_RESULT_NO_ERROR),
               std::make_tuple("w{3}.google.com", PATTERN_MATCH_RESULT_NO_ERROR),
               std::make_tuple("*.google.com", PATTERN_MATCH_RESULT_NO_ERROR),
               std::make_tuple("*", PATTERN_MATCH_RESULT_NO_ERROR)
              ),
        [](const testing::TestParamInfo<PatternMatchAddTestSuiteParametrized::ParamType> &info) {
            std::string name = std::get<0>(info.param);
            if (name.empty()) {
                name = std::string("Empty");
            } else {
                replace_if(name.begin(), name.end(), [](unsigned char c){return !::isalnum(c);}, '_');
            }
            return name;
        });

using ParamCreatePattern = std::tuple<std::string, int, std::string>;
class PatternMatchCreateTestSuiteParametrized: public PatternMatchTestSuite,
                                         public WithParamInterface<ParamCreatePattern> {
};

TEST_P(PatternMatchCreateTestSuiteParametrized, TestPatternCreatePattern) {
    //Setup Expectation
    auto expected_result = std::get<2>(GetParam());
    int expected_len = std::get<1>(GetParam());

    //Setup Test Data
    auto input_pattern = std::get<0>(GetParam());
    int pattern_len = strlen(input_pattern.c_str());
    pattern_len += 2;
    pattern_len *= 2;

    char *mod_pattern = (char *)PATTERN_MATCH_MALLOC(pattern_len + 1);

    //Run Test
    pattern_match_create_pattern(input_pattern.c_str(), mod_pattern, pattern_len);

    //Test Assertions
    EXPECT_STREQ(mod_pattern, expected_result.c_str());
    EXPECT_EQ(strlen(mod_pattern), expected_len);
}

INSTANTIATE_TEST_SUITE_P(PatternMatchCreatePatternTests, PatternMatchCreateTestSuiteParametrized,
        Values(std::make_tuple("www.google.com", strlen("^www\\.google\\.com$"), "^www\\.google\\.com$"),
               std::make_tuple("", strlen("^$"), "^$"),
               std::make_tuple("w*w.google.com", strlen("^w.*w\\.google\\.com$"), "^w.*w\\.google\\.com$"),
               std::make_tuple("www.g?ogle.com", strlen("^www\\.g.ogle\\.com$"), "^www\\.g.ogle\\.com$"),
               std::make_tuple("www.g**gle.com", strlen("^www\\.g.*gle\\.com$"), "^www\\.g.*gle\\.com$"),
               std::make_tuple("w+.google.com", strlen("^w+\\.google\\.com$"), "^w+\\.google\\.com$"),
               std::make_tuple("w{3}.google.com", strlen("^w{3}\\.google\\.com$"), "^w{3}\\.google\\.com$"),
               std::make_tuple("*", strlen("^.*$"), "^.*$")
              ),
        [](const testing::TestParamInfo<PatternMatchCreateTestSuiteParametrized::ParamType> &info) {
            std::string name = std::get<0>(info.param);
            if (name.empty()) {
                name = std::string("Empty");
            } else {
                replace_if(name.begin(), name.end(), [](unsigned char c){return !::isalnum(c);}, '_');
            }
            return name;
        });

using ParamRemove = std::tuple<std::string, int, int>;
class PatternMatchRemoveTestSuiteParametrized: public PatternMatchTestSuite,
                                         public WithParamInterface<ParamRemove> {
};

TEST_P(PatternMatchRemoveTestSuiteParametrized, TestPatternMatchRemove) {
    //Setup Expectation
    int expected_result = std::get<1>(GetParam());
    int expected_recurring_remove_result = PATTERN_MATCH_RESULT_NOT_FOUND;
    int actual_recurring_remove_result = PATTERN_MATCH_RESULT_NOT_FOUND;
    int expected_num_patterns = std::get<2>(GetParam());

    //Setup Test Data
    auto input_pattern = std::get<0>(GetParam());
    pattern_add(ctx, "www.google.com");
    pattern_add(ctx, "www.google.com");
    pattern_add(ctx, "w*w.google.com");
    pattern_add(ctx, "www.g?ogle.com");
    pattern_add(ctx, "w+.google.com");

    //Run Test
    auto actual_result = pattern_remove(ctx, (input_pattern == "nullptr") ? nullptr: input_pattern.c_str());
    if (input_pattern != "nullptr") {
        actual_recurring_remove_result = pattern_remove(ctx, input_pattern.c_str());
    }

    //Test Assertions
    EXPECT_EQ(actual_result, expected_result);
    EXPECT_EQ(actual_recurring_remove_result, expected_recurring_remove_result);
    EXPECT_EQ(ctx->num_patterns, expected_num_patterns);
}

INSTANTIATE_TEST_SUITE_P(PatternMatchRemoveTests, PatternMatchRemoveTestSuiteParametrized,
        Values(std::make_tuple("www.google.com", PATTERN_MATCH_RESULT_NO_ERROR, 3),
               std::make_tuple("nullptr", PATTERN_MATCH_RESULT_BAD_ARGUMENT, 5),
               std::make_tuple("", PATTERN_MATCH_RESULT_NOT_FOUND, 5),
               std::make_tuple("w*w.google.com", PATTERN_MATCH_RESULT_NO_ERROR, 4),
               std::make_tuple("www.g?ogle.com", PATTERN_MATCH_RESULT_NO_ERROR, 4),
               std::make_tuple("w+.google.com", PATTERN_MATCH_RESULT_NO_ERROR, 4),
               std::make_tuple("w{3}.google.com", PATTERN_MATCH_RESULT_NOT_FOUND, 5),
               std::make_tuple("*.google.com", PATTERN_MATCH_RESULT_NOT_FOUND, 5)
              ),
        [](const testing::TestParamInfo<PatternMatchRemoveTestSuiteParametrized::ParamType> &info) {
            std::string name = std::get<0>(info.param);
            if (name.empty()) {
                name = std::string("Empty");
            } else {
                replace_if(name.begin(), name.end(), [](unsigned char c){return !::isalnum(c);}, '_');
            }
            return name;
        });

using ParamLookup = std::tuple<std::string, int, int, enum pattern_match_type>;
class PatternMatchLookUpTestSuiteParametrized: public PatternMatchTestSuite,
                                         public WithParamInterface<ParamLookup> {
};

TEST_P(PatternMatchLookUpTestSuiteParametrized, TestPatternMatchLookUp) {
    //Setup Expectation
    int expected_result = std::get<1>(GetParam());
    int expected_num_pattern_matched = std::get<2>(GetParam());
    enum pattern_match_type match_type = std::get<3>(GetParam());

    //Setup Test Data
    set_pattern_match_type(match_type);
    auto input_app = std::get<0>(GetParam());
    pattern_add(ctx, "www.google.com");
    pattern_add(ctx, "w*.g*.com");
    pattern_add(ctx, "*w.go*.com");
    pattern_add(ctx, "*.com");
    pattern_add(ctx, "*");
    if (match_type != BASIC) { // we only support '*' with BASIC
        pattern_add(ctx, "w?.google.com");
        pattern_add(ctx, "w{3}.google.com");
        pattern_add(ctx, "w+.google.com");
        pattern_add(ctx, "w*.g?.com");
        pattern_add(ctx, "www.g?ogle.com");
        pattern_add(ctx, "w+.*.com");
    }

    char *pattern_match_result[DEFAULT_NUM_PATTERNS_PER_SUFFIX];
    int actual_num_pattern_matched = 0;
    //Run Test
    auto actual_result = pattern_match(ctx, (input_app == "nullptr") ? nullptr: input_app.c_str(),
                                       pattern_match_result, DEFAULT_NUM_PATTERNS_PER_SUFFIX, &actual_num_pattern_matched);

    //Test Assertions
    EXPECT_EQ(actual_result, expected_result);
    EXPECT_EQ(actual_num_pattern_matched, expected_num_pattern_matched);
}

INSTANTIATE_TEST_SUITE_P(PatternMatchLookUpTests, PatternMatchLookUpTestSuiteParametrized,
        Values(std::make_tuple("www.google.com", PATTERN_MATCH_RESULT_NO_ERROR, 9, POSIX),
               std::make_tuple("nullptr", PATTERN_MATCH_RESULT_BAD_ARGUMENT, 0, POSIX),
               std::make_tuple("", PATTERN_MATCH_RESULT_NO_ERROR, 1, POSIX),
               std::make_tuple("ww.google.com", PATTERN_MATCH_RESULT_NO_ERROR, 7, POSIX),
               std::make_tuple("www.go.com", PATTERN_MATCH_RESULT_NO_ERROR, 6, POSIX),
               std::make_tuple("www.facebook.com", PATTERN_MATCH_RESULT_NO_ERROR, 3, POSIX),
               std::make_tuple("www.g.com", PATTERN_MATCH_RESULT_NO_ERROR, 4, POSIX),
               std::make_tuple("www.stanford.edu", PATTERN_MATCH_RESULT_NO_ERROR, 1, POSIX),
               std::make_tuple("www.google.com", PATTERN_MATCH_RESULT_NO_ERROR, 5, BASIC),
               std::make_tuple("nullptr", PATTERN_MATCH_RESULT_BAD_ARGUMENT, 0, BASIC),
               std::make_tuple("", PATTERN_MATCH_RESULT_NO_ERROR, 1, BASIC),
               std::make_tuple("ww.google.com", PATTERN_MATCH_RESULT_NO_ERROR, 4, BASIC),
               std::make_tuple("www.go.com", PATTERN_MATCH_RESULT_NO_ERROR, 4, BASIC),
               std::make_tuple("www.facebook.com", PATTERN_MATCH_RESULT_NO_ERROR, 2, BASIC),
               std::make_tuple("www.stanford.edu", PATTERN_MATCH_RESULT_NO_ERROR, 1, BASIC),
               std::make_tuple("www.google.com", PATTERN_MATCH_RESULT_NO_ERROR, 9, PCRE),
               std::make_tuple("nullptr", PATTERN_MATCH_RESULT_BAD_ARGUMENT, 0, PCRE),
               std::make_tuple("", PATTERN_MATCH_RESULT_NO_ERROR, 1, PCRE),
               std::make_tuple("ww.google.com", PATTERN_MATCH_RESULT_NO_ERROR, 7, PCRE),
               std::make_tuple("www.go.com", PATTERN_MATCH_RESULT_NO_ERROR, 6, PCRE),
               std::make_tuple("www.facebook.com", PATTERN_MATCH_RESULT_NO_ERROR, 3, PCRE),
               std::make_tuple("www.g.com", PATTERN_MATCH_RESULT_NO_ERROR, 4, PCRE),
               std::make_tuple("www.stanford.edu", PATTERN_MATCH_RESULT_NO_ERROR, 1, PCRE)
             ),
        [](const testing::TestParamInfo<PatternMatchLookUpTestSuiteParametrized::ParamType> &info) {
            std::string name = std::get<0>(info.param);
            enum pattern_match_type type = std::get<3>(info.param);
            if (type == BASIC) {
                name += "_BASIC";
            } else if (type == POSIX) {
                name += "_POSIX";
            } else if (type == PCRE) {
                name += "_PCRE";
            }

            if (name.empty()) {
                name = std::string("Empty");
            } else {
                replace_if(name.begin(), name.end(), [](unsigned char c){return !::isalnum(c);}, '_');
            }
            return name;
        });
