/*
 * pattern_match_test_headers.h. Copyright (C) 2025 Zscaler, Inc. All Rights Reserved
 * Testing helper to import c headers correctly without compilation errors
 */

#ifndef _PATTERN_MATCH_TEST_HEADERS_H
#define _PATTERN_MATCH_TEST_HEADERS_H

#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wunused-parameter"
#elif defined __GNUC__
#pragma GCC diagnostic push
#pragma GCC diagnostic ignored "-Wunused-parameter"
#endif // __clang__

#include <cstdint>
#include <algorithm>

extern "C" {
#include "pattern_match/pattern_match.h"
};

#ifdef __clang__
#pragma clang diagnostic pop
#elif defined __GNUC__
#pragma GCC diagnostic pop
#endif // __clang__

// Testing prototypes exposed
extern "C" {
void pattern_match_create_pattern(const char *pattern, char *new_pattern, int new_pattern_len);
extern enum pattern_match_type pattern_match_type;
};

#endif //_PATTERN_MATCH_TEST_HEADERS_H
