%global common_install() \
        rm -rf $RPM_BUILD_ROOT \
        %make_install \
        mkdir -p $RPM_BUILD_ROOT%{_unitdir} \
        mkdir -p $RPM_BUILD_ROOT%{_unitdir}/../system-preset \
        mkdir -p $RPM_BUILD_ROOT/etc/sysctl.d \
        install -p -D -m 444 %1.service $RPM_BUILD_ROOT%{_unitdir}/ \
        install -p -D -m 444 53-maps.conf $RPM_BUILD_ROOT/etc/sysctl.d/ \
        install -d -m 755 $RPM_BUILD_ROOT%{prefix}/var \
        %if 0%{?_sanitized} \
            install -d -m 755 $RPM_BUILD_ROOT%{prefix}/report \
            install -p -D -m 644 asan.options $RPM_BUILD_ROOT%{prefix}/etc/asan.options \
        %endif \
        %if 0%{?Command_Logging_Required} \
            install -p -D -m 444 53-et $RPM_BUILD_ROOT/etc/sudoers.d/53-et \
            install -p -D -m 755 bash.bash_logout $RPM_BUILD_ROOT/etc/bash.bash_logout \
            install -p -D -m 755 add_command_logging.sh $RPM_BUILD_ROOT/etc/profile.d/add_command_logging.sh \
            install -p -D -m 755 itasca_pam_log_login.sh $RPM_BUILD_ROOT/usr/local/bin/itasca_pam_log_login.sh \
            install -p -D -m 755 add_login_logging.sh $RPM_BUILD_ROOT/usr/local/bin/add_login_logging.sh \
        %endif \
        install -p -D -m 755 icurl_autocomplete.sh $RPM_BUILD_ROOT/etc/profile.d/icurl_autocomplete.sh \
        install -p -D -m 755 failover_manager.py $RPM_BUILD_ROOT%{prefix}/bin/failover_manager.py \
        %if 0%{?Filesystem_RBAC_Required} \
            install -p -D -m 755 unconfigure_file_permissions.sh $RPM_BUILD_ROOT%{prefix}/var/rbac/unconfigure_file_permissions.sh \
        %endif \
        install -p -D -m 755 setup_user_and_groups.sh $RPM_BUILD_ROOT%{prefix}/var/rbac/setup_user_and_groups.sh \
        %if 0%{?PostgresRBAC_Requried} \
            mkdir -p $RPM_BUILD_ROOT%{prefix}/var/rbac \
            mkdir -p $RPM_BUILD_ROOT/etc/pam.d \
            install -p -D -m 755 configure_pg_rbac.sh $RPM_BUILD_ROOT%{prefix}/var/rbac/configure_pg_rbac.sh \
            install -p -D -m 644 configure_pg_roles.sql $RPM_BUILD_ROOT%{prefix}/var/rbac/configure_pg_roles.sql \
            install -p -D -m 755 create_pg_role_for_user.sh $RPM_BUILD_ROOT%{prefix}/var/rbac/create_pg_role_for_user.sh \
            install -p -D -m 644 configure_pg_privileges.sql $RPM_BUILD_ROOT%{prefix}/var/rbac/configure_pg_privileges.sql \
            install -p -D -m 755 configure_pg_rbac.sh $RPM_BUILD_ROOT%{prefix}/var/rbac/configure_pg_rbac.sh \
            install -p -D -m 755 teleport $RPM_BUILD_ROOT%{prefix}/var/rbac/teleport \
            install -p -D -m 755 pg_hba.conf $RPM_BUILD_ROOT%{prefix}/var/rbac/pg_hba.conf \
            install -p -D -m 755 pg_ident.conf $RPM_BUILD_ROOT%{prefix}/var/rbac/pg_ident.conf \
            install -p -D -m 755 unconfigure_pg_rbac.sh $RPM_BUILD_ROOT%{prefix}/var/rbac/unconfigure_pg_rbac.sh \
        %endif \
        %if 0%{?ZPM_Required} \
            install -p -D -m 444 40-zscaler-zpm.preset $RPM_BUILD_ROOT%{_unitdir}/../system-preset/ \
            install -p -D -m 444 zpm.service $RPM_BUILD_ROOT%{_unitdir}/ \
        %endif \
        %if 0%{?ZHM_Required} \
            install -p -D -m 444 51-zscaler-zhm.preset $RPM_BUILD_ROOT%{_unitdir}/../system-preset/ \
            install -p -D -m 444 zhm.service $RPM_BUILD_ROOT%{_unitdir}/ \
        %endif

%global common_files() \
    %if 0%{?Filesystem_RBAC_Required} \
        %defattr(755,root,root) \
    %else \
        %defattr(-,zscaler,zscaler) \
    %endif \
    %if "%1" == "sbin" \
        %{prefix}/sbin/* \
    %else \
        %{prefix}/bin/* \
    %endif \
    %{prefix}/share/* \
    %{_unitdir}/* \
    %{_unitdir}/../system-preset/* \
    %if 0%{?Filesystem_RBAC_Required} \
        %attr(755, root, root) %{prefix}/var \
    %else \
        %attr(755, zscaler, zscaler) %{prefix}/var \
    %endif \
    %attr(644, root, root) /etc/sysctl.d/* \
    %if 0%{?_sanitized} \
        %dir %{prefix}/report \
        %config(noreplace) %{prefix}/etc/asan.options \
    %endif \
    %if 0%{?Command_Logging_Required} \
        %attr(644, root, root) /etc/sudoers.d/53-et \
        %attr(755, root, root) /etc/bash.bash_logout \
        %attr(755, root, root) /etc/profile.d/add_command_logging.sh \
        %attr(755, root, root) /usr/local/bin/add_login_logging.sh \
        %attr(755, root, root) /usr/local/bin/itasca_pam_log_login.sh \
    %endif \
    %attr(755, root, root) /etc/profile.d/icurl_autocomplete.sh \
    %attr(755, root, root) %{prefix}/bin/failover_manager.py

%global common_clean() \
    [ "$RPM_BUILD_ROOT" != "/" ] && rm -rf $RPM_BUILD_ROOT

%global common_post() \
    %if 0%{?ZPM_Required} \
        %systemd_post zpm.service \
    %endif \
    %if 0%{?ZHM_Required} \
        %systemd_post zhm.service \
    %endif \
    %systemd_post %1.service \
    %{!?Binary_Name: \
        %define BIN_NAME bin/%{1} \
    } \
    %{?Binary_Name: \
        %define BIN_NAME %{Binary_Name} \
    } \
    %if 0%{?APP_CAPABILITIES:1}  \
        %if 0%{?_sanitized} \
            setcap %{APP_CAPABILITIES},CAP_SYS_PTRACE=ep %{prefix}/%{BIN_NAME} \
        %else \
            setcap %{APP_CAPABILITIES}=ep %{prefix}/%{BIN_NAME} \
        %endif \
    %else \
        %if 0%{?_sanitized} \
            setcap CAP_SYS_PTRACE=ep %{prefix}/%{BIN_NAME} \
        %endif \
    %endif \
    /sbin/sysctl -q -p /etc/sysctl.d/53-maps.conf \
    %if 0%{?ZPM_Required} \
        %if 0%{?_sanitized} \
            setcap CAP_NET_BIND_SERVICE,CAP_SYS_PTRACE=ep %{prefix}/bin/zpm \
        %else \
            setcap CAP_NET_BIND_SERVICE=ep %{prefix}/bin/zpm \
        %endif \
    %endif \
    %if 0%{?Zblu_Required} \
        %if 0%{?_sanitized} \
            setcap CAP_NET_BIND_SERVICE,CAP_SYS_PTRACE=ep %{prefix}/bin/zblu \
        %else \
            setcap CAP_NET_BIND_SERVICE=ep %{prefix}/bin/zblu \
        %endif \
    %endif \
    %if 0%{?Command_Logging_Required} \
        bash /usr/local/bin/add_login_logging.sh \
    %endif \
    bash %{prefix}/var/rbac/setup_user_and_groups.sh "zscaler" "zscaler" "pki-access" \

%global common_preun() \
    %if 0%{?Filesystem_RBAC_Required} \
        [ $1 -eq 0 ] && bash %{prefix}/var/rbac/unconfigure_file_permissions.sh \
    %endif \
    %if 0%{?PostgresRBAC_Requried} \
        [ $1 -eq 0 ] && bash %{prefix}/var/rbac/unconfigure_pg_rbac.sh %{_version} \
    %endif \
    %if 0%{?ZPM_Required} \
        %systemd_preun zpm.service \
    %endif \
    %if 0%{?ZHM_Required} \
        %systemd_preun zhm.service \
    %endif \
    %systemd_preun %1.service

%global common_postun() \
    %if 0%{?ZPM_Required} \
        %systemd_postun zpm.service \
    %endif \
    %if 0%{?ZHM_Required} \
        %systemd_postun zhm.service \
    %endif \
    %systemd_postun %1.service \


%global common_global() \
    %global debug_package %{nil} \
    %global __debug_install_post /bin/true \
    %global __os_install_post %{nil}

%global common_posttrans() \
    %if 0%{?PostgresRBAC_Requried} \
        bash %{prefix}/var/rbac/configure_pg_rbac.sh %{_version} \
    %endif \
    %if 0%{?Filesystem_RBAC_Required} \
        bash %{prefix}/var/rbac/setup_folders_and_permissions.sh %{prefix}/var/rbac/itasca_folders_config_v2.conf \
    %endif
