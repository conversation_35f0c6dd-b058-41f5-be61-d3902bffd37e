#!/bin/bash

if [[ -z "${TELEPORT_LOGIN}" || -z "${TELEPORT_ROLES}" ]]; then
    echo "Error: Required environment variables TELEPORT_LOGIN or TELEPORT_ROLES not set"
    exit 1
fi

USERNAME="${TELEPORT_LOGIN}"
TELEPORT_ROLE="${TELEPORT_ROLES}"
PSQL_USER="postgres"
PSQL_CMD="/bin/psql -U ${PSQL_USER} -q "
CHANGES_MADE=0

log_message() {
    logger -t create_pg_teleport -p local0.info "${1}"
}

get_pg_file_path() {
    local config_param=$1
    ${PSQL_CMD} -tAc "SHOW ${config_param};" 2>/dev/null
}

PG_IDENT_FILE=$(get_pg_file_path "ident_file" | tr -d ' ')

USER_GROUPS=$(groups ${USERNAME} | tr ' ' '\n' | grep "datapath-db")
if [ -z "${USER_GROUPS}" ]; then
    echo "No groups containing 'datapath-db' found"
    exit 0
fi
while read -r GROUP_NAME; do
    log_message "Creating PG Role: ${GROUP_NAME} for user ${USERNAME} as "`whoami`
    ADD_ROLE_TEXT="itasca_map    ${USERNAME}    ${GROUP_NAME}"
    if grep -v '^#' "${PG_IDENT_FILE}" | grep -q "${ADD_ROLE_TEXT}"; then
        echo "Map '${ADD_ROLE_TEXT}' already exists in ${PG_IDENT_FILE}"
    else
        echo -e "\n${ADD_ROLE_TEXT}\n" >> ${PG_IDENT_FILE}
        CHANGES_MADE=1
    fi
done <<< "${USER_GROUPS}"

if [ ${CHANGES_MADE} -eq 1 ]; then
    log_message "PostgreSQL configuration reloaded after changes to pg_ident.conf"
    ${PSQL_CMD} -tAc "SELECT pg_reload_conf();"
fi

log_message "ZPA DB Role ${USERNAME} created successfully with ${USER_GROUPS}"
