-- Ensure roles exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_roles WHERE rolname = 'datapath-db-ro') THEN
        CREATE ROLE "datapath-db-ro" LOGIN;
    END IF;
    IF NOT EXISTS (SELECT 1 FROM pg_roles WHERE rolname = 'datapath-db-rw') THEN
        CREATE ROLE "datapath-db-rw" LOGIN;
    END IF;
    IF NOT EXISTS (SELECT 1 FROM pg_roles WHERE rolname = 'datapath-db-admin') THEN
        CREATE ROLE "datapath-db-admin" WITH LOGIN CREATEDB CREATEROLE REPLICATION BYPASSRLS;
    END IF;
END $$;
