#!/usr/bin/env python3

import sys
import argparse

if __name__ == "__main__":
    # Command-line argument parsing
    parser = argparse.ArgumentParser(description="Failover Manager")
    parser.add_argument("--service",         type=str,                                                     help="Name of the component's systemctl service")
    parser.add_argument("--code",            type=str, default="",                                         help="EXIT_CODE      as set by systemd")
    parser.add_argument("--status",          type=str, default="",                                         help="EXIT_STATUS    as set by systemd")
    parser.add_argument("--result",          type=str, default="",                                         help="SERVICE_RESULT as set by systemd")
    parser.add_argument("--base_dir",        type=str, default="/opt/zscaler/dist",                        help="Base directory for RPM files")
    parser.add_argument("--enablement_file", type=str, default="/opt/zscaler/etc/fallback_enablement.txt", help="Path to enablement file")
    parser.add_argument("--dry_run",         type=int, default=None,                                       help="Enable dry run mode (simulates execution)")

    args = parser.parse_args()
    print(f"failover_mgr: Received args code:{args.code}, status:{args.status}, result:{args.result}")

    #Just exit..for now
    sys.exit(0)
