/*
 * zpn_private_brokerd.c. Copyright (C) 2020 Zscaler, Inc. All Rights Reserved.
 *
 */

#include <stdio.h>
#include <stdarg.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <pthread.h>
#include <sys/time.h>
#include <ctype.h>
#include <errno.h>
#include <fcntl.h>
#ifdef __linux__
#include <malloc.h>
#endif

#include "avl/avl.h"
#include "fohh/fohh.h"
#include "fohh/fohh_log.h"
#include "fohh/fohh_http.h"
#include "fohh/fohh_private.h"
#include "wally/wally.h"
#include "wally/wally_postgres.h"
#include "wally/wally_fohh_client.h"
#include "wally/wally_fohh_server.h"
#include "zpath_misc/zpath_misc.h"
#include "zhw/zhw_os.h"

#include "zpath_lib/zpath_local.h"
#include "zpath_lib/zpath_debug.h"
#include "zpath_lib/zpath_app.h"
#include "zpath_lib/zpath_domain_lookup.h"
#include "zpath_lib/zpath_et_zone.h"
#include "zpath_lib/zpath_et_customer_zone.h"
#include "zpath_lib/zpath_et_service_endpoint.h"
#include "zpath_lib/zpath_geoip.h"
#include "zpath_lib/zpath_customer.h"
#include "zpath_lib/et_geoip_override.h"
#include "zpath_lib/zpath_config_override.h"
#include "zpath_lib/zpath_config_override_keys.h"

#include "zthread/zthread.h"

#include "zpn/zpn_lib.h"
#include "zpn/zpn_broker.h"
#include "zpn/zpn_application.h"
#include "zpn/zpn_inspection_application.h"
#include "zpn/zpn_application_domain.h"
#include "zpath_lib/zpath_et_wally_userdb.h"

#include "zcdns/zcdns_libevent.h"

#include "zpath_misc/zpath_version.h"

#include "zpath_misc/zpath_platform.h"

/* Private broker includes */
#include "zpn_enrollment_lib/zpn_enrollment.h"
#include "zpn/zpn_private_broker_private.h"
#include "zpn/zpn_private_broker_site_config.h"
#include "zpn/zpn_private_broker_site.h"
#include "zpn/zpn_private_broker_util.h"
#include "zpn_private_brokerd/zpn_private_broker_enroll.h"
#include "zpn/zpn_pbroker_broker_control.h"
#include "zpn/zpn_pbroker_sitec_control.h"
#include "zpn_private_brokerd/zpn_private_broker_admin_probe.h"
#include "zpn_private_brokerd/zpn_private_broker_stats.h"
#include "zpn_private_brokerd/zpn_private_broker_stats_tx.h"
#include "zpn/zpn_broker_common.h"
#include "zpn_private_brokerd/pbroker_data_connection.h"
#include "zpn/zpn_pbroker_monitor.h"
#include "zpn/zpn_rpc.h"
#include "zcrypt/zcrypt_meta.h"
#include "zhealth/zhealth_probe_lib_thread.h"
#include "zhealth/zhealth_probe_lib.h"
#include "zhealth/zhealth_probe_udp.h"
#include "zpn_zdx/zpn_zdx_mtr.h"
#include "zpn_zdx/zpn_zdx_probes.h"
#include "zpn_pcap/zpn_pcap_lib.h"
#include "zpn_pcap/zpn_pcap.h"
#include "zpn/zpn_file_fetch.h"
#include "zpn/pbroker_assert.h"

#include "zpath_lib/sanitizer_config.h"

#include "zpn_zdx/zpn_zdx_feature.h"
#include "zpn_zdx/zpn_zdx_rate_limit_config.h"
#include "zpn_zdx/zpn_zdx_webprobe_rate_limit.h"
#include "zpn_zdx/zpn_zdx_webprobe_lib.h"
#include "zpn_dr/zpn_dr_lib.h"
#include "zpn_private_brokerd/zpn_dr_interface.h"
#include "zpn/zpn_customer_resiliency_settings.h"

#include "wally/wally_test_origin.h"
#include "zpn_private_brokerd/pbroker_additional_debug_logs.h"
#include "zpath_lib/zpath_system.h"
#include "zpath_lib/zpath_config_override.h"
#include "zpath_lib/zpath_config_override_desc.h"
#include "zpn_event/zpn_event.h"
#include "zpn/zpn_pbroker_data_connection_stats.h"
#include "zpn/zpn_rpc.h"
#include "zpn_private_brokerd/zpn_pbroker_dta_monitor.h"
#include "zpn_private_brokerd/zpn_private_broker_alt_cloud_monitor.h"
#include "fohh/fohh_resolver.h"
#include "zpn/zpn_c2c_client_registration.h"
#include "zpn/zpn_idp_cert.h"
#include "zpath_lib/zpath_system_linux.h"
#include "zpn/zpn_private_broker_proxy.h"
#include "zpn/zpn_version_control/zpn_version_control_utils.h"
#include "zpn/zpn_version_control/zpn_version_control.h"
#include "zpath_lib/zpa_cloud_config.h"
#include "zpath_lib/zpath_upgrade_utils.h"
#include "zpath_lib/zpath_oauth_utils.h"

int config_daemon = 0;
int pbroker_fohh_thread_count = PBROKER_DEFAULT_FOHH_THREADS;
int dr_mode_enabled = 0;

/*
 * Dirty way to tune the memory. Today we have 41 non-fohh threads. Lets give some leeway for expansion and say that we
 * have 45 non-fohh threads.
 */
int pbroker_non_fohh_thread_count = 45;
int debuglog = 0;
int logfiles = 0;
const char *logfile = NULL;
int debug_port = 8500;
int use_sqlt = 0;
int is_container_env = 0;
char mmdb_geoip_version_g[MAX_VERSION_LEN] = {0};
char mmdb_isp_version_g[MAX_VERSION_LEN] = {0};

extern int64_t zevent_use_libevent_priority;
extern int64_t use_custom_app_thread;
extern int sub_module_upgrade_failed;
int sub_module_fail_restart_time;

extern int fohh_tlv_window_size;
extern int fohh_tlv_mconn_window_size;

const char *dbhost = "localhost";
const char *local_file = NULL;
extern char *geoip_db_file;
extern char *geoip_isp_db_file;
extern char zpath_geoip_filename[FILE_PATH_LEN];
extern char zpath_ispip_filename[FILE_PATH_LEN];
static int64_t g_config_pb_client_geoip_disable = DEFAULT_ZPN_GEOIP_MMDB_DOWNLOAD_DISABLE;
extern int64_t g_config_pb_client_no_create;
extern uint8_t g_pb_client_fohh_debug;
int d_authlog_sent=0;
int e_authlog_sent=0;
static zpath_rwlock_t config_check_lock;
static char *stackpath = NULL;
static char config_fetch_ca_file[MAX_CA_FILE_LEN];

int64_t stats_status_interval;
int64_t log_status_interval;
int64_t ctl_status_interval;
int64_t cfg_status_interval;
int64_t rcfg_status_interval;
int64_t ovd_status_interval;
int64_t userdb_status_interval;
int64_t sarge_upgrade_feature_flag = 0;
int64_t os_upgrade_feature_flag = 0;
int64_t sarge_backup_version_feature_flag = 0;
int64_t full_os_upgrade_feature_flag = 0;
int64_t oauth_enrollment_feature_flag = 0;

extern int key_retry_flag[FILE_CODE_MAX];
extern struct ff_stats file_stats[FILE_CODE_MAX];
/* Local static  private broker config */
static struct zpn_common_broker_cfg private_broker_cfg = { 0 };

int64_t argo_logging_threshold_percentage = ARGO_MEM_THRESHOLD_PERCENTAGE_PSE_DEFAULT;

/* Local defines */
#define APP_ROLE "zpa-service-edge-child"

/* Private broker provisioning key path */
#define PBROKER_PROVISION_KEY_PATH  "/opt/zscaler/var/pbroker"

/* Only allow max 15 minutes time adjustment */
#define ZPN_PRIVATE_BROKER_MAX_CLOUD_TIME_ADJUST_US                 (int64_t)(15*60*1000*1000L)

/* Max time critical log frequency */
#define ZPN_PRIVATE_BROKER_MAX_TIME_CRITICAL_LOG_FREQ_S             (2*60)

/* Max allowed fohh queue depth for time adjustment */
#define ZPN_PRIVATE_BROKER_MAX_TIME_ADJ_FOHH_Q_DEPTH                (200)

/* Log every 5 mins */
#define ZPN_PRIVATE_BROKER_TIME_ADJUST_LOG_COUNT                    (5*60)

/* Default max logging size for pbroker is 64 MB */
#define DEFAULT_MAX_LOGGING_MB                                      64

/* Max pse restart time in case of config change*/
#define MAX_PSE_LIBEVENT_RESTART_TIME                               1200

/* PSE APP THREAD AND LIBEVENT FLAG VALUES */
#define LIBEVENT_APP_THREAD_FEATURE_ENABLED                         1
#define LIBEVENT_APP_THREAD_FEATURE_DISABLED                        0
#define LIBEVENT_APP_THREAD_FEATURE_DISABLED_RESTART               -1

int private_broker_main_loop(void);

int g_config_init_done = 0;

void usage(const char *argv0, const char *format, ...)
    __attribute__((format(printf, 2, 3)));

/* ET-87286 usage() on PSE child will no longer exit
 * Caller may decide to exit if it's a critical error */
void usage(const char *argv0, const char *format, ...)
{
    va_list list;

    fprintf(stdout, "Error: ");
	va_start(list, format);
	vfprintf(stdout, format, list);

    fprintf(stdout, "%s: Usage:\n", argv0);
    fprintf(stdout,
            "  -version          : Opt : Display version and exit\n"
            "  -role             : Opt : Display role and exit\n"
            "  -platform         : Opt : Display platform and exit\n"
            "  -arch             : Opt : Display platform architecture and exit\n"
            "  -local_file FILE  : Specify that config should come\n"
            "                      from FILE rather than local_db\n"
            "  -quiet            : Operate without FOHH status messages\n"
            "  -debuglog         : Specify in order to send debug messages to syslog/stderr\n"
            "  -container        : Opt : Set current environment to container\n"
            "  -logfiles         : Generate event and statslog files. WARNING: unconstrained growth\n"
            "  -logfile FILE     : Send event logs to FILE (argo format) WARNING: unconstrained growth\n"
            "  -fproxy NAME      : Opt : Connect to brokers via forward proxy NAME\n"
            "  -fproxy_port #    : Opt : Connect to forward proxy via port #\n"
            "  -disable_heartbeat_monitor: Opt : Disable thread-hang detection\n"
            "  -dbhost NAME      : Specify DB host to connect to. Default 'localhost'\n"
            "  -disable_geoip    : Disable geoip file downloads\n"
            "  -disable_client_version_check    : Disable checking if client is running higher version or not\n"
            "  -threads COUNT    : Specify number of worker threads. Default 4\n"
            "  -daemon           : Run as daemon\n"
            "  -no_flow_control  : Disable flow control on FOHH\n"
            "  -fohh_win         : FOHH connection window size\n"
            "  -fohh_mconn_win   : FOHH TLV mconn window size\n"
            "  -stackpath PATH   : Write cores to path specified. No trailing slash please\n"
            "  -always_re_enroll : Opt : Reenroll for cert.pem renewal\n"
            "  -no_auto_upgrade  : Opt : Disable auto-upgrade\n"
            "  -maxlogmb             : Opt : Specify maximum MB to use for argo logging.\n"
            "  -memory_arena_count # : Opt : Sets a hard limit on the maximum number of arenas that can be created. Linux only\n"
            "  -sqlite           : use sqlt for cache or not\n"
            "  --print-core FILE : Opt : Read FILE and print stack\n"
            "  --print-core-force FILE : Opt : Read FILE and print stack, without checking app name/version\n"

           );
    zpath_app_logging_usage_print();
    va_end(list);
}

/* Verify version logger */
static void log_verify_version_f(int priority, char *log_buf)
{
    char buf[1000];

    snprintf(buf, sizeof(buf), "FILE_FETCH:%s", log_buf);
    ZPATH_LOG(priority, "%s", buf);
}

int file_fetch_callback(char *filename, int is_update, int fcode, char *version){
    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();
    struct zpn_file_fetch_key key;
    memset(&key, 0, sizeof(struct zpn_file_fetch_key));
    key.filename = filename;
    key.is_update = is_update;
    key.version = version;
    key.enc_or_dec_pvt_key = NULL;
    key.enc_or_dec_pvt_key2 = NULL;

    file_stats[fcode].num_key_cb++;
    ZPN_LOG(AL_NOTICE, "File code: %d. Getting the key from public broker(Total public broker callbacks:%d)",fcode,file_stats[fcode].num_key_cb);
    if(zpn_send_zpn_file_fetch_key(gs->private_broker_state->broker_control,
                fohh_connection_incarnation(gs->private_broker_state->broker_control), &key)){
        ZPN_LOG(AL_ERROR, "file_fetch_callback: Unable to fetch decryption key from broker, will retry");
        key_retry_flag[fcode] = 1;
    }

    return ZPATH_RESULT_NO_ERROR;
}

static int zpn_dr_dump_dr_apps(struct zpath_debug_state* request_state, const char** query_values,
                                int query_value_count, void* cookie)
{
    struct zpn_application **apps;
    size_t apps_count;
    size_t i;
    int res;
    int dr_apps_count = 0;
    struct zpn_private_broker_global_state *gs  = zpn_get_private_broker_global_state();
    int64_t customer_gid = gs->customer_id;

    if (!g_config_init_done) {
        ZDP("Config init in-progress. Please try after some time... \n");
        return ZPN_RESULT_NO_ERROR;
    }

    if (!is_pbroker_group_marked_to_support_dr()) {
        ZDP("This PSE is not configured to support DR\n");
        return ZPN_RESULT_NO_ERROR;
    }

    apps = ZPN_MALLOC(sizeof(*apps) * MAX_CLIENT_APPS);
    apps_count = MAX_CLIENT_APPS;

    res = zpn_application_get_by_customer_gid_immediate(customer_gid, apps, &apps_count);
    if (res) {
        ZDP("Error fetching apps for customer:%ld, error_str: %s", (long)customer_gid, zpath_result_string(res));
    } else {
        for (i = 0; i < apps_count; i++) {
            struct zpn_application *app = apps[i];
            if (app->use_in_dr_mode) {
                dr_apps_count++;
                ZDP("App#%d name: %s app_gid: %ld\n", dr_apps_count, app->name, (long) app->gid);
            }
        }
        if (dr_apps_count == 0) {
            ZDP("No DR apps configured for a tenant:%ld.\n", (long)customer_gid);
        }
    }

    ZPN_FREE(apps);
    return ZPN_RESULT_NO_ERROR;
}

static int zpn_dr_show_config_json_stats(struct zpath_debug_state* request_state, const char** query_values,
                                int query_value_count, void* cookie)
{
    if (!g_config_init_done) {
        ZDP("Config init in-progress. Please try after some time... \n");
        return ZPN_RESULT_NO_ERROR;
    }

    if (!is_pbroker_group_marked_to_support_dr()) {
        ZDP("This PSE is not configured to support DR\n");
        return ZPN_RESULT_NO_ERROR;
    }

    zpn_dr_debug_dump_config_file_stats(request_state);

    return ZPN_RESULT_NO_ERROR;
}

static int zpn_dr_show_config(struct zpath_debug_state* request_state, const char** query_values,
                                int query_value_count, void* cookie)
{
    struct zpn_private_broker_global_state *gs  = zpn_get_private_broker_global_state();
    struct  zpath_customer *customer            = NULL;
    int     res                                 = 0;


    if (!g_config_init_done) {
        ZDP("Config init in-progress. Please try after some time... \n");
        return ZPN_RESULT_NO_ERROR;
    }

    if (!is_pbroker_group_marked_to_support_dr()) {
        ZDP("This PSE is not configured to support DR\n");
        return ZPN_RESULT_NO_ERROR;
    }

    // Get DR config from zpath_customer table
    res = zpath_customer_get(gs->customer_id, &customer, NULL, NULL, 0);
    if (res != ZPN_RESULT_NO_ERROR) {
         ZDP("Could not get zpath_customer data to retrieve DR configuration\n");
         return ZPN_RESULT_NO_ERROR;
    }

    ZDP("DR activation domain: %s\n", customer->dr_domain_name);
    ZDP("DR publickey uploaded: %s\n", (customer->zpa_dr_trigger_public_key ? "YES" : "NO"));
    ZDP("DR Max auth interval secs: %d\n", customer->dr_mode_auth_interval);

    return ZPN_RESULT_NO_ERROR;
}
static int zpn_pse_dns_debug_callback(const char *domain_name,
                                             struct argo_inet *ips,
                                             size_t ips_count,
                                             void *callback_void,
                                             int64_t callback_int)
{
    char str[ARGO_INET_ADDRSTRLEN];
    memset(str, 0, ARGO_INET_ADDRSTRLEN);
    for (int i = 0; (i < ips_count) ; i++){
        argo_inet_generate(str, &(ips[i]));
        ZPN_LOG(AL_NOTICE, "Debug test result for fohh resolve: Resolution for fqdn %s is %s", domain_name, str);
    }
    /* Returning an err makes the resolver stop calling us */
    return FOHH_RESULT_ERR;
}

static int  zpn_pse_dns_resolution_query_debug(struct zpath_debug_state* request_state, const char** query_values,
                                int query_value_count, void* cookie)
{
    char domain_name[256] = {0};
    if (!query_values[0]) {
        ZDP("Missing domain_name\n");
        ZDP("Ex: curl http://localhost:8500/pbroker/dns/fohh_resolve/query?domain_name=www.google.com \n");
        return ZPN_RESULT_NO_ERROR;
    }
    snprintf(domain_name,sizeof(domain_name),"%s",query_values[0]);
    fohh_resolve_start(domain_name, FOHH_MIN_DNS_FREQUENCY, NULL, NULL, 0);
    fohh_resolve_start(domain_name, FOHH_MIN_DNS_FREQUENCY, zpn_pse_dns_debug_callback, NULL, 0);
    ZDP("Please check the resolution of requested domain in pse logs(use journalctl).\n");
    ZDP("Check for the string \"Debug test result for fohh resolve\" in the logs \n");
    ZDP("Remove the host %s after testing using the below command \n", domain_name);
    ZDP("\tcurl http://localhost:8500/pbroker/dns/fohh_resolve/remove?domain_name=%s \n",domain_name);
    return ZPATH_RESULT_NO_ERROR;
}

static int  zpn_pse_dns_resolution_remove_debug(struct zpath_debug_state* request_state, const char** query_values,
                                int query_value_count, void* cookie)
{
    char domain_name[256] = {0};
    if (!query_values[0]) {
        ZDP("Missing domain_name\n");
        ZDP("Ex: curl http://localhost:8500/pbroker/dns/fohh_resolve/remove?domain_name=www.google.com \n");
        return ZPN_RESULT_NO_ERROR;
    }
    snprintf(domain_name,sizeof(domain_name),"%s",query_values[0]);
    fohh_resolve_remove(domain_name);
    ZDP("Triggered removal of host %s from the fohh_resolver host table.\n", domain_name);
    return ZPATH_RESULT_NO_ERROR;
}


static int zpn_pse_pbclient_fohh_debug_status(struct zpath_debug_state* request_state, const char** query_values,
                                int query_value_count, void* cookie)
{
    if ( g_pb_client_fohh_debug ) {
        ZDP("pse pb client fohh debug stats is enabled \n");
    } else {
        ZDP("pse pb client fohh debug stats is disabled \n" );
    }
    return ZPN_RESULT_NO_ERROR;
}

static int zpn_pse_pbclient_fohh_debug_enable(struct zpath_debug_state* request_state, const char** query_values,
                                int query_value_count, void* cookie)
{
    g_pb_client_fohh_debug = 1;
    return ZPN_RESULT_NO_ERROR;
}

static int zpn_pse_pbclient_fohh_debug_disable(struct zpath_debug_state* request_state, const char** query_values,
                                int query_value_count, void* cookie)
{
    g_pb_client_fohh_debug = 0;
    return ZPN_RESULT_NO_ERROR;
}

static int zpn_pse_pbclient_create_status(struct zpath_debug_state* request_state, const char** query_values,
                                int query_value_count, void* cookie)
{
    struct zpn_private_broker_global_state *gs  = zpn_get_private_broker_global_state();
    if ( g_config_pb_client_no_create ) {
        ZDP("pse pb client creation is disabled for customer %" PRId64 " \n", gs->customer_id );
    } else {
        ZDP("pse pb client creation is enabled for customer %" PRId64 " \n", gs->customer_id );
    }
    return ZPN_RESULT_NO_ERROR;
}

static int zpn_pse_resiliency_status(struct zpath_debug_state* request_state, const char** query_values,
                                int query_value_count, void* cookie)
{
    struct zpn_private_broker_global_state *gs  = zpn_get_private_broker_global_state();
    struct  zpn_customer_resiliency_settings *resiliency_settings            = NULL;
    int     res                                 = 0;

    res = zpn_pse_get_resiliency_settings(gs->customer_id, &resiliency_settings);
    if ( res ) {
        ZDP("Error: Failed to get resiliency settings for customer %" PRId64 " \n", gs->customer_id );
        return ZPN_RESULT_NO_ERROR;
    }
    ZDP("customer gid : %" PRId64 "\n",resiliency_settings->customer_gid);
    zpn_is_pse_resiliency_enabled() ?
        ZDP("pse resiliency feature : enabled\n") :  ZDP("pse resiliency feature : disabled\n");

    ( zpn_is_pse_resiliency_enabled() && zpn_pse_control_connection_is_cloud_unreachable() )?
        ZDP("pse resiliency mode : active \n") : ZDP("pse resiliency mode : inactive \n");

    ZDP("pse resiliency grace timeout value :  %" PRId64 " \n",resiliency_settings->reauth_grace_timeout_value);
    ZDP("pse resiliency grace timeout unit :  %s \n",resiliency_settings->reauth_grace_timeout_value_unit);
    ZDP("pse calculated resiliency grace timeout value :  %" PRId64 " seconds\n",resiliency_settings->calc_reauth_grace_timeout_value_s);
    ZDP("pse resiliency grace timeout local override  :  %s \n",resiliency_settings->local_override? "yes" : "no");
    ZDP("pse resiliency cloud down interval : %" PRId64 " seconds\n",zpn_pse_get_resiliency_cloud_down_interval_s());

    return ZPN_RESULT_NO_ERROR;
}

static int zpn_pse_resiliency_stats_show(struct zpath_debug_state* request_state, const char** query_values,
                                int query_value_count, void* cookie)
{
    struct zpn_private_broker_global_state *gs  = zpn_get_private_broker_global_state();
    struct tm tmp_time = { 0 };
    char tm_buf[128] = "";

    time_t start_time = zpn_pse_get_resiliency_mode_start_time();
    time_t end_time = zpn_pse_get_resiliency_mode_end_time();
    time_t max_time = zpn_pse_get_resiliency_max_duration_start_time();
    int64_t last_duration = end_time - start_time;

    ZDP("customer gid : %" PRId64 "\n",gs->customer_id);
    if ( start_time != 0 )
    {
        gmtime_r(&start_time,&tmp_time);
        strftime(tm_buf, sizeof(tm_buf), "%a, %d %b %Y %H:%M:%S %Z", &tmp_time);
        ZDP("pse resiliency mode last start time :  %s \n",tm_buf);
    } else {
        ZDP("pse resiliency mode last start time : 0\n");
    }

    if ( end_time != 0 )
    {
        gmtime_r(&end_time,&tmp_time);
        strftime(tm_buf, sizeof(tm_buf), "%a, %d %b %Y %H:%M:%S %Z", &tmp_time);
        ZDP("pse resiliency mode last end time :  %s \n",tm_buf);
    } else {
        ZDP("pse resiliency mode last end time :  0 \n");
    }

    ZDP("pse resiliency mode last duration:  %" PRId64 " seconds\n", (last_duration > 0 )? last_duration : 0);
    ZDP("pse resiliency mode activation count:  %" PRId64 "\n", zpn_pse_get_resiliency_activation_count());
    ZDP("pse resiliency mode max duration:  %" PRId64 " seconds \n",zpn_pse_get_resiliency_max_duration());

    if ( max_time != 0 )
    {
        gmtime_r(&max_time,&tmp_time);
        strftime(tm_buf, sizeof(tm_buf), "%a, %d %b %Y %H:%M:%S %Z", &tmp_time);
        ZDP("pse resiliency mode max duration start time : %s \n",tm_buf);
    } else {
        ZDP("pse resiliency mode max duration start time : 0 \n");
    }

    return ZPN_RESULT_NO_ERROR;
}

static int zpn_pse_resiliency_set_reauth_interval(struct zpath_debug_state* request_state,
                                                    const char** query_values,
                                                    int query_value_count,
                                                    void* cookie)
{
    #define DIGITS_IN_INT64_MAX 19
    struct zpn_private_broker_global_state *gs  = zpn_get_private_broker_global_state();
    struct  zpn_customer_resiliency_settings *resiliency_settings            = NULL;
    char buffer[DIGITS_IN_INT64_MAX+1] = {0};
    char *endptr = NULL;
    if (!query_values[0]) {
        ZDP("Expected argument 'value'\n");
        ZDP("Example: \n");
        ZDP("\tcurl 127.0.0.1:8500/pbroker/resiliency/set_auth_interval?value=300\n");
        return ZPN_RESULT_NO_ERROR;
    } else {
        int res = snprintf(buffer,sizeof(buffer),"%s",query_values[0]);
        if ( res >= 0 ) {
            errno = 0;
            int64_t reauth_interval = strtoll(buffer,&endptr,10);
            if (  ( reauth_interval == 0 ) && ( buffer == endptr ) ) {
                ZDP("Could not convert %s to valid int64_t value ; errno : %d \n",buffer,errno);
            } else if ( errno ) {
                ZDP("Could not convert %s to valid int64_t value ; errno : %d \n",buffer,errno);
            } else if ( reauth_interval < 0 ) {
                ZDP(" Not setting reauth value to negative number %" PRId64 "\n", reauth_interval );
            } else {
                res = zpn_pse_get_resiliency_settings(gs->customer_id, &resiliency_settings);
                if (res) {
                    ZDP("Could not get zpn_customer_resiliency_settings data to set resiliency configuration; res=%s\n" ,
                                 zpath_result_string(res));
                    return ZPN_RESULT_NO_ERROR;
                } else {
                    resiliency_settings->calc_reauth_grace_timeout_value_s = reauth_interval;
                    resiliency_settings->local_override = 1;
                    ZDP("Resiliency reauth interval set to %" PRId64 "\n", reauth_interval );
                }
            }
        } else {
            ZDP("Failed to set reauth interval;Error in copying value;  res=%d " , res );
        }
    }
    return ZPN_RESULT_NO_ERROR;
}

static int zpn_dr_force_deactivate_drmode(struct zpath_debug_state* request_state, const char** query_values,
                                int query_value_count, void* cookie)
{
    int64_t pse_gid = 0;

    if (query_values[0]) {
        pse_gid = strtoul(query_values[0], NULL, 0);
    } else {
        ZDP("Missing parameter.. Please include PSE gid parameter...\n");
        return ZPN_RESULT_NO_ERROR;
    }

    if (pse_gid != g_broker_common_cfg->private_broker.broker_id) {
        ZDP("Incorrect Instance gid:%"PRId64" entered. Please enter the correct PSE Instance Gid...\n", pse_gid);
        return ZPN_RESULT_NO_ERROR;
    }

    if (!is_zpn_drmode_enabled()) {
        ZDP("This PSE isn't running DR mode. Ignoring the deactivation request...\n");
        return ZPN_RESULT_NO_ERROR;
    }

    // De-activate DR mode by removing DR marker file
    ZPN_LOG(AL_NOTICE, "CUSTOMER_DR: Force DR Deactivation requested through Debug command. Service will now restart...");
    zpn_dr_remove_marker_flag();
    /* sleep to ensure contents are synced out to disk from memory before exiting */
    sleep(1);
    exit(0);

    return ZPN_RESULT_NO_ERROR;
}

static int zpn_dr_resolve_dr_domain(struct zpath_debug_state* request_state, const char** query_values,
                                int query_value_count, void* cookie)
{
    struct zpn_private_broker_global_state *gs  = zpn_get_private_broker_global_state();
    enum zpn_dr_domain_record_type_e rec_type   = zpn_dr_domain_record_type_unknown;
    struct  zpath_customer *customer            = NULL;
    int     res                                 = 0;


    if (!g_config_init_done) {
        ZDP("Config init in-progress. Please try after some time... \n");
        return ZPN_RESULT_NO_ERROR;
    }

    if (!is_pbroker_group_marked_to_support_dr()) {
        ZDP("DR domain can't be resolved as this PSE is not configured to support DR\n");
        return ZPN_RESULT_NO_ERROR;
    }

    if (!query_values[0]) {
        ZDP("Missing argument 'record_type'...\n");
        return ZPN_RESULT_NO_ERROR;
    } else {
        if (strcasecmp(query_values[0], ZPN_DR_DNS_RECORD_TYPE_TXT) == 0) {
            rec_type = zpn_dr_domain_record_type_txt;
        } else if (strcasecmp(query_values[0], ZPN_DR_DNS_RECORD_TYPE_A) == 0) {
            rec_type = zpn_dr_domain_record_type_a;
        } else {
            ZDP("Invalid record_type:'%s'.... Expected record_type is 'A' or 'TXT'.\n", query_values[0]);
            return ZPN_RESULT_NO_ERROR;
        }
    }

    // Get txt domain name to query from zpath_customer table
    res = zpath_customer_get(gs->customer_id, &customer, NULL, NULL, 0);
    if (res != ZPN_RESULT_NO_ERROR) {
         ZDP("Could not get zpath_customer data to retrieve DR configuration\n");
         return ZPN_RESULT_NO_ERROR;
    }

    zpn_dr_debug_resolve_dr_domain(request_state, gs->zcdns, customer->dr_domain_name, rec_type);

    return ZPN_RESULT_NO_ERROR;
}

static int zpn_dr_query_txt_record(struct zpath_debug_state* request_state, const char** query_values,
                                        int query_value_count, void* cookie)
{
    if (!g_config_init_done) {
        ZDP("Config init in-progress. Please try after some time... \n");
        return ZPN_RESULT_NO_ERROR;
    }

    if (!is_pbroker_group_marked_to_support_dr()) {
        ZDP("This PSE isn't configured to support DR\n");
    } else {
        zpn_dr_resolve_txt_record();
    }
    return ZPN_RESULT_NO_ERROR;
}

static int zpn_dr_pse_list_config_snapshots(struct zpath_debug_state* request_state, const char** query_values,
                                        int query_value_count, void* cookie)
{
    if (!g_config_init_done) {
        ZDP("Config init in-progress. Please try after some time... \n");
        return ZPN_RESULT_NO_ERROR;
    }

    if (!is_pbroker_group_marked_to_support_dr()) {
        ZDP("This PSE isn't configured to support DR\n");
    } else {
        zpn_dr_list_config_snapshots(request_state, query_values, query_value_count, cookie);
    }
    return ZPN_RESULT_NO_ERROR;
}

static int zpn_dr_pse_list_binary_snapshots(struct zpath_debug_state* request_state, const char** query_values,
                                        int query_value_count, void* cookie)
{
    if (!g_config_init_done) {
        ZDP("Config init in-progress. Please try after some time... \n");
        return ZPN_RESULT_NO_ERROR;
    }

    if (!is_pbroker_group_marked_to_support_dr()) {
        ZDP("This PSE isn't configured to support DR\n");
    } else {
        if (is_binary_snapshot_init_done()) {
            zpn_dr_list_binary_snapshots(request_state, query_values, query_value_count, cookie);
        } else {
            ZDP("Binary snapshot isn't initialized. Try after some time.\n");
        }
    }
    return ZPN_RESULT_NO_ERROR;
}

#ifdef ZPN_DR_SNAPSHOT_INSTALL_CMDS_ENABLE
static int zpn_dr_pse_install_config_snapshot(struct zpath_debug_state* request_state, const char** query_values,
                                        int query_value_count, void* cookie)
{
    if (!is_pbroker_group_marked_to_support_dr()) {
        ZDP("This PSE isn't configured to support DR\n");
    } else {
        zpn_dr_install_config_snapshot(request_state, query_values, query_value_count, cookie);
    }
    return ZPN_RESULT_NO_ERROR;
}

static int zpn_dr_pse_install_binary_snapshot(struct zpath_debug_state* request_state, const char** query_values,
                                        int query_value_count, void* cookie)
{
    if (!is_pbroker_group_marked_to_support_dr()) {
        ZDP("This PSE isn't configured to support DR\n");
    } else {
        zpn_dr_install_binary_snapshot(request_state, query_values, query_value_count, cookie);
    }
    return ZPN_RESULT_NO_ERROR;
}
#endif

static int zpn_dr_pse_snapshot_mgmnt_script_generate(struct zpath_debug_state* request_state, const char** query_values,
                                                    int query_value_count, void* cookie)
{
    if (!g_config_init_done) {
        ZDP("Config init in-progress. Please try after some time... \n");
        return ZPN_RESULT_NO_ERROR;
    }

    if (!is_pbroker_group_marked_to_support_dr()) {
        ZDP("This PSE isn't configured to support DR\n");
    } else {
        zpn_dr_snapshot_mgmnt_script_generate(request_state, query_values, query_value_count, cookie);
    }
    return ZPN_RESULT_NO_ERROR;
}

static int zpn_dr_get_current_status(struct zpath_debug_state* request_state, const char** query_values,
                                        int query_value_count, void* cookie)
{
    if (!g_config_init_done) {
        ZDP("Config init in-progress. Please try after some time... \n");
        return ZPN_RESULT_NO_ERROR;
    }

    if (!is_pbroker_group_marked_to_support_dr()) {
        ZDP("This PSE isn't configured to support DR\n");
    } else {
        if (is_zpn_drmode_enabled()) {
            ZDP("DR mode is active !\n");
        } else {
            ZDP("DR mode is not active !\n");
        }
    }

    return ZPN_RESULT_NO_ERROR;
}

static int pbroker_log_tx_conn_cb(struct fohh_connection *f_conn,
                                  enum fohh_connection_state f_conn_state,
                                  void *cookie)
{
    struct fohh_log_tx_state *tx_state = cookie;
    struct argo_state *argo;
    int res;

    /* We can get a callback before the connection is complete. We'll
     * assign tx_state->f_conn here in case that happens, as tx_state->f_conn is
     * never destroyed once assigned */
    if (!tx_state->f_conn) tx_state->f_conn = f_conn;

    ZPN_DEBUG_STARTUP("Connection callback %s -> %s[%s]:%d, connection state = %s, max sequence acked = %ld",
                       argo_log_get_name(tx_state->collection),
                       tx_state->f_conn->remote_address_name,
                       tx_state->f_conn->remote_address_str,
                       ntohs(tx_state->f_conn->service_port_ne),
                       fohh_connection_state_strings[f_conn_state],
                       (long) tx_state->max_sequence_acked);

    if (f_conn_state == fohh_connection_connected) {
        //fohh_set_debug(f_conn, 1);
        ZPN_LOG(AL_NOTICE, "Log(%s) successfully connected to Zscaler Cloud: %s",
                 argo_log_get_name(tx_state->collection), fohh_description(f_conn));
        ZPN_DEBUG_STARTUP("%p: %s: Connected, will start reading from id: %ld, setting incarnation %ld", tx_state, argo_log_get_name(tx_state->collection), (long)(tx_state->max_sequence_acked + 1), (long) fohh_connection_incarnation(f_conn));
        argo_log_read_set_index(tx_state->reader, tx_state->max_sequence_acked + 1);
        tx_state->f_conn_incarnation = fohh_connection_incarnation(f_conn);
        argo = fohh_argo_get_rx(f_conn);
        if ((res = argo_register_structure(argo, fohh_log_element_ack_description, log_tx_conn_ack_cb, cookie))) {
            ZPN_LOG(AL_ERROR, "Could not register log upload for connection %s", fohh_description(f_conn));
            return res;
        }

        res = log_tx_conn_type(f_conn, argo_log_get_name(tx_state->collection));
        if (res) {
            ZPN_LOG(AL_ERROR, "log(%s) failed to send log type to receiver side %s", argo_log_get_name(tx_state->collection), fohh_description(f_conn));
        }

        /* Register handler for zpn_broker_redirect message */
        res = argo_register_structure(fohh_argo_get_rx(f_conn), zpn_broker_redirect_description, zpn_private_broker_handle_redirect_request, f_conn);
        if (res) {
            ZPN_LOG(AL_ERROR, "Could not register broker_redirect for log connection %s, error: %s", fohh_description(f_conn), argo_result_string(res));
        }

        /* Need to send this message to trigger redirect evaluation logic on the broker side */
        zpn_private_broker_send_fohh_info_with_alt_cloud_capability(f_conn);
    }
    if (f_conn_state == fohh_connection_disconnected) {
        ZPN_LOG(AL_NOTICE, "Log(%s) to Zscaler Cloud closed: %s %s",
                 argo_log_get_name(tx_state->collection), fohh_description(f_conn), fohh_close_reason(f_conn));
        tx_state->current_connection_transmit_count = 0;
        tx_state->current_connection_ack_receive_count = 0;
        tx_state->current_connection_unacknowledged_count = 0;
    }
    return FOHH_RESULT_NO_ERROR;
}

int version_check_callback(int file_code, char *db_version, wally_version_resp_cb *callback_f, void *fi){

    if(file_code == MMDB_GEOIP){
        if(geoip_db_file == NULL){
            memset(db_version, 0, MAX_VERSION_LEN);
            snprintf(db_version, MAX_VERSION_LEN, "default");
        }else{
            memset(db_version, 0, MAX_VERSION_LEN);
            snprintf(db_version, MAX_VERSION_LEN, "%s",mmdb_geoip_version_g);
        }
    }else if(file_code == MMDB_ISP){
        if(geoip_isp_db_file == NULL){
            memset(db_version, 0, MAX_VERSION_LEN);
            snprintf(db_version, MAX_VERSION_LEN, "default");
        }else{
            memset(db_version, 0, MAX_VERSION_LEN);
            snprintf(db_version, MAX_VERSION_LEN, "%s",mmdb_isp_version_g);
        }
    }else{
         snprintf(db_version,MAX_VERSION_LEN, "0");
    }

    return ZPATH_RESULT_NO_ERROR;
}

int download_fail_callback(int file_code){
     if(file_code == MMDB_GEOIP){
        if(geoip_db_file == NULL){
            ZPN_LOG(AL_ERROR, "download_fail_callback: Too many failures for GeoIP2-City.mmdb. Exiting service-edge-child.");
            if(!sub_module_upgrade_failed){
                write_to_file_and_update();
            }
            sub_module_upgrade_failed = 1;
        }
    }else if(file_code == MMDB_ISP){
        if(geoip_isp_db_file == NULL){
            ZPN_LOG(AL_ERROR, "download_fail_callback: Too many failures for GeoIP2-ISP.mmdb. Exiting service-edge-child.");
            if(!sub_module_upgrade_failed){
                write_to_file_and_update();
            }
            sub_module_upgrade_failed = 1;
        }
    }

    return ZPATH_RESULT_NO_ERROR;
}

int64_t config_disable_check_callback(){
    char file_version[FILE_PATH_LEN] = {0};
    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();

    ZPATH_RWLOCK_WRLOCK(&(config_check_lock), __FILE__, __LINE__);

    if(g_config_pb_client_geoip_disable){
        e_authlog_sent = 0;
        if(!d_authlog_sent){
            write_version(FILENAME_GEOIP_ENC, "disabled");
            write_version(FILENAME_ISP_ENC, "disabled");
            //update the auth log
            if (fohh_get_state(gs->private_broker_state->broker_control) == fohh_connection_connected) {
                zpn_send_zpn_tcp_info_report(gs->private_broker_state->broker_control,
                                             fohh_connection_incarnation(gs->private_broker_state->broker_control),
                                             gs->private_broker_state->broker_control,
                                             zvm_vm_type_to_str_concise(zvm_type_get()), g_pbroker_runtime_os);
            }
            d_authlog_sent=1;
        }
    }else{
        d_authlog_sent=0;
        if(!e_authlog_sent){
            if(zpath_is_ispip_running()){
                if(!zpath_is_ispip_fallback_running()){
                    zcrypt_get_metadata_version(FILENAME_ISP_META, file_version, FILE_PATH_LEN, log_verify_version_f, 1);
                }else if(zpath_is_ispip_fallback_running()){
                    zcrypt_get_metadata_version(FILENAME_ISP_FALLBACK_META, file_version, FILE_PATH_LEN, log_verify_version_f, 1);
                }
                ZPN_LOG(AL_NOTICE, "Config check: found version: %s for ISP DB", file_version);
                write_version(FILENAME_ISP_ENC, file_version);
                memset(file_version,0,FILE_PATH_LEN);
            }

            if(zpath_is_geoip_running()){
                if(!zpath_is_geoip_fallback_running()){
                    zcrypt_get_metadata_version(FILENAME_GEOIP_META, file_version, FILE_PATH_LEN, log_verify_version_f, 1);
                }else if(zpath_is_geoip_fallback_running()){
                    zcrypt_get_metadata_version(FILENAME_GEOIP_FALLBACK_META, file_version, FILE_PATH_LEN, log_verify_version_f, 1);
                }
                ZPN_LOG(AL_NOTICE, "Config check: found version: %s for GEOIP DB", file_version);
                write_version(FILENAME_GEOIP_ENC, file_version);
            }

            //update the auth log
            if (fohh_get_state(gs->private_broker_state->broker_control) == fohh_connection_connected) {
                zpn_send_zpn_tcp_info_report(gs->private_broker_state->broker_control,
                                             fohh_connection_incarnation(gs->private_broker_state->broker_control),
                                             gs->private_broker_state->broker_control,
                                             zvm_vm_type_to_str_concise(zvm_type_get()), g_pbroker_runtime_os);
            }
            e_authlog_sent=1;
        }
    }
    ZPATH_RWLOCK_UNLOCK(&(config_check_lock), __FILE__, __LINE__);

    return g_config_pb_client_geoip_disable;
}

static void log_f(int priority, const char *format, va_list list)
{
    char dump[2000];
    vsnprintf(dump, sizeof(dump), format, list);
    if (priority < argo_log_priority_notice) {
        ZPN_LOG(priority, "%s", dump);
    } else {
        ZPN_DEBUG_HEALTH("%s", dump);
    }
}

static int set_status_interval(int interval)
{
    int res = interval;

    /* Round up or round down the status interval, if it is not within FOHH_MIN_STATUS_INTERVAL and FOHH_MAX_STATUS_INTERVAL range */
    if (res < FOHH_MIN_STATUS_INTERVAL) {
        res = FOHH_MIN_STATUS_INTERVAL;
    } else if (res > FOHH_MAX_STATUS_INTERVAL) {
        res = FOHH_MAX_STATUS_INTERVAL;
    }

    return res;
}

void zpn_pbroker_stats_status_interval_config_override_monitor_callback(const int64_t *config_value, int64_t impacted_gid)
{
	int status_interval = set_status_interval((int)*config_value);
    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();

    gs->stats_status_interval = status_interval;
    ZPN_LOG(AL_INFO, "Private broker stats connection status interval configuration set to %d", status_interval);

    return;
}

void zpn_pb_sarge_upgrade_monitor_callback(const int64_t *config_value, int64_t impacted_gid)
{
    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();

    if (*config_value != gs->sarge_upgrade_feature_flag) {
        gs->sarge_upgrade_feature_flag = *config_value;
        ZPN_LOG(AL_INFO, "Private broker Sarge upgrade feature flag changed to %"PRId64, gs->sarge_upgrade_feature_flag);
        zpath_upgrade_set_sarge_upgrade_feature_flag_cfg(gs->sarge_upgrade_feature_flag);
    }
    return;
}

void zpn_pb_os_upgrade_monitor_callback(const int64_t *config_value, int64_t impacted_gid)
{
    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();

    if (*config_value != gs->os_upgrade_feature_flag) {
        gs->os_upgrade_feature_flag = *config_value;
        ZPN_LOG(AL_INFO, "Private broker os upgrade feature flag changed to %"PRId64, gs->os_upgrade_feature_flag);
        zpath_upgrade_set_os_upgrade_feature_flag_cfg(gs->os_upgrade_feature_flag);
    }
    return;
}

void zpn_pb_sarge_backup_version_monitor_callback(const int64_t *config_value, int64_t impacted_gid)
{
    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();

    if (*config_value != gs->sarge_backup_version_feature_flag) {
        gs->sarge_backup_version_feature_flag = *config_value;
        ZPN_LOG(AL_INFO, "Private broker sarge backup version feature flag changed to %"PRId64, gs->sarge_backup_version_feature_flag);
        zpath_upgrade_set_sarge_backup_version_feature_flag(gs->sarge_backup_version_feature_flag);
    }
    return;
}

void zpn_pb_full_os_upgrade_monitor_callback(const int64_t *config_value, int64_t impacted_gid)
{
    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();

    if (*config_value != gs->full_os_upgrade_feature_flag) {
        gs->full_os_upgrade_feature_flag = *config_value;
        ZPN_LOG(AL_INFO, "Private broker full os upgrade feature flag changed to %"PRId64, gs->full_os_upgrade_feature_flag);
        zpath_upgrade_set_full_os_upgrade_version_feature_flag(gs->full_os_upgrade_feature_flag);
    }
    return;
}

void zpn_pb_oauth_enrollment_config_override_monitor_callback(const int64_t *config_value, int64_t impacted_gid)
{
    int32_t feature_disabled = *config_value ? 1 : 0;
    ZPN_LOG(AL_INFO, "Private broker oauth enrollment feature status set to %s", feature_disabled ? "disabled" : "enabled");

    if (feature_disabled) {
        FILE *fp = fopen(OAUTH_ENROLL_DISABLE_FLAG, "w");
        if (fp == NULL) {
            ZPN_LOG(AL_ERROR, "[OAuth] Error creating oauth flag file: %s", strerror(errno));
            return;
        }
        fclose(fp);
        ZPN_LOG(AL_NOTICE, "[OAuth] OAuth Enrollment Disabled. OAuth is the Preferred way of enrollment. Alternatively, you need to copy Provision key to the machine and restart service.");
    } else {
        if (unlink(OAUTH_ENROLL_DISABLE_FLAG) != 0) {
            if (errno != ENOENT) {
                ZPN_LOG(AL_ERROR, "[OAuth] Error deleting oauth flag file: %s", strerror(errno));
            }
        }
        ZPN_LOG(AL_NOTICE, "[OAuth] Oauth Enrollment Enabled");
    }
    return;
}

void zpn_pb_sarge_and_os_upgrade_feature_setup() {

    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();

    gs->sarge_upgrade_feature_flag = zpath_config_override_get_config_int(PBROKER_SARGE_UPGRADE_ENABLE,
                                                                          &gs->sarge_upgrade_feature_flag,
                                                                          DEFAULT_PBROKER_SARGE_UPGRADE_ENABLE,
                                                                          (int64_t)private_broker_cfg.private_broker.broker_id,
                                                                          (int64_t)private_broker_cfg.private_broker.pb_group_id,
                                                                          (int64_t)private_broker_cfg.private_broker.customer_id,
                                                                          (int64_t) ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                                          (int64_t)0);


    gs->os_upgrade_feature_flag = zpath_config_override_get_config_int(AUTOMATIC_OS_UPGRADE_ENABLE,
                                                                       &gs->os_upgrade_feature_flag,
                                                                       DEFAULT_AUTOMATIC_OS_UPGRADE_ENABLE,
                                                                       (int64_t)private_broker_cfg.private_broker.broker_id,
                                                                       (int64_t)private_broker_cfg.private_broker.pb_group_id,
                                                                       (int64_t)private_broker_cfg.private_broker.customer_id,
                                                                       (int64_t) ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                                       (int64_t)0);

    gs->sarge_backup_version_feature_flag = zpath_config_override_get_config_int(SARGE_BACKUP_VERSION_ENABLE,
                                                                                &gs->sarge_backup_version_feature_flag,
                                                                                DEFAULT_SARGE_BACKUP_VERSION_ENABLE,
                                                                                (int64_t)private_broker_cfg.private_broker.broker_id,
                                                                                (int64_t)private_broker_cfg.private_broker.pb_group_id,
                                                                                (int64_t)private_broker_cfg.private_broker.customer_id,
                                                                                (int64_t) ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                                                (int64_t)0);

    gs->full_os_upgrade_feature_flag = zpath_config_override_get_config_int(AUTOMATIC_FULL_OS_UPGRADE_ENABLE,
                                                                            &gs->full_os_upgrade_feature_flag,
                                                                            DEFAULT_AUTOMATIC_FULL_OS_UPGRADE_ENABLE,
                                                                            (int64_t)private_broker_cfg.private_broker.broker_id,
                                                                            (int64_t)private_broker_cfg.private_broker.pb_group_id,
                                                                            (int64_t)private_broker_cfg.private_broker.customer_id,
                                                                            (int64_t) ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                                            (int64_t)0);

    zpath_upgrade_set_sarge_upgrade_feature_flag_cfg(gs->sarge_upgrade_feature_flag);
    zpath_upgrade_set_os_upgrade_feature_flag_cfg(gs->os_upgrade_feature_flag);
    zpath_upgrade_set_sarge_backup_version_feature_flag(gs->sarge_backup_version_feature_flag);
    zpath_upgrade_set_full_os_upgrade_version_feature_flag(gs->full_os_upgrade_feature_flag);

    /* Set up override monitors*/

    zpath_config_override_monitor_int(PBROKER_SARGE_UPGRADE_ENABLE,
                                      &sarge_upgrade_feature_flag,
                                      zpn_pb_sarge_upgrade_monitor_callback,
                                      DEFAULT_PBROKER_SARGE_UPGRADE_ENABLE,
                                      (int64_t)private_broker_cfg.private_broker.broker_id,
                                      (int64_t)private_broker_cfg.private_broker.pb_group_id,
                                      (int64_t)private_broker_cfg.private_broker.customer_id,
                                      (int64_t) ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                      (int64_t)0);

    zpath_config_override_monitor_int(AUTOMATIC_OS_UPGRADE_ENABLE,
                                      &os_upgrade_feature_flag,
                                      zpn_pb_os_upgrade_monitor_callback,
                                      DEFAULT_AUTOMATIC_OS_UPGRADE_ENABLE,
                                      (int64_t)private_broker_cfg.private_broker.broker_id,
                                      (int64_t)private_broker_cfg.private_broker.pb_group_id,
                                      (int64_t)private_broker_cfg.private_broker.customer_id,
                                      (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                      (int64_t)0);

    zpath_config_override_monitor_int(SARGE_BACKUP_VERSION_ENABLE,
                                      &sarge_backup_version_feature_flag,
                                      zpn_pb_sarge_backup_version_monitor_callback,
                                      DEFAULT_SARGE_BACKUP_VERSION_ENABLE,
                                      (int64_t)private_broker_cfg.private_broker.broker_id,
                                      (int64_t)private_broker_cfg.private_broker.pb_group_id,
                                      (int64_t)private_broker_cfg.private_broker.customer_id,
                                      (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                      (int64_t)0);

    zpath_config_override_monitor_int(AUTOMATIC_FULL_OS_UPGRADE_ENABLE,
                                      &full_os_upgrade_feature_flag,
                                      zpn_pb_full_os_upgrade_monitor_callback,
                                      DEFAULT_AUTOMATIC_FULL_OS_UPGRADE_ENABLE,
                                      (int64_t)private_broker_cfg.private_broker.broker_id,
                                      (int64_t)private_broker_cfg.private_broker.pb_group_id,
                                      (int64_t)private_broker_cfg.private_broker.customer_id,
                                      (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                      (int64_t)0);
}

void zpn_pbroker_log_status_interval_config_override_monitor_callback(const int64_t *config_value, int64_t impacted_gid)
{
	int status_interval = set_status_interval((int)*config_value);
    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();

    gs->pblog_txn_status_interval = status_interval;
    gs->pblog_auth_status_interval = status_interval;
    gs->pblog_ast_auth_status_interval = status_interval;
    gs->pblog_event_status_interval = status_interval;
    gs->pblog_dns_status_interval = status_interval;

    ZPN_LOG(AL_INFO, "Private broker log connection status interval configuration set to %d", status_interval);

    return;
}

void zpn_pbroker_ctl_status_interval_config_override_monitor_callback(const int64_t *config_value, int64_t impacted_gid)
{
	int status_interval = set_status_interval((int)*config_value);
    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();

    gs->ctl_status_interval = status_interval;
    ZPN_LOG(AL_INFO, "Private broker control connection status interval configuration set to %d", status_interval);

    return;
}

void zpn_pbroker_cfg_status_interval_config_override_monitor_callback(const int64_t *config_value, int64_t impacted_gid)
{
	int status_interval = set_status_interval((int)*config_value);
    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();

    gs->cfg_status_interval = status_interval;
    ZPN_LOG(AL_INFO, "Private broker cfg connection status interval configuration set to %d", status_interval);

    return;
}

void zpn_pbroker_rcfg_status_interval_config_override_monitor_callback(const int64_t *config_value, int64_t impacted_gid)
{
	int status_interval = set_status_interval((int)*config_value);
    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();

    gs->rcfg_status_interval = status_interval;
    ZPN_LOG(AL_INFO, "Private broker rcfg connection status interval configuration set to %d", status_interval);

    return;
}

void zpn_pbroker_ovd_status_interval_config_override_monitor_callback(const int64_t *config_value, int64_t impacted_gid)
{
	int status_interval = set_status_interval((int)*config_value);
    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();

    gs->ovd_status_interval = status_interval;
    ZPN_LOG(AL_INFO, "Private broker ovd connection status interval configuration set to %d", status_interval);

    return;
}

void zpn_pbroker_userdb_status_interval_config_override_monitor_callback(const int64_t *config_value, int64_t impacted_gid)
{
	int status_interval = set_status_interval((int)*config_value);
    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();

    gs->userdb_status_interval = status_interval;
    ZPN_LOG(AL_INFO, "Private broker userdb connection status interval configuration set to %d", status_interval);

    return;
}

static void zpn_private_broker_enroll_monitor_periodically(evutil_socket_t sock, short flags, void *cookie)
{
    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();

    uint64_t reenroll_period = 0;

    if (zpn_private_broker_has_offline_domain()) {
        reenroll_period = zpn_private_broker_get_reenroll_period_with_lock();
        if (reenroll_period == 0) {
            reenroll_period = ZPN_RE_ENROLL_LEAST_DAYS;
        }
    }

    /* Token is not needed here, signature generated by old certs is good for enrollment API Authorization */
    int result = zpn_enroll_check(&gs->cfg_hw_key, gs->enroll_state->cfg_rsa_key, gs->cfg_fingerprint_str,
                              gs->cfg_provisioning_key, gs->cfg_key_shard, gs->cfg_key_api,
                              ZPN_ENROLLMENT_TYPE_PRIVATE_BROKER, gs->enroll_state->enroll_style, gs->cfg_key_cloud,
                              PBROKER_PROVISION_KEY_PATH, 0,
                              reenroll_period, gs->customer_id, NULL);
    if (result) {
        ZPN_LOG(AL_ERROR, "Failed to enroll invoked from enroll timer callback.");
    }
}

static struct event *reenroll_timer = NULL;

static int
zpn_private_broker_check_reenroll_status(struct zpath_debug_state* request_state,
                                         const char** query_values,
                                         int query_value_count,
                                         void* cookie)
{
    struct timeval now, reenroll;

    evutil_gettimeofday(&now, NULL);

    if (!evtimer_pending(reenroll_timer, &reenroll)) {
        ZDP("Reenroll timer is not running\n");
    } else {
        ZDP("Will reenroll after %ld seconds\n", reenroll.tv_sec - now.tv_sec);
    }

    return ZPN_RESULT_NO_ERROR;
}

static int zpn_private_broker_reenroll_monitor(struct event_base *base)
{

    reenroll_timer = event_new(base,
                               -1,
                               EV_PERSIST,
                               zpn_private_broker_enroll_monitor_periodically,
                               NULL);

    if (!reenroll_timer) {
        ZPN_LOG(AL_CRITICAL, "Failed to create timer for re-enroll");
        return ZPN_RESULT_NO_MEMORY;
    }

    struct timeval tv;
    tv.tv_sec = 300;
    tv.tv_usec = 0;
    if (evtimer_add(reenroll_timer, &tv)) {
        evtimer_del(reenroll_timer);
        ZPN_LOG(AL_CRITICAL, "Failed to activate timer for re-enroll");
        return ZPN_RESULT_NO_MEMORY;
    }

    int res = zpath_debug_add_read_command("check reenroll status",
                                      "/pbroker/reenroll_status",
                                      zpn_private_broker_check_reenroll_status,
                                      NULL,
                                      NULL);
    if (res) {
        ZPN_LOG(AL_ERROR, "Could not register pbroker/reenroll_status. Error: %s", zpn_result_string(res));
        return res;
    }

    return ZPN_RESULT_NO_ERROR;
}

int zpn_private_broker_enroll(int always_re_enroll, zpn_pbroker_site_config_t *site_config)
{
    char str[1000];
    char debug_str[1000];
    struct stat st;
    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();
    struct zpn_enroll_state *enroll_state;
    int result;
    int res;
    int i;
    FILE *fp;
    int write_cloud_root = 0;
    char zhw_err_str[1000] = {0};
    int validate_hw_key = 0;
    int load_hw_info_fail = 0;
    char api_version[10] = {0};
    char offline_domain[ZPN_MAX_DOMAIN_NAME_LEN + 1] = {};
    uint64_t reenroll_period = 0;
    char token[1024] = {0};
    FILE *fp_jwt;

    /* Init enrollment lib */
    if (zpn_enroll_init(&gs->enroll_state) != ZPATH_RESULT_NO_ERROR) {
        ZPN_LOG(AL_ERROR, "Unable to init enrollment api");
        sleep(1);
        exit(1);
    }
    /* Pbroker only does V2 or V3 or V4 enrollment */
    gs->enroll_version = 2;

    /* Local pointer to enroll state */
    enroll_state = gs->enroll_state;

    /* Set enroll style */
    enroll_state->enroll_style = ZPN_ENROLLMENT_STYLE_V2;

    /* If OAuth based enrollment is supported, then use V4 Enrollment APIs */
    if (gs->oauth_enroll) {
        enroll_state->enroll_style = ZPN_ENROLLMENT_STYLE_V4;
        gs->enroll_version = 4;
    }

    /* Set role name */
    snprintf(gs->role_name, sizeof(gs->role_name), "%s", SERVICE_EDGE_LOG_NAME);

    /* Use global zcdns */
    gs->enroll_state->zcdns = gs->zcdns;
    ZPN_LOG(AL_NOTICE, "Checking %s Enrollment", gs->role_name);

    /* Init ZVM */
    result = zvm_init(zpath_event_collection, enroll_state->zcdns);
    if (result) {
        ZPN_LOG(AL_ERROR, "Cannot init zvm");
        return 1;
    }

    memset(debug_str, 0, sizeof(debug_str));
    load_hw_info_fail = load_prev_zhw_id_sha_info();
    if (load_hw_info_fail) {
        /* This is not a critical error for us to take an aggressive action */
        ZPN_LOG(AL_DEBUG, "Error while loading hw_id_info from file %s, it is possible that the file is not present", INSTANCE_ID_BIN_FILE);
    }

    /* Get Hardware ID */
    result = zhw_id_get(&gs->cfg_hw_id, str, sizeof(str), zhw_err_str, sizeof(zhw_err_str), 1);

    if (strlen(zhw_err_str)) {
        ZPN_LOG(AL_ERROR, "Error occurred when getting hardware id: %s", zhw_err_str);
    }
    if (result) {
        ZPN_LOG(AL_ERROR, "Cannot get hardware id");
        return 1;
    }
    //ZPN_LOG(AL_DEBUG, "Hardware ID says: %s", str);

    /* Generate key */
    if (zcrypt_gen_key(&gs->cfg_hw_key, gs->cfg_hw_id.id, sizeof(gs->cfg_hw_id.id)) != ZCRYPT_RESULT_NO_ERROR) {
        ZPN_LOG(AL_ERROR, "Cannot get key");
        return 1;
    }

    if (! load_hw_info_fail) {
        memset(debug_str, 0, sizeof(debug_str));
        res = validate_zhw_id_sha_info(&validate_hw_key, debug_str, sizeof(debug_str));
        if (! res) {
            if (!validate_hw_key && strnlen(debug_str, sizeof(debug_str))) {
                ZPN_LOG(AL_ERROR, "Validation for HW ID failed due to change in %s", debug_str);
            }
        } else {
            ZPN_LOG(AL_ERROR, "Cannot validate current zhw id info with the previous info");
        }
    } else {
        res = store_curr_zhw_id_sha_info();
        if (res) {
            ZPN_LOG(AL_DEBUG, "Error while storing current zhw id info into a file %s", INSTANCE_ID_BIN_FILE);
        }
    }

    /* Get instance */
    res = zpn_enroll_get_instance_id(&gs->cfg_hw_key, gs->cfg_instance_bytes);
    if (res) {
        ZPN_LOG(AL_ERROR, "Cannot get instance id");
        return 1;
    }

    gs->disk_id_fail = hw_id_fails.disk_id_fail;
    gs->imds_disabled = hw_id_fails.imds_disabled;
    gs->imdsv2_required = hw_id_fails.imdsv2_required;

    for (i = 0; i < INSTANCE_ID_BYTES; i++) {
        gs->cfg_fingerprint_bytes[i] = gs->cfg_instance_bytes[i] ^ gs->cfg_hw_id.id[i];
    }

    /* Base64 enocde fingerprint */
    base64_encode_binary(gs->cfg_fingerprint_str, gs->cfg_fingerprint_bytes, sizeof(gs->cfg_fingerprint_bytes));

    /* Get provisioning key */
    if (gs->oauth_enroll) {
        res = zpn_enroll_get_oauth_key(gs->cfg_provisioning_key, sizeof(gs->cfg_provisioning_key));
    } else {
        res = zpn_enroll_get_provisioning_key(&gs->cfg_hw_key, gs->cfg_provisioning_key, sizeof(gs->cfg_provisioning_key));
    }
    if (res) {
        ZPATH_LOG(AL_ERROR, "Cannot get provisioning key, please check if provision_key exists in the current working directory");
        return 1;
    }

    enroll_state->cfg_rsa_key = zcrypt_rsa_key_create();
    if (!enroll_state->cfg_rsa_key) {
        ZPN_LOG(AL_ERROR, "Cannot create keyholder\n");
        return ZPATH_RESULT_ERR;
    }

    /* Get private key */
    if (zpn_enroll_get_private_key(&gs->cfg_hw_key, enroll_state->cfg_rsa_key, FILENAME_KEY) != ZPATH_RESULT_NO_ERROR) {
        ZPN_LOG(AL_ERROR, "Cannot get private key\n");
        return ZPATH_RESULT_ERR;
    }

    /* Read JWT Token required for enrollment. This will be available only for the fresh
     * enrollment using OAuth Server
     */
    if (gs->oauth_enroll) {
        fp_jwt = fopen(FILENAME_OAUTH_TOKEN, "r");
        if (fp_jwt) {
            if (fgets(token, sizeof(token), fp_jwt) == NULL) {
                ZPATH_LOG(AL_ERROR, "[OAuth] Unable to read JWT token needed for v4 enrollment %s - %s",
                                                                                    FILENAME_OAUTH_TOKEN,
                                                                                    strerror(errno));
                fclose(fp_jwt);
                return ZPATH_RESULT_ERR;
            }
            fclose(fp_jwt);
        } else {
            /* After successful enrollment, this file won't be available, its OK!! */
            ZPATH_LOG(AL_DEBUG, "[OAuth] Failed reading %s - %s\n", FILENAME_OAUTH_TOKEN, strerror(errno));
        }
    }

    int new_provision_key = 0;
    struct evbuffer *evbuffer = NULL;

    if (gs->oauth_enroll) {
        res = zpath_get_oauth_cloud_details(gs->cfg_provisioning_key, gs->cfg_key_api, gs->cfg_key_cloud, &gs->customer_id);
    } else {
        res = zpath_get_provisioning_key_details(gs->cfg_provisioning_key, gs->cfg_key_api, gs->cfg_key_cloud);
    }

    /* Only if parsing of new provisioning key is success
     * and we have a valid cloud name, we call the enrollment with api and cloud name
     */

    if ((!gs->cloud_config) &&
        (gs->cfg_key_cloud[0] != '\0') &&
        ( res == ZPATH_RESULT_NO_ERROR ) ) {
        evbuffer = zpn_enroll_get_enrollment_details_raw(gs->cfg_key_cloud,
                                                        enroll_state->cfg_rsa_key,
                                                        gs->cfg_fingerprint_str,
                                                        gs->cfg_provisioning_key,
                                                        gs->cfg_key_api,
                                                        sizeof(gs->cfg_key_api),
                                                        ZPN_ENROLLMENT_TYPE_PRIVATE_BROKER,
                                                        enroll_state->enroll_style,
                                                        token,
                                                        gs->customer_id);
        new_provision_key = 1;

    }

    result = zpath_load_cloud_config_for_customer_apps(&gs->cfg_hw_key, gs->cfg_key_api, evbuffer);
    if (result != ZPATH_RESULT_NO_ERROR) {
        ZPATH_LOG(AL_NOTICE, "Cannot load zpa_cloud_config for the given cloud ");
        return -1;
    }

    /* Enroll provisioning key */
    if (!gs->oauth_enroll && zpn_enroll_extract_key_fields(gs->cfg_provisioning_key, &gs->cfg_key_shard, gs->cfg_key_api,
                                      sizeof(gs->cfg_key_api), gs->cfg_key_cloud,
                                      sizeof(gs->cfg_key_cloud), &gs->root_cert, &gs->root_cert_time) != ZPATH_RESULT_NO_ERROR) {
        ZPATH_LOG(AL_ERROR, "Invalid provisioning key: <%.*s...>",
                  PROVISION_KEY_LOG_BYTES,
                  gs->cfg_provisioning_key);
        return 1;
    }
    /* Try fetching the cloud config based on cloud name, only if we dont have valid cloud config
    */
    if(!gs->cloud_config) {
        gs->cloud_config=zpath_get_cloud_config_from_name(gs->cfg_key_cloud);
    }

    if (!gs->cloud_config) {
        ZPATH_LOG(AL_ERROR, "Unable to fetch cloud configuration file");
        return 1;
    }
    /*
     * Set the appropriate cloud name
     *
     * Here, we will not check for alt_cloud existance, since this cloud name
     * is used in only in admin_probe module to get the cloud name for s3 upload
     *
     * According to the alt_cloud design doc, SNIs for stacktraces and packet
     * captures should be using the default cloud name
     */
    zthread_set_cloud_name(gs->cfg_key_cloud);

    ZPATH_LOG(AL_NOTICE, "Provisioning key shard = %d, api = %s", gs->cfg_key_shard, gs->cfg_key_api);

    reenroll_period = site_config->reenroll_period;
    snprintf(offline_domain, sizeof(offline_domain), "%s", site_config->offline_domain);

    /* Get offline domain */
    ZPN_LOG(AL_DEBUG, "Before enrollment, offline domain is '%s', reenroll period is %ld",
            offline_domain, (long)reenroll_period);

    if ( new_provision_key == 0 ) {
        evbuffer = zpn_enroll_get_enrollment_details_raw(gs->cfg_key_cloud,
                                                        enroll_state->cfg_rsa_key,
                                                        gs->cfg_fingerprint_str,
                                                        gs->cfg_provisioning_key,
                                                        gs->cfg_key_api,
                                                        sizeof(gs->cfg_key_api),
                                                        ZPN_ENROLLMENT_TYPE_PRIVATE_BROKER,
                                                        enroll_state->enroll_style,
                                                        token,
                                                        gs->customer_id);
    }

    result = zpn_enroll_get_enrollment_details(gs->cfg_key_api, sizeof(gs->cfg_key_api), evbuffer,
                                                gs->cfg_key_cloud, &gs->root_cert, &gs->root_cert_time,
                                               &gs->customer_id, api_version, sizeof(api_version),
                                               offline_domain, sizeof(offline_domain),
                                               &reenroll_period);
    if (result) {
        /*
         * Get enrollment details failed, still continue and use api hostname mentioned in
         * provision key and cloud cert coming from hardcoded data.
         */
        ZPN_LOG(AL_ERROR, "Could not fetch enrollment details but it's okay, continuing with existing data");
    } else {
        /* Use V3 enrollment style if api_version returned is "V3". Otherwise V2 by default. */
        if (api_version[0] != 0 && strcmp(api_version, "V3") == 0) {
            gs->enroll_version = 3;
            enroll_state->enroll_style = ZPN_ENROLLMENT_STYLE_V3;
            ZPN_LOG(AL_DEBUG, "Received api_version: %s in get_enrollment_details response, using api style %d for enrollment",
                    api_version, enroll_state->enroll_style);
        }
    }

    if (!gs->root_cert && (stat(FILENAME_CLOUD_ROOT, &st) != 0)) {
        /*
         * This is a fresh enrollment (cloud.pem doesn't exist) and couldn't get cloud cert
         * from hardcoded data and fetch from api also failed. Log and stop.
         */
        ZPN_LOG(AL_ERROR, "Could not get cloud cert, stopping.");
        return ZPATH_RESULT_ERR;
    }

    /* Enrollment check */
    result = zpn_enroll_check(&gs->cfg_hw_key, enroll_state->cfg_rsa_key, gs->cfg_fingerprint_str,
                              gs->cfg_provisioning_key, gs->cfg_key_shard, gs->cfg_key_api,
                              ZPN_ENROLLMENT_TYPE_PRIVATE_BROKER, enroll_state->enroll_style, gs->cfg_key_cloud,
                              PBROKER_PROVISION_KEY_PATH, always_re_enroll,
                              reenroll_period,
                              gs->customer_id,
                              token);
    if (result) {
        ZPN_LOG(AL_ERROR, "Failed to enroll.");
        return ZPATH_RESULT_ERR;
    }

    ZPN_LOG(AL_DEBUG, "After enrollment, offline domain is '%s', reenroll period is %ld",
            offline_domain, (long)reenroll_period);

    snprintf(site_config->offline_domain, sizeof(site_config->offline_domain), "%s", offline_domain);
    site_config->reenroll_period = reenroll_period;

    if (stat(FILENAME_CLOUD_ROOT, &st) != 0) {
        // File not found...
        write_cloud_root = 1;
    } else {
    // Do root_cert_time check only if it's known cloud
        if (gs->root_cert && (st.st_mtime != gs->root_cert_time)) {
            // File has wrong timestamp...
            write_cloud_root = 1;
            unlink(FILENAME_CLOUD_ROOT);
            ZPN_LOG(AL_NOTICE, "Private broker is updating cloud certificates");
        }
    }

    if (write_cloud_root) {
        struct utimbuf tm;

        fp = fopen(FILENAME_CLOUD_ROOT, "w");
        if (!fp) {
            ZPN_LOG(AL_ERROR, "Could not open file for writing cloud certificates");
            return ZPATH_RESULT_ERR;
        }
        if (fwrite(gs->root_cert, strlen(gs->root_cert), 1, fp) != 1) {
            fclose(fp);
            ZPN_LOG(AL_ERROR, "Could not write cloud certificates");
            return ZPATH_RESULT_ERR;
        }

        fclose(fp);

        tm.actime = gs->root_cert_time;
        tm.modtime = gs->root_cert_time;
        if (utime(FILENAME_CLOUD_ROOT, &tm) != 0) {
            ZPN_LOG(AL_ERROR, "Could not update modification time on cloud certificates: %s. Continuing...", strerror(errno));
        }
    }

    gs->cfg_pkey = EVP_PKEY_new();
    res = EVP_PKEY_set1_RSA(gs->cfg_pkey, zcrypt_rsa_key_get_rsa(enroll_state->cfg_rsa_key));
    if (res != 1) {
        ZPATH_LOG(AL_ERROR, "Could not configure private key");
        return ZPATH_RESULT_ERR;
    }

    result = fohh_reset_global_ssl_ctx(FILENAME_CLOUD_ROOT,
                                       FILENAME_CERT,
                                       FILENAME_KEY_PRIV,
                                       gs->cfg_pkey,
                                       VERIFY_PEER);
    if (result) {
        ZPATH_LOG(AL_ERROR, "Could not configure SSL for cloud communication");
        return ZPATH_RESULT_ERR;
    }

    /* Need to read private_broker ID out of certificate */
    {
        struct zcrypt_cert *cert;
        char *w;

        cert = zcrypt_cert_read(FILENAME_CERT);
        if (!cert) {
            ZPN_LOG(AL_ERROR, "Could not read cert file %s", FILENAME_CERT);
            sleep(1);
            exit(1);
        }

        if (fohh_x509_get_cn(zcrypt_cert_get_x509(cert), gs->cn, sizeof(gs->cn))) {
            ZPN_LOG(AL_ERROR, "Could not get CN from cert file %s", FILENAME_CERT);
            sleep(1);
            exit(1);
        }
        zcrypt_cert_free(cert);


        /* Parse first integer of CN */
        for (w = &(gs->cn[0]); *w; w++) {
            if (isdigit(*w)) {
                gs->private_broker_id = strtoll(w, &w, 0);
                break;
            }
        }

        if (!gs->private_broker_id) {
            ZPN_LOG(AL_ERROR, "Could not get CN from cert file %s", FILENAME_CERT);
            sleep(1);
            exit(1);
        }
    }

    gs->customer_id = ZPATH_GID_GET_CUSTOMER_GID(gs->private_broker_id);
    zpn_enroll_upload_stack(gs->cloud_config->sarge->stack_upload_host, gs->cloud_config->sarge->stack_upload_path, APP_ROLE, gs->cn, gs->customer_id);
    zpn_enroll_upload_stack(gs->cloud_config->sarge->stack_upload_host, gs->cloud_config->sarge->stack_upload_path, SARGE_ROLE, gs->cn, gs->customer_id);

    /* Dump global debug state */
    zpn_private_broker_global_state_dump();

    /* Set dev environmaent if applicable */
    set_is_pbroker_dev_environment();

    ZPN_LOG(AL_DEBUG, "Service Edge Enrollment Initialization Complete - %ld", (long)gs->private_broker_id);

    return ZPATH_RESULT_NO_ERROR;
}

/* Broker zdx combiner send callback */
static int zpn_pbroker_probe_combiner_send_cb(char *mtunnel_id,
                                              uint64_t mtunnel_id_hash,
                                              int64_t incarnation,
                                              void *argo_structure_cookie_ptr, //tlv
                                              struct zpn_zdx_probe_legs_info *object) //argo object
{
    int res = ZDX_RESULT_NO_ERROR;
    struct zpn_tlv *cached_tlv = (struct zpn_tlv *)argo_structure_cookie_ptr;

    res = zpn_broker_pb_send_leg_report_to_combiner(mtunnel_id, mtunnel_id_hash, incarnation, object, cached_tlv);
    if (res) {
        ZDX_LOG(AL_INFO, "%s: Unable to send leg report to combiner, ret: %s", mtunnel_id, zpath_result_string(res));
    }

    return ZDX_RESULT_NO_ERROR;
}

static const char *zpn_pb_get_sarge_version(void) {
    FILE *fp = fopen(ZPATH_SARGE_FILENAME_UPDATER, "r");
    if (!fp)
        return NULL;

    char *str = ZPN_CALLOC(ZPATH_VERSION_STR_MAX_LEN + 1);
    if (!fread(str, 1, ZPATH_VERSION_STR_MAX_LEN, fp)) {
        fclose(fp);
        ZPN_FREE(str);
        return NULL;
    };

    fclose(fp);
    return str;
}

static int get_dr_config_snapshot_time_helper(struct zpath_debug_state *request_state,
                                              const char **query_values,
                                              int                        query_value_count,
                                              void *cookie)
{
    struct zpn_private_broker_global_state *gs = NULL;
    char configured_time[ZPN_DR_CONFIG_SNAPSHOT_BUFF_LEN] = { 0 };

    if (!g_config_init_done) {
        ZDP("Config init in-progress. Please try after some time... \n");
        return ZPN_RESULT_NO_ERROR;
    }

    if (!is_pbroker_group_marked_to_support_dr()) {
        ZDP("This PSE isn't configured to support DR\n");
    } else {
        gs = zpn_get_private_broker_global_state();

        zpn_dr_fetch_configured_snapshot_time(ZPN_BROKER_GET_GID(), ZPN_BROKER_GET_GROUP_GID(), gs->customer_id, configured_time);
        ZDP("Config Snapshot Time: %s\n", configured_time);
    }
    return ZPN_RESULT_NO_ERROR;
}

static int dump_dr_config_helper(struct zpath_debug_state*  request_state,
                          const char **              query_values,
                          int                        query_value_count,
                          void*                      cookie) {
    struct zpn_private_broker_global_state *gs = NULL;

    if (!g_config_init_done) {
        ZDP("Config init in-progress. Please try after some time... \n");
        return ZPN_RESULT_NO_ERROR;
    }

    if (!is_pbroker_group_marked_to_support_dr()) {
        ZDP("This PSE isn't configured to support DR\n");
    } else {
        gs = zpn_get_private_broker_global_state();
        dump_dr_config(1, gs->zthread);
    }
    return ZPN_RESULT_NO_ERROR;
}

void argo_logging_threshold_config_override_monitor_callback(const int64_t *config_value, int64_t impacted_gid)
{
	int al_mem_threshold = (int)*config_value;

    if (al_mem_threshold > 100 || al_mem_threshold < 0) {
        ZPATH_LOG(AL_ERROR, "Invalid logging threshold received from config override: %d, using default", al_mem_threshold);
        return;
    }
    argo_log_global_set_mem_threshold(0, al_mem_threshold);
    ZPATH_LOG(AL_INFO, "argo memory logging threshold set to %d", al_mem_threshold);

    return;
}

void restart_pse_config_change(int max_restart_time)
{
    int pse_restart_sec, i=0;

    srand(time(NULL));              // Seed the random number generator with current time
    pse_restart_sec = rand() % max_restart_time;
    ZPATH_LOG(AL_CRITICAL, "Restarting PSE in %d seconds to reflect change.", pse_restart_sec);

    while(i < pse_restart_sec){
        sleep(1);
        i++;
        if(i%10 == 0) {
            ZPATH_LOG(AL_CRITICAL, "Restarting PSE in %d seconds.", pse_restart_sec - i);
        }
        zthread_heartbeat(NULL);
    }

    exit(0);
}

void pse_app_thread_monitor_callback(const int64_t *config_value, int64_t impacted_gid)
{
    int app_thread_new = (int)*config_value;

    ZPATH_LOG(AL_CRITICAL, "Change in app thread usage detected, new value:%s for gid:%"PRId64"", (app_thread_new?"custom threads":"default threads"), impacted_gid);
    if(app_thread_new == LIBEVENT_APP_THREAD_FEATURE_ENABLED || app_thread_new == LIBEVENT_APP_THREAD_FEATURE_DISABLED) {
        ZPATH_LOG(AL_CRITICAL, "Configuration will reflect upon next pse restart");
        return;
    }

    //if it comes here it means the new value is LIBEVENT_APP_THREAD_FEATURE_DISABLED_RESTART and we should restart immediately
    restart_pse_config_change(MAX_PSE_LIBEVENT_RESTART_TIME);
}

void pse_libevent_priority_monitor_callback(const int64_t *config_value, int64_t impacted_gid)
{
    int libevent_priority_new = (int)*config_value;

    ZPATH_LOG(AL_CRITICAL, "Change in libevent priority queue usage detected, new value:%s for gid:%"PRId64"", (libevent_priority_new?"libevent priority":"zevent priority"), impacted_gid);
    if(libevent_priority_new == LIBEVENT_APP_THREAD_FEATURE_ENABLED || libevent_priority_new == LIBEVENT_APP_THREAD_FEATURE_DISABLED) {
        ZPATH_LOG(AL_CRITICAL, "Configuration will reflect upon next pse restart");
        return;
    }

    //if it comes here it means the new value is LIBEVENT_APP_THREAD_FEATURE_DISABLED_RESTART and we should restart immediately
    restart_pse_config_change(MAX_PSE_LIBEVENT_RESTART_TIME);
}

int private_broker_main_loop(void)
{
    int once_waiting = 0;
    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();
    int64_t last_log_time_s = 0;
    uint64_t q_depth = 0;
    uint64_t adj_count = 0;
    uint64_t skip_count = 0;

    /* Assume no delta until we sync time */
    zpn_private_broker_set_cloud_time_delta_us(0);
    while (1) {
        int64_t time_delta_us = 0;

        /* Keep watchdog happy */
        zthread_heartbeat(gs->zthread);

        /* Count iterations */
        adj_count++;

        if (!is_pbroker_running_drmode() && zpn_private_broker_time_differential(gs->private_broker_state, &time_delta_us) != ZPN_RESULT_NO_ERROR) {
            ZPATH_LOG(AL_NOTICE, "Service Edge - waiting for time synchronization");
            once_waiting = 1;
            sleep(1);
            continue;
        }


        if (!is_pbroker_running_drmode())
            q_depth = fohh_connection_get_queue_depth(gs->private_broker_state->broker_control);

        /* Skip time adjustment if queue depth is too deep as time delta will not be accurate anymore */
        if (q_depth <= ZPN_PRIVATE_BROKER_MAX_TIME_ADJ_FOHH_Q_DEPTH) {
            int64_t d;
            char neg = '-';

            /* Update local cloud time delta */

            /* Update our local delta w.r.t cloud if within threshold */
            zpn_private_broker_set_cloud_time_delta_us(time_delta_us);

            /* Upgrade prepare, unlimited adjust for enroll */
            zpn_enroll_upgrade_prep(zpn_private_broker_upgrade_prep, gs->private_broker_id,
                                        (void *)gs->private_broker_state, time_delta_us, gs->auto_upgrade_disabled);

            if (time_delta_us < 0) {
                d = 0 - time_delta_us;
            } else {
                d = time_delta_us;
                neg = '+';
            }

            if (once_waiting) {
                ZPATH_LOG(AL_NOTICE, "%s - Initial Time synchronized, Local time %c %ld.%06lds = cloud_time",
                          SERVICE_EDGE_LOG_NAME,
                          neg,
                          (long)d / 1000000, (long)d % 1000000);
            } else {
                if ((adj_count % ZPN_PRIVATE_BROKER_TIME_ADJUST_LOG_COUNT) == 0) {
                    ZPATH_LOG(AL_NOTICE, "%s - Non-Initial Time synchronized, Local time %c %ld.%06lds = cloud_time",
                              SERVICE_EDGE_LOG_NAME,
                              neg,
                              (long)d / 1000000, (long)d % 1000000);
                }
            }
            once_waiting = 0;
        } else {
            skip_count++;
            if ((adj_count % ZPN_PRIVATE_BROKER_TIME_ADJUST_LOG_COUNT) == 0) {
                if (skip_count > 0) {
                    ZPN_LOG(AL_NOTICE, "Skipped %ld log timestamp adjustments, control queue depth: %ld", (long)skip_count, (long)q_depth);
                    skip_count = 0;
                }
            }
        }

        if(labs(time_delta_us) > ZPN_PRIVATE_BROKER_MAX_CLOUD_TIME_ADJUST_US){
            /* Log every log interval, log hold-down time */
            if (labs(epoch_s() - last_log_time_s) >= ZPN_PRIVATE_BROKER_MAX_TIME_CRITICAL_LOG_FREQ_S) {
                ZPN_LOG(AL_CRITICAL, "Time difference with cloud exceeds: %ld.%06lds, current difference is: %ld.%06lds",
                        (long)(ZPN_PRIVATE_BROKER_MAX_CLOUD_TIME_ADJUST_US / 1000000L),
                        (long)(ZPN_PRIVATE_BROKER_MAX_CLOUD_TIME_ADJUST_US % 1000000L),
                        (long)(time_delta_us / 1000000L),
                        (long)(time_delta_us % 1000000L));
                last_log_time_s = epoch_s();
            }
        }

        zthread_heartbeat(gs->zthread);
        private_broker_state_pause_evaluate();
        private_broker_admin_probe_restart_evaluable();
        sleep(1);
    }
}

int zpn_pbroker_get_max_fohh_threads ()
{
    int num_cores;
    num_cores = sysconf(_SC_NPROCESSORS_ONLN);
    if (-1 == num_cores) {
        fprintf(stderr, "Failed to get number of available cpus - %s\n", strerror(errno));
        num_cores = PBROKER_DEFAULT_FOHH_THREADS;
    } else if (num_cores > FOHH_MAX_THREADS) {
        num_cores = FOHH_MAX_THREADS;
    } else if (num_cores < PBROKER_DEFAULT_FOHH_THREADS) {
        num_cores = PBROKER_DEFAULT_FOHH_THREADS;
    }

    return num_cores;

}
static int pbroker_get_raw_dr_stats(char *buf, size_t buf_len, int pretty)
{
    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();
    struct zpn_private_broker_dr_stats *stats = &gs->dr_stats;
    int ret = ZPN_RESULT_NO_ERROR;

    ret = argo_structure_dump(zpn_private_broker_dr_stats_description, stats, buf, buf_len, NULL, pretty);
    if (ARGO_RESULT_NO_ERROR != ret) {
        ZPN_LOG(AL_ERROR, "Error while dumping argo object 'private broker dr stats' error:%s", zpath_result_string(ret));
    }

    return ret;
}

static int
zpn_pbroker_clear_alt_cloud_cache(struct zpath_debug_state* request_state,
                                  const char** query_values,
                                  int query_value_count,
                                  void* cookie)
{
    (void) request_state;
    (void) query_values;
    (void) query_value_count;
    (void) cookie;

    if(pbroker_store_alt_cloud_to_file(NULL, NULL)) {
        ZDP("Failed to clear local alt_cloud cache");
    } else {
        ZDP("Successfully cleared local alt_cloud cache");
    }

    return ZPN_RESULT_NO_ERROR;
}

/*
 * DO NOT DELETE
 * This timer keeps the misc thread alive with heartbeats
 */
static void pbroker_once_per_second_timer(evutil_socket_t sock, int16_t flags, void *cookie)
{
    struct zthread_info *zthread_arg = cookie;

    zthread_heartbeat(zthread_arg);

    /* Not much to do yet */
    return;
}

static void *pbroker_site_thread(struct zthread_info *zthread_arg, void *cookie)
{
    struct event_base *event_base;
    struct timeval tv;
    struct event *ev;

    event_base = event_base_new();
    if (!event_base) {
        ZPN_LOG(AL_ERROR, "Could not init event_base in pbroker site thread");
        goto fail;
    }

    ev = event_new(event_base, -1, EV_PERSIST, pbroker_once_per_second_timer, zthread_arg);
    if (!ev) {
        ZPN_LOG(AL_CRITICAL, "Could not make event");
        goto fail;
    }

    /* every so often... */
    tv.tv_usec = 0;
    tv.tv_sec = 1;
    if (event_add(ev, &tv)) {
        ZPN_LOG(AL_CRITICAL, "Could not event_add");
        goto fail;
    }

    if (zpn_private_broker_reenroll_monitor(event_base) != ZPN_RESULT_NO_ERROR) {
        ZPN_LOG(AL_CRITICAL, "Failed to monitor enroll");
        goto fail;
    }

    if (zpn_private_broker_site_init_tasks(event_base) != ZPN_RESULT_NO_ERROR) {
        ZPN_LOG(AL_CRITICAL, "Failed to monitor site events");
        goto fail;
    }

    zevent_base_dispatch(event_base);

    ZPN_LOG(AL_CRITICAL, "Not reachable");

fail:
    /* Should watchdog... */
    while(1) {
        sleep(1);
    }
    return NULL;
}

static void zpn_pbroker_on_userdb_client_created(struct fohh_connection *f_conn,
        int64_t zone, int64_t userdb, int current_to_sitec)
{
    char userdb_wally_host[ARGO_MAX_NAME_LENGTH];
    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();
    snprintf(userdb_wally_host, sizeof(userdb_wally_host),
            "%s.%s", ZPN_PRIVATE_BROKER_TO_PUBLIC_BROKER_DNS_NAME, pbroker_get_redir_cloud_name());

    char sni[ARGO_MAX_NAME_LENGTH];
    snprintf(sni, sizeof(sni), pb_sni_customer_domain_userdb_tmpl, (long)zone, (long)userdb);

    zpn_private_broker_site_register_fohh(f_conn, "pbuserdb",
            userdb_wally_host, sni, pbroker_get_redir_cloud_name(), 0, current_to_sitec);

    fohh_connection_monitor_sanity(f_conn,
            zpn_private_broker_fohh_connection_sanity_callback_with_lock,
            ZPN_PRIVATE_BROKER_ALT_CLOUD_CONN_MAX_INIT_TIME_S);

    /*
     * Set status interval address, which has the interval in seconds for sending fohh_status_request messages by connected broker.
     * Default status interval is 1 second for sending fohh_status_request messages.
     */
    fohh_set_status_interval(f_conn, &(gs->userdb_status_interval));
}

static int
pbroker_version_state_dump(struct zpath_debug_state*  request_state __attribute__((unused)),
                           const char**               query_values __attribute__((unused)),
                           int                        query_value_count __attribute__((unused)),
                           void*                      cookie __attribute__((unused)))
{
    const struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();

    ZDP("===private broker version state ===\n");
    if (gs->version_data.client_version == zpn_client_version_invalid) {
        ZDP("%s\n", "Invalid");
    } else if (gs->version_data.client_version == zpn_client_version_valid) {
        ZDP("%s\n", "Valid");
    } else {
        ZDP("%s\n", "Unknown");
    }

    return ZPATH_RESULT_NO_ERROR;
}


static int pbroker_validate_current_version_prep() {
    int result = 0;
    struct wally *private_broker_gwally = NULL;
    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();

    private_broker_gwally = gs->private_broker_state->wally_ovd;
    PBROKER_ASSERT_HARD(private_broker_gwally != NULL, "Broker Init: Private broker gwally is NULL!");

    ZPN_DEBUG_STARTUP("Initializing zpath_config_override...for pbroker");

    result = zpath_config_override_init(private_broker_gwally,
                                        ZPATH_GID_GET_CUSTOMER_GID(gs->private_broker_id),
                                        0,
                                        config_component_pbroker);
    if (result) {
        ZPN_LOG(AL_ERROR, "zpath_config_override failed: %s", zpath_result_string(result));
        return ZPN_RESULT_ERR;
    }

    result = zpn_version_control_register_pse_version_control_descriptions();
    if (result) {
        ZPN_LOG(AL_CRITICAL, "Could not register descriptions for zpn_pse_version_control : %s", zpn_result_string(result));
        return ZPN_RESULT_ERR;
    }

    return ZPN_RESULT_NO_ERROR;
}

static void pbroker_validate_current_version() {
    const struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();

    if (zpn_version_control_feature_status_for_pbroker(gs->private_broker_id)) {
        int64_t start_time = epoch_s();
        while (gs->version_data.client_version == zpn_client_version_unknown) {
            if (!zpn_version_control_feature_status_for_pbroker(gs->private_broker_id)) {
                ZPN_LOG(AL_INFO, "version control feature got disabled on pse, continuing without version validation");
                return;
            }

            /* If pse does not receive reply to zpn_version rpc after 10 secs, skip version validation */
            if ((epoch_s() - start_time) >= MAX_VERSION_CONTROL_CHECK_WAIT_TIME_S) {
                ZPN_LOG(AL_INFO, "Max time to receive version_ack rpc exceeded, continuing without version validation");
                return;
            }

            ZPN_LOG(AL_WARNING, "Pbroker version has not been verified. Checking again in 1 sec");
            sleep(1);
        }

        if (gs->version_data.client_version == zpn_client_version_invalid) {
            if (zpn_version_control_denied_version_file_exists()) {
                ZPATH_LOG(AL_NOTICE, "%s file: %s already exists ...deleting it!",SERVICE_EDGE_LOG_NAME,DENIED_VERSION_FILE);
                zpn_version_control_delete_denied_version_file();
            }

            if (zpn_version_control_create_denied_version_file()) {
                ZPATH_LOG(AL_NOTICE, "%s Unable to create the %s file!",SERVICE_EDGE_LOG_NAME,DENIED_VERSION_FILE);
            }

            ZPN_LOG(AL_CRITICAL, "Pbroker running version: %s is not compatible. Exiting!!",ZPATH_VERSION);
            sleep(1);
            exit(0);
        } else {
            ZPN_LOG(AL_INFO, "Pbroker running version: %s is compatible, continuing!",ZPATH_VERSION);
        }
    } else {
        ZPN_LOG(AL_INFO, "version control feature is disabled on pse, continuing without version validation");
    }
}

/*
    Compare current running version with previously seen denied version.
    If current running version is the same as the denied version, exit...
*/
static void pbroker_check_last_denied_version() {
    int ret = -1;
    char version[MAX_VERSION_STRING_LEN] = {0};

    if (zpn_version_control_denied_version_file_exists()) {
        ret = zpn_version_control_read_denied_version_file(version,MAX_VERSION_STRING_LEN);
        if (ret == ZPN_RESULT_NO_ERROR) {

            if (strcmp(version,ZPATH_VERSION) != 0) {
                ZPATH_LOG(AL_NOTICE, "%s current version does not %s match previous denied version %s, deleting denied_version file! ", SERVICE_EDGE_LOG_NAME, ZPATH_VERSION, version);
                ret = zpn_version_control_delete_denied_version_file();
                struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();
                if (ret == ZPN_RESULT_ERR) {
                    gs->version_data.denied_version_del_fail++;
                }
                gs->version_data.restart_invalid_version++;
            } else {
                ZPATH_LOG(AL_CRITICAL, "%s current version %s matches previous denied version %s, Exiting!",SERVICE_EDGE_LOG_NAME, ZPATH_VERSION, version);
                sleep(1);
                exit(0);
            }
        } else {
            unlink(DENIED_VERSION_FILE);
            ZPATH_LOG(AL_NOTICE, "%s Unable to read version from %s file for version control enforcement on pbroker, deleting the file", SERVICE_EDGE_LOG_NAME,DENIED_VERSION_FILE);
        }
    }

}

void zpn_private_broker_check_firedrill_mode_on_boot()
{
    int64_t current_time_s = 0;
    int64_t fd_leftover_s = 0;

    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();

   /* Read the site_config.cfg file for firedrill status */
    current_time_s = epoch_s();
    fd_leftover_s = (current_time_s - gs->firedrill_starttime);
    if(gs->firedrill_status == ZPN_PRIVATE_BROKER_FIREDRILL_ENABLED &&
        /* if the difference of current and start is less than the firedrill interval then we are in the firedrill window */
        fd_leftover_s < gs->firedrill_interval)  {

        gs->firedrill_leftover_s = gs->firedrill_interval - fd_leftover_s;

        /*
           we are in firedrill mode before the restart, start timer for the remaining time
           we have to wait for pbroker_firedrill_timer to be initialised and then start the timer
           but set the flag here so that we know we have to start the firedrill
        */

        ZPN_LOG(AL_ERROR, "pse booted in firedrill mode, starting the timer for the remaining duration");
        ZPN_LOG(AL_ERROR, "firedrill interval: %"PRId64" start time: %"PRId64" leftover_interval_s: %"PRId64"", gs->firedrill_interval, gs->firedrill_starttime, gs->firedrill_leftover_s);
    } else {
        pthread_mutex_lock(&gs->lock);

        gs->firedrill_leftover_s = 0;
        gs->firedrill_status = ZPN_PRIVATE_BROKER_FIREDRILL_DISABLED;
        gs->firedrill_starttime = 0;
        gs->firedrill_interval = 0;

        pthread_mutex_unlock(&gs->lock);
        ZPN_LOG(AL_ERROR, "pse boot firedrill not active");
    }
}


int sub_main(int argc, char *argv[])
{
    int result;
    int i;
    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();
    int always_re_enroll;
    int disable_auto_upgrade;
    int disable_geoip;
    char env_val_develop[64] = "";
    char env_val_disable_geoip[64] = "";
    int memory_arena_count;
    const char *pbroker_name   = NULL;
    int64_t swap_config;
    char *geoip_data = NULL, *geoip_fallback_data = NULL;
    size_t geoip_size = 0,geoip_fallback_size = 0;
    char *ispip_data = NULL, *ispip_fallback_data = NULL;
    size_t ispip_size = 0,ispip_fallback_size = 0;
    char *mmdb_fetch_proxyname;
    char *mmdb_fetch_hostname;
    char *geodb_path = FETCH_PATH_DEVELOP;
    char *geoip_filename = FILENAME_GEOIP_ENC;
    char *isp_filename = FILENAME_ISP_ENC;
    char geodb_path_full[FILE_PATH_LEN] = "";
    char isp_path_full[FILE_PATH_LEN] = "";
    char cl_arg[PBROKER_COMMAND_LINE_ARG_LEN] = {'\0'};
    int develop_mode = 1;
    int isp_sanity = 1, geo_sanity = 1;
    config_check_lock = ZPATH_RWLOCK_INIT;
    int64_t min_mem_config_val = 2048;
    uint64_t total_memory = 0, memfree_abs_mem = 0, system_used_abs_mem = 0;
    uint64_t total_swap_memory = 0, swapfree_abs_mem = 0, process_used_abs_mem = 0;
    pthread_t thread_site;
    zpn_pbroker_site_config_t site_config = {};
    int64_t custom_app_thread_use = 0;
    int64_t use_libevent_priority = 0;
    char *env_oauth = NULL;

    config_fetch_ca_file[0] = '\0';
    sub_module_upgrade_failed = 0;
    sub_module_fail_restart_time = 0;
    gs->version_data.client_version = zpn_client_version_unknown;

    zthread_init(APP_ROLE, ZPATH_VERSION, "unknown", NULL, NULL);
    zthread_do_stack_dump(&argc, argv);

	pbroker_fohh_thread_count = zpn_pbroker_get_max_fohh_threads();

    result = zpn_private_broker_global_state_clear();
    if (result != ZPATH_RESULT_NO_ERROR) {
        ZPN_LOG(AL_ERROR, "Private broker global state clear, err: %s", zpath_result_string(result));
    }

    /* Read Alt-Cloud cached information from local filesystem. If found, populate the cloud names in 'zpn_private_broker_global_state' */
    int fresult = pbroker_read_alt_cloud_from_file(gs->listener_alt_cloud_name, gs->redir_alt_cloud_name);
    if(fresult) {
        ZPN_LOG(AL_ERROR, "Failed to read alt cloud information, err: %s", zpath_result_string(fresult));
        /* In case of any error while reading the cache-file, we revert to using the default cloud_name */
        memset(gs->listener_alt_cloud_name, 0, sizeof(gs->listener_alt_cloud_name));
        memset(gs->redir_alt_cloud_name, 0, sizeof(gs->redir_alt_cloud_name));
    }
    gs->sarge_version = zpn_pb_get_sarge_version();

    /* Get the runtime OS information for the pbroker */
    zhw_get_runtime_os(g_pbroker_runtime_os, sizeof(g_pbroker_runtime_os));

    zpn_private_broker_site_config_load(&site_config);
    update_global_state_from_site_config(&site_config);

    always_re_enroll = 0;
    disable_auto_upgrade = 0;
    memory_arena_count = -1;
    disable_geoip = zcrypt_metadata_disable_geoip_false;

    if (zpath_app_logging_parse_args(&argc, argv)) {
        ZPN_LOG(AL_ERROR, "zpath_app_logging_parse_args failed");
        sleep(1);
        exit(1);
    }

    for (i = 1; i < argc; i++) {
        snprintf(cl_arg+strlen(cl_arg),PBROKER_COMMAND_LINE_ARG_LEN - strlen(cl_arg)," %s",argv[i]);
        cl_arg[PBROKER_COMMAND_LINE_ARG_LEN - 1] = '\0';
        /* Test for all one-word arguments. */
        if (strcmp(argv[i], "-daemon") == 0) {
            config_daemon = 1;
        } else if (strcmp(argv[i], "-version") == 0) {
            fprintf(stdout, "%s\n", ZPATH_VERSION);
            exit(1);
        } else if (strcmp(argv[i], "-role") == 0) {
            fprintf(stdout, "%s\n", APP_ROLE);
            exit(1);
        } else if (strcmp(argv[i], "-platform") == 0) {
            fprintf(stdout, "%s%d\n", ZPATH_PLATFORM_NAME, ZPATH_PLATFORM_VERSION);
            exit(1);
        } else if (strcmp(argv[i], "-arch") == 0) {
            fprintf(stdout, "%s\n", ZPATH_PLATFORM_ARCH);
            exit(1);
        } else if (strcmp(argv[i], "-quiet") == 0) {
            fohh_set_quiet(1);
        } else if (strcmp(argv[i], "-debuglog") == 0) {
            debuglog = 1;
        } else if (strcmp(argv[i], "-container") == 0) {
            is_container_env = 1;
            zpn_private_broker_state_set_container_env();
        } else if (strcmp(argv[i], "-logfiles") == 0) {
            logfiles = 1;
        } else if (strcmp(argv[i], "-disable_heartbeat_monitor") == 0) {
            zthread_disable_heartbeat_monitor();
        } else if (strcmp(argv[i], "-no_flow_control") == 0) {
            flow_control_enabled = 0;
        } else if (strcmp(argv[i], "-always_re_enroll") == 0) {
            always_re_enroll = 1;
        } else if (strcmp(argv[i], "-sqlite") == 0) {
            use_sqlt = 1;
        } else if (strcmp(argv[i], "-no_auto_upgrade") == 0) {
            disable_auto_upgrade = 1;
        } else if (strcmp(argv[i], "-disable_geoip") == 0) {
            disable_geoip = zcrypt_metadata_disable_geoip_true;
        } else if (strcmp(argv[i], "-disable_client_version_check") == 0) {
            set_wally_client_incompatible_version_check_disable_cli();
        } else {
            /* Test for all two-word arguments. */
            if ((i + 1) >= argc) {
                /* There is not a pair of words... */
                usage(argv[0], "Improper argument- may be missing second field: %s\n", argv[i]);
                break;
            }
            snprintf(cl_arg+strlen(cl_arg),PBROKER_COMMAND_LINE_ARG_LEN - strlen(cl_arg)," %s",argv[i+1]);
            cl_arg[PBROKER_COMMAND_LINE_ARG_LEN - 1] = '\0';
            if (strcmp(argv[i], "-stackpath") == 0) {
                i++;
                stackpath = argv[i];
            } else if (strcmp(argv[i], "-local_file") == 0) {
                i++;
                local_file = argv[i];
            } else if (strcmp(argv[i], "-logfile") == 0) {
                i++;
                logfile = argv[i];
            } else if (strcmp(argv[i], "-dbhost") == 0) {
                i++;
                dbhost = argv[i];
            } else if (strcmp(argv[i], "-offline_domain") == 0) {
                i++;
                if (strlen(argv[i]) >= sizeof(site_config.offline_domain)) {
                    fprintf(stdout, "The max argument length of the '-offline_domain' is %lu\n",
                            sizeof(site_config.offline_domain) - 1);
                    exit(1);
                }
                strcpy(site_config.offline_domain, argv[i]);
            } else if (strcmp(argv[i], "-threads") == 0) {
                i++;
				int         cli_fohh_thread;
                cli_fohh_thread = atoi(argv[i]);
                if ((cli_fohh_thread <= 0) || (cli_fohh_thread > pbroker_fohh_thread_count)
                    || (cli_fohh_thread > FOHH_MAX_THREADS)) {
                    fprintf(stdout, "Thread count %d not supported in this hardware architecture max %d\n",
                            cli_fohh_thread, pbroker_fohh_thread_count);
                    exit(1);
                }
                pbroker_fohh_thread_count = cli_fohh_thread;
            } else if (strcmp(argv[i], "-fohh_win") == 0) {
                i++;
                fohh_tlv_window_size = atoi(argv[i]);
            } else if (strcmp(argv[i], "-fohh_mconn_win") == 0) {
                i++;
                fohh_tlv_mconn_window_size = atoi(argv[i]);;
            } else if (strcmp(argv[i], "-maxlogmb") == 0) {
                i++;
                zpath_app_set_specific_max_logging_mb(atoi(argv[i]));
            } else if (strcmp(argv[i], "-memory_arena_count") == 0) {
                i++;
                memory_arena_count = atoi(argv[i]);
            } else if (strcmp(argv[i], "-fproxy") == 0) {
                i++;
                fohh_proxy_hostname = (argv[i]);
            } else if (strcmp(argv[i], "-fproxy_port") == 0) {
                i++;
                fohh_proxy_port = atoi(argv[i]);
            } else if (strcmp(argv[i], "-repo_ca_file") == 0) {
                i++;
                snprintf(config_fetch_ca_file, MAX_CA_FILE_LEN , "%s", argv[i]);
            } else {
                usage(argv[0], "Unrecognized argument: %s\n", argv[i]);
                // continue with the remaining arguments
                continue;
            }
        }
    }

    gs->auto_upgrade_disabled = disable_auto_upgrade;

#ifdef __linux__
    if (zpn_private_broker_state_is_container_env()) {
        gs->cgroup_version = zpath_system_check_if_cgroup_exists();
        if (gs->cgroup_version) {
            ZPN_LOG(AL_NOTICE, "Cgroups v%d detected!", gs->cgroup_version);
        }
    } else {
        gs->cgroup_version = ZPN_SYSTEM_USE_DEFAULT;
    }
#else
    gs->cgroup_version = ZPN_SYSTEM_USE_DEFAULT;
#endif

	zpn_private_broker_state_set_fohh_threads(pbroker_fohh_thread_count);

    if (-1 == memory_arena_count) {
        memory_arena_count = pbroker_fohh_thread_count + pbroker_non_fohh_thread_count;
    }

#ifdef __linux__
    /*
     * Have only one main thread arena(sbrk done here) + ~30 thread arena(mmap done here). We don't want to aggressively
     * reduce the thread arena as that will lead to thread contention when accessing memory. Withtout the limit on
     * thread arena, we can see a huge growth in the number of thread arenas(say in some peak memory intensive events),
     * but the problem is that the entire arena have to be freed to release the memory back to the kernel.
     *
     * Allocator uses the main arena after it can't spawn any new thread arena. So there is no fear of memory
     * constraints.
     */
     mallopt(M_ARENA_MAX, memory_arena_count);
#endif

    if (fohh_tlv_mconn_window_size >= fohh_tlv_window_size) {
        usage(argv[0], "fohh_win should be greater than fohh_mconn_win\n");
        exit(1);
    }

    if (is_container_env) {
        zpath_app_enable_console_log();
    }

    if (config_daemon) {
        int fd;
        fprintf(stderr, "Daemonizing\n");
        /* Take off... */
        switch (fork()) {
		case 0:
			break;
		case -1:
            ZPN_LOG(AL_ERROR, "fork failed: %s", strerror(errno));
            return ZPN_RESULT_ERR;
		default:
			/* exit interactive session */
			exit(0);
        }
        if(setsid() == -1) {
            ZPN_LOG(AL_ERROR, "setsid failed: %s", strerror(errno));
            return ZPN_RESULT_ERR;
        }
        if ((fd = open("/dev/null", O_RDWR, 0)) != -1) {
            (void)dup2(fd, STDIN_FILENO);
            (void)dup2(fd, STDOUT_FILENO);
            (void)dup2(fd, STDERR_FILENO);
            if (fd > 2)
                (void)close(fd);
        }
    }
    zpn_debug_set(ZPN_DEBUG_SVCP_IDX);
    zdtls_debug = (
                   //ZDTLS_DEBUG_SETUP_BIT |
                   //ZDTLS_DEBUG_SESSION_BIT |
                   //ZDTLS_DEBUG_BIO_BIT |
                   //ZDTLS_DEBUG_PACKET_BIT |
                   0);

    zrdt_debug = (
                   //ZRDT_DEBUG_BIT |
                   //ZRDT_DEBUG_PACKET_BIT |
                   //ZRDT_DEBUG_STREAM_BIT |
                   //ZRDT_DEBUG_CAPTURE_BIT |
                   //ZRDT_DEBUG_PING_BIT |
                   //ZRDT_DEBUG_CONN_BIT |
                   //ZRDT_DEBUG_STATS_BIT |
                   0);

    if (debuglog) {
        zpath_debug |=
                    (ZPATH_DEBUG_CLOUD_CONFIG_BIT) |
                    0;
    }

    struct zpn_broker *broker = NULL;

    /* If we are replicating the global DB, then we pay attention to
     * remote_host configuration for this DB when we initialize the
     * global DB. */
    struct zpath_simple_app_init_params app_params;
    zpath_simple_app_init_params_default(&app_params);
    app_params.instance_name = app_params.role_name = "zpa-service-edge-child";
    app_params.fohh_thread_count = pbroker_fohh_thread_count;
    app_params.fohh_watchdog_s = ZPN_MAX_HEARTBEAT_TIMEOUT;
    app_params.log_filename = logfile;
    app_params.debug_port = debug_port;
    app_params.debuglog = debuglog;
    app_params.personality = ZPATH_APP_PERSONALITY_MINIMUM_MEMORY_FOOTPRINT;
    if (!logfiles) {
        app_params.personality |= ZPATH_APP_PERSONALITY_NO_FILE_LOGGING;
    }
    app_params.load_zpa_cloud_config = 0;

    result = zpath_simple_app_init(&app_params);
    if (result) {
        ZPN_LOG(AL_ERROR, "Error: Could not intialize\n");
        sleep(1);
        exit(1);
    }

    if ( config_fetch_ca_file[0] != '\0' ) {
        fohh_add_ext_trusted_certs_from_file(config_fetch_ca_file);
    }

    zpath_init_cloud_config();
    /*
     * Read proxy bypass config
     */
    if (fohh_proxy_hostname) {
        result = fohh_read_bypass_from_file("proxy-bypass");
        if (result == FOHH_RESULT_NOT_FOUND) {
            /* This is okay- the file not being there doesn't hurt
             * anyone */
        } else if (result == FOHH_RESULT_NO_ERROR) {
            /* This is okay- we read proxy config */
        } else {
            /* This is not okay- there was some error. */
            ZPN_LOG(AL_ERROR, "Could not properly parse proxy-bypass file");
            sleep(1);
            exit(1);
        }
    }

#ifdef __linux__
    ZPN_LOG(AL_NOTICE, "memory_arena_count=%d", memory_arena_count);
#endif

    zpn_event_collection = zpath_event_collection;

    if(argc > 1){
        ZPN_LOG(AL_NOTICE,"Starting Service edge child with \"%s\" argument", cl_arg);
    }else{
        ZPN_LOG(AL_NOTICE,"Starting Service edge child without extra arguments");
    }

    ZPN_LOG(AL_NOTICE, "%s version: %s", SERVICE_EDGE_LOG_NAME, ZPATH_VERSION);

    /* Check if we are enabled for dev certs */
    if (zcrypt_metadata_get_zpa_develop_env(env_val_develop, sizeof(env_val_develop)) == ZCRYPT_RESULT_NO_ERROR) {
        ZPN_LOG(AL_DEBUG, "Environment %s is set to %s", ZCRYPT_ZPA_DEVELOP_ENV, env_val_develop);
        gs->develop_certs_mode = zcrypt_metatdata_get_develop_mode_from_env(env_val_develop);
    } else {
        gs->develop_certs_mode = zcrypt_metadata_develop_mode_unknown;
    }

    /* Check if geoip is disabled */
    if (zcrypt_metadata_get_zpa_disable_geoip_env(env_val_disable_geoip, sizeof(env_val_disable_geoip)) == ZCRYPT_RESULT_NO_ERROR) {
        ZPN_LOG(AL_DEBUG, "Environment %s is set to %s", ZCRYPT_ZPA_DISABLE_GEOIP, env_val_disable_geoip);
        disable_geoip = zcrypt_metatdata_get_disable_geoip_env(env_val_disable_geoip);
    }

    if ((env_oauth = getenv(ENV_ZPA_OAUTH_ENROLLMENT))) {
        if (!strncmp(env_oauth, "TRUE", 4) && !is_oauth_enrollment_disabled()) {
            gs->oauth_enroll = 1;
        }
    }

    gs->zcdns = zcdns_libevent_create(fohh_get_thread_event_base(0),
                                      1,
                                      NULL,
                                      "/etc/resolv.conf",
                                      "/etc/hosts",
                                      log_f,
                                      NULL);

    if (!is_zpn_drmode_enabled()) {
        pbroker_check_last_denied_version();
    } else {
        ZPATH_LOG(AL_INFO, "%s skipping version validation in active dr mode", SERVICE_EDGE_LOG_NAME);
    }

    result = zpn_private_broker_enroll(always_re_enroll, &site_config);
    if (result) {
        ZPN_LOG(AL_ERROR, "Error: %s, Could not enroll %s", zpath_result_string(result), gs->role_name);
        sleep(1);
        exit(1);
    }

    if (zpn_private_broker_site_config_save(&site_config) != ZPATH_RESULT_NO_ERROR) {
        ZPN_LOG(AL_ERROR, "Could not save file for writing offline domain");
        // continue to execute
    }
    update_global_state_from_site_config(&site_config);

    /* Now instantiate the full instance name for this PSE */
    if(strnlen(gs->listener_alt_cloud_name, sizeof(gs->listener_alt_cloud_name))) {
        snprintf_nowarn(gs->instance_full_name, sizeof(gs->instance_full_name), "%"PRId64".%s",
            gs->private_broker_id, gs->listener_alt_cloud_name);
    } else {
        snprintf_nowarn(gs->instance_full_name, sizeof(gs->instance_full_name), "%"PRId64".%s",
            gs->private_broker_id, gs->cfg_key_cloud);
    }

    zthread_init(APP_ROLE, ZPATH_VERSION, gs->cn, stackpath, get_pbroker_additional_debug_logs);

    /* Need to read Organization Name out of certificate. */
    {
        struct zcrypt_cert *cert;
        char org_name[128] = {0};
        cert = zcrypt_cert_read(FILENAME_CERT);

        if (!cert) {
            ZPN_LOG(AL_ERROR, "Could not read cert file %s", FILENAME_CERT);
            sleep(1);
            exit(1);
        }

        if (!fohh_x509_get_on(zcrypt_cert_get_x509(cert), org_name, sizeof(org_name))) {
            zthread_set_org_name(org_name);
        }
        else {
            ZPN_LOG(AL_NOTICE, "Could not get ON from cert file %s", FILENAME_CERT);
            sleep(1);
        }

        zcrypt_cert_free(cert);
    }

    /* FOHH Status Interval related feature flags */
    zpath_config_override_monitor_int(CONFIG_FOHH_STATUS_INTVL_STATS,
                                      &stats_status_interval,
                                      zpn_pbroker_stats_status_interval_config_override_monitor_callback,
                                      STATUS_INTVL_DEFAULT,
                                      gs->private_broker_id,
                                      gs->customer_id,
                                      (int64_t)0);
    zpath_config_override_monitor_int(CONFIG_FOHH_STATUS_INTVL_LOG,
                                      &log_status_interval,
                                      zpn_pbroker_log_status_interval_config_override_monitor_callback,
                                      STATUS_INTVL_DEFAULT,
                                      gs->private_broker_id,
                                      gs->customer_id,
                                      (int64_t)0);
    zpath_config_override_monitor_int(CONFIG_FOHH_STATUS_INTVL_CTL,
                                      &ctl_status_interval,
                                      zpn_pbroker_ctl_status_interval_config_override_monitor_callback,
                                      STATUS_INTVL_DEFAULT,
                                      gs->private_broker_id,
                                      gs->customer_id,
                                      (int64_t)0);
    zpath_config_override_monitor_int(CONFIG_FOHH_STATUS_INTVL_CFG,
                                      &cfg_status_interval,
                                      zpn_pbroker_cfg_status_interval_config_override_monitor_callback,
                                      STATUS_INTVL_DEFAULT,
                                      gs->private_broker_id,
                                      gs->customer_id,
                                      (int64_t)0);
    zpath_config_override_monitor_int(CONFIG_FOHH_STATUS_INTVL_RCFG,
                                      &rcfg_status_interval,
                                      zpn_pbroker_rcfg_status_interval_config_override_monitor_callback,
                                      STATUS_INTVL_DEFAULT,
                                      gs->private_broker_id,
                                      gs->customer_id,
                                      (int64_t)0);
    zpath_config_override_monitor_int(CONFIG_FOHH_STATUS_INTVL_OVD,
                                      &ovd_status_interval,
                                      zpn_pbroker_ovd_status_interval_config_override_monitor_callback,
                                      STATUS_INTVL_DEFAULT,
                                      gs->private_broker_id,
                                      gs->customer_id,
                                      (int64_t)0);
    zpath_config_override_monitor_int(CONFIG_FOHH_STATUS_INTVL_USERDB,
                                      &userdb_status_interval,
                                      zpn_pbroker_userdb_status_interval_config_override_monitor_callback,
                                      STATUS_INTVL_DEFAULT,
                                      gs->private_broker_id,
                                      gs->customer_id,
                                      (int64_t)0);

    zpath_upgrade_get_restart_timestamps(&(gs->last_os_upgrade_time), &(gs->last_sarge_upgrade_time));
    zpath_upgrade_read_stats(&(gs->upgrade_stats));
    gs->platform_version = zvm_vm_type_rh_image_version();

    result = zpn_dr_lib_init(zpath_event_collection, zpn_dr_system_type_private_broker, pbroker_get_raw_dr_stats);
    if (result) {
        ZPN_LOG(AL_ERROR, "Could not init zpn_dr_lib: %s", zpn_result_string(result));
        return result;
    }

    result = zpn_zdx_lib_init(zpn_event_collection);
    if (result) {
        ZPN_LOG(AL_ERROR, "Could not init zdx");
        sleep(1);
        exit(1);
    }

    result = zpn_pcap_lib_init(zpn_event_collection);
    if (result) {
        ZPN_LOG(AL_ERROR, "Could not init zpn_pcap_lib");
        sleep(1);
        exit(1);
    }

    result = zpn_pcap_init();
    if (result) {
        ZPN_LOG(AL_ERROR, "Could not init zpn_pcap");
        sleep(1);
        exit(1);
    }

    result = zpn_zdx_webprobe_lib_init(zpath_event_collection);
    if (result) {
        ZPN_LOG(AL_ERROR, "Unable to init the webprobe rate limit lib");
        sleep(1);
        exit(1);
    }

    result = zpn_zdx_webprobe_rate_limit_init();
    if (result) {
        ZPN_LOG(AL_ERROR, "Unable to init the webprobe rate limit");
        return result;
    }

    result = zpn_rpc_init();
    if (result) {
        ZPN_LOG(AL_ERROR, "Could not register RPCs");
        sleep(1);
        exit(1);
    }

    /* check the firedrill state from site_config.cfg file */
    zpn_private_broker_check_firedrill_mode_on_boot();

    if(is_zpn_drmode_enabled()) {
        ZPN_LOG(AL_NOTICE, "ZPN DR mode is detected!!");
        dr_mode_enabled = 1;
        // Need to initialize Private broker in DR mode
        ZPN_LOG(AL_WARNING, "Disabling auto upgrade in DR mode. current running version(%s)", ZPATH_VERSION);
        gs->auto_upgrade_disabled = 1;
    }

    result = pbroker_dr_interface_init();
    if (result) {
        ZPN_LOG(AL_ERROR, "pbroker_dr_interface_init failed: %s",
                zpath_result_string(result));
        return result;
    }

    /* Init ssl context early during startup */
    result = zpn_private_broker_init_self_to_broker_ssl_ctx(FILENAME_CLOUD_ROOT, FILENAME_CERT, FILENAME_KEY_PRIV);
    if (result) {
        ZPN_LOG(AL_CRITICAL, "broker initialization failed!");
        return ZPN_RESULT_ERR;
    }

    result = zpn_private_broker_init_self_to_private_ssl_ctx(FILENAME_ROOT, FILENAME_CERT, FILENAME_KEY_PRIV);
    if (result) {
        ZPN_LOG(AL_CRITICAL, "private cloud context initialization failed!");
        return ZPN_RESULT_ERR;
    }


    /* Initialize basics from zpath_local. This includes location of
     * certificates, etc, so we can operate much like a normal
     * broker. */
    char zpath_local[1000];
    size_t len;

    /***********************************************************************/
    /* PRODUCTION CODE HERE */
    len = snprintf(zpath_local, sizeof(zpath_local),
                   "{"
                   "    \"zpath_local\" : {"
                   "        \"id\" : 0,"
                   "        \"role\" : \"pbroker\","
                   "        \"sequence\" : 1,"
                   "        \"instance_name\" : \"%ld\","
                   "        \"cloud_name\" : \"%s\","
                   "        \"root_certificate_file\" : \"./cloud.pem\","
                   "        \"public_certificate_file\" : \"./cert.pem\","
                   "        \"private_key_file\" : \"./rsa_key.pem\","
                   "        \"event_log_file\" : \"./%ld.%s.event.log\","
                   "        \"event_log_file_short\" : \"./event.log\","
                   "        \"stats_log_file\" : \"./%ld.%s.stats.log\","
                   "        \"stats_log_file_short\" : \"./stats.log\""
                   "    }"
                   "}",
                   (long)gs->private_broker_id,
                   gs->cfg_key_cloud,
                   (long)gs->private_broker_id,
                   gs->cfg_key_cloud,
                   (long)gs->private_broker_id,
                   gs->cfg_key_cloud);
    if (len >= sizeof(zpath_local)) {
        ZPN_LOG(AL_ERROR, "String too large");
        return ZPATH_RESULT_ERR;
    }

    result = zpath_local_init_with_string(zpath_local);
    if (result) {
        ZPN_LOG(AL_ERROR, "Could not local init using <%s>", zpath_local);
        return ZPATH_RESULT_ERR;
    }

    zthread_record_cpu_time_since_last_heartbeat();

    zdtls_init(zpn_event_collection);
    zrdt_init(zpn_event_collection, pbroker_fohh_thread_count);
    zrdt_set_cloud_environment(zpn_broker_is_dev_environment());

    ZPN_DEBUG_STARTUP("Initializing %s(%"PRId64")...", gs->role_name, gs->private_broker_id);

    result = zpn_private_broker_conn_sni_init();
    if (result != ZPATH_RESULT_NO_ERROR) {
        ZPN_LOG(AL_ERROR, "Unable to init conn SNI");
        return ZPN_RESULT_ERR;
    }

    if (debuglog && dr_mode_enabled) {
          wally_debug =(
                      WALLY_DEBUG_RESULT_BIT |
                      WALLY_DEBUG_TABLE_BIT |
                      WALLY_DEBUG_REGISTRATION_BIT |
                      WALLY_DEBUG_ROW_BIT |
                      WALLY_DEBUG_POSTGRES_BIT |
                      WALLY_DEBUG_FOHH_CLIENT_ROW_BIT |
                      WALLY_DEBUG_POSTGRES_POLL_BIT |
                      WALLY_DEBUG_POSTGRES_FC_BIT |
                      WALLY_DEBUG_POSTGRES_CONN_BIT |
                      WALLY_DEBUG_POSTGRES_EVENT_BIT |
                      WALLY_DEBUG_ROW_DETAIL_BIT |
                      WALLY_DEBUG_WRITE_ROW_BIT |
                      WALLY_DEBUG_POSTGRES_WRITE_BIT |
                      WALLY_DEBUG_TEST_ORIGIN_BIT |
                      WALLY_DEBUG_INTEREST_CB_BIT |
                      0);
    }

    if(gs->redir_alt_cloud_name[0] || gs->listener_alt_cloud_name[0]) {
        /* Now start the recovery timer, just before we initiate the config connection based on redir */
        zpn_private_broker_alt_cloud_monitor_init();
    }

    /* Add debug command very early to be able to clear the alt_cloud cache */
    result = zpath_debug_add_admin_command("clear pse alt_cloud cache",
                          "/pbroker/clear_alt_cloud",
                          zpn_pbroker_clear_alt_cloud_cache,
                          NULL,
                          NULL);
    if (result) {
        ZPN_LOG(AL_ERROR, "Could not register pbroker/clear_alt_cloud debug command. Error: %s", zpn_result_string(result));
        return result;
    }

    gs->private_broker_state = ZPN_CALLOC(sizeof(struct zpn_private_broker_state));

    if (!gs->private_broker_state) {
        ZPN_LOG(AL_CRITICAL, "Unable to initilize private broker state");
        return ZPN_RESULT_ERR;
    }

    /* Set dr running mode to "off" by default */
    strcpy(gs->private_broker_state->dr_running_mode, "off");

    if(!dr_mode_enabled) {
        /* Setup control connection from private broker to site controller */
        result = zpn_private_broker_sitec_control_conn_init(gs->private_broker_state,
                                             gs->private_broker_id,
                                             gs->offline_domain);
        if (result) {
            ZPN_LOG(AL_ERROR, "Private broker sitec control connection init error: %s",
                    zpath_result_string(result));
            return result;
        }
    }

    /* Init wally and control connection, set the ball rolling with private broker state */
    if (!zpn_private_broker_wally_init(gs->private_broker_id,
                                    gs->cfg_key_shard,
                                    pbroker_get_redir_cloud_name(),
                                    NULL,
                                    gs->cn,
                                    NULL,
                                    0,
                                    0,
                                    use_sqlt,
                                    dr_mode_enabled,
                                    zpn_dr_wally_init,
                                    gs->cfg_key_cloud)) {
        ZPN_LOG(AL_ERROR, "Could not initialize config connection: %s", zpn_result_string(result));
        return ZPN_RESULT_ERR;
    }

    result = zpn_private_broker_wally_override_init(gs->private_broker_state,
                                                    pbroker_get_redir_cloud_name(),
                                                    NULL,
                                                    gs->customer_id,
                                                    use_sqlt,
                                                    dr_mode_enabled,
                                                    zpn_dr_wally_init,
                                                    gs->cfg_key_cloud);
    if (result) {
        ZPN_LOG(AL_ERROR, "Could not initialize config override connection: %s", zpn_result_string(result));
        return ZPN_RESULT_ERR;
    }

    /* always keep the below version validation code after conifig/config-override conn initialization */
    if (!dr_mode_enabled) {
        result = pbroker_validate_current_version_prep();
        if(result) {
            return ZPN_RESULT_ERR;
        }

        pbroker_validate_current_version();
    }

    zpath_init_common_config_overrides(gs->private_broker_id, gs->customer_id, DEFAULT_CLIENT_CIPHERSUITE_INDEX);

    zpath_app_init_fohh_cipher_configuration(gs->private_broker_id, gs->customer_id, DEFAULT_CLIENT_CIPHERSUITE_INDEX );
    fohh_ssl_ctx_client_custom_options(gs->self_to_broker_ssl_ctx, FOHH_ECDH_CURVES, FOHH_SET_CUSTOM_CIPHERSUITE_INDEX, VERIFY_PEER);
    fohh_ssl_ctx_client_custom_options(gs->self_to_private_ssl_ctx, FOHH_ECDH_CURVES, FOHH_SET_CUSTOM_CIPHERSUITE_INDEX, VERIFY_PEER);
    if (gs->self_to_broker_dtls_ctx) {
        fohh_ssl_ctx_client_custom_options(gs->self_to_broker_dtls_ctx, FOHH_ECDH_CURVES, FOHH_SET_CUSTOM_CIPHERSUITE_INDEX, VERIFY_PEER);
    }
    fohh_reset_client_cipher_global_ssl_ctx();

    result = zpath_debug_add_read_command("dump cloud config",
                          "/zpn/zpn_dr/dump_dr_config",
                          dump_dr_config_helper,
                          NULL,
                          NULL);
    if (result) {
        ZPN_LOG(AL_ERROR, "Could not register /zpn/zpn_dr/dump_dr_config to debug: %s", zpn_result_string(result));
        return result;
    }

    result = zpath_debug_add_read_command("dump config snapshot time",
                          "/zpn/zpn_dr/get_config_snapshot_time",
                          get_dr_config_snapshot_time_helper,
                          NULL,
                          NULL);
    if (result) {
        ZPN_LOG(AL_ERROR, "Could not register /zpn/zpn_dr/get_config_snapshot_time to debug: %s", zpn_result_string(result));
        return result;
    }

#ifdef ZPN_DR_DEBUG_CMDS_ENABLE
    result = zpath_debug_add_admin_command("set PB in DR mode",
                          "/zpn/zpn_dr/activate_drmode",
                          zpn_dr_mode_activate,
                          NULL,
                          (void *)gs->zthread);
    if (result) {
        ZPN_LOG(AL_ERROR, "Could not register /zpn/zpn_dr/activate_drmode to debug: %s", zpn_result_string(result));
        return result;
    }
#endif

    result = zpath_debug_add_write_command("Query DNS DR activation record and verify signature",
                          "/zpn/zpn_dr/resolve_and_verify_dr_activation_txt_record",
                          zpn_dr_query_txt_record,
                          NULL,
                          NULL);
    if (result) {
        ZPN_LOG(AL_ERROR, "Could not register /zpn/zpn_dr/resolve_and_verify_dr_activation_txt_record to debug: %s", zpn_result_string(result));
        return result;
    }

    result = zpath_debug_add_read_command("Get DR mode status",
                          "/zpn/zpn_dr/drmode_status",
                          zpn_dr_get_current_status,
                          NULL,
                          NULL);
    if (result) {
        ZPN_LOG(AL_ERROR, "Could not register /zpn/zpn_dr/drmode_status to debug: %s", zpn_result_string(result));
        return result;
    }

    result = zpath_debug_add_admin_command("Force end DR mode",
                          "/zpn/zpn_dr/force_deactivate_drmode",
                          zpn_dr_force_deactivate_drmode,
                          NULL,
                          "instance_gid", "Private Broker GID",
                          NULL);
    if (result) {
        ZPN_LOG(AL_ERROR, "Could not register /zpn/zpn_dr/force_deactivate_drmode to debug: %s", zpn_result_string(result));
        return result;
    }

    result = zpath_debug_add_read_command("Get DR mode status",
                                     "/zpn/zpn_dr/stats",
                                     zpn_dr_get_stats,
                                     NULL,
                                     NULL);
    if (result) {
        ZPN_LOG(AL_ERROR, "Could not register /zpn/zpn_dr/stats to debug: %s", zpn_result_string(result));
        return result;
    }

    result = zpath_debug_add_read_command("Get all the config snapshots",
                                     "/zpn/zpn_dr/config_snapshots/list",
                                     zpn_dr_pse_list_config_snapshots,
                                     NULL,
                                     NULL);
    if (result) {
        ZPN_LOG(AL_ERROR, "Could not register /zpn/zpn_dr/config_snapshots/list to debug: %s", zpn_result_string(result));
        return result;
    }

    result = zpath_debug_add_read_command("Get all the binary snapshots",
                                     "/zpn/zpn_dr/binary_snapshots/list",
                                     zpn_dr_pse_list_binary_snapshots,
                                     NULL,
                                     NULL);
    if (result) {
        ZPN_LOG(AL_ERROR, "Could not register /zpn/zpn_dr/binary_snapshots/list to debug: %s", zpn_result_string(result));
        return result;
    }

#ifdef ZPN_DR_SNAPSHOT_INSTALL_CMDS_ENABLE
    result = zpath_debug_add_admin_command("Install config snapshot: Config snapshot directory (e.g. /.../config_snapshots/config-2022-09-19, format - (/.../config_snapshots/config-YYYY-MM-DD)",
                                     "/zpn/zpn_dr/config_snapshots/install",
                                     zpn_dr_pse_install_config_snapshot,
                                     NULL,
                                     "snapshot_dir", "Config snapshot directory (e.g. /.../config_snapshots/config-2022-09-19, format - (/.../config_snapshots/config-YYYY-MM-DD)",
                                     NULL);
    if (result) {
        ZPN_LOG(AL_ERROR, "Could not register /zpn/zpn_dr/config_snapshots/install to debug: %s", zpn_result_string(result));
        return result;
    }

    result = zpath_debug_add_admin_command("Install binary snapshot: Binary snapshot directory (e.g. /.../binary_snapshots/binary-2022-10-05-07-40_ver_22.284.1-678, format - (/.../binary_snapshots/binary-YYYY-MM-DD-HH-MM_ver_<version string>)",
                                     "/zpn/zpn_dr/binary_snapshots/install",
                                     zpn_dr_pse_install_binary_snapshot,
                                     NULL,
                                     "snapshot_dir", "Binary snapshot directory (e.g. /.../binary_snapshots/binary-2022-10-05-07-40_ver_22.284.1-678, format - (/.../binary_snapshots/binary-YYYY-MM-DD-HH-MM_ver_<version string>)",
                                     NULL);
    if (result) {
        ZPN_LOG(AL_ERROR, "Could not register /zpn/zpn_dr/binary_snapshots/install to debug: %s", zpn_result_string(result));
        return result;
    }
#endif
    result = zpath_debug_add_write_command("Generate snapshot management script",
                                     "/zpn/zpn_dr/snapshot/generate_script",
                                     zpn_dr_pse_snapshot_mgmnt_script_generate,
                                     NULL,
                                     NULL);
    if (result) {
        ZPN_LOG(AL_ERROR, "CUSTOMER_DR: Could not register /zpn/zpn_dr/snapshot/generate_script to debug: %s", zpn_result_string(result));
        return result;
    }

    result = zpath_debug_add_write_command("Query DNS DR activation record and display response",
                          "/zpn/zpn_dr/resolve_dr_domain",
                          zpn_dr_resolve_dr_domain,
                          NULL,
                          "record_type", "DNS record type to query(A/TXT)",
                          NULL);
    if (result) {
        ZPN_LOG(AL_ERROR, "Could not register /zpn/zpn_dr/resolve_dr_domain to debug: %s", zpn_result_string(result));
        return result;
    }

    result = zpath_debug_add_read_command("Get PSE resiliency status",
                          "/pbroker/resiliency/status",
                          zpn_pse_resiliency_status,
                          NULL,
                          NULL);
    if (result) {
        ZPN_LOG(AL_ERROR, "Could not register /pbroker/resiliency/status  to debug: %s", zpn_result_string(result));
        return result;
    }

    result = zpath_debug_add_read_command("Get PSE resiliency stats",
                          "/pbroker/resiliency/stats",
                          zpn_pse_resiliency_stats_show,
                          NULL,
                          NULL);
    if (result) {
        ZPN_LOG(AL_ERROR, "Could not register /pbroker/resiliency/stats  to debug: %s", zpn_result_string(result));
        return result;
    }

    result = zpath_debug_add_write_command("Unit Test: Testing for resolution",
                                "/pbroker/dns/fohh_resolve/query",
                                zpn_pse_dns_resolution_query_debug,
                                NULL,
                                "domain_name", "fqdn",
                                NULL);
    if (result) {
        ZPN_LOG(AL_ERROR, "Could not register /pbroker/dns/fohh_resolve/query to debug: %s", zpn_result_string(result));
    }

    result = zpath_debug_add_write_command("Unit Test: Testing for resolution",
                                "/pbroker/dns/fohh_resolve/remove",
                                zpn_pse_dns_resolution_remove_debug,
                                NULL,
                                "domain_name", "fqdn",
                                NULL);
    if (result) {
        ZPN_LOG(AL_ERROR, "Could not register /pbroker/dns/fohh_resolve/remove to debug: %s", zpn_result_string(result));
    }

    result = zpath_debug_add_write_command("Set PSE resiliency reauth interval value",
                          "/pbroker/resiliency/set_auth_interval",
                          zpn_pse_resiliency_set_reauth_interval,
                          NULL,
                          "value", "Time in seconds for extending reauthorization expiry",
                          NULL);
    if (result) {
        ZPN_LOG(AL_ERROR, "Could not register /pbroker/resiliency/set_auth_interval to debug: %s", zpn_result_string(result));
        return result;
    }

    result = zpath_debug_add_read_command("Get PB Client creation enabled status",
                          "/pbroker/pbclient_create_status",
                          zpn_pse_pbclient_create_status,
                          NULL,
                          NULL);
    if (result) {
        ZPN_LOG(AL_ERROR, "Could not register /pbroker/pbclient_create_status to debug: %s", zpn_result_string(result));
        return result;
    }

    result = zpath_debug_add_read_command("Get PB Client debug fohh stats enabled status",
                          "/pbroker/stats/pbclient_debug/fohh_debug/status",
                          zpn_pse_pbclient_fohh_debug_status,
                          NULL,
                          NULL);
    if (result) {
        ZPN_LOG(AL_ERROR, "Could not register to /pbroker/stats/pbclient_debug/fohh_debug/status debug: %s", zpn_result_string(result));
        return result;
    }

    result = zpath_debug_add_write_command("Enable PB Client debug fohh stats",
                          "/pbroker/stats/pbclient_debug/fohh_debug/enable",
                          zpn_pse_pbclient_fohh_debug_enable,
                          NULL,
                          NULL);
    if (result) {
        ZPN_LOG(AL_ERROR, "Could not register /pbroker/stats/pbclient_debug/fohh_debug/enable to debug: %s", zpn_result_string(result));
        return result;
    }

    result = zpath_debug_add_write_command("Disable PB Client debug fohh stats",
                          "/pbroker/stats/pbclient_debug/fohh_debug/disable",
                          zpn_pse_pbclient_fohh_debug_disable,
                          NULL,
                          NULL);
    if (result) {
        ZPN_LOG(AL_ERROR, "Could not register /pbroker/stats/pbclient_debug/fohh_debug/disable to debug: %s", zpn_result_string(result));
        return result;
    }
    result = zpath_debug_add_read_command("Display DR critical configurations",
                          "/zpn/zpn_dr/show_configuration",
                          zpn_dr_show_config,
                          NULL,
                          NULL);
    if (result) {
        ZPN_LOG(AL_ERROR, "Could not register /zpn/zpn_dr/show_configuration to debug: %s", zpn_result_string(result));
        return result;
    }

    result = zpath_debug_add_read_command("Display DR config json files stats",
                          "/zpn/zpn_dr/config_json_stats",
                          zpn_dr_show_config_json_stats,
                          NULL,
                          NULL);
    if (result) {
        ZPN_LOG(AL_ERROR, "Could not register /zpn/zpn_dr/config_json_stats to debug: %s", zpn_result_string(result));
        return result;
    }

    result = zpath_debug_add_read_command("Display list of apps configured for DR support",
                          "/zpn/zpn_dr/show_dr_apps",
                          zpn_dr_dump_dr_apps,
                          NULL,
                          NULL);
    if (result) {
        ZPN_LOG(AL_ERROR, "Could not register /zpn/zpn_dr/show_dr_apps to debug: %s", zpn_result_string(result));
        return result;
    }

    if (!dr_mode_enabled) {
        result = zpath_debug_add_read_command("dump the state of private broker version control",
                                  "/pbroker/version/state/dump",
                                  pbroker_version_state_dump,
                                  NULL,
                                  NULL);
        if (result) {
            ZPN_LOG(AL_ERROR, "Could not register /pbroker/version/state/dump to debug: %s", zpn_result_string(result));
            return result;
        }
    }

    if (dr_mode_enabled) {
        /* Register the expected argo objects before we start parsing the DR config files */
        dr_register_argo_objects();

        /* Parse dr_wally config file (row by row for each table) and statically load JSON row object into appropriate tables */
        for (int category_idx = zpn_dr_file_category_unknown; category_idx <= zpn_dr_file_category_other; category_idx++) {
            const char* filename = zpn_dr_lib_get_cfg_file_name_for_category(category_idx);
            if (strcmp(filename, "override_conf.json") == 0) {
                /* Parse dr_ovd_wally config file (row by row for each table) and statically load JSON row object into appropriate tables */
                load_dr_config_to_wally_origin( filename,
                                         gs->private_broker_state->wally_ovd_test_origin,
                                         NULL);
            } else {
                load_dr_config_to_wally_origin( filename,
                                        gs->private_broker_state->wally_test_origin,
                                        NULL);
            }
        }

        /* Set property of test origin to automatically respond the dr_wally requests */
        wally_test_origin_autorespond(gs->private_broker_state->wally_test_origin, 1);
        /* Set property of test origin to automatically respond the dr_ovd_wally requests */
        wally_test_origin_autorespond(gs->private_broker_state->wally_ovd_test_origin, 1);

        if(debuglog) {
            dump_test_origin(gs->private_broker_state->wally_test_origin);
            dump_test_origin(gs->private_broker_state->wally_ovd_test_origin);
        }
    }

    private_broker_cfg.instance_type = ZPN_INSTANCE_TYPE_PRIVATE_BROKER;
    private_broker_cfg.private_broker.broker_id = gs->private_broker_id;
    private_broker_cfg.private_broker.customer_id = gs->customer_id;
    private_broker_cfg.private_broker.cn = gs->cn;
    private_broker_cfg.private_broker.wally = gs->private_broker_state->wally;
    private_broker_cfg.private_broker.gwally = gs->private_broker_state->wally_ovd;
    private_broker_cfg.private_broker.pkey_mem = gs->cfg_pkey;
    private_broker_cfg.private_broker.cloud_name = gs->cfg_key_cloud;
    private_broker_cfg.private_broker.alt_cloud_name = gs->listener_alt_cloud_name;
    private_broker_cfg.private_broker.alt_cloud_feature_state = &gs->alt_cloud_feature_state;
    private_broker_cfg.private_broker.alt_cloud_feature_state_initialised = &gs->alt_cloud_feature_state_initialised;

    // FIXME: IPv4 INADDR_ANY
    private_broker_cfg.private_broker.listen_ips[0].length = 4;
    bzero(private_broker_cfg.private_broker.listen_ips[0].address, sizeof(private_broker_cfg.private_broker.listen_ips[0].address));
    private_broker_cfg.private_broker.listen_ips[0].netmask = 0;
    private_broker_cfg.private_broker.listen_ips_count = 1;
    private_broker_cfg.private_broker.use_sqlt = use_sqlt;

    zpath_shard_wally[gs->cfg_key_shard] = gs->private_broker_state->wally;

    /* Init private broker data connection handler callbacks */
    result = zpn_pbroker_set_data_conn_handler_cb(&private_broker_cfg, pbroker_broker_conns_init);
    if (result) {
        ZPN_LOG(AL_CRITICAL, "Cannot set private broker data connection handlers");
        return ZPN_RESULT_ERR;
    }

    if (!zpn_private_broker_get_self_to_broker_ssl_ctx()) {
        ZPN_LOG(AL_CRITICAL, "broker get self to broker ssl ctx failed!");
        return ZPN_RESULT_ERR;
    }

    /* Initialize our library before other scim components */
    char userdb_wally_host[ARGO_MAX_NAME_LENGTH];
    snprintf(userdb_wally_host, sizeof(userdb_wally_host), "%s.%s", ZPN_PRIVATE_BROKER_TO_PUBLIC_BROKER_DNS_NAME, pbroker_get_redir_cloud_name());

    char offline_userdb_wally_host[ARGO_MAX_NAME_LENGTH];
    char offline_sni_userdb_tmpl[PRIVATE_BROKER_SNI_DOMAIN_LEN];
    const char* offline_domain = zpn_private_broker_get_offline_domain();
    int to_sitec = zpn_private_broker_site_is_sitec_eligible(offline_domain);

    snprintf_nowarn(offline_userdb_wally_host, sizeof(offline_userdb_wally_host), "%s.%s",
            ZPN_PRIVATE_BROKER_TO_SITE_CONTROLLER_DNS_NAME, offline_domain);
    snprintf_nowarn(offline_sni_userdb_tmpl, sizeof(offline_sni_userdb_tmpl), "%ld.%%ld.%%ld.pbuserdb.%s", (long) gs->private_broker_id, offline_domain);

    result = zpath_et_wally_userdb_init_using_ctx(
                                        dbhost,
                                        to_sitec ? offline_userdb_wally_host : userdb_wally_host,
                                        to_sitec ? offline_sni_userdb_tmpl : pb_sni_customer_domain_userdb_tmpl,
                                        0,
                                        use_sqlt,
                                        1,
                                        0,
                                        0,
                                        zpn_private_broker_userdb_conn_cb,
                                        to_sitec ? (char*)offline_domain : pbroker_get_redir_cloud_name(),
                                        gs->cfg_key_cloud,
                                        to_sitec ? gs->self_to_private_ssl_ctx : gs->self_to_broker_ssl_ctx,
                                        zpn_pbroker_on_userdb_client_created,
                                        to_sitec);
    if (result) {
        ZPN_LOG(AL_ERROR, "Could not initialize wally userdb library: %s", zpn_result_string(result));
        return result;
    }

    result = zpn_pbroker_pse_cfg_override_register();
    if (result) {
        ZPN_LOG(AL_ERROR, "Unable to register zpn_pbroker_pse_cfg_override_register, err: %s", zpn_result_string(result));
        return result;
    }

    custom_app_thread_use = zpath_config_override_get_config_int(CONFIG_FEATURE_PSE_CUSTOM_APP_THREAD,
                                                                &custom_app_thread_use,
                                                                CONFIG_FEATURE_PSE_CUSTOM_APP_THREAD_DEFAULT,
                                                                (int64_t)private_broker_cfg.private_broker.broker_id,
                                                                (int64_t)private_broker_cfg.private_broker.customer_id,
                                                                (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                                (int64_t)0);

    if (custom_app_thread_use == LIBEVENT_APP_THREAD_FEATURE_ENABLED) {
        use_custom_app_thread = 1;
        ZPN_LOG(AL_NOTICE, "Using custom app threads");
    } else {
        use_custom_app_thread = 0;
        ZPN_LOG(AL_NOTICE, "Using default app threads");
    }

    zpath_config_override_monitor_int(CONFIG_FEATURE_PSE_CUSTOM_APP_THREAD,
                                      &custom_app_thread_use,
                                      pse_app_thread_monitor_callback,
                                      CONFIG_FEATURE_PSE_CUSTOM_APP_THREAD_DEFAULT,
                                      (int64_t)private_broker_cfg.private_broker.broker_id,
                                      (int64_t)private_broker_cfg.private_broker.customer_id,
                                      (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                      (int64_t)0);

    broker = zpn_broker_init(pbroker_fohh_thread_count, gs->zcdns, &private_broker_cfg, logfiles ? "." : NULL, 0, 0);
    if (!broker) {
        ZPN_LOG(AL_CRITICAL, "broker initialization failed!");
        return ZPN_RESULT_ERR;
    }
    zpath_shard_wally_static[gs->cfg_key_shard] = gs->private_broker_state->static_wally;

    result = zpn_dr_lib_cfg_override_register(zpath_event_collection, zpn_dr_system_type_private_broker);
    if (result) {
        ZPN_DR_LOG(AL_ERROR, "zpn_dr_lib_cfg_override_register failed: %s",
                zpath_result_string(result));
        return result;
    }

    zpath_config_override_monitor_int(CONFIG_FOHH_LOG_MAX_IN_FLIGHT,
                                      &fohh_log_max_logs_in_flight,
                                      NULL,
                                      FOHH_LOG_MAX_LOGS_IN_FLIGHT_DEFAULT,
                                      (int64_t)private_broker_cfg.private_broker.broker_id,
                                      (int64_t)private_broker_cfg.private_broker.customer_id,
                                      (int64_t)1,
                                      (int64_t)0);
    /* Read argo logging memory threshold from config override and update */
    zpath_config_override_monitor_int(ARGO_MEM_THRESHOLD_PERCENTAGE_PSE,
                                      &argo_logging_threshold_percentage,
                                      argo_logging_threshold_config_override_monitor_callback,
                                      ARGO_MEM_THRESHOLD_PERCENTAGE_PSE_DEFAULT,
                                      (int64_t)private_broker_cfg.private_broker.broker_id,
                                      (int64_t)private_broker_cfg.private_broker.pb_group_id,
                                      (int64_t)private_broker_cfg.private_broker.customer_id,
                                      (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                      (int64_t)0);

    result = zthread_create(&thread_site,
                            pbroker_site_thread,
                            NULL,
                            "pbroker_site_thread",
                            60,
                            16*1024*1024,
                            60*1000*1000,
                            NULL);
    if (result) {
        ZPN_LOG(AL_ERROR, "Could not create pbroker_site_thread");
        return result;
    }

    use_libevent_priority = zpath_config_override_get_config_int(CONFIG_FEATURE_PSE_LIBEVENT_PRIORITY,
                                                        &use_libevent_priority,
                                                        CONFIG_FEATURE_PSE_LIBEVENT_PRIORITY_DEFAULT,
                                                        (int64_t)private_broker_cfg.private_broker.broker_id,
                                                        (int64_t)private_broker_cfg.private_broker.customer_id,
                                                        (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                        (int64_t)0);

    if (use_libevent_priority == LIBEVENT_APP_THREAD_FEATURE_ENABLED) {
        zevent_use_libevent_priority = 1;
        ZPN_LOG(AL_NOTICE, "Using libevent priority");
    } else {
        zevent_use_libevent_priority = 0;
        ZPN_LOG(AL_NOTICE, "Using zevent priority");
    }

    zpath_config_override_monitor_int(CONFIG_FEATURE_PSE_LIBEVENT_PRIORITY,
                                      &use_libevent_priority,
                                      pse_libevent_priority_monitor_callback,
                                      CONFIG_FEATURE_PSE_LIBEVENT_PRIORITY_DEFAULT,
                                      (int64_t)private_broker_cfg.private_broker.broker_id,
                                      (int64_t)private_broker_cfg.private_broker.customer_id,
                                      (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                      (int64_t)0);

    g_config_init_done = 1;

    result = zpn_zdx_features_init(zpn_zdx_system_type_pbroker, &zpn_private_broker_state_get_system_mem_usage, ZPN_BROKER_GET_GID(), ZPN_BROKER_GET_GROUP_GID(),config_component_pbroker);
    if (result) {
        ZPN_LOG(AL_CRITICAL, "zdx probe feature init failed");
        return result;
    }

    result = zpn_zdx_rate_limit_init(zpn_zdx_system_type_pbroker, ZPN_BROKER_GET_GID(), ZPN_BROKER_GET_GROUP_GID(), config_component_pbroker);
    if (result) {
        ZPN_LOG(AL_CRITICAL, "zdx rate limit init failed");
        return result;
    }

    result = zhealth_probe_lib_thread_init(NULL);
    if (result) {
        ZPN_LOG(AL_CRITICAL, "zdx probe lib thread initialization failed");
        return result;
    }

    result = zhealth_probe_lib_init(NULL, zpath_event_collection, zpn_zdx_zhealth_probe_lib_config_get_socket_engine_cb, zhealth_system_type_pbroker, ZPN_BROKER_GET_GID(), ZPN_BROKER_GET_GROUP_GID(), 0);
    if (result) {
        ZPN_LOG(AL_CRITICAL, "zdx probe lib initialization failed");
        return result;
    }

    result = zhealth_probe_udp_init(0, zhealth_system_type_pbroker);
    if (result) {
        ZPN_LOG(AL_CRITICAL, "zdx probe udp initialization failed");
        return result;
    }

    result = zpn_zdx_mtr_init(NULL, zpath_event_collection);
    if (result) {
        ZPN_LOG(AL_CRITICAL, "zdx mtr init failed");
        return result;
    }

    result = zpn_zdx_probes_init(zpn_pbroker_probe_combiner_send_cb, NULL);
    if (result) {
        ZPN_LOG(AL_ERROR, "Unable to init zpn_zdx_probes");
        return result;
    }

    if (!dr_mode_enabled) {
        /* Setup control connection from private broker to public broker */
        result = zpn_private_broker_control_conn_init(gs->private_broker_state,
                                             gs->private_broker_id,
                                             gs->dom,
                                             pbroker_get_redir_cloud_name(),
                                             NULL,
                                             NULL,
                                             gs->cfg_key_cloud);
        if (result) {
            ZPN_LOG(AL_ERROR, "Private broker control connection init error: %s",
                    zpath_result_string(result));
            return result;
        }
    }

    zpn_private_broker_client_connection_monitor_cfg_ovd_init( ZPN_BROKER_GET_GID(),
                                                               ZPN_BROKER_GET_GROUP_GID(),
                                                               gs->customer_id);

    if (zpn_system_get_memory_usage_info(&total_memory, &memfree_abs_mem, &total_swap_memory,
                                            &swapfree_abs_mem, &system_used_abs_mem, &process_used_abs_mem) == ZPN_RESULT_ERR) {
            ZPN_LOG(AL_WARNING, "Could not check for physical memory.. Continuing without minimum memory check");
    }else{
        min_mem_config_val = zpath_config_override_get_config_int(CONFIG_FEATURE_PSE_MIN_MEMORY_KB,
                                          &min_mem_config_val,
                                          CONFIG_FEATURE_PSE_MIN_MEMORY_KB_DEFAULT,
                                          (int64_t)private_broker_cfg.private_broker.broker_id,
                                          (int64_t)private_broker_cfg.private_broker.pb_group_id,
                                          (int64_t)private_broker_cfg.private_broker.customer_id,
                                          (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                          (int64_t)0);
        if(total_memory == 0){
            ZPN_LOG(AL_WARNING, "Could not check for physical memory.. Continuing without minimum memory check");
        } else if(min_mem_config_val > total_memory){
            ZPN_LOG(AL_ERROR, "System does not have enough physical memory to run zpa-service-edge-child Required:%"PRIu64"mB Actual:%"PRIu64"... Exiting!", min_mem_config_val/1024, total_memory/1024);
            sleep(1);
            exit(0);
        } else{
            ZPN_LOG(AL_NOTICE, "Physical memory check -- Passed. Required:%"PRIu64"mB Actual:%"PRIu64"mB", min_mem_config_val/1024, total_memory/1024);
        }
    }

    pbroker_state_check_capabilities();

    /* private broker WAF inspection tables init */
    result = zpn_inspection_application_init(private_broker_cfg.private_broker.wally, ZPN_BROKER_GET_GID(), 0 /* no fully load */, 0, NULL);
    if (result) {
        ZPN_LOG(AL_ERROR, "Could not init zpn_inspection_application table");
        return result;
    }

    char broker_name[256];
    snprintf(broker_name, sizeof(broker_name), "%s.%s", ZPN_PRIVATE_BROKER_TO_PUBLIC_LOGGING_BROKER_DNS_NAME, pbroker_get_redir_cloud_name());

    char offline_broker_name[256];
    char offline_sni_name[PRIVATE_BROKER_SNI_DOMAIN_LEN];
    offline_domain = zpn_private_broker_get_offline_domain();
    to_sitec = zpn_private_broker_site_is_sitec_eligible(offline_domain);

    snprintf(offline_broker_name, sizeof(offline_broker_name),
            "%s.%s", ZPN_PRIVATE_BROKER_TO_SITE_CONTROLLER_DNS_NAME, offline_domain);
    snprintf(offline_sni_name, sizeof(offline_sni_name),
            "%ld.pblog.%s", (long) gs->private_broker_id, offline_domain);

    const char* remote_host_name = to_sitec ? offline_broker_name : broker_name;
    const char* sni_service_name = to_sitec ? offline_sni_name : pb_sni_customer_domain_log;
    const char* sni_suffix = to_sitec ? offline_domain : pbroker_get_redir_cloud_name();
    uint16_t service_port = to_sitec ? ZPN_PRIVATE_BROKER_TO_SITE_CONTROLLER_PORT : ZPN_PRIVATE_BROKER_TO_PUBLIC_BROKER_PORT;
    SSL_CTX* ssl_ctx = to_sitec ? gs->self_to_private_ssl_ctx : gs->self_to_broker_ssl_ctx;

    if (!dr_mode_enabled) {

        result = fohh_log_send(argo_log_get_name(zpn_transaction_collection),   //reader_name,
                               argo_log_get_name(zpn_transaction_collection),   //argo_collection_name,
                               remote_host_name,                                //domain_name,
                               sni_service_name,                                //sni_service_name
                               sni_suffix,                                      //sni_suffix
                               NULL,                                            //topic.
                               htons(service_port),                             //service_port_he,
                               ssl_ctx, 0, 0,                                   //ssl_ctx
                               pbroker_log_tx_conn_cb);                         //custom_log_tx_conn_cb
        if (result) {
            ZPN_LOG(AL_ERROR, "Failed to set up transaction log transmit: %s", zpath_result_string(result));
            return result;
        }

        gs->private_broker_state->transaction_log_channel = fohh_get_log_handle(zpn_transaction_collection);
        fohh_connection_set_default_sni(gs->private_broker_state->transaction_log_channel,
                                        private_broker_cfg.private_broker.cloud_name);

        zpn_private_broker_site_register_fohh(gs->private_broker_state->transaction_log_channel, "pblog.transaction",
                broker_name, pb_sni_customer_domain_log, pbroker_get_redir_cloud_name(), 0, to_sitec);
        fohh_connection_monitor_sanity(gs->private_broker_state->transaction_log_channel,
                zpn_private_broker_fohh_connection_sanity_callback_with_lock,
                ZPN_PRIVATE_BROKER_ALT_CLOUD_CONN_MAX_INIT_TIME_S);
        /*
         * Set status interval address, which has the interval in seconds for sending fohh_status_request messages by connected broker.
         * Default status interval is 1 second for sending fohh_status_request messages.
         */
        fohh_set_status_interval(gs->private_broker_state->transaction_log_channel, &(gs->pblog_txn_status_interval));

        result = fohh_log_send(argo_log_get_name(zpn_auth_collection),          //reader_name,
                               argo_log_get_name(zpn_auth_collection),          //argo_collection_name,
                               remote_host_name,                                //domain_name,
                               sni_service_name,                                //sni_service_name
                               sni_suffix,                                      //sni_suffix
                               NULL,                                            //topic.
                               htons(service_port),                             //service_port_he,
                               ssl_ctx, 0, 0,                                   //ssl_ctx
                               pbroker_log_tx_conn_cb);                         //custom_log_tx_conn_cb
        if (result) {
            ZPN_LOG(AL_ERROR, "Failed to set up authn log transmit: %s", zpath_result_string(result));
            return result;
        }

        gs->private_broker_state->auth_log_channel = fohh_get_log_handle(zpn_auth_collection);
        fohh_connection_set_default_sni(gs->private_broker_state->auth_log_channel,
                                        private_broker_cfg.private_broker.cloud_name);

        zpn_private_broker_site_register_fohh(gs->private_broker_state->auth_log_channel, "pblog.auth",
                broker_name, pb_sni_customer_domain_log, pbroker_get_redir_cloud_name(), 0, to_sitec);
        fohh_connection_monitor_sanity(gs->private_broker_state->auth_log_channel,
                zpn_private_broker_fohh_connection_sanity_callback_with_lock,
                ZPN_PRIVATE_BROKER_ALT_CLOUD_CONN_MAX_INIT_TIME_S);
        /*
         * Set status interval address, which has the interval in seconds for sending fohh_status_request messages by connected broker.
         * Default status interval is 1 second for sending fohh_status_request messages.
         */
        fohh_set_status_interval(gs->private_broker_state->auth_log_channel, &(gs->pblog_auth_status_interval));

        result = fohh_log_send(argo_log_get_name(zpn_ast_auth_collection),      //reader_name,
                               argo_log_get_name(zpn_ast_auth_collection),      //argo_collection_name,
                               remote_host_name,                                //domain_name,
                               sni_service_name,                                //sni_service_name
                               sni_suffix,                                      //sni_suffix
                               NULL,                                            //topic.
                               htons(service_port),                             //service_port_he,
                               ssl_ctx, 0, 0,                                   //ssl_ctx
                               pbroker_log_tx_conn_cb);                         //custom_log_tx_conn_cb
        if (result) {
            ZPN_LOG(AL_ERROR, "Failed to set up ast auth log transmit: %s", zpath_result_string(result));
            return result;
        }

        gs->private_broker_state->ast_auth_log_channel = fohh_get_log_handle(zpn_ast_auth_collection);
        fohh_connection_set_default_sni(gs->private_broker_state->ast_auth_log_channel,
                                        private_broker_cfg.private_broker.cloud_name);

        zpn_private_broker_site_register_fohh(gs->private_broker_state->ast_auth_log_channel, "pblog.ast_auth",
                broker_name, pb_sni_customer_domain_log, pbroker_get_redir_cloud_name(), 0, to_sitec);
        fohh_connection_monitor_sanity(gs->private_broker_state->ast_auth_log_channel,
                zpn_private_broker_fohh_connection_sanity_callback_with_lock,
                ZPN_PRIVATE_BROKER_ALT_CLOUD_CONN_MAX_INIT_TIME_S);
        /*
         * Set status interval address, which has the interval in seconds for sending fohh_status_request messages by connected broker.
         * Default status interval is 1 second for sending fohh_status_request messages.
         */
        fohh_set_status_interval(gs->private_broker_state->ast_auth_log_channel, &(gs->pblog_ast_auth_status_interval));

        result = fohh_log_send(argo_log_get_name(zpn_event_collection),         //reader_name,
                               argo_log_get_name(zpn_event_collection),         //argo_collection_name,
                               remote_host_name,                                //domain_name,
                               sni_service_name,                                //sni_service_name
                               sni_suffix,                                      //sni_suffix
                               NULL,                                            //topic.
                               htons(service_port),                             //service_port_he,
                               ssl_ctx, 0, 0,                                   //ssl_ctx
                               pbroker_log_tx_conn_cb);                         //custom_log_tx_conn_cb
        if (result) {
            ZPN_LOG(AL_ERROR, "Failed to set up ast auth log transmit: %s", zpath_result_string(result));
            return result;
        }

        gs->private_broker_state->event_log_channel = fohh_get_log_handle(zpn_event_collection);
        fohh_connection_set_default_sni(gs->private_broker_state->event_log_channel,
                                        private_broker_cfg.private_broker.cloud_name);

        zpn_private_broker_site_register_fohh(gs->private_broker_state->event_log_channel, "pblog.event",
                broker_name, pb_sni_customer_domain_log, pbroker_get_redir_cloud_name(), 0, to_sitec);
        fohh_connection_monitor_sanity(gs->private_broker_state->event_log_channel,
                zpn_private_broker_fohh_connection_sanity_callback_with_lock,
                ZPN_PRIVATE_BROKER_ALT_CLOUD_CONN_MAX_INIT_TIME_S);
        /*
         * Set status interval address, which has the interval in seconds for sending fohh_status_request messages by connected broker.
         * Default status interval is 1 second for sending fohh_status_request messages.
         */
        fohh_set_status_interval(gs->private_broker_state->event_log_channel, &(gs->pblog_event_status_interval));

        result = fohh_log_send(argo_log_get_name(zpn_dns_collection),           //reader_name,
                               argo_log_get_name(zpn_dns_collection),           //argo_collection_name,
                               remote_host_name,                                //domain_name,
                               sni_service_name,                                //sni_service_name
                               sni_suffix,                                      //sni_suffix
                               NULL,                                            //topic.
                               htons(service_port),                             //service_port_he,
                               ssl_ctx, 0, 0,                                   //ssl_ctx
                               pbroker_log_tx_conn_cb);                         //custom_log_tx_conn_cb
        if (result) {
            ZPN_LOG(AL_ERROR, "Failed to set up dns log transmit: %s", zpath_result_string(result));
            return result;
        }

        gs->private_broker_state->dns_log_channel = fohh_get_log_handle(zpn_dns_collection);
        fohh_connection_set_default_sni(gs->private_broker_state->dns_log_channel,
                                        private_broker_cfg.private_broker.cloud_name);

        zpn_private_broker_site_register_fohh(gs->private_broker_state->dns_log_channel, "pblog.dns",
                broker_name, pb_sni_customer_domain_log, pbroker_get_redir_cloud_name(), 0, to_sitec);
        fohh_connection_monitor_sanity(gs->private_broker_state->dns_log_channel,
                zpn_private_broker_fohh_connection_sanity_callback_with_lock,
                ZPN_PRIVATE_BROKER_ALT_CLOUD_CONN_MAX_INIT_TIME_S);
        /*
         * Set status interval address, which has the interval in seconds for sending fohh_status_request messages by connected broker.
         * Default status interval is 1 second for sending fohh_status_request messages.
         */
        fohh_set_status_interval(gs->private_broker_state->dns_log_channel, &(gs->pblog_dns_status_interval));

        zpath_debug_add_fohh_log_collection(zpn_transaction_collection);
        zpath_debug_add_fohh_log_collection(zpn_auth_collection);
        zpath_debug_add_fohh_log_collection(zpn_ast_auth_collection);
        zpath_debug_add_fohh_log_collection(zpn_event_collection);
        zpath_debug_add_fohh_log_collection(zpn_dns_collection);
    }

    zpn_dr_config_ovd_monitor_init(ZPN_BROKER_GET_GID(), ZPN_BROKER_GET_GROUP_GID(), gs->customer_id);

    zpn_pse_resiliency_config_ovd_monitor_init(ZPN_BROKER_GET_GID(), ZPN_BROKER_GET_GROUP_GID(), gs->customer_id);

    zpn_broker_get_app_buffer_parameters();

    result = zpath_debug_add_flag_ext(zpn_debug_cnxt(),
                                      zpn_debug_catch_cnxt(),
                                      zpn_debug_cnxt_cnt(),
                                      "zpn",
                                      zpn_debug_names);
    if (result) {
        ZPN_LOG(AL_ERROR, "zpath_debug_add_flag failed: %s",
                zpath_result_string(result));
        return result;
    }
    static const char *pbroker_debug_names[] = PBROKER_DEBUG_NAMES;

    result = zpath_debug_add_flag(&pbroker_debug,
                                  pbroker_debug_catch_defaults,
                                  "pbroker",
                                  pbroker_debug_names);
    if (result) {
        ZPN_LOG(AL_ERROR, "zpath_debug_add_flag failed: %s",
                zpath_result_string(result));
        return result;
    }

    pbroker_name = pbroker_get_name_by_id(&gs->private_broker_id);
    if(pbroker_name) {
        snprintf(gs->cfg_name, sizeof(gs->cfg_name), "%s", pbroker_name);
    }
    else {
        snprintf(gs->cfg_name, sizeof(gs->cfg_name), "%s", SERVICE_EDGE_LOG_NAME);
    }

    if (!dr_mode_enabled) {
        /* Private broker admin probe library init */
        result = private_broker_admin_probe_init(gs->private_broker_state->wally,
                                                 gs->private_broker_id,
                                                 gs->cfg_name,
                                                 gs->customer_id,
                                                 zpath_event_collection,
                                                 1);
        if (result) {
            ZPN_LOG(AL_ERROR, "Could not init private broker admin_probe: %s",
                                             zpn_result_string(result));
            return result;
        }

        result = admin_probe_rpc_init();
        if (result) {
            ZPN_LOG(AL_ERROR, "Could not init admin_probe_rpc_init: %s", zpn_result_string(result));
            return result;
        }
    }

    zpath_system_get_system_memory(NULL,
                                   NULL,
                                   NULL,
                                   &swap_config,
                                   NULL);

    pbroker_set_swap_config(swap_config);

    result = zpath_debug_add_flag(&zhealth_debug, zhealth_debug_catch, "zhealth", zhealth_debug_names);
    if (result) {
        ZPN_LOG(AL_ERROR, "Could not initialize zhealth debug flags: %s", zpath_result_string(result));
        return result;
    }

    result = pbroker_stats_monitor_init();
    if (result) {
        ZPN_LOG(AL_ERROR, "pbroker_stats_monitor_init failed: %s",
                zpath_result_string(result));
        return result;
    }

    result = zpn_event_init(private_broker_is_dev_environment(), 1);
    if (result) {
        ZPN_LOG(AL_ERROR, "zpn_event_init failed: %s", zpath_result_string(result));
        return result;
    }

    result = zpn_pbroker_data_connection_stats_data_init();
    if (result) {
        ZPN_LOG(AL_ERROR, "zpn_pbroker_data_connection_stats_data_init failed: %s",
                zpath_result_string(result));
        return result;
    }

    result = zpath_debug_add_flag(&zrdt_debug,
                                  zrdt_debug_catch_defaults,
                                  "zrdt",
                                  zrdt_debug_names);
    if (result) {
        ZPN_LOG(AL_ERROR, "zpath_debug_add_flag failed: %s",
                zpath_result_string(result));
        return result;
    }

    result = zpath_debug_add_flag(&zdtls_debug,
                                  zdtls_debug_catch_defaults,
                                  "zdtls",
                                  zdtls_debug_names);
    if (result) {
        ZPN_LOG(AL_ERROR, "zpath_debug_add_flag failed: %s",
                zpath_result_string(result));
        return result;
    }

    if (!dr_mode_enabled) {
        result = pbroker_stats_init(NULL);
        if (result) {
            ZPN_LOG(AL_ERROR, "Private broker Stats connection init error: %s",
                    zpath_result_string(result));
            return result;
        }

        result = pbroker_stats_tx_init();
        if (result) {
            ZPN_LOG(AL_ERROR, "Private broker Stats tx init error: %s",
                    zpath_result_string(result));
            return result;
        }

        result = zpn_dta_monitor_init();
        if(result) {
            ZPN_LOG(AL_ERROR, "Could not initialize DTA monitor");
            return ZPN_RESULT_ERR;
        }
    }

    ZPN_LOG(AL_NOTICE, "SSL memory allocator %s", is_openssl_allocator() ? "set" : "not set");

   zpath_config_override_monitor_int(ZPN_PB_CLIENT_NO_CREATE,
                                     &g_config_pb_client_no_create,
                                     NULL,
                                     ZPN_PB_CLIENT_NO_CREATE_DEFAULT,
                                     (int64_t)private_broker_cfg.private_broker.broker_id,
                                     (int64_t)private_broker_cfg.private_broker.pb_group_id,
                                     (int64_t)private_broker_cfg.private_broker.customer_id,
                                     (int64_t) ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                     (int64_t) 0);

   result = zpath_maxmind_status_init();
   if (result) {
        ZPN_LOG(AL_ERROR, "Private broker Maxmind stats init error: %s",
                zpath_result_string(result));
        return result;
   }

   zpath_config_override_monitor_int(ZPN_GEOIP_MMDB_DOWNLOAD_DISABLE,
                                     &g_config_pb_client_geoip_disable,
                                     NULL,
                                     DEFAULT_ZPN_GEOIP_MMDB_DOWNLOAD_DISABLE,
                                     (int64_t)private_broker_cfg.private_broker.broker_id,
                                     (int64_t)private_broker_cfg.private_broker.pb_group_id,
                                     (int64_t)private_broker_cfg.private_broker.customer_id,
                                     (int64_t) ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                     (int64_t) 0);


    /* Cleanup upgrade config before writing to it*/
    zpath_upgrade_cfg_cleanup();
    if (zvm_vm_type_is_zscaler_rh_image()) {
        zpn_pb_sarge_and_os_upgrade_feature_setup();
    }

    zpath_config_override_monitor_int(PSE_OAUTH_ENROLL_DISABLE,
                                      &oauth_enrollment_feature_flag,
                                      zpn_pb_oauth_enrollment_config_override_monitor_callback,
                                      DEFAULT_PSE_OAUTH_ENROLL_DISABLE,
                                      (int64_t)private_broker_cfg.private_broker.broker_id,
                                      (int64_t)private_broker_cfg.private_broker.pb_group_id,
                                      (int64_t)private_broker_cfg.private_broker.customer_id,
                                      (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                      (int64_t)0);

   ZPN_LOG(AL_NOTICE, "Initialization Complete");

   /* initialize forward proxy module if we are on Zscaler RHEL image and we are not on docker */
    if (zvm_vm_type_is_zscaler_rh_image() && !zvm_vm_type_is_docker()) {
        zpn_private_broker_features_fproxy_cfg_monitor_init(gs->private_broker_id);
    }

   //MMDB FILE DOWNLOAD CODE STARTS
   if(disable_geoip != zcrypt_metadata_disable_geoip_true){ //<- user has not explicitly asked not to download and install mmdb files

   memset(mmdb_geoip_version_g, 0, MAX_VERSION_LEN);
   memset(mmdb_isp_version_g, 0, MAX_VERSION_LEN);

   //Create the directory in which the geoip files will go
   if(mkdir(MMDB_FILE_PATH, 0755) == -1) {
        if(errno != EEXIST){
            ZPN_LOG(AL_CRITICAL, "Failed to create the new directory for geoip files!: %s",strerror(errno));
            return ZPN_RESULT_ERR;
        }
   }
   mmdb_fetch_hostname = gs->cloud_config->sarge->dist_hostname;
   mmdb_fetch_proxyname = gs->cloud_config->sarge->dist_proxyname;

   if(gs->develop_certs_mode == zcrypt_metadata_develop_mode_disabled){
       geodb_path = FETCH_PATH;
       develop_mode = 0;
    }

    snprintf(geodb_path_full,FILE_PATH_LEN,"%s/GeoIP2-City.mmdb",geodb_path);
    snprintf(isp_path_full,FILE_PATH_LEN,"%s/GeoIP2-ISP.mmdb",geodb_path);

    write_version(FILENAME_GEOIP_ENC, VERSION_NONE);
    write_version(FILENAME_ISP_ENC, VERSION_NONE);

   //CHECK FOR GEOIP FILES
   if(file_exists(FILENAME_GEOIP_FALLBACK)){
        geoip_fallback_data = decrypt_file((char*)gs->cfg_hw_key.data, FILENAME_GEOIP_FALLBACK, &geoip_fallback_size, 0);
        if(!geoip_fallback_data){
            ZPN_LOG(AL_ERROR,"Unable to decrypt file:%s",FILENAME_GEOIP_FALLBACK);
        }
    }

    if(file_exists(FILENAME_GEOIP)){
        geoip_data = decrypt_file((char*)gs->cfg_hw_key.data, FILENAME_GEOIP, &geoip_size, 0);
        if(!geoip_data){
            ZPN_LOG(AL_ERROR,"Unable to decrypt file:%s",FILENAME_GEOIP);
        }
    }

     //if geoip, then usual, if only fallback, initialise with it and leave the fallback part null
    //INIT GEOIP DB IF ONE OF THEM IS PRESENT
    if(geoip_data || geoip_fallback_data){
        ZPN_LOG(LOG_INFO,"Loading GeoIP-City database...\n");
        if(geoip_data){
            snprintf(zpath_geoip_filename, sizeof(zpath_geoip_filename), "%s", FILENAME_GEOIP);
            geoip_db_file = zpath_geoip_filename;
            result = zpath_geoip_init_from_memory(geoip_db_file, geoip_data, geoip_size, geoip_fallback_data, geoip_fallback_size);
        }else{
            snprintf(zpath_geoip_filename, sizeof(zpath_geoip_filename), "%s", FILENAME_GEOIP_FALLBACK);
            geoip_db_file = zpath_geoip_filename;
            ZPN_LOG(LOG_INFO,"Loading GeoIP-City database from fallback file...\n");
            result = zpath_geoip_init_from_memory(geoip_db_file, geoip_fallback_data, geoip_fallback_size, NULL, 0);
        }
        if (result) {
            ZPN_LOG(AL_ERROR, "GeoIP-City databse init failed: %s",
                    zpath_result_string(result));
            geoip_db_file = NULL;
        }else{
            result = zpath_geoip_sanity_verify();
            if(result){
                ZPN_LOG(AL_ERROR,"GeoIP-City db failed sanity check\n");
                geo_sanity = 0;
                geoip_db_file = NULL;
            }else{
                geo_sanity = 1;
                ZPN_LOG(LOG_INFO,"Successfully loaded GeoIP-City database..\n");
            }

            if(!geoip_db_file && geoip_fallback_data && !zpath_is_geoip_fallback_running())
            {
                snprintf(zpath_geoip_filename, sizeof(zpath_geoip_filename), "%s", FILENAME_GEOIP_FALLBACK);
                geoip_db_file = zpath_geoip_filename;
                ZPN_LOG(LOG_INFO,"Loading GeoIP-City database from fallback file...\n");
                result = zpath_trigger_geoip_reload(NULL, geoip_fallback_data, geoip_fallback_size, NULL, 0);
                if(!result){
                    result = zpath_geoip_sanity_verify();
                    if(!result){
                        ZPN_LOG(LOG_INFO,"Successfully loaded GeoIP-City database from fallback file..\n");
                        geo_sanity = 1;
                    }else{
                        ZPN_LOG(AL_ERROR,"GeoIP-City db failed sanity check\n");
                        geo_sanity = 0;
                        geoip_db_file = NULL;
                    }
                }else{
                    ZPN_LOG(AL_ERROR, "GeoIP-City databse init failed from fallback file: %s",
                        zpath_result_string(result));
                    geo_sanity = 0;
                    geoip_db_file = NULL;
                }
            }
        }
    }else{
        ZPN_LOG(LOG_INFO,"GeoIP-City database not present.\n");
    }

    if(geo_sanity && zpath_is_geoip_fallback_running()){
        char file_version[FILE_PATH_LEN] = {0};
        zcrypt_get_metadata_version(FILENAME_GEOIP_FALLBACK_META, file_version, FILE_PATH_LEN, log_verify_version_f, 0);
        write_version(FILENAME_GEOIP_ENC, file_version);
        ZPN_LOG(LOG_INFO,"Running GeoIP-City version:%s", file_version);
        if(geoip_data){
            ZPN_FF_FREE(geoip_data);
            geoip_data = NULL;
        }
    }else if(geo_sanity && zpath_is_geoip_running()){
        char file_version[FILE_PATH_LEN] = {0};
        zcrypt_get_metadata_version(FILENAME_GEOIP_META, file_version, FILE_PATH_LEN, log_verify_version_f, 0);
        ZPN_LOG(LOG_INFO,"Running GeoIP-City version:%s", file_version);
        write_version(FILENAME_GEOIP_ENC, file_version);
        if(geoip_fallback_data){
            ZPN_FF_FREE(geoip_fallback_data);
            geoip_fallback_data = NULL;
        }
    }else{
        if(geoip_data){
            ZPN_FF_FREE(geoip_data);
            geoip_data = NULL;
        }
        if(geoip_fallback_data){
            ZPN_FF_FREE(geoip_fallback_data);
            geoip_fallback_data = NULL;
        }
    }

   //CHECK FOR ISPIP FILES
   if(file_exists(FILENAME_ISP_FALLBACK)){
        ispip_fallback_data = decrypt_file((char*)gs->cfg_hw_key.data, FILENAME_ISP_FALLBACK, &ispip_fallback_size, 0);
        if(!ispip_fallback_data){
            ZPN_LOG(AL_ERROR,"Unable to decrypt file:%s",FILENAME_ISP_FALLBACK);
        }
    }

    if(file_exists(FILENAME_ISP)){
        ispip_data = decrypt_file((char*)gs->cfg_hw_key.data, FILENAME_ISP, &ispip_size, 0);
        if(!ispip_data){
            ZPN_LOG(AL_ERROR,"Unable to decrypt file:%s",FILENAME_ISP);
        }
    }

    //INIT ISP DB IF ONE OF THEM EXISTS
    if(ispip_fallback_data || ispip_data){
        ZPN_LOG(LOG_INFO,"Loading GeoIP-ISP database...\n");
        if(ispip_data){
            snprintf(zpath_ispip_filename, sizeof(zpath_ispip_filename), "%s", FILENAME_ISP);
            geoip_isp_db_file = zpath_ispip_filename;
            result = zpath_ispip_init_from_memory(geoip_isp_db_file, ispip_data, ispip_size, ispip_fallback_data, ispip_fallback_size);
        }else{
            snprintf(zpath_ispip_filename, sizeof(zpath_ispip_filename), "%s", FILENAME_ISP_FALLBACK);
            geoip_isp_db_file = zpath_ispip_filename;
            ZPN_LOG(LOG_INFO,"Loading GeoIP-ISP database from fallback file...\n");
            result = zpath_ispip_init_from_memory(geoip_isp_db_file, ispip_fallback_data, ispip_fallback_size, NULL, 0);
        }
        if (result) {
            ZPN_LOG(AL_ERROR, "GeoIP-ISP database failed: %s",
                    zpath_result_string(result));
            geoip_isp_db_file = NULL;
        }else{
            result = zpath_ispip_sanity_verify();
            if(result){
                ZPN_LOG(AL_ERROR,"GeoIP-ISP db failed sanity check\n");
                geoip_isp_db_file = NULL;
                isp_sanity = 0;
            }else{
                ZPN_LOG(LOG_INFO,"Successfully loaded GeoIP-ISP database..\n");
                isp_sanity = 1;
            }

            if(!geoip_isp_db_file && ispip_fallback_data && !zpath_is_ispip_fallback_running())
            {
                snprintf(zpath_ispip_filename, sizeof(zpath_ispip_filename), "%s", FILENAME_ISP_FALLBACK);
                geoip_isp_db_file = zpath_ispip_filename;
                ZPN_LOG(LOG_INFO,"Loading GeoIP-ISP database from fallback file...\n");
                result = zpath_trigger_ispip_reload(NULL,ispip_fallback_data, ispip_fallback_size, NULL, 0);
                if(!result){
                    result = zpath_ispip_sanity_verify();
                    if(!result){
                        isp_sanity = 1;
                        ZPN_LOG(LOG_INFO,"Successfully loaded GeoIP-ISP database from fallback file..\n");
                    }else{
                        isp_sanity = 0;
                        ZPN_LOG(AL_ERROR,"GeoIP-ISP db failed sanity check\n");
                        geoip_isp_db_file = NULL;
                    }
                }else{
                    ZPN_LOG(AL_ERROR, "GeoIP-ISP databse init failed from fallback file: %s",
                        zpath_result_string(result));
                    isp_sanity = 0;
                    geoip_isp_db_file = NULL;
                }
            }
        }
    }else{
        ZPN_LOG(LOG_INFO,"GeoIP-ISP database not present.\n");
    }

    if(isp_sanity && zpath_is_ispip_fallback_running()){
        char file_version[FILE_PATH_LEN] = {0};
        zcrypt_get_metadata_version(FILENAME_ISP_FALLBACK_META, file_version, FILE_PATH_LEN, log_verify_version_f, 0);
        write_version(FILENAME_ISP_ENC, file_version);
        ZPN_LOG(LOG_INFO,"Running GeoIP-ISP db version:%s", file_version);
        if(ispip_data){
            ZPN_FF_FREE(ispip_data);
            ispip_data = NULL;
        }
    }else if(isp_sanity && zpath_is_ispip_running()){
        char file_version[FILE_PATH_LEN] = {0};
        zcrypt_get_metadata_version(FILENAME_ISP_META, file_version, FILE_PATH_LEN, log_verify_version_f, 0);
        write_version(FILENAME_ISP_ENC, file_version);
        ZPN_LOG(LOG_INFO,"Running GeoIP-ISP db version:%s", file_version);
        if(ispip_fallback_data){
            ZPN_FF_FREE(ispip_fallback_data);
            ispip_fallback_data = NULL;
        }
    }else{
        if(ispip_data){
            ZPN_FF_FREE(ispip_data);
            ispip_data = NULL;
        }
        if(ispip_fallback_data){
            ZPN_FF_FREE(ispip_fallback_data);
            ispip_fallback_data = NULL;
        }
    }


    if(!dr_mode_enabled){
        //update the auth log
        if (fohh_get_state(gs->private_broker_state->broker_control) == fohh_connection_connected) {
            zpn_send_zpn_tcp_info_report(gs->private_broker_state->broker_control,
                                         fohh_connection_incarnation(gs->private_broker_state->broker_control),
                                         gs->private_broker_state->broker_control,
                                         zvm_vm_type_to_str_concise(zvm_type_get()), g_pbroker_runtime_os);
        }

        ZPN_LOG(AL_NOTICE, "Starting mmdb fetch thread");
        fetch_file(mmdb_fetch_proxyname,mmdb_fetch_hostname,geodb_path_full,geoip_filename,
                NULL, 0, 1, file_fetch_callback, 1, 1, version_check_callback, MMDB_GEOIP, 1, develop_mode,download_fail_callback, config_disable_check_callback,gs->zcdns);
        fetch_file(mmdb_fetch_proxyname,mmdb_fetch_hostname,isp_path_full,isp_filename,
                NULL, 0, 1, file_fetch_callback, 1, 1, version_check_callback, MMDB_ISP, 1, develop_mode,download_fail_callback, config_disable_check_callback,gs->zcdns);
    }
   } else if(!dr_mode_enabled){
        //<- if(disable_geoip != zcrypt_metadata_disable_geoip_true)
        //Create the directory in which the geoip files will go
        if(mkdir(MMDB_FILE_PATH, 0755) == -1) {
            if(errno != EEXIST){
                ZPN_LOG(AL_CRITICAL, "Failed to create the new directory for geoip files!: %s",strerror(errno));
                return ZPN_RESULT_ERR;
            }
        }
        write_version(FILENAME_GEOIP_ENC, "disabled");
        write_version(FILENAME_ISP_ENC, "disabled");
        //update the auth log
        if (fohh_get_state(gs->private_broker_state->broker_control) == fohh_connection_connected) {
            zpn_send_zpn_tcp_info_report(gs->private_broker_state->broker_control,
                                         fohh_connection_incarnation(gs->private_broker_state->broker_control),
                                         gs->private_broker_state->broker_control,
                                         zvm_vm_type_to_str_concise(zvm_type_get()), g_pbroker_runtime_os);
        }
   }

    gs->zthread = zthread_register_self(gs->cn, 60);

    result = zpn_broker_assistant_listen_socket();
    if (result) {
        ZPN_LOG(AL_ERROR, "Could not listen for assistants: %s", zpath_result_string(result));
        return result;
    }

    result = zpn_broker_client_listen_socket(1);
    if (result) {
        ZPN_LOG(AL_ERROR, "Could not listen for clients: %s", zpath_result_string(result));
        return result;
    }

    result = zpn_broker_socket_listen_client_init();
    if (result) {
        ZPN_LOG(AL_ERROR, "Could not attach ip to 443/8443. Unable to init server listen for client.");
        return result;
    }

    result = zpn_broker_c2c_regex_customer_cache_init(gs->customer_id);
    if (result) {
        ZPN_LOG(AL_ERROR, "Unable to init c2c regex cache, err: %s for customer: %"PRId64, zpn_result_string(result), gs->customer_id);
        return result;
    }
    result = zpn_dispatcher_set_c2c_regex_cb(zpn_broker_dispatch_c2c_regex_match_check);
    if (result) {
        ZPN_LOG(AL_ERROR, "Unable to set c2c regex cb, err %s", zpn_result_string(result));
        return result;
    }

    result = zpn_private_broker_site_start();
    if (result) {
        ZPN_LOG(AL_ERROR, "Unable to start site service, err %s", zpn_result_string(result));
        return result;
    }

    zpath_registration_completed();

    /* Start main loop */
    private_broker_main_loop();

    sleep(1);

    return 0;
}

int main(int argc, char *argv[]) {
    int result;

    /* Turn off buffering of stdout. This needs to be called before any stdout operation */
    setvbuf(stdout, NULL, _IONBF, 0);

    result = sub_main(argc, argv);
    sleep(1);
    return result;
}
