/*
 * zpn_private_broker_admin_probe.c. Copyright (C) 2021 Zscaler Inc, All Rights Reserved
 */

#include "zpath_lib/zpath_local.h"
#include "zpath_lib/zpath_config_override.h"
#include "zpn/zpn_broker_private.h"
#include "zpn/zpn_pbroker_broker.h"
#include "zpn/zpn_fohh_client_pbroker.h"
#include "zpn/zpn_private_broker_util.h"
#include "zpn_private_brokerd/zpn_private_broker_admin_probe.h"
#include "zpn/pbroker_assert.h"
#include "admin_probe/admin_probe.h"
#include "admin_probe/admin_probe_public.h"
#include "zpn_enrollment_lib/zpn_enrollment.h"
#include "zthread/zthread.h"
#include "admin_probe/admin_probe_stats.h"
#include "zpath_lib/zpath_capability_util.h"

#ifdef __linux__
#include <linux/reboot.h>
#include <sys/syscall.h>
#include <sys/reboot.h>
#include <unistd.h>
#include <sys/capability.h>
#include <sys/types.h>
#else
#endif

#include "zpath_lib/zpath_debug.h"

int effective_pb_configured_capabilities[PBROKER_STATE_MAX_CAPABILITIES];
int permitted_pb_configured_capabilities[PBROKER_STATE_MAX_CAPABILITIES];

const char *pb_configured_capabilities_str[PBROKER_STATE_MAX_CAPABILITIES] = {
    "cap_net_admin",
    "cap_net_bind_service",
    "cap_net_raw",
    "cap_sys_boot",
    "cap_sys_nice",
    "cap_sys_time"
};

extern int is_container_env;

/*
 * Capability sanity check.
 * Private broker should now have all the capabilities set by sarge:
 *     cap_net_admin, cap_net_bind_service, cap_net_raw, cap_sys_boot, cap_sys_nice, cap_sys_time
 *
 * Note: if Private broker is in container env, we don't enforce cap_sys_boot.
 */
#ifdef __linux__
static int pbroker_state_get_capabilities(const char * const *cap_name, int cap_len, int *effective, int *permitted)
{
    pid_t pid;
    cap_t cap;
    cap_value_t cap_list[cap_len];
    cap_flag_value_t cap_flags_value;

    pid = getpid();
    cap = cap_get_pid(pid);
    if (cap == NULL) {
        ZPN_LOG(AL_ERROR, "Failed to get capability info using private broker's pid");
        return ZPATH_RESULT_ERR;
    }

    /* dump the capabilies */
    for (int i = 0; i < cap_len; i++) {
        cap_from_name(cap_name[i], &cap_list[i]);
        cap_get_flag(cap, cap_list[i], CAP_EFFECTIVE, &cap_flags_value);
        effective[i] = (cap_flags_value == CAP_SET) ? 1 : 0;
        cap_get_flag(cap, cap_list[i], CAP_PERMITTED, &cap_flags_value);
        permitted[i] = (cap_flags_value == CAP_SET) ? 1 : 0;
    }
    return ZPATH_RESULT_NO_ERROR;
}
#else
static int pbroker_state_get_capabilities(const char * const *cap_name, int cap_len, int *effective, int *permitted)
{
    return ZPATH_RESULT_NOT_IMPLEMENTED;
}
#endif

/*
 * Container env:
 * Capabilities will be set manually when container is created. Usually cap_sys_boot is not set by customer (see ET-32579
 * for details). So we need to skip setting & checking the cap_sys_boot capability if we're in container env.
 */
void pbroker_state_check_capabilities() {
    const char *const *cap_list = NULL;
    int cap_count = 0;
    int *effective = NULL;
    int *permitted = NULL;

    static int container_effective_caps[PBROKER_STATE_MAX_CAPABILITIES];
    static int container_permitted_caps[PBROKER_STATE_MAX_CAPABILITIES];

    if (is_container_env) {
        cap_list = zpath_get_final_capabilities(&cap_count);
        effective = container_effective_caps;
        permitted = container_permitted_caps;
    } else {
        cap_list = pb_configured_capabilities_str;
        cap_count = PBROKER_STATE_MAX_CAPABILITIES;
        effective = effective_pb_configured_capabilities;
        permitted = permitted_pb_configured_capabilities;
    }

    if (!cap_list || cap_count == 0) {
        ZPN_LOG(AL_ERROR, "No capabilities configured or retrieved");
        return;
    }

    int res = pbroker_state_get_capabilities(cap_list, cap_count, effective, permitted);
    if (res) {
        ZPN_LOG(AL_NOTICE, "Could not get private broker capacity info: %s", zpath_result_string(res));
        return;
    }

    int capability_check_failed = 0;
    ZPN_LOG(AL_INFO,
            "Running capability check; container env: %s, cap_count: %d",
            is_container_env ? "yes" : "no",
            cap_count);  // TBD remove
    for (int i = 0; i < cap_count; i++) {
        ZPN_LOG(AL_INFO, "Cap to check: %s", cap_list[i]);  // TBD remove
        if (is_container_env && strcmp(cap_list[i], "cap_sys_boot") == 0) {
            continue;  // Skip cap_sys_boot in container
        }

        if (!effective[i] || !permitted[i]) {
            capability_check_failed = 1;
            ZPN_LOG(AL_CRITICAL,
                    "Private broker capability %s not set! EFFECTIVE: %s, PERMITTED: %s",
                    cap_list[i],
                    effective[i] ? "SET" : "NOT SET",
                    permitted[i] ? "SET" : "NOT SET");
        }
    }

    if (capability_check_failed) {
        ZPN_LOG(AL_ERROR, "Private broker capability check failed, please ensure the required capabilities are set");
    } else {
        const char *mode_str = getenv("ZPA_CAPABILITY_MODE");
        ZPN_LOG(AL_NOTICE,
                "Private broker capability check passed (mode: %s, container: %s)",
                mode_str ? mode_str : "default",
                is_container_env ? "yes" : "no");
    }
}

static void private_broker_state_pause_breaker(void* cookie1,
                                        void* cookie2)
{
    PBROKER_ASSERT_HARD(0, "Private broker in PASUE state for a long period (%ld secs), restarting..",
                                             (long)PRIVATE_BROKER_STATE_PAUSE_BREAKER_TIME_US/1000000);
    exit(1);
}

int private_broker_state_is_paused()
{
    const struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();
    return gs->is_paused;
}

char *private_broker_state_get_pause_reason(enum zpn_private_broker_pause_reason reason_code)
{
    char *pause_reason = NULL;

    switch(reason_code)
    {
        case zpn_private_broker_pause_reason_restart_process:
            pause_reason = PAUSE_REASON_ADMIN_PROBE_PROCESS_RESTART;
            break;
        case zpn_private_broker_pause_reason_restart_system:
            pause_reason = PAUSE_REASON_ADMIN_PROBE_SYSTEM_RESTART;
            break;
        case zpn_private_broker_pause_reason_certificate_expiry:
            pause_reason = PAUSE_REASON_CERT_EXPIRY;
            break;
        case zpn_private_broker_pause_reason_upgrade_inprogress:
            pause_reason = PAUSE_REASON_UPGRADE;
            break;
        default:
            pause_reason = "N/A";
    }
    return pause_reason;
}

/*
 * zpn_private_broker_pause_reason_restart_process and zpn_private_broker_pause_reason_restart_system
 * will be checked every 1 second from main thread.
 *
 * once main thread finds out either value becomes one, it will enter pause state mode, and restart after 30s
 */
void private_broker_state_set_admin_probe_pause_for_process_restart()
{
    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();
    gs->pause_reason = zpn_private_broker_pause_reason_restart_process;
}

void private_broker_state_set_admin_probe_pause_for_system_restart()
{
    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();
    gs->pause_reason = zpn_private_broker_pause_reason_restart_system;
}

int private_broker_state_is_admin_probe_pause_for_restart_process()
{
    const struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();
    return (gs->pause_reason == zpn_private_broker_pause_reason_restart_process) ? 1 : 0;
}

int private_broker_state_is_admin_probe_pause_for_restart_system()
{
    const struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();
    return (gs->pause_reason == zpn_private_broker_pause_reason_restart_system) ? 1 : 0;
}

void private_broker_state_set_pause(int is_admin_probe)
{
    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();
    if (gs->is_paused) {
        goto EXIT;
    }
    gs->is_paused = 1;
    gs->pause_mode_enter_time_cloud_s = zpn_cloud_adjusted_epoch_s();

    zevent_defer(private_broker_state_pause_breaker,  0, 0, PRIVATE_BROKER_STATE_PAUSE_BREAKER_TIME_US);

    if (is_admin_probe) {
        goto EXIT;
    }

    // Find the reason for going into pause state.
    if (0 == gs->private_broker_state->next_restart_time) {
        gs->pause_reason = zpn_private_broker_pause_reason_certificate_expiry;
    }
    else if (gs->private_broker_state->next_restart_time < zpn_cloud_adjusted_epoch_s()) {
        gs->pause_reason = zpn_private_broker_pause_reason_certificate_expiry;
    }
    else {
        PBROKER_ASSERT_HARD(gs->cert_force_re_enroll_time_cloud_s, "unexpected code path, please report to customer support team");
        if (gs->cert_force_re_enroll_time_cloud_s < gs->private_broker_state->next_restart_time) {
            gs->pause_reason = zpn_private_broker_pause_reason_certificate_expiry;
        } else {
            gs->pause_reason = zpn_private_broker_pause_reason_upgrade_inprogress;
        }
    }

EXIT:
    return;
}

/*
 * It returns the epoch_s cloud_time that this Private broker can say to the ZPA that it will not be able to serve.
 * Check certificate re_enroll_time and upgrade restart time to return the epoch_s cloud time from when
 * this private broker will not be able to serve admin probe command.
 *
 *  (eg)
 *  a. If current cloud time is 9:00am; next restart time is 9:30am; cert re-enroll is somewhere next year; return 9.25am.
 *  b. If current cloud time is 9:27am; next restart time is 9:30am; cert re-enroll is somewhere next year; return 9.25am.
 *  c. If current cloud time is 9:40am; next restart time is 9:30am; cert re-enroll is somewhere next year; return
 *  cert re-enroll time.
 *  d. If current cloud time is 10:00am; next restart time is 11am; cert re-enroll time is 10.15am; return 10.10am.
 */

int64_t zpn_private_broker_state_get_inactive_time_cloud_s()
{
    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();
    int64_t        last_expiry_epoch_cloud_s;
    int64_t        time_cloud_s;

    assert(gs->cert_force_re_enroll_time_cloud_s);
    last_expiry_epoch_cloud_s = gs->cert_force_re_enroll_time_cloud_s;

    time_cloud_s = zpn_cloud_adjusted_epoch_s();
    if ((gs->private_broker_state->next_restart_time) &&
        (gs->private_broker_state->next_restart_time > time_cloud_s) &&
        (last_expiry_epoch_cloud_s > gs->private_broker_state->next_restart_time)) {
        // Upgrade restart is gonna happen soon. Return the next restart time.
        last_expiry_epoch_cloud_s = gs->private_broker_state->next_restart_time;
    }

    gs->pb_restart_time_cloud_s = last_expiry_epoch_cloud_s;

    // Inorder to support graceful restart, give 5mins of pause time before certificate expiry/upgrade.
    last_expiry_epoch_cloud_s -= SLOW_STOP_TIME_S;

    return last_expiry_epoch_cloud_s;
}

/*
 * Evaluate every second to see if this Private broker should enter into PAUSE-ed state. PAUSE if we passed the last active
 * time allowed for the Private broker.
 *
 */
void private_broker_state_pause_evaluate()
{
    struct zpn_private_broker_global_state *gs = NULL;
    int64_t        cloud_now_s;
    int64_t        cloud_pausetime_s;

    if (private_broker_state_is_paused()) {
        return;
    }

    gs = zpn_get_private_broker_global_state();

    cloud_pausetime_s = zpn_private_broker_state_get_inactive_time_cloud_s();
    cloud_now_s = zpn_cloud_adjusted_epoch_s();
    if (cloud_now_s >= cloud_pausetime_s) {
        private_broker_state_set_pause(0);
        ZPN_LOG(AL_INFO, "Private broker(ID = %"PRId64") (Name = %s) entering PAUSE mode because of - %s",
                      gs->private_broker_id, gs->cfg_name, private_broker_state_get_pause_reason(gs->pause_reason));
    }
    if (private_broker_state_is_admin_probe_pause_for_restart_process() ||
            private_broker_state_is_admin_probe_pause_for_restart_system()) {
        private_broker_state_set_pause(1);
        gs->pb_restart_time_cloud_s = gs->pause_mode_enter_time_cloud_s + ADMIN_PROBE_RESTART_TIME_S;
        ZPN_LOG(AL_INFO, "Private broker (ID = %"PRId64") (Name = %s) entering PAUSE mode because of - %s",
                      gs->private_broker_id, gs->cfg_name, private_broker_state_get_pause_reason(gs->pause_reason));
    }
    return;
}

/*
 * Evaluate every second to see if this Private broker should restart the process/system.
 */
void private_broker_admin_probe_restart_evaluable()
{
#ifdef __linux__
    static int restart_process_counter_s = ADMIN_PROBE_RESTART_TIME_S;
    static int restart_system_counter_s = ADMIN_PROBE_RESTART_TIME_S;

    if (private_broker_state_is_admin_probe_pause_for_restart_process()) {
        if (0 == (restart_process_counter_s % 10)) {
            ZPN_LOG(AL_NOTICE, "Private broker will restart the process in %d sec due to admin probe request", restart_process_counter_s);
        }
        restart_process_counter_s--;
        if (restart_process_counter_s <= 0) {
            ZPN_LOG(AL_NOTICE, "Private broker restarting now for admin probe request");
            sleep(1);
            exit(0);
        }
    } else if (private_broker_state_is_admin_probe_pause_for_restart_system()) {
        int res;
        // not worry about sync(), if we do sync there will be some hang which is out of our control*
        if (0 == (restart_system_counter_s % 10)) {
            ZPN_LOG(AL_NOTICE, "Private broker will reboot the system in %d sec due to admin probe request", restart_system_counter_s);
        }
        restart_system_counter_s--;
        if (restart_system_counter_s <= 0) {
            ZPN_LOG(AL_NOTICE, "System rebooting now for admin probe request");
            sleep(1);
            res = reboot(LINUX_REBOOT_CMD_RESTART);
            if (res != 0) {
                ZPN_LOG(AL_NOTICE, "System reboot failed, will restart process now instead");
                sleep(1);
                exit(0);
            }
        }
    }
#else
    return;
#endif
}

/*
 * Private broker handler for the admin probe restart command.
 * type 0 to restart the process
 * type 1 to restart the system
 */
int private_broker_admin_probe_restart(enum restart_type type)
{
    if (private_broker_state_is_paused()) {
        ZPN_LOG(AL_NOTICE, "rx admin_probe request on restart, but private broker is already in pause state and scheduled to restart soon");
        return ZPN_RESULT_NO_ERROR;
    }

#ifdef __linux__
    if (type == process_restart) {
        ZPN_LOG(AL_NOTICE, "preparing for restarting process");
        private_broker_state_set_admin_probe_pause_for_process_restart();
    } else if (type == system_restart) {

        if (!effective_pb_configured_capabilities[pb_cap_sys_boot] || !permitted_pb_configured_capabilities[pb_cap_sys_boot]) {
            ZPN_LOG(AL_NOTICE, "This private broker has no capability to reboot the system, returning ");
            return ZPN_RESULT_ERR;
        } else {
            ZPN_LOG(AL_NOTICE, "preparing for rebooting system");
            private_broker_state_set_admin_probe_pause_for_system_restart();
        }
    } else {
        ZPN_LOG(AL_NOTICE, "rx unknown restart type from admin_probe, returning");
        return ZPN_RESULT_ERR;
    }

    return ZPN_RESULT_NO_ERROR;
#else
    ZPN_LOG(AL_ERROR, "Admin Probe restart not supportted on current platform, returning");
    return ZPN_RESULT_NOT_IMPLEMENTED;
#endif

    return ZPN_RESULT_NO_ERROR;
}


/*
 * Send admin probe command status to control broker.
 *
 */
int zpn_pbroker_control_tx_command_probe_status(struct zpn_command_probe_status* status)
{
    int res = 0;
    struct zpn_private_broker_global_state *gs = NULL;

    if(!pbroker_broker_control_is_connected()) {
        ZPN_LOG(AL_DEBUG,"Private broker control connection is not there, can not proceed sending command_probe_status report");
        return ZPN_RESULT_ERR;
    }

    gs = zpn_get_private_broker_global_state();
    res = admin_probe_send_zpn_command_probe_status(gs->private_broker_state->broker_control,
            fohh_connection_incarnation(gs->private_broker_state->broker_control), status);
    if (res) {
        ZPN_LOG(AL_ERROR,"Private broker Error sending sending command_probe_status report");
        return res;
    }

    return res;
}

/*
 * Private broker handler to send admin probe command status to control broker.
 *
 */
int private_broker_admin_probe_tx_task_update(void* status, int is_np_command_probe)
{
    int res = 0;
    res = zpn_pbroker_control_tx_command_probe_status(status);
    if (res) {
        return res;
    }

    return ZPN_RESULT_NO_ERROR;
}

/*
 * Private broker handler to get remote host name for s3 upload.
 *
 */
int private_broker_get_remote_host_for_s3_upload(char *remote_host, int size, int *no_proxy)
{
    if (remote_host) {
        snprintf(remote_host, size, "pb2br.%s", zthread_get_cloud_name());
    }
    if (no_proxy) {
        *no_proxy = 0;
    }
    return 0;

}

/*
 * Private broker handler to check the feature flag of admin probe commands.
 *
 */
int private_broker_cfg_override_feature_is_admin_probe_task_type_enabled(enum admin_probe_task_type type)
{
    int64_t config_value = 0;

    /*check overall feature flag first, if its enabled, return immediately*/
    config_value = zpath_config_override_get_config_int(PBROKER_ADMIN_PROBE_FEATURE_ALL,
                                                        &config_value,
                                                        DEFAULT_PBROKER_ADMIN_PROBE_ALL_ENABLED,
                                                        (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                        (int64_t)0);
    if (config_value) {
        return 1;
    }

    switch (type) {
        case admin_probe_task_type_restart_process:
            config_value = zpath_config_override_get_config_int(PBROKER_ADMIN_PROBE_FEATURE_RESTART_PROCESS,
                                                                &config_value,
                                                                DEFAULT_PBROKER_ADMIN_PROBE_RESTART_PROCESS_ENABLED,
                                                                (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                                (int64_t)0);

            break;
        case admin_probe_task_type_restart_system:
            config_value = zpath_config_override_get_config_int(PBROKER_ADMIN_PROBE_FEATURE_RESTART_SYSTEM,
                                                                &config_value,
                                                                DEFAULT_PBROKER_ADMIN_PROBE_RESTART_SYSTEM_ENABLED,
                                                                (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                                (int64_t)0);
            break;
        case admin_probe_task_type_dns:
                config_value = zpath_config_override_get_config_int(PBROKER_ADMIN_PROBE_FEATURE_DNS,
                                                                    &config_value,
                                                                    DEFAULT_PBROKER_ADMIN_PROBE_DNS_ENABLED,
                                                                    (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                                    (int64_t)0);
            break;
        case admin_probe_task_type_icmp:
            config_value = zpath_config_override_get_config_int(PBROKER_ADMIN_PROBE_FEATURE_ICMP,
                                                                &config_value,
                                                                DEFAULT_PBROKER_ADMIN_PROBE_ICMP_ENABLED,
                                                                (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                                (int64_t)0);
            break;
        case admin_probe_task_type_tcp:
            config_value = zpath_config_override_get_config_int(PBROKER_ADMIN_PROBE_FEATURE_TCP,
                                                                &config_value,
                                                                DEFAULT_PBROKER_ADMIN_PROBE_TCP_ENABLED,
                                                                (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                                (int64_t)0);
            break;
        case admin_probe_task_type_tcpdump:
            config_value = zpath_config_override_get_config_int(PBROKER_ADMIN_PROBE_FEATURE_TCPDUMP,
                                                                &config_value,
                                                                DEFAULT_PBROKER_ADMIN_PROBE_TCPDUMP_ENABLED,
                                                                (int64_t)ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                                                (int64_t)0);
            break;
        default:
            break;

    }

    return config_value?1:0;

}

/*
 * Display private broker's upcoming restart time along with reason.
 *
 */
static int private_broker_dump_restart_state(struct zpath_debug_state *request_state,
                                              const char **query_values,
                                              int query_value_count,
                                              void *cookie)
{
    struct zpn_private_broker_global_state  *gs             = NULL;
    int64_t                                 now_cloud_s     = 0;
    int64_t                                 time_remaining  = 0;
    enum zpn_private_broker_pause_reason    restart_reason  = 0;

    gs = zpn_get_private_broker_global_state();
    now_cloud_s = zpn_cloud_adjusted_epoch_s();
    time_remaining = gs->pb_restart_time_cloud_s - now_cloud_s;

    if(gs->is_paused) {
        restart_reason = gs->pause_reason;
    }
    else if(gs->pb_restart_time_cloud_s == gs->cert_force_re_enroll_time_cloud_s) {
        restart_reason = zpn_private_broker_pause_reason_certificate_expiry;
    }
    else {
        restart_reason = zpn_private_broker_pause_reason_upgrade_inprogress;
    }

    ZDP("Private broker will restart in %"PRId64" days, %"PRId64" hours, %"PRId64" minutes, %"PRId64" seconds, because of:%s\n",
            time_remaining / 86400, (time_remaining % 86400) / 3600, ((time_remaining % 86400) % 3600) / 60,((time_remaining % 86400) % 3600) % 60,
            private_broker_state_get_pause_reason(restart_reason));

    return ZPATH_RESULT_NO_ERROR;
}

/*
 * Dump capabilities seen by private broker process.
 *
 */
static int private_broker_dump_process_capabilities(struct zpath_debug_state *request_state,
                                                     const char **query_values,
                                                     int query_value_count,
                                                     void *cookie)
{
    for (int i = 0; i < PBROKER_STATE_MAX_CAPABILITIES; i++) {
        ZDP("Private broker capability %s is: %s\n", pb_configured_capabilities_str[i],
                                                     permitted_pb_configured_capabilities[i] ? "SET" : "NOT SET");
    }
    return ZPATH_RESULT_NO_ERROR;
}

/*
 * Initialize the admin probe library and register all the private broker handler functions.
 *
 */
int private_broker_admin_probe_init(struct wally *private_broker_wally, int64_t private_broker_gid,char *pbroker_name, int64_t customer_gid, struct argo_log_collection *event_log, int is_zpath_coonfig_override_inited)
{
    int res;
    int is_dev_env;

    is_dev_env = private_broker_is_dev_environment();

    res = admin_probe_init(private_broker_wally,
                           private_broker_gid,
                           pbroker_name,
                           customer_gid,
                           admin_probe_app_type_private_broker,
                           private_broker_admin_probe_tx_task_update,
                           private_broker_cfg_override_feature_is_admin_probe_task_type_enabled,
                           private_broker_admin_probe_restart,
                           NULL,
                           zpn_private_broker_cloud_adjusted_epoch_s,
                           is_dev_env,
                           0,
                           private_broker_get_remote_host_for_s3_upload,
                           private_broker_state_is_paused,
                           event_log,
                           is_zpath_coonfig_override_inited);
    if (res) {
        ZPN_LOG(AL_NOTICE, "Private broker admin probe init failed");
        return ZPN_RESULT_ERR;
    }

    ZPN_LOG(AL_NOTICE, "Private broker admin probe init success pb_id:%ld customer_id:%ld", (long)private_broker_gid, (long)customer_gid);

    // Register CURL commands for debugging
    (void)zpath_debug_add_read_command("Dump private broker's upcoming restart time along with reason",
                             "/pbroker/dump_restart_state/",
                             private_broker_dump_restart_state,
                             NULL,
                             NULL,
                             NULL);

    (void)zpath_debug_add_read_command("Dump capabilities seen by private broker process",
                             "/pbroker/dump_process_capabilities/",
                             private_broker_dump_process_capabilities,
                             NULL,
                             NULL,
                             NULL);

    return ZPN_RESULT_NO_ERROR;
}

int private_broker_admin_probe_stats_fill(void *cookie, int counter, void *structure_data)
{
    struct admin_probe_stats *out_data;

    (void) cookie;

    out_data = (struct admin_probe_stats *) structure_data;

    admin_probe_stats_fill(out_data);

    return ZPATH_RESULT_NO_ERROR;
}
