/*
 * zpn_private_broker_admin_probe.h. Copyright (C) 2021 Zscaler Inc, All Rights Reserved
 */

#ifndef _ZPN_PRIVATE_BROKER_ADMIN_PROBE_H_
#define _ZPN_PRIVATE_BROKER_ADMIN_PROBE_H_

#include "wally/wally_private.h"
#include "admin_probe/admin_probe_rpc.h"
#include "zpath_lib/zpath_config_override_keys.h"

#define PAUSE_REASON_CERT_EXPIRY                            "certificate expiry"
#define PAUSE_REASON_UPGRADE                                "software upgrade"
#define PAUSE_REASON_ADMIN_PROBE_PROCESS_RESTART            "admin probe process restart"
#define PAUSE_REASON_ADMIN_PROBE_SYSTEM_RESTART             "admin probe system restart"

#define DEFAULT_PBROKER_KEEPALIVE_ENABLED                   0  /* SO_KEEPALIVE is disabled by default for all customers */

/*once admin_probe receive a restart request, it restart after ADMIN_PROBE_RESTART_TIME_S*/
#define ADMIN_PROBE_RESTART_TIME_S                          30
#define PRIVATE_BROKER_STATE_PAUSE_BREAKER_TIME_US          (int64_t)(6 * 60 * 1000 * 1000ll)
#define PBROKER_STATE_MAX_CAPABILITIES                      6


enum pb_configured_capabilities {
    pb_cap_net_admin = 0,
    pb_cap_net_bind_service = 1,
    pb_cap_net_raw = 2,
    pb_cap_sys_boot = 3,
    pb_cap_sys_nice = 4,
    pb_cap_sys_time = 5,
};

int private_broker_admin_probe_init(struct wally *private_broker_wally, int64_t private_broker_gid,char *pbroker_name, int64_t customer_gid, struct argo_log_collection *event_log, int is_zpath_config_override_inited);
int private_broker_admin_probe_tx_task_update(void* status, int is_np_command_probe);
void private_broker_admin_probe_restart_evaluable();
void private_broker_state_pause_evaluate();
void pbroker_state_check_capabilities();

int private_broker_admin_probe_stats_fill(void *cookie, int counter, void *structure_data);


#endif /* _ZPN_PRIVATE_BROKER_ADMIN_PROBE_H_ */
