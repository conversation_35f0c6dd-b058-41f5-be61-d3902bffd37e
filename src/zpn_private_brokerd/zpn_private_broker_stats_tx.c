/*
 * zpn_private_broker_stats_tx.c. Copyright (C) 2018 - 2022 Zscaler Inc. All Rights Reserved.
 * Collects and transmits the pbroker's stats to stats brokers
 *
 */

#include "zpath_lib/zpath_debug.h"
#include "argo/argo_hash.h"

#include "zpn/zpn_system.h"
#include "zpn_private_brokerd/zpn_private_broker_stats.h"
#include "zpn_private_brokerd/zpn_private_broker_stats_tx.h"
#include "zpn/zpn_customer_resiliency_settings.h"

#include "zpn/zpn_private_broker_private.h"
#include "zpn/zpn_private_broker_util.h"
#include "zpn/zpn_fohh_worker.h"
#include "zpath_lib/zpath_system.h"
#include "zpn/zpn_rpc.h"
#include "zpn/pbroker_assert.h"
#include "zpath_lib/zpath_config_override.h"
#include "zpath_lib/zpath_config_override_keys.h"
#include "zpn_private_brokerd/zpn_private_broker_admin_probe.h"
#include "admin_probe/admin_probe_rpc.h"
#include "zpn/zpn_pbroker_data_connection_stats.h"
#include "zpn/zpn_broker_client_apps_db.h"
#include "zpn/zpn_broker_client.h"
#include "zpn/zpn_policy_engine.h"
#include "zpn_zdx/zpn_zdx_probes.h"
#include "zpn_zdx/zpn_zdx_mtr.h"
#include "zpn_zdx/zpn_zdx_cache.h"
#include "zpath_lib/zpath_system_stats.h"
#include "zpn/zpn_broker_client.h"
#include "zpn/zpn_private_broker_proxy.h"
#include "zpn/zpn_broker_dns.h"
#include "zpn/zpn_dispatcher.h"
#include "zpn/zpn_broker_dispatch.h"
#include "zpn/zpn_broker_stepup_auth.h"

#include <string.h>

/* 1 sec */
#define  PBROKER_STATS_TX_COMPREHENSIVE_MIN_TIMEPERIOD_USEC       ((int64_t)(1ll * 1000ll * 1000ll))
/* 5 mins */
#define  PBROKER_STATS_TX_COMPREHENSIVE_TIMEPERIOD_USEC           ((int64_t)(5ll * 60ll * 1000ll * 1000ll))
/* 1 hour */
#define  PBROKER_STATS_TX_ADMIN_PROBE_USEC                        ((int64_t)(1ll * 60ll * 60ll * 1000ll * 1000ll))
/* 5 mins */
#define  PBROKER_STATS_TX_FILE_FETCH                              ((int64_t)(5ll * 60ll * 1000ll * 1000ll))
/* 5 mins */
#define  PBROKER_STATS_TX_5_MINS_INTERVAL_USEC                    ((int64_t)(5ll * 60ll * 1000ll * 1000ll))
/* 1 mins */
#define  PBROKER_STATS_TX_SYSTEM_MEMORY_TIMEPERIOD_USEC           ((int64_t)(1ll * 60ll * 1000ll * 1000ll))
/* 1 mins */
#define  PBROKER_STATS_TX_SYSTEM_NETWORK_TIMEPERIOD_USEC           ((int64_t)(1ll * 60ll * 1000ll * 1000ll))

#define  PBROKER_STATS_TX_FPROXY_USEC                             ((int64_t)(1ll * 60ll * 60ll * 1000ll * 1000ll))
/*24hrs*/
#define  PBROKER_STATS_TX_SYSTEM_PERIOD_USEC                      ((uint64_t)(24ll * 60ll * 60ll * 1000ll * 1000ll))
/* 1 hour */
#define  PBROKER_STATS_TX_UPGRADE_USEC                            ((int64_t)(1ll * 60ll * 60ll * 1000ll * 1000ll))

#define  TOTAL_NUM_FF_SENT        3
struct pbroker_stats_tx_cfg {
    char*                                   l_name;
    char*                                   l_otyp;
    int                                     enabled;
    int64_t                                 interval_us;
    struct argo_structure_description*      argo_description;
    int                                     size_of_object;
    argo_log_pre_log_callback_f*            pre_log_cb;
    void*                                   pre_log_cb_cookie;
    int                                     log_immediate;
};

#define PBROKER_STATS_TX_MAX_OBJECTS 17

static struct  pbroker_stats_tx_cfg cfg[PBROKER_STATS_TX_MAX_OBJECTS];
static int     cfg_ready;
extern int     debuglog;
int64_t        pb_stats_feature_enabled = PBROKER_CONFIG_OVERRIDE_STATS_COMPREHENSIVE_DEFAULT;
int64_t        pb_wally_table_stats_upload_enabled = PSE_CONFIG_OVERRIDE_ENABLE_WALLY_TABLE_STATS_UPLOAD_DEFAULT;

static struct argo_structure_description* pbroker_stats_tx_stats_description;

static
struct pbroker_stats_tx_stats {                                      /* _ARGO: object_definition */
    uint64_t stats;                                                      /* _ARGO: integer */
}stats;

extern struct zpn_pbroker_mtunnel_stats mstats;
extern int new_version_available[FILE_CODE_MAX];
extern int new_version_downloaded[FILE_CODE_MAX];
int num_ff_stats_sent;
extern int64_t g_pb_client_close_fini_count;
extern int64_t g_pb_zrdt_client_close_count;
extern int64_t g_pb_client_create_count;
extern int64_t g_pb_client_no_create_count;
extern int64_t g_pb_client_close_count;
extern int64_t g_pb_client_connect_count;
extern int64_t g_pb_client_disconnect_count;
extern int64_t g_pb_client_start_auth_count;
extern int64_t g_pb_client_auth_complete_count;
extern uint8_t g_pb_client_fohh_debug;
extern int64_t g_fconn_alloc_count;
extern int64_t g_fconn_bufferevent_alloc_count;
extern int64_t g_fconn_close_count;
extern int64_t g_fconn_free_count;
extern int64_t g_fconn_direct_close_count;
extern int64_t g_fconn_libevent_close_count;


extern struct argo_structure_description *zpn_zdx_probe_stats_description;



extern struct zpn_pbroker_dns_dispatcher_stats pse_dns_disp_stats;



#include "zpn_private_brokerd/zpn_private_broker_stats_tx_compiled_c.h"

static void
pbroker_stats_tx_reader_cb_log(void*                       void_cookie,
                                 char*                       reason,
                                 int                         status)
{
    struct argo_log *log = void_cookie;
    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();

    if (FOHH_RESULT_NO_ERROR != status) {
        /*
         * Even this below message may be dropped while being sent to the stats broker if the FOHH is in the mood
         * of WOULD_BLOCK. But logging this, so that if any customer is willing to provide /var/log/messages, we
         * will get some insights.
         */
        ZPN_LOG(AL_ERROR, "stats upload to stats broker otyp(%s) name(%s) - dropped",
                      log->l_otyp, log->l_name);
        if (0 == strncmp("pbroker_stats_comprehensive", log->l_name, sizeof("pbroker_stats_comprehensive"))) {
            __sync_add_and_fetch_8(&gs->private_broker_state->num_comprehensive_stats_upload_fails, 1);
        }
    } else {
        PBROKER_DEBUG_STATS_TX("stats upload to stats broker otyp(%s) name(%s), queued into fohh",
                                log->l_otyp, log->l_name);
        if (0 == strncmp("pbroker_stats_comprehensive", log->l_name, sizeof("pbroker_stats_comprehensive"))) {
            __sync_add_and_fetch_8(&gs->private_broker_state->num_comprehensive_stats_upload, 1);
        }
    }

}

int pbroker_app_comprehensive_stats_fill(void* structure_data)
{
    struct zpn_pbroker_comprehensive_stats *out_data = (struct zpn_pbroker_comprehensive_stats *)structure_data;
    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();
    struct zpn_application **apps;
    size_t apps_count;
    int res;

    apps = ZPN_MALLOC(sizeof(*apps) * MAX_CLIENT_APPS);
    apps_count = MAX_CLIENT_APPS;
    res = zpn_broker_client_apps_get_apps_by_customer_gid(gs->customer_id,
                                                          &(apps[0]),
                                                          &apps_count);
    out_data->app_count = res ? 0 : apps_count;
    ZPN_FREE(apps);

    return ZPATH_RESULT_NO_ERROR;
}

int zpn_broker_broker_promote_stats_fill(void* structure_data)
{
    struct zpn_pbroker_comprehensive_stats *out_data = (struct zpn_pbroker_comprehensive_stats *)structure_data;

    out_data->mt_promote_success_count = mstats.mt_promote_success_count;
    out_data->mt_promote_fail_count = mstats.mt_promote_fail_count;
    out_data->mt_promote_terminated_count = mstats.mt_promote_terminated_count;
    out_data->mt_promote_c2c_regex_bypass_count = mstats.mt_promote_c2c_regex_bypass_count;

    return ZPATH_RESULT_NO_ERROR;
}

int pbroker_dispatch_comprehensive_stats_fill(void* structure_data)
{
    struct zpn_pbroker_comprehensive_stats *out_data = (struct zpn_pbroker_comprehensive_stats *)structure_data;
    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();
    struct zpn_private_broker_dispatcher_stats *dispatch_stats = &gs->private_broker_state->dispatcher_stats;

    ZPN_ATOMIC_LOAD(&dispatch_stats->total_cache_dispatch_hit_count, out_data->total_cache_dispatch_hit_count);
    ZPN_ATOMIC_LOAD(&dispatch_stats->total_cache_dispatch_miss_count, out_data->total_cache_dispatch_miss_count);
    ZPN_ATOMIC_LOAD(&dispatch_stats->total_cloud_dispatch_fail_count, out_data->total_cloud_dispatch_fail_count);
    ZPN_ATOMIC_LOAD(&dispatch_stats->total_max_dispatch_exceeded_count, out_data->total_max_dispatch_exceeded_count);
    ZPN_ATOMIC_LOAD(&dispatch_stats->total_cloud_dispatch_count, out_data->total_cloud_dispatch_count);
    ZPN_ATOMIC_LOAD(&dispatch_stats->local_dispatcher_state_eval_avg_time_us, out_data->local_dispatcher_state_eval_avg_time_us);
    ZPN_ATOMIC_LOAD(&dispatch_stats->local_dispatcher_state_eval_max_time_us, out_data->local_dispatcher_state_eval_max_time_us);

    return ZPATH_RESULT_NO_ERROR;
}

int pbroker_mtunnel_comprehensive_stats_fill(void* structure_data)
{
    struct zpn_pbroker_comprehensive_stats *out_data = (struct zpn_pbroker_comprehensive_stats *)structure_data;
    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();
    struct zpn_private_broker_mtunnel_stats *mtstats = &gs->private_broker_state->mtunnel_stats;

    ZPN_ATOMIC_LOAD(&mtstats->total_mtunnels_created, out_data->total_mtunnels_created);
    ZPN_ATOMIC_LOAD(&mtstats->total_mtunnels_freed, out_data->total_mtunnels_freed);
    ZPN_ATOMIC_LOAD(&mtstats->total_mtunnels_reaped_in, out_data->total_mtunnels_reaped_in);
    ZPN_ATOMIC_LOAD(&mtstats->total_mtunnels_reaped_out, out_data->total_mtunnels_reaped_out);
    ZPN_ATOMIC_LOAD(&mtstats->total_mtunnels_active, out_data->total_mtunnels_active);
    ZPN_ATOMIC_LOAD(&mtstats->total_mtunnels_init_failed, out_data->total_mtunnels_init_failed);
    ZPN_ATOMIC_LOAD(&mtstats->approx_mtunnels_peak_active, out_data->approx_mtunnels_peak_active);
    ZPN_ATOMIC_LOAD(&mtstats->total_mtunnels_c2c_regex_bypass, out_data->total_mtunnels_c2c_regex_bypass);

    ZPN_ATOMIC_LOAD(&mtstats->zcc_mtunnels_created, out_data->zcc_mtunnels_created);
    ZPN_ATOMIC_LOAD(&mtstats->zcc_mtunnels_freed, out_data->zcc_mtunnels_freed);
    ZPN_ATOMIC_LOAD(&mtstats->zcc_mtunnels_reaped_in, out_data->zcc_mtunnels_reaped_in);
    ZPN_ATOMIC_LOAD(&mtstats->zcc_mtunnels_reaped_out, out_data->zcc_mtunnels_reaped_out);
    ZPN_ATOMIC_LOAD(&mtstats->zcc_mtunnels_active, out_data->zcc_mtunnels_active);
    ZPN_ATOMIC_LOAD(&mtstats->zcc_mtunnels_init_failed, out_data->zcc_mtunnels_init_failed);
    ZPN_ATOMIC_LOAD(&mtstats->zcc_mtunnels_c2c_regex_bypass, out_data->zcc_mtunnels_c2c_regex_bypass);

    ZPN_ATOMIC_LOAD(&mtstats->ec_mtunnels_created, out_data->ec_mtunnels_created);
    ZPN_ATOMIC_LOAD(&mtstats->ec_mtunnels_freed, out_data->ec_mtunnels_freed);
    ZPN_ATOMIC_LOAD(&mtstats->ec_mtunnels_reaped_in, out_data->ec_mtunnels_reaped_in);
    ZPN_ATOMIC_LOAD(&mtstats->ec_mtunnels_reaped_out, out_data->ec_mtunnels_reaped_out);
    ZPN_ATOMIC_LOAD(&mtstats->ec_mtunnels_active, out_data->ec_mtunnels_active);
    ZPN_ATOMIC_LOAD(&mtstats->ec_mtunnels_init_failed, out_data->ec_mtunnels_init_failed);
    ZPN_ATOMIC_LOAD(&mtstats->ec_mtunnels_c2c_regex_bypass, out_data->ec_mtunnels_c2c_regex_bypass);

    ZPN_ATOMIC_LOAD(&mtstats->vdi_mtunnels_created, out_data->vdi_mtunnels_created);
    ZPN_ATOMIC_LOAD(&mtstats->vdi_mtunnels_freed, out_data->vdi_mtunnels_freed);
    ZPN_ATOMIC_LOAD(&mtstats->vdi_mtunnels_reaped_in, out_data->vdi_mtunnels_reaped_in);
    ZPN_ATOMIC_LOAD(&mtstats->vdi_mtunnels_reaped_out, out_data->vdi_mtunnels_reaped_out);
    ZPN_ATOMIC_LOAD(&mtstats->vdi_mtunnels_active, out_data->vdi_mtunnels_active);
    ZPN_ATOMIC_LOAD(&mtstats->vdi_mtunnels_init_failed, out_data->vdi_mtunnels_init_failed);
    ZPN_ATOMIC_LOAD(&mtstats->vdi_mtunnels_c2c_regex_bypass, out_data->vdi_mtunnels_c2c_regex_bypass);

    ZPN_ATOMIC_LOAD(&mtstats->bc_mtunnels_created, out_data->bc_mtunnels_created);
    ZPN_ATOMIC_LOAD(&mtstats->bc_mtunnels_freed, out_data->bc_mtunnels_freed);
    ZPN_ATOMIC_LOAD(&mtstats->bc_mtunnels_reaped_in, out_data->bc_mtunnels_reaped_in);
    ZPN_ATOMIC_LOAD(&mtstats->bc_mtunnels_reaped_out, out_data->bc_mtunnels_reaped_out);
    ZPN_ATOMIC_LOAD(&mtstats->bc_mtunnels_active, out_data->bc_mtunnels_active);
    ZPN_ATOMIC_LOAD(&mtstats->bc_mtunnels_init_failed, out_data->bc_mtunnels_init_failed);
    ZPN_ATOMIC_LOAD(&mtstats->bc_mtunnels_c2c_regex_bypass, out_data->bc_mtunnels_c2c_regex_bypass);

    ZPN_ATOMIC_LOAD(&mtstats->mt_mtunnels_created, out_data->mt_mtunnels_created);
    ZPN_ATOMIC_LOAD(&mtstats->mt_mtunnels_freed, out_data->mt_mtunnels_freed);
    ZPN_ATOMIC_LOAD(&mtstats->mt_mtunnels_reaped_in, out_data->mt_mtunnels_reaped_in);
    ZPN_ATOMIC_LOAD(&mtstats->mt_mtunnels_reaped_out, out_data->mt_mtunnels_reaped_out);
    ZPN_ATOMIC_LOAD(&mtstats->mt_mtunnels_active, out_data->mt_mtunnels_active);
    ZPN_ATOMIC_LOAD(&mtstats->mt_mtunnels_init_failed, out_data->mt_mtunnels_init_failed);
    ZPN_ATOMIC_LOAD(&mtstats->mt_mtunnels_c2c_regex_bypass, out_data->mt_mtunnels_c2c_regex_bypass);


    ZPN_ATOMIC_LOAD(&mtstats->zp_mtunnels_created, out_data->zp_mtunnels_created);
    ZPN_ATOMIC_LOAD(&mtstats->zp_mtunnels_freed, out_data->zp_mtunnels_freed);
    ZPN_ATOMIC_LOAD(&mtstats->zp_mtunnels_reaped_in, out_data->zp_mtunnels_reaped_in);
    ZPN_ATOMIC_LOAD(&mtstats->zp_mtunnels_reaped_out, out_data->zp_mtunnels_reaped_out);
    ZPN_ATOMIC_LOAD(&mtstats->zp_mtunnels_active, out_data->zp_mtunnels_active);
    ZPN_ATOMIC_LOAD(&mtstats->zp_mtunnels_init_failed, out_data->zp_mtunnels_init_failed);
    ZPN_ATOMIC_LOAD(&mtstats->zp_mtunnels_c2c_regex_bypass, out_data->zp_mtunnels_c2c_regex_bypass);

    ZPN_ATOMIC_LOAD(&mtstats->other_mtunnels_created, out_data->other_mtunnels_created);
    ZPN_ATOMIC_LOAD(&mtstats->other_mtunnels_freed, out_data->other_mtunnels_freed);
    ZPN_ATOMIC_LOAD(&mtstats->other_mtunnels_reaped_in, out_data->other_mtunnels_reaped_in);
    ZPN_ATOMIC_LOAD(&mtstats->other_mtunnels_reaped_out, out_data->other_mtunnels_reaped_out);
    ZPN_ATOMIC_LOAD(&mtstats->other_mtunnels_active, out_data->other_mtunnels_active);
    ZPN_ATOMIC_LOAD(&mtstats->other_mtunnels_init_failed, out_data->other_mtunnels_init_failed);
    ZPN_ATOMIC_LOAD(&mtstats->other_mtunnels_c2c_regex_bypass, out_data->other_mtunnels_c2c_regex_bypass);

    ZPN_ATOMIC_LOAD(&mtstats->c2c_regex_eval_avg_time_us, out_data->c2c_regex_eval_avg_time_us);

    return ZPATH_RESULT_NO_ERROR;
}

static int
pbroker_client_connection_comprehensive_stats_fill(void *structure_data)
{
    struct zpn_pbroker_comprehensive_stats *out_data = (struct zpn_pbroker_comprehensive_stats *)structure_data;
    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();
    struct zpn_private_broker_client_connection_stats *cstats = &gs->private_broker_state->client_connection_stats;

    ZPN_ATOMIC_LOAD(&cstats->total_active_client_connections_count, out_data->total_active_client_connections_count);
    ZPN_ATOMIC_LOAD(&cstats->total_client_connections_init_failed, out_data->total_client_connections_init_failed);
    ZPN_ATOMIC_LOAD(&cstats->total_client_connections_created, out_data->total_client_connections_created);
    ZPN_ATOMIC_LOAD(&cstats->total_client_connections_freed, out_data->total_client_connections_freed);
    ZPN_ATOMIC_LOAD(&cstats->approx_peak_active_client_connections_count, out_data->approx_peak_active_client_connections_count);

    ZPN_ATOMIC_LOAD(&cstats->zcc_active_connections_count, out_data->zcc_active_connections_count);
    ZPN_ATOMIC_LOAD(&cstats->zcc_connections_init_failed, out_data->zcc_connections_init_failed);
    ZPN_ATOMIC_LOAD(&cstats->zcc_connections_created, out_data->zcc_connections_created);
    ZPN_ATOMIC_LOAD(&cstats->zcc_connections_freed, out_data->zcc_connections_freed);

    ZPN_ATOMIC_LOAD(&cstats->edge_connector_active_connections_count, out_data->edge_connector_active_connections_count);
    ZPN_ATOMIC_LOAD(&cstats->edge_connector_connections_init_failed, out_data->edge_connector_connections_init_failed);
    ZPN_ATOMIC_LOAD(&cstats->edge_connector_connections_created, out_data->edge_connector_connections_created);
    ZPN_ATOMIC_LOAD(&cstats->edge_connector_connections_freed, out_data->edge_connector_connections_freed);

    ZPN_ATOMIC_LOAD(&cstats->vdi_active_connections_count, out_data->vdi_active_connections_count);
    ZPN_ATOMIC_LOAD(&cstats->vdi_connections_init_failed, out_data->vdi_connections_init_failed);
    ZPN_ATOMIC_LOAD(&cstats->vdi_connections_created, out_data->vdi_connections_created);
    ZPN_ATOMIC_LOAD(&cstats->vdi_connections_freed, out_data->vdi_connections_freed);

    ZPN_ATOMIC_LOAD(&cstats->branch_connector_active_connections_count, out_data->branch_connector_active_connections_count);
    ZPN_ATOMIC_LOAD(&cstats->branch_connector_connections_init_failed, out_data->branch_connector_connections_init_failed);
    ZPN_ATOMIC_LOAD(&cstats->branch_connector_connections_created, out_data->branch_connector_connections_created);
    ZPN_ATOMIC_LOAD(&cstats->branch_connector_connections_freed, out_data->branch_connector_connections_freed);

    ZPN_ATOMIC_LOAD(&cstats->machine_tunnel_active_connections_count, out_data->machine_tunnel_active_connections_count);
    ZPN_ATOMIC_LOAD(&cstats->machine_tunnel_connections_init_failed, out_data->machine_tunnel_connections_init_failed);
    ZPN_ATOMIC_LOAD(&cstats->machine_tunnel_connections_created, out_data->machine_tunnel_connections_created);
    ZPN_ATOMIC_LOAD(&cstats->machine_tunnel_connections_freed, out_data->machine_tunnel_connections_freed);

    ZPN_ATOMIC_LOAD(&cstats->zapp_partner_active_connections_count, out_data->zapp_partner_active_connections_count);
    ZPN_ATOMIC_LOAD(&cstats->zapp_partner_connections_init_failed, out_data->zapp_partner_connections_init_failed);
    ZPN_ATOMIC_LOAD(&cstats->zapp_partner_connections_created, out_data->zapp_partner_connections_created);
    ZPN_ATOMIC_LOAD(&cstats->zapp_partner_connections_freed, out_data->zapp_partner_connections_freed);

    ZPN_ATOMIC_LOAD(&cstats->other_client_active_connections_count, out_data->other_client_active_connections_count);
    ZPN_ATOMIC_LOAD(&cstats->other_client_connections_init_failed, out_data->other_client_connections_init_failed);
    ZPN_ATOMIC_LOAD(&cstats->other_client_connections_created, out_data->other_client_connections_created);
    ZPN_ATOMIC_LOAD(&cstats->other_client_connections_freed, out_data->other_client_connections_freed);

    return ZPATH_RESULT_NO_ERROR;
}

static int
pbroker_resiliency_stats_fill(void*     cookie,
                                   int       counter,
                                   void*     structure_data)
{
    struct zpn_pse_resiliency_stats *out_data = (struct zpn_pse_resiliency_stats *)structure_data;
    memset(out_data,0,sizeof(struct zpn_pse_resiliency_stats));
    out_data->resiliency_last_start_time = zpn_pse_get_resiliency_mode_start_time();
    out_data->resiliency_last_end_time =   zpn_pse_get_resiliency_mode_end_time();
    out_data->resiliency_activation_count = zpn_pse_get_resiliency_activation_count();
    out_data->resiliency_max_duration = zpn_pse_get_resiliency_max_duration();
    out_data->resiliency_max_duration_start_time = zpn_pse_get_resiliency_max_duration_start_time();

    return ZPATH_RESULT_NO_ERROR;
}


/*
 * Collect pbroker comprehensive stats and upload
 * We have to collect the stats from different places...
 */
static int
pbroker_comprehensive_stats_fill(void*     cookie,
                                   int       counter,
                                   void*     structure_data)
{
    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();
    static int64_t          sys_disk_total_bytes;
    static int              meminfo_read = 0;
    struct zpn_pbroker_comprehensive_stats *out_data = (struct zpn_pbroker_comprehensive_stats *)structure_data;
    struct mmdb_stats mmdb_s;
    uint8_t argo_high_tide_state = 0;

    memset(&mmdb_s, 0, sizeof(struct mmdb_stats));
    get_mmdb_stats(&mmdb_s);

    memset(out_data, 0, sizeof(struct zpn_pbroker_comprehensive_stats));
    out_data->g_pse = gs->private_broker_id;
    out_data->g_cst = gs->customer_id;
    out_data->g_microtenant = gs->private_broker_scope_id == gs->customer_id ? 0 : gs->private_broker_scope_id;
    out_data->cloud_time_us = zpn_cloud_adjusted_epoch_us();

    if (meminfo_read == 0) {
        zpath_system_get_disk_info(NULL, NULL, &sys_disk_total_bytes);
        meminfo_read = 1;
    }
    out_data->total_system_mem_mb = (int32_t)((gs->sys_stats.memtotal_abs_mem)/1024);
    out_data->total_disk_bytes = sys_disk_total_bytes;
    out_data->pse_restart_invalid_version = gs->version_data.restart_invalid_version;
    out_data->denied_version_del_fail = gs->version_data.denied_version_del_fail;

    get_argo_high_tide_state(&argo_high_tide_state);
    out_data->argo_high_tide_state = argo_high_tide_state;

    out_data->imds_disabled = gs->imds_disabled;
    out_data->imdsv2_required = gs->imdsv2_required;
    out_data->disk_id_fail = gs->disk_id_fail;

    zpn_pbroker_data_connection_stats_comprehensive_stats_fill(out_data);
    pbroker_mtunnel_comprehensive_stats_fill(out_data);
    pbroker_dispatch_comprehensive_stats_fill(out_data);
    pbroker_client_connection_comprehensive_stats_fill(out_data);
    pbroker_app_comprehensive_stats_fill(out_data);
    zpn_system_pbroker_comprehensive_stats_fill(structure_data, cookie);

    /* DR stats */
    zpn_private_broker_get_dr_stats(&out_data->dr_activation_on_count,
                                    &out_data->dr_activation_test_count,
                                    &out_data->dr_activation_off_count,
                                    &out_data->dr_activation_err_cnt,
                                    &out_data->dr_activation_req_cnt,
                                    &out_data->dr_activation_resp_cnt,
                                    &out_data->dr_activation_no_resp_cnt,
                                    &out_data->dr_activation_err_resp_cnt,
                                    &out_data->dr_config_auto_dump_count,
                                    &out_data->dr_config_auto_dump_fail_count,
                                    &out_data->dr_config_snapshot_dump_count,
                                    &out_data->dr_config_snapshot_dump_fail_count,
                                    &out_data->dr_config_snapshot_current_count);

    /* MT promote stats */
    zpn_broker_broker_promote_stats_fill(out_data);

    /* mmdb stats */
    out_data->num_geo_queries = mmdb_s.num_geo_queries;
    out_data->num_geo_cc_queries_failed = mmdb_s.num_geo_cc_queries_failed;
    out_data->num_geo_city_queries_failed  = mmdb_s.num_geo_city_queries_failed;
    out_data->num_geo_subdivision_queries_failed  = mmdb_s.num_geo_subdivision_queries_failed;
    out_data->geo_query_rate = mmdb_s.geo_query_rate;
    out_data->num_isp_queries = mmdb_s.num_isp_queries;
    out_data->num_isp_queries_failed = mmdb_s.num_isp_queries_failed;
    out_data->isp_query_rate = mmdb_s.isp_query_rate;

    /* Export the current stats for delta fields derivation */
    export_comprehensive_stats(out_data);

    return ZPATH_RESULT_NO_ERROR;
}

static int zdx_mtr_stats_fill(void *cookie, int counter, void *structure_data)
{
    struct zpn_zdx_mtr_stats *out_data = (struct zpn_zdx_mtr_stats *)structure_data;
    memset(out_data, 0, sizeof(struct zpn_zdx_mtr_stats));

    zpn_zdx_mtr_stats_get(out_data);

    return ZPATH_RESULT_NO_ERROR;
}

static int zdx_cache_stats_fill(void *cookie, int counter, void *structure_data)
{
    struct zpn_zdx_cache_stats *out_data = (struct zpn_zdx_cache_stats *)structure_data;
    memset(out_data, 0, sizeof(struct zpn_zdx_cache_stats));

    memcpy(out_data, zpn_zdx_cache_get_stats(), sizeof(struct zpn_zdx_cache_stats));

    return ZPATH_RESULT_NO_ERROR;
}

static int zpn_pbclient_debug_stats_fill(void *cookie, int counter, void *structure_data)
{
    struct zpn_pbclient_debug_stats *out_data = (struct zpn_pbclient_debug_stats * )structure_data;
    memset(out_data, 0, sizeof(struct zpn_pbclient_debug_stats));
    ZPN_ATOMIC_LOAD(&g_pb_client_close_fini_count,out_data->pb_client_close_fini_count);
    ZPN_ATOMIC_LOAD(&g_pb_zrdt_client_close_count,out_data->pb_zrdt_client_close_count);
    ZPN_ATOMIC_LOAD(&g_pb_client_create_count,out_data->pb_client_create_count);
    ZPN_ATOMIC_LOAD(&g_pb_client_no_create_count,out_data->pb_client_no_create_count);
    ZPN_ATOMIC_LOAD(&g_pb_client_close_count,out_data->pb_client_close_count);
    ZPN_ATOMIC_LOAD(&g_pb_client_connect_count,out_data->pb_client_connect_count);
    ZPN_ATOMIC_LOAD(&g_pb_client_disconnect_count,out_data->pb_client_disconnect_count);
    ZPN_ATOMIC_LOAD(&g_pb_client_start_auth_count,out_data->pb_client_start_auth_count);
    ZPN_ATOMIC_LOAD(&g_pb_client_auth_complete_count,out_data->pb_client_auth_complete_count);

    if ( g_pb_client_fohh_debug )
    {
        ZPN_ATOMIC_LOAD(&g_fconn_alloc_count,out_data->fconn_alloc_count);
        ZPN_ATOMIC_LOAD(&g_fconn_bufferevent_alloc_count,out_data->fconn_bufferevent_alloc_count);
        ZPN_ATOMIC_LOAD(&g_fconn_close_count,out_data->fconn_close_count);
        ZPN_ATOMIC_LOAD(&g_fconn_free_count,out_data->fconn_free_count);
        ZPN_ATOMIC_LOAD(&g_fconn_direct_close_count,out_data->fconn_direct_close_count);
        ZPN_ATOMIC_LOAD(&g_fconn_libevent_close_count,out_data->fconn_libevent_close_count);
    }
    return ZPATH_RESULT_NO_ERROR;
}

static int
pbroker_file_fetch_stats_fill(void*     cookie,
                                   int       counter,
                                   void*     structure_data)
{
    struct zpn_pbroker_file_stats *out_data = (struct zpn_pbroker_file_stats *)structure_data;
    memset(out_data, 0, sizeof(struct zpn_pbroker_file_stats));
    struct  ff_stats fstats[FILE_CODE_MAX];

    get_file_stats(&fstats[MMDB_GEOIP],MMDB_GEOIP);
    get_file_stats(&fstats[MMDB_ISP],MMDB_ISP);
    out_data->total_key_retry_count_geo = fstats[MMDB_GEOIP].total_key_retry_count;
    out_data->total_file_retry_count_geo = fstats[MMDB_GEOIP].total_file_retry_count;
    out_data->num_fetch_fail_geo = fstats[MMDB_GEOIP].num_fetch_fail;
    out_data->key_fail_cnt_geo = fstats[MMDB_GEOIP].key_fail_cnt;
    out_data->num_key_cb_geo = fstats[MMDB_GEOIP].num_key_cb;
    out_data->file_size_geo = fstats[MMDB_GEOIP].file_size;
    out_data->download_time_geo = fstats[MMDB_GEOIP].download_time;
    out_data->download_speed_geo = fstats[MMDB_GEOIP].download_speed;

    out_data->total_key_retry_count_isp = fstats[MMDB_ISP].total_key_retry_count;
    out_data->total_file_retry_count_isp = fstats[MMDB_ISP].total_file_retry_count;
    out_data->num_fetch_fail_isp = fstats[MMDB_ISP].num_fetch_fail;
    out_data->key_fail_cnt_isp = fstats[MMDB_ISP].key_fail_cnt;
    out_data->num_key_cb_isp = fstats[MMDB_ISP].num_key_cb;
    out_data->file_size_isp = fstats[MMDB_ISP].file_size;
    out_data->download_time_isp = fstats[MMDB_ISP].download_time;
    out_data->download_speed_isp = fstats[MMDB_ISP].download_speed;

    out_data->rollback_cnt = get_rollback_cnt();

    return ZPATH_RESULT_NO_ERROR;
}

static int
pbroker_dns_dispatcher_stats_fill(  void*     cookie,
                                    int       counter,
                                    void*     structure_data)
{
    struct zpn_pbroker_dns_dispatcher_stats *out_data = (struct zpn_pbroker_dns_dispatcher_stats *)structure_data;
    memset(out_data, 0 , sizeof(struct zpn_pbroker_dns_dispatcher_stats));

    zpn_pbroker_dns_disp_time_stats_fill(&pse_dns_disp_stats);
    memcpy(out_data, &pse_dns_disp_stats, sizeof(struct zpn_pbroker_dns_dispatcher_stats));

    // Reset the Delta count
    pse_dns_disp_stats.total_dns_public_dispatch_resp_delta = 0;
    pse_dns_disp_stats.total_dns_local_dispatch_resp_delta = 0;

    return ZPATH_RESULT_NO_ERROR;
}

static int
pbroker_svcp_stats_fill(void*     cookie,
                         int       counter,
                         void*     structure_data)
{
    struct zpn_svcp_stats *out_data = (struct zpn_svcp_stats *)structure_data;
    memset(out_data, 0 , sizeof(struct zpn_svcp_stats));
    zpn_broker_client_get_svcp_stats(out_data);

    return ZPATH_RESULT_NO_ERROR;
}

static int
pbroker_stepup_auth_stats_fill(void*     cookie,
                               int       counter,
                               void*     structure_data)
{
    struct zpn_stepup_auth_stats *out_data = (struct zpn_stepup_auth_stats *)structure_data;
    memset(out_data, 0 , sizeof(struct zpn_stepup_auth_stats));
    zpn_broker_client_get_stepup_auth_stats(out_data);

    return ZPATH_RESULT_NO_ERROR;
}

static int
zpn_zdx_probe_stats_fill(void*     cookie,
                         int       counter,
                         void*     structure_data)
{
    struct zpn_zdx_probe_stats *out_data = (struct zpn_zdx_probe_stats *)structure_data;
    memset(out_data, 0 , sizeof(struct zpn_zdx_probe_stats));

    int res = zpn_zdx_probe_stats_get(out_data);

    if(res){
        ZPN_LOG(AL_ERROR, "unable to send zdx probe stats, res=%d", res);
    }
    return ZPATH_RESULT_NO_ERROR;
}

static int
zpn_private_broker_upgrade_stats_fill(void*     cookie,
                                      int       counter,
                                      void*     structure_data)
{
    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();
    struct zpn_private_broker_upgrade_stats *out_data = (struct zpn_private_broker_upgrade_stats *)structure_data;

    out_data->os_upgrade_fail = gs->upgrade_stats.os_upgrade_fail;
    out_data->sarge_upgrade_fail = gs->upgrade_stats.sarge_upgrade_fail;
    out_data->os_upgrade_success = gs->upgrade_stats.os_upgrade_success;
    out_data->sarge_upgrade_success = gs->upgrade_stats.sarge_upgrade_success;
    out_data->os_upgrade_timeout = gs->upgrade_stats.os_upgrade_timeout;
    out_data->sarge_os_cfg_read_fail = gs->upgrade_stats.sarge_os_cfg_read_fail;
    out_data->sudo_path_fail = gs->upgrade_stats.sudo_path_fail;
    out_data->package_manager_path_fail = gs->upgrade_stats.package_manager_path_fail;

    return ZPATH_RESULT_NO_ERROR;
}

static void
pbroker_stats_tx(struct argo_object*            log_object,
                   int64_t                      argo_log_sequence,
                   pbroker_stats_tx_done_cb     done_cb,
                   void*                        done_cb_void_cookie,
                   char*                        done_cb_str_cookie)
{
    int fohh_ret;
    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();

    // check the stats connection state
    if(!pbroker_stats_is_connected()) {
        // pbroker doesn't have connection with its stats broker
        return;
    }
    // dump the object that we are transmitting if pbroker debug bit stats_tx is enabled
    if (debuglog) {
        char dump[8000] = {0};
        if (argo_object_dump(log_object, dump, sizeof(dump) - 1, NULL, 0) == ARGO_RESULT_NO_ERROR) {
            PBROKER_DEBUG_STATS_TX("Pbroker Stats Tx: %s", dump);
        }
    }

    fohh_ret = fohh_argo_serialize_object(gs->private_broker_state->broker_stats, log_object, argo_log_sequence,
                                          fohh_queue_element_type_control);
    if (done_cb) done_cb(done_cb_void_cookie, done_cb_str_cookie, fohh_ret);
    stats.stats++;
}

static void zpn_pb_stats_config_override_monitor(int64_t pbroker_id, int64_t customer_gid)
{
    zpath_config_override_monitor_int(PBROKER_CONFIG_OVERRIDE_STATS_COMPREHENSIVE,
                                      &pb_stats_feature_enabled,
                                      NULL,
                                      PBROKER_CONFIG_OVERRIDE_STATS_COMPREHENSIVE_DEFAULT,
                                      (int64_t) pbroker_id,
                                      (int64_t) customer_gid,
                                      (int64_t) ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                      (int64_t) 0);

}

static void zpn_pb_wally_table_stats_upload_config_override_monitor(int64_t pbroker_id, int64_t customer_gid)
{
    zpath_config_override_monitor_int(PSE_CONFIG_OVERRIDE_ENABLE_WALLY_TABLE_STATS_UPLOAD,
                                      &pb_wally_table_stats_upload_enabled,
                                      NULL,
                                      PSE_CONFIG_OVERRIDE_ENABLE_WALLY_TABLE_STATS_UPLOAD_DEFAULT,
                                      (int64_t) pbroker_id,
                                      ZPN_BROKER_GET_GROUP_GID(),
                                      (int64_t) customer_gid,
                                      (int64_t) ZPATH_GLOBAL_CONFIG_OVERRIDE_GID,
                                      (int64_t) 0);

}

/*
 * This is a filter for all the stats messages that are going out of the pbroker. We need this because the
 * libraries that we rely on could register for a stats upload which we don't intend to be useful for the pbroker.
 * This function will make sure that pbroker know exactly what it is sending out of the system - so explicitly check
 * for each and every message going out.
 */
int
pbroker_stats_tx_reader_cb(struct argo_object*    log_object,
                             void*                  callback_cookie,
                             int64_t                argo_log_sequence)
{
    struct argo_log *log = log_object->base_structure_void;
    int             upload_ok;
    size_t          cfg_iter;

    upload_ok = 0;

    if (0 == cfg_ready) {
        PBROKER_ASSERT_SOFT(0, "dropped stats as config is not yet initialized");
        return ZPATH_RESULT_NO_ERROR;
    }
    for (cfg_iter = 0; cfg_iter < PBROKER_STATS_TX_MAX_OBJECTS; cfg_iter++) {
        if (0 != strcmp(cfg[cfg_iter].l_name, log->l_name)) {
            continue;
        }

        if ((cfg[cfg_iter].l_otyp) && (0 != strcmp(cfg[cfg_iter].l_otyp, log->l_otyp))) {
            continue;
        }

        if (0 == cfg[cfg_iter].enabled) {
            continue;
        }

        if (0 == strcmp(log->l_name, "pbroker_stats_comprehensive")) {
            // check the config flag and allow comprehensive_stats tx only when config flag is enabled.
            if (pb_stats_feature_enabled) {
                upload_ok = 1;
            }
        } else if (0 == strcmp(log->l_name, "pbroker_stats_file_fetch")) {
            //Everytime a new version is available, send the stats some time (limit 3 for now) AFTER the file was fetched successfully
            //We continuously send the stats when the attempt is ongoing
            if(new_version_available[MMDB_GEOIP] || new_version_available[MMDB_ISP]){
                    num_ff_stats_sent = 0;
                    new_version_available[MMDB_GEOIP] = 0;
                    new_version_available[MMDB_ISP] = 0;
            }
            if(num_ff_stats_sent < TOTAL_NUM_FF_SENT){
                upload_ok = 1;
                //we only start the countdown once we know that the file was downloaded successfully
                if(new_version_downloaded[MMDB_GEOIP] && new_version_downloaded[MMDB_ISP]){
                    num_ff_stats_sent++; // files are downloaded, start the countdown
                }else{
                    num_ff_stats_sent = 0; // do not start the countdown if the file is not successfully downloaded
                }
            }else{
                upload_ok = 0;
            }
        } else {
            upload_ok = 1;
        }
        goto done;
    }

    if ((0 == strncmp("zpn_event_log", log->l_otyp, sizeof("zpn_event_log"))) ||
        (0 == strncmp("zpn_event_stats", log->l_name, sizeof("zpn_event_stats"))) ||
        (0 == strncmp("app_thread_stats", log->l_otyp, sizeof("app_thread_stats"))) ||
        ((0 == strncmp("zthread_rusage", log->l_otyp, sizeof("zthread_rusage"))) &&
         (0 == strncmp("rusage_thread_all", log->l_name, sizeof("rusage_thread_all"))) ) ||
        (0 == strncmp("zpath_debug_memory_allocator_stats", log->l_otyp, sizeof("zpath_debug_memory_allocator_stats"))) ||
        (0 == strncmp("zpe_policy_build_stats", log->l_otyp, sizeof("zpe_policy_build_stats"))) ||
        (0 == strncmp("zpe_policy_config_change_stats", log->l_otyp, sizeof("zpe_policy_config_change_stats"))) ||
        (0 == strncmp("zpn_application_search_stats", log->l_otyp, sizeof("zpn_application_search_stats"))) ||
        (0 == strncmp("zpn_event_stats", log->l_name, sizeof("zpn_event_stats"))) ||
        (0 == strncmp("zpe_policy_evaluate_stats", log->l_otyp, sizeof("zpe_policy_evaluate_stats"))) ||
        (0 == strncmp("zpn_c2c_fqdn_bypass_cache_stats", log->l_otyp, sizeof("zpn_c2c_fqdn_bypass_cache_stats"))) ||
        (0 == strncmp("zpn_broker_mtunnel_type_stats", log->l_otyp, sizeof("zpn_broker_mtunnel_type_stats"))) ||
        (0 == strncmp("zpn_broker_client_app_download_stats", log->l_otyp, sizeof("zpn_broker_client_app_download_stats"))) ||
        (0 == strncmp("zpn_broker_client_app_re_download_stats", log->l_otyp, sizeof("zpn_broker_client_app_re_download_stats"))) ||
        (0 == strncmp("zpn_broker_client_stats", log->l_otyp, sizeof("zpn_broker_client_stats"))) ||
        (0 == strncmp("zpn_user_risk_stats", log->l_otyp, sizeof("zpn_user_risk_stats"))) ||
        (0 == strncmp("zpn_aae_profile_conclusion_stats", log->l_otyp, sizeof("zpn_aae_profile_conclusion_stats"))) ||
        (0 == strncmp("zpn_workload_tag_group_stats", log->l_otyp, sizeof("zpn_workload_tag_group_stats"))) ||
        (0 == strncmp("fohh_tracker_stats", log->l_otyp, sizeof("fohh_tracker_stats"))) ||
        (pb_wally_table_stats_upload_enabled && (0 == strncmp("wally_table_stats", log->l_otyp, sizeof("wally_table_stats")))) ) {
        upload_ok = 1;
    }

done:
    if (upload_ok) {
        pbroker_stats_tx(log_object, argo_log_sequence, pbroker_stats_tx_reader_cb_log, log, "");
    }

    return ZPATH_RESULT_NO_ERROR;
}

static int
pbroker_stats_tx_dump(struct zpath_debug_state*   request_state,
                        const char**                query_values,
                        int                         query_value_count,
                        void*                       cookie)
{
    char jsonout[10000];

    if (ARGO_RESULT_NO_ERROR == argo_structure_dump(pbroker_stats_tx_stats_description, &stats, jsonout,
                                                    sizeof(jsonout), NULL, 1)){
        ZDP("%s\n", jsonout);
    }

    return ZPATH_RESULT_NO_ERROR;
}

static int
pbroker_stats_client_connection_stats_reset(struct zpath_debug_state*   request_state,
                                            const char**                query_values,
                                            int                         query_value_count,
                                            void*                       cookie)
{
    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();

    /* Reset zpn_pbroker_client_connection_stats */
    ZPN_ATOMIC_STORE(&gs->private_broker_state->client_connection_stats.total_active_client_connections_count, 0);
    ZPN_ATOMIC_STORE(&gs->private_broker_state->client_connection_stats.total_client_connections_init_failed, 0);
    ZPN_ATOMIC_STORE(&gs->private_broker_state->client_connection_stats.total_client_connections_created, 0);
    ZPN_ATOMIC_STORE(&gs->private_broker_state->client_connection_stats.total_client_connections_freed, 0);
    ZPN_ATOMIC_STORE(&gs->private_broker_state->client_connection_stats.approx_peak_active_client_connections_count, 0);

    ZPN_ATOMIC_STORE(&gs->private_broker_state->client_connection_stats.zcc_connections_created, 0);
    ZPN_ATOMIC_STORE(&gs->private_broker_state->client_connection_stats.zcc_connections_freed, 0);
    ZPN_ATOMIC_STORE(&gs->private_broker_state->client_connection_stats.zcc_active_connections_count, 0);
    ZPN_ATOMIC_STORE(&gs->private_broker_state->client_connection_stats.zcc_connections_init_failed, 0);

    ZPN_ATOMIC_STORE(&gs->private_broker_state->client_connection_stats.branch_connector_connections_created, 0);
    ZPN_ATOMIC_STORE(&gs->private_broker_state->client_connection_stats.branch_connector_connections_freed, 0);
    ZPN_ATOMIC_STORE(&gs->private_broker_state->client_connection_stats.branch_connector_active_connections_count, 0);
    ZPN_ATOMIC_STORE(&gs->private_broker_state->client_connection_stats.branch_connector_connections_init_failed, 0);

    ZPN_ATOMIC_STORE(&gs->private_broker_state->client_connection_stats.edge_connector_connections_created, 0);
    ZPN_ATOMIC_STORE(&gs->private_broker_state->client_connection_stats.edge_connector_connections_freed, 0);
    ZPN_ATOMIC_STORE(&gs->private_broker_state->client_connection_stats.edge_connector_active_connections_count, 0);
    ZPN_ATOMIC_STORE(&gs->private_broker_state->client_connection_stats.edge_connector_connections_init_failed, 0);

    ZPN_ATOMIC_STORE(&gs->private_broker_state->client_connection_stats.vdi_connections_created, 0);
    ZPN_ATOMIC_STORE(&gs->private_broker_state->client_connection_stats.vdi_connections_freed, 0);
    ZPN_ATOMIC_STORE(&gs->private_broker_state->client_connection_stats.vdi_active_connections_count, 0);
    ZPN_ATOMIC_STORE(&gs->private_broker_state->client_connection_stats.vdi_connections_init_failed, 0);

    ZPN_ATOMIC_STORE(&gs->private_broker_state->client_connection_stats.machine_tunnel_connections_created, 0);
    ZPN_ATOMIC_STORE(&gs->private_broker_state->client_connection_stats.machine_tunnel_connections_freed, 0);
    ZPN_ATOMIC_STORE(&gs->private_broker_state->client_connection_stats.machine_tunnel_active_connections_count, 0);
    ZPN_ATOMIC_STORE(&gs->private_broker_state->client_connection_stats.machine_tunnel_connections_init_failed, 0);

    ZPN_ATOMIC_STORE(&gs->private_broker_state->client_connection_stats.zapp_partner_connections_created, 0);
    ZPN_ATOMIC_STORE(&gs->private_broker_state->client_connection_stats.zapp_partner_connections_freed, 0);
    ZPN_ATOMIC_STORE(&gs->private_broker_state->client_connection_stats.zapp_partner_active_connections_count, 0);
    ZPN_ATOMIC_STORE(&gs->private_broker_state->client_connection_stats.zapp_partner_connections_init_failed, 0);

    ZPN_ATOMIC_STORE(&gs->private_broker_state->client_connection_stats.other_client_connections_created, 0);
    ZPN_ATOMIC_STORE(&gs->private_broker_state->client_connection_stats.other_client_connections_freed, 0);
    ZPN_ATOMIC_STORE(&gs->private_broker_state->client_connection_stats.other_client_active_connections_count, 0);
    ZPN_ATOMIC_STORE(&gs->private_broker_state->client_connection_stats.other_client_connections_init_failed, 0);

    return ZPATH_RESULT_NO_ERROR;
}

static int
pbroker_stats_pblient_debug_stats_reset(struct zpath_debug_state*   request_state,
                                  const char**                query_values,
                                  int                         query_value_count,
                                  void*                       cookie)
{
    ZPN_ATOMIC_STORE(&g_pb_client_close_fini_count,0);
    ZPN_ATOMIC_STORE(&g_pb_zrdt_client_close_count,0);
    ZPN_ATOMIC_STORE(&g_pb_client_create_count,0);
    ZPN_ATOMIC_STORE(&g_pb_client_no_create_count,0);
    ZPN_ATOMIC_STORE(&g_pb_client_close_count,0);
    ZPN_ATOMIC_STORE(&g_pb_client_connect_count,0);
    ZPN_ATOMIC_STORE(&g_pb_client_disconnect_count,0);
    ZPN_ATOMIC_STORE(&g_pb_client_start_auth_count,0);
    ZPN_ATOMIC_STORE(&g_pb_client_auth_complete_count,0);

    if ( g_pb_client_fohh_debug )
    {
        ZPN_ATOMIC_STORE(&g_fconn_alloc_count,0);
        ZPN_ATOMIC_STORE(&g_fconn_bufferevent_alloc_count,0);
        ZPN_ATOMIC_STORE(&g_fconn_close_count,0);
        ZPN_ATOMIC_STORE(&g_fconn_free_count,0);
        ZPN_ATOMIC_STORE(&g_fconn_direct_close_count,0);
        ZPN_ATOMIC_STORE(&g_fconn_libevent_close_count,0);
    }
    return ZPATH_RESULT_NO_ERROR;
}

static int
pbroker_stats_mtunnel_stats_reset(struct zpath_debug_state*   request_state,
                                  const char**                query_values,
                                  int                         query_value_count,
                                  void*                       cookie)
{
    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();
    struct zpn_private_broker_mtunnel_stats *mtstats = &gs->private_broker_state->mtunnel_stats;
    struct zpn_private_broker_dispatcher_stats *dispatch_stats = &gs->private_broker_state->dispatcher_stats;


    ZPN_ATOMIC_STORE(&mtstats->total_mtunnels_created, 0);
    ZPN_ATOMIC_STORE(&mtstats->total_mtunnels_freed, 0);
    ZPN_ATOMIC_STORE(&mtstats->total_mtunnels_reaped_in, 0);
    ZPN_ATOMIC_STORE(&mtstats->total_mtunnels_reaped_out, 0);
    ZPN_ATOMIC_STORE(&mtstats->total_mtunnels_active, 0);
    ZPN_ATOMIC_STORE(&mtstats->total_mtunnels_init_failed, 0);
    ZPN_ATOMIC_STORE(&mtstats->approx_mtunnels_peak_active, 0);
    ZPN_ATOMIC_STORE(&mtstats->total_mtunnels_c2c_regex_bypass, 0);

    ZPN_ATOMIC_STORE(&mtstats->zcc_mtunnels_created, 0);
    ZPN_ATOMIC_STORE(&mtstats->zcc_mtunnels_freed, 0);
    ZPN_ATOMIC_STORE(&mtstats->zcc_mtunnels_reaped_in, 0);
    ZPN_ATOMIC_STORE(&mtstats->zcc_mtunnels_reaped_out, 0);
    ZPN_ATOMIC_STORE(&mtstats->zcc_mtunnels_active, 0);
    ZPN_ATOMIC_STORE(&mtstats->zcc_mtunnels_init_failed, 0);
    ZPN_ATOMIC_STORE(&mtstats->zcc_mtunnels_c2c_regex_bypass, 0);

    ZPN_ATOMIC_STORE(&mtstats->ec_mtunnels_created, 0);
    ZPN_ATOMIC_STORE(&mtstats->ec_mtunnels_freed, 0);
    ZPN_ATOMIC_STORE(&mtstats->ec_mtunnels_reaped_in, 0);
    ZPN_ATOMIC_STORE(&mtstats->ec_mtunnels_reaped_out, 0);
    ZPN_ATOMIC_STORE(&mtstats->ec_mtunnels_active, 0);
    ZPN_ATOMIC_STORE(&mtstats->ec_mtunnels_init_failed, 0);
    ZPN_ATOMIC_STORE(&mtstats->ec_mtunnels_c2c_regex_bypass, 0);

    ZPN_ATOMIC_STORE(&mtstats->vdi_mtunnels_created, 0);
    ZPN_ATOMIC_STORE(&mtstats->vdi_mtunnels_freed, 0);
    ZPN_ATOMIC_STORE(&mtstats->vdi_mtunnels_reaped_in, 0);
    ZPN_ATOMIC_STORE(&mtstats->vdi_mtunnels_reaped_out, 0);
    ZPN_ATOMIC_STORE(&mtstats->vdi_mtunnels_active, 0);
    ZPN_ATOMIC_STORE(&mtstats->vdi_mtunnels_init_failed, 0);
    ZPN_ATOMIC_STORE(&mtstats->vdi_mtunnels_c2c_regex_bypass, 0);

    ZPN_ATOMIC_STORE(&mtstats->bc_mtunnels_created, 0);
    ZPN_ATOMIC_STORE(&mtstats->bc_mtunnels_freed, 0);
    ZPN_ATOMIC_STORE(&mtstats->bc_mtunnels_reaped_in, 0);
    ZPN_ATOMIC_STORE(&mtstats->bc_mtunnels_reaped_out, 0);
    ZPN_ATOMIC_STORE(&mtstats->bc_mtunnels_active, 0);
    ZPN_ATOMIC_STORE(&mtstats->bc_mtunnels_init_failed, 0);
    ZPN_ATOMIC_STORE(&mtstats->bc_mtunnels_c2c_regex_bypass, 0);

    ZPN_ATOMIC_STORE(&mtstats->mt_mtunnels_created, 0);
    ZPN_ATOMIC_STORE(&mtstats->mt_mtunnels_freed, 0);
    ZPN_ATOMIC_STORE(&mtstats->mt_mtunnels_reaped_in, 0);
    ZPN_ATOMIC_STORE(&mtstats->mt_mtunnels_reaped_out, 0);
    ZPN_ATOMIC_STORE(&mtstats->mt_mtunnels_active, 0);
    ZPN_ATOMIC_STORE(&mtstats->mt_mtunnels_init_failed, 0);
    ZPN_ATOMIC_STORE(&mtstats->mt_mtunnels_c2c_regex_bypass, 0);

    ZPN_ATOMIC_STORE(&mtstats->zp_mtunnels_created, 0);
    ZPN_ATOMIC_STORE(&mtstats->zp_mtunnels_freed, 0);
    ZPN_ATOMIC_STORE(&mtstats->zp_mtunnels_reaped_in, 0);
    ZPN_ATOMIC_STORE(&mtstats->zp_mtunnels_reaped_out, 0);
    ZPN_ATOMIC_STORE(&mtstats->zp_mtunnels_active, 0);
    ZPN_ATOMIC_STORE(&mtstats->zp_mtunnels_init_failed, 0);
    ZPN_ATOMIC_STORE(&mtstats->zp_mtunnels_c2c_regex_bypass, 0);

    ZPN_ATOMIC_STORE(&mtstats->other_mtunnels_created, 0);
    ZPN_ATOMIC_STORE(&mtstats->other_mtunnels_freed, 0);
    ZPN_ATOMIC_STORE(&mtstats->other_mtunnels_reaped_in, 0);
    ZPN_ATOMIC_STORE(&mtstats->other_mtunnels_reaped_out, 0);
    ZPN_ATOMIC_STORE(&mtstats->other_mtunnels_active, 0);
    ZPN_ATOMIC_STORE(&mtstats->other_mtunnels_init_failed, 0);
    ZPN_ATOMIC_STORE(&mtstats->other_mtunnels_c2c_regex_bypass, 0);

    ZPN_ATOMIC_STORE(&dispatch_stats->total_cache_dispatch_hit_count, 0);
    ZPN_ATOMIC_STORE(&dispatch_stats->total_cache_dispatch_miss_count, 0);
    ZPN_ATOMIC_STORE(&dispatch_stats->total_max_dispatch_exceeded_count, 0);
    ZPN_ATOMIC_STORE(&dispatch_stats->total_cloud_dispatch_count, 0);
    ZPN_ATOMIC_STORE(&dispatch_stats->total_cloud_dispatch_fail_count, 0);
    ZPN_ATOMIC_STORE(&dispatch_stats->local_dispatcher_state_eval_avg_time_us, 0);
    ZPN_ATOMIC_STORE(&dispatch_stats->local_dispatcher_state_eval_max_time_us, 0);

    ZPN_ATOMIC_STORE(&mtstats->c2c_regex_eval_avg_time_us, 0);

    return ZPATH_RESULT_NO_ERROR;
}

static int
pbroker_stats_client_connection_stats_dump(struct zpath_debug_state*   request_state,
                                           const char**                query_values,
                                           int                         query_value_count,
                                           void*                       cookie)
{
    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();
    struct zpn_private_broker_client_connection_stats *cstats = &gs->private_broker_state->client_connection_stats;
    uint64_t total_active_client_connections_count = 0, total_client_connections_init_failed = 0, total_client_connections_created = 0, total_client_connections_freed = 0, approx_peak_active_client_connections_count = 0;
    uint64_t zcc_active_connections_count = 0, zcc_connections_init_failed = 0, zcc_connections_created = 0, zcc_connections_freed = 0;
    uint64_t edge_connector_active_connections_count = 0, edge_connector_connections_init_failed = 0, edge_connector_connections_created = 0, edge_connector_connections_freed = 0;
    uint64_t branch_connector_active_connections_count = 0, branch_connector_connections_init_failed = 0, branch_connector_connections_created = 0, branch_connector_connections_freed = 0;
    uint64_t machine_tunnel_active_connections_count = 0, machine_tunnel_connections_init_failed = 0, machine_tunnel_connections_created = 0, machine_tunnel_connections_freed = 0;
    uint64_t zapp_partner_active_connections_count = 0, zapp_partner_connections_init_failed = 0, zapp_partner_connections_created = 0, zapp_partner_connections_freed = 0;
    uint64_t vdi_active_connections_count = 0, vdi_connections_init_failed = 0, vdi_connections_created = 0, vdi_connections_freed = 0;
    uint64_t other_client_active_connections_count = 0, other_client_connections_init_failed = 0, other_client_connections_created = 0, other_client_connections_freed = 0;

    ZPN_ATOMIC_LOAD(&cstats->total_active_client_connections_count, total_active_client_connections_count);
    ZPN_ATOMIC_LOAD(&cstats->total_client_connections_init_failed, total_client_connections_init_failed);
    ZPN_ATOMIC_LOAD(&cstats->total_client_connections_created, total_client_connections_created);
    ZPN_ATOMIC_LOAD(&cstats->total_client_connections_freed, total_client_connections_freed);
    ZPN_ATOMIC_LOAD(&cstats->approx_peak_active_client_connections_count, approx_peak_active_client_connections_count);

    ZPN_ATOMIC_LOAD(&cstats->zcc_active_connections_count, zcc_active_connections_count);
    ZPN_ATOMIC_LOAD(&cstats->zcc_connections_init_failed, zcc_connections_init_failed);
    ZPN_ATOMIC_LOAD(&cstats->zcc_connections_created, zcc_connections_created);
    ZPN_ATOMIC_LOAD(&cstats->zcc_connections_freed, zcc_connections_freed);

    ZPN_ATOMIC_LOAD(&cstats->edge_connector_active_connections_count, edge_connector_active_connections_count);
    ZPN_ATOMIC_LOAD(&cstats->edge_connector_connections_init_failed, edge_connector_connections_init_failed);
    ZPN_ATOMIC_LOAD(&cstats->edge_connector_connections_created, edge_connector_connections_created);
    ZPN_ATOMIC_LOAD(&cstats->edge_connector_connections_freed, edge_connector_connections_freed);

    ZPN_ATOMIC_LOAD(&cstats->branch_connector_active_connections_count, branch_connector_active_connections_count);
    ZPN_ATOMIC_LOAD(&cstats->branch_connector_connections_init_failed, branch_connector_connections_init_failed);
    ZPN_ATOMIC_LOAD(&cstats->branch_connector_connections_created, branch_connector_connections_created);
    ZPN_ATOMIC_LOAD(&cstats->branch_connector_connections_freed, branch_connector_connections_freed);

    ZPN_ATOMIC_LOAD(&cstats->machine_tunnel_active_connections_count, machine_tunnel_active_connections_count);
    ZPN_ATOMIC_LOAD(&cstats->machine_tunnel_connections_init_failed, machine_tunnel_connections_init_failed);
    ZPN_ATOMIC_LOAD(&cstats->machine_tunnel_connections_created, machine_tunnel_connections_created);
    ZPN_ATOMIC_LOAD(&cstats->machine_tunnel_connections_freed, machine_tunnel_connections_freed);

    ZPN_ATOMIC_LOAD(&cstats->vdi_active_connections_count, vdi_active_connections_count);
    ZPN_ATOMIC_LOAD(&cstats->vdi_connections_init_failed, vdi_connections_init_failed);
    ZPN_ATOMIC_LOAD(&cstats->vdi_connections_created, vdi_connections_created);
    ZPN_ATOMIC_LOAD(&cstats->vdi_connections_freed, vdi_connections_freed);

    ZPN_ATOMIC_LOAD(&cstats->zapp_partner_active_connections_count, zapp_partner_active_connections_count);
    ZPN_ATOMIC_LOAD(&cstats->zapp_partner_connections_init_failed, zapp_partner_connections_init_failed);
    ZPN_ATOMIC_LOAD(&cstats->zapp_partner_connections_created, zapp_partner_connections_created);
    ZPN_ATOMIC_LOAD(&cstats->zapp_partner_connections_freed, zapp_partner_connections_freed);

    ZPN_ATOMIC_LOAD(&cstats->other_client_active_connections_count, other_client_active_connections_count);
    ZPN_ATOMIC_LOAD(&cstats->other_client_connections_init_failed, other_client_connections_init_failed);
    ZPN_ATOMIC_LOAD(&cstats->other_client_connections_created, other_client_connections_created);
    ZPN_ATOMIC_LOAD(&cstats->other_client_connections_freed, other_client_connections_freed);

    ZDP("pbroker client connection statistics\n");
    ZDP("------------------------------------\n");
    ZDP("Total client connections created:            %"PRIdLEAST64"\n", total_client_connections_created);
    ZDP("Total client connections freed:              %"PRIdLEAST64"\n", total_client_connections_freed);
    ZDP("Total client connections init failed:        %"PRIdLEAST64"\n", total_client_connections_init_failed);
    ZDP("Total active client connections count:       %"PRIdLEAST64"\n", total_active_client_connections_count);
    ZDP("Approx peak active client connections count: %"PRIdLEAST64"\n", approx_peak_active_client_connections_count);
    ZDP("ZCC connections created:                     %"PRIdLEAST64"\n", zcc_connections_created);
    ZDP("ZCC connections freed:                       %"PRIdLEAST64"\n", zcc_connections_freed);
    ZDP("ZCC connections init failed:                 %"PRIdLEAST64"\n", zcc_connections_init_failed);
    ZDP("ZCC active connections count:                %"PRIdLEAST64"\n", zcc_active_connections_count);
    ZDP("Edge connector connections created:          %"PRIdLEAST64"\n", edge_connector_connections_created);
    ZDP("Edge connector connections freed:            %"PRIdLEAST64"\n", edge_connector_connections_freed);
    ZDP("Edge connector connections init failed:      %"PRIdLEAST64"\n", edge_connector_connections_init_failed);
    ZDP("Edge connector active connections count:     %"PRIdLEAST64"\n", edge_connector_active_connections_count);
    ZDP("Branch connector connections created:        %"PRIdLEAST64"\n", branch_connector_connections_created);
    ZDP("Branch connector connections freed:          %"PRIdLEAST64"\n", branch_connector_connections_freed);
    ZDP("Branch connector connections init failed:    %"PRIdLEAST64"\n", branch_connector_connections_init_failed);
    ZDP("Branch connector active connections count:   %"PRIdLEAST64"\n", branch_connector_active_connections_count);
    ZDP("VDI connections created:                     %"PRIdLEAST64"\n", vdi_connections_created);
    ZDP("VDI connections freed:                       %"PRIdLEAST64"\n", vdi_connections_freed);
    ZDP("VDI connections init failed:                 %"PRIdLEAST64"\n", vdi_connections_init_failed);
    ZDP("VDI active connections count:                %"PRIdLEAST64"\n", vdi_active_connections_count);
    ZDP("Machine tunnel connections created:          %"PRIdLEAST64"\n", machine_tunnel_connections_created);
    ZDP("Machine tunnel connections freed:            %"PRIdLEAST64"\n", machine_tunnel_connections_freed);
    ZDP("Machine tunnel connections init failed:      %"PRIdLEAST64"\n", machine_tunnel_connections_init_failed);
    ZDP("Machine tunnel active connections count:     %"PRIdLEAST64"\n", machine_tunnel_active_connections_count);
    ZDP("Zapp Partner connections created:            %"PRIdLEAST64"\n", zapp_partner_connections_created);
    ZDP("Zapp Partner connections freed:              %"PRIdLEAST64"\n", zapp_partner_connections_freed);
    ZDP("Zapp Partner connections init failed:        %"PRIdLEAST64"\n", zapp_partner_connections_init_failed);
    ZDP("Zapp Partner active connections count:       %"PRIdLEAST64"\n", zapp_partner_active_connections_count);
    ZDP("Other client connections created:            %"PRIdLEAST64"\n", other_client_connections_created);
    ZDP("Other client connections freed:              %"PRIdLEAST64"\n", other_client_connections_freed);
    ZDP("Other client connections init failed:        %"PRIdLEAST64"\n", other_client_connections_init_failed);
    ZDP("Other client active connections count:       %"PRIdLEAST64"\n", other_client_active_connections_count);

    return ZPATH_RESULT_NO_ERROR;
}

static int
pbroker_stats_dispatcher_stats_dump(struct zpath_debug_state*   request_state,
                                 const char**                query_values,
                                 int                         query_value_count,
                                 void*                       cookie)
{
    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();
    struct zpn_private_broker_dispatcher_stats *dispatch_stats = &gs->private_broker_state->dispatcher_stats;
    uint64_t total_cache_dispatch_count = 0, total_cache_dispatch_miss_count = 0, total_max_dispatch_exceeded_count = 0;
    uint64_t total_cloud_dispatch_count = 0,  total_cloud_dispatch_fail_count = 0;
    uint64_t local_dispatcher_state_eval_max_time_us = 0,  local_dispatcher_state_eval_avg_time_us = 0;
    ZPN_ATOMIC_LOAD(&dispatch_stats->total_cache_dispatch_hit_count,total_cache_dispatch_count);
    ZPN_ATOMIC_LOAD(&dispatch_stats->total_cache_dispatch_miss_count,total_cache_dispatch_miss_count);
    ZPN_ATOMIC_LOAD(&dispatch_stats->total_max_dispatch_exceeded_count,total_max_dispatch_exceeded_count);
    ZPN_ATOMIC_LOAD(&dispatch_stats->total_cloud_dispatch_count,total_cloud_dispatch_count);
    ZPN_ATOMIC_LOAD(&dispatch_stats->total_cloud_dispatch_fail_count,total_cloud_dispatch_fail_count);
    ZPN_ATOMIC_LOAD(&dispatch_stats->local_dispatcher_state_eval_avg_time_us,local_dispatcher_state_eval_avg_time_us);
    ZPN_ATOMIC_LOAD(&dispatch_stats->local_dispatcher_state_eval_max_time_us,local_dispatcher_state_eval_max_time_us);

    ZDP("pbroker dispatcher statistics\n");
    ZDP("------------------------------------\n");
    ZDP("Total cache dispatch hit count :        %"PRIdLEAST64"\n",total_cache_dispatch_count );
    ZDP("Total cache dispatch miss count :       %"PRIdLEAST64"\n",total_cache_dispatch_miss_count );
    ZDP("Total max dispatch exceeded count :     %"PRIdLEAST64"\n",total_max_dispatch_exceeded_count );
    ZDP("Total cloud dispatch count :            %"PRIdLEAST64"\n",total_cloud_dispatch_count );
    ZDP("Total cloud dispatch fail count :       %"PRIdLEAST64"\n",total_cloud_dispatch_fail_count );
    ZDP("Local dispatch state eval avg time :    %"PRIdLEAST64"\n",local_dispatcher_state_eval_avg_time_us);
    ZDP("Local dispatch state max eval time :    %"PRIdLEAST64"\n",local_dispatcher_state_eval_max_time_us);

    return ZPATH_RESULT_NO_ERROR;
}

static int pbroker_pbclient_debug_stats_dump(struct zpath_debug_state*   request_state,
                                 const char**                query_values,
                                 int                         query_value_count,
                                 void*                       cookie)
{
    int64_t pb_client_close_fini_count = 0, pb_zrdt_client_close_count = 0, pb_client_create_count = 0;
    int64_t pb_client_no_create_count = 0, pb_client_close_count = 0, pb_client_connect_count = 0;
    int64_t pb_client_disconnect_count = 0, pb_client_start_auth_count = 0, pb_client_auth_complete_count = 0;
    int64_t fconn_alloc_count = 0;
    int64_t fconn_bufferevent_alloc_count = 0;
    int64_t fconn_close_count = 0;
    int64_t fconn_free_count = 0;
    int64_t fconn_direct_close_count = 0;
    int64_t fconn_libevent_close_count = 0;

    ZPN_ATOMIC_LOAD(&g_pb_client_close_fini_count,pb_client_close_fini_count);
    ZPN_ATOMIC_LOAD(&g_pb_zrdt_client_close_count,pb_zrdt_client_close_count);
    ZPN_ATOMIC_LOAD(&g_pb_client_create_count,pb_client_create_count);
    ZPN_ATOMIC_LOAD(&g_pb_client_no_create_count,pb_client_no_create_count);
    ZPN_ATOMIC_LOAD(&g_pb_client_close_count,pb_client_close_count);
    ZPN_ATOMIC_LOAD(&g_pb_client_connect_count,pb_client_connect_count);
    ZPN_ATOMIC_LOAD(&g_pb_client_disconnect_count,pb_client_disconnect_count);
    ZPN_ATOMIC_LOAD(&g_pb_client_start_auth_count,pb_client_start_auth_count);
    ZPN_ATOMIC_LOAD(&g_pb_client_auth_complete_count,pb_client_auth_complete_count);

    if ( g_pb_client_fohh_debug )
    {
        ZPN_ATOMIC_LOAD(&g_fconn_alloc_count,fconn_alloc_count);
        ZPN_ATOMIC_LOAD(&g_fconn_bufferevent_alloc_count,fconn_bufferevent_alloc_count);
        ZPN_ATOMIC_LOAD(&g_fconn_close_count,fconn_close_count);
        ZPN_ATOMIC_LOAD(&g_fconn_free_count,fconn_free_count);
        ZPN_ATOMIC_LOAD(&g_fconn_direct_close_count,fconn_direct_close_count);
        ZPN_ATOMIC_LOAD(&g_fconn_libevent_close_count,fconn_libevent_close_count);
    }

    ZDP("pbroker pbclient debug stats statistics\n");
    ZDP("------------------------------------\n");
    ZDP(" pb_client_close_fini_count:        %"PRIdLEAST64"\n",pb_client_close_fini_count );
    ZDP(" pb_zrdt_client_close_count:       %"PRIdLEAST64"\n",pb_zrdt_client_close_count );
    ZDP(" pb_client_create_count:           %"PRIdLEAST64"\n",pb_client_create_count );
    ZDP(" pb_client_no_create_count:        %"PRIdLEAST64"\n",pb_client_no_create_count );
    ZDP(" pb_client_close_count:            %"PRIdLEAST64"\n",pb_client_close_count );
    ZDP(" pb_client_connect_count:          %"PRIdLEAST64"\n",pb_client_connect_count );
    ZDP(" pb_client_disconnect_count:       %"PRIdLEAST64"\n",pb_client_disconnect_count );
    ZDP(" pb_client_start_auth_count:       %"PRIdLEAST64"\n",pb_client_start_auth_count );
    ZDP(" pb_client_auth_complete_count:    %"PRIdLEAST64"\n",pb_client_auth_complete_count );

    if ( g_pb_client_fohh_debug )
    {
        ZDP(" fconn_alloc_count:                    %"PRIdLEAST64"\n",fconn_alloc_count );
        ZDP(" fconn_bufferevent_alloc_count:        %"PRIdLEAST64"\n",fconn_bufferevent_alloc_count );
        ZDP(" fconn_close_count:                    %"PRIdLEAST64"\n",fconn_close_count );
        ZDP(" fconn_free_count:                     %"PRIdLEAST64"\n",fconn_free_count );
        ZDP(" fconn_direct_close_count:             %"PRIdLEAST64"\n",fconn_direct_close_count );
        ZDP(" fconn_libevent_close_count:           %"PRIdLEAST64"\n",fconn_libevent_close_count );
    }

    return ZPATH_RESULT_NO_ERROR;

}
static int
pbroker_stats_mtunnel_stats_dump(struct zpath_debug_state*   request_state,
                                 const char**                query_values,
                                 int                         query_value_count,
                                 void*                       cookie)
{
    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();
    struct zpn_private_broker_mtunnel_stats *mtstats = &gs->private_broker_state->mtunnel_stats;

    uint64_t total_mtunnels_created = 0, total_mtunnels_freed = 0, total_mtunnels_reaped_in = 0, total_mtunnels_reaped_out = 0, total_mtunnels_active = 0, total_mtunnels_init_failed = 0, approx_mtunnels_peak_active = 0;
    uint64_t zcc_mtunnels_created = 0, zcc_mtunnels_freed = 0, zcc_mtunnels_reaped_in = 0, zcc_mtunnels_reaped_out = 0, zcc_mtunnels_active = 0, zcc_mtunnels_init_failed = 0;
    uint64_t ec_mtunnels_created = 0, ec_mtunnels_freed = 0, ec_mtunnels_reaped_in = 0, ec_mtunnels_reaped_out = 0, ec_mtunnels_active = 0, ec_mtunnels_init_failed = 0;
    uint64_t vdi_mtunnels_created = 0, vdi_mtunnels_freed = 0, vdi_mtunnels_reaped_in = 0, vdi_mtunnels_reaped_out = 0, vdi_mtunnels_active = 0, vdi_mtunnels_init_failed = 0;
    uint64_t bc_mtunnels_created = 0, bc_mtunnels_freed = 0, bc_mtunnels_reaped_in = 0, bc_mtunnels_reaped_out = 0, bc_mtunnels_active = 0, bc_mtunnels_init_failed = 0;
    uint64_t mt_mtunnels_created = 0, mt_mtunnels_freed = 0, mt_mtunnels_reaped_in = 0, mt_mtunnels_reaped_out = 0, mt_mtunnels_active = 0, mt_mtunnels_init_failed = 0;
    uint64_t zp_mtunnels_created = 0, zp_mtunnels_freed = 0, zp_mtunnels_reaped_in = 0, zp_mtunnels_reaped_out = 0, zp_mtunnels_active = 0, zp_mtunnels_init_failed = 0;
    uint64_t other_mtunnels_created = 0, other_mtunnels_freed = 0, other_mtunnels_reaped_in = 0, other_mtunnels_reaped_out = 0, other_mtunnels_active = 0, other_mtunnels_init_failed = 0;
    uint64_t total_mtunnels_c2c_regex_bypass = 0, zcc_mtunnels_c2c_regex_bypass = 0, ec_mtunnels_c2c_regex_bypass = 0, bc_mtunnels_c2c_regex_bypass = 0, mt_mtunnels_c2c_regex_bypass = 0, zp_mtunnels_c2c_regex_bypass = 0, other_mtunnels_c2c_regex_bypass = 0, vdi_mtunnels_c2c_regex_bypass = 0;
    uint64_t c2c_regex_eval_avg_time_us = 0;

    ZPN_ATOMIC_LOAD(&mtstats->total_mtunnels_created,     total_mtunnels_created);
    ZPN_ATOMIC_LOAD(&mtstats->total_mtunnels_freed,       total_mtunnels_freed);
    ZPN_ATOMIC_LOAD(&mtstats->total_mtunnels_reaped_in,   total_mtunnels_reaped_in);
    ZPN_ATOMIC_LOAD(&mtstats->total_mtunnels_reaped_out,  total_mtunnels_reaped_out);
    ZPN_ATOMIC_LOAD(&mtstats->total_mtunnels_active,      total_mtunnels_active);
    ZPN_ATOMIC_LOAD(&mtstats->total_mtunnels_init_failed, total_mtunnels_init_failed);
    ZPN_ATOMIC_LOAD(&mtstats->approx_mtunnels_peak_active, approx_mtunnels_peak_active);
    ZPN_ATOMIC_LOAD(&mtstats->total_mtunnels_c2c_regex_bypass, total_mtunnels_c2c_regex_bypass);

    ZPN_ATOMIC_LOAD(&mtstats->zcc_mtunnels_created,     zcc_mtunnels_created);
    ZPN_ATOMIC_LOAD(&mtstats->zcc_mtunnels_freed,       zcc_mtunnels_freed);
    ZPN_ATOMIC_LOAD(&mtstats->zcc_mtunnels_reaped_in,   zcc_mtunnels_reaped_in);
    ZPN_ATOMIC_LOAD(&mtstats->zcc_mtunnels_reaped_out,  zcc_mtunnels_reaped_out);
    ZPN_ATOMIC_LOAD(&mtstats->zcc_mtunnels_active,      zcc_mtunnels_active);
    ZPN_ATOMIC_LOAD(&mtstats->zcc_mtunnels_init_failed, zcc_mtunnels_init_failed);
    ZPN_ATOMIC_LOAD(&mtstats->zcc_mtunnels_c2c_regex_bypass, zcc_mtunnels_c2c_regex_bypass);

    ZPN_ATOMIC_LOAD(&mtstats->ec_mtunnels_created,     ec_mtunnels_created);
    ZPN_ATOMIC_LOAD(&mtstats->ec_mtunnels_freed,       ec_mtunnels_freed);
    ZPN_ATOMIC_LOAD(&mtstats->ec_mtunnels_reaped_in,   ec_mtunnels_reaped_in);
    ZPN_ATOMIC_LOAD(&mtstats->ec_mtunnels_reaped_out,  ec_mtunnels_reaped_out);
    ZPN_ATOMIC_LOAD(&mtstats->ec_mtunnels_active,      ec_mtunnels_active);
    ZPN_ATOMIC_LOAD(&mtstats->ec_mtunnels_init_failed, ec_mtunnels_init_failed);
    ZPN_ATOMIC_LOAD(&mtstats->ec_mtunnels_c2c_regex_bypass, ec_mtunnels_c2c_regex_bypass);

    ZPN_ATOMIC_LOAD(&mtstats->vdi_mtunnels_created,     vdi_mtunnels_created);
    ZPN_ATOMIC_LOAD(&mtstats->vdi_mtunnels_freed,       vdi_mtunnels_freed);
    ZPN_ATOMIC_LOAD(&mtstats->vdi_mtunnels_reaped_in,   vdi_mtunnels_reaped_in);
    ZPN_ATOMIC_LOAD(&mtstats->vdi_mtunnels_reaped_out,  vdi_mtunnels_reaped_out);
    ZPN_ATOMIC_LOAD(&mtstats->vdi_mtunnels_active,      vdi_mtunnels_active);
    ZPN_ATOMIC_LOAD(&mtstats->vdi_mtunnels_init_failed, vdi_mtunnels_init_failed);
    ZPN_ATOMIC_LOAD(&mtstats->vdi_mtunnels_c2c_regex_bypass, vdi_mtunnels_c2c_regex_bypass);

    ZPN_ATOMIC_LOAD(&mtstats->bc_mtunnels_created,     bc_mtunnels_created);
    ZPN_ATOMIC_LOAD(&mtstats->bc_mtunnels_freed,       bc_mtunnels_freed);
    ZPN_ATOMIC_LOAD(&mtstats->bc_mtunnels_reaped_in,   bc_mtunnels_reaped_in);
    ZPN_ATOMIC_LOAD(&mtstats->bc_mtunnels_reaped_out,  bc_mtunnels_reaped_out);
    ZPN_ATOMIC_LOAD(&mtstats->bc_mtunnels_active,      bc_mtunnels_active);
    ZPN_ATOMIC_LOAD(&mtstats->bc_mtunnels_init_failed, bc_mtunnels_init_failed);
    ZPN_ATOMIC_LOAD(&mtstats->bc_mtunnels_c2c_regex_bypass, bc_mtunnels_c2c_regex_bypass);

    ZPN_ATOMIC_LOAD(&mtstats->mt_mtunnels_created,     mt_mtunnels_created);
    ZPN_ATOMIC_LOAD(&mtstats->mt_mtunnels_freed,       mt_mtunnels_freed);
    ZPN_ATOMIC_LOAD(&mtstats->mt_mtunnels_reaped_in,   mt_mtunnels_reaped_in);
    ZPN_ATOMIC_LOAD(&mtstats->mt_mtunnels_reaped_out,  mt_mtunnels_reaped_out);
    ZPN_ATOMIC_LOAD(&mtstats->mt_mtunnels_active,      mt_mtunnels_active);
    ZPN_ATOMIC_LOAD(&mtstats->mt_mtunnels_init_failed, mt_mtunnels_init_failed);
    ZPN_ATOMIC_LOAD(&mtstats->mt_mtunnels_c2c_regex_bypass, mt_mtunnels_c2c_regex_bypass);

    ZPN_ATOMIC_LOAD(&mtstats->zp_mtunnels_created,     zp_mtunnels_created);
    ZPN_ATOMIC_LOAD(&mtstats->zp_mtunnels_freed,       zp_mtunnels_freed);
    ZPN_ATOMIC_LOAD(&mtstats->zp_mtunnels_reaped_in,   zp_mtunnels_reaped_in);
    ZPN_ATOMIC_LOAD(&mtstats->zp_mtunnels_reaped_out,  zp_mtunnels_reaped_out);
    ZPN_ATOMIC_LOAD(&mtstats->zp_mtunnels_active,      zp_mtunnels_active);
    ZPN_ATOMIC_LOAD(&mtstats->zp_mtunnels_init_failed, zp_mtunnels_init_failed);
    ZPN_ATOMIC_LOAD(&mtstats->zp_mtunnels_c2c_regex_bypass, zp_mtunnels_c2c_regex_bypass);

    ZPN_ATOMIC_LOAD(&mtstats->other_mtunnels_created,     other_mtunnels_created);
    ZPN_ATOMIC_LOAD(&mtstats->other_mtunnels_freed,       other_mtunnels_freed);
    ZPN_ATOMIC_LOAD(&mtstats->other_mtunnels_reaped_in,   other_mtunnels_reaped_in);
    ZPN_ATOMIC_LOAD(&mtstats->other_mtunnels_reaped_out,  other_mtunnels_reaped_out);
    ZPN_ATOMIC_LOAD(&mtstats->other_mtunnels_active,      other_mtunnels_active);
    ZPN_ATOMIC_LOAD(&mtstats->other_mtunnels_init_failed, other_mtunnels_init_failed);
    ZPN_ATOMIC_LOAD(&mtstats->other_mtunnels_c2c_regex_bypass, other_mtunnels_c2c_regex_bypass);

    ZPN_ATOMIC_LOAD(&mtstats->c2c_regex_eval_avg_time_us, c2c_regex_eval_avg_time_us);

    ZDP("pbroker mtunnel statistics\n");
    ZDP("------------------------------------\n");
    ZDP("Total mtunnels created:                  %"PRIdLEAST64"\n", total_mtunnels_created);
    ZDP("Total mtunnels freed:                    %"PRIdLEAST64"\n", total_mtunnels_freed);
    ZDP("Total mtunnels reaped in:                %"PRIdLEAST64"\n", total_mtunnels_reaped_in);
    ZDP("Total mtunnels reaped out:               %"PRIdLEAST64"\n", total_mtunnels_reaped_out);
    ZDP("Total mtunnels init failed:              %"PRIdLEAST64"\n", total_mtunnels_init_failed);
    ZDP("Total active mtunnels:                   %"PRIdLEAST64"\n", total_mtunnels_active);
    ZDP("Approx peak active mtunnels:             %"PRIdLEAST64"\n", approx_mtunnels_peak_active);
    ZDP("Total c2c bypass mtunnels:               %"PRIdLEAST64"\n", total_mtunnels_c2c_regex_bypass);
    ZDP("ZCC mtunnels created:                    %"PRIdLEAST64"\n", zcc_mtunnels_created);
    ZDP("ZCC mtunnels freed:                      %"PRIdLEAST64"\n", zcc_mtunnels_freed);
    ZDP("ZCC mtunnels reaped in:                  %"PRIdLEAST64"\n", zcc_mtunnels_reaped_in);
    ZDP("ZCC mtunnels reaped out:                 %"PRIdLEAST64"\n", zcc_mtunnels_reaped_out);
    ZDP("ZCC mtunnels init failed:                %"PRIdLEAST64"\n", zcc_mtunnels_init_failed);
    ZDP("ZCC active mtunnels:                     %"PRIdLEAST64"\n", zcc_mtunnels_active);
    ZDP("ZCC c2c bypass mtunnels:                 %"PRIdLEAST64"\n", zcc_mtunnels_c2c_regex_bypass);
    ZDP("Edge connector mtunnels created:         %"PRIdLEAST64"\n", ec_mtunnels_created);
    ZDP("Edge connector mtunnels freed:           %"PRIdLEAST64"\n", ec_mtunnels_freed);
    ZDP("Edge connector mtunnels reaped in:       %"PRIdLEAST64"\n", ec_mtunnels_reaped_in);
    ZDP("Edge connector mtunnels reaped out:      %"PRIdLEAST64"\n", ec_mtunnels_reaped_out);
    ZDP("Edge connector mtunnels init failed:     %"PRIdLEAST64"\n", ec_mtunnels_init_failed);
    ZDP("Edge connector active mtunnels:          %"PRIdLEAST64"\n", ec_mtunnels_active);
    ZDP("Edge connector c2c bypass mtunnels:      %"PRIdLEAST64"\n", ec_mtunnels_c2c_regex_bypass);
    ZDP("VDI mtunnels created:                    %"PRIdLEAST64"\n", vdi_mtunnels_created);
    ZDP("VDI mtunnels freed:                      %"PRIdLEAST64"\n", vdi_mtunnels_freed);
    ZDP("VDI mtunnels reaped in:                  %"PRIdLEAST64"\n", vdi_mtunnels_reaped_in);
    ZDP("VDI mtunnels reaped out:                 %"PRIdLEAST64"\n", vdi_mtunnels_reaped_out);
    ZDP("VDI mtunnels init failed:                %"PRIdLEAST64"\n", vdi_mtunnels_init_failed);
    ZDP("VDI active mtunnels:                     %"PRIdLEAST64"\n", vdi_mtunnels_active);
    ZDP("VDI c2c bypass mtunnels:                 %"PRIdLEAST64"\n", vdi_mtunnels_c2c_regex_bypass);
    ZDP("Branch connector mtunnels created:       %"PRIdLEAST64"\n", bc_mtunnels_created);
    ZDP("Branch connector mtunnels freed:         %"PRIdLEAST64"\n", bc_mtunnels_freed);
    ZDP("Branch connector mtunnels reaped in:     %"PRIdLEAST64"\n", bc_mtunnels_reaped_in);
    ZDP("Branch connector mtunnels reaped out:    %"PRIdLEAST64"\n", bc_mtunnels_reaped_out);
    ZDP("Branch connector mtunnels init failed:   %"PRIdLEAST64"\n", bc_mtunnels_init_failed);
    ZDP("Branch connector active mtunnels:        %"PRIdLEAST64"\n", bc_mtunnels_active);
    ZDP("Branch connector c2c bypass mtunnels:    %"PRIdLEAST64"\n", bc_mtunnels_c2c_regex_bypass);
    ZDP("Machine tunnel mtunnels created:         %"PRIdLEAST64"\n", mt_mtunnels_created);
    ZDP("Machine tunnel mtunnels freed:           %"PRIdLEAST64"\n", mt_mtunnels_freed);
    ZDP("Machine tunnel mtunnels reaped in:       %"PRIdLEAST64"\n", mt_mtunnels_reaped_in);
    ZDP("Machine tunnel mtunnels reaped out:      %"PRIdLEAST64"\n", mt_mtunnels_reaped_out);
    ZDP("Machine tunnel mtunnels init failed:     %"PRIdLEAST64"\n", mt_mtunnels_init_failed);
    ZDP("Machine tunnel active mtunnels:          %"PRIdLEAST64"\n", mt_mtunnels_active);
    ZDP("Machine tunnel c2c bypass mtunnels:      %"PRIdLEAST64"\n", mt_mtunnels_c2c_regex_bypass);
    ZDP("Zapp Partner mtunnels created:           %"PRIdLEAST64"\n", zp_mtunnels_created);
    ZDP("Zapp Partner mtunnels freed:             %"PRIdLEAST64"\n", zp_mtunnels_freed);
    ZDP("Zapp Partner mtunnels reaped in:         %"PRIdLEAST64"\n", zp_mtunnels_reaped_in);
    ZDP("Zapp Partner mtunnels reaped out:        %"PRIdLEAST64"\n", zp_mtunnels_reaped_out);
    ZDP("Zapp Partner mtunnels init failed:       %"PRIdLEAST64"\n", zp_mtunnels_init_failed);
    ZDP("Zapp Partner active mtunnels:            %"PRIdLEAST64"\n", zp_mtunnels_active);
    ZDP("Zapp Parther c2c bypass mtunnels:        %"PRIdLEAST64"\n", zp_mtunnels_c2c_regex_bypass);
    ZDP("Other client mtunnels created:           %"PRIdLEAST64"\n", other_mtunnels_created);
    ZDP("Other client mtunnels freed:             %"PRIdLEAST64"\n", other_mtunnels_freed);
    ZDP("Other client mtunnels reaped in:         %"PRIdLEAST64"\n", other_mtunnels_reaped_in);
    ZDP("Other client mtunnels reaped out:        %"PRIdLEAST64"\n", other_mtunnels_reaped_out);
    ZDP("Other client mtunnels init failed:       %"PRIdLEAST64"\n", other_mtunnels_init_failed);
    ZDP("Other client active mtunnels:            %"PRIdLEAST64"\n", other_mtunnels_active);
    ZDP("Other c2c regex bypass mtunnels:         %"PRIdLEAST64"\n", other_mtunnels_c2c_regex_bypass);
    ZDP("c2c regex eval avg time us:              %"PRIdLEAST64"\n", c2c_regex_eval_avg_time_us);

    return ZPATH_RESULT_NO_ERROR;
}

static int pbroker_dns_dispatcher_stats_dump(struct zpath_debug_state*   request_state,
                                     const char**                query_values,
                                     int                         query_value_count,
                                     void*                       cookie)
{
    char jsonout[10000] = {0};

    if (ARGO_RESULT_NO_ERROR == argo_structure_dump(zpn_pbroker_dns_dispatcher_stats_description, &pse_dns_disp_stats, jsonout,
                                                    sizeof(jsonout), NULL, 1)){
        ZDP("Pbroker DNS dispatcher stats: %s\n", jsonout);
    }

    return ZPATH_RESULT_NO_ERROR;
}

static int
pbroker_stats_tx_comprehensive_stats_set_interval(struct zpath_debug_state*   request_state,
                                                    const char**                query_values,
                                                    int                         query_value_count,
                                                    void*                       cookie)
{
    int64_t interval_us;
    int res;

    if (!query_values[0]) {
        ZDP("Missing argument\n");
        goto done;
    }

    interval_us = strtoll(query_values[0], NULL, 10);
    if (interval_us < PBROKER_STATS_TX_COMPREHENSIVE_MIN_TIMEPERIOD_USEC) {
        ZDP("Change comprehensive stats logging interval failed, minimum interval: %"PRId64"\n",
                                    PBROKER_STATS_TX_COMPREHENSIVE_MIN_TIMEPERIOD_USEC);
        goto done;
    }

    res = argo_log_set_interval_by_log_name(argo_log_get("statistics_log"),
                                            "pbroker_stats_comprehensive",
                                            sizeof("pbroker_stats_comprehensive"),
                                            interval_us);
    if (res) {
        ZDP("Change comprehensive stats logging interval failed: %s\n", zpath_result_string(res));
    } else {
        ZDP("Comprehensive stats logging interval successfully changed to: %"PRId64"\n", interval_us);
    }

done:
    return ZPATH_RESULT_NO_ERROR;
}
/*
 * Adding the stats specific to pbroker here. At any point of time, if you feel this have to be moved
 * to common location and applicable for all software, pls move this to zpath_debug_app_stats()
 *
 * If diagnostic mode is enabled, dump the stats in pbroker's local disk. This is done only for engineering
 * development debugging.
 */
int
pbroker_stats_tx_init()
{
    static int available_cpus;
    int res = ZPATH_RESULT_NO_ERROR;
    struct argo_log_reader *stats_upload_reader;
    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();
    struct argo_structure_description *zpn_zdx_mtr_stats_description = zpn_zdx_init_mtr_stats_description();
    struct argo_structure_description *zpn_zdx_cache_stats_description = zpn_zdx_init_cache_stats_description();


    static struct zpn_system_state pbroker_sys_stats;
    pbroker_sys_stats.available_cpus = zpn_private_broker_state_get_available_cpus();
    pbroker_sys_stats.configured_cpus = zpn_private_broker_state_get_configured_cpus();
    pbroker_sys_stats.fohh_threads = zpn_private_broker_state_get_fohh_threads();
    pbroker_sys_stats.is_container_env = zpn_private_broker_state_is_container_env();
    pbroker_sys_stats.is_zscaler_os = 0;  /* Note: This bit is relevant for AppC on BC and does not apply to PSE, as PSE is not supported on BC yet.It will always be 0 for PSE.*/

    if ((available_cpus = sysconf(_SC_NPROCESSORS_ONLN)) == -1) {
        ZPN_LOG(AL_NOTICE, "Failed to get available cpus");
    }

    zpn_pb_stats_config_override_monitor(gs->private_broker_id, gs->customer_id);
    zpn_pb_wally_table_stats_upload_config_override_monitor(gs->private_broker_id, gs->customer_id);

    num_ff_stats_sent = 0;
    // Do not forget to increment PBROKER_STATS_TX_MAX_OBJECTS if you add a new entry for cfg
    cfg[0] = (struct pbroker_stats_tx_cfg){"pbroker_stats_comprehensive", NULL, 1, PBROKER_STATS_TX_COMPREHENSIVE_TIMEPERIOD_USEC, zpn_pbroker_comprehensive_stats_description, sizeof(struct zpn_pbroker_comprehensive_stats), pbroker_comprehensive_stats_fill, &available_cpus, 1};
    cfg[1] = (struct pbroker_stats_tx_cfg){"pbroker_stats_admin_probe", NULL, 1, PBROKER_STATS_TX_ADMIN_PROBE_USEC, admin_probe_stats_description, sizeof(struct admin_probe_stats), private_broker_admin_probe_stats_fill, NULL, 1};
    cfg[2] = (struct pbroker_stats_tx_cfg){"pbroker_stats_file_fetch", NULL, 1, PBROKER_STATS_TX_FILE_FETCH, zpn_pbroker_file_stats_description, sizeof(struct zpn_pbroker_file_stats), pbroker_file_fetch_stats_fill, NULL, 1};
    cfg[3] = (struct pbroker_stats_tx_cfg){"pbroker_stats_system_memory", NULL, 1, PBROKER_STATS_TX_SYSTEM_MEMORY_TIMEPERIOD_USEC, zpath_system_memory_stats_description, sizeof(struct zpn_system_memory_stats), assistant_pbroker_system_memory_stats_fill, NULL, 1};
    cfg[4] = (struct pbroker_stats_tx_cfg){"pbroker_stats_zdx_mtr", NULL, 1, PBROKER_STATS_TX_5_MINS_INTERVAL_USEC, zpn_zdx_mtr_stats_description, sizeof(struct zpn_zdx_mtr_stats), zdx_mtr_stats_fill, NULL, 1};
    cfg[5] = (struct pbroker_stats_tx_cfg){"pbroker_stats_zdx_probes", NULL, 1, PBROKER_STATS_TX_5_MINS_INTERVAL_USEC, zpn_zdx_probe_stats_description, sizeof(struct zpn_zdx_probe_stats), zpn_zdx_probe_stats_fill, NULL, 1};
    cfg[6] = (struct pbroker_stats_tx_cfg){"pbroker_stats_zdx_cache", NULL, 1, PBROKER_STATS_TX_5_MINS_INTERVAL_USEC, zpn_zdx_cache_stats_description, sizeof(struct zpn_zdx_cache_stats), zdx_cache_stats_fill, NULL, 1};
    cfg[7] = (struct pbroker_stats_tx_cfg){"pbroker_stats_pblicent_debug", NULL, 1, PBROKER_STATS_TX_5_MINS_INTERVAL_USEC, zpn_pbclient_debug_stats_description, sizeof(struct zpn_pbclient_debug_stats), zpn_pbclient_debug_stats_fill, NULL, 1};
    cfg[8] = (struct pbroker_stats_tx_cfg){"pbroker_stats_resiliency", NULL, 1, PBROKER_STATS_TX_5_MINS_INTERVAL_USEC, zpn_pse_resiliency_stats_description, sizeof(struct zpn_pse_resiliency_stats), pbroker_resiliency_stats_fill, NULL, 1};
    cfg[9] = (struct pbroker_stats_tx_cfg){"pbroker_stats_svcp", NULL, 1, PBROKER_STATS_TX_5_MINS_INTERVAL_USEC, zpn_svcp_stats_description, sizeof(struct zpn_svcp_stats), pbroker_svcp_stats_fill, NULL, 1};
    cfg[10] = (struct pbroker_stats_tx_cfg){"pbroker_stats_system_network", NULL, 1, PBROKER_STATS_TX_SYSTEM_NETWORK_TIMEPERIOD_USEC, zpath_system_network_stats_description, sizeof(struct zpn_system_network_stats), zpn_system_network_stats_fill, NULL, 1};
    cfg[11] = (struct pbroker_stats_tx_cfg){"pbroker_stats_fproxy", NULL, 1, PBROKER_STATS_TX_FPROXY_USEC, zpn_private_broker_fproxy_stats_description, sizeof(struct zpn_private_broker_fproxy_stats), zpn_private_broker_fproxy_stats_fill, NULL, 1};
    cfg[12] = (struct pbroker_stats_tx_cfg){"pbroker_stats_dsp", NULL, 1, PBROKER_STATS_TX_5_MINS_INTERVAL_USEC, dsp_stats_description, sizeof(struct dsp_stats), zpn_local_dispatcher_stats_fill, NULL, 1};
    cfg[13] = (struct pbroker_stats_tx_cfg){"pbroker_stats_stepup_auth", NULL, 1, PBROKER_STATS_TX_5_MINS_INTERVAL_USEC, zpn_stepup_auth_stats_description, sizeof(struct zpn_stepup_auth_stats), pbroker_stepup_auth_stats_fill, NULL, 1};
    cfg[14] = (struct pbroker_stats_tx_cfg){"pbroker_stats_dns_dispatcher", NULL, 1, PBROKER_STATS_TX_5_MINS_INTERVAL_USEC, zpn_pbroker_dns_dispatcher_stats_description, sizeof(struct zpn_pbroker_dns_dispatcher_stats), pbroker_dns_dispatcher_stats_fill, NULL, 1};
    cfg[15] = (struct pbroker_stats_tx_cfg){"pbroker_stats_system_inventory", NULL, 1, PBROKER_STATS_TX_SYSTEM_PERIOD_USEC, zpn_system_inventory_description, sizeof(struct zpn_system_inventory_stats), zpn_system_inventory_stats_fill, &pbroker_sys_stats, 1};
    cfg[16] = (struct pbroker_stats_tx_cfg){"pbroker_stats_upgrade", NULL, 1, PBROKER_STATS_TX_UPGRADE_USEC, zpn_private_broker_upgrade_stats_description, sizeof(struct zpn_private_broker_upgrade_stats), zpn_private_broker_upgrade_stats_fill, NULL, 1};
    cfg_ready = 1;

    res = zpath_debug_mem_stats_init(zpath_service_private_broker);
    if (res) {
        ZPN_LOG(AL_ERROR, "Initializing memory allocator stats failed: %s", zpath_result_string(res));
        return res;
    }

    {
        size_t                              cfg_iter;
        void*                               obj;

        for (cfg_iter = 0; cfg_iter < PBROKER_STATS_TX_MAX_OBJECTS; cfg_iter++) {
            if (0 == cfg[cfg_iter].enabled) {
                continue;
            }
            obj = PBROKER_MALLOC(cfg[cfg_iter].size_of_object);
            if (!argo_log_register_structure(argo_log_get("statistics_log"), cfg[cfg_iter].l_name, AL_INFO,
                                             cfg[cfg_iter].interval_us, cfg[cfg_iter].argo_description, obj,
                                             cfg[cfg_iter].log_immediate, cfg[cfg_iter].pre_log_cb,
                                             cfg[cfg_iter].pre_log_cb_cookie)) {
                ZPN_LOG(AL_ERROR, "Could not register %s stats", cfg[cfg_iter].l_name);
                res = ZPATH_RESULT_ERR;
                goto done;
            }
        }
    }

    stats_upload_reader = argo_log_read(zpath_stats_collection, "stats_upload", 0, 1,
                                                         pbroker_stats_tx_reader_cb, NULL, 0, NULL,
                                                         60 * 1000 * 1000);
    if (!stats_upload_reader) {
        ZPN_LOG(AL_ERROR, "Could not create stats log upload reader");
        res = ZPATH_RESULT_ERR;
        goto done;
    }

    if (!(pbroker_stats_tx_stats_description = argo_register_global_structure(PBROKER_STATS_TX_STATS_HELPER))) {
        ZPN_LOG(AL_ERROR, "couldn't init pbroker_stats_tx_stats argo object");
        res = ZPATH_RESULT_ERR;
        goto done;
    }

    if (zpath_debug_add_read_command("Dump info related to stats TX operation.",
                               "/pbroker/stats/tx/stats/dump", pbroker_stats_tx_dump, NULL,
                               NULL)) {
        ZPN_LOG(AL_ERROR, "couldn't add /pbroker/stats/tx/stats/dump");
        res = ZPATH_RESULT_ERR;
        goto done;
    }

    if (zpath_debug_add_write_command("Set the rate of comprehensive stats log transmit",
                                "/pbroker/stats/tx/comprehensive_stats",
                                pbroker_stats_tx_comprehensive_stats_set_interval,
                                NULL,
                                "interval_us", "New interval for stats transmit",
                                NULL)) {
        ZPN_LOG(AL_NOTICE, "Unable to register /pbroker/stats/tx/comprehensive_stats");
        res = ZPATH_RESULT_ERR;
        goto done;
    }

    if(zpath_debug_add_write_command("Reset pbroker client connection stats.",
                               "/pbroker/stats/client_connection_stats/reset",
                               pbroker_stats_client_connection_stats_reset,
                               NULL,
                               NULL)) {
        ZPN_LOG(AL_NOTICE, "Unable to register pbroker_stats_client_connection_stats_reset");
        res = ZPATH_RESULT_ERR;
        goto done;
    }

    if(zpath_debug_add_read_command("Dump pbroker mtunnel stats.",
                               "/pbroker/stats/mtunnel_stats/dump",
                               pbroker_stats_mtunnel_stats_dump,
                               NULL,
                               NULL)) {
        ZPN_LOG(AL_NOTICE, "Unable to register pbroker_stats_mtunnel_stats_dump");
        res = ZPATH_RESULT_ERR;
        goto done;
    }

    if(zpath_debug_add_read_command("Dump pbroker dispatcher stats.",
                               "/pbroker/stats/dispatcher/dump",
                               pbroker_stats_dispatcher_stats_dump,
                               NULL,
                               NULL)) {
        ZPN_LOG(AL_NOTICE, "Unable to register pbroker_stats_dispatcher_stats_dump");
        res = ZPATH_RESULT_ERR;
        goto done;
    }

    if(zpath_debug_add_write_command("Reset pbroker mtunnel stats.",
                               "/pbroker/stats/mtunnel_stats/reset",
                               pbroker_stats_mtunnel_stats_reset,
                               NULL,
                               NULL)) {
        ZPN_LOG(AL_NOTICE, "Unable to register pbroker_stats_mtunnel_stats_reset");
        res = ZPATH_RESULT_ERR;
        goto done;
    }

    if(zpath_debug_add_write_command("Reset pbclient debug stats.",
                               "/pbroker/stats/pbclient_debug/reset",
                               pbroker_stats_pblient_debug_stats_reset,
                               NULL,
                               NULL)) {
        ZPN_LOG(AL_NOTICE, "Unable to register pbroker_stats_pbclient_reset");
        res = ZPATH_RESULT_ERR;
        goto done;
    }

    if(zpath_debug_add_read_command("Dump pbroker client connection stats.",
                               "/pbroker/stats/client_connection_stats/dump",
                               pbroker_stats_client_connection_stats_dump,
                               NULL,
                               NULL)) {
        ZPN_LOG(AL_NOTICE, "Unable to register pbroker_stats_client_connection_stats_dump");
        res = ZPATH_RESULT_ERR;
        goto done;
    }


    if(zpath_debug_add_read_command("Dump pbroker comprehensive stats.",
                               "/pbroker/stats/comprehensive/dump",
                               pbroker_comprehensive_stats_dump,
                               NULL,
                               NULL)) {
        ZPN_LOG(AL_NOTICE, "Unable to register pbroker_stats_comprehensive_dump");
        res = ZPATH_RESULT_ERR;
        goto done;
    }

    if(zpath_debug_add_read_command("Dump pbroker dns dispatcher stats.",
                               "/pbroker/stats/dns_dispatcher/dump",
                               pbroker_dns_dispatcher_stats_dump,
                               NULL,
                               NULL)) {
        ZPN_LOG(AL_NOTICE, "Unable to register pbroker_dns_dispatcher_stats_dump");
        res = ZPATH_RESULT_ERR;
        goto done;
    }

    if(zpath_debug_add_read_command("Dump pbroker pbclient debug stats.",
                               "/pbroker/stats/pbclient_debug/dump",
                               pbroker_pbclient_debug_stats_dump,
                               NULL,
                               NULL)) {
        ZPN_LOG(AL_NOTICE, "Unable to register pbroker_pbclient_debug_stats_dump");
        res = ZPATH_RESULT_ERR;
        goto done;
    }

done:
    return res;
}
