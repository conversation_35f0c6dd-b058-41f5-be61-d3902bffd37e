/*
 * zpn_dr_interface.c. Copyright (C) 2022 Zscaler Inc. All Rights Reserved.
 */


#include "zpn_private_brokerd/zpn_dr_interface.h"

#include "zpn/zpn_private_broker_util.h"
#include "zpn/zpn_fohh_worker.h"
#include "zpath_lib/zpath_system.h"
#include "zpath_lib/zpath_config_override_keys.h"
#include "zpath_lib/zpath_config_override.h"
#include "zpn_dr/zpn_dr_lib.h"
#include "zthread/zthread.h"
#include "wally/wally_private.h"
#include "zpath_lib/zpath_customer.h"
#include "zpn/zpn_broker_common.h"

#include "zpath_lib/zpath_debug.h"
#include <time.h>

static struct zthread_info *tickle_me = NULL;
static int snapshot_mgmnt_script_written = 0;
static int binary_snapshot_init_done = 0;

extern int g_config_init_done;
int g_dns_err_resp_count = 0;
static int g_dr_interface_monitor_count = 0;

int is_binary_snapshot_init_done()
{
    return binary_snapshot_init_done;
}

static int auto_config_dump_stats(struct zpath_debug_state*  request_state,
                                  const char **              query_values,
                                  int                        query_value_count,
                                  void*                      cookie)
{
    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();
    struct zpn_private_broker_dr_stats *stats = &gs->dr_stats;

    if (!g_config_init_done) {
        ZDP("Config init in-progress. Please try after some time... \n");
        return ZPN_RESULT_NO_ERROR;
    }

    // Check whether PSE this group marked to support DR or not
    if (!is_pbroker_group_marked_to_support_dr()) {
        // This PSE is not configured to support DR.
        ZDP("This PSE is not configured to support DR\n");
        return ZPN_RESULT_NO_ERROR;
    }

    if (is_zpn_drmode_enabled()) {
        // Auto config dump is disabled during DR mode.
        ZDP("No stats available as 'auto_config_dump' is disabled during DR mode\n");
        return ZPN_RESULT_NO_ERROR;
    }


    ZDP("DR auto config stats: Auto DR config dump counter: %ld, Max time taken to dump DR config: %ld, "
        "Last DR config dump start time: %ld, Last DR config dump end time: %ld, DR config dump failed counter: %ld\n",
        (long)stats->dr_config_auto_dump_count, (long)stats->dr_config_max_dump_time, (long)stats->dr_config_dump_start_time_s,
        (long)stats->dr_config_dump_end_time_s, (long)stats->dr_config_dump_fail_count);

    return ZPN_RESULT_NO_ERROR;
}


void
dr_dns_txt_response_cb(void*    cb_void_cookie,
                       int64_t  cb_int_cookie,
                       struct zcdns_result*  result)
{
    int     txt_record_count                    = 0;
    int     num_of_parts                        = 0;
    struct  txt_record_sorted *sorted_rec       = NULL;
    int     i                                   = 0;
    int     ret                                 = ZPN_RESULT_NO_ERROR;
    char    *public_key                         = (char *)cb_void_cookie;
    int     is_dr_requested                     = -1;
    int     do_exit_app                         = 0;
    int64_t start_time                          = 0;
    int64_t expiry_time                         = 0;
    int64_t current_time                        = 0;
    char    rec_time_str[255]                   = "";
    char    current_time_str[255]               = "";
    char    buf[ZPN_DR_CONFIG_MAX_SIZE + 1];
    char    *data                               = NULL;
    size_t  len;
    struct  dr_activation_record_table table;
    char    dr_running_mode[DR_RUNNING_MODE_STATUS_LEN] = {0};
    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();
    struct zpn_private_broker_dr_stats *stats = &gs->dr_stats;

    if (!result) {
        // "FAIL: no resolution"
        stats->dr_activate_no_resp_count++;
        return;
    }

    memset(&table, 0, sizeof(struct  dr_activation_record_table));
    txt_record_count = get_txt_records_count(result->txt);

    if (txt_record_count <= 0) {
        ZPN_LOG(AL_ERROR, "CUSTOMER_DR: No txt records returned for DR domain. Response code:%d", result->txt_rcode);
        if (result->txt_rcode == 0) {
            // DNS response code is NOERROR(0) but No txt records returned. Switch to default mode 'off'
            stats->dr_activate_no_resp_count++;
            ret = ZPN_RESULT_ERR;
        } else {
            // DNS response error code returned(NXDOMAIN/SERVFAIL etc).
            // Switch to default mode 'off' if there are consecutive errors
            g_dns_err_resp_count++;
            stats->dr_activate_err_resp_count++;
            if (g_dns_err_resp_count % ZPN_DR_MAX_ALLOWED_NO_RESP == 0) {
                ret = ZPN_RESULT_ERR;
            }
        }
        goto done;
    }

    stats->dr_activate_resp_count++;
    // Reset DNS response global error count
    g_dns_err_resp_count = 0;

    ZPN_DR_DNS_DEBUG("Processing DR DNS txt response. Total received txt records count:%d, TTL:%d",
            txt_record_count, result->txt->a_ttl);

    sorted_rec = sort_txt_record_based_on_partindex(txt_record_count, result);
    if (sorted_rec == NULL) {
        ZPN_LOG(AL_ERROR, "CUSTOMER_DR: Failed to Sort DR activation txt record based on Part index");
        ret = ZPN_RESULT_ERR;
        goto done;
    }

    for (i = 0; i < txt_record_count; i++) {

        ZPN_LOG(AL_DEBUG, "CUSTOMER_DR: Parsing txt record:%s len:%d index:%d", sorted_rec[i].txt->txt + 1, sorted_rec[i].txt->txt_len, (i + 1));
        ret = zpn_dr_activation_record_parse((char *) sorted_rec[i].txt->txt + 1,
                                             sorted_rec[i].txt->txt_len,
                                             &table);
        if (ret != ZPN_RESULT_NO_ERROR) {
            // Failed to parse txt record
            ZPN_LOG(AL_ERROR, "CUSTOMER_DR: Failed to Parse DR activation txt record:%s error:%s",
                                         sorted_rec[i].txt->txt + 1, zpath_result_string(ret));
            goto done;
        }
    }

    zpn_dr_activation_record_print_table(&table);

    num_of_parts = zpn_dr_get_num_of_parts_field(&table);
    if (num_of_parts != txt_record_count) {
        ZPN_LOG(AL_ERROR, "CUSTOMER_DR: Invalid DR DNS activation response. Record received:%d for parts:%d",
                txt_record_count, num_of_parts);
        ret = ZPN_RESULT_ERR;
        goto done;
    }

    if (!g_config_init_done) {
        // Config init is not done. Check if we have any publickey cached locally.
        // If exist then use it to verify the signature of the DNS response.
        if (zpn_dr_local_config_exist(ZPN_DR_PUBLIC_KEY_FILE)) {
            pthread_mutex_lock(&gs->dr_handler_lock);
            memset(buf, 0, sizeof(buf));
            len = zpn_dr_read_file_content(ZPN_DR_PUBLIC_KEY_FILE, buf, ZPN_DR_CONFIG_MAX_SIZE);
            pthread_mutex_unlock(&gs->dr_handler_lock);
            if (len) {
                public_key = buf;
            }
        }
    }

    if (public_key) {
        // Verify signature of DR activation record
        ret = dr_activation_record_verify_signature(&table, public_key);
        if (ret != ZPN_RESULT_NO_ERROR) {
            // Failed to parse txt record
            ZPN_LOG(AL_ERROR, "CUSTOMER_DR: DR activation record verification failed. error:%s",
                                          zpath_result_string(ret));
            if (ret == ZPN_DR_RESULT_NOT_FOUND) {
                // bh tag doesn't exist in received record when publickey is configured..
                ZPN_LOG(AL_WARNING, "CUSTOMER_DR: Signature(bh=) not found in DR DNS activation record");
            }
            goto done;
        }
        ZPN_LOG(AL_INFO, "CUSTOMER_DR: DR activation record signature successfully verified");
    }

    // Validate start_time of DNS txt record if exist.
    start_time  = zpn_dr_get_epoch_validity_time(&table, TAG_NAME_START_TIME);
    if (start_time) {
        // Start time exist in DR record. Validate start time
        current_time = epoch_s();
        if (current_time < start_time) {
            // start time mismatches with current time of the system.
            ZPN_LOG(AL_INFO, "CUSTOMER_DR: DR activation record's StartTime:%s mismatches with CurrentTime:%s. Skipping for now",
                    zpn_dr_epoch_to_time_str(start_time, rec_time_str, sizeof(rec_time_str)),
                    zpn_dr_epoch_to_time_str(current_time, current_time_str, sizeof(current_time_str)));
            ret = ZPN_RESULT_ERR;
            goto done;
        }
    }

    // Validate expiry time of DNS txt record if exist.
    expiry_time = zpn_dr_get_epoch_validity_time(&table, TAG_NAME_EXPIRY_TIME);
    if (expiry_time) {
        // Expiry time exist in DR record. Validate expiry time
        current_time = epoch_s();
        if (current_time > expiry_time) {
            // DR txt record is expired
            ZPN_LOG(AL_INFO, "CUSTOMER_DR: DR activation record expired. ExpiryTime:%s and CurrentTime:%s. Skipping this expired record",
                    zpn_dr_epoch_to_time_str(expiry_time, rec_time_str, sizeof(rec_time_str)),
                    zpn_dr_epoch_to_time_str(current_time, current_time_str, sizeof(current_time_str)));
            ret = ZPN_RESULT_ERR;
            goto done;
        }
    }

    // Validate Kind(k) tag of DNS txt record if exist. Expected kind value is either 'zpa' or 'all'
    data = zpn_dr_activation_record_get_tag_data(&table, TAG_NAME_KIND);
    if (data) {
        // Tag kind exist in DNS txt record. Validating it.
        if (!(strcmp(data, DNS_DR_RECORD_KIND_ZPA) == 0 ||
                    strcmp(data, DNS_DR_RECORD_KIND_ALL) == 0)) {
            ZPN_LOG(AL_ERROR, "DR activation record's kind value isn't valid for ZPA. Received k=%s",
                    data);
            ret = ZPN_RESULT_ERR;
            goto done;
        }
    }

    is_dr_requested = zpn_dr_get_activate_cmd_from_txt_response(&table, dr_running_mode);
    if (is_dr_requested == -1) {
        ZPN_LOG(AL_ERROR, "Could not get activation status from DR txt record response");
        ret = ZPN_RESULT_ERR;
        goto done;
    }

    if (is_dr_requested && (!is_pbroker_running_drmode() || strcmp(dr_running_mode, gs->private_broker_state->dr_running_mode) != 0)) {
        // DR mode is activated through DNS but PSE not running in DR mode or running with different mode than requested.
        // Go ahead and activate DR mode
        ZPN_LOG(AL_INFO, "CUSTOMER_DR: DR mode is requested through DNS. Activating DR mode:%s .....", dr_running_mode);
        zpn_dr_mode_activate(tickle_me, dr_running_mode);

        /* Count "on" and "test" activations */
        if (strcmp(dr_running_mode, DNS_DR_ACTIVATION_ON_STR) == 0) {
            stats->dr_activate_on_count++;
        } else if (strcmp(dr_running_mode, DNS_DR_ACTIVATION_TEST_STR) == 0) {
            stats->dr_activate_test_count++;
        }
    } else if(!is_dr_requested && is_pbroker_running_drmode()) {
        // DR mode is deactivated through DNS but PSE running in DR mode.
        // Go ahead and deactivate DR mode
        ZPN_LOG(AL_INFO, "CUSTOMER_DR: DR mode deactivation received through DNS. Deactivating DR mode.....");
        // Remove DR mode marker file here and let the PSE monitor to restart the system
        do_exit_app = 1;
    }

done:
    if (sorted_rec) {
        ZPN_FREE(sorted_rec);
    }

    zpn_dr_activation_record_free(&table);
    zcdns_result_release(result);

    if (ret != ZPN_RESULT_NO_ERROR && is_pbroker_running_drmode()) {
        // Switch to default DR mode 'off' if there is no valid record to stay in DR mode
        ZPN_LOG(AL_WARNING, "CUSTOMER_DR: DR activation record isn't valid to stay in DR mode.. Switching to default DR mode 'off'");
        do_exit_app = 1;
    }

    if (ret != ZPN_RESULT_NO_ERROR) {
        /* Ignore unexpected dns responses, they are already counted via dr_activate_err_resp_count */
        if (g_dns_err_resp_count == 0) {
            stats->dr_activate_err_count++;
        }
    }

    /* Reset err_resp_count once reached the max_allowed consecutive no_resp count */
    if (g_dns_err_resp_count % ZPN_DR_MAX_ALLOWED_NO_RESP == 0) {
        g_dns_err_resp_count = 0;
    }

    if (do_exit_app) {
        ZPN_LOG(AL_NOTICE, "CUSTOMER_DR: Turning off DR mode. Service will now restart...");
        zpn_dr_remove_marker_flag();
        stats->dr_activate_off_count++;
        /* sleep to ensure contents are synced out to disk from memory before exiting */
        zpn_dr_log_stats("exiting app");
        sleep(1);
        exit(0);
    }

    return;

}

int zpn_dr_resolve_txt_record()
{
    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();
    char    *dr_txt_domain              = NULL;
    struct  zpath_customer *customer    = NULL;
    int     res                         = 0;
    char    *dr_public_key              = NULL;

    if (!gs) {
        return ZPN_RESULT_ERR;
    }

    struct zpn_private_broker_dr_stats *stats = &gs->dr_stats;

    // Get txt domain name to query from zpath_customer table
    res = zpath_customer_get(gs->customer_id, &customer, NULL, NULL, 0);
    if (res != ZPN_RESULT_NO_ERROR) {
         ZPN_LOG(AL_ERROR, "CUSTOMER_DR: Could not get zpath_customer data to retrieve DR configuration, error:%s",
                 zpath_result_string(res));
         return res;
    }

    dr_txt_domain = customer->dr_domain_name;

    if (dr_txt_domain == NULL) {
        ZPN_LOG(AL_ERROR, "CUSTOMER_DR: Could not get DR activation domain name from customer configuration");
        goto done;
    }

    dr_public_key = customer->zpa_dr_trigger_public_key;

    ZPN_DR_DNS_DEBUG("CUSTOMER_DR: Resolving DR activation_record domain:%s, Verification mode:%s",
            dr_txt_domain, (dr_public_key? "signed":"unsigned"));

    resolve_dr_txt_domain(gs->zcdns, dr_txt_domain, dr_public_key, dr_dns_txt_response_cb);
    stats->dr_activate_req_count++;

done:

    if (!is_pbroker_running_drmode()) {
        pthread_mutex_lock(&gs->dr_handler_lock);
        // Cache DR configs locally to the file system
        res = zpn_dr_config_cache_locally(dr_txt_domain, dr_public_key);
        pthread_mutex_unlock(&gs->dr_handler_lock);
        if (res != ZPN_RESULT_NO_ERROR) {
            ZPN_LOG(AL_ERROR, "CUSTOMER_DR: Could not cache/update DR configurations to local filesystem");
            return res;
        }
    }

    return res;
}

// Move this function in zpn_dr library when we do the cleanup
int zpn_dr_mode_activate(struct zthread_info *z_info, char *dr_running_mode)
{
    int written;
    size_t length;
    FILE *fp = NULL;

    if (g_config_init_done) {
        if (!is_pbroker_group_marked_to_support_dr()) {
            ZPATH_LOG(AL_INFO, "CUSTOMER_DR: DR mode can't be enabled as this PSE is not configured to support DR");
            return 0;
        }
    }

    fp = fopen("ZPA_DR_MODE", "w");
    if (fp == NULL) {
        ZPATH_LOG(AL_ERROR, "CUSTOMER_DR: Failed to set DR mode flag");
        return 1;
    }

    length = strlen(dr_running_mode);

    written = fwrite(dr_running_mode, length, 1, fp);
    if (written != 1) {
        ZPN_LOG(AL_ERROR, "CUSTOMER_DR: Failed to write DR running mode %s into marker file", dr_running_mode);
        fclose(fp);
        return 1;
    }

    fclose(fp);
    if (g_config_init_done) {
        dump_dr_config(1, z_info);
    }
    ZPATH_LOG(AL_CRITICAL, "CUSTOMER_DR: DR mode is activated ! Service will now restart...");
    zpn_dr_log_stats("app exiting");
    /* sleep to ensure contents are synced out to disk from memory before exiting */
    sleep(1);
    exit(0);
}

static void
pbroker_dr_config_snapshot_monitor(evutil_socket_t sock, short flags, void *cookie)
{
    int config_dump_success = 0;
    int res = ZPN_RESULT_NO_ERROR;
    char current_time[ZPN_DR_CONFIG_SNAPSHOT_BUFF_LEN] = {0};
    char configured_time[ZPN_DR_CONFIG_SNAPSHOT_BUFF_LEN] = {0};
    char config_dir_name[ZPN_DR_CONFIG_SNAPSHOT_BUFF_LEN] = {0};

    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();
    struct zpn_private_broker_dr_stats *stats = &gs->dr_stats;

    if (tickle_me) zthread_heartbeat(tickle_me);

    if (!g_config_init_done) {
        // Config init is not done yet. This probably could be a scenerio
        // where the cloud/config channel is down and we are trying to boot PSE.
        return;
    }

    // Check whether PSE this group marked to support DR or not
    if (!is_pbroker_group_marked_to_support_dr()) {
        // This PSE is not configured to support DR.
        return;
    }

    if (zpn_dr_create_parent_snapshot_dir(ZPN_DR_CONFIG_SNAPSHOT_ROOT_PSE) != ZPN_RESULT_NO_ERROR) {
        ZPN_LOG(AL_ERROR, "CUSTOMER_DR: Could not create parent snapshot directory %s", ZPN_DR_CONFIG_SNAPSHOT_ROOT_PSE);
        return;
    }

    zpn_dr_fetch_current_time_in_config_snapshot_format(current_time, ZPN_DR_CONFIG_SNAPSHOT_BUFF_LEN);
    zpn_dr_fetch_configured_snapshot_time(ZPN_BROKER_GET_GID(), ZPN_BROKER_GET_GROUP_GID(), gs->customer_id, configured_time);

    if (!stats->dr_last_config_snapshot_attempt_status || strcmp(current_time, configured_time) == 0) {
        int64_t start_time_s = epoch_s();
        // We need to detect/dump config changes only when we are connected with cloud.
        if (!is_zpn_drmode_enabled()) {
            int disk_percentage_usage = zpn_dr_get_disk_space_usage_in_percentage();
            if (disk_percentage_usage >= ZPN_DR_DISK_SPACE_USAGE_THRESHOLD_FOR_SNAPSHOT) {
                ZPN_LOG(AL_WARNING, "CUSTOMER_DR: Disk space usage(%d%%) has reached threshold(%d%%), cannot capture config snapshot",
                                    disk_percentage_usage, ZPN_DR_DISK_SPACE_USAGE_THRESHOLD_FOR_SNAPSHOT);
                return;
            }

            if (dump_dr_config(1, tickle_me) == ZPN_RESULT_NO_ERROR) {
                config_dump_success = 1;
            } else {
                stats->dr_last_config_snapshot_attempt_status = 0;
                stats->dr_config_snapshot_failed_counter ++;
                ZPN_LOG(AL_ERROR, "CUSTOMER_DR: Error in dumping DR config files while making config snapshot");
            }
        }

        if (config_dump_success) {
            // Copy current config files into a newly fresh directory
            res = zpn_dr_get_todays_config_dir_name(config_dir_name, ZPN_DR_CONFIG_SNAPSHOT_BUFF_LEN);
            if (res != ZPN_RESULT_NO_ERROR) {
                ZPN_LOG(AL_ERROR, "CUSTOMER_DR: Error getting directory name for storing config files for config snapshot, error: %s", zpath_result_string(res));
                return;
            }
            res = zpn_dr_copy_config_files(config_dir_name);
            if (res != ZPN_RESULT_NO_ERROR) {
                ZPN_LOG(AL_ERROR, "CUSTOMER_DR: Error while copying config files to directory: %s, error: %s", config_dir_name, zpath_result_string(res));
                stats->dr_last_config_snapshot_attempt_status = 0;
                stats->dr_config_snapshot_failed_counter ++;
            } else {
                int64_t now = epoch_s();
                stats->dr_last_config_snapshot_attempt_status = 1;
                stats->dr_config_snapshot_counter ++;
                stats->dr_last_config_snapshot_start_time = start_time_s;
                stats->dr_last_config_snapshot_end_time = now;
                stats->dr_config_snapshot_max_time_taken = ((now - start_time_s) > stats->dr_config_snapshot_max_time_taken) ? now - start_time_s : stats->dr_config_snapshot_max_time_taken;
                ZPN_LOG(AL_INFO, "CUSTOMER_DR: Config snapshot captured successfully into directory %s", config_dir_name);
                /*
                 * Do cleanup of old config snapshots in a lazy way
                 * Meaning, only when we successfully dump one config snapshot, we then cleanup
                 */
                zpn_dr_cleanup_old_snapshots(ZPN_DR_CONFIG_SNAPSHOT_ROOT_PSE, ZPN_DR_CONFIG_SNAPSHOT_DIR_PREFIX, zpn_dr_get_max_configured_config_snapshots(), &(stats->dr_config_snapshot_current_count));
            }
        }
    }
}

/*
 * This Monitor function wakes up every 10secs and it is mainly responsible for below jobs
 * 1. Resolve DR DNS txt record at configured interval (default 30 secs)
 * 2. Detect/Dump config changes local DR config files at fixed interval of 60 secs.
 */
static void
pbroker_dr_monitor_timer_cb(evutil_socket_t sock, short flags, void *cookie)
{
    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();
    struct zpn_private_broker_dr_stats *stats = &gs->dr_stats;
    int res = 0;
    int do_dns_check = 0;
    int do_config_dump_check = 0;
    int dns_check_interval_count = ZPN_DR_DEFAULT_RESOLVE_TXT_INTERVAL_COUNT;

    g_dr_interface_monitor_count++;
    if (tickle_me) zthread_heartbeat(tickle_me);

    dns_check_interval_count = zpn_dr_get_dns_check_interval_count();

    // Check whether we need to resolve DR DNS txt record during this monitor cycle or not.
    if (g_dr_interface_monitor_count % dns_check_interval_count == 0) {
        do_dns_check = 1;
    }

    // Check whether we need to detect/dump config changes during this monitor cycle or not.
    if (g_dr_interface_monitor_count % ZPN_DR_CONFIG_DUMP_INTERVAL_COUNT == 0) {
        do_config_dump_check = 1;
    }

    if (do_dns_check && g_config_init_done) {
        // Check whether PSE this group marked to support DR or not
        if (!is_pbroker_group_marked_to_support_dr()) {
            // This PSE is not configured to support DR.
            zpn_dr_remove_local_config_files();
            return;
        }

        if (!snapshot_mgmnt_script_written || !is_snapshot_mgmnt_script_file_exist()) {
            if (write_snapshot_mgmnt_script_file() != ZPN_RESULT_NO_ERROR) {
                ZPN_LOG(AL_ERROR, "CUSTOMER_DR: %s: Error occurred while writing snapshot management script", __func__);
            } else {
                ZPN_LOG(AL_INFO, "CUSTOMER_DR: %s: Snapshot management script file is written !", __func__);
                snapshot_mgmnt_script_written = 1;
            }
        }

        if (!binary_snapshot_init_done) {
            res = zpn_dr_binary_snapshot_init(zpn_dr_system_type_private_broker);
            if (res) {
                ZPN_LOG(AL_ERROR, "CUSTOMER_DR: %s: binary snapshot init failed, err: %s - Ignoring, Binary snapshots support not avaliable",
                        __func__, zpath_result_string(res));
            }
            binary_snapshot_init_done = 1;
        }

        // Skip DNS check if config override isn't enabled during cloud mode
        if (!zpn_dr_config_dns_check_enabled() && !is_zpn_drmode_enabled()) {
            ZPATH_LOG(AL_DEBUG, "CUSTOMER_DR: DR DNS check is not enabled (%s).", ZPN_DR_CONFIG_DNS_CHECK);
            zpn_dr_remove_local_config_files();
        } else {
            // Resolving DR DNS txt record
            zpn_dr_resolve_txt_record();
        }
    } else if (do_dns_check) {
        // Config init is not done yet. This probably could be a scenerio
        // where the cloud/config channel is down and we are trying to boot PSE.

        char buf[ZPN_DR_CONFIG_MAX_SIZE + 1];
        size_t len;

        // Check if any DR configs are cached locally. If yes then use them to resolve DR activation domain
        if (zpn_dr_local_config_exist(ZPN_DR_TXT_DOMAIN_FILE)) {
            // Local config exist. Try resolve DR domain using local config
            pthread_mutex_lock(&gs->dr_handler_lock);
            memset(buf, 0, sizeof(buf));
            len = zpn_dr_read_file_content(ZPN_DR_TXT_DOMAIN_FILE, buf, ZPN_DR_CONFIG_MAX_SIZE);
            pthread_mutex_unlock(&gs->dr_handler_lock);
            if (!len) {
                ZPATH_LOG(AL_DEBUG, "CUSTOMER_DR: Could not read DR local config:%s", ZPN_DR_TXT_DOMAIN_FILE);
                return;
            }
            resolve_dr_txt_domain(gs->zcdns, buf, NULL, dr_dns_txt_response_cb);
            stats->dr_activate_req_count++;
        }
    }

    // We need to detect/dump config changes only when we are connected with cloud.
    if (do_config_dump_check && !is_zpn_drmode_enabled() && g_config_init_done) {
        int64_t start_time_s = epoch_s();
        if (start_time_s - stats->dr_config_dump_end_time_s >= zpn_get_auto_dr_config_dump_time_interval()) {
            if (dump_dr_config(0, tickle_me) == ZPN_RESULT_NO_ERROR) {
                int64_t now = epoch_s();
                stats->dr_config_dump_start_time_s = start_time_s;
                stats->dr_config_dump_end_time_s = now;
                stats->dr_config_max_dump_time = ((now - start_time_s) > stats->dr_config_max_dump_time) ? now - start_time_s : stats->dr_config_max_dump_time;
                stats->dr_config_auto_dump_count++;
            } else {
                stats->dr_config_dump_fail_count++;
            }

            ZPN_LOG(AL_DEBUG, "CUSTOMER_DR: DR auto config stats: Auto DR config dump counter: %ld, Max time taken to dump DR config: %ld, "
                              "Last DR config dump start time: %ld, Last DR config dump end time: %ld, DR config dump failed counter: %ld",
                              (long)stats->dr_config_auto_dump_count, (long)stats->dr_config_max_dump_time, (long)stats->dr_config_dump_start_time_s,
                              (long)stats->dr_config_dump_end_time_s, (long)stats->dr_config_dump_fail_count);
        }
    }

    // Reset monitor counter if both intervals are met during this monitor cycle.
    if (do_dns_check && do_config_dump_check) {
        g_dr_interface_monitor_count = 0;
    }

    if (!is_pbroker_dr_flag_exist()) {
        if (gs->private_broker_state->drmode_enabled) {
            // DR marker file does not exist but pboker running in DR mode.
            ZPN_LOG(AL_NOTICE, "CUSTOMER_DR: DR mode is turned off. Service will now restart...");
            /* sleep to ensure contents are synced out to disk from memory before exiting */
            sleep(1);
            exit(0);
        }
    }
}

int
pbroker_automatic_dr_config_dump(struct event_base *base) {
    struct timeval tv;
    struct event *auto_config_dump_timer;
    struct event *config_snapshot_timer;

    auto_config_dump_timer = event_new(base,
                         -1,
                         EV_PERSIST,
                         pbroker_dr_monitor_timer_cb,
                         NULL);

    if (!auto_config_dump_timer) {
        ZPN_LOG(AL_ERROR, "CUSTOMER_DR: Could not create pbroker_dr_monitor_timer_cb timer");
        return ZPN_RESULT_ERR;
    }

    /*
     * Lets have the timer trigger every 10 seconds which will
     * check if we want to dump the configuration into disk now
     */
    tv.tv_sec = ZPN_DR_INTERFACE_MONITOR_INTERVAL_SECS;
    tv.tv_usec = 0;
    if (event_add(auto_config_dump_timer, &tv)) {
        ZPN_LOG(AL_ERROR, "CUSTOMER_DR: Could not activate pbroker_dr_monitor_timer_cb timer");
        return ZPN_RESULT_ERR;
    }

    config_snapshot_timer = event_new(base,
                            -1,
                            EV_PERSIST,
                            pbroker_dr_config_snapshot_monitor,
                            NULL);

    if (!config_snapshot_timer) {
        ZPN_LOG(AL_ERROR, "CUSTOMER_DR: Could not create assistant_dr_monitor_timer_cb timer");
        return ZPN_RESULT_ERR;
    }

    /*
     * Lets have the timer trigger every 35 seconds to mainly do two things,
     * 1) Periodically checks if we want to take a config snapshot based on the configured time
     * 2) Clean up the old snapshots if there are any
     *
     * Q: Why 35 seconds?
     * A: We can be in a situation where is we run it every 60 seconds, we may or may not call this routine
     *    that particular minuite window, and if we run it every 30 seconds, we may end up calling this routine
     *    twice. 35 seconds seems safer !
     */
    tv.tv_sec = 35;
    tv.tv_usec = 0;
    if (event_add(config_snapshot_timer, &tv)) {
        ZPN_LOG(AL_ERROR, "CUSTOMER_DR: Could not activate assistant_dr_config_snapshot_monitor timer");
        return ZPN_RESULT_ERR;
    }

    return ZPN_RESULT_NO_ERROR;
}


void*
pbroker_dr_interface_thread(struct zthread_info *zthread_arg, void *cookie)
{
    struct event_base *base = NULL;
    int res;

    tickle_me = zthread_arg;

    base = event_base_new();
    if (!base) {
        ZPN_LOG(AL_ERROR, "CUSTOMER_DR: Could not create event_base: pbroker_dr_interface_thread");
        goto fail;
    }

    res = pbroker_automatic_dr_config_dump(base);
    if (res) {
        ZPN_LOG(AL_ERROR, "CUSTOMER_DR: Could not initialize timer for auto dr config dump, pbroker_dr_interface_thread thread, error: %s", zpath_result_string(res));
        goto fail;
    }

    zevent_base_dispatch(base);

    ZPN_LOG(AL_ERROR, "CUSTOMER_DR: Not reachable here, pbroker_dr_interface_thread");
fail:
    /* Should watchdog... */
    while (1) {
        sleep(1);
    }
    return NULL;
}


int
pbroker_dr_interface_init(void)
{
    pthread_t thread;
    int res;
    res = zthread_create(&thread,                       // thread
                         pbroker_dr_interface_thread,   // thread func
                         NULL,                          // cookie
                         "pbroker_dr_interface_thread", // thread name
                         3*60,                          // max missing heartbeat_s
                         16 * 1024 * 1024,              // stack size bytes
                         60 * 1000 * 1000,              // user int
                         NULL);                         // user void

    if (res) return ZPN_RESULT_ERR;

    res = zpath_debug_add_read_command("DR auto config dump stats",
                          "/zpn/zpn_dr/auto_config_dump_stats",
                          auto_config_dump_stats,
                          NULL,
                          NULL);
    if (res) {
        ZPN_LOG(AL_ERROR, "CUSTOMER_DR: Could not register /zpn/zpn_dr/dump_dr_config to debug: %s", zpn_result_string(res));
        return ZPN_RESULT_ERR;
    }

    return ZPN_RESULT_NO_ERROR;
}


int write_rows_into_file(void *cookie, void *object, void *key, size_t key_len)
{
    int res;
    FILE *fp = cookie;
    struct wally_row *row = object;
    char *dump = NULL;

    dump = zpn_dr_allocate_buffer(ZPN_DR_MAX_BUF_SIZE);
    res = argo_object_dump(row->current_row, dump, ZPN_DR_MAX_BUF_SIZE, NULL, 0);
    if (res) {
        ZPN_LOG(AL_ERROR, "CUSTOMER_DR: Error while dumping argo object while writing DR config into file");
        zpn_dr_free_buffer(dump);
        return ZPN_RESULT_ERR;
    }

    if (fwrite(dump, strlen(dump), 1, fp) != 1) {
        ZPN_LOG(AL_ERROR, "CUSTOMER_DR: Error while writing json rows into DR config file");
        zpn_dr_free_buffer(dump);
        return ZPN_RESULT_ERR;
    }
    zpn_dr_free_buffer(dump);
    if (fwrite("\n", 1, 1, fp) != 1) {
        ZPN_LOG(AL_ERROR, "CUSTOMER_DR: Error while writing a new line into DR config file");
        return ZPN_RESULT_ERR;
    }

    return ZPN_RESULT_NO_ERROR;
}

int dump_wally_state(struct wally *wally_a, int is_append, struct zthread_info *z_info) {

    int result = ZPN_RESULT_NO_ERROR;
    FILE **config_fp = NULL;
    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();

    if (wally_a == NULL) {
        ZPN_LOG(AL_ERROR, "CUSTOMER_DR: wally is null for dumping wally config into a file");
        return ZPN_RESULT_BAD_DATA;
    }

    pthread_mutex_lock(&gs->dr_handler_lock);
    config_fp = ZPN_CALLOC(sizeof(FILE *) * get_cfg_file_category_size());

    for (int category_idx = zpn_dr_file_category_unknown; category_idx <= zpn_dr_file_category_other; category_idx++) {
        const char* filename = zpn_dr_lib_get_cfg_file_name_for_category(category_idx);
        config_fp[category_idx] = fopen(filename, is_append ? "a" : "w");
        if (config_fp[category_idx] == NULL) {
            ZPN_LOG(AL_ERROR, "CUSTOMER_DR: Error opening file(%s) for dumping wally state, error(%s)", filename, strerror(errno));
            result = ZPN_RESULT_ERR;
            goto done;
        }
    }

    for (int i = 0; i < wally_a->tables_count; i++) {
        int is_needed = 0;
        enum zpn_dr_file_category_e category = zpn_dr_file_category_unknown;
        // check whether table is needed for dr
        if (ZPN_DR_RESULT_NO_ERROR != zpn_dr_lib_is_db_cfg_table_needed_for_dr(wally_a->tables[i]->name, &is_needed, &category)) {
            ZPN_LOG(AL_ERROR, "CUSTOMER_DR: Error while checking if the table(%s) is needed for dr config", wally_a->tables[i]->name);
            result = ZPN_RESULT_ERR;
            goto done;
        }

        if (is_needed) {
            if (wally_a->tables[i]->integer_key) {
                int64_t walk_key = 0;
                zhash_table_walk(wally_a->tables[i]->key_rows_integer, &walk_key, write_rows_into_file, config_fp[(int)category]);
            } else {
                int64_t walk_key = 0;
                zhash_table_walk(wally_a->tables[i]->key_rows_string, &walk_key, write_rows_into_file, config_fp[(int)category]);
            }
            /*
             * tickle heartbeat after every table dump, ho harm in updating
             * to avoid unnecessary heartbeat failures
             */
            if (z_info) zthread_heartbeat(z_info);
        }
    }

done:
    if (config_fp) {
        for (int category_idx = zpn_dr_file_category_unknown; category_idx <= zpn_dr_file_category_other; category_idx++) {
            if (config_fp[category_idx]) {
                fclose(config_fp[category_idx]);
                config_fp[category_idx] = NULL;
            }
        }

        ZPN_FREE(config_fp);
        config_fp = NULL;
    }
    pthread_mutex_unlock(&gs->dr_handler_lock);

    return result;
}

int dump_dr_config(int force_dump_config, struct zthread_info *z_info) {
    int res;
    int cat_count = 0;
    enum zpn_dr_file_category_e categories[zpn_dr_file_category_other + 1];

    struct zpn_private_broker_global_state *gs = zpn_get_private_broker_global_state();
    struct wally *wally_instances[2] = {gs->private_broker_state->wally, gs->private_broker_state->wally_ovd};

    if (! force_dump_config) {
        zpn_dr_get_unmodified_categories(wally_instances, sizeof(wally_instances) / sizeof(wally_instances[0]), categories, &cat_count);

        if (!cat_count) {
            ZPN_LOG(AL_DEBUG, "CUSTOMER_DR: Configurations for DR tables are recently modified, skipping the dumping !!");
            return ZPN_RESULT_CANT_WRITE;
        }

        ZPN_LOG(AL_DEBUG, "CUSTOMER_DR: Configurations unchanged for few DR tables, hence dumping the config, number of categories to be dumped: %d", cat_count);
    }

    res = dump_wally_state(gs->private_broker_state->wally, 0, z_info);
    if (res) {
        ZPN_LOG(AL_ERROR, "CUSTOMER_DR: Error while dumping config wally state");
        return res;
    }
    res = dump_wally_state(gs->private_broker_state->wally_ovd, 1, z_info);
    if (res) {
        ZPN_LOG(AL_ERROR, "CUSTOMER_DR: Error while dumping config override wally state");
        return res;
    }

    ZPN_LOG(AL_INFO, "CUSTOMER_DR: DR config is written into files !");

    return res;
}
