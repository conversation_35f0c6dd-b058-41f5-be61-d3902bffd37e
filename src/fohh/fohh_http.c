/*
 * fohh_http.c. Copyright (C) 2014 Zscaler, Inc. All Rights Reserved
 */


#define _GNU_SOURCE

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <sys/socket.h>
#include <sys/time.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <pthread.h>
#include <ctype.h>
#include <netinet/tcp.h>
#include <errno.h>

#include <event2/event.h>
#include <event2/listener.h>
#include <event2/bufferevent_ssl.h>
#include <event2/thread.h>
#include <event2/buffer.h>

#include <openssl/ocsp.h>

#include "parson/parson.h"

#include "zcrypt/zcrypt.h"
#include <assert.h>

#include "zpath_misc/zpath_misc.h"
#include "argo/argo.h"
#include "argo/argo_hash.h"
#include "fohh/fohh.h"
#include "fohh/fohh_http.h"
#include "fohh/fohh_http_compiled.h"
#include "fohh/fohh_http_private.h"
#include "fohh/fohh_private.h"
#include "zlibevent/zlibevent_bufferevent.h"

#include "fohh/http_parser.h"

#include "fohh/ca.sarge.pem.h"

/* FIXME: temp global variable for a work around */
int is_zpn_clientd = 0;

/***************************************************
 * Configuration for standard HTTP trust.
 */
struct cert_info {
    unsigned char *data;
    size_t data_len;
};
#include "fohh/GeoTrustGlobal.pem_generated.h"
#include "fohh/digicert_ha_ev_root.pem_generated.h"
#include "fohh/VeriSign_Class_3_Public_Primary_Certification_Authority_G5.pem_generated.h"
#include "fohh/starfield.pem_generated.h"
struct cert_info trusted_certs[] = {
    {GeoTrustGlobal_pem, sizeof(GeoTrustGlobal_pem)},
    {digicert_ha_ev_root_pem, sizeof(digicert_ha_ev_root_pem)},
    {VeriSign_Class_3_Public_Primary_Certification_Authority_G5_pem, sizeof(VeriSign_Class_3_Public_Primary_Certification_Authority_G5_pem)},
    {starfield_pem, sizeof(starfield_pem)},
};
#define trusted_certs_count (sizeof(trusted_certs) / sizeof(trusted_certs[0]))

/* Default timeout in seconds for FOHH HTTP Client request */
#define FOHH_HTTP_CLIENT_DEFAULT_TIMEOUT 10

struct callback_struct {
    struct zpath_interlock *lock;

    int *http_status;
    enum fohh_http_client_request_status *status;
    struct evbuffer **result_body;
};

static char **fohh_ext_trusted_ca_certs;
static size_t fohh_ext_trusted_ca_certs_count = 0;

extern struct fohh_global_state fohh_g;

static struct fohh_http_listener fohh_http_listeners[FOHH_MAX_HTTP_LISTENERS];
static int fohh_http_listeners_count;
static pthread_mutex_t fohh_http_lock = (pthread_mutex_t) PTHREAD_MUTEX_INITIALIZER;
static http_parser_settings http_parser_callbacks;
struct argo_structure_description *fohh_http_request_description;

static void fohh_http_client_state_machine(struct fohh_http_client *client);
int fohh_http_client_parser_cb_begin(http_parser *parser);
int fohh_http_client_parser_cb_status(http_parser *parser, const char *at, size_t length);
int fohh_http_client_parser_cb_header_field(http_parser *parser, const char *at, size_t length);
int fohh_http_client_parser_cb_header_value(http_parser *parser, const char *at, size_t length);
int fohh_http_client_parser_cb_header_complete(http_parser *parser);
int fohh_http_client_parser_cb_body(http_parser *parser, const char *at, size_t length);
int fohh_http_client_parser_cb_complete(http_parser *parser);

struct http_parser_settings fohh_http_client_parser_settings = {
    fohh_http_client_parser_cb_begin,           // http_cb      on_message_begin;
    NULL,                                       // http_data_cb on_url;
    fohh_http_client_parser_cb_status,          // http_data_cb on_status;
    fohh_http_client_parser_cb_header_field,    // http_data_cb on_header_field;
    fohh_http_client_parser_cb_header_value,    // http_data_cb on_header_value;
    fohh_http_client_parser_cb_header_complete, // http_cb      on_headers_complete;
    fohh_http_client_parser_cb_body,            // http_data_cb on_body;
    fohh_http_client_parser_cb_complete,        // http_cb      on_message_complete;
    NULL,                                       // http_cb      on_chunk_header;
    NULL                                        // http_cb      on_chunk_complete;
};

static const char *fohh_http_method_str[] = {
    [FOHH_HTTP_METHOD_DELETE] = "DELETE",
    [FOHH_HTTP_METHOD_GET] = "GET",
    [FOHH_HTTP_METHOD_HEAD] = "HEAD",
    [FOHH_HTTP_METHOD_POST] = "POST",
    [FOHH_HTTP_METHOD_PUT] = "PUT",
    [FOHH_HTTP_METHOD_CONNECT] = "CONNECT",
    [FOHH_HTTP_METHOD_OPTIONS] = "OPTIONS",
    [FOHH_HTTP_METHOD_TRACE] = "TRACE",
    [FOHH_HTTP_METHOD_COPY] = "COPY",
    [FOHH_HTTP_METHOD_LOCK] = "LOCK",
    [FOHH_HTTP_METHOD_MKCOL] = "MKCOL",
    [FOHH_HTTP_METHOD_MOVE] = "MOVE",
    [FOHH_HTTP_METHOD_PROPFIND] = "PROPFIND",
    [FOHH_HTTP_METHOD_PROPPATCH] = "PROPPATCH",
    [FOHH_HTTP_METHOD_SEARCH] = "SEARCH",
    [FOHH_HTTP_METHOD_UNLOCK] = "UNLOCK",
    [FOHH_HTTP_METHOD_REPORT] = "REPORT",
    [FOHH_HTTP_METHOD_MKACTIVITY] = "MKACTIVITY",
    [FOHH_HTTP_METHOD_CHECKOUT] = "CHECKOUT",
    [FOHH_HTTP_METHOD_MERGE] = "MERGE",
    [FOHH_HTTP_METHOD_MSEARCH] = "MSEARCH",
    [FOHH_HTTP_METHOD_NOTIFY] = "NOTIFY",
    [FOHH_HTTP_METHOD_SUBSCRIBE] = "SUBSCRIBE",
    [FOHH_HTTP_METHOD_UNSUBSCRIBE] = "UNSUBSCRIBE",
    [FOHH_HTTP_METHOD_PATCH] = "PATCH",
    [FOHH_HTTP_METHOD_PURGE] = "PURGE",
    [FOHH_HTTP_METHOD_MKCALENDAR] = "MKCALENDAR"
};

static int fohh_http_method_str_count = sizeof(fohh_http_method_str) / sizeof(fohh_http_method_str[0]);


static const char *status_description[600] = {
    [0] = NULL,
    [100] = "Continue",
    [101] = "Switching Protocols",
    [102] = "Processing",
    [200] = "OK",
    [201] = "Created",
    [202] = "Accepted",
    [203] = "Non-Authoritative Information",
    [204] = "No Content",
    [205] = "Reset Content",
    [206] = "Partial Content",
    [207] = "Multi-Status",
    [208] = "Already Reported",
    [226] = "IM Used",
    [300] = "Multiple Choices",
    [301] = "Moved Permanently",
    [302] = "Found",
    [303] = "See Other",
    [304] = "Not Modified",
    [305] = "Use Proxy",
    [306] = "Switch Proxy",
    [307] = "Temporary Redirect",
    [308] = "Permanent Redirect",
    [400] = "Bad Request",
    [401] = "Unauthorized",
    [402] = "Payment Required",
    [403] = "Forbidden",
    [404] = "Not Found",
    [405] = "Method Not Allowed",
    [406] = "Not Acceptable",
    [407] = "Proxy Authentication Required",
    [408] = "Request Timeout",
    [409] = "Conflict",
    [410] = "Gone",
    [411] = "Length Required",
    [412] = "Precondition Failed",
    [413] = "Request Entity Too Large",
    [414] = "Request-URI Too Long",
    [415] = "Unsupported Media Type",
    [416] = "Requested Range Not Satisfiable",
    [417] = "Expectation Failed",
    [419] = "Authentication Timeout",
    [420] = "Enhance Your Calm",
    [422] = "Unprocessable Entity",
    [423] = "Locked",
    [424] = "Failed Dependency",
    [426] = "Upgrade Required",
    [428] = "Precondition Required",
    [429] = "Too Many Requests",
    [431] = "Request Header Fields Too Large",
    [440] = "Login Timeout",
    [444] = "No Response",
    [449] = "Retry With",
    [450] = "Blocked by Windows Parental Controls",
    [451] = "Redirect",
    [494] = "Request Header Too Large",
    [495] = "Cert Error",
    [496] = "No Cert",
    [497] = "HTTP to HTTPS",
    [498] = "Token expired/invalid",
    [499] = "Token required",
    [500] = "Internal Server Error",
    [501] = "Not Implemented",
    [502] = "Bad Gateway",
    [503] = "Service Unavailable",
    [504] = "Gateway Timeout",
    [505] = "HTTP Version Not Supported",
    [506] = "Variant Also Negotiates",
    [507] = "Insufficient Storage",
    [508] = "Loop Detected",
    [509] = "Bandwidth Limit Exceeded",
    [510] = "Not Extended",
    [511] = "Network Authentication Required",
    [520] = "Origin Error",
    [521] = "Web server is down",
    [522] = "Connection timed out",
    [523] = "Proxy Declined Request",
    [524] = "A timeout occurred",
    [598] = "Network read timeout error",
    [599] = "Network connect timeout error"
};

#define STATUS_DESCRIPTION_COUNT (sizeof(status_description) / sizeof(status_description[0]))


struct header_info {
    const char *name;
    size_t offset;
};

/*
 * IMPORTANT: For common name prefixes, these must be in order from
 * longest to shortest. i.e. 'Accept' must come after
 * 'Accept-Encoding'
 */
struct header_info all_headers[] = {
    {"Accept-Encoding", offsetof(struct fohh_http_request, hdr_accept_encoding)},
    {"Accept-Language", offsetof(struct fohh_http_request, hdr_accept_language)},
    {"Accept", offsetof(struct fohh_http_request, hdr_accept)},
    {"Authorization", offsetof(struct fohh_http_request, hdr_authorization)},
    {"Cache-Control", offsetof(struct fohh_http_request, hdr_cache_control)},
    {"Connection", offsetof(struct fohh_http_request, hdr_connection)},
    {"Content-Encoding", offsetof(struct fohh_http_request, hdr_content_encoding)},
    {"Content-Length", offsetof(struct fohh_http_request, hdr_content_length)},
    {"Content-Type", offsetof(struct fohh_http_request, hdr_content_type)},
    {"Cookie", offsetof(struct fohh_http_request, hdr_cookie)},
    {"Date", offsetof(struct fohh_http_request, hdr_date)},
    {"Expect", offsetof(struct fohh_http_request, hdr_expect)},
    {"Host", offsetof(struct fohh_http_request, hdr_host)},
    {"If-Match", offsetof(struct fohh_http_request, hdr_if_match)},
    {"If-Modified-Since", offsetof(struct fohh_http_request, hdr_if_modified_since)},
    {"If-None-Match", offsetof(struct fohh_http_request, hdr_if_none_match)},
    {"If-Range", offsetof(struct fohh_http_request, hdr_if_range)},
    {"If-Unmodified-Since", offsetof(struct fohh_http_request, hdr_if_unmodified_since)},
    {"Keep-Alive", offsetof(struct fohh_http_request, hdr_keep_alive)},
    {"Range", offsetof(struct fohh_http_request, hdr_range)},
    {"Referer", offsetof(struct fohh_http_request, hdr_referer)},
    {"Transfer-Encoding", offsetof(struct fohh_http_request, hdr_transfer_encoding)},
    {"Upgrade", offsetof(struct fohh_http_request, hdr_upgrade)},
    {"User-Agent", offsetof(struct fohh_http_request, hdr_user_agent)},
    {"Via", offsetof(struct fohh_http_request, hdr_via)},
    {"X-Forwarded-For", offsetof(struct fohh_http_request, hdr_x_forwarded_for)},
    {"Forwarded-For", offsetof(struct fohh_http_request, hdr_forwarded)},
    {"X-Real-IP", offsetof(struct fohh_http_request, hdr_x_real_ip)},
};


static pthread_mutex_t freeq_lock = (pthread_mutex_t) PTHREAD_MUTEX_INITIALIZER;
static struct fohh_http_server_connection_head freeq;






static void fohh_http_server_process_received_data(struct fohh_http_server_connection *connection);







static struct fohh_http_server_connection *http_connection_alloc(void)
{
    struct fohh_http_server_connection *connection;
    pthread_mutex_lock(&freeq_lock);
    connection = LIST_FIRST(&freeq);
    if (connection) {
        LIST_REMOVE(connection, free_connections);
        if (!connection->exists_in_free_connections_list) {
            FOHH_LOG(AL_CRITICAL, "Connection: %s: Allocatine while not in free queue!", connection->description);
        }
        connection->exists_in_free_connections_list = 0;
    } else {
        connection = (struct fohh_http_server_connection *) FOHH_CALLOC(sizeof(*connection));
        connection->lock = (pthread_mutex_t) PTHREAD_MUTEX_INITIALIZER;
        connection->req.resp_buf = evbuffer_new();
        assert(connection->req.resp_buf);
        evbuffer_set_dont_dump(connection->req.resp_buf);
        connection->req.req_body = evbuffer_new();
        assert(connection->req.req_body);
        evbuffer_set_dont_dump(connection->req.req_body);
    }
    pthread_mutex_unlock(&freeq_lock);
    return connection;
}

static void http_connection_free(struct fohh_http_server_connection *connection)
{
    pthread_mutex_lock(&freeq_lock);
    if (connection->exists_in_free_connections_list) {
        FOHH_LOG(AL_CRITICAL, "Connection: %s: Freeing while in free queue!", connection->description);
    }
    connection->exists_in_free_connections_list = 1;
    connection->incarnation++;
    LIST_INSERT_HEAD(&freeq, connection, free_connections);
    pthread_mutex_unlock(&freeq_lock);
}


static int header_url_cb(http_parser *parser, const char *at, size_t length)
{
    struct fohh_http_server_connection *connection = parser->data;
    struct fohh_http_request *req = &(connection->req.request);

    if (req->req_url) {
        /* Append */
        req->req_url = zmicro_heap_append_str(&(connection->req.heap), req->req_url, strlen(req->req_url), at, length);
    } else {
        req->req_url = zmicro_heap_str(&(connection->req.heap), at, length);
    }

    if (!req->req_url) {
        FOHH_LOG(AL_ERROR, "Connection %s: Could not heap the url", connection->description);
        return -1;
    }

    return 0;
}


static int query_parse(struct fohh_http_server_connection *connection)
{
    enum states {
        init,
        key_found,
        key_done,
        value_found,
        value_done,
        done
    };


    struct fohh_http_query_string **q;
    struct fohh_http_request *request = &(connection->req.request);

    const char *w = request->req_query;

    const char *key_start = w;
    const char *key_end = NULL;

    const char *value_start = NULL;
    const char *value_end = NULL;

    enum states s = init;

    char ch;

    if (!w) return FOHH_RESULT_NO_ERROR;

    q = &(request->queries);

    while (s != done) {
        ch = *w;
        switch (s) {
        case init:
        case value_done:
            if (ch == 0) {
                s = done;
                break;
            }
            if (isspace(ch) || (ch == '&') || (ch == '=')) {
                break;
            }
            key_start = w;
            s = key_found;
            break;
        case key_found:
            key_end = w;
            if (isspace(ch) || (ch == 0)) {
                value_start = w;
                value_end = w;
                if (ch == 0) {
                    s = done;
                } else {
                    s = value_done;
                }
                break;
            }
            if (ch == '=') {
                s = key_done;
                break;
            } else if (ch == '&') {
                s = key_done;
                ZPATH_FALLTHROUGH;
            } else {
                break;
            }

        case key_done:
            /* Skip '=', whitespace */
            if (isspace(ch) || (ch == 0) || (ch == '&')) {
                /* Erk. */
                value_start = w;
                value_end = w;
                if (ch == 0) {
                    s = done;
                } else {
                    s = value_done;
                }
                break;
            }
            if (ch == '=') break;
            value_start = w;
            s = value_found;
            break;

        case value_found:
            value_end = w;
            if ((ch == '&') || isspace(ch) || (ch == 0)) {
                if (ch == 0) {
                    s = done;
                } else {
                    s = value_done;
                }
                break;
            }
            break;
        case done:
        default:
            break;
        }
        w++;
        if ((s == value_done) || (s == done)) {
            struct fohh_http_query_string *qs;

            if (key_start && key_end && value_start && value_end) {
                qs = zmicro_heap_alloc_aligned(&(connection->req.heap), sizeof(*qs));
                if (!qs) {
                    return FOHH_RESULT_NO_MEMORY;
                }
                qs->key = zmicro_heap_str(&(connection->req.heap), key_start, key_end - key_start);
                if (!qs->key) return FOHH_RESULT_NO_MEMORY;
                qs->value = zmicro_heap_str(&(connection->req.heap), value_start, value_end - value_start);
                if (!qs->value) return FOHH_RESULT_NO_MEMORY;

                FOHH_DEBUG_HTTP_PARSER("Connection %s: Query Key = \"%s\", Value = \"%s\"",
                                       connection->description,
                                       qs->key,
                                       qs->value);

                /* Link it in: */
                qs->next = (*q);
                (*q) = qs;
            }
            key_end = NULL;
            value_start = NULL;
            value_end = NULL;
        }
    }
    return FOHH_RESULT_NO_ERROR;
}


/*
 * Placed all post-header-processing here.
 *
 * Parse URL
 *
 * Parse Host + Port
 */
static int header_complete_cb(http_parser *parser)
{
    struct fohh_http_server_connection *connection = parser->data;
    struct fohh_http_request *req = &(connection->req.request);
    int res;
    struct http_parser_url url;
    const char *parse_host;

    memset(&url, 0, sizeof(url));

    if (req->req_url == NULL) {
        FOHH_LOG(AL_ERROR, "Connection %s: Inconsistent- no URL and headers done", connection->description);
        return -1;
    }

    FOHH_DEBUG_HTTP_PARSER("Connection %s: URL: \"%s\"", connection->description, req->req_url);

    res = http_parser_parse_url(req->req_url, strlen(req->req_url), parser->method == HTTP_CONNECT, &url);
    if (res) {
        FOHH_LOG(AL_NOTICE, "Connection %s: Could not parse URL \"%s\"", connection->description, req->req_url);
        return -1;
    } else {
        req->method = parser->method;
        req->req_method = http_method_str(parser->method);

#define DF(xx, yy) do {                                                                 \
            if (url.field_set & (1 << xx)) {                                            \
                FOHH_DEBUG_HTTP_PARSER("Connection %s: " #xx "=%.*s",                   \
                                       connection->description,                         \
                                       (int) url.field_data[xx].len,                    \
                                       req->req_url + url.field_data[xx].off);          \
                req->yy = zmicro_heap_str(&(connection->req.heap),                      \
                                          req->req_url + url.field_data[xx].off,        \
                                          url.field_data[xx].len);                      \
                if (!req->yy) {                                                         \
                    FOHH_LOG(AL_ERROR, "Connection %s: Could not heap url field " #yy,  \
                             connection->description);                                  \
                    return -1;                                                          \
                }                                                                       \
            }                                                                           \
        } while (0)

        DF(UF_SCHEMA, req_schema);
        DF(UF_HOST, req_host);
        DF(UF_PORT, req_port);
        DF(UF_PATH, req_path);
        DF(UF_QUERY, req_query);
        DF(UF_FRAGMENT, req_fragment);
        DF(UF_USERINFO, req_userinfo);
    }


    res = query_parse(connection);
    if (res) {
        FOHH_LOG(AL_NOTICE, "Connection: %s Could not parse query string", connection->description);
        return -1;
    }


    if (req->hdr_host) {
        parse_host = req->hdr_host;
    } else if (req->req_host) {
        parse_host = req->req_host;
    } else {
        parse_host = NULL;
    }
    if (parse_host) {
        /* Figure out length of host. */
        char downcase_host[1000];
        char port[50];
        char *s, *e;
        const char *w;

        s = downcase_host;
        e = s + sizeof(downcase_host) - 1;
        w = parse_host;

        /* Note: Host can be IPv6 format, in which case it is encased in brackets.
         * i.e. the host might be:
         *
         * domain.com:port
         *
         * *******:port
         *
         * [ffff::ffff]:port
         * [::FfFf:*******]:port  Or whatever
         */
        if (w) {
            /* Downcase the host */
            int have_bracket = 0;
            int err = 0;
            while (s < e) {
                char ch = *w;
                if (ch == 0) break;
                if (((ch < 'a') || (ch > 'z')) &&
                    ((ch < 'A') || (ch > 'Z')) &&
                    ((ch < '0') || (ch > '9')) &&
                    (ch != '.') &&
                    (ch != '[') &&
                    (ch != ']') &&
                    (ch != '-') &&
                    (ch != '_')) {
                    break;
                }
                if ((ch == ':') && (!have_bracket)) {
                    break;
                }
                if (ch == '[') {
                    if (have_bracket) {
                        err = 1;
                        break;
                    }
                    have_bracket = 1;
                }
                *s = tolower(*w);
                s++;
                w++;
                if (ch == ']') {
                    if (!have_bracket) {
                        err = 1;
                        break;
                    }
                    break;
                }
            }
            if (!err) {
                *s = 0;
                int len = (int) (s - downcase_host);
                if (len) {
                    req->norm_host = zmicro_heap_str(&(connection->req.heap),
                                                     downcase_host,
                                                     len);
                    if (req->norm_host) {
                        FOHH_DEBUG_HTTP_PARSER("Connection %s: Normalized host = %s", connection->description, req->norm_host);
                    }
                }
                if ((*w) == ']') w++;
                if ((*w) == ':') w++;
                if ((*w)) {
                    /* Pull out port. */
                    s = port;
                    e = s + sizeof(port);
                    while (s < e) {
                        char ch = *w;
                        if ((ch < '0') || (ch > '9')) {
                            break;
                        }
                        *s = *w;
                        s++;
                        w++;
                    }
                    len = (int) (s - port);
                    if (len)  {
                        req->norm_host_port = zmicro_heap_str(&(connection->req.heap),
                                                              port,
                                                              len);
                        if (req->norm_host_port) {
                            FOHH_DEBUG_HTTP_PARSER("Connection %s: Normalized port = %s", connection->description, req->norm_host_port);
                        }
                    }
                }
            }
        }
    }

    if (connection->is_handoff) {
        connection->state = fohh_http_server_connection_handoff;
        /* Returning an error will short-circuit parsing right
         * here. We could probably just call our callback here, but
         * we'll exit control. */
        return -1;
    }

    return 0;
}

static int header_field_cb(http_parser *parser, const char *at, size_t length)
{
    int i;
    int max = sizeof(all_headers) / sizeof(all_headers[0]);
    struct fohh_http_server_connection *connection = parser->data;

    if (length == 0) {
        FOHH_LOG(AL_ERROR, "Connection %s: Expecting non-zero length", connection->description);
        return 0;
    }

    if (connection->req.header_field_in_progress) {
        /* Add this data to currently collected header ... */
        size_t cur_len = strlen(connection->req.header_field);
        if ((cur_len + length) >= sizeof(connection->req.header_field)) {
            FOHH_LOG(AL_ERROR, "Connection %s: excessively long header \"%.*s\"+\"%.*s\"",
                     connection->description,
                     (int) cur_len,
                     connection->req.header_field,
                     (int) length,
                     at);
            return -1;
        }
        memcpy(&(connection->req.header_field[cur_len]), at, length);
        connection->req.header_field[cur_len + length] = 0;
    } else {
        if (length > sizeof(connection->req.header_field) - 1 ) {
            FOHH_LOG(AL_ERROR, "Connection %s: excessively long header \"%.*s\"",
                     connection->description,
                     (int) length,
                     at);
            return -1;
        }
        memcpy(&(connection->req.header_field[0]), at, length);
        connection->req.header_field[length] = 0;
        connection->req.header_field_in_progress = 1;
    }

    connection->req.current_header_value = NULL;
    for (i = 0; i < max; i++) {
        if (strncasecmp(at, all_headers[i].name, length) == 0) {
            connection->req.current_header_value = (const char **) (((uint8_t *) &(connection->req.request)) + all_headers[i].offset);
            FOHH_DEBUG_HTTP_PARSER("Connection %s: field CB: \"%s\"", connection->description, connection->req.header_field);
            break;
        }
    }
    if (i == max) {
        FOHH_DEBUG_HTTP_PARSER("Connection %s: unrecognized or partial field CB: \"%s\"", connection->description, connection->req.header_field);
    }

    return 0;
}

static int header_value_cb(http_parser *parser, const char *at, size_t length)
{
    struct fohh_http_server_connection *connection = parser->data;

    FOHH_DEBUG_HTTP_PARSER("Connection %s: Value CB: \"%.*s\"", connection->description, (int) length, at);

    connection->req.header_field_in_progress = 0;

    if (connection->req.current_header_value == NULL) {
        /* Unrecognized header. */
    } else {
        const char *str;
        if ((*(connection->req.current_header_value)) == NULL) {
            str = zmicro_heap_str(&(connection->req.heap), at, length);
            if (str) {
                *(connection->req.current_header_value) = str;
            } else {
                FOHH_LOG(AL_ERROR, "Connection %s: Could not str header", connection->description);
                return -1;
            }
        } else {
            str = zmicro_heap_append_str(&(connection->req.heap), *(connection->req.current_header_value), strlen(*(connection->req.current_header_value)), at, length);
            if (str) {
                FOHH_DEBUG_HTTP_PARSER("Connection %s: Appended header value...", connection->description);
                *(connection->req.current_header_value) = str;
            } else {
                FOHH_LOG(AL_ERROR, "Connection %s: Could not re_str header", connection->description);
                return -1;
            }
        }
    }

    return 0;
}

void parse_reset(struct fohh_http_server_connection *connection)
{
    FOHH_DEBUG_HTTP_SERVER("Connection %s: Parse reset", connection->description);
    zmicro_heap_init(&(connection->req.heap), connection->req.header_value_buf, sizeof(connection->req.header_value_buf));
    memset(&(connection->req.request), 0, sizeof(connection->req.request));
    http_parser_init(&(connection->req.parser), HTTP_REQUEST);
    connection->req.parser.data = connection;
    connection->req.header_field_in_progress = 0;
    connection->req.current_header_value = 0;
}

static struct fohh_http_server_registered_path *
path_find(struct fohh_http_server *server, struct fohh_http_request *request)
{
    void *host_void[10];
#define URL_HOST_MAX (sizeof(host_void) / sizeof(host_void[0]))
    void *path_void[10];
#define URL_PATH_MAX (sizeof(path_void) / sizeof(path_void[0]))
    size_t host_count;
    size_t host_ix;
    size_t path_count;
    const char *path_str;
    size_t path_len;
    struct fohh_http_server_registered_path *ret_path = NULL;

    pthread_rwlock_rdlock(&(server->lock));

    if (request->norm_host) {
        /* Look up the host */
        host_count = diamond_search(server->hosts,
                                    (const uint8_t *)request->norm_host,
                                    strlen(request->norm_host),
                                    &(host_void[0]),
                                    NULL,
                                    URL_HOST_MAX);
        if (host_count < URL_HOST_MAX) {
            if (server->default_host) {
                host_void[host_count] = server->default_host;
                host_count++;
            }
        }
    } else {
        if (server->default_host) {
            host_void[0] = server->default_host;
            host_count = 1;
        } else {
            host_count = 0;
        }
    }

    if (request->req_path) {
        path_len = strlen(request->req_path);
        path_str = request->req_path;
    } else {
        path_len = 0;
        path_str = "";
    }

    for (host_ix = 0; host_ix < host_count; host_ix++) {
        struct fohh_http_server_registered_host *host = host_void[host_ix];
        path_count = diamond_search(host->paths,
                                    (const uint8_t *)path_str,
                                    path_len,
                                    &(path_void[0]),
                                    NULL,
                                    URL_PATH_MAX);
        if (path_count) {
            ret_path = path_void[0];
            break;
        }
        FOHH_DEBUG_HTTP_SERVER("Server %s: Host %p (%s): Lookup of path \"%s\" failed",
                               server->name,
                               host,
                               host->host,
                               path_str);
        if (host->default_path) {
            ret_path = host->default_path;
            break;
        }
    }

    pthread_rwlock_unlock(&(server->lock));

    return ret_path;
}

/*
 * Finding path for the command that we received via domain socket.
 *  NOTE: we are leveraging the http server and the registration path; so that the command is available via both paths.
 */
struct fohh_http_server_registered_path *
path_find_command_request(struct fohh_http_server *server, const char *req_path)
{
    void *host_void[10];
#define URL_HOST_MAX (sizeof(host_void) / sizeof(host_void[0]))
    void *path_void[10];
#define URL_PATH_MAX (sizeof(path_void) / sizeof(path_void[0]))
    size_t host_count;
    size_t host_ix;
    size_t path_count;
    const char *path_str;
    size_t path_len;
    struct fohh_http_server_registered_path *ret_path = NULL;

    if (!server) {
        FOHH_LOG(AL_CRITICAL, "command-listener: server not available, return!");
        return NULL;
    }

    pthread_rwlock_rdlock(&(server->lock));

    //We will lookup the default host
    if (server->default_host) {
        host_void[0] = server->default_host;
        host_count = 1;
    } else {
        host_void[0] = NULL;
        host_count = 0;
    }

    //Host path
    if (req_path) {
        path_len = strlen(req_path);
        path_str = req_path;
    } else {
        path_len = 0;
        path_str = "";
    }

    for (host_ix = 0; host_ix < host_count; host_ix++) {
        struct fohh_http_server_registered_host *host = host_void[host_ix];
        path_count = diamond_search(host->paths,
                                    (const uint8_t *)path_str,
                                    path_len,
                                    &(path_void[0]),
                                    NULL,
                                    URL_PATH_MAX);
        if (path_count) {
            ret_path = path_void[0];
            break;
        }
        FOHH_DEBUG_HTTP_SERVER("command-listener: Server %s: Host %p (%s): Lookup of path \"%s\" failed",
                               server->name,
                               host,
                               host->host,
                               path_str);
        if (host->default_path) {
            ret_path = host->default_path;
            break;
        }
    }

    pthread_rwlock_unlock(&(server->lock));
    return ret_path;
}


static int body_parser_cb(http_parser *parser, const char *at, size_t length)
{
    struct fohh_http_server_connection *connection = parser->data;

    if (connection->req.req_body) {
        evbuffer_add(connection->req.req_body, at, length);
        return 0;
    } else {
        return 1;
    }
}

static int message_complete_cb(http_parser *parser)
{
    struct fohh_http_server_connection *connection = parser->data;
    struct fohh_http_server *server = connection->listener->server;
    struct fohh_http_server_registered_path *path = NULL;
    struct fohh_http_response response;
    char dbg_buf[4096];
    int retval = 0;
    int res = 0;

    if (fohh_debug & FOHH_DEBUG_HTTP_PARSER_BIT) {
        if (ARGO_RESULT_NO_ERROR == argo_structure_dump(fohh_http_request_description, &(connection->req.request), dbg_buf, sizeof(dbg_buf), NULL, 1)) {
            FOHH_DEBUG_HTTP_PARSER("Connection %s: RX request = %s", connection->description, dbg_buf);
        } else {
            FOHH_DEBUG_HTTP_PARSER("Connection %s: RX large request", connection->description);
        }
    }

    /* Find registered host+path */
    path = path_find(server, &(connection->req.request));

    pthread_mutex_lock(&(connection->lock));
    if (!path) {
        /* Send 404 */
        response.http_version_major = connection->req.parser.http_major;
        response.http_version_minor = connection->req.parser.http_minor;
        response.status = 404;
        response.content_type = NULL;
        response.extra_headers = NULL;
        response.body = evbuffer_new();
        if (!response.body) {
            FOHH_LOG(AL_WARNING, "Connection: %s could not allocate evbuffer", connection->description);
            parse_reset(connection);
            connection->state = fohh_http_server_connection_error;
            retval = -1;
            goto done;
        }
        evbuffer_set_dont_dump(response.body);
        evbuffer_add_printf(response.body, "404 Not Found\n\r");
        res = fohh_http_send_sync_response(connection, &response);
    } else {
        connection->req.request.http_version_major = connection->req.parser.http_major;
        connection->req.request.http_version_minor = connection->req.parser.http_minor;
        if (path->request_cb) {
            res = (path->request_cb)(connection,
                                     connection->incarnation,
                                     &(connection->req.request),
                                     path->host->host,
                                     path->path,
                                     path->void_cookie,
                                     path->int_cookie);
        } else {
            FOHH_LOG(AL_ERROR, "Connection: %s no request callback", connection->description);
            res = FOHH_RESULT_ERR;
        }
    }

    if (res == FOHH_RESULT_NO_ERROR) {
        /*
         * Synchronous response sent. This is darn near the 'do
         * nothing' case.
         */
        /* Continue parsing: */
        connection->state = fohh_http_server_connection_active;
        FOHH_DEBUG_HTTP_SERVER("Connection %s: Completed request with valid callback", connection->description);
        retval = 0;
    } else if (res == FOHH_RESULT_ASYNCHRONOUS) {
        /*
         * Waiting for asynchronous response. We turn off request
         * parsing and wait for a response.
         */
        connection->state = fohh_http_server_connection_waiting_asynchronous_response;
        FOHH_DEBUG_HTTP_SERVER("Connection %s: Completed request asynchronous callback", connection->description);
        /* Cause parse stop: */
        retval = -1;
    } else {
        /*
         * Some error occurred. Should we close?
         */
        connection->state = fohh_http_server_connection_active;
        /* Cause parse stop: */
        FOHH_DEBUG_HTTP_SERVER("Connection %s: Request processing failed- return internal server error.", connection->description);
        response.http_version_major = connection->req.parser.http_major;
        response.http_version_minor = connection->req.parser.http_minor;
        response.status = 500;
        response.content_type = NULL;
        response.extra_headers = NULL;
        response.body = evbuffer_new();
        if (!response.body) {
            FOHH_LOG(AL_WARNING, "Connection: %s could not allocate evbuffer", connection->description);
            parse_reset(connection);
            connection->state = fohh_http_server_connection_error;
            retval = -1;
            goto done;
        }
        evbuffer_set_dont_dump(response.body);
        evbuffer_add_printf(response.body, "500 Internal Server Error\n\r");
        res = fohh_http_send_sync_response(connection, &response);
        retval = -1;
    }

 done:
    /*
     * Clean up our parsing state.
     */
    pthread_mutex_unlock(&(connection->lock));

    return retval;
}


/*
 * Resume transmit on connection
 *
 * MUST be called from correct thread context.
 */
static int fohh_http_rx_resume(struct fohh_http_server_connection *connection)
{
    short events;

    events = bufferevent_get_enabled(connection->io_bufferevent);
    events |= EV_READ;
    bufferevent_enable(connection->io_bufferevent, events);
    FOHH_DEBUG_HTTP_PARSER("Connection %s: bufferevent now%s%s",
                           connection->description,
                           events & EV_READ ? " EV_READ":"",
                           events & EV_WRITE ? " EV_WRITE":"");
    fohh_http_server_process_received_data(connection);
    return FOHH_RESULT_NO_ERROR;
}

/*
 * Resume transmit on connection
 *
 * MUST be called from correct thread context.
 */
static int fohh_http_rx_pause(struct fohh_http_server_connection *connection)
{
    short events;
    bufferevent_disable(connection->io_bufferevent, EV_READ);

    events = bufferevent_get_enabled(connection->io_bufferevent);
    FOHH_DEBUG_HTTP_PARSER("Connection %s: bufferevent now%s%s",
                           connection->description,
                           events & EV_READ ? " EV_READ":"",
                           events & EV_WRITE ? " EV_WRITE":"");
    return FOHH_RESULT_NO_ERROR;
}


static int fohh_http_init(void)
{
    memset(&http_parser_callbacks, 0, sizeof(http_parser_callbacks));

    http_parser_callbacks.on_url = header_url_cb;

    http_parser_callbacks.on_header_field = header_field_cb;
    http_parser_callbacks.on_header_value = header_value_cb;

    http_parser_callbacks.on_headers_complete = header_complete_cb;
    http_parser_callbacks.on_body = body_parser_cb;
    http_parser_callbacks.on_message_complete = message_complete_cb;

    fohh_http_request_description = argo_register_global_structure(FOHH_HTTP_REQUEST_HELPER);

    LIST_INIT(&freeq);

    return FOHH_RESULT_NO_ERROR;
}


static struct fohh_http_server *fohh_http_server_create_internal(const char *name, int is_handoff)
{
    struct fohh_http_server *server;
    static int initialized = 0;
    int res;

    pthread_mutex_lock(&fohh_http_lock);
    if (!initialized) {
        initialized = 1;
        res = fohh_http_init();
        if (res != FOHH_RESULT_NO_ERROR) {
            FOHH_LOG(AL_ERROR, "Could not init fohh_http");
            pthread_mutex_unlock(&fohh_http_lock);
            return NULL;
        }
    }
    pthread_mutex_unlock(&fohh_http_lock);

    server = FOHH_CALLOC(sizeof(*server));
    if (server) {
        server->is_handoff = is_handoff;
        server->name = FOHH_STRDUP(name, strlen(name));
        if (!server->name) {
            FOHH_FREE(server);
            return NULL;
        }

        LIST_INIT(&(server->listeners));

        server->lock = (pthread_rwlock_t)PTHREAD_RWLOCK_INITIALIZER;

        server->hosts = diamond_create(0, NULL, 10);
        if (!server->hosts) {
            //FOHH_FREE(server->name); It was const char allocated. Let it leak.
            FOHH_FREE(server);
            return NULL;
        }
    }
    return server;
}

struct fohh_http_server *fohh_http_server_create(const char *name)
{
    return fohh_http_server_create_internal(name, 0);
}

struct fohh_http_server *fohh_http_server_create_handoff(const char *name)
{
    return fohh_http_server_create_internal(name, 1);
}


/*
 * Generic connection free call.
 */
static void fohh_http_server_connection_close(struct fohh_http_server_connection *connection)
{
    struct evbuffer *buf;
    struct evbuffer *rbuf;
    FOHH_DEBUG_HTTP_SERVER("Connection %s: CLOSE", connection->description);
    pthread_mutex_lock(&(connection->lock));
    if (connection->exists_in_connections_list) {
        LIST_REMOVE(connection, connections);
        connection->exists_in_connections_list = 0;
    }
    if (connection->proxy_protocol_event) {
        event_del(connection->proxy_protocol_event);
        event_free(connection->proxy_protocol_event);
        connection->proxy_protocol_event = NULL;
    }
    if (connection->io_bufferevent) {
        zlibevent_bufferevent_free(connection->io_bufferevent);
        connection->io_bufferevent = NULL;
    }
    if (connection->ssl) {
        SSL_free(connection->ssl);
        connection->ssl = NULL;
    }
    if (connection->sock >= 0) {
        close(connection->sock);
        connection->sock = -1;
    }
    buf = connection->req.resp_buf;
    evbuffer_drain(connection->req.resp_buf, evbuffer_get_length(connection->req.resp_buf));
    rbuf = connection->req.req_body;
    evbuffer_drain(connection->req.req_body, evbuffer_get_length(connection->req.req_body));
    memset(&(connection->req), 0, sizeof(connection->req));
    connection->req.resp_buf = buf;
    connection->req.req_body = rbuf;

    http_connection_free(connection);
    pthread_mutex_unlock(&(connection->lock));
}

static void fohh_http_server_connection_describe(struct fohh_http_server_connection *connection)
{
    char buf1[ARGO_INET_ADDRSTRLEN];
    char buf2[ARGO_INET_ADDRSTRLEN];

    // coverity[offset : FALSE]
    // coverity[overlapping_copy : FALSE]
    snprintf(connection->description, sizeof(connection->description), "(%p) %s: s=%d %s:%d->%s:%d",
             connection,
             connection->listener->server->name,
             connection->sock,
             argo_inet_generate(buf1, &(connection->remote_ip)),
             (int)ntohs(connection->remote_port_ne),
             argo_inet_generate(buf2, &(connection->listener->addr)),
             (int)connection->listener->port_he);
}


static void fohh_http_server_handoff_received_data(struct fohh_http_server_connection *connection)
{
    uint8_t buffer[4*1024];

    struct bufferevent *bev = connection->io_bufferevent;
    struct http_parser *parser = &(connection->req.parser);
    ssize_t rx_len;
    size_t parse_len;
    struct evbuffer *rx_buffer;
    struct fohh_http_server_registered_path *path = NULL;
    int res;
    int is_sni = 0;

    /* Update timeouts... */
    connection->last_event = epoch_s();

    parse_reset(connection);

    memset(buffer, 0, sizeof(buffer));

    /* Get up to 4K contiguous rx data: */
    rx_buffer = bufferevent_get_input(bev);
    rx_len = evbuffer_copyout(rx_buffer, buffer, sizeof(buffer));
    if (rx_len < 0) {
        FOHH_LOG(AL_ERROR, "Connection %s: close for evbuffer_copyout error", connection->description);
        fohh_http_server_connection_close(connection);
    } else if (rx_len == 0) {
        /* Wait for more data */
        return;
    } else {
        parse_len = http_parser_execute(&(connection->req.parser), &http_parser_callbacks, (const char *)buffer, rx_len);
        if (parser->http_errno == 0) {
            /* Get more data. */
            return;
        }
        if (parser->http_errno > HPE_CB_status) {
            /* True parser error... Check if the connection is SSL, and pull out the SNI domain if it is. */
            char sni_str[100];
            res = get_sni_from_client_hello((const unsigned char *) buffer, (int)rx_len, sni_str, (int)sizeof(sni_str));
            if (res) {
                size_t len;
                for (len = 0; len < sizeof(sni_str); len++) {
                    if (!sni_str[len]) break;
                    sni_str[len] = tolower(sni_str[len]);
                }
                connection->req.request.norm_host = zmicro_heap_str(&(connection->req.heap),
                                                                    sni_str,
                                                                    len);
                is_sni = 1;
                FOHH_LOG(AL_NOTICE, "Connection %s: SNI client hello = <%s>", connection->description, sni_str);
            } else {
                FOHH_LOG(AL_NOTICE, "Connection %s: SNI client hello parse failed\n", connection->description);
            }
        }
        if (is_sni || ((parser->http_errno == HPE_CB_headers_complete) && (connection->state == fohh_http_server_connection_handoff))) {
            /* We have a handoff ready... */
            path = path_find(connection->listener->server, &(connection->req.request));
            if (!path) {
                FOHH_LOG(AL_ERROR, "Connection %s: handoff missed host/path", connection->description);
                fohh_http_server_connection_close(connection);
            } else {
                connection->req.request.http_version_major = connection->req.parser.http_major;
                connection->req.request.http_version_minor = connection->req.parser.http_minor;
                bufferevent_disable(connection->io_bufferevent, EV_READ|EV_WRITE);
                if (path->handoff_cb) {
                    res = (path->handoff_cb)(&(connection->req.request),
                                             path->host->host,
                                             path->path,
                                             connection->io_bufferevent,
                                             parse_len,
                                             connection->fohh_thread_id,
                                             path->void_cookie,
                                             path->int_cookie);
                } else {
                    FOHH_LOG(AL_ERROR, "Connection: %s no handoff callback", connection->description);
                    res = FOHH_RESULT_ERR;
                }

                if (res == FOHH_RESULT_NO_ERROR) {
                    /* We handed our bufferevent to whatever we called. */
                    connection->io_bufferevent = NULL;
                    FOHH_LOG(AL_DEBUG, "Connection %s: Handoff complete", connection->description);
                } else {
                    FOHH_LOG(AL_ERROR, "Connection %s: Handoff failed", connection->description);
                }
                fohh_http_server_connection_close(connection);
            }
        } else {
            /* Something bad happened. */
            FOHH_LOG(AL_ERROR, "Connection went shoddy");
            fohh_http_server_connection_close(connection);
        }
    }
    return;
}

static void fohh_http_server_process_received_data(struct fohh_http_server_connection *connection)
{
    struct bufferevent *bev = connection->io_bufferevent;
    struct http_parser *parser = &(connection->req.parser);
    size_t rx_len;
    size_t parse_len;
    uint8_t *data;
    struct evbuffer *rx_buffer;

    /* Update timeouts... */
    connection->last_event = epoch_s();

    /* Get up to 4K contiguous rx data: */
    rx_buffer = bufferevent_get_input(bev);

    while ((connection->state == fohh_http_server_connection_active) &&
           ((rx_len = evbuffer_get_contiguous_space(rx_buffer)))) {
        FOHH_DEBUG_HTTP_PARSER("Connection %s: Processing %ld bytes of rx data", connection->description, (long) rx_len);
        data = evbuffer_pullup(rx_buffer, rx_len);
        if (!data) {
            FOHH_LOG(AL_ERROR, "Connection %s: weird", connection->description);
            fohh_http_server_connection_close(connection);
            return;
        }
        parse_len = http_parser_execute(&(connection->req.parser), &http_parser_callbacks, (const char *)data, rx_len);

        /*
         * Possible outcomes:
         *
         * 1. Parse failed. Indicated by:
         *    (parser->http_errno > HPE_CB_status)
         *
         * 2. Parse succeeded, but request handling failed. Indicated by:
         *    (HPE_CB_STATUS >= parser->http_errno > 0)
         *    AND
         *    (connection->state == fohh_http_server_connection_error)
         *
         * 3. Parse succeeded, request handling succeeded, and
         *    response was generated successfully synchronously.
         *    Indicated by:
         *    (parser->http_errno == 0)
         *
         * 4. Parse succeeded, request handling succeeded, but
         *    response will come asynchronously. Indicated by:
         *    (HPE_CB_STATUS >= parser->http_errno > 0)
         *    AND
         *    (connection->state ==
         *     fohh_http_server_connection_waiting_asynchronous_response)
         *
         * 5. Parse succeeded, request handling failed, but a valid
         *    response has been sent (probably a 500).
         *    (HPE_CB_STATUS >= parser->http_errno > 0)
         *    AND
         *    (connection->state ==
         *     fohh_http_server_connection_active)
         */
        if (parser->http_errno == 0) {
            /* Case 3 above... Do nothing! */
            /* Fall through. */
            FOHH_DEBUG_HTTP_SERVER("Connection %s: Have sent synchronous response", connection->description);
        } else if (parser->http_errno <= HPE_CB_status) {
            /* Case 2 and 4, above... */
            if (connection->state == fohh_http_server_connection_error) {
                /* Case 2. */
                FOHH_LOG(AL_WARNING, "Connection %s: Request processing error %.*s",
                                     connection->description,
                                     (fohh_debug & FOHH_DEBUG_HTTP_PARSER_BIT) ? (int) rx_len : 0,
                                     (fohh_debug & FOHH_DEBUG_HTTP_PARSER_BIT) ? data : NULL);
                fohh_http_server_connection_close(connection);
                return;
            } else if (connection->state == fohh_http_server_connection_waiting_asynchronous_response) {
                /* Case 4. */
                /* Turn off receiving, and change state. */
                fohh_http_rx_pause(connection);
                connection->state = fohh_http_server_connection_waiting_asynchronous_response;
                FOHH_DEBUG_HTTP_SERVER("Connection %s: Waiting for asynchronous response", connection->description);
                /* Fall through to consume parsed data. */
            } else {
                /* Case 5. */
                /* Fall through */
                FOHH_DEBUG_HTTP_SERVER("Connection %s: Processing failed, but have sent synchronous response", connection->description);
            }
        } else if (parser->http_errno > HPE_CB_status) {
            /* Case 1 */
            FOHH_LOG(AL_WARNING, "Connection %s: Parse error, %.*s",
                                 connection->description,
                                 (fohh_debug & FOHH_DEBUG_HTTP_PARSER_BIT) ? (int) rx_len : 0,
                                 (fohh_debug & FOHH_DEBUG_HTTP_PARSER_BIT) ? data : NULL);
            fohh_http_server_connection_close(connection);
            return;
        } else {
            /* CANNOT HAPPEN!? */
            FOHH_LOG(AL_WARNING, "Connection %s: In unusual state, %.*s",
                                 connection->description,
                                 (fohh_debug & FOHH_DEBUG_HTTP_PARSER_BIT) ? (int) rx_len : 0,
                                 (fohh_debug & FOHH_DEBUG_HTTP_PARSER_BIT) ? data : NULL);
            fohh_http_server_connection_close(connection);
            return;
        }

        /* If parse_len is 0 here, we'll likely spin-loop. We'll kill
         * the connection instead... */
        if ((parse_len == 0) || evbuffer_drain(rx_buffer, parse_len)) {
            FOHH_LOG(AL_ERROR, "Connection %s: weirder", connection->description);
            fohh_http_server_connection_close(connection);
            return;
        }
    }

    return;
}


/* Read/Parse HTTP request... */
static void fohh_http_server_read_callback(struct bufferevent *bev, void *cookie)
{
    struct fohh_http_server_connection *connection = cookie;
    if (connection->is_handoff) {
        fohh_http_server_handoff_received_data(connection);
    } else {
        fohh_http_server_process_received_data(connection);
    }
    return;
}


static void fohh_http_server_write_callback(struct bufferevent *bev, void *cookie)
{
    struct fohh_http_server_connection *connection = cookie;
    FOHH_DEBUG_HTTP_PARSER("Connection %s: Write callback", connection->description);
}


static void fohh_http_server_event_callback(struct bufferevent *bev, short style, void *cookie)
{
    struct fohh_http_server_connection *connection = cookie;

    char *b_reading = "";
    char *b_writing = "";
    char *b_eof = "";
    char *b_error = "";
    char *b_timeout = "";
    char *b_connected = "";

    connection->last_event = epoch_s();

    if (style & BEV_EVENT_READING) b_reading = " BEV_EVENT_READING";
    if (style & BEV_EVENT_WRITING) b_writing = " BEV_EVENT_WRITING";
    if (style & BEV_EVENT_EOF) b_eof = " BEV_EVENT_EOF";
    if (style & BEV_EVENT_ERROR) b_error = " BEV_EVENT_ERROR";
    if (style & BEV_EVENT_TIMEOUT) b_timeout = " BEV_EVENT_TIMEOUT";
    if (style & BEV_EVENT_CONNECTED) b_connected = " BEV_EVENT_CONNECTED";

    FOHH_DEBUG_HTTP_SERVER("Connection %s: Received event %s%s%s%s%s%s",
                           connection->description,
                           b_reading,
                           b_writing,
                           b_eof,
                           b_error,
                           b_timeout,
                           b_connected);

    if (style & (BEV_EVENT_EOF | BEV_EVENT_ERROR)) {
        /* Wipeout.... */
        fohh_http_server_connection_close(connection);
    } else {
        /* Connection initialized... Not really anything to do here. */
    }
}

/*
 * Called to start processing on a connection- i.e. after proxy
 * protocol, or immediately after accept.
 *
 * Note: We switch connection to close_on_free, here.
 */
static void fohh_http_server_connection_start(struct fohh_http_server_connection *connection)
{
    if (connection->listener->ssl_ctx) {
        connection->ssl = SSL_new(connection->listener->ssl_ctx);
        if (!connection->ssl) {
            FOHH_LOG(AL_ERROR, "Connection %s: Could not create SSL context.", connection->description);
            fohh_http_server_connection_close(connection);
            return;
        }
        connection->io_bufferevent = bufferevent_openssl_socket_new(fohh_get_thread_event_base(connection->fohh_thread_id),
                                                                    connection->sock,
                                                                    connection->ssl,
                                                                    BUFFEREVENT_SSL_ACCEPTING,
                                                                    BEV_OPT_THREADSAFE | BEV_OPT_DEFER_CALLBACKS);
    } else {
        if (is_zpn_clientd) {
            connection->io_bufferevent = bufferevent_socket_new(fohh_get_thread_event_base(connection->fohh_thread_id),
                                                                connection->sock,
                                                                BEV_OPT_CLOSE_ON_FREE | BEV_OPT_THREADSAFE | BEV_OPT_DEFER_CALLBACKS);
        } else {
            connection->io_bufferevent = bufferevent_socket_new(fohh_get_thread_event_base(connection->fohh_thread_id),
                                                                connection->sock,
                                                                BEV_OPT_THREADSAFE | BEV_OPT_DEFER_CALLBACKS);
        }
    }
    if (!connection->io_bufferevent) {
        FOHH_LOG(AL_ERROR, "Connection %s: Could not create io_bufferevent", connection->description);
        fohh_http_server_connection_close(connection);
        return;
    } else {
        bufferevent_set_dont_dump(connection->io_bufferevent);
        if (is_zpn_clientd) {
            connection->sock = -1;
        }
    }

    parse_reset(connection);

    /* Set callbacks. */
    bufferevent_setcb(connection->io_bufferevent,
                      fohh_http_server_read_callback,    /* Read Callback */
                      fohh_http_server_write_callback,   /* Write Callback */
                      fohh_http_server_event_callback,   /* Event Callback */
                      connection);                       /* Callback cookie */

    connection->state = fohh_http_server_connection_active;

    bufferevent_enable(connection->io_bufferevent, EV_READ|EV_WRITE);

}

/*
 * Example proxy protocol headers: (Note, lines should be \r\n terminated)

PROXY TCP4 *********** ************ 56324 443
GET / HTTP/1.1
Host: ************

*/


/*
 * Called to perform proxy protocol on a plain TCP socket.
 */
static void fohh_http_server_proxy_protocol(evutil_socket_t sock, int16_t flags, void *cookie)
{
    char str[200];
    char inet[ARGO_INET_ADDRSTRLEN] = {0};
    uint8_t proxy_protocol_buf[128] = {0};
    uint16_t new_port;
    size_t i;
    ssize_t rx_len;

    /* The parsed pp len... */
    size_t pp_len = 0;

    uint8_t *address = NULL;
    size_t address_len = 0;
    uint8_t *port = NULL;
    size_t port_len = 0;

    struct fohh_http_server_connection *connection = cookie;

    if (!(flags & EV_READ)) {
        /* Wait for readable data. Note we get a read event on socket
         * close */
        return;
    }

    rx_len = recv(connection->sock, proxy_protocol_buf, sizeof(proxy_protocol_buf) - 1, MSG_PEEK);
    if (rx_len <= 0) {
        /* Connection closed... */
        fohh_http_server_connection_close(connection);
        return;
    }

    proxy_protocol_buf[rx_len] = 0;

    while (1) {
        /* Proxy Protocol Parsing from nginx: */
        uint8_t *buf = proxy_protocol_buf;
        uint8_t *last = buf + rx_len;
        size_t len;
        uint8_t ch, *p, *addr;

        p = buf;
        len = last - buf;

        if (len < 8 || strncmp((const char *)p, "PROXY ", 6) != 0) {
            goto invalid;
        }

        p += 6;
        len -= 6;

        if (len >= 7 && strncmp((const char *)p, "UNKNOWN", 7) == 0) {
            p += 7;
            goto skip;
        }

        if (len < 5 ||
            strncmp((const char *)p, "TCP", 3) != 0 ||
            (p[3] != '4' && p[3] != '6') ||
            p[4] != ' ') {
            goto invalid;
        }

        p += 5;
        addr = p;

        /* Capture first address */
        for ( ;; ) {
            if (p == last) {
                goto invalid;
            }

            ch = *p;
            p++;

            if (ch == ' ') {
                break;
            }

            if (ch != ':' &&
                ch != '.' &&
                (ch < 'a' || ch > 'f') &&
                (ch < 'A' || ch > 'F') &&
                (ch < '0' || ch > '9')) {
                goto invalid;
            }
        }

        len = p - addr - 1;

        address = addr;
        address_len = len;

        /* Skip second address. */
        for ( ;; ) {
            if (p == last) {
                goto invalid;
            }

            ch = *p;
            p++;

            if (ch == ' ') {
                break;
            }

            if (ch != ':' &&
                ch != '.' &&
                (ch < 'a' || ch > 'f') &&
                (ch < 'A' || ch > 'F') &&
                (ch < '0' || ch > '9')) {
                goto invalid;
            }
        }

        /* Capture first port. */
        addr = p;

        for ( ;; ) {
            if (p == last) {
                goto invalid;
            }

            ch = *p;
            p++;

            if (ch == ' ') {
                break;
            }

            if ((ch < '0' || ch > '9')) {
                goto invalid;
            }
        }

        len = p - addr - 1;
        port = addr;
        port_len = len;

    skip:

        for ( /* void */ ; p < last - 1; p++) {
            if (p[0] == '\r' && p[1] == '\n') {
                pp_len = (p + 2) - buf;
                break;
            }
        }
        break;

    invalid:
        break;
    }
    if (!pp_len) {
        FOHH_LOG(AL_ERROR, "Connection %s: Invalid proxy protocol parse: (len = %d) %.*s", connection->description, (int) rx_len, (int) rx_len, proxy_protocol_buf);
        fohh_http_server_connection_close(connection);
        return;
    }

    if (fohh_debug & FOHH_DEBUG_HTTP_PP_BIT) {
        snprintf(str, sizeof(str), "%s", connection->description);
    }

    /* Recover remote port, pretend it is what we received. */
    new_port = 0;
    for (i = 0; i < port_len; i++) {
        new_port *= 10;
        new_port += port[i] - '0';
    }
    connection->remote_port_ne = htons(new_port);

    /* Recover remote IP, pretend it is what we received. */
    snprintf(inet, sizeof(inet), "%.*s", (int) address_len, address);
    if (argo_string_to_inet(inet, &(connection->remote_ip)) != ARGO_RESULT_NO_ERROR) {
        FOHH_LOG(AL_ERROR, "Connection %s: Could not parse proxy protocol IP address %s", connection->description, inet);
        fohh_http_server_connection_close(connection);
    }

    /* Rewrite connection description */
    fohh_http_server_connection_describe(connection);

    if (fohh_debug & FOHH_DEBUG_HTTP_PP_BIT) {
        FOHH_DEBUG_HTTP_PP("Connection %s: PROXY PROTOCOL, is now %s", str, connection->description);
    }

    /* Remove proxy protocol header... */
    rx_len = recv(connection->sock, proxy_protocol_buf, pp_len, 0);

    /* Clean up our event. */
    if (event_del(connection->proxy_protocol_event)) {
        FOHH_LOG(AL_ERROR, "Connection %s: Could not delete event", connection->description);
        fohh_http_server_connection_close(connection);
        return;
    }
    event_free(connection->proxy_protocol_event);
    connection->proxy_protocol_event = NULL;

    /* Start up the connection using normal io_bufferevents. */
    connection->state = fohh_http_server_connection_active;
    fohh_http_server_connection_start(connection);
}


/*
 * Callback within proper thread context for the thread that will process this HTTP connection.
 */
static void fohh_http_server_connection_setup(struct fohh_thread *thread, void *cookie, int64_t int_cookie)
{
    struct fohh_http_server_connection *connection = cookie;
    struct fohh_http_listener *listener = connection->listener;

#if (defined(__APPLE__) && defined(__MACH__)) || defined(__FreeBSD__)
    {
        int tmp_val;
        tmp_val = 1;
        if (setsockopt(connection->sock, SOL_SOCKET, SO_NOSIGPIPE, (void *)&tmp_val, sizeof(tmp_val)) < 0) {
            FOHH_LOG(AL_ERROR, "Connection %s: Could not set socket for no sigpipe, error = %s", connection->description, strerror(errno));
        }
    }
#endif

    LIST_INSERT_HEAD(&(listener->connections[connection->fohh_thread_id]), connection, connections);
    connection->exists_in_connections_list = 1;
    if (listener->use_proxy_protocol) {
        /* Proxy Protocol handling... */
        connection->state = fohh_http_server_connection_proxy_protocol;
        connection->proxy_protocol_event = event_new(thread->ev_base, connection->sock, EV_READ|EV_PERSIST, fohh_http_server_proxy_protocol, connection);
        if (!connection->proxy_protocol_event) {
            FOHH_LOG(AL_ERROR, "Connection %s: Could not create event for proxy protocol handler", connection->description);
            fohh_http_server_connection_close(connection);
            return;
        }
        if (event_add(connection->proxy_protocol_event, NULL)) {
            FOHH_LOG(AL_ERROR, "Connection %s: Could not add event for proxy protocol handler", connection->description);
            fohh_http_server_connection_close(connection);
            return;
        }
        /* Superfluous, but clearer: */
        return;
    } else {
        connection->state = fohh_http_server_connection_active;
        fohh_http_server_connection_start(connection);
        /* Superfluous, but clearer: */
        return;
    }
}


/*
 * Callback called whenever we receive a connection on a listening
 * socket.
 */
void fohh_http_server_accept_callback(struct evconnlistener *ev_listener,
                                      evutil_socket_t sock,
                                      struct sockaddr *addr,
                                      int len,
                                      void *cookie)
{
    struct fohh_http_server_connection *connection;
    struct fohh_http_listener *listener = cookie;
    struct argo_inet remote_ip;
    uint16_t remote_port_ne;
    char str1[ARGO_INET_ADDRSTRLEN];
    char str2[ARGO_INET_ADDRSTRLEN];
    int res;

    argo_sockaddr_to_inet(addr, &remote_ip, &remote_port_ne);

    /* The very first thing we do is determine if we will accept this
     * callback- unless we're using proxy protocol, in which case
     * we'll do it later in the state machine. */
    if (!listener->use_proxy_protocol) {
        if (listener->accept_cb) {
            res = (listener->accept_cb)(&remote_ip, &(listener->addr), listener->port_he);
            if (res && (res != FOHH_RESULT_ASYNCHRONOUS)) {
                close(sock);
                FOHH_LOG(AL_NOTICE, "Connection %s: %s:%d->%s:%d REJECTED via accept CB",
                         listener->server->name,
                         argo_inet_generate(str1, &remote_ip),
                         (int)ntohs(remote_port_ne),
                         argo_inet_generate(str2, &(listener->addr)),
                         (int)listener->port_he);
            } else if (res == FOHH_RESULT_ASYNCHRONOUS) {
                /* XXX Implement me. */
                FOHH_LOG(AL_CRITICAL, "Implement Me");
            }
        }
    }

    /* Create a connection object, and pass the connection off to a specific thread. */
    connection = http_connection_alloc();
    if (!connection) {
        FOHH_LOG(AL_NOTICE, "Connection %s: %s:%d->%s:%d Failed to allocate connection",
                 listener->server->name,
                 argo_inet_generate(str1, &remote_ip),
                 (int)ntohs(remote_port_ne),
                 argo_inet_generate(str2, &(listener->addr)),
                 (int)listener->port_he);
        close(sock);
        return;
    }
    connection->is_handoff = listener->server->is_handoff;
    if (listener->is_debug_port_listen && fohh_is_debugcmd_pool_support_enabled()) {
       connection->fohh_thread_id = fohh_worker_pool_get_thread_id(FOHH_DEBUG_CMD_WORKER_POOL);
    } else{
       connection->fohh_thread_id = fohh_next_thread_id();
    }
    connection->remote_ip = remote_ip;
    connection->remote_port_ne = remote_port_ne;
    connection->sock = sock;
    connection->listener = listener;
    connection->last_event = epoch_s();
    fohh_http_server_connection_describe(connection);

    /* Call to thread context to perform per-thread creation
     * piece. Note: log first because of otherwise race condition */
    FOHH_DEBUG_HTTP_SERVER("Connection %s: CREATED", connection->description);
    res = fohh_thread_call(connection->fohh_thread_id, fohh_http_server_connection_setup, connection, 0);
    if (res) {
        FOHH_LOG(AL_ERROR, "Connection %s: Could not perform fohh_thread_call for thread %d",
                 connection->description,
                 connection->fohh_thread_id);
        fohh_http_server_connection_close(connection);
    }
}

/*
 * Call within correct thread context for finishing initialization of listener.
 *
 * Unfortunately binds can fail here, and our exception pass-back will
 * miss it, so we log very loudly.
 */
static void fohh_http_server_listen_call(struct fohh_thread *thread, void *cookie, int64_t int_cookie)
{
    struct fohh_http_listener *listener = cookie;
    struct sockaddr_storage addr;
    socklen_t addr_len;
    char str[ARGO_INET_ADDRSTRLEN];

    argo_inet_generate(str, &(listener->addr));

    argo_inet_to_sockaddr(&(listener->addr), (struct sockaddr *)&addr, &addr_len, htons(listener->port_he));
    listener->ev_listener = evconnlistener_new_bind(fohh_get_thread_event_base(listener->fohh_thread_id),
                                                    fohh_http_server_accept_callback,
                                                    listener,
                                                    LEV_OPT_CLOSE_ON_FREE | LEV_OPT_REUSEABLE, // | LEV_OPT_THREADSAFE,
                                                    1024,
                                                    (struct sockaddr *)&addr,
                                                    addr_len);

    if (listener->ev_listener) {
        //FOHH_LOG(AL_NOTICE, "Bound http server listening on address: %s:%d", str, (int) listener->port_he);
    } else {
        FOHH_LOG(AL_CRITICAL, "Could not bind connection listener asynchronously, address: %s:%d", str, (int) listener->port_he);
    }
}

int fohh_http_server_listen(struct fohh_http_server *server,
                            struct argo_inet *listen_addr,
                            uint16_t listen_port_he,
                            fohh_http_server_accept_cb *accept_cb,
                            SSL_CTX *ssl_ctx,
                            int use_proxy_protocol,
                            int is_debug_port_listen)
{
    int res;
    struct fohh_http_listener *listener;
    int i;

    pthread_rwlock_rdlock(&(server->lock));
    pthread_mutex_lock(&fohh_http_lock);

    if (fohh_http_listeners_count >= FOHH_MAX_HTTP_LISTENERS) {
        pthread_mutex_unlock(&fohh_http_lock);
        pthread_rwlock_unlock(&(server->lock));
        FOHH_LOG(AL_ERROR, "Could not create listener; max listeners exceeded.");
        return FOHH_RESULT_ERR;
    }

    listener = &(fohh_http_listeners[fohh_http_listeners_count]);
    memset(listener, 0, sizeof(*listener));

    listener->addr = *listen_addr;
    listener->port_he = listen_port_he;
    listener->is_debug_port_listen = is_debug_port_listen;
    if (is_debug_port_listen && fohh_is_debugcmd_pool_support_enabled()) {
        listener->fohh_thread_id = fohh_worker_pool_get_thread_id(FOHH_DEBUG_CMD_WORKER_POOL);
    } else {
        listener->fohh_thread_id = fohh_next_accept_thread_id();
    }
    listener->accept_cb = accept_cb;
    listener->use_proxy_protocol = use_proxy_protocol;
    listener->ssl_ctx = ssl_ctx;
    listener->server = server;
    for (i = 0; i < FOHH_MAX_THREADS; i++) {
        LIST_INIT(&(listener->connections[i]));
    }

    LIST_INSERT_HEAD(&(server->listeners), listener, listeners);
    fohh_http_listeners_count++;

    /* thread_call to perform per-thread operations on correct FOHH thread. */
    res = fohh_thread_call(listener->fohh_thread_id, fohh_http_server_listen_call, listener, 0);
    if (res) {
        LIST_REMOVE(listener, listeners);
        fohh_http_listeners_count--;
        FOHH_LOG(AL_ERROR, "Could not spawn listener callback.");
    }

    pthread_mutex_unlock(&fohh_http_lock);
    pthread_rwlock_unlock(&(server->lock));

    return FOHH_RESULT_NO_ERROR;
}

struct fohh_http_listener* fohh_http_listener_alloc(struct fohh_http_server *server,
                                                    struct argo_inet *listen_addr,
                                                    uint16_t listen_port_he,
                                                    fohh_http_server_accept_cb *accept_cb,
                                                    SSL_CTX *ssl_ctx,
                                                    int use_proxy_protocol)
{
    struct fohh_http_listener *listener;
    int i;

    pthread_rwlock_rdlock(&(server->lock));
    pthread_mutex_lock(&fohh_http_lock);

    if (fohh_http_listeners_count >= FOHH_MAX_HTTP_LISTENERS) {
        pthread_mutex_unlock(&fohh_http_lock);
        pthread_rwlock_unlock(&(server->lock));
        FOHH_LOG(AL_ERROR, "Could not create listener; max listeners exceeded.");
        return NULL;
    }

    listener = &(fohh_http_listeners[fohh_http_listeners_count]);
    memset(listener, 0, sizeof(*listener));

    listener->addr = *listen_addr;
    listener->port_he = listen_port_he;
    listener->fohh_thread_id = fohh_next_accept_thread_id();
    listener->accept_cb = accept_cb;
    listener->use_proxy_protocol = use_proxy_protocol;
    listener->ssl_ctx = ssl_ctx;
    listener->server = server;
    for (i = 0; i < FOHH_MAX_THREADS; i++) {
        LIST_INIT(&(listener->connections[i]));
    }

    LIST_INSERT_HEAD(&(server->listeners), listener, listeners);
    fohh_http_listeners_count++;

    pthread_mutex_unlock(&fohh_http_lock);
    pthread_rwlock_unlock(&(server->lock));

    return listener;
}

static struct evbuffer *generate_response(struct fohh_http_response *response)
{
    char tmp_str[1000];
    time_t now;
    struct tm tm;
    struct evbuffer *buf = evbuffer_new();
    if (!buf) return NULL;
    evbuffer_set_dont_dump(buf);

    if ((response->status >= STATUS_DESCRIPTION_COUNT) ||
        (status_description[response->status] == NULL)) {
        evbuffer_free(buf);
        if (response->body) {
            evbuffer_free(response->body);
        }
        return NULL;
    }

    /* Generate date: */
    now = time(0);
    gmtime_r(&now, &tm);
    strftime(tmp_str, sizeof(tmp_str),
             "%a, %d %b %Y %H:%M:%S %Z", &tm);

    /* Response Line... */
    if (0 >= evbuffer_add_printf
        (buf,
         "HTTP/%d.%d %d %s\r\n"
         "Date: %s\r\n"
         "Expires: %s\r\n"
         //"Connection: close\r\n"
         "Cache-Control: max-age=0, no-cache\r\n"
         "Content-Type: %s\r\n"
         "Content-Length: %ld\r\n"
         "%s"
         "\r\n",
         (int)response->http_version_major,
         (int)response->http_version_minor,
         (int)response->status,
         status_description[response->status],
         tmp_str,
         tmp_str,
         response->content_type ? response->content_type : "text/html; charset=ISO-8859-1",
         response->body ? (long)evbuffer_get_length(response->body) : (long) 0,
         response->extra_headers ? response->extra_headers : "")) {
        evbuffer_free(buf);
        if (response->body) {
            evbuffer_free(response->body);
        }
        return NULL;
    }

    /* Append body... */
    if (response->body) {
        if (evbuffer_add_buffer(buf, response->body)) {
            evbuffer_free(buf);
            evbuffer_free(response->body);
            return NULL;
        }
        evbuffer_free(response->body);
    }

    return buf;
}

/*
 * SYNCHRONOUS version.
 */
int fohh_http_send_sync_response(struct fohh_http_server_connection *connection,
                                 struct fohh_http_response *response)
{
    struct evbuffer *buf;

    /* Generate_response always consumes any buffer within response */
    buf = generate_response(response);
    if (!buf) {
        parse_reset(connection);
        return FOHH_RESULT_ERR;
    }


    /* Queue output... */
    if (bufferevent_write_buffer(connection->io_bufferevent, buf)) {
        evbuffer_free(buf);
        parse_reset(connection);
        return FOHH_RESULT_ERR;
    }

    evbuffer_free(buf);
    parse_reset(connection);
    return FOHH_RESULT_NO_ERROR;
}

/*
 * Asynchronous callback to resume processing connection- in
 * particular, the response evbuffer might have data for us to use to
 * transmit back to client... (In which case we want to resume the
 * connection)
 */
void fohh_http_resume_asynchronous(struct fohh_thread *thread,
                                   void *cookie,
                                   int64_t connection_incarnation)
{
    struct fohh_http_server_connection *connection = cookie;

    if (connection->incarnation != connection_incarnation) {
        return;
    }

    if (connection->state == fohh_http_server_connection_waiting_asynchronous_response) {
        if (connection->req.resp_buf) {
            if (bufferevent_write_buffer(connection->io_bufferevent, connection->req.resp_buf)) {
                /* Error on xmit. */
                parse_reset(connection);
                fohh_http_server_connection_close(connection);
            } else {
                /* Successful xmit. */
                connection->state = fohh_http_server_connection_active;
                parse_reset(connection);
                fohh_http_rx_resume(connection);
            }
        } else {
            parse_reset(connection);
            fohh_http_server_connection_close(connection);
        }
    } else {
        /* ANYTHING else should toss the connection. */
        parse_reset(connection);
        fohh_http_server_connection_close(connection);
    }
}


int fohh_http_server_connection_verify_lock(struct fohh_http_server_connection *connection,
                                            int64_t connection_incarnation)
{
    pthread_mutex_lock(&(connection->lock));

    if (connection->incarnation != connection_incarnation) {
        pthread_mutex_unlock(&connection->lock);
        return FOHH_RESULT_ERR;
    }
    /* Expected behavior that connection->lock shall be unlocked by caller
        using the function fohh_http_server_connection_unlock */
    /* coverity[missing_unlock] */
    return FOHH_RESULT_NO_ERROR;
}



/*
 * ASYNCHRONOUS version.
 *
 * XXX
 * To Do: Optimize for my_fohh_thread matching connection thread.
 *
 * NOTE: connection lock is held when called, and incarnation has been
 * checked by the locking routine.
 */
int fohh_http_send_async_response_unlock(int my_fohh_thread,
                                         struct fohh_http_server_connection *connection,
                                         int64_t connection_incarnation,
                                         struct fohh_http_response *response)
{
    struct evbuffer *buf;
    int res;

    buf = generate_response(response);
    if (!buf) {
        fohh_http_cancel_async_response_unlock(my_fohh_thread,
                                               connection,
                                               connection_incarnation);
        parse_reset(connection);
        return FOHH_RESULT_ERR;
    }

    if (connection->req.resp_buf) {
        size_t len;
        if ((len = evbuffer_get_length(connection->req.resp_buf))) {
            FOHH_LOG(AL_WARNING, "Connection: %s, Unexpected %ld bytes data in buffer", connection->description, (long) len);
        }
        evbuffer_add_buffer(connection->req.resp_buf, buf);
        evbuffer_free(buf);
    } else {
        FOHH_LOG(AL_CRITICAL, "Unexpected");
    }

    res = fohh_thread_call(connection->fohh_thread_id, fohh_http_resume_asynchronous, connection, connection_incarnation);

    if (res) {
        /* Will kill connection via timeout check in a few seconds... */
        parse_reset(connection);
        connection->state = fohh_http_server_connection_error;
    }

    pthread_mutex_unlock(&(connection->lock));

    return res;
}


/*
 * ASYNCHRONOUS CANCEL
 *
 * XXX
 *
 * To Do: Optimize for my_fohh_thread matching connection thread.
 *
 * NOTE: connection lock is held when called, and incarnation has been
 * checked by the locking routine.
 */
int fohh_http_cancel_async_response_unlock(int my_fohh_thread,
                                           struct fohh_http_server_connection *connection,
                                           int64_t connection_incarnation)
{
    int res;

    connection->state = fohh_http_server_connection_error;

    res = fohh_thread_call(connection->fohh_thread_id, fohh_http_resume_asynchronous, connection, connection_incarnation);

    if (res) {
        parse_reset(connection);
        connection->state = fohh_http_server_connection_error;
    }

    pthread_mutex_unlock(&(connection->lock));

    return res;
}


static struct fohh_http_server_registered_host *
reg_host_create(struct fohh_http_server *server, const char *host, size_t host_len, int wildcarded)
{
    struct fohh_http_server_registered_host *reg_host;

    reg_host = (struct fohh_http_server_registered_host *) FOHH_CALLOC(sizeof(*reg_host));
    if (!reg_host) {
        return NULL;
    }
    reg_host->server = server;
    if (host_len) {
        reg_host->host = FOHH_STRDUP(host, host_len);
    } else {
        reg_host->host = FOHH_STRDUP("", 0);
    }
    if (!reg_host->host) {
        FOHH_FREE(reg_host);
        return NULL;
    }
    reg_host->wildcarded = wildcarded;
    reg_host->paths = diamond_create(0, NULL, 10);
    if (!reg_host->paths) {
        FOHH_FREE(reg_host->host);
        FOHH_FREE(reg_host);
        return NULL;
    }

    return reg_host;
}

static struct fohh_http_server_registered_path *
reg_path_create(struct fohh_http_server_registered_host *reg_host,
                const char *path,
                size_t path_len,
                int wildcarded,
                fohh_http_server_request_cb *callback,
                fohh_http_server_handoff_cb *handoff_callback,
                fohh_domain_sock_cmd_request_cb *command_req_callback,
                void *void_cookie,
                int64_t int_cookie)
{
    struct fohh_http_server_registered_path *reg_path;

    reg_path = (struct fohh_http_server_registered_path *) FOHH_CALLOC(sizeof(*reg_path));
    if (!reg_path) {
        return NULL;
    }
    reg_path->host = reg_host;
    if (path_len) {
        reg_path->path = FOHH_STRDUP(path, path_len);
    } else {
        reg_path->path = FOHH_STRDUP("", 0);
    }
    if (!reg_path->path) {
        FOHH_FREE(reg_path);
        return NULL;
    }
    reg_path->wildcarded = wildcarded;
    reg_path->request_cb = callback;
    reg_path->handoff_cb = handoff_callback;
    reg_path->command_req_cb = command_req_callback;
    reg_path->void_cookie = void_cookie;
    reg_path->int_cookie = int_cookie;

    return reg_path;
}

static int fohh_http_server_register_internal(struct fohh_http_server *server,
                                              const char *host,
                                              const char *path,
                                              fohh_http_server_request_cb *callback,
                                              fohh_http_server_handoff_cb *handoff_callback,
                                              void *void_cookie,
                                              int64_t int_cookie)
{
    size_t host_len;
    int    host_wildcarded = 0;
    size_t path_len;
    int    path_wildcarded = 0;

    struct fohh_http_server_registered_host *reg_host;
    struct fohh_http_server_registered_path *reg_path;

    FOHH_DEBUG_HTTP_SERVER("%s: Register Host = %s, Path = %s", server->name, host, path);

    pthread_rwlock_wrlock(&(server->lock));

    /* First, get host. */
    if (host) {
        if (host[0] == '*') {
            host_wildcarded = 1;
            host++;
        }
        host_len = strlen(host);
    } else {
        host_len = 0;
    }
    if (host_len) {
        reg_host = diamond_lookup(server->hosts,         /* Diamond */
                                  (const uint8_t *) host,/* Data    */
                                  host_len,              /* data_len */
                                  host_wildcarded,       /* wild prefix */
                                  0,                     /* wild suffix */
                                  0,                     /* agg suffix */
                                  0,                     /* is agg suffix */
                                  NULL);                 /* match any */
        if (!reg_host) {
            reg_host = reg_host_create(server, host, host_len, host_wildcarded);
            if (!reg_host) {
                pthread_rwlock_unlock(&(server->lock));
                return FOHH_RESULT_NO_MEMORY;
            }
            if (diamond_add(server->hosts,          /* Diamond */
                            (const uint8_t *) host, /* Data    */
                            host_len,               /* data_len */
                            host_wildcarded,        /* wild prefix */
                            0,                      /* wild suffix */
                            0,                      /* agg suffix */
                            0,                      /* is agg suffix */
                            1,                      /* copy data */
                            reg_host)) {            /* object */
                FOHH_LOG(AL_CRITICAL, "Memory Leak");
                pthread_rwlock_unlock(&(server->lock));
                return FOHH_RESULT_NO_MEMORY;
            }
        }
    } else {
        reg_host = server->default_host;
        if (!reg_host) {
            reg_host = reg_host_create(server, host, host_len, host_wildcarded);
            if (!reg_host) {
                pthread_rwlock_unlock(&(server->lock));
                return FOHH_RESULT_NO_MEMORY;
            }
            server->default_host = reg_host;
        }
    }


    /* Then, do path */
    if (path) {
        path_len = strlen(path);
        if (path[path_len - 1] == '*') {
            path_len--;
        }
    } else {
        path_len = 0;
    }

    if (path_len) {
        reg_path = diamond_lookup(reg_host->paths,        /* Diamond */
                                  (const uint8_t *) path, /* Data    */
                                  path_len,               /* data_len */
                                  0,                      /* wild prefix */
                                  path_wildcarded,        /* wild suffix */
                                  0,                      /* agg suffix */
                                  0,                      /* is agg suffix */
                                  NULL);                  /* match any */
        if (!reg_path) {
            reg_path = reg_path_create(reg_host, path, path_len, path_wildcarded,
                                       callback, handoff_callback, NULL, void_cookie, int_cookie);
            if (!reg_path) {
                FOHH_LOG(AL_ERROR, "Server %s: Could not create path \"%s\" already exists", server->name, path);
                pthread_rwlock_unlock(&(server->lock));
                return FOHH_RESULT_NO_MEMORY;
            }

            if (diamond_add(reg_host->paths,        /* Diamond */
                            (const uint8_t *) path, /* Data    */
                            path_len,               /* data_len */
                            0,                      /* wild prefix */
                            path_wildcarded,        /* wild suffix */
                            0,                      /* agg suffix */
                            0,                      /* is agg suffix */
                            1,                      /* copy data */
                            reg_path)) {            /* object */
                FOHH_LOG(AL_CRITICAL, "Memory Leak");
                pthread_rwlock_unlock(&(server->lock));
                return FOHH_RESULT_NO_MEMORY;
            } else {
                FOHH_DEBUG_HTTP_SERVER("Server %s: Registered path \"%s\"", server->name, path);
            }
        } else {
            FOHH_LOG(AL_ERROR, "Server %s: Path \"%s\" already exists", server->name, path);
        }
    } else {
        reg_path = reg_host->default_path;
        if (!reg_path) {
            reg_path = reg_path_create(reg_host, path, path_len, path_wildcarded,
                                       callback, handoff_callback, NULL, void_cookie, int_cookie);
            if (!reg_path) {
                pthread_rwlock_unlock(&(server->lock));
                return FOHH_RESULT_NO_MEMORY;
            }
            reg_host->default_path = reg_path;
        }
    }

    pthread_rwlock_unlock(&(server->lock));
    return FOHH_RESULT_NO_ERROR;
}

int fohh_http_server_update_path_command_req_callback(struct fohh_http_server *server,
                                                      const char *host,
                                                      const char *path,
                                                      fohh_domain_sock_cmd_request_cb *command_req_cb)
{
    size_t host_len;
    int    host_wildcarded = 0;
    size_t path_len;
    int    path_wildcarded = 0;

    struct fohh_http_server_registered_host *reg_host;
    struct fohh_http_server_registered_path *reg_path;

    FOHH_DEBUG_HTTP_SERVER("command-listener: %s: Register Path = %s", server->name, path);

    pthread_rwlock_wrlock(&(server->lock));

    /* NOTE: this routine only serves to update the command_req_cb ; we do not want to create anything here.
     *  the creates would have already got done during the prior call to http_server register.
     */

    /* First, get host. */
    if (host) {
        if (host[0] == '*') {
            host_wildcarded = 1;
            host++;
        }
        host_len = strlen(host);
    } else {
        host_len = 0;
    }

    if (host_len) {
        reg_host = diamond_lookup(server->hosts,         /* Diamond */
                                  (const uint8_t *) host,/* Data    */
                                  host_len,              /* data_len */
                                  host_wildcarded,       /* wild prefix */
                                  0,                     /* wild suffix */
                                  0,                     /* agg suffix */
                                  0,                     /* is agg suffix */
                                  NULL);                 /* match any */
        if (!reg_host) {
            FOHH_LOG(AL_ERROR, "command-listener: %s: Lookup failed to get registered host..path: %s", server->name, path);
            pthread_rwlock_unlock(&(server->lock));
            return FOHH_RESULT_NOT_FOUND;
        }
    } else {
        reg_host = server->default_host;
        if (!reg_host) {
            FOHH_LOG(AL_ERROR, "command-listener: %s: Lookup failed to get registered default_host..path: %s", server->name, path);
            pthread_rwlock_unlock(&(server->lock));
            return FOHH_RESULT_NOT_FOUND;
        }
    }

    /* Now we have a valid reg_host, Do path */
    if (path) {
        path_len = strlen(path);
        if (path[path_len - 1] == '*') {
            path_len--;
        }
    } else {
        path_len = 0;
    }

    if (path_len) {
        reg_path = diamond_lookup(reg_host->paths,        /* Diamond */
                                  (const uint8_t *) path, /* Data    */
                                  path_len,               /* data_len */
                                  0,                      /* wild prefix */
                                  path_wildcarded,        /* wild suffix */
                                  0,                      /* agg suffix */
                                  0,                      /* is agg suffix */
                                  NULL);                  /* match any */
        if (!reg_path) {
            FOHH_LOG(AL_ERROR, "command-listener: %s: Lookup failed to get registered path for path: %s", server->name, path);
            pthread_rwlock_unlock(&(server->lock));
            return FOHH_RESULT_NOT_FOUND;
        }
    } else {
        reg_path = reg_host->default_path;
        if (!reg_path) {
            FOHH_LOG(AL_ERROR, "command-listener: %s: Lookup failed to get default_path for path: %s", server->name, path);
            pthread_rwlock_unlock(&(server->lock));
            return FOHH_RESULT_NOT_FOUND;
        }
    }

    /* at this point, we have a valid reg_path, update the callback */
    reg_path->command_req_cb = command_req_cb;

    pthread_rwlock_unlock(&(server->lock));
    return FOHH_RESULT_NO_ERROR;
}

int fohh_http_server_register(struct fohh_http_server *server,
                                      const char *host,
                                      const char *path,
                                      fohh_http_server_request_cb *callback,
                                      void *void_cookie,
                                      int64_t int_cookie)
{
    return fohh_http_server_register_internal(server,
                                              host,
                                              path,
                                              callback,
                                              NULL,
                                              void_cookie,
                                              int_cookie);
}

int fohh_http_server_register_handoff(struct fohh_http_server *server,
                                      const char *host,
                                      const char *path,
                                      fohh_http_server_handoff_cb *callback,
                                      void *void_cookie,
                                      int64_t int_cookie)
{
    return fohh_http_server_register_internal(server,
                                              host,
                                              path,
                                              NULL,
                                              callback,
                                              void_cookie,
                                              int_cookie);
}

struct fohh_http_server_paths {
    char **paths;
    size_t path_max_len;
    size_t *path_count;
    size_t ix;
};

static int fohh_http_server_paths_iter_cb(void* cookie,
                                          int wildcard_prefix,
                                          const char* phrase,
                                          int phrase_len,
                                          int is_pattern)
{
    struct fohh_http_server_paths *ret_path = cookie;

    if (ret_path->paths != NULL) {
        if (ret_path->ix < *ret_path->path_count) {
            snprintf(ret_path->paths[ret_path->ix++], ret_path->path_max_len, "%s%*.*s",
                    wildcard_prefix > 0 ? "*." : "", phrase_len, phrase_len, phrase);
        }
    } else {
        if (ret_path->path_count) {
            *ret_path->path_count += 1;
        }
    }
    return FOHH_RESULT_NO_ERROR;
}

int fohh_http_server_get_paths(struct fohh_http_server *server,
                               const char *host,
                               char **path,
                               size_t path_max_len,
                               size_t *path_count)
{
    size_t host_len;
    int    host_wildcarded = 0;
    int    res;
    struct fohh_http_server_paths ret_paths;

    struct fohh_http_server_registered_host *reg_host;

    pthread_rwlock_rdlock(&(server->lock));

    /* First, get host. */
    if (host) {
        if (host[0] == '*') {
            host_wildcarded = 1;
            host++;
        }
        host_len = strlen(host);
    } else {
        host_len = 0;
    }

    if (host_len) {
        reg_host = diamond_lookup(server->hosts,         /* Diamond */
                                  (const uint8_t *) host,/* Data    */
                                  host_len,              /* data_len */
                                  host_wildcarded,       /* wild prefix */
                                  0,                     /* wild suffix */
                                  0,                     /* agg suffix */
                                  0,                     /* is agg suffix */
                                  NULL);                 /* match any */
    } else {
        reg_host = server->default_host;
    }

    if (!reg_host) {
        pthread_rwlock_unlock(&(server->lock));
        if (path && path_count) {
            *path_count = 0;
        }
        return FOHH_RESULT_NOT_FOUND;
    }

    ret_paths.paths = path;
    ret_paths.path_count = path_count;
    ret_paths.path_max_len = path_max_len;
    ret_paths.ix = 0;

    res = diamond_walk_with_pattern(reg_host->paths,
                                    fohh_http_server_paths_iter_cb, &ret_paths, 0);
    if (FOHH_RESULT_NO_ERROR != res) {
        FOHH_LOG(AL_ERROR, "Unable to get any paths from server %s with host:%s", server->name, host);
        if (path && path_count) {
            *path_count = 0;
        }
        pthread_rwlock_unlock(&(server->lock));
        return FOHH_RESULT_NOT_FOUND;
    }

    pthread_rwlock_unlock(&(server->lock));
    return FOHH_RESULT_NO_ERROR;
}

int fohh_http_server_is_registered(struct fohh_http_server *server,
                                   const char *host,
                                   const char *path,
                                   void **void_cookie,
                                   int64_t *int_cookie)
{
    size_t host_len;
    int    host_wildcarded = 0;
    size_t path_len;
    int    path_wildcarded = 0;

    struct fohh_http_server_registered_host *reg_host;
    struct fohh_http_server_registered_path *reg_path;

    pthread_rwlock_rdlock(&(server->lock));

    /* First, get host. */
    if (host) {
        if (host[0] == '*') {
            host_wildcarded = 1;
            host++;
        }
        host_len = strlen(host);
    } else {
        host_len = 0;
    }
    if (host_len) {
        reg_host = diamond_lookup(server->hosts,         /* Diamond */
                                  (const uint8_t *) host,/* Data    */
                                  host_len,              /* data_len */
                                  host_wildcarded,       /* wild prefix */
                                  0,                     /* wild suffix */
                                  0,                     /* agg suffix */
                                  0,                     /* is agg suffix */
                                  NULL);                 /* match any */
    } else {
        reg_host = server->default_host;
    }

    if (!reg_host) {
        pthread_rwlock_unlock(&(server->lock));
        return FOHH_RESULT_NOT_FOUND;
    }

    /* Then, do path */
    if (path) {
        path_len = strlen(path);
        if (path[path_len - 1] == '*') {
            path_len--;
        }
    } else {
        path_len = 0;
    }

    if (path_len) {
        reg_path = diamond_lookup(reg_host->paths,        /* Diamond */
                                  (const uint8_t *) path, /* Data    */
                                  path_len,               /* data_len */
                                  0,                      /* wild prefix */
                                  path_wildcarded,        /* wild suffix */
                                  0,                      /* agg suffix */
                                  0,                      /* is agg suffix */
                                  NULL);                  /* match any */
    } else {
        reg_path = reg_host->default_path;
    }

    if (!reg_path) {
        pthread_rwlock_unlock(&(server->lock));
        return FOHH_RESULT_NOT_FOUND;
    }

    if (void_cookie) {
        (*void_cookie) = reg_path->void_cookie;
    }
    if (int_cookie) {
        (*int_cookie) = reg_path->int_cookie;
    }

    pthread_rwlock_unlock(&(server->lock));
    return FOHH_RESULT_NO_ERROR;
}

int fohh_http_server_deregister(struct fohh_http_server *server,
                                const char *host,
                                const char *path)
{
    size_t host_len;
    int    host_wildcarded = 0;
    size_t path_len;
    int    path_wildcarded = 0;

    struct fohh_http_server_registered_host *reg_host;
    struct fohh_http_server_registered_path *reg_path;

    pthread_rwlock_wrlock(&(server->lock));

    /* First, get host. */
    if (host) {
        if (host[0] == '*') {
            host_wildcarded = 1;
            host++;
        }
        host_len = strlen(host);
    } else {
        host_len = 0;
    }
    if (host_len) {
        reg_host = diamond_lookup(server->hosts,         /* Diamond */
                                  (const uint8_t *) host,/* Data    */
                                  host_len,              /* data_len */
                                  host_wildcarded,       /* wild prefix */
                                  0,                     /* wild suffix */
                                  0,                     /* agg suffix */
                                  0,                     /* is agg suffix */
                                  NULL);                 /* match any */
    } else {
        reg_host = server->default_host;
    }

    if (!reg_host) {
        pthread_rwlock_unlock(&(server->lock));
        return FOHH_RESULT_NOT_FOUND;
    }

    /* Then, do path */
    if (path) {
        path_len = strlen(path);
        if (path[path_len - 1] == '*') {
            path_len--;
        }
    } else {
        path_len = 0;
    }

    if (path_len) {
        reg_path = diamond_lookup(reg_host->paths,        /* Diamond */
                                  (const uint8_t *) path, /* Data    */
                                  path_len,               /* data_len */
                                  0,                      /* wild prefix */
                                  path_wildcarded,        /* wild suffix */
                                  0,                      /* agg suffix */
                                  0,                      /* is agg suffix */
                                  NULL);                  /* match any */
    } else {
        reg_path = reg_host->default_path;
    }

    if (!reg_path) {
        pthread_rwlock_unlock(&(server->lock));
        return FOHH_RESULT_NOT_FOUND;
    } else {
        int result;
        result = diamond_remove(reg_host->paths,
                                (const uint8_t *)path,
                                path_len,
                                0,
                                path_wildcarded,
                                0,
                                0,
                                NULL);
        if (result) {
            FOHH_LOG(AL_ERROR, "Could not remove domain: %s from diamond table", path);
            pthread_rwlock_unlock(&(server->lock));
            return result;
        }
    }
    pthread_rwlock_unlock(&(server->lock));
    return FOHH_RESULT_NO_ERROR;
}

void fohh_http_server_connection_unlock(struct fohh_http_server_connection *connection)
{
    pthread_mutex_unlock(&(connection->lock));
}

void fohh_http_server_connection_get_remote_ip(struct fohh_http_server_connection *connection, struct argo_inet *remote_ip)
{
    *remote_ip = connection->remote_ip;
}

static const unsigned char smhex2num_table[256] = {
    99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, /* 00-0F */
    99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, /* 10-1F */
    99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, /* 20-2F */
    0,  1,  2,  3,  4,  5,  6,  7,  8,  9,  99, 99, 99, 99, 99, 99, /* 30-3F 0 ... 9 */
    99, 10, 11, 12, 13, 14, 15, 99, 99, 99, 99, 99, 99, 99, 99, 99, /* 40-4F A ... F */
    99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, /* 50-5F */
    99, 10, 11, 12, 13, 14, 15, 99, 99, 99, 99, 99, 99, 99, 99, 99, /* 60-6F a.. f */
    99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, /* 70-7F */
    99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, /* 80-FF */
    99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, /* */
    99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, /* */
    99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, /* */
    99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, /* */
    99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, /* */
    99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, /* */
    99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, /* */
};

static const unsigned char url_encode_table[256][2] = {
	"00", "01", "02", "03", "04", "05", "06", "07", "08", "09", "0a", "0b", "0c", "0d", "0e", "0f", /* 00-0F \0 \t \n \r */
	"10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "1a", "1b", "1c", "1d", "1e", "1f", /* 10-1F */
	"20", "21", "22", "23", "24", "25", "26", "27", "28", "29", "2a", "2b", "2c", "2d", "2e", "2f", /* 20-2F  !"#$%&'()*+,-./ */
	"\0", "\0", "\0", "\0", "\0", "\0", "\0", "\0", "\0", "\0", "3a", "3b", "3c", "3d", "3e", "3f", /* 30-3F		   :;<=>? */
	"40", "\0", "\0", "\0", "\0", "\0", "\0", "\0", "\0", "\0", "\0", "\0", "\0", "\0", "\0", "\0", /* 40-4F @				  */
	"\0", "\0", "\0", "\0", "\0", "\0", "\0", "\0", "\0", "\0", "\0", "5b", "5c", "5d", "5e", "5f", /* 50-5F			[\]^_ */
	"60", "\0", "\0", "\0", "\0", "\0", "\0", "\0", "\0", "\0", "\0", "\0", "\0", "\0", "\0", "\0", /* 60-6F `				  */
	"\0", "\0", "\0", "\0", "\0", "\0", "\0", "\0", "\0", "\0", "\0", "7b", "7c", "7d", "7e", "7f", /* 70-7f			{|}~  */
	"80", "81", "82", "83", "84", "85", "86", "87", "88", "89", "8A", "8B", "8C", "8D", "8E", "8F", /* */
	"90", "91", "92", "93", "94", "95", "96", "97", "98", "99", "9A", "9B", "9C", "9D", "9E", "9F", /* */
	"A0", "A1", "A2", "A3", "A4", "A5", "A6", "A7", "A8", "A9", "AA", "AB", "AC", "AD", "AE", "AF", /* */
	"B0", "B1", "B2", "B3", "B4", "B5", "B6", "B7", "B8", "B9", "BA", "BB", "BC", "BD", "BE", "BF", /* */
	"C0", "C1", "C2", "C3", "C4", "C5", "C6", "C7", "C8", "C9", "CA", "CB", "CC", "CD", "CE", "CF", /* */
	"D0", "D1", "D2", "D3", "D4", "D5", "D6", "D7", "D8", "D9", "DA", "DB", "DC", "DD", "DE", "DF", /* */
	"E0", "E1", "E2", "E3", "E4", "E5", "E6", "E7", "E8", "E9", "EA", "EB", "EC", "ED", "EE", "EF", /* */
	"F0", "F1", "F2", "F3", "F4", "F5", "F6", "F7", "F8", "F9", "FA", "FB", "FC", "FD", "FE", "FF", /* */
};

int fohh_http_normalize_str(const char *src_str,
                            size_t src_str_len,
                            char *dst_str,
                            size_t dst_str_len)
{
    const unsigned char *s_s = (const unsigned char *)src_str;
    const unsigned char *s_e = s_s + src_str_len;
    unsigned char *d_s = (unsigned char *) dst_str;
    unsigned char *d_e = d_s + dst_str_len - 1;

    unsigned char high, low;

    while ((s_s < s_e) && (d_s < d_e)) {
        if (((*s_s) == '%') && ((s_e - s_s) >= 3)) {
            high = smhex2num_table[s_s[1]];
            low = smhex2num_table[s_s[2]];
            if ((high < 16) && (low < 16)) {
                (*d_s) = (high << 4) | low;
                d_s++;
                s_s += 3;
                continue;
            }
        }
        *d_s = *s_s;
        d_s++;
        s_s++;
    }
    *d_s = 0;
    if (s_s < s_e) {
        return FOHH_RESULT_ERR_TOO_LARGE;
    }
    return FOHH_RESULT_NO_ERROR;
}

int fohh_http_denormalize_str(const char *src_str,
                              size_t src_str_len,
                              char *dst_str,
                              size_t dst_str_len)
{
    const unsigned char *s_s = (const unsigned char *)src_str;
    const unsigned char *s_e = s_s + src_str_len;
    unsigned char *d_s = (unsigned char *) dst_str;
    unsigned char *d_e = d_s + dst_str_len - 1;

    while ((s_s < s_e) && (d_s < d_e)) {
        if (url_encode_table[*s_s][0]) {
            if ((d_e - d_s) < 3) {
                *dst_str = 0;
                return FOHH_RESULT_ERR_TOO_LARGE;
            }
            d_s[0] = '%';
            d_s[1] = url_encode_table[*s_s][0];
            d_s[2] = url_encode_table[*s_s][1];
            d_s += 3;
            s_s ++;
        } else {
            *d_s = *s_s;
            d_s++;
            s_s++;
        }
    }
    *d_s = 0;
    if (s_s < s_e) {
        return FOHH_RESULT_ERR_TOO_LARGE;
    }
    return FOHH_RESULT_NO_ERROR;
}

/*
 * Invalidate a client structure, but don't free it.
 */
static void fohh_http_client_disable(struct fohh_http_client *client, int is_client_freed_after_this_call)
{
    if (client) {
        struct fohh_http_client_request *request;

        pthread_mutex_lock(&(client->lock));


        /* Clean out all the callbacks, because something is unhappy. */
        while ((request = TAILQ_FIRST(&(client->client_requests_queued)))) {
            TAILQ_REMOVE(&(client->client_requests_queued), request, client_requests);
            request->in_requests_queued = 0;
            if (request->cb) {
                (request->cb)(client,
                              fohh_http_client_request_status_failure,
                              0,
                              NULL,
                              request->void_cookie,
                              request->int_cookie);
                request->cb = NULL;
            }
            if (request->timeout) {
                event_free(request->timeout);
                request->timeout = NULL;
            }
            if (request->req_buf) {
                evbuffer_free(request->req_buf);
                request->req_buf = NULL;
            }

            if (is_client_freed_after_this_call) {
                request->client = NULL;
                FOHH_FREE(request);
            }
        }

        /* Clean out all the callbacks, because something is unhappy. */
        while ((request = TAILQ_FIRST(&(client->client_requests_outstanding)))) {
            TAILQ_REMOVE(&(client->client_requests_outstanding), request, client_requests);
            request->in_requests_outstanding = 0;
            if (request->cb) {
                (request->cb)(client,
                              fohh_http_client_request_status_failure,
                              0,
                              NULL,
                              request->void_cookie,
                              request->int_cookie);
                request->cb = NULL;
            }
            if (request->timeout) {
                event_free(request->timeout);
                request->timeout = NULL;
            }
            if (request->req_buf) {
                evbuffer_free(request->req_buf);
                request->req_buf = NULL;
            }

            if (is_client_freed_after_this_call) {
                request->client = NULL;
                FOHH_FREE(request);
            }
        }

        if (client->zcdns_result) {
            zcdns_result_release(client->zcdns_result);
            client->zcdns_result = NULL;
        }
        if (client->proxy_host) {
            FOHH_FREE(client->proxy_host);
            client->proxy_host = NULL;
        }
        if (client->remote_host) {
            FOHH_FREE(client->remote_host);
            client->remote_host = NULL;
        }
        if (client->hostname) {
            FOHH_FREE(client->hostname);
            client->hostname = NULL;
        }
        if (client->ssl) {
            SSL_free(client->ssl);
            client->ssl = NULL;
        }
        if (client->io_bufferevent) {
            zlibevent_bufferevent_free(client->io_bufferevent);
            client->io_bufferevent = NULL;
        }
        if (client->timeout_event) {
            event_free(client->timeout_event);
            client->timeout_event = NULL;
        }
        if (client->proxy_event) {
            event_free(client->proxy_event);
            client->proxy_event = NULL;
        }
        if (client->req.resp_buf) {
            evbuffer_free(client->req.resp_buf);
            client->req.resp_buf = NULL;
        }
        if (client->status == fohh_http_client_status_success) {
            //fprintf(stderr, "Fail 1\n");
            client->status = fohh_http_client_status_connect_fail;
        }
        if (client->sock >= 0) {
            close(client->sock);
            client->sock = -1;
        }
        pthread_mutex_unlock(&(client->lock));
    }
}


/*
 * on the right fohh thread, now delete the object. Don't call it directly from fohh_thread_x. See
 * fohh_http_client_destroy_from_another_thread() for the reason.
 */
static void fohh_http_client_destroy(struct fohh_thread*    thread,
                                     void*                  cookie,
                                     int64_t                connection_incarnation)
{
    struct fohh_http_client*    client;

    client = (struct fohh_http_client *)cookie;
    fohh_http_client_disable(client, 1);
    FOHH_FREE_SLOW(client);
}

/*
 * Destroy request from another thread. Will not touch client structure from another thread!
 * Send a message to the corresponding fohh thread and manipulate it there.
 *
 * Q: How are we sure that by the time the message is delivered to the fohh thread, the client object is valid?
 * A: the client object destroy is never triggered from the fohh thread. Its guranteed if we follow this rule.
 */
void fohh_http_client_destroy_from_another_thread(struct fohh_http_client *client)
{
    int res;
    int desired = 1;
    int expected = 0;

    if( NULL == client ) {
        FOHH_LOG(AL_ERROR, "Couldn't delete http client, NULL");
        return;
    }

    if (!__atomic_compare_exchange_n(&client->scheduled_for_deletion, &expected, desired, 0, __ATOMIC_RELAXED,
                                     __ATOMIC_RELAXED)) {
        FOHH_LOG(AL_ERROR, "already scheduled delete http client");
        return;
    }

    res = fohh_thread_call(client->fohh_thread_id, fohh_http_client_destroy, client, 0);
    assert(!res);
    if (res) {
        FOHH_LOG(AL_ERROR, "Couldn't notify fohh(%d) to delete http client(%s)", client->fohh_thread_id,
                 client->hostname);
    }
}


static void fohh_http_client_timeout(evutil_socket_t sock, int16_t flags, void *cookie)
{
    struct fohh_http_client *client = cookie;
    //fprintf(stderr, "Client Timeout\n");

    switch(client->status) {
    case fohh_http_client_status_resolve_v4:
    case fohh_http_client_status_resolve_v6:
        client->status = fohh_http_client_status_dns_timeout;
        break;
    case fohh_http_client_status_connect_v4:
    case fohh_http_client_status_connect_v6:
        client->status = fohh_http_client_status_connect_timeout;
        break;
    default:
        client->status = fohh_http_client_status_timeout;
        break;
    }

    if (client->create_callback) {
        (client->create_callback)(client,
                                  client->status,
                                  client->create_callback_void,
                                  client->create_callback_int);
    }
    fohh_http_client_disable(client, 0);
}

int fohh_http_client_parser_cb_begin(http_parser *parser)
{
    struct fohh_http_client *client = parser->data;

    client->req.header_field[0] = 0;
    client->req.header_value[0] = 0;

    client->respond_with_headers_in_progress = 1;

    return 0;
}

int fohh_http_client_parser_cb_status(http_parser *parser, const char *at, size_t length)
{
    //fprintf(stderr, "Status = %.*s\n", (int) length, at);
    return 0;
}

int fohh_http_client_parser_cb_header_field(http_parser *parser, const char *at, size_t length)
{
    struct fohh_http_client *client = parser->data;
    size_t len;

    if (client->req.header_value[0]) {
        fohh_http_client_parser_cb_header_complete(parser);
        client->req.header_value[0] = 0;
        client->req.header_field[0] = 0;
    }

    len = strlen(client->req.header_field);

    if (length >= sizeof(client->req.header_field) - len) return 0;
    memcpy(&(client->req.header_field[len]), at, length);
    len += length;
    client->req.header_field[len] = 0;

    return 0;
}

int fohh_http_client_parser_cb_header_value(http_parser *parser, const char *at, size_t length)
{
    struct fohh_http_client *client = parser->data;
    size_t len = strlen(client->req.header_value);

    if (length >= sizeof(client->req.header_value) - len) return 0;
    memcpy(&(client->req.header_value[len]), at, length);
    len += length;

    client->req.header_value[len] = 0;

    if (client->respond_with_headers && client->req.header_field[0] && client->req.header_value[0]) {
        if (strcmp(client->req.header_field, "Transfer-Encoding") != 0) {
            evbuffer_add_printf(client->req.resp_buf, "%s: %s\r\n", client->req.header_field, client->req.header_value);
        } else {
            // We transform chunked transfer to full response message
            client->respond_with_headers_chunked_encoding = 1;
        }
    }

    return 0;
}

int fohh_http_client_parser_cb_header_complete(http_parser *parser)
{
    struct fohh_http_client *client = parser->data;
    char *w, *e;
    int i;

    //fprintf(stderr, "Header complete call: %s - %s\n", client->req.header_field, client->req.header_value);

    if (strcmp(client->req.header_field, "Set-Cookie") == 0) {
        char *equal = NULL;
        //fprintf(stderr, "Got a set-cookie: %s\n", client->req.header_value);
        w = client->req.header_value;
        e = w + sizeof(client->req.header_value);
        while ((*w) && ((*w) != ';') && (w < e)) {
            if (((*w) == '=') && !equal) equal = w;
            w++;
        }
        if ((w < e) && ((*w) == ';')) {
            *w = 0;
        } else {
            return 0;
        }

        if (!equal) return 0;

        if (client->req.cookie_count >= MAX_CLIENT_COOKIES) return 0;

        client->req.cookie_name[client->req.cookie_count] = zmicro_heap_str(&(client->req.heap), client->req.header_value, equal - client->req.header_value);

        /* Check if cookie is duplicate. */
        for (i = 0; i < client->req.cookie_count; i++) {
            if (strcmp(client->req.cookie_name[client->req.cookie_count], client->req.cookie_name[i]) == 0) {
                /* Replace. */
                w = client->req.cookie_value[i]; /* Save, in case micro heap fails */
                client->req.cookie_value[i] = zmicro_heap_str(&(client->req.heap), equal + 1, w - (equal + 1));
                if (!client->req.cookie_value[i]) client->req.cookie_value[i] = w;
                return 0;
            }
        }
        client->req.cookie_value[client->req.cookie_count] = zmicro_heap_str(&(client->req.heap), equal + 1, w - (equal + 1));

        if (!client->req.cookie_name[client->req.cookie_count] ||
            !client->req.cookie_value[client->req.cookie_count]) {
            return 0;
        }
        //fprintf(stderr, "Got cookie <%s> = <%s>\n", client->req.cookie_name[client->req.cookie_count], client->req.cookie_value[client->req.cookie_count]);
        client->req.cookie_count++;
    }

    return 0;
}

int fohh_http_client_parser_cb_body(http_parser *parser, const char *at, size_t length)
{
    struct fohh_http_client *client = parser->data;

    if (client->req.resp_buf) {

        if (client->respond_with_headers && client->respond_with_headers_in_progress) {
            evbuffer_add_printf(client->req.resp_buf, "\r\n");
            client->respond_with_headers_len = evbuffer_get_length(client->req.resp_buf);
        }
        client->respond_with_headers_in_progress = 0;

        evbuffer_add(client->req.resp_buf, at, length);
        return 0;
    } else {
        return 1;
    }
}

int fohh_http_client_parser_cb_complete(http_parser *parser)
{
    struct fohh_http_client *client = parser->data;
    struct fohh_http_client_request *request;
    int ret = 0;

    if (!client) {
        FOHH_LOG(AL_ERROR, "fohh_http_client_parser_cb_complete client is gone");
        return ret;
    }

    if (client->respond_with_headers && client->respond_with_headers_in_progress) {
        evbuffer_add_printf(client->req.resp_buf, "\r\n");
        client->respond_with_headers_len = evbuffer_get_length(client->req.resp_buf);
    }
    client->respond_with_headers_in_progress = 0;

    pthread_mutex_lock(&(client->lock));

    request = TAILQ_FIRST(&(client->client_requests_outstanding));
    if (request) {
        TAILQ_REMOVE(&(client->client_requests_outstanding), request, client_requests);
        request->in_requests_outstanding = 0;
        if (request->cb) {
            (request->cb)(client,
                          fohh_http_client_request_status_success,
                          parser->status_code,
                          client->req.resp_buf,
                          request->void_cookie,
                          request->int_cookie);
            request->cb = NULL;
        }
        evbuffer_drain(client->req.resp_buf, evbuffer_get_length(client->req.resp_buf));
        if (request->timeout) {
            event_free(request->timeout);
            request->timeout = NULL;
        }
        if (request->req_buf) {
            evbuffer_free(request->req_buf);
            request->req_buf = NULL;
        }

        /*we are completely done with this request*/
        FOHH_FREE(request);
    } else {
        ret = 1;
    }

    pthread_mutex_unlock(&(client->lock));
    return ret;
}


static void fohh_http_client_read_callback(struct bufferevent *bev, void *cookie)
{
    struct fohh_http_client *client = cookie;
    struct evbuffer *buf = bufferevent_get_input(bev);
    struct fohh_http_client_request *request;

    size_t len;
    size_t processed;

    /* Refresh timeout */
    request = TAILQ_FIRST(&(client->client_requests_outstanding));
    if (request && request->timeout) {
        int64_t now = epoch_us();
        int64_t diff = request->timeout_us;
        struct timeval tv;

        event_del(request->timeout);

        request->timeout_epoch_us = now + diff;

        tv.tv_sec = diff / 1000000;
        tv.tv_usec = diff % 1000000;

        event_add(request->timeout, &tv);
    }

    while ((len = evbuffer_get_contiguous_space(buf))) {
        uint8_t *where;
        where = evbuffer_pullup(buf, len);

        processed = http_parser_execute(&(client->req.parser),
                                        &fohh_http_client_parser_settings,
                                        (const char *)where,
                                        len);
        if (processed != len) {
            /* Error... */
            break;
        }
        evbuffer_drain(buf, processed);
    }
    if (len) {
        fohh_http_client_disable(client, 0);
    }
}

static void fohh_http_client_event_callback(struct bufferevent *bev, short style, void *cookie)
{
    struct fohh_http_client *client = cookie;

    char *b_reading = "";
    char *b_writing = "";
    char *b_eof = "";
    char *b_error = "";
    char *b_timeout = "";
    char *b_connected = "";

    if (style & BEV_EVENT_READING) b_reading = " BEV_EVENT_READING";
    if (style & BEV_EVENT_WRITING) b_writing = " BEV_EVENT_WRITING";
    if (style & BEV_EVENT_EOF) b_eof = " BEV_EVENT_EOF";
    if (style & BEV_EVENT_ERROR) b_error = " BEV_EVENT_ERROR";
    if (style & BEV_EVENT_TIMEOUT) b_timeout = " BEV_EVENT_TIMEOUT";
    if (style & BEV_EVENT_CONNECTED) b_connected = " BEV_EVENT_CONNECTED";

    FOHH_NOTICE_QUIET("FOHH Client Received event %s%s%s%s%s%s, sock=%d",
                      b_reading,
                      b_writing,
                      b_eof,
                      b_error,
                      b_timeout,
                      b_connected,
                      client->sock);

    if (style & BEV_EVENT_ERROR) {
        FOHH_NOTICE_QUIET("Error event received with error:%s",
                            evutil_socket_error_to_string(EVUTIL_SOCKET_ERROR()));
    }


    /* No timeouts allowed at this point */
    if (client->timeout_event) {
        event_free(client->timeout_event);
        client->timeout_event = NULL;
    }

    if (style & (BEV_EVENT_EOF | BEV_EVENT_ERROR | BEV_EVENT_TIMEOUT)) {
        /* Wipeout.... */
        if (client->ssl_status.err) {
            client->status = fohh_http_client_status_connect_fail_ssl;
        } else {
            //fprintf(stderr, "Fail 2\n");
            client->status = fohh_http_client_status_connect_fail;
        }
        if (client->create_callback) {
            (client->create_callback)(client,
                                      client->status,
                                      client->create_callback_void,
                                      client->create_callback_int);
        }

    } else if (style & BEV_EVENT_CONNECTED) {
        client->status = fohh_http_client_status_success;
        if (client->create_callback) {
            (client->create_callback)(client,
                                      client->status,
                                      client->create_callback_void,
                                      client->create_callback_int);
        }

    } else {
        client->status = fohh_http_client_status_failure;
        if (client->create_callback) {
            (client->create_callback)(client,
                                      client->status,
                                      client->create_callback_void,
                                      client->create_callback_int);
        }
    }
}

static int fohh_client_proxy_connect_headers_complete(struct http_parser *parser)
{
    struct fohh_http_client *client = parser->data;
    client->status = fohh_http_client_status_proxy_done;
    return 0;
}

static void fohh_client_proxy_connect_cb(evutil_socket_t sock, short flags, void *cookie)
{
    char buf[2000];
    struct fohh_http_client *client = (struct fohh_http_client *) cookie;
    ssize_t slen, txlen;
    static struct http_parser_settings null_parser = {
        NULL, //  http_cb      on_message_begin;
        NULL, //  http_data_cb on_url;
        NULL, //  http_data_cb on_status;
        NULL, //  http_data_cb on_header_field;
        NULL, //  http_data_cb on_header_value;
        fohh_client_proxy_connect_headers_complete, //  http_cb      on_headers_complete;
        NULL, //  http_data_cb on_body;
        NULL, //  http_cb      on_message_complete;
        NULL, //  http_cb      on_chunk_header;
        NULL  //  http_cb      on_chunk_complete;
    };

    if (flags & EV_TIMEOUT) {
        FOHH_LOG(AL_ERROR, "Connection through proxy %s timed out, remote host: %s:%d",
                    client->proxy_host, client->remote_host, client->remote_port_he);
        client->status = fohh_http_client_status_failure;
        fohh_http_client_state_machine(client);
        return;
    }

    if ((client->status == fohh_http_client_status_proxy_connect_v4) ||
        (client->status == fohh_http_client_status_proxy_connect_v6)) {
        int err = 0;
        socklen_t len = sizeof(err);
        if (getsockopt(client->sock, SOL_SOCKET, SO_ERROR, &err, &len)) {
            /* Cannot get socket status */
            FOHH_LOG(AL_ERROR, "Cannot get status of proxy connection to %s, remote host: %s:%d",
                            client->proxy_host, client->remote_host, client->remote_port_he);
            client->status = fohh_http_client_status_failure;
            fohh_http_client_state_machine(client);
            return;
        }

        if (err) {
            if (client->status == fohh_http_client_status_proxy_connect_v4) {
                /* Try V6 */
                FOHH_LOG(AL_NOTICE, "Proxy connect failed to %s (v4), remote host: %s:%d",
                            client->proxy_host, client->remote_host, client->remote_port_he);
                event_free(client->timeout_event);
                client->timeout_event = NULL;
                close(client->sock);
                client->sock = -1;
                client->status = fohh_http_client_status_resolve_v6;
                fohh_http_client_state_machine(client);
                return;
            } else {
                FOHH_LOG(AL_ERROR, "Proxy connect failed to %s (v6), remote host: %s:%d",
                            client->proxy_host, client->remote_host, client->remote_port_he);
                client->status = fohh_http_client_status_failure;
                fohh_http_client_state_machine(client);
                return;
            }
        }

        snprintf(buf, sizeof(buf),
                 "CONNECT %s:%d HTTP/1.1\r\n"
                 "Host: %s:%d\r\n"
                 "Proxy-Connection: keep-alive\r\n"
                 "User-Agent: Zscaler/0.1\r\n"
                 "\r\n",
                 client->remote_host, client->remote_port_he,
                 client->remote_host, client->remote_port_he);
        slen = strlen(buf);
        txlen = send(client->sock, buf, slen, 0);
        if (txlen != slen) {
            FOHH_LOG(AL_ERROR, "Cannot send proxy request to %s, remote host: %s:%d",
                            client->proxy_host, client->remote_host, client->remote_port_he);
            client->status = fohh_http_client_status_failure;
            fohh_http_client_state_machine(client);
            return;
        }
        client->status = fohh_http_client_status_proxy_parse;
        http_parser_init(&(client->proxy_parser), HTTP_RESPONSE);
        client->proxy_parser.data = client;
    }
    if (client->status == fohh_http_client_status_proxy_parse) {
        slen = recv(client->sock, buf, sizeof(buf) - 1, 0);
        if (slen < 0) {
            if (errno == EAGAIN) return;
            FOHH_LOG(AL_ERROR, "Received connection error while attempting to proxy connection through %s: %s, remote host: %s:%d",
                            client->proxy_host, strerror(errno), client->remote_host, client->remote_port_he);
            client->status = fohh_http_client_status_failure;
            fohh_http_client_state_machine(client);
            return;
        } else if (slen == 0) {
            FOHH_LOG(AL_ERROR, "Received connection close while attempting to proxy connection through %s, remote host: %s:%d",
                                client->proxy_host, client->remote_host, client->remote_port_he);
            client->status = fohh_http_client_status_failure;
            fohh_http_client_state_machine(client);
            return;
        }
        buf[slen] = 0;
        txlen = http_parser_execute(&(client->proxy_parser), &null_parser, (const char *)buf, slen);
        if (txlen != slen) {
            FOHH_LOG(AL_ERROR, "HTTP response parsing error while attempting to proxy connection through %s, remote host: %s:%d",
                        client->proxy_host, client->remote_host, client->remote_port_he);
            client->status = fohh_http_client_status_failure;
            fohh_http_client_state_machine(client);
            return;
        }
        if (client->status != fohh_http_client_status_proxy_done) {
            FOHH_DEBUG_CONN("Waiting for more HTTP data. Rxed: [%s]", buf);
            return;
        }
        FOHH_DEBUG_CONN("Proxy response complete, code = %d", client->proxy_parser.status_code);
        if (client->proxy_parser.status_code != 200) {
            FOHH_LOG(AL_ERROR, "HTTP response = %d while attempting to proxy connection through %s, remote host: %s:%d",
                            client->proxy_parser.status_code, client->proxy_host, client->remote_host, client->remote_port_he);
            client->status = fohh_http_client_status_failure;
            fohh_http_client_state_machine(client);
            return;
        }
        event_free(client->proxy_event);
        client->proxy_event = NULL;
        client->status = fohh_http_client_status_proxy_done;
        fohh_http_client_state_machine(client);
        return;
    } else {
        FOHH_LOG(AL_ERROR, "Proxy connection in weird state");
        client->status = fohh_http_client_status_failure;
        fohh_http_client_state_machine(client);
        return;
    }
}


static void fohh_http_client_state_machine(struct fohh_http_client *client)
{
    int tmp_val;
    int64_t now = epoch_us();
    struct event_base *evbase;
    struct timeval tv;

    //fprintf(stderr, "Running client state machine... status = %s\n", fohh_http_client_status_str(client->status));

    if (now >= client->timeout_epoch_us) {
        fohh_http_client_timeout(-1, 0, client);
        return;
    }

    switch(client->status) {
    case fohh_http_client_status_resolve_v4:
        if (client->zcdns_result->addr_a) {
            client->remote_addr = client->zcdns_result->addr_a->addr;
            if (client->proxy_host) {
                ((struct sockaddr_in *)&(client->remote_addr))->sin_port = htons(client->proxy_port_he);
            } else {
                ((struct sockaddr_in *)&(client->remote_addr))->sin_port = htons(client->remote_port_he);
            }
            client->status = fohh_http_client_status_connect_v4;
            break;
        }
        ZPATH_FALLTHROUGH;
    case fohh_http_client_status_resolve_v6:
        if (client->zcdns_result->addr_aaaa) {
            client->remote_addr = client->zcdns_result->addr_aaaa->addr;
            if (client->proxy_host) {
                ((struct sockaddr_in6 *)&(client->remote_addr))->sin6_port = htons(client->proxy_port_he);
            } else {
                ((struct sockaddr_in6 *)&(client->remote_addr))->sin6_port = htons(client->remote_port_he);
            }
            client->status = fohh_http_client_status_connect_v6;
            break;
        }
        client->status = fohh_http_client_status_dns_fail;
        break;
    default:
        break;
    }

    switch(client->status) {
    case fohh_http_client_status_proxy_connect_v4:
    case fohh_http_client_status_proxy_connect_v6:
        break;
    case fohh_http_client_status_proxy_parse:
        /* Weird. */
        break;
    case fohh_http_client_status_resolve_v4:
    case fohh_http_client_status_resolve_v6:
    default:
        /* These two shouldn't be possible. */
        client->status = fohh_http_client_status_dns_fail;
        ZPATH_FALLTHROUGH;
    case fohh_http_client_status_invalid:
    case fohh_http_client_status_timeout:
    case fohh_http_client_status_dns_fail:
    case fohh_http_client_status_connect_fail:
    case fohh_http_client_status_failure:
        if (client->create_callback) {
            (client->create_callback)(client,
                                      client->status,
                                      client->create_callback_void,
                                      client->create_callback_int);
        }
        fohh_http_client_disable(client, 0);
        break;
    case fohh_http_client_status_connect_v4:
        if (client->sock == -1) {
            client->sock = socket(AF_INET, SOCK_STREAM, IPPROTO_TCP);
            if (client->sock < 0) {
                /* Try V6 */
                client->status = fohh_http_client_status_resolve_v6;
                fohh_http_client_state_machine(client);
                return;
            }
            //fprintf(stderr, "Created socket V4\n");
        }
        ZPATH_FALLTHROUGH;
    case fohh_http_client_status_connect_v6:
        if (client->sock == -1) {
            client->sock = socket(AF_INET6, SOCK_STREAM, IPPROTO_TCP);
            if (client->sock < 0) goto fail;
            //fprintf(stderr, "Created socket V6\n");
        }

#if (defined(__APPLE__) && defined(__MACH__)) || defined(__FreeBSD__)
        tmp_val = 1;
        setsockopt(client->sock, SOL_SOCKET, SO_NOSIGPIPE, (void *)&tmp_val, sizeof(tmp_val));
#endif
        tmp_val = 1;
        setsockopt(client->sock, IPPROTO_TCP, TCP_NODELAY, (void *)&tmp_val, sizeof(tmp_val));
        evutil_make_socket_nonblocking(client->sock);

        evbase = fohh_get_thread_event_base(client->fohh_thread_id);
        if (!evbase) goto fail;

        client->timeout_event = event_new(evbase, -1, 0, fohh_http_client_timeout, client);
        if (!client->timeout_event) goto fail;

        tv.tv_sec = (client->timeout_epoch_us - now) / 1000000;
        tv.tv_usec = (client->timeout_epoch_us - now) % 1000000;
        if (event_add(client->timeout_event, &tv)) goto fail;

        if (client->proxy_host) {
            if (client->status == fohh_http_client_status_connect_v4) {
                client->status = fohh_http_client_status_proxy_connect_v4;
            } else {
                client->status = fohh_http_client_status_proxy_connect_v6;
            }
            client->proxy_event = event_new(evbase, client->sock, EV_TIMEOUT|EV_WRITE|EV_READ|EV_PERSIST, fohh_client_proxy_connect_cb, client);
            if (!client->proxy_event) goto fail;
            tv.tv_sec = 3;
            tv.tv_usec = 0;
            event_add(client->proxy_event, &tv);
            int res;
            res = connect(client->sock,
                          (struct sockaddr *) &(client->remote_addr),
                          client->remote_addr.ss_family == AF_INET ? sizeof(struct sockaddr_in) : sizeof(struct sockaddr_in6));

            if ((res) && (errno != EINPROGRESS)) {
                if (client->status == fohh_http_client_status_proxy_connect_v4) {
                    /* Try V6 */
                    event_free(client->timeout_event);
                    client->timeout_event = NULL;
                    close(client->sock);
                    client->sock = -1;
                    client->status = fohh_http_client_status_resolve_v6;
                    fohh_http_client_state_machine(client);
                    return;
                }
                goto fail;
            }
            return;
        }
        ZPATH_FALLTHROUGH;
    case fohh_http_client_status_proxy_done:

        evbase = fohh_get_thread_event_base(client->fohh_thread_id);
        if (!evbase) goto fail;

        if (client->ssl_ctx) {
            if (client->ssl) SSL_free(client->ssl);
            client->ssl = SSL_new(client->ssl_ctx);
            if (!client->ssl) goto fail;

#ifdef X509_CHECK_FLAG_NO_PARTIAL_WILDCARDS
            {
                X509_VERIFY_PARAM *param = NULL;
                param = SSL_get0_param(client->ssl);

                X509_VERIFY_PARAM_set_hostflags(param, X509_CHECK_FLAG_NO_PARTIAL_WILDCARDS);
                X509_VERIFY_PARAM_set1_host(param, client->hostname, 0);
            }
#endif

            SSL_set_ex_data(client->ssl, fohh_ssl_ex_index, &(client->ssl_status));
            SSL_set_tlsext_host_name(client->ssl, client->hostname);
            //fprintf(stderr, "Created SSL state\n");
        }

        {
            char str[INET6_ADDRSTRLEN];
            zcdns_sockaddr_storage_to_str(&(client->remote_addr), str, sizeof(str));
            //fprintf(stderr, "Resolved and connecting to %s:%d\n", str, client->remote_port_he);
        }

        if (client->ssl) {
            client->io_bufferevent =
                bufferevent_openssl_socket_new(evbase,
                                               client->sock,
                                               client->ssl,
                                               BUFFEREVENT_SSL_CONNECTING,
                                               BEV_OPT_THREADSAFE | BEV_OPT_DEFER_CALLBACKS);
        } else {
            client->io_bufferevent =
                bufferevent_socket_new(evbase,
                                       client->sock,
                                       BEV_OPT_THREADSAFE | BEV_OPT_DEFER_CALLBACKS);
        }
        if (!client->io_bufferevent) goto fail;
        bufferevent_set_dont_dump(client->io_bufferevent);

        bufferevent_setcb(client->io_bufferevent,
                          fohh_http_client_read_callback,
                          NULL,
                          fohh_http_client_event_callback,
                          client);
        bufferevent_enable(client->io_bufferevent, EV_READ|EV_WRITE);

        if (!client->proxy_host) {
            if (bufferevent_socket_connect(client->io_bufferevent,
                                           (struct sockaddr *) &(client->remote_addr),
                                           client->remote_addr.ss_family == AF_INET ? sizeof(struct sockaddr_in) : sizeof(struct sockaddr_in6))) {
                if (client->status == fohh_http_client_status_connect_v4) {
                    /* Try V6 */
                    zlibevent_bufferevent_free(client->io_bufferevent);
                    client->io_bufferevent = NULL;
                    event_free(client->timeout_event);
                    client->timeout_event = NULL;
                    if (client->ssl) {
                        SSL_free(client->ssl);
                        client->ssl = NULL;
                    }
                    close(client->sock);
                    client->sock = -1;
                    client->status = fohh_http_client_status_resolve_v6;
                    fohh_http_client_state_machine(client);
                    return;
                } else {
                    //fprintf(stderr, "Fail 3\n");
                    client->status = fohh_http_client_status_connect_fail;
                    goto fail2;
                }
            }
        } else {
            /* No timeouts allowed at this point unless we still have to negotiate SSL */
            if (!client->ssl) {
                if (client->timeout_event) {
                    event_free(client->timeout_event);
                    client->timeout_event = NULL;
                }
                client->status = fohh_http_client_status_success;
                if (client->create_callback) {
                    (client->create_callback)(client,
                                              client->status,
                                              client->create_callback_void,
                                              client->create_callback_int);
                }
            }
        }
        break;
    case fohh_http_client_status_success:
        break;
    }
    return;

 fail:
    client->status = fohh_http_client_status_failure;
 fail2:
    fohh_http_client_state_machine(client);
    return;
}

static void fohh_http_client_create_resolved(struct fohh_thread *thread, void *cookie, int64_t int_cookie)
{
    struct fohh_http_client *client = cookie;

    /* Check for any failure */
    if (!client->zcdns_result || (!client->zcdns_result->addr_a && !client->zcdns_result->addr_aaaa)) {
        FOHH_LOG(AL_WARNING, "fohh http client create failed - dns, host(%s) sni(%s)",
                                         client->remote_host, client->hostname);
        if (client->create_callback) {
            (client->create_callback)(client,
                                      fohh_http_client_status_dns_fail,
                                      client->create_callback_void,
                                      client->create_callback_int);
        }
        fohh_http_client_disable(client, 0);
        return;
    }
    fohh_http_client_state_machine(client);
}

static void fohh_http_client_create_resolved_wrong_thread(void *cb_void_cookie,
                                                          int64_t cb_int_cookie,
                                                          struct zcdns_result *result)
{
    struct fohh_http_client *client = cb_void_cookie;
    int res;

    client->zcdns_result = result;

    FOHH_DEBUG_HTTP_PP( "Resolution completed for host(%s) sni(%s)",
                                         client->remote_host, client->hostname);
    res = fohh_thread_call(client->fohh_thread_id,
                           fohh_http_client_create_resolved,
                           client,
                           0);
    if (res) {
        FOHH_LOG(AL_WARNING, "fohh http client create - resolve failed, host(%s) sni(%s)",
                                         client->remote_host, client->hostname);
        if (result) {
            zcdns_result_release(result);
        }
        if (client->create_callback) {
            (client->create_callback)(client,
                                      fohh_http_client_status_dns_fail,
                                      client->create_callback_void,
                                      client->create_callback_int);
        }
        fohh_http_client_disable(client, 0);
    }
}

static void fohh_http_client_create_onthread(struct fohh_thread *thread, void *cookie, int64_t int_cookie)
{
    struct fohh_http_client *client = cookie;
    struct zcdns_request *request;
    struct zcdns_result *result = NULL;

    client->status = fohh_http_client_status_resolve_v4;

    //fprintf(stderr, "Launching resolution request, A/AAAA\n");
    request = zcdns_resolve(client->zcdns,
                            client->proxy_host ? client->proxy_host : client->remote_host,
                            1, // do_A
                            1, // do_AAAA
                            0, // do_SRV
                            &result,
                            fohh_http_client_create_resolved_wrong_thread,
                            client,
                            0);
    if (!request) {
        client->zcdns_result = result;
        fohh_http_client_create_resolved(thread, client, 0);
    }
}

/*
 * In accordance with normal FOHH operation, we do very little
 * initialization here. We do most of it in the thread that is
 * handling this client.
 */
struct fohh_http_client *
fohh_http_client_create(struct zcdns *zcdns,
                        SSL_CTX *ssl_ctx,
                        const char *hostname,
                        const char *remote_host,
                        const char *proxy_host,
                        int32_t port_he,
                        int32_t proxy_port_he,
                        int64_t timeout_us,
                        fohh_http_client_create_cb *cb,
                        void *void_cookie,
                        int64_t int_cookie)
{
    struct fohh_http_client *client;
    int result;

    if (!zcdns ||
        !hostname ||
        !port_he ||
        !cb) {
        return NULL;
    }

    client = FOHH_CALLOC(sizeof(*client));
    if (!client) return NULL;

    client->lock = (pthread_mutex_t) PTHREAD_MUTEX_INITIALIZER;
    client->fohh_thread_id = fohh_next_thread_id();
    client->hostname = FOHH_STRDUP(hostname, strlen(hostname));
    if (remote_host) {
        client->remote_host = FOHH_STRDUP(remote_host, strlen(remote_host));
    } else {
        client->remote_host = FOHH_STRDUP(hostname, strlen(hostname));
    }
    if (proxy_host) client->proxy_host = FOHH_STRDUP(proxy_host, strlen(proxy_host));
    client->remote_port_he = port_he;
    client->proxy_port_he = proxy_port_he;
    client->sock = -1;
    client->status = fohh_http_client_status_invalid;
    client->request_status = fohh_http_client_request_status_invalid;
    client->create_callback = cb;
    client->create_callback_void = void_cookie;
    client->create_callback_int = int_cookie;
    client->start_us = epoch_us();
    client->timeout_epoch_us = client->start_us + timeout_us;
    client->zcdns = zcdns;
    client->start_us = epoch_us();
    client->ssl_ctx = ssl_ctx;
    http_parser_init(&(client->req.parser), HTTP_RESPONSE);
    client->respond_with_headers = 0;
    client->respond_with_headers_chunked_encoding = 0;
    client->respond_with_headers_len = 0;
    client->req.parser.data = client;
    client->req.resp_buf = evbuffer_new();
    zmicro_heap_init(&(client->req.heap), client->req.heap_storage, sizeof(client->req.heap_storage));

    if (!client->req.resp_buf) {
        FOHH_FREE(client->remote_host);
        FOHH_FREE(client->hostname);
        if (client->req.resp_buf) evbuffer_free(client->req.resp_buf);
        if (client->proxy_host) FOHH_FREE(client->proxy_host);
        FOHH_FREE(client);
        return NULL;
    }
    evbuffer_set_dont_dump(client->req.resp_buf);

    TAILQ_INIT(&(client->client_requests_outstanding));
    TAILQ_INIT(&(client->client_requests_queued));

    result = fohh_thread_call(client->fohh_thread_id,
                              fohh_http_client_create_onthread,
                              client,
                              0);

    if (result) {
        client->status = fohh_http_client_status_failure;
        fohh_http_client_disable(client, 0);
    }

    return client;
}


char *fohh_http_client_status_str(enum fohh_http_client_status status)
{
    static char *retvals[] = {
        [fohh_http_client_status_invalid] = "fohh_http_client_status_invalid",
        [fohh_http_client_status_resolve_v4] = "fohh_http_client_status_resolve_v4",
        [fohh_http_client_status_resolve_v6] = "fohh_http_client_status_resolve_v6",
        [fohh_http_client_status_connect_v4] = "fohh_http_client_status_connect_v4",
        [fohh_http_client_status_connect_v6] = "fohh_http_client_status_connect_v6",
        [fohh_http_client_status_dns_timeout] = "fohh_http_client_status_dns_timeout",
        [fohh_http_client_status_dns_fail] = "fohh_http_client_status_dns_fail",
        [fohh_http_client_status_connect_timeout] = "fohh_http_client_status_connect_timeout",
        [fohh_http_client_status_connect_fail] = "fohh_http_client_status_connect_fail",
        [fohh_http_client_status_connect_fail_ssl] = "fohh_http_client_status_connect_fail_ssl",
        [fohh_http_client_status_success] = "fohh_http_client_status_success",
        [fohh_http_client_status_timeout] = "fohh_http_client_status_timeout",
        [fohh_http_client_status_proxy_connect_v4] = "fohh_http_client_status_proxy_connect_v4",
        [fohh_http_client_status_proxy_connect_v6] = "fohh_http_client_status_proxy_connect_v6",
        [fohh_http_client_status_proxy_parse] = "fohh_http_client_status_proxy_parse",
        [fohh_http_client_status_proxy_done] = "fohh_http_client_status_proxy_done",
        [fohh_http_client_status_failure] = "fohh_http_client_status_failure"
    };
    if (status >= (sizeof(retvals) / sizeof(retvals[0]))) {
        return "Invalid";
    }
    if (!retvals[status]) return "Invalid";

    return retvals[status];
}

char *fohh_http_client_request_status_str(enum fohh_http_client_request_status status)
{
    static char *retvals[] = {
        [fohh_http_client_request_status_invalid] = "fohh_http_client_request_status_invalid",
        [fohh_http_client_request_status_success] = "fohh_http_client_request_status_success",
        [fohh_http_client_request_status_timeout] = "fohh_http_client_request_status_timeout",
        [fohh_http_client_request_status_failure] = "fohh_http_client_request_status_failure"
    };

    if ((int)status >= (sizeof(retvals) / sizeof(retvals[0]))) {
        return "Invalid";
    }
    if (!retvals[status]) return "Invalid";

    return retvals[status];
}

struct fohh_ssl_status *fohh_http_client_get_ssl_status(struct fohh_http_client *client)
{
    if (!client) return NULL;
    return &(client->ssl_status);
}

int fohh_http_client_get_remote_addr(struct fohh_http_client *client,
                                     struct sockaddr_storage *remote_addr)
{
    if (!client) return FOHH_RESULT_BAD_ARGUMENT;
    if (!client->remote_addr.ss_family) return FOHH_RESULT_BAD_ARGUMENT;
    (*remote_addr) = client->remote_addr;
    return FOHH_RESULT_NO_ERROR;
}

static int fohh_http_client_create_synchronous_cb(struct fohh_http_client *client,
                                                  enum fohh_http_client_status status,
                                                  void *void_cookie,
                                                  int64_t int_cookie)
{
    struct zpath_interlock *lock = void_cookie;
    client->status = status;
    client->create_callback_void = NULL;
    if (lock) {
        zpath_interlock_signal(lock);
    }
    return 0;
}

struct fohh_http_client *
fohh_http_client_create_synchronous(struct zcdns *zcdns,
                                    SSL_CTX *ssl_ctx,
                                    const char *hostname,
                                    const char *remote_host,
                                    const char *proxy_host,
                                    int32_t port_he,
                                    int32_t proxy_port_he,
                                    int64_t timeout_us,
                                    enum fohh_http_client_status *status)
{
    struct fohh_http_client *client;
    struct zpath_interlock lock;

    zpath_interlock_init(&lock);
    client = fohh_http_client_create(zcdns,
                                     ssl_ctx,
                                     hostname,
                                     remote_host,
                                     proxy_host,
                                     port_he,
                                     proxy_port_he,
                                     timeout_us,
                                     fohh_http_client_create_synchronous_cb,
                                     &lock,
                                     0);
    //fprintf(stderr, "client = %p\n", client);
    if (client) {
        zpath_interlock_wait(&lock);
        *status = client->status;
    } else {
        *status = fohh_http_client_status_failure;
    }
    return client;
}

/*
 * at this point, we should have sent out the request but it got timeout
 *
 * we will not remove the request from the outstanding queue (client->client_requests_outstanding)
 * as once the response callback triggered, it will fetch this request by request = TAILQ_FIRST(&(client->client_requests_outstanding));
 * and that routine will freed up the request there.
 *
 * in the case where we come here and client itself is gone, we have to clean up the request here.
 */
static void fohh_http_client_request_timeout(evutil_socket_t sock, int16_t flags, void *cookie)
{
    struct fohh_http_client_request *request = cookie;
    struct fohh_http_client *client = request->client;

    if (request->cb) {
        (request->cb)(client,
                      fohh_http_client_request_status_timeout,
                      0,
                      NULL,
                      request->void_cookie,
                      request->int_cookie);
        request->cb = NULL;
    }

    if (request->timeout) {
        event_free(request->timeout);
        request->timeout = NULL;
    }

    if (!client) {
        FOHH_LOG(AL_NOTICE, "reaching fohh_http_client_request_timeout and client is gone");
        if (request->req_buf) {
            evbuffer_free(request->req_buf);
            request->req_buf = NULL;
        }

        FOHH_FREE(request);
    }
}

static struct evbuffer *fohh_http_client_serialize_request(struct fohh_http_client *client,
                                                           enum fohh_http_method method,
                                                           const char *url,
                                                           struct evbuffer *extra_headers,
                                                           const char *content_type,
                                                           struct evbuffer *body)
{
    struct evbuffer *buf;
    int i;

    if (!url) return NULL;

    if (method >= fohh_http_method_str_count) return NULL;

    buf = evbuffer_new();
    if (buf) {
        evbuffer_set_dont_dump(buf);
        if (!evbuffer_add_printf(buf, "%s %s HTTP/1.1\r\nHost: %s\r\nAccept: application/json, text/javascript, */*\r\nContent-Length: %ld\r\n",
                                 fohh_http_method_str[method],
                                 url,
                                 client->hostname,
                                 body ? evbuffer_get_length(body) : 0)) {
            evbuffer_free(buf);
            buf = NULL;
        } else {
            if (client->req.cookie_count) {
                if (!evbuffer_add_printf(buf, "Cookie:")) {
                    evbuffer_free(buf);
                    return NULL;
                }
                for (i = 0; i < client->req.cookie_count; i++) {
                    if (!evbuffer_add_printf(buf, " %s=%s;", client->req.cookie_name[i], client->req.cookie_value[i])) {
                        evbuffer_free(buf);
                        return NULL;
                    }
                    // fprintf(stderr, "Sending cookie: %s=%s\n", client->req.cookie_name[i], client->req.cookie_value[i]);
                }
                if (!evbuffer_add_printf(buf, "\r\n")) {
                    evbuffer_free(buf);
                    return NULL;
                }
            }

            if (extra_headers) {
                evbuffer_add_buffer(buf, extra_headers);
            }
            if (body) {
                evbuffer_add_printf(buf, "Content-Type: %s\r\n\r\n", content_type);
                evbuffer_add_buffer(buf, body);
            } else {
                evbuffer_add(buf, "\r\n", 2);
            }

#if 0
            {
                char txt[10000];
                size_t len = evbuffer_get_length(buf);
                if (len < (sizeof(txt) - 1)) {
                    evbuffer_copyout(buf, txt, len);
                    txt[len] = 0;
                    fprintf(stderr, "Request:\n%s\n", txt);
                }
            }
#endif
        }
    }
    return buf;
}

/*
 * at this point, we have a request sitting on client->client_requests_queued
 * and its failure or timeout and we are not going to send this request ever.
 *
 * so we should clean up this request by
 *      - remove this request from client->client_requests_queued
 *      - trigger callback to notify end user that this has been failed
 *      - free the memory this request hold
 */
static void fohh_http_client_request_destroy(struct fohh_http_client_request *request, enum fohh_http_client_request_status reason)
{
    struct fohh_http_client *client = request->client;

    if (client) {
        pthread_mutex_lock(&(client->lock));
        if (request->in_requests_outstanding) {
            TAILQ_REMOVE(&(request->client->client_requests_outstanding), request, client_requests);
            request->in_requests_outstanding = 0;
        }
        if (request->in_requests_queued) {
            TAILQ_REMOVE(&(request->client->client_requests_queued), request, client_requests);
            request->in_requests_queued = 0;
        }
        pthread_mutex_unlock(&(client->lock));
    }
    if (request->cb) {
        (request->cb)(client,
                      reason,
                      0,
                      NULL,
                      request->void_cookie,
                      request->int_cookie);
        request->cb = NULL;
    }
    if (request->timeout) {
        event_free(request->timeout);
        request->timeout = NULL;
    }
    if (request->req_buf) {
        evbuffer_free(request->req_buf);
        request->req_buf = NULL;
    }

    FOHH_FREE(request);
}

static void fohh_http_client_request_queue(struct fohh_thread *thread, void *cookie, int64_t int_cookie)
{
    struct fohh_http_client_request *request = cookie;

    if (!request->client || (request->client->status != fohh_http_client_status_success)) {
        fohh_http_client_request_destroy(request, fohh_http_client_request_status_failure);
    } else {
        struct evbuffer *out;
        struct fohh_http_client *client = request->client;
        int64_t now = epoch_us();

        if (now >= request->timeout_epoch_us) {
            fohh_http_client_request_destroy(request, fohh_http_client_request_status_timeout);
        } else {
            request->timeout = event_new(fohh_get_thread_event_base(client->fohh_thread_id), -1, 0, fohh_http_client_request_timeout, request);
            if (!request->timeout) {
                fohh_http_client_request_destroy(request, fohh_http_client_request_status_failure);
            } else {
                int64_t diff = request->timeout_us;
                struct timeval tv;

                request->timeout_epoch_us = now + diff;

                tv.tv_sec = diff / 1000000;
                tv.tv_usec = diff % 1000000;

                event_add(request->timeout, &tv);

                pthread_mutex_lock(&(client->lock));
                if (request->in_requests_queued) {
                    TAILQ_REMOVE(&(request->client->client_requests_queued), request, client_requests);
                    request->in_requests_queued = 0;
                }

                out = bufferevent_get_output(request->client->io_bufferevent);
                evbuffer_add_buffer(out, request->req_buf);
                evbuffer_free(request->req_buf);
                request->req_buf = NULL;

                TAILQ_INSERT_TAIL(&(request->client->client_requests_outstanding), request, client_requests);
                request->in_requests_outstanding = 1;

                pthread_mutex_unlock(&(client->lock));
            }
        }
    }
}

/*
 * Make an HTTP request. When the full response has returned, the
 * callback will be called.
 */
int fohh_http_client_request(struct fohh_http_client *client,
                             int64_t timeout_us,
                             enum fohh_http_method method,
                             const char *url,
                             struct evbuffer *extra_headers,
                             const char *content_type,
                             struct evbuffer *body,
                             fohh_http_client_result_cb *cb,
                             void *void_cookie,
                             int64_t int_cookie)
{
    struct fohh_http_client_request *request;
    struct event_base *evbase;
    int res;

    if (!url) return FOHH_RESULT_BAD_ARGUMENT;
    if (!client) return FOHH_RESULT_BAD_ARGUMENT;

    evbase = fohh_get_thread_event_base(client->fohh_thread_id);
    if (!evbase) return FOHH_RESULT_BAD_ARGUMENT;

    if (!timeout_us) return FOHH_RESULT_BAD_ARGUMENT;

    request = FOHH_CALLOC(sizeof(*request));
    if (!request) return FOHH_RESULT_NO_MEMORY;

#if 0
    request->timeout = event_new(evbase, -1, 0, fohh_http_client_request_timeout, request);
    if (!request->timeout) {
        FOHH_FREE(request);
        return FOHH_RESULT_NO_MEMORY;
    }
#endif // 0

    request->cb = cb;
    request->void_cookie = void_cookie;
    request->int_cookie = int_cookie;
    request->client = client;
    request->timeout_us = timeout_us;
    request->timeout_epoch_us = epoch_us() + timeout_us;

    request->req_buf = fohh_http_client_serialize_request(client,
                                                          method,
                                                          url,
                                                          extra_headers,
                                                          content_type,
                                                          body);
    if (!request->req_buf) {
        FOHH_FREE(request);
        return FOHH_RESULT_NO_MEMORY;
    }

    pthread_mutex_lock(&(client->lock));

    res = fohh_thread_call(client->fohh_thread_id, fohh_http_client_request_queue, request, 0);
    if (res == FOHH_RESULT_NO_ERROR) {
        TAILQ_INSERT_TAIL(&(client->client_requests_queued), request, client_requests);
        request->in_requests_queued = 1;
    }

    pthread_mutex_unlock(&(client->lock));

    if (res != FOHH_RESULT_NO_ERROR) {
        evbuffer_free(request->req_buf);
        FOHH_FREE(request);
    }

    return res;
}


static int fohh_http_client_request_synchronous_cb(struct fohh_http_client *client,
                                                   enum fohh_http_client_request_status status,
                                                   int http_status,
                                                   struct evbuffer *result_body,
                                                   void *void_cookie,
                                                   int64_t int_cookie)
{
    struct callback_struct *cb = void_cookie;

    if (cb->http_status) *(cb->http_status) = http_status;
    if (cb->status) *(cb->status) = status;
    if (cb->result_body) {
        if (result_body) {
            struct evbuffer *body_copy = evbuffer_new();
            if (body_copy) {
                evbuffer_set_dont_dump(body_copy);
                if (evbuffer_add_buffer(body_copy, result_body)) {
                    FOHH_LOG(AL_ERROR, "Cannot move data");
                }
            }
            *(cb->result_body) = body_copy;
        } else {
            *(cb->result_body) = NULL;
        }
    }
    zpath_interlock_signal(cb->lock);
    return 0;
}


int fohh_http_client_request_synchronous(struct fohh_http_client *client,
                                         int64_t timeout_us,
                                         enum fohh_http_method method,
                                         const char *url,
                                         struct evbuffer *extra_headers,
                                         const char *content_type,
                                         struct evbuffer *body,
                                         enum fohh_http_client_request_status *status,
                                         int *http_status,
                                         struct evbuffer **result_body)
{
    struct callback_struct cb;
    struct zpath_interlock lock;
    int res;

    cb.lock = &lock;
    cb.status = status;
    cb.result_body = result_body;
    cb.http_status = http_status;

    zpath_interlock_init(&lock);
    res = fohh_http_client_request(client,
                                   timeout_us,
                                   method,
                                   url,
                                   extra_headers,
                                   content_type,
                                   body,
                                   fohh_http_client_request_synchronous_cb,
                                   &cb,
                                   0);
    if (res) {
        return res;
    }
    zpath_interlock_wait(&lock);
    return res;
}


int fohh_http_client_add_trusted_certs_to_ssl_ctx_from_buffer(SSL_CTX *ssl_ctx, const char* buf, size_t data_len) {
    int res = FOHH_RESULT_NO_ERROR;
    int i;
    char **pems;
    int pems_count = 0;

    pems = zcrypt_read_pems_from_buffer(buf, data_len, &pems_count);
    if (pems) {
        /* Add the certs we want to trust... */
        for (i = 0; i < pems_count; i++) {
            res = fohh_ssl_ctx_add_trusted_root_mem(ssl_ctx, (char *)pems[i], strlen(pems[i]));
            if (res) {
                FOHH_LOG(AL_ERROR, "Could not add trusted cert to SSL context, errored #%d", i+1);
                break;
            }
        }
        FOHH_LOG(AL_NOTICE, "FOHH Custom Trusted certificates added %d", pems_count);
        zcrypt_pems_free(pems);
    } else {
        FOHH_LOG(AL_ERROR, "Could not initialize trusted certificate store");
    }
    return res;
}

static SSL_CTX *fohh_http_client_get_ssl_ctx_from_buffer(const char* buf, size_t data_len) {
    SSL_CTX *ssl_ctx;
    int res;
    int i;
    char **pems;
    int pems_count = 0;

    ssl_ctx = SSL_CTX_new(FOHH_TLS_CLIENT_METHOD());
    if (!ssl_ctx) {
        FOHH_LOG(AL_NOTICE, "Could not create SSL context");
        return NULL;
    }

    /* Standard ciphers, etc */
    res = fohh_ssl_ctx_client_standard_options(ssl_ctx, FOHH_ECDH_CURVES, VERIFY_PEER);
    if (res) {
        FOHH_LOG(AL_ERROR, "Could not set standard SSL options");
        SSL_CTX_free(ssl_ctx);
        return NULL;
    }

    /* Set up only peer certification */
    SSL_CTX_set_verify(ssl_ctx, SSL_VERIFY_PEER, fohh_ssl_verify_callback);

    pems = zcrypt_read_pems_from_buffer(buf, data_len, &pems_count);
    if (pems) {
        FOHH_LOG(AL_DEBUG, "Trusted certificate store contains %d certs", pems_count);

        /* Add the certs we want to trust... */
        for (i = 0; i < pems_count; i++) {
            res = fohh_ssl_ctx_add_trusted_root_mem(ssl_ctx, (char *)pems[i], strlen(pems[i]));
            if (res) {
                FOHH_LOG(AL_ERROR, "Could not add trusted cert to SSL context");
                SSL_CTX_free(ssl_ctx);
                zcrypt_pems_free(pems);
                return NULL;
            }
        }
        zcrypt_pems_free(pems);
    } else {
        FOHH_LOG(AL_ERROR, "Could not initialize trusted certificate store");
    }
    return ssl_ctx;
}

SSL_CTX *fohh_http_client_get_ssl_ctx(void)
{
    SSL_CTX *ssl_ctx = NULL;
    ssl_ctx=fohh_http_client_get_ssl_ctx_from_buffer((const char *)ca_sarge_pem, ca_sarge_pem_len);
    if ( fohh_ext_trusted_ca_certs_count ) {
        for (int certind=0; certind < fohh_ext_trusted_ca_certs_count; certind++ ){
            fohh_http_client_add_trusted_certs_to_ssl_ctx_from_buffer(ssl_ctx, fohh_ext_trusted_ca_certs[certind], strnlen(fohh_ext_trusted_ca_certs[certind], ZCRYPT_MAX_CERT_LEN));
        }
    }
    return ssl_ctx;
}

int fohh_http_client_add_os_bundle_to_ssl_ctx(SSL_CTX *ssl_ctx, int is_zscaler_os)
{
    if (!ssl_ctx) {
        FOHH_LOG(AL_ERROR, "Null ssl_ctx when adding os bundle");
    }

    if (is_zscaler_os) {
        FOHH_LOG(AL_NOTICE, "Using FreeBSD OS bundle");
        return fohh_http_client_add_certs_to_ssl_ctx_from_file(ssl_ctx, OS_BUNDLE_PATH_FREEBSD);
    }
#ifdef __linux__
    int res = FOHH_RESULT_NO_ERROR;
    FOHH_LOG(AL_NOTICE, "Using RHEL OS bundle");
    res = fohh_http_client_add_certs_to_ssl_ctx_from_file(ssl_ctx, OS_BUNDLE_PATH_RHEL);
    if (res) {
        /* failed to fetch bundle cert, maybe not CentOS? retry with Debian... */
        FOHH_LOG(AL_NOTICE, "Using Debian OS bundle");
        res = fohh_http_client_add_certs_to_ssl_ctx_from_file(ssl_ctx, OS_BUNDLE_PATH_DEBIAN);
    }
    return res;
#endif
#ifdef __FreeBSD__
    FOHH_LOG(AL_NOTICE, "Using FreeBSD OS bundle");
    return fohh_http_client_add_certs_to_ssl_ctx_from_file(ssl_ctx, OS_BUNDLE_PATH_FREEBSD);
#endif
#if defined(__APPLE__) && defined(__MACH__)
    FOHH_LOG(AL_NOTICE, "Using OSX OS bundle");
    return fohh_http_client_add_certs_to_ssl_ctx_from_file(ssl_ctx, OS_BUNDLE_PATH_OSX);
#endif
    FOHH_LOG(AL_ERROR, "Unknown OS type");
    return FOHH_RESULT_NOT_IMPLEMENTED;
}

int fohh_http_client_add_certs_to_ssl_ctx_from_file(SSL_CTX *ssl_ctx, const char* filename) {
    // Protection in case some wrong file name was provided with ridiculous size
    static long MAX_CERT_FILE_SIZE = 1024*1024;

    char * buffer = NULL;
    size_t file_size;
    int res = FOHH_RESULT_NO_ERROR;

    if (!ssl_ctx) {
        FOHH_LOG(AL_ERROR, "Null ssl_ctx when adding certs from file");
        return FOHH_RESULT_ERR;
    }

    FILE * f = fopen(filename, "rb");
    if (!f) {
        FOHH_LOG(AL_ERROR, "Could not open cert file %s, errno: %d", filename, errno);
        res = FOHH_RESULT_NOT_FOUND;
        goto cleanup;
    }

    if (0 != fseek(f, 0, SEEK_END)) {
        FOHH_LOG(AL_ERROR, "Cert file %s, fseek_end errno: %d", filename, errno);
        res = FOHH_RESULT_ERR;
        goto cleanup;
    }

    long len = ftell(f);
    if (len < 0) {
        FOHH_LOG(AL_ERROR, "Cert file %s, ftell errno: %d", filename, errno);
        res = FOHH_RESULT_ERR;
        goto cleanup;
    }

    if (len > MAX_CERT_FILE_SIZE) {
        FOHH_LOG(AL_ERROR, "Cert file %s is too large, size: %ld, allowed: %ld",
                            filename, len, MAX_CERT_FILE_SIZE);
        res = FOHH_RESULT_ERR_TOO_LARGE;
        goto cleanup;
    }

    file_size = (size_t)len;
    if (0 != fseek(f, 0, SEEK_SET)) {
        FOHH_LOG(AL_ERROR, "Cert file %s, fseek_head errno: %d", filename, errno);
        res = FOHH_RESULT_ERR;
        goto cleanup;
    }

    buffer = FOHH_CALLOC(file_size + 1);
    size_t bytes_read_total = 0;
    while (bytes_read_total < file_size) {
        size_t bytes_read = fread((buffer + bytes_read_total), 1, (file_size - bytes_read_total), f);
        if (!bytes_read) {
            FOHH_LOG(AL_ERROR, "Partially read cert file %s, %lu out of %lu bytes",
                                filename, bytes_read_total, file_size);
            res = FOHH_RESULT_ERR;
            goto cleanup;
        }
        bytes_read_total += bytes_read;
    }

    res = fohh_http_client_add_trusted_certs_to_ssl_ctx_from_buffer(ssl_ctx, buffer, file_size);

cleanup:
    if (f) fclose (f);
    if (buffer) FOHH_FREE(buffer);
    return res;
}

SSL_CTX *fohh_http_client_get_ssl_ctx_from_file(const char* filename) {
    // Protection in case some wrong file name was provided with ridiculous size
    static long MAX_CERT_FILE_SIZE = 1024*1024;

    SSL_CTX *ssl_ctx = NULL;
    char * buffer = NULL;
    size_t file_size;

    FILE * f = fopen(filename, "rb");
    if (!f) {
        FOHH_LOG(AL_ERROR, "Could not open cert file %s, errno: %d", filename, errno);
        goto cleanup;
    }

    if (0 != fseek(f, 0, SEEK_END)) {
        FOHH_LOG(AL_ERROR, "Cert file %s, fseek_end errno: %d", filename, errno);
        goto cleanup;
    }

    long len = ftell(f);
    if (len < 0) {
        FOHH_LOG(AL_ERROR, "Cert file %s, ftell errno: %d", filename, errno);
        goto cleanup;
    }

    if (len > MAX_CERT_FILE_SIZE) {
        FOHH_LOG(AL_ERROR, "Cert file %s is too large, size: %ld, allowed: %ld",
                            filename, len, MAX_CERT_FILE_SIZE);
        goto cleanup;
    }

    file_size = (size_t)len;
    if (0 != fseek(f, 0, SEEK_SET)) {
        FOHH_LOG(AL_ERROR, "Cert file %s, fseek_head errno: %d", filename, errno);
        goto cleanup;
    }

    buffer = FOHH_CALLOC(file_size + 1);
    size_t bytes_read_total = 0;
    while (bytes_read_total < file_size) {
        size_t bytes_read = fread((buffer + bytes_read_total), 1, (file_size - bytes_read_total), f);
        if (!bytes_read) {
            FOHH_LOG(AL_ERROR, "Partially read cert file %s, %lu out of %lu bytes",
                                filename, bytes_read_total, file_size);
            goto cleanup;
        }
        bytes_read_total += bytes_read;
    }
    ssl_ctx = fohh_http_client_get_ssl_ctx_from_buffer(buffer, file_size);

cleanup:
    if (f) fclose (f);
    if (buffer) FOHH_FREE(buffer);
    return ssl_ctx;
}

/*
 * Create an evbuffer with a json object in it consisting of
 * name:value pairs that were passed in as arguments
 */
static struct evbuffer *evbuffer_object(int count, va_list list)
{
    struct evbuffer *buf;
    JSON_Value *json;
    JSON_Object *json_obj;
    JSON_Status status;
    char tmp_str[10000];
    int i;

    json = json_value_init_object();
    if (!json) {
        return NULL;
    }

    json_obj = json_value_get_object(json);
    if (!json_obj) {
        json_value_free(json);
        return NULL;
    }

    for (i = 0; i < count; i++) {
        char *name = va_arg(list, char *);
        char *value = va_arg(list, char *);

        status = json_object_set_value(json_obj, name, json_value_init_string(value));
        if (status == JSONFailure) {
            json_value_free(json);
            return NULL;
        }
    }

    status = json_serialize_to_buffer(json, tmp_str, sizeof(tmp_str));
    if (status == JSONFailure) {
        json_value_free(json);
        return NULL;
    }

    buf = evbuffer_new();
    if (!buf) {
        json_value_free(json);
        return NULL;
    }
    evbuffer_set_dont_dump(buf);

    //ZPATH_LOG(AL_DEBUG, "jsonified to <%s>", tmp_str);

    if (evbuffer_add(buf, tmp_str, strlen(tmp_str))) {
        evbuffer_free(buf);
        json_value_free(json);
        return NULL;
    }
    json_value_free(json);

    return buf;
}


/*
 * Note about http error code 404:
 * S3 doesn’t use a 404 because then you’d be able to enumerate file paths and stuff. It sends 403 if something doesn’t
 * exist
 */
int fohh_http_client_fetch_synchronous(char *description,
                                       struct fohh_http_client *client,
                                       enum fohh_http_method method,
                                       const char *url,
                                       int http_expected_status,
                                       int *http_status,
                                       struct evbuffer **body,
                                       int value_count,
                                       ...)
{
    enum fohh_http_client_request_status req_status;
    struct evbuffer *buf = NULL;
    struct evbuffer *req_body;
    va_list list;
    int res;
    int timeout = FOHH_HTTP_CLIENT_DEFAULT_TIMEOUT;

    /* Use custom timeout, if specified. Else, default timeout value will be used */
    if (value_count == FOHH_HTTP_CLIENT_FETCH_TIMEOUT_INDICATOR) {
        va_start(list, value_count);
        timeout = va_arg(list, int);
        va_end(list);
        buf = evbuffer_new();
    } else if (value_count) {
        va_start(list, value_count);
        buf = evbuffer_object(value_count, list);
        va_end(list);
    } else {
        buf = evbuffer_new();
    }

    if (!buf) {
        return FOHH_RESULT_NO_MEMORY;
    }
    evbuffer_set_dont_dump(buf);

    req_body = NULL;

    res = fohh_http_client_request_synchronous(client,
                                               timeout*1000*1000,
                                               method,
                                               url,
                                               NULL,
                                               "application/json",
                                               buf,
                                               &req_status,
                                               http_status,
                                               &req_body);
    if (res) {
        goto done;
    }

    if (req_status != fohh_http_client_request_status_success) {
        if (req_status == fohh_http_client_request_status_timeout) {
            FOHH_LOG(AL_ERROR, "%s failed: timeout", description);
        } else if (req_status == fohh_http_client_request_status_failure) {
            FOHH_LOG(AL_ERROR, "%s failed: http protocol failure", description);
        } else {
            FOHH_LOG(AL_ERROR, "%s failed.", description);
        }
        res = FOHH_RESULT_ERR;
        goto done;
    }

    if (http_expected_status && (*http_status != http_expected_status)) {
        FOHH_LOG(AL_ERROR, "%s from %s%s failed: Expected HTTP %d, got HTTP %d",
                 description,
                 client->hostname,
                 url,
                 http_expected_status,
                 *http_status);
        res = FOHH_RESULT_ERR;
        goto done;
    }

    res = FOHH_RESULT_NO_ERROR;

done:
    /* buf should be empty here, but still... */
    evbuffer_drain(buf, evbuffer_get_length(buf));
    if (req_body) evbuffer_add_buffer(buf, req_body);
    *body = buf;
    if (req_body) evbuffer_free(req_body);

#if 0
    {
        char rx_txt[10000];
        ssize_t len;

        len = evbuffer_copyout(buf, rx_txt, sizeof(rx_txt));
        if (len >= 0) {
            rx_txt[len] = 0;
        }
        FOHH_LOG(AL_DEBUG, "HTTP Request returned body data <%s>", rx_txt);
    }
#endif // 0

    //FOHH_LOG(AL_DEBUG, "%s: Success. HTTP status = %d, body_len = %d", description, *http_status, (int) evbuffer_get_length(*body));
    return res;
}

/*
 * fohh_http_client_fetch_synchronous_v4
 * This call is used for fetching data from enrollment service usinf V4 version of APIs
 *
 * description - Type of call (Get CSR/ Get Enrollment details etc)
 * client - FOHH http client (already created)
 * method - GET/POST
 * url - API URL
 * http_expected_status - Expected return code from enrollment service - 200 usually
 * http_status - Actual status code received from enrollment service
 * body - Data returned from enrollment service for success/failure cases
 * token - Bearer token needed for fresh enrollment (Optional)
 * value_count - Number of additional arguments to follow
 */
int fohh_http_client_fetch_synchronous_v4(char *description,
                                       struct fohh_http_client *client,
                                       enum fohh_http_method method,
                                       const char *url,
                                       int http_expected_status,
                                       int *http_status,
                                       struct evbuffer **body,
                                       const char* token,
                                       int value_count,
                                       ...)
{
    enum fohh_http_client_request_status req_status;
    struct evbuffer *buf = NULL;
    struct evbuffer *req_body;
    va_list list;
    int res;
    int timeout = FOHH_HTTP_CLIENT_DEFAULT_TIMEOUT;
    struct evbuffer *extra_headers = NULL;

    /* Use custom timeout, if specified. Else, default timeout value will be used */
    if (value_count == FOHH_HTTP_CLIENT_FETCH_TIMEOUT_INDICATOR) {
        va_start(list, value_count);
        timeout = va_arg(list, int);
        va_end(list);
        buf = evbuffer_new();
    } else if (value_count) {
        va_start(list, value_count);
        buf = evbuffer_object(value_count, list);
        va_end(list);
    } else {
        buf = evbuffer_new();
    }

    if (!buf) {
        return FOHH_RESULT_NO_MEMORY;
    }
    evbuffer_set_dont_dump(buf);

    req_body = NULL;

    /* For fresh/first-time enrollment, it must be a defined value */
    if (token && token[0] != '\0') {
        extra_headers = evbuffer_new();
        if (!extra_headers) {
            res = FOHH_RESULT_ERR;
            goto done;
        }

        if (evbuffer_add_printf(extra_headers, "Authorization: Bearer %s\n", token) < 0) {
            res = FOHH_RESULT_ERR;
            goto done;
        }
    }

    res = fohh_http_client_request_synchronous(client,
                                            timeout*1000*1000,
                                            method,
                                            url,
                                            extra_headers,
                                            "application/json",
                                            buf,
                                            &req_status,
                                            http_status,
                                            &req_body);

    if (res) {
        FOHH_LOG(AL_ERROR, "%s failed.", description);
        goto done;
    }

    if (req_status != fohh_http_client_request_status_success) {
        if (req_status == fohh_http_client_request_status_timeout) {
            FOHH_LOG(AL_ERROR, "%s failed: timeout", description);
        } else if (req_status == fohh_http_client_request_status_failure) {
            FOHH_LOG(AL_ERROR, "%s failed: http protocol failure", description);
        } else {
            FOHH_LOG(AL_ERROR, "%s failed.", description);
        }
        res = FOHH_RESULT_ERR;
        goto done;
    }

    if (http_expected_status && (*http_status != http_expected_status)) {
        FOHH_LOG(AL_ERROR, "%s from %s%s failed: Expected HTTP %d, got HTTP %d",
                 description,
                 client->hostname,
                 url,
                 http_expected_status,
                 *http_status);
        res = FOHH_RESULT_ERR;
        goto done;
    }

    res = FOHH_RESULT_NO_ERROR;

done:
    /* buf should be empty here, but still... */
    evbuffer_drain(buf, evbuffer_get_length(buf));
    if (req_body) evbuffer_add_buffer(buf, req_body);
    *body = buf;
    if (req_body) evbuffer_free(req_body);
    if (extra_headers) evbuffer_free(extra_headers);

    return res;
}

/**
 * Adds custom external trusted CA certificates to the existing list.
 *
 * This function reallocates memory for the existing list of trusted CA certificates
 * and appends the new certificates to it.
 *
 */
void fohh_add_ext_trusted_certs(struct zcrypt_root_cert_map *trusted_ca_certs, size_t trusted_ca_certs_count) {
    char **new_fohh_ext_trusted_ca_certs = NULL;
    int *uniq_cert_ind;
    if (trusted_ca_certs_count == 0) {
        return;
    }
    uniq_cert_ind = (int *) FOHH_CALLOC (trusted_ca_certs_count * sizeof(int));

    size_t unique_count = 0;
    for (size_t certind = 0; certind < trusted_ca_certs_count; certind++) {
        int is_duplicate = 0;
        for (size_t ind = 0; ind < fohh_ext_trusted_ca_certs_count; ind++) {
            if (fohh_ext_trusted_ca_certs[ind] != NULL &&
                !strcmp(fohh_ext_trusted_ca_certs[ind], trusted_ca_certs[certind].root_cert)) {
                is_duplicate = 1;
                uniq_cert_ind[certind] = 0;
                break;
            }
        }
        if (!is_duplicate) {
            unique_count++;
            uniq_cert_ind[certind] = 1;
        }
    }

    if ( unique_count <= 0 ) {
        FOHH_FREE(uniq_cert_ind);
        return;
    }

    size_t new_total_count = fohh_ext_trusted_ca_certs_count + unique_count;


    new_fohh_ext_trusted_ca_certs = (char **)FOHH_REALLOC(
            fohh_ext_trusted_ca_certs, (fohh_ext_trusted_ca_certs_count + unique_count) * sizeof(char *));

    if (new_fohh_ext_trusted_ca_certs == NULL) {
        FOHH_LOG(AL_ERROR, "Failed: allocate memory for trusted CA certs ");
        FOHH_FREE(uniq_cert_ind);
        return;
    }

    fohh_ext_trusted_ca_certs = new_fohh_ext_trusted_ca_certs;

    for (size_t i = fohh_ext_trusted_ca_certs_count; i < new_total_count; i++) {
        fohh_ext_trusted_ca_certs[i] = NULL;
    }

    for (int certind = 0; certind < trusted_ca_certs_count; certind++) {
        if(uniq_cert_ind[certind]) {
            size_t cert_len = strnlen(trusted_ca_certs[certind].root_cert, ZCRYPT_MAX_CERT_LEN);

            fohh_ext_trusted_ca_certs[fohh_ext_trusted_ca_certs_count] = FOHH_STRDUP(trusted_ca_certs[certind].root_cert, cert_len);
            fohh_ext_trusted_ca_certs_count++;
        }
    }

    FOHH_FREE(uniq_cert_ind);
    FOHH_LOG(AL_NOTICE, "Success: Added custom external trusted CA certs");
}

/**
 * Adds external trusted CA certificates from a file.
 *
 * This function reads a file containing external trusted CA certificates and adds
 * them to the list of trusted certificates.
 *
 */
void fohh_add_ext_trusted_certs_from_file(const char* filename) {
    static long MAX_CERT_FILE_SIZE = 1024*1024;

    char * buffer = NULL;
    size_t file_size;

    FILE * f = fopen(filename, "rb");
    if (!f) {
        FOHH_LOG(AL_ERROR, "Could not open cert file %s, errno: %d", filename, errno);
        goto cleanup;
    }

    if (0!= fseek(f, 0, SEEK_END)) {
        FOHH_LOG(AL_ERROR, "Cert file %s, fseek_end errno: %d", filename, errno);
        goto cleanup;
    }

    long len = ftell(f);
    if (len < 0) {
        FOHH_LOG(AL_ERROR, "Cert file %s, ftell errno: %d", filename, errno);
        goto cleanup;
    }

    if (len > MAX_CERT_FILE_SIZE) {
        FOHH_LOG(AL_ERROR, "Cert file %s is too large, size: %ld, allowed: %ld",
                            filename, len, MAX_CERT_FILE_SIZE);
        goto cleanup;
    }

    file_size = (size_t)len;
    if (0!= fseek(f, 0, SEEK_SET)) {
        FOHH_LOG(AL_ERROR, "Cert file %s, fseek_head errno: %d", filename, errno);
        goto cleanup;
    }

    buffer = FOHH_CALLOC(file_size + 1);
    size_t bytes_read_total = 0;
    while (bytes_read_total < file_size) {
        size_t bytes_read = fread((buffer + bytes_read_total), 1, (file_size - bytes_read_total), f);
        if (!bytes_read) {
            FOHH_LOG(AL_ERROR, "Partially read cert file %s, %lu out of %lu bytes",
                                filename, bytes_read_total, file_size);
            goto cleanup;
        }
        bytes_read_total += bytes_read;
    }

    char **new_fohh_ext_trusted_ca_certs = NULL;
    new_fohh_ext_trusted_ca_certs = (char **)FOHH_REALLOC(fohh_ext_trusted_ca_certs, (fohh_ext_trusted_ca_certs_count + 1) * sizeof(char *));
    fohh_ext_trusted_ca_certs = new_fohh_ext_trusted_ca_certs;

    fohh_ext_trusted_ca_certs[fohh_ext_trusted_ca_certs_count] = FOHH_STRDUP(buffer, file_size);
    fohh_ext_trusted_ca_certs_count++;

    FOHH_LOG(AL_NOTICE, "Success: Added custom external trusted CA certs");

cleanup:
    if (f) fclose (f);
    if (buffer) FOHH_FREE(buffer);
    return ;
}

size_t fohh_get_ext_trusted_certs_count() {
    return fohh_ext_trusted_ca_certs_count;
}
