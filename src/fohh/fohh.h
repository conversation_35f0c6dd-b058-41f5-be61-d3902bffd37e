/*
 * fohh.h. Copyright (C) 2013 Zscaler, Inc. All Rights Reserved.
 *
 * Full-On Honkey Handshake.
 *
 * Name origin:
 * http://www.wired.com/wired/archive/1.05/jargon_watch.html
 *
 * This library is designed to maintain communication within a
 * cooperative set of systems.
 *
 * FOHH relies very heavily on the argo library- they go basically
 * hand-in-hand. Many fohh routines are pretty close to wrappers
 * around argo commands.
 *
 * Communication is maintained via TLS.
 *
 * Communication can require client certificate on a per-server basis.
 *
 * FOHH can connect to a single domain name exactly once.
 *
 * This library sends and receives data exclusively via argo. That is,
 * only structures can be passed into the system, and only structure
 * callbacks are used on data reception. There are no exceptions.
 *
 * This library maintains communication with a remote system by
 * name. It relies on DNS to do proper name resolution. DNS resolution
 * is performed within this library using system DNS services, so it
 * correctly pays attention to /etc/hosts for static configuration and
 * its recursive resolver for dynamic configuration.
 *
 * For multiprocessing, this library uses pthreads.
 *
 * For I/O, this library uses libevent.
 *
 * This library is designed for asynchronous execution.
 *
 * fohh_connection provides mutual exclusion on itself only if
 * requested at creation time. Mutual exclusion is provided as
 * follows:
 *
 * 1. Registered services are assigned to threads round-robin.
 *
 * 2. New connections are assigned to threads round-robin.
 *
 * 3. All state associated with each thread is locked per-thread.
 *
 * 4. When multiple threads' locks must be held, then those locks must
 *    be acquired in thread-order. (to avoid deadlocks)
 *
 * 5. Locking should be mostly transparent to the user.
 *
 *
 *
 * Job: This library's job is to maintain a connection to
 * "service.domain.com". (Or to be service.domain.com, and accept
 * connections from clients). Argo tx/rx is allowed on an open
 * connection.
 *
 * There are two "sides" to the FOHH module: Server and Client.
 *
 * fohh_connection provides a callback for when connection state
 * changes.
 */

#ifndef __FOHH_H__
#define __FOHH_H__

#include <stdint.h>
#include <sys/socket.h>
#include <netinet/in.h>

#include <openssl/ssl.h>

#include <event2/buffer.h>

#include "zpath_misc/zpath_misc.h"
#include "zevent/zevent.h"

#include "argo/argo.h"
#include "argo/argo_log.h"
#include "fohh/fohh_rtt_histogram.h"

/* Server ciphers are more permissive, for backwards
 * compatibility. Note that we prefer faster curves for performance
 * reasons. */
#define FOHH_ECDH_CURVES "P-256:P-384:P-521"
#define FOHH_SSL_CIPHER_SERVER_OLD "ECDHE-RSA-AES128-GCM-SHA256:AES128-SHA"
#define FOHH_SSL_CIPHER_CLIENT "HIGH:!aNULL:!RC4:!MD5"

#define FOHH_SERVER_CIPHER_SUITE_LIST_MAX   4
#define FOHH_CLIENT_CIPHER_SUITE_LIST_MAX   3
#define FOHH_CIPHER_SUITE_STRING_MAX        1024

/*
 * To enable PFS disable session ticket reuse in client hello
 * */
#define FOHH_SSL_ENABLE_PFS SSL_OP_NO_TICKET

#if OPENSSL_VERSION_NUMBER < 0x10100000L
    // 1.0.x
#define FOHH_TLS_CLIENT_METHOD TLSv1_2_client_method
#define FOHH_TLS_SERVER_METHOD TLSv1_2_server_method
#define FOHH_DTLS_CLIENT_METHOD DTLSv1_2_client_method
#define FOHH_DTLS_SERVER_METHOD DTLSv1_2_server_method
#else
    // 1.1.x
#define FOHH_TLS_CLIENT_METHOD TLS_client_method
#define FOHH_TLS_SERVER_METHOD TLS_server_method
#define FOHH_DTLS_CLIENT_METHOD DTLS_client_method
#define FOHH_DTLS_SERVER_METHOD DTLS_server_method
#endif

/*
 * Some worker pool names for built-in capabilities of FOHH
 */
#define FOHH_POOL_LOG_SEND     "fohh_log"
#define FOHH_POOL_LOG_RECEIVE  "fohh_log"


/*
 * Return error codes.
 *
 * IMPORTANT: IMPORTANT: IMPORTANT:
 *
 * THESE RETURN CODES EXPLICITY MIRROR ARGO ERROR CODES.
 */
#define FOHH_RESULT_NO_ERROR          ARGO_RESULT_NO_ERROR          /* AKA success */
#define FOHH_RESULT_ERR               ARGO_RESULT_ERR               /* Generic error, when none other are appropriate */
#define FOHH_RESULT_NOT_FOUND         ARGO_RESULT_NOT_FOUND         /* Could not find what was requested. */
#define FOHH_RESULT_NO_MEMORY         ARGO_RESULT_NO_MEMORY         /* Could not allocate memory */
#define FOHH_RESULT_CANT_WRITE        ARGO_RESULT_CANT_WRITE        /* Failure to write (output callback failed?) */
#define FOHH_RESULT_ERR_TOO_LARGE     ARGO_RESULT_ERR_TOO_LARGE     /* Requested data doesn't fit in space provided */
#define FOHH_RESULT_BAD_ARGUMENT      ARGO_RESULT_BAD_ARGUMENT      /* Asked for something wrong. */
#define FOHH_RESULT_INSUFFICIENT_DATA ARGO_RESULT_INSUFFICIENT_DATA /* Was not provided enough data to perform operation */
#define FOHH_RESULT_NOT_IMPLEMENTED   ARGO_RESULT_NOT_IMPLEMENTED   /* Yes, for features that are not yet implemented. */
#define FOHH_RESULT_BAD_DATA          ARGO_RESULT_BAD_DATA          /* Tried to parse data, but format seemed wrong. */
#define FOHH_RESULT_WOULD_BLOCK       ARGO_RESULT_WOULD_BLOCK       /* Attempting operation would result in blocking. Bad, naughty blocking. */
#define FOHH_RESULT_BAD_STATE         ARGO_RESULT_BAD_STATE
#define FOHH_RESULT_INCOMPLETE        ARGO_RESULT_INCOMPLETE
#define FOHH_RESULT_ASYNCHRONOUS      ARGO_RESULT_ASYNCHRONOUS
#define FOHH_RESULT_EXCESS_DYN_FIELDS ARGO_RESULT_EXCESS_DYN_FIELDS
#define FOHH_RESULT_MAX               ARGO_RESULT_MAX
/*
 * Not needed at the moment but keeping it in comment
 * so the counter is understood
 * #define FOHH_RESULT_NOT_READY         (ARGO_RESULT_MAX + 1)
 * #define FOHH_RESULT_EXPIRED           (ARGO_RESULT_MAX + 2)
 */
#define FOHH_RESULT_ACCESS_DENIED     (ARGO_RESULT_MAX + 3)
#define FOHH_RESULT_CONN_DELETED      (ARGO_RESULT_MAX + 4)

#define FOHH_MAX_THREADS 256      /* Could be anything... */
#define FOHH_MAX_WORKER_POOLS 128 /* Could be anything... */
#define FOHH_MAX_NAMELEN 256      /* Maximum DNS name length */
#define FOHH_RX_DATA_TIMEOUT_S (60 * 15) /* If an FOHH connection hasn't seen
                                   * any data incoming in this much
                                   * time, the connection is
                                   * closed. */
#define FOHH_MIN_DNS_FREQUENCY 60 /* There will be at LEAST this many
                                   * seconds between DNS
                                   * requests. Note that internally
                                   * this library uses system DNS
                                   * services, so requests may not be
                                   * seen on the wire quite this
                                   * frequently. */
#define FOHH_MAX_HTTP_LISTENERS 100
#define FOHH_RTT_SINCE_LAST_QUERY_MSG_STR_MAX 64 /*taking the worst case scenario of the str*/

//worst case: 21 bytes day str + 4 bytes hour str + 4 bytes minute str + 3 bytes second str + 1 byte null at the end = 33 total bytes
#define FOHH_MAX_UPTIME_BUF_LEN 33

#define FOHH_TLV_MTU_CTRL               (2*1024*1024)
#define FOHH_TLV_MTU_DATA               (60*1024)


#define MAX_CIPHER_SUITES               100
#define MAX_CIPHER_SUITE_NAME_LEN       100

#define FOHH_CONN_TYPE_SIZE             10

#define FOHH_MAX_REDIRECT_NAME          7                /* Match the number in zpn_rpc redirect message (ZPN_CLIENT_MAX_BROKERS) */
/* Max redirect retries. We cannot reach any of redirect candidates, and have to come back to original broker this many times */
#define MAX_REDIRECT_RETRIES            3

#define FOHH_DEFAULT_BACKOFF_MAX_S             300

#define FOHH_CLOSE_REASON_TIMEOUT "FOHH_CLOSE_REASON_TIMEOUT"
#define FOHH_CLOSE_REASON_CONNECT_TIMEOUT "FOHH_CLOSE_REASON_CONNECT_TIMEOUT"
#define FOHH_CLOSE_REASON_SNI_SLOW "FOHH_CLOSE_REASON_SNI_SLOW"
#define FOHH_CLOSE_REASON_SNI_TIMEOUT "FOHH_CLOSE_REASON_SNI_TIMEOUT"
#define FOHH_CLOSE_REASON_SNI_MISSING "FOHH_CLOSE_REASON_SNI_MISSING"
#define FOHH_CLOSE_REASON_PROXY_IDLE "FOHH_CLOSE_REASON_PROXY_IDLE"
#define FOHH_CLOSE_REASON_PROXY_DNS "FOHH_CLOSE_REASON_PROXY_DNS"
#define FOHH_CLOSE_REASON_SETSOCKOPT "FOHH_CLOSE_REASON_SETSOCKOPT"
#define FOHH_CLOSE_REASON_PROXY_FAIL "FOHH_CLOSE_REASON_PROXY_FAIL"
#define FOHH_CLOSE_REASON_PROXY_PARSE "FOHH_CLOSE_REASON_PROXY_PARSE"
#define FOHH_CLOSE_REASON_PROXY_TIMEOUT "FOHH_CLOSE_REASON_PROXY_TIMEOUT"
#define FOHH_CLOSE_REASON_PROXY_NOT_200 "FOHH_CLOSE_REASON_PROXY_NOT_200"
#define FOHH_CLOSE_REASON_SERIALIZE "FOHH_CLOSE_REASON_SERIALIZE"
#define FOHH_CLOSE_REASON_REGISTRATION "FOHH_CLOSE_REASON_REGISTRATION"
#define FOHH_CLOSE_REASON_CALLBACK_ERR "FOHH_CLOSE_REASON_CALLBACK_ERR"
#define FOHH_CLOSE_REASON_HTTP_RESPONSE "FOHH_CLOSE_REASON_HTTP_RESPONSE"
#define FOHH_CLOSE_REASON_ERR "FOHH_CLOSE_REASON_ERR"
#define FOHH_CLOSE_REASON_MEMORY "FOHH_CLOSE_REASON_MEMORY"
#define FOHH_CLOSE_REASON_TLV_LEN "FOHH_CLOSE_REASON_TLV_LEN"
#define FOHH_CLOSE_REASON_TLV_DESERIALIZE "FOHH_CLOSE_REASON_TLV_DESERIALIZE"
#define FOHH_CLOSE_REASON_TLV_CALLBACK "FOHH_CLOSE_REASON_TLV_CALLBACK"
#define FOHH_CLOSE_REASON_TLV_NO_CALLBACK "FOHH_CLOSE_REASON_TLV_NO_CALLBACK"
#define FOHH_CLOSE_REASON_SOCKET_ERR "FOHH_CLOSE_REASON_SOCKET_ERR"
#define FOHH_CLOSE_REASON_SOCKET_CLOSE "FOHH_CLOSE_REASON_SOCKET_CLOSE"
#define FOHH_CLOSE_REASON_RX_TIMEOUT "FOHH_CLOSE_REASON_RX_TIMEOUT"
#define FOHH_CLOSE_REASON_SSL_CTX_NONE "FOHH_CLOSE_REASON_SSL_CTX_NONE"
#define FOHH_CLOSE_REASON_IP_ACL_NOT_MATCHED "FOHH_CLOSE_REASON_IP_ACL_NOT_MATCHED"
#define FOHH_CLOSE_REASON_REGION_RESTRICTED "FOHH_CLOSE_REASON_REGION_RESTRICTED"
#define FOHH_CLOSE_REASON_CERT_VERIFY "FOHH_CLOSE_REASON_CERT_VERIFY"
#define FOHH_CLOSE_REASON_OPS "FOHH_CLOSE_REASON_OPS"
#define FOHH_CLOSE_REASON_AST_DATA_CONN_FLOW_CONTROL "FOHH_CLOSE_REASON_AST_DATA_CONN_FLOW_CONTROL"
#define FOHH_CLOSE_REASON_BRK_DATA_CONN_FLOW_CONTROL "FOHH_CLOSE_REASON_BRK_DATA_CONN_FLOW_CONTROL"
#define FOHH_CLOSE_REASON_DATA_CONN_FLOW_CONTROL "FOHH_CLOSE_REASON_DATA_CONN_FLOW_CONTROL"
#define FOHH_CLOSE_REASON_UT_CLOSE_FROM_ASST "FOHH_CLOSE_REASON_UT_CLOSE_FROM_ASST"
#define FOHH_CLOSE_REASON_AST_PBRK_CTRL_CONN_CFG_CHG "FOHH_CLOSE_REASON_AST_PBRK_CTRL_CONN_CFG_CHG"
#define FOHH_CLOSE_REASON_AST_PBRK_VERIFY_FAILED "FOHH_CLOSE_REASON_AST_PBRK_VERIFY_FAILED"
#define FOHH_CLOSE_REASON_AST_PBRK_DATA_DOWN "FOHH_CLOSE_REASON_AST_PBRK_DATA_DOWN"
#define FOHH_CLOSE_REASON_REDIRECT "FOHH_CLOSE_REASON_REDIRECT"
#define FOHH_CLOSE_REASON_LOG_RECONN "FOHH_CLOSE_REASON_LOG_RECONN"
#define FOHH_CLOSE_REASON_UPGRADE "FOHH_CLOSE_REASON_UPGRADE"
#define FOHH_CLOSE_REASON_STATUS_TIMEOUT "FOHH_CLOSE_REASON_STATUS_TIMEOUT"
#define FOHH_CLOSE_REASON_CLIENT_AUTH_TIMEOUT "FOHH_CLOSE_REASON_CLIENT_AUTH_TIMEOUT"
#define FOHH_CLOSE_REASON_SCOPE_DISABLED "FOHH_CLOSE_REASON_SCOPE_DISABLED"
#define FOHH_CLOSE_REASON_SCOPE_FETCH_FAILED "FOHH_CLOSE_REASON_SCOPE_FETCH_FAILED"
#define FOHH_CLOSE_REASON_SCOPE_MISMATCH "FOHH_CLOSE_REASON_SCOPE_MISMATCH"
#define FOHH_CLOSE_REASON_ALT_CLOUD_SWITCH "FOHH_CLOSE_REASON_ALT_CLOUD_SWITCH"
#define FOHH_CLOSE_REASON_MANUAL_DRAIN  "FOHH_CLOSE_REASON_MANUAL_DRAIN"
#define FOHH_CLOSE_REASON_SITE_OFFLINE_DOMAIN_REMOVED  "FOHH_CLOSE_REASON_SITE_OFFLINE_DOMAIN_REMOVED"
#define FOHH_CLOSE_REASON_SITE_OFFLINE_DOMAIN_ADDED  "FOHH_CLOSE_REASON_SITE_OFFLINE_DOMAIN_ADDED"
#define FOHH_CLOSE_REASON_SITE_OFFLINE_DOMAIN_CHANGED  "FOHH_CLOSE_REASON_SITE_OFFLINE_DOMAIN_CHANGED"
#define FOHH_CLOSE_REASON_SITE_OFFLINE_DOMAIN_REACHABLE  "FOHH_CLOSE_REASON_SITE_OFFLINE_DOMAIN_REACHABLE"
#define FOHH_CLOSE_REASON_SITE_OFFLINE_DOMAIN_NOT_REACHABLE  "FOHH_CLOSE_REASON_SITE_OFFLINE_DOMAIN_NOT_REACHABLE"
#define FOHH_CLOSE_REASON_SITE_OFFLINE_PREFERRED "FOHH_CLOSE_REASON_SITE_OFFLINE_PREFERRED"
#define FOHH_CLOSE_REASON_SITE_DEFAULT_ENDPOINT_REACHABLE  "FOHH_CLOSE_REASON_SITE_DEFAULT_ENDPOINT_REACHABLE"
#define FOHH_CLOSE_REASON_SITE_DEFAULT_ENDPOINT_NOT_REACHABLE  "FOHH_CLOSE_REASON_SITE_DEFAULT_ENDPOINT_NOT_REACHABLE"
#define FOHH_CLOSE_REASON_SITE_DEFAULT_ENDPOINT_PREFERRED  "FOHH_CLOSE_REASON_SITE_DEFAULT_ENDPOINT_PREFERRED"
#define FOHH_CLOSE_REASON_SITE_DISABLED  "FOHH_CLOSE_REASON_SITE_DISABLED"
#define FOHH_CLOSE_REASON_REDIR_NO_BROKER_AVAILABLE "FOHH_CLOSE_REASON_REDIR_NO_BROKER_AVAILABLE"
#define FOHH_CLOSE_REASON_INCOMPATIBLE_CLIENT_VERSION "FOHH_CLOSE_REASON_INCOMPATIBLE_CLIENT_VERSION"
#define FOHH_CLOSE_REASON_SITEC_DELETED "FOHH_CLOSE_REASON_SITEC_DELETED"
#define FOHH_CLOSE_DATA_CONN_IDLE_TIMEOUT "FOHH_CLOSE_DATA_CONN_IDLE_TIMEOUT"
#define FOHH_CLOSE_REASON_SITEC_FIREDRILL "FOHH_CLOSE_REASON_SITEC_FIREDRILL"
#define FOHH_CLOSE_REASON_PRIVATE_BROKER_FIREDRILL "FOHH_CLOSE_REASON_PRIVATE_BROKER_FIREDRILL"
#define FOHH_CLOSE_REASON_ASSISTANT_FIREDRILL "FOHH_CLOSE_REASON_ASSISTANT_FIREDRILL"
#define FOHH_CLOSE_REASON_MISSION_ACCOMPLISHED "FOHH_CLOSE_REASON_MISSION_ACCOMPLISHED"
#define FOHH_CLOSE_REASON_MISSION_CRITICAL_CONN_FAILED "FOHH_CLOSE_REASON_MISSION_CRITICAL_CONN_FAILED"

//new reasons for pcc, pse and appc
#define FOHH_CLOSE_REASON_SWITCH_TO_PREFERRED_SITEC "FOHH_CLOSE_REASON_SWITCH_TO_PREFERRED_SITEC"
#define FOHH_CLOSE_REASON_SWITCH_TO_PUBLIC_CLOUD "FOHH_CLOSE_REASON_SWITCH_TO_PUBLIC_CLOUD"
#define FOHH_CLOSE_REASON_BROKER_REDIRECT "FOHH_CLOSE_REASON_BROKER_REDIRECT"


#define TCP_QUICKACK_CONFIG      0x1
#define TCP_QUICKACK_CONFIG_READ 0x2
#define TCP_QUICKACK_FEATURE_DELIMITER "|"
#define TCP_QUICKACK_FEATURE_STRING "QA"
#define TCP_QUICKACK_READ_FEATURE_STRING "QAR"
#define TCP_QUICKACK_FEATURE_MAXLEN 64

#define FOHH_MIN_STATUS_INTERVAL            1
#define FOHH_MAX_STATUS_INTERVAL            120
#define FOHH_DDIL_MAX_ALLOWED_DOWNTIME_S    120

#define FOHH_MALLOC_DONT_DUMP(x) fohh_malloc_aligned(x)
#define FOHH_FREE_DONT_DUMP(x) ({fohh_free_aligned(x); x = NULL;})

#define FOHH_DEBUG_CMD_WORKER_POOL "debugcmd"
#define FOHH_SET_CUSTOM_CIPHERSUITE_INDEX              -1
#define DEFAULT_SERVER_CIPHERSUITE_INDEX                0
#define DEFAULT_WEBSERVER_AES256_CIPHERSUITE_INDEX      2
#define DEFAULT_CLIENT_CIPHERSUITE_INDEX                0
#define DEFAULT_APP_CONNECTOR_CLIENT_CIPHERSUITE_INDEX  0

/*
 * The following states are used for both client and server
 * connections. Most states are only applicable to client connections.
 */
enum fohh_connection_state {
    fohh_connection_unresolved=0,    /* We have not successfully
                                      * resolved the name */
    fohh_connection_unreachable,     /* We don't have a route to the
                                      * destination */
    fohh_connection_backoff,         /* We are backing off a failed
                                      * connection or connection
                                      * attempt. */
    fohh_connection_proxy_connect,   /* Forward Proxy Connection being
                                      * connected */
    fohh_connection_proxy_parse,     /* Forward Proxy Connection being
                                      * parsed */
    fohh_connection_proxy_done,      /* Forward Proxy Connection done
                                      * being parsed */
    fohh_connection_wait_sni,        /* We have initiated a connection
                                      * to the remote system and are
                                      * waiting for SNI hello
                                      * packets... */
    fohh_connection_wait_sni_ctx,    /* We have received SNI, but
                                      * we're waiting for some
                                      * callback to have an SSL
                                      * context ready for us to use
                                      * for this connection. */
    fohh_connection_connecting,      /* We have initiated a connection
                                      * to the remote system */
    fohh_connection_validate,        /* We have a connection
                                      * established, but we're waiting
                                      * for it to be validated. */
    fohh_connection_complete,        /* Validation/SSL complete. This
                                      * state is used to connect up
                                      * callbacks, etc. It is not
                                      * passed to any users of
                                      * FOHH. */
    fohh_connection_connected,       /* We have a working (probably
                                      * TLS) connection. This is where
                                      * the connected callback
                                      * occurs. */
    fohh_connection_disconnected,    /* We had a connection for a
                                      * while... It got broken. */
    fohh_connection_deleted,         /* This connection has been
                                      * deleted */
};

/* *********************************************************************
 *  Modeled after "struct tcp_info" for linux.
 */
struct fohh_tcp_info {
    uint8_t tcpi_state;
    uint8_t tcpi_ca_state;
    uint8_t tcpi_retransmits;
    uint8_t tcpi_probes;
    uint8_t tcpi_backoff;
    uint8_t tcpi_options;
    uint8_t tcpi_snd_wscale : 4, tcpi_rcv_wscale : 4;

    uint32_t tcpi_rto;
    uint32_t tcpi_ato;
    uint32_t tcpi_snd_mss;
    uint32_t tcpi_rcv_mss;

    uint32_t tcpi_unacked;
    uint32_t tcpi_sacked;
    uint32_t tcpi_lost;
    uint32_t tcpi_retrans;
    uint32_t tcpi_fackets;

    /* Times. */
    uint32_t tcpi_last_data_sent;
    uint32_t tcpi_last_ack_sent;     /* Not remembered, sorry. */
    uint32_t tcpi_last_data_recv;
    uint32_t tcpi_last_ack_recv;

     /* Metrics. */
    uint32_t tcpi_pmtu;
    uint32_t tcpi_rcv_ssthresh;
    uint32_t tcpi_rtt;
    uint32_t tcpi_rttvar;
    uint32_t tcpi_snd_ssthresh;
    uint32_t tcpi_snd_cwnd;
    uint32_t tcpi_advmss;
    uint32_t tcpi_reordering;

    uint32_t tcpi_rcv_rtt;
    uint32_t tcpi_rcv_space;

    uint32_t tcpi_total_retrans;

    uint64_t tcpi_thru_put;

    /* The following only applies to CentOS */

    uint64_t tcpi_bytes_acked;    /* RFC4898 tcpEStatsAppHCThruOctetsAcked */
    uint64_t tcpi_bytes_received; /* RFC4898 tcpEStatsAppHCThruOctetsReceived */
    uint64_t tcpi_segs_out;       /* RFC4898 tcpEStatsPerfSegsOut */
    uint64_t tcpi_segs_in;        /* RFC4898 tcpEStatsPerfSegsIn */

};

enum fohh_connection_type {
    fohh_connection_type_listener,
    fohh_connection_type_server,
    fohh_connection_type_client
};

enum fohh_connection_style {
    fohh_connection_style_argo = 0,
    fohh_connection_style_argo_tlv = 1,
    fohh_connection_style_http,
    fohh_connection_style_tlv,
};

enum fohh_site_connection_state {
    fohh_site_connection_default = 0,
    fohh_site_offline_domain_added,
    fohh_site_offline_domain_removed,
    fohh_site_offline_domain_changed,
    fohh_site_offline_domain_reachable,
    fohh_site_offline_domain_not_reachable,
    fohh_site_offline_domain_preferred,
    fohh_site_default_endpoint_reachable,
    fohh_site_default_endpoint_not_reachable,
    fohh_site_default_endpoint_preferred,
    fohh_site_disabled,
    fohh_site_force_switch_to_sitec,
    fohh_site_force_switch_to_default_endpoint,
};

enum fohh_site_connection_endpoint_type {
    fohh_connection_on_we_dont_care = 0,
    fohh_connection_on_default_endpoint,
    fohh_connection_on_sitec,
};

enum fohh_switch_connection {
    fohh_switch_connection_dont_care = 0,
    fohh_switch_connection_to_sitec,
    fohh_switch_connection_to_cloud,
};

/*
 * There can be broadly two categories of elements in the fohh queue.
 * a. Mission Critical - It MUST not be dropped whenever possible. They have to make it to the other end for the
 * system to behave as expected.
 * b. others - it either have to make it in time to the other end or get dropped in the source. There is no use in
 * it making it to the other side late.
 *
 * We are going to have different element type going into the __same__ fohh_queue. This will mean all the data are
 * synchronous between sender and receiver as the data ordering is maintained. At the same time, we can apply different
 * queuing config (like max size) for each different element type - for example we have a smaller
 * max_amount_of_control data in comparision to max_amount_of_mission_critical_data.
 */
enum fohh_queue_element_type {
    fohh_queue_element_type_data,
    fohh_queue_element_type_min_valid = fohh_queue_element_type_data,
    fohh_queue_element_type_control,
    fohh_queue_element_type_mission_critical,
    fohh_queue_element_type_max_valid = fohh_queue_element_type_mission_critical,
};

/*
 * You can get an ephemeral reference to this status from a connection
 * object None of these fields should be trusted if err == 0.
 *
 * This information is generally most valid on connection
 * callback. (connected/err)
 */
 // NUM_ERRORS is defined as 3 in libevent put_error
 #define FOHH_MAX_SSL_ERRORS 3
struct fohh_ssl_status {
    char err_subject[256];
    char err_issuer[256];
    const char *x509_err;
    int x509_err_depth;
    int err;
    int dont_verify;
    // ssl errors  as it comes from OpenSSL
    int num_errs; // 0 < num < FOHH_MAX_SSL_ERRORS
    const char *ssl_state;  // one state only
    const char *ssl_error_str[FOHH_MAX_SSL_ERRORS];
    int ssl_error[FOHH_MAX_SSL_ERRORS];
    char cipher_name[MAX_CIPHER_SUITE_NAME_LEN];
};

extern int64_t fohh_conn_burst_value;
extern struct fohh_global_state fohh_g;
extern struct zpath_allocator libevent_allocator;
extern struct zpath_allocator openssl_allocator;
extern char *fohh_proxy_hostname;
extern uint16_t fohh_proxy_port;
extern const char *fohh_connection_state_strings[];
extern const char *fohh_result_strings[];
extern uint64_t fohh_debug;
extern uint64_t fohh_debug_catch;
#define FOHH_DEBUG_NAMES {                      \
        "queue",                                \
            "conn",                             \
            "serialize",                        \
            "resolver",                         \
            "http_pp",                          \
            "http_server",                      \
            "http_parser",                      \
            "quiet",                            \
            "log",                              \
            "log_detail",                       \
            "autotune",                         \
            "ssl_err",                          \
            "fconn_usage",                      \
            "fconn_burst",                      \
            "site",                             \
            "fproxy",                           \
            "latency",                          \
            NULL                                \
            }

/* FOHH automatically creates an fohh_ssl_ex_index. It is generally
 * used to link ssl state to fohh_connections. But they're global, and
 * if you want to use an index and you're not doing fohh... */
extern int fohh_ssl_ex_index;

struct fohh_sni_server;
struct fohh_connection;
struct fohh_thread;

/* pktinfo is the additional info that the caller has requested
 * Struct are defined in fohh.c for encapsulation
 * Caller must not reference private fohh structures directly
 *
 * Struct refered by other libs are public are defined here
 * */
struct pktinfo_s {
    unsigned char *pkt;
    int  pkt_len;
};


#define FOHH_DEBUG

/* Max length of fohh user debug string */
#define FOHH_USER_DEBUG_STR_LEN     256

/* fohh connection description type */
enum fohh_conn_describe_type {
    fohh_conn_describe_type_err,
    fohh_conn_describe_type_small,
    fohh_conn_describe_type_stats,
    fohh_conn_describe_type_full,
    fohh_conn_describe_type_debug
};

/*
 * fohh_libevent_init
 *
 * This is just an initialization routine for libevent. If you do not
 * initialize via some other globabl mechanism, then this
 * initialization code can be used. If you do your initialization
 * elsewhere, be sure to tell libevent to be MP safe.
 *
 * event_log can be NULL, otherwise it is used to log libevent version.
 */
int fohh_libevent_init(struct argo_log_collection *event_log);

/*
 * fohh_ssl_init is simply an SSL initialization routine. Most
 * applications initialize openssl once, then spawn off threads,
 * etc. If no other SSL initialization is present, this routine will
 * initialize openssl with multithreading enabled.
 *
 * This routine should not be called if your application already
 * initialized openssl in multithreading mode.
 *
 * Otherwise, feel free to use this one. It has no fohh dependencies,
 * if that makes you feel better. (Other than error return codes-
 * non-zero being error.)
 *
 * event_log can be NULL, otherwise it is used to log libevent version.
 *
 * fips_mode - Pass 1/0 to enable/disable fips.
 */
int fohh_ssl_init(struct argo_log_collection *event_log, int fips_mode);

/*
* Must be called before any openssl memory oparation, otherwise it does not set the allocators
*/
int init_openssl_allocator();
int is_openssl_allocator();

/*
 * It is *HIGHLY* recommended that you use this SSL verification
 * callback when configuring your SSL context.
 *
 * This verification callback allows more intelligent errors to be
 * communicated about why SSL connection setup doesn't work. In
 * particular, it conveys what certificates errored when a connection
 * fails...
 *
 * NOTE: This ssl_verify_callback is only suitable for using with a
 * context that is used exclusively for FOHH.
 *
 * Furthermore, this verify callback is kept up to date with respect
 * to ensuring sufficiently strong keys are used in certificate
 * chains.
 */
int fohh_ssl_verify_callback(int preverify_ok, X509_STORE_CTX *ctx);

/*
 * This is a synchronous function, and it can block for a short period
 * of time- it reads certificate files. The certificate files/keys are
 * only used for client connections. The Server keys are initialized
 * independently via fohh_server_create.
 *
 * Note: If fohh_init fails, it leaks memory. It's a catastrophic
 * error in this case anyway, so that's okay. (This can be fixed- just
 * takes time)
 *
 * thread_count - How many worker threads to have available for
 *    processing traffic received via fohh. The fohh clients/servers
 *    are distributed amongst these threads. There must be one, and at
 *    most FOHH_MAX_THREADS threads.
 *
 * fully_disable_ssl - If set, then FOHH will never, under any
 *    circumstances use or attempt to use SSL. This configuration will
 *    override any client or server attempts to use SSL. All requests
 *    for servers/clients using SSL will succeed silently- they just
 *    will not use SSL. This functionality is intended to allow
 *    unencrypted access (via localhost) to a local proxy rather than
 *    over an external network. It should be used with extreme
 *    caution.
 *
 * service_prefix - NULL okay - If set, then this prefix will be
 *    prepended to any service destination a FOHH client attempts to
 *    connect to. i.e. asking fohh_client_create to connect to a
 *    remote_host_name of "service.name.com" with a "service_prefix"
 *    of "beta" would have fohh_client try to connect to
 *    "beta.service.name.com". If the "remote_host_name" is a numeric
 *    IP address, then the service prefix is ignored.
 *
 * root_cert_file_name - Trusted root certificate file.
 *
 * my_cert_file_name - Certificate file for this machine.
 *
 * my_private_key_file_name - Private key file for my_cert_file_name
 *
 * my_private_key_mem - On-memory private key for my_cert_file_name
 *
 * provide_locking - Ensures mutual exclusion to fohh_connections.
 *
 * max_seconds_watchdog - Maximum time an FOHH thread can be busy
 *    before the system thinks it is deadlocked.
 *
 * use_localhost_resolution - If set, will prefer localhost resolution
 *    over resolution specified in /etc/resolv.conf
 */
int fohh_init(int thread_count,
              int fully_disable_ssl,
              char *service_prefix,
              char *root_cert_file_name,
              char *my_cert_file_name,
              char *my_private_key_file_name,
              EVP_PKEY *my_private_key_mem,
              int provide_locking,
              int max_seconds_watchdog,
              int use_localhost_resolution);
extern int g_fohh_openssl_err_override; /* see ET-34757 */

/*
 * Processes argv/argc, looking for singular arguments of the
 * following two forms:
 *
 * -fohh-pool=NAME=N,M : Create worker pool named "NAME" comprising
 *                       threads N through M (inclusive). Thread
 *                       numbers start at 0.
 *
 * -fohh-pin=A,B=X,Y : Assign threads A through B (inclusive) to CPU X
 *                     through Y (inclusive). CPU numbers start at 0.
 *
 * This routine *removes* the argument from argv.
 *
 * It is required to call this routine before initializing FOHH.
 */
int fohh_worker_pool_parse_args(int *argc, char *argv[]);

/*
 * Create or update the specified worker pool, comprising the
 * specified fohh threads. Returns an error if the threads are out of
 * bounds.
 *
 * Uninitialized or uncreated pools use the entire worker set.
 */
int fohh_worker_pool_create_or_update(char *name, int first_thread, int last_thread);

/*
 * Pin the specified fohh threads to the specified cores. Returns
 * error if either threads or cores is out of bounds
 */
int fohh_worker_pin(int first_thread, int last_thread, int first_core, int last_core);

/*
 * Return 1 if the specified pool exists, 0 if it does not
 */
int fohh_worker_pool_exists(const char *name, int *first_thread, int *last_thread);

int fohh_get_fohh_global_state_args_count();

/*
 * If you wish to change how the default global fohh context runs
 * after starting, this is the routine for you.
 */
int fohh_reset_global_ssl_ctx(char *root_cert_file_name,
                              char *my_cert_file_name,
                              char *my_private_key_file_name,
                              EVP_PKEY *my_private_key_mem,
                              int verify_peer);
int fohh_reset_client_cipher_global_ssl_ctx();

/*
 * Globally configure whether or not FOHH connections should ever send
 * status requests/responses. Note that this is for disabling sending
 * status requests. It does not prevent responding to them. Nor does
 * this enable sending status requests if an individual client/server
 * connection is configured to be quiet originally.
 *
 * This routine's purpose is primarily to reduce periodic network
 * traffic for debugging purposes.
 */
void fohh_set_quiet(int quiet);


/*
 * thread call function- This call is executed in the context of an
 * FOHH thread as specified by fohh_thread_call().
 *
 * 'thread' is used for internal callbacks that use this interface-
 * other systems probably do not need it.
 */
typedef void (fohh_thread_call_f)(struct fohh_thread *thread, void *cookie, int64_t int_cookie);

/*
 * Call the specified function in the context of the FOHH thread
 * thread_num. This is an asynchronous call- the call to func may
 * occur before or arbitrarily after fohh_thread_call returns.
 *
 * If the receiving thread is seriously overloaded with jobs to
 * perform, then this call may return FOHH_RESULT_WOULD_BLOCK. It is
 * the responsibility of the caller to deal with this.
 */
int fohh_thread_call(int thread_num, fohh_thread_call_f *func, void *cookie, int64_t int_cookie);

int fohh_thread_call_zevent(int thread_num, zevent_call_f *func, void *cookie, int64_t int_cookie);
int fohh_thread_call_big_zevent(int thread_num, zevent_big_call_f *func, void *cookie1, int64_t int_cookie,
                                void *cookie2, void *cookie3);


/*
 * Some fohh dump routines allow (require) an external function to be
 * specified for logging
 */
typedef void (fohh_external_log_f)(void *cookie, const char *format, ...);

/*
 * Connection callback. This callback is called when a connection
 * changes state- either client or server.
 *
 * This routine is called with the connection lock held.
 *
 * For newly accepted connections to a server, the first callback is
 * fohh_connection_connecteding, When a connection to a server is
 * broken/interrupted/etc, this routine is called with
 * fohh_connection_deleted.
 *
 * Note that argo state is completely recreated when argo is
 * reconnected. For this reason, when a connection is established, all
 * argo state must be re-registered.
 *
 * For client connections, almost all states except
 * fohh_connection_deleted will be experienced.
 */
typedef int (fohh_connection_callback_f)(struct fohh_connection *connection,
                                         enum fohh_connection_state state,
                                         void *cookie);

/*
 * connection_unblock_f: This callback is called when a connection
 * that has previously reported a block_of_element_type has dequeued enough traffic to
 * accept more data of that particular element type. It is conceivable that this routine will be
 * called multiple times successively, and the callback should be able
 * to deal with this gracefully.
 */
typedef int (fohh_connection_unblock_f)(struct fohh_connection *connection,
                                        enum fohh_queue_element_type element_type,
                                        void *cookie);

/*
 * connection_ack_f: This callback is called for HTTP client
 * connections only, to acknowledge each successful transmission once
 * acknowledgement is received from the remote system.
 */
typedef int (fohh_connection_ack_f)(struct fohh_connection *connection,
                                    int64_t ack_sequence,
                                    void *cookie);

/*
 * connection_sanity_f: This callback is called for when the connection is declared to be not good.
 */
typedef int (fohh_connection_sanity_f)(struct fohh_connection *connection);

/*
 * tlv_callback: This callback is called for any TLV that is received
 * on an FOHH connetion in tlv mode, with the exception of value = 0,
 * which is an indication of JSON data that is passed through the
 * normal JSON deserialization process.
 *
 * value_length is the length of the *value data. It does not include
 * the length of the tag or the length of the 'value_length' itself.
 */
typedef int (fohh_connection_tlv_f)(struct fohh_connection *connection,
                                    void *cookie,
                                    int32_t tag,
                                    int32_t data_length,
                                    struct evbuffer *data);

/*
 * For FOHH server connections only. This callback is called when the
 * server finally knows what version of argo (underlying transport) is
 * used by the peer. Before this callback, all transmissions will
 * simply fail and drop silently.
 */
typedef void (fohh_connection_version_done_f)(struct fohh_connection *connection,
                                              void *cookie);

/*
 * For FOHH server connection only. This callback is called when the server
 * receive fohh_info message from peer. Any redirect decision can be made
 * in the callback.
 */
typedef void (fohh_connection_info_cb_f)(struct fohh_connection *connection,
                                         void *cookie);

/*
 * This callback is used to verify the alt_cloud information sent in fohh_info
 */
typedef void (fohh_connection_verify_info_cb_f)(struct fohh_connection *connection,
                                                const char **capabilities,
                                                int ccapabilities_count,
                                                char *current_alt_cloud);

/*
 * Create a fohh client.
 *
 * stats_name - optional - If specified, logs stats to specified name.
 *
 * encoding - The argo encoding to use when sending messages.
 *
 * style - What style of client connection to create- either ARGO or HTTP.
 *
 * quiet - whether or not to send status keepalive messages.
 *
 * cookie - Cookie passed back with every callback.
 *
 * callback - Connection status callback (connected, disconnected, etc)
 *
 * tlv_callback - If in TLV mode, this is the TLV callback.
 *
 * unblock_callback - Called when the client is requesting more data
 *    to send after previously blocking the user.
 *
 * data_ack - Optional; only valid for HTTP clients; callback
 *    acknowledging each item sent to the remote system. (Only HTTP
 *    has direct acknowledgement)
 *
 * remote_host_name - the domain name of the host to which to
 *    connect. Note that the remote_host_name may be modified (have a
 *    prefix applied) based on fohh_init configuration.
 *
 * sni_service_name - The name that should be placed in the SNI host
 *    field for the TLS connection.
 *
 * sni_suffix - (optional) cloud name suffix for the client connection,
 *    only needs to be set for alternate cloud redirect purposes
 *
 * service_port_ne - The TCP port to connect to, in network byte
 *    order.
 *
 * SSL_CTX - If NULL, then uses the default client context (which was
 *     initialized as part of FOHH_INIT) If non-null, represents the
 *     client context used for connecting.
 *
 * use_ssl - Whether or not the client shout attempt to use SSL for
 *    this connection. Note that fohh_init can make it impossible to
 *    use SSL. (This routine will not 'fail' in that case- it will
 *    just not use ssl even if it was asked to)
 *
 * The system attempts to resolve and connect to domain_name
 * immediately.
 */
struct fohh_connection *fohh_client_create(char *worker_pool,
                                           const char *stats_name,
                                           enum argo_serialize_mode encoding,
                                           enum fohh_connection_style style,
                                           int quiet,
                                           void *cookie,
                                           fohh_connection_callback_f *callback,
                                           fohh_connection_tlv_f *tlv_callback,
                                           fohh_connection_unblock_f *unblock_callback,
                                           fohh_connection_ack_f *data_ack,
                                           const char *remote_host_name,
                                           const char *sni_service_name,
                                           const char *sni_suffix,
                                           uint16_t service_port_ne,
                                           SSL_CTX *ssl_ctx,
                                           int use_ssl,
                                           int rx_data_timeout_s);

struct fohh_connection *fohh_client_create_2(char *worker_pool,
                                             const char *stats_name,
                                             enum argo_serialize_mode encoding,
                                             enum fohh_connection_style style,
                                             int quiet,
                                             void *cookie,
                                             fohh_connection_callback_f *callback,
                                             fohh_connection_tlv_f *tlv_callback,
                                             fohh_connection_unblock_f *unblock_callback,
                                             fohh_connection_ack_f *data_ack,
                                             const char *remote_host_name,
                                             const char *sni_service_name,
                                             const char *sni_suffix,
                                             uint16_t service_port_ne,
                                             SSL_CTX *ssl_ctx,
                                             int use_ssl,
                                             int rx_data_timeout_s,
                                             int ignore_service_prefix);

/* fohh_client_create_3 add server cert validation flag */
struct fohh_connection *fohh_client_create_3(char *worker_pool,
                                             const char *stats_name,
                                             enum argo_serialize_mode encoding,
                                             enum fohh_connection_style style,
                                             int quiet,
                                             void *cookie,
                                             fohh_connection_callback_f *callback,
                                             fohh_connection_tlv_f *tlv_callback,
                                             fohh_connection_unblock_f *unblock_callback,
                                             fohh_connection_ack_f *data_ack,
                                             const char *remote_host_name,
                                             const char *sni_service_name,
                                             const char *sni_suffix,
                                             uint16_t service_port_ne,
                                             SSL_CTX *ssl_ctx,
                                             int use_ssl,
                                             int rx_data_timeout_s,
                                             int ignore_service_prefix,
                                             int validate_server_cert_san // <---  new parameter
                                             );

struct fohh_connection *
fohh_client_create_on_thread(int fohh_thread_id,
                            const char *stats_name,
                            enum argo_serialize_mode encoding,
                            enum fohh_connection_style style,
                            int quiet,
                            void *cookie,
                            fohh_connection_callback_f *callback,
                            fohh_connection_tlv_f *tlv_callback,
                            fohh_connection_unblock_f *unblock_callback,
                            fohh_connection_ack_f *ack_callback,
                            const char *remote_host_name,
                            const char *sni_service_name,
                            const char *sni_suffix,
                            uint16_t service_port_ne,
                            SSL_CTX *ssl_ctx,
                            int use_ssl,
                            int rx_data_timeout_s,
                            int ignore_service_prefix);

/*
 * Update the remote host an f_conn will connect to. This is best done
 * in response to a connection failure/disconnect
 */
void fohh_update_remote_host(struct fohh_connection *f_conn, const char *new_remote_host);

/*
 * Look up a fohh client. Yeah, this assumes one connection per domain.
 */
struct fohh_connection *fohh_client_get(char *domain_name);


/*
 * Callback to be made by fohh_server to find an SSL context for
 * servicing the inbound connection.
 *
 * sni is the full sni.
 *
 * sni_root is the matched sni from an sni dispatching server
 * connection, or NULL.
 *
 * Should return error on error, no_error if ssl_ctx is set, or
 * FOHH_RESULT_ASYNCHRONOUS if something needs to be fetched in order
 * to handle the connection.
 */
typedef int (fohh_ssl_ctx_get_f)(struct fohh_connection *f_conn,
                                 const char *sni,
                                 const char *sni_root,
                                 SSL_CTX **ssl_ctx);


/*
 * Callback to be made by fohh_server to validate a connection. (If so
 * configured). Note that, if SSL, the connection has already passed
 * certificate checks. This further check is intended to check
 * certificate revocation, etc.
 *
 * Should return any error on error (reject conn), no_error if the
 * connection passes, or FOHH_RESULT_ASYNCHRONOUS if something needs
 * to be fetched in order to validate the connection.
 *
 * On return from asynchronous call, the asynchronous event can call
 * fohh_connection_process to resume the connection state
 * machine. (Which will probably just call this routine again)
 */
typedef int (fohh_connection_verify_f)(struct fohh_connection *f_conn);

/*
 * Callback made to do an optional post validation step
 * This function is meant to do any generic checks
 * First use case is as a hook to allow for doing a region check across
 * many different server types/connection types.
 */
typedef int (fohh_connection_post_verify_f)(struct fohh_connection *f_conn);


typedef int (fohh_generic_server_pre_accept_f)(struct sockaddr_storage *sa, const char *sni, size_t sni_len, void *cookie);
/*
 * Callback for when a generic server accepts a connection.
 *
 * bev will only be set if the connection was set up to do TLS as a
 * result of having an SSL context callback. (see
 * fohh_generic_server_register_accept)
 */
typedef void (fohh_generic_server_accept_f)(struct fohh_thread *f_thread,
                                            int sock,
                                            struct sockaddr *sa,
                                            int sa_len,
                                            const char *sni,
                                            const char *sni_suffix,
                                            void *cookie,
                                            void *bev,
                                            struct pktinfo_s *pktinfo);

/*
 * Callback to free fohh_generic_server cookie.
 */
typedef void (fohh_generic_server_cookie_free_cb)(void *cookie);

/*
 * Create a fohh server.
 *
 * quiet - Whether or not FOHH attempts to send status messages
 *   in-stream.
 *
 * encoding - What encoding to use on transmission.
 *
 * style - What style of connection it is. argo and argo_tlv styles
 *   allowed. http server is not valid, and will be rejected.
 *
 * cookie - Passed along with callback for new connections.
 *
 * callback - Callback that is called when connections come and go.
 *
 * tlv_callback - Callback with TLV data, if this is a TLV server.
 *
 * unblock_callback - Callback that is called when the source can send
 *   this FOHH instance more data after previously being blocked.
 *
 * argo_callback - Callback that is called with argo objects that have
 *   never been registered for their own callbacks
 *
 * bind_addr - OPTIONAL - The address (IPv4 or IPv6) to bind.
 *   INADDR_ANY allowed. Set port correctly for the port upon which
 *   you wish to listen. This field is required if you are not using
 *   the generic FOHH connection dispatcher (fohh_generic_*). This
 *   field must be NULL if you are using the generic FOHH connection
 *   dispatcher.
 *
 * bind_addr_len - The length of the sockaddr. (different for IPv4 and
 *   IPv6, of course)
 *
 * root_cert_file_name - CA cert. If NULL, will copy initialization
 *   context, and the other two file names will be ignored.
 *
 * my_cert_file_name - server certificate.
 *
 * my_cert_key_file_name - server private key.
 *
 * require_client_cert - Whether or not we require a client
 *   certificate for inbound connections.
 *
 * use_ssl - Whether or not the service will use SSL. Note that
 *   fohh_init can make it impossible to use SSL.
 *
 * ssl_ctx_callback - Optional - If set, then this routine is called
 *   with SNI information in order to get the SSL context to use for
 *   the server side of the connection.
 *
 * verify_callback - Optional - If set, then once the connection is
 *   set up, (SSL complete, if an SSL connection), then this callback
 *   is called before any data is processed. This data can verify the
 *   authenticity of the connection in any manner beyond normal SSL
 *   verification. (Asynchronous cert revocation check, whatever)
 *
 * accept_binary_argo - If set, then the FOHH will allow binary argo
 *   to be received. If not, then FOHH will not allow binary argo to
 *   be received.
 *
 * WARNING WARNING WARNING: This library is designed to never have to
 * deal with a server being destroyed/freed. There's some code to
 * write if this ever becomes necessary.
 */
struct fohh_connection *
fohh_server_create(int quiet,
                   enum argo_serialize_mode encoding,
                   enum fohh_connection_style style,
                   void *cookie,
                   fohh_connection_callback_f *callback,
                   fohh_connection_tlv_f *tlv_callback,
                   fohh_connection_unblock_f *unblock_callback,
                   argo_structure_callback_f *argo_callback,
                   struct sockaddr *bind_addr,
                   int bind_addr_len,
                   char *root_cert_file_name,
                   char *my_cert_file_name,
                   char *my_cert_key_file_name,
                   int require_client_cert,
                   int use_ssl,
                   fohh_ssl_ctx_get_f *ssl_ctx_callback,
                   fohh_connection_verify_f *verify_callback,
                   fohh_connection_post_verify_f *post_verify_callback,
                   int accept_binary_argo,
                   int rx_data_timeout_s);


/*
 * Create a fohh server.
 *
 * quiet - Whether or not FOHH attempts to send status messages
 *   in-stream.
 *
 * encoding - What encoding to use on transmission.
 *
 * style - What style of connection it is. argo and argo_tlv styles
 *   allowed. http server is not valid, and will be rejected.
 *
 * cookie - Passed along with callback for new connections.
 *
 * callback - Callback that is called when connections come and go.
 *
 * tlv_callback - Callback with TLV data, if this is a TLV server.
 *
 * unblock_callback - Callback that is called when the source can send
 *   this FOHH instance more data after previously being blocked.
 *
 * argo_callback - Callback that is called with argo objects that have
 *   never been registered for their own callbacks
 *
 * bind_addr - OPTIONAL - The address (IPv4 or IPv6) to bind.
 *   INADDR_ANY allowed. Set port correctly for the port upon which
 *   you wish to listen. This field is required if you are not using
 *   the generic FOHH connection dispatcher (fohh_generic_*). This
 *   field must be NULL if you are using the generic FOHH connection
 *   dispatcher.
 *
 * bind_addr_len - The length of the sockaddr. (different for IPv4 and
 *   IPv6, of course)
 *
 * root_cert_file_name - CA cert. If NULL, will copy initialization
 *   context, and the other two file names will be ignored.
 *
 * my_cert_file_name - server certificate.
 *
 * my_cert_key_file_name - server private key.
 *
 * my_cert_key_mem - server private key mem.
 *
 * require_client_cert - Whether or not we require a client
 *   certificate for inbound connections.
 *
 * use_ssl - Whether or not the service will use SSL. Note that
 *   fohh_init can make it impossible to use SSL.
 *
 * ssl_ctx_callback - Optional - If set, then this routine is called
 *   with SNI information in order to get the SSL context to use for
 *   the server side of the connection.
 *
 * verify_callback - Optional - If set, then once the connection is
 *   set up, (SSL complete, if an SSL connection), then this callback
 *   is called before any data is processed. This data can verify the
 *   authenticity of the connection in any manner beyond normal SSL
 *   verification. (Asynchronous cert revocation check, whatever)
 *
 * accept_binary_argo - If set, then the FOHH will allow binary argo
 *   to be received. If not, then FOHH will not allow binary argo to
 *   be received.
 *
 * WARNING WARNING WARNING: This library is designed to never have to
 * deal with a server being destroyed/freed. There's some code to
 * write if this ever becomes necessary.
 */


struct fohh_connection*
fohh_server_create_ex(int quiet,
                      enum argo_serialize_mode encoding,
                      enum fohh_connection_style style,
                      void *cookie,
                      fohh_connection_callback_f *callback,
                      fohh_connection_tlv_f *tlv_callback,
                      fohh_connection_unblock_f *unblock_callback,
                      argo_structure_callback_f *argo_callback,
                      struct sockaddr *bind_addr,
                      int bind_addr_len,
                      char *root_cert_file_name,
                      char *my_cert_file_name,
                      char *my_cert_key_file_name,
                      EVP_PKEY *my_client_key_mem,
                      int require_client_cert,
                      int use_ssl,
                      fohh_ssl_ctx_get_f *ssl_ctx_callback,
                      fohh_connection_verify_f *verify_callback,
                      fohh_connection_post_verify_f *post_verify_callback,
                      int accept_binary_argo,
                      int rx_data_timeout_s);



/*
 * Set a callback to call when we know the version of our peer, and
 * are thus safe to send.
 *
 * Would be more clever to block transmit until we know, but this is
 * simpler to implement in the short term, as we don't have output
 * blocking, only input blocking. (We block input by checking our
 * output queue depth- the output queue itself does not block us)
 *
 * This should be called immediately after creating an
 * fohh_server. (Yes, there is a very short race condition there if
 * intializing in a different thread)
 */
void fohh_set_version_callback(struct fohh_connection *f_conn,
                               fohh_connection_version_done_f *cb);

/*
 *  Set a callback to call when we get the fohh connection info
 *  from our peer. This can be used for any redirect to be made.
 */
void fohh_set_info_callback(struct fohh_connection *f_conn,
                            fohh_connection_info_cb_f *cb,
                            fohh_connection_verify_info_cb_f *verify_cb);

/*
 * Set a callback to call whenever we receive a (parseable) object for
 * which there is no callback registered.
 */
void fohh_set_default_argo_callback(struct fohh_connection *connection,
                                    argo_structure_callback_f *argo_callback);

/*
 * Create a connection server that listens and dispatches requests to
 * worker threads.
 *
 * The server supports listeners that work for performing both proxy
 * protocol and SNI.
 *
 * Since the generic server can dispatch inbound requests to different
 * handlers based on SNI, the callback(s) are specified in their own
 * call.
 *
 * do_sni_dispatch means you will probably eventually attach multiple
 * fohh_servers to this connection, differentiated by different SNI
 * headers.
 *
 * SNI identification uses the diamond library for its suffix
 * best-match lookups.
 */
struct fohh_generic_server *fohh_generic_server_create(int do_sni, int32_t log_connection_with_bad_sni);
struct fohh_generic_server *fohh_generic_server_create2(int do_sni, int32_t log_connection_with_bad_sni, int do_ja3);


void fohh_generic_server_close(struct fohh_generic_server *server);
void fohh_generic_server_free(struct fohh_generic_server *server);

/*
 * Get all the SNIs advertised by the generic server. snis points at
 * an array of pre-allocated strings, each with sni_max_len space
 * available.
 *
 * If snis is NULL, then the strings are not written, and only the
 * count is returned.
 *
 * If print_state is set, prints additional state to each SNI,
 * e.g. disabled. Used for debugging.
 */
int fohh_generic_server_get_snis(struct fohh_generic_server *server,
                                 char **snis,
                                 size_t sni_max_len,
                                 size_t *sni_count,
                                 int print_state);

/*
 * Listen to a specific address/port for a generic server. The port
 * can be configured to parse proxy protocol and/or SNI.
 */
int fohh_generic_server_listen(struct fohh_generic_server *server,
                               struct sockaddr *listen_addr,
                               int listen_addr_len,
                               int do_proxy_protocol);

/*
 * Register a specific server connection as listening to a
 * generic_server attachment point.
 *
 * fohh_generic_server - This was created with fohh_generic_server_create.
 *
 * fohh_server_connection - This was created with fohh_server_create.
 *
 * If the server is matching SNI, then the following two are required:
 *
 * domain - The domain which, if matched SNI, will cause this
 *    fohh_server_connection to process it. Domain is standard DNS
 *    form: 'special.service.com', for example. FOHH converts all
 *    domains to all lower-case before comparing.
 *
 * wildcard_prefix - If set, means the domain will be wildcard
 *    matched: '*.special.service.com' will match and go to this
 *    server, as will 'special.service.com'. Note that longest prefix
 *    matching applies- see diamond documentaion for details.
 */
int fohh_generic_server_register(struct fohh_generic_server *fohh_generic_server,
                                 struct fohh_connection *fohh_server_connection,
                                 const char *domain,
                                 int wildcard_prefix,
                                 char *worker_pool);

/*
 * Register a generic socket accept connection as listening to a
 * generic_server attachment point.
 *
 * fohh_generic_server - This was created with fohh_generic_server_create.
 *
 * accept_f - This will be called when a connection arrives matching
 *    the specified domain.
 *
 * accept_cookie - Cookie that will be passed to the accept function.
 *
 * domain - The domain which, if matched SNI, will cause this accept_f
 *    to process the connection. Domain is standard DNS form:
 *    'special.service.com', for example. FOHH converts all domains to
 *    all lower-case before comparing.
 *
 * unregister_accept is the unregistration version.
 */
int fohh_generic_server_register_accept(struct fohh_generic_server *server,
                                        fohh_generic_server_pre_accept_f *pre_accept_f,
                                        fohh_generic_server_accept_f *accept_f,
                                        void *accept_cookie,
                                        fohh_generic_server_cookie_free_cb *cookie_free_cb,
                                        const char *domain,
                                        int wildcard_prefix,
                                        char *worker_pool);
int fohh_generic_server_unregister_accept(struct fohh_generic_server *server,
                                          const char *domain,
                                          int wildcard_prefix);
/* similar to unregister, but dynamically enable/disables a sni */
int fohh_generic_server_set_domain_disabled(struct fohh_generic_server *server,
                                          const char *domain,
                                          int wildcard_prefix,
                                          int disable);
/*
 * Checks if the domain is already present.
 * returns 1 when found and 0 when entry not found
 */
int fohh_generic_server_lookup_domain(struct fohh_generic_server *server,
                                      const char *domain,
                                      int wildcard_prefix);
 /*
 * fohh_generic_server_re_register
 * deregister and Register a specific server connection as listening to a
 *   generic_server attachment point.
 *  This is invoked when a domain(sni) changes
 */
int fohh_generic_server_re_register(struct fohh_generic_server *server,
                                    const char *old_domain,
                                    const char *new_domain,
                                    int wildcard_prefix);

/*
 * Use this call to update the callbacks for a connection. (Handy for
 * accepted connections)
 */
int fohh_update_callback(struct fohh_connection *connection,
                         void *cookie,
                         fohh_connection_callback_f *callback,
                         fohh_connection_unblock_f *unblock_callback,
                         fohh_connection_ack_f *data_ack,
                         fohh_connection_sanity_f *sanity_cb,
                         int64_t sanity_max_allowed_downtime_s);


SSL_CTX *fohh_client_ssl_ctx_create_with_cipher(const char *peer_trusted_root_cert_pem_filename,
                                    const char *client_cert_pem_filename,
                                    const char *client_key_pem_filename,
                                    EVP_PKEY *client_key_mem,
                                    int ciphersuite_index);

/*
 * Create a client SSL context for a client FOHH connection
 */
SSL_CTX* fohh_client_ssl_ctx_create(const char *peer_trusted_root_cert_pem_filename,
                                    const char *client_cert_pem_filename,
                                    const char *client_key_pem_filename,
                                    EVP_PKEY *client_key_mem);

/*
 * Create a client SSL context, trusting normal root CAs. No client
 * certificate used.
 */
SSL_CTX *fohh_web_client_ssl_ctx_create(void);
SSL_CTX *fohh_web_client_ssl_ctx_create_noverify(void);

/*
 * Create a client SSL context, trusting normal root CAs.
 * No client certificate used.
 *
 * min_ssl_ver - minimun supported TLS client version:
 *   min_ssl_ver eq. 3 - TLSv1.1
 *   min_ssl_ver eq. 2 - TLSv1.0
 *   min_ssl_ver eq. 1 - TLSv1.2
 *
 */
#define ZPA_FOHH_USE_MIN_SSL_TLSv1_1 3
#define ZPA_FOHH_USE_MIN_SSL_TLSv1_0 2
#define ZPA_FOHH_USE_MIN_SSL_TLSv1_2 1
SSL_CTX *fohh_waf_client_ssl_ctx_create(int min_ssl_ver);
SSL_CTX *fohh_waf_client_ssl_ctx_create_noverify(int min_ssl_ver);

/*
 * Create a client SSL context, using specific cert, trusting normal root CAs
 * Note that this does not automatically enforce SAN domain check for the ssl_context
 */
SSL_CTX *fohh_web_client_ssl_ctx_create_with_cert(const char *peer_trusted_root_cert_pem_filename,
                                                  const char *client_cert_pem_filename,
                                                  const char *private_key_pem_filename,
                                                  EVP_PKEY *client_key_mem);

SSL_CTX *fohh_web_client_ssl_ctx_create_with_cert_with_cipher(const char *peer_trusted_root_cert_pem_filename,
                                                  const char *client_cert_pem_filename,
                                                  const char *private_key_pem_filename,
                                                  EVP_PKEY *client_key_mem,
                                                  int ciphersuite_index);
/*
 * Destroy a fohh client or server.
 *
 * This closes, very forcefully, all threads and state associated with
 * fohh.
 */
void fohh_connection_release(struct fohh_connection *connection);

/* Use this if we are the server of an fohh connection */
void fohh_connection_delete(struct fohh_connection *f_conn, const char *reason);
/* Use this delete if you are not on the connection's thread */
void fohh_connection_delete_async(struct fohh_connection *f_conn, int64_t incarnation, const char *reason);

/* Use this if we are the client of an fohh connection */
void fohh_connection_disconnect(struct fohh_connection *f_conn, const char *reason) __attribute__((weak));

void fohh_connection_disconnect_async(struct fohh_connection *f_conn, int64_t incarnation, const char *reason);

/* Use this if you never want the client connection to reconnect. This
 * basically makes the connection useless. But we don't yet support
 * truly destroying client connections yet, so this at least keeps us
 * from consuming sockets, logging, etc dealing with it */
void fohh_connection_disable(struct fohh_connection *f_conn, const char *reason);
void fohh_connection_disable_async(struct fohh_connection *f_conn, int64_t incarnation, const char *reason);

void fohh_connection_enable_async(struct fohh_connection *f_conn, int64_t incarnation);


/*
 * Configure fohh connection to monitor the connection for sanity.
 * This was initially designed for assistant control/config connection sanity monitoring, now since we move that logic
 * to monitor thread (ET-22146), this became obsolete.
 *
 * Now this can be customized to achieve multiple sanity checks on the connection, under different scenarios.
 * To reset the sanity check, simply set sanity_callback to 0
 *
 * sanity_callback will be invoked by the state machine.
 * Previously, it was only triggered for unconnected fohh states,
 * but now it will be invoked for all states.
 */
void fohh_connection_monitor_sanity(struct fohh_connection*         f_conn,
                                    fohh_connection_sanity_f        sanity_callback,
                                    int64_t                         max_allowed_downtime_s);

/*
 * If the state is disconnected, return the epoch_s of when the disconnection happened. Otherwise 0.
 */
int64_t fohh_conn_get_current_disconnected_time_s(struct fohh_connection *f_conn);

/*
 * If the state is disconnected, return the number of seconds passed for the last disconnection. Otherwise 0.
 */
int64_t fohh_conn_get_current_disconnect_duration_s(struct fohh_connection *f_conn);

/*
 * Get the argo state for this fohh object- either transmit or receive
 * argo state.
 *
 * This state state can be used to register structures, etc.
 *
 * You should NEVER use the argo state for any purpose other than
 * registering structure descriptions. All serialization and
 * deserialization occurs through fohh.
 */
struct argo_state *fohh_argo_get_tx(struct fohh_connection *connection);
struct argo_state *fohh_argo_get_rx(struct fohh_connection *connection);


/*
 * fohh_argo_serialize
 *
 * Serialize data through fohh+argo. Transmitting via argo is designed
 * to ALWAYS succeed with respect to buffering. fohh/argo will simply
 * keep allocating/queueing up more memory, buffering away. However,
 * that's not a particularly happy scenario. Therefore, one of the
 * response types returned by this serializer is "wouldblock". This is
 * an indication that there is too much data queued up in front of our
 * TX socket, and should be used as a hint by the user of this
 * interface. (note there is an unblock callback to trigger resumption
 * of transmission)
 *
 * fohh_connection - The connection upon which to serialize.
 *
 * description - argo structure description.
 *
 * data - pointer to the structure described by 'description'
 *
 * sequence - Opaque - value returned (for HTTP clients only) when
 *     sent data has been acknowledged by remote system.
 *
 * Returns:
 *
 * FOHH_RESULT_NO_ERROR on success
 * FOHH_RESULT_WOULD_BLOCK if serialize fails because it would block.
 * FOHH_RESULT_* on some other failure.
 *
 * NOTE: the _synchronous versions of these routines is experimental,
 *    and skips the asynchrnous (cross thread/queueing) system used to
 *    keep thread access to a single FOHH connection on a single
 *    thread.
 */
int fohh_argo_serialize(struct fohh_connection *connection,
                        struct argo_structure_description *description,
                        void *data,
                        int64_t sequence,
                        enum fohh_queue_element_type elem_type);
int fohh_argo_serialize_object(struct fohh_connection *connection,
                               struct argo_object *object,
                               int64_t sequence,
                               enum fohh_queue_element_type elem_type);
int fohh_argo_serialize_synchronous(struct fohh_connection *connection,
                                    int64_t connection_incarnation,
                                    struct argo_structure_description *description,
                                    void *data,
                                    int64_t sequence);
int fohh_argo_serialize_object_synchronous(struct fohh_connection *connection,
                                           int64_t connection_incarnation,
                                           struct argo_object *object,
                                           int64_t sequence);
/* Send the argo version header. This is sent automatically in the
 * binary argo case, but for argo_tlv format, you must call this
 * routine directly */
int fohh_argo_send_version(struct fohh_connection *connection);


/*
 * fohh_tlv_send
 *
 * Send the specified TLV via FOHH. Note that this routine can fail in
 * the exact same manner as fohh_argo_serialize. This routine copies
 * the full TLV.
 *
 * The value+length is split in two- the caller has the option of
 * allowing this routine to combine two buffers into one. (Only one is
 * required. Two are available for convenience)
 *
 * It allocates an argo_object in a manner such that
 * argo_object_release, etc, works. (FOHH's queueing mechanism uses
 * argo_objects- thus the reason for the overloading)
 *
 * The overloading is recognized via negative object indexes which
 * represent the tag. The rest of the information in the argo_object
 * is fairly correct.
 *
 * tag can be any positive nonzero value that fits in int32_t.  length
 * is the length of the value only, not including the 'tag' and the
 * 'length'. i.e. a one byte value would have a length of 1, while the
 * whole 'TLV' would have a length of 9.
 */
int fohh_tlv_send(struct fohh_connection *connection,
                  int32_t tag,
                  int32_t length1,
                  const void *value1,
                  int32_t length2,
                  const void *value2,
                  enum fohh_queue_element_type elem_type);

/*
 * fohh_tlv_send_raw
 *
 * EXPERIMENTAL
 *
 * Send 'len' bytes of the specified raw evbuffer on the connection.
 *
 * returns FOHH_RESULT_WOULD_BLOCK if there isn't enough room.
 *
 * Can be called from any thread.
 *
 * YOUR RESPONSIBILITY TO SEND INTELLIGENT DATA. (Probably only useful
 * for sending TLV's from evbuffers... at least that's the experiment)
 *
 * You must send one full TLV- Tag + Length + Value-data within data.
 *
 * This routine only drains the buffer on success.
 *
 * returns:
 *
 * FOHH_RESULT_NO_ERROR - success. (buffer consumed)
 * FOHH_RESULT_WOULD_BLOCK - Can't send, would block.
 * FOHH_RESULT_CANT_SEND - Connection probably closed.
 * FOHH_RESULT_* - some other error.
 */
int fohh_tlv_send_raw(struct fohh_connection *connection,
                      int64_t connection_incarnation,
                      struct evbuffer *data,
                      size_t len,
                      uint8_t *block_fohh,
                      uint8_t *block_evbuf);


/* Check if this FOHH is a client (originated by us) connection. */
enum fohh_connection_type fohh_type(struct fohh_connection *connection);

/* Get the current state of the connection. */
enum fohh_connection_state fohh_get_state(struct fohh_connection *connection);

/* Get the thread ID for an fohh_connection */
int fohh_connection_get_thread_id(struct fohh_connection *connection);

uint64_t fohh_connection_get_nr_switch_to_sitec(struct fohh_connection *connection);
uint64_t fohh_connection_get_nr_switch_to_broker(struct fohh_connection *connection);

/* Get the event_base for an FOHH thread. */
struct event_base *fohh_get_thread_event_base(int thread_num);

/* Get thread from thread num */
struct fohh_thread *fohh_get_thread(int thread_num);

/* Get the event_base for the _current_ FOHH thread. */
struct event_base *fohh_get_current_thread_event_base();

/* Get the thread_id (not pthread_id) for the _current_ FOHH thread. */
int fohh_get_current_thread_id();

/* Check if the _current_ thread is an FOHH thread */
int is_current_thread_fohh_thread();

/* Check if the _current_ thread is the given FOHH thread */
static inline int is_current_thread_the_same_fohh_thread(int fohh_thread_id)
{
    return is_current_thread_fohh_thread() && fohh_get_current_thread_id() == fohh_thread_id;
}

/*
 * DEPRECATED FOR EXTERNAL USE
 *
 * Get the next thread ID to which to assign a new socket.
 */
int fohh_next_thread_id(void);

/*
 * Get the next thread ID to use, based on a pool name. Should always
 * be used in place of fohh_next_thread_id, which is being slowly
 * deprecated
 */
int fohh_worker_pool_get_thread_id(char *name);

/* Get the totoal number of fohh threads */
int fohh_thread_count(void);

/* Get the next thread ID for creating an accept socket. */
int fohh_next_accept_thread_id(void);

/* Get the thread_id of a fohh_thread */
int fohh_thread_get_id(struct fohh_thread *thread);

/* Get a description of the connection. */
char *fohh_description(struct fohh_connection *connection);

/* Get the state of the connection. */
const char *fohh_state(struct fohh_connection *connection);

/* Get the CN of the connection peer. NULL if not SSL or peer CN not
 * available */
char *fohh_peer_cn(struct fohh_connection *connection);
/* Get the peer cert in pem format */
int fohh_peer_cert_pem(struct fohh_connection *connection, char *out_txt, size_t out_txt_len);

/* Get/Set 64 bit ID representing the connection peer. */
void fohh_peer_set_id(struct fohh_connection *connection, int64_t id);
int64_t fohh_peer_get_id(struct fohh_connection *connection);

/* Get/Set 64 bit Aux ID related to the connection peer. */
void fohh_peer_set_aux_id(struct fohh_connection *connection, int64_t aux_id);
int64_t fohh_peer_get_aux_id(struct fohh_connection *connection);
/* Get/Set quickack config related to socket option during read */
void fohh_peer_set_quickack_read_config (struct fohh_connection *connection);
int fohh_peer_get_quickack_read_config (struct fohh_connection *connection);
void fohh_peer_set_libevent_low_write_watermark_config(struct fohh_connection *connection);

/* Get/Set preloaded state of a connecting customer */
void fohh_set_preloaded_state(struct fohh_connection *connection, int state);
int fohh_get_preloaded_state(struct fohh_connection *connection);

/* Turn on fohh debugging for this connection. Just for TLV at the
 * moment- dumps all tx/rx TLVs */
void fohh_set_debug(struct fohh_connection *connection, int value);

/* Turn on stickiness for this connection. Stickiness is a client only
 * function- it will keep a connection open even if name resolution
 * completely changes. If not sticky, the connection will close and
 * reconnect as name resolution changes.
 * Set to 1 (sticky) or 0 (not sticky) */
void fohh_set_sticky(struct fohh_connection *connection, int value);

/* Set the address containing the interval in seconds for sending fohh_status_request messages on the connected peer */
void fohh_set_status_interval(struct fohh_connection *connection, int64_t* status_interval);

/*
 * To disable all the non-debug level logging that is done when a connection goes down, up, retried et all.
 */
void fohh_suppress_connection_event_logs(struct fohh_connection *connection);

/* Handy routine for retrieving CN from cert */
int fohh_x509_get_cn(X509 *cert, char *buf, size_t buf_len);
/* Handy routine for retrieving Organization Name from cert */
int fohh_x509_get_on(X509 *cert, char *buf, size_t buf_len);

int fohh_x509_get_not_after(X509 *cert, char *buf, size_t buf_len);
void fohh_x509_serial(X509 *x509, char * serial, size_t len);
void fohh_x509_cert(X509 *xi, char *buf, size_t len);
int fohh_ssl_ctx_print(const SSL_CTX *ctx, char *buf, size_t len);

/* Get the peer certificate. Caller is responsible for calling
 * X509_free on the returned value. */
X509 *fohh_peer_cert(struct fohh_connection *connection);

/* Get the IP addresses of the connection host talking to us. */
void fohh_connection_address(struct fohh_connection *connection, struct argo_inet *remote_ip, struct argo_inet *local_ip);

/* Get the IP addresses and NETWORK ENDIAN port of the host talking to us. */
void fohh_connection_address_and_port(struct fohh_connection *connection, struct argo_inet *remote_ip, uint16_t *remote_port_ne, struct argo_inet *local_ip, uint16_t *local_port_ne);

/* Get the incarnation of the connection */
int64_t fohh_connection_incarnation(struct fohh_connection *connection);

int fohh_connection_is_alt_cloud_aware(struct fohh_connection *connection);
int fohh_connection_is_alt_cloud_redirect_needed(struct fohh_connection *connection);
void fohh_connection_set_alt_cloud_aware(struct fohh_connection *connection, int val);
void fohh_connection_set_redirect_for_alt_cloud_change(struct fohh_connection *connection, int val);

/*
 * Dynamic cookie accessors. This can be used by fohh consumers to
 * allocate their own private state associated with the connection. It
 * is the responsibility of the accessor to ensure that any allocated
 * space is freed when the connection goes away.
 */
void *fohh_connection_get_dynamic_cookie(struct fohh_connection *connection);
void fohh_connection_set_dynamic_cookie(struct fohh_connection *connection, void *dynamic_cookie);
void fohh_connection_set_cookie(struct fohh_connection *connection, void *cookie);
void *fohh_connection_get_cookie(struct fohh_connection *connection);

/* Get the fohh version as a string */
const char *fohh_version(void);

/*
 * Get the count of fohh server connections from all fohh threads which are in connected state.
 */
int64_t fohh_get_server_connection_connected_count();

/* Describe all connections. Result could be long... */
/* int_arg is used only for stats (so far), for limiting display to
 * those tunnels that have evbuffer size >= int_arg */
void fohh_connections_describe(char *out_str, size_t out_str_len, enum fohh_connection_type type, const char *filter, enum fohh_conn_describe_type desc_type, int fohh_thread_id, int64_t int_arg);

/*
 * Reset a client connection... Linear search to find it. Returns
 * NOT_FOUND: Not found.
 * NO_ERROR: It was attempted. (It's asynchronous- may not have succeeded)
 * BAD_STATE: Not connected.
 * ERR: Could not make asynchronous attempt
 */
int fohh_connections_reset(const char *domain, const char *port, enum fohh_connection_type type, int limit, fohh_external_log_f *func, void *func_cookie);


/*
 * Run the socket buffer tuning algorithm on the specified connection
 *
 * Returns no_error on success, any other error indicates an
 * error. (You will get more data out of actual event logs...)
 */
int fohh_connection_tune_tx_sockbuf_by_name(const char *domain, uint16_t port_he);

int fohh_connection_tune_tx_sockbuf(struct fohh_connection *f_conn);

void fohh_set_autotune(struct fohh_connection *f_conn, int enabled, double tuning_factor, double reduction_factor, int max_average, int min_sockbuf);
/*
 * Toggle debug flag for a connection... Linear search to find it. Returns
 * NOT_FOUND: Not found.
 * NO_ERROR: It was attempted. (It's asynchronous- may not have succeeded)
 * BAD_STATE: Not connected.
 * ERR: Could not make asynchronous attempt
 */
int fohh_connections_toggle_debug(const char *domain, uint16_t port_he);

/*
 * Note: There are no deserialize functions. Deserialization results
 * in direct callbacks to functions registered with the argo state
 * contained within FOHH.
 *
 * However, it should be noted that they are called back with the
 * fohh_connection lock held, if locking is enabled.
 */


/*
 * FOHH can optionally call a trigger function when it begins
 * processing data from an IP, and a trigger function when it is done
 * processing data from an IP.
 *
 * This is implemented to allow for triggered IP based debugging- it
 * is not really meant for any other reason.
 */
typedef int (fohh_ip_trigger_f)(struct argo_inet *inet);

int fohh_install_ip_trigger_callback(fohh_ip_trigger_f *trigger_start,
                                     fohh_ip_trigger_f *trigger_end);


/* Mark a connection for reaping. May take a few seconds, etc */
int fohh_connection_reap(struct fohh_connection *connection);

/* Tickle fohh to wake up thread when we have something to send */
void fohh_tickle(struct fohh_connection *f_conn, int check_before_tickle);

/* Cause the connection to run its state machine- reprocess
 * verification, context fetching, killing, post-name-resolution
 * connection, etc */
void fohh_connection_process(struct fohh_connection *connection, int64_t int_cookie);

/*
 * Get SNI from client hello message. Returns 1 on success, 0 on
 * failure
 *
 * NOTE: UNUSUAL RETURN CODES FROM OTHER FOHH ROUTINES- THIS CODE IS
 * FROM SME
 */
int get_sni_from_client_hello(const unsigned char *sp0, int inplen, char *sni_outbuf, int outbuf_maxsz);

/*
 * Get the difference in time between ourself and our peer, in
 * microseconds. Returns 0 if unavailable. This value is
 * 'remote_epoch_us - local_epoch_us'
 */
int fohh_peer_epoch_us_diff(struct fohh_connection *connection, int64_t *diff);
/*
 * Get current rtt in us
 */
uint64_t fohh_connection_get_app_rtt_us(struct fohh_connection *connection);
uint64_t fohh_connection_get_tcp_rtt_us(struct fohh_connection *connection);
/*
 * Get max app&tcp rtt info
 */
uint64_t fohh_connection_get_max_app_rtt(struct fohh_connection *f_conn);
uint64_t fohh_connection_get_max_tcp_rtt(struct fohh_connection *f_conn);
/*
 * Convert max app&tcp rtt info into a string. Format: "tcp_rtt|app_rtt us"
 */
size_t fohh_connection_max_rtt_to_string(struct fohh_connection *f_conn, char *str, size_t max_len);
/*
 * Update max tcp/app rtt.
 */
uint64_t fohh_connection_update_max_app_rtt(struct fohh_connection *connection, uint64_t value);
uint64_t fohh_connection_update_max_tcp_rtt(struct fohh_connection *connection, uint64_t value);
/*
 * Reset max app|tcp rtt.
 */
void fohh_connection_reset_max_rtt(struct fohh_connection *connection);
/* Get current queue depth in # messages */
uint64_t fohh_connection_get_queue_depth(struct fohh_connection *connection);

/*
 * Enable or disable bufferevent read for fohh connections
 */
void fohh_connection_enable_read(struct fohh_connection *f_conn);
void fohh_connection_disable_read(struct fohh_connection *f_conn);

int fohh_connection_get_tcp_throughput(struct fohh_connection *f_conn, uint64_t *thru_put);

int fohh_connection_get_tcp_info(struct fohh_connection *f_conn, struct fohh_tcp_info *info);

char *fohh_connection_get_sni(struct fohh_connection *f_conn);
const char *fohh_connection_get_sni_suffix(struct fohh_connection *f_conn);

uint16_t fohh_connection_get_local_port(struct fohh_connection *f_conn);

int fohh_connection_set_identity_str(struct fohh_connection *f_conn, const char *str);

int fohh_connection_get_stats(struct fohh_connection *f_conn, uint64_t *txb, uint64_t *rxb, uint64_t *txo, uint64_t *rxo,
                              uint64_t *txrtlv, uint64_t *rxrtlv);

struct fohh_connection *fohh_find_connection(const char *domain, uint16_t port_he);
struct fohh_connection *fohh_find_connection_2(const char *filter1, const char *filter2);

int fohh_connection_get_tune_state(struct fohh_connection *f_conn,
                                   uint32_t *sockbuf,
                                   uint32_t *delay_optimized,
                                   uint32_t *chunk_size,
                                   uint32_t *allowed_chunk);

uint16_t sockaddr_get_port_he(struct sockaddr_storage *addr);

#define VERIFY_PEER 1
#define DONT_VERIFY_PEER 0

const char * fohh_get_cipher_list_from_cipher_index(int cipher_index);
int fohh_ssl_ctx_server_standard_options(SSL_CTX *ctx, char *curves, int verify_peer);
int fohh_ssl_ctx_server_custom_options(SSL_CTX *ctx, char *curves, int ciphersuite_index, int verify_peer);
int fohh_ssl_ctx_client_standard_options(SSL_CTX *ctx, char *curves, int verify_peer);
int fohh_ssl_ctx_client_custom_options(SSL_CTX *ctx, char *curves, int ciphersuite_index, int verify_peer);
int fohh_ssl_ctx_standard_options_with_tls1(SSL_CTX *ctx, char *curves, char *ciphers, int verify_peer);
int fohh_ssl_ctx_standard_options_without_tls1_0(SSL_CTX *ctx, char *curves, char *ciphers, int verify_peer);

int fohh_ssl_ctx_standard_webserver_options(SSL_CTX *ctx, char *curves);
int fohh_ssl_ctx_standard_webserver_options2(SSL_CTX *ctx, char *curves, char *ciphers, long options);
int fohh_ssl_ctx_standard_webserver_options_custom_cipher(SSL_CTX *ctx, char *curves, long options, int ciphersuite_index);
//int fohh_ssl_ctx_add_trusted_root_file(SSL_CTX *ctx, char *filename);

int fohh_ssl_ctx_add_trusted_root_mem(SSL_CTX *ctx, char *data, size_t data_len);

int fohh_show_tune_values(const char *domain, const char *port, enum fohh_connection_type type,
                          int *sndbuf, int *delay_optimized, int *chunk_size, int *allowed_chunk, int *auto_tune_disable);
int fohh_set_socket_snd_buf(const char *domain, const char *port, enum fohh_connection_type type, int value);
int fohh_toggle_auto_tune_disable(const char *domain, const char *port, enum fohh_connection_type type, int *value);
int fohh_toggle_delay_optimized(const char *domain, const char *port, enum fohh_connection_type type, int *value);
int fohh_set_chunk_size(const char *domain, const char *port, enum fohh_connection_type type, int value);
int fohh_set_allowed_chunk(const char *domain, const char *port, enum fohh_connection_type type, int value);

int fohh_get_chunk_size(struct fohh_connection *f_conn);
int fohh_get_allowed_chunk(struct fohh_connection *f_conn);

int fohh_enable_direct_write(struct fohh_connection *f_conn);
const char *fohh_result_string(int fohh_result);
void fohh_sockaddr_storage_to_str(struct sockaddr_storage *ss, char *out_str, size_t out_str_len);
int fohh_compare_sockaddr_storage(struct sockaddr_storage *s1, struct sockaddr_storage *s2);
int fohh_set_max_backoff(struct fohh_connection *f_conn, int max_s);

const char *fohh_close_reason(struct fohh_connection *f_conn);
int fohh_connection_is_quiet(struct fohh_connection *f_conn);

/* get the uptime of a connection. Note uptime is valid only if the connection is really UP (i.e in connected state)*/
int64_t fohh_get_uptime_s(struct fohh_connection *connection);
char* fohh_get_uptime_str(struct fohh_connection *f_conn, char* buf, int buf_len);

struct zevent_base *fohh_connection_zevent_base(struct fohh_connection *f_conn);
struct zevent_base *fohh_thread_id_zevent_base(int fohh_thread_id);
struct zthread_info *fohh_thread_id_zthread(int fohh_thread_id);
int fohh_connection_get_zthread_num(struct fohh_connection *f_conn);
int fohh_thread_id_get_zthread_num(int fohh_thread_id);

int fohh_str_to_sockaddr_storage(const char *str, struct sockaddr_storage *storage, socklen_t *slen);
void fohh_sockaddr_set_port(struct sockaddr_storage *addr, uint16_t port_ne);

struct event_base *fohh_connection_event_base(struct fohh_connection *f_conn);

const char* fohh_get_user_debug_str(struct fohh_connection *f_conn);

void fohh_set_user_debug_str(struct fohh_connection *f_conn, char *user_debug_str);

void fohh_append_user_debug_str(struct fohh_connection *f_conn, char *append_debug_str, int add_quote);

int fohh_user_debug_display_str(struct fohh_connection *f_conn, char *display_str, size_t display_str_len);

void fohh_delimit_user_debug_str(struct fohh_connection *f_conn);

/* Disable quiet flag for the fohh connection */
void fohh_connection_set_quiet(struct fohh_connection *connection, int quiet);

void fohh_connection_set_conn_desc_flag(struct fohh_connection *connection, int conn_desc_flag);

void fohh_connection_set_burst_flag(struct fohh_connection *connection, int burst_flag);

int fohh_connection_set_username(struct fohh_connection *connection, char* username);

enum fohh_conn_describe_type fohh_connections_describe_get_type(const char *arg);

void fohh_worker_pool_dump(char *out_buf, size_t out_buf_len);

int fohh_redirect(struct fohh_connection *f_conn, const char **targets, int target_count, int64_t redirect_msg_timestamp_s, char **sni_suffixes);

void fohh_update_domain_name(struct fohh_connection *f_conn, const char *remote_host_name);
void fohh_update_sni_name(struct fohh_connection *f_conn, const char *sni_service_name);

int fohh_connection_reset_alt_cloud(struct fohh_connection*  f_conn,
                                    char*                    new_cloud_name);

int fohh_send_info(struct fohh_connection *f_conn);
int fohh_send_info_with_capability(struct fohh_connection *f_conn, void*fohh_cookie);
void
fohh_connection_set_default_sni(struct fohh_connection *connection, char* default_sni_suffix);

int64_t fohh_get_peer_last_redirect_s(struct fohh_connection *f_conn);

int fohh_get_redirect_retry_count(struct fohh_connection *f_conn);


int64_t fohh_get_socket_resource_errors_count(void);


void fohh_update_socket_resource_errors_count(int errno_val);

/*
 * Read the set of proxy bypasses from file. Expected to be called 0
 * or 1 times. Returns a variety of errors on
 * failure. FOHH_RESULT_NOT_FOUND is returned if the filename is not
 * found. FOHH_RESULT_NO_ERROR on success.
 */
int fohh_read_bypass_from_file(const char *filename);

int fohh_is_proxied(const char *destination_ip_or_domain);

/* Gets the number of bytes in the buffer. system*size return -1 if
 * the data cannot be gleaned */
void fohh_get_buffer_size(struct fohh_connection *f_conn, int64_t *user_bytes_size, int64_t *system_bytes_size, int64_t *system_buffer_size);


void fohh_increment_window_update_stats(struct fohh_connection *connection, int32_t tag_id);
void fohh_increment_pause_stats(struct fohh_connection *connection);
void fohh_increment_resume_stats(struct fohh_connection *connection);

void fohh_set_dev_environment(int is_dev);

void fohh_set_rx_data_timeout_s(struct fohh_connection *f_conn, int rx_data_timeout_s);

void fohh_set_max_tx_history_depth(struct fohh_connection *f_conn, size_t max_depth);
size_t fohh_get_max_tx_history_depth(struct fohh_connection *f_conn);

/* Get history of objects on the queue. All objects returned have an
 * extra reference count, and that reference count must be dropped by
 * the consumer */
size_t fohh_get_tx_history(struct fohh_connection *f_conn, struct argo_object **objects, int64_t *object_us, size_t array_size);

/*
 * the history filter starts empty, which means all objects will be
 * stored in the history As soon as the history filter is not empty,
 * then only objects that exist in the filter will be stored in the
 * history
 *
 * The history filter is global.
 */
void fohh_tx_filter_history_add(const char *object_name);
void fohh_tx_filter_history_remove(const char *object_name);

void fohh_tx_filter_history_get(char ***filters, int *filter_count);
void fohh_tx_filter_history_free(char **filters, int filter_count);

void zpn_broker_add_quickack_to_authlog (char *feature_str, uint32_t feature);

/* getter for fohh_status_request_recevied_us */
int64_t fohh_get_status_request_recevied_us(struct fohh_connection *f_conn);
/* getter for peer_alive_detected_epoch_s */
int64_t fohh_get_peer_alive_detected(struct fohh_connection *f_conn);

void fohh_set_conn_setup_timeout(uint64_t value);

/*
 * Listen_count. If 0 (default), a single thread does all the
 * listening for generic servers. If non-zero, then that many threads
 * (starting at 0) perform reuseaddr/reuseport listening for generic
 * servers. Furthermore, if a worker pool maps exactly to the size of
 * listen count, then connections are processed on the thread on which
 * the listen callback was received, making the kernel's work much
 * easier.
 *
 * Make this call immediately after fohh initialization and before
 * generic server listening/etc occurs.
 */
int fohh_set_generic_server_listen_count(int listen_count);

/* Tell fohh threads to enable/disable stats-logging-by-state.
 * Should be called before fohh_init. Default is NOT logging.
 */
void fohh_set_state_stats_logging(int enable);
int fohh_get_state_stats_logging();

void fohh_set_aggregated_stats_logging(int enable);
int fohh_connection_aggregated_stats_enabled();

void fohh_set_connection_aggregated_hop_latency_stats(int enable);
int fohh_connection_aggregated_hop_latency_stats_enabled();

void fohh_set_connection_aggregated_pipeline_latency_stats(int enable);
int fohh_connection_aggregated_pipeline_latency_stats_enabled();

/* Set to turn on debugging (full logging) of all new connections. */
void fohh_set_debug_new_connections(int debug_new_connections);

/* Set function for log collection name being carried in the logging connection
   between the connector and the broker. This is used to populate
   the log type field in the connection auth log. */
void fohh_connection_set_log_collection_name(struct fohh_connection *f_conn,
                                             const char* collection_name);
/* Get function for log collection name */
const char* fohh_connection_get_log_collection_name(struct fohh_connection *f_conn);

void fohh_connection_set_log_type(struct fohh_connection *f_conn,
                                  const char* log_type);
const char* fohh_connection_get_log_type(struct fohh_connection *f_conn);
void fohh_connection_enable_fc_reset_recover_and_update_parameters(struct fohh_connection *f_conn, int64_t timeout_s, int64_t percent_hundred);
int fohh_connection_is_fc_reset_recover_enabled(struct fohh_connection *f_conn);
int64_t fohh_connection_get_fc_continous_timeout_interval_s(struct fohh_connection *f_conn);
double fohh_connection_get_fc_continuous_threshold_percent(struct fohh_connection *f_conn);
void fohh_connection_set_kick_flow_control_reset(struct fohh_connection *f_conn, int value);
void fohh_connection_set_stats_fohh_peer_tx(struct fohh_connection *f_conn, int64_t value);
void fohh_connection_set_stats_fohh_rx_data(struct fohh_connection *f_conn, int64_t value);
void fohh_connection_set_stats_fohh_rx_data_us(struct fohh_connection *f_conn, int64_t value);
void fohh_connection_set_stats_fohh_enq_data_us(struct fohh_connection *f_conn, int64_t value);
void fohh_connection_set_stats_fohh_enq_bytes(struct fohh_connection *f_conn, int64_t value);
void fohh_connection_set_stats_fohh_deq_bytes(struct fohh_connection *f_conn, int64_t value);
void fohh_connection_set_stats_fohh_tx_data(struct fohh_connection *f_conn, int64_t value);
void fohh_connection_set_stats_fohh_tx_data_drop(struct fohh_connection *f_conn, int64_t value);
void fohh_connection_set_stats_fohh_tx_limit(struct fohh_connection *f_conn, int64_t value);
void fohh_connection_set_stats_fohh_remote_rx_data(struct fohh_connection *f_conn, int64_t value);
void fohh_connection_set_stats_fohh_unblocked_thread_call_count(struct fohh_connection *f_conn, int64_t value);
void fohh_connection_set_stats_fohh_data_write_blocked(struct fohh_connection *f_conn, int64_t value,
                                                       int64_t block_fohh, int64_t block_evbuf);
void fohh_connection_set_stats_fohh_fc_blocked(struct fohh_connection *f_conn, int64_t value);
void fohh_connection_set_stats_fohh_fc_blocked_time(struct fohh_connection *f_conn, int64_t value);
void fohh_connection_set_stats_fohh_fc_blocked_max_time(struct fohh_connection *f_conn, int64_t value);
int get_ja3_hash_from_client_hello(const unsigned char *sp0,
                                   int inplen,
                                   char *sni,
                                   char* ja3_hash,
                                   char *ja3_hash_extn_sorted,
                                   int hash_len);

// returns 1 if f_conn ptr is NULL
// returns 2 if f_conn has unexpected connection state
// returns 0 otherwise
int  fohh_error_debug_check(struct fohh_connection *f_conn);
/* set the fohh connection mode - active or passive */
void fohh_connection_set_current_connection_mode(struct fohh_connection *f_conn, int conn_mode);

int fohh_connection_send_fohh_status_request(struct fohh_connection *f_conn, int64_t request_id);
void fohh_set_inst_update_in_logs_not_required(struct fohh_connection *f_conn, int value);
int fohh_get_inst_update_in_logs_not_required(struct fohh_connection* f_conn);
void fohh_connection_incr_stats_fohh_win_update_expired(struct fohh_connection *f_conn);
void fohh_connection_incr_stats_fohh_win_update_above_threshold(struct fohh_connection *f_conn);
void fohh_connection_incr_stats_fohh_win_update_low_water_mark(struct fohh_connection *f_conn);

/* DDIL stuff */

/*
 * Call this function after fohh_client_create() if the connection needs to be switched between site controller
 * and default broker. This routine stores infos in fohh object which later is used by state machine.
 *
 * site_offline_domain: Site offline domain
 *
 * site_offline_domain_prefix: Prefix("co2bcp") to connect to offline domain.
 * E.g. "co2bcp".<offline_domain>
 *
 * default_remote_address_name: Always the default broker remote address name
 * E.g. "co2br/any.co2br/any.co2slbr".<cloud_name>
 *
 * ep_always_sitec: this keeps the connection always with site controller
 *
 * switch_allowed: If false, connections won't switch back and forth
 *
 * currently_to_sitec: First attempt is done towards sitec or default endpoint
 *
 * ssl_ctx_pri: SSL context for private cloud(needed when same connection is used for private cloud)
 *
 * ssl_ctx_pub: SSL context for public cloud(needed when same connection is used for public cloud)
 */
void fohh_connection_site_init(struct fohh_connection *f_conn,
                               const char *site_offline_domain,
                               const char *site_offline_domain_prefix,
                               const char *default_remote_address_name,
                               int ep_always_sitec,
                               int switch_not_needed,
                               int currently_to_sitec,
                               SSL_CTX *ssl_ctx_pri,
                               SSL_CTX *ssl_ctx_pub
                               );
/*
 * This function is designed to be invoked upon encountering a site offline domain.
 *
 * It efficiently detects changes in the domain, automatically interpreting a NULL input as a removal of the domain.
 *
 * It manages the update of connection states, ensuring proper handling for scenarios involving
 * offline domain addition, removal, or modification.
 *
 * Subsequently, the fohh_connection_site_state_check() function from the state machine gets activated
 * to facilitate seamless connection switching between the public broker and the site controller, as required.
 */
int fohh_connection_update_site_offline_domain(struct fohh_connection *f_conn, char *domain_name);

typedef void (fohh_connection_site_state_cb)(struct fohh_connection *f_conn, int conn_disconnected, enum fohh_switch_connection *switch_now);

/*
 * This function is designed to do connection switching between public broker and site controller
 * upon detecting state change within the connection.
 *
 * This function gets activated from connection state machine and acts upon the state change.
 *
 * State change can be from these 5 cases,
 *
 * 1. Site offline domain is added - connection switch from public broker to site controller
 * 2. Site offline domain is removed - connection switch from site controller to public broker
 * 3. Site offline domain is changed - connection remains on site controller with an updated remote address name
 * 4. SiteC is not reachable - connection switch from site controller to public broker
 * 5. SiteC is now accessible again since we reverted to the default broker - connection switch from public broker to site controller
 * 6. Site is disabled - connection switches to default broker regardless of sitec being preferred or not
 */
int fohh_connection_site_state_check(struct fohh_connection *f_conn,
                                     int site_active,
                                     int sitec_reachable,
                                     int def_ep_reachable,
                                     int sitec_preferred,
                                     int64_t max_allowed_downtime_s,
                                     fohh_connection_site_state_cb *state_cb);

/*
 * This function is to check if offline domain is configured for this connection or not
 */
int fohh_connection_get_site_offline_domain_status(struct fohh_connection *f_conn);
void fohh_connection_update_site_prefix(struct fohh_connection *f_conn, char *prefix);

void fohh_ssl_ctx_set_verify(SSL_CTX *ctx, int verify_peer);
// do not use these APIs directly, instead use FOHH_MALLOC_DONT_DUMP/FOHH_FREE_DONT_DUMP
void *fohh_malloc_aligned(size_t sz);
void fohh_free_aligned(void *data);

/* Used to obtain all listener socket FDs of an SNI server */
int fohh_generic_server_obtain_sockfd(struct fohh_generic_server *server, int *array, int array_count);

void fohh_set_debugcmd_pool_support(int supported);
int fohh_is_debugcmd_pool_support_enabled();
/**
 * Sets the cipher list index for the SSL context.
 * This function atomically sets the selected cipher list index to the provided value.
 * @param cipher_index The index of the cipher list to be selected.
 */
void fohh_ssl_ctx_set_server_cipher_list_index_cb(const int64_t *config_value, int64_t impacted_gid);

void fohh_ssl_ctx_set_client_cipher_list_index_cb (const int64_t *config_value, int64_t impacted_gid);

void fohh_connection_set_tlv_conn(struct fohh_connection *f_conn, uint32_t value);

void fohh_set_fohh_fc_enhacement_config(struct fohh_connection *connection);

uint32_t fohh_get_fohh_fc_enhacement_config(struct fohh_connection *connection);

void fohh_set_batched_mconn_window_updates_config(struct fohh_connection *connection);
uint32_t fohh_get_batched_mconn_window_updates_config(struct fohh_connection *connection);

void fohh_set_syn_status_msg_config(struct fohh_connection *connection);
void fohh_set_pipeline_latency_trace_config(struct fohh_connection *connection);
uint32_t fohh_get_pipeline_latency_trace_config(struct fohh_connection *connection);

enum fohh_site_connection_endpoint_type fohh_connection_get_site_ep_type(struct fohh_connection *f_conn);
#endif /* __FOHH_H__ */
