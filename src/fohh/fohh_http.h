/*
 * fohh_http.h. Copyright (C) 2014 Zscaler, Inc. All Rights Reserved.
 */


/*
 * A simple HTTP(s) server.
 *
 * The basic operation:
 *
 * - Create an HTTP server.
 *
 * - Specify addresses to listen to.
 *
 * - Register any callback desired, by method and path. There is a
 *   default callback.
 *
 * - For callback, there is a standard output mechanism for generating
 *   a response.
 *
 * Note that this server is really designed to be quite simple, and is
 * not intended for particularly dynamic HTTP content.
 */

#ifndef __FOHH_HTTP_H__
#define __FOHH_HTTP_H__

#include "zcdns/zcdns.h"
#include <openssl/ssl.h>
#include "fohh/fohh.h"
#include <event2/bufferevent.h>
#include "zcrypt/zcrypt.h"
#include "zpath_misc/zmicro_heap.h"

/* Timeout indicator for FOHH HTTP Client Fetch */
#define FOHH_HTTP_CLIENT_FETCH_TIMEOUT_INDICATOR    (-1)
/*
 * This enumeration is taken DIRECTLY from http_parser.
 *
 * It MUST match.
 *
 * I do not include http_parser.h because I do not want users of this
 * software to have to deal with http_parser*
 */
enum fohh_http_method {
    FOHH_HTTP_METHOD_DELETE=0,
    FOHH_HTTP_METHOD_GET=1,
    FOHH_HTTP_METHOD_HEAD=2,
    FOHH_HTTP_METHOD_POST=3,
    FOHH_HTTP_METHOD_PUT=4,
    FOHH_HTTP_METHOD_CONNECT=5,
    FOHH_HTTP_METHOD_OPTIONS=6,
    FOHH_HTTP_METHOD_TRACE=7,
    FOHH_HTTP_METHOD_COPY=8,
    FOHH_HTTP_METHOD_LOCK=9,
    FOHH_HTTP_METHOD_MKCOL=10,
    FOHH_HTTP_METHOD_MOVE=11,
    FOHH_HTTP_METHOD_PROPFIND=12,
    FOHH_HTTP_METHOD_PROPPATCH=13,
    FOHH_HTTP_METHOD_SEARCH=14,
    FOHH_HTTP_METHOD_UNLOCK=15,
    FOHH_HTTP_METHOD_REPORT=16,
    FOHH_HTTP_METHOD_MKACTIVITY=17,
    FOHH_HTTP_METHOD_CHECKOUT=18,
    FOHH_HTTP_METHOD_MERGE=19,
    FOHH_HTTP_METHOD_MSEARCH=20,
    FOHH_HTTP_METHOD_NOTIFY=21,
    FOHH_HTTP_METHOD_SUBSCRIBE=22,
    FOHH_HTTP_METHOD_UNSUBSCRIBE=23,
    FOHH_HTTP_METHOD_PATCH=24,
    FOHH_HTTP_METHOD_PURGE=25,
    FOHH_HTTP_METHOD_MKCALENDAR=26
};

enum fohh_http_client_status {
    fohh_http_client_status_invalid = 0,
    fohh_http_client_status_resolve_v4,
    fohh_http_client_status_resolve_v6,
    fohh_http_client_status_connect_v4,
    fohh_http_client_status_connect_v6,
    fohh_http_client_status_dns_timeout,
    fohh_http_client_status_dns_fail,
    fohh_http_client_status_connect_timeout,
    fohh_http_client_status_connect_fail,
    fohh_http_client_status_connect_fail_ssl,
    fohh_http_client_status_success,
    fohh_http_client_status_timeout,
    fohh_http_client_status_proxy_connect_v4,
    fohh_http_client_status_proxy_connect_v6,
    fohh_http_client_status_proxy_parse,
    fohh_http_client_status_proxy_done,
    fohh_http_client_status_failure,
};


enum fohh_http_client_request_status {
    fohh_http_client_request_status_invalid = 0,
    fohh_http_client_request_status_success,
    fohh_http_client_request_status_timeout,
    fohh_http_client_request_status_failure
};

/*
 * The options that the engine can be told to perform at connection
 * accept time (when there is only a remote IP address upon which to
 * make a decision)
 *
 * These are VERY likely to change.
 */
enum fohh_http_server_accept_ssl_options {
    fohh_http_server_accept_ssl_reset,
    fohh_http_server_accept_ssl_bridge,
    fohh_http_server_accept_ssl_sni
};

/*
 * This structure is used by the accept callback to get configuration
 * for how to proceed re: processing SSL/HTTP.
 *
 * These are VERY likely to change.
 */
struct fohh_http_server_accept_config {
    unsigned                                 accept_plain_http:1;
    enum fohh_http_server_accept_ssl_options ssl_options;
};


struct fohh_http_server;
struct fohh_http_server_connection;

struct fohh_http_client;

/*
 * Used for a list of query strings on request.
 */
struct fohh_http_query_string {
    const char *key;
    const char *value;
    struct fohh_http_query_string *next;
};

/*
 * All the state associated with an HTTP request. This state is
 * read-only, and available only for the duration of a callback
 *
 * NOTE: This state is in raw form- you may need to parse this/decode
 * this in order to make use of it. This is raw text form. i.e. not
 * URI decoded, etc. (no %20 translation, etc)
 */
struct fohh_http_request {               /* _ARGO: object_definition */
    enum fohh_http_method method;
    uint16_t http_version_minor;         /* _ARGO: integer */
    uint16_t http_version_major;         /* _ARGO: integer */



    /* Normalized parser state- these are interpreted from the
     * request. */

    /* norm_host- Normalized host- taken at first preference from
     * headers, and at second preference from the request line. If
     * there is no host specified, this will be NULL. This will NOT
     * include the :port piece of the host. This host WILL be
     * converted to all lower case. */
    const char *norm_host;               /* _ARGO: string */

    /* norm_port- taken from wherever host is taken. Note this is a
     * string... */
    const char *norm_host_port;          /* _ARGO: string */

    /* The query strings (if any), parsed into key/value pairs. */
    struct fohh_http_query_string *queries;

    /* Below is all RAW parsing state. */

    /* The full URL. It's independent pieces are extracted below. */
    const char *req_url;

    /* Fields from request line (including URL) */
    const char *req_fragment;            /* _ARGO: string */
    const char *req_host;                /* _ARGO: string */
    const char *req_method;              /* _ARGO: string */
    const char *req_path;                /* _ARGO: string */
    const char *req_port;                /* _ARGO: string */
    const char *req_query;               /* _ARGO: string */
    const char *req_schema;              /* _ARGO: string */
    const char *req_userinfo;            /* _ARGO: string */

    /* Fields from headers */
    const char *hdr_accept;              /* _ARGO: string */
    const char *hdr_accept_encoding;     /* _ARGO: string */
    const char *hdr_accept_language;     /* _ARGO: string */
    const char *hdr_authorization;       /* _ARGO: string */
    const char *hdr_cache_control;       /* _ARGO: string */
    const char *hdr_connection;          /* _ARGO: string */
    const char *hdr_content_encoding;    /* _ARGO: string */
    const char *hdr_content_length;      /* _ARGO: string */
    const char *hdr_content_type;        /* _ARGO: string */
    const char *hdr_cookie;              /* _ARGO: string */
    const char *hdr_date;                /* _ARGO: string */
    const char *hdr_expect;              /* _ARGO: string */
    const char *hdr_host;                /* _ARGO: string */
    const char *hdr_if_match;            /* _ARGO: string */
    const char *hdr_if_modified_since;   /* _ARGO: string */
    const char *hdr_if_none_match;       /* _ARGO: string */
    const char *hdr_if_range;            /* _ARGO: string */
    const char *hdr_if_unmodified_since; /* _ARGO: string */
    const char *hdr_keep_alive;          /* _ARGO: string */
    const char *hdr_range;               /* _ARGO: string */
    const char *hdr_referer;             /* _ARGO: string */
    const char *hdr_transfer_encoding;   /* _ARGO: string */
    const char *hdr_upgrade;             /* _ARGO: string */
    const char *hdr_user_agent;          /* _ARGO: string */
    const char *hdr_via;                 /* _ARGO: string */
    const char *hdr_x_forwarded_for;     /* _ARGO: string */
    const char *hdr_x_real_ip;           /* _ARGO: string */
    const char *hdr_forwarded;     /* _ARGO: string */
};

/*
 * HTTP status code reason phrases are generated automatically based
 * on status.
 *
 * Headers are generated as follows:
 *
 * Cache-Control: (Whatever it takes to make non-caching)
 * Date: (Right Now)
 * Expires: (Right Now)
 * Content-Type: text/html; charset=ISO-8859-1 (Unless overridden)
 * Content-Length: length. (Only if body is specified)
 * Connection: close
 *
 * Eventually, a more dynamic response mechanism will be generated.
 */
struct fohh_http_response {
    uint16_t           http_version_minor;
    uint16_t           http_version_major;
    uint16_t           status;

    /* Content-Type defaults to:
     *
     * "Content-Type: text/html; charset=ISO-8859-1"
     *
     * If non-null, it will be overridden.
     */
    const char        *content_type;

    /*
     * Extra headers (eg for redirect). NULL if not needed.
     */
    const char        *extra_headers;

    /* Body text is accumulated and passed as an evbuffer
     * (optional). Ownership of the evbuffer passes with any of the
     * response calls.
     */
    struct evbuffer   *body;
};

/*
 * Defining structures for unix domain socket command handling.
 *  Including here; as its associated with fohh http layer.
 */
struct fohh_domain_sock_cmd_query_string {
    const char *key;
    const char *value;
    struct fohh_domain_sock_cmd_query_string *next;
};

/* struct for storing the command obtained via unix domain socket(uds) handle */
struct fohh_domain_sock_cmd_request {
    /* The query strings (if any), parsed into key/value pairs. */
    struct fohh_domain_sock_cmd_query_string *queries;

    /* The full command string */
    const char *req_cmd;

    /* extracted command path */
    const char *req_path;

    /* extracted command query */
    const char *req_query;
};

struct fohh_domain_sock_cmd_listener_info {
    /* user info */
    struct {
        uid_t peer_uid;
    } user_info;

    /* the actual request data, and parsed info thereof. */
    struct fohh_domain_sock_cmd_request request;

    /* Temporary buffering of request headers. */
    uint8_t header_value_buf[4096];

    /* Heap for managing header_value_buf */
    struct zmicro_heap heap;

    /* response; to be inline with existing command usage */
    struct fohh_http_response response;
};

/*
 * The following absolute paths are assumed to be unchanged, and the bundle is
 * assumed to be always up to date.
 *
 * Note:
 * OS_BUNDLE_PATH_RHEL location will have up-to-date bundle certs supported by
 * update-ca-trust tool.
 *
 * There is no CA certificate bundle on OS X, because SSL libraries typically use
 * Apple's Security Framework and obtain certificates from Keychain.
 * I found this path seems to work on my mac, so using it for now.
 * we won't bother too much on supporting sarge on mac. will we?
 */
#define OS_BUNDLE_PATH_OSX "/usr/local/etc/ca-certificates/cert.pem"
#define OS_BUNDLE_PATH_FREEBSD "/usr/local/share/certs/ca-root-nss.crt"
#define OS_BUNDLE_PATH_RHEL "/etc/pki/ca-trust/extracted/pem/tls-ca-bundle.pem"
#define OS_BUNDLE_PATH_DEBIAN "/etc/ssl/certs/ca-certificates.crt"

/*
 * Prototype for http request callbacks.
 *
 * Request data is only valid for the duration of this call.
 * Asynchronous callbacks should remember the HTTP version (in
 * addition to silly things like the connection...) in order to make
 * proper callbacks.
 *
 * HTTP request callbacks must perform one of the following:
 *
 * 1: SYNCHRONOUS RESPONSE
 *    A. If you are happy with the request, and feel it deserves a
 *       valid immediate response (200, 404, whatever), make a call to
 *       fohh_http_send_sync_response and return its response.
 *
 * 2: SYNCHRONOUS FAILURE
 *    A. If you are unhappy with this request, and want the connection
 *       to be destroyed immediately, Return something other than
 *       FOHH_RESULT_NO_ERROR, but NOT FOHH_RESULT_ASYNCHRONOUS. No
 *       HTTP response will be sent at all.
 *
 * 3: ASYNCHRONOUS RESPONSE
 *    A. If you want to respond to this request asynchronously, you
 *       need to 'remember':
 *       1. Required: connection.
 *       2. Required: connection_incarnation.
 *       3. Required: HTTP version of the request.
 *       4. Optional: Any other request data you may need
 *          later. (request data becomes invalid on return from
 *          callback)
 *    B. Return FOHH_RESULT_ASYNCHRONOUS.  Note that as soon as you
 *       return, fohh_http_request state is no longer valid.
 *    C. In the future, ONE of:
 *       i.  Make a call to fohh_http_send_async_response.  (You will
 *           need the connection, connection_incarnation, and
 *           http_version to do this correctly) (Even if this call
 *           fails, it fails cleanly- no need to make any other calls
 *           to clean up state)
 *       ii. Make a call to fohh_http_cancel_async_response.
 *           (To kill it off)
 */
typedef int (fohh_http_server_request_cb)(struct fohh_http_server_connection *connection,
                                          int64_t connection_incarnation,
                                          struct fohh_http_request *request,
                                          const char *matched_host,
                                          const char *matched_path,
                                          void *void_cookie,
                                          int64_t int_cookie);

/*
 * Prototype for command received via Unix Domain socket (UDS)
 *  Request data is only valid for the duration of this call.
 *  Inputs:
 *      command_info :- Information regarding the command; and the parsed data and resp.
 *      request :- command request
 *      matched_host - matched host
 *      matched_path - the command path
 *      void_cookie - info regarding the command.
 */
typedef int (fohh_domain_sock_cmd_request_cb)(struct fohh_domain_sock_cmd_listener_info *command_info,
                                            struct fohh_domain_sock_cmd_request *request,
                                            const char *matched_host,
                                            const char *matched_path,
                                            void *void_cookie,
                                            int64_t int_cookie);

/*
 * Prototype for http handoff callbacks.
 *
 * These callbacks are designed to hand off a connection (bufferevent)
 * to another module once the request parsing is complete.
 *
 * Request data is only valid for the duration of this call.
 *
 * HTTP request callbacks have the following possible responses:
 *
 * 1: SYNCHRONOUS ACCEPT- The connection has been taken over by the
 *    callback receiver, and the http server should drop all
 *    references to it. This is accomplished by returning
 *    FOHH_RESULT_NO_ERROR.
 *
 * 2: SYNCHRONOUS REJECT- Return any result other than _NO_ERROR. A
 *    synchronous reject will cause the connection to be simply closed.
 *
 * There is no asynchronous option for http handoffs.
 *
 * bev- the bufferevent that is to be handed off.
 *
 * parsed_bytes- The number of parsed bytes in the HTTP request-
 *    HEADERS ONLY.
 *
 * fohh_thread_id- The thread ID making this callback, where the bev
 *    is 'owned'
 */
typedef int (fohh_http_server_handoff_cb)(struct fohh_http_request *request,
                                          const char *matched_host,
                                          const char *matched_path,
                                          struct bufferevent *bev,
                                          size_t parsed_bytes,
                                          int fohh_thread_id,
                                          void *void_cookie,
                                          int64_t int_cookie);

/*
 * Callback received when an HTTP client connection succeeds/fails
 */
typedef int (fohh_http_client_create_cb)(struct fohh_http_client *client,
                                         enum fohh_http_client_status status,
                                         void *void_cookie,
                                         int64_t int_cookie);

/*
 * Callback received when an HTTP client request returns.
 *
 * NOTE: response is ephemeral- You must copy/use any state in
 * response before returning from this routine!
 */
typedef int (fohh_http_client_result_cb)(struct fohh_http_client *client,
                                         enum fohh_http_client_request_status status,
                                         int http_status,
                                         struct evbuffer *result_body,
                                         void *void_cookie,
                                         int64_t int_cookie);

/*
 * When data must be asynchronously transmitted back on an HTTP
 * connection, it is possible that that connection has been removed in
 * the meantime.
 *
 * The following routine verifies (and locks) connection/request state
 * via mutexes and incarnation...
 *
 * If this routine returns FOHH_RESULT_NO_ERROR, that means the
 * connection state is still valid, and you must make a call to
 * fohh_http_*_async_response_unlock to release the lock.
 *
 * If this routine returns FOHH_RESULT_ERR, then there is an
 * incarnation mismatch- the old connection is dead, and there is not
 * much that can be done. (No locks are to be released in this case)
 */
int fohh_http_server_connection_verify_lock(struct fohh_http_server_connection *connection,
                                            int64_t connection_incarnation);
void fohh_http_server_connection_unlock(struct fohh_http_server_connection *connection);

void fohh_http_server_connection_get_remote_ip(struct fohh_http_server_connection *connection, struct argo_inet *remote_ip);

/*
 * Prototype for TCP accept callbacks- used to filter IP addresses to
 * which we will respond at all.
 *
 * This routine may proceed asynchronously.
 *
 * This routine must result in a call to fohh_http_accept at some
 * point in the future, or the connection will be 'hung'. (Can be done
 * either synchronously or asynchronously)
 *
 * If this routine cannot call fohh_http_accept for some reason, then
 * this routine should return some form of FOHH_RESULT_ERR indication.
 */
typedef int (fohh_http_server_accept_cb)(struct argo_inet *remote_ip,
                                         struct argo_inet *local_ip,
                                         uint16_t port_he);


/*
 * Create an HTTP server. Creates HTTP server state, to which
 * callbacks and can be registered and to which listeners can be
 * added.
 *
 * The name is used for debugging logs only.
 */
struct fohh_http_server *fohh_http_server_create(const char *name);

/*
 * Create an HTTP server running in proxy mode. All callbacks take the
 * form of a server_handoff_cb instead of a server_request_cb. Note
 * that the proxy is a full TCP connection proxy- it does not examine
 * intermediate requests within pipelined connections. It only proxies
 * the first request/header.
 *
 * The parser consumes zero data from the stream.
 *
 * The name is used for debugging logs only.
 */
struct fohh_http_server *fohh_http_server_create_handoff(const char *name);

/*
 * Add a listener for the HTTP server.
 *
 * Specify IPv4/IPv6 address to bind to (can be any). Address is an
 * argo_inet (always network order), and listen_port is host
 * endian. Yes, not consistent, but still probably best.
 *
 * If provided, the accept_cb will be called for every inbound
 * connection before significant processing occurs. (i.e. before SSL
 * happens, but AFTER proxy protocol (to get the REAL remote addr)
 *
 * If an ssl_ctx is provided, then that ssl_ctx will be used to verify
 * client connections and to advertise our own worthiness.
 *
 * If an SSL context is specified, then the HTTP service will
 * negotiate SSL.
 *
 * If use_proxy_protocol is specified, then proxy protocol is expected
 * on connection open.
 */
int fohh_http_server_listen(struct fohh_http_server *server,
                            struct argo_inet *listen_addr,
                            uint16_t listen_port_he,
                            fohh_http_server_accept_cb *accept_cb,
                            SSL_CTX *ssl_ctx,
                            int use_proxy_protocol,
                            int is_debug_port_listen);

struct fohh_http_listener* fohh_http_listener_alloc(struct fohh_http_server *server,
                                                    struct argo_inet *listen_addr,
                                                    uint16_t listen_port_he,
                                                    fohh_http_server_accept_cb *accept_cb,
                                                    SSL_CTX *ssl_ctx,
                                                    int use_proxy_protocol);

/*
 * Create an SSL context suitable for fohh_http_server_listen.
 *
 * If a root_cert_file_name is specified, then client cert
 * authentication will be performed.
 *
 * A private/public cert for this instance is required. (my_*)
 *
 * In the future, this initializer might be modified to allow
 * specifying tls/sslv2/sslv3/etc. At the moment, it only alows TLSv1.
 */
SSL_CTX *fohh_http_ssl_ctx_create(const char *root_cert_file_name,
                                         const char *my_cert_file_name,
                                         const char *my_private_key_file_name);

/*
 * This routine should only be called in response to a server accept
 * callback being called.
 *
 * config is the config to control what SSLish/HTTPish stuff is done.
 */
void fohh_http_accept(struct fohh_http_request *request,
                      struct fohh_http_server_accept_config *config);

/*
 * Set callback for a specific host+path. Both can be wildcarded as
 * much as you want.
 *
 * Host is of the form 'domain.com'
 *
 * Path is of the form '/path/to/stuff'
 *
 * If host is NULL, then the registration will apply to any request
 * that doesn't match another host.
 *
 * If path is NULL, then the registration will apply to any path for
 * the specified host.
 *
 * If a host match occurs without a path match, then the other
 * matching hosts are checked in descending precedence, up to the
 * default registration.
 *
 * NOTE: the wildcards are 'WORD' wildcards, NOT string wildcards.
 * This means that *com matches any zones with a trailing .com:

 * Host:           *hello.com
 *                  hello.com  MATCHES
 *                a.hello.com  MATCHES
 *               bb.hello.com  MATCHES
 *                        com  DOES NOT MATCH
 *                      f.com  DOES NOT MATCH
 *                 ahello.com  DOES NOT MATCH
 *         hello.comhello.com  DOES NOT MATCH
 *
 * Path:                       /my/stuff*
 *                     MATCHES /my/stuff/bedroom
 *                     MATCHES /my/stuff
 *                     MATCHES /my/stuff/
 *              DOES NOT MATCH /my/stuffs
 *              DOES NOT MATCH /my/stuf
 *              DOES NOT MATCH /my/stu/ff
 *
 * i.e. Register
 *
 * A: host:   *example.com  path: /foo/bar
 * B: host:           *com  path: /foo*
 * C: host:              *  path: /foo*
 * D: host:              *  path: *
 *
 * Request hello.example.com/foo/bar     matches A.
 * Request hello.example.com/foo/bar/baz matches B.
 * Request hello.example.com/foo         matches B.
 * Request hello.example.com/foo/        matches B.
 * Request hello.example.com/food        matches D.
 * Request hello.example.org/foo/bar     matches C.
 * Request hello.example.org/index       matched D.
 */
int fohh_http_server_register(struct fohh_http_server *server,
                              const char *host,
                              const char *path,
                              fohh_http_server_request_cb *callback,
                              void *void_cookie,
                              int64_t int_cookie);

/* Handoff version of the above registration */
int fohh_http_server_register_handoff(struct fohh_http_server *server,
                                      const char *host,
                                      const char *path,
                                      fohh_http_server_handoff_cb *callback,
                                      void *void_cookie,
                                      int64_t int_cookie);

int fohh_http_server_update_path_command_req_callback(struct fohh_http_server *server,
                                                        const char *host,
                                                        const char *path,
                                                        fohh_domain_sock_cmd_request_cb *command_req_cb);
/*
 * Check if the specified host/path is already registered.
 *
 * Returns FOHH_RESULT_NO_ERROR if it is registered.
 *
 * Returns FOHH_RESULT_NOT_FOUND if it is not.
 *
 * Sets void_cookie and int_cookie to the registered cookie values if
 * found.
 */
int fohh_http_server_is_registered(struct fohh_http_server *server,
                                   const char *host,
                                   const char *path,
                                   void **void_cookie,
                                   int64_t *int_cookie);

int fohh_http_server_deregister(struct fohh_http_server *server,
                                const char *host,
                                const char *path);

/*
 * User fills up a response structure, and send a response.
 *
 * SYNCHRONOUS version.
 */
int fohh_http_send_sync_response(struct fohh_http_server_connection *connection,
                                 struct fohh_http_response *response);

/*
 * User fills up a response structure, and send a response.
 *
 * my_fohh_thread is the thread ID making the call, or -1 if unknown.
 *
 * CRITICAL: This routine unlocks connection state that has been
 * verified by fohh_http_server_connection_verify_lock.
 * fohh_http_server_connection_verify_lock must have been called with
 * a successful response before calling this routine.
 *
 * Returns: FOHH_RESULT_NO_ERROR on success.
 *
 * FOHH_RESULT_ERR: Could not create response.
 * FOHH_RESULT_BAD_STATE: The connection was probably closed by
 *     timeout behind our back.
 * FOHH_RESULT_WOULD_BLOCK: We could not transfer 'ownership' of this
 *     response to the correct thread.
 *
 * For all error conditions, the connection is closed.
 */
int fohh_http_send_async_response_unlock(int my_fohh_thread,
                                         struct fohh_http_server_connection *connection,
                                         int64_t connection_incarnation,
                                         struct fohh_http_response *response);

/*
 * Cancel an asynchronous request/response.
 *
 * my_fohh_thread is the thread ID making the call, or -1 if unknown.
 *
 * CRITICAL: This routine unlocks connection state that has been
 * verified by fohh_http_server_connection_verify_lock.
 * fohh_http_server_connection_verify_lock must have been called with
 * a successful response before calling this routine.
 *
 * ASYNCHRONOUS USE ONLY!
 */
int fohh_http_cancel_async_response_unlock(int my_fohh_thread,
                                           struct fohh_http_server_connection *connection,
                                           int64_t connection_incarnation);


/*
 * Normalize a string. In this case, removes '%' encoded characters
 * from a URL, replacing them with their ASCII equivalent.
 *
 * There are conditions where this will fail- an error is indicated
 * for such.
 */
int fohh_http_normalize_str(const char *src_str,
                            size_t src_str_len,
                            char *dst_str,
                            size_t dst_str_len);

int fohh_http_denormalize_str(const char *src_str,
                              size_t src_str_len,
                              char *dst_str,
                              size_t dst_str_len);

/*
 * Create a connection to the specified destination.
 *
 * Callback is called on success/failure.
 *
 * This will generally return success unless something is pretty
 * violently wrong.
 *
 * Note: Unlike other fohh modules, fohh_http_client uses zcdns, and
 * one must be passed in for its use.
 *
 * Note: this routine does not limit timeout from zcdns, so timeout_us
 * may be exceeded if DNS takes too long.
 */
struct fohh_http_client *
fohh_http_client_create(struct zcdns *zcdns,
                        SSL_CTX *ssl_ctx,
                        const char *hostname,
                        const char *remote_host,
                        const char *proxy_host,
                        int32_t port_he,
                        int32_t proxy_port_he,
                        int64_t timeout_us,
                        fohh_http_client_create_cb *cb,
                        void *void_cookie,
                        int64_t int_cookie);

void fohh_http_client_destroy_from_another_thread(struct fohh_http_client *client);

/*
 * Create a connection to the specified destination (BLOCKING)
 *
 * Returns:
 *
 * NULL: Check status- it was probably a timeout.
 *
 * Non-NULL: Check status.
 *
 * Note: this routine does may exceed timeout_us in the event that DNS
 * itself is taking too long.
 */
struct fohh_http_client *
fohh_http_client_create_synchronous(struct zcdns *zcdns,
                                    SSL_CTX *ssl_ctx,
                                    const char *hostname,
                                    const char *remote_host,
                                    const char *proxy_host,
                                    int32_t port_he,
                                    int32_t proxy_port_he,
                                    int64_t timeout_us,
                                    enum fohh_http_client_status *status);

/*
 * Make an HTTP request. When the full response has returned, the
 * callback will be called.
 *
 * The standard headers sent are: "host" (from the client) and
 * "content-length" appropriate for the optional 'body'
 *
 * extra_headers can be supplied by the caller to send extra headers
 * along with the request. The format of the headers is standard HTTP,
 * and each header included must also include a \r\n.
 *
 * extra_headers, if it exists, is consumed but not freed.
 *
 * body may be NULL, or have real data.
 *
 * body, if it exists, is consumed but not freed.
 */
int fohh_http_client_request(struct fohh_http_client *client,
                             int64_t timeout_us,
                             enum fohh_http_method method,
                             const char *url,
                             struct evbuffer *extra_headers,
                             const char *content_type,
                             struct evbuffer *body,
                             fohh_http_client_result_cb *cb,
                             void *void_cookie,
                             int64_t int_cookie);

/*
 * Make an HTTP request, but synchronously. This routine completes
 * when the full response has returned, or a timeout or connection
 * error has returned.
 *
 * On return, result_body (if it exists) is owned by the caller and
 * must be freed. (evbuffer_free)
 */
int fohh_http_client_request_synchronous(struct fohh_http_client *client,
                                         int64_t timeout_us,
                                         enum fohh_http_method method,
                                         const char *url,
                                         struct evbuffer *extra_headers,
                                         const char *content_type,
                                         struct evbuffer *body,
                                         enum fohh_http_client_request_status *status,
                                         int *http_status,
                                         struct evbuffer **result_body);


char *fohh_http_client_status_str(enum fohh_http_client_status status);
char *fohh_http_client_request_status_str(enum fohh_http_client_request_status status);
void fohh_add_ext_trusted_certs(struct zcrypt_root_cert_map *trusted_ca_certs, size_t trusted_ca_certs_count);
void fohh_add_ext_trusted_certs_from_file(const char* filename);

/*
 * Get SSL status of a failed connection attempt.
 */
struct fohh_ssl_status *fohh_http_client_get_ssl_status(struct fohh_http_client *client);

/*
 * Get remote address that client is using.
 */
int fohh_http_client_get_remote_addr(struct fohh_http_client *client,
                                     struct sockaddr_storage *remote_addr);

/*
 * Get a good SSL context for doing HTTP client functionality.
 * The "from_file" version allows specifying an external CA file.
 * Otherwise, embedded CAs will be used.
 */
SSL_CTX *fohh_http_client_get_ssl_ctx(void);
int fohh_http_client_add_os_bundle_to_ssl_ctx(SSL_CTX *ssl_ctx, int is_zscaler_os);
SSL_CTX *fohh_http_client_get_ssl_ctx_from_file(const char* filename);
int fohh_http_client_add_certs_to_ssl_ctx_from_file(SSL_CTX *ssl_ctx, const char* filename);
int fohh_http_client_add_trusted_certs_to_ssl_ctx_from_buffer(SSL_CTX *ssl_ctx, const char* buf, size_t data_len);
size_t fohh_get_ext_trusted_certs_count();

/*
 * Simple synchronous fetch, with pretty standard error/message handling.
 *
 * description is only used for logging messages.
 *
 * client, method, url are pretty straightforward.
 *
 * http_expected_status, if not 0, will cause this routine to error
 * unless the resulting http status exactly matches.
 *
 * http_status is the returned http status.
 *
 * if this routine does not error, body will ALWAYS be set (even if
 * zero length) and must be freed by the caller.
 *
 * value_count indicates how many pairs of name/values there are to
 * place into a json oject as part of the request. There must be
 * 2*value_count char* arguments in the argument list.
 *
 * However, if value_count is FOHH_HTTP_CLIENT_FETCH_TIMEOUT_INDICATOR,
 * the next argument is the timeout value to be used for the synchronous
 * fetch.
 */
int fohh_http_client_fetch_synchronous(char *description,
                                       struct fohh_http_client *client,
                                       enum fohh_http_method method,
                                       const char *url,
                                       int http_expected_status,
                                       int *http_status,
                                       struct evbuffer **body,
                                       int value_count,
                                       ...);

/*
 * fohh_http_client_fetch_synchronous_v4
 * This call is used for fetching data from enrollment service usinf V4 version of APIs
 *
 * description - Type of call for logging (Get CSR/ Get Enrollment details etc)
 * client - FOHH http client (already created)
 * method - GET/POST
 * url - API URL
 * http_expected_status - Expected return code from enrollment service - 200 usually
 * http_status - Actual status code received from enrollment service
 *
 * body - Data returned from enrollment service for success/failure cases
 *
 * token - Bearer token needed for fresh enrollment.
 *  Required for fresh enrollment - No Cert available yet
 *  Not required for Reboots/Reenroll/Cert Renewal calls dont need this. Cert based
 *  signature is used instead.
 *
 * value_count - indicates how many pairs of name/values there are to
 *  place into a json oject as part of the request. There must be
 *  2*value_count char* arguments in the argument list.
 *
 *  if value_count is FOHH_HTTP_CLIENT_FETCH_TIMEOUT_INDICATOR,
 *  the next argument is the timeout value to be used for the synchronous
 *  fetch.
 */
int fohh_http_client_fetch_synchronous_v4(char *description,
                                          struct fohh_http_client *client,
                                          enum fohh_http_method method,
                                          const char *url,
                                          int http_expected_status,
                                          int *http_status,
                                          struct evbuffer **body,
                                          const char* token,
                                          int value_count,
                                          ...);


/* FIXME: temp global variable for a work around */
extern int is_zpn_clientd;

int fohh_http_server_get_paths(struct fohh_http_server *server,
                               const char *host,
                               char **path,
                               size_t path_max_len,
                               size_t *path_count);
#endif /* __FOHH_HTTP_H__ */
