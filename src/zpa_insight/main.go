package main

import (
	"fmt"
	"insight/internal/agent"
	"insight/internal/cli"
	"insight/internal/core"
	"insight/internal/engine"
	"insight/internal/remote"
	"os"
	"slices"
	"strings"
	"sync"

	"github.com/spf13/cobra"
)

var TARGETS = []string{"auto", "pcc", "pse", "ac", "none"}

var (
	rootCmd = &cobra.Command{
		Use:  "PROG [flags] [args]",
		Long: "Gain insight into Business Continuity",
		PreRunE: func(Cmd *cobra.Command, args []string) error {
			config := core.GetConfig()

			if !slices.Contains(core.AllDebugLevels, config.DebugLevel) {
				return fmt.Errorf("invalid debug level: %s", config.DebugLevel)
			}
			if !slices.Contains(TARGETS, config.Target.Kind) {
				return fmt.Errorf("invalid target type: %s", config.Target.Kind)
			}

			return nil
		},
		Run: func(Cmd *cobra.Command, args []string) {
			flags := false
			config := core.GetConfig()
			if len(config.ListenOn) > 0 {
				flags = true
				// unimplemented
			}
			if len(config.ConnectTo) > 0 {
				flags = true
				// unimplemented
			}
			if config.BatchMode {
				config.Args = args
			} else if len(args) > 0 {
				config.Args = []string{strings.Join(args, " ")}
			}
			if len(config.Args) > 0 {
				config.BatchMode = true
			}
			if !flags && !config.BatchMode && !config.Interactive {
				// no mode is specified, enter interactive mode by default
				config.Interactive = true
			}
		},
	}
)

func main() {
	config := core.GetConfig()

	rootCmd.Flags().StringVarP(
		&config.DebugLevel, "debug-level", "d", "warning",
		fmt.Sprintf("One of (%s)", strings.Join(core.AllDebugLevels, "|")))

	rootCmd.Flags().StringVarP(
		&config.Target.Kind, "target-type", "t", "auto",
		fmt.Sprintf("One of (%s)", strings.Join(TARGETS, "|")))
	rootCmd.Flags().StringVar(
		&config.Target.Addr, "target-addr", "127.0.0.1",
		"The address where runs debug service")
	rootCmd.Flags().Uint16Var(
		&config.Target.Port, "target-port", 0,
		"The port where runs debug service, auto detect by default")
	rootCmd.Flags().StringVar(
		&config.Target.Cwd, "target-cwd", "",
		"The current working dir of target process, auto detect by default")

	rootCmd.Flags().BoolVarP(
		&config.Interactive, "interactive", "i", false,
		"Explicitly enable interactive mode, auto detect by default")
	rootCmd.Flags().BoolVarP(
		&config.BatchMode, "batch", "b", false,
		"Explicitly enable interactive mode, auto detect by default")
	rootCmd.Flags().StringVarP(
		&config.ListenOn, "listen-on", "l", "",
		"Specify the address for other agents to connect to")
	rootCmd.Flags().StringVarP(
		&config.ConnectTo, "connect-to", "c", "",
		"Specify the address of this agent to connect to")

	_ = rootCmd.Flags().MarkHidden("listen-on")
	_ = rootCmd.Flags().MarkHidden("connect-to")

	if err := rootCmd.Execute(); err != nil {
		fmt.Println("Parse prog arguments error:", err)
		os.Exit(1)
	}

	helpFlag, err := rootCmd.Flags().GetBool("help")
	if err == nil && helpFlag {
		os.Exit(0)
	}

	core.Setup()
	cli.Setup()

	var wg sync.WaitGroup

	wg.Add(1)
	go engine.Run(&wg)

	if config.ListenOn != "" {
		wg.Add(1)
		go agent.Run(&wg, config.ListenOn)
	}

	if config.ConnectTo != "" {
		wg.Add(1)
		go remote.Run(&wg, config.ConnectTo)
	}

	if config.BatchMode || config.Interactive {
		wg.Add(1)
		go cli.Run(&wg, config.Args, config.Interactive)
	}

	wg.Wait()
	core.Logger.Info("Exit")
}
