package cli

import (
	"fmt"
	"insight/internal/analyzer"
	"insight/internal/core"
	"insight/internal/engine"
	"os"
	"slices"
	"strings"
	"text/template"
)

var channelID engine.ChannelID
var nextMessageSeq uint64

var cmdGraph CmdGraph = NewCmdGraph()

var helpCmd = CmdNode{
	help:   "print this help",
	values: []string{"help", "?"},
}

var usageCmd = CmdNode{
	help:   "print usage",
	values: []string{"usage"},
}

var exitCmd = CmdNode{
	help:   "exit the program",
	values: []string{"exit", "quit"},
}

var showCmd = CmdNode{
	help:   "show command",
	values: []string{"show"},
}

var showProfileCmd = CmdNode{
	help:   "show profile",
	values: []string{"profile"},
}

var showStatusCmd = CmdNode{
	help:   "show status of specified object",
	values: []string{"status"},
}

var showStatusSystemCmd = CmdNode{
	help:   "show system status",
	values: []string{"system"},
}

var showStatusCertCmd = CmdNode{
	help:   "show certificate status",
	values: []string{"cert"},
}

var showStatusSQLiteDBCmd = CmdNode{
	help:   "show status of sqlite databases",
	values: []string{"db", "database"},
}

var showStatusBCPCmd = CmdNode{
	help:   "show bcp status",
	values: []string{"bcp", "site"},
}

var showConnectionsCmd = CmdNode{
	help:   "show connections",
	values: []string{"connection"},
}

var showConnectionsClientCmd = CmdNode{
	help:   "show outgoing connections",
	values: []string{"clients"},
}

var showConnectionsServerCmd = CmdNode{
	help:   "show incoming connections",
	values: []string{"servers"},
}

var showConfig = CmdNode{
	help:   "show current configuration",
	values: []string{"config"},
}

var checkCmd = CmdNode{
	help:   "perform check",
	values: []string{"check"},
}

var checkRedirCmd = CmdNode{
	help:   "evaluae pcc redirection",
	values: []string{"redirect"},
}

var checkRedirByIPCmd = CmdNode{
	help:   "evaluate pcc redirection by user IP",
	values: []string{"ip"},
}

var checkRedirIPInputCmd = CmdNode{
	help:     "IP address",
	label:    "<IP-ADDRESS>",
	isRegexp: true,
	values:   []string{`^[\d.a-fA-F:]+$`},
}

var checkRedirByMTNCmd = CmdNode{
	help:   "evaluate pcc redirection trust network",
	values: []string{"trust-network"},
}

var checkRedirMTNInputCmd = CmdNode{
	help:     "name of trust network",
	label:    "<TRUST-NETWORK>",
	minTimes: 1,
	maxTimes: 100,
	isRegexp: true,
	values:   []string{`.*`},
}

var checkNetworkCmd = CmdNode{
	help:   "evaluate network status",
	values: []string{"network"},
}

var checkAppCmd = CmdNode{
	help:   "evaluate the reachability of an application",
	values: []string{"app"},
}

var checkAppInputCmd = CmdNode{
	help:     "URL of the app",
	label:    "<URL>",
	isRegexp: true,
	values:   []string{`.*`},
}

type TemplEntry struct {
	name   string
	format string
	templ  *template.Template
}

const (
	T_SHOW_PROFILE uint = iota
	T_SHOW_SYS_STATS_PROC
	T_SHOW_SYS_STATS_FS
	T_SHOW_SYS_STATS_LOAD
	T_SHOW_CERT_INFO
	T_SHOW_SQLITE_INTEGRITY
	T_SHOW_CONNECTIONS_CLIENTS
	T_SHOW_CONNECTIONS_SERVERS
	T_SHOW_BCP_CONFIG
	T_SHOW_BCP_STATUS
	T_SHOW_REDIRECT_RESULT
	T_SHOW_NETWORK_STATUS
)

var (
	templates = []TemplEntry{
		T_SHOW_PROFILE: {
			name: "show.profile",
			format: `{{"Version:" | printf "%16s"}} {{.Version}}
{{"Uptime:" | printf "%16s"}} {{.Uptime}}
{{"Status:" | printf "%16s"}} {{.Status}}
{{"Instance ID:" | printf "%16s"}} {{.InstanceID}}
{{"Customer ID:" | printf "%16s"}} {{.CustomerID}}
{{"Offline Domain:" | printf "%16s"}} {{.OfflineDomain}}
`},
		T_SHOW_SYS_STATS_PROC: {
			name: "show.sys_stats_proc",
			format: `{{.Name | printf "%24s"}}: CPU {{.CPU | printf "%6.2f%%"}}      RSS {{divf .RSS 1048576 | printf "%6.1fMB"}}      VMS {{divf .VMS 1048576 | printf "%6.1fMB"}}      Uptime {{.Uptime | printf "%d" | duration}}
`},
		T_SHOW_SYS_STATS_FS: {
			name: "show.sys_stats_fs",
			format: `{{.Path | printf "%24s"}}: Used {{divf .Used 1073741824 | printf "%5.2fGB"}}     Total {{divf .Total 1073741824 | printf "%5.2fGB"}}    Percent {{.Percent | printf "%5.1f%%"}}
`},
		T_SHOW_SYS_STATS_LOAD: {
			name: "show.sys_stats_load",
			format: `{{"System Load" | printf "%24s"}}: Load1 {{mulf .Load1 100 | printf "%.2f%%"}}    Load5 {{mulf .Load5 100 | printf "%.2f%%"}}     Load15 {{mulf .Load15 100 | printf "%.2f%%"}}
`},
		T_SHOW_CERT_INFO: {
			name: "show.cert",
			format: `{{"Issuer" | printf "%10s"}}: {{.Cert.Issuer}}
{{"Subject" | printf "%10s"}}: {{.Cert.Subject}}
{{"NotBefore" | printf "%10s"}}: {{.Cert.NotBefore}}
{{"NotAfter" | printf "%10s"}}: {{if le .Lifetime 1296000}}{{.Cert.NotAfter | toString | red}}{{else}}{{.Cert.NotAfter}}{{- end}}
`},
		T_SHOW_SQLITE_INTEGRITY: {
			name: "show.sqlite.integrity",
			format: `Result:
{{- range $key, $value := . }}
{{if eq $value ""}}{{green "PASS"}}{{else}}{{red "FAIL"}}{{end}} {{$key}}
{{- end }}
`},
		T_SHOW_CONNECTIONS_CLIENTS: {
			name: "show.connection.clients",
			format: `    {{if eq .State "fohh_connection_connected" -}}
{{trimPrefix "fohh_connection_" .State | upper | printf "%-16s" | green}}
{{- else -}}
{{trimPrefix "fohh_connection_" .State | upper | printf "%-16s" | red}}
{{- end -}}
:{{.LocalPort}} -> {{.PeerCommonName}}@{{.RemoteAddr}}:{{.RemotePort}}
`},
		T_SHOW_CONNECTIONS_SERVERS: {
			name: "show.connections.servers",
			format: `    {{if eq .State "fohh_connection_connected" -}}
{{trimPrefix "fohh_connection_" .State | upper | printf "%-16s" | green}}
{{- else -}}
{{trimPrefix "fohh_connection_" .State | upper | printf "%-16s" | red}}
{{- end -}}
{{.PeerCommonName}}@{{.RemoteAddr}}:{{.RemotePort}}
`},
		T_SHOW_BCP_CONFIG: {
			name: "show.config.pcc",
			format: `{{printf "%20s: %d" "Customer ID" .Global.CustomerID}}
{{printf "%20s: %d" "Instance ID" .InstanceID}}
{{printf "%20s: %s" "Offline Domain" .Global.OfflineDomain}}
{{printf "%20s: " "Prefer to PCC"}}{{if .Global.SitecPreferred}}true{{else}}false{{end}}
{{printf "%20s: " "Support new user"}}{{if .Global.NewUserSupport}}true{{else}}false{{end}}
{{printf "%20s: " "Send Switch message"}}
{{- if .Global.IsSwitchTimeEnabled -}}
{{.Global.MaxAllowedSwitchtime}} {{.Global.MaxAllowedSwitchtimeUnit | lower}}
{{- else -}}
disabled
{{- end}}
{{range $k1, $site := .Sites}}
Private Cloud "{{$site.Name}}": {{if $site.Enabled}}enabled{{else}}disabled{{end}}, reenroll period is {{$site.ReenrollPeriod}} days
  {{- range $k2, $group := $site.SitecGroups}}
    Cloud Controller Group "{{$group.Name}}": {{if $group.Enabled}}enabled{{else}}disabled{{end}}, {{$group.Location}}
    {{- range $k3, $sitec := $group.SiteControllers}}
      {{if eq $sitec.ID $.InstanceID}}*{{else}} {{end}} {{$sitec.ID}} "{{$sitec.Name}}": {{if $sitec.Enabled}}enabled{{else}}disabled{{end}}
    {{- end }}
  {{- end }}
  {{- range $k2, $group := $site.PBrokerGroups}}
    Service Edge Group "{{$group.Name}}": {{if $group.Enabled}}enabled{{else}}disabled{{end}}, {{$group.Location}}
    {{- range $k3, $pbroker := $group.PBrokers}}
        {{$pbroker.ID}} "{{$pbroker.Name}}": {{if $pbroker.Enabled}}enabled{{else}}disabled{{end}}
    {{- end }}
  {{- end }}
  {{- range $k2, $group := $site.AppConnectorGroups}}
    App Connector Group "{{$group.Name}}": {{if $group.Enabled}}enabled{{else}}disabled{{end}}, {{$group.Location}}
    {{- range $k3, $ac := $group.AppConnectors}}
        {{$ac.ID}} "{{$ac.Name}}": {{if $ac.Enabled}}enabled{{else}}disabled{{end}}
    {{- end }}
  {{- end }}
{{- end }}
`},
		T_SHOW_BCP_STATUS: {
			name: "show.bcp.status",
			format: `
{{- range $cat, $sess := .Sessions}}
{{- if gt (len $sess) 0}}
{{$cat | catString}}:
{{- range $peer, $status := $sess}}
    {{if eq $status.Connected $status.Total -}}
    {{$status.PeerAddr | printf "%-18s" | green}}{{else}}{{$status.PeerAddr | printf "%-18s" | red}}{{end}} {{$status.PeerName}}: {{$status.Connected}} of {{$status.Total}} is connected
    {{- range $i, $fohh := $status.NotReady}}
        * {{trimPrefix "fohh_connection_" .State | upper | printf "%-12s" | red}} {{.LocalAddr}}:{{.LocalPort}} <-> {{.PeerCommonName}}@{{.RemoteAddr}}:{{.RemotePort}} {{.Sni}}
    {{- end}}
{{- end}}
{{- end}}
{{- end}}
{{if .AttachStatus.IsValid}}
{{if .AttachStatus.SitecPreferred}}Prefer to BCP, {{else}}Prefer to Cloud, {{end}}
{{- if .AttachStatus.ToBCP}}BCP is Reachable, {{else}}{{"BCP is Unreachable, " | red}}{{end}}
{{- if .AttachStatus.ToCloud}}Cloud is Reachable{{else}}{{"cloud is Unreachable" | red}}{{end}}
    {{with .AttachStatus.ToBcpFohh}}{{trimPrefix "fohh_connection_" .State | upper | printf "%-19s"}}{{.LocalAddr}}:{{.LocalPort}} <-> {{.PeerCommonName}}@{{.RemoteAddr}}:{{.RemotePort}} {{.Sni}}{{end}}
    {{with .AttachStatus.ToCloudFohh}}{{trimPrefix "fohh_connection_" .State | upper | printf "%-19s"}}{{.LocalAddr}}:{{.LocalPort}} <-> {{.PeerCommonName}}@{{.RemoteAddr}}:{{.RemotePort}} {{.Sni}}{{end}}
{{- end}}
`},
		T_SHOW_REDIRECT_RESULT: {
			name: "show.redir.result",
			format: `The redirect targets:
{{- range $i, $target := . }}
  {{$target}}
{{- end}}
`},
		T_SHOW_NETWORK_STATUS: {
			name: "show.network.result",
			format: `
{{- range $i, $ns := . }}
{{$ns.Domain}}:
  {{- if eq (len $ns.DNS) 0}}
  {{"NXDomain"|red}}
  {{- else}}
  {{- range $j, $ip := $ns.DNS }}
  {{$ip | printf "%-16s"}}
  {{- if index $ns.TCP $j}}{{"TCP Reachable"|green}}{{else}}{{"TCP Unreachable"|red}}{{end}}, Peer CN is {{if ne (len (index $ns.SSL $j)) 0}}{{index $ns.SSL $j|green}}{{else}}{{"Unavailable"|red}}{{end}}
  {{- end}}
  {{- end}}
{{- end}}
`},
	}
)

func requestAndReceive(mtype engine.MessageType, toAgent bool, args any) any {
	seq := nextMessageSeq
	nextMessageSeq++

	msg := engine.RequestAndWait(engine.MessageRequest{FromID: channelID, Seq: seq, MType: mtype, ToAgent: toAgent, Args: args}, 0)
	if msg == nil {
		fmt.Println("Waiting for response timeout")
		return nil
	}

	// TODO: here assume the message tx and rx is in a stream
	if msg.MType == mtype && msg.Seq == seq {
		// TODO: Index/Total check
		return msg.Body
	}

	if msg.MType == engine.MTYPE_ERROR {
		fmt.Println("Received error message:", strings.Join(msg.Body.([]string), "\n"))
		return nil
	}

	fmt.Printf("Received unknown message, type %d, seq %d, expected type %d, expected seq %d\n",
		msg.MType, msg.Seq, mtype, seq)

	return nil
}

func handleHelp(nodes []*CmdNode, args []string) bool {
	// placeholder
	return true
}

func handleUsage(nodes []*CmdNode, args []string) bool {
	cmdGraph.PrintUsage(nodes, args)
	return true
}

func handleExit(nodes []*CmdNode, args []string) bool {
	return false
}

func handleShowProfile(nodes []*CmdNode, args []string) bool {
	if msg := requestAndReceive(engine.MTYPE_GET_PROFILE, false, nil); msg != nil {
		profile := msg.(core.Profile)
		core.UpdateProfile(&profile)
		if err := templates[T_SHOW_PROFILE].templ.Execute(os.Stdout, profile); err != nil {
			fmt.Println(err)
		}
	}
	return true
}

func handleShowSystem(nodes []*CmdNode, args []string) bool {
	if msg := requestAndReceive(engine.MTYPE_GET_SYS_STATS, false, nil); msg != nil {
		for _, sysStats := range msg.(map[string]engine.SysStats) {
			if err := templates[T_SHOW_SYS_STATS_PROC].templ.Execute(os.Stdout, sysStats.MainProc); err != nil {
				fmt.Println(err)
			}
			if err := templates[T_SHOW_SYS_STATS_PROC].templ.Execute(os.Stdout, sysStats.ChildProc); err != nil {
				fmt.Println(err)
			}
			if err := templates[T_SHOW_SYS_STATS_FS].templ.Execute(os.Stdout, sysStats.OptZscaler); err != nil {
				fmt.Println(err)
			}
			if err := templates[T_SHOW_SYS_STATS_LOAD].templ.Execute(os.Stdout, sysStats.SysLoad); err != nil {
				fmt.Println(err)
			}
		}
	}
	return true
}

func handleShowConnections(nodes []*CmdNode, args []string) bool {
	filter := args[len(args)-1]

	if msg := requestAndReceive(engine.MTYPE_GET_CONNECTIONS, false, filter); msg != nil {
		fohhInfo := msg.([]engine.FohhInfo)
		groups := analyzer.FohhGroupBySni(&fohhInfo)
		var templEntry *TemplEntry

		if filter == "clients" {
			templEntry = &templates[T_SHOW_CONNECTIONS_CLIENTS]
		} else {
			templEntry = &templates[T_SHOW_CONNECTIONS_SERVERS]
		}

		for k, v := range groups {
			fmt.Println(k)
			for _, fohh := range v {
				if err := templEntry.templ.Execute(os.Stdout, fohh); err != nil {
					fmt.Println(err)
				}
			}
		}
	}
	return true
}

func handleShowClients(nodes []*CmdNode, args []string) bool {
	return handleShowConnections(nodes, []string{"clients"})
}

func handleShowServers(nodes []*CmdNode, args []string) bool {
	return handleShowConnections(nodes, []string{"servers"})
}

func handleShowConfigFromDB(nodes []*CmdNode, args []string) bool {
	if msg := requestAndReceive(engine.MTYPE_GET_BCP_CONFIG_FROM_DB, false, nil); msg != nil {
		config := msg.(engine.SiteConfig)
		if err := templates[T_SHOW_BCP_CONFIG].templ.Execute(os.Stdout, config); err != nil {
			fmt.Println(err)
		}
	}
	return true
}

func handleShowConfigFromCurl(nodes []*CmdNode, args []string) bool {
	if msg := requestAndReceive(engine.MTYPE_GET_BCP_CONFIG_FROM_CURL, false, nil); msg != nil {
		config := msg.(engine.SiteConfig)
		if err := templates[T_SHOW_BCP_CONFIG].templ.Execute(os.Stdout, config); err != nil {
			fmt.Println(err)
		}
	}
	return true
}

func handleShowCert(nodes []*CmdNode, args []string) bool {
	if msg := requestAndReceive(engine.MTYPE_GET_CERT_INFO, false, nil); msg != nil {
		if certInfo, ok := msg.(engine.CertInfo); ok {
			if err := templates[T_SHOW_CERT_INFO].templ.Execute(os.Stdout, certInfo); err != nil {
				fmt.Println(err)
			}
		}
	}
	return true
}

func handleShowSQLiteDB(nodes []*CmdNode, args []string) bool {
	if msg := requestAndReceive(engine.MTYPE_GET_SQLITE_INTEGRITY, false, nil); msg != nil {
		if results, ok := msg.(map[string]string); ok {
			if err := templates[T_SHOW_SQLITE_INTEGRITY].templ.Execute(os.Stdout, results); err != nil {
				fmt.Println(err)
			}
		}
	}
	return true
}

func handleShowPccStatus(nodes []*CmdNode, args []string) bool {
	var config engine.SiteConfig
	var fohhClients []engine.FohhInfo
	var fohhServers []engine.FohhInfo

	if msg := requestAndReceive(engine.MTYPE_GET_BCP_CONFIG_FROM_DB, false, nil); msg != nil {
		config = msg.(engine.SiteConfig)
	}
	if msg := requestAndReceive(engine.MTYPE_GET_CONNECTIONS, false, "clients"); msg != nil {
		fohhClients = msg.([]engine.FohhInfo)
	}
	if msg := requestAndReceive(engine.MTYPE_GET_CONNECTIONS, false, "servers"); msg != nil {
		fohhServers = msg.([]engine.FohhInfo)
	}

	msg := analyzer.CheckBcpStatus(&config, &fohhClients, &fohhServers)
	if err := templates[T_SHOW_BCP_STATUS].templ.Execute(os.Stdout, msg); err != nil {
		fmt.Println(err)
	}

	return true
}

func handleShowBcpStatus(nodes []*CmdNode, args []string) bool {
	var config engine.SiteConfig
	var fohhClients []engine.FohhInfo
	var fohhServers []engine.FohhInfo

	if msg := requestAndReceive(engine.MTYPE_GET_BCP_CONFIG_FROM_CURL, false, nil); msg != nil {
		config = msg.(engine.SiteConfig)
	}
	if msg := requestAndReceive(engine.MTYPE_GET_CONNECTIONS, false, "clients"); msg != nil {
		fohhClients = msg.([]engine.FohhInfo)
	}
	if msg := requestAndReceive(engine.MTYPE_GET_CONNECTIONS, false, "servers"); msg != nil {
		fohhServers = msg.([]engine.FohhInfo)
	}

	msg := analyzer.CheckBcpStatus(&config, &fohhClients, &fohhServers)
	if err := templates[T_SHOW_BCP_STATUS].templ.Execute(os.Stdout, msg); err != nil {
		fmt.Println(err)
	}

	return true
}

func handleCheckRedirByIP(nodes []*CmdNode, args []string) bool {
	ip := args[len(args)-1]

	if msg := requestAndReceive(engine.MTYPE_GET_REDIRECT_BY_IP, false, ip); msg != nil {
		if results, ok := msg.(([]string)); ok {
			if err := templates[T_SHOW_REDIRECT_RESULT].templ.Execute(os.Stdout, results); err != nil {
				fmt.Println(err)
			}
		}
	}

	return true
}

func handleCheckRedirByMTN(nodes []*CmdNode, args []string) bool {
	idx := slices.Index(nodes, &checkRedirByMTNCmd)
	if idx < 0 {
		return true
	}

	if msg := requestAndReceive(engine.MTYPE_GET_REDIRECT_BY_MTN, false, args[idx+1:]); msg != nil {
		if results, ok := msg.(([]string)); ok {
			if err := templates[T_SHOW_REDIRECT_RESULT].templ.Execute(os.Stdout, results); err != nil {
				fmt.Println(err)
			}
		}
	}

	return true
}

func handleCheckNetwork(nodes []*CmdNode, args []string) bool {
	if msg := requestAndReceive(engine.MTYPE_GET_NETWORK_STATUS, false, nil); msg != nil {
		if results, ok := msg.(([]engine.NetworkStatus)); ok {
			if err := templates[T_SHOW_NETWORK_STATUS].templ.Execute(os.Stdout, results); err != nil {
				fmt.Println(err)
			}
		}
	}

	return true
}

func handleCheckAppStatus(nodes []*CmdNode, args []string) bool {
	url := args[len(args)-1]

	if msg := requestAndReceive(engine.MTYPE_CHECK_APP_STATUS, false, url); msg != nil {
		if result, ok := msg.(string); ok {
			fmt.Println(result)
		}
	}

	return true
}

func buildProfile() {
	if msg := requestAndReceive(engine.MTYPE_GET_PROFILE, false, nil); msg != nil {
		profile := msg.(core.Profile)
		core.UpdateProfile(&profile)
	}
}

func healthCheck() {
}

func execute(line string) bool {
	line = strings.TrimSpace(line)

	if line != "" && !cmdGraph.Execute(strings.Split(line, " ")) {
		return false
	}

	return true
}

func setupCommands() {
	channelID = engine.RegisterChannel("cli")

	cmdGraph.AddCommand(handleHelp, &helpCmd)
	cmdGraph.AddCommand(handleUsage, &usageCmd)
	cmdGraph.AddCommand(handleExit, &exitCmd)

	cmdGraph.AddCommand(handleShowProfile, &showCmd, &showProfileCmd)
	cmdGraph.AddCommand(handleShowSystem, &showCmd, &showStatusCmd, &showStatusSystemCmd)
	cmdGraph.AddCommand(handleShowCert, &showCmd, &showStatusCmd, &showStatusCertCmd)
	cmdGraph.AddPccCommand(handleShowSQLiteDB, &showCmd, &showStatusCmd, &showStatusSQLiteDBCmd)

	cmdGraph.AddPccCommand(handleShowPccStatus, &showCmd, &showStatusCmd, &showStatusBCPCmd)
	cmdGraph.AddPseCommand(handleShowBcpStatus, &showCmd, &showStatusCmd, &showStatusBCPCmd)
	cmdGraph.AddAcCommand(handleShowBcpStatus, &showCmd, &showStatusCmd, &showStatusBCPCmd)

	cmdGraph.AddCommand(handleShowClients, &showCmd, &showConnectionsCmd, &showConnectionsClientCmd)
	cmdGraph.AddCommand(handleShowServers, &showCmd, &showConnectionsCmd, &showConnectionsServerCmd)

	cmdGraph.AddPccCommand(handleShowConfigFromDB, &showCmd, &showConfig)
	cmdGraph.AddPseCommand(handleShowConfigFromCurl, &showCmd, &showConfig)
	cmdGraph.AddAcCommand(handleShowConfigFromCurl, &showCmd, &showConfig)

	if core.GetConfig().Target.Kind == "ac" {
		showConfig.hide = true
	}

	cmdGraph.AddPccCommand(handleCheckRedirByIP, &checkCmd, &checkRedirCmd, &checkRedirByIPCmd, &checkRedirIPInputCmd)
	cmdGraph.AddPccCommand(handleCheckRedirByMTN, &checkCmd, &checkRedirCmd, &checkRedirByMTNCmd, &checkRedirMTNInputCmd)

	cmdGraph.AddPseCommand(handleCheckNetwork, &checkCmd, &checkNetworkCmd)
	cmdGraph.AddAcCommand(handleCheckNetwork, &checkCmd, &checkNetworkCmd)

	cmdGraph.AddAcCommand(handleCheckAppStatus, &checkCmd, &checkAppCmd, &checkAppInputCmd)

	for i, entry := range templates {
		if templ, err := template.New(entry.name).Funcs(templFuncMap).Parse(entry.format); err != nil {
			core.Logger.Panic("Failed to create template for show.sys", err)
		} else {
			templates[i].templ = templ
		}
	}
}

func destroyCommands() {
	engine.UnregisterChannel(channelID)
}
