package cli

import (
	"fmt"
	"hash/fnv"
	"insight/internal/core"
	"io"
	"regexp"
	"strings"
	"sync"
	"unsafe"

	"github.com/chzyer/readline"
	"github.com/fatih/color"
)

type PtrHasher struct {
	hash uint64
}

func NewPtrHasher() PtrHasher {
	return PtrHasher{hash: fnv.New64a().Sum64()}
}

func (ph *PtrHasher) Update(value uintptr) {
	size := int(unsafe.Sizeof(value))
	bytes := make([]byte, 8+size)

	for i := 0; i < 8; i++ {
		bytes[i] = byte(ph.hash >> (i * 8))
	}
	for i := 0; i < size; i++ {
		bytes[i+8] = byte(value >> (i * 8))
	}

	h := fnv.New64a()
	h.Write(bytes)
	ph.hash = h.Sum64()
}

type CmdNode struct {
	label    string
	help     string
	isRegexp bool
	minTimes uint // inclusive
	maxTimes uint // inclusive
	values   []string
	hide     bool
	adj      map[*CmdNode]struct{}
	cookie   any
}

type CmdGraph struct {
	cmdNodes [][]*CmdNode
	root     CmdNode
	funcs    map[uint64]func(nodes []*CmdNode, args []string) bool
}

func NewCmdGraph() CmdGraph {
	return CmdGraph{
		root:  CmdNode{adj: make(map[*CmdNode]struct{})},
		funcs: make(map[uint64]func(nodes []*CmdNode, args []string) bool),
	}
}

func (graph *CmdGraph) AddCommand(callback func([]*CmdNode, []string) bool, start *CmdNode, path ...*CmdNode) {
	cmdNodes := []*CmdNode{}
	cmdNodes = append(cmdNodes, start)
	cmdNodes = append(cmdNodes, path...)
	graph.cmdNodes = append(graph.cmdNodes, cmdNodes)

	if _, exists := graph.root.adj[start]; !exists {
		graph.root.adj[start] = struct{}{}
	}

	cur := start
	hash := NewPtrHasher()
	hash.Update(uintptr(unsafe.Pointer(start)))
	for _, next := range path {
		if cur.adj == nil {
			cur.adj = make(map[*CmdNode]struct{})
		}
		cur.adj[next] = struct{}{}
		cur = next
		hash.Update(uintptr(unsafe.Pointer(next)))
	}
	graph.funcs[hash.hash] = callback
}

func (graph *CmdGraph) AddPccCommand(callback func([]*CmdNode, []string) bool, start *CmdNode, path ...*CmdNode) {
	if core.GetConfig().Target.Kind == "pcc" {
		graph.AddCommand(callback, start, path...)
	}
}

func (graph *CmdGraph) AddPseCommand(callback func([]*CmdNode, []string) bool, start *CmdNode, path ...*CmdNode) {
	if core.GetConfig().Target.Kind == "pse" {
		graph.AddCommand(callback, start, path...)
	}
}

func (graph *CmdGraph) AddAcCommand(callback func([]*CmdNode, []string) bool, start *CmdNode, path ...*CmdNode) {
	if core.GetConfig().Target.Kind == "ac" {
		graph.AddCommand(callback, start, path...)
	}
}

func (graph *CmdGraph) BottomUpTraversal(visitor func(*CmdNode, any), args any) {
	var stack []*CmdNode
	var visited = make(map[*CmdNode]struct{})

	for node := range graph.root.adj {
		stack = append(stack, node)
	}

	for len(stack) > 0 {
		cur := stack[len(stack)-1]

		if _, exists := visited[cur]; exists {
			stack = stack[:len(stack)-1]
			continue
		}

		n := 0
		for next := range cur.adj {
			if _, exists := visited[next]; exists {
				n++
			} else {
				stack = append(stack, next)
			}
		}
		if n == len(cur.adj) {
			visitor(cur, args)
			visited[cur] = struct{}{}
			stack = stack[:len(stack)-1]
		}
	}
}

func (graph *CmdGraph) PrintHelp(path []*CmdNode, args []string) {
	// the nodes in `path` are all matched ones

	var adj map[*CmdNode]struct{}
	if len(path) == 0 {
		adj = graph.root.adj
	} else {
		adj = path[len(path)-1].adj
	}

	var nodes []*CmdNode
	for node := range adj {
		nodes = append(nodes, node)
	}

	var acceptable bool
	hash := NewPtrHasher()
	for _, node := range path {
		hash.Update(uintptr(unsafe.Pointer(node)))
	}
	if _, exists := graph.funcs[hash.hash]; exists {
		acceptable = true
	}

	if len(path) < len(args) && args[len(path)] != "help" && args[len(path)] != "?" {
		cur := len(path)
		fmt.Printf("Invalid command: %s %s<-unexpected--< %s\n",
			strings.Join(args[:cur], " "),
			color.RedString(args[cur]),
			strings.Join(args[cur+1:], " "),
		)
	}

	if len(nodes) > 0 || acceptable {
		fmt.Println("Suggestions:")
		if acceptable {
			if len(path) > 0 {
				fmt.Printf("  %-40s%s\n", "<RETURN>", path[len(path)-1].help)
			}
		}
		for _, node := range nodes {
			if !node.hide {
				var label string
				if node.label != "" {
					label = node.label
				} else {
					label = strings.Join(node.values, " | ")
				}
				fmt.Printf("  %-40s%s\n", label, node.help)
			}
		}
	} else {
		fmt.Println("Help unavailable for this command")
	}
}

func (graph *CmdGraph) PrintUsage(path []*CmdNode, args []string) {
	for _, cmdNodes := range graph.cmdNodes {
		hide := false
		tokens := []string{}
		for _, cmdNode := range cmdNodes {
			var token string
			if cmdNode.label != "" {
				token = cmdNode.label
			} else {
				if len(cmdNode.values) == 1 {
					token = cmdNode.values[0]
				} else {
					token = "{" + strings.Join(cmdNode.values, "|") + "}"
				}
			}
			tokens = append(tokens, token)
			hide = hide || cmdNode.hide
		}
		if !hide {
			fmt.Println(strings.Join(tokens, " "))
			fmt.Println("    ", cmdNodes[len(cmdNodes)-1].help)
		}
	}
}

func (graph *CmdGraph) Execute(args []string) bool {
	curr := &graph.root
	path := []*CmdNode{}
	hash := NewPtrHasher()

	if len(args) == 0 {
		return true
	}

	var helpMode bool
	if args[0] == "help" || args[0] == "?" {
		args = args[1:]
		helpMode = true
	}

	var graphLoop uint

	for i := 0; i < len(args); i += 1 {
		var found *CmdNode

		arg := args[i]

		next := []*CmdNode{}

		if graphLoop > 0 {
			next = append(next, curr)
		}
		for node := range curr.adj {
			next = append(next, node)
		}

		for _, node := range next {
			for _, value := range node.values {
				if !node.isRegexp {
					if value == arg {
						found = node
					}
				}
			}
			if found == nil {
				for _, value := range node.values {
					if node.isRegexp {
						if m, err := regexp.MatchString(value, arg); m && err == nil {
							found = node
						}
					}
				}
			}
			if found != nil {
				break
			}
		}

		if graphLoop > 0 && found == curr {
			graphLoop += 1
			if graphLoop == curr.maxTimes {
				graphLoop = 0
			}
		} else if found != nil {
			if found.maxTimes >= 2 && found.minTimes <= found.maxTimes {
				graphLoop = 1
			}
		}

		if found != nil {
			curr = found
			path = append(path, found)
			if graphLoop <= 1 {
				hash.Update(uintptr(unsafe.Pointer(curr)))
			}
		} else {
			var skipNode *CmdNode
			var skipNodeNum int
			for _, node := range next {
				if node.minTimes == 0 && node.minTimes < node.maxTimes {
					skipNodeNum += 1
					skipNode = node
				}
			}

			if skipNodeNum == 1 {
				curr = skipNode
				i -= 1 // skip the optional node and re-match
				hash.Update(uintptr(unsafe.Pointer(curr)))
				continue
			}

			graph.PrintHelp(path, args)
			return true
		}
	}

	if !helpMode {
		if f, exists := graph.funcs[hash.hash]; exists {
			return f(path, args)
		}
	}

	graph.PrintHelp(path, args)

	return true
}

var commandCompleters []readline.PrefixCompleterInterface

func filterInput(r rune) (rune, bool) {
	switch r {
	case readline.CharCtrlZ:
		return r, false
	}
	return r, true
}

func registerCompletionCallback(node *CmdNode, args any) {
	reset := args.(bool)

	if reset {
		node.cookie = nil
		return
	}

	if node.cookie != nil {
		return
	}

	var children_completers []readline.PrefixCompleterInterface
	var completers []readline.PrefixCompleterInterface

	for next := range node.adj {
		children_completers = append(children_completers, next.cookie.([]readline.PrefixCompleterInterface)...)
	}

	if !node.hide {
		for _, value := range node.values {
			completers = append(completers, readline.PcItem(value, children_completers...))
		}
	}

	node.cookie = completers
}

func registerCompletions() {
	cmdGraph.BottomUpTraversal(registerCompletionCallback, false)

	for node := range cmdGraph.root.adj {
		commandCompleters = append(commandCompleters, node.cookie.([]readline.PrefixCompleterInterface)...)
	}

	cmdGraph.BottomUpTraversal(registerCompletionCallback, true)
}

func Run(wg *sync.WaitGroup, preCmds []string, interactive bool) {
	core.Logger.Debug("Start cli")

	defer destroy()
	defer wg.Done()

	buildProfile()

	for i := range preCmds {
		if !execute(preCmds[i]) {
			break
		}
	}

	healthCheck()

	if !interactive {
		return
	}

	rl, err := readline.NewEx(&readline.Config{
		Prompt:          fmt.Sprintf("%s> ", core.GetConfig().Target.Kind),
		AutoComplete:    readline.NewPrefixCompleter(commandCompleters...),
		InterruptPrompt: "^C",
		EOFPrompt:       "exit",

		HistorySearchFold:   true,
		FuncFilterInputRune: filterInput,
	})

	if err != nil {
		core.Logger.Error("Failed to create CLI shell")
	}

	defer func() { _ = rl.Close() }()

	rl.CaptureExitSignal()

	for {
		line, err := rl.Readline()

		if err == readline.ErrInterrupt || err == io.EOF {
			if len(line) == 0 {
				break
			} else {
				continue
			}
		}

		if !execute(line) {
			break
		}
	}
}

func Setup() {
	setupCommands()
	registerCompletions()
}

func destroy() {
	destroyCommands()
}
