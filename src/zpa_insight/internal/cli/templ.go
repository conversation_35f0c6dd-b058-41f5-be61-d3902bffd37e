package cli

import (
	"strings"
	"text/template"

	"github.com/Masterminds/sprig/v3"
	"github.com/fatih/color"
)

func black(text string) string {
	return color.BlackString(text)
}

func red(text string) string {
	return color.RedString(text)
}

func green(text string) string {
	return color.GreenString(text)
}

func yellow(text string) string {
	return color.YellowString(text)
}

func blue(text string) string {
	return color.BlueString(text)
}

func magenta(text string) string {
	return color.MagentaString(text)
}

func cyan(text string) string {
	return color.CyanString(text)
}

func white(text string) string {
	return color.WhiteString(text)
}

func catString(cat int) string {
	desc := []string{
		"To Cloud",
		"To Site Controller",
		"To Private Service Edige",
		"To App Connector",
		"To Unlabeled Destination",
		"From Site Controller",
		"From Private Service Edige",
		"From App Connector",
		"From Zscaler Client Connector",
		"From Unlabeled Destination",
	}
	if cat < 0 || cat >= len(desc) {
		return "Code BUG"
	}
	return desc[cat]
}

var templFuncMap = template.FuncMap{
	"black":     black,
	"red":       red,
	"green":     green,
	"yellow":    yellow,
	"blue":      blue,
	"magenta":   magenta,
	"cyan":      cyan,
	"white":     white,
	"contains":  strings.Contains,
	"hasPrefix": strings.HasPrefix,
	"hasSuffix": strings.HasSuffix,
	"catString": catString,
}

func init() {
	for k, v := range sprig.FuncMap() {
		if _, exists := templFuncMap[k]; !exists {
			templFuncMap[k] = v
		}
	}
}
