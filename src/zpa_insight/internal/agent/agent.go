package agent

import (
	"insight/internal/core"
	"insight/internal/engine"
	"sync"
)

var channelID engine.ChannelID

type EgressChannel = chan engine.MessageRequest
type IngressChannel = chan engine.MessageResponse

type Agent struct {
	Egress  EgressChannel
	Ingress IngressChannel
}

// var aConnections map[string]net.Conn = make(map[string]net.Conn)

func Run(wg *sync.WaitGroup, listenOn string) {
	core.Logger.Debug("Start remote")

	defer destroy()
	defer wg.Done()

	channelID = engine.RegisterChannel("agent")
}

func destroy() {
	engine.UnregisterChannel(channelID)
}
