package engine

import (
	"fmt"
	"insight/internal/core"
	"sync"
	"time"
)

type ChannelID = uint64

type HostChannel = chan MessageRequest
type UserChannel = chan MessageResponse

type UserChannelEntity struct {
	name    string
	enabled bool
	channel UserChannel
}

const (
	DEFAULT_WAIT_RESPONSE_SECONDS = 30
)

var hostChannel HostChannel = make(HostChannel)
var userChannels []UserChannelEntity = []UserChannelEntity{{name: "", enabled: false, channel: nil}}
var nextUserChannelID ChannelID = 1

func getHostChannel() HostChannel {
	return hostChannel
}

func getUserChannel(id ChannelID) UserChannel {
	if int(id) < len(userChannels) {
		if userChannels[id].enabled {
			return userChannels[id].channel
		}
	}
	return nil
}

func RegisterChannel(name string) ChannelID {
	id := nextUserChannelID
	nextUserChannelID++
	userChannels = append(userChannels, UserChannelEntity{name: name, enabled: true, channel: make(UserChannel)})
	return id
}

func UnregisterChannel(id ChannelID) {
	if int(id) < len(userChannels) {
		userChannels[id].enabled = false
		getHostChannel() <- MessageRequest{FromID: id, MType: MTYPE_QUIT}
	}
}

func RequestAndWait(msg MessageRequest, timeout uint) *MessageResponse {
	if timeout <= 0 {
		timeout = DEFAULT_WAIT_RESPONSE_SECONDS
	}

	id := msg.FromID

	if int(id) >= len(userChannels) || !userChannels[id].enabled {
		core.Logger.Warnf("Can't send message from %s, since it is not registered", userChannels[id].name)
		return nil
	}

	if len(userChannels[id].channel) != 0 {
		core.Logger.Warn("Channel is not empty, probably it takes too long time to process the previous request, discard them...")
		for len(userChannels[id].channel) > 0 {
			msg := <-userChannels[id].channel
			core.Logger.Warn("DISCARD", msg)
		}
	}

	getHostChannel() <- msg

	select {
	case msg := <-userChannels[id].channel:
		return &msg
	case <-time.After(time.Duration(timeout) * time.Second):
		return nil
	}
}

func replyMessage(id ChannelID, msg *MessageResponse) {
	core.Logger.Debugf("Reply message: type %d, seq %d", msg.MType, msg.Seq)
	core.Logger.Tracef("Message: %v", msg)
	getUserChannel(id) <- *msg
}

func replyError(msg MessageRequest, errors []string) {
	core.Logger.Debugf("Reply error message: seq %d, errors: %v", msg.Seq, errors)
	getUserChannel(msg.FromID) <- MessageResponse{Seq: msg.Seq, MType: MTYPE_ERROR, Total: 1, Index: 0, Body: errors}
}

func Run(wg *sync.WaitGroup) {
	core.Logger.Debug("Start engine")

	defer wg.Done()

	for {
		total := 0
		for _, channel := range userChannels {
			if channel.enabled {
				total++
			}
		}
		if total == 0 {
			break
		}

		msg := <-getHostChannel()

		core.Logger.Debugf("Received message: type %d, seq %d", msg.MType, msg.Seq)

		switch msg.MType {
		case MTYPE_QUIT:
			// this is a one-way message, do nothing
		case MTYPE_GET_PROFILE:
			handleGetProfile(msg)
		case MTYPE_GET_SYS_STATS:
			handleGetSysStats(msg)
		case MTYPE_GET_CERT_INFO:
			handleGetCertInfo(msg)
		case MTYPE_GET_SQLITE_INTEGRITY:
			handleGetSQLiteIntegrity(msg)
		case MTYPE_GET_CONNECTIONS:
			handleGetConnections(msg)
		case MTYPE_GET_BCP_CONFIG_FROM_DB:
			handleGetBcpConfigFromDB(msg)
		case MTYPE_GET_BCP_CONFIG_FROM_CURL:
			handleGetBcpConfigFromCurl(msg)
		case MTYPE_GET_REDIRECT_BY_IP:
			handleGetRedirectByIP(msg)
		case MTYPE_GET_REDIRECT_BY_MTN:
			handleGetRedirectByMTN(msg)
		case MTYPE_GET_NETWORK_STATUS:
			handleGetNetworkStatus(msg)
		case MTYPE_CHECK_APP_STATUS:
			handleCheckAppStatus(msg)
		default:
			replyError(msg, []string{fmt.Sprintf("Unknown message type %d", msg.MType)})
		}
	}
}
