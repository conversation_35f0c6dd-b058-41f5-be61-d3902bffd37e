package engine

import (
	"crypto/tls"
	"crypto/x509"
	"encoding/json"
	"encoding/pem"
	"errors"
	"fmt"
	"insight/internal/core"
	"insight/internal/utils"
	"insight/internal/utils/sqlite"
	"io"
	"net"
	"net/http"
	"os"
	"path/filepath"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/shirou/gopsutil/disk"
	"github.com/shirou/gopsutil/load"
	"github.com/shirou/gopsutil/process"
)

type PatternEntry struct {
	pat  string
	creg *regexp.Regexp
}

const (
	PAT_VERSION uint = iota
	PAT_FOHH_SMALL
	PAT_INSTANCE_ID_A
	PAT_INSTANCE_ID_B
	PAT_CUSTOMER_ID_A
	PAT_CUSTOMER_ID_B
	PAT_OFFLINE_DOMAIN_A
	PAT_OFFLINE_DOMAIN_B
	PAT_BALANCE_REDIRECT_A
	PAT_BALANCE_REDIRECT_B
)

var (
	patterns = []PatternEntry{
		PAT_VERSION:            {pat: `^(\d+)\.(\d+)\.(\d+)-`},
		PAT_FOHH_SMALL:         {pat: `^\[([\w.]+)\]:(\d+);([^:]+):\[([\w.]+)\]:(\d+);\d+`},
		PAT_INSTANCE_ID_A:      {pat: `^\s*instance_gid:\s*(\d+)\s*`},
		PAT_INSTANCE_ID_B:      {pat: `^\s*site gid:\s*(\d+)\s*`},
		PAT_CUSTOMER_ID_A:      {pat: `^\s*customer_gid:\s*(\d+)\s*`},
		PAT_CUSTOMER_ID_B:      {pat: `^\s*customer gid:\s*(\d+)\s*`},
		PAT_OFFLINE_DOMAIN_A:   {pat: `^\s*offline_domain:\s*([\w\-.]+)\s*`},
		PAT_OFFLINE_DOMAIN_B:   {pat: `^\s*site offline domain:\s*([\w\-.]+)\s*`},
		PAT_BALANCE_REDIRECT_A: {pat: `^\s*Final Selection:\s*(\d+) brokers`},
		PAT_BALANCE_REDIRECT_B: {pat: `^\s*\d+.\s*([\w.]+)`},
	}
)

func init() {
	for i, entry := range patterns {
		if creg, err := regexp.Compile(entry.pat); err != nil {
			core.Logger.Panic("Failed to compiled pattern for", entry.pat)
		} else {
			patterns[i].creg = creg
		}
	}
}

func convertToSeconds(value uint64, unit string) uint64 {
	units := map[string]uint64{
		"SECONDS": 1, "MINUTES": 60, "HOURS": 60 * 60, "DAYS": 24 * 60 * 60, "WEEKS": 7 * 24 * 60 * 60,
	}
	var factor uint64 = 60 // default unit is MINUTES
	for k, v := range units {
		if strings.EqualFold(k, unit) {
			factor = v
		}
	}
	return value * factor
}

func fillProcessStats(procStats *ProcessStats, proc *process.Process) {
	procStats.Pid = uint64(proc.Pid)
	if t, err := proc.CreateTime(); err == nil {
		procStats.Uptime = uint64(time.Now().UnixMilli()-t) / 1000
	}
	if cwd, err := proc.Cwd(); err == nil {
		procStats.Cwd = cwd
	}
	if percent, err := proc.CPUPercent(); err == nil {
		procStats.CPU = percent
	}
	if mi, err := proc.MemoryInfo(); err == nil {
		procStats.RSS = mi.RSS
		procStats.VMS = mi.VMS
	}
	if n, err := proc.NumFDs(); err == nil {
		procStats.FDs = uint64(n)
	}
	if n, err := proc.NumThreads(); err == nil {
		procStats.Threads = uint64(n)
	}
}

func httpGet(url string) (string, error) {
	if resp, err := http.Get(url); err == nil {
		defer func() { _ = resp.Body.Close() }()
		body, err := io.ReadAll(resp.Body)
		if err != nil {
			return "", fmt.Errorf("failed to read http response from %s: %v", url, err)
		}
		return string(body), nil
	} else {
		return "", fmt.Errorf("failed to http.Get('%s'): %v", url, err)
	}
}

func queryTablesFromCurl(table string, column string, value string) ([]string, error) {
	var wally string
	if core.GetConfig().Target.Kind == "pcc" {
		wally = "sitec_wally_cfg"
	} else if core.GetConfig().Target.Kind == "pse" {
		wally = "Private_Broker_Wally"
	} else if core.GetConfig().Target.Kind == "ac" {
		wally = "Assistant_Wally"
	} else {
		return nil, fmt.Errorf("inavlid target kind: %s", core.GetConfig().Target.Kind)
	}

	url := fmt.Sprintf("http://%s:%d/wally/%s/lookup?table=%s&column=%s&rows",
		core.GetConfig().Target.Addr, core.GetConfig().Target.Port, wally, table, column)
	if value != "" {
		url += "&value=" + value
	}

	core.Logger.Tracef("Query table from curl: %s", url)

	if body, err := httpGet(url); err == nil {
		rows := strings.Split(body, "\n")
		var result []string
		for _, row := range rows {
			if len(row) > 0 && row[0] == '{' {
				result = append(result, row)
			}
		}
		return result, nil
	} else {
		return nil, err
	}
}

func unpickleRowsFromCurl[TableJSON CommonTableJSON](table string, column string, value string) ([]TableJSON, error) {
	if rows, err := queryTablesFromCurl(table, column, value); err != nil {
		return nil, fmt.Errorf("failed to curl %s:%s for %s, error: %v", table, column, value, err)
	} else {
		var result []TableJSON
		for _, row := range rows {
			var obj TableJSON
			if err := json.Unmarshal([]byte(row), &obj); err != nil {
				return nil, fmt.Errorf("failed to parse %s:%s for %s, error: %v", table, column, value, err)
			}
			if !obj.IsDeleted() {
				result = append(result, obj)
			}
		}
		return result, nil
	}
}

func getInstanceId() (uint64, error) {
	var url string
	var patID uint

	if core.GetConfig().Target.Kind == "pcc" {
		url = fmt.Sprintf("http://%s:%d/sitec/config", core.GetConfig().Target.Addr, core.GetConfig().Target.Port)
		patID = PAT_INSTANCE_ID_A
	} else if core.GetConfig().Target.Kind == "pse" {
		url = fmt.Sprintf("http://%s:%d/pbroker/site/config", core.GetConfig().Target.Addr, core.GetConfig().Target.Port)
		patID = PAT_INSTANCE_ID_A
	} else {
		url = fmt.Sprintf("http://%s:%d/assistant/cfg/site/info", core.GetConfig().Target.Addr, core.GetConfig().Target.Port)
		patID = PAT_INSTANCE_ID_B
	}

	if body, err := httpGet(url); err == nil {
		lines := strings.Split(body, "\n")
		for _, line := range lines {
			m := patterns[patID].creg.FindStringSubmatch(line)
			if m != nil {
				n, _ := strconv.Atoi(m[1])
				return uint64(n), nil
			}
		}
		return 0, fmt.Errorf("failed to match instance ID in response from %s", url)
	} else {
		return 0, fmt.Errorf("failed to get instance ID, error: %v", err)
	}
}

func getCustomerId() (uint64, error) {
	var url string
	var patID uint

	if core.GetConfig().Target.Kind == "pcc" {
		url = fmt.Sprintf("http://%s:%d/sitec/config", core.GetConfig().Target.Addr, core.GetConfig().Target.Port)
		patID = PAT_CUSTOMER_ID_A
	} else if core.GetConfig().Target.Kind == "pse" {
		url = fmt.Sprintf("http://%s:%d/pbroker/site/config", core.GetConfig().Target.Addr, core.GetConfig().Target.Port)
		patID = PAT_CUSTOMER_ID_A
	} else {
		url = fmt.Sprintf("http://%s:%d/assistant/cfg/site/info", core.GetConfig().Target.Addr, core.GetConfig().Target.Port)
		patID = PAT_CUSTOMER_ID_B
	}

	if body, err := httpGet(url); err == nil {
		lines := strings.Split(body, "\n")
		for _, line := range lines {
			m := patterns[patID].creg.FindStringSubmatch(line)
			if m != nil {
				n, _ := strconv.Atoi(m[1])
				return uint64(n), nil
			}
		}
		return 0, fmt.Errorf("failed to match customer ID in response from %s", url)
	} else {
		return 0, fmt.Errorf("failed to get customer ID, error: %v", err)
	}
}

func getOfflineDomain() (string, error) {
	var url string
	var patID uint

	if core.GetConfig().Target.Kind == "pcc" {
		url = fmt.Sprintf("http://%s:%d/sitec/config", core.GetConfig().Target.Addr, core.GetConfig().Target.Port)
		patID = PAT_OFFLINE_DOMAIN_A
	} else if core.GetConfig().Target.Kind == "pse" {
		url = fmt.Sprintf("http://%s:%d/pbroker/site/config", core.GetConfig().Target.Addr, core.GetConfig().Target.Port)
		patID = PAT_OFFLINE_DOMAIN_A
	} else {
		url = fmt.Sprintf("http://%s:%d/assistant/cfg/site/info", core.GetConfig().Target.Addr, core.GetConfig().Target.Port)
		patID = PAT_OFFLINE_DOMAIN_B
	}

	if body, err := httpGet(url); err == nil {
		lines := strings.Split(body, "\n")
		for _, line := range lines {
			m := patterns[patID].creg.FindStringSubmatch(line)
			if m != nil {
				return m[1], nil
			}
		}
		return "", fmt.Errorf("failed to match offline domain in response from %s", url)
	} else {
		return "", fmt.Errorf("failed to get offline domain, error: %v", err)
	}
}

func handleGetProfile(msg MessageRequest) {
	var profile core.Profile
	var err error

	profile.Version, err = httpGet(fmt.Sprintf("http://%s:%d/version", core.GetConfig().Target.Addr, core.GetConfig().Target.Port))
	if err != nil {
		core.Logger.Debug("Failed to get Version when getting profile")
	}
	profile.Version = strings.TrimSpace(profile.Version)
	version := patterns[PAT_VERSION].creg.FindStringSubmatch(profile.Version)
	if version != nil {
		profile.MajorVersion, _ = strconv.Atoi(version[1])
		profile.MinorVersion, _ = strconv.Atoi(version[2])
		profile.PatchVersion, _ = strconv.Atoi(version[3])
	}

	profile.Uptime, err = httpGet(fmt.Sprintf("http://%s:%d/uptime", core.GetConfig().Target.Addr, core.GetConfig().Target.Port))
	if err != nil {
		core.Logger.Debug("Failed to get Uptime when getting profile")
	}
	profile.Uptime = strings.TrimSpace(profile.Uptime)

	profile.Status, err = httpGet(fmt.Sprintf("http://%s:%d/app/status", core.GetConfig().Target.Addr, core.GetConfig().Target.Port))
	if err != nil {
		core.Logger.Debug("Failed to get Status when getting profile")
	}
	profile.Status = strings.TrimSpace(profile.Status)

	profile.InstanceID, err = getInstanceId()
	if err != nil {
		core.Logger.Debug("Failed to get Instance ID when getting profile")
	}

	profile.CustomerID, err = getCustomerId()
	if err != nil {
		core.Logger.Debug("Failed to get Customer ID when getting profile")
	}

	profile.OfflineDomain, err = getOfflineDomain()
	if err != nil {
		core.Logger.Debug("Failed to get Offline Domain when getting profile")
	}

	replyMessage(msg.FromID, &MessageResponse{Seq: msg.Seq, MType: msg.MType, Total: 1, Index: 0, Body: profile})
}

func handleGetSysStats(msg MessageRequest) {
	kind := core.GetConfig().Target.Kind
	localSysStats := SysStats{
		MainProc:  ProcessStats{Pid: 0, Name: core.TARGETS[kind].Bin},
		ChildProc: ProcessStats{Pid: 0, Name: core.TARGETS[kind].Bin + "-child"},
	}

	if procs, err := process.Processes(); err == nil {
		for _, proc := range procs {
			if name, err := proc.Name(); err == nil {
				switch name {
				case localSysStats.MainProc.Name:
					fillProcessStats(&localSysStats.MainProc, proc)
				case localSysStats.ChildProc.Name, "image.bin":
					fillProcessStats(&localSysStats.ChildProc, proc)
				}
			}
		}
	}

	if usage, err := disk.Usage("/opt/zscaler"); err == nil {
		localSysStats.OptZscaler.Path = usage.Path
		localSysStats.OptZscaler.Total = usage.Total
		localSysStats.OptZscaler.Used = usage.Used
		localSysStats.OptZscaler.Percent = usage.UsedPercent
	}

	if load, err := load.Avg(); err == nil {
		localSysStats.SysLoad.Load1 = load.Load1
		localSysStats.SysLoad.Load5 = load.Load5
		localSysStats.SysLoad.Load15 = load.Load15
	}

	sysStats := map[string]SysStats{
		"localhost": localSysStats,
	}

	replyMessage(msg.FromID, &MessageResponse{Seq: msg.Seq, MType: msg.MType, Total: 1, Index: 0, Body: sysStats})
}

func handleGetCertInfo(msg MessageRequest) {
	cwd := core.GetConfig().Target.Cwd
	path := filepath.Join(cwd, "cert.pem")
	if data, err := os.ReadFile(path); err != nil {
		replyError(msg, []string{fmt.Sprintf("Failed to open '%s', error: %v", path, err)})
	} else {
		certInfo := CertInfo{Path: path}
		block, _ := pem.Decode(data)
		if block == nil || block.Type != "CERTIFICATE" {
			replyError(msg, []string{fmt.Sprintf("'%s' is malformed", path)})
			return
		}
		cert, err := x509.ParseCertificate(block.Bytes)
		if err != nil {
			replyError(msg, []string{fmt.Sprintf("'%s' is invalid certificate, error: %v", path, err)})
			return
		}
		certInfo.Cert = cert
		certInfo.Lifetime = int64(time.Until(cert.NotAfter))
		replyMessage(msg.FromID, &MessageResponse{Seq: msg.Seq, MType: msg.MType, Total: 1, Index: 0, Body: certInfo})
	}
}

func handleGetConnections(msg MessageRequest) {
	connections := []FohhInfo{}

	tag := msg.Args.(string)
	url := fmt.Sprintf("http://%s:%d/fohh/%s?small", core.GetConfig().Target.Addr, core.GetConfig().Target.Port, tag)

	if body, err := httpGet(url); err == nil {
		var fohhs [][]string
		if err := json.Unmarshal([]byte(body), &fohhs); err != nil {
			replyError(msg, []string{fmt.Sprintf("Failed to parse response for mtype %d, error: %v", msg.MType, err)})
			return
		}
		for _, fohh := range fohhs {
			desc := patterns[PAT_FOHH_SMALL].creg.FindStringSubmatch(fohh[0])
			if desc == nil {
				replyError(msg, []string{fmt.Sprintf("Failed to parse description '%s' for mtype %d", fohh[0], msg.MType)})
				return
			}
			var fohhInfo FohhInfo
			if n, err := strconv.Atoi(desc[2]); err == nil {
				fohhInfo.LocalPort = uint16(n)
			}
			if n, err := strconv.Atoi(desc[5]); err == nil {
				fohhInfo.RemotePort = uint16(n)
			}
			fohhInfo.LocalAddr = desc[1]
			fohhInfo.RemoteAddr = desc[4]
			fohhInfo.PeerDomainName = fohh[1]
			fohhInfo.PeerCommonName = desc[3]
			fohhInfo.Sni = fohh[3]
			fohhInfo.State = fohh[2]
			connections = append(connections, fohhInfo)
		}
		replyMessage(msg.FromID, &MessageResponse{Seq: msg.Seq, MType: msg.MType, Total: 1, Index: 0, Body: connections})
	} else {
		replyError(msg, []string{fmt.Sprintf("Failed to get connections, mtype %d, error: %v", msg.MType, err)})
	}
}

func doGetBcpConfigFromDB() (*SiteConfig, error) {
	var config SiteConfig

	config.InstanceID = core.GetProfile().InstanceID

	cwd := core.GetConfig().Target.Cwd
	if files, err := utils.FindFiles(cwd, `^i\.0\.[0-9]r?$`); err != nil || len(files) == 0 {
		return nil, fmt.Errorf("failed to find database file in dir %s, error: %v", cwd, err)
	} else {
		file := files[0]
		if err := sqlite.Get(file, &config.Global, "SELECT * FROM zpn_ddil_config WHERE deleted=0 LIMIT 1"); err != nil {
			return nil, fmt.Errorf("failed to query zpn_ddil_config, error: %v", err)
		}

		config.Global.MaxAllowedDowntimeInSecs = convertToSeconds(config.Global.MaxAllowedDowntime, config.Global.MaxAllowedDowntimeUnit)
		config.Global.MaxAllowedSwitchtimeInSecs = convertToSeconds(config.Global.MaxAllowedSwitchtime, config.Global.MaxAllowedSwitchtimeUnit)

		if err := sqlite.Select(
			file, &config.Sites,
			"SELECT * FROM zpn_site WHERE deleted=0 AND customer_gid=?",
			config.Global.CustomerID,
		); err != nil {
			return nil, fmt.Errorf("failed to query zpn_site, error: %v", err)
		}

		for i, site := range config.Sites {
			if err := sqlite.Select(
				file, &config.Sites[i].SitecGroups,
				"SELECT * FROM zpn_site_controller_group WHERE deleted=0 AND customer_gid=? AND site_gid=?",
				config.Global.CustomerID, site.ID,
			); err != nil {
				return nil, fmt.Errorf("failed to query zpn_site_controller_group, error: %v", err)
			}

			for j, group := range config.Sites[i].SitecGroups {
				if err := sqlite.Select(
					file, &config.Sites[i].SitecGroups[j].SiteControllers,
					`SELECT s.* FROM zpn_site_controller_to_group AS r, zpn_site_controller AS s
					 WHERE r.deleted=0 AND s.deleted=0 AND r.customer_gid=? AND s.customer_gid=?
						   AND r.site_controller_group_gid=? AND r.site_controller_gid=s.gid`,
					config.Global.CustomerID, config.Global.CustomerID, group.ID,
				); err != nil {
					return nil, fmt.Errorf("failed to query zpn_site_controller_to_group, error: %v", err)
				}
			}

			if err := sqlite.Select(
				file, &config.Sites[i].PBrokerGroups,
				"SELECT * FROM zpn_private_broker_group WHERE deleted=0 AND customer_gid=? AND site_gid=?",
				config.Global.CustomerID, site.ID,
			); err != nil {
				return nil, fmt.Errorf("failed to query zpn_private_broker_group, error: %v", err)
			}

			for j, group := range config.Sites[i].PBrokerGroups {
				if err := sqlite.Select(
					file, &config.Sites[i].PBrokerGroups[j].PBrokers,
					`SELECT p.* FROM zpn_private_broker_to_group AS r, zpn_private_broker AS p
					 WHERE r.deleted=0 AND p.deleted=0 AND r.customer_gid=? AND p.customer_gid=?
						   AND r.private_broker_group_gid=? AND r.private_broker_gid=p.gid`,
					config.Global.CustomerID, config.Global.CustomerID, group.ID,
				); err != nil {
					return nil, fmt.Errorf("failed to query zpn_private_broker, error: %v", err)
				}
			}

			if err := sqlite.Select(
				file, &config.Sites[i].AppConnectorGroups,
				"SELECT * FROM zpn_assistant_group WHERE deleted=0 AND customer_gid=? AND site_gid=?",
				config.Global.CustomerID, site.ID,
			); err != nil {
				return nil, fmt.Errorf("failed to query zpn_assistant_group, error: %v", err)
			}

			for j, group := range config.Sites[i].AppConnectorGroups {
				if err := sqlite.Select(
					file, &config.Sites[i].AppConnectorGroups[j].AppConnectors,
					`SELECT a.* FROM zpn_assistantgroup_assistant_relation AS r, zpn_assistant AS a
					 WHERE r.deleted=0 AND a.deleted=0 AND r.customer_gid=? AND r.customer_gid=?
						   AND r.assistant_group_id=? AND r.assistant_id=a.gid`,
					config.Global.CustomerID, config.Global.CustomerID, group.ID,
				); err != nil {
					return nil, fmt.Errorf("failed to query zpn_assistant, error: %v", err)
				}
			}
		}
		return &config, nil
	}
}

func handleGetBcpConfigFromDB(msg MessageRequest) {
	if config, err := doGetBcpConfigFromDB(); err != nil {
		replyError(msg, []string{err.Error()})
		return
	} else {
		replyMessage(msg.FromID, &MessageResponse{Seq: msg.Seq, MType: msg.MType, Total: 1, Index: 0, Body: *config})
	}
}

func fetchGroupsAndInstancesFromCurl[G CommonTableJSON, ToG CommonTableJSON, I CommonTableJSON](
	customerID string, group string, toGroup string, instance string) ([]G, []ToG, []I, error) {

	groups, err := unpickleRowsFromCurl[G](group, "customer_gid", customerID)
	if err != nil {
		return nil, nil, nil, err
	}

	toGroups, err := unpickleRowsFromCurl[ToG](toGroup, "customer_gid", customerID)
	if err != nil {
		return nil, nil, nil, err
	}

	instances, err := unpickleRowsFromCurl[I](instance, "customer_gid", customerID)
	if err != nil {
		return nil, nil, nil, err
	}

	return groups, toGroups, instances, nil
}

func doGetBcpConfigFromCurl() (*SiteConfig, error) {
	var config SiteConfig

	config.InstanceID = core.GetProfile().InstanceID

	if objs, err := unpickleRowsFromCurl[DDILTableJSON]("zpn_ddil_config", "customer_gid", ""); err != nil {
		return nil, fmt.Errorf("failed to unpickle, error: %v", err)
	} else {
		if len(objs) == 0 {
			return nil, errors.New("no valid rows in zpn_ddil_config")
		}
		config.Global = objs[0].Value
		config.Global.MaxAllowedDowntimeInSecs =
			convertToSeconds(config.Global.MaxAllowedDowntime, config.Global.MaxAllowedDowntimeUnit)
		config.Global.MaxAllowedSwitchtimeInSecs =
			convertToSeconds(config.Global.MaxAllowedSwitchtime, config.Global.MaxAllowedSwitchtimeUnit)
	}

	customerID := strconv.Itoa(int(config.Global.CustomerID))

	if objs, err := unpickleRowsFromCurl[SiteTableJSON]("zpn_site", "customer_gid", customerID); err != nil {
		return nil, fmt.Errorf("failed to unpickle, error: %v", err)
	} else {
		for _, obj := range objs {
			config.Sites = append(config.Sites, obj.Value)
		}
	}

	sitecGroups, sitecToGroups, sitecs, err := fetchGroupsAndInstancesFromCurl[SitecGroupTableJSON, SitecToGroupTableJSON, SitecTableJSON](
		customerID, "zpn_site_controller_group", "zpn_site_controller_to_group", "zpn_site_controller",
	)
	if err != nil {
		return nil, fmt.Errorf("failed to unpickle, error: %v", err)
	}

	pbrokerGroups, pbrokerToGroups, pbrokers, err :=
		fetchGroupsAndInstancesFromCurl[PBrokerGroupTableJSON, PBrokerToGroupTableJSON, PBrokerTableJSON](
			customerID, "zpn_private_broker_group", "zpn_private_broker_to_group", "zpn_private_broker",
		)
	if err != nil {
		return nil, fmt.Errorf("failed to unpickle, error: %v", err)
	}

	appcGroups, appcToGroups, appcs, err :=
		fetchGroupsAndInstancesFromCurl[AppConnectorGroupTableJSON, AppConnectorToGroupTableJSON, AppConnectorTableJSON](
			customerID, "zpn_assistant_group", "zpn_assistantgroup_assistant_relation", "zpn_assistant",
		)
	if err != nil {
		return nil, fmt.Errorf("failed to unpickle, error: %v", err)
	}

	for i, site := range config.Sites {
		for _, group := range sitecGroups {
			if group.Value.SiteID != site.ID {
				continue
			}
			config.Sites[i].SitecGroups = append(config.Sites[i].SitecGroups, group.Value)
			cur := &config.Sites[i].SitecGroups[len(config.Sites[i].SitecGroups)-1]
			for _, toGroup := range sitecToGroups {
				if toGroup.Value.GroupID == group.Value.ID {
					for _, inst := range sitecs {
						if inst.Value.ID == toGroup.Value.InstanceID {
							cur.SiteControllers = append(cur.SiteControllers, inst.Value)
						}
					}
				}
			}
		}

		for _, group := range pbrokerGroups {
			if group.Value.SiteID != site.ID {
				continue
			}
			config.Sites[i].PBrokerGroups = append(config.Sites[i].PBrokerGroups, group.Value)
			cur := &config.Sites[i].PBrokerGroups[len(config.Sites[i].PBrokerGroups)-1]
			for _, toGroup := range pbrokerToGroups {
				if toGroup.Value.GroupID == group.Value.ID {
					for _, inst := range pbrokers {
						if inst.Value.ID == toGroup.Value.InstanceID {
							cur.PBrokers = append(cur.PBrokers, inst.Value)
						}
					}
				}
			}
		}

		for _, group := range appcGroups {
			if group.Value.SiteID != site.ID {
				continue
			}
			config.Sites[i].AppConnectorGroups = append(config.Sites[i].AppConnectorGroups, group.Value)
			cur := &config.Sites[i].AppConnectorGroups[len(config.Sites[i].AppConnectorGroups)-1]
			for _, toGroup := range appcToGroups {
				if toGroup.Value.GroupID == group.Value.ID {
					for _, inst := range appcs {
						if inst.Value.ID == toGroup.Value.InstanceID {
							cur.AppConnectors = append(cur.AppConnectors, inst.Value)
						}
					}
				}
			}
		}
	}

	return &config, nil
}

func handleGetBcpConfigFromCurl(msg MessageRequest) {
	if config, err := doGetBcpConfigFromCurl(); err != nil {
		replyError(msg, []string{err.Error()})
		return
	} else {
		replyMessage(msg.FromID, &MessageResponse{Seq: msg.Seq, MType: msg.MType, Total: 1, Index: 0, Body: *config})
	}
}

func handleGetSQLiteIntegrity(msg MessageRequest) {
	type IntegrityCheck struct {
		Result string `db:"integrity_check"`
	}

	cwd := core.GetConfig().Target.Cwd
	if files, err := utils.FindFiles(cwd, `^(i\.0|i\.0\.[0-9]r?|i\.0\.cz[0-9]+\.d[0-9]+)$`); err != nil {
		replyError(msg, []string{fmt.Sprintf("failed to find database files in dir %s, error: %v", cwd, err)})
	} else {
		var integrity []IntegrityCheck
		var results = make(map[string]string)
		for _, file := range files {
			if err := sqlite.Select(file, &integrity, "PRAGMA integrity_check"); err == nil {
				if len(integrity) < 1 {
					results[file] = "no result"
				} else if integrity[0].Result != "ok" {
					results[file] = integrity[0].Result
				} else {
					results[file] = ""
				}
			} else {
				results[file] = err.Error()
			}
		}
		replyMessage(msg.FromID, &MessageResponse{Seq: msg.Seq, MType: msg.MType, Total: 1, Index: 0, Body: results})
	}
}

func doGetRedirectBy(msg MessageRequest, filter string) {
	var results []string

	url := fmt.Sprintf("http://%s:%d/balance/redirect?client_type=zapp&scope_gid=%d&%s",
		core.GetConfig().Target.Addr, core.GetConfig().Target.Port,
		core.GetProfile().CustomerID, filter)

	if body, err := httpGet(url); err == nil {
		lines := strings.Split(body, "\n")
		startList := false
		for _, line := range lines {
			if !startList {
				m := patterns[PAT_BALANCE_REDIRECT_A].creg.FindStringSubmatch(line)
				if m != nil {
					startList = true
				}
			} else {
				m := patterns[PAT_BALANCE_REDIRECT_B].creg.FindStringSubmatch(line)
				if m != nil {
					results = append(results, m[1])
				}
			}
		}
		replyMessage(msg.FromID, &MessageResponse{Seq: msg.Seq, MType: msg.MType, Total: 1, Index: 0, Body: results})
	} else {
		replyError(msg, []string{fmt.Sprintf("failed to get redirect info, error: %v", err)})
	}
}

func handleGetRedirectByIP(msg MessageRequest) {
	doGetRedirectBy(msg, fmt.Sprintf("remote_ip=%s", msg.Args.(string)))
}

func handleGetRedirectByMTN(msg MessageRequest) {
	doGetRedirectBy(msg, fmt.Sprintf("trusted_networks=%s", strings.Join(msg.Args.([]string), ",")))
}

func doGetNetworkStatus(sni string, domains []string) ([]NetworkStatus, error) {
	var results []NetworkStatus

	for i := range domains {
		var result NetworkStatus
		if i != 0 {
			break
		}

		result.Domain = domains[i]
		if ips, err := net.LookupIP(domains[i]); err == nil {
			for _, ip := range ips {
				result.DNS = append(result.DNS, ip.String())
			}
		}

		for _, ip := range result.DNS {
			if conn, err := net.DialTimeout("tcp", ip+":443", 1*time.Second); err == nil {
				defer func() { _ = conn.Close() }()
				result.TCP = append(result.TCP, true)
			} else {
				result.TCP = append(result.TCP, false)
			}

			var peerCN = ""
			if sni != "" {
				tlsConfig := tls.Config{
					ServerName:         sni,
					InsecureSkipVerify: false,
				}

				if conn, err := tls.Dial("tcp", ip+":443", &tlsConfig); err == nil {
					defer func() { _ = conn.Close() }()
					certs := conn.ConnectionState().PeerCertificates
					if len(certs) > 0 {
						peerCN = certs[0].Subject.CommonName
					}
				} else {
					if cve, ok := err.(*tls.CertificateVerificationError); ok {
						certs := cve.UnverifiedCertificates
						if len(certs) > 0 {
							peerCN = certs[0].Subject.CommonName
						}
					} else {
						core.Logger.Debugf("Dail to '%s' using sni '%s', get error (%T): %v",
							ip+":443", sni, err, err)
					}
				}
				if peerCN == "" {
					core.Logger.Debugf("Dail to '%s' using sni '%s', cert is not found", ip+":443", sni)
				}
			}
			result.SSL = append(result.SSL, peerCN)
		}

		results = append(results, result)
	}

	return results, nil
}

func handleGetNetworkStatus(msg MessageRequest) {
	var sni string
	var offlineDomain string
	var domains []string
	var results []NetworkStatus

	instanceID := core.GetProfile().InstanceID

	siteConfig, err := doGetBcpConfigFromCurl()
	if err != nil {
		core.Logger.Debug("Failed to get BCP config when checking network status")
	}

	if siteConfig == nil {
		offlineDomain, err = getOfflineDomain()
		if err != nil {
			replyError(msg, []string{fmt.Sprintf("Can't find offline domain, error: %v", err)})
			return
		}
	} else {
		offlineDomain = siteConfig.Global.OfflineDomain
	}

	if core.GetConfig().Target.Kind == "pse" {
		if instanceID != 0 {
			sni = fmt.Sprintf("%d.pbctl.%s", instanceID, offlineDomain)
		}
		domains = append(domains, fmt.Sprintf("%s.%s", "pb2bcp", offlineDomain))
	} else if core.GetConfig().Target.Kind == "ac" {
		if instanceID != 0 {
			sni = fmt.Sprintf("%d.actl.%s", instanceID, offlineDomain)
		}
		domains = append(domains, fmt.Sprintf("%s.%s", "co2bcp", offlineDomain))
	} else {
		replyError(msg, []string{fmt.Sprintf("Can't run this command on %s", core.GetConfig().Target.Kind)})
		return
	}

	if siteConfig != nil {
		for _, site := range siteConfig.Sites {
			for _, group := range site.SitecGroups {
				for _, sitec := range group.SiteControllers {
					domains = append(domains, fmt.Sprintf("bcpsp-%d.%s", sitec.ID, offlineDomain))
				}
			}
		}
	}

	if status, err := doGetNetworkStatus(sni, domains); err != nil {
		replyError(msg, []string{err.Error()})
		return
	} else {
		results = append(results, status...)
	}

	replyMessage(msg.FromID, &MessageResponse{Seq: msg.Seq, MType: msg.MType, Total: 1, Index: 0, Body: results})
}

func handleCheckAppStatus(msg MessageRequest) {
	var result string
	var https bool
	url := msg.Args.(string)

	if strings.HasPrefix(url, "https://") {
		https = true
	} else if !strings.HasPrefix(url, "http://") {
		replyError(msg, []string{"Need specify https or http as scheme"})
		return
	}

	if https {
		client := &http.Client{
			Transport: &http.Transport{
				TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
			},
		}
		if _, err := client.Get(url); err == nil {
			result = "App is reachable"
		} else {
			result = "FAIL: " + err.Error()
		}
	} else {
		if _, err := httpGet(url); err == nil {
			result = "App is reachable"
		} else {
			result = "FAIL: " + err.Error()
		}
	}

	replyMessage(msg.FromID, &MessageResponse{Seq: msg.Seq, MType: msg.MType, Total: 1, Index: 0, Body: result})
}
