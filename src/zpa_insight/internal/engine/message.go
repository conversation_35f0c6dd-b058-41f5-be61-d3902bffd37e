package engine

import "crypto/x509"

type MessageType uint64

const (
	MTYPE_ERROR MessageType = iota
	MTYPE_QUIT
)

const (
	MTYPE_GET = iota + 1000
	MTYPE_GET_PROFILE
	MTYPE_GET_SYS_STATS
	MTYPE_GET_CERT_INFO
	MTYPE_GET_SQLITE_INTEGRITY
	MTYPE_GET_CONNECTIONS
	MTYPE_GET_BCP_CONFIG_FROM_DB
	MTYPE_GET_BCP_CONFIG_FROM_CURL
	MTYPE_GET_REDIRECT_BY_IP
	MTYPE_GET_REDIRECT_BY_MTN
	MTYPE_GET_NETWORK_STATUS
	MTYPE_CHECK_APP_STATUS
)

const ()

type MessageRequest struct {
	FromID  ChannelID
	Seq     uint64
	MType   MessageType
	ToAgent bool
	Args    any
}

type MessageResponse struct {
	FromID ChannelID
	Seq    uint64
	MType  MessageType
	Total  uint32
	Index  uint32
	Body   any
}

type ProcessStats struct {
	Pid     uint64
	Name    string
	Uptime  uint64
	Cwd     string
	CPU     float64
	RSS     uint64
	VMS     uint64
	FDs     uint64
	Threads uint64
}

type FsStats struct {
	Path    string
	Total   uint64
	Used    uint64
	Percent float64
}

type SysLoad struct {
	Load1  float64
	Load5  float64
	Load15 float64
}

type SysStats struct {
	MainProc   ProcessStats
	ChildProc  ProcessStats
	OptZscaler FsStats
	SysLoad    SysLoad
}

type CertInfo struct {
	Path     string
	Cert     *x509.Certificate
	Lifetime int64
}

type FohhInfo struct {
	LocalAddr      string
	LocalPort      uint16
	RemoteAddr     string
	RemotePort     uint16
	PeerDomainName string
	PeerCommonName string
	Sni            string
	State          string
}

type SiteConfig struct {
	InstanceID uint64
	Global     DDILTable
	Sites      []SiteTable
}

type NetworkStatus struct {
	Domain string
	DNS    []string
	TCP    []bool
	SSL    []string
}
