package engine

type DDILTable struct {
	CustomerID                 uint64 `db:"customer_gid" json:"customer_gid"`
	Name                       string `db:"name" json:"name"`
	Description                string `db:"description" json:"description"`
	Deleted                    uint64 `db:"deleted" json:"deleted"`
	OfflineDomain              string `db:"offline_domain" json:"offline_domain"`
	SitecPreferred             uint64 `db:"sitec_preferred" json:"sitec_preferred"`
	NewUserSupport             uint64 `db:"new_user_support" json:"new_user_support"`
	MaxAllowedDowntime         uint64 `db:"max_allowed_downtime" json:"max_allowed_downtime"`
	MaxAllowedDowntimeUnit     string `db:"max_allowed_downtime_unit" json:"max_allowed_downtime_unit"`
	MaxAllowedDowntimeInSecs   uint64
	IsSwitchTimeEnabled        uint64 `db:"is_switchtime_enabled" json:"is_switchtime_enabled"`
	MaxAllowedSwitchtime       uint64 `db:"max_allowed_switchtime" json:"max_allowed_switchtime"`
	MaxAllowedSwitchtimeUnit   string `db:"max_allowed_switchtime_unit" json:"max_allowed_switchtime_unit"`
	MaxAllowedSwitchtimeInSecs uint64
}

type SiteTable struct {
	CustomerID         uint64 `db:"customer_gid" json:"customer_gid"`
	ID                 uint64 `db:"gid" json:"gid"`
	Name               string `db:"name" json:"name"`
	Description        string `db:"description" json:"description"`
	Deleted            uint64 `db:"deleted" json:"deleted"`
	Enabled            uint64 `db:"enabled" json:"enabled"`
	OfflineDomain      string `db:"offline_domain" json:"offline_domain"`
	ReenrollPeriod     uint64 `db:"reenroll_period" json:"reenroll_period"`
	SitecGroups        []SitecGroupTable
	PBrokerGroups      []PBrokerGroupTable
	AppConnectorGroups []AppConnectorGroupTable
}

type SitecGroupTable struct {
	CustomerID      uint64  `db:"customer_gid" json:"customer_gid"`
	ID              uint64  `db:"gid" json:"gid"`
	Name            string  `db:"name" json:"name"`
	Description     string  `db:"description" json:"description"`
	Deleted         uint64  `db:"deleted" json:"deleted"`
	Enabled         uint64  `db:"enabled" json:"enabled"`
	SiteID          uint64  `db:"site_gid" json:"site_gid"`
	UpgradeDay      uint64  `db:"upgrade_day" json:"upgrade_day"`
	UpgradeTime     uint64  `db:"upgrade_time" json:"upgrade_time"`
	Location        string  `db:"location" json:"location"`
	Latitude        float64 `db:"latitude" json:"latitude"`
	Longitude       float64 `db:"longitude" json:"longitude"`
	CountryCode     string  `db:"country_code" json:"country_code"`
	SiteControllers []SitecTable
}

type SitecTable struct {
	CustomerID  uint64 `db:"customer_gid" json:"customer_gid"`
	ID          uint64 `db:"gid" json:"gid"`
	Name        string `db:"name" json:"name"`
	Description string `db:"description" json:"description"`
	Deleted     uint64 `db:"deleted" json:"deleted"`
	Enabled     uint64 `db:"enabled" json:"enabled"`
}

type SitecToGroupTable struct {
	CustomerID uint64 `db:"customer_gid" json:"customer_gid"`
	GroupID    uint64 `db:"site_controller_group_gid" json:"site_controller_group_gid"`
	InstanceID uint64 `db:"site_controller_gid" json:"site_controller_gid"`
	Deleted    uint64 `db:"deleted" json:"deleted"`
}

type PBrokerGroupTable struct {
	CustomerID  uint64  `db:"customer_gid" json:"customer_gid"`
	ID          uint64  `db:"gid" json:"gid"`
	Name        string  `db:"name" json:"name"`
	Description string  `db:"description" json:"description"`
	Deleted     uint64  `db:"deleted" json:"deleted"`
	Enabled     uint64  `db:"enabled" json:"enabled"`
	SiteID      uint64  `db:"site_gid" json:"site_gid"`
	UpgradeDay  uint64  `db:"upgrade_day" json:"upgrade_day"`
	UpgradeTime uint64  `db:"upgrade_time" json:"upgrade_time"`
	Location    string  `db:"location" json:"location"`
	Latitude    float64 `db:"latitude" json:"latitude"`
	Longitude   float64 `db:"longitude" json:"longitude"`
	CountryCode string  `db:"country_code" json:"country_code"`
	AltCloud    string  `db:"alt_cloud" json:"alt_cloud"`
	PBrokers    []PBrokerTable
}

type PBrokerTable struct {
	CustomerID  uint64 `db:"customer_gid" json:"customer_gid"`
	ID          uint64 `db:"gid" json:"gid"`
	Name        string `db:"name" json:"name"`
	Description string `db:"description" json:"description"`
	Deleted     uint64 `db:"deleted" json:"deleted"`
	Enabled     uint64 `db:"enabled" json:"enabled"`
}

type PBrokerToGroupTable struct {
	CustomerID uint64 `db:"customer_gid" json:"customer_gid"`
	GroupID    uint64 `db:"private_broker_group_gid" json:"private_broker_group_gid"`
	InstanceID uint64 `db:"private_broker_gid" json:"private_broker_gid"`
	Deleted    uint64 `db:"deleted" json:"deleted"`
}

type AppConnectorGroupTable struct {
	CustomerID    uint64  `db:"customer_gid" json:"customer_gid"`
	ID            uint64  `db:"gid" json:"gid"`
	Name          string  `db:"name" json:"name"`
	Description   string  `db:"description" json:"description"`
	Deleted       uint64  `db:"deleted" json:"deleted"`
	Enabled       uint64  `db:"enabled" json:"enabled"`
	SiteID        uint64  `db:"site_gid" json:"site_gid"`
	UpgradeDay    uint64  `db:"upgrade_day" json:"upgrade_day"`
	UpgradeTime   uint64  `db:"upgrade_time" json:"upgrade_time"`
	Location      string  `db:"location" json:"location"`
	Latitude      float64 `db:"latitude" json:"latitude"`
	Longitude     float64 `db:"longitude" json:"longitude"`
	CountryCode   string  `db:"country_code" json:"country_code"`
	AppConnectors []AppConnectorTable
}

type AppConnectorTable struct {
	CustomerID  uint64 `db:"customer_gid" json:"customer_gid"`
	ID          uint64 `db:"gid" json:"gid"`
	Name        string `db:"name" json:"name"`
	Description string `db:"description" json:"description"`
	Deleted     uint64 `db:"deleted" json:"deleted"`
	Enabled     uint64 `db:"enabled" json:"enabled"`
}

type AppConnectorToGroupTable struct {
	CustomerID uint64 `db:"customer_gid" json:"customer_gid"`
	GroupID    uint64 `db:"assistant_group_id" json:"assistant_group_id"`
	InstanceID uint64 `db:"assistant_id" json:"assistant_id"`
	Deleted    uint64 `db:"deleted" json:"deleted"`
}

type CommonTableJSON interface {
	IsDeleted() bool
}

type DDILTableJSON struct {
	Value DDILTable `json:"zpn_ddil_config"`
}

func (t DDILTableJSON) IsDeleted() bool { return t.Value.Deleted != 0 }

type SiteTableJSON struct {
	Value SiteTable `json:"zpn_site"`
}

func (t SiteTableJSON) IsDeleted() bool { return t.Value.Deleted != 0 }

type SitecGroupTableJSON struct {
	Value SitecGroupTable `json:"zpn_site_controller_group"`
}

func (t SitecGroupTableJSON) IsDeleted() bool { return t.Value.Deleted != 0 }

type SitecTableJSON struct {
	Value SitecTable `json:"zpn_site_controller"`
}

func (t SitecTableJSON) IsDeleted() bool { return t.Value.Deleted != 0 }

type SitecToGroupTableJSON struct {
	Value SitecToGroupTable `json:"zpn_site_controller_to_group"`
}

func (t SitecToGroupTableJSON) IsDeleted() bool { return t.Value.Deleted != 0 }

type PBrokerGroupTableJSON struct {
	Value PBrokerGroupTable `json:"zpn_private_broker_group"`
}

func (t PBrokerGroupTableJSON) IsDeleted() bool { return t.Value.Deleted != 0 }

type PBrokerTableJSON struct {
	Value PBrokerTable `json:"zpn_private_broker"`
}

func (t PBrokerTableJSON) IsDeleted() bool { return t.Value.Deleted != 0 }

type PBrokerToGroupTableJSON struct {
	Value PBrokerToGroupTable `json:"zpn_private_broker_to_group"`
}

func (t PBrokerToGroupTableJSON) IsDeleted() bool { return t.Value.Deleted != 0 }

type AppConnectorGroupTableJSON struct {
	Value AppConnectorGroupTable `json:"zpn_assistant_group"`
}

func (t AppConnectorGroupTableJSON) IsDeleted() bool { return t.Value.Deleted != 0 }

type AppConnectorTableJSON struct {
	Value AppConnectorTable `json:"zpn_assistant"`
}

func (t AppConnectorTableJSON) IsDeleted() bool { return t.Value.Deleted != 0 }

type AppConnectorToGroupTableJSON struct {
	Value AppConnectorToGroupTable `json:"zpn_assistantgroup_assistant_relation"`
}

func (t AppConnectorToGroupTableJSON) IsDeleted() bool { return t.Value.Deleted != 0 }
