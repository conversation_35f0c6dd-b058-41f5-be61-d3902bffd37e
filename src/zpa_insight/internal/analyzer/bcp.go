package analyzer

import (
	"fmt"
	"insight/internal/core"
	"insight/internal/engine"
	"regexp"
	"strconv"
	"strings"
)

const (
	FOHH_CATEGORY_TO_CLOUD = iota
	FOHH_CATEGORY_TO_PCC
	FOHH_CATEGORY_TO_PSE
	FOHH_CATEGORY_TO_AC
	FOHH_CATEGORY_TO_UNLABELED
	FOHH_CATEGORY_FROM_PCC
	FOHH_CATEGORY_FROM_PSE
	FOHH_CATEGORY_FROM_AC
	FOHH_CATEGORY_FROM_ZCC
	FOHH_CATEGORY_FROM_UNLABELED
	FOHH_CATEGORY_MAX_NUM
)

type FohhStatus struct {
	PeerAddr  string
	PeerName  string
	Total     uint
	Connected uint

	SitecGroup        *engine.SitecGroupTable
	Sitec             *engine.SitecTable
	PBrokerGroup      *engine.PBrokerGroupTable
	PBroker           *engine.PBrokerTable
	AppConnectorGroup *engine.AppConnectorGroupTable
	AppConnector      *engine.AppConnectorTable

	NotReady []*engine.FohhInfo
}

type AttachStatus struct {
	IsValid        bool
	SitecPreferred bool
	ToCloud        bool
	ToCloudFohh    *engine.FohhInfo
	ToBCP          bool
	ToBcpFohh      *engine.FohhInfo
}

type BcpStatus struct {
	Sessions     []map[string]FohhStatus // [map[PEER], map[PEER], ...]
	AttachStatus AttachStatus
}

func GetOfflineDomain(config *engine.SiteConfig) string {
	for _, site := range config.Sites {
		if site.ID == config.InstanceID && site.OfflineDomain != "" {
			return site.OfflineDomain
		}
	}
	return config.Global.OfflineDomain
}

func GetSitecInstance(config *engine.SiteConfig, id uint64) (*engine.SitecGroupTable, *engine.SitecTable) {
	for _, site := range config.Sites {
		for _, group := range site.SitecGroups {
			for _, sitec := range group.SiteControllers {
				if sitec.ID == id {
					return &group, &sitec
				}
			}
		}
	}
	return nil, nil
}

func GetPBrokerInstance(config *engine.SiteConfig, id uint64) (*engine.PBrokerGroupTable, *engine.PBrokerTable) {
	for _, site := range config.Sites {
		for _, group := range site.PBrokerGroups {
			for _, pbroker := range group.PBrokers {
				if pbroker.ID == id {
					return &group, &pbroker
				}
			}
		}
	}
	return nil, nil
}

func GetAppConnectorInstance(config *engine.SiteConfig, id uint64) (*engine.AppConnectorGroupTable, *engine.AppConnectorTable) {
	for _, site := range config.Sites {
		for _, group := range site.AppConnectorGroups {
			for _, ac := range group.AppConnectors {
				if ac.ID == id {
					return &group, &ac
				}
			}
		}
	}
	return nil, nil
}

func SniToCategory(asClient bool, fohh *engine.FohhInfo, offlineDomain string) (int, uint64) {
	var cat int
	tag, id := ParseSNI(fohh.Sni, offlineDomain)

	if asClient {
		id = 0
		if strings.HasSuffix(fohh.PeerDomainName, offlineDomain) {
			cat = FOHH_CATEGORY_TO_PCC
		} else {
			cat = FOHH_CATEGORY_TO_CLOUD
		}

		if tag == "a2pb_ctl" {
			cat = FOHH_CATEGORY_TO_PSE
		}

		creg := regexp.MustCompile(`^bcpsp-(\d+)\.`)
		if m := creg.FindStringSubmatch(fohh.PeerDomainName); m != nil {
			n, _ := strconv.Atoi(m[1])
			id = uint64(n)
		}

		return cat, id
	}

	if tag[:2] == "sc" {
		return FOHH_CATEGORY_FROM_PCC, id
	} else if tag[:2] == "pb" {
		return FOHH_CATEGORY_FROM_PSE, id
	} else if tag[:1] == "a" {
		return FOHH_CATEGORY_FROM_AC, id
	} else if tag[:1] == "c" {
		return FOHH_CATEGORY_FROM_ZCC, id
	}

	return FOHH_CATEGORY_FROM_UNLABELED, id
}

func CheckBcpStatus(config *engine.SiteConfig, clients *[]engine.FohhInfo, servers *[]engine.FohhInfo) BcpStatus {
	kind := core.GetConfig().Target.Kind

	bcpStatus := BcpStatus{
		Sessions: make([]map[string]FohhStatus, 0, FOHH_CATEGORY_MAX_NUM),
	}

	for i := 0; i < FOHH_CATEGORY_MAX_NUM; i += 1 {
		bcpStatus.Sessions = append(bcpStatus.Sessions, make(map[string]FohhStatus))
	}

	offlineDomain := GetOfflineDomain(config)

	allFohhs := [2]map[string][]*engine.FohhInfo{
		FohhGroupByRemoteAddrPort(clients),
		FohhGroupByRemoteSni(servers, offlineDomain),
	}

	for i := 0; i < 2; i += 1 {
		for key, fohhs := range allFohhs[i] {
			status := FohhStatus{Total: uint(len(fohhs))}
			status.Total = uint(len(fohhs))
			for _, fohh := range fohhs {
				if fohh.State == "fohh_connection_connected" {
					status.Connected += 1
				} else {
					status.NotReady = append(status.NotReady, fohh)
				}
			}

			if key == "uninitialized:0" {
				status.PeerAddr = "Unresolvable DNS"
			} else {
				status.PeerAddr = fohhs[0].RemoteAddr
			}

			cat, id := SniToCategory(i == 0, fohhs[0], offlineDomain)

			if i == 0 {
				status.PeerName = fohhs[0].PeerDomainName
			}

			if id > 0 {
				switch cat {
				case FOHH_CATEGORY_TO_PCC, FOHH_CATEGORY_FROM_PCC:
					status.SitecGroup, status.Sitec = GetSitecInstance(config, id)
					status.PeerName = fmt.Sprintf("%s (%d)", status.Sitec.Name, id)
				case FOHH_CATEGORY_TO_PSE, FOHH_CATEGORY_FROM_PSE:
					status.PBrokerGroup, status.PBroker = GetPBrokerInstance(config, id)
					status.PeerName = fmt.Sprintf("%s (%d)", status.PBroker.Name, id)
				case FOHH_CATEGORY_TO_AC, FOHH_CATEGORY_FROM_AC:
					status.AppConnectorGroup, status.AppConnector = GetAppConnectorInstance(config, id)
					status.PeerName = fmt.Sprintf("%s (%d)", status.AppConnector.Name, id)
				default:
					status.PeerName = fmt.Sprintf("(%d)", id)
				}
			}

			bcpStatus.Sessions[cat][key] = status
		}
	}

	if kind == "pse" || kind == "ac" {
		bcpStatus.AttachStatus.IsValid = true
		bcpStatus.AttachStatus.SitecPreferred = config.Global.SitecPreferred != 0

		for _, fohh := range *clients {
			var found bool
			if kind == "pse" && strings.Contains(fohh.Sni, ".pbctl.") {
				found = true
			} else if kind == "ac" && strings.Contains(fohh.Sni, ".actl.") {
				found = true
			}
			if found {
				if strings.HasSuffix(fohh.Sni, offlineDomain) {
					bcpStatus.AttachStatus.ToBCP = fohh.State == "fohh_connection_connected"
					bcpStatus.AttachStatus.ToBcpFohh = &fohh
				} else {
					bcpStatus.AttachStatus.ToCloud = fohh.State == "fohh_connection_connected"
					bcpStatus.AttachStatus.ToCloudFohh = &fohh
				}
			}
		}
	}

	return bcpStatus
}
