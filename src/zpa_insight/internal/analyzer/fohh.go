package analyzer

import (
	"insight/internal/engine"
	"regexp"
	"strconv"
)

func ParseSNI(sni string, offlineDomain string) (string, uint64) {
	creg := regexp.MustCompile(`^(\d+)\.[\d.]*([sc|pb|a|c][\w_]+)\.`)

	if m := creg.FindStringSubmatch(sni); m != nil {
		gid, _ := strconv.Atoi(m[1])
		return m[2], uint64(gid)
	}

	return "", 0
}

func FohhGroupBySni(fohhInfo *[]engine.FohhInfo) map[string][]*engine.FohhInfo {
	groups := make(map[string][]*engine.FohhInfo)

	for _, fohh := range *fohhInfo {
		key := fohh.Sni
		groups[key] = append(groups[key], &fohh)
	}

	return groups
}

func FohhGroupByRemoteAddrPort(fohhInfo *[]engine.FohhInfo) map[string][]*engine.FohhInfo {
	groups := make(map[string][]*engine.FohhInfo)

	for _, fohh := range *fohhInfo {
		key := fohh.RemoteAddr + ":" + strconv.Itoa(int(fohh.RemotePort))
		groups[key] = append(groups[key], &fohh)
	}

	return groups
}

func FohhGroupByRemoteAddr(fohhInfo *[]engine.FohhInfo) map[string][]*engine.FohhInfo {
	groups := make(map[string][]*engine.FohhInfo)

	for _, fohh := range *fohhInfo {
		key := fohh.RemoteAddr
		groups[key] = append(groups[key], &fohh)
	}

	return groups
}

func FohhGroupByRemoteSni(fohhInfo *[]engine.FohhInfo, offlineDomain string) map[string][]*engine.FohhInfo {
	groups := make(map[string][]*engine.FohhInfo)

	for _, fohh := range *fohhInfo {
		_, id := ParseSNI(fohh.Sni, offlineDomain)
		key := strconv.Itoa(int(id))
		groups[key] = append(groups[key], &fohh)
	}

	return groups
}
