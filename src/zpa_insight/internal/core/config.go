package core

import (
	"fmt"
	"io"
	"net/http"
	"os"
	"strconv"
	"strings"
	"sync"
	"time"
)

type Target struct {
	Kind string
	Addr string
	Port uint16
	Cwd  string
}

type Config struct {
	DebugLevel  string
	Target      Target
	BatchMode   bool
	Interactive bool
	ListenOn    string
	ConnectTo   string
	Args        []string
}

var (
	once   sync.Once
	config *Config
)

func GetConfig() *Config {
	once.Do(func() {
		config = &Config{}
	})
	return config
}

func (c *Config) String() string {
	output := []string{
		fmt.Sprintf("%14s: %s@%s:%d%s", "Target", c.Target.Kind, c.Target.Addr, c.Target.Port, c.Target.Cwd),
		fmt.Sprintf("%14s: %s", "Listen on", c.ListenOn),
		fmt.Sprintf("%14s: %s", "Connect to", c.ConnectTo),
		fmt.Sprintf("%14s: %t", "Batch mode", c.<PERSON>),
		fmt.Sprintf("%14s: %t", "Interactive", c.Interactive),
		fmt.Sprintf("%14s: %s", "Argumetns", strings.Join(c.Args, ", ")),
	}
	return strings.Join(output, "\n")
}

var BIN_DIR = "/opt/zscaler/bin"
var VAR_DIR = "/opt/zscaler/var"

var TARGETS = map[string]struct {
	Port uint16
	Bin  string
	Cwd  string
}{
	"pcc": {Port: 8000, Bin: "zpa-pcc", Cwd: VAR_DIR + "/pcc"},
	"pse": {Port: 8500, Bin: "zpa-service-edge", Cwd: VAR_DIR + "service-edge"},
	"ac":  {Port: 9000, Bin: "zpa-connector", Cwd: VAR_DIR},
}

func (c *Config) probe() {
	client :=
		&http.Client{
			Timeout: 3 * time.Second,
		}

	for kind, attrs := range TARGETS {
		url := fmt.Sprintf("http://%s:%d/uptime?seconds", config.Target.Addr, attrs.Port)
		resp, err := client.Get(url)
		if err != nil {
			Logger.Debugf("Can't open http connection to '%s'", url)
			continue
		}
		defer func() { _ = resp.Body.Close() }()

		body, err := io.ReadAll(resp.Body)
		if err != nil {
			Logger.Debugf("Can't read body from '%s'", url)
			continue
		}
		_, err = strconv.Atoi(strings.TrimSpace(string(body)))
		if err != nil {
			Logger.Debugf("Can't parse returned data from '%s'", url)
			continue
		}
		Logger.Infof("Successfully connected to '%s'. Set target as %s", url, kind)
		config.Target.Kind = kind
		return
	}

	for kind, attrs := range TARGETS {
		absPath := BIN_DIR + "/" + attrs.Bin
		_, err := os.Stat(absPath)
		if os.IsNotExist(err) {
			Logger.Debugf("Can't find file '%s'", absPath)
			continue
		}
		Logger.Infof("Successfully found file '%s'. Set target as %s", absPath, kind)
		config.Target.Kind = kind
		return
	}
}

func setupConfig() error {
	config := GetConfig()

	if config.Target.Kind == "auto" {
		config.probe()
	}

	switch config.Target.Kind {
	case "pcc", "pse", "ac":
		kind := config.Target.Kind
		attrs := TARGETS[kind]
		if config.Target.Port == 0 {
			config.Target.Port = attrs.Port
		}
		if len(config.Target.Cwd) == 0 {
			config.Target.Cwd = attrs.Cwd
		}
	case "auto":
		Logger.Error("Failed to probe the target type")
	}

	return nil
}
