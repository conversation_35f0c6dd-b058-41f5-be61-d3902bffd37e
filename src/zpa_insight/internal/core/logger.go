package core

import (
	"io"
	"os"
	"strings"

	"github.com/fatih/color"
	"github.com/sirupsen/logrus"
)

var (
	Logger         *logrus.Logger = logrus.New()
	AllDebugLevels []string
)

func init() {
	for _, level := range logrus.AllLevels {
		AllDebugLevels = append(AllDebugLevels, level.String())
	}
}

type LevelHook struct {
	Writer    io.Writer
	Formatter logrus.Formatter
	Level     logrus.Level
}

func (lh *LevelHook) Fire(entry *logrus.Entry) error {
	formatted, err := lh.Formatter.Format(entry)
	if err != nil {
		return err
	}
	_, err = lh.Writer.Write(formatted)
	return err
}

func (lh *LevelHook) Levels() []logrus.Level {
	if int(lh.Level) > len(logrus.AllLevels)-1 {
		return logrus.AllLevels
	}
	return logrus.AllLevels[:lh.Level+1]
}

// var LOG_FILE_NAME = "/var/log/zpa-insight.log"
var LOG_FILE_NAME = "/var/tmp/zpa-insight.log"

func setupLogger() error {
	stdout_level, err := logrus.ParseLevel(strings.ToLower(GetConfig().DebugLevel))
	if err != nil {
		Logger.Errorf("Unrecognized debug level: %s", GetConfig().DebugLevel)
		stdout_level = logrus.WarnLevel
	}

	logfile, err := os.OpenFile(LOG_FILE_NAME, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0660)
	if err == nil {
		Logger.SetOutput(logfile)
		Logger.SetLevel(logrus.TraceLevel)
		Logger.SetReportCaller(true)
		Logger.SetFormatter(&logrus.JSONFormatter{})

		hook := &LevelHook{
			Writer: os.Stdout,
			Level:  stdout_level,
			Formatter: &logrus.TextFormatter{
				DisableTimestamp: true,
				ForceColors:      !color.NoColor,
			},
		}
		Logger.AddHook(hook)
	} else {
		Logger.SetOutput(os.Stdout)
		Logger.SetReportCaller(true)
		Logger.SetLevel(stdout_level)
		Logger.SetFormatter(&logrus.TextFormatter{
			DisableTimestamp: true,
			ForceColors:      !color.NoColor,
		})

		Logger.Warnf("Can not create log file: %s", LOG_FILE_NAME)
	}

	return nil
}

func SetDebugLevel(level string) bool {
	if level, err := logrus.ParseLevel(strings.ToLower(level)); err == nil {
		Logger.SetLevel(level)
		return true
	}
	return false
}
