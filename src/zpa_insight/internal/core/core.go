package core

type Profile struct {
	Uptime        string
	Version       string
	MajorVersion  int
	MinorVersion  int
	PatchVersion  int
	Status        string
	InstanceID    uint64
	CustomerID    uint64
	OfflineDomain string
}

var profile Profile

func UpdateProfile(profile_ *Profile) {
	profile = *profile_
}

func GetProfile() *Profile {
	return &profile
}

func Setup() {
	_ = setupLogger()
	_ = setupConfig()
}
