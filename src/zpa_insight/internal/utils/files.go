package utils

import (
	"os"
	"path/filepath"
	"regexp"
)

func FindFiles(dir string, pattern string) ([]string, error) {
	var files []string

	entries, err := os.ReadDir(dir)
	if err != nil {
		return nil, err
	}

	for _, entry := range entries {
		if !entry.IsDir() {
			matched, err := regexp.MatchString(pattern, entry.Name())
			if err != nil {
				return nil, err
			}
			if matched {
				files = append(files, filepath.Join(dir, entry.Name()))
			}
		}
	}
	return files, nil
}
