package sqlite

import (
	"fmt"

	"github.com/jmoiron/sqlx"
	_ "github.com/mattn/go-sqlite3"
)

func Select(dbPath string, dest any, query string, args ...any) error {
	db, err := sqlx.Connect("sqlite3", dbPath)
	if err != nil {
		return fmt.Errorf("failed to open database: %v", err)
	}
	defer func() { _ = db.Close() }()

	return db.Unsafe().Select(dest, query, args...)
}

func Get(dbPath string, dest any, query string, args ...any) error {
	db, err := sqlx.Connect("sqlite3", dbPath)
	if err != nil {
		return fmt.Errorf("failed to open database: %v", err)
	}
	defer func() { _ = db.Close() }()

	return db.Unsafe().Get(dest, query, args...)
}
