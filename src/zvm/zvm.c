/*
 * zvm.c. Copyright (C) 2016 Zscaler Inc. All Rights Reserved
 */

#include <sys/stat.h>
#include <unistd.h>
#include <poll.h>
#include <regex.h>
#include <fcntl.h>
#include <errno.h>
#include <signal.h>
#include <sys/wait.h>
#include <assert.h>

#include "fohh/fohh_http.h"
#include "zcdns/zcdns.h"
#include "zpath_misc/zpath_misc.h"
#include "base64/base64.h"
#include "parson/parson.h"

#include "zvm/zvm.h"
#include "zpath_lib/zpath_debug.h"
#include "zpath_lib/zpath_capability_util.h"

struct zpn_zvm_reg_id_stats {
    uint32_t reg_id_fohh_http_create_count;
    uint32_t reg_id_fohh_http_free_count;

    uint32_t reg_id_imdsv2_token_alloc_count;
    uint32_t reg_id_imdsv2_token_free_count;

    uint32_t reg_id_api_ver_alloc_count;
    uint32_t reg_id_api_ver_free_count;

    uint32_t reg_id_query_count;
    uint32_t reg_id_query_success_count;
};

static struct {
    char region_id_info[REGION_ID_INFO_LEN];
    int region_id_info_known;
    pthread_mutex_t region_id_info_lock;
}  g_region_id_info = {
    // Guarantee uniqueness of this struct
    // Guarantee that it is always initialized
    .region_id_info = {'\0'},
    .region_id_info_known = 0,
    .region_id_info_lock = PTHREAD_MUTEX_INITIALIZER
};

struct zcdns *zcdns;
static int global_known = 0;
enum zvm_vm_type global_vm_type = zvm_vm_type_unknown;
static enum zvm_vm_type previous_docker_type = zvm_vm_type_unknown;

static zvm_test_get_buf_f *get_id = NULL;
static zvm_test_get_buf_f *get_config = NULL;
enum platform_detail {
    unknown,
    esxi,
    azure,
    aws,
    docker,
    nutanix,
    hyperv,
    kvm,
    gcp,
    k8s,
    openshift
};
static const char* platform_detail_str[] = { "Unknown","ESXi","Azure","AWS","Docker","Nutanix","Hyper-V","KVM","GCP","K8S", "OpenShift"};
static const char* platform_detail_str_rh[] = { "Unknown-Zscaler RedHat Image",
                                                "ESXi-Zscaler RedHat Image",
                                                "Azure-Zscaler RedHat Image",
                                                "AWS-Zscaler RedHat Image",
                                                "Docker-Zscaler RedHat Image",
                                                "Nutanix-Zscaler RedHat Image",
                                                "Hyper-V-Zscaler RedHat Image",
                                                "KVM-Zscaler RedHat Image",
                                                "GCP-Zscaler RedHat Image",
                                                "Kubernetes-Zscaler UBI Image",
                                                "OpenShift-Zscaler UBI Image"};

static char platform_version[IMAGE_VERSION_LEN];

struct argo_log_collection *zpn_zvm_event_collection = NULL;
struct zpath_allocator zpn_zvm_allocator = ZPATH_ALLOCATOR_INIT("zpn_zvm");

uint64_t zpn_zvm_debug_log = 0;

uint64_t zpn_zvm_debug_log_catch_defaults =
        (ZPN_ZVM_DEBUG_REGION_ID_BIT) |
        0;

const char *zpn_zvm_log_debug_names[] = ZPN_ZVM_DEBUG_LOG_NAMES;

static struct zpn_zvm_reg_id_stats g_zvm_stats;

#define REDHAT_OVA_FLAG "/opt/zscaler/var/.zrhi"

#define RHI_VERSION_FALLBACK_VALUE "NA"

const char *zvm_vm_type_to_str(enum zvm_vm_type type)
{
    switch (type) {
    case zvm_vm_type_unknown:
        return "zvm_vm_type_unknown";
    case zvm_vm_type_esx:
        return "zvm_vm_type_esx";
    case zvm_vm_type_azure:
        return "zvm_vm_type_azure";
    case zvm_vm_type_aws:
        return "zvm_vm_type_aws";
    case zvm_vm_type_aws_docker:
        return "zvm_vm_type_docker";
    case zvm_vm_type_docker:
        return "zvm_vm_type_docker";
    case zvm_vm_type_nutanix:
        return "zvm_vm_type_nutanix";
    case zvm_vm_type_hyper_v:
        return "zvm_vm_type_hyper_v";
    case zvm_vm_type_kvm:
        return "zvm_vm_type_kvm";
    case zvm_vm_type_gcp:
        return "zvm_vm_type_gcp";
    case zvm_vm_type_k8s:
        return "zvm_vm_type_k8s";
    case zvm_vm_type_openshift:
        return "zvm_vm_type_openshift";
    default:
        return "unknown";
    }
}

int zvm_vm_type_is_zscaler_rh_image()
{
    if (access(REDHAT_OVA_FLAG, F_OK) == 0) {
        return 1;
    } else {
        return 0;
    }
}

char *zvm_vm_type_rh_image_version()
{
    memset(platform_version, 0, sizeof(platform_version));
    if (zvm_vm_type_is_zscaler_rh_image()) {
        int fp = open(REDHAT_OVA_FLAG, O_RDONLY);
        if (fp == -1){
            snprintf(platform_version, sizeof(platform_version), RHI_VERSION_FALLBACK_VALUE);
            ZPN_ZVM_LOG(AL_ERROR, "Could not open red hat flag file %s", strerror(errno));
            return platform_version;
        }
        int len = read(fp, platform_version, sizeof(platform_version) - 1);
        if (len >= 0) {
            platform_version[len] = '\0';
        }
        if (len != (IMAGE_VERSION_LEN - 1)) {
            ZPN_ZVM_LOG(AL_ERROR, "Incorrect image version format or error reading red hat file");
            snprintf(platform_version, sizeof(platform_version), RHI_VERSION_FALLBACK_VALUE);
        }
        close(fp);
    } else {
        snprintf(platform_version, sizeof(platform_version), RHI_VERSION_FALLBACK_VALUE);
    }
    return platform_version;
}

int zvm_vm_type_is_docker()
{
    if (global_vm_type == zvm_vm_type_docker || global_vm_type == zvm_vm_type_aws_docker) {
        return 1;
    } else {
        return 0;
    }
}

const char *zvm_vm_type_to_str_for_qbr_concise(enum zvm_vm_type type)
{
    switch (type) {
    case zvm_vm_type_azure:
        return "Azure";
    case zvm_vm_type_aws:
    case zvm_vm_type_aws_docker:
        return "AWS";
    case zvm_vm_type_gcp:
        return "GCP";
    default:
        return NULL;
    }
}

const char *zvm_vm_type_to_str_concise(enum zvm_vm_type type)
{
    const char **platform_detail = NULL;

    if (zvm_vm_type_is_zscaler_rh_image()) {
        platform_detail = platform_detail_str_rh;
    } else {
        platform_detail = platform_detail_str;
    }

    switch (type) {
    case zvm_vm_type_unknown:
        return platform_detail[unknown];
    case zvm_vm_type_esx:
        return platform_detail[esxi];
    case zvm_vm_type_azure:
        return platform_detail[azure];
    case zvm_vm_type_aws:
        return platform_detail[aws];
    case zvm_vm_type_aws_docker:
        return platform_detail[docker];
    case zvm_vm_type_docker:
        return platform_detail[docker];
    case zvm_vm_type_nutanix:
        return platform_detail[nutanix];
    case zvm_vm_type_hyper_v:
        return platform_detail[hyperv];
    case zvm_vm_type_kvm:
        return platform_detail[kvm];
    case zvm_vm_type_gcp:
        return platform_detail[gcp];
    case zvm_vm_type_k8s:
        return platform_detail[k8s];
    case zvm_vm_type_openshift:
        return platform_detail[openshift];
    default:
        return platform_detail[unknown];
    }
}

/* IMDSv1: curl -v http://***************/<url> */
static int zvm_aws_get_instance_metadata_v1(const char  *url,
                                            char        *out_buf,
                                            size_t      *out_buf_len,
                                            int         base64_decode,
                                            char        *err_str,
                                            size_t      err_str_len)
{
    int ret = ZVM_RESULT_ERR;
    struct evbuffer *body = NULL;
    enum fohh_http_client_status status;

    struct fohh_http_client *client =
        fohh_http_client_create_synchronous(zcdns,
                                            NULL,
                                            "***************",
                                            "***************",
                                            NULL,
                                            80,
                                            0,
                                            ZVM_TIMEOUT_AWS_CONN_SETUP_US,
                                            &status);
    if (client) {
        if (status == fohh_http_client_status_success) {
            int res;
            enum fohh_http_client_request_status r_status;
            int http_status;

            res = fohh_http_client_request_synchronous(client,
                                                       ZVM_TIMEOUT_AWS_HTTP_REQ_US,
                                                       FOHH_HTTP_METHOD_GET,
                                                       url,
                                                       NULL,
                                                       NULL,
                                                       NULL,
                                                       &r_status,
                                                       &http_status,
                                                       &body);
            if ((res == FOHH_RESULT_NO_ERROR) && (http_status == 200) && body) {
                if (base64_decode) {
                    char *tmp_buf = ZPN_ZVM_MALLOC(*out_buf_len);
                    if (tmp_buf) {
                        size_t tmp_buf_len = *out_buf_len;
                        int len;
                        tmp_buf_len = evbuffer_remove(body, tmp_buf, tmp_buf_len);
                        len = base64_decode_binary((unsigned char *)out_buf, tmp_buf, tmp_buf_len);
                        ret = ZVM_RESULT_NO_ERROR;
                        if (len < 0) {
                            len = 0; /* decode error set len = 0 */
                            ret = ZVM_RESULT_ERR;
                            if (err_str && err_str_len) {
                                snprintf(err_str, err_str_len, "IMDSv1 decode error when getting metadata for url %s", url);
                            }
                        } else if (len >= *out_buf_len) {
                            /* buffer not enough for decode */
                            len = 0; /* lets treat this the same way as decode error */
                            ret = ZVM_RESULT_ERR;
                            if (err_str && err_str_len) {
                                snprintf(err_str, err_str_len, "IMDSv1 buffer overrun when getting metadata for url %s", url);
                            }
                        }
                        out_buf[len] = 0; /* null terminate */
                        *out_buf_len = len; /* set length */
                        ZPN_ZVM_FREE(tmp_buf);
                    } else {
                        if (err_str && err_str_len) {
                            snprintf(err_str, err_str_len, "IMDSv1 decode FAIL: No memory");
                        }
                    }
                } else {
                    size_t len = *out_buf_len;
                    (*out_buf_len) = evbuffer_remove(body, out_buf, len);
                    if (len > *out_buf_len) {
                        out_buf[*out_buf_len] = 0;
                    } else {
                        out_buf[len - 1] = 0;
                    }
                    ret = ZVM_RESULT_NO_ERROR;
                }
            } else {
                if (err_str && err_str_len) {
                    snprintf(err_str, err_str_len, "Failed to get aws instance metadata using IMDSv1, res = %d, url = %s, status = %s, http_status = %d, body = %p",
                                    res, url, fohh_http_client_request_status_str(r_status), http_status, body);
                }
            }
        } else {
            if (err_str && err_str_len) {
                snprintf(err_str, err_str_len, "Failed to connect to AWS EC2 server, status = %s",
                                    fohh_http_client_status_str(status));
            }
        }
    } else {
        if (err_str && err_str_len) {
            snprintf(err_str, err_str_len, "Failed to create http client");
        }
    }

    if (body) evbuffer_free(body);
    if (client) fohh_http_client_destroy_from_another_thread(client);
    return ret;
}

static int zvm_aws_IMDSv2_get_token(char    *token,
                                    size_t  *token_len,
                                    char    *err_str,
                                    size_t  err_str_len)
{
    int ret = ZVM_RESULT_ERR;
    struct evbuffer *body = NULL;
    struct evbuffer *extra_headers = NULL;
    enum fohh_http_client_status status;

    struct fohh_http_client *client =
        fohh_http_client_create_synchronous(zcdns,
                                            NULL,
                                            "***************",
                                            "***************",
                                            NULL,
                                            80,
                                            0,
                                            ZVM_TIMEOUT_AWS_CONN_SETUP_US,
                                            &status);
    if (client) {
        if (status == fohh_http_client_status_success) {
            int res;
            enum fohh_http_client_request_status r_status;
            int http_status;

            extra_headers = evbuffer_new();
            if (!extra_headers) {
                if (err_str && err_str_len) {
                    snprintf(err_str, err_str_len, "IMDSv2 Token fetch FAIL: no memory for header");
                }
                goto done;
            }

            evbuffer_add_printf(extra_headers, "X-aws-ec2-metadata-token-ttl-seconds: 21600\r\n");
            res = fohh_http_client_request_synchronous(client,
                                                       ZVM_TIMEOUT_AWS_HTTP_REQ_US,
                                                       FOHH_HTTP_METHOD_PUT,
                                                       "/latest/api/token",
                                                       extra_headers,
                                                       NULL,
                                                       NULL,
                                                       &r_status,
                                                       &http_status,
                                                       &body);
            if ((res == FOHH_RESULT_NO_ERROR) && (http_status == 200) && body) {
                size_t len = *token_len;
                *token_len = evbuffer_remove(body, token, len);
                if (len > *token_len) {
                    token[*token_len] = 0;
                } else {
                    token[len - 1] = 0;
                }
                ret = ZVM_RESULT_NO_ERROR;
                //fprintf(stderr, "IMDSv2 Token: %s\n", token);
            } else {
                if (err_str && err_str_len) {
                    snprintf(err_str, err_str_len, "Failed to get IMDSv2 Token, res = %d, status = %s, http_status = %d, body = %p",
                                        res, fohh_http_client_request_status_str(r_status), http_status, body);
                }
            }
        } else {
            if (err_str && err_str_len) {
                snprintf(err_str, err_str_len, "Failed to connect to AWS EC2 server, status = %s",
                                    fohh_http_client_status_str(status));
            }
        }
    } else {
        if (err_str && err_str_len) {
            snprintf(err_str, err_str_len, "Failed to create http client");
        }
    }

done:
    if (body) evbuffer_free(body);
    if (extra_headers) evbuffer_free(extra_headers);
    if (client) fohh_http_client_destroy_from_another_thread(client);
    return ret;
}

/* IMDSv2: TOKEN=`curl -X PUT "http://***************/latest/api/token" -H "X-aws-ec2-metadata-token-ttl-seconds: 21600"` && curl -H "X-aws-ec2-metadata-token: $TOKEN" http://***************/<url>
 * Get the token, and use it for fetching instance metadata
 * AWS server closes the connection immediately after each request, hence we need to create http client twice
 */
static int zvm_aws_get_instance_metadata_v2(const char  *url,
                                            char        *out_buf,
                                            size_t      *out_buf_len,
                                            int         base64_decode,
                                            char        *err_str,
                                            size_t      err_str_len)
{
    int ret = ZVM_RESULT_ERR;
    struct evbuffer *body = NULL;
    struct evbuffer *extra_headers = NULL;
    enum fohh_http_client_status status;

    /* STEP1: get token */
    char token[100] = {0};
    size_t token_len = sizeof(token);
    if (ZVM_RESULT_NO_ERROR != zvm_aws_IMDSv2_get_token(&token[0], &token_len, err_str, err_str_len)) {
        return ret;
    }

    struct fohh_http_client *client =
        fohh_http_client_create_synchronous(zcdns,
                                            NULL,
                                            "***************",
                                            "***************",
                                            NULL,
                                            80,
                                            0,
                                            ZVM_TIMEOUT_AWS_CONN_SETUP_US,
                                            &status);
    if (client) {
        if (status == fohh_http_client_status_success) {
            int res;
            enum fohh_http_client_request_status r_status;

            int http_status;

            extra_headers = evbuffer_new();
            if (!extra_headers) goto done;
            evbuffer_add_printf(extra_headers, "X-aws-ec2-metadata-token: %s\r\n", token);
            res = fohh_http_client_request_synchronous(client,
                                                       ZVM_TIMEOUT_AWS_HTTP_REQ_US,
                                                       FOHH_HTTP_METHOD_GET,
                                                       url,
                                                       extra_headers,
                                                       NULL,
                                                       NULL,
                                                       &r_status,
                                                       &http_status,
                                                       &body);
            if ((res == FOHH_RESULT_NO_ERROR) && (http_status == 200) && body) {
                if (base64_decode) {
                    char *tmp_buf = ZPN_ZVM_MALLOC(*out_buf_len);
                    if (tmp_buf) {
                        size_t tmp_buf_len = *out_buf_len;
                        int len;
                        tmp_buf_len = evbuffer_remove(body, tmp_buf, tmp_buf_len);
                        len = base64_decode_binary((unsigned char *)out_buf, tmp_buf, tmp_buf_len);
                        ret = ZVM_RESULT_NO_ERROR;
                        if (len < 0) {
                            len = 0; /* decode error set len = 0 */
                            ret = ZVM_RESULT_ERR;
                            if (err_str && err_str_len) {
                                snprintf(err_str, err_str_len, "IMDSv2 decode error when getting metadata for url %s", url);
                            }
                        } else if (len >= *out_buf_len) {
                            /* buffer not enough for decode */
                            len = 0; /* lets treat this the same way as decode error */
                            ret = ZVM_RESULT_ERR;
                            if (err_str && err_str_len) {
                                snprintf(err_str, err_str_len, "IMDSv2 buffer overrun when getting metadata for url %s", url);
                            }
                        }
                        out_buf[len] = 0; /* null terminate */
                        *out_buf_len = len; /* set length */
                        ZPN_ZVM_FREE(tmp_buf);
                    } else {
                        if (err_str && err_str_len) {
                            snprintf(err_str, err_str_len, "IMDSv2 decode FAIL: no memory");
                        }
                    }
                } else {
                    size_t len = *out_buf_len;
                    (*out_buf_len) = evbuffer_remove(body, out_buf, len);
                    if (len > *out_buf_len) {
                        out_buf[*out_buf_len] = 0;
                    } else {
                        out_buf[len - 1] = 0;
                    }
                    ret = ZVM_RESULT_NO_ERROR;
                }
            } else {
                if (err_str && err_str_len) {
                    snprintf(err_str, err_str_len, "Failed to get aws instance metadata using IMDSv2, res = %d, url = %s, status = %s, http_status = %d, body = %p",
                                        res, url, fohh_http_client_request_status_str(r_status), http_status, body);
                }
            }
        } else {
            if (err_str && err_str_len) {
                snprintf(err_str, err_str_len, "Failed to connect to AWS EC2 server, status = %s",
                                    fohh_http_client_status_str(status));
            }
        }
    } else {
        if (err_str && err_str_len) {
            snprintf(err_str, err_str_len, "Failed to create http client");
        }
    }

done:
    if (body) evbuffer_free(body);
    if (extra_headers) evbuffer_free(extra_headers);
    if (client) fohh_http_client_destroy_from_another_thread(client);
    return ret;
}

/*
 * Retrieve instance metadata on AWS EC2
 * Day1 implementation only supports IMDSv1 (Instance Metadata Service Version 1)
 * We need to support IMDSv2 as well as a fall back in case IMDSv1 fails
 * IMDSv1 is security compromised hence IMDSv2 will become a preferred way in future.
 * refer to https://docs.aws.amazon.com/AWSEC2/latest/UserGuide/instancedata-data-retrieval.html for more details
 *
 * base64_decode: when set, do base64 decoding before adding to buf
 */

static int zvm_aws_get_instance_metadata(const char     *url,
                                         char           *out_buf,
                                         size_t         *out_buf_len,
                                         int            base64_decode,
                                         char           *err_str,
                                         size_t         err_str_len)
{
    int res = ZVM_RESULT_NO_ERROR;

    /* Try IMDSv2 first, then fallback to v1 if failed
     *FIXEME: remove v1 when IMDSV1 is no longer supported */
    res = zvm_aws_get_instance_metadata_v2(url, out_buf, out_buf_len, base64_decode, err_str, err_str_len);
    if (res) {
        res = zvm_aws_get_instance_metadata_v1(url, out_buf, out_buf_len, base64_decode, err_str, err_str_len);
    }
    return res;
}

static int zvm_custom_config_read_aws(char *buf, size_t *buf_len)
{
    /* requires base64 decode */
    return zvm_aws_get_instance_metadata("/latest/user-data", buf, buf_len, 1, NULL, 0);
}

static int zvm_custom_config_read_esx(char *buf, size_t *buf_len)
{
    struct stat st;
    char *bin_path = USR_BIN_VMWARE_RPCTOOL;

    if (stat(USR_LOCAL_BIN_VMWARE_RPCTOOL, &st) == 0) {
        bin_path = USR_LOCAL_BIN_VMWARE_RPCTOOL;
    } else if (stat(USR_SBIN_VMWARE_RPCTOOL, &st) == 0) {
        bin_path = USR_SBIN_VMWARE_RPCTOOL;
    }

    return zvm_run_cmd(buf, buf_len, bin_path, 0, "info-get guestinfo.ovfEnv", NULL);
}

static int zvm_custom_config_read_azure(char *buf, size_t *buf_len)
{
    return ZVM_RESULT_ERR;
}

int zvm_custom_config_read(char *buf, size_t *buf_len)
{
    enum zvm_vm_type vm_type = zvm_type_get();

    if (get_config) return (get_config)(buf, buf_len);

    switch(vm_type) {
    case zvm_vm_type_aws:
        return zvm_custom_config_read_aws(buf, buf_len);
    case zvm_vm_type_esx:
        return zvm_custom_config_read_esx(buf, buf_len);
    case zvm_vm_type_azure:
        return zvm_custom_config_read_azure(buf, buf_len);
    default:
        return ZVM_RESULT_ERR;
    }
    return ZVM_RESULT_ERR;
}

static char *regcompile_errors[] = {
	[0] = (char *) "N/A",
#ifdef REG_NOMATCH
    [REG_NOMATCH] = (char *) "The regexec() function failed to match",
#endif
#ifdef REG_BADPAT
    [REG_BADPAT] = (char *) "invalid regular expression",
#endif
#ifdef REG_ECOLLATE
    [REG_ECOLLATE] = (char *) "invalid collating element",
#endif
#ifdef REG_ECTYPE
    [REG_ECTYPE] = (char *) "invalid character class",
#endif
#ifdef REG_EESCAPE
    [REG_EESCAPE] = (char *) "applied to unescapable character",
#endif
#ifdef REG_ESUBREG
    [REG_ESUBREG] = (char *) "invalid backreference number",
#endif
#ifdef REG_EBRACK
    [REG_EBRACK] = (char *) "brackets `[ ]' not balanced",
#endif
#ifdef REG_EPAREN
    [REG_EPAREN] = (char *) "parentheses `( )' not balanced",
#endif
#ifdef REG_EBRACE
    [REG_EBRACE] = (char *) "braces `{ }' not balanced",
#endif
#ifdef REG_BADBR
    [REG_BADBR] = (char *) "invalid repetition count(s) in `{ }'",
#endif
#ifdef REG_ERANGE
    [REG_ERANGE] = (char *) "invalid character range in `[ ]'",
#endif
#ifdef REG_ESPACE
    [REG_ESPACE] = (char *) "ran out of memory",
#endif
#ifdef REG_BADRPT
    [REG_BADRPT] = (char *) "or `+' operand invalid",
#endif
#ifdef REG_EMPTY
    [REG_EMPTY] = (char *) "empty (sub)expression",
#endif
#ifdef REG_ASSERT
    [REG_ASSERT] = (char *) "cannot happen - you found a bug",
#endif
#ifdef REG_INVARG
    [REG_INVARG] = (char *) "invalid argument, e.g. negative-length string",
#endif
#ifdef REG_ILLSEQ
    [REG_ILLSEQ] = (char *) "illegal byte sequence (bad multibyte character)",
#endif
};

#define ZVM_REGCOMP_ERR_STR_INDEX(x)    ((((x) >= 0) && \
          ((x) < (sizeof(regcompile_errors)/sizeof(regcompile_errors[0])))) ? \
             (x) : 0)

int zvm_key_get_by_regex(const char *regex_str,
                         zvm_key_verify_f *verify_f,
                         void *verify_cookie,
                         char *key_value,
                         size_t *key_value_len)
{
    char buf[16*1024];
    size_t buf_len;
    char line[16*1024];
    char key[2*1024];
    size_t key_len;
    regex_t regex;
    int res;
    char *w, *o, *we, *oe;
    int is_comment;
    int have_commented_key = 0;
    size_t match_len = 0;

    if (!regex_str) return ZVM_RESULT_BAD_ARGUMENT;
    res = regcomp(&regex, regex_str, REG_EXTENDED);
    if (res) {
        res = ZVM_REGCOMP_ERR_STR_INDEX(res);
        fprintf(stderr, "Could not compile regular expressin: %s: %s\n", regex_str, regcompile_errors[res]);
        return ZVM_RESULT_BAD_ARGUMENT;
    }

    buf_len = sizeof(buf);
    res = zvm_custom_config_read(&(buf[0]), &buf_len);
    if (res) {
        //fprintf(stderr, "Could not read custom config\n");
        return res;
    }

    w = buf;
    we = w + buf_len;
    while (w < we) {
        o = line;
        oe = o + sizeof(line);
        /* Skip NULLs, newlines */
        while ((w < we) && ((*w == 0) || (*w == '\r') || (*w == '\n') || (!isprint(*w)))) w++;
        if (w < we) {
            regmatch_t match;

            if (*w == '#') {
                is_comment = 1;
            } else {
                is_comment = 0;
            }
            /* Copy anything that is printable, not null, and not newline onto our line buffer */
            while (((w < we) && (o < oe) && (*w != 0) && (*w != '\r') && (*w != '\n') && (isprint(*w)))) {
                *o = *w;
                o++;
                w++;
            }
            *o = 0;
            //fprintf(stderr, "Testing line = <%s>\n", line);
            res = regexec(&regex, line, 1, &match, 0);
            if (res == 0) {
                key_len = match.rm_eo - match.rm_so;
                //fprintf(stderr, "  Regex matched, len = %ld, so = %ld, eo = %ld\n", (long) key_len, (long) match.rm_so, (long) match.rm_eo);
                if ((key_len < sizeof(key)) && (key_len < *key_value_len)) {
                    memcpy(key, &(line[match.rm_so]), key_len);
                    key[key_len] = 0;
                    if (!verify_f || ((verify_f)(verify_cookie, key) == ZVM_RESULT_NO_ERROR)) {
                        if (is_comment) {
                            if (!have_commented_key) {
                                memcpy(key_value, key, key_len);
                                match_len = key_len;
                                have_commented_key = 1;
                            } else {
                                /* Use first commented key */
                            }
                        } else {
                            memcpy(key_value, key, key_len);
                            match_len = key_len;
                            break;
                        }
                    }
                } else {
                    /* Not enough space for key */
                }
            } else {
                //fprintf(stderr, "  Regex did not match\n");
            }
        }
    }
    if (match_len) {
        *key_value_len = match_len;
        return ZVM_RESULT_NO_ERROR;
    }
    return ZVM_RESULT_NOT_FOUND;
}

/*
 * FIXME: We can get the instance ID from GCP too as below,
 * curl "http://metadata.google.internal/computeMetadata/v1/instance/id" -H "Metadata-Flavor: Google"; echo
 * Lets try to use it.
 */
int zvm_id_get(char *id_value,
               size_t *id_value_len, char *err_str, size_t err_str_len, int version)
{
    enum zvm_vm_type vm_type = zvm_type_get();
    int ret = ZVM_RESULT_ERR;

    if (get_id) return (get_id)(id_value, id_value_len);

    switch(vm_type) {
    /*
     * ET-39604 Docker is deployed inside the aws instance
     * ensure during upgrade the key is complete i.e gets
     * the aws instance id to decrypt the provision crypted file
     */
    case zvm_vm_type_k8s:
    case zvm_vm_type_openshift:
        {
            if (previous_docker_type == zvm_vm_type_docker || previous_docker_type == zvm_vm_type_unknown)
                break;
        }
    case zvm_vm_type_aws_docker:
    case zvm_vm_type_aws:
        if(version == ZVM_VERSION_IMDSV2) {
            ret = zvm_aws_get_instance_metadata("/latest/meta-data/instance-id", id_value, id_value_len, 0, err_str, err_str_len);
        } else { /* This is for backward compatibility - remove this code after all customers move to IMDSv2 */
            ret = zvm_aws_get_instance_metadata_v1("/latest/meta-data/instance-id", id_value, id_value_len, 0, err_str, err_str_len);
        }
        break;
    default:
        break;
    }
    return ret;
}

int zvm_init(struct argo_log_collection *event_log, struct zcdns *zcdns_arg)
{
    int res;
    zpn_zvm_event_collection = event_log;

    zpath_debug_add_allocator(&zpn_zvm_allocator, "zpn_zvm");

    res = zpath_debug_add_flag(&zpn_zvm_debug_log, zpn_zvm_debug_log_catch_defaults, "zpn_zvm", zpn_zvm_log_debug_names);
    if (res) {
        ZPN_ZVM_LOG(AL_ERROR, "zpath_debug_add_flag failed: %s", zpath_result_string(res));
        return res;
    }

    zcdns = zcdns_arg;
    ZPN_ZVM_LOG(AL_INFO, "ZVM init complete !");
    return ZVM_RESULT_NO_ERROR;
}

static pid_t spawn(const char *command, char *const *argv, int *fd_to_child, int *fd_from_child)
{
    int pipe_to_child[2], pipe_from_child[2];
    pid_t pid;

    /* PIPE: Write to [1], read from [0] */
    if (pipe(pipe_to_child) != 0) {
        return -1;
    }
    if (pipe(pipe_from_child) != 0) {
        close(pipe_to_child[0]);
        close(pipe_to_child[1]);
        return -1;
    }

    pid = fork();

    if (pid < 0) {
        /* Error! */
        close(pipe_to_child[0]);
        close(pipe_to_child[1]);
        close(pipe_from_child[0]);
        close(pipe_from_child[1]);
        return pid;
    } else if (pid == 0) {
        /* Child! Close the side of the pipes we do not use. */
        close(pipe_to_child[1]);
        close(pipe_from_child[0]);

        dup2(pipe_to_child[0], STDIN_FILENO);
        dup2(pipe_from_child[1], STDOUT_FILENO);
        dup2(pipe_from_child[1], STDERR_FILENO);

        execv(command, argv);
        /* Never reached except on error. */
        ZPN_ZVM_LOG(AL_ERROR, "Could not execv %s - %s", command, strerror(errno));
        _exit(1);
    }

    /* Parent! Close the side of the pipes we do not use. */
    close(pipe_to_child[0]);
    close(pipe_from_child[1]);

    *fd_to_child = pipe_to_child[1];
    *fd_from_child = pipe_from_child[0];

    //fprintf(stderr, "to_child = %d, from_child = %d\n", pipe_to_child[1], pipe_from_child[0]);

    return pid;
}


int zvm_run_cmd(char *out, size_t *out_len, char *path, int timeout_ms, ...)
{
    int sock_in, sock_out;
    char *w = out;
    char *e;
    struct pollfd pfd;
    pid_t pid, pid2;
    int i;
    va_list va, va2;
    char **argv;
    int argv_len;
    int flags;
    int ret = ZVM_RESULT_NO_ERROR;
    size_t len;
    int res;
    int child_status;
    int poll_timeout_ms = (timeout_ms > 0) ? timeout_ms : DEAFULT_POLL_TIMEOUT_MS;

    if ((out == NULL) ||
        (out_len == NULL) ||
        (path == NULL) ||
        (*out_len == 0)) {
        return (ZVM_RESULT_BAD_ARGUMENT);
    }

    e = w + *out_len;

    *out_len = 0;

    /* Create argument array. */
    va_start(va, timeout_ms);
    va_copy(va2, va);
    for (i = 0; va_arg(va, char *); i++);
    argv_len = i + 2;
    argv = (char **) ZPN_ZVM_MALLOC(argv_len * sizeof(char *));
    if (!argv) {
        va_end(va);
        return ZVM_RESULT_NO_MEMORY;
    }
    i = 0;
    argv[i] = path;
    while ((i < (argv_len - 1)) && (argv[i] != NULL)) {
        i++;
        argv[i] = va_arg(va2, char *);
    }
    argv[argv_len - 1] = NULL;

    /* Spawn process, make read socket non-blocking */
    pid = spawn(path, argv, &sock_out, &sock_in);
    if (pid < 0) {
        va_end(va);
        va_end(va2);
        ZPN_ZVM_FREE(argv);
        return ZVM_RESULT_ERR;
    }
    //fprintf(stderr, "Spawned pid = %d\n", (int) pid);
    if ((flags = fcntl(sock_in, F_GETFL, 0)) < 0) {
        fprintf(stderr, "Could not get socket flags/fcntl: %s\n", strerror(errno));
        ZPN_ZVM_LOG(AL_ERROR, "Could not get socket flags/fcntl: %s", strerror(errno));
        va_end(va);
        va_end(va2);
        ZPN_ZVM_FREE(argv);
        return ZVM_RESULT_ERR;
    }
    if (fcntl(sock_in, F_SETFL, flags | O_NONBLOCK) < 0) {
        fprintf(stderr, "Could not set non blocking: %s\n", strerror(errno));
        ZPN_ZVM_LOG(AL_ERROR, "Could not set non blocking: %s", strerror(errno));
        va_end(va);
        va_end(va2);
        ZPN_ZVM_FREE(argv);
        return ZVM_RESULT_ERR;
    }

    /* Read data until full or process exits or times out. */
    while (w < e) {
        pfd.fd = sock_in;
        pfd.events = POLLIN | POLLPRI;
        pfd.revents = 0;

        res = poll(&pfd, 1, poll_timeout_ms /* milliseconds */);
        if (res > 0) {
            /* Can read data. */
            len = read(sock_in, w, e - w);
            if (len == 0) {
                /* Program exited */
                break;
            } else if (len > 0) {
                if (len == (e - w)) {
                    /* Filled buffer, but program may not be
                     * done. Kill child, but we return 'success' in
                     * this case- we just got more data than
                     * expected. */
                    w += len;
                    ret = ZVM_RESULT_ERR;
                    break;
                } else {
                    w += len;
                    /* Could be more to read. */
                }
            } else {
                /* Socket error? */
                fprintf(stderr, "Received %s", strerror(errno));
                ZPN_ZVM_LOG(AL_ERROR, "Received %s", strerror(errno));
                ret = ZVM_RESULT_ERR;
                break;
            }
        } else {
            /* Timeout. */
            //fprintf(stderr, "Poll timed out\n");
            ret = ZVM_RESULT_ERR;
            break;
        }
    }
    if ((w == e) && (w > out)) {
        w--;
    }
    if (w < e) {
        *w = 0;
    }
    *out_len = w - out;

    /* Make sure child is dead. This will error if it's already
     * gone. */
    close(sock_in);
    close(sock_out);

    pid2 = wait4(pid, &child_status, WNOHANG, NULL);
    if (pid2 == 0) {
        /* Still running */
        res = kill(pid, SIGTERM);
        if (res != 0) {
            //fprintf(stderr, "Kill returned %s\n", strerror(errno));
        } else {
            //fprintf(stderr, "Killed pid = %d\n", (int) pid);
        }
        pid2 = wait4(pid, &child_status, 0, NULL);
    }
    if (pid2 != pid) {
        fprintf(stderr, "Pid mismatch\n");
        ZPN_ZVM_LOG(AL_ERROR, "Pid mismatch");
    }
    if (WIFEXITED(child_status)) {
        if (WEXITSTATUS(child_status) == 0) {
            /* Exit clean */
            //fprintf(stderr, "Clean child exit, ret = 0\n");
        } else {
            /* Exit unclean */
            ret = ZVM_RESULT_UNLCEAN_EXIT;
            //fprintf(stderr, "Clean child exit, ret = !0\n");
        }
    } else {
        /* Not exited. */
        ret = ZVM_RESULT_ERR;
        //fprintf(stderr, "Unclean child exit\n");
    }

    va_end(va);
    va_end(va2);

    ZPN_ZVM_FREE(argv);

    return ret;
}

enum zvm_vm_type zvm_type_get(void)
{
    struct stat st;
    FILE *fp;
    char buf[1000];
    size_t buf_len;

    int res;

    if (global_known) return global_vm_type;

    /* Check for vmware */
    if (stat(USR_BIN_VMWARE_RPCTOOL, &st) == 0) {
        /* Just because it has rpctool doesn't mean it is vmware. Now
         * we try communicate using rpctool. We do this by trying to
         * set guestinfo. */
        buf_len = sizeof(buf);

        res = zvm_run_cmd(buf, &buf_len, USR_BIN_VMWARE_RPCTOOL, 0, "info-set guestinfo.zscalertesting 1", NULL);
        if (res) {
            ZPN_ZVM_LOG(AL_ERROR, "vmware RPCtool exists, but does not function. Must not be vmware.");
        } else {
            ZPN_ZVM_LOG(AL_ERROR, "vmware RPCtool exists and functions. Assuming vmware");
            global_vm_type = zvm_vm_type_esx;
            global_known = 1;
        }
    }

    /* Check for vmware */
    if (stat(USR_SBIN_VMWARE_RPCTOOL, &st) == 0) {
        /* Just because it has rpctool doesn't mean it is vmware. Now
         * we try communicate using rpctool. We do this by trying to
         * set guestinfo. */
        buf_len = sizeof(buf);

        res = zvm_run_cmd(buf, &buf_len, USR_SBIN_VMWARE_RPCTOOL, 0, "info-set guestinfo.zscalertesting 1", NULL);
        if (res) {
            ZPN_ZVM_LOG(AL_ERROR, "vmware RPCtool exists, but does not function. Must not be vmware.");
        } else {
            ZPN_ZVM_LOG(AL_ERROR, "vmware RPCtool exists and functions. Assuming vmware");
            global_vm_type = zvm_vm_type_esx;
            global_known = 1;
        }
    }

    /* Check for vmware on FreeBSD */
    if (stat(USR_LOCAL_BIN_VMWARE_RPCTOOL, &st) == 0) {
        /* Just because it has rpctool doesn't mean it is vmware. Now
         * we try communicate using rpctool. We do this by trying to
         * set guestinfo. */
        buf_len = sizeof(buf);

        res = zvm_run_cmd(buf, &buf_len, USR_LOCAL_BIN_VMWARE_RPCTOOL, 0, "info-set guestinfo.zscalertesting 1", NULL);
        if (res) {
            ZPN_ZVM_LOG(AL_ERROR, "vmware RPCtool exists, but does not function. Must not be vmware.");
        } else {
            ZPN_ZVM_LOG(AL_ERROR, "vmware RPCtool exists and functions. Assuming vmware");
            global_vm_type = zvm_vm_type_esx;
            global_known = 1;
        }
    }

    if (!global_known && (stat("/var/lib/waagent", &st) == 0)) {
        global_vm_type = zvm_vm_type_azure;
        global_known = 1;
    }

    if (!global_known) {
        fp = fopen(SYS_VENDOR_FILEPATH, "r");
        if (fp) {
            memset(buf, 0, sizeof(buf));
            fscanf_nowarn(fp, "%999s", buf);
            if (strncasecmp(buf, "Nutanix", strlen("Nutanix")) == 0) {
                global_vm_type = zvm_vm_type_nutanix;
                global_known = 1;
            }
            fclose(fp);
        }
    }

    if (!global_known) {
        fp = fopen(PRODUCT_VERSION_FILEPATH, "r");
        if (fp) {
            memset(buf, 0, sizeof(buf));
            fscanf_nowarn(fp, "%999s", buf);
            if (strncasecmp(buf, "Hyper-V", strlen("Hyper-V")) == 0) {
                global_vm_type = zvm_vm_type_hyper_v;
                global_known = 1;
            }
            fclose(fp);
        }
    }

    /*Check for KVM*/
    if (!global_known) {
        fp = fopen(PRODUCT_NAME_FILEPATH, "r");
        if (fp) {
            memset(buf, 0, sizeof(buf));
            fscanf_nowarn(fp, "%999[^\n]", buf);
            if ((strncasecmp(buf, "KVM", strlen("KVM")) == 0) || (strncasecmp(buf, "OpenStack Compute", strlen("OpenStack Compute")) == 0)) {
                global_vm_type = zvm_vm_type_kvm;
                global_known = 1;
            }
            fclose(fp);
        }
    }

    if (!global_known && (stat("/.dockerenv", &st) == 0)) {
        global_vm_type = zvm_vm_type_docker;
        previous_docker_type = zvm_vm_type_docker;
        /*
         * ET-39604 If it is docker then we still
         * want to keep on checking if this docker
         * is deployed inside an AWS instance so
         * not setting global_known here
         * global_known = 1;
         */
    }

    if (!global_known) {
        /* Check for AWS. */
        char tmp_buf[1000];
        size_t tmp_buf_len = sizeof(tmp_buf);
        if (ZVM_RESULT_NO_ERROR == zvm_aws_get_instance_metadata("/", &tmp_buf[0], &tmp_buf_len, 0, NULL, 0)) {
            if (zvm_vm_type_docker == global_vm_type) {
                /*
                 * ET-39604 this use case is when
                 * docker is deployed inside the
                 * aws instance
                 */
                global_vm_type = zvm_vm_type_aws_docker;
                previous_docker_type = zvm_vm_type_aws_docker;
            } else {
                global_vm_type = zvm_vm_type_aws;
            }
        }
        if (global_vm_type == zvm_vm_type_unknown || global_vm_type == zvm_vm_type_docker) {
            fp = fopen(SYS_VENDOR_FILEPATH, "r");
            if (fp) {
                memset(buf, 0, sizeof(buf));
                fscanf_nowarn(fp, "%999s", buf);
                if (strncasecmp(buf, "Amazon", strlen("Amazon")) == 0) {
                    if (global_vm_type == zvm_vm_type_docker) {
                        global_vm_type = zvm_vm_type_aws_docker;
                        previous_docker_type = zvm_vm_type_aws_docker;
                    } else {
                        global_vm_type = zvm_vm_type_aws;
                    }
                }
                fclose(fp);
            }
        }
    }

    if (!global_known) {
        /* Check if we are in kubernetes or openshift env */
        if (is_kubernetes_environment()) {
            global_vm_type = zvm_vm_type_k8s;
            global_known = 1;
        } else if (is_openshift_environment()) {
            global_vm_type = zvm_vm_type_openshift;
            global_known = 1;
        }
    }

    if (!global_known) {
       fp = fopen(PRODUCT_NAME_FILEPATH, "r");
        if (fp) {
            memset(buf, 0, sizeof(buf));
            fscanf_nowarn(fp, "%999[^\n]", buf);
            if (strncasecmp(buf, "Google Compute Engine", strlen("Google Compute Engine")) == 0) {
                global_vm_type = zvm_vm_type_gcp;
                global_known = 1;
            }
            fclose(fp);
        }
    }
    global_known = 1;
    return global_vm_type;
}

int zvm_test_set_type(enum zvm_vm_type type)
{
    global_known = 1;
    global_vm_type = type;
    return ZVM_RESULT_NO_ERROR;
}

int zvm_test_set_id_callback(zvm_test_get_buf_f *f)
{
    get_id = f;
    return ZVM_RESULT_NO_ERROR;
}

int zvm_test_set_config_callback(zvm_test_get_buf_f *f)
{
    get_config = f;
    return ZVM_RESULT_NO_ERROR;
}

static int zvm_get_instance_metadata_generic(const char *hostname,
                                             const char *remote_host,
                                             int port,
                                             const char *url,
                                             const char *extra_header1,
                                             const char *extra_header2,
                                             char *out_buf,
                                             size_t *out_buf_len,
                                             const char *platform,
                                             char *err_str,
                                             size_t err_str_len)
{
    int ret = ZVM_RESULT_ERR;
    struct evbuffer *body = NULL;
    struct evbuffer *extra_headers = NULL;
    enum fohh_http_client_status status;

    struct fohh_http_client *client =
        fohh_http_client_create_synchronous(zcdns,
                                            NULL,
                                            hostname,
                                            remote_host,
                                            NULL,
                                            port,
                                            0,
                                            ZVM_TIMEOUT_GENERIC_CONN_SETUP_US,
                                            &status);
    if (client) {
        if (status == fohh_http_client_status_success) {
            int res;
            enum fohh_http_client_request_status r_status;
            int http_status;

            extra_headers = evbuffer_new();
            if (!extra_headers) {
                if (err_str && err_str_len) {
                    snprintf(err_str, err_str_len, "no memory for extra headers for %s instance", platform);
                }
                ZPN_ZVM_LOG(AL_ERROR, "Could not create evbuffer for extra headers");
                goto done;
            }

            if (extra_header1)
                evbuffer_add_printf(extra_headers, "%s\r\n", extra_header1);
            if (extra_header2)
                evbuffer_add_printf(extra_headers, "%s\r\n", extra_header2);

            res = fohh_http_client_request_synchronous(client,
                                                       ZVM_TIMEOUT_GENERIC_HTTP_REQ_US,
                                                       FOHH_HTTP_METHOD_GET,
                                                       url,
                                                       extra_headers,
                                                       NULL,
                                                       NULL,
                                                       &r_status,
                                                       &http_status,
                                                       &body);
            if ((res == FOHH_RESULT_NO_ERROR) && (http_status == 200) && body) {
                size_t len = *out_buf_len;
                (*out_buf_len) = evbuffer_remove(body, out_buf, len);
                if (len > *out_buf_len) {
                    out_buf[*out_buf_len] = 0;
                } else {
                    out_buf[len - 1] = 0;
                }
                ret = ZVM_RESULT_NO_ERROR;
            } else {
                if (err_str && err_str_len) {
                    snprintf(err_str, err_str_len, "Failed to get %s instance metadata using IMDSv1, res = %d, url = %s, status = %s, http_status = %d, body = %p",
                                    platform, res, url, fohh_http_client_request_status_str(r_status), http_status, body);
                }
            }
        } else {
            if (err_str && err_str_len) {
                snprintf(err_str, err_str_len, "Failed to connect to %s instance metadata service, status = %s",
                                    platform, fohh_http_client_status_str(status));
            }
        }
    } else {
        if (err_str && err_str_len) {
            snprintf(err_str, err_str_len, "Failed to create http client");
        }
    }

done:
    if (body) evbuffer_free(body);
    if (extra_headers) evbuffer_free(extra_headers);
    if (client) fohh_http_client_destroy_from_another_thread(client);
    return ret;
}

static int zvm_extract_region_id_info(char *out_buf, size_t out_buf_len, enum zvm_vm_type vm_type) {
    int ret = ZVM_RESULT_NO_ERROR;
    char *zone_start = NULL;
    const char *location = NULL;
    JSON_Value *metadata_json = NULL;
    JSON_Object *metadata_root_object = NULL;

    switch (vm_type) {
        case zvm_vm_type_aws:
        case zvm_vm_type_aws_docker:
            if (!(out_buf_len > 0 && out_buf_len < REGION_ID_INFO_LEN)) {
                ZPN_ZVM_LOG(AL_ERROR, "region info len(%d) for AWS instance is not valid", (int) out_buf_len);
                ret = ZVM_RESULT_BAD_DATA;
            } else {
                /* out_buf should contain region info */
                pthread_mutex_lock(&g_region_id_info.region_id_info_lock);
                memset(g_region_id_info.region_id_info, 0, sizeof(g_region_id_info.region_id_info));
                strncpy(g_region_id_info.region_id_info, out_buf, out_buf_len);
                pthread_mutex_unlock(&g_region_id_info.region_id_info_lock);
            }
            break;
        case zvm_vm_type_azure:
            metadata_json = json_parse_string(out_buf);
            if (!metadata_json) {
                ZPN_ZVM_LOG(AL_ERROR, "Data %s received for metadata response from azure instance metadata service is not a valid JSON", out_buf);
                ret = ZVM_RESULT_BAD_DATA;
                goto done;
            }

            metadata_root_object = json_value_get_object(metadata_json);
            if (!metadata_root_object) {
                ZPN_ZVM_LOG(AL_ERROR, "metadata response %s from azure instance metadata service is not a valid JSON", out_buf);
                ret = ZVM_RESULT_BAD_DATA;
                goto done;
            }

            location = json_object_dotget_string(metadata_root_object, "location");
            if (!location) {
                ZPN_ZVM_LOG(AL_ERROR, "metadata response %s from azure instance metadata service does not have location field", out_buf);
                ret = ZVM_RESULT_BAD_DATA;
                goto done;
            }

            /* location should contain region info */
            pthread_mutex_lock(&g_region_id_info.region_id_info_lock);
            memset(g_region_id_info.region_id_info, 0, sizeof(g_region_id_info.region_id_info));
            snprintf(g_region_id_info.region_id_info, sizeof(g_region_id_info.region_id_info), "%s", location);
            pthread_mutex_unlock(&g_region_id_info.region_id_info_lock);
            break;
        case zvm_vm_type_gcp:
            zone_start = strstr(out_buf, "zones/");
            if (zone_start != NULL) {
                /* Move the pointer to the beginning of the zone information */
                zone_start += strlen("zones/");
                /* out_buf should contain region info */
                pthread_mutex_lock(&g_region_id_info.region_id_info_lock);
                memset(g_region_id_info.region_id_info, 0, sizeof(g_region_id_info.region_id_info));
                snprintf(g_region_id_info.region_id_info, sizeof(g_region_id_info.region_id_info), "%s", zone_start);

                /* Null-terminate at the next '/' if found */
                char *slash_pos = strchr(g_region_id_info.region_id_info, '/');
                if (slash_pos) {
                    *slash_pos = '\0';
                }
                pthread_mutex_unlock(&g_region_id_info.region_id_info_lock);
            } else {
                ZPN_ZVM_LOG(AL_ERROR, "zone info is absent for GCP instance");
                ret = ZVM_RESULT_BAD_DATA;
            }
            break;
        default:
            break;
    }
done:
    pthread_mutex_lock(&g_region_id_info.region_id_info_lock);
    if (strnlen(g_region_id_info.region_id_info, REGION_ID_INFO_LEN) > 0) {
        __sync_add_and_fetch_4(&g_zvm_stats.reg_id_query_success_count, 1);
        g_region_id_info.region_id_info_known = 1;
    }
    pthread_mutex_unlock(&g_region_id_info.region_id_info_lock);
    return ret;
}

static int zvm_collect_region_info(char *err_str, size_t err_str_len)
{
    /* JSONify data... */
    JSON_Value *av_json = NULL;
    JSON_Object *av_root_object = NULL;
    JSON_Array  *api_versions = NULL;
    size_t array_count = 0;
    int ret = ZVM_RESULT_NO_ERROR;
    char out_buf[INSTANCE_METADATA_LEN] = {0};
    size_t out_buf_len = sizeof(out_buf);
    enum zvm_vm_type vm_type = zvm_type_get();

    pthread_mutex_lock(&g_region_id_info.region_id_info_lock);
    if (g_region_id_info.region_id_info_known) {
        ZPN_ZVM_DEBUG_REGION_ID("Region ID(%s) is already known for %s instance", g_region_id_info.region_id_info, zvm_vm_type_to_str_for_qbr_concise(vm_type));
        pthread_mutex_unlock(&g_region_id_info.region_id_info_lock);
        return ZVM_RESULT_NO_ERROR;
    }
    pthread_mutex_unlock(&g_region_id_info.region_id_info_lock);

    switch(vm_type) {
    case zvm_vm_type_aws_docker:
    case zvm_vm_type_aws:
        __sync_add_and_fetch_4(&g_zvm_stats.reg_id_query_count, 1);
        ZPN_ZVM_LOG(AL_INFO, "Query initiated to retrieve region ID for %s instance type", zvm_vm_type_to_str_for_qbr_concise(vm_type));
        ret = zvm_aws_get_instance_metadata("/latest/meta-data/placement/region",
                                            out_buf,
                                            &out_buf_len,
                                            0,
                                            err_str,
                                            err_str_len);
        if (ret != ZVM_RESULT_NO_ERROR) {
            ZPN_ZVM_LOG(AL_ERROR, "Could not get region info for AWS instance, error: %s", err_str);
        } else {
            ZPN_ZVM_DEBUG_REGION_ID("Region ID query response(%s) for %s instance type", out_buf, zvm_vm_type_to_str_for_qbr_concise(vm_type));
            zvm_extract_region_id_info(out_buf, out_buf_len, zvm_vm_type_aws);
        }
        break;

    case zvm_vm_type_gcp:
        /*
         * For GCP instance
         * =================
         * Query: curl -H "Metadata-Flavor: Google" "http://metadata.google.internal/computeMetadata/v1/instance/zone"
         * Sample response: projects/485344481500/zones/us-west1-b
         *
         * We then have extract region(us-west1-b) info
         */
        __sync_add_and_fetch_4(&g_zvm_stats.reg_id_query_count, 1);
        ZPN_ZVM_LOG(AL_INFO, "Query initiated to retrieve region ID for %s instance type", zvm_vm_type_to_str_for_qbr_concise(vm_type));
        ret = zvm_get_instance_metadata_generic("metadata.google.internal",
                                                "metadata.google.internal",
                                                80,
                                                "/computeMetadata/v1/instance/zone",
                                                "Metadata-Flavor: Google",
                                                NULL,
                                                out_buf,
                                                &out_buf_len,
                                                zvm_vm_type_to_str_concise(vm_type),
                                                err_str,
                                                err_str_len);
        if (ret != ZVM_RESULT_NO_ERROR) {
            ZPN_ZVM_LOG(AL_ERROR, "Could not get region info for GCP instance, error: %s", err_str);
        } else {
            ZPN_ZVM_DEBUG_REGION_ID("Region ID query response(%s) for %s instance type", out_buf, zvm_vm_type_to_str_for_qbr_concise(vm_type));
            zvm_extract_region_id_info(out_buf, out_buf_len, zvm_vm_type_gcp);
        }
        break;

    case zvm_vm_type_azure:
        /*
         * For Azure instance
         * =================
         * First get api version:
         * Query: curl -H "Metadata:true" "http://***************/metadata/versions"
         * Sample response: {"apiVersions":["2017-03-01","2017-04-02","2017-08-01","2017-10-01","2017-12-01","2018-02-01","2018-04-02","2018-10-01","2019-02-01","2019-03-11","2019-04-30","2019-06-01","2019-06-04","2019-08-01","2019-08-15","2019-11-01","2020-06-01","2020-07-15","2020-09-01","2020-10-01","2020-12-01","2021-01-01","2021-02-01","2021-03-01","2021-05-01","2021-08-01","2021-10-01","2021-11-01","2021-11-15","2021-12-13","2023-07-01","2023-11-01","2023-11-15"]}
         *
         * Then get instance metadata using the latest api version
         * Query: curl -H "Metadata:true" "http://***************/metadata/instance/compute?api-version=2023-11-15"
         * Sample response: {"compute":{"additionalCapabilities":{"hibernationEnabled":"false"},"azEnvironment":"AzurePublicCloud","customData":"","evictionPolicy":"","extendedLocation":{"name":"","type":""},"host":{"id":""},"hostGroup":{"id":""},"isHostCompatibilityLayerVm":"false","isVmInStandbyPool":"","licenseType":"","location":"westus2","name":"tdixit-ac-test48","offer":"","osProfile":{"adminUsername":"azureuser","computerName":"tdixit-ac-test48","disablePasswordAuthentication":"true"},"osType":"Linux","physicalZone":"uswest2-AZ03","placementGroupId":"","plan":{"name":"","product":"","publisher":""},"platformFaultDomain":"0","platformSubFaultDomain":"","platformUpdateDomain":"0","priority":"","provider":"Microsoft.Compute","publicKeys":[{"keyData":"ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABgQDnix2GixakEKQvm7Z2KikprfTZRicZ8H7XIWAYLjeyMxjQF+c5C1mhrQ5MCDoXduSIvo1JsEGU8KG1zhHWf3AnXjJ4hl8ZDn1+NnKYZIr/OZUD8ZeW5v0xSlIncjNqnuljqd6m1IoQpSnXUOcZ6M6d9+OwWckq67BuCUr4f4FHJ/HNuX6Qsh4javVJnrptvWwIAwTXVsRPRZemUSslseFDuUIIgPVltnj4U2RiL+45n+HaBa8FYl+68qSNiRfjwaJswYEtMgd2uhjXwy/WwA0oI/+CsaA8NFtWZuP3BCr6rD1E8ovvLs00nkSjFtlMz7INTIxv25mGBZL06NErRA9lRJgvKT+FKynE3dYEZrT1twpkWGr8NzDWi6NukhsRb3zLTJrUx1Xbb/2u7oVZJPJ33uB5Bz1IO1y8a1z9WImzfkNXx4Q2Y8qn6SHJPl26QHAV90l05aMcKT7s6dIz0VQpK0dXA98BAlNE2KT+k03pT0pTPC+LCCPlHwfuVe58yak= generated-by-azure","path":"/home/<USER>/.ssh/authorized_keys"}],"publisher":"","resourceGroupName":"INTERNALIMAGES","resourceId":"/subscriptions/c0cac3b8-8e65-4a26-ba84-975697a90372/resourceGroups/INTERNALIMAGES/providers/Microsoft.Compute/virtualMachines/tdixit-ac-test48","securityProfile":{"encryptionAtHost":"false","secureBootEnabled":"false","securityType":"","virtualTpmEnabled":"false"},"sku":"","storageProfile":{"dataDisks":[],"imageReference":{"communityGalleryImageId":"","exactVersion":"2024.0925.1433","id":"/subscriptions/c0cac3b8-8e65-4a26-ba84-975697a90372/resourceGroups/internalImages/providers/Microsoft.Compute/galleries/internalDevelopmentImages/images/zpa-connector-rhel9-x86_64/versions/2024.0925.1433","offer":"","publisher":"","sharedGalleryImageId":"","sku":"","version":""},"osDisk":{"caching":"ReadWrite","createOption":"FromImage","diffDiskSettings":{"option":""},"diskSizeGB":"64","encryptionSettings":{"diskEncryptionKey":{"secretUrl":"","sourceVault":{"id":""}},"enabled":"false","keyEncryptionKey":{"keyUrl":"","sourceVault":{"id":""}}},"image":{"uri":""},"managedDisk":{"id":"/subscriptions/c0cac3b8-8e65-4a26-ba84-975697a90372/resourceGroups/INTERNALIMAGES/providers/Microsoft.Compute/disks/tdixit-ac-test48_OsDisk_1_8adb9b341e8d4cc8acead773d005a319","storageAccountType":"Premium_LRS"},"name":"tdixit-ac-test48_OsDisk_1_8adb9b341e8d4cc8acead773d005a319","osType":"Linux","vhd":{"uri":""},"writeAcceleratorEnabled":"false"},"resourceDisk":{"size":"16384"}},"subscriptionId":"c0cac3b8-8e65-4a26-ba84-975697a90372","tags":"","tagsList":[],"userData":"","version":"2024.0925.1433","virtualMachineScaleSet":{"id":""},"vmId":"344b6e2d-c319-4514-81c1-a91a9f98ac41","vmScaleSetName":"","vmSize":"Standard_D2s_v3","zone":"1"},"network":{"interface":[{"ipv4":{"ipAddress":[{"privateIpAddress":"*********","publicIpAddress":""}],"subnet":[{"address":"********","prefix":"24"}]},"ipv6":{"ipAddress":[]},"macAddress":"002248BC65F2"},{"ipv4":{"ipAddress":[{"privateIpAddress":"**********","publicIpAddress":""}],"subnet":[{"address":"********","prefix":"24"}]},"ipv6":{"ipAddress":[]},"macAddress":"002248B42932"}]}}
         */
        __sync_add_and_fetch_4(&g_zvm_stats.reg_id_query_count, 1);
        ZPN_ZVM_LOG(AL_INFO, "Query initiated to retrieve region ID for %s instance type", zvm_vm_type_to_str_for_qbr_concise(vm_type));
        ret = zvm_get_instance_metadata_generic("***************",
                                                "***************",
                                                80,
                                                "/metadata/versions",
                                                "Metadata:true",
                                                "Accept: application/json",
                                                out_buf,
                                                &out_buf_len,
                                                zvm_vm_type_to_str_concise(vm_type),
                                                err_str,
                                                err_str_len);
        if (ret != ZVM_RESULT_NO_ERROR) {
            ZPN_ZVM_LOG(AL_ERROR, "Could not get API version info for Azure instance, error: %s", err_str);
        } else {
            ZPN_ZVM_DEBUG_REGION_ID("API version query response(%s) for %s instance type", out_buf, zvm_vm_type_to_str_for_qbr_concise(vm_type));
            av_json = json_parse_string(out_buf);
            if (!av_json) {
                ZPN_ZVM_LOG(AL_ERROR, "Data received for api versions response from azure instance metadata service is not JSON");
                ret = ZVM_RESULT_BAD_DATA;
                goto done;
            }

            av_root_object = json_value_get_object(av_json);
            if (!av_root_object) {
                ZPN_ZVM_LOG(AL_ERROR, "api versions response from azure instance metadata service is not JSON");
                ret = ZVM_RESULT_BAD_DATA;
                goto done;
            }

            api_versions = json_object_dotget_array(av_root_object, "apiVersions");
            if (api_versions) {
                array_count = json_array_get_count(api_versions);
                if (array_count > 0) {
                    const char *api_version = json_array_get_string(api_versions, array_count - 1);
                    if (api_version) {
                        char url[URL_MAX_LEN] = {0};
                        memset(out_buf, 0, sizeof(out_buf));
                        out_buf_len = sizeof(out_buf);
                        snprintf(url, sizeof(url), "/metadata/instance/compute?api-version=%s", api_version);
                        ret = zvm_get_instance_metadata_generic("***************",
                                                                "***************",
                                                                80,
                                                                url,
                                                                "Metadata:true",
                                                                "Accept: application/json",
                                                                out_buf,
                                                                &out_buf_len,
                                                                zvm_vm_type_to_str_concise(vm_type),
                                                                err_str,
                                                                err_str_len);
                        if (ret != ZVM_RESULT_NO_ERROR) {
                            ZPN_ZVM_LOG(AL_ERROR, "Could not get instance metadata info for Azure instance, error: %s", err_str);
                        } else {
                            ZPN_ZVM_DEBUG_REGION_ID("Region ID query response(%s) for %s instance type", out_buf, zvm_vm_type_to_str_for_qbr_concise(vm_type));
                            zvm_extract_region_id_info(out_buf, out_buf_len, zvm_vm_type_azure);
                        }
                    }
                } else {
                    ZPN_ZVM_LOG(AL_ERROR, "api versions response from azure instance metadata service does not contain non-zero valid version list");
                    ret = ZVM_RESULT_BAD_DATA;
                    goto done;
                }
            } else {
                ZPN_ZVM_LOG(AL_ERROR, "api versions response from azure instance metadata service does not contain apiVersions array field");
                ret = ZVM_RESULT_BAD_DATA;
                goto done;
            }
        }
        break;

    default:
        break;
    }

done:
    if (av_json) json_value_free(av_json);
    pthread_mutex_lock(&g_region_id_info.region_id_info_lock);
    if (strnlen(g_region_id_info.region_id_info, REGION_ID_INFO_LEN) > 0) {
        __sync_add_and_fetch_4(&g_zvm_stats.reg_id_query_success_count, 1);
        g_region_id_info.region_id_info_known = 1;
    }
    pthread_mutex_unlock(&g_region_id_info.region_id_info_lock);
    return ret;
}

int zvm_ensure_region_id(char *err_str, size_t err_str_len) {
    int ret = zvm_collect_region_info(err_str, err_str_len);
    if (ret != ZVM_RESULT_NO_ERROR) {
        ZPN_ZVM_LOG(AL_ERROR, "Error while collecting region id, error: %s", err_str);
    }
    return ret;
}

void zvm_fill_region_id(char *reg_id, size_t reg_id_len) {
    pthread_mutex_lock(&g_region_id_info.region_id_info_lock);
    if (g_region_id_info.region_id_info_known) {
        snprintf(reg_id, reg_id_len, "%s", g_region_id_info.region_id_info);
    }
    pthread_mutex_unlock(&g_region_id_info.region_id_info_lock);
}

static int zvm_aws_imdsv1_region_id_http_resp_cb(struct fohh_http_client *client,
                                                 enum fohh_http_client_request_status status,
                                                 int req_status,
                                                 struct evbuffer *result_buf,
                                                 void *void_cookie,
                                                 int64_t int_cookie)
{
    char *out = NULL;
    size_t len = 0;
    ZPN_ZVM_DEBUG_REGION_ID("zvm_aws_imdsv1_region_id_http_resp_cb: %s", fohh_http_client_request_status_str(status));
    switch(status) {
        case fohh_http_client_request_status_timeout:
        case fohh_http_client_request_status_failure:
            ZPN_ZVM_LOG(AL_ERROR, "Region ID request using IMDSv1 failed/timed-out, status: %d", status);
            return ZVM_RESULT_ERR;
        case fohh_http_client_request_status_success:
            if (req_status == 200 && result_buf) {
                len = evbuffer_get_length(result_buf);
                if (len != 0) {
                    out = ZPN_ZVM_CALLOC(len+1);
                    evbuffer_copyout(result_buf, out, len);
                } else {
                    ZPN_ZVM_LOG(AL_ERROR, "Invalid response for region ID request from IMDSv1 server, len: %zu", len);
                    return ZVM_RESULT_ERR;
                }
                ZPN_ZVM_DEBUG_REGION_ID("zvm_aws_imdsv1_region_id_http_resp_cb: region id query response: %s", out);
                zvm_extract_region_id_info(out, len, zvm_vm_type_aws);

                if (out != NULL) {
                    ZPN_ZVM_FREE(out);
                }
            }
            break;
        default:
            ZPN_ZVM_LOG(AL_ERROR, "Failure response for region ID from IMDSv1 server, status : %d", status);
            return ZVM_RESULT_ERR;
    }

    return ZVM_RESULT_NO_ERROR;
}


static int zvm_aws_imdsv1_region_id_http_conn_cb(struct fohh_http_client *client,
                                                 enum fohh_http_client_status status,
                                                 void *void_cookie,
                                                 int64_t int_cookie)
{
    int res = ZVM_RESULT_NO_ERROR;
    ZPN_ZVM_DEBUG_REGION_ID("zvm_aws_imdsv1_region_id_http_conn_cb: %s", fohh_http_client_status_str(status));
    if (status == fohh_http_client_status_success) {
        res = fohh_http_client_request(client,
                                       ZVM_TIMEOUT_GENERIC_HTTP_REQ_ASYNC_US,
                                       FOHH_HTTP_METHOD_GET,
                                       "/latest/meta-data/placement/region",
                                       NULL,
                                       NULL,
                                       NULL,
                                       zvm_aws_imdsv1_region_id_http_resp_cb,
                                       NULL,
                                       0);

        if (res != ZVM_RESULT_NO_ERROR) {
            ZPN_ZVM_LOG(AL_ERROR, "Error in sending region ID request using IMDSv1, status : %d", status);
        }
    } else {
        __sync_add_and_fetch_4(&g_zvm_stats.reg_id_fohh_http_free_count, 1);
        fohh_http_client_destroy_from_another_thread(client);
    }

    return res;
}

static int zvm_aws_fetch_region_id_imdsv1()
{
    __sync_add_and_fetch_4(&g_zvm_stats.reg_id_query_count, 1);
    struct fohh_http_client *client = fohh_http_client_create(zcdns,
                                                              NULL,
                                                              "***************",
                                                              "***************",
                                                              NULL,
                                                              80,
                                                              0,
                                                              ZVM_TIMEOUT_GENERIC_CONN_SETUP_ASYNC_US,
                                                              zvm_aws_imdsv1_region_id_http_conn_cb,
                                                              NULL,
                                                              0);
    if (!client) {
        ZPN_ZVM_LOG(AL_ERROR, "Failed to create AWS HTTP client for querying region ID from IMDSv1 server.");
        return ZVM_RESULT_ERR;
    } else {
        __sync_add_and_fetch_4(&g_zvm_stats.reg_id_fohh_http_create_count, 1);
    }
    return ZVM_RESULT_NO_ERROR;
}

static int zvm_aws_imdsv2_region_id_http_resp_cb(struct fohh_http_client *client,
                                                 enum fohh_http_client_request_status status,
                                                 int req_status,
                                                 struct evbuffer *result_buf,
                                                 void *void_cookie,
                                                 int64_t int_cookie)
{
    char *out = NULL;
    size_t len = 0;
    ZPN_ZVM_DEBUG_REGION_ID("zvm_aws_imdsv2_region_id_http_resp_cb: %s", fohh_http_client_request_status_str(status));
    switch(status) {
        case fohh_http_client_request_status_timeout:
        case fohh_http_client_request_status_failure:
            ZPN_ZVM_LOG(AL_ERROR, "Region ID request with token failed/timed-out, status: %d. Now trying for IMDSv1", status);
            return zvm_aws_fetch_region_id_imdsv1();
        case fohh_http_client_request_status_success:
            if (req_status == 200 && result_buf) {
                len = evbuffer_get_length(result_buf);
                if (len != 0) {
                    out = ZPN_ZVM_CALLOC(len+1);
                    evbuffer_copyout(result_buf, out, len);
                } else {
                    ZPN_ZVM_LOG(AL_ERROR, "Invalid response for region ID request from IMDSv2 server, len: %zu", len);
                    return ZVM_RESULT_ERR;
                }
                ZPN_ZVM_DEBUG_REGION_ID("zvm_aws_imdsv2_region_id_http_resp_cb: region id query response: %s", out);
                zvm_extract_region_id_info(out, len, zvm_vm_type_aws);

                if (out != NULL) {
                    ZPN_ZVM_FREE(out);
                }
            } else {
                ZPN_ZVM_LOG(AL_ERROR, "Region ID request with token resulted in error response, status: %d, req_status: %d. Now trying for IMDSv1", status, req_status);
                zvm_aws_fetch_region_id_imdsv1();
            }
            break;
        default:
            ZPN_ZVM_LOG(AL_ERROR, "Failure response for region ID from IMDSv2 server, status : %d", status);
            return ZVM_RESULT_ERR;
    }

    return ZVM_RESULT_NO_ERROR;
}

static int zvm_aws_imdsv2_region_id_http_conn_cb(struct fohh_http_client *client,
                                                 enum fohh_http_client_status status,
                                                 void *void_cookie,
                                                 int64_t int_cookie)
{
    int res = ZVM_RESULT_NO_ERROR;
    char *token = (char *) void_cookie;
    ZPN_ZVM_DEBUG_REGION_ID("zvm_aws_imdsv2_region_id_http_conn_cb: %s", fohh_http_client_status_str(status));
    if (status == fohh_http_client_status_success && token) {
        struct evbuffer *extra_headers = evbuffer_new();

        evbuffer_add_printf(extra_headers, "X-aws-ec2-metadata-token: %s\r\n", token);
        res = fohh_http_client_request(client,
                                       ZVM_TIMEOUT_GENERIC_HTTP_REQ_ASYNC_US,
                                       FOHH_HTTP_METHOD_GET,
                                       "/latest/meta-data/placement/region",
                                       extra_headers,
                                       NULL,
                                       NULL,
                                       zvm_aws_imdsv2_region_id_http_resp_cb,
                                       NULL,
                                       0);

        if (res != ZVM_RESULT_NO_ERROR) {
            ZPN_ZVM_LOG(AL_ERROR, "Error in sending region ID request with token, status : %d", status);
        }
        evbuffer_free(extra_headers);
    } else {
        if (token) {
            __sync_add_and_fetch_4(&g_zvm_stats.reg_id_imdsv2_token_free_count, 1);
           ZPN_ZVM_FREE(token);
        }
        __sync_add_and_fetch_4(&g_zvm_stats.reg_id_fohh_http_free_count, 1);
        fohh_http_client_destroy_from_another_thread(client);
    }

    return res;
}

static int zvm_aws_imdsv2_token_http_resp_cb(struct fohh_http_client *client,
                                             enum fohh_http_client_request_status status,
                                             int req_status,
                                             struct evbuffer *result_buf,
                                             void *void_cookie,
                                             int64_t int_cookie)
{
    struct fohh_http_client *client_region_id = NULL;
    int res = ZVM_RESULT_NO_ERROR;
    char *out = NULL;
    size_t len = 0;
    ZPN_ZVM_DEBUG_REGION_ID("zvm_aws_imdsv2_token_http_resp_cb: %s", fohh_http_client_request_status_str(status));
    switch(status) {
        case fohh_http_client_request_status_timeout:
        case fohh_http_client_request_status_failure:
            ZPN_ZVM_LOG(AL_ERROR, "Token request failed/timed-out, status: %d. Now trying for IMDSv1", status);
            return zvm_aws_fetch_region_id_imdsv1();
        case fohh_http_client_request_status_success:
            if (req_status == 200 && result_buf) {
                len = evbuffer_get_length(result_buf);
                if (len != 0) {
                    out = ZPN_ZVM_CALLOC(len+1);
                    __sync_add_and_fetch_4(&g_zvm_stats.reg_id_imdsv2_token_alloc_count, 1);
                    evbuffer_copyout(result_buf, out, len);
                    ZPN_ZVM_DEBUG_REGION_ID("zvm_aws_imdsv2_token_http_resp_cb: token retrieved: %s", out);
                } else {
                    ZPN_ZVM_LOG(AL_ERROR, "Invalid response for token request from IMDSv2 server, len: %zu", len);
                    return ZVM_RESULT_ERR;
                }
                client_region_id = fohh_http_client_create(zcdns,
                                                           NULL,
                                                           "***************",
                                                           "***************",
                                                           NULL,
                                                           80,
                                                           0,
                                                           ZVM_TIMEOUT_GENERIC_CONN_SETUP_ASYNC_US,
                                                           zvm_aws_imdsv2_region_id_http_conn_cb,
                                                           out,
                                                           0);
                if (!client_region_id) {
                    if (out != NULL) {
                        __sync_add_and_fetch_4(&g_zvm_stats.reg_id_imdsv2_token_free_count, 1);
                        ZPN_ZVM_FREE(out);
                    }
                    ZPN_ZVM_LOG(AL_ERROR, "Failed to create AWS HTTP client for querying region ID from IMDSv2 server.");
                    return ZVM_RESULT_ERR;
                } else {
                    __sync_add_and_fetch_4(&g_zvm_stats.reg_id_fohh_http_create_count, 1);
                }
            } else {
                ZPN_ZVM_LOG(AL_ERROR, "Token request resulted in error response, status: %d, req_status: %d. Now trying for IMDSv1", status, req_status);
                zvm_aws_fetch_region_id_imdsv1();
            }
            break;
        default:
            ZPN_ZVM_LOG(AL_ERROR, "Failure response for token from IMDSv2 server, status : %d", status);
            return ZVM_RESULT_ERR;
    }

    return res;
}

static int zvm_aws_imdsv2_token_http_conn_cb(struct fohh_http_client *client,
                                             enum fohh_http_client_status status,
                                             void *void_cookie,
                                             int64_t int_cookie)
{
    int res = ZVM_RESULT_NO_ERROR;
    ZPN_ZVM_DEBUG_REGION_ID("zvm_aws_imdsv2_token_http_conn_cb: %s", fohh_http_client_status_str(status));
    if (status == fohh_http_client_status_success) {
        struct evbuffer *extra_headers = evbuffer_new();

        /* First try fetching token(IMDSv2 way) */
        evbuffer_add_printf(extra_headers, "X-aws-ec2-metadata-token-ttl-seconds: 21600\r\n");
        res = fohh_http_client_request(client,
                                       ZVM_TIMEOUT_GENERIC_HTTP_REQ_ASYNC_US,
                                       FOHH_HTTP_METHOD_PUT,
                                       "/latest/api/token",
                                       extra_headers,
                                       NULL,
                                       NULL,
                                       zvm_aws_imdsv2_token_http_resp_cb,
                                       void_cookie,
                                       int_cookie);

        if (res != ZVM_RESULT_NO_ERROR) {
            ZPN_ZVM_LOG(AL_ERROR, "Error in sending token request, status : %d", status);
        }
        evbuffer_free(extra_headers);
    } else {
        __sync_add_and_fetch_4(&g_zvm_stats.reg_id_fohh_http_free_count, 1);
        fohh_http_client_destroy_from_another_thread(client);
    }

    return res;
}

static int zvm_azure_region_id_http_resp_cb(struct fohh_http_client *client,
                                            enum fohh_http_client_request_status status,
                                            int req_status,
                                            struct evbuffer *result_buf,
                                            void *void_cookie,
                                            int64_t int_cookie)
{
    char *out = NULL;
    size_t len = 0;
    ZPN_ZVM_DEBUG_REGION_ID("zvm_azure_region_id_http_resp_cb: %s", fohh_http_client_request_status_str(status));
    switch(status) {
        case fohh_http_client_request_status_timeout:
        case fohh_http_client_request_status_failure:
            ZPN_ZVM_LOG(AL_ERROR, "Region ID request with apiVersion failed/timed-out, status: %d", status);
            return ZVM_RESULT_ERR;
        case fohh_http_client_request_status_success:
            if (req_status == 200 && result_buf) {
                len = evbuffer_get_length(result_buf);
                if (len != 0) {
                    out = ZPN_ZVM_CALLOC(len+1);
                    evbuffer_copyout(result_buf, out, len);
                } else {
                    ZPN_ZVM_LOG(AL_ERROR, "Invalid response for region ID request from Azure IMDS server, len: %zu", len);
                    return ZVM_RESULT_ERR;
                }
                ZPN_ZVM_DEBUG_REGION_ID("zvm_azure_region_id_http_resp_cb: region id query response: %s", out);
                zvm_extract_region_id_info(out, len, zvm_vm_type_azure);

                if (out != NULL) {
                    ZPN_ZVM_FREE(out);
                }
            } else {
                ZPN_ZVM_LOG(AL_ERROR, "Region ID request with api version resulted in error response, status: %d, req_status: %d", status, req_status);
                return ZVM_RESULT_ERR;
            }
            break;
        default:
            ZPN_ZVM_LOG(AL_ERROR, "Failure response for region ID from IMDSv2 server, status : %d", status);
            return ZVM_RESULT_ERR;
    }

    return ZVM_RESULT_NO_ERROR;
}

static int zvm_azure_region_id_http_conn_cb(struct fohh_http_client *client,
                                            enum fohh_http_client_status status,
                                            void *void_cookie,
                                            int64_t int_cookie)
{
    /* JSONify data... */
    JSON_Value *av_json;
    JSON_Object *av_root_object;
    JSON_Array  *api_versions = NULL;
    int res = ZVM_RESULT_NO_ERROR;
    char *api_version_response = (char *) void_cookie;
    ZPN_ZVM_DEBUG_REGION_ID("zvm_azure_region_id_http_conn_cb: %s", fohh_http_client_status_str(status));
    if (status == fohh_http_client_status_success && api_version_response) {
        size_t array_count = 0;

        av_json = json_parse_string(api_version_response);
        if (!av_json) {
            ZPN_ZVM_LOG(AL_ERROR, "Api versions response from azure instance metadata service is not a valid JSON");
            return ZVM_RESULT_ERR;
        }

        av_root_object = json_value_get_object(av_json);
        if (!av_root_object) {
            ZPN_ZVM_LOG(AL_ERROR, "Api versions response from azure instance metadata service is not a valid JSON");
            json_value_free(av_json);
            return ZVM_RESULT_ERR;
        }

        api_versions = json_object_dotget_array(av_root_object, "apiVersions");
        if (api_versions) {
            array_count = json_array_get_count(api_versions);
            if (array_count > 0) {
                const char *api_version = json_array_get_string(api_versions, array_count - 1);
                if (api_version) {
                    char url[URL_MAX_LEN] = {0};
                    snprintf(url, sizeof(url), "/metadata/instance/compute?api-version=%s", api_version);

                    struct evbuffer *extra_headers = evbuffer_new();

                    evbuffer_add_printf(extra_headers, "Metadata:true\r\n");
                    evbuffer_add_printf(extra_headers, "Accept: application/json\r\n");
                    res = fohh_http_client_request(client,
                                                ZVM_TIMEOUT_GENERIC_HTTP_REQ_ASYNC_US,
                                                FOHH_HTTP_METHOD_GET,
                                                url,
                                                extra_headers,
                                                NULL,
                                                NULL,
                                                zvm_azure_region_id_http_resp_cb,
                                                NULL,
                                                0);

                    if (res != ZVM_RESULT_NO_ERROR) {
                        ZPN_ZVM_LOG(AL_ERROR, "Error in sending region ID request with apiVersion: %s, status : %d", api_version, status);
                    }
                    evbuffer_free(extra_headers);
                }
            }
        }
        json_value_free(av_json);
    } else {
        if (api_version_response) {
            __sync_add_and_fetch_4(&g_zvm_stats.reg_id_api_ver_free_count, 1);
           ZPN_ZVM_FREE(api_version_response);
        }
        __sync_add_and_fetch_4(&g_zvm_stats.reg_id_fohh_http_free_count, 1);
        fohh_http_client_destroy_from_another_thread(client);
    }

    return res;
}


static int zvm_azure_api_version_http_resp_cb(struct fohh_http_client *client,
                                              enum fohh_http_client_request_status status,
                                              int req_status,
                                              struct evbuffer *result_buf,
                                              void *void_cookie,
                                              int64_t int_cookie)
{
    struct fohh_http_client *client_region_id = NULL;
    int res = ZVM_RESULT_NO_ERROR;
    char *out = NULL;
    size_t len = 0;
    ZPN_ZVM_DEBUG_REGION_ID("zvm_azure_api_version_http_resp_cb: %s", fohh_http_client_request_status_str(status));
    switch(status) {
        case fohh_http_client_request_status_timeout:
        case fohh_http_client_request_status_failure:
            ZPN_ZVM_LOG(AL_ERROR, "Api version request failed/timed-out, status: %d", status);
            return ZVM_RESULT_ERR;
        case fohh_http_client_request_status_success:
            if (req_status == 200 && result_buf) {
                len = evbuffer_get_length(result_buf);
                if (len != 0) {
                    out = ZPN_ZVM_CALLOC(len+1);
                    __sync_add_and_fetch_4(&g_zvm_stats.reg_id_api_ver_alloc_count, 1);
                    evbuffer_copyout(result_buf, out, len);
                } else {
                    ZPN_ZVM_LOG(AL_ERROR, "Invalid response for api versions request from IMDS server, len: %zu", len);
                    return ZVM_RESULT_ERR;
                }
                ZPN_ZVM_DEBUG_REGION_ID("zvm_azure_api_version_http_resp_cb: api version query response: %s", out);
                client_region_id = fohh_http_client_create(zcdns,
                                                           NULL,
                                                           "***************",
                                                           "***************",
                                                           NULL,
                                                           80,
                                                           0,
                                                           ZVM_TIMEOUT_GENERIC_CONN_SETUP_ASYNC_US,
                                                           zvm_azure_region_id_http_conn_cb,
                                                           out,
                                                           0);
                if (!client_region_id) {
                    if (out != NULL) {
                        __sync_add_and_fetch_4(&g_zvm_stats.reg_id_api_ver_free_count, 1);
                        ZPN_ZVM_FREE(out);
                    }
                    ZPN_ZVM_LOG(AL_ERROR, "Failed to create Azure HTTP client for querying region ID from IMDS server.");
                    return ZVM_RESULT_ERR;
                } else {
                    __sync_add_and_fetch_4(&g_zvm_stats.reg_id_fohh_http_create_count, 1);
                }
            } else {
                ZPN_ZVM_LOG(AL_ERROR, "Api versions request resulted in error response, status: %d, req_status: %d", status, req_status);
            }
            break;
        default:
            ZPN_ZVM_LOG(AL_ERROR, "Failure response for api version from Azure IMDS server, status : %d", status);
            return ZVM_RESULT_ERR;
    }

    return res;
}

static int zvm_azure_api_version_http_conn_cb(struct fohh_http_client *client,
                                              enum fohh_http_client_status status,
                                              void *void_cookie,
                                              int64_t int_cookie)
{
    int res = ZVM_RESULT_NO_ERROR;
    ZPN_ZVM_DEBUG_REGION_ID("zvm_azure_api_version_http_conn_cb: %s", fohh_http_client_status_str(status));
    if (status == fohh_http_client_status_success) {
        struct evbuffer *extra_headers = evbuffer_new();

        /* First fetch apiVersions */
        evbuffer_add_printf(extra_headers, "Metadata:true\r\n");
        evbuffer_add_printf(extra_headers, "Accept: application/json\r\n");
        res = fohh_http_client_request(client,
                                       ZVM_TIMEOUT_GENERIC_HTTP_REQ_ASYNC_US,
                                       FOHH_HTTP_METHOD_GET,
                                       "/metadata/versions",
                                       extra_headers,
                                       NULL,
                                       NULL,
                                       zvm_azure_api_version_http_resp_cb,
                                       void_cookie,
                                       int_cookie);

        if (res != ZVM_RESULT_NO_ERROR) {
            ZPN_ZVM_LOG(AL_ERROR, "Error in sending apiVersions request, status : %d", status);
        }
        evbuffer_free(extra_headers);
    } else {
        __sync_add_and_fetch_4(&g_zvm_stats.reg_id_fohh_http_free_count, 1);
        fohh_http_client_destroy_from_another_thread(client);
    }

    return res;
}

static int zvm_gcp_region_id_http_resp_cb(struct fohh_http_client *client,
                                          enum fohh_http_client_request_status status,
                                          int req_status,
                                          struct evbuffer *result_buf,
                                          void *void_cookie,
                                          int64_t int_cookie)
{
    char *out = NULL;
    size_t len = 0;
    ZPN_ZVM_DEBUG_REGION_ID("zvm_gcp_region_id_http_resp_cb: %s", fohh_http_client_request_status_str(status));
    switch(status) {
        case fohh_http_client_request_status_timeout:
        case fohh_http_client_request_status_failure:
            ZPN_ZVM_LOG(AL_ERROR, "Region ID request for GCP instance failed/timed-out, status: %d", status);
            return ZVM_RESULT_ERR;
        case fohh_http_client_request_status_success:
            if (req_status == 200 && result_buf) {
                len = evbuffer_get_length(result_buf);
                if (len != 0) {
                    out = ZPN_ZVM_CALLOC(len+1);
                    evbuffer_copyout(result_buf, out, len);
                } else {
                    ZPN_ZVM_LOG(AL_ERROR, "Invalid response for region ID request from GCP IMDS server, len: %zu", len);
                    return ZVM_RESULT_ERR;
                }
                ZPN_ZVM_DEBUG_REGION_ID("zvm_gcp_region_id_http_resp_cb: region id query response: %s", out);
                zvm_extract_region_id_info(out, len, zvm_vm_type_gcp);

                if (out != NULL) {
                    ZPN_ZVM_FREE(out);
                }
            } else {
                ZPN_ZVM_LOG(AL_ERROR, "Region ID request with api version resulted in error response, status: %d, req_status: %d", status, req_status);
                return ZVM_RESULT_ERR;
            }
            break;
        default:
            ZPN_ZVM_LOG(AL_ERROR, "Failure response for region ID from IMDSv2 server, status : %d", status);
            return ZVM_RESULT_ERR;
    }

    return ZVM_RESULT_NO_ERROR;
}


static int zvm_gcp_region_id_http_conn_cb(struct fohh_http_client *client,
                                          enum fohh_http_client_status status,
                                          void *void_cookie,
                                          int64_t int_cookie)
{
    int res = ZVM_RESULT_NO_ERROR;
    ZPN_ZVM_DEBUG_REGION_ID("zvm_gcp_region_id_http_conn_cb: %s", fohh_http_client_status_str(status));
    if (status == fohh_http_client_status_success) {
        struct evbuffer *extra_headers = evbuffer_new();

        /* Fetch region ID */
        evbuffer_add_printf(extra_headers, "Metadata-Flavor: Google\r\n");
        res = fohh_http_client_request(client,
                                       ZVM_TIMEOUT_GENERIC_HTTP_REQ_ASYNC_US,
                                       FOHH_HTTP_METHOD_GET,
                                       "/computeMetadata/v1/instance/zone",
                                       extra_headers,
                                       NULL,
                                       NULL,
                                       zvm_gcp_region_id_http_resp_cb,
                                       void_cookie,
                                       int_cookie);

        if (res != ZVM_RESULT_NO_ERROR) {
            ZPN_ZVM_LOG(AL_ERROR, "Error in sending region ID request for GCP, status : %d", status);
        }
        evbuffer_free(extra_headers);
    } else {
        __sync_add_and_fetch_4(&g_zvm_stats.reg_id_fohh_http_free_count, 1);
        fohh_http_client_destroy_from_another_thread(client);
    }

    return res;
}


static int zvm_collect_region_info_async() {
    enum zvm_vm_type vm_type = zvm_type_get();
    struct fohh_http_client *client;

    pthread_mutex_lock(&g_region_id_info.region_id_info_lock);
    if (g_region_id_info.region_id_info_known) {
        ZPN_ZVM_DEBUG_REGION_ID("Region ID(%s) is already known for %s instance", g_region_id_info.region_id_info, zvm_vm_type_to_str_for_qbr_concise(vm_type));
        pthread_mutex_unlock(&g_region_id_info.region_id_info_lock);
        return ZVM_RESULT_NO_ERROR;
    }
    pthread_mutex_unlock(&g_region_id_info.region_id_info_lock);

    switch (vm_type) {
        case zvm_vm_type_aws:
        case zvm_vm_type_aws_docker:
            __sync_add_and_fetch_4(&g_zvm_stats.reg_id_query_count, 1);
            ZPN_ZVM_LOG(AL_INFO, "IMDSv2 query initiated to retrieve region ID for %s instance type", zvm_vm_type_to_str_for_qbr_concise(vm_type));
            client = fohh_http_client_create(zcdns,
                                             NULL,
                                             "***************",
                                             "***************",
                                             NULL,
                                             80,
                                             0,
                                             ZVM_TIMEOUT_GENERIC_CONN_SETUP_ASYNC_US,
                                             zvm_aws_imdsv2_token_http_conn_cb,
                                             NULL,
                                             0);
            if (!client) {
                ZPN_ZVM_LOG(AL_ERROR, "Failed to create AWS HTTP client for querying token from IMDSv2 server.");
                return ZVM_RESULT_ERR;
            } else {
                __sync_add_and_fetch_4(&g_zvm_stats.reg_id_fohh_http_create_count, 1);
            }
            break;
        case zvm_vm_type_azure:
            __sync_add_and_fetch_4(&g_zvm_stats.reg_id_query_count, 1);
            ZPN_ZVM_LOG(AL_INFO, "Query initiated to retrieve region ID for %s instance type", zvm_vm_type_to_str_for_qbr_concise(vm_type));
            client = fohh_http_client_create(zcdns,
                                             NULL,
                                             "***************",
                                             "***************",
                                             NULL,
                                             80,
                                             0,
                                             ZVM_TIMEOUT_GENERIC_CONN_SETUP_ASYNC_US,
                                             zvm_azure_api_version_http_conn_cb,
                                             NULL,
                                             0);
            if (!client) {
                ZPN_ZVM_LOG(AL_ERROR, "Failed to create Azure HTTP client for querying api version from IMDS server.\n");
                return ZVM_RESULT_ERR;
            } else {
                __sync_add_and_fetch_4(&g_zvm_stats.reg_id_fohh_http_create_count, 1);
            }
            break;
        case zvm_vm_type_gcp:
            __sync_add_and_fetch_4(&g_zvm_stats.reg_id_query_count, 1);
            ZPN_ZVM_LOG(AL_INFO, "Query initiated to retrieve region ID for %s instance type", zvm_vm_type_to_str_for_qbr_concise(vm_type));
            client = fohh_http_client_create(zcdns,
                                             NULL,
                                             "metadata.google.internal",
                                             "metadata.google.internal",
                                             NULL,
                                             80,
                                             0,
                                             ZVM_TIMEOUT_GENERIC_CONN_SETUP_ASYNC_US,
                                             zvm_gcp_region_id_http_conn_cb,
                                             NULL,
                                             0);
            if (!client) {
                ZPN_ZVM_LOG(AL_ERROR, "Failed to create GCP HTTP client for querying region id from IMDS server.\n");
                return ZVM_RESULT_ERR;
            } else {
                __sync_add_and_fetch_4(&g_zvm_stats.reg_id_fohh_http_create_count, 1);
            }
            break;
        default:
            break;
    }
    return ZVM_RESULT_NO_ERROR;
}

int zvm_ensure_region_id_async() {
    return zvm_collect_region_info_async();
}

void zpn_zvm_get_zvm_stats(uint32* reg_id_fohh_http_create_count,
                           uint32* reg_id_fohh_http_free_count,
                           uint32* reg_id_imdsv2_token_alloc_count,
                           uint32* reg_id_imdsv2_token_free_count,
                           uint32* reg_id_api_ver_alloc_count,
                           uint32* reg_id_api_ver_free_count,
                           uint32* reg_id_query_count,
                           uint32* reg_id_query_success_count,
                           uint32* reg_id_query_failure_count)
{
    *reg_id_fohh_http_create_count = __sync_add_and_fetch_4(&g_zvm_stats.reg_id_fohh_http_create_count, 0);
    *reg_id_fohh_http_free_count = __sync_add_and_fetch_4(&g_zvm_stats.reg_id_fohh_http_free_count, 0);
    *reg_id_imdsv2_token_alloc_count = __sync_add_and_fetch_4(&g_zvm_stats.reg_id_imdsv2_token_alloc_count, 0);
    *reg_id_imdsv2_token_free_count = __sync_add_and_fetch_4(&g_zvm_stats.reg_id_imdsv2_token_free_count, 0);
    *reg_id_api_ver_alloc_count = __sync_add_and_fetch_4(&g_zvm_stats.reg_id_api_ver_alloc_count, 0);
    *reg_id_api_ver_free_count = __sync_add_and_fetch_4(&g_zvm_stats.reg_id_api_ver_free_count, 0);
    *reg_id_query_count = __sync_add_and_fetch_4(&g_zvm_stats.reg_id_query_count, 0);
    *reg_id_query_success_count = __sync_add_and_fetch_4(&g_zvm_stats.reg_id_query_success_count, 0);
    *reg_id_query_failure_count = *reg_id_query_count - *reg_id_query_success_count;
}
