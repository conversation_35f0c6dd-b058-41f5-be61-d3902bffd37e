/*
 * zvm.h. Copyright (C) 2016 Zscaler Inc. All Rights Reserved
 */

/*
 * zvm - Pull out zscaler provisioning information from a virtual
 * machine/environment.
 *
 * All of these routines can block.
 */

#ifndef _ZVM_H_
#define _ZVM_H_

#include <stdlib.h>
#include "zcdns/zcdns.h"
#include "argo/argo_private.h"

#define ZVM_TIMEOUT_GENERIC_CONN_SETUP_US (1ll*1000*1000)
#define ZVM_TIMEOUT_GENERIC_HTTP_REQ_US (1ll*1000*1000)

#define ZVM_TIMEOUT_AWS_CONN_SETUP_US (1ll*1000*1000)
#define ZVM_TIMEOUT_AWS_HTTP_REQ_US (1ll*1000*1000)

#define ZVM_TIMEOUT_GENERIC_CONN_SETUP_ASYNC_US (5ll*1000*1000)
#define ZVM_TIMEOUT_GENERIC_HTTP_REQ_ASYNC_US (5ll*1000*1000)

#define SYS_VENDOR_FILEPATH          "/sys/devices/virtual/dmi/id/sys_vendor"
#define PRODUCT_VERSION_FILEPATH     "/sys/devices/virtual/dmi/id/product_version"
#define PRODUCT_NAME_FILEPATH        "/sys/devices/virtual/dmi/id/product_name"
#define USR_BIN_VMWARE_RPCTOOL       "/usr/bin/vmware-rpctool"
#define USR_SBIN_VMWARE_RPCTOOL      "/usr/sbin/vmware-rpctool"
#define USR_LOCAL_BIN_VMWARE_RPCTOOL "/usr/local/bin/vmware-rpctool"
#define KUBERNETES_ENV               "KUBERNETES_ENVIRONMENT"

enum zvm_vm_type {
    zvm_vm_type_unknown = 0,
    zvm_vm_type_esx,
    zvm_vm_type_azure,
    zvm_vm_type_aws,
    zvm_vm_type_aws_docker,
    zvm_vm_type_docker,
    zvm_vm_type_nutanix,
    zvm_vm_type_hyper_v,
    zvm_vm_type_kvm,
    zvm_vm_type_gcp,
    zvm_vm_type_k8s,
    zvm_vm_type_openshift
};

extern enum zvm_vm_type global_vm_type;
extern struct argo_log_collection *zpn_zvm_event_collection;
extern struct zpath_allocator zpn_zvm_allocator;

#define ZPN_ZVM_MALLOC(x) zpath_malloc(&zpn_zvm_allocator, x, __LINE__, __FILE__)
#define ZPN_ZVM_FREE(x) zpath_free(x, __LINE__, __FILE__)
#define ZPN_ZVM_FREE_SLOW(x) zpath_free_slow(x, __LINE__, __FILE__)
#define ZPN_ZVM_CALLOC(x) zpath_calloc(&zpn_zvm_allocator, x, __LINE__, __FILE__)
#define ZPN_ZVM_REALLOC(x, y) zpath_realloc(&zpn_zvm_allocator, x, y, __LINE__, __FILE__)
#define ZPN_ZVM_STRDUP(x, y) zpath_strdup(&zpn_zvm_allocator, x, y, __LINE__, __FILE__)
#define ZPN_ZVM_SAFE_STRDUP(x) ZPN_DR_STRDUP(x, strlen(x) + 1)

#define ZVM_LOG_PREFIX  "ZVM: "

extern uint64_t zpn_zvm_debug_log;
extern const char *zpn_zvm_log_debug_names[];

#define ZPN_ZVM_DEBUG_REGION_ID_BIT              (uint64_t)0x000000001

/* The following is used to set up more debugging... Add to this list
 * as you add debugging bits. Make sure they stay in sync. */
#define ZPN_ZVM_DEBUG_LOG_NAMES {                \
        "region_id",                             \
        NULL                                     \
}

#define ZPN_ZVM_LOG(priority, format...) if (1) ARGO_LOG(zpn_zvm_event_collection, priority, "zpn_zvm", ZVM_LOG_PREFIX format)
#define ZPN_ZVM_DEBUG_LOG(condition, format...) if (1) ARGO_DEBUG_LOG(condition, zpath_event_collection, argo_log_priority_debug, "zpn_zvm", ##format)
#define ZPN_ZVM_DEBUG_REGION_ID(format...) ZPN_ZVM_DEBUG_LOG(zpn_zvm_debug_log & ZPN_ZVM_DEBUG_REGION_ID_BIT, ##format)

/* Taken from argo */
#define ZVM_RESULT_NO_ERROR          0   /* AKA success */
#define ZVM_RESULT_ERR               1   /* Generic failure: when
                                            * none of the below
                                            * apply. */
#define ZVM_RESULT_NOT_FOUND         2   /* Could not find what was
                                            * requested. */
#define ZVM_RESULT_NO_MEMORY         3   /* Could not allocate
                                            * memory */
#define ZVM_RESULT_CANT_WRITE        4   /* Failure to write (output
                                            * callback failed?) */
#define ZVM_RESULT_ERR_TOO_LARGE     5   /* Requested data doesn't
                                            * fit in space provided */
#define ZVM_RESULT_BAD_ARGUMENT      6   /* Asked for something
                                            * wrong. */
#define ZVM_RESULT_INSUFFICIENT_DATA 7   /* Was not provided enough
                                            * data to perform
                                            * operation */
#define ZVM_RESULT_NOT_IMPLEMENTED   8   /* Yes, for features that
                                            * are not yet
                                            * implemented. */
#define ZVM_RESULT_BAD_DATA          9   /* Tried to parse data, but
                                            * format seemed wrong. */
#define ZVM_RESULT_REPARSE          10   /* Tried to parse data, but
                                            * doing so changed state,
                                            * requiring us to
                                            * re-parse. This should
                                            * NEVER be seen in
                                            * user-code. It is used
                                            * internally. */
#define ZVM_RESULT_WOULD_BLOCK      11   /* Attempting operation
                                            * would result in
                                            * blocking. Bad, naughty
                                            * blocking. */
#define ZVM_RESULT_BAD_STATE        12   /* Encountered some bad
                                            * internal state */
#define ZVM_RESULT_INCOMPLETE       13   /* Encountered some bad
                                            * internal state */
#define ZVM_RESULT_ASYNCHRONOUS     14   /* Asynchronous result
                                            * expected later */

#define ZVM_RESULT_UNLCEAN_EXIT     15   /* Asynchronous result*/

#define ZVM_RESULT_MAX              15   /* Max error number */

#define ZVM_VERSION_IMDSV1          1
#define ZVM_VERSION_IMDSV2          2

/*
 * Will detect nonces like:
 *
 * 1|api.zscalerconnect.net|xyzzyxyzzyxyzzyaTdMWFu/iVwSU2Fho2q9dNCqZpvuJwdA3FTua2JQcO/of831jvE/WfMbiT8sTLrlMsCL3tAbDxpwmBDFTk1vUQVErxv4ojDYKgRsYIh1BxpJwNOVPR2JVVgU11qxvVAEk318SC4vFF0VhEB2b6YvyInft2/6V8HsWGWFvBhn7lbsRhOw3oYmMCuk/YMGLI0bnrZ1YgHsqpUSaE8HfTJNkPOw/PaSHqnDPgh98ltsCf5pI1EqfIx+/KoRsr4I3QbqdktSlheRQ04SSwJrp+iMk1S4QPRZPaojZbRNc29t0YEuwB74FPoK1n78Gc+aUFfTBKGpIJg==
 *
 */
#define ZVM_REGEX_ASSISTANT_NONCE "[0-9]{1,3}\\|([a-z-]+\\.){2,3}[a-z]+\\|[a-zA-Z0-9/+=]+"

#define IMAGE_VERSION_LEN 8
#define PLATFORM_DETAIL_LEN 64
#define INSTANCE_METADATA_LEN 8192
#define REGION_ID_INFO_LEN 64
#define URL_MAX_LEN 1024

#define DEAFULT_POLL_TIMEOUT_MS 1000

/*
 * Get the type as a string
 */
const char *zvm_vm_type_to_str(enum zvm_vm_type type);

int zvm_vm_type_is_zscaler_rh_image();
int zvm_vm_type_is_docker();
char *zvm_vm_type_rh_image_version();

/*
 * Get the concise vm type name
 * e.q AWS, Azure, Docker ...
 */
const char *zvm_vm_type_to_str_concise(enum zvm_vm_type type);

/*
 * Get the concise vm type name for QBR revamped efforts
 * e.q AWS, Azure, GCP ...
 */
const char *zvm_vm_type_to_str_for_qbr_concise(enum zvm_vm_type type);

/*
 * Verify that potential_key is a correctly formed provisioning
 * key. This should be a syntax/format verification only. Thie routine
 * should return ZVM_RESULT_NO_ERROR if the key appears to be a good
 * key, otherwise it should return ZVM_RESULT_BAD_DATA.
 */
typedef int (zvm_key_verify_f)(void *verify_cookie,
                               const char *potential_key);

/*
 * Get a key from custom data assiciated with this VM.
 *
 * The regular expression is used to capture the key. The verify
 * function (if provided) is used to check that the captured data is
 * actually a key. If no verify function is provided, any regex match
 * is assumed to be valid.
 *
 * The order of processing the custom data is as follows:
 *
 * 1. If the custom data can be extracted fully from base64, use the
 *    extracted version.
 *
 * 2. For each line of input data, scan for regex.
 *
 * 3. If regex match, use verify function (if provided).
 *
 * 4. If verified, check if line is a comment. (leading '#')
 *
 * 5. Remember first verified key for both commented and not commented
 *    lines.
 *
 * 6. If we have a verified key that was not a comment, return that.
 *
 * 7. If we have a verified key that is a comment, return that.
 *
 * 8. Return failure.
 */
int zvm_key_get_by_regex(const char *regex,
                         zvm_key_verify_f *verify_f,
                         void *verify_cookie,
                         char *key_value,
                         size_t *key_value_len);

/*
 * Get the id of this VM instance. If the VM type cannot be
 * determined, returns failure. Error message will be saved
 * in 'err_str' for logging purposes.
 */
int zvm_id_get(char *id_value,
               size_t *id_value_len, char *err_str, size_t err_str_len, int version);

/* Note: This routine operates by attempting zvm_id_get for each vm
 * type in the order of the enumerated type */
enum zvm_vm_type zvm_type_get(void);

/*
 * This is really a utility function used internally to zvm, but it's handy anyway.
 *
 * This routine runs the command in path, using optional string
 * arguments, which MUST be NULL terminated (i.e. last argument to
 * calls of zvm_run_cmd MUST be 'NULL')
 *
 * If the program output exceeds out_len, the return status in non-deterministic.
 *
 * If the program exits uncleanly, this returns error.
 *
 * *out_len is updated for both success and failure.
 *
 * This routine will wait at most timeout second for a result to return. Timeout by default is 1 second
 *
 * Timeouts return as ZVM_RESULT_ERR.
 */
int zvm_run_cmd(char *out, size_t *out_len, char *path, int timeout_ms, ...);


/* It is horrible that we need zcdns to run this software, but that's
 * just the way it is at the moment */
int zvm_init(struct argo_log_collection *event_log, struct zcdns *zcdns);

/*
 * Test routines. Use these if you wish to do some testing... But call
 * them before you actually try to do anything.
 */
typedef int zvm_test_get_buf_f(char *buf, size_t *buf_len);

int zvm_test_set_type(enum zvm_vm_type type);
int zvm_test_set_id_callback(zvm_test_get_buf_f *f);
int zvm_test_set_config_callback(zvm_test_get_buf_f *f);

/* extract region information */
int zvm_ensure_region_id(char *err_str, size_t err_str_len);
int zvm_ensure_region_id_async();
void zvm_fill_region_id(char *reg_id, size_t reg_id_len);
void zpn_zvm_get_zvm_stats(uint32*, uint32*, uint32*, uint32*, uint32*, uint32*, uint32*, uint32*, uint32*);

#endif /* _ZVM_H_ */
