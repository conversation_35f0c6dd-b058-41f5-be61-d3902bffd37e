#include "zpn_event/zpn_event_config_override.h"
#include "zpath_lib/zpath_config_override_keys.h"
#include "zpn_event/zpn_event.h"


/* zpn_event config overrides for broker. */
static struct zpath_config_override_desc zpn_event_broker_override_desc[] = {
    {
        .key                = CONFIG_SEND_AST_DISCONNECT_EVENT,
        .desc               = "When enabled, send connector control connection disconnect event",
        .details            = "0: Disabled\n"
                              "1: Enabled\n"
                              "Order of check: assistant gid, assistant group gid, customer gid, broker gid, root customer, global\n"
                              "default: 1 (Enabled)",
        .val_type           = config_type_int,
        .component_types    = config_component_broker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_inst_grp | config_target_gid_type_cust |
                              config_target_gid_type_zsroot | config_target_gid_type_global,
        .int_range_lo       = CONFIG_SEND_AST_DISCONNECT_EVENT_MIN,
        .int_range_hi       = CONFIG_SEND_AST_DISCONNECT_EVENT_MAX,
        .int_default        = CONFIG_SEND_AST_DISCONNECT_EVENT_DEFAULT,
        .feature_group      = FEATURE_GROUP_APP_CONNECTOR_EVENT,
        .value_traits       = config_value_traits_feature_enablement,
    },

    {
        .key                = CONFIG_SEND_PB_DISCONNECT_EVENT,
        .desc               = "When enabled, send PSE control connection disconnect event",
        .details            = "0: Disabled\n"
                              "1: Enabled\n"
                              "Order of check: pbroker gid, pbroker group gid, customer gid, broker gid, root customer, global\n"
                              "default: 1 (Enabled)",
        .val_type           = config_type_int,
        .component_types    = config_component_broker,
        .target_gid_types   = config_target_gid_type_inst | config_target_gid_type_inst_grp | config_target_gid_type_cust |
                              config_target_gid_type_zsroot | config_target_gid_type_global,
        .int_range_lo       = CONFIG_SEND_PB_DISCONNECT_EVENT_MIN,
        .int_range_hi       = CONFIG_SEND_PB_DISCONNECT_EVENT_MAX,
        .int_default        = CONFIG_SEND_PB_DISCONNECT_EVENT_DEFAULT,
        .feature_group      = FEATURE_GROUP_PRIVATE_SERVICE_EDGE_EVENT,
        .value_traits       = config_value_traits_feature_enablement,
    }
};

int zpn_event_broker_override_desc_init()
{
    size_t broker_override_desc_cnt = sizeof(zpn_event_broker_override_desc) / sizeof(zpn_event_broker_override_desc[0]);
    size_t i;
    int res;

    for (i = 0; i < broker_override_desc_cnt; i++) {
        res = zpath_config_override_desc_register(&zpn_event_broker_override_desc[i]);
        if (res) {
            ZPN_EVENT_LOG(AL_ERROR, "Could not register override description for %s: %s",
                          zpn_event_broker_override_desc[i].key, zpn_event_result_str(res));
            return res;
        }
    }

    return ZPN_EVENT_RESULT_NO_ERROR;
}
