/*
 * wally_postgres.c. Copyright (C) 2013 Zscaler, Inc. All Rights Reserved.
 *
 * Postgres origin DB interface.
 *
 * Minor notes:
 *
 * * Postgres columns are only added when the database is written- NOT when read.
 * * Argo fields are only added when the database is READ.
 */

#include <sys/queue.h>
//#include <math.h>
#include <event2/event.h>
#include <unistd.h>
#include <libpq-fe.h>
#include "zpath_misc/zpath_misc.h"
#include "zthread/zthread.h"
#include "zevent/zevent.h"
#include "wally/wally.h"
#include "wally/wally_private.h"
#include "wally/wally_postgres.h"
#include "wally/wally_db.h"
#include "wally/wally_oper.h"
#include "wally/wally_table_queue.h"

int wally_postgres_concurrent_write = 0; // changeable by wallyd commandline -concurrent_write
int wally_postgres_synchronous_commit_off = 0; // changeable by wallyd commandline -synchronous_commit_off
extern int wally_db_init_read_nolock;

#define WALLY_PSQL_PARAM_MAX_INDEX   11
/* Maximum number of rows to write before relinguishing the DB connection.
 * Let other tables bound to this connection a chance to write. */
#define WP_WRITE_BATCH_MAX_ROWS 10000

#define WP_MAX_LOCK_FAIL_TIME_US (1000*1000*120)  /* 120 seconds */

/* This is the SQL standard error code for lock not available: */
#define WP_LOCK_NOT_AVAILABLE "55P03"

#define WP_CONFLICT_ERROR            "21000"
#define WP_MAX_ERROR_CODE_LEN         10
#define WP_BATCH_RETRY_COUNT          10
#define WP_SQL_STMT_BUF_SIZE          (20 * 1024)
#define WP_BINDVAR_VALUE_BUF_SIZE_DFT (16 * 1024)
#define BIND_VAL_MAX_BUF_SIZE         (128*1024*1024)

/* Number of try lock attempts before logging warning event */
#define WALLY_TRYLOCK_LOG_INTERVAL_COUNT 120

#define WALLY_LOOKUP_TABLE_SUFFIX "__lookup_"

extern bool wally_origin_disc_manual;
extern int et_26295_test_size;

/* API to check if the template database exists on DB */
int wally_check_template_database_exists(PGconn *conn);

/* Get the status of server using PQ ping. */
static PGPing wp_get_ping_status(struct wp_db *wp_db)
{
    PGPing wally_ping_status = PQPING_NO_ATTEMPT;

    if (wp_db == NULL) {
        return wally_ping_status;
    }
    wally_ping_status = PQpingParams((const char **)wp_db->wally_postgres_param_names,
            (const char **)wp_db->wally_postgres_param_values, 0);

    switch (wally_ping_status) {
        case PQPING_OK:             /* server is accepting connections */
            break;
        case PQPING_REJECT:         /* server is alive but rejecting connections */
            WALLY_LOG(AL_CRITICAL, "Db %s server alive, rejecting connections", wp_db->name);
            break;
        case PQPING_NO_RESPONSE:    /* could not establish connection */
            WALLY_LOG(AL_CRITICAL, "Could not establish connection to Db %s server", wp_db->name);
            break;
        case PQPING_NO_ATTEMPT:     /* connection not attempted (bad params) */
            WALLY_LOG(AL_CRITICAL, "Connection not attempted to DB %s server, bad params", wp_db->name);
            break;
        default:
            WALLY_LOG(AL_CRITICAL, "Invalid return code DB %s server", wp_db->name);
            break;
    }
    return wally_ping_status;
}

/* Postgres DB reconnect routine, which creates new DB connection to the RDS .
 *
 * index : DB connection index
 * wp_db : Pointer to wp_db.
 * */
void wally_postgres_reconnect(int32_t index, struct wp_db *wp_db)
{
    int res = 0;

    if (wp_get_ping_status(wp_db) != PQPING_OK) {
        /* RDS is not accessible. Return */
        return;
    }
    /* Attempt to reset the connection, db_conn pointer is available */
    if (wp_db->connections[index].db_conn) {
        PQreset(wp_db->connections[index].db_conn);

        if (PQstatus(wp_db->connections[index].db_conn) == CONNECTION_OK) {
            WALLY_LOG(AL_NOTICE, "Reconnected to the database successfully.");
            goto SUCCESS;
        } else {
            WALLY_LOG(AL_ERROR, "DB reconnection failed: %s",
                    PQerrorMessage(wp_db->connections[index].db_conn));
        }

        /* Reset failed. Free the db_conn.  */
        PQfinish((PGconn *)(wp_db->connections[index].db_conn));
        wp_db->connections[index].db_conn = NULL;
    }
    /* db_conn is NULL. Create a new db_conn now */
    wp_db->connections[index].db_conn = PQconnectdbParams(
            (const char **)wp_db->wally_postgres_param_names,
            (const char **)wp_db->wally_postgres_param_values,
            0);
    if ((!wp_db->connections[index].db_conn) ||
            (PQstatus((PGconn *)(wp_db->connections[index].db_conn)) != CONNECTION_OK)) {
        if (wp_db->connections[index].db_conn) {
            /* close the connection and free the resources, will recover it in next try */
            PQfinish((PGconn *)(wp_db->connections[index].db_conn));
            wp_db->connections[index].db_conn = NULL;
        }
        WALLY_LOG(AL_ERROR, "PQconnectdbParams failed");
        return;
    }

SUCCESS:
    if (wp_db->connections[index].db_conn != NULL) {
        WALLY_LOG(AL_NOTICE, "Reconnected to the database successfully with a new connection");
        wp_db->connections[index].state = conn_idle;
        res = wally_postgres_db_conn_config(wp_db, wp_db->schema_name, index);
        if (res) {
            /* close the connection and free the resources, will recreate it in next try */
            PQfinish((PGconn *)(wp_db->connections[index].db_conn));
            wp_db->connections[index].db_conn = NULL;
            WALLY_LOG(AL_ERROR, "Postgres DB connection configuration failed");
            /* Mark the connection failed and retry recovery in next try */
            wp_db->connections[index].state = conn_failed;
            return;
        }
        /* Successful recovery. */
        wp_db->failed_connections_count--;
        WALLY_LOG(AL_NOTICE, "Recovery succeed. DB %s connection failed count %d.",
                wp_db->name, wp_db->failed_connections_count);
        if (wp_db->is_true_origin == false || (wp_db->is_true_origin == true &&
                    wp_db->is_alterable == true )) {
            /* Local DB recovery */
            wally_oper_event_post(E_DB_RECO, SE_DB_LOCAL, wp_db);
        } else {
            /* Remote DB recovery */
            wally_oper_event_post (E_DB_RECO, SE_DB_REMOTE, wp_db);
        }
        return;
    }

    WALLY_LOG(AL_ERROR, "Failed to reconnect to the database");
    return;
}

int64_t data_waiting_bucket_max[MAX_DATA_WAITING_BUCKET_SIZE] = { 0, 10, 30, 60, 120};
/* 0-10s    = buckets[0]
 * 10-30s   = buckets[1]
 * 30-60s   = buckets[2]
 * 60-120s  = buckets[3]
 * 120s >   = buckets[4]
*/
#define WALLY_DATA_WAITING_BUCKET_10S_INDEX                 0
#define WALLY_DATA_WAITING_BUCKET_30S_INDEX                 1
#define WALLY_DATA_WAITING_BUCKET_60S_INDEX                 2
#define WALLY_DATA_WAITING_BUCKET_120S_INDEX                3
#define WALLY_DATA_WAITING_BUCKET_MORE_THAN_120S_INDEX      4

/* The Template DB that stores the default privileges to roles
 * This is required, so that all new DB created with this template DB
 * will inherit the default privileges.
 */
const char* ITASCA_TEMPLATE_DB="itasca_template_db";

#define WALLY_DURATION_US_IN_S 1000000
int wp_send_poll_rollback(struct wp_connection *wp_conn);

static int xPQsendQueryParams(PGconn *conn,const char *command,int nParams,const Oid *paramTypes,
                                 const char *const *paramValues,const int *paramLengths,const int *paramFormats,int resultFormat);

/*
 *INSERT INTO table_name (column1, column2, column3, ...)*
 *VALUES (value1, value2, value3, ...)*
 *ON CONFLICT (conflict_column) *
 *DO UPDATE SET *
 *column1 = EXCLUDED.column1,*
 *column2 = EXCLUDED.column2,*
 *    ...;*
*/

struct wp_conn_pg_specific {
    struct wally_local_db_write_queue_object *write_objects[WALLY_MAX_WRITE_BATCH_SIZE];
    int write_objects_count;
    int64_t last_sequence;
    int64_t query_start_time;

};
int wp_fill_field_for_write(char **sos, char *ee,char **value, struct argo_structure_description *argo_desc,
                                                       struct argo_field_description *pd,
                                                       void *struct_data);

static __inline__ int wp_write_object_not_writeable_field(struct argo_object *write_object, struct wp_table *wpt, struct argo_field_description *pd) {
    if (!pd->write_value || pd->dont_db) return 1;
    if (wpt->db->is_true_origin && pd->dont_write_origin) return 1;;
    /* If the sequence is zero, skip it. This is done to keep from having duplicate insertions into a table for the first time,
     * as first-insert would otherwise always be zero for sequence, and wouldn't leave the value 0 until updated. */
    if (pd->is_sequence && (argo_object_get_sequence(write_object) == 0)) return 1;
    return 0;
}

void wp_dequeue_batch_object(struct wally_origin *origin, struct wp_conn_pg_specific *pgs, int q_index)
{
   struct wally_local_db_write_queue_object *write_object[WALLY_MAX_WRITE_BATCH_SIZE] = {NULL};
   int row_count = 0;

    row_count = wally_origin_dequeue_write_objects_specific(origin, q_index ,write_object);
    for(int i = 0 ; i < row_count ; i++) {
        pgs->write_objects[pgs->write_objects_count] = write_object[i];
        pgs->write_objects_count++;
    }
}

int wp_remove_duplicate_write_object(struct wp_db *wp_db, struct wp_connection *wp_conn, int wq_indx)
{
    int result = WALLY_RESULT_NO_ERROR;
    struct argo_structure_description *argo_desc = NULL;
    if(wp_db == NULL || wp_conn == NULL) {
       WALLY_LOG(AL_ERROR,"Conn is not available");
       return result;
    }
    struct wally_local_db_write_queue_object *write_object[WALLY_MAX_WRITE_BATCH_SIZE] = {NULL};
    struct wp_conn_pg_specific* pgs = wp_conn->cookie;
    struct wally_origin *origin = wp_db->db_to_wally_cookie;

    if(pgs == NULL || origin == NULL) {
       WALLY_LOG(AL_ERROR,"conn is not valid");
       return result;
    }
    if(pgs->write_objects_count <= 0 || !pgs->write_objects[0]) {
         WALLY_LOG(AL_ERROR,"No object to write");
         return result;
    }

	if (pgs->write_objects[0]->db_op == db_oper_rowargo_object) {

		argo_read_lock();
		argo_desc = argo_global.all_descriptions[pgs->write_objects[0]->data.row_to_write->base_structure_index];
		int local_description_count = argo_desc->static_description_count + pgs->write_objects[0]->data.row_to_write->excess_description_count;
		int key_index = -1;
		int key_count = 0;

		for (int i = 0; i < local_description_count; i++) {
			struct argo_field_description *pd = &(argo_desc->description[i]->public_description);
			if (pd->is_key) {
				key_index = i;
				key_count ++;
			}
		}
		if(key_index == -1 || key_count != 1 ) {
		   WALLY_LOG(AL_ERROR,"Key requirement is not match");
		   argo_unlock();
		   return WALLY_RESULT_BAD_ARGUMENT;
		}

		struct argo_field_description *pd = &(argo_desc->description[key_index]->public_description);
		if(pd->argo_field_type != argo_field_data_type_integer && pd->argo_field_type != argo_field_data_type_string ) {
		   WALLY_LOG(AL_ERROR,"Key is neither integer nor string");
		   argo_unlock();
		   return WALLY_RESULT_BAD_ARGUMENT;
		}

		struct zhash_table *p_keys = zhash_table_alloc(&wally_allocator);
		int n_count = 0;
		if (pd->argo_field_type == argo_field_data_type_integer) {
			for (int count = pgs->write_objects_count-1; count >= 0 ; count--) {
				int64_t int_a;
				argo_object_read_int_by_column_index(pgs->write_objects[count]->data.row_to_write, key_index, &int_a);
				if (!zhash_table_lookup(p_keys, &int_a, sizeof(int_a), NULL)) {
					write_object[n_count] = pgs->write_objects[count];
					zhash_table_store(p_keys, &int_a, sizeof(int_a), 0, &int_a);
					n_count ++;
				} else {
					argo_object_release(pgs->write_objects[count]->data.row_to_write);
					WALLY_DEBUG_POSTGRES("Object release index %d key is %"PRId64"",count ,int_a);
				}
			}
		} else if(pd->argo_field_type == argo_field_data_type_string) {
			for (int count = pgs->write_objects_count-1; count >= 0 ; count--) {
				char *index_str=NULL;
				argo_object_read_string_by_column_index(pgs->write_objects[count]->data.row_to_write, key_index, &index_str);
				if (!zhash_table_lookup(p_keys, index_str, strlen(index_str), NULL)) {
					write_object[n_count] = pgs->write_objects[count];
					zhash_table_store(p_keys, index_str, strlen(index_str), 0, index_str);
					n_count ++;
				} else {
					WALLY_DEBUG_POSTGRES("Object release index %d key is %s",count ,index_str);
					argo_object_release(pgs->write_objects[count]->data.row_to_write);
				}
			}
		} else {
		   zhash_table_free(p_keys);
		   argo_unlock();
		   return WALLY_RESULT_BAD_ARGUMENT;
		}
		zhash_table_free(p_keys);
		WALLY_DEBUG_POSTGRES("Total duplicate object found %d",(pgs->write_objects_count - n_count));
		/* coverity[lock_evasion:FALSE] */
		pgs->write_objects_count = 0;
		while(n_count > 0) {
			pgs->write_objects[pgs->write_objects_count] = write_object[n_count-1];
			/* coverity[lock_evasion:FALSE] */
			pgs->write_objects_count++;
			n_count--;
		}
		argo_unlock();
	}
    if (pgs->write_objects_count) {
         result = wp_write_object_upsert(wp_conn,wq_indx);
         if (result == WALLY_RESULT_WOULD_BLOCK) {
             /* Couldn't queue this object. Requeue it back on wally for the moment. (Be sure to enqueue to head) */
             WALLY_DEBUG_POSTGRES_WRITE("%s: Write would block ix = %d - back-queueing object.", wp_conn->wp_db->name, wq_indx);
             for (int i = pgs->write_objects_count-1; i >= 0; i--) {
                 wally_origin_enqueue_write_object(wp_db->db_to_wally_cookie, wq_indx, pgs->write_objects[i], 1);
                 /* argo_object_release is required for earlier wally_origin_dequeue_write_row_specific */
				 wp_release_write_object(pgs->write_objects[i], 1);
             }
             WALLY_DEBUG_POSTGRES_WRITE("%s: Write would block ix = %d - enqueueing object back", wp_conn->wp_db->name, wq_indx);
             pgs->write_objects_count = 0;
             result = WALLY_RESULT_NO_ERROR;
         } else if (result == WALLY_RESULT_NO_ERROR) {
             for (int i = 0; i < pgs->write_objects_count; i++) {
				 wp_release_write_object(pgs->write_objects[i], 0);
             }
             WALLY_DEBUG_POSTGRES_WRITE("%s: Write successful, sync ix = %d", wp_conn->wp_db->name, wq_indx);
         } else if (result == WALLY_RESULT_ASYNCHRONOUS) {
             WALLY_DEBUG_POSTGRES_WRITE("%s: Write successful, async ix = %d", wp_conn->wp_db->name, wq_indx);
         } else {
             /* XXX This is bad. Log. And this case will likely result in losing our DB connection. */
             origin->wq_info[wq_indx].total_failed_batch ++;
             WALLY_LOG(AL_ERROR, "Bad: %s (Table %s/%s not initialized? Tossing %d rows.)",
                      wally_error_strings[result], wp_conn->wp_db->name, argo_object_get_type(pgs->write_objects[0]->data.row_to_write),pgs->write_objects_count);
             for (int i = pgs->write_objects_count-1; i >= 0; i--) {
                 wally_origin_enqueue_write_object(wp_db->db_to_wally_cookie, wq_indx, pgs->write_objects[i], 1);
				 wp_release_write_object(pgs->write_objects[i], 1);
             }
             result = WALLY_RESULT_NO_ERROR;
        }
     }
    return result;
}

int wp_write_next_batch_on_conn(struct wp_db *wp_db, struct wp_connection *wp_conn, int wq_indx )
{
    int result = WALLY_RESULT_NO_ERROR;

    if(wp_db == NULL || wp_conn == NULL) {
        WALLY_LOG(AL_ERROR,"Conn is not available");
        return result;
    }

    struct wally_origin *origin = wp_db->db_to_wally_cookie;
    struct wp_conn_pg_specific *pgs = wp_conn->cookie;

    if(origin == NULL || pgs == NULL) {
        WALLY_LOG(AL_ERROR,"conn is not valid");
        return result;
    }

    pgs->write_objects_count = 0;
    wp_dequeue_batch_object(wp_db->db_to_wally_cookie, pgs , wq_indx);
    if (pgs->write_objects_count) {
         result = wp_write_object_upsert(wp_conn,wq_indx);
         if (result == WALLY_RESULT_WOULD_BLOCK) {
             /* Couldn't queue this object. Requeue it back on wally for the moment. (Be sure to enqueue to head) */
             WALLY_DEBUG_POSTGRES_WRITE("%s: Write would block ix = %d - back-queueing object.", wp_conn->wp_db->name, wq_indx);
             for (int i = pgs->write_objects_count-1; i >= 0; i--) {
                 wally_origin_enqueue_write_object(wp_db->db_to_wally_cookie, wq_indx, pgs->write_objects[i], 1);
                 /* argo_object_release is required for earlier wally_origin_dequeue_write_row_specific */
				 wp_release_write_object(pgs->write_objects[i], 1);
             }
             pgs->write_objects_count= 0;
             result = WALLY_RESULT_NO_ERROR;
         } else if (result == WALLY_RESULT_NO_ERROR) {
             WALLY_DEBUG_POSTGRES_WRITE("%s: Write successful, sync ix = %d", wp_conn->wp_db->name, wq_indx);
             for (int i = 0; i < pgs->write_objects_count; i++) {
				wp_release_write_object(pgs->write_objects[i], 0);
                pgs->write_objects[i] = NULL;
             }
         } else if (result == WALLY_RESULT_ASYNCHRONOUS) {
             WALLY_DEBUG_POSTGRES_WRITE("%s: Write successful, async ix = %d", wp_conn->wp_db->name, wq_indx);
         } else {
             /* XXX This is bad. Log. And this case will likely result in losing our DB connection. */
            if(pgs->write_objects[0]->db_op == db_oper_rowargo_object) {
                WALLY_LOG(AL_ERROR, "Bad: %s (Table %s/%s not initialized? .)",
                       wally_error_strings[result], wp_conn->wp_db->name,
						 argo_object_get_type(pgs->write_objects[0]->data.row_to_write));
            } else if (pgs->write_objects[0]->db_op == db_oper_lookuptable_update) {
                 WALLY_LOG(AL_ERROR, "Bad: %s (Table %s/%s not initialized? .)",
                       wally_error_strings[result], wp_conn->wp_db->name,
						 pgs->write_objects[0]->data.lookup_table_write.table_name);
            } else if (pgs->write_objects[0]->db_op == db_oper_recovery_update) {
                WALLY_LOG(AL_ERROR, "Bad: Recovery update is not supported in batch write ");
            }
             for (int i = 0; i < pgs->write_objects_count; i++) {
                 wally_origin_enqueue_write_object(wp_db->db_to_wally_cookie, wq_indx, pgs->write_objects[i], 1);
				 wp_release_write_object(pgs->write_objects[i], 1);
             }
             origin->wq_info[wq_indx].total_failed_batch ++;
             result = WALLY_RESULT_NO_ERROR;
        }
   }
   return result;
}

/* Function: wally_postgres_update_data_waiting_stats
 * Arg     : table - wp_table
 *         : duration_us - duration us
 * Return  : None
 * Description : Data waiting stats update in buckets */
void wally_postgres_update_data_waiting_stats(struct wp_table *table, int64_t duration_us)
{
    int64_t duration_s = duration_us/WALLY_DURATION_US_IN_S;
    int index = 0;
    if (table == NULL)
    {
        return;
    }
    for(index = (MAX_DATA_WAITING_BUCKET_SIZE-1); index > 0; index --)
    {
        if (duration_s > data_waiting_bucket_max[index])
        {
            break;
        }
    }

    table->data_waiting_time_buckets[index]++;
}

/* Function: wally_postgres_reset_data_waiting_stats
 * Arg     : table - wp_table
 * Return  : None
 * Description : memset data waiting time stats buckets */
void wally_postgres_reset_data_waiting_stats(struct wp_table *table)
{
    if (table)
    {
        memset(table->data_waiting_time_buckets, 0, sizeof(table->data_waiting_time_buckets));
    }
}

/* Function: fill_wally_postgres_table_stats
 * Arg     : cookie - wp_table
 *           counter - stats counterx
 *           structure_data - argo stats
 * Return  : None
 * Description : Timer function called every 60s to update postgres stats */
int fill_wally_postgres_table_stats(void *cookie, int counter, void *structure_data)
{
    struct wp_table *table = cookie;
    struct wally_postgres_table_stats *stats = structure_data;
    if ( !table || !stats)
    {
        return WALLY_RESULT_ERR;
    }

    /* if there is a pending try-lock data to read, count it in buckets before publishing */
    if (table->xpoll_first_detected_data_to_read_us != 0)
    {
        wally_postgres_update_data_waiting_stats(table, (epoch_us() - table->xpoll_first_detected_data_to_read_us));
    }
    stats->db_new_data_waiting_time_10s = table->data_waiting_time_buckets[WALLY_DATA_WAITING_BUCKET_10S_INDEX];
    stats->db_new_data_waiting_time_30s = table->data_waiting_time_buckets[WALLY_DATA_WAITING_BUCKET_30S_INDEX];
    stats->db_new_data_waiting_time_60s = table->data_waiting_time_buckets[WALLY_DATA_WAITING_BUCKET_60S_INDEX];
    stats->db_new_data_waiting_time_120s = table->data_waiting_time_buckets[WALLY_DATA_WAITING_BUCKET_120S_INDEX];
    stats->db_new_data_waiting_time_more_than_120s = table->data_waiting_time_buckets[WALLY_DATA_WAITING_BUCKET_MORE_THAN_120S_INDEX];

    /* Poll Interval, Poll Rate, New Data detect counter, try lock counter, try lock failure counter */
    stats->db_polling_interval = (table->xpoll_poll_interval_us ? table->xpoll_poll_interval_us : table->db->polling_interval_us);
    stats->db_new_data_poll_rate = table->xpoll_check_rate;
    stats->db_new_data_detect_counter = table->xpoll_check_new_data;
    stats->db_try_lock_counter = table->xpoll_trylock;
    stats->db_try_lock_failure_counter = table->xpoll_trylock_fails;

    /* GVR stats */
    stats->db_skip_poll_excl_lock = table->db_poll_skip_excl_lock;
    stats->db_gvr_poll_time = table->db_table_gvr_poll_time;
    stats->db_poll_table_total_time = table->db_poll_table_total_time;
    if (table->db_table_pending_txn_waiting_time && table->db_table_pending_txn_chk_count)
    {
        stats->db_poll_avg_txn_wait_time = (table->db_table_pending_txn_waiting_time /
                                            table->db_table_pending_txn_chk_count);
    } else {
        stats->db_poll_avg_txn_wait_time = 0;
    }
    stats->db_poll_avg_max_wait_time = table->db_table_max_waiting_time;
    stats->time_since_last_successful_read = table->last_successful_read_us?(epoch_us() - table->last_successful_read_us):0;
    stats->db_poll_table_parse_time = table->db_poll_table_parse_time;
    stats->db_poll_table_read_time = table->db_poll_table_read_time;
    stats->db_poll_table_processing_time = table->db_poll_table_processing_time;
    stats->db_poll_num_gap_txns = table->db_poll_num_gap_txns;
    stats->db_poll_num_non_gap_txns = table->db_poll_num_non_gap_txns;

    /* Reset rate stats */
    table->xpoll_check_rate = 0;
    table->xpoll_trylock = 0;
    table->xpoll_trylock_fails = 0;
    wally_postgres_reset_data_waiting_stats(table);
    table->db_poll_skip_excl_lock = 0;
    table->db_table_gvr_poll_time = 0;
    table->db_poll_table_total_time = 0;
    table->db_table_pending_txn_waiting_time = 0;
    table->db_table_pending_txn_chk_count = 0;
    table->db_table_max_waiting_time = 0;
    table->db_poll_table_read_time = 0;
    table->db_poll_table_parse_time = 0;
    table->db_poll_table_processing_time = 0;
    table->db_poll_num_gap_txns = 0;
    table->db_poll_num_non_gap_txns = 0;
    return WALLY_RESULT_NO_ERROR;
}

/* Free the DB connection. Call this function to
 * 1. Reset the DB connection
 * 2. Free the DB connection memory and
 * 2. Clear the EV base */
static void wp_free_db_conn (struct wp_connection *wp_conn, struct wp_db *wp_db, bool free_db)
{
    if (!wp_conn || !wp_db) {
        return;
    }

    /* Mark the connection as failed and clear the event base. */
    wp_conn->state = conn_failed;
    if (wp_conn->ev_conn) {
        event_del(wp_conn->ev_conn);
        event_free(wp_conn->ev_conn);
        wp_conn->ev_conn = NULL;
    }
    if (free_db) {
        PQfinish((PGconn *)(wp_conn->db_conn));
        wp_conn->db_conn = NULL;
    }
    wp_db->failed_connections_count++;
    /* Connection failure event would come for free connections too. For those
     * connections table will be NULL */
    if (wp_conn->table) {
        wp_conn->table->db_state = wp_db_table_poll_idle;
    }
    WALLY_LOG(AL_NOTICE, "DB connection failed count %d db name %s",
            wp_db->failed_connections_count, wp_db->name);
}


/* Routine to check and report the DB failure.
 * If the DB-Conn is in failed state, it removes the node
 * from the TAILQ and report the failure to FSM
 * */
void wally_db_error_handler(struct wp_connection *wp_conn)
{
    struct wp_db *wp_db = NULL;
    struct wp_connection *wp_conn_temp = NULL;
    int i = 0;

    if (wp_conn == NULL){
        return;
    }

    wp_db = wp_conn->wp_db;

    /* Check the connection status. If status is CONNECTION_OK, which means
     * RDS is accessible. So this failure is TRANSACTION FAILURE */
    ConnStatusType dbConnStatus = PQstatus((PGconn *)(wp_conn->db_conn));
    if (dbConnStatus == CONNECTION_OK){
        /* Transaction failed, restart the synchronization */
        /* If we are here, then wp_conn->table should be available, as it
         * should be a transaction failure, but still NULL check is added */
        if (wp_conn->table) {
            wp_conn->table->db_state = wp_db_table_poll_idle;
            wp_conn->table->needs_to_be_resynchronized = 1;
            wp_conn->table->state = table_ready;
            wp_send_poll_rollback (wp_conn);
        }
        wally_oper_event_post(E_TRANSACTION_FAIL, SE_NONE, wp_db);
        return;
    }

    /* wp_conn is failed, so check is this entry still in free_connections
     * queue. If it is available in the queue remove it.
     * free_connections queue should keep only the working connections */
    wp_conn_temp = TAILQ_FIRST(&(wp_db->free_connections));
    while (wp_conn_temp) {
        i++;
        TAILQ_REMOVE(&(wp_db->free_connections), wp_conn_temp, free_connections);
        /* Node found, break the loop */
        if (wp_conn->my_index == wp_conn_temp->my_index) {
            WALLY_LOG(AL_ERROR, "Failed DB connection is found in Queue, dequeued it");
            break;
        }

        /* Node not found, so insert back the node */
        wally_db_insert_free_conn (wp_db, wp_conn_temp);

        /* This logic re-insert the working node back to tail queue, so we cannot
         * detect the last node using NULL check. Used the number of connections_count
         * to break the loop */
        if (i >= wp_db->connections_count){
            WALLY_LOG(AL_ERROR, "Failed DB connection is not found in Queue");
            break;
        }
        wp_conn_temp = TAILQ_FIRST(&(wp_db->free_connections));
    }

    wp_free_db_conn(wp_conn, wp_db, false);

    /* Local DB can be found using
     * 1. is_true_origin should be false for all local database, except "local_db"
     * 2. For local_db database in local postgres, true_origin and is_alterable both are true */
    if (wp_db->is_true_origin == false || (wp_db->is_true_origin == true &&
                wp_db->is_alterable == true )) {
        wally_oper_event_post(E_DB_FAIL, SE_DB_LOCAL, wp_db);
        return;
    }
    wally_oper_event_post(E_DB_FAIL, SE_DB_REMOTE, wp_db);
}

/* Update table error stats */
void wally_table_oper_update_stats (enum wally_oper_events event, enum wally_oper_sub_events sub_event,
        struct wp_connection *wp_conn)
{
    struct wp_table *table = NULL;

    if (wp_conn == NULL || wp_conn->table == NULL) {
        return;
    }

    table = wp_conn->table;

    if (event == E_SCHEMA_MISMATCH) {
        table->db_table_oper_stats.schema_mismatch_count++;
    }
    if (event == E_TRANSACTION_FAIL) {
        table->db_table_oper_stats.transaction_failure_count++;
    }
    if (event == E_LDP_WRITE_FAIL) {
        table->db_table_oper_stats.object_wrtie_failure_count++;
    }
}

/*
 * Routine to handle the critical postgress error.
 * Based on the input, this function initiated the watchdog shutdown trigger
 *
 * This function retrieves and prints specific error fields using PQresultErrorField:
 *    - PG_DIAG_SEVERITY for the severity level of the error.
 *    - PG_DIAG_SQLSTATE for the SQLSTATE error code.
 *    - PG_DIAG_MESSAGE_PRIMARY for the primary error message.
 *    - PG_DIAG_MESSAGE_DETAIL for additional detail about the error, if available.
 *    - PG_DIAG_MESSAGE_HINT for suggestions on how to resolve the error, if available.
 *
 *  cookie: PGresult error code. It is void pointer, as this function would be called from
 *          other files too.
 *  trigger_wd: true - to trigger watchdog shutdown, false to report only the error
*/
void wally_error_handler(void *cookie, struct wp_connection *wp_conn, bool trigger_wd,
        enum wally_oper_events event, enum wally_oper_sub_events sub_event)
{
    PGresult *pres = (PGresult *)cookie;
    struct zthread_info *zthread = zthread_self();
    struct wp_db *wp_db = NULL;

    if (zthread) {
        WALLY_LOG(AL_ERROR, "Critical error. In wally error handler. Thread %d (%s)",
                zthread->stack.thread_num, zthread->stack.thread_name);
    }

    if (pres != NULL) {
        /* Log the error as critical only if WD trigger is required */
        if (trigger_wd) {
            WALLY_LOG(AL_CRITICAL, " PG operation failed status = %s, Error:  %s",
                    PQresStatus(PQresultStatus(pres)),
                    PQresultErrorMessage(pres));
        } else {
            WALLY_LOG(AL_ERROR, " PG operation failed status = %s, Error:  %s",
                    PQresStatus(PQresultStatus(pres)),
                    PQresultErrorMessage(pres));
        }

        char *severity = PQresultErrorField(pres, PG_DIAG_SEVERITY);
        char *sqlstate = PQresultErrorField(pres, PG_DIAG_SQLSTATE);
        char *message_primary = PQresultErrorField(pres, PG_DIAG_MESSAGE_PRIMARY);
        char *detail = PQresultErrorField(pres, PG_DIAG_MESSAGE_DETAIL);
        char *hint = PQresultErrorField(pres, PG_DIAG_MESSAGE_HINT);
        char *table_name = PQresultErrorField(pres, PG_DIAG_TABLE_NAME);

        if (severity) {
            WALLY_LOG(AL_DEBUG, "Severity: %s", severity);
        }

        if (sqlstate) {
            WALLY_LOG(AL_DEBUG, "SQLSTATE: %s", sqlstate);
        }

        if (message_primary) {
            WALLY_LOG(AL_DEBUG, "Message: %s", message_primary);
        }

        if (detail) {
            WALLY_LOG(AL_DEBUG, "Detail: %s", detail);
        }

        if (hint) {
            WALLY_LOG(AL_DEBUG, "Hint: %s", hint);
        }

        if (table_name) {
            WALLY_LOG(AL_DEBUG, "Table Name: %s", table_name);
        }

        PQclear(pres);
        pres = NULL;
        while ((pres = PQgetResult((PGconn *)(wp_conn->db_conn)))) {
            PQclear(pres);
            pres = NULL;
        }
    }

    if (wp_conn) {
        wp_db = wp_conn->wp_db;
        /* Connection failure event would come for free connections too. For those
         * connections table will be NULL */
        if (wp_conn->table) {
            WALLY_LOG(AL_INFO, "DB-Conn table %s db_state %d state %s",
                    wp_conn->table->db_table_name,
                    wp_conn->table->db_state,
                    wp_table_state_names[wp_conn->table->state]);
        }
        WALLY_LOG(AL_INFO, "DB Name: %s DB-Conn Name: %s",
                wp_db->name, wp_conn->name);
        wally_table_oper_update_stats(event, sub_event, wp_conn);
    }

    if (trigger_wd) {
        if (wp_db == NULL) {
            wally_oper_event_post(E_TERMINATE, SE_NONE, wp_db);
            return;
        }
        if (event == E_MAX) {
            /* No event received, detect the failure and post
             * the event */
            wally_db_error_handler(wp_conn);
        } else {
            wally_oper_event_post(event, sub_event, wp_db);
            /* Connection failure event would come for free connections too. For those
             * connections table will be NULL */
            if (wp_conn->table) {
                wp_conn->table->db_state = wp_db_table_poll_idle;
            }
        }
    }
}



PGresult *xPQexec(PGconn *conn, const char *query)
{
    WALLY_DEBUG_POSTGRES_SQL("%s", query);
    return PQexec(conn, query);
}

static int xPQsendQueryParams(PGconn *conn,
							  const char *command,
							  int nParams,
							  const Oid *paramTypes,
							  const char *const *paramValues,
							  const int *paramLengths,
							  const int *paramFormats,
							  int resultFormat)
{
    WALLY_DEBUG_POSTGRES_SQL("%s", command);
    return PQsendQueryParams(conn, command, nParams, paramTypes, paramValues, paramLengths, paramFormats, resultFormat);
}


/*
 * The standard callins from wally into this origin DB.
 */
int wally_postgres_register_for_table(void *callout_cookie,
                                      int64_t sequence,
                                      const char *table_name,
                                      const char *column_name,
                                      const char *key)
{
    return WALLY_RESULT_NO_ERROR;
}
int wally_postgres_deregister_for_table(void *callout_cookie,
                                        const char *table_name,
                                        const char *column_name)
{
    return WALLY_RESULT_NO_ERROR;
}

int wp_send_create_db(PGconn *conn, const char *db_name)
{
    int result;

    char tmp_str[200];
    if (wally_check_template_database_exists(conn)) {
        snprintf(tmp_str, sizeof(tmp_str), "CREATE DATABASE %s TEMPLATE %s ", db_name, ITASCA_TEMPLATE_DB);
    } else {
        snprintf(tmp_str, sizeof(tmp_str), "CREATE DATABASE %s ", db_name);
    }

    result = xPQsendQueryParams(conn,
                               tmp_str,
                               0,
                               NULL,
                               NULL,
                               NULL,
                               NULL,
                               0);
    return result;
}

int wp_send_get_table(PGconn *conn, char *table_name, char *schema_name)
{
    int result;

    if (schema_name) {
        char *params[2];
        params[0] = table_name;
        params[1] = schema_name;
        result = xPQsendQueryParams(conn,
                                   "SELECT table_name FROM information_schema.tables WHERE table_name = $1 AND table_schema = $2",
                                   2,
                                   NULL,
                                   (const char * const *)params,
                                   NULL,
                                   NULL,
                                   0);
    } else {
        char *params[1];
        params[0] = table_name;
        result = xPQsendQueryParams(conn,
                                   "SELECT table_name FROM information_schema.tables WHERE table_name = $1",
                                   1,
                                   NULL,
                                   (const char * const *)params,
                                   NULL,
                                   NULL,
                                   0);
    }

    return result;
}

int wp_send_create_table(PGconn *conn, char *table_name)
{
    char *params[1];
    char tmp_str[200];
    int result;

    if (snprintf(tmp_str, sizeof(tmp_str), "CREATE TABLE %s ()", table_name) < sizeof(tmp_str)) {
        result = xPQsendQueryParams(conn,
                                   tmp_str,
                                   0,
                                   NULL,
                                   NULL,
                                   NULL,
                                   NULL,
                                   0);
    } else {
        /* Yeah, this should never run. */

        params[0] = table_name;
        result = xPQsendQueryParams(conn,
                                   "CREATE TABLE $1 ()",
                                   1,
                                   NULL,
                                   (const char * const *)params,
                                   NULL,
                                   NULL,
                                   0);
    }
    return result;
}

int wp_send_create_lookup_table(PGconn *conn, char *table_name)
{
    int result;
    char str[DEFAULT_DB_STRING_SIZE];

	if (snprintf(str,
				 sizeof(str),
				 "CREATE TABLE %s%s (sequence bigint NOT NULL,"
				 "column_index smallint NOT NULL,"
				 "value VARCHAR NOT NULL,"
				  "UNIQUE (column_index, value))", table_name, WALLY_LOOKUP_TABLE_SUFFIX) >= sizeof(str)) {
		return 0;
	}
	result = xPQsendQueryParams(conn,
							   str,
							   0,
							   NULL,
							   NULL,
							   NULL,
							   NULL,
							   0);

	WALLY_DEBUG_POSTGRES("xPQsendQueryParams create lookup table query=%s, %s",
			str, PQerrorMessage((PGconn *)(conn)));

    return result;
}

int wp_send_read_lookup_table(PGconn *conn, char *table_name, char *schema_name)
{
    int result;
    char str[DEFAULT_DB_STRING_SIZE];

    if (schema_name) {
		if (snprintf(str,
					 sizeof(str),
					 "SELECT table_name FROM information_schema.tables WHERE table_name = '%s%s' AND table_schema = '%s'",
					 table_name, WALLY_LOOKUP_TABLE_SUFFIX, schema_name) >= sizeof(str)) {
			return 0;
		}
        result = xPQsendQueryParams(conn,
									str,
                                   0,
                                   NULL,
                                   NULL,
                                   NULL,
                                   NULL,
                                   0);
    } else {
		if (snprintf(str,
					 sizeof(str),
					 "SELECT table_name FROM information_schema.tables WHERE table_name = '%s%s'",
					 table_name, WALLY_LOOKUP_TABLE_SUFFIX) >= sizeof(str)) {
			return 0;
		}
        result = xPQsendQueryParams(conn,
									str,
                                   0,
                                   NULL,
                                   NULL,
                                   NULL,
                                   NULL,
                                   0);
    }
	WALLY_DEBUG_POSTGRES("xPQsendQueryParams read lookup table query=%s, %s",
			str, PQerrorMessage((PGconn *)(conn)));

    return result;
}

int wp_send_get_table_columns(PGconn *conn, char *table_name, char *schema_name)
{
    int result;

    if (schema_name) {
        char *params[2];
        params[0] = table_name;
        params[1] = schema_name;
        result = xPQsendQueryParams(conn,
                                   "SELECT column_name, data_type, udt_name "
                                   "FROM information_schema.columns "
                                   "WHERE table_name = $1 AND table_schema = $2",
                                   2,
                                   NULL,
                                   (const char * const *)params,
                                   NULL,
                                   NULL,
                                   0);
    } else {
        char *params[1];
        params[0] = table_name;
        result = xPQsendQueryParams(conn,
                                   "SELECT column_name, data_type, udt_name "
                                   "FROM information_schema.columns "
                                   "WHERE table_name = $1",
                                   1,
                                   NULL,
                                   (const char * const *)params,
                                   NULL,
                                   NULL,
                                   0);
    }

    return result;
}

int wp_send_read_indexes(PGconn *conn, char *table_name)
{
    char *params[1];
    int result;

    /*
     * Pretty complex query here- returns indexes for a table by name
     * of index. If the index involves multiple keys, it returns
     * multiple rows for that index, in order of precedence.
     *
     * Returns column 1: Index Name
     *         column 2: Column Name
     */
    params[0] = table_name;
    result = xPQsendQueryParams(conn,
                               /* Source: http://www.alberton.info/postgresql_meta_info.html#.USv8X-s4VqY */
                               "SELECT a.index_name, b.attname "
                               "FROM ( "
                               "SELECT a.indrelid, "
                               "c.relname index_name, "
                               "unnest(a.indkey) index_num "
                               "FROM pg_index a, "
                               "pg_class b, "
                               "pg_class c "
                               "WHERE b.relname=$1 "
                               "AND b.oid=a.indrelid "
                               "AND a.indexrelid=c.oid "
                               ") a, "
                               "pg_attribute b "
                               "WHERE a.indrelid = b.attrelid "
                               "AND a.index_num = b.attnum "
                               "ORDER BY a.index_name, a.index_num",
                               1,
                               NULL,
                               (const char * const *)params,
                               NULL,
                               NULL,
                               0);
    return result;
}

int wp_send_read_sequence_begin(PGconn *conn)
{
    int result;
    char str[DEFAULT_DB_STRING_SIZE];

    if (snprintf(str,
                 sizeof(str),
                 "BEGIN") >= sizeof(str)) {
        return 0;
    }
    result = xPQsendQueryParams(conn,
                               str,
                               0,
                               NULL,
                               NULL,
                               NULL,
                               NULL,
                               0);
    if (result == 0) {
        WALLY_LOG(AL_CRITICAL, "Failed sending query: %s, %s", str, PQerrorMessage(conn));
    } else {
        //WALLY_LOG(AL_NOTICE, "Sent query: %s", str);
    }

    return result;
}
int wp_send_read_sequence_lock(PGconn *conn, char *table_name)
{
    int result;
    char str[DEFAULT_DB_STRING_SIZE];

    if (snprintf(str,
                 sizeof(str),
                 "LOCK TABLE %s", table_name) >= sizeof(str)) {
        return 0;
    }
    result = xPQsendQueryParams(conn,
                               str,
                               0,
                               NULL,
                               NULL,
                               NULL,
                               NULL,
                               0);
    if (result == 0) {
        WALLY_LOG(AL_CRITICAL, "Failed sending query: %s, %s", str, PQerrorMessage(conn));
    } else {
        //WALLY_LOG(AL_NOTICE, "Sent query: %s", str);
        WALLY_DEBUG_POSTGRES("Read sequence %s: %s", table_name, str);
    }

    return result;
}

int wp_send_read_sequence_select(PGconn *conn, char *table_name, const char *sequence_column_name)
{
    int result;
    char str[DEFAULT_DB_STRING_SIZE];

    if (snprintf(str,
                 sizeof(str),
                 "SELECT %s "
                 "FROM %s "
                 "ORDER BY %s DESC "
                 "LIMIT 1",
                 sequence_column_name,
                 table_name,
                 sequence_column_name) >= sizeof(str)) {
        return 0;
    }
    result = xPQsendQueryParams(conn,
                               str,
                               0,
                               NULL,
                               NULL,
                               NULL,
                               NULL,
                               0);
    if (result == 0) {
        WALLY_LOG(AL_CRITICAL, "Failed sending query: %s, %s", str, PQerrorMessage(conn));
    } else {
        //WALLY_LOG(AL_NOTICE, "Sent query: %s", str);
    }

    return result;
}




int wp_send_read_max_sequence_gvr(PGconn *conn, char *table_name, const char *sequence_column_name,int64_t current_max_sequence)
{
    int result;
    char str[DEFAULT_DB_STRING_SIZE];

    /*  lock check arg is sent as 0 to indicate reading highest sequence number */
    if(snprintf(str,
                sizeof(str),
                "select  max_sequence, pending_lock from retrieve_max_sequence_v1('%s', '%s',%ld, 0);",
                table_name,sequence_column_name, (long) current_max_sequence) >= sizeof(str)) {
        return 0;
    }

    result = xPQsendQueryParams(conn,
            str,
            0,
            NULL,
            NULL,
            NULL,
            NULL,
            0);
    if (result == 0) {
        WALLY_LOG(AL_CRITICAL, "Failed sending query: %s, %s", str, PQerrorMessage(conn));
    }

    return result;
}



int wp_send_select_last_transaction_gvr_status(PGconn *conn, char *table_name, const char *sequence_column_name)
{
    int result;
    char str[DEFAULT_DB_STRING_SIZE];

    /*  lock_check arg is sent with 1 to indicate this call is to check lock exists or not */
    if(snprintf(str,
             sizeof(str),
             "select max_sequence, pending_lock from retrieve_max_sequence_v1('%s', '%s', 0, 1);", table_name, sequence_column_name)
                        >= sizeof(str)) {
         return 0;
     }
    result = xPQsendQueryParams(conn,
                               str,
                               0,
                               NULL,
                               NULL,
                               NULL,
                               NULL,
                               0);
    if (result == 0) {
        WALLY_LOG(AL_CRITICAL, "Failed sending query: %s, %s", str, PQerrorMessage(conn));
    }
    return result;
}

int wp_send_read_sequence_commit(PGconn *conn)
{
    int result;
    char str[DEFAULT_DB_STRING_SIZE];

    if (snprintf(str,
                 sizeof(str),
                 "COMMIT") >= sizeof(str)) {
        return 0;
    }
    result = xPQsendQueryParams(conn,
                               str,
                               0,
                               NULL,
                               NULL,
                               NULL,
                               NULL,
                               0);
    if (result == 0) {
        WALLY_LOG(AL_CRITICAL, "Failed sending query: %s, %s", str, PQerrorMessage(conn));
    } else {
        //WALLY_LOG(AL_NOTICE, "Sent query: %s", str);
    }

    return result;
}

int wp_send_add_notification(PGconn *conn, char *table_name)
{
    int result;
    char str[DEFAULT_DB_STRING_SIZE];

    if (snprintf(str,
                 sizeof(str),
                 "LISTEN %s_notify",
                 table_name) >= sizeof(str)) {
        return 0;
    }
    result = xPQsendQueryParams(conn,
                               str,
                               0,
                               NULL,
                               NULL,
                               NULL,
                               NULL,
                               0);
    return result;
}

/*
 * Get the number of rows we could conceivably read by issuing a
 * locked read request. This 'test' is used to avoid unnecessary DB
 * locking while polling.
 */
int wp_send_gvr_poll_check(struct wp_connection *wp_conn)
{
    int result = 0;
    struct wp_table *t = wp_conn->table;

    wp_conn->row_request_sequence = t->current_max_sequence;
    wp_conn->state = conn_table_gvr_poll_check;
    t->db_state = wp_db_table_gvr_poll_check;

    t->xpoll_last_unlocked_read_us = epoch_us();
    t->xpoll_check++;
    t->xpoll_check_rate++;
    WP_STATS_CONN_START(wp_conn);
    result = wp_send_read_max_sequence_gvr((PGconn *)(wp_conn->db_conn),
                            t->db_table_name, t->sequence_column_name, t->current_max_sequence);

    return result;
}


/*
 * Send pending transaction request to RDS.
 */
int wp_send_gvr_pending_transaction_check(struct wp_connection *wp_conn)
{
    int result;
    struct wp_table *t = wp_conn->table;

    wp_conn->state = conn_table_gvr_pending_txn_check;
    t->db_state = wp_db_table_gvr_pending_txn_check;

    WP_STATS_CONN_START(wp_conn);
    result = wp_send_select_last_transaction_gvr_status((PGconn *)(wp_conn->db_conn), t->db_table_name, t->sequence_column_name);
    WALLY_DEBUG_POSTGRES_POLL("Polling table %s", t->db_table_name);
    return result;
}


/*
 * Get the number of rows we could conceivably read by issuing a
 * locked read request. This 'test' is used to avoid unnecessary DB
 * locking while polling.
 */
int wp_send_poll_check(struct wp_connection *wp_conn)
{
    int result;
    struct wp_table *t = wp_conn->table;
    char str[DEFAULT_DB_STRING_SIZE];

    wp_conn->row_request_sequence = t->current_max_sequence;
    wp_conn->state = conn_table_poll_check;
    t->db_state = wp_db_table_poll_check;

    t->xpoll_last_unlocked_read_us = epoch_us();
    /* Increment poll counter only on fresh attempt */
    if (!t->xpoll_repeated_trylock_fail_count)
    {
        t->xpoll_check++;
        t->xpoll_check_rate++;
    }
    snprintf(str,
             sizeof(str),
             "SELECT count(*) from %s where %s > %ld",
             t->db_table_name,
             t->sequence_column_name,
             (long)t->current_max_sequence);
    WP_STATS_CONN_START(wp_conn);
    result = xPQsendQueryParams((PGconn *)(wp_conn->db_conn),
                               str,
                               0,
                               NULL,
                               NULL,
                               NULL,
                               NULL,
                               0);
    WALLY_DEBUG_POSTGRES_POLL("Poll %s: %s", t->db_table_name, str);
    return result;
}

/* Issue sequence update row request. */
int wp_send_poll_begin(struct wp_connection *wp_conn)
{
    int result;
    struct wp_table *t = wp_conn->table;
    char str[DEFAULT_DB_STRING_SIZE];

    t->sequence_is_being_read = 1;
    wp_conn->table = t;
    wp_conn->state = conn_table_poll_begin;
    t->db_state = wp_db_table_poll_begin;
    snprintf(str,
             sizeof(str),
             "BEGIN");
    WP_STATS_CONN_START(wp_conn);
    result = xPQsendQueryParams((PGconn *)(wp_conn->db_conn),
                               str,
                               0,
                               NULL,
                               NULL,
                               NULL,
                               NULL,
                               0);
    WALLY_DEBUG_POSTGRES_POLL("Poll %s: %s", t->db_table_name, str);
    if (result == 0) {
        WALLY_LOG(AL_CRITICAL, "Failed sending query: BEGIN");
    }
    return result;
}

/* Issue sequence update row request. */
int wp_send_poll_lock(struct wp_connection *wp_conn)
{
    int result;
    struct wp_table *t = wp_conn->table;
    char str[DEFAULT_DB_STRING_SIZE];

    int64_t now_us = epoch_us();

    wp_conn->table = t;
    wp_conn->state = conn_table_poll_lock;
    t->db_state = wp_db_table_poll_lock;

    /* This log prints every 120s while waiting for lock */
    if ((t->xpoll_repeated_trylock_fail_count) && !(t->xpoll_repeated_trylock_fail_count %  WALLY_TRYLOCK_LOG_INTERVAL_COUNT))
    {
        WALLY_LOG(AL_CRITICAL, "Wally Try lock attempted  %"PRId64"s for %s:%s table",
           t->xpoll_repeated_trylock_fail_count, wp_conn->table->db->name,
                wp_conn->table->db_table_name);
    }
    snprintf(str,
                 sizeof(str),
                 "LOCK TABLE %s NOWAIT", wp_conn->table->db_table_name);
    t->xpoll_trylock++;
    t->xpoll_last_trylocked_read_us = now_us;
    WP_STATS_CONN_START(wp_conn);
    result = xPQsendQueryParams((PGconn *)(wp_conn->db_conn),
                               str,
                               0,
                               NULL,
                               NULL,
                               NULL,
                               NULL,
                               0);
    if (result == 0) {
        WALLY_LOG(AL_CRITICAL, "Failed sending query: %s, %s", str, PQerrorMessage((PGconn *)(wp_conn->db_conn)));
    }
    WALLY_DEBUG_POSTGRES_POLL("Poll %s: %s", t->db_table_name, str);
    return result;
}

/* Issue sequence update row request. */
int wp_send_gvr_poll_select(struct wp_connection *wp_conn)
{
    int result;
    struct wp_table *t = wp_conn->table;
    char str[DEFAULT_DB_STRING_SIZE];

    wp_conn->row_request_sequence = t->current_max_sequence;
    wp_conn->state = conn_table_gvr_poll_select;
    t->db_state = wp_db_table_gvr_poll_select;
    snprintf(str,
             sizeof(str),
             "SELECT * FROM %s WHERE %s > %ld AND %s <= %ld ORDER BY %s limit %d",
             t->db_table_name,
             t->sequence_column_name,
             (long)t->current_max_sequence,t->sequence_column_name, (long)t->poll_max_sequence,
             t->sequence_column_name, wally_gbl_cfg.wally_postgres_db_query_batch_size);
    WP_STATS_CONN_START(wp_conn);
    result = xPQsendQueryParams((PGconn *)(wp_conn->db_conn),
                               str,
                               0,
                               NULL,
                               NULL,
                               NULL,
                               NULL,
                               0);
    WALLY_DEBUG_POSTGRES_POLL("Poll %s: %s", t->db_table_name, str);
    return result;
}


/* Issue sequence update row request. */
int wp_send_poll_select(struct wp_connection *wp_conn)
{
    int result;
    struct wp_table *t = wp_conn->table;
    char str[DEFAULT_DB_STRING_SIZE];

    wp_conn->row_request_sequence = t->current_max_sequence;
    wp_conn->state = conn_table_poll_select;
    t->db_state = wp_db_table_poll_select;
    snprintf(str,
             sizeof(str),
             "SELECT * from %s where %s > %ld ORDER BY %s limit %d",
             t->db_table_name,
             t->sequence_column_name,
             (long)t->current_max_sequence,
             t->sequence_column_name, wally_gbl_cfg.wally_postgres_db_query_batch_size);
    WP_STATS_CONN_START(wp_conn);
    result = xPQsendQueryParams((PGconn *)(wp_conn->db_conn),
                               str,
                               0,
                               NULL,
                               NULL,
                               NULL,
                               NULL,
                               0);
    WALLY_DEBUG_POSTGRES_POLL("Poll %s: %s", t->db_table_name, str);
    return result;
}

/* Issue sequence update row request. */
int wp_send_poll_commit(struct wp_connection *wp_conn)
{
    int result;
    struct wp_table *t = wp_conn->table;
    char str[DEFAULT_DB_STRING_SIZE];

    wp_conn->table = t;
    wp_conn->state = conn_table_poll_commit;
    t->db_state = wp_db_table_poll_commit;
    snprintf(str,
             sizeof(str),
             "COMMIT");
    WP_STATS_CONN_START(wp_conn);
    result = xPQsendQueryParams((PGconn *)(wp_conn->db_conn),
                               str,
                               0,
                               NULL,
                               NULL,
                               NULL,
                               NULL,
                               0);
    WALLY_DEBUG_POSTGRES_POLL("Poll %s: %s", t->db_table_name, str);
    return result;
}

int wp_send_poll_rollback(struct wp_connection *wp_conn)
{
    int result;
    struct wp_table *t = wp_conn->table;
    char str[DEFAULT_DB_STRING_SIZE];

    wp_conn->table = t;
    wp_conn->state = conn_table_poll_rollback;
    t->db_state = wp_db_table_poll_rollback;
    snprintf(str,
             sizeof(str),
             "ROLLBACK");
    WP_STATS_CONN_START(wp_conn);
    result = xPQsendQueryParams((PGconn *)(wp_conn->db_conn),
                               str,
                               0,
                               NULL,
                               NULL,
                               NULL,
                               NULL,
                               0);
    WALLY_DEBUG_POSTGRES_POLL("Poll %s: %s", t->db_table_name, str);
    return result;
}

/* Issue sequence update row request. */
int wp_send_sequence_delete_request(struct wp_connection *wp_conn)
{
    int result;
    struct wp_table *t = wp_conn->table;
    char str[DEFAULT_DB_STRING_SIZE];

    t->sequence_deletion_in_progress = 1;
    wp_conn->state = conn_table_delete_sequence;
    snprintf(str,
             sizeof(str),
             "DELETE from %s where %s < %ld",
             t->db_table_name,
             t->sequence_column_name,
             (long)t->min_valid_sequence_set);
    t->min_valid_sequence_performed = t->min_valid_sequence_set;
    WP_STATS_CONN_START(wp_conn);
    result = xPQsendQueryParams((PGconn *)(wp_conn->db_conn),
                               str,
                               0,
                               NULL,
                               NULL,
                               NULL,
                               NULL,
                               0);
    WALLY_LOG(AL_NOTICE, "Deleting rows from %s/%s/%s: %s", wp_conn->wp_db->name, wp_conn->table->db_table_name, wp_conn->name, str);
    return result;
}

int wp_send_read_begin(struct wp_connection *wp_conn)
{
    int result;
    struct wp_table *t = wp_conn->table;
    char str[DEFAULT_DB_STRING_SIZE];

    wp_conn->table = t;
    wp_conn->state = conn_table_read_begin;
    snprintf(str,
             sizeof(str),
             "BEGIN");
    WP_STATS_CONN_START(wp_conn);
    result = xPQsendQueryParams((PGconn *)(wp_conn->db_conn),
                               str,
                               0,
                               NULL,
                               NULL,
                               NULL,
                               NULL,
                               0);
    WALLY_DEBUG_POSTGRES("Read %s: %s", t->db_table_name, str);
    return result;
}

/* Issue sequence update row request. */
int wp_send_read_lock(struct wp_connection *wp_conn)
{
    int result;
    struct wp_table *t = wp_conn->table;
    char str[DEFAULT_DB_STRING_SIZE];

    wp_conn->table = t;
    wp_conn->state = conn_table_read_lock;

	if (wally_multiple_index_consistency_support && !wp_conn->wp_db->is_true_origin) {
		snprintf(str,
				 sizeof(str),
				 "LOCK TABLE %s, %s%s", wp_conn->table->db_table_name,
				 wp_conn->table->db_table_name, WALLY_LOOKUP_TABLE_SUFFIX);
	} else {
		snprintf(str,
				 sizeof(str),
				 "LOCK TABLE %s", wp_conn->table->db_table_name);
	}
    WP_STATS_CONN_START(wp_conn);
    result = xPQsendQueryParams((PGconn *)(wp_conn->db_conn),
                               str,
                               0,
                               NULL,
                               NULL,
                               NULL,
                               NULL,
                               0);
    if (result)
    {
        wp_bootup_update_table_stats(t, wp_bootup_table_readlock_start);
    }
    WALLY_DEBUG_POSTGRES("Read %s: %s", t->db_table_name, str);
    return result;
}

/* Function :  wp_send_read_select_with_lookup
 * Arg      :  DB connection
 * Return   :  result of DB operation
 *
 *  Function to read DB rows greater than current max, current max sequence is picked from the lookup table
 */
int static wp_send_read_select_with_lookup(struct wp_connection *wp_conn, char *str)
{
	int result = 0;
    char *params[1];

	if (wp_conn->request_key) {
		if (wp_conn->table->row_key_argo_index != wp_conn->table->indexes[wp_conn->request_wp_index_index]->argo_index) {

			snprintf(str,
					 DEFAULT_DB_STRING_SIZE,
					 "SELECT * FROM %s WHERE %s=$1 AND %s<= (SELECT sequence FROM %s%s WHERE column_index=%d AND value=CAST($1 as TEXT)) "
					 "AND %s>%ld order by %s limit %d",
					 wp_conn->table->db_table_name,
					 wp_conn->table->indexes[wp_conn->request_wp_index_index]->column_name,
					 wp_conn->table->sequence_column_name,
					 wp_conn->table->db_table_name,
					 WALLY_LOOKUP_TABLE_SUFFIX,
					 wp_conn->table->indexes[wp_conn->request_wp_index_index]->argo_index,
					 wp_conn->table->sequence_column_name,
					 (long)wp_conn->row_request_sequence,
					 wp_conn->table->sequence_column_name, wally_gbl_cfg.wally_postgres_db_query_batch_size);
			} else {
				snprintf(str,
						 DEFAULT_DB_STRING_SIZE,
						 "SELECT * from %s where %s=$1 and %s>%ld and %s<=%ld order by %s limit %d",
						 wp_conn->table->db_table_name,
						 wp_conn->table->indexes[wp_conn->request_wp_index_index]->column_name,
						 wp_conn->table->sequence_column_name,
						 (long)wp_conn->row_request_sequence,
						 wp_conn->table->sequence_column_name,
						 (long)wp_conn->table->current_max_sequence,
						 wp_conn->table->sequence_column_name, wally_gbl_cfg.wally_postgres_db_query_batch_size);
			}
		WALLY_DEBUG_POSTGRES("%s WP: Connection %d Query, with $1=%s: (%p) %s\n", wp_conn->wp_db->name, wp_conn->my_index, wp_conn->request_key, str, str);
		params[0] = wp_conn->request_key;
		WP_STATS_CONN_START(wp_conn);
		result = xPQsendQueryParams((PGconn *)(wp_conn->db_conn),
								   str,
								   1,
								   NULL,
								   (const char * const *)params,
								   NULL,
								   NULL,
								   0);
	} else {
		snprintf(str,
				 DEFAULT_DB_STRING_SIZE,
				 "SELECT * FROM %s WHERE %s <= (SELECT sequence FROM %s%s WHERE column_index=%d AND value='null') AND %s>%ld order by %s limit %d",
				 wp_conn->table->db_table_name,
				 wp_conn->table->sequence_column_name,
				 wp_conn->table->db_table_name,
                 WALLY_LOOKUP_TABLE_SUFFIX,
				 WALLY_MAX_COLUMNS,
				 wp_conn->table->sequence_column_name,
				 (long)wp_conn->row_request_sequence,
				 wp_conn->table->sequence_column_name,
				 wally_gbl_cfg.wally_postgres_db_query_batch_size);
		WALLY_DEBUG_POSTGRES("%s WP: Connection %d Query: (%p) %s\n", wp_conn->wp_db->name, wp_conn->my_index, str, str);
		WP_STATS_CONN_START(wp_conn);
		result = xPQsendQueryParams((PGconn *)(wp_conn->db_conn),
								   str,
								   0,
								   NULL,
								   NULL,
								   NULL,
								   NULL,
								   0);
	}
	return result;
}

#define WALLY_MULTIPLE_INDEX_CONSISTENCY_CHECK_LOOKUP wally_multiple_index_consistency_support && \
													  !(wp_conn->wp_db->is_true_origin) && \
													  !(wp_conn->table->fully_loaded)

/* Function :  wp_send_read_select_nolock
 * Arg      :  DB connection
 * Return   :  result of DB operation
 *
 *  Function to read DB rows greater than current max sequence without
 * table exclusive lock in DB. This function is called only during
 * initialisation.
 */
int wp_send_read_select_nolock(struct wp_connection *wp_conn)
{
    int result = 0;
    char str[DEFAULT_DB_STRING_SIZE] = {0};
    char *params[1];

    wp_conn->state = conn_table_read_select_nolock;
    wp_bootup_update_table_stats(wp_conn->table, wp_bootup_table_num_queries);

	if (WALLY_MULTIPLE_INDEX_CONSISTENCY_CHECK_LOOKUP){
		result = wp_send_read_select_with_lookup(wp_conn, str);
	} else {
		if (wp_conn->request_key) {
			params[0] = wp_conn->request_key;
			snprintf(str,
					 sizeof(str),
					 "SELECT * from %s where %s=$1 and %s>%ld and %s<=%ld order by %s limit %d",
					 wp_conn->table->db_table_name,
					 wp_conn->table->indexes[wp_conn->request_wp_index_index]->column_name,
					 wp_conn->table->sequence_column_name,
					 (long)wp_conn->row_request_sequence,
					 wp_conn->table->sequence_column_name,
					 (long)wp_conn->table->current_max_sequence,
					 wp_conn->table->sequence_column_name, wally_gbl_cfg.wally_postgres_db_query_batch_size);
			WP_STATS_CONN_START(wp_conn);
			result = xPQsendQueryParams((PGconn *)(wp_conn->db_conn),
									   str,
									   1,
									   NULL,
									   (const char * const *)params,
									   NULL,
									   NULL,
									   0);
		} else {
			snprintf(str,
					 sizeof(str),
					 "SELECT * from %s where %s > %"PRId64" and %s <= %"PRId64" order by %s limit %d",
					 wp_conn->table->db_table_name,
					 wp_conn->table->sequence_column_name,
					 wp_conn->row_request_sequence,
					 wp_conn->table->sequence_column_name,
					 wp_conn->table->current_max_sequence,
					 wp_conn->table->sequence_column_name, wally_gbl_cfg.wally_postgres_db_query_batch_size);
			WP_STATS_CONN_START(wp_conn);
			result = xPQsendQueryParams((PGconn *)(wp_conn->db_conn),
									   str,
									   0,
									   NULL,
									   NULL,
									   NULL,
									   NULL,
									   0);
		}
	}
    WALLY_DEBUG_POSTGRES("Read %s: %s", wp_conn->table->db_table_name, str);
    if (!result) {
        /* XXX LOG */
        WALLY_LOG(AL_ERROR, "xPQsendQueryParams failed: %s, cmd = %s", PQerrorMessage((PGconn *)(wp_conn->db_conn)), str);
    } else {
        wp_bootup_update_table_stats(wp_conn->table, wp_bootup_table_query_start);
    }
    return result;
}


/* Issue row request. */
int wp_send_read_select(struct wp_connection *wp_conn)
{
    int result;
    char str[DEFAULT_DB_STRING_SIZE];
    char *params[1];

    wp_conn->state = conn_table_read_select;
    wp_bootup_update_table_stats(wp_conn->table, wp_bootup_table_num_queries);

	if (WALLY_MULTIPLE_INDEX_CONSISTENCY_CHECK_LOOKUP) {
		result = wp_send_read_select_with_lookup(wp_conn, str);
	} else {
		if (wp_conn->request_key) {
			snprintf(str,
					 sizeof(str),
					 "SELECT * from %s where %s=$1 and %s>%ld and %s<=%ld order by %s limit %d",
					 wp_conn->table->db_table_name,
					 wp_conn->table->indexes[wp_conn->request_wp_index_index]->column_name,
					 wp_conn->table->sequence_column_name,
					 (long)wp_conn->row_request_sequence,
					 wp_conn->table->sequence_column_name,
					 (long)wp_conn->table->current_max_sequence,
					 wp_conn->table->sequence_column_name, wally_gbl_cfg.wally_postgres_db_query_batch_size);
			//fprintf(stderr, "WP: Connection %d Query, with $1=%s: (%p) %s\n", wp_conn->my_index, wp_conn->request_key, str, str);
			params[0] = wp_conn->request_key;
			WP_STATS_CONN_START(wp_conn);
			result = xPQsendQueryParams((PGconn *)(wp_conn->db_conn),
									   str,
									   1,
									   NULL,
									   (const char * const *)params,
									   NULL,
									   NULL,
									   0);
		} else {
			snprintf(str,
					 sizeof(str),
					 "SELECT * from %s where %s>%ld order by %s limit %d",
					 wp_conn->table->db_table_name,
					 wp_conn->table->sequence_column_name,
					 (long)wp_conn->row_request_sequence,
					 wp_conn->table->sequence_column_name, wally_gbl_cfg.wally_postgres_db_query_batch_size);
			//fprintf(stderr, "WP: Connection %d Query: (%p) %s\n", wp_conn->my_index, str, str);
			params[0] = wp_conn->request_key;
			WP_STATS_CONN_START(wp_conn);
			result = xPQsendQueryParams((PGconn *)(wp_conn->db_conn),
									   str,
									   0,
									   NULL,
									   NULL,
									   NULL,
									   NULL,
									   0);
		}
	}
    WALLY_DEBUG_POSTGRES("Read %s: %s", wp_conn->table->db_table_name, str);
    if (!result) {
        /* XXX LOG */
        WALLY_LOG(AL_ERROR, "xPQsendQueryParams failed: %s, cmd = %s", PQerrorMessage((PGconn *)(wp_conn->db_conn)), str);
    } else {
        wp_bootup_update_table_stats(wp_conn->table, wp_bootup_table_query_start);
    }

    return result;
}

int wp_send_read_commit(struct wp_connection *wp_conn)
{
    int result;
    struct wp_table *t = wp_conn->table;
    char str[DEFAULT_DB_STRING_SIZE];

    wp_conn->table = t;
    wp_conn->state = conn_table_read_commit;
    snprintf(str,
             sizeof(str),
             "COMMIT");
    WP_STATS_CONN_START(wp_conn);
    result = xPQsendQueryParams((PGconn *)(wp_conn->db_conn),
                               str,
                               0,
                               NULL,
                               NULL,
                               NULL,
                               NULL,
                               0);
    WALLY_DEBUG_POSTGRES("Read %s: %s", t->db_table_name, str);
    if (!result) {
        /* XXX LOG */
        WALLY_LOG(AL_ERROR, "xPQsendQueryParams failed: %s, cmd = %s", PQerrorMessage((PGconn *)(wp_conn->db_conn)), str);
    }
    return result;
}

int wp_send_minseq_update(struct wp_connection *wp_conn)
{
    int result;
    struct wp_table *t = wp_conn->table;
    char str[DEFAULT_DB_STRING_SIZE];

    wp_conn->state = conn_table_minseq_update;
    snprintf(str,
             sizeof(str),
             "UPDATE zpath_table SET min_valid_sequence = %"PRId64" where name = '%s'",
             t->min_valid_sequence_set,
             t->db_table_name);
    WP_STATS_CONN_START(wp_conn);
    result = xPQsendQueryParams((PGconn *)wp_conn->db_conn,
                               str,
                               0,
                               NULL,
                               NULL,
                               NULL,
                               NULL,
                               0);
    WALLY_LOG(AL_NOTICE, "Min Sequence Update: result=%d, update row from %s/%s/%s: %s",
                         result, wp_conn->wp_db->name, wp_conn->table->db_table_name, wp_conn->name, str);
    return result;
}

int wp_send_cleanup_update(struct wp_connection *wp_conn)
{
    int result;
    struct wp_table *t = wp_conn->table;
    char str[DEFAULT_DB_STRING_SIZE];

    int64_t sequence = t->cleanup_buf[t->cleanup_index].sequence;
    int64_t key_int = t->cleanup_buf[t->cleanup_index].key_int;
    wp_conn->state = conn_table_cleanup_update;
    snprintf(str,
             sizeof(str),
             "UPDATE %s SET deleted = 0 where sequence = %ld",
             t->db_table_name,
             (long)sequence);
    WP_STATS_CONN_START(wp_conn);
    result = xPQsendQueryParams((PGconn *)wp_conn->db_conn,
                               str,
                               0,
                               NULL,
                               NULL,
                               NULL,
                               NULL,
                               0);
    WALLY_DEBUG_TABLE_CLEANUP("wally cleanup: result=%d, update row from %s/%s/%s, seq=%"PRId64", key_int=%"PRId64" : %s",
                              result, wp_conn->wp_db->name, wp_conn->table->db_table_name, wp_conn->name, sequence, key_int, str);
    return result;
}

/*
 * Given a result, parse columns and determine with which argo
 * description indexes they match.  If there is no match, set to -1.
 */
int *wp_parse_columns(struct wp_table *t, PGresult *pres, int *sequence_argo_id, struct wp_connection *wp_conn)
{
    struct argo_private_field_description *fd;
    int field_count;
    int *retval = NULL;
    int i;

    argo_read_lock();
    field_count = PQnfields(pres);
    if (field_count) {
        retval = (int *) WALLY_MALLOC (sizeof(int) * field_count);
        if (retval) {
            for (i = 0; i < field_count; i++) {
                char *field_name;

                field_name = PQfname(pres, i);
                fd = zhash_table_lookup(t->description->described_fields, field_name, strlen(field_name), NULL);
                if (fd) {
                    retval[i] = fd->index;
                    if (fd->public_description.is_sequence) *sequence_argo_id = fd->index;
                } else {
                    /* Field not found- no place to put it in an argo
                     * object! We'll mark the table for
                     * resynchronization. We cannot simply expand argo
                     * here, because we don't really know the type of
                     * the object being returned- we need to read more
                     * data out of the table for that, which is an
                     * asynchronous process. */
                    if (!t->needs_to_be_resynchronized) {
                        t->needs_to_be_resynchronized = 1;
                    }
                    WALLY_FREE(retval);
                    retval = NULL;
                    WALLY_LOG(AL_CRITICAL, "WP: could not find field for %s; resynchonize the table %s",
                            field_name, t->db_table_name);
                    wally_error_handler(NULL, wp_conn, true, E_SCHEMA_MISMATCH,
                            SE_NONE);
                }
            }
        }
    }
    argo_unlock();
    return retval;
}

/*
 * This code CHANGES the contents of the string, exclusively shrinking
 * it.  It also null-terminates strings by overwriting the dividing
 * character (generally a '"' or a comma)
 *
 * This code MUST be run on an array. (parsing non-arrays is direct)
 *
 *  normalize = modify string to remove quotes, escape
 *              characters. Otherwise string is left intact.
 *
 *  start = Points at the first character of the string, unless it
 *          points at a '"', in which case it points at the beginning
 *          of a quoted string.
 *
 *  next = when done, points at the 'start' of the next string in the
 *         array, is NULLed if there is not another one.
 */
static int wp_get_next_string(int normalize, uint8_t *start, uint8_t **next)
{
    uint8_t *w = start;
    uint8_t *out = start;
    int need_quote;

    //char foo[200];
    //WALLY_LOG(AL_DEBUG, "processing string <%s>", start);
    //argo_hexdump_buf(start, strlen((char *)start), 0, foo, sizeof(foo));
    //WALLY_LOG(AL_DEBUG, "Hex: %s", foo);

    /* Skip leading '{' at beginning of array. All '{' and '}' are in
     * quoted strings out of postgres, so this is legal. */
    if (w && ((*w) == '{')) w++;

    if (!w || (!(*w)) || ((*w) == '}')) {
        *next = NULL;
        return WALLY_RESULT_NOT_FOUND;
    }

    if ((*w) == '"') {
        need_quote = 1;
        w++;
    } else {
        need_quote = 0;
    }
    while (*w) {
        if (need_quote && ((*w) == '"')) {
            /* Found end of string, quoted */
            break;
        }
        if (!need_quote && (((*w) == ',') || ((*w) == '}'))) {
            /* Found end of string, non-quoted */
            break;
        }
        if (need_quote && ((*w) == '\\')) {
            w++;
        }
        if (normalize) (*out) = *w;
        out++;
        w++;
    }
    if (*w) {
        if ((*w) == '"') {
            if (w[1] == ',') {
                w += 2;
            } else {
                w = NULL;
            }
        } else if ((*w) == ',') {
            w++;
        } else if ((*w) == '}') {
            w++;
            if (*w) {
                WALLY_LOG(AL_ERROR, "Unusual non-terminated string");
            }
        } else if ((*w) != 0) {
            *next = NULL;
            return WALLY_RESULT_ERR;
        }
    }
    (*next) = w;

    if (normalize) (*out) = 0;
    return WALLY_RESULT_NO_ERROR;
}

/* Get the number of elements in a postgres array */
static int wp_count_elements(uint8_t *field_str)
{
    int count = 0;
    int res;

    uint8_t  *walk = field_str;

    while(walk) {
        res = wp_get_next_string(0, walk, &walk);
        if (res) {
            if (res == WALLY_RESULT_NOT_FOUND) return count;

            WALLY_LOG(AL_ERROR, "Could not count elements for string <%s>: %s", field_str, wally_error_strings[res]);
            return 0;
        }
        count++;
    }
    return count;
}


struct argo_object *wp_parse_row(struct wp_table *t, int *xlate, PGresult *pres, int row)
{
    int desc_count = t->description->description_count - t->description->static_description_count;
    /* YAY, 1MB STACK, GO GO GO */
    int def_sz = 1024*1024;
    int min_bsz = 1 + sizeof(struct argo_object) + t->description->structure_size + sizeof(struct argo_excess_field)*desc_count;
    if (et_26295_test_size > 0) {
        def_sz = db_min(def_sz, et_26295_test_size);
        WALLY_LOG(AL_WARNING, "ET_26295_TEST default_sz = %d", def_sz);
    }
    int bsz = db_max(def_sz, min_bsz);
    uint8_t buf[bsz];

    uint8_t *heap = NULL;
    uint8_t *heap_end = NULL;
    uint8_t *heap_start = NULL;
    int i;
    int field_count;
    struct argo_object *dest_object;
    int64_t tmp_int;
    //size_t tmp_int_size;
    uint8_t *tmp_str;
    int is_null;
    char *tmp_char;
    //int count = 0;
    //int size = 0;
    int res;
    int err = 0;
    size_t alloc_size = 0;

    argo_read_lock();

 retry:
    err = 0;
    if (heap) {
        /* Stack heap must have been too small.. */
        /* create heap using... heap. */
        /* Keep trying up to 16 MB */
        if (alloc_size == 0) {
            alloc_size = 8 * 1024 * 1024;
        } else if (alloc_size == (8 * 1024 * 1024)) {
            WALLY_FREE(heap_start);
            alloc_size = 16 * 1024 * 1024;
        } else {
            WALLY_FREE(heap_start);
            return NULL;
        }
        WALLY_LOG(AL_WARNING, "Data is too large, heap is reallocated with size %d", (int)alloc_size);
        heap = WALLY_MALLOC(alloc_size);
        heap_end = heap + alloc_size;
    } else {
        heap = &(buf[0]);
        heap_end = heap + sizeof(buf);
    }
    heap_start = heap;

    /* Set up object */
    /*
     * Must make sure the min_bsz is calculated correctly as needed in this block,
     * if the code in this block is updated, we may need to adjust min_bsz at begining of the function.
     */
    dest_object = (struct argo_object *) heap;
    heap += sizeof(struct argo_object);
    dest_object->base_structure_index = t->description->global_index;
    dest_object->excess_description_count = desc_count;
    dest_object->reference_count = 0;
    dest_object->base_structure_data = (void *) heap;
    heap += t->description->structure_size;
    dest_object->excess_fields = (void *) heap;
    heap += sizeof(struct argo_excess_field) * dest_object->excess_description_count;
    memset(dest_object->base_structure_data, 0, heap - (dest_object->base_structure_data));


    field_count = PQnfields(pres);
    for (i = 0; i < field_count; i++) {
        if (xlate[i] >= 0) {
            int array_index = 0;
            int max_array_index = 1;
            int db_element_count;

            struct argo_field_description *fd;
            fd = &(t->description->description[xlate[i]]->public_description);

            if (fd->dont_read_origin) continue;
            if (fd->dont_db) continue;


            /*
             * Figure out maximum number of array elements, keeping in
             * mind the plethora of supported configurations
             *
             * max_array_index will be between 1 and
             * ARGO2_MAX_ARRAY_SIZE upon completion of this little
             * section, and will account for how much space is
             * available in the structure definition
             */
            tmp_str = (uint8_t *)PQgetvalue(pres, row, i);
            if (fd->is_array) {
                db_element_count = wp_count_elements(tmp_str);
            } else {
                db_element_count = 1;
            }
            is_null = PQgetisnull(pres, row, i);
            if (fd->hard_count) {
                /* Modern code: We will have a hard count if we ever have a hard count */
                max_array_index = fd->hard_count;
                if (db_element_count < max_array_index) max_array_index = db_element_count;
            } else {
                /* Older code: can be mostly deprecated later, and
                 * just be set to argo2_max_array_size */
                if (fd->have_dynamic_count_index) {
                    max_array_index = db_element_count;
                } else {
                    if (fd->is_reference || (fd->argo_field_type != argo_field_data_type_string)) {
                        max_array_index = fd->count;
                    } else {
                        max_array_index = 1;
                    }
                }
            }
            if (max_array_index > ARGO2_MAX_ARRAY_SIZE) {
                max_array_index = ARGO2_MAX_ARRAY_SIZE;
            }
            if (max_array_index == 0) max_array_index = 1;
            if (db_element_count > max_array_index) {
                WALLY_LOG(AL_ERROR, "Field %s of table %s contains more entries (%d) than allowed (%d). Trimmed!",
                          fd->field_name,
                          t->db_table_name,
                          db_element_count,
                          max_array_index);
            }

            //WALLY_DEBUG_POSTGRES("Read string for %s/%s as <%s>", t->name, fd->field_name, tmp_str);
            switch (fd->argo_field_type) {
            case argo_field_data_type_integer:
                //WALLY_LOG(AL_DEBUG, "Read array count for %s as %d", fd->field_name, max_array_index);
                if (fd->is_array) {
                    /* Format: empty string, or {int1,int2,int3} etc */
                    /* Check for NULL array. */
                    if (!*tmp_str) continue;
                    tmp_str++;
                }

                if (!(*tmp_str)) {
                    /* Must always have a value for an integer, if it is not an array- we read it as zero, even if it is null */
                    if (!fd->is_array) {
                        tmp_int = 0;
                        //tmp_int_size = sizeof(tmp_int);
                        res = argo2_structure_write_field_by_description(fd,
                                                                         dest_object->base_structure_data,
                                                                         array_index,
                                                                         1,
                                                                         &tmp_int,
                                                                         &heap,
                                                                         heap_end);
                        if (res) {
                            if (res == ARGO_RESULT_ERR_TOO_LARGE) goto retry;
                            WALLY_LOG(AL_ERROR, "Error: Could not write field (%s): %s, ix = %d, max = %d", fd->field_name, argo_result_string(res), array_index, max_array_index);
                            err = 1;
                            break;
                        }
                    } else {
                        WALLY_LOG(AL_ERROR, "Implement Me!");
                        /* Implement me! */
                        /* XXX */
                    }
                    array_index++;
                } else {
                    while ((array_index < max_array_index) && (*tmp_str) && (*tmp_str != '}')) {

                        /* Quick check if value is NULL */
                        if (strncmp((char *)tmp_str, "NULL", 4) == 0) {
                            tmp_int = 0;
                            tmp_str += 4;
                            if (fd->is_array) {
                                /* Don't include NULL in arrays of integers */
                                if (*tmp_str) tmp_str++;
                                continue;
                            }
                        } else if ((tmp_str[0] | 0x20) == 't') {
                            /* Catch boolean true */
                            tmp_int = 1;
                            tmp_str++;
                        } else if ((tmp_str[0] | 0x20) == 'f') {
                            /* Catch boolean false */
                            tmp_int = 0;
                            tmp_str++;
                        } else {
                            tmp_int = strtoll((char *)tmp_str, &tmp_char, 10);
                            tmp_str = (uint8_t *)tmp_char;
                        }

                        //tmp_int_size = sizeof(tmp_int);
                        res = argo2_structure_write_field_by_description(fd,
                                                                         dest_object->base_structure_data,
                                                                         array_index,
                                                                         max_array_index,
                                                                         &tmp_int,
                                                                         &heap,
                                                                         heap_end);
                        if (res) {
                            if (res == ARGO_RESULT_ERR_TOO_LARGE) goto retry;
                            WALLY_LOG(AL_ERROR, "Error: Could not write field (%s)", fd->field_name);
                            err = 1;
                            break;
                        }
                        array_index++;
                        if (*tmp_str) tmp_str++;
                    }
                }
                if (fd->have_dynamic_count_index) {
                    argo_structure_write_int_by_column_index(t->description, dest_object->base_structure_data, fd->dynamic_count_index, array_index);
                    //WALLY_LOG(AL_DEBUG, "Writing dynamic count for field %s, at index %d as %d", fd->field_name, (int)fd->dynamic_count_index, (int) array_index);
                }


                //size = fd->size;
                //count = array_index;
                break;
            case argo_field_data_type_string:
                if (1) {
                    uint8_t *w = tmp_str;

                    if (((*w) == 0) || is_null) {
                        /* Empty string or NULL. We encode both these
                         * cases as a NULL because not an empty
                         * string. This is because TONS of code does
                         * NULL pointer checks for 'lack of value'
                         * rather than checking for empty
                         * strings. This is not technically correct,
                         * but would take some significant code work
                         * to fix everywhere else it these values are
                         * checked. */
                        res = argo_structure_null_field(fd, dest_object->base_structure_data, 0);
                        if (res) {
                            WALLY_LOG(AL_ERROR, "Unexpected error: %s", wally_error_strings[res]);
                        }
                    } else {
                        if (!fd->is_array) {
                            res = argo2_structure_write_field_by_description(fd,
                                                                             dest_object->base_structure_data,
                                                                             array_index,
                                                                             1,
                                                                             tmp_str,
                                                                             &heap,
                                                                             heap_end);
                            if (res) {
                                if (res == ARGO_RESULT_ERR_TOO_LARGE) goto retry;
                                WALLY_LOG(AL_ERROR, "%s: Error: Could not set up write field %s: %s", t->db_table_name, fd->field_name, argo_result_string(res));
                                err = 1;
                                break;
                            }

                            array_index++;
                        } else {
                            while (tmp_str && (array_index < max_array_index)) {
                                res = wp_get_next_string(1, tmp_str, &w);
                                if (res == WALLY_RESULT_NOT_FOUND) {
                                    res = WALLY_RESULT_NO_ERROR;
                                    break;
                                }
                                if (res) {
                                    WALLY_LOG(AL_ERROR, "Error: could not get string from %s, for field %s", tmp_str, fd->field_name);
                                    err = 1;
                                    break;
                                }

                                res = argo2_structure_write_field_by_description(fd,
                                                                                 dest_object->base_structure_data,
                                                                                 array_index,
                                                                                 max_array_index,
                                                                                 tmp_str,
                                                                                 &heap,
                                                                                 heap_end);
                                if (res) {
                                    if (res == ARGO_RESULT_ERR_TOO_LARGE) goto retry;
                                    WALLY_LOG(AL_ERROR, "Error: Could not set up write field (%s): %s: %s", fd->field_name, wally_error_strings[res], tmp_str);
                                    err = 1;
                                    break;
                                }
                                array_index++;
                                tmp_str = w;
                            }
                            if (err) {
                                //WALLY_LOG(AL_DEBUG, "Breaking on err");
                                break;
                            }
                        }
                    }
                    if (fd->have_dynamic_count_index) {
                        argo_structure_write_int_by_column_index(t->description, dest_object->base_structure_data, fd->dynamic_count_index, array_index);
                    }

                }
                break;
            case argo_field_data_type_binary:
                if (fd->is_array && (fd->is_inet || fd->is_double)) {
                    char one_str[100];
                    uint8_t *w = tmp_str;
                    /* XXX LOG */
                    while (array_index < max_array_index) {
                        char *s = one_str;
                        char *e = s + sizeof(one_str) - 1;
                        int res;
                        if (*w == '{') w++;
                        if (*w == ',') w++;
                        if (*w == '}') break;
                        if (*w == 0) break;
                        while ((*w) &&
                               (*w != ',') &&
                               (*w != '}') &&
                               (s < e)) {
                            *s = *w;
                            s++;
                            w++;
                        }
                        if (s == e) {
                            WALLY_LOG(AL_ERROR, "Error: Could not parse data for <%s>", tmp_str);
                            err = 1;
                            break;
                        }
                        *s = 0;

                        if (fd->is_double) {
                            char *tmp_res;
                            double value;
                            value = strtod(one_str, &tmp_res);
                            if (tmp_res == one_str) {
                                if (strlen(one_str) == 0) {
                                    value = 0;
                                } else {
                                    WALLY_LOG(AL_ERROR, "Error: Non-parsable double for %s: <%s>", fd->field_name, one_str);
                                    value = 0;
                                }
                            }

                            res = argo2_structure_write_field_by_description(fd,
                                                                             dest_object->base_structure_data,
                                                                             array_index,
                                                                             max_array_index,
                                                                             &value,
                                                                             &heap,
                                                                             heap_end);
                            if (res) {
                                if (res == ARGO_RESULT_ERR_TOO_LARGE) goto retry;
                                WALLY_LOG(AL_ERROR, "Error: Could not set up write field (%s): %s: %s", fd->field_name, wally_error_strings[res], tmp_str);
                                err = 1;
                                break;
                            }
                        } else if (fd->is_inet) {
                            struct argo_inet inet;
                            res = argo_string_to_inet(one_str, &inet);
                            if (res) {
                                WALLY_LOG(AL_ERROR, "Error: Could not convert string to inet: <%s>", one_str);
                                memset(&inet, 0, sizeof(inet));
                            }
                            res = argo2_structure_write_field_by_description(fd,
                                                                             dest_object->base_structure_data,
                                                                             array_index,
                                                                             max_array_index,
                                                                             &inet,
                                                                             &heap,
                                                                             heap_end);
                            if (res) {
                                if (res == ARGO_RESULT_ERR_TOO_LARGE) goto retry;
                                WALLY_LOG(AL_ERROR, "Error: Could not set up write field (%s): %s: %s", fd->field_name, wally_error_strings[res], tmp_str);
                                err = 1;
                                break;
                            }
                        } else {
                            res = WALLY_RESULT_ERR;
                        }

                        if (res) {
                            /* XXX LOG */
                            if (one_str[0]) {
                                WALLY_LOG(AL_ERROR, "Cannot parse Field=%s: %s, table %s, str = <%s>", fd->field_name, argo_result_string(res), t->db_table_name, one_str);
                            } else {
                                /* Empty data- this happens with empty arrays fairly often... */
                            }
                        } else {
                            array_index++;
                        }
                    }
                    if (fd->have_dynamic_count_index) {
                        argo_structure_write_int_by_column_index(t->description, dest_object->base_structure_data, fd->dynamic_count_index, array_index);
                        //WALLY_LOG(AL_DEBUG, "Writing dynamic count for field %s, at index %d as %d", fd->field_name, (int)fd->dynamic_count_index, (int) array_index);
                    }
                } else {
                    if (fd->is_inet || fd->is_double) {
                        int res;
                        uint8_t *out_address_start;
                        uint8_t *out_address_end;
                        res = argo_structure_setup_write_field(t->description,
                                                               fd,
                                                               dest_object->base_structure_data,
                                                               0,
                                                               &out_address_start,
                                                               &out_address_end,
                                                               0,
                                                               &heap,
                                                               heap_end);
                        if (res) {
                            if (res == ARGO_RESULT_ERR_TOO_LARGE) goto retry;
                            WALLY_LOG(AL_ERROR, "Error: Could not set up write field (%s)", fd->field_name);
                            err = 1;
                            break;
                        }
                        //fprintf(stderr, "Parsing INET: <%s>\n", tmp_str);
                        if (fd->is_inet) {
                            res = argo_string_to_inet((char *)tmp_str, (struct argo_inet *)out_address_start);
                        } else if (fd->is_double) {
                            char *tmp_res;
                            double value;
                            value = strtod((const char *)tmp_str, &tmp_res);
                            if ((uint8_t *)tmp_res == tmp_str) {
                                if (strlen((char *)tmp_str) == 0) {
                                    value = 0;
                                } else {
                                    WALLY_LOG(AL_ERROR, "Error: Non-parsable double for %s: %s", fd->field_name, tmp_str);
                                    value = 0;
                                }
                                /* Don't fail- just log */
                            }
                            if ((out_address_end - out_address_start) < sizeof(double)) {
                                WALLY_LOG(AL_ERROR, "Error: Too small size for double output for %s: %ld", fd->field_name, (long) (out_address_end - out_address_start));
                                res = WALLY_RESULT_ERR;
                                err = 1;
                                break;
                            }
                            *((double *)out_address_start) = value;
                            res = WALLY_RESULT_NO_ERROR;
                        }
                        if (res) {
                            /* XXX LOG */
                            WALLY_LOG(AL_ERROR, "Cannot parse Field=%s", fd->field_name);
                        }
                    } else {
                        /* XXX LOG */
                        WALLY_LOG_NOT_IMPLEMENTED();
                        WALLY_LOG(AL_ERROR, "Not implemented, table = %s, fd->field_name = %s", t->db_table_name, fd->field_name);
                    }
                }
                break;
            default:
                /* XXX LOG */
                WALLY_LOG(AL_ERROR, "Cannot process field %s", fd->field_name);
                continue;
            }
        }
    }
    dest_object->total_size = heap - heap_start;

    argo_unlock();

    /* Copy out of our static buffer into an allocated buffer. We
     * don't pre-allocate this because we don't know how big it will
     * be. */
    if (err) {
        dest_object = NULL;
    } else {
        dest_object = argo_object_copy(dest_object);
    }

    if (alloc_size) WALLY_FREE(heap_start);

    return dest_object;
}


/*
 * The result of issuing a query to read some rows has completed.
 *
 * is_update is set if this row reading is the result of a
 * notification message indicating the table has been updated.
 *
 * Holds wally lock for duration.
 */
int wp_update_table_read_rows(struct wp_table *t, struct wp_connection *wp_conn, int is_poll, struct argo_object **objects, int64_t *indexes, int *object_count, int *read_more)
{
    PGresult *pres;
    int rows;
    int *pg_row_to_argo_row;
    int i, j, res;
    int ret_result = WALLY_RESULT_NO_ERROR;
    int sequence_argo_id = -1;
    int64_t seq;
    struct argo_object *object;
    int somebody_loves_me;

    //fprintf(stderr, "WP: Reading rows: Is update = %d\n", is_poll);

    *object_count = 0;
    *read_more = 0;

    pres = PQgetResult((PGconn *)(wp_conn->db_conn));
    if (!pres) {
        WALLY_LOG(AL_WARNING, "WP: Reading rows: No rows to read? W T F?");
        return WALLY_RESULT_NO_ERROR;
    }

    res = PQresultStatus(pres);
    if (res != PGRES_TUPLES_OK) {
        /* XXX LOG */
        fprintf(stderr, "Postgres Error: %s\n", PQresultErrorMessage(pres));
        WALLY_LOG(AL_ERROR, "WP: Error: bad row read");
        /* Only log the PSQL error and don't trigger watchdog trigger,
         * caller will decide on this */
        wally_error_handler(pres, wp_conn, true,
                E_MAX, SE_NONE);
        return WALLY_RESULT_BAD_DATA;
    }

    rows = PQntuples(pres);

    //fprintf(stderr, "WP: Read %d rows\n", rows);

    if (rows) {
        /* XXX Read columns, and map them to argo indexes. (will mark for resynchronization if needed) */
        pg_row_to_argo_row = wp_parse_columns(t, pres, &sequence_argo_id, wp_conn);

        if (!pg_row_to_argo_row) {
            /* XXX LOG. */
            WALLY_LOG(AL_WARNING, "WP: No row lookup");
            ret_result = WALLY_RESULT_BAD_DATA;
        } else if (sequence_argo_id < 0) {
            WALLY_LOG(AL_WARNING, "WP: No sequence ID lookup");
            /* XXX LOG. */
            WALLY_FREE(pg_row_to_argo_row);
        } else {

            /* XXX For each row, process the row. */
            for (i = 0; i < rows; i++) {
                object = wp_parse_row(t, pg_row_to_argo_row, pres, i);
                if (object) {
                    /* send object upstream. Also, check its sequence, if
                     * we're in "is_poll" mode. */
                    if (sequence_argo_id >= 0) {
                        struct argo_field_description *fd;
                        fd = &(t->description->description[sequence_argo_id]->public_description);
                        if (fd->argo_field_type != argo_field_data_type_integer) {
                            /* XXX LOG. Bad. */
                        } else if (fd->is_reference) {
                            /* XXX LOG. Bad. */
                        } else {
                            seq = argo_read_int(((int8_t *)object->base_structure_data) + fd->offset, fd->size);
                            //fprintf(stderr, "WP: Received row with sequence %ld\n", (long) seq);
                            /* We only update our table seqeuencing
                             * when we're doing overall table reads-
                             * we don't want some other read
                             * in-between to short circuit our seqence
                             * updates and thereby skip rows. */
                            if (is_poll) {
                                if (seq > t->current_max_sequence) {
                                    t->current_max_sequence = seq;
                                }
                            }
                            if (seq > wp_conn->row_request_sequence) {
#if 0
                                fprintf(stderr, "Updating sequence from %ld to %ld (%ld)\n", (long)wp_conn->row_request_sequence, (long)seq,
                                        (long)argo_object_get_sequence(object));
#endif // 0
                                wp_conn->row_request_sequence = seq;
                            }
                        }
                    }
                    /* We test this object against our registrations... */
                    somebody_loves_me = 0;
                    for (j = 0; j < t->index_count; j++) {
                        int64_t val;
                        char *str;
                        char val_str[100];
                        int64_t req_id = 0;
                        int64_t *p;

                        if (t->indexes[j]->is_null) {
                            if (t->indexes[j]->is_null_registered) {
                                req_id = t->indexes[j]->null_request_id;
                            }
                        } else if (t->indexes[j]->argo_field_type == argo_field_data_type_integer) {
                            res = argo_object_read_int_by_column_index(object, t->indexes[j]->argo_index, &val);
                            if (res) {
                                /* XXX LOG */
                            } else {
                                snprintf(val_str, sizeof(val_str), "%ld", (long) val);
                                if ((p = argo_hash_lookup(t->indexes[j]->interests, val_str, strlen(val_str), NULL))) {
                                    req_id = *p;
                                } else {
                                }
                            }
                        } else if (t->indexes[j]->argo_field_type == argo_field_data_type_string) {
                            res = argo_object_read_string_by_column_index(object, t->indexes[j]->argo_index, &str);
                            if (res) {
                                /* XXX LOG */
                            } else {
                                /* XXX If we need to support null
                                 * string interest registrations in
                                 * the future, we will need to do a
                                 * little better here. For now, this
                                 * is perfectly fine. */
                                if (str && ((p = argo_hash_lookup(t->indexes[j]->interests, str, strlen(str), NULL)))) {
                                    req_id = *p;
                                } else {
                                }
                            }
                        } else {
                            /* Erk. */
                            /* XXX LOG */
                        }
                        if (req_id) {
                            somebody_loves_me = 1;
                            /* Send them uh.. rows */
                            /* XXX IMPLEMENT ME */
                            //fprintf(stderr, "WP: Supposed to send this row upstream, because somebody is interested in it.\n");
                            if (*object_count >= (wally_gbl_cfg.wally_postgres_db_query_batch_size * WALLY_MAX_INDEXED_COLUMNS)) {
                                /* XXX LOG! */
                                WALLY_LOG(AL_ERROR, "Object count exceeds max");
                            } else {
                                objects[*object_count] = object;
                                indexes[*object_count] = req_id;
                                (*object_count)++;
                                argo_object_hold(object);
                            }
                        }
                    }
                    if (somebody_loves_me) {
                        wp_conn->row_count++;
                    }
                    argo_object_release(object);
                }
            }
            WALLY_FREE(pg_row_to_argo_row);

            /* If we reached row wally_gbl_cfg.wally_postgres_db_query_batch_size, we need to re-send
             * another request to get another wally_gbl_cfg.wally_postgres_db_query_batch_size rows or
             * so. */
            if (rows == wally_gbl_cfg.wally_postgres_db_query_batch_size) {
                if (is_poll) {
                    /* Warn if we see a huge number of rows get injected in the table in a short period of time (polling interval) */
                    /* Clear result (this batch is done), and tell caller to try this one more time for the next batch */
                    WALLY_LOG(AL_WARNING, "polling %s.%s returned more than %d rows. sequence=%"PRId64", count=%d",
                        t->db->name, t->db_table_name, wally_gbl_cfg.wally_postgres_db_query_batch_size, wp_conn->row_request_sequence, wp_conn->row_count);

                    PQclear(pres);
                    pres = NULL;
                    while ((pres = PQgetResult((PGconn *)(wp_conn->db_conn)))) {
                        PQclear(pres);
                        pres = NULL;
                    }
                    *read_more = 1;
                    return WALLY_RESULT_NO_ERROR;
                }

                /* Initial table read */
                /* Check if we really need to continue this request- verify we have an index matching this row. */
                struct wp_index *index = wp_conn->table->indexes[wp_conn->request_wp_index_index];
                if ((index->is_null && !index->is_null_registered)) {
                    /* Short circuit full table lookup if no one is asking for it. */
                } else {
#if 0
                    fprintf(stderr, "Asking for more than %d rows. (is_poll = %d, sequence=%ld, count=%ld)\n",
                            wally_gbl_cfg.wally_postgres_db_query_batch_size,
                            is_poll,
                            (long)wp_conn->row_request_sequence,
                            (long)wp_conn->row_count);
#endif // 0
                    PQclear(pres);
                    pres = NULL;
                    while ((pres = PQgetResult((PGconn *)(wp_conn->db_conn)))) {
                        PQclear(pres);
                        pres = NULL;
                    }
                    *read_more = 1;
                    return WALLY_RESULT_NO_ERROR;
                }
            }
        }
    }
    PQclear(pres);
    pres = NULL;

    if (is_poll) {
        t->last_read_complete_us = epoch_us();
        t->sequence_is_being_read = 0;
    }

    return ret_result;
}

/*
 * Incoming states: Any table state.
 *
 * Must hold write lock on call.
 *
 * we own wp_conn- i.e. we must free it on error. (and on success, for that matter...)
 *
 * This is only called when there are no DB requests outstanding, or
 * we are guaranteed to be able to get a complete result.
 */
int postgres_update_table_state_machine(struct wp_table *t, struct wp_connection *wp_conn)
{
    PGresult *pres[100] = {NULL};
    char buf[DEFAULT_DB_STRING_SIZE];
    char *s, *e;
    //const char *ctmp;
    int result_count = 0;
    int add_columns = 0;
    int i;
    int j;
    int result;
    int ret_result = WALLY_RESULT_NO_ERROR;
    enum wally_oper_events event = E_MAX;
    enum wally_oper_sub_events sub_event = SE_NONE;
    /* When the state machine runs, we often have data to process...
     * (but not always) */

    WALLY_DEBUG_POSTGRES("%s: %s: Entering state machine for table, currently in state %s", t->db->name, t->db_table_name, wp_table_state_names[t->state]);

    /* Consume input... */
    while (!PQisBusy((PGconn *)(wp_conn->db_conn))) {
        /* Can read a result. */
        if ((pres[result_count] = PQgetResult((PGconn *)(wp_conn->db_conn)))) {
            result_count++;
        } else {
            break;
        }

        if (result_count == 100) {
            /* Bad shit. I would expect result count to never be more
             * than 1. */
            WALLY_LOG(AL_ERROR, "DB %s, PQgetResult failed after several tries, table state %s name %s.", t->db->name, wp_table_state_names[t->state], t->db_table_name);
            event = E_TRANSACTION_FAIL;
            sub_event = SE_NONE;
            goto failure;
        }
    }

    /* Figure out result counts... to not have lots of repeated code */
    switch (t->state) {
    case table_ready:
    case table_hard_error:
    case table_resynchronize:
    case table_read_get_last_transcation_gvr:
    case table_read_highest_sequence_gvr_query_pending:
        break;
    default:
        if (result_count != 1) {
            WALLY_LOG(AL_ERROR, "DB %s, failure, result_count = %d, table state %s name %s", t->db->name, result_count, wp_table_state_names[t->state], t->db_table_name);
            event = E_TRANSACTION_FAIL;
            sub_event = SE_NONE;
            goto failure;
        }
        break;
    }

    switch (t->state) {
    case table_ready:
        /* Should just be row(s) arriving. */
        /* XXX IMPLEMENT ME */
        break;
    case table_hard_error:
        WALLY_LOG(AL_ERROR, "DB %s, failure, table state %s name %s hard error", t->db->name, wp_table_state_names[t->state], t->db_table_name);
        goto failure;
    case table_resynchronize:
        /* Table resynchronize- we shouldn't have any data arriving in
         * this case. (We were called, rather than callback) */
        if (result_count) {
            WALLY_LOG(AL_ERROR, "DB %s, failure, table state %s name %s resynchronize, result_count = %d", t->db->name, wp_table_state_names[t->state], t->db_table_name, result_count);
            event = E_TRANSACTION_FAIL;
            sub_event = SE_NONE;
            goto failure;
        }
        wp_bootup_update_table_stats(t, wp_bootup_table_total_start);
        WP_STATS_CONN_START(wp_conn);
        /* Issue a request to read whether the table exists. */
        result = wp_send_get_table((PGconn *)(wp_conn->db_conn), t->db_table_name, wp_conn->wp_db->schema_name);
        if (!result) {
            WALLY_LOG(AL_ERROR, "DB %s, request to read table exists failed state %s name %s", t->db->name, wp_table_state_names[t->state], t->db_table_name);
            goto failure;
        }
        t->state = table_read_table;
        ret_result = WALLY_RESULT_WOULD_BLOCK;
        break;

    case table_read_table:
        WP_STATS_CONN_STOP(wp_conn, t->db, t, query)
        /* Read whether the table exists in postgres. We should have a
         * 1 row result. */
        result = PQntuples(pres[0]);
        if (result == 0) {
            /* Table does not exist. */
            /* If the DB is writable, create the table */
            if (!t->db->is_alterable) {
                WALLY_LOG(AL_NOTICE, "Table %s does not exist, non-writable table", t->db_table_name);
                t->state = table_doesnt_exist;
                if (t->wally_db_table_bootup_stats_structure) {
                    argo_log_deregister_structure(t->wally_db_table_bootup_stats_structure, 1);
                    t->wally_db_table_bootup_stats_structure = NULL;
                }
                /* We return NO_ERROR here because we want to be able
                 * to process requests for other tables!! */
				if (IS_WALLY_LAYER_ENABLED) {
					wally_table_queue_enqueue_db_table_exists(t->db->db_to_wally_cookie, t->argo_object_name, false);
				} else {
					wally_xfer_update_table_exists(t->db->db_to_wally_cookie, t->argo_object_name, false, true);
				}
                ret_result = WALLY_RESULT_NO_ERROR;
            } else {
                WP_STATS_CONN_START(wp_conn);
                result = wp_send_create_table((PGconn *)(wp_conn->db_conn), t->db_table_name);
                if (!result) {
                    WALLY_LOG(AL_ERROR, "DB %s, request to create table failed state %s name %s", t->db->name, wp_table_state_names[t->state], t->db_table_name);
                    goto failure;
                }
                t->state = table_create_table;
                ret_result = WALLY_RESULT_WOULD_BLOCK;
            }
        } else if (result == 1) {
            /* WP stats need to be publish if table just exists (event if row does not exists). This will result in
             * leaf wally publishing stats and expected */
            {
                char name[WALLY_DB_STATS_NAME_SIZE] = {0};
                snprintf(name, sizeof(name), "wp_db_table_stats_%s_%s", t->db->name, t->db_table_name);
                WALLY_LOG(AL_NOTICE, "Registering for stats %s", name);
                struct wp_db_table_stats * wp_db_table_stats = WALLY_MALLOC(sizeof(struct wp_db_table_stats));
                argo_log_register_structure(wally_stats_log, name, AL_INFO,
                    60*1000*1000,    /* 1 minute */
                    wp_db_table_stats_description, wp_db_table_stats, 0,
                    fill_wp_db_table_stats, t);
            }
            /* Table does exist. */
			if (wp_conn->wp_db->is_true_origin) {
				/* wally postgres table stats provides information for postgress polling stats.
				 * As leaf wally does not poll postgres we can ignore this stats for leaf wally
				 */
				char name[MAX_TABLE_NAME_LEN] = {0};
				snprintf(name, sizeof(name), "wally_postgres_table_stats_%s_%s", t->db->name, t->db_table_name);
				t->wally_db_table_stats_structure = argo_log_register_structure(
											wally_stats_log, name, AL_INFO,
											60*1000*1000,    /* 1 minute */
											wally_db_pg_table_stats_description,
											&(t->wp_pg_table_stats), 0,
											fill_wally_postgres_table_stats, t);
			}

            WP_STATS_CONN_START(wp_conn);
			if (wally_multiple_index_consistency_support && !wp_conn->wp_db->is_true_origin) {
				WALLY_DEBUG_MIC("DB %s/%s Read lookup table", t->db->name,
						t->db_table_name);
				result = wp_send_read_lookup_table((PGconn *)(wp_conn->db_conn),
												t->db_table_name, wp_conn->wp_db->schema_name);
				if (!result) {
					WALLY_LOG(AL_ERROR, "DB %s/%s, lookup table read failed state= %s",
							t->db->name, t->db_table_name, wp_table_state_names[t->state]);
					goto failure;
				}
				t->state = table_read_lookup_table;
				ret_result = WALLY_RESULT_WOULD_BLOCK;
			} else {

				result = wp_send_get_table_columns((PGconn *)(wp_conn->db_conn),
						t->db_table_name, wp_conn->wp_db->schema_name);
				if (!result) {
					WALLY_LOG(AL_ERROR, "DB %s, request to get table columns failed state %s name %s",
							t->db->name, wp_table_state_names[t->state], t->db_table_name);
					goto failure;
				}
				t->state = table_read_columns;
				ret_result = WALLY_RESULT_WOULD_BLOCK;
			}
        } else {
            /* Table table bo-bable, bonanafana fo-fable, me mi mo mable, TABLE! */
            WALLY_LOG(AL_ERROR, "DB %s, table read unknown error state %s name %s, number of rows returned: %d", t->db->name, wp_table_state_names[t->state], t->db_table_name, result);
            event = E_TRANSACTION_FAIL;
            sub_event = SE_NONE;
            goto failure;
        }
        break;

    case table_create_table:
        WP_STATS_CONN_STOP(wp_conn, t->db, t, ddl)
        /* Should have a single result. */

        if (PQresultStatus(pres[0]) != PGRES_COMMAND_OK) {
            WALLY_LOG(AL_ERROR, "DB %s, create table cmd not ok state %s name %s", t->db->name, wp_table_state_names[t->state], t->db_table_name);
            goto failure;
        }
        WP_STATS_CONN_START(wp_conn);
		if (wally_multiple_index_consistency_support && !wp_conn->wp_db->is_true_origin) {
			WALLY_DEBUG_MIC("DB %s Read lookup table %s", t->db->name,
					t->db_table_name);
			result = wp_send_read_lookup_table((PGconn *)(wp_conn->db_conn),
											t->db_table_name, wp_conn->wp_db->schema_name);
			if (!result) {
				WALLY_LOG(AL_ERROR, "DB %s, Request to read lookup table failed state %s name %s",
						t->db->name, wp_table_state_names[t->state], t->db_table_name);
				goto failure;
			}
			t->state = table_read_lookup_table;
			ret_result = WALLY_RESULT_WOULD_BLOCK;
		} else {
			result = wp_send_get_table_columns((PGconn *)(wp_conn->db_conn), t->db_table_name, wp_conn->wp_db->schema_name);
			if (!result) {
				WALLY_LOG(AL_ERROR, "DB %s, request to get table columns failed state %s name %s", t->db->name, wp_table_state_names[t->state], t->db_table_name);
				goto failure;
			}
			t->state = table_read_columns;
			ret_result = WALLY_RESULT_WOULD_BLOCK;
		}
        break;

	case table_read_lookup_table:
        WP_STATS_CONN_STOP(wp_conn, t->db, t, query)
        result = PQntuples(pres[0]);
        WP_STATS_CONN_START(wp_conn);
        if (result == 1) {
			//lookup table exists dont create it
			t->lookup_table_exists = true;
			WALLY_DEBUG_MIC("DB %s  lookup table exists %s", t->db->name,
					t->db_table_name);
			result = wp_send_get_table_columns((PGconn *)(wp_conn->db_conn),
						t->db_table_name, wp_conn->wp_db->schema_name);
			if (!result) {
				WALLY_LOG(AL_ERROR, "DB %s, request to get table columns failed state %s name %s",
						t->db->name, wp_table_state_names[t->state], t->db_table_name);
				goto failure;
			}
			t->state = table_read_columns;
			ret_result = WALLY_RESULT_WOULD_BLOCK;
		} else if (result == 0) {
			WALLY_DEBUG_MIC("DB %s lookup table does not exists %s create it",
			                     t->db->name, t->db_table_name);
			//table does not exists create table
			result = wp_send_create_lookup_table((PGconn *)(wp_conn->db_conn),
					t->db_table_name);
			if (!result) {
				WALLY_LOG(AL_ERROR, "DB %s, request create lookup table failed state %s name %s",
						t->db->name, wp_table_state_names[t->state], t->db_table_name);
				goto failure;
			}
			t->state = table_create_lookup_table;
			ret_result = WALLY_RESULT_WOULD_BLOCK;
		} else {
            WALLY_LOG(AL_ERROR, "DB %s, lookup table read unknown error state %s name %s",
					t->db->name, wp_table_state_names[t->state], t->db_table_name);
            event = E_TRANSACTION_FAIL;
            sub_event = SE_NONE;
            goto failure;
		}
		break;
	case table_create_lookup_table:
        WP_STATS_CONN_STOP(wp_conn, t->db, t, query)
        result = PQntuples(pres[0]);
        WP_STATS_CONN_START(wp_conn);
		if (result == 1) {
			t->lookup_table_exists = true;
			WALLY_LOG(AL_ERROR, "DB %s, lookup table for table %s created successfully",
					t->db->name, t->db_table_name);
		}

		result = wp_send_get_table_columns((PGconn *)(wp_conn->db_conn),
				t->db_table_name, wp_conn->wp_db->schema_name);
		if (!result) {
			WALLY_LOG(AL_ERROR, "DB %s, request to get table columns failed state %s name %s",
					t->db->name, wp_table_state_names[t->state], t->db_table_name);
			goto failure;
		}
		t->state = table_read_columns;
		ret_result = WALLY_RESULT_WOULD_BLOCK;
	break;
    case table_read_columns:
        WP_STATS_CONN_STOP(wp_conn, t->db, t, query)
        /* For all the columns that come back, we need to see if we
         * need to add any to argo. And at the same time, we need to
         * remember which argo fields are not represented by
         * columns. The first is easy. The second a little trickier-
         * we need a scratchpad to note down which fields have been
         * seen. */
        argo_read_lock();
        t->argo_field_count = t->description->description_count;
        argo_unlock();
        if (!t->scratch) {
            t->scratch = WALLY_MALLOC(argo_library_get_max_descriptions());
        }
        if (!t->scratch) {
            WALLY_LOG(AL_ERROR, "DB %s, scratch pad malloc failed for table state %s name %s", t->db->name, wp_table_state_names[t->state], t->db_table_name);
            event = E_TERMINATE;
            sub_event = SE_MEM_FAIL;
            goto failure;
        }
        memset(t->scratch, 0, argo_library_get_max_descriptions());

        /* Iterate columns, checking them against argo. Note we still
         * have an argo lock here. */
        j = PQntuples(pres[0]);
        if (PQnfields(pres[0]) != 3) {
            /* Erk. */
            WALLY_LOG(AL_ERROR, "DB %s, PQnfields for table wrong state %s name %s", t->db->name, wp_table_state_names[t->state], t->db_table_name);
            event = E_SCHEMA_MISMATCH;
            sub_event = SE_NONE;
            goto failure;
        }
        for (i = 0; i < j; i++) {
            struct argo_private_field_description *fd;
            char *column_name, *data_type, *udt_name;
            enum argo_field_data_type argo_data_type;
            int is_array = 0;
            int is_double = 0;
            int is_inet = 0;

            column_name = PQgetvalue(pres[0], i, 0);
            data_type = PQgetvalue(pres[0], i, 1);
            udt_name = PQgetvalue(pres[0], i, 2);

            if (strcmp(data_type, "integer") == 0) argo_data_type = argo_field_data_type_integer;
            else if (strcmp(data_type, "bigint") == 0) argo_data_type = argo_field_data_type_integer;
            else if (strcmp(data_type, "smallint") == 0) argo_data_type = argo_field_data_type_integer;
            else if (strcmp(data_type, "boolean") == 0) argo_data_type = argo_field_data_type_integer;
            else if (strcmp(data_type, "text") == 0) argo_data_type = argo_field_data_type_string;
            else if (strcmp(data_type, "uuid") == 0) argo_data_type = argo_field_data_type_string;
            else if (strcmp(data_type, "inet") == 0) {
                argo_data_type = argo_field_data_type_binary;
                is_inet = 1;
            }
            else if (strcmp(data_type, "double precision") == 0) {
                argo_data_type = argo_field_data_type_binary;
                is_double = 1;
            } else if (strcmp(data_type, "ARRAY") == 0) {
                /* We only support arrays of integers at the moment. */
                if ((strcmp(udt_name, "_int2") == 0) ||
                    (strcmp(udt_name, "_int4") == 0) ||
                    (strcmp(udt_name, "_int8") == 0)) {
                    /* Note that we aren't setting this to an array type, here */
                    argo_data_type = argo_field_data_type_integer;
                    is_array = 1;
                } else if (strcmp(udt_name, "_text") == 0) {
                    argo_data_type = argo_field_data_type_string;
                    is_array = 1;
                } else if ((strcmp(udt_name, "_inet") == 0) ||
                           (strcmp(udt_name, "_float8") == 0)) {
                    argo_data_type = argo_field_data_type_binary;
                    if (strcmp(udt_name, "_float8") == 0) {
                        is_double = 1;
                    } else {
                        is_inet = 1;
                    }

                    is_array = 1;
                } else {
                    argo_data_type = argo_field_data_type_invalid;
                }
            } else {
                /* Unimplemented automatic recognition type. */
                argo_data_type = argo_field_data_type_invalid;
            }

            argo_read_lock();
            fd = zhash_table_lookup(t->description->described_fields, column_name, strlen(column_name), NULL);
            argo_unlock();
            if (fd) {
                /* Mark that we have seen this field- use our scratch for that. */
                t->scratch[fd->index] = 1;

                /* Verify we don't have a type mismatch. If
                 * we have a type mismatch, we abort, because there is
                 * not really anything we can do about it. */
                if ((fd->public_description.argo_field_type == argo_field_data_type_integer) &&
                    (argo_data_type == argo_field_data_type_binary) &&
                    (is_double)) {
                    /* Do nothing- we can read floats as ints */
                } else if ((fd->public_description.argo_field_type == argo_field_data_type_binary) &&
                           fd->public_description.is_double &&
                           (argo_data_type == argo_field_data_type_integer)) {
                    /* Do nothing- we can read ints as floats */
                } else {
                    if (fd->public_description.argo_field_type != argo_data_type) {
                        WALLY_LOG(AL_ERROR, "Failure: %d(%s) != %d(%s), %s",
                                  fd->public_description.argo_field_type,
                                  argo_field_data_type_to_str(fd->public_description.argo_field_type),
                                  argo_data_type,
                                  argo_field_data_type_to_str(argo_data_type),
                                  column_name);
                        event = E_SCHEMA_MISMATCH;
                        sub_event = SE_NONE;
                        goto failure;
                    }
                }
            } else {
                /* We need to add this column to argo. */
                if (argo_data_type != argo_field_data_type_invalid) {
                    WALLY_DEBUG_POSTGRES("Attempting to add field %s to table %s, type %d (%s), is_array=%d, is_double=%d, is_inet=%d",
                                         column_name, t->db_table_name, argo_data_type, argo_field_data_type_to_str(argo_data_type),
                                         is_array, is_double, is_inet);
                    result = argo_global_structure_add_field(t->description,
                                                             column_name,
                                                             strlen(column_name),
                                                             argo_data_type,
                                                             is_array,
                                                             is_double,
                                                             is_inet);
                    if (result) {
                        WALLY_LOG(AL_ERROR, "DB %s, Argo add field for table failed state %s name %s", t->db->name, wp_table_state_names[t->state], t->db_table_name);
                        event = E_TERMINATE;
                        sub_event = SE_ARGO_FAIL;
                        goto failure;
                    }
                } else {
                    WALLY_LOG(AL_ERROR, "Invalid data type attempting to add field %s to table %s", column_name, t->db_table_name);
                    /* Ignore unrecognized types for now- they simply
                     * don't get transported. i.e. if we need to move
                     * an array, it has to exist in the row structure,
                     * for now. */
                }
            }
        }

        /* Now that we have read the columns, we need to add any
         * columns argo knows about that we didn't read from the
         * database- if it's allowed for this table. Note that this
         * purposely skips over any columns that were added to
         * argo. */
        /* XXX Implement permissions. */
        if (t->db->is_alterable) {
            s = buf;
            e = buf + sizeof(buf);
            s += sxprintf(s, e, "ALTER TABLE %s", t->db_table_name);
            argo_read_lock();
            for (i = 0; i < t->argo_field_count; i++) {
                if (t->scratch[i]) {
                    struct argo_field_description *fd;
                    fd = &(t->description->description[i]->public_description);
                    if (fd->dont_db) {
                        if (!add_columns) {
                            s += sxprintf(s, e, " ");
                        } else {
                            s += sxprintf(s, e, ", ");
                        }
                        s += sxprintf(s, e, "DROP COLUMN %s",
                                      fd->field_name);
                        add_columns = 1;
                    }
                } else {
                    struct argo_field_description *fd;
                    fd = &(t->description->description[i]->public_description);
                    if (!fd->write_value) continue;
                    if (fd->dont_db) continue;
                    if (!add_columns) {
                        s += sxprintf(s, e, " ");
                    } else {
                        s += sxprintf(s, e, ", ");
                    }
                    add_columns = 1;
                    s += sxprintf(s, e, "ADD COLUMN %s ",
                                  fd->field_name);
                    switch (fd->argo_field_type) {
                    case argo_field_data_type_integer:
                        switch(fd->size) {
                        case 1:
                        case 2:
                            s += sxprintf(s, e, "smallint");
                            break;
                        case 4:
                            s += sxprintf(s, e, "integer");
                            break;
                        case 8:
                        default:
                            s += sxprintf(s, e, "bigint");
                            break;
                        }
                        if (fd->is_array) {
                            s += sxprintf(s, e, "[]");
                        }
                        break;
                    case argo_field_data_type_string:
                        s += sxprintf(s, e, "text");
                        if (fd->is_array) {
                            s += sxprintf(s, e, "[]");
                        }
                        break;
                    case argo_field_data_type_binary:
                        if (fd->is_inet) {
                            s += sxprintf(s, e, "inet");
                            if (fd->is_array) {
                                s += sxprintf(s, e, "[]");
                            }
                        } else if (fd->is_double) {
                            s += sxprintf(s, e, "double precision");
                            if (fd->is_array) {
                                s += sxprintf(s, e, "[]");
                            }
                        } else {
                            argo_unlock();
                            WALLY_LOG(AL_ERROR, "DB %s: %s: Failure: unsupported: binary or array, %s", t->db->name, t->db_table_name, fd->field_name);
                            event = E_TERMINATE;
                            sub_event = SE_ARGO_FAIL;
                            goto failure;
                        }
                        break;
                    default:
                        /* To do: Implement binary, in particular */
                        argo_unlock();
                        event = E_TERMINATE;
                        sub_event = SE_ARGO_FAIL;
                        WALLY_LOG(AL_ERROR, "DB %s, unknown argo field data type for table state %s name %s", t->db->name, wp_table_state_names[t->state], t->db_table_name);
                        goto failure;
                        /*break;*/
                    }
                }
            }
            argo_unlock();
        }

        /* DB and ARGO addition completed, update the field count */
        t->argo_field_count = t->description->description_count;

        if (add_columns) {
            /* Send add/drop columns request. */
            snprintf_nowarn(wp_conn->debug_str, sizeof(wp_conn->debug_str), "Postgres Command: <%s>", buf);
            WP_STATS_CONN_START(wp_conn);
            result = xPQsendQueryParams((PGconn *)(wp_conn->db_conn),
                                       buf,
                                       0,
                                       NULL,
                                       NULL,
                                       NULL,
                                       NULL,
                                       0);
            if (!result) {
                WALLY_LOG(AL_ERROR, "DB %s, xPQsendQueryParams for table failed state %s name %s", t->db->name, wp_table_state_names[t->state], t->db_table_name);
                goto failure;
            }
            t->state = table_add_columns;
            ret_result = WALLY_RESULT_WOULD_BLOCK;
        } else {
            WP_STATS_CONN_START(wp_conn);
            /* Send read indexes request. */
            result = wp_send_read_indexes((PGconn *)(wp_conn->db_conn), t->db_table_name);
            if (!result) {
                WALLY_LOG(AL_ERROR, "DB %s, read indexes for table failed state %s name %s", t->db->name, wp_table_state_names[t->state], t->db_table_name);
                goto failure;
            }
            t->state = table_read_indexes;
            ret_result = WALLY_RESULT_WOULD_BLOCK;
        }

        break;
    case table_add_columns:
        WP_STATS_CONN_STOP(wp_conn, t->db, t, ddl)
        if (PQresultStatus(pres[0]) != PGRES_COMMAND_OK) {
            WALLY_LOG(AL_ERROR, "DB %s, command not ok for table state %s name %s", t->db->name, wp_table_state_names[t->state], t->db_table_name);
            goto failure;
        }
        WP_STATS_CONN_START(wp_conn);
        result = wp_send_read_indexes((PGconn *)(wp_conn->db_conn), t->db_table_name);
        if (!result) {
            WALLY_LOG(AL_ERROR, "DB %s, cannot read indexes for table state %s name %s", t->db->name, wp_table_state_names[t->state], t->db_table_name);
            goto failure;
        }
        t->state = table_read_indexes;
        ret_result = WALLY_RESULT_WOULD_BLOCK;
        break;
    case table_read_indexes:
        WP_STATS_CONN_STOP(wp_conn, t->db, t, query)
        /* We only enforce indexes in one direction- If argo thinks a
         * field must be indexed, then indexed it must be.*/

        /* Make a little scratchpad for finding which indexes are missing. */
        argo_read_lock();

        {
            struct argo_field_description *sequence_field = NULL;
            struct argo_field_description *fd;
            size_t di; /* description index */
            int ti; /* tuple index */
            int tc = PQntuples(pres[0]);
            char *index_name, *index_field;
            /* find sequence field. */
            for (di = 0; di < t->description->description_count; di++) {
                fd = &(t->description->description[di]->public_description);
                if (fd->is_index) {
                    /* Idempotent call: */
                    result = wp_add_index_column(t, fd->field_name);
                    if (result) {
                        argo_unlock();
                        WALLY_LOG(AL_ERROR, "WP: Error: Could not add index tracking column %s to table %s",
                                  fd->field_name, t->db_table_name);
                        goto failure;
                    } else {
                        /* Success. */
                    }
                }
                if (fd->is_sequence) {
                    sequence_field = fd;
                }
            }
            if (!sequence_field) {
                /* ERK */
                argo_unlock();
                WALLY_LOG(AL_ERROR, "DB %s, sequence field not found for table state %s name %s", t->db->name, wp_table_state_names[t->state], t->db_table_name);
                event = E_SCHEMA_MISMATCH;
                sub_event = SE_NONE;
                goto failure;
            }

            if (t->db->is_alterable) {
                /* Search for a missing index... */
                for (di = 0; di < t->description->description_count; di++) {
                    struct argo_field_description *fd = &(t->description->description[di]->public_description);

                    if ((fd->is_sequence) ||
                        (fd->is_key) ||
                        ((fd->is_index) && (fd->is_unique))) {
                        /* Check for single-index on this field. */
                        for (ti = 0; ti < tc; ti++) {
                            index_name = PQgetvalue(pres[0], ti, 0);
                            index_field = PQgetvalue(pres[0], ti, 1);
                            if ((strcmp(index_field, fd->field_name) == 0) &&
                                ((ti == 0) || (strcmp(index_name, PQgetvalue(pres[0], ti - 1, 0)))) &&
                                ((ti == (tc - 1)) || (strcmp(index_name, PQgetvalue(pres[0], ti + 1, 0))))) {
                                /* We have such an index. */
                                // WALLY_DEBUG_POSTGRES("FOUND: single-index for field <%s> (%s)", fd->field_name, index_name);
                                break;
                            }
                        }
                        if (ti == tc) {
                            /* did not find sequence, must add it. */
                            const char *params[2];
                            // WALLY_DEBUG_POSTGRES("Should add single-index for field <%s>", fd->field_name);
                            params[0] = t->db_table_name;
                            params[1] = fd->field_name;

                            s = buf;
                            e = buf + sizeof(buf);

                            if (fd->is_key) {
                                s += sxprintf(s, e, "CREATE UNIQUE INDEX ON %s ( %s )", params[0], params[1]);
                            } else {
                                s += sxprintf(s, e, "CREATE INDEX ON %s ( %s )", params[0], params[1]);
                            }

                            WP_STATS_CONN_START(wp_conn);
                            argo_unlock();
                            result = xPQsendQueryParams((PGconn *)(wp_conn->db_conn),
                                                       buf,
                                                       0,
                                                       NULL,
                                                       NULL,
                                                       NULL,
                                                       NULL,
                                                       0);
                            argo_read_lock();
                            if (!result) {
                                argo_unlock();
                                WALLY_LOG(AL_ERROR, "DB %s, Could not send query for table state %s name %s", t->db->name, wp_table_state_names[t->state], t->db_table_name);
                                goto failure;
                            }
                            t->state = table_add_indexes;
                            ret_result = WALLY_RESULT_WOULD_BLOCK;
                            //fprintf(stderr, "Perform SQL: %s", buf);
                            //WALLY_DEBUG_POSTGRES("Perform SQL: %s", buf);
                            break;
                        }
                    } else if (t->description->description[di]->public_description.is_index) {
                        /* Check for double-index on this field. */
                        for (ti = 0; ti < tc; ti++) {
                            index_name = PQgetvalue(pres[0], ti, 0);
                            index_field = PQgetvalue(pres[0], ti, 1);
                            if ((strcmp(index_field, fd->field_name) == 0) &&  /* Index on our field. */
                                (((ti >= 1) &&  /* With 'sequence' coming immediately before... OR */
                                  (strcmp(index_name, PQgetvalue(pres[0], ti - 1, 0)) == 0) &&
                                  (strcmp(sequence_field->field_name, PQgetvalue(pres[0], ti - 1, 1)) == 0)) ||
                                 ((ti < tc - 1) &&  /* after. */
                                  (strcmp(index_name, PQgetvalue(pres[0], ti + 1, 0)) == 0) &&
                                  (strcmp(sequence_field->field_name, PQgetvalue(pres[0], ti + 1, 1)) == 0)))) {
                                /* We have such an index. */
                                //WALLY_DEBUG_POSTGRES("FOUND: double-index for field <%s> (%s)", fd->field_name, index_name);
                                break;
                            }
                        }
                        if (ti == tc) {
                            /* did not find sequence, must add it. */
                            const char *params[3];
                            //WALLY_DEBUG_POSTGRES("Should add double-index for field <%s>", fd->field_name);
                            params[0] = t->db_table_name;
                            params[1] = fd->field_name;
                            params[2] = sequence_field->field_name;

                            s = buf;
                            e = buf + sizeof(buf);
                            s += sxprintf(s, e, "CREATE INDEX ON %s ( %s, %s )", params[0], params[1], params[2]);
                            WP_STATS_CONN_START(wp_conn);

                            argo_unlock();
                            result = xPQsendQueryParams((PGconn *)(wp_conn->db_conn),
                                                       buf,
                                                       0,
                                                       NULL,
                                                       NULL,
                                                       NULL,
                                                       NULL,
                                                       0);
                            argo_read_lock();
                            if (!result) {
                                argo_unlock();
                                WALLY_LOG(AL_ERROR, "DB %s, Could not send query for table state %s name %s", t->db->name, wp_table_state_names[t->state], t->db_table_name);
                                goto failure;
                            }
                            t->state = table_add_indexes;
                            ret_result = WALLY_RESULT_WOULD_BLOCK;
                            //WALLY_DEBUG_POSTGRES("Perform SQL: %s", buf);
                            break;
                        }
                    }
                }
            }
        }

        argo_unlock();
        if (t->state != table_add_indexes) {
            if (oper_mode_g.is_active_done &&
                    wp_conn->wp_db->is_true_origin && !wp_conn->wp_db->is_alterable) {
                /* table_resynchronize called when system is active. No need to read
                 * the highest sequence number as the polling takes care of reading it
                 * Execute this section only on Origin-wally's RDS table */
                t->state = table_ready;
                t->needs_to_be_resynchronized = 0;
                ret_result = WALLY_RESULT_NO_ERROR;
                WALLY_LOG(AL_NOTICE, "DB %s, Table rsync completed. state %s name %s",
                        t->db->name, wp_table_state_names[t->state], t->db_table_name);
                break;
            }
            WP_STATS_CONN_START(wp_conn);
            /* If we got here, we didn't send an add-index request. */
            /* Read highest sequence next */
            if(WALLY_DB_GVR_MODE_DISABLED ||  !(t->db->is_true_origin && !t->db->is_alterable)) {
                result = wp_send_read_sequence_begin((PGconn *)(wp_conn->db_conn));
                if (!result) {
                    WALLY_LOG(AL_ERROR, "DB %s, send read seq for table failed state %s name %s", t->db->name, wp_table_state_names[t->state], t->db_table_name);
                    goto failure;
                }
                t->state = table_read_highest_sequence_begin;
            } else {
                   t->last_gvr_request_time_us = epoch_us();
                   result = wp_send_read_max_sequence_gvr((PGconn *)(wp_conn->db_conn), t->db_table_name, t->sequence_column_name, 0);
                   if (!result) {
                      WALLY_LOG(AL_ERROR, "DB %s, send read seq for table failed state %s name %s", t->db->name, wp_table_state_names[t->state], t->db_table_name);
                       goto failure;
                   } else {
                       wp_bootup_update_table_stats(t, wp_bootup_table_nolock_start);
                   }
                t->state = table_read_highest_sequence_gvr;
            }
            ret_result = WALLY_RESULT_WOULD_BLOCK;
        }

        break;
    case table_add_indexes:
        WP_STATS_CONN_STOP(wp_conn, t->db, t, ddl)
        if (PQresultStatus(pres[0]) != PGRES_COMMAND_OK) {
            WALLY_LOG(AL_ERROR, "DB %s, Cmd not ok for table state %s name %s", t->db->name, wp_table_state_names[t->state], t->db_table_name);
            goto failure;
        }
        WP_STATS_CONN_START(wp_conn);
        /* We re-read indexes, so that we can re-add more indexes if
         * they are needed. */
        result = wp_send_read_indexes((PGconn *)(wp_conn->db_conn), t->db_table_name);
        if (!result) {
            WALLY_LOG(AL_ERROR, "DB %s, send read indexes for table failed state %s name %s", t->db->name, wp_table_state_names[t->state], t->db_table_name);
            goto failure;
        }
        t->state = table_read_indexes;
        ret_result = WALLY_RESULT_WOULD_BLOCK;
        break;

        /* NOTE: table_add_notification is no longer part of the state
         * machine. The code for processing it has been left here,
         * however. */
    case table_add_notification:
        if (PQresultStatus(pres[0]) != PGRES_COMMAND_OK) {
            WALLY_LOG(AL_ERROR, "DB %s, cmd not ok for table state %s name %s", t->db->name, wp_table_state_names[t->state], t->db_table_name);
            goto failure;
        }

        WP_STATS_CONN_START(wp_conn);
        result = wp_send_read_sequence_begin((PGconn *)(wp_conn->db_conn));
        if (!result) {
            WALLY_LOG(AL_ERROR, "DB %s, send read seq begin for table failed state %s name %s", t->db->name, wp_table_state_names[t->state], t->db_table_name);
            goto failure;
        }
        t->state = table_read_highest_sequence_begin;
        ret_result = WALLY_RESULT_WOULD_BLOCK;
        break;

    case table_read_highest_sequence_begin:
        WP_STATS_CONN_STOP(wp_conn, t->db, t, tcl)
        if (PQresultStatus(pres[0]) != PGRES_COMMAND_OK) {
            WALLY_LOG(AL_ERROR, "DB %s, cmd not ok for table state %s name %s", t->db->name, wp_table_state_names[t->state], t->db_table_name);
            goto failure;
        }
        WP_STATS_CONN_START(wp_conn);
        result = wp_send_read_sequence_lock((PGconn *)(wp_conn->db_conn), t->db_table_name);
        if (!result) {
            WALLY_LOG(AL_ERROR, "DB %s, send read seq lock for table failed state %s name %s", t->db->name, wp_table_state_names[t->state], t->db_table_name);
            goto failure;
        } else {
            /* Table lock request for reading highest sequence is successful, set start time */
            wp_bootup_update_table_stats(t, wp_bootup_table_seqlock_start);
        }
        t->state = table_read_highest_sequence_lock;
        ret_result = WALLY_RESULT_WOULD_BLOCK;
        break;

    case table_read_highest_sequence_gvr:
        /* Check if we have a row returned. */
        j = PQntuples(pres[0]);
        if (j) {
            if (j > 1) {
                WALLY_LOG(AL_ERROR, "DB %s, Error PQntuples for table state %s name %s", t->db->name, wp_table_state_names[t->state], t->db_table_name);
                event = E_SCHEMA_MISMATCH;
                sub_event = SE_NONE;
                goto failure;
            }
            char *tmp_str[2]= {NULL};
            tmp_str[0] = PQgetvalue(pres[0], 0, 0);
            tmp_str[1] = PQgetvalue(pres[0], 0, 1);
            if(tmp_str[0] != NULL) {
                t->current_max_sequence  =  strtoll(tmp_str[0], NULL, 10);
            } else {
                t->current_max_sequence = 0;
            }
            if(tmp_str[1] != NULL) {
                t->poll_lock_count = strtoll(tmp_str[1], NULL, 10);
            } else {
                t->poll_lock_count = 0;
            }
        } else {
            t->current_max_sequence = 0;
            t->poll_lock_count = 0;
        }

       /*
        *1. If current_max_sequence is -1, it indicates an exclusive lock.
        *2. If poll_lock_count is non-zero, it means a transaction is still pending.
        *3. If neither of the above conditions is true, the query will succeed.
        */

        if(t->current_max_sequence == -1) {
           t->state = table_read_highest_sequence_gvr_query_pending;
           wp_bootup_update_table_stats(t, wp_bootup_table_excl_lock_start);
        } else if(t->poll_lock_count != 0) {
           t->state = table_read_get_last_transcation_gvr;
           wp_bootup_update_table_stats(t, wp_bootup_table_txn_pending_start);
           WALLY_DEBUG_POSTGRES("Transaction is pending table %s with poll_lock_count %"PRId64" ",t->db_table_name,t->poll_lock_count);
        } else {
           t->state = table_ready;
           t->needs_to_be_resynchronized = 0;
           wp_bootup_update_table_stats(t, wp_bootup_table_nolock_end);
           WALLY_DEBUG_POSTGRES("  Table is ready Max sequence = %ld for table %s", (long) t->current_max_sequence,t->db_table_name);
        }
        ret_result = WALLY_RESULT_NO_ERROR ;
        break;

    case table_read_highest_sequence_gvr_query_pending:
        t->last_gvr_request_time_us = epoch_us();
        result = wp_send_read_max_sequence_gvr((PGconn *)(wp_conn->db_conn), t->db_table_name, t->sequence_column_name,0);
        if (!result) {
            WALLY_LOG(AL_ERROR, "DB %s, send read seq select for table failed state %s name %s", t->db->name, wp_table_state_names[t->state], t->db_table_name);
            goto failure;
        } else {
            wp_bootup_update_table_stats(t, wp_bootup_excl_lock_counter);
        }
        t->state = table_read_highest_sequence_gvr;
        ret_result = WALLY_RESULT_WOULD_BLOCK;
        break;


    case table_read_get_last_transcation_gvr:
        t->last_gvr_request_time_us = epoch_us();
        result = wp_send_select_last_transaction_gvr_status((PGconn *)(wp_conn->db_conn), t->db_table_name, t->sequence_column_name);
        if (!result) {
            WALLY_LOG(AL_ERROR, "DB %s, send read seq select for table failed state %s name %s", t->db->name, wp_table_state_names[t->state], t->db_table_name);
            goto failure;
        } else {
            wp_bootup_update_table_stats(t, wp_bootup_txn_pending_counter);
        }
        t->state = table_read_get_last_transcation_gvr_query_pending;
        ret_result = WALLY_RESULT_WOULD_BLOCK;
        break;

    case table_read_get_last_transcation_gvr_query_pending:
        j = PQntuples(pres[0]);
        if (j) {
            if (j > 1) {
                WALLY_LOG(AL_ERROR, "DB %s, Error PQntuples for table state %s name %s", t->db->name, wp_table_state_names[t->state], t->db_table_name);
                event = E_SCHEMA_MISMATCH;
                sub_event = SE_NONE;
                goto failure;
            }
        }

        char *tmp_str;
        int64_t new_row_count = -1;
        tmp_str = (char *)PQgetvalue(pres[0], 0, 1);
        if (tmp_str)
        {
            new_row_count = strtoul(tmp_str, NULL, 0);
        } else {
            new_row_count = 0;
        }
        if (new_row_count == 0) {
           t->poll_lock_count = 0;
           t->state = table_ready;
           t->needs_to_be_resynchronized = 0;
           wp_bootup_update_table_stats(t, wp_bootup_table_nolock_end);
           WALLY_DEBUG_POSTGRES("Table is ready Max sequence = %ld for table %s", (long) t->current_max_sequence,t->db_table_name);
        } else {
            t->state = table_read_get_last_transcation_gvr;
        }
        ret_result = WALLY_RESULT_NO_ERROR;
        break;


    case table_read_highest_sequence_lock:
        WP_STATS_CONN_STOP(wp_conn, t->db, t, lock)
        if (PQresultStatus(pres[0]) != PGRES_COMMAND_OK) {
            WALLY_LOG(AL_ERROR, "DB %s, cmd not ok for table state %s name %s", t->db->name, wp_table_state_names[t->state], t->db_table_name);
            goto failure;
        }
        wp_bootup_update_table_stats(t, wp_bootup_table_num_queries);
        WP_STATS_CONN_START(wp_conn);
        result = wp_send_read_sequence_select((PGconn *)(wp_conn->db_conn), t->db_table_name, t->sequence_column_name);
        if (!result) {
            WALLY_LOG(AL_ERROR, "DB %s, send read seq select for table failed state %s name %s", t->db->name, wp_table_state_names[t->state], t->db_table_name);
            goto failure;
        } else {
            wp_bootup_update_table_stats(t, wp_bootup_table_query_start);
        }
        t->state = table_read_highest_sequence_select;
        ret_result = WALLY_RESULT_WOULD_BLOCK;
        break;

    case table_read_highest_sequence_select:
        WP_STATS_CONN_STOP(wp_conn, t->db, t, query)
        wp_bootup_update_table_stats(t, wp_bootup_table_query_end);
        /* Check if we have a row returned. */
        j = PQntuples(pres[0]);
        if (j) {
            if (j > 1) {
                WALLY_LOG(AL_ERROR, "DB %s, Error PQntuples for table state %s name %s", t->db->name, wp_table_state_names[t->state], t->db_table_name);
                event = E_SCHEMA_MISMATCH;
                sub_event = SE_NONE;
                goto failure;
            }
            t->current_max_sequence = strtoll(PQgetvalue(pres[0], 0, 0), NULL, 10);
            WALLY_DEBUG_POSTGRES("Max sequence = %ld", (long) t->current_max_sequence);
        } else {
            t->current_max_sequence = 0;
        }
        WP_STATS_CONN_START(wp_conn);
        result = wp_send_read_sequence_commit((PGconn *)(wp_conn->db_conn));
        if (!result) {
            WALLY_LOG(AL_ERROR, "DB %s, send read seq commit for table failed state %s name %s", t->db->name, wp_table_state_names[t->state], t->db_table_name);
            goto failure;
        }
        t->needs_to_be_resynchronized = 0;
        t->state = table_read_highest_sequence_commit;
        ret_result = WALLY_RESULT_WOULD_BLOCK;
        break;

    case table_read_highest_sequence_commit:
        WP_STATS_CONN_STOP(wp_conn, t->db, t, tcl)
        if (PQresultStatus(pres[0]) != PGRES_COMMAND_OK) {
            WALLY_LOG(AL_ERROR, "DB %s, cmd not ok for table state %s name %s", t->db->name, wp_table_state_names[t->state], t->db_table_name);
            goto failure;
        }
        wp_bootup_update_table_stats(t, wp_bootup_table_seqlock_end);
        wp_bootup_update_table_stats(t, wp_bootup_table_total_end);
        /* deregister stats to stop reporting after phase-1 of table read */
        /* Tables are read in two phases 1. table definition and sequence read 2. table rows read
         * bootup stats are reported in both phases.
         * Stats report started and stopped in phase 1 after reading highest sequence number.
         * Stats report again started on reading rows and stopped after reading all rows for the table.
         * In fully_loaded mode, Stats reported in both phases. In on_demand mode, stats reported only
         * in phase-1(no phase-2 for on_demand mode).
         */
        if (t->wally_db_table_bootup_stats_structure)
        {
            argo_log_deregister_structure(t->wally_db_table_bootup_stats_structure, 1);
            t->wally_db_table_bootup_stats_structure = NULL;
        }
        t->state = table_ready;
        t->needs_to_be_resynchronized = 0;
        ret_result = WALLY_RESULT_NO_ERROR;
        break;

    default :
        /* Huh? */
        WALLY_LOG(AL_ERROR, "DB %s Bad state for table %s", t->db->name, t->db_table_name);
        return WALLY_RESULT_BAD_STATE;
    }

    /* Free query results. */
    if (pres[0]) {
        PQclear(pres[0]);
        pres[0] = NULL;
    }
    WALLY_DEBUG_POSTGRES("%s: %s: Leaving  state machine for table, currently in state %s", t->db->name, t->db_table_name, wp_table_state_names[t->state]);
    return ret_result;

 failure:

    WALLY_LOG(AL_CRITICAL, "%s: %s: Could not process data to/from table, currently in state %s.", t->db->name, t->db_table_name, wp_table_state_names[t->state]);

    wally_error_handler(pres[0], wp_conn, true,
            event, sub_event);
#if 0
    t->state = table_hard_error;
    WALLY_DEBUG_POSTGRES("Leaving  state machine for table <%s>, currently in state %s", t->db_table_name, wp_table_state_names[t->state]);
#endif
    return WALLY_RESULT_ERR;
}

/* Origin-disconnect is executed. Reset all the RDS DB connections */
void wp_origin_disconnect (struct wp_db *wp_db)
{
    struct wp_connection *wp_conn = NULL;
    int32_t i = 0;

    /* No need to disconnect for DB connenction when Manual Origin-disconnected is active
     * or for local DB*/
    if (wp_db == NULL ||
            !wally_origin_disc_manual || !wp_db->is_true_origin || wp_db->is_alterable) {
        return;
    }

    /* All the DB connections are down already. skip the check */
    if (wp_db->connections_count == wp_db->failed_connections_count) {
        return ;
    }

    wp_conn = TAILQ_FIRST(&(wp_db->free_connections));
    while (wp_conn) {
        /* Free the connections in the free_connections list and then
         * wp_db->connections*/
        TAILQ_REMOVE(&(wp_db->free_connections), wp_conn, free_connections);
        WALLY_DEBUG_POSTGRES_FC("Origin-disconnect. Disable DB connection (wp = %s)",
                wp_db->name);

        wp_free_db_conn(wp_conn, wp_db, true);

        wp_conn = TAILQ_FIRST(&(wp_db->free_connections));
    }

    for (i = 0; i < wp_db->connections_count; i++) {
        if (wp_db->connections[i].db_conn) {
            WALLY_DEBUG_POSTGRES_FC("Origin-disconnect. Disable DB connection (wp = %s, index = %d)",
                    wp_db->name, i);

            wp_free_db_conn(&wp_db->connections[i], wp_db, true);

        }
    }
    return ;

}

/* DB recover function.
 *
 * Recover the DB connections if it is failed */
void wp_recovery_db_conn(struct wp_db *wp_db)
{
    int i = 0;

    if (!wp_db) {
        return;
    }

    if (wally_origin_disc_manual) {
        /* Should not recover if origin-disconnect command is active */
        return;
    }
    /* Few connections are failed */
    if (wp_db->failed_connections_count == 0) {
        return;
    }

    ZPATH_RWLOCK_WRLOCK(&(wp_db->lock), __FILE__, __LINE__);
    for (i = 0; i < wp_db->connections_count; i++) {
        if (wp_db->connections[i].state == conn_failed) {
            wally_postgres_reconnect (i, wp_db);
        }
    }
    ZPATH_RWLOCK_UNLOCK(&(wp_db->lock), __FILE__, __LINE__);
    return;
}
/*
 * Get the status of this origin DB. Basically checks whether or not
 * the DB connections are okay.
 */
void wp_get_status(void *callout_cookie)
{
    int i = 0;
    struct wp_db *wp_db = (struct wp_db *) callout_cookie;

    if (wally_origin_disc_manual && wp_db->is_true_origin && !wp_db->is_alterable)  {
        if (wp_db->connections_count == wp_db->failed_connections_count) {
            /* No connection to RDS is active. Return */
            return ;
        }
        /* Origin-disconnect is requested. Reset all the RDS connections */
        ZPATH_RWLOCK_WRLOCK(&(wp_db->lock), __FILE__, __LINE__);
        wp_origin_disconnect(wp_db);
        ZPATH_RWLOCK_UNLOCK(&(wp_db->lock), __FILE__, __LINE__);
        return ;
    }

    if (wp_db) {
        /* All of these calls are may be thread-safe (because it doesn't
         * change after intialization), but we'll lock anyway. */
        ZPATH_RWLOCK_RDLOCK(&(wp_db->lock), __FILE__, __LINE__);
        for (i = 0; i < wp_db->connections_count; i++) {
            if ((wp_db->connections[i].db_conn == NULL) ||
                (wp_db->connections[i].state == conn_failed)) {
                continue;
            }
            ConnStatusType dbConnStatus = PQstatus((PGconn *)(wp_db->connections[i].db_conn));
            if (dbConnStatus == CONNECTION_OK) continue;
            if (!wally_origin_disc_manual) {
                WALLY_LOG(AL_CRITICAL, "Connection idx %d, name %s, status error %s.",
                        i, wp_db->connections[i].name, PQerrorMessage((PGconn*)(wp_db->connections[i].db_conn)));
                wp_db->connections[i].state = conn_failed;
                wp_db->failed_connections_count++;
                wally_oper_event_post(E_DB_FAIL, SE_DB_REMOTE, wp_db);
            }
        }
        ZPATH_RWLOCK_UNLOCK(&(wp_db->lock), __FILE__, __LINE__);
    }
    return;
}

/*
 * Write field out in format appropriate for writing to postgres.
 */
int wp_fill_field_for_write(char **sos,
                            char *ee,
                            char **value,
                            struct argo_structure_description *argo_desc,
                            struct argo_field_description *pd,
                            void *struct_data)
{
    uint8_t *src_addr;
    void *src_addr_void;
    int array_count;
    int array_index;
    size_t src_size;
    char *ss = *sos;
    int res;

    /* Now, generate the values, if necessary. */
    /* XXX Implement binaries and arrays of binaries! */
    array_count = argo_structure_field_get_array_count_by_description(argo_desc, pd, struct_data);

    *value = ss;

    switch(pd->argo_field_type) {
    case argo_field_data_type_integer:
        /* Integer fields need to be written to auxiliary string... */
        if (pd->is_array) {
            ss += sxprintf(ss, ee, "{");
        }
        for (array_index = 0; array_index < array_count; array_index++) {
            res = argo_structure_read_field_by_description(argo_desc,
                                                           pd,
                                                           struct_data,
                                                           array_index,
                                                           &src_addr_void,
                                                           &src_size,
                                                           NULL);
            src_addr = src_addr_void;
            if (res) {
                WALLY_LOG(AL_ERROR, "Could not set up to read (%s)", pd->field_name);
            } else {
                if (array_index) {
                    ss += sxprintf(ss, ee, ",%ld", (long)argo_read_int(src_addr, src_size));
                } else {
                    ss += sxprintf(ss, ee, "%ld", (long)argo_read_int(src_addr, src_size));
                }
            }
            src_addr += pd->size;
        }
        if (pd->is_array) {
            ss += sxprintf(ss, ee, "}");
        }
        if (ss < ee) ss++;
        break;
    case argo_field_data_type_string:
        if (pd->is_array) {
            ss += sxprintf(ss, ee, "{");
        }
        for (array_index = 0; array_index < array_count; array_index++) {
            if (array_index != 0) ss += sxprintf(ss, ee, ",");
            res = argo_structure_read_field_by_description(argo_desc,
                                                           pd,
                                                           struct_data,
                                                           array_index,
                                                           &src_addr_void,
                                                           &src_size,
                                                           NULL);
            src_addr = src_addr_void;
            if (res) {
                WALLY_LOG(AL_ERROR, "Could not set up to read (%s)", pd->field_name);
                return res;
            } else {
                if (!src_addr) src_addr = (uint8_t *)"";
                if (pd->is_array) {
                    res = argo_postgres_string_generate(&ss, ee - ss, (char *)src_addr);
                    if(res == ARGO_RESULT_ERR_TOO_LARGE) {
                       return res;
                    }
                } else {
                    ss += sxprintf(ss, ee, "%s", src_addr);
                }
            }
        }
        if (pd->is_array) {
            ss += sxprintf(ss, ee, "}");
        }
        if (ss < ee) ss++;
        /* fprintf(stderr, "Writing string array (isarray = %d), (name = %s), (count = %d)  = <%s>\n",
         *  pd->is_array, pd->field_name, array_count, row_values[value_index]); */
        break;
    case argo_field_data_type_binary:
        if (pd->is_inet || pd->is_double) {
            int res;
            struct argo_inet *ip;

            if (pd->is_array) {
                ss += sxprintf(ss, ee, "{");
            }

            for (array_index = 0; array_index < array_count; array_index++) {
                if (array_index != 0) ss += sxprintf(ss, ee, ",");

                res = argo_structure_read_field_by_description(argo_desc,
                                                               pd,
                                                               struct_data,
                                                               array_index,
                                                               &src_addr_void,
                                                               &src_size,
                                                               NULL);
                src_addr = src_addr_void;
                if (res) {
                    WALLY_LOG(AL_ERROR, "Could not set up to read (%s)", pd->field_name);
                    return res;
                } else {
                    if (pd->is_inet) {
                        ip = (struct argo_inet *) src_addr;
                        if (ip->length == 0) {
                            *value = NULL;
                        } else {
                            res = argo_json_inet_generate(&ss, ee - ss, (struct argo_inet *) src_addr, 0);
                            if (res) {
                                /* XXX LOG */
                                WALLY_LOG(AL_ERROR, "Could not generate inet string (%s)", pd->field_name);
                            }
                        }
                    } else if (pd->is_double) {
                        double *d = (double *) src_addr;
                        if (!d && pd->is_reference) {
                            /* ptr reference that is NULL. Just write 0. */
                            ss +=sxprintf(ss, ee, "0");
                        } else {
                            if (src_size != sizeof(double)) {
                                WALLY_LOG(AL_ERROR, "Not size of double: %d (%s)", (int)src_size, pd->field_name);
                            } else {
                                ss += sxprintf(ss, ee, "%.9g", (d?*d:0));
                            }
                        }
                    }
                }
            }
            if (pd->is_array) {
                ss += sxprintf(ss, ee, "}");
            }
            if (ss < ee) ss++;
        } else {
            /* XXX LOG */
            WALLY_LOG_NOT_IMPLEMENTED();
        }
        break;
    default:
        WALLY_LOG_NOT_IMPLEMENTED();
        break;
    }
    *sos = ss;
    if (ss >= ee) return ARGO_RESULT_ERR_TOO_LARGE;
    return ARGO_RESULT_NO_ERROR;
}

/*
 * Check if table is ready for update
 */
static int wp_table_validate_state(struct wp_connection *wp_conn, char *table_name)
{
	int result = WALLY_RESULT_NO_ERROR;
	struct wp_table* wpt = NULL;

    argo_read_lock();

    wpt = argo_hash_lookup(wp_conn->wp_db->tables_by_argo_name, table_name, strlen(table_name), NULL);
    if (!wpt)  {
        WALLY_LOG(AL_ERROR, "%s: Row to write, table %s non-existent", wp_conn->wp_db->name, table_name);
		result = WALLY_RESULT_BAD_ARGUMENT;
    }

	if (!result) {
		wp_conn->table = wpt;

		/* XXX Check whether synchronization is required. (or allowed) (or in progress) */
		if (wpt->state != table_ready || wpt->needs_to_be_resynchronized) {
			result = WALLY_RESULT_WOULD_BLOCK;
		}
	}

	argo_unlock();

	return result;
}

int wp_write_object_lookup_table_upsert(struct wp_connection *wp_conn, int wp_indx)
{
	int result = WALLY_RESULT_NO_ERROR;
    struct wp_conn_pg_specific* pgs = wp_conn->cookie;
	struct  wally_local_db_write_queue_object *queue_object = pgs->write_objects[0];
	struct wp_table* wpt = NULL;
	char *table_name = queue_object->data.lookup_table_write.table_name;
	char *write_string = NULL;
	int buf_size = WP_SQL_STMT_BUF_SIZE;
	char *s, *e;

	if ((result = wp_table_validate_state(wp_conn, table_name)) != WALLY_RESULT_NO_ERROR) {
		return result;
	}

retry:
	write_string = WALLY_CALLOC(buf_size);
	if (write_string == NULL) {
		WALLY_LOG(AL_ERROR, "No memory");
		return WALLY_RESULT_NO_MEMORY;
	}
    s = write_string;
    e = s + buf_size;

    /*
     * Form upsert request for lookuptable.
     *
     */
    s += sxprintf(s, e, "INSERT INTO %s%s ", table_name, WALLY_LOOKUP_TABLE_SUFFIX);


	//Add the column names
	s += sxprintf(s, e, "(%s, %s, %s) ", "sequence", "column_index", "value");

	//Add values for each column
	s += sxprintf(s, e, "VALUES(%"PRId64", %d, '%s') ON CONFLICT(column_index, value) DO UPDATE set sequence=EXCLUDED.sequence",
			queue_object->data.lookup_table_write.max_sequence_seen, // int64_t max_sequence_seen;
			queue_object->data.lookup_table_write.column_index, queue_object->data.lookup_table_write.value);

	if (s >= e) {
		if (write_string) {
			WALLY_FREE(write_string);
		}
		buf_size *= 16;
		if (buf_size >  BIND_VAL_MAX_BUF_SIZE) {
			WALLY_LOG(AL_ERROR, "Memory allocation crossing limit");
			return WALLY_RESULT_NO_MEMORY;
		}
		goto retry;
	}

	wpt = wp_conn->table;
	WP_STATS_CONN_START(wp_conn);
	result = xPQsendQueryParams((PGconn *)(wp_conn->db_conn),
							   write_string,
							   0,
							   NULL,
							   NULL,
							   NULL,
							   NULL,
							   0);
	if (result) {
		wpt->first_row_sync_completed = true;
		result = WALLY_RESULT_ASYNCHRONOUS;
		wpt->db_lookup_table_start_time_us = epoch_us();
	} else {
		WP_STATS_CONN_STOP(wp_conn, wpt->db, wpt, insert)
		WALLY_LOG(AL_ERROR, "xPQsendQueryParams upsert lookuptable failed: %s", PQerrorMessage((PGconn *)(wp_conn->db_conn)));
		result = WALLY_RESULT_ERR;
	}

	WALLY_DEBUG_POSTGRES("xPQsendQueryParams upsert lookuptable query = %s, res = %s",
			 write_string, PQerrorMessage((PGconn *)(wp_conn->db_conn)));
	if (write_string) {
		WALLY_FREE(write_string);
	}
	return result;

}

int wp_write_object_upsert(struct wp_connection *wp_conn,int wq_indx)
{
    int64_t query_start_time = epoch_us();
    int result = WALLY_RESULT_NO_ERROR;
    struct wp_conn_pg_specific* pgs = wp_conn->cookie;
    struct argo_structure_description *argo_desc = NULL;
    struct wp_table *wpt = NULL;
    char *stmt = NULL;
    char *buff = NULL;
    char *s, *e;
    char *ss, *ee;
    struct wally_origin *origin = wp_conn->wp_db->db_to_wally_cookie;
    int curr_batch_size = origin->wq_info[wq_indx].batch_size;
    int buf_size = curr_batch_size  * WP_BINDVAR_VALUE_BUF_SIZE_DFT;
    int stmt_size = curr_batch_size * WP_SQL_STMT_BUF_SIZE;
    char **row_values;


    int value_index = 0;
    int key_index = -1;
    int sequence_index = -1;
    int64_t sequence = -1;
    int64_t last_sequence = -1;

    if (pgs->write_objects_count <= 0) {
        return result;
    }

    if (!wp_conn->wp_db->is_row_writable) {
        WALLY_LOG(AL_ERROR, "%s: Row to write, db not row writable", wp_conn->wp_db->name);
        return WALLY_RESULT_NO_ERROR;
    }

	if (pgs->write_objects[0]->db_op == db_oper_lookuptable_update) {
		return wp_write_object_lookup_table_upsert(wp_conn, wq_indx);
    } else if (pgs->write_objects[0]->db_op == db_oper_recovery_update) {
        /* This needs to be supported when optimized write option to be supported for
         * brokers, exporters, sloggers etc. Return WALLY_RESULT_NO_ERROR to remove
         * object from the batch.
         */
        WALLY_LOG(AL_ERROR, " Recovery Not supported with optimized batch write.");
        return WALLY_RESULT_NO_ERROR;
    }

    /* XXX Get the table for the object. */
    argo_read_lock();
    argo_desc = argo_global.all_descriptions[pgs->write_objects[0]->data.row_to_write->base_structure_index];

    wpt = argo_hash_lookup(wp_conn->wp_db->tables_by_argo_name, argo_desc->type, argo_desc->type_len, NULL);
    if (!wpt)  {
        argo_unlock();
        WALLY_LOG(AL_ERROR, "%s: Row to write, table %s non-existent", wp_conn->wp_db->name, argo_desc->type);
        return WALLY_RESULT_BAD_ARGUMENT;
    }

    wp_conn->table = wpt;

    /* XXX Check whether synchronization is required. (or allowed) (or in progress) */
    if (wpt->state != table_ready) {
        argo_unlock();
        WALLY_LOG(AL_NOTICE,"table is not ready name: %s state: is %d",wpt->db_table_name,wpt->state);
        return WALLY_RESULT_WOULD_BLOCK;
    }
    if (wpt->needs_to_be_resynchronized) {
        argo_unlock();
        WALLY_LOG(AL_NOTICE,"needs_to_be_resynchronized is set for the table %s. Upsert would block",wpt->db_table_name);
        return WALLY_RESULT_WOULD_BLOCK;
    }
    if (wpt->argo_field_count != argo_desc->description_count) {
        wpt->needs_to_be_resynchronized = 1;
        argo_unlock();
        if (oper_mode_g.is_leaf_wally &&
                oper_mode_g.is_active_done &&
                (wpt->first_row_sync_completed ||
                wpt->current_max_sequence)) {
            /* Report only for the leaf-wally. Schema-mismatch for RDS will be
             * detected in table state machine (wp_parse_columns).
             * Check the commencts in wp_write_object_insert() for more
             * information.
             * */
            wally_error_handler(NULL, wp_conn, true, E_SCHEMA_MISMATCH,
                    SE_NONE);
        }
        WALLY_LOG(AL_NOTICE, "DB resynchronization needed: %s:%s: %s, %d != %d",
                  wpt->db->name, wpt->db_table_name, argo_desc->type, wpt->argo_field_count, (int)argo_desc->description_count);
        return WALLY_RESULT_WOULD_BLOCK;
    }

    stmt = WALLY_CALLOC(stmt_size);
    if(stmt == NULL) {
        return WALLY_RESULT_NO_MEMORY;
    }

    buff =  WALLY_CALLOC(buf_size);
    if(buff == NULL) {
        return WALLY_RESULT_NO_MEMORY;
    }

    row_values = WALLY_CALLOC(curr_batch_size * DEFAULT_DB_STRING_SIZE);
    if(row_values == NULL) {
        return WALLY_RESULT_NO_MEMORY;
    }

    s = stmt;
    e = s + stmt_size;
    ss = buff;
    ee = ss + buf_size;
    int local_description_count = 0;
    int wrote_first = 0;
    int first_row = 0;

retry:

    local_description_count = argo_desc->static_description_count + pgs->write_objects[0]->data.row_to_write->excess_description_count;
    wrote_first = 0;
    first_row = 0;
    last_sequence = origin->wq_info[wq_indx].last_sequence;

    s += sxprintf(s, e, "INSERT INTO %s ( ", wpt->db_table_name);
    for (int i = 0; i < local_description_count; i++) {
        struct argo_field_description *pd = &(argo_desc->description[i]->public_description);

        if (wp_write_object_not_writeable_field(pgs->write_objects[0]->data.row_to_write, wpt, pd)) {
            continue;
        }

        if (pd->is_key) {
            key_index = i;
        }

        if (pd->is_sequence) {
            sequence_index = i;
        }

        s += sxprintf(s, e, (wrote_first ? ", %s" : "%s"), pd->field_name);
        wrote_first ++;
    }

    if (key_index < 0 || sequence_index < 0) {
        WALLY_LOG(AL_ERROR, "argo definition for table %s is invalid?! key_index=%d, sequence_index=%d",
                                 wpt->db_table_name, key_index, sequence_index);
        WALLY_FREE_UPSERT_RESOURCES(stmt, buff, row_values);
        argo_unlock();
        return WALLY_RESULT_BAD_ARGUMENT;
    }
    s += sxprintf(s, e, ") VALUES ");

    for (int count = 0; count < pgs->write_objects_count; count++) {
        char bind_string [WP_SQL_STMT_BUF_SIZE];
        struct argo_object *write_object = pgs->write_objects[count]->data.row_to_write;

        // column names
        // bind var names
        char *s_bv  = bind_string;
        char *e_bv  = s_bv + sizeof(bind_string);
        wrote_first = 0;


        for (int i = 0; i < local_description_count; i++) {
            struct argo_field_description *pd = &(argo_desc->description[i]->public_description);
            if (wp_write_object_not_writeable_field(write_object, wpt, pd)) continue;
            if (pd->is_sequence) {
                argo_object_read_int_by_column_index(pgs->write_objects[count]->data.row_to_write, i, &sequence);
                if(sequence <= last_sequence) {
                     WALLY_LOG(AL_DEBUG , "sequence is not in correct order for table %s  last_sequence:%"PRId64" current sequence:%"PRId64"",
                               wpt->db_table_name,last_sequence,sequence);
                     origin->wq_info[wq_indx].sequence_unordered++;
                }
                last_sequence = sequence;
            }
            s_bv += sxprintf(s_bv, e_bv, (wrote_first ? ", $%d" : "$%d"), value_index + 1);
            result = wp_fill_field_for_write(&ss, ee, &row_values[value_index], argo_desc, pd, write_object->base_structure_data);
            if (result == WALLY_RESULT_ERR_TOO_LARGE) {
                /* Try to allocate buffer to try again. */
                if (buff) {
                    WALLY_FREE(buff);
                }
                buf_size *= 16;
                if (buf_size >  BIND_VAL_MAX_BUF_SIZE) {
                    if(stmt) {
                        WALLY_FREE(stmt);
                    }
                    if(row_values) {
                        WALLY_FREE(row_values);
                    }
                    argo_unlock();
                    WALLY_LOG(AL_ERROR, "row object crossing memory limit");
                    return result;
                }
                buff = WALLY_CALLOC(buf_size);
                if (!buff) {
                    argo_unlock();
                    return WALLY_RESULT_NO_MEMORY;
                }
                origin->wq_info[wq_indx].mem_reallocation ++ ;
                memset(stmt,0,stmt_size);
                s = stmt;
                e = s + stmt_size;
                ss = buff;
                ee = ss + buf_size;
                wrote_first = 0;
                value_index = 0;
                goto retry;
            }
            if (result) {
                argo_unlock();
                WALLY_LOG(AL_ERROR, "Could not fill field for table %s, index = %d, field = %s, err=%s",
                                    wpt->db_table_name, i, pd->field_name, wally_error_strings[result]);
                return result;
            }
            value_index++;
            wrote_first = 1;
        }

        s += sxprintf(s, e, (first_row ? ",(%s)": "(%s)"),bind_string);
        first_row ++;
        if ((ss >= ee) || (s >= e)) {
            WALLY_FREE_UPSERT_RESOURCES(stmt, buff, row_values);
            WALLY_LOG(AL_ERROR, "Upsert row for table %s failed (row too large)", wpt->db_table_name);
            argo_unlock();
            return WALLY_RESULT_ERR_TOO_LARGE;
        }
    }
    s += sxprintf(s, e, " ON CONFLICT (%s) DO UPDATE SET ", argo_desc->description[key_index]->public_description.field_name);
    wrote_first = 0;
    for (int i = 0; i < local_description_count; i++) {
        struct argo_field_description *pd = &(argo_desc->description[i]->public_description);

        if (!pd->write_value || pd->dont_db) {
            continue ;
        }

        if (wpt->db->is_true_origin && pd->dont_write_origin) {
             continue;
        }

        if (pd->is_key) {
            key_index = i;
        }
        if (pd->is_sequence) {
            sequence_index = i;
        }
        s += sxprintf(s, e, (wrote_first ? ", %s=EXCLUDED.%s" : "%s=EXCLUDED.%s"), pd->field_name,pd->field_name);
        wrote_first ++;
    }
    s += sxprintf(s, e, ";");
    if ((s >= e)) {
            WALLY_LOG(AL_ERROR, "Upsert row for table %s failed (row too large)", wpt->db_table_name);
            WALLY_FREE_UPSERT_RESOURCES(stmt, buff, row_values);
            argo_unlock();
            return WALLY_RESULT_ERR_TOO_LARGE;
    }
    argo_unlock();
    origin->wq_info[wq_indx].query_creation_time += epoch_us() - query_start_time ;
    pgs->last_sequence = sequence;
    /* coverity[atomicity: FALSE] */
    result = xPQsendQueryParams((PGconn *)(wp_conn->db_conn),stmt,
                                value_index,
                                NULL,
                                (const char * const *)row_values,
                                NULL,
                                NULL,
                                0);
    argo_read_lock();
    if (result) {
        result = WALLY_RESULT_ASYNCHRONOUS;
        wpt->first_row_sync_completed = true;
        origin->wq_info[wq_indx].total_executed_batch++;
        pgs->query_start_time = epoch_us();
    } else {
        argo_unlock();
        WALLY_LOG(AL_ERROR, "xPQsendQueryParams failed on table %s: %s reset batch size to 1",
                                      wpt->db_table_name, PQerrorMessage((PGconn *)(wp_conn->db_conn)));
        /* In case of error reset batch size to 1 */
        origin->wq_info[wq_indx].batch_size = 1;
        argo_read_lock();
        origin->wq_info[wq_indx].total_failed_batch ++;
        result = WALLY_RESULT_ERR;
    }
    /* coverity[ATOMICITY: FALSE] */
    if (stmt) {  WALLY_FREE(stmt);}
    /* coverity[ATOMICITY: FALSE] */
    if (buff) {  WALLY_FREE(buff);}
    /* coverity[ATOMICITY: FALSE] */
    if (row_values) { WALLY_FREE(row_values);}
    argo_unlock();
    return result;
}

/*
 * Update an lookuptable entry  in the database.
 */

int wp_write_object_update_lookup_table(struct wp_connection *wp_conn, struct wally_local_db_write_queue_object *queue_object)
{
	char *write_string = NULL;
	int buf_size = WP_SQL_STMT_BUF_SIZE;
	char *s, *e;
	int result = WALLY_RESULT_NO_ERROR;
	struct wp_table* wpt = NULL;
	char *table_name = NULL;

    if (queue_object->db_op == db_oper_recovery_update) {
        table_name = queue_object->data.recovery_update.table_name;
    } else {
        table_name = queue_object->data.lookup_table_write.table_name;
    }

	if ((result = wp_table_validate_state(wp_conn, table_name)) != WALLY_RESULT_NO_ERROR) {
		return result;
	}

	wpt = wp_conn->table;

retry:
	write_string = WALLY_CALLOC(buf_size);
	if (write_string == NULL) {
		WALLY_LOG(AL_ERROR, "No memory");
		return WALLY_RESULT_NO_MEMORY;
	}
    s = write_string;
    e = s + buf_size;

    /*
     * Form write request.
     *
     * UPDATE [table_name] set ([col1], [col2], ...)
     */
    if (queue_object->db_op == db_oper_lookuptable_update) {
        s += sxprintf(s, e, "UPDATE %s%s set ", queue_object->data.lookup_table_write.table_name, WALLY_LOOKUP_TABLE_SUFFIX);

	//Append the squence value
	s += sxprintf(s, e, "%s=%"PRId64", %s=%d, %s='%s' where %s=%d and %s='%s'",
				"sequence", queue_object->data.lookup_table_write.max_sequence_seen, // int64_t max_sequence_seen;
				"column_index", queue_object->data.lookup_table_write.column_index,
				"value", queue_object->data.lookup_table_write.value,
				"column_index", queue_object->data.lookup_table_write.column_index,
				"value", queue_object->data.lookup_table_write.value);
    } else if (queue_object->db_op == db_oper_recovery_update) {
        s += sxprintf(s, e, "UPDATE %s%s set ", queue_object->data.recovery_update.table_name, WALLY_LOOKUP_TABLE_SUFFIX);

    	s += sxprintf(s, e, "%s=%"PRId64" where %s>%"PRId64" ",
				"sequence", queue_object->data.recovery_update.recovery_sequence,
				"sequence", queue_object->data.recovery_update.recovery_sequence);
    }
	if (s >= e) {
        if (write_string) {
            WALLY_FREE(write_string);
        }
        buf_size *= 16;
        if (buf_size >  BIND_VAL_MAX_BUF_SIZE) {
            WALLY_LOG(AL_ERROR, "Memory allocation crossing limit");
			return WALLY_RESULT_NO_MEMORY;
        }
		goto retry;
	}

	WP_STATS_CONN_START(wp_conn);
	result = xPQsendQueryParams((PGconn *)(wp_conn->db_conn),
							   write_string,
							   0,
							   NULL,
							   NULL,
							   NULL,
							   NULL,
							   0);
	if (result) {
		wpt->db_lookup_table_start_time_us = epoch_us();
		result = WALLY_RESULT_ASYNCHRONOUS;
	} else {
		/* MUST release argo lock to log error! */
		WP_STATS_CONN_STOP(wp_conn, wpt->db, wpt, update)
		WALLY_LOG(AL_ERROR, "xPQsendQueryParams lookup_table %s update failed: %s",
				table_name,
				PQerrorMessage((PGconn *)(wp_conn->db_conn)));
		wpt->db_lookup_table_total_operations_failed++;
		result = WALLY_RESULT_ERR;
	}

	WALLY_DEBUG_POSTGRES("xPQsendQueryParams lookup_table_update query=%s, %s",
			write_string, PQerrorMessage((PGconn *)(wp_conn->db_conn)));
	if (write_string) {
		WALLY_FREE(write_string);
	}
	return result;
}

/*
 * Update an object in the database.
 *
 * Update will only update a row if it already exists. It is common to
 * call both update and insert on each row when it is being "written"
 * to the database. One of the two is basically guaranteed to
 * fail. There is a short time during which a race condition can exist
 * between the update and the insert, but we avoid that problem by
 * having only one thread at a time writing.
 *
 * wp lock must be held when called.
 */
int wp_write_object_update(struct wp_connection *wp_conn, struct wally_local_db_write_queue_object *queue_object)
{
    struct argo_structure_description *argo_desc;
    struct wp_table *wpt;
    char write_string[20000];
    char write_integers[16 * 1024];
    char *alloc_buf = NULL;
    size_t alloc_buf_size = sizeof(write_integers);
    char *row_values[DEFAULT_DB_STRING_SIZE];
    char *s, *e;
    char *ss, *ee;
    int64_t sequence;
    int64_t index;
    int sequence_index = 0;
    int key_index = 0;
    int i;
    int wrote_first = 0;
    int value_index = 0;
    int result = WALLY_RESULT_NO_ERROR;
    int is_true_origin = 0;
	struct argo_object *write_object = NULL;

    if (!wp_conn->wp_db->is_row_writable) {
        WALLY_LOG(AL_ERROR, "%s: Row to write, db not row writable", wp_conn->wp_db->name);
        return WALLY_RESULT_NO_ERROR;
    }

    if (queue_object->db_op == db_oper_recovery_update ||
                queue_object->db_op == db_oper_lookuptable_update) {
        result = wp_write_object_update_lookup_table(wp_conn, queue_object);
        return result;
	}
	write_object = queue_object->data.row_to_write;

    is_true_origin = wp_conn->wp_db->is_true_origin;

    /* XXX Get the table for the object. */
    argo_read_lock();
    argo_desc = argo_global.all_descriptions[write_object->base_structure_index];

    wpt = argo_hash_lookup(wp_conn->wp_db->tables_by_argo_name, argo_desc->type, argo_desc->type_len, NULL);
    if (!wpt)  {
        argo_unlock();
        WALLY_LOG(AL_ERROR, "%s: Row to write, table %s non-existent", wp_conn->wp_db->name, argo_desc->type);
        return WALLY_RESULT_BAD_ARGUMENT;
    }

    wp_conn->table = wpt;

    /* XXX Check whether synchronization is required. (or allowed) (or in progress) */
    if (wpt->state != table_ready) {
        argo_unlock();
        return WALLY_RESULT_WOULD_BLOCK;
    }
    if (wpt->needs_to_be_resynchronized) {
        argo_unlock();
        return WALLY_RESULT_WOULD_BLOCK;
    }
    if (wpt->argo_field_count != argo_desc->description_count) {
        wpt->needs_to_be_resynchronized = 1;
        argo_unlock();
        if (oper_mode_g.is_leaf_wally &&
                oper_mode_g.is_active_done &&
                (wpt->first_row_sync_completed ||
                wpt->current_max_sequence)) {
            /* Report only for the leaf-wally. Schema-mismatch for RDS will be
             * detected in table state machine (wp_parse_columns).
             * Check the commencts in wp_write_object_insert() for more
             * information.
             * */
            wally_error_handler(NULL, wp_conn, true, E_SCHEMA_MISMATCH,
                    SE_NONE);
        }
        WALLY_LOG(AL_NOTICE, "DB resynchronization needed: %s:%s: %s, %d != %d",
                  wpt->db->name, wpt->db_table_name, argo_desc->type, wpt->argo_field_count, (int)argo_desc->description_count);
        return WALLY_RESULT_WOULD_BLOCK;
    }

    /*
     * Form write request.
     *
     * UPDATE [table_name] set ([col1], [col2], ...)
     */
    s = write_string;
    e = s + sizeof(write_string);
    ss = write_integers;
    ee = ss + sizeof(write_integers);

 retry:

    s += sxprintf(s, e, "UPDATE %s set ", wpt->db_table_name);
    int local_description_count = argo_desc->static_description_count + write_object->excess_description_count;
    for (i = 0; i < local_description_count; i++) {
        struct argo_field_description *pd = &(argo_desc->description[i]->public_description);
        int res;

        if (pd->is_sequence) {
            sequence_index = i;
        }
        if (pd->is_key) {
            key_index = i;
        }

        if (!(pd->write_value)) continue;
        if (pd->dont_db) continue;

        if (is_true_origin && pd->dont_write_origin) continue;

        /* Write each field. */
        if (wrote_first) {
            s += sxprintf(s, e, ", %s=$%d", pd->field_name, value_index + 1);
        } else {
            s += sxprintf(s, e, "%s=$%d", pd->field_name, value_index + 1);
        }
        wrote_first = 1;

        res = wp_fill_field_for_write(&ss, ee, &row_values[value_index], argo_desc, pd, write_object->base_structure_data);
        if (res) {
            if (res == WALLY_RESULT_ERR_TOO_LARGE) {
                /* Try to allocate buffer to try again. */
                if (alloc_buf) {
                    WALLY_FREE(alloc_buf);
                }
                alloc_buf_size *= 16;
                if (alloc_buf_size > (16 * 1024 * 1024)) {
                    argo_unlock();
                    return res;
                }
                alloc_buf = WALLY_MALLOC(alloc_buf_size);
                if (!alloc_buf) {
                    argo_unlock();
                    return WALLY_RESULT_NO_MEMORY;
                }
                s = write_string;
                e = s + sizeof(write_string);
                ss = alloc_buf;
                ee = ss + alloc_buf_size;
                wrote_first = 0;
                value_index = 0;
                goto retry;
            }
            argo_unlock();
            WALLY_LOG(AL_ERROR, "Could not fill field, index = %d, field = %s", i, pd->field_name);
            if (alloc_buf) WALLY_FREE(alloc_buf);
            return res;
        }

        //fprintf(stderr, "Value index %d == %s\n", value_index, row_values[value_index]);
        value_index++;
    }
    if ((ss >= ee) || (s >= e)) {
        /* XXX LOG */
        argo_unlock();
        /* Exceeded our stack based buffer sizes for row writes. */
        WALLY_LOG(AL_ERROR, "Error, too large");
        result = WALLY_RESULT_ERR_TOO_LARGE;
    } else {
        sequence = argo_read_int(write_object->base_structure_data + argo_desc->description[sequence_index]->public_description.offset,
                                 argo_desc->description[sequence_index]->public_description.size);
        if (argo_desc->description[key_index]->public_description.argo_field_type == argo_field_data_type_string) {
            char *index_str;
            argo_object_read_string_by_column_index(write_object, key_index, &index_str);
            if (is_true_origin) {
                s += sxprintf(s, e, " where %s='%s'",
                              argo_desc->description[key_index]->public_description.field_name,
                              index_str);
            } else {
                s += sxprintf(s, e, " where %s='%s' and %s<%ld",
                              argo_desc->description[key_index]->public_description.field_name,
                              index_str,
                              argo_desc->description[sequence_index]->public_description.field_name,
                              (long)sequence);
            }
        } else {
            index = argo_read_int(write_object->base_structure_data + argo_desc->description[key_index]->public_description.offset,
                                  argo_desc->description[key_index]->public_description.size);
            if (is_true_origin) {
                s += sxprintf(s, e, " where %s=%ld",
                              argo_desc->description[key_index]->public_description.field_name,
                              (long)index);
            } else {
                s += sxprintf(s, e, " where %s=%ld and %s<%ld",
                              argo_desc->description[key_index]->public_description.field_name,
                              (long)index,
                              argo_desc->description[sequence_index]->public_description.field_name,
                              (long)sequence);
            }
        }
        argo_unlock();

        if (wally_debug & WALLY_DEBUG_WRITE_ROW_BIT) {
            char msg[10000];
            char *sss, *eee;
            int iii;
            sss = msg;
            eee = sss + sizeof(msg);
            sss += sxprintf(sss, eee, "%s: DB write: %s values ", wp_conn->wp_db->name, write_string);
            for (iii = 0; iii < value_index; iii++) {
                if (iii != 0) {
                    sss += sxprintf(sss, eee, ", ");
                }
                sss += sxprintf(sss, eee, "%s", row_values[iii]);
            }
            WALLY_DEBUG_WRITE_ROW("%s", msg);
        }
        WP_STATS_CONN_START(wp_conn);

        result = xPQsendQueryParams((PGconn *)(wp_conn->db_conn),
                                   write_string,
                                   value_index,
                                   NULL,
                                   (const char * const *)row_values,
                                   NULL,
                                   NULL,
                                   0);
        if (result) {
            result = WALLY_RESULT_ASYNCHRONOUS;
        } else {
            /* MUST release argo lock to log error! */
            WP_STATS_CONN_STOP(wp_conn, wpt->db, wpt, update)
            WALLY_LOG(AL_ERROR, "xPQsendQueryParams failed: %s", PQerrorMessage((PGconn *)(wp_conn->db_conn)));
            result = WALLY_RESULT_ERR;
        }
    }

    /* XXX Send write request. */
    /* XXX Return result. */

    if (alloc_buf) WALLY_FREE(alloc_buf);
    return result;
}


/*
 * Insert an lookuptable entry to the database.
 *
 * The basic format is:
 *
 * INSERT INTO table_name
 *    (col_A, col_B, ...)
 *    (SELECT col_A_value AS col_A, col_B_value AS col_B, ...
 */

int wp_write_object_insert_lookup_table(struct wp_connection *wp_conn, struct wally_local_db_write_queue_object *queue_object)
{
	char *write_string = NULL;
	int buf_size = WP_SQL_STMT_BUF_SIZE;
	char *s, *e;
	int result = WALLY_RESULT_NO_ERROR;
	struct wp_table* wpt = NULL;
	char *table_name = queue_object->data.lookup_table_write.table_name;

	if ((result = wp_table_validate_state(wp_conn, table_name)) != WALLY_RESULT_NO_ERROR) {
		return result;
	}

retry:
	write_string = WALLY_CALLOC(buf_size);
	if (write_string == NULL) {
		WALLY_LOG(AL_ERROR, "No memory");
	}
	s = write_string;
    e = s + buf_size;

    s += sxprintf(s, e, "INSERT INTO %s%s ", table_name, WALLY_LOOKUP_TABLE_SUFFIX);


	//Add the column names
	s += sxprintf(s, e, "(%s, %s, %s) ", "sequence", "column_index", "value");

	//Add values for each column
	s += sxprintf(s, e, "VALUES(%"PRId64", %d, '%s')", queue_object->data.lookup_table_write.max_sequence_seen,
											queue_object->data.lookup_table_write.column_index,
											queue_object->data.lookup_table_write.value);

	 if (s >= e) {
        if (write_string) {
            WALLY_FREE(write_string);
        }
        buf_size *= 16;
        if (buf_size >  BIND_VAL_MAX_BUF_SIZE) {
            WALLY_LOG(AL_ERROR, "Memory allocation crossing limit");
			return WALLY_RESULT_NO_MEMORY;
        }
        goto retry;
    }

	wpt = wp_conn->table;
	WP_STATS_CONN_START(wp_conn);
	result = xPQsendQueryParams((PGconn *)(wp_conn->db_conn),
							   write_string,
							   0,
							   NULL,
							   NULL,
							   NULL,
							   NULL,
							   0);
	if (result) {
		//wpt->first_row_sync_completed = true;
		result = WALLY_RESULT_ASYNCHRONOUS;
		wpt->db_lookup_table_start_time_us = epoch_us();
	} else {
		WP_STATS_CONN_STOP(wp_conn, wpt->db, wpt, insert)
		WALLY_LOG(AL_ERROR, "xPQsendQueryParams Insert lookup table query failed: %s", PQerrorMessage((PGconn *)(wp_conn->db_conn)));
		result = WALLY_RESULT_ERR;
	}

	WALLY_DEBUG_POSTGRES("xPQsendQueryParams insert lookuptable query = %s, res = %s",
			 write_string, PQerrorMessage((PGconn *)(wp_conn->db_conn)));
	if (write_string) {
		WALLY_FREE(write_string);
	}
	return result;
}

/*
 * Insert an object to the database, but only if it does not exist.
 *
 * The basic format is:
 *
 * INSERT INTO table_name
 *    (col_A, col_B, ...)
 *    (SELECT col_A_value AS col_A, col_B_value AS col_B, ...
 *     WHERE NOT EXISTS (SELECT 1 FROM table_name WHERE col_index=index_value)
 *    )
 *
 * wp lock must be held when called.
 */
int wp_write_object_insert(struct wp_connection *wp_conn, struct wally_local_db_write_queue_object *queue_object)
{
    struct argo_structure_description *argo_desc;
    struct wp_table *wpt;
    char write_string[20000];
    char write_integers[16 * 1024];
    char *alloc_buf = NULL;
    size_t alloc_buf_size = sizeof(write_integers);
    char *row_values[DEFAULT_DB_STRING_SIZE];
    char *s, *e;
    char *ss, *ee;
    int64_t index;
    int key_index = 0;
    int i;
    int wrote_first = 0;
    int value_index = 0;
    int result = 0;
    int local_description_count;
	struct argo_object *write_object = NULL;

    if (!wp_conn->wp_db->is_row_writable) return WALLY_RESULT_NO_ERROR;

	if (queue_object->db_op == db_oper_lookuptable_update) {
		result = wp_write_object_insert_lookup_table(wp_conn, queue_object);
		return result;
	}

	write_object = queue_object->data.row_to_write;
    /* XXX Get the table for the object. */
    argo_read_lock();
    argo_desc = argo_global.all_descriptions[write_object->base_structure_index];

    wpt = argo_hash_lookup(wp_conn->wp_db->tables_by_argo_name, argo_desc->type, argo_desc->type_len, NULL);
    if (!wpt)  {
        argo_unlock();
        return WALLY_RESULT_BAD_ARGUMENT;
    }

    wp_conn->table = wpt;

    /* XXX Check whether synchronization is required. (or allowed) (or in progress) */
    if (wpt->state != table_ready){
        argo_unlock();
        return WALLY_RESULT_WOULD_BLOCK;
    }
    if (wpt->needs_to_be_resynchronized){
        argo_unlock();
        return WALLY_RESULT_WOULD_BLOCK;
    }
    if (wpt->argo_field_count != argo_desc->description_count) {
        wpt->needs_to_be_resynchronized = 1;
        argo_unlock();
        if (oper_mode_g.is_leaf_wally &&
                oper_mode_g.is_active_done &&
                (wpt->first_row_sync_completed ||
                wpt->current_max_sequence)) {
            /* Report only for the leaf-wally. Schema-mismatch for RDS will be
             * detected in table state machine (wp_parse_columns).
             * Schema_mistmatch should be reported only during the runtime, and
             * bootup column mismatch(first time sync) should be omitted. Bootup
             * column mismatches are expected as always there will be some extra
             * columns in the RDS than the ARGO definition. Schema_mistmatch will
             * be reported only when existing in-memory ARGO/local_db columns are
             * different from the received one.
             *
             * These checks are added to handle above requirement and there are two cases
             * to be handled
             * 1. Wally restart with existing local DB fields
             * 2. Wally fresh start without existing local DB data (DB fresh start)
             *
             * In both the cases we need to identify whether the table has any rows
             * in the local DB and schema_mismatch needs to be skipped for first row insert.
             *
             * In case 1:
             *  wpt->current_max_sequence holds the max sequence of the local DB row and it
             *  tells us that local DB is having rows.
             *  Even before first row insert, local DB was having the data so
             *  wpt->first_row_sync_completed flag is not required.
             * In case 2:
             *  wpt->current_max_sequence will be 0 as local DB is empty and
             *  wpt->first_row_sync_completed is used in this case. It
             *  will be TRUE if the first row is inserted.
             * */
            wally_error_handler(NULL, wp_conn, true, E_SCHEMA_MISMATCH,
                    SE_NONE);
        }
        WALLY_LOG(AL_NOTICE, "DB resynchronization needed");
        return WALLY_RESULT_WOULD_BLOCK;
    }

    /*
     * Form write request.
     */
    s = write_string;
    e = s + sizeof(write_string);
    ss = write_integers;
    ee = ss + sizeof(write_integers);

 retry:

    local_description_count = argo_desc->static_description_count + write_object->excess_description_count;
    s += sxprintf(s, e, "INSERT INTO %s (", wpt->db_table_name);
    for (i = 0; i < local_description_count; i++) {
        struct argo_field_description *pd = &(argo_desc->description[i]->public_description);

        if (!(pd->write_value)) continue;
        if (pd->dont_db) continue;
        if (wpt->db->is_true_origin && pd->dont_write_origin) continue;
        /* If it is the sequence, and if the sequence is zero, skip
         * it. This is done to keep from having duplicate insertions
         * into a table for the first time, as first-insert would
         * otherwise always be zero for sequence, and wouldn't leave
         * the value 0 until updated. */
        if (pd->is_sequence) {
            if (argo_object_get_sequence(write_object) == 0) continue;
        }

        if (wrote_first) {
            s += sxprintf(s, e, ", %s", pd->field_name);
        } else {
            s += sxprintf(s, e, " %s", pd->field_name);
        }
        wrote_first = 1;
    }
    s += sxprintf(s, e, " ) ( SELECT ");


    wrote_first = 0;
    for (i = 0; i < local_description_count; i++) {
        struct argo_field_description *pd = &(argo_desc->description[i]->public_description);

        if (!(pd->write_value)) continue;
        if (pd->dont_db) continue;
        if (wpt->db->is_true_origin && pd->dont_write_origin) continue;
        /* If it is the sequence, and if the sequence is zero, skip
         * it. This is done to keep from having duplicate insertions
         * into a table for the first time, as first-insert would
         * otherwise always be zero for sequence, and wouldn't leave
         * the value 0 until updated. */
        if (pd->is_sequence) {
            if (argo_object_get_sequence(write_object) == 0) continue;
        }

        if (pd->is_key) {
            key_index = i;
        }

        /* Write each field. */
        if (wrote_first) {
            s += sxprintf(s, e, ", $%d AS %s", value_index + 1, pd->field_name);
        } else {
            s += sxprintf(s, e, "$%d AS %s", value_index + 1, pd->field_name);
        }
        wrote_first = 1;

        result = wp_fill_field_for_write(&ss, ee, &row_values[value_index], argo_desc, pd, write_object->base_structure_data);
        if (result) {
            if (result == WALLY_RESULT_ERR_TOO_LARGE) {
                /* Try to allocate buffer to try again. */
                if (alloc_buf) {
                    WALLY_FREE(alloc_buf);
                    alloc_buf = NULL;
                }
                alloc_buf_size *= 16;
                if (alloc_buf_size > (16 * 1024 * 1024)) {
                    argo_unlock();
                    return result;
                }
                alloc_buf = WALLY_MALLOC(alloc_buf_size);
                if (!alloc_buf) {
                    argo_unlock();
                    return WALLY_RESULT_NO_MEMORY;
                }
                s = write_string;
                e = s + sizeof(write_string);
                ss = alloc_buf;
                ee = ss + alloc_buf_size;
                value_index = 0;
                wrote_first = 0;
                goto retry;
            }
            argo_unlock();
            WALLY_LOG(AL_ERROR, "Could not fill field, index = %d, field = %s", i, pd->field_name);
            if (alloc_buf) WALLY_FREE(alloc_buf);
            return result;
        }

        //fprintf(stderr, "Value index %d == %s\n", value_index, row_values[value_index]);
        value_index++;
    }

    if (ss == ee) {
        /* XXX LOG */
        argo_unlock();
        WALLY_LOG(AL_ERROR, "postgres write length exceeded, table = %s", wpt->db_table_name);
    } else {

        if (argo_desc->description[key_index]->public_description.argo_field_type == argo_field_data_type_string) {
            char *index_str;
            argo_object_read_string_by_column_index(write_object, key_index, &index_str);
            s += sxprintf(s, e, " WHERE NOT EXISTS ( SELECT 1 FROM %s WHERE %s='%s' ) )",
                          wpt->db_table_name,
                          argo_desc->description[key_index]->public_description.field_name,
                          index_str);
        } else {
            index = argo_read_int(write_object->base_structure_data + argo_desc->description[key_index]->public_description.offset,
                                  argo_desc->description[key_index]->public_description.size);

            s += sxprintf(s, e, " WHERE NOT EXISTS ( SELECT 1 FROM %s WHERE %s=%ld ) )",
                          wpt->db_table_name,
                          argo_desc->description[key_index]->public_description.field_name,
                          (long)index);
        }

        argo_unlock();
        if ((ss >= ee) || (s >= e)) {
            /* XXX LOG */
            /* Exceeded our stack based buffer sizes for row writes. */
            result = WALLY_RESULT_ERR_TOO_LARGE;
        } else {
            if (wally_debug & WALLY_DEBUG_WRITE_ROW_BIT) {
                char msg[10000];
                char *sss, *eee;
                int iii;
                sss = msg;
                eee = sss + sizeof(msg);
                sss += sxprintf(sss, eee, "%s: DB write: %s values ", wp_conn->wp_db->name, write_string);
                for (iii = 0; iii < value_index; iii++) {
                    if (iii != 0) {
                        sss += sxprintf(sss, eee, ", ");
                    }
                    sss += sxprintf(sss, eee, "%s", row_values[iii]);
                }
                WALLY_DEBUG_WRITE_ROW("%s", msg);
            }
            WP_STATS_CONN_START(wp_conn);
            result = xPQsendQueryParams((PGconn *)(wp_conn->db_conn),
                                       write_string,
                                       value_index,
                                       NULL,
                                       (const char * const *)row_values,
                                       NULL,
                                       NULL,
                                       0);
            if (result) {
                wpt->first_row_sync_completed = true;
                result = WALLY_RESULT_ASYNCHRONOUS;
            } else {
                WP_STATS_CONN_STOP(wp_conn, wpt->db, wpt, insert)
                WALLY_LOG(AL_ERROR, "xPQsendQueryParams failed: %s", PQerrorMessage((PGconn *)(wp_conn->db_conn)));
                result = WALLY_RESULT_ERR;
            }
        }
    }

    if (alloc_buf) WALLY_FREE(alloc_buf);

    /* XXX Send write request. */
    /* XXX Return result. */

    return result;
}


int wp_table_create_db_complete(struct wp_connection *wp_conn)
{
    PGresult *pres;
    int res;

    pres = PQgetResult((PGconn *)(wp_conn->db_conn));
    if (!pres) {
        WALLY_LOG(AL_WARNING, "WP: Create database complete, but no result? W T F?");
        return WALLY_RESULT_NO_ERROR;
    }

    res = PQresultStatus(pres);
    if (res != PGRES_COMMAND_OK) {
        /* XXX LOG */
        WALLY_LOG(AL_WARNING, "WP: Create database complete, but command not okay? W T F?");
        /* Only log the PSQL error and don't trigger watchdog trigger,
         * caller will decide on this */
        wally_error_handler(pres, wp_conn, false, E_MAX, SE_NONE);
        return WALLY_RESULT_BAD_DATA;
    }
    PQclear(pres);
    pres = NULL;
    return WALLY_RESULT_NO_ERROR;
}

/*
 * Consumes any outstanding input (clear input buffer on conn), and if
 * the result of the command was good, execute the next command. If
 * next command was executed, this routine returns ASYNC
 */
int consume_and_call(struct wp_connection *wp_conn, int (*callback)(struct wp_connection *), char *debug_str)
{
    PGresult *pres = NULL;
    int zero_fails;

    //WALLY_LOG(AL_NOTICE, "consume_and_call %s", debug_str);
    pres = PQgetResult((PGconn *)(wp_conn->db_conn));
    if (PQresultStatus(pres) != PGRES_COMMAND_OK) {
        // Trigger abort.
        if ((PQresultStatus(pres) == PGRES_FATAL_ERROR) && !strcmp(PQresultErrorField(pres, PG_DIAG_SQLSTATE), WP_LOCK_NOT_AVAILABLE)) {
            /* If it was a lock failure, we return ASYNC */
            WALLY_DEBUG_POSTGRES("Could not get lock: %s, status = %s, Error Field = %s: %.*s",
                    debug_str,
                    PQresStatus(PQresultStatus(pres)),
                    PQresultErrorField(pres, PG_DIAG_SQLSTATE),
                    (int) (strlen(PQresultErrorMessage(pres)) - 1),
                    PQresultErrorMessage(pres));
            PQclear(pres);
            pres = NULL;
            while ((pres = PQgetResult((PGconn *)(wp_conn->db_conn)))) {
                WALLY_LOG(AL_NOTICE, "Toss result %s:%s",
                          PQresStatus(PQresultStatus(pres)),
                          PQresultErrorMessage(pres));
                PQclear(pres);
                pres = NULL;
            }
            /* Returning no_error will cause the caller to STOP
             * processing its state machine. returning asynchronous
             * tells the state machine to go on... */
            return WALLY_RESULT_NO_ERROR;
        } else {
            WALLY_LOG(AL_CRITICAL, "Could not consume_and_call %s",
                      debug_str);
            wally_error_handler(pres, wp_conn, true,
                    E_MAX, SE_NONE);
            return WALLY_RESULT_ERR;
        }
    } else {
        // Insert log here?
    }
    PQclear(pres);
    pres = NULL;
    while ((pres = PQgetResult((PGconn *)(wp_conn->db_conn)))) {
        WALLY_LOG(AL_NOTICE, "Toss result %s:%s",
                  PQresStatus(PQresultStatus(pres)),
                  PQresultErrorMessage(pres));
        PQclear(pres);
        pres = NULL;
    }
    if (callback) {
        zero_fails = (callback)(wp_conn);
        if (zero_fails == 0) {
            // Trigger abort.
            WALLY_LOG(AL_CRITICAL, "Could not consume_and_call (callback) %s",
                      debug_str);
            wally_error_handler(NULL, wp_conn, true,
                    E_MAX, SE_NONE);
            return WALLY_RESULT_ERR;
        }
        return WALLY_RESULT_ASYNCHRONOUS;
    }
    return WALLY_RESULT_NO_ERROR;
}

/* wally table cleanup callback */
int wally_postgres_cleanup(void *callout_cookie,
                           const char *table_name,
                           int64_t *sequence,
                           int64_t *deleted,
                           int64_t *key_int,
                           int      sz,
                           int64_t min_seq_auto_update)
{
    int i;
    int res;
    struct wp_db *wp_db = (struct wp_db *) callout_cookie;
    struct wp_table *t;

    ZPATH_RWLOCK_WRLOCK(&(wp_db->lock), __FILE__, __LINE__);

    t = argo_hash_lookup(wp_db->tables_by_argo_name, table_name, strlen(table_name), NULL);
    if (t) {
        t->min_seq_auto_update = min_seq_auto_update;

        if (sz <= 0) {
            /* nothing to trim, just skip, this may happen at wallyd initilization time */
            res = WALLY_TABLE_CLEANUP_NO_ERROR;
            WALLY_DEBUG_TABLE_CLEANUP("wally cleanup:db=%s, table=%s, sz=0", wp_db->name, table_name);
        } else {
            WALLY_DEBUG_TABLE_CLEANUP("wally cleanup:db=%s, table=%s, sz=%d, buf_s=%d, first sequence=%ld, fist id=%ld, "
                                      "min_sequence=%ld, state=%d, now_us =%ld, last_read_complete_us=%ld"
                                      "polling_interval_us = %"PRId64".",
                                      wp_db->name, table_name, sz, t->cleanup_buf_sz, (long)sequence[0], (long)key_int[0],
                                      (long)t->min_valid_sequence_set, (int)t->cleanup_state, (long)t->now_us,
                                      (long)t->last_read_complete_us, (t->xpoll_poll_interval_us ?
                                      t->xpoll_poll_interval_us :wp_db->polling_interval_us));

            if (t->cleanup_state != cleanup_idle) {
                /* still processing last request */
                res = WALLY_TABLE_CLEANUP_BUSY;
            } else if (sequence[0]<t->min_valid_sequence_set) {
                /* waiting for last request to comeback to wally and AVL tree */
                res = WALLY_TABLE_CLEANUP_WAITING;
            } else if (wp_db->polling_interval_us && (t->now_us - t->last_read_complete_us) >
                            (t->xpoll_poll_interval_us ? t->xpoll_poll_interval_us :wp_db->polling_interval_us)) {
                /* do not cleanup if it is time to poll */
                res = WALLY_TABLE_CLEANUP_POLL;
            } else {
                if(t->cleanup_buf_sz < sz) {
                   t->cleanup_buf = WALLY_REALLOC(t->cleanup_buf, sz*sizeof(struct cleanup_table_data));
                   t->cleanup_buf_sz = sz;
                }
                t->cleanup_state = cleanup_start;
                for (i = 0; i < sz; i++) {
                    t->cleanup_buf[i].sequence = sequence[i];
                    t->cleanup_buf[i].deleted = deleted[i];
                    t->cleanup_buf[i].key_int = key_int[i];
                }
                t->cleanup_count = sz;

                res = WALLY_TABLE_CLEANUP_NO_ERROR;
            }
        }
    } else {
        res = WALLY_TABLE_CLEANUP_ERROR;
        WALLY_LOG(AL_ERROR, "wally cleanup: Could not find table %s in db %s", table_name, wp_db->name);
    }

    ZPATH_RWLOCK_UNLOCK(&(wp_db->lock), __FILE__, __LINE__);
    return res;
}

/* update min sequence on zpath_table */
void wally_postgres_update_min_seq(struct wp_connection *wp_conn)
{
    PGresult *pres;
    struct wp_table *t = wp_conn->table;
    struct wp_db *wp_db = t->db;
    struct wp_table *z_table;
    char *name = "zpath_table";

    z_table = argo_hash_lookup(wp_db->tables_by_argo_name, name, strlen(name), NULL);
    if (z_table && z_table->state == table_ready) {
        while ((pres = PQgetResult((PGconn *)(wp_conn->db_conn)))) {
            WALLY_LOG(AL_CRITICAL, "Toss result %s:%s",
            PQresStatus(PQresultStatus(pres)),
            PQresultErrorMessage(pres));
            PQclear(pres);
            pres = NULL;
        }

        if (wp_send_minseq_update(wp_conn) == 0) {
            /* failed to dispatch the request */
            WALLY_LOG(AL_ERROR, "%s: %s: Update min sequence error %s",
                                t->db->name, t->db_table_name, PQerrorMessage((PGconn *)(wp_conn->db_conn)));
            WALLY_DEBUG_POSTGRES_CONN("%s: %s: Free connection %d", t->db->name, t->db_table_name, wp_conn->my_index);
            wally_db_insert_free_conn (wp_db, wp_conn);
        } else {
            t->min_squence_last_rollback = t->min_squence_last;
            t->min_squence_last = t->min_valid_sequence_set;
        }
        t->min_sequence_last_us = epoch_us();
    }
}

/* must be called with db lock */
void wally_postgres_do_cleanup(struct wp_connection *wp_conn)
{
    PGresult *pres;
    struct wp_table *t = wp_conn->table;
    struct wp_db *wp_db = t->db;

    t->cleanup_state = cleanup_in_process;
    t->cleanup_process_start_us = epoch_us();
    WALLY_DEBUG_TABLE_CLEANUP("wally cleanup: table=%s, idx=%d, count=%d", t->db_table_name, t->cleanup_index, t->cleanup_count);
    for( ; t->cleanup_index < t->cleanup_count; t->cleanup_index++) {
        if (t->cleanup_buf[t->cleanup_index].deleted == 0) {
            while ((pres = PQgetResult((PGconn *)(wp_conn->db_conn)))) {
                WALLY_LOG(AL_CRITICAL, "Toss result %s:%s",
                PQresStatus(PQresultStatus(pres)),
                PQresultErrorMessage(pres));
                PQclear(pres);
                pres = NULL;
            }

            if (wp_send_cleanup_update(wp_conn) == 0) {
                /* failed to dispatch the request */
                reset_cleanup(t, t->cleanup_buf[t->cleanup_index].sequence);
                WALLY_LOG(AL_ERROR, "wally cleanup error: %s: %s: %ld, %ld, %s",
                                    t->db->name, t->db_table_name, (long)t->cleanup_buf[t->cleanup_index].sequence,
                                    (long)t->cleanup_buf[t->cleanup_index].key_int, PQerrorMessage((PGconn *)(wp_conn->db_conn)));
                WALLY_DEBUG_POSTGRES_CONN("%s: %s: Free connection %d", t->db->name, t->db_table_name, wp_conn->my_index);
                wally_db_insert_free_conn (wp_db, wp_conn);
            }
            break;
        }
    }

    if (t->cleanup_index >= t->cleanup_count) {
        reset_cleanup(t, t->cleanup_buf[t->cleanup_count-1].sequence + 1);
    }
}

void wally_postgres_event_callback(evutil_socket_t sock, short flags, void *cookie)
{
    struct wp_connection *wp_conn = (struct wp_connection *) cookie;
    struct wp_table *table = wp_conn->table;
    struct wp_db *wp_db = wp_conn->wp_db;
    struct wally_origin *origin = wp_db->db_to_wally_cookie;
    int64_t now_s = 0;
    int64_t delta_s = 0;
    int64_t parse_start_time= 0;
    int64_t process_start_time = 0;

    PGresult *pres;
    int result = 0;
    int do_unblock = 0;
    int do_cmd_complete = 0;
    struct argo_object *objects[wally_gbl_cfg.wally_postgres_db_query_batch_size * WALLY_MAX_INDEXED_COLUMNS];
    int64_t indexes[wally_gbl_cfg.wally_postgres_db_query_batch_size * WALLY_MAX_INDEXED_COLUMNS];
    int object_count = 0;
    int i;
    int64_t key_int;
    int64_t seq;
    //int res;

    int64_t cmd_id;
    void *cmd_cookie;
    int cmd_row_count;
    int read_more;
    int rows;

    ZPATH_RWLOCK_WRLOCK(&(wp_db->lock), __FILE__, __LINE__);

    /* While connection has data... */
    if (!PQconsumeInput((PGconn *)(wp_conn->db_conn))) {

        if (wally_origin_disc_manual && wp_db->is_true_origin && !wp_db->is_alterable)  {
            /* Origin-disconnect only allowed in Origin-wally and for the
             * RDS DB connections. Not for the local-db*/
            wp_origin_disconnect(wp_db);
            ZPATH_RWLOCK_UNLOCK(&(wp_db->lock), __FILE__, __LINE__);
            return ;
        }

        WALLY_LOG(AL_CRITICAL, "WP: could not consume input: %s, %s\n",
                               PQerrorMessage((PGconn *)(wp_conn->db_conn)), wp_conn->debug_str);

        /* DB connection failed, mark the connection as failed and post the event  */
        wally_error_handler(NULL, wp_conn, true,
                E_MAX, SE_NONE);
        ZPATH_RWLOCK_UNLOCK(&(wp_db->lock), __FILE__, __LINE__);
        return;
    }

    /* XXX We read rows/responses before checking the notification
     * queue. */
    if (PQisBusy((PGconn *)(wp_conn->db_conn))) {
        /* We would block trying to read a result- just return and
         * wait for more data. */
        ZPATH_RWLOCK_UNLOCK(&(wp_db->lock), __FILE__, __LINE__);
        return;
    }
    /* Now, check if we are reading rows, reading an update, or
     * processing table state machine. */
    switch (wp_conn->state) {
    case conn_table_create_db:
        WALLY_DEBUG_POSTGRES_EVENT("Event callback, %s/%s/%s (create_db)", wp_db->name, table->db_table_name, wp_conn->name);
        result = wp_table_create_db_complete(wp_conn);
        break;

    case conn_table_read_begin:
        WP_STATS_CONN_STOP(wp_conn, table->db, table, tcl)
        result = consume_and_call(wp_conn, wp_send_read_lock, "conn_table_read_begin");
        break;
    case conn_table_read_lock:
        WP_STATS_CONN_STOP(wp_conn, table->db, table, lock)
        result = consume_and_call(wp_conn, wp_send_read_select, "conn_table_read_lock");
        break;
    case conn_table_read_select:
        WP_STATS_CONN_STOP(wp_conn, table->db, table, query)
        WALLY_DEBUG_POSTGRES_EVENT("Event callback, %s/%s/%s (table_read)", wp_db->name, table->db_table_name, wp_conn->name);
        /* Update Bootup Stats */
        wp_bootup_update_table_stats(table, wp_bootup_table_query_end);
        wp_bootup_update_table_stats(table, wp_bootup_table_rowparse_start);
        result = wp_update_table_read_rows(wp_conn->table, wp_conn, 0, objects, indexes, &object_count, &read_more);
        wp_bootup_update_table_stats(table, wp_bootup_table_rowparse_end);
        wp_bootup_update_table_stats(table, wp_bootup_table_batches_completed);

        if (result) {
            WALLY_LOG(AL_CRITICAL, "WP: could not read rows: %s", wally_error_strings[result]);
            while ((pres = PQgetResult((PGconn *)(wp_conn->db_conn)))) {
                WALLY_LOG(AL_NOTICE, "Toss result %s:%s",
                        PQresStatus(PQresultStatus(pres)),
                        PQresultErrorMessage(pres));
                PQclear(pres);
                pres = NULL;
            }
            result = wp_send_read_commit(wp_conn);
            result = WALLY_RESULT_ASYNCHRONOUS;
            break;
        }
        if (read_more) wp_conn->read_more = 1;
        while ((pres = PQgetResult((PGconn *)(wp_conn->db_conn)))) {
            WALLY_LOG(AL_NOTICE, "Toss result %s:%s",
                      PQresStatus(PQresultStatus(pres)),
                      PQresultErrorMessage(pres));
            PQclear(pres);
            pres = NULL;
        }
        result = wp_send_read_commit(wp_conn);
        result = WALLY_RESULT_ASYNCHRONOUS;
        break;
    case conn_table_read_select_nolock:
        WP_STATS_CONN_STOP(wp_conn, table->db, table, query)
        WALLY_DEBUG_POSTGRES_EVENT("Event callback, %s/%s/%s (table_read_nolock)", wp_db->name, table->db_table_name, wp_conn->name);
        wp_bootup_update_table_stats(table, wp_bootup_table_query_end);
        wp_bootup_update_table_stats(table, wp_bootup_table_rowparse_start);
        result = wp_update_table_read_rows(wp_conn->table, wp_conn, 0, objects, indexes, &object_count, &read_more);
        wp_bootup_update_table_stats(table, wp_bootup_table_rowparse_end);
        wp_bootup_update_table_stats(table, wp_bootup_table_batches_completed);
        /* Result is set to NO_ERROR(0), if there are no rows */
        /* If row parsing failed, go with inifinite loop to get killed on hearbeat expiry*/
        if (result) {
            WALLY_LOG(AL_CRITICAL, "WP: could not read rows: %s", wally_error_strings[result]);
            while ((pres = PQgetResult((PGconn *)(wp_conn->db_conn)))) {
                WALLY_LOG(AL_NOTICE, "Toss result %s:%s",
                        PQresStatus(PQresultStatus(pres)),
                        PQresultErrorMessage(pres));
                PQclear(pres);
                pres = NULL;
            }
            result = WALLY_RESULT_NO_ERROR;
            break;
        }

        /* Cleanup results after read */
        while ((pres = PQgetResult((PGconn *)(wp_conn->db_conn)))) {
            WALLY_LOG(AL_NOTICE, "Toss result %s:%s",
                      PQresStatus(PQresultStatus(pres)),
                      PQresultErrorMessage(pres));
            PQclear(pres);
            pres = NULL;
        }

        if (read_more) {
            /* If there are more rows, then execute select
             *  with current max sequence with no state change
             */
            result = wp_send_read_select_nolock(wp_conn);
            /* If DB row read failed, go with inifinite loop to get killed on heartbeat expiry*/
            if (!result) {
                WALLY_LOG(AL_CRITICAL, "WP: Send nolock query failed");
                wally_error_handler(NULL, wp_conn, true,
                        E_MAX, SE_NONE);
                ZPATH_RWLOCK_UNLOCK(&(wp_db->lock), __FILE__, __LINE__);
                return;
            }
            /* Send command request to postgres and waiting
             * for results. set state to WALLY_RESULT_ASYNCHRONOUS */
            result = WALLY_RESULT_ASYNCHRONOUS;
        } else {
            /* If no more rows, change state to conn_table_read_commit
             * so that, do_cmd_complete set to call wally_xfer_response
             * for row response callback.
             */
            wp_conn->state = conn_table_read_commit;
        }
        break;
    case conn_table_read_commit:
        WP_STATS_CONN_STOP(wp_conn, table->db, table, tcl)
        wp_bootup_update_table_stats(table, wp_bootup_table_readlock_end);
        if (wp_conn->read_more) {
            wp_conn->read_more = 0;
            result = consume_and_call(wp_conn, wp_send_read_begin, "conn_table_read_commit (read more!)");
        } else {
            result = consume_and_call(wp_conn, NULL, "conn_table_read_commit");
        }
        break;
    case conn_table_gvr_poll_check:
        table->db_table_gvr_poll_time += (epoch_us() - wp_conn->db_op_start_time_us);
        WP_STATS_CONN_STOP(wp_conn, table->db, table, query)
        /* Check if there are any rows to read. If there are we will get reading... */
        pres = PQgetResult((PGconn *)(wp_conn->db_conn));
        if (PQresultStatus(pres) != PGRES_TUPLES_OK) {
            WALLY_LOG(AL_CRITICAL, "Could not read new row count");
            wally_error_handler(pres, wp_conn, true,
                    E_MAX, SE_NONE);
            ZPATH_RWLOCK_UNLOCK(&(wp_db->lock), __FILE__, __LINE__);
            return;
        }
        rows = PQntuples(pres);
        if (rows != 1) {
            /*Print critical error, free connection and wait for next polling */
            WALLY_LOG(AL_CRITICAL, "Could not read new sequence, got %d tuples",
                      (int) rows);
            /* Result cleanup */
            {
                PQclear(pres);
                pres = NULL;
                while ((pres = PQgetResult((PGconn *)(wp_conn->db_conn)))) {
                    WALLY_LOG(AL_NOTICE, "Toss result %s:%s",
                            PQresStatus(PQresultStatus(pres)),
                            PQresultErrorMessage(pres));
                    PQclear(pres);
                    pres = NULL;
                }
            }
            table->db_state = wp_db_table_poll_idle;
            result = WALLY_RESULT_NO_ERROR;
            break;
        } else {
            char *tmp_str = NULL;
            /* 1. Max sequence number */
            {
                tmp_str = (char *)PQgetvalue(pres, 0, 0);
                if (tmp_str) {
                    table->poll_max_sequence = strtoul(tmp_str, NULL, 0);
                } else {
                    // if NULL, skip polling by setting current max sequence
                    table->poll_max_sequence = table->current_max_sequence;
                }
            }
            /* 2. Lock count */
            {
                tmp_str = (char *)PQgetvalue(pres, 0, 1);
                if (tmp_str)
                {
                    table->poll_lock_count = strtoul(tmp_str, NULL, 0);
                } else {
                    // if NULL, assuming no lock held on table.
                    table->poll_lock_count = 0;
                }
            }

            /* Result cleanup */
            {
                PQclear(pres);
                pres = NULL;
                while ((pres = PQgetResult((PGconn *)(wp_conn->db_conn)))) {
                    WALLY_LOG(AL_NOTICE, "Toss result %s:%s",
                            PQresStatus(PQresultStatus(pres)),
                            PQresultErrorMessage(pres));
                    PQclear(pres);
                    pres = NULL;
                }
            }
            /* if new poll max sequence is -1, then
             *       -> table is exclusively locked by someone
             *       -> not able to read max sequence for the table
             *       -> Action: just skip current polling.
             * if new poll max sequence equal to current poll max sequence, then
             *       -> no changes to table
             *       -> Action, just skip current polling.
             */
            if ((table->poll_max_sequence == -1) || (table->poll_max_sequence == table->current_max_sequence)) {
                /* Increment counter for polling skip due to exclusive lock */
                if (table->poll_max_sequence == -1) {
                    table->db_poll_skip_excl_lock++;
                }
                /* Indication we are done working */
                result = WALLY_RESULT_NO_ERROR;
                /* last_read_complete_us is used for poll interval calculation */
                table->last_read_complete_us = epoch_us();
                table->db_state = wp_db_table_poll_idle;
            } else {
                if (!table->xpoll_first_detected_data_to_read_us) {
                    /* New data first detecting time is used for db_new_data_waiting_time_Xs stats */
                    table->xpoll_first_detected_data_to_read_us = epoch_us();
                    /* New data counter is used in itascurl command and stats also */
                    table->xpoll_check_new_data++;
                }

                if (table->poll_lock_count == 0)
                {
                    result =  wp_send_gvr_poll_select(wp_conn);
                    result = WALLY_RESULT_ASYNCHRONOUS;
                }
                else
                {
                    table->db_table_pending_txn_chk_start_time = epoch_us();
                    result =  wp_send_gvr_pending_transaction_check(wp_conn);
                    result = WALLY_RESULT_ASYNCHRONOUS;
                }
            }
        }
        break;
    case conn_table_gvr_pending_txn_check:
        WP_STATS_CONN_STOP(wp_conn, table->db, table, query)
        /* Check if there are any rows to read. If there are we will get reading... */
        pres = PQgetResult((PGconn *)(wp_conn->db_conn));
        if (PQresultStatus(pres) != PGRES_TUPLES_OK) {
            WALLY_LOG(AL_CRITICAL, "Could not read new row count");
            wally_error_handler(pres, wp_conn, true,
                    E_MAX, SE_NONE);
            ZPATH_RWLOCK_UNLOCK(&(wp_db->lock), __FILE__, __LINE__);
            return;
        }
        rows = PQntuples(pres);

        if (rows != 1) {
            WALLY_LOG(AL_CRITICAL, "Could not read new row count, got %d rows",
                      (int) rows);
            wally_error_handler(NULL, wp_conn, true,
                    E_MAX, SE_NONE);
            ZPATH_RWLOCK_UNLOCK(&(wp_db->lock), __FILE__, __LINE__);
            return;
        } else {
            /* Read a count... is it zero or not? */
            char *tmp_str;
            int64_t new_row_count;
            /* No need to check for max sequence, skip to poll lock count */
            tmp_str = (char *)PQgetvalue(pres, 0, 1);
            if (tmp_str )
            {
                new_row_count = strtoul(tmp_str, NULL, 0);
            } else {
                new_row_count = 0;
            }
            PQclear(pres);
            pres = NULL;
            while ((pres = PQgetResult((PGconn *)(wp_conn->db_conn)))) {
                WALLY_LOG(AL_NOTICE, "Toss result %s:%s",
                          PQresStatus(PQresultStatus(pres)),
                          PQresultErrorMessage(pres));
                PQclear(pres);
                pres = NULL;
            }
            if (new_row_count == 0) {
                int64_t pending_time = (epoch_us() - table->db_table_pending_txn_chk_start_time);
                table->db_table_pending_txn_waiting_time += pending_time;
                if (pending_time > table->db_table_max_waiting_time) {
                    table->db_table_max_waiting_time = pending_time;
                }
                table->db_table_pending_txn_chk_start_time = 0;
                table->db_table_pending_txn_chk_count++;

                result =  wp_send_gvr_poll_select(wp_conn);
                result = WALLY_RESULT_ASYNCHRONOUS;
            } else {
                table->db_state = wp_db_table_gvr_poll_pending_txn;
                table->last_pending_txn_gvr_chk_time = epoch_us();
                /* Fall through releases conn for other processing */
                result = WALLY_RESULT_NO_ERROR;
            }
        }
        break;
    case conn_table_gvr_poll_select:
        table->db_poll_table_read_time += (epoch_us() - wp_conn->db_op_start_time_us);
        WP_STATS_CONN_STOP(wp_conn, table->db, table, query)
        /* Read result. */
        WALLY_DEBUG_POSTGRES_EVENT("Event callback, %s/%s/%s (table_update)", wp_db->name, table->db_table_name, wp_conn->name);
        parse_start_time= epoch_us();
        result = wp_update_table_read_rows(wp_conn->table, wp_conn, 1, objects, indexes, &object_count, &read_more);
        table->db_poll_table_parse_time += (epoch_us() - parse_start_time);
        if (result) {
            WALLY_LOG(AL_CRITICAL, "WP: could not read rows: %s", wally_error_strings[result]);
            while ((pres = PQgetResult((PGconn *)(wp_conn->db_conn)))) {
                WALLY_LOG(AL_NOTICE, "Toss result %s:%s",
                        PQresStatus(PQresultStatus(pres)),
                        PQresultErrorMessage(pres));
                PQclear(pres);
                pres = NULL;
            }
            table->db_state = wp_db_table_poll_idle;
            table->xpoll_first_detected_data_to_read_us = 0;
            /* Fall through releases conn for other processing */
            result = WALLY_RESULT_NO_ERROR;
            break;
        }
        while ((pres = PQgetResult((PGconn *)(wp_conn->db_conn)))) {
            WALLY_LOG(AL_NOTICE, "Toss result %s:%s",
                      PQresStatus(PQresultStatus(pres)),
                      PQresultErrorMessage(pres));
            PQclear(pres);
            pres = NULL;
        }
        if (read_more) {
            result = wp_send_gvr_poll_select(wp_conn);
            if (!result) {
                WALLY_LOG(AL_CRITICAL, "WP: Send gvr poll nolock query failed");
                wally_error_handler(NULL, wp_conn, true, E_MAX, SE_NONE);
                ZPATH_RWLOCK_UNLOCK(&(wp_db->lock), __FILE__, __LINE__);
                return;
            }
            result = WALLY_RESULT_ASYNCHRONOUS;
        } else {
            result = WALLY_RESULT_NO_ERROR;
            table->last_read_complete_us = epoch_us();
            table->db_state = wp_db_table_poll_idle;
            table->last_successful_read_us = epoch_us();
            wally_postgres_update_data_waiting_stats(table, (epoch_us() -table->xpoll_first_detected_data_to_read_us));
            table->db_poll_table_total_time += table->xpoll_first_detected_data_to_read_us?(epoch_us() - table->xpoll_first_detected_data_to_read_us):0;
            table->xpoll_first_detected_data_to_read_us = 0;
        }
        break;
    case conn_table_poll_check:
        WP_STATS_CONN_STOP(wp_conn, table->db, table, query)
        /* Check if there are any rows to read. If there are we will get reading... */
        pres = PQgetResult((PGconn *)(wp_conn->db_conn));
        if (PQresultStatus(pres) != PGRES_TUPLES_OK) {
            WALLY_LOG(AL_CRITICAL, "Could not read new row count");
            wally_error_handler(pres, wp_conn, true,
                    E_MAX, SE_NONE);
            ZPATH_RWLOCK_UNLOCK(&(wp_db->lock), __FILE__, __LINE__);
            return;
        }
        rows = PQntuples(pres);
        if (rows != 1) {
            WALLY_LOG(AL_CRITICAL, "Could not read new row count, got %d rows",
                      (int) rows);
            wally_error_handler(NULL, wp_conn, true,
                    E_MAX, SE_NONE);
            ZPATH_RWLOCK_UNLOCK(&(wp_db->lock), __FILE__, __LINE__);
            return;
        } else {
            /* Read a count... is it zero or not? */
            char *tmp_str;
            int64_t new_row_count;
            tmp_str = (char *)PQgetvalue(pres, 0, 0);
            new_row_count = strtoul(tmp_str, NULL, 0);
            PQclear(pres);
            pres = NULL;
            while ((pres = PQgetResult((PGconn *)(wp_conn->db_conn)))) {
                WALLY_LOG(AL_NOTICE, "Toss result %s:%s",
                          PQresStatus(PQresultStatus(pres)),
                          PQresultErrorMessage(pres));
                PQclear(pres);
                pres = NULL;
            }
            if (new_row_count > 0) {
                if (!table->xpoll_first_detected_data_to_read_us) {
                    table->xpoll_first_detected_data_to_read_us = epoch_us();
                    table->xpoll_check_new_data++;
                }
                result =  wp_send_poll_begin(wp_conn);
                result = WALLY_RESULT_ASYNCHRONOUS;
            } else {
                /* Fall through releases conn for other processing */
                result = WALLY_RESULT_NO_ERROR; /* Indication we are done working */
                table->last_read_complete_us = epoch_us();
                table->db_state = wp_db_table_poll_idle;
            }
        }
        break;
    case conn_table_poll_begin:
        WP_STATS_CONN_STOP(wp_conn, table->db, table, tcl)
        /* Consume result. */
        result = consume_and_call(wp_conn, wp_send_poll_lock, "conn_table_poll_begin");
        break;
    case conn_table_poll_lock:
        WP_STATS_CONN_STOP(wp_conn, table->db, table, lock)
        /* Consume result. */
        result = consume_and_call(wp_conn, wp_send_poll_select, "conn_table_poll_lock");
        if (result == WALLY_RESULT_NO_ERROR) {
            /* Could not get lock... We would have gotten async if the
             * select had gone off. And if something worse happened,
             * we wouldn't get here at all */
            table->xpoll_trylock_fails++;
            table->xpoll_repeated_trylock_fail_count++;
            table->sequence_is_being_read = 0;
            table->xpoll_last_trylock_failed_us = epoch_us();
            if (wp_send_poll_rollback(wp_conn) == 0) {
                /* Failure to send rollback */
                WALLY_LOG(AL_CRITICAL, "WP: could not send rollback, table %s", table->db_table_name);
                wally_error_handler(NULL, wp_conn, true,
                        E_MAX, SE_NONE);
                ZPATH_RWLOCK_UNLOCK(&(wp_db->lock), __FILE__, __LINE__);
                return;
            }
            result = WALLY_RESULT_ASYNCHRONOUS;
        } else {
            table->xpoll_repeated_trylock_fail_count = 0;
            table->xpoll_last_read_us = epoch_us();
            table->xpoll_last_trylock_failed_us = 0;
        }

        break;
    case conn_table_poll_select:
        table->db_poll_table_read_time += (epoch_us() - wp_conn->db_op_start_time_us);
        WP_STATS_CONN_STOP(wp_conn, table->db, table, query)
        /* Read result. */
        WALLY_DEBUG_POSTGRES_EVENT("Event callback, %s/%s/%s (table_update)", wp_db->name, table->db_table_name, wp_conn->name);
        parse_start_time= epoch_us();
        result = wp_update_table_read_rows(wp_conn->table, wp_conn, 1, objects, indexes, &object_count, &read_more);
        table->db_poll_table_parse_time += (epoch_us() - parse_start_time);

        if (result) {
            WALLY_LOG(AL_CRITICAL, "WP: could not read rows: %s", wally_error_strings[result]);
            while ((pres = PQgetResult((PGconn *)(wp_conn->db_conn)))) {
                WALLY_LOG(AL_NOTICE, "Toss result %s:%s",
                        PQresStatus(PQresultStatus(pres)),
                        PQresultErrorMessage(pres));
                PQclear(pres);
                pres = NULL;
            }
            table->xpoll_first_detected_data_to_read_us = 0;
            if (wp_send_poll_rollback(wp_conn) == 0) {
                /* Failure to send rollback */
                WALLY_LOG(AL_CRITICAL, "WP: could not send rollback, table %s", table->db_table_name);
                wally_error_handler(NULL, wp_conn, true,
                        E_MAX, SE_NONE);
                ZPATH_RWLOCK_UNLOCK(&(wp_db->lock), __FILE__, __LINE__);
                return;
            }
            result = WALLY_RESULT_ASYNCHRONOUS;
            break;
        }
        if (read_more) wp_conn->read_more = 1;
        while ((pres = PQgetResult((PGconn *)(wp_conn->db_conn)))) {
            WALLY_LOG(AL_NOTICE, "Toss result %s:%s",
                      PQresStatus(PQresultStatus(pres)),
                      PQresultErrorMessage(pres));
            PQclear(pres);
            pres = NULL;
        }
        wp_send_poll_commit(wp_conn);
        result = WALLY_RESULT_ASYNCHRONOUS;
        break;
    case conn_table_poll_commit:
        WP_STATS_CONN_STOP(wp_conn, table->db, table, tcl)
        /* Consume result. */
        if (wp_conn->read_more) {
            wp_conn->read_more = 0;
            result = consume_and_call(wp_conn, wp_send_poll_begin, "conn_table_poll_commit (read more!)");
        } else {
            table->last_successful_read_us = epoch_us();
            result = consume_and_call(wp_conn, NULL, "conn_table_poll_commit");
            wally_postgres_update_data_waiting_stats(table, (epoch_us()- table->xpoll_first_detected_data_to_read_us));
            table->db_state = wp_db_table_poll_idle;
            table->db_poll_table_total_time += table->xpoll_first_detected_data_to_read_us?(epoch_us() - table->xpoll_first_detected_data_to_read_us):0;
            table->xpoll_first_detected_data_to_read_us = 0;
        }
        break;

    case conn_table_poll_rollback:
        WP_STATS_CONN_STOP(wp_conn, table->db, table, tcl)
        /* rollback (because we couldn't get lock)  */
        result = consume_and_call(wp_conn, NULL, "conn_table_poll_rollback");
        table->db_state = wp_db_table_poll_idle;
        break;


    case conn_table_resynchronize:
        WALLY_DEBUG_POSTGRES_EVENT("Event callback, %s/%s/%s (table_resync)", wp_db->name, table->db_table_name, wp_conn->name);
        result = postgres_update_table_state_machine(wp_conn->table, wp_conn);
        break;

    case conn_table_minseq_update:
        WP_STATS_CONN_STOP(wp_conn, table->db, table, update);
        WALLY_DEBUG_POSTGRES_EVENT("Event callback, %s/%s/%s (conn_table_minseq_update)",
                                   wp_db->name, table->db_table_name, wp_conn->name);
        pres = PQgetResult((PGconn *)(wp_conn->db_conn));
        if ((PQresultStatus(pres) != PGRES_TUPLES_OK) &&
            (PQresultStatus(pres) != PGRES_COMMAND_OK)) {
            WALLY_LOG(AL_ERROR, "Could not update row, status = %s : %s",
                      PQresStatus(PQresultStatus(pres)),
                      PQresultErrorMessage(pres));
            table->min_squence_last = table->min_squence_last_rollback;
        } else {
            WALLY_DEBUG_POSTGRES("Update min seq complete, %s/%s/%s", wp_db->name, table->db_table_name, wp_conn->name);
        }
        PQclear(pres);
        pres = NULL;

        result = WALLY_RESULT_NO_ERROR;
        break;

    case conn_table_cleanup_update:
        WP_STATS_CONN_STOP(wp_conn, table->db, table, update);
        WALLY_DEBUG_POSTGRES_EVENT("Event callback, %s/%s/%s (conn_table_cleanup_update)",
                                   wp_db->name, table->db_table_name, wp_conn->name);
        pres = PQgetResult((PGconn *)(wp_conn->db_conn));
        key_int = table->cleanup_buf[table->cleanup_index].key_int;
        seq = table->cleanup_buf[table->cleanup_index].sequence;
        if ((PQresultStatus(pres) != PGRES_TUPLES_OK) &&
            (PQresultStatus(pres) != PGRES_COMMAND_OK)) {
            WALLY_LOG(AL_ERROR, "wally cleanup: Could not update row, seq=%"PRId64", key_int=%"PRId64", status = %s : %s",
                      seq,
                      key_int,
                      PQresStatus(PQresultStatus(pres)),
                      PQresultErrorMessage(pres));
            reset_cleanup(table, table->cleanup_buf[table->cleanup_index].sequence);
        } else {
            table->cleanup_index++;
            if (table->cleanup_index >= table->cleanup_count) {
                reset_cleanup(table, table->cleanup_buf[table->cleanup_count-1].sequence + 1);
            } else {
                table->cleanup_state = cleanup_start;
            }
            WALLY_DEBUG_TABLE_CLEANUP("wally cleanup: Update row complete, %s/%s/%s, cleanup state=%d, cleanup index=%d"
                                      ", seq=%"PRId64", key_int=%"PRId64,
                                      wp_db->name, table->db_table_name, wp_conn->name, table->cleanup_state,
                                      table->cleanup_index-1, seq, key_int);
        }
        PQclear(pres);
        pres = NULL;

        result = WALLY_RESULT_NO_ERROR;
        break;

    case conn_table_delete_sequence:
        WP_STATS_CONN_STOP(wp_conn, table->db, table, delete);
        WALLY_DEBUG_POSTGRES_EVENT("Event callback, %s/%s/%s (table_delete_sequence)", wp_db->name, table->db_table_name, wp_conn->name);
        pres = PQgetResult((PGconn *)(wp_conn->db_conn));
        if ((PQresultStatus(pres) != PGRES_TUPLES_OK) &&
            (PQresultStatus(pres) != PGRES_COMMAND_OK)) {
            WALLY_LOG(AL_ERROR, "wally cleanup: Could not delete sequence rows, status = %s : %s",
                      PQresStatus(PQresultStatus(pres)),
                      PQresultErrorMessage(pres));
        } else {
            WALLY_LOG(AL_NOTICE, "Deleted rows complete, %s/%s/%s", wp_db->name, table->db_table_name, wp_conn->name);
        }
        PQclear(pres);
        pres = NULL;
        wp_conn->table->sequence_deletion_in_progress = 0;
        wp_conn->table->state = table_ready;
        result = WALLY_RESULT_NO_ERROR;
        break;
    case conn_idle:
        WALLY_DEBUG_POSTGRES_EVENT("Event callback, %s/%s/%s (conn_idle)", wp_db->name, table->db_table_name, wp_conn->name);
        /* Do nothing- probably just a notify coming in. */
        result = WALLY_RESULT_NO_ERROR;
        break;

    case conn_table_batch_write:
        WALLY_DEBUG_POSTGRES_EVENT("Event callback, %s/%s/%s (conn_table_batch_write)", wp_db->name, table->db_table_name, wp_conn->name);
        struct wp_conn_pg_specific *pgs = wp_conn->cookie;
        int queue_index = wp_conn->write_q_index;
        int has_error = 0;
        const char *ptr = NULL;
        origin->wq_info[queue_index].query_execution_time += (epoch_us() - pgs->query_start_time);
        char sqlstate[WP_MAX_ERROR_CODE_LEN];

        while ((pres = PQgetResult((PGconn *)(wp_conn->db_conn)))) {
            if (PQresultStatus(pres) != PGRES_COMMAND_OK) {
                ptr = PQresultErrorField(pres, PG_DIAG_SQLSTATE);
                strncpy(sqlstate,ptr,WP_MAX_ERROR_CODE_LEN);
                sqlstate[WP_MAX_ERROR_CODE_LEN-1] = '\0';
                has_error = 1;
            }
            PQclear(pres);
            pres = NULL;
        }

        // Command sucess
        if (has_error == 0) {
            origin->wq_info[queue_index].last_sequence = pgs->last_sequence;
            origin->wq_info[queue_index].total_success_batch++;
            origin->wq_info[queue_index].retry = 0;
            if(origin->wq_info[queue_index].total_failed_batch == 0) {
                origin->wq_info[queue_index].batch_size = wally_gbl_cfg.write_batch_size;
            }
            origin->wq_info[wp_conn->write_q_index].last_batch++;
            for (int cnt = 0; cnt < pgs->write_objects_count; cnt++) {
				wp_release_write_object(pgs->write_objects[cnt], 0);
                pgs->write_objects[cnt] = NULL;
            }

            int max_batch_per_con = WP_WRITE_BATCH_MAX_ROWS/wally_gbl_cfg.write_batch_size;
            if (origin->wq_info[wp_conn->write_q_index].last_batch > max_batch_per_con) {
                // let other tables use this connection for a while, since table writes are bound to connections.
                result = WALLY_RESULT_NO_ERROR;
                /* Will fall though to process notifications. (And add connection back to free queue) */
                break;
            } else {
                /* write next batch */
                result = wp_write_next_batch_on_conn(wp_db, wp_conn, queue_index);
            }
            break;
        } else {
           if (strcmp(sqlstate, WP_CONFLICT_ERROR) == 0) {
                origin->wq_info[queue_index].batch_row_conflict ++;
                result = wp_remove_duplicate_write_object(wp_db,wp_conn,queue_index);
                if(result == WALLY_RESULT_BAD_ARGUMENT) {
                    for (int cnt = pgs->write_objects_count-1; cnt >= 0; cnt--) {
                       wally_origin_enqueue_write_object(wp_db->db_to_wally_cookie, queue_index, pgs->write_objects[cnt], 1);
					   wp_release_write_object(pgs->write_objects[cnt], 1);
                    }
                    result = WALLY_RESULT_NO_ERROR;
                    origin->wq_info[queue_index].total_failed_batch ++;
                }
                break;
           } else {
                WALLY_LOG(AL_ERROR, "Command not okay? %s/%s error code %s conn-name %s retry count %"PRId64" reset batch size to 1"
                           , wp_db->name,table->db_table_name,sqlstate,wp_conn->name,origin->wq_info[queue_index].retry);
                /* In case of error reset batch size to 1 */
                origin->wq_info[queue_index].batch_size = 1;
                origin->wq_info[queue_index].retry++;
                origin->wq_info[queue_index].total_failed_batch ++;
                if( origin->wq_info[queue_index].retry <= WP_BATCH_RETRY_COUNT ) {
                    for (int k = pgs->write_objects_count-1; k >= 0; k--) {
                        wally_origin_enqueue_write_object(wp_db->db_to_wally_cookie, queue_index, pgs->write_objects[k], 1);
					    wp_release_write_object(pgs->write_objects[k], 1);
                    }
                } else {
                    WALLY_LOG(AL_ERROR,"Not able to recover losing row table name %s sequence number %"PRId64"",table->db_table_name,pgs->last_sequence);
                    origin->wq_info[queue_index].retry = 0;
                    origin->wq_info[wp_conn->write_q_index].count_fail += pgs->write_objects_count;
                    wally_error_handler(NULL, wp_conn, true, E_LDP_WRITE_FAIL, SE_NONE);
                }
                pgs->write_objects_count= 0;
                result = WALLY_RESULT_NO_ERROR;
           }
        }
        break;
    case conn_table_write:
        WALLY_DEBUG_POSTGRES_EVENT("Event callback, %s/%s/%s (table_write)", wp_db->name, table->db_table_name, wp_conn->name);
        /* Recovery update did not return any error even if no rows updated */
        /* Table always exists in mic and no case with tables_does_not_exists */
        //fprintf(stderr, "WP: Event callback: Write\n");
        while ((pres = PQgetResult((PGconn *)(wp_conn->db_conn)))) {
            if (PQresultStatus(pres) != PGRES_COMMAND_OK) {
                /* XXX Log */
                if (wp_conn->write_object) {
                    if (wp_conn->write_object->db_op == db_oper_rowargo_object) {
						char dump[8000];
						if (argo_object_dump(wp_conn->write_object->data.row_to_write, dump, sizeof(dump), NULL, 0) == ARGO_RESULT_NO_ERROR) {
							WALLY_LOG(AL_ERROR, "Command not okay?, argo object : %s, %s/%s/%s (id = %ld) ",
									  dump, wp_db->name, table->db_table_name, wp_conn->name,
									  (long)argo_object_get_sequence(wp_conn->write_object->data.row_to_write));
						}
					} else {
						wp_conn->table->db_lookup_table_total_time_us += epoch_us() - wp_conn->table->db_lookup_table_start_time_us;
						wp_conn->table->db_lookup_table_total_operations_failed++;
						wp_conn->table->db_lookup_table_start_time_us = 0;
						WALLY_DEBUG_MIC("Command not okay? lookup table update: %s, column_index=%d, sequence=%"PRId64", value = %s",
								wp_conn->write_object->data.lookup_table_write.table_name,
								wp_conn->write_object->data.lookup_table_write.column_index,
								wp_conn->write_object->data.lookup_table_write.max_sequence_seen, // int64_t max_sequence_seen;
								wp_conn->write_object->data.lookup_table_write.value);
					}
                } else {
                    WALLY_LOG(AL_DEBUG, "Command not okay? %s/%s/%s", wp_db->name, table->db_table_name, wp_conn->name);
                }
                origin->wq_info[wp_conn->write_q_index].count_fail++;
                // WALLY_LOG(AL_ERROR, "%s", PQresultVerboseErrorMessage(pres, PQERRORS_VERBOSE, PQSHOW_CONTEXT_ALWAYS));
            } else {
				//If command is okay
				if (wp_conn->write_object) {
                    if (wp_conn->write_object->db_op == db_oper_lookuptable_update) {
						wp_conn->table->db_lookup_table_total_time_us += epoch_us() - wp_conn->table->db_lookup_table_start_time_us;
						wp_conn->table->db_lookup_table_total_operations_successful++;
						wp_conn->table->db_lookup_table_start_time_us = 0;
					}
				}
			}

            PQclear(pres);
            pres = NULL;
        }
        /* Recovery requires only update. skipping insert by freeing write_object */
        if (wp_conn->write_object) {
            if (wp_conn->write_object->db_op == db_oper_recovery_update) {
                wp_release_write_object(wp_conn->write_object, 0);
                wp_conn->write_object = NULL;
            }
        }
        if (wp_conn->write_object) {
            WP_STATS_CONN_STOP(wp_conn, table->db, table, update)
            /* Need to perform insert update... */
            result = wp_write_object_insert(wp_conn, wp_conn->write_object);
            if ((result == WALLY_RESULT_NO_ERROR) || (result == WALLY_RESULT_ASYNCHRONOUS)) {
				wp_release_write_object(wp_conn->write_object, 0);
                wp_conn->write_object = NULL;
            } else {
                /* XXX Log. Bad. */
                WALLY_LOG(AL_ERROR, "Bad");
                result = WALLY_RESULT_ERR;
            }
        } else {
            WP_STATS_CONN_STOP(wp_conn, table->db, table, insert)
            // a row was written completely.
            origin->wq_info[wp_conn->write_q_index].last_batch++;
            if (origin->wq_info[wp_conn->write_q_index].last_batch >= WP_WRITE_BATCH_MAX_ROWS) {
                // let other tables use this connection for a while, since table writes are bound to connections.
                result = WALLY_RESULT_NO_ERROR;
                /* Will fall though to process notifications. (And add connection back to free queue) */
                break;
            }

            int repeats = 1000;
            /* We set repeats to be a reasonable number so that we
             * clear out non-writable entries faster. Otherwise we are
             * waiting for a timeout to grab more rows to write. */
            while (repeats > 0) {
                /* Insert complete. Need to see if we have another row to write. */
                wp_conn->write_object = wally_origin_dequeue_write_object_specific(wp_db->db_to_wally_cookie, wp_conn->write_q_index);
                if (wp_conn->write_object) {
                    result = wp_write_object_update(wp_conn, wp_conn->write_object);
                    if ((result == WALLY_RESULT_NO_ERROR) || (result == WALLY_RESULT_ASYNCHRONOUS)) {
                        /* Do nothing... Callback is needed first. */
                        break;
                    } else {
                        if (result == WALLY_RESULT_WOULD_BLOCK) {
                            /* This is okay- Put our object back, we'll get to it later. */
                            wally_origin_enqueue_write_object(wp_db->db_to_wally_cookie, wp_conn->write_q_index, wp_conn->write_object, 1);
                            /* argo_object_release is required for earlier wally_origin_dequeue_write_row */
							wp_release_write_object(wp_conn->write_object, 1);
							wp_conn->write_object = NULL;
                            result = WALLY_RESULT_NO_ERROR;
                            /* state will get more cleaned up in the ==
                             * WALLY_RESULT_NO_ERROR case, lower. */
                            break;
                        } else {
                            origin->wq_info[wp_conn->write_q_index].count_fail++;

                            /* Can't write the row- probably because the
                             * table is not yet initialized. Toss the
                             * row */
                            if (wp_conn->write_object->db_op == db_oper_rowargo_object) {
								WALLY_LOG(AL_ERROR, "Bad: %s (Table %s not initialized? Tossing row, repeats = %d)", wally_error_strings[result], argo_object_get_type(wp_conn->write_object->data.row_to_write), repeats);
							} else if (wp_conn->write_object->db_op == db_oper_lookuptable_update) {
								WALLY_LOG(AL_ERROR, "Bad: %s (Table %s not initialized? Tossing row, repeats = %d)", wally_error_strings[result], wp_conn->write_object->data.lookup_table_write.table_name, repeats);
							} else if (wp_conn->write_object->db_op == db_oper_recovery_update) {
								WALLY_LOG(AL_ERROR, "Bad: %s (Table %s not initialized? Tossing row, repeats = %d)", wally_error_strings[result], wp_conn->write_object->data.recovery_update.table_name, repeats);
                            }
							wp_release_write_object(wp_conn->write_object, 0);
							wp_conn->write_object = NULL;
                            result = WALLY_RESULT_NO_ERROR;
                            if (repeats > 0) {
                                repeats--;
                                continue;
                            }
                            break;
                        }
                    }
                } else {
                    result = WALLY_RESULT_NO_ERROR;
                    /* Will fall though to process notifications. (And add
                     * connection back to free queue) */
                    break;
                }
            }
        }
        break;
    default:
        WALLY_LOG(AL_ERROR, "Event callback, %s/%s/%s (table_WTF)", wp_db->name, table->db_table_name, wp_conn->name);
        /* XXX LOG */
        break;
    }


    if (result == WALLY_RESULT_NO_ERROR) {
        /* Not blocking, nothing outstanding, we can use this for
         * notification processing. */
        PGnotify *n;
        struct wp_table *t;

        /* Release connection- for block, err cases, we do not release it. */
        if (wp_conn->state != conn_idle) {
            //fprintf(stderr, "WP: Release conn\n");
            if (wp_conn->state == conn_table_read_commit) {
                do_cmd_complete = 1;
            }
            {
                PGresult *res2;
                while ((res2 = PQgetResult((PGconn *)(wp_conn->db_conn)))) {
                    WALLY_LOG(AL_CRITICAL, "Flushing extra data: %s:%d", wp_conn->wp_db->name, wp_conn->my_index);
                    PQclear(res2);
                    res2 = NULL;
                }
            }
            wally_db_insert_free_conn (wp_conn->wp_db, wp_conn);
            if (wp_db->is_blocking) {
                //fprintf(stderr, "WP:   and unblock\n");
                do_unblock = 1;
                wp_db->is_blocking = 0;
            }
        }

        while ((n = PQnotifies((PGconn *)(wp_conn->db_conn)))) {
            /* Will be for a different table... */
            char str[200];
            int len;
            int len2;
            len = strlen(n->relname);
            len2 = 7; //strlen("_notify")
            len -= len2;
            if (len >= sizeof(str)) {
                /* Erk. */
                /* XXX LOG */
            } else {
                memcpy(str, n->relname, len);
                str[len] = 0;
                /* Look up the table.. */
                t = argo_hash_lookup(wp_db->tables_by_argo_name, str, len, NULL);
                if (t) {
                    /* Mark this table as needing to be read. */
                    //fprintf(stderr, "WP: Notify: Need to read table %s: Last index = %ld\n", str, (long) t->current_max_sequence);
                } else {
                    /* XXX LOG. */
                    WALLY_LOG(AL_ERROR, "Could not find notification table %s", str);
                }
            }
        }
    }

    /* Before dropping lock, track our cookies, tables, etc for callbacks as required. */
    cmd_id = wp_conn->wally_request_id;
    cmd_row_count = wp_conn->row_count;
    cmd_cookie = wp_db->db_to_wally_cookie;

    int is_true_origin = 0;
    is_true_origin = wp_db->is_true_origin;

    ZPATH_RWLOCK_UNLOCK(&(wp_db->lock), __FILE__, __LINE__);

    if (IS_WALLY_LAYER_ENABLED)
    {
        if (object_count)
        {
            if (table && table->argo_object_name) {
                if (wp_db->zthread ) {
                    now_s = monotime_s();
                    delta_s = (now_s - wp_db->zthread->last_heartbeat_monotime_s);
                    if (delta_s > wally_max_hb_miss_timeout_s) {
                        WALLY_DEBUG_HB_TIMEOUT("Postgres transfer row heartbeat threshold reached, delta_s %" PRId64 "" ,
                                delta_s);
                        origin->wally->batch_hb_count++;
                        zthread_heartbeat(wp_db->zthread);
                    }
                }
            }
            if (table)
            {
                process_start_time = epoch_us();
                /* wally bootup stats row_store_time and row_cb_time will be empty if new thread is enabled */
				wally_table_queue_enqueue_row_objects(cmd_cookie, table->argo_object_name, objects, indexes, object_count);
                table->db_poll_table_processing_time += (epoch_us() - process_start_time);
            }
        }

        if (!is_true_origin && object_count && table && table->argo_object_name) {
            wally_table_queue_enqueue_db_paused_seq(cmd_cookie, table->argo_object_name);
        }
        if (do_cmd_complete)
        {
            if (wp_db->zthread) {
                now_s = monotime_s();
                delta_s = (now_s - wp_db->zthread->last_heartbeat_monotime_s);
                if (delta_s > wally_max_hb_miss_timeout_s) {
                    WALLY_DEBUG_HB_TIMEOUT("Postgres response row heartbeat threshold reached, delta_s %" PRId64 "" ,
                            delta_s);
                    origin->wally->batch_hb_count++;
                    zthread_heartbeat(wp_db->zthread);
                }
            }
            /* Deregister stats after reading all the rows for the table during bootup */
            if (get_wally_app_state() == wally_state_tables_loading)
            {
                if(table && table->wally_db_table_bootup_stats_structure)
                {
                    argo_log_deregister_structure(table->wally_db_table_bootup_stats_structure, 1);
                    table->wally_db_table_bootup_stats_structure = NULL;
                }
            }
            if (table)
            {
                wp_bootup_update_table_stats(table, wp_bootup_table_total_end);
                process_start_time = epoch_us();
				if (table->argo_object_name) {
					wally_table_queue_enqueue_xfer_response(cmd_cookie,
															cmd_id,
															cmd_row_count,
															true,
															table->argo_object_name);
				}
                table->db_poll_table_processing_time += (epoch_us() - process_start_time);
            }
        }
    }
    else
    {
        process_start_time = epoch_us();
        /* Do row callbacks first, then command complete responses, then resume transmit callback.
         * Note that this routine may take long time to finish, especially when table is being fully loaded. */
        for (i = 0; i < object_count; i++) {
            if (table && table->argo_object_name) {
                if (wp_db->zthread && (i % wally_max_hb_iteration) == 0) {
                    now_s = monotime_s();
                    delta_s = (now_s - wp_db->zthread->last_heartbeat_monotime_s);
                    if (delta_s > wally_max_hb_miss_timeout_s) {
                        WALLY_DEBUG_HB_TIMEOUT("Postgres transfer row heartbeat threshold reached, iteration %d delta_s %" PRId64 "" ,
                                i, delta_s);
                        origin->wally->batch_hb_count++;
                        zthread_heartbeat(wp_db->zthread);
                    }
                }
                /* Total start time is not equal to 0 only during bootup */
                if (get_wally_app_state() == wally_state_tables_loading)
                {
                    int64_t row_store_time = 0, row_cb_time = 0;
                    wally_xfer_row(cmd_cookie, table->argo_object_name, objects[i], indexes[i], &row_store_time, &row_cb_time);
                    wp_bootup_update_table_row_stats(table, wp_bootup_table_rowstore_time, row_store_time);
                    wp_bootup_update_table_row_stats(table, wp_bootup_table_rowcb_time, row_cb_time);
                } else {
                    wally_xfer_row(cmd_cookie, table->argo_object_name, objects[i], indexes[i], NULL, NULL);
                }
            } else {
                /* XXX LOG. If we have row callbacks, we REALLY should have had a table. */
                WALLY_LOG(AL_ERROR, "Row callbacks with no table");
            }
            argo_object_release(objects[i]);
        }
        if (table && object_count) {
            table->db_poll_table_processing_time += (epoch_us() - process_start_time);
        }

        if (!is_true_origin && object_count && table && table->argo_object_name) {
            if (wally_update_paused_seq(cmd_cookie, table->argo_object_name)) {
                WALLY_LOG(AL_ERROR, "wally_pause_debug: Could not update paused_seq for postgres table: %s!",
                        table->argo_object_name);
            }
        }

        if (do_cmd_complete) {
            if (wp_db->zthread) {
                now_s = monotime_s();
                delta_s = (now_s - wp_db->zthread->last_heartbeat_monotime_s);
                if (delta_s > wally_max_hb_miss_timeout_s) {
                    WALLY_DEBUG_HB_TIMEOUT("Postgres response row heartbeat threshold reached, delta_s %" PRId64 "" ,
                            delta_s);
                    origin->wally->batch_hb_count++;
                    zthread_heartbeat(wp_db->zthread);
                }
            }

            if (table)
            {
                wp_bootup_update_table_stats(table, wp_bootup_table_total_end);
                /* Deregister stats after reading all the rows for the table during bootup */
                if (get_wally_app_state() == wally_state_tables_loading)
                {
                    if(table->wally_db_table_bootup_stats_structure)
                    {
                        argo_log_deregister_structure(table->wally_db_table_bootup_stats_structure, 1);
                        table->wally_db_table_bootup_stats_structure = NULL;
                    }
                }
                process_start_time = epoch_us();
				if (table->argo_object_name) {
					wally_xfer_response(cmd_cookie, cmd_id, cmd_row_count, true, table->argo_object_name);
				} else {
					WALLY_LOG(AL_NOTICE, "Table name is NULL in FOHH Xfer Response recevied from remote wally(origin/global)");
					WALLY_LOG(AL_NOTICE, "Please upgrade origin & global wally to same version or higher version");
					wally_oper_event_post(E_TERMINATE, SE_NONE, NULL);

				}
                table->db_poll_table_processing_time += (epoch_us() - process_start_time);
            }
        }
    }
    if (do_unblock) {
        if (wp_db->zthread) {
            now_s = monotime_s();
            delta_s = (now_s - wp_db->zthread->last_heartbeat_monotime_s);
            if (delta_s > wally_max_hb_miss_timeout_s) {
                WALLY_DEBUG_HB_TIMEOUT("Postgres transfer resume row heartbeat threshold reached, delta_s %" PRId64 "" ,
                        delta_s);
                origin->wally->batch_hb_count++;
                zthread_heartbeat(wp_db->zthread);
            }
        }

        WALLY_DEBUG_POSTGRES_FC("Unblock (wp = %s)",
                                wp_db->name);
        /*
         * Above Heartbeat tick will not able to handle,  if wally_xfer_resume_xmit
         * takes longer time due to large set of pending register/deregister entries.
         * */
        wally_xfer_resume_xmit(wp_db->db_to_wally_cookie);
    }

    return;
}


/*
 * SYNCHRONOUS
 *
 * This routine checks if the template database exists in local postgres or not
 *
 * Requires a validly connected DB.
 */
int wally_check_template_database_exists(PGconn *conn) {
    PGresult *pres;
    int exists = 0;
    char query_str[WALLY_BUFF_LEN] = { 0 };

    memset (query_str, 0, sizeof(query_str));
    snprintf(query_str, sizeof(query_str), "SELECT 1 FROM pg_database WHERE datname = '%s'",
            ITASCA_TEMPLATE_DB);
    pres = xPQexec(conn, query_str);
    if (!pres || PQresultStatus(pres) != PGRES_TUPLES_OK) {
        WALLY_LOG(AL_ERROR, "Could not %s", query_str);
        exists = 0;
    } else {
        WALLY_LOG(AL_INFO, " %s was successful.", query_str);
        exists = (PQntuples(pres) > 0);;
    }

    PQclear(pres);
    pres = NULL;
    return exists;
}

/*
 * SYNCHRONOUS
 *
 * Database creation routine. This routine just tries to create the
 * named database- doesn't do anything at all whatsoever beyond
 * that. No return error code, etc.
 *
 * Requires a validly connected, writable DB for doing the creation.
 */
static int wally_postgres_createdb(const char *db_name,
                                   const char *host,
                                   const char *user,
                                   const char *password)
{
    PGconn *pg_conn;
    PGresult *pres;

    int res;

    const char *wally_postgres_param_names[6];
    const char *wally_postgres_param_values[6];

    if ( (host) &&
         ( strcmp(host, "localhost") != 0 )) {
        wally_postgres_param_values[0] = host;
    } else {
        wally_postgres_param_values[0] = NULL;
    }
    wally_postgres_param_values[1] = user;
    wally_postgres_param_values[2] = "postgres";
    wally_postgres_param_values[3] = password;
    wally_postgres_param_values[4] = password ? "require" : NULL;
    wally_postgres_param_values[5] = NULL;

    if (password) {
        wally_postgres_param_names[0] = "host";
        wally_postgres_param_names[1] = "user";
        wally_postgres_param_names[2] = "dbname";
        wally_postgres_param_names[3] = "password";
        wally_postgres_param_names[4] = "sslmode";
        wally_postgres_param_names[5] = NULL;
    } else {
        wally_postgres_param_names[0] = "host";
        wally_postgres_param_names[1] = "user";
        wally_postgres_param_names[2] = "dbname";
        wally_postgres_param_names[3] = NULL;
        wally_postgres_param_names[4] = NULL;
        wally_postgres_param_names[5] = NULL;
    }

    pg_conn  = PQconnectdbParams((const char **)wally_postgres_param_names,
                                 (const char **)wally_postgres_param_values, 0);

    if (!pg_conn) {
        WALLY_LOG(AL_WARNING, "Cannot connect to db %s for creating db %s", wally_postgres_param_values[2], db_name);
        return WALLY_RESULT_ERR;
    }

    if (PQstatus(pg_conn) != CONNECTION_OK) {
        PQfinish(pg_conn);
        WALLY_LOG(AL_WARNING, "Connection to db %s for creating db %s is not CONNECTION_OK", wally_postgres_param_values[2], db_name);
        return WALLY_RESULT_ERR;
    }

    char tmp_str[300];
    if (wally_check_template_database_exists(pg_conn)) {
        snprintf(tmp_str, sizeof(tmp_str), "CREATE DATABASE %s TEMPLATE %s", db_name, ITASCA_TEMPLATE_DB);
    } else {
        snprintf(tmp_str, sizeof(tmp_str), "CREATE DATABASE %s ", db_name);
    }
    WALLY_LOG(AL_NOTICE, "Creating Database: %s ",tmp_str);
    WALLY_DEBUG_POSTGRES("CREATING DB: %s, using db %s", db_name, wally_postgres_param_values[2]);

    pres = xPQexec(pg_conn, tmp_str);
    if (!pres) {
        PQfinish(pg_conn);
        WALLY_LOG(AL_WARNING, "No result whatsoever from create database: %s", db_name);
        return WALLY_RESULT_ERR;
    }

    res = PQresultStatus(pres);
    if (res != PGRES_COMMAND_OK) {
        res = WALLY_RESULT_ERR;
        WALLY_LOG(AL_WARNING, "Command not okay, creating db %s", db_name);
    } else {
        res = WALLY_RESULT_NO_ERROR;
    }
    PQclear(pres);
    pres = NULL;
    PQfinish(pg_conn);

    return res;
}

void *wally_postgres_create(void *db_to_wally_cookie,
                            const char *host,
                            char *user,
                            const char *bad_dbname,
                            const char *thread_name,
                            char *password,
                            int nconns,
                            int is_row_writable,
                            int is_alterable,
                            int is_true_origin,
                            int is_endpoint,
                            int64_t polling_interval_us)
{
 return wally_postgres_create_with_schema(db_to_wally_cookie,
                                          host,
                                          user,
                                          bad_dbname,
                                          thread_name,
                                          password,
                                          NULL, // Pass schema as NULL
                                          nconns,
                                          is_row_writable,
                                          is_alterable,
                                          is_true_origin,
                                          is_endpoint,
                                          polling_interval_us);
}

/* Postgres DB connection configuration */
int wally_postgres_db_conn_config(struct wp_db *wp_db, char *schema, int32_t index)
{
    PGresult *pres = NULL;
    int fd = 0;
    char query_str[WALLY_BUFF_LEN] = { 0 };

	/* unless we are the true origin, turn off synchronous_commit so writes become a lot faster.*/
	if (!wp_db->is_true_origin && wally_postgres_synchronous_commit_off) {
		pres = xPQexec((PGconn *)(wp_db->connections[index].db_conn),
				"SET synchronous_commit=off");
		if (!pres || PQresultStatus(pres) != PGRES_COMMAND_OK) {
			WALLY_LOG(AL_ERROR, "Could not SET synchronous_commit=off. Write performance can be lower...");
		} else {
			WALLY_LOG(AL_INFO, " SET synchronous_commit=off was successful.");
		}
		if (pres) {
			PQclear(pres);
			pres = NULL;
		}
	}

	if (wally_gbl_cfg.statement_timeout &&
			!wp_db->is_alterable &&
			wp_db->is_true_origin) {
		memset (query_str, 0, sizeof(query_str));
		snprintf(query_str, sizeof(query_str), "SET statement_timeout TO %"PRId64"",
				(wally_gbl_cfg.statement_timeout * WALLY_MIN_TO_MILLISEC));
		pres = xPQexec((PGconn *)(wp_db->connections[index].db_conn), query_str);
		if (!pres || PQresultStatus(pres) != PGRES_COMMAND_OK) {
			WALLY_LOG(AL_ERROR, "Could not %s",
					query_str);
		} else {
			WALLY_LOG(AL_INFO, " %s was successful.",
					query_str);
		}
		if (pres) {
			PQclear(pres);
			pres = NULL;
		}
	}

	if (wally_gbl_cfg.lock_timeout &&
			!wp_db->is_alterable &&
			wp_db->is_true_origin) {
		memset (query_str, 0, sizeof(query_str));
		snprintf(query_str, sizeof(query_str), "SET lock_timeout TO %"PRId64"",
				(wally_gbl_cfg.lock_timeout * WALLY_MIN_TO_MILLISEC));
		pres = xPQexec((PGconn *)(wp_db->connections[index].db_conn), query_str);
		if (!pres || PQresultStatus(pres) != PGRES_COMMAND_OK) {
			WALLY_LOG(AL_ERROR, "Could not %s",
					query_str);
		} else {
			WALLY_LOG(AL_INFO, " %s was successful.",
					query_str);
		}
		if (pres) {
			PQclear(pres);
			pres = NULL;
		}
	}

	if (wally_gbl_cfg.idle_in_trans_session_timeout &&
			!wp_db->is_alterable &&
			wp_db->is_true_origin) {
		memset (query_str, 0, sizeof(query_str));
		snprintf(query_str, sizeof(query_str), "SET idle_in_transaction_session_timeout TO %"PRId64"",
				(wally_gbl_cfg.idle_in_trans_session_timeout * WALLY_MIN_TO_MILLISEC));
		pres = xPQexec((PGconn *)(wp_db->connections[index].db_conn), query_str);
		if (!pres || PQresultStatus(pres) != PGRES_COMMAND_OK) {
			WALLY_LOG(AL_ERROR, "Could not %s",
					query_str);
		} else {
			WALLY_LOG(AL_INFO, " %s was successful.",
					query_str);
		}
		if (pres) {
			PQclear(pres);
			pres = NULL;
		}
	}

	if (wally_gbl_cfg.idle_session_timeout &&
			!wp_db->is_alterable &&
			wp_db->is_true_origin) {
		memset (query_str, 0, sizeof(query_str));
		snprintf(query_str, sizeof(query_str), "SET idle_session_timeout TO %"PRId64"",
				(wally_gbl_cfg.idle_session_timeout * WALLY_MIN_TO_MILLISEC));
		pres = xPQexec((PGconn *)(wp_db->connections[index].db_conn), query_str);
		if (!pres || PQresultStatus(pres) != PGRES_COMMAND_OK) {
			WALLY_LOG(AL_ERROR, "Could not %s",
					query_str);
		} else {
			WALLY_LOG(AL_INFO, " %s was successful.",
					query_str);
		}
		if (pres) {
			PQclear(pres);
			pres = NULL;
		}
	}

	/* Set search path to specific schema if provided (only for wallyd to remote db connections). */
	if (schema) {
		memset (query_str, 0, sizeof(query_str));
		snprintf(query_str, sizeof(query_str), "SET search_path TO %s", schema);
		pres = xPQexec((PGconn *)(wp_db->connections[index].db_conn), query_str);
		if (!pres || PQresultStatus(pres) != PGRES_COMMAND_OK) {
			WALLY_LOG(AL_ERROR, "Could not set search path to %s schema", schema);
			if (pres) {
				PQclear(pres);
				pres = NULL;
			}
            return WALLY_RESULT_ERR;
		}
		if (pres) {
			PQclear(pres);
			pres = NULL;
		}
        if (wp_db->schema_name == NULL) {
            wp_db->schema_name = WALLY_STRDUP(schema, strnlen(schema, 200));
        }
		if (!wp_db->schema_name) {
            return WALLY_RESULT_ERR;
        }
		WALLY_LOG(AL_NOTICE, "Successfully set search path to %s", schema);
	}

	/* For each DB connection, attach its socket to event handler. */
	fd = PQsocket((PGconn *)(wp_db->connections[index].db_conn));
	wp_db->connections[index].ev_conn = event_new(wp_db->ev_base,
			fd,
			EV_PERSIST | EV_READ,
			wally_postgres_event_callback,
			&(wp_db->connections[index]));
	if (!(wp_db->connections[index].ev_conn)) {
        return WALLY_RESULT_ERR;
	}

	if (event_add(wp_db->connections[index].ev_conn, NULL)) {
        event_free(wp_db->connections[index].ev_conn);
        wp_db->connections[index].ev_conn = NULL;
        return WALLY_RESULT_ERR;
	}

	wally_db_insert_free_conn (wp_db, &(wp_db->connections[index]));
    WALLY_LOG(AL_NOTICE, "Successfully added event for DB %d", index);

    return WALLY_RESULT_NO_ERROR;
}

/*
 * Creation/initialization routine. This is a synchronous call, and
 * may take some time to complete. If postgres is not available, this
 * routine will keep trying to connect up to 30 times, with a second
 * delay between each time.
 *
 * Returns a "callout cookie" for use on calls from wally to this
 * origin DB.
 *
 * Is passed a cookie for use on calls from this origin DB to wally.
 *
 * table_name_prefix is a prefix to append to all table names. (Allows
 * for multiple copies of the same table from multiple applications)
 */
void *wally_postgres_create_with_schema(void *db_to_wally_cookie,
                                        const char *host,
                                        char *user,
                                        const char *bad_dbname,
                                        const char *thread_name,
                                        char *password,
                                        char *schema,
                                        int nconns,
                                        int is_row_writable,
                                        int is_alterable,
                                        int is_true_origin,
                                        int is_endpoint,
                                        int64_t polling_interval_us)
{
    struct wp_db *wp_db;
    struct timeval tv;
    int res = 0;
    int i = 0;
    int j = 0, ssl_index = 0;
    char keepalive_idle[WALLY_BUFF_LEN] = { 0 };
    char keepalives_interval[WALLY_BUFF_LEN] = { 0 };
    char keepalives_count[WALLY_BUFF_LEN] = { 0 };
    char user_timeout[WALLY_BUFF_LEN] = { 0 };
    PGresult *pres;

    static pthread_mutex_t local_lock = (pthread_mutex_t) PTHREAD_MUTEX_INITIALIZER;
    static int initialized = 0;

    const char *wally_postgres_param_names[WALLY_PSQL_PARAM_MAX_INDEX];
    const char *wally_postgres_param_values[WALLY_PSQL_PARAM_MAX_INDEX];
    char wally_postgres_good_db_name[256];
    struct zthread_info *zthread = zthread_self();

#if 0
    if (is_endpoint) {
        fprintf(stderr, "Creating postgres DB as endpoint: %s\n", bad_dbname);
    }
#endif // 0

    if (!bad_dbname || (strlen(bad_dbname) > sizeof(wally_postgres_good_db_name))) return NULL;
    for (i = 0; i < strlen(bad_dbname); i++) {
        if (bad_dbname[i] == '.' ||
            bad_dbname[i] == '-') {
            wally_postgres_good_db_name[i] = '_';
        } else {
            wally_postgres_good_db_name[i] = bad_dbname[i];
        }
    }
    wally_postgres_good_db_name[i] = 0;

    /* Initialize the variables */
    for (j = 0; j < WALLY_PSQL_PARAM_MAX_INDEX; j++) {
        wally_postgres_param_names[j] = NULL;
        wally_postgres_param_values[j] = NULL;
    }

    j = 0;
    if ( strcmp(host,"localhost") != 0 ) {
        wally_postgres_param_values[j++] = host;
    } else {
        wally_postgres_param_values[j++] = NULL;
    }
    wally_postgres_param_values[j++] = user;
    wally_postgres_param_values[j++] = wally_postgres_good_db_name;

    if (is_true_origin && !is_alterable && !disable_wally_tcp_config) {
        snprintf(keepalive_idle, sizeof(keepalive_idle), "%"PRId64"", tcp_keepalives_idle);
        snprintf(keepalives_interval, sizeof(keepalives_interval), "%"PRId64"", tcp_keepalives_interval);
        snprintf(keepalives_count, sizeof(keepalives_count), "%"PRId64"", tcp_keepalives_count);
        snprintf(user_timeout, sizeof(user_timeout), "%"PRId64"", tcp_user_timeout);

        wally_postgres_param_values[j++] = keepalive_idle;
        wally_postgres_param_values[j++] = keepalives_interval;
        wally_postgres_param_values[j++] = keepalives_count;
        wally_postgres_param_values[j++] = WALLY_KEEPALIVES;
        wally_postgres_param_values[j++] = user_timeout;
    }
    wally_postgres_param_values[j++] = password;
    wally_postgres_param_values[j++] = password ? "require" : NULL;
    wally_postgres_param_values[j++] = NULL;

    if (j > WALLY_PSQL_PARAM_MAX_INDEX) {
        return NULL;
    }

    j = 0;
    wally_postgres_param_names[j++] = "host";
    wally_postgres_param_names[j++] = "user";
    wally_postgres_param_names[j++] = "dbname";
    if (is_true_origin && !disable_wally_tcp_config) {
        wally_postgres_param_names[j++] = "keepalives_idle";
        wally_postgres_param_names[j++] = "keepalives_interval";
        wally_postgres_param_names[j++] = "keepalives_count";
        wally_postgres_param_names[j++] = "keepalives";
        wally_postgres_param_names[j++] = "tcp_user_timeout";
    }
    if (password) {
        wally_postgres_param_names[j++] = "password";
        ssl_index = j; /* Store the SSL index to log the values */
        wally_postgres_param_names[j++] = "sslmode";
    }
    wally_postgres_param_names[j++] = NULL;

    if (j > WALLY_PSQL_PARAM_MAX_INDEX) {
        return NULL;
    }

    WALLY_LOG(AL_NOTICE, "Opening %d connection(s) to host=%s, user=%s, dbname=%s",
            nconns,
            host,
            user,
            wally_postgres_good_db_name);
    if (is_true_origin && !disable_wally_tcp_config) {
        WALLY_LOG(AL_NOTICE, "Postgres TCP parameters %s = %s %s = %s %s = %s %s = %s %s = %s",
                wally_postgres_param_values[3]? wally_postgres_param_names[3]:"NULL",
                wally_postgres_param_values[3]? wally_postgres_param_values[3]:"NULL",
                wally_postgres_param_values[4]? wally_postgres_param_names[4]:"NULL",
                wally_postgres_param_values[4]? wally_postgres_param_values[4]:"NULL",
                wally_postgres_param_values[5]? wally_postgres_param_names[5]:"NULL",
                wally_postgres_param_values[5]? wally_postgres_param_values[5]:"NULL",
                wally_postgres_param_values[6]? wally_postgres_param_names[6]:"NULL",
                wally_postgres_param_values[6]? wally_postgres_param_values[6]:"NULL",
                wally_postgres_param_values[7]? wally_postgres_param_names[7]:"NULL",
                wally_postgres_param_values[7]? wally_postgres_param_values[7]:"NULL");
    }
    if (password) {
        WALLY_LOG(AL_NOTICE, "Postgres Password parameters password = ******* %s = %s",
            wally_postgres_param_values[ssl_index]? wally_postgres_param_names[ssl_index]:"NULL",
            wally_postgres_param_values[ssl_index]?wally_postgres_param_values[ssl_index]:"NULL");
    } else {
        WALLY_LOG(AL_NOTICE, "Postgres Password parameters: No password/SSL");
    }


    if (!host || !user || !bad_dbname || !nconns) {
        return NULL;
    }

    if (!PQisthreadsafe()) {
        WALLY_LOG(AL_ERROR, "LibPQ is not thread safe");
        sleep(1);
        exit(1);
    }

    pthread_mutex_lock(&local_lock);

    set_et_26295_test_size();

    if (!initialized) {
        PQinitOpenSSL(0, 0);
        initialized = 1;
    }

    if ((c_state_count + nconns) > WALLY_DB_MAX_CONNS) {
        WALLY_LOG(AL_ERROR, "Too many postgres connections");
        pthread_mutex_unlock(&local_lock);
        return NULL;
    }

    wp_db = (struct wp_db *) WALLY_MALLOC(sizeof(*wp_db));
    if (wp_db) {
        memset(wp_db, 0, sizeof(*wp_db));

        wp_db->db_type = db_postgres;

        wp_db->name = WALLY_STRDUP(wally_postgres_good_db_name, strlen(wally_postgres_good_db_name));
        if (!wp_db->name) goto fail_free;

        wp_db->is_endpoint = is_endpoint;

        LIST_INIT(&(wp_db->all_tables));
        TAILQ_INIT(&(wp_db->free_connections));

        //ZPATH_RWLOCK_INIT(&(wp_db->lock), NULL, __FILE__, __LINE__);
        wp_db->lock = ZPATH_RWLOCK_INIT;

        ZPATH_RWLOCK_WRLOCK(&(wp_db->lock), __FILE__, __LINE__);

        wp_db->is_row_writable = is_row_writable;
        wp_db->is_alterable = is_alterable;
        wp_db->is_true_origin = is_true_origin;

        wp_db->polling_interval_us = polling_interval_us;

        wp_db->db_to_wally_cookie = db_to_wally_cookie;

        wp_db->tables_by_argo_name = argo_hash_alloc(8, 1);
        if (!wp_db->tables_by_argo_name) goto fail_free;

        /* Create libevent event for processing postgres data- we will
         * attach all the postgres sockets to this event base. */
        wp_db->ev_base = event_base_new();
        if (!wp_db->ev_base) {
            goto fail_free;
        }

        /* Set up nconns db connections */
        wp_db->connections_count = nconns;
        wp_db->connections = (struct wp_connection *) WALLY_MALLOC(sizeof(struct wp_connection) * nconns);
        memset(wp_db->connections, 0, sizeof(struct wp_connection) * nconns);

        /* Copy the configured keywords/params in wp_db struct. This informatation
         * is required during DB connection recovery */
        for (i = 0; i < WALLY_PSQL_PARAM_MAX_INDEX; i++) {
            if (wally_postgres_param_names[i]) {
                WP_CFG_PQSL_PARAMS(wp_db->wally_postgres_param_names[i], wally_postgres_param_names[i]);
            }
            if (wally_postgres_param_values[i]) {
                WP_CFG_PQSL_PARAMS(wp_db->wally_postgres_param_values[i], wally_postgres_param_values[i]);
            }
        }
        for (i = 0; i < nconns; i++) {
            int retries;
            char tmp_str[100];
            snprintf(tmp_str, sizeof(tmp_str), "%s:%d", wp_db->name, i);
            c_state[i + c_state_count].name = WALLY_STRDUP(tmp_str, strlen(tmp_str));
            c_conns[i + c_state_count] = &(wp_db->connections[i]);
            wp_db->connections[i].name = c_state[i + c_state_count].name;

            wp_db->connections[i].wp_db = wp_db;

            wp_db->connections[i].my_index = i;

            wp_db->connections[i].cookie = NULL;

            for (retries = 0; retries < 30; retries++) {
                wp_db->connections[i].db_conn = PQconnectdbParams((const char **)wp_db->wally_postgres_param_names,
                                                                  (const char **)wp_db->wally_postgres_param_values, 0);
                if ((!wp_db->connections[i].db_conn) || (PQstatus((PGconn *)(wp_db->connections[i].db_conn)) != CONNECTION_OK)) {
                    if (wp_db->connections[i].db_conn) {
                        WALLY_LOG(AL_NOTICE, "Could not connect to db as %s: %s %s: %s %s: %s : got db_conn, but...",
                                  wally_postgres_param_names[0], wally_postgres_param_values[0],
                                  wally_postgres_param_names[1], wally_postgres_param_values[1],
                                  wally_postgres_param_names[2], wally_postgres_param_values[2]);
                        switch (PQstatus((PGconn *)(wp_db->connections[i].db_conn))) {
                        default:
                            WALLY_LOG(AL_NOTICE, "Conn currently other");
                            break;
                        case CONNECTION_STARTED:
                            WALLY_LOG(AL_NOTICE, "Conn currently CONNECTION_STARTED");
                            break;
                        case CONNECTION_MADE:
                            WALLY_LOG(AL_NOTICE, "Conn currently CONNECTION_MADE");
                            break;
                        case CONNECTION_AWAITING_RESPONSE:
                            WALLY_LOG(AL_NOTICE, "Conn currently CONNECTION_AWAITING_RESPONSE");
                            break;
                        case CONNECTION_AUTH_OK:
                            WALLY_LOG(AL_NOTICE, "Conn currently CONNECTION_AUTH_OK");
                            break;
                        case CONNECTION_SSL_STARTUP:
                            WALLY_LOG(AL_NOTICE, "Conn currently CONNECTION_SSL_STARTUP");
                            break;
                        case CONNECTION_SETENV:
                            WALLY_LOG(AL_NOTICE, "Conn currently CONNECTION_SETENV");
                            break;
#if 0
/* These are not in all versions of postgres... */
                        case CONNECTION_CHECK_WRITABLE:
                            WALLY_LOG(AL_NOTICE, "Conn currently CONNECTION_CHECK_WRITABLE");
                            break;
                        case CONNECTION_CONSUME:
                            WALLY_LOG(AL_NOTICE, "Conn currently CONNECTION_CONSUME");
                            break;
#endif // 0
                        }
                        WALLY_LOG(AL_NOTICE, "Last error = %s", PQerrorMessage((PGconn *)(wp_db->connections[i].db_conn)));

                        /* close the connection and free the resources, will recreate it in next try */
                        PQfinish((PGconn *)(wp_db->connections[i].db_conn));
                        wp_db->connections[i].db_conn = NULL;

                    } else {
                        WALLY_LOG(AL_NOTICE, "Could not connect to db as %s: %s %s: %s %s: %s : did not get db_conn",
                                  wally_postgres_param_names[0], wally_postgres_param_values[0],
                                  wally_postgres_param_names[1], wally_postgres_param_values[1],
                                  wally_postgres_param_names[2], wally_postgres_param_values[2]);
                    }
                    if (retries < 29) {
                        /* We might have started before postgres is able to run... */
                        /* If we can, attempt to create the DB... */
                        if (wp_db->is_alterable) {
                            WALLY_LOG(AL_NOTICE, "Could not connect to db as %s: %s %s: %s %s: %s - Attempting to create DB. (Could be DB not running, or out of connections, or db does not exist)",
                                      wally_postgres_param_names[0], wally_postgres_param_values[0],
                                      wally_postgres_param_names[1], wally_postgres_param_values[1],
                                      wally_postgres_param_names[2], wally_postgres_param_values[2]);
                            res = wally_postgres_createdb(wally_postgres_param_values[2],
                                                          wally_postgres_param_values[0],
                                                          wally_postgres_param_values[1],
                                                          wally_postgres_param_values[3]);
                            if (res) {
                                WALLY_LOG(AL_WARNING, "Could not create db as %s: %s %s: %s %s: %s. Sleeping 1 second and trying again.",
                                          wally_postgres_param_names[0], wally_postgres_param_values[0],
                                          wally_postgres_param_names[1], wally_postgres_param_values[1],
                                          wally_postgres_param_names[2], wally_postgres_param_values[2]);
                                sleep(1);
                            } else {
                                WALLY_LOG(AL_NOTICE, "Created DB as %s: %s %s: %s %s: %s",
                                          wally_postgres_param_names[0], wally_postgres_param_values[0],
                                          wally_postgres_param_names[1], wally_postgres_param_values[1],
                                          wally_postgres_param_names[2], wally_postgres_param_values[2]);
                            }
                        } else {
                            WALLY_LOG(AL_WARNING, "Could not connect to db as %s: %s %s: %s %s: %s - Sleeping 1 second and trying again. (Could be DB not running, or out of connections, or db does not exist)",
                                      wally_postgres_param_names[0], wally_postgres_param_values[0],
                                      wally_postgres_param_names[1], wally_postgres_param_values[1],
                                      wally_postgres_param_names[2], wally_postgres_param_values[2]);
                            sleep(1);
                        }
                        /* This flow can be called in main-thread or zthread context. Zthread heartbeat
                         * monitoring is not enabled for main thread, so zthread(struct zthread_info)
                         * context will be NULL during main thread processing. To differentiate the
                         * accessing thread, zthread NULL check is required.
                         * EX:
                         *  - Wallyd process - this flow is called in main thread, heartbeat tick not needed
                         *  - zpn_brokerd - this flow is called in thread context, which requires
                         *    heartbeat tick
                         **/
                        if (zthread) {
                            zthread_heartbeat(zthread);
                        }
                    } else {
                        /* Failed for 30 times, go to end of the function */
                        /* XXX LOG */
                        WALLY_LOG(AL_CRITICAL, "Could not connect to db as %s: %s %s: %s %s: %s - Failed after too many attempts",
                                  wally_postgres_param_names[0], wally_postgres_param_values[0],
                                  wally_postgres_param_names[1], wally_postgres_param_values[1],
                                  wally_postgres_param_names[2], wally_postgres_param_values[2]);
                        goto fail_free;
                    }
                } else {
                    /* connetion success */
                    break;
                }
            } /* end of retry */

            /* connection success within 30 retries */
			res = wally_postgres_db_conn_config(wp_db, schema, i);
            if (res) {
                goto fail_free;
            }

        }

        WALLY_LOG(AL_INFO, "DB-Function check. GVR Mode = %d db_name = %s is_true_origin = %d is_alterable = %d ",
            wally_gbl_cfg.enable_db_gvr_mode, wp_db->name, is_true_origin, is_alterable);
        /* This block checks if DB function exists in origin RDS, fails if it doesn't exists */
        if (WALLY_DB_GVR_MODE_ENABLED)
        {
            if (is_true_origin && !is_alterable)
            {
                /* Using first connection in the list to send query to RDS */
                pres = xPQexec((PGconn *)(wp_db->connections[0].db_conn),
                        "SELECT COUNT(*) FROM pg_proc WHERE proname LIKE '%retrieve_max_sequence_v1%'");
                /* if DB query operation failed, then fail the startup */
                if (!pres || PQresultStatus(pres) != PGRES_TUPLES_OK) {
                    WALLY_LOG(AL_CRITICAL, "DB query failed. "
                        "Could not check if retrieve_max_sequence_v1 Function present. Failed to startup");
                    if (pres)
                    {
                        PQclear(pres);
                        pres = NULL;
                    }
                    goto fail_free;
                } else
                {
                    char *tmp_str = NULL;
                    int64_t new_row_count;
                    tmp_str = (char *)PQgetvalue(pres, 0, 0);
                    new_row_count = strtoul(tmp_str, NULL, 0);
                    if (new_row_count == 0)
                    {
                        WALLY_LOG(AL_CRITICAL, "retrieve_max_sequence_v1 DB-Function not present in origin DB. Wallyd Exiting...");
                        if (pres)
                        {
                            PQclear(pres);
                            pres = NULL;
                        }
                        goto fail_free;

                    }
                    else
                    {
                        WALLY_LOG(AL_INFO, "retrieve_max_sequence_v1 DB-Function present. row_count = %"PRId64" ", new_row_count);
                        if (pres)
                        {
                            PQclear(pres);
                            pres = NULL;
                        }

                    }
                }
            }
        }


        /* Add a timer event to the database thread. This timer event
         * can do auxiliary maintenance on tables... It also does
         * table update reading. */
        /* Create timer event for this thread */
        wp_db->ev_timer = event_new(wp_db->ev_base,         /* event_base for this thread */
                                    -1,                     /* No socket, so -1. */
                                    EV_PERSIST,             /* Repeating event */
                                    wally_timer_event_callback,   /* Callback function */
                                    wp_db);                 /* Callback cookie- our db state. */
        if (!wp_db->ev_timer) {
            goto fail_free;
        }

        /* Start the timer for this thread. This runs every 5ms by default and
         * can be configured from command line. This
         * timer is related to the polling interval for database
         * changes as well, so if you want faster response time, you
         * can spin this down to a smaller query rate. */
        tv.tv_sec = (timer_interval_us) / 1000000;
        tv.tv_usec = (timer_interval_us) % 1000000;
        if (event_add(wp_db->ev_timer, &tv)) {
            goto fail_free;
        }

        /* Create thread. */
        if (zthread_create(&(wp_db->thread),
                           wally_db_thread,
                           wp_db,
                           thread_name,
                           wally_default_hb_timeout_s, /* 60s for wallyd and for others 20s thread watchdog */
                           16*1024*1024,     /* 16 MB stack */
                           60*1024*1024,     /* 60s stats interval */
                           NULL)) {
            /* XXX LOG */
            goto fail_free;
        }
        if (wally_gbl_cfg.enable_row_process_thread)
        {
            char db_handler_thread_name[WALLY_MAX_THREAD_NAME_LEN] = {0};
            snprintf(db_handler_thread_name, sizeof(db_handler_thread_name), "row_process_thread_%s", thread_name);
            wp_db->db_base = zevent_handler_create(db_handler_thread_name, 16*1024*1024, wally_default_hb_timeout_s);
            if (wp_db->db_base == NULL)
            {
                WALLY_LOG(AL_ERROR, "DB event handler base creation failed for %s", db_handler_thread_name);
                /* Not Stopping above wally_db_thread, anyway process exists on returning NULL */
                /* wallyd does not handle this error but along with lot of errors */
                goto fail_free;
            }
        }
        ZPATH_RWLOCK_UNLOCK(&(wp_db->lock), __FILE__, __LINE__);
    }

    /* Update connection and server poll time. */
    wp_db->wp_get_conn_status_poll = epoch_us();
    wp_db->wp_get_ping_status_poll = epoch_us();

    c_state_count += nconns;

    WALLY_LOG(AL_NOTICE, "wp_db is created, name=%s, is_alterable=%d", wp_db->name, wp_db->is_alterable);
    pthread_mutex_unlock(&local_lock);

    char name[WALLY_MAX_THREAD_NAME_LEN];
    snprintf(name, sizeof(name), "wp_db_%s_stats", wp_db->name);
    struct wp_db_stats * wp_db_stats = WALLY_MALLOC(sizeof(struct wp_db_stats));
    if (wp_db_stats == NULL)
    {
        WALLY_LOG(AL_ERROR, "DB stats creation failed ");
        goto fail_free;
    }
    argo_log_register_structure(argo_log_get("statistics_log"),
                                name,
                                AL_INFO, 60*1000*1000,    /* 1 minute */
                                wp_db_stats_description,
                                wp_db_stats, 0,
                                fill_wp_db_stats,
                                wp_db);

    return wp_db;

 fail_free:
    ZPATH_RWLOCK_UNLOCK(&(wp_db->lock), __FILE__, __LINE__);
    if (wp_db->name) WALLY_FREE(wp_db->name);
    if (wp_db->schema_name) WALLY_FREE(wp_db->schema_name);
    if (wp_db->ev_timer) event_free(wp_db->ev_timer);
    if (wp_db->tables_by_argo_name) argo_hash_free(wp_db->tables_by_argo_name);
    if (wp_db->connections) {
        for (i = 0; i < nconns; i++) {
            if (wp_db->connections[i].ev_conn) {
                event_free(wp_db->connections[i].ev_conn);
            }
            if (wp_db->connections[i].db_conn) {
                PQfinish((PGconn *)(wp_db->connections[i].db_conn));
            }
        }
        WALLY_FREE(wp_db->connections);
    }
    if (wp_db->ev_base) event_base_free(wp_db->ev_base);
    WALLY_FREE(wp_db);
    pthread_mutex_unlock(&local_lock);
    return NULL;
}


void wp_write_batch_object(struct wp_db *wp_db)
{
    int wq_count;
    int wq_indx;
    int conns_writing = 0;
    int max_write_conns = 1;

    struct wally_origin *origin = wp_db->db_to_wally_cookie;
    if (!origin) return;

    wq_count = origin->write_queue_count; // write queue count never goes down so it's OK to read a copy. If we miss anything we'll pick up next time.
    if (wq_count <= 0) return;

    if (wally_postgres_concurrent_write) {
        // save 2 connections solely for read (to avoid writes taking long and reads get delayed)
        max_write_conns = db_max(max_write_conns, wp_db->connections_count - 2);
    }

    wq_indx = (origin->write_queue_last_index + 1) % wq_count;
    for (int i = 0;
            i < wq_count && conns_writing < max_write_conns; // quit when we looped, or all write conns are in use
            i++, wq_indx = ((wq_indx + 1) % wq_count))
    {
        // write queue to connection binding is done on the write.
        int connIx = origin->wq_info[wq_indx].conn_indx;
        if (connIx < 0) {
            connIx = wq_indx % max_write_conns;
            origin->wq_info[wq_indx].conn_indx = connIx;
        }

        struct wp_connection *wp_conn = &(wp_db->connections[connIx]);
        if (wp_conn->state != conn_idle) {
            continue;
        }

        TAILQ_REMOVE(&(wp_db->free_connections), wp_conn, free_connections);
        if(wp_conn->cookie == NULL) {
             wp_conn->cookie = (struct wp_conn_pg_specific *)WALLY_CALLOC(sizeof(struct wp_conn_pg_specific));
        }

        // we have a connection now, try to write an object from the current write q.
        struct wp_conn_pg_specific *pgs = wp_conn->cookie;
        pgs->write_objects_count = 0;
        origin->wq_info[wq_indx].batch_size = 1; // first batch will write only one row
        wp_dequeue_batch_object(wp_db->db_to_wally_cookie, pgs , wq_indx);
        if (pgs->write_objects_count) {
            int result = wp_write_object_upsert(wp_conn, wq_indx);
            if (result == WALLY_RESULT_WOULD_BLOCK) {
                /* Couldn't queue this object. Requeue it back on wally for the moment. (Be sure to enqueue to head) */
                wp_conn->state = conn_idle;
                WALLY_DEBUG_POSTGRES_WRITE("%s: Write would block ix = %d - back-queueing object.", wp_conn->wp_db->name, wq_indx);
                for (int k = pgs->write_objects_count-1; k >= 0; k--) {
                    wally_origin_enqueue_write_object(wp_db->db_to_wally_cookie, wq_indx, pgs->write_objects[k], 1);
                    /* argo_object_release is required for earlier wally_origin_dequeue_write_row_specific */
					wp_release_write_object(pgs->write_objects[k], 1);
                }
                pgs->write_objects_count= 0;
            } else if (result == WALLY_RESULT_NO_ERROR) {
                WALLY_DEBUG_POSTGRES_WRITE("%s: Write successful, sync ix = %d", wp_conn->wp_db->name, wq_indx);
                wp_conn->state = conn_idle;
                for (int k = 0; k < pgs->write_objects_count; k++) {
					wp_release_write_object(pgs->write_objects[k], 0);
                }
            } else if (result == WALLY_RESULT_ASYNCHRONOUS) {
                WALLY_DEBUG_POSTGRES_WRITE("%s: Write successful, async ix = %d", wp_conn->wp_db->name, wq_indx);
                conns_writing++;
                wp_conn->state = conn_table_batch_write; // To change
                wp_conn->write_q_index = wq_indx;
                origin->wq_info[wq_indx].last_batch = 1; // 1st in this batch
            } else if (result == WALLY_RESULT_ERR_TOO_LARGE) {
                WALLY_LOG(AL_ERROR, "row object is too large DB:%s async ix = %d  sequence number is %"PRId64" "
                                                                         ,wp_conn->wp_db->name,wq_indx,pgs->last_sequence);
                wp_conn->state = conn_idle;
                origin->wq_info[wq_indx].object_out_of_memory ++;
                origin->wq_info[wq_indx].total_failed_batch ++;
                for (int k = 0; k < pgs->write_objects_count; k++) {
					wp_release_write_object(pgs->write_objects[k], 0);
                }
            } else {
                /* XXX This is bad. Log. And this case will likely result in losing our DB connection. */
                wp_conn->state = conn_idle;
                origin->wq_info[wq_indx].retry++;
                origin->wq_info[wq_indx].total_failed_batch ++;
                if( origin->wq_info[wq_indx].retry <= WP_BATCH_RETRY_COUNT ) {
                    if (pgs->write_objects[0]->db_op == db_oper_rowargo_object) {
                        WALLY_LOG(AL_ERROR, "Bad: %s (Table %s/%s not initialized ? local_db connection failure retry count %"PRId64" )" ,
                                wally_error_strings[result], wp_conn->wp_db->name,
                                argo_object_get_type(pgs->write_objects[0]->data.row_to_write),
                                origin->wq_info[wq_indx].retry);
                    } else if (pgs->write_objects[0]->db_op == db_oper_lookuptable_update) {
                        WALLY_LOG(AL_ERROR, "Bad: %s (Table %s/%s not initialized ? local_db connection failure retry count %"PRId64" )" ,
                                wally_error_strings[result], wp_conn->wp_db->name,
                                 pgs->write_objects[0]->data.lookup_table_write.table_name,
                                origin->wq_info[wq_indx].retry);
                    } else if (pgs->write_objects[0]->db_op == db_oper_recovery_update) {
                         WALLY_LOG(AL_ERROR, "Bad: %s (Table %s/%s not initialized ? local_db connection failure retry count %"PRId64" )" ,
                                wally_error_strings[result], wp_conn->wp_db->name,
                                 pgs->write_objects[0]->data.recovery_update.table_name,
                                origin->wq_info[wq_indx].retry);
                    }
                    for (int k = 0; k < pgs->write_objects_count; k++) {
                        wally_origin_enqueue_write_object(wp_db->db_to_wally_cookie, wq_indx, pgs->write_objects[k], 1);
						wp_release_write_object(pgs->write_objects[k], 1);
                    }
                } else {
                    if (pgs->write_objects[0]->db_op == db_oper_rowargo_object) {
                        WALLY_LOG(AL_ERROR, "Bad: %s (Table %s/%s not initialized? Tossing %d rows. sequence number %"PRId64")",wally_error_strings[result],
                             wp_conn->wp_db->name,
							 argo_object_get_type(pgs->write_objects[0]->data.row_to_write),
							 pgs->write_objects_count,
							 pgs->last_sequence);
                    } else if (pgs->write_objects[0]->db_op == db_oper_lookuptable_update) {
                         WALLY_LOG(AL_ERROR, "Bad: %s (Table %s/%s not initialized? Tossing %d rows. sequence number %"PRId64")",wally_error_strings[result],
                             wp_conn->wp_db->name,
                             pgs->write_objects[0]->data.lookup_table_write.table_name,
							 pgs->write_objects_count,
							 pgs->last_sequence);
                    } else if (pgs->write_objects[0]->db_op == db_oper_recovery_update) {
                         WALLY_LOG(AL_ERROR, "Bad: %s (Table %s/%s not initialized? Tossing %d rows. sequence number %"PRId64")",wally_error_strings[result],
                             wp_conn->wp_db->name,
                             pgs->write_objects[0]->data.recovery_update.table_name,
							 pgs->write_objects_count,
							 pgs->last_sequence);
                    }
                    for (int k = 0; k < pgs->write_objects_count; k++) {
						wp_release_write_object(pgs->write_objects[k], 0);
                    }
                    origin->wq_info[wq_indx].retry = 0;
                    wally_error_handler(NULL, wp_conn, true, E_LDP_WRITE_FAIL, SE_NONE);
                    origin->wq_info[wq_indx].count_fail += pgs->write_objects_count;
                }
                wp_conn->state = conn_idle;
            }
        }

        if (wp_conn->state == conn_idle) {
            wally_db_insert_free_conn (wp_db, wp_conn);
        }
    }
}

/* must be called with db lock */
void wp_write_object(struct wp_db *wp_db)
{
    struct wally_origin *origin;
    int wq_count;
    int wq_indx;
    int conns_writing = 0;
    int max_write_conns = 1;

    origin = wp_db->db_to_wally_cookie;
    if (!origin) return;

    wq_count = origin->write_queue_count; // write queue count never goes down so it's OK to read a copy. If we miss anything we'll pick up next time.
    if (wq_count <= 0) return;

    wq_indx = (origin->write_queue_last_index + 1) % wq_count;

    if (wally_postgres_concurrent_write) {
        // save 2 connections solely for read (to avoid writes taking long and reads get delayed)
        max_write_conns = db_max(max_write_conns, wp_db->connections_count - 2);
    }

    for (int i = 0;
            i < wq_count && conns_writing < max_write_conns; // quit when we looped, or all write conns are in use
            i++, wq_indx = ((wq_indx + 1) % wq_count))
    {
        // write queue to connection binding is done on the write.
        int connIx = origin->wq_info[wq_indx].conn_indx;
        if (connIx < 0) {
            connIx = wq_indx % max_write_conns;
            origin->wq_info[wq_indx].conn_indx = connIx;
        }

        struct wp_connection *wp_conn = &(wp_db->connections[connIx]);
        if (wp_conn->state != conn_idle) {
            continue;
        }

        TAILQ_REMOVE(&(wp_db->free_connections), wp_conn, free_connections);
        WALLY_DEBUG_POSTGRES_CONN("%s: Alloc connection %d", wp_conn->wp_db->name, wp_conn->my_index);

        // we have a connection now, try to write an object from the current write q.
        struct wally_local_db_write_queue_object *write_object = wally_origin_dequeue_write_object_specific(wp_db->db_to_wally_cookie, wq_indx);
        if (write_object) {
            int result = wp_write_object_update(wp_conn, write_object);
            if (result == WALLY_RESULT_WOULD_BLOCK) {
                /* Couldn't queue this object. Requeue it back on wally for the moment. (Be sure to enqueue to head) */
                wp_conn->state = conn_idle;
                WALLY_DEBUG_POSTGRES_WRITE("%s: Write would block ix = %d - back-queueing object.", wp_conn->wp_db->name, wq_indx);
                wally_origin_enqueue_write_object(wp_db->db_to_wally_cookie, wq_indx, write_object, 1);
                /* argo_object_release is required for earlier wally_origin_dequeue_write_row_specific */
				wp_release_write_object(write_object, 1);
            } else if (result == WALLY_RESULT_NO_ERROR) {
                WALLY_DEBUG_POSTGRES_WRITE("%s: Write successful, sync ix = %d", wp_conn->wp_db->name, wq_indx);
                wp_conn->state = conn_idle;
				wp_release_write_object(write_object, 0);
            } else if (result == WALLY_RESULT_ASYNCHRONOUS) {
                WALLY_DEBUG_POSTGRES_WRITE("%s: Write successful, async ix = %d", wp_conn->wp_db->name, wq_indx);
                conns_writing++;
                wp_conn->state = conn_table_write;
                wp_conn->write_object = write_object;
                wp_conn->write_q_index = wq_indx;
                origin->wq_info[wq_indx].last_batch = 1; // 1st in this batch
            } else {
                /* XXX This is bad. Log. And this case will likely result in losing our DB connection. */
                WALLY_LOG(AL_ERROR, "Bad: %s (Table not initialized? Tossing row.)", wally_error_strings[result]);
				wp_release_write_object(write_object, 0);
                wp_conn->state = conn_idle;
            }
        }

        if (wp_conn->state == conn_idle) {
            wally_db_insert_free_conn (wp_db, wp_conn);
        }
    }
}
