/*
 * wally_fohh.h. Copyright (C) 2013 Zscaler, Inc. All Rights Reserved.
 *
 * Wally origin database implementation for accessing a remote wally
 *    as an origin database. Also includes code for exporting local
 *    wally as an origin database.
 *
 * ASSUMPTIONS:
 *
 * 1. Assumes FOHH has already been initialized.
 *
 * 2. When server is created, assumes wally is already running.
 */

#ifndef __WALLY_FOHH_CLIENT_H__
#define __WALLY_FOHH_CLIENT_H__

#include "wally/wally.h"
#include "fohh/fohh.h"

struct wally_fohh_client;

/*
 * The standard callins from wally into this origin DB.
 */
int wally_fohh_register_for_index(void *callout_cookie,
                                  int64_t request_id,
                                  int64_t request_sequence,
                                  char *table_name,
                                  char *column_name,
                                  char *key);
int wally_fohh_register_for_table(void *callout_cookie,
                                  int64_t sequence,
                                  const char *table_name,
                                  const char *column_name,
                                  const char *key);
int wally_fohh_deregister_for_index(void *callout_cookie,
                                    char *table_name,
                                    char *column_name,
                                    char *key);
int wally_fohh_deregister_for_table(void *callout_cookie,
                                    const char *table_name,
                                    const char *column_name);
int wally_fohh_set_cookie(void *callout_cookie,
                          void *callback_cookie);

int wally_fohh_dump_state(void *callout_cookie, char *s, char *e);

enum wally_origin_status wally_fohh_get_status(void *callout_cookie);

int wally_fohh_add_table(void *callout_cookie,
                         const char *db_table_name,
                         const char *argo_object_name);

/*
 * Creation/initialization routine. This is a synchronous call.
 *
 * This creates a remote connection to the origin database represented
 * by domain_name. FOHH will maintain the connection (using FOHH
 * security- TLS, etc)
 *
 * _using_ctx allows specifying the context to use when creating the
 * client FOHH connection.
 */
struct wally_fohh_client *wally_fohh_client_create(struct wally *wally,
                                                   void *fohh_to_wally_cookie,
                                                   char *domain_name,
                                                   char *SNI,
                                                   char *sni_suffix,
                                                   uint16_t tcp_port_ne,
                                                   fohh_connection_callback_f *callback);
struct wally_fohh_client *wally_fohh_client_create_using_ctx(struct wally *wally,
                                                             void *fohh_to_wally_cookie,
                                                             char *domain_name,
                                                             char *SNI,
                                                             char *sni_suffix,
                                                             uint16_t tcp_port_ne,
                                                             fohh_connection_callback_f *callback,
                                                             SSL_CTX *ctx);

/*
 * Get the client fohh_connection associated with this
 * wally_fohh_client
 */
struct fohh_connection *
wally_fohh_client_get_f_conn(struct wally_fohh_client *client);

struct wally_origin *wally_fohh_client_get_slave_origin(struct wally_fohh_client *wfc);
struct wally* wally_fohh_client_get_wally(struct fohh_connection *f_conn);
/* Function : wally_fohh_send_recovery_complete_response
 * Arg      : Callcout_cookie - WFC object
 *            table_name - Table name for which response received
 *            row_count - Number of rows received in client
 * Ret      : WALLY_RESULT_NO_ERR if success
 * Desc     : This function receives complete response from client before
 *              forwarding to remote wallys.
 */
int wally_fohh_send_recovery_complete_response(void *callout_cookie, char *table_name, int64_t row_count);
#endif /* __WALLY_FOHH_CLIENT_H__ */
