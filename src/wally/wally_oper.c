/*
 * wally_oper.c. Copyright (C) 2024 Zscaler, Inc. All Rights Reserved.
 *
 * Wally operational mode and event handler file
 */

#define _GNU_SOURCE

#include <stdio.h>
#include <unistd.h>
#include <stdbool.h>

#include "zthread/zthread.h"
#include "zpath_misc/zpath_misc.h"
#include "zevent/zevent.h"

#include "wally/wally.h"
#include "wally/wally_hash.h"
#include "wally/wally_private.h"
#include "wally/wally_db.h"
#include "wally/wally_oper.h"

/*Operation mode to string */
const char *wally_oper_mode_strings[] = {
    [S_NA]="NA",
    [S_INIT]="INIT",
    [S_ACTIVE]="ACTIVE",
    [S_OFFLINE]="OFFLINE",
    [S_TERMINATE]="TERMINATE",
    [S_MAX]="MODE MAX",
};

/*Operation event to string */
const char *wally_oper_event_strings[] = {
    [E_INIT]="INIT",
    [E_ACTIVE]="ACTIVE",
    [E_ORIGIN_DISCONNECT]="ORIGIN DISCONNECT",
    [E_ORIGIN_CONNECT]="ORIGIN CONNECT",
    [E_CLIENT_DISCONNECT]="CLIENT DISCONNECT",
    [E_CLIENT_CONNECT]="CLIENT CONNECT",
    [E_OFFLINE]="OFFLINE",
    [E_TERMINATE]="TERMINATE",
    [E_DB_FAIL]="DB FAILURE",
    [E_DB_RECO]="DB RECOVERY",
    [E_TRANSACTION_FAIL]="TRANSACTION FAILURE",
    [E_SCHEMA_MISMATCH]="SCHEMA MISMATCH",
    [E_LDP_WRITE_FAIL]="LOCAL DB WRITE FAIL",
    [E_RECOVERY]="E_RECOVERY",
    [E_MAX]="EVENT MAX",
};

/*Operation sub-event to string */
const char *wally_oper_sub_event_strings[] = {
    [SE_NONE] = "None",
    [SE_DB_LOCAL] = "LOCAL DB EVENT",
    [SE_DB_REMOTE] = "RDS EVENT",
    [SE_DB_POLL_FAILED] = "POLLING FAILURE",
    [SE_FOHH_DISCONNECT] = "FOHH SERVER DISCONNECT",
    [SE_FOHH_ACTIVE] = "FOHH SERVER RECONNECT",
    [SE_MANUAL] = "MANUAL COMMAND",
    [SE_DRAIN_MANUAL] = "DRAIN & MANUAL COMMAND",
    [SE_NO_DRAIN_MANUAL] = "NO-DRAIN & MANUAL COMMAND",
    [SE_MEM_FAIL] = "MEMORY FAILURE",
    [SE_FSM_FAIL] = "FSM FAILURE",
    [SE_SYNTAX_FAIL] = "SQL SYNTAX FAILURE",
    [SE_LONG_RUN_QUERY] ="SQL LONG RUNNING QUERIES",
    [SE_RECOVERY_START] = "RECOVERY START",
    [SE_RECOVERY_CMPLT] = "RECOVERY COMPLETE",
    [SE_RECOVERY_TIMEOUT] = "RECOVERY TIMEOUT",
    [SE_RECOVERY_FAILURE] = "RECOVERY FAILURE",
    [SE_ARGO_FAIL] ="ARGO FAILURE",
    [SE_MAX]="EVENT MAX",
};


struct wallyd_oper_stats wallyd_oper_stats;

struct wallyd_oper_stats {         /* _ARGO: object_definition */
    uint32_t wally_oper_mode;      /* _ARGO: integer */
    uint32_t wally_oper_event;     /* _ARGO: integer */
    uint32_t wally_oper_sub_event; /* _ARGO: integer */
    uint64_t trasaction_fail;      /* _ARGO: integer */
    uint64_t termination;          /* _ARGO: integer */
    uint64_t schema_mismatch;      /* _ARGO: integer */
    uint32_t rds_failure;          /* _ARGO: integer */
    uint32_t client_disconnect;    /* _ARGO: integer */
    uint32_t origin_disconnect;    /* _ARGO: integer */
    uint32_t offline;              /* _ARGO: integer */
    uint32_t client_disconnect_count;  /* _ARGO: integer */
    uint32_t origin_disconnect_count;  /* _ARGO: integer */
    uint32_t offline_count;        /* _ARGO: integer */
    uint32_t poll_failure;         /* _ARGO: integer */
    uint32_t rds_db_conn_fail_count;  /* _ARGO: integer */
    uint32_t ldb_failure;          /* _ARGO: integer */
    uint32_t fohh_origin_fail;     /* _ARGO: integer */
    uint32_t trimmer_stop;         /* _ARGO: integer */
    uint64_t ldb_write_failure;    /* _ARGO: integer */
    uint64_t recovery_in_progress; /* _ARGO: integer */
    uint64_t recovery_failures;    /* _ARGO: integer */
};

#include "wally/wally_oper_compiled_c.h"

struct argo_structure_description *wallyd_oper_stats_description;
struct wally_oper_mode_g oper_mode_g;

wally_oper_fsm wally_oper_fsm_cb[S_MAX][E_MAX];
/*
 *State/Event
 *             E_INIT  E_ACTIVE E_ORIGIN_DISCONNECT  E_ORIGIN_CONNECT E_CLIENT_DISCONNECT  E_CLIENT_CONNECT E_OFFLINE E_TERMINATE  E_DB_FAIL  E_DB_RECO  E_TRANSACTION_FAIL E_SCHEMA_MISMATCH
 * S_NA
 * S_INIT
 * S_ACTIVE
 * S_OFFLINE
 * S_TERMINATE */

/* Initialize the FSB callback functions */
void wally_oper_init_fsm_cb()
{
    wally_oper_fsm_cb[S_NA][E_INIT]              = wally_oper_init;
    wally_oper_fsm_cb[S_NA][E_ACTIVE]            = wally_oper_na;
    wally_oper_fsm_cb[S_NA][E_ORIGIN_DISCONNECT] = wally_oper_na;
    wally_oper_fsm_cb[S_NA][E_ORIGIN_CONNECT]    = wally_oper_na;
    wally_oper_fsm_cb[S_NA][E_CLIENT_DISCONNECT] = wally_oper_na;
    wally_oper_fsm_cb[S_NA][E_CLIENT_CONNECT]    = wally_oper_na;
    wally_oper_fsm_cb[S_NA][E_OFFLINE]           = wally_oper_na;
    wally_oper_fsm_cb[S_NA][E_TERMINATE]         = wally_oper_terminate;
    wally_oper_fsm_cb[S_NA][E_DB_FAIL]           = wally_oper_na;
    wally_oper_fsm_cb[S_NA][E_DB_RECO]           = wally_oper_na;
    wally_oper_fsm_cb[S_NA][E_TRANSACTION_FAIL]  = wally_oper_na;
    wally_oper_fsm_cb[S_NA][E_SCHEMA_MISMATCH]   = wally_oper_na;
    wally_oper_fsm_cb[S_NA][E_LDP_WRITE_FAIL]    = wally_oper_na;
    wally_oper_fsm_cb[S_NA][E_RECOVERY]          = wally_oper_na;

    wally_oper_fsm_cb[S_INIT][E_INIT]              = wally_oper_init;
    wally_oper_fsm_cb[S_INIT][E_ACTIVE]            = wally_oper_active;
    wally_oper_fsm_cb[S_INIT][E_ORIGIN_DISCONNECT] = wally_oper_na;
    wally_oper_fsm_cb[S_INIT][E_ORIGIN_CONNECT]    = wally_oper_na;
    wally_oper_fsm_cb[S_INIT][E_CLIENT_DISCONNECT] = wally_oper_na;
    wally_oper_fsm_cb[S_INIT][E_CLIENT_CONNECT]    = wally_oper_na;
    wally_oper_fsm_cb[S_INIT][E_OFFLINE]           = wally_oper_na;
    wally_oper_fsm_cb[S_INIT][E_TERMINATE]         = wally_oper_terminate;
    wally_oper_fsm_cb[S_INIT][E_DB_FAIL]           = wally_oper_db_fail;
    wally_oper_fsm_cb[S_INIT][E_DB_RECO]           = wally_oper_db_recovery;
    wally_oper_fsm_cb[S_INIT][E_TRANSACTION_FAIL]  = wally_oper_trans;
    wally_oper_fsm_cb[S_INIT][E_SCHEMA_MISMATCH]   = wally_oper_sch_fail;
    wally_oper_fsm_cb[S_INIT][E_LDP_WRITE_FAIL]    = wally_oper_ldp_write_fail;
    wally_oper_fsm_cb[S_INIT][E_RECOVERY]          = wally_oper_na;

    wally_oper_fsm_cb[S_ACTIVE][E_INIT]              = wally_oper_na;
    wally_oper_fsm_cb[S_ACTIVE][E_ACTIVE]            = wally_oper_active;
    wally_oper_fsm_cb[S_ACTIVE][E_ORIGIN_DISCONNECT] = wally_oper_origin_disconnect;
    wally_oper_fsm_cb[S_ACTIVE][E_ORIGIN_CONNECT]    = wally_oper_origin_connect;
    wally_oper_fsm_cb[S_ACTIVE][E_CLIENT_DISCONNECT] = wally_oper_client_disconnect;
    wally_oper_fsm_cb[S_ACTIVE][E_CLIENT_CONNECT]    = wally_oper_client_connect;
    wally_oper_fsm_cb[S_ACTIVE][E_OFFLINE]           = wally_oper_offline;
    wally_oper_fsm_cb[S_ACTIVE][E_TERMINATE]         = wally_oper_terminate;
    wally_oper_fsm_cb[S_ACTIVE][E_DB_FAIL]           = wally_oper_db_fail;
    wally_oper_fsm_cb[S_ACTIVE][E_DB_RECO]           = wally_oper_db_recovery;
    wally_oper_fsm_cb[S_ACTIVE][E_TRANSACTION_FAIL]  = wally_oper_trans;
    wally_oper_fsm_cb[S_ACTIVE][E_SCHEMA_MISMATCH]   = wally_oper_sch_fail;
    wally_oper_fsm_cb[S_ACTIVE][E_LDP_WRITE_FAIL]    = wally_oper_ldp_write_fail;
    wally_oper_fsm_cb[S_ACTIVE][E_RECOVERY]          = wally_oper_recovery_handle;

    wally_oper_fsm_cb[S_OFFLINE][E_INIT]              = wally_oper_na;
    wally_oper_fsm_cb[S_OFFLINE][E_ACTIVE]            = wally_oper_active;
    wally_oper_fsm_cb[S_OFFLINE][E_ORIGIN_DISCONNECT] = wally_oper_origin_disconnect;
    wally_oper_fsm_cb[S_OFFLINE][E_ORIGIN_CONNECT]    = wally_oper_origin_connect;
    wally_oper_fsm_cb[S_OFFLINE][E_CLIENT_DISCONNECT] = wally_oper_client_disconnect;
    wally_oper_fsm_cb[S_OFFLINE][E_CLIENT_CONNECT]    = wally_oper_client_connect;
    wally_oper_fsm_cb[S_OFFLINE][E_OFFLINE]           = wally_oper_na;
    wally_oper_fsm_cb[S_OFFLINE][E_TERMINATE]         = wally_oper_terminate;
    wally_oper_fsm_cb[S_OFFLINE][E_DB_FAIL]           = wally_oper_db_fail;
    wally_oper_fsm_cb[S_OFFLINE][E_DB_RECO]           = wally_oper_db_recovery;
    wally_oper_fsm_cb[S_OFFLINE][E_TRANSACTION_FAIL]  = wally_oper_trans;
    wally_oper_fsm_cb[S_OFFLINE][E_SCHEMA_MISMATCH]   = wally_oper_sch_fail;
    wally_oper_fsm_cb[S_OFFLINE][E_LDP_WRITE_FAIL]    = wally_oper_ldp_write_fail;
    wally_oper_fsm_cb[S_OFFLINE][E_RECOVERY]          = wally_oper_na;

    wally_oper_fsm_cb[S_TERMINATE][E_INIT]              = wally_oper_na;
    wally_oper_fsm_cb[S_TERMINATE][E_ACTIVE]            = wally_oper_na;
    wally_oper_fsm_cb[S_TERMINATE][E_ORIGIN_DISCONNECT] = wally_oper_na;
    wally_oper_fsm_cb[S_TERMINATE][E_ORIGIN_CONNECT]    = wally_oper_na;
    wally_oper_fsm_cb[S_TERMINATE][E_CLIENT_DISCONNECT] = wally_oper_na;
    wally_oper_fsm_cb[S_TERMINATE][E_CLIENT_CONNECT]    = wally_oper_na;
    wally_oper_fsm_cb[S_TERMINATE][E_OFFLINE]           = wally_oper_na;
    wally_oper_fsm_cb[S_TERMINATE][E_TERMINATE]         = wally_oper_na;
    wally_oper_fsm_cb[S_TERMINATE][E_DB_FAIL]           = wally_oper_na;
    wally_oper_fsm_cb[S_TERMINATE][E_DB_RECO]           = wally_oper_na;
    wally_oper_fsm_cb[S_TERMINATE][E_TRANSACTION_FAIL]  = wally_oper_na;
    wally_oper_fsm_cb[S_TERMINATE][E_SCHEMA_MISMATCH]   = wally_oper_na;
    wally_oper_fsm_cb[S_TERMINATE][E_LDP_WRITE_FAIL]    = wally_oper_na;
    wally_oper_fsm_cb[S_TERMINATE][E_RECOVERY]          = wally_oper_na;
}

static void wally_disable_db_conn()
{
    bool ret = false;
    ret = wally_stop_all_cleanup(oper_mode_g.wally);
    if (ret) {
        oper_mode_g.trimmer_stop = true;
    }

    wally_db_set_read_disabled(true);
}

static void wally_enable_db_conn()
{
    wally_db_set_read_disabled(false);
}
static int wallyd_oper_mode_stats_fill (void *cookie, int counter, void *structure_data)
{
    struct wallyd_oper_stats *oper_stats = (struct wallyd_oper_stats *) structure_data;

    oper_stats->wally_oper_mode = oper_mode_g.mode;
    oper_stats->wally_oper_event = oper_mode_g.event;
    oper_stats->wally_oper_sub_event = oper_mode_g.sub_event;
    oper_stats->trasaction_fail = oper_mode_g.trasaction_fail;
    oper_stats->schema_mismatch = oper_mode_g.schema_mismatch;
    oper_stats->fohh_origin_fail = oper_mode_g.fohh_origin_fail;
    oper_stats->rds_failure = oper_mode_g.rds_failure?1:0;
    oper_stats->poll_failure = oper_mode_g.poll_failure?1:0;
    oper_stats->origin_disconnect = oper_mode_g.origin_disconnect?1:0;
    oper_stats->client_disconnect = oper_mode_g.client_disconnect?1:0;
    oper_stats->offline = oper_mode_g.offline?1:0;
    oper_stats->origin_disconnect_count = oper_mode_g.origin_disconnect_count;
    oper_stats->client_disconnect_count = oper_mode_g.client_disconnect_count;
    oper_stats->offline_count = oper_mode_g.offline_count;
    oper_stats->ldb_failure = oper_mode_g.ldb_failure?1:0;
    oper_stats->trimmer_stop = oper_mode_g.trimmer_stop?1:0;
    oper_stats->rds_db_conn_fail_count = oper_mode_g.rds_db_conn_fail_count;
    oper_stats->ldb_write_failure = oper_mode_g.ldb_write_failure;
    oper_stats->recovery_in_progress = oper_mode_g.recovery_in_progress;
    oper_stats->recovery_failures = oper_mode_g.recovery_failures;

    /* Published after running state also to support future fields */
    return ARGO_RESULT_NO_ERROR;
}

static int zpn_wally_oper_stats_init()
{
    wallyd_oper_stats_description = argo_register_global_structure(WALLYD_OPER_STATS_HELPER);
    if(!wallyd_oper_stats_description)
    {
        return WALLY_RESULT_ERR;
    }
    argo_log_register_structure(argo_log_get("statistics_log"),
                                "wallyd_oper_stats",
                                AL_INFO,
                                10*1000*1000,    /* 10 seconds */
                                wallyd_oper_stats_description,
                                &wallyd_oper_stats, 1,
                                wallyd_oper_mode_stats_fill, NULL);
    return WALLY_RESULT_NO_ERROR;
}

/*
 * Wallyd cleanup thread handler. */
static void wally_oper_fsm_handler(int sock, short flags, void *cookie)
{
    struct wally_oper_data *data = NULL;

    zthread_heartbeat(NULL);

    /* Previous state change is in progress, so wait for it to
     * complete */
    if (oper_mode_g.oper_state_change_in_progress == true) {
        return;
    }

    data = wally_oper_dequeue_event();
    if (data) {
        WALLY_LOG(AL_INFO, "Oper FSM event received %s",
                wally_oper_event_strings[data->event]);
        wally_oper_handler(data);
        WALLY_FREE(data);
        data = NULL;
    }
    return;
}

/* Operational FSM thread routine */
void *wally_oper_fsm_thread(struct zthread_info *zthread_arg, void *cookie)
{
    struct event_base *ev_base = event_base_new();
    oper_mode_g.timer_event = event_new(ev_base, -1,
                                        EV_PERSIST,
                                        wally_oper_fsm_handler, NULL);
    struct timeval tv;

    tv.tv_sec = 1;
    tv.tv_usec = 0;
    event_add(oper_mode_g.timer_event, &tv);

    oper_mode_g.oper_base = zevent_attach(ev_base);
    if (oper_mode_g.oper_base == NULL)
    {
        WALLY_LOG(AL_ERROR, "Oper FSM event handler base creation failed ");
        fprintf(stdout, "Oper FSM event handler base creation failed\n");
        fprintf(stderr, "Aborting\n");
        abort();
    }
    zevent_base_dispatch(ev_base);

    return NULL;
}

/* Wally operational fsm handler routine */
int32_t wally_oper_handler(struct wally_oper_data *oper_data)
{
    uint32_t ret = 0;

    WALLY_LOG(AL_NOTICE, "Wally received oper config:  event %s sub_event %s", wally_oper_event_strings[oper_data->event],
            wally_oper_sub_event_strings[oper_data->sub_event]);
    WALLY_LOG(AL_NOTICE, "Wally current mode %s event %s sub_event %s", wally_oper_mode_strings[oper_mode_g.mode],
            wally_oper_event_strings[oper_mode_g.event], wally_oper_sub_event_strings[oper_mode_g.sub_event]);

    if (!wally_oper_fsm_cb[oper_mode_g.mode][oper_data->event]) {
        WALLY_LOG(AL_CRITICAL, "Wally FSM CB not initialized :event %s ", wally_oper_event_strings[oper_data->event]);
        return WALLY_RESULT_NO_ERROR;
    }

    oper_mode_g.oper_state_change_in_progress = true;
    ret = wally_oper_fsm_cb[oper_mode_g.mode][oper_data->event](oper_data);
    if (ret != WALLY_RESULT_ASYNCHRONOUS) {
        oper_mode_g.oper_state_change_in_progress = false;
    } else {
        WALLY_LOG(AL_INFO, "Wally operational mode state change in progress");
    }
    WALLY_LOG(AL_INFO, "Wally new mode %s event %s sub_event %s", wally_oper_mode_strings[oper_mode_g.mode],
            wally_oper_event_strings[oper_mode_g.event],
            wally_oper_sub_event_strings[oper_mode_g.sub_event]);

    return WALLY_RESULT_NO_ERROR;
}

/* Offline and terminate modes needs to drain all the clients for its operation.
 * Operanational mode drain should wait, if itascurl drain command is in progress.
 * This functions will be called, once the itascurl drain is completed and
 * drain command is asynchronous, so drainc complete notification also
 * processed here.*/
void wally_oper_drain_completed(struct zevent_base *base, void *void_cookie, int64_t cookie)
{
   if (oper_mode_g.drain_pending) {
       oper_mode_g.drain_pending = false;
       WALLY_LOG(AL_INFO, "Wally operational state drain started");
       oper_mode_g.drain_cb();
       return;
   } else if (oper_mode_g.drain_in_process) {
       WALLY_LOG(AL_INFO, "Wally operational state drain completed");
       oper_mode_g.drain_in_process = false;
       oper_mode_g.oper_state_change_in_progress = false;
   } else {
       oper_mode_g.oper_state_change_in_progress = false;
       WALLY_LOG(AL_INFO, "Wally drain completed, no operation needed");
   }
}

/* Update the state, event and sub-event in the oper_mode_g struct */
static int32_t wally_oper_mode_update (enum wally_oper_modes mode, struct wally_oper_data *oper_data)
{
    oper_mode_g.prev_mode = oper_mode_g.mode;
    oper_mode_g.prev_event = oper_mode_g.event;
    oper_mode_g.prev_sub_event = oper_mode_g.sub_event;
    oper_mode_g.mode = mode;
    oper_mode_g.event = oper_data->event;
    oper_mode_g.sub_event = oper_data->sub_event;
    memset (&oper_mode_g.oper_prev_time, 0, sizeof (oper_mode_g.oper_prev_time));
    memcpy (&oper_mode_g.oper_prev_time, &oper_mode_g.oper_time, sizeof (oper_mode_g.oper_prev_time));
    memset (&oper_mode_g.oper_time, 0, sizeof (oper_mode_g.oper_time));
    gettimeofday(&oper_mode_g.oper_time, NULL);

    return WALLY_RESULT_NO_ERROR;
}

/* Not a valid event for this currect state */
int32_t wally_oper_na(struct wally_oper_data *oper_data)
{
    WALLY_LOG(AL_INFO, "Wally operational FSM change not required");
    return WALLY_RESULT_NO_ERROR;
}

/* All the initializations are completed, Set the mode to ACTIVE */
int32_t wally_oper_active(struct wally_oper_data *oper_data)
{
    int  ret = WALLY_RESULT_NO_ERROR;

    WALLY_LOG(AL_INFO, "Wally operational Active");
    if (oper_mode_g.is_active_done) {
        /* Active init is already done. This call is coming from curl command */
        /* Manual drain is request */
        if (oper_data->sub_event == SE_DRAIN_MANUAL) {
            ret = oper_mode_g.drain_cb();
        }
        /* Recover the state */
        if (oper_mode_g.offline) {
            /* Enable RDS connection and client connection */
            wally_enable_db_conn();
            wally_enable_client_access();
            oper_mode_g.offline = false;
            wally_oper_mode_update(S_ACTIVE, oper_data);
        }
        return ret;
    }

    if (oper_mode_g.rds_failure) {
        return WALLY_RESULT_NO_ERROR;
    }
    set_wally_app_state(wally_state_tables_loaded);
    wally_oper_mode_update(S_ACTIVE, oper_data);
    oper_mode_g.is_active_done = true;

    return ret;
}

/* Transaction failure handler
 * This is not DB connection failure, so keep the state as existing state
 * and increment the failure counter. This will be uploaded to stats log */
int32_t wally_oper_trans(struct wally_oper_data *oper_data)
{
    WALLY_LOG(AL_CRITICAL, "Wally operational mode Active(Transaction failure)");
    oper_mode_g.trasaction_fail++;
    wally_oper_mode_update(oper_mode_g.mode, oper_data);

    return WALLY_RESULT_NO_ERROR;
}

/* SCHEMA FAIL operational mode handler
 * This is not DB connection failure, so keep the state as existing state
 * and increment the failure counter. This will be uploaded to stats log */
int32_t wally_oper_sch_fail(struct wally_oper_data *oper_data)
{
    WALLY_LOG(AL_CRITICAL, "Wally operational mode Schema failure");
    oper_mode_g.schema_mismatch++;

    wally_oper_mode_update(oper_mode_g.mode, oper_data);

    return WALLY_RESULT_NO_ERROR;
}

/* WRITE DB operational mode handler
 * This is not DB connection failure, so keep the state as existing state
 * and increment the failure counter. This will be uploaded to stats log */
int32_t wally_oper_ldp_write_fail(struct wally_oper_data *oper_data)
{
    WALLY_LOG(AL_CRITICAL, "Local DB write failure");
    oper_mode_g.ldb_write_failure++;

    wally_oper_mode_update(oper_mode_g.mode, oper_data);

    return WALLY_RESULT_NO_ERROR;
}

int32_t wally_oper_recovery_handle(struct wally_oper_data *oper_data)
{
    WALLY_LOG(AL_CRITICAL, "Recovery operation event received");
    if (oper_data->sub_event != SE_RECOVERY_START)
        oper_mode_g.recovery_in_progress++;
    else
        oper_mode_g.recovery_in_progress--;

    if ((oper_data->sub_event != SE_RECOVERY_START) &&
            (oper_data->sub_event != SE_RECOVERY_CMPLT))
       oper_mode_g.recovery_failures++;

    wally_oper_mode_update(oper_mode_g.mode, oper_data);

    return WALLY_RESULT_NO_ERROR;
}

/* INIT operational mode handler */
int32_t wally_oper_init(struct wally_oper_data *oper_data)
{
    WALLY_LOG(AL_INFO, "Wally operational mode init");
    set_wally_app_state(wally_state_app_initilizing);
    wally_oper_mode_update(S_INIT, oper_data);

    return WALLY_RESULT_NO_ERROR;
}

/* client connected event handler */
int32_t wally_oper_client_connect (struct wally_oper_data *oper_data)
{
   /* Enable the client access */
    oper_mode_g.client_disconnect = false;
    wally_enable_client_access();
    wally_oper_mode_update(oper_mode_g.mode, oper_data);

    return WALLY_RESULT_NO_ERROR;
}

/* client disconnected event handler */
int32_t wally_oper_client_disconnect (struct wally_oper_data *oper_data)
{
    int32_t ret = WALLY_RESULT_NO_ERROR;

    oper_mode_g.client_disconnect = true;
    oper_mode_g.client_disconnect_count++;
    wally_disable_client_access();
    /* Drain is default for client-disconnected.
     * If no-drain is requested, skip it */
    if (oper_data->sub_event != SE_NO_DRAIN_MANUAL) {
        ret = oper_mode_g.drain_cb();
    }
    wally_oper_mode_update(oper_mode_g.mode, oper_data);

    return ret;
}

/* Origin connected event handler */
int32_t wally_oper_origin_connect (struct wally_oper_data *oper_data)
{
    if (oper_data->sub_event == SE_FOHH_ACTIVE) {
        WALLY_LOG(AL_INFO, "Wally operational mode FOHH Server connected");
        if (oper_mode_g.fohh_origin_fail == 0) {
            WALLY_LOG(AL_INFO,
                    "No FOHH server disconnected, so it could be first time connect, skip it");
            return WALLY_RESULT_NO_ERROR;
        }

        oper_mode_g.fohh_origin_fail--;
        /*
         * Decrease the fohh_origin_fail counter and report the count,
         * No state change
         */
        if (oper_mode_g.fohh_origin_fail == 0) {
                wally_oper_mode_update(oper_mode_g.mode, oper_data);
        }
    } else {
        wally_enable_db_conn();
        wally_oper_mode_update(oper_mode_g.mode, oper_data);

        if (oper_mode_g.origin_disconnect) {
            oper_mode_g.origin_disconnect = false;
        }
    }

    return WALLY_RESULT_NO_ERROR;
}

int32_t wally_oper_origin_disconnect (struct wally_oper_data *oper_data)
{
    int32_t ret = WALLY_RESULT_NO_ERROR;

    if (oper_data->sub_event == SE_FOHH_DISCONNECT) {
        /* FOHH server is disconnected
         * Leaf-Wally - It has 2 FOHH server connections,  g-wally and Origin-wally connection
         * Origin-Wally - It has one g-wally connection
         *   - Increment the counter and it will be stored only to report the failure
         *   - No state change for FOHH disconnect event
         *
         *   */
        WALLY_LOG(AL_INFO, "Wally operational mode FOHH Server disconnected");
        oper_mode_g.fohh_origin_fail++;
        /*
         * FOHH failure will not change the state
         */
        wally_oper_mode_update(oper_mode_g.mode, oper_data);
    } else {
        /* Handles
         * E_ORIGIN_DISC (MANUAL command)
         * if it is manual command update the flag
         * */
        if (oper_data->sub_event == SE_DRAIN_MANUAL ||
                oper_data->sub_event == SE_NO_DRAIN_MANUAL ||
                oper_data->sub_event == SE_MANUAL) {
            oper_mode_g.origin_disconnect = true;
            oper_mode_g.origin_disconnect_count++;
            /* Disconnect RDS */
            wally_disable_db_conn();
        }

        /* Origin-disconnect will not drain the clients by default.
         * If requested manually drain it */
        if (oper_data->sub_event == SE_DRAIN_MANUAL) {
            /* Drain all the connected clients */
            ret = oper_mode_g.drain_cb();
        }

        wally_oper_mode_update(oper_mode_g.mode, oper_data);
    }

    return ret;
}

/* Manual offline mode handler, it does below operations
 * - It drains all the clients,
 * - Disable DB access
 * - Disconnect all the clients*/
int32_t wally_oper_offline (struct wally_oper_data *oper_data)
{
    int32_t ret = WALLY_RESULT_NO_ERROR;
    WALLY_LOG(AL_INFO, "Wally operational mode Offline");

    /* Disconnect DB */
    wally_disable_db_conn();
    /* Reject client connection */
    wally_disable_client_access();
    /* Drain is default for offline. If no-drain is requested
     * skip the draining */
    if (oper_data->sub_event != SE_NO_DRAIN_MANUAL) {
        /* Drain all the connected clients */
        ret = oper_mode_g.drain_cb();
    }
    oper_mode_g.offline = true;
    /* Offline is superset of Client and Origin disconnect,
     * so reset both flags. */
    oper_mode_g.client_disconnect = false;
    oper_mode_g.origin_disconnect = false;
    oper_mode_g.offline_count++;
    wally_oper_mode_update(S_OFFLINE, oper_data);

    return ret;
}

/* DB connection is recovered */
int32_t wally_oper_db_recovery (struct wally_oper_data *oper_data)
{
    /* Local DB failure and One connection failure will not affect
     * the functionality of the Wally, so keep the old state and
     * just update the events */
    if (oper_data->sub_event == SE_DB_LOCAL) {
        WALLY_LOG(AL_INFO, "Wally operational mode local DB recovered");
        wally_oper_mode_update(oper_mode_g.mode, oper_data);
        oper_mode_g.ldb_failure = false;
    } else if (oper_data->sub_event == SE_DB_POLL_FAILED){
        WALLY_LOG(AL_INFO, "Wally operational mode polling recovered");
        oper_mode_g.poll_failure = false;
    } else {
        WALLY_LOG(AL_INFO, "Wally operational mode RDS conn recovered");
        wally_oper_mode_update(oper_mode_g.mode, oper_data);
        oper_mode_g.rds_db_conn_fail_count = oper_data->db_conn_fail_count;
        /* Timmer would have stopped, when RDS is disconnected, so restart the
         * trimmer again for the tables which are stopped */
        if (oper_mode_g.trimmer_stop) {
            wally_start_all_cleanup(oper_mode_g.wally);
        }
        oper_mode_g.rds_failure = false;
        oper_mode_g.trimmer_stop = false;
    }

    return WALLY_RESULT_NO_ERROR;
}

/* Wally DB failure operational mode handler
 * This processes the below sub event
 * 1. SE_DB_LOCAL - Local DB failure
 * 2. SE_DB_REMOTE_ONE_CONN - RDS connection failed, but still
 *                            at least one connection is active
 * 3. SE_DB_REMOTE_ALL_CONN - All RDS connections failed
 * 4. SE_DB_POLL_FAILED - Polling failed
 * */
int32_t wally_oper_db_fail(struct wally_oper_data *oper_data)
{
    /* Local DB failure and One connection failure will not affect
     * the functionality of the Wally, so keep the old state and
     * just update the events */
    if (oper_data->sub_event == SE_DB_LOCAL) {
        WALLY_LOG(AL_INFO, "Wally operational mode local DB failure");
        wally_oper_mode_update(oper_mode_g.mode, oper_data);
        oper_mode_g.ldb_failure = true;
    } else if (oper_data->sub_event == SE_DB_POLL_FAILED) {
        /* Polling is failed, could be RDS issue */
        WALLY_LOG(AL_INFO, "Wally operational mode RDS polling failed");
        if (!oper_mode_g.poll_failure) {
            oper_mode_g.poll_failure = true;
            wally_oper_mode_update(oper_mode_g.mode, oper_data);
        }
    } else {
        // oper_data->sub_event == SE_DB_REMOTE
        WALLY_LOG(AL_INFO, "Wally operational mode all RDS connections failed");
        if (oper_data->db_conn_count == oper_data->db_conn_fail_count) {
            /* All connections down. Stop the trimmer and update the RDS failure */
            if (wally_stop_all_cleanup(oper_mode_g.wally)) {
                oper_mode_g.trimmer_stop = true;
            }
            oper_mode_g.rds_failure = true;
        }
        oper_mode_g.rds_db_conn_fail_count = oper_data->db_conn_fail_count;
        wally_oper_mode_update(oper_mode_g.mode, oper_data);
    }

    return WALLY_RESULT_NO_ERROR;
}

/* Wally terminate mode handler, it drains all the client and
 * trigger the watchdog */
int32_t wally_oper_terminate (struct wally_oper_data *oper_data)
{
    int32_t ret = WALLY_RESULT_NO_ERROR;
    WALLY_LOG(AL_CRITICAL, "-------Critical unrecoverable error. Watchdog shutdown trigger initiated------");
    WALLY_LOG(AL_CRITICAL, "---------------------Process will be down in %d seconds-----------------------",
            wally_default_hb_timeout_s);
    ret = oper_mode_g.drain_cb();
    oper_mode_g.termination = true;

    /* Reject client connection */
    wally_disable_client_access();
    wally_oper_mode_update(S_TERMINATE, oper_data);

    wally_infinite_loop();
    return ret;
}

/* Init operational mode data*/
int32_t wally_oper_mode_init(wally_oper_drain_cb cb, int is_leaf_wally, struct wally *wally)
{
    pthread_t thread;
    int32_t res = 0;

    oper_mode_g.drain_cb = cb;
    oper_mode_g.wally = wally;
    oper_mode_g.is_leaf_wally = is_leaf_wally?true:false;
    ZTAILQ_INIT(&oper_mode_g.oper_queue_head);
    oper_mode_g.init_done = true;

    res = zthread_create(&thread,
            wally_oper_fsm_thread,
            NULL,
            "oper_fsm_wally",
            wally_default_hb_timeout_s,  /* 60s thread watchdog */
            16*1024*1024,   /* 16 MB stack. */
            60*1000*1000,   /* 60s Statistics interval */
            NULL);
    if(res) {
        WALLY_LOG(AL_ERROR, "Wally cleanup thread init failed: %d", res);
        return res;
    }

    res = zpn_wally_oper_stats_init();
    if(res) {
        WALLY_LOG(AL_ERROR, "Initializing wally oper stats failed: %d", res);
        return res;
    }

    return WALLY_RESULT_NO_ERROR;
}

/* Dequeue operational mode's data and return it */
struct wally_oper_data *wally_oper_dequeue_event()
{
    struct wally_oper_data *data = NULL;

    data = ZTAILQ_FIRST(&(oper_mode_g.oper_queue_head));
    if (data) {
        ZTAILQ_REMOVE(&(oper_mode_g.oper_queue_head), data, oper_queue);
    }

    return data;
}

/* Enqueue operational mode's data to the queue, which will be dequeued by oper_fsm thread*/
void wally_oper_enqueue_event(struct zevent_base *base, void *void_cookie, int64_t cookie)
{
    struct wally_oper_data *data = (struct wally_oper_data *) void_cookie;

    ZTAILQ_INSERT_TAIL(&(oper_mode_g.oper_queue_head), data, oper_queue);

    /* Manually trigger the event. No need to wait for timer trigger */
    event_active(oper_mode_g.timer_event, EV_TIMEOUT, 0);

    return ;
}

int32_t wally_oper_event_post(enum wally_oper_events event, enum wally_oper_sub_events sub_event,
        struct wp_db *wp_db)
{
    struct wally_oper_data *data = NULL;
    if (!oper_mode_g.is_active_done &&
            event == E_SCHEMA_MISMATCH) {
        WALLY_LOG(AL_INFO, "Schema mismatch in INIT state");
        return WALLY_RESULT_NO_ERROR;
    }

    if (!oper_mode_g.is_active_done &&
            event == E_LDP_WRITE_FAIL ) {
        WALLY_LOG(AL_INFO, "Local DB write fail -  INIT state");
        return WALLY_RESULT_NO_ERROR;
    }

    if (!oper_mode_g.init_done) {
        WALLY_LOG(AL_CRITICAL, "-------Critical unrecoverable error. Watchdog shutdown trigger initiated------");
        WALLY_LOG(AL_CRITICAL,
                "---------------------Process will be down in %d seconds-----------------------",
                wally_default_hb_timeout_s);
        wally_infinite_loop();
        return WALLY_RESULT_NO_ERROR;
    }

    data = (struct wally_oper_data *)WALLY_MALLOC(sizeof(*data));
    if (!data) {
        return WALLY_RESULT_ERR;
    }

    if (event == E_DB_FAIL || event == E_DB_RECO) {
        data->db_conn_fail_count = wp_db->failed_connections_count;
        data->db_conn_count = wp_db->connections_count;
    }

    data->event = event;
    data->sub_event = sub_event;

    if (zevent_base_call(oper_mode_g.oper_base, wally_oper_enqueue_event, data, 0) != 0)
    {
        WALLY_LOG(AL_ERROR, "Queueing wally_oper_enqueue_event failed ");
        return WALLY_RESULT_ERR;
    }

    return WALLY_RESULT_NO_ERROR;
}
