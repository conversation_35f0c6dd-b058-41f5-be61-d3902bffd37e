/*
 * wally_fohh.h. Copyright (C) 2013 Zscaler, Inc. All Rights Reserved.
 *
 * Wally origin database implementation for accessing a remote wally
 *    as an origin database. Also includes code for exporting local
 *    wally as an origin database.
 *
 * ASSUMPTIONS:
 *
 * 1. Assumes FOHH has already been initialized.
 *
 * 2. When server is created, assumes wally is already running.
 */

#ifndef __WALLY_FOHH_SERVER_H__
#define __WALLY_FOHH_SERVER_H__

#include "wally/wally.h"
#include "fohh/fohh.h"
#include <stdbool.h>
#include "zhash/zhash_table.h"

#define WALLY_LOG_FILE_LEN       256
#define WALLY_PORT_LEN           32
#define WALLY_DOMAIN_LEN         512
#define WALLY_MAX_DRAIN_HIST     5
#define WALLY_PORT_POS           5
#define WALLY_DRAIN_LOG          "/zpath/log/wally_drain"
#define WALLY_CLIENT_LOG         "/zpath/log/wally_clients"

/* Wally Drain status */
enum wally_drain_status {
    WALLY_DRAIN_NONE,         /* No status */
    WALLY_DRAIN_IN_PROGRESS,  /* Wally server drain in progress */
    WALLY_DRAIN_DESTROY,      /* Drain completed, destroy the drained clients */
    WALLY_DRAIN_INVALID
};

/* Drain phase. Used only in Operational-mode drain.
 * Two-phase drain is required in operational-mode
 * to drain high-priority clients first and then low-priority */
enum wally_drain_phase {
    WALLY_DRAIN_P_NONE,
    WALLY_DRAIN_P1,        /* P1/High priority draining phase */
    WALLY_DRAIN_P2,        /* P2/Low priority draining phase */
};


/* Two phase drain state
 * State flow
 * 1. WALLY_DRAIN_S_P1 Drain leaf-wally clients
 * 2. WALLY_DRAIN_S_P1_P2_DELAY -
 *    Sleep for WALLY_DRAIN_S_P1_P2_DELAY seconds after the completion of P1 clients.
 *    This delay is required for P1 clients to settle down in fault tolerant wally
 * 3. WALLY_DRAIN_S_P2 Drain P2 clients
 *
 * WALLY_DRAIN_S_DELAY will be given for each draining iteration*/
enum wally_drain_state {
    WALLY_DRAIN_S_P1,            /* P1 draining */
    WALLY_DRAIN_S_DELAY,         /* Delay to be given for each 1% drain clients*/
    WALLY_DRAIN_S_P1_P2_DELAY,   /* Delay to be given after the completion of P1 clients*/
    WALLY_DRAIN_S_P2,            /* P2 draining */
};

struct wally_fohh_server;
struct wally_fohh_server_client;

enum wally_server_client_drain_status;
/*
 * Creation/initialization routine. This is a synchronous call.
 *
 * This exports the specified wally database as a service to which
 * clients can connect. It uses FOHH to manage incoming connections.
 *
 * if no_service_ip is set, then the server doesn't create a service
 * IP. The server is expected to be handed FOHH connections to convert
 * in to client access connections for the specified database.
 *
 * service_ip - The IP address to bind to. If NULL, binds to
 * INADDR_ANY on both IPv4 and IPv6
 *
 * use_ssl- Whether to use SSL on inbound connections or not.
 */
struct wally_fohh_server *wally_fohh_server_create(struct wally *wally,
                                                   enum argo_serialize_mode encoding,
                                                   enum fohh_connection_style style,
                                                   int no_service_ip,
                                                   struct argo_inet *service_ip,
                                                   uint16_t tcp_port_ne,
                                                   char *root_cert_file_name,
                                                   char *my_cert_file_name,
                                                   char *my_cert_key_file_name,
                                                   int use_ssl);

/*
 * Attach an arbitrary connected FOHH connection to this wally for
 * registration/deregistration purposes.
 *
 * Once handed off, the FOHH connection is 'owned' by
 * wally_fohh_server.
 *
 * 'allow_tables' is the set of tables the client is allowed to access
 * without regard to indexing. (i.e. unfiltered).
 *
 * 'filter_tables' is the set of tables the client is allowed to
 * access, but only if the requested indexing matches the filter
 * mask/value pair. For filtered tables, only numeric indexed tables
 * are allowed.
 *
 * If neither allow_tables or filter_tables is specified, then all
 * tables are accessible in all forms.
 *
 * If a filter mask + value are set, then row registrations are only
 * allowed for integer keys, and the integer keys when masked must
 * match the filter value, else the request will be rejected. (No
 * full-table requests allowed!)
 *
 * The return value is the only indication of success. (Afterwards it
 * can be tossed, since there isn't anything you can do with it...)
 *
 * NOTE: The only FOHH connections allowed are those running standard
 * argo over FOHH. No TLV, etc, connections allowed!
 */
struct wally_fohh_server_client *wally_fohh_create_fohh_from_client(struct wally_fohh_server *wfs,
                                                                    struct fohh_connection *connection,
                                                                    char **allow_tables,
                                                                    int allow_tables_count,
                                                                    char **filter_tables,
                                                                    int filter_tables_count,
                                                                    int64_t filter_mask,
                                                                    int64_t filter_value,
                                                                    int64_t pass_key,
                                                                    struct zhash_table *zhash_table_filter,
                                                                    struct zhash_table *nullify_state,
                                                                    fohh_connection_callback_f  *disconnect_callback);

/*
 * Make it possible to receive row writes/updates from clients feeding
 * data back to us for the specified table.
 */
int wally_fohh_server_allow_table_writes(struct wally_fohh_server *server,
                                         const char *table_name);


/*
 * List the connected clients details
 *
 * wfs: server
 * fp: Client list redirected to this file
 * domain: Search pattern. Matching clients will be redirect to the "fp". If NULL
 *         all the client data will be redirected
 * Port: Port number. If given, only matching client will be logged
 * count : Number of clients matched/logged for given domain/port
 */
int wally_server_client_list (struct wally_fohh_server *wfs, FILE *fp, const char *domain, const char *port, uint32_t *count);

/*
 * Reset the clients which are marked for drain
 *
 * wfs: server
 * fp: Reset logs list redirected to this file
 * is_completed: Will be set true, if all the clients are reset
 * drain_rate: Number of client should be reset per call
 * two_phase_drain : Is it two-phase drain - true only for the drain initiated by operational-mode
 * phase : Current phase of the drain
 */
int wally_server_fohh_reset_clients (struct wally_fohh_server *wfs, bool *is_completed, FILE *fp, int drain_rate, uint32_t *reset_count,
                                     uint32_t *reset_fail_count, bool two_phase_drain, enum wally_drain_phase phase);

/*
 * Mark the client for drain for the matching domain/port. Marked clients will be drained using the
 * routine wally_server_fohh_reset_clients()
 *
 * wfs: server
 * domain: Search pattern. Matching clients will be marked for drain. If NULL, " drain_count" clients will be marked
 *         from the client list.
 * Port: Port number.  Matching clients will be marked for drain.
 * force : Forcefully mark and include wally-leaf for marking
 * drain_count : Number of clients will be chosen for given domain/port for marking
 * marked_count: Number of clients marked for the given wfs(server)
 * high_prio: Is it high_priority client?
 */
int wally_server_mark_drain (struct wally_fohh_server *wfs, const char *domain, const char *port,
        uint32_t force, uint32_t *marked_count, int drain_count, bool high_prio);

/*
 * Get the client count for the matching domain/port.
 *
 * wfs: server
 * domain: Search pattern. If null, return all the clients
 * Port: Port number.  Matching clients will be counted. If null, return all the clients
 * force : Forcefully count and include wally-leaf for counting
 * Return - pattern_match_client: Total number of clients cliented, except leaf-wally
 * Return - wally_client : Leaf wally client count
 */
int wally_server_clients_count (struct wally_fohh_server *wfs, const char *domain, const char *port,
        uint32_t force, uint32_t *pattern_match_client, uint32_t *wally_client);

void wally_server_destroy_clients (struct wally_fohh_server *wfs);
const char *wally_server_get_drain_status_string(enum wally_server_client_drain_status drain_status);
void wally_disable_client_access();
void wally_enable_client_access();
int client_register_row_string_callback_internal(void *cookie, void *structure_cookie, struct argo_object *object);
int client_deregister_row_string_callback_internal(void *cookie, void *structure_cookie, struct argo_object *object);
void wally_fohh_client_destroy(struct wally_fohh_server_client *wfc);


/* Function : wally_fohh_server_send_recovery_begin_request
 * Arg      : wfs - wally fohh server for which requests to be sent all clients
 *            table_name - table name to be recovered
 *            recovery_sequence - recovery sequence number
 *            recovery_timeout - recovery timeout
 *            sync_missing_rows - sync only missing rows or all rows
 * Ret      : Number of requests sent out for this server.
 * Desc     : This function iterates all the clients for the server and sends begin recovery
 *              requests for each client.
 */
int wally_fohh_server_send_recovery_begin_request(struct wally_fohh_server *wfs,
                                                    char *table_name, int64_t recovery_sequence,
                                                    int64_t recovery_timeout, int sync_missing_rows);
/* Function : wally_fohh_server_send_row_to_all_clients
 * Arg      : wfs - Wally Fohh Server
 *            table - wall table
 *            row - row argo object
 * Ret      : FOHH_RESULT_NO_ERROR if no error, otherwise appropriate error.
 * Desc     : Sends rows to fohh clients connected to this server
 */
int wally_fohh_server_send_row_to_all_clients(struct wally_fohh_server *wfs, struct wally_table *table, struct argo_object *row);
/* Function : wally_fohh_server_send_recovery_end_request
 * Arg      : wfs - Wally Fohh Server
 *            table_name - name of the recovery table
 *            server_sent_rows - server_sent_rows
 * Ret      : Number of requests sent
 * Desc     : This function iterates clients for the server and sends request
 *                  to the clients.
 */
int wally_fohh_server_send_recovery_end_request(struct wally_fohh_server *wfs,
                                                    char *table_name, int64_t recovered_rows);
/* Function : wally_fohh_retrieve_unsupported_clients
 * Arg      : wfs - wally fohh server
 *            buffer - buffer to be filled.
 *            start - starting position in buffer
 *            bufsize - total buffer size
 * Ret      : appropriate WALLY_RESULT
 * Desc     : This function iterates and write recovery unsupported clients
 *              into buffer
 */
int wally_fohh_retrieve_unsupported_clients(struct  wally_fohh_server *wfs, char *buffer, int64_t *start, size_t bufsize);
#endif /* __WALLY_FOHH_SERVER_H__ */
