/*
 * wally_oper.h. Copyright (C) 2024 Zscaler, Inc. All Rights Reserved.
 *
 * Wally operational modes and events header file.
 *
 */
#ifndef __WALLY_OPER_H__
#define __WALLY_OPER_H__

#include "wally/wally_private.h"
#include "wally/wally_db.h"

ZTAILQ_HEAD(wally_oper_queue_head, wally_oper_data);
/* Operational modes */
enum wally_oper_modes {
    S_NA,                /* Just to start the number */
    S_INIT,              /* INIT state */
    S_ACTIVE,            /* All DB initialized and App is running */
    S_OFFLINE,           /* Postgres and clients are disconnected */
    S_TERMINATE,         /* Termination in progress */
    S_MAX
};

/* Operational events */
enum wally_oper_events {
    E_INIT,              /* INIT started */
    E_ACTIVE,            /* INIT completed and APP is running */
    E_ORIGIN_DISCONNECT, /* RDS/FOHH failure detected */
    E_ORIGIN_CONNECT,    /* FOHH server connected*/
    E_CLIENT_DISCONNECT, /* Client disconnected and new connections rejected */
    E_CLIENT_CONNECT,    /* Accept new client connections */
    E_OFFLINE,           /* OFFLINE itascurl command executed */
    E_TERMINATE,         /* Termination requested */
    E_DB_FAIL,           /* DB access failed */
    E_DB_RECO,           /* DB access recovered */
    E_TRANSACTION_FAIL,  /* SQL transaction failed */
    E_SCHEMA_MISMATCH,   /* Schema mismatch/failure */
    E_LDP_WRITE_FAIL,    /* LDB write object failure */
    E_RECOVERY,          /* Recovery started */
    E_MAX
};

/* Operational sub-events,
 * Event could happen for multiple reasons, so sub-events helps
 * to identify the proper reason for the event trigger */
enum wally_oper_sub_events {
    SE_NONE,
    SE_DB_LOCAL,           /* Local DB failure */
    SE_DB_REMOTE,          /* RDS connections down */
    SE_DB_POLL_FAILED,     /* Polling stopped */
    SE_FOHH_DISCONNECT,    /* FOHH server disconnected */
    SE_FOHH_ACTIVE,        /* FOHH server connected */
    SE_MANUAL,             /* Manual command */
    SE_DRAIN_MANUAL,       /* Drain and Manual command */
    SE_NO_DRAIN_MANUAL,    /* No-drain and Manual command */
    SE_MEM_FAIL,           /* Memory resource failure */
    SE_FSM_FAIL,           /* FSM state failures */
    SE_SYNTAX_FAIL,        /* SQL syntax failure */
    SE_LONG_RUN_QUERY,     /* SQL long running query */
    SE_ARGO_FAIL,          /* AGRO failure */
    SE_RECOVERY_START,     /* Recovery Start */
    SE_RECOVERY_CMPLT,     /* Recovery Complete */
    SE_RECOVERY_TIMEOUT,   /* Recovery Timeout */
    SE_RECOVERY_FAILURE,   /* Recovery Failure */
    SE_MAX,
};

typedef int32_t (*wally_oper_fsm)(struct wally_oper_data *oper_data);
extern const char *wally_oper_mode_strings[S_MAX + 1];
extern const char *wally_oper_event_strings[E_MAX + 1];
extern const char *wally_oper_sub_event_strings[SE_MAX + 1];
extern struct wally_oper_mode_g oper_mode_g;
typedef int32_t (*wally_oper_drain_cb)();


/* Operation mode global main structure,
 * used for FSM state handler */
struct wally_oper_mode_g {
    enum wally_oper_modes mode;            /* Current operational mode */
    enum wally_oper_modes prev_mode;       /* Previous operational mode */
    enum wally_oper_events event;          /* Current operational event  */
    enum wally_oper_events prev_event;     /* Current operational event  */
    enum wally_oper_sub_events sub_event;  /* Current operational sub-event  */
    enum wally_oper_sub_events prev_sub_event; /* Prev operational sub-event  */
    struct wally *wally;                   /* Back pointer to Wally */
    struct timeval oper_time;              /* Mode Set time */
    struct timeval oper_prev_time;         /* Prev Mode set time */
    struct zevent_base *oper_base;         /* Zevent base. Used to post the event */
    struct wally_oper_queue_head oper_queue_head;  /* operational mode queue */
    bool is_leaf_wally;                    /* TRUE if this is leaf-wally */
    bool init_done;                        /* TRUE in wally and false in other APPs */
    bool oper_state_change_in_progress;    /* Oper-mode FSM is in processing state */
    bool is_active_done;                   /* Is wally ever moved to ACTIVE state. */
    bool ldb_failure;                      /* Local DB failure*/
    bool termination;                      /* Wally termination in progress */
    bool origin_disconnect;                /* Manual origin-disconnect action executed */
    bool client_disconnect;                /* Manual client-disconnect action executed */
    bool offline;                          /* Manual offline mode executed */
    uint32_t origin_disconnect_count;      /* origin-disconnect executed count */
    uint32_t client_disconnect_count;      /* client-disconnect executed count*/
    uint32_t offline_count;                /* offline mode executed count*/
    bool drain_pending;                    /* Drain command is pending for this state */
    bool drain_in_process;                 /* Client draining is in process for this state */
    uint32_t rds_db_conn_fail_count;       /* No of failed RDS DB connection */
    bool rds_failure;                      /* RDS failure detected, All the DB connections are down */
    bool poll_failure;                     /* Polling failed */
    uint64_t trasaction_fail;              /* Transaction failure happened */
    uint64_t schema_mismatch;              /* Schema failure detected */
    uint32_t fohh_origin_fail;             /* FOHH origin servers disconnected */
    uint32_t ldb_write_failure;            /* Local DB write failure */
    bool trimmer_stop;                     /* Trimmer stopped due to operational mode state change*/
    struct event *timer_event;
    wally_oper_drain_cb drain_cb;          /* Drain callback function */
    uint32_t recovery_in_progress;         /* Recovery requests*/
    uint32_t recovery_failures;            /* Recovery completed */
};

/* Structure to post the the event from Wally thread to
 * oper_fsm thread */
struct wally_oper_data {
    enum wally_oper_events event;             /* Triggered event */
    enum wally_oper_sub_events sub_event;     /* Triggered sub-event*/
    uint32_t db_conn_fail_count;              /* No of failed RDS DB connection */
    uint32_t db_conn_count;                   /* No of DB connection */
    ZTAILQ_ENTRY(wally_oper_data) oper_queue; /* TAILQ enter - head "oper_queue_head"*/
};

void wally_error_handler (void *cookie, struct wp_connection *conn, bool trigger_wd,
        enum wally_oper_events event, enum wally_oper_sub_events sub_event);
int32_t wally_oper_mode_init (wally_oper_drain_cb cb, int is_leaf_wally, struct wally *wally);
void wally_oper_drain_completed (struct zevent_base *base, void *void_cookie, int64_t cookie);
void wally_oper_init_fsm_cb();
int32_t wally_oper_handler (struct wally_oper_data *oper_data);
int32_t wally_oper_na (struct wally_oper_data *oper_dat);
int32_t wally_oper_active (struct wally_oper_data *oper_data);
int32_t wally_oper_trans (struct wally_oper_data *oper_data);
int32_t wally_oper_sch_fail (struct wally_oper_data *oper_data);
int32_t wally_oper_init (struct wally_oper_data *oper_data);
int32_t wally_oper_origin_disconnect (struct wally_oper_data *oper_data);
int32_t wally_oper_origin_connect (struct wally_oper_data *oper_data);
int32_t wally_oper_main (struct wally_oper_data *oper_data);
int32_t wally_oper_client_disconnect (struct wally_oper_data *oper_data);
int32_t wally_oper_client_connect (struct wally_oper_data *oper_data);
int32_t wally_oper_offline (struct wally_oper_data *oper_data);
int32_t wally_oper_terminate (struct wally_oper_data *oper_data);
int32_t wally_oper_db_fail (struct wally_oper_data *oper_data);
int32_t wally_oper_ldp_write_fail (struct wally_oper_data *oper_data);
int32_t wally_oper_db_recovery (struct wally_oper_data *oper_data);
int32_t wally_oper_event_post (enum wally_oper_events event, enum wally_oper_sub_events sub_event,
        struct wp_db *wp_db);
struct wally_oper_data *wally_oper_dequeue_event();
int wallyd_oper_mode_init (int is_leaf_wally, struct wally *wally);
int32_t wally_oper_recovery_handle(struct wally_oper_data *oper_data);

#endif /* __WALLY_OPER_H__ */
