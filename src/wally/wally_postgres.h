#ifndef __WALLY_POSTGRES_H__
#define __WALLY_POSTGRES_H__
/*
 * wally_postgres.h. Copyright (C) 2013 Zscaler, Inc. All Rights Reserved.
 *
 * Wally origin database implementation for accessing a postgres
 *   database.
 *
 */
#include "wally/wally_private.h"

/* This database starts up as follows, for a given table:
 *
 * 1. Register for notifications on the table. (Basically update
 *    notification). This
 * 2. Find the last sequence in the table.
 * 3. Running.
 *
 * When running, the following occur:
 *
 * 1. Upon notification- read new sequences.
 * 2. For each new sequence, test it against all listening indexes.
 * 3. If any index matches, send the row to wally.
 * 4. Don't listen for notifications again for xxx seconds. (to keep
 *    from spamming the database too hard when large numbers of
 *    updates occur simultaneously)
 *
 * 1. Upon registration- read matching rows from postgres.
 * 2. For each row returned, send the row to wally.
 * 3. Send completion message to wally.
 *
 * 1. Upon row push- Simply write the row to the database. (<PERSON> has
 *    already processed the row- no need to send it to wally.)
 *
 *
 * Couple special cases:
 *
 * 1. When a table is first accessed by name, the following occurs:
 *
 * 2. If the table did not exist, it is created with at least the
 *    default id/sequence columns. If other columns are known, they
 *    are created as well.
 *
 * 3. When rows are written, (only argo objects are written), columns
 *    are added if necessary to match the fields of the argo object.
 *
 * 4. When rows are read, the argo object description is expanded to
 *    support all columns.
 */

/*
 * Routine for retrieving current connection state. This routine
 * populates the table, which is statically allocated. (i.e. you do
 * not need to free it)
 *
 * Both state and state_count are passed out.
 *
 * This routine cannot fail. (famous last words)
 */
/*
 * The standard callins from wally into this origin DB.
 */

int wally_postgres_register_for_table(void *callout_cookie,
                                      int64_t sequence,
                                      const char *table_name,
                                      const char *column_name,
                                      const char *key);

int wally_postgres_deregister_for_table(void *callout_cookie,
                                        const char *table_name,
                                        const char *column_name);

/*
 * Creation/initialization routine. This is a synchronous call.
 *
 * if password is non-null, then this routine uses SSL to connect to
 * the remote database.
 *
 * This routine spawns a thread to perform simple maintenance on
 * postgres state. The thread is given 'thread_name'. (Debugging/stats
 * purposes only)
 *
 * createdb_cookie can be NULL. If it is not NULL, it should be the
 * result of a prior successful call to wally_postgres_create (with a
 * writable table). If so passed, then, if the requested database does
 * not exist, then this routine will attempt to create the database
 * using the prior, successful dabase creation routine.
 *
 * is_row_writable is an indication that the database can have rows
 * written to it.
 *
 * is_alterable is an indication that the database can be altered to
 * match the argo_description for the table. (Generally true for slave
 * databases, generally false for true origin databases)
 *
 * is_true_origin is an indication that this postgres database is a
 * true origin, which is row-written to slightly differently from
 * others.
 *
 * schema - db schema to look for tables. Only used for wallyd to remote
 * db connections.
 */
void *wally_postgres_create_with_schema(void *db_to_wally_cookie,
                                        const char *host,
                                        char *user,
                                        const char *dbname,
                                        const char *thread_name,
                                        char *password,
                                        char *schema,
                                        int nconns,
                                        int is_row_writable,
                                        int is_alterable,
                                        int is_true_origin,
                                        int is_endpoint,
                                        int64_t polling_interval_us);

/*
 * Kind of wrapper function for wally_postgres_create_with_schema
 *
 * If you want to create db connection with particular schema - call
 * wally_postgres_create_with_schema with schema name, otherwise you
 * can use wally_postgres_create function which will ultimately call
 * wally_postgres_create_with_schema with schema name set to NULL.
 */
void *wally_postgres_create(void *db_to_wally_cookie,
                            const char *host,
                            char *user,
                            const char *dbname,
                            const char *thread_name,
                            char *password,
                            int nconns,
                            int is_row_writable,
                            int is_alterable,
                            int is_true_origin,
                            int is_endpoint,
                            int64_t polling_interval_us);

struct wp_connection;
struct wp_table;
struct wp_db;

int postgres_update_table_state_machine(struct wp_table *t, struct wp_connection *wp_conn);
void wp_write_object(struct wp_db *wp_db);
void wp_write_batch_object(struct wp_db *wp_db);
void wally_postgres_update_min_seq(struct wp_connection *wp_conn);
void wally_postgres_do_cleanup(struct wp_connection *wp_conn);
int wally_postgres_cleanup(void *callout_cookie,
                           const char *table_name,
                           int64_t *sequence,
                           int64_t *deleted,
                           int64_t *key_int,
                           int      sz,
                           int64_t min_seq_auto_update);

/*
 * postgress request functions
 */
int wp_send_read_begin(struct wp_connection *wp_conn);
int wp_send_read_select_nolock(struct wp_connection *wp_conn);
int wp_send_sequence_delete_request(struct wp_connection *wp_conn);
int wp_send_poll_check(struct wp_connection *wp_conn);
int wp_send_gvr_poll_check(struct wp_connection *wp_conn);
int wp_send_gvr_pending_transaction_check(struct wp_connection *wp_conn);
int wp_write_object_update(struct wp_connection *wp_conn,
		struct wally_local_db_write_queue_object *queue_object);
int wp_write_object_upsert(struct wp_connection *wp_conn, int queue_index);
int wp_write_batch_on_conn(struct wp_db *wp_db, struct wp_connection *wp_conn, int queue_index);


/* DB recover function */
void wp_recovery_db_conn(struct wp_db *wp_db);
int wally_postgres_db_conn_config(struct wp_db *wp_db, char *schema, int32_t index);

/*
 * wally postgres connection and server status
 */
void wp_get_status(void *callout_cookie);

// PG specific flags set by wally command line options
// -concurrent_write
extern int wally_postgres_concurrent_write;
// -synchronous_commit_off
extern int wally_postgres_synchronous_commit_off;

/* Copy Postgres params from src to dest*/
#define WP_CFG_PQSL_PARAMS(dest, data) \
    do { \
    if (dest == NULL) {\
        dest = (char *)malloc(WALLY_PSQL_PARAM_BUF); \
        snprintf(dest, WALLY_PSQL_PARAM_BUF, "%s", (char *)data); \
    } } while (0)

#define WALLY_FREE_UPSERT_RESOURCES(stmt, buff, row_values) do { \
    if (stmt) {  WALLY_FREE(stmt);} \
    if (buff) {  WALLY_FREE(buff);} \
    if (row_values) { WALLY_FREE(row_values);} \
} while (0)

#endif /*__WALLY_POSTGRES_H_*/
