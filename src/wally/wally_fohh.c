/*
 * wally_fohh.c. Copyright (C) 2013 Zscaler, Inc. All Rights Reserved.
 */

#include "wally/wally.h"
#include "wally/wally_fohh.h"
#include "wally/wally_fohh_compiled.h"

struct argo_structure_description *wally_fohh_register_row_request_string_description;
struct argo_structure_description *wally_fohh_deregister_row_request_string_description;
struct argo_structure_description *wally_fohh_request_result_description;
struct argo_structure_description *wally_fohh_register_row_request_integer_description;
struct argo_structure_description *wally_fohh_deregister_row_request_integer_description;
struct argo_structure_description *wally_fohh_version_description;
struct argo_structure_description *wally_fohh_version_ack_description;
struct argo_structure_description *wally_fohh_version_recovery_begin_request;
struct argo_structure_description *wally_fohh_version_recovery_end_request;
struct argo_structure_description *wally_fohh_version_recovery_complete_response;

/* Note: These are idempotent, so this doesn't need to be lock-protected. */
void wally_fohh_register_structures(void)
{
    static int initialized = 0;

    if (initialized) return;
    initialized = 1;
    wally_fohh_register_row_request_string_description = argo_register_global_structure(WALLY_FOHH_REGISTER_ROW_REQUEST_STRING_HELPER);
    wally_fohh_deregister_row_request_string_description = argo_register_global_structure(WALLY_FOHH_DEREGISTER_ROW_REQUEST_STRING_HELPER);
    wally_fohh_register_row_request_integer_description = argo_register_global_structure(WALLY_FOHH_REGISTER_ROW_REQUEST_INTEGER_HELPER);
    wally_fohh_deregister_row_request_integer_description = argo_register_global_structure(WALLY_FOHH_DEREGISTER_ROW_REQUEST_INTEGER_HELPER);
    wally_fohh_request_result_description = argo_register_global_structure(WALLY_FOHH_REQUEST_RESULT_HELPER);
    wally_fohh_version_description = argo_register_global_structure(WALLY_FOHH_VERSION_HELPER);
    wally_fohh_version_ack_description = argo_register_global_structure(WALLY_FOHH_VERSION_ACK_HELPER);
    wally_fohh_version_recovery_begin_request = argo_register_global_structure(WALLY_FOHH_RECOVERY_BEGIN_REQUEST_HELPER);
    wally_fohh_version_recovery_end_request = argo_register_global_structure(WALLY_FOHH_RECOVERY_END_REQUEST_HELPER);
    wally_fohh_version_recovery_complete_response = argo_register_global_structure(WALLY_FOHH_RECOVERY_COMPLETE_RESPONSE_HELPER);
}
