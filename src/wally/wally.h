/*
 * wally.h
 *
 * <PERSON> is a library designed to distribute information from a master
 * (origin) database to an arbitrary number of client systems. The
 * system can be daisy-chained multiple times, allowing for arbitrary
 * fanout.
 *
 *
 * Name origin: In the movie "Crocodile Dundee," <PERSON><PERSON> makes the
 * following observation when talking about psychiatrists:
 *
 * "... back there [outback] if you got a problem you tell <PERSON>. And
 * he tells everyone in town, brings it out in the open, no more
 * problem."
 *
 *
 * <PERSON> obtains data from the origin database by "registering
 * interest" in an object or set of objects. The entity that
 * registered interest in an object is called a "registrant". The
 * registrant's interest is satisfied by the origin database sending
 * wally all the row information appropriate for satisfying the
 * interest. <PERSON> then sends that data to the appropriate
 * registrant(s). The origin database, and wally, track all the
 * registrants to ensure all interests are satisfied. When changes
 * occur in the origin database that are of interest to a registrant,
 * those changes (rows of database) are sent to it.
 *
 * When a registrant is no longer interested in some particular data,
 * it revokes its interest in that data. After revocation, updates to
 * those portions of the database are no longer transferred from
 * origin to that registrant.
 *
 * It should be noted that registration/deregistration/row_movement
 * are asynchronous operations. It is possible to receive rows from an
 * earlier registration after it has been deregistered.
 *
 * There are two primary manners in which wally is used for database
 * replication: full-table and indexed.
 *
 * Full table replication is where a complete database table from the
 * origin is replicated in wally. In this case, all rows are always
 * updated. This method is accessed just like indexed replication (see
 * next), except that the index column of "" (empty string) is used
 * (i.e. no index)
 *
 * Indexed table replication is where a small subset of rows are all
 * that wally is interested in from the origin database. In this case,
 * only that small subset of rows is synchronized.
 *
 * You should only mix full table replication and indexed replication
 * if you understand the ramifications of doing so. (i.e. the system
 * operates on these two sets of data moderately independently.)
 *
 * Despite these two manners of use, and the special "empty string"
 * column name, the interface to wally remains the same.
 *
 *
 * Some details:
 *
 * Wally is exceptionally dependent on argo's "object" format for
 * storing/transferring data. See argo.h, and (unfortunately)
 * argo_private.h. Argo was extended purposefully in order to allow
 * configuration of index fields and sequence fields within it.
 *
 * Argo uses structures named DIRECTLY after tables. i.e. a table
 * named "users" will rely on an argo description named "users"
 *
 * Wally supports (without rebuilding) column growth.
 *
 * Wally supports (without rebuilding) column shrinking. (The data
 * will likely still be flying around, just mostly ignored by the end
 * system)
 *
 * Wally supports, but probably not efficiently, indexed column
 * growth.
 *
 * It is possible to back database distribution with a local
 * persistent database. (i.e. two origin databases, a local one for
 * immediate access and a remote one for slightly slower access)
 *
 * Fanout can be performed via multiple levels of wally systems.
 *
 * Fanout steps introduces some latency in configuration
 * distribution. (Though less so if locally persistently cached,
 * obviously)
 *
 * This distribution mechanism is designed to be resilient with loss
 * of connectivity to the origin database.
 *
 *
 * Wally places some requirements on how the database is configured,
 * as follows:
 *
 * -- There must be a unique key for each row in the table. The unique
 *    key must be either a string or integer data type. An "empty
 *    string" key and index 0 must never be used.
 *
 * -- There must be a unique, monotonically increasing, guaranteed
 *    sequentially ordered sequence number for every row. This field
 *    must be updated to a new sequence number on creation and on any
 *    update that changes any of the rest of the row. This is a little
 *    trickier than it sounds, as a simple "sequence" is not enough-
 *    it must be impossible for a reader to see, for the first time, a
 *    sequence earlier than the highest sequence it has already
 *    seen. This can most easily be accomplished in postgres by
 *    enforcing single-writer on the table. It is allowed for this
 *    sequence number to be the same as the unique key, so long as all
 *    other conditions are met. (obviously the unique key is then an
 *    integer, and not a string) In a future version, wally will be
 *    updated to perform its own table locking when performing
 *    sequence synchronization such that multiple writers don't need
 *    to have a table lock on update.
 *
 * -- Row removal is never allowed. There must be a "deleted" field
 *    available for each row, in the event that deletion is
 *    required. The interpretation of the deleted field is left to the
 *    user. (i.e. the user must ignore deleted rows when they are
 *    returned) In the future, there may be a facility created for
 *    manual row removal, but not at this time.
 *
 * -- Any columns added to a table after its inception MUST have a
 *    valid default value of zero/nil/empty. This is done to allow for
 *    the creation of columns without requiring the update of all
 *    existing rows with initial values- the default nil/zero value is
 *    acceptable. Furthermore, the software expecting to find new
 *    information out of this column must be prepared to accept the
 *    nil/zero default value that may exist in older rows. Note that
 *    argo does not have the ability to transfer the concept of
 *    "NULL"- it can only transfer empty strings and zero values, thus
 *    this requirement. (It is so limited because it maps these
 *    directly to structure members, which have difficulty
 *    representing a NULL, either, unless it's a referenced value,
 *    which is not always the case)
 *
 * -- Any columns removed from a table after its inception MUST have a
 *    valid default value of zero/nil/empty.
 *
 ***************************************************
 *
 * How it works:
 *
 *
 * There are several pieces of this system:
 *
 * 1. wally: An in-memory representation of what is being
 *    distributed. This is the information that is operated upon by
 *    the true consumer of the data. Basically the heart of
 *    wally. (Elaborated upon, below)
 *
 * 2. wally_config: The configuration interface
 *
 * 3. wally_user_api: An API for accessing data programatically within
 *    wally. Direct access for user programs, really. This is a rather
 *    direct C API. The wally_xfer_api is written on top of this.
 *
 * 4. wally_xfer_api: An API for transferring data between
 *    databases. Intended for simplicity rather than power. This is
 *    basically the registration and deregistration interface, as well
 *    as the data transfer interface, between systems. It is a text
 *    based interface.
 *
 * 5. wally_xfer_fohh: FOHH (Argo over TLS) interface to wally-xfer
 *    API- Both Client and Server sides.
 *
 * 6. wally_xfer_postgre: PSQL interface to wally-xfer API- Both read
 *    and write interfaces.
 *
 * Database table distribution will often look like the following,
 * where numbers represent systems. System 1 is origin postgres
 * service. System 2 is service fanout, System 3 is end-user system.
 *
 * 1. A.  Postgres                (origin database)
 *    < Network (Optional, by postgres config) >
 * 1. B.  wally_xfer_postgres     (xfer API)
 * 1. C.  wally                   (origin database wally)
 * 1. D.  wally_xfer_fohh- server (xfer API)
 *    < Network >
 * 2. A.  wally_xfer_fohh- client (xfer API)
 * 2. B.  wally                   (fanout wally)
 * 2. C.  wally_xfer_fohh- server (xfer API)
 *    < Network >
 * 3. A.  wally_xfer_fohh- client (xfer API)
 * 3. B.  wally                   (application system wally)
 * 3. C.  wally_user_api          (application)
 *
 *******************************
 *
 * wally:
 *
 * Responsible for maintaining database tables in-memory. It does NOT
 * require that database tables be in memory for all time- it will age
 * out entries as it is able (client side, and optional)
 *
 * wally only blocks due to mutex issues: There is only a single
 * read-write lock on the entire database at the
 * moment. Multiple/improved locking in the future is tbd.
 *
 * wally performs minor maintenance (timeouts) via a single thread per
 * wally_db.
 */

#ifndef __WALLY_H__
#define __WALLY_H__

#include "argo/argo.h"
#include <stdlib.h>
#include <stdbool.h>
#include <pthread.h>
#include "zhash/zhash_table.h"
#include "argo/argo_hash.h"

#define WALLY_ZDP(format...) snprintf(buf, size, ##format)

/*
 * Minimum Wally Compatibility Version
 */
#define _WALLY_VERSION_MAJOR_MIN_   21
#define _WALLY_VERSION_MINOR_MIN_   255
#define _WALLY_VERSION_PATCH_MIN_   7

/*
 * Return error codes. Taken directly from argo.
 */
#define WALLY_RESULT_NO_ERROR          ARGO_RESULT_NO_ERROR          /* AKA success */
#define WALLY_RESULT_ERR               ARGO_RESULT_ERR               /* Generic error, when none other are appropriate */
#define WALLY_RESULT_NOT_FOUND         ARGO_RESULT_NOT_FOUND         /* Could not find what was requested. */
#define WALLY_RESULT_NO_MEMORY         ARGO_RESULT_NO_MEMORY         /* Could not allocate memory */
#define WALLY_RESULT_CANT_WRITE        ARGO_RESULT_CANT_WRITE        /* Failure to write (output callback failed?) */
#define WALLY_RESULT_ERR_TOO_LARGE     ARGO_RESULT_ERR_TOO_LARGE     /* Requested data doesn't fit in space provided */
#define WALLY_RESULT_BAD_ARGUMENT      ARGO_RESULT_BAD_ARGUMENT      /* Asked for something wrong. */
#define WALLY_RESULT_INSUFFICIENT_DATA ARGO_RESULT_INSUFFICIENT_DATA /* Was not provided enough data to perform operation */
#define WALLY_RESULT_NOT_IMPLEMENTED   ARGO_RESULT_NOT_IMPLEMENTED   /* Yes, for features that are not yet implemented. */
#define WALLY_RESULT_BAD_DATA          ARGO_RESULT_BAD_DATA          /* Tried to parse data, but format seemed wrong. */
#define WALLY_RESULT_WOULD_BLOCK       ARGO_RESULT_WOULD_BLOCK       /* Attempting operation would result in blocking. Bad, naughty blocking. */
#define WALLY_RESULT_BAD_STATE         ARGO_RESULT_BAD_STATE         /* Encountered bad internal state while attempting operation */
#define WALLY_RESULT_INCOMPLETE        ARGO_RESULT_INCOMPLETE
#define WALLY_RESULT_ASYNCHRONOUS      ARGO_RESULT_ASYNCHRONOUS      /* Asynchronous means the request will still complete, but at a
                                                                      * later time, with a callback. As opposed to would_block, which
                                                                      * did not do the job, nor will it. */
#define WALLY_RESULT_EXCESS_DYN_FIELDS ARGO_RESULT_EXCESS_DYN_FIELDS /* The RPC has too many dynamic fields */
#define WALLY_RESULT_VALIDATION_ERR    ARGO_RESULT_VALIDATION_ERR    /* Indicates non-critical yet major failure during validation of some state/args/params at runtime */
#define WALLY_RESULT_NOT_READY         (ARGO_RESULT_MAX+1)           /* Requested DB is not ready. */
#define WALLY_RESULT_MAX               WALLY_RESULT_NOT_READY
#define REQUEST_AT_LEAST_ONE    1
#define FOHH_WORKER_WALLY "wally"
#define MAX_REG_REQ_CNT 10000
#define WALLY_CMD_CLIENT_BUFFER_SIZE  10000
#define WALLY_CMD_ERROR_BUFFER_SIZE 1024
#define WALLY_DURATION_US_IN_S        1000000
#define WALLY_ORIGIN_GB_INDEX    0

#define WALLY_SET_DB_TABLE_POLL  1
#define WALLY_DEFAULT_RECOVERY_TIMEOUT_SEC (10 * 60) //10m

#define WALLY_CLIENT_TYPE_ITASCA  0x1

#define MAX_TABLE_NAME_LEN 250

 /* Flag to check whether Multiple index consistency feature is enabled or not */
 extern int wally_multiple_index_consistency_support;

 extern const char *wally_error_strings[];
 extern const char *zpath_result_string(int);
/* Wally Debug info */
extern uint64_t wally_debug;
extern uint64_t wally_debug_catch;
#define WALLY_DEBUG_NAMES {                     \
        "result",                               \
            "table",                            \
            "registration",                     \
            "row",                              \
            "postgres",                         \
            "fohh_client",                      \
            "postgres_poll",                    \
            "postgres_fc",                      \
            "postgres_conn",                    \
            "postgres_event",                   \
            "row_detail",                       \
            "write_row",                        \
            "postgres_write",                   \
            "test_origin",                      \
            "timeout",                          \
            "table_cleanup",                    \
            "interest_cb",                      \
            "postgres_sql",                     \
            "sync_pause",                       \
            "hb_timeout",                       \
            "table_queue",                      \
            "mic",                              \
            "recovery",                         \
            NULL                                \
            }

struct table_cleanup_parameter;
/*
 *  Wally Table Cleanup states
 */
enum wally_table_cleanup_status {
    wally_table_cleanup_stop = 0,
    wally_table_cleanup_pause = 1,
    wally_table_cleanup_scanning = 2,
    wally_table_cleanup_cleaning = 3
};

/*
 * Wally app bootup states
 */
enum wally_app_state {
    wally_state_app_initilizing = 0,
    wally_state_tables_loading,
    wally_state_tables_loaded,
    wally_state_app_running
};


/*
 * Origin database status.
 */
enum wally_origin_status {
    wally_origin_status_ready = 1,
    wally_origin_status_not_ready = 2,
};

enum table_pause_status {
    wally_table_not_paused = 0,
    wally_table_paused = 1,
    wally_table_resume_in_progress = 2,
    wally_table_resume_completed = 3,
};
#define MAX_TABLE_STATE_NAME_LEN 100

struct wt {                         /* _ARGO: object_definition */
    int skip_for_global_wally;      /* _ARGO: integer */
    int writable_true_origin;       /* _ARGO: integer */
    int is_static;                  /* _ARGO: integer */
    /* db_name is the name of the database table to use. If NULL, uses the argo object type (table_name)*/
    char *db_name;                  /* _ARGO: string */
    char *table_name;               /* _ARGO: string */
    int struct_size;                /* _ARGO: integer */
    struct argo_field_description *desc;
    int struct_field_count;         /* _ARGO: integer */
};

/* WARNING: This structure mirrors an internal data structure. Be
 * careful. */
struct wally_stats {                /* _ARGO: object_definition */
    /* Registration stats */
    int64_t unregistered;            /* _ARGO: integer */
    int64_t reg_waiting;             /* _ARGO: integer */
    int64_t registered;              /* _ARGO: integer */
    int64_t reg_xmit_pend;           /* _ARGO: integer */
    int64_t dereg_xmit_pend;         /* _ARGO: integer */
    int64_t passive;                 /* _ARGO: integer */
    int64_t deleted;                 /* _ARGO: integer */

    // write queue info
    int64_t write_queue_in;          /* _ARGO: integer */
    int64_t write_queue_in_rate;     /* _ARGO: integer */
    int64_t write_queue_out;         /* _ARGO: integer */
    int64_t write_queue_out_rate;    /* _ARGO: integer */
    int64_t write_queue_depth;       /* _ARGO: integer */
    int64_t write_queue_fail;        /* _ARGO: integer */
    int64_t write_queue_delta_time_s; /* _ARGO: integer */

    // write queue batch info
    int64_t batch_size;               /* _ARGO: integer */
    int64_t total_executed_batch;     /* _ARGO: integer */
    int64_t total_success_batch;      /* _ARGO: integer */
    int64_t total_failed_batch;       /* _ARGO: integer */

    // Heartbeat timeout statistic
    uint64_t batch_hb_count;          /* _ARGO: integer */

    // recovery stats
    int64_t recovery_requests;       /* _ARGO: integer */
    int64_t recovery_duration;       /* _ARGO: integer */

};

struct wally_fohh_connection_stats {    /* _ARGO: object_definition */
    int64_t num_server_connect;         /* _ARGO: integer */
    int64_t num_server_disconnect;      /* _ARGO: integer */
    int64_t num_server_active;          /* _ARGO: integer */
    int64_t num_client_connect;         /* _ARGO: integer */
    int64_t num_client_disconnect;      /* _ARGO: integer */
    int64_t num_client_active;          /* _ARGO: integer */
};

struct wally_registrant_stats {                             /* _ARGO: object_definition */
    int64_t current_time_us;                                /* _ARGO: integer */
    int64_t total_registration_requests;                    /* _ARGO: integer */
    int64_t total_registration_response;                    /* _ARGO: integer */
    int64_t total_response_last_60s;                        /* _ARGO: integer */
    int64_t avg_response_time_last_60s_us;                  /* _ARGO: integer */
    int64_t max_response_us;                                /* _ARGO: integer */
    int64_t min_response_us;                                /* _ARGO: integer */
    int64_t response_time_p50_last_60s_us;                  /* _ARGO: integer */
    int64_t response_time_p75_last_60s_us;                  /* _ARGO: integer */
    int64_t response_time_p90_last_60s_us;                  /* _ARGO: integer */
    int64_t interest_duplicate;                             /* _ARGO: integer */
    int64_t row_notification_count;                         /* _ARGO: integer */
    int64_t total_deregister_count;                         /* _ARGO: integer */
    int64_t interest_unregistered;                          /* _ARGO: integer */
    int64_t interest_reg_waiting;                           /* _ARGO: integer */
    int64_t interest_registered;                            /* _ARGO: integer */
    int64_t interest_reg_xmit_pend;                         /* _ARGO: integer */
    int64_t interest_dereg_xmit_pend;                       /* _ARGO: integer */
    int64_t interest_passive;                               /* _ARGO: integer */
    int64_t interest_deleted;                               /* _ARGO: integer */
    int64_t interest_registered_last_10s;                   /* _ARGO: integer */
    int64_t interest_registered_last_20s;                   /* _ARGO: integer */
    int64_t interest_registered_last_30s;                   /* _ARGO: integer */
    int64_t interest_registered_last_40s;                   /* _ARGO: integer */
    int64_t interest_registered_last_50s;                   /* _ARGO: integer */
    int64_t interest_registered_last_60s;                   /* _ARGO: integer */
    int64_t total_interest_up_registration_last_60s;        /* _ARGO: integer */
    int64_t avg_interest_up_registration_last_60s_us;       /* _ARGO: integer */
    int64_t max_interest_up_registration_us;                /* _ARGO: integer */
    int64_t min_interest_up_registration_us;                /* _ARGO: integer */
    int64_t interest_up_registration_time_p50_last_60s_us;  /* _ARGO: integer */
    int64_t interest_up_registration_time_p75_last_60s_us;  /* _ARGO: integer */
    int64_t interest_up_registration_time_p90_last_60s_us;  /* _ARGO: integer */
    int64_t total_registration_failed_requests;             /* _ARGO: integer */
    int64_t row_notification_failed_count;                  /* _ARGO: integer */
};

struct wally_sync_pause_stats {     /* _ARGO: object_definition */
    /* Table pause/resume stats */
    int64_t table_pause_count;      /* _ARGO: integer */
    int64_t table_resume_count;     /* _ARGO: integer */
    int64_t table_resumed_err;      /* _ARGO: integer */
    int64_t rows_skipped_count;      /* _ARGO: integer */
    int64_t rows_resumed_count;     /* _ARGO: integer */
};

/*
 * wally db resource usage
 */
struct wally_db_stats {      /* _ARGO: object_definition */
    int64_t cur_db_size;     /* _ARGO: integer */
    int64_t max_db_size;     /* _ARGO: integer */
};

/*
 * wp db resource usage
 */
struct wp_db_stats {                        /* _ARGO: object_definition */
    int64_t db_query_count;                 /* _ARGO: integer */
    int64_t db_query_time_max_us;           /* _ARGO: integer */
    int64_t db_query_time_avg_us;           /* _ARGO: integer */
    int64_t db_update_count;                /* _ARGO: integer */
    int64_t db_update_time_max_us;          /* _ARGO: integer */
    int64_t db_update_time_avg_us;          /* _ARGO: integer */
    int64_t db_insert_count;                /* _ARGO: integer */
    int64_t db_insert_time_max_us;          /* _ARGO: integer */
    int64_t db_insert_time_avg_us;          /* _ARGO: integer */
    int64_t db_delete_count;                /* _ARGO: integer */
    int64_t db_delete_time_max_us;          /* _ARGO: integer */
    int64_t db_delete_time_avg_us;          /* _ARGO: integer */
    int64_t db_lock_count;                  /* _ARGO: integer */
    int64_t db_lock_time_max_us;            /* _ARGO: integer */
    int64_t db_lock_time_avg_us;            /* _ARGO: integer */
    int64_t db_ddl_count;                   /* _ARGO: integer */
    int64_t db_ddl_time_max_us;             /* _ARGO: integer */
    int64_t db_ddl_time_avg_us;             /* _ARGO: integer */
    int64_t db_tcl_count;                   /* _ARGO: integer */
    int64_t db_tcl_time_max_us;             /* _ARGO: integer */
    int64_t db_tcl_time_avg_us;             /* _ARGO: integer */
    int64_t poll_interval;                  /* _ARGO: integer */
    int64_t poll_rate_timeout;              /* _ARGO: integer */

};

/*
 * wally db table resource usage
 */
struct wp_db_table_stats {                  /* _ARGO: object_definition */
    int64_t db_table_query_count;           /* _ARGO: integer */
    int64_t db_table_query_time_max_us;     /* _ARGO: integer */
    int64_t db_table_query_time_avg_us;     /* _ARGO: integer */
    int64_t db_table_update_count;          /* _ARGO: integer */
    int64_t db_table_update_time_max_us;    /* _ARGO: integer */
    int64_t db_table_update_time_avg_us;    /* _ARGO: integer */
    int64_t db_table_insert_count;          /* _ARGO: integer */
    int64_t db_table_insert_time_max_us;    /* _ARGO: integer */
    int64_t db_table_insert_time_avg_us;    /* _ARGO: integer */
    int64_t db_table_delete_count;          /* _ARGO: integer */
    int64_t db_table_delete_time_max_us;    /* _ARGO: integer */
    int64_t db_table_delete_time_avg_us;    /* _ARGO: integer */
    int64_t db_table_lock_count;            /* _ARGO: integer */
    int64_t db_table_lock_time_max_us;      /* _ARGO: integer */
    int64_t db_table_lock_time_avg_us;      /* _ARGO: integer */
    int64_t db_table_ddl_count;             /* _ARGO: integer */
    int64_t db_table_ddl_time_max_us;       /* _ARGO: integer */
    int64_t db_table_ddl_time_avg_us;       /* _ARGO: integer */
    int64_t db_table_tcl_count;             /* _ARGO: integer */
    int64_t db_table_tcl_time_max_us;       /* _ARGO: integer */
    int64_t db_table_tcl_time_avg_us;       /* _ARGO: integer */
    int64_t schema_mismatch_count;          /* _ARGO: integer */
    int64_t transaction_failure_count;      /* _ARGO: integer */
    int64_t object_wrtie_failure_count;     /* _ARGO: integer */

	//lookup table info
	int64_t db_lookup_table_write_retry;                   /* _ARGO: integer */
    int64_t db_lookup_table_total_operations_successful;   /* _ARGO: integer */
    int64_t db_lookup_table_total_operations_failed;       /* _ARGO: integer */
    int64_t db_lookup_table_total_time_us;                 /* _ARGO: integer */

};

/*
 * wally db table resource usage
 */
struct wally_db_table_stats {     /* _ARGO: object_definition */
    int64_t cur_row_count;        /* _ARGO: integer */
    int64_t max_row_count;        /* _ARGO: integer */
    int64_t del_row_count;        /* _ARGO: integer */
};

struct wally_table_cleanup_stats {   /* _ARGO: object_definition */
    int64_t state;                       /* _ARGO: integer */
    int64_t max_cleanup_rows;            /* _ARGO: integer */
    int64_t max_scan_rows;               /* _ARGO: integer */
    int64_t cleanup_interval_us;         /* _ARGO: integer */
    int64_t row_expire_sec;              /* _ARGO: integer */
    int64_t rotated_rows;                /* _ARGO: integer */
    int64_t deleted_rows;                /* _ARGO: integer */
    int64_t proccesed_rows;              /* _ARGO: integer */
    int64_t scanned_rows;                /* _ARGO: integer */
    int64_t set_min_seq_times;           /* _ARGO: integer */
    int64_t max_retry_count;             /* _ARGO: integer */
    int64_t scanning_time_us;            /* _ARGO: integer */
    int64_t cleanup_time_us;             /* _ARGO: integer */
    int64_t process_time_us;             /* _ARGO: integer */
    int64_t retry_time_us;               /* _ARGO: integer */
    int64_t retry_time_poll_us;          /* _ARGO: integer */
    int64_t retry_time_busy_us;          /* _ARGO: integer */
    int64_t retry_time_waiting_us;       /* _ARGO: integer */
    int64_t retry_time_error_us;         /* _ARGO: integer */
    double process_row_per_sec;      /* _ARGO: double */
};

/*
 * wally postgres db table stats
 */
struct wally_postgres_table_bootup_stats {                  /* _ARGO: object_definition */
    int64_t bootup_table_num_queries;                       /* _ARGO: integer */
    int64_t bootup_table_batches_completed;                 /* _ARGO: integer */
    int64_t bootup_table_seqlock_time_us;                   /* _ARGO: integer */
    int64_t bootup_table_readlock_time_us;                  /* _ARGO: integer */
    int64_t bootup_table_query_time_us;                     /* _ARGO: integer */
    int64_t bootup_table_rowparse_time_us;                  /* _ARGO: integer */
    int64_t bootup_table_rowstore_time_us;                  /* _ARGO: integer */
    int64_t bootup_table_rowcb_time_us;                     /* _ARGO: integer */
    int64_t bootup_table_total_time_us;                     /* _ARGO: integer */
    int64_t bootup_gvr_txn_pending_waiting_time;            /* _ARGO: integer */
    int64_t bootup_gvr_excl_lock_waiting_time;              /* _ARGO: integer */
    int64_t bootup_gvr_table_max_seq_read_time;             /* _ARGO: integer */
    int64_t bootup_gvr_pending_no_of_txns;                  /* _ARGO: integer */
    int64_t bootup_gvr_pending_no_of_query;                 /* _ARGO: integer */
};

struct wally_global_config {
    int wally_postgres_db_query_batch_size;
    int enable_row_process_thread;
    int64_t statement_timeout;
    int64_t lock_timeout;
    int64_t idle_session_timeout;
    int64_t idle_in_trans_session_timeout;
    int enable_db_gvr_mode;
    int64_t wally_threads;
    int enable_batch_write;
    int64_t write_batch_size;
};

extern struct wally_global_config wally_gbl_cfg;
/*
 * wally postgres db table stats
 */
struct wally_postgres_table_stats {                 /* _ARGO: object_definition */
    int64_t db_new_data_waiting_time_10s;               /* _ARGO: integer */
    int64_t db_new_data_waiting_time_30s;               /* _ARGO: integer */
    int64_t db_new_data_waiting_time_60s;               /* _ARGO: integer */
    int64_t db_new_data_waiting_time_120s;              /* _ARGO: integer */
    int64_t db_new_data_waiting_time_more_than_120s;    /* _ARGO: integer */
    int64_t db_try_lock_failure_counter;                /* _ARGO: integer */
    int64_t db_new_data_poll_rate;                      /* _ARGO: integer */
    int64_t db_new_data_detect_counter;                 /* _ARGO: integer */
    int64_t db_try_lock_counter;                        /* _ARGO: integer */
    int64_t db_polling_interval;                        /* _ARGO: integer */
    int64_t db_skip_poll_excl_lock;                     /* _ARGO: integer */
    int64_t db_gvr_poll_time;                           /* _ARGO: integer */
    int64_t db_poll_avg_txn_wait_time;                  /* _ARGO: integer */
    int64_t db_poll_avg_max_wait_time;                  /* _ARGO: integer */
    int64_t time_since_last_successful_read;            /* _ARGO: integer */
    int64_t db_poll_table_parse_time;                   /* _ARGO: integer */
    int64_t db_poll_table_read_time;                    /* _ARGO: integer */
    int64_t db_poll_num_gap_txns;                       /* _ARGO: integer */
    int64_t db_poll_num_non_gap_txns;                   /* _ARGO: integer */
    int64_t db_poll_table_total_time;                   /* _ARGO: integer */
    int64_t db_poll_table_processing_time;              /* _ARGO: integer */
};

struct wally_table_stats {                        /* _ARGO: object_definition */
    int64_t interest_count;                       /* _ARGO: integer */
    int64_t interest_registered;                  /* _ARGO: integer */
    int64_t registered_waiting_for_response;      /* _ARGO: integer */
    int64_t interest_unregistered;                /* _ARGO: integer */
    int64_t interest_registration_xmit_pending;   /* _ARGO: integer */
    int64_t interest_registered_long_pending;     /* _ARGO: integer */
    int64_t interest_registered_pending_10s;      /* _ARGO: integer */
    int64_t interest_registered_pending_20s;      /* _ARGO: integer */
    int64_t interest_registered_pending_30s;      /* _ARGO: integer */
    int64_t interest_registered_pending_40s;      /* _ARGO: integer */
    int64_t interest_registered_pending_50s;      /* _ARGO: integer */
    int64_t interest_registered_pending_60s;      /* _ARGO: integer */
    int64_t interest_deregistration_xmit_pending; /* _ARGO: integer */
    int64_t interest_passive;                     /* _ARGO: integer */
    int64_t total_row_count;                      /* _ARGO: integer */
    int64_t last_update_time_s;                   /* _ARGO: integer */
    int64_t max_sequence_seen;                    /* _ARGO: integer */
    int64_t row_store_time;                       /* _ARGO: integer */
    int64_t row_callback_time;                    /* _ARGO: integer */
    int64_t row_process_time;                     /* _ARGO: integer */


    // write queue info
    int64_t write_queue_in;                       /* _ARGO: integer */
    int64_t write_queue_in_rate;                  /* _ARGO: integer */
    int64_t write_queue_out;                      /* _ARGO: integer */
    int64_t write_queue_out_rate;                 /* _ARGO: integer */
    int64_t write_queue_depth;                    /* _ARGO: integer */
    int64_t write_queue_fail;                     /* _ARGO: integer */
    int64_t last_sequence_write;                  /* _ARGO: integer */

    // write queue batch info
    int64_t batch_size;                           /* _ARGO: integer */
    int64_t total_executed_batch;                 /* _ARGO: integer */
    int64_t total_success_batch;                  /* _ARGO: integer */
    int64_t total_failed_batch;                   /* _ARGO: integer */
    int64_t query_execution_time;                 /* _ARGO: integer */
    int64_t query_creation_time;                  /* _ARGO: integer */
    int64_t mem_reallocation_count;               /* _ARGO: integer */
    int64_t batch_row_conflict;                   /* _ARGO: integer */
    int64_t object_out_of_memory;                 /* _ARGO: integer */
    int64_t sequence_unordered;                   /* _ARGO: integer */

    // recovery stats
    int64_t recovery_state;                      /* _ARGO: integer */
    int64_t recovery_start_time;                 /* _ARGO: integer */
    int64_t recovery_requests;                   /* _ARGO: integer */
    int64_t recovery_sequence;                   /* _ARGO: integer */
    int64_t recovery_received_rows;              /* _ARGO: integer */
    int64_t recovered_rows;                      /* _ARGO: integer */
    int64_t recovery_duration;                   /* _ARGO: integer */
    int64_t recovered_clients;                   /* _ARGO: integer */
    int64_t recovery_status;                     /* _ARGO: integer */
    int64_t recovery_incomplete_report;          /* _ARGO: integer */
    int64_t recovery_timeout;                    /* _ARGO: integer */

};

extern struct argo_structure_description *wally_stats_description;
extern struct argo_structure_description *generic_row_description;
extern struct argo_structure_description *wally_db_stats_description;
extern struct argo_structure_description *wally_db_table_stats_description;
extern struct argo_structure_description *wally_fohh_connection_stats_description;
extern struct argo_structure_description *wally_registrant_stats_description;
extern struct argo_structure_description *wally_sync_pause_stats_description;
extern struct argo_structure_description *wally_db_pg_table_bootup_stats_description;
extern struct argo_structure_description *wally_db_pg_table_stats_description;
extern struct argo_structure_description *wally_postgres_stats_description;
extern struct argo_structure_description *wally_table_stats_description;
extern struct argo_structure_description *wp_db_stats_description;
extern struct argo_structure_description *wp_db_table_stats_description;

/*
 * Database handle. Only really needed for initialization, adding
 * tables, etc.
 */
struct wally;

/*
 * Table handle. Used for most operations.
 */
struct wally_table;

/*
 * Row handle. Rarely used, believe it or not.
 */
struct wally_row;

/*
 * Index column handle. Used for custom indexed lookups. Applicable to
 * exactly one index of exactly one table. Indexes must be created
 * before any data is added to wally. (Think: hash table) (While these
 * indexes can technically be created afterwards, it may result in
 * rows arriving into wally before the index being created not
 * existing with respect to that index until they arrive "again". (If
 * that ever happens)
 */
struct wally_index_column;

/*
 * Origin Database handle.
 */
struct wally_origin;

/*
 * Registration handle.
 */
struct wally_registrant;

/*
 * A callback queue of wally callbacks.
 *
 * Callback queues can be maintained in order to perform aggregate
 * fetches of data across a variety of tables. The
 * wally_callback_queue routines exist or external systems to use-
 * they are not used by wally itself.
 *
 * The wally_callback_queue routines are thread-safe, but you must be
 * aware of how your system performs locking in order to avoid
 * callback deadlocks.
 */
struct wally_callback_queue;


/***************************************************
 * Large scale initialization routines.
 */


/*
 * Initialize wally state.
 *
 * This should occur after argo_log state is initialized, as it does
 * further logging initialization.
 */
void wally_init(void);

typedef int (wallyd_zpath_table_register_cb_f)(struct wally* wallyd,
                                        struct wally_table *table );

/*
 * A registration function. This function, if it exists, is called for
 * every column that is registered with wally.
 */
typedef int (wally_debug_register_f)(struct wally_index_column *column, void *cookie);


/*
 * Function to initialize wally related debug endpoints. This function, if it
 * exists, is called only once for the newly created wally.
 */
typedef void (wally_debug_endpoints_init_f)(struct wally *wally);

/*
 * Create datastructure needed for FOHH and wally layer separation
 */
int32_t wally_layer_init(void);
/*
 * Create a wally.
 *
 * Pretty simple, really. Give each wally a name, for debug
 * happiness. The debug registration function is entirely optional,
 * and is used for debugging.
 *
 * is_endpoint- if set (non-zero), it means this wally is an
 *    'endpoint' node which is allowed to perform hard-deletes rather
 *    than soft-deletes.
 */
/*
 * Callback function to be called after all tables in a wally have been resumed
 * This is used to handle special cases like profile activation after resume
 */
typedef int (wally_post_resume_callback_f)(struct wally *wally);

struct wally *wally_create(char *name,
                           int is_endpoint,
                           wally_debug_endpoints_init_f *debug_endpoints_init_cb,
                           wally_debug_register_f *debug_registration_callback,
                           void *callback_cookie,
                           wally_post_resume_callback_f *post_resume_callback);

int wally_is_endpoint(struct wally *wally);

/*
 * Get the name of the wally.
 */
char *wally_name(struct wally *wally);


/*
 * Add/set custom storage in this wally. This data is opaque to wally,
 * and is not locked on access/setting.
 */
void wally_set_custom_data(struct wally *wally, void *data);
void *wally_get_custom_data(struct wally *wally);

/*
 * Origin database xfer callouts. (Calls from client to origin DB server)
 *
 * This is a mostly text interface. (Rows arrive as ARGO objects, still)
 *
 * Some of these routines are called with WALLY locks held- they must
 * not block, and they must not recurse/call back into WALLY.
 *
 * If any of these routines would block, they should fail gracefully
 * and return WALLY_RESULT_WOULD_BLOCK.
 *
 * If any of these routines returned WALLY_RESULT_WOULD_BLOCK, then
 * once congestion has cleared, wally_xfer_resume_xmit_f must be used
 * to restart (de)registration.
 */
typedef int (wally_xfer_callout_register_for_index_f)(void *callout_cookie,
                                                      int64_t request_id,
                                                      int64_t request_sequence,
                                                      char *table_name,
                                                      char *column_name,
                                                      char *key);

typedef int (wally_xfer_callout_deregister_for_index_f)(void *callout_cookie,
                                                        char *table_name,
                                                        char *column_name,
                                                        char *key);

typedef int (wally_xfer_callout_set_cookie_f)(void *callout_cookie,
                                              void *callback_cookie);

typedef enum wally_origin_status
(wally_xfer_callout_get_status_f)
(void *callout_cookie);

/* This callout is called when we add an origin database to a table-
 * it just lets the origin DB know to expect the table...  (origin can
 * use the table name, hopefully, to prepare itself... Which it can in
 * the case of FOHH, as FOHH only needs to query argo in order to get
 * structure description for table, etc.).
 *
 * write_queue_index is updated with the index of the write queue for
 * writing from this table into the database.
 *
 * argo_object_name and db_table_name are usually the same value, but
 * in some rare instances all RPCs, data transport, etc will occur
 * with an argo object of one name, and a physical database name that
 * is different. (Everything runs off argo_object_name- db_table_name
 * is only even used if there is a real DB, and only if that DB needs
 * to have a different name than the argo_object. i.e. conflicting RPC
 * and db_table_name namespace.)
 */
typedef int (wally_xfer_callout_add_table_f)(void *callout_cookie, const char *db_table_name, const char *argo_object_name);

/* This callout is called to set the minimum valid sequence that
 * should exist in the database. It is used to assist cleaning up in
 * soft-delete cases. */
typedef int (wally_xfer_callout_set_min_sequence_f)(void *callout_cookie,
                                                    const char *table_name,
                                                    int64_t min_valid_sequence);

/* This callout is called to trim table with soft deleted rows */
typedef int (wally_xfer_callout_cleanup_f)(void *callout_cookie,
                                           const char *table_name,
                                           int64_t *sequence,
                                           int64_t *deleted,
                                           int64_t *key_int,
                                           int      sz,
                                           int64_t  min_seq_auto_update);

/*This callout is called to stop polling from wally layer */
typedef int (wally_xfer_callout_cmd_trigger_f) (void *callout_cookie,
                                            const char* table_name,
                                            int cmd_id,
                                            void *param1);
/*
 * Dump state into the string space represented by 's' and 'e', where
 * s is the start of the string buffer, and e represents the next
 * character after the buffer. The buffer will always be null
 * terminated, if there is space. This returns the number of
 * characters added to the buffer.
 */
typedef int (wally_xfer_callout_dump_state)(void *callout_cookie, char *s, char *e);


/*
 * Origin database to Wally calls:
 *
 * NOTE: ALL wally_xfer callbacks MUST be non-blocking.
 *
 * NOTE: All wally_xfer callbacks must be called with NO LOCKS HELD.
 */

int wally_update_paused_seq(void *cookie, char *table_name);

/*
 * When a row arrives, use this call into wally. The request_id must
 * be zero, or otherwise must match the request id of a registration.
 *
 * The row is REALEASED when this routine returnes. Hold onto the
 * object if you want it.
 */
int wally_xfer_row(void *cookie, char *table_name, struct argo_object *row, int64_t request_id, int64_t *store_time, int64_t *cb_time);

/*
 * When a request has been satisfied, use this callback:
 */
int wally_xfer_response(void *cookie, int64_t sequence, int64_t row_count, int64_t table_exists, char *table_name);

/*
 * Issue this callback to queue table_name for DB table to update the table->state
 */
int wally_table_process_table_exists(void *cookie, const char *table_name);

/*
 * Issue this callback when table is read and does not exists to update table->exists
 */
int wally_xfer_update_table_exists(void *cookie, const char *table_name, uint8_t table_exists, bool locked);
/*
 * If any of our calls returned a block indication (any of the
 * registration/deregistrations), then this call MUST eventually be
 * used to resume that registration/deregistration. The exception is a
 * disconnect event, which simply clears out all registration
 * state, blocking/queued or not.
 */
int wally_xfer_resume_xmit(void *cookie);

/*
 * For asynchronouse indication of major database
 * events. i.e. connect/disconnect
 */
int wally_xfer_status_update(void *cookie, enum wally_origin_status status);

/*
 * Wally API database response callbacks
 *
 * VERY IMPORTANT: both ROW and RESPONSE callbacks hold internal wally
 * locking state when called back. i.e. You should not perform any
 * operations that could result in a deadlock...
 *
 * VERY IMPORTANT: For the row callback functiion, the value of
 * previous_row is correct and predictable only in the environment
 * where there is a single registrant receiving row callbacks for a
 * single column on a table- only one column with outstanding requests
 * to origin databases. (This is the common use case- leaf nodes that
 * are consuming wally data are almost exclusively
 * single-column-registrant per table)
 *
 * Finally Important: When used with wally_callback_queue
 * functionality, the response callback is called without registrant,
 * table, or row count. (Eventually it will probably be its own
 * callback type, but not for now)
 *
 * wally_row_fixup_f is called for every row received, BEFORE that row
 * can ever possibly be read by any call into wally. This allows
 * software to pre-process the row before any lockup occurs that
 * returns that row.
 */
typedef int (wally_row_callback_f)(void *response_callback_cookie,
                                   struct wally_registrant *registrant,
                                   struct wally_table *table,
                                   struct argo_object *previous_row,
                                   struct argo_object *row,
                                   int64_t request_id);
typedef int (wally_response_callback_f)(void *response_callback_cookie,
                                        struct wally_registrant *registrant,
                                        struct wally_table *table,
                                        int64_t request_id,
                                        int row_count);
typedef void (wally_row_fixup_f)(struct argo_object *row);


/*
 * Create an origin database whence wally can get data.
 *
 * set_sequence is unique in that it is not required. (But recommended
 * for postgres slave/caching tables)
 *
 * If multiple origin databases exist, they are queried in add-order.
 *
 * is_writable is only an indication whether or not the table should
 * be considered row-writable or not.
 */
struct wally_origin *
wally_add_origin(struct wally *wally,
                 const char *name,
                 void *callout_handle,
                 wally_xfer_callout_register_for_index_f *register_index,
                 wally_xfer_callout_deregister_for_index_f *deregister_index,
                 wally_xfer_callout_set_cookie_f *set_cookie,
                 wally_xfer_callout_get_status_f *get_status,
                 wally_xfer_callout_add_table_f *add_table,
                 wally_xfer_callout_set_min_sequence_f *set_sequence,
                 wally_xfer_callout_dump_state *dump_state,
                 int is_writable);

struct wally_origin *
wally_add_origin_1(struct wally *wally,
                   const char *name,
                   void *callout_handle,
                   wally_xfer_callout_register_for_index_f *register_index,
                   wally_xfer_callout_deregister_for_index_f *deregister_index,
                   wally_xfer_callout_set_cookie_f *set_cookie,
                   wally_xfer_callout_get_status_f *get_status,
                   wally_xfer_callout_add_table_f *add_table,
                   wally_xfer_callout_set_min_sequence_f *set_sequence,
                   wally_xfer_callout_dump_state *dump_state,
                   wally_xfer_callout_cleanup_f *cleanup,
                   wally_xfer_callout_cmd_trigger_f *cmd_trigger,
                   int is_writable);


/*
 * Get the n'th origin added to this wally.
 */
struct wally_origin *
wally_get_origin(struct wally *wally,
                 int origin_index);

/*
 * Get data about all origins for a wally...
 *
 * Dumps into buf, assures NULL terminated, does not exceed buf_len
 */
int wally_dump_origins(struct wally *wally, char *buf, size_t buf_len, int quick);

int wally_dump_write_queue(struct wally *wally, int method, char *buf, size_t buf_len, int num_rows);


/***************************************************
 * Table initialization routines.
 */

/*
 * Create a table. Sorry for the huge number of arguments...
 *
 * wally - The wally for which the table is being created.
 *
 * is_row_writable - Indication whether or not this particular table
 *    should allow row writes. Takes precedence over wally
 *    configuration.
 *
 * argo_description - Description of the structure used when passing
 *    row data around.
 *
 * all_rows_callback - Callback made for all new rows.
 *
 * all_rows_callback_cookie - Callback cookie.
 *
 * use_all_origins - If set, then the table will use all the origins
 *    that wally has configured so that you do not have to attach
 *    them.
 *
 * multiple_index_consistency - If the table is going to register
 *    multiple indexes with origin databases, then this should be set
 *    in order to ensure that tables are consistently read. Setting
 *    this field results in significant reduction in caching
 *    performance, but does not affect lookup capability. Note that
 *    multiple indexes can be used for lookup WITHOUT setting this
 *    field- this field must only be set if REGISTRATIONS are used
 *    against multiple indexes.
 *
 * fully_loaded: Preps and configured the table to operate
 *    fully_loaded mode- where the entire table is loaded from origin
 *    DB into memory. All other registrations are handled locally
 *    rather than ever going to origin DB
 *
 * fixup_f: A routine (optional) that will be called on every row that
 *    arrives, and will be called before that row can every be
 *    retrieved by a lookup function. This routine is suitable for
 *    pre-processing a row on arrival. This routine MUST be
 *    synchronous/non-blocking and must not perform dangerous locks of
 *    its own.
 */
struct wally_table *
wally_table_create_named_db(struct wally *wally,
                            int is_row_writable,
                            const char *db_table_name,
                            struct argo_structure_description *argo_description,
                            wally_row_callback_f *all_rows_callback,
                            void *all_rows_callback_cookie,
                            int use_all_origins,
                            int multiple_index_consistency,
                            int fully_loaded,
                            wally_row_fixup_f *fixup_f);
struct wally_table *
wally_table_create_named_db_1(struct wally *wally,
                              int is_row_writable,
                              const char *db_table_name,
                              struct argo_structure_description *argo_description,
                              wally_row_callback_f *all_rows_callback,
                              void *all_rows_callback_cookie,
                              int use_all_origins,
                              int multiple_index_consistency,
                              int fully_loaded,
                              wally_row_fixup_f *fixup_f,
                              struct table_cleanup_parameter cleanup_param);
struct wally_table *
wally_table_create(struct wally *wally,
                   int is_row_writable,
                   struct argo_structure_description *argo_description,
                   wally_row_callback_f *all_rows_callback,
                   void *all_rows_callback_cookie,
                   int use_all_origins,
                   int multiple_index_consistency,
                   wally_row_fixup_f *fixup_f);

/*
 * Add an origin database to a wally table.
 */
int wally_table_add_origin(struct wally *wally,
                           struct wally_table *table,
                           struct wally_origin *origin);


/***************************************************
 * User table access routines.
 */

/*
 * Let wally know wheat the minimum (smallest) valid sequence is for
 * this table. Any sequences smaller than this value can be assumed to
 * be deleted.
 *
 * the _locked version of this function call is suitable for use in
 * wally callbacks, where a wally lock is already held.
 */
int wally_table_set_min_valid_sequence(struct wally_table *table, int64_t sequence);
int wally_table_set_min_valid_sequence_locked(struct wally_table *table, int64_t sequence);

/*
 * Get a handle for a table by name.
 */
struct wally_table *wally_table_get(struct wally *wally, const char *table_name);

/*
 * Create or Get an index handle for a table.
 *
 * This handle is used for local lookups.
 *
 * If column_name is NULL, then the index returned is appropriate for
 * grabbing the whole table.
 */
struct wally_index_column *wally_table_get_index(struct wally_table *table, const char *column_name);

/*
 * Get rows... Designed to get rows (fast) from an indexed column of a
 * wally table. This routine can, via configuration, control whether
 * or not it will asynchronously request a row if it is not found in
 * the local wally cache.
 *
 * The following are the mechanics of getting a row: (Not all happen
 * in this routine)
 *
 * 1. User Requests Row(s).
 *
 * 2. If wally has row, it is returned immediately. Callback arguments
 *    are ignored. Multiples are returned up to the maximum space
 *    available as indicated by arguments. If the table has a
 *    'deleted' field so marked, only non-deleted rows are
 *    returned. If wally has only deleted rows, the result will be
 *    synchronous.
 *
 * 3. If wally does not have row, it checks if it is registered for
 *    that row. If wally is registered for that row, a lookup miss is
 *    returned immediately.
 *
 * 4. Wally Registers with origin DB for that index. (If asked to do so)
 *
 * 5. Row(s) arrive (Or do not) and are stored in wally.
 *
 * 6. Request complete indication arrives. (Includes number of rows
 *    returned by call, which can be used for success indication)
 *
 * 7. If request missed, and there are more origin databases to try,
 *    repeat steps 4->5 on the next origin database.
 *
 * 8. If callback was given, callback is called, including how many
 *    rows were returned. (Note: This callback does not include the
 *    requested row(s))
 *
 * 9. User requests row again. (It should be there now, if the remote
 *    request was successful)
 *
 * 10. Win.
 *
 *
 * NOTE: Regarding asynchronous callbacks. Wally does not call this
 *   response_callback with the row result. The response_callback is
 *   simply an indication that the request has completed. A new lookup
 *   must then be performed to get the row data.
 *
 * column: the handle to the column of the table we are
 *   searching. Infers table/wally. Can not be a null column.
 *
 * key: Reference to the key for which we are searching. Key type is
 *   controlled by column. Don't pass the wrong type, please.
 *
 * row_result: If lookup returns immediately, this will return a row
 *   reference, with an incremented reference count. It is the
 *   responsibility of the calling routine to release the row.
 *
 * result_count: In: The number of spaces available for a result. Out:
 *   The number of rows actually returned.
 *
 * register_on_miss: If set, then this routine will try to register
 *   its interest in this column value with the origin database(s).
 *
 * reponse_callback: If lookup must be asynchronous, this is the
 *   callback that should be called when the response has
 *   completed. If no callback is given, no asynchonous retrieval is
 *   attempted.
 *
 * response_callback_cookie: Cookie passed to the callback response.
 *
 * Returns: Variety of errors on error condition, and
 *
 *   WALLY_RESULT_NO_ERROR : If found (regardless of origin DB
 *      status). This result GUARANTEES that there is at least one
 *      non-deleted row returned.
 *
 *   WALLY_RESULT_NOT_FOUND : If not found, and no callback given
 *                            (would have been asynchronous) or not
 *                            found synchronously (already received a
 *                            not found indication from an origin DB
 *                            in the past) or not found, and
 *                            dont_register is set.
 *
 *   WALLY_RESULT_ASYNCHRONOUS : If not found, and callback was given,
 *                               and origin DB is available.
 *
 *   WALLY_RESULT_NOT_READY : If not found, and origin database is not
 *                            available. (Regardless of whether
 *                            callbacks exist)
 *
 * **** VERY IMPORTANT ****
 *
 * If this routine (BUT NOT get_rows_fast!!!) returns
 * WALLY_RESULT_ASYNCHRONOUS, you MUST thereafter call
 * wally_table_release_index_lock() or a deadlock WILL occur! This
 * delayed unlocking exists so that the caller can prepare response
 * callback state without fear of a callback occuring while the the
 * routine is preparing for it.
 *
 * If this routine returns ANY OTHER RESULT, then unlocking is not
 * only not required, it will result in astonishingly horrible
 * failure.
 *
 * wally_table_get_rows_fast is identical in function to
 * wally_table_get_rows, except that it does not perform any locking
 * whatsoever during lookup unnless a miss occurs, does no reference
 * counting of objects returned, and returns structures rather than
 * argo objects. The structures are guaranteed to be available for 5
 * seconds, after which bad juju happens. i.e. if you are using
 * wally_table_get_rows_fast, use the results quickly, then toss them,
 * or increase the reference count on the structures returned.
 */
int wally_table_get_rows(struct wally_index_column *column,
                         const void *key,
                         const size_t key_length,
                         void **row_result,
                         size_t *result_count,
                         int register_on_miss,
                         wally_response_callback_f *response_callback,
                         void *response_callback_cookie,
                         int64_t response_callback_int);
int wally_table_get_rows_fast(struct wally_index_column *column,
                              const void *key,
                              const size_t key_length,
                              void **row_result,
                              size_t *result_count,
                              int register_on_miss,
                              wally_response_callback_f *response_callback,
                              void *response_callback_cookie,
                              int64_t response_callback_int);

void wally_table_release_column_lock(struct wally_index_column *column, const char *file, int line);

void wally_table_lock(struct wally_table *table);
void wally_table_unlock(struct wally_table *table);

int wally_table_get_rows_synchronous(struct wally_index_column *column,
                                     const void *key,
                                     const size_t key_length,
                                     void **row_result,
                                     size_t *result_count,
                                     int register_on_miss);

void wally_row_just_release(struct wally_table *table, struct wally_row *row);

/* This routine is very similar to get_rows, except that it will never
 * attempt an asynchronous operation. i.e. data is either retrieved or
 * not. This routine does no locking on internal wally state. This
 * means it is not useful for generic calls into wally- it is really
 * specially crafted for access into wally state from a row callback
 * function. (row callback functions have a wally lock held when they
 * are called) */
int wally_table_get_rows_no_locking(struct wally_index_column *column,
                                    const void *key,
                                    const size_t key_length,
                                    void **row_result,
                                    size_t *result_count);
/*
 * Get the status of the wally table's origin.
 *
 * If multiple origin databases exist, it returns the bitwise OR of
 * all their statuses.
 *
 * Returns WALLY_ORIGIN_*
 */
enum wally_origin_status wally_table_get_origin_status(struct wally_table *table, int origin_index);


/*
 * Write the specified row to the (true origin) database- i.e. to the
 * highest index origin database known to the system. It is up to the
 * configuration of that origin to either allow or reject (both
 * silently) the row update.
 *
 * The row will only write if the DB is at least row-writable.
 *
 * The row will be queued for writing if the origin DB is not available.
 *
 * If the origin DB itself fails the row write, there is no feedback
 * path to discover that was the case.
 */
int wally_table_write_origin_row(struct wally_table *table, struct argo_object *row);

/*
 * Get/release a registration handle to use with registration commands. Each
 * registration handle should be used for exactly one "consumer" of
 * the data in a table.
 *
 * Registrants must provide response and row update callbacks. The row
 * update callback is called for all rows that arrive for the
 * registrant. The response callback is called for sequence number
 * response events. (generally data complete callbacks or DB down
 * callbacks)
 *
 * Massive numbers of registrants is NOT a good idea. This system is
 * designed for massive numbers of registers from a smaller number of
 * registrants. (i.e. each remote database access this wally as an
 * origin database is a registrant)
 *
 * Releasing a registration handle can clear out tons of state. Just
 * FYI and all. (Deregisters all registrations)
 *
 * It should be noted that there is an internal registrant that is
 * used for local access to the database via wally_table_get*
 */
struct wally_registrant *wally_table_create_registrant(struct wally_table *table,
                                                       wally_row_callback_f *row_callback,
                                                       void *row_callback_cookie,
                                                       const char *debug_str);

void wally_table_destroy_registrant(struct wally_registrant *registrant);


/*
 * The following are "raw" row/table registration/deregistration
 * commands. They are intended for use by software wishing to
 * propogate wally to other systems and the like.
 *
 * It can be used by "user" programs as well, but is not really
 * necessary. The routines above use an implicit registration handle
 * and handle all registration/deregistration for the user. If
 * registrant is left NULL, then the default registrant is used.
 *
 * Note that it is possible that a single "row" matches multiple
 * registrations for a single registrant. In this case, the row may be
 * transported multiple times. This should not really be an issue as
 * the compression algorithm will smack its lips with the
 * repetetiveness, and the duplicate data will be ignored on arrival
 * due to idempotency of duplicate rows.
 *
 * registrant - Can be null - The registrant that is registering for
 *   the row.
 *
 * column - Decription of the column used to perform the registration.
 *
 * key,key_length - The key to search for. Exact match only.
 *
 * request_id - an ID- passed back during callback when this
 *   registration has been satisfied.
 *
 * request_sequence - Only rows of sequence higher than that specified
 *   will be retrieved. (i.e. this behaves somewhat like getnext).
 *
 * request_atleast_one - Forces filling local database from remote
 *   database if local database experiences a miss, EVEN if the remote
 *   database is not available. (It will never return until remote
 *   database has responded, OR there are real rows satisfying the
 *   request available locally.)
 *
 * just_callback - Indicates that we do not need to read data with
 *   this callback. All we are interested in is the callback when the
 *   request itself is complete. This is generally only ever used for
 *   true client code.
 *
 * unique_registration - Indicates that there should be at most one
 *   unique registration for this key- no need to make others.
 *
 * Returns:
 *
 * ASYNC: If a request needs to be created or is outstanding.
 *   (Callback will be called when we have successfully received a
 *   response)
 *
 * SUCCESS: If we are already registered. (And callback will never be
 *   called)
 */
int wally_table_register_for_row(struct wally_registrant *registrant,
                                 struct wally_index_column *column,
                                 const void *key,
                                 size_t key_length,
                                 int64_t request_id,
                                 int64_t request_sequence,
                                 int request_atleast_one,
                                 int just_callback,
                                 int unique_registration,
                                 wally_response_callback_f *response_callback,
                                 void *response_callback_cookie);
int wally_table_register_for_row_internal(struct wally_registrant *registrant,
                                                 struct wally_index_column *column,
                                                 const void *key,
                                                 const size_t key_length,
                                                 int64_t request_id,
                                                 int64_t request_sequence,
                                                 int request_atleast_one,
                                                 int just_callback,
                                                 int unique_registration,
                                                 wally_response_callback_f *response_callback,
                                                 void *response_callback_cookie,
                                                 struct zhash_table *request_filter_table_hash,
                                                 struct zhash_table *nullify_state,
                                                 int callback_on_calling_thread,
                                                 int pierce_fully_loaded);


/* This registration registers to origin server even if it is a
 * fully_loaded table. This can be used to actually choose what part
 * of the table is 'fully loaded'... */
int wally_table_register_for_row_through_fully_loaded(struct wally_registrant *registrant,
                                                      struct wally_index_column *column,
                                                      const void *key,
                                                      const size_t key_length,
                                                      int64_t request_id,
                                                      int64_t request_sequence,
                                                      int request_atleast_one,
                                                      int just_callback,
                                                      int unique_registration,
                                                      wally_response_callback_f *response_callback,
                                                      void *response_callback_cookie);

int wally_table_register_for_row_filtered(struct wally_registrant *registrant,
                                          struct wally_index_column *column,
                                          const void *key,
                                          size_t key_length,
                                          int64_t request_id,
                                          int64_t request_sequence,
                                          int request_atleast_one,
                                          int just_callback,
                                          int unique_registration,
                                          wally_response_callback_f *response_callback,
                                          void *response_callback_cookie,
                                          struct zhash_table *request_filter_table_hash,
                                          struct zhash_table *nullify_state);

int wally_table_deregister_for_row(struct wally_registrant *registrant,
                                   struct wally_index_column *column,
                                   const void *key,
                                   size_t key_length);
int wally_table_register_for_row_recovery(struct wally_registrant *registrant,
                                                      struct wally_index_column *column,
                                                      const void *key,
                                                      const size_t key_length,
                                                      int64_t request_id,
                                                      int64_t request_sequence,
                                                      int request_atleast_one,
                                                      int just_callback,
                                                      int unique_registration,
                                                      wally_response_callback_f *response_callback,
                                                      void *response_callback_cookie);

int wally_table_deregister_for_row_internal(struct wally_registrant *registrant,
                                                   struct wally_index_column *column,
                                                   const void *key,
                                                   const size_t key_length);

/*
 * Checks if the specified registrant is registered for row.
 * Returns NO_ERROR if the registration is in place.
 * Returns NOT_FOUND if the registration is not in place.
 * Returns other errors for weird stuff happening.
 */
int wally_table_is_registered_for_row(struct wally_registrant *registrant,
                                      struct wally_index_column *column,
                                      const void *key,
                                      size_t key_length);


int wally_table_resume_callbacks(struct wally_registrant *registrant);


struct generic_row {    /* _ARGO: object_definition */
    int64_t index;      /* _ARGO: integer, key */
    int64_t sequence;   /* _ARGO: integer, sequence */
};


/* Create a callback queue. */
struct wally_callback_queue *wally_callback_queue_create(void);

/* Destroy a callback queue. Any callbacks sitting on this queue are
 * released without being called back */
int wally_callback_queue_destroy(struct wally_callback_queue *queue);

/* Return the number of elements in a callback queue */
int wally_callback_queue_depth(struct wally_callback_queue *queue);

/* Add a callback to a callback queue */
int wally_callback_queue_add(struct wally_callback_queue *queue,
                             wally_response_callback_f *callback,
                             void *void_cookie,
                             int64_t int_cookie);

/* Call (and remove) all callbacks in a callback queue. If any of the
 * callbacks return an error, that error is propagated into the return
 * value of this routine. */
int wally_callback_queue_callback(struct wally_callback_queue *queue);


/*
 * Interlock used for performing synchronous access to DB.
 *
 * A condition variable is needed to correctly make use of pthreads.
 */
struct wally_interlock {
    pthread_mutex_t lock;
    pthread_cond_t cond;
};

extern void wally_interlock_init(struct wally_interlock *i);
extern void wally_interlock_release(struct wally_interlock *i);
extern void wally_interlock_lock_1(struct wally_interlock *i);
extern void wally_interlock_lock_2(struct wally_interlock *i);

/* Return version string for wally. */
const char *wally_version(void);

/* Return the name of a wally table. */
const char *wally_table_name(struct wally_table *table);

int64_t wally_table_get_remote_update_time(struct wally_table *table);
/* Return the max sequence number seen till now */
int64_t wally_table_max_sequence_seen(struct wally_table *table);
/* Return the paused sequence number for the table */
int64_t wally_table_paused_sequence(struct wally_table *table);
/* Return the largest sequence number paused until now */
int64_t wally_table_max_sequence_paused(struct wally_table *table);
/* Return current state of pause/resume */
enum table_pause_status wally_table_pause_status(struct wally_table *table);

void wally_table_set_is_pausable(struct wally_table *table, int value);

/* Return the name of the wally to which a wally table belongs. */
const char *wally_table_wally_name(struct wally_table *table);

/*
 * Fill in the stats structure provided with the stats from the
 * specified wally.
 */
void wally_fill_stats(struct wally *wally, struct wally_stats *wally_stats);

void wally_fohh_server_connect_stat(struct wally *wally);
void wally_fohh_server_disconnect_stat(struct wally *wally);
void wally_fohh_client_connect_stat(struct wally *wally);
void wally_fohh_client_disconnect_stat(struct wally *wally);

/*
 * Fill the wally_fohh_connection_stats stucture provided with connection
 * stats from specified wally
 */
void wally_fill_fohh_connection_stats(struct wally *wally, struct wally_fohh_connection_stats *wally_fohh_connection_stats);

/* wally registrant(client) stats */
void wally_fill_registrant_stats(struct wally *wally, struct wally_registrant_stats *wally_registrant_stats);

/* Interest state stats filled at table level */
void wally_fill_interest_state_stats(struct wally_table *table, struct wally_stats *wally_stats);

/* wally sync pause stats per table */
void wally_fill_table_level_sync_pause_stats(struct wally_table *table,
                                             struct wally_sync_pause_stats *stats);
/* wally sync pause stats per wally */
void wally_fill_wally_level_sync_pause_stats(struct wally *wally,
                                             struct wally_sync_pause_stats *stats);

/* get cleanup process rate: rows/sec */
double get_process_rate(struct wally_table *t);

/* Start the trimmer, which are stopped due to RDS failure */
void wally_start_all_cleanup(struct wally *wally);
/* RDS failure is detected, stop the trimmer */
bool wally_stop_all_cleanup(struct wally *wally);

struct wally_index_column *get_null_column(struct wally_table *t);
/* fetch all rows synchronously */
void wally_table_read_synchronized(struct wally_index_column *column, void *key, size_t key_len);

/* pause/resume remote syncing of data */
int wally_table_pause(struct wally *wally, const char *table_name);
int wally_table_resume(struct wally *wally, const char *table_name);
int wally_all_tables_resume_completed(struct wally *wally);
void wally_check_post_resume_callback(struct wally *wally);
const char * wally_table_get_pause_status_str(enum table_pause_status status);

/*
 * is_row_filtered will return NOT_FILTERED if no match is found.
 * FILTERED implies a filter exists that does not allow this row to be transmitted.
 * The function takes the row, table and filter and compares the key
 * with value in the row.
 */
enum filter_result
{
    FILTERED,
    NOT_FILTERED,
};

enum filter_result is_row_filtered(struct zhash_table *filter_table_hash,
                                   struct wally_table *table,
                                   struct argo_object *row);

/* wally app state api */
void set_wally_app_state(enum wally_app_state state);
void set_wally_client_incompatible_version_check_disable_cli();
enum wally_app_state get_wally_app_state();
void set_multiple_index_consistency_support();
int do_wts_internal(struct wt *wt ,
                    struct wally_origin *wallyd_slave_db,
                    struct wally_origin *wallyd_remote_db,
                    struct wally *wallyd,
                    int fully_loaded,
                    struct table_cleanup_parameter cleanup_param ,
                    int load_tables,
                    wally_row_fixup_f *fixup,
                    int is_gwally,
                    wallyd_zpath_table_register_cb_f *ztblr);

struct argo_structure_description* get_wt_desc(void);
int has_null_column(struct wally_table *t);
void wally_enable_batch_write();


/*
 * Wally Debugging.
 *
 * Wally provides the following interfaces when dealing with tables:
 *
 * /wally/<wally_name>/<table_name>[/<column>]/interest - Provides the index and
 *   basic status for the specified interest. Options: value=<column
 *   value> - The value to check for. If not set, then all interests
 *   (to some limit) are returned. Note that column value is text or
 *   64 bit integer, depending on the table. Note that if column is omitted, then
 */

/********************************************************************************
 * Error or response codes
 *
 * - CLIENT_VERSION_TOO_OLD                                   Client version is too old
 * - CLIENT_VERSION_HIGHER_THAN_SERVER                        Client version is higher than Server
 */

#define CLIENT_VERSION_TOO_OLD                                "CLIENT_VERSION_TOO_OLD"
#define CLIENT_VERSION_HIGHER_THAN_SERVER                     "CLIENT_VERSION_HIGHER_THAN_SERVER"

extern int timer_interval_us; // Configured timer_interval_us, default: WP_POLL_INTERVAL_US 5ms
extern int64_t cleanup_delay_at_start_us; // Configured cleanup delay at start, default: 10 min
int64_t wally_get_total_max_seq_with_hash(struct wally *wally, uint64_t *hash) __attribute__((weak));
int64_t wally_get_last_remote_sync_time(struct wally *wally, struct argo_hash_table *skip_table);
void wally_ut_set_last_remote_sync_time(struct wally_table *table, int64_t time);

/* Function: wally_table_recovery_cmd_handler
 * Arg     : wally             - Wally object for which recovery command is executed
 *         : table             - Name of the table to be recovered
 *         : recovery_sequence - sequence from which rows to be recovered
 *         : sync_missing_rows - 1 if only missing rows to be recovered, otherwise 0.
 *         : recovery_timeout  - recovery timeout in seconds
 * Ret     : ERR returned incase of app state validation, otherwise success.
 * Desc    : This function validates wally internal state before starting recovery process.
 *           Internal state is updated, begin message sent to clients,
 *           registered to read the rows and DB stopped polling
 */
int wally_table_recovery_cmd_handler(char *buf, size_t size, struct wally *wally, const char *table_name, int64_t max_sequence,
                    int sync_missing_rows, int64_t recovery_timeout);
/* Function: wally_table_client_recovery_end_request_handler
 * Arg     : table_name        - Name of the table to be recovered
 *         : server_sent_rows  - number of rows sent by server
 * Ret     : ERR returned incase of app state validation, otherwise success.
 * Desc    : This function forwards the end requests to clients, otherwise sends response
 *              back to server.
 */
int wally_table_client_recovery_end_request_handler(void *cookie, char *table_name, int64_t server_sent_rows);
/* Function: wally_table_server_handle_complete_response
 * Arg     : table_name        - Name of the table to be recovered
 *         : server_sent_rows  - number of rows sent by server
 * Ret     : ERR returned incase of app state validation, otherwise success.
 * Desc    : This function forwards the end requests to clients, otherwise sends response
 *              back to server.
 */
int wally_table_server_handle_complete_response(void *cookie, char *table_name, int64_t received_row_count);
int wally_table_client_recovery_begin_request_handler(void *cookie, char *table_name, int64_t recovery_sequence,
                        int64_t recovery_timeout, int sync_missing_rows);
/* Function: wally_table_client_recovery_begin_request_handler
 * Arg     : wally             - Wally object for which recovery command is executed
 *         : table             - Name of the table to be recovered
 *         : recovery_sequence - sequence from which rows to be recovered
 *         : sync_missing_rows - 1 if only missing rows to be recovered, otherwise 0.
 *         : recovery_timeout  - recovery timeout in seconds
 * Ret     : ERR returned incase of app state validation, otherwise success.
 * Desc    : This function validates wally internal state before starting recovery process.
 *           Internal state is updated, begin message forwarded to clients if present,
 *           start waiting for rows from remote wally
 */
int wally_send_recovery_begin_requests(struct wally_table *table, char *table_name, int64_t recovery_sequence, int64_t recovery_timeout, int sync_missing_rows );
/* Function : wally_send_recovered_rows_to_all_clients
 * Arg      : table - wally table
 *          : row - wally row object
 * Ret      : WALLY_RESULT_NO_ERROR if no error.
 * Desc     : This function iterates all the clients and sends row objects to them.
 */
int wally_send_recovered_rows_to_all_clients(struct wally_table *table, struct wally_row *row);
/* Function : wally_send_recovery_end_requests
 * Arg      : table - wally table
 * Ret      : Number of requests sent to client
 * Desc     : This function iterates all the clients and sends end requests and
 *                starts end request timer.
 */
int wally_send_recovery_end_requests(struct wally_table *table, char *table_name, int64_t received_rows);
/* Function : wally_reset_recovery_mode
 * Arg      : table - wally_table
 *            mode = RESET_STATE, RESET_STATS, RESET_ALL
 * Ret      : None
 * Desc     : This function resets recovery parameters based on mode
 */
void wally_reset_recovery_mode(struct wally_table *table, uint8_t mode);
/* Function : wally_recovery_state_string
 * Arg      : state - state of recovery
 * Ret      : String representation of state
 * Desc     : This function converts enum to string representation
 *              of recovery state.
 */
/* Function : wally_retrieve_unsupported_clients
 * Arg      : wally - wally object
 *            buffer - buffer to be filled
 *            bufsize - total buffer size
 * ret      : WALLY_RESULT
 * Desc     : This function iterates the fohh servers and retrieve clients to list
 *              unsupported clients.
 */
int wally_retrieve_unsupported_clients(struct wally *wally, char *buffer, size_t bufsize);
/* Function : wally_recovery_handle_stop_cmd_handler
 * Arg      : wally - wally object
 *            table_name - table name
 * Ret      : WALLY_RESULT_NO_ERROR if success
 * Desc     : This function tries to stop the recovery in the middle based on current
 *              state.
 */
int wally_recovery_handle_stop_cmd_handler(struct wally *wally,const char *table_name);
/* Function : wally_recovery_state_string
 * Arg      : state - state of recovery
 * Ret      : String representation of state
 * Desc     : This function converts enum to string representation
 *              of recovery state.
 */
const char *wally_recovery_state_string(int state);
/* Function : wally_result_string
 * Arg      : result - wally result enum value
 * Ret      : string representation of error
 * Desc     : This function converts enum into string value and
 *              returns pointer to the string.
 */
const char *wally_result_string(int result);
void wally_client_incompatible_version_check_config_override_cb(const int64_t *config_value, int64_t impacted_gid);
#endif /* __WALLY_H__ */
