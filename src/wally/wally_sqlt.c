/*
 * wally_sqlt.c. Copyright (C) 2020 Zscaler, Inc. All Rights Reserved.
 *
 * SQLite origin DB interface.
 *
 */

//#include <sys/queue.h>
//#include <math.h>
#include <event2/event.h>
#include <unistd.h>
#include <sqlite3.h>
#include "zpath_misc/zpath_misc.h"
#include "zthread/zthread.h"
#include "zevent/zevent.h"
#include "wally/wally.h"
#include "wally/wally_private.h"
#include "wally/wally_sqlt.h"
#include "wally/wally_db.h"
#include "wally/wally_oper.h"

#define MAX_SINGLE_SQL_LENGTH 1024
#define SQLT_REQUEST_THREAD "sqlt_request_thread"

extern int et_26295_test_size;

int is_current_source_code(){
    return !strcmp(SQLITE_SOURCE_ID, SQLT_SOURCE_ID);
}

void do_wally_xfer_resume_xmit(struct zevent_base *base, void *cookie1, int64_t int_cookie)
{
    wally_xfer_resume_xmit(cookie1);
}

/*
 * Returns a single field value of ith row  and jth col of a pazResult.
 * Note pazResult size is (row+1 X col), the 0th row id name of columns, data starting from 1st row.
 * if dup is 0, The caller should not free the result directly. It will be freed using sqlite3_free_table(pazResult).
 */
char *sqlt_get_value(char **pazResult, int row, int col, int i, int j, int dup)
{
    if (i > row || j >= col) return NULL;
    char *p = *(pazResult+i*col+j);
    return dup ? strdup(p): p;
}

int set_column_affinity(struct argo_field_description *fd, char affinity[], size_t sz)
{
    memset(affinity, 0, sz);
    if (fd->is_array) {
        switch (fd->argo_field_type) {
            case argo_field_data_type_integer:
                strncpy(affinity, "BLOB_AR_N", sz-1);
                break;
            case argo_field_data_type_string:
                strncpy(affinity, "BLOB_AR_S", sz-1);
                break;
            case argo_field_data_type_binary:
                if (fd->is_double) {
                    strncpy(affinity, "BLOB_AR_DBL", sz-1);
                } else if (fd->is_inet) {
                    strncpy(affinity, "BLOB_AR_INET", sz-1);
                } else {
                    return -1;
                }
                break;
            default:
                return -1;
        } /*end of switch */
    } else {
        switch (fd->argo_field_type) {
            case argo_field_data_type_integer:
                strncpy(affinity, "INTEGER", sz-1);
                break;
            case argo_field_data_type_string:
                strncpy(affinity, "TEXT", sz-1);
                break;
            case argo_field_data_type_binary:
                if (fd->is_double) {
                    strncpy(affinity, "DOUBLE", sz-1);
                } else if (fd->is_inet) {
                    strncpy(affinity, "BLOB_INET", sz-1);
                } else {
                    return -1;
                }
                break;
            default:
                return -1;
        } /* end of awitch */
    }

    return 0;
}

 /* util functions -- end */

/*
 * Figure out maximum number of array elements, keeping in
 * mind the plethora of supported configurations
 */
int sqlt_get_data_count(struct argo_field_description *fd, const char *buf, size_t sz)
{
    if (!buf || sz <= 0) return 0;
    if (!fd->is_array){
        return 1;
    } else {
        if (fd->is_double) {
            return sz/sizeof(double);
        } else if (fd->is_inet) {
            return sz/sizeof(struct argo_inet);
        } else if (fd->argo_field_type == argo_field_data_type_integer) {
            return sz/sizeof(int64_t);
        } else if (fd->argo_field_type == argo_field_data_type_string) {
            const char *p = buf;
            int ct = 0;
            while (p < buf + sz) {
                if (*p == '\0') ct++;
                p++;
            }
            return ct;
        } else {
            /* invalid */
            return 0;
        }
    }
}

/*
 * max_array_index will be between 1 and
 * ARGO2_MAX_ARRAY_SIZE upon completion of this little
 * section, and will account for how much space is
 * available in the structure definition
 */
int sqlt_get_max_index(struct argo_field_description *fd, const char *buf, size_t sz)
{
    int max_array_index;
    int db_element_count = sqlt_get_data_count(fd, buf, sz);
    if (fd->hard_count) {
        /* Modern code: We will have a hard count if we ever have a hard count */
        max_array_index = fd->hard_count;
        if (db_element_count < max_array_index) max_array_index = db_element_count;
    } else {
        /* Older code: can be mostly deprecated later, and
         * just be set to argo2_max_array_size */
        if (fd->have_dynamic_count_index) {
            max_array_index = db_element_count;
        } else {
            if (fd->is_reference || (fd->argo_field_type != argo_field_data_type_string)) {
                max_array_index = fd->count;
            } else {
                max_array_index = 1;
            }
        }
    }
    if (max_array_index > ARGO2_MAX_ARRAY_SIZE) {
        max_array_index = ARGO2_MAX_ARRAY_SIZE;
    }
    if (max_array_index == 0) max_array_index = 1;

    return max_array_index;
}

/* return next idx */
int sqlt_get_next_data(struct argo_field_description *fd, int idx, char *cur, char **next, const char *buf, size_t sz)
{
    if (!buf || sz <= 0){
        *next = NULL;
        return -1;
    }

    /* first data */
    if (cur == NULL) {
        *next = (char *)buf;
        return 0;
    }

    const char *end = buf + sz;
    if (fd->is_double) {
        *next = cur + sizeof(double);
    } else if (fd->is_inet) {
        *next = cur + sizeof(struct argo_inet);
    } else if (fd->argo_field_type == argo_field_data_type_integer) {
        *next = cur + sizeof(int64_t);
    } else if (fd->argo_field_type == argo_field_data_type_string) {
        while (*cur != '\0' && cur < end) cur++;
        *next = ++cur;
    } else {
        /* invalid */
        *next = NULL;
        return -1;
    }

    if (*next < end) {
        return ++idx;
    } else {
        *next = NULL;
        return -1;
    }
}

int sqlt_process_columns(struct wp_table *t, struct column_meta *columns, int count)
{

    int i;
    int result;

    /* For all the columns that come back, we need to see if we
     * need to add any to argo. And at the same time, we need to
     * remember which argo fields are not represented by
     * columns. The first is easy. The second a little trickier-
     * we need a scratchpad to note down which fields have been
     * seen. */
    argo_read_lock();
    t->argo_field_count = t->description->description_count;
    argo_unlock();
    if (!t->scratch) {
        t->scratch = WALLY_MALLOC(argo_library_get_max_descriptions());
        if (!t->scratch) return -1;
    }
    memset(t->scratch, 0, argo_library_get_max_descriptions());
    /* Iterate columns, checking them against argo. Note we still
     * have an argo lock here. */
    for (i = 0; i < count; i++) {
        struct argo_private_field_description *fd;
        char *column_name =  columns[i].name;
        char *data_type = columns[i].data_type;
        enum argo_field_data_type argo_data_type;
        int is_array = 0;
        int is_double = 0;
        int is_inet = 0;

        if (strcasecmp(data_type, "INTEGER") == 0) {
            argo_data_type = argo_field_data_type_integer;
        } else if (strcasecmp(data_type, "TEXT") == 0) {
            argo_data_type = argo_field_data_type_string;
        } else if (strcasecmp(data_type, "BLOB_INET") == 0) {
            argo_data_type = argo_field_data_type_binary;
            is_inet = 1;
        } else if (strcasecmp(data_type, "DOUBLE") == 0) {
            argo_data_type = argo_field_data_type_binary;
            is_double = 1;
        } else if (strcasecmp(data_type, "BLOB_AR_N") == 0) {
            argo_data_type = argo_field_data_type_integer;
            is_array = 1;
        } else if (strcasecmp(data_type, "BLOB_AR_S") == 0) {
            argo_data_type = argo_field_data_type_string;
            is_array = 1;
        } else if (strcasecmp(data_type, "BLOB_AR_INET") == 0) {
            argo_data_type = argo_field_data_type_binary;
            is_inet = 1;
            is_array = 1;
        } else if (strcasecmp(data_type, "BLOB_AR_DBL") == 0) {
            argo_data_type = argo_field_data_type_binary;
            is_double = 1;
            is_array = 1;
        }  else {
            /* Unimplemented automatic recognition type. */
            argo_data_type = argo_field_data_type_invalid;
        }

        argo_read_lock();
        fd = zhash_table_lookup(t->description->described_fields, column_name, strlen(column_name), NULL);
        argo_unlock();
        if (fd) {
            /* Mark that we have seen this field- use our scratch for that. */
            t->scratch[fd->index] = 1;

            /* Verify we don't have a type mismatch. If
             * we have a type mismatch, we abort, because there is
             * not really anything we can do about it. */
            if ((fd->public_description.argo_field_type == argo_field_data_type_integer) &&
                (argo_data_type == argo_field_data_type_binary) &&
                (is_double)) {
                /* Do nothing- we can read floats as ints */
            } else if ((fd->public_description.argo_field_type == argo_field_data_type_binary) &&
                       fd->public_description.is_double &&
                       (argo_data_type == argo_field_data_type_integer)) {
                /* Do nothing- we can read ints as floats */
            } else {
                if (fd->public_description.argo_field_type != argo_data_type) {
                    WALLY_LOG(AL_ERROR, "Failure: %d(%s) != %d(%s), %s",
                              fd->public_description.argo_field_type,
                              argo_field_data_type_to_str(fd->public_description.argo_field_type),
                              argo_data_type,
                              argo_field_data_type_to_str(argo_data_type),
                              column_name);
                    return -1;
                }
            }
        } else {
            /* We need to add this column to argo. */
            if (argo_data_type != argo_field_data_type_invalid) {
                WALLY_DEBUG_POSTGRES("Attempting to add field %s to table %s in db %s, type %d (%s), is_array=%d, is_double=%d, is_inet=%d",
                                     column_name, t->db_table_name, t->db->name, argo_data_type, argo_field_data_type_to_str(argo_data_type),
                                     is_array, is_double, is_inet);
                result = argo_global_structure_add_field(t->description,
                                                         column_name,
                                                         strlen(column_name),
                                                         argo_data_type,
                                                         is_array,
                                                         is_double,
                                                         is_inet);
                if (result) {
                    WALLY_LOG(AL_ERROR, "Failure");
                    return -1;
                }
            } else {
                WALLY_LOG(AL_ERROR, "Invalid data type attempting to add field %s to table %s", column_name, t->db_table_name);
                /* XXX LOG */
                /* Ignore unrecognized types for now- they simply
                 * don't get transported. i.e. if we need to move
                 * an array, it has to exist in the row structure,
                 * for now. */
            }
        }
    } /* end of column loop */
    return 0;
}

/*
 * Construct sql request string for "ALTER TABLE ADD/DROP COLUMN".
 * Note, SQLite cannot add/drop multiple columns in a single request,
 * so we need to execute a transaction with multiple "ALTER TABLE" requests.
 * This function returns the number of "ALTER TABLE" requests.
 */
int sqlt_add_column_str(struct wp_table *t, char **sql_cmd, size_t max_cmd_sz)
{
    int i = 0;
    int rc;
    int alter_count = 0;
    char *s = *sql_cmd;
    char *e = *sql_cmd + max_cmd_sz;
    char affinity[64];

    if (!t->db->is_alterable) return 0;

    /*s += sxprintf(s, e, "BEGIN TRANSACTION;");*/

    /* Now that we have read the columns, we need to add any
     * columns argo knows about that we didn't read from the
     * database- if it's allowed for this table. Note that this
     * purposely skips over any columns that were added to
     * argo. */
    /* XXX Implement permissions. */

    argo_read_lock();
    for (i = 0; i < t->argo_field_count; i++) {
        struct argo_field_description *fd;
        fd = &(t->description->description[i]->public_description);
        if (t->scratch[i]) {
            if (fd->dont_db) {
                s += sxprintf(s, e, "ALTER TABLE %s DROP COLUMN %s;",
                              t->db_table_name,
                              fd->field_name);

            }
        } else {
            if (!fd->write_value) {
                continue;
            }
            if (fd->dont_db) {
                continue;
            }
            rc = set_column_affinity(fd, affinity, sizeof(affinity));
            if (rc) {
                /* XXX LOG */
                /* To do: Implement binary, in particular */
                argo_unlock();
                WALLY_LOG(AL_ERROR, "Failure");
                return 0;
            }
            s += sxprintf(s, e, "ALTER TABLE %s ADD COLUMN %s %s;",
                          t->db_table_name,
                          fd->field_name,
                          affinity);
            alter_count++;
        }

    } /* end of for loop */

    argo_unlock();

    /*s += sxprintf(s, e, "COMMIT;");*/
    return alter_count;
}

/*
 * process index result from sqlt_read_indexes().
 * The first row in pazResult is title "name", rests are actual index names.
 * return count of index to be created. return -1 for error
 */
int sqlt_process_index(struct wp_table *t, char **pazResult, size_t row, char **sql_cmd, size_t max_cmd_sz)
{

    struct argo_field_description *sequence_field = NULL;
    struct argo_field_description *fd;
    size_t di;
    size_t ti;
    char *s = *sql_cmd;
    char *e = *sql_cmd + max_cmd_sz;
    int count = 0;

    /* We only enforce indexes in one direction- If argo thinks a
         * field must be indexed, then indexed it must be.*/

    /* Make a little scratchpad for finding which indexes are missing. */
    argo_read_lock();

    /* find sequence field. */
    for (di = 0; di < t->description->description_count; di++) {
        fd = &(t->description->description[di]->public_description);
        if (fd->is_index) {
            /* Idempotent call: */
            if(wp_add_index_column(t, fd->field_name) != 0) {
                argo_unlock();
                WALLY_LOG(AL_ERROR, "WP: Error: Could not add index tracking column %s to table %s",
                          fd->field_name, t->db_table_name);
                return -1;
            }
        }
        if (fd->is_sequence) {
            sequence_field = fd;
        }
    }

    if (!sequence_field) {
        argo_unlock();
        WALLY_LOG(AL_ERROR, "sequence_field not found");
        return -1;
    }

    /*s += sxprintf(s, e, "BEGIN TRANSACTION;");*/


    /*
     * Search for a missing index... */
    /* For single index, the index name format is: <table_name>$<column_name>
     * For double_index, the index name format is : <table_name>$<column_name>$<sequence_field_name>
     * Assuming we do not have any table or column name contains: "$".
     */
    for (di = 0; di < t->description->description_count; di++) {

        struct argo_field_description *fd = &(t->description->description[di]->public_description);
        char idx_name[MAX_COLUMN_META_SIZE];
        char fields[MAX_COLUMN_META_SIZE];

        if (!fd->write_value || fd->dont_db ) continue;

        if ((fd->is_sequence) || (fd->is_key) || ((fd->is_index) && (fd->is_unique))) {

            /* Check for single-index on this field. */
            sxprintf(idx_name, idx_name+sizeof(idx_name), "%s$%s", t->db_table_name, fd->field_name);
            sxprintf(fields, fields+sizeof(fields), "(%s)", fd->field_name);

        } else if (t->description->description[di]->public_description.is_index) {
            /* Check for double-index on this field. */
            sxprintf(idx_name, idx_name+sizeof(idx_name),
                     "%s$%s$%s",
                     t->db_table_name,
                     fd->field_name,
                     sequence_field->field_name);
            sxprintf(fields, fields+sizeof(fields), "(%s,%s)", fd->field_name, sequence_field->field_name);
        } else {
            continue;
        }

        /* if the idx_name not in pazResult, we need to create the index on table */
        /* ignore the first row in pazResult, it is just the title "name", not real indices */
        for (ti = 1; ti < row+1; ti++) {
            if (strcasecmp(idx_name, pazResult[ti]) == 0) break;
        }

        if (ti == row+1) {
            /* did not find the index, must add it. */
            if (fd->is_key) {
                s += sxprintf(s, e, "CREATE UNIQUE INDEX %s ON %s%s;", idx_name, t->db_table_name, fields);
            } else {
                s += sxprintf(s, e, "CREATE INDEX %s ON %s%s;", idx_name, t->db_table_name, fields);
            }
            count++;
        }
    }

    /* s += sxprintf(s, e, "COMMIT;"); */
    argo_unlock();

    return count;
}

/*
 * Given a result, parse columns and determine with which argo
 * description indexes they match.  If there is no match, set to -1.
 */
/*
 * Given a result, parse columns and determine with which argo
 * description indexes they match.  If there is no match, set to -1.
 */
int *sqlt_parse_columns(struct wp_table *t, sqlite3_stmt *stmt, int col_count, int *sequence_argo_id, struct wp_connection *wp_conn)
{
    struct argo_private_field_description *fd;
    int *retval = NULL;
    int i;

    argo_read_lock();

    retval = (int *) WALLY_MALLOC (sizeof(int) * col_count);
    if (retval) {
        for (i = 0; i < col_count; i++) {
            const char *field_name = sqlite3_column_name(stmt, i);
            fd = zhash_table_lookup(t->description->described_fields, field_name, strlen(field_name), NULL);
            if (fd) {
                retval[i] = fd->index;
                if (fd->public_description.is_sequence) *sequence_argo_id = fd->index;
            } else {
                /* Field not found- no place to put it in an argo
                 * object! We'll mark the table for
                 * resynchronization. We cannot simply expand argo
                 * here, because we don't really know the type of
                 * the object being returned- we need to read more
                 * data out of the table for that, which is an
                 * asynchronous process. */
                retval[i] = -1;
                if (!t->needs_to_be_resynchronized) {
                    t->needs_to_be_resynchronized = 1;
                }
                WALLY_FREE(retval);
                retval = NULL;
                /* XXX Fix properly at some point. A restart
                 * works, but is not preferred */
                WALLY_LOG(AL_CRITICAL, "WP: could not find field for %s; resynchonization fails so deadlocking to force restart", field_name);
                wally_error_handler(NULL, wp_conn, true, E_SCHEMA_MISMATCH, SE_NONE);
            }
        }
    }

    argo_unlock();
    return retval;
}

struct argo_object *sqlt_parse_row(struct wp_table *t, int *xlate, sqlite3_stmt *stmt, int col_count)
{
    int desc_count = t->description->description_count - t->description->static_description_count;
    /* YAY, 1MB STACK, GO GO GO */
    int def_sz = 1024*1024;
    int min_bsz = 1 + sizeof(struct argo_object) + t->description->structure_size + sizeof(struct argo_excess_field)*desc_count;
    if (et_26295_test_size > 0) {
        def_sz = db_min(def_sz, et_26295_test_size);
        WALLY_LOG(AL_WARNING, "ET_26295_TEST default_sz = %d", def_sz);
    }
    int bsz = db_max(def_sz, min_bsz);
    uint8_t buf[bsz];

    uint8_t *heap = NULL;
    uint8_t *heap_end = NULL;
    uint8_t *heap_start = NULL;
    int j;
    struct argo_object *dest_object;
    int res;
    int err = 0;
    size_t alloc_size = 0;

    argo_read_lock();

 retry:
    err = 0;
    if (heap) {
        /* Stack heap must have been too small.. */
        /* create heap using... heap. */
        /* Keep trying up to 16 MB */
        if (alloc_size == 0) {
            alloc_size = 8 * 1024 * 1024;
        } else if (alloc_size == (8 * 1024 * 1024)) {
            WALLY_FREE(heap_start);
            alloc_size = 16 * 1024 * 1024;
        } else {
            WALLY_FREE(heap_start);
            return NULL;
        }
        WALLY_LOG(AL_WARNING, "Data is too large, heap is reallocated with size %d", (int)alloc_size);
        heap = WALLY_MALLOC(alloc_size);
        heap_end = heap + alloc_size;
    } else {
        heap = &(buf[0]);
        heap_end = heap + sizeof(buf);
    }
    heap_start = heap;

    /* Set up object */
    /*
     * Must make sure the min_bsz is calculated correctly as needed in this block,
     * if the code in this block is updated, we may need to adjust min_bsz at begining of the function.
     */
    dest_object = (struct argo_object *) heap;
    heap += sizeof(struct argo_object);
    dest_object->base_structure_index = t->description->global_index;
    dest_object->excess_description_count = desc_count;
    dest_object->reference_count = 0;
    dest_object->base_structure_data = (void *) heap;
    heap += t->description->structure_size;
    dest_object->excess_fields = (void *) heap;
    heap += sizeof(struct argo_excess_field) * dest_object->excess_description_count;
    memset(dest_object->base_structure_data, 0, heap - (dest_object->base_structure_data));


    for (j = 0; j < col_count; j++) {
        if (xlate[j] >= 0) {
            int array_index = 0;
            int max_array_index = 1;
            int item_count = 0;
            sqlite3_value *pValue;
            int64_t n = 0;
            double d = 0.0;
            const char *buf1 = NULL;
            size_t sz = 0;
            char *p_data;

            struct argo_field_description *fd;
            fd = &(t->description->description[xlate[j]]->public_description);

            if (fd->dont_read_origin) continue;
            if (fd->dont_db) continue;

            pValue = sqlite3_column_value(stmt, j);
            if (fd->is_array || fd->is_inet) {
                buf1 = sqlite3_value_blob(pValue);
                sz = sqlite3_value_bytes(pValue);
            } else if (fd->is_double) {
                if (pValue) d = sqlite3_value_double(pValue);
                buf1 = (const char *)&d;
                sz = sizeof(double);
            } else if (fd->argo_field_type == argo_field_data_type_integer) {
                if (pValue) n = sqlite3_value_int64(pValue);
                buf1 = (const char *)&n;
                sz = sizeof(int64_t);
            } else if (fd->argo_field_type == argo_field_data_type_string) {
                buf1 = (const char *)sqlite3_value_text(pValue);
                if (buf1 == NULL) {
                    sz = 0;
                } else if (strlen(buf1) == 0) {
                    sz = 0;
                } else {
                    sz = strlen(buf1) + 1;
                }
            } else {
                /* invalid */
                WALLY_LOG(AL_CRITICAL, "Invalid data type");
            }

            max_array_index = sqlt_get_max_index(fd, buf1, sz);
            array_index = -1;
            p_data = NULL;
            while (1) {
                array_index = sqlt_get_next_data(fd, array_index, p_data, &p_data, buf1, sz);
                if ( array_index < 0 || array_index >= max_array_index) break;
                item_count++;

                res = argo2_structure_write_field_by_description(fd,
                                                                 dest_object->base_structure_data,
                                                                 array_index,
                                                                 max_array_index,
                                                                 p_data,
                                                                 &heap,
                                                                 heap_end);
                if (res) {
                    if (res == ARGO_RESULT_ERR_TOO_LARGE) {
                         goto retry;
                    }
                    WALLY_LOG(AL_ERROR, "Error: Could not write field (%s): %s, ix = %d, max = %d, argo_field_type = %d",
                                         fd->field_name, argo_result_string(res), array_index, max_array_index, fd->argo_field_type);
                    err = 1;
                    goto end;
                }
            }

            if (fd->have_dynamic_count_index) {
                argo_structure_write_int_by_column_index(t->description,
                                                         dest_object->base_structure_data,
                                                         fd->dynamic_count_index,
                                                         item_count);
            }

            if (array_index > max_array_index) {
                WALLY_LOG(AL_ERROR, "Field %s of table %s contains more entries (%d) than allowed (%d). Trimmed!",
                          fd->field_name,
                          t->db_table_name,
                          array_index,
                          max_array_index);
            }
        }
    }

end:
    dest_object->total_size = heap - heap_start;
    argo_unlock();

    /* Copy out of our static buffer into an allocated buffer. We
     * don't pre-allocate this because we don't know how big it will
     * be. */
    if (err) {
        dest_object = NULL;
    } else {
        dest_object = argo_object_copy(dest_object);
    }

    if (alloc_size) WALLY_FREE(heap_start);

    return dest_object;
}

/*
 * The result of issuing a query to read some rows has completed.
 *
 * is_update is set if this row reading is the result of a
 * notification message indicating the table has been updated.
 *
 * Holds wally lock for duration.
 * This function process a single row from current sqlite3_step
 */
void sqlt_update_table_read_row(struct wp_connection *wp_conn,
                               sqlite3_stmt *stmt,
                               int col_count,
                               int *pg_row_to_argo_row,
                               int sequence_argo_id,
                               struct argo_object **objects,
                               int64_t *indexes,
                               int *object_count)
{
    int j;
    int res;
    int64_t seq;
    struct argo_object *object;
    int somebody_loves_me;
    struct wp_table *t = wp_conn->table;

    /* XXX For each row, process the row. */
    object = sqlt_parse_row(t, pg_row_to_argo_row, stmt, col_count);
    if (object) {
        /* send object upstream */
        if (sequence_argo_id >= 0) {
            struct argo_field_description *fd;
            fd = &(t->description->description[sequence_argo_id]->public_description);
            if (fd->argo_field_type != argo_field_data_type_integer) {
                /* XXX LOG. Bad. */
            } else if (fd->is_reference) {
                /* XXX LOG. Bad. */
            } else {
                seq = argo_read_int(((int8_t *)object->base_structure_data) + fd->offset, fd->size);
                //fprintf(stderr, "WP: Received row with sequence %ld\n", (long) seq);
                /* We only update our table seqeuencing
                 * when we're doing overall table reads-
                 * we don't want some other read
                 * in-between to short circuit our seqence
                 * updates and thereby skip rows. */
                if (seq > wp_conn->row_request_sequence) {
                    wp_conn->row_request_sequence = seq;
                }
            }
        }

        /* We test this object against our registrations... */
        somebody_loves_me = 0;
        for (j = 0; j < t->index_count; j++) {
            int64_t val;
            char *str;
            char val_str[100];
            int64_t req_id = 0;
            int64_t *p;

            if (t->indexes[j]->is_null) {
                if (t->indexes[j]->is_null_registered) {
                    req_id = t->indexes[j]->null_request_id;
                }
            } else if (t->indexes[j]->argo_field_type == argo_field_data_type_integer) {
                res = argo_object_read_int_by_column_index(object, t->indexes[j]->argo_index, &val);
                if (res) {
                    /* XXX LOG */
                } else {
                    snprintf(val_str, sizeof(val_str), "%ld", (long) val);
                    if ((p = argo_hash_lookup(t->indexes[j]->interests, val_str, strlen(val_str), NULL))) {
                        req_id = *p;
                    } else {
                    }
                }
            } else if (t->indexes[j]->argo_field_type == argo_field_data_type_string) {
                res = argo_object_read_string_by_column_index(object, t->indexes[j]->argo_index, &str);
                if (res) {
                    /* XXX LOG */
                } else {
                    /* XXX If we need to support null
                     * string interest registrations in
                     * the future, we will need to do a
                     * little better here. For now, this
                     * is perfectly fine. */
                    if (str && ((p = argo_hash_lookup(t->indexes[j]->interests, str, strlen(str), NULL)))) {
                        req_id = *p;
                    } else {
                    }
                }
            } else {
                /* Erk. */
                /* XXX LOG */
            }

            if (req_id) {
                somebody_loves_me = 1;
                /* Send them uh.. rows */
                /* XXX IMPLEMENT ME */
                //fprintf(stderr, "WP: Supposed to send this row upstream, because somebody is interested in it.\n");
                if (*object_count >= (wally_gbl_cfg.wally_postgres_db_query_batch_size * WALLY_MAX_INDEXED_COLUMNS)) {
                    /* XXX LOG! */
                    WALLY_LOG(AL_ERROR, "Object count exceeds max");
                } else {
                    objects[*object_count] = object;
                    indexes[*object_count] = req_id;
                    (*object_count)++;
                    argo_object_hold(object);
                }
            }
        }

        if (somebody_loves_me) {
            wp_conn->row_count++;
        }
        argo_object_release(object);
        object = NULL;
    }
}


char *sqlt_field_data_from_argo(struct argo_structure_description *argo_desc,
                                struct argo_field_description *pd,
                                void *struct_data,
                                int *data_sz)
{
    char *data;
    char *p;
    int sz = 0;
    int array_count;
    int array_index;
    int res;
    void *src_addr_void = NULL;
    size_t src_size = 0;
    size_t unit_size = 0;

    array_count = argo_structure_field_get_array_count_by_description(argo_desc, pd, struct_data);
    if (pd->argo_field_type != argo_field_data_type_string) {
        /* size for each element is fixed and known */
        if (pd->is_double) {
            unit_size = sizeof(double);
        } else if (pd->is_inet) {
            unit_size = sizeof(struct argo_inet);
        } else if (pd->argo_field_type == argo_field_data_type_integer){
            unit_size = sizeof(int64_t);
        } else {
            /* invalid, should never come here*/
            WALLY_LOG(AL_CRITICAL, "Invalid data type: Field type: %d, Field name: %s", pd->argo_field_type, pd->field_name);
        }
        sz = array_count* unit_size;

        data =  WALLY_MALLOC(sz);
        p = data;
        for (array_index = 0; array_index < array_count; array_index++) {
            res = argo_structure_read_field_by_description(argo_desc,
                                                           pd,
                                                           struct_data,
                                                           array_index,
                                                           &src_addr_void,
                                                           &src_size,
                                                           NULL);
            if (res) {
                WALLY_LOG(AL_ERROR, "Could not set up to read (%s)", pd->field_name);
            } else {
                if (!src_addr_void || src_size == 0) {
                    memset(p, 0, unit_size);
                } else {
                    if (pd->argo_field_type == argo_field_data_type_integer) {
                        int64_t n;
                        switch (src_size) {
                            case 1:
                                n = *((int8_t*)src_addr_void);
                                break;
                            case 2:
                                n = *((int16_t*)src_addr_void);
                                break;
                            case 4:
                                n = *((int32_t*)src_addr_void);
                                break;
                            case 8:
                            default:
                                n = *((int64_t*)src_addr_void);
                                break;
                        }
                        memcpy(p, &n, sizeof(n));
                    } else {
                        memcpy(p, (char *)src_addr_void, src_size);
                    }
                }
                p += unit_size;
            }
        }
    } else {
        /* element is string, size is variable */
        sz = 0;
        int new_sz;
        data = NULL;
        for (array_index = 0; array_index < array_count; array_index++) {
            res = argo_structure_read_field_by_description(argo_desc,
                                                           pd,
                                                           struct_data,
                                                           array_index,
                                                           &src_addr_void,
                                                           &src_size,
                                                           NULL);
            new_sz = sz + src_size+1;
            data = WALLY_REALLOC(data, new_sz);
            memcpy(data + sz, (char *)src_addr_void, src_size);
            data[new_sz-1] = '\0';
            sz = new_sz;
        }
    }

    *data_sz = sz;
    return data;
}

/*
 * argo_desc->description_count can change while this is occuring
 * This function must be called with argo_lock
 */
int sqlt_bind_exe_argo(struct wp_connection *wp_conn,
                       char *sql_str,
                       struct argo_structure_description *argo_desc,
                       struct argo_object *write_object)
{
    size_t i;
    int result;
    char **arrays;
    sqlite3_stmt *stmt;
    sqlite3 *db = (sqlite3 *)(wp_conn->db_conn);
    int is_true_origin = wp_conn->wp_db->is_true_origin;
    void *struct_data = write_object->base_structure_data;

    sqlite3_prepare_v2(db, sql_str, -1, &stmt, NULL);

    /* bind columns */
    arrays = (char **)WALLY_MALLOC(argo_desc->description_count*sizeof(char*));
    int ct = 0;
    int data_sz;

    for (i = 0; i < argo_desc->description_count; i++) {
        struct argo_field_description *pd = &(argo_desc->description[i]->public_description);
        if (!pd->write_value || pd->dont_db || (is_true_origin && pd->dont_write_origin)) {
            arrays[i] = NULL;
            continue;
        }

        if (pd->is_sequence && argo_object_get_sequence(write_object) == 0) {
            arrays[i] = NULL;
            continue;
        }

        ct++;

        arrays[i] = sqlt_field_data_from_argo(argo_desc, pd, struct_data, &data_sz);
        if (pd->is_array || pd->is_inet) {
            sqlite3_bind_blob(stmt, ct, arrays[i], data_sz, NULL);
        } else if (pd->is_double) {
            sqlite3_bind_double(stmt, ct, *((double*)(arrays[i])));
        } else if (pd->argo_field_type == argo_field_data_type_string) {
            sqlite3_bind_text(stmt, ct, arrays[i], -1, NULL);
        } else if (pd->argo_field_type == argo_field_data_type_integer) {
            sqlite3_bind_int64(stmt, ct, *((sqlite3_int64*)(arrays[i])));
        } else {
            /* invalid */
            WALLY_LOG(AL_CRITICAL, "Invalid SQLite binding");
        }
    }

    /* "SQLT_TEST": for perormace evaluation */
    WALLY_DEBUG_POSTGRES("SQLT_TEST STEP ST, sql: %s", sql_str);
    do {
        result = sqlite3_step(stmt);
        WALLY_DEBUG_POSTGRES("sqlite3_step, rc=%d, db=%s, table=%s, ct=%d, sql=%s, errMsg=%s",
                             result, wp_conn->wp_db->name, wp_conn->table->db_table_name, ct, sql_str, sqlite3_errmsg(db));
    }while (result == SQLITE_BUSY || result == SQLITE_LOCKED);
    WALLY_DEBUG_POSTGRES("SQLT_TEST STEP END, sql: %s", sql_str);

    sqlite3_finalize(stmt);
    wp_conn->state = conn_idle;
    for (i = 0; i < argo_desc->description_count; i++) {
        if (arrays[i]) WALLY_FREE(arrays[i]);
    }

    WALLY_FREE(arrays);

    return result;
}

/*
 * Update an object in the database.
 *
 * Update will only update a row if it already exists. It is common to
 * call both update and insert on each row when it is being "written"
 * to the database. One of the two is basically guaranteed to
 * fail. There is a short time during which a race condition can exist
 * between the update and the insert, but we avoid that problem by
 * having only one thread at a time writing.
 *
 * wp lock must be held when called.
 */

 void cleanup_objects(struct wp_connection *wp_conn, int success)
 {
    int i;
    struct wp_db *wp_db = wp_conn->wp_db;
    struct argo_object **objects = wp_conn->object_info.objects;
    int *qindexes = wp_conn->object_info.qindexes;
    int cnt = wp_conn->object_info.cnt;


    for (i = 0; i < cnt; i++) {

        if (i < success) {
            if (objects[i]) argo_object_release(objects[i]);
        } else {
            /* enquque to the head in the reverse order */
            int k = cnt - 1 - (i - success);
            WALLY_DEBUG_POSTGRES_POLL("%s: back-queueing object, ix = %d", wp_db->name, qindexes[i]);
            wally_origin_enqueue_write_row(wp_db->db_to_wally_cookie, qindexes[k], objects[k], 1);
        }
    }

    wp_conn->object_info.cnt = 0;
 }

int tables_inserted_walk_cb(void* cookie, void* object)
{
    if (object) {
        struct wp_db *wp_db = (struct wp_db *) cookie;
        struct wp_table *t = (struct wp_table *)object;
        wally_db_log_row_counts(t);
        argo_hash_remove(wp_db->tables_inserted, t->db_table_name, strlen(t->db_table_name), NULL);
    }

    return 0;
}

 int sqlt_update_insert_rows(struct wp_connection *wp_conn)
 {
    static int64_t total_rows = 0;
    static int64_t total_time_us = 0;

    struct wp_db *wp_db = wp_conn->wp_db;
    char* zErrMsg;
    int rc = -1;
    int rc1 = -1;
    int rc2 = -1;
    int i;
    int64_t wrt_rate; /* update/insert rows per second */
    int64_t ts1;
    int64_t walk_key = 0;
    int success = 0;
    sqlite3 *db = (sqlite3 *)(wp_conn->db_conn);
    struct argo_object **objects = wp_conn->object_info.objects;
    int cnt = wp_conn->object_info.cnt;

    /* "SQLT_TEST": for perormace evaluation */
    WALLY_DEBUG_POSTGRES("SQLT_TEST WRITE ST, db=%s", wp_conn->wp_db->name);
    ts1 = epoch_us();
    if (0 != (rc = sqlite3_exec(db, "PRAGMA synchronous=NORMAL", NULL, NULL, &zErrMsg))) goto fail;
    if (0 != (rc = sqlite3_exec(db, "PRAGMA count_changes=OFF", NULL, NULL, &zErrMsg))) goto fail;
    if (0 != (rc = sqlite3_exec(db, "PRAGMA journal_mode=WAL", NULL, NULL, &zErrMsg))) goto fail;
    if (0 != (rc = sqlite3_exec(db, "PRAGMA temp_store=MEMORY", NULL, NULL, &zErrMsg))) goto fail;
    if (0 != (rc = sqlite3_exec(db, "BEGIN TRANSACTION", NULL, NULL, &zErrMsg))) goto fail;

    for (i = 0; i < cnt; i++) {
        struct argo_object *write_object = objects[i];
        rc2 = -1;
        rc1 = sqlt_write_object_update(wp_conn, write_object, 1);
        if (rc1 == WALLY_RESULT_ASYNCHRONOUS || rc1 == WALLY_RESULT_ERR) {
            rc2 = sqlt_write_object_insert(wp_conn, write_object);
        }

        if (rc1 != WALLY_RESULT_ASYNCHRONOUS && rc2 != WALLY_RESULT_ASYNCHRONOUS) {
            /* both update and insert the row failed, stop writing the rest objects */
            WALLY_LOG(AL_ERROR, "sqlt_update_insert_rows failed db=%s, %d out of %d success", wp_db->name, success, cnt);
            break;
        } else {
            success++;
        }
    }

    if (0 != (rc = sqlite3_exec(db, "COMMIT TRANSACTION", NULL, NULL, &zErrMsg))) goto fail;
    total_time_us += epoch_us() - ts1;
    total_rows += success;
    wrt_rate = (1000000*total_rows)/total_time_us;
    WALLY_DEBUG_POSTGRES("SQLT_TEST WRITE END, db=%s, rows=%d, average written rate=%" PRId64 " rows per second",
                         wp_conn->wp_db->name, success, wrt_rate);

    /* logging row counts for each table */
    argo_hash_walk(wp_db->tables_inserted, &walk_key, tables_inserted_walk_cb, wp_db);

    return success;

fail:

    WALLY_LOG(AL_CRITICAL, "sqlt_update_insert_rows failed, rc=%d, db=%s, errMsg: %s", rc, wp_conn->wp_db->name, zErrMsg);
    if (zErrMsg) sqlite3_free(zErrMsg);

    return 0;
 }

 void sqlt_write_object_update_1(struct zevent_base *base,
                                void *void_cookie,    /* conn */
                                int64_t int_cookie,
                                void *extra_cookie1,
                                void *extra_cookie2,
                                void *extra_cookie3,
                                int64_t extra_int_cookie)
{
    struct wp_connection *wp_conn = (struct wp_connection *)void_cookie;
    struct wp_db *wp_db = wp_conn->wp_db;

    int success=0;

    ZPATH_RWLOCK_WRLOCK(&(wp_conn->wp_db->lock), __FILE__, __LINE__);

    success = sqlt_update_insert_rows(wp_conn);
    WALLY_DEBUG_POSTGRES("update/insert rows %d out of %d success: db=%s", success, wp_conn->object_info.cnt, wp_db->name);
    cleanup_objects(wp_conn, success);

    wp_conn->state = conn_idle;
    WALLY_DEBUG_POSTGRES_CONN("Free connection %s/%d", wp_db->name, wp_conn->my_index);
    TAILQ_INSERT_TAIL(&(wp_db->free_connections), wp_conn, free_connections);

    wally_db_set_size(wp_db);
    ZPATH_RWLOCK_UNLOCK(&(wp_conn->wp_db->lock), __FILE__, __LINE__);

    if (success > 0) {
        /* either update or insert success */
        zevent_base_call(zevent_get_for_class(SQLT_REQUEST_THREAD_POOL),
                        do_wally_xfer_resume_xmit,
                        wp_db->db_to_wally_cookie,
                        0);
   }
}
int sqlt_write_object_update(struct wp_connection *wp_conn, struct argo_object *write_object, int synch)
{
    struct argo_structure_description *argo_desc;
    struct wp_table *wpt;
    char *sql_str;
    size_t sz;
    char *s, *e;
    int64_t sequence;
    int64_t index;
    int sequence_index = 0;
    int key_index = 0;
    size_t i;
    int result;
    int is_true_origin = 0;
    int res;

    if (!wp_conn->wp_db->is_row_writable) {
        WALLY_LOG(AL_ERROR, "%s: Row to write, db not row writable", wp_conn->wp_db->name);
        return WALLY_RESULT_NO_ERROR;
    }

    is_true_origin = wp_conn->wp_db->is_true_origin;

    /* XXX Get the table for the object. */
    argo_read_lock();
    argo_desc = argo_global.all_descriptions[write_object->base_structure_index];
    wpt = argo_hash_lookup(wp_conn->wp_db->tables_by_argo_name, argo_desc->type, argo_desc->type_len, NULL);
    if (!wpt)  {
        argo_unlock();
        WALLY_DEBUG_POSTGRES("%s: Row to write, table %s non-existent", wp_conn->wp_db->name, argo_desc->type);
        res = -1;
        if (synch) {
            wpt = wp_just_create_table(wp_conn->wp_db, argo_desc->type, argo_desc->type);
            if (wpt) {
                wp_conn->table = wpt;
                res = sqlt_synchronize(wp_conn);
            }
        }

        if (res == SQLT_SUCCESS) {
            return sqlt_write_object_update(wp_conn, write_object, 0);
        } else {
            WALLY_LOG(AL_ERROR, "%s: Row to write, table %s failed to be created", wp_conn->wp_db->name, argo_desc->type);
            return WALLY_RESULT_BAD_ARGUMENT;
        }
    }

    wp_conn->table = wpt;

    if (synch && wpt->argo_field_count != argo_desc->description_count) {
        WALLY_LOG(AL_NOTICE, "DB resynchronization needed: %s:%s: %s, %d != %d",
                  wpt->db->name, wpt->db_table_name, argo_desc->type, wpt->argo_field_count, (int)argo_desc->description_count);

        argo_unlock();
        res = sqlt_synchronize(wp_conn);
        if ( res != SQLT_SUCCESS) {
            WALLY_LOG(AL_ERROR, "DB resynchronization failed: %s:%s: %s, %d != %d",
                      wpt->db->name, wpt->db_table_name, argo_desc->type, wpt->argo_field_count, (int)argo_desc->description_count);
            return WALLY_RESULT_WOULD_BLOCK;
        } else {
           return sqlt_write_object_update(wp_conn, write_object, 0);
        }
    }

    /*
     * construct sql cmd :
     * UPDATE <TABLE_NAME> Col1=?, col2=?,.... WHERE ....
     */
    sz = 128 * (argo_desc->description_count) + 256;

retry:
    sql_str = WALLY_MALLOC(sz);
    s = sql_str;
    e = s + sz;
    s += sxprintf(s, e, "UPDATE %s set ", argo_desc->type);
    for (i = 0; i < argo_desc->description_count; i++) {
        struct argo_field_description *pd = &(argo_desc->description[i]->public_description);
        if (pd->is_sequence) sequence_index = i;
        if (pd->is_key) key_index = i;
        if (!pd->write_value || pd->dont_db || (is_true_origin && pd->dont_write_origin)) continue;
        if (pd->is_sequence && argo_object_get_sequence(write_object) == 0) continue;

        if (i == 0) {
            s += sxprintf(s, e, "%s=?", pd->field_name);
        } else {
            s += sxprintf(s, e, ", %s=?", pd->field_name);
        }
    }

    sequence = argo_read_int(write_object->base_structure_data + argo_desc->description[sequence_index]->public_description.offset,
                             argo_desc->description[sequence_index]->public_description.size);

    if (argo_desc->description[key_index]->public_description.argo_field_type == argo_field_data_type_string) {
        char *index_str;
        argo_object_read_string_by_column_index(write_object, key_index, &index_str);
        if (is_true_origin) {
            s += sxprintf(s, e, " where %s='%s';",
                                argo_desc->description[key_index]->public_description.field_name,
                                index_str);
        } else {
            s += sxprintf(s, e, " where %s='%s' and %s<%ld;",
                                argo_desc->description[key_index]->public_description.field_name,
                                index_str,
                                argo_desc->description[sequence_index]->public_description.field_name,
                                (long)sequence);
        }
    } else {
        index = argo_read_int(write_object->base_structure_data + argo_desc->description[key_index]->public_description.offset,
                              argo_desc->description[key_index]->public_description.size);
        if (is_true_origin) {
            s += sxprintf(s, e, " where %s=%ld;",
                                argo_desc->description[key_index]->public_description.field_name,
                                (long)index);
        } else {
            s += sxprintf(s, e, " where %s=%ld and %s<%ld;",
                                argo_desc->description[key_index]->public_description.field_name,
                                (long)index,
                                argo_desc->description[sequence_index]->public_description.field_name,
                                (long)sequence);
        }
    }

    if (s >= e){
        WALLY_FREE(sql_str);
        sz *= 2;
        goto retry;
    }

    result = sqlt_bind_exe_argo(wp_conn, sql_str, argo_desc, write_object);
    argo_unlock();

    if (result != SQLITE_DONE && result != SQLITE_OK) {
        WALLY_LOG(AL_ERROR, "sqlite3_step failed, rc=%d, db=%s, sql=%s", result, wpt->db->name, sql_str);
        res = WALLY_RESULT_ERR;
    } else {
        WALLY_DEBUG_POSTGRES("sqlite3_step success, rc=%d, db=%s, sql=%s", result, wpt->db->name, sql_str);
        res = WALLY_RESULT_ASYNCHRONOUS;
    }

    if (sql_str) WALLY_FREE(sql_str);

    return res;
}

/*
 * Insert an object to the database, but only if it does not exist.
 * wp lock must be held when called.
 */
int sqlt_write_object_insert(struct wp_connection *wp_conn, struct argo_object *write_object)
{
    struct argo_structure_description *argo_desc;
    struct wp_table *wpt;
    char *sql_str;
    int sz;
    char *s, *e;
    int key_index = 0;
    int index;
    size_t i;
    int result = 0;
    int is_true_origin;
    int res;

    if (!wp_conn->wp_db->is_row_writable) return WALLY_RESULT_NO_ERROR;

    /* XXX Get the table for the object. */
    argo_read_lock();
    argo_desc = argo_global.all_descriptions[write_object->base_structure_index];

    wpt = argo_hash_lookup(wp_conn->wp_db->tables_by_argo_name, argo_desc->type, argo_desc->type_len, NULL);
    if (!wpt)  {
        argo_unlock();
        return WALLY_RESULT_BAD_ARGUMENT;
    }

    wp_conn->table = wpt;

    is_true_origin = wp_conn->wp_db->is_true_origin;

    /*
     * construct sql cmd :
     * INSERT INTO <TABLE_NAME> (Col1, col2, ...) WHERE ? as Col1, ? as Col2, ....
     * WHERE NOT EXISTS (SELECT 1 FROM <TABLE NAME> WHERE col_index=index_value)
     */
    sz = 128 * (argo_desc->description_count) + 256;

retry:

    sql_str = WALLY_MALLOC(sz);
    s = sql_str;
    e = s + sz;
    s += sxprintf(s, e, "INSERT INTO %s (", argo_desc->type);
    for (i = 0; i < argo_desc->description_count; i++) {
        struct argo_field_description *pd = &(argo_desc->description[i]->public_description);
        if (!pd->write_value || pd->dont_db || (is_true_origin && pd->dont_write_origin)) continue;
        /* If it is the sequence, and if the sequence is zero, skip
         * it. This is done to keep from having duplicate insertions
         * into a table for the first time, as first-insert would
         * otherwise always be zero for sequence, and wouldn't leave
         * the value 0 until updated. */
        if (pd->is_sequence && argo_object_get_sequence(write_object) == 0) continue;


        if (i == 0) {
            s += sxprintf(s, e, "%s", pd->field_name);
        } else {
            s += sxprintf(s, e, ", %s", pd->field_name);
        }
    }
    s += sxprintf(s, e, ") SELECT ");

    for (i = 0; i < argo_desc->description_count; i++) {
        struct argo_field_description *pd = &(argo_desc->description[i]->public_description);
        if (!pd->write_value || pd->dont_db || (is_true_origin && pd->dont_write_origin)) continue;
        if (pd->is_sequence && argo_object_get_sequence(write_object) == 0) continue;

        if (pd->is_key) key_index = i;

        if (i == 0) {
            s += sxprintf(s, e, "? as %s", pd->field_name);
        } else {
            s += sxprintf(s, e, ", ? as %s", pd->field_name);
        }
    }

    if (argo_desc->description[key_index]->public_description.argo_field_type == argo_field_data_type_string) {
        char *index_str;
        argo_object_read_string_by_column_index(write_object, key_index, &index_str);
        s += sxprintf(s, e, " WHERE NOT EXISTS ( SELECT 1 FROM %s WHERE %s='%s');",
                            argo_desc->type,
                            argo_desc->description[key_index]->public_description.field_name,
                            index_str);
    } else {
        index = argo_read_int(write_object->base_structure_data + argo_desc->description[key_index]->public_description.offset,
                              argo_desc->description[key_index]->public_description.size);

        s += sxprintf(s, e, " WHERE NOT EXISTS ( SELECT 1 FROM %s WHERE %s=%ld);",
                            argo_desc->type,
                            argo_desc->description[key_index]->public_description.field_name,
                            (long)index);
    }

    if (s >= e){
        WALLY_FREE(sql_str);
        sz *= 2;
        goto retry;
    }

    result = sqlt_bind_exe_argo(wp_conn, sql_str, argo_desc, write_object);
    argo_unlock();

    if (result != SQLITE_DONE && result != SQLITE_OK) {
        res = WALLY_RESULT_ERR;
        WALLY_DEBUG_POSTGRES("sqlite3_step failed, rc=%d, db=%s, sql=%s", result, wpt->db->name, sql_str);

    } else {
        res = WALLY_RESULT_ASYNCHRONOUS;
        WALLY_DEBUG_POSTGRES("sqlite3_step success, rc=%d, db=%s, sql=%s", result, wpt->db->name, sql_str);
        wally_db_set_row_counts(wp_conn->table, wp_conn->table->cur_row_count+1, 0);
        if (NULL == argo_hash_lookup(wp_conn->wp_db->tables_inserted, wpt->db_table_name, strlen(wpt->db_table_name), NULL)) {
            argo_hash_store(wp_conn->wp_db->tables_inserted, wpt->db_table_name, strlen(wpt->db_table_name), 1, wpt);
        }
    }
    if (sql_str) WALLY_FREE(sql_str);

    return res;
}

int sqlt_read_sequence(struct wp_connection *wp_conn)
{
    int rc;
    int res = SQLT_SUCCESS;
    char *zErrMsg = NULL;
    char **pazResult =  NULL;
    int row = 0; /* This is the row count for data only, not including title row in pazResult */
    int col = 0;
    char sql_str[DEFAULT_DB_STRING_SIZE];

    sqlite3 *db = (sqlite3 *)(wp_conn->db_conn);
    struct wp_table *t = wp_conn->table;
    t->state = table_read_highest_sequence_begin;
    snprintf(sql_str,
             sizeof(sql_str),
             "SELECT %s FROM %s ORDER BY %s DESC LIMIT 1",
             t->sequence_column_name,
             t->db_table_name,
             t->sequence_column_name);

    do {
        if (zErrMsg) sqlite3_free(zErrMsg);
        rc = sqlite3_get_table(db, sql_str, &pazResult, &row, &col, &zErrMsg);
        WALLY_DEBUG_POSTGRES("sqlite3_get_table rc=%d, db=%s, table=%s, sqlcmd: %s, errMsg: %s",
                             rc, t->db->name, t->db_table_name, sql_str, zErrMsg);
    }while (rc == SQLITE_BUSY || rc == SQLITE_LOCKED);

    if (rc) {
        WALLY_LOG(AL_CRITICAL, "sqlt_read_sequence failed, rc=%d, db=%s, table=%s, sqlcmd: %s, errMsg: %s",
                               rc, t->db->name, t->db_table_name, sql_str, zErrMsg);
        if (zErrMsg) sqlite3_free(zErrMsg);
        res = SQLT_ERR_READ_SEQUENCE;

    } else {
        if (row == 1) {
            /* ignore pazResult[0], which is column name */
            t->current_max_sequence = strtoll(pazResult[1], NULL, 10);
            WALLY_DEBUG_POSTGRES("Max sequence = %ld, db=%s, table=%s",
                                 (long) t->current_max_sequence, t->db->name, t->db_table_name);
        } else {
            t->current_max_sequence = 0;
        }

        if (t->min_valid_sequence_set > t->min_valid_sequence_performed){
            sqlt_sequence_delete(wp_conn);
        }

        t->state = table_ready;
        t->needs_to_be_resynchronized = 0;

        if (pazResult) sqlite3_free_table(pazResult);
        res = SQLT_SUCCESS;
    }

    return res;
}

/* Issue sequence update row request. */
void sqlt_sequence_delete_1(struct zevent_base *base, void *cookie1, int64_t int_cookie)
{
    struct wp_connection *wp_conn = (struct wp_connection *)cookie1;
    ZPATH_RWLOCK_WRLOCK(&(wp_conn->wp_db->lock), __FILE__, __LINE__);
    sqlt_sequence_delete(wp_conn);

    wp_conn->state = conn_idle;
    wp_conn->wp_db->is_blocking = 0;
    TAILQ_INSERT_TAIL(&(wp_conn->wp_db->free_connections), wp_conn, free_connections);

    ZPATH_RWLOCK_UNLOCK(&(wp_conn->wp_db->lock), __FILE__, __LINE__);
}
int sqlt_sequence_delete(struct wp_connection *wp_conn)
{
    int rc;
    char *zErrMsg = NULL;
    char sql_str[DEFAULT_DB_STRING_SIZE];

    sqlite3 *db = (sqlite3 *)(wp_conn->db_conn);
    struct wp_table *t = wp_conn->table;

    if (t->min_valid_sequence_set == 0 ) {
        t->min_valid_sequence_performed = 0;
        return 0;
    }

    t->sequence_deletion_in_progress = 1;
    wp_conn->state = conn_table_delete_sequence;

    snprintf(sql_str,
             sizeof(sql_str),
             "DELETE from %s WHERE %s < %ld",
             t->db_table_name,
             t->sequence_column_name,
             (long)t->min_valid_sequence_set);

    do {
        if (zErrMsg) sqlite3_free(zErrMsg);
        rc = sqlite3_exec(db, sql_str, NULL, NULL, &zErrMsg);
        WALLY_DEBUG_POSTGRES("sqlite3_exec rc=%d, db=%s, table=%s, sqlcmd: %s, errMsg: %s",
                             rc, t->db->name, t->db_table_name, sql_str, zErrMsg);
    }while (rc == SQLITE_BUSY || rc == SQLITE_LOCKED);

    wp_conn->table->sequence_deletion_in_progress = 0;
    wp_conn->table->state = table_ready;

    if (rc) {
        WALLY_LOG(AL_WARNING, "sqlt_sequence_delete failed, rc=%d, db=%s, table=%s, sqlcmd: %s, errMsg: %s",
                               rc, t->db->name, t->db_table_name, sql_str, zErrMsg);
        if (zErrMsg) sqlite3_free(zErrMsg);
    } else {
        int cnt = sqlite3_changes(db);
        if (cnt > 0) {
            wally_db_set_row_counts(t, t->cur_row_count - cnt, 1);
            wally_db_set_size(wp_conn->wp_db);
        }

        t->min_valid_sequence_performed = t->min_valid_sequence_set;
        WALLY_LOG(AL_NOTICE, "Deleting rows from %s/%s/%s: %s",
                             wp_conn->wp_db->name,
                             wp_conn->table->db_table_name,
                             wp_conn->name,
                             sql_str);
    }

    return rc;
}

int sqlt_add_indexes(struct wp_connection *wp_conn, char *sql_str)
{
    int rc = 0;
    int res = SQLT_SUCCESS;
    char *zErrMsg = NULL;

    sqlite3 *db = (sqlite3 *)(wp_conn->db_conn);
    struct wp_table *t = wp_conn->table;

    t->state = table_add_indexes;

    if (sql_str && strlen(sql_str) > 0) {

        char sql_cmd[MAX_SINGLE_SQL_LENGTH];
        char *p1 = sql_str;
        int len = 0;
        char exist_msg[] = "already exists";
        char no_col_msg[] = "no such column";
        while (rc==0 && *p1 != '\0') {
            len++;
            if (*(p1 + len - 1) == ';') {
                memset(sql_cmd, 0, MAX_SINGLE_SQL_LENGTH);
                memcpy(sql_cmd, p1, len);
                p1 += len;
                len = 0;
                do {
                    if (zErrMsg) sqlite3_free(zErrMsg);
                    rc = sqlite3_exec(db, sql_cmd, NULL, NULL, &zErrMsg);
                    WALLY_DEBUG_POSTGRES("sqlite3_exec rc=%d, db=%s, table=%s, sqlcmd: %s, zErrMsg: %s",
                                         rc, t->db->name, t->db_table_name, sql_cmd, zErrMsg);

                    if (rc == SQLITE_ERROR && zErrMsg && !is_current_source_code()) {
                        WALLY_LOG(AL_CRITICAL, "Incompatibale SQLite build !");
                        break;
                    }

                    /* ignore the error if the index already exists */
                    /* "index zpath_xxxx already exists" */
                    if (rc == SQLITE_ERROR && zErrMsg && strlen(zErrMsg) >= strlen(exist_msg)) {
                        char *p = zErrMsg + strlen(zErrMsg) - strlen(exist_msg);
                        if (strcmp(p, exist_msg) == 0) {
                            sqlite3_free(zErrMsg);
                            rc = 0;
                            break;
                        }
                    }

                    /* ignore the error if no such column */
                    /* "no such column: xxxx */
                    if (rc == SQLITE_ERROR && zErrMsg && strncmp(zErrMsg, no_col_msg, strlen(no_col_msg))) {
                        sqlite3_free(zErrMsg);
                        rc = 0;
                        break;
                    }

                } while (rc == SQLITE_BUSY || rc == SQLITE_LOCKED);
            }
        }
    }

    if (rc) {
        WALLY_LOG(AL_CRITICAL, "sqlt_add_indexes failed, rc=%d, db=%s, table=%s, sqlcmd: %s, errMsg: %s",
                               rc, t->db->name, t->db_table_name, sql_str, zErrMsg);

        /* This function will terminate the thread, dcode after it will not be executed*/
        if (zErrMsg) sqlite3_free(zErrMsg);
        res = SQLT_ERR_ADD_INDEXES;
    } else {
        /* Read highest sequence next */
        res = SQLT_SUCCESS;
    }

    return res;
}

int sqlt_read_indexes(struct wp_connection *wp_conn)
{
    int rc;
    int res = SQLT_SUCCESS;
    char *zErrMsg = NULL;
    char **pazResult =  NULL;
    int row = 0; /* This is the row count for data only, not including title row in pazResult */
    int col = 0;
    char sql_str[DEFAULT_DB_STRING_SIZE];

    sqlite3 *db = (sqlite3 *)(wp_conn->db_conn);
    struct wp_table *t = wp_conn->table;

    t->state = table_read_indexes;
    snprintf(sql_str,
             sizeof(sql_str),
             "select name FROM sqlite_master WHERE type='index' AND tbl_name='%s';",
             t->db_table_name);

    do {
        if (zErrMsg) sqlite3_free(zErrMsg);
        rc = sqlite3_get_table(db, sql_str, &pazResult, &row, &col, &zErrMsg);
        WALLY_DEBUG_POSTGRES("sqlite3_get_table rc=%d, db=%s, table=%s, sqlcmd: %s, errMsg: %s",
                             rc, t->db->name, t->db_table_name, sql_str, zErrMsg);
    }while (rc == SQLITE_BUSY || rc == SQLITE_LOCKED);

    if (rc) {
        WALLY_LOG(AL_CRITICAL, "sqlt_read_indexes failed, rc=%d, db=%s, table=%s, sqlcmd: %s, errMsg: %s",
                               rc, t->db->name, t->db_table_name, sql_str, zErrMsg);
        if (zErrMsg) sqlite3_free(zErrMsg);
        res = SQLT_ERR_READ_IDX;

    } else {
        int count = 0;
        char sql_cmd[DEFAULT_DB_STRING_SIZE] = {0};
        char *p = (char*) &sql_cmd[0];

        count = sqlt_process_index(t, pazResult, row, &p, DEFAULT_DB_STRING_SIZE);
        if (pazResult) sqlite3_free_table(pazResult);
        if (count < 0) {
            WALLY_LOG(AL_CRITICAL, "sqlite3_free_table failed, rc=%d, db=%s, table=%s, sqlcmd: %s, errMsg: %s",
                                   rc, t->db->name, t->db_table_name, sql_str, zErrMsg);
            res = SQLT_ERR_READ_IDX;
        } else if (count == 0) {
            /* Read highest sequence next */
            res = SQLT_SUCCESS;
        } else {
            res = sqlt_add_indexes(wp_conn, sql_cmd);
        }
    }

    return res;
}

int sqlt_add_columns(struct wp_connection * wp_conn, char *sql_str)
{
    int rc = 0;
    int res = SQLT_SUCCESS;
    char *zErrMsg = NULL;

    sqlite3 *db = (sqlite3 *)(wp_conn->db_conn);
    struct wp_table *t = wp_conn->table;

    t->state = table_add_columns;

    if (sql_str && strlen(sql_str) > 0) {

        char sql_cmd[MAX_SINGLE_SQL_LENGTH];
        char *p1 = sql_str;
        int len = 0;
        char dup_msg[] = "duplicate column name";

        while (rc == 0 && *p1 != '\0') {
            len++;
            if (*(p1 + len - 1) == ';') {
                memset(sql_cmd, 0, MAX_SINGLE_SQL_LENGTH);
                memcpy(sql_cmd, p1, len);
                p1 += len;
                len = 0;
                do {
                    if (zErrMsg) sqlite3_free(zErrMsg);
                    rc = sqlite3_exec(db, sql_cmd, NULL, NULL, &zErrMsg);
                    WALLY_DEBUG_POSTGRES("sqlite3_exec rc=%d, db=%s, table=%s, sqlcmd: %s, errMsg: %s",
                                         rc, t->db->name, t->db_table_name, sql_cmd, zErrMsg);

                    /* ignore the error if column already exist */
                    if (rc == SQLITE_ERROR) {
                        if (!is_current_source_code()) {
                            WALLY_LOG(AL_CRITICAL, "Incompatibale SQLite build !");
                        } else if (strncmp(dup_msg, zErrMsg, strlen(dup_msg)) == 0) {
                            sqlite3_free(zErrMsg);
                            rc = 0;
                        }
                    }

                } while (rc == SQLITE_BUSY || rc == SQLITE_LOCKED);
            }
        }
    }

    if (rc) {
        WALLY_LOG(AL_CRITICAL, "sqlt_add_columns failed, rc=%d, db=%s, table=%s, sqlcmd: %s, errMsg: %s",
                               rc, t->db->name, t->db_table_name, sql_str, zErrMsg);
        if (zErrMsg) sqlite3_free(zErrMsg);
        res = SQLT_ERR_ADD_COLUMNS;

    }

    return res;
}

int sqlt_get_columns(struct wp_connection *wp_conn)
{
    int rc;
    int res = SQLT_SUCCESS;
    char *zErrMsg = NULL;
    char **pazResult =  NULL;
    int row = 0; /* This is the row count for data only, not including title row in pazResult */
    int col = 0;
    char sql_str[DEFAULT_DB_STRING_SIZE];
    char *pSql = NULL;

    sqlite3 *db = (sqlite3 *)(wp_conn->db_conn);
    struct wp_table *t = wp_conn->table;

    t->state = table_read_columns;
    snprintf(sql_str, DEFAULT_DB_STRING_SIZE, "PRAGMA table_info(%s);", t->db_table_name);
    do {
        if (zErrMsg) sqlite3_free(zErrMsg);
        rc = sqlite3_get_table(db, sql_str, &pazResult, &row, &col, &zErrMsg);
        WALLY_DEBUG_POSTGRES("sqlite3_get_table rc=%d, db=%s, table=%s, sqlcmd: %s, errMsg: %s",
                             rc, t->db->name, t->db_table_name, sql_str, zErrMsg);
    }while (rc == SQLITE_BUSY || rc == SQLITE_LOCKED);

    if (rc) {
        WALLY_LOG(AL_CRITICAL, "sqlt_get_table_columns failed, rc=%d, db=%s, table=%s, sqlcmd: %s, errMsg: %s",
                                   rc, t->db->name, t->db_table_name, sql_str, zErrMsg);
        if (zErrMsg) sqlite3_free(zErrMsg);
        res = SQLT_ERR_GET_COLUMNS;

    } else {
        struct column_meta *columns = NULL;
        int update_count = 0;
        char sql_cmd[DEFAULT_DB_STRING_SIZE] ={0};

        if (row > 0) {
            /*
             * An example of pazResult format for a two-column table:
             * cid	name	type	notnull	dflt_value	pk
             * 0	rowid	INTEGER	0	    (null)	    1
             * 1	user	TEXT	0	    (null)	    0
             */
            int i, j;
            columns = (struct column_meta *) WALLY_MALLOC(sizeof(struct column_meta)*row);
            memset(columns, 0, sizeof(struct column_meta)*row);
            /* ignore first row (i==0) which are titles */
            for (i = 1; i < row+1; i++) {
                for(j = 0; j < col; j++) {
                    char *p = *(pazResult+i*col+j);
                    /* we only need "name" and "type" (2nd and 3rd column in pazResult) */
                    if (j == 1) {
                        strncpy(columns[i-1].name, p, sizeof(columns[i-1].name)-1);
                    } else if (j == 2) {
                        strncpy(columns[i-1].data_type, p, sizeof(columns[i-1].data_type)-1);
                    }
                }
            }

            rc = sqlt_process_columns(t, columns, row);
            if (columns) WALLY_FREE(columns);
            if (rc) {
                WALLY_LOG(AL_CRITICAL, "sqlt_process_columns failed, rc=%d, db=%s, table=%s, sqlcmd: %s, errMsg: %s",
                                       rc, t->db->name, t->db_table_name, sql_str, zErrMsg);
                res = SQLT_ERR_GET_COLUMNS;
            } else {

                pSql = &sql_cmd[0];
                update_count = sqlt_add_column_str(t, &pSql, DEFAULT_DB_STRING_SIZE);

                WALLY_DEBUG_POSTGRES("call sqlt_add_columns db=%s, table=%s, update_cont=%d, sql=%s",
                                     t->db->name, t->db_table_name,  update_count, sql_cmd);
                res = sqlt_add_columns(wp_conn, sql_cmd);
            }

        } else {
            /* table may not exist in the db */
            WALLY_LOG(AL_NOTICE, "sqlt_get_columns no record found, rc=%d, db=%s, table=%s, sqlcmd: %s, errMsg: %s",
                                 rc, t->db->name, t->db_table_name, sql_str, zErrMsg);
            t->state = table_doesnt_exist;
            res = SQLT_ERR_GET_COLUMNS;
        }

        sqlite3_free_table(pazResult);
    }

    return res;
}

int sqlt_create_table(struct wp_connection *wp_conn)
{
    int rc;
    char *zErrMsg = NULL;
    char sql_str[DEFAULT_DB_STRING_SIZE];

    sqlite3 *db = (sqlite3 *)(wp_conn->db_conn);
    struct wp_table *t = wp_conn->table;

    t->state = table_create_table;
    snprintf(sql_str,
             sizeof(sql_str),
             "CREATE TABLE %s (rowid INTEGER);",
             t->db_table_name);
    do {
        if (zErrMsg) sqlite3_free(zErrMsg);
        rc = sqlite3_exec(db, sql_str, NULL, NULL, &zErrMsg);
        WALLY_DEBUG_POSTGRES("sqlite3_exec rc=%d, db=%s, table=%s, sqlcmd: %s, errMsg: %s",
                             rc, t->db->name, t->db_table_name, sql_str, zErrMsg);

        /* ignore the error if the table already exists */
        if (rc == SQLITE_ERROR) {
            char msg[512];
            memset(msg, 0, sizeof(msg));
            snprintf(msg, sizeof(msg)-1, "table %s already exists", t->db_table_name);
            if (!is_current_source_code()) {
                WALLY_LOG(AL_CRITICAL, "Incompatibale SQLite build !");
            } else if (strcmp(msg, zErrMsg) == 0) {
                sqlite3_free(zErrMsg);
                rc = 0;
            }
        }

    }while (rc == SQLITE_BUSY || rc == SQLITE_LOCKED);

    wally_db_set_row_counts(t, 0, 1);

    if (rc) {
        WALLY_LOG(AL_CRITICAL, "sqlt_create_table failed, db=%s, table=%s, rc=%d, errMsg: %s",
                                t->db->name, t->db_table_name, rc, zErrMsg);
        if (zErrMsg) sqlite3_free(zErrMsg);
        return SQLT_ERR_CREATE_TABLE;
    } else {
        return SQLT_SUCCESS;
    }

}

int cnt_callback(void *count, int argc, char **argv, char **azColName) {
    char *pEnd;
    *((int64_t*)count) = strtoll(argv[0], &pEnd, 10);
    return 0;
}

int sqlt_get_table_row_count(struct wp_connection *wp_conn, int deleted)
{
    sqlite3 *db = (sqlite3 *)(wp_conn->db_conn);
    struct wp_table *t = wp_conn->table;
    char sql_str[DEFAULT_DB_STRING_SIZE];
    char *zErrMsg;
    int rc;
    int64_t count = 0;

    if (deleted) {
        if (t->deleted_column_name == NULL) {
            return 0;
        }
        snprintf(sql_str, sizeof(sql_str), "SELECT COUNT(*) FROM '%s' WHERE deleted<>0;", t->db_table_name);
    } else {
        snprintf(sql_str, sizeof(sql_str), "SELECT COUNT(*) FROM '%s';", t->db_table_name);
    }
    rc = sqlite3_exec(db, sql_str, cnt_callback, &count, &zErrMsg);
    if (rc != SQLITE_OK) {
        WALLY_LOG(AL_ERROR, "sqlt_get_table_row_count failed: rc=%d, db=%s, table=%s, deleted=%d, errMsg = %s",
                            rc, t->db->name, t->db_table_name, deleted, zErrMsg);
        sqlite3_free(zErrMsg);
    } else {
        if (deleted) {
            wally_db_set_deleted_row_counts(t, count);
        } else {
            wally_db_set_row_counts(t, count, 1);
        }
    }

    return rc;
}

int sqlt_get_table(struct wp_connection *wp_conn)
{
    int rc;
    int res = SQLT_SUCCESS;
    char *zErrMsg = NULL;
    char **pazResult =  NULL;
    int row = 0; /* This is the row count for data only, not including title row in pazResult */
    int col = 0;
    char sql_str[DEFAULT_DB_STRING_SIZE];

    sqlite3 *db = (sqlite3 *)(wp_conn->db_conn);
    struct wp_table *t = wp_conn->table;

    t->state = table_read_table;
    snprintf(sql_str,
             sizeof(sql_str),
             "SELECT name FROM sqlite_master where name='%s';",
             t->db_table_name);

    do {
        if (zErrMsg) sqlite3_free(zErrMsg);
        rc = sqlite3_get_table(db, sql_str, &pazResult, &row, &col, &zErrMsg);
        WALLY_DEBUG_POSTGRES("sqlite3_get_table rc=%d, db=%s, table=%s, sqlcmd: %s, errMsg: %s",
                             rc, t->db->name, t->db_table_name, sql_str, zErrMsg);
    }while (rc == SQLITE_BUSY || rc == SQLITE_LOCKED);

    if (rc) {
        WALLY_LOG(AL_CRITICAL, "sqlt_get_table failed: rc=%d, db=%s, table=%s, errMsg = %s",
                               rc, t->db->name, t->db_table_name, zErrMsg);
        if (zErrMsg) sqlite3_free(zErrMsg);
        res = SQLT_ERR_GET_TABLE;

    } else {
        sqlite3_free_table(pazResult);
        if (row == 0) {
            /* table does not exist, create it if DB is writable */
            if (!t->db->is_alterable) {
                WALLY_LOG(AL_NOTICE, "Table %s does not exist, non-writable table", t->db_table_name);
                t->state = table_doesnt_exist;
                /*
                wp_conn->state = conn_idle;
                TAILQ_INSERT_TAIL(&(wp_conn->wp_db->free_connections), wp_conn, free_connections);
                */
                wp_conn->wp_db->is_blocking = 0;
                /* ????? */
                res = SQLT_ERR_GET_TABLE;
            } else {
                WALLY_LOG(AL_NOTICE, "call sqlt_create_table: %s in db: %s", t->db_table_name, t->db->name);
                res = SQLT_NO_TABLE;
            }
        } else {
            /* table exist */
            sqlt_get_table_row_count(wp_conn, 0);
            sqlt_get_table_row_count(wp_conn, 1);
            res = SQLT_SUCCESS;
        }
    }

    return res;
}


void sqlt_read_select_1(struct zevent_base *base, void *cookie1, int64_t int_cookie)
{
    sqlt_read_select((struct wp_connection *)cookie1);
}
void sqlt_read_select(struct wp_connection *wp_conn)
{
    int rc;
    int i;
    sqlite3_stmt *stmt;
    int col_count = 0;
    int row_count = 0;
    struct argo_object *objects[wally_gbl_cfg.wally_postgres_db_query_batch_size * WALLY_MAX_INDEXED_COLUMNS];
    int64_t indexes[wally_gbl_cfg.wally_postgres_db_query_batch_size * WALLY_MAX_INDEXED_COLUMNS];
    int object_count = 0;
    int *pg_row_to_argo_row = NULL;
    int sequence_argo_id = -1;
    int read_more = 0;
    char sql_str[DEFAULT_DB_STRING_SIZE];

    struct wp_db *wp_db = wp_conn->wp_db;

    sqlite3 *db = (sqlite3 *)(wp_conn->db_conn);
    struct wp_table *t = wp_conn->table;

    ZPATH_RWLOCK_WRLOCK(&(wp_conn->wp_db->lock), __FILE__, __LINE__);

    wp_conn->state = conn_table_read_begin;

    if (wp_conn->request_key) {
        snprintf(sql_str,
                 sizeof(sql_str),
                 "SELECT * from %s where %s='%s' and %s>%ld and %s<=%ld order by %s limit %d ",
                 wp_conn->table->db_table_name,
                 wp_conn->table->indexes[wp_conn->request_wp_index_index]->column_name,
                 wp_conn->request_key,
                 wp_conn->table->sequence_column_name,
                 (long)wp_conn->row_request_sequence,
                 wp_conn->table->sequence_column_name,
                 (long)wp_conn->table->current_max_sequence,
                 wp_conn->table->sequence_column_name, wally_gbl_cfg.wally_postgres_db_query_batch_size);

    } else {
        snprintf(sql_str,
                 sizeof(sql_str),
                 "SELECT * from %s where %s>%ld order by %s limit %d",
                 wp_conn->table->db_table_name,
                 wp_conn->table->sequence_column_name,
                 (long)wp_conn->row_request_sequence,
                 wp_conn->table->sequence_column_name, wally_gbl_cfg.wally_postgres_db_query_batch_size);
    }

    sqlite3_prepare_v2(db, sql_str, -1, &stmt, NULL);
    col_count = sqlite3_column_count(stmt);
    if (col_count > 0) {
        do {
            rc = sqlite3_step(stmt);
            WALLY_DEBUG_POSTGRES("sqlite3_step, rc=%d, db=%s, table=%s, ct=%d, sql=%s, errMsg=%s",
                                 rc, wp_conn->wp_db->name, wp_conn->table->db_table_name, col_count, sql_str, sqlite3_errmsg(db));
            switch (rc) {
                case SQLITE_ROW:
                    row_count++;
                    if (row_count == 1) {
                        /* process column if it is first row */
                        pg_row_to_argo_row = sqlt_parse_columns(t, stmt, col_count, &sequence_argo_id, wp_conn);
                    }

                    /* process row */
                    sqlt_update_table_read_row(wp_conn, stmt, col_count, pg_row_to_argo_row, sequence_argo_id,
                                               objects, indexes, &object_count);
                    break;
                case SQLITE_BUSY:
                case SQLITE_LOCKED:
                    break;
                default:
                    /* something wrong */
                    if (rc != SQLITE_DONE && rc != SQLITE_OK) {
                        WALLY_LOG(AL_CRITICAL, "WP: could not read rows, rc=%d", rc);
                        rc = -1; /* This will enforce it exit the while loop */
                        break;
                    }
            }
        } while (rc != SQLITE_DONE && rc != SQLITE_OK && rc != -1);

        if (row_count == 0) {
            WALLY_LOG(AL_WARNING, "WP: No row lookup db=%s, object_count=%d, sql=%s", wp_db->name, object_count, sql_str);
        } else {
            WALLY_DEBUG_POSTGRES("WP: %d row lookup db=%s, object_count=%d, sql=%s", row_count, wp_db->name, object_count, sql_str);
        }

        if (row_count == wally_gbl_cfg.wally_postgres_db_query_batch_size) {
            struct wp_index *index = wp_conn->table->indexes[wp_conn->request_wp_index_index];
            if ((index->is_null && !index->is_null_registered)) {
                /* Short circuit full table lookup if no one is asking for it. */
            } else {
                /* more to read */
                read_more = 1;
            }
        }

    } else {
        WALLY_LOG(AL_WARNING, "WP: No column lookup db=%s, sql=%s", wp_db->name, sql_str);
    }

    if (pg_row_to_argo_row) {
        WALLY_FREE(pg_row_to_argo_row);
        pg_row_to_argo_row = NULL;
    }

    sqlite3_finalize(stmt);
    wp_conn->state = conn_table_read_commit;

    int is_true_origin = 0;
    is_true_origin = wp_conn->wp_db->is_true_origin;

    ZPATH_RWLOCK_UNLOCK(&(wp_conn->wp_db->lock), __FILE__, __LINE__);

    /* Do row callbacks first, then command complete responses, then
     * resume transmit callback. */
    for (i = 0; i < object_count; i++) {
        if (wp_conn->table->argo_object_name) {
            wally_xfer_row(wp_conn->wp_db->db_to_wally_cookie, wp_conn->table->argo_object_name, objects[i], indexes[i], NULL, NULL);



        } else {
            /* XXX LOG. If we have row callbacks, we REALLY should have had a table. */
            WALLY_LOG(AL_ERROR, "Row callbacks with no table");
        }

        if (objects[i]) {
            argo_object_release(objects[i]);
            objects[i] = NULL;
        }
    }

    /* Update Paused sequence */
    if (!is_true_origin && wp_conn->table->argo_object_name) {
        if (wally_update_paused_seq(wp_conn->wp_db->db_to_wally_cookie, wp_conn->table->argo_object_name)) {
            WALLY_LOG(AL_ERROR, "wally_pause_debug: Could not update paused_seq for sqlt table: %s!",
                                wp_conn->table->argo_object_name);
        }
    }

    if (!read_more) {
        WALLY_DEBUG_POSTGRES("SQLT SELECT complete, db=%s, req=%d, row_count=%d, sql=%s",
                            wp_conn->wp_db->name, (int)wp_conn->wally_request_id, wp_conn->row_count, sql_str);
        wally_xfer_response(wp_conn->wp_db->db_to_wally_cookie, wp_conn->wally_request_id, wp_conn->row_count, true, wp_conn->table->argo_object_name);

        /* no more read, relelase conections */
        wp_conn->state = conn_idle;
        TAILQ_INSERT_TAIL(&(wp_conn->wp_db->free_connections), wp_conn, free_connections);
        wp_db->is_blocking = 0;
        WALLY_DEBUG_POSTGRES_FC("Unblock (wp = %s)", wp_conn->wp_db->name);
        //wally_xfer_resume_xmit(wp_db->db_to_wally_cookie);
        zevent_base_call(zevent_get_for_class(SQLT_REQUEST_THREAD_POOL),
                         do_wally_xfer_resume_xmit,
                         wp_db->db_to_wally_cookie,
                         0);
    } else {
        zevent_base_call(zevent_get_for_class(SQLT_REQUEST_THREAD_POOL),
                         sqlt_read_select_1,
                         wp_conn,
                         0);
    }
}

static void create_sqlt_event_base()
{
    static pthread_mutex_t local_sqlt_lock = (pthread_mutex_t) PTHREAD_MUTEX_INITIALIZER;
    static struct zevent_base *zbase = NULL;

    pthread_mutex_lock(&local_sqlt_lock);
    if (!zbase) {
        zbase = zevent_handler_create(SQLT_REQUEST_THREAD, 16*1024*1024, 30);
        zevent_add_to_class(zbase, SQLT_REQUEST_THREAD_POOL);
    }
    pthread_mutex_unlock(&local_sqlt_lock);
}


/*
 * There is no need to execute "CreateDB" for SQLite
 * The DB will be created when it is opened first time.
 */
void *wally_sqlt_create(const char *dbname,
                        const char *thread_name,
                        int nconns,
                        int is_row_writable,
                        int is_true_origin,
                        int is_endpoint,
                        int64_t polling_interval_us)
{
    struct wp_db *wp_db;
    struct timeval tv;
    int i;
    struct wally_db_stats *z_stats;
    char name[256];

    static pthread_mutex_t local_lock = (pthread_mutex_t) PTHREAD_MUTEX_INITIALIZER;

#if 0
    if (is_endpoint) {
        fprintf(stderr, "Creating SQLite DB as endpoint: %s\n", dbname);
    }
#endif // 0

    pthread_mutex_lock(&local_lock);

    set_et_26295_test_size();

    if ((c_state_count + nconns) > WALLY_DB_MAX_CONNS) {
        WALLY_LOG(AL_ERROR, "Too many SQLite connections");
        pthread_mutex_unlock(&local_lock);
        return NULL;
    }

    create_sqlt_event_base();

    wp_db = (struct wp_db *) WALLY_MALLOC(sizeof(*wp_db));
    if (wp_db) {
        memset(wp_db, 0, sizeof(*wp_db));

        wp_db->db_type = db_sqlt;

        wp_db->name = WALLY_STRDUP(dbname, strlen(dbname));
        if (!wp_db->name) goto fail_free;

        wp_db->is_endpoint = is_endpoint;

        LIST_INIT(&(wp_db->all_tables));
        TAILQ_INIT(&(wp_db->free_connections));

        //ZPATH_RWLOCK_INIT(&(wp_db->lock), NULL, __FILE__, __LINE__);
        wp_db->lock = ZPATH_RWLOCK_INIT;

        ZPATH_RWLOCK_WRLOCK(&(wp_db->lock), __FILE__, __LINE__);

        wp_db->is_row_writable = is_row_writable;
        wp_db->is_alterable = 1;  /* always alterable for SQLite */
        wp_db->is_true_origin = is_true_origin;

        wp_db->polling_interval_us = polling_interval_us;

        wp_db->db_to_wally_cookie = NULL;

        wp_db->tables_by_argo_name = argo_hash_alloc(8, 1);
        if (!wp_db->tables_by_argo_name) goto fail_free;

        wp_db->tables_inserted = argo_hash_alloc(16, 1);
        if (!wp_db->tables_inserted) goto fail_free;

        /* Create libevent event for processing SQLite data */
        wp_db->ev_base = event_base_new();
        if (!wp_db->ev_base) {
            goto fail_free;
        }

        /* Set up nconns db connections */
        wp_db->connections_count = nconns;
        wp_db->connections = (struct wp_connection *) WALLY_MALLOC(sizeof(struct wp_connection) * nconns);
        memset(wp_db->connections, 0, sizeof(struct wp_connection) * nconns);
        for (i = 0; i < nconns; i++) {
            int rc;
            char tmp_str[100];
            sqlite3 *db;
            snprintf(tmp_str, sizeof(tmp_str), "%s:%d", wp_db->name, i);
            c_state[i + c_state_count].name = WALLY_STRDUP(tmp_str, strlen(tmp_str));
            c_conns[i + c_state_count] = &(wp_db->connections[i]);
            wp_db->connections[i].name = c_state[i + c_state_count].name;

            wp_db->connections[i].wp_db = wp_db;

            wp_db->connections[i].my_index = i;

            /*
             * SQLITE_OPEN_CREATE make sure to create Db if it does not exist,
             * SQLITE_OPEN_FULLMUTEX enforces "serialized" threading mode in run time, This will override the
             * thread mode set in compiling time or start-time, and make sure it is thread safety.
             */
            rc = sqlite3_open_v2(wp_db->name,
                                 &db,
                                 SQLITE_OPEN_READWRITE | SQLITE_OPEN_CREATE | SQLITE_OPEN_FULLMUTEX,
                                 NULL);
            wp_db->connections[i].db_conn = db;
            if (rc) {
                WALLY_LOG(AL_CRITICAL, "Could not create or open SQLite db %s", wp_db->name);
                goto fail_free;
            } else {
                wp_db->connections[i].object_info.objects = WALLY_MALLOC(MAX_WRITE_OBJECT_CNT * sizeof(struct argo_object *));
                wp_db->connections[i].object_info.qindexes = WALLY_MALLOC(MAX_WRITE_OBJECT_CNT * sizeof(int));
                wp_db->connections[i].object_info.cnt = 0;
                WALLY_LOG(AL_NOTICE, "An handler to SQLite db %s is opened", wp_db->name);
            }

            wp_db->connections[i].state = conn_idle;
            TAILQ_INSERT_TAIL(&(wp_db->free_connections), &(wp_db->connections[i]), free_connections);
            wp_db->is_blocking = 0;

            /* N/A for SQLite Db */
            wp_db->connections[i].ev_conn = NULL;
        }

        wally_db_set_size(wp_db);

        snprintf(name, sizeof(name), "wally_db_%s_stats", wp_db->name);
        z_stats = WALLY_MALLOC(sizeof(*z_stats));
        argo_log_register_structure(wally_stats_log,
                                    name,
                                    AL_INFO,
                                    300*1000*1000,    /* 5 minutes */
                                    wally_db_stats_description,
                                    z_stats,
                                    0,
                                    fill_wally_db_stats,
                                    wp_db);

        /* Add a timer event to the database thread. This timer event
         * can do auxiliary maintenance on tables... It also does
         * table update reading. */
        /* Create timer event for this thread */
        wp_db->ev_timer = event_new(wp_db->ev_base,         /* event_base for this thread */
                                    -1,                     /* No socket, so -1. */
                                    EV_PERSIST,             /* Repeating event */
                                    wally_timer_event_callback,   /* Callback function */
                                    wp_db);                 /* Callback cookie- our db state. */
        if (!wp_db->ev_timer) {
            goto fail_free;
        }

        /* Start the timer for this thread. This runs every 0.1s This
         * timer is related to the polling interval for database
         * changes as well, so if you want faster response time, you
         * can spin this down to a smaller query rate. */
        tv.tv_sec = WP_POLL_INTERVAL_US / 1000000;
        tv.tv_usec = WP_POLL_INTERVAL_US % 1000000;
        if (event_add(wp_db->ev_timer, &tv)) {
            goto fail_free;
        }

        /* Create thread. */
        if (zthread_create(&(wp_db->thread),
                           wally_db_thread,
                           wp_db,
                           thread_name,
                           wally_default_hb_timeout_s, /* 60s for wallyd and for others 20s thread watchdog */
                           16*1024*1024,     /* 16 MB stack */
                           60*1024*1024,     /* 60s stats interval */
                           NULL)) {
            /* XXX LOG */
            goto fail_free;
        }

        ZPATH_RWLOCK_UNLOCK(&(wp_db->lock), __FILE__, __LINE__);
    }

    c_state_count += nconns;

    pthread_mutex_unlock(&local_lock);

    return wp_db;

 fail_free:
    ZPATH_RWLOCK_UNLOCK(&(wp_db->lock), __FILE__, __LINE__);
    if (wp_db->name) WALLY_FREE(wp_db->name);
    if (wp_db->ev_timer) event_free(wp_db->ev_timer);
    if (wp_db->tables_by_argo_name) argo_hash_free(wp_db->tables_by_argo_name);
    if (wp_db->tables_inserted) argo_hash_free(wp_db->tables_inserted);

    if (wp_db->connections) {
        for (i = 0; i < nconns; i++) {
            if (wp_db->connections[i].db_conn) {
                sqlite3_close((sqlite3 *)(wp_db->connections[i].db_conn));
            }
        }
        WALLY_FREE(wp_db->connections);
    }
    if (wp_db->ev_base) event_base_free(wp_db->ev_base);
    WALLY_FREE(wp_db);
    pthread_mutex_unlock(&local_lock);
    return NULL;
}

void sqlt_synchronize_1(struct zevent_base *base, void *cookie1, int64_t int_cookie)
{
    struct wp_connection * wp_conn = (struct wp_connection *)cookie1;

    ZPATH_RWLOCK_WRLOCK(&(wp_conn->wp_db->lock), __FILE__, __LINE__);
    sqlt_synchronize(wp_conn);

    wp_conn->state = conn_idle;
    wp_conn->wp_db->is_blocking = 0;
    TAILQ_INSERT_TAIL(&(wp_conn->wp_db->free_connections), wp_conn, free_connections);

    ZPATH_RWLOCK_UNLOCK(&(wp_conn->wp_db->lock), __FILE__, __LINE__);
}

int sqlt_synchronize(struct wp_connection *wp_conn)
{
    int rc;
    WALLY_LOG(AL_NOTICE, "SQLT synchronization starting, db=%s, table=%s",
                  wp_conn->wp_db->name, wp_conn->table->db_table_name);
    rc = sqlt_get_table(wp_conn);
    if (rc == SQLT_NO_TABLE) rc = sqlt_create_table(wp_conn);

    if (rc == SQLT_SUCCESS) {
        wally_db_table_log_register(wp_conn->table);
        rc = sqlt_get_columns(wp_conn);
        if (rc == SQLT_SUCCESS) {
            rc = sqlt_read_indexes(wp_conn);
            if (rc == SQLT_SUCCESS) rc = sqlt_read_sequence(wp_conn);
        } else {
            /* sqlt_get_columns or sqlt_add_columns failed */
        }
    } else {
        /* sqlt_get_table or sqlt_create_table failed */
    }

    if (rc == SQLT_SUCCESS) {
        WALLY_LOG(AL_NOTICE, "SQLT synchronization success, db=%s, table=%s",
                  wp_conn->wp_db->name, wp_conn->table->db_table_name);
    } else {
        WALLY_LOG(AL_CRITICAL, "SQLT synchronization failed, rc=%d, db=%s, table=%s, table state=%d",
                  rc, wp_conn->wp_db->name, wp_conn->table->db_table_name, wp_conn->table->state);
    }

    return rc;
}

void sqlt_write_object(struct wp_db *wp_db)
{
    struct wp_connection * wp_conn = &(wp_db->connections[0]);

    if (wp_conn->state == conn_idle) {

        int queue_index=-1;
        int64_t t_max;
        int64_t t_prev;
        int64_t t_now;
        struct wally_local_db_write_queue_object *write_object = NULL;



        t_max = WP_POLL_INTERVAL_US / 2;

        TAILQ_REMOVE(&(wp_db->free_connections), wp_conn, free_connections);
        WALLY_DEBUG_POSTGRES_CONN("%s: Alloc connection %d", wp_conn->wp_db->name, wp_conn->my_index);

        t_prev = epoch_us();

        wp_conn->object_info.cnt = 0;

        do {
            write_object = wally_origin_dequeue_write_row(wp_db->db_to_wally_cookie, &queue_index);
            if (write_object) {
			   	if (write_object->db_op == db_oper_rowargo_object) {
					wp_conn->object_info.objects[wp_conn->object_info.cnt] = write_object->data.row_to_write;
					wp_conn->object_info.qindexes[wp_conn->object_info.cnt] = queue_index;
					wp_conn->object_info.cnt++;
				} else {
					//for now multiple index consistency support is not added for SQL LITE
					//Recovery cannot be done for SQL LITE.
					wp_release_write_object(write_object, 0);
				}
                //WALLY_DEBUG_POSTGRES("%s: Dequeued object to write, ix = %d", wp_conn->wp_db->name, queue_index);
            } else {
                break;
            }

            t_now = epoch_us();
        } while (t_now - t_prev < t_max && wp_conn->object_info.cnt < MAX_WRITE_OBJECT_CNT);

        //WALLY_DEBUG_POSTGRES("Deququed object count=%d", cnt);

        if (wp_conn->object_info.cnt > 0) {
            wp_conn->state = conn_table_write;
            zevent_base_big_call(zevent_get_for_class(SQLT_REQUEST_THREAD_POOL),
                                         sqlt_write_object_update_1,
                                         wp_conn,
                                         0,
                                         NULL,
                                         NULL,
                                         NULL,
                                         0);
        } else {
            wp_conn->state = conn_idle;
            TAILQ_INSERT_TAIL(&(wp_db->free_connections), wp_conn, free_connections);
        }
    }
}

/*
 * Get the status of this origin DB. Basically checks whether or not
 * the DB connections are okay.
 */
void wp_get_sqlt_status(void *callout_cookie)
{
    int i;
    struct wp_db *wp_db = (struct wp_db *) callout_cookie;
    int pCur;
    int pHiwtr;

    if (wp_db) {
        /* All of these calls are may be thread-safe (because it doesn't
         * change after intialization), but we'll lock anyway. */
        ZPATH_RWLOCK_RDLOCK(&(wp_db->lock), __FILE__, __LINE__);
        for (i = 0; i < wp_db->connections_count; i++) {
            int dbConnStatus = sqlite3_db_status((sqlite3*)(wp_db->connections[i].db_conn), 0, &pCur, &pHiwtr, 0);
            if (dbConnStatus == SQLITE_OK) continue;
            WALLY_LOG(AL_CRITICAL, "Connection idx %d, name %s, status error %s.",
                        i, wp_db->connections[i].name, sqlite3_errmsg((sqlite3*)(wp_db->connections[i].db_conn)));
        }
        ZPATH_RWLOCK_UNLOCK(&(wp_db->lock), __FILE__, __LINE__);
        return;
    } else {
        return;
    }
}

int wally_sqlt_backup(struct wally_origin *origin,
                      const char* backup_path,
                      void *request_state,
                      void(*xProgress)(void*, int, int, int*))
{
    struct wp_db *wp_db = (struct wp_db *)origin->callout_handle;
    struct wp_connection * wp_conn = &(wp_db->connections[0]);
    int rc;
    sqlite3 *pFile;
    sqlite3_backup *pBackup;
    char dst_file[2048];
    int res = WALLY_RESULT_ERR;
    int current_percent = 0;

    snprintf(dst_file, sizeof(dst_file), "%s/%s_backup", backup_path, wp_db->name);

    /* Open the database file identified by zFilename. */
    rc = sqlite3_open(dst_file, &pFile);
    if (rc == SQLITE_OK) {
        pBackup = sqlite3_backup_init(pFile, "main", wp_conn->db_conn, "main");
        if (pBackup) {
            do {
                rc = sqlite3_backup_step(pBackup, 100);
                xProgress(request_state,
                          sqlite3_backup_remaining(pBackup),
                          sqlite3_backup_pagecount(pBackup),
                          &current_percent);
                if (rc == SQLITE_BUSY || rc == SQLITE_LOCKED) {
                    sqlite3_sleep(10);
                }
                zthread_heartbeat(NULL);
            } while(rc == SQLITE_OK || rc == SQLITE_BUSY || rc == SQLITE_LOCKED);

            (void)sqlite3_backup_finish(pBackup);
        }
        rc = sqlite3_errcode(pFile);
    }

    if (rc == SQLITE_OK) {
        res = WALLY_RESULT_NO_ERROR;
    }

    (void)sqlite3_close(pFile);

    return res;
}
