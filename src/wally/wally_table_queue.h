/*
 * wally_table_queue.h Copyright (C) 2024 Zscaler, Inc. All Rights Reserved
 *
 * <PERSON> queue to handle table operations and different messsage types posted
 * from DB layer to wally Layer
 */

#ifndef __WALLY_TABLE_QUEUE_H__
#define __WALLY_TABLE_QUEUE_H__

#include "wally/wally_private.h"
#include "zpath_lib/zpath_lib.h"

#define NO_OF_WALLY_THREADS wally_gbl_cfg.wally_threads
#define IS_WALLY_LAYER_ENABLED NO_OF_WALLY_THREADS

/* Different message type sent from DB layer to wally Layer */
enum wally_msg_type {
	WALLY_MSG_DB_XFER_ROW,
	WALLY_MSG_DB_XFER_RESPONSE,
	WALLY_MSG_FOHH_XFER_ROW,
	WALLY_MSG_FOHH_XFER_RESPONSE,
	WALLY_MSG_DB_PAUSED_SEQ,
	WALLY_MSG_DB_TABLE_EXISTS,
	WALLY_MSG_FOHH_CLIENT_REGISTER_ROW_STRING,
	WALLY_MSG_FOHH_CLIENT_DEREGISTER_ROW_STRING,
	WALLY_MSG_DO_WTS,
    WALLY_MSG_FOHH_BEGIN_RECOVERY_REQ,
    WALLY_MSG_FOHH_END_RECOVERY_REQ,
    WALLY_MSG_FOHH_RECOVERY_CMPLT_RESP,
	WALLY_MSG_MAX
};


/*
 * Wally table queue stats
 */
struct wally_table_queue_stats {                              /* _ARGO: object_definition */
	int64_t total_message_pending;   			              /* _ARGO: integer */
	int64_t total_message_xfer_row_pending;				      /* _ARGO: integer */
	int64_t total_message_xfer_resp_pending;				  /* _ARGO: integer */
	int64_t total_message_fohh_xfer_row_pending;			  /* _ARGO: integer */
	int64_t total_message_fohh_xfer_resp_pending;			  /* _ARGO: integer */
	int64_t total_message_fohh_begin_recovery_req_pending;    /* _ARGO: integer */
	int64_t total_message_fohh_end_recovery_req_pending;	  /* _ARGO: integer */
	int64_t total_message_fohh_recovery_cmplt_resp_pending;	  /* _ARGO: integer */
	int64_t total_message_paused_seq_pending;			      /* _ARGO: integer */
	int64_t total_message_db_table_exists_pending;		      /* _ARGO: integer */
	int64_t total_message_fohh_client_register_row_pending;   /* _ARGO: integer */
	int64_t total_message_fohh_client_deregister_row_pending;   /* _ARGO: integer */
	int64_t total_message_do_wts_pending;                     /* _ARGO: integer */
	int64_t total_message_processed;   			               /* _ARGO: integer */
	int64_t total_message_xfer_row_processed;				  /* _ARGO: integer */
	int64_t total_message_xfer_resp_processed;				  /* _ARGO: integer */
	int64_t total_message_fohh_xfer_row_processed;			  /* _ARGO: integer */
	int64_t total_message_fohh_xfer_resp_processed;			  /* _ARGO: integer */
    int64_t total_message_fohh_begin_recovery_req_processed;  /* _ARGO: integer */
    int64_t total_message_fohh_end_recovery_req_processed;    /* _ARGO: integer */
    int64_t total_message_fohh_recovery_cmplt_resp_processed; /* _ARGO: integer */
	int64_t total_message_paused_seq_processed;			      /* _ARGO: integer */
	int64_t total_message_db_table_exists_processed;		  /* _ARGO: integer */
	int64_t total_message_fohh_client_register_row_processed; /* _ARGO: integer */
	int64_t total_message_fohh_client_deregister_row_processed; /* _ARGO: integer */
	int64_t total_message_do_wts_processed; 				   /* _ARGO: integer */
	int64_t avg_wait_time_us; 				                    /* _ARGO: integer */
	int64_t min_wait_time_us; 				                    /* _ARGO: integer */
	int64_t max_wait_time_us; 				                    /* _ARGO: integer */
	int64_t avg_msg_process_time_us; 				            /* _ARGO: integer */
};

/*
 * Wally table thread
 */
struct wally_table_thread {

    /* Thread watchdog state */
	struct zthread_info *zthread;
	struct zevent_base *zevent;
	char wally_table_thread_name[WALLY_MAX_THREAD_NAME_LEN];

	struct wally_table_queue_stats *queue_stats;
	struct argo_log_registered_structure *wally_table_queue_structure;

	pthread_mutex_t wally_table_thread_lock;
	int64_t total_message_pending;
	int64_t total_message_processed;

	/* Messages pending or processed per message type */
	int64_t message_pending[WALLY_MSG_MAX];
	int64_t message_processed[WALLY_MSG_MAX];

	int64_t avg_wait_time_us;
	int64_t min_wait_time_us;
	int64_t max_wait_time_us;
	int64_t avg_msg_process_time_us;
};

/* DB  xfer row message format */
typedef struct wally_table_queue_db_xfer_row {
	void *cmd_cookie;                       // DB to wally cookie
	char *argo_table_name;				    // Table name
	int object_count;						//no.of row per indexes
	struct argo_object **objects;			//row objects
	int64_t *indexes;						//indexes;
}wally_table_queue_db_xfer_row;

/* DB xfer response message format */
typedef struct wally_table_queue_fohh_xfer_response wally_table_queue_db_xfer_response;

/* FOHH xfer row message format */
typedef struct wally_table_queue_fohh_xfer_row {
	void *cmd_cookie;						//Wally origin cookie
	struct argo_object *object;				//Row objects
	char *argo_table_name;					//Table name
	int64_t cmd_id;							//Request ID

}wally_table_queue_fohh_xfer_row;

/* FOHH xfer response message format */
typedef struct wally_table_queue_fohh_xfer_response {
	void *cmd_cookie;					 	//Wally origin cookie
	int64_t cmd_id;							//Request ID
	int64_t cmd_row_count;					//row count
	bool table_exists;						//Whether table exists or not
	char *argo_table_name;					//Table name
}wally_table_queue_fohh_xfer_response;

/* Message format to update paused seq in wally */
typedef struct wally_table_queue_db_paused_seq {
	void *cmd_cookie;						//Db to wally cookies
	char *argo_table_name;					//Table name
}wally_table_queue_db_paused_seq;

/* Message format to update table exists in wally */
typedef struct wally_table_queue_db_table_exists {
	void *cmd_cookie;						//DB to wally cookie
	char *argo_table_name;					//Table_name;
	bool table_exists;						//Whether table exists in DB or not
}wally_table_queue_db_table_exists;

/* fohh client register for row message format */
typedef struct wally_table_queue_fohh_client_register_row_string {
	void *cookie;							//Wally origin
	void *structure_cookie;					//WFC
	struct argo_object *object;				//row request string
}wally_table_queue_fohh_client_register_row_string;

typedef struct wally_table_queue_fohh_begin_recovery_req {
    void *cmd_cookie;                           //Wally origin
    char *table_name;                       //Table name
    int64_t recovery_sequence;              // Recovery sequence
    int64_t recovery_timeout;               // Recovery timeout
    int sync_missing_rows;                  // Sync missing rows
} wally_table_queue_fohh_begin_recovery_req;

typedef struct wally_table_queue_fohh_end_recovery_req {
    void *cmd_cookie;                       //Wally origin
    void *structure_cookie;                 //WFC
    char *table_name;                       //Table name
    int64_t server_sent_rows;               // Server sent rows
} wally_table_queue_fohh_end_recovery_req;

typedef struct wally_table_queue_fohh_recovery_cmplt_resp {
    void *cmd_cookie;                           //Wally origin
    void *structure_cookie;                 //WFC
    char *table_name;                       //Table name
    int64_t received_rows;                 // received_rows
} wally_table_queue_fohh_recovery_cmplt_resp;

typedef wally_table_queue_fohh_client_register_row_string wally_table_queue_fohh_client_deregister_row_string;
/* Do wts message format */
typedef struct wally_table_queue_do_wts {
	struct wt *wt;
	struct wally_origin *wallyd_slave_db;
    struct wally_origin *wallyd_remote_db;
    struct wally *wallyd;
    int fully_loaded;
    struct table_cleanup_parameter cleanup_param;
	int load_tables;
	wally_row_fixup_f *fixup_f;
	int is_gwally;
	wallyd_zpath_table_register_cb_f *zptr_f;
}wally_table_queue_do_wts;

struct wally_table_queue_node {
	enum wally_msg_type msg_type;
	union {
		wally_table_queue_db_xfer_row xfer_row;
		wally_table_queue_db_xfer_response xfer_resp;
		wally_table_queue_fohh_xfer_row fohh_xfer_row;
		wally_table_queue_fohh_xfer_response fohh_xfer_resp;
        wally_table_queue_fohh_begin_recovery_req fohh_begin_recovery_req;
        wally_table_queue_fohh_end_recovery_req fohh_end_recovery_req;
        wally_table_queue_fohh_recovery_cmplt_resp fohh_recovery_cmplt_resp;
		wally_table_queue_db_paused_seq paused_seq;
		wally_table_queue_db_table_exists table_exists;
		wally_table_queue_fohh_client_register_row_string register_row_string;
		wally_table_queue_fohh_client_deregister_row_string deregister_row_string;
		wally_table_queue_do_wts do_wts;
	}msg;

	int64_t wait_start_time_main_us;
	int64_t wait_start_time_slave_us;
	int64_t process_start_time_us;
	int64_t total_msg_process_time_us;

	ZTAILQ_ENTRY(wally_table_queue_node) list;
};

/*
 * enqueue and deque function for wally table threads to pick up an
 * entry for processing
 */
void wally_table_queue_enqueue_node_internal(struct wally_table_queue_node *q);

int wally_table_queue_enqueue_row_objects(void *cmd_cookie, char *argo_table_name,
		struct argo_object **objects, int64_t *indexes, int object_count);

int wally_table_queue_enqueue_xfer_response(void *cmd_cookie, int64_t cmd_id,
		int64_t cmd_row_count, bool table_exists,
		char *argo_table_name);

int wally_table_queue_enqueue_fohh_xfer_row(void *cmd_cookie, struct argo_object *row);

int wally_table_queue_enqueue_fohh_xfer_response(void *cmd_cookie,
		int64_t cmd_id,
		int64_t cmd_row_count,
		bool table_exists,
		char *argo_table_name);

int wally_table_queue_enqueue_db_xfer_xmit(void *cmd_cookie);

int wally_table_queue_enqueue_db_paused_seq(void *cmd_cookie,
	  char *argo_table_name);

int wally_table_queue_enqueue_db_table_exists(void *cmd_cookie,
		char *argo_table_name,
		bool table_exists);

int wally_table_queue_enqueue_fohh_client_register_row_string(void *cookie,
                                                              void *structure_cookie,
                                                              struct argo_object *object);

int wally_table_queue_enqueue_fohh_client_deregister_row_string(void *cookie,
                                                              void *structure_cookie,
                                                              struct argo_object *object);
int wally_table_queue_enqueue_do_wts(struct wt *wt,
                                    struct wally_origin *wallyd_slave_db,
                                    struct wally_origin *wallyd_remote_db,
                                    struct wally *wallyd,
                                    int config_fully_loaded,
                                    struct table_cleanup_parameter cleanup_param ,
                                    int load_tables,
                                    wally_row_fixup_f *fixup_f,
                                    int is_gwally,
									wallyd_zpath_table_register_cb_f *zptr_f);

int wally_table_queue_enqueue_fohh_recovery_begin_req(void *cmd_cookie,
                                                              char *table_name, int64_t recovery_sequence,
                                                              int64_t recovery_timeout, int sync_missing_rows);

int wally_table_queue_enqueue_fohh_recovery_end_req(void *cmd_cookie,
                                                              char *table_name, int64_t server_sent_rows);

int wally_table_queue_enqueue_fohh_recovery_cmplt_resp(void *cmd_cookie,
                                                              char *table_name, int64_t received_rows);

void wally_table_queue_process_msg_cb(struct zevent_base *base, void *void_cookie, int64_t cookie);
struct wally_table_queue_node *wally_table_process_msg_dequeue();

extern const char *wally_table_queue_msg_string[];

void wally_table_thread_load_distributor(struct zevent_base *base, void *void_cookie, int64_t cookie);

extern struct wally_table_thread *wally_table_thread_inst;

int32_t wally_table_thread_init();
uint64_t wally_get_table_thread_id(const char *table_name);

#endif
